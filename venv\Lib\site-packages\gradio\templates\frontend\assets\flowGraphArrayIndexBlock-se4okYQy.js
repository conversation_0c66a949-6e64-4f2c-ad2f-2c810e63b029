import{F as s,g as i}from"./KHR_interactivity-DTxiAnOo.js";import{R as t,F as l}from"./declarationMapper-BZjsjg7g.js";import{R as u}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class o extends s{constructor(e){super(e),this.config=e,this.array=this.registerDataInput("array",t),this.index=this.registerDataInput("index",t,new l(-1)),this.value=this.registerDataOutput("value",t)}_updateOutputs(e){const r=this.array.getValue(e),a=i(this.index.getValue(e));r&&a>=0&&a<r.length?this.value.setValue(r[a],e):this.value.setValue(null,e)}serialize(e){super.serialize(e)}getClassName(){return"FlowGraphArrayIndexBlock"}}u("FlowGraphArrayIndexBlock",o);export{o as FlowGraphArrayIndexBlock};
//# sourceMappingURL=flowGraphArrayIndexBlock-se4okYQy.js.map
