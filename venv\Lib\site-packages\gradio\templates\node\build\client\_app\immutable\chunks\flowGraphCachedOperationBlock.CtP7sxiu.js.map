{"version": 3, "file": "flowGraphCachedOperationBlock.CtP7sxiu.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphCachedOperationBlock.js"], "sourcesContent": ["import { FlowGraphBlock } from \"../../flowGraphBlock.js\";\nimport { RichTypeBoolean } from \"../../flowGraphRichTypes.js\";\nconst cacheName = \"cachedOperationValue\";\nconst cacheExecIdName = \"cachedExecutionId\";\n/**\n * A block that will cache the result of an operation and deliver it as an output.\n */\nexport class FlowGraphCachedOperationBlock extends FlowGraphBlock {\n    constructor(outputRichType, config) {\n        super(config);\n        this.value = this.registerDataOutput(\"value\", outputRichType);\n        this.isValid = this.registerDataOutput(\"isValid\", RichTypeBoolean);\n    }\n    _updateOutputs(context) {\n        const cachedExecutionId = context._getExecutionVariable(this, cacheExecIdName, -1);\n        const cachedValue = context._getExecutionVariable(this, cacheName, null);\n        if (cachedValue !== undefined && cachedValue !== null && cachedExecutionId === context.executionId) {\n            this.isValid.setValue(true, context);\n            this.value.setValue(cachedValue, context);\n        }\n        else {\n            try {\n                const calculatedValue = this._doOperation(context);\n                if (calculatedValue === undefined || calculatedValue === null) {\n                    this.isValid.setValue(false, context);\n                    return;\n                }\n                context._setExecutionVariable(this, cacheName, calculatedValue);\n                context._setExecutionVariable(this, cacheExecIdName, context.executionId);\n                this.value.setValue(calculatedValue, context);\n                this.isValid.setValue(true, context);\n            }\n            catch (e) {\n                this.isValid.setValue(false, context);\n            }\n        }\n    }\n}\n//# sourceMappingURL=flowGraphCachedOperationBlock.js.map"], "names": ["cacheName", "cacheExecIdName", "FlowGraphCachedOperationBlock", "FlowGraphBlock", "outputRichType", "config", "RichTypeBoolean", "context", "cachedExecutionId", "cachedValue", "calculatedValue"], "mappings": "mGAEA,MAAMA,EAAY,uBACZC,EAAkB,oBAIjB,MAAMC,UAAsCC,CAAe,CAC9D,YAAYC,EAAgBC,EAAQ,CAChC,MAAMA,CAAM,EACZ,KAAK,MAAQ,KAAK,mBAAmB,QAASD,CAAc,EAC5D,KAAK,QAAU,KAAK,mBAAmB,UAAWE,CAAe,CACpE,CACD,eAAeC,EAAS,CACpB,MAAMC,EAAoBD,EAAQ,sBAAsB,KAAMN,EAAiB,EAAE,EAC3EQ,EAAcF,EAAQ,sBAAsB,KAAMP,EAAW,IAAI,EACvE,GAAiCS,GAAgB,MAAQD,IAAsBD,EAAQ,YACnF,KAAK,QAAQ,SAAS,GAAMA,CAAO,EACnC,KAAK,MAAM,SAASE,EAAaF,CAAO,MAGxC,IAAI,CACA,MAAMG,EAAkB,KAAK,aAAaH,CAAO,EACjD,GAAqCG,GAAoB,KAAM,CAC3D,KAAK,QAAQ,SAAS,GAAOH,CAAO,EACpC,MACH,CACDA,EAAQ,sBAAsB,KAAMP,EAAWU,CAAe,EAC9DH,EAAQ,sBAAsB,KAAMN,EAAiBM,EAAQ,WAAW,EACxE,KAAK,MAAM,SAASG,EAAiBH,CAAO,EAC5C,KAAK,QAAQ,SAAS,GAAMA,CAAO,CACtC,MACS,CACN,KAAK,QAAQ,SAAS,GAAOA,CAAO,CACvC,CAER,CACL", "x_google_ignoreList": [0]}