import{GLTFLoader as n,ArrayItem as a}from"./glTFLoader.BetPWe9U.js";import{an as p,ao as _}from"./index.BoI39RQH.js";const r="EXT_texture_webp";class c{constructor(e){this.name=r,this._loader=e,this.enabled=e.isExtensionUsed(r)}dispose(){this._loader=null}_loadTextureAsync(e,s,o){return n.LoadExtensionAsync(e,s,this.name,(i,l)=>{const m=s.sampler==null?n.DefaultSampler:a.Get(`${e}/sampler`,this._loader.gltf.samplers,s.sampler),d=a.Get(`${i}/source`,this._loader.gltf.images,l.source);return this._loader._createTextureAsync(e,m,d,u=>{o(u)},void 0,!s._textureInfo.nonColorData)})}}p(r);_(r,!0,t=>new c(t));export{c as EXT_texture_webp};
//# sourceMappingURL=EXT_texture_webp.D7AS9p0M.js.map
