{"version": 3, "file": "classDiagram-L266QK7U-BrtqgbFI.js", "sources": ["../../../../node_modules/.pnpm/mermaid@11.5.0/node_modules/mermaid/dist/chunks/mermaid.core/classDiagram-L266QK7U.mjs"], "sourcesContent": ["import {\n  ClassDB,\n  classDiagram_default,\n  classRenderer_v3_unified_default,\n  styles_default\n} from \"./chunk-5V4FS25O.mjs\";\nimport \"./chunk-2O5F6CEG.mjs\";\nimport \"./chunk-SSJB2B2L.mjs\";\nimport \"./chunk-XWQKHCUW.mjs\";\nimport \"./chunk-JXS2JFWQ.mjs\";\nimport \"./chunk-VRARSN5C.mjs\";\nimport \"./chunk-6Q42YGA5.mjs\";\nimport \"./chunk-KIRMWWLE.mjs\";\nimport \"./chunk-4BQVQIO5.mjs\";\nimport \"./chunk-ABD7OU7K.mjs\";\nimport {\n  __name\n} from \"./chunk-O7R7247Q.mjs\";\n\n// src/diagrams/class/classDiagram.ts\nvar diagram = {\n  parser: classDiagram_default,\n  get db() {\n    return new ClassDB();\n  },\n  renderer: classRenderer_v3_unified_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ __name((cnf) => {\n    if (!cnf.class) {\n      cnf.class = {};\n    }\n    cnf.class.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;\n  }, \"init\")\n};\nexport {\n  diagram\n};\n"], "names": ["diagram", "classDiagram_default", "ClassDB", "classRenderer_v3_unified_default", "styles_default", "__name", "cnf"], "mappings": "wSAoBG,IAACA,EAAU,CACZ,OAAQC,EACR,IAAI,IAAK,CACP,OAAO,IAAIC,CACZ,EACD,SAAUC,EACV,OAAQC,EACR,KAAsBC,EAAQC,GAAQ,CAC/BA,EAAI,QACPA,EAAI,MAAQ,IAEdA,EAAI,MAAM,oBAAsBA,EAAI,mBACrC,EAAE,MAAM,CACX", "x_google_ignoreList": [0]}