import{SvelteComponent as E,init as F,safe_not_equal as G,create_component as v,claim_component as w,mount_component as C,transition_in as b,transition_out as h,destroy_component as B,afterUpdate as H,assign as J,binding_callbacks as K,bind as L,space as S,claim_space as q,insert_hydration as I,get_spread_update as M,get_spread_object as O,group_outros as P,check_outros as Q,add_flush_callback as R,detach as j}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{B as T,S as V,q as W}from"./2.B2AoQPnG.js";import{C as X}from"./Checkbox.BBJAZbOw.js";function N(s){let e,l;return e=new W({props:{info:s[5]}}),{c(){v(e.$$.fragment)},l(t){w(e.$$.fragment,t)},m(t,a){C(e,t,a),l=!0},p(t,a){const _={};a&32&&(_.info=t[5]),e.$set(_)},i(t){l||(b(e.$$.fragment,t),l=!0)},o(t){h(e.$$.fragment,t),l=!1},d(t){B(e,t)}}}function Y(s){let e,l,t,a,_,c;const o=[{autoscroll:s[10].autoscroll},{i18n:s[10].i18n},s[9]];let g={};for(let n=0;n<o.length;n+=1)g=J(g,o[n]);e=new V({props:g}),e.$on("clear_status",s[14]);let f=s[5]&&N(s);function k(n){s[15](n)}let d={label:s[4],interactive:s[11]};return s[0]!==void 0&&(d.value=s[0]),a=new X({props:d}),K.push(()=>L(a,"value",k)),a.$on("change",s[12]),a.$on("select",s[16]),{c(){v(e.$$.fragment),l=S(),f&&f.c(),t=S(),v(a.$$.fragment)},l(n){w(e.$$.fragment,n),l=q(n),f&&f.l(n),t=q(n),w(a.$$.fragment,n)},m(n,u){C(e,n,u),I(n,l,u),f&&f.m(n,u),I(n,t,u),C(a,n,u),c=!0},p(n,u){const m=u&1536?M(o,[u&1024&&{autoscroll:n[10].autoscroll},u&1024&&{i18n:n[10].i18n},u&512&&O(n[9])]):{};e.$set(m),n[5]?f?(f.p(n,u),u&32&&b(f,1)):(f=N(n),f.c(),b(f,1),f.m(t.parentNode,t)):f&&(P(),h(f,1,1,()=>{f=null}),Q());const r={};u&16&&(r.label=n[4]),u&2048&&(r.interactive=n[11]),!_&&u&1&&(_=!0,r.value=n[0],R(()=>_=!1)),a.$set(r)},i(n){c||(b(e.$$.fragment,n),b(f),b(a.$$.fragment,n),c=!0)},o(n){h(e.$$.fragment,n),h(f),h(a.$$.fragment,n),c=!1},d(n){n&&(j(l),j(t)),B(e,n),f&&f.d(n),B(a,n)}}}function Z(s){let e,l;return e=new T({props:{visible:s[3],elem_id:s[1],elem_classes:s[2],container:s[6],scale:s[7],min_width:s[8],$$slots:{default:[Y]},$$scope:{ctx:s}}}),{c(){v(e.$$.fragment)},l(t){w(e.$$.fragment,t)},m(t,a){C(e,t,a),l=!0},p(t,[a]){const _={};a&8&&(_.visible=t[3]),a&2&&(_.elem_id=t[1]),a&4&&(_.elem_classes=t[2]),a&64&&(_.container=t[6]),a&128&&(_.scale=t[7]),a&256&&(_.min_width=t[8]),a&134705&&(_.$$scope={dirty:a,ctx:t}),e.$set(_)},i(t){l||(b(e.$$.fragment,t),l=!0)},o(t){h(e.$$.fragment,t),l=!1},d(t){B(e,t)}}}function y(s,e,l){let{elem_id:t=""}=e,{elem_classes:a=[]}=e,{visible:_=!0}=e,{value:c=!1}=e,{value_is_output:o=!1}=e,{label:g="Checkbox"}=e,{info:f=void 0}=e,{container:k=!0}=e,{scale:d=null}=e,{min_width:n=void 0}=e,{loading_status:u}=e,{gradio:m}=e,{interactive:r}=e;function U(){m.dispatch("change"),o||m.dispatch("input")}H(()=>{l(13,o=!1)});const z=()=>m.dispatch("clear_status",u);function A(i){c=i,l(0,c)}const D=i=>m.dispatch("select",i.detail);return s.$$set=i=>{"elem_id"in i&&l(1,t=i.elem_id),"elem_classes"in i&&l(2,a=i.elem_classes),"visible"in i&&l(3,_=i.visible),"value"in i&&l(0,c=i.value),"value_is_output"in i&&l(13,o=i.value_is_output),"label"in i&&l(4,g=i.label),"info"in i&&l(5,f=i.info),"container"in i&&l(6,k=i.container),"scale"in i&&l(7,d=i.scale),"min_width"in i&&l(8,n=i.min_width),"loading_status"in i&&l(9,u=i.loading_status),"gradio"in i&&l(10,m=i.gradio),"interactive"in i&&l(11,r=i.interactive)},[c,t,a,_,g,f,k,d,n,u,m,r,U,o,z,A,D]}class ne extends E{constructor(e){super(),F(this,e,y,Z,G,{elem_id:1,elem_classes:2,visible:3,value:0,value_is_output:13,label:4,info:5,container:6,scale:7,min_width:8,loading_status:9,gradio:10,interactive:11})}}export{X as BaseCheckbox,ne as default};
//# sourceMappingURL=Index.DVcrV1NY.js.map
