// QtGuimod.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%Module(name=PyQt6.QtGui, keyword_arguments="Optional", use_limited_api=True)

%Import QtCore/QtCoremod.sip

%Copying
Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>

This file is part of PyQt6.

This file may be used under the terms of the GNU General Public License
version 3.0 as published by the Free Software Foundation and appearing in
the file LICENSE included in the packaging of this file.  Please review the
following information to ensure the GNU General Public License version 3.0
requirements will be met: http://www.gnu.org/copyleft/gpl.html.

If you do not wish to use this file under the terms of the GPL version 3.0
then you may purchase a commercial license.  For more information contact
<EMAIL>.

This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
%End

%DefaultSupertype PyQt6.sip.simplewrapper

%Include qabstractfileiconprovider.sip
%Include qabstracttextdocumentlayout.sip
%Include qaction.sip
%Include qactiongroup.sip
%Include qbackingstore.sip
%Include qbitmap.sip
%Include qcolor.sip
%Include qbrush.sip
%Include qclipboard.sip
%Include qcolorspace.sip
%Include qcolortransform.sip
%Include qcursor.sip
%Include qdesktopservices.sip
%Include qdrag.sip
%Include qevent.sip
%Include qeventpoint.sip
%Include qfilesystemmodel.sip
%Include qfont.sip
%Include qfontdatabase.sip
%Include qfontinfo.sip
%Include qfontmetrics.sip
%Include qfontvariableaxis.sip
%Include qgenericmatrix.sip
%Include qglyphrun.sip
%Include qguiapplication.sip
%Include qicon.sip
%Include qiconengine.sip
%Include qimage.sip
%Include qimageiohandler.sip
%Include qimagereader.sip
%Include qimagewriter.sip
%Include qinputdevice.sip
%Include qinputmethod.sip
%Include qkeysequence.sip
%Include qmatrix4x4.sip
%Include qmovie.sip
%Include qoffscreensurface.sip
%Include qopenglcontext.sip
%Include qpagedpaintdevice.sip
%Include qpagelayout.sip
%Include qpageranges.sip
%Include qpagesize.sip
%Include qpaintdevice.sip
%Include qpaintdevicewindow.sip
%Include qpaintengine.sip
%Include qpainter.sip
%Include qpainterpath.sip
%Include qpainterstateguard.sip
%Include qpalette.sip
%Include qpdfoutputintent.sip
%Include qpdfwriter.sip
%Include qpen.sip
%Include qpicture.sip
%Include qpixelformat.sip
%Include qpixmap.sip
%Include qpixmapcache.sip
%Include qpointingdevice.sip
%Include qpolygon.sip
%Include qquaternion.sip
%Include qrasterwindow.sip
%Include qrawfont.sip
%Include qregion.sip
%Include qrgba64.sip
%Include qrgb.sip
%Include qscreen.sip
%Include qsessionmanager.sip
%Include qshortcut.sip
%Include qstandarditemmodel.sip
%Include qstatictext.sip
%Include qstylehints.sip
%Include qsurface.sip
%Include qsurfaceformat.sip
%Include qsyntaxhighlighter.sip
%Include qtextcursor.sip
%Include qtextdocument.sip
%Include qtextdocumentfragment.sip
%Include qtextdocumentwriter.sip
%Include qtextformat.sip
%Include qtextlayout.sip
%Include qtextlist.sip
%Include qtextobject.sip
%Include qtextoption.sip
%Include qtexttable.sip
%Include qtransform.sip
%Include qundogroup.sip
%Include qundostack.sip
%Include qutimimeconverter.sip
%Include qvalidator.sip
%Include qvectornd.sip
%Include qwindow.sip
%Include qwindowdefs.sip
%Include opengl_types.sip
%Include qpygui_qlist.sip
%Include qpygui_vulkan.sip
