import { c as create_ssr_component, v as validate_component, b as createEventDispatcher, g as getContext, s as subscribe, d as add_attribute, e as escape, h as add_styles } from './ssr-C3HYbsxA.js';
import { t as tick } from './Component-NmRBwSfF.js';
import { TABS as L } from './Index46-BdwCIiVI.js';
import { h } from './2-DJbI4FWc.js';
import './index-ClteBeTX.js';
import 'path';
import 'url';
import 'fs';

const W={code:"div.svelte-wv8on1{display:flex;flex-direction:column;position:relative;border:none;border-radius:var(--radius-sm);padding:var(--block-padding);width:100%;box-sizing:border-box}.grow-children.svelte-wv8on1>.column > .column{flex-grow:1}",map:`{"version":3,"file":"TabItem.svelte","sources":["TabItem.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { getContext, onMount, createEventDispatcher, tick } from \\"svelte\\";\\nimport { TABS } from \\"@gradio/tabs\\";\\nimport Column from \\"@gradio/column\\";\\nexport let elem_id = \\"\\";\\nexport let elem_classes = [];\\nexport let label;\\nexport let id = {};\\nexport let visible;\\nexport let interactive;\\nexport let order;\\nexport let scale;\\nconst dispatch = createEventDispatcher();\\nconst { register_tab, unregister_tab, selected_tab, selected_tab_index } = getContext(TABS);\\nlet tab_index;\\n$: tab_index = register_tab({ label, id, elem_id, visible, interactive, scale }, order);\\nonMount(() => {\\n    return () => unregister_tab({ label, id, elem_id }, order);\\n});\\n$: $selected_tab_index === tab_index && tick().then(() => dispatch(\\"select\\", { value: label, index: tab_index }));\\n<\/script>\\n\\n{#if $selected_tab === id && visible}\\n\\t<div\\n\\t\\tid={elem_id}\\n\\t\\tclass=\\"tabitem {elem_classes.join(' ')}\\"\\n\\t\\tclass:grow-children={scale >= 1}\\n\\t\\tstyle:display={$selected_tab === id && visible ? \\"flex\\" : \\"none\\"}\\n\\t\\tstyle:flex-grow={scale}\\n\\t\\trole=\\"tabpanel\\"\\n\\t>\\n\\t\\t<Column scale={scale >= 1 ? scale : null}>\\n\\t\\t\\t<slot />\\n\\t\\t</Column>\\n\\t</div>\\n{/if}\\n\\n<style>\\n\\tdiv {\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: column;\\n\\t\\tposition: relative;\\n\\t\\tborder: none;\\n\\t\\tborder-radius: var(--radius-sm);\\n\\t\\tpadding: var(--block-padding);\\n\\t\\twidth: 100%;\\n\\t\\tbox-sizing: border-box;\\n\\t}\\n\\t.grow-children > :global(.column > .column) {\\n\\t\\tflex-grow: 1;\\n\\t}</style>\\n"],"names":[],"mappings":"AAqCC,iBAAI,CACH,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,IAAI,CACZ,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,OAAO,CAAE,IAAI,eAAe,CAAC,CAC7B,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,UACb,CACA,4BAAc,CAAW,iBAAmB,CAC3C,SAAS,CAAE,CACZ"}`},z=create_ssr_component((m,e,t,s)=>{let n,v,r,A,{elem_id:d=""}=e,{elem_classes:o=[]}=e,{label:i}=e,{id:l={}}=e,{visible:a}=e,{interactive:u}=e,{order:_}=e,{scale:c}=e;const h$1=createEventDispatcher(),{register_tab:w,unregister_tab:B,selected_tab:E,selected_tab_index:y}=getContext(L);A=subscribe(E,b=>r=b),v=subscribe(y,b=>n=b);let C;return e.elem_id===void 0&&t.elem_id&&d!==void 0&&t.elem_id(d),e.elem_classes===void 0&&t.elem_classes&&o!==void 0&&t.elem_classes(o),e.label===void 0&&t.label&&i!==void 0&&t.label(i),e.id===void 0&&t.id&&l!==void 0&&t.id(l),e.visible===void 0&&t.visible&&a!==void 0&&t.visible(a),e.interactive===void 0&&t.interactive&&u!==void 0&&t.interactive(u),e.order===void 0&&t.order&&_!==void 0&&t.order(_),e.scale===void 0&&t.scale&&c!==void 0&&t.scale(c),m.css.add(W),C=w({label:i,id:l,elem_id:d,visible:a,interactive:u,scale:c},_),n===C&&tick().then(()=>h$1("select",{value:i,index:C})),v(),A(),`${r===l&&a?`<div${add_attribute("id",d,0)} class="${["tabitem "+escape(o.join(" "),!0)+" svelte-wv8on1",c>=1?"grow-children":""].join(" ").trim()}" role="tabpanel"${add_styles({display:r===l&&a?"flex":"none","flex-grow":c})}>${validate_component(h,"Column").$$render(m,{scale:c>=1?c:null},{},{default:()=>`${s.default?s.default({}):""}`})}</div>`:""}`}),K=z,g=create_ssr_component((m,e,t,s)=>{let{elem_id:n=""}=e,{elem_classes:v=[]}=e,{label:r}=e,{id:A}=e,{gradio:d}=e,{visible:o=!0}=e,{interactive:i=!0}=e,{order:l}=e,{scale:a}=e;return e.elem_id===void 0&&t.elem_id&&n!==void 0&&t.elem_id(n),e.elem_classes===void 0&&t.elem_classes&&v!==void 0&&t.elem_classes(v),e.label===void 0&&t.label&&r!==void 0&&t.label(r),e.id===void 0&&t.id&&A!==void 0&&t.id(A),e.gradio===void 0&&t.gradio&&d!==void 0&&t.gradio(d),e.visible===void 0&&t.visible&&o!==void 0&&t.visible(o),e.interactive===void 0&&t.interactive&&i!==void 0&&t.interactive(i),e.order===void 0&&t.order&&l!==void 0&&t.order(l),e.scale===void 0&&t.scale&&a!==void 0&&t.scale(a),`${validate_component(K,"TabItem").$$render(m,{elem_id:n,elem_classes:v,label:r,visible:o,interactive:i,id:A,order:l,scale:a},{},{default:()=>`${o?`${s.default?s.default({}):""}`:""}`})}`});

export { K as BaseTabItem, g as default };
//# sourceMappingURL=Index45-YAX4orlg.js.map
