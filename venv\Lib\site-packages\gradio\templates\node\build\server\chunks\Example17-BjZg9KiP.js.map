{"version": 3, "file": "Example17-BjZg9KiP.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Example17.js"], "sourcesContent": ["import{create_ssr_component as n,validate_component as C}from\"svelte/internal\";import{J as i}from\"./JSON.js\";const s={code:\".container.svelte-v7ph9u img{width:100%;height:100%}.container.selected.svelte-v7ph9u{border-color:var(--border-color-accent)}.border.table.svelte-v7ph9u{border:1px solid var(--border-color-primary)}.container.table.svelte-v7ph9u{margin:0 auto;border-radius:var(--radius-lg);overflow:hidden;width:100%;height:100%;max-width:var(--size-40);max-height:var(--size-20);object-fit:cover}.container.gallery.svelte-v7ph9u{width:100%;max-width:100%;object-fit:cover;max-width:var(--size-40);max-height:var(--size-20);overflow:hidden}\",map:'{\"version\":3,\"file\":\"Example.svelte\",\"sources\":[\"Example.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import JSON from \\\\\"./shared/JSON.svelte\\\\\";\\\\nexport let value;\\\\nexport let theme_mode = \\\\\"system\\\\\";\\\\nlet show_indices = false;\\\\nlet label_height = 0;\\\\nexport let type;\\\\nexport let selected = false;\\\\n<\\/script>\\\\n\\\\n<div\\\\n\\\\tclass=\\\\\"container\\\\\"\\\\n\\\\tclass:table={type === \\\\\"table\\\\\"}\\\\n\\\\tclass:gallery={type === \\\\\"gallery\\\\\"}\\\\n\\\\tclass:selected\\\\n\\\\tclass:border={value}\\\\n>\\\\n\\\\t{#if value}\\\\n\\\\t\\\\t<JSON\\\\n\\\\t\\\\t\\\\t{value}\\\\n\\\\t\\\\t\\\\topen={true}\\\\n\\\\t\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\t\\\\t{show_indices}\\\\n\\\\t\\\\t\\\\t{label_height}\\\\n\\\\t\\\\t\\\\tinteractive={false}\\\\n\\\\t\\\\t\\\\tshow_copy_button={false}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.container :global(img) {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.container.selected {\\\\n\\\\t\\\\tborder-color: var(--border-color-accent);\\\\n\\\\t}\\\\n\\\\t.border.table {\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t}\\\\n\\\\n\\\\t.container.table {\\\\n\\\\t\\\\tmargin: 0 auto;\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tmax-width: var(--size-40);\\\\n\\\\t\\\\tmax-height: var(--size-20);\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t}\\\\n\\\\n\\\\t.container.gallery {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tmax-width: 100%;\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t\\\\tmax-width: var(--size-40);\\\\n\\\\t\\\\tmax-height: var(--size-20);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA8BC,wBAAU,CAAS,GAAK,CACvB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CAEA,UAAU,uBAAU,CACnB,YAAY,CAAE,IAAI,qBAAqB,CACxC,CACA,OAAO,oBAAO,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAC7C,CAEA,UAAU,oBAAO,CAChB,MAAM,CAAE,CAAC,CAAC,IAAI,CACd,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,QAAQ,CAAE,MAAM,CAChB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,UAAU,CAAE,IAAI,SAAS,CAAC,CAC1B,UAAU,CAAE,KACb,CAEA,UAAU,sBAAS,CAClB,KAAK,CAAE,IAAI,CACX,SAAS,CAAE,IAAI,CACf,UAAU,CAAE,KAAK,CACjB,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,UAAU,CAAE,IAAI,SAAS,CAAC,CAC1B,QAAQ,CAAE,MACX\"}'};let d=!1,c=0;const f=n((o,e,t,v)=>{let{value:A}=e,{theme_mode:l=\"system\"}=e,{type:a}=e,{selected:r=!1}=e;return e.value===void 0&&t.value&&A!==void 0&&t.value(A),e.theme_mode===void 0&&t.theme_mode&&l!==void 0&&t.theme_mode(l),e.type===void 0&&t.type&&a!==void 0&&t.type(a),e.selected===void 0&&t.selected&&r!==void 0&&t.selected(r),o.css.add(s),`<div class=\"${[\"container svelte-v7ph9u\",(a===\"table\"?\"table\":\"\")+\" \"+(a===\"gallery\"?\"gallery\":\"\")+\" \"+(r?\"selected\":\"\")+\" \"+(A?\"border\":\"\")].join(\" \").trim()}\">${A?`${C(i,\"JSON\").$$render(o,{value:A,open:!0,theme_mode:l,show_indices:d,label_height:c,interactive:!1,show_copy_button:!1},{},{})}`:\"\"} </div>`});export{f as default};\n//# sourceMappingURL=Example17.js.map\n"], "names": ["n", "C", "i"], "mappings": ";;;;;;;;;AAA6G,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,+gBAA+gB,CAAC,GAAG,CAAC,miEAAmiE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAM,MAAC,CAAC,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAEC,kBAAC,CAACC,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;;;;"}