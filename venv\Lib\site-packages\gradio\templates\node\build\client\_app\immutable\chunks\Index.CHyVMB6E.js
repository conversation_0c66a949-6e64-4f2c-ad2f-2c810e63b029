import{SvelteComponent as c,init as u,safe_not_equal as r,create_component as d,claim_component as b,mount_component as g,transition_in as m,transition_out as a,destroy_component as p,create_slot as v,update_slot_base as h,get_all_dirty_from_scope as k,get_slot_changes as B}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{B as q}from"./2.B2AoQPnG.js";function w(i){let t;const s=i[3].default,e=v(s,i,i[4],null);return{c(){e&&e.c()},l(l){e&&e.l(l)},m(l,n){e&&e.m(l,n),t=!0},p(l,n){e&&e.p&&(!t||n&16)&&h(e,s,l,l[4],t?B(s,l[4],n,null):k(l[4]),null)},i(l){t||(m(e,l),t=!0)},o(l){a(e,l),t=!1},d(l){e&&e.d(l)}}}function C(i){let t,s;return t=new q({props:{elem_id:i[0],elem_classes:i[1],visible:i[2],explicit_call:!0,$$slots:{default:[w]},$$scope:{ctx:i}}}),{c(){d(t.$$.fragment)},l(e){b(t.$$.fragment,e)},m(e,l){g(t,e,l),s=!0},p(e,[l]){const n={};l&1&&(n.elem_id=e[0]),l&2&&(n.elem_classes=e[1]),l&4&&(n.visible=e[2]),l&16&&(n.$$scope={dirty:l,ctx:e}),t.$set(n)},i(e){s||(m(t.$$.fragment,e),s=!0)},o(e){a(t.$$.fragment,e),s=!1},d(e){p(t,e)}}}function I(i,t,s){let{$$slots:e={},$$scope:l}=t,{elem_id:n}=t,{elem_classes:_}=t,{visible:f=!0}=t;return i.$$set=o=>{"elem_id"in o&&s(0,n=o.elem_id),"elem_classes"in o&&s(1,_=o.elem_classes),"visible"in o&&s(2,f=o.visible),"$$scope"in o&&s(4,l=o.$$scope)},[n,_,f,e,l]}class A extends c{constructor(t){super(),u(this,t,I,C,r,{elem_id:0,elem_classes:1,visible:2})}}export{A as default};
//# sourceMappingURL=Index.CHyVMB6E.js.map
