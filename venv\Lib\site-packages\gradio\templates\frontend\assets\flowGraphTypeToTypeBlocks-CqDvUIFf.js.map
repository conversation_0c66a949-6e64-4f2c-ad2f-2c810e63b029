{"version": 3, "file": "flowGraphTypeToTypeBlocks-CqDvUIFf.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Transformers/flowGraphTypeToTypeBlocks.js"], "sourcesContent": ["import { FlowGraphUnaryOperationBlock } from \"../flowGraphUnaryOperationBlock.js\";\nimport { RichTypeBoolean, RichTypeFlowGraphInteger, RichTypeNumber } from \"../../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\nimport { FlowGraphInteger } from \"../../../CustomTypes/flowGraphInteger.js\";\n/**\n * A block that converts a boolean to a float.\n */\nexport class FlowGraphBooleanToFloat extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeBoolean, RichTypeNumber, (a) => +a, \"FlowGraphBooleanToFloat\" /* FlowGraphBlockNames.BooleanToFloat */, config);\n    }\n}\nRegisterClass(\"FlowGraphBooleanToFloat\" /* FlowGraphBlockNames.BooleanToFloat */, FlowGraphBooleanToFloat);\n/**\n * A block that converts a boolean to an integer\n */\nexport class FlowGraphBooleanToInt extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeBoolean, RichTypeFlowGraphInteger, (a) => FlowGraphInteger.FromValue(+a), \"FlowGraphBooleanToInt\" /* FlowGraphBlockNames.BooleanToInt */, config);\n    }\n}\nRegisterClass(\"FlowGraphBooleanToInt\" /* FlowGraphBlockNames.BooleanToInt */, FlowGraphBooleanToInt);\n/**\n * A block that converts a float to a boolean.\n */\nexport class FlowGraphFloatToBoolean extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeNumber, RichTypeBoolean, (a) => !!a, \"FlowGraphFloatToBoolean\" /* FlowGraphBlockNames.FloatToBoolean */, config);\n    }\n}\nRegisterClass(\"FlowGraphFloatToBoolean\" /* FlowGraphBlockNames.FloatToBoolean */, FlowGraphFloatToBoolean);\n/**\n * A block that converts an integer to a boolean.\n */\nexport class FlowGraphIntToBoolean extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeFlowGraphInteger, RichTypeBoolean, (a) => !!a.value, \"FlowGraphIntToBoolean\" /* FlowGraphBlockNames.IntToBoolean */, config);\n    }\n}\nRegisterClass(\"FlowGraphIntToBoolean\" /* FlowGraphBlockNames.IntToBoolean */, FlowGraphIntToBoolean);\n/**\n * A block that converts an integer to a float.\n */\nexport class FlowGraphIntToFloat extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeFlowGraphInteger, RichTypeNumber, (a) => a.value, \"FlowGraphIntToFloat\" /* FlowGraphBlockNames.IntToFloat */, config);\n    }\n}\nRegisterClass(\"FlowGraphIntToFloat\" /* FlowGraphBlockNames.IntToFloat */, FlowGraphIntToFloat);\n/**\n * A block that converts a float to an integer.\n */\nexport class FlowGraphFloatToInt extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeNumber, RichTypeFlowGraphInteger, (a) => {\n            const roundingMode = config?.roundingMode;\n            switch (roundingMode) {\n                case \"floor\":\n                    return FlowGraphInteger.FromValue(Math.floor(a));\n                case \"ceil\":\n                    return FlowGraphInteger.FromValue(Math.ceil(a));\n                case \"round\":\n                    return FlowGraphInteger.FromValue(Math.round(a));\n                default:\n                    return FlowGraphInteger.FromValue(a);\n            }\n        }, \"FlowGraphFloatToInt\" /* FlowGraphBlockNames.FloatToInt */, config);\n    }\n}\nRegisterClass(\"FlowGraphFloatToInt\" /* FlowGraphBlockNames.FloatToInt */, FlowGraphFloatToInt);\n//# sourceMappingURL=flowGraphTypeToTypeBlocks.js.map"], "names": ["FlowGraphBooleanToFloat", "FlowGraphUnaryOperationBlock", "config", "RichTypeBoolean", "RichTypeNumber", "a", "RegisterClass", "FlowGraphBooleanToInt", "RichTypeFlowGraphInteger", "FlowGraphInteger", "FlowGraphFloatToBoolean", "FlowGraphIntToBoolean", "FlowGraphIntToFloat", "FlowGraphFloatToInt"], "mappings": "4WAOO,MAAMA,UAAgCC,CAA6B,CACtE,YAAYC,EAAQ,CAChB,MAAMC,EAAiBC,EAAiBC,GAAM,CAACA,EAAG,0BAAoEH,CAAM,CAC/H,CACL,CACAI,EAAc,0BAAoEN,CAAuB,EAIlG,MAAMO,UAA8BN,CAA6B,CACpE,YAAYC,EAAQ,CAChB,MAAMC,EAAiBK,EAA2BH,GAAMI,EAAiB,UAAU,CAACJ,CAAC,EAAG,wBAAgEH,CAAM,CACjK,CACL,CACAI,EAAc,wBAAgEC,CAAqB,EAI5F,MAAMG,UAAgCT,CAA6B,CACtE,YAAYC,EAAQ,CAChB,MAAME,EAAgBD,EAAkBE,GAAM,CAAC,CAACA,EAAG,0BAAoEH,CAAM,CAChI,CACL,CACAI,EAAc,0BAAoEI,CAAuB,EAIlG,MAAMC,UAA8BV,CAA6B,CACpE,YAAYC,EAAQ,CAChB,MAAMM,EAA0BL,EAAkBE,GAAM,CAAC,CAACA,EAAE,MAAO,wBAAgEH,CAAM,CAC5I,CACL,CACAI,EAAc,wBAAgEK,CAAqB,EAI5F,MAAMC,UAA4BX,CAA6B,CAClE,YAAYC,EAAQ,CAChB,MAAMM,EAA0BJ,EAAiBC,GAAMA,EAAE,MAAO,sBAA4DH,CAAM,CACrI,CACL,CACAI,EAAc,sBAA4DM,CAAmB,EAItF,MAAMC,UAA4BZ,CAA6B,CAClE,YAAYC,EAAQ,CAChB,MAAME,EAAgBI,EAA2BH,GAAM,CAEnD,OADqBH,GAAQ,aACT,CAChB,IAAK,QACD,OAAOO,EAAiB,UAAU,KAAK,MAAMJ,CAAC,CAAC,EACnD,IAAK,OACD,OAAOI,EAAiB,UAAU,KAAK,KAAKJ,CAAC,CAAC,EAClD,IAAK,QACD,OAAOI,EAAiB,UAAU,KAAK,MAAMJ,CAAC,CAAC,EACnD,QACI,OAAOI,EAAiB,UAAUJ,CAAC,CAC1C,CACb,EAAW,sBAA4DH,CAAM,CACxE,CACL,CACAI,EAAc,sBAA4DO,CAAmB", "x_google_ignoreList": [0]}