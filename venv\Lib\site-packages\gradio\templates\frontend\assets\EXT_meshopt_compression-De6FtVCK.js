import{GLTFLoader as m,ArrayItem as h}from"./glTFLoader-9Z3KGax5.js";import{b as i,an as p,ao as _}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./bone-kZWM5-u7.js";import"./rawTexture-DmvUfjqF.js";import"./assetContainer-BRzQBugc.js";import"./objectModelMapping-BR4RdEzn.js";let f=0,d=null;class r{static get Default(){return r._Default||(r._Default=new r),r._Default}constructor(){const e=r.Configuration.decoder;this._decoderModulePromise=i.LoadBabylonScriptAsync(e.url).then(()=>MeshoptDecoder.ready)}dispose(){delete this._decoderModulePromise}decodeGltfBufferAsync(e,s,n,t,o){return this._decoderModulePromise.then(async()=>{f===0&&(MeshoptDecoder.useWorkers(1),f=1);const u=await MeshoptDecoder.decodeGltfBufferAsync(s,n,e,t,o);return d!==null&&clearTimeout(d),d=setTimeout(()=>{MeshoptDecoder.useWorkers(0),f=0,d=null},1e3),u})}}r.Configuration={decoder:{url:`${i._DefaultCdnUrl}/meshopt_decoder.js`}};r._Default=null;const a="EXT_meshopt_compression";class D{constructor(e){this.name=a,this.enabled=e.isExtensionUsed(a),this._loader=e}dispose(){this._loader=null}loadBufferViewAsync(e,s){return m.LoadExtensionAsync(e,s,this.name,(n,t)=>{const o=s;if(o._meshOptData)return o._meshOptData;const u=h.Get(`${e}/buffer`,this._loader.gltf.buffers,t.buffer);return o._meshOptData=this._loader.loadBufferAsync(`/buffers/${u.index}`,u,t.byteOffset||0,t.byteLength).then(c=>r.Default.decodeGltfBufferAsync(c,t.count,t.byteStride,t.mode,t.filter)),o._meshOptData})}}p(a);_(a,!0,l=>new D(l));export{D as EXT_meshopt_compression};
//# sourceMappingURL=EXT_meshopt_compression-De6FtVCK.js.map
