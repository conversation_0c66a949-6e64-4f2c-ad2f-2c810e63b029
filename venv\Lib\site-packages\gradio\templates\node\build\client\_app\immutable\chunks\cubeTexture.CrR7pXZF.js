import{B as C,M as g,O as P,V as p,T as M,a as S,b as E,S as L,G as D,_ as u,s as c,c as A,d as z,R as I}from"./index.BoI39RQH.js";import"./abstractEngine.cubeTexture.ELnmP5e-.js";const v=.8;class l extends C{set boundingBoxSize(t){if(this._boundingBoxSize&&this._boundingBoxSize.equals(t))return;this._boundingBoxSize=t;const i=this.getScene();i&&i.markAllMaterialsAsDirty(1)}get boundingBoxSize(){return this._boundingBoxSize}set rotationY(t){this._rotationY=t,this.setReflectionTextureMatrix(g.RotationY(this._rotationY))}get rotationY(){return this._rotationY}get noMipmap(){return this._noMipmap}get forcedExtension(){return this._forcedExtension}static CreateFromImages(t,i,e){let a="";return t.forEach(s=>a+=s),new l(a,i,null,e,t)}static CreateFromPrefilteredData(t,i,e=null,a=!0){const s=i.useDelayedTextureLoading;i.useDelayedTextureLoading=!1;const r=new l(t,i,null,!1,null,null,null,void 0,!0,e,a);return i.useDelayedTextureLoading=s,r}constructor(t,i,e=null,a=!1,s=null,r=null,n=null,o=5,h=!1,f=null,d=!1,m=v,x=0,b,_){var R;super(i),this.onLoadObservable=new P,this.boundingBoxPosition=p.Zero(),this._rotationY=0,this._files=null,this._forcedExtension=null,this._extensions=null,this._textureMatrixRefraction=new g,this._buffer=null,this.name=t,this.url=t,this._noMipmap=a,this.hasAlpha=!1,this.isCube=!0,this._textureMatrix=g.Identity(),this.coordinatesMode=M.CUBIC_MODE;let y=null,B=null;e!==null&&!Array.isArray(e)?(y=e.extensions??null,this._noMipmap=e.noMipmap??!1,s=e.files??null,B=e.buffer??null,this._format=e.format??5,h=e.prefiltered??!1,f=e.forcedExtension??null,this._createPolynomials=e.createPolynomials??!1,this._lodScale=e.lodScale??v,this._lodOffset=e.lodOffset??0,this._loaderOptions=e.loaderOptions,this._useSRGBBuffer=e.useSRGBBuffer,r=e.onLoad??null,n=e.onError??null):(this._noMipmap=a,this._format=o,this._createPolynomials=d,y=e,this._loaderOptions=b,this._useSRGBBuffer=_,this._lodScale=m,this._lodOffset=x),!(!t&&!s)&&this.updateURL(t,f,r,h,n,y,(R=this.getScene())==null?void 0:R.useDelayedTextureLoading,s,B)}getClassName(){return"CubeTexture"}updateURL(t,i=null,e=null,a=!1,s=null,r=null,n=!1,o=null,h=null){(!this.name||this.name.startsWith("data:"))&&(this.name=t),this.url=t,i&&(this._forcedExtension=i);const f=t.lastIndexOf("."),d=i||(f>-1?t.substring(f).toLowerCase():""),m=d.indexOf(".dds")===0,x=d.indexOf(".env")===0,b=d.indexOf(".basis")===0;if(x?(this.gammaSpace=!1,this._prefiltered=!1,this.anisotropicFilteringLevel=1):(this._prefiltered=a,a&&(this.gammaSpace=!1,this.anisotropicFilteringLevel=1)),o)this._files=o;else if(!b&&!x&&!m&&!r&&(r=["_px.jpg","_py.jpg","_pz.jpg","_nx.jpg","_ny.jpg","_nz.jpg"]),this._files=this._files||[],this._files.length=0,r){for(let _=0;_<r.length;_++)this._files.push(t+r[_]);this._extensions=r}this._buffer=h,n?(this.delayLoadState=4,this._delayedOnLoad=e,this._delayedOnError=s):this._loadTexture(e,s)}delayLoad(t){this.delayLoadState===4&&(t&&(this._forcedExtension=t),this.delayLoadState=1,this._loadTexture(this._delayedOnLoad,this._delayedOnError))}getReflectionTextureMatrix(){return this._textureMatrix}setReflectionTextureMatrix(t){var s,r;if(t.updateFlag===this._textureMatrix.updateFlag||(t.isIdentity()!==this._textureMatrix.isIdentity()&&((s=this.getScene())==null||s.markAllMaterialsAsDirty(1,n=>n.getActiveTextures().indexOf(this)!==-1)),this._textureMatrix=t,!((r=this.getScene())!=null&&r.useRightHandedSystem)))return;const i=S.Vector3[0],e=S.Quaternion[0],a=S.Vector3[1];this._textureMatrix.decompose(i,e,a),e.z*=-1,e.w*=-1,g.ComposeToRef(i,e,a,this._textureMatrixRefraction)}getRefractionTextureMatrix(){var t;return(t=this.getScene())!=null&&t.useRightHandedSystem?this._textureMatrixRefraction:this._textureMatrix}_loadTexture(t=null,i=null){var n;const e=this.getScene(),a=this._texture;this._texture=this._getFromCache(this.url,this._noMipmap,void 0,void 0,this._useSRGBBuffer,this.isCube);const s=()=>{var o;this.onLoadObservable.notifyObservers(this),a&&(a.dispose(),(o=this.getScene())==null||o.markAllMaterialsAsDirty(1)),t&&t()},r=(o,h)=>{this._loadingError=!0,this._errorObject={message:o,exception:h},i&&i(o,h),M.OnTextureLoadErrorObservable.notifyObservers(this)};this._texture?this._texture.isReady?E.SetImmediate(()=>s()):this._texture.onLoadedObservable.add(()=>s()):(this._prefiltered?this._texture=this._getEngine().createPrefilteredCubeTexture(this.url,e,this._lodScale,this._lodOffset,t,r,this._format,this._forcedExtension,this._createPolynomials):this._texture=this._getEngine().createCubeTexture(this.url,e,this._files,this._noMipmap,t,r,this._format,this._forcedExtension,!1,this._lodScale,this._lodOffset,null,this._loaderOptions,!!this._useSRGBBuffer,this._buffer),(n=this._texture)==null||n.onLoadedObservable.add(()=>this.onLoadObservable.notifyObservers(this)))}static Parse(t,i,e){const a=L.Parse(()=>{let s=!1;return t.prefiltered&&(s=t.prefiltered),new l(e+(t.url??t.name),i,t.extensions,!1,t.files||null,null,null,void 0,s,t.forcedExtension)},t,i);if(t.boundingBoxPosition&&(a.boundingBoxPosition=p.FromArray(t.boundingBoxPosition)),t.boundingBoxSize&&(a.boundingBoxSize=p.FromArray(t.boundingBoxSize)),t.animations)for(let s=0;s<t.animations.length;s++){const r=t.animations[s],n=D("BABYLON.Animation");n&&a.animations.push(n.Parse(r))}return a}clone(){let t=0;const i=L.Clone(()=>{const e=new l(this.url,this.getScene()||this._getEngine(),this._extensions,this._noMipmap,this._files);return t=e.uniqueId,e},this);return i.uniqueId=t,i}}u([c()],l.prototype,"url",void 0);u([A()],l.prototype,"boundingBoxPosition",void 0);u([A()],l.prototype,"boundingBoxSize",null);u([c("rotationY")],l.prototype,"rotationY",null);u([c("files")],l.prototype,"_files",void 0);u([c("forcedExtension")],l.prototype,"_forcedExtension",void 0);u([c("extensions")],l.prototype,"_extensions",void 0);u([z("textureMatrix")],l.prototype,"_textureMatrix",void 0);u([z("textureMatrixRefraction")],l.prototype,"_textureMatrixRefraction",void 0);M._CubeTextureParser=l.Parse;I("BABYLON.CubeTexture",l);export{l as CubeTexture};
//# sourceMappingURL=cubeTexture.CrR7pXZF.js.map
