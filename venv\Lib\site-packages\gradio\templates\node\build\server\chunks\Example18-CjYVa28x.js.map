{"version": 3, "file": "Example18-CjYVa28x.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Example18.js"], "sourcesContent": ["import{create_ssr_component as d,validate_component as c}from\"svelte/internal\";import{M as m}from\"./MarkdownCode.js\";const v={code:\".gallery.svelte-1ayixqk{padding:var(--size-1) var(--size-2)}\",map:'{\"version\":3,\"file\":\"Example.svelte\",\"sources\":[\"Example.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { MarkdownCode } from \\\\\"@gradio/markdown-code\\\\\";\\\\nexport let value;\\\\nexport let type;\\\\nexport let selected = false;\\\\nexport let sanitize_html;\\\\nexport let line_breaks;\\\\nexport let latex_delimiters;\\\\nfunction truncate_text(text, max_length = 60) {\\\\n    if (!text)\\\\n        return \\\\\"\\\\\";\\\\n    const str = String(text);\\\\n    if (str.length <= max_length)\\\\n        return str;\\\\n    return str.slice(0, max_length) + \\\\\"...\\\\\";\\\\n}\\\\n<\\/script>\\\\n\\\\n<div\\\\n\\\\tclass:table={type === \\\\\"table\\\\\"}\\\\n\\\\tclass:gallery={type === \\\\\"gallery\\\\\"}\\\\n\\\\tclass:selected\\\\n\\\\tclass=\\\\\"prose\\\\\"\\\\n>\\\\n\\\\t<MarkdownCode\\\\n\\\\t\\\\tmessage={truncate_text(value)}\\\\n\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\tchatbot={false}\\\\n\\\\t/>\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.gallery {\\\\n\\\\t\\\\tpadding: var(--size-1) var(--size-2);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAiCC,uBAAS,CACR,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CACpC\"}'};function _(l,t=60){if(!l)return\"\";const e=String(l);return e.length<=t?e:e.slice(0,t)+\"...\"}const u=d((l,t,e,x)=>{let{value:n}=t,{type:a}=t,{selected:r=!1}=t,{sanitize_html:s}=t,{line_breaks:i}=t,{latex_delimiters:o}=t;return t.value===void 0&&e.value&&n!==void 0&&e.value(n),t.type===void 0&&e.type&&a!==void 0&&e.type(a),t.selected===void 0&&e.selected&&r!==void 0&&e.selected(r),t.sanitize_html===void 0&&e.sanitize_html&&s!==void 0&&e.sanitize_html(s),t.line_breaks===void 0&&e.line_breaks&&i!==void 0&&e.line_breaks(i),t.latex_delimiters===void 0&&e.latex_delimiters&&o!==void 0&&e.latex_delimiters(o),l.css.add(v),`<div class=\"${[\"prose svelte-1ayixqk\",(a===\"table\"?\"table\":\"\")+\" \"+(a===\"gallery\"?\"gallery\":\"\")+\" \"+(r?\"selected\":\"\")].join(\" \").trim()}\">${c(m,\"MarkdownCode\").$$render(l,{message:_(n),latex_delimiters:o,sanitize_html:s,line_breaks:i,chatbot:!1},{},{})} </div>`});export{u as default};\n//# sourceMappingURL=Example18.js.map\n"], "names": ["d", "c", "m"], "mappings": ";;;;;;;;AAAqH,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,8DAA8D,CAAC,GAAG,CAAC,2iCAA2iC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAM,EAAE,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAM,MAAC,CAAC,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAEC,kBAAC,CAACC,EAAC,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;;;;"}