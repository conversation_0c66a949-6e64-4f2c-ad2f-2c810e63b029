{"version": 3, "file": "flowGraphStopAnimationBlock.DRdSCS1A.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/Animation/flowGraphStopAnimationBlock.js"], "sourcesContent": ["import { RichTypeAny, RichTypeN<PERSON>ber } from \"../../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\nimport { Logger } from \"../../../../Misc/logger.js\";\nimport { FlowGraphAsyncExecutionBlock } from \"../../../flowGraphAsyncExecutionBlock.js\";\n/**\n * @experimental\n * Block that stops a running animation\n */\nexport class FlowGraphStopAnimationBlock extends FlowGraphAsyncExecutionBlock {\n    constructor(config) {\n        super(config);\n        this.animationGroup = this.registerDataInput(\"animationGroup\", RichTypeAny);\n        this.stopAtFrame = this.registerDataInput(\"stopAtFrame\", RichTypeNumber, -1);\n    }\n    _preparePendingTasks(context) {\n        const animationToStopValue = this.animationGroup.getValue(context);\n        const stopAtFrame = this.stopAtFrame.getValue(context) ?? -1;\n        // get the context variable\n        const pendingStopAnimations = context._getGlobalContextVariable(\"pendingStopAnimations\", []);\n        // add the animation to the list\n        pendingStopAnimations.push({ uniqueId: animationToStopValue.uniqueId, stopAtFrame });\n        // set the global context variable\n        context._setGlobalContextVariable(\"pendingStopAnimations\", pendingStopAnimations);\n    }\n    _cancelPendingTasks(context) {\n        // remove the animation from the list\n        const animationToStopValue = this.animationGroup.getValue(context);\n        const pendingStopAnimations = context._getGlobalContextVariable(\"pendingStopAnimations\", []);\n        for (let i = 0; i < pendingStopAnimations.length; i++) {\n            if (pendingStopAnimations[i].uniqueId === animationToStopValue.uniqueId) {\n                pendingStopAnimations.splice(i, 1);\n                // set the global context variable\n                context._setGlobalContextVariable(\"pendingStopAnimations\", pendingStopAnimations);\n                break;\n            }\n        }\n    }\n    _execute(context) {\n        const animationToStopValue = this.animationGroup.getValue(context);\n        const stopTime = this.stopAtFrame.getValue(context) ?? -1;\n        // check the values\n        if (!animationToStopValue) {\n            Logger.Warn(\"No animation group provided to stop.\");\n            return this._reportError(context, \"No animation group provided to stop.\");\n        }\n        if (isNaN(stopTime)) {\n            return this._reportError(context, \"Invalid stop time.\");\n        }\n        if (stopTime > 0) {\n            this._startPendingTasks(context);\n        }\n        else {\n            this._stopAnimation(animationToStopValue, context);\n        }\n        // note that out will not be triggered in case of an error\n        this.out._activateSignal(context);\n    }\n    _executeOnTick(context) {\n        const animationToStopValue = this.animationGroup.getValue(context);\n        // check each frame if any animation should be stopped\n        const pendingStopAnimations = context._getGlobalContextVariable(\"pendingStopAnimations\", []);\n        for (let i = 0; i < pendingStopAnimations.length; i++) {\n            // compare the uniqueId to the animation to stop\n            if (pendingStopAnimations[i].uniqueId === animationToStopValue.uniqueId) {\n                // check if the current frame is AFTER the stopAtFrame\n                if (animationToStopValue.getCurrentFrame() >= pendingStopAnimations[i].stopAtFrame) {\n                    // stop the animation\n                    this._stopAnimation(animationToStopValue, context);\n                    // remove the animation from the list\n                    pendingStopAnimations.splice(i, 1);\n                    // set the global context variable\n                    context._setGlobalContextVariable(\"pendingStopAnimations\", pendingStopAnimations);\n                    this.done._activateSignal(context);\n                    context._removePendingBlock(this);\n                    break;\n                }\n            }\n        }\n    }\n    /**\n     * @returns class name of the block.\n     */\n    getClassName() {\n        return \"FlowGraphStopAnimationBlock\" /* FlowGraphBlockNames.StopAnimation */;\n    }\n    _stopAnimation(animationGroup, context) {\n        const currentlyRunning = context._getGlobalContextVariable(\"currentlyRunningAnimationGroups\", []);\n        const index = currentlyRunning.indexOf(animationGroup.uniqueId);\n        if (index !== -1) {\n            animationGroup.stop();\n            currentlyRunning.splice(index, 1);\n            // update the global context variable\n            context._setGlobalContextVariable(\"currentlyRunningAnimationGroups\", currentlyRunning);\n        }\n        else {\n            // Logger.Warn(\"Trying to stop an animation that is not running.\");\n            // no-op for now. Probably no need to log anything here.\n        }\n    }\n}\nRegisterClass(\"FlowGraphStopAnimationBlock\" /* FlowGraphBlockNames.StopAnimation */, FlowGraphStopAnimationBlock);\n//# sourceMappingURL=flowGraphStopAnimationBlock.js.map"], "names": ["FlowGraphStopAnimationBlock", "FlowGraphAsyncExecutionBlock", "config", "RichTypeAny", "RichTypeNumber", "context", "animationToStopValue", "stopAtFrame", "pendingStopAnimations", "i", "stopTime", "<PERSON><PERSON>", "animationGroup", "currentlyRunning", "index", "RegisterClass"], "mappings": "8JAQO,MAAMA,UAAoCC,CAA6B,CAC1E,YAAYC,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,eAAiB,KAAK,kBAAkB,iBAAkBC,CAAW,EAC1E,KAAK,YAAc,KAAK,kBAAkB,cAAeC,EAAgB,EAAE,CAC9E,CACD,qBAAqBC,EAAS,CAC1B,MAAMC,EAAuB,KAAK,eAAe,SAASD,CAAO,EAC3DE,EAAc,KAAK,YAAY,SAASF,CAAO,GAAK,GAEpDG,EAAwBH,EAAQ,0BAA0B,wBAAyB,CAAE,CAAA,EAE3FG,EAAsB,KAAK,CAAE,SAAUF,EAAqB,SAAU,YAAAC,CAAW,CAAE,EAEnFF,EAAQ,0BAA0B,wBAAyBG,CAAqB,CACnF,CACD,oBAAoBH,EAAS,CAEzB,MAAMC,EAAuB,KAAK,eAAe,SAASD,CAAO,EAC3DG,EAAwBH,EAAQ,0BAA0B,wBAAyB,CAAE,CAAA,EAC3F,QAASI,EAAI,EAAGA,EAAID,EAAsB,OAAQC,IAC9C,GAAID,EAAsBC,CAAC,EAAE,WAAaH,EAAqB,SAAU,CACrEE,EAAsB,OAAOC,EAAG,CAAC,EAEjCJ,EAAQ,0BAA0B,wBAAyBG,CAAqB,EAChF,KACH,CAER,CACD,SAASH,EAAS,CACd,MAAMC,EAAuB,KAAK,eAAe,SAASD,CAAO,EAC3DK,EAAW,KAAK,YAAY,SAASL,CAAO,GAAK,GAEvD,GAAI,CAACC,EACD,OAAAK,EAAO,KAAK,sCAAsC,EAC3C,KAAK,aAAaN,EAAS,sCAAsC,EAE5E,GAAI,MAAMK,CAAQ,EACd,OAAO,KAAK,aAAaL,EAAS,oBAAoB,EAEtDK,EAAW,EACX,KAAK,mBAAmBL,CAAO,EAG/B,KAAK,eAAeC,EAAsBD,CAAO,EAGrD,KAAK,IAAI,gBAAgBA,CAAO,CACnC,CACD,eAAeA,EAAS,CACpB,MAAMC,EAAuB,KAAK,eAAe,SAASD,CAAO,EAE3DG,EAAwBH,EAAQ,0BAA0B,wBAAyB,CAAE,CAAA,EAC3F,QAASI,EAAI,EAAGA,EAAID,EAAsB,OAAQC,IAE9C,GAAID,EAAsBC,CAAC,EAAE,WAAaH,EAAqB,UAEvDA,EAAqB,gBAAiB,GAAIE,EAAsBC,CAAC,EAAE,YAAa,CAEhF,KAAK,eAAeH,EAAsBD,CAAO,EAEjDG,EAAsB,OAAOC,EAAG,CAAC,EAEjCJ,EAAQ,0BAA0B,wBAAyBG,CAAqB,EAChF,KAAK,KAAK,gBAAgBH,CAAO,EACjCA,EAAQ,oBAAoB,IAAI,EAChC,KACH,CAGZ,CAID,cAAe,CACX,MAAO,6BACV,CACD,eAAeO,EAAgBP,EAAS,CACpC,MAAMQ,EAAmBR,EAAQ,0BAA0B,kCAAmC,CAAE,CAAA,EAC1FS,EAAQD,EAAiB,QAAQD,EAAe,QAAQ,EAC1DE,IAAU,KACVF,EAAe,KAAI,EACnBC,EAAiB,OAAOC,EAAO,CAAC,EAEhCT,EAAQ,0BAA0B,kCAAmCQ,CAAgB,EAM5F,CACL,CACAE,EAAc,8BAAuEf,CAA2B", "x_google_ignoreList": [0]}