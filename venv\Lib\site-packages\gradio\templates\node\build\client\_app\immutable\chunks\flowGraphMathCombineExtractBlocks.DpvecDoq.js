import{F}from"./flowGraphCachedOperationBlock.CtP7sxiu.js";import{f as l,e as n,l as h,h as g,m as o,n as V,o as c,p as _,b as D}from"./declarationMapper.UBCwU7BT.js";import{F as G}from"./KHR_interactivity.DEAVS2UW.js";import{ay as I,R as p,V as m,az as w,M as d}from"./index.BoI39RQH.js";class s extends F{constructor(t,a,e){super(a,e);for(let u=0;u<t;u++)this.registerDataInput(`input_${u}`,D,0)}}class r extends G{constructor(t,a,e){super(e),this.registerDataInput("input",a);for(let u=0;u<t;u++)this.registerDataOutput(`output_${u}`,D,0)}}class k extends s{constructor(t){super(2,l,t)}_doOperation(t){t._hasExecutionVariable(this,"cachedVector")||t._setExecutionVariable(this,"cachedVector",new I);const a=t._getExecutionVariable(this,"cachedVector",null);return a.set(this.getDataInput("input_0").getValue(t),this.getDataInput("input_1").getValue(t)),a}getClassName(){return"FlowGraphCombineVector2Block"}}p("FlowGraphCombineVector2Block",k);class B extends s{constructor(t){super(3,n,t)}_doOperation(t){t._hasExecutionVariable(this,"cachedVector")||t._setExecutionVariable(this,"cachedVector",new m);const a=t._getExecutionVariable(this,"cachedVector",null);return a.set(this.getDataInput("input_0").getValue(t),this.getDataInput("input_1").getValue(t),this.getDataInput("input_2").getValue(t)),a}getClassName(){return"FlowGraphCombineVector3Block"}}p("FlowGraphCombineVector3Block",B);class b extends s{constructor(t){super(4,h,t)}_doOperation(t){t._hasExecutionVariable(this,"cachedVector")||t._setExecutionVariable(this,"cachedVector",new w);const a=t._getExecutionVariable(this,"cachedVector",null);return a.set(this.getDataInput("input_0").getValue(t),this.getDataInput("input_1").getValue(t),this.getDataInput("input_2").getValue(t),this.getDataInput("input_3").getValue(t)),a}getClassName(){return"FlowGraphCombineVector4Block"}}p("FlowGraphCombineVector4Block",b);class M extends s{constructor(t){super(16,g,t)}_doOperation(t){var e;t._hasExecutionVariable(this,"cachedMatrix")||t._setExecutionVariable(this,"cachedMatrix",new d);const a=t._getExecutionVariable(this,"cachedMatrix",null);return(e=this.config)!=null&&e.inputIsColumnMajor?a.set(this.getDataInput("input_0").getValue(t),this.getDataInput("input_4").getValue(t),this.getDataInput("input_8").getValue(t),this.getDataInput("input_12").getValue(t),this.getDataInput("input_1").getValue(t),this.getDataInput("input_5").getValue(t),this.getDataInput("input_9").getValue(t),this.getDataInput("input_13").getValue(t),this.getDataInput("input_2").getValue(t),this.getDataInput("input_6").getValue(t),this.getDataInput("input_10").getValue(t),this.getDataInput("input_14").getValue(t),this.getDataInput("input_3").getValue(t),this.getDataInput("input_7").getValue(t),this.getDataInput("input_11").getValue(t),this.getDataInput("input_15").getValue(t)):a.set(this.getDataInput("input_0").getValue(t),this.getDataInput("input_1").getValue(t),this.getDataInput("input_2").getValue(t),this.getDataInput("input_3").getValue(t),this.getDataInput("input_4").getValue(t),this.getDataInput("input_5").getValue(t),this.getDataInput("input_6").getValue(t),this.getDataInput("input_7").getValue(t),this.getDataInput("input_8").getValue(t),this.getDataInput("input_9").getValue(t),this.getDataInput("input_10").getValue(t),this.getDataInput("input_11").getValue(t),this.getDataInput("input_12").getValue(t),this.getDataInput("input_13").getValue(t),this.getDataInput("input_14").getValue(t),this.getDataInput("input_15").getValue(t)),a}getClassName(){return"FlowGraphCombineMatrixBlock"}}p("FlowGraphCombineMatrixBlock",M);class E extends s{constructor(t){super(4,o,t)}_doOperation(t){var u;t._hasExecutionVariable(this,"cachedMatrix")||t._setExecutionVariable(this,"cachedMatrix",new V);const a=t._getExecutionVariable(this,"cachedMatrix",null),e=(u=this.config)!=null&&u.inputIsColumnMajor?[this.getDataInput("input_0").getValue(t),this.getDataInput("input_2").getValue(t),this.getDataInput("input_1").getValue(t),this.getDataInput("input_3").getValue(t)]:[this.getDataInput("input_0").getValue(t),this.getDataInput("input_1").getValue(t),this.getDataInput("input_2").getValue(t),this.getDataInput("input_3").getValue(t)];return a.fromArray(e),a}getClassName(){return"FlowGraphCombineMatrix2DBlock"}}p("FlowGraphCombineMatrix2DBlock",E);class C extends s{constructor(t){super(9,c,t)}_doOperation(t){var u;t._hasExecutionVariable(this,"cachedMatrix")||t._setExecutionVariable(this,"cachedMatrix",new _);const a=t._getExecutionVariable(this,"cachedMatrix",null),e=(u=this.config)!=null&&u.inputIsColumnMajor?[this.getDataInput("input_0").getValue(t),this.getDataInput("input_3").getValue(t),this.getDataInput("input_6").getValue(t),this.getDataInput("input_1").getValue(t),this.getDataInput("input_4").getValue(t),this.getDataInput("input_7").getValue(t),this.getDataInput("input_2").getValue(t),this.getDataInput("input_5").getValue(t),this.getDataInput("input_8").getValue(t)]:[this.getDataInput("input_0").getValue(t),this.getDataInput("input_1").getValue(t),this.getDataInput("input_2").getValue(t),this.getDataInput("input_3").getValue(t),this.getDataInput("input_4").getValue(t),this.getDataInput("input_5").getValue(t),this.getDataInput("input_6").getValue(t),this.getDataInput("input_7").getValue(t),this.getDataInput("input_8").getValue(t)];return a.fromArray(e),a}getClassName(){return"FlowGraphCombineMatrix3DBlock"}}p("FlowGraphCombineMatrix3DBlock",C);class f extends r{constructor(t){super(2,l,t)}_updateOutputs(t){var e;let a=(e=this.getDataInput("input"))==null?void 0:e.getValue(t);a||(a=I.Zero(),this.getDataInput("input").setValue(a,t)),this.getDataOutput("output_0").setValue(a.x,t),this.getDataOutput("output_1").setValue(a.y,t)}getClassName(){return"FlowGraphExtractVector2Block"}}p("FlowGraphExtractVector2Block",f);class O extends r{constructor(t){super(3,n,t)}_updateOutputs(t){var e;let a=(e=this.getDataInput("input"))==null?void 0:e.getValue(t);a||(a=m.Zero(),this.getDataInput("input").setValue(a,t)),this.getDataOutput("output_0").setValue(a.x,t),this.getDataOutput("output_1").setValue(a.y,t),this.getDataOutput("output_2").setValue(a.z,t)}getClassName(){return"FlowGraphExtractVector3Block"}}p("FlowGraphExtractVector3Block",O);class y extends r{constructor(t){super(4,h,t)}_updateOutputs(t){var e;let a=(e=this.getDataInput("input"))==null?void 0:e.getValue(t);a||(a=w.Zero(),this.getDataInput("input").setValue(a,t)),this.getDataOutput("output_0").setValue(a.x,t),this.getDataOutput("output_1").setValue(a.y,t),this.getDataOutput("output_2").setValue(a.z,t),this.getDataOutput("output_3").setValue(a.w,t)}getClassName(){return"FlowGraphExtractVector4Block"}}p("FlowGraphExtractVector4Block",y);class N extends r{constructor(t){super(16,g,t)}_updateOutputs(t){var e;let a=(e=this.getDataInput("input"))==null?void 0:e.getValue(t);a||(a=d.Identity(),this.getDataInput("input").setValue(a,t));for(let u=0;u<16;u++)this.getDataOutput(`output_${u}`).setValue(a.m[u],t)}getClassName(){return"FlowGraphExtractMatrixBlock"}}p("FlowGraphExtractMatrixBlock",N);class R extends r{constructor(t){super(4,o,t)}_updateOutputs(t){var e;let a=(e=this.getDataInput("input"))==null?void 0:e.getValue(t);a||(a=new V,this.getDataInput("input").setValue(a,t));for(let u=0;u<4;u++)this.getDataOutput(`output_${u}`).setValue(a.m[u],t)}getClassName(){return"FlowGraphExtractMatrix2DBlock"}}p("FlowGraphExtractMatrix2DBlock",R);class T extends r{constructor(t){super(9,c,t)}_updateOutputs(t){var e;let a=(e=this.getDataInput("input"))==null?void 0:e.getValue(t);a||(a=new _,this.getDataInput("input").setValue(a,t));for(let u=0;u<9;u++)this.getDataOutput(`output_${u}`).setValue(a.m[u],t)}getClassName(){return"FlowGraphExtractMatrix3DBlock"}}p("FlowGraphExtractMatrix3DBlock",T);export{E as FlowGraphCombineMatrix2DBlock,C as FlowGraphCombineMatrix3DBlock,M as FlowGraphCombineMatrixBlock,k as FlowGraphCombineVector2Block,B as FlowGraphCombineVector3Block,b as FlowGraphCombineVector4Block,R as FlowGraphExtractMatrix2DBlock,T as FlowGraphExtractMatrix3DBlock,N as FlowGraphExtractMatrixBlock,f as FlowGraphExtractVector2Block,O as FlowGraphExtractVector3Block,y as FlowGraphExtractVector4Block};
//# sourceMappingURL=flowGraphMathCombineExtractBlocks.DpvecDoq.js.map
