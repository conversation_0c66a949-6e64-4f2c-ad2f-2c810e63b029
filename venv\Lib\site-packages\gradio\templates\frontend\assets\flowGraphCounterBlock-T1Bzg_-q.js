import{b as i}from"./declarationMapper-BZjsjg7g.js";import{b as s}from"./KHR_interactivity-DTxiAnOo.js";import{R as o}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class a extends s{constructor(t){super(t),this.count=this.registerDataOutput("count",i),this.reset=this._registerSignalInput("reset")}_execute(t,r){if(r===this.reset){t._setExecutionVariable(this,"count",0),this.count.setValue(0,t);return}const e=t._getExecutionVariable(this,"count",0)+1;t._setExecutionVariable(this,"count",e),this.count.setValue(e,t),this.out._activateSignal(t)}getClassName(){return"FlowGraphCallCounterBlock"}}o("FlowGraphCallCounterBlock",a);export{a as FlowGraphCallCounterBlock};
//# sourceMappingURL=flowGraphCounterBlock-T1Bzg_-q.js.map
