import{SvelteComponent as A,init as K,safe_not_equal as L,element as N,space as O,create_component as B,claim_element as P,children as Q,claim_space as R,claim_component as M,detach as S,attr as w,toggle_class as C,set_style as G,insert_hydration as F,append_hydration as Y,mount_component as E,action_destroyer as Z,transition_in as b,group_outros as y,transition_out as v,check_outros as x,destroy_component as j,createEventDispatcher as p,assign as $,get_spread_update as ee,get_spread_object as te}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{M as ie,F as q,G as ne,I as le,H,J,B as ae,S as se}from"./2.B2AoQPnG.js";import{I as _e}from"./IconButtonWrapper.D5aGR59h.js";import{default as Ce}from"./Example.4x8pwdna.js";function W(i){let e,n;return e=new _e({props:{$$slots:{default:[fe]},$$scope:{ctx:i}}}),{c(){B(e.$$.fragment)},l(t){M(e.$$.fragment,t)},m(t,s){E(e,t,s),n=!0},p(t,s){const f={};s&270336&&(f.$$scope={dirty:s,ctx:t}),e.$set(f)},i(t){n||(b(e.$$.fragment,t),n=!0)},o(t){v(e.$$.fragment,t),n=!1},d(t){j(e,t)}}}function fe(i){let e,n;return e=new le({props:{Icon:i[13]?H:J,label:i[13]?"Copied conversation":"Copy conversation"}}),e.$on("click",i[14]),{c(){B(e.$$.fragment)},l(t){M(e.$$.fragment,t)},m(t,s){E(e,t,s),n=!0},p(t,s){const f={};s&8192&&(f.Icon=t[13]?H:J),s&8192&&(f.label=t[13]?"Copied conversation":"Copy conversation"),e.$set(f)},i(t){n||(b(e.$$.fragment,t),n=!0)},o(t){v(e.$$.fragment,t),n=!1},d(t){j(e,t)}}}function me(i){let e,n,t,s,f,g,r,a,m,o=i[10]&&W(i);return t=new ie({props:{message:i[2],latex_delimiters:i[7],sanitize_html:i[5],line_breaks:i[6],chatbot:!1,header_links:i[8],theme_mode:i[12]}}),{c(){e=N("div"),o&&o.c(),n=O(),B(t.$$.fragment),this.h()},l(l){e=P(l,"DIV",{class:!0,"data-testid":!0,dir:!0,style:!0});var h=Q(e);o&&o.l(h),n=R(h),M(t.$$.fragment,h),h.forEach(S),this.h()},h(){var l,h;w(e,"class",s="prose "+(((l=i[0])==null?void 0:l.join(" "))||"")+" svelte-lag733"),w(e,"data-testid","markdown"),w(e,"dir",f=i[4]?"rtl":"ltr"),w(e,"style",g=i[9]?`max-height: ${q(i[9])}; overflow-y: auto;`:""),C(e,"hide",!i[1]),G(e,"min-height",i[3]&&((h=i[11])==null?void 0:h.status)!=="pending"?q(i[3]):void 0)},m(l,h){F(l,e,h),o&&o.m(e,null),Y(e,n),E(t,e,null),r=!0,a||(m=Z(ne.call(null,e)),a=!0)},p(l,[h]){var k,c;l[10]?o?(o.p(l,h),h&1024&&b(o,1)):(o=W(l),o.c(),b(o,1),o.m(e,n)):o&&(y(),v(o,1,1,()=>{o=null}),x());const d={};h&4&&(d.message=l[2]),h&128&&(d.latex_delimiters=l[7]),h&32&&(d.sanitize_html=l[5]),h&64&&(d.line_breaks=l[6]),h&256&&(d.header_links=l[8]),h&4096&&(d.theme_mode=l[12]),t.$set(d),(!r||h&1&&s!==(s="prose "+(((k=l[0])==null?void 0:k.join(" "))||"")+" svelte-lag733"))&&w(e,"class",s),(!r||h&16&&f!==(f=l[4]?"rtl":"ltr"))&&w(e,"dir",f),(!r||h&512&&g!==(g=l[9]?`max-height: ${q(l[9])}; overflow-y: auto;`:""))&&w(e,"style",g),(!r||h&3)&&C(e,"hide",!l[1]);const z=h&512;(h&2568||z)&&G(e,"min-height",l[3]&&((c=l[11])==null?void 0:c.status)!=="pending"?q(l[3]):void 0)},i(l){r||(b(o),b(t.$$.fragment,l),r=!0)},o(l){v(o),v(t.$$.fragment,l),r=!1},d(l){l&&S(e),o&&o.d(),j(t),a=!1,m()}}}function he(i,e,n){let{elem_classes:t=[]}=e,{visible:s=!0}=e,{value:f}=e,{min_height:g=void 0}=e,{rtl:r=!1}=e,{sanitize_html:a=!0}=e,{line_breaks:m=!1}=e,{latex_delimiters:o}=e,{header_links:l=!1}=e,{height:h=void 0}=e,{show_copy_button:d=!1}=e,{loading_status:z=void 0}=e,{theme_mode:k}=e,c=!1,I;const D=p();async function T(){"clipboard"in navigator&&(await navigator.clipboard.writeText(f),D("copy",{value:f}),V())}function V(){n(13,c=!0),I&&clearTimeout(I),I=setTimeout(()=>{n(13,c=!1)},1e3)}return i.$$set=u=>{"elem_classes"in u&&n(0,t=u.elem_classes),"visible"in u&&n(1,s=u.visible),"value"in u&&n(2,f=u.value),"min_height"in u&&n(3,g=u.min_height),"rtl"in u&&n(4,r=u.rtl),"sanitize_html"in u&&n(5,a=u.sanitize_html),"line_breaks"in u&&n(6,m=u.line_breaks),"latex_delimiters"in u&&n(7,o=u.latex_delimiters),"header_links"in u&&n(8,l=u.header_links),"height"in u&&n(9,h=u.height),"show_copy_button"in u&&n(10,d=u.show_copy_button),"loading_status"in u&&n(11,z=u.loading_status),"theme_mode"in u&&n(12,k=u.theme_mode)},i.$$.update=()=>{i.$$.dirty&4&&D("change")},[t,s,f,g,r,a,m,o,l,h,d,z,k,c,T]}class oe extends A{constructor(e){super(),K(this,e,he,me,L,{elem_classes:0,visible:1,value:2,min_height:3,rtl:4,sanitize_html:5,line_breaks:6,latex_delimiters:7,header_links:8,height:9,show_copy_button:10,loading_status:11,theme_mode:12})}}const ue=oe;function re(i){let e,n,t,s,f;const g=[{autoscroll:i[8].autoscroll},{i18n:i[8].i18n},i[4],{variant:"center"}];let r={};for(let a=0;a<g.length;a+=1)r=$(r,g[a]);return e=new se({props:r}),e.$on("clear_status",i[18]),s=new ue({props:{value:i[3],elem_classes:i[1],visible:i[2],rtl:i[5],latex_delimiters:i[9],sanitize_html:i[6],line_breaks:i[7],header_links:i[10],show_copy_button:i[14],loading_status:i[4],theme_mode:i[16]}}),s.$on("change",i[19]),s.$on("copy",i[20]),{c(){B(e.$$.fragment),n=O(),t=N("div"),B(s.$$.fragment),this.h()},l(a){M(e.$$.fragment,a),n=R(a),t=P(a,"DIV",{class:!0});var m=Q(t);M(s.$$.fragment,m),m.forEach(S),this.h()},h(){var a;w(t,"class","svelte-vuh1yp"),C(t,"padding",i[17]),C(t,"pending",((a=i[4])==null?void 0:a.status)==="pending")},m(a,m){E(e,a,m),F(a,n,m),F(a,t,m),E(s,t,null),f=!0},p(a,m){var h;const o=m&272?ee(g,[m&256&&{autoscroll:a[8].autoscroll},m&256&&{i18n:a[8].i18n},m&16&&te(a[4]),g[3]]):{};e.$set(o);const l={};m&8&&(l.value=a[3]),m&2&&(l.elem_classes=a[1]),m&4&&(l.visible=a[2]),m&32&&(l.rtl=a[5]),m&512&&(l.latex_delimiters=a[9]),m&64&&(l.sanitize_html=a[6]),m&128&&(l.line_breaks=a[7]),m&1024&&(l.header_links=a[10]),m&16384&&(l.show_copy_button=a[14]),m&16&&(l.loading_status=a[4]),m&65536&&(l.theme_mode=a[16]),s.$set(l),(!f||m&131072)&&C(t,"padding",a[17]),(!f||m&16)&&C(t,"pending",((h=a[4])==null?void 0:h.status)==="pending")},i(a){f||(b(e.$$.fragment,a),b(s.$$.fragment,a),f=!0)},o(a){v(e.$$.fragment,a),v(s.$$.fragment,a),f=!1},d(a){a&&(S(n),S(t)),j(e,a),j(s)}}}function ge(i){let e,n;return e=new ae({props:{visible:i[2],elem_id:i[0],elem_classes:i[1],container:i[15],allow_overflow:!0,overflow_behavior:"auto",height:i[11],min_height:i[12],max_height:i[13],$$slots:{default:[re]},$$scope:{ctx:i}}}),{c(){B(e.$$.fragment)},l(t){M(e.$$.fragment,t)},m(t,s){E(e,t,s),n=!0},p(t,[s]){const f={};s&4&&(f.visible=t[2]),s&1&&(f.elem_id=t[0]),s&2&&(f.elem_classes=t[1]),s&32768&&(f.container=t[15]),s&2048&&(f.height=t[11]),s&4096&&(f.min_height=t[12]),s&8192&&(f.max_height=t[13]),s&2312190&&(f.$$scope={dirty:s,ctx:t}),e.$set(f)},i(t){n||(b(e.$$.fragment,t),n=!0)},o(t){v(e.$$.fragment,t),n=!1},d(t){j(e,t)}}}function de(i,e,n){let{elem_id:t=""}=e,{elem_classes:s=[]}=e,{visible:f=!0}=e,{value:g=""}=e,{loading_status:r}=e,{rtl:a=!1}=e,{sanitize_html:m=!0}=e,{line_breaks:o=!1}=e,{gradio:l}=e,{latex_delimiters:h}=e,{header_links:d=!1}=e,{height:z}=e,{min_height:k}=e,{max_height:c}=e,{show_copy_button:I=!1}=e,{container:D=!1}=e,{theme_mode:T}=e,{padding:V=!1}=e;const u=()=>l.dispatch("clear_status",r),U=()=>l.dispatch("change"),X=_=>l.dispatch("copy",_.detail);return i.$$set=_=>{"elem_id"in _&&n(0,t=_.elem_id),"elem_classes"in _&&n(1,s=_.elem_classes),"visible"in _&&n(2,f=_.visible),"value"in _&&n(3,g=_.value),"loading_status"in _&&n(4,r=_.loading_status),"rtl"in _&&n(5,a=_.rtl),"sanitize_html"in _&&n(6,m=_.sanitize_html),"line_breaks"in _&&n(7,o=_.line_breaks),"gradio"in _&&n(8,l=_.gradio),"latex_delimiters"in _&&n(9,h=_.latex_delimiters),"header_links"in _&&n(10,d=_.header_links),"height"in _&&n(11,z=_.height),"min_height"in _&&n(12,k=_.min_height),"max_height"in _&&n(13,c=_.max_height),"show_copy_button"in _&&n(14,I=_.show_copy_button),"container"in _&&n(15,D=_.container),"theme_mode"in _&&n(16,T=_.theme_mode),"padding"in _&&n(17,V=_.padding)},[t,s,f,g,r,a,m,o,l,h,d,z,k,c,I,D,T,V,u,U,X]}class ve extends A{constructor(e){super(),K(this,e,de,ge,L,{elem_id:0,elem_classes:1,visible:2,value:3,loading_status:4,rtl:5,sanitize_html:6,line_breaks:7,gradio:8,latex_delimiters:9,header_links:10,height:11,min_height:12,max_height:13,show_copy_button:14,container:15,theme_mode:16,padding:17})}}export{Ce as BaseExample,ue as BaseMarkdown,ve as default};
//# sourceMappingURL=Index.3nCFs0Nn.js.map
