{"version": 3, "file": "Index-CPGNVvBX.js", "sources": ["../../../../js/gallery/Index.svelte"], "sourcesContent": ["<script context=\"module\" lang=\"ts\">\n\texport { default as BaseGallery } from \"./shared/Gallery.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { GalleryImage, GalleryVideo } from \"./types\";\n\timport type { FileData } from \"@gradio/client\";\n\timport type { Gradio, ShareData, SelectData } from \"@gradio/utils\";\n\timport { Block, UploadText } from \"@gradio/atoms\";\n\timport Gallery from \"./shared/Gallery.svelte\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport { BaseFileUpload } from \"@gradio/file\";\n\n\ttype GalleryData = GalleryImage | GalleryVideo;\n\n\texport let loading_status: LoadingStatus;\n\texport let show_label: boolean;\n\texport let label: string;\n\texport let root: string;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: GalleryData[] | null = null;\n\texport let file_types: string[] | null = [\"image\", \"video\"];\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let columns: number | number[] | undefined = [2];\n\texport let rows: number | number[] | undefined = undefined;\n\texport let height: number | \"auto\" = \"auto\";\n\texport let preview: boolean;\n\texport let allow_preview = true;\n\texport let selected_index: number | null = null;\n\texport let object_fit: \"contain\" | \"cover\" | \"fill\" | \"none\" | \"scale-down\" =\n\t\t\"cover\";\n\texport let show_share_button = false;\n\texport let interactive: boolean;\n\texport let show_download_button = false;\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tupload: typeof value;\n\t\tselect: SelectData;\n\t\tshare: ShareData;\n\t\terror: string;\n\t\tprop_change: Record<string, any>;\n\t\tclear_status: LoadingStatus;\n\t\tpreview_open: never;\n\t\tpreview_close: never;\n\t}>;\n\texport let show_fullscreen_button = true;\n\texport let fullscreen = false;\n\n\tconst dispatch = createEventDispatcher();\n\n\t$: no_value = value === null ? true : value.length === 0;\n\t$: selected_index, dispatch(\"prop_change\", { selected_index });\n\n\tasync function process_upload_files(\n\t\tfiles: FileData[]\n\t): Promise<GalleryData[]> {\n\t\tconst processed_files = await Promise.all(\n\t\t\tfiles.map(async (x) => {\n\t\t\t\tif (x.path?.toLowerCase().endsWith(\".svg\") && x.url) {\n\t\t\t\t\tconst response = await fetch(x.url);\n\t\t\t\t\tconst svgContent = await response.text();\n\t\t\t\t\treturn {\n\t\t\t\t\t\t...x,\n\t\t\t\t\t\turl: `data:image/svg+xml,${encodeURIComponent(svgContent)}`\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\treturn x;\n\t\t\t})\n\t\t);\n\n\t\treturn processed_files.map((x) =>\n\t\t\tx.mime_type?.includes(\"video\")\n\t\t\t\t? { video: x, caption: null }\n\t\t\t\t: { image: x, caption: null }\n\t\t);\n\t}\n</script>\n\n<Block\n\t{visible}\n\tvariant=\"solid\"\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n\theight={typeof height === \"number\" ? height : undefined}\n\tbind:fullscreen\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t{#if interactive && no_value}\n\t\t<BaseFileUpload\n\t\t\tvalue={null}\n\t\t\t{root}\n\t\t\t{label}\n\t\t\tmax_file_size={gradio.max_file_size}\n\t\t\tfile_count={\"multiple\"}\n\t\t\t{file_types}\n\t\t\ti18n={gradio.i18n}\n\t\t\tupload={(...args) => gradio.client.upload(...args)}\n\t\t\tstream_handler={(...args) => gradio.client.stream(...args)}\n\t\t\ton:upload={async (e) => {\n\t\t\t\tconst files = Array.isArray(e.detail) ? e.detail : [e.detail];\n\t\t\t\tvalue = await process_upload_files(files);\n\t\t\t\tgradio.dispatch(\"upload\", value);\n\t\t\t\tgradio.dispatch(\"change\", value);\n\t\t\t}}\n\t\t\ton:error={({ detail }) => {\n\t\t\t\tloading_status = loading_status || {};\n\t\t\t\tloading_status.status = \"error\";\n\t\t\t\tgradio.dispatch(\"error\", detail);\n\t\t\t}}\n\t\t>\n\t\t\t<UploadText i18n={gradio.i18n} type=\"gallery\" />\n\t\t</BaseFileUpload>\n\t{:else}\n\t\t<Gallery\n\t\t\ton:change={() => gradio.dispatch(\"change\", value)}\n\t\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t\ton:share={(e) => gradio.dispatch(\"share\", e.detail)}\n\t\t\ton:error={(e) => gradio.dispatch(\"error\", e.detail)}\n\t\t\ton:preview_open={() => gradio.dispatch(\"preview_open\")}\n\t\t\ton:preview_close={() => gradio.dispatch(\"preview_close\")}\n\t\t\ton:fullscreen={({ detail }) => {\n\t\t\t\tfullscreen = detail;\n\t\t\t}}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{columns}\n\t\t\t{rows}\n\t\t\t{height}\n\t\t\t{preview}\n\t\t\t{object_fit}\n\t\t\t{interactive}\n\t\t\t{allow_preview}\n\t\t\tbind:selected_index\n\t\t\tbind:value\n\t\t\t{show_share_button}\n\t\t\t{show_download_button}\n\t\t\ti18n={gradio.i18n}\n\t\t\t_fetch={(...args) => gradio.client.fetch(...args)}\n\t\t\t{show_fullscreen_button}\n\t\t\t{fullscreen}\n\t\t/>\n\t{/if}\n</Block>\n"], "names": ["ctx", "dirty", "gallery_changes", "basefileupload_changes", "uploadtext_changes", "block_changes", "process_upload_files", "files", "x", "svgContent", "loading_status", "$$props", "show_label", "label", "root", "elem_id", "elem_classes", "visible", "value", "file_types", "container", "scale", "min_width", "columns", "rows", "height", "preview", "allow_preview", "selected_index", "object_fit", "show_share_button", "interactive", "show_download_button", "gradio", "show_fullscreen_button", "fullscreen", "dispatch", "createEventDispatcher", "clear_status_handler", "func", "args", "func_1", "e", "detail", "$$invalidate", "func_2", "change_handler", "no_value"], "mappings": "syDAYuC,EAAA,OAAA,oSA4I9B,KAAAA,MAAO,mzBAAPC,EAAA,CAAA,EAAA,UAAAC,EAAA,KAAAF,MAAO,iWA/CN,0BAGQ,cAAAA,MAAO,yBACV,4BAEN,KAAAA,MAAO,0OAHEC,EAAA,CAAA,EAAA,UAAAE,EAAA,cAAAH,MAAO,+CAGhBC,EAAA,CAAA,EAAA,UAAAE,EAAA,KAAAH,MAAO,yQAeK,KAAAA,MAAO,sFAAPC,EAAA,CAAA,EAAA,UAAAG,EAAA,KAAAJ,MAAO,yIA5Bd,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,0IAGd,OAAAA,OAAeA,EAAQ,EAAA,EAAA,iLALf,WAAAA,MAAO,YACbC,EAAA,CAAA,EAAA,SAAA,CAAA,KAAAD,MAAO,IAAI,aACbA,EAAc,CAAA,CAAA,6WAbV,6FAMO,GACD,OAAA,OAAAA,OAAW,SAAWA,EAAM,EAAA,EAAG,2XAA/BC,EAAA,CAAA,EAAA,QAAAI,EAAA,OAAA,OAAAL,OAAW,SAAWA,EAAM,EAAA,EAAG,uNAnC/BM,GACdC,EAAA,CAgBO,OAduB,MAAA,QAAQ,IACrCA,EAAM,IAAW,MAAAC,GAAA,CACZ,GAAAA,EAAE,MAAM,YAAA,EAAc,SAAS,MAAM,GAAKA,EAAE,IAAA,CAEzC,MAAAC,EAAA,MADiB,MAAA,MAAMD,EAAE,GAAG,GACA,cAE9B,GAAAA,EACH,IAAA,sBAA2B,mBAAmBC,CAAU,CAAA,IAGnD,OAAAD,MAIc,IAAKA,GAC3BA,EAAE,WAAW,SAAS,OAAO,EACxB,CAAA,MAAOA,EAAG,QAAS,IAAA,EACnB,CAAA,MAAOA,EAAG,QAAS,IAAA,CAAA,2BA9Df,CAAA,eAAAE,CAAA,EAAAC,EACA,CAAA,WAAAC,CAAA,EAAAD,EACA,CAAA,MAAAE,CAAA,EAAAF,EACA,CAAA,KAAAG,CAAA,EAAAH,GACA,QAAAI,EAAU,EAAA,EAAAJ,EACV,CAAA,aAAAK,EAAA,EAAA,EAAAL,GACA,QAAAM,EAAU,EAAA,EAAAN,GACV,MAAAO,EAA8B,IAAA,EAAAP,EAC9B,CAAA,WAAAQ,EAAA,CAA+B,QAAS,OAAO,CAAA,EAAAR,GAC/C,UAAAS,EAAY,EAAA,EAAAT,GACZ,MAAAU,EAAuB,IAAA,EAAAV,GACvB,UAAAW,EAAgC,MAAA,EAAAX,EAChC,CAAA,QAAAY,EAAA,CAA0C,CAAC,CAAA,EAAAZ,GAC3C,KAAAa,EAAsC,MAAA,EAAAb,GACtC,OAAAc,EAA0B,MAAA,EAAAd,EAC1B,CAAA,QAAAe,CAAA,EAAAf,GACA,cAAAgB,EAAgB,EAAA,EAAAhB,GAChB,eAAAiB,EAAgC,IAAA,EAAAjB,GAChC,WAAAkB,EACV,OAAA,EAAAlB,GACU,kBAAAmB,EAAoB,EAAA,EAAAnB,EACpB,CAAA,YAAAoB,CAAA,EAAApB,GACA,qBAAAqB,EAAuB,EAAA,EAAArB,EACvB,CAAA,OAAAsB,CAAA,EAAAtB,GAWA,uBAAAuB,EAAyB,EAAA,EAAAvB,GACzB,WAAAwB,EAAa,EAAA,EAAAxB,QAElByB,EAAWC,KA+COC,EAAA,IAAAL,EAAO,SAAS,eAAgBvB,CAAc,EAWxD6B,EAAA,IAAAC,IAASP,EAAO,OAAO,UAAUO,CAAI,EAC7BC,EAAA,IAAAD,IAASP,EAAO,OAAO,UAAUO,CAAI,UACvCE,GAAC,CACZ,MAAAnC,GAAQ,MAAM,QAAQmC,EAAE,MAAM,EAAIA,EAAE,OAAU,CAAAA,EAAE,MAAM,MAC5DxB,EAAK,MAASZ,GAAqBC,EAAK,CAAA,EACxC0B,EAAO,SAAS,SAAUf,CAAK,EAC/Be,EAAO,SAAS,SAAUf,CAAK,OAEnB,OAAAyB,KAAM,CAClBC,EAAA,EAAAlC,EAAiBA,GAAc,CAAA,CAAA,MAC/BA,EAAe,OAAS,QAAOA,CAAA,EAC/BuB,EAAO,SAAS,QAASU,CAAM,GA8BpBE,EAAA,IAAAL,IAASP,EAAO,OAAO,SAASO,CAAI,oDAvB/B,MAAAM,EAAA,IAAAb,EAAO,SAAS,SAAUf,CAAK,IACpCwB,GAAMT,EAAO,SAAS,SAAUS,EAAE,MAAM,IACzCA,GAAMT,EAAO,SAAS,QAASS,EAAE,MAAM,IACvCA,GAAMT,EAAO,SAAS,QAASS,EAAE,MAAM,SAC3BT,EAAO,SAAS,cAAc,SAC7BA,EAAO,SAAS,eAAe,OACrC,OAAAU,KAAM,CACvBC,EAAA,EAAAT,EAAaQ,CAAM,4gCAjFtBC,EAAA,GAAGG,EAAW7B,IAAU,KAAO,GAAOA,EAAM,SAAW,CAAA,mBACpCkB,EAAS,cAAiB,CAAA,eAAAR,CAAA,CAAA"}