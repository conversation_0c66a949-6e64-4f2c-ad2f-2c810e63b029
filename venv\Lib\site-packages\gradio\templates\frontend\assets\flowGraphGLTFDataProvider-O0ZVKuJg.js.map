{"version": 3, "file": "flowGraphGLTFDataProvider-O0ZVKuJg.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_interactivity/flowGraphGLTFDataProvider.js"], "sourcesContent": ["import { FlowGraphBlock } from \"@babylonjs/core/FlowGraph/flowGraphBlock.js\";\nimport { RichTypeAny } from \"@babylonjs/core/FlowGraph/flowGraphRichTypes.js\";\n/**\n * a glTF-based FlowGraph block that provides arrays with babylon object, based on the glTF tree\n * Can be used, for example, to get animation index from a glTF animation\n */\nexport class FlowGraphGLTFDataProvider extends FlowGraphBlock {\n    constructor(config) {\n        super();\n        const glTF = config.glTF;\n        const animationGroups = glTF.animations?.map((a) => a._babylonAnimationGroup) || [];\n        this.animationGroups = this.registerDataOutput(\"animationGroups\", RichTypeAny, animationGroups);\n        const nodes = glTF.nodes?.map((n) => n._babylonTransformNode) || [];\n        this.nodes = this.registerDataOutput(\"nodes\", RichTypeAny, nodes);\n    }\n    getClassName() {\n        return \"FlowGraphGLTFDataProvider\";\n    }\n}\n//# sourceMappingURL=flowGraphGLTFDataProvider.js.map"], "names": ["FlowGraphGLTFDataProvider", "FlowGraphBlock", "config", "glTF", "animationGroups", "a", "RichTypeAny", "nodes", "n"], "mappings": "oOAMO,MAAMA,UAAkCC,CAAe,CAC1D,YAAYC,EAAQ,CAChB,QACA,MAAMC,EAAOD,EAAO,KACdE,EAAkBD,EAAK,YAAY,IAAKE,GAAMA,EAAE,sBAAsB,GAAK,GACjF,KAAK,gBAAkB,KAAK,mBAAmB,kBAAmBC,EAAaF,CAAe,EAC9F,MAAMG,EAAQJ,EAAK,OAAO,IAAKK,GAAMA,EAAE,qBAAqB,GAAK,GACjE,KAAK,MAAQ,KAAK,mBAAmB,QAASF,EAAaC,CAAK,CACnE,CACD,cAAe,CACX,MAAO,2BACV,CACL", "x_google_ignoreList": [0]}