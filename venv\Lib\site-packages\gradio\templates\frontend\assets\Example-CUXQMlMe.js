import{J as _}from"./JSON-D1BmJsmI.js";import"./Check-CEkiXcyC.js";import"./Copy-CxQ9EyK2.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import"./prism-python-MMh3z1bK.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./IconButton-C_HS7fTi.js";import"./Empty-ZqppqzTN.js";import"./IconButtonWrapper--EIOWuEM.js";const{SvelteComponent:h,attr:d,check_outros:g,create_component:p,destroy_component:v,detach:b,element:y,flush:a,group_outros:w,init:k,insert:j,mount_component:J,safe_not_equal:S,toggle_class:r,transition_in:m,transition_out:u}=window.__gradio__svelte__internal;function c(s){let e,n;return e=new _({props:{value:s[0],open:!0,theme_mode:s[1],show_indices:C,label_height:E,interactive:!1,show_copy_button:!1}}),{c(){p(e.$$.fragment)},m(t,l){J(e,t,l),n=!0},p(t,l){const i={};l&1&&(i.value=t[0]),l&2&&(i.theme_mode=t[1]),e.$set(i)},i(t){n||(m(e.$$.fragment,t),n=!0)},o(t){u(e.$$.fragment,t),n=!1},d(t){v(e,t)}}}function q(s){let e,n,t=s[0]&&c(s);return{c(){e=y("div"),t&&t.c(),d(e,"class","container svelte-v7ph9u"),r(e,"table",s[2]==="table"),r(e,"gallery",s[2]==="gallery"),r(e,"selected",s[3]),r(e,"border",s[0])},m(l,i){j(l,e,i),t&&t.m(e,null),n=!0},p(l,[i]){l[0]?t?(t.p(l,i),i&1&&m(t,1)):(t=c(l),t.c(),m(t,1),t.m(e,null)):t&&(w(),u(t,1,1,()=>{t=null}),g()),(!n||i&4)&&r(e,"table",l[2]==="table"),(!n||i&4)&&r(e,"gallery",l[2]==="gallery"),(!n||i&8)&&r(e,"selected",l[3]),(!n||i&1)&&r(e,"border",l[0])},i(l){n||(m(t),n=!0)},o(l){u(t),n=!1},d(l){l&&b(e),t&&t.d()}}}let C=!1,E=0;function N(s,e,n){let{value:t}=e,{theme_mode:l="system"}=e,{type:i}=e,{selected:f=!1}=e;return s.$$set=o=>{"value"in o&&n(0,t=o.value),"theme_mode"in o&&n(1,l=o.theme_mode),"type"in o&&n(2,i=o.type),"selected"in o&&n(3,f=o.selected)},[t,l,i,f]}class M extends h{constructor(e){super(),k(this,e,N,q,S,{value:0,theme_mode:1,type:2,selected:3})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),a()}get theme_mode(){return this.$$.ctx[1]}set theme_mode(e){this.$$set({theme_mode:e}),a()}get type(){return this.$$.ctx[2]}set type(e){this.$$set({type:e}),a()}get selected(){return this.$$.ctx[3]}set selected(e){this.$$set({selected:e}),a()}}export{M as default};
//# sourceMappingURL=Example-CUXQMlMe.js.map
