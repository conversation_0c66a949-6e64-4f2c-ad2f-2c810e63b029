import{F as r,g as i,i as u}from"./KHR_interactivity.DEAVS2UW.js";import{R as a}from"./declarationMapper.UBCwU7BT.js";import{R as h}from"./index.BoI39RQH.js";class p extends r{constructor(s){super(s),this.config=s,this._inputCases=new Map,this.case=this.registerDataInput("case",a,NaN),this.default=this.registerDataInput("default",a),this.value=this.registerDataOutput("value",a),(this.config.cases||[]).forEach(t=>{t=i(t),!(this.config.treatCasesAsIntegers&&(t=t|0,this._inputCases.has(t)))&&this._inputCases.set(t,this.registerDataInput(`in_${t}`,a))})}_updateOutputs(s){const t=this.case.getValue(s);let e;u(t)?e=this._getOutputValueForCase(i(t),s):e=this.default.getValue(s),this.value.setValue(e,s)}_getOutputValueForCase(s,t){var e;return(e=this._inputCases.get(s))==null?void 0:e.getValue(t)}getClassName(){return"FlowGraphDataSwitchBlock"}}h("FlowGraphDataSwitchBlock",p);export{p as FlowGraphDataSwitchBlock};
//# sourceMappingURL=flowGraphDataSwitchBlock.CRZGNJmJ.js.map
