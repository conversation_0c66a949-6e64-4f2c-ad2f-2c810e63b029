import{F as a,h as s}from"./KHR_interactivity-DTxiAnOo.js";import{k as e}from"./declarationMapper-BZjsjg7g.js";import{R as r}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class u extends a{constructor(t){super(t),this.config=t,this.output=this.registerDataOutput("output",e(t.value))}_updateOutputs(t){this.output.setValue(this.config.value,t)}getClassName(){return"FlowGraphConstantBlock"}serialize(t={},o=s){super.serialize(t),o("value",this.config.value,t.config)}}r("FlowGraphConstantBlock",u);export{u as FlowGraphConstantBlock};
//# sourceMappingURL=flowGraphConstantBlock-BoMGDRL7.js.map
