{"version": 3, "file": "MSFT_audio_emitter.BlJQZ28w.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Animations/animationEvent.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Audio/sound.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Audio/weightedsound.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Audio/soundTrack.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Audio/audioSceneComponent.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/MSFT_audio_emitter.js"], "sourcesContent": ["/**\n * Composed of a frame, and an action function\n */\nexport class AnimationEvent {\n    /**\n     * Initializes the animation event\n     * @param frame The frame for which the event is triggered\n     * @param action The event to perform when triggered\n     * @param onlyOnce Specifies if the event should be triggered only once\n     */\n    constructor(\n    /** The frame for which the event is triggered **/\n    frame, \n    /** The event to perform when triggered **/\n    action, \n    /** Specifies if the event should be triggered only once**/\n    onlyOnce) {\n        this.frame = frame;\n        this.action = action;\n        this.onlyOnce = onlyOnce;\n        /**\n         * Specifies if the animation event is done\n         */\n        this.isDone = false;\n    }\n    /** @internal */\n    _clone() {\n        return new AnimationEvent(this.frame, this.action, this.onlyOnce);\n    }\n}\n//# sourceMappingURL=animationEvent.js.map", "import { Tools } from \"../Misc/tools.js\";\nimport { Observable } from \"../Misc/observable.js\";\nimport { Vector3 } from \"../Maths/math.vector.js\";\nimport { Logger } from \"../Misc/logger.js\";\nimport { _WarnImport } from \"../Misc/devTools.js\";\nimport { EngineStore } from \"../Engines/engineStore.js\";\nimport { RegisterClass } from \"../Misc/typeStore.js\";\nimport { AbstractEngine } from \"../Engines/abstractEngine.js\";\nimport { _retryWithInterval } from \"../Misc/timingTools.js\";\n/**\n * Defines a sound that can be played in the application.\n * The sound can either be an ambient track or a simple sound played in reaction to a user action.\n * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\n */\nexport class Sound {\n    /**\n     * Does the sound loop after it finishes playing once.\n     */\n    get loop() {\n        return this._loop;\n    }\n    set loop(value) {\n        if (value === this._loop) {\n            return;\n        }\n        this._loop = value;\n        this.updateOptions({ loop: value });\n    }\n    /**\n     * Gets the current time for the sound.\n     */\n    get currentTime() {\n        if (this._htmlAudioElement) {\n            return this._htmlAudioElement.currentTime;\n        }\n        if (AbstractEngine.audioEngine?.audioContext && (this.isPlaying || this.isPaused)) {\n            // The `_currentTime` member is only updated when the sound is paused. Add the time since the last start\n            // to get the actual current time.\n            const timeSinceLastStart = this.isPaused ? 0 : AbstractEngine.audioEngine.audioContext.currentTime - this._startTime;\n            return this._currentTime + timeSinceLastStart;\n        }\n        return 0;\n    }\n    /**\n     * Does this sound enables spatial sound.\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\n     */\n    get spatialSound() {\n        return this._spatialSound;\n    }\n    /**\n     * Does this sound enables spatial sound.\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\n     */\n    set spatialSound(newValue) {\n        if (newValue == this._spatialSound) {\n            return;\n        }\n        const wasPlaying = this.isPlaying;\n        this.pause();\n        if (newValue) {\n            this._spatialSound = newValue;\n            this._updateSpatialParameters();\n        }\n        else {\n            this._disableSpatialSound();\n        }\n        if (wasPlaying) {\n            this.play();\n        }\n    }\n    /**\n     * Create a sound and attach it to a scene\n     * @param name Name of your sound\n     * @param urlOrArrayBuffer Url to the sound to load async or ArrayBuffer, it also works with MediaStreams and AudioBuffers\n     * @param scene defines the scene the sound belongs to\n     * @param readyToPlayCallback Provide a callback function if you'd like to load your code once the sound is ready to be played\n     * @param options Objects to provide with the current available options: autoplay, loop, volume, spatialSound, maxDistance, rolloffFactor, refDistance, distanceModel, panningModel, streaming\n     */\n    constructor(name, urlOrArrayBuffer, scene, readyToPlayCallback = null, options) {\n        /**\n         * Does the sound autoplay once loaded.\n         */\n        this.autoplay = false;\n        this._loop = false;\n        /**\n         * Does the sound use a custom attenuation curve to simulate the falloff\n         * happening when the source gets further away from the camera.\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-your-own-custom-attenuation-function\n         */\n        this.useCustomAttenuation = false;\n        /**\n         * Is this sound currently played.\n         */\n        this.isPlaying = false;\n        /**\n         * Is this sound currently paused.\n         */\n        this.isPaused = false;\n        /**\n         * Define the reference distance the sound should be heard perfectly.\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\n         */\n        this.refDistance = 1;\n        /**\n         * Define the roll off factor of spatial sounds.\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\n         */\n        this.rolloffFactor = 1;\n        /**\n         * Define the max distance the sound should be heard (intensity just became 0 at this point).\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\n         */\n        this.maxDistance = 100;\n        /**\n         * Define the distance attenuation model the sound will follow.\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\n         */\n        this.distanceModel = \"linear\";\n        /**\n         * Gets or sets an object used to store user defined information for the sound.\n         */\n        this.metadata = null;\n        /**\n         * Observable event when the current playing sound finishes.\n         */\n        this.onEndedObservable = new Observable();\n        this._spatialSound = false;\n        this._panningModel = \"equalpower\";\n        this._playbackRate = 1;\n        this._streaming = false;\n        this._startTime = 0;\n        this._currentTime = 0;\n        this._position = Vector3.Zero();\n        this._localDirection = new Vector3(1, 0, 0);\n        this._volume = 1;\n        this._isReadyToPlay = false;\n        this._isDirectional = false;\n        // Used if you'd like to create a directional sound.\n        // If not set, the sound will be omnidirectional\n        this._coneInnerAngle = 360;\n        this._coneOuterAngle = 360;\n        this._coneOuterGain = 0;\n        this._isOutputConnected = false;\n        this._urlType = \"Unknown\";\n        this.name = name;\n        scene = scene || EngineStore.LastCreatedScene;\n        if (!scene) {\n            return;\n        }\n        this._scene = scene;\n        Sound._SceneComponentInitialization(scene);\n        this._readyToPlayCallback = readyToPlayCallback;\n        // Default custom attenuation function is a linear attenuation\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        this._customAttenuationFunction = (currentVolume, currentDistance, maxDistance, refDistance, rolloffFactor) => {\n            if (currentDistance < maxDistance) {\n                return currentVolume * (1 - currentDistance / maxDistance);\n            }\n            else {\n                return 0;\n            }\n        };\n        if (options) {\n            this.autoplay = options.autoplay || false;\n            this._loop = options.loop || false;\n            // if volume === 0, we need another way to check this option\n            if (options.volume !== undefined) {\n                this._volume = options.volume;\n            }\n            this._spatialSound = options.spatialSound ?? false;\n            this.maxDistance = options.maxDistance ?? 100;\n            this.useCustomAttenuation = options.useCustomAttenuation ?? false;\n            this.rolloffFactor = options.rolloffFactor || 1;\n            this.refDistance = options.refDistance || 1;\n            this.distanceModel = options.distanceModel || \"linear\";\n            this._playbackRate = options.playbackRate || 1;\n            this._streaming = options.streaming ?? false;\n            this._length = options.length;\n            this._offset = options.offset;\n        }\n        if (AbstractEngine.audioEngine?.canUseWebAudio && AbstractEngine.audioEngine.audioContext) {\n            this._soundGain = AbstractEngine.audioEngine.audioContext.createGain();\n            this._soundGain.gain.value = this._volume;\n            this._inputAudioNode = this._soundGain;\n            this._outputAudioNode = this._soundGain;\n            if (this._spatialSound) {\n                this._createSpatialParameters();\n            }\n            this._scene.mainSoundTrack.addSound(this);\n            let validParameter = true;\n            // if no parameter is passed, you need to call setAudioBuffer yourself to prepare the sound\n            if (urlOrArrayBuffer) {\n                try {\n                    if (typeof urlOrArrayBuffer === \"string\") {\n                        this._urlType = \"String\";\n                        this._url = urlOrArrayBuffer;\n                    }\n                    else if (urlOrArrayBuffer instanceof ArrayBuffer) {\n                        this._urlType = \"ArrayBuffer\";\n                    }\n                    else if (urlOrArrayBuffer instanceof HTMLMediaElement) {\n                        this._urlType = \"MediaElement\";\n                    }\n                    else if (urlOrArrayBuffer instanceof MediaStream) {\n                        this._urlType = \"MediaStream\";\n                    }\n                    else if (urlOrArrayBuffer instanceof AudioBuffer) {\n                        this._urlType = \"AudioBuffer\";\n                    }\n                    else if (Array.isArray(urlOrArrayBuffer)) {\n                        this._urlType = \"Array\";\n                    }\n                    let urls = [];\n                    let codecSupportedFound = false;\n                    switch (this._urlType) {\n                        case \"MediaElement\":\n                            this._streaming = true;\n                            this._isReadyToPlay = true;\n                            this._streamingSource = AbstractEngine.audioEngine.audioContext.createMediaElementSource(urlOrArrayBuffer);\n                            if (this.autoplay) {\n                                this.play(0, this._offset, this._length);\n                            }\n                            if (this._readyToPlayCallback) {\n                                this._readyToPlayCallback();\n                            }\n                            break;\n                        case \"MediaStream\":\n                            this._streaming = true;\n                            this._isReadyToPlay = true;\n                            this._streamingSource = AbstractEngine.audioEngine.audioContext.createMediaStreamSource(urlOrArrayBuffer);\n                            if (this.autoplay) {\n                                this.play(0, this._offset, this._length);\n                            }\n                            if (this._readyToPlayCallback) {\n                                this._readyToPlayCallback();\n                            }\n                            break;\n                        case \"ArrayBuffer\":\n                            if (urlOrArrayBuffer.byteLength > 0) {\n                                codecSupportedFound = true;\n                                this._soundLoaded(urlOrArrayBuffer);\n                            }\n                            break;\n                        case \"AudioBuffer\":\n                            this._audioBufferLoaded(urlOrArrayBuffer);\n                            break;\n                        case \"String\":\n                            urls.push(urlOrArrayBuffer);\n                        // eslint-disable-next-line no-fallthrough\n                        case \"Array\":\n                            if (urls.length === 0) {\n                                urls = urlOrArrayBuffer;\n                            }\n                            // If we found a supported format, we load it immediately and stop the loop\n                            for (let i = 0; i < urls.length; i++) {\n                                const url = urls[i];\n                                codecSupportedFound =\n                                    (options && options.skipCodecCheck) ||\n                                        (url.indexOf(\".mp3\", url.length - 4) !== -1 && AbstractEngine.audioEngine.isMP3supported) ||\n                                        (url.indexOf(\".ogg\", url.length - 4) !== -1 && AbstractEngine.audioEngine.isOGGsupported) ||\n                                        url.indexOf(\".wav\", url.length - 4) !== -1 ||\n                                        url.indexOf(\".m4a\", url.length - 4) !== -1 ||\n                                        url.indexOf(\".mp4\", url.length - 4) !== -1 ||\n                                        url.indexOf(\"blob:\") !== -1;\n                                if (codecSupportedFound) {\n                                    // Loading sound\n                                    if (!this._streaming) {\n                                        this._scene._loadFile(url, (data) => {\n                                            this._soundLoaded(data);\n                                        }, undefined, true, true, (exception) => {\n                                            if (exception) {\n                                                Logger.Error(\"XHR \" + exception.status + \" error on: \" + url + \".\");\n                                            }\n                                            Logger.Error(\"Sound creation aborted.\");\n                                            this._scene.mainSoundTrack.removeSound(this);\n                                        });\n                                    }\n                                    // Streaming sound using HTML5 Audio tag\n                                    else {\n                                        this._htmlAudioElement = new Audio(url);\n                                        this._htmlAudioElement.controls = false;\n                                        this._htmlAudioElement.loop = this.loop;\n                                        Tools.SetCorsBehavior(url, this._htmlAudioElement);\n                                        this._htmlAudioElement.preload = \"auto\";\n                                        this._htmlAudioElement.addEventListener(\"canplaythrough\", () => {\n                                            this._isReadyToPlay = true;\n                                            if (this.autoplay) {\n                                                this.play(0, this._offset, this._length);\n                                            }\n                                            if (this._readyToPlayCallback) {\n                                                this._readyToPlayCallback();\n                                            }\n                                        }, { once: true });\n                                        document.body.appendChild(this._htmlAudioElement);\n                                        this._htmlAudioElement.load();\n                                    }\n                                    break;\n                                }\n                            }\n                            break;\n                        default:\n                            validParameter = false;\n                            break;\n                    }\n                    if (!validParameter) {\n                        Logger.Error(\"Parameter must be a URL to the sound, an Array of URLs (.mp3 & .ogg) or an ArrayBuffer of the sound.\");\n                    }\n                    else {\n                        if (!codecSupportedFound) {\n                            this._isReadyToPlay = true;\n                            // Simulating a ready to play event to avoid breaking code path\n                            if (this._readyToPlayCallback) {\n                                setTimeout(() => {\n                                    if (this._readyToPlayCallback) {\n                                        this._readyToPlayCallback();\n                                    }\n                                }, 1000);\n                            }\n                        }\n                    }\n                }\n                catch (ex) {\n                    Logger.Error(\"Unexpected error. Sound creation aborted.\");\n                    this._scene.mainSoundTrack.removeSound(this);\n                }\n            }\n        }\n        else {\n            // Adding an empty sound to avoid breaking audio calls for non Web Audio browsers\n            this._scene.mainSoundTrack.addSound(this);\n            if (AbstractEngine.audioEngine && !AbstractEngine.audioEngine.WarnedWebAudioUnsupported) {\n                Logger.Error(\"Web Audio is not supported by your browser.\");\n                AbstractEngine.audioEngine.WarnedWebAudioUnsupported = true;\n            }\n            // Simulating a ready to play event to avoid breaking code for non web audio browsers\n            if (this._readyToPlayCallback) {\n                setTimeout(() => {\n                    if (this._readyToPlayCallback) {\n                        this._readyToPlayCallback();\n                    }\n                }, 1000);\n            }\n        }\n    }\n    /**\n     * Release the sound and its associated resources\n     */\n    dispose() {\n        if (AbstractEngine.audioEngine?.canUseWebAudio) {\n            if (this.isPlaying) {\n                this.stop();\n            }\n            this._isReadyToPlay = false;\n            if (this.soundTrackId === -1) {\n                this._scene.mainSoundTrack.removeSound(this);\n            }\n            else if (this._scene.soundTracks) {\n                this._scene.soundTracks[this.soundTrackId].removeSound(this);\n            }\n            if (this._soundGain) {\n                this._soundGain.disconnect();\n                this._soundGain = null;\n            }\n            if (this._soundPanner) {\n                this._soundPanner.disconnect();\n                this._soundPanner = null;\n            }\n            if (this._soundSource) {\n                this._soundSource.disconnect();\n                this._soundSource = null;\n            }\n            this._audioBuffer = null;\n            if (this._htmlAudioElement) {\n                this._htmlAudioElement.pause();\n                this._htmlAudioElement.src = \"\";\n                document.body.removeChild(this._htmlAudioElement);\n                this._htmlAudioElement = null;\n            }\n            if (this._streamingSource) {\n                this._streamingSource.disconnect();\n                this._streamingSource = null;\n            }\n            if (this._connectedTransformNode && this._registerFunc) {\n                this._connectedTransformNode.unregisterAfterWorldMatrixUpdate(this._registerFunc);\n                this._connectedTransformNode = null;\n            }\n            this._clearTimeoutsAndObservers();\n        }\n    }\n    /**\n     * Gets if the sounds is ready to be played or not.\n     * @returns true if ready, otherwise false\n     */\n    isReady() {\n        return this._isReadyToPlay;\n    }\n    /**\n     * Get the current class name.\n     * @returns current class name\n     */\n    getClassName() {\n        return \"Sound\";\n    }\n    _audioBufferLoaded(buffer) {\n        if (!AbstractEngine.audioEngine?.audioContext) {\n            return;\n        }\n        this._audioBuffer = buffer;\n        this._isReadyToPlay = true;\n        if (this.autoplay) {\n            this.play(0, this._offset, this._length);\n        }\n        if (this._readyToPlayCallback) {\n            this._readyToPlayCallback();\n        }\n    }\n    _soundLoaded(audioData) {\n        if (!AbstractEngine.audioEngine?.audioContext) {\n            return;\n        }\n        AbstractEngine.audioEngine.audioContext.decodeAudioData(audioData, (buffer) => {\n            this._audioBufferLoaded(buffer);\n        }, (err) => {\n            Logger.Error(\"Error while decoding audio data for: \" + this.name + \" / Error: \" + err);\n        });\n    }\n    /**\n     * Sets the data of the sound from an audiobuffer\n     * @param audioBuffer The audioBuffer containing the data\n     */\n    setAudioBuffer(audioBuffer) {\n        if (AbstractEngine.audioEngine?.canUseWebAudio) {\n            this._audioBuffer = audioBuffer;\n            this._isReadyToPlay = true;\n        }\n    }\n    /**\n     * Updates the current sounds options such as maxdistance, loop...\n     * @param options A JSON object containing values named as the object properties\n     */\n    updateOptions(options) {\n        if (options) {\n            this.loop = options.loop ?? this.loop;\n            this.maxDistance = options.maxDistance ?? this.maxDistance;\n            this.useCustomAttenuation = options.useCustomAttenuation ?? this.useCustomAttenuation;\n            this.rolloffFactor = options.rolloffFactor ?? this.rolloffFactor;\n            this.refDistance = options.refDistance ?? this.refDistance;\n            this.distanceModel = options.distanceModel ?? this.distanceModel;\n            this._playbackRate = options.playbackRate ?? this._playbackRate;\n            this._length = options.length ?? undefined;\n            this.spatialSound = options.spatialSound ?? this._spatialSound;\n            this._setOffset(options.offset ?? undefined);\n            this.setVolume(options.volume ?? this._volume);\n            this._updateSpatialParameters();\n            if (this.isPlaying) {\n                if (this._streaming && this._htmlAudioElement) {\n                    this._htmlAudioElement.playbackRate = this._playbackRate;\n                    if (this._htmlAudioElement.loop !== this.loop) {\n                        this._htmlAudioElement.loop = this.loop;\n                    }\n                }\n                else {\n                    if (this._soundSource) {\n                        this._soundSource.playbackRate.value = this._playbackRate;\n                        if (this._soundSource.loop !== this.loop) {\n                            this._soundSource.loop = this.loop;\n                        }\n                        if (this._offset !== undefined && this._soundSource.loopStart !== this._offset) {\n                            this._soundSource.loopStart = this._offset;\n                        }\n                        if (this._length !== undefined && this._length !== this._soundSource.loopEnd) {\n                            this._soundSource.loopEnd = (this._offset | 0) + this._length;\n                        }\n                    }\n                }\n            }\n        }\n    }\n    _createSpatialParameters() {\n        if (AbstractEngine.audioEngine?.canUseWebAudio && AbstractEngine.audioEngine.audioContext) {\n            if (this._scene.headphone) {\n                this._panningModel = \"HRTF\";\n            }\n            this._soundPanner = this._soundPanner ?? AbstractEngine.audioEngine.audioContext.createPanner();\n            if (this._soundPanner && this._outputAudioNode) {\n                this._updateSpatialParameters();\n                this._soundPanner.connect(this._outputAudioNode);\n                this._inputAudioNode = this._soundPanner;\n            }\n        }\n    }\n    _disableSpatialSound() {\n        if (!this._spatialSound) {\n            return;\n        }\n        this._inputAudioNode = this._soundGain;\n        this._soundPanner?.disconnect();\n        this._soundPanner = null;\n        this._spatialSound = false;\n    }\n    _updateSpatialParameters() {\n        if (!this._spatialSound) {\n            return;\n        }\n        if (this._soundPanner) {\n            if (this.useCustomAttenuation) {\n                // Tricks to disable in a way embedded Web Audio attenuation\n                this._soundPanner.distanceModel = \"linear\";\n                this._soundPanner.maxDistance = Number.MAX_VALUE;\n                this._soundPanner.refDistance = 1;\n                this._soundPanner.rolloffFactor = 1;\n                this._soundPanner.panningModel = this._panningModel;\n            }\n            else {\n                this._soundPanner.distanceModel = this.distanceModel;\n                this._soundPanner.maxDistance = this.maxDistance;\n                this._soundPanner.refDistance = this.refDistance;\n                this._soundPanner.rolloffFactor = this.rolloffFactor;\n                this._soundPanner.panningModel = this._panningModel;\n            }\n        }\n        else {\n            this._createSpatialParameters();\n        }\n    }\n    /**\n     * Switch the panning model to HRTF:\n     * Renders a stereo output of higher quality than equalpower — it uses a convolution with measured impulse responses from human subjects.\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\n     */\n    switchPanningModelToHRTF() {\n        this._panningModel = \"HRTF\";\n        this._switchPanningModel();\n    }\n    /**\n     * Switch the panning model to Equal Power:\n     * Represents the equal-power panning algorithm, generally regarded as simple and efficient. equalpower is the default value.\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\n     */\n    switchPanningModelToEqualPower() {\n        this._panningModel = \"equalpower\";\n        this._switchPanningModel();\n    }\n    _switchPanningModel() {\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._spatialSound && this._soundPanner) {\n            this._soundPanner.panningModel = this._panningModel;\n        }\n    }\n    /**\n     * Connect this sound to a sound track audio node like gain...\n     * @param soundTrackAudioNode the sound track audio node to connect to\n     */\n    connectToSoundTrackAudioNode(soundTrackAudioNode) {\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._outputAudioNode) {\n            if (this._isOutputConnected) {\n                this._outputAudioNode.disconnect();\n            }\n            this._outputAudioNode.connect(soundTrackAudioNode);\n            this._isOutputConnected = true;\n        }\n    }\n    /**\n     * Transform this sound into a directional source\n     * @param coneInnerAngle Size of the inner cone in degree\n     * @param coneOuterAngle Size of the outer cone in degree\n     * @param coneOuterGain Volume of the sound outside the outer cone (between 0.0 and 1.0)\n     */\n    setDirectionalCone(coneInnerAngle, coneOuterAngle, coneOuterGain) {\n        if (coneOuterAngle < coneInnerAngle) {\n            Logger.Error(\"setDirectionalCone(): outer angle of the cone must be superior or equal to the inner angle.\");\n            return;\n        }\n        this._coneInnerAngle = coneInnerAngle;\n        this._coneOuterAngle = coneOuterAngle;\n        this._coneOuterGain = coneOuterGain;\n        this._isDirectional = true;\n        if (this.isPlaying && this.loop) {\n            this.stop();\n            this.play(0, this._offset, this._length);\n        }\n    }\n    /**\n     * Gets or sets the inner angle for the directional cone.\n     */\n    get directionalConeInnerAngle() {\n        return this._coneInnerAngle;\n    }\n    /**\n     * Gets or sets the inner angle for the directional cone.\n     */\n    set directionalConeInnerAngle(value) {\n        if (value != this._coneInnerAngle) {\n            if (this._coneOuterAngle < value) {\n                Logger.Error(\"directionalConeInnerAngle: outer angle of the cone must be superior or equal to the inner angle.\");\n                return;\n            }\n            this._coneInnerAngle = value;\n            if (AbstractEngine.audioEngine?.canUseWebAudio && this._spatialSound && this._soundPanner) {\n                this._soundPanner.coneInnerAngle = this._coneInnerAngle;\n            }\n        }\n    }\n    /**\n     * Gets or sets the outer angle for the directional cone.\n     */\n    get directionalConeOuterAngle() {\n        return this._coneOuterAngle;\n    }\n    /**\n     * Gets or sets the outer angle for the directional cone.\n     */\n    set directionalConeOuterAngle(value) {\n        if (value != this._coneOuterAngle) {\n            if (value < this._coneInnerAngle) {\n                Logger.Error(\"directionalConeOuterAngle: outer angle of the cone must be superior or equal to the inner angle.\");\n                return;\n            }\n            this._coneOuterAngle = value;\n            if (AbstractEngine.audioEngine?.canUseWebAudio && this._spatialSound && this._soundPanner) {\n                this._soundPanner.coneOuterAngle = this._coneOuterAngle;\n            }\n        }\n    }\n    /**\n     * Sets the position of the emitter if spatial sound is enabled\n     * @param newPosition Defines the new position\n     */\n    setPosition(newPosition) {\n        if (newPosition.equals(this._position)) {\n            return;\n        }\n        this._position.copyFrom(newPosition);\n        if (AbstractEngine.audioEngine?.canUseWebAudio &&\n            this._spatialSound &&\n            this._soundPanner &&\n            !isNaN(this._position.x) &&\n            !isNaN(this._position.y) &&\n            !isNaN(this._position.z)) {\n            this._soundPanner.positionX.value = this._position.x;\n            this._soundPanner.positionY.value = this._position.y;\n            this._soundPanner.positionZ.value = this._position.z;\n        }\n    }\n    /**\n     * Sets the local direction of the emitter if spatial sound is enabled\n     * @param newLocalDirection Defines the new local direction\n     */\n    setLocalDirectionToMesh(newLocalDirection) {\n        this._localDirection = newLocalDirection;\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._connectedTransformNode && this.isPlaying) {\n            this._updateDirection();\n        }\n    }\n    _updateDirection() {\n        if (!this._connectedTransformNode || !this._soundPanner) {\n            return;\n        }\n        const mat = this._connectedTransformNode.getWorldMatrix();\n        const direction = Vector3.TransformNormal(this._localDirection, mat);\n        direction.normalize();\n        this._soundPanner.orientationX.value = direction.x;\n        this._soundPanner.orientationY.value = direction.y;\n        this._soundPanner.orientationZ.value = direction.z;\n    }\n    /** @internal */\n    updateDistanceFromListener() {\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._connectedTransformNode && this.useCustomAttenuation && this._soundGain && this._scene.activeCamera) {\n            const distance = this._scene.audioListenerPositionProvider\n                ? this._connectedTransformNode.position.subtract(this._scene.audioListenerPositionProvider()).length()\n                : this._connectedTransformNode.getDistanceToCamera(this._scene.activeCamera);\n            this._soundGain.gain.value = this._customAttenuationFunction(this._volume, distance, this.maxDistance, this.refDistance, this.rolloffFactor);\n        }\n    }\n    /**\n     * Sets a new custom attenuation function for the sound.\n     * @param callback Defines the function used for the attenuation\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-your-own-custom-attenuation-function\n     */\n    setAttenuationFunction(callback) {\n        this._customAttenuationFunction = callback;\n    }\n    /**\n     * Play the sound\n     * @param time (optional) Start the sound after X seconds. Start immediately (0) by default.\n     * @param offset (optional) Start the sound at a specific time in seconds\n     * @param length (optional) Sound duration (in seconds)\n     */\n    play(time, offset, length) {\n        if (this._isReadyToPlay && this._scene.audioEnabled && AbstractEngine.audioEngine?.audioContext) {\n            try {\n                this._clearTimeoutsAndObservers();\n                let startTime = time ? AbstractEngine.audioEngine?.audioContext.currentTime + time : AbstractEngine.audioEngine?.audioContext.currentTime;\n                if (!this._soundSource || !this._streamingSource) {\n                    if (this._spatialSound && this._soundPanner) {\n                        if (!isNaN(this._position.x) && !isNaN(this._position.y) && !isNaN(this._position.z)) {\n                            this._soundPanner.positionX.value = this._position.x;\n                            this._soundPanner.positionY.value = this._position.y;\n                            this._soundPanner.positionZ.value = this._position.z;\n                        }\n                        if (this._isDirectional) {\n                            this._soundPanner.coneInnerAngle = this._coneInnerAngle;\n                            this._soundPanner.coneOuterAngle = this._coneOuterAngle;\n                            this._soundPanner.coneOuterGain = this._coneOuterGain;\n                            if (this._connectedTransformNode) {\n                                this._updateDirection();\n                            }\n                            else {\n                                this._soundPanner.setOrientation(this._localDirection.x, this._localDirection.y, this._localDirection.z);\n                            }\n                        }\n                    }\n                }\n                if (this._streaming) {\n                    if (!this._streamingSource && this._htmlAudioElement) {\n                        this._streamingSource = AbstractEngine.audioEngine.audioContext.createMediaElementSource(this._htmlAudioElement);\n                        this._htmlAudioElement.onended = () => {\n                            this._onended();\n                        };\n                        this._htmlAudioElement.playbackRate = this._playbackRate;\n                    }\n                    if (this._streamingSource) {\n                        this._streamingSource.disconnect();\n                        if (this._inputAudioNode) {\n                            this._streamingSource.connect(this._inputAudioNode);\n                        }\n                    }\n                    if (this._htmlAudioElement) {\n                        // required to manage properly the new suspended default state of Chrome\n                        // When the option 'streaming: true' is used, we need first to wait for\n                        // the audio engine to be unlocked by a user gesture before trying to play\n                        // an HTML Audio element\n                        const tryToPlay = () => {\n                            if (AbstractEngine.audioEngine?.unlocked) {\n                                if (!this._htmlAudioElement) {\n                                    return;\n                                }\n                                this._htmlAudioElement.currentTime = offset ?? 0;\n                                const playPromise = this._htmlAudioElement.play();\n                                // In browsers that don’t yet support this functionality,\n                                // playPromise won’t be defined.\n                                if (playPromise !== undefined) {\n                                    playPromise.catch(() => {\n                                        // Automatic playback failed.\n                                        // Waiting for the audio engine to be unlocked by user click on unmute\n                                        AbstractEngine.audioEngine?.lock();\n                                        if (this.loop || this.autoplay) {\n                                            this._audioUnlockedObserver = AbstractEngine.audioEngine?.onAudioUnlockedObservable.addOnce(() => {\n                                                tryToPlay();\n                                            });\n                                        }\n                                    });\n                                }\n                            }\n                            else {\n                                if (this.loop || this.autoplay) {\n                                    this._audioUnlockedObserver = AbstractEngine.audioEngine?.onAudioUnlockedObservable.addOnce(() => {\n                                        tryToPlay();\n                                    });\n                                }\n                            }\n                        };\n                        tryToPlay();\n                    }\n                }\n                else {\n                    const tryToPlay = () => {\n                        if (AbstractEngine.audioEngine?.audioContext) {\n                            length = length || this._length;\n                            if (offset !== undefined) {\n                                this._setOffset(offset);\n                            }\n                            if (this._soundSource) {\n                                const oldSource = this._soundSource;\n                                oldSource.onended = () => {\n                                    oldSource.disconnect();\n                                };\n                            }\n                            this._soundSource = AbstractEngine.audioEngine?.audioContext.createBufferSource();\n                            if (this._soundSource && this._inputAudioNode) {\n                                this._soundSource.buffer = this._audioBuffer;\n                                this._soundSource.connect(this._inputAudioNode);\n                                this._soundSource.loop = this.loop;\n                                if (offset !== undefined) {\n                                    this._soundSource.loopStart = offset;\n                                }\n                                if (length !== undefined) {\n                                    this._soundSource.loopEnd = (offset | 0) + length;\n                                }\n                                this._soundSource.playbackRate.value = this._playbackRate;\n                                this._soundSource.onended = () => {\n                                    this._onended();\n                                };\n                                startTime = time ? AbstractEngine.audioEngine?.audioContext.currentTime + time : AbstractEngine.audioEngine.audioContext.currentTime;\n                                const actualOffset = ((this.isPaused ? this.currentTime : 0) + (this._offset ?? 0)) % this._soundSource.buffer.duration;\n                                this._soundSource.start(startTime, actualOffset, this.loop ? undefined : length);\n                            }\n                        }\n                    };\n                    if (AbstractEngine.audioEngine?.audioContext.state === \"suspended\") {\n                        // Wait a bit for FF as context seems late to be ready.\n                        this._tryToPlayTimeout = setTimeout(() => {\n                            if (AbstractEngine.audioEngine?.audioContext.state === \"suspended\") {\n                                // Automatic playback failed.\n                                // Waiting for the audio engine to be unlocked by user click on unmute\n                                AbstractEngine.audioEngine.lock();\n                                if (this.loop || this.autoplay) {\n                                    this._audioUnlockedObserver = AbstractEngine.audioEngine.onAudioUnlockedObservable.addOnce(() => {\n                                        tryToPlay();\n                                    });\n                                }\n                            }\n                            else {\n                                tryToPlay();\n                            }\n                        }, 500);\n                    }\n                    else {\n                        tryToPlay();\n                    }\n                }\n                this._startTime = startTime;\n                this.isPlaying = true;\n                this.isPaused = false;\n            }\n            catch (ex) {\n                Logger.Error(\"Error while trying to play audio: \" + this.name + \", \" + ex.message);\n            }\n        }\n    }\n    _onended() {\n        this.isPlaying = false;\n        this._startTime = 0;\n        this._currentTime = 0;\n        if (this.onended) {\n            this.onended();\n        }\n        this.onEndedObservable.notifyObservers(this);\n    }\n    /**\n     * Stop the sound\n     * @param time (optional) Stop the sound after X seconds. Stop immediately (0) by default.\n     */\n    stop(time) {\n        if (this.isPlaying) {\n            this._clearTimeoutsAndObservers();\n            if (this._streaming) {\n                if (this._htmlAudioElement) {\n                    this._htmlAudioElement.pause();\n                    // Test needed for Firefox or it will generate an Invalid State Error\n                    if (this._htmlAudioElement.currentTime > 0) {\n                        this._htmlAudioElement.currentTime = 0;\n                    }\n                }\n                else {\n                    this._streamingSource?.disconnect();\n                }\n                this.isPlaying = false;\n            }\n            else if (AbstractEngine.audioEngine?.audioContext && this._soundSource) {\n                const stopTime = time ? AbstractEngine.audioEngine.audioContext.currentTime + time : undefined;\n                this._soundSource.onended = () => {\n                    this.isPlaying = false;\n                    this.isPaused = false;\n                    this._startTime = 0;\n                    this._currentTime = 0;\n                    if (this._soundSource) {\n                        this._soundSource.onended = () => void 0;\n                    }\n                    this._onended();\n                };\n                this._soundSource.stop(stopTime);\n            }\n            else {\n                this.isPlaying = false;\n            }\n        }\n        else if (this.isPaused) {\n            this.isPaused = false;\n            this._startTime = 0;\n            this._currentTime = 0;\n        }\n    }\n    /**\n     * Put the sound in pause\n     */\n    pause() {\n        if (this.isPlaying) {\n            this._clearTimeoutsAndObservers();\n            if (this._streaming) {\n                if (this._htmlAudioElement) {\n                    this._htmlAudioElement.pause();\n                }\n                else {\n                    this._streamingSource?.disconnect();\n                }\n                this.isPlaying = false;\n                this.isPaused = true;\n            }\n            else if (AbstractEngine.audioEngine?.audioContext && this._soundSource) {\n                this._soundSource.onended = () => void 0;\n                this._soundSource.stop();\n                this.isPlaying = false;\n                this.isPaused = true;\n                this._currentTime += AbstractEngine.audioEngine.audioContext.currentTime - this._startTime;\n            }\n        }\n    }\n    /**\n     * Sets a dedicated volume for this sounds\n     * @param newVolume Define the new volume of the sound\n     * @param time Define time for gradual change to new volume\n     */\n    setVolume(newVolume, time) {\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._soundGain) {\n            if (time && AbstractEngine.audioEngine.audioContext) {\n                this._soundGain.gain.cancelScheduledValues(AbstractEngine.audioEngine.audioContext.currentTime);\n                this._soundGain.gain.setValueAtTime(this._soundGain.gain.value, AbstractEngine.audioEngine.audioContext.currentTime);\n                this._soundGain.gain.linearRampToValueAtTime(newVolume, AbstractEngine.audioEngine.audioContext.currentTime + time);\n            }\n            else {\n                this._soundGain.gain.value = newVolume;\n            }\n        }\n        this._volume = newVolume;\n    }\n    /**\n     * Set the sound play back rate\n     * @param newPlaybackRate Define the playback rate the sound should be played at\n     */\n    setPlaybackRate(newPlaybackRate) {\n        this._playbackRate = newPlaybackRate;\n        if (this.isPlaying) {\n            if (this._streaming && this._htmlAudioElement) {\n                this._htmlAudioElement.playbackRate = this._playbackRate;\n            }\n            else if (this._soundSource) {\n                this._soundSource.playbackRate.value = this._playbackRate;\n            }\n        }\n    }\n    /**\n     * Gets the sound play back rate.\n     * @returns the  play back rate of the sound\n     */\n    getPlaybackRate() {\n        return this._playbackRate;\n    }\n    /**\n     * Gets the volume of the sound.\n     * @returns the volume of the sound\n     */\n    getVolume() {\n        return this._volume;\n    }\n    /**\n     * Attach the sound to a dedicated mesh\n     * @param transformNode The transform node to connect the sound with\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#attaching-a-sound-to-a-mesh\n     */\n    attachToMesh(transformNode) {\n        if (this._connectedTransformNode && this._registerFunc) {\n            this._connectedTransformNode.unregisterAfterWorldMatrixUpdate(this._registerFunc);\n            this._registerFunc = null;\n        }\n        this._connectedTransformNode = transformNode;\n        if (!this._spatialSound) {\n            this._spatialSound = true;\n            this._createSpatialParameters();\n            if (this.isPlaying && this.loop) {\n                this.stop();\n                this.play(0, this._offset, this._length);\n            }\n        }\n        this._onRegisterAfterWorldMatrixUpdate(this._connectedTransformNode);\n        this._registerFunc = (transformNode) => this._onRegisterAfterWorldMatrixUpdate(transformNode);\n        this._connectedTransformNode.registerAfterWorldMatrixUpdate(this._registerFunc);\n    }\n    /**\n     * Detach the sound from the previously attached mesh\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#attaching-a-sound-to-a-mesh\n     */\n    detachFromMesh() {\n        if (this._connectedTransformNode && this._registerFunc) {\n            this._connectedTransformNode.unregisterAfterWorldMatrixUpdate(this._registerFunc);\n            this._registerFunc = null;\n            this._connectedTransformNode = null;\n        }\n    }\n    _onRegisterAfterWorldMatrixUpdate(node) {\n        if (!node.getBoundingInfo) {\n            this.setPosition(node.absolutePosition);\n        }\n        else {\n            const mesh = node;\n            const boundingInfo = mesh.getBoundingInfo();\n            this.setPosition(boundingInfo.boundingSphere.centerWorld);\n        }\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._isDirectional && this.isPlaying) {\n            this._updateDirection();\n        }\n    }\n    /**\n     * Clone the current sound in the scene.\n     * @returns the new sound clone\n     */\n    clone() {\n        if (!this._streaming) {\n            const setBufferAndRun = () => {\n                _retryWithInterval(() => this._isReadyToPlay, () => {\n                    clonedSound._audioBuffer = this.getAudioBuffer();\n                    clonedSound._isReadyToPlay = true;\n                    if (clonedSound.autoplay) {\n                        clonedSound.play(0, this._offset, this._length);\n                    }\n                }, undefined, 300);\n            };\n            const currentOptions = {\n                autoplay: this.autoplay,\n                loop: this.loop,\n                volume: this._volume,\n                spatialSound: this._spatialSound,\n                maxDistance: this.maxDistance,\n                useCustomAttenuation: this.useCustomAttenuation,\n                rolloffFactor: this.rolloffFactor,\n                refDistance: this.refDistance,\n                distanceModel: this.distanceModel,\n            };\n            const clonedSound = new Sound(this.name + \"_cloned\", new ArrayBuffer(0), this._scene, null, currentOptions);\n            if (this.useCustomAttenuation) {\n                clonedSound.setAttenuationFunction(this._customAttenuationFunction);\n            }\n            clonedSound.setPosition(this._position);\n            clonedSound.setPlaybackRate(this._playbackRate);\n            setBufferAndRun();\n            return clonedSound;\n        }\n        // Can't clone a streaming sound\n        else {\n            return null;\n        }\n    }\n    /**\n     * Gets the current underlying audio buffer containing the data\n     * @returns the audio buffer\n     */\n    getAudioBuffer() {\n        return this._audioBuffer;\n    }\n    /**\n     * Gets the WebAudio AudioBufferSourceNode, lets you keep track of and stop instances of this Sound.\n     * @returns the source node\n     */\n    getSoundSource() {\n        return this._soundSource;\n    }\n    /**\n     * Gets the WebAudio GainNode, gives you precise control over the gain of instances of this Sound.\n     * @returns the gain node\n     */\n    getSoundGain() {\n        return this._soundGain;\n    }\n    /**\n     * Serializes the Sound in a JSON representation\n     * @returns the JSON representation of the sound\n     */\n    serialize() {\n        const serializationObject = {\n            name: this.name,\n            url: this._url,\n            autoplay: this.autoplay,\n            loop: this.loop,\n            volume: this._volume,\n            spatialSound: this._spatialSound,\n            maxDistance: this.maxDistance,\n            rolloffFactor: this.rolloffFactor,\n            refDistance: this.refDistance,\n            distanceModel: this.distanceModel,\n            playbackRate: this._playbackRate,\n            panningModel: this._panningModel,\n            soundTrackId: this.soundTrackId,\n            metadata: this.metadata,\n        };\n        if (this._spatialSound) {\n            if (this._connectedTransformNode) {\n                serializationObject.connectedMeshId = this._connectedTransformNode.id;\n            }\n            serializationObject.position = this._position.asArray();\n            serializationObject.refDistance = this.refDistance;\n            serializationObject.distanceModel = this.distanceModel;\n            serializationObject.isDirectional = this._isDirectional;\n            serializationObject.localDirectionToMesh = this._localDirection.asArray();\n            serializationObject.coneInnerAngle = this._coneInnerAngle;\n            serializationObject.coneOuterAngle = this._coneOuterAngle;\n            serializationObject.coneOuterGain = this._coneOuterGain;\n        }\n        return serializationObject;\n    }\n    /**\n     * Parse a JSON representation of a sound to instantiate in a given scene\n     * @param parsedSound Define the JSON representation of the sound (usually coming from the serialize method)\n     * @param scene Define the scene the new parsed sound should be created in\n     * @param rootUrl Define the rooturl of the load in case we need to fetch relative dependencies\n     * @param sourceSound Define a sound place holder if do not need to instantiate a new one\n     * @returns the newly parsed sound\n     */\n    static Parse(parsedSound, scene, rootUrl, sourceSound) {\n        const soundName = parsedSound.name;\n        let soundUrl;\n        if (parsedSound.url) {\n            soundUrl = rootUrl + parsedSound.url;\n        }\n        else {\n            soundUrl = rootUrl + soundName;\n        }\n        const options = {\n            autoplay: parsedSound.autoplay,\n            loop: parsedSound.loop,\n            volume: parsedSound.volume,\n            spatialSound: parsedSound.spatialSound,\n            maxDistance: parsedSound.maxDistance,\n            rolloffFactor: parsedSound.rolloffFactor,\n            refDistance: parsedSound.refDistance,\n            distanceModel: parsedSound.distanceModel,\n            playbackRate: parsedSound.playbackRate,\n        };\n        let newSound;\n        if (!sourceSound) {\n            newSound = new Sound(soundName, soundUrl, scene, () => {\n                scene.removePendingData(newSound);\n            }, options);\n            scene.addPendingData(newSound);\n        }\n        else {\n            const setBufferAndRun = () => {\n                _retryWithInterval(() => sourceSound._isReadyToPlay, () => {\n                    newSound._audioBuffer = sourceSound.getAudioBuffer();\n                    newSound._isReadyToPlay = true;\n                    if (newSound.autoplay) {\n                        newSound.play(0, newSound._offset, newSound._length);\n                    }\n                }, undefined, 300);\n            };\n            newSound = new Sound(soundName, new ArrayBuffer(0), scene, null, options);\n            setBufferAndRun();\n        }\n        if (parsedSound.position) {\n            const soundPosition = Vector3.FromArray(parsedSound.position);\n            newSound.setPosition(soundPosition);\n        }\n        if (parsedSound.isDirectional) {\n            newSound.setDirectionalCone(parsedSound.coneInnerAngle || 360, parsedSound.coneOuterAngle || 360, parsedSound.coneOuterGain || 0);\n            if (parsedSound.localDirectionToMesh) {\n                const localDirectionToMesh = Vector3.FromArray(parsedSound.localDirectionToMesh);\n                newSound.setLocalDirectionToMesh(localDirectionToMesh);\n            }\n        }\n        if (parsedSound.connectedMeshId) {\n            const connectedMesh = scene.getMeshById(parsedSound.connectedMeshId);\n            if (connectedMesh) {\n                newSound.attachToMesh(connectedMesh);\n            }\n        }\n        if (parsedSound.metadata) {\n            newSound.metadata = parsedSound.metadata;\n        }\n        return newSound;\n    }\n    _setOffset(value) {\n        if (this._offset === value) {\n            return;\n        }\n        if (this.isPaused) {\n            this.stop();\n            this.isPaused = false;\n        }\n        this._offset = value;\n    }\n    _clearTimeoutsAndObservers() {\n        if (this._tryToPlayTimeout) {\n            clearTimeout(this._tryToPlayTimeout);\n            this._tryToPlayTimeout = null;\n        }\n        if (this._audioUnlockedObserver) {\n            AbstractEngine.audioEngine?.onAudioUnlockedObservable.remove(this._audioUnlockedObserver);\n            this._audioUnlockedObserver = null;\n        }\n    }\n}\n/**\n * @internal\n */\nSound._SceneComponentInitialization = (_) => {\n    throw _WarnImport(\"AudioSceneComponent\");\n};\n// Register Class Name\nRegisterClass(\"BABYLON.Sound\", Sound);\n//# sourceMappingURL=sound.js.map", "import { Logger } from \"../Misc/logger.js\";\n/**\n * Wraps one or more Sound objects and selects one with random weight for playback.\n */\nexport class WeightedSound {\n    /**\n     * Creates a new WeightedSound from the list of sounds given.\n     * @param loop When true a Sound will be selected and played when the current playing Sound completes.\n     * @param sounds Array of Sounds that will be selected from.\n     * @param weights Array of number values for selection weights; length must equal sounds, values will be normalized to 1\n     */\n    constructor(loop, sounds, weights) {\n        /** When true a Sound will be selected and played when the current playing Sound completes. */\n        this.loop = false;\n        this._coneInnerAngle = 360;\n        this._coneOuterAngle = 360;\n        this._volume = 1;\n        /** A Sound is currently playing. */\n        this.isPlaying = false;\n        /** A Sound is currently paused. */\n        this.isPaused = false;\n        this._sounds = [];\n        this._weights = [];\n        if (sounds.length !== weights.length) {\n            throw new Error(\"Sounds length does not equal weights length\");\n        }\n        this.loop = loop;\n        this._weights = weights;\n        // Normalize the weights\n        let weightSum = 0;\n        for (const weight of weights) {\n            weightSum += weight;\n        }\n        const invWeightSum = weightSum > 0 ? 1 / weightSum : 0;\n        for (let i = 0; i < this._weights.length; i++) {\n            this._weights[i] *= invWeightSum;\n        }\n        this._sounds = sounds;\n        for (const sound of this._sounds) {\n            sound.onEndedObservable.add(() => {\n                this._onended();\n            });\n        }\n    }\n    /**\n     * The size of cone in degrees for a directional sound in which there will be no attenuation.\n     */\n    get directionalConeInnerAngle() {\n        return this._coneInnerAngle;\n    }\n    /**\n     * The size of cone in degrees for a directional sound in which there will be no attenuation.\n     */\n    set directionalConeInnerAngle(value) {\n        if (value !== this._coneInnerAngle) {\n            if (this._coneOuterAngle < value) {\n                Logger.Error(\"directionalConeInnerAngle: outer angle of the cone must be superior or equal to the inner angle.\");\n                return;\n            }\n            this._coneInnerAngle = value;\n            for (const sound of this._sounds) {\n                sound.directionalConeInnerAngle = value;\n            }\n        }\n    }\n    /**\n     * Size of cone in degrees for a directional sound outside of which there will be no sound.\n     * Listener angles between innerAngle and outerAngle will falloff linearly.\n     */\n    get directionalConeOuterAngle() {\n        return this._coneOuterAngle;\n    }\n    /**\n     * Size of cone in degrees for a directional sound outside of which there will be no sound.\n     * Listener angles between innerAngle and outerAngle will falloff linearly.\n     */\n    set directionalConeOuterAngle(value) {\n        if (value !== this._coneOuterAngle) {\n            if (value < this._coneInnerAngle) {\n                Logger.Error(\"directionalConeOuterAngle: outer angle of the cone must be superior or equal to the inner angle.\");\n                return;\n            }\n            this._coneOuterAngle = value;\n            for (const sound of this._sounds) {\n                sound.directionalConeOuterAngle = value;\n            }\n        }\n    }\n    /**\n     * Playback volume.\n     */\n    get volume() {\n        return this._volume;\n    }\n    /**\n     * Playback volume.\n     */\n    set volume(value) {\n        if (value !== this._volume) {\n            for (const sound of this._sounds) {\n                sound.setVolume(value);\n            }\n        }\n    }\n    _onended() {\n        if (this._currentIndex !== undefined) {\n            this._sounds[this._currentIndex].autoplay = false;\n        }\n        if (this.loop && this.isPlaying) {\n            this.play();\n        }\n        else {\n            this.isPlaying = false;\n        }\n    }\n    /**\n     * Suspend playback\n     */\n    pause() {\n        if (this.isPlaying) {\n            this.isPaused = true;\n            if (this._currentIndex !== undefined) {\n                this._sounds[this._currentIndex].pause();\n            }\n        }\n    }\n    /**\n     * Stop playback\n     */\n    stop() {\n        this.isPlaying = false;\n        if (this._currentIndex !== undefined) {\n            this._sounds[this._currentIndex].stop();\n        }\n    }\n    /**\n     * Start playback.\n     * @param startOffset Position the clip head at a specific time in seconds.\n     */\n    play(startOffset) {\n        if (!this.isPaused) {\n            this.stop();\n            const randomValue = Math.random();\n            let total = 0;\n            for (let i = 0; i < this._weights.length; i++) {\n                total += this._weights[i];\n                if (randomValue <= total) {\n                    this._currentIndex = i;\n                    break;\n                }\n            }\n        }\n        const sound = this._sounds[this._currentIndex ?? 0];\n        if (sound.isReady()) {\n            sound.play(0, this.isPaused ? undefined : startOffset);\n        }\n        else {\n            sound.autoplay = true;\n        }\n        this.isPlaying = true;\n        this.isPaused = false;\n    }\n}\n//# sourceMappingURL=weightedsound.js.map", "import { EngineStore } from \"../Engines/engineStore.js\";\nimport { AbstractEngine } from \"../Engines/abstractEngine.js\";\n/**\n * It could be useful to isolate your music & sounds on several tracks to better manage volume on a grouped instance of sounds.\n * It will be also used in a future release to apply effects on a specific track.\n * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#using-sound-tracks\n */\nexport class SoundTrack {\n    /**\n     * Creates a new sound track.\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#using-sound-tracks\n     * @param scene Define the scene the sound track belongs to\n     * @param options\n     */\n    constructor(scene, options = {}) {\n        /**\n         * The unique identifier of the sound track in the scene.\n         */\n        this.id = -1;\n        this._isInitialized = false;\n        scene = scene || EngineStore.LastCreatedScene;\n        if (!scene) {\n            return;\n        }\n        this._scene = scene;\n        this.soundCollection = [];\n        this._options = options;\n        if (!this._options.mainTrack && this._scene.soundTracks) {\n            this._scene.soundTracks.push(this);\n            this.id = this._scene.soundTracks.length - 1;\n        }\n    }\n    _initializeSoundTrackAudioGraph() {\n        if (AbstractEngine.audioEngine?.canUseWebAudio && AbstractEngine.audioEngine.audioContext) {\n            this._outputAudioNode = AbstractEngine.audioEngine.audioContext.createGain();\n            this._outputAudioNode.connect(AbstractEngine.audioEngine.masterGain);\n            if (this._options) {\n                if (this._options.volume) {\n                    this._outputAudioNode.gain.value = this._options.volume;\n                }\n            }\n            this._isInitialized = true;\n        }\n    }\n    /**\n     * Release the sound track and its associated resources\n     */\n    dispose() {\n        if (AbstractEngine.audioEngine && AbstractEngine.audioEngine.canUseWebAudio) {\n            if (this._connectedAnalyser) {\n                this._connectedAnalyser.stopDebugCanvas();\n            }\n            while (this.soundCollection.length) {\n                this.soundCollection[0].dispose();\n            }\n            if (this._outputAudioNode) {\n                this._outputAudioNode.disconnect();\n            }\n            this._outputAudioNode = null;\n        }\n    }\n    /**\n     * Adds a sound to this sound track\n     * @param sound define the sound to add\n     * @ignoreNaming\n     */\n    addSound(sound) {\n        if (!this._isInitialized) {\n            this._initializeSoundTrackAudioGraph();\n        }\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._outputAudioNode) {\n            sound.connectToSoundTrackAudioNode(this._outputAudioNode);\n        }\n        if (sound.soundTrackId !== undefined) {\n            if (sound.soundTrackId === -1) {\n                this._scene.mainSoundTrack.removeSound(sound);\n            }\n            else if (this._scene.soundTracks) {\n                this._scene.soundTracks[sound.soundTrackId].removeSound(sound);\n            }\n        }\n        this.soundCollection.push(sound);\n        sound.soundTrackId = this.id;\n    }\n    /**\n     * Removes a sound to this sound track\n     * @param sound define the sound to remove\n     * @ignoreNaming\n     */\n    removeSound(sound) {\n        const index = this.soundCollection.indexOf(sound);\n        if (index !== -1) {\n            this.soundCollection.splice(index, 1);\n        }\n    }\n    /**\n     * Set a global volume for the full sound track.\n     * @param newVolume Define the new volume of the sound track\n     */\n    setVolume(newVolume) {\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._outputAudioNode) {\n            this._outputAudioNode.gain.value = newVolume;\n        }\n    }\n    /**\n     * Switch the panning model to HRTF:\n     * Renders a stereo output of higher quality than equalpower — it uses a convolution with measured impulse responses from human subjects.\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\n     */\n    switchPanningModelToHRTF() {\n        if (AbstractEngine.audioEngine?.canUseWebAudio) {\n            for (let i = 0; i < this.soundCollection.length; i++) {\n                this.soundCollection[i].switchPanningModelToHRTF();\n            }\n        }\n    }\n    /**\n     * Switch the panning model to Equal Power:\n     * Represents the equal-power panning algorithm, generally regarded as simple and efficient. equalpower is the default value.\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\n     */\n    switchPanningModelToEqualPower() {\n        if (AbstractEngine.audioEngine?.canUseWebAudio) {\n            for (let i = 0; i < this.soundCollection.length; i++) {\n                this.soundCollection[i].switchPanningModelToEqualPower();\n            }\n        }\n    }\n    /**\n     * Connect the sound track to an audio analyser allowing some amazing\n     * synchronization between the sounds/music and your visualization (VuMeter for instance).\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#using-the-analyser\n     * @param analyser The analyser to connect to the engine\n     */\n    connectToAnalyser(analyser) {\n        if (this._connectedAnalyser) {\n            this._connectedAnalyser.stopDebugCanvas();\n        }\n        this._connectedAnalyser = analyser;\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._outputAudioNode) {\n            this._outputAudioNode.disconnect();\n            this._connectedAnalyser.connectAudioNodes(this._outputAudioNode, AbstractEngine.audioEngine.masterGain);\n        }\n    }\n}\n//# sourceMappingURL=soundTrack.js.map", "import { Sound } from \"./sound.js\";\nimport { SoundTrack } from \"./soundTrack.js\";\nimport { Matrix, Vector3 } from \"../Maths/math.vector.js\";\nimport { SceneComponentConstants } from \"../sceneComponent.js\";\nimport { Scene } from \"../scene.js\";\nimport \"./audioEngine.js\";\nimport { PrecisionDate } from \"../Misc/precisionDate.js\";\nimport { EngineStore } from \"../Engines/engineStore.js\";\nimport { AbstractEngine } from \"../Engines/abstractEngine.js\";\nimport { AddParser } from \"../Loading/Plugins/babylonFileParser.function.js\";\n// Adds the parser to the scene parsers.\nAddParser(SceneComponentConstants.NAME_AUDIO, (parsedData, scene, container, rootUrl) => {\n    // TODO: add sound\n    let loadedSounds = [];\n    let loadedSound;\n    container.sounds = container.sounds || [];\n    if (parsedData.sounds !== undefined && parsedData.sounds !== null) {\n        for (let index = 0, cache = parsedData.sounds.length; index < cache; index++) {\n            const parsedSound = parsedData.sounds[index];\n            if (AbstractEngine.audioEngine?.canUseWebAudio) {\n                if (!parsedSound.url) {\n                    parsedSound.url = parsedSound.name;\n                }\n                if (!loadedSounds[parsedSound.url]) {\n                    loadedSound = Sound.Parse(parsedSound, scene, rootUrl);\n                    loadedSounds[parsedSound.url] = loadedSound;\n                    container.sounds.push(loadedSound);\n                }\n                else {\n                    container.sounds.push(Sound.Parse(parsedSound, scene, rootUrl, loadedSounds[parsedSound.url]));\n                }\n            }\n            else {\n                container.sounds.push(new Sound(parsedSound.name, null, scene));\n            }\n        }\n    }\n    loadedSounds = [];\n});\nObject.defineProperty(Scene.prototype, \"mainSoundTrack\", {\n    get: function () {\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO);\n        if (!compo) {\n            compo = new AudioSceneComponent(this);\n            this._addComponent(compo);\n        }\n        if (!this._mainSoundTrack) {\n            this._mainSoundTrack = new SoundTrack(this, { mainTrack: true });\n        }\n        return this._mainSoundTrack;\n    },\n    enumerable: true,\n    configurable: true,\n});\nScene.prototype.getSoundByName = function (name) {\n    let index;\n    for (index = 0; index < this.mainSoundTrack.soundCollection.length; index++) {\n        if (this.mainSoundTrack.soundCollection[index].name === name) {\n            return this.mainSoundTrack.soundCollection[index];\n        }\n    }\n    if (this.soundTracks) {\n        for (let sdIndex = 0; sdIndex < this.soundTracks.length; sdIndex++) {\n            for (index = 0; index < this.soundTracks[sdIndex].soundCollection.length; index++) {\n                if (this.soundTracks[sdIndex].soundCollection[index].name === name) {\n                    return this.soundTracks[sdIndex].soundCollection[index];\n                }\n            }\n        }\n    }\n    return null;\n};\nObject.defineProperty(Scene.prototype, \"audioEnabled\", {\n    get: function () {\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO);\n        if (!compo) {\n            compo = new AudioSceneComponent(this);\n            this._addComponent(compo);\n        }\n        return compo.audioEnabled;\n    },\n    set: function (value) {\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO);\n        if (!compo) {\n            compo = new AudioSceneComponent(this);\n            this._addComponent(compo);\n        }\n        if (value) {\n            compo.enableAudio();\n        }\n        else {\n            compo.disableAudio();\n        }\n    },\n    enumerable: true,\n    configurable: true,\n});\nObject.defineProperty(Scene.prototype, \"headphone\", {\n    get: function () {\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO);\n        if (!compo) {\n            compo = new AudioSceneComponent(this);\n            this._addComponent(compo);\n        }\n        return compo.headphone;\n    },\n    set: function (value) {\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO);\n        if (!compo) {\n            compo = new AudioSceneComponent(this);\n            this._addComponent(compo);\n        }\n        if (value) {\n            compo.switchAudioModeForHeadphones();\n        }\n        else {\n            compo.switchAudioModeForNormalSpeakers();\n        }\n    },\n    enumerable: true,\n    configurable: true,\n});\nObject.defineProperty(Scene.prototype, \"audioListenerPositionProvider\", {\n    get: function () {\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO);\n        if (!compo) {\n            compo = new AudioSceneComponent(this);\n            this._addComponent(compo);\n        }\n        return compo.audioListenerPositionProvider;\n    },\n    set: function (value) {\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO);\n        if (!compo) {\n            compo = new AudioSceneComponent(this);\n            this._addComponent(compo);\n        }\n        if (value && typeof value !== \"function\") {\n            throw new Error(\"The value passed to [Scene.audioListenerPositionProvider] must be a function that returns a Vector3\");\n        }\n        else {\n            compo.audioListenerPositionProvider = value;\n        }\n    },\n    enumerable: true,\n    configurable: true,\n});\nObject.defineProperty(Scene.prototype, \"audioListenerRotationProvider\", {\n    get: function () {\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO);\n        if (!compo) {\n            compo = new AudioSceneComponent(this);\n            this._addComponent(compo);\n        }\n        return compo.audioListenerRotationProvider;\n    },\n    set: function (value) {\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO);\n        if (!compo) {\n            compo = new AudioSceneComponent(this);\n            this._addComponent(compo);\n        }\n        if (value && typeof value !== \"function\") {\n            throw new Error(\"The value passed to [Scene.audioListenerRotationProvider] must be a function that returns a Vector3\");\n        }\n        else {\n            compo.audioListenerRotationProvider = value;\n        }\n    },\n    enumerable: true,\n    configurable: true,\n});\nObject.defineProperty(Scene.prototype, \"audioPositioningRefreshRate\", {\n    get: function () {\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO);\n        if (!compo) {\n            compo = new AudioSceneComponent(this);\n            this._addComponent(compo);\n        }\n        return compo.audioPositioningRefreshRate;\n    },\n    set: function (value) {\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO);\n        if (!compo) {\n            compo = new AudioSceneComponent(this);\n            this._addComponent(compo);\n        }\n        compo.audioPositioningRefreshRate = value;\n    },\n    enumerable: true,\n    configurable: true,\n});\n/**\n * Defines the sound scene component responsible to manage any sounds\n * in a given scene.\n */\nexport class AudioSceneComponent {\n    /**\n     * Gets whether audio is enabled or not.\n     * Please use related enable/disable method to switch state.\n     */\n    get audioEnabled() {\n        return this._audioEnabled;\n    }\n    /**\n     * Gets whether audio is outputting to headphone or not.\n     * Please use the according Switch methods to change output.\n     */\n    get headphone() {\n        return this._headphone;\n    }\n    /**\n     * Creates a new instance of the component for the given scene\n     * @param scene Defines the scene to register the component in\n     */\n    constructor(scene) {\n        /**\n         * The component name helpful to identify the component in the list of scene components.\n         */\n        this.name = SceneComponentConstants.NAME_AUDIO;\n        this._audioEnabled = true;\n        this._headphone = false;\n        /**\n         * Gets or sets a refresh rate when using 3D audio positioning\n         */\n        this.audioPositioningRefreshRate = 500;\n        /**\n         * Gets or Sets a custom listener position for all sounds in the scene\n         * By default, this is the position of the first active camera\n         */\n        this.audioListenerPositionProvider = null;\n        /**\n         * Gets or Sets a custom listener rotation for all sounds in the scene\n         * By default, this is the rotation of the first active camera\n         */\n        this.audioListenerRotationProvider = null;\n        this._cachedCameraDirection = new Vector3();\n        this._cachedCameraPosition = new Vector3();\n        this._lastCheck = 0;\n        this._invertMatrixTemp = new Matrix();\n        this._cameraDirectionTemp = new Vector3();\n        scene = scene || EngineStore.LastCreatedScene;\n        if (!scene) {\n            return;\n        }\n        this.scene = scene;\n        scene.soundTracks = [];\n        scene.sounds = [];\n    }\n    /**\n     * Registers the component in a given scene\n     */\n    register() {\n        this.scene._afterRenderStage.registerStep(SceneComponentConstants.STEP_AFTERRENDER_AUDIO, this, this._afterRender);\n    }\n    /**\n     * Rebuilds the elements related to this component in case of\n     * context lost for instance.\n     */\n    rebuild() {\n        // Nothing to do here. (Not rendering related)\n    }\n    /**\n     * Serializes the component data to the specified json object\n     * @param serializationObject The object to serialize to\n     */\n    serialize(serializationObject) {\n        serializationObject.sounds = [];\n        if (this.scene.soundTracks) {\n            for (let index = 0; index < this.scene.soundTracks.length; index++) {\n                const soundtrack = this.scene.soundTracks[index];\n                for (let soundId = 0; soundId < soundtrack.soundCollection.length; soundId++) {\n                    serializationObject.sounds.push(soundtrack.soundCollection[soundId].serialize());\n                }\n            }\n        }\n    }\n    /**\n     * Adds all the elements from the container to the scene\n     * @param container the container holding the elements\n     */\n    addFromContainer(container) {\n        if (!container.sounds) {\n            return;\n        }\n        container.sounds.forEach((sound) => {\n            sound.play();\n            sound.autoplay = true;\n            this.scene.mainSoundTrack.addSound(sound);\n        });\n    }\n    /**\n     * Removes all the elements in the container from the scene\n     * @param container contains the elements to remove\n     * @param dispose if the removed element should be disposed (default: false)\n     */\n    removeFromContainer(container, dispose = false) {\n        if (!container.sounds) {\n            return;\n        }\n        container.sounds.forEach((sound) => {\n            sound.stop();\n            sound.autoplay = false;\n            this.scene.mainSoundTrack.removeSound(sound);\n            if (dispose) {\n                sound.dispose();\n            }\n        });\n    }\n    /**\n     * Disposes the component and the associated resources.\n     */\n    dispose() {\n        const scene = this.scene;\n        if (scene._mainSoundTrack) {\n            scene.mainSoundTrack.dispose();\n        }\n        if (scene.soundTracks) {\n            for (let scIndex = 0; scIndex < scene.soundTracks.length; scIndex++) {\n                scene.soundTracks[scIndex].dispose();\n            }\n        }\n    }\n    /**\n     * Disables audio in the associated scene.\n     */\n    disableAudio() {\n        const scene = this.scene;\n        this._audioEnabled = false;\n        if (AbstractEngine.audioEngine && AbstractEngine.audioEngine.audioContext) {\n            AbstractEngine.audioEngine.audioContext.suspend();\n        }\n        let i;\n        for (i = 0; i < scene.mainSoundTrack.soundCollection.length; i++) {\n            scene.mainSoundTrack.soundCollection[i].pause();\n        }\n        if (scene.soundTracks) {\n            for (i = 0; i < scene.soundTracks.length; i++) {\n                for (let j = 0; j < scene.soundTracks[i].soundCollection.length; j++) {\n                    scene.soundTracks[i].soundCollection[j].pause();\n                }\n            }\n        }\n    }\n    /**\n     * Enables audio in the associated scene.\n     */\n    enableAudio() {\n        const scene = this.scene;\n        this._audioEnabled = true;\n        if (AbstractEngine.audioEngine && AbstractEngine.audioEngine.audioContext) {\n            AbstractEngine.audioEngine.audioContext.resume();\n        }\n        let i;\n        for (i = 0; i < scene.mainSoundTrack.soundCollection.length; i++) {\n            if (scene.mainSoundTrack.soundCollection[i].isPaused) {\n                scene.mainSoundTrack.soundCollection[i].play();\n            }\n        }\n        if (scene.soundTracks) {\n            for (i = 0; i < scene.soundTracks.length; i++) {\n                for (let j = 0; j < scene.soundTracks[i].soundCollection.length; j++) {\n                    if (scene.soundTracks[i].soundCollection[j].isPaused) {\n                        scene.soundTracks[i].soundCollection[j].play();\n                    }\n                }\n            }\n        }\n    }\n    /**\n     * Switch audio to headphone output.\n     */\n    switchAudioModeForHeadphones() {\n        const scene = this.scene;\n        this._headphone = true;\n        scene.mainSoundTrack.switchPanningModelToHRTF();\n        if (scene.soundTracks) {\n            for (let i = 0; i < scene.soundTracks.length; i++) {\n                scene.soundTracks[i].switchPanningModelToHRTF();\n            }\n        }\n    }\n    /**\n     * Switch audio to normal speakers.\n     */\n    switchAudioModeForNormalSpeakers() {\n        const scene = this.scene;\n        this._headphone = false;\n        scene.mainSoundTrack.switchPanningModelToEqualPower();\n        if (scene.soundTracks) {\n            for (let i = 0; i < scene.soundTracks.length; i++) {\n                scene.soundTracks[i].switchPanningModelToEqualPower();\n            }\n        }\n    }\n    _afterRender() {\n        const now = PrecisionDate.Now;\n        if (this._lastCheck && now - this._lastCheck < this.audioPositioningRefreshRate) {\n            return;\n        }\n        this._lastCheck = now;\n        const scene = this.scene;\n        if (!this._audioEnabled || !scene._mainSoundTrack || !scene.soundTracks || (scene._mainSoundTrack.soundCollection.length === 0 && scene.soundTracks.length === 1)) {\n            return;\n        }\n        const audioEngine = AbstractEngine.audioEngine;\n        if (!audioEngine) {\n            return;\n        }\n        if (audioEngine.audioContext) {\n            let listeningCamera = scene.activeCamera;\n            if (scene.activeCameras && scene.activeCameras.length > 0) {\n                listeningCamera = scene.activeCameras[0];\n            }\n            // A custom listener position provider was set\n            // Use the users provided position instead of camera's\n            if (this.audioListenerPositionProvider) {\n                const position = this.audioListenerPositionProvider();\n                // Set the listener position\n                audioEngine.audioContext.listener.setPosition(position.x || 0, position.y || 0, position.z || 0);\n                // Check if there is a listening camera\n            }\n            else if (listeningCamera) {\n                // Set the listener position to the listening camera global position\n                if (!this._cachedCameraPosition.equals(listeningCamera.globalPosition)) {\n                    this._cachedCameraPosition.copyFrom(listeningCamera.globalPosition);\n                    audioEngine.audioContext.listener.setPosition(listeningCamera.globalPosition.x, listeningCamera.globalPosition.y, listeningCamera.globalPosition.z);\n                }\n            }\n            // Otherwise set the listener position to 0, 0 ,0\n            else {\n                // Set the listener position\n                audioEngine.audioContext.listener.setPosition(0, 0, 0);\n            }\n            // A custom listener rotation provider was set\n            // Use the users provided rotation instead of camera's\n            if (this.audioListenerRotationProvider) {\n                const rotation = this.audioListenerRotationProvider();\n                audioEngine.audioContext.listener.setOrientation(rotation.x || 0, rotation.y || 0, rotation.z || 0, 0, 1, 0);\n                // Check if there is a listening camera\n            }\n            else if (listeningCamera) {\n                // for VR cameras\n                if (listeningCamera.rigCameras && listeningCamera.rigCameras.length > 0) {\n                    listeningCamera = listeningCamera.rigCameras[0];\n                }\n                listeningCamera.getViewMatrix().invertToRef(this._invertMatrixTemp);\n                Vector3.TransformNormalToRef(AudioSceneComponent._CameraDirection, this._invertMatrixTemp, this._cameraDirectionTemp);\n                this._cameraDirectionTemp.normalize();\n                // To avoid some errors on GearVR\n                if (!isNaN(this._cameraDirectionTemp.x) && !isNaN(this._cameraDirectionTemp.y) && !isNaN(this._cameraDirectionTemp.z)) {\n                    if (!this._cachedCameraDirection.equals(this._cameraDirectionTemp)) {\n                        this._cachedCameraDirection.copyFrom(this._cameraDirectionTemp);\n                        audioEngine.audioContext.listener.setOrientation(this._cameraDirectionTemp.x, this._cameraDirectionTemp.y, this._cameraDirectionTemp.z, 0, 1, 0);\n                    }\n                }\n            }\n            // Otherwise set the listener rotation to 0, 0 ,0\n            else {\n                // Set the listener position\n                audioEngine.audioContext.listener.setOrientation(0, 0, 0, 0, 1, 0);\n            }\n            let i;\n            for (i = 0; i < scene.mainSoundTrack.soundCollection.length; i++) {\n                const sound = scene.mainSoundTrack.soundCollection[i];\n                if (sound.useCustomAttenuation) {\n                    sound.updateDistanceFromListener();\n                }\n            }\n            if (scene.soundTracks) {\n                for (i = 0; i < scene.soundTracks.length; i++) {\n                    for (let j = 0; j < scene.soundTracks[i].soundCollection.length; j++) {\n                        const sound = scene.soundTracks[i].soundCollection[j];\n                        if (sound.useCustomAttenuation) {\n                            sound.updateDistanceFromListener();\n                        }\n                    }\n                }\n            }\n        }\n    }\n}\nAudioSceneComponent._CameraDirection = new Vector3(0, 0, -1);\nSound._SceneComponentInitialization = (scene) => {\n    let compo = scene._getComponent(SceneComponentConstants.NAME_AUDIO);\n    if (!compo) {\n        compo = new AudioSceneComponent(scene);\n        scene._addComponent(compo);\n    }\n};\n//# sourceMappingURL=audioSceneComponent.js.map", "import { Vector3 } from \"@babylonjs/core/Maths/math.vector.js\";\nimport { Tools } from \"@babylonjs/core/Misc/tools.js\";\nimport { AnimationEvent } from \"@babylonjs/core/Animations/animationEvent.js\";\nimport { Sound } from \"@babylonjs/core/Audio/sound.js\";\nimport { WeightedSound } from \"@babylonjs/core/Audio/weightedsound.js\";\nimport { GLTFLoader, ArrayItem } from \"../glTFLoader.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nimport \"@babylonjs/core/Audio/audioSceneComponent.js\";\nconst NAME = \"MSFT_audio_emitter\";\n/**\n * [Specification](https://github.com/najadojo/glTF/blob/MSFT_audio_emitter/extensions/2.0/Vendor/MSFT_audio_emitter/README.md)\n * !!! Experimental Extension Subject to Changes !!!\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class MSFT_audio_emitter {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n        this._clips = null;\n        this._emitters = null;\n    }\n    /** @internal */\n    onLoading() {\n        const extensions = this._loader.gltf.extensions;\n        if (extensions && extensions[this.name]) {\n            const extension = extensions[this.name];\n            this._clips = extension.clips;\n            this._emitters = extension.emitters;\n            ArrayItem.Assign(this._clips);\n            ArrayItem.Assign(this._emitters);\n        }\n    }\n    /**\n     * @internal\n     */\n    loadSceneAsync(context, scene) {\n        return GLTFLoader.LoadExtensionAsync(context, scene, this.name, (extensionContext, extension) => {\n            const promises = new Array();\n            promises.push(this._loader.loadSceneAsync(context, scene));\n            for (const emitterIndex of extension.emitters) {\n                const emitter = ArrayItem.Get(`${extensionContext}/emitters`, this._emitters, emitterIndex);\n                if (emitter.refDistance != undefined ||\n                    emitter.maxDistance != undefined ||\n                    emitter.rolloffFactor != undefined ||\n                    emitter.distanceModel != undefined ||\n                    emitter.innerAngle != undefined ||\n                    emitter.outerAngle != undefined) {\n                    throw new Error(`${extensionContext}: Direction or Distance properties are not allowed on emitters attached to a scene`);\n                }\n                promises.push(this._loadEmitterAsync(`${extensionContext}/emitters/${emitter.index}`, emitter));\n            }\n            return Promise.all(promises).then(() => { });\n        });\n    }\n    /**\n     * @internal\n     */\n    loadNodeAsync(context, node, assign) {\n        return GLTFLoader.LoadExtensionAsync(context, node, this.name, (extensionContext, extension) => {\n            const promises = new Array();\n            return this._loader\n                .loadNodeAsync(extensionContext, node, (babylonMesh) => {\n                for (const emitterIndex of extension.emitters) {\n                    const emitter = ArrayItem.Get(`${extensionContext}/emitters`, this._emitters, emitterIndex);\n                    promises.push(this._loadEmitterAsync(`${extensionContext}/emitters/${emitter.index}`, emitter).then(() => {\n                        for (const sound of emitter._babylonSounds) {\n                            sound.attachToMesh(babylonMesh);\n                            if (emitter.innerAngle != undefined || emitter.outerAngle != undefined) {\n                                sound.setLocalDirectionToMesh(Vector3.Forward());\n                                sound.setDirectionalCone(2 * Tools.ToDegrees(emitter.innerAngle == undefined ? Math.PI : emitter.innerAngle), 2 * Tools.ToDegrees(emitter.outerAngle == undefined ? Math.PI : emitter.outerAngle), 0);\n                            }\n                        }\n                    }));\n                }\n                assign(babylonMesh);\n            })\n                .then((babylonMesh) => {\n                return Promise.all(promises).then(() => {\n                    return babylonMesh;\n                });\n            });\n        });\n    }\n    /**\n     * @internal\n     */\n    loadAnimationAsync(context, animation) {\n        return GLTFLoader.LoadExtensionAsync(context, animation, this.name, (extensionContext, extension) => {\n            return this._loader.loadAnimationAsync(context, animation).then((babylonAnimationGroup) => {\n                const promises = new Array();\n                ArrayItem.Assign(extension.events);\n                for (const event of extension.events) {\n                    promises.push(this._loadAnimationEventAsync(`${extensionContext}/events/${event.index}`, context, animation, event, babylonAnimationGroup));\n                }\n                return Promise.all(promises).then(() => {\n                    return babylonAnimationGroup;\n                });\n            });\n        });\n    }\n    _loadClipAsync(context, clip) {\n        if (clip._objectURL) {\n            return clip._objectURL;\n        }\n        let promise;\n        if (clip.uri) {\n            promise = this._loader.loadUriAsync(context, clip, clip.uri);\n        }\n        else {\n            const bufferView = ArrayItem.Get(`${context}/bufferView`, this._loader.gltf.bufferViews, clip.bufferView);\n            promise = this._loader.loadBufferViewAsync(`/bufferViews/${bufferView.index}`, bufferView);\n        }\n        clip._objectURL = promise.then((data) => {\n            return URL.createObjectURL(new Blob([data], { type: clip.mimeType }));\n        });\n        return clip._objectURL;\n    }\n    _loadEmitterAsync(context, emitter) {\n        emitter._babylonSounds = emitter._babylonSounds || [];\n        if (!emitter._babylonData) {\n            const clipPromises = new Array();\n            const name = emitter.name || `emitter${emitter.index}`;\n            const options = {\n                loop: false,\n                autoplay: false,\n                volume: emitter.volume == undefined ? 1 : emitter.volume,\n            };\n            for (let i = 0; i < emitter.clips.length; i++) {\n                const clipContext = `/extensions/${this.name}/clips`;\n                const clip = ArrayItem.Get(clipContext, this._clips, emitter.clips[i].clip);\n                clipPromises.push(this._loadClipAsync(`${clipContext}/${emitter.clips[i].clip}`, clip).then((objectURL) => {\n                    const sound = (emitter._babylonSounds[i] = new Sound(name, objectURL, this._loader.babylonScene, null, options));\n                    sound.refDistance = emitter.refDistance || 1;\n                    sound.maxDistance = emitter.maxDistance || 256;\n                    sound.rolloffFactor = emitter.rolloffFactor || 1;\n                    sound.distanceModel = emitter.distanceModel || \"exponential\";\n                }));\n            }\n            const promise = Promise.all(clipPromises).then(() => {\n                const weights = emitter.clips.map((clip) => {\n                    return clip.weight || 1;\n                });\n                const weightedSound = new WeightedSound(emitter.loop || false, emitter._babylonSounds, weights);\n                if (emitter.innerAngle) {\n                    weightedSound.directionalConeInnerAngle = 2 * Tools.ToDegrees(emitter.innerAngle);\n                }\n                if (emitter.outerAngle) {\n                    weightedSound.directionalConeOuterAngle = 2 * Tools.ToDegrees(emitter.outerAngle);\n                }\n                if (emitter.volume) {\n                    weightedSound.volume = emitter.volume;\n                }\n                emitter._babylonData.sound = weightedSound;\n            });\n            emitter._babylonData = {\n                loaded: promise,\n            };\n        }\n        return emitter._babylonData.loaded;\n    }\n    _getEventAction(context, sound, action, time, startOffset) {\n        switch (action) {\n            case \"play\" /* IMSFTAudioEmitter_AnimationEventAction.play */: {\n                return (currentFrame) => {\n                    const frameOffset = (startOffset || 0) + (currentFrame - time);\n                    sound.play(frameOffset);\n                };\n            }\n            case \"stop\" /* IMSFTAudioEmitter_AnimationEventAction.stop */: {\n                return () => {\n                    sound.stop();\n                };\n            }\n            case \"pause\" /* IMSFTAudioEmitter_AnimationEventAction.pause */: {\n                return () => {\n                    sound.pause();\n                };\n            }\n            default: {\n                throw new Error(`${context}: Unsupported action ${action}`);\n            }\n        }\n    }\n    _loadAnimationEventAsync(context, animationContext, animation, event, babylonAnimationGroup) {\n        if (babylonAnimationGroup.targetedAnimations.length == 0) {\n            return Promise.resolve();\n        }\n        const babylonAnimation = babylonAnimationGroup.targetedAnimations[0];\n        const emitterIndex = event.emitter;\n        const emitter = ArrayItem.Get(`/extensions/${this.name}/emitters`, this._emitters, emitterIndex);\n        return this._loadEmitterAsync(context, emitter).then(() => {\n            const sound = emitter._babylonData.sound;\n            if (sound) {\n                const babylonAnimationEvent = new AnimationEvent(event.time, this._getEventAction(context, sound, event.action, event.time, event.startOffset));\n                babylonAnimation.animation.addEvent(babylonAnimationEvent);\n                // Make sure all started audio stops when this animation is terminated.\n                babylonAnimationGroup.onAnimationGroupEndObservable.add(() => {\n                    sound.stop();\n                });\n                babylonAnimationGroup.onAnimationGroupPauseObservable.add(() => {\n                    sound.pause();\n                });\n            }\n        });\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new MSFT_audio_emitter(loader));\n//# sourceMappingURL=MSFT_audio_emitter.js.map"], "names": ["AnimationEvent", "frame", "action", "onlyOnce", "Sound", "value", "_a", "AbstractEngine", "timeSinceLastStart", "newValue", "wasPlaying", "name", "url<PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON>er", "scene", "readyToPlayCallback", "options", "Observable", "Vector3", "EngineStore", "currentVolume", "currentDistance", "maxDistance", "refDistance", "rolloffFactor", "validParameter", "urls", "codecSupportedFound", "i", "url", "Tools", "data", "exception", "<PERSON><PERSON>", "buffer", "audioData", "err", "audioBuffer", "soundTrackAudioNode", "coneInnerAngle", "coneOuterAngle", "coneOuterGain", "newPosition", "newLocalDirection", "mat", "direction", "distance", "callback", "time", "offset", "length", "startTime", "_b", "_c", "tryToPlay", "playPromise", "oldSource", "actualOffset", "_d", "ex", "stopTime", "newVolume", "newPlaybackRate", "transformNode", "node", "boundingInfo", "setBufferAndRun", "_retryWithInterval", "clonedSound", "currentOptions", "serializationObject", "parsedSound", "rootUrl", "sourceSound", "soundName", "soundUrl", "newSound", "soundPosition", "localDirectionToMesh", "<PERSON><PERSON>esh", "_", "_WarnImport", "RegisterClass", "WeightedSound", "loop", "sounds", "weights", "weightSum", "weight", "invWeightSum", "sound", "startOffset", "randomValue", "total", "SoundTrack", "index", "analyser", "Scene", "compo", "SceneComponentConstants", "AudioSceneComponent", "sdIndex", "Matrix", "soundtrack", "soundId", "container", "dispose", "scIndex", "j", "now", "PrecisionDate", "audioEngine", "listeningCamera", "position", "rotation", "NAME", "MSFT_audio_emitter", "loader", "extensions", "extension", "ArrayItem", "context", "GLTFLoader", "extensionContext", "promises", "emitterIndex", "emitter", "assign", "<PERSON><PERSON><PERSON><PERSON>", "animation", "babylonAnimationGroup", "event", "clip", "promise", "bufferView", "clipPromises", "clipContext", "objectURL", "weightedSound", "currentFrame", "frameOffset", "animationContext", "babylonAnimation", "babylonAnimationEvent", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "mPAGO,MAAMA,CAAe,CAOxB,YAEAC,EAEAC,EAEAC,EAAU,CACN,KAAK,MAAQF,EACb,KAAK,OAASC,EACd,KAAK,SAAWC,EAIhB,KAAK,OAAS,EACjB,CAED,QAAS,CACL,OAAO,IAAIH,EAAe,KAAK,MAAO,KAAK,OAAQ,KAAK,QAAQ,CACnE,CACL,CCfO,MAAMI,CAAM,CAIf,IAAI,MAAO,CACP,OAAO,KAAK,KACf,CACD,IAAI,KAAKC,EAAO,CACRA,IAAU,KAAK,QAGnB,KAAK,MAAQA,EACb,KAAK,cAAc,CAAE,KAAMA,CAAO,CAAA,EACrC,CAID,IAAI,aAAc,OACd,GAAI,KAAK,kBACL,OAAO,KAAK,kBAAkB,YAElC,IAAIC,EAAAC,EAAe,cAAf,MAAAD,EAA4B,eAAiB,KAAK,WAAa,KAAK,UAAW,CAG/E,MAAME,EAAqB,KAAK,SAAW,EAAID,EAAe,YAAY,aAAa,YAAc,KAAK,WAC1G,OAAO,KAAK,aAAeC,CAC9B,CACD,MAAO,EACV,CAKD,IAAI,cAAe,CACf,OAAO,KAAK,aACf,CAKD,IAAI,aAAaC,EAAU,CACvB,GAAIA,GAAY,KAAK,cACjB,OAEJ,MAAMC,EAAa,KAAK,UACxB,KAAK,MAAK,EACND,GACA,KAAK,cAAgBA,EACrB,KAAK,yBAAwB,GAG7B,KAAK,qBAAoB,EAEzBC,GACA,KAAK,KAAI,CAEhB,CASD,YAAYC,EAAMC,EAAkBC,EAAOC,EAAsB,KAAMC,EAAS,OAoE5E,GAhEA,KAAK,SAAW,GAChB,KAAK,MAAQ,GAMb,KAAK,qBAAuB,GAI5B,KAAK,UAAY,GAIjB,KAAK,SAAW,GAKhB,KAAK,YAAc,EAKnB,KAAK,cAAgB,EAKrB,KAAK,YAAc,IAKnB,KAAK,cAAgB,SAIrB,KAAK,SAAW,KAIhB,KAAK,kBAAoB,IAAIC,EAC7B,KAAK,cAAgB,GACrB,KAAK,cAAgB,aACrB,KAAK,cAAgB,EACrB,KAAK,WAAa,GAClB,KAAK,WAAa,EAClB,KAAK,aAAe,EACpB,KAAK,UAAYC,EAAQ,OACzB,KAAK,gBAAkB,IAAIA,EAAQ,EAAG,EAAG,CAAC,EAC1C,KAAK,QAAU,EACf,KAAK,eAAiB,GACtB,KAAK,eAAiB,GAGtB,KAAK,gBAAkB,IACvB,KAAK,gBAAkB,IACvB,KAAK,eAAiB,EACtB,KAAK,mBAAqB,GAC1B,KAAK,SAAW,UAChB,KAAK,KAAON,EACZE,EAAQA,GAASK,EAAY,iBACzB,EAACL,EAkCL,GA/BA,KAAK,OAASA,EACdT,EAAM,8BAA8BS,CAAK,EACzC,KAAK,qBAAuBC,EAG5B,KAAK,2BAA6B,CAACK,EAAeC,EAAiBC,EAAaC,EAAaC,IACrFH,EAAkBC,EACXF,GAAiB,EAAIC,EAAkBC,GAGvC,EAGXN,IACA,KAAK,SAAWA,EAAQ,UAAY,GACpC,KAAK,MAAQA,EAAQ,MAAQ,GAEzBA,EAAQ,SAAW,SACnB,KAAK,QAAUA,EAAQ,QAE3B,KAAK,cAAgBA,EAAQ,cAAgB,GAC7C,KAAK,YAAcA,EAAQ,aAAe,IAC1C,KAAK,qBAAuBA,EAAQ,sBAAwB,GAC5D,KAAK,cAAgBA,EAAQ,eAAiB,EAC9C,KAAK,YAAcA,EAAQ,aAAe,EAC1C,KAAK,cAAgBA,EAAQ,eAAiB,SAC9C,KAAK,cAAgBA,EAAQ,cAAgB,EAC7C,KAAK,WAAaA,EAAQ,WAAa,GACvC,KAAK,QAAUA,EAAQ,OACvB,KAAK,QAAUA,EAAQ,SAEvBT,EAAAC,EAAe,cAAf,MAAAD,EAA4B,gBAAkBC,EAAe,YAAY,aAAc,CACvF,KAAK,WAAaA,EAAe,YAAY,aAAa,WAAU,EACpE,KAAK,WAAW,KAAK,MAAQ,KAAK,QAClC,KAAK,gBAAkB,KAAK,WAC5B,KAAK,iBAAmB,KAAK,WACzB,KAAK,eACL,KAAK,yBAAwB,EAEjC,KAAK,OAAO,eAAe,SAAS,IAAI,EACxC,IAAIiB,EAAiB,GAErB,GAAIZ,EACA,GAAI,CACI,OAAOA,GAAqB,UAC5B,KAAK,SAAW,SAChB,KAAK,KAAOA,GAEPA,aAA4B,YACjC,KAAK,SAAW,cAEXA,aAA4B,iBACjC,KAAK,SAAW,eAEXA,aAA4B,YACjC,KAAK,SAAW,cAEXA,aAA4B,YACjC,KAAK,SAAW,cAEX,MAAM,QAAQA,CAAgB,IACnC,KAAK,SAAW,SAEpB,IAAIa,EAAO,CAAA,EACPC,EAAsB,GAC1B,OAAQ,KAAK,SAAQ,CACjB,IAAK,eACD,KAAK,WAAa,GAClB,KAAK,eAAiB,GACtB,KAAK,iBAAmBnB,EAAe,YAAY,aAAa,yBAAyBK,CAAgB,EACrG,KAAK,UACL,KAAK,KAAK,EAAG,KAAK,QAAS,KAAK,OAAO,EAEvC,KAAK,sBACL,KAAK,qBAAoB,EAE7B,MACJ,IAAK,cACD,KAAK,WAAa,GAClB,KAAK,eAAiB,GACtB,KAAK,iBAAmBL,EAAe,YAAY,aAAa,wBAAwBK,CAAgB,EACpG,KAAK,UACL,KAAK,KAAK,EAAG,KAAK,QAAS,KAAK,OAAO,EAEvC,KAAK,sBACL,KAAK,qBAAoB,EAE7B,MACJ,IAAK,cACGA,EAAiB,WAAa,IAC9Bc,EAAsB,GACtB,KAAK,aAAad,CAAgB,GAEtC,MACJ,IAAK,cACD,KAAK,mBAAmBA,CAAgB,EACxC,MACJ,IAAK,SACDa,EAAK,KAAKb,CAAgB,EAE9B,IAAK,QACGa,EAAK,SAAW,IAChBA,EAAOb,GAGX,QAASe,EAAI,EAAGA,EAAIF,EAAK,OAAQE,IAAK,CAClC,MAAMC,EAAMH,EAAKE,CAAC,EASlB,GARAD,EACKX,GAAWA,EAAQ,gBACfa,EAAI,QAAQ,OAAQA,EAAI,OAAS,CAAC,IAAM,IAAMrB,EAAe,YAAY,gBACzEqB,EAAI,QAAQ,OAAQA,EAAI,OAAS,CAAC,IAAM,IAAMrB,EAAe,YAAY,gBAC1EqB,EAAI,QAAQ,OAAQA,EAAI,OAAS,CAAC,IAAM,IACxCA,EAAI,QAAQ,OAAQA,EAAI,OAAS,CAAC,IAAM,IACxCA,EAAI,QAAQ,OAAQA,EAAI,OAAS,CAAC,IAAM,IACxCA,EAAI,QAAQ,OAAO,IAAM,GAC7BF,EAAqB,CAEhB,KAAK,YAaN,KAAK,kBAAoB,IAAI,MAAME,CAAG,EACtC,KAAK,kBAAkB,SAAW,GAClC,KAAK,kBAAkB,KAAO,KAAK,KACnCC,EAAM,gBAAgBD,EAAK,KAAK,iBAAiB,EACjD,KAAK,kBAAkB,QAAU,OACjC,KAAK,kBAAkB,iBAAiB,iBAAkB,IAAM,CAC5D,KAAK,eAAiB,GAClB,KAAK,UACL,KAAK,KAAK,EAAG,KAAK,QAAS,KAAK,OAAO,EAEvC,KAAK,sBACL,KAAK,qBAAoB,CAEzE,EAA2C,CAAE,KAAM,EAAI,CAAE,EACjB,SAAS,KAAK,YAAY,KAAK,iBAAiB,EAChD,KAAK,kBAAkB,QA3BvB,KAAK,OAAO,UAAUA,EAAME,GAAS,CACjC,KAAK,aAAaA,CAAI,CACzB,EAAE,OAAW,GAAM,GAAOC,GAAc,CACjCA,GACAC,EAAO,MAAM,OAASD,EAAU,OAAS,cAAgBH,EAAM,GAAG,EAEtEI,EAAO,MAAM,yBAAyB,EACtC,KAAK,OAAO,eAAe,YAAY,IAAI,CACvF,CAAyC,EAqBL,KACH,CACJ,CACD,MACJ,QACIR,EAAiB,GACjB,KACP,CACIA,EAIIE,IACD,KAAK,eAAiB,GAElB,KAAK,sBACL,WAAW,IAAM,CACT,KAAK,sBACL,KAAK,qBAAoB,CAEhC,EAAE,GAAI,GAXfM,EAAO,MAAM,sGAAsG,CAe1H,MACU,CACPA,EAAO,MAAM,2CAA2C,EACxD,KAAK,OAAO,eAAe,YAAY,IAAI,CAC9C,CAER,MAGG,KAAK,OAAO,eAAe,SAAS,IAAI,EACpCzB,EAAe,aAAe,CAACA,EAAe,YAAY,4BAC1DyB,EAAO,MAAM,6CAA6C,EAC1DzB,EAAe,YAAY,0BAA4B,IAGvD,KAAK,sBACL,WAAW,IAAM,CACT,KAAK,sBACL,KAAK,qBAAoB,CAEhC,EAAE,GAAI,CAGlB,CAID,SAAU,QACFD,EAAAC,EAAe,cAAf,MAAAD,EAA4B,iBACxB,KAAK,WACL,KAAK,KAAI,EAEb,KAAK,eAAiB,GAClB,KAAK,eAAiB,GACtB,KAAK,OAAO,eAAe,YAAY,IAAI,EAEtC,KAAK,OAAO,aACjB,KAAK,OAAO,YAAY,KAAK,YAAY,EAAE,YAAY,IAAI,EAE3D,KAAK,aACL,KAAK,WAAW,aAChB,KAAK,WAAa,MAElB,KAAK,eACL,KAAK,aAAa,aAClB,KAAK,aAAe,MAEpB,KAAK,eACL,KAAK,aAAa,aAClB,KAAK,aAAe,MAExB,KAAK,aAAe,KAChB,KAAK,oBACL,KAAK,kBAAkB,QACvB,KAAK,kBAAkB,IAAM,GAC7B,SAAS,KAAK,YAAY,KAAK,iBAAiB,EAChD,KAAK,kBAAoB,MAEzB,KAAK,mBACL,KAAK,iBAAiB,aACtB,KAAK,iBAAmB,MAExB,KAAK,yBAA2B,KAAK,gBACrC,KAAK,wBAAwB,iCAAiC,KAAK,aAAa,EAChF,KAAK,wBAA0B,MAEnC,KAAK,2BAA0B,EAEtC,CAKD,SAAU,CACN,OAAO,KAAK,cACf,CAKD,cAAe,CACX,MAAO,OACV,CACD,mBAAmB2B,EAAQ,QAClB3B,EAAAC,EAAe,cAAf,MAAAD,EAA4B,eAGjC,KAAK,aAAe2B,EACpB,KAAK,eAAiB,GAClB,KAAK,UACL,KAAK,KAAK,EAAG,KAAK,QAAS,KAAK,OAAO,EAEvC,KAAK,sBACL,KAAK,qBAAoB,EAEhC,CACD,aAAaC,EAAW,QACf5B,EAAAC,EAAe,cAAf,MAAAD,EAA4B,cAGjCC,EAAe,YAAY,aAAa,gBAAgB2B,EAAYD,GAAW,CAC3E,KAAK,mBAAmBA,CAAM,CACjC,EAAGE,GAAQ,CACRH,EAAO,MAAM,wCAA0C,KAAK,KAAO,aAAeG,CAAG,CACjG,CAAS,CACJ,CAKD,eAAeC,EAAa,QACpB9B,EAAAC,EAAe,cAAf,MAAAD,EAA4B,iBAC5B,KAAK,aAAe8B,EACpB,KAAK,eAAiB,GAE7B,CAKD,cAAcrB,EAAS,CACfA,IACA,KAAK,KAAOA,EAAQ,MAAQ,KAAK,KACjC,KAAK,YAAcA,EAAQ,aAAe,KAAK,YAC/C,KAAK,qBAAuBA,EAAQ,sBAAwB,KAAK,qBACjE,KAAK,cAAgBA,EAAQ,eAAiB,KAAK,cACnD,KAAK,YAAcA,EAAQ,aAAe,KAAK,YAC/C,KAAK,cAAgBA,EAAQ,eAAiB,KAAK,cACnD,KAAK,cAAgBA,EAAQ,cAAgB,KAAK,cAClD,KAAK,QAAUA,EAAQ,QAAU,OACjC,KAAK,aAAeA,EAAQ,cAAgB,KAAK,cACjD,KAAK,WAAWA,EAAQ,QAAU,MAAS,EAC3C,KAAK,UAAUA,EAAQ,QAAU,KAAK,OAAO,EAC7C,KAAK,yBAAwB,EACzB,KAAK,YACD,KAAK,YAAc,KAAK,mBACxB,KAAK,kBAAkB,aAAe,KAAK,cACvC,KAAK,kBAAkB,OAAS,KAAK,OACrC,KAAK,kBAAkB,KAAO,KAAK,OAInC,KAAK,eACL,KAAK,aAAa,aAAa,MAAQ,KAAK,cACxC,KAAK,aAAa,OAAS,KAAK,OAChC,KAAK,aAAa,KAAO,KAAK,MAE9B,KAAK,UAAY,QAAa,KAAK,aAAa,YAAc,KAAK,UACnE,KAAK,aAAa,UAAY,KAAK,SAEnC,KAAK,UAAY,QAAa,KAAK,UAAY,KAAK,aAAa,UACjE,KAAK,aAAa,SAAW,KAAK,QAAU,GAAK,KAAK,WAM7E,CACD,0BAA2B,QACnBT,EAAAC,EAAe,cAAf,MAAAD,EAA4B,gBAAkBC,EAAe,YAAY,eACrE,KAAK,OAAO,YACZ,KAAK,cAAgB,QAEzB,KAAK,aAAe,KAAK,cAAgBA,EAAe,YAAY,aAAa,eAC7E,KAAK,cAAgB,KAAK,mBAC1B,KAAK,yBAAwB,EAC7B,KAAK,aAAa,QAAQ,KAAK,gBAAgB,EAC/C,KAAK,gBAAkB,KAAK,cAGvC,CACD,sBAAuB,OACd,KAAK,gBAGV,KAAK,gBAAkB,KAAK,YAC5BD,EAAA,KAAK,eAAL,MAAAA,EAAmB,aACnB,KAAK,aAAe,KACpB,KAAK,cAAgB,GACxB,CACD,0BAA2B,CAClB,KAAK,gBAGN,KAAK,aACD,KAAK,sBAEL,KAAK,aAAa,cAAgB,SAClC,KAAK,aAAa,YAAc,OAAO,UACvC,KAAK,aAAa,YAAc,EAChC,KAAK,aAAa,cAAgB,EAClC,KAAK,aAAa,aAAe,KAAK,gBAGtC,KAAK,aAAa,cAAgB,KAAK,cACvC,KAAK,aAAa,YAAc,KAAK,YACrC,KAAK,aAAa,YAAc,KAAK,YACrC,KAAK,aAAa,cAAgB,KAAK,cACvC,KAAK,aAAa,aAAe,KAAK,eAI1C,KAAK,yBAAwB,EAEpC,CAMD,0BAA2B,CACvB,KAAK,cAAgB,OACrB,KAAK,oBAAmB,CAC3B,CAMD,gCAAiC,CAC7B,KAAK,cAAgB,aACrB,KAAK,oBAAmB,CAC3B,CACD,qBAAsB,QACdA,EAAAC,EAAe,cAAf,MAAAD,EAA4B,gBAAkB,KAAK,eAAiB,KAAK,eACzE,KAAK,aAAa,aAAe,KAAK,cAE7C,CAKD,6BAA6B+B,EAAqB,QAC1C/B,EAAAC,EAAe,cAAf,MAAAD,EAA4B,gBAAkB,KAAK,mBAC/C,KAAK,oBACL,KAAK,iBAAiB,aAE1B,KAAK,iBAAiB,QAAQ+B,CAAmB,EACjD,KAAK,mBAAqB,GAEjC,CAOD,mBAAmBC,EAAgBC,EAAgBC,EAAe,CAC9D,GAAID,EAAiBD,EAAgB,CACjCN,EAAO,MAAM,6FAA6F,EAC1G,MACH,CACD,KAAK,gBAAkBM,EACvB,KAAK,gBAAkBC,EACvB,KAAK,eAAiBC,EACtB,KAAK,eAAiB,GAClB,KAAK,WAAa,KAAK,OACvB,KAAK,KAAI,EACT,KAAK,KAAK,EAAG,KAAK,QAAS,KAAK,OAAO,EAE9C,CAID,IAAI,2BAA4B,CAC5B,OAAO,KAAK,eACf,CAID,IAAI,0BAA0BnC,EAAO,OACjC,GAAIA,GAAS,KAAK,gBAAiB,CAC/B,GAAI,KAAK,gBAAkBA,EAAO,CAC9B2B,EAAO,MAAM,kGAAkG,EAC/G,MACH,CACD,KAAK,gBAAkB3B,GACnBC,EAAAC,EAAe,cAAf,MAAAD,EAA4B,gBAAkB,KAAK,eAAiB,KAAK,eACzE,KAAK,aAAa,eAAiB,KAAK,gBAE/C,CACJ,CAID,IAAI,2BAA4B,CAC5B,OAAO,KAAK,eACf,CAID,IAAI,0BAA0BD,EAAO,OACjC,GAAIA,GAAS,KAAK,gBAAiB,CAC/B,GAAIA,EAAQ,KAAK,gBAAiB,CAC9B2B,EAAO,MAAM,kGAAkG,EAC/G,MACH,CACD,KAAK,gBAAkB3B,GACnBC,EAAAC,EAAe,cAAf,MAAAD,EAA4B,gBAAkB,KAAK,eAAiB,KAAK,eACzE,KAAK,aAAa,eAAiB,KAAK,gBAE/C,CACJ,CAKD,YAAYmC,EAAa,OACjBA,EAAY,OAAO,KAAK,SAAS,IAGrC,KAAK,UAAU,SAASA,CAAW,GAC/BnC,EAAAC,EAAe,cAAf,MAAAD,EAA4B,gBAC5B,KAAK,eACL,KAAK,cACL,CAAC,MAAM,KAAK,UAAU,CAAC,GACvB,CAAC,MAAM,KAAK,UAAU,CAAC,GACvB,CAAC,MAAM,KAAK,UAAU,CAAC,IACvB,KAAK,aAAa,UAAU,MAAQ,KAAK,UAAU,EACnD,KAAK,aAAa,UAAU,MAAQ,KAAK,UAAU,EACnD,KAAK,aAAa,UAAU,MAAQ,KAAK,UAAU,GAE1D,CAKD,wBAAwBoC,EAAmB,OACvC,KAAK,gBAAkBA,GACnBpC,EAAAC,EAAe,cAAf,MAAAD,EAA4B,gBAAkB,KAAK,yBAA2B,KAAK,WACnF,KAAK,iBAAgB,CAE5B,CACD,kBAAmB,CACf,GAAI,CAAC,KAAK,yBAA2B,CAAC,KAAK,aACvC,OAEJ,MAAMqC,EAAM,KAAK,wBAAwB,eAAc,EACjDC,EAAY3B,EAAQ,gBAAgB,KAAK,gBAAiB0B,CAAG,EACnEC,EAAU,UAAS,EACnB,KAAK,aAAa,aAAa,MAAQA,EAAU,EACjD,KAAK,aAAa,aAAa,MAAQA,EAAU,EACjD,KAAK,aAAa,aAAa,MAAQA,EAAU,CACpD,CAED,4BAA6B,OACzB,IAAItC,EAAAC,EAAe,cAAf,MAAAD,EAA4B,gBAAkB,KAAK,yBAA2B,KAAK,sBAAwB,KAAK,YAAc,KAAK,OAAO,aAAc,CACxJ,MAAMuC,EAAW,KAAK,OAAO,8BACvB,KAAK,wBAAwB,SAAS,SAAS,KAAK,OAAO,+BAA+B,EAAE,OAAQ,EACpG,KAAK,wBAAwB,oBAAoB,KAAK,OAAO,YAAY,EAC/E,KAAK,WAAW,KAAK,MAAQ,KAAK,2BAA2B,KAAK,QAASA,EAAU,KAAK,YAAa,KAAK,YAAa,KAAK,aAAa,CAC9I,CACJ,CAMD,uBAAuBC,EAAU,CAC7B,KAAK,2BAA6BA,CACrC,CAOD,KAAKC,EAAMC,EAAQC,EAAQ,aACvB,GAAI,KAAK,gBAAkB,KAAK,OAAO,gBAAgB3C,EAAAC,EAAe,cAAf,MAAAD,EAA4B,cAC/E,GAAI,CACA,KAAK,2BAA0B,EAC/B,IAAI4C,EAAYH,IAAOI,EAAA5C,EAAe,cAAf,YAAA4C,EAA4B,aAAa,aAAcJ,GAAOK,EAAA7C,EAAe,cAAf,YAAA6C,EAA4B,aAAa,YAqB9H,IApBI,CAAC,KAAK,cAAgB,CAAC,KAAK,mBACxB,KAAK,eAAiB,KAAK,eACvB,CAAC,MAAM,KAAK,UAAU,CAAC,GAAK,CAAC,MAAM,KAAK,UAAU,CAAC,GAAK,CAAC,MAAM,KAAK,UAAU,CAAC,IAC/E,KAAK,aAAa,UAAU,MAAQ,KAAK,UAAU,EACnD,KAAK,aAAa,UAAU,MAAQ,KAAK,UAAU,EACnD,KAAK,aAAa,UAAU,MAAQ,KAAK,UAAU,GAEnD,KAAK,iBACL,KAAK,aAAa,eAAiB,KAAK,gBACxC,KAAK,aAAa,eAAiB,KAAK,gBACxC,KAAK,aAAa,cAAgB,KAAK,eACnC,KAAK,wBACL,KAAK,iBAAgB,EAGrB,KAAK,aAAa,eAAe,KAAK,gBAAgB,EAAG,KAAK,gBAAgB,EAAG,KAAK,gBAAgB,CAAC,IAKnH,KAAK,YAcL,GAbI,CAAC,KAAK,kBAAoB,KAAK,oBAC/B,KAAK,iBAAmB7C,EAAe,YAAY,aAAa,yBAAyB,KAAK,iBAAiB,EAC/G,KAAK,kBAAkB,QAAU,IAAM,CACnC,KAAK,SAAQ,CACzC,EACwB,KAAK,kBAAkB,aAAe,KAAK,eAE3C,KAAK,mBACL,KAAK,iBAAiB,aAClB,KAAK,iBACL,KAAK,iBAAiB,QAAQ,KAAK,eAAe,GAGtD,KAAK,kBAAmB,CAKxB,MAAM8C,EAAY,IAAM,SACpB,IAAI/C,EAAAC,EAAe,cAAf,MAAAD,EAA4B,SAAU,CACtC,GAAI,CAAC,KAAK,kBACN,OAEJ,KAAK,kBAAkB,YAAc0C,GAAU,EAC/C,MAAMM,EAAc,KAAK,kBAAkB,KAAI,EAG3CA,IAAgB,QAChBA,EAAY,MAAM,IAAM,UAGpBhD,EAAAC,EAAe,cAAf,MAAAD,EAA4B,QACxB,KAAK,MAAQ,KAAK,YAClB,KAAK,wBAAyB6C,EAAA5C,EAAe,cAAf,YAAA4C,EAA4B,0BAA0B,QAAQ,IAAM,CAC9FE,GAChD,GAEA,CAAqC,CAER,MAEO,KAAK,MAAQ,KAAK,YAClB,KAAK,wBAAyBF,EAAA5C,EAAe,cAAf,YAAA4C,EAA4B,0BAA0B,QAAQ,IAAM,CAC9FE,GACxC,GAGA,EACwBA,GACH,MAEA,CACD,MAAMA,EAAY,IAAM,WACpB,IAAI/C,EAAAC,EAAe,cAAf,MAAAD,EAA4B,aAAc,CAK1C,GAJA2C,EAASA,GAAU,KAAK,QACpBD,IAAW,QACX,KAAK,WAAWA,CAAM,EAEtB,KAAK,aAAc,CACnB,MAAMO,EAAY,KAAK,aACvBA,EAAU,QAAU,IAAM,CACtBA,EAAU,WAAU,CACxD,CAC6B,CAED,GADA,KAAK,cAAeJ,EAAA5C,EAAe,cAAf,YAAA4C,EAA4B,aAAa,qBACzD,KAAK,cAAgB,KAAK,gBAAiB,CAC3C,KAAK,aAAa,OAAS,KAAK,aAChC,KAAK,aAAa,QAAQ,KAAK,eAAe,EAC9C,KAAK,aAAa,KAAO,KAAK,KAC1BH,IAAW,SACX,KAAK,aAAa,UAAYA,GAE9BC,IAAW,SACX,KAAK,aAAa,SAAWD,EAAS,GAAKC,GAE/C,KAAK,aAAa,aAAa,MAAQ,KAAK,cAC5C,KAAK,aAAa,QAAU,IAAM,CAC9B,KAAK,SAAQ,CACjD,EACgCC,EAAYH,IAAOK,EAAA7C,EAAe,cAAf,YAAA6C,EAA4B,aAAa,aAAcL,EAAOxC,EAAe,YAAY,aAAa,YACzH,MAAMiD,IAAiB,KAAK,SAAW,KAAK,YAAc,IAAM,KAAK,SAAW,IAAM,KAAK,aAAa,OAAO,SAC/G,KAAK,aAAa,MAAMN,EAAWM,EAAc,KAAK,KAAO,OAAYP,CAAM,CAClF,CACJ,CACzB,IACwBQ,EAAAlD,EAAe,cAAf,YAAAkD,EAA4B,aAAa,SAAU,YAEnD,KAAK,kBAAoB,WAAW,IAAM,SAClCnD,EAAAC,EAAe,cAAf,YAAAD,EAA4B,aAAa,SAAU,aAGnDC,EAAe,YAAY,QACvB,KAAK,MAAQ,KAAK,YAClB,KAAK,uBAAyBA,EAAe,YAAY,0BAA0B,QAAQ,IAAM,CAC7F8C,GACxC,CAAqC,IAILA,GAEP,EAAE,GAAG,EAGNA,GAEP,CACD,KAAK,WAAaH,EAClB,KAAK,UAAY,GACjB,KAAK,SAAW,EACnB,OACMQ,EAAI,CACP1B,EAAO,MAAM,qCAAuC,KAAK,KAAO,KAAO0B,EAAG,OAAO,CACpF,CAER,CACD,UAAW,CACP,KAAK,UAAY,GACjB,KAAK,WAAa,EAClB,KAAK,aAAe,EAChB,KAAK,SACL,KAAK,QAAO,EAEhB,KAAK,kBAAkB,gBAAgB,IAAI,CAC9C,CAKD,KAAKX,EAAM,SACP,GAAI,KAAK,UAEL,GADA,KAAK,2BAA0B,EAC3B,KAAK,WACD,KAAK,mBACL,KAAK,kBAAkB,QAEnB,KAAK,kBAAkB,YAAc,IACrC,KAAK,kBAAkB,YAAc,KAIzCzC,EAAA,KAAK,mBAAL,MAAAA,EAAuB,aAE3B,KAAK,UAAY,YAEZ6C,EAAA5C,EAAe,cAAf,MAAA4C,EAA4B,cAAgB,KAAK,aAAc,CACpE,MAAMQ,EAAWZ,EAAOxC,EAAe,YAAY,aAAa,YAAcwC,EAAO,OACrF,KAAK,aAAa,QAAU,IAAM,CAC9B,KAAK,UAAY,GACjB,KAAK,SAAW,GAChB,KAAK,WAAa,EAClB,KAAK,aAAe,EAChB,KAAK,eACL,KAAK,aAAa,QAAU,IAAA,IAEhC,KAAK,SAAQ,CACjC,EACgB,KAAK,aAAa,KAAKY,CAAQ,CAClC,MAEG,KAAK,UAAY,QAGhB,KAAK,WACV,KAAK,SAAW,GAChB,KAAK,WAAa,EAClB,KAAK,aAAe,EAE3B,CAID,OAAQ,SACA,KAAK,YACL,KAAK,2BAA0B,EAC3B,KAAK,YACD,KAAK,kBACL,KAAK,kBAAkB,SAGvBrD,EAAA,KAAK,mBAAL,MAAAA,EAAuB,aAE3B,KAAK,UAAY,GACjB,KAAK,SAAW,KAEX6C,EAAA5C,EAAe,cAAf,MAAA4C,EAA4B,cAAgB,KAAK,eACtD,KAAK,aAAa,QAAU,IAAA,GAC5B,KAAK,aAAa,OAClB,KAAK,UAAY,GACjB,KAAK,SAAW,GAChB,KAAK,cAAgB5C,EAAe,YAAY,aAAa,YAAc,KAAK,YAG3F,CAMD,UAAUqD,EAAWb,EAAM,QACnBzC,EAAAC,EAAe,cAAf,MAAAD,EAA4B,gBAAkB,KAAK,aAC/CyC,GAAQxC,EAAe,YAAY,cACnC,KAAK,WAAW,KAAK,sBAAsBA,EAAe,YAAY,aAAa,WAAW,EAC9F,KAAK,WAAW,KAAK,eAAe,KAAK,WAAW,KAAK,MAAOA,EAAe,YAAY,aAAa,WAAW,EACnH,KAAK,WAAW,KAAK,wBAAwBqD,EAAWrD,EAAe,YAAY,aAAa,YAAcwC,CAAI,GAGlH,KAAK,WAAW,KAAK,MAAQa,GAGrC,KAAK,QAAUA,CAClB,CAKD,gBAAgBC,EAAiB,CAC7B,KAAK,cAAgBA,EACjB,KAAK,YACD,KAAK,YAAc,KAAK,kBACxB,KAAK,kBAAkB,aAAe,KAAK,cAEtC,KAAK,eACV,KAAK,aAAa,aAAa,MAAQ,KAAK,eAGvD,CAKD,iBAAkB,CACd,OAAO,KAAK,aACf,CAKD,WAAY,CACR,OAAO,KAAK,OACf,CAMD,aAAaC,EAAe,CACpB,KAAK,yBAA2B,KAAK,gBACrC,KAAK,wBAAwB,iCAAiC,KAAK,aAAa,EAChF,KAAK,cAAgB,MAEzB,KAAK,wBAA0BA,EAC1B,KAAK,gBACN,KAAK,cAAgB,GACrB,KAAK,yBAAwB,EACzB,KAAK,WAAa,KAAK,OACvB,KAAK,KAAI,EACT,KAAK,KAAK,EAAG,KAAK,QAAS,KAAK,OAAO,IAG/C,KAAK,kCAAkC,KAAK,uBAAuB,EACnE,KAAK,cAAiBA,GAAkB,KAAK,kCAAkCA,CAAa,EAC5F,KAAK,wBAAwB,+BAA+B,KAAK,aAAa,CACjF,CAKD,gBAAiB,CACT,KAAK,yBAA2B,KAAK,gBACrC,KAAK,wBAAwB,iCAAiC,KAAK,aAAa,EAChF,KAAK,cAAgB,KACrB,KAAK,wBAA0B,KAEtC,CACD,kCAAkCC,EAAM,OACpC,GAAI,CAACA,EAAK,gBACN,KAAK,YAAYA,EAAK,gBAAgB,MAErC,CAED,MAAMC,EADOD,EACa,kBAC1B,KAAK,YAAYC,EAAa,eAAe,WAAW,CAC3D,EACG1D,EAAAC,EAAe,cAAf,MAAAD,EAA4B,gBAAkB,KAAK,gBAAkB,KAAK,WAC1E,KAAK,iBAAgB,CAE5B,CAKD,OAAQ,CACJ,GAAK,KAAK,WAgCN,OAAO,KAhCW,CAClB,MAAM2D,EAAkB,IAAM,CAC1BC,EAAmB,IAAM,KAAK,eAAgB,IAAM,CAChDC,EAAY,aAAe,KAAK,iBAChCA,EAAY,eAAiB,GACzBA,EAAY,UACZA,EAAY,KAAK,EAAG,KAAK,QAAS,KAAK,OAAO,CAEtE,EAAmB,OAAW,GAAG,CACjC,EACkBC,EAAiB,CACnB,SAAU,KAAK,SACf,KAAM,KAAK,KACX,OAAQ,KAAK,QACb,aAAc,KAAK,cACnB,YAAa,KAAK,YAClB,qBAAsB,KAAK,qBAC3B,cAAe,KAAK,cACpB,YAAa,KAAK,YAClB,cAAe,KAAK,aACpC,EACkBD,EAAc,IAAI/D,EAAM,KAAK,KAAO,UAAW,IAAI,YAAY,CAAC,EAAG,KAAK,OAAQ,KAAMgE,CAAc,EAC1G,OAAI,KAAK,sBACLD,EAAY,uBAAuB,KAAK,0BAA0B,EAEtEA,EAAY,YAAY,KAAK,SAAS,EACtCA,EAAY,gBAAgB,KAAK,aAAa,EAC9CF,IACOE,CACV,CAKJ,CAKD,gBAAiB,CACb,OAAO,KAAK,YACf,CAKD,gBAAiB,CACb,OAAO,KAAK,YACf,CAKD,cAAe,CACX,OAAO,KAAK,UACf,CAKD,WAAY,CACR,MAAME,EAAsB,CACxB,KAAM,KAAK,KACX,IAAK,KAAK,KACV,SAAU,KAAK,SACf,KAAM,KAAK,KACX,OAAQ,KAAK,QACb,aAAc,KAAK,cACnB,YAAa,KAAK,YAClB,cAAe,KAAK,cACpB,YAAa,KAAK,YAClB,cAAe,KAAK,cACpB,aAAc,KAAK,cACnB,aAAc,KAAK,cACnB,aAAc,KAAK,aACnB,SAAU,KAAK,QAC3B,EACQ,OAAI,KAAK,gBACD,KAAK,0BACLA,EAAoB,gBAAkB,KAAK,wBAAwB,IAEvEA,EAAoB,SAAW,KAAK,UAAU,QAAO,EACrDA,EAAoB,YAAc,KAAK,YACvCA,EAAoB,cAAgB,KAAK,cACzCA,EAAoB,cAAgB,KAAK,eACzCA,EAAoB,qBAAuB,KAAK,gBAAgB,QAAO,EACvEA,EAAoB,eAAiB,KAAK,gBAC1CA,EAAoB,eAAiB,KAAK,gBAC1CA,EAAoB,cAAgB,KAAK,gBAEtCA,CACV,CASD,OAAO,MAAMC,EAAazD,EAAO0D,EAASC,EAAa,CACnD,MAAMC,EAAYH,EAAY,KAC9B,IAAII,EACAJ,EAAY,IACZI,EAAWH,EAAUD,EAAY,IAGjCI,EAAWH,EAAUE,EAEzB,MAAM1D,EAAU,CACZ,SAAUuD,EAAY,SACtB,KAAMA,EAAY,KAClB,OAAQA,EAAY,OACpB,aAAcA,EAAY,aAC1B,YAAaA,EAAY,YACzB,cAAeA,EAAY,cAC3B,YAAaA,EAAY,YACzB,cAAeA,EAAY,cAC3B,aAAcA,EAAY,YACtC,EACQ,IAAIK,EACJ,GAAI,CAACH,EACDG,EAAW,IAAIvE,EAAMqE,EAAWC,EAAU7D,EAAO,IAAM,CACnDA,EAAM,kBAAkB8D,CAAQ,CACnC,EAAE5D,CAAO,EACVF,EAAM,eAAe8D,CAAQ,MAE5B,CACD,MAAMV,EAAkB,IAAM,CAC1BC,EAAmB,IAAMM,EAAY,eAAgB,IAAM,CACvDG,EAAS,aAAeH,EAAY,iBACpCG,EAAS,eAAiB,GACtBA,EAAS,UACTA,EAAS,KAAK,EAAGA,EAAS,QAASA,EAAS,OAAO,CAE3E,EAAmB,OAAW,GAAG,CACjC,EACYA,EAAW,IAAIvE,EAAMqE,EAAW,IAAI,YAAY,CAAC,EAAG5D,EAAO,KAAME,CAAO,EACxEkD,GACH,CACD,GAAIK,EAAY,SAAU,CACtB,MAAMM,EAAgB3D,EAAQ,UAAUqD,EAAY,QAAQ,EAC5DK,EAAS,YAAYC,CAAa,CACrC,CACD,GAAIN,EAAY,gBACZK,EAAS,mBAAmBL,EAAY,gBAAkB,IAAKA,EAAY,gBAAkB,IAAKA,EAAY,eAAiB,CAAC,EAC5HA,EAAY,sBAAsB,CAClC,MAAMO,EAAuB5D,EAAQ,UAAUqD,EAAY,oBAAoB,EAC/EK,EAAS,wBAAwBE,CAAoB,CACxD,CAEL,GAAIP,EAAY,gBAAiB,CAC7B,MAAMQ,EAAgBjE,EAAM,YAAYyD,EAAY,eAAe,EAC/DQ,GACAH,EAAS,aAAaG,CAAa,CAE1C,CACD,OAAIR,EAAY,WACZK,EAAS,SAAWL,EAAY,UAE7BK,CACV,CACD,WAAWtE,EAAO,CACV,KAAK,UAAYA,IAGjB,KAAK,WACL,KAAK,KAAI,EACT,KAAK,SAAW,IAEpB,KAAK,QAAUA,EAClB,CACD,4BAA6B,OACrB,KAAK,oBACL,aAAa,KAAK,iBAAiB,EACnC,KAAK,kBAAoB,MAEzB,KAAK,0BACLC,EAAAC,EAAe,cAAf,MAAAD,EAA4B,0BAA0B,OAAO,KAAK,wBAClE,KAAK,uBAAyB,KAErC,CACL,CAIAF,EAAM,8BAAiC2E,GAAM,CACzC,MAAMC,EAAY,qBAAqB,CAC3C,EAEAC,EAAc,gBAAiB7E,CAAK,ECzqC7B,MAAM8E,CAAc,CAOvB,YAAYC,EAAMC,EAAQC,EAAS,CAY/B,GAVA,KAAK,KAAO,GACZ,KAAK,gBAAkB,IACvB,KAAK,gBAAkB,IACvB,KAAK,QAAU,EAEf,KAAK,UAAY,GAEjB,KAAK,SAAW,GAChB,KAAK,QAAU,GACf,KAAK,SAAW,GACZD,EAAO,SAAWC,EAAQ,OAC1B,MAAM,IAAI,MAAM,6CAA6C,EAEjE,KAAK,KAAOF,EACZ,KAAK,SAAWE,EAEhB,IAAIC,EAAY,EAChB,UAAWC,KAAUF,EACjBC,GAAaC,EAEjB,MAAMC,EAAeF,EAAY,EAAI,EAAIA,EAAY,EACrD,QAAS3D,EAAI,EAAGA,EAAI,KAAK,SAAS,OAAQA,IACtC,KAAK,SAASA,CAAC,GAAK6D,EAExB,KAAK,QAAUJ,EACf,UAAWK,KAAS,KAAK,QACrBA,EAAM,kBAAkB,IAAI,IAAM,CAC9B,KAAK,SAAQ,CAC7B,CAAa,CAER,CAID,IAAI,2BAA4B,CAC5B,OAAO,KAAK,eACf,CAID,IAAI,0BAA0BpF,EAAO,CACjC,GAAIA,IAAU,KAAK,gBAAiB,CAChC,GAAI,KAAK,gBAAkBA,EAAO,CAC9B2B,EAAO,MAAM,kGAAkG,EAC/G,MACH,CACD,KAAK,gBAAkB3B,EACvB,UAAWoF,KAAS,KAAK,QACrBA,EAAM,0BAA4BpF,CAEzC,CACJ,CAKD,IAAI,2BAA4B,CAC5B,OAAO,KAAK,eACf,CAKD,IAAI,0BAA0BA,EAAO,CACjC,GAAIA,IAAU,KAAK,gBAAiB,CAChC,GAAIA,EAAQ,KAAK,gBAAiB,CAC9B2B,EAAO,MAAM,kGAAkG,EAC/G,MACH,CACD,KAAK,gBAAkB3B,EACvB,UAAWoF,KAAS,KAAK,QACrBA,EAAM,0BAA4BpF,CAEzC,CACJ,CAID,IAAI,QAAS,CACT,OAAO,KAAK,OACf,CAID,IAAI,OAAOA,EAAO,CACd,GAAIA,IAAU,KAAK,QACf,UAAWoF,KAAS,KAAK,QACrBA,EAAM,UAAUpF,CAAK,CAGhC,CACD,UAAW,CACH,KAAK,gBAAkB,SACvB,KAAK,QAAQ,KAAK,aAAa,EAAE,SAAW,IAE5C,KAAK,MAAQ,KAAK,UAClB,KAAK,KAAI,EAGT,KAAK,UAAY,EAExB,CAID,OAAQ,CACA,KAAK,YACL,KAAK,SAAW,GACZ,KAAK,gBAAkB,QACvB,KAAK,QAAQ,KAAK,aAAa,EAAE,MAAK,EAGjD,CAID,MAAO,CACH,KAAK,UAAY,GACb,KAAK,gBAAkB,QACvB,KAAK,QAAQ,KAAK,aAAa,EAAE,KAAI,CAE5C,CAKD,KAAKqF,EAAa,CACd,GAAI,CAAC,KAAK,SAAU,CAChB,KAAK,KAAI,EACT,MAAMC,EAAc,KAAK,SACzB,IAAIC,EAAQ,EACZ,QAASjE,EAAI,EAAGA,EAAI,KAAK,SAAS,OAAQA,IAEtC,GADAiE,GAAS,KAAK,SAASjE,CAAC,EACpBgE,GAAeC,EAAO,CACtB,KAAK,cAAgBjE,EACrB,KACH,CAER,CACD,MAAM8D,EAAQ,KAAK,QAAQ,KAAK,eAAiB,CAAC,EAC9CA,EAAM,UACNA,EAAM,KAAK,EAAG,KAAK,SAAW,OAAYC,CAAW,EAGrDD,EAAM,SAAW,GAErB,KAAK,UAAY,GACjB,KAAK,SAAW,EACnB,CACL,CC3JO,MAAMI,CAAW,CAOpB,YAAYhF,EAAOE,EAAU,GAAI,CAI7B,KAAK,GAAK,GACV,KAAK,eAAiB,GACtBF,EAAQA,GAASK,EAAY,iBACxBL,IAGL,KAAK,OAASA,EACd,KAAK,gBAAkB,GACvB,KAAK,SAAWE,EACZ,CAAC,KAAK,SAAS,WAAa,KAAK,OAAO,cACxC,KAAK,OAAO,YAAY,KAAK,IAAI,EACjC,KAAK,GAAK,KAAK,OAAO,YAAY,OAAS,GAElD,CACD,iCAAkC,QAC1BT,EAAAC,EAAe,cAAf,MAAAD,EAA4B,gBAAkBC,EAAe,YAAY,eACzE,KAAK,iBAAmBA,EAAe,YAAY,aAAa,WAAU,EAC1E,KAAK,iBAAiB,QAAQA,EAAe,YAAY,UAAU,EAC/D,KAAK,UACD,KAAK,SAAS,SACd,KAAK,iBAAiB,KAAK,MAAQ,KAAK,SAAS,QAGzD,KAAK,eAAiB,GAE7B,CAID,SAAU,CACN,GAAIA,EAAe,aAAeA,EAAe,YAAY,eAAgB,CAIzE,IAHI,KAAK,oBACL,KAAK,mBAAmB,kBAErB,KAAK,gBAAgB,QACxB,KAAK,gBAAgB,CAAC,EAAE,QAAO,EAE/B,KAAK,kBACL,KAAK,iBAAiB,aAE1B,KAAK,iBAAmB,IAC3B,CACJ,CAMD,SAASkF,EAAO,OACP,KAAK,gBACN,KAAK,gCAA+B,GAEpCnF,EAAAC,EAAe,cAAf,MAAAD,EAA4B,gBAAkB,KAAK,kBACnDmF,EAAM,6BAA6B,KAAK,gBAAgB,EAExDA,EAAM,eAAiB,SACnBA,EAAM,eAAiB,GACvB,KAAK,OAAO,eAAe,YAAYA,CAAK,EAEvC,KAAK,OAAO,aACjB,KAAK,OAAO,YAAYA,EAAM,YAAY,EAAE,YAAYA,CAAK,GAGrE,KAAK,gBAAgB,KAAKA,CAAK,EAC/BA,EAAM,aAAe,KAAK,EAC7B,CAMD,YAAYA,EAAO,CACf,MAAMK,EAAQ,KAAK,gBAAgB,QAAQL,CAAK,EAC5CK,IAAU,IACV,KAAK,gBAAgB,OAAOA,EAAO,CAAC,CAE3C,CAKD,UAAUlC,EAAW,QACbtD,EAAAC,EAAe,cAAf,MAAAD,EAA4B,gBAAkB,KAAK,mBACnD,KAAK,iBAAiB,KAAK,MAAQsD,EAE1C,CAMD,0BAA2B,OACvB,IAAItD,EAAAC,EAAe,cAAf,MAAAD,EAA4B,eAC5B,QAASqB,EAAI,EAAGA,EAAI,KAAK,gBAAgB,OAAQA,IAC7C,KAAK,gBAAgBA,CAAC,EAAE,yBAAwB,CAG3D,CAMD,gCAAiC,OAC7B,IAAIrB,EAAAC,EAAe,cAAf,MAAAD,EAA4B,eAC5B,QAASqB,EAAI,EAAGA,EAAI,KAAK,gBAAgB,OAAQA,IAC7C,KAAK,gBAAgBA,CAAC,EAAE,+BAA8B,CAGjE,CAOD,kBAAkBoE,EAAU,OACpB,KAAK,oBACL,KAAK,mBAAmB,kBAE5B,KAAK,mBAAqBA,GACtBzF,EAAAC,EAAe,cAAf,MAAAD,EAA4B,gBAAkB,KAAK,mBACnD,KAAK,iBAAiB,aACtB,KAAK,mBAAmB,kBAAkB,KAAK,iBAAkBC,EAAe,YAAY,UAAU,EAE7G,CACL,CCzGA,OAAO,eAAeyF,EAAM,UAAW,iBAAkB,CACrD,IAAK,UAAY,CACb,IAAIC,EAAQ,KAAK,cAAcC,EAAwB,UAAU,EACjE,OAAKD,IACDA,EAAQ,IAAIE,EAAoB,IAAI,EACpC,KAAK,cAAcF,CAAK,GAEvB,KAAK,kBACN,KAAK,gBAAkB,IAAIJ,EAAW,KAAM,CAAE,UAAW,EAAI,CAAE,GAE5D,KAAK,eACf,EACD,WAAY,GACZ,aAAc,EAClB,CAAC,EACDG,EAAM,UAAU,eAAiB,SAAUrF,EAAM,CAC7C,IAAImF,EACJ,IAAKA,EAAQ,EAAGA,EAAQ,KAAK,eAAe,gBAAgB,OAAQA,IAChE,GAAI,KAAK,eAAe,gBAAgBA,CAAK,EAAE,OAASnF,EACpD,OAAO,KAAK,eAAe,gBAAgBmF,CAAK,EAGxD,GAAI,KAAK,aACL,QAASM,EAAU,EAAGA,EAAU,KAAK,YAAY,OAAQA,IACrD,IAAKN,EAAQ,EAAGA,EAAQ,KAAK,YAAYM,CAAO,EAAE,gBAAgB,OAAQN,IACtE,GAAI,KAAK,YAAYM,CAAO,EAAE,gBAAgBN,CAAK,EAAE,OAASnF,EAC1D,OAAO,KAAK,YAAYyF,CAAO,EAAE,gBAAgBN,CAAK,EAKtE,OAAO,IACX,EACA,OAAO,eAAeE,EAAM,UAAW,eAAgB,CACnD,IAAK,UAAY,CACb,IAAIC,EAAQ,KAAK,cAAcC,EAAwB,UAAU,EACjE,OAAKD,IACDA,EAAQ,IAAIE,EAAoB,IAAI,EACpC,KAAK,cAAcF,CAAK,GAErBA,EAAM,YAChB,EACD,IAAK,SAAU5F,EAAO,CAClB,IAAI4F,EAAQ,KAAK,cAAcC,EAAwB,UAAU,EAC5DD,IACDA,EAAQ,IAAIE,EAAoB,IAAI,EACpC,KAAK,cAAcF,CAAK,GAExB5F,EACA4F,EAAM,YAAW,EAGjBA,EAAM,aAAY,CAEzB,EACD,WAAY,GACZ,aAAc,EAClB,CAAC,EACD,OAAO,eAAeD,EAAM,UAAW,YAAa,CAChD,IAAK,UAAY,CACb,IAAIC,EAAQ,KAAK,cAAcC,EAAwB,UAAU,EACjE,OAAKD,IACDA,EAAQ,IAAIE,EAAoB,IAAI,EACpC,KAAK,cAAcF,CAAK,GAErBA,EAAM,SAChB,EACD,IAAK,SAAU5F,EAAO,CAClB,IAAI4F,EAAQ,KAAK,cAAcC,EAAwB,UAAU,EAC5DD,IACDA,EAAQ,IAAIE,EAAoB,IAAI,EACpC,KAAK,cAAcF,CAAK,GAExB5F,EACA4F,EAAM,6BAA4B,EAGlCA,EAAM,iCAAgC,CAE7C,EACD,WAAY,GACZ,aAAc,EAClB,CAAC,EACD,OAAO,eAAeD,EAAM,UAAW,gCAAiC,CACpE,IAAK,UAAY,CACb,IAAIC,EAAQ,KAAK,cAAcC,EAAwB,UAAU,EACjE,OAAKD,IACDA,EAAQ,IAAIE,EAAoB,IAAI,EACpC,KAAK,cAAcF,CAAK,GAErBA,EAAM,6BAChB,EACD,IAAK,SAAU5F,EAAO,CAClB,IAAI4F,EAAQ,KAAK,cAAcC,EAAwB,UAAU,EAKjE,GAJKD,IACDA,EAAQ,IAAIE,EAAoB,IAAI,EACpC,KAAK,cAAcF,CAAK,GAExB5F,GAAS,OAAOA,GAAU,WAC1B,MAAM,IAAI,MAAM,qGAAqG,EAGrH4F,EAAM,8BAAgC5F,CAE7C,EACD,WAAY,GACZ,aAAc,EAClB,CAAC,EACD,OAAO,eAAe2F,EAAM,UAAW,gCAAiC,CACpE,IAAK,UAAY,CACb,IAAIC,EAAQ,KAAK,cAAcC,EAAwB,UAAU,EACjE,OAAKD,IACDA,EAAQ,IAAIE,EAAoB,IAAI,EACpC,KAAK,cAAcF,CAAK,GAErBA,EAAM,6BAChB,EACD,IAAK,SAAU5F,EAAO,CAClB,IAAI4F,EAAQ,KAAK,cAAcC,EAAwB,UAAU,EAKjE,GAJKD,IACDA,EAAQ,IAAIE,EAAoB,IAAI,EACpC,KAAK,cAAcF,CAAK,GAExB5F,GAAS,OAAOA,GAAU,WAC1B,MAAM,IAAI,MAAM,qGAAqG,EAGrH4F,EAAM,8BAAgC5F,CAE7C,EACD,WAAY,GACZ,aAAc,EAClB,CAAC,EACD,OAAO,eAAe2F,EAAM,UAAW,8BAA+B,CAClE,IAAK,UAAY,CACb,IAAIC,EAAQ,KAAK,cAAcC,EAAwB,UAAU,EACjE,OAAKD,IACDA,EAAQ,IAAIE,EAAoB,IAAI,EACpC,KAAK,cAAcF,CAAK,GAErBA,EAAM,2BAChB,EACD,IAAK,SAAU5F,EAAO,CAClB,IAAI4F,EAAQ,KAAK,cAAcC,EAAwB,UAAU,EAC5DD,IACDA,EAAQ,IAAIE,EAAoB,IAAI,EACpC,KAAK,cAAcF,CAAK,GAE5BA,EAAM,4BAA8B5F,CACvC,EACD,WAAY,GACZ,aAAc,EAClB,CAAC,EAKM,MAAM8F,CAAoB,CAK7B,IAAI,cAAe,CACf,OAAO,KAAK,aACf,CAKD,IAAI,WAAY,CACZ,OAAO,KAAK,UACf,CAKD,YAAYtF,EAAO,CAIf,KAAK,KAAOqF,EAAwB,WACpC,KAAK,cAAgB,GACrB,KAAK,WAAa,GAIlB,KAAK,4BAA8B,IAKnC,KAAK,8BAAgC,KAKrC,KAAK,8BAAgC,KACrC,KAAK,uBAAyB,IAAIjF,EAClC,KAAK,sBAAwB,IAAIA,EACjC,KAAK,WAAa,EAClB,KAAK,kBAAoB,IAAIoF,EAC7B,KAAK,qBAAuB,IAAIpF,EAChCJ,EAAQA,GAASK,EAAY,iBACxBL,IAGL,KAAK,MAAQA,EACbA,EAAM,YAAc,GACpBA,EAAM,OAAS,GAClB,CAID,UAAW,CACP,KAAK,MAAM,kBAAkB,aAAaqF,EAAwB,uBAAwB,KAAM,KAAK,YAAY,CACpH,CAKD,SAAU,CAET,CAKD,UAAU7B,EAAqB,CAE3B,GADAA,EAAoB,OAAS,GACzB,KAAK,MAAM,YACX,QAASyB,EAAQ,EAAGA,EAAQ,KAAK,MAAM,YAAY,OAAQA,IAAS,CAChE,MAAMQ,EAAa,KAAK,MAAM,YAAYR,CAAK,EAC/C,QAASS,EAAU,EAAGA,EAAUD,EAAW,gBAAgB,OAAQC,IAC/DlC,EAAoB,OAAO,KAAKiC,EAAW,gBAAgBC,CAAO,EAAE,UAAS,CAAE,CAEtF,CAER,CAKD,iBAAiBC,EAAW,CACnBA,EAAU,QAGfA,EAAU,OAAO,QAASf,GAAU,CAChCA,EAAM,KAAI,EACVA,EAAM,SAAW,GACjB,KAAK,MAAM,eAAe,SAASA,CAAK,CACpD,CAAS,CACJ,CAMD,oBAAoBe,EAAWC,EAAU,GAAO,CACvCD,EAAU,QAGfA,EAAU,OAAO,QAASf,GAAU,CAChCA,EAAM,KAAI,EACVA,EAAM,SAAW,GACjB,KAAK,MAAM,eAAe,YAAYA,CAAK,EACvCgB,GACAhB,EAAM,QAAO,CAE7B,CAAS,CACJ,CAID,SAAU,CACN,MAAM5E,EAAQ,KAAK,MAInB,GAHIA,EAAM,iBACNA,EAAM,eAAe,UAErBA,EAAM,YACN,QAAS6F,EAAU,EAAGA,EAAU7F,EAAM,YAAY,OAAQ6F,IACtD7F,EAAM,YAAY6F,CAAO,EAAE,QAAO,CAG7C,CAID,cAAe,CACX,MAAM7F,EAAQ,KAAK,MACnB,KAAK,cAAgB,GACjBN,EAAe,aAAeA,EAAe,YAAY,cACzDA,EAAe,YAAY,aAAa,UAE5C,IAAIoB,EACJ,IAAKA,EAAI,EAAGA,EAAId,EAAM,eAAe,gBAAgB,OAAQc,IACzDd,EAAM,eAAe,gBAAgBc,CAAC,EAAE,MAAK,EAEjD,GAAId,EAAM,YACN,IAAKc,EAAI,EAAGA,EAAId,EAAM,YAAY,OAAQc,IACtC,QAASgF,EAAI,EAAGA,EAAI9F,EAAM,YAAYc,CAAC,EAAE,gBAAgB,OAAQgF,IAC7D9F,EAAM,YAAYc,CAAC,EAAE,gBAAgBgF,CAAC,EAAE,OAIvD,CAID,aAAc,CACV,MAAM9F,EAAQ,KAAK,MACnB,KAAK,cAAgB,GACjBN,EAAe,aAAeA,EAAe,YAAY,cACzDA,EAAe,YAAY,aAAa,SAE5C,IAAIoB,EACJ,IAAKA,EAAI,EAAGA,EAAId,EAAM,eAAe,gBAAgB,OAAQc,IACrDd,EAAM,eAAe,gBAAgBc,CAAC,EAAE,UACxCd,EAAM,eAAe,gBAAgBc,CAAC,EAAE,KAAI,EAGpD,GAAId,EAAM,YACN,IAAKc,EAAI,EAAGA,EAAId,EAAM,YAAY,OAAQc,IACtC,QAASgF,EAAI,EAAGA,EAAI9F,EAAM,YAAYc,CAAC,EAAE,gBAAgB,OAAQgF,IACzD9F,EAAM,YAAYc,CAAC,EAAE,gBAAgBgF,CAAC,EAAE,UACxC9F,EAAM,YAAYc,CAAC,EAAE,gBAAgBgF,CAAC,EAAE,MAK3D,CAID,8BAA+B,CAC3B,MAAM9F,EAAQ,KAAK,MAGnB,GAFA,KAAK,WAAa,GAClBA,EAAM,eAAe,2BACjBA,EAAM,YACN,QAASc,EAAI,EAAGA,EAAId,EAAM,YAAY,OAAQc,IAC1Cd,EAAM,YAAYc,CAAC,EAAE,yBAAwB,CAGxD,CAID,kCAAmC,CAC/B,MAAMd,EAAQ,KAAK,MAGnB,GAFA,KAAK,WAAa,GAClBA,EAAM,eAAe,iCACjBA,EAAM,YACN,QAASc,EAAI,EAAGA,EAAId,EAAM,YAAY,OAAQc,IAC1Cd,EAAM,YAAYc,CAAC,EAAE,+BAA8B,CAG9D,CACD,cAAe,CACX,MAAMiF,EAAMC,EAAc,IAC1B,GAAI,KAAK,YAAcD,EAAM,KAAK,WAAa,KAAK,4BAChD,OAEJ,KAAK,WAAaA,EAClB,MAAM/F,EAAQ,KAAK,MACnB,GAAI,CAAC,KAAK,eAAiB,CAACA,EAAM,iBAAmB,CAACA,EAAM,aAAgBA,EAAM,gBAAgB,gBAAgB,SAAW,GAAKA,EAAM,YAAY,SAAW,EAC3J,OAEJ,MAAMiG,EAAcvG,EAAe,YACnC,GAAKuG,GAGDA,EAAY,aAAc,CAC1B,IAAIC,EAAkBlG,EAAM,aAM5B,GALIA,EAAM,eAAiBA,EAAM,cAAc,OAAS,IACpDkG,EAAkBlG,EAAM,cAAc,CAAC,GAIvC,KAAK,8BAA+B,CACpC,MAAMmG,EAAW,KAAK,gCAEtBF,EAAY,aAAa,SAAS,YAAYE,EAAS,GAAK,EAAGA,EAAS,GAAK,EAAGA,EAAS,GAAK,CAAC,CAElG,MACQD,EAEA,KAAK,sBAAsB,OAAOA,EAAgB,cAAc,IACjE,KAAK,sBAAsB,SAASA,EAAgB,cAAc,EAClED,EAAY,aAAa,SAAS,YAAYC,EAAgB,eAAe,EAAGA,EAAgB,eAAe,EAAGA,EAAgB,eAAe,CAAC,GAMtJD,EAAY,aAAa,SAAS,YAAY,EAAG,EAAG,CAAC,EAIzD,GAAI,KAAK,8BAA+B,CACpC,MAAMG,EAAW,KAAK,gCACtBH,EAAY,aAAa,SAAS,eAAeG,EAAS,GAAK,EAAGA,EAAS,GAAK,EAAGA,EAAS,GAAK,EAAG,EAAG,EAAG,CAAC,CAE9G,MACQF,GAEDA,EAAgB,YAAcA,EAAgB,WAAW,OAAS,IAClEA,EAAkBA,EAAgB,WAAW,CAAC,GAElDA,EAAgB,cAAe,EAAC,YAAY,KAAK,iBAAiB,EAClE9F,EAAQ,qBAAqBkF,EAAoB,iBAAkB,KAAK,kBAAmB,KAAK,oBAAoB,EACpH,KAAK,qBAAqB,YAEtB,CAAC,MAAM,KAAK,qBAAqB,CAAC,GAAK,CAAC,MAAM,KAAK,qBAAqB,CAAC,GAAK,CAAC,MAAM,KAAK,qBAAqB,CAAC,IAC3G,KAAK,uBAAuB,OAAO,KAAK,oBAAoB,IAC7D,KAAK,uBAAuB,SAAS,KAAK,oBAAoB,EAC9DW,EAAY,aAAa,SAAS,eAAe,KAAK,qBAAqB,EAAG,KAAK,qBAAqB,EAAG,KAAK,qBAAqB,EAAG,EAAG,EAAG,CAAC,KAOvJA,EAAY,aAAa,SAAS,eAAe,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,EAErE,IAAInF,EACJ,IAAKA,EAAI,EAAGA,EAAId,EAAM,eAAe,gBAAgB,OAAQc,IAAK,CAC9D,MAAM8D,EAAQ5E,EAAM,eAAe,gBAAgBc,CAAC,EAChD8D,EAAM,sBACNA,EAAM,2BAA0B,CAEvC,CACD,GAAI5E,EAAM,YACN,IAAKc,EAAI,EAAGA,EAAId,EAAM,YAAY,OAAQc,IACtC,QAASgF,EAAI,EAAGA,EAAI9F,EAAM,YAAYc,CAAC,EAAE,gBAAgB,OAAQgF,IAAK,CAClE,MAAMlB,EAAQ5E,EAAM,YAAYc,CAAC,EAAE,gBAAgBgF,CAAC,EAChDlB,EAAM,sBACNA,EAAM,2BAA0B,CAEvC,CAGZ,CACJ,CACL,CACAU,EAAoB,iBAAmB,IAAIlF,EAAQ,EAAG,EAAG,EAAE,EAC3Db,EAAM,8BAAiCS,GAAU,CAC7C,IAAIoF,EAAQpF,EAAM,cAAcqF,EAAwB,UAAU,EAC7DD,IACDA,EAAQ,IAAIE,EAAoBtF,CAAK,EACrCA,EAAM,cAAcoF,CAAK,EAEjC,ECjeA,MAAMiB,EAAO,qBAMN,MAAMC,CAAmB,CAI5B,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EACZ,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,CACnD,CAED,SAAU,CACN,KAAK,QAAU,KACf,KAAK,OAAS,KACd,KAAK,UAAY,IACpB,CAED,WAAY,CACR,MAAMG,EAAa,KAAK,QAAQ,KAAK,WACrC,GAAIA,GAAcA,EAAW,KAAK,IAAI,EAAG,CACrC,MAAMC,EAAYD,EAAW,KAAK,IAAI,EACtC,KAAK,OAASC,EAAU,MACxB,KAAK,UAAYA,EAAU,SAC3BC,EAAU,OAAO,KAAK,MAAM,EAC5BA,EAAU,OAAO,KAAK,SAAS,CAClC,CACJ,CAID,eAAeC,EAAS3G,EAAO,CAC3B,OAAO4G,EAAW,mBAAmBD,EAAS3G,EAAO,KAAK,KAAM,CAAC6G,EAAkBJ,IAAc,CAC7F,MAAMK,EAAW,IAAI,MACrBA,EAAS,KAAK,KAAK,QAAQ,eAAeH,EAAS3G,CAAK,CAAC,EACzD,UAAW+G,KAAgBN,EAAU,SAAU,CAC3C,MAAMO,EAAUN,EAAU,IAAI,GAAGG,CAAgB,YAAa,KAAK,UAAWE,CAAY,EAC1F,GAAIC,EAAQ,aAAe,MACvBA,EAAQ,aAAe,MACvBA,EAAQ,eAAiB,MACzBA,EAAQ,eAAiB,MACzBA,EAAQ,YAAc,MACtBA,EAAQ,YAAc,KACtB,MAAM,IAAI,MAAM,GAAGH,CAAgB,oFAAoF,EAE3HC,EAAS,KAAK,KAAK,kBAAkB,GAAGD,CAAgB,aAAaG,EAAQ,KAAK,GAAIA,CAAO,CAAC,CACjG,CACD,OAAO,QAAQ,IAAIF,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CACvD,CAAS,CACJ,CAID,cAAcH,EAASzD,EAAM+D,EAAQ,CACjC,OAAOL,EAAW,mBAAmBD,EAASzD,EAAM,KAAK,KAAM,CAAC2D,EAAkBJ,IAAc,CAC5F,MAAMK,EAAW,IAAI,MACrB,OAAO,KAAK,QACP,cAAcD,EAAkB3D,EAAOgE,GAAgB,CACxD,UAAWH,KAAgBN,EAAU,SAAU,CAC3C,MAAMO,EAAUN,EAAU,IAAI,GAAGG,CAAgB,YAAa,KAAK,UAAWE,CAAY,EAC1FD,EAAS,KAAK,KAAK,kBAAkB,GAAGD,CAAgB,aAAaG,EAAQ,KAAK,GAAIA,CAAO,EAAE,KAAK,IAAM,CACtG,UAAWpC,KAASoC,EAAQ,eACxBpC,EAAM,aAAasC,CAAW,GAC1BF,EAAQ,YAAc,MAAaA,EAAQ,YAAc,QACzDpC,EAAM,wBAAwBxE,EAAQ,QAAS,CAAA,EAC/CwE,EAAM,mBAAmB,EAAI5D,EAAM,UAAUgG,EAAQ,YAAc,KAAY,KAAK,GAAKA,EAAQ,UAAU,EAAG,EAAIhG,EAAM,UAAUgG,EAAQ,YAAc,KAAY,KAAK,GAAKA,EAAQ,UAAU,EAAG,CAAC,EAG/M,CAAA,CAAC,CACL,CACDC,EAAOC,CAAW,CAClC,CAAa,EACI,KAAMA,GACA,QAAQ,IAAIJ,CAAQ,EAAE,KAAK,IACvBI,CACV,CACJ,CACb,CAAS,CACJ,CAID,mBAAmBP,EAASQ,EAAW,CACnC,OAAOP,EAAW,mBAAmBD,EAASQ,EAAW,KAAK,KAAM,CAACN,EAAkBJ,IAC5E,KAAK,QAAQ,mBAAmBE,EAASQ,CAAS,EAAE,KAAMC,GAA0B,CACvF,MAAMN,EAAW,IAAI,MACrBJ,EAAU,OAAOD,EAAU,MAAM,EACjC,UAAWY,KAASZ,EAAU,OAC1BK,EAAS,KAAK,KAAK,yBAAyB,GAAGD,CAAgB,WAAWQ,EAAM,KAAK,GAAIV,EAASQ,EAAWE,EAAOD,CAAqB,CAAC,EAE9I,OAAO,QAAQ,IAAIN,CAAQ,EAAE,KAAK,IACvBM,CACV,CACjB,CAAa,CACJ,CACJ,CACD,eAAeT,EAASW,EAAM,CAC1B,GAAIA,EAAK,WACL,OAAOA,EAAK,WAEhB,IAAIC,EACJ,GAAID,EAAK,IACLC,EAAU,KAAK,QAAQ,aAAaZ,EAASW,EAAMA,EAAK,GAAG,MAE1D,CACD,MAAME,EAAad,EAAU,IAAI,GAAGC,CAAO,cAAe,KAAK,QAAQ,KAAK,YAAaW,EAAK,UAAU,EACxGC,EAAU,KAAK,QAAQ,oBAAoB,gBAAgBC,EAAW,KAAK,GAAIA,CAAU,CAC5F,CACD,OAAAF,EAAK,WAAaC,EAAQ,KAAMtG,GACrB,IAAI,gBAAgB,IAAI,KAAK,CAACA,CAAI,EAAG,CAAE,KAAMqG,EAAK,QAAQ,CAAE,CAAC,CACvE,EACMA,EAAK,UACf,CACD,kBAAkBX,EAASK,EAAS,CAEhC,GADAA,EAAQ,eAAiBA,EAAQ,gBAAkB,CAAA,EAC/C,CAACA,EAAQ,aAAc,CACvB,MAAMS,EAAe,IAAI,MACnB3H,EAAOkH,EAAQ,MAAQ,UAAUA,EAAQ,KAAK,GAC9C9G,EAAU,CACZ,KAAM,GACN,SAAU,GACV,OAAQ8G,EAAQ,QAAU,KAAY,EAAIA,EAAQ,MAClE,EACY,QAASlG,EAAI,EAAGA,EAAIkG,EAAQ,MAAM,OAAQlG,IAAK,CAC3C,MAAM4G,EAAc,eAAe,KAAK,IAAI,SACtCJ,EAAOZ,EAAU,IAAIgB,EAAa,KAAK,OAAQV,EAAQ,MAAMlG,CAAC,EAAE,IAAI,EAC1E2G,EAAa,KAAK,KAAK,eAAe,GAAGC,CAAW,IAAIV,EAAQ,MAAMlG,CAAC,EAAE,IAAI,GAAIwG,CAAI,EAAE,KAAMK,GAAc,CACvG,MAAM/C,EAASoC,EAAQ,eAAelG,CAAC,EAAI,IAAIvB,EAAMO,EAAM6H,EAAW,KAAK,QAAQ,aAAc,KAAMzH,CAAO,EAC9G0E,EAAM,YAAcoC,EAAQ,aAAe,EAC3CpC,EAAM,YAAcoC,EAAQ,aAAe,IAC3CpC,EAAM,cAAgBoC,EAAQ,eAAiB,EAC/CpC,EAAM,cAAgBoC,EAAQ,eAAiB,aAClD,CAAA,CAAC,CACL,CACD,MAAMO,EAAU,QAAQ,IAAIE,CAAY,EAAE,KAAK,IAAM,CACjD,MAAMjD,EAAUwC,EAAQ,MAAM,IAAKM,GACxBA,EAAK,QAAU,CACzB,EACKM,EAAgB,IAAIvD,EAAc2C,EAAQ,MAAQ,GAAOA,EAAQ,eAAgBxC,CAAO,EAC1FwC,EAAQ,aACRY,EAAc,0BAA4B,EAAI5G,EAAM,UAAUgG,EAAQ,UAAU,GAEhFA,EAAQ,aACRY,EAAc,0BAA4B,EAAI5G,EAAM,UAAUgG,EAAQ,UAAU,GAEhFA,EAAQ,SACRY,EAAc,OAASZ,EAAQ,QAEnCA,EAAQ,aAAa,MAAQY,CAC7C,CAAa,EACDZ,EAAQ,aAAe,CACnB,OAAQO,CACxB,CACS,CACD,OAAOP,EAAQ,aAAa,MAC/B,CACD,gBAAgBL,EAAS/B,EAAOvF,EAAQ6C,EAAM2C,EAAa,CACvD,OAAQxF,EAAM,CACV,IAAK,OACD,OAAQwI,GAAiB,CACrB,MAAMC,GAAejD,GAAe,IAAMgD,EAAe3F,GACzD0C,EAAM,KAAKkD,CAAW,CAC1C,EAEY,IAAK,OACD,MAAO,IAAM,CACTlD,EAAM,KAAI,CAC9B,EAEY,IAAK,QACD,MAAO,IAAM,CACTA,EAAM,MAAK,CAC/B,EAEY,QACI,MAAM,IAAI,MAAM,GAAG+B,CAAO,wBAAwBtH,CAAM,EAAE,CAEjE,CACJ,CACD,yBAAyBsH,EAASoB,EAAkBZ,EAAWE,EAAOD,EAAuB,CACzF,GAAIA,EAAsB,mBAAmB,QAAU,EACnD,OAAO,QAAQ,UAEnB,MAAMY,EAAmBZ,EAAsB,mBAAmB,CAAC,EAC7DL,EAAeM,EAAM,QACrBL,EAAUN,EAAU,IAAI,eAAe,KAAK,IAAI,YAAa,KAAK,UAAWK,CAAY,EAC/F,OAAO,KAAK,kBAAkBJ,EAASK,CAAO,EAAE,KAAK,IAAM,CACvD,MAAMpC,EAAQoC,EAAQ,aAAa,MACnC,GAAIpC,EAAO,CACP,MAAMqD,EAAwB,IAAI9I,EAAekI,EAAM,KAAM,KAAK,gBAAgBV,EAAS/B,EAAOyC,EAAM,OAAQA,EAAM,KAAMA,EAAM,WAAW,CAAC,EAC9IW,EAAiB,UAAU,SAASC,CAAqB,EAEzDb,EAAsB,8BAA8B,IAAI,IAAM,CAC1DxC,EAAM,KAAI,CAC9B,CAAiB,EACDwC,EAAsB,gCAAgC,IAAI,IAAM,CAC5DxC,EAAM,MAAK,CAC/B,CAAiB,CACJ,CACb,CAAS,CACJ,CACL,CACAsD,EAAwB7B,CAAI,EAC5B8B,EAAsB9B,EAAM,GAAOE,GAAW,IAAID,EAAmBC,CAAM,CAAC", "x_google_ignoreList": [0, 1, 2, 3, 4, 5]}