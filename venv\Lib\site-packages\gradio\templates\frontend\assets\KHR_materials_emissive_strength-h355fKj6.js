import{ar as a,an as m,ao as d}from"./index-Dpxo-yl_.js";import{GLTFLoader as h}from"./glTFLoader-9Z3KGax5.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./bone-kZWM5-u7.js";import"./rawTexture-DmvUfjqF.js";import"./assetContainer-BRzQBugc.js";import"./objectModelMapping-BR4RdEzn.js";const t="KHR_materials_emissive_strength";class p{constructor(e){this.name=t,this.order=170,this._loader=e,this.enabled=this._loader.isExtensionUsed(t)}dispose(){this._loader=null}loadMaterialPropertiesAsync(e,s,i){return h.LoadExtensionAsync(e,s,this.name,(o,n)=>this._loader.loadMaterialPropertiesAsync(e,s,i).then(()=>{this._loadEmissiveProperties(o,n,i)}))}_loadEmissiveProperties(e,s,i){if(!(i instanceof a))throw new Error(`${e}: Material type not supported`);s.emissiveStrength!==void 0&&(i.emissiveIntensity=s.emissiveStrength)}}m(t);d(t,!0,r=>new p(r));export{p as KHR_materials_emissive_strength};
//# sourceMappingURL=KHR_materials_emissive_strength-h355fKj6.js.map
