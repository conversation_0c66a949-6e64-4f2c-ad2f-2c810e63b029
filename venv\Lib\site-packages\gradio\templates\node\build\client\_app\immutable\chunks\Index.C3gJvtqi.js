import{SvelteComponent as z,init as A,safe_not_equal as M,empty as T,insert_hydration as B,transition_in as o,group_outros as V,transition_out as _,check_outros as F,detach as j,createEventDispatcher as W,getContext as X,component_subscribe as E,onMount as Y,tick as Z,element as y,create_component as G,claim_element as x,children as p,claim_component as H,attr as k,toggle_class as N,set_style as I,mount_component as J,destroy_component as K,create_slot as L,update_slot_base as O,get_all_dirty_from_scope as P,get_slot_changes as Q}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{a as $}from"./Tabs.DkGgrUWn.js";import{z as ee}from"./2.B2AoQPnG.js";function S(f){let i,n,e,l;return n=new ee({props:{scale:f[4]>=1?f[4]:null,$$slots:{default:[le]},$$scope:{ctx:f}}}),{c(){i=y("div"),G(n.$$.fragment),this.h()},l(t){i=x(t,"DIV",{id:!0,class:!0,role:!0});var u=p(i);H(n.$$.fragment,u),u.forEach(j),this.h()},h(){k(i,"id",f[0]),k(i,"class",e="tabitem "+f[1].join(" ")+" svelte-wv8on1"),k(i,"role","tabpanel"),N(i,"grow-children",f[4]>=1),I(i,"display",f[5]===f[2]&&f[3]?"flex":"none"),I(i,"flex-grow",f[4])},m(t,u){B(t,i,u),J(n,i,null),l=!0},p(t,u){const c={};u&16&&(c.scale=t[4]>=1?t[4]:null),u&16384&&(c.$$scope={dirty:u,ctx:t}),n.$set(c),(!l||u&1)&&k(i,"id",t[0]),(!l||u&2&&e!==(e="tabitem "+t[1].join(" ")+" svelte-wv8on1"))&&k(i,"class",e),(!l||u&18)&&N(i,"grow-children",t[4]>=1),u&44&&I(i,"display",t[5]===t[2]&&t[3]?"flex":"none"),u&16&&I(i,"flex-grow",t[4])},i(t){l||(o(n.$$.fragment,t),l=!0)},o(t){_(n.$$.fragment,t),l=!1},d(t){t&&j(i),K(n)}}}function le(f){let i;const n=f[13].default,e=L(n,f,f[14],null);return{c(){e&&e.c()},l(l){e&&e.l(l)},m(l,t){e&&e.m(l,t),i=!0},p(l,t){e&&e.p&&(!i||t&16384)&&O(e,n,l,l[14],i?Q(n,l[14],t,null):P(l[14]),null)},i(l){i||(o(e,l),i=!0)},o(l){_(e,l),i=!1},d(l){e&&e.d(l)}}}function ie(f){let i,n,e=f[5]===f[2]&&f[3]&&S(f);return{c(){e&&e.c(),i=T()},l(l){e&&e.l(l),i=T()},m(l,t){e&&e.m(l,t),B(l,i,t),n=!0},p(l,[t]){l[5]===l[2]&&l[3]?e?(e.p(l,t),t&44&&o(e,1)):(e=S(l),e.c(),o(e,1),e.m(i.parentNode,i)):e&&(V(),_(e,1,1,()=>{e=null}),F())},i(l){n||(o(e),n=!0)},o(l){_(e),n=!1},d(l){l&&j(i),e&&e.d(l)}}}function te(f,i,n){let e,l,{$$slots:t={},$$scope:u}=i,{elem_id:c=""}=i,{elem_classes:h=[]}=i,{label:r}=i,{id:m={}}=i,{visible:b}=i,{interactive:g}=i,{order:d}=i,{scale:v}=i;const s=W(),{register_tab:R,unregister_tab:U,selected_tab:C,selected_tab_index:D}=X($);E(f,C,a=>n(5,l=a)),E(f,D,a=>n(12,e=a));let w;return Y(()=>()=>U({label:r,id:m,elem_id:c},d)),f.$$set=a=>{"elem_id"in a&&n(0,c=a.elem_id),"elem_classes"in a&&n(1,h=a.elem_classes),"label"in a&&n(8,r=a.label),"id"in a&&n(2,m=a.id),"visible"in a&&n(3,b=a.visible),"interactive"in a&&n(9,g=a.interactive),"order"in a&&n(10,d=a.order),"scale"in a&&n(4,v=a.scale),"$$scope"in a&&n(14,u=a.$$scope)},f.$$.update=()=>{f.$$.dirty&1821&&n(11,w=R({label:r,id:m,elem_id:c,visible:b,interactive:g,scale:v},d)),f.$$.dirty&6400&&e===w&&Z().then(()=>s("select",{value:r,index:w}))},[c,h,m,b,v,l,C,D,r,g,d,w,e,t,u]}class ne extends z{constructor(i){super(),A(this,i,te,ie,M,{elem_id:0,elem_classes:1,label:8,id:2,visible:3,interactive:9,order:10,scale:4})}}const fe=ne;function q(f){let i;const n=f[9].default,e=L(n,f,f[11],null);return{c(){e&&e.c()},l(l){e&&e.l(l)},m(l,t){e&&e.m(l,t),i=!0},p(l,t){e&&e.p&&(!i||t&2048)&&O(e,n,l,l[11],i?Q(n,l[11],t,null):P(l[11]),null)},i(l){i||(o(e,l),i=!0)},o(l){_(e,l),i=!1},d(l){e&&e.d(l)}}}function se(f){let i,n,e=f[5]&&q(f);return{c(){e&&e.c(),i=T()},l(l){e&&e.l(l),i=T()},m(l,t){e&&e.m(l,t),B(l,i,t),n=!0},p(l,t){l[5]?e?(e.p(l,t),t&32&&o(e,1)):(e=q(l),e.c(),o(e,1),e.m(i.parentNode,i)):e&&(V(),_(e,1,1,()=>{e=null}),F())},i(l){n||(o(e),n=!0)},o(l){_(e),n=!1},d(l){l&&j(i),e&&e.d(l)}}}function ae(f){let i,n;return i=new fe({props:{elem_id:f[0],elem_classes:f[1],label:f[2],visible:f[5],interactive:f[6],id:f[3],order:f[7],scale:f[8],$$slots:{default:[se]},$$scope:{ctx:f}}}),i.$on("select",f[10]),{c(){G(i.$$.fragment)},l(e){H(i.$$.fragment,e)},m(e,l){J(i,e,l),n=!0},p(e,[l]){const t={};l&1&&(t.elem_id=e[0]),l&2&&(t.elem_classes=e[1]),l&4&&(t.label=e[2]),l&32&&(t.visible=e[5]),l&64&&(t.interactive=e[6]),l&8&&(t.id=e[3]),l&128&&(t.order=e[7]),l&256&&(t.scale=e[8]),l&2080&&(t.$$scope={dirty:l,ctx:e}),i.$set(t)},i(e){n||(o(i.$$.fragment,e),n=!0)},o(e){_(i.$$.fragment,e),n=!1},d(e){K(i,e)}}}function ue(f,i,n){let{$$slots:e={},$$scope:l}=i,{elem_id:t=""}=i,{elem_classes:u=[]}=i,{label:c}=i,{id:h}=i,{gradio:r}=i,{visible:m=!0}=i,{interactive:b=!0}=i,{order:g}=i,{scale:d}=i;const v=({detail:s})=>r==null?void 0:r.dispatch("select",s);return f.$$set=s=>{"elem_id"in s&&n(0,t=s.elem_id),"elem_classes"in s&&n(1,u=s.elem_classes),"label"in s&&n(2,c=s.label),"id"in s&&n(3,h=s.id),"gradio"in s&&n(4,r=s.gradio),"visible"in s&&n(5,m=s.visible),"interactive"in s&&n(6,b=s.interactive),"order"in s&&n(7,g=s.order),"scale"in s&&n(8,d=s.scale),"$$scope"in s&&n(11,l=s.$$scope)},[t,u,c,h,r,m,b,g,d,e,v,l]}class me extends z{constructor(i){super(),A(this,i,ue,ae,M,{elem_id:0,elem_classes:1,label:2,id:3,gradio:4,visible:5,interactive:6,order:7,scale:8})}}export{fe as BaseTabItem,me as default};
//# sourceMappingURL=Index.C3gJvtqi.js.map
