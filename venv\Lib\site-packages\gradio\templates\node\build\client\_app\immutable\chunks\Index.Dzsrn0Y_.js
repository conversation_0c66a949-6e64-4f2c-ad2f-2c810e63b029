import{SvelteComponent as I,init as j,safe_not_equal as G,create_component as M,claim_component as N,mount_component as S,transition_in as z,transition_out as k,destroy_component as A,createEventDispatcher as F,create_slot as H,space as J,claim_space as K,insert_hydration as B,update_slot_base as L,get_all_dirty_from_scope as O,get_slot_changes as P,detach as C,element as Q,claim_element as R,attr as v,src_url_equal as q,text as T,claim_text as U,set_data as V}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{o as W}from"./2.B2AoQPnG.js";function E(a){let l,i,e;return{c(){l=Q("img"),this.h()},l(n){l=R(n,"IMG",{class:!0,src:!0,alt:!0}),this.h()},h(){v(l,"class","button-icon svelte-yjn27e"),q(l.src,i=a[6].url)||v(l,"src",i),v(l,"alt",e=`${a[5]} icon`)},m(n,t){B(n,l,t)},p(n,t){t&64&&!q(l.src,i=n[6].url)&&v(l,"src",i),t&32&&e!==(e=`${n[5]} icon`)&&v(l,"alt",e)},d(n){n&&C(l)}}}function X(a){let l,i,e=a[6]&&E(a);const n=a[11].default,t=H(n,a,a[12],null);return{c(){e&&e.c(),l=J(),t&&t.c()},l(s){e&&e.l(s),l=K(s),t&&t.l(s)},m(s,c){e&&e.m(s,c),B(s,l,c),t&&t.m(s,c),i=!0},p(s,c){s[6]?e?e.p(s,c):(e=E(s),e.c(),e.m(l.parentNode,l)):e&&(e.d(1),e=null),t&&t.p&&(!i||c&4096)&&L(t,n,s,s[12],i?P(n,s[12],c,null):O(s[12]),null)},i(s){i||(z(t,s),i=!0)},o(s){k(t,s),i=!1},d(s){s&&C(l),e&&e.d(s),t&&t.d(s)}}}function Y(a){let l,i;return l=new W({props:{size:a[4],variant:a[3],elem_id:a[0],elem_classes:a[1],visible:a[2],scale:a[8],min_width:a[9],disabled:a[7],$$slots:{default:[X]},$$scope:{ctx:a}}}),l.$on("click",a[10]),{c(){M(l.$$.fragment)},l(e){N(l.$$.fragment,e)},m(e,n){S(l,e,n),i=!0},p(e,[n]){const t={};n&16&&(t.size=e[4]),n&8&&(t.variant=e[3]),n&1&&(t.elem_id=e[0]),n&2&&(t.elem_classes=e[1]),n&4&&(t.visible=e[2]),n&256&&(t.scale=e[8]),n&512&&(t.min_width=e[9]),n&128&&(t.disabled=e[7]),n&4192&&(t.$$scope={dirty:n,ctx:e}),l.$set(t)},i(e){i||(z(l.$$.fragment,e),i=!0)},o(e){k(l.$$.fragment,e),i=!1},d(e){A(l,e)}}}function Z(a,l,i){let{$$slots:e={},$$scope:n}=l,{elem_id:t=""}=l,{elem_classes:s=[]}=l,{visible:c=!0}=l,{variant:o="secondary"}=l,{size:d="lg"}=l,{value:m}=l,{icon:b}=l,{disabled:r=!1}=l,{scale:h=null}=l,{min_width:_=void 0}=l;const w=F();function u(){if(w("click"),!(m!=null&&m.url))return;let f;if(!m.orig_name&&m.url){const D=m.url.split("/");f=D[D.length-1],f=f.split("?")[0].split("#")[0]}else f=m.orig_name;const g=document.createElement("a");g.href=m.url,g.download=f||"file",document.body.appendChild(g),g.click(),document.body.removeChild(g)}return a.$$set=f=>{"elem_id"in f&&i(0,t=f.elem_id),"elem_classes"in f&&i(1,s=f.elem_classes),"visible"in f&&i(2,c=f.visible),"variant"in f&&i(3,o=f.variant),"size"in f&&i(4,d=f.size),"value"in f&&i(5,m=f.value),"icon"in f&&i(6,b=f.icon),"disabled"in f&&i(7,r=f.disabled),"scale"in f&&i(8,h=f.scale),"min_width"in f&&i(9,_=f.min_width),"$$scope"in f&&i(12,n=f.$$scope)},[t,s,c,o,d,m,b,r,h,_,u,e,n]}class y extends I{constructor(l){super(),j(this,l,Z,Y,G,{elem_id:0,elem_classes:1,visible:2,variant:3,size:4,value:5,icon:6,disabled:7,scale:8,min_width:9})}}const x=y;function p(a){let l=(a[10]??"")+"",i;return{c(){i=T(l)},l(e){i=U(e,l)},m(e,n){B(e,i,n)},p(e,n){n&1024&&l!==(l=(e[10]??"")+"")&&V(i,l)},d(e){e&&C(i)}}}function $(a){let l,i;return l=new x({props:{value:a[3],variant:a[4],elem_id:a[0],elem_classes:a[1],size:a[6],scale:a[7],icon:a[8],min_width:a[9],visible:a[2],disabled:!a[5],$$slots:{default:[p]},$$scope:{ctx:a}}}),l.$on("click",a[12]),{c(){M(l.$$.fragment)},l(e){N(l.$$.fragment,e)},m(e,n){S(l,e,n),i=!0},p(e,[n]){const t={};n&8&&(t.value=e[3]),n&16&&(t.variant=e[4]),n&1&&(t.elem_id=e[0]),n&2&&(t.elem_classes=e[1]),n&64&&(t.size=e[6]),n&128&&(t.scale=e[7]),n&256&&(t.icon=e[8]),n&512&&(t.min_width=e[9]),n&4&&(t.visible=e[2]),n&32&&(t.disabled=!e[5]),n&9216&&(t.$$scope={dirty:n,ctx:e}),l.$set(t)},i(e){i||(z(l.$$.fragment,e),i=!0)},o(e){k(l.$$.fragment,e),i=!1},d(e){A(l,e)}}}function ee(a,l,i){let{elem_id:e=""}=l,{elem_classes:n=[]}=l,{visible:t=!0}=l,{value:s}=l,{variant:c="secondary"}=l,{interactive:o}=l,{size:d="lg"}=l,{scale:m=null}=l,{icon:b=null}=l,{min_width:r=void 0}=l,{label:h=null}=l,{gradio:_}=l;const w=()=>_.dispatch("click");return a.$$set=u=>{"elem_id"in u&&i(0,e=u.elem_id),"elem_classes"in u&&i(1,n=u.elem_classes),"visible"in u&&i(2,t=u.visible),"value"in u&&i(3,s=u.value),"variant"in u&&i(4,c=u.variant),"interactive"in u&&i(5,o=u.interactive),"size"in u&&i(6,d=u.size),"scale"in u&&i(7,m=u.scale),"icon"in u&&i(8,b=u.icon),"min_width"in u&&i(9,r=u.min_width),"label"in u&&i(10,h=u.label),"gradio"in u&&i(11,_=u.gradio)},[e,n,t,s,c,o,d,m,b,r,h,_,w]}class te extends I{constructor(l){super(),j(this,l,ee,$,G,{elem_id:0,elem_classes:1,visible:2,value:3,variant:4,interactive:5,size:6,scale:7,icon:8,min_width:9,label:10,gradio:11})}}export{x as BaseButton,te as default};
//# sourceMappingURL=Index.Dzsrn0Y_.js.map
