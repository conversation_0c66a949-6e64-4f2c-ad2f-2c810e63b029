import{V as f,aq as m,C as _,T as y,an as L,ao as A}from"./index.BoI39RQH.js";import{a as w}from"./objectModelMapping.ha_8hIyl.js";import{ArrayItem as l,GLTFLoader as u}from"./glTFLoader.BetPWe9U.js";const n="EXT_lights_ies";class p{constructor(i){this.name=n,this._loader=i,this.enabled=this._loader.isExtensionUsed(n)}dispose(){this._loader=null,delete this._lights}onLoading(){const i=this._loader.gltf.extensions;if(i&&i[this.name]){const a=i[this.name];this._lights=a.lights,l.Assign(this._lights)}}loadNodeAsync(i,a,c){return u.LoadExtensionAsync(i,a,this.name,async(h,r)=>{this._loader._allMaterialsDirtyRequired=!0;let e,t;const g=await this._loader.loadNodeAsync(i,a,s=>{t=l.Get(h,this._lights,r.light);const b=t.name||s.name;this._loader.babylonScene._blockEntityCollection=!!this._loader._assetContainer,e=new w(b,f.Zero(),f.Backward(),0,1,this._loader.babylonScene),e.angle=Math.PI/2,e.innerAngle=0,e._parentContainer=this._loader._assetContainer,this._loader.babylonScene._blockEntityCollection=!1,t._babylonLight=e,e.falloffType=m.FALLOFF_GLTF,e.diffuse=r.color?_.FromArray(r.color):_.White(),e.intensity=r.multiplier||1,e.range=Number.MAX_VALUE,e.parent=s,this._loader._babylonLights.push(e),u.AddPointerMetadata(e,h),c(s)});let o;if(t.uri)o=await this._loader.loadUriAsync(i,t,t.uri);else{const s=l.Get(`${i}/bufferView`,this._loader.gltf.bufferViews,t.bufferView);o=await this._loader.loadBufferViewAsync(`/bufferViews/${s.index}`,s)}return e.iesProfileTexture=new y(name+"_iesProfile",this._loader.babylonScene,!0,!1,void 0,null,null,o,!0,void 0,void 0,void 0,void 0,".ies"),g})}}L(n);A(n,!0,d=>new p(d));export{p as EXT_lights_ies};
//# sourceMappingURL=EXT_lights_ies.TfXTlsqD.js.map
