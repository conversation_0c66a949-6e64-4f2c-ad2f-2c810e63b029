import{SvelteComponent as o,init as r,safe_not_equal as _,element as g,text as y,claim_element as v,children as b,claim_text as q,detach as d,attr as E,toggle_class as s,insert_hydration as k,append_hydration as C,set_data as D,noop as u}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function I(n){let e,i;return{c(){e=g("div"),i=y(n[2]),this.h()},l(t){e=v(t,"DIV",{class:!0});var l=b(e);i=q(l,n[2]),l.forEach(d),this.h()},h(){E(e,"class","svelte-1ayixqk"),s(e,"table",n[0]==="table"),s(e,"gallery",n[0]==="gallery"),s(e,"selected",n[1])},m(t,l){k(t,e,l),C(e,i)},p(t,[l]){l&4&&D(i,t[2]),l&1&&s(e,"table",t[0]==="table"),l&1&&s(e,"gallery",t[0]==="gallery"),l&2&&s(e,"selected",t[1])},i:u,o:u,d(t){t&&d(e)}}}function S(n,e,i){let{value:t}=e,{type:l}=e,{selected:h=!1}=e,{choices:c}=e,f;if(t===null)f="";else{let a=c.find(m=>m[1]===t);f=a?a[0]:""}return n.$$set=a=>{"value"in a&&i(3,t=a.value),"type"in a&&i(0,l=a.type),"selected"in a&&i(1,h=a.selected),"choices"in a&&i(4,c=a.choices)},[l,h,f,t,c]}class w extends o{constructor(e){super(),r(this,e,S,I,_,{value:3,type:0,selected:1,choices:4})}}export{w as default};
//# sourceMappingURL=Example.Dl9poLS6.js.map
