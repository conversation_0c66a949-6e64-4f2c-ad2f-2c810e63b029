import { m as mt, z as zA, j as Ke$1, l as Le, N as xt, g as kt, ar as Jt, al as globals } from './2-DJbI4FWc.js';
import { c as create_ssr_component, v as validate_component, e as escape, d as add_attribute } from './ssr-C3HYbsxA.js';
import './index-ClteBeTX.js';
import './Component-NmRBwSfF.js';
import 'path';
import 'url';
import 'fs';

const {Object:qe}=globals,Ke={code:"div.svelte-19qacdz{width:100%;height:100%}#vg-tooltip-element{font-family:var(--font) !important;font-size:var(--text-xs) !important;box-shadow:none !important;background-color:var(--block-background-fill) !important;border:1px solid var(--border-color-primary) !important;color:var(--body-text-color) !important}#vg-tooltip-element .key{color:var(--body-text-color-subdued) !important}.caption.svelte-19qacdz{padding:0 4px;margin:0;text-align:center}",map:'{"version":3,"file":"Index.svelte","sources":["Index.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { BlockTitle } from \\"@gradio/atoms\\";\\nimport { Block } from \\"@gradio/atoms\\";\\nimport { FullscreenButton, IconButtonWrapper } from \\"@gradio/atoms\\";\\nimport { StatusTracker } from \\"@gradio/statustracker\\";\\nimport { onMount } from \\"svelte\\";\\nimport { LineChart as LabelIcon } from \\"@gradio/icons\\";\\nimport { Empty } from \\"@gradio/atoms\\";\\nexport let value;\\nexport let x;\\nexport let y;\\nexport let color = null;\\n$: unique_colors = color && value && value.datatypes[color] === \\"nominal\\" ? Array.from(new Set(_data.map((d) => d[color]))) : [];\\nexport let title = null;\\nexport let x_title = null;\\nexport let y_title = null;\\nexport let color_title = null;\\nexport let x_bin = null;\\nexport let y_aggregate = void 0;\\nexport let color_map = null;\\nexport let x_lim = null;\\nexport let y_lim = null;\\n$: x_lim = x_lim || null;\\n$: y_lim = y_lim || null;\\n$: [x_start, x_end] = x_lim === null ? [void 0, void 0] : x_lim;\\n$: [y_start, y_end] = y_lim || [void 0, void 0];\\nexport let x_label_angle = null;\\nexport let y_label_angle = null;\\nexport let x_axis_labels_visible = true;\\nexport let caption = null;\\nexport let sort = null;\\nexport let tooltip = \\"axis\\";\\nexport let show_fullscreen_button = false;\\nlet fullscreen = false;\\nfunction reformat_sort(_sort2) {\\n    if (_sort2 === \\"x\\") {\\n        return \\"ascending\\";\\n    }\\n    else if (_sort2 === \\"-x\\") {\\n        return \\"descending\\";\\n    }\\n    else if (_sort2 === \\"y\\") {\\n        return { field: y, order: \\"ascending\\" };\\n    }\\n    else if (_sort2 === \\"-y\\") {\\n        return { field: y, order: \\"descending\\" };\\n    }\\n    else if (_sort2 === null) {\\n        return null;\\n    }\\n    else if (Array.isArray(_sort2)) {\\n        return _sort2;\\n    }\\n}\\n$: _sort = reformat_sort(sort);\\nexport let _selectable = false;\\nlet _data;\\nexport let gradio;\\n$: x_temporal = value && value.datatypes[x] === \\"temporal\\";\\n$: _x_lim = x_lim && x_temporal ? [x_lim[0] * 1e3, x_lim[1] * 1e3] : x_lim;\\nlet _x_bin;\\nlet mouse_down_on_chart = false;\\nconst SUFFIX_DURATION = {\\n    s: 1,\\n    m: 60,\\n    h: 60 * 60,\\n    d: 24 * 60 * 60\\n};\\n$: _x_bin = x_bin ? typeof x_bin === \\"string\\" ? 1e3 * parseInt(x_bin.substring(0, x_bin.length - 1)) * SUFFIX_DURATION[x_bin[x_bin.length - 1]] : x_bin : void 0;\\nlet _y_aggregate;\\nlet aggregating;\\n$: {\\n    if (value) {\\n        if (value.mark === \\"point\\") {\\n            aggregating = _x_bin !== void 0;\\n            _y_aggregate = y_aggregate || aggregating ? \\"sum\\" : void 0;\\n        }\\n        else {\\n            aggregating = _x_bin !== void 0 || value.datatypes[x] === \\"nominal\\";\\n            _y_aggregate = y_aggregate ? y_aggregate : \\"sum\\";\\n        }\\n    }\\n}\\nfunction downsample(data, x_index, y_index, color_index, x_start2, x_end2) {\\n    if (data.length < 1e3 || x_bin !== null || value?.mark !== \\"line\\" || value?.datatypes[x] === \\"nominal\\") {\\n        return data;\\n    }\\n    const bin_count = 250;\\n    let min_max_bins_per_color = {};\\n    if (x_start2 === void 0 || x_end2 === void 0) {\\n        data.forEach((row) => {\\n            let x_value = row[x_index];\\n            if (x_start2 === void 0 || x_value < x_start2) {\\n                x_start2 = x_value;\\n            }\\n            if (x_end2 === void 0 || x_value > x_end2) {\\n                x_end2 = x_value;\\n            }\\n        });\\n    }\\n    if (x_start2 === void 0 || x_end2 === void 0) {\\n        return data;\\n    }\\n    const x_range = x_end2 - x_start2;\\n    const bin_size = x_range / bin_count;\\n    data.forEach((row, i) => {\\n        const x_value = row[x_index];\\n        const y_value = row[y_index];\\n        const color_value = color_index !== null ? row[color_index] : \\"any\\";\\n        const bin_index = Math.floor((x_value - x_start2) / bin_size);\\n        if (min_max_bins_per_color[color_value] === void 0) {\\n            min_max_bins_per_color[color_value] = [];\\n        }\\n        min_max_bins_per_color[color_value][bin_index] = min_max_bins_per_color[color_value][bin_index] || [\\n            null,\\n            Number.POSITIVE_INFINITY,\\n            null,\\n            Number.NEGATIVE_INFINITY\\n        ];\\n        if (y_value < min_max_bins_per_color[color_value][bin_index][1]) {\\n            min_max_bins_per_color[color_value][bin_index][0] = i;\\n            min_max_bins_per_color[color_value][bin_index][1] = y_value;\\n        }\\n        if (y_value > min_max_bins_per_color[color_value][bin_index][3]) {\\n            min_max_bins_per_color[color_value][bin_index][2] = i;\\n            min_max_bins_per_color[color_value][bin_index][3] = y_value;\\n        }\\n    });\\n    const downsampled_data = [];\\n    Object.values(min_max_bins_per_color).forEach((bins) => {\\n        bins.forEach(([min_index, _, max_index, __]) => {\\n            let indices = [];\\n            if (min_index !== null && max_index !== null) {\\n                indices = [\\n                    Math.min(min_index, max_index),\\n                    Math.max(min_index, max_index)\\n                ];\\n            }\\n            else if (min_index !== null) {\\n                indices = [min_index];\\n            }\\n            else if (max_index !== null) {\\n                indices = [max_index];\\n            }\\n            indices.forEach((index) => {\\n                downsampled_data.push(data[index]);\\n            });\\n        });\\n    });\\n    return downsampled_data;\\n}\\nfunction reformat_data(data, x_start2, x_end2) {\\n    let x_index = data.columns.indexOf(x);\\n    let y_index = data.columns.indexOf(y);\\n    let color_index = color ? data.columns.indexOf(color) : null;\\n    let datatable = data.data;\\n    if (x_start2 !== void 0 && x_end2 !== void 0) {\\n        const time_factor = data.datatypes[x] === \\"temporal\\" ? 1e3 : 1;\\n        const _x_start = x_start2 * time_factor;\\n        const _x_end = x_end2 * time_factor;\\n        let largest_before_start = {};\\n        let smallest_after_end = {};\\n        const _datatable = datatable.filter((row, i) => {\\n            const x_value = row[x_index];\\n            const color_value = color_index !== null ? row[color_index] : \\"any\\";\\n            if (x_value < _x_start && (largest_before_start[color_value] === void 0 || x_value > largest_before_start[color_value][1])) {\\n                largest_before_start[color_value] = [i, x_value];\\n            }\\n            if (x_value > _x_end && (smallest_after_end[color_value] === void 0 || x_value < smallest_after_end[color_value][1])) {\\n                smallest_after_end[color_value] = [i, x_value];\\n            }\\n            return x_value >= _x_start && x_value <= _x_end;\\n        });\\n        datatable = [\\n            ...Object.values(largest_before_start).map(([i, _]) => datatable[i]),\\n            ...downsample(_datatable, x_index, y_index, color_index, _x_start, _x_end),\\n            ...Object.values(smallest_after_end).map(([i, _]) => datatable[i])\\n        ];\\n    }\\n    else {\\n        datatable = downsample(datatable, x_index, y_index, color_index, void 0, void 0);\\n    }\\n    if (tooltip == \\"all\\" || Array.isArray(tooltip)) {\\n        return datatable.map((row) => {\\n            const obj = {};\\n            data.columns.forEach((col, i) => {\\n                obj[col] = row[i];\\n            });\\n            return obj;\\n        });\\n    }\\n    return datatable.map((row) => {\\n        const obj = {\\n            [x]: row[x_index],\\n            [y]: row[y_index]\\n        };\\n        if (color && color_index !== null) {\\n            obj[color] = row[color_index];\\n        }\\n        return obj;\\n    });\\n}\\n$: _data = value ? reformat_data(value, x_start, x_end) : [];\\nlet old_value = value;\\n$: if (old_value !== value && view) {\\n    old_value = value;\\n    view.data(\\"data\\", _data).runAsync();\\n}\\nconst is_browser = typeof window !== \\"undefined\\";\\nlet chart_element;\\n$: computed_style = chart_element ? window.getComputedStyle(chart_element) : null;\\nlet view;\\nlet mounted = false;\\nlet old_width;\\nlet old_height;\\nlet resizeObserver;\\nlet vegaEmbed;\\nasync function load_chart() {\\n    if (mouse_down_on_chart) {\\n        refresh_pending = true;\\n        return;\\n    }\\n    if (view) {\\n        view.finalize();\\n    }\\n    if (!value || !chart_element)\\n        return;\\n    old_width = chart_element.offsetWidth;\\n    old_height = chart_element.offsetHeight;\\n    const spec = create_vega_lite_spec();\\n    if (!spec)\\n        return;\\n    resizeObserver = new ResizeObserver((el) => {\\n        if (!el[0].target || !(el[0].target instanceof HTMLElement))\\n            return;\\n        if (old_width === 0 && chart_element.offsetWidth !== 0 && value.datatypes[x] === \\"nominal\\") {\\n            load_chart();\\n        }\\n        else {\\n            view.signal(\\"width\\", el[0].target.offsetWidth).run();\\n        }\\n        if (old_height !== el[0].target.offsetHeight && fullscreen) {\\n            view.signal(\\"height\\", el[0].target.offsetHeight).run();\\n            old_height = el[0].target.offsetHeight;\\n        }\\n    });\\n    if (!vegaEmbed) {\\n        vegaEmbed = (await import(\\"vega-embed\\")).default;\\n    }\\n    vegaEmbed(chart_element, spec, { actions: false }).then(function (result) {\\n        view = result.view;\\n        resizeObserver.observe(chart_element);\\n        var debounceTimeout;\\n        var lastSelectTime = 0;\\n        view.addEventListener(\\"dblclick\\", () => {\\n            gradio.dispatch(\\"double_click\\");\\n        });\\n        chart_element.addEventListener(\\"mousedown\\", function (e) {\\n            if (e.detail > 1) {\\n                e.preventDefault();\\n            }\\n        }, false);\\n        if (_selectable) {\\n            view.addSignalListener(\\"brush\\", function (_, value2) {\\n                if (Date.now() - lastSelectTime < 1e3)\\n                    return;\\n                mouse_down_on_chart = true;\\n                if (Object.keys(value2).length === 0)\\n                    return;\\n                clearTimeout(debounceTimeout);\\n                let range = value2[Object.keys(value2)[0]];\\n                if (x_temporal) {\\n                    range = [range[0] / 1e3, range[1] / 1e3];\\n                }\\n                debounceTimeout = setTimeout(function () {\\n                    mouse_down_on_chart = false;\\n                    lastSelectTime = Date.now();\\n                    gradio.dispatch(\\"select\\", {\\n                        value: range,\\n                        index: range,\\n                        selected: true\\n                    });\\n                    if (refresh_pending) {\\n                        refresh_pending = false;\\n                        load_chart();\\n                    }\\n                }, 250);\\n            });\\n        }\\n    });\\n}\\nlet refresh_pending = false;\\nonMount(() => {\\n    mounted = true;\\n    return () => {\\n        mounted = false;\\n        if (view) {\\n            view.finalize();\\n        }\\n        if (resizeObserver) {\\n            resizeObserver.disconnect();\\n        }\\n    };\\n});\\n$: _color_map = JSON.stringify(color_map);\\n$: title, x_title, y_title, color_title, x, y, color, x_bin, _y_aggregate, _color_map, x_start, x_end, y_start, y_end, caption, sort, mounted, chart_element, fullscreen, computed_style && requestAnimationFrame(load_chart);\\nfunction create_vega_lite_spec() {\\n    if (!value || !computed_style)\\n        return null;\\n    let accent_color = computed_style.getPropertyValue(\\"--color-accent\\");\\n    let body_text_color = computed_style.getPropertyValue(\\"--body-text-color\\");\\n    let borderColorPrimary = computed_style.getPropertyValue(\\"--border-color-primary\\");\\n    let font_family = computed_style.fontFamily;\\n    let title_weight = computed_style.getPropertyValue(\\"--block-title-text-weight\\");\\n    const font_to_px_val = (font) => {\\n        return font.endsWith(\\"px\\") ? parseFloat(font.slice(0, -2)) : 12;\\n    };\\n    let text_size_md = font_to_px_val(computed_style.getPropertyValue(\\"--text-md\\"));\\n    let text_size_sm = font_to_px_val(computed_style.getPropertyValue(\\"--text-sm\\"));\\n    return {\\n        $schema: \\"https://vega.github.io/schema/vega-lite/v5.17.0.json\\",\\n        background: \\"transparent\\",\\n        config: {\\n            autosize: { type: \\"fit\\", contains: \\"padding\\" },\\n            axis: {\\n                labelFont: font_family,\\n                labelColor: body_text_color,\\n                titleFont: font_family,\\n                titleColor: body_text_color,\\n                titlePadding: 8,\\n                tickColor: borderColorPrimary,\\n                labelFontSize: text_size_sm,\\n                gridColor: borderColorPrimary,\\n                titleFontWeight: \\"normal\\",\\n                titleFontSize: text_size_sm,\\n                labelFontWeight: \\"normal\\",\\n                domain: false,\\n                labelAngle: 0\\n            },\\n            legend: {\\n                labelColor: body_text_color,\\n                labelFont: font_family,\\n                titleColor: body_text_color,\\n                titleFont: font_family,\\n                titleFontWeight: \\"normal\\",\\n                titleFontSize: text_size_sm,\\n                labelFontWeight: \\"normal\\",\\n                offset: 2\\n            },\\n            title: {\\n                color: body_text_color,\\n                font: font_family,\\n                fontSize: text_size_md,\\n                fontWeight: title_weight,\\n                anchor: \\"middle\\"\\n            },\\n            view: { stroke: borderColorPrimary },\\n            mark: {\\n                stroke: value.mark !== \\"bar\\" ? accent_color : void 0,\\n                fill: value.mark === \\"bar\\" ? accent_color : void 0,\\n                cursor: \\"crosshair\\"\\n            }\\n        },\\n        data: { name: \\"data\\" },\\n        datasets: {\\n            data: _data\\n        },\\n        layer: [\\"plot\\", ...value.mark === \\"line\\" ? [\\"hover\\"] : []].map((mode) => {\\n            return {\\n                encoding: {\\n                    size: value.mark === \\"line\\" ? mode == \\"plot\\" ? {\\n                        condition: {\\n                            empty: false,\\n                            param: \\"hoverPlot\\",\\n                            value: 3\\n                        },\\n                        value: 2\\n                    } : {\\n                        condition: { empty: false, param: \\"hover\\", value: 100 },\\n                        value: 0\\n                    } : void 0,\\n                    opacity: mode === \\"plot\\" ? void 0 : {\\n                        condition: { empty: false, param: \\"hover\\", value: 1 },\\n                        value: 0\\n                    },\\n                    x: {\\n                        axis: {\\n                            ...x_label_angle !== null && { labelAngle: x_label_angle },\\n                            labels: x_axis_labels_visible,\\n                            ticks: x_axis_labels_visible\\n                        },\\n                        field: x,\\n                        title: x_title || x,\\n                        type: value.datatypes[x],\\n                        scale: _x_lim ? { domain: _x_lim } : void 0,\\n                        bin: _x_bin ? { step: _x_bin } : void 0,\\n                        sort: _sort\\n                    },\\n                    y: {\\n                        axis: y_label_angle ? { labelAngle: y_label_angle } : {},\\n                        field: y,\\n                        title: y_title || y,\\n                        type: value.datatypes[y],\\n                        scale: {\\n                            zero: false,\\n                            domainMin: y_start ?? void 0,\\n                            domainMax: y_end ?? void 0\\n                        },\\n                        aggregate: aggregating ? _y_aggregate : void 0\\n                    },\\n                    color: color ? {\\n                        field: color,\\n                        legend: { orient: \\"bottom\\", title: color_title },\\n                        scale: value.datatypes[color] === \\"nominal\\" ? {\\n                            domain: unique_colors,\\n                            range: color_map ? unique_colors.map((c) => color_map[c]) : void 0\\n                        } : {\\n                            range: [\\n                                100,\\n                                200,\\n                                300,\\n                                400,\\n                                500,\\n                                600,\\n                                700,\\n                                800,\\n                                900\\n                            ].map((n) => computed_style.getPropertyValue(\\"--primary-\\" + n)),\\n                            interpolate: \\"hsl\\"\\n                        },\\n                        type: value.datatypes[color]\\n                    } : void 0,\\n                    tooltip: tooltip == \\"none\\" ? void 0 : [\\n                        {\\n                            field: y,\\n                            type: value.datatypes[y],\\n                            aggregate: aggregating ? _y_aggregate : void 0,\\n                            title: y_title || y\\n                        },\\n                        {\\n                            field: x,\\n                            type: value.datatypes[x],\\n                            title: x_title || x,\\n                            format: x_temporal ? \\"%Y-%m-%d %H:%M:%S\\" : void 0,\\n                            bin: _x_bin ? { step: _x_bin } : void 0\\n                        },\\n                        ...color ? [\\n                            {\\n                                field: color,\\n                                type: value.datatypes[color]\\n                            }\\n                        ] : [],\\n                        ...tooltip === \\"axis\\" ? [] : value?.columns.filter((col) => col !== x && col !== y && col !== color && (tooltip === \\"all\\" || tooltip.includes(col))).map((column) => ({\\n                            field: column,\\n                            type: value.datatypes[column]\\n                        }))\\n                    ]\\n                },\\n                strokeDash: {},\\n                mark: { clip: true, type: mode === \\"hover\\" ? \\"point\\" : value.mark },\\n                name: mode\\n            };\\n        }),\\n        // @ts-ignore\\n        params: [\\n            ...value.mark === \\"line\\" ? [\\n                {\\n                    name: \\"hoverPlot\\",\\n                    select: {\\n                        clear: \\"mouseout\\",\\n                        fields: color ? [color] : [],\\n                        nearest: true,\\n                        on: \\"mouseover\\",\\n                        type: \\"point\\"\\n                    },\\n                    views: [\\"hover\\"]\\n                },\\n                {\\n                    name: \\"hover\\",\\n                    select: {\\n                        clear: \\"mouseout\\",\\n                        nearest: true,\\n                        on: \\"mouseover\\",\\n                        type: \\"point\\"\\n                    },\\n                    views: [\\"hover\\"]\\n                }\\n            ] : [],\\n            ..._selectable ? [\\n                {\\n                    name: \\"brush\\",\\n                    select: {\\n                        encodings: [\\"x\\"],\\n                        mark: { fill: \\"gray\\", fillOpacity: 0.3, stroke: \\"none\\" },\\n                        type: \\"interval\\"\\n                    },\\n                    views: [\\"plot\\"]\\n                }\\n            ] : []\\n        ],\\n        width: chart_element.offsetWidth,\\n        height: height || fullscreen ? \\"container\\" : void 0,\\n        title: title || void 0\\n    };\\n}\\nexport let label = \\"Textbox\\";\\nexport let elem_id = \\"\\";\\nexport let elem_classes = [];\\nexport let visible = true;\\nexport let show_label;\\nexport let scale = null;\\nexport let min_width = void 0;\\nexport let loading_status = void 0;\\nexport let height = void 0;\\n<\/script>\\n\\n<Block\\n\\t{visible}\\n\\t{elem_id}\\n\\t{elem_classes}\\n\\t{scale}\\n\\t{min_width}\\n\\tallow_overflow={false}\\n\\tpadding={true}\\n\\t{height}\\n\\tbind:fullscreen\\n>\\n\\t{#if loading_status}\\n\\t\\t<StatusTracker\\n\\t\\t\\tautoscroll={gradio.autoscroll}\\n\\t\\t\\ti18n={gradio.i18n}\\n\\t\\t\\t{...loading_status}\\n\\t\\t\\ton:clear_status={() => gradio.dispatch(\\"clear_status\\", loading_status)}\\n\\t\\t/>\\n\\t{/if}\\n\\t{#if show_fullscreen_button}\\n\\t\\t<IconButtonWrapper>\\n\\t\\t\\t<FullscreenButton\\n\\t\\t\\t\\t{fullscreen}\\n\\t\\t\\t\\ton:fullscreen={({ detail }) => {\\n\\t\\t\\t\\t\\tfullscreen = detail;\\n\\t\\t\\t\\t}}\\n\\t\\t\\t/>\\n\\t\\t</IconButtonWrapper>\\n\\t{/if}\\n\\t<BlockTitle {show_label} info={undefined}>{label}</BlockTitle>\\n\\n\\t{#if value && is_browser}\\n\\t\\t<div bind:this={chart_element}></div>\\n\\n\\t\\t{#if caption}\\n\\t\\t\\t<p class=\\"caption\\">{caption}</p>\\n\\t\\t{/if}\\n\\t{:else}\\n\\t\\t<Empty unpadded_box={true}><LabelIcon /></Empty>\\n\\t{/if}\\n</Block>\\n\\n<style>\\n\\tdiv {\\n\\t\\twidth: 100%;\\n\\t\\theight: 100%;\\n\\t}\\n\\t:global(#vg-tooltip-element) {\\n\\t\\tfont-family: var(--font) !important;\\n\\t\\tfont-size: var(--text-xs) !important;\\n\\t\\tbox-shadow: none !important;\\n\\t\\tbackground-color: var(--block-background-fill) !important;\\n\\t\\tborder: 1px solid var(--border-color-primary) !important;\\n\\t\\tcolor: var(--body-text-color) !important;\\n\\t}\\n\\t:global(#vg-tooltip-element .key) {\\n\\t\\tcolor: var(--body-text-color-subdued) !important;\\n\\t}\\n\\t.caption {\\n\\t\\tpadding: 0 4px;\\n\\t\\tmargin: 0;\\n\\t\\ttext-align: center;\\n\\t}</style>\\n"],"names":[],"mappings":"AA8iBC,kBAAI,CACH,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CACQ,mBAAqB,CAC5B,WAAW,CAAE,IAAI,MAAM,CAAC,CAAC,UAAU,CACnC,SAAS,CAAE,IAAI,SAAS,CAAC,CAAC,UAAU,CACpC,UAAU,CAAE,IAAI,CAAC,UAAU,CAC3B,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAAC,UAAU,CACzD,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAAC,UAAU,CACxD,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAAC,UAC/B,CACQ,wBAA0B,CACjC,KAAK,CAAE,IAAI,yBAAyB,CAAC,CAAC,UACvC,CACA,uBAAS,CACR,OAAO,CAAE,CAAC,CAAC,GAAG,CACd,MAAM,CAAE,CAAC,CACT,UAAU,CAAE,MACb"}'},Qe=create_ssr_component((w,e,t,Ge)=>{let Q,xe,ge,ye,pe,be,U,Z,b,{value:n}=e,{x:f}=e,{y}=e,{color:d=null}=e,{title:$=null}=e,{x_title:N=null}=e,{y_title:V=null}=e,{color_title:ee=null}=e,{x_bin:C=null}=e,{y_aggregate:S=void 0}=e,{color_map:P=null}=e,{x_lim:k=null}=e,{y_lim:W=null}=e,{x_label_angle:L=null}=e,{y_label_angle:D=null}=e,{x_axis_labels_visible:H=!0}=e,{caption:q=null}=e,{sort:te=null}=e,{tooltip:F="axis"}=e,{show_fullscreen_button:ne=!1}=e,M=!1;function Ie(l){if(l==="x")return "ascending";if(l==="-x")return "descending";if(l==="y")return {field:y,order:"ascending"};if(l==="-y")return {field:y,order:"descending"};if(l===null)return null;if(Array.isArray(l))return l}let{_selectable:K=!1}=e,G,{gradio:T}=e,B,le=!1;const Ee={s:1,m:60,h:60*60,d:24*60*60};let R,j;function he(l,a,A,v,m,i){if(l.length<1e3||C!==null||n?.mark!=="line"||n?.datatypes[f]==="nominal")return l;const c=250;let o={};if((m===void 0||i===void 0)&&l.forEach(p=>{let x=p[a];(m===void 0||x<m)&&(m=x),(i===void 0||x>i)&&(i=x);}),m===void 0||i===void 0)return l;const s=(i-m)/c;l.forEach((p,x)=>{const z=p[a],g=p[A],r=v!==null?p[v]:"any",_=Math.floor((z-m)/s);o[r]===void 0&&(o[r]=[]),o[r][_]=o[r][_]||[null,Number.POSITIVE_INFINITY,null,Number.NEGATIVE_INFINITY],g<o[r][_][1]&&(o[r][_][0]=x,o[r][_][1]=g),g>o[r][_][3]&&(o[r][_][2]=x,o[r][_][3]=g);});const E=[];return Object.values(o).forEach(p=>{p.forEach(([x,z,g,r])=>{let _=[];x!==null&&g!==null?_=[Math.min(x,g),Math.max(x,g)]:x!==null?_=[x]:g!==null&&(_=[g]),_.forEach(Te=>{E.push(l[Te]);});});}),E}function ze(l,a,A){let v=l.columns.indexOf(f),m=l.columns.indexOf(y),i=d?l.columns.indexOf(d):null,c=l.data;if(a!==void 0&&A!==void 0){const o=l.datatypes[f]==="temporal"?1e3:1,u=a*o,s=A*o;let E={},p={};const x=c.filter((z,g)=>{const r=z[v],_=i!==null?z[i]:"any";return r<u&&(E[_]===void 0||r>E[_][1])&&(E[_]=[g,r]),r>s&&(p[_]===void 0||r<p[_][1])&&(p[_]=[g,r]),r>=u&&r<=s});c=[...Object.values(E).map(([z,g])=>c[z]),...he(x,v,m,i,u,s),...Object.values(p).map(([z,g])=>c[z])];}else c=he(c,v,m,i,void 0,void 0);return F=="all"||Array.isArray(F)?c.map(o=>{const u={};return l.columns.forEach((s,E)=>{u[s]=o[E];}),u}):c.map(o=>{const u={[f]:o[v],[y]:o[m]};return d&&i!==null&&(u[d]=o[i]),u})}let Ae=n;const Fe=typeof window<"u";let I,h,we,oe,Y,ae;async function ie(){if(le){re=!0;return}if(h&&h.finalize(),!n||!I)return;we=I.offsetWidth,oe=I.offsetHeight;const l=Be();l&&(Y=new ResizeObserver(a=>{!a[0].target||!(a[0].target instanceof HTMLElement)||(we===0&&I.offsetWidth!==0&&n.datatypes[f]==="nominal"?ie():h.signal("width",a[0].target.offsetWidth).run(),oe!==a[0].target.offsetHeight&&M&&(h.signal("height",a[0].target.offsetHeight).run(),oe=a[0].target.offsetHeight));}),ae||(ae=(await import('./vega-embed.module--sbAW075.js')).default),ae(I,l,{actions:!1}).then(function(a){h=a.view,Y.observe(I);var A,v=0;h.addEventListener("dblclick",()=>{T.dispatch("double_click");}),I.addEventListener("mousedown",function(m){m.detail>1&&m.preventDefault();},!1),K&&h.addSignalListener("brush",function(m,i){if(Date.now()-v<1e3||(le=!0,Object.keys(i).length===0))return;clearTimeout(A);let c=i[Object.keys(i)[0]];U&&(c=[c[0]/1e3,c[1]/1e3]),A=setTimeout(function(){le=!1,v=Date.now(),T.dispatch("select",{value:c,index:c,selected:!0}),re&&(re=!1,ie());},250);});}));}let re=!1;function Be(){if(!n||!b)return null;let l=b.getPropertyValue("--color-accent"),a=b.getPropertyValue("--body-text-color"),A=b.getPropertyValue("--border-color-primary"),v=b.fontFamily,m=b.getPropertyValue("--block-title-text-weight");const i=u=>u.endsWith("px")?parseFloat(u.slice(0,-2)):12;let c=i(b.getPropertyValue("--text-md")),o=i(b.getPropertyValue("--text-sm"));return {$schema:"https://vega.github.io/schema/vega-lite/v5.17.0.json",background:"transparent",config:{autosize:{type:"fit",contains:"padding"},axis:{labelFont:v,labelColor:a,titleFont:v,titleColor:a,titlePadding:8,tickColor:A,labelFontSize:o,gridColor:A,titleFontWeight:"normal",titleFontSize:o,labelFontWeight:"normal",domain:!1,labelAngle:0},legend:{labelColor:a,labelFont:v,titleColor:a,titleFont:v,titleFontWeight:"normal",titleFontSize:o,labelFontWeight:"normal",offset:2},title:{color:a,font:v,fontSize:c,fontWeight:m,anchor:"middle"},view:{stroke:A},mark:{stroke:n.mark!=="bar"?l:void 0,fill:n.mark==="bar"?l:void 0,cursor:"crosshair"}},data:{name:"data"},datasets:{data:G},layer:["plot",...n.mark==="line"?["hover"]:[]].map(u=>({encoding:{size:n.mark==="line"?u=="plot"?{condition:{empty:!1,param:"hoverPlot",value:3},value:2}:{condition:{empty:!1,param:"hover",value:100},value:0}:void 0,opacity:u==="plot"?void 0:{condition:{empty:!1,param:"hover",value:1},value:0},x:{axis:{...L!==null&&{labelAngle:L},labels:H,ticks:H},field:f,title:N||f,type:n.datatypes[f],scale:Z?{domain:Z}:void 0,bin:B?{step:B}:void 0,sort:be},y:{axis:D?{labelAngle:D}:{},field:y,title:V||y,type:n.datatypes[y],scale:{zero:!1,domainMin:ye??void 0,domainMax:pe??void 0},aggregate:j?R:void 0},color:d?{field:d,legend:{orient:"bottom",title:ee},scale:n.datatypes[d]==="nominal"?{domain:Q,range:P?Q.map(s=>P[s]):void 0}:{range:[100,200,300,400,500,600,700,800,900].map(s=>b.getPropertyValue("--primary-"+s)),interpolate:"hsl"},type:n.datatypes[d]}:void 0,tooltip:F=="none"?void 0:[{field:y,type:n.datatypes[y],aggregate:j?R:void 0,title:V||y},{field:f,type:n.datatypes[f],title:N||f,format:U?"%Y-%m-%d %H:%M:%S":void 0,bin:B?{step:B}:void 0},...d?[{field:d,type:n.datatypes[d]}]:[],...F==="axis"?[]:n?.columns.filter(s=>s!==f&&s!==y&&s!==d&&(F==="all"||F.includes(s))).map(s=>({field:s,type:n.datatypes[s]}))]},strokeDash:{},mark:{clip:!0,type:u==="hover"?"point":n.mark},name:u})),params:[...n.mark==="line"?[{name:"hoverPlot",select:{clear:"mouseout",fields:d?[d]:[],nearest:!0,on:"mouseover",type:"point"},views:["hover"]},{name:"hover",select:{clear:"mouseout",nearest:!0,on:"mouseover",type:"point"},views:["hover"]}]:[],...K?[{name:"brush",select:{encodings:["x"],mark:{fill:"gray",fillOpacity:.3,stroke:"none"},type:"interval"},views:["plot"]}]:[]],width:I.offsetWidth,height:J||M?"container":void 0,title:$||void 0}}let{label:_e="Textbox"}=e,{elem_id:de=""}=e,{elem_classes:se=[]}=e,{visible:ce=!0}=e,{show_label:ue}=e,{scale:me=null}=e,{min_width:fe=void 0}=e,{loading_status:X=void 0}=e,{height:J=void 0}=e;e.value===void 0&&t.value&&n!==void 0&&t.value(n),e.x===void 0&&t.x&&f!==void 0&&t.x(f),e.y===void 0&&t.y&&y!==void 0&&t.y(y),e.color===void 0&&t.color&&d!==void 0&&t.color(d),e.title===void 0&&t.title&&$!==void 0&&t.title($),e.x_title===void 0&&t.x_title&&N!==void 0&&t.x_title(N),e.y_title===void 0&&t.y_title&&V!==void 0&&t.y_title(V),e.color_title===void 0&&t.color_title&&ee!==void 0&&t.color_title(ee),e.x_bin===void 0&&t.x_bin&&C!==void 0&&t.x_bin(C),e.y_aggregate===void 0&&t.y_aggregate&&S!==void 0&&t.y_aggregate(S),e.color_map===void 0&&t.color_map&&P!==void 0&&t.color_map(P),e.x_lim===void 0&&t.x_lim&&k!==void 0&&t.x_lim(k),e.y_lim===void 0&&t.y_lim&&W!==void 0&&t.y_lim(W),e.x_label_angle===void 0&&t.x_label_angle&&L!==void 0&&t.x_label_angle(L),e.y_label_angle===void 0&&t.y_label_angle&&D!==void 0&&t.y_label_angle(D),e.x_axis_labels_visible===void 0&&t.x_axis_labels_visible&&H!==void 0&&t.x_axis_labels_visible(H),e.caption===void 0&&t.caption&&q!==void 0&&t.caption(q),e.sort===void 0&&t.sort&&te!==void 0&&t.sort(te),e.tooltip===void 0&&t.tooltip&&F!==void 0&&t.tooltip(F),e.show_fullscreen_button===void 0&&t.show_fullscreen_button&&ne!==void 0&&t.show_fullscreen_button(ne),e._selectable===void 0&&t._selectable&&K!==void 0&&t._selectable(K),e.gradio===void 0&&t.gradio&&T!==void 0&&t.gradio(T),e.label===void 0&&t.label&&_e!==void 0&&t.label(_e),e.elem_id===void 0&&t.elem_id&&de!==void 0&&t.elem_id(de),e.elem_classes===void 0&&t.elem_classes&&se!==void 0&&t.elem_classes(se),e.visible===void 0&&t.visible&&ce!==void 0&&t.visible(ce),e.show_label===void 0&&t.show_label&&ue!==void 0&&t.show_label(ue),e.scale===void 0&&t.scale&&me!==void 0&&t.scale(me),e.min_width===void 0&&t.min_width&&fe!==void 0&&t.min_width(fe),e.loading_status===void 0&&t.loading_status&&X!==void 0&&t.loading_status(X),e.height===void 0&&t.height&&J!==void 0&&t.height(J),w.css.add(Ke);let ve,Ce,Oe=w.head;do ve=!0,w.head=Oe,k=k||null,[xe,ge]=k===null?[void 0,void 0]:k,G=n?ze(n,xe,ge):[],Q=d&&n&&n.datatypes[d]==="nominal"?Array.from(new Set(G.map(l=>l[d]))):[],W=W||null,[ye,pe]=W||[void 0,void 0],be=Ie(te),U=n&&n.datatypes[f]==="temporal",Z=k&&U?[k[0]*1e3,k[1]*1e3]:k,B=C?typeof C=="string"?1e3*parseInt(C.substring(0,C.length-1))*Ee[C[C.length-1]]:C:void 0,n&&(n.mark==="point"?(j=B!==void 0,R=S||j?"sum":void 0):(j=B!==void 0||n.datatypes[f]==="nominal",R=S||"sum")),Ae!==n&&h&&(Ae=n,h.data("data",G).runAsync()),b=null,JSON.stringify(P),b&&requestAnimationFrame(ie),Ce=`${validate_component(mt,"Block").$$render(w,{visible:ce,elem_id:de,elem_classes:se,scale:me,min_width:fe,allow_overflow:!1,padding:!0,height:J,fullscreen:M},{fullscreen:l=>{M=l,ve=!1;}},{default:()=>`${X?`${validate_component(zA,"StatusTracker").$$render(w,qe.assign({},{autoscroll:T.autoscroll},{i18n:T.i18n},X),{},{})}`:""} ${ne?`${validate_component(Ke$1,"IconButtonWrapper").$$render(w,{},{},{default:()=>`${validate_component(Le,"FullscreenButton").$$render(w,{fullscreen:M},{},{})}`})}`:""} ${validate_component(xt,"BlockTitle").$$render(w,{show_label:ue,info:void 0},{},{default:()=>`${escape(_e)}`})} ${n&&Fe?`<div class="svelte-19qacdz"${add_attribute("this",I,0)}></div> ${q?`<p class="caption svelte-19qacdz">${escape(q)}</p>`:""}`:`${validate_component(kt,"Empty").$$render(w,{unpadded_box:!0},{},{default:()=>`${validate_component(Jt,"LabelIcon").$$render(w,{},{},{})}`})}`}`})}`;while(!ve);return Ce});

export { Qe as default };
//# sourceMappingURL=Index27-Sq9NX1MW.js.map
