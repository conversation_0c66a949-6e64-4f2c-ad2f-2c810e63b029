import { c as create_ssr_component, v as validate_component, e as escape, b as createEventDispatcher, d as add_attribute } from './ssr-C3HYbsxA.js';
import { ah as E } from './2-DJbI4FWc.js';
import './index-ClteBeTX.js';
import './Component-NmRBwSfF.js';
import 'path';
import 'url';
import 'fs';

const S={code:".hide.svelte-1rvxzzt{display:none}.button-icon.svelte-1rvxzzt{width:var(--text-xl);height:var(--text-xl);margin-right:var(--spacing-xl)}",map:'{"version":3,"file":"UploadButton.svelte","sources":["UploadButton.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { tick, createEventDispatcher } from \\"svelte\\";\\nimport { BaseButton } from \\"@gradio/button\\";\\nimport { prepare_files } from \\"@gradio/client\\";\\nexport let elem_id = \\"\\";\\nexport let elem_classes = [];\\nexport let visible = true;\\nexport let label;\\nexport let value;\\nexport let file_count;\\nexport let file_types = [];\\nexport let root;\\nexport let size = \\"lg\\";\\nexport let icon = null;\\nexport let scale = null;\\nexport let min_width = void 0;\\nexport let variant = \\"secondary\\";\\nexport let disabled = false;\\nexport let max_file_size = null;\\nexport let upload;\\nconst dispatch = createEventDispatcher();\\nlet hidden_upload;\\nlet accept_file_types;\\nif (file_types == null) {\\n    accept_file_types = null;\\n}\\nelse {\\n    file_types = file_types.map((x) => {\\n        if (x.startsWith(\\".\\")) {\\n            return x;\\n        }\\n        return x + \\"/*\\";\\n    });\\n    accept_file_types = file_types.join(\\", \\");\\n}\\nfunction open_file_upload() {\\n    dispatch(\\"click\\");\\n    hidden_upload.click();\\n}\\nasync function load_files(files) {\\n    let _files = Array.from(files);\\n    if (!files.length) {\\n        return;\\n    }\\n    if (file_count === \\"single\\") {\\n        _files = [files[0]];\\n    }\\n    let all_file_data = await prepare_files(_files);\\n    await tick();\\n    try {\\n        all_file_data = (await upload(all_file_data, root, void 0, max_file_size ?? Infinity))?.filter((x) => x !== null);\\n    }\\n    catch (e) {\\n        dispatch(\\"error\\", e.message);\\n        return;\\n    }\\n    value = file_count === \\"single\\" ? all_file_data?.[0] : all_file_data;\\n    dispatch(\\"change\\", value);\\n    dispatch(\\"upload\\", value);\\n}\\nasync function load_files_from_upload(e) {\\n    const target = e.target;\\n    if (!target.files)\\n        return;\\n    await load_files(target.files);\\n}\\nfunction clear_input_value(e) {\\n    const target = e.target;\\n    if (target.value)\\n        target.value = \\"\\";\\n}\\n<\/script>\\n\\n<input\\n\\tclass=\\"hide\\"\\n\\taccept={accept_file_types}\\n\\ttype=\\"file\\"\\n\\tbind:this={hidden_upload}\\n\\ton:change={load_files_from_upload}\\n\\ton:click={clear_input_value}\\n\\tmultiple={file_count === \\"multiple\\" || undefined}\\n\\twebkitdirectory={file_count === \\"directory\\" || undefined}\\n\\tmozdirectory={file_count === \\"directory\\" || undefined}\\n\\tdata-testid=\\"{label}-upload-button\\"\\n/>\\n\\n<BaseButton\\n\\t{size}\\n\\t{variant}\\n\\t{elem_id}\\n\\t{elem_classes}\\n\\t{visible}\\n\\ton:click={open_file_upload}\\n\\t{scale}\\n\\t{min_width}\\n\\t{disabled}\\n>\\n\\t{#if icon}\\n\\t\\t<img class=\\"button-icon\\" src={icon.url} alt={`${value} icon`} />\\n\\t{/if}\\n\\t<slot />\\n</BaseButton>\\n\\n<style>\\n\\t.hide {\\n\\t\\tdisplay: none;\\n\\t}\\n\\t.button-icon {\\n\\t\\twidth: var(--text-xl);\\n\\t\\theight: var(--text-xl);\\n\\t\\tmargin-right: var(--spacing-xl);\\n\\t}</style>\\n"],"names":[],"mappings":"AAuGC,oBAAM,CACL,OAAO,CAAE,IACV,CACA,2BAAa,CACZ,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,MAAM,CAAE,IAAI,SAAS,CAAC,CACtB,YAAY,CAAE,IAAI,YAAY,CAC/B"}'},Y=create_ssr_component((z,e,t,B)=>{let{elem_id:s=""}=e,{elem_classes:n=[]}=e,{visible:c=!0}=e,{label:d}=e,{value:a}=e,{file_count:i}=e,{file_types:l=[]}=e,{root:A}=e,{size:f="lg"}=e,{icon:o=null}=e,{scale:v=null}=e,{min_width:u=void 0}=e,{variant:_="secondary"}=e,{disabled:r=!1}=e,{max_file_size:m=null}=e,{upload:x}=e;createEventDispatcher();let h;return l==null?h=null:(l=l.map(C=>C.startsWith(".")?C:C+"/*"),h=l.join(", ")),e.elem_id===void 0&&t.elem_id&&s!==void 0&&t.elem_id(s),e.elem_classes===void 0&&t.elem_classes&&n!==void 0&&t.elem_classes(n),e.visible===void 0&&t.visible&&c!==void 0&&t.visible(c),e.label===void 0&&t.label&&d!==void 0&&t.label(d),e.value===void 0&&t.value&&a!==void 0&&t.value(a),e.file_count===void 0&&t.file_count&&i!==void 0&&t.file_count(i),e.file_types===void 0&&t.file_types&&l!==void 0&&t.file_types(l),e.root===void 0&&t.root&&A!==void 0&&t.root(A),e.size===void 0&&t.size&&f!==void 0&&t.size(f),e.icon===void 0&&t.icon&&o!==void 0&&t.icon(o),e.scale===void 0&&t.scale&&v!==void 0&&t.scale(v),e.min_width===void 0&&t.min_width&&u!==void 0&&t.min_width(u),e.variant===void 0&&t.variant&&_!==void 0&&t.variant(_),e.disabled===void 0&&t.disabled&&r!==void 0&&t.disabled(r),e.max_file_size===void 0&&t.max_file_size&&m!==void 0&&t.max_file_size(m),e.upload===void 0&&t.upload&&x!==void 0&&t.upload(x),z.css.add(S),`<input class="hide svelte-1rvxzzt"${add_attribute("accept",h,0)} type="file" ${i==="multiple"?"multiple":""}${add_attribute("webkitdirectory",i==="directory"||void 0,0)}${add_attribute("mozdirectory",i==="directory"||void 0,0)} data-testid="${escape(d,!0)+"-upload-button"}"> ${validate_component(E,"BaseButton").$$render(z,{size:f,variant:_,elem_id:s,elem_classes:n,visible:c,scale:v,min_width:u,disabled:r},{},{default:()=>`${o?`<img class="button-icon svelte-1rvxzzt"${add_attribute("src",o.url,0)}${add_attribute("alt",`${a} icon`,0)}>`:""} ${B.default?B.default({}):""}`})}`}),D=Y,W=create_ssr_component((z,e,t,B)=>{let s,{elem_id:n=""}=e,{elem_classes:c=[]}=e,{visible:d=!0}=e,{label:a}=e,{value:i}=e,{file_count:l}=e,{file_types:A=[]}=e,{root:f}=e,{size:o="lg"}=e,{scale:v=null}=e,{icon:u=null}=e,{min_width:_=void 0}=e,{variant:r="secondary"}=e,{gradio:m}=e,{interactive:x}=e;return e.elem_id===void 0&&t.elem_id&&n!==void 0&&t.elem_id(n),e.elem_classes===void 0&&t.elem_classes&&c!==void 0&&t.elem_classes(c),e.visible===void 0&&t.visible&&d!==void 0&&t.visible(d),e.label===void 0&&t.label&&a!==void 0&&t.label(a),e.value===void 0&&t.value&&i!==void 0&&t.value(i),e.file_count===void 0&&t.file_count&&l!==void 0&&t.file_count(l),e.file_types===void 0&&t.file_types&&A!==void 0&&t.file_types(A),e.root===void 0&&t.root&&f!==void 0&&t.root(f),e.size===void 0&&t.size&&o!==void 0&&t.size(o),e.scale===void 0&&t.scale&&v!==void 0&&t.scale(v),e.icon===void 0&&t.icon&&u!==void 0&&t.icon(u),e.min_width===void 0&&t.min_width&&_!==void 0&&t.min_width(_),e.variant===void 0&&t.variant&&r!==void 0&&t.variant(r),e.gradio===void 0&&t.gradio&&m!==void 0&&t.gradio(m),e.interactive===void 0&&t.interactive&&x!==void 0&&t.interactive(x),s=!x,`${validate_component(D,"UploadButton").$$render(z,{elem_id:n,elem_classes:c,visible:d,file_count:l,file_types:A,size:o,scale:v,icon:u,min_width:_,root:f,value:i,disabled:s,variant:r,label:a,max_file_size:m.max_file_size,upload:(...h)=>m.client.upload(...h)},{},{default:()=>`${escape(a??"")}`})}`});

export { D as BaseUploadButton, W as default };
//# sourceMappingURL=Index47-BF3qkUeE.js.map
