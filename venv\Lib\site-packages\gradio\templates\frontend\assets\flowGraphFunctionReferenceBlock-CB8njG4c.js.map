{"version": 3, "file": "flowGraphFunctionReferenceBlock-CB8njG4c.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Utils/flowGraphFunctionReferenceBlock.js"], "sourcesContent": ["import { FlowGraphBlock } from \"../../../flowGraphBlock.js\";\nimport { RichTypeAny, RichTypeString } from \"../../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\n/**\n * A flow graph block that takes a function name, an object and an optional context as inputs and calls the function on the object.\n */\nexport class FlowGraphFunctionReferenceBlock extends FlowGraphBlock {\n    constructor(\n    /**\n     * the configuration of the block\n     */\n    config) {\n        super(config);\n        this.functionName = this.registerDataInput(\"functionName\", RichTypeString);\n        this.object = this.registerDataInput(\"object\", RichTypeAny);\n        this.context = this.registerDataInput(\"context\", RichTypeAny, null);\n        this.output = this.registerDataOutput(\"output\", RichTypeAny);\n    }\n    _updateOutputs(context) {\n        const functionName = this.functionName.getValue(context);\n        const object = this.object.getValue(context);\n        const contextValue = this.context.getValue(context);\n        if (object && functionName) {\n            const func = object[functionName];\n            if (func && typeof func === \"function\") {\n                this.output.setValue(func.bind(contextValue), context);\n            }\n        }\n    }\n    getClassName() {\n        return \"FlowGraphFunctionReference\" /* FlowGraphBlockNames.FunctionReference */;\n    }\n}\nRegisterClass(\"FlowGraphFunctionReference\" /* FlowGraphBlockNames.FunctionReference */, FlowGraphFunctionReferenceBlock);\n//# sourceMappingURL=flowGraphFunctionReferenceBlock.js.map"], "names": ["FlowGraphFunctionReferenceBlock", "FlowGraphBlock", "config", "RichTypeString", "RichTypeAny", "context", "functionName", "object", "contextValue", "func", "RegisterClass"], "mappings": "uPAMO,MAAMA,UAAwCC,CAAe,CAChE,YAIAC,EAAQ,CACJ,MAAMA,CAAM,EACZ,KAAK,aAAe,KAAK,kBAAkB,eAAgBC,CAAc,EACzE,KAAK,OAAS,KAAK,kBAAkB,SAAUC,CAAW,EAC1D,KAAK,QAAU,KAAK,kBAAkB,UAAWA,EAAa,IAAI,EAClE,KAAK,OAAS,KAAK,mBAAmB,SAAUA,CAAW,CAC9D,CACD,eAAeC,EAAS,CACpB,MAAMC,EAAe,KAAK,aAAa,SAASD,CAAO,EACjDE,EAAS,KAAK,OAAO,SAASF,CAAO,EACrCG,EAAe,KAAK,QAAQ,SAASH,CAAO,EAClD,GAAIE,GAAUD,EAAc,CACxB,MAAMG,EAAOF,EAAOD,CAAY,EAC5BG,GAAQ,OAAOA,GAAS,YACxB,KAAK,OAAO,SAASA,EAAK,KAAKD,CAAY,EAAGH,CAAO,CAE5D,CACJ,CACD,cAAe,CACX,MAAO,4BACV,CACL,CACAK,EAAc,6BAA0EV,CAA+B", "x_google_ignoreList": [0]}