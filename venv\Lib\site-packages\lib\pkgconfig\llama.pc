prefix=C:/Users/<USER>/AppData/Local/Temp/tmp9tx0cqol/wheel/platlib
exec_prefix=C:/Users/<USER>/AppData/Local/Temp/tmp9tx0cqol/wheel/platlib
libdir=C:/Users/<USER>/AppData/Local/Temp/tmp9tx0cqol/wheel/platlib/lib
includedir=C:/Users/<USER>/AppData/Local/Temp/tmp9tx0cqol/wheel/platlib/include

Name: llama
Description: Port of Facebook's LLaMA model in C/C++
Version: 0.0.1
Libs: -L${libdir} -lggml -lggml-base -lllama
Cflags: -I${includedir}
