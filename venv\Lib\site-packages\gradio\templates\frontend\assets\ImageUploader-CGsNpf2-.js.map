{"version": 3, "file": "ImageUploader-CGsNpf2-.js", "sources": ["../../../../js/icons/src/Camera.svelte", "../../../../js/icons/src/Circle.svelte", "../../../../js/image/shared/WebcamPermissions.svelte", "../../../../js/image/shared/stream_utils.ts", "../../../../js/image/shared/Webcam.svelte", "../../../../js/image/shared/ImageUploader.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-camera\"\n>\n\t<path\n\t\td=\"M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z\"\n\t/>\n\t<circle cx=\"12\" cy=\"13\" r=\"4\" />\n</svg>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-circle\"\n>\n\t<circle cx=\"12\" cy=\"12\" r=\"10\" />\n</svg>\n", "<script lang=\"ts\">\n\timport { Webcam } from \"@gradio/icons\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\tconst dispatch = createEventDispatcher<{\n\t\tclick: undefined;\n\t}>();\n</script>\n\n<button style:height=\"100%\" on:click={() => dispatch(\"click\")}>\n\t<div class=\"wrap\">\n\t\t<span class=\"icon-wrap\">\n\t\t\t<Webcam />\n\t\t</span>\n\t\t{\"Click to Access Webcam\"}\n\t</div>\n</button>\n\n<style>\n\tbutton {\n\t\tcursor: pointer;\n\t\twidth: var(--size-full);\n\t}\n\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmin-height: var(--size-60);\n\t\tcolor: var(--block-label-text-color);\n\t\theight: 100%;\n\t\tpadding-top: var(--size-3);\n\t}\n\n\t.icon-wrap {\n\t\twidth: 30px;\n\t\tmargin-bottom: var(--spacing-lg);\n\t}\n\n\t@media (--screen-md) {\n\t\t.wrap {\n\t\t\tfont-size: var(--text-lg);\n\t\t}\n\t}\n</style>\n", "export function get_devices(): Promise<MediaDeviceInfo[]> {\n\treturn navigator.mediaDevices.enumerateDevices();\n}\n\nexport function handle_error(error: string): void {\n\tthrow new Error(error);\n}\n\nexport function set_local_stream(\n\tlocal_stream: MediaStream | null,\n\tvideo_source: HTMLVideoElement\n): void {\n\tvideo_source.srcObject = local_stream;\n\tvideo_source.muted = true;\n\tvideo_source.play();\n}\n\nexport async function get_video_stream(\n\tinclude_audio: boolean,\n\tvideo_source: HTMLVideoElement,\n\twebcam_constraints: { [key: string]: any } | null,\n\tdevice_id?: string\n): Promise<MediaStream> {\n\tconst constraints: MediaStreamConstraints = {\n\t\tvideo: device_id\n\t\t\t? { deviceId: { exact: device_id }, ...webcam_constraints?.video }\n\t\t\t: webcam_constraints?.video || {\n\t\t\t\t\twidth: { ideal: 1920 },\n\t\t\t\t\theight: { ideal: 1440 }\n\t\t\t\t},\n\t\taudio: include_audio && (webcam_constraints?.audio ?? true) // Defaults to true if not specified\n\t};\n\n\treturn navigator.mediaDevices\n\t\t.getUserMedia(constraints)\n\t\t.then((local_stream: MediaStream) => {\n\t\t\tset_local_stream(local_stream, video_source);\n\t\t\treturn local_stream;\n\t\t});\n}\n\nexport function set_available_devices(\n\tdevices: MediaDeviceInfo[]\n): MediaDeviceInfo[] {\n\tconst cameras = devices.filter(\n\t\t(device: MediaDeviceInfo) => device.kind === \"videoinput\"\n\t);\n\n\treturn cameras;\n}\n", "<script lang=\"ts\">\n\timport { createE<PERSON><PERSON><PERSON><PERSON>tch<PERSON>, on<PERSON><PERSON><PERSON>, onMount } from \"svelte\";\n\timport {\n\t\tCamera,\n\t\tCircle,\n\t\tSquare,\n\t\tDropdownArrow,\n\t\tSpinner\n\t} from \"@gradio/icons\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { StreamingBar } from \"@gradio/statustracker\";\n\timport { type FileData, type Client, prepare_files } from \"@gradio/client\";\n\timport WebcamPermissions from \"./WebcamPermissions.svelte\";\n\timport { fade } from \"svelte/transition\";\n\timport {\n\t\tget_devices,\n\t\tget_video_stream,\n\t\tset_available_devices\n\t} from \"./stream_utils\";\n\timport type { Base64File } from \"./types\";\n\n\tlet video_source: HTMLVideoElement;\n\tlet available_video_devices: MediaDeviceInfo[] = [];\n\tlet selected_device: MediaDeviceInfo | null = null;\n\tlet time_limit: number | null = null;\n\tlet stream_state: \"open\" | \"waiting\" | \"closed\" = \"closed\";\n\n\texport const modify_stream: (state: \"open\" | \"closed\" | \"waiting\") => void = (\n\t\tstate: \"open\" | \"closed\" | \"waiting\"\n\t) => {\n\t\tif (state === \"closed\") {\n\t\t\ttime_limit = null;\n\t\t\tstream_state = \"closed\";\n\t\t\tvalue = null;\n\t\t} else if (state === \"waiting\") {\n\t\t\tstream_state = \"waiting\";\n\t\t} else {\n\t\t\tstream_state = \"open\";\n\t\t}\n\t};\n\n\texport const set_time_limit = (time: number): void => {\n\t\tif (recording) time_limit = time;\n\t};\n\n\tlet canvas: HTMLCanvasElement;\n\texport let streaming = false;\n\texport let pending = false;\n\texport let root = \"\";\n\texport let stream_every = 1;\n\n\texport let mode: \"image\" | \"video\" = \"image\";\n\texport let mirror_webcam: boolean;\n\texport let include_audio: boolean;\n\texport let webcam_constraints: { [key: string]: any } | null = null;\n\texport let i18n: I18nFormatter;\n\texport let upload: Client[\"upload\"];\n\texport let value: FileData | null | Base64File = null;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tstream: Blob | string;\n\t\tcapture: FileData | Blob | null;\n\t\terror: string;\n\t\tstart_recording: undefined;\n\t\tstop_recording: undefined;\n\t\tclose_stream: undefined;\n\t}>();\n\n\tonMount(() => {\n\t\tcanvas = document.createElement(\"canvas\");\n\t\tif (streaming && mode === \"image\") {\n\t\t\twindow.setInterval(() => {\n\t\t\t\tif (video_source && !pending) {\n\t\t\t\t\ttake_picture();\n\t\t\t\t}\n\t\t\t}, stream_every * 1000);\n\t\t}\n\t});\n\n\tconst handle_device_change = async (event: InputEvent): Promise<void> => {\n\t\tconst target = event.target as HTMLInputElement;\n\t\tconst device_id = target.value;\n\n\t\tawait get_video_stream(\n\t\t\tinclude_audio,\n\t\t\tvideo_source,\n\t\t\twebcam_constraints,\n\t\t\tdevice_id\n\t\t).then(async (local_stream) => {\n\t\t\tstream = local_stream;\n\t\t\tselected_device =\n\t\t\t\tavailable_video_devices.find(\n\t\t\t\t\t(device) => device.deviceId === device_id\n\t\t\t\t) || null;\n\t\t\toptions_open = false;\n\t\t});\n\t};\n\n\tasync function access_webcam(): Promise<void> {\n\t\ttry {\n\t\t\tget_video_stream(include_audio, video_source, webcam_constraints)\n\t\t\t\t.then(async (local_stream) => {\n\t\t\t\t\twebcam_accessed = true;\n\t\t\t\t\tavailable_video_devices = await get_devices();\n\t\t\t\t\tstream = local_stream;\n\t\t\t\t})\n\t\t\t\t.then(() => set_available_devices(available_video_devices))\n\t\t\t\t.then((devices) => {\n\t\t\t\t\tavailable_video_devices = devices;\n\n\t\t\t\t\tconst used_devices = stream\n\t\t\t\t\t\t.getTracks()\n\t\t\t\t\t\t.map((track) => track.getSettings()?.deviceId)[0];\n\n\t\t\t\t\tselected_device = used_devices\n\t\t\t\t\t\t? devices.find((device) => device.deviceId === used_devices) ||\n\t\t\t\t\t\t\tavailable_video_devices[0]\n\t\t\t\t\t\t: available_video_devices[0];\n\t\t\t\t});\n\n\t\t\tif (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n\t\t\t\tdispatch(\"error\", i18n(\"image.no_webcam_support\"));\n\t\t\t}\n\t\t} catch (err) {\n\t\t\tif (err instanceof DOMException && err.name == \"NotAllowedError\") {\n\t\t\t\tdispatch(\"error\", i18n(\"image.allow_webcam_access\"));\n\t\t\t} else {\n\t\t\t\tthrow err;\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction take_picture(): void {\n\t\tvar context = canvas.getContext(\"2d\")!;\n\t\tif (\n\t\t\t(!streaming || (streaming && recording)) &&\n\t\t\tvideo_source.videoWidth &&\n\t\t\tvideo_source.videoHeight\n\t\t) {\n\t\t\tcanvas.width = video_source.videoWidth;\n\t\t\tcanvas.height = video_source.videoHeight;\n\t\t\tcontext.drawImage(\n\t\t\t\tvideo_source,\n\t\t\t\t0,\n\t\t\t\t0,\n\t\t\t\tvideo_source.videoWidth,\n\t\t\t\tvideo_source.videoHeight\n\t\t\t);\n\n\t\t\tif (mirror_webcam) {\n\t\t\t\tcontext.scale(-1, 1);\n\t\t\t\tcontext.drawImage(video_source, -video_source.videoWidth, 0);\n\t\t\t}\n\n\t\t\tif (streaming && (!recording || stream_state === \"waiting\")) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (streaming) {\n\t\t\t\tconst image_data = canvas.toDataURL(\"image/jpeg\");\n\t\t\t\tdispatch(\"stream\", image_data);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tcanvas.toBlob(\n\t\t\t\t(blob) => {\n\t\t\t\t\tdispatch(streaming ? \"stream\" : \"capture\", blob);\n\t\t\t\t},\n\t\t\t\t`image/${streaming ? \"jpeg\" : \"png\"}`,\n\t\t\t\t0.8\n\t\t\t);\n\t\t}\n\t}\n\n\tlet recording = false;\n\tlet recorded_blobs: BlobPart[] = [];\n\tlet stream: MediaStream;\n\tlet mimeType: string;\n\tlet media_recorder: MediaRecorder;\n\n\tfunction take_recording(): void {\n\t\tif (recording) {\n\t\t\tmedia_recorder.stop();\n\t\t\tlet video_blob = new Blob(recorded_blobs, { type: mimeType });\n\t\t\tlet ReaderObj = new FileReader();\n\t\t\tReaderObj.onload = async function (e): Promise<void> {\n\t\t\t\tif (e.target) {\n\t\t\t\t\tlet _video_blob = new File(\n\t\t\t\t\t\t[video_blob],\n\t\t\t\t\t\t\"sample.\" + mimeType.substring(6)\n\t\t\t\t\t);\n\t\t\t\t\tconst val = await prepare_files([_video_blob]);\n\t\t\t\t\tlet val_ = (\n\t\t\t\t\t\t(await upload(val, root))?.filter(Boolean) as FileData[]\n\t\t\t\t\t)[0];\n\t\t\t\t\tdispatch(\"capture\", val_);\n\t\t\t\t\tdispatch(\"stop_recording\");\n\t\t\t\t}\n\t\t\t};\n\t\t\tReaderObj.readAsDataURL(video_blob);\n\t\t} else if (typeof MediaRecorder !== \"undefined\") {\n\t\t\tdispatch(\"start_recording\");\n\t\t\trecorded_blobs = [];\n\t\t\tlet validMimeTypes = [\"video/webm\", \"video/mp4\"];\n\t\t\tfor (let validMimeType of validMimeTypes) {\n\t\t\t\tif (MediaRecorder.isTypeSupported(validMimeType)) {\n\t\t\t\t\tmimeType = validMimeType;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (mimeType === null) {\n\t\t\t\tconsole.error(\"No supported MediaRecorder mimeType\");\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tmedia_recorder = new MediaRecorder(stream, {\n\t\t\t\tmimeType: mimeType\n\t\t\t});\n\t\t\tmedia_recorder.addEventListener(\"dataavailable\", function (e) {\n\t\t\t\trecorded_blobs.push(e.data);\n\t\t\t});\n\t\t\tmedia_recorder.start(200);\n\t\t}\n\t\trecording = !recording;\n\t}\n\n\tlet webcam_accessed = false;\n\n\tfunction record_video_or_photo({\n\t\tdestroy\n\t}: { destroy?: boolean } = {}): void {\n\t\tif (mode === \"image\" && streaming) {\n\t\t\trecording = !recording;\n\t\t}\n\n\t\tif (!destroy) {\n\t\t\tif (mode === \"image\") {\n\t\t\t\ttake_picture();\n\t\t\t} else {\n\t\t\t\ttake_recording();\n\t\t\t}\n\t\t}\n\n\t\tif (!recording && stream) {\n\t\t\tdispatch(\"close_stream\");\n\t\t\tstream.getTracks().forEach((track) => track.stop());\n\t\t\tvideo_source.srcObject = null;\n\t\t\twebcam_accessed = false;\n\t\t\twindow.setTimeout(() => {\n\t\t\t\tvalue = null;\n\t\t\t}, 500);\n\t\t\tvalue = null;\n\t\t}\n\t}\n\n\tlet options_open = false;\n\n\texport function click_outside(node: Node, cb: any): any {\n\t\tconst handle_click = (event: MouseEvent): void => {\n\t\t\tif (\n\t\t\t\tnode &&\n\t\t\t\t!node.contains(event.target as Node) &&\n\t\t\t\t!event.defaultPrevented\n\t\t\t) {\n\t\t\t\tcb(event);\n\t\t\t}\n\t\t};\n\n\t\tdocument.addEventListener(\"click\", handle_click, true);\n\n\t\treturn {\n\t\t\tdestroy() {\n\t\t\t\tdocument.removeEventListener(\"click\", handle_click, true);\n\t\t\t}\n\t\t};\n\t}\n\n\tfunction handle_click_outside(event: MouseEvent): void {\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\t\toptions_open = false;\n\t}\n\n\tonDestroy(() => {\n\t\tif (typeof window === \"undefined\") return;\n\t\trecord_video_or_photo({ destroy: true });\n\t\tstream?.getTracks().forEach((track) => track.stop());\n\t});\n</script>\n\n<div class=\"wrap\">\n\t<StreamingBar {time_limit} />\n\t<!-- svelte-ignore a11y-media-has-caption -->\n\t<!-- need to suppress for video streaming https://github.com/sveltejs/svelte/issues/5967 -->\n\t<video\n\t\tbind:this={video_source}\n\t\tclass:flip={mirror_webcam}\n\t\tclass:hide={!webcam_accessed || (webcam_accessed && !!value)}\n\t/>\n\t<!-- svelte-ignore a11y-missing-attribute -->\n\t<img\n\t\tsrc={value?.url}\n\t\tclass:hide={!webcam_accessed || (webcam_accessed && !value)}\n\t/>\n\t{#if !webcam_accessed}\n\t\t<div\n\t\t\tin:fade={{ delay: 100, duration: 200 }}\n\t\t\ttitle=\"grant webcam access\"\n\t\t\tstyle=\"height: 100%\"\n\t\t>\n\t\t\t<WebcamPermissions on:click={async () => access_webcam()} />\n\t\t</div>\n\t{:else}\n\t\t<div class=\"button-wrap\">\n\t\t\t<button\n\t\t\t\ton:click={() => record_video_or_photo()}\n\t\t\t\taria-label={mode === \"image\" ? \"capture photo\" : \"start recording\"}\n\t\t\t>\n\t\t\t\t{#if mode === \"video\" || streaming}\n\t\t\t\t\t{#if streaming && stream_state === \"waiting\"}\n\t\t\t\t\t\t<div class=\"icon-with-text\" style=\"width:var(--size-24);\">\n\t\t\t\t\t\t\t<div class=\"icon color-primary\" title=\"spinner\">\n\t\t\t\t\t\t\t\t<Spinner />\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{i18n(\"audio.waiting\")}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{:else if (streaming && stream_state === \"open\") || (!streaming && recording)}\n\t\t\t\t\t\t<div class=\"icon-with-text\">\n\t\t\t\t\t\t\t<div class=\"icon color-primary\" title=\"stop recording\">\n\t\t\t\t\t\t\t\t<Square />\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{i18n(\"audio.stop\")}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{:else}\n\t\t\t\t\t\t<div class=\"icon-with-text\">\n\t\t\t\t\t\t\t<div class=\"icon color-primary\" title=\"start recording\">\n\t\t\t\t\t\t\t\t<Circle />\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{i18n(\"audio.record\")}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t{:else}\n\t\t\t\t\t<div class=\"icon\" title=\"capture photo\">\n\t\t\t\t\t\t<Camera />\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t</button>\n\t\t\t{#if !recording}\n\t\t\t\t<button\n\t\t\t\t\tclass=\"icon\"\n\t\t\t\t\ton:click={() => (options_open = true)}\n\t\t\t\t\taria-label=\"select input source\"\n\t\t\t\t>\n\t\t\t\t\t<DropdownArrow />\n\t\t\t\t</button>\n\t\t\t{/if}\n\t\t</div>\n\t\t{#if options_open && selected_device}\n\t\t\t<select\n\t\t\t\tclass=\"select-wrap\"\n\t\t\t\taria-label=\"select source\"\n\t\t\t\tuse:click_outside={handle_click_outside}\n\t\t\t\ton:change={handle_device_change}\n\t\t\t>\n\t\t\t\t<button\n\t\t\t\t\tclass=\"inset-icon\"\n\t\t\t\t\ton:click|stopPropagation={() => (options_open = false)}\n\t\t\t\t>\n\t\t\t\t\t<DropdownArrow />\n\t\t\t\t</button>\n\t\t\t\t{#if available_video_devices.length === 0}\n\t\t\t\t\t<option value=\"\">{i18n(\"common.no_devices\")}</option>\n\t\t\t\t{:else}\n\t\t\t\t\t{#each available_video_devices as device}\n\t\t\t\t\t\t<option\n\t\t\t\t\t\t\tvalue={device.deviceId}\n\t\t\t\t\t\t\tselected={selected_device.deviceId === device.deviceId}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{device.label}\n\t\t\t\t\t\t</option>\n\t\t\t\t\t{/each}\n\t\t\t\t{/if}\n\t\t\t</select>\n\t\t{/if}\n\t{/if}\n</div>\n\n<style>\n\t.wrap {\n\t\tposition: relative;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.hide {\n\t\tdisplay: none;\n\t}\n\n\tvideo {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: contain;\n\t}\n\n\t.button-wrap {\n\t\tposition: absolute;\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-xl);\n\t\tpadding: var(--size-1-5);\n\t\tdisplay: flex;\n\t\tbottom: var(--size-2);\n\t\tleft: 50%;\n\t\ttransform: translate(-50%, 0);\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tborder-radius: var(--radius-xl);\n\t\tline-height: var(--size-3);\n\t\tcolor: var(--button-secondary-text-color);\n\t}\n\n\t.icon-with-text {\n\t\twidth: var(--size-20);\n\t\talign-items: center;\n\t\tmargin: 0 var(--spacing-xl);\n\t\tdisplay: flex;\n\t\tjustify-content: space-evenly;\n\t}\n\n\t@media (--screen-md) {\n\t\tbutton {\n\t\t\tbottom: var(--size-4);\n\t\t}\n\t}\n\n\t@media (--screen-xl) {\n\t\tbutton {\n\t\t\tbottom: var(--size-8);\n\t\t}\n\t}\n\n\t.icon {\n\t\twidth: 18px;\n\t\theight: 18px;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t}\n\n\t.color-primary {\n\t\tfill: var(--primary-600);\n\t\tstroke: var(--primary-600);\n\t\tcolor: var(--primary-600);\n\t}\n\n\t.flip {\n\t\ttransform: scaleX(-1);\n\t}\n\n\t.select-wrap {\n\t\t-webkit-appearance: none;\n\t\t-moz-appearance: none;\n\t\tappearance: none;\n\t\tcolor: var(--button-secondary-text-color);\n\t\tbackground-color: transparent;\n\t\twidth: 95%;\n\t\tfont-size: var(--text-md);\n\t\tposition: absolute;\n\t\tbottom: var(--size-2);\n\t\tbackground-color: var(--block-background-fill);\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tborder-radius: var(--radius-xl);\n\t\tz-index: var(--layer-top);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\ttext-align: left;\n\t\tline-height: var(--size-4);\n\t\twhite-space: nowrap;\n\t\ttext-overflow: ellipsis;\n\t\tleft: 50%;\n\t\ttransform: translate(-50%, 0);\n\t\tmax-width: var(--size-52);\n\t}\n\n\t.select-wrap > option {\n\t\tpadding: 0.25rem 0.5rem;\n\t\tborder-bottom: 1px solid var(--border-color-accent);\n\t\tpadding-right: var(--size-8);\n\t\ttext-overflow: ellipsis;\n\t\toverflow: hidden;\n\t}\n\n\t.select-wrap > option:hover {\n\t\tbackground-color: var(--color-accent);\n\t}\n\n\t.select-wrap > option:last-child {\n\t\tborder: none;\n\t}\n\n\t.inset-icon {\n\t\tposition: absolute;\n\t\ttop: 5px;\n\t\tright: -6.5px;\n\t\twidth: var(--size-10);\n\t\theight: var(--size-5);\n\t\topacity: 0.8;\n\t}\n\n\t@media (--screen-md) {\n\t\t.wrap {\n\t\t\tfont-size: var(--text-lg);\n\t\t}\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher, tick } from \"svelte\";\n\timport { <PERSON><PERSON><PERSON><PERSON>, IconButtonWrapper, IconButton } from \"@gradio/atoms\";\n\timport { Clear, Image as ImageIcon } from \"@gradio/icons\";\n\timport { FullscreenButton } from \"@gradio/atoms\";\n\timport {\n\t\ttype SelectData,\n\t\ttype I18nFormatter,\n\t\ttype ValueData\n\t} from \"@gradio/utils\";\n\timport { get_coordinates_of_clicked_image } from \"./utils\";\n\timport Webcam from \"./Webcam.svelte\";\n\n\timport { Upload } from \"@gradio/upload\";\n\timport { FileData, type Client } from \"@gradio/client\";\n\timport { SelectSource } from \"@gradio/atoms\";\n\timport Image from \"./Image.svelte\";\n\timport type { Base64File, WebcamOptions } from \"./types\";\n\n\texport let value: null | FileData | Base64File = null;\n\texport let label: string | undefined = undefined;\n\texport let show_label: boolean;\n\n\ttype source_type = \"upload\" | \"webcam\" | \"clipboard\" | \"microphone\" | null;\n\n\texport let sources: source_type[] = [\"upload\", \"clipboard\", \"webcam\"];\n\texport let streaming = false;\n\texport let pending = false;\n\texport let webcam_options: WebcamOptions;\n\texport let selectable = false;\n\texport let root: string;\n\texport let i18n: I18nFormatter;\n\texport let max_file_size: number | null = null;\n\texport let upload: Client[\"upload\"];\n\texport let stream_handler: Client[\"stream\"];\n\texport let stream_every: number;\n\n\texport let modify_stream: (state: \"open\" | \"closed\" | \"waiting\") => void;\n\texport let set_time_limit: (arg0: number) => void;\n\texport let show_fullscreen_button = true;\n\n\tlet upload_input: Upload;\n\texport let uploading = false;\n\texport let active_source: source_type = null;\n\texport let fullscreen = false;\n\n\tasync function handle_upload({\n\t\tdetail\n\t}: CustomEvent<FileData>): Promise<void> {\n\t\tif (!streaming) {\n\t\t\tif (detail.path?.toLowerCase().endsWith(\".svg\") && detail.url) {\n\t\t\t\tconst response = await fetch(detail.url);\n\t\t\t\tconst svgContent = await response.text();\n\t\t\t\tvalue = {\n\t\t\t\t\t...detail,\n\t\t\t\t\turl: `data:image/svg+xml,${encodeURIComponent(svgContent)}`\n\t\t\t\t};\n\t\t\t} else {\n\t\t\t\tvalue = detail;\n\t\t\t}\n\n\t\t\tawait tick();\n\t\t\tdispatch(\"upload\");\n\t\t}\n\t}\n\n\tfunction handle_clear(): void {\n\t\tvalue = null;\n\t\tdispatch(\"clear\");\n\t\tdispatch(\"change\", null);\n\t}\n\n\tasync function handle_save(\n\t\timg_blob: Blob | any,\n\t\tevent: \"change\" | \"stream\" | \"upload\"\n\t): Promise<void> {\n\t\tif (event === \"stream\") {\n\t\t\tdispatch(\"stream\", {\n\t\t\t\tvalue: { url: img_blob } as Base64File,\n\t\t\t\tis_value_data: true\n\t\t\t});\n\t\t\treturn;\n\t\t}\n\t\tpending = true;\n\t\tconst f = await upload_input.load_files([\n\t\t\tnew File([img_blob], `image/${streaming ? \"jpeg\" : \"png\"}`)\n\t\t]);\n\n\t\tif (event === \"change\" || event === \"upload\") {\n\t\t\tvalue = f?.[0] || null;\n\t\t\tawait tick();\n\t\t\tdispatch(\"change\");\n\t\t}\n\t\tpending = false;\n\t}\n\n\t$: active_streaming = streaming && active_source === \"webcam\";\n\t$: if (uploading && !active_streaming) value = null;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange?: never;\n\t\tstream: ValueData;\n\t\tclear?: never;\n\t\tdrag: boolean;\n\t\tupload?: never;\n\t\tselect: SelectData;\n\t\tend_stream: never;\n\t}>();\n\n\texport let dragging = false;\n\n\t$: dispatch(\"drag\", dragging);\n\n\tfunction handle_click(evt: MouseEvent): void {\n\t\tlet coordinates = get_coordinates_of_clicked_image(evt);\n\t\tif (coordinates) {\n\t\t\tdispatch(\"select\", { index: coordinates, value: null });\n\t\t}\n\t}\n\n\t$: if (!active_source && sources) {\n\t\tactive_source = sources[0];\n\t}\n\n\tasync function handle_select_source(\n\t\tsource: (typeof sources)[number]\n\t): Promise<void> {\n\t\tswitch (source) {\n\t\t\tcase \"clipboard\":\n\t\t\t\tupload_input.paste_clipboard();\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tbreak;\n\t\t}\n\t}\n\n\tlet image_container: HTMLElement;\n\n\tfunction on_drag_over(evt: DragEvent): void {\n\t\tevt.preventDefault();\n\t\tevt.stopPropagation();\n\t\tif (evt.dataTransfer) {\n\t\t\tevt.dataTransfer.dropEffect = \"copy\";\n\t\t}\n\n\t\tdragging = true;\n\t}\n\n\tasync function on_drop(evt: DragEvent): Promise<void> {\n\t\tevt.preventDefault();\n\t\tevt.stopPropagation();\n\t\tdragging = false;\n\n\t\tif (value) {\n\t\t\thandle_clear();\n\t\t\tawait tick();\n\t\t}\n\n\t\tactive_source = \"upload\";\n\t\tawait tick();\n\t\tupload_input.load_files_from_drop(evt);\n\t}\n</script>\n\n<BlockLabel {show_label} Icon={ImageIcon} label={label || \"Image\"} />\n\n<div data-testid=\"image\" class=\"image-container\" bind:this={image_container}>\n\t<IconButtonWrapper>\n\t\t{#if value?.url && !active_streaming}\n\t\t\t{#if show_fullscreen_button}\n\t\t\t\t<FullscreenButton {fullscreen} on:fullscreen />\n\t\t\t{/if}\n\t\t\t<IconButton\n\t\t\t\tIcon={Clear}\n\t\t\t\tlabel=\"Remove Image\"\n\t\t\t\ton:click={(event) => {\n\t\t\t\t\tvalue = null;\n\t\t\t\t\tdispatch(\"clear\");\n\t\t\t\t\tevent.stopPropagation();\n\t\t\t\t}}\n\t\t\t/>\n\t\t{/if}\n\t</IconButtonWrapper>\n\t<!-- svelte-ignore a11y-no-static-element-interactions -->\n\t<div\n\t\tclass=\"upload-container\"\n\t\tclass:reduced-height={sources.length > 1}\n\t\tstyle:width={value ? \"auto\" : \"100%\"}\n\t\ton:dragover={on_drag_over}\n\t\ton:drop={on_drop}\n\t>\n\t\t<Upload\n\t\t\thidden={value !== null || active_source === \"webcam\"}\n\t\t\tbind:this={upload_input}\n\t\t\tbind:uploading\n\t\t\tbind:dragging\n\t\t\tfiletype={active_source === \"clipboard\" ? \"clipboard\" : \"image/*\"}\n\t\t\ton:load={handle_upload}\n\t\t\ton:error\n\t\t\t{root}\n\t\t\t{max_file_size}\n\t\t\tdisable_click={!sources.includes(\"upload\") || value !== null}\n\t\t\t{upload}\n\t\t\t{stream_handler}\n\t\t\taria_label={i18n(\"image.drop_to_upload\")}\n\t\t>\n\t\t\t{#if value === null}\n\t\t\t\t<slot />\n\t\t\t{/if}\n\t\t</Upload>\n\t\t{#if active_source === \"webcam\" && (streaming || (!streaming && !value))}\n\t\t\t<Webcam\n\t\t\t\t{root}\n\t\t\t\t{value}\n\t\t\t\ton:capture={(e) => handle_save(e.detail, \"change\")}\n\t\t\t\ton:stream={(e) => handle_save(e.detail, \"stream\")}\n\t\t\t\ton:error\n\t\t\t\ton:drag\n\t\t\t\ton:upload={(e) => handle_save(e.detail, \"upload\")}\n\t\t\t\ton:close_stream\n\t\t\t\tmirror_webcam={webcam_options.mirror}\n\t\t\t\t{stream_every}\n\t\t\t\t{streaming}\n\t\t\t\tmode=\"image\"\n\t\t\t\tinclude_audio={false}\n\t\t\t\t{i18n}\n\t\t\t\t{upload}\n\t\t\t\tbind:modify_stream\n\t\t\t\tbind:set_time_limit\n\t\t\t\twebcam_constraints={webcam_options.constraints}\n\t\t\t/>\n\t\t{:else if value !== null && !streaming}\n\t\t\t<!-- svelte-ignore a11y-click-events-have-key-events-->\n\t\t\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t\t\t<div class:selectable class=\"image-frame\" on:click={handle_click}>\n\t\t\t\t<Image src={value.url} alt={value.alt_text} />\n\t\t\t</div>\n\t\t{/if}\n\t</div>\n\t{#if sources.length > 1 || sources.includes(\"clipboard\")}\n\t\t<SelectSource\n\t\t\t{sources}\n\t\t\tbind:active_source\n\t\t\t{handle_clear}\n\t\t\thandle_select={handle_select_source}\n\t\t/>\n\t{/if}\n</div>\n\n<style>\n\t.image-frame :global(img) {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: scale-down;\n\t}\n\n\t.upload-container {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\n\t\theight: 100%;\n\t\tflex-shrink: 1;\n\t\tmax-height: 100%;\n\t}\n\n\t.reduced-height {\n\t\theight: calc(100% - var(--size-10));\n\t}\n\n\t.image-container {\n\t\tdisplay: flex;\n\t\theight: 100%;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmax-height: 100%;\n\t}\n\n\t.selectable {\n\t\tcursor: crosshair;\n\t}\n\n\t.image-frame {\n\t\tobject-fit: cover;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "circle", "button", "div", "span", "dispatch", "createEventDispatcher", "get_devices", "set_local_stream", "local_stream", "video_source", "get_video_stream", "include_audio", "webcam_constraints", "device_id", "constraints", "set_available_devices", "devices", "device", "onDestroy", "ctx", "create_if_block_3", "if_block2", "create_if_block_1", "attr", "button_aria_label_value", "current", "dirty", "div_intro", "create_in_transition", "fade", "t1_value", "div1", "div0", "set_data", "t1", "create_if_block_2", "select", "i", "t_value", "option", "t", "t0_value", "option_value_value", "option_selected_value", "t0", "src_url_equal", "img", "img_src_value", "video", "click_outside", "node", "cb", "handle_click", "event", "available_video_devices", "selected_device", "time_limit", "stream_state", "modify_stream", "state", "value", "set_time_limit", "time", "recording", "$$invalidate", "canvas", "streaming", "$$props", "pending", "root", "stream_every", "mode", "mirror_webcam", "i18n", "upload", "onMount", "take_picture", "handle_device_change", "stream", "options_open", "access_webcam", "webcam_accessed", "used_devices", "track", "err", "context", "image_data", "blob", "recorded_blobs", "mimeType", "media_recorder", "take_recording", "video_blob", "ReaderObj", "e", "_video_blob", "val", "prepare_files", "val_", "validMimeTypes", "validMimeType", "record_video_or_photo", "destroy", "handle_click_outside", "$$value", "click_handler_2", "click_handler_3", "tick", "create_if_block_5", "Clear", "create_if_block_4", "if_block", "image_changes", "webcam_changes", "ImageIcon", "blocklabel_changes", "upload_1_changes", "label", "show_label", "sources", "webcam_options", "selectable", "max_file_size", "stream_handler", "show_fullscreen_button", "upload_input", "uploading", "active_source", "fullscreen", "handle_upload", "detail", "svgContent", "handle_clear", "handle_save", "img_blob", "f", "dragging", "evt", "coordinates", "get_coordinates_of_clicked_image", "handle_select_source", "source", "image_container", "on_drag_over", "on_drop", "capture_handler", "stream_handler_1", "upload_handler", "active_streaming"], "mappings": "kkDAAAA,GAgBKC,EAAAC,EAAAC,CAAA,EAJJC,GAECF,EAAAG,CAAA,EACDD,GAA+BF,EAAAI,CAAA,qlBCfhCN,GAWKC,EAAAC,EAAAC,CAAA,EADJC,GAAgCF,EAAAI,CAAA,sZCRM,EAAA,OAAA,0DAYpC,uQALHN,GAOQC,EAAAM,EAAAJ,CAAA,EANPC,GAKKG,EAAAC,CAAA,EAJJJ,GAEMI,EAAAC,CAAA,gMATDC,EAAWC,cAK0B,IAAAD,EAAS,OAAO,sECTrD,SAASE,IAA0C,CAClD,OAAA,UAAU,aAAa,kBAC/B,CAMgB,SAAAC,GACfC,EACAC,EACO,CACPA,EAAa,UAAYD,EACzBC,EAAa,MAAQ,GACrBA,EAAa,KAAK,CACnB,CAEA,eAAsBC,GACrBC,EACAF,EACAG,EACAC,EACuB,CACvB,MAAMC,EAAsC,CAC3C,MAAOD,EACJ,CAAE,SAAU,CAAE,MAAOA,GAAa,GAAGD,GAAoB,OACzDA,GAAoB,OAAS,CAC7B,MAAO,CAAE,MAAO,IAAK,EACrB,OAAQ,CAAE,MAAO,IAAK,CACvB,EACF,MAAOD,IAAkBC,GAAoB,OAAS,GAAA,EAGvD,OAAO,UAAU,aACf,aAAaE,CAAW,EACxB,KAAMN,IACND,GAAiBC,EAAcC,CAAY,EACpCD,EACP,CACH,CAEO,SAASO,GACfC,EACoB,CAKb,OAJSA,EAAQ,OACtBC,GAA4BA,EAAO,OAAS,YAAA,CAI/C,4hBChDU,CAAA,sBAAAZ,GAAA,UAAAa,aAAiD,EAAA,OAAA,yKA2TlDC,EAAI,CAAA,IAAK,SAAWA,EAAS,CAAA,EAAA,iCA6B7BA,EAAS,EAAA,GAAAC,GAAAD,CAAA,EAUXE,EAAAF,OAAgBA,EAAe,CAAA,GAAAG,GAAAH,CAAA,iFAzCtBI,EAAAtB,EAAA,aAAAuB,EAAAL,OAAS,QAAU,gBAAkB,iBAAiB,kFAHpEzB,EA2CKC,EAAAO,EAAAL,CAAA,EA1CJC,EAgCQI,EAAAD,CAAA,wPA9BK,CAAAwB,GAAAC,EAAA,CAAA,EAAA,GAAAF,KAAAA,EAAAL,OAAS,QAAU,gBAAkB,yCA+B5CA,EAAS,EAAA,yGAUXA,OAAgBA,EAAe,CAAA,kZApDpCzB,EAMKC,EAAAO,EAAAL,CAAA,qEALO8B,EAAAC,GAAA1B,EAAA2B,GAAA,CAAA,MAAO,IAAK,SAAU,GAAG,CAAA,gOAoClCnC,EAEKC,EAAAO,EAAAL,CAAA,0LAzBAsB,EAAS,CAAA,GAAIA,EAAY,EAAA,IAAK,UAAS,EAOjCA,MAAaA,EAAY,EAAA,IAAK,QAAa,CAAAA,MAAaA,EAAS,EAAA,EAAA,oUAYzEW,EAAAX,KAAK,cAAc,EAAA,2NAJrBzB,EAKKC,EAAAoC,EAAAlC,CAAA,EAJJC,EAEKiC,EAAAC,CAAA,2CACJ,CAAAP,GAAAC,EAAA,CAAA,EAAA,KAAAI,KAAAA,EAAAX,KAAK,cAAc,EAAA,KAAAc,GAAAC,EAAAJ,CAAA,oHAPnBA,EAAAX,KAAK,YAAY,EAAA,0NAJnBzB,EAKKC,EAAAoC,EAAAlC,CAAA,EAJJC,EAEKiC,EAAAC,CAAA,2CACJ,CAAAP,GAAAC,EAAA,CAAA,EAAA,KAAAI,KAAAA,EAAAX,KAAK,YAAY,EAAA,KAAAc,GAAAC,EAAAJ,CAAA,oHAPjBA,EAAAX,KAAK,eAAe,EAAA,kPAJtBzB,EAKKC,EAAAoC,EAAAlC,CAAA,EAJJC,EAEKiC,EAAAC,CAAA,2CACJ,CAAAP,GAAAC,EAAA,CAAA,EAAA,KAAAI,KAAAA,EAAAX,KAAK,eAAe,EAAA,KAAAc,GAAAC,EAAAJ,CAAA,kQAwBxBpC,EAMQC,EAAAM,EAAAJ,CAAA,8NAgBHsB,EAAuB,CAAA,EAAC,SAAW,EAACgB,0NAZ1CzC,EAwBQC,EAAAyC,EAAAvC,CAAA,EAlBPC,EAKQsC,EAAAnC,CAAA,wFARWkB,EAAoB,EAAA,CAAA,CAAA,gBAC5BA,EAAoB,EAAA,CAAA,mNAWvBA,EAAuB,CAAA,CAAA,uBAA5B,OAAIkB,GAAA,qKAAClB,EAAuB,CAAA,CAAA,oBAA5B,OAAIkB,GAAA,EAAA,2HAAJ,qDAFgBC,EAAAnB,KAAK,mBAAmB,EAAA,yGAA1CzB,EAAoDC,EAAA4C,EAAA1C,CAAA,iBAAlC6B,EAAA,CAAA,EAAA,IAAAY,KAAAA,EAAAnB,KAAK,mBAAmB,EAAA,KAAAc,GAAAO,EAAAF,CAAA,uCAOvCG,EAAAtB,MAAO,MAAK,kDAHNoB,EAAA,QAAAG,EAAAvB,MAAO,yBACJoB,EAAA,SAAAI,EAAAxB,EAAgB,CAAA,EAAA,WAAaA,MAAO,+CAF/CzB,EAKQC,EAAA4C,EAAA1C,CAAA,wBADN6B,EAAA,CAAA,EAAA,KAAAe,KAAAA,EAAAtB,MAAO,MAAK,KAAAc,GAAAW,EAAAH,CAAA,EAHNf,EAAA,CAAA,EAAA,KAAAgB,KAAAA,EAAAvB,MAAO,yCACJO,EAAA,CAAA,EAAA,KAAAiB,KAAAA,EAAAxB,EAAgB,CAAA,EAAA,WAAaA,MAAO,mKAxE9CA,EAAe,EAAA,IAAA,2JARRA,EAAa,CAAA,CAAA,eACZA,EAAe,EAAA,GAAKA,EAAe,EAAA,GAAA,CAAA,CAAMA,EAAK,CAAA,CAAA,EAItD0B,GAAAC,EAAA,IAAAC,EAAA5B,MAAO,GAAG,GAAAI,EAAAuB,EAAA,MAAAC,CAAA,6CACF5B,EAAe,EAAA,GAAKA,EAAe,EAAA,GAAA,CAAKA,EAAK,CAAA,CAAA,6CAZ5DzB,EA+FKC,EAAAO,EAAAL,CAAA,sBA3FJC,EAICI,EAAA8C,CAAA,kBAEDlD,EAGCI,EAAA4C,CAAA,kHAPY3B,EAAa,CAAA,CAAA,gCACZA,EAAe,EAAA,GAAKA,EAAe,EAAA,GAAA,CAAA,CAAMA,EAAK,CAAA,CAAA,GAItD,CAAAM,GAAAC,EAAA,CAAA,EAAA,GAAA,CAAAmB,GAAAC,EAAA,IAAAC,EAAA5B,MAAO,GAAG,+CACFA,EAAe,EAAA,GAAKA,EAAe,EAAA,GAAA,CAAKA,EAAK,CAAA,CAAA,6PA7C3C,SAAA8B,GAAcC,EAAYC,EAAA,OACnCC,EAAgBC,GAAA,CAEpBH,GAAA,CACCA,EAAK,SAASG,EAAM,MAAc,IAClCA,EAAM,kBAEPF,EAAGE,CAAK,GAIV,gBAAS,iBAAiB,QAASD,EAAc,EAAI,GAGpD,SAAA,CACC,SAAS,oBAAoB,QAASA,EAAc,EAAI,uBAzPvD,IAAA3C,EACA6C,EAAA,CAAA,EACAC,EAA0C,KAC1CC,EAA4B,KAC5BC,EAA8C,eAErCC,EACZC,GAAA,CAEIA,IAAU,cACbH,EAAa,IAAA,OACbC,EAAe,QAAA,MACfG,EAAQ,IAAA,GACED,IAAU,eACpBF,EAAe,SAAA,OAEfA,EAAe,MAAA,GAIJI,EAAkBC,GAAA,CAC1BC,GAAAC,EAAA,EAAWR,EAAaM,CAAA,GAGzB,IAAAG,GACO,UAAAC,EAAY,EAAA,EAAAC,GACZ,QAAAC,EAAU,EAAA,EAAAD,GACV,KAAAE,EAAO,EAAA,EAAAF,GACP,aAAAG,EAAe,CAAA,EAAAH,GAEf,KAAAI,EAA0B,OAAA,EAAAJ,EAC1B,CAAA,cAAAK,CAAA,EAAAL,EACA,CAAA,cAAAxD,CAAA,EAAAwD,GACA,mBAAAvD,EAAoD,IAAA,EAAAuD,EACpD,CAAA,KAAAM,CAAA,EAAAN,EACA,CAAA,OAAAO,CAAA,EAAAP,GACA,MAAAP,EAAsC,IAAA,EAAAO,QAE3C/D,EAAWC,KASjBsE,GAAA,IAAA,CACCV,EAAS,SAAS,cAAc,QAAQ,EACpCC,GAAaK,IAAS,SACzB,OAAO,iBACF9D,GAAiB,CAAA2D,GACpBQ,KAECN,EAAe,aAIdO,EAA8B,MAAAxB,GAAA,CAE7B,MAAAxC,EADSwC,EAAM,OACI,MAEnB,MAAA3C,GACLC,EACAF,EACAG,EACAC,CAAA,EACC,KAAY,MAAAL,IAAA,CACbsE,EAAStE,GACTwD,EAAA,EAAAT,EACCD,EAAwB,KACtBrC,IAAWA,GAAO,WAAaJ,CAC5B,GAAA,IAAA,OACNkE,EAAe,EAAA,KAIF,eAAAC,GAAA,KAEbtE,GAAiBC,EAAeF,EAAcG,CAAkB,EAC9D,KAAY,MAAAJ,GAAA,MACZyE,GAAkB,EAAA,MAClB3B,EAAgC,MAAAhD,GAAA,CAAA,EAChCwE,EAAStE,IAET,KAAW,IAAAO,GAAsBuC,CAAuB,CAAA,EACxD,KAAMtC,GAAA,KACNsC,EAA0BtC,CAAA,QAEpBkE,EAAeJ,EACnB,YACA,IAAKK,GAAUA,EAAM,YAAe,GAAA,QAAQ,EAAE,CAAC,MAEjD5B,EAAkB2B,GACflE,EAAQ,KAAMC,GAAWA,EAAO,WAAaiE,CAAY,GAC1D5B,EAAwB,CAAC,CACC,MAGzB,UAAU,cAAA,CAAiB,UAAU,aAAa,eACtDlD,EAAS,QAASqE,EAAK,yBAAyB,CAAA,CAEzC,OAAAW,EAAA,IACJA,aAAe,cAAgBA,EAAI,MAAQ,kBAC9ChF,EAAS,QAASqE,EAAK,2BAA2B,CAAA,MAE5C,OAAAW,GAKA,SAAAR,GAAA,KACJS,EAAUpB,EAAO,WAAW,IAAI,EAEjC,IAAA,CAAAC,GAAcA,GAAaH,IAC7BtD,EAAa,YACbA,EAAa,YAAA,CAiBT,GAfJwD,EAAO,MAAQxD,EAAa,WAC5BwD,EAAO,OAASxD,EAAa,YAC7B4E,EAAQ,UACP5E,EACA,EACA,EACAA,EAAa,WACbA,EAAa,WAAA,EAGV+D,IACHa,EAAQ,MAAA,GAAU,CAAC,EACnBA,EAAQ,UAAU5E,EAAA,CAAeA,EAAa,WAAY,CAAC,GAGxDyD,IAAA,CAAeH,GAAaN,IAAiB,kBAG7C,GAAAS,EAAA,OACGoB,EAAarB,EAAO,UAAU,YAAY,EAChD7D,EAAS,SAAUkF,CAAU,SAI9BrB,EAAO,OACLsB,GAAA,CACAnF,EAAS8D,EAAY,SAAW,UAAWqB,CAAI,GAEvC,SAAArB,EAAY,OAAS,KAAK,GACnC,SAKCH,EAAY,GACZyB,EAAA,CAAA,EACAV,EACAW,EACAC,EAEK,SAAAC,GAAA,CACJ,GAAA5B,EAAA,CACH2B,EAAe,KAAA,MACXE,EAAiB,IAAA,KAAKJ,GAAkB,KAAMC,CAAA,CAAA,EAC9CI,EAAgB,IAAA,WACpBA,EAAU,OAAyB,eAAAC,EAAA,IAC9BA,EAAE,OAAA,CACD,IAAAC,GAAA,IAAkB,MACpBH,CAAU,EACX,UAAYH,EAAS,UAAU,CAAC,CAAA,EAE3B,MAAAO,GAAA,MAAYC,IAAeF,EAAW,CAAA,EACxC,IAAAG,IAAA,MACIxB,EAAOsB,GAAK3B,CAAI,IAAI,OAAO,OAAO,EACxC,CAAC,EACHjE,EAAS,UAAW8F,EAAI,EACxB9F,EAAS,gBAAgB,IAG3ByF,EAAU,cAAcD,CAAU,iBACjB,cAAkB,IAAA,CACnCxF,EAAS,iBAAiB,EAC1BoF,EAAA,CAAA,EACI,IAAAW,EAAA,CAAkB,aAAc,WAAW,UACtCC,KAAiBD,EACrB,GAAA,cAAc,gBAAgBC,CAAa,EAAA,CAC9CX,EAAWW,WAITX,IAAa,KAAA,CAChB,QAAQ,MAAM,qCAAqC,SAGpDC,EAAA,IAAqB,cAAcZ,EAClC,CAAA,SAAAW,CAAA,CAAA,EAEDC,EAAe,iBAAiB,gBAA2B,SAAAI,EAAA,CAC1DN,EAAe,KAAKM,EAAE,IAAI,IAE3BJ,EAAe,MAAM,GAAG,OAEzB3B,EAAa,CAAAA,CAAA,MAGVkB,GAAkB,YAEboB,GACR,CAAA,QAAAC,CAAA,EAAA,GAAA,CAEI/B,IAAS,SAAWL,QACvBH,EAAa,CAAAA,CAAA,EAGTuC,IACA/B,IAAS,QACZK,IAEAe,MAIG5B,GAAae,IACjB1E,EAAS,cAAc,EACvB0E,EAAO,UAAA,EAAY,QAASK,GAAUA,EAAM,KAAA,CAAA,EAC5CnB,EAAA,EAAAvD,EAAa,UAAY,KAAAA,CAAA,OACzBwE,GAAkB,EAAA,EAClB,OAAO,oBACNrB,EAAQ,IAAA,GACN,SACHA,EAAQ,IAAA,OAINmB,EAAe,YAsBVwB,GAAqBlD,EAAA,CAC7BA,EAAM,eAAA,EACNA,EAAM,gBAAA,OACN0B,EAAe,EAAA,EAGhB7D,GAAA,IAAA,QACY,OAAW,MACtBmF,GAAA,CAAwB,QAAS,EAAA,CAAA,EACjCvB,GAAQ,UAAA,EAAY,QAASK,GAAUA,EAAM,KAAA,CAAA,gDASlC1E,EAAY+F,6BAemBxB,WAKxBqB,KAmCEI,GAAA,IAAAzC,EAAA,GAAAe,EAAe,EAAI,EAgBH2B,GAAA,IAAA1C,EAAA,GAAAe,EAAe,EAAK,2uEC3WhD,CAAA,sBAAA1E,GAAA,KAAAsG,IAAmC,OAAA,sDAwKrCxF,EAAsB,EAAA,GAAAyF,GAAAzF,CAAA,+BAIpB0F,GAAK,MAAA,cAAA,mHAJP1F,EAAsB,EAAA,ihBADvBA,EAAK,CAAA,GAAE,KAAG,CAAKA,EAAgB,EAAA,GAAA2F,GAAA3F,CAAA,wEAA/BA,EAAK,CAAA,GAAE,KAAG,CAAKA,EAAgB,EAAA,0cAsC9B4F,EAAA5F,OAAU,MAAIC,GAAAD,CAAA,wEAAdA,OAAU,sOA6BF,IAAAA,KAAM,IAAU,IAAAA,KAAM,0HADnCzB,GAEKC,EAAAO,EAAAL,CAAA,sCAF+CsB,EAAY,EAAA,CAAA,2BACnDO,EAAA,CAAA,EAAA,IAAAsF,EAAA,IAAA7F,KAAM,KAAUO,EAAA,CAAA,EAAA,IAAAsF,EAAA,IAAA7F,KAAM,oQAfnB,cAAAA,MAAe,oEAIf,2BAKK,mBAAAA,MAAe,wbATpBO,EAAA,CAAA,EAAA,OAAAuF,EAAA,cAAA9F,MAAe,gIASVO,EAAA,CAAA,EAAA,OAAAuF,EAAA,mBAAA9F,MAAe,yTAerBA,EAAoB,EAAA,iYALhCA,EAAO,CAAA,EAAC,OAAS,GAAKA,EAAO,CAAA,EAAC,SAAS,WAAW,8CA3EzB+F,GAAkB,MAAA/F,MAAS,2HA4B/C,OAAAA,EAAU,CAAA,IAAA,MAAQA,OAAkB,SAIlC,SAAAA,OAAkB,YAAc,YAAc,yCAKxC,cAAA,CAAAA,KAAQ,SAAS,QAAQ,GAAKA,OAAU,uCAG5C,WAAAA,MAAK,sBAAsB,wNAP9BA,EAAa,EAAA,CAAA,4DAalB,OAAAA,OAAkB,WAAaA,EAAe,CAAA,GAAA,CAAAA,OAAcA,EAAK,CAAA,GAAA,EAqB5DA,EAAK,CAAA,IAAK,MAAI,CAAKA,EAAS,CAAA,EAAA,0PA7ChBA,EAAO,CAAA,EAAC,OAAS,CAAC,eAC3BA,EAAK,CAAA,EAAG,OAAS,MAAM,wGArBtCzB,GAiFKC,EAAAoC,EAAAlC,CAAA,sBA/DJC,GAsDKiC,EAAAC,CAAA,qGAlDSb,EAAY,EAAA,CAAA,cAChBA,EAAO,EAAA,CAAA,0DAzB+BO,EAAA,CAAA,EAAA,KAAAyF,EAAA,MAAAhG,MAAS,yGA4B/CO,EAAA,CAAA,EAAA,KAAA0F,EAAA,OAAAjG,EAAU,CAAA,IAAA,MAAQA,OAAkB,UAIlCO,EAAA,CAAA,EAAA,IAAA0F,EAAA,SAAAjG,OAAkB,YAAc,YAAc,yEAKxCO,EAAA,CAAA,EAAA,MAAA0F,EAAA,cAAA,CAAAjG,KAAQ,SAAS,QAAQ,GAAKA,OAAU,wEAG5CO,EAAA,CAAA,EAAA,OAAA0F,EAAA,WAAAjG,MAAK,sBAAsB,8VAlBlBA,EAAO,CAAA,EAAC,OAAS,CAAC,uBAC3BA,EAAK,CAAA,EAAG,OAAS,MAAM,eAoDhCA,EAAO,CAAA,EAAC,OAAS,GAAKA,EAAO,CAAA,EAAC,SAAS,WAAW,oaA5N5C,MAAAyC,EAAsC,IAAA,EAAAO,GACtC,MAAAkD,EAA4B,MAAA,EAAAlD,EAC5B,CAAA,WAAAmD,CAAA,EAAAnD,GAIA,QAAAoD,EAA0B,CAAA,SAAU,YAAa,QAAQ,CAAA,EAAApD,GACzD,UAAAD,EAAY,EAAA,EAAAC,GACZ,QAAAC,EAAU,EAAA,EAAAD,EACV,CAAA,eAAAqD,CAAA,EAAArD,GACA,WAAAsD,EAAa,EAAA,EAAAtD,EACb,CAAA,KAAAE,CAAA,EAAAF,EACA,CAAA,KAAAM,CAAA,EAAAN,GACA,cAAAuD,EAA+B,IAAA,EAAAvD,EAC/B,CAAA,OAAAO,CAAA,EAAAP,EACA,CAAA,eAAAwD,CAAA,EAAAxD,EACA,CAAA,aAAAG,CAAA,EAAAH,EAEA,CAAA,cAAAT,CAAA,EAAAS,EACA,CAAA,eAAAN,CAAA,EAAAM,GACA,uBAAAyD,EAAyB,EAAA,EAAAzD,EAEhC0D,GACO,UAAAC,EAAY,EAAA,EAAA3D,GACZ,cAAA4D,EAA6B,IAAA,EAAA5D,GAC7B,WAAA6D,EAAa,EAAA,EAAA7D,iBAET8D,EACd,CAAA,OAAAC,GAAA,CAEK,GAAA,CAAAhE,EAAA,CACA,GAAAgE,EAAO,MAAM,YAAA,EAAc,SAAS,MAAM,GAAKA,EAAO,IAAA,CAEnD,MAAAC,GAAA,MADiB,MAAA,MAAMD,EAAO,GAAG,GACL,OAClClE,EAAA,EAAAJ,EAAA,CACI,GAAAsE,EACH,IAAA,sBAA2B,mBAAmBC,EAAU,CAAA,cAGzDvE,EAAQsE,CAAA,EAGH,MAAAvB,GAAA,EACNvG,EAAS,QAAQ,GAIV,SAAAgI,GAAA,KACRxE,EAAQ,IAAA,EACRxD,EAAS,OAAO,EAChBA,EAAS,SAAU,IAAI,EAGT,eAAAiI,EACdC,EACAjF,GAAA,IAEIA,KAAU,SAAA,CACbjD,EAAS,SAAA,CACR,MAAA,CAAS,IAAKkI,CAAA,EACd,cAAe,iBAIjBlE,EAAU,EAAA,EACJ,MAAAmE,GAAA,MAAUV,EAAa,WACxB,CAAA,IAAA,KAAA,CAAMS,CAAQ,EAAY,SAAApE,EAAY,OAAS,KAAK,EAAA,CAAA,CAAA,GAGrDb,KAAU,UAAYA,KAAU,gBACnCO,EAAQ2E,KAAI,CAAC,GAAK,IAAA,EACZ,MAAA5B,GAAA,EACNvG,EAAS,QAAQ,QAElBgE,EAAU,EAAA,QAMLhE,EAAWC,SAUN,SAAAmI,EAAW,EAAA,EAAArE,WAIbf,GAAaqF,EAAA,CACjB,IAAAC,GAAcC,GAAiCF,CAAG,EAClDC,IACHtI,EAAS,SAAA,CAAY,MAAOsI,GAAa,MAAO,IAAA,CAAA,iBAQnCE,GACdC,EAAA,CAEQ,OAAAA,EAAA,CACF,IAAA,YACJhB,EAAa,gBAAA,SAOZ,IAAAiB,WAEKC,GAAaN,EAAA,CACrBA,EAAI,eAAA,EACJA,EAAI,gBAAA,EACAA,EAAI,eACPA,EAAI,aAAa,WAAa,YAG/BD,EAAW,EAAA,iBAGGQ,GAAQP,EAAA,CACtBA,EAAI,eAAA,EACJA,EAAI,gBAAA,MACJD,EAAW,EAAA,EAEP5E,IACHwE,IACM,MAAAzB,GAAA,OAGPoB,EAAgB,QAAA,EACV,MAAApB,GAAA,EACNkB,EAAa,qBAAqBY,CAAG,4CAexBpF,GAAK,CACfW,EAAA,EAAAJ,EAAQ,IAAI,EACZxD,EAAS,OAAO,EAChBiD,EAAM,gBAAe,8CAeZwE,EAAYrB,kJAqBT,MAAAyC,GAAAnD,GAAMuC,EAAYvC,EAAE,OAAQ,QAAQ,EACrCoD,GAAApD,GAAMuC,EAAYvC,EAAE,OAAQ,QAAQ,oEAGpC,MAAAqD,GAAArD,GAAMuC,EAAYvC,EAAE,OAAQ,QAAQ,+GApDQgD,EAAetC,44BA9C1E,CAAQuB,GAAiBR,GACxBvD,EAAA,EAAA+D,EAAgBR,EAAQ,CAAC,CAAA,qBAzBvBvD,EAAA,GAAAoF,EAAmBlF,GAAa6D,IAAkB,QAAA,yBAC9CD,GAAA,CAAcsB,OAAkBxF,EAAQ,IAAA,mBAc5CxD,EAAS,OAAQoI,CAAQ"}