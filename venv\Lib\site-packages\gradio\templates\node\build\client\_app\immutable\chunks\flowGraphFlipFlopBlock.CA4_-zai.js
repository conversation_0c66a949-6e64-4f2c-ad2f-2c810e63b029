import{f as i}from"./KHR_interactivity.DEAVS2UW.js";import{c as l}from"./declarationMapper.UBCwU7BT.js";import{R as s}from"./index.BoI39RQH.js";class o extends i{constructor(t){super(t),this.onOn=this._registerSignalOutput("onOn"),this.onOff=this._registerSignalOutput("onOff"),this.value=this.registerDataOutput("value",l)}_execute(t,u){var e;let a=t._getExecutionVariable(this,"value",typeof((e=this.config)==null?void 0:e.startValue)=="boolean"?!this.config.startValue:!1);a=!a,t._setExecutionVariable(this,"value",a),this.value.setValue(a,t),a?this.onOn._activateSignal(t):this.onOff._activateSignal(t)}getClassName(){return"FlowGraphFlipFlopBlock"}}s("FlowGraphFlipFlopBlock",o);export{o as FlowGraphFlipFlopBlock};
//# sourceMappingURL=flowGraphFlipFlopBlock.CA4_-zai.js.map
