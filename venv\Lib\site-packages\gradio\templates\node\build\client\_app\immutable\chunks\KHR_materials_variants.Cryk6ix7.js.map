{"version": 3, "file": "KHR_materials_variants.Cryk6ix7.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_materials_variants.js"], "sourcesContent": ["import { G<PERSON>FLoader, ArrayItem } from \"../glTFLoader.js\";\nimport { Mesh } from \"@babylonjs/core/Meshes/mesh.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"KHR_materials_variants\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_variants/README.md)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_materials_variants {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /**\n     * Gets the list of available variant names for this asset.\n     * @param rootNode The glTF root node\n     * @returns the list of all the variant names for this model\n     */\n    static GetAvailableVariants(rootNode) {\n        const extensionMetadata = this._GetExtensionMetadata(rootNode);\n        if (!extensionMetadata) {\n            return [];\n        }\n        return Object.keys(extensionMetadata.variants);\n    }\n    /**\n     * Gets the list of available variant names for this asset.\n     * @param rootNode The glTF root node\n     * @returns the list of all the variant names for this model\n     */\n    getAvailableVariants(rootNode) {\n        return KHR_materials_variants.GetAvailableVariants(rootNode);\n    }\n    /**\n     * Select a variant given a variant name or a list of variant names.\n     * @param rootNode The glTF root node\n     * @param variantName The variant name(s) to select.\n     */\n    static SelectVariant(rootNode, variantName) {\n        const extensionMetadata = this._GetExtensionMetadata(rootNode);\n        if (!extensionMetadata) {\n            throw new Error(`Cannot select variant on a glTF mesh that does not have the ${NAME} extension`);\n        }\n        const select = (variantName) => {\n            const entries = extensionMetadata.variants[variantName];\n            if (entries) {\n                for (const entry of entries) {\n                    entry.mesh.material = entry.material;\n                }\n            }\n        };\n        if (variantName instanceof Array) {\n            for (const name of variantName) {\n                select(name);\n            }\n        }\n        else {\n            select(variantName);\n        }\n        extensionMetadata.lastSelected = variantName;\n    }\n    /**\n     * Select a variant given a variant name or a list of variant names.\n     * @param rootNode The glTF root node\n     * @param variantName The variant name(s) to select.\n     */\n    selectVariant(rootNode, variantName) {\n        KHR_materials_variants.SelectVariant(rootNode, variantName);\n    }\n    /**\n     * Reset back to the original before selecting a variant.\n     * @param rootNode The glTF root node\n     */\n    static Reset(rootNode) {\n        const extensionMetadata = this._GetExtensionMetadata(rootNode);\n        if (!extensionMetadata) {\n            throw new Error(`Cannot reset on a glTF mesh that does not have the ${NAME} extension`);\n        }\n        for (const entry of extensionMetadata.original) {\n            entry.mesh.material = entry.material;\n        }\n        extensionMetadata.lastSelected = null;\n    }\n    /**\n     * Reset back to the original before selecting a variant.\n     * @param rootNode The glTF root node\n     */\n    reset(rootNode) {\n        KHR_materials_variants.Reset(rootNode);\n    }\n    /**\n     * Gets the last selected variant name(s) or null if original.\n     * @param rootNode The glTF root node\n     * @returns The selected variant name(s).\n     */\n    static GetLastSelectedVariant(rootNode) {\n        const extensionMetadata = this._GetExtensionMetadata(rootNode);\n        if (!extensionMetadata) {\n            throw new Error(`Cannot get the last selected variant on a glTF mesh that does not have the ${NAME} extension`);\n        }\n        return extensionMetadata.lastSelected;\n    }\n    /**\n     * Gets the last selected variant name(s) or null if original.\n     * @param rootNode The glTF root node\n     * @returns The selected variant name(s).\n     */\n    getLastSelectedVariant(rootNode) {\n        return KHR_materials_variants.GetLastSelectedVariant(rootNode);\n    }\n    static _GetExtensionMetadata(rootNode) {\n        return rootNode?._internalMetadata?.gltf?.[NAME] || null;\n    }\n    /** @internal */\n    onLoading() {\n        const extensions = this._loader.gltf.extensions;\n        if (extensions && extensions[this.name]) {\n            const extension = extensions[this.name];\n            this._variants = extension.variants;\n        }\n    }\n    /** @internal */\n    onReady() {\n        const rootNode = this._loader.rootBabylonMesh;\n        if (rootNode) {\n            const options = this._loader.parent.extensionOptions[NAME];\n            if (options?.defaultVariant) {\n                KHR_materials_variants.SelectVariant(rootNode, options.defaultVariant);\n            }\n            options?.onLoaded?.({\n                get variants() {\n                    return KHR_materials_variants.GetAvailableVariants(rootNode);\n                },\n                get selectedVariant() {\n                    const lastSelectedVariant = KHR_materials_variants.GetLastSelectedVariant(rootNode);\n                    if (!lastSelectedVariant) {\n                        return KHR_materials_variants.GetAvailableVariants(rootNode)[0];\n                    }\n                    if (Array.isArray(lastSelectedVariant)) {\n                        return lastSelectedVariant[0];\n                    }\n                    return lastSelectedVariant;\n                },\n                set selectedVariant(variantName) {\n                    KHR_materials_variants.SelectVariant(rootNode, variantName);\n                },\n            });\n        }\n    }\n    /**\n     * @internal\n     */\n    _loadMeshPrimitiveAsync(context, name, node, mesh, primitive, assign) {\n        return GLTFLoader.LoadExtensionAsync(context, primitive, this.name, (extensionContext, extension) => {\n            const promises = new Array();\n            promises.push(this._loader._loadMeshPrimitiveAsync(context, name, node, mesh, primitive, (babylonMesh) => {\n                assign(babylonMesh);\n                if (babylonMesh instanceof Mesh) {\n                    const babylonDrawMode = GLTFLoader._GetDrawMode(context, primitive.mode);\n                    const root = this._loader.rootBabylonMesh;\n                    const metadata = root ? (root._internalMetadata = root._internalMetadata || {}) : {};\n                    const gltf = (metadata.gltf = metadata.gltf || {});\n                    const extensionMetadata = (gltf[NAME] = gltf[NAME] || { lastSelected: null, original: [], variants: {} });\n                    // Store the original material.\n                    extensionMetadata.original.push({ mesh: babylonMesh, material: babylonMesh.material });\n                    // For each mapping, look at the variants and make a new entry for them.\n                    for (let mappingIndex = 0; mappingIndex < extension.mappings.length; ++mappingIndex) {\n                        const mapping = extension.mappings[mappingIndex];\n                        const material = ArrayItem.Get(`${extensionContext}/mappings/${mappingIndex}/material`, this._loader.gltf.materials, mapping.material);\n                        promises.push(this._loader._loadMaterialAsync(`#/materials/${mapping.material}`, material, babylonMesh, babylonDrawMode, (babylonMaterial) => {\n                            for (let mappingVariantIndex = 0; mappingVariantIndex < mapping.variants.length; ++mappingVariantIndex) {\n                                const variantIndex = mapping.variants[mappingVariantIndex];\n                                const variant = ArrayItem.Get(`/extensions/${NAME}/variants/${variantIndex}`, this._variants, variantIndex);\n                                extensionMetadata.variants[variant.name] = extensionMetadata.variants[variant.name] || [];\n                                extensionMetadata.variants[variant.name].push({\n                                    mesh: babylonMesh,\n                                    material: babylonMaterial,\n                                });\n                                // Replace the target when original mesh is cloned\n                                babylonMesh.onClonedObservable.add((newOne) => {\n                                    const newMesh = newOne;\n                                    let metadata = null;\n                                    let newRoot = newMesh;\n                                    // Find root to get medata\n                                    do {\n                                        newRoot = newRoot.parent;\n                                        if (!newRoot) {\n                                            return;\n                                        }\n                                        metadata = KHR_materials_variants._GetExtensionMetadata(newRoot);\n                                    } while (metadata === null);\n                                    // Need to clone the metadata on the root (first time only)\n                                    if (root && metadata === KHR_materials_variants._GetExtensionMetadata(root)) {\n                                        // Copy main metadata\n                                        newRoot._internalMetadata = {};\n                                        for (const key in root._internalMetadata) {\n                                            newRoot._internalMetadata[key] = root._internalMetadata[key];\n                                        }\n                                        // Copy the gltf metadata\n                                        newRoot._internalMetadata.gltf = [];\n                                        for (const key in root._internalMetadata.gltf) {\n                                            newRoot._internalMetadata.gltf[key] = root._internalMetadata.gltf[key];\n                                        }\n                                        // Duplicate the extension specific metadata\n                                        newRoot._internalMetadata.gltf[NAME] = { lastSelected: null, original: [], variants: {} };\n                                        for (const original of metadata.original) {\n                                            newRoot._internalMetadata.gltf[NAME].original.push({\n                                                mesh: original.mesh,\n                                                material: original.material,\n                                            });\n                                        }\n                                        for (const key in metadata.variants) {\n                                            if (Object.prototype.hasOwnProperty.call(metadata.variants, key)) {\n                                                newRoot._internalMetadata.gltf[NAME].variants[key] = [];\n                                                for (const variantEntry of metadata.variants[key]) {\n                                                    newRoot._internalMetadata.gltf[NAME].variants[key].push({\n                                                        mesh: variantEntry.mesh,\n                                                        material: variantEntry.material,\n                                                    });\n                                                }\n                                            }\n                                        }\n                                        metadata = newRoot._internalMetadata.gltf[NAME];\n                                    }\n                                    // Relocate\n                                    for (const target of metadata.original) {\n                                        if (target.mesh === babylonMesh) {\n                                            target.mesh = newMesh;\n                                        }\n                                    }\n                                    for (const target of metadata.variants[variant.name]) {\n                                        if (target.mesh === babylonMesh) {\n                                            target.mesh = newMesh;\n                                        }\n                                    }\n                                });\n                            }\n                        }));\n                    }\n                }\n            }));\n            return Promise.all(promises).then(([babylonMesh]) => {\n                return babylonMesh;\n            });\n        });\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_materials_variants(loader));\n//# sourceMappingURL=KHR_materials_variants.js.map"], "names": ["NAME", "KHR_materials_variants", "loader", "rootNode", "extensionMetadata", "variantName", "select", "entries", "entry", "name", "_b", "_a", "extensions", "extension", "options", "lastSelectedVariant", "context", "node", "mesh", "primitive", "assign", "GLTFLoader", "extensionContext", "promises", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "babylonDrawMode", "root", "metadata", "gltf", "mappingIndex", "mapping", "material", "ArrayItem", "babylonMaterial", "mappingVariantIndex", "variantIndex", "variant", "newOne", "new<PERSON><PERSON>", "newRoot", "key", "original", "variantEntry", "target", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "8HAGA,MAAMA,EAAO,yBAKN,MAAMC,CAAuB,CAIhC,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EACZ,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,CACnD,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAMD,OAAO,qBAAqBG,EAAU,CAClC,MAAMC,EAAoB,KAAK,sBAAsBD,CAAQ,EAC7D,OAAKC,EAGE,OAAO,KAAKA,EAAkB,QAAQ,EAFlC,EAGd,CAMD,qBAAqBD,EAAU,CAC3B,OAAOF,EAAuB,qBAAqBE,CAAQ,CAC9D,CAMD,OAAO,cAAcA,EAAUE,EAAa,CACxC,MAAMD,EAAoB,KAAK,sBAAsBD,CAAQ,EAC7D,GAAI,CAACC,EACD,MAAM,IAAI,MAAM,+DAA+DJ,CAAI,YAAY,EAEnG,MAAMM,EAAUD,GAAgB,CAC5B,MAAME,EAAUH,EAAkB,SAASC,CAAW,EACtD,GAAIE,EACA,UAAWC,KAASD,EAChBC,EAAM,KAAK,SAAWA,EAAM,QAGhD,EACQ,GAAIH,aAAuB,MACvB,UAAWI,KAAQJ,EACfC,EAAOG,CAAI,OAIfH,EAAOD,CAAW,EAEtBD,EAAkB,aAAeC,CACpC,CAMD,cAAcF,EAAUE,EAAa,CACjCJ,EAAuB,cAAcE,EAAUE,CAAW,CAC7D,CAKD,OAAO,MAAMF,EAAU,CACnB,MAAMC,EAAoB,KAAK,sBAAsBD,CAAQ,EAC7D,GAAI,CAACC,EACD,MAAM,IAAI,MAAM,sDAAsDJ,CAAI,YAAY,EAE1F,UAAWQ,KAASJ,EAAkB,SAClCI,EAAM,KAAK,SAAWA,EAAM,SAEhCJ,EAAkB,aAAe,IACpC,CAKD,MAAMD,EAAU,CACZF,EAAuB,MAAME,CAAQ,CACxC,CAMD,OAAO,uBAAuBA,EAAU,CACpC,MAAMC,EAAoB,KAAK,sBAAsBD,CAAQ,EAC7D,GAAI,CAACC,EACD,MAAM,IAAI,MAAM,8EAA8EJ,CAAI,YAAY,EAElH,OAAOI,EAAkB,YAC5B,CAMD,uBAAuBD,EAAU,CAC7B,OAAOF,EAAuB,uBAAuBE,CAAQ,CAChE,CACD,OAAO,sBAAsBA,EAAU,SACnC,QAAOO,GAAAC,EAAAR,GAAA,YAAAA,EAAU,oBAAV,YAAAQ,EAA6B,OAA7B,YAAAD,EAAoCV,KAAS,IACvD,CAED,WAAY,CACR,MAAMY,EAAa,KAAK,QAAQ,KAAK,WACrC,GAAIA,GAAcA,EAAW,KAAK,IAAI,EAAG,CACrC,MAAMC,EAAYD,EAAW,KAAK,IAAI,EACtC,KAAK,UAAYC,EAAU,QAC9B,CACJ,CAED,SAAU,OACN,MAAMV,EAAW,KAAK,QAAQ,gBAC9B,GAAIA,EAAU,CACV,MAAMW,EAAU,KAAK,QAAQ,OAAO,iBAAiBd,CAAI,EACrDc,GAAA,MAAAA,EAAS,gBACTb,EAAuB,cAAcE,EAAUW,EAAQ,cAAc,GAEzEH,EAAAG,GAAA,YAAAA,EAAS,WAAT,MAAAH,EAAA,KAAAG,EAAoB,CAChB,IAAI,UAAW,CACX,OAAOb,EAAuB,qBAAqBE,CAAQ,CAC9D,EACD,IAAI,iBAAkB,CAClB,MAAMY,EAAsBd,EAAuB,uBAAuBE,CAAQ,EAClF,OAAKY,EAGD,MAAM,QAAQA,CAAmB,EAC1BA,EAAoB,CAAC,EAEzBA,EALId,EAAuB,qBAAqBE,CAAQ,EAAE,CAAC,CAMrE,EACD,IAAI,gBAAgBE,EAAa,CAC7BJ,EAAuB,cAAcE,EAAUE,CAAW,CAC7D,CACjB,EACS,CACJ,CAID,wBAAwBW,EAASP,EAAMQ,EAAMC,EAAMC,EAAWC,EAAQ,CAClE,OAAOC,EAAW,mBAAmBL,EAASG,EAAW,KAAK,KAAM,CAACG,EAAkBT,IAAc,CACjG,MAAMU,EAAW,IAAI,MACrB,OAAAA,EAAS,KAAK,KAAK,QAAQ,wBAAwBP,EAASP,EAAMQ,EAAMC,EAAMC,EAAYK,GAAgB,CAEtG,GADAJ,EAAOI,CAAW,EACdA,aAAuBC,EAAM,CAC7B,MAAMC,EAAkBL,EAAW,aAAaL,EAASG,EAAU,IAAI,EACjEQ,EAAO,KAAK,QAAQ,gBACpBC,EAAWD,EAAQA,EAAK,kBAAoBA,EAAK,mBAAqB,CAAE,EAAI,GAC5EE,EAAQD,EAAS,KAAOA,EAAS,MAAQ,CAAA,EACzCxB,EAAqByB,EAAK7B,CAAI,EAAI6B,EAAK7B,CAAI,GAAK,CAAE,aAAc,KAAM,SAAU,CAAA,EAAI,SAAU,CAAA,CAAI,EAExGI,EAAkB,SAAS,KAAK,CAAE,KAAMoB,EAAa,SAAUA,EAAY,QAAQ,CAAE,EAErF,QAASM,EAAe,EAAGA,EAAejB,EAAU,SAAS,OAAQ,EAAEiB,EAAc,CACjF,MAAMC,EAAUlB,EAAU,SAASiB,CAAY,EACzCE,EAAWC,EAAU,IAAI,GAAGX,CAAgB,aAAaQ,CAAY,YAAa,KAAK,QAAQ,KAAK,UAAWC,EAAQ,QAAQ,EACrIR,EAAS,KAAK,KAAK,QAAQ,mBAAmB,eAAeQ,EAAQ,QAAQ,GAAIC,EAAUR,EAAaE,EAAkBQ,GAAoB,CAC1I,QAASC,EAAsB,EAAGA,EAAsBJ,EAAQ,SAAS,OAAQ,EAAEI,EAAqB,CACpG,MAAMC,EAAeL,EAAQ,SAASI,CAAmB,EACnDE,EAAUJ,EAAU,IAAI,eAAejC,CAAI,aAAaoC,CAAY,GAAI,KAAK,UAAWA,CAAY,EAC1GhC,EAAkB,SAASiC,EAAQ,IAAI,EAAIjC,EAAkB,SAASiC,EAAQ,IAAI,GAAK,GACvFjC,EAAkB,SAASiC,EAAQ,IAAI,EAAE,KAAK,CAC1C,KAAMb,EACN,SAAUU,CAC9C,CAAiC,EAEDV,EAAY,mBAAmB,IAAKc,GAAW,CAC3C,MAAMC,EAAUD,EAChB,IAAIV,EAAW,KACXY,EAAUD,EAEd,EAAG,CAEC,GADAC,EAAUA,EAAQ,OACd,CAACA,EACD,OAEJZ,EAAW3B,EAAuB,sBAAsBuC,CAAO,CACvG,OAA6CZ,IAAa,MAEtB,GAAID,GAAQC,IAAa3B,EAAuB,sBAAsB0B,CAAI,EAAG,CAEzEa,EAAQ,kBAAoB,GAC5B,UAAWC,KAAOd,EAAK,kBACnBa,EAAQ,kBAAkBC,CAAG,EAAId,EAAK,kBAAkBc,CAAG,EAG/DD,EAAQ,kBAAkB,KAAO,GACjC,UAAWC,KAAOd,EAAK,kBAAkB,KACrCa,EAAQ,kBAAkB,KAAKC,CAAG,EAAId,EAAK,kBAAkB,KAAKc,CAAG,EAGzED,EAAQ,kBAAkB,KAAKxC,CAAI,EAAI,CAAE,aAAc,KAAM,SAAU,CAAA,EAAI,SAAU,CAAE,CAAA,EACvF,UAAW0C,KAAYd,EAAS,SAC5BY,EAAQ,kBAAkB,KAAKxC,CAAI,EAAE,SAAS,KAAK,CAC/C,KAAM0C,EAAS,KACf,SAAUA,EAAS,QACnE,CAA6C,EAEL,UAAWD,KAAOb,EAAS,SACvB,GAAI,OAAO,UAAU,eAAe,KAAKA,EAAS,SAAUa,CAAG,EAAG,CAC9DD,EAAQ,kBAAkB,KAAKxC,CAAI,EAAE,SAASyC,CAAG,EAAI,GACrD,UAAWE,KAAgBf,EAAS,SAASa,CAAG,EAC5CD,EAAQ,kBAAkB,KAAKxC,CAAI,EAAE,SAASyC,CAAG,EAAE,KAAK,CACpD,KAAME,EAAa,KACnB,SAAUA,EAAa,QAC/E,CAAqD,CAER,CAELf,EAAWY,EAAQ,kBAAkB,KAAKxC,CAAI,CACjD,CAED,UAAW4C,KAAUhB,EAAS,SACtBgB,EAAO,OAASpB,IAChBoB,EAAO,KAAOL,GAGtB,UAAWK,KAAUhB,EAAS,SAASS,EAAQ,IAAI,EAC3CO,EAAO,OAASpB,IAChBoB,EAAO,KAAOL,EAG1D,CAAiC,CACJ,CACJ,CAAA,CAAC,CACL,CACJ,CACJ,CAAA,CAAC,EACK,QAAQ,IAAIhB,CAAQ,EAAE,KAAK,CAAC,CAACC,CAAW,IACpCA,CACV,CACb,CAAS,CACJ,CACL,CACAqB,EAAwB7C,CAAI,EAC5B8C,EAAsB9C,EAAM,GAAOE,GAAW,IAAID,EAAuBC,CAAM,CAAC", "x_google_ignoreList": [0]}