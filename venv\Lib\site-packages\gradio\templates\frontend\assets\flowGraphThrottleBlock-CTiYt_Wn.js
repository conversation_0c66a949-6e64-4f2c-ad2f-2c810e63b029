import{b as l}from"./declarationMapper-BZjsjg7g.js";import{b as u}from"./KHR_interactivity-DTxiAnOo.js";import{R as o}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class h extends u{constructor(i){super(i),this.reset=this._registerSignalInput("reset"),this.duration=this.registerDataInput("duration",l),this.lastRemainingTime=this.registerDataOutput("lastRemainingTime",l,NaN)}_execute(i,n){if(n===this.reset){this.lastRemainingTime.setValue(NaN,i),i._setExecutionVariable(this,"lastRemainingTime",NaN),i._setExecutionVariable(this,"timestamp",0);return}const e=this.duration.getValue(i);if(e<=0||isNaN(e)||!isFinite(e))return this._reportError(i,"Invalid duration in Throttle block");const m=i._getExecutionVariable(this,"lastRemainingTime",NaN),t=Date.now();if(isNaN(m))return this.lastRemainingTime.setValue(0,i),i._setExecutionVariable(this,"lastRemainingTime",0),i._setExecutionVariable(this,"timestamp",t),this.out._activateSignal(i);{const a=t-i._getExecutionVariable(this,"timestamp",0),s=e*1e3;if(s<=a)return this.lastRemainingTime.setValue(0,i),i._setExecutionVariable(this,"lastRemainingTime",0),i._setExecutionVariable(this,"timestamp",t),this.out._activateSignal(i);{const r=s-a;this.lastRemainingTime.setValue(r/1e3,i),i._setExecutionVariable(this,"lastRemainingTime",r)}}}getClassName(){return"FlowGraphThrottleBlock"}}o("FlowGraphThrottleBlock",h);export{h as FlowGraphThrottleBlock};
//# sourceMappingURL=flowGraphThrottleBlock-CTiYt_Wn.js.map
