import{SvelteComponent as u,init as y,safe_not_equal as _,element as v,text as g,claim_element as b,children as q,claim_text as A,detach as m,attr as E,toggle_class as s,insert_hydration as j,append_hydration as k,noop as o}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";/* empty css                                              */function C(i){let e,n;return{c(){e=v("div"),n=g(i[2]),this.h()},l(t){e=b(t,"DIV",{class:!0});var a=q(e);n=A(a,i[2]),a.forEach(m),this.h()},h(){E(e,"class","svelte-1ayixqk"),s(e,"table",i[0]==="table"),s(e,"gallery",i[0]==="gallery"),s(e,"selected",i[1])},m(t,a){j(t,e,a),k(e,n)},p(t,[a]){a&1&&s(e,"table",t[0]==="table"),a&1&&s(e,"gallery",t[0]==="gallery"),a&2&&s(e,"selected",t[1])},i:o,o,d(t){t&&m(e)}}}function D(i,e,n){let{value:t}=e,{type:a}=e,{selected:r=!1}=e,{choices:c}=e,d=(t?Array.isArray(t)?t:[t]:[]).map(l=>{var f;return(f=c.find(h=>h[1]===l))==null?void 0:f[0]}).filter(l=>l!==void 0).join(", ");return i.$$set=l=>{"value"in l&&n(3,t=l.value),"type"in l&&n(0,a=l.type),"selected"in l&&n(1,r=l.selected),"choices"in l&&n(4,c=l.choices)},[a,r,d,t,c]}class z extends u{constructor(e){super(),y(this,e,D,C,_,{value:3,type:0,selected:1,choices:4})}}export{z as default};
//# sourceMappingURL=Example.AaUiXLZ_.js.map
