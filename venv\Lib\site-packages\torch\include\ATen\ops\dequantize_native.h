#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>


namespace at {
namespace native {
TORCH_API at::Tensor & dequantize_self_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor dequantize_cpu_or_cuda(const at::Tensor & self);
TORCH_API at::Tensor dequantize_quantized(const at::Tensor & self);
TORCH_API void dequantize_tensors_out(at::TensorList tensors, at::TensorList out);
TORCH_API ::std::vector<at::Tensor> dequantize_tensors_quantized_cpu(at::TensorList tensors);
} // namespace native
} // namespace at
