"""
Agents Hub页面
卡片式展示所有可用的Agent，替换下拉菜单选择方式
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QScrollArea, QFrame, QGridLayout,
                            QTextEdit, QDialog, QDialogButtonBox)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap

from personas.persona_manager import persona_manager
from core.utils.logger import get_logger

logger = get_logger(__name__)

class AgentDetailDialog(QDialog):
    """Agent详情对话框"""
    
    agent_selected = pyqtSignal(str)  # persona_id
    
    def __init__(self, persona_id: str, persona_info: dict, parent=None):
        super().__init__(parent)
        self.persona_id = persona_id
        self.persona_info = persona_info
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle(f"Agent详情 - {self.persona_info['name']}")
        self.setModal(True)
        self.resize(600, 700)
        
        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1a1f2e, stop:1 #2d3548);
                color: #e2e8f0;
            }
            QPushButton {
                background: rgba(102, 126, 234, 0.8);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: 500;
            }
            QPushButton:hover {
                background: rgba(102, 126, 234, 1.0);
            }
            QPushButton#select_button {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #06d6a0, stop:1 #39d353);
            }
            QPushButton#select_button:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #059669, stop:1 #16a34a);
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # 头部信息
        header_layout = QHBoxLayout()
        
        # Agent头像
        avatar_label = QLabel()
        avatar_label.setFixedSize(80, 80)
        avatar_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 40px;
                border: 3px solid rgba(255,255,255,0.2);
                font-size: 32px;
                color: white;
            }
        """)
        avatar_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 设置头像
        avatar_path = self.persona_info.get('avatar_path')
        if avatar_path:
            try:
                pixmap = QPixmap(avatar_path)
                if not pixmap.isNull():
                    scaled_pixmap = pixmap.scaled(74, 74, Qt.AspectRatioMode.KeepAspectRatio,
                                                Qt.TransformationMode.SmoothTransformation)
                    avatar_label.setPixmap(scaled_pixmap)
                else:
                    avatar_label.setText("🤖")
            except:
                avatar_label.setText("🤖")
        else:
            avatar_label.setText("🤖")
            
        header_layout.addWidget(avatar_label)
        
        # Agent信息
        info_layout = QVBoxLayout()
        
        # 名称
        name_label = QLabel(self.persona_info['name'])
        name_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: 700;
                color: #e2e8f0;
                margin-bottom: 5px;
            }
        """)
        info_layout.addWidget(name_label)
        
        # 标签
        tags_layout = QHBoxLayout()
        
        # NSFW标签
        if self.persona_info.get('nsfw_enabled', False):
            nsfw_label = QLabel("🔞 NSFW")
            nsfw_label.setStyleSheet("""
                QLabel {
                    background: rgba(239, 68, 68, 0.8);
                    color: white;
                    border-radius: 12px;
                    padding: 4px 8px;
                    font-size: 11px;
                    font-weight: 600;
                }
            """)
            tags_layout.addWidget(nsfw_label)
        
        # 类型标签
        persona_type = self.persona_info.get('type', 'assistant')
        type_label = QLabel(f"📋 {persona_type}")
        type_label.setStyleSheet("""
            QLabel {
                background: rgba(102, 126, 234, 0.6);
                color: white;
                border-radius: 12px;
                padding: 4px 8px;
                font-size: 11px;
                font-weight: 600;
            }
        """)
        tags_layout.addWidget(type_label)
        
        tags_layout.addStretch()
        info_layout.addLayout(tags_layout)
        
        header_layout.addLayout(info_layout, 1)
        layout.addLayout(header_layout)
        
        # 简短描述
        if 'description' in self.persona_info:
            desc_label = QLabel(self.persona_info['description'])
            desc_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #94a3b8;
                    font-style: italic;
                    line-height: 1.4;
                }
            """)
            desc_label.setWordWrap(True)
            layout.addWidget(desc_label)
        
        # 详细介绍
        detail_label = QLabel("详细介绍")
        detail_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: 600;
                color: #e2e8f0;
                margin-top: 10px;
                margin-bottom: 5px;
            }
        """)
        layout.addWidget(detail_label)
        
        # 详细内容
        detail_text = QTextEdit()
        detail_text.setReadOnly(True)
        detail_text.setMaximumHeight(200)
        detail_text.setStyleSheet("""
            QTextEdit {
                background: rgba(45, 53, 72, 0.6);
                border: 1px solid rgba(102, 126, 234, 0.3);
                border-radius: 8px;
                padding: 15px;
                font-size: 13px;
                color: #e2e8f0;
                line-height: 1.5;
            }
        """)
        
        # 设置详细内容
        detail_content = self.persona_info.get('prompt', '暂无详细介绍')
        detail_text.setPlainText(detail_content)
        layout.addWidget(detail_text)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 取消按钮
        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)
        
        # 选择按钮
        select_button = QPushButton("选择此Agent")
        select_button.setObjectName("select_button")
        select_button.clicked.connect(self.select_agent)
        button_layout.addWidget(select_button)
        
        layout.addLayout(button_layout)
        
    def select_agent(self):
        """选择Agent"""
        self.agent_selected.emit(self.persona_id)
        self.accept()

class AgentCard(QFrame):
    """Agent卡片"""
    
    clicked = pyqtSignal(str)  # persona_id
    
    def __init__(self, persona_id: str, persona_info: dict):
        super().__init__()
        self.persona_id = persona_id
        self.persona_info = persona_info
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setObjectName("agent_card")
        self.setFixedSize(280, 200)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # 设置卡片样式
        self.setStyleSheet("""
            QFrame#agent_card {
                background: rgba(45, 53, 72, 0.8);
                border: 2px solid rgba(102, 126, 234, 0.3);
                border-radius: 16px;
                padding: 0;
            }
            QFrame#agent_card:hover {
                background: rgba(45, 53, 72, 0.9);
                border: 2px solid rgba(102, 126, 234, 0.6);
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(12)
        
        # 头部区域
        header_layout = QHBoxLayout()
        
        # Agent头像
        avatar_label = QLabel()
        avatar_label.setFixedSize(50, 50)
        avatar_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 25px;
                border: 2px solid rgba(255,255,255,0.2);
                font-size: 20px;
                color: white;
            }
        """)
        avatar_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 设置头像
        avatar_path = self.persona_info.get('avatar_path')
        if avatar_path:
            try:
                pixmap = QPixmap(avatar_path)
                if not pixmap.isNull():
                    scaled_pixmap = pixmap.scaled(46, 46, Qt.AspectRatioMode.KeepAspectRatio,
                                                Qt.TransformationMode.SmoothTransformation)
                    avatar_label.setPixmap(scaled_pixmap)
                else:
                    avatar_label.setText("🤖")
            except:
                avatar_label.setText("🤖")
        else:
            avatar_label.setText("🤖")
            
        header_layout.addWidget(avatar_label)
        
        # 名称和标签
        info_layout = QVBoxLayout()
        
        # 名称
        name_label = QLabel(self.persona_info['name'])
        name_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: 600;
                color: #e2e8f0;
            }
        """)
        info_layout.addWidget(name_label)
        
        # 标签
        tags_layout = QHBoxLayout()
        tags_layout.setSpacing(5)
        
        if self.persona_info.get('nsfw_enabled', False):
            nsfw_label = QLabel("🔞")
            nsfw_label.setFixedSize(20, 20)
            nsfw_label.setStyleSheet("""
                QLabel {
                    background: rgba(239, 68, 68, 0.8);
                    border-radius: 10px;
                    font-size: 10px;
                    color: white;
                }
            """)
            nsfw_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            tags_layout.addWidget(nsfw_label)
        
        tags_layout.addStretch()
        info_layout.addLayout(tags_layout)
        
        header_layout.addLayout(info_layout, 1)
        layout.addLayout(header_layout)
        
        # 描述
        desc_text = self.persona_info.get('description', '暂无描述')
        if len(desc_text) > 80:
            desc_text = desc_text[:80] + "..."
            
        desc_label = QLabel(desc_text)
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #94a3b8;
                line-height: 1.4;
            }
        """)
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label, 1)
        
        # 查看详情按钮
        view_button = QPushButton("查看详情")
        view_button.setStyleSheet("""
            QPushButton {
                background: rgba(102, 126, 234, 0.6);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: 500;
            }
            QPushButton:hover {
                background: rgba(102, 126, 234, 0.8);
            }
        """)
        view_button.clicked.connect(self.on_clicked)
        layout.addWidget(view_button)
        
    def on_clicked(self):
        """卡片点击事件"""
        self.clicked.emit(self.persona_id)
        
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit(self.persona_id)
        super().mousePressEvent(event)

class AgentsHub(QWidget):
    """Agents Hub主页面"""

    agent_selected = pyqtSignal(str)  # persona_id
    back_requested = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.agent_cards = {}
        self.init_ui()
        self.load_agents()

    def init_ui(self):
        """初始化UI"""
        self.setObjectName("agents_hub")

        # 设置样式
        self.setStyleSheet("""
            QWidget#agents_hub {
                background: transparent;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # 头部区域
        header_layout = QHBoxLayout()

        # 返回按钮
        back_button = QPushButton("← 返回聊天")
        back_button.setStyleSheet("""
            QPushButton {
                background: rgba(102, 126, 234, 0.8);
                color: white;
                border: none;
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: 500;
            }
            QPushButton:hover {
                background: rgba(102, 126, 234, 1.0);
            }
        """)
        back_button.clicked.connect(self.back_requested.emit)
        header_layout.addWidget(back_button)

        header_layout.addStretch()

        # 刷新按钮
        refresh_button = QPushButton("🔄 刷新")
        refresh_button.setStyleSheet("""
            QPushButton {
                background: rgba(45, 53, 72, 0.8);
                color: #e2e8f0;
                border: 1px solid rgba(102, 126, 234, 0.3);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: 500;
            }
            QPushButton:hover {
                background: rgba(45, 53, 72, 1.0);
                border: 1px solid rgba(102, 126, 234, 0.6);
            }
        """)
        refresh_button.clicked.connect(self.load_agents)
        header_layout.addWidget(refresh_button)

        layout.addLayout(header_layout)

        # 标题
        title_label = QLabel("🌟 Agents Hub")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: 700;
                color: #e2e8f0;
                margin: 10px 0;
            }
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # 副标题
        subtitle_label = QLabel("选择您的AI助手")
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #94a3b8;
                margin-bottom: 20px;
            }
        """)
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(subtitle_label)

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background: transparent;
                border: none;
            }
            QScrollBar:vertical {
                background: rgba(45, 53, 72, 0.5);
                width: 12px;
                border-radius: 6px;
                margin: 0;
            }
            QScrollBar::handle:vertical {
                background: rgba(102, 126, 234, 0.6);
                border-radius: 6px;
                min-height: 20px;
                margin: 2px;
            }
            QScrollBar::handle:vertical:hover {
                background: rgba(102, 126, 234, 0.8);
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0;
                width: 0;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
        """)

        # 卡片容器
        self.cards_widget = QWidget()
        self.cards_layout = QGridLayout(self.cards_widget)
        self.cards_layout.setContentsMargins(10, 10, 10, 10)
        self.cards_layout.setSpacing(20)

        scroll_area.setWidget(self.cards_widget)
        layout.addWidget(scroll_area, 1)

    def load_agents(self):
        """加载所有Agent"""
        try:
            # 清除现有卡片
            self.clear_cards()

            # 获取所有人设
            personas = persona_manager.get_available_personas()

            # 创建卡片
            row = 0
            col = 0
            max_cols = 3  # 每行最多3个卡片

            for persona_id, persona_info in personas.items():
                card = AgentCard(persona_id, persona_info)
                card.clicked.connect(self.show_agent_detail)

                self.cards_layout.addWidget(card, row, col)
                self.agent_cards[persona_id] = card

                col += 1
                if col >= max_cols:
                    col = 0
                    row += 1

            # 添加弹簧以保持布局
            self.cards_layout.setRowStretch(row + 1, 1)

            logger.info(f"已加载 {len(personas)} 个Agent")

        except Exception as e:
            logger.error(f"加载Agent时出错: {e}")

    def clear_cards(self):
        """清除所有卡片"""
        for card in self.agent_cards.values():
            self.cards_layout.removeWidget(card)
            card.deleteLater()
        self.agent_cards.clear()

    def show_agent_detail(self, persona_id: str):
        """显示Agent详情"""
        try:
            personas = persona_manager.get_available_personas()
            if persona_id in personas:
                persona_info = personas[persona_id]

                detail_dialog = AgentDetailDialog(persona_id, persona_info, self)
                detail_dialog.agent_selected.connect(self.agent_selected.emit)
                detail_dialog.exec()

        except Exception as e:
            logger.error(f"显示Agent详情时出错: {e}")
