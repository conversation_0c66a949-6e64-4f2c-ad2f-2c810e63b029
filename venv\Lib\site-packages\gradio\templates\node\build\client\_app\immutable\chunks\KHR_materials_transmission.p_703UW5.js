import{ar as h,an as l,ao as c,O as _,b as f,as as T}from"./index.BoI39RQH.js";import{GLTFLoader as g}from"./glTFLoader.BetPWe9U.js";import{C as m}from"./objectModelMapping.ha_8hIyl.js";class u{static _GetDefaultOptions(){return{renderSize:1024,samples:4,lodGenerationScale:1,lodGenerationOffset:-4,renderTargetTextureType:m.TEXTURETYPE_HALF_FLOAT,generateMipmaps:!0}}constructor(e,t){this._opaqueRenderTarget=null,this._opaqueMeshesCache=[],this._transparentMeshesCache=[],this._materialObservers={},this._options={...u._GetDefaultOptions(),...e},this._scene=t,this._scene._transmissionHelper=this,this.onErrorObservable=new _,this._scene.onDisposeObservable.addOnce(()=>{this.dispose()}),this._parseScene(),this._setupRenderTargets()}updateOptions(e){if(!Object.keys(e).filter(r=>this._options[r]!==e[r]).length)return;const s={...this._options,...e},a=this._options;this._options=s,s.renderSize!==a.renderSize||s.renderTargetTextureType!==a.renderTargetTextureType||s.generateMipmaps!==a.generateMipmaps||!this._opaqueRenderTarget?this._setupRenderTargets():(this._opaqueRenderTarget.samples=s.samples,this._opaqueRenderTarget.lodGenerationScale=s.lodGenerationScale,this._opaqueRenderTarget.lodGenerationOffset=s.lodGenerationOffset)}getOpaqueTarget(){return this._opaqueRenderTarget}_shouldRenderAsTransmission(e){return e?!!(e instanceof h&&e.subSurface.isRefractionEnabled):!1}_addMesh(e){this._materialObservers[e.uniqueId]=e.onMaterialChangedObservable.add(this._onMeshMaterialChanged.bind(this)),f.SetImmediate(()=>{this._shouldRenderAsTransmission(e.material)?(e.material.refractionTexture=this._opaqueRenderTarget,this._transparentMeshesCache.indexOf(e)===-1&&this._transparentMeshesCache.push(e)):this._opaqueMeshesCache.indexOf(e)===-1&&this._opaqueMeshesCache.push(e)})}_removeMesh(e){e.onMaterialChangedObservable.remove(this._materialObservers[e.uniqueId]),delete this._materialObservers[e.uniqueId];let t=this._transparentMeshesCache.indexOf(e);t!==-1&&this._transparentMeshesCache.splice(t,1),t=this._opaqueMeshesCache.indexOf(e),t!==-1&&this._opaqueMeshesCache.splice(t,1)}_parseScene(){this._scene.meshes.forEach(this._addMesh.bind(this)),this._scene.onNewMeshAddedObservable.add(this._addMesh.bind(this)),this._scene.onMeshRemovedObservable.add(this._removeMesh.bind(this))}_onMeshMaterialChanged(e){const t=this._transparentMeshesCache.indexOf(e),s=this._opaqueMeshesCache.indexOf(e);this._shouldRenderAsTransmission(e.material)?(e.material instanceof h&&(e.material.subSurface.refractionTexture=this._opaqueRenderTarget),s!==-1?(this._opaqueMeshesCache.splice(s,1),this._transparentMeshesCache.push(e)):t===-1&&this._transparentMeshesCache.push(e)):t!==-1?(this._transparentMeshesCache.splice(t,1),this._opaqueMeshesCache.push(e)):s===-1&&this._opaqueMeshesCache.push(e)}_isRenderTargetValid(){var e;return((e=this._opaqueRenderTarget)==null?void 0:e.getInternalTexture())!==null}_setupRenderTargets(){var t;this._opaqueRenderTarget&&this._opaqueRenderTarget.dispose(),this._opaqueRenderTarget=new T("opaqueSceneTexture",this._options.renderSize,this._scene,this._options.generateMipmaps,void 0,this._options.renderTargetTextureType),this._opaqueRenderTarget.ignoreCameraViewport=!0,this._opaqueRenderTarget.renderList=this._opaqueMeshesCache,this._opaqueRenderTarget.clearColor=((t=this._options.clearColor)==null?void 0:t.clone())??this._scene.clearColor.clone(),this._opaqueRenderTarget.gammaSpace=!1,this._opaqueRenderTarget.lodGenerationScale=this._options.lodGenerationScale,this._opaqueRenderTarget.lodGenerationOffset=this._options.lodGenerationOffset,this._opaqueRenderTarget.samples=this._options.samples,this._opaqueRenderTarget.renderSprites=!0,this._opaqueRenderTarget.renderParticles=!0,this._opaqueRenderTarget.disableImageProcessing=!0;let e;this._opaqueRenderTarget.onBeforeBindObservable.add(s=>{e=this._scene.environmentIntensity,this._scene.environmentIntensity=1,this._options.clearColor?s.clearColor.copyFrom(this._options.clearColor):this._scene.clearColor.toLinearSpaceToRef(s.clearColor,this._scene.getEngine().useExactSrgbConversions)}),this._opaqueRenderTarget.onAfterUnbindObservable.add(()=>{this._scene.environmentIntensity=e}),this._transparentMeshesCache.forEach(s=>{this._shouldRenderAsTransmission(s.material)&&(s.material.refractionTexture=this._opaqueRenderTarget)})}dispose(){this._scene._transmissionHelper=void 0,this._opaqueRenderTarget&&(this._opaqueRenderTarget.dispose(),this._opaqueRenderTarget=null),this._transparentMeshesCache=[],this._opaqueMeshesCache=[]}}const o="KHR_materials_transmission";class R{constructor(e){this.name=o,this.order=175,this._loader=e,this.enabled=this._loader.isExtensionUsed(o),this.enabled&&(e.parent.transparencyAsCoverage=!0)}dispose(){this._loader=null}loadMaterialPropertiesAsync(e,t,s){return g.LoadExtensionAsync(e,t,this.name,(a,r)=>{const n=new Array;return n.push(this._loader.loadMaterialPropertiesAsync(e,t,s)),n.push(this._loadTransparentPropertiesAsync(a,t,s,r)),Promise.all(n).then(()=>{})})}_loadTransparentPropertiesAsync(e,t,s,a){var n,p;if(!(s instanceof h))throw new Error(`${e}: Material type not supported`);const r=s;if(r.subSurface.isRefractionEnabled=!0,r.subSurface.volumeIndexOfRefraction=1,r.subSurface.useAlbedoToTintRefraction=!0,a.transmissionFactor!==void 0){r.subSurface.refractionIntensity=a.transmissionFactor;const i=r.getScene();r.subSurface.refractionIntensity&&!i._transmissionHelper?new u({},r.getScene()):r.subSurface.refractionIntensity&&!((n=i._transmissionHelper)!=null&&n._isRenderTargetValid())&&((p=i._transmissionHelper)==null||p._setupRenderTargets())}else return r.subSurface.refractionIntensity=0,r.subSurface.isRefractionEnabled=!1,Promise.resolve();return r.subSurface.minimumThickness=0,r.subSurface.maximumThickness=0,a.transmissionTexture?(a.transmissionTexture.nonColorData=!0,this._loader.loadTextureInfoAsync(`${e}/transmissionTexture`,a.transmissionTexture,void 0).then(i=>{i.name=`${s.name} (Transmission)`,r.subSurface.refractionIntensityTexture=i,r.subSurface.useGltfStyleTextures=!0})):Promise.resolve()}}l(o);c(o,!0,d=>new R(d));export{R as KHR_materials_transmission};
//# sourceMappingURL=KHR_materials_transmission.p_703UW5.js.map
