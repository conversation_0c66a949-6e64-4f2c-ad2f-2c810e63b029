/*
 Custom element styles
 */
gradio-lite {
	display: flex;
}

/*
	To avoid FOUC of custom elements
	Refs:
	https://www.abeautifulsite.net/posts/flash-of-undefined-custom-elements/#the-%3Adefined-selector
	https://github.com/pyscript/pypercard/blob/66d3550abd8e478f9389e6b87790d5985e55ef7f/static/pyscript.css#L6-L8
	*/
gradio-lite:not(:defined) {
	display: none;
}

.scroll-hide {
	-ms-overflow-style: none;
	scrollbar-width: none;
}

.sr-only {
	clip: rect(0, 0, 0, 0);
	position: absolute;
	margin: -1px;
	border-width: 0;
	padding: 0;
	width: 1px;
	height: 1px;
	overflow: hidden;
	white-space: nowrap;
}

.scroll-hide::-webkit-scrollbar {
	display: none;
}

.gradio-container {
	-webkit-text-size-adjust: 100%; /* 2 */
	line-height: 1.5; /* 1 */
	font-family: var(--font); /* 4 */
	-moz-tab-size: 4; /* 3 */
	tab-size: 4; /* 3 */
}

.cropper-container {
	position: relative;
	-ms-touch-action: none;
	touch-action: none;
	font-size: 0;
	line-height: 0;
	direction: ltr;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

.cropper-container img {
	display: block;
	image-orientation: 0deg;
	width: 100%;
	min-width: 0 !important;
	max-width: none !important;
	height: 100%;
	min-height: 0 !important;
	max-height: none !important;
}

.cropper-wrap-box,
.cropper-canvas,
.cropper-drag-box,
.cropper-crop-box,
.cropper-modal {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
}

.cropper-wrap-box,
.cropper-canvas {
	overflow: hidden;
}

.cropper-drag-box {
	opacity: 0;
	background-color: #fff;
}

.cropper-modal {
	opacity: 0.5;
	background-color: #000;
}

.cropper-view-box {
	display: block;
	outline: 1px solid #39f;
	outline-color: rgba(51, 153, 255, 0.75);
	width: 100%;
	height: 100%;
	overflow: hidden;
}

.cropper-dashed {
	display: block;
	position: absolute;
	opacity: 0.5;
	border: 0 dashed #eee;
}

.cropper-dashed.dashed-h {
	top: calc(100% / 3);
	left: 0;
	border-top-width: 1px;
	border-bottom-width: 1px;
	width: 100%;
	height: calc(100% / 3);
}

.cropper-dashed.dashed-v {
	top: 0;
	left: calc(100% / 3);
	border-right-width: 1px;
	border-left-width: 1px;
	width: calc(100% / 3);
	height: 100%;
}

.cropper-center {
	display: block;
	position: absolute;
	top: 50%;
	left: 50%;
	opacity: 0.75;
	width: 0;
	height: 0;
}

.cropper-center::before,
.cropper-center::after {
	display: block;
	position: absolute;
	background-color: #eee;
	content: " ";
}

.cropper-center::before {
	top: 0;
	left: -3px;
	width: 7px;
	height: 1px;
}

.cropper-center::after {
	top: -3px;
	left: 0;
	width: 1px;
	height: 7px;
}

.cropper-face,
.cropper-line,
.cropper-point {
	display: block;
	position: absolute;
	opacity: 0.1;
	width: 100%;
	height: 100%;
}

.cropper-face {
	top: 0;
	left: 0;
	background-color: #fff;
}

.cropper-line {
	background-color: #39f;
}

.cropper-line.line-e {
	top: 0;
	right: -3px;
	cursor: ew-resize;
	width: 5px;
}

.cropper-line.line-n {
	top: -3px;
	left: 0;
	cursor: ns-resize;
	height: 5px;
}

.cropper-line.line-w {
	top: 0;
	left: -3px;
	cursor: ew-resize;
	width: 5px;
}

.cropper-line.line-s {
	bottom: -3px;
	left: 0;
	cursor: ns-resize;
	height: 5px;
}

.cropper-point {
	opacity: 0.75;
	background-color: #39f;
	width: 5px;
	height: 5px;
}

.cropper-point.point-e {
	top: 50%;
	right: -3px;
	cursor: ew-resize;
	margin-top: -3px;
}

.cropper-point.point-n {
	top: -3px;
	left: 50%;
	cursor: ns-resize;
	margin-left: -3px;
}

.cropper-point.point-w {
	top: 50%;
	left: -3px;
	cursor: ew-resize;
	margin-top: -3px;
}

.cropper-point.point-s {
	bottom: -3px;
	left: 50%;
	cursor: s-resize;
	margin-left: -3px;
}

.cropper-point.point-ne {
	top: -3px;
	right: -3px;
	cursor: nesw-resize;
}

.cropper-point.point-nw {
	top: -3px;
	left: -3px;
	cursor: nwse-resize;
}

.cropper-point.point-sw {
	bottom: -3px;
	left: -3px;
	cursor: nesw-resize;
}

.cropper-point.point-se {
	right: -3px;
	bottom: -3px;
	opacity: 1;
	cursor: nwse-resize;
	width: 20px;
	height: 20px;
}

@media (min-width: 768px) {
	.cropper-point.point-se {
		width: 15px;
		height: 15px;
	}
}

@media (min-width: 992px) {
	.cropper-point.point-se {
		width: 10px;
		height: 10px;
	}
}

@media (min-width: 1200px) {
	.cropper-point.point-se {
		opacity: 0.75;
		width: 5px;
		height: 5px;
	}
}

.cropper-point.point-se::before {
	display: block;
	position: absolute;
	right: -50%;
	bottom: -50%;
	opacity: 0;
	background-color: #39f;
	width: 200%;
	height: 200%;
	content: " ";
}

.cropper-invisible {
	opacity: 0;
}

.cropper-bg {
	background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC");
}

.cropper-hide {
	display: block;
	position: absolute;
	width: 0;
	height: 0;
}

.cropper-hidden {
	display: none !important;
}

.cropper-move {
	cursor: move;
}

.cropper-crop {
	cursor: crosshair;
}

.cropper-disabled .cropper-drag-box,
.cropper-disabled .cropper-face,
.cropper-disabled .cropper-line,
.cropper-disabled .cropper-point {
	cursor: not-allowed;
}
