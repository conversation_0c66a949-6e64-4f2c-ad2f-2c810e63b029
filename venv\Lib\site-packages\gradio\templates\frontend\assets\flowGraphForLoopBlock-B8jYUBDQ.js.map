{"version": 3, "file": "flowGraphForLoopBlock-B8jYUBDQ.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphForLoopBlock.js"], "sourcesContent": ["import { FlowGraphExecutionBlockWithOutSignal } from \"../../../flowGraphExecutionBlockWithOutSignal.js\";\nimport { RichTypeAny, RichTypeFlowGraphInteger, RichTypeNumber } from \"../../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\nimport { getNumericValue } from \"../../../utils.js\";\nimport { FlowGraphInteger } from \"../../../CustomTypes/flowGraphInteger.js\";\n/**\n * Block that executes an action in a loop.\n */\nexport class FlowGraphForLoopBlock extends FlowGraphExecutionBlockWithOutSignal {\n    constructor(config) {\n        super(config);\n        this.startIndex = this.registerDataInput(\"startIndex\", RichTypeAny, 0);\n        this.endIndex = this.registerDataInput(\"endIndex\", RichTypeAny);\n        this.step = this.registerDataInput(\"step\", RichTypeNumber, 1);\n        this.index = this.registerDataOutput(\"index\", RichTypeFlowGraphInteger, new FlowGraphInteger(getNumericValue(config?.initialIndex ?? 0)));\n        this.executionFlow = this._registerSignalOutput(\"executionFlow\");\n        this.completed = this._registerSignalOutput(\"completed\");\n        this._unregisterSignalOutput(\"out\");\n    }\n    /**\n     * @internal\n     */\n    _execute(context) {\n        const index = getNumericValue(this.startIndex.getValue(context));\n        const step = this.step.getValue(context);\n        let endIndex = getNumericValue(this.endIndex.getValue(context));\n        for (let i = index; i < endIndex; i += step) {\n            this.index.setValue(new FlowGraphInteger(i), context);\n            this.executionFlow._activateSignal(context);\n            endIndex = getNumericValue(this.endIndex.getValue(context));\n            if (i > FlowGraphForLoopBlock.MaxLoopIterations) {\n                break;\n            }\n        }\n        this.completed._activateSignal(context);\n    }\n    /**\n     * @returns class name of the block.\n     */\n    getClassName() {\n        return \"FlowGraphForLoopBlock\" /* FlowGraphBlockNames.ForLoop */;\n    }\n}\n/**\n * The maximum number of iterations allowed for the loop.\n * If the loop exceeds this number, it will stop. This number is configurable to avoid infinite loops.\n */\nFlowGraphForLoopBlock.MaxLoopIterations = 1000;\nRegisterClass(\"FlowGraphForLoopBlock\" /* FlowGraphBlockNames.ForLoop */, FlowGraphForLoopBlock);\n//# sourceMappingURL=flowGraphForLoopBlock.js.map"], "names": ["FlowGraphForLoopBlock", "FlowGraphExecutionBlockWithOutSignal", "config", "RichTypeAny", "RichTypeNumber", "RichTypeFlowGraphInteger", "FlowGraphInteger", "getNumericValue", "context", "index", "step", "endIndex", "i", "RegisterClass"], "mappings": "4QAQO,MAAMA,UAA8BC,CAAqC,CAC5E,YAAYC,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,WAAa,KAAK,kBAAkB,aAAcC,EAAa,CAAC,EACrE,KAAK,SAAW,KAAK,kBAAkB,WAAYA,CAAW,EAC9D,KAAK,KAAO,KAAK,kBAAkB,OAAQC,EAAgB,CAAC,EAC5D,KAAK,MAAQ,KAAK,mBAAmB,QAASC,EAA0B,IAAIC,EAAiBC,EAAgBL,GAAQ,cAAgB,CAAC,CAAC,CAAC,EACxI,KAAK,cAAgB,KAAK,sBAAsB,eAAe,EAC/D,KAAK,UAAY,KAAK,sBAAsB,WAAW,EACvD,KAAK,wBAAwB,KAAK,CACrC,CAID,SAASM,EAAS,CACd,MAAMC,EAAQF,EAAgB,KAAK,WAAW,SAASC,CAAO,CAAC,EACzDE,EAAO,KAAK,KAAK,SAASF,CAAO,EACvC,IAAIG,EAAWJ,EAAgB,KAAK,SAAS,SAASC,CAAO,CAAC,EAC9D,QAASI,EAAIH,EAAOG,EAAID,IACpB,KAAK,MAAM,SAAS,IAAIL,EAAiBM,CAAC,EAAGJ,CAAO,EACpD,KAAK,cAAc,gBAAgBA,CAAO,EAC1CG,EAAWJ,EAAgB,KAAK,SAAS,SAASC,CAAO,CAAC,EACtD,EAAAI,EAAIZ,EAAsB,oBAJAY,GAAKF,EAInC,CAIJ,KAAK,UAAU,gBAAgBF,CAAO,CACzC,CAID,cAAe,CACX,MAAO,uBACV,CACL,CAKAR,EAAsB,kBAAoB,IAC1Ca,EAAc,wBAA2Db,CAAqB", "x_google_ignoreList": [0]}