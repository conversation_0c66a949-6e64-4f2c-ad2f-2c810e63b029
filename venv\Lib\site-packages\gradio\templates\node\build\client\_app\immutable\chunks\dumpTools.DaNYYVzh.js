const __vite__fileDeps=["./index.BoI39RQH.js","./preload-helper.D6kgxu3v.js","./pass.fragment.DRSohf02.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as h}from"./preload-helper.D6kgxu3v.js";import{aj as I,b as d,ak as m,al as b,am as R}from"./index.BoI39RQH.js";let l,c=null;async function S(){return c||(c=new Promise((t,r)=>{let a,e=null;const i={preserveDrawingBuffer:!0,depth:!1,stencil:!1,alpha:!0,premultipliedAlpha:!1,antialias:!1,failIfMajorPerformanceCaveat:!1};h(()=>import("./index.BoI39RQH.js").then(s=>s.bX),__vite__mapDeps([0,1]),import.meta.url).then(({ThinEngine:s})=>{var o;const f=m.Instances.length;try{a=new OffscreenCanvas(100,100),e=new s(a,!1,i)}catch{f<m.Instances.length&&((o=m.Instances.pop())==null||o.dispose()),a=document.createElement("canvas"),e=new s(a,!1,i)}m.Instances.pop(),m.OnEnginesDisposedObservable.add(n=>{e&&n!==e&&!e.isDisposed&&m.Instances.length===0&&A()}),e.getCaps().parallelShaderCompile=void 0;const p=new b(e);h(()=>import("./pass.fragment.DRSohf02.js"),__vite__mapDeps([2,0,1]),import.meta.url).then(({passPixelShader:n})=>{if(!e){r("Engine is not defined");return}const w=new R({engine:e,name:n.name,fragmentShader:n.shader,samplerNames:["textureSampler"]});l={canvas:a,engine:e,renderer:p,wrapper:w},t(l)})}).catch(r)})),await c}async function v(t,r,a,e,i="image/png",s,f){const p=await a.readPixels(0,0,t,r),o=new Uint8Array(p.buffer);D(t,r,o,e,i,s,!0,void 0,f)}function E(t,r,a,e="image/png",i,s=!1,f=!1,p){return new Promise(o=>{D(t,r,a,n=>o(n),e,i,s,f,p)})}function D(t,r,a,e,i="image/png",s,f=!1,p=!1,o){S().then(n=>{if(n.engine.setSize(t,r,!0),a instanceof Float32Array){const g=new Uint8Array(a.length);let u=a.length;for(;u--;){const _=a[u];g[u]=Math.round(I(_)*255)}a=g}const w=n.engine.createRawTexture(a,t,r,5,!1,!f,1);n.renderer.setViewport(),n.renderer.applyEffectWrapper(n.wrapper),n.wrapper.effect._bindTexture("textureSampler",w),n.renderer.draw(),p?d.ToBlob(n.canvas,g=>{const u=new FileReader;u.onload=_=>{const y=_.target.result;e&&e(y)},u.readAsArrayBuffer(g)},i,o):d.EncodeScreenshotCanvasData(n.canvas,e,i,s,o),w.dispose()})}function A(){l?(l.wrapper.dispose(),l.renderer.dispose(),l.engine.dispose()):c==null||c.then(t=>{t.wrapper.dispose(),t.renderer.dispose(),t.engine.dispose()}),c=null,l=null}const O={DumpData:D,DumpDataAsync:E,DumpFramebuffer:v,Dispose:A},x=()=>{d.DumpData=D,d.DumpDataAsync=E,d.DumpFramebuffer=v};x();export{A as Dispose,D as DumpData,E as DumpDataAsync,v as DumpFramebuffer,O as DumpTools};
//# sourceMappingURL=dumpTools.DaNYYVzh.js.map
