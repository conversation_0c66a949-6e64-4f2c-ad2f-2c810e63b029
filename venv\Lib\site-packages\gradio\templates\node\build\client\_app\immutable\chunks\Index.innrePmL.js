const __vite__fileDeps=["./Canvas3D.BkXXMcr1.js","./preload-helper.D6kgxu3v.js","./2.B2AoQPnG.js","./stores.z8sZTwoA.js","./client.Cd1aarwx.js","../assets/2.BTQDGmJF.css","./Canvas3DGS.BikOyDfL.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{SvelteComponent as ee,init as le,safe_not_equal as ne,create_component as w,space as W,empty as C,claim_component as v,claim_space as G,mount_component as k,insert_hydration as I,transition_in as h,group_outros as S,transition_out as p,check_outros as V,detach as D,destroy_component as z,element as oe,claim_element as ae,children as ie,attr as P,append_hydration as re,binding_callbacks as A,construct_svelte_component as T,bind as H,add_flush_callback as K,createEventDispatcher as ze,tick as _e,bubble as ve,create_slot as De,update_slot_base as Ie,get_all_dirty_from_scope as Ee,get_slot_changes as Ce,assign as ue,get_spread_update as me,get_spread_object as ce}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{_ as Q}from"./preload-helper.D6kgxu3v.js";import{O as Me,I as de,B as ge,S as he}from"./2.B2AoQPnG.js";import{B as te}from"./BlockLabel.BTSz9r5s.js";import{D as Ue}from"./Download.CpfEFmFf.js";import{F as X}from"./File.Dl9hvYLG.js";import{U as Be}from"./Undo.LhwFM5M8.js";import{I as Ne}from"./IconButtonWrapper.D5aGR59h.js";import{a as Se}from"./Upload.yOHVlgUe.js";import{M as Ve}from"./ModifyUpload.uW4g0eE0.js";import{E as je}from"./Empty.DwQ6nkN6.js";import{U as Ae}from"./UploadText.CJcy9n89.js";import{default as Ml}from"./Example.V12cx6oG.js";function se(s){let e,n,l,o,i,_;n=new Ne({props:{$$slots:{default:[Le]},$$scope:{ctx:s}}});const f=[Pe,Oe],a=[];function u(t,r){return t[10]?0:1}return o=u(s),i=a[o]=f[o](s),{c(){e=oe("div"),w(n.$$.fragment),l=W(),i.c(),this.h()},l(t){e=ae(t,"DIV",{class:!0,"data-testid":!0});var r=ie(e);v(n.$$.fragment,r),l=G(r),i.l(r),r.forEach(D),this.h()},h(){P(e,"class","model3D svelte-1mxwah3"),P(e,"data-testid","model3d")},m(t,r){I(t,e,r),k(n,e,null),re(e,l),a[o].m(e,null),_=!0},p(t,r){const c={};r&2115105&&(c.$$scope={dirty:r,ctx:t}),n.$set(c);let d=o;o=u(t),o===d?a[o].p(t,r):(S(),p(a[d],1,1,()=>{a[d]=null}),V(),i=a[o],i?i.p(t,r):(i=a[o]=f[o](t),i.c()),h(i,1),i.m(e,null))},i(t){_||(h(n.$$.fragment,t),h(i),_=!0)},o(t){p(n.$$.fragment,t),p(i),_=!1},d(t){t&&D(e),z(n),a[o].d()}}}function fe(s){let e,n;return e=new de({props:{Icon:Be,label:"Undo",disabled:!s[9]}}),e.$on("click",s[17]),{c(){w(e.$$.fragment)},l(l){v(e.$$.fragment,l)},m(l,o){k(e,l,o),n=!0},p(l,o){const i={};o&512&&(i.disabled=!l[9]),e.$set(i)},i(l){n||(h(e.$$.fragment,l),n=!0)},o(l){p(e.$$.fragment,l),n=!1},d(l){z(e,l)}}}function Le(s){let e,n,l,o,i,_=!s[10]&&fe(s);return l=new de({props:{Icon:Ue,label:s[5]("common.download")}}),{c(){_&&_.c(),e=W(),n=oe("a"),w(l.$$.fragment),this.h()},l(f){_&&_.l(f),e=G(f),n=ae(f,"A",{href:!0,target:!0,download:!0});var a=ie(n);v(l.$$.fragment,a),a.forEach(D),this.h()},h(){P(n,"href",s[14]),P(n,"target",window.__is_colab__?"_blank":null),P(n,"download",o=window.__is_colab__?null:s[0].orig_name||s[0].path)},m(f,a){_&&_.m(f,a),I(f,e,a),I(f,n,a),k(l,n,null),i=!0},p(f,a){f[10]?_&&(S(),p(_,1,1,()=>{_=null}),V()):_?(_.p(f,a),a&1024&&h(_,1)):(_=fe(f),_.c(),h(_,1),_.m(e.parentNode,e));const u={};a&32&&(u.label=f[5]("common.download")),l.$set(u),(!i||a&16384)&&P(n,"href",f[14]),(!i||a&1&&o!==(o=window.__is_colab__?null:f[0].orig_name||f[0].path))&&P(n,"download",o)},i(f){i||(h(_),h(l.$$.fragment,f),i=!0)},o(f){p(_),p(l.$$.fragment,f),i=!1},d(f){f&&(D(e),D(n)),_&&_.d(f),z(l)}}}function Oe(s){let e,n,l,o;function i(a){s[20](a)}var _=s[13];function f(a,u){let t={value:a[0],display_mode:a[1],clear_color:a[2],camera_position:a[8],zoom_speed:a[6],pan_speed:a[7]};return a[14]!==void 0&&(t.resolved_url=a[14]),{props:t}}return _&&(e=T(_,f(s)),s[19](e),A.push(()=>H(e,"resolved_url",i))),{c(){e&&w(e.$$.fragment),l=C()},l(a){e&&v(e.$$.fragment,a),l=C()},m(a,u){e&&k(e,a,u),I(a,l,u),o=!0},p(a,u){if(u&8192&&_!==(_=a[13])){if(e){S();const t=e;p(t.$$.fragment,1,0,()=>{z(t,1)}),V()}_?(e=T(_,f(a)),a[19](e),A.push(()=>H(e,"resolved_url",i)),w(e.$$.fragment),h(e.$$.fragment,1),k(e,l.parentNode,l)):e=null}else if(_){const t={};u&1&&(t.value=a[0]),u&2&&(t.display_mode=a[1]),u&4&&(t.clear_color=a[2]),u&256&&(t.camera_position=a[8]),u&64&&(t.zoom_speed=a[6]),u&128&&(t.pan_speed=a[7]),!n&&u&16384&&(n=!0,t.resolved_url=a[14],K(()=>n=!1)),e.$set(t)}},i(a){o||(e&&h(e.$$.fragment,a),o=!0)},o(a){e&&p(e.$$.fragment,a),o=!1},d(a){a&&D(l),s[19](null),e&&z(e,a)}}}function Pe(s){let e,n,l,o;function i(a){s[18](a)}var _=s[12];function f(a,u){let t={value:a[0],zoom_speed:a[6],pan_speed:a[7]};return a[14]!==void 0&&(t.resolved_url=a[14]),{props:t}}return _&&(e=T(_,f(s)),A.push(()=>H(e,"resolved_url",i))),{c(){e&&w(e.$$.fragment),l=C()},l(a){e&&v(e.$$.fragment,a),l=C()},m(a,u){e&&k(e,a,u),I(a,l,u),o=!0},p(a,u){if(u&4096&&_!==(_=a[12])){if(e){S();const t=e;p(t.$$.fragment,1,0,()=>{z(t,1)}),V()}_?(e=T(_,f(a)),A.push(()=>H(e,"resolved_url",i)),w(e.$$.fragment),h(e.$$.fragment,1),k(e,l.parentNode,l)):e=null}else if(_){const t={};u&1&&(t.value=a[0]),u&64&&(t.zoom_speed=a[6]),u&128&&(t.pan_speed=a[7]),!n&&u&16384&&(n=!0,t.resolved_url=a[14],K(()=>n=!1)),e.$set(t)}},i(a){o||(e&&h(e.$$.fragment,a),o=!0)},o(a){e&&p(e.$$.fragment,a),o=!1},d(a){a&&D(l),e&&z(e,a)}}}function Te(s){let e,n,l,o;e=new te({props:{show_label:s[4],Icon:X,label:s[3]||s[5]("3D_model.3d_model")}});let i=s[0]&&se(s);return{c(){w(e.$$.fragment),n=W(),i&&i.c(),l=C()},l(_){v(e.$$.fragment,_),n=G(_),i&&i.l(_),l=C()},m(_,f){k(e,_,f),I(_,n,f),i&&i.m(_,f),I(_,l,f),o=!0},p(_,[f]){const a={};f&16&&(a.show_label=_[4]),f&40&&(a.label=_[3]||_[5]("3D_model.3d_model")),e.$set(a),_[0]?i?(i.p(_,f),f&1&&h(i,1)):(i=se(_),i.c(),h(i,1),i.m(l.parentNode,l)):i&&(S(),p(i,1,1,()=>{i=null}),V())},i(_){o||(h(e.$$.fragment,_),h(i),o=!0)},o(_){p(e.$$.fragment,_),p(i),o=!1},d(_){_&&(D(n),D(l)),z(e,_),i&&i.d(_)}}}async function We(){return(await Q(()=>import("./Canvas3D.BkXXMcr1.js"),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url)).default}async function Ge(){return(await Q(()=>import("./Canvas3DGS.BikOyDfL.js"),__vite__mapDeps([6,2,1,3,4,5]),import.meta.url)).default}function Re(s,e,n){let{value:l}=e,{display_mode:o="solid"}=e,{clear_color:i=[0,0,0,0]}=e,{label:_=""}=e,{show_label:f}=e,{i18n:a}=e,{zoom_speed:u=1}=e,{pan_speed:t=1}=e,{camera_position:r=[null,null,null]}=e,{has_change_history:c=!1}=e,d={camera_position:r,zoom_speed:u,pan_speed:t},B=!1,U,E,M;function L(){M==null||M.reset_camera_position()}let N;const O=()=>L();function j(b){N=b,n(14,N)}function R(b){A[b?"unshift":"push"](()=>{M=b,n(11,M)})}function q(b){N=b,n(14,N)}return s.$$set=b=>{"value"in b&&n(0,l=b.value),"display_mode"in b&&n(1,o=b.display_mode),"clear_color"in b&&n(2,i=b.clear_color),"label"in b&&n(3,_=b.label),"show_label"in b&&n(4,f=b.show_label),"i18n"in b&&n(5,a=b.i18n),"zoom_speed"in b&&n(6,u=b.zoom_speed),"pan_speed"in b&&n(7,t=b.pan_speed),"camera_position"in b&&n(8,r=b.camera_position),"has_change_history"in b&&n(9,c=b.has_change_history)},s.$$.update=()=>{s.$$.dirty&1025&&l&&(n(10,B=l.path.endsWith(".splat")||l.path.endsWith(".ply")),B?Ge().then(b=>{n(12,U=b)}):We().then(b=>{n(13,E=b)})),s.$$.dirty&68032&&(!Me(d.camera_position,r)||d.zoom_speed!==u||d.pan_speed!==t)&&(M==null||M.update_camera(r,u,t),n(16,d={camera_position:r,zoom_speed:u,pan_speed:t}))},[l,o,i,_,f,a,u,t,r,c,B,M,U,E,N,L,d,O,j,R,q]}class qe extends ee{constructor(e){super(),le(this,e,Re,Te,ne,{value:0,display_mode:1,clear_color:2,label:3,show_label:4,i18n:5,zoom_speed:6,pan_speed:7,camera_position:8,has_change_history:9})}}const Fe=qe;function He(s){let e,n,l,o,i,_;n=new Ve({props:{undoable:!s[14],i18n:s[7]}}),n.$on("clear",s[20]),n.$on("undo",s[21]);const f=[Qe,Ke],a=[];function u(t,r){return t[14]?0:1}return o=u(s),i=a[o]=f[o](s),{c(){e=oe("div"),w(n.$$.fragment),l=W(),i.c(),this.h()},l(t){e=ae(t,"DIV",{class:!0});var r=ie(e);v(n.$$.fragment,r),l=G(r),i.l(r),r.forEach(D),this.h()},h(){P(e,"class","input-model svelte-jub4pj")},m(t,r){I(t,e,r),k(n,e,null),re(e,l),a[o].m(e,null),_=!0},p(t,r){const c={};r&16384&&(c.undoable=!t[14]),r&128&&(c.i18n=t[7]),n.$set(c);let d=o;o=u(t),o===d?a[o].p(t,r):(S(),p(a[d],1,1,()=>{a[d]=null}),V(),i=a[o],i?i.p(t,r):(i=a[o]=f[o](t),i.c()),h(i,1),i.m(e,null))},i(t){_||(h(n.$$.fragment,t),h(i),_=!0)},o(t){p(n.$$.fragment,t),p(i),_=!1},d(t){t&&D(e),z(n),a[o].d()}}}function Je(s){let e,n,l,o;function i(a){s[23](a)}function _(a){s[24](a)}let f={upload:s[12],stream_handler:s[13],root:s[6],max_file_size:s[10],filetype:[".stl",".obj",".gltf",".glb","model/obj",".splat",".ply"],aria_label:s[7]("model3d.drop_to_upload"),$$slots:{default:[Xe]},$$scope:{ctx:s}};return s[15]!==void 0&&(f.dragging=s[15]),s[1]!==void 0&&(f.uploading=s[1]),e=new Se({props:f}),A.push(()=>H(e,"dragging",i)),A.push(()=>H(e,"uploading",_)),e.$on("load",s[19]),e.$on("error",s[25]),{c(){w(e.$$.fragment)},l(a){v(e.$$.fragment,a)},m(a,u){k(e,a,u),o=!0},p(a,u){const t={};u&4096&&(t.upload=a[12]),u&8192&&(t.stream_handler=a[13]),u&64&&(t.root=a[6]),u&1024&&(t.max_file_size=a[10]),u&128&&(t.aria_label=a[7]("model3d.drop_to_upload")),u&134217728&&(t.$$scope={dirty:u,ctx:a}),!n&&u&32768&&(n=!0,t.dragging=a[15],K(()=>n=!1)),!l&&u&2&&(l=!0,t.uploading=a[1],K(()=>l=!1)),e.$set(t)},i(a){o||(h(e.$$.fragment,a),o=!0)},o(a){p(e.$$.fragment,a),o=!1},d(a){z(e,a)}}}function Ke(s){let e,n,l;var o=s[17];function i(_,f){return{props:{value:_[0],display_mode:_[2],clear_color:_[3],camera_position:_[11],zoom_speed:_[8],pan_speed:_[9]}}}return o&&(e=T(o,i(s)),s[26](e)),{c(){e&&w(e.$$.fragment),n=C()},l(_){e&&v(e.$$.fragment,_),n=C()},m(_,f){e&&k(e,_,f),I(_,n,f),l=!0},p(_,f){if(f&131072&&o!==(o=_[17])){if(e){S();const a=e;p(a.$$.fragment,1,0,()=>{z(a,1)}),V()}o?(e=T(o,i(_)),_[26](e),w(e.$$.fragment),h(e.$$.fragment,1),k(e,n.parentNode,n)):e=null}else if(o){const a={};f&1&&(a.value=_[0]),f&4&&(a.display_mode=_[2]),f&8&&(a.clear_color=_[3]),f&2048&&(a.camera_position=_[11]),f&256&&(a.zoom_speed=_[8]),f&512&&(a.pan_speed=_[9]),e.$set(a)}},i(_){l||(e&&h(e.$$.fragment,_),l=!0)},o(_){e&&p(e.$$.fragment,_),l=!1},d(_){_&&D(n),s[26](null),e&&z(e,_)}}}function Qe(s){let e,n,l;var o=s[16];function i(_,f){return{props:{value:_[0],zoom_speed:_[8],pan_speed:_[9]}}}return o&&(e=T(o,i(s))),{c(){e&&w(e.$$.fragment),n=C()},l(_){e&&v(e.$$.fragment,_),n=C()},m(_,f){e&&k(e,_,f),I(_,n,f),l=!0},p(_,f){if(f&65536&&o!==(o=_[16])){if(e){S();const a=e;p(a.$$.fragment,1,0,()=>{z(a,1)}),V()}o?(e=T(o,i(_)),w(e.$$.fragment),h(e.$$.fragment,1),k(e,n.parentNode,n)):e=null}else if(o){const a={};f&1&&(a.value=_[0]),f&256&&(a.zoom_speed=_[8]),f&512&&(a.pan_speed=_[9]),e.$set(a)}},i(_){l||(e&&h(e.$$.fragment,_),l=!0)},o(_){e&&p(e.$$.fragment,_),l=!1},d(_){_&&D(n),e&&z(e,_)}}}function Xe(s){let e;const n=s[22].default,l=De(n,s,s[27],null);return{c(){l&&l.c()},l(o){l&&l.l(o)},m(o,i){l&&l.m(o,i),e=!0},p(o,i){l&&l.p&&(!e||i&134217728)&&Ie(l,n,o,o[27],e?Ce(n,o[27],i,null):Ee(o[27]),null)},i(o){e||(h(l,o),e=!0)},o(o){p(l,o),e=!1},d(o){l&&l.d(o)}}}function Ye(s){let e,n,l,o,i,_;e=new te({props:{show_label:s[5],Icon:X,label:s[4]||"3D Model"}});const f=[Je,He],a=[];function u(t,r){return t[0]===null?0:1}return l=u(s),o=a[l]=f[l](s),{c(){w(e.$$.fragment),n=W(),o.c(),i=C()},l(t){v(e.$$.fragment,t),n=G(t),o.l(t),i=C()},m(t,r){k(e,t,r),I(t,n,r),a[l].m(t,r),I(t,i,r),_=!0},p(t,[r]){const c={};r&32&&(c.show_label=t[5]),r&16&&(c.label=t[4]||"3D Model"),e.$set(c);let d=l;l=u(t),l===d?a[l].p(t,r):(S(),p(a[d],1,1,()=>{a[d]=null}),V(),o=a[l],o?o.p(t,r):(o=a[l]=f[l](t),o.c()),h(o,1),o.m(i.parentNode,i))},i(t){_||(h(e.$$.fragment,t),h(o),_=!0)},o(t){p(e.$$.fragment,t),p(o),_=!1},d(t){t&&(D(n),D(i)),z(e,t),a[l].d(t)}}}async function Ze(){return(await Q(()=>import("./Canvas3D.BkXXMcr1.js"),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url)).default}async function ye(){return(await Q(()=>import("./Canvas3DGS.BikOyDfL.js"),__vite__mapDeps([6,2,1,3,4,5]),import.meta.url)).default}function $e(s,e,n){let{$$slots:l={},$$scope:o}=e,{value:i}=e,{display_mode:_="solid"}=e,{clear_color:f=[0,0,0,0]}=e,{label:a=""}=e,{show_label:u}=e,{root:t}=e,{i18n:r}=e,{zoom_speed:c=1}=e,{pan_speed:d=1}=e,{max_file_size:B=null}=e,{uploading:U=!1}=e,{camera_position:E=[null,null,null]}=e,{upload:M}=e,{stream_handler:L}=e;async function N({detail:g}){n(0,i=g),await _e(),F("change",i),F("load",i)}async function O(){n(0,i=null),await _e(),F("clear"),F("change")}let j=!1,R,q,b;async function Y(){b==null||b.reset_camera_position()}const F=ze();let J=!1;function Z(g){J=g,n(15,J)}function y(g){U=g,n(1,U)}function $(g){ve.call(this,s,g)}function x(g){A[g?"unshift":"push"](()=>{b=g,n(18,b)})}return s.$$set=g=>{"value"in g&&n(0,i=g.value),"display_mode"in g&&n(2,_=g.display_mode),"clear_color"in g&&n(3,f=g.clear_color),"label"in g&&n(4,a=g.label),"show_label"in g&&n(5,u=g.show_label),"root"in g&&n(6,t=g.root),"i18n"in g&&n(7,r=g.i18n),"zoom_speed"in g&&n(8,c=g.zoom_speed),"pan_speed"in g&&n(9,d=g.pan_speed),"max_file_size"in g&&n(10,B=g.max_file_size),"uploading"in g&&n(1,U=g.uploading),"camera_position"in g&&n(11,E=g.camera_position),"upload"in g&&n(12,M=g.upload),"stream_handler"in g&&n(13,L=g.stream_handler),"$$scope"in g&&n(27,o=g.$$scope)},s.$$.update=()=>{s.$$.dirty&16385&&i&&(n(14,j=i.path.endsWith(".splat")||i.path.endsWith(".ply")),j?ye().then(g=>{n(16,R=g)}):Ze().then(g=>{n(17,q=g)})),s.$$.dirty&32768&&F("drag",J)},[i,U,_,f,a,u,t,r,c,d,B,E,M,L,j,J,R,q,b,N,O,Y,l,Z,y,$,x,o]}class xe extends ee{constructor(e){super(),le(this,e,$e,Ye,ne,{value:0,display_mode:2,clear_color:3,label:4,show_label:5,root:6,i18n:7,zoom_speed:8,pan_speed:9,max_file_size:10,uploading:1,camera_position:11,upload:12,stream_handler:13})}}const el=xe;function ll(s){let e,n;return e=new ge({props:{visible:s[5],variant:s[0]===null?"dashed":"solid",border_mode:s[20]?"focus":"base",padding:!1,elem_id:s[3],elem_classes:s[4],container:s[11],scale:s[12],min_width:s[13],height:s[15],$$slots:{default:[al]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},l(l){v(e.$$.fragment,l)},m(l,o){k(e,l,o),n=!0},p(l,o){const i={};o[0]&32&&(i.visible=l[5]),o[0]&1&&(i.variant=l[0]===null?"dashed":"solid"),o[0]&1048576&&(i.border_mode=l[20]?"focus":"base"),o[0]&8&&(i.elem_id=l[3]),o[0]&16&&(i.elem_classes=l[4]),o[0]&2048&&(i.container=l[11]),o[0]&4096&&(i.scale=l[12]),o[0]&8192&&(i.min_width=l[13]),o[0]&32768&&(i.height=l[15]),o[0]&1787847|o[1]&8&&(i.$$scope={dirty:o,ctx:l}),e.$set(i)},i(l){n||(h(e.$$.fragment,l),n=!0)},o(l){p(e.$$.fragment,l),n=!1},d(l){z(e,l)}}}function nl(s){let e,n;return e=new ge({props:{visible:s[5],variant:s[0]===null?"dashed":"solid",border_mode:s[20]?"focus":"base",padding:!1,elem_id:s[3],elem_classes:s[4],container:s[11],scale:s[12],min_width:s[13],height:s[15],$$slots:{default:[sl]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},l(l){v(e.$$.fragment,l)},m(l,o){k(e,l,o),n=!0},p(l,o){const i={};o[0]&32&&(i.visible=l[5]),o[0]&1&&(i.variant=l[0]===null?"dashed":"solid"),o[0]&1048576&&(i.border_mode=l[20]?"focus":"base"),o[0]&8&&(i.elem_id=l[3]),o[0]&16&&(i.elem_classes=l[4]),o[0]&2048&&(i.container=l[11]),o[0]&4096&&(i.scale=l[12]),o[0]&8192&&(i.min_width=l[13]),o[0]&32768&&(i.height=l[15]),o[0]&214919|o[1]&8&&(i.$$scope={dirty:o,ctx:l}),e.$set(i)},i(l){n||(h(e.$$.fragment,l),n=!0)},o(l){p(e.$$.fragment,l),n=!1},d(l){z(e,l)}}}function ol(s){let e,n;return e=new Ae({props:{i18n:s[14].i18n,type:"file"}}),{c(){w(e.$$.fragment)},l(l){v(e.$$.fragment,l)},m(l,o){k(e,l,o),n=!0},p(l,o){const i={};o[0]&16384&&(i.i18n=l[14].i18n),e.$set(i)},i(l){n||(h(e.$$.fragment,l),n=!0)},o(l){p(e.$$.fragment,l),n=!1},d(l){z(e,l)}}}function al(s){let e,n,l,o,i;const _=[{autoscroll:s[14].autoscroll},{i18n:s[14].i18n},s[1]];let f={};for(let t=0;t<_.length;t+=1)f=ue(f,_[t]);e=new he({props:f}),e.$on("clear_status",s[24]);function a(t){s[27](t)}let u={label:s[9],show_label:s[10],root:s[6],display_mode:s[7],clear_color:s[8],value:s[0],camera_position:s[17],zoom_speed:s[16],i18n:s[14].i18n,max_file_size:s[14].max_file_size,upload:s[25],stream_handler:s[26],$$slots:{default:[ol]},$$scope:{ctx:s}};return s[19]!==void 0&&(u.uploading=s[19]),l=new el({props:u}),A.push(()=>H(l,"uploading",a)),l.$on("change",s[28]),l.$on("drag",s[29]),l.$on("change",s[30]),l.$on("clear",s[31]),l.$on("load",s[32]),l.$on("error",s[33]),{c(){w(e.$$.fragment),n=W(),w(l.$$.fragment)},l(t){v(e.$$.fragment,t),n=G(t),v(l.$$.fragment,t)},m(t,r){k(e,t,r),I(t,n,r),k(l,t,r),i=!0},p(t,r){const c=r[0]&16386?me(_,[r[0]&16384&&{autoscroll:t[14].autoscroll},r[0]&16384&&{i18n:t[14].i18n},r[0]&2&&ce(t[1])]):{};e.$set(c);const d={};r[0]&512&&(d.label=t[9]),r[0]&1024&&(d.show_label=t[10]),r[0]&64&&(d.root=t[6]),r[0]&128&&(d.display_mode=t[7]),r[0]&256&&(d.clear_color=t[8]),r[0]&1&&(d.value=t[0]),r[0]&131072&&(d.camera_position=t[17]),r[0]&65536&&(d.zoom_speed=t[16]),r[0]&16384&&(d.i18n=t[14].i18n),r[0]&16384&&(d.max_file_size=t[14].max_file_size),r[0]&16384&&(d.upload=t[25]),r[0]&16384&&(d.stream_handler=t[26]),r[0]&16384|r[1]&8&&(d.$$scope={dirty:r,ctx:t}),!o&&r[0]&524288&&(o=!0,d.uploading=t[19],K(()=>o=!1)),l.$set(d)},i(t){i||(h(e.$$.fragment,t),h(l.$$.fragment,t),i=!0)},o(t){p(e.$$.fragment,t),p(l.$$.fragment,t),i=!1},d(t){t&&D(n),z(e,t),z(l,t)}}}function il(s){let e,n,l,o;return e=new te({props:{show_label:s[10],Icon:X,label:s[9]||"3D Model"}}),l=new je({props:{unpadded_box:!0,size:"large",$$slots:{default:[_l]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment),n=W(),w(l.$$.fragment)},l(i){v(e.$$.fragment,i),n=G(i),v(l.$$.fragment,i)},m(i,_){k(e,i,_),I(i,n,_),k(l,i,_),o=!0},p(i,_){const f={};_[0]&1024&&(f.show_label=i[10]),_[0]&512&&(f.label=i[9]||"3D Model"),e.$set(f);const a={};_[1]&8&&(a.$$scope={dirty:_,ctx:i}),l.$set(a)},i(i){o||(h(e.$$.fragment,i),h(l.$$.fragment,i),o=!0)},o(i){p(e.$$.fragment,i),p(l.$$.fragment,i),o=!1},d(i){i&&D(n),z(e,i),z(l,i)}}}function tl(s){let e,n;return e=new Fe({props:{value:s[0],i18n:s[14].i18n,display_mode:s[7],clear_color:s[8],label:s[9],show_label:s[10],camera_position:s[17],zoom_speed:s[16],has_change_history:s[2]}}),{c(){w(e.$$.fragment)},l(l){v(e.$$.fragment,l)},m(l,o){k(e,l,o),n=!0},p(l,o){const i={};o[0]&1&&(i.value=l[0]),o[0]&16384&&(i.i18n=l[14].i18n),o[0]&128&&(i.display_mode=l[7]),o[0]&256&&(i.clear_color=l[8]),o[0]&512&&(i.label=l[9]),o[0]&1024&&(i.show_label=l[10]),o[0]&131072&&(i.camera_position=l[17]),o[0]&65536&&(i.zoom_speed=l[16]),o[0]&4&&(i.has_change_history=l[2]),e.$set(i)},i(l){n||(h(e.$$.fragment,l),n=!0)},o(l){p(e.$$.fragment,l),n=!1},d(l){z(e,l)}}}function _l(s){let e,n;return e=new X({}),{c(){w(e.$$.fragment)},l(l){v(e.$$.fragment,l)},m(l,o){k(e,l,o),n=!0},i(l){n||(h(e.$$.fragment,l),n=!0)},o(l){p(e.$$.fragment,l),n=!1},d(l){z(e,l)}}}function sl(s){let e,n,l,o,i,_;const f=[{autoscroll:s[14].autoscroll},{i18n:s[14].i18n},s[1]];let a={};for(let c=0;c<f.length;c+=1)a=ue(a,f[c]);e=new he({props:a}),e.$on("clear_status",s[23]);const u=[tl,il],t=[];function r(c,d){return c[0]&&c[21]?0:1}return l=r(s),o=t[l]=u[l](s),{c(){w(e.$$.fragment),n=W(),o.c(),i=C()},l(c){v(e.$$.fragment,c),n=G(c),o.l(c),i=C()},m(c,d){k(e,c,d),I(c,n,d),t[l].m(c,d),I(c,i,d),_=!0},p(c,d){const B=d[0]&16386?me(f,[d[0]&16384&&{autoscroll:c[14].autoscroll},d[0]&16384&&{i18n:c[14].i18n},d[0]&2&&ce(c[1])]):{};e.$set(B);let U=l;l=r(c),l===U?t[l].p(c,d):(S(),p(t[U],1,1,()=>{t[U]=null}),V(),o=t[l],o?o.p(c,d):(o=t[l]=u[l](c),o.c()),h(o,1),o.m(i.parentNode,i))},i(c){_||(h(e.$$.fragment,c),h(o),_=!0)},o(c){p(e.$$.fragment,c),p(o),_=!1},d(c){c&&(D(n),D(i)),z(e,c),t[l].d(c)}}}function fl(s){let e,n,l,o;const i=[nl,ll],_=[];function f(a,u){return a[18]?1:0}return e=f(s),n=_[e]=i[e](s),{c(){n.c(),l=C()},l(a){n.l(a),l=C()},m(a,u){_[e].m(a,u),I(a,l,u),o=!0},p(a,u){let t=e;e=f(a),e===t?_[e].p(a,u):(S(),p(_[t],1,1,()=>{_[t]=null}),V(),n=_[e],n?n.p(a,u):(n=_[e]=i[e](a),n.c()),h(n,1),n.m(l.parentNode,l))},i(a){o||(h(n),o=!0)},o(a){p(n),o=!1},d(a){a&&D(l),_[e].d(a)}}}function rl(s,e,n){let{elem_id:l=""}=e,{elem_classes:o=[]}=e,{visible:i=!0}=e,{value:_=null}=e,{root:f}=e,{display_mode:a="solid"}=e,{clear_color:u}=e,{loading_status:t}=e,{label:r}=e,{show_label:c}=e,{container:d=!0}=e,{scale:B=null}=e,{min_width:U=void 0}=e,{gradio:E}=e,{height:M=void 0}=e,{zoom_speed:L=1}=e,{input_ready:N}=e,O=!1,{has_change_history:j=!1}=e,{camera_position:R=[null,null,null]}=e,{interactive:q}=e,b=!1;const Y=typeof window<"u",F=()=>E.dispatch("clear_status",t),J=()=>E.dispatch("clear_status",t),Z=(...m)=>E.client.upload(...m),y=(...m)=>E.client.stream(...m);function $(m){O=m,n(19,O)}const x=({detail:m})=>n(0,_=m),g=({detail:m})=>n(20,b=m),be=({detail:m})=>{E.dispatch("change",m),n(2,j=!0)},pe=()=>{n(0,_=null),E.dispatch("clear")},we=({detail:m})=>{n(0,_=m),E.dispatch("upload")},ke=({detail:m})=>{n(1,t=t||{}),n(1,t.status="error",t),E.dispatch("error",m)};return s.$$set=m=>{"elem_id"in m&&n(3,l=m.elem_id),"elem_classes"in m&&n(4,o=m.elem_classes),"visible"in m&&n(5,i=m.visible),"value"in m&&n(0,_=m.value),"root"in m&&n(6,f=m.root),"display_mode"in m&&n(7,a=m.display_mode),"clear_color"in m&&n(8,u=m.clear_color),"loading_status"in m&&n(1,t=m.loading_status),"label"in m&&n(9,r=m.label),"show_label"in m&&n(10,c=m.show_label),"container"in m&&n(11,d=m.container),"scale"in m&&n(12,B=m.scale),"min_width"in m&&n(13,U=m.min_width),"gradio"in m&&n(14,E=m.gradio),"height"in m&&n(15,M=m.height),"zoom_speed"in m&&n(16,L=m.zoom_speed),"input_ready"in m&&n(22,N=m.input_ready),"has_change_history"in m&&n(2,j=m.has_change_history),"camera_position"in m&&n(17,R=m.camera_position),"interactive"in m&&n(18,q=m.interactive)},s.$$.update=()=>{s.$$.dirty[0]&524288&&n(22,N=!O)},[_,t,j,l,o,i,f,a,u,r,c,d,B,U,E,M,L,R,q,O,b,Y,N,F,J,Z,y,$,x,g,be,pe,we,ke]}class Il extends ee{constructor(e){super(),le(this,e,rl,fl,ne,{elem_id:3,elem_classes:4,visible:5,value:0,root:6,display_mode:7,clear_color:8,loading_status:1,label:9,show_label:10,container:11,scale:12,min_width:13,gradio:14,height:15,zoom_speed:16,input_ready:22,has_change_history:2,camera_position:17,interactive:18},null,[-1,-1])}}export{Ml as BaseExample,Fe as BaseModel3D,el as BaseModel3DUpload,Il as default};
//# sourceMappingURL=Index.innrePmL.js.map
