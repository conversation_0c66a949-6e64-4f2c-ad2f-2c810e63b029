import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/scene3ditem_p.h"
        name: "Qt3DRender::Scene3DItem"
        accessSemantics: "reference"
        defaultProperty: "entity"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Scene3D/Scene3D 2.0",
            "QtQuick.Scene3D/Scene3D 2.1",
            "QtQuick.Scene3D/Scene3D 2.4",
            "QtQuick.Scene3D/Scene3D 2.7",
            "QtQuick.Scene3D/Scene3D 2.11",
            "QtQuick.Scene3D/Scene3D 2.14",
            "QtQuick.Scene3D/Scene3D 6.0",
            "QtQuick.Scene3D/Scene3D 6.3",
            "QtQuick.Scene3D/Scene3D 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            516,
            519,
            523,
            526,
            1536,
            1539,
            1543
        ]
        Enum {
            name: "CameraAspectRatioMode"
            values: ["AutomaticAspectRatio", "UserAspectRatio"]
        }
        Enum {
            name: "CompositingMode"
            values: ["FBO", "Underlay"]
        }
        Property {
            name: "entity"
            type: "Qt3DCore::QEntity"
            isPointer: true
            read: "entity"
            write: "setEntity"
            notify: "entityChanged"
            index: 0
        }
        Property {
            name: "aspects"
            type: "QStringList"
            read: "aspects"
            write: "setAspects"
            notify: "aspectsChanged"
            index: 1
        }
        Property {
            name: "multisample"
            type: "bool"
            read: "multisample"
            write: "setMultisample"
            notify: "multisampleChanged"
            index: 2
        }
        Property {
            name: "cameraAspectRatioMode"
            type: "CameraAspectRatioMode"
            read: "cameraAspectRatioMode"
            write: "setCameraAspectRatioMode"
            notify: "cameraAspectRatioModeChanged"
            index: 3
        }
        Property {
            name: "hoverEnabled"
            type: "bool"
            read: "isHoverEnabled"
            write: "setHoverEnabled"
            notify: "hoverEnabledChanged"
            index: 4
        }
        Property {
            name: "compositingMode"
            revision: 526
            type: "CompositingMode"
            read: "compositingMode"
            write: "setCompositingMode"
            notify: "compositingModeChanged"
            index: 5
        }
        Signal { name: "aspectsChanged" }
        Signal { name: "entityChanged" }
        Signal { name: "multisampleChanged" }
        Signal {
            name: "cameraAspectRatioModeChanged"
            Parameter { name: "mode"; type: "CameraAspectRatioMode" }
        }
        Signal { name: "hoverEnabledChanged" }
        Signal { name: "compositingModeChanged" }
        Method {
            name: "setAspects"
            Parameter { name: "aspects"; type: "QStringList" }
        }
        Method {
            name: "setEntity"
            Parameter { name: "entity"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
        Method {
            name: "setCameraAspectRatioMode"
            Parameter { name: "mode"; type: "CameraAspectRatioMode" }
        }
        Method {
            name: "setHoverEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setCompositingMode"
            Parameter { name: "mode"; type: "CompositingMode" }
        }
        Method { name: "applyRootEntityChange" }
        Method { name: "requestUpdate" }
        Method {
            name: "setItemAreaAndDevicePixelRatio"
            Parameter { name: "area"; type: "QSize" }
            Parameter { name: "devicePixelRatio"; type: "double" }
        }
    }
}
