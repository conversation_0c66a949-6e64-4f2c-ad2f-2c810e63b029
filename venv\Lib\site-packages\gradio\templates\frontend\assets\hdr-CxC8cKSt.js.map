{"version": 3, "file": "hdr-CxC8cKSt.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Misc/HighDynamicRange/panoramaToCubemap.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Misc/HighDynamicRange/hdr.js"], "sourcesContent": ["import { Vector3 } from \"../../Maths/math.vector.js\";\n\n/**\n * Helper class useful to convert panorama picture to their cubemap representation in 6 faces.\n */\nexport class PanoramaToCubeMapTools {\n    /**\n     * Converts a panorama stored in RGB right to left up to down format into a cubemap (6 faces).\n     *\n     * @param float32Array The source data.\n     * @param inputWidth The width of the input panorama.\n     * @param inputHeight The height of the input panorama.\n     * @param size The willing size of the generated cubemap (each faces will be size * size pixels)\n     * @param supersample enable supersampling the cubemap\n     * @returns The cubemap data\n     */\n    static ConvertPanoramaToCubemap(float32Array, inputWidth, inputHeight, size, supersample = false) {\n        if (!float32Array) {\n            // eslint-disable-next-line no-throw-literal\n            throw \"ConvertPanoramaToCubemap: input cannot be null\";\n        }\n        if (float32Array.length != inputWidth * inputHeight * 3) {\n            // eslint-disable-next-line no-throw-literal\n            throw \"ConvertPanoramaToCubemap: input size is wrong\";\n        }\n        const textureFront = this.CreateCubemapTexture(size, this.FACE_FRONT, float32Array, inputWidth, inputHeight, supersample);\n        const textureBack = this.CreateCubemapTexture(size, this.FACE_BACK, float32Array, inputWidth, inputHeight, supersample);\n        const textureLeft = this.CreateCubemapTexture(size, this.FACE_LEFT, float32Array, inputWidth, inputHeight, supersample);\n        const textureRight = this.CreateCubemapTexture(size, this.FACE_RIGHT, float32Array, inputWidth, inputHeight, supersample);\n        const textureUp = this.CreateCubemapTexture(size, this.FACE_UP, float32Array, inputWidth, inputHeight, supersample);\n        const textureDown = this.CreateCubemapTexture(size, this.FACE_DOWN, float32Array, inputWidth, inputHeight, supersample);\n        return {\n            front: textureFront,\n            back: textureBack,\n            left: textureLeft,\n            right: textureRight,\n            up: textureUp,\n            down: textureDown,\n            size: size,\n            type: 1,\n            format: 4,\n            gammaSpace: false,\n        };\n    }\n    static CreateCubemapTexture(texSize, faceData, float32Array, inputWidth, inputHeight, supersample = false) {\n        const buffer = new ArrayBuffer(texSize * texSize * 4 * 3);\n        const textureArray = new Float32Array(buffer);\n        // If supersampling, determine number of samples needed when source texture width is divided for 4 cube faces\n        const samples = supersample ? Math.max(1, Math.round(inputWidth / 4 / texSize)) : 1;\n        const sampleFactor = 1 / samples;\n        const sampleFactorSqr = sampleFactor * sampleFactor;\n        const rotDX1 = faceData[1].subtract(faceData[0]).scale(sampleFactor / texSize);\n        const rotDX2 = faceData[3].subtract(faceData[2]).scale(sampleFactor / texSize);\n        const dy = 1 / texSize;\n        let fy = 0;\n        for (let y = 0; y < texSize; y++) {\n            for (let sy = 0; sy < samples; sy++) {\n                let xv1 = faceData[0];\n                let xv2 = faceData[2];\n                for (let x = 0; x < texSize; x++) {\n                    for (let sx = 0; sx < samples; sx++) {\n                        const v = xv2.subtract(xv1).scale(fy).add(xv1);\n                        v.normalize();\n                        const color = this.CalcProjectionSpherical(v, float32Array, inputWidth, inputHeight);\n                        // 3 channels per pixels\n                        textureArray[y * texSize * 3 + x * 3 + 0] += color.r * sampleFactorSqr;\n                        textureArray[y * texSize * 3 + x * 3 + 1] += color.g * sampleFactorSqr;\n                        textureArray[y * texSize * 3 + x * 3 + 2] += color.b * sampleFactorSqr;\n                        xv1 = xv1.add(rotDX1);\n                        xv2 = xv2.add(rotDX2);\n                    }\n                }\n                fy += dy * sampleFactor;\n            }\n        }\n        return textureArray;\n    }\n    static CalcProjectionSpherical(vDir, float32Array, inputWidth, inputHeight) {\n        let theta = Math.atan2(vDir.z, vDir.x);\n        const phi = Math.acos(vDir.y);\n        while (theta < -Math.PI) {\n            theta += 2 * Math.PI;\n        }\n        while (theta > Math.PI) {\n            theta -= 2 * Math.PI;\n        }\n        let dx = theta / Math.PI;\n        const dy = phi / Math.PI;\n        // recenter.\n        dx = dx * 0.5 + 0.5;\n        let px = Math.round(dx * inputWidth);\n        if (px < 0) {\n            px = 0;\n        }\n        else if (px >= inputWidth) {\n            px = inputWidth - 1;\n        }\n        let py = Math.round(dy * inputHeight);\n        if (py < 0) {\n            py = 0;\n        }\n        else if (py >= inputHeight) {\n            py = inputHeight - 1;\n        }\n        const inputY = inputHeight - py - 1;\n        const r = float32Array[inputY * inputWidth * 3 + px * 3 + 0];\n        const g = float32Array[inputY * inputWidth * 3 + px * 3 + 1];\n        const b = float32Array[inputY * inputWidth * 3 + px * 3 + 2];\n        return {\n            r: r,\n            g: g,\n            b: b,\n        };\n    }\n}\nPanoramaToCubeMapTools.FACE_LEFT = [new Vector3(-1.0, -1.0, -1.0), new Vector3(1.0, -1.0, -1.0), new Vector3(-1.0, 1.0, -1.0), new Vector3(1.0, 1.0, -1.0)];\nPanoramaToCubeMapTools.FACE_RIGHT = [new Vector3(1.0, -1.0, 1.0), new Vector3(-1.0, -1.0, 1.0), new Vector3(1.0, 1.0, 1.0), new Vector3(-1.0, 1.0, 1.0)];\nPanoramaToCubeMapTools.FACE_FRONT = [new Vector3(1.0, -1.0, -1.0), new Vector3(1.0, -1.0, 1.0), new Vector3(1.0, 1.0, -1.0), new Vector3(1.0, 1.0, 1.0)];\nPanoramaToCubeMapTools.FACE_BACK = [new Vector3(-1.0, -1.0, 1.0), new Vector3(-1.0, -1.0, -1.0), new Vector3(-1.0, 1.0, 1.0), new Vector3(-1.0, 1.0, -1.0)];\nPanoramaToCubeMapTools.FACE_DOWN = [new Vector3(1.0, 1.0, -1.0), new Vector3(1.0, 1.0, 1.0), new Vector3(-1.0, 1.0, -1.0), new Vector3(-1.0, 1.0, 1.0)];\nPanoramaToCubeMapTools.FACE_UP = [new Vector3(-1.0, -1.0, -1.0), new Vector3(-1.0, -1.0, 1.0), new Vector3(1.0, -1.0, -1.0), new Vector3(1.0, -1.0, 1.0)];\n//# sourceMappingURL=panoramaToCubemap.js.map", "import { PanoramaToCubeMapTools } from \"./panoramaToCubemap.js\";\n/* This groups tools to convert HDR texture to native colors array. */\nfunction ldexp(mantissa, exponent) {\n    if (exponent > 1023) {\n        return mantissa * Math.pow(2, 1023) * Math.pow(2, exponent - 1023);\n    }\n    if (exponent < -1074) {\n        return mantissa * Math.pow(2, -1074) * Math.pow(2, exponent + 1074);\n    }\n    return mantissa * Math.pow(2, exponent);\n}\nfunction rgbe2float(float32array, red, green, blue, exponent, index) {\n    if (exponent > 0) {\n        /*nonzero pixel*/\n        exponent = ldexp(1.0, exponent - (128 + 8));\n        float32array[index + 0] = red * exponent;\n        float32array[index + 1] = green * exponent;\n        float32array[index + 2] = blue * exponent;\n    }\n    else {\n        float32array[index + 0] = 0;\n        float32array[index + 1] = 0;\n        float32array[index + 2] = 0;\n    }\n}\nfunction readStringLine(uint8array, startIndex) {\n    let line = \"\";\n    let character = \"\";\n    for (let i = startIndex; i < uint8array.length - startIndex; i++) {\n        character = String.fromCharCode(uint8array[i]);\n        if (character == \"\\n\") {\n            break;\n        }\n        line += character;\n    }\n    return line;\n}\n/**\n * Reads header information from an RGBE texture stored in a native array.\n * More information on this format are available here:\n * https://en.wikipedia.org/wiki/RGBE_image_format\n *\n * @param uint8array The binary file stored in  native array.\n * @returns The header information.\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function RGBE_ReadHeader(uint8array) {\n    let height = 0;\n    let width = 0;\n    let line = readStringLine(uint8array, 0);\n    if (line[0] != \"#\" || line[1] != \"?\") {\n        // eslint-disable-next-line no-throw-literal\n        throw \"Bad HDR Format.\";\n    }\n    let endOfHeader = false;\n    let findFormat = false;\n    let lineIndex = 0;\n    do {\n        lineIndex += line.length + 1;\n        line = readStringLine(uint8array, lineIndex);\n        if (line == \"FORMAT=32-bit_rle_rgbe\") {\n            findFormat = true;\n        }\n        else if (line.length == 0) {\n            endOfHeader = true;\n        }\n    } while (!endOfHeader);\n    if (!findFormat) {\n        // eslint-disable-next-line no-throw-literal\n        throw \"HDR Bad header format, unsupported FORMAT\";\n    }\n    lineIndex += line.length + 1;\n    line = readStringLine(uint8array, lineIndex);\n    const sizeRegexp = /^-Y (.*) \\+X (.*)$/g;\n    const match = sizeRegexp.exec(line);\n    // TODO. Support +Y and -X if needed.\n    if (!match || match.length < 3) {\n        // eslint-disable-next-line no-throw-literal\n        throw \"HDR Bad header format, no size\";\n    }\n    width = parseInt(match[2]);\n    height = parseInt(match[1]);\n    if (width < 8 || width > 0x7fff) {\n        // eslint-disable-next-line no-throw-literal\n        throw \"HDR Bad header format, unsupported size\";\n    }\n    lineIndex += line.length + 1;\n    return {\n        height: height,\n        width: width,\n        dataPosition: lineIndex,\n    };\n}\n/**\n * Returns the cubemap information (each faces texture data) extracted from an RGBE texture.\n * This RGBE texture needs to store the information as a panorama.\n *\n * More information on this format are available here:\n * https://en.wikipedia.org/wiki/RGBE_image_format\n *\n * @param buffer The binary file stored in an array buffer.\n * @param size The expected size of the extracted cubemap.\n * @param supersample enable supersampling the cubemap (default: false)\n * @returns The Cube Map information.\n */\nexport function GetCubeMapTextureData(buffer, size, supersample = false) {\n    const uint8array = new Uint8Array(buffer);\n    const hdrInfo = RGBE_ReadHeader(uint8array);\n    const data = RGBE_ReadPixels(uint8array, hdrInfo);\n    const cubeMapData = PanoramaToCubeMapTools.ConvertPanoramaToCubemap(data, hdrInfo.width, hdrInfo.height, size, supersample);\n    return cubeMapData;\n}\n/**\n * Returns the pixels data extracted from an RGBE texture.\n * This pixels will be stored left to right up to down in the R G B order in one array.\n *\n * More information on this format are available here:\n * https://en.wikipedia.org/wiki/RGBE_image_format\n *\n * @param uint8array The binary file stored in an array buffer.\n * @param hdrInfo The header information of the file.\n * @returns The pixels data in RGB right to left up to down order.\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function RGBE_ReadPixels(uint8array, hdrInfo) {\n    return readRGBEPixelsRLE(uint8array, hdrInfo);\n}\nfunction readRGBEPixelsRLE(uint8array, hdrInfo) {\n    let num_scanlines = hdrInfo.height;\n    const scanline_width = hdrInfo.width;\n    let a, b, c, d, count;\n    let dataIndex = hdrInfo.dataPosition;\n    let index = 0, endIndex = 0, i = 0;\n    const scanLineArrayBuffer = new ArrayBuffer(scanline_width * 4); // four channel R G B E\n    const scanLineArray = new Uint8Array(scanLineArrayBuffer);\n    // 3 channels of 4 bytes per pixel in float.\n    const resultBuffer = new ArrayBuffer(hdrInfo.width * hdrInfo.height * 4 * 3);\n    const resultArray = new Float32Array(resultBuffer);\n    // read in each successive scanline\n    while (num_scanlines > 0) {\n        a = uint8array[dataIndex++];\n        b = uint8array[dataIndex++];\n        c = uint8array[dataIndex++];\n        d = uint8array[dataIndex++];\n        if (a != 2 || b != 2 || c & 0x80 || hdrInfo.width < 8 || hdrInfo.width > 32767) {\n            return readRGBEPixelsNotRLE(uint8array, hdrInfo);\n        }\n        if (((c << 8) | d) != scanline_width) {\n            // eslint-disable-next-line no-throw-literal\n            throw \"HDR Bad header format, wrong scan line width\";\n        }\n        index = 0;\n        // read each of the four channels for the scanline into the buffer\n        for (i = 0; i < 4; i++) {\n            endIndex = (i + 1) * scanline_width;\n            while (index < endIndex) {\n                a = uint8array[dataIndex++];\n                b = uint8array[dataIndex++];\n                if (a > 128) {\n                    // a run of the same value\n                    count = a - 128;\n                    if (count == 0 || count > endIndex - index) {\n                        // eslint-disable-next-line no-throw-literal\n                        throw \"HDR Bad Format, bad scanline data (run)\";\n                    }\n                    while (count-- > 0) {\n                        scanLineArray[index++] = b;\n                    }\n                }\n                else {\n                    // a non-run\n                    count = a;\n                    if (count == 0 || count > endIndex - index) {\n                        // eslint-disable-next-line no-throw-literal\n                        throw \"HDR Bad Format, bad scanline data (non-run)\";\n                    }\n                    scanLineArray[index++] = b;\n                    if (--count > 0) {\n                        for (let j = 0; j < count; j++) {\n                            scanLineArray[index++] = uint8array[dataIndex++];\n                        }\n                    }\n                }\n            }\n        }\n        // now convert data from buffer into floats\n        for (i = 0; i < scanline_width; i++) {\n            a = scanLineArray[i];\n            b = scanLineArray[i + scanline_width];\n            c = scanLineArray[i + 2 * scanline_width];\n            d = scanLineArray[i + 3 * scanline_width];\n            rgbe2float(resultArray, a, b, c, d, (hdrInfo.height - num_scanlines) * scanline_width * 3 + i * 3);\n        }\n        num_scanlines--;\n    }\n    return resultArray;\n}\nfunction readRGBEPixelsNotRLE(uint8array, hdrInfo) {\n    // this file is not run length encoded\n    // read values sequentially\n    let num_scanlines = hdrInfo.height;\n    const scanline_width = hdrInfo.width;\n    let a, b, c, d, i;\n    let dataIndex = hdrInfo.dataPosition;\n    // 3 channels of 4 bytes per pixel in float.\n    const resultBuffer = new ArrayBuffer(hdrInfo.width * hdrInfo.height * 4 * 3);\n    const resultArray = new Float32Array(resultBuffer);\n    // read in each successive scanline\n    while (num_scanlines > 0) {\n        for (i = 0; i < hdrInfo.width; i++) {\n            a = uint8array[dataIndex++];\n            b = uint8array[dataIndex++];\n            c = uint8array[dataIndex++];\n            d = uint8array[dataIndex++];\n            rgbe2float(resultArray, a, b, c, d, (hdrInfo.height - num_scanlines) * scanline_width * 3 + i * 3);\n        }\n        num_scanlines--;\n    }\n    return resultArray;\n}\n/**\n * @deprecated Use functions separately\n */\nexport const HDRTools = {\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    RGBE_ReadHeader,\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    GetCubeMapTextureData,\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    RGBE_ReadPixels,\n};\n//# sourceMappingURL=hdr.js.map"], "names": ["PanoramaToCubeMapTools", "float32Array", "inputWidth", "inputHeight", "size", "supersample", "textureFront", "textureBack", "textureLeft", "textureRight", "textureUp", "textureDown", "texSize", "faceData", "buffer", "textureArray", "samples", "sampleFactor", "sampleFactorSqr", "rotDX1", "rotDX2", "dy", "fy", "y", "sy", "xv1", "xv2", "x", "sx", "v", "color", "vDir", "theta", "phi", "dx", "px", "py", "inputY", "r", "g", "b", "Vector3", "ldexp", "mantissa", "exponent", "rgbe2float", "float32array", "red", "green", "blue", "index", "readStringLine", "uint8array", "startIndex", "line", "character", "i", "RGBE_ReadHeader", "height", "width", "endOfHeader", "findFormat", "lineIndex", "match", "GetCubeMapTextureData", "hdrInfo", "data", "RGBE_ReadPixels", "readRGBEPixelsRLE", "num_scanlines", "scanline_width", "a", "c", "d", "count", "dataIndex", "endIndex", "scanLineArrayBuffer", "scanLineArray", "result<PERSON><PERSON><PERSON>", "resultArray", "readRGBEPixelsNotRLE", "j"], "mappings": "wCAKO,MAAMA,CAAuB,CAWhC,OAAO,yBAAyBC,EAAcC,EAAYC,EAAaC,EAAMC,EAAc,GAAO,CAC9F,GAAI,CAACJ,EAED,KAAM,iDAEV,GAAIA,EAAa,QAAUC,EAAaC,EAAc,EAElD,KAAM,gDAEV,MAAMG,EAAe,KAAK,qBAAqBF,EAAM,KAAK,WAAYH,EAAcC,EAAYC,EAAaE,CAAW,EAClHE,EAAc,KAAK,qBAAqBH,EAAM,KAAK,UAAWH,EAAcC,EAAYC,EAAaE,CAAW,EAChHG,EAAc,KAAK,qBAAqBJ,EAAM,KAAK,UAAWH,EAAcC,EAAYC,EAAaE,CAAW,EAChHI,EAAe,KAAK,qBAAqBL,EAAM,KAAK,WAAYH,EAAcC,EAAYC,EAAaE,CAAW,EAClHK,EAAY,KAAK,qBAAqBN,EAAM,KAAK,QAASH,EAAcC,EAAYC,EAAaE,CAAW,EAC5GM,EAAc,KAAK,qBAAqBP,EAAM,KAAK,UAAWH,EAAcC,EAAYC,EAAaE,CAAW,EACtH,MAAO,CACH,MAAOC,EACP,KAAMC,EACN,KAAMC,EACN,MAAOC,EACP,GAAIC,EACJ,KAAMC,EACN,KAAMP,EACN,KAAM,EACN,OAAQ,EACR,WAAY,EACxB,CACK,CACD,OAAO,qBAAqBQ,EAASC,EAAUZ,EAAcC,EAAYC,EAAaE,EAAc,GAAO,CACvG,MAAMS,EAAS,IAAI,YAAYF,EAAUA,EAAU,EAAI,CAAC,EAClDG,EAAe,IAAI,aAAaD,CAAM,EAEtCE,EAAUX,EAAc,KAAK,IAAI,EAAG,KAAK,MAAMH,EAAa,EAAIU,CAAO,CAAC,EAAI,EAC5EK,EAAe,EAAID,EACnBE,EAAkBD,EAAeA,EACjCE,EAASN,EAAS,CAAC,EAAE,SAASA,EAAS,CAAC,CAAC,EAAE,MAAMI,EAAeL,CAAO,EACvEQ,EAASP,EAAS,CAAC,EAAE,SAASA,EAAS,CAAC,CAAC,EAAE,MAAMI,EAAeL,CAAO,EACvES,EAAK,EAAIT,EACf,IAAIU,EAAK,EACT,QAASC,EAAI,EAAGA,EAAIX,EAASW,IACzB,QAASC,EAAK,EAAGA,EAAKR,EAASQ,IAAM,CACjC,IAAIC,EAAMZ,EAAS,CAAC,EAChBa,EAAMb,EAAS,CAAC,EACpB,QAASc,EAAI,EAAGA,EAAIf,EAASe,IACzB,QAASC,EAAK,EAAGA,EAAKZ,EAASY,IAAM,CACjC,MAAMC,EAAIH,EAAI,SAASD,CAAG,EAAE,MAAMH,CAAE,EAAE,IAAIG,CAAG,EAC7CI,EAAE,UAAS,EACX,MAAMC,EAAQ,KAAK,wBAAwBD,EAAG5B,EAAcC,EAAYC,CAAW,EAEnFY,EAAaQ,EAAIX,EAAU,EAAIe,EAAI,EAAI,CAAC,GAAKG,EAAM,EAAIZ,EACvDH,EAAaQ,EAAIX,EAAU,EAAIe,EAAI,EAAI,CAAC,GAAKG,EAAM,EAAIZ,EACvDH,EAAaQ,EAAIX,EAAU,EAAIe,EAAI,EAAI,CAAC,GAAKG,EAAM,EAAIZ,EACvDO,EAAMA,EAAI,IAAIN,CAAM,EACpBO,EAAMA,EAAI,IAAIN,CAAM,CACvB,CAELE,GAAMD,EAAKJ,CACd,CAEL,OAAOF,CACV,CACD,OAAO,wBAAwBgB,EAAM9B,EAAcC,EAAYC,EAAa,CACxE,IAAI6B,EAAQ,KAAK,MAAMD,EAAK,EAAGA,EAAK,CAAC,EACrC,MAAME,EAAM,KAAK,KAAKF,EAAK,CAAC,EAC5B,KAAOC,EAAQ,CAAC,KAAK,IACjBA,GAAS,EAAI,KAAK,GAEtB,KAAOA,EAAQ,KAAK,IAChBA,GAAS,EAAI,KAAK,GAEtB,IAAIE,EAAKF,EAAQ,KAAK,GACtB,MAAMX,EAAKY,EAAM,KAAK,GAEtBC,EAAKA,EAAK,GAAM,GAChB,IAAIC,EAAK,KAAK,MAAMD,EAAKhC,CAAU,EAC/BiC,EAAK,EACLA,EAAK,EAEAA,GAAMjC,IACXiC,EAAKjC,EAAa,GAEtB,IAAIkC,EAAK,KAAK,MAAMf,EAAKlB,CAAW,EAChCiC,EAAK,EACLA,EAAK,EAEAA,GAAMjC,IACXiC,EAAKjC,EAAc,GAEvB,MAAMkC,EAASlC,EAAciC,EAAK,EAC5BE,EAAIrC,EAAaoC,EAASnC,EAAa,EAAIiC,EAAK,EAAI,CAAC,EACrDI,EAAItC,EAAaoC,EAASnC,EAAa,EAAIiC,EAAK,EAAI,CAAC,EACrDK,EAAIvC,EAAaoC,EAASnC,EAAa,EAAIiC,EAAK,EAAI,CAAC,EAC3D,MAAO,CACH,EAAGG,EACH,EAAGC,EACH,EAAGC,CACf,CACK,CACL,CACAxC,EAAuB,UAAY,CAAC,IAAIyC,EAAQ,GAAM,GAAM,EAAI,EAAG,IAAIA,EAAQ,EAAK,GAAM,EAAI,EAAG,IAAIA,EAAQ,GAAM,EAAK,EAAI,EAAG,IAAIA,EAAQ,EAAK,EAAK,EAAI,CAAC,EAC1JzC,EAAuB,WAAa,CAAC,IAAIyC,EAAQ,EAAK,GAAM,CAAG,EAAG,IAAIA,EAAQ,GAAM,GAAM,CAAG,EAAG,IAAIA,EAAQ,EAAK,EAAK,CAAG,EAAG,IAAIA,EAAQ,GAAM,EAAK,CAAG,CAAC,EACvJzC,EAAuB,WAAa,CAAC,IAAIyC,EAAQ,EAAK,GAAM,EAAI,EAAG,IAAIA,EAAQ,EAAK,GAAM,CAAG,EAAG,IAAIA,EAAQ,EAAK,EAAK,EAAI,EAAG,IAAIA,EAAQ,EAAK,EAAK,CAAG,CAAC,EACvJzC,EAAuB,UAAY,CAAC,IAAIyC,EAAQ,GAAM,GAAM,CAAG,EAAG,IAAIA,EAAQ,GAAM,GAAM,EAAI,EAAG,IAAIA,EAAQ,GAAM,EAAK,CAAG,EAAG,IAAIA,EAAQ,GAAM,EAAK,EAAI,CAAC,EAC1JzC,EAAuB,UAAY,CAAC,IAAIyC,EAAQ,EAAK,EAAK,EAAI,EAAG,IAAIA,EAAQ,EAAK,EAAK,CAAG,EAAG,IAAIA,EAAQ,GAAM,EAAK,EAAI,EAAG,IAAIA,EAAQ,GAAM,EAAK,CAAG,CAAC,EACtJzC,EAAuB,QAAU,CAAC,IAAIyC,EAAQ,GAAM,GAAM,EAAI,EAAG,IAAIA,EAAQ,GAAM,GAAM,CAAG,EAAG,IAAIA,EAAQ,EAAK,GAAM,EAAI,EAAG,IAAIA,EAAQ,EAAK,GAAM,CAAG,CAAC,ECtHxJ,SAASC,EAAMC,EAAUC,EAAU,CAC/B,OAAIA,EAAW,KACJD,EAAW,KAAK,IAAI,EAAG,IAAI,EAAI,KAAK,IAAI,EAAGC,EAAW,IAAI,EAEjEA,EAAW,MACJD,EAAW,KAAK,IAAI,EAAG,KAAK,EAAI,KAAK,IAAI,EAAGC,EAAW,IAAI,EAE/DD,EAAW,KAAK,IAAI,EAAGC,CAAQ,CAC1C,CACA,SAASC,EAAWC,EAAcC,EAAKC,EAAOC,EAAML,EAAUM,EAAO,CAC7DN,EAAW,GAEXA,EAAWF,EAAM,EAAKE,EAAY,GAAQ,EAC1CE,EAAaI,EAAQ,CAAC,EAAIH,EAAMH,EAChCE,EAAaI,EAAQ,CAAC,EAAIF,EAAQJ,EAClCE,EAAaI,EAAQ,CAAC,EAAID,EAAOL,IAGjCE,EAAaI,EAAQ,CAAC,EAAI,EAC1BJ,EAAaI,EAAQ,CAAC,EAAI,EAC1BJ,EAAaI,EAAQ,CAAC,EAAI,EAElC,CACA,SAASC,EAAeC,EAAYC,EAAY,CAC5C,IAAIC,EAAO,GACPC,EAAY,GAChB,QAASC,EAAIH,EAAYG,EAAIJ,EAAW,OAASC,IAC7CE,EAAY,OAAO,aAAaH,EAAWI,CAAC,CAAC,EACzCD,GAAa;AAAA,GAFwCC,IAKzDF,GAAQC,EAEZ,OAAOD,CACX,CAUO,SAASG,EAAgBL,EAAY,CACxC,IAAIM,EAAS,EACTC,EAAQ,EACRL,EAAOH,EAAeC,EAAY,CAAC,EACvC,GAAIE,EAAK,CAAC,GAAK,KAAOA,EAAK,CAAC,GAAK,IAE7B,KAAM,kBAEV,IAAIM,EAAc,GACdC,EAAa,GACbC,EAAY,EAChB,GACIA,GAAaR,EAAK,OAAS,EAC3BA,EAAOH,EAAeC,EAAYU,CAAS,EACvCR,GAAQ,yBACRO,EAAa,GAERP,EAAK,QAAU,IACpBM,EAAc,UAEb,CAACA,GACV,GAAI,CAACC,EAED,KAAM,4CAEVC,GAAaR,EAAK,OAAS,EAC3BA,EAAOH,EAAeC,EAAYU,CAAS,EAE3C,MAAMC,EADa,sBACM,KAAKT,CAAI,EAElC,GAAI,CAACS,GAASA,EAAM,OAAS,EAEzB,KAAM,iCAIV,GAFAJ,EAAQ,SAASI,EAAM,CAAC,CAAC,EACzBL,EAAS,SAASK,EAAM,CAAC,CAAC,EACtBJ,EAAQ,GAAKA,EAAQ,MAErB,KAAM,0CAEV,OAAAG,GAAaR,EAAK,OAAS,EACpB,CACH,OAAQI,EACR,MAAOC,EACP,aAAcG,CACtB,CACA,CAaO,SAASE,EAAsBlD,EAAQV,EAAMC,EAAc,GAAO,CACrE,MAAM+C,EAAa,IAAI,WAAWtC,CAAM,EAClCmD,EAAUR,EAAgBL,CAAU,EACpCc,EAAOC,EAAgBf,EAAYa,CAAO,EAEhD,OADoBjE,EAAuB,yBAAyBkE,EAAMD,EAAQ,MAAOA,EAAQ,OAAQ7D,EAAMC,CAAW,CAE9H,CAaO,SAAS8D,EAAgBf,EAAYa,EAAS,CACjD,OAAOG,EAAkBhB,EAAYa,CAAO,CAChD,CACA,SAASG,EAAkBhB,EAAYa,EAAS,CAC5C,IAAII,EAAgBJ,EAAQ,OAC5B,MAAMK,EAAiBL,EAAQ,MAC/B,IAAIM,EAAG/B,EAAGgC,EAAGC,EAAGC,EACZC,EAAYV,EAAQ,aACpBf,EAAQ,EAAG0B,EAAW,EAAGpB,EAAI,EACjC,MAAMqB,EAAsB,IAAI,YAAYP,EAAiB,CAAC,EACxDQ,EAAgB,IAAI,WAAWD,CAAmB,EAElDE,EAAe,IAAI,YAAYd,EAAQ,MAAQA,EAAQ,OAAS,EAAI,CAAC,EACrEe,EAAc,IAAI,aAAaD,CAAY,EAEjD,KAAOV,EAAgB,GAAG,CAKtB,GAJAE,EAAInB,EAAWuB,GAAW,EAC1BnC,EAAIY,EAAWuB,GAAW,EAC1BH,EAAIpB,EAAWuB,GAAW,EAC1BF,EAAIrB,EAAWuB,GAAW,EACtBJ,GAAK,GAAK/B,GAAK,GAAKgC,EAAI,KAAQP,EAAQ,MAAQ,GAAKA,EAAQ,MAAQ,MACrE,OAAOgB,EAAqB7B,EAAYa,CAAO,EAEnD,IAAMO,GAAK,EAAKC,IAAMH,EAElB,KAAM,+CAIV,IAFApB,EAAQ,EAEHM,EAAI,EAAGA,EAAI,EAAGA,IAEf,IADAoB,GAAYpB,EAAI,GAAKc,EACdpB,EAAQ0B,GAGX,GAFAL,EAAInB,EAAWuB,GAAW,EAC1BnC,EAAIY,EAAWuB,GAAW,EACtBJ,EAAI,IAAK,CAGT,GADAG,EAAQH,EAAI,IACRG,GAAS,GAAKA,EAAQE,EAAW1B,EAEjC,KAAM,0CAEV,KAAOwB,KAAU,GACbI,EAAc5B,GAAO,EAAIV,CAEhC,KACI,CAGD,GADAkC,EAAQH,EACJG,GAAS,GAAKA,EAAQE,EAAW1B,EAEjC,KAAM,8CAGV,GADA4B,EAAc5B,GAAO,EAAIV,EACrB,EAAEkC,EAAQ,EACV,QAASQ,EAAI,EAAGA,EAAIR,EAAOQ,IACvBJ,EAAc5B,GAAO,EAAIE,EAAWuB,GAAW,CAG1D,CAIT,IAAKnB,EAAI,EAAGA,EAAIc,EAAgBd,IAC5Be,EAAIO,EAActB,CAAC,EACnBhB,EAAIsC,EAActB,EAAIc,CAAc,EACpCE,EAAIM,EAActB,EAAI,EAAIc,CAAc,EACxCG,EAAIK,EAActB,EAAI,EAAIc,CAAc,EACxCzB,EAAWmC,EAAaT,EAAG/B,EAAGgC,EAAGC,GAAIR,EAAQ,OAASI,GAAiBC,EAAiB,EAAId,EAAI,CAAC,EAErGa,GACH,CACD,OAAOW,CACX,CACA,SAASC,EAAqB7B,EAAYa,EAAS,CAG/C,IAAII,EAAgBJ,EAAQ,OAC5B,MAAMK,EAAiBL,EAAQ,MAC/B,IAAIM,EAAG/B,EAAGgC,EAAGC,EAAGjB,EACZmB,EAAYV,EAAQ,aAExB,MAAMc,EAAe,IAAI,YAAYd,EAAQ,MAAQA,EAAQ,OAAS,EAAI,CAAC,EACrEe,EAAc,IAAI,aAAaD,CAAY,EAEjD,KAAOV,EAAgB,GAAG,CACtB,IAAKb,EAAI,EAAGA,EAAIS,EAAQ,MAAOT,IAC3Be,EAAInB,EAAWuB,GAAW,EAC1BnC,EAAIY,EAAWuB,GAAW,EAC1BH,EAAIpB,EAAWuB,GAAW,EAC1BF,EAAIrB,EAAWuB,GAAW,EAC1B9B,EAAWmC,EAAaT,EAAG/B,EAAGgC,EAAGC,GAAIR,EAAQ,OAASI,GAAiBC,EAAiB,EAAId,EAAI,CAAC,EAErGa,GACH,CACD,OAAOW,CACX", "x_google_ignoreList": [0, 1]}