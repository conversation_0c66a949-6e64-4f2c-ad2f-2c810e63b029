import{SvelteComponent as z,init as B,safe_not_equal as F,element as y,space as S,claim_element as E,children as N,detach as d,claim_space as D,attr as w,toggle_class as H,insert_hydration as p,append_hydration as k,group_outros as J,transition_out as P,check_outros as K,transition_in as U,create_component as G,claim_component as R,mount_component as T,destroy_component as V,text as h,empty as A,claim_text as b,set_data as I}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import"./2.B2AoQPnG.js";import{U as L,I as M}from"./Upload.yOHVlgUe.js";const O=/^(#\s*)(.+)$/m;function Q(r){const t=r.trim(),i=t.match(O);if(!i)return[!1,t||!1];const[e,,l]=i,n=l.trim();if(t===e)return[n,!1];const o=i.index!==void 0?i.index+e.length:0,u=t.substring(o).trim()||!1;return[n,u]}function W(r){let t,i;return t=new L({}),{c(){G(t.$$.fragment)},l(e){R(t.$$.fragment,e)},m(e,l){T(t,e,l),i=!0},i(e){i||(U(t.$$.fragment,e),i=!0)},o(e){P(t.$$.fragment,e),i=!1},d(e){V(t,e)}}}function X(r){let t,i;return t=new M({}),{c(){G(t.$$.fragment)},l(e){R(t.$$.fragment,e)},m(e,l){T(t,e,l),i=!0},i(e){i||(U(t.$$.fragment,e),i=!0)},o(e){P(t.$$.fragment,e),i=!1},d(e){V(t,e)}}}function Y(r){let t=r[1](r[7][r[0]]||r[7].file)+"",i,e,l,n=r[3]!=="short"&&j(r);return{c(){i=h(t),e=S(),n&&n.c(),l=A()},l(o){i=b(o,t),e=D(o),n&&n.l(o),l=A()},m(o,a){p(o,i,a),p(o,e,a),n&&n.m(o,a),p(o,l,a)},p(o,a){a&3&&t!==(t=o[1](o[7][o[0]]||o[7].file)+"")&&I(i,t),o[3]!=="short"?n?n.p(o,a):(n=j(o),n.c(),n.m(l.parentNode,l)):n&&(n.d(1),n=null)},d(o){o&&(d(i),d(e),d(l)),n&&n.d(o)}}}function Z(r){let t,i,e=r[6]&&q(r),l=r[5]&&C(r);return{c(){e&&e.c(),t=S(),l&&l.c(),i=A()},l(n){e&&e.l(n),t=D(n),l&&l.l(n),i=A()},m(n,o){e&&e.m(n,o),p(n,t,o),l&&l.m(n,o),p(n,i,o)},p(n,o){n[6]?e?e.p(n,o):(e=q(n),e.c(),e.m(t.parentNode,t)):e&&(e.d(1),e=null),n[5]?l?l.p(n,o):(l=C(n),l.c(),l.m(i.parentNode,i)):l&&(l.d(1),l=null)},d(n){n&&(d(t),d(i)),e&&e.d(n),l&&l.d(n)}}}function j(r){let t,i,e=r[1]("common.or")+"",l,n,o,a=(r[2]||r[1]("upload_text.click_to_upload"))+"",u;return{c(){t=y("span"),i=h("- "),l=h(e),n=h(" -"),o=S(),u=h(a),this.h()},l(_){t=E(_,"SPAN",{class:!0});var c=N(t);i=b(c,"- "),l=b(c,e),n=b(c," -"),c.forEach(d),o=D(_),u=b(_,a),this.h()},h(){w(t,"class","or svelte-12ioyct")},m(_,c){p(_,t,c),k(t,i),k(t,l),k(t,n),p(_,o,c),p(_,u,c)},p(_,c){c&2&&e!==(e=_[1]("common.or")+"")&&I(l,e),c&6&&a!==(a=(_[2]||_[1]("upload_text.click_to_upload"))+"")&&I(u,a)},d(_){_&&(d(t),d(o),d(u))}}}function q(r){let t,i;return{c(){t=y("h2"),i=h(r[6]),this.h()},l(e){t=E(e,"H2",{class:!0});var l=N(t);i=b(l,r[6]),l.forEach(d),this.h()},h(){w(t,"class","svelte-12ioyct")},m(e,l){p(e,t,l),k(t,i)},p(e,l){l&64&&I(i,e[6])},d(e){e&&d(t)}}}function C(r){let t,i;return{c(){t=y("p"),i=h(r[5]),this.h()},l(e){t=E(e,"P",{class:!0});var l=N(t);i=b(l,r[5]),l.forEach(d),this.h()},h(){w(t,"class","svelte-12ioyct")},m(e,l){p(e,t,l),k(t,i)},p(e,l){l&32&&I(i,e[5])},d(e){e&&d(t)}}}function x(r){let t,i,e,l,n,o;const a=[X,W],u=[];function _(s,m){return s[0]==="clipboard"?0:1}e=_(r),l=u[e]=a[e](r);function c(s,m){return s[6]||s[5]?Z:Y}let v=c(r),f=v(r);return{c(){t=y("div"),i=y("span"),l.c(),n=S(),f.c(),this.h()},l(s){t=E(s,"DIV",{class:!0});var m=N(t);i=E(m,"SPAN",{class:!0});var g=N(i);l.l(g),g.forEach(d),n=D(m),f.l(m),m.forEach(d),this.h()},h(){w(i,"class","icon-wrap svelte-12ioyct"),H(i,"hovered",r[4]),w(t,"class","wrap svelte-12ioyct")},m(s,m){p(s,t,m),k(t,i),u[e].m(i,null),k(t,n),f.m(t,null),o=!0},p(s,[m]){let g=e;e=_(s),e!==g&&(J(),P(u[g],1,1,()=>{u[g]=null}),K(),l=u[e],l||(l=u[e]=a[e](s),l.c()),U(l,1),l.m(i,null)),(!o||m&16)&&H(i,"hovered",s[4]),v===(v=c(s))&&f?f.p(s,m):(f.d(1),f=v(s),f&&(f.c(),f.m(t,null)))},i(s){o||(U(l),o=!0)},o(s){P(l),o=!1},d(s){s&&d(t),u[e].d(),f.d()}}}function $(r,t,i){let e,l,{type:n="file"}=t,{i18n:o}=t,{message:a=void 0}=t,{mode:u="full"}=t,{hovered:_=!1}=t,{placeholder:c=void 0}=t;const v={image:"upload_text.drop_image",video:"upload_text.drop_video",audio:"upload_text.drop_audio",file:"upload_text.drop_file",csv:"upload_text.drop_csv",gallery:"upload_text.drop_gallery",clipboard:"upload_text.paste_clipboard"};return r.$$set=f=>{"type"in f&&i(0,n=f.type),"i18n"in f&&i(1,o=f.i18n),"message"in f&&i(2,a=f.message),"mode"in f&&i(3,u=f.mode),"hovered"in f&&i(4,_=f.hovered),"placeholder"in f&&i(8,c=f.placeholder)},r.$$.update=()=>{r.$$.dirty&256&&i(6,[e,l]=c?Q(c):[!1,!1],e,(i(5,l),i(8,c)))},[n,o,a,u,_,l,e,v,c]}class ne extends z{constructor(t){super(),B(this,t,$,x,F,{type:0,i18n:1,message:2,mode:3,hovered:4,placeholder:8})}}export{ne as U};
//# sourceMappingURL=UploadText.CJcy9n89.js.map
