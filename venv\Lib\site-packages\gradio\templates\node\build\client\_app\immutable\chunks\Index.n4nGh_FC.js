import{SvelteComponent as A,init as F,safe_not_equal as G,create_component as k,claim_component as v,mount_component as S,transition_in as g,transition_out as b,destroy_component as B,assign as K,element as M,space as D,claim_element as P,children as Q,detach as w,claim_space as E,add_render_callback as R,insert_hydration as J,add_iframe_resize_listener as T,group_outros as U,check_outros as W,get_spread_update as X,get_spread_object as Y}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{J as Z,a as y}from"./JSON.CA9rn_1b.js";import{B as x,S as p}from"./2.B2AoQPnG.js";import{B as $}from"./BlockLabel.BTSz9r5s.js";function H(t){let e,a;return e=new $({props:{Icon:y,show_label:t[6],label:t[5],float:!1,disable:t[7]===!1}}),{c(){k(e.$$.fragment)},l(l){v(e.$$.fragment,l)},m(l,_){S(e,l,_),a=!0},p(l,_){const s={};_&64&&(s.show_label=l[6]),_&32&&(s.label=l[5]),_&128&&(s.disable=l[7]===!1),e.$set(s)},i(l){a||(g(e.$$.fragment,l),a=!0)},o(l){b(e.$$.fragment,l),a=!1},d(l){B(e,l)}}}function ee(t){let e,a,l,_,s,h,u,f=t[5]&&H(t);const c=[{autoscroll:t[10].autoscroll},{i18n:t[10].i18n},t[4]];let r={};for(let i=0;i<c.length;i+=1)r=K(r,c[i]);return _=new p({props:r}),_.$on("clear_status",t[20]),h=new Z({props:{value:t[3],open:t[11],theme_mode:t[12],show_indices:t[13],label_height:t[17]}}),{c(){e=M("div"),f&&f.c(),l=D(),k(_.$$.fragment),s=D(),k(h.$$.fragment),this.h()},l(i){e=P(i,"DIV",{});var m=Q(e);f&&f.l(m),m.forEach(w),l=E(i),v(_.$$.fragment,i),s=E(i),v(h.$$.fragment,i),this.h()},h(){R(()=>t[19].call(e))},m(i,m){J(i,e,m),f&&f.m(e,null),a=T(e,t[19].bind(e)),J(i,l,m),S(_,i,m),J(i,s,m),S(h,i,m),u=!0},p(i,m){i[5]?f?(f.p(i,m),m&32&&g(f,1)):(f=H(i),f.c(),g(f,1),f.m(e,null)):f&&(U(),b(f,1,1,()=>{f=null}),W());const d=m&1040?X(c,[m&1024&&{autoscroll:i[10].autoscroll},m&1024&&{i18n:i[10].i18n},m&16&&Y(i[4])]):{};_.$set(d);const o={};m&8&&(o.value=i[3]),m&2048&&(o.open=i[11]),m&4096&&(o.theme_mode=i[12]),m&8192&&(o.show_indices=i[13]),m&131072&&(o.label_height=i[17]),h.$set(o)},i(i){u||(g(f),g(_.$$.fragment,i),g(h.$$.fragment,i),u=!0)},o(i){b(f),b(_.$$.fragment,i),b(h.$$.fragment,i),u=!1},d(i){i&&(w(e),w(l),w(s)),f&&f.d(),a(),B(_,i),B(h,i)}}}function le(t){let e,a;return e=new x({props:{visible:t[2],test_id:"json",elem_id:t[0],elem_classes:t[1],container:t[7],scale:t[8],min_width:t[9],padding:!1,allow_overflow:!0,overflow_behavior:"auto",height:t[14],min_height:t[15],max_height:t[16],$$slots:{default:[ee]},$$scope:{ctx:t}}}),{c(){k(e.$$.fragment)},l(l){v(e.$$.fragment,l)},m(l,_){S(e,l,_),a=!0},p(l,[_]){const s={};_&4&&(s.visible=l[2]),_&1&&(s.elem_id=l[0]),_&2&&(s.elem_classes=l[1]),_&128&&(s.container=l[7]),_&256&&(s.scale=l[8]),_&512&&(s.min_width=l[9]),_&16384&&(s.height=l[14]),_&32768&&(s.min_height=l[15]),_&65536&&(s.max_height=l[16]),_&2243832&&(s.$$scope={dirty:_,ctx:l}),e.$set(s)},i(l){a||(g(e.$$.fragment,l),a=!0)},o(l){b(e.$$.fragment,l),a=!1},d(l){B(e,l)}}}function ie(t,e,a){let{elem_id:l=""}=e,{elem_classes:_=[]}=e,{visible:s=!0}=e,{value:h}=e,u,{loading_status:f}=e,{label:c}=e,{show_label:r}=e,{container:i=!0}=e,{scale:m=null}=e,{min_width:d=void 0}=e,{gradio:o}=e,{open:z=!1}=e,{theme_mode:I}=e,{show_indices:N}=e,{height:O}=e,{min_height:q}=e,{max_height:C}=e,j=0;function L(){j=this.clientHeight,a(17,j)}const V=()=>o.dispatch("clear_status",f);return t.$$set=n=>{"elem_id"in n&&a(0,l=n.elem_id),"elem_classes"in n&&a(1,_=n.elem_classes),"visible"in n&&a(2,s=n.visible),"value"in n&&a(3,h=n.value),"loading_status"in n&&a(4,f=n.loading_status),"label"in n&&a(5,c=n.label),"show_label"in n&&a(6,r=n.show_label),"container"in n&&a(7,i=n.container),"scale"in n&&a(8,m=n.scale),"min_width"in n&&a(9,d=n.min_width),"gradio"in n&&a(10,o=n.gradio),"open"in n&&a(11,z=n.open),"theme_mode"in n&&a(12,I=n.theme_mode),"show_indices"in n&&a(13,N=n.show_indices),"height"in n&&a(14,O=n.height),"min_height"in n&&a(15,q=n.min_height),"max_height"in n&&a(16,C=n.max_height)},t.$$.update=()=>{t.$$.dirty&263176&&h!==u&&(a(18,u=h),o.dispatch("change"))},[l,_,s,h,f,c,r,i,m,d,o,z,I,N,O,q,C,j,u,L,V]}class fe extends A{constructor(e){super(),F(this,e,ie,le,G,{elem_id:0,elem_classes:1,visible:2,value:3,loading_status:4,label:5,show_label:6,container:7,scale:8,min_width:9,gradio:10,open:11,theme_mode:12,show_indices:13,height:14,min_height:15,max_height:16})}}export{Z as BaseJSON,fe as default};
//# sourceMappingURL=Index.n4nGh_FC.js.map
