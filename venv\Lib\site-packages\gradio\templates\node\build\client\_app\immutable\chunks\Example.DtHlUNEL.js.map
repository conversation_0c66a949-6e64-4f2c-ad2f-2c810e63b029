{"version": 3, "file": "Example.DtHlUNEL.js", "sources": ["../../../../../../../textbox/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\n\texport let value: string | null;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n\n\tlet size: number;\n\tlet el: HTMLDivElement;\n\n\tfunction set_styles(element: HTMLElement, el_width: number): void {\n\t\telement.style.setProperty(\n\t\t\t\"--local-text-width\",\n\t\t\t`${el_width && el_width < 150 ? el_width : 200}px`\n\t\t);\n\t\telement.style.whiteSpace = \"unset\";\n\t}\n\n\tfunction truncate_text(text: string | null, max_length = 60): string {\n\t\tif (!text) return \"\";\n\t\tconst str = String(text);\n\t\tif (str.length <= max_length) return str;\n\t\treturn str.slice(0, max_length) + \"...\";\n\t}\n\n\tonMount(() => {\n\t\tset_styles(el, size);\n\t});\n</script>\n\n<div\n\tbind:clientWidth={size}\n\tbind:this={el}\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{truncate_text(value)}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n\n\tdiv {\n\t\toverflow: hidden;\n\t\twhite-space: nowrap;\n\t}\n</style>\n"], "names": ["t_value", "truncate_text", "ctx", "toggle_class", "div", "insert_hydration", "target", "anchor", "dirty", "set_data", "t", "set_styles", "element", "el_width", "text", "max_length", "str", "value", "$$props", "type", "selected", "size", "el", "onMount", "$$value"], "mappings": "ocAqCEA,EAAAC,EAAcC,EAAK,CAAA,CAAA,EAAA,4KAJPC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAJlCG,EAQKC,EAAAF,EAAAG,CAAA,+CADHC,EAAA,GAAAR,KAAAA,EAAAC,EAAcC,EAAK,CAAA,CAAA,EAAA,KAAAO,EAAAC,EAAAV,CAAA,OAJPG,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,mEAxBxB,SAAAS,EAAWC,EAAsBC,EAAA,CACzCD,EAAQ,MAAM,YACb,qBAAA,GACGC,GAAYA,EAAW,IAAMA,EAAW,GAAG,IAAA,EAE/CD,EAAQ,MAAM,WAAa,QAGnB,SAAAX,EAAca,EAAqBC,EAAa,GAAA,KACnDD,EAAa,MAAA,GACZ,MAAAE,EAAM,OAAOF,CAAI,EACnB,OAAAE,EAAI,QAAUD,EAAmBC,EAC9BA,EAAI,MAAM,EAAGD,CAAU,EAAI,wBAnBxB,GAAA,CAAA,MAAAE,CAAA,EAAAC,EACA,CAAA,KAAAC,CAAA,EAAAD,GACA,SAAAE,EAAW,EAAA,EAAAF,EAElBG,EACAC,EAiBJC,EAAA,IAAA,CACCZ,EAAWW,EAAID,CAAI,iBAKFA,EAAI,KAAA,4DACXC,EAAEE"}