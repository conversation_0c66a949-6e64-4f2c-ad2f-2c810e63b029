{"version": 3, "file": "Canvas3D-DPGN8rC5.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Canvas3D.js"], "sourcesContent": ["import{create_ssr_component as O,add_attribute as P}from\"svelte/internal\";import{onMount as V}from\"svelte\";import{r as A}from\"./DownloadLink.js\";const B=O((L,a,e,S)=>{let r,h,{value:s}=a,{display_mode:c}=a,{clear_color:_}=a,{camera_position:m}=a,{zoom_speed:v}=a,{pan_speed:f}=a,{resolved_url:d=void 0}=a,w,y,i,u,p=!1;V(()=>((async()=>{h=await import(\"./index17.js\").then(o=>o.bY),h.createViewerForCanvas(y,{clearColor:_,useRightHandedSystem:!0,animationAutoPlay:!0,cameraAutoOrbit:{enabled:!1},onInitialized:o=>{u=o}}).then(o=>{i=o,p=!0})})(),()=>{i?.dispose()}));function C(t,o){u.scene.forcePointsCloud=t,u.scene.forceWireframe=o}function z(t){i&&(t?i.loadModel(t,{pluginOptions:{obj:{importVertexColors:!0}}}).then(()=>{c===\"point_cloud\"?C(!0,!1):c===\"wireframe\"?C(!1,!0):n(m,v,f)}):i.resetModel())}function n(t,o,I){const l=u.camera;t[0]!==null&&(l.alpha=t[0]*Math.PI/180),t[1]!==null&&(l.beta=t[1]*Math.PI/180),t[2]!==null&&(l.radius=t[2]),l.lowerRadiusLimit=.1;const R=()=>{l.wheelPrecision=250/(l.radius*o),l.panningSensibility=1e4*I/l.radius};R(),l.onAfterCheckInputsObservable.add(R)}function M(){u&&i.resetCamera()}if(a.value===void 0&&e.value&&s!==void 0&&e.value(s),a.display_mode===void 0&&e.display_mode&&c!==void 0&&e.display_mode(c),a.clear_color===void 0&&e.clear_color&&_!==void 0&&e.clear_color(_),a.camera_position===void 0&&e.camera_position&&m!==void 0&&e.camera_position(m),a.zoom_speed===void 0&&e.zoom_speed&&v!==void 0&&e.zoom_speed(v),a.pan_speed===void 0&&e.pan_speed&&f!==void 0&&e.pan_speed(f),a.resolved_url===void 0&&e.resolved_url&&d!==void 0&&e.resolved_url(d),a.update_camera===void 0&&e.update_camera&&n!==void 0&&e.update_camera(n),a.reset_camera_position===void 0&&e.reset_camera_position&&M!==void 0&&e.reset_camera_position(M),r=s.url,d=r,r){w=r;const t=r;A(r).then(o=>{w===t?d=o??void 0:o&&URL.revokeObjectURL(o)})}return p&&z(d),`<canvas${P(\"this\",y,0)}></canvas>`});export{B as default};\n//# sourceMappingURL=Canvas3D.js.map\n"], "names": ["O", "P"], "mappings": ";;;;;;;;AAAsJ,MAAC,CAAC,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAI,IAAC,CAAC,CAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAG,CAAC,CAA2e,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,4BAA4B,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,SAAS,CAAC,EAAE,CAAmB,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,qBAAqB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,eAAe,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,OAAe,CAAC,OAAO,EAAEC,aAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;;;;"}