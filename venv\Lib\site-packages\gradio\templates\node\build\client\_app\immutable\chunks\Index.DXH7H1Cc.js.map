{"version": 3, "file": "Index.DXH7H1Cc.js", "sources": ["../../../../../../../slider/Index.svelte"], "sourcesContent": ["<script context=\"module\">\n\tlet _id = 0;\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport { Block, BlockTitle } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { afterUpdate } from \"svelte\";\n\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tinput: never;\n\t\trelease: number;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value = 0;\n\tlet initial_value = value;\n\n\texport let label = gradio.i18n(\"slider.slider\");\n\texport let info: string | undefined = undefined;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let minimum: number;\n\texport let maximum = 100;\n\texport let step: number;\n\texport let show_label: boolean;\n\texport let interactive: boolean;\n\texport let loading_status: LoadingStatus;\n\texport let value_is_output = false;\n\texport let show_reset_button: boolean;\n\n\tlet range_input: HTMLInputElement;\n\tlet number_input: HTMLInputElement;\n\n\tconst id = `range_id_${_id++}`;\n\n\tlet window_width: number;\n\n\t$: minimum_value = minimum ?? 0;\n\n\tfunction handle_change(): void {\n\t\tgradio.dispatch(\"change\");\n\t\tif (!value_is_output) {\n\t\t\tgradio.dispatch(\"input\");\n\t\t}\n\t}\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t\tset_slider();\n\t});\n\n\tfunction handle_release(e: MouseEvent): void {\n\t\tgradio.dispatch(\"release\", value);\n\t}\n\tfunction clamp(): void {\n\t\tgradio.dispatch(\"release\", value);\n\t\tvalue = Math.min(Math.max(value, minimum), maximum);\n\t}\n\n\tfunction set_slider(): void {\n\t\tset_slider_range();\n\t\trange_input.addEventListener(\"input\", set_slider_range);\n\t\tnumber_input.addEventListener(\"input\", set_slider_range);\n\t}\n\tfunction set_slider_range(): void {\n\t\tconst range = range_input;\n\t\tconst min = Number(range.min);\n\t\tconst max = Number(range.max);\n\t\tconst val = Number(range.value);\n\t\tconst percentage = ((val - min) / (max - min)) * 100;\n\t\trange.style.setProperty(\"--range_progress\", `${percentage}%`);\n\t}\n\n\t$: disabled = !interactive;\n\n\t// When the value changes, dispatch the change event via handle_change()\n\t// See the docs for an explanation: https://svelte.dev/docs/svelte-components#script-3-$-marks-a-statement-as-reactive\n\t$: value, handle_change();\n\n\tfunction handle_resize(): void {\n\t\twindow_width = window.innerWidth;\n\t}\n\n\tfunction reset_value(): void {\n\t\tvalue = initial_value;\n\t\tset_slider_range();\n\t\tgradio.dispatch(\"change\");\n\t\tgradio.dispatch(\"release\", value);\n\t}\n</script>\n\n<svelte:window on:resize={handle_resize} />\n\n<Block {visible} {elem_id} {elem_classes} {container} {scale} {min_width}>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\n\t<div class=\"wrap\">\n\t\t<div class=\"head\">\n\t\t\t<label for={id}>\n\t\t\t\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\n\t\t\t</label>\n\t\t\t<div class=\"tab-like-container\">\n\t\t\t\t<input\n\t\t\t\t\taria-label={`number input for ${label}`}\n\t\t\t\t\tdata-testid=\"number-input\"\n\t\t\t\t\ttype=\"number\"\n\t\t\t\t\tbind:value\n\t\t\t\t\tbind:this={number_input}\n\t\t\t\t\tmin={minimum}\n\t\t\t\t\tmax={maximum}\n\t\t\t\t\ton:blur={clamp}\n\t\t\t\t\t{step}\n\t\t\t\t\t{disabled}\n\t\t\t\t\ton:pointerup={handle_release}\n\t\t\t\t/>\n\t\t\t\t{#if show_reset_button}\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass=\"reset-button\"\n\t\t\t\t\t\ton:click={reset_value}\n\t\t\t\t\t\t{disabled}\n\t\t\t\t\t\taria-label=\"Reset to default value\"\n\t\t\t\t\t\tdata-testid=\"reset-button\"\n\t\t\t\t\t>\n\t\t\t\t\t\t↺\n\t\t\t\t\t</button>\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t</div>\n\n\t\t<div class=\"slider_input_container\">\n\t\t\t<span class=\"min_value\">{minimum_value}</span>\n\t\t\t<input\n\t\t\t\ttype=\"range\"\n\t\t\t\t{id}\n\t\t\t\tname=\"cowbell\"\n\t\t\t\tbind:value\n\t\t\t\tbind:this={range_input}\n\t\t\t\tmin={minimum}\n\t\t\t\tmax={maximum}\n\t\t\t\t{step}\n\t\t\t\t{disabled}\n\t\t\t\ton:pointerup={handle_release}\n\t\t\t\taria-label={`range slider for ${label}`}\n\t\t\t/>\n\t\t\t<span class=\"max_value\">{maximum}</span>\n\t\t</div>\n\t</div>\n</Block>\n\n<style>\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\twidth: 100%;\n\t}\n\n\t.head {\n\t\tmargin-bottom: var(--size-2);\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: flex-start;\n\t\tflex-wrap: wrap;\n\t\twidth: 100%;\n\t}\n\n\t.head > label {\n\t\tflex: 1;\n\t}\n\n\t.head > .tab-like-container {\n\t\tmargin-left: auto;\n\t\torder: 1;\n\t}\n\n\t.slider_input_container {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: var(--size-2);\n\t}\n\n\tinput[type=\"range\"] {\n\t\t-webkit-appearance: none;\n\t\tappearance: none;\n\t\twidth: 100%;\n\t\tcursor: pointer;\n\t\toutline: none;\n\t\tborder-radius: var(--radius-xl);\n\t\tmin-width: var(--size-28);\n\t\tbackground: transparent;\n\t}\n\n\t/* webkit track */\n\tinput[type=\"range\"]::-webkit-slider-runnable-track {\n\t\theight: var(--size-2);\n\t\tbackground: var(--neutral-200);\n\t\tborder-radius: var(--radius-xl);\n\t}\n\n\t/* webkit thumb */\n\tinput[type=\"range\"]::-webkit-slider-thumb {\n\t\t-webkit-appearance: none;\n\t\tappearance: none;\n\t\theight: var(--size-4);\n\t\twidth: var(--size-4);\n\t\tbackground-color: white;\n\t\tborder-radius: 50%;\n\t\tmargin-top: -5px;\n\t\tbox-shadow:\n\t\t\t0 0 0 1px rgba(247, 246, 246, 0.739),\n\t\t\t1px 1px 4px rgba(0, 0, 0, 0.1);\n\t}\n\n\tinput[type=\"range\"]::-webkit-slider-runnable-track {\n\t\tbackground: linear-gradient(\n\t\t\tto right,\n\t\t\tvar(--slider-color) var(--range_progress),\n\t\t\tvar(--neutral-200) var(--range_progress)\n\t\t);\n\t}\n\n\t/* firefox */\n\tinput[type=\"range\"]::-moz-range-track {\n\t\theight: var(--size-2);\n\t\tbackground: var(--neutral-200);\n\t\tborder-radius: var(--radius-xl);\n\t}\n\n\tinput[type=\"range\"]::-moz-range-thumb {\n\t\tappearance: none;\n\t\theight: var(--size-4);\n\t\twidth: var(--size-4);\n\t\tbackground-color: white;\n\t\tborder-radius: 50%;\n\t\tborder: none;\n\t\tmargin-top: calc(-1 * (var(--size-4) - var(--size-2)) / 2);\n\t\tbox-shadow:\n\t\t\t0 0 0 1px rgba(247, 246, 246, 0.739),\n\t\t\t1px 1px 4px rgba(0, 0, 0, 0.1);\n\t}\n\n\tinput[type=\"range\"]::-moz-range-progress {\n\t\theight: var(--size-2);\n\t\tbackground-color: var(--slider-color);\n\t\tborder-radius: var(--radius-xl);\n\t}\n\n\tinput[type=\"number\"] {\n\t\tdisplay: block;\n\t\toutline: none;\n\t\tborder: 1px solid var(--input-border-color);\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground: var(--input-background-fill);\n\t\tpadding: var(--size-2) var(--size-3);\n\t\theight: var(--size-8);\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-sm);\n\t\ttext-align: center;\n\t\tmin-width: var(--size-16);\n\t\ttransition: border-color 0.15s ease-in-out;\n\t}\n\n\tinput[type=\"number\"]:focus {\n\t\tbox-shadow: none;\n\t\tborder-width: 2px;\n\t}\n\n\tinput:disabled,\n\tinput[disabled] {\n\t\t-webkit-text-fill-color: var(--body-text-color);\n\t\topacity: 1;\n\t\tcursor: not-allowed;\n\t}\n\n\tinput::placeholder {\n\t\tcolor: var(--input-placeholder-color);\n\t}\n\n\tinput[type=\"range\"][disabled] {\n\t\topacity: 0.6;\n\t}\n\n\tinput[type=\"range\"][disabled]::-webkit-slider-thumb,\n\tinput[type=\"range\"][disabled]::-moz-range-thumb,\n\tinput[type=\"range\"][disabled]::-ms-thumb,\n\tinput[type=\"range\"][disabled]::-webkit-slider-thumb:hover,\n\tinput[type=\"range\"][disabled]::-moz-range-thumb:hover,\n\tinput[type=\"range\"][disabled]::-moz-range-track:hover {\n\t\tbackground-color: var(--body-text-color-subdued);\n\t\tcursor: not-allowed;\n\t}\n\n\t.min_value,\n\t.max_value {\n\t\tfont-size: var(--text-sm);\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.min_value {\n\t\tmargin-right: var(--size-0-5);\n\t}\n\n\t.max_value {\n\t\tmargin-left: var(--size-0-5);\n\t\tmargin-right: var(--size-0-5);\n\t}\n\n\t@media (max-width: 480px) {\n\t\t.min_value,\n\t\t.max_value {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n\n\t@media (max-width: 520px) {\n\t\t.head {\n\t\t\tgap: var(--size-3);\n\t\t}\n\t}\n\n\t@media (max-width: 420px) {\n\t\t.head .tab-like-container {\n\t\t\tmargin-bottom: var(--size-4);\n\t\t}\n\t}\n\n\t.tab-like-container {\n\t\tdisplay: flex;\n\t\talign-items: stretch;\n\t\tborder: 1px solid var(--input-border-color);\n\t\tborder-radius: var(--radius-sm);\n\t\toverflow: hidden;\n\t\theight: var(--size-6);\n\t}\n\n\tinput[type=\"number\"] {\n\t\tborder: none;\n\t\tborder-radius: 0;\n\t\tpadding: var(--size-1) var(--size-2);\n\t\theight: 100%;\n\t\tmin-width: var(--size-14);\n\t\tfont-size: var(--text-sm);\n\t}\n\n\tinput[type=\"number\"]:focus {\n\t\tbox-shadow: inset 0 0 0 1px var(--color-accent);\n\t\tborder-radius: 3px 0 0px 3px;\n\t}\n\n\t.reset-button {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground: none;\n\t\tborder: none;\n\t\tborder-left: 1px solid var(--input-border-color);\n\t\tcursor: pointer;\n\t\tfont-size: var(--text-sm);\n\t\tcolor: var(--body-text-color);\n\t\tpadding: 0 var(--size-2);\n\t\tmin-width: var(--size-6);\n\t\ttransition: background-color 0.15s ease-in-out;\n\t}\n\n\t.reset-button:hover:not(:disabled) {\n\t\tbackground-color: var(--background-fill-secondary);\n\t}\n\n\t.reset-button:disabled {\n\t\topacity: 0.5;\n\t\tcursor: not-allowed;\n\t}\n</style>\n"], "names": ["ctx", "insert_hydration", "target", "button", "anchor", "create_if_block", "div3", "append_hydration", "div1", "label_1", "div0", "input0", "div2", "span0", "input1", "span1", "dirty", "_id", "gradio", "$$props", "elem_id", "elem_classes", "visible", "value", "initial_value", "label", "info", "container", "scale", "min_width", "minimum", "maximum", "step", "show_label", "interactive", "loading_status", "value_is_output", "show_reset_button", "range_input", "number_input", "id", "handle_change", "afterUpdate", "set_slider", "handle_release", "e", "clamp", "$$invalidate", "set_slider_range", "range", "min", "max", "percentage", "handle_resize", "reset_value", "clear_status_handler", "$$value", "minimum_value", "disabled"], "mappings": "guBA8GqCA,EAAK,CAAA,CAAA,cAALA,EAAK,CAAA,CAAA,yCAALA,EAAK,CAAA,CAAA,0EAuBrC,GAEA,8FAFA,GAEA,8KARAC,EAQQC,EAAAC,EAAAC,CAAA,2BANGJ,EAAW,EAAA,CAAA,qJA5Bb,CAAA,WAAAA,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,EAAA,iMAuBXA,EAAiB,EAAA,GAAAK,GAAAL,CAAA,sKAeEA,EAAa,EAAA,CAAA,2CAcbA,EAAO,EAAA,CAAA,+bAdPA,EAAa,EAAA,CAAA,6JAcbA,EAAO,EAAA,CAAA,iEA9CpBA,EAAE,EAAA,CAAA,uEAKoBA,EAAK,CAAA,CAAA,EAAA,mEAKhCA,EAAO,EAAA,CAAA,YACPA,EAAO,EAAA,CAAA,mQA4BRA,EAAO,EAAA,CAAA,YACPA,EAAO,EAAA,CAAA,4EAIoBA,EAAK,CAAA,CAAA,EAAA,0LA9CxCC,EAkDKC,EAAAI,EAAAF,CAAA,EAjDJG,EA8BKD,EAAAE,CAAA,EA7BJD,EAEOC,EAAAC,CAAA,qBACPF,EAyBKC,EAAAE,CAAA,EAxBJH,EAYCG,EAAAC,CAAA,kDAeHJ,EAgBKD,EAAAM,CAAA,EAfJL,EAA6CK,EAAAC,CAAA,gBAC7CN,EAYCK,EAAAE,CAAA,4BACDP,EAAuCK,EAAAG,CAAA,mDAlC5Bf,EAAK,EAAA,CAAA,kBAGAA,EAAc,EAAA,CAAA,yDA4BfA,EAAc,EAAA,CAAA,0CAnDlBgB,EAAA,CAAA,EAAA,GAAA,CAAA,WAAAhB,KAAO,UAAU,EACvBgB,EAAA,CAAA,EAAA,GAAA,CAAA,KAAAhB,KAAO,IAAI,iBACbA,EAAc,EAAA,CAAA,mLAWiBA,EAAK,CAAA,CAAA,qDAKhCA,EAAO,EAAA,CAAA,6BACPA,EAAO,EAAA,CAAA,iHAMRA,EAAiB,EAAA,oFAeEA,EAAa,EAAA,CAAA,6BAOhCA,EAAO,EAAA,CAAA,6BACPA,EAAO,EAAA,CAAA,oHAIoBA,EAAK,CAAA,CAAA,kEAEbA,EAAO,EAAA,CAAA,ucA1DTA,EAAa,EAAA,CAAA,sVAhGlC,IAAAiB,GAAM,6BAUC,CAAA,OAAAC,CAAA,EAAAC,GAMA,QAAAC,EAAU,EAAA,EAAAD,EACV,CAAA,aAAAE,EAAA,EAAA,EAAAF,GACA,QAAAG,EAAU,EAAA,EAAAH,GACV,MAAAI,EAAQ,CAAA,EAAAJ,EACfK,EAAgBD,GAET,MAAAE,EAAQP,EAAO,KAAK,eAAe,CAAA,EAAAC,GACnC,KAAAO,EAA2B,MAAA,EAAAP,GAC3B,UAAAQ,EAAY,EAAA,EAAAR,GACZ,MAAAS,EAAuB,IAAA,EAAAT,GACvB,UAAAU,EAAgC,MAAA,EAAAV,EAChC,CAAA,QAAAW,CAAA,EAAAX,GACA,QAAAY,EAAU,GAAA,EAAAZ,EACV,CAAA,KAAAa,CAAA,EAAAb,EACA,CAAA,WAAAc,CAAA,EAAAd,EACA,CAAA,YAAAe,CAAA,EAAAf,EACA,CAAA,eAAAgB,CAAA,EAAAhB,GACA,gBAAAiB,EAAkB,EAAA,EAAAjB,EAClB,CAAA,kBAAAkB,CAAA,EAAAlB,EAEPmB,EACAC,QAEEC,EAAiB,YAAAvB,IAAA,GAMd,SAAAwB,GAAA,CACRvB,EAAO,SAAS,QAAQ,EACnBkB,GACJlB,EAAO,SAAS,OAAO,EAGzBwB,GAAA,IAAA,MACCN,EAAkB,EAAA,EAClBO,eAGQC,EAAeC,EAAA,CACvB3B,EAAO,SAAS,UAAWK,CAAK,EAExB,SAAAuB,GAAA,CACR5B,EAAO,SAAS,UAAWK,CAAK,EAChCwB,EAAA,EAAAxB,EAAQ,KAAK,IAAI,KAAK,IAAIA,EAAOO,CAAO,EAAGC,CAAO,CAAA,EAG1C,SAAAY,GAAA,CACRK,IACAV,EAAY,iBAAiB,QAASU,CAAgB,EACtDT,EAAa,iBAAiB,QAASS,CAAgB,EAE/C,SAAAA,GAAA,OACFC,EAAQX,EACRY,GAAM,OAAOD,EAAM,GAAG,EACtBE,GAAM,OAAOF,EAAM,GAAG,EAEtBG,IADM,OAAOH,EAAM,KAAK,EACHC,KAAQC,GAAMD,IAAQ,IACjDD,EAAM,MAAM,YAAY,mBAAA,GAAuBG,EAAU,GAAA,EASjD,SAAAC,GAAA,EAIA,SAAAC,GAAA,KACR/B,EAAQC,CAAA,EACRwB,IACA9B,EAAO,SAAS,QAAQ,EACxBA,EAAO,SAAS,UAAWK,CAAK,EAWT,MAAAgC,EAAA,IAAArC,EAAO,SAAS,eAAgBiB,CAAc,kFAcvDI,EAAYiB,6FA6BblB,EAAWkB,qsBAvGzBT,EAAA,GAAGU,EAAgB3B,GAAW,CAAA,0BAmC9BiB,EAAA,GAAGW,EAAY,CAAAxB,CAAA,mBAILO,EAAA"}