{"version": 3, "file": "flowGraphSetVariableBlock-CAgoXY6t.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/flowGraphSetVariableBlock.js"], "sourcesContent": ["import { RegisterClass } from \"../../../Misc/typeStore.js\";\nimport { FlowGraphExecutionBlockWithOutSignal } from \"../../flowGraphExecutionBlockWithOutSignal.js\";\nimport { RichTypeAny } from \"../../flowGraphRichTypes.js\";\n/**\n * This block will set a variable on the context.\n */\nexport class FlowGraphSetVariableBlock extends FlowGraphExecutionBlockWithOutSignal {\n    constructor(config) {\n        super(config);\n        // check if the variable is defined\n        if (!config.variable && !config.variables) {\n            throw new Error(\"FlowGraphSetVariableBlock: variable/variables is not defined\");\n        }\n        // check if the variable is an array\n        if (config.variables && config.variable) {\n            throw new Error(\"FlowGraphSetVariableBlock: variable and variables are both defined\");\n        }\n        // check if we have either a variable or variables. If we have variables, set the inputs correctly\n        if (config.variables) {\n            for (const variable of config.variables) {\n                this.registerDataInput(variable, RichTypeAny);\n            }\n        }\n        else {\n            this.registerDataInput(\"value\", RichTypeAny);\n        }\n    }\n    _execute(context, _callingSignal) {\n        if (this.config?.variables) {\n            for (const variable of this.config.variables) {\n                this._saveVariable(context, variable);\n            }\n        }\n        else {\n            this._saveVariable(context, this.config?.variable, \"value\");\n        }\n        this.out._activateSignal(context);\n    }\n    _saveVariable(context, variableName, inputName) {\n        // check if there is an animation(group) running on this variable. If there is, stop the animation - a value was force-set.\n        const currentlyRunningAnimationGroups = context._getGlobalContextVariable(\"currentlyRunningAnimationGroups\", []);\n        for (const animationUniqueId of currentlyRunningAnimationGroups) {\n            const animation = context.assetsContext.animationGroups[animationUniqueId];\n            // check if there is a target animation that has the target set to be the context\n            for (const targetAnimation of animation.targetedAnimations) {\n                if (targetAnimation.target === context) {\n                    // check if the target property is the variable we are setting\n                    if (targetAnimation.target === context) {\n                        // check the variable name\n                        if (targetAnimation.animation.targetProperty === variableName) {\n                            // stop the animation\n                            animation.stop();\n                            // remove the animation from the currently running animations\n                            const index = currentlyRunningAnimationGroups.indexOf(animationUniqueId);\n                            if (index > -1) {\n                                currentlyRunningAnimationGroups.splice(index, 1);\n                            }\n                            context._setGlobalContextVariable(\"currentlyRunningAnimationGroups\", currentlyRunningAnimationGroups);\n                            break;\n                        }\n                    }\n                }\n            }\n        }\n        const value = this.getDataInput(inputName || variableName)?.getValue(context);\n        context.setVariable(variableName, value);\n    }\n    getClassName() {\n        return \"FlowGraphSetVariableBlock\" /* FlowGraphBlockNames.SetVariable */;\n    }\n    serialize(serializationObject) {\n        super.serialize(serializationObject);\n        serializationObject.config.variable = this.config?.variable;\n    }\n}\nRegisterClass(\"FlowGraphSetVariableBlock\" /* FlowGraphBlockNames.SetVariable */, FlowGraphSetVariableBlock);\n//# sourceMappingURL=flowGraphSetVariableBlock.js.map"], "names": ["FlowGraphSetVariableBlock", "FlowGraphExecutionBlockWithOutSignal", "config", "variable", "RichTypeAny", "context", "_callingSignal", "variableName", "inputName", "currentlyRunningAnimationGroups", "animationUniqueId", "animation", "targetAnimation", "index", "value", "serializationObject", "RegisterClass"], "mappings": "gPAMO,MAAMA,UAAkCC,CAAqC,CAChF,YAAYC,EAAQ,CAGhB,GAFA,MAAMA,CAAM,EAER,CAACA,EAAO,UAAY,CAACA,EAAO,UAC5B,MAAM,IAAI,MAAM,8DAA8D,EAGlF,GAAIA,EAAO,WAAaA,EAAO,SAC3B,MAAM,IAAI,MAAM,oEAAoE,EAGxF,GAAIA,EAAO,UACP,UAAWC,KAAYD,EAAO,UAC1B,KAAK,kBAAkBC,EAAUC,CAAW,OAIhD,KAAK,kBAAkB,QAASA,CAAW,CAElD,CACD,SAASC,EAASC,EAAgB,CAC9B,GAAI,KAAK,QAAQ,UACb,UAAWH,KAAY,KAAK,OAAO,UAC/B,KAAK,cAAcE,EAASF,CAAQ,OAIxC,KAAK,cAAcE,EAAS,KAAK,QAAQ,SAAU,OAAO,EAE9D,KAAK,IAAI,gBAAgBA,CAAO,CACnC,CACD,cAAcA,EAASE,EAAcC,EAAW,CAE5C,MAAMC,EAAkCJ,EAAQ,0BAA0B,kCAAmC,CAAE,CAAA,EAC/G,UAAWK,KAAqBD,EAAiC,CAC7D,MAAME,EAAYN,EAAQ,cAAc,gBAAgBK,CAAiB,EAEzE,UAAWE,KAAmBD,EAAU,mBACpC,GAAIC,EAAgB,SAAWP,GAEvBO,EAAgB,SAAWP,GAEvBO,EAAgB,UAAU,iBAAmBL,EAAc,CAE3DI,EAAU,KAAI,EAEd,MAAME,EAAQJ,EAAgC,QAAQC,CAAiB,EACnEG,EAAQ,IACRJ,EAAgC,OAAOI,EAAO,CAAC,EAEnDR,EAAQ,0BAA0B,kCAAmCI,CAA+B,EACpG,KACH,CAIhB,CACD,MAAMK,EAAQ,KAAK,aAAaN,GAAaD,CAAY,GAAG,SAASF,CAAO,EAC5EA,EAAQ,YAAYE,EAAcO,CAAK,CAC1C,CACD,cAAe,CACX,MAAO,2BACV,CACD,UAAUC,EAAqB,CAC3B,MAAM,UAAUA,CAAmB,EACnCA,EAAoB,OAAO,SAAW,KAAK,QAAQ,QACtD,CACL,CACAC,EAAc,4BAAmEhB,CAAyB", "x_google_ignoreList": [0]}