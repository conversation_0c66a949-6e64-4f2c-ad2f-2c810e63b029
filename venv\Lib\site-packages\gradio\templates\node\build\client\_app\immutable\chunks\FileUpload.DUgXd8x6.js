import{SvelteComponent as ue,init as se,safe_not_equal as _e,ensure_array_like as me,element as O,claim_element as R,children as q,detach as v,attr as F,set_style as be,insert_hydration as y,append_hydration as D,group_outros as ee,update_keyed_each as ye,outro_and_destroy_block as Ne,check_outros as le,transition_in as b,transition_out as w,createEventDispatcher as Te,space as G,text as ne,claim_space as K,claim_text as te,toggle_class as $,listen as j,prevent_default as Ue,set_data as fe,run_all as Be,bubble as J,get_svelte_dataset as Fe,noop as pe,create_component as N,claim_component as U,mount_component as S,destroy_component as L,HtmlTagHydration as Se,claim_html_tag as Le,empty as ae,tick as Oe,binding_callbacks as re,bind as ie,add_flush_callback as oe,create_slot as Re,update_slot_base as Ye,get_all_dirty_from_scope as He,get_slot_changes as Me}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{I as Ce,C as qe}from"./2.B2AoQPnG.js";import{B as De}from"./BlockLabel.BTSz9r5s.js";import{E as Ge}from"./Empty.DwQ6nkN6.js";import{F as ge}from"./File.Dl9hvYLG.js";import{a as Ie,U as Ke}from"./Upload.yOHVlgUe.js";import{I as Ve}from"./IconButtonWrapper.D5aGR59h.js";import{D as We}from"./DownloadLink.D1g3Q1HV.js";const ke=r=>{let e=["B","KB","MB","GB","PB"],l=0;for(;r>1024;)r/=1024,l++;let n=e[l];return r.toFixed(1)+"&nbsp;"+n};function we(r,e,l){const n=r.slice();return n[25]=e[l],n[27]=l,n}function ve(r){let e,l="⋮⋮";return{c(){e=O("span"),e.textContent=l,this.h()},l(n){e=R(n,"SPAN",{class:!0,"data-svelte-h":!0}),Fe(e)!=="svelte-1u4up0a"&&(e.textContent=l),this.h()},h(){F(e,"class","drag-handle svelte-1rvzbk6")},m(n,t){y(n,e,t)},d(n){n&&v(e)}}}function je(r){let e=r[2]("file.uploading")+"",l;return{c(){l=ne(e)},l(n){l=te(n,e)},m(n,t){y(n,l,t)},p(n,t){t&4&&e!==(e=n[2]("file.uploading")+"")&&fe(l,e)},i:pe,o:pe,d(n){n&&v(l)}}}function Je(r){let e,l;function n(){return r[17](r[25])}return e=new We({props:{href:r[25].url,download:r[14]&&window.__is_colab__?null:r[25].orig_name,$$slots:{default:[Qe]},$$scope:{ctx:r}}}),e.$on("click",n),{c(){N(e.$$.fragment)},l(t){U(e.$$.fragment,t)},m(t,a){S(e,t,a),l=!0},p(t,a){r=t;const u={};a&64&&(u.href=r[25].url),a&64&&(u.download=r[14]&&window.__is_colab__?null:r[25].orig_name),a&268435520&&(u.$$scope={dirty:a,ctx:r}),e.$set(u)},i(t){l||(b(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){L(e,t)}}}function Qe(r){let e,l=(r[25].size!=null?ke(r[25].size):"(size unknown)")+"",n;return{c(){e=new Se(!1),n=ne(" ⇣"),this.h()},l(t){e=Le(t,!1),n=te(t," ⇣"),this.h()},h(){e.a=n},m(t,a){e.m(l,t,a),y(t,n,a)},p(t,a){a&64&&l!==(l=(t[25].size!=null?ke(t[25].size):"(size unknown)")+"")&&e.p(l)},d(t){t&&(e.d(),v(n))}}}function ze(r){let e,l,n="×",t,a;function u(){return r[18](r[27])}function c(...o){return r[19](r[27],...o)}return{c(){e=O("td"),l=O("button"),l.textContent=n,this.h()},l(o){e=R(o,"TD",{class:!0});var f=q(e);l=R(f,"BUTTON",{class:!0,"aria-label":!0,"data-svelte-h":!0}),Fe(l)!=="svelte-nhtord"&&(l.textContent=n),f.forEach(v),this.h()},h(){F(l,"class","label-clear-button svelte-1rvzbk6"),F(l,"aria-label","Remove this file"),F(e,"class","svelte-1rvzbk6")},m(o,f){y(o,e,f),D(e,l),t||(a=[j(l,"click",u),j(l,"keydown",c)],t=!0)},p(o,f){r=o},d(o){o&&v(e),t=!1,Be(a)}}}function Ae(r,e){let l,n,t,a,u=e[25].filename_stem+"",c,o,f,g=e[25].filename_ext+"",i,d,m,z,E,T,C,Y,V,M,p,I,Q,A=e[3]&&e[6].length>1&&ve();const X=[Je,je],P=[];function Z(s,k){return s[25].url?0:1}E=Z(e),T=P[E]=X[E](e);let _=e[6].length>1&&ze(e);function h(...s){return e[20](e[27],...s)}function B(...s){return e[21](e[27],...s)}function W(...s){return e[22](e[27],...s)}function x(...s){return e[23](e[27],...s)}return{key:r,first:null,c(){l=O("tr"),n=O("td"),A&&A.c(),t=G(),a=O("span"),c=ne(u),o=G(),f=O("span"),i=ne(g),m=G(),z=O("td"),T.c(),C=G(),_&&_.c(),Y=G(),this.h()},l(s){l=R(s,"TR",{class:!0,"data-drop-target":!0,draggable:!0});var k=q(l);n=R(k,"TD",{class:!0,"aria-label":!0});var H=q(n);A&&A.l(H),t=K(H),a=R(H,"SPAN",{class:!0});var ce=q(a);c=te(ce,u),ce.forEach(v),o=K(H),f=R(H,"SPAN",{class:!0});var de=q(f);i=te(de,g),de.forEach(v),H.forEach(v),m=K(k),z=R(k,"TD",{class:!0});var he=q(z);T.l(he),he.forEach(v),C=K(k),_&&_.l(k),Y=K(k),k.forEach(v),this.h()},h(){F(a,"class","stem svelte-1rvzbk6"),F(f,"class","ext svelte-1rvzbk6"),F(n,"class","filename svelte-1rvzbk6"),F(n,"aria-label",d=e[25].orig_name),F(z,"class","download svelte-1rvzbk6"),F(l,"class","file svelte-1rvzbk6"),F(l,"data-drop-target",V=e[5]===e[6].length&&e[27]===e[6].length-1||e[5]===e[27]+1?"after":"before"),F(l,"draggable",M=e[3]&&e[6].length>1),$(l,"selectable",e[0]),$(l,"dragging",e[4]===e[27]),$(l,"drop-target",e[5]===e[27]||e[27]===e[6].length-1&&e[5]===e[6].length),this.first=l},m(s,k){y(s,l,k),D(l,n),A&&A.m(n,null),D(n,t),D(n,a),D(a,c),D(n,o),D(n,f),D(f,i),D(l,m),D(l,z),P[E].m(z,null),D(l,C),_&&_.m(l,null),D(l,Y),p=!0,I||(Q=[j(l,"click",h),j(l,"dragstart",B),j(l,"dragenter",Ue(e[16])),j(l,"dragover",W),j(l,"drop",x),j(l,"dragend",e[9])],I=!0)},p(s,k){e=s,e[3]&&e[6].length>1?A||(A=ve(),A.c(),A.m(n,t)):A&&(A.d(1),A=null),(!p||k&64)&&u!==(u=e[25].filename_stem+"")&&fe(c,u),(!p||k&64)&&g!==(g=e[25].filename_ext+"")&&fe(i,g),(!p||k&64&&d!==(d=e[25].orig_name))&&F(n,"aria-label",d);let H=E;E=Z(e),E===H?P[E].p(e,k):(ee(),w(P[H],1,1,()=>{P[H]=null}),le(),T=P[E],T?T.p(e,k):(T=P[E]=X[E](e),T.c()),b(T,1),T.m(z,null)),e[6].length>1?_?_.p(e,k):(_=ze(e),_.c(),_.m(l,Y)):_&&(_.d(1),_=null),(!p||k&96&&V!==(V=e[5]===e[6].length&&e[27]===e[6].length-1||e[5]===e[27]+1?"after":"before"))&&F(l,"data-drop-target",V),(!p||k&72&&M!==(M=e[3]&&e[6].length>1))&&F(l,"draggable",M),(!p||k&1)&&$(l,"selectable",e[0]),(!p||k&80)&&$(l,"dragging",e[4]===e[27]),(!p||k&96)&&$(l,"drop-target",e[5]===e[27]||e[27]===e[6].length-1&&e[5]===e[6].length)},i(s){p||(b(T),p=!0)},o(s){w(T),p=!1},d(s){s&&v(l),A&&A.d(),P[E].d(),_&&_.d(),I=!1,Be(Q)}}}function Xe(r){let e,l,n,t=[],a=new Map,u,c=me(r[6]);const o=f=>f[25].url;for(let f=0;f<c.length;f+=1){let g=we(r,c,f),i=o(g);a.set(i,t[f]=Ae(i,g))}return{c(){e=O("div"),l=O("table"),n=O("tbody");for(let f=0;f<t.length;f+=1)t[f].c();this.h()},l(f){e=R(f,"DIV",{class:!0});var g=q(e);l=R(g,"TABLE",{class:!0});var i=q(l);n=R(i,"TBODY",{class:!0});var d=q(n);for(let m=0;m<t.length;m+=1)t[m].l(d);d.forEach(v),i.forEach(v),g.forEach(v),this.h()},h(){F(n,"class","svelte-1rvzbk6"),F(l,"class","file-preview svelte-1rvzbk6"),F(e,"class","file-preview-holder svelte-1rvzbk6"),be(e,"max-height",r[1]?typeof r[1]=="number"?r[1]+"px":r[1]:"auto")},m(f,g){y(f,e,g),D(e,l),D(l,n);for(let i=0;i<t.length;i+=1)t[i]&&t[i].m(n,null);u=!0},p(f,[g]){g&32765&&(c=me(f[6]),ee(),t=ye(t,g,o,1,f,c,a,n,Ne,Ae,null,we),le()),g&2&&be(e,"max-height",f[1]?typeof f[1]=="number"?f[1]+"px":f[1]:"auto")},i(f){if(!u){for(let g=0;g<c.length;g+=1)b(t[g]);u=!0}},o(f){for(let g=0;g<t.length;g+=1)w(t[g]);u=!1},d(f){f&&v(e);for(let g=0;g<t.length;g+=1)t[g].d()}}}function Ze(r){const e=r.lastIndexOf(".");return e===-1?[r,""]:[r.slice(0,e),r.slice(e)]}function $e(r,e,l){let n;const t=Te();let{value:a}=e,{selectable:u=!1}=e,{height:c=void 0}=e,{i18n:o}=e,{allow_reordering:f=!1}=e,g=null,i=null;function d(_,h){l(4,g=h),_.dataTransfer&&(_.dataTransfer.effectAllowed="move",_.dataTransfer.setData("text/plain",h.toString()))}function m(_,h){if(_.preventDefault(),h===n.length-1){const B=_.currentTarget.getBoundingClientRect(),W=B.top+B.height/2;l(5,i=_.clientY>W?n.length:h)}else l(5,i=h);_.dataTransfer&&(_.dataTransfer.dropEffect="move")}function z(_){var h;(!((h=_.dataTransfer)!=null&&h.dropEffect)||_.dataTransfer.dropEffect==="none")&&(l(4,g=null),l(5,i=null))}function E(_,h){if(_.preventDefault(),g===null||g===h)return;const B=Array.isArray(a)?[...a]:[a],[W]=B.splice(g,1);B.splice(i===n.length?n.length:h,0,W);const x=Array.isArray(a)?B:B[0];t("change",x),l(4,g=null),l(5,i=null)}function T(_,h){const B=_.currentTarget;(_.target===B||B&&B.firstElementChild&&_.composedPath().includes(B.firstElementChild))&&t("select",{value:n[h].orig_name,index:h})}function C(_){const h=n.splice(_,1);l(6,n=[...n]),l(15,a=n),t("delete",h[0]),t("change",n)}function Y(_){t("download",_)}const V=typeof window<"u";function M(_){J.call(this,r,_)}const p=_=>Y(_),I=_=>{C(_)},Q=(_,h)=>{h.key==="Enter"&&C(_)},A=(_,h)=>{T(h,_)},X=(_,h)=>d(h,_),P=(_,h)=>m(h,_),Z=(_,h)=>E(h,_);return r.$$set=_=>{"value"in _&&l(15,a=_.value),"selectable"in _&&l(0,u=_.selectable),"height"in _&&l(1,c=_.height),"i18n"in _&&l(2,o=_.i18n),"allow_reordering"in _&&l(3,f=_.allow_reordering)},r.$$.update=()=>{r.$$.dirty&32768&&l(6,n=(Array.isArray(a)?a:[a]).map(_=>{const[h,B]=Ze(_.orig_name??"");return{..._,filename_stem:h,filename_ext:B}}))},[u,c,o,f,g,i,n,d,m,z,E,T,C,Y,V,a,M,p,I,Q,A,X,P,Z]}class xe extends ue{constructor(e){super(),se(this,e,$e,Xe,_e,{value:15,selectable:0,height:1,i18n:2,allow_reordering:3})}}const Pe=xe;function el(r){let e,l;return e=new Ge({props:{unpadded_box:!0,size:"large",$$slots:{default:[nl]},$$scope:{ctx:r}}}),{c(){N(e.$$.fragment)},l(n){U(e.$$.fragment,n)},m(n,t){S(e,n,t),l=!0},p(n,t){const a={};t&256&&(a.$$scope={dirty:t,ctx:n}),e.$set(a)},i(n){l||(b(e.$$.fragment,n),l=!0)},o(n){w(e.$$.fragment,n),l=!1},d(n){L(e,n)}}}function ll(r){let e,l;return e=new Pe({props:{i18n:r[5],selectable:r[3],value:r[0],height:r[4]}}),e.$on("select",r[6]),e.$on("download",r[7]),{c(){N(e.$$.fragment)},l(n){U(e.$$.fragment,n)},m(n,t){S(e,n,t),l=!0},p(n,t){const a={};t&32&&(a.i18n=n[5]),t&8&&(a.selectable=n[3]),t&1&&(a.value=n[0]),t&16&&(a.height=n[4]),e.$set(a)},i(n){l||(b(e.$$.fragment,n),l=!0)},o(n){w(e.$$.fragment,n),l=!1},d(n){L(e,n)}}}function nl(r){let e,l;return e=new ge({}),{c(){N(e.$$.fragment)},l(n){U(e.$$.fragment,n)},m(n,t){S(e,n,t),l=!0},i(n){l||(b(e.$$.fragment,n),l=!0)},o(n){w(e.$$.fragment,n),l=!1},d(n){L(e,n)}}}function tl(r){let e,l,n,t,a,u,c;e=new De({props:{show_label:r[2],float:r[0]===null,Icon:ge,label:r[1]||"File"}});const o=[ll,el],f=[];function g(i,d){return d&1&&(n=null),n==null&&(n=!!(i[0]&&(!Array.isArray(i[0])||i[0].length>0))),n?0:1}return t=g(r,-1),a=f[t]=o[t](r),{c(){N(e.$$.fragment),l=G(),a.c(),u=ae()},l(i){U(e.$$.fragment,i),l=K(i),a.l(i),u=ae()},m(i,d){S(e,i,d),y(i,l,d),f[t].m(i,d),y(i,u,d),c=!0},p(i,[d]){const m={};d&4&&(m.show_label=i[2]),d&1&&(m.float=i[0]===null),d&2&&(m.label=i[1]||"File"),e.$set(m);let z=t;t=g(i,d),t===z?f[t].p(i,d):(ee(),w(f[z],1,1,()=>{f[z]=null}),le(),a=f[t],a?a.p(i,d):(a=f[t]=o[t](i),a.c()),b(a,1),a.m(u.parentNode,u))},i(i){c||(b(e.$$.fragment,i),b(a),c=!0)},o(i){w(e.$$.fragment,i),w(a),c=!1},d(i){i&&(v(l),v(u)),L(e,i),f[t].d(i)}}}function al(r,e,l){let{value:n=null}=e,{label:t}=e,{show_label:a=!0}=e,{selectable:u=!1}=e,{height:c=void 0}=e,{i18n:o}=e;function f(i){J.call(this,r,i)}function g(i){J.call(this,r,i)}return r.$$set=i=>{"value"in i&&l(0,n=i.value),"label"in i&&l(1,t=i.label),"show_label"in i&&l(2,a=i.show_label),"selectable"in i&&l(3,u=i.selectable),"height"in i&&l(4,c=i.height),"i18n"in i&&l(5,o=i.i18n)},[n,t,a,u,c,o,f,g]}class rl extends ue{constructor(e){super(),se(this,e,al,tl,_e,{value:0,label:1,show_label:2,selectable:3,height:4,i18n:5})}}const Al=rl;function il(r){let e,l,n,t;function a(o){r[26](o)}function u(o){r[27](o)}let c={filetype:r[5],file_count:r[4],max_file_size:r[10],root:r[7],stream_handler:r[12],upload:r[11],height:r[8],$$slots:{default:[fl]},$$scope:{ctx:r}};return r[14]!==void 0&&(c.dragging=r[14]),r[1]!==void 0&&(c.uploading=r[1]),e=new Ie({props:c}),re.push(()=>ie(e,"dragging",a)),re.push(()=>ie(e,"uploading",u)),e.$on("load",r[15]),e.$on("error",r[28]),{c(){N(e.$$.fragment)},l(o){U(e.$$.fragment,o)},m(o,f){S(e,o,f),t=!0},p(o,f){const g={};f&32&&(g.filetype=o[5]),f&16&&(g.file_count=o[4]),f&1024&&(g.max_file_size=o[10]),f&128&&(g.root=o[7]),f&4096&&(g.stream_handler=o[12]),f&2048&&(g.upload=o[11]),f&256&&(g.height=o[8]),f&536870912&&(g.$$scope={dirty:f,ctx:o}),!l&&f&16384&&(l=!0,g.dragging=o[14],oe(()=>l=!1)),!n&&f&2&&(n=!0,g.uploading=o[1],oe(()=>n=!1)),e.$set(g)},i(o){t||(b(e.$$.fragment,o),t=!0)},o(o){w(e.$$.fragment,o),t=!1},d(o){L(e,o)}}}function ol(r){let e,l,n,t;return e=new Ve({props:{$$slots:{default:[sl]},$$scope:{ctx:r}}}),n=new Pe({props:{i18n:r[9],selectable:r[6],value:r[0],height:r[8],allow_reordering:r[13]}}),n.$on("select",r[23]),n.$on("change",r[24]),n.$on("delete",r[25]),{c(){N(e.$$.fragment),l=G(),N(n.$$.fragment)},l(a){U(e.$$.fragment,a),l=K(a),U(n.$$.fragment,a)},m(a,u){S(e,a,u),y(a,l,u),S(n,a,u),t=!0},p(a,u){const c={};u&536895155&&(c.$$scope={dirty:u,ctx:a}),e.$set(c);const o={};u&512&&(o.i18n=a[9]),u&64&&(o.selectable=a[6]),u&1&&(o.value=a[0]),u&256&&(o.height=a[8]),u&8192&&(o.allow_reordering=a[13]),n.$set(o)},i(a){t||(b(e.$$.fragment,a),b(n.$$.fragment,a),t=!0)},o(a){w(e.$$.fragment,a),w(n.$$.fragment,a),t=!1},d(a){a&&v(l),L(e,a),L(n,a)}}}function fl(r){let e;const l=r[18].default,n=Re(l,r,r[29],null);return{c(){n&&n.c()},l(t){n&&n.l(t)},m(t,a){n&&n.m(t,a),e=!0},p(t,a){n&&n.p&&(!e||a&536870912)&&Ye(n,l,t,t[29],e?Me(l,t[29],a,null):He(t[29]),null)},i(t){e||(b(n,t),e=!0)},o(t){w(n,t),e=!1},d(t){n&&n.d(t)}}}function Ee(r){let e,l;return e=new Ce({props:{Icon:Ke,label:r[9]("common.upload"),$$slots:{default:[ul]},$$scope:{ctx:r}}}),{c(){N(e.$$.fragment)},l(n){U(e.$$.fragment,n)},m(n,t){S(e,n,t),l=!0},p(n,t){const a={};t&512&&(a.label=n[9]("common.upload")),t&536894642&&(a.$$scope={dirty:t,ctx:n}),e.$set(a)},i(n){l||(b(e.$$.fragment,n),l=!0)},o(n){w(e.$$.fragment,n),l=!1},d(n){L(e,n)}}}function ul(r){let e,l,n,t;function a(o){r[19](o)}function u(o){r[20](o)}let c={icon_upload:!0,filetype:r[5],file_count:r[4],max_file_size:r[10],root:r[7],stream_handler:r[12],upload:r[11]};return r[14]!==void 0&&(c.dragging=r[14]),r[1]!==void 0&&(c.uploading=r[1]),e=new Ie({props:c}),re.push(()=>ie(e,"dragging",a)),re.push(()=>ie(e,"uploading",u)),e.$on("load",r[15]),e.$on("error",r[21]),{c(){N(e.$$.fragment)},l(o){U(e.$$.fragment,o)},m(o,f){S(e,o,f),t=!0},p(o,f){const g={};f&32&&(g.filetype=o[5]),f&16&&(g.file_count=o[4]),f&1024&&(g.max_file_size=o[10]),f&128&&(g.root=o[7]),f&4096&&(g.stream_handler=o[12]),f&2048&&(g.upload=o[11]),!l&&f&16384&&(l=!0,g.dragging=o[14],oe(()=>l=!1)),!n&&f&2&&(n=!0,g.uploading=o[1],oe(()=>n=!1)),e.$set(g)},i(o){t||(b(e.$$.fragment,o),t=!0)},o(o){w(e.$$.fragment,o),t=!1},d(o){L(e,o)}}}function sl(r){let e=!(r[4]==="single"&&(Array.isArray(r[0])?r[0].length>0:r[0]!==null)),l,n,t,a=e&&Ee(r);return n=new Ce({props:{Icon:qe,label:r[9]("common.clear")}}),n.$on("click",r[22]),{c(){a&&a.c(),l=G(),N(n.$$.fragment)},l(u){a&&a.l(u),l=K(u),U(n.$$.fragment,u)},m(u,c){a&&a.m(u,c),y(u,l,c),S(n,u,c),t=!0},p(u,c){c&17&&(e=!(u[4]==="single"&&(Array.isArray(u[0])?u[0].length>0:u[0]!==null))),e?a?(a.p(u,c),c&17&&b(a,1)):(a=Ee(u),a.c(),b(a,1),a.m(l.parentNode,l)):a&&(ee(),w(a,1,1,()=>{a=null}),le());const o={};c&512&&(o.label=u[9]("common.clear")),n.$set(o)},i(u){t||(b(a),b(n.$$.fragment,u),t=!0)},o(u){w(a),w(n.$$.fragment,u),t=!1},d(u){u&&v(l),a&&a.d(u),L(n,u)}}}function _l(r){let e,l,n,t,a,u,c;e=new De({props:{show_label:r[3],Icon:ge,float:!r[0],label:r[2]||"File"}});const o=[ol,il],f=[];function g(i,d){return d&1&&(n=null),n==null&&(n=!!(i[0]&&(!Array.isArray(i[0])||i[0].length>0))),n?0:1}return t=g(r,-1),a=f[t]=o[t](r),{c(){N(e.$$.fragment),l=G(),a.c(),u=ae()},l(i){U(e.$$.fragment,i),l=K(i),a.l(i),u=ae()},m(i,d){S(e,i,d),y(i,l,d),f[t].m(i,d),y(i,u,d),c=!0},p(i,[d]){const m={};d&8&&(m.show_label=i[3]),d&1&&(m.float=!i[0]),d&4&&(m.label=i[2]||"File"),e.$set(m);let z=t;t=g(i,d),t===z?f[t].p(i,d):(ee(),w(f[z],1,1,()=>{f[z]=null}),le(),a=f[t],a?a.p(i,d):(a=f[t]=o[t](i),a.c()),b(a,1),a.m(u.parentNode,u))},i(i){c||(b(e.$$.fragment,i),b(a),c=!0)},o(i){w(e.$$.fragment,i),w(a),c=!1},d(i){i&&(v(l),v(u)),L(e,i),f[t].d(i)}}}function gl(r,e,l){let{$$slots:n={},$$scope:t}=e,{value:a}=e,{label:u}=e,{show_label:c=!0}=e,{file_count:o="single"}=e,{file_types:f=null}=e,{selectable:g=!1}=e,{root:i}=e,{height:d=void 0}=e,{i18n:m}=e,{max_file_size:z=null}=e,{upload:E}=e,{stream_handler:T}=e,{uploading:C=!1}=e,{allow_reordering:Y=!1}=e;async function V({detail:s}){Array.isArray(a)?l(0,a=[...a,...Array.isArray(s)?s:[s]]):a?l(0,a=[a,...Array.isArray(s)?s:[s]]):l(0,a=s),await Oe(),p("change",a),p("upload",s)}function M(){l(0,a=null),p("change",null),p("clear")}const p=Te();let I=!1;function Q(s){I=s,l(14,I)}function A(s){C=s,l(1,C)}function X(s){J.call(this,r,s)}const P=s=>{p("clear"),s.stopPropagation(),M()};function Z(s){J.call(this,r,s)}function _(s){J.call(this,r,s)}function h(s){J.call(this,r,s)}function B(s){I=s,l(14,I)}function W(s){C=s,l(1,C)}function x(s){J.call(this,r,s)}return r.$$set=s=>{"value"in s&&l(0,a=s.value),"label"in s&&l(2,u=s.label),"show_label"in s&&l(3,c=s.show_label),"file_count"in s&&l(4,o=s.file_count),"file_types"in s&&l(5,f=s.file_types),"selectable"in s&&l(6,g=s.selectable),"root"in s&&l(7,i=s.root),"height"in s&&l(8,d=s.height),"i18n"in s&&l(9,m=s.i18n),"max_file_size"in s&&l(10,z=s.max_file_size),"upload"in s&&l(11,E=s.upload),"stream_handler"in s&&l(12,T=s.stream_handler),"uploading"in s&&l(1,C=s.uploading),"allow_reordering"in s&&l(13,Y=s.allow_reordering),"$$scope"in s&&l(29,t=s.$$scope)},r.$$.update=()=>{r.$$.dirty&16384&&p("drag",I)},[a,C,u,c,o,f,g,i,d,m,z,E,T,Y,I,V,M,p,n,Q,A,X,P,Z,_,h,B,W,x,t]}class cl extends ue{constructor(e){super(),se(this,e,gl,_l,_e,{value:0,label:2,show_label:3,file_count:4,file_types:5,selectable:6,root:7,height:8,i18n:9,max_file_size:10,upload:11,stream_handler:12,uploading:1,allow_reordering:13})}}const El=cl;export{El as B,Al as F,Pe as a};
//# sourceMappingURL=FileUpload.DUgXd8x6.js.map
