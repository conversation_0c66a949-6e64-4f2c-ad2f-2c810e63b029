// qvideoframe.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QVideoFrame
{
%TypeHeaderCode
#include <qvideoframe.h>
%End

public:
    QVideoFrame();
    QVideoFrame(const QVideoFrameFormat &format);
%If (Qt_6_8_0 -)
    explicit QVideoFrame(const QImage &image);
%End
    QVideoFrame(const QVideoFrame &other);
    ~QVideoFrame();

    enum HandleType
    {
        NoHandle,
        RhiTextureHandle,
    };

    enum MapMode
    {
        NotMapped,
        ReadOnly,
        WriteOnly,
        ReadWrite,
    };

    bool isValid() const;
    QVideoFrameFormat::PixelFormat pixelFormat() const;
    QVideoFrame::HandleType handleType() const;
    QSize size() const;
    int width() const;
    int height() const;
    bool isMapped() const;
    bool isReadable() const;
    bool isWritable() const;
    QVideoFrame::MapMode mapMode() const;
    bool map(QVideoFrame::MapMode mode);
    void unmap();
    int bytesPerLine(int plane) const;
    void *bits(int plane) [uchar * (int plane)];
    int mappedBytes(int plane) const;
    qint64 startTime() const;
    void setStartTime(qint64 time);
    qint64 endTime() const;
    void setEndTime(qint64 time);
    int planeCount() const;
    bool operator==(const QVideoFrame &other) const;
    bool operator!=(const QVideoFrame &other) const;
    QVideoFrameFormat surfaceFormat() const;
    QImage toImage() const;

    struct PaintOptions
    {
%TypeHeaderCode
#include <qvideoframe.h>
%End

        QColor backgroundColor;
        Qt::AspectRatioMode aspectRatioMode;

        enum PaintFlag /BaseType=Flag/
        {
            DontDrawSubtitles,
        };

        typedef QFlags<QVideoFrame::PaintOptions::PaintFlag> PaintFlags;
        QVideoFrame::PaintOptions::PaintFlags paintFlags;
    };

    QString subtitleText() const;
    void setSubtitleText(const QString &text);
    void paint(QPainter *painter, const QRectF &rect, const QVideoFrame::PaintOptions &options);
%If (Qt_6_3_0 -)

    enum RotationAngle
    {
        Rotation0,
        Rotation90,
        Rotation180,
        Rotation270,
    };

%End
%If (Qt_6_3_0 -)
    void setRotationAngle(QVideoFrame::RotationAngle);
%End
%If (Qt_6_3_0 -)
    QVideoFrame::RotationAngle rotationAngle() const;
%End
%If (Qt_6_3_0 -)
    void setMirrored(bool);
%End
%If (Qt_6_3_0 -)
    bool mirrored() const;
%End
%If (Qt_6_7_0 -)
    void setRotation(QtVideo::Rotation angle);
%End
%If (Qt_6_7_0 -)
    QtVideo::Rotation rotation() const;
%End
%If (Qt_6_8_0 -)
    void setStreamFrameRate(qreal rate);
%End
%If (Qt_6_8_0 -)
    qreal streamFrameRate() const;
%End
};

%End
