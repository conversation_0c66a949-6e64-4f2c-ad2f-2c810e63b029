import{SvelteComponent as oe,init as ie,safe_not_equal as se,svg_element as me,claim_svg_element as be,children as B,detach as d,attr as _,insert_hydration as E,append_hydration as A,noop as le,element as V,claim_element as T,createEventDispatcher as nl,ensure_array_like as U,space as O,claim_space as D,destroy_each as ee,get_svelte_dataset as _e,text as W,claim_text as X,set_data as ne,listen as N,run_all as $,empty as R,toggle_class as M,set_style as F,group_outros as G,transition_out as j,check_outros as J,transition_in as w,onMount as Ol,binding_callbacks as ke,bind as ve,create_component as Z,claim_component as q,mount_component as K,add_flush_callback as pe,destroy_component as Y,assign as tl,get_spread_update as ol,get_spread_object as il}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{g as sl}from"./color.6b7WW5Kp.js";import{V as we,B as rl,S as al}from"./2.B2AoQPnG.js";import{B as fl}from"./BlockLabel.BTSz9r5s.js";import{E as ul}from"./Empty.DwQ6nkN6.js";function Dl(n){let e,t,l;return{c(){e=me("svg"),t=me("path"),l=me("path"),this.h()},l(o){e=be(o,"svg",{xmlns:!0,"xmlns:xlink":!0,"aria-hidden":!0,role:!0,class:!0,width:!0,height:!0,preserveAspectRatio:!0,viewBox:!0});var s=B(e);t=be(s,"path",{fill:!0,d:!0}),B(t).forEach(d),l=be(s,"path",{fill:!0,d:!0}),B(l).forEach(d),s.forEach(d),this.h()},h(){_(t,"fill","currentColor"),_(t,"d","M12 15H5a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5V5a1 1 0 0 0-1-1H3V2h6a3 3 0 0 1 3 3zM5 9a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h5V9zm15 14v2a1 1 0 0 0 1 1h5v-4h-5a1 1 0 0 0-1 1z"),_(l,"fill","currentColor"),_(l,"d","M2 30h28V2Zm26-2h-7a3 3 0 0 1-3-3v-2a3 3 0 0 1 3-3h5v-2a1 1 0 0 0-1-1h-6v-2h6a3 3 0 0 1 3 3Z"),_(e,"xmlns","http://www.w3.org/2000/svg"),_(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),_(e,"aria-hidden","true"),_(e,"role","img"),_(e,"class","iconify iconify--carbon"),_(e,"width","100%"),_(e,"height","100%"),_(e,"preserveAspectRatio","xMidYMid meet"),_(e,"viewBox","0 0 32 32")},m(o,s){E(o,e,s),A(e,t),A(e,l)},p:le,i:le,o:le,d(o){o&&d(e)}}}class he extends oe{constructor(e){super(),ie(this,e,null,Dl,se,{})}}function Ee(n,e,t){if(!t){var l=document.createElement("canvas");t=l.getContext("2d")}t.fillStyle=n,t.fillRect(0,0,1,1);const[o,s,i]=t.getImageData(0,0,1,1).data;return t.clearRect(0,0,1,1),`rgba(${o}, ${s}, ${i}, ${255/e})`}function cl(n,e,t,l){for(const o in n){const s=n[o].trim();s in we?e[o]=we[s]:e[o]={primary:t?Ee(n[o],1,l):n[o],secondary:t?Ee(n[o],.5,l):n[o]}}}function _l(n,e){let t=[],l=null,o=null;for(const s of n)o===s.class_or_confidence?l=l?l+s.token:s.token:(l!==null&&t.push({token:l,class_or_confidence:o}),l=s.token,o=s.class_or_confidence);return l!==null&&t.push({token:l,class_or_confidence:o}),t}function ye(n,e,t){const l=n.slice();l[18]=e[t];const o=typeof l[18].class_or_confidence=="string"?parseInt(l[18].class_or_confidence):l[18].class_or_confidence;return l[27]=o,l}function Se(n,e,t){const l=n.slice();return l[18]=e[t],l[20]=t,l}function je(n,e,t){const l=n.slice();return l[21]=e[t],l[23]=t,l}function Ne(n,e,t){const l=n.slice();return l[24]=e[t][0],l[25]=e[t][1],l[20]=t,l}function Rl(n){let e,t,l=n[1]&&ze(),o=U(n[0]),s=[];for(let i=0;i<o.length;i+=1)s[i]=Ie(ye(n,o,i));return{c(){l&&l.c(),e=O(),t=V("div");for(let i=0;i<s.length;i+=1)s[i].c();this.h()},l(i){l&&l.l(i),e=D(i),t=T(i,"DIV",{class:!0,"data-testid":!0});var a=B(t);for(let r=0;r<s.length;r+=1)s[r].l(a);a.forEach(d),this.h()},h(){_(t,"class","textfield svelte-ju12zg"),_(t,"data-testid","highlighted-text:textfield")},m(i,a){l&&l.m(i,a),E(i,e,a),E(i,t,a);for(let r=0;r<s.length;r+=1)s[r]&&s[r].m(t,null)},p(i,a){if(i[1]?l||(l=ze(),l.c(),l.m(e.parentNode,e)):l&&(l.d(1),l=null),a&1){o=U(i[0]);let r;for(r=0;r<o.length;r+=1){const u=ye(i,o,r);s[r]?s[r].p(u,a):(s[r]=Ie(u),s[r].c(),s[r].m(t,null))}for(;r<s.length;r+=1)s[r].d(1);s.length=o.length}},d(i){i&&(d(e),d(t)),l&&l.d(i),ee(s,i)}}}function Ul(n){let e,t,l=n[1]&&Ve(n),o=U(n[0]),s=[];for(let i=0;i<o.length;i+=1)s[i]=Pe(Se(n,o,i));return{c(){l&&l.c(),e=O(),t=V("div");for(let i=0;i<s.length;i+=1)s[i].c();this.h()},l(i){l&&l.l(i),e=D(i),t=T(i,"DIV",{class:!0});var a=B(t);for(let r=0;r<s.length;r+=1)s[r].l(a);a.forEach(d),this.h()},h(){_(t,"class","textfield svelte-ju12zg")},m(i,a){l&&l.m(i,a),E(i,e,a),E(i,t,a);for(let r=0;r<s.length;r+=1)s[r]&&s[r].m(t,null)},p(i,a){if(i[1]?l?l.p(i,a):(l=Ve(i),l.c(),l.m(e.parentNode,e)):l&&(l.d(1),l=null),a&223){o=U(i[0]);let r;for(r=0;r<o.length;r+=1){const u=Se(i,o,r);s[r]?s[r].p(u,a):(s[r]=Pe(u),s[r].c(),s[r].m(t,null))}for(;r<s.length;r+=1)s[r].d(1);s.length=o.length}},d(i){i&&(d(e),d(t)),l&&l.d(i),ee(s,i)}}}function ze(n){let e,t="<span>-1</span> <span>0</span> <span>+1</span>";return{c(){e=V("div"),e.innerHTML=t,this.h()},l(l){e=T(l,"DIV",{class:!0,"data-testid":!0,"data-svelte-h":!0}),_e(e)!=="svelte-mv3vmx"&&(e.innerHTML=t),this.h()},h(){_(e,"class","color-legend svelte-ju12zg"),_(e,"data-testid","highlighted-text:color-legend")},m(l,o){E(l,e,o)},d(l){l&&d(e)}}}function Ie(n){let e,t,l=n[18].token+"",o,s,i;return{c(){e=V("span"),t=V("span"),o=W(l),s=O(),this.h()},l(a){e=T(a,"SPAN",{class:!0,style:!0});var r=B(e);t=T(r,"SPAN",{class:!0});var u=B(t);o=X(u,l),u.forEach(d),s=D(r),r.forEach(d),this.h()},h(){_(t,"class","text svelte-ju12zg"),_(e,"class","textspan score-text svelte-ju12zg"),_(e,"style",i="background-color: rgba("+(n[27]&&n[27]<0?"128, 90, 213,"+-n[27]:"239, 68, 60,"+n[27])+")")},m(a,r){E(a,e,r),A(e,t),A(t,o),A(e,s)},p(a,r){r&1&&l!==(l=a[18].token+"")&&ne(o,l),r&1&&i!==(i="background-color: rgba("+(a[27]&&a[27]<0?"128, 90, 213,"+-a[27]:"239, 68, 60,"+a[27])+")")&&_(e,"style",i)},d(a){a&&d(e)}}}function Ve(n){let e,t=U(Object.entries(n[6])),l=[];for(let o=0;o<t.length;o+=1)l[o]=Te(Ne(n,t,o));return{c(){e=V("div");for(let o=0;o<l.length;o+=1)l[o].c();this.h()},l(o){e=T(o,"DIV",{class:!0,"data-testid":!0});var s=B(e);for(let i=0;i<l.length;i+=1)l[i].l(s);s.forEach(d),this.h()},h(){_(e,"class","category-legend svelte-ju12zg"),_(e,"data-testid","highlighted-text:category-legend")},m(o,s){E(o,e,s);for(let i=0;i<l.length;i+=1)l[i]&&l[i].m(e,null)},p(o,s){if(s&832){t=U(Object.entries(o[6]));let i;for(i=0;i<t.length;i+=1){const a=Ne(o,t,i);l[i]?l[i].p(a,s):(l[i]=Te(a),l[i].c(),l[i].m(e,null))}for(;i<l.length;i+=1)l[i].d(1);l.length=t.length}},d(o){o&&d(e),ee(l,o)}}}function Te(n){let e,t=n[24]+"",l,o,s,i;function a(){return n[11](n[24])}function r(){return n[12](n[24])}return{c(){e=V("div"),l=W(t),o=O(),this.h()},l(u){e=T(u,"DIV",{class:!0,style:!0});var f=B(e);l=X(f,t),o=D(f),f.forEach(d),this.h()},h(){_(e,"class","category-label svelte-ju12zg"),_(e,"style","background-color:"+n[25].secondary)},m(u,f){E(u,e,f),A(e,l),A(e,o),s||(i=[N(e,"mouseover",a),N(e,"focus",r),N(e,"mouseout",n[13]),N(e,"blur",n[14])],s=!0)},p(u,f){n=u},d(u){u&&d(e),s=!1,$(i)}}}function Ce(n){let e,t,l=n[21]+"",o,s,i,a,r=!n[1]&&n[2]&&n[18].class_or_confidence!==null&&Ae(n);function u(){return n[15](n[20],n[18])}return{c(){e=V("span"),t=V("span"),o=W(l),s=O(),r&&r.c(),this.h()},l(f){e=T(f,"SPAN",{class:!0});var m=B(e);t=T(m,"SPAN",{class:!0});var p=B(t);o=X(p,l),p.forEach(d),s=D(m),r&&r.l(m),m.forEach(d),this.h()},h(){_(t,"class","text svelte-ju12zg"),M(t,"no-label",n[18].class_or_confidence===null||!n[6][n[18].class_or_confidence]),_(e,"class","textspan svelte-ju12zg"),M(e,"no-cat",n[18].class_or_confidence===null||n[4]&&n[4]!==n[18].class_or_confidence),M(e,"hl",n[18].class_or_confidence!==null),M(e,"selectable",n[3]),F(e,"background-color",n[18].class_or_confidence===null||n[4]&&n[4]!==n[18].class_or_confidence?"":n[6][n[18].class_or_confidence].secondary)},m(f,m){E(f,e,m),A(e,t),A(t,o),A(e,s),r&&r.m(e,null),i||(a=N(e,"click",u),i=!0)},p(f,m){n=f,m&1&&l!==(l=n[21]+"")&&ne(o,l),m&65&&M(t,"no-label",n[18].class_or_confidence===null||!n[6][n[18].class_or_confidence]),!n[1]&&n[2]&&n[18].class_or_confidence!==null?r?r.p(n,m):(r=Ae(n),r.c(),r.m(e,null)):r&&(r.d(1),r=null),m&17&&M(e,"no-cat",n[18].class_or_confidence===null||n[4]&&n[4]!==n[18].class_or_confidence),m&1&&M(e,"hl",n[18].class_or_confidence!==null),m&8&&M(e,"selectable",n[3]),m&17&&F(e,"background-color",n[18].class_or_confidence===null||n[4]&&n[4]!==n[18].class_or_confidence?"":n[6][n[18].class_or_confidence].secondary)},d(f){f&&d(e),r&&r.d(),i=!1,a()}}}function Ae(n){let e,t,l=n[18].class_or_confidence+"",o;return{c(){e=W(` 
								`),t=V("span"),o=W(l),this.h()},l(s){e=X(s,` 
								`),t=T(s,"SPAN",{class:!0});var i=B(t);o=X(i,l),i.forEach(d),this.h()},h(){_(t,"class","label svelte-ju12zg"),F(t,"background-color",n[18].class_or_confidence===null||n[4]&&n[4]!==n[18].class_or_confidence?"":n[6][n[18].class_or_confidence].primary)},m(s,i){E(s,e,i),E(s,t,i),A(t,o)},p(s,i){i&1&&l!==(l=s[18].class_or_confidence+"")&&ne(o,l),i&17&&F(t,"background-color",s[18].class_or_confidence===null||s[4]&&s[4]!==s[18].class_or_confidence?"":s[6][s[18].class_or_confidence].primary)},d(s){s&&(d(e),d(t))}}}function Be(n){let e;return{c(){e=V("br")},l(t){e=T(t,"BR",{})},m(t,l){E(t,e,l)},d(t){t&&d(e)}}}function He(n){let e=n[21].trim()!=="",t,l=n[23]<ue(n[18].token).length-1,o,s=e&&Ce(n),i=l&&Be();return{c(){s&&s.c(),t=O(),i&&i.c(),o=R()},l(a){s&&s.l(a),t=D(a),i&&i.l(a),o=R()},m(a,r){s&&s.m(a,r),E(a,t,r),i&&i.m(a,r),E(a,o,r)},p(a,r){r&1&&(e=a[21].trim()!==""),e?s?s.p(a,r):(s=Ce(a),s.c(),s.m(t.parentNode,t)):s&&(s.d(1),s=null),r&1&&(l=a[23]<ue(a[18].token).length-1),l?i||(i=Be(),i.c(),i.m(o.parentNode,o)):i&&(i.d(1),i=null)},d(a){a&&(d(t),d(o)),s&&s.d(a),i&&i.d(a)}}}function Pe(n){let e,t=U(ue(n[18].token)),l=[];for(let o=0;o<t.length;o+=1)l[o]=He(je(n,t,o));return{c(){for(let o=0;o<l.length;o+=1)l[o].c();e=R()},l(o){for(let s=0;s<l.length;s+=1)l[s].l(o);e=R()},m(o,s){for(let i=0;i<l.length;i+=1)l[i]&&l[i].m(o,s);E(o,e,s)},p(o,s){if(s&223){t=U(ue(o[18].token));let i;for(i=0;i<t.length;i+=1){const a=je(o,t,i);l[i]?l[i].p(a,s):(l[i]=He(a),l[i].c(),l[i].m(e.parentNode,e))}for(;i<l.length;i+=1)l[i].d(1);l.length=t.length}},d(o){o&&d(e),ee(l,o)}}}function Fl(n){let e;function t(s,i){return s[5]==="categories"?Ul:Rl}let l=t(n),o=l(n);return{c(){e=V("div"),o.c(),this.h()},l(s){e=T(s,"DIV",{class:!0});var i=B(e);o.l(i),i.forEach(d),this.h()},h(){_(e,"class","container svelte-ju12zg")},m(s,i){E(s,e,i),o.m(e,null)},p(s,[i]){l===(l=t(s))&&o?o.p(s,i):(o.d(1),o=l(s),o&&(o.c(),o.m(e,null)))},i:le,o:le,d(s){s&&d(e),o.d()}}}function ue(n){return n.split(`
`)}function Zl(n,e,t){const l=typeof document<"u";let{value:o=[]}=e,{show_legend:s=!1}=e,{show_inline_category:i=!0}=e,{color_map:a={}}=e,{selectable:r=!1}=e,u,f={},m="";const p=nl();let v;function c(b){t(4,m=b)}function g(){t(4,m="")}const S=b=>c(b),H=b=>c(b),I=()=>g(),k=()=>g(),z=(b,C)=>{p("select",{index:b,value:[C.token,C.class_or_confidence]})};return n.$$set=b=>{"value"in b&&t(0,o=b.value),"show_legend"in b&&t(1,s=b.show_legend),"show_inline_category"in b&&t(2,i=b.show_inline_category),"color_map"in b&&t(10,a=b.color_map),"selectable"in b&&t(3,r=b.selectable)},n.$$.update=()=>{if(n.$$.dirty&1025){if(a||t(10,a={}),o.length>0){for(let b of o)if(b.class_or_confidence!==null)if(typeof b.class_or_confidence=="string"){if(t(5,v="categories"),!(b.class_or_confidence in a)){let C=sl(Object.keys(a).length);t(10,a[b.class_or_confidence]=C,a)}}else t(5,v="scores")}cl(a,f,l,u)}},[o,s,i,r,m,v,f,p,c,g,a,S,H,I,k,z]}class ql extends oe{constructor(e){super(),ie(this,e,Zl,Fl,se,{value:0,show_legend:1,show_inline_category:2,color_map:10,selectable:3})}}const Kl=ql;function Yl(n){let e,t,l,o;return{c(){e=V("input"),this.h()},l(s){e=T(s,"INPUT",{class:!0,type:!0,step:!0,style:!0}),this.h()},h(){_(e,"class","label-input svelte-1cag2po"),e.autofocus=!0,_(e,"type","number"),_(e,"step","0.1"),_(e,"style",t="background-color: rgba("+(typeof n[1]=="number"&&n[1]<0?"128, 90, 213,"+-n[1]:"239, 68, 60,"+n[1])+")"),e.value=n[1],F(e,"width","7ch")},m(s,i){E(s,e,i),e.focus(),l||(o=[N(e,"input",n[8]),N(e,"blur",n[14]),N(e,"keydown",n[15])],l=!0)},p(s,i){i&2&&t!==(t="background-color: rgba("+(typeof s[1]=="number"&&s[1]<0?"128, 90, 213,"+-s[1]:"239, 68, 60,"+s[1])+")")&&_(e,"style",t),i&2&&e.value!==s[1]&&(e.value=s[1]);const a=i&2;(i&2||a)&&F(e,"width","7ch")},d(s){s&&d(e),l=!1,$(o)}}}function Gl(n){let e,t,l,o;return{c(){e=V("input"),this.h()},l(s){e=T(s,"INPUT",{class:!0,id:!0,type:!0,placeholder:!0}),this.h()},h(){var s;_(e,"class","label-input svelte-1cag2po"),e.autofocus=!0,_(e,"id",t=`label-input-${n[3]}`),_(e,"type","text"),_(e,"placeholder","label"),e.value=n[1],F(e,"background-color",n[1]===null||n[2]&&n[2]!==n[1]?"":n[6][n[1]].primary),F(e,"width",n[7]?((s=n[7].toString())==null?void 0:s.length)+4+"ch":"8ch")},m(s,i){E(s,e,i),e.focus(),l||(o=[N(e,"input",n[8]),N(e,"blur",n[12]),N(e,"keydown",n[13]),N(e,"focus",Ql)],l=!0)},p(s,i){var a;i&8&&t!==(t=`label-input-${s[3]}`)&&_(e,"id",t),i&2&&e.value!==s[1]&&(e.value=s[1]),i&70&&F(e,"background-color",s[1]===null||s[2]&&s[2]!==s[1]?"":s[6][s[1]].primary),i&128&&F(e,"width",s[7]?((a=s[7].toString())==null?void 0:a.length)+4+"ch":"8ch")},d(s){s&&d(e),l=!1,$(o)}}}function Jl(n){let e;function t(s,i){return s[5]?Yl:Gl}let l=t(n),o=l(n);return{c(){o.c(),e=R()},l(s){o.l(s),e=R()},m(s,i){o.m(s,i),E(s,e,i)},p(s,[i]){l===(l=t(s))&&o?o.p(s,i):(o.d(1),o=l(s),o&&(o.c(),o.m(e.parentNode,e)))},i:le,o:le,d(s){s&&d(e),o.d(s)}}}function Ql(n){let e=n.target;e&&e.placeholder&&(e.placeholder="")}function Wl(n,e,t){let{value:l}=e,{category:o}=e,{active:s}=e,{labelToEdit:i}=e,{indexOfLabel:a}=e,{text:r}=e,{handleValueChange:u}=e,{isScoresMode:f=!1}=e,{_color_map:m}=e,p=o;function v(k){let z=k.target;z&&t(7,p=z.value)}function c(k,z,b){let C=k.target;t(10,l=[...l.slice(0,z),{token:b,class_or_confidence:C.value===""?null:f?Number(C.value):C.value},...l.slice(z+1)]),u()}const g=k=>c(k,a,r),S=k=>{k.key==="Enter"&&(c(k,a,r),t(0,i=-1))},H=k=>c(k,a,r),I=k=>{k.key==="Enter"&&(c(k,a,r),t(0,i=-1))};return n.$$set=k=>{"value"in k&&t(10,l=k.value),"category"in k&&t(1,o=k.category),"active"in k&&t(2,s=k.active),"labelToEdit"in k&&t(0,i=k.labelToEdit),"indexOfLabel"in k&&t(3,a=k.indexOfLabel),"text"in k&&t(4,r=k.text),"handleValueChange"in k&&t(11,u=k.handleValueChange),"isScoresMode"in k&&t(5,f=k.isScoresMode),"_color_map"in k&&t(6,m=k._color_map)},[i,o,s,a,r,f,m,p,v,c,l,u,g,S,H,I]}class hl extends oe{constructor(e){super(),ie(this,e,Wl,Jl,se,{value:10,category:1,active:2,labelToEdit:0,indexOfLabel:3,text:4,handleValueChange:11,isScoresMode:5,_color_map:6})}}function Le(n,e,t){const l=n.slice();l[45]=e[t].token,l[46]=e[t].class_or_confidence,l[48]=t;const o=typeof l[46]=="string"?parseInt(l[46]):l[46];return l[54]=o,l}function Me(n,e,t){const l=n.slice();return l[45]=e[t].token,l[46]=e[t].class_or_confidence,l[48]=t,l}function Oe(n,e,t){const l=n.slice();return l[49]=e[t],l[51]=t,l}function De(n,e,t){const l=n.slice();return l[46]=e[t][0],l[52]=e[t][1],l[48]=t,l}function Xl(n){let e,t,l,o=n[1]&&Re(),s=U(n[0]),i=[];for(let r=0;r<s.length;r+=1)i[r]=Ze(Le(n,s,r));const a=r=>j(i[r],1,1,()=>{i[r]=null});return{c(){o&&o.c(),e=O(),t=V("div");for(let r=0;r<i.length;r+=1)i[r].c();this.h()},l(r){o&&o.l(r),e=D(r),t=T(r,"DIV",{class:!0,"data-testid":!0});var u=B(t);for(let f=0;f<i.length;f+=1)i[f].l(u);u.forEach(d),this.h()},h(){_(t,"class","textfield svelte-1ozsnjl"),_(t,"data-testid","highlighted-text:textfield")},m(r,u){o&&o.m(r,u),E(r,e,u),E(r,t,u);for(let f=0;f<i.length;f+=1)i[f]&&i[f].m(t,null);l=!0},p(r,u){if(r[1]?o||(o=Re(),o.c(),o.m(e.parentNode,e)):o&&(o.d(1),o=null),u[0]&889){s=U(r[0]);let f;for(f=0;f<s.length;f+=1){const m=Le(r,s,f);i[f]?(i[f].p(m,u),w(i[f],1)):(i[f]=Ze(m),i[f].c(),w(i[f],1),i[f].m(t,null))}for(G(),f=s.length;f<i.length;f+=1)a(f);J()}},i(r){if(!l){for(let u=0;u<s.length;u+=1)w(i[u]);l=!0}},o(r){i=i.filter(Boolean);for(let u=0;u<i.length;u+=1)j(i[u]);l=!1},d(r){r&&(d(e),d(t)),o&&o.d(r),ee(i,r)}}}function $l(n){let e,t,l,o=n[1]&&qe(n),s=U(n[0]),i=[];for(let r=0;r<s.length;r+=1)i[r]=xe(Me(n,s,r));const a=r=>j(i[r],1,1,()=>{i[r]=null});return{c(){o&&o.c(),e=O(),t=V("div");for(let r=0;r<i.length;r+=1)i[r].c();this.h()},l(r){o&&o.l(r),e=D(r),t=T(r,"DIV",{class:!0});var u=B(t);for(let f=0;f<i.length;f+=1)i[f].l(u);u.forEach(d),this.h()},h(){_(t,"class","textfield svelte-1ozsnjl")},m(r,u){o&&o.m(r,u),E(r,e,u),E(r,t,u);for(let f=0;f<i.length;f+=1)i[f]&&i[f].m(t,null);l=!0},p(r,u){if(r[1]?o?o.p(r,u):(o=qe(r),o.c(),o.m(e.parentNode,e)):o&&(o.d(1),o=null),u[0]&13183){s=U(r[0]);let f;for(f=0;f<s.length;f+=1){const m=Me(r,s,f);i[f]?(i[f].p(m,u),w(i[f],1)):(i[f]=xe(m),i[f].c(),w(i[f],1),i[f].m(t,null))}for(G(),f=s.length;f<i.length;f+=1)a(f);J()}},i(r){if(!l){for(let u=0;u<s.length;u+=1)w(i[u]);l=!0}},o(r){i=i.filter(Boolean);for(let u=0;u<i.length;u+=1)j(i[u]);l=!1},d(r){r&&(d(e),d(t)),o&&o.d(r),ee(i,r)}}}function Re(n){let e,t="<span>-1</span> <span>0</span> <span>+1</span>";return{c(){e=V("div"),e.innerHTML=t,this.h()},l(l){e=T(l,"DIV",{class:!0,"data-testid":!0,"data-svelte-h":!0}),_e(e)!=="svelte-mv3vmx"&&(e.innerHTML=t),this.h()},h(){_(e,"class","color-legend svelte-1ozsnjl"),_(e,"data-testid","highlighted-text:color-legend")},m(l,o){E(l,e,o)},d(l){l&&d(e)}}}function Ue(n){let e,t,l;function o(i){n[32](i)}let s={labelToEdit:n[6],_color_map:n[3],category:n[46],active:n[5],indexOfLabel:n[48],text:n[45],handleValueChange:n[9],isScoresMode:!0};return n[0]!==void 0&&(s.value=n[0]),e=new hl({props:s}),ke.push(()=>ve(e,"value",o)),{c(){Z(e.$$.fragment)},l(i){q(e.$$.fragment,i)},m(i,a){K(e,i,a),l=!0},p(i,a){const r={};a[0]&64&&(r.labelToEdit=i[6]),a[0]&8&&(r._color_map=i[3]),a[0]&1&&(r.category=i[46]),a[0]&32&&(r.active=i[5]),a[0]&1&&(r.text=i[45]),!t&&a[0]&1&&(t=!0,r.value=i[0],pe(()=>t=!1)),e.$set(r)},i(i){l||(w(e.$$.fragment,i),l=!0)},o(i){j(e.$$.fragment,i),l=!1},d(i){Y(e,i)}}}function Fe(n){let e,t="×",l,o;function s(){return n[37](n[48])}function i(...a){return n[38](n[48],...a)}return{c(){e=V("span"),e.textContent=t,this.h()},l(a){e=T(a,"SPAN",{class:!0,role:!0,"aria-roledescription":!0,tabindex:!0,"data-svelte-h":!0}),_e(e)!=="svelte-hxhs1z"&&(e.textContent=t),this.h()},h(){_(e,"class","label-clear-button svelte-1ozsnjl"),_(e,"role","button"),_(e,"aria-roledescription","Remove label from text"),_(e,"tabindex","0")},m(a,r){E(a,e,r),l||(o=[N(e,"click",s),N(e,"keydown",i)],l=!0)},p(a,r){n=a},d(a){a&&d(e),l=!1,$(o)}}}function Ze(n){let e,t,l,o=n[45]+"",s,i,a,r,u,f,m,p,v=n[46]&&n[6]===n[48]&&Ue(n);function c(){return n[33](n[48])}function g(){return n[34](n[48])}function S(){return n[35](n[48])}function H(...k){return n[36](n[48],...k)}let I=n[46]&&n[4]===n[48]&&Fe(n);return{c(){e=V("span"),t=V("span"),l=V("span"),s=W(o),i=O(),v&&v.c(),r=O(),I&&I.c(),u=O(),this.h()},l(k){e=T(k,"SPAN",{class:!0});var z=B(e);t=T(z,"SPAN",{class:!0,role:!0,tabindex:!0,style:!0});var b=B(t);l=T(b,"SPAN",{class:!0});var C=B(l);s=X(C,o),C.forEach(d),i=D(b),v&&v.l(b),b.forEach(d),r=D(z),I&&I.l(z),u=D(z),z.forEach(d),this.h()},h(){_(l,"class","text svelte-1ozsnjl"),_(t,"class","textspan score-text svelte-1ozsnjl"),_(t,"role","button"),_(t,"tabindex","0"),_(t,"style",a="background-color: rgba("+(n[54]&&n[54]<0?"128, 90, 213,"+-n[54]:"239, 68, 60,"+n[54])+")"),M(t,"no-cat",n[46]===null||n[5]&&n[5]!==n[46]),M(t,"hl",n[46]!==null),_(e,"class","score-text-container svelte-1ozsnjl")},m(k,z){E(k,e,z),A(e,t),A(t,l),A(l,s),A(t,i),v&&v.m(t,null),A(e,r),I&&I.m(e,null),A(e,u),f=!0,m||(p=[N(t,"mouseover",c),N(t,"focus",g),N(t,"click",S),N(t,"keydown",H)],m=!0)},p(k,z){n=k,(!f||z[0]&1)&&o!==(o=n[45]+"")&&ne(s,o),n[46]&&n[6]===n[48]?v?(v.p(n,z),z[0]&65&&w(v,1)):(v=Ue(n),v.c(),w(v,1),v.m(t,null)):v&&(G(),j(v,1,1,()=>{v=null}),J()),(!f||z[0]&1&&a!==(a="background-color: rgba("+(n[54]&&n[54]<0?"128, 90, 213,"+-n[54]:"239, 68, 60,"+n[54])+")"))&&_(t,"style",a),(!f||z[0]&33)&&M(t,"no-cat",n[46]===null||n[5]&&n[5]!==n[46]),(!f||z[0]&1)&&M(t,"hl",n[46]!==null),n[46]&&n[4]===n[48]?I?I.p(n,z):(I=Fe(n),I.c(),I.m(e,u)):I&&(I.d(1),I=null)},i(k){f||(w(v),f=!0)},o(k){j(v),f=!1},d(k){k&&d(e),v&&v.d(),I&&I.d(),m=!1,$(p)}}}function qe(n){let e,t=n[3]&&Ke(n);return{c(){e=V("div"),t&&t.c(),this.h()},l(l){e=T(l,"DIV",{class:!0,"data-testid":!0});var o=B(e);t&&t.l(o),o.forEach(d),this.h()},h(){_(e,"class","class_or_confidence-legend svelte-1ozsnjl"),_(e,"data-testid","highlighted-text:class_or_confidence-legend")},m(l,o){E(l,e,o),t&&t.m(e,null)},p(l,o){l[3]?t?t.p(l,o):(t=Ke(l),t.c(),t.m(e,null)):t&&(t.d(1),t=null)},d(l){l&&d(e),t&&t.d()}}}function Ke(n){let e,t=U(Object.entries(n[3])),l=[];for(let o=0;o<t.length;o+=1)l[o]=Ye(De(n,t,o));return{c(){for(let o=0;o<l.length;o+=1)l[o].c();e=R()},l(o){for(let s=0;s<l.length;s+=1)l[s].l(o);e=R()},m(o,s){for(let i=0;i<l.length;i+=1)l[i]&&l[i].m(o,s);E(o,e,s)},p(o,s){if(s[0]&3080){t=U(Object.entries(o[3]));let i;for(i=0;i<t.length;i+=1){const a=De(o,t,i);l[i]?l[i].p(a,s):(l[i]=Ye(a),l[i].c(),l[i].m(e.parentNode,e))}for(;i<l.length;i+=1)l[i].d(1);l.length=t.length}},d(o){o&&d(e),ee(l,o)}}}function Ye(n){let e,t=n[46]+"",l,o,s,i,a;function r(){return n[15](n[46])}function u(){return n[16](n[46])}return{c(){e=V("div"),l=W(t),o=O(),this.h()},l(f){e=T(f,"DIV",{role:!0,"aria-roledescription":!0,tabindex:!0,class:!0,style:!0});var m=B(e);l=X(m,t),o=D(m),m.forEach(d),this.h()},h(){_(e,"role","button"),_(e,"aria-roledescription","Categories of highlighted text. Hover to see text with this class_or_confidence highlighted."),_(e,"tabindex","0"),_(e,"class","class_or_confidence-label svelte-1ozsnjl"),_(e,"style",s="background-color:"+n[52].secondary)},m(f,m){E(f,e,m),A(e,l),A(e,o),i||(a=[N(e,"mouseover",r),N(e,"focus",u),N(e,"mouseout",n[17]),N(e,"blur",n[18])],i=!0)},p(f,m){n=f,m[0]&8&&t!==(t=n[46]+"")&&ne(l,t),m[0]&8&&s!==(s="background-color:"+n[52].secondary)&&_(e,"style",s)},d(f){f&&d(e),i=!1,$(a)}}}function Ge(n){let e,t,l,o=n[49]+"",s,i,a,r,u,f,m;function p(){return n[20](n[48])}function v(){return n[21](n[48])}function c(){return n[22](n[48])}let g=!n[1]&&n[46]!==null&&n[6]!==n[48]&&Je(n),S=n[6]===n[48]&&n[46]!==null&&Qe(n);function H(){return n[26](n[46],n[48],n[45])}function I(...C){return n[27](n[46],n[48],n[45],...C)}function k(){return n[28](n[48])}function z(){return n[29](n[48])}let b=n[46]!==null&&We(n);return{c(){e=V("span"),t=V("span"),l=V("span"),s=W(o),i=O(),g&&g.c(),a=O(),S&&S.c(),r=O(),b&&b.c(),this.h()},l(C){e=T(C,"SPAN",{class:!0});var P=B(e);t=T(P,"SPAN",{role:!0,tabindex:!0,class:!0});var Q=B(t);l=T(Q,"SPAN",{class:!0,role:!0,tabindex:!0});var te=B(l);s=X(te,o),te.forEach(d),i=D(Q),g&&g.l(Q),a=D(Q),S&&S.l(Q),Q.forEach(d),r=D(P),b&&b.l(P),P.forEach(d),this.h()},h(){_(l,"class","text svelte-1ozsnjl"),_(l,"role","button"),_(l,"tabindex","0"),M(l,"no-label",n[46]===null),_(t,"role","button"),_(t,"tabindex","0"),_(t,"class","textspan svelte-1ozsnjl"),M(t,"no-cat",n[46]===null||n[5]&&n[5]!==n[46]),M(t,"hl",n[46]!==null),M(t,"selectable",n[2]),F(t,"background-color",n[46]===null||n[5]&&n[5]!==n[46]?"":n[46]&&n[3][n[46]]?n[3][n[46]].secondary:""),_(e,"class","text-class_or_confidence-container svelte-1ozsnjl")},m(C,P){E(C,e,P),A(e,t),A(t,l),A(l,s),A(t,i),g&&g.m(t,null),A(t,a),S&&S.m(t,null),A(e,r),b&&b.m(e,null),u=!0,f||(m=[N(l,"keydown",n[19]),N(l,"focus",p),N(l,"mouseover",v),N(l,"click",c),N(t,"click",H),N(t,"keydown",I),N(t,"focus",k),N(t,"mouseover",z)],f=!0)},p(C,P){n=C,(!u||P[0]&1)&&o!==(o=n[49]+"")&&ne(s,o),(!u||P[0]&1)&&M(l,"no-label",n[46]===null),!n[1]&&n[46]!==null&&n[6]!==n[48]?g?g.p(n,P):(g=Je(n),g.c(),g.m(t,a)):g&&(g.d(1),g=null),n[6]===n[48]&&n[46]!==null?S?(S.p(n,P),P[0]&65&&w(S,1)):(S=Qe(n),S.c(),w(S,1),S.m(t,null)):S&&(G(),j(S,1,1,()=>{S=null}),J()),(!u||P[0]&33)&&M(t,"no-cat",n[46]===null||n[5]&&n[5]!==n[46]),(!u||P[0]&1)&&M(t,"hl",n[46]!==null),(!u||P[0]&4)&&M(t,"selectable",n[2]),P[0]&41&&F(t,"background-color",n[46]===null||n[5]&&n[5]!==n[46]?"":n[46]&&n[3][n[46]]?n[3][n[46]].secondary:""),n[46]!==null?b?b.p(n,P):(b=We(n),b.c(),b.m(e,null)):b&&(b.d(1),b=null)},i(C){u||(w(S),u=!0)},o(C){j(S),u=!1},d(C){C&&d(e),g&&g.d(),S&&S.d(),b&&b.d(),f=!1,$(m)}}}function Je(n){let e,t=n[46]+"",l,o,s;function i(){return n[23](n[48])}function a(){return n[24](n[48])}return{c(){e=V("span"),l=W(t),this.h()},l(r){e=T(r,"SPAN",{id:!0,class:!0,role:!0,tabindex:!0});var u=B(e);l=X(u,t),u.forEach(d),this.h()},h(){_(e,"id",`label-tag-${n[48]}`),_(e,"class","label svelte-1ozsnjl"),_(e,"role","button"),_(e,"tabindex","0"),F(e,"background-color",n[46]===null||n[5]&&n[5]!==n[46]?"":n[3][n[46]].primary)},m(r,u){E(r,e,u),A(e,l),o||(s=[N(e,"click",i),N(e,"keydown",a)],o=!0)},p(r,u){n=r,u[0]&1&&t!==(t=n[46]+"")&&ne(l,t),u[0]&41&&F(e,"background-color",n[46]===null||n[5]&&n[5]!==n[46]?"":n[3][n[46]].primary)},d(r){r&&d(e),o=!1,$(s)}}}function Qe(n){let e,t,l,o;function s(a){n[25](a)}let i={labelToEdit:n[6],category:n[46],active:n[5],_color_map:n[3],indexOfLabel:n[48],text:n[45],handleValueChange:n[9]};return n[0]!==void 0&&(i.value=n[0]),t=new hl({props:i}),ke.push(()=>ve(t,"value",s)),{c(){e=W(` 
									`),Z(t.$$.fragment)},l(a){e=X(a,` 
									`),q(t.$$.fragment,a)},m(a,r){E(a,e,r),K(t,a,r),o=!0},p(a,r){const u={};r[0]&64&&(u.labelToEdit=a[6]),r[0]&1&&(u.category=a[46]),r[0]&32&&(u.active=a[5]),r[0]&8&&(u._color_map=a[3]),r[0]&1&&(u.text=a[45]),!l&&r[0]&1&&(l=!0,u.value=a[0],pe(()=>l=!1)),t.$set(u)},i(a){o||(w(t.$$.fragment,a),o=!0)},o(a){j(t.$$.fragment,a),o=!1},d(a){a&&d(e),Y(t,a)}}}function We(n){let e,t="×",l,o;function s(){return n[30](n[48])}function i(...a){return n[31](n[48],...a)}return{c(){e=V("span"),e.textContent=t,this.h()},l(a){e=T(a,"SPAN",{class:!0,role:!0,"aria-roledescription":!0,tabindex:!0,"data-svelte-h":!0}),_e(e)!=="svelte-1fuy4vv"&&(e.textContent=t),this.h()},h(){_(e,"class","label-clear-button svelte-1ozsnjl"),_(e,"role","button"),_(e,"aria-roledescription","Remove label from text"),_(e,"tabindex","0")},m(a,r){E(a,e,r),l||(o=[N(e,"click",s),N(e,"keydown",i)],l=!0)},p(a,r){n=a},d(a){a&&d(e),l=!1,$(o)}}}function Xe(n){let e;return{c(){e=V("br")},l(t){e=T(t,"BR",{})},m(t,l){E(t,e,l)},d(t){t&&d(e)}}}function $e(n){let e=n[49].trim()!=="",t,l=n[51]<ce(n[45]).length-1,o,s,i=e&&Ge(n),a=l&&Xe();return{c(){i&&i.c(),t=O(),a&&a.c(),o=R()},l(r){i&&i.l(r),t=D(r),a&&a.l(r),o=R()},m(r,u){i&&i.m(r,u),E(r,t,u),a&&a.m(r,u),E(r,o,u),s=!0},p(r,u){u[0]&1&&(e=r[49].trim()!==""),e?i?(i.p(r,u),u[0]&1&&w(i,1)):(i=Ge(r),i.c(),w(i,1),i.m(t.parentNode,t)):i&&(G(),j(i,1,1,()=>{i=null}),J()),u[0]&1&&(l=r[51]<ce(r[45]).length-1),l?a||(a=Xe(),a.c(),a.m(o.parentNode,o)):a&&(a.d(1),a=null)},i(r){s||(w(i),s=!0)},o(r){j(i),s=!1},d(r){r&&(d(t),d(o)),i&&i.d(r),a&&a.d(r)}}}function xe(n){let e,t,l=U(ce(n[45])),o=[];for(let i=0;i<l.length;i+=1)o[i]=$e(Oe(n,l,i));const s=i=>j(o[i],1,1,()=>{o[i]=null});return{c(){for(let i=0;i<o.length;i+=1)o[i].c();e=R()},l(i){for(let a=0;a<o.length;a+=1)o[a].l(i);e=R()},m(i,a){for(let r=0;r<o.length;r+=1)o[r]&&o[r].m(i,a);E(i,e,a),t=!0},p(i,a){if(a[0]&13183){l=U(ce(i[45]));let r;for(r=0;r<l.length;r+=1){const u=Oe(i,l,r);o[r]?(o[r].p(u,a),w(o[r],1)):(o[r]=$e(u),o[r].c(),w(o[r],1),o[r].m(e.parentNode,e))}for(G(),r=l.length;r<o.length;r+=1)s(r);J()}},i(i){if(!t){for(let a=0;a<l.length;a+=1)w(o[a]);t=!0}},o(i){o=o.filter(Boolean);for(let a=0;a<o.length;a+=1)j(o[a]);t=!1},d(i){i&&d(e),ee(o,i)}}}function xl(n){let e,t,l,o;const s=[$l,Xl],i=[];function a(r,u){return r[7]==="categories"?0:1}return t=a(n),l=i[t]=s[t](n),{c(){e=V("div"),l.c(),this.h()},l(r){e=T(r,"DIV",{class:!0});var u=B(e);l.l(u),u.forEach(d),this.h()},h(){_(e,"class","container svelte-1ozsnjl")},m(r,u){E(r,e,u),i[t].m(e,null),o=!0},p(r,u){let f=t;t=a(r),t===f?i[t].p(r,u):(G(),j(i[f],1,1,()=>{i[f]=null}),J(),l=i[t],l?l.p(r,u):(l=i[t]=s[t](r),l.c()),w(l,1),l.m(e,null))},i(r){o||(w(l),o=!0)},o(r){j(l),o=!1},d(r){r&&d(e),i[t].d()}}}function ce(n){return n.split(`
`)}function en(n,e,t){const l=typeof document<"u";let{value:o=[]}=e,{show_legend:s=!1}=e,{color_map:i={}}=e,{selectable:a=!1}=e,r=-1,u,f={},m="",p,v=-1;Ol(()=>{const h=()=>{p=window.getSelection(),C(),window.removeEventListener("mouseup",h)};window.addEventListener("mousedown",()=>{window.addEventListener("mouseup",h)})});async function c(h,L){var x;if(p!=null&&p.toString()&&r!==-1&&o[r].token.toString().includes(p.toString())){const re=Symbol(),ge=o[r].token,[Hl,Pl,Ll]=[ge.substring(0,h),ge.substring(h,L),ge.substring(L)];let ae=[...o.slice(0,r),{token:Hl,class_or_confidence:null},{token:Pl,class_or_confidence:I==="scores"?1:"label",flag:re},{token:Ll,class_or_confidence:null},...o.slice(r+1)];t(6,v=ae.findIndex(({flag:fe})=>fe===re)),ae=ae.filter(fe=>fe.token.trim()!==""),t(0,o=ae.map(({flag:fe,...Ml})=>Ml)),H(),(x=document.getElementById(`label-input-${v}`))==null||x.focus()}}const g=nl();function S(h){var L;!o||h<0||h>=o.length||(t(0,o[h].class_or_confidence=null,o),t(0,o=_l(o)),H(),(L=window.getSelection())==null||L.empty())}function H(){g("change",o),t(6,v=-1),s&&(t(14,i={}),t(3,f={}))}let I;function k(h){t(5,m=h)}function z(){t(5,m="")}async function b(h){p=window.getSelection(),h.key==="Enter"&&C()}function C(){if(p&&(p==null?void 0:p.toString().trim())!==""){const h=p.getRangeAt(0).startOffset,L=p.getRangeAt(0).endOffset;c(h,L)}}function P(h,L,x){g("select",{index:h,value:[L,x]})}const Q=h=>k(h),te=h=>k(h),de=()=>z(),y=()=>z(),dl=h=>b(h),gl=h=>t(4,r=h),ml=h=>t(4,r=h),bl=h=>t(6,v=h),kl=h=>t(6,v=h),vl=h=>t(6,v=h);function pl(h){o=h,t(0,o)}const wl=(h,L,x)=>{h!==null&&P(L,x,h)},El=(h,L,x,re)=>{h!==null?(t(6,v=L),P(L,x,h)):b(re)},yl=h=>t(4,r=h),Sl=h=>t(4,r=h),jl=h=>S(h),Nl=(h,L)=>{L.key==="Enter"&&S(h)};function zl(h){o=h,t(0,o)}const Il=h=>t(4,r=h),Vl=h=>t(4,r=h),Tl=h=>t(6,v=h),Cl=(h,L)=>{L.key==="Enter"&&t(6,v=h)},Al=h=>S(h),Bl=(h,L)=>{L.key==="Enter"&&S(h)};return n.$$set=h=>{"value"in h&&t(0,o=h.value),"show_legend"in h&&t(1,s=h.show_legend),"color_map"in h&&t(14,i=h.color_map),"selectable"in h&&t(2,a=h.selectable)},n.$$.update=()=>{if(n.$$.dirty[0]&16393){if(i||t(14,i={}),o.length>0){for(let h of o)if(h.class_or_confidence!==null)if(typeof h.class_or_confidence=="string"){if(t(7,I="categories"),!(h.class_or_confidence in i)){let L=sl(Object.keys(i).length);t(14,i[h.class_or_confidence]=L,i)}}else t(7,I="scores")}cl(i,f,l,u)}},[o,s,a,f,r,m,v,I,S,H,k,z,b,P,i,Q,te,de,y,dl,gl,ml,bl,kl,vl,pl,wl,El,yl,Sl,jl,Nl,zl,Il,Vl,Tl,Cl,Al,Bl]}class ln extends oe{constructor(e){super(),ie(this,e,en,xl,se,{value:0,show_legend:1,color_map:14,selectable:2},null,[-1,-1])}}const nn=ln;function tn(n){let e,t;return e=new rl({props:{variant:n[13]?"dashed":"solid",test_id:"highlighted-text",visible:n[5],elem_id:n[3],elem_classes:n[4],padding:!1,container:n[9],scale:n[10],min_width:n[11],$$slots:{default:[fn]},$$scope:{ctx:n}}}),{c(){Z(e.$$.fragment)},l(l){q(e.$$.fragment,l)},m(l,o){K(e,l,o),t=!0},p(l,o){const s={};o&8192&&(s.variant=l[13]?"dashed":"solid"),o&32&&(s.visible=l[5]),o&8&&(s.elem_id=l[3]),o&16&&(s.elem_classes=l[4]),o&512&&(s.container=l[9]),o&1024&&(s.scale=l[10]),o&2048&&(s.min_width=l[11]),o&16896839&&(s.$$scope={dirty:o,ctx:l}),e.$set(s)},i(l){t||(w(e.$$.fragment,l),t=!0)},o(l){j(e.$$.fragment,l),t=!1},d(l){Y(e,l)}}}function on(n){let e,t;return e=new rl({props:{variant:"solid",test_id:"highlighted-text",visible:n[5],elem_id:n[3],elem_classes:n[4],padding:!1,container:n[9],scale:n[10],min_width:n[11],rtl:n[15],$$slots:{default:[hn]},$$scope:{ctx:n}}}),{c(){Z(e.$$.fragment)},l(l){q(e.$$.fragment,l)},m(l,o){K(e,l,o),t=!0},p(l,o){const s={};o&32&&(s.visible=l[5]),o&8&&(s.elem_id=l[3]),o&16&&(s.elem_classes=l[4]),o&512&&(s.container=l[9]),o&1024&&(s.scale=l[10]),o&2048&&(s.min_width=l[11]),o&32768&&(s.rtl=l[15]),o&16896967&&(s.$$scope={dirty:o,ctx:l}),e.$set(s)},i(l){t||(w(e.$$.fragment,l),t=!0)},o(l){j(e.$$.fragment,l),t=!1},d(l){Y(e,l)}}}function el(n){let e,t;return e=new fl({props:{Icon:he,label:n[8],float:!1,disable:n[9]===!1,show_label:n[14],rtl:n[15]}}),{c(){Z(e.$$.fragment)},l(l){q(e.$$.fragment,l)},m(l,o){K(e,l,o),t=!0},p(l,o){const s={};o&256&&(s.label=l[8]),o&512&&(s.disable=l[9]===!1),o&16384&&(s.show_label=l[14]),o&32768&&(s.rtl=l[15]),e.$set(s)},i(l){t||(w(e.$$.fragment,l),t=!0)},o(l){j(e.$$.fragment,l),t=!1},d(l){Y(e,l)}}}function sn(n){let e,t;return e=new ul({props:{size:"small",unpadded_box:!0,$$slots:{default:[an]},$$scope:{ctx:n}}}),{c(){Z(e.$$.fragment)},l(l){q(e.$$.fragment,l)},m(l,o){K(e,l,o),t=!0},p(l,o){const s={};o&16777216&&(s.$$scope={dirty:o,ctx:l}),e.$set(s)},i(l){t||(w(e.$$.fragment,l),t=!0)},o(l){j(e.$$.fragment,l),t=!1},d(l){Y(e,l)}}}function rn(n){let e,t,l;function o(i){n[22](i)}let s={selectable:n[12],show_legend:n[6],color_map:n[1]};return n[0]!==void 0&&(s.value=n[0]),e=new nn({props:s}),ke.push(()=>ve(e,"value",o)),e.$on("change",n[23]),{c(){Z(e.$$.fragment)},l(i){q(e.$$.fragment,i)},m(i,a){K(e,i,a),l=!0},p(i,a){const r={};a&4096&&(r.selectable=i[12]),a&64&&(r.show_legend=i[6]),a&2&&(r.color_map=i[1]),!t&&a&1&&(t=!0,r.value=i[0],pe(()=>t=!1)),e.$set(r)},i(i){l||(w(e.$$.fragment,i),l=!0)},o(i){j(e.$$.fragment,i),l=!1},d(i){Y(e,i)}}}function an(n){let e,t;return e=new he({}),{c(){Z(e.$$.fragment)},l(l){q(e.$$.fragment,l)},m(l,o){K(e,l,o),t=!0},i(l){t||(w(e.$$.fragment,l),t=!0)},o(l){j(e.$$.fragment,l),t=!1},d(l){Y(e,l)}}}function fn(n){let e,t,l,o,s,i,a;const r=[{autoscroll:n[2].autoscroll},n[16],{i18n:n[2].i18n}];let u={};for(let c=0;c<r.length;c+=1)u=tl(u,r[c]);e=new al({props:u}),e.$on("clear_status",n[21]);let f=n[8]&&n[14]&&el(n);const m=[rn,sn],p=[];function v(c,g){return c[0]?0:1}return o=v(n),s=p[o]=m[o](n),{c(){Z(e.$$.fragment),t=O(),f&&f.c(),l=O(),s.c(),i=R()},l(c){q(e.$$.fragment,c),t=D(c),f&&f.l(c),l=D(c),s.l(c),i=R()},m(c,g){K(e,c,g),E(c,t,g),f&&f.m(c,g),E(c,l,g),p[o].m(c,g),E(c,i,g),a=!0},p(c,g){const S=g&65540?ol(r,[g&4&&{autoscroll:c[2].autoscroll},g&65536&&il(c[16]),g&4&&{i18n:c[2].i18n}]):{};e.$set(S),c[8]&&c[14]?f?(f.p(c,g),g&16640&&w(f,1)):(f=el(c),f.c(),w(f,1),f.m(l.parentNode,l)):f&&(G(),j(f,1,1,()=>{f=null}),J());let H=o;o=v(c),o===H?p[o].p(c,g):(G(),j(p[H],1,1,()=>{p[H]=null}),J(),s=p[o],s?s.p(c,g):(s=p[o]=m[o](c),s.c()),w(s,1),s.m(i.parentNode,i))},i(c){a||(w(e.$$.fragment,c),w(f),w(s),a=!0)},o(c){j(e.$$.fragment,c),j(f),j(s),a=!1},d(c){c&&(d(t),d(l),d(i)),Y(e,c),f&&f.d(c),p[o].d(c)}}}function ll(n){let e,t;return e=new fl({props:{Icon:he,label:n[8],float:!1,disable:n[9]===!1,show_label:n[14],rtl:n[15]}}),{c(){Z(e.$$.fragment)},l(l){q(e.$$.fragment,l)},m(l,o){K(e,l,o),t=!0},p(l,o){const s={};o&256&&(s.label=l[8]),o&512&&(s.disable=l[9]===!1),o&16384&&(s.show_label=l[14]),o&32768&&(s.rtl=l[15]),e.$set(s)},i(l){t||(w(e.$$.fragment,l),t=!0)},o(l){j(e.$$.fragment,l),t=!1},d(l){Y(e,l)}}}function un(n){let e,t;return e=new ul({props:{$$slots:{default:[_n]},$$scope:{ctx:n}}}),{c(){Z(e.$$.fragment)},l(l){q(e.$$.fragment,l)},m(l,o){K(e,l,o),t=!0},p(l,o){const s={};o&16777216&&(s.$$scope={dirty:o,ctx:l}),e.$set(s)},i(l){t||(w(e.$$.fragment,l),t=!0)},o(l){j(e.$$.fragment,l),t=!1},d(l){Y(e,l)}}}function cn(n){let e,t;return e=new Kl({props:{selectable:n[12],value:n[0],show_legend:n[6],show_inline_category:n[7],color_map:n[1]}}),e.$on("select",n[20]),{c(){Z(e.$$.fragment)},l(l){q(e.$$.fragment,l)},m(l,o){K(e,l,o),t=!0},p(l,o){const s={};o&4096&&(s.selectable=l[12]),o&1&&(s.value=l[0]),o&64&&(s.show_legend=l[6]),o&128&&(s.show_inline_category=l[7]),o&2&&(s.color_map=l[1]),e.$set(s)},i(l){t||(w(e.$$.fragment,l),t=!0)},o(l){j(e.$$.fragment,l),t=!1},d(l){Y(e,l)}}}function _n(n){let e,t;return e=new he({}),{c(){Z(e.$$.fragment)},l(l){q(e.$$.fragment,l)},m(l,o){K(e,l,o),t=!0},i(l){t||(w(e.$$.fragment,l),t=!0)},o(l){j(e.$$.fragment,l),t=!1},d(l){Y(e,l)}}}function hn(n){let e,t,l,o,s,i,a;const r=[{autoscroll:n[2].autoscroll},{i18n:n[2].i18n},n[16]];let u={};for(let c=0;c<r.length;c+=1)u=tl(u,r[c]);e=new al({props:u}),e.$on("clear_status",n[19]);let f=n[8]&&n[14]&&ll(n);const m=[cn,un],p=[];function v(c,g){return c[0]?0:1}return o=v(n),s=p[o]=m[o](n),{c(){Z(e.$$.fragment),t=O(),f&&f.c(),l=O(),s.c(),i=R()},l(c){q(e.$$.fragment,c),t=D(c),f&&f.l(c),l=D(c),s.l(c),i=R()},m(c,g){K(e,c,g),E(c,t,g),f&&f.m(c,g),E(c,l,g),p[o].m(c,g),E(c,i,g),a=!0},p(c,g){const S=g&65540?ol(r,[g&4&&{autoscroll:c[2].autoscroll},g&4&&{i18n:c[2].i18n},g&65536&&il(c[16])]):{};e.$set(S),c[8]&&c[14]?f?(f.p(c,g),g&16640&&w(f,1)):(f=ll(c),f.c(),w(f,1),f.m(l.parentNode,l)):f&&(G(),j(f,1,1,()=>{f=null}),J());let H=o;o=v(c),o===H?p[o].p(c,g):(G(),j(p[H],1,1,()=>{p[H]=null}),J(),s=p[o],s?s.p(c,g):(s=p[o]=m[o](c),s.c()),w(s,1),s.m(i.parentNode,i))},i(c){a||(w(e.$$.fragment,c),w(f),w(s),a=!0)},o(c){j(e.$$.fragment,c),j(f),j(s),a=!1},d(c){c&&(d(t),d(l),d(i)),Y(e,c),f&&f.d(c),p[o].d(c)}}}function dn(n){let e,t,l,o;const s=[on,tn],i=[];function a(r,u){return r[13]?1:0}return e=a(n),t=i[e]=s[e](n),{c(){t.c(),l=R()},l(r){t.l(r),l=R()},m(r,u){i[e].m(r,u),E(r,l,u),o=!0},p(r,[u]){let f=e;e=a(r),e===f?i[e].p(r,u):(G(),j(i[f],1,1,()=>{i[f]=null}),J(),t=i[e],t?t.p(r,u):(t=i[e]=s[e](r),t.c()),w(t,1),t.m(l.parentNode,l))},i(r){o||(w(t),o=!0)},o(r){j(t),o=!1},d(r){r&&d(l),i[e].d(r)}}}function gn(n,e,t){let{gradio:l}=e,{elem_id:o=""}=e,{elem_classes:s=[]}=e,{visible:i=!0}=e,{value:a}=e,r,{show_legend:u}=e,{show_inline_category:f}=e,{color_map:m={}}=e,{label:p=l.i18n("highlighted_text.highlighted_text")}=e,{container:v=!0}=e,{scale:c=null}=e,{min_width:g=void 0}=e,{_selectable:S=!1}=e,{combine_adjacent:H=!1}=e,{interactive:I}=e,{show_label:k=!0}=e,{rtl:z=!1}=e,{loading_status:b}=e;const C=()=>l.dispatch("clear_status",b),P=({detail:y})=>l.dispatch("select",y),Q=()=>l.dispatch("clear_status",b);function te(y){a=y,t(0,a),t(17,H)}const de=()=>l.dispatch("change");return n.$$set=y=>{"gradio"in y&&t(2,l=y.gradio),"elem_id"in y&&t(3,o=y.elem_id),"elem_classes"in y&&t(4,s=y.elem_classes),"visible"in y&&t(5,i=y.visible),"value"in y&&t(0,a=y.value),"show_legend"in y&&t(6,u=y.show_legend),"show_inline_category"in y&&t(7,f=y.show_inline_category),"color_map"in y&&t(1,m=y.color_map),"label"in y&&t(8,p=y.label),"container"in y&&t(9,v=y.container),"scale"in y&&t(10,c=y.scale),"min_width"in y&&t(11,g=y.min_width),"_selectable"in y&&t(12,S=y._selectable),"combine_adjacent"in y&&t(17,H=y.combine_adjacent),"interactive"in y&&t(13,I=y.interactive),"show_label"in y&&t(14,k=y.show_label),"rtl"in y&&t(15,z=y.rtl),"loading_status"in y&&t(16,b=y.loading_status)},n.$$.update=()=>{n.$$.dirty&2&&!m&&Object.keys(m).length&&t(1,m),n.$$.dirty&131073&&a&&H&&t(0,a=_l(a)),n.$$.dirty&262149&&a!==r&&(t(18,r=a),l.dispatch("change"))},[a,m,l,o,s,i,u,f,p,v,c,g,S,I,k,z,b,H,r,C,P,Q,te,de]}class En extends oe{constructor(e){super(),ie(this,e,gn,dn,se,{gradio:2,elem_id:3,elem_classes:4,visible:5,value:0,show_legend:6,show_inline_category:7,color_map:1,label:8,container:9,scale:10,min_width:11,_selectable:12,combine_adjacent:17,interactive:13,show_label:14,rtl:15,loading_status:16})}}export{nn as BaseInteractiveHighlightedText,Kl as BaseStaticHighlightedText,En as default};
//# sourceMappingURL=Index.D56cqZJI.js.map
