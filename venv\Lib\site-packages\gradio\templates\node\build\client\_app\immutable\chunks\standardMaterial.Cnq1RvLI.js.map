{"version": 3, "mappings": ";iaAsBA,MAAMA,EAA4B,CAAE,OAAQ,KAAM,QAAS,IAAI,EAExD,MAAMC,WAAgCC,EAAgB,CAKzD,YAAYC,EAAoB,CAC5B,MAAMA,CAAkB,EACxB,KAAK,QAAU,GACf,KAAK,QAAU,GACf,KAAK,QAAU,GACf,KAAK,QAAU,GACf,KAAK,QAAU,GACf,KAAK,QAAU,GACf,KAAK,QAAU,GACf,KAAK,gBAAkB,EACvB,KAAK,+BAAiC,GACtC,KAAK,QAAU,GACf,KAAK,gBAAkB,EACvB,KAAK,QAAU,GACf,KAAK,gBAAkB,EACvB,KAAK,WAAa,GAClB,KAAK,WAAa,GAClB,KAAK,SAAW,GAChB,KAAK,iBAAmB,EACxB,KAAK,SAAW,GAChB,KAAK,iBAAmB,EACxB,KAAK,KAAO,GACZ,KAAK,aAAe,EACpB,KAAK,SAAW,GAChB,KAAK,aAAe,GACpB,KAAK,kBAAoB,GACzB,KAAK,kBAAoB,GACzB,KAAK,UAAY,GACjB,KAAK,WAAa,GAClB,KAAK,WAAa,GAClB,KAAK,WAAa,GAClB,KAAK,WAAa,GAClB,KAAK,WAAa,GAClB,KAAK,UAAY,GACjB,KAAK,aAAe,GACpB,KAAK,iBAAmB,GACxB,KAAK,UAAY,GACjB,KAAK,IAAM,GACX,KAAK,aAAe,GACpB,KAAK,eAAiB,GACtB,KAAK,eAAiB,GACtB,KAAK,kBAAoB,GACzB,KAAK,kBAAoB,GACzB,KAAK,gBAAkB,GACvB,KAAK,QAAU,GACf,KAAK,OAAS,GACd,KAAK,QAAU,GACf,KAAK,IAAM,GACX,KAAK,IAAM,GACX,KAAK,IAAM,GACX,KAAK,IAAM,GACX,KAAK,IAAM,GACX,KAAK,IAAM,GACX,KAAK,YAAc,GACnB,KAAK,YAAc,GACnB,KAAK,qBAAuB,EAC5B,KAAK,aAAe,EACpB,KAAK,YAAc,GACnB,KAAK,uBAAyB,GAC9B,KAAK,UAAY,GACjB,KAAK,eAAiB,GACtB,KAAK,eAAiB,GACtB,KAAK,WAAa,GAClB,KAAK,UAAY,GACjB,KAAK,uBAAyB,GAC9B,KAAK,wBAA0B,GAC/B,KAAK,8BAAgC,GACrC,KAAK,SAAW,GAChB,KAAK,iBAAmB,EACxB,KAAK,sBAAwB,GAC7B,KAAK,uBAAyB,GAC9B,KAAK,iBAAmB,GACxB,KAAK,wBAA0B,GAC/B,KAAK,qBAAuB,GAC5B,KAAK,oBAAsB,GAC3B,KAAK,8BAAgC,GACrC,KAAK,8BAAgC,GACrC,KAAK,yBAA2B,GAChC,KAAK,qBAAuB,GAC5B,KAAK,uBAAyB,GAC9B,KAAK,8BAAgC,GACrC,KAAK,oCAAsC,GAC3C,KAAK,4CAA8C,GACnD,KAAK,wBAA0B,GAC/B,KAAK,eAAiB,GACtB,KAAK,iBAAmB,GACxB,KAAK,WAAa,GAClB,KAAK,iBAAmB,GACxB,KAAK,oBAAsB,GAC3B,KAAK,iBAAmB,GACxB,KAAK,YAAc,GACnB,KAAK,aAAe,GACpB,KAAK,sBAAwB,GAC7B,KAAK,oBAAsB,GAC3B,KAAK,qBAAuB,GAC5B,KAAK,gBAAkB,GACvB,KAAK,iBAAmB,GACxB,KAAK,mBAAqB,GAC1B,KAAK,gCAAkC,GACvC,KAAK,8BAAgC,GACrC,KAAK,+BAAiC,GACtC,KAAK,0BAA4B,GACjC,KAAK,2BAA6B,GAClC,KAAK,6BAA+B,GACpC,KAAK,sBAAwB,EAC7B,KAAK,qBAAuB,GAC5B,KAAK,kBAAoB,GACzB,KAAK,iBAAmB,GACxB,KAAK,oCAAsC,GAC3C,KAAK,WAAa,GAClB,KAAK,QAAU,GACf,KAAK,cAAgB,GACrB,KAAK,oBAAsB,GAC3B,KAAK,mBAAqB,GAC1B,KAAK,yBAA2B,GAChC,KAAK,eAAiB,GACtB,KAAK,qBAAuB,GAC5B,KAAK,oBAAsB,GAC3B,KAAK,0BAA4B,GACjC,KAAK,cAAgB,GACrB,KAAK,oBAAsB,GAC3B,KAAK,0BAA4B,GACjC,KAAK,gCAAkC,GACvC,KAAK,eAAiB,GACtB,KAAK,qBAAuB,GAC5B,KAAK,0BAA4B,GACjC,KAAK,qBAAuB,GAC5B,KAAK,2BAA6B,GAClC,KAAK,iBAAmB,GACxB,KAAK,uBAAyB,GAC9B,KAAK,uBAAyB,GAC9B,KAAK,6BAA+B,GACpC,KAAK,iBAAmB,GACxB,KAAK,uBAAyB,GAC9B,KAAK,wBAA0B,GAC/B,KAAK,8BAAgC,GACrC,KAAK,qBAAuB,GAC5B,KAAK,2BAA6B,GAClC,KAAK,gBAAkB,EACvB,KAAK,aAAe,GACpB,KAAK,eAAiB,GACtB,KAAK,eAAiB,GACtB,KAAK,gBAAkB,GACvB,KAAK,SAAW,GAChB,KAAK,0BAA4B,GACjC,KAAK,wBAA0B,GAC/B,KAAK,YAAc,EACnB,KAAK,SAAW,GAChB,KAAK,YAAc,GACnB,KAAK,aAAe,GACpB,KAAK,eAAiB,GACtB,KAAK,oBAAsB,GAC3B,KAAK,gBAAkB,GACvB,KAAK,OAAS,GACd,KAAK,2BAA6B,GAClC,KAAK,oBAAsB,GAC3B,KAAK,UAAY,GACjB,KAAK,+BAAiC,GACtC,KAAK,sCAAwC,GAC7C,KAAK,oBAAsB,GAC3B,KAAK,mBAAqB,GAC1B,KAAK,mBAAqB,GAK1B,KAAK,qBAAuB,GAK5B,KAAK,qBAAuB,GAC5B,KAAK,SAAW,GAChB,KAAK,mBAAqB,GAC1B,KAAK,QAAO,CACf,CACD,kBAAkBC,EAAc,CAC5B,MAAMC,EAAQ,CACV,sBACA,yBACA,uBACA,2BACA,2BACA,uBACA,0BACA,gCACA,sCACA,6CACZ,EACQ,UAAWC,KAAQD,EACf,KAAKC,CAAI,EAAIA,IAASF,CAE7B,CACL,CAMO,MAAMG,UAAyBC,CAAa,CAI/C,IAAI,8BAA+B,CAC/B,OAAO,KAAK,6BACf,CAMD,IAAI,6BAA6BC,EAAO,CACpC,KAAK,oCAAoCA,CAAK,EAE9C,KAAK,iCAAgC,CACxC,CAKD,oCAAoCC,EAAe,CAC3CA,IAAkB,KAAK,gCAIvB,KAAK,+BAAiC,KAAK,0BAC3C,KAAK,8BAA8B,mBAAmB,OAAO,KAAK,wBAAwB,EAGzFA,EAID,KAAK,8BAAgCA,EAHrC,KAAK,8BAAgC,KAAK,SAAQ,EAAG,6BAMrD,KAAK,gCACL,KAAK,yBAA2B,KAAK,8BAA8B,mBAAmB,IAAI,IAAM,CAC5F,KAAK,wCAAuC,CAC5D,CAAa,GAER,CAID,IAAI,kBAAmB,CACnB,MAAO,CAAC,KAAK,iBAChB,CAID,IAAI,0BAA2B,CAC3B,OAAO,KAAK,6BAA6B,kBAC5C,CAID,IAAI,yBAAyBD,EAAO,CAChC,KAAK,6BAA6B,mBAAqBA,CAC1D,CAID,IAAI,2BAA4B,CAC5B,OAAO,KAAK,6BAA6B,mBAC5C,CAID,IAAI,0BAA0BA,EAAO,CACjC,KAAK,6BAA6B,oBAAsBA,CAC3D,CAID,IAAI,0BAA2B,CAC3B,OAAO,KAAK,8BAA8B,kBAC7C,CAID,IAAI,yBAAyBA,EAAO,CAChC,KAAK,8BAA8B,mBAAqBA,CAC3D,CAMD,IAAI,gBAAiB,CACjB,OAAO,KAAK,8BAA8B,QAC7C,CAMD,IAAI,eAAeA,EAAO,CACtB,KAAK,8BAA8B,SAAWA,CACjD,CAID,IAAI,gBAAiB,CACjB,OAAO,KAAK,8BAA8B,QAC7C,CAID,IAAI,eAAeA,EAAO,CACtB,KAAK,8BAA8B,SAAWA,CACjD,CAID,IAAI,2BAA4B,CAC5B,OAAO,KAAK,8BAA8B,mBAC7C,CAID,IAAI,0BAA0BA,EAAO,CACjC,KAAK,8BAA8B,oBAAsBA,CAC5D,CAOD,IAAI,mBAAoB,CACpB,OAAO,KAAK,8BAA8B,WAC7C,CAOD,IAAI,kBAAkBA,EAAO,CACzB,KAAK,8BAA8B,YAAcA,CACpD,CAID,IAAI,gBAAiB,CACjB,MAAO,EACV,CAUD,YAAYE,EAAMC,EAAOC,EAAY,GAAO,CACxC,MAAMF,EAAMC,EAAO,OAAWC,GAAaN,EAAiB,SAAS,EACrE,KAAK,gBAAkB,KACvB,KAAK,gBAAkB,KACvB,KAAK,gBAAkB,KACvB,KAAK,mBAAqB,KAC1B,KAAK,iBAAmB,KACxB,KAAK,iBAAmB,KACxB,KAAK,aAAe,KACpB,KAAK,iBAAmB,KACxB,KAAK,mBAAqB,KAK1B,KAAK,aAAe,IAAIO,EAAO,EAAG,EAAG,CAAC,EAItC,KAAK,aAAe,IAAIA,EAAO,EAAG,EAAG,CAAC,EAItC,KAAK,cAAgB,IAAIA,EAAO,EAAG,EAAG,CAAC,EAKvC,KAAK,cAAgB,IAAIA,EAAO,EAAG,EAAG,CAAC,EAMvC,KAAK,cAAgB,GACrB,KAAK,4BAA8B,GACnC,KAAK,2BAA6B,GAClC,KAAK,yBAA2B,GAChC,KAAK,sBAAwB,GAC7B,KAAK,wBAA0B,GAC/B,KAAK,iBAAmB,GACxB,KAAK,yBAA2B,GAChC,KAAK,aAAe,GACpB,KAAK,sBAAwB,GAI7B,KAAK,kBAAoB,IACzB,KAAK,WAAa,EAKlB,KAAK,kBAAoB,IAMzB,KAAK,kBAAoB,GAIzB,KAAK,YAAc,GACnB,KAAK,wBAA0B,GAC/B,KAAK,kCAAoC,GACzC,KAAK,mCAAqC,GAC1C,KAAK,uBAAyB,EAC9B,KAAK,kBAAoB,GACzB,KAAK,kBAAoB,GACzB,KAAK,kBAAoB,GACzB,KAAK,6BAA+B,GACpC,KAAK,eAAiB,GACtB,KAAK,eAAiB,IAAIC,EAAW,EAAE,EACvC,KAAK,oBAAsB,IAAID,EAAO,EAAG,EAAG,CAAC,EAC7C,KAAK,8BAAgC,GACrC,KAAK,UAAY,IAAIE,EAAuB,IAAI,EAEhD,KAAK,oCAAoC,IAAI,EAC7C,KAAK,qBAAuB,IAAIC,EAChC,KAAK,wBAA0B,KAC3B,KAAK,eAAe,QAChBV,EAAiB,0BAA4B,KAAK,oBAAsB,KAAK,mBAAmB,gBAChG,KAAK,eAAe,KAAK,KAAK,kBAAkB,EAEhDA,EAAiB,0BAA4B,KAAK,oBAAsB,KAAK,mBAAmB,gBAChG,KAAK,eAAe,KAAK,KAAK,kBAAkB,EAEpD,KAAK,WAAW,cAAgB,KAAK,eACrC,KAAK,6CAA6C,KAAK,UAAU,EAC1D,KAAK,eAEnB,CAID,IAAI,yBAA0B,CAI1B,OAHIA,EAAiB,0BAA4B,KAAK,oBAAsB,KAAK,mBAAmB,gBAGhGA,EAAiB,0BAA4B,KAAK,oBAAsB,KAAK,mBAAmB,eACzF,GAEJ,KAAK,6BACf,CAMD,cAAe,CACX,MAAO,kBACV,CAKD,mBAAoB,CAChB,OAAI,KAAK,qBACE,KAAK,yBAEZ,KAAK,sBACE,GAEH,KAAK,MAAQ,GACjB,KAAK,iBAAmB,MACxB,KAAK,kCAAmC,GACvC,KAAK,2BAA6B,KAAK,0BAA0B,SACzE,CAKD,kBAAmB,CACf,OAAI,KAAK,qBACE,KAAK,wBAET,KAAK,qBAAuB,KAAK,mBAAqB,MAAQ,KAAK,oBAAsBW,EAAS,mBAC5G,CAID,mCAAoC,CAChC,OAAO,KAAK,iBAAmB,MAAQ,KAAK,gBAAgB,UAAY,KAAK,6BAA+B,KAAK,oBAAsBA,EAAS,eACnJ,CAID,kBAAmB,CACf,OAAQ,KAAK,iBAAmB,MAAQ,KAAK,gBAAgB,UAAa,KAAK,iBAAmB,IACrG,CAKD,qBAAsB,CAClB,OAAO,KAAK,eACf,CASD,kBAAkBC,EAAMC,EAASC,EAAe,GAAO,CAC9C,KAAK,2BACN,KAAK,mBAAkB,EAE3B,MAAMC,EAAcF,EAAQ,aAC5B,GAAIE,EAAY,QAAU,KAAK,UACvBA,EAAY,qBAAuBA,EAAY,+BAAiCD,EAChF,MAAO,GAGVD,EAAQ,kBACT,KAAK,4BAA4B,EAA4C,KAAK,UAAU,EAC5FA,EAAQ,gBAAkB,IAAInB,GAAwB,KAAK,WAAW,WAAW,GAErF,MAAMW,EAAQ,KAAK,WACbW,EAAUH,EAAQ,gBACxB,GAAI,KAAK,mBAAmBA,CAAO,EAC/B,MAAO,GAEX,MAAMI,EAASZ,EAAM,YAErBW,EAAQ,aAAeE,EAAwBb,EAAOO,EAAMI,EAAS,GAAM,KAAK,uBAAwB,KAAK,gBAAgB,EAE7HG,EAA2Bd,EAAOW,CAAO,EAEzC,MAAMI,EAAM,KAAK,yBAAyBR,CAAI,GAAK,KAAK,SAAU,EAAC,gCAMnE,GALAS,EAAyBhB,EAAOW,EAAS,KAAK,gBAAkB,CAACI,CAAG,EAEpEE,EAAqBjB,EAAOW,EAASI,CAAG,EACxCG,EAAgC,eAAeN,EAAO,oBAAqBL,EAAMI,CAAO,EAEpFA,EAAQ,kBAAmB,CAC3B,KAAK,WAAW,wBAA0B,GAC1C,KAAK,4CAA4C,KAAK,UAAU,EAChE,KAAK,8BAAgC,KAAK,WAAW,wBACrDA,EAAQ,SAAW,GACnB,QAASQ,EAAI,EAAGA,GAAK,EAAG,EAAEA,EACtBR,EAAQ,SAAWQ,CAAC,EAAI,GAE5B,GAAInB,EAAM,gBAAiB,CAQvB,GAPAW,EAAQ,gBAAkB,EAC1BA,EAAQ,aAAe,EACvBA,EAAQ,gBAAkB,EAC1BA,EAAQ,gBAAkB,EAC1BA,EAAQ,iBAAmB,EAC3BA,EAAQ,iBAAmB,EAC3BA,EAAQ,iBAAmB,EACvB,KAAK,iBAAmBhB,EAAiB,sBACzC,GAAK,KAAK,gBAAgB,uBAItByB,EAA0B,KAAK,gBAAiBT,EAAS,SAAS,MAHlE,OAAO,QAOXA,EAAQ,QAAU,GAEtB,GAAI,KAAK,iBAAmBhB,EAAiB,sBACzC,GAAK,KAAK,gBAAgB,uBAItByB,EAA0B,KAAK,gBAAiBT,EAAS,SAAS,MAHlE,OAAO,QAOXA,EAAQ,QAAU,GAEtB,GAAI,KAAK,iBAAmBhB,EAAiB,sBACzC,GAAK,KAAK,gBAAgB,uBAItByB,EAA0B,KAAK,gBAAiBT,EAAS,SAAS,EAClEA,EAAQ,WAAa,KAAK,gBAAgB,oBAJ1C,OAAO,QAQXA,EAAQ,QAAU,GAEtB,GAAI,KAAK,oBAAsBhB,EAAiB,yBAC5C,GAAK,KAAK,mBAAmB,uBAGxB,CAUD,OATAgB,EAAQ,aAAe,GACvBA,EAAQ,WAAa,GACrBA,EAAQ,UAAY,KAAK,WAAa,EACtCA,EAAQ,oBAAsB,KAAK,wBACnCA,EAAQ,eAAiB,KAAK,mBAAmB,kBAAoBU,EAAQ,cAC7EV,EAAQ,iBAAmB,KAAK,mBAAmB,OACnDA,EAAQ,wBACJA,EAAQ,kBAAoB,KAAK,SAAU,EAAC,qBAAuB,CAAC,KAAK,mBAAmB,QAAU,KAAK,mBAAmB,QAClIA,EAAQ,eAAiB,KAAK,mBAAmB,OACzC,KAAK,mBAAmB,gBAAe,CAC3C,KAAKU,EAAQ,cACTV,EAAQ,kBAAkB,wBAAwB,EAClD,MACJ,KAAKU,EAAQ,YACTV,EAAQ,kBAAkB,sBAAsB,EAChD,MACJ,KAAKU,EAAQ,gBACTV,EAAQ,kBAAkB,0BAA0B,EACpD,MACJ,KAAKU,EAAQ,YACTV,EAAQ,kBAAkB,sBAAsB,EAChD,MACJ,KAAKU,EAAQ,eACTV,EAAQ,kBAAkB,yBAAyB,EACnD,MACJ,KAAKU,EAAQ,qBACTV,EAAQ,kBAAkB,+BAA+B,EACzD,MACJ,KAAKU,EAAQ,2BACTV,EAAQ,kBAAkB,qCAAqC,EAC/D,MACJ,KAAKU,EAAQ,oCACTV,EAAQ,kBAAkB,6CAA6C,EACvE,MACJ,KAAKU,EAAQ,WACb,KAAKA,EAAQ,cACb,QACIV,EAAQ,kBAAkB,qBAAqB,EAC/C,KACP,CACDA,EAAQ,8BAAgC,OAAK,mBAAmB,eACnE,KA5CG,OAAO,QA+CXA,EAAQ,WAAa,GACrBA,EAAQ,wBAA0B,GAEtC,GAAI,KAAK,kBAAoBhB,EAAiB,uBAC1C,GAAK,KAAK,iBAAiB,uBAIvByB,EAA0B,KAAK,iBAAkBT,EAAS,UAAU,MAHpE,OAAO,QAOXA,EAAQ,SAAW,GAEvB,GAAI,KAAK,kBAAoBhB,EAAiB,uBAC1C,GAAK,KAAK,iBAAiB,uBAIvByB,EAA0B,KAAK,iBAAkBT,EAAS,UAAU,EACpEA,EAAQ,uBAAyB,KAAK,wBACtCA,EAAQ,aAAe,KAAK,iBAAiB,WAL7C,OAAO,QASXA,EAAQ,SAAW,GAEvB,GAAI,KAAK,kBAAoBhB,EAAiB,uBAC1C,GAAK,KAAK,iBAAiB,uBAIvByB,EAA0B,KAAK,iBAAkBT,EAAS,UAAU,EACpEA,EAAQ,WAAa,KAAK,uCAJ1B,OAAO,QAQXA,EAAQ,SAAW,GAEvB,GAAIX,EAAM,UAAW,EAAC,QAAS,EAAC,qBAAuB,KAAK,cAAgBL,EAAiB,mBAAoB,CAE7G,GAAK,KAAK,aAAa,UAInByB,EAA0B,KAAK,aAAcT,EAAS,MAAM,EAC5DA,EAAQ,SAAW,KAAK,aACxBA,EAAQ,aAAeX,EAAM,qBAC7BW,EAAQ,kBAAoB,KAAK,0BANjC,OAAO,GAQXA,EAAQ,sBAAwB,KAAK,wBACxC,MAEGA,EAAQ,KAAO,GACfA,EAAQ,SAAW,GACnBA,EAAQ,aAAe,GACvBA,EAAQ,kBAAoB,GAEhC,GAAI,KAAK,oBAAsBhB,EAAiB,yBAC5C,GAAK,KAAK,mBAAmB,uBAIzBgB,EAAQ,SAAW,GACnBA,EAAQ,WAAa,GACrBA,EAAQ,iBAAmB,KAAK,mBAAmB,OACnDA,EAAQ,eAAiB,KAAK,mBAAmB,OACjDA,EAAQ,8BAAgC,OAAK,mBAAmB,oBAPhE,OAAO,QAWXA,EAAQ,WAAa,GAEzBA,EAAQ,iBAAmB,CAAC,KAAK,kBAAoB,KAAK,iBAC7D,MAEGA,EAAQ,QAAU,GAClBA,EAAQ,QAAU,GAClBA,EAAQ,QAAU,GAClBA,EAAQ,WAAa,GACrBA,EAAQ,SAAW,GACnBA,EAAQ,SAAW,GACnBA,EAAQ,KAAO,GACfA,EAAQ,WAAa,GAEzBA,EAAQ,iBAAmB,KAAK,oCAChCA,EAAQ,uBAAyB,KAAK,2BACtCA,EAAQ,wBAA0B,KAAK,yBACvCA,EAAQ,kBAAoB,KAAK,sBACjCA,EAAQ,iBAAmB,KAAK,YAAc,GAAK,KAAK,YAAc,EACtEA,EAAQ,oCAAsC,KAAK,mBAAqB,KACxEA,EAAQ,WAAa,KAAK,mBAAqB,MAAQ,KAAK,yBAAyBJ,CAAI,CAC5F,CAKD,GAJA,KAAK,WAAW,kBAAoB,GACpC,KAAK,WAAW,QAAUI,EAC1B,KAAK,WAAW,QAAUH,EAC1B,KAAK,sCAAsC,KAAK,UAAU,EACtD,CAAC,KAAK,WAAW,kBACjB,MAAO,GAEX,GAAIG,EAAQ,0BAA4B,KAAK,8BAA+B,CACxE,GAAI,CAAC,KAAK,8BAA8B,UACpC,MAAO,GAEX,KAAK,8BAA8B,eAAeA,CAAO,EACzDA,EAAQ,qBAAuB,KAAK,mBAAqB,MAAQ,CAAC,KAAK,kBAAkB,WACzFA,EAAQ,qBAAuB,KAAK,mBAAqB,MAAQ,CAAC,KAAK,kBAAkB,UAC5F,CAwBD,GAvBIA,EAAQ,mBACJhB,EAAiB,gBAEb,KAAK,2BACL,KAAK,2BACL,KAAK,4BACL,KAAK,8BACL,KAAK,gCACLgB,EAAQ,eAAiB,KAAK,2BAA6B,KAAK,0BAA0B,UAC1FA,EAAQ,eAAiB,KAAK,2BAA6B,KAAK,0BAA0B,UAC1FA,EAAQ,kBAAoB,KAAK,8BAAgC,KAAK,6BAA6B,UACnGA,EAAQ,8BAAgC,KAAK,kCAC7CA,EAAQ,kBAAoB,KAAK,8BAAgC,KAAK,6BAA6B,UACnGA,EAAQ,gBAAkB,KAAK,4BAA8B,KAAK,2BAA2B,UAC7FA,EAAQ,aAAe,GACvBA,EAAQ,QAAU,IAItBA,EAAQ,QAAU,IAItBA,EAAQ,eACR,QAASW,EAAQ,EAAGA,EAAQf,EAAK,aAAa,OAAQe,IAClD,GAAI,CAACf,EAAK,aAAae,CAAK,EAAE,SAAQ,EAClC,MAAO,GAKnBC,EAAsBhB,EAAMP,EAAO,KAAK,qBAAsB,KAAK,YAAa,KAAK,WAAY,KAAK,wBAAwBO,CAAI,EAAGI,EAAS,KAAK,4BAA4B,EAE/Ka,EAAkCxB,EAAOY,EAAQ,KAAMD,EAASF,EAAc,KAAMD,EAAQ,iBAAkB,EAAC,gBAAgB,EAE/H,KAAK,WAAW,QAAUG,EAC1B,KAAK,WAAW,KAAOJ,EACvB,KAAK,mDAAmD,KAAK,UAAU,EAEvEkB,GAA4BlB,EAAMI,EAAS,GAAM,GAAM,EAAI,EAE3D,KAAK,mCAAmC,KAAK,UAAU,EAEvD,IAAIe,EAA6B,GACjC,GAAIf,EAAQ,QAAS,CACjB,MAAMgB,EAAgBhB,EAAQ,mBAC9BA,EAAQ,gBAAe,EAEvB,MAAMiB,EAAY,IAAIC,GAClBlB,EAAQ,YACRiB,EAAU,YAAY,EAAG,YAAY,EAErCjB,EAAQ,UACRiB,EAAU,YAAY,EAAG,UAAU,EAEnCjB,EAAQ,MACRiB,EAAU,YAAY,EAAG,MAAM,EAE/BjB,EAAQ,UACRiB,EAAU,YAAY,EAAG,UAAU,EAEnCjB,EAAQ,cACRiB,EAAU,YAAY,EAAG,cAAc,EAEvCjB,EAAQ,mBACRiB,EAAU,YAAY,EAAG,mBAAmB,EAE5CjB,EAAQ,mBACRiB,EAAU,YAAY,EAAG,mBAAmB,EAE5CjB,EAAQ,KACRiB,EAAU,YAAY,EAAG,KAAK,EAE9BjB,EAAQ,WACRiB,EAAU,YAAY,EAAG,WAAW,EAEpCjB,EAAQ,kBACRiB,EAAU,YAAY,EAAG,kBAAkB,EAE/CE,GAA0BnB,EAASiB,EAAW,KAAK,sBAAsB,EACrEjB,EAAQ,cACRiB,EAAU,YAAY,EAAG,cAAc,EAEvCjB,EAAQ,gBACRiB,EAAU,YAAY,EAAG,gBAAgB,EAEzCjB,EAAQ,gBACRiB,EAAU,YAAY,EAAG,gBAAgB,EAEzCjB,EAAQ,mBACRiB,EAAU,YAAY,EAAG,mBAAmB,EAE5CjB,EAAQ,iBACRiB,EAAU,YAAY,EAAG,iBAAiB,EAE1CjB,EAAQ,SACRiB,EAAU,YAAY,EAAG,SAAS,EAElCjB,EAAQ,WACRiB,EAAU,YAAY,EAAG,WAAW,EAGxC,MAAMG,EAAU,CAACC,EAAa,YAAY,EACtCrB,EAAQ,QACRoB,EAAQ,KAAKC,EAAa,UAAU,EAEpCrB,EAAQ,SACRoB,EAAQ,KAAKC,EAAa,WAAW,EAEzC,QAASb,EAAI,EAAGA,GAAK,EAAG,EAAEA,EAClBR,EAAQ,KAAOQ,CAAC,GAChBY,EAAQ,KAAK,KAAKZ,IAAM,EAAI,GAAKA,CAAC,EAAE,EAGxCR,EAAQ,aACRoB,EAAQ,KAAKC,EAAa,SAAS,EAEvCC,GAA0BF,EAASxB,EAAMI,EAASiB,CAAS,EAC3DM,GAA8BH,EAASpB,CAAO,EAC9CwB,GAAiCJ,EAASxB,EAAMI,CAAO,EACvDyB,GAAyCL,EAASxB,EAAMI,CAAO,EAC/D,IAAI0B,EAAa,UACjB,MAAMC,EAAW,CACb,QACA,OACA,iBACA,eACA,cACA,gBACA,gBACA,iBACA,iBACA,aACA,YACA,YACA,YACA,gBACA,gBACA,gBACA,mBACA,iBACA,iBACA,aACA,iBACA,mBACA,SACA,gBACA,gBACA,gBACA,mBACA,iBACA,iBACA,aACA,eACA,iBACA,mBACA,mBACA,oBACA,eACA,sBACA,uBACA,oBACA,qBACA,sBACA,uBACA,sBACA,kBACA,sBACA,kBACA,2BACA,sBACA,cACA,mBACA,yBACA,2BAChB,EACkBC,EAAW,CACb,iBACA,iBACA,iBACA,wBACA,sBACA,kBACA,kBACA,cACA,kBACA,wBACA,sBACA,cACA,eACA,kBACA,uBACA,wBACA,uBAChB,EACkBC,EAAiB,CAAC,WAAY,QAAS,MAAM,EAC7CC,EAAkB,CAAE,sBAAuB,KAAK,uBAAwB,4BAA6B9B,EAAQ,uBACnH,KAAK,WAAW,UAAYiB,EAC5B,KAAK,WAAW,aAAe,EAC/B,KAAK,WAAW,QAAUjB,EAC1B,KAAK,WAAW,SAAW2B,EAC3B,KAAK,WAAW,WAAaP,EAC7B,KAAK,WAAW,SAAWQ,EAC3B,KAAK,WAAW,oBAAsBC,EACtC,KAAK,WAAW,WAAa,OAC7B,KAAK,WAAW,KAAOjC,EACvB,KAAK,WAAW,gBAAkBkC,EAClC,KAAK,4BAA4B,IAA6C,KAAK,UAAU,EAC7FvB,EAAgC,uBAAuBoB,EAAUC,CAAQ,EACzElC,EAAqB,YAAYiC,CAAQ,EAErCI,IACAA,EAA6B,gBAAgBJ,EAAU3B,CAAO,EAC9D+B,EAA6B,gBAAgBH,EAAU5B,CAAO,GAElEgC,GAA+B,CAC3B,cAAeL,EACf,oBAAqBE,EACrB,SAAUD,EACV,QAAS5B,EACT,sBAAuB,KAAK,sBAC5C,CAAa,EACDiC,GAAqBN,CAAQ,EAC7B,MAAMO,EAAc,GAChB,KAAK,0BACLR,EAAa,KAAK,wBAAwBA,EAAYC,EAAUE,EAAgBD,EAAU5B,EAASoB,EAASc,CAAW,GAE3H,MAAMC,EAAOnC,EAAQ,WACfoC,EAAiBvC,EAAQ,OAC/B,IAAIwC,EAAShD,EAAM,UAAS,EAAG,aAAaqC,EAAY,CACpD,WAAYN,EACZ,cAAeO,EACf,oBAAqBE,EACrB,SAAUD,EACV,QAASO,EACT,UAAWlB,EACX,WAAY,KAAK,WACjB,QAAS,KAAK,QACd,gBAAAa,EACA,iBAAkBI,EAAY,iBAC9B,yBAA0B,KAAK,WAAW,WAC1C,YAAalC,EAAQ,QACrB,eAAgB,KAAK,gBACrB,0BAA2B,KAAK,eAC1B,OACA,SAAY,CACN,KAAK,kBAAoB,EACzB,MAAM,QAAQ,IAAI,CAAAsC,EAAA,IAAC,OAAO,8BAAkC,6DAAGA,EAAA,WAAO,gCAAoC,EAAC,kEAAC,EAG5G,MAAM,QAAQ,IAAI,CAAAA,EAAA,IAAC,OAAO,8BAA8B,+DAAGA,EAAA,WAAO,gCAAgC,EAAC,6DAAC,EAExG,KAAK,eAAiB,EACzB,CACR,EAAErC,CAAM,EAET,GADA,KAAK,WAAW,WAAa,OACzBoC,EAOA,GANI,KAAK,6BACL5D,EAA0B,OAAS4D,EACnC5D,EAA0B,QAAUoB,EACpC,KAAK,2BAA2B,gBAAgBpB,CAAyB,GAGzE,KAAK,wBAA0B2D,GAAkB,CAACC,EAAO,QAAO,GAIhE,GAHAA,EAASD,EACTpC,EAAQ,kBAAiB,EACzBe,EAA6B,KAAK,SAC9BC,EAEA,OAAAhB,EAAQ,mBAAqB,GACtB,QAIXX,EAAM,oBAAmB,EACzBQ,EAAQ,UAAUwC,EAAQrC,EAAS,KAAK,gBAAgB,CAGnE,CACD,MAAI,CAACH,EAAQ,QAAU,CAACA,EAAQ,OAAO,UAC5B,IAEXG,EAAQ,UAAYX,EAAM,cAC1BU,EAAY,oBAAsB,CAAAgB,EAClChB,EAAY,6BAA+BD,EAC3C,KAAK,+BAA8B,EAC5B,GACV,CAKD,oBAAqB,CAEjB,MAAMyC,EAAM,KAAK,eACjBA,EAAI,WAAW,mBAAoB,CAAC,EACpCA,EAAI,WAAW,oBAAqB,CAAC,EACrCA,EAAI,WAAW,eAAgB,CAAC,EAChCA,EAAI,WAAW,sBAAuB,CAAC,EACvCA,EAAI,WAAW,uBAAwB,CAAC,EACxCA,EAAI,WAAW,sBAAuB,CAAC,EACvCA,EAAI,WAAW,uBAAwB,CAAC,EACxCA,EAAI,WAAW,oBAAqB,CAAC,EACrCA,EAAI,WAAW,qBAAsB,CAAC,EACtCA,EAAI,WAAW,gBAAiB,CAAC,EACjCA,EAAI,WAAW,gBAAiB,CAAC,EACjCA,EAAI,WAAW,gBAAiB,CAAC,EACjCA,EAAI,WAAW,mBAAoB,CAAC,EACpCA,EAAI,WAAW,sBAAuB,CAAC,EACvCA,EAAI,WAAW,kBAAmB,CAAC,EACnCA,EAAI,WAAW,iBAAkB,CAAC,EAClCA,EAAI,WAAW,iBAAkB,CAAC,EAClCA,EAAI,WAAW,iBAAkB,CAAC,EAClCA,EAAI,WAAW,aAAc,CAAC,EAC9BA,EAAI,WAAW,gBAAiB,EAAE,EAClCA,EAAI,WAAW,gBAAiB,EAAE,EAClCA,EAAI,WAAW,gBAAiB,EAAE,EAClCA,EAAI,WAAW,mBAAoB,EAAE,EACrCA,EAAI,WAAW,iBAAkB,EAAE,EACnCA,EAAI,WAAW,iBAAkB,EAAE,EACnCA,EAAI,WAAW,iBAAkB,EAAE,EACnCA,EAAI,WAAW,aAAc,EAAE,EAC/BA,EAAI,WAAW,sBAAuB,CAAC,EACvCA,EAAI,WAAW,YAAa,CAAC,EAC7BA,EAAI,WAAW,cAAe,CAAC,EAC/BA,EAAI,WAAW,mBAAoB,EAAE,EACrCA,EAAI,WAAW,mBAAoB,CAAC,EACpCA,EAAI,WAAW,sBAAuB,CAAC,EACvCA,EAAI,WAAW,kBAAmB,CAAC,EACnCA,EAAI,WAAW,iBAAkB,CAAC,EAClCA,EAAI,WAAW,iBAAkB,CAAC,EAClCA,EAAI,WAAW,gBAAiB,CAAC,EACjCA,EAAI,WAAW,gBAAiB,CAAC,EACjC,MAAM,mBAAkB,CAC3B,CAOD,eAAeC,EAAO5C,EAAMC,EAAS,OACjC,MAAMR,EAAQ,KAAK,WACbW,EAAUH,EAAQ,gBACxB,GAAI,CAACG,EACD,OAEJ,MAAMqC,EAASxC,EAAQ,OACvB,GAAI,CAACwC,EACD,OAEJ,KAAK,cAAgBA,EAErBzC,EAAK,qBAAsB,EAAC,aAAayC,EAAQ,MAAM,EACvDzC,EAAK,iBAAiB4C,CAAK,EAE3B,KAAK,eAAe,aAAaH,EAAQ,UAAU,EACnD,KAAK,qBAAqB,eAAe,KAAK,cAAehD,EAAOO,EAAM4C,EAAO,KAAK,QAAQ,EAC9FjC,EAAgC,KAAKlB,EAAM,YAAY,oBAAqB,KAAK,cAAeO,EAAM4C,EAAO,IAAI,EACjH,KAAK,WAAW,QAAU3C,EAC1B,KAAK,uCAAuC,KAAK,UAAU,EAEvDG,EAAQ,wBACRwC,EAAM,eAAe,KAAK,aAAa,EACvC,KAAK,qBAAqB,KAAK,aAAa,GAEhD,MAAMC,EAAa,KAAK,YAAYpD,EAAOgD,EAAQxC,EAASD,EAAK,UAAU,EAE3E8C,GAAoB9C,EAAMyC,CAAM,EAChC,MAAME,EAAM,KAAK,eACjB,GAAIE,EAAY,CAEZ,GADA,KAAK,mBAAmBJ,CAAM,EAC1B,CAACE,EAAI,QAAU,CAAC,KAAK,UAAY,CAACA,EAAI,QAAU1C,EAAQ,aAAa,uBAAwB,CAwB7F,GAvBIb,EAAiB,gBAAkBgB,EAAQ,UAEvC,KAAK,0BAA4B,KAAK,yBAAyB,YAC/DuC,EAAI,aAAa,mBAAoB,KAAK,yBAAyB,UAAW,KAAK,yBAAyB,KAAK,EACjHA,EAAI,aAAa,oBAAqB,KAAK,yBAAyB,WAAY,KAAK,yBAAyB,IAAI,GAElH,KAAK,0BAA4B,KAAK,yBAAyB,WAC/DA,EAAI,aAAa,eAAgB,IAAIhD,EAAO,KAAK,yBAAyB,UAAU,YAAW,EAAI,KAAK,yBAAyB,WAAW,cAAe,KAAK,yBAAyB,IAAI,EAAG,KAAK,yBAAyB,KAAK,EAEnO,KAAK,6BAA+B,KAAK,4BAA4B,YACrEgD,EAAI,aAAa,sBAAuB,KAAK,4BAA4B,UAAW,KAAK,4BAA4B,KAAK,EAC1HA,EAAI,aAAa,uBAAwB,KAAK,4BAA4B,WAAY,KAAK,4BAA4B,IAAI,GAE3H,KAAK,6BAA+B,KAAK,4BAA4B,YACrEA,EAAI,aAAa,sBAAuB,KAAK,4BAA4B,UAAW,KAAK,4BAA4B,KAAK,EAC1HA,EAAI,aAAa,uBAAwB,KAAK,4BAA4B,WAAY,KAAK,4BAA4B,IAAI,GAE3H,KAAK,2BAA6B,KAAK,0BAA0B,YACjEA,EAAI,aAAa,oBAAqB,KAAK,0BAA0B,UAAW,KAAK,0BAA0B,KAAK,EACpHA,EAAI,aAAa,qBAAsB,KAAK,0BAA0B,WAAY,KAAK,0BAA0B,IAAI,IAIzHlD,EAAM,gBAAiB,CAgBvB,GAfI,KAAK,iBAAmBL,EAAiB,wBACzCuD,EAAI,aAAa,gBAAiB,KAAK,gBAAgB,iBAAkB,KAAK,gBAAgB,KAAK,EACnGI,EAAkB,KAAK,gBAAiBJ,EAAK,SAAS,GAEtD,KAAK,iBAAmBvD,EAAiB,wBACzCuD,EAAI,aAAa,gBAAiB,KAAK,gBAAgB,iBAAkB,KAAK,gBAAgB,KAAK,EACnGI,EAAkB,KAAK,gBAAiBJ,EAAK,SAAS,GAEtD,KAAK,iBAAmBvD,EAAiB,wBACzCuD,EAAI,aAAa,gBAAiB,KAAK,gBAAgB,iBAAkB,KAAK,gBAAgB,KAAK,EACnGI,EAAkB,KAAK,gBAAiBJ,EAAK,SAAS,GAEtD,KAAK,oBACLA,EAAI,YAAY,cAAe,KAAK,WAAW,EAE/C,KAAK,oBAAsBvD,EAAiB,0BAG5C,GAFAuD,EAAI,aAAa,mBAAoB,KAAK,mBAAmB,MAAO,KAAK,SAAS,EAClFA,EAAI,aAAa,mBAAoB,KAAK,mBAAmB,2BAA0B,CAAE,EACrF,KAAK,mBAAmB,gBAAiB,CACzC,MAAMK,EAAc,KAAK,mBACzBL,EAAI,cAAc,sBAAuBK,EAAY,mBAAmB,EACxEL,EAAI,cAAc,kBAAmBK,EAAY,eAAe,CACnE,OAGDL,EAAI,aAAa,mBAAoB,EAAK,KAAK,SAAS,EAwB5D,GAtBI,KAAK,kBAAoBvD,EAAiB,yBAC1CuD,EAAI,aAAa,iBAAkB,KAAK,iBAAiB,iBAAkB,KAAK,iBAAiB,KAAK,EACtGI,EAAkB,KAAK,iBAAkBJ,EAAK,UAAU,GAExD,KAAK,kBAAoBvD,EAAiB,yBAC1CuD,EAAI,aAAa,iBAAkB,KAAK,iBAAiB,iBAAkB,KAAK,iBAAiB,KAAK,EACtGI,EAAkB,KAAK,iBAAkBJ,EAAK,UAAU,GAExD,KAAK,kBAAoBvD,EAAiB,yBAC1CuD,EAAI,aAAa,iBAAkB,KAAK,iBAAiB,iBAAkB,KAAK,iBAAiB,KAAK,EACtGI,EAAkB,KAAK,iBAAkBJ,EAAK,UAAU,GAExD,KAAK,cAAgBlD,EAAM,UAAS,EAAG,UAAU,qBAAuBL,EAAiB,qBACzFuD,EAAI,aAAa,aAAc,KAAK,aAAa,iBAAkB,EAAM,KAAK,aAAa,MAAO,KAAK,iBAAiB,EACxHI,EAAkB,KAAK,aAAcJ,EAAK,MAAM,EAC5ClD,EAAM,wBACNkD,EAAI,aAAa,sBAAuB,KAAK,kBAAoB,EAAM,GAAM,KAAK,kBAAoB,EAAM,EAAI,EAGhHA,EAAI,aAAa,sBAAuB,KAAK,kBAAoB,GAAO,EAAK,KAAK,kBAAoB,GAAO,CAAG,GAGpH,KAAK,oBAAsBvD,EAAiB,yBAA0B,CACtE,IAAI6D,EAAQ,EAQZ,GAPK,KAAK,mBAAmB,SACzBN,EAAI,aAAa,mBAAoB,KAAK,mBAAmB,2BAA0B,CAAE,EACrF,KAAK,mBAAmB,QACxBM,EAAQ,KAAK,mBAAmB,QAGxCN,EAAI,aAAa,mBAAoB,KAAK,mBAAmB,MAAO,KAAK,kBAAmBM,EAAO,KAAK,kBAAoB,GAAK,CAAC,EAC9H,KAAK,mBAAmB,gBAAiB,CACzC,MAAMD,EAAc,KAAK,mBACzBL,EAAI,cAAc,sBAAuBK,EAAY,mBAAmB,EACxEL,EAAI,cAAc,kBAAmBK,EAAY,eAAe,CACnE,CACJ,CACJ,CAEG,KAAK,aACLL,EAAI,YAAY,YAAa,KAAK,SAAS,EAE/CA,EAAI,aAAa,iBAAkB,KAAK,cAAe,KAAK,aAAa,EACzEA,EAAI,aAAa,iBAAkBvD,EAAiB,uBAAyB,KAAK,cAAgBO,EAAO,aAAa,EACtHgD,EAAI,aAAa,gBAAiB,KAAK,aAAc,KAAK,KAAK,EAC/DlD,EAAM,aAAa,cAAc,KAAK,aAAc,KAAK,mBAAmB,EAC5EkD,EAAI,aAAa,gBAAiB,KAAK,mBAAmB,CAC7D,CAEGlD,EAAM,kBACF,KAAK,iBAAmBL,EAAiB,uBACzCqD,EAAO,WAAW,iBAAkB,KAAK,eAAe,EAExD,KAAK,iBAAmBrD,EAAiB,uBACzCqD,EAAO,WAAW,iBAAkB,KAAK,eAAe,EAExD,KAAK,iBAAmBrD,EAAiB,uBACzCqD,EAAO,WAAW,iBAAkB,KAAK,eAAe,EAExD,KAAK,oBAAsBrD,EAAiB,2BACxC,KAAK,mBAAmB,OACxBqD,EAAO,WAAW,wBAAyB,KAAK,kBAAkB,EAGlEA,EAAO,WAAW,sBAAuB,KAAK,kBAAkB,GAGpE,KAAK,kBAAoBrD,EAAiB,wBAC1CqD,EAAO,WAAW,kBAAmB,KAAK,gBAAgB,EAE1D,KAAK,kBAAoBrD,EAAiB,wBAC1CqD,EAAO,WAAW,kBAAmB,KAAK,gBAAgB,EAE1D,KAAK,kBAAoBrD,EAAiB,wBAC1CqD,EAAO,WAAW,kBAAmB,KAAK,gBAAgB,EAE1D,KAAK,cAAgBhD,EAAM,UAAS,EAAG,UAAU,qBAAuBL,EAAiB,oBACzFqD,EAAO,WAAW,cAAe,KAAK,YAAY,EAElD,KAAK,oBAAsBrD,EAAiB,2BACxC,KAAK,mBAAmB,OACxBqD,EAAO,WAAW,wBAAyB,KAAK,kBAAkB,EAGlEA,EAAO,WAAW,sBAAuB,KAAK,kBAAkB,IAKxE,KAAK,WAAW,iCAAmC,KAAK,yBAAyBzC,CAAI,GACrF,KAAK,SAAU,EAAC,qBAAqB,KAAKyC,CAAM,EAEpD,KAAK,WAAW,QAAUxC,EAC1B,KAAK,mCAAmC,KAAK,UAAU,EAEvDiD,GAAcT,EAAQ,KAAMhD,CAAK,EAEjC,KAAK,gBAAgBgD,CAAM,CAC9B,MACQhD,EAAM,YAAY,UAAU,iCACjC,KAAK,oBAAsB,KAE3BoD,GAAc,CAAC,KAAK,YAEhBpD,EAAM,eAAiB,CAAC,KAAK,kBAC7B0D,GAAW1D,EAAOO,EAAMyC,EAAQrC,EAAS,KAAK,sBAAsB,GAGnEX,EAAM,YAAcO,EAAK,UAAYP,EAAM,UAAY2D,EAAM,cAC9D,KAAK,oBACL,KAAK,oBACLpD,EAAK,gBACLI,EAAQ,UACR,KAAK,SAASqC,CAAM,EAGxBY,GAAkB5D,EAAOO,EAAMyC,CAAM,EAEjCrC,EAAQ,uBACRkD,GAA0BtD,EAAMyC,CAAM,EAEtCrC,EAAQ,kCACRmD,EAAAvD,EAAK,8BAAL,MAAAuD,EAAkC,KAAKd,EAAQrC,EAAQ,YAGvD,KAAK,qBACLoD,GAAapD,EAASqC,EAAQhD,CAAK,EAGnC,KAAK,+BAAiC,CAAC,KAAK,8BAA8B,oBAC1E,KAAK,8BAA8B,KAAK,KAAK,aAAa,GAGlE,KAAK,WAAWO,EAAM,KAAK,cAAeC,CAAO,EACjD0C,EAAI,OAAM,CACb,CAKD,gBAAiB,CACb,MAAMc,EAAU,MAAM,iBACtB,OAAI,KAAK,iBAAmB,KAAK,gBAAgB,YAAc,KAAK,gBAAgB,WAAW,OAAS,GACpGA,EAAQ,KAAK,KAAK,eAAe,EAEjC,KAAK,iBAAmB,KAAK,gBAAgB,YAAc,KAAK,gBAAgB,WAAW,OAAS,GACpGA,EAAQ,KAAK,KAAK,eAAe,EAEjC,KAAK,iBAAmB,KAAK,gBAAgB,YAAc,KAAK,gBAAgB,WAAW,OAAS,GACpGA,EAAQ,KAAK,KAAK,eAAe,EAEjC,KAAK,oBAAsB,KAAK,mBAAmB,YAAc,KAAK,mBAAmB,WAAW,OAAS,GAC7GA,EAAQ,KAAK,KAAK,kBAAkB,EAEpC,KAAK,kBAAoB,KAAK,iBAAiB,YAAc,KAAK,iBAAiB,WAAW,OAAS,GACvGA,EAAQ,KAAK,KAAK,gBAAgB,EAElC,KAAK,kBAAoB,KAAK,iBAAiB,YAAc,KAAK,iBAAiB,WAAW,OAAS,GACvGA,EAAQ,KAAK,KAAK,gBAAgB,EAElC,KAAK,cAAgB,KAAK,aAAa,YAAc,KAAK,aAAa,WAAW,OAAS,GAC3FA,EAAQ,KAAK,KAAK,YAAY,EAE9B,KAAK,kBAAoB,KAAK,iBAAiB,YAAc,KAAK,iBAAiB,WAAW,OAAS,GACvGA,EAAQ,KAAK,KAAK,gBAAgB,EAElC,KAAK,oBAAsB,KAAK,mBAAmB,YAAc,KAAK,mBAAmB,WAAW,OAAS,GAC7GA,EAAQ,KAAK,KAAK,kBAAkB,EAEjCA,CACV,CAKD,mBAAoB,CAChB,MAAMC,EAAiB,MAAM,oBAC7B,OAAI,KAAK,iBACLA,EAAe,KAAK,KAAK,eAAe,EAExC,KAAK,iBACLA,EAAe,KAAK,KAAK,eAAe,EAExC,KAAK,iBACLA,EAAe,KAAK,KAAK,eAAe,EAExC,KAAK,oBACLA,EAAe,KAAK,KAAK,kBAAkB,EAE3C,KAAK,kBACLA,EAAe,KAAK,KAAK,gBAAgB,EAEzC,KAAK,kBACLA,EAAe,KAAK,KAAK,gBAAgB,EAEzC,KAAK,cACLA,EAAe,KAAK,KAAK,YAAY,EAErC,KAAK,kBACLA,EAAe,KAAK,KAAK,gBAAgB,EAEzC,KAAK,oBACLA,EAAe,KAAK,KAAK,kBAAkB,EAExCA,CACV,CAMD,WAAWC,EAAS,CA4BhB,MA3BI,SAAM,WAAWA,CAAO,GAGxB,KAAK,kBAAoBA,GAGzB,KAAK,kBAAoBA,GAGzB,KAAK,kBAAoBA,GAGzB,KAAK,qBAAuBA,GAG5B,KAAK,mBAAqBA,GAG1B,KAAK,mBAAqBA,GAG1B,KAAK,eAAiBA,GAGtB,KAAK,mBAAqBA,GAG1B,KAAK,qBAAuBA,EAInC,CAMD,QAAQC,EAAoBC,EAAsB,uBAC1CA,KACAN,EAAA,KAAK,kBAAL,MAAAA,EAAsB,WACtBO,EAAA,KAAK,kBAAL,MAAAA,EAAsB,WACtBC,EAAA,KAAK,kBAAL,MAAAA,EAAsB,WACtBC,EAAA,KAAK,qBAAL,MAAAA,EAAyB,WACzBC,EAAA,KAAK,mBAAL,MAAAA,EAAuB,WACvBC,EAAA,KAAK,mBAAL,MAAAA,EAAuB,WACvBC,EAAA,KAAK,eAAL,MAAAA,EAAmB,WACnBC,EAAA,KAAK,mBAAL,MAAAA,EAAuB,WACvBC,EAAA,KAAK,qBAAL,MAAAA,EAAyB,WAEzB,KAAK,+BAAiC,KAAK,0BAC3C,KAAK,8BAA8B,mBAAmB,OAAO,KAAK,wBAAwB,EAE9F,MAAM,QAAQT,EAAoBC,CAAoB,CACzD,CAQD,MAAMrE,EAAM8E,EAAwB,GAAMC,EAAU,GAAI,CACpD,MAAMC,EAASC,EAAoB,MAAM,IAAM,IAAIrF,EAAiBI,EAAM,KAAK,SAAQ,CAAE,EAAG,KAAM,CAAE,sBAAA8E,CAAuB,GAC3H,OAAAE,EAAO,KAAOhF,EACdgF,EAAO,GAAKhF,EACZ,KAAK,QAAQ,OAAOgF,EAAO,OAAO,EAClC,KAAK,cAAcA,EAAQD,CAAO,EAC3BC,CACV,CAQD,OAAO,MAAME,EAAQjF,EAAO8E,EAAS,CACjC,MAAMI,EAAWF,EAAoB,MAAM,IAAM,IAAIrF,EAAiBsF,EAAO,KAAMjF,CAAK,EAAGiF,EAAQjF,EAAO8E,CAAO,EACjH,OAAIG,EAAO,SACPC,EAAS,QAAQ,MAAMD,EAAO,QAASjF,EAAO8E,CAAO,EAEzDxE,EAAS,cAAc2E,EAAQC,EAAUlF,EAAO8E,CAAO,EAChDI,CACV,CAKD,WAAW,uBAAwB,CAC/B,OAAOC,EAAc,qBACxB,CACD,WAAW,sBAAsBtF,EAAO,CACpCsF,EAAc,sBAAwBtF,CACzC,CAID,WAAW,sBAAuB,CAC9B,OAAOsF,EAAc,oBACxB,CACD,WAAW,qBAAqBtF,EAAO,CACnCsF,EAAc,qBAAuBtF,CACxC,CAID,WAAW,uBAAwB,CAC/B,OAAOsF,EAAc,qBACxB,CACD,WAAW,sBAAsBtF,EAAO,CACpCsF,EAAc,sBAAwBtF,CACzC,CAID,WAAW,uBAAwB,CAC/B,OAAOsF,EAAc,qBACxB,CACD,WAAW,sBAAsBtF,EAAO,CACpCsF,EAAc,sBAAwBtF,CACzC,CAID,WAAW,0BAA2B,CAClC,OAAOsF,EAAc,wBACxB,CACD,WAAW,yBAAyBtF,EAAO,CACvCsF,EAAc,yBAA2BtF,CAC5C,CAID,WAAW,wBAAyB,CAChC,OAAOsF,EAAc,sBACxB,CACD,WAAW,uBAAuBtF,EAAO,CACrCsF,EAAc,uBAAyBtF,CAC1C,CAID,WAAW,wBAAyB,CAChC,OAAOsF,EAAc,sBACxB,CACD,WAAW,uBAAuBtF,EAAO,CACrCsF,EAAc,uBAAyBtF,CAC1C,CAID,WAAW,oBAAqB,CAC5B,OAAOsF,EAAc,kBACxB,CACD,WAAW,mBAAmBtF,EAAO,CACjCsF,EAAc,mBAAqBtF,CACtC,CAID,WAAW,wBAAyB,CAChC,OAAOsF,EAAc,sBACxB,CACD,WAAW,uBAAuBtF,EAAO,CACrCsF,EAAc,uBAAyBtF,CAC1C,CAID,WAAW,0BAA2B,CAClC,OAAOsF,EAAc,wBACxB,CACD,WAAW,yBAAyBtF,EAAO,CACvCsF,EAAc,yBAA2BtF,CAC5C,CAID,WAAW,4BAA6B,CACpC,OAAOsF,EAAc,0BACxB,CACD,WAAW,2BAA2BtF,EAAO,CACzCsF,EAAc,2BAA6BtF,CAC9C,CAID,WAAW,gBAAiB,CACxB,OAAOsF,EAAc,cACxB,CACD,WAAW,eAAetF,EAAO,CAC7BsF,EAAc,eAAiBtF,CAClC,CACL,CAKAF,EAAiB,UAAY,GAC7ByF,EAAW,CACPC,EAAmB,gBAAgB,CACvC,EAAG1F,EAAiB,UAAW,kBAAmB,MAAM,EACxDyF,EAAW,CACPE,EAAiB,yCAAyC,CAC9D,EAAG3F,EAAiB,UAAW,iBAAkB,MAAM,EACvDyF,EAAW,CACPC,EAAmB,gBAAgB,CACvC,EAAG1F,EAAiB,UAAW,kBAAmB,MAAM,EACxDyF,EAAW,CACPE,EAAiB,kCAAkC,CACvD,EAAG3F,EAAiB,UAAW,iBAAkB,MAAM,EACvDyF,EAAW,CACPC,EAAmB,gBAAgB,CACvC,EAAG1F,EAAiB,UAAW,kBAAmB,MAAM,EACxDyF,EAAW,CACPE,EAAiB,yCAAyC,CAC9D,EAAG3F,EAAiB,UAAW,iBAAkB,MAAM,EACvDyF,EAAW,CACPC,EAAmB,mBAAmB,CAC1C,EAAG1F,EAAiB,UAAW,qBAAsB,MAAM,EAC3DyF,EAAW,CACPE,EAAiB,kCAAkC,CACvD,EAAG3F,EAAiB,UAAW,oBAAqB,MAAM,EAC1DyF,EAAW,CACPC,EAAmB,iBAAiB,CACxC,EAAG1F,EAAiB,UAAW,mBAAoB,MAAM,EACzDyF,EAAW,CACPE,EAAiB,kCAAkC,CACvD,EAAG3F,EAAiB,UAAW,kBAAmB,MAAM,EACxDyF,EAAW,CACPC,EAAmB,iBAAiB,CACxC,EAAG1F,EAAiB,UAAW,mBAAoB,MAAM,EACzDyF,EAAW,CACPE,EAAiB,kCAAkC,CACvD,EAAG3F,EAAiB,UAAW,kBAAmB,MAAM,EACxDyF,EAAW,CACPC,EAAmB,aAAa,CACpC,EAAG1F,EAAiB,UAAW,eAAgB,MAAM,EACrDyF,EAAW,CACPE,EAAiB,kCAAkC,CACvD,EAAG3F,EAAiB,UAAW,cAAe,MAAM,EACpDyF,EAAW,CACPC,EAAmB,iBAAiB,CACxC,EAAG1F,EAAiB,UAAW,mBAAoB,MAAM,EACzDyF,EAAW,CACPE,EAAiB,kCAAkC,CACvD,EAAG3F,EAAiB,UAAW,kBAAmB,MAAM,EACxDyF,EAAW,CACPC,EAAmB,mBAAmB,CAC1C,EAAG1F,EAAiB,UAAW,qBAAsB,MAAM,EAC3DyF,EAAW,CACPE,EAAiB,kCAAkC,CACvD,EAAG3F,EAAiB,UAAW,oBAAqB,MAAM,EAC1DyF,EAAW,CACPG,EAAkB,SAAS,CAC/B,EAAG5F,EAAiB,UAAW,eAAgB,MAAM,EACrDyF,EAAW,CACPG,EAAkB,SAAS,CAC/B,EAAG5F,EAAiB,UAAW,eAAgB,MAAM,EACrDyF,EAAW,CACPG,EAAkB,UAAU,CAChC,EAAG5F,EAAiB,UAAW,gBAAiB,MAAM,EACtDyF,EAAW,CACPG,EAAkB,UAAU,CAChC,EAAG5F,EAAiB,UAAW,gBAAiB,MAAM,EACtDyF,EAAW,CACPI,EAAW,CACf,EAAG7F,EAAiB,UAAW,gBAAiB,MAAM,EACtDyF,EAAW,CACPI,EAAU,4BAA4B,CAC1C,EAAG7F,EAAiB,UAAW,8BAA+B,MAAM,EACpEyF,EAAW,CACPE,EAAiB,yCAAyC,CAC9D,EAAG3F,EAAiB,UAAW,6BAA8B,MAAM,EACnEyF,EAAW,CACPI,EAAU,2BAA2B,CACzC,EAAG7F,EAAiB,UAAW,6BAA8B,MAAM,EACnEyF,EAAW,CACPE,EAAiB,kCAAkC,CACvD,EAAG3F,EAAiB,UAAW,4BAA6B,MAAM,EAClEyF,EAAW,CACPI,EAAU,yBAAyB,CACvC,EAAG7F,EAAiB,UAAW,2BAA4B,MAAM,EACjEyF,EAAW,CACPE,EAAiB,kCAAkC,CACvD,EAAG3F,EAAiB,UAAW,0BAA2B,MAAM,EAChEyF,EAAW,CACPI,EAAU,sBAAsB,CACpC,EAAG7F,EAAiB,UAAW,wBAAyB,MAAM,EAC9DyF,EAAW,CACPE,EAAiB,kCAAkC,CACvD,EAAG3F,EAAiB,UAAW,uBAAwB,MAAM,EAC7DyF,EAAW,CACPI,EAAU,wBAAwB,CACtC,EAAG7F,EAAiB,UAAW,0BAA2B,MAAM,EAChEyF,EAAW,CACPE,EAAiB,kCAAkC,CACvD,EAAG3F,EAAiB,UAAW,yBAA0B,MAAM,EAC/DyF,EAAW,CACPI,EAAU,iBAAiB,CAC/B,EAAG7F,EAAiB,UAAW,mBAAoB,MAAM,EACzDyF,EAAW,CACPE,EAAiB,gCAAgC,CACrD,EAAG3F,EAAiB,UAAW,kBAAmB,MAAM,EACxDyF,EAAW,CACPI,EAAU,yBAAyB,CACvC,EAAG7F,EAAiB,UAAW,2BAA4B,MAAM,EACjEyF,EAAW,CACPE,EAAiB,kCAAkC,CACvD,EAAG3F,EAAiB,UAAW,0BAA2B,MAAM,EAChEyF,EAAW,CACPI,EAAU,aAAa,CAC3B,EAAG7F,EAAiB,UAAW,eAAgB,MAAM,EACrDyF,EAAW,CACPE,EAAiB,kCAAkC,CACvD,EAAG3F,EAAiB,UAAW,cAAe,MAAM,EACpDyF,EAAW,CACPI,EAAU,sBAAsB,CACpC,EAAG7F,EAAiB,UAAW,wBAAyB,MAAM,EAC9DyF,EAAW,CACPE,EAAiB,kCAAkC,CACvD,EAAG3F,EAAiB,UAAW,uBAAwB,MAAM,EAC7DyF,EAAW,CACPI,EAAW,CACf,EAAG7F,EAAiB,UAAW,oBAAqB,MAAM,EAC1DyF,EAAW,CACPI,EAAU,WAAW,CACzB,EAAG7F,EAAiB,UAAW,aAAc,MAAM,EACnDyF,EAAW,CACPE,EAAiB,kCAAkC,CACvD,EAAG3F,EAAiB,UAAW,YAAa,MAAM,EAClDyF,EAAW,CACPI,EAAW,CACf,EAAG7F,EAAiB,UAAW,oBAAqB,MAAM,EAC1DyF,EAAW,CACPI,EAAW,CACf,EAAG7F,EAAiB,UAAW,oBAAqB,MAAM,EAC1DyF,EAAW,CACPI,EAAW,CACf,EAAG7F,EAAiB,UAAW,cAAe,MAAM,EACpDyF,EAAW,CACPI,EAAU,wBAAwB,CACtC,EAAG7F,EAAiB,UAAW,0BAA2B,MAAM,EAChEyF,EAAW,CACPE,EAAiB,kCAAkC,CACvD,EAAG3F,EAAiB,UAAW,yBAA0B,MAAM,EAC/DyF,EAAW,CACPK,EAA6B,0BAA0B,CAC3D,EAAG9F,EAAiB,UAAW,4BAA6B,MAAM,EAClEyF,EAAW,CACPE,EAAiB,iCAAiC,CACtD,EAAG3F,EAAiB,UAAW,2BAA4B,MAAM,EACjEyF,EAAW,CACPK,EAA6B,0BAA0B,CAC3D,EAAG9F,EAAiB,UAAW,4BAA6B,MAAM,EAClEyF,EAAW,CACPE,EAAiB,wCAAwC,CAC7D,EAAG3F,EAAiB,UAAW,2BAA4B,MAAM,EACjEyF,EAAW,CACPK,EAA6B,6BAA6B,CAC9D,EAAG9F,EAAiB,UAAW,+BAAgC,MAAM,EACrEyF,EAAW,CACPE,EAAiB,iCAAiC,CACtD,EAAG3F,EAAiB,UAAW,8BAA+B,MAAM,EACpEyF,EAAW,CACPK,EAA6B,6BAA6B,CAC9D,EAAG9F,EAAiB,UAAW,+BAAgC,MAAM,EACrEyF,EAAW,CACPE,EAAiB,iCAAiC,CACtD,EAAG3F,EAAiB,UAAW,8BAA+B,MAAM,EACpEyF,EAAW,CACPK,EAA6B,2BAA2B,CAC5D,EAAG9F,EAAiB,UAAW,6BAA8B,MAAM,EACnEyF,EAAW,CACPE,EAAiB,iCAAiC,CACtD,EAAG3F,EAAiB,UAAW,4BAA6B,MAAM,EAClEyF,EAAW,CACPI,EAAU,kCAAkC,CAChD,EAAG7F,EAAiB,UAAW,oCAAqC,MAAM,EAC1EyF,EAAW,CACPE,EAAiB,iCAAiC,CACtD,EAAG3F,EAAiB,UAAW,mCAAoC,MAAM,EACzEyF,EAAW,CACPI,EAAU,mCAAmC,CACjD,EAAG7F,EAAiB,UAAW,qCAAsC,MAAM,EAC3EyF,EAAW,CACPE,EAAiB,kCAAkC,CACvD,EAAG3F,EAAiB,UAAW,oCAAqC,MAAM,EAC1EyF,EAAW,CACPI,EAAU,uBAAuB,CACrC,EAAG7F,EAAiB,UAAW,yBAA0B,MAAM,EAC/DyF,EAAW,CACPE,EAAiB,gCAAgC,CACrD,EAAG3F,EAAiB,UAAW,wBAAyB,MAAM,EAC9DyF,EAAW,CACPI,EAAU,kBAAkB,CAChC,EAAG7F,EAAiB,UAAW,oBAAqB,MAAM,EAC1DyF,EAAW,CACPE,EAAiB,kCAAkC,CACvD,EAAG3F,EAAiB,UAAW,mBAAoB,MAAM,EACzDyF,EAAW,CACPI,EAAU,kBAAkB,CAChC,EAAG7F,EAAiB,UAAW,oBAAqB,MAAM,EAC1DyF,EAAW,CACPE,EAAiB,kCAAkC,CACvD,EAAG3F,EAAiB,UAAW,mBAAoB,MAAM,EACzDyF,EAAW,CACPI,EAAU,kBAAkB,CAChC,EAAG7F,EAAiB,UAAW,oBAAqB,MAAM,EAC1DyF,EAAW,CACPE,EAAiB,kCAAkC,CACvD,EAAG3F,EAAiB,UAAW,mBAAoB,MAAM,EACzDyF,EAAW,CACPI,EAAU,6BAA6B,CAC3C,EAAG7F,EAAiB,UAAW,+BAAgC,MAAM,EACrEyF,EAAW,CACPE,EAAiB,8BAA8B,CACnD,EAAG3F,EAAiB,UAAW,8BAA+B,MAAM,EACpE+F,EAAc,2BAA4B/F,CAAgB,EAC1DgE,EAAM,uBAA0B3D,GACrB,IAAIL,EAAiB,mBAAoBK,CAAK", "names": ["onCreatedEffectParameters", "StandardMaterialDefines", "MaterialDefines", "externalProperties", "modeToEnable", "modes", "mode", "StandardMaterial", "PushMaterial", "value", "configuration", "name", "scene", "forceGLSL", "Color3", "SmartArray", "DetailMapConfiguration", "PrePassConfiguration", "Material", "mesh", "<PERSON><PERSON><PERSON>", "useInstances", "drawWrapper", "defines", "engine", "PrepareDefinesForLights", "PrepareDefinesForMultiview", "oit", "PrepareDefinesForPrePass", "PrepareDefinesForOIT", "MaterialHelperGeometryRendering", "i", "PrepareDefinesForMergedUV", "Texture", "index", "PrepareDefinesForMisc", "PrepareDefinesForFrameBoundValues", "PrepareDefinesForAttributes", "forceWasNotReadyPreviously", "lightDisposed", "fallbacks", "EffectFallbacks", "HandleFallbacksForShadows", "attribs", "VertexBuffer", "PrepareAttributesForBones", "PrepareAttributesForInstances", "PrepareAttributesForMorphTargets", "PrepareAttributesForBakedVertexAnimation", "shaderName", "uniforms", "samplers", "uniformBuffers", "indexParameters", "ImageProcessingConfiguration", "PrepareUniformsAndSamplersList", "addClipPlaneUniforms", "csnrOptions", "join", "previousEffect", "effect", "__vitePreload", "ubo", "world", "<PERSON><PERSON><PERSON><PERSON>", "BindBonesParameters", "BindTextureMatrix", "cubeTexture", "depth", "bindClipPlane", "BindLights", "Scene", "BindFogParameters", "BindMorphTargetParameters", "_a", "BindLogDepth", "results", "activeTextures", "texture", "forceDisposeEffect", "forceDisposeTextures", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "_i", "cloneTexturesOnlyOnce", "rootUrl", "result", "SerializationHelper", "source", "material", "MaterialFlags", "__decorate", "serializeAsTexture", "expandToProperty", "serializeAsColor3", "serialize", "serializeAsFresnelParameters", "RegisterClass"], "ignoreList": [0], "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/standardMaterial.js"], "sourcesContent": ["import { __decorate } from \"../tslib.es6.js\";\n/* eslint-disable @typescript-eslint/naming-convention */\nimport { serialize, serializeAsColor3, expandToProperty, serializeAsFresnelParameters, serializeAsTexture } from \"../Misc/decorators.js\";\nimport { SmartArray } from \"../Misc/smartArray.js\";\nimport { Scene } from \"../scene.js\";\nimport { Color3 } from \"../Maths/math.color.js\";\nimport { VertexBuffer } from \"../Buffers/buffer.js\";\nimport { PrePassConfiguration } from \"./prePassConfiguration.js\";\nimport { ImageProcessingConfiguration } from \"./imageProcessingConfiguration.js\";\nimport { Material } from \"../Materials/material.js\";\nimport { MaterialDefines } from \"../Materials/materialDefines.js\";\nimport { PushMaterial } from \"./pushMaterial.js\";\nimport { Texture } from \"../Materials/Textures/texture.js\";\nimport { RegisterClass } from \"../Misc/typeStore.js\";\nimport { MaterialFlags } from \"./materialFlags.js\";\n\nimport { EffectFallbacks } from \"./effectFallbacks.js\";\nimport { DetailMapConfiguration } from \"./material.detailMapConfiguration.js\";\nimport { addClipPlaneUniforms, bindClipPlane } from \"./clipPlaneMaterialHelper.js\";\nimport { BindBonesParameters, BindFogParameters, BindLights, BindLogDepth, BindMorphTargetParameters, BindTextureMatrix, HandleFallbacksForShadows, PrepareAttributesForBakedVertexAnimation, PrepareAttributesForBones, PrepareAttributesForInstances, PrepareAttributesForMorphTargets, PrepareDefinesForAttributes, PrepareDefinesForFrameBoundValues, PrepareDefinesForLights, PrepareDefinesForMergedUV, PrepareDefinesForMisc, PrepareDefinesForMultiview, PrepareDefinesForOIT, PrepareDefinesForPrePass, PrepareUniformsAndSamplersList, } from \"./materialHelper.functions.js\";\nimport { SerializationHelper } from \"../Misc/decorators.serialization.js\";\nimport { MaterialHelperGeometryRendering } from \"./materialHelper.geometryrendering.js\";\nconst onCreatedEffectParameters = { effect: null, subMesh: null };\n/** @internal */\nexport class StandardMaterialDefines extends MaterialDefines {\n    /**\n     * Initializes the Standard Material defines.\n     * @param externalProperties The external properties\n     */\n    constructor(externalProperties) {\n        super(externalProperties);\n        this.MAINUV1 = false;\n        this.MAINUV2 = false;\n        this.MAINUV3 = false;\n        this.MAINUV4 = false;\n        this.MAINUV5 = false;\n        this.MAINUV6 = false;\n        this.DIFFUSE = false;\n        this.DIFFUSEDIRECTUV = 0;\n        this.BAKED_VERTEX_ANIMATION_TEXTURE = false;\n        this.AMBIENT = false;\n        this.AMBIENTDIRECTUV = 0;\n        this.OPACITY = false;\n        this.OPACITYDIRECTUV = 0;\n        this.OPACITYRGB = false;\n        this.REFLECTION = false;\n        this.EMISSIVE = false;\n        this.EMISSIVEDIRECTUV = 0;\n        this.SPECULAR = false;\n        this.SPECULARDIRECTUV = 0;\n        this.BUMP = false;\n        this.BUMPDIRECTUV = 0;\n        this.PARALLAX = false;\n        this.PARALLAX_RHS = false;\n        this.PARALLAXOCCLUSION = false;\n        this.SPECULAROVERALPHA = false;\n        this.CLIPPLANE = false;\n        this.CLIPPLANE2 = false;\n        this.CLIPPLANE3 = false;\n        this.CLIPPLANE4 = false;\n        this.CLIPPLANE5 = false;\n        this.CLIPPLANE6 = false;\n        this.ALPHATEST = false;\n        this.DEPTHPREPASS = false;\n        this.ALPHAFROMDIFFUSE = false;\n        this.POINTSIZE = false;\n        this.FOG = false;\n        this.SPECULARTERM = false;\n        this.DIFFUSEFRESNEL = false;\n        this.OPACITYFRESNEL = false;\n        this.REFLECTIONFRESNEL = false;\n        this.REFRACTIONFRESNEL = false;\n        this.EMISSIVEFRESNEL = false;\n        this.FRESNEL = false;\n        this.NORMAL = false;\n        this.TANGENT = false;\n        this.UV1 = false;\n        this.UV2 = false;\n        this.UV3 = false;\n        this.UV4 = false;\n        this.UV5 = false;\n        this.UV6 = false;\n        this.VERTEXCOLOR = false;\n        this.VERTEXALPHA = false;\n        this.NUM_BONE_INFLUENCERS = 0;\n        this.BonesPerMesh = 0;\n        this.BONETEXTURE = false;\n        this.BONES_VELOCITY_ENABLED = false;\n        this.INSTANCES = false;\n        this.THIN_INSTANCES = false;\n        this.INSTANCESCOLOR = false;\n        this.GLOSSINESS = false;\n        this.ROUGHNESS = false;\n        this.EMISSIVEASILLUMINATION = false;\n        this.LINKEMISSIVEWITHDIFFUSE = false;\n        this.REFLECTIONFRESNELFROMSPECULAR = false;\n        this.LIGHTMAP = false;\n        this.LIGHTMAPDIRECTUV = 0;\n        this.OBJECTSPACE_NORMALMAP = false;\n        this.USELIGHTMAPASSHADOWMAP = false;\n        this.REFLECTIONMAP_3D = false;\n        this.REFLECTIONMAP_SPHERICAL = false;\n        this.REFLECTIONMAP_PLANAR = false;\n        this.REFLECTIONMAP_CUBIC = false;\n        this.USE_LOCAL_REFLECTIONMAP_CUBIC = false;\n        this.USE_LOCAL_REFRACTIONMAP_CUBIC = false;\n        this.REFLECTIONMAP_PROJECTION = false;\n        this.REFLECTIONMAP_SKYBOX = false;\n        this.REFLECTIONMAP_EXPLICIT = false;\n        this.REFLECTIONMAP_EQUIRECTANGULAR = false;\n        this.REFLECTIONMAP_EQUIRECTANGULAR_FIXED = false;\n        this.REFLECTIONMAP_MIRROREDEQUIRECTANGULAR_FIXED = false;\n        this.REFLECTIONMAP_OPPOSITEZ = false;\n        this.INVERTCUBICMAP = false;\n        this.LOGARITHMICDEPTH = false;\n        this.REFRACTION = false;\n        this.REFRACTIONMAP_3D = false;\n        this.REFLECTIONOVERALPHA = false;\n        this.TWOSIDEDLIGHTING = false;\n        this.SHADOWFLOAT = false;\n        this.MORPHTARGETS = false;\n        this.MORPHTARGETS_POSITION = false;\n        this.MORPHTARGETS_NORMAL = false;\n        this.MORPHTARGETS_TANGENT = false;\n        this.MORPHTARGETS_UV = false;\n        this.MORPHTARGETS_UV2 = false;\n        this.MORPHTARGETS_COLOR = false;\n        this.MORPHTARGETTEXTURE_HASPOSITIONS = false;\n        this.MORPHTARGETTEXTURE_HASNORMALS = false;\n        this.MORPHTARGETTEXTURE_HASTANGENTS = false;\n        this.MORPHTARGETTEXTURE_HASUVS = false;\n        this.MORPHTARGETTEXTURE_HASUV2S = false;\n        this.MORPHTARGETTEXTURE_HASCOLORS = false;\n        this.NUM_MORPH_INFLUENCERS = 0;\n        this.MORPHTARGETS_TEXTURE = false;\n        this.NONUNIFORMSCALING = false; // https://playground.babylonjs.com#V6DWIH\n        this.PREMULTIPLYALPHA = false; // https://playground.babylonjs.com#LNVJJ7\n        this.ALPHATEST_AFTERALLALPHACOMPUTATIONS = false;\n        this.ALPHABLEND = true;\n        this.PREPASS = false;\n        this.PREPASS_COLOR = false;\n        this.PREPASS_COLOR_INDEX = -1;\n        this.PREPASS_IRRADIANCE = false;\n        this.PREPASS_IRRADIANCE_INDEX = -1;\n        this.PREPASS_ALBEDO = false;\n        this.PREPASS_ALBEDO_INDEX = -1;\n        this.PREPASS_ALBEDO_SQRT = false;\n        this.PREPASS_ALBEDO_SQRT_INDEX = -1;\n        this.PREPASS_DEPTH = false;\n        this.PREPASS_DEPTH_INDEX = -1;\n        this.PREPASS_SCREENSPACE_DEPTH = false;\n        this.PREPASS_SCREENSPACE_DEPTH_INDEX = -1;\n        this.PREPASS_NORMAL = false;\n        this.PREPASS_NORMAL_INDEX = -1;\n        this.PREPASS_NORMAL_WORLDSPACE = false;\n        this.PREPASS_WORLD_NORMAL = false;\n        this.PREPASS_WORLD_NORMAL_INDEX = -1;\n        this.PREPASS_POSITION = false;\n        this.PREPASS_POSITION_INDEX = -1;\n        this.PREPASS_LOCAL_POSITION = false;\n        this.PREPASS_LOCAL_POSITION_INDEX = -1;\n        this.PREPASS_VELOCITY = false;\n        this.PREPASS_VELOCITY_INDEX = -1;\n        this.PREPASS_VELOCITY_LINEAR = false;\n        this.PREPASS_VELOCITY_LINEAR_INDEX = -1;\n        this.PREPASS_REFLECTIVITY = false;\n        this.PREPASS_REFLECTIVITY_INDEX = -1;\n        this.SCENE_MRT_COUNT = 0;\n        this.RGBDLIGHTMAP = false;\n        this.RGBDREFLECTION = false;\n        this.RGBDREFRACTION = false;\n        this.IMAGEPROCESSING = false;\n        this.VIGNETTE = false;\n        this.VIGNETTEBLENDMODEMULTIPLY = false;\n        this.VIGNETTEBLENDMODEOPAQUE = false;\n        this.TONEMAPPING = 0;\n        this.CONTRAST = false;\n        this.COLORCURVES = false;\n        this.COLORGRADING = false;\n        this.COLORGRADING3D = false;\n        this.SAMPLER3DGREENDEPTH = false;\n        this.SAMPLER3DBGRMAP = false;\n        this.DITHER = false;\n        this.IMAGEPROCESSINGPOSTPROCESS = false;\n        this.SKIPFINALCOLORCLAMP = false;\n        this.MULTIVIEW = false;\n        this.ORDER_INDEPENDENT_TRANSPARENCY = false;\n        this.ORDER_INDEPENDENT_TRANSPARENCY_16BITS = false;\n        this.CAMERA_ORTHOGRAPHIC = false;\n        this.CAMERA_PERSPECTIVE = false;\n        this.AREALIGHTSUPPORTED = true;\n        /**\n         * If the reflection texture on this material is in linear color space\n         * @internal\n         */\n        this.IS_REFLECTION_LINEAR = false;\n        /**\n         * If the refraction texture on this material is in linear color space\n         * @internal\n         */\n        this.IS_REFRACTION_LINEAR = false;\n        this.EXPOSURE = false;\n        this.DECAL_AFTER_DETAIL = false;\n        this.rebuild();\n    }\n    setReflectionMode(modeToEnable) {\n        const modes = [\n            \"REFLECTIONMAP_CUBIC\",\n            \"REFLECTIONMAP_EXPLICIT\",\n            \"REFLECTIONMAP_PLANAR\",\n            \"REFLECTIONMAP_PROJECTION\",\n            \"REFLECTIONMAP_PROJECTION\",\n            \"REFLECTIONMAP_SKYBOX\",\n            \"REFLECTIONMAP_SPHERICAL\",\n            \"REFLECTIONMAP_EQUIRECTANGULAR\",\n            \"REFLECTIONMAP_EQUIRECTANGULAR_FIXED\",\n            \"REFLECTIONMAP_MIRROREDEQUIRECTANGULAR_FIXED\",\n        ];\n        for (const mode of modes) {\n            this[mode] = mode === modeToEnable;\n        }\n    }\n}\n/**\n * This is the default material used in Babylon. It is the best trade off between quality\n * and performances.\n * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/materials_introduction\n */\nexport class StandardMaterial extends PushMaterial {\n    /**\n     * Gets the image processing configuration used either in this material.\n     */\n    get imageProcessingConfiguration() {\n        return this._imageProcessingConfiguration;\n    }\n    /**\n     * Sets the Default image processing configuration used either in the this material.\n     *\n     * If sets to null, the scene one is in use.\n     */\n    set imageProcessingConfiguration(value) {\n        this._attachImageProcessingConfiguration(value);\n        // Ensure the effect will be rebuilt.\n        this._markAllSubMeshesAsTexturesDirty();\n    }\n    /**\n     * Attaches a new image processing configuration to the Standard Material.\n     * @param configuration\n     */\n    _attachImageProcessingConfiguration(configuration) {\n        if (configuration === this._imageProcessingConfiguration) {\n            return;\n        }\n        // Detaches observer\n        if (this._imageProcessingConfiguration && this._imageProcessingObserver) {\n            this._imageProcessingConfiguration.onUpdateParameters.remove(this._imageProcessingObserver);\n        }\n        // Pick the scene configuration if needed\n        if (!configuration) {\n            this._imageProcessingConfiguration = this.getScene().imageProcessingConfiguration;\n        }\n        else {\n            this._imageProcessingConfiguration = configuration;\n        }\n        // Attaches observer\n        if (this._imageProcessingConfiguration) {\n            this._imageProcessingObserver = this._imageProcessingConfiguration.onUpdateParameters.add(() => {\n                this._markAllSubMeshesAsImageProcessingDirty();\n            });\n        }\n    }\n    /**\n     * Can this material render to prepass\n     */\n    get isPrePassCapable() {\n        return !this.disableDepthWrite;\n    }\n    /**\n     * Gets whether the color curves effect is enabled.\n     */\n    get cameraColorCurvesEnabled() {\n        return this.imageProcessingConfiguration.colorCurvesEnabled;\n    }\n    /**\n     * Sets whether the color curves effect is enabled.\n     */\n    set cameraColorCurvesEnabled(value) {\n        this.imageProcessingConfiguration.colorCurvesEnabled = value;\n    }\n    /**\n     * Gets whether the color grading effect is enabled.\n     */\n    get cameraColorGradingEnabled() {\n        return this.imageProcessingConfiguration.colorGradingEnabled;\n    }\n    /**\n     * Gets whether the color grading effect is enabled.\n     */\n    set cameraColorGradingEnabled(value) {\n        this.imageProcessingConfiguration.colorGradingEnabled = value;\n    }\n    /**\n     * Gets whether tonemapping is enabled or not.\n     */\n    get cameraToneMappingEnabled() {\n        return this._imageProcessingConfiguration.toneMappingEnabled;\n    }\n    /**\n     * Sets whether tonemapping is enabled or not\n     */\n    set cameraToneMappingEnabled(value) {\n        this._imageProcessingConfiguration.toneMappingEnabled = value;\n    }\n    /**\n     * The camera exposure used on this material.\n     * This property is here and not in the camera to allow controlling exposure without full screen post process.\n     * This corresponds to a photographic exposure.\n     */\n    get cameraExposure() {\n        return this._imageProcessingConfiguration.exposure;\n    }\n    /**\n     * The camera exposure used on this material.\n     * This property is here and not in the camera to allow controlling exposure without full screen post process.\n     * This corresponds to a photographic exposure.\n     */\n    set cameraExposure(value) {\n        this._imageProcessingConfiguration.exposure = value;\n    }\n    /**\n     * Gets The camera contrast used on this material.\n     */\n    get cameraContrast() {\n        return this._imageProcessingConfiguration.contrast;\n    }\n    /**\n     * Sets The camera contrast used on this material.\n     */\n    set cameraContrast(value) {\n        this._imageProcessingConfiguration.contrast = value;\n    }\n    /**\n     * Gets the Color Grading 2D Lookup Texture.\n     */\n    get cameraColorGradingTexture() {\n        return this._imageProcessingConfiguration.colorGradingTexture;\n    }\n    /**\n     * Sets the Color Grading 2D Lookup Texture.\n     */\n    set cameraColorGradingTexture(value) {\n        this._imageProcessingConfiguration.colorGradingTexture = value;\n    }\n    /**\n     * The color grading curves provide additional color adjustmnent that is applied after any color grading transform (3D LUT).\n     * They allow basic adjustment of saturation and small exposure adjustments, along with color filter tinting to provide white balance adjustment or more stylistic effects.\n     * These are similar to controls found in many professional imaging or colorist software. The global controls are applied to the entire image. For advanced tuning, extra controls are provided to adjust the shadow, midtone and highlight areas of the image;\n     * corresponding to low luminance, medium luminance, and high luminance areas respectively.\n     */\n    get cameraColorCurves() {\n        return this._imageProcessingConfiguration.colorCurves;\n    }\n    /**\n     * The color grading curves provide additional color adjustment that is applied after any color grading transform (3D LUT).\n     * They allow basic adjustment of saturation and small exposure adjustments, along with color filter tinting to provide white balance adjustment or more stylistic effects.\n     * These are similar to controls found in many professional imaging or colorist software. The global controls are applied to the entire image. For advanced tuning, extra controls are provided to adjust the shadow, midtone and highlight areas of the image;\n     * corresponding to low luminance, medium luminance, and high luminance areas respectively.\n     */\n    set cameraColorCurves(value) {\n        this._imageProcessingConfiguration.colorCurves = value;\n    }\n    /**\n     * Can this material render to several textures at once\n     */\n    get canRenderToMRT() {\n        return true;\n    }\n    /**\n     * Instantiates a new standard material.\n     * This is the default material used in Babylon. It is the best trade off between quality\n     * and performances.\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/materials_introduction\n     * @param name Define the name of the material in the scene\n     * @param scene Define the scene the material belong to\n     * @param forceGLSL Use the GLSL code generation for the shader (even on WebGPU). Default is false\n     */\n    constructor(name, scene, forceGLSL = false) {\n        super(name, scene, undefined, forceGLSL || StandardMaterial.ForceGLSL);\n        this._diffuseTexture = null;\n        this._ambientTexture = null;\n        this._opacityTexture = null;\n        this._reflectionTexture = null;\n        this._emissiveTexture = null;\n        this._specularTexture = null;\n        this._bumpTexture = null;\n        this._lightmapTexture = null;\n        this._refractionTexture = null;\n        /**\n         * The color of the material lit by the environmental background lighting.\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/materials_introduction#ambient-color-example\n         */\n        this.ambientColor = new Color3(0, 0, 0);\n        /**\n         * The basic color of the material as viewed under a light.\n         */\n        this.diffuseColor = new Color3(1, 1, 1);\n        /**\n         * Define how the color and intensity of the highlight given by the light in the material.\n         */\n        this.specularColor = new Color3(1, 1, 1);\n        /**\n         * Define the color of the material as if self lit.\n         * This will be mixed in the final result even in the absence of light.\n         */\n        this.emissiveColor = new Color3(0, 0, 0);\n        /**\n         * Defines how sharp are the highlights in the material.\n         * The bigger the value the sharper giving a more glossy feeling to the result.\n         * Reversely, the smaller the value the blurrier giving a more rough feeling to the result.\n         */\n        this.specularPower = 64;\n        this._useAlphaFromDiffuseTexture = false;\n        this._useEmissiveAsIllumination = false;\n        this._linkEmissiveWithDiffuse = false;\n        this._useSpecularOverAlpha = false;\n        this._useReflectionOverAlpha = false;\n        this._disableLighting = false;\n        this._useObjectSpaceNormalMap = false;\n        this._useParallax = false;\n        this._useParallaxOcclusion = false;\n        /**\n         * Apply a scaling factor that determine which \"depth\" the height map should reprensent. A value between 0.05 and 0.1 is reasonnable in Parallax, you can reach 0.2 using Parallax Occlusion.\n         */\n        this.parallaxScaleBias = 0.05;\n        this._roughness = 0;\n        /**\n         * In case of refraction, define the value of the index of refraction.\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/reflectionTexture#how-to-obtain-reflections-and-refractions\n         */\n        this.indexOfRefraction = 0.98;\n        /**\n         * Invert the refraction texture alongside the y axis.\n         * It can be useful with procedural textures or probe for instance.\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/reflectionTexture#how-to-obtain-reflections-and-refractions\n         */\n        this.invertRefractionY = true;\n        /**\n         * Defines the alpha limits in alpha test mode.\n         */\n        this.alphaCutOff = 0.4;\n        this._useLightmapAsShadowmap = false;\n        this._useReflectionFresnelFromSpecular = false;\n        this._useGlossinessFromSpecularMapAlpha = false;\n        this._maxSimultaneousLights = 4;\n        this._invertNormalMapX = false;\n        this._invertNormalMapY = false;\n        this._twoSidedLighting = false;\n        this._applyDecalMapAfterDetailMap = false;\n        this._shadersLoaded = false;\n        this._renderTargets = new SmartArray(16);\n        this._globalAmbientColor = new Color3(0, 0, 0);\n        this._cacheHasRenderTargetTextures = false;\n        this.detailMap = new DetailMapConfiguration(this);\n        // Setup the default processing configuration to the scene.\n        this._attachImageProcessingConfiguration(null);\n        this.prePassConfiguration = new PrePassConfiguration();\n        this.getRenderTargetTextures = () => {\n            this._renderTargets.reset();\n            if (StandardMaterial.ReflectionTextureEnabled && this._reflectionTexture && this._reflectionTexture.isRenderTarget) {\n                this._renderTargets.push(this._reflectionTexture);\n            }\n            if (StandardMaterial.RefractionTextureEnabled && this._refractionTexture && this._refractionTexture.isRenderTarget) {\n                this._renderTargets.push(this._refractionTexture);\n            }\n            this._eventInfo.renderTargets = this._renderTargets;\n            this._callbackPluginEventFillRenderTargetTextures(this._eventInfo);\n            return this._renderTargets;\n        };\n    }\n    /**\n     * Gets a boolean indicating that current material needs to register RTT\n     */\n    get hasRenderTargetTextures() {\n        if (StandardMaterial.ReflectionTextureEnabled && this._reflectionTexture && this._reflectionTexture.isRenderTarget) {\n            return true;\n        }\n        if (StandardMaterial.RefractionTextureEnabled && this._refractionTexture && this._refractionTexture.isRenderTarget) {\n            return true;\n        }\n        return this._cacheHasRenderTargetTextures;\n    }\n    /**\n     * Gets the current class name of the material e.g. \"StandardMaterial\"\n     * Mainly use in serialization.\n     * @returns the class name\n     */\n    getClassName() {\n        return \"StandardMaterial\";\n    }\n    /**\n     * Specifies if the material will require alpha blending\n     * @returns a boolean specifying if alpha blending is needed\n     */\n    needAlphaBlending() {\n        if (this._hasTransparencyMode) {\n            return this._transparencyModeIsBlend;\n        }\n        if (this._disableAlphaBlending) {\n            return false;\n        }\n        return (this.alpha < 1.0 ||\n            this._opacityTexture != null ||\n            this._shouldUseAlphaFromDiffuseTexture() ||\n            (this._opacityFresnelParameters && this._opacityFresnelParameters.isEnabled));\n    }\n    /**\n     * Specifies if this material should be rendered in alpha test mode\n     * @returns a boolean specifying if an alpha test is needed.\n     */\n    needAlphaTesting() {\n        if (this._hasTransparencyMode) {\n            return this._transparencyModeIsTest;\n        }\n        return this._hasAlphaChannel() && (this._transparencyMode == null || this._transparencyMode === Material.MATERIAL_ALPHATEST);\n    }\n    /**\n     * @returns whether or not the alpha value of the diffuse texture should be used for alpha blending.\n     */\n    _shouldUseAlphaFromDiffuseTexture() {\n        return this._diffuseTexture != null && this._diffuseTexture.hasAlpha && this._useAlphaFromDiffuseTexture && this._transparencyMode !== Material.MATERIAL_OPAQUE;\n    }\n    /**\n     * @returns whether or not there is a usable alpha channel for transparency.\n     */\n    _hasAlphaChannel() {\n        return (this._diffuseTexture != null && this._diffuseTexture.hasAlpha) || this._opacityTexture != null;\n    }\n    /**\n     * Get the texture used for alpha test purpose.\n     * @returns the diffuse texture in case of the standard material.\n     */\n    getAlphaTestTexture() {\n        return this._diffuseTexture;\n    }\n    /**\n     * Get if the submesh is ready to be used and all its information available.\n     * Child classes can use it to update shaders\n     * @param mesh defines the mesh to check\n     * @param subMesh defines which submesh to check\n     * @param useInstances specifies that instances should be used\n     * @returns a boolean indicating that the submesh is ready or not\n     */\n    isReadyForSubMesh(mesh, subMesh, useInstances = false) {\n        if (!this._uniformBufferLayoutBuilt) {\n            this.buildUniformLayout();\n        }\n        const drawWrapper = subMesh._drawWrapper;\n        if (drawWrapper.effect && this.isFrozen) {\n            if (drawWrapper._wasPreviouslyReady && drawWrapper._wasPreviouslyUsingInstances === useInstances) {\n                return true;\n            }\n        }\n        if (!subMesh.materialDefines) {\n            this._callbackPluginEventGeneric(4 /* MaterialPluginEvent.GetDefineNames */, this._eventInfo);\n            subMesh.materialDefines = new StandardMaterialDefines(this._eventInfo.defineNames);\n        }\n        const scene = this.getScene();\n        const defines = subMesh.materialDefines;\n        if (this._isReadyForSubMesh(subMesh)) {\n            return true;\n        }\n        const engine = scene.getEngine();\n        // Lights\n        defines._needNormals = PrepareDefinesForLights(scene, mesh, defines, true, this._maxSimultaneousLights, this._disableLighting);\n        // Multiview\n        PrepareDefinesForMultiview(scene, defines);\n        // PrePass\n        const oit = this.needAlphaBlendingForMesh(mesh) && this.getScene().useOrderIndependentTransparency;\n        PrepareDefinesForPrePass(scene, defines, this.canRenderToMRT && !oit);\n        // Order independant transparency\n        PrepareDefinesForOIT(scene, defines, oit);\n        MaterialHelperGeometryRendering.PrepareDefines(engine.currentRenderPassId, mesh, defines);\n        // Textures\n        if (defines._areTexturesDirty) {\n            this._eventInfo.hasRenderTargetTextures = false;\n            this._callbackPluginEventHasRenderTargetTextures(this._eventInfo);\n            this._cacheHasRenderTargetTextures = this._eventInfo.hasRenderTargetTextures;\n            defines._needUVs = false;\n            for (let i = 1; i <= 6; ++i) {\n                defines[\"MAINUV\" + i] = false;\n            }\n            if (scene.texturesEnabled) {\n                defines.DIFFUSEDIRECTUV = 0;\n                defines.BUMPDIRECTUV = 0;\n                defines.AMBIENTDIRECTUV = 0;\n                defines.OPACITYDIRECTUV = 0;\n                defines.EMISSIVEDIRECTUV = 0;\n                defines.SPECULARDIRECTUV = 0;\n                defines.LIGHTMAPDIRECTUV = 0;\n                if (this._diffuseTexture && StandardMaterial.DiffuseTextureEnabled) {\n                    if (!this._diffuseTexture.isReadyOrNotBlocking()) {\n                        return false;\n                    }\n                    else {\n                        PrepareDefinesForMergedUV(this._diffuseTexture, defines, \"DIFFUSE\");\n                    }\n                }\n                else {\n                    defines.DIFFUSE = false;\n                }\n                if (this._ambientTexture && StandardMaterial.AmbientTextureEnabled) {\n                    if (!this._ambientTexture.isReadyOrNotBlocking()) {\n                        return false;\n                    }\n                    else {\n                        PrepareDefinesForMergedUV(this._ambientTexture, defines, \"AMBIENT\");\n                    }\n                }\n                else {\n                    defines.AMBIENT = false;\n                }\n                if (this._opacityTexture && StandardMaterial.OpacityTextureEnabled) {\n                    if (!this._opacityTexture.isReadyOrNotBlocking()) {\n                        return false;\n                    }\n                    else {\n                        PrepareDefinesForMergedUV(this._opacityTexture, defines, \"OPACITY\");\n                        defines.OPACITYRGB = this._opacityTexture.getAlphaFromRGB;\n                    }\n                }\n                else {\n                    defines.OPACITY = false;\n                }\n                if (this._reflectionTexture && StandardMaterial.ReflectionTextureEnabled) {\n                    if (!this._reflectionTexture.isReadyOrNotBlocking()) {\n                        return false;\n                    }\n                    else {\n                        defines._needNormals = true;\n                        defines.REFLECTION = true;\n                        defines.ROUGHNESS = this._roughness > 0;\n                        defines.REFLECTIONOVERALPHA = this._useReflectionOverAlpha;\n                        defines.INVERTCUBICMAP = this._reflectionTexture.coordinatesMode === Texture.INVCUBIC_MODE;\n                        defines.REFLECTIONMAP_3D = this._reflectionTexture.isCube;\n                        defines.REFLECTIONMAP_OPPOSITEZ =\n                            defines.REFLECTIONMAP_3D && this.getScene().useRightHandedSystem ? !this._reflectionTexture.invertZ : this._reflectionTexture.invertZ;\n                        defines.RGBDREFLECTION = this._reflectionTexture.isRGBD;\n                        switch (this._reflectionTexture.coordinatesMode) {\n                            case Texture.EXPLICIT_MODE:\n                                defines.setReflectionMode(\"REFLECTIONMAP_EXPLICIT\");\n                                break;\n                            case Texture.PLANAR_MODE:\n                                defines.setReflectionMode(\"REFLECTIONMAP_PLANAR\");\n                                break;\n                            case Texture.PROJECTION_MODE:\n                                defines.setReflectionMode(\"REFLECTIONMAP_PROJECTION\");\n                                break;\n                            case Texture.SKYBOX_MODE:\n                                defines.setReflectionMode(\"REFLECTIONMAP_SKYBOX\");\n                                break;\n                            case Texture.SPHERICAL_MODE:\n                                defines.setReflectionMode(\"REFLECTIONMAP_SPHERICAL\");\n                                break;\n                            case Texture.EQUIRECTANGULAR_MODE:\n                                defines.setReflectionMode(\"REFLECTIONMAP_EQUIRECTANGULAR\");\n                                break;\n                            case Texture.FIXED_EQUIRECTANGULAR_MODE:\n                                defines.setReflectionMode(\"REFLECTIONMAP_EQUIRECTANGULAR_FIXED\");\n                                break;\n                            case Texture.FIXED_EQUIRECTANGULAR_MIRRORED_MODE:\n                                defines.setReflectionMode(\"REFLECTIONMAP_MIRROREDEQUIRECTANGULAR_FIXED\");\n                                break;\n                            case Texture.CUBIC_MODE:\n                            case Texture.INVCUBIC_MODE:\n                            default:\n                                defines.setReflectionMode(\"REFLECTIONMAP_CUBIC\");\n                                break;\n                        }\n                        defines.USE_LOCAL_REFLECTIONMAP_CUBIC = this._reflectionTexture.boundingBoxSize ? true : false;\n                    }\n                }\n                else {\n                    defines.REFLECTION = false;\n                    defines.REFLECTIONMAP_OPPOSITEZ = false;\n                }\n                if (this._emissiveTexture && StandardMaterial.EmissiveTextureEnabled) {\n                    if (!this._emissiveTexture.isReadyOrNotBlocking()) {\n                        return false;\n                    }\n                    else {\n                        PrepareDefinesForMergedUV(this._emissiveTexture, defines, \"EMISSIVE\");\n                    }\n                }\n                else {\n                    defines.EMISSIVE = false;\n                }\n                if (this._lightmapTexture && StandardMaterial.LightmapTextureEnabled) {\n                    if (!this._lightmapTexture.isReadyOrNotBlocking()) {\n                        return false;\n                    }\n                    else {\n                        PrepareDefinesForMergedUV(this._lightmapTexture, defines, \"LIGHTMAP\");\n                        defines.USELIGHTMAPASSHADOWMAP = this._useLightmapAsShadowmap;\n                        defines.RGBDLIGHTMAP = this._lightmapTexture.isRGBD;\n                    }\n                }\n                else {\n                    defines.LIGHTMAP = false;\n                }\n                if (this._specularTexture && StandardMaterial.SpecularTextureEnabled) {\n                    if (!this._specularTexture.isReadyOrNotBlocking()) {\n                        return false;\n                    }\n                    else {\n                        PrepareDefinesForMergedUV(this._specularTexture, defines, \"SPECULAR\");\n                        defines.GLOSSINESS = this._useGlossinessFromSpecularMapAlpha;\n                    }\n                }\n                else {\n                    defines.SPECULAR = false;\n                }\n                if (scene.getEngine().getCaps().standardDerivatives && this._bumpTexture && StandardMaterial.BumpTextureEnabled) {\n                    // Bump texture can not be not blocking.\n                    if (!this._bumpTexture.isReady()) {\n                        return false;\n                    }\n                    else {\n                        PrepareDefinesForMergedUV(this._bumpTexture, defines, \"BUMP\");\n                        defines.PARALLAX = this._useParallax;\n                        defines.PARALLAX_RHS = scene.useRightHandedSystem;\n                        defines.PARALLAXOCCLUSION = this._useParallaxOcclusion;\n                    }\n                    defines.OBJECTSPACE_NORMALMAP = this._useObjectSpaceNormalMap;\n                }\n                else {\n                    defines.BUMP = false;\n                    defines.PARALLAX = false;\n                    defines.PARALLAX_RHS = false;\n                    defines.PARALLAXOCCLUSION = false;\n                }\n                if (this._refractionTexture && StandardMaterial.RefractionTextureEnabled) {\n                    if (!this._refractionTexture.isReadyOrNotBlocking()) {\n                        return false;\n                    }\n                    else {\n                        defines._needUVs = true;\n                        defines.REFRACTION = true;\n                        defines.REFRACTIONMAP_3D = this._refractionTexture.isCube;\n                        defines.RGBDREFRACTION = this._refractionTexture.isRGBD;\n                        defines.USE_LOCAL_REFRACTIONMAP_CUBIC = this._refractionTexture.boundingBoxSize ? true : false;\n                    }\n                }\n                else {\n                    defines.REFRACTION = false;\n                }\n                defines.TWOSIDEDLIGHTING = !this._backFaceCulling && this._twoSidedLighting;\n            }\n            else {\n                defines.DIFFUSE = false;\n                defines.AMBIENT = false;\n                defines.OPACITY = false;\n                defines.REFLECTION = false;\n                defines.EMISSIVE = false;\n                defines.LIGHTMAP = false;\n                defines.BUMP = false;\n                defines.REFRACTION = false;\n            }\n            defines.ALPHAFROMDIFFUSE = this._shouldUseAlphaFromDiffuseTexture();\n            defines.EMISSIVEASILLUMINATION = this._useEmissiveAsIllumination;\n            defines.LINKEMISSIVEWITHDIFFUSE = this._linkEmissiveWithDiffuse;\n            defines.SPECULAROVERALPHA = this._useSpecularOverAlpha;\n            defines.PREMULTIPLYALPHA = this.alphaMode === 7 || this.alphaMode === 8;\n            defines.ALPHATEST_AFTERALLALPHACOMPUTATIONS = this.transparencyMode !== null;\n            defines.ALPHABLEND = this.transparencyMode === null || this.needAlphaBlendingForMesh(mesh); // check on null for backward compatibility\n        }\n        this._eventInfo.isReadyForSubMesh = true;\n        this._eventInfo.defines = defines;\n        this._eventInfo.subMesh = subMesh;\n        this._callbackPluginEventIsReadyForSubMesh(this._eventInfo);\n        if (!this._eventInfo.isReadyForSubMesh) {\n            return false;\n        }\n        if (defines._areImageProcessingDirty && this._imageProcessingConfiguration) {\n            if (!this._imageProcessingConfiguration.isReady()) {\n                return false;\n            }\n            this._imageProcessingConfiguration.prepareDefines(defines);\n            defines.IS_REFLECTION_LINEAR = this.reflectionTexture != null && !this.reflectionTexture.gammaSpace;\n            defines.IS_REFRACTION_LINEAR = this.refractionTexture != null && !this.refractionTexture.gammaSpace;\n        }\n        if (defines._areFresnelDirty) {\n            if (StandardMaterial.FresnelEnabled) {\n                // Fresnel\n                if (this._diffuseFresnelParameters ||\n                    this._opacityFresnelParameters ||\n                    this._emissiveFresnelParameters ||\n                    this._refractionFresnelParameters ||\n                    this._reflectionFresnelParameters) {\n                    defines.DIFFUSEFRESNEL = this._diffuseFresnelParameters && this._diffuseFresnelParameters.isEnabled;\n                    defines.OPACITYFRESNEL = this._opacityFresnelParameters && this._opacityFresnelParameters.isEnabled;\n                    defines.REFLECTIONFRESNEL = this._reflectionFresnelParameters && this._reflectionFresnelParameters.isEnabled;\n                    defines.REFLECTIONFRESNELFROMSPECULAR = this._useReflectionFresnelFromSpecular;\n                    defines.REFRACTIONFRESNEL = this._refractionFresnelParameters && this._refractionFresnelParameters.isEnabled;\n                    defines.EMISSIVEFRESNEL = this._emissiveFresnelParameters && this._emissiveFresnelParameters.isEnabled;\n                    defines._needNormals = true;\n                    defines.FRESNEL = true;\n                }\n            }\n            else {\n                defines.FRESNEL = false;\n            }\n        }\n        // Check if Area Lights have LTC texture.\n        if (defines[\"AREALIGHTUSED\"]) {\n            for (let index = 0; index < mesh.lightSources.length; index++) {\n                if (!mesh.lightSources[index]._isReady()) {\n                    return false;\n                }\n            }\n        }\n        // Misc.\n        PrepareDefinesForMisc(mesh, scene, this._useLogarithmicDepth, this.pointsCloud, this.fogEnabled, this.needAlphaTestingForMesh(mesh), defines, this._applyDecalMapAfterDetailMap);\n        // Values that need to be evaluated on every frame\n        PrepareDefinesForFrameBoundValues(scene, engine, this, defines, useInstances, null, subMesh.getRenderingMesh().hasThinInstances);\n        // External config\n        this._eventInfo.defines = defines;\n        this._eventInfo.mesh = mesh;\n        this._callbackPluginEventPrepareDefinesBeforeAttributes(this._eventInfo);\n        // Attribs\n        PrepareDefinesForAttributes(mesh, defines, true, true, true);\n        // External config\n        this._callbackPluginEventPrepareDefines(this._eventInfo);\n        // Get correct effect\n        let forceWasNotReadyPreviously = false;\n        if (defines.isDirty) {\n            const lightDisposed = defines._areLightsDisposed;\n            defines.markAsProcessed();\n            // Fallbacks\n            const fallbacks = new EffectFallbacks();\n            if (defines.REFLECTION) {\n                fallbacks.addFallback(0, \"REFLECTION\");\n            }\n            if (defines.SPECULAR) {\n                fallbacks.addFallback(0, \"SPECULAR\");\n            }\n            if (defines.BUMP) {\n                fallbacks.addFallback(0, \"BUMP\");\n            }\n            if (defines.PARALLAX) {\n                fallbacks.addFallback(1, \"PARALLAX\");\n            }\n            if (defines.PARALLAX_RHS) {\n                fallbacks.addFallback(1, \"PARALLAX_RHS\");\n            }\n            if (defines.PARALLAXOCCLUSION) {\n                fallbacks.addFallback(0, \"PARALLAXOCCLUSION\");\n            }\n            if (defines.SPECULAROVERALPHA) {\n                fallbacks.addFallback(0, \"SPECULAROVERALPHA\");\n            }\n            if (defines.FOG) {\n                fallbacks.addFallback(1, \"FOG\");\n            }\n            if (defines.POINTSIZE) {\n                fallbacks.addFallback(0, \"POINTSIZE\");\n            }\n            if (defines.LOGARITHMICDEPTH) {\n                fallbacks.addFallback(0, \"LOGARITHMICDEPTH\");\n            }\n            HandleFallbacksForShadows(defines, fallbacks, this._maxSimultaneousLights);\n            if (defines.SPECULARTERM) {\n                fallbacks.addFallback(0, \"SPECULARTERM\");\n            }\n            if (defines.DIFFUSEFRESNEL) {\n                fallbacks.addFallback(1, \"DIFFUSEFRESNEL\");\n            }\n            if (defines.OPACITYFRESNEL) {\n                fallbacks.addFallback(2, \"OPACITYFRESNEL\");\n            }\n            if (defines.REFLECTIONFRESNEL) {\n                fallbacks.addFallback(3, \"REFLECTIONFRESNEL\");\n            }\n            if (defines.EMISSIVEFRESNEL) {\n                fallbacks.addFallback(4, \"EMISSIVEFRESNEL\");\n            }\n            if (defines.FRESNEL) {\n                fallbacks.addFallback(4, \"FRESNEL\");\n            }\n            if (defines.MULTIVIEW) {\n                fallbacks.addFallback(0, \"MULTIVIEW\");\n            }\n            //Attributes\n            const attribs = [VertexBuffer.PositionKind];\n            if (defines.NORMAL) {\n                attribs.push(VertexBuffer.NormalKind);\n            }\n            if (defines.TANGENT) {\n                attribs.push(VertexBuffer.TangentKind);\n            }\n            for (let i = 1; i <= 6; ++i) {\n                if (defines[\"UV\" + i]) {\n                    attribs.push(`uv${i === 1 ? \"\" : i}`);\n                }\n            }\n            if (defines.VERTEXCOLOR) {\n                attribs.push(VertexBuffer.ColorKind);\n            }\n            PrepareAttributesForBones(attribs, mesh, defines, fallbacks);\n            PrepareAttributesForInstances(attribs, defines);\n            PrepareAttributesForMorphTargets(attribs, mesh, defines);\n            PrepareAttributesForBakedVertexAnimation(attribs, mesh, defines);\n            let shaderName = \"default\";\n            const uniforms = [\n                \"world\",\n                \"view\",\n                \"viewProjection\",\n                \"vEyePosition\",\n                \"vLightsType\",\n                \"vAmbientColor\",\n                \"vDiffuseColor\",\n                \"vSpecularColor\",\n                \"vEmissiveColor\",\n                \"visibility\",\n                \"vFogInfos\",\n                \"vFogColor\",\n                \"pointSize\",\n                \"vDiffuseInfos\",\n                \"vAmbientInfos\",\n                \"vOpacityInfos\",\n                \"vReflectionInfos\",\n                \"vEmissiveInfos\",\n                \"vSpecularInfos\",\n                \"vBumpInfos\",\n                \"vLightmapInfos\",\n                \"vRefractionInfos\",\n                \"mBones\",\n                \"diffuseMatrix\",\n                \"ambientMatrix\",\n                \"opacityMatrix\",\n                \"reflectionMatrix\",\n                \"emissiveMatrix\",\n                \"specularMatrix\",\n                \"bumpMatrix\",\n                \"normalMatrix\",\n                \"lightmapMatrix\",\n                \"refractionMatrix\",\n                \"diffuseLeftColor\",\n                \"diffuseRightColor\",\n                \"opacityParts\",\n                \"reflectionLeftColor\",\n                \"reflectionRightColor\",\n                \"emissiveLeftColor\",\n                \"emissiveRightColor\",\n                \"refractionLeftColor\",\n                \"refractionRightColor\",\n                \"vReflectionPosition\",\n                \"vReflectionSize\",\n                \"vRefractionPosition\",\n                \"vRefractionSize\",\n                \"logarithmicDepthConstant\",\n                \"vTangentSpaceParams\",\n                \"alphaCutOff\",\n                \"boneTextureWidth\",\n                \"morphTargetTextureInfo\",\n                \"morphTargetTextureIndices\",\n            ];\n            const samplers = [\n                \"diffuseSampler\",\n                \"ambientSampler\",\n                \"opacitySampler\",\n                \"reflectionCubeSampler\",\n                \"reflection2DSampler\",\n                \"emissiveSampler\",\n                \"specularSampler\",\n                \"bumpSampler\",\n                \"lightmapSampler\",\n                \"refractionCubeSampler\",\n                \"refraction2DSampler\",\n                \"boneSampler\",\n                \"morphTargets\",\n                \"oitDepthSampler\",\n                \"oitFrontColorSampler\",\n                \"areaLightsLTC1Sampler\",\n                \"areaLightsLTC2Sampler\",\n            ];\n            const uniformBuffers = [\"Material\", \"Scene\", \"Mesh\"];\n            const indexParameters = { maxSimultaneousLights: this._maxSimultaneousLights, maxSimultaneousMorphTargets: defines.NUM_MORPH_INFLUENCERS };\n            this._eventInfo.fallbacks = fallbacks;\n            this._eventInfo.fallbackRank = 0;\n            this._eventInfo.defines = defines;\n            this._eventInfo.uniforms = uniforms;\n            this._eventInfo.attributes = attribs;\n            this._eventInfo.samplers = samplers;\n            this._eventInfo.uniformBuffersNames = uniformBuffers;\n            this._eventInfo.customCode = undefined;\n            this._eventInfo.mesh = mesh;\n            this._eventInfo.indexParameters = indexParameters;\n            this._callbackPluginEventGeneric(128 /* MaterialPluginEvent.PrepareEffect */, this._eventInfo);\n            MaterialHelperGeometryRendering.AddUniformsAndSamplers(uniforms, samplers);\n            PrePassConfiguration.AddUniforms(uniforms);\n            PrePassConfiguration.AddSamplers(samplers);\n            if (ImageProcessingConfiguration) {\n                ImageProcessingConfiguration.PrepareUniforms(uniforms, defines);\n                ImageProcessingConfiguration.PrepareSamplers(samplers, defines);\n            }\n            PrepareUniformsAndSamplersList({\n                uniformsNames: uniforms,\n                uniformBuffersNames: uniformBuffers,\n                samplers: samplers,\n                defines: defines,\n                maxSimultaneousLights: this._maxSimultaneousLights,\n            });\n            addClipPlaneUniforms(uniforms);\n            const csnrOptions = {};\n            if (this.customShaderNameResolve) {\n                shaderName = this.customShaderNameResolve(shaderName, uniforms, uniformBuffers, samplers, defines, attribs, csnrOptions);\n            }\n            const join = defines.toString();\n            const previousEffect = subMesh.effect;\n            let effect = scene.getEngine().createEffect(shaderName, {\n                attributes: attribs,\n                uniformsNames: uniforms,\n                uniformBuffersNames: uniformBuffers,\n                samplers: samplers,\n                defines: join,\n                fallbacks: fallbacks,\n                onCompiled: this.onCompiled,\n                onError: this.onError,\n                indexParameters,\n                processFinalCode: csnrOptions.processFinalCode,\n                processCodeAfterIncludes: this._eventInfo.customCode,\n                multiTarget: defines.PREPASS,\n                shaderLanguage: this._shaderLanguage,\n                extraInitializationsAsync: this._shadersLoaded\n                    ? undefined\n                    : async () => {\n                        if (this._shaderLanguage === 1 /* ShaderLanguage.WGSL */) {\n                            await Promise.all([import(\"../ShadersWGSL/default.vertex.js\"), import(\"../ShadersWGSL/default.fragment.js\")]);\n                        }\n                        else {\n                            await Promise.all([import(\"../Shaders/default.vertex.js\"), import(\"../Shaders/default.fragment.js\")]);\n                        }\n                        this._shadersLoaded = true;\n                    },\n            }, engine);\n            this._eventInfo.customCode = undefined;\n            if (effect) {\n                if (this._onEffectCreatedObservable) {\n                    onCreatedEffectParameters.effect = effect;\n                    onCreatedEffectParameters.subMesh = subMesh;\n                    this._onEffectCreatedObservable.notifyObservers(onCreatedEffectParameters);\n                }\n                // Use previous effect while new one is compiling\n                if (this.allowShaderHotSwapping && previousEffect && !effect.isReady()) {\n                    effect = previousEffect;\n                    defines.markAsUnprocessed();\n                    forceWasNotReadyPreviously = this.isFrozen;\n                    if (lightDisposed) {\n                        // re register in case it takes more than one frame.\n                        defines._areLightsDisposed = true;\n                        return false;\n                    }\n                }\n                else {\n                    scene.resetCachedMaterial();\n                    subMesh.setEffect(effect, defines, this._materialContext);\n                }\n            }\n        }\n        if (!subMesh.effect || !subMesh.effect.isReady()) {\n            return false;\n        }\n        defines._renderId = scene.getRenderId();\n        drawWrapper._wasPreviouslyReady = forceWasNotReadyPreviously ? false : true;\n        drawWrapper._wasPreviouslyUsingInstances = useInstances;\n        this._checkScenePerformancePriority();\n        return true;\n    }\n    /**\n     * Builds the material UBO layouts.\n     * Used internally during the effect preparation.\n     */\n    buildUniformLayout() {\n        // Order is important !\n        const ubo = this._uniformBuffer;\n        ubo.addUniform(\"diffuseLeftColor\", 4);\n        ubo.addUniform(\"diffuseRightColor\", 4);\n        ubo.addUniform(\"opacityParts\", 4);\n        ubo.addUniform(\"reflectionLeftColor\", 4);\n        ubo.addUniform(\"reflectionRightColor\", 4);\n        ubo.addUniform(\"refractionLeftColor\", 4);\n        ubo.addUniform(\"refractionRightColor\", 4);\n        ubo.addUniform(\"emissiveLeftColor\", 4);\n        ubo.addUniform(\"emissiveRightColor\", 4);\n        ubo.addUniform(\"vDiffuseInfos\", 2);\n        ubo.addUniform(\"vAmbientInfos\", 2);\n        ubo.addUniform(\"vOpacityInfos\", 2);\n        ubo.addUniform(\"vReflectionInfos\", 2);\n        ubo.addUniform(\"vReflectionPosition\", 3);\n        ubo.addUniform(\"vReflectionSize\", 3);\n        ubo.addUniform(\"vEmissiveInfos\", 2);\n        ubo.addUniform(\"vLightmapInfos\", 2);\n        ubo.addUniform(\"vSpecularInfos\", 2);\n        ubo.addUniform(\"vBumpInfos\", 3);\n        ubo.addUniform(\"diffuseMatrix\", 16);\n        ubo.addUniform(\"ambientMatrix\", 16);\n        ubo.addUniform(\"opacityMatrix\", 16);\n        ubo.addUniform(\"reflectionMatrix\", 16);\n        ubo.addUniform(\"emissiveMatrix\", 16);\n        ubo.addUniform(\"lightmapMatrix\", 16);\n        ubo.addUniform(\"specularMatrix\", 16);\n        ubo.addUniform(\"bumpMatrix\", 16);\n        ubo.addUniform(\"vTangentSpaceParams\", 2);\n        ubo.addUniform(\"pointSize\", 1);\n        ubo.addUniform(\"alphaCutOff\", 1);\n        ubo.addUniform(\"refractionMatrix\", 16);\n        ubo.addUniform(\"vRefractionInfos\", 4);\n        ubo.addUniform(\"vRefractionPosition\", 3);\n        ubo.addUniform(\"vRefractionSize\", 3);\n        ubo.addUniform(\"vSpecularColor\", 4);\n        ubo.addUniform(\"vEmissiveColor\", 3);\n        ubo.addUniform(\"vDiffuseColor\", 4);\n        ubo.addUniform(\"vAmbientColor\", 3);\n        super.buildUniformLayout();\n    }\n    /**\n     * Binds the submesh to this material by preparing the effect and shader to draw\n     * @param world defines the world transformation matrix\n     * @param mesh defines the mesh containing the submesh\n     * @param subMesh defines the submesh to bind the material to\n     */\n    bindForSubMesh(world, mesh, subMesh) {\n        const scene = this.getScene();\n        const defines = subMesh.materialDefines;\n        if (!defines) {\n            return;\n        }\n        const effect = subMesh.effect;\n        if (!effect) {\n            return;\n        }\n        this._activeEffect = effect;\n        // Matrices Mesh.\n        mesh.getMeshUniformBuffer().bindToEffect(effect, \"Mesh\");\n        mesh.transferToEffect(world);\n        // Binding unconditionally\n        this._uniformBuffer.bindToEffect(effect, \"Material\");\n        this.prePassConfiguration.bindForSubMesh(this._activeEffect, scene, mesh, world, this.isFrozen);\n        MaterialHelperGeometryRendering.Bind(scene.getEngine().currentRenderPassId, this._activeEffect, mesh, world, this);\n        this._eventInfo.subMesh = subMesh;\n        this._callbackPluginEventHardBindForSubMesh(this._eventInfo);\n        // Normal Matrix\n        if (defines.OBJECTSPACE_NORMALMAP) {\n            world.toNormalMatrix(this._normalMatrix);\n            this.bindOnlyNormalMatrix(this._normalMatrix);\n        }\n        const mustRebind = this._mustRebind(scene, effect, subMesh, mesh.visibility);\n        // Bones\n        BindBonesParameters(mesh, effect);\n        const ubo = this._uniformBuffer;\n        if (mustRebind) {\n            this.bindViewProjection(effect);\n            if (!ubo.useUbo || !this.isFrozen || !ubo.isSync || subMesh._drawWrapper._forceRebindOnNextCall) {\n                if (StandardMaterial.FresnelEnabled && defines.FRESNEL) {\n                    // Fresnel\n                    if (this.diffuseFresnelParameters && this.diffuseFresnelParameters.isEnabled) {\n                        ubo.updateColor4(\"diffuseLeftColor\", this.diffuseFresnelParameters.leftColor, this.diffuseFresnelParameters.power);\n                        ubo.updateColor4(\"diffuseRightColor\", this.diffuseFresnelParameters.rightColor, this.diffuseFresnelParameters.bias);\n                    }\n                    if (this.opacityFresnelParameters && this.opacityFresnelParameters.isEnabled) {\n                        ubo.updateColor4(\"opacityParts\", new Color3(this.opacityFresnelParameters.leftColor.toLuminance(), this.opacityFresnelParameters.rightColor.toLuminance(), this.opacityFresnelParameters.bias), this.opacityFresnelParameters.power);\n                    }\n                    if (this.reflectionFresnelParameters && this.reflectionFresnelParameters.isEnabled) {\n                        ubo.updateColor4(\"reflectionLeftColor\", this.reflectionFresnelParameters.leftColor, this.reflectionFresnelParameters.power);\n                        ubo.updateColor4(\"reflectionRightColor\", this.reflectionFresnelParameters.rightColor, this.reflectionFresnelParameters.bias);\n                    }\n                    if (this.refractionFresnelParameters && this.refractionFresnelParameters.isEnabled) {\n                        ubo.updateColor4(\"refractionLeftColor\", this.refractionFresnelParameters.leftColor, this.refractionFresnelParameters.power);\n                        ubo.updateColor4(\"refractionRightColor\", this.refractionFresnelParameters.rightColor, this.refractionFresnelParameters.bias);\n                    }\n                    if (this.emissiveFresnelParameters && this.emissiveFresnelParameters.isEnabled) {\n                        ubo.updateColor4(\"emissiveLeftColor\", this.emissiveFresnelParameters.leftColor, this.emissiveFresnelParameters.power);\n                        ubo.updateColor4(\"emissiveRightColor\", this.emissiveFresnelParameters.rightColor, this.emissiveFresnelParameters.bias);\n                    }\n                }\n                // Textures\n                if (scene.texturesEnabled) {\n                    if (this._diffuseTexture && StandardMaterial.DiffuseTextureEnabled) {\n                        ubo.updateFloat2(\"vDiffuseInfos\", this._diffuseTexture.coordinatesIndex, this._diffuseTexture.level);\n                        BindTextureMatrix(this._diffuseTexture, ubo, \"diffuse\");\n                    }\n                    if (this._ambientTexture && StandardMaterial.AmbientTextureEnabled) {\n                        ubo.updateFloat2(\"vAmbientInfos\", this._ambientTexture.coordinatesIndex, this._ambientTexture.level);\n                        BindTextureMatrix(this._ambientTexture, ubo, \"ambient\");\n                    }\n                    if (this._opacityTexture && StandardMaterial.OpacityTextureEnabled) {\n                        ubo.updateFloat2(\"vOpacityInfos\", this._opacityTexture.coordinatesIndex, this._opacityTexture.level);\n                        BindTextureMatrix(this._opacityTexture, ubo, \"opacity\");\n                    }\n                    if (this._hasAlphaChannel()) {\n                        ubo.updateFloat(\"alphaCutOff\", this.alphaCutOff);\n                    }\n                    if (this._reflectionTexture && StandardMaterial.ReflectionTextureEnabled) {\n                        ubo.updateFloat2(\"vReflectionInfos\", this._reflectionTexture.level, this.roughness);\n                        ubo.updateMatrix(\"reflectionMatrix\", this._reflectionTexture.getReflectionTextureMatrix());\n                        if (this._reflectionTexture.boundingBoxSize) {\n                            const cubeTexture = this._reflectionTexture;\n                            ubo.updateVector3(\"vReflectionPosition\", cubeTexture.boundingBoxPosition);\n                            ubo.updateVector3(\"vReflectionSize\", cubeTexture.boundingBoxSize);\n                        }\n                    }\n                    else {\n                        ubo.updateFloat2(\"vReflectionInfos\", 0.0, this.roughness);\n                    }\n                    if (this._emissiveTexture && StandardMaterial.EmissiveTextureEnabled) {\n                        ubo.updateFloat2(\"vEmissiveInfos\", this._emissiveTexture.coordinatesIndex, this._emissiveTexture.level);\n                        BindTextureMatrix(this._emissiveTexture, ubo, \"emissive\");\n                    }\n                    if (this._lightmapTexture && StandardMaterial.LightmapTextureEnabled) {\n                        ubo.updateFloat2(\"vLightmapInfos\", this._lightmapTexture.coordinatesIndex, this._lightmapTexture.level);\n                        BindTextureMatrix(this._lightmapTexture, ubo, \"lightmap\");\n                    }\n                    if (this._specularTexture && StandardMaterial.SpecularTextureEnabled) {\n                        ubo.updateFloat2(\"vSpecularInfos\", this._specularTexture.coordinatesIndex, this._specularTexture.level);\n                        BindTextureMatrix(this._specularTexture, ubo, \"specular\");\n                    }\n                    if (this._bumpTexture && scene.getEngine().getCaps().standardDerivatives && StandardMaterial.BumpTextureEnabled) {\n                        ubo.updateFloat3(\"vBumpInfos\", this._bumpTexture.coordinatesIndex, 1.0 / this._bumpTexture.level, this.parallaxScaleBias);\n                        BindTextureMatrix(this._bumpTexture, ubo, \"bump\");\n                        if (scene._mirroredCameraPosition) {\n                            ubo.updateFloat2(\"vTangentSpaceParams\", this._invertNormalMapX ? 1.0 : -1.0, this._invertNormalMapY ? 1.0 : -1.0);\n                        }\n                        else {\n                            ubo.updateFloat2(\"vTangentSpaceParams\", this._invertNormalMapX ? -1.0 : 1.0, this._invertNormalMapY ? -1.0 : 1.0);\n                        }\n                    }\n                    if (this._refractionTexture && StandardMaterial.RefractionTextureEnabled) {\n                        let depth = 1.0;\n                        if (!this._refractionTexture.isCube) {\n                            ubo.updateMatrix(\"refractionMatrix\", this._refractionTexture.getReflectionTextureMatrix());\n                            if (this._refractionTexture.depth) {\n                                depth = this._refractionTexture.depth;\n                            }\n                        }\n                        ubo.updateFloat4(\"vRefractionInfos\", this._refractionTexture.level, this.indexOfRefraction, depth, this.invertRefractionY ? -1 : 1);\n                        if (this._refractionTexture.boundingBoxSize) {\n                            const cubeTexture = this._refractionTexture;\n                            ubo.updateVector3(\"vRefractionPosition\", cubeTexture.boundingBoxPosition);\n                            ubo.updateVector3(\"vRefractionSize\", cubeTexture.boundingBoxSize);\n                        }\n                    }\n                }\n                // Point size\n                if (this.pointsCloud) {\n                    ubo.updateFloat(\"pointSize\", this.pointSize);\n                }\n                ubo.updateColor4(\"vSpecularColor\", this.specularColor, this.specularPower);\n                ubo.updateColor3(\"vEmissiveColor\", StandardMaterial.EmissiveTextureEnabled ? this.emissiveColor : Color3.BlackReadOnly);\n                ubo.updateColor4(\"vDiffuseColor\", this.diffuseColor, this.alpha);\n                scene.ambientColor.multiplyToRef(this.ambientColor, this._globalAmbientColor);\n                ubo.updateColor3(\"vAmbientColor\", this._globalAmbientColor);\n            }\n            // Textures\n            if (scene.texturesEnabled) {\n                if (this._diffuseTexture && StandardMaterial.DiffuseTextureEnabled) {\n                    effect.setTexture(\"diffuseSampler\", this._diffuseTexture);\n                }\n                if (this._ambientTexture && StandardMaterial.AmbientTextureEnabled) {\n                    effect.setTexture(\"ambientSampler\", this._ambientTexture);\n                }\n                if (this._opacityTexture && StandardMaterial.OpacityTextureEnabled) {\n                    effect.setTexture(\"opacitySampler\", this._opacityTexture);\n                }\n                if (this._reflectionTexture && StandardMaterial.ReflectionTextureEnabled) {\n                    if (this._reflectionTexture.isCube) {\n                        effect.setTexture(\"reflectionCubeSampler\", this._reflectionTexture);\n                    }\n                    else {\n                        effect.setTexture(\"reflection2DSampler\", this._reflectionTexture);\n                    }\n                }\n                if (this._emissiveTexture && StandardMaterial.EmissiveTextureEnabled) {\n                    effect.setTexture(\"emissiveSampler\", this._emissiveTexture);\n                }\n                if (this._lightmapTexture && StandardMaterial.LightmapTextureEnabled) {\n                    effect.setTexture(\"lightmapSampler\", this._lightmapTexture);\n                }\n                if (this._specularTexture && StandardMaterial.SpecularTextureEnabled) {\n                    effect.setTexture(\"specularSampler\", this._specularTexture);\n                }\n                if (this._bumpTexture && scene.getEngine().getCaps().standardDerivatives && StandardMaterial.BumpTextureEnabled) {\n                    effect.setTexture(\"bumpSampler\", this._bumpTexture);\n                }\n                if (this._refractionTexture && StandardMaterial.RefractionTextureEnabled) {\n                    if (this._refractionTexture.isCube) {\n                        effect.setTexture(\"refractionCubeSampler\", this._refractionTexture);\n                    }\n                    else {\n                        effect.setTexture(\"refraction2DSampler\", this._refractionTexture);\n                    }\n                }\n            }\n            // OIT with depth peeling\n            if (this.getScene().useOrderIndependentTransparency && this.needAlphaBlendingForMesh(mesh)) {\n                this.getScene().depthPeelingRenderer.bind(effect);\n            }\n            this._eventInfo.subMesh = subMesh;\n            this._callbackPluginEventBindForSubMesh(this._eventInfo);\n            // Clip plane\n            bindClipPlane(effect, this, scene);\n            // Colors\n            this.bindEyePosition(effect);\n        }\n        else if (scene.getEngine()._features.needToAlwaysBindUniformBuffers) {\n            this._needToBindSceneUbo = true;\n        }\n        if (mustRebind || !this.isFrozen) {\n            // Lights\n            if (scene.lightsEnabled && !this._disableLighting) {\n                BindLights(scene, mesh, effect, defines, this._maxSimultaneousLights);\n            }\n            // View\n            if ((scene.fogEnabled && mesh.applyFog && scene.fogMode !== Scene.FOGMODE_NONE) ||\n                this._reflectionTexture ||\n                this._refractionTexture ||\n                mesh.receiveShadows ||\n                defines.PREPASS) {\n                this.bindView(effect);\n            }\n            // Fog\n            BindFogParameters(scene, mesh, effect);\n            // Morph targets\n            if (defines.NUM_MORPH_INFLUENCERS) {\n                BindMorphTargetParameters(mesh, effect);\n            }\n            if (defines.BAKED_VERTEX_ANIMATION_TEXTURE) {\n                mesh.bakedVertexAnimationManager?.bind(effect, defines.INSTANCES);\n            }\n            // Log. depth\n            if (this.useLogarithmicDepth) {\n                BindLogDepth(defines, effect, scene);\n            }\n            // image processing\n            if (this._imageProcessingConfiguration && !this._imageProcessingConfiguration.applyByPostProcess) {\n                this._imageProcessingConfiguration.bind(this._activeEffect);\n            }\n        }\n        this._afterBind(mesh, this._activeEffect, subMesh);\n        ubo.update();\n    }\n    /**\n     * Get the list of animatables in the material.\n     * @returns the list of animatables object used in the material\n     */\n    getAnimatables() {\n        const results = super.getAnimatables();\n        if (this._diffuseTexture && this._diffuseTexture.animations && this._diffuseTexture.animations.length > 0) {\n            results.push(this._diffuseTexture);\n        }\n        if (this._ambientTexture && this._ambientTexture.animations && this._ambientTexture.animations.length > 0) {\n            results.push(this._ambientTexture);\n        }\n        if (this._opacityTexture && this._opacityTexture.animations && this._opacityTexture.animations.length > 0) {\n            results.push(this._opacityTexture);\n        }\n        if (this._reflectionTexture && this._reflectionTexture.animations && this._reflectionTexture.animations.length > 0) {\n            results.push(this._reflectionTexture);\n        }\n        if (this._emissiveTexture && this._emissiveTexture.animations && this._emissiveTexture.animations.length > 0) {\n            results.push(this._emissiveTexture);\n        }\n        if (this._specularTexture && this._specularTexture.animations && this._specularTexture.animations.length > 0) {\n            results.push(this._specularTexture);\n        }\n        if (this._bumpTexture && this._bumpTexture.animations && this._bumpTexture.animations.length > 0) {\n            results.push(this._bumpTexture);\n        }\n        if (this._lightmapTexture && this._lightmapTexture.animations && this._lightmapTexture.animations.length > 0) {\n            results.push(this._lightmapTexture);\n        }\n        if (this._refractionTexture && this._refractionTexture.animations && this._refractionTexture.animations.length > 0) {\n            results.push(this._refractionTexture);\n        }\n        return results;\n    }\n    /**\n     * Gets the active textures from the material\n     * @returns an array of textures\n     */\n    getActiveTextures() {\n        const activeTextures = super.getActiveTextures();\n        if (this._diffuseTexture) {\n            activeTextures.push(this._diffuseTexture);\n        }\n        if (this._ambientTexture) {\n            activeTextures.push(this._ambientTexture);\n        }\n        if (this._opacityTexture) {\n            activeTextures.push(this._opacityTexture);\n        }\n        if (this._reflectionTexture) {\n            activeTextures.push(this._reflectionTexture);\n        }\n        if (this._emissiveTexture) {\n            activeTextures.push(this._emissiveTexture);\n        }\n        if (this._specularTexture) {\n            activeTextures.push(this._specularTexture);\n        }\n        if (this._bumpTexture) {\n            activeTextures.push(this._bumpTexture);\n        }\n        if (this._lightmapTexture) {\n            activeTextures.push(this._lightmapTexture);\n        }\n        if (this._refractionTexture) {\n            activeTextures.push(this._refractionTexture);\n        }\n        return activeTextures;\n    }\n    /**\n     * Specifies if the material uses a texture\n     * @param texture defines the texture to check against the material\n     * @returns a boolean specifying if the material uses the texture\n     */\n    hasTexture(texture) {\n        if (super.hasTexture(texture)) {\n            return true;\n        }\n        if (this._diffuseTexture === texture) {\n            return true;\n        }\n        if (this._ambientTexture === texture) {\n            return true;\n        }\n        if (this._opacityTexture === texture) {\n            return true;\n        }\n        if (this._reflectionTexture === texture) {\n            return true;\n        }\n        if (this._emissiveTexture === texture) {\n            return true;\n        }\n        if (this._specularTexture === texture) {\n            return true;\n        }\n        if (this._bumpTexture === texture) {\n            return true;\n        }\n        if (this._lightmapTexture === texture) {\n            return true;\n        }\n        if (this._refractionTexture === texture) {\n            return true;\n        }\n        return false;\n    }\n    /**\n     * Disposes the material\n     * @param forceDisposeEffect specifies if effects should be forcefully disposed\n     * @param forceDisposeTextures specifies if textures should be forcefully disposed\n     */\n    dispose(forceDisposeEffect, forceDisposeTextures) {\n        if (forceDisposeTextures) {\n            this._diffuseTexture?.dispose();\n            this._ambientTexture?.dispose();\n            this._opacityTexture?.dispose();\n            this._reflectionTexture?.dispose();\n            this._emissiveTexture?.dispose();\n            this._specularTexture?.dispose();\n            this._bumpTexture?.dispose();\n            this._lightmapTexture?.dispose();\n            this._refractionTexture?.dispose();\n        }\n        if (this._imageProcessingConfiguration && this._imageProcessingObserver) {\n            this._imageProcessingConfiguration.onUpdateParameters.remove(this._imageProcessingObserver);\n        }\n        super.dispose(forceDisposeEffect, forceDisposeTextures);\n    }\n    /**\n     * Makes a duplicate of the material, and gives it a new name\n     * @param name defines the new name for the duplicated material\n     * @param cloneTexturesOnlyOnce - if a texture is used in more than one channel (e.g diffuse and opacity), only clone it once and reuse it on the other channels. Default false.\n     * @param rootUrl defines the root URL to use to load textures\n     * @returns the cloned material\n     */\n    clone(name, cloneTexturesOnlyOnce = true, rootUrl = \"\") {\n        const result = SerializationHelper.Clone(() => new StandardMaterial(name, this.getScene()), this, { cloneTexturesOnlyOnce });\n        result.name = name;\n        result.id = name;\n        this.stencil.copyTo(result.stencil);\n        this._clonePlugins(result, rootUrl);\n        return result;\n    }\n    /**\n     * Creates a standard material from parsed material data\n     * @param source defines the JSON representation of the material\n     * @param scene defines the hosting scene\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\n     * @returns a new standard material\n     */\n    static Parse(source, scene, rootUrl) {\n        const material = SerializationHelper.Parse(() => new StandardMaterial(source.name, scene), source, scene, rootUrl);\n        if (source.stencil) {\n            material.stencil.parse(source.stencil, scene, rootUrl);\n        }\n        Material._ParsePlugins(source, material, scene, rootUrl);\n        return material;\n    }\n    // Flags used to enable or disable a type of texture for all Standard Materials\n    /**\n     * Are diffuse textures enabled in the application.\n     */\n    static get DiffuseTextureEnabled() {\n        return MaterialFlags.DiffuseTextureEnabled;\n    }\n    static set DiffuseTextureEnabled(value) {\n        MaterialFlags.DiffuseTextureEnabled = value;\n    }\n    /**\n     * Are detail textures enabled in the application.\n     */\n    static get DetailTextureEnabled() {\n        return MaterialFlags.DetailTextureEnabled;\n    }\n    static set DetailTextureEnabled(value) {\n        MaterialFlags.DetailTextureEnabled = value;\n    }\n    /**\n     * Are ambient textures enabled in the application.\n     */\n    static get AmbientTextureEnabled() {\n        return MaterialFlags.AmbientTextureEnabled;\n    }\n    static set AmbientTextureEnabled(value) {\n        MaterialFlags.AmbientTextureEnabled = value;\n    }\n    /**\n     * Are opacity textures enabled in the application.\n     */\n    static get OpacityTextureEnabled() {\n        return MaterialFlags.OpacityTextureEnabled;\n    }\n    static set OpacityTextureEnabled(value) {\n        MaterialFlags.OpacityTextureEnabled = value;\n    }\n    /**\n     * Are reflection textures enabled in the application.\n     */\n    static get ReflectionTextureEnabled() {\n        return MaterialFlags.ReflectionTextureEnabled;\n    }\n    static set ReflectionTextureEnabled(value) {\n        MaterialFlags.ReflectionTextureEnabled = value;\n    }\n    /**\n     * Are emissive textures enabled in the application.\n     */\n    static get EmissiveTextureEnabled() {\n        return MaterialFlags.EmissiveTextureEnabled;\n    }\n    static set EmissiveTextureEnabled(value) {\n        MaterialFlags.EmissiveTextureEnabled = value;\n    }\n    /**\n     * Are specular textures enabled in the application.\n     */\n    static get SpecularTextureEnabled() {\n        return MaterialFlags.SpecularTextureEnabled;\n    }\n    static set SpecularTextureEnabled(value) {\n        MaterialFlags.SpecularTextureEnabled = value;\n    }\n    /**\n     * Are bump textures enabled in the application.\n     */\n    static get BumpTextureEnabled() {\n        return MaterialFlags.BumpTextureEnabled;\n    }\n    static set BumpTextureEnabled(value) {\n        MaterialFlags.BumpTextureEnabled = value;\n    }\n    /**\n     * Are lightmap textures enabled in the application.\n     */\n    static get LightmapTextureEnabled() {\n        return MaterialFlags.LightmapTextureEnabled;\n    }\n    static set LightmapTextureEnabled(value) {\n        MaterialFlags.LightmapTextureEnabled = value;\n    }\n    /**\n     * Are refraction textures enabled in the application.\n     */\n    static get RefractionTextureEnabled() {\n        return MaterialFlags.RefractionTextureEnabled;\n    }\n    static set RefractionTextureEnabled(value) {\n        MaterialFlags.RefractionTextureEnabled = value;\n    }\n    /**\n     * Are color grading textures enabled in the application.\n     */\n    static get ColorGradingTextureEnabled() {\n        return MaterialFlags.ColorGradingTextureEnabled;\n    }\n    static set ColorGradingTextureEnabled(value) {\n        MaterialFlags.ColorGradingTextureEnabled = value;\n    }\n    /**\n     * Are fresnels enabled in the application.\n     */\n    static get FresnelEnabled() {\n        return MaterialFlags.FresnelEnabled;\n    }\n    static set FresnelEnabled(value) {\n        MaterialFlags.FresnelEnabled = value;\n    }\n}\n/**\n * Force all the standard materials to compile to glsl even on WebGPU engines.\n * False by default. This is mostly meant for backward compatibility.\n */\nStandardMaterial.ForceGLSL = false;\n__decorate([\n    serializeAsTexture(\"diffuseTexture\")\n], StandardMaterial.prototype, \"_diffuseTexture\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesAndMiscDirty\")\n], StandardMaterial.prototype, \"diffuseTexture\", void 0);\n__decorate([\n    serializeAsTexture(\"ambientTexture\")\n], StandardMaterial.prototype, \"_ambientTexture\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\n], StandardMaterial.prototype, \"ambientTexture\", void 0);\n__decorate([\n    serializeAsTexture(\"opacityTexture\")\n], StandardMaterial.prototype, \"_opacityTexture\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesAndMiscDirty\")\n], StandardMaterial.prototype, \"opacityTexture\", void 0);\n__decorate([\n    serializeAsTexture(\"reflectionTexture\")\n], StandardMaterial.prototype, \"_reflectionTexture\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\n], StandardMaterial.prototype, \"reflectionTexture\", void 0);\n__decorate([\n    serializeAsTexture(\"emissiveTexture\")\n], StandardMaterial.prototype, \"_emissiveTexture\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\n], StandardMaterial.prototype, \"emissiveTexture\", void 0);\n__decorate([\n    serializeAsTexture(\"specularTexture\")\n], StandardMaterial.prototype, \"_specularTexture\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\n], StandardMaterial.prototype, \"specularTexture\", void 0);\n__decorate([\n    serializeAsTexture(\"bumpTexture\")\n], StandardMaterial.prototype, \"_bumpTexture\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\n], StandardMaterial.prototype, \"bumpTexture\", void 0);\n__decorate([\n    serializeAsTexture(\"lightmapTexture\")\n], StandardMaterial.prototype, \"_lightmapTexture\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\n], StandardMaterial.prototype, \"lightmapTexture\", void 0);\n__decorate([\n    serializeAsTexture(\"refractionTexture\")\n], StandardMaterial.prototype, \"_refractionTexture\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\n], StandardMaterial.prototype, \"refractionTexture\", void 0);\n__decorate([\n    serializeAsColor3(\"ambient\")\n], StandardMaterial.prototype, \"ambientColor\", void 0);\n__decorate([\n    serializeAsColor3(\"diffuse\")\n], StandardMaterial.prototype, \"diffuseColor\", void 0);\n__decorate([\n    serializeAsColor3(\"specular\")\n], StandardMaterial.prototype, \"specularColor\", void 0);\n__decorate([\n    serializeAsColor3(\"emissive\")\n], StandardMaterial.prototype, \"emissiveColor\", void 0);\n__decorate([\n    serialize()\n], StandardMaterial.prototype, \"specularPower\", void 0);\n__decorate([\n    serialize(\"useAlphaFromDiffuseTexture\")\n], StandardMaterial.prototype, \"_useAlphaFromDiffuseTexture\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesAndMiscDirty\")\n], StandardMaterial.prototype, \"useAlphaFromDiffuseTexture\", void 0);\n__decorate([\n    serialize(\"useEmissiveAsIllumination\")\n], StandardMaterial.prototype, \"_useEmissiveAsIllumination\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\n], StandardMaterial.prototype, \"useEmissiveAsIllumination\", void 0);\n__decorate([\n    serialize(\"linkEmissiveWithDiffuse\")\n], StandardMaterial.prototype, \"_linkEmissiveWithDiffuse\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\n], StandardMaterial.prototype, \"linkEmissiveWithDiffuse\", void 0);\n__decorate([\n    serialize(\"useSpecularOverAlpha\")\n], StandardMaterial.prototype, \"_useSpecularOverAlpha\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\n], StandardMaterial.prototype, \"useSpecularOverAlpha\", void 0);\n__decorate([\n    serialize(\"useReflectionOverAlpha\")\n], StandardMaterial.prototype, \"_useReflectionOverAlpha\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\n], StandardMaterial.prototype, \"useReflectionOverAlpha\", void 0);\n__decorate([\n    serialize(\"disableLighting\")\n], StandardMaterial.prototype, \"_disableLighting\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsLightsDirty\")\n], StandardMaterial.prototype, \"disableLighting\", void 0);\n__decorate([\n    serialize(\"useObjectSpaceNormalMap\")\n], StandardMaterial.prototype, \"_useObjectSpaceNormalMap\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\n], StandardMaterial.prototype, \"useObjectSpaceNormalMap\", void 0);\n__decorate([\n    serialize(\"useParallax\")\n], StandardMaterial.prototype, \"_useParallax\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\n], StandardMaterial.prototype, \"useParallax\", void 0);\n__decorate([\n    serialize(\"useParallaxOcclusion\")\n], StandardMaterial.prototype, \"_useParallaxOcclusion\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\n], StandardMaterial.prototype, \"useParallaxOcclusion\", void 0);\n__decorate([\n    serialize()\n], StandardMaterial.prototype, \"parallaxScaleBias\", void 0);\n__decorate([\n    serialize(\"roughness\")\n], StandardMaterial.prototype, \"_roughness\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\n], StandardMaterial.prototype, \"roughness\", void 0);\n__decorate([\n    serialize()\n], StandardMaterial.prototype, \"indexOfRefraction\", void 0);\n__decorate([\n    serialize()\n], StandardMaterial.prototype, \"invertRefractionY\", void 0);\n__decorate([\n    serialize()\n], StandardMaterial.prototype, \"alphaCutOff\", void 0);\n__decorate([\n    serialize(\"useLightmapAsShadowmap\")\n], StandardMaterial.prototype, \"_useLightmapAsShadowmap\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\n], StandardMaterial.prototype, \"useLightmapAsShadowmap\", void 0);\n__decorate([\n    serializeAsFresnelParameters(\"diffuseFresnelParameters\")\n], StandardMaterial.prototype, \"_diffuseFresnelParameters\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsFresnelDirty\")\n], StandardMaterial.prototype, \"diffuseFresnelParameters\", void 0);\n__decorate([\n    serializeAsFresnelParameters(\"opacityFresnelParameters\")\n], StandardMaterial.prototype, \"_opacityFresnelParameters\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsFresnelAndMiscDirty\")\n], StandardMaterial.prototype, \"opacityFresnelParameters\", void 0);\n__decorate([\n    serializeAsFresnelParameters(\"reflectionFresnelParameters\")\n], StandardMaterial.prototype, \"_reflectionFresnelParameters\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsFresnelDirty\")\n], StandardMaterial.prototype, \"reflectionFresnelParameters\", void 0);\n__decorate([\n    serializeAsFresnelParameters(\"refractionFresnelParameters\")\n], StandardMaterial.prototype, \"_refractionFresnelParameters\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsFresnelDirty\")\n], StandardMaterial.prototype, \"refractionFresnelParameters\", void 0);\n__decorate([\n    serializeAsFresnelParameters(\"emissiveFresnelParameters\")\n], StandardMaterial.prototype, \"_emissiveFresnelParameters\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsFresnelDirty\")\n], StandardMaterial.prototype, \"emissiveFresnelParameters\", void 0);\n__decorate([\n    serialize(\"useReflectionFresnelFromSpecular\")\n], StandardMaterial.prototype, \"_useReflectionFresnelFromSpecular\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsFresnelDirty\")\n], StandardMaterial.prototype, \"useReflectionFresnelFromSpecular\", void 0);\n__decorate([\n    serialize(\"useGlossinessFromSpecularMapAlpha\")\n], StandardMaterial.prototype, \"_useGlossinessFromSpecularMapAlpha\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\n], StandardMaterial.prototype, \"useGlossinessFromSpecularMapAlpha\", void 0);\n__decorate([\n    serialize(\"maxSimultaneousLights\")\n], StandardMaterial.prototype, \"_maxSimultaneousLights\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsLightsDirty\")\n], StandardMaterial.prototype, \"maxSimultaneousLights\", void 0);\n__decorate([\n    serialize(\"invertNormalMapX\")\n], StandardMaterial.prototype, \"_invertNormalMapX\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\n], StandardMaterial.prototype, \"invertNormalMapX\", void 0);\n__decorate([\n    serialize(\"invertNormalMapY\")\n], StandardMaterial.prototype, \"_invertNormalMapY\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\n], StandardMaterial.prototype, \"invertNormalMapY\", void 0);\n__decorate([\n    serialize(\"twoSidedLighting\")\n], StandardMaterial.prototype, \"_twoSidedLighting\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\n], StandardMaterial.prototype, \"twoSidedLighting\", void 0);\n__decorate([\n    serialize(\"applyDecalMapAfterDetailMap\")\n], StandardMaterial.prototype, \"_applyDecalMapAfterDetailMap\", void 0);\n__decorate([\n    expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\n], StandardMaterial.prototype, \"applyDecalMapAfterDetailMap\", void 0);\nRegisterClass(\"BABYLON.StandardMaterial\", StandardMaterial);\nScene.DefaultMaterialFactory = (scene) => {\n    return new StandardMaterial(\"default material\", scene);\n};\n//# sourceMappingURL=standardMaterial.js.map"], "file": "_app/immutable/chunks/standardMaterial.Cnq1RvLI.js"}