import{an as b,ao as f}from"./index.BoI39RQH.js";import{A as _}from"./objectModelMapping.ha_8hIyl.js";const r="KHR_node_visibility";_("/nodes/{}/extensions/KHR_node_visibility/visible",{get:s=>{const i=s._babylonTransformNode;return i&&i.isVisible!==void 0?i.isVisible:!0},set:(s,i)=>{var e,t;(e=i._primitiveBabylonMeshes)==null||e.forEach(o=>{o.inheritVisibility=!0}),i._babylonTransformNode&&(i._babylonTransformNode.isVisible=s),(t=i._primitiveBabylonMeshes)==null||t.forEach(o=>{o.isVisible=s})},getTarget:s=>s._babylonTransformNode,getPropertyName:[()=>"isVisible"],type:"boolean"});class y{constructor(i){this.name=r,this._loader=i,this.enabled=i.isExtensionUsed(r)}async onReady(){var i;(i=this._loader.gltf.nodes)==null||i.forEach(e=>{var t,o,a,l;(t=e._primitiveBabylonMeshes)==null||t.forEach(n=>{n.inheritVisibility=!0}),(o=e.extensions)!=null&&o.KHR_node_visibility&&((a=e.extensions)==null?void 0:a.KHR_node_visibility.visible)===!1&&(e._babylonTransformNode&&(e._babylonTransformNode.isVisible=!1),(l=e._primitiveBabylonMeshes)==null||l.forEach(n=>{n.isVisible=!1}))})}dispose(){this._loader=null}}b(r);f(r,!0,s=>new y(s));export{y as KHR_node_visibility};
//# sourceMappingURL=KHR_node_visibility.DrdPfsSp.js.map
