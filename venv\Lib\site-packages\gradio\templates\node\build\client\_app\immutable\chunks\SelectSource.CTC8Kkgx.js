import{SvelteComponent as M,init as z,safe_not_equal as x,svg_element as w,claim_svg_element as v,children as g,detach as u,attr as n,insert_hydration as T,append_hydration as k,noop as y,empty as R,transition_in as p,group_outros as N,transition_out as b,check_outros as S,element as U,space as I,claim_element as $,claim_space as W,create_component as O,claim_component as P,toggle_class as B,mount_component as j,listen as q,destroy_component as A}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import"./2.B2AoQPnG.js";import{U as J,I as K}from"./Upload.yOHVlgUe.js";function L(o){let e,l,t,i,a;return{c(){e=w("svg"),l=w("path"),t=w("path"),i=w("line"),a=w("line"),this.h()},l(r){e=v(r,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0,class:!0});var s=g(e);l=v(s,"path",{d:!0}),g(l).forEach(u),t=v(s,"path",{d:!0}),g(t).forEach(u),i=v(s,"line",{x1:!0,y1:!0,x2:!0,y2:!0}),g(i).forEach(u),a=v(s,"line",{x1:!0,y1:!0,x2:!0,y2:!0}),g(a).forEach(u),s.forEach(u),this.h()},h(){n(l,"d","M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"),n(t,"d","M19 10v2a7 7 0 0 1-14 0v-2"),n(i,"x1","12"),n(i,"y1","19"),n(i,"x2","12"),n(i,"y2","23"),n(a,"x1","8"),n(a,"y1","23"),n(a,"x2","16"),n(a,"y2","23"),n(e,"xmlns","http://www.w3.org/2000/svg"),n(e,"width","100%"),n(e,"height","100%"),n(e,"viewBox","0 0 24 24"),n(e,"fill","none"),n(e,"stroke","currentColor"),n(e,"stroke-width","2"),n(e,"stroke-linecap","round"),n(e,"stroke-linejoin","round"),n(e,"class","feather feather-mic")},m(r,s){T(r,e,s),k(e,l),k(e,t),k(e,i),k(e,a)},p:y,i:y,o:y,d(r){r&&u(e)}}}class Q extends M{constructor(e){super(),z(this,e,null,L,x,{})}}function X(o){let e,l,t;return{c(){e=w("svg"),l=w("path"),t=w("path"),this.h()},l(i){e=v(i,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0});var a=g(e);l=v(a,"path",{fill:!0,d:!0}),g(l).forEach(u),t=v(a,"path",{fill:!0,d:!0}),g(t).forEach(u),a.forEach(u),this.h()},h(){n(l,"fill","currentColor"),n(l,"d","M12 2c-4.963 0-9 4.038-9 9c0 3.328 1.82 6.232 4.513 7.79l-2.067 1.378A1 1 0 0 0 6 22h12a1 1 0 0 0 .555-1.832l-2.067-1.378C19.18 17.232 21 14.328 21 11c0-4.962-4.037-9-9-9zm0 16c-3.859 0-7-3.141-7-7c0-3.86 3.141-7 7-7s7 3.14 7 7c0 3.859-3.141 7-7 7z"),n(t,"fill","currentColor"),n(t,"d","M12 6c-2.757 0-5 2.243-5 5s2.243 5 5 5s5-2.243 5-5s-2.243-5-5-5zm0 8c-1.654 0-3-1.346-3-3s1.346-3 3-3s3 1.346 3 3s-1.346 3-3 3z"),n(e,"xmlns","http://www.w3.org/2000/svg"),n(e,"width","100%"),n(e,"height","100%"),n(e,"viewBox","0 0 24 24")},m(i,a){T(i,e,a),k(e,l),k(e,t)},p:y,i:y,o:y,d(i){i&&u(e)}}}class Y extends M{constructor(e){super(),z(this,e,null,X,x,{})}}function Z(o){let e,l,t;return{c(){e=w("svg"),l=w("circle"),t=w("animateTransform"),this.h()},l(i){e=v(i,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,class:!0});var a=g(e);l=v(a,"circle",{cx:!0,cy:!0,r:!0,fill:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-dasharray":!0,"stroke-dashoffset":!0});var r=g(l);t=v(r,"animateTransform",{attributeName:!0,type:!0,from:!0,to:!0,repeatCount:!0}),g(t).forEach(u),r.forEach(u),a.forEach(u),this.h()},h(){n(t,"attributeName","transform"),n(t,"type","rotate"),n(t,"from","0 25 25"),n(t,"to","360 25 25"),n(t,"repeatCount","indefinite"),n(l,"cx","25"),n(l,"cy","25"),n(l,"r","20"),n(l,"fill","none"),n(l,"stroke-width","3.0"),n(l,"stroke-linecap","round"),n(l,"stroke-dasharray","94.2477796076938 94.2477796076938"),n(l,"stroke-dashoffset","0"),n(e,"xmlns","http://www.w3.org/2000/svg"),n(e,"width","100%"),n(e,"height","100%"),n(e,"viewBox","0 0 50 50"),n(e,"class","svelte-184ngxt")},m(i,a){T(i,e,a),k(e,l),k(l,t)},p:y,i:y,o:y,d(i){i&&u(e)}}}class ae extends M{constructor(e){super(),z(this,e,null,Z,x,{})}}function V(o){let e,l=o[1].includes("upload"),t,i=o[1].includes("microphone"),a,r=o[1].includes("webcam"),s,E=o[1].includes("clipboard"),C,h=l&&D(o),m=i&&F(o),d=r&&G(o),c=E&&H(o);return{c(){e=U("span"),h&&h.c(),t=I(),m&&m.c(),a=I(),d&&d.c(),s=I(),c&&c.c(),this.h()},l(_){e=$(_,"SPAN",{class:!0,"data-testid":!0});var f=g(e);h&&h.l(f),t=W(f),m&&m.l(f),a=W(f),d&&d.l(f),s=W(f),c&&c.l(f),f.forEach(u),this.h()},h(){n(e,"class","source-selection svelte-snayfm"),n(e,"data-testid","source-select")},m(_,f){T(_,e,f),h&&h.m(e,null),k(e,t),m&&m.m(e,null),k(e,a),d&&d.m(e,null),k(e,s),c&&c.m(e,null),C=!0},p(_,f){f&2&&(l=_[1].includes("upload")),l?h?(h.p(_,f),f&2&&p(h,1)):(h=D(_),h.c(),p(h,1),h.m(e,t)):h&&(N(),b(h,1,1,()=>{h=null}),S()),f&2&&(i=_[1].includes("microphone")),i?m?(m.p(_,f),f&2&&p(m,1)):(m=F(_),m.c(),p(m,1),m.m(e,a)):m&&(N(),b(m,1,1,()=>{m=null}),S()),f&2&&(r=_[1].includes("webcam")),r?d?(d.p(_,f),f&2&&p(d,1)):(d=G(_),d.c(),p(d,1),d.m(e,s)):d&&(N(),b(d,1,1,()=>{d=null}),S()),f&2&&(E=_[1].includes("clipboard")),E?c?(c.p(_,f),f&2&&p(c,1)):(c=H(_),c.c(),p(c,1),c.m(e,null)):c&&(N(),b(c,1,1,()=>{c=null}),S())},i(_){C||(p(h),p(m),p(d),p(c),C=!0)},o(_){b(h),b(m),b(d),b(c),C=!1},d(_){_&&u(e),h&&h.d(),m&&m.d(),d&&d.d(),c&&c.d()}}}function D(o){let e,l,t,i,a;return l=new J({}),{c(){e=U("button"),O(l.$$.fragment),this.h()},l(r){e=$(r,"BUTTON",{class:!0,"aria-label":!0});var s=g(e);P(l.$$.fragment,s),s.forEach(u),this.h()},h(){n(e,"class","icon svelte-snayfm"),n(e,"aria-label","Upload file"),B(e,"selected",o[0]==="upload"||!o[0])},m(r,s){T(r,e,s),j(l,e,null),t=!0,i||(a=q(e,"click",o[6]),i=!0)},p(r,s){(!t||s&1)&&B(e,"selected",r[0]==="upload"||!r[0])},i(r){t||(p(l.$$.fragment,r),t=!0)},o(r){b(l.$$.fragment,r),t=!1},d(r){r&&u(e),A(l),i=!1,a()}}}function F(o){let e,l,t,i,a;return l=new Q({}),{c(){e=U("button"),O(l.$$.fragment),this.h()},l(r){e=$(r,"BUTTON",{class:!0,"aria-label":!0});var s=g(e);P(l.$$.fragment,s),s.forEach(u),this.h()},h(){n(e,"class","icon svelte-snayfm"),n(e,"aria-label","Record audio"),B(e,"selected",o[0]==="microphone")},m(r,s){T(r,e,s),j(l,e,null),t=!0,i||(a=q(e,"click",o[7]),i=!0)},p(r,s){(!t||s&1)&&B(e,"selected",r[0]==="microphone")},i(r){t||(p(l.$$.fragment,r),t=!0)},o(r){b(l.$$.fragment,r),t=!1},d(r){r&&u(e),A(l),i=!1,a()}}}function G(o){let e,l,t,i,a;return l=new Y({}),{c(){e=U("button"),O(l.$$.fragment),this.h()},l(r){e=$(r,"BUTTON",{class:!0,"aria-label":!0});var s=g(e);P(l.$$.fragment,s),s.forEach(u),this.h()},h(){n(e,"class","icon svelte-snayfm"),n(e,"aria-label","Capture from camera"),B(e,"selected",o[0]==="webcam")},m(r,s){T(r,e,s),j(l,e,null),t=!0,i||(a=q(e,"click",o[8]),i=!0)},p(r,s){(!t||s&1)&&B(e,"selected",r[0]==="webcam")},i(r){t||(p(l.$$.fragment,r),t=!0)},o(r){b(l.$$.fragment,r),t=!1},d(r){r&&u(e),A(l),i=!1,a()}}}function H(o){let e,l,t,i,a;return l=new K({}),{c(){e=U("button"),O(l.$$.fragment),this.h()},l(r){e=$(r,"BUTTON",{class:!0,"aria-label":!0});var s=g(e);P(l.$$.fragment,s),s.forEach(u),this.h()},h(){n(e,"class","icon svelte-snayfm"),n(e,"aria-label","Paste from clipboard"),B(e,"selected",o[0]==="clipboard")},m(r,s){T(r,e,s),j(l,e,null),t=!0,i||(a=q(e,"click",o[9]),i=!0)},p(r,s){(!t||s&1)&&B(e,"selected",r[0]==="clipboard")},i(r){t||(p(l.$$.fragment,r),t=!0)},o(r){b(l.$$.fragment,r),t=!1},d(r){r&&u(e),A(l),i=!1,a()}}}function ee(o){let e,l,t=o[2].length>1&&V(o);return{c(){t&&t.c(),e=R()},l(i){t&&t.l(i),e=R()},m(i,a){t&&t.m(i,a),T(i,e,a),l=!0},p(i,[a]){i[2].length>1?t?(t.p(i,a),a&4&&p(t,1)):(t=V(i),t.c(),p(t,1),t.m(e.parentNode,e)):t&&(N(),b(t,1,1,()=>{t=null}),S())},i(i){l||(p(t),l=!0)},o(i){b(t),l=!1},d(i){i&&u(e),t&&t.d(i)}}}function te(o,e,l){let t,{sources:i}=e,{active_source:a}=e,{handle_clear:r=()=>{}}=e,{handle_select:s=()=>{}}=e;async function E(c){r(),l(0,a=c),s(c)}const C=()=>E("upload"),h=()=>E("microphone"),m=()=>E("webcam"),d=()=>E("clipboard");return o.$$set=c=>{"sources"in c&&l(1,i=c.sources),"active_source"in c&&l(0,a=c.active_source),"handle_clear"in c&&l(4,r=c.handle_clear),"handle_select"in c&&l(5,s=c.handle_select)},o.$$.update=()=>{o.$$.dirty&2&&l(2,t=[...new Set(i)])},[a,i,t,E,r,s,C,h,m,d]}class se extends M{constructor(e){super(),z(this,e,te,ee,x,{sources:1,active_source:0,handle_clear:4,handle_select:5})}}export{Q as M,se as S,Y as W,ae as a};
//# sourceMappingURL=SelectSource.CTC8Kkgx.js.map
