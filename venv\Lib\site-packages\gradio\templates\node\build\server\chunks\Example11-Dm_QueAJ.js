import { c as create_ssr_component, e as escape } from './ssr-C3HYbsxA.js';

const y={code:".gallery.svelte-1ayixqk{padding:var(--size-1) var(--size-2)}",map:'{"version":3,"file":"Example.svelte","sources":["Example.svelte"],"sourcesContent":["<script lang=\\"ts\\">export let value;\\nexport let type;\\nexport let selected = false;\\nexport let choices;\\nlet value_array = value ? Array.isArray(value) ? value : [value] : [];\\nlet names = value_array.map((val) => choices.find((pair) => pair[1] === val)?.[0]).filter((name) => name !== void 0);\\nlet names_string = names.join(\\", \\");\\n<\/script>\\n\\n<div\\n\\tclass:table={type === \\"table\\"}\\n\\tclass:gallery={type === \\"gallery\\"}\\n\\tclass:selected\\n>\\n\\t{names_string}\\n</div>\\n\\n<style>\\n\\t.gallery {\\n\\t\\tpadding: var(--size-1) var(--size-2);\\n\\t}</style>\\n"],"names":[],"mappings":"AAkBC,uBAAS,CACR,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CACpC"}'},f=create_ssr_component((v,e,a,d)=>{let{value:l}=e,{type:t}=e,{selected:s=!1}=e,{choices:r}=e,n=(l?Array.isArray(l)?l:[l]:[]).map(i=>r.find(c=>c[1]===i)?.[0]).filter(i=>i!==void 0).join(", ");return e.value===void 0&&a.value&&l!==void 0&&a.value(l),e.type===void 0&&a.type&&t!==void 0&&a.type(t),e.selected===void 0&&a.selected&&s!==void 0&&a.selected(s),e.choices===void 0&&a.choices&&r!==void 0&&a.choices(r),v.css.add(y),`<div class="${["svelte-1ayixqk",(t==="table"?"table":"")+" "+(t==="gallery"?"gallery":"")+" "+(s?"selected":"")].join(" ").trim()}">${escape(n)} </div>`});

export { f as default };
//# sourceMappingURL=Example11-Dm_QueAJ.js.map
