{"version": 3, "file": "flowGraphWaitAllBlock-CyKArKRg.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphWaitAllBlock.js"], "sourcesContent": ["import { FlowGraphExecutionBlockWithOutSignal } from \"../../../flowGraphExecutionBlockWithOutSignal.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\nimport { RichTypeNumber } from \"../../../flowGraphRichTypes.js\";\n/**\n * A block that waits for all input flows to be activated before activating its output flow.\n */\nexport class FlowGraphWaitAllBlock extends FlowGraphExecutionBlockWithOutSignal {\n    constructor(\n    /**\n     * the configuration of the block\n     */\n    config) {\n        super(config);\n        this.config = config;\n        /**\n         * An array of input signals\n         */\n        this.inFlows = [];\n        this._cachedActivationState = [];\n        this.reset = this._registerSignalInput(\"reset\");\n        this.completed = this._registerSignalOutput(\"completed\");\n        this.remainingInputs = this.registerDataOutput(\"remainingInputs\", RichTypeNumber, this.config.inputSignalCount || 0);\n        // The first inFlow is the default input signal all execution blocks have\n        for (let i = 0; i < this.config.inputSignalCount; i++) {\n            this.inFlows.push(this._registerSignalInput(`in_${i}`));\n        }\n        // no need for in\n        this._unregisterSignalInput(\"in\");\n    }\n    _getCurrentActivationState(context) {\n        const activationState = this._cachedActivationState;\n        activationState.length = 0;\n        if (!context._hasExecutionVariable(this, \"activationState\")) {\n            for (let i = 0; i < this.config.inputSignalCount; i++) {\n                activationState.push(false);\n            }\n        }\n        else {\n            const contextActivationState = context._getExecutionVariable(this, \"activationState\", []);\n            for (let i = 0; i < contextActivationState.length; i++) {\n                activationState.push(contextActivationState[i]);\n            }\n        }\n        return activationState;\n    }\n    _execute(context, callingSignal) {\n        const activationState = this._getCurrentActivationState(context);\n        if (callingSignal === this.reset) {\n            for (let i = 0; i < this.config.inputSignalCount; i++) {\n                activationState[i] = false;\n            }\n        }\n        else {\n            const index = this.inFlows.indexOf(callingSignal);\n            if (index >= 0) {\n                activationState[index] = true;\n            }\n        }\n        this.remainingInputs.setValue(activationState.filter((v) => !v).length, context);\n        context._setExecutionVariable(this, \"activationState\", activationState.slice());\n        if (!activationState.includes(false)) {\n            this.completed._activateSignal(context);\n            for (let i = 0; i < this.config.inputSignalCount; i++) {\n                activationState[i] = false;\n            }\n        }\n        else {\n            callingSignal !== this.reset && this.out._activateSignal(context);\n        }\n    }\n    /**\n     * @returns class name of the block.\n     */\n    getClassName() {\n        return \"FlowGraphWaitAllBlock\" /* FlowGraphBlockNames.WaitAll */;\n    }\n    /**\n     * Serializes this block into a object\n     * @param serializationObject the object to serialize to\n     */\n    serialize(serializationObject) {\n        super.serialize(serializationObject);\n        serializationObject.config.inputFlows = this.config.inputSignalCount;\n    }\n}\nRegisterClass(\"FlowGraphWaitAllBlock\" /* FlowGraphBlockNames.WaitAll */, FlowGraphWaitAllBlock);\n//# sourceMappingURL=flowGraphWaitAllBlock.js.map"], "names": ["FlowGraphWaitAllBlock", "FlowGraphExecutionBlockWithOutSignal", "config", "RichTypeNumber", "i", "context", "activationState", "contextActivationState", "callingSignal", "index", "v", "serializationObject", "RegisterClass"], "mappings": "gPAMO,MAAMA,UAA8BC,CAAqC,CAC5E,YAIAC,EAAQ,CACJ,MAAMA,CAAM,EACZ,KAAK,OAASA,EAId,KAAK,QAAU,GACf,KAAK,uBAAyB,GAC9B,KAAK,MAAQ,KAAK,qBAAqB,OAAO,EAC9C,KAAK,UAAY,KAAK,sBAAsB,WAAW,EACvD,KAAK,gBAAkB,KAAK,mBAAmB,kBAAmBC,EAAgB,KAAK,OAAO,kBAAoB,CAAC,EAEnH,QAASC,EAAI,EAAGA,EAAI,KAAK,OAAO,iBAAkBA,IAC9C,KAAK,QAAQ,KAAK,KAAK,qBAAqB,MAAMA,CAAC,EAAE,CAAC,EAG1D,KAAK,uBAAuB,IAAI,CACnC,CACD,2BAA2BC,EAAS,CAChC,MAAMC,EAAkB,KAAK,uBAE7B,GADAA,EAAgB,OAAS,EACpBD,EAAQ,sBAAsB,KAAM,iBAAiB,EAKrD,CACD,MAAME,EAAyBF,EAAQ,sBAAsB,KAAM,kBAAmB,CAAA,CAAE,EACxF,QAASD,EAAI,EAAGA,EAAIG,EAAuB,OAAQH,IAC/CE,EAAgB,KAAKC,EAAuBH,CAAC,CAAC,CAErD,KATG,SAASA,EAAI,EAAGA,EAAI,KAAK,OAAO,iBAAkBA,IAC9CE,EAAgB,KAAK,EAAK,EASlC,OAAOA,CACV,CACD,SAASD,EAASG,EAAe,CAC7B,MAAMF,EAAkB,KAAK,2BAA2BD,CAAO,EAC/D,GAAIG,IAAkB,KAAK,MACvB,QAASJ,EAAI,EAAGA,EAAI,KAAK,OAAO,iBAAkBA,IAC9CE,EAAgBF,CAAC,EAAI,OAGxB,CACD,MAAMK,EAAQ,KAAK,QAAQ,QAAQD,CAAa,EAC5CC,GAAS,IACTH,EAAgBG,CAAK,EAAI,GAEhC,CAGD,GAFA,KAAK,gBAAgB,SAASH,EAAgB,OAAQI,GAAM,CAACA,CAAC,EAAE,OAAQL,CAAO,EAC/EA,EAAQ,sBAAsB,KAAM,kBAAmBC,EAAgB,MAAK,CAAE,EACzEA,EAAgB,SAAS,EAAK,EAO/BE,IAAkB,KAAK,OAAS,KAAK,IAAI,gBAAgBH,CAAO,MAP9B,CAClC,KAAK,UAAU,gBAAgBA,CAAO,EACtC,QAASD,EAAI,EAAGA,EAAI,KAAK,OAAO,iBAAkBA,IAC9CE,EAAgBF,CAAC,EAAI,EAE5B,CAIJ,CAID,cAAe,CACX,MAAO,uBACV,CAKD,UAAUO,EAAqB,CAC3B,MAAM,UAAUA,CAAmB,EACnCA,EAAoB,OAAO,WAAa,KAAK,OAAO,gBACvD,CACL,CACAC,EAAc,wBAA2DZ,CAAqB", "x_google_ignoreList": [0]}