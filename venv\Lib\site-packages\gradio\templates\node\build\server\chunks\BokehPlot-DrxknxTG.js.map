{"version": 3, "file": "BokehPlot-DrxknxTG.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/BokehPlot.js"], "sourcesContent": ["import{create_ssr_component as w,add_attribute as u}from\"svelte/internal\";import{createEventDispatcher as E,onD<PERSON>roy as A}from\"svelte\";const C={code:\".gradio-bokeh.svelte-1rhu6ax{display:flex;justify-content:center}\",map:'{\"version\":3,\"file\":\"BokehPlot.svelte\",\"sources\":[\"BokehPlot.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { onDestroy, createEventDispatcher } from \\\\\"svelte\\\\\";\\\\nexport let value;\\\\nexport let bokeh_version;\\\\nconst div_id = `bokehDiv-${Math.random().toString(5).substring(2)}`;\\\\nconst dispatch = createEventDispatcher();\\\\n$: plot = value?.plot;\\\\nasync function embed_bokeh(_plot) {\\\\n    if (document) {\\\\n        if (document.getElementById(div_id)) {\\\\n            document.getElementById(div_id).innerHTML = \\\\\"\\\\\";\\\\n        }\\\\n    }\\\\n    if (window.Bokeh) {\\\\n        load_bokeh();\\\\n        let plotObj = JSON.parse(_plot);\\\\n        const y = await window.Bokeh.embed.embed_item(plotObj, div_id);\\\\n        y._roots.forEach(async (p) => {\\\\n            await p.ready;\\\\n            dispatch(\\\\\"load\\\\\");\\\\n        });\\\\n    }\\\\n}\\\\n$: loaded && embed_bokeh(plot);\\\\nconst main_src = `https://cdn.bokeh.org/bokeh/release/bokeh-${bokeh_version}.min.js`;\\\\nconst plugins_src = [\\\\n    `https://cdn.pydata.org/bokeh/release/bokeh-widgets-${bokeh_version}.min.js`,\\\\n    `https://cdn.pydata.org/bokeh/release/bokeh-tables-${bokeh_version}.min.js`,\\\\n    `https://cdn.pydata.org/bokeh/release/bokeh-gl-${bokeh_version}.min.js`,\\\\n    `https://cdn.pydata.org/bokeh/release/bokeh-api-${bokeh_version}.min.js`\\\\n];\\\\nlet loaded = false;\\\\nasync function load_plugins() {\\\\n    await Promise.all(plugins_src.map((src, i) => {\\\\n        return new Promise((resolve) => {\\\\n            const script = document.createElement(\\\\\"script\\\\\");\\\\n            script.onload = resolve;\\\\n            script.src = src;\\\\n            document.head.appendChild(script);\\\\n            return script;\\\\n        });\\\\n    }));\\\\n    loaded = true;\\\\n}\\\\nlet plugin_scripts = [];\\\\nfunction handle_bokeh_loaded() {\\\\n    plugin_scripts = load_plugins();\\\\n}\\\\nfunction load_bokeh() {\\\\n    const script = document.createElement(\\\\\"script\\\\\");\\\\n    script.onload = handle_bokeh_loaded;\\\\n    script.src = main_src;\\\\n    const is_bokeh_script_present = document.head.querySelector(`script[src=\\\\\"${main_src}\\\\\"]`);\\\\n    if (!is_bokeh_script_present) {\\\\n        document.head.appendChild(script);\\\\n    }\\\\n    else {\\\\n        handle_bokeh_loaded();\\\\n    }\\\\n    return script;\\\\n}\\\\nconst main_script = bokeh_version ? load_bokeh() : null;\\\\nonDestroy(() => {\\\\n    if (main_script in document.children) {\\\\n        document.removeChild(main_script);\\\\n        plugin_scripts.forEach((child) => document.removeChild(child));\\\\n    }\\\\n});\\\\n<\\/script>\\\\n\\\\n<div data-testid={\\\\\"bokeh\\\\\"} id={div_id} class=\\\\\"gradio-bokeh\\\\\" />\\\\n\\\\n<style>\\\\n\\\\t.gradio-bokeh {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAwEC,4BAAc,CACb,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAClB\"}'},x=w((k,o,s,j)=>{let a,{value:r}=o,{bokeh_version:n}=o;const i=`bokehDiv-${Math.random().toString(5).substring(2)}`,v=E();async function f(e){if(document&&document.getElementById(i)&&(document.getElementById(i).innerHTML=\"\"),window.Bokeh){m();let d=JSON.parse(e);(await window.Bokeh.embed.embed_item(d,i))._roots.forEach(async t=>{await t.ready,v(\"load\")})}}const c=`https://cdn.bokeh.org/bokeh/release/bokeh-${n}.min.js`,g=[`https://cdn.pydata.org/bokeh/release/bokeh-widgets-${n}.min.js`,`https://cdn.pydata.org/bokeh/release/bokeh-tables-${n}.min.js`,`https://cdn.pydata.org/bokeh/release/bokeh-gl-${n}.min.js`,`https://cdn.pydata.org/bokeh/release/bokeh-api-${n}.min.js`];let l=!1;async function y(){await Promise.all(g.map((e,d)=>new Promise(b=>{const t=document.createElement(\"script\");return t.onload=b,t.src=e,document.head.appendChild(t),t}))),l=!0}let h=[];function p(){h=y()}function m(){const e=document.createElement(\"script\");return e.onload=p,e.src=c,document.head.querySelector(`script[src=\"${c}\"]`)?p():document.head.appendChild(e),e}const _=n?m():null;return A(()=>{_ in document.children&&(document.removeChild(_),h.forEach(e=>document.removeChild(e)))}),o.value===void 0&&s.value&&r!==void 0&&s.value(r),o.bokeh_version===void 0&&s.bokeh_version&&n!==void 0&&s.bokeh_version(n),k.css.add(C),a=r?.plot,l&&f(a),`<div${u(\"data-testid\",\"bokeh\",0)}${u(\"id\",i,0)} class=\"gradio-bokeh svelte-1rhu6ax\"></div>`});export{x as default};\n//# sourceMappingURL=BokehPlot.js.map\n"], "names": ["w", "E", "A", "u"], "mappings": ";;AAA6I,MAAC,CAAC,CAAC,CAAC,IAAI,CAAC,mEAAmE,CAAC,GAAG,CAAC,qwFAAqwF,CAAC,CAAC,CAAC,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,qBAAC,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAC,CAAC,EAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,0CAA0C,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mDAAmD,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,kDAAkD,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,8CAA8C,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,+CAA+C,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAOC,SAAC,CAAC,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAEC,aAAC,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAEA,aAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,2CAA2C,CAAC,CAAC;;;;"}