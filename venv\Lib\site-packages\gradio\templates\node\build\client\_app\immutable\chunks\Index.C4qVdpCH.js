import{SvelteComponent as ke,init as Ee,safe_not_equal as pe,create_component as Z,space as j,element as C,empty as ue,claim_component as x,claim_space as y,claim_element as M,children as H,detach as p,attr as V,set_style as R,mount_component as $,insert_hydration as A,listen as U,transition_in as X,group_outros as Be,transition_out as G,check_outros as De,destroy_component as ee,run_all as ze,createEventDispatcher as Xe,onMount as Oe,afterUpdate as Pe,globals as Le,text as Te,claim_text as Ie,set_data as qe,ensure_array_like as ae,append_hydration as D,set_input_value as re,action_destroyer as Ge,destroy_each as Ye,toggle_class as ce,tick as je,bubble as fe,binding_callbacks as ie,flush as z,assign as ye,bind as _e,get_spread_update as Ae,get_spread_object as Fe,add_flush_callback as he}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{t as se,E as Je}from"./tinycolor.CowIdatr.js";import{y as Ke,B as Qe,S as We}from"./2.B2AoQPnG.js";import{default as dt}from"./Example.BtYdbMfl.js";function Ze(t,e){const n=l=>{t&&!t.contains(l.target)&&!l.defaultPrevented&&e(l)};return document.addEventListener("mousedown",n,!0),{destroy(){document.removeEventListener("mousedown",n,!0)}}}function de(t){const e=t.s,n=t.v;let l=e*n;const a=t.h/60;let r=l*(1-Math.abs(a%2-1));const f=n-l;l=l+f,r=r+f;const h=Math.floor(a)%6,g=[l,r,f,f,r,l][h],_=[r,l,l,r,f,f][h],i=[f,f,r,l,l,r][h];return`rgba(${g*255}, ${_*255}, ${i*255}, ${t.a})`}function xe(t,e){return e==="hex"?se(t).toHexString():e==="rgb"?se(t).toRgbString():se(t).toHslString()}const{window:me}=Le;function ge(t,e,n){const l=t.slice();return l[6]=e[n][0],l[1]=e[n][1],l}function $e(t){let e;return{c(){e=Te(t[6])},l(n){e=Ie(n,t[6])},m(n,l){A(n,e,l)},p(n,l){l[0]&64&&qe(e,n[6])},d(n){n&&p(e)}}}function be(t){let e,n,l,a=`translate(${t[11][0]}px,${t[11][1]}px)`,r,f,h,g=`translateX(${t[13]}px)`,_,i,c,o,m,B,d,O,w,P,T,N,q,I,b=t[8]&&we(),L=ae(t[20]),v=[];for(let s=0;s<L.length;s+=1)v[s]=ve(ge(t,L,s));return{c(){e=C("div"),n=C("div"),l=C("div"),r=j(),f=C("div"),h=C("div"),_=j(),i=C("div"),c=C("button"),o=j(),m=C("div"),B=C("div"),d=C("input"),O=j(),w=C("button"),b&&b.c(),P=j(),T=C("div");for(let s=0;s<v.length;s+=1)v[s].c();this.h()},l(s){e=M(s,"DIV",{class:!0});var E=H(e);n=M(E,"DIV",{class:!0,style:!0});var k=H(n);l=M(k,"DIV",{class:!0}),H(l).forEach(p),k.forEach(p),r=y(E),f=M(E,"DIV",{class:!0});var F=H(f);h=M(F,"DIV",{class:!0}),H(h).forEach(p),F.forEach(p),_=y(E),i=M(E,"DIV",{class:!0});var Y=H(i);c=M(Y,"BUTTON",{class:!0}),H(c).forEach(p),o=y(Y),m=M(Y,"DIV",{});var K=H(m);B=M(K,"DIV",{class:!0});var Q=H(B);d=M(Q,"INPUT",{type:!0,class:!0}),O=y(Q),w=M(Q,"BUTTON",{class:!0});var te=H(w);b&&b.l(te),te.forEach(p),Q.forEach(p),P=y(K),T=M(K,"DIV",{class:!0});var le=H(T);for(let W=0;W<v.length;W+=1)v[W].l(le);le.forEach(p),K.forEach(p),Y.forEach(p),E.forEach(p),this.h()},h(){V(l,"class","marker svelte-1oxhzww"),R(l,"transform",a),R(l,"background",t[1]),V(n,"class","color-gradient svelte-1oxhzww"),R(n,"--hue",t[12]),V(h,"class","marker svelte-1oxhzww"),R(h,"background","hsl("+t[12]+", 100%, 50%)"),R(h,"transform",g),V(f,"class","hue-slider svelte-1oxhzww"),V(c,"class","swatch svelte-1oxhzww"),R(c,"background",t[1]),V(d,"type","text"),V(d,"class","svelte-1oxhzww"),V(w,"class","eyedropper svelte-1oxhzww"),V(B,"class","input-wrap svelte-1oxhzww"),V(T,"class","buttons svelte-1oxhzww"),V(i,"class","input svelte-1oxhzww"),V(e,"class","color-picker svelte-1oxhzww")},m(s,E){A(s,e,E),D(e,n),D(n,l),t[27](n),D(e,r),D(e,f),D(f,h),t[28](f),D(e,_),D(e,i),D(i,c),D(i,o),D(i,m),D(m,B),D(B,d),re(d,t[7]),D(B,O),D(B,w),b&&b.m(w,null),D(m,P),D(m,T);for(let k=0;k<v.length;k+=1)v[k]&&v[k].m(T,null);N=!0,q||(I=[U(n,"mousedown",t[15]),U(f,"mousedown",t[14]),U(c,"click",t[22]),U(d,"input",t[29]),U(d,"change",t[30]),U(w,"click",t[19]),U(e,"focus",t[24]),U(e,"blur",t[25]),Ge(Ze.call(null,e,t[21]))],q=!0)},p(s,E){if(E[0]&2048&&a!==(a=`translate(${s[11][0]}px,${s[11][1]}px)`)&&R(l,"transform",a),E[0]&2&&R(l,"background",s[1]),(!N||E[0]&4096)&&R(n,"--hue",s[12]),E[0]&4096&&R(h,"background","hsl("+s[12]+", 100%, 50%)"),E[0]&8192&&g!==(g=`translateX(${s[13]}px)`)&&R(h,"transform",g),E[0]&2&&R(c,"background",s[1]),E[0]&128&&d.value!==s[7]&&re(d,s[7]),s[8]?b?E[0]&256&&X(b,1):(b=we(),b.c(),X(b,1),b.m(w,null)):b&&(Be(),G(b,1,1,()=>{b=null}),De()),E[0]&1048577){L=ae(s[20]);let k;for(k=0;k<L.length;k+=1){const F=ge(s,L,k);v[k]?v[k].p(F,E):(v[k]=ve(F),v[k].c(),v[k].m(T,null))}for(;k<v.length;k+=1)v[k].d(1);v.length=L.length}},i(s){N||(X(b),N=!0)},o(s){G(b),N=!1},d(s){s&&p(e),t[27](null),t[28](null),b&&b.d(),Ye(v,s),q=!1,ze(I)}}}function we(t){let e,n;return e=new Je({}),{c(){Z(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,a){$(e,l,a),n=!0},i(l){n||(X(e.$$.fragment,l),n=!0)},o(l){G(e.$$.fragment,l),n=!1},d(l){ee(e,l)}}}function ve(t){let e,n=t[6]+"",l,a,r;function f(){return t[31](t[1])}return{c(){e=C("button"),l=Te(n),this.h()},l(h){e=M(h,"BUTTON",{class:!0});var g=H(e);l=Ie(g,n),g.forEach(p),this.h()},h(){V(e,"class","button svelte-1oxhzww"),ce(e,"active",t[0]===t[1])},m(h,g){A(h,e,g),D(e,l),a||(r=U(e,"click",f),a=!0)},p(h,g){t=h,g[0]&1048577&&ce(e,"active",t[0]===t[1])},d(h){h&&p(e),a=!1,r()}}}function et(t){let e,n,l,a,r,f,h,g;e=new Ke({props:{show_label:t[5],info:t[3],$$slots:{default:[$e]},$$scope:{ctx:t}}});let _=t[2]&&be(t);return{c(){Z(e.$$.fragment),n=j(),l=C("button"),a=j(),_&&_.c(),r=ue(),this.h()},l(i){x(e.$$.fragment,i),n=y(i),l=M(i,"BUTTON",{class:!0}),H(l).forEach(p),a=y(i),_&&_.l(i),r=ue(),this.h()},h(){V(l,"class","dialog-button svelte-1oxhzww"),l.disabled=t[4],R(l,"background",t[1])},m(i,c){$(e,i,c),A(i,n,c),A(i,l,c),A(i,a,c),_&&_.m(i,c),A(i,r,c),f=!0,h||(g=[U(me,"mousemove",t[16]),U(me,"mouseup",t[17]),U(l,"click",t[26])],h=!0)},p(i,c){const o={};c[0]&32&&(o.show_label=i[5]),c[0]&8&&(o.info=i[3]),c[0]&64|c[1]&4096&&(o.$$scope={dirty:c,ctx:i}),e.$set(o),(!f||c[0]&16)&&(l.disabled=i[4]),c[0]&2&&R(l,"background",i[1]),i[2]?_?(_.p(i,c),c[0]&4&&X(_,1)):(_=be(i),_.c(),X(_,1),_.m(r.parentNode,r)):_&&(Be(),G(_,1,1,()=>{_=null}),De())},i(i){f||(X(e.$$.fragment,i),X(_),f=!0)},o(i){G(e.$$.fragment,i),G(_),f=!1},d(i){i&&(p(n),p(l),p(a),p(r)),ee(e,i),_&&_.d(i),h=!1,ze(g)}}}function tt(t,e,n){let l,{value:a="#000000"}=e,{value_is_output:r=!1}=e,{label:f}=e,{info:h=void 0}=e,{disabled:g=!1}=e,{show_label:_=!0}=e,{current_mode:i="hex"}=e,{dialog_open:c=!1}=e,o=!1,m,B;const d=Xe();let O=[0,0],w=null,P=!1,T=[0,0],N=0,q=0,I=null,b=!1;function L(u){I=u.currentTarget.getBoundingClientRect(),b=!0,v(u.clientX)}function v(u){if(!I)return;const S=Math.max(0,Math.min(u-I.left,I.width));n(13,q=S);const J=S/I.width*360;n(12,N=J),n(1,a=de({h:J,s:T[0],v:T[1],a:1}))}function s(u,S){if(!w)return;const J=Math.max(0,Math.min(u-w.left,w.width)),ne=Math.max(0,Math.min(S-w.top,w.height));n(11,O=[J,ne]);const oe={h:N*1,s:J/w.width,v:1-ne/w.height,a:1};T=[oe.s,oe.v],n(1,a=de(oe))}function E(u){P=!0,w=u.currentTarget.getBoundingClientRect(),s(u.clientX,u.clientY)}function k(u){P&&s(u.clientX,u.clientY),b&&v(u.clientX)}function F(){P=!1,b=!1}async function Y(u){if(P||b||(await je(),!u)||(!w&&m&&(w=m.getBoundingClientRect()),!I&&B&&(I=B.getBoundingClientRect()),!w||!I))return;const S=se(u).toHsv(),J=S.s*w.width,ne=(1-S.v)*w.height;n(11,O=[J,ne]),T=[S.s,S.v],n(12,N=S.h),n(13,q=S.h/360*I.width)}function K(){new EyeDropper().open().then(S=>{n(1,a=S.sRGBHex)})}const Q=[["Hex","hex"],["RGB","rgb"],["HSL","hsl"]];Oe(async()=>{n(8,o=window!==void 0&&!!window.EyeDropper)});function te(){n(2,c=!1)}function le(){d("change",a),r||d("input")}Pe(()=>{n(23,r=!1)});function W(){d("selected",l),d("close")}function Ce(u){fe.call(this,t,u)}function Me(u){fe.call(this,t,u)}const Ve=()=>{Y(a),n(2,c=!c)};function Re(u){ie[u?"unshift":"push"](()=>{m=u,n(9,m)})}function Se(u){ie[u?"unshift":"push"](()=>{B=u,n(10,B)})}function He(){l=this.value,n(7,l),n(1,a),n(0,i)}const Ne=u=>n(1,a=u.currentTarget.value),Ue=u=>n(0,i=u);return t.$$set=u=>{"value"in u&&n(1,a=u.value),"value_is_output"in u&&n(23,r=u.value_is_output),"label"in u&&n(6,f=u.label),"info"in u&&n(3,h=u.info),"disabled"in u&&n(4,g=u.disabled),"show_label"in u&&n(5,_=u.show_label),"current_mode"in u&&n(0,i=u.current_mode),"dialog_open"in u&&n(2,c=u.dialog_open)},t.$$.update=()=>{t.$$.dirty[0]&3&&n(7,l=xe(a,i)),t.$$.dirty[0]&128&&l&&d("selected",l),t.$$.dirty[0]&2&&Y(a),t.$$.dirty[0]&2&&le()},[i,a,c,h,g,_,f,l,o,m,B,O,N,q,L,E,k,F,Y,K,Q,te,W,r,Ce,Me,Ve,Re,Se,He,Ne,Ue]}class lt extends ke{constructor(e){super(),Ee(this,e,tt,et,pe,{value:1,value_is_output:23,label:6,info:3,disabled:4,show_label:5,current_mode:0,dialog_open:2},null,[-1,-1])}}const nt=lt;function st(t){let e,n,l,a,r,f;const h=[{autoscroll:t[12].autoscroll},{i18n:t[12].i18n},t[11]];let g={};for(let o=0;o<h.length;o+=1)g=ye(g,h[o]);e=new We({props:g}),e.$on("clear_status",t[15]);function _(o){t[16](o)}function i(o){t[17](o)}let c={label:t[2],info:t[3],show_label:t[7],disabled:!t[13]||t[14]};return t[0]!==void 0&&(c.value=t[0]),t[1]!==void 0&&(c.value_is_output=t[1]),l=new nt({props:c}),ie.push(()=>_e(l,"value",_)),ie.push(()=>_e(l,"value_is_output",i)),l.$on("change",t[18]),l.$on("input",t[19]),l.$on("submit",t[20]),l.$on("blur",t[21]),l.$on("focus",t[22]),{c(){Z(e.$$.fragment),n=j(),Z(l.$$.fragment)},l(o){x(e.$$.fragment,o),n=y(o),x(l.$$.fragment,o)},m(o,m){$(e,o,m),A(o,n,m),$(l,o,m),f=!0},p(o,m){const B=m&6144?Ae(h,[m&4096&&{autoscroll:o[12].autoscroll},m&4096&&{i18n:o[12].i18n},m&2048&&Fe(o[11])]):{};e.$set(B);const d={};m&4&&(d.label=o[2]),m&8&&(d.info=o[3]),m&128&&(d.show_label=o[7]),m&24576&&(d.disabled=!o[13]||o[14]),!a&&m&1&&(a=!0,d.value=o[0],he(()=>a=!1)),!r&&m&2&&(r=!0,d.value_is_output=o[1],he(()=>r=!1)),l.$set(d)},i(o){f||(X(e.$$.fragment,o),X(l.$$.fragment,o),f=!0)},o(o){G(e.$$.fragment,o),G(l.$$.fragment,o),f=!1},d(o){o&&p(n),ee(e,o),ee(l,o)}}}function it(t){let e,n;return e=new Qe({props:{visible:t[6],elem_id:t[4],elem_classes:t[5],container:t[8],scale:t[9],min_width:t[10],$$slots:{default:[st]},$$scope:{ctx:t}}}),{c(){Z(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,a){$(e,l,a),n=!0},p(l,[a]){const r={};a&64&&(r.visible=l[6]),a&16&&(r.elem_id=l[4]),a&32&&(r.elem_classes=l[5]),a&256&&(r.container=l[8]),a&512&&(r.scale=l[9]),a&1024&&(r.min_width=l[10]),a&8419471&&(r.$$scope={dirty:a,ctx:l}),e.$set(r)},i(l){n||(X(e.$$.fragment,l),n=!0)},o(l){G(e.$$.fragment,l),n=!1},d(l){ee(e,l)}}}function ot(t,e,n){let{label:l="ColorPicker"}=e,{info:a=void 0}=e,{elem_id:r=""}=e,{elem_classes:f=[]}=e,{visible:h=!0}=e,{value:g}=e,{value_is_output:_=!1}=e,{show_label:i}=e,{container:c=!0}=e,{scale:o=null}=e,{min_width:m=void 0}=e,{loading_status:B}=e,{gradio:d}=e,{interactive:O}=e,{disabled:w=!1}=e;const P=()=>d.dispatch("clear_status",B);function T(s){g=s,n(0,g)}function N(s){_=s,n(1,_)}const q=()=>d.dispatch("change"),I=()=>d.dispatch("input"),b=()=>d.dispatch("submit"),L=()=>d.dispatch("blur"),v=()=>d.dispatch("focus");return t.$$set=s=>{"label"in s&&n(2,l=s.label),"info"in s&&n(3,a=s.info),"elem_id"in s&&n(4,r=s.elem_id),"elem_classes"in s&&n(5,f=s.elem_classes),"visible"in s&&n(6,h=s.visible),"value"in s&&n(0,g=s.value),"value_is_output"in s&&n(1,_=s.value_is_output),"show_label"in s&&n(7,i=s.show_label),"container"in s&&n(8,c=s.container),"scale"in s&&n(9,o=s.scale),"min_width"in s&&n(10,m=s.min_width),"loading_status"in s&&n(11,B=s.loading_status),"gradio"in s&&n(12,d=s.gradio),"interactive"in s&&n(13,O=s.interactive),"disabled"in s&&n(14,w=s.disabled)},[g,_,l,a,r,f,h,i,c,o,m,B,d,O,w,P,T,N,q,I,b,L,v]}class ft extends ke{constructor(e){super(),Ee(this,e,ot,it,pe,{label:2,info:3,elem_id:4,elem_classes:5,visible:6,value:0,value_is_output:1,show_label:7,container:8,scale:9,min_width:10,loading_status:11,gradio:12,interactive:13,disabled:14})}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),z()}get info(){return this.$$.ctx[3]}set info(e){this.$$set({info:e}),z()}get elem_id(){return this.$$.ctx[4]}set elem_id(e){this.$$set({elem_id:e}),z()}get elem_classes(){return this.$$.ctx[5]}set elem_classes(e){this.$$set({elem_classes:e}),z()}get visible(){return this.$$.ctx[6]}set visible(e){this.$$set({visible:e}),z()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),z()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),z()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),z()}get container(){return this.$$.ctx[8]}set container(e){this.$$set({container:e}),z()}get scale(){return this.$$.ctx[9]}set scale(e){this.$$set({scale:e}),z()}get min_width(){return this.$$.ctx[10]}set min_width(e){this.$$set({min_width:e}),z()}get loading_status(){return this.$$.ctx[11]}set loading_status(e){this.$$set({loading_status:e}),z()}get gradio(){return this.$$.ctx[12]}set gradio(e){this.$$set({gradio:e}),z()}get interactive(){return this.$$.ctx[13]}set interactive(e){this.$$set({interactive:e}),z()}get disabled(){return this.$$.ctx[14]}set disabled(e){this.$$set({disabled:e}),z()}}export{nt as BaseColorPicker,dt as BaseExample,ft as default};
//# sourceMappingURL=Index.C4qVdpCH.js.map
