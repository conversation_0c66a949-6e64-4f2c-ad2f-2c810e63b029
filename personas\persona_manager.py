"""
人设管理器
管理不同的AI人设和角色配置
"""

import os
import yaml
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from pathlib import Path

from core.utils.logger import get_logger
from core.utils.config import config_manager

logger = get_logger(__name__)

@dataclass
class PersonaConfig:
    """人设配置类"""
    name: str
    id: str
    description: str
    avatar: str
    nsfw_enabled: bool
    personality: Dict[str, Any]
    system_prompt: str
    conversation_examples: List[Dict[str, str]]
    special_commands: Dict[str, Any]
    emotional_states: Dict[str, str]
    nsfw_settings: Optional[Dict[str, Any]] = None

class PersonaManager:
    """人设管理器"""
    
    def __init__(self, config_path: str = None):
        self.config_path = config_path or config_manager.get('personas.config_path', 'personas/configs')
        self.current_persona: Optional[PersonaConfig] = None
        self.available_personas: Dict[str, PersonaConfig] = {}
        
        # 加载所有人设
        self._load_personas()
        
        # 设置默认人设
        default_persona = config_manager.get('personas.default_persona', 'wife')
        if default_persona in self.available_personas:
            self.switch_persona(default_persona)
    
    def _load_personas(self):
        """加载所有人设配置"""
        if not os.path.exists(self.config_path):
            logger.warning(f"人设配置目录不存在: {self.config_path}")
            return
        
        for file in os.listdir(self.config_path):
            if file.endswith('.yaml') or file.endswith('.yml'):
                file_path = os.path.join(self.config_path, file)
                try:
                    persona = self._load_persona_from_file(file_path)
                    if persona:
                        self.available_personas[persona.id] = persona
                        logger.info(f"加载人设: {persona.name} ({persona.id})")
                except Exception as e:
                    logger.error(f"加载人设配置失败 {file}: {e}")
    
    def _load_persona_from_file(self, file_path: str) -> Optional[PersonaConfig]:
        """从文件加载人设配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            return PersonaConfig(
                name=data['name'],
                id=data['id'],
                description=data['description'],
                avatar=data.get('avatar', ''),
                nsfw_enabled=data.get('nsfw_enabled', False),
                personality=data.get('personality', {}),
                system_prompt=data['system_prompt'],
                conversation_examples=data.get('conversation_examples', []),
                special_commands=data.get('special_commands', {}),
                emotional_states=data.get('emotional_states', {}),
                nsfw_settings=data.get('nsfw_settings')
            )
        except Exception as e:
            logger.error(f"解析人设配置失败: {e}")
            return None
    
    def get_available_personas(self) -> Dict[str, Dict[str, Any]]:
        """获取可用人设列表"""
        personas_info = {}
        for persona_id, persona in self.available_personas.items():
            personas_info[persona_id] = {
                'name': persona.name,
                'description': persona.description,
                'avatar': persona.avatar,
                'nsfw_enabled': persona.nsfw_enabled
            }
        return personas_info
    
    def switch_persona(self, persona_id: str) -> bool:
        """切换人设"""
        if persona_id not in self.available_personas:
            logger.error(f"人设不存在: {persona_id}")
            return False
        
        self.current_persona = self.available_personas[persona_id]
        logger.info(f"切换到人设: {self.current_persona.name}")
        return True
    
    def get_current_persona(self) -> Optional[PersonaConfig]:
        """获取当前人设"""
        return self.current_persona
    
    def get_system_prompt(self) -> str:
        """获取当前人设的系统提示词"""
        if not self.current_persona:
            return "你是Reverie，一个友好的AI助手。"
        
        return self.current_persona.system_prompt
    
    def get_persona_info(self, persona_id: str = None) -> Optional[Dict[str, Any]]:
        """获取人设信息"""
        persona = self.current_persona if persona_id is None else self.available_personas.get(persona_id)
        
        if not persona:
            return None
        
        return {
            'name': persona.name,
            'id': persona.id,
            'description': persona.description,
            'avatar': persona.avatar,
            'nsfw_enabled': persona.nsfw_enabled,
            'personality': persona.personality,
            'emotional_states': persona.emotional_states
        }
    
    def check_special_command(self, user_input: str) -> Optional[Dict[str, Any]]:
        """检查是否触发特殊指令"""
        if not self.current_persona:
            return None
        
        for command_type, command_config in self.current_persona.special_commands.items():
            trigger_words = command_config.get('trigger_words', [])
            
            for trigger in trigger_words:
                if trigger in user_input:
                    return {
                        'type': command_type,
                        'config': command_config,
                        'response_template': command_config.get('response_template', '')
                    }
        
        return None
    
    def get_conversation_examples(self) -> List[Dict[str, str]]:
        """获取对话示例"""
        if not self.current_persona:
            return []
        
        return self.current_persona.conversation_examples
    
    def is_nsfw_enabled(self) -> bool:
        """检查是否启用NSFW内容"""
        if not self.current_persona:
            return False
        
        # 检查全局设置
        global_nsfw = config_manager.get('personas.enable_nsfw', True)
        if not global_nsfw:
            return False
        
        # 检查人设设置
        return self.current_persona.nsfw_enabled
    
    def get_nsfw_settings(self) -> Dict[str, Any]:
        """获取NSFW设置"""
        if not self.current_persona or not self.current_persona.nsfw_settings:
            return {}
        
        return self.current_persona.nsfw_settings
    
    def create_persona(self, persona_data: Dict[str, Any]) -> bool:
        """创建新人设"""
        try:
            # 验证必需字段
            required_fields = ['name', 'id', 'description', 'system_prompt']
            for field in required_fields:
                if field not in persona_data:
                    logger.error(f"缺少必需字段: {field}")
                    return False
            
            # 检查ID是否已存在
            if persona_data['id'] in self.available_personas:
                logger.error(f"人设ID已存在: {persona_data['id']}")
                return False
            
            # 创建人设配置
            persona = PersonaConfig(
                name=persona_data['name'],
                id=persona_data['id'],
                description=persona_data['description'],
                avatar=persona_data.get('avatar', ''),
                nsfw_enabled=persona_data.get('nsfw_enabled', False),
                personality=persona_data.get('personality', {}),
                system_prompt=persona_data['system_prompt'],
                conversation_examples=persona_data.get('conversation_examples', []),
                special_commands=persona_data.get('special_commands', {}),
                emotional_states=persona_data.get('emotional_states', {}),
                nsfw_settings=persona_data.get('nsfw_settings')
            )
            
            # 保存到文件
            file_path = os.path.join(self.config_path, f"{persona.id}.yaml")
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(persona_data, f, default_flow_style=False, allow_unicode=True)
            
            # 添加到可用人设
            self.available_personas[persona.id] = persona
            
            logger.info(f"创建人设成功: {persona.name}")
            return True
            
        except Exception as e:
            logger.error(f"创建人设失败: {e}")
            return False
    
    def delete_persona(self, persona_id: str) -> bool:
        """删除人设"""
        if persona_id not in self.available_personas:
            logger.error(f"人设不存在: {persona_id}")
            return False
        
        try:
            # 删除文件
            file_path = os.path.join(self.config_path, f"{persona_id}.yaml")
            if os.path.exists(file_path):
                os.remove(file_path)
            
            # 从内存中移除
            del self.available_personas[persona_id]
            
            # 如果删除的是当前人设，切换到默认人设
            if self.current_persona and self.current_persona.id == persona_id:
                default_personas = ['wife', 'reasoning_expert', 'travel_guide', 'career_mentor']
                for default_id in default_personas:
                    if default_id in self.available_personas:
                        self.switch_persona(default_id)
                        break
                else:
                    self.current_persona = None
            
            logger.info(f"删除人设成功: {persona_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除人设失败: {e}")
            return False
    
    def reload_personas(self):
        """重新加载所有人设"""
        self.available_personas.clear()
        self._load_personas()
        logger.info("人设配置已重新加载")

# 全局人设管理器实例
persona_manager = PersonaManager()
