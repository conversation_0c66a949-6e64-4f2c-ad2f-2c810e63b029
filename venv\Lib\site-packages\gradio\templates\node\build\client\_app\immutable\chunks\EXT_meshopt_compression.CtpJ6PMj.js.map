{"version": 3, "file": "EXT_meshopt_compression.CtpJ6PMj.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Meshes/Compression/meshoptCompression.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/EXT_meshopt_compression.js"], "sourcesContent": ["import { Tools } from \"../../Misc/tools.js\";\n// eslint-disable-next-line @typescript-eslint/naming-convention\nlet NumberOfWorkers = 0;\n// eslint-disable-next-line @typescript-eslint/naming-convention\nlet WorkerTimeout = null;\n/**\n * Meshopt compression (https://github.com/zeux/meshoptimizer)\n *\n * This class wraps the meshopt library from https://github.com/zeux/meshoptimizer/tree/master/js.\n *\n * **Encoder**\n *\n * The encoder is not currently implemented.\n *\n * **Decoder**\n *\n * By default, the configuration points to a copy of the meshopt files on the Babylon.js preview CDN (e.g. https://preview.babylonjs.com/meshopt_decoder.js).\n *\n * To update the configuration, use the following code:\n * ```javascript\n *     MeshoptCompression.Configuration = {\n *         decoder: {\n *             url: \"<url to the meshopt decoder library>\"\n *         }\n *     };\n * ```\n */\nexport class MeshoptCompression {\n    /**\n     * Default instance for the meshoptimizer object.\n     */\n    static get Default() {\n        if (!MeshoptCompression._Default) {\n            MeshoptCompression._Default = new MeshoptCompression();\n        }\n        return MeshoptCompression._Default;\n    }\n    /**\n     * Constructor\n     */\n    constructor() {\n        const decoder = MeshoptCompression.Configuration.decoder;\n        this._decoderModulePromise = Tools.LoadBabylonScriptAsync(decoder.url).then(() => {\n            // Wait for WebAssembly compilation before resolving promise\n            return MeshoptDecoder.ready;\n        });\n    }\n    /**\n     * Stop all async operations and release resources.\n     */\n    dispose() {\n        delete this._decoderModulePromise;\n    }\n    /**\n     * Decode meshopt data.\n     * @see https://github.com/zeux/meshoptimizer/tree/master/js#decoder\n     * @param source The input data.\n     * @param count The number of elements.\n     * @param stride The stride in bytes.\n     * @param mode The compression mode.\n     * @param filter The compression filter.\n     * @returns a Promise<Uint8Array> that resolves to the decoded data\n     */\n    decodeGltfBufferAsync(source, count, stride, mode, filter) {\n        return this._decoderModulePromise.then(async () => {\n            if (NumberOfWorkers === 0) {\n                MeshoptDecoder.useWorkers(1);\n                NumberOfWorkers = 1;\n            }\n            const result = await MeshoptDecoder.decodeGltfBufferAsync(count, stride, source, mode, filter);\n            // a simple debounce to avoid switching back and forth between workers and no workers while decoding\n            if (WorkerTimeout !== null) {\n                clearTimeout(WorkerTimeout);\n            }\n            WorkerTimeout = setTimeout(() => {\n                MeshoptDecoder.useWorkers(0);\n                NumberOfWorkers = 0;\n                WorkerTimeout = null;\n            }, 1000);\n            return result;\n        });\n    }\n}\n/**\n * The configuration. Defaults to the following:\n * ```javascript\n * decoder: {\n *   url: \"https://cdn.babylonjs.com/meshopt_decoder.js\"\n * }\n * ```\n */\nMeshoptCompression.Configuration = {\n    decoder: {\n        url: `${Tools._DefaultCdnUrl}/meshopt_decoder.js`,\n    },\n};\nMeshoptCompression._Default = null;\n//# sourceMappingURL=meshoptCompression.js.map", "import { ArrayItem, GLTFLoader } from \"../glTFLoader.js\";\nimport { MeshoptCompression } from \"@babylonjs/core/Meshes/Compression/meshoptCompression.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"EXT_meshopt_compression\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Vendor/EXT_meshopt_compression/README.md)\n *\n * This extension uses a WebAssembly decoder module from https://github.com/zeux/meshoptimizer/tree/master/js\n * @since 5.0.0\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class EXT_meshopt_compression {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        this.enabled = loader.isExtensionUsed(NAME);\n        this._loader = loader;\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /**\n     * @internal\n     */\n    loadBufferViewAsync(context, bufferView) {\n        return GLTFLoader.LoadExtensionAsync(context, bufferView, this.name, (extensionContext, extension) => {\n            const bufferViewMeshopt = bufferView;\n            if (bufferViewMeshopt._meshOptData) {\n                return bufferViewMeshopt._meshOptData;\n            }\n            const buffer = ArrayItem.Get(`${context}/buffer`, this._loader.gltf.buffers, extension.buffer);\n            bufferViewMeshopt._meshOptData = this._loader.loadBufferAsync(`/buffers/${buffer.index}`, buffer, extension.byteOffset || 0, extension.byteLength).then((buffer) => {\n                return MeshoptCompression.Default.decodeGltfBufferAsync(buffer, extension.count, extension.byteStride, extension.mode, extension.filter);\n            });\n            return bufferViewMeshopt._meshOptData;\n        });\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new EXT_meshopt_compression(loader));\n//# sourceMappingURL=EXT_meshopt_compression.js.map"], "names": ["NumberOfWorkers", "WorkerTimeout", "MeshoptCompression", "decoder", "Tools", "source", "count", "stride", "mode", "filter", "result", "NAME", "EXT_meshopt_compression", "loader", "context", "bufferView", "GLTFLoader", "extensionContext", "extension", "bufferViewMeshopt", "buffer", "ArrayItem", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "6HAEA,IAAIA,EAAkB,EAElBC,EAAgB,KAuBb,MAAMC,CAAmB,CAI5B,WAAW,SAAU,CACjB,OAAKA,EAAmB,WACpBA,EAAmB,SAAW,IAAIA,GAE/BA,EAAmB,QAC7B,CAID,aAAc,CACV,MAAMC,EAAUD,EAAmB,cAAc,QACjD,KAAK,sBAAwBE,EAAM,uBAAuBD,EAAQ,GAAG,EAAE,KAAK,IAEjE,eAAe,KACzB,CACJ,CAID,SAAU,CACN,OAAO,KAAK,qBACf,CAWD,sBAAsBE,EAAQC,EAAOC,EAAQC,EAAMC,EAAQ,CACvD,OAAO,KAAK,sBAAsB,KAAK,SAAY,CAC3CT,IAAoB,IACpB,eAAe,WAAW,CAAC,EAC3BA,EAAkB,GAEtB,MAAMU,EAAS,MAAM,eAAe,sBAAsBJ,EAAOC,EAAQF,EAAQG,EAAMC,CAAM,EAE7F,OAAIR,IAAkB,MAClB,aAAaA,CAAa,EAE9BA,EAAgB,WAAW,IAAM,CAC7B,eAAe,WAAW,CAAC,EAC3BD,EAAkB,EAClBC,EAAgB,IACnB,EAAE,GAAI,EACAS,CACnB,CAAS,CACJ,CACL,CASAR,EAAmB,cAAgB,CAC/B,QAAS,CACL,IAAK,GAAGE,EAAM,cAAc,qBAC/B,CACL,EACAF,EAAmB,SAAW,KC7F9B,MAAMS,EAAO,0BAQN,MAAMC,CAAwB,CAIjC,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EACZ,KAAK,QAAUE,EAAO,gBAAgBF,CAAI,EAC1C,KAAK,QAAUE,CAClB,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAID,oBAAoBC,EAASC,EAAY,CACrC,OAAOC,EAAW,mBAAmBF,EAASC,EAAY,KAAK,KAAM,CAACE,EAAkBC,IAAc,CAClG,MAAMC,EAAoBJ,EAC1B,GAAII,EAAkB,aAClB,OAAOA,EAAkB,aAE7B,MAAMC,EAASC,EAAU,IAAI,GAAGP,CAAO,UAAW,KAAK,QAAQ,KAAK,QAASI,EAAU,MAAM,EAC7F,OAAAC,EAAkB,aAAe,KAAK,QAAQ,gBAAgB,YAAYC,EAAO,KAAK,GAAIA,EAAQF,EAAU,YAAc,EAAGA,EAAU,UAAU,EAAE,KAAME,GAC9IlB,EAAmB,QAAQ,sBAAsBkB,EAAQF,EAAU,MAAOA,EAAU,WAAYA,EAAU,KAAMA,EAAU,MAAM,CAC1I,EACMC,EAAkB,YACrC,CAAS,CACJ,CACL,CACAG,EAAwBX,CAAI,EAC5BY,EAAsBZ,EAAM,GAAOE,GAAW,IAAID,EAAwBC,CAAM,CAAC", "x_google_ignoreList": [0, 1]}