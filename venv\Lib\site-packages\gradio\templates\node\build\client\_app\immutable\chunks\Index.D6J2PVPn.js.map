{"version": 3, "file": "Index.D6J2PVPn.js", "sources": ["../../../../../../../dataframe/shared/utils/sort_utils.ts", "../../../../../../../dataframe/shared/utils/filter_utils.ts", "../../../../../../../dataframe/shared/utils/table_utils.ts", "../../../../../../../dataframe/shared/selection_utils.ts", "../../../../../../../dataframe/shared/context/dataframe_context.ts", "../../../../../../../dataframe/shared/icons/SelectionButtons.svelte", "../../../../../../../dataframe/shared/BooleanCell.svelte", "../../../../../../../dataframe/shared/EditableCell.svelte", "../../../../../../../dataframe/shared/RowNumber.svelte", "../../../../../../../dataframe/shared/CellMenuButton.svelte", "../../../../../../../dataframe/shared/icons/Padlock.svelte", "../../../../../../../dataframe/shared/icons/SortArrowUp.svelte", "../../../../../../../dataframe/shared/icons/SortArrowDown.svelte", "../../../../../../../dataframe/shared/CellMenuIcons.svelte", "../../../../../../../dataframe/shared/TableHeader.svelte", "../../../../../../../dataframe/shared/TableCell.svelte", "../../../../../../../dataframe/shared/EmptyRowButton.svelte", "../../../../../../../dataframe/shared/VirtualTable.svelte", "../../../../../../../dataframe/shared/FilterMenu.svelte", "../../../../../../../dataframe/shared/CellMenu.svelte", "../../../../../../../dataframe/shared/Toolbar.svelte", "../../../../../../../dataframe/shared/utils/data_processing.ts", "../../../../../../../dataframe/shared/utils.ts", "../../../../../../../dataframe/shared/utils/keyboard_utils.ts", "../../../../../../../dataframe/shared/utils/drag_utils.ts", "../../../../../../../dataframe/shared/Table.svelte", "../../../../../../../dataframe/Index.svelte"], "sourcesContent": ["import type { Headers } from \"../types\";\nimport { sort_table_data } from \"./table_utils\";\n\nexport type SortDirection = \"asc\" | \"desc\";\n\nexport function get_sort_status(\n\tname: string,\n\tsort_columns: { col: number; direction: SortDirection }[],\n\theaders: Headers\n): \"none\" | \"asc\" | \"desc\" {\n\tif (!sort_columns.length) return \"none\";\n\n\tconst sort_item = sort_columns.find((item) => {\n\t\tconst col = item.col;\n\t\tif (col < 0 || col >= headers.length) return false;\n\t\treturn headers[col] === name;\n\t});\n\n\tif (!sort_item) return \"none\";\n\treturn sort_item.direction;\n}\n\nexport function sort_data(\n\tdata: { id: string; value: string | number }[][],\n\tsort_columns: { col: number; direction: SortDirection }[]\n): number[] {\n\tif (!data || !data.length || !data[0]) {\n\t\treturn [];\n\t}\n\n\tif (sort_columns.length > 0) {\n\t\tconst row_indices = [...Array(data.length)].map((_, i) => i);\n\t\trow_indices.sort((row_a_idx, row_b_idx) => {\n\t\t\tconst row_a = data[row_a_idx];\n\t\t\tconst row_b = data[row_b_idx];\n\n\t\t\tfor (const { col: sort_by, direction } of sort_columns) {\n\t\t\t\tif (\n\t\t\t\t\t!row_a ||\n\t\t\t\t\t!row_b ||\n\t\t\t\t\tsort_by < 0 ||\n\t\t\t\t\tsort_by >= row_a.length ||\n\t\t\t\t\tsort_by >= row_b.length ||\n\t\t\t\t\t!row_a[sort_by] ||\n\t\t\t\t\t!row_b[sort_by]\n\t\t\t\t) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tconst val_a = row_a[sort_by].value;\n\t\t\t\tconst val_b = row_b[sort_by].value;\n\t\t\t\tconst comparison = val_a < val_b ? -1 : val_a > val_b ? 1 : 0;\n\n\t\t\t\tif (comparison !== 0) {\n\t\t\t\t\treturn direction === \"asc\" ? comparison : -comparison;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn 0;\n\t\t});\n\t\treturn row_indices;\n\t}\n\treturn [...Array(data.length)].map((_, i) => i);\n}\n\nexport function sort_data_and_preserve_selection(\n\tdata: { id: string; value: string | number }[][],\n\tdisplay_value: string[][] | null,\n\tstyling: string[][] | null,\n\tsort_columns: { col: number; direction: SortDirection }[],\n\tselected: [number, number] | false,\n\tget_current_indices: (\n\t\tid: string,\n\t\tdata: { id: string; value: string | number }[][]\n\t) => [number, number]\n): { data: typeof data; selected: [number, number] | false } {\n\tlet id = null;\n\tif (selected && selected[0] in data && selected[1] in data[selected[0]]) {\n\t\tid = data[selected[0]][selected[1]].id;\n\t}\n\n\tsort_table_data(data, display_value, styling, sort_columns);\n\n\tlet new_selected = selected;\n\tif (id) {\n\t\tconst [i, j] = get_current_indices(id, data);\n\t\tnew_selected = [i, j];\n\t}\n\n\treturn { data, selected: new_selected };\n}\n", "import { filter_table_data } from \"./table_utils\";\n\nexport type FilterDatatype = \"string\" | \"number\";\n\nexport function filter_data(\n\tdata: { id: string; value: string | number }[][],\n\tfilter_columns: {\n\t\tcol: number;\n\t\tdatatype: FilterDatatype;\n\t\tfilter: string;\n\t\tvalue: string;\n\t}[]\n): number[] {\n\tif (!data || !data.length || !data[0]) {\n\t\treturn [];\n\t}\n\tlet row_indices = [...Array(data.length)].map((_, i) => i);\n\n\tif (filter_columns.length > 0) {\n\t\tfilter_columns.forEach((column) => {\n\t\t\tif (column.datatype === \"string\") {\n\t\t\t\tswitch (column.filter) {\n\t\t\t\t\tcase \"Contains\":\n\t\t\t\t\t\trow_indices = row_indices.filter((i) =>\n\t\t\t\t\t\t\tdata[i][column.col]?.value.toString().includes(column.value)\n\t\t\t\t\t\t);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"Does not contain\":\n\t\t\t\t\t\trow_indices = row_indices.filter(\n\t\t\t\t\t\t\t(i) =>\n\t\t\t\t\t\t\t\t!data[i][column.col]?.value.toString().includes(column.value)\n\t\t\t\t\t\t);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"Starts with\":\n\t\t\t\t\t\trow_indices = row_indices.filter((i) =>\n\t\t\t\t\t\t\tdata[i][column.col]?.value.toString().startsWith(column.value)\n\t\t\t\t\t\t);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"Ends with\":\n\t\t\t\t\t\trow_indices = row_indices.filter((i) =>\n\t\t\t\t\t\t\tdata[i][column.col]?.value.toString().endsWith(column.value)\n\t\t\t\t\t\t);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"Is\":\n\t\t\t\t\t\trow_indices = row_indices.filter(\n\t\t\t\t\t\t\t(i) => data[i][column.col]?.value.toString() === column.value\n\t\t\t\t\t\t);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"Is not\":\n\t\t\t\t\t\trow_indices = row_indices.filter(\n\t\t\t\t\t\t\t(i) => !(data[i][column.col]?.value.toString() === column.value)\n\t\t\t\t\t\t);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"Is empty\":\n\t\t\t\t\t\trow_indices = row_indices.filter(\n\t\t\t\t\t\t\t(i) => data[i][column.col]?.value.toString() === \"\"\n\t\t\t\t\t\t);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"Is not empty\":\n\t\t\t\t\t\trow_indices = row_indices.filter(\n\t\t\t\t\t\t\t(i) => !(data[i][column.col]?.value.toString() === \"\")\n\t\t\t\t\t\t);\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t} else if (column.datatype === \"number\") {\n\t\t\t\tswitch (column.filter) {\n\t\t\t\t\tcase \"=\":\n\t\t\t\t\t\trow_indices = row_indices.filter((i) => {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t!isNaN(Number(data[i][column.col]?.value)) &&\n\t\t\t\t\t\t\t\t!isNaN(Number(column.value))\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\t\tNumber(data[i][column.col]?.value) === Number(column.value)\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"≠\":\n\t\t\t\t\t\trow_indices = row_indices.filter((i) => {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t!isNaN(Number(data[i][column.col]?.value)) &&\n\t\t\t\t\t\t\t\t!isNaN(Number(column.value))\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\treturn !(\n\t\t\t\t\t\t\t\t\tNumber(data[i][column.col]?.value) === Number(column.value)\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \">\":\n\t\t\t\t\t\trow_indices = row_indices.filter((i) => {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t!isNaN(Number(data[i][column.col]?.value)) &&\n\t\t\t\t\t\t\t\t!isNaN(Number(column.value))\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\t\tNumber(data[i][column.col]?.value) > Number(column.value)\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"<\":\n\t\t\t\t\t\trow_indices = row_indices.filter((i) => {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t!isNaN(Number(data[i][column.col]?.value)) &&\n\t\t\t\t\t\t\t\t!isNaN(Number(column.value))\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\t\tNumber(data[i][column.col]?.value) < Number(column.value)\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"≥\":\n\t\t\t\t\t\trow_indices = row_indices.filter((i) => {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t!isNaN(Number(data[i][column.col]?.value)) &&\n\t\t\t\t\t\t\t\t!isNaN(Number(column.value))\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\t\tNumber(data[i][column.col]?.value) >= Number(column.value)\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"≤\":\n\t\t\t\t\t\trow_indices = row_indices.filter((i) => {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t!isNaN(Number(data[i][column.col]?.value)) &&\n\t\t\t\t\t\t\t\t!isNaN(Number(column.value))\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\t\tNumber(data[i][column.col]?.value) <= Number(column.value)\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"Is empty\":\n\t\t\t\t\t\trow_indices = row_indices.filter(\n\t\t\t\t\t\t\t(i) => data[i][column.col]?.value.toString() === \"\"\n\t\t\t\t\t\t);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"Is not empty\":\n\t\t\t\t\t\trow_indices = row_indices.filter((i) => {\n\t\t\t\t\t\t\tif (!isNaN(Number(data[i][column.col]?.value))) {\n\t\t\t\t\t\t\t\treturn !(data[i][column.col]?.value.toString() === \"\");\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t\treturn row_indices;\n\t}\n\treturn [...Array(data.length)].map((_, i) => i);\n}\n\nexport function filter_data_and_preserve_selection(\n\tdata: { id: string; value: string | number }[][],\n\tdisplay_value: string[][] | null,\n\tstyling: string[][] | null,\n\tfilter_columns: {\n\t\tcol: number;\n\t\tdatatype: FilterDatatype;\n\t\tfilter: string;\n\t\tvalue: string;\n\t}[],\n\tselected: [number, number] | false,\n\tget_current_indices: (\n\t\tid: string,\n\t\tdata: { id: string; value: string | number }[][]\n\t) => [number, number],\n\toriginal_data?: { id: string; value: string | number }[][],\n\toriginal_display_value?: string[][] | null,\n\toriginal_styling?: string[][] | null\n): { data: typeof data; selected: [number, number] | false } {\n\tlet id = null;\n\tif (selected && selected[0] in data && selected[1] in data[selected[0]]) {\n\t\tid = data[selected[0]][selected[1]].id;\n\t}\n\n\tfilter_table_data(\n\t\tdata,\n\t\tdisplay_value,\n\t\tstyling,\n\t\tfilter_columns,\n\t\toriginal_data,\n\t\toriginal_display_value,\n\t\toriginal_styling\n\t);\n\n\tlet new_selected = selected;\n\tif (id) {\n\t\tconst [i, j] = get_current_indices(id, data);\n\t\tnew_selected = [i, j];\n\t}\n\n\treturn { data, selected: new_selected };\n}\n", "import type { <PERSON><PERSON>, <PERSON>ersWithIDs, TableCell, TableData } from \"../types\";\nimport { sort_data } from \"./sort_utils\";\nimport { filter_data } from \"./filter_utils\";\nimport type { SortDirection } from \"./sort_utils\";\nimport type { FilterDatatype } from \"./filter_utils\";\nimport { dsvFormat } from \"d3-dsv\";\n\nexport function make_cell_id(row: number, col: number): string {\n\treturn `cell-${row}-${col}`;\n}\n\nexport function make_header_id(col: number): string {\n\treturn `header-${col}`;\n}\n\nexport function get_max(data: TableData): TableCell[] {\n\tif (!data || !data.length) return [];\n\tlet max = data[0].slice();\n\tfor (let i = 0; i < data.length; i++) {\n\t\tfor (let j = 0; j < data[i].length; j++) {\n\t\t\tif (`${max[j].value}`.length < `${data[i][j].value}`.length) {\n\t\t\t\tmax[j] = data[i][j];\n\t\t\t}\n\t\t}\n\t}\n\treturn max;\n}\n\nexport function sort_table_data(\n\tdata: TableData,\n\tdisplay_value: string[][] | null,\n\tstyling: string[][] | null,\n\tsort_columns: { col: number; direction: SortDirection }[]\n): void {\n\tif (!sort_columns.length) return;\n\tif (!data || !data.length) return;\n\n\tconst indices = sort_data(data, sort_columns);\n\n\tconst new_data = indices.map((i: number) => data[i]);\n\tdata.splice(0, data.length, ...new_data);\n\n\tif (display_value) {\n\t\tconst new_display = indices.map((i: number) => display_value[i]);\n\t\tdisplay_value.splice(0, display_value.length, ...new_display);\n\t}\n\n\tif (styling) {\n\t\tconst new_styling = indices.map((i: number) => styling[i]);\n\t\tstyling.splice(0, styling.length, ...new_styling);\n\t}\n}\n\nexport function filter_table_data(\n\tdata: TableData,\n\tdisplay_value: string[][] | null,\n\tstyling: string[][] | null,\n\tfilter_columns: {\n\t\tcol: number;\n\t\tdatatype: FilterDatatype;\n\t\tfilter: string;\n\t\tvalue: string;\n\t}[],\n\toriginal_data?: TableData,\n\toriginal_display_value?: string[][] | null,\n\toriginal_styling?: string[][] | null\n): void {\n\tconst base_data = original_data ?? data;\n\tconst base_display_value = original_display_value ?? display_value;\n\tconst base_styling = original_styling ?? styling;\n\n\tif (!filter_columns.length) {\n\t\tdata.splice(0, data.length, ...base_data.map((row) => [...row]));\n\t\tif (display_value && base_display_value) {\n\t\t\tdisplay_value.splice(\n\t\t\t\t0,\n\t\t\t\tdisplay_value.length,\n\t\t\t\t...base_display_value.map((row) => [...row])\n\t\t\t);\n\t\t}\n\t\tif (styling && base_styling) {\n\t\t\tstyling.splice(0, styling.length, ...base_styling.map((row) => [...row]));\n\t\t}\n\t\treturn;\n\t}\n\tif (!data || !data.length) return;\n\n\tconst indices = filter_data(base_data, filter_columns);\n\n\tconst new_data = indices.map((i: number) => base_data[i]);\n\tdata.splice(0, data.length, ...new_data);\n\n\tif (display_value && base_display_value) {\n\t\tconst new_display = indices.map((i: number) => base_display_value[i]);\n\t\tdisplay_value.splice(0, display_value.length, ...new_display);\n\t}\n\n\tif (styling && base_styling) {\n\t\tconst new_styling = indices.map((i: number) => base_styling[i]);\n\t\tstyling.splice(0, styling.length, ...new_styling);\n\t}\n}\n\nexport async function copy_table_data(\n\tdata: TableData,\n\tselected_cells: [number, number][] | null\n): Promise<void> {\n\tif (!data || !data.length) return;\n\n\tconst cells_to_copy =\n\t\tselected_cells ||\n\t\tdata.flatMap((row, r) => row.map((_, c) => [r, c] as [number, number]));\n\n\tconst csv = cells_to_copy.reduce(\n\t\t(acc: { [key: string]: { [key: string]: string } }, [row, col]) => {\n\t\t\tacc[row] = acc[row] || {};\n\t\t\tconst value = String(data[row][col].value);\n\t\t\tacc[row][col] =\n\t\t\t\tvalue.includes(\",\") || value.includes('\"') || value.includes(\"\\n\")\n\t\t\t\t\t? `\"${value.replace(/\"/g, '\"\"')}\"`\n\t\t\t\t\t: value;\n\t\t\treturn acc;\n\t\t},\n\t\t{}\n\t);\n\n\tconst rows = Object.keys(csv).sort((a, b) => +a - +b);\n\tif (!rows.length) return;\n\n\tconst cols = Object.keys(csv[rows[0]]).sort((a, b) => +a - +b);\n\tconst text = rows\n\t\t.map((r) => cols.map((c) => csv[r][c] || \"\").join(\",\"))\n\t\t.join(\"\\n\");\n\n\ttry {\n\t\tawait navigator.clipboard.writeText(text);\n\t} catch (err) {\n\t\tthrow new Error(\"Failed to copy to clipboard: \" + (err as Error).message);\n\t}\n}\n\n// File Import/Export\nexport function guess_delimiter(\n\ttext: string,\n\tpossibleDelimiters: string[]\n): string[] {\n\treturn possibleDelimiters.filter(weedOut);\n\n\tfunction weedOut(delimiter: string): boolean {\n\t\tvar cache = -1;\n\t\treturn text.split(\"\\n\").every(checkLength);\n\n\t\tfunction checkLength(line: string): boolean {\n\t\t\tif (!line) return true;\n\t\t\tvar length = line.split(delimiter).length;\n\t\t\tif (cache < 0) cache = length;\n\t\t\treturn cache === length && length > 1;\n\t\t}\n\t}\n}\n\nexport function data_uri_to_blob(data_uri: string): Blob {\n\tconst byte_str = atob(data_uri.split(\",\")[1]);\n\tconst mime_str = data_uri.split(\",\")[0].split(\":\")[1].split(\";\")[0];\n\tconst ab = new ArrayBuffer(byte_str.length);\n\tconst ia = new Uint8Array(ab);\n\tfor (let i = 0; i < byte_str.length; i++) {\n\t\tia[i] = byte_str.charCodeAt(i);\n\t}\n\treturn new Blob([ab], { type: mime_str });\n}\n\nexport function handle_file_upload(\n\tdata_uri: string,\n\tupdate_headers: (headers: Headers) => HeadersWithIDs[],\n\tupdate_values: (values: (string | number)[][]) => void\n): void {\n\tconst blob = data_uri_to_blob(data_uri);\n\tconst reader = new FileReader();\n\treader.addEventListener(\"loadend\", (e) => {\n\t\tif (!e?.target?.result || typeof e.target.result !== \"string\") return;\n\t\tconst [delimiter] = guess_delimiter(e.target.result, [\",\", \"\\t\"]);\n\t\tconst [head, ...rest] = dsvFormat(delimiter).parseRows(e.target.result);\n\t\tupdate_headers(head);\n\t\tupdate_values(rest);\n\t});\n\treader.readAsText(blob);\n}\n", "import type { CellCoordinate } from \"./types\";\n\nexport type CellData = { id: string; value: string | number };\n\nexport function is_cell_in_selection(\n\tcoords: [number, number],\n\tselected_cells: [number, number][]\n): boolean {\n\tconst [row, col] = coords;\n\treturn selected_cells.some(([r, c]) => r === row && c === col);\n}\n\nexport function is_cell_selected(\n\tcell: CellCoordinate,\n\tselected_cells: CellCoordinate[]\n): string {\n\tconst [row, col] = cell;\n\tif (!selected_cells.some(([r, c]) => r === row && c === col)) return \"\";\n\n\tconst up = selected_cells.some(([r, c]) => r === row - 1 && c === col);\n\tconst down = selected_cells.some(([r, c]) => r === row + 1 && c === col);\n\tconst left = selected_cells.some(([r, c]) => r === row && c === col - 1);\n\tconst right = selected_cells.some(([r, c]) => r === row && c === col + 1);\n\n\treturn `cell-selected${up ? \" no-top\" : \"\"}${down ? \" no-bottom\" : \"\"}${left ? \" no-left\" : \"\"}${right ? \" no-right\" : \"\"}`;\n}\n\nexport function get_range_selection(\n\tstart: CellCoordinate,\n\tend: CellCoordinate\n): CellCoordinate[] {\n\tconst [start_row, start_col] = start;\n\tconst [end_row, end_col] = end;\n\tconst min_row = Math.min(start_row, end_row);\n\tconst max_row = Math.max(start_row, end_row);\n\tconst min_col = Math.min(start_col, end_col);\n\tconst max_col = Math.max(start_col, end_col);\n\n\tconst cells: CellCoordinate[] = [];\n\t// Add the start cell as the \"anchor\" cell so that when\n\t// we press shift+arrow keys, the selection will always\n\t// include the anchor cell.\n\tcells.push(start);\n\n\tfor (let i = min_row; i <= max_row; i++) {\n\t\tfor (let j = min_col; j <= max_col; j++) {\n\t\t\tif (i === start_row && j === start_col) continue;\n\t\t\tcells.push([i, j]);\n\t\t}\n\t}\n\treturn cells;\n}\n\nexport function handle_selection(\n\tcurrent: CellCoordinate,\n\tselected_cells: CellCoordinate[],\n\tevent: { shiftKey: boolean; metaKey: boolean; ctrlKey: boolean }\n): CellCoordinate[] {\n\tif (event.shiftKey && selected_cells.length > 0) {\n\t\treturn get_range_selection(\n\t\t\tselected_cells[selected_cells.length - 1],\n\t\t\tcurrent\n\t\t);\n\t}\n\n\tif (event.metaKey || event.ctrlKey) {\n\t\tconst is_cell_match = ([r, c]: CellCoordinate): boolean =>\n\t\t\tr === current[0] && c === current[1];\n\t\tconst index = selected_cells.findIndex(is_cell_match);\n\t\treturn index === -1\n\t\t\t? [...selected_cells, current]\n\t\t\t: selected_cells.filter((_, i) => i !== index);\n\t}\n\n\treturn [current];\n}\n\nexport function handle_delete_key(\n\tdata: CellData[][],\n\tselected_cells: CellCoordinate[]\n): CellData[][] {\n\tconst new_data = data.map((row) => [...row]);\n\tselected_cells.forEach(([row, col]) => {\n\t\tif (new_data[row] && new_data[row][col]) {\n\t\t\tnew_data[row][col] = { ...new_data[row][col], value: \"\" };\n\t\t}\n\t});\n\treturn new_data;\n}\n\nexport function should_show_cell_menu(\n\tcell: CellCoordinate,\n\tselected_cells: CellCoordinate[],\n\teditable: boolean\n): boolean {\n\tconst [row, col] = cell;\n\treturn (\n\t\teditable &&\n\t\tselected_cells.length === 1 &&\n\t\tselected_cells[0][0] === row &&\n\t\tselected_cells[0][1] === col\n\t);\n}\n\nexport function get_next_cell_coordinates(\n\tcurrent: CellCoordinate,\n\tdata: CellData[][],\n\tshift_key: boolean\n): CellCoordinate | false {\n\tconst [row, col] = current;\n\tconst direction = shift_key ? -1 : 1;\n\n\tif (data[row]?.[col + direction]) {\n\t\treturn [row, col + direction];\n\t}\n\n\tconst next_row = row + (direction > 0 ? 1 : 0);\n\tconst prev_row = row + (direction < 0 ? -1 : 0);\n\n\tif (direction > 0 && data[next_row]?.[0]) {\n\t\treturn [next_row, 0];\n\t}\n\n\tif (direction < 0 && data[prev_row]?.[data[0].length - 1]) {\n\t\treturn [prev_row, data[0].length - 1];\n\t}\n\n\treturn false;\n}\n\nexport function move_cursor(\n\tevent: KeyboardEvent,\n\tcurrent_coords: CellCoordinate,\n\tdata: CellData[][]\n): CellCoordinate | false {\n\tconst key = event.key as \"ArrowRight\" | \"ArrowLeft\" | \"ArrowDown\" | \"ArrowUp\";\n\tconst dir = {\n\t\tArrowRight: [0, 1],\n\t\tArrowLeft: [0, -1],\n\t\tArrowDown: [1, 0],\n\t\tArrowUp: [-1, 0]\n\t}[key];\n\n\tlet i, j;\n\tif (event.metaKey || event.ctrlKey) {\n\t\tif (key === \"ArrowRight\") {\n\t\t\ti = current_coords[0];\n\t\t\tj = data[0].length - 1;\n\t\t} else if (key === \"ArrowLeft\") {\n\t\t\ti = current_coords[0];\n\t\t\tj = 0;\n\t\t} else if (key === \"ArrowDown\") {\n\t\t\ti = data.length - 1;\n\t\t\tj = current_coords[1];\n\t\t} else if (key === \"ArrowUp\") {\n\t\t\ti = 0;\n\t\t\tj = current_coords[1];\n\t\t} else {\n\t\t\treturn false;\n\t\t}\n\t} else {\n\t\ti = current_coords[0] + dir[0];\n\t\tj = current_coords[1] + dir[1];\n\t}\n\n\tif (i < 0 && j <= 0) {\n\t\treturn false;\n\t}\n\n\tconst is_data = data[i]?.[j];\n\tif (is_data) {\n\t\treturn [i, j];\n\t}\n\treturn false;\n}\n\nexport function get_current_indices(\n\tid: string,\n\tdata: CellData[][]\n): [number, number] {\n\treturn data.reduce(\n\t\t(acc, arr, i) => {\n\t\t\tconst j = arr.reduce(\n\t\t\t\t(_acc, _data, k) => (id === _data.id ? k : _acc),\n\t\t\t\t-1\n\t\t\t);\n\t\t\treturn j === -1 ? acc : [i, j];\n\t\t},\n\t\t[-1, -1]\n\t);\n}\n\nexport function handle_click_outside(\n\tevent: Event,\n\tparent: HTMLElement\n): boolean {\n\tconst [trigger] = event.composedPath() as HTMLElement[];\n\treturn !parent.contains(trigger);\n}\n\nexport function select_column(data: any[][], col: number): CellCoordinate[] {\n\treturn Array.from({ length: data.length }, (_, i) => [i, col]);\n}\n\nexport function select_row(data: any[][], row: number): CellCoordinate[] {\n\treturn Array.from({ length: data[0].length }, (_, i) => [row, i]);\n}\n\nexport function calculate_selection_positions(\n\tselected: CellCoordinate,\n\tdata: { id: string; value: string | number }[][],\n\tels: Record<string, { cell: HTMLTableCellElement | null }>,\n\tparent: HTMLElement,\n\ttable: HTMLElement\n): { col_pos: string; row_pos: string | undefined } {\n\tconst [row, col] = selected;\n\tif (!data[row]?.[col]) {\n\t\treturn { col_pos: \"0px\", row_pos: undefined };\n\t}\n\n\tconst cell_id = data[row][col].id;\n\tconst cell_el = els[cell_id]?.cell;\n\n\tif (!cell_el) {\n\t\treturn { col_pos: \"0px\", row_pos: undefined };\n\t}\n\n\tconst cell_rect = cell_el.getBoundingClientRect();\n\tconst table_rect = table.getBoundingClientRect();\n\tconst col_pos = `${cell_rect.left - table_rect.left + cell_rect.width / 2}px`;\n\tconst row_pos = `${cell_rect.top - table_rect.top + cell_rect.height / 2}px`;\n\n\treturn { col_pos, row_pos };\n}\n", "import { getContext, setContext } from \"svelte\";\nimport { dequal } from \"dequal\";\nimport { writable, get } from \"svelte/store\";\nimport { sort_table_data } from \"../utils/table_utils\";\nimport { tick } from \"svelte\";\nimport {\n\thandle_selection,\n\tget_next_cell_coordinates,\n\tget_range_selection,\n\tmove_cursor\n} from \"../selection_utils\";\n\nexport const DATAFRAME_KEY = Symbol(\"dataframe\");\n\nexport type SortDirection = \"asc\" | \"desc\";\nexport type FilterDatatype = \"string\" | \"number\";\nexport type CellCoordinate = [number, number];\n\ninterface DataFrameState {\n\tconfig: {\n\t\tshow_fullscreen_button: boolean;\n\t\tshow_copy_button: boolean;\n\t\tshow_search: \"none\" | \"search\" | \"filter\";\n\t\tshow_row_numbers: boolean;\n\t\teditable: boolean;\n\t\tpinned_columns: number;\n\t\tshow_label: boolean;\n\t\tline_breaks: boolean;\n\t\twrap: boolean;\n\t\tmax_height: number;\n\t\tcolumn_widths: string[];\n\t\tmax_chars?: number;\n\t\tstatic_columns?: (string | number)[];\n\t};\n\tcurrent_search_query: string | null;\n\tsort_state: {\n\t\tsort_columns: { col: number; direction: SortDirection }[];\n\t\trow_order: number[];\n\t\tinitial_data: {\n\t\t\tdata: { id: string; value: string | number }[][];\n\t\t\tdisplay_value: string[][] | null;\n\t\t\tstyling: string[][] | null;\n\t\t} | null;\n\t};\n\tfilter_state: {\n\t\tfilter_columns: {\n\t\t\tcol: number;\n\t\t\tdatatype: FilterDatatype;\n\t\t\tfilter: string;\n\t\t\tvalue: string;\n\t\t}[];\n\t\tinitial_data: {\n\t\t\tdata: { id: string; value: string | number }[][];\n\t\t\tdisplay_value: string[][] | null;\n\t\t\tstyling: string[][] | null;\n\t\t} | null;\n\t};\n\tui_state: {\n\t\tactive_cell_menu: { row: number; col: number; x: number; y: number } | null;\n\t\tactive_header_menu: { col: number; x: number; y: number } | null;\n\t\tselected_cells: CellCoordinate[];\n\t\tselected: CellCoordinate | false;\n\t\tediting: CellCoordinate | false;\n\t\theader_edit: number | false;\n\t\tselected_header: number | false;\n\t\tactive_button: {\n\t\t\ttype: \"header\" | \"cell\";\n\t\t\trow?: number;\n\t\t\tcol: number;\n\t\t} | null;\n\t\tcopy_flash: boolean;\n\t};\n}\n\ninterface DataFrameActions {\n\thandle_search: (query: string | null) => void;\n\thandle_sort: (col: number, direction: SortDirection) => void;\n\thandle_filter: (\n\t\tcol: number,\n\t\tdatatype: FilterDatatype,\n\t\tfilter: string,\n\t\tvalue: string\n\t) => void;\n\tget_sort_status: (name: string, headers: string[]) => \"none\" | \"asc\" | \"desc\";\n\tsort_data: (\n\t\tdata: any[][],\n\t\tdisplay_value: string[][] | null,\n\t\tstyling: string[][] | null\n\t) => void;\n\tupdate_row_order: (data: any[][]) => void;\n\tfilter_data: (data: any[][]) => any[][];\n\tadd_row: (data: any[][], make_id: () => string, index?: number) => any[][];\n\tadd_col: (\n\t\tdata: any[][],\n\t\theaders: string[],\n\t\tmake_id: () => string,\n\t\tindex?: number\n\t) => { data: any[][]; headers: string[] };\n\tadd_row_at: (\n\t\tdata: any[][],\n\t\tindex: number,\n\t\tposition: \"above\" | \"below\",\n\t\tmake_id: () => string\n\t) => any[][];\n\tadd_col_at: (\n\t\tdata: any[][],\n\t\theaders: string[],\n\t\tindex: number,\n\t\tposition: \"left\" | \"right\",\n\t\tmake_id: () => string\n\t) => { data: any[][]; headers: string[] };\n\tdelete_row: (data: any[][], index: number) => any[][];\n\tdelete_col: (\n\t\tdata: any[][],\n\t\theaders: string[],\n\t\tindex: number\n\t) => { data: any[][]; headers: string[] };\n\tdelete_row_at: (data: any[][], index: number) => any[][];\n\tdelete_col_at: (\n\t\tdata: any[][],\n\t\theaders: string[],\n\t\tindex: number\n\t) => { data: any[][]; headers: string[] };\n\ttrigger_change: (\n\t\tdata: any[][],\n\t\theaders: any[],\n\t\tprevious_data: string[][],\n\t\tprevious_headers: string[],\n\t\tvalue_is_output: boolean,\n\t\tdispatch: (e: \"change\" | \"input\", detail?: any) => void\n\t) => Promise<void>;\n\treset_sort_state: () => void;\n\treset_filter_state: () => void;\n\tset_active_cell_menu: (\n\t\tmenu: { row: number; col: number; x: number; y: number } | null\n\t) => void;\n\tset_active_header_menu: (\n\t\tmenu: { col: number; x: number; y: number } | null\n\t) => void;\n\tset_selected_cells: (cells: CellCoordinate[]) => void;\n\tset_selected: (selected: CellCoordinate | false) => void;\n\tset_editing: (editing: CellCoordinate | false) => void;\n\tclear_ui_state: () => void;\n\tset_header_edit: (header_index: number | false) => void;\n\tset_selected_header: (header_index: number | false) => void;\n\thandle_header_click: (col: number, editable: boolean) => void;\n\tend_header_edit: (key: string) => void;\n\tget_selected_cells: () => CellCoordinate[];\n\tget_active_cell_menu: () => {\n\t\trow: number;\n\t\tcol: number;\n\t\tx: number;\n\t\ty: number;\n\t} | null;\n\tget_active_button: () => {\n\t\ttype: \"header\" | \"cell\";\n\t\trow?: number;\n\t\tcol: number;\n\t} | null;\n\tset_active_button: (\n\t\tbutton: { type: \"header\" | \"cell\"; row?: number; col: number } | null\n\t) => void;\n\tset_copy_flash: (value: boolean) => void;\n\thandle_cell_click: (event: MouseEvent, row: number, col: number) => void;\n\ttoggle_cell_menu: (event: MouseEvent, row: number, col: number) => void;\n\ttoggle_cell_button: (row: number, col: number) => void;\n\thandle_select_column: (col: number) => void;\n\thandle_select_row: (row: number) => void;\n\tget_next_cell_coordinates: typeof get_next_cell_coordinates;\n\tget_range_selection: typeof get_range_selection;\n\tmove_cursor: typeof move_cursor;\n}\n\nexport interface DataFrameContext {\n\tstate: ReturnType<typeof writable<DataFrameState>>;\n\tactions: DataFrameActions;\n\tdata?: any[][];\n\theaders?: { id: string; value: string }[];\n\tdisplay_value?: string[][] | null;\n\tstyling?: string[][] | null;\n\tels?: Record<\n\t\tstring,\n\t\t{ cell: HTMLTableCellElement | null; input: HTMLTextAreaElement | null }\n\t>;\n\tparent_element?: HTMLElement;\n\tget_data_at?: (row: number, col: number) => string | number;\n\tget_column?: (col: number) => (string | number)[];\n\tget_row?: (row: number) => (string | number)[];\n\tdispatch?: (e: \"change\" | \"select\" | \"search\", detail?: any) => void;\n}\n\nfunction create_actions(\n\tstate: ReturnType<typeof writable<DataFrameState>>,\n\tcontext: DataFrameContext\n): DataFrameActions {\n\tconst update_state = (\n\t\tupdater: (s: DataFrameState) => Partial<DataFrameState>\n\t): void => state.update((s) => ({ ...s, ...updater(s) }));\n\n\tconst add_row = (\n\t\tdata: any[][],\n\t\tmake_id: () => string,\n\t\tindex?: number\n\t): any[][] => {\n\t\tconst new_row = data[0]?.length\n\t\t\t? Array(data[0].length)\n\t\t\t\t\t.fill(null)\n\t\t\t\t\t.map(() => ({ value: \"\", id: make_id() }))\n\t\t\t: [{ value: \"\", id: make_id() }];\n\t\tconst new_data = [...data];\n\t\tindex !== undefined\n\t\t\t? new_data.splice(index, 0, new_row)\n\t\t\t: new_data.push(new_row);\n\t\treturn new_data;\n\t};\n\n\tconst add_col = (\n\t\tdata: any[][],\n\t\theaders: string[],\n\t\tmake_id: () => string,\n\t\tindex?: number\n\t): { data: any[][]; headers: string[] } => {\n\t\tconst new_headers = context.headers\n\t\t\t? [...headers.map((h) => context.headers![headers.indexOf(h)].value)]\n\t\t\t: [...headers, `Header ${headers.length + 1}`];\n\t\tconst new_data = data.map((row) => [...row, { value: \"\", id: make_id() }]);\n\t\tif (index !== undefined) {\n\t\t\tnew_headers.splice(index, 0, new_headers.pop()!);\n\t\t\tnew_data.forEach((row) => row.splice(index, 0, row.pop()!));\n\t\t}\n\t\treturn { data: new_data, headers: new_headers };\n\t};\n\n\tconst update_array = (\n\t\tsource: { id: string; value: string | number }[][] | string[][] | null,\n\t\ttarget: any[] | null | undefined\n\t): void => {\n\t\tif (source && target) {\n\t\t\ttarget.splice(0, target.length, ...JSON.parse(JSON.stringify(source)));\n\t\t}\n\t};\n\n\treturn {\n\t\thandle_search: (query) =>\n\t\t\tupdate_state((s) => ({ current_search_query: query })),\n\t\thandle_sort: (col, direction) =>\n\t\t\tupdate_state((s) => {\n\t\t\t\tconst sort_cols = s.sort_state.sort_columns.filter(\n\t\t\t\t\t(c) => c.col !== col\n\t\t\t\t);\n\t\t\t\tif (\n\t\t\t\t\t!s.sort_state.sort_columns.some(\n\t\t\t\t\t\t(c) => c.col === col && c.direction === direction\n\t\t\t\t\t)\n\t\t\t\t) {\n\t\t\t\t\tsort_cols.push({ col, direction });\n\t\t\t\t}\n\n\t\t\t\tconst initial_data =\n\t\t\t\t\ts.sort_state.initial_data ||\n\t\t\t\t\t(context.data && sort_cols.length > 0\n\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\tdata: JSON.parse(JSON.stringify(context.data)),\n\t\t\t\t\t\t\t\tdisplay_value: context.display_value\n\t\t\t\t\t\t\t\t\t? JSON.parse(JSON.stringify(context.display_value))\n\t\t\t\t\t\t\t\t\t: null,\n\t\t\t\t\t\t\t\tstyling: context.styling\n\t\t\t\t\t\t\t\t\t? JSON.parse(JSON.stringify(context.styling))\n\t\t\t\t\t\t\t\t\t: null\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t: null);\n\n\t\t\t\treturn {\n\t\t\t\t\tsort_state: {\n\t\t\t\t\t\t...s.sort_state,\n\t\t\t\t\t\tsort_columns: sort_cols.slice(-3),\n\t\t\t\t\t\tinitial_data: initial_data\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}),\n\t\thandle_filter: (col, datatype, filter, value) =>\n\t\t\tupdate_state((s) => {\n\t\t\t\tconst filter_cols = s.filter_state.filter_columns.some(\n\t\t\t\t\t(c) => c.col === col\n\t\t\t\t)\n\t\t\t\t\t? s.filter_state.filter_columns.filter((c) => c.col !== col)\n\t\t\t\t\t: [\n\t\t\t\t\t\t\t...s.filter_state.filter_columns,\n\t\t\t\t\t\t\t{ col, datatype, filter, value }\n\t\t\t\t\t\t];\n\n\t\t\t\tconst initial_data =\n\t\t\t\t\ts.filter_state.initial_data ||\n\t\t\t\t\t(context.data && filter_cols.length > 0\n\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\tdata: JSON.parse(JSON.stringify(context.data)),\n\t\t\t\t\t\t\t\tdisplay_value: context.display_value\n\t\t\t\t\t\t\t\t\t? JSON.parse(JSON.stringify(context.display_value))\n\t\t\t\t\t\t\t\t\t: null,\n\t\t\t\t\t\t\t\tstyling: context.styling\n\t\t\t\t\t\t\t\t\t? JSON.parse(JSON.stringify(context.styling))\n\t\t\t\t\t\t\t\t\t: null\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t: null);\n\n\t\t\t\treturn {\n\t\t\t\t\tfilter_state: {\n\t\t\t\t\t\t...s.filter_state,\n\t\t\t\t\t\tfilter_columns: filter_cols,\n\t\t\t\t\t\tinitial_data: initial_data\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}),\n\t\tget_sort_status: (name, headers) => {\n\t\t\tconst s = get(state);\n\t\t\tconst sort_item = s.sort_state.sort_columns.find(\n\t\t\t\t(item) => headers[item.col] === name\n\t\t\t);\n\t\t\treturn sort_item ? sort_item.direction : \"none\";\n\t\t},\n\t\tsort_data: (data, display_value, styling) => {\n\t\t\tconst {\n\t\t\t\tsort_state: { sort_columns }\n\t\t\t} = get(state);\n\t\t\tif (sort_columns.length)\n\t\t\t\tsort_table_data(data, display_value, styling, sort_columns);\n\t\t},\n\t\tupdate_row_order: (data) =>\n\t\t\tupdate_state((s) => ({\n\t\t\t\tsort_state: {\n\t\t\t\t\t...s.sort_state,\n\t\t\t\t\trow_order:\n\t\t\t\t\t\ts.sort_state.sort_columns.length && data[0]\n\t\t\t\t\t\t\t? [...Array(data.length)]\n\t\t\t\t\t\t\t\t\t.map((_, i) => i)\n\t\t\t\t\t\t\t\t\t.sort((a, b) => {\n\t\t\t\t\t\t\t\t\t\tfor (const { col, direction } of s.sort_state\n\t\t\t\t\t\t\t\t\t\t\t.sort_columns) {\n\t\t\t\t\t\t\t\t\t\t\tconst comp =\n\t\t\t\t\t\t\t\t\t\t\t\t(data[a]?.[col]?.value ?? \"\") <\n\t\t\t\t\t\t\t\t\t\t\t\t(data[b]?.[col]?.value ?? \"\")\n\t\t\t\t\t\t\t\t\t\t\t\t\t? -1\n\t\t\t\t\t\t\t\t\t\t\t\t\t: 1;\n\t\t\t\t\t\t\t\t\t\t\tif (comp) return direction === \"asc\" ? comp : -comp;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\treturn 0;\n\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t: [...Array(data.length)].map((_, i) => i)\n\t\t\t\t}\n\t\t\t})),\n\t\tfilter_data: (data) => {\n\t\t\tconst query = get(state).current_search_query?.toLowerCase();\n\t\t\treturn query\n\t\t\t\t? data.filter((row) =>\n\t\t\t\t\t\trow.some((cell) =>\n\t\t\t\t\t\t\tString(cell?.value).toLowerCase().includes(query)\n\t\t\t\t\t\t)\n\t\t\t\t\t)\n\t\t\t\t: data;\n\t\t},\n\t\tadd_row,\n\t\tadd_col,\n\t\tadd_row_at: (data, index, position, make_id) =>\n\t\t\tadd_row(data, make_id, position === \"above\" ? index : index + 1),\n\t\tadd_col_at: (data, headers, index, position, make_id) =>\n\t\t\tadd_col(data, headers, make_id, position === \"left\" ? index : index + 1),\n\t\tdelete_row: (data, index) =>\n\t\t\tdata.length > 1 ? data.filter((_, i) => i !== index) : data,\n\t\tdelete_col: (data, headers, index) =>\n\t\t\theaders.length > 1\n\t\t\t\t? {\n\t\t\t\t\t\tdata: data.map((row) => row.filter((_, i) => i !== index)),\n\t\t\t\t\t\theaders: headers.filter((_, i) => i !== index)\n\t\t\t\t\t}\n\t\t\t\t: { data, headers },\n\t\tdelete_row_at: (data, index) =>\n\t\t\tdata.length > 1\n\t\t\t\t? [...data.slice(0, index), ...data.slice(index + 1)]\n\t\t\t\t: data,\n\t\tdelete_col_at: (data, headers, index) =>\n\t\t\theaders.length > 1\n\t\t\t\t? {\n\t\t\t\t\t\tdata: data.map((row) => [\n\t\t\t\t\t\t\t...row.slice(0, index),\n\t\t\t\t\t\t\t...row.slice(index + 1)\n\t\t\t\t\t\t]),\n\t\t\t\t\t\theaders: [...headers.slice(0, index), ...headers.slice(index + 1)]\n\t\t\t\t\t}\n\t\t\t\t: { data, headers },\n\t\ttrigger_change: async (\n\t\t\tdata,\n\t\t\theaders,\n\t\t\tprevious_data,\n\t\t\tprevious_headers,\n\t\t\tvalue_is_output,\n\t\t\tdispatch\n\t\t) => {\n\t\t\tconst s = get(state);\n\t\t\tif (s.current_search_query) return;\n\n\t\t\tconst current_headers = headers.map((h) => h.value);\n\t\t\tconst current_data = data.map((row) =>\n\t\t\t\trow.map((cell) => String(cell.value))\n\t\t\t);\n\n\t\t\tif (\n\t\t\t\t!dequal(current_data, previous_data) ||\n\t\t\t\t!dequal(current_headers, previous_headers)\n\t\t\t) {\n\t\t\t\tif (!dequal(current_headers, previous_headers)) {\n\t\t\t\t\tupdate_state((s) => ({\n\t\t\t\t\t\tsort_state: { sort_columns: [], row_order: [], initial_data: null },\n\t\t\t\t\t\tfilter_state: { filter_columns: [], initial_data: null }\n\t\t\t\t\t}));\n\t\t\t\t}\n\t\t\t\tdispatch(\"change\", {\n\t\t\t\t\tdata: data.map((row) => row.map((cell) => cell.value)),\n\t\t\t\t\theaders: current_headers,\n\t\t\t\t\tmetadata: null\n\t\t\t\t});\n\t\t\t\tif (!value_is_output) dispatch(\"input\");\n\t\t\t}\n\t\t},\n\t\treset_sort_state: () =>\n\t\t\tupdate_state((s) => {\n\t\t\t\tif (s.sort_state.initial_data && context.data) {\n\t\t\t\t\tconst original = s.sort_state.initial_data;\n\n\t\t\t\t\tupdate_array(original.data, context.data);\n\t\t\t\t\tupdate_array(original.display_value, context.display_value);\n\t\t\t\t\tupdate_array(original.styling, context.styling);\n\t\t\t\t}\n\n\t\t\t\treturn {\n\t\t\t\t\tsort_state: { sort_columns: [], row_order: [], initial_data: null }\n\t\t\t\t};\n\t\t\t}),\n\t\treset_filter_state: () =>\n\t\t\tupdate_state((s) => {\n\t\t\t\tif (s.filter_state.initial_data && context.data) {\n\t\t\t\t\tconst original = s.filter_state.initial_data;\n\n\t\t\t\t\tupdate_array(original.data, context.data);\n\t\t\t\t\tupdate_array(original.display_value, context.display_value);\n\t\t\t\t\tupdate_array(original.styling, context.styling);\n\t\t\t\t}\n\n\t\t\t\treturn {\n\t\t\t\t\tfilter_state: { filter_columns: [], initial_data: null }\n\t\t\t\t};\n\t\t\t}),\n\t\tset_active_cell_menu: (menu) =>\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: { ...s.ui_state, active_cell_menu: menu }\n\t\t\t})),\n\t\tset_active_header_menu: (menu) =>\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: { ...s.ui_state, active_header_menu: menu }\n\t\t\t})),\n\t\tset_selected_cells: (cells) =>\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: { ...s.ui_state, selected_cells: cells }\n\t\t\t})),\n\t\tset_selected: (selected) =>\n\t\t\tupdate_state((s) => ({ ui_state: { ...s.ui_state, selected } })),\n\t\tset_editing: (editing) =>\n\t\t\tupdate_state((s) => ({ ui_state: { ...s.ui_state, editing } })),\n\t\tclear_ui_state: () =>\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: {\n\t\t\t\t\tactive_cell_menu: null,\n\t\t\t\t\tactive_header_menu: null,\n\t\t\t\t\tselected_cells: [],\n\t\t\t\t\tselected: false,\n\t\t\t\t\tediting: false,\n\t\t\t\t\theader_edit: false,\n\t\t\t\t\tselected_header: false,\n\t\t\t\t\tactive_button: null,\n\t\t\t\t\tcopy_flash: false\n\t\t\t\t}\n\t\t\t})),\n\t\tset_header_edit: (header_index) =>\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: {\n\t\t\t\t\t...s.ui_state,\n\t\t\t\t\tselected_cells: [],\n\t\t\t\t\tselected_header: header_index,\n\t\t\t\t\theader_edit: header_index\n\t\t\t\t}\n\t\t\t})),\n\t\tset_selected_header: (header_index) =>\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: {\n\t\t\t\t\t...s.ui_state,\n\t\t\t\t\tselected_header: header_index,\n\t\t\t\t\tselected: false,\n\t\t\t\t\tselected_cells: []\n\t\t\t\t}\n\t\t\t})),\n\t\thandle_header_click: (col, editable) =>\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: {\n\t\t\t\t\t...s.ui_state,\n\t\t\t\t\tactive_cell_menu: null,\n\t\t\t\t\tactive_header_menu: null,\n\t\t\t\t\tselected: false,\n\t\t\t\t\tselected_cells: [],\n\t\t\t\t\tselected_header: col,\n\t\t\t\t\theader_edit: editable ? col : false\n\t\t\t\t}\n\t\t\t})),\n\t\tend_header_edit: (key) => {\n\t\t\tif ([\"Escape\", \"Enter\", \"Tab\"].includes(key)) {\n\t\t\t\tupdate_state((s) => ({\n\t\t\t\t\tui_state: { ...s.ui_state, selected: false, header_edit: false }\n\t\t\t\t}));\n\t\t\t}\n\t\t},\n\t\tget_selected_cells: () => get(state).ui_state.selected_cells,\n\t\tget_active_cell_menu: () => get(state).ui_state.active_cell_menu,\n\t\tget_active_button: () => get(state).ui_state.active_button,\n\t\tset_active_button: (button) =>\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: { ...s.ui_state, active_button: button }\n\t\t\t})),\n\t\tset_copy_flash: (value) =>\n\t\t\tupdate_state((s) => ({ ui_state: { ...s.ui_state, copy_flash: value } })),\n\t\thandle_cell_click: (event, row, col) => {\n\t\t\tevent.preventDefault();\n\t\t\tevent.stopPropagation();\n\n\t\t\tconst s = get(state);\n\t\t\tif (s.config.show_row_numbers && col === -1) return;\n\n\t\t\tlet actual_row = row;\n\t\t\tif (s.current_search_query && context.data) {\n\t\t\t\tconst filtered_indices: number[] = [];\n\t\t\t\tcontext.data.forEach((dataRow, idx) => {\n\t\t\t\t\tif (\n\t\t\t\t\t\tdataRow.some((cell) =>\n\t\t\t\t\t\t\tString(cell?.value)\n\t\t\t\t\t\t\t\t.toLowerCase()\n\t\t\t\t\t\t\t\t.includes(s.current_search_query?.toLowerCase() || \"\")\n\t\t\t\t\t\t)\n\t\t\t\t\t) {\n\t\t\t\t\t\tfiltered_indices.push(idx);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tactual_row = filtered_indices[row] ?? row;\n\t\t\t}\n\n\t\t\tconst cells = handle_selection(\n\t\t\t\t[actual_row, col],\n\t\t\t\ts.ui_state.selected_cells,\n\t\t\t\tevent\n\t\t\t);\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: {\n\t\t\t\t\t...s.ui_state,\n\t\t\t\t\tactive_cell_menu: null,\n\t\t\t\t\tactive_header_menu: null,\n\t\t\t\t\tselected_header: false,\n\t\t\t\t\theader_edit: false,\n\t\t\t\t\tselected_cells: cells,\n\t\t\t\t\tselected: cells[0]\n\t\t\t\t}\n\t\t\t}));\n\n\t\t\tif (s.config.editable && cells.length === 1) {\n\t\t\t\tupdate_state((s) => ({\n\t\t\t\t\tui_state: { ...s.ui_state, editing: [actual_row, col] }\n\t\t\t\t}));\n\t\t\t\ttick().then(() =>\n\t\t\t\t\tcontext.els![context.data![actual_row][col].id]?.input?.focus()\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\t// ensure parent has focus for keyboard navigation\n\t\t\t\ttick().then(() => {\n\t\t\t\t\tif (context.parent_element) {\n\t\t\t\t\t\tcontext.parent_element.focus();\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tcontext.dispatch?.(\"select\", {\n\t\t\t\tindex: [actual_row, col],\n\t\t\t\tcol_value: context.get_column!(col),\n\t\t\t\trow_value: context.get_row!(actual_row),\n\t\t\t\tvalue: context.get_data_at!(actual_row, col)\n\t\t\t});\n\t\t},\n\t\ttoggle_cell_menu: (event, row, col) => {\n\t\t\tevent.stopPropagation();\n\t\t\tconst current_menu = get(state).ui_state.active_cell_menu;\n\t\t\tif (current_menu?.row === row && current_menu.col === col) {\n\t\t\t\tupdate_state((s) => ({\n\t\t\t\t\tui_state: { ...s.ui_state, active_cell_menu: null }\n\t\t\t\t}));\n\t\t\t} else {\n\t\t\t\tconst cell = (event.target as HTMLElement).closest(\"td\");\n\t\t\t\tif (cell) {\n\t\t\t\t\tconst rect = cell.getBoundingClientRect();\n\t\t\t\t\tupdate_state((s) => ({\n\t\t\t\t\t\tui_state: {\n\t\t\t\t\t\t\t...s.ui_state,\n\t\t\t\t\t\t\tactive_cell_menu: { row, col, x: rect.right, y: rect.bottom }\n\t\t\t\t\t\t}\n\t\t\t\t\t}));\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\ttoggle_cell_button: (row, col) => {\n\t\t\tconst current_button = get(state).ui_state.active_button;\n\t\t\tconst new_button =\n\t\t\t\tcurrent_button?.type === \"cell\" &&\n\t\t\t\tcurrent_button.row === row &&\n\t\t\t\tcurrent_button.col === col\n\t\t\t\t\t? null\n\t\t\t\t\t: { type: \"cell\" as const, row, col };\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: { ...s.ui_state, active_button: new_button }\n\t\t\t}));\n\t\t},\n\t\thandle_select_column: (col) => {\n\t\t\tif (!context.data) return;\n\t\t\tconst cells = context.data.map((_, row) => [row, col] as CellCoordinate);\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: {\n\t\t\t\t\t...s.ui_state,\n\t\t\t\t\tselected_cells: cells,\n\t\t\t\t\tselected: cells[0],\n\t\t\t\t\tediting: false\n\t\t\t\t}\n\t\t\t}));\n\t\t\tsetTimeout(() => context.parent_element?.focus(), 0);\n\t\t},\n\t\thandle_select_row: (row) => {\n\t\t\tif (!context.data || !context.data[0]) return;\n\t\t\tconst cells = context.data[0].map(\n\t\t\t\t(_, col) => [row, col] as CellCoordinate\n\t\t\t);\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: {\n\t\t\t\t\t...s.ui_state,\n\t\t\t\t\tselected_cells: cells,\n\t\t\t\t\tselected: cells[0],\n\t\t\t\t\tediting: false\n\t\t\t\t}\n\t\t\t}));\n\t\t\tsetTimeout(() => context.parent_element?.focus(), 0);\n\t\t},\n\t\tget_next_cell_coordinates,\n\t\tget_range_selection,\n\t\tmove_cursor\n\t};\n}\n\nexport function create_dataframe_context(\n\tconfig: DataFrameState[\"config\"]\n): DataFrameContext {\n\tconst state = writable<DataFrameState>({\n\t\tconfig,\n\t\tcurrent_search_query: null,\n\t\tsort_state: { sort_columns: [], row_order: [], initial_data: null },\n\t\tfilter_state: { filter_columns: [], initial_data: null },\n\t\tui_state: {\n\t\t\tactive_cell_menu: null,\n\t\t\tactive_header_menu: null,\n\t\t\tselected_cells: [],\n\t\t\tselected: false,\n\t\t\tediting: false,\n\t\t\theader_edit: false,\n\t\t\tselected_header: false,\n\t\t\tactive_button: null,\n\t\t\tcopy_flash: false\n\t\t}\n\t});\n\n\tconst context: DataFrameContext = { state, actions: null as any };\n\tcontext.actions = create_actions(state, context);\n\n\tconst instance_id = Symbol(\n\t\t`dataframe_${Math.random().toString(36).substring(2)}`\n\t);\n\tsetContext(instance_id, context);\n\tsetContext(DATAFRAME_KEY, { instance_id, context });\n\n\treturn context;\n}\n\nexport function get_dataframe_context(): DataFrameContext {\n\tconst ctx = getContext<{ instance_id: symbol; context: DataFrameContext }>(\n\t\tDATAFRAME_KEY\n\t);\n\treturn ctx?.context ?? getContext<DataFrameContext>(DATAFRAME_KEY);\n}\n", "<script lang=\"ts\">\n\texport let position: \"column\" | \"row\";\n\texport let coords: [number, number];\n\texport let on_click: (() => void) | null = null;\n\n\t$: is_first_position =\n\t\tposition === \"column\" ? coords[0] === 0 : coords[1] === 0;\n\t$: direction =\n\t\tposition === \"column\"\n\t\t\t? is_first_position\n\t\t\t\t? \"down\"\n\t\t\t\t: \"up\"\n\t\t\t: is_first_position\n\t\t\t\t? \"right\"\n\t\t\t\t: \"left\";\n</script>\n\n<button\n\tclass=\"selection-button selection-button-{position} {is_first_position\n\t\t? `move-${direction}`\n\t\t: ''}\"\n\ton:click|stopPropagation={() => on_click && on_click()}\n\taria-label={`Select ${position}`}\n>\n\t<span class={direction}>\n\t\t<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n\t\t\t<path\n\t\t\t\td=\"m16.707 13.293-4-4a1 1 0 0 0-1.414 0l-4 4A1 1 0 0 0 8 15h8a1 1 0 0 0 .707-1.707z\"\n\t\t\t\tdata-name={direction}\n\t\t\t/>\n\t\t</svg>\n\t</span>\n</button>\n\n<style>\n\t.selection-button {\n\t\tposition: absolute;\n\t\tbackground: var(--color-accent);\n\t\twidth: var(--size-3);\n\t\theight: var(--size-5);\n\t\tcolor: var(--background-fill-primary);\n\t}\n\n\t.selection-button-column {\n\t\ttop: -15px;\n\t\tleft: 50%;\n\t\ttransform: translateX(-50%) rotate(90deg);\n\t\tborder-radius: var(--radius-sm) 0 0 var(--radius-sm);\n\t}\n\n\t.selection-button-row {\n\t\tleft: calc(var(--size-2-5) * -1);\n\t\tborder-radius: var(--radius-sm) 0 0 var(--radius-sm);\n\t}\n\n\t.move-down {\n\t\tbottom: -14px;\n\t\ttop: auto;\n\t\tborder-radius: 0 var(--radius-sm) var(--radius-sm) 0;\n\t}\n\n\t.move-right {\n\t\tleft: auto;\n\t\tright: calc(var(--size-2-5) * -1);\n\t\tborder-radius: 0 var(--radius-sm) var(--radius-sm) 0;\n\t}\n\n\tsvg {\n\t\tfill: currentColor;\n\t}\n\n\tspan {\n\t\tdisplay: flex;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.up {\n\t\ttransform: rotate(-90deg);\n\t}\n\n\t.down {\n\t\ttransform: rotate(90deg);\n\t}\n\n\t.left {\n\t\ttransform: rotate(-90deg);\n\t}\n\n\t.right {\n\t\ttransform: rotate(90deg);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { BaseCheckbox } from \"@gradio/checkbox\";\n\n\texport let value: boolean | string = false;\n\texport let editable = true;\n\texport let on_change: (value: boolean) => void;\n\n\t$: bool_value =\n\t\ttypeof value === \"string\" ? value.toLowerCase() === \"true\" : !!value;\n\n\tfunction handle_change(event: CustomEvent<boolean>): void {\n\t\tif (editable) {\n\t\t\ton_change(event.detail);\n\t\t}\n\t}\n</script>\n\n<div class=\"bool-cell\" role=\"button\" tabindex=\"-1\">\n\t<BaseCheckbox\n\t\tbind:value={bool_value}\n\t\tlabel=\"\"\n\t\tinteractive={editable}\n\t\ton:change={handle_change}\n\t/>\n</div>\n\n<style>\n\t.bool-cell {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\twidth: min-content;\n\t\theight: var(--size-full);\n\t\tmargin: 0 auto;\n\t}\n\n\t.bool-cell :global(input:disabled) {\n\t\tcursor: not-allowed;\n\t}\n\n\t.bool-cell :global(label) {\n\t\tmargin: 0;\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\n\t.bool-cell :global(span) {\n\t\tdisplay: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { MarkdownCode } from \"@gradio/markdown-code\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport SelectionButtons from \"./icons/SelectionButtons.svelte\";\n\timport BooleanCell from \"./BooleanCell.svelte\";\n\n\texport let edit: boolean;\n\texport let value: string | number = \"\";\n\texport let display_value: string | null = null;\n\texport let styling = \"\";\n\texport let header = false;\n\texport let datatype:\n\t\t| \"str\"\n\t\t| \"markdown\"\n\t\t| \"html\"\n\t\t| \"number\"\n\t\t| \"bool\"\n\t\t| \"date\"\n\t\t| \"image\" = \"str\";\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let line_breaks = true;\n\texport let editable = true;\n\texport let is_static = false;\n\texport let max_chars: number | null = null;\n\texport let components: Record<string, any> = {};\n\texport let i18n: I18nFormatter;\n\texport let is_dragging = false;\n\texport let wrap_text = false;\n\n\texport let show_selection_buttons = false;\n\texport let coords: [number, number];\n\texport let on_select_column: ((col: number) => void) | null = null;\n\texport let on_select_row: ((row: number) => void) | null = null;\n\texport let el: HTMLTextAreaElement | null;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tblur: { blur_event: FocusEvent; coords: [number, number] };\n\t\tkeydown: KeyboardEvent;\n\t}>();\n\n\tfunction truncate_text(\n\t\ttext: string | number,\n\t\tmax_length: number | null = null,\n\t\tis_image = false\n\t): string {\n\t\tif (is_image) return String(text);\n\t\tconst str = String(text);\n\t\tif (!max_length || max_length <= 0) return str;\n\t\tif (str.length <= max_length) return str;\n\t\treturn str.slice(0, max_length) + \"...\";\n\t}\n\n\t$: should_truncate = !edit && max_chars !== null && max_chars > 0;\n\n\t$: display_content = editable\n\t\t? value\n\t\t: display_value !== null\n\t\t\t? display_value\n\t\t\t: value;\n\n\t$: display_text = should_truncate\n\t\t? truncate_text(display_content, max_chars, datatype === \"image\")\n\t\t: display_content;\n\n\tfunction use_focus(node: HTMLTextAreaElement): any {\n\t\trequestAnimationFrame(() => {\n\t\t\tnode.focus();\n\t\t});\n\n\t\treturn {};\n\t}\n\n\tfunction handle_blur(event: FocusEvent): void {\n\t\tdispatch(\"blur\", {\n\t\t\tblur_event: event,\n\t\t\tcoords: coords\n\t\t});\n\t}\n\n\tfunction handle_keydown(event: KeyboardEvent): void {\n\t\tdispatch(\"keydown\", event);\n\t}\n\n\tfunction handle_bool_change(new_value: boolean): void {\n\t\tvalue = new_value.toString();\n\t\tdispatch(\"blur\", {\n\t\t\tblur_event: {\n\t\t\t\ttarget: {\n\t\t\t\t\ttype: \"checkbox\",\n\t\t\t\t\tchecked: new_value,\n\t\t\t\t\tvalue: new_value.toString()\n\t\t\t\t}\n\t\t\t} as unknown as FocusEvent,\n\t\t\tcoords: coords\n\t\t});\n\t}\n</script>\n\n{#if edit && datatype !== \"bool\"}\n\t<textarea\n\t\treadonly={is_static}\n\t\taria-readonly={is_static}\n\t\taria-label={is_static ? \"Cell is read-only\" : \"Edit cell\"}\n\t\tbind:this={el}\n\t\tbind:value\n\t\tclass:header\n\t\ttabindex=\"-1\"\n\t\ton:blur={handle_blur}\n\t\ton:mousedown|stopPropagation\n\t\ton:click|stopPropagation\n\t\tuse:use_focus\n\t\ton:keydown={handle_keydown}\n\t/>\n{/if}\n\n{#if datatype === \"bool\"}\n\t<BooleanCell\n\t\tvalue={String(display_content)}\n\t\t{editable}\n\t\ton_change={handle_bool_change}\n\t/>\n{:else}\n\t<span\n\t\tclass:dragging={is_dragging}\n\t\ton:keydown={handle_keydown}\n\t\ttabindex=\"0\"\n\t\trole=\"button\"\n\t\tclass:edit\n\t\tclass:expanded={edit}\n\t\tclass:multiline={header}\n\t\ton:focus|preventDefault\n\t\tstyle={styling}\n\t\tdata-editable={editable}\n\t\tdata-max-chars={max_chars}\n\t\tdata-expanded={edit}\n\t\tplaceholder=\" \"\n\t\tclass:text={datatype === \"str\"}\n\t\tclass:wrap={wrap_text}\n\t>\n\t\t{#if datatype === \"image\" && components.image}\n\t\t\t<svelte:component\n\t\t\t\tthis={components.image}\n\t\t\t\tvalue={{ url: display_text }}\n\t\t\t\tshow_label={false}\n\t\t\t\tlabel=\"cell-image\"\n\t\t\t\tshow_download_button={false}\n\t\t\t\t{i18n}\n\t\t\t\tgradio={{ dispatch: () => {} }}\n\t\t\t/>\n\t\t{:else if datatype === \"html\"}\n\t\t\t{@html display_text}\n\t\t{:else if datatype === \"markdown\"}\n\t\t\t<MarkdownCode\n\t\t\t\tmessage={display_text.toLocaleString()}\n\t\t\t\t{latex_delimiters}\n\t\t\t\t{line_breaks}\n\t\t\t\tchatbot={false}\n\t\t\t/>\n\t\t{:else}\n\t\t\t{display_text}\n\t\t{/if}\n\t</span>\n{/if}\n\n{#if show_selection_buttons && coords && on_select_column && on_select_row}\n\t<SelectionButtons\n\t\tposition=\"column\"\n\t\t{coords}\n\t\ton_click={() => on_select_column(coords[1])}\n\t/>\n\t<SelectionButtons\n\t\tposition=\"row\"\n\t\t{coords}\n\t\ton_click={() => on_select_row(coords[0])}\n\t/>\n{/if}\n\n<style>\n\t.dragging {\n\t\tcursor: crosshair !important;\n\t}\n\n\ttextarea {\n\t\tposition: absolute;\n\t\tflex: 1 1 0%;\n\t\ttransform: translateX(-0.1px);\n\t\toutline: none;\n\t\tborder: none;\n\t\tbackground: transparent;\n\t\tcursor: text;\n\t\twidth: calc(100% - var(--size-2));\n\t\tresize: none;\n\t\theight: 100%;\n\t\tpadding-left: 0;\n\t\tfont-size: inherit;\n\t\tfont-weight: inherit;\n\t\tline-height: var(--line-lg);\n\t}\n\n\ttextarea:focus {\n\t\toutline: none;\n\t}\n\n\tspan {\n\t\tflex: 1 1 0%;\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\toutline: none;\n\t\t-webkit-user-select: text;\n\t\t-moz-user-select: text;\n\t\t-ms-user-select: text;\n\t\tuser-select: text;\n\t\tcursor: text;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\toverflow: hidden;\n\t}\n\n\tspan.text.expanded {\n\t\theight: auto;\n\t\tmin-height: 100%;\n\t\twhite-space: pre-wrap;\n\t\tword-break: break-word;\n\t\toverflow: visible;\n\t}\n\n\t.multiline {\n\t\twhite-space: pre;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t}\n\n\t.header {\n\t\ttransform: translateX(0);\n\t\tfont-weight: var(--weight-bold);\n\t\twhite-space: nowrap;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\tmargin-left: var(--size-1);\n\t}\n\n\t.edit {\n\t\topacity: 0;\n\t\tpointer-events: none;\n\t}\n\n\tspan :global(img) {\n\t\tmax-height: 100px;\n\t\twidth: auto;\n\t\tobject-fit: contain;\n\t}\n\n\ttextarea:read-only {\n\t\tcursor: not-allowed;\n\t}\n\n\t.wrap,\n\t.wrap.expanded {\n\t\twhite-space: normal;\n\t\tword-wrap: break-word;\n\t\toverflow-wrap: break-word;\n\t\tword-wrap: break-word;\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let index: number | null = null;\n\texport let is_header = false;\n</script>\n\n{#if is_header}\n\t<th tabindex=\"-1\" class=\"row-number\">\n\t\t<div class=\"cell-wrap\">\n\t\t\t<div class=\"header-content\">\n\t\t\t\t<div class=\"header-text\"></div>\n\t\t\t</div>\n\t\t</div>\n\t</th>\n{:else}\n\t<td class=\"row-number\" tabindex=\"-1\" data-row={index} data-col=\"row-number\">\n\t\t{index !== null ? index + 1 : \"\"}\n\t</td>\n{/if}\n\n<style>\n\t.row-number {\n\t\ttext-align: center;\n\t\tpadding: var(--size-1);\n\t\tmin-width: var(--size-12);\n\t\twidth: var(--size-12);\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t\tfont-weight: var(--weight-semibold);\n\t\tborder-right: 1px solid var(--border-color-primary);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let on_click: (event: MouseEvent) => void;\n</script>\n\n<button\n\taria-label=\"Open cell menu\"\n\tclass=\"cell-menu-button\"\n\taria-haspopup=\"menu\"\n\ton:click={on_click}\n\ton:touchstart={(event) => {\n\t\tevent.preventDefault();\n\t\tconst touch = event.touches[0];\n\t\tconst mouseEvent = new MouseEvent(\"click\", {\n\t\t\tclientX: touch.clientX,\n\t\t\tclientY: touch.clientY,\n\t\t\tbubbles: true,\n\t\t\tcancelable: true,\n\t\t\tview: window\n\t\t});\n\t\ton_click(mouseEvent);\n\t}}\n>\n\t&#8942;\n</button>\n\n<style>\n\t.cell-menu-button {\n\t\tflex-shrink: 0;\n\t\tdisplay: none;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--block-radius);\n\t\twidth: var(--size-5);\n\t\theight: var(--size-5);\n\t\tmin-width: var(--size-5);\n\t\tpadding: 0;\n\t\tmargin-right: var(--spacing-sm);\n\t\tz-index: 2;\n\t\tposition: absolute;\n\t\tright: var(--size-1);\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t}\n</style>\n", "<div class=\"wrapper\" aria-label=\"Static column\">\n\t<svg\n\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\twidth=\"13\"\n\t\theight=\"13\"\n\t\tviewBox=\"0 0 24 24\"\n\t\tfill=\"none\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"2\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t>\n\t\t<rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\"></rect>\n\t\t<path d=\"M7 11V7a5 5 0 0 1 10 0v4\"></path>\n\t</svg>\n</div>\n\n<style>\n\t.wrapper {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let size = 16;\n</script>\n\n<svg\n\twidth={size}\n\theight={size}\n\tviewBox=\"0 0 16 16\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M4 8L8 4L12 8\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t/>\n\t<path\n\t\td=\"M8 4V12\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\texport let size = 16;\n</script>\n\n<svg\n\twidth={size}\n\theight={size}\n\tviewBox=\"0 0 16 16\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M4 8L8 12L12 8\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t/>\n\t<path\n\t\td=\"M8 12V4\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\texport let icon: string;\n</script>\n\n{#if icon == \"add-column-right\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<rect\n\t\t\tx=\"4\"\n\t\t\ty=\"6\"\n\t\t\twidth=\"4\"\n\t\t\theight=\"12\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M12 12H19M16 8L19 12L16 16\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"add-column-left\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<rect\n\t\t\tx=\"16\"\n\t\t\ty=\"6\"\n\t\t\twidth=\"4\"\n\t\t\theight=\"12\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M12 12H5M8 8L5 12L8 16\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"add-row-above\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<rect\n\t\t\tx=\"6\"\n\t\t\ty=\"16\"\n\t\t\twidth=\"12\"\n\t\t\theight=\"4\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M12 12V5M8 8L12 5L16 8\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"add-row-below\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<rect\n\t\t\tx=\"6\"\n\t\t\ty=\"4\"\n\t\t\twidth=\"12\"\n\t\t\theight=\"4\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M12 12V19M8 16L12 19L16 16\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"delete-row\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<rect\n\t\t\tx=\"5\"\n\t\t\ty=\"10\"\n\t\t\twidth=\"14\"\n\t\t\theight=\"4\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M8 7L16 17M16 7L8 17\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"delete-column\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<rect\n\t\t\tx=\"10\"\n\t\t\ty=\"5\"\n\t\t\twidth=\"4\"\n\t\t\theight=\"14\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M7 8L17 16M17 8L7 16\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"sort-asc\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<path\n\t\t\td=\"M8 16L12 12L16 16\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t\tstroke-linecap=\"round\"\n\t\t\tstroke-linejoin=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M12 12V19\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M5 7H19\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"sort-desc\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<path\n\t\t\td=\"M8 12L12 16L16 12\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t\tstroke-linecap=\"round\"\n\t\t\tstroke-linejoin=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M12 16V9\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M5 5H19\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"clear-sort\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<path\n\t\t\td=\"M5 5H19\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M5 9H15\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M5 13H11\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M5 17H7\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M17 17L21 21M21 17L17 21\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"filter\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<path\n\t\t\td=\"M5 5H19\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M8 9H16\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M11 13H13\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"clear-filter\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<path\n\t\t\td=\"M5 5H19\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M8 9H16\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M11 13H13\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M17 17L21 21M21 17L17 21\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{/if}\n", "<script lang=\"ts\">\n\timport EditableCell from \"./EditableCell.svelte\";\n\timport CellMenuButton from \"./CellMenuButton.svelte\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport { get_sort_status } from \"./utils/sort_utils\";\n\timport Padlock from \"./icons/Padlock.svelte\";\n\timport SortArrowUp from \"./icons/SortArrowUp.svelte\";\n\timport SortArrowDown from \"./icons/SortArrowDown.svelte\";\n\timport type { SortDirection } from \"./context/dataframe_context\";\n\timport CellMenuIcons from \"./CellMenuIcons.svelte\";\n\timport type { FilterDatatype } from \"./context/dataframe_context\";\n\texport let value: string;\n\texport let i: number;\n\texport let actual_pinned_columns: number;\n\texport let header_edit: number | false;\n\texport let selected_header: number | false;\n\texport let headers: string[];\n\texport let get_cell_width: (index: number) => string;\n\texport let handle_header_click: (event: MouseEvent, col: number) => void;\n\texport let toggle_header_menu: (event: MouseEvent, col: number) => void;\n\texport let end_header_edit: (event: CustomEvent<KeyboardEvent>) => void;\n\texport let sort_columns: { col: number; direction: SortDirection }[] = [];\n\texport let filter_columns: {\n\t\tcol: number;\n\t\tdatatype: FilterDatatype;\n\t\tfilter: string;\n\t\tvalue: string;\n\t}[] = [];\n\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let line_breaks: boolean;\n\texport let max_chars: number | undefined;\n\texport let editable: boolean;\n\texport let i18n: I18nFormatter;\n\texport let el: HTMLTextAreaElement | null;\n\texport let is_static: boolean;\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\n\t$: can_add_columns = col_count && col_count[1] === \"dynamic\";\n\t$: sort_index = sort_columns.findIndex((item) => item.col === i);\n\t$: filter_index = filter_columns.findIndex((item) => item.col === i);\n\t$: sort_priority = sort_index !== -1 ? sort_index + 1 : null;\n\t$: current_direction =\n\t\tsort_index !== -1 ? sort_columns[sort_index].direction : null;\n\n\tfunction get_header_position(col_index: number): string {\n\t\tif (col_index >= actual_pinned_columns) {\n\t\t\treturn \"auto\";\n\t\t}\n\n\t\tif (col_index === 0) {\n\t\t\treturn \"0\";\n\t\t}\n\n\t\tconst previous_widths = Array(col_index)\n\t\t\t.fill(0)\n\t\t\t.map((_, idx) => {\n\t\t\t\treturn get_cell_width(idx);\n\t\t\t})\n\t\t\t.join(\" + \");\n\n\t\treturn `calc(${previous_widths})`;\n\t}\n</script>\n\n<th\n\tclass:pinned-column={i < actual_pinned_columns}\n\tclass:last-pinned={i === actual_pinned_columns - 1}\n\tclass:focus={header_edit === i || selected_header === i}\n\tclass:sorted={sort_index !== -1}\n\tclass:filtered={filter_index !== -1}\n\taria-sort={get_sort_status(value, sort_columns, headers) === \"none\"\n\t\t? \"none\"\n\t\t: get_sort_status(value, sort_columns, headers) === \"asc\"\n\t\t\t? \"ascending\"\n\t\t\t: \"descending\"}\n\tstyle=\"width: {get_cell_width(i)}; left: {get_header_position(i)};\"\n\ton:click={(event) => handle_header_click(event, i)}\n\ton:mousedown={(event) => {\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\t}}\n\ttitle={value}\n>\n\t<div class=\"cell-wrap\">\n\t\t<div class=\"header-content\">\n\t\t\t<button\n\t\t\t\tclass=\"header-button\"\n\t\t\t\ton:click={(event) => handle_header_click(event, i)}\n\t\t\t\ton:mousedown={(event) => {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tevent.stopPropagation();\n\t\t\t\t}}\n\t\t\t\ttitle={value}\n\t\t\t>\n\t\t\t\t<EditableCell\n\t\t\t\t\t{max_chars}\n\t\t\t\t\tbind:value\n\t\t\t\t\tbind:el\n\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t{line_breaks}\n\t\t\t\t\tedit={header_edit === i}\n\t\t\t\t\ton:keydown={(event) => {\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\tevent.detail.key === \"Enter\" ||\n\t\t\t\t\t\t\tevent.detail.key === \"Escape\" ||\n\t\t\t\t\t\t\tevent.detail.key === \"Tab\"\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tend_header_edit(event);\n\t\t\t\t\t\t}\n\t\t\t\t\t}}\n\t\t\t\t\theader\n\t\t\t\t\t{editable}\n\t\t\t\t\t{is_static}\n\t\t\t\t\t{i18n}\n\t\t\t\t\tcoords={[i, 0]}\n\t\t\t\t/>\n\t\t\t\t{#if sort_index !== -1}\n\t\t\t\t\t<div class=\"sort-indicators\">\n\t\t\t\t\t\t<span class=\"sort-arrow\">\n\t\t\t\t\t\t\t{#if current_direction === \"asc\"}\n\t\t\t\t\t\t\t\t<SortArrowUp size={12} />\n\t\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t\t<SortArrowDown size={12} />\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t{#if sort_columns.length > 1}\n\t\t\t\t\t\t\t<span class=\"sort-priority\">\n\t\t\t\t\t\t\t\t{sort_priority}\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t\t{#if filter_index !== -1}\n\t\t\t\t\t<div class=\"filter-indicators\">\n\t\t\t\t\t\t<span class=\"filter-icon\">\n\t\t\t\t\t\t\t<CellMenuIcons icon=\"filter\" />\n\t\t\t\t\t\t</span>\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t</button>\n\t\t\t{#if is_static}\n\t\t\t\t<Padlock />\n\t\t\t{/if}\n\t\t</div>\n\t\t{#if can_add_columns}\n\t\t\t<CellMenuButton on_click={(event) => toggle_header_menu(event, i)} />\n\t\t{/if}\n\t</div>\n</th>\n\n<style>\n\tth {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\toutline: none;\n\t\tbox-shadow: inset 0 0 0 1px var(--ring-color);\n\t\tpadding: 0;\n\t\tbackground: var(--table-even-background-fill);\n\t\tborder-right-width: 0px;\n\t\tborder-left-width: 1px;\n\t\tborder-style: solid;\n\t\tborder-color: var(--border-color-primary);\n\t}\n\n\tth:first-child {\n\t\tborder-top-left-radius: var(--table-radius);\n\t\tborder-bottom-left-radius: var(--table-radius);\n\t\tborder-left-width: 0px;\n\t}\n\n\tth:last-child {\n\t\tborder-top-right-radius: var(--table-radius);\n\t\tborder-bottom-right-radius: var(--table-radius);\n\t}\n\n\tth.focus {\n\t\t--ring-color: var(--color-accent);\n\t\tbox-shadow: inset 0 0 0 2px var(--ring-color);\n\t\tz-index: 4;\n\t}\n\n\tth.focus :global(.cell-menu-button) {\n\t\tdisplay: flex;\n\t}\n\n\tth:hover :global(.cell-menu-button) {\n\t\tdisplay: flex;\n\t}\n\n\t.cell-wrap {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: flex-start;\n\t\toutline: none;\n\t\tmin-height: var(--size-9);\n\t\tposition: relative;\n\t\theight: 100%;\n\t\tpadding: var(--size-2);\n\t\tbox-sizing: border-box;\n\t\tmargin: 0;\n\t\tgap: var(--size-1);\n\t\toverflow: visible;\n\t\tmin-width: 0;\n\t\tborder-radius: var(--table-radius);\n\t}\n\n\t.header-content {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\toverflow: hidden;\n\t\tflex-grow: 1;\n\t\tmin-width: 0;\n\t\twhite-space: normal;\n\t\toverflow-wrap: break-word;\n\t\tword-break: normal;\n\t\theight: 100%;\n\t\tgap: var(--size-1);\n\t}\n\n\t.header-button {\n\t\tdisplay: flex;\n\t\ttext-align: left;\n\t\twidth: 100%;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tposition: relative;\n\t}\n\n\t.sort-indicators {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-left: var(--size-1);\n\t\tgap: var(--size-1);\n\t}\n\n\t.sort-arrow {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.sort-priority {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: var(--size-2);\n\t\tbackground-color: var(--button-secondary-background-fill);\n\t\tcolor: var(--body-text-color);\n\t\tborder-radius: var(--radius-sm);\n\t\twidth: var(--size-2-5);\n\t\theight: var(--size-2-5);\n\t\tpadding: var(--size-1-5);\n\t}\n\n\t.filter-indicators {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-left: var(--size-1);\n\t\tgap: var(--size-1);\n\t}\n\n\t.filter-icon {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.pinned-column {\n\t\tposition: sticky;\n\t\tz-index: 5;\n\t\tborder-right: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport EditableCell from \"./EditableCell.svelte\";\n\timport CellMenuButton from \"./CellMenuButton.svelte\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport type { Datatype } from \"./utils\";\n\timport { is_cell_in_selection } from \"./selection_utils\";\n\n\texport let value: string | number;\n\texport let index: number;\n\texport let j: number;\n\texport let actual_pinned_columns: number;\n\texport let get_cell_width: (index: number) => string;\n\texport let handle_cell_click: (\n\t\tevent: MouseEvent,\n\t\trow: number,\n\t\tcol: number\n\t) => void;\n\texport let handle_blur: (\n\t\tevent: CustomEvent<{\n\t\t\tblur_event: FocusEvent;\n\t\t\tcoords: [number, number];\n\t\t}>\n\t) => void;\n\texport let toggle_cell_menu: (\n\t\tevent: MouseEvent,\n\t\trow: number,\n\t\tcol: number\n\t) => void;\n\texport let is_cell_selected: (\n\t\tcoords: [number, number],\n\t\tselected_cells: [number, number][]\n\t) => string;\n\texport let should_show_cell_menu: (\n\t\tcoords: [number, number],\n\t\tselected_cells: [number, number][],\n\t\teditable: boolean\n\t) => boolean;\n\texport let selected_cells: [number, number][];\n\texport let copy_flash: boolean;\n\texport let active_cell_menu: {\n\t\trow: number;\n\t\tcol: number;\n\t\tx: number;\n\t\ty: number;\n\t} | null;\n\texport let styling: string | undefined;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let line_breaks: boolean;\n\texport let datatype: Datatype;\n\texport let editing: [number, number] | false;\n\texport let max_chars: number | undefined;\n\texport let editable: boolean;\n\texport let is_static = false;\n\texport let i18n: I18nFormatter;\n\texport let components: Record<string, any> = {};\n\texport let el: {\n\t\tcell: HTMLTableCellElement | null;\n\t\tinput: HTMLTextAreaElement | null;\n\t};\n\texport let handle_select_column: (col: number) => void;\n\texport let handle_select_row: (row: number) => void;\n\texport let is_dragging: boolean;\n\texport let display_value: string | undefined;\n\texport let wrap = false;\n\n\tfunction get_cell_position(col_index: number): string {\n\t\tif (col_index >= actual_pinned_columns) {\n\t\t\treturn \"auto\";\n\t\t}\n\n\t\tif (col_index === 0) {\n\t\t\treturn \"0\";\n\t\t}\n\n\t\tconst previous_widths = Array(col_index)\n\t\t\t.fill(0)\n\t\t\t.map((_, idx) => {\n\t\t\t\treturn get_cell_width(idx);\n\t\t\t})\n\t\t\t.join(\" + \");\n\n\t\treturn `calc(${previous_widths})`;\n\t}\n\n\t$: cell_classes = is_cell_selected([index, j], selected_cells || []);\n\t$: is_in_selection = is_cell_in_selection([index, j], selected_cells);\n\t$: has_no_top = cell_classes.includes(\"no-top\");\n\t$: has_no_bottom = cell_classes.includes(\"no-bottom\");\n\t$: has_no_left = cell_classes.includes(\"no-left\");\n\t$: has_no_right = cell_classes.includes(\"no-right\");\n</script>\n\n<td\n\tclass:pinned-column={j < actual_pinned_columns}\n\tclass:last-pinned={j === actual_pinned_columns - 1}\n\ttabindex={j < actual_pinned_columns ? -1 : 0}\n\tbind:this={el.cell}\n\tdata-row={index}\n\tdata-col={j}\n\tdata-testid={`cell-${index}-${j}`}\n\ton:mousedown={(e) => handle_cell_click(e, index, j)}\n\ton:contextmenu|preventDefault={(e) => toggle_cell_menu(e, index, j)}\n\tstyle=\"width: {get_cell_width(j)}; left: {get_cell_position(j)}; {styling ||\n\t\t''}\"\n\tclass:flash={copy_flash && is_in_selection}\n\tclass:cell-selected={is_in_selection}\n\tclass:no-top={has_no_top}\n\tclass:no-bottom={has_no_bottom}\n\tclass:no-left={has_no_left}\n\tclass:no-right={has_no_right}\n\tclass:menu-active={active_cell_menu &&\n\t\tactive_cell_menu.row === index &&\n\t\tactive_cell_menu.col === j}\n\tclass:dragging={is_dragging}\n>\n\t<div class=\"cell-wrap\">\n\t\t<EditableCell\n\t\t\tbind:value\n\t\t\tbind:el={el.input}\n\t\t\tdisplay_value={display_value !== undefined\n\t\t\t\t? display_value\n\t\t\t\t: String(value)}\n\t\t\t{latex_delimiters}\n\t\t\t{line_breaks}\n\t\t\t{editable}\n\t\t\t{is_static}\n\t\t\tedit={editing && editing[0] === index && editing[1] === j}\n\t\t\t{datatype}\n\t\t\ton:focus={() => {\n\t\t\t\tconst row = index;\n\t\t\t\tconst col = j;\n\t\t\t\tif (!selected_cells.some(([r, c]) => r === row && c === col)) {\n\t\t\t\t\tselected_cells = [[row, col]];\n\t\t\t\t}\n\t\t\t}}\n\t\t\ton:blur={handle_blur}\n\t\t\t{max_chars}\n\t\t\t{i18n}\n\t\t\t{components}\n\t\t\tshow_selection_buttons={selected_cells.length === 1 &&\n\t\t\t\tselected_cells[0][0] === index &&\n\t\t\t\tselected_cells[0][1] === j}\n\t\t\tcoords={[index, j]}\n\t\t\ton_select_column={handle_select_column}\n\t\t\ton_select_row={handle_select_row}\n\t\t\t{is_dragging}\n\t\t\twrap_text={wrap}\n\t\t/>\n\t\t{#if editable && should_show_cell_menu([index, j], selected_cells, editable)}\n\t\t\t<CellMenuButton on_click={(event) => toggle_cell_menu(event, index, j)} />\n\t\t{/if}\n\t</div>\n</td>\n\n<style>\n\ttd {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\toutline: none;\n\t\tbox-shadow: inset 0 0 0 1px var(--ring-color);\n\t\tpadding: 0;\n\t\tborder-right-width: 0px;\n\t\tborder-left-width: 1px;\n\t\tborder-style: solid;\n\t\tborder-color: var(--border-color-primary);\n\t}\n\n\t.cell-wrap {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: flex-start;\n\t\toutline: none;\n\t\tmin-height: var(--size-9);\n\t\tposition: relative;\n\t\theight: 100%;\n\t\tpadding: var(--size-2);\n\t\tbox-sizing: border-box;\n\t\tmargin: 0;\n\t\tgap: var(--size-1);\n\t\toverflow: visible;\n\t\tmin-width: 0;\n\t\tborder-radius: var(--table-radius);\n\t}\n\n\t.cell-selected {\n\t\t--ring-color: var(--color-accent);\n\t\tbox-shadow: inset 0 0 0 2px var(--ring-color);\n\t\tz-index: 2;\n\t\tposition: relative;\n\t}\n\n\t.cell-selected :global(.cell-menu-button) {\n\t\tdisplay: flex;\n\t}\n\n\t.flash.cell-selected {\n\t\tanimation: flash-color 700ms ease-out;\n\t}\n\n\t@keyframes flash-color {\n\t\t0%,\n\t\t30% {\n\t\t\tbackground: var(--color-accent-copied);\n\t\t}\n\n\t\t100% {\n\t\t\tbackground: transparent;\n\t\t}\n\t}\n\n\t.pinned-column {\n\t\tposition: sticky;\n\t\tz-index: 3;\n\t\tborder-right: none;\n\t}\n\n\t.pinned-column:nth-child(odd) {\n\t\tbackground: var(--table-odd-background-fill);\n\t}\n\n\t.pinned-column:nth-child(even) {\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\ttd:first-child {\n\t\tborder-left-width: 0px;\n\t}\n\n\t:global(tr:last-child) td:first-child {\n\t\tborder-bottom-left-radius: var(--table-radius);\n\t}\n\n\t:global(tr:last-child) td:last-child {\n\t\tborder-bottom-right-radius: var(--table-radius);\n\t}\n\n\t.dragging {\n\t\tcursor: crosshair;\n\t}\n\n\t/* Add back the cell selection border styles */\n\t.cell-selected.no-top {\n\t\tbox-shadow:\n\t\t\tinset 2px 0 0 var(--ring-color),\n\t\t\tinset -2px 0 0 var(--ring-color),\n\t\t\tinset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-bottom {\n\t\tbox-shadow:\n\t\t\tinset 2px 0 0 var(--ring-color),\n\t\t\tinset -2px 0 0 var(--ring-color),\n\t\t\tinset 0 2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-left {\n\t\tbox-shadow:\n\t\t\tinset 0 2px 0 var(--ring-color),\n\t\t\tinset -2px 0 0 var(--ring-color),\n\t\t\tinset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-right {\n\t\tbox-shadow:\n\t\t\tinset 0 2px 0 var(--ring-color),\n\t\t\tinset 2px 0 0 var(--ring-color),\n\t\t\tinset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-top.no-left {\n\t\tbox-shadow:\n\t\t\tinset -2px 0 0 var(--ring-color),\n\t\t\tinset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-top.no-right {\n\t\tbox-shadow:\n\t\t\tinset 2px 0 0 var(--ring-color),\n\t\t\tinset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-bottom.no-left {\n\t\tbox-shadow:\n\t\t\tinset -2px 0 0 var(--ring-color),\n\t\t\tinset 0 2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-bottom.no-right {\n\t\tbox-shadow:\n\t\t\tinset 2px 0 0 var(--ring-color),\n\t\t\tinset 0 2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-top.no-bottom {\n\t\tbox-shadow:\n\t\t\tinset 2px 0 0 var(--ring-color),\n\t\t\tinset -2px 0 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-left.no-right {\n\t\tbox-shadow:\n\t\t\tinset 0 2px 0 var(--ring-color),\n\t\t\tinset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-top.no-left.no-right {\n\t\tbox-shadow: inset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-bottom.no-left.no-right {\n\t\tbox-shadow: inset 0 2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-left.no-top.no-bottom {\n\t\tbox-shadow: inset -2px 0 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-right.no-top.no-bottom {\n\t\tbox-shadow: inset 2px 0 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-top.no-bottom.no-left.no-right {\n\t\tbox-shadow: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let on_click: () => void;\n</script>\n\n<button class=\"add-row-button\" on:click={on_click} aria-label=\"Add row\">\n\t+\n</button>\n\n<style>\n\t.add-row-button {\n\t\twidth: 100%;\n\t\tpadding: var(--size-1);\n\t\tbackground: transparent;\n\t\tborder: 1px dashed var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tcolor: var(--body-text-color);\n\t\tcursor: pointer;\n\t\ttransition: all 150ms;\n\t\tmargin-top: var(--size-2);\n\t\tz-index: 10;\n\t\tposition: relative;\n\t\tpointer-events: auto;\n\t}\n\n\t.add-row-button:hover {\n\t\tbackground: var(--background-fill-secondary);\n\t\tborder-style: solid;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onMount, tick, createEventDispatcher } from \"svelte\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let items: any[][] = [];\n\n\texport let max_height: number;\n\texport let actual_height: number;\n\texport let table_scrollbar_width: number;\n\texport let start = 0;\n\texport let end = 20;\n\texport let selected: number | false;\n\texport let disable_scroll = false;\n\texport let show_scroll_button = false;\n\texport let viewport: HTMLTableElement;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tscroll_top: number;\n\t}>();\n\n\tlet height = \"100%\";\n\n\tlet average_height = 30;\n\tlet bottom = 0;\n\tlet contents: HTMLTableSectionElement;\n\tlet head_height = 0;\n\tlet foot_height = 0;\n\tlet height_map: number[] = [];\n\tlet mounted: boolean;\n\tlet rows: HTMLCollectionOf<HTMLTableRowElement>;\n\tlet top = 0;\n\tlet viewport_height = 200;\n\tlet visible: { index: number; data: any[] }[] = [];\n\tlet viewport_box: DOMRectReadOnly;\n\n\t$: viewport_height = viewport_box?.height || 200;\n\n\tconst is_browser = typeof window !== \"undefined\";\n\tconst raf = is_browser\n\t\t? window.requestAnimationFrame\n\t\t: (cb: (...args: any[]) => void) => cb();\n\n\t$: {\n\t\tif (mounted && viewport_height && viewport.offsetParent) {\n\t\t\tsortedItems, raf(refresh_height_map);\n\t\t}\n\t}\n\n\tasync function refresh_height_map(): Promise<void> {\n\t\tif (sortedItems.length < start) {\n\t\t\tawait scroll_to_index(sortedItems.length - 1, { behavior: \"auto\" });\n\t\t}\n\n\t\tconst scrollTop = Math.max(0, viewport.scrollTop);\n\t\tshow_scroll_button = scrollTop > 100;\n\t\ttable_scrollbar_width = viewport.offsetWidth - viewport.clientWidth;\n\n\t\t// acquire height map for currently visible rows\n\t\tfor (let v = 0; v < rows.length; v += 1) {\n\t\t\theight_map[start + v] = rows[v].getBoundingClientRect().height;\n\t\t}\n\t\tlet i = 0;\n\t\tlet y = head_height;\n\t\t// loop items to find new start\n\t\twhile (i < sortedItems.length) {\n\t\t\tconst row_height = height_map[i] || average_height;\n\t\t\t// keep a page of rows buffered above\n\t\t\tif (y + row_height > scrollTop - max_height) {\n\t\t\t\tstart = i;\n\t\t\t\ttop = y - head_height;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\ty += row_height;\n\t\t\ti += 1;\n\t\t}\n\n\t\tlet content_height = head_height;\n\t\twhile (i < sortedItems.length) {\n\t\t\tconst row_height = height_map[i] || average_height;\n\t\t\tcontent_height += row_height;\n\t\t\ti += 1;\n\t\t\t// keep a page of rows buffered below\n\t\t\tif (content_height - head_height > 3 * max_height) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tend = i;\n\t\tconst remaining = sortedItems.length - end;\n\n\t\tconst scrollbar_height = viewport.offsetHeight - viewport.clientHeight;\n\t\tif (scrollbar_height > 0) {\n\t\t\tcontent_height += scrollbar_height;\n\t\t}\n\n\t\tlet filtered_height_map = height_map.filter((v) => typeof v === \"number\");\n\t\taverage_height =\n\t\t\tfiltered_height_map.reduce((a, b) => a + b, 0) /\n\t\t\t\tfiltered_height_map.length || 30;\n\n\t\tbottom = remaining * average_height;\n\t\tif (!isFinite(bottom)) {\n\t\t\tbottom = 200000;\n\t\t}\n\t\theight_map.length = sortedItems.length;\n\t\twhile (i < sortedItems.length) {\n\t\t\ti += 1;\n\t\t\theight_map[i] = average_height;\n\t\t}\n\t\tif (max_height && content_height > max_height) {\n\t\t\tactual_height = max_height;\n\t\t} else {\n\t\t\tactual_height = content_height;\n\t\t}\n\t}\n\n\t$: scroll_and_render(selected);\n\n\tasync function scroll_and_render(n: number | false): Promise<void> {\n\t\traf(async () => {\n\t\t\tif (typeof n !== \"number\") return;\n\t\t\tconst direction = typeof n !== \"number\" ? false : is_in_view(n);\n\t\t\tif (direction === true) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (direction === \"back\") {\n\t\t\t\tawait scroll_to_index(n, { behavior: \"instant\" });\n\t\t\t}\n\n\t\t\tif (direction === \"forwards\") {\n\t\t\t\tawait scroll_to_index(n, { behavior: \"instant\" }, true);\n\t\t\t}\n\t\t});\n\t}\n\n\tfunction is_in_view(n: number): \"back\" | \"forwards\" | true {\n\t\tconst current = rows && rows[n - start];\n\t\tif (!current && n < start) {\n\t\t\treturn \"back\";\n\t\t}\n\t\tif (!current && n >= end - 1) {\n\t\t\treturn \"forwards\";\n\t\t}\n\n\t\tconst { top: viewport_top } = viewport.getBoundingClientRect();\n\t\tconst { top, bottom } = current.getBoundingClientRect();\n\n\t\tif (top - viewport_top < 37) {\n\t\t\treturn \"back\";\n\t\t}\n\n\t\tif (bottom - viewport_top > viewport_height) {\n\t\t\treturn \"forwards\";\n\t\t}\n\n\t\treturn true;\n\t}\n\n\texport async function scroll_to_index(\n\t\tindex: number,\n\t\topts: ScrollToOptions,\n\t\talign_end = false\n\t): Promise<void> {\n\t\tawait tick();\n\n\t\tconst _itemHeight = average_height;\n\n\t\tlet distance = index * _itemHeight;\n\t\tif (align_end) {\n\t\t\tdistance = distance - viewport_height + _itemHeight + head_height;\n\t\t}\n\n\t\tconst scrollbar_height = viewport.offsetHeight - viewport.clientHeight;\n\t\tif (scrollbar_height > 0) {\n\t\t\tdistance += scrollbar_height;\n\t\t}\n\n\t\tconst _opts = {\n\t\t\ttop: distance,\n\t\t\tbehavior: \"smooth\" as ScrollBehavior,\n\t\t\t...opts\n\t\t};\n\n\t\tviewport.scrollTo(_opts);\n\t}\n\n\t$: sortedItems = items;\n\n\t$: visible = is_browser\n\t\t? sortedItems.slice(start, end).map((data, i) => {\n\t\t\t\treturn { index: i + start, data };\n\t\t\t})\n\t\t: sortedItems\n\t\t\t\t.slice(0, (max_height / sortedItems.length) * average_height + 1)\n\t\t\t\t.map((data, i) => {\n\t\t\t\t\treturn { index: i + start, data };\n\t\t\t\t});\n\n\tonMount(() => {\n\t\trows = contents.children as HTMLCollectionOf<HTMLTableRowElement>;\n\t\tmounted = true;\n\t});\n</script>\n\n<svelte-virtual-table-viewport>\n\t<div>\n\t\t<table\n\t\t\tclass=\"table\"\n\t\t\tclass:disable-scroll={disable_scroll}\n\t\t\tbind:this={viewport}\n\t\t\tbind:contentRect={viewport_box}\n\t\t\ton:scroll={refresh_height_map}\n\t\t\tstyle=\"height: {height}; --bw-svt-p-top: {top}px; --bw-svt-p-bottom: {bottom}px; --bw-svt-head-height: {head_height}px; --bw-svt-foot-height: {foot_height}px; --bw-svt-avg-row-height: {average_height}px; --max-height: {max_height}px\"\n\t\t>\n\t\t\t<thead class=\"thead\" bind:offsetHeight={head_height}>\n\t\t\t\t<slot name=\"thead\" />\n\t\t\t</thead>\n\t\t\t<tbody bind:this={contents} class=\"tbody\">\n\t\t\t\t{#if visible.length && visible[0].data.length}\n\t\t\t\t\t{#each visible as item (item.data[0].id)}\n\t\t\t\t\t\t<slot name=\"tbody\" item={item.data} index={item.index}>\n\t\t\t\t\t\t\tMissing Table Row\n\t\t\t\t\t\t</slot>\n\t\t\t\t\t{/each}\n\t\t\t\t{/if}\n\t\t\t</tbody>\n\t\t\t<tfoot class=\"tfoot\" bind:offsetHeight={foot_height}>\n\t\t\t\t<slot name=\"tfoot\" />\n\t\t\t</tfoot>\n\t\t</table>\n\t</div>\n</svelte-virtual-table-viewport>\n\n<style type=\"text/css\">\n\ttable {\n\t\tposition: relative;\n\t\toverflow: auto;\n\t\t-webkit-overflow-scrolling: touch;\n\t\tmax-height: var(--max-height);\n\t\tbox-sizing: border-box;\n\t\tdisplay: block;\n\t\tpadding: 0;\n\t\tmargin: 0;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-md);\n\t\tfont-family: var(--font-mono);\n\t\tborder-spacing: 0;\n\t\twidth: 100%;\n\t\tscroll-snap-type: x proximity;\n\t\tborder-collapse: separate;\n\t\tscrollbar-width: thin;\n\t\tscrollbar-color: rgba(128, 128, 128, 0.5) transparent;\n\t}\n\n\ttable::-webkit-scrollbar {\n\t\twidth: 4px;\n\t\theight: 4px;\n\t}\n\n\ttable::-webkit-scrollbar-track {\n\t\tbackground: transparent;\n\t}\n\n\ttable::-webkit-scrollbar-thumb {\n\t\tbackground-color: rgba(128, 128, 128, 0.5);\n\t\tborder-radius: 4px;\n\t}\n\n\ttable:hover {\n\t\tscrollbar-color: rgba(160, 160, 160, 0.7) transparent;\n\t}\n\n\ttable:hover::-webkit-scrollbar-thumb {\n\t\tbackground-color: rgba(160, 160, 160, 0.7);\n\t\tborder-radius: 4px;\n\t\twidth: 4px;\n\t}\n\n\t@media (hover: none) {\n\t\ttable {\n\t\t\tscrollbar-color: rgba(160, 160, 160, 0.7) transparent;\n\t\t}\n\n\t\ttable::-webkit-scrollbar-thumb {\n\t\t\tbackground-color: rgba(160, 160, 160, 0.7);\n\t\t\tborder-radius: 4px;\n\t\t}\n\t}\n\n\t@media (pointer: coarse) {\n\t\ttable::-webkit-scrollbar {\n\t\t\twidth: 8px;\n\t\t\theight: 8px;\n\t\t}\n\t}\n\n\ttable :is(thead, tfoot, tbody) {\n\t\tdisplay: table;\n\t\ttable-layout: fixed;\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t}\n\n\ttbody {\n\t\toverflow-x: scroll;\n\t\toverflow-y: hidden;\n\t}\n\n\ttable tbody {\n\t\tpadding-top: var(--bw-svt-p-top);\n\t\tpadding-bottom: var(--bw-svt-p-bottom);\n\t}\n\ttbody {\n\t\tposition: relative;\n\t\tbox-sizing: border-box;\n\t\tborder: 0px solid currentColor;\n\t}\n\n\ttbody > :global(tr:last-child) {\n\t\tborder: none;\n\t}\n\n\ttable :global(td) {\n\t\tscroll-snap-align: start;\n\t}\n\n\ttbody :global(td.pinned-column) {\n\t\tposition: sticky;\n\t\tz-index: 3;\n\t}\n\n\ttbody :global(tr:nth-child(odd)) :global(td.pinned-column) {\n\t\tbackground: var(--table-odd-background-fill);\n\t}\n\n\ttbody :global(tr:nth-child(even)) :global(td.pinned-column) {\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\ttbody :global(td.last-pinned) {\n\t\tborder-right: 1px solid var(--border-color-primary);\n\t}\n\n\tthead {\n\t\tposition: sticky;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tbackground: var(--background-fill-primary);\n\t\tz-index: 7;\n\t}\n\n\tthead :global(th) {\n\t\tbackground: var(--table-even-background-fill) !important;\n\t}\n\n\tthead :global(th.pinned-column) {\n\t\tposition: sticky;\n\t\tz-index: 7;\n\t\tbackground: var(--table-even-background-fill) !important;\n\t}\n\n\tthead :global(th.last-pinned) {\n\t\tborder-right: 1px solid var(--border-color-primary);\n\t}\n\n\t.table.disable-scroll {\n\t\toverflow: hidden !important;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport { Check } from \"@gradio/icons\";\n\timport DropdownArrow from \"../../icons/src/DropdownArrow.svelte\";\n\timport type { FilterDatatype } from \"./context/dataframe_context\";\n\n\texport let on_filter: (\n\t\tdatatype: FilterDatatype,\n\t\tselected_filter: string,\n\t\tvalue: string\n\t) => void = () => {};\n\n\tlet menu_element: HTMLDivElement;\n\tlet datatype: \"string\" | \"number\" = \"string\";\n\tlet current_filter = \"Contains\";\n\tlet filter_dropdown_open = false;\n\tlet filter_input_value = \"\";\n\n\tconst filter_options = {\n\t\tstring: [\n\t\t\t\"Contains\",\n\t\t\t\"Does not contain\",\n\t\t\t\"Starts with\",\n\t\t\t\"Ends with\",\n\t\t\t\"Is\",\n\t\t\t\"Is not\",\n\t\t\t\"Is empty\",\n\t\t\t\"Is not empty\"\n\t\t],\n\t\tnumber: [\"=\", \"≠\", \">\", \"<\", \"≥\", \"≤\", \"Is empty\", \"Is not empty\"]\n\t};\n\n\tonMount(() => {\n\t\tposition_menu();\n\t});\n\n\tfunction position_menu(): void {\n\t\tif (!menu_element) return;\n\n\t\tconst viewport_width = window.innerWidth;\n\t\tconst viewport_height = window.innerHeight;\n\t\tconst menu_rect = menu_element.getBoundingClientRect();\n\n\t\tconst x = (viewport_width - menu_rect.width) / 2;\n\t\tconst y = (viewport_height - menu_rect.height) / 2;\n\n\t\tmenu_element.style.left = `${x}px`;\n\t\tmenu_element.style.top = `${y}px`;\n\t}\n\n\tfunction handle_filter_input(e: Event): void {\n\t\tconst target = e.target as HTMLInputElement;\n\t\tfilter_input_value = target.value;\n\t}\n</script>\n\n<div>\n\t<div class=\"background\"></div>\n\t<div bind:this={menu_element} class=\"filter-menu\">\n\t\t<div class=\"filter-datatype-container\">\n\t\t\t<span>Filter as</span>\n\t\t\t<button\n\t\t\t\ton:click|stopPropagation={() => {\n\t\t\t\t\tdatatype = datatype === \"string\" ? \"number\" : \"string\";\n\t\t\t\t\tcurrent_filter = filter_options[datatype][0];\n\t\t\t\t}}\n\t\t\t\taria-label={`Change filter type. Filtering ${datatype}s`}\n\t\t\t>\n\t\t\t\t{datatype}\n\t\t\t</button>\n\t\t</div>\n\n\t\t<div class=\"input-container\">\n\t\t\t<div class=\"filter-dropdown\">\n\t\t\t\t<button\n\t\t\t\t\ton:click|stopPropagation={() =>\n\t\t\t\t\t\t(filter_dropdown_open = !filter_dropdown_open)}\n\t\t\t\t\taria-label={`Change filter. Using '${current_filter}'`}\n\t\t\t\t>\n\t\t\t\t\t{current_filter}\n\t\t\t\t\t<DropdownArrow />\n\t\t\t\t</button>\n\n\t\t\t\t{#if filter_dropdown_open}\n\t\t\t\t\t<div class=\"dropdown-filter-options\">\n\t\t\t\t\t\t{#each filter_options[datatype] as opt}\n\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\ton:click|stopPropagation={() => {\n\t\t\t\t\t\t\t\t\tcurrent_filter = opt;\n\t\t\t\t\t\t\t\t\tfilter_dropdown_open = !filter_dropdown_open;\n\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\tclass=\"filter-option\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{opt}\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t{/each}\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t</div>\n\n\t\t\t<input\n\t\t\t\ttype=\"text\"\n\t\t\t\tvalue={filter_input_value}\n\t\t\t\ton:click|stopPropagation\n\t\t\t\ton:input={handle_filter_input}\n\t\t\t\tplaceholder=\"Type a value\"\n\t\t\t\tclass=\"filter-input\"\n\t\t\t/>\n\t\t</div>\n\n\t\t<button\n\t\t\tclass=\"check-button\"\n\t\t\ton:click={() => on_filter(datatype, current_filter, filter_input_value)}\n\t\t>\n\t\t\t<Check />\n\t\t</button>\n\t</div>\n</div>\n\n<style>\n\t.background {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100vw;\n\t\theight: 100vh;\n\t\tbackground-color: rgba(0, 0, 0, 0.4);\n\t\tz-index: 20;\n\t}\n\n\t.filter-menu {\n\t\tposition: fixed;\n\t\tbackground: var(--background-fill-primary);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tpadding: var(--size-2);\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--size-2);\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\twidth: 300px;\n\t\tz-index: 21;\n\t}\n\n\t.filter-datatype-container {\n\t\tdisplay: flex;\n\t\tgap: var(--size-2);\n\t\talign-items: center;\n\t}\n\n\t.filter-menu span {\n\t\tfont-size: var(--text-sm);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.filter-menu button {\n\t\theight: var(--size-6);\n\t\tbackground: none;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--text-sm);\n\t\tcursor: pointer;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tgap: var(--size-2);\n\t}\n\n\t.filter-menu button:hover {\n\t\tbackground-color: var(--background-fill-secondary);\n\t}\n\n\t.filter-input {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-6);\n\t\tpadding: var(--size-2);\n\t\tpadding-right: var(--size-8);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--table-radius);\n\t\tfont-size: var(--text-sm);\n\t\tcolor: var(--body-text-color);\n\t\tbackground: var(--background-fill-secondary);\n\t\ttransition: all 0.2s ease;\n\t}\n\n\t.filter-input:hover {\n\t\tborder-color: var(--border-color-secondary);\n\t\tbackground: var(--background-fill-primary);\n\t}\n\n\t.filter-input:focus {\n\t\toutline: none;\n\t\tborder-color: var(--color-accent);\n\t\tbackground: var(--background-fill-primary);\n\t\tbox-shadow: 0 0 0 1px var(--color-accent);\n\t}\n\n\t.dropdown-filter-options {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tbackground: var(--background-fill-primary);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tbox-shadow: var(--shadow-drop-md);\n\t\tposition: absolute;\n\t\tz-index: var(--layer-1);\n\t}\n\n\t.dropdown-filter-options .filter-option {\n\t\tborder: none;\n\t\tjustify-content: flex-start;\n\t}\n\n\t.input-container {\n\t\tdisplay: flex;\n\t\tgap: var(--size-2);\n\t\talign-items: center;\n\t}\n\n\t.input-container button {\n\t\twidth: 130px;\n\t}\n\n\t:global(svg.dropdown-arrow) {\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t\tmargin-left: auto;\n\t}\n\n\t.filter-menu .check-button {\n\t\tbackground: var(--color-accent);\n\t\tcolor: white;\n\t\tborder: none;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-6);\n\t\tborder-radius: var(--radius-sm);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: var(--size-1);\n\t}\n\n\t.check-button:hover {\n\t\tbackground: var(--color-accent-soft);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport CellMenuIcons from \"./CellMenuIcons.svelte\";\n\timport FilterMenu from \"./FilterMenu.svelte\";\n\timport type { I18nFormatter } from \"js/utils/src\";\n\timport type {\n\t\tSortDirection,\n\t\tFilterDatatype\n\t} from \"./context/dataframe_context\";\n\n\texport let x: number;\n\texport let y: number;\n\texport let on_add_row_above: () => void;\n\texport let on_add_row_below: () => void;\n\texport let on_add_column_left: () => void;\n\texport let on_add_column_right: () => void;\n\texport let row: number;\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\texport let row_count: [number, \"fixed\" | \"dynamic\"];\n\texport let on_delete_row: () => void;\n\texport let on_delete_col: () => void;\n\texport let can_delete_rows: boolean;\n\texport let can_delete_cols: boolean;\n\texport let on_sort: (direction: SortDirection) => void = () => {};\n\texport let on_clear_sort: () => void = () => {};\n\texport let sort_direction: SortDirection | null = null;\n\texport let sort_priority: number | null = null;\n\texport let on_filter: (\n\t\tdatatype: FilterDatatype,\n\t\tselected_filter: string,\n\t\tvalue: string\n\t) => void = () => {};\n\texport let on_clear_filter: () => void = () => {};\n\texport let filter_active: boolean | null = null;\n\texport let editable = true;\n\n\texport let i18n: I18nFormatter;\n\tlet menu_element: HTMLDivElement;\n\tlet active_filter_menu: { x: number; y: number } | null = null;\n\n\t$: is_header = row === -1;\n\t$: can_add_rows = editable && row_count[1] === \"dynamic\";\n\t$: can_add_columns = editable && col_count[1] === \"dynamic\";\n\n\tonMount(() => {\n\t\tposition_menu();\n\t});\n\n\tfunction position_menu(): void {\n\t\tif (!menu_element) return;\n\n\t\tconst viewport_width = window.innerWidth;\n\t\tconst viewport_height = window.innerHeight;\n\t\tconst menu_rect = menu_element.getBoundingClientRect();\n\n\t\tlet new_x = x - 30;\n\t\tlet new_y = y - 20;\n\n\t\tif (new_x + menu_rect.width > viewport_width) {\n\t\t\tnew_x = x - menu_rect.width + 10;\n\t\t}\n\n\t\tif (new_y + menu_rect.height > viewport_height) {\n\t\t\tnew_y = y - menu_rect.height + 10;\n\t\t}\n\n\t\tmenu_element.style.left = `${new_x}px`;\n\t\tmenu_element.style.top = `${new_y}px`;\n\t}\n\n\tfunction toggle_filter_menu(): void {\n\t\tif (filter_active) {\n\t\t\ton_filter(\"string\", \"\", \"\");\n\t\t\treturn;\n\t\t}\n\n\t\tconst menu_rect = menu_element.getBoundingClientRect();\n\t\tactive_filter_menu = {\n\t\t\tx: menu_rect.right,\n\t\t\ty: menu_rect.top + menu_rect.height / 2\n\t\t};\n\t}\n</script>\n\n<div bind:this={menu_element} class=\"cell-menu\" role=\"menu\">\n\t{#if is_header}\n\t\t<button\n\t\t\trole=\"menuitem\"\n\t\t\ton:click={() => on_sort(\"asc\")}\n\t\t\tclass:active={sort_direction === \"asc\"}\n\t\t>\n\t\t\t<CellMenuIcons icon=\"sort-asc\" />\n\t\t\t{i18n(\"dataframe.sort_ascending\")}\n\t\t\t{#if sort_direction === \"asc\" && sort_priority !== null}\n\t\t\t\t<span class=\"priority\">{sort_priority}</span>\n\t\t\t{/if}\n\t\t</button>\n\t\t<button\n\t\t\trole=\"menuitem\"\n\t\t\ton:click={() => on_sort(\"desc\")}\n\t\t\tclass:active={sort_direction === \"desc\"}\n\t\t>\n\t\t\t<CellMenuIcons icon=\"sort-desc\" />\n\t\t\t{i18n(\"dataframe.sort_descending\")}\n\t\t\t{#if sort_direction === \"desc\" && sort_priority !== null}\n\t\t\t\t<span class=\"priority\">{sort_priority}</span>\n\t\t\t{/if}\n\t\t</button>\n\t\t<button role=\"menuitem\" on:click={on_clear_sort}>\n\t\t\t<CellMenuIcons icon=\"clear-sort\" />\n\t\t\t{i18n(\"dataframe.clear_sort\")}\n\t\t</button>\n\t\t<button\n\t\t\trole=\"menuitem\"\n\t\t\ton:click|stopPropagation={toggle_filter_menu}\n\t\t\tclass:active={filter_active || active_filter_menu}\n\t\t>\n\t\t\t<CellMenuIcons icon=\"filter\" />\n\t\t\t{i18n(\"dataframe.filter\")}\n\t\t\t{#if filter_active}\n\t\t\t\t<span class=\"priority\">1</span>\n\t\t\t{/if}\n\t\t</button>\n\t\t<button role=\"menuitem\" on:click={on_clear_filter}>\n\t\t\t<CellMenuIcons icon=\"clear-filter\" />\n\t\t\t{i18n(\"dataframe.clear_filter\")}\n\t\t</button>\n\t{/if}\n\n\t{#if !is_header && can_add_rows}\n\t\t<button\n\t\t\trole=\"menuitem\"\n\t\t\ton:click={() => on_add_row_above()}\n\t\t\taria-label=\"Add row above\"\n\t\t>\n\t\t\t<CellMenuIcons icon=\"add-row-above\" />\n\t\t\t{i18n(\"dataframe.add_row_above\")}\n\t\t</button>\n\t\t<button\n\t\t\trole=\"menuitem\"\n\t\t\ton:click={() => on_add_row_below()}\n\t\t\taria-label=\"Add row below\"\n\t\t>\n\t\t\t<CellMenuIcons icon=\"add-row-below\" />\n\t\t\t{i18n(\"dataframe.add_row_below\")}\n\t\t</button>\n\t\t{#if can_delete_rows}\n\t\t\t<button\n\t\t\t\trole=\"menuitem\"\n\t\t\t\ton:click={on_delete_row}\n\t\t\t\tclass=\"delete\"\n\t\t\t\taria-label=\"Delete row\"\n\t\t\t>\n\t\t\t\t<CellMenuIcons icon=\"delete-row\" />\n\t\t\t\t{i18n(\"dataframe.delete_row\")}\n\t\t\t</button>\n\t\t{/if}\n\t{/if}\n\t{#if can_add_columns}\n\t\t<button\n\t\t\trole=\"menuitem\"\n\t\t\ton:click={() => on_add_column_left()}\n\t\t\taria-label=\"Add column to the left\"\n\t\t>\n\t\t\t<CellMenuIcons icon=\"add-column-left\" />\n\t\t\t{i18n(\"dataframe.add_column_left\")}\n\t\t</button>\n\t\t<button\n\t\t\trole=\"menuitem\"\n\t\t\ton:click={() => on_add_column_right()}\n\t\t\taria-label=\"Add column to the right\"\n\t\t>\n\t\t\t<CellMenuIcons icon=\"add-column-right\" />\n\t\t\t{i18n(\"dataframe.add_column_right\")}\n\t\t</button>\n\t\t{#if can_delete_cols}\n\t\t\t<button\n\t\t\t\trole=\"menuitem\"\n\t\t\t\ton:click={on_delete_col}\n\t\t\t\tclass=\"delete\"\n\t\t\t\taria-label=\"Delete column\"\n\t\t\t>\n\t\t\t\t<CellMenuIcons icon=\"delete-column\" />\n\t\t\t\t{i18n(\"dataframe.delete_column\")}\n\t\t\t</button>\n\t\t{/if}\n\t{/if}\n</div>\n\n{#if active_filter_menu}\n\t<FilterMenu {on_filter} />\n{/if}\n\n<style>\n\t.cell-menu {\n\t\tposition: fixed;\n\t\tz-index: 9;\n\t\tbackground: var(--background-fill-primary);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tpadding: var(--size-1);\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--size-1);\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tmin-width: 150px;\n\t\tz-index: var(--layer-1);\n\t}\n\n\t.cell-menu button {\n\t\tbackground: none;\n\t\tborder: none;\n\t\tcursor: pointer;\n\t\ttext-align: left;\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tborder-radius: var(--radius-sm);\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--text-sm);\n\t\ttransition:\n\t\t\tbackground-color 0.2s,\n\t\t\tcolor 0.2s;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: var(--size-2);\n\t\tposition: relative;\n\t}\n\n\t.cell-menu button.active {\n\t\tbackground-color: var(--background-fill-secondary);\n\t}\n\n\t.cell-menu button:hover {\n\t\tbackground-color: var(--background-fill-secondary);\n\t}\n\n\t.cell-menu button :global(svg) {\n\t\tfill: currentColor;\n\t\ttransition: fill 0.2s;\n\t}\n\n\t.priority {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-left: auto;\n\t\tfont-size: var(--size-2);\n\t\tbackground-color: var(--button-secondary-background-fill);\n\t\tcolor: var(--body-text-color);\n\t\tborder-radius: var(--radius-sm);\n\t\twidth: var(--size-2-5);\n\t\theight: var(--size-2-5);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { Copy, Check } from \"@gradio/icons\";\n\timport { FullscreenButton } from \"@gradio/atoms\";\n\timport { onDestroy } from \"svelte\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\texport let show_fullscreen_button = false;\n\texport let show_copy_button = false;\n\texport let show_search: \"none\" | \"search\" | \"filter\" = \"none\";\n\texport let fullscreen = false;\n\texport let on_copy: () => Promise<void>;\n\texport let on_commit_filter: () => void;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tsearch: string | null;\n\t}>();\n\n\tlet copied = false;\n\tlet timer: ReturnType<typeof setTimeout>;\n\texport let current_search_query: string | null = null;\n\tlet input_value = \"\";\n\n\tfunction handle_search_input(e: Event): void {\n\t\tconst target = e.target as HTMLTextAreaElement;\n\t\tinput_value = target.value;\n\t\tconst new_query = input_value || null;\n\t\tif (current_search_query !== new_query) {\n\t\t\tcurrent_search_query = new_query;\n\t\t\tdispatch(\"search\", current_search_query);\n\t\t}\n\t}\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 2000);\n\t}\n\n\tasync function handle_copy(): Promise<void> {\n\t\tawait on_copy();\n\t\tcopy_feedback();\n\t}\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n<div class=\"toolbar\" role=\"toolbar\" aria-label=\"Table actions\">\n\t<div class=\"toolbar-buttons\">\n\t\t{#if show_search !== \"none\"}\n\t\t\t<div class=\"search-container\">\n\t\t\t\t<input\n\t\t\t\t\ttype=\"text\"\n\t\t\t\t\tvalue={current_search_query || \"\"}\n\t\t\t\t\ton:input={handle_search_input}\n\t\t\t\t\tplaceholder={show_search === \"filter\" ? \"Filter...\" : \"Search...\"}\n\t\t\t\t\tclass=\"search-input\"\n\t\t\t\t\tclass:filter-mode={show_search === \"filter\"}\n\t\t\t\t\ttitle={`Enter text to ${show_search} the table`}\n\t\t\t\t/>\n\t\t\t\t{#if current_search_query && show_search === \"filter\"}\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass=\"toolbar-button check-button\"\n\t\t\t\t\t\ton:click={on_commit_filter}\n\t\t\t\t\t\taria-label=\"Apply filter and update dataframe values\"\n\t\t\t\t\t\ttitle=\"Apply filter and update dataframe values\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<Check />\n\t\t\t\t\t</button>\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t{/if}\n\t\t{#if show_copy_button}\n\t\t\t<button\n\t\t\t\tclass=\"toolbar-button\"\n\t\t\t\ton:click={handle_copy}\n\t\t\t\taria-label={copied ? \"Copied to clipboard\" : \"Copy table data\"}\n\t\t\t\ttitle={copied ? \"Copied to clipboard\" : \"Copy table data\"}\n\t\t\t>\n\t\t\t\t{#if copied}\n\t\t\t\t\t<Check />\n\t\t\t\t{:else}\n\t\t\t\t\t<Copy />\n\t\t\t\t{/if}\n\t\t\t</button>\n\t\t{/if}\n\t\t{#if show_fullscreen_button}\n\t\t\t<FullscreenButton {fullscreen} on:fullscreen />\n\t\t{/if}\n\t</div>\n</div>\n\n<style>\n\t.toolbar {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: var(--size-2);\n\t\tflex: 0 0 auto;\n\t}\n\n\t.toolbar-buttons {\n\t\tdisplay: flex;\n\t\tgap: var(--size-1);\n\t\tflex-wrap: nowrap;\n\t}\n\n\t.toolbar-button {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\twidth: var(--size-6);\n\t\theight: var(--size-6);\n\t\tpadding: var(--size-1);\n\t\tborder: none;\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground: transparent;\n\t\tcolor: var(--body-text-color-subdued);\n\t\tcursor: pointer;\n\t\ttransition: all 0.2s;\n\t}\n\n\t.toolbar-button:hover {\n\t\tbackground: var(--background-fill-secondary);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.toolbar-button :global(svg) {\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t}\n\n\t.search-container {\n\t\tposition: relative;\n\t}\n\n\t.search-input {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-6);\n\t\tpadding: var(--size-2);\n\t\tpadding-right: var(--size-8);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--table-radius);\n\t\tfont-size: var(--text-sm);\n\t\tcolor: var(--body-text-color);\n\t\tbackground: var(--background-fill-secondary);\n\t\ttransition: all 0.2s ease;\n\t}\n\n\t.search-input:hover {\n\t\tborder-color: var(--border-color-secondary);\n\t\tbackground: var(--background-fill-primary);\n\t}\n\n\t.search-input:focus {\n\t\toutline: none;\n\t\tborder-color: var(--color-accent);\n\t\tbackground: var(--background-fill-primary);\n\t\tbox-shadow: 0 0 0 1px var(--color-accent);\n\t}\n\n\t.check-button {\n\t\tposition: absolute;\n\t\tright: var(--size-1);\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\tbackground: var(--color-accent);\n\t\tcolor: white;\n\t\tborder: none;\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t\tborder-radius: var(--radius-sm);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: var(--size-1);\n\t}\n\n\t.check-button :global(svg) {\n\t\twidth: var(--size-3);\n\t\theight: var(--size-3);\n\t}\n\n\t.check-button:hover {\n\t\tbackground: var(--color-accent-soft);\n\t}\n</style>\n", "import type { Headers, HeadersWithIDs } from \"../utils\";\n\nexport function make_headers(\n\t_head: Headers,\n\tcol_count: [number, \"fixed\" | \"dynamic\"],\n\tels: Record<\n\t\tstring,\n\t\t{ cell: null | HTMLTableCellElement; input: null | HTMLTextAreaElement }\n\t>,\n\tmake_id: () => string\n): HeadersWithIDs {\n\tlet _h = _head || [];\n\tif (col_count[1] === \"fixed\" && _h.length < col_count[0]) {\n\t\tconst fill = Array(col_count[0] - _h.length)\n\t\t\t.fill(\"\")\n\t\t\t.map((_, i) => `${i + _h.length}`);\n\t\t_h = _h.concat(fill);\n\t}\n\n\tif (!_h || _h.length === 0) {\n\t\treturn Array(col_count[0])\n\t\t\t.fill(0)\n\t\t\t.map((_, i) => {\n\t\t\t\tconst _id = make_id();\n\t\t\t\tels[_id] = { cell: null, input: null };\n\t\t\t\treturn { id: _id, value: JSON.stringify(i + 1) };\n\t\t\t});\n\t}\n\n\treturn _h.map((h, i) => {\n\t\tconst _id = make_id();\n\t\tels[_id] = { cell: null, input: null };\n\t\treturn { id: _id, value: h ?? \"\" };\n\t});\n}\n\nexport function process_data(\n\tvalues: (string | number)[][],\n\tels: Record<\n\t\tstring,\n\t\t{ cell: null | HTMLTableCellElement; input: null | HTMLTextAreaElement }\n\t>,\n\tdata_binding: Record<string, any>,\n\tmake_id: () => string,\n\tdisplay_value: string[][] | null = null\n): { id: string; value: string | number; display_value?: string }[][] {\n\tif (!values || values.length === 0) {\n\t\treturn [];\n\t}\n\n\tconst result = values.map((row, i) => {\n\t\treturn row.map((value, j) => {\n\t\t\tconst _id = make_id();\n\t\t\tels[_id] = { cell: null, input: null };\n\t\t\tdata_binding[_id] = value;\n\n\t\t\tlet display = display_value?.[i]?.[j];\n\n\t\t\tif (display === undefined) {\n\t\t\t\tdisplay = String(value);\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\tid: _id,\n\t\t\t\tvalue,\n\t\t\t\tdisplay_value: display\n\t\t\t};\n\t\t});\n\t});\n\n\treturn result;\n}\n", "export type Headers = string[];\nexport type Data = (string | number)[][];\nexport type Datatype = \"str\" | \"markdown\" | \"html\" | \"number\" | \"bool\" | \"date\";\nexport type Metadata = {\n\t[key: string]: string[][] | null;\n} | null;\nexport type HeadersWithIDs = { value: string; id: string }[];\nexport type DataframeValue = {\n\tdata: Data;\n\theaders: Headers;\n\tmetadata: Metadata;\n};\n\n/**\n * Coerce a value to a given type.\n * @param v - The value to coerce.\n * @param t - The type to coerce to.\n * @returns The coerced value.\n */\nexport function cast_value_to_type(\n\tv: any,\n\tt: Datatype\n): string | number | boolean {\n\tif (t === \"number\") {\n\t\tconst n = Number(v);\n\t\treturn isNaN(n) ? v : n;\n\t}\n\tif (t === \"bool\") {\n\t\tif (typeof v === \"boolean\") return v;\n\t\tif (typeof v === \"number\") return v !== 0;\n\t\tconst s = String(v).toLowerCase();\n\t\tif (s === \"true\" || s === \"1\") return true;\n\t\tif (s === \"false\" || s === \"0\") return false;\n\t\treturn v;\n\t}\n\tif (t === \"date\") {\n\t\tconst d = new Date(v);\n\t\treturn isNaN(d.getTime()) ? v : d.toISOString();\n\t}\n\treturn v;\n}\n", "import { dequal } from \"dequal/lite\";\nimport { handle_delete_key } from \"../selection_utils\";\nimport type { DataFrameContext } from \"../context/dataframe_context\";\nimport { tick } from \"svelte\";\nimport { get } from \"svelte/store\";\nimport { copy_table_data } from \"./table_utils\";\n\nasync function save_cell_value(\n\tinput_value: string,\n\tctx: DataFrameContext,\n\trow: number,\n\tcol: number\n): Promise<void> {\n\tif (!ctx.data || !ctx.data[row] || !ctx.data[row][col]) return;\n\n\tconst old_value = ctx.data[row][col].value;\n\tctx.data[row][col].value = input_value;\n\n\tif (old_value !== input_value && ctx.dispatch) {\n\t\tctx.dispatch(\"change\", {\n\t\t\tdata: ctx.data.map((row) => row.map((cell) => cell.value)),\n\t\t\theaders: ctx.headers?.map((h) => h.value) || [],\n\t\t\tmetadata: null\n\t\t});\n\t}\n\n\tctx.actions.set_selected([row, col]);\n}\n\nexport async function handle_cell_blur(\n\tevent: FocusEvent,\n\tctx: DataFrameContext,\n\tcoords: [number, number]\n): Promise<void> {\n\tif (!ctx.data || !ctx.headers || !ctx.els) return;\n\n\tconst input_el = event.target as HTMLInputElement;\n\tif (!input_el || input_el.value === undefined) return;\n\n\tawait save_cell_value(\n\t\tinput_el.type === \"checkbox\" ? String(input_el.checked) : input_el.value,\n\t\tctx,\n\t\tcoords[0],\n\t\tcoords[1]\n\t);\n}\n\nfunction handle_header_navigation(\n\tevent: KeyboardEvent,\n\tctx: DataFrameContext\n): boolean {\n\tconst state = get(ctx.state);\n\tconst selected_header = state.ui_state.selected_header;\n\tconst header_edit = state.ui_state.header_edit;\n\tconst headers = ctx.headers || [];\n\n\tif (selected_header === false || header_edit !== false) return false;\n\n\tswitch (event.key) {\n\t\tcase \"ArrowDown\":\n\t\t\tctx.actions.set_selected_header(false);\n\t\t\tctx.actions.set_selected([0, selected_header as number]);\n\t\t\tctx.actions.set_selected_cells([[0, selected_header as number]]);\n\t\t\treturn true;\n\t\tcase \"ArrowLeft\":\n\t\t\tctx.actions.set_selected_header(\n\t\t\t\tselected_header > 0 ? selected_header - 1 : selected_header\n\t\t\t);\n\t\t\treturn true;\n\t\tcase \"ArrowRight\":\n\t\t\tctx.actions.set_selected_header(\n\t\t\t\tselected_header < headers.length - 1\n\t\t\t\t\t? selected_header + 1\n\t\t\t\t\t: selected_header\n\t\t\t);\n\t\t\treturn true;\n\t\tcase \"Escape\":\n\t\t\tevent.preventDefault();\n\t\t\tctx.actions.set_selected_header(false);\n\t\t\treturn true;\n\t\tcase \"Enter\":\n\t\t\tevent.preventDefault();\n\t\t\tif (state.config.editable) {\n\t\t\t\tctx.actions.set_header_edit(selected_header);\n\t\t\t}\n\t\t\treturn true;\n\t}\n\treturn false;\n}\n\n// eslint-disable-next-line complexity\nfunction handle_delete_operation(\n\tevent: KeyboardEvent,\n\tctx: DataFrameContext\n): boolean {\n\tif (!ctx.data || !ctx.headers || !ctx.els || !ctx.dispatch) return false;\n\n\tconst state = get(ctx.state);\n\tif (!state.config.editable) return false;\n\tif (event.key !== \"Delete\" && event.key !== \"Backspace\") return false;\n\n\tconst editing = state.ui_state.editing;\n\tconst selected_cells = state.ui_state.selected_cells;\n\n\tconst static_columns = state.config.static_columns || [];\n\tif (selected_cells.some(([_, col]) => static_columns.includes(col))) {\n\t\treturn false;\n\t}\n\n\tif (editing) {\n\t\tconst [row, col] = editing;\n\t\tconst input_el = ctx.els[ctx.data[row][col].id]?.input;\n\t\tif (input_el && input_el.selectionStart !== input_el.selectionEnd) {\n\t\t\treturn false;\n\t\t}\n\t\tif (\n\t\t\tevent.key === \"Delete\" &&\n\t\t\tinput_el?.selectionStart !== input_el?.value.length\n\t\t) {\n\t\t\treturn false;\n\t\t}\n\t\tif (event.key === \"Backspace\" && input_el?.selectionStart !== 0) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\tevent.preventDefault();\n\tif (selected_cells.length > 0) {\n\t\tconst new_data = handle_delete_key(ctx.data, selected_cells);\n\t\tctx.dispatch(\"change\", {\n\t\t\tdata: new_data.map((row) => row.map((cell) => cell.value)),\n\t\t\theaders: ctx.headers.map((h) => h.value),\n\t\t\tmetadata: null\n\t\t});\n\t}\n\treturn true;\n}\n\nfunction handle_arrow_keys(\n\tevent: KeyboardEvent,\n\tctx: DataFrameContext,\n\ti: number,\n\tj: number\n): boolean {\n\tconst state = get(ctx.state);\n\tconst editing = state.ui_state.editing;\n\tconst selected_cells = state.ui_state.selected_cells;\n\n\tif (editing) return false;\n\tif (!ctx.data) return false;\n\n\tevent.preventDefault();\n\n\tconst next_coords = ctx.actions.move_cursor(event, [i, j], ctx.data);\n\tif (next_coords) {\n\t\tif (event.shiftKey) {\n\t\t\tctx.actions.set_selected_cells(\n\t\t\t\tctx.actions.get_range_selection(\n\t\t\t\t\tselected_cells.length > 0 ? selected_cells[0] : [i, j],\n\t\t\t\t\tnext_coords\n\t\t\t\t)\n\t\t\t);\n\t\t\tctx.actions.set_editing(false);\n\t\t} else {\n\t\t\tctx.actions.set_selected_cells([next_coords]);\n\t\t\tctx.actions.set_editing(false);\n\t\t}\n\t\tctx.actions.set_selected(next_coords);\n\t} else if (next_coords === false && event.key === \"ArrowUp\" && i === 0) {\n\t\tctx.actions.set_selected_header(j);\n\t\tctx.actions.set_selected(false);\n\t\tctx.actions.set_selected_cells([]);\n\t\tctx.actions.set_editing(false);\n\t}\n\treturn true;\n}\n\nasync function handle_enter_key(\n\tevent: KeyboardEvent,\n\tctx: DataFrameContext,\n\ti: number,\n\tj: number\n): Promise<boolean> {\n\tif (!ctx.data || !ctx.els) return false;\n\n\tconst state = get(ctx.state);\n\tif (!state.config.editable) return false;\n\n\tconst editing = state.ui_state.editing;\n\tif (editing && event.shiftKey) return false;\n\n\tevent.preventDefault();\n\n\tif (editing && dequal(editing, [i, j])) {\n\t\tconst cell_id = ctx.data[i][j].id;\n\t\tconst input_el = ctx.els[cell_id]?.input;\n\t\tif (input_el) {\n\t\t\tawait save_cell_value(input_el.value, ctx, i, j);\n\t\t}\n\t\tctx.actions.set_editing(false);\n\t\tawait tick();\n\t\tctx.parent_element?.focus();\n\t} else {\n\t\tctx.actions.set_editing([i, j]);\n\t}\n\n\treturn true;\n}\n\nfunction handle_tab_key(\n\tevent: KeyboardEvent,\n\tctx: DataFrameContext,\n\ti: number,\n\tj: number\n): boolean {\n\tif (!ctx.data) return false;\n\n\tevent.preventDefault();\n\tctx.actions.set_editing(false);\n\tconst next_cell = ctx.actions.get_next_cell_coordinates(\n\t\t[i, j],\n\t\tctx.data,\n\t\tevent.shiftKey\n\t);\n\tif (next_cell) {\n\t\tctx.actions.set_selected_cells([next_cell]);\n\t\tctx.actions.set_selected(next_cell);\n\t\tif (get(ctx.state).config.editable) {\n\t\t\tctx.actions.set_editing(next_cell);\n\t\t}\n\t}\n\treturn true;\n}\n\nfunction handle_default_key(\n\tevent: KeyboardEvent,\n\tctx: DataFrameContext,\n\ti: number,\n\tj: number\n): boolean {\n\tconst state = get(ctx.state);\n\tif (!state.config.editable) return false;\n\n\tconst editing = state.ui_state.editing;\n\n\tif (\n\t\t(!editing || (editing && dequal(editing, [i, j]))) &&\n\t\tevent.key.length === 1\n\t) {\n\t\tctx.actions.set_editing([i, j]);\n\t\treturn true;\n\t}\n\treturn false;\n}\n\nasync function handle_cell_navigation(\n\tevent: KeyboardEvent,\n\tctx: DataFrameContext\n): Promise<boolean> {\n\tif (!ctx.data) return false;\n\n\tconst state = get(ctx.state);\n\tconst selected = state.ui_state.selected;\n\tconst selected_cells = state.ui_state.selected_cells;\n\n\tif (!selected) return false;\n\tif (event.key === \"c\" && (event.metaKey || event.ctrlKey)) {\n\t\tevent.preventDefault();\n\t\tif (selected_cells.length > 0) {\n\t\t\tawait copy_table_data(ctx.data, selected_cells);\n\t\t}\n\t\tctx.actions.set_copy_flash(true);\n\t\treturn true;\n\t}\n\n\tconst [i, j] = selected;\n\n\tswitch (event.key) {\n\t\tcase \"ArrowRight\":\n\t\tcase \"ArrowLeft\":\n\t\tcase \"ArrowDown\":\n\t\tcase \"ArrowUp\":\n\t\t\treturn handle_arrow_keys(event, ctx, i, j);\n\t\tcase \"Escape\":\n\t\t\tif (!state.config.editable) return false;\n\t\t\tevent.preventDefault();\n\t\t\tctx.actions.set_editing(false);\n\t\t\ttick().then(() => {\n\t\t\t\tif (ctx.parent_element) {\n\t\t\t\t\tctx.parent_element.focus();\n\t\t\t\t}\n\t\t\t});\n\n\t\t\treturn true;\n\t\tcase \"Enter\":\n\t\t\treturn await handle_enter_key(event, ctx, i, j);\n\t\tcase \"Tab\":\n\t\t\treturn handle_tab_key(event, ctx, i, j);\n\t\tcase \"Delete\":\n\t\tcase \"Backspace\":\n\t\t\treturn handle_delete_operation(event, ctx);\n\t\tdefault:\n\t\t\treturn handle_default_key(event, ctx, i, j);\n\t}\n}\n\nexport async function handle_keydown(\n\tevent: KeyboardEvent,\n\tcontext: DataFrameContext\n): Promise<void> {\n\tif (handle_header_navigation(event, context)) return;\n\tif (handle_delete_operation(event, context)) return;\n\tawait handle_cell_navigation(event, context);\n}\n", "import type { CellCoordinate } from \"../types\";\nimport { get_range_selection } from \"../selection_utils\";\n\nexport type DragState = {\n\tis_dragging: boolean;\n\tdrag_start: CellCoordinate | null;\n\tmouse_down_pos: { x: number; y: number } | null;\n};\n\nexport type DragHandlers = {\n\thandle_mouse_down: (event: MouseEvent, row: number, col: number) => void;\n\thandle_mouse_move: (event: <PERSON><PERSON>vent) => void;\n\thandle_mouse_up: (event: MouseEvent) => void;\n};\n\nexport function create_drag_handlers(\n\tstate: DragState,\n\tset_is_dragging: (value: boolean) => void,\n\tset_selected_cells: (cells: CellCoordinate[]) => void,\n\tset_selected: (cell: CellCoordinate | false) => void,\n\thandle_cell_click: (event: MouseEvent, row: number, col: number) => void,\n\tshow_row_numbers: boolean,\n\tparent_element?: HTMLElement\n): DragHandlers {\n\tconst start_drag = (event: MouseEvent, row: number, col: number): void => {\n\t\tconst target = event.target as HTMLElement;\n\t\tconst is_checkbox_click =\n\t\t\t(target as HTMLInputElement).type === \"checkbox\" ||\n\t\t\ttarget.closest('input[type=\"checkbox\"]') ||\n\t\t\ttarget.closest(\".bool-cell\");\n\n\t\tif (\n\t\t\tevent.target instanceof HTMLAnchorElement ||\n\t\t\t(show_row_numbers && col === -1) ||\n\t\t\tis_checkbox_click\n\t\t)\n\t\t\treturn;\n\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\n\t\tstate.mouse_down_pos = { x: event.clientX, y: event.clientY };\n\t\tstate.drag_start = [row, col];\n\n\t\tif (!event.shiftKey && !event.metaKey && !event.ctrlKey) {\n\t\t\tset_selected_cells([[row, col]]);\n\t\t\tset_selected([row, col]);\n\t\t\thandle_cell_click(event, row, col);\n\t\t}\n\t};\n\n\tconst update_selection = (event: MouseEvent): void => {\n\t\tconst cell = (event.target as HTMLElement).closest(\"td\");\n\t\tif (!cell) return;\n\n\t\tconst row = parseInt(cell.getAttribute(\"data-row\") || \"0\");\n\t\tconst col = parseInt(cell.getAttribute(\"data-col\") || \"0\");\n\n\t\tif (isNaN(row) || isNaN(col)) return;\n\n\t\tconst selection_range = get_range_selection(state.drag_start!, [row, col]);\n\t\tset_selected_cells(selection_range);\n\t\tset_selected([row, col]);\n\t};\n\n\tconst end_drag = (event: MouseEvent): void => {\n\t\tif (!state.is_dragging && state.drag_start) {\n\t\t\thandle_cell_click(event, state.drag_start[0], state.drag_start[1]);\n\t\t} else if (state.is_dragging && parent_element) {\n\t\t\tparent_element.focus();\n\t\t}\n\n\t\tstate.is_dragging = false;\n\t\tset_is_dragging(false);\n\t\tstate.drag_start = null;\n\t\tstate.mouse_down_pos = null;\n\t};\n\n\treturn {\n\t\thandle_mouse_down: start_drag,\n\n\t\thandle_mouse_move(event: MouseEvent): void {\n\t\t\tif (!state.drag_start || !state.mouse_down_pos) return;\n\n\t\t\tif (!(event.buttons & 1)) {\n\t\t\t\tend_drag(event);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst dx = Math.abs(event.clientX - state.mouse_down_pos.x);\n\t\t\tconst dy = Math.abs(event.clientY - state.mouse_down_pos.y);\n\n\t\t\tif (!state.is_dragging && (dx > 3 || dy > 3)) {\n\t\t\t\tstate.is_dragging = true;\n\t\t\t\tset_is_dragging(true);\n\t\t\t}\n\n\t\t\tif (state.is_dragging) {\n\t\t\t\tupdate_selection(event);\n\t\t\t}\n\t\t},\n\n\t\thandle_mouse_up: end_drag\n\t};\n}\n", "<script lang=\"ts\" context=\"module\">\n\timport {\n\t\tcreate_dataframe_context,\n\t\ttype SortDirection,\n\t\ttype FilterDatatype\n\t} from \"./context/dataframe_context\";\n</script>\n\n<script lang=\"ts\">\n\timport { afterUpdate, createEventDispatcher, tick, onMount } from \"svelte\";\n\timport { dequal } from \"dequal/lite\";\n\timport { Upload } from \"@gradio/upload\";\n\n\timport EditableCell from \"./EditableCell.svelte\";\n\timport RowN<PERSON>ber from \"./RowNumber.svelte\";\n\timport TableHeader from \"./TableHeader.svelte\";\n\timport TableCell from \"./TableCell.svelte\";\n\timport EmptyRowButton from \"./EmptyRowButton.svelte\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport { type Client } from \"@gradio/client\";\n\timport VirtualTable from \"./VirtualTable.svelte\";\n\timport type { Headers, DataframeValue, Datatype } from \"./utils\";\n\timport CellMenu from \"./CellMenu.svelte\";\n\timport Toolbar from \"./Toolbar.svelte\";\n\timport type { CellCoordinate } from \"./types\";\n\timport {\n\t\tis_cell_selected,\n\t\tshould_show_cell_menu,\n\t\tget_current_indices,\n\t\thandle_click_outside as handle_click_outside_util,\n\t\tcalculate_selection_positions\n\t} from \"./selection_utils\";\n\timport {\n\t\tcopy_table_data,\n\t\tget_max,\n\t\thandle_file_upload\n\t} from \"./utils/table_utils\";\n\timport { make_headers, process_data } from \"./utils/data_processing\";\n\timport { cast_value_to_type } from \"./utils\";\n\timport { handle_keydown, handle_cell_blur } from \"./utils/keyboard_utils\";\n\timport {\n\t\tcreate_drag_handlers,\n\t\ttype DragState,\n\t\ttype DragHandlers\n\t} from \"./utils/drag_utils\";\n\timport { sort_data_and_preserve_selection } from \"./utils/sort_utils\";\n\timport { filter_data_and_preserve_selection } from \"./utils/filter_utils\";\n\n\texport let datatype: Datatype | Datatype[];\n\texport let label: string | null = null;\n\texport let show_label = true;\n\texport let headers: Headers = [];\n\texport let values: (string | number)[][] = [];\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\texport let row_count: [number, \"fixed\" | \"dynamic\"];\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let components: Record<string, any> = {};\n\n\texport let editable = true;\n\texport let wrap = false;\n\texport let root: string;\n\texport let i18n: I18nFormatter;\n\n\texport let max_height = 500;\n\texport let line_breaks = true;\n\texport let column_widths: string[] = [];\n\texport let show_row_numbers = false;\n\texport let upload: Client[\"upload\"];\n\texport let stream_handler: Client[\"stream\"];\n\texport let show_fullscreen_button = false;\n\texport let show_copy_button = false;\n\texport let value_is_output = false;\n\texport let max_chars: number | undefined = undefined;\n\texport let show_search: \"none\" | \"search\" | \"filter\" = \"none\";\n\texport let pinned_columns = 0;\n\texport let static_columns: (string | number)[] = [];\n\texport let fullscreen = false;\n\n\tconst df_ctx = create_dataframe_context({\n\t\tshow_fullscreen_button,\n\t\tshow_copy_button,\n\t\tshow_search,\n\t\tshow_row_numbers,\n\t\teditable,\n\t\tpinned_columns,\n\t\tshow_label,\n\t\tline_breaks,\n\t\twrap,\n\t\tmax_height,\n\t\tcolumn_widths,\n\t\tmax_chars,\n\t\tstatic_columns\n\t});\n\n\tconst { state: df_state, actions: df_actions } = df_ctx;\n\n\t$: selected_cells = $df_state.ui_state.selected_cells;\n\t$: selected = $df_state.ui_state.selected;\n\t$: editing = $df_state.ui_state.editing;\n\t$: header_edit = $df_state.ui_state.header_edit;\n\t$: selected_header = $df_state.ui_state.selected_header;\n\t$: active_cell_menu = $df_state.ui_state.active_cell_menu;\n\t$: active_header_menu = $df_state.ui_state.active_header_menu;\n\t$: copy_flash = $df_state.ui_state.copy_flash;\n\n\t$: actual_pinned_columns =\n\t\tpinned_columns && data?.[0]?.length\n\t\t\t? Math.min(pinned_columns, data[0].length)\n\t\t\t: 0;\n\n\tonMount(() => {\n\t\tdf_ctx.parent_element = parent;\n\t\tdf_ctx.get_data_at = get_data_at;\n\t\tdf_ctx.get_column = get_column;\n\t\tdf_ctx.get_row = get_row;\n\t\tdf_ctx.dispatch = dispatch;\n\t\tinit_drag_handlers();\n\n\t\tconst observer = new IntersectionObserver((entries) => {\n\t\t\tentries.forEach((entry) => {\n\t\t\t\tif (entry.isIntersecting && !is_visible) {\n\t\t\t\t\twidth_calculated = false;\n\t\t\t\t}\n\t\t\t\tis_visible = entry.isIntersecting;\n\t\t\t});\n\t\t});\n\t\tobserver.observe(parent);\n\t\tdocument.addEventListener(\"click\", handle_click_outside);\n\t\twindow.addEventListener(\"resize\", handle_resize);\n\n\t\tconst global_mouse_up = (event: MouseEvent): void => {\n\t\t\tif (is_dragging || drag_start) {\n\t\t\t\thandle_mouse_up(event);\n\t\t\t}\n\t\t};\n\t\tdocument.addEventListener(\"mouseup\", global_mouse_up);\n\n\t\treturn () => {\n\t\t\tobserver.disconnect();\n\t\t\tdocument.removeEventListener(\"click\", handle_click_outside);\n\t\t\twindow.removeEventListener(\"resize\", handle_resize);\n\t\t\tdocument.removeEventListener(\"mouseup\", global_mouse_up);\n\t\t};\n\t});\n\n\t$: {\n\t\tif (data || _headers || els) {\n\t\t\tdf_ctx.data = data;\n\t\t\tdf_ctx.headers = _headers;\n\t\t\tdf_ctx.els = els;\n\t\t\tdf_ctx.display_value = display_value;\n\t\t\tdf_ctx.styling = styling;\n\t\t}\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: DataframeValue;\n\t\tinput: undefined;\n\t\tselect: SelectData;\n\t\tsearch: string | null;\n\t}>();\n\n\tlet els: Record<\n\t\tstring,\n\t\t{ cell: null | HTMLTableCellElement; input: null | HTMLTextAreaElement }\n\t> = {};\n\tlet data_binding: Record<string, (typeof data)[0][0]> = {};\n\tlet _headers = make_headers(headers, col_count, els, make_id);\n\tlet old_headers: string[] = headers;\n\tlet data: { id: string; value: string | number; display_value?: string }[][] =\n\t\t[[]];\n\tlet old_val: undefined | (string | number)[][] = undefined;\n\tlet search_results: {\n\t\tid: string;\n\t\tvalue: string | number;\n\t\tdisplay_value?: string;\n\t\tstyling?: string;\n\t}[][] = [[]];\n\tlet dragging = false;\n\tlet color_accent_copied: string;\n\tlet filtered_to_original_map: number[] = [];\n\n\tonMount(() => {\n\t\tconst color = getComputedStyle(document.documentElement)\n\t\t\t.getPropertyValue(\"--color-accent\")\n\t\t\t.trim();\n\t\tcolor_accent_copied = color + \"40\"; // 80 is 50% opacity in hex\n\t\tdocument.documentElement.style.setProperty(\n\t\t\t\"--color-accent-copied\",\n\t\t\tcolor_accent_copied\n\t\t);\n\t});\n\n\tconst get_data_at = (row: number, col: number): string | number =>\n\t\tdata?.[row]?.[col]?.value;\n\n\tconst get_column = (col: number): (string | number)[] =>\n\t\tdata?.map((row) => row[col]?.value) ?? [];\n\n\tconst get_row = (row: number): (string | number)[] =>\n\t\tdata?.[row]?.map((cell) => cell.value) ?? [];\n\n\t$: {\n\t\tif (!dequal(headers, old_headers)) {\n\t\t\t_headers = make_headers(headers, col_count, els, make_id);\n\t\t\told_headers = JSON.parse(JSON.stringify(headers));\n\t\t}\n\t}\n\n\tfunction make_id(): string {\n\t\treturn Math.random().toString(36).substring(2, 15);\n\t}\n\n\texport let display_value: string[][] | null = null;\n\texport let styling: string[][] | null = null;\n\n\t$: if (!dequal(values, old_val)) {\n\t\tif (parent) {\n\t\t\t// only clear column widths when the data structure changes\n\t\t\tconst is_reset =\n\t\t\t\tvalues.length === 0 || (values.length === 1 && values[0].length === 0);\n\t\t\tconst is_different_structure =\n\t\t\t\told_val !== undefined &&\n\t\t\t\t(values.length !== old_val.length ||\n\t\t\t\t\t(values[0] && old_val[0] && values[0].length !== old_val[0].length));\n\n\t\t\tif (is_reset || is_different_structure) {\n\t\t\t\tfor (let i = 0; i < 50; i++) {\n\t\t\t\t\tparent.style.removeProperty(`--cell-width-${i}`);\n\t\t\t\t}\n\t\t\t\tlast_width_data_length = 0;\n\t\t\t\tlast_width_column_count = 0;\n\t\t\t\twidth_calculated = false;\n\t\t\t}\n\t\t}\n\n\t\t// only reset sort state when values are changed\n\t\tconst is_reset =\n\t\t\tvalues.length === 0 || (values.length === 1 && values[0].length === 0);\n\t\tconst is_different_structure =\n\t\t\told_val !== undefined &&\n\t\t\t(values.length !== old_val.length ||\n\t\t\t\t(values[0] && old_val[0] && values[0].length !== old_val[0].length));\n\n\t\tdata = process_data(\n\t\t\tvalues as (string | number)[][],\n\t\t\tels,\n\t\t\tdata_binding,\n\t\t\tmake_id,\n\t\t\tdisplay_value\n\t\t);\n\t\told_val = JSON.parse(JSON.stringify(values)) as (string | number)[][];\n\n\t\tif (is_reset || is_different_structure) {\n\t\t\tdf_actions.reset_sort_state();\n\t\t} else if ($df_state.sort_state.sort_columns.length > 0) {\n\t\t\tsort_data(data, display_value, styling);\n\t\t} else {\n\t\t\tdf_actions.handle_sort(-1, \"asc\");\n\t\t\tdf_actions.reset_sort_state();\n\t\t}\n\n\t\tif ($df_state.filter_state.filter_columns.length > 0) {\n\t\t\tfilter_data(data, display_value, styling);\n\t\t} else {\n\t\t\tdf_actions.reset_filter_state();\n\t\t}\n\n\t\tif ($df_state.current_search_query) {\n\t\t\tdf_actions.handle_search(null);\n\t\t}\n\n\t\tif (parent && cells.length > 0 && (is_reset || is_different_structure)) {\n\t\t\twidth_calculated = false;\n\t\t}\n\t}\n\n\t$: if ($df_state.current_search_query !== undefined) {\n\t\tconst cell_map = new Map();\n\t\tfiltered_to_original_map = [];\n\n\t\tdata.forEach((row, row_idx) => {\n\t\t\tif (\n\t\t\t\trow.some((cell) =>\n\t\t\t\t\tString(cell?.value)\n\t\t\t\t\t\t.toLowerCase()\n\t\t\t\t\t\t.includes($df_state.current_search_query?.toLowerCase() || \"\")\n\t\t\t\t)\n\t\t\t) {\n\t\t\t\tfiltered_to_original_map.push(row_idx);\n\t\t\t}\n\t\t\trow.forEach((cell, col_idx) => {\n\t\t\t\tcell_map.set(cell.id, {\n\t\t\t\t\tvalue: cell.value,\n\t\t\t\t\tdisplay_value:\n\t\t\t\t\t\tcell.display_value !== undefined\n\t\t\t\t\t\t\t? cell.display_value\n\t\t\t\t\t\t\t: String(cell.value),\n\t\t\t\t\tstyling: styling?.[row_idx]?.[col_idx] || \"\"\n\t\t\t\t});\n\t\t\t});\n\t\t});\n\n\t\tconst filtered = df_actions.filter_data(data);\n\n\t\tsearch_results = filtered.map((row) =>\n\t\t\trow.map((cell) => {\n\t\t\t\tconst original = cell_map.get(cell.id);\n\t\t\t\treturn {\n\t\t\t\t\t...cell,\n\t\t\t\t\tdisplay_value:\n\t\t\t\t\t\toriginal?.display_value !== undefined\n\t\t\t\t\t\t\t? original.display_value\n\t\t\t\t\t\t\t: String(cell.value),\n\t\t\t\t\tstyling: original?.styling || \"\"\n\t\t\t\t};\n\t\t\t})\n\t\t);\n\t} else {\n\t\tfiltered_to_original_map = [];\n\t}\n\n\tlet previous_headers = _headers.map((h) => h.value);\n\tlet previous_data = data.map((row) => row.map((cell) => String(cell.value)));\n\n\t$: {\n\t\tif (data || _headers) {\n\t\t\tdf_actions.trigger_change(\n\t\t\t\tdata.map((row, rowIdx) =>\n\t\t\t\t\trow.map((cell, colIdx) => {\n\t\t\t\t\t\tconst dtype = Array.isArray(datatype) ? datatype[colIdx] : datatype;\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t...cell,\n\t\t\t\t\t\t\tvalue: cast_value_to_type(cell.value, dtype)\n\t\t\t\t\t\t};\n\t\t\t\t\t})\n\t\t\t\t),\n\t\t\t\t_headers,\n\t\t\t\tprevious_data,\n\t\t\t\tprevious_headers,\n\t\t\t\tvalue_is_output,\n\t\t\t\tdispatch\n\t\t\t);\n\t\t\tprevious_data = data.map((row) => row.map((cell) => String(cell.value)));\n\t\t\tprevious_headers = _headers.map((h) => h.value);\n\t\t}\n\t}\n\n\tfunction handle_sort(col: number, direction: SortDirection): void {\n\t\tdf_actions.handle_sort(col, direction);\n\t\tsort_data(data, display_value, styling);\n\t}\n\n\tfunction clear_sort(): void {\n\t\tdf_actions.reset_sort_state();\n\t\tsort_data(data, display_value, styling);\n\t}\n\n\t$: {\n\t\tif ($df_state.filter_state.filter_columns.length > 0) {\n\t\t\tfilter_data(data, display_value, styling);\n\t\t}\n\n\t\tif ($df_state.sort_state.sort_columns.length > 0) {\n\t\t\tsort_data(data, display_value, styling);\n\t\t\tdf_actions.update_row_order(data);\n\t\t}\n\t}\n\n\tfunction handle_filter(\n\t\tcol: number,\n\t\tdatatype: FilterDatatype,\n\t\tfilter: string,\n\t\tvalue: string\n\t): void {\n\t\tdf_actions.handle_filter(col, datatype, filter, value);\n\t\tfilter_data(data, display_value, styling);\n\t}\n\n\tfunction clear_filter(): void {\n\t\tdf_actions.reset_filter_state();\n\t\tfilter_data(data, display_value, styling);\n\t}\n\n\tasync function edit_header(i: number, _select = false): Promise<void> {\n\t\tif (!editable || header_edit === i || col_count[1] !== \"dynamic\") return;\n\t\tdf_actions.set_header_edit(i);\n\t}\n\n\tfunction handle_header_click(event: MouseEvent, col: number): void {\n\t\tif (event.target instanceof HTMLAnchorElement) {\n\t\t\treturn;\n\t\t}\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\t\tif (!editable) return;\n\t\tdf_actions.set_editing(false);\n\t\tdf_actions.handle_header_click(col, editable);\n\t\tparent.focus();\n\t}\n\n\tfunction end_header_edit(event: CustomEvent<KeyboardEvent>): void {\n\t\tif (!editable) return;\n\t\tdf_actions.end_header_edit(event.detail.key);\n\t\tparent.focus();\n\t}\n\n\tasync function add_row(index?: number): Promise<void> {\n\t\tparent.focus();\n\n\t\tif (row_count[1] !== \"dynamic\") return;\n\n\t\tconst new_row = Array(data[0]?.length || headers.length)\n\t\t\t.fill(0)\n\t\t\t.map((_, i) => {\n\t\t\t\tconst _id = make_id();\n\t\t\t\tels[_id] = { cell: null, input: null };\n\t\t\t\treturn { id: _id, value: \"\" };\n\t\t\t});\n\n\t\tif (data.length === 0) {\n\t\t\tdata = [new_row];\n\t\t} else if (index !== undefined && index >= 0 && index <= data.length) {\n\t\t\tdata.splice(index, 0, new_row);\n\t\t} else {\n\t\t\tdata.push(new_row);\n\t\t}\n\n\t\tselected = [index !== undefined ? index : data.length - 1, 0];\n\t}\n\n\tasync function add_col(index?: number): Promise<void> {\n\t\tparent.focus();\n\t\tif (col_count[1] !== \"dynamic\") return;\n\n\t\tconst result = df_actions.add_col(data, headers, make_id, index);\n\n\t\tresult.data.forEach((row) => {\n\t\t\trow.forEach((cell) => {\n\t\t\t\tif (!els[cell.id]) {\n\t\t\t\t\tels[cell.id] = { cell: null, input: null };\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\n\t\tdata = result.data;\n\t\theaders = result.headers;\n\n\t\tawait tick();\n\n\t\trequestAnimationFrame(() => {\n\t\t\tedit_header(index !== undefined ? index : data[0].length - 1, true);\n\t\t\tconst new_w = parent.querySelectorAll(\"tbody\")[1].offsetWidth;\n\t\t\tparent.querySelectorAll(\"table\")[1].scrollTo({ left: new_w });\n\t\t});\n\t}\n\n\tfunction handle_click_outside(event: Event): void {\n\t\tif (handle_click_outside_util(event, parent)) {\n\t\t\tdf_actions.clear_ui_state();\n\t\t\theader_edit = false;\n\t\t\tselected_header = false;\n\t\t}\n\t}\n\n\t$: max = get_max(data);\n\n\tlet width_calc_timeout: ReturnType<typeof setTimeout>;\n\t$: if (cells[0] && cells[0]?.clientWidth) {\n\t\tclearTimeout(width_calc_timeout);\n\t\twidth_calc_timeout = setTimeout(() => set_cell_widths(), 100);\n\t}\n\n\tlet width_calculated = false;\n\t$: if (cells[0] && !width_calculated) {\n\t\tset_cell_widths();\n\t\twidth_calculated = true;\n\t}\n\tlet cells: HTMLTableCellElement[] = [];\n\tlet parent: HTMLDivElement;\n\tlet table: HTMLTableElement;\n\tlet last_width_data_length = 0;\n\tlet last_width_column_count = 0;\n\n\tfunction set_cell_widths(): void {\n\t\tconst column_count = data[0]?.length || 0;\n\t\tif ($df_state.filter_state.filter_columns.length > 0) {\n\t\t\treturn;\n\t\t}\n\t\tif (\n\t\t\tlast_width_data_length === data.length &&\n\t\t\tlast_width_column_count === column_count &&\n\t\t\t$df_state.sort_state.sort_columns.length > 0\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\tlast_width_data_length = data.length;\n\t\tlast_width_column_count = column_count;\n\n\t\tconst widths = cells.map((el) => el?.clientWidth || 0);\n\t\tif (widths.length === 0) return;\n\n\t\tif (show_row_numbers) {\n\t\t\tparent.style.setProperty(`--cell-width-row-number`, `${widths[0]}px`);\n\t\t}\n\n\t\tfor (let i = 0; i < 50; i++) {\n\t\t\tif (!column_widths[i]) {\n\t\t\t\tparent.style.removeProperty(`--cell-width-${i}`);\n\t\t\t} else if (column_widths[i].endsWith(\"%\")) {\n\t\t\t\tconst percentage = parseFloat(column_widths[i]);\n\t\t\t\tconst pixel_width = Math.floor((percentage / 100) * parent.clientWidth);\n\t\t\t\tparent.style.setProperty(`--cell-width-${i}`, `${pixel_width}px`);\n\t\t\t} else {\n\t\t\t\tparent.style.setProperty(`--cell-width-${i}`, column_widths[i]);\n\t\t\t}\n\t\t}\n\n\t\twidths.forEach((width, i) => {\n\t\t\tif (!column_widths[i]) {\n\t\t\t\tconst calculated_width = `${Math.max(width, 45)}px`;\n\t\t\t\tparent.style.setProperty(`--cell-width-${i}`, calculated_width);\n\t\t\t}\n\t\t});\n\t}\n\n\tfunction get_cell_width(index: number): string {\n\t\treturn `var(--cell-width-${index})`;\n\t}\n\n\tlet table_height: number =\n\t\tvalues.slice(0, (max_height / values.length) * 37).length * 37 + 37;\n\tlet scrollbar_width = 0;\n\n\tfunction sort_data(\n\t\t_data: typeof data,\n\t\t_display_value: string[][] | null,\n\t\t_styling: string[][] | null\n\t): void {\n\t\tconst result = sort_data_and_preserve_selection(\n\t\t\t_data,\n\t\t\t_display_value,\n\t\t\t_styling,\n\t\t\t$df_state.sort_state.sort_columns,\n\t\t\tselected,\n\t\t\tget_current_indices\n\t\t);\n\n\t\tdata = result.data;\n\t\tselected = result.selected;\n\t}\n\n\tfunction filter_data(\n\t\t_data: typeof data,\n\t\t_display_value: string[][] | null,\n\t\t_styling: string[][] | null\n\t): void {\n\t\tconst result = filter_data_and_preserve_selection(\n\t\t\t_data,\n\t\t\t_display_value,\n\t\t\t_styling,\n\t\t\t$df_state.filter_state.filter_columns,\n\t\t\tselected,\n\t\t\tget_current_indices,\n\t\t\t$df_state.filter_state.initial_data?.data,\n\t\t\t$df_state.filter_state.initial_data?.display_value,\n\t\t\t$df_state.filter_state.initial_data?.styling\n\t\t);\n\t\tdata = result.data;\n\t\tselected = result.selected;\n\t}\n\n\t$: selected_index = !!selected && selected[0];\n\n\tlet is_visible = false;\n\n\tconst set_copy_flash = (value: boolean): void => {\n\t\tdf_actions.set_copy_flash(value);\n\t\tif (value) {\n\t\t\tsetTimeout(() => df_actions.set_copy_flash(false), 800);\n\t\t}\n\t};\n\n\tlet previous_selected_cells: [number, number][] = [];\n\n\t$: {\n\t\tif (copy_flash && !dequal(selected_cells, previous_selected_cells)) {\n\t\t\tset_copy_flash(false);\n\t\t}\n\t\tprevious_selected_cells = selected_cells;\n\t}\n\n\tfunction handle_blur(\n\t\tevent: CustomEvent<{\n\t\t\tblur_event: FocusEvent;\n\t\t\tcoords: [number, number];\n\t\t}>\n\t): void {\n\t\tconst { blur_event, coords } = event.detail;\n\t\thandle_cell_blur(blur_event, df_ctx, coords);\n\t}\n\n\tfunction toggle_header_menu(event: MouseEvent, col: number): void {\n\t\tevent.stopPropagation();\n\t\tif (active_header_menu && active_header_menu.col === col) {\n\t\t\tdf_actions.set_active_header_menu(null);\n\t\t} else {\n\t\t\tconst header = (event.target as HTMLElement).closest(\"th\");\n\t\t\tif (header) {\n\t\t\t\tconst rect = header.getBoundingClientRect();\n\t\t\t\tdf_actions.set_active_header_menu({\n\t\t\t\t\tcol,\n\t\t\t\t\tx: rect.right,\n\t\t\t\t\ty: rect.bottom\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\n\tfunction delete_col_at(index: number): void {\n\t\tif (col_count[1] !== \"dynamic\") return;\n\t\tif (data[0].length <= 1) return;\n\n\t\tconst result = df_actions.delete_col_at(data, headers, index);\n\t\tdata = result.data;\n\t\theaders = result.headers;\n\t\t_headers = make_headers(headers, col_count, els, make_id);\n\t\tdf_actions.set_active_cell_menu(null);\n\t\tdf_actions.set_active_header_menu(null);\n\t\tdf_actions.set_selected(false);\n\t\tdf_actions.set_selected_cells([]);\n\t\tdf_actions.set_editing(false);\n\t}\n\n\tfunction delete_row_at(index: number): void {\n\t\tdata = df_actions.delete_row_at(data, index);\n\t\tdf_actions.set_active_cell_menu(null);\n\t\tdf_actions.set_active_header_menu(null);\n\t}\n\n\tlet selected_cell_coords: CellCoordinate;\n\t$: if (selected !== false) selected_cell_coords = selected;\n\n\t$: if (selected !== false) {\n\t\tconst positions = calculate_selection_positions(\n\t\t\tselected,\n\t\t\tdata,\n\t\t\tels,\n\t\t\tparent,\n\t\t\ttable\n\t\t);\n\t\tdocument.documentElement.style.setProperty(\n\t\t\t\"--selected-col-pos\",\n\t\t\tpositions.col_pos\n\t\t);\n\t\tdocument.documentElement.style.setProperty(\n\t\t\t\"--selected-row-pos\",\n\t\t\tpositions.row_pos || \"0px\"\n\t\t);\n\t}\n\n\tfunction commit_filter(): void {\n\t\tif ($df_state.current_search_query && show_search === \"filter\") {\n\t\t\tconst filtered_data: (string | number)[][] = [];\n\t\t\tconst filtered_display_values: string[][] = [];\n\t\t\tconst filtered_styling: string[][] = [];\n\n\t\t\tsearch_results.forEach((row) => {\n\t\t\t\tconst data_row: (string | number)[] = [];\n\t\t\t\tconst display_row: string[] = [];\n\t\t\t\tconst styling_row: string[] = [];\n\n\t\t\t\trow.forEach((cell) => {\n\t\t\t\t\tdata_row.push(cell.value);\n\t\t\t\t\tdisplay_row.push(\n\t\t\t\t\t\tcell.display_value !== undefined\n\t\t\t\t\t\t\t? cell.display_value\n\t\t\t\t\t\t\t: String(cell.value)\n\t\t\t\t\t);\n\t\t\t\t\tstyling_row.push(cell.styling || \"\");\n\t\t\t\t});\n\n\t\t\t\tfiltered_data.push(data_row);\n\t\t\t\tfiltered_display_values.push(display_row);\n\t\t\t\tfiltered_styling.push(styling_row);\n\t\t\t});\n\n\t\t\tconst change_payload = {\n\t\t\t\tdata: filtered_data,\n\t\t\t\theaders: _headers.map((h) => h.value),\n\t\t\t\tmetadata: {\n\t\t\t\t\tdisplay_value: filtered_display_values,\n\t\t\t\t\tstyling: filtered_styling\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tdispatch(\"change\", change_payload);\n\n\t\t\tif (!value_is_output) {\n\t\t\t\tdispatch(\"input\");\n\t\t\t}\n\n\t\t\tdf_actions.handle_search(null);\n\t\t}\n\t}\n\n\tlet viewport: HTMLTableElement;\n\tlet show_scroll_button = false;\n\n\tfunction scroll_to_top(): void {\n\t\tviewport.scrollTo({\n\t\t\ttop: 0\n\t\t});\n\t}\n\n\tfunction handle_resize(): void {\n\t\tdf_actions.set_active_cell_menu(null);\n\t\tdf_actions.set_active_header_menu(null);\n\t\tselected_cells = [];\n\t\tselected = false;\n\t\tediting = false;\n\t\twidth_calculated = false;\n\t\tset_cell_widths();\n\t}\n\n\tfunction add_row_at(index: number, position: \"above\" | \"below\"): void {\n\t\tconst row_index = position === \"above\" ? index : index + 1;\n\t\tadd_row(row_index);\n\t\tactive_cell_menu = null;\n\t\tactive_header_menu = null;\n\t}\n\n\tfunction add_col_at(index: number, position: \"left\" | \"right\"): void {\n\t\tconst col_index = position === \"left\" ? index : index + 1;\n\t\tadd_col(col_index);\n\t\tactive_cell_menu = null;\n\t\tactive_header_menu = null;\n\t}\n\n\texport function reset_sort_state(): void {\n\t\tdf_actions.reset_sort_state();\n\t}\n\n\tlet is_dragging = false;\n\tlet drag_start: [number, number] | null = null;\n\tlet mouse_down_pos: { x: number; y: number } | null = null;\n\n\tconst drag_state: DragState = {\n\t\tis_dragging,\n\t\tdrag_start,\n\t\tmouse_down_pos\n\t};\n\n\t$: {\n\t\tis_dragging = drag_state.is_dragging;\n\t\tdrag_start = drag_state.drag_start;\n\t\tmouse_down_pos = drag_state.mouse_down_pos;\n\t}\n\n\tlet drag_handlers: DragHandlers;\n\n\tfunction init_drag_handlers(): void {\n\t\tdrag_handlers = create_drag_handlers(\n\t\t\tdrag_state,\n\t\t\t(value) => (is_dragging = value),\n\t\t\t(cells) => df_actions.set_selected_cells(cells),\n\t\t\t(cell) => df_actions.set_selected(cell),\n\t\t\t(event, row, col) => df_actions.handle_cell_click(event, row, col),\n\t\t\tshow_row_numbers,\n\t\t\tparent\n\t\t);\n\t}\n\n\t$: if (parent) init_drag_handlers();\n\n\t$: handle_mouse_down = drag_handlers?.handle_mouse_down || (() => {});\n\t$: handle_mouse_move = drag_handlers?.handle_mouse_move || (() => {});\n\t$: handle_mouse_up = drag_handlers?.handle_mouse_up || (() => {});\n\n\tfunction get_cell_display_value(row: number, col: number): string {\n\t\tconst is_search_active = $df_state.current_search_query !== undefined;\n\n\t\tif (is_search_active && search_results?.[row]?.[col]) {\n\t\t\treturn search_results[row][col].display_value !== undefined\n\t\t\t\t? search_results[row][col].display_value\n\t\t\t\t: String(search_results[row][col].value);\n\t\t}\n\n\t\tif (data?.[row]?.[col]) {\n\t\t\treturn data[row][col].display_value !== undefined\n\t\t\t\t? data[row][col].display_value\n\t\t\t\t: String(data[row][col].value);\n\t\t}\n\n\t\treturn \"\";\n\t}\n</script>\n\n<svelte:window on:resize={() => set_cell_widths()} />\n\n<div class=\"table-container\">\n\t{#if (label && label.length !== 0 && show_label) || show_fullscreen_button || show_copy_button || show_search !== \"none\"}\n\t\t<div class=\"header-row\">\n\t\t\t{#if label && label.length !== 0 && show_label}\n\t\t\t\t<div class=\"label\">\n\t\t\t\t\t<p>{label}</p>\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t\t<Toolbar\n\t\t\t\t{show_fullscreen_button}\n\t\t\t\t{fullscreen}\n\t\t\t\ton_copy={async () => await copy_table_data(data, null)}\n\t\t\t\t{show_copy_button}\n\t\t\t\t{show_search}\n\t\t\t\ton:search={(e) => df_actions.handle_search(e.detail)}\n\t\t\t\ton:fullscreen\n\t\t\t\ton_commit_filter={commit_filter}\n\t\t\t\tcurrent_search_query={$df_state.current_search_query}\n\t\t\t/>\n\t\t</div>\n\t{/if}\n\t<div\n\t\tbind:this={parent}\n\t\tclass=\"table-wrap\"\n\t\tclass:dragging={is_dragging}\n\t\tclass:no-wrap={!wrap}\n\t\tstyle=\"height:{table_height}px;\"\n\t\tclass:menu-open={active_cell_menu || active_header_menu}\n\t\ton:keydown={(e) => handle_keydown(e, df_ctx)}\n\t\ton:mousemove={handle_mouse_move}\n\t\ton:mouseup={handle_mouse_up}\n\t\ton:mouseleave={handle_mouse_up}\n\t\trole=\"grid\"\n\t\ttabindex=\"0\"\n\t>\n\t\t<table bind:this={table} aria-hidden=\"true\">\n\t\t\t{#if label && label.length !== 0}\n\t\t\t\t<caption class=\"sr-only\">{label}</caption>\n\t\t\t{/if}\n\t\t\t<thead>\n\t\t\t\t<tr>\n\t\t\t\t\t{#if show_row_numbers}\n\t\t\t\t\t\t<RowNumber is_header={true} />\n\t\t\t\t\t{/if}\n\t\t\t\t\t{#each _headers as { value, id }, i (id)}\n\t\t\t\t\t\t<TableHeader\n\t\t\t\t\t\t\tbind:value={_headers[i].value}\n\t\t\t\t\t\t\t{i}\n\t\t\t\t\t\t\t{actual_pinned_columns}\n\t\t\t\t\t\t\t{header_edit}\n\t\t\t\t\t\t\t{selected_header}\n\t\t\t\t\t\t\t{headers}\n\t\t\t\t\t\t\t{get_cell_width}\n\t\t\t\t\t\t\t{handle_header_click}\n\t\t\t\t\t\t\t{toggle_header_menu}\n\t\t\t\t\t\t\t{end_header_edit}\n\t\t\t\t\t\t\tsort_columns={$df_state.sort_state.sort_columns}\n\t\t\t\t\t\t\tfilter_columns={$df_state.filter_state.filter_columns}\n\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t{max_chars}\n\t\t\t\t\t\t\t{editable}\n\t\t\t\t\t\t\tis_static={static_columns.includes(i)}\n\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\tbind:el={els[id].input}\n\t\t\t\t\t\t\t{col_count}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/each}\n\t\t\t\t</tr>\n\t\t\t</thead>\n\t\t\t<tbody>\n\t\t\t\t<tr>\n\t\t\t\t\t{#if show_row_numbers}\n\t\t\t\t\t\t<RowNumber index={0} />\n\t\t\t\t\t{/if}\n\t\t\t\t\t{#each max as { value, id }, j (id)}\n\t\t\t\t\t\t<td tabindex=\"-1\" bind:this={cells[j]}>\n\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t<EditableCell\n\t\t\t\t\t\t\t\t\t{value}\n\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\tdatatype={Array.isArray(datatype) ? datatype[j] : datatype}\n\t\t\t\t\t\t\t\t\tedit={false}\n\t\t\t\t\t\t\t\t\tel={null}\n\t\t\t\t\t\t\t\t\t{editable}\n\t\t\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\t\t\tshow_selection_buttons={selected_cells.length === 1 &&\n\t\t\t\t\t\t\t\t\t\tselected_cells[0][0] === 0 &&\n\t\t\t\t\t\t\t\t\t\tselected_cells[0][1] === j}\n\t\t\t\t\t\t\t\t\tcoords={selected_cell_coords}\n\t\t\t\t\t\t\t\t\ton_select_column={df_actions.handle_select_column}\n\t\t\t\t\t\t\t\t\ton_select_row={df_actions.handle_select_row}\n\t\t\t\t\t\t\t\t\t{is_dragging}\n\t\t\t\t\t\t\t\t\ton:blur={handle_blur}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t{/each}\n\t\t\t\t</tr>\n\t\t\t</tbody>\n\t\t</table>\n\t\t<Upload\n\t\t\t{upload}\n\t\t\t{stream_handler}\n\t\t\tflex={false}\n\t\t\tcenter={false}\n\t\t\tboundedheight={false}\n\t\t\tdisable_click={true}\n\t\t\t{root}\n\t\t\ton:load={({ detail }) =>\n\t\t\t\thandle_file_upload(\n\t\t\t\t\tdetail.data,\n\t\t\t\t\t(head) => {\n\t\t\t\t\t\t_headers = make_headers(\n\t\t\t\t\t\t\thead.map((h) => h ?? \"\"),\n\t\t\t\t\t\t\tcol_count,\n\t\t\t\t\t\t\tels,\n\t\t\t\t\t\t\tmake_id\n\t\t\t\t\t\t);\n\t\t\t\t\t\treturn _headers;\n\t\t\t\t\t},\n\t\t\t\t\t(vals) => {\n\t\t\t\t\t\tvalues = vals;\n\t\t\t\t\t}\n\t\t\t\t)}\n\t\t\tbind:dragging\n\t\t\taria_label={i18n(\"dataframe.drop_to_upload\")}\n\t\t>\n\t\t\t<div class=\"table-wrap\">\n\t\t\t\t<VirtualTable\n\t\t\t\t\tbind:items={search_results}\n\t\t\t\t\t{max_height}\n\t\t\t\t\tbind:actual_height={table_height}\n\t\t\t\t\tbind:table_scrollbar_width={scrollbar_width}\n\t\t\t\t\tselected={selected_index}\n\t\t\t\t\tdisable_scroll={active_cell_menu !== null ||\n\t\t\t\t\t\tactive_header_menu !== null}\n\t\t\t\t\tbind:viewport\n\t\t\t\t\tbind:show_scroll_button\n\t\t\t\t\ton:scroll_top={(_) => {}}\n\t\t\t\t>\n\t\t\t\t\t{#if label && label.length !== 0}\n\t\t\t\t\t\t<caption class=\"sr-only\">{label}</caption>\n\t\t\t\t\t{/if}\n\t\t\t\t\t<tr slot=\"thead\">\n\t\t\t\t\t\t{#if show_row_numbers}\n\t\t\t\t\t\t\t<RowNumber is_header={true} />\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{#each _headers as { value, id }, i (id)}\n\t\t\t\t\t\t\t<TableHeader\n\t\t\t\t\t\t\t\tbind:value={_headers[i].value}\n\t\t\t\t\t\t\t\t{i}\n\t\t\t\t\t\t\t\t{actual_pinned_columns}\n\t\t\t\t\t\t\t\t{header_edit}\n\t\t\t\t\t\t\t\t{selected_header}\n\t\t\t\t\t\t\t\t{headers}\n\t\t\t\t\t\t\t\t{get_cell_width}\n\t\t\t\t\t\t\t\t{handle_header_click}\n\t\t\t\t\t\t\t\t{toggle_header_menu}\n\t\t\t\t\t\t\t\t{end_header_edit}\n\t\t\t\t\t\t\t\tsort_columns={$df_state.sort_state.sort_columns}\n\t\t\t\t\t\t\t\tfilter_columns={$df_state.filter_state.filter_columns}\n\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t{max_chars}\n\t\t\t\t\t\t\t\t{editable}\n\t\t\t\t\t\t\t\tis_static={static_columns.includes(i)}\n\t\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\t\tbind:el={els[id].input}\n\t\t\t\t\t\t\t\t{col_count}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t{/each}\n\t\t\t\t\t</tr>\n\t\t\t\t\t<tr slot=\"tbody\" let:item let:index class:row-odd={index % 2 === 0}>\n\t\t\t\t\t\t{#if show_row_numbers}\n\t\t\t\t\t\t\t<RowNumber {index} />\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{#each item as { value, id }, j (id)}\n\t\t\t\t\t\t\t<TableCell\n\t\t\t\t\t\t\t\tbind:value={search_results[index][j].value}\n\t\t\t\t\t\t\t\tdisplay_value={get_cell_display_value(index, j)}\n\t\t\t\t\t\t\t\tindex={$df_state.current_search_query !== undefined &&\n\t\t\t\t\t\t\t\tfiltered_to_original_map[index] !== undefined\n\t\t\t\t\t\t\t\t\t? filtered_to_original_map[index]\n\t\t\t\t\t\t\t\t\t: index}\n\t\t\t\t\t\t\t\t{j}\n\t\t\t\t\t\t\t\t{actual_pinned_columns}\n\t\t\t\t\t\t\t\t{get_cell_width}\n\t\t\t\t\t\t\t\thandle_cell_click={(e, r, c) => handle_mouse_down(e, r, c)}\n\t\t\t\t\t\t\t\t{handle_blur}\n\t\t\t\t\t\t\t\ttoggle_cell_menu={df_actions.toggle_cell_menu}\n\t\t\t\t\t\t\t\t{is_cell_selected}\n\t\t\t\t\t\t\t\t{should_show_cell_menu}\n\t\t\t\t\t\t\t\t{selected_cells}\n\t\t\t\t\t\t\t\t{copy_flash}\n\t\t\t\t\t\t\t\t{active_cell_menu}\n\t\t\t\t\t\t\t\tstyling={search_results[index][j].styling}\n\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\tdatatype={Array.isArray(datatype) ? datatype[j] : datatype}\n\t\t\t\t\t\t\t\t{editing}\n\t\t\t\t\t\t\t\t{max_chars}\n\t\t\t\t\t\t\t\t{editable}\n\t\t\t\t\t\t\t\tis_static={static_columns.includes(j)}\n\t\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\t\t{components}\n\t\t\t\t\t\t\t\thandle_select_column={df_actions.handle_select_column}\n\t\t\t\t\t\t\t\thandle_select_row={df_actions.handle_select_row}\n\t\t\t\t\t\t\t\tbind:el={els[id]}\n\t\t\t\t\t\t\t\t{is_dragging}\n\t\t\t\t\t\t\t\t{wrap}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t{/each}\n\t\t\t\t\t</tr>\n\t\t\t\t</VirtualTable>\n\t\t\t</div>\n\t\t</Upload>\n\t\t{#if show_scroll_button}\n\t\t\t<button class=\"scroll-top-button\" on:click={scroll_to_top}>\n\t\t\t\t&uarr;\n\t\t\t</button>\n\t\t{/if}\n\t</div>\n</div>\n{#if data.length === 0 && editable && row_count[1] === \"dynamic\"}\n\t<EmptyRowButton on_click={() => add_row()} />\n{/if}\n\n{#if active_cell_menu || active_header_menu}\n\t<CellMenu\n\t\tx={active_cell_menu?.x ?? active_header_menu?.x ?? 0}\n\t\ty={active_cell_menu?.y ?? active_header_menu?.y ?? 0}\n\t\trow={active_header_menu ? -1 : active_cell_menu?.row ?? 0}\n\t\t{col_count}\n\t\t{row_count}\n\t\ton_add_row_above={() => add_row_at(active_cell_menu?.row ?? -1, \"above\")}\n\t\ton_add_row_below={() => add_row_at(active_cell_menu?.row ?? -1, \"below\")}\n\t\ton_add_column_left={() =>\n\t\t\tadd_col_at(\n\t\t\t\tactive_cell_menu?.col ?? active_header_menu?.col ?? -1,\n\t\t\t\t\"left\"\n\t\t\t)}\n\t\ton_add_column_right={() =>\n\t\t\tadd_col_at(\n\t\t\t\tactive_cell_menu?.col ?? active_header_menu?.col ?? -1,\n\t\t\t\t\"right\"\n\t\t\t)}\n\t\ton_delete_row={() => delete_row_at(active_cell_menu?.row ?? -1)}\n\t\ton_delete_col={() =>\n\t\t\tdelete_col_at(active_cell_menu?.col ?? active_header_menu?.col ?? -1)}\n\t\t{editable}\n\t\tcan_delete_rows={!active_header_menu && data.length > 1 && editable}\n\t\tcan_delete_cols={data.length > 0 && data[0]?.length > 1 && editable}\n\t\t{i18n}\n\t\ton_sort={active_header_menu\n\t\t\t? (direction) => {\n\t\t\t\t\tif (active_header_menu) {\n\t\t\t\t\t\thandle_sort(active_header_menu.col, direction);\n\t\t\t\t\t\tdf_actions.set_active_header_menu(null);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t: undefined}\n\t\ton_clear_sort={active_header_menu\n\t\t\t? () => {\n\t\t\t\t\tclear_sort();\n\t\t\t\t\tdf_actions.set_active_header_menu(null);\n\t\t\t\t}\n\t\t\t: undefined}\n\t\tsort_direction={active_header_menu\n\t\t\t? $df_state.sort_state.sort_columns.find(\n\t\t\t\t\t(item) => item.col === (active_header_menu?.col ?? -1)\n\t\t\t\t)?.direction ?? null\n\t\t\t: null}\n\t\tsort_priority={active_header_menu\n\t\t\t? $df_state.sort_state.sort_columns.findIndex(\n\t\t\t\t\t(item) => item.col === (active_header_menu?.col ?? -1)\n\t\t\t\t) + 1 || null\n\t\t\t: null}\n\t\ton_filter={active_header_menu\n\t\t\t? (datatype, filter, value) => {\n\t\t\t\t\tif (active_header_menu) {\n\t\t\t\t\t\thandle_filter(active_header_menu.col, datatype, filter, value);\n\t\t\t\t\t\tdf_actions.set_active_header_menu(null);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t: undefined}\n\t\ton_clear_filter={active_header_menu\n\t\t\t? () => {\n\t\t\t\t\tclear_filter();\n\t\t\t\t\tdf_actions.set_active_header_menu(null);\n\t\t\t\t}\n\t\t\t: undefined}\n\t\tfilter_active={active_header_menu\n\t\t\t? $df_state.filter_state.filter_columns.some(\n\t\t\t\t\t(c) => c.col === (active_header_menu?.col ?? -1)\n\t\t\t\t)\n\t\t\t: null}\n\t/>\n{/if}\n\n<style>\n\t.table-container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--size-2);\n\t\tposition: relative;\n\t}\n\n\t.table-wrap {\n\t\tposition: relative;\n\t\ttransition: 150ms;\n\t}\n\n\t.table-wrap.menu-open {\n\t\toverflow: hidden;\n\t}\n\n\t.table-wrap:focus-within {\n\t\toutline: none;\n\t}\n\n\t.table-wrap.dragging {\n\t\tcursor: crosshair !important;\n\t\tuser-select: none;\n\t}\n\n\t.table-wrap.dragging * {\n\t\tcursor: crosshair !important;\n\t\tuser-select: none;\n\t}\n\n\t.table-wrap > :global(button) {\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--table-radius);\n\t\toverflow: hidden;\n\t}\n\n\ttable {\n\t\tposition: absolute;\n\t\topacity: 0;\n\t\tz-index: -1;\n\t\ttransition: 150ms;\n\t\twidth: var(--size-full);\n\t\ttable-layout: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-md);\n\t\tfont-family: var(--font-mono);\n\t\tborder-spacing: 0;\n\t\tborder-collapse: separate;\n\t}\n\n\tthead {\n\t\tposition: sticky;\n\t\ttop: 0;\n\t\tz-index: 5;\n\t\tbox-shadow: var(--shadow-drop);\n\t}\n\n\tthead :global(th.pinned-column) {\n\t\tposition: sticky;\n\t\tz-index: 6;\n\t\tbackground: var(--table-even-background-fill) !important;\n\t}\n\n\t.dragging {\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.no-wrap {\n\t\twhite-space: nowrap;\n\t}\n\n\tdiv:not(.no-wrap) td {\n\t\toverflow-wrap: anywhere;\n\t}\n\n\tdiv.no-wrap td {\n\t\toverflow-x: hidden;\n\t}\n\n\ttr {\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\ttr.row-odd {\n\t\tbackground: var(--table-odd-background-fill);\n\t}\n\n\t.header-row {\n\t\tdisplay: flex;\n\t\tjustify-content: flex-end;\n\t\talign-items: center;\n\t\tgap: var(--size-2);\n\t\tmin-height: var(--size-6);\n\t\tflex-wrap: nowrap;\n\t\twidth: 100%;\n\t}\n\n\t.header-row .label {\n\t\tflex: 1 1 auto;\n\t\tmargin-right: auto;\n\t}\n\n\t.header-row .label p {\n\t\tmargin: 0;\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-size: var(--block-label-text-size);\n\t\tline-height: var(--line-sm);\n\t\tposition: relative;\n\t\tz-index: 4;\n\t}\n\n\t.scroll-top-button {\n\t\tposition: absolute;\n\t\tright: var(--size-4);\n\t\tbottom: var(--size-4);\n\t\twidth: var(--size-8);\n\t\theight: var(--size-8);\n\t\tborder-radius: var(--table-radius);\n\t\tbackground: var(--color-accent);\n\t\tcolor: white;\n\t\tborder: none;\n\t\tcursor: pointer;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: var(--text-lg);\n\t\tz-index: 9;\n\t\topacity: 0.5;\n\t}\n\n\t.scroll-top-button:hover {\n\t\topacity: 1;\n\t}\n\n\ttr {\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t\ttext-align: left;\n\t}\n</style>\n", "<svelte:options accessors={true} />\n\n<script context=\"module\" lang=\"ts\">\n\texport { default as BaseDataFrame } from \"./shared/Table.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { Block } from \"@gradio/atoms\";\n\timport Table from \"./shared/Table.svelte\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport type { Headers, Datatype, DataframeValue } from \"./shared/utils\";\n\timport Image from \"@gradio/image\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: DataframeValue = {\n\t\tdata: [[\"\", \"\", \"\"]],\n\t\theaders: [\"1\", \"2\", \"3\"],\n\t\tmetadata: null\n\t};\n\texport let value_is_output = false;\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\texport let row_count: [number, \"fixed\" | \"dynamic\"];\n\texport let label: string | null = null;\n\texport let show_label = true;\n\texport let wrap: boolean;\n\texport let datatype: Datatype | Datatype[];\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let root: string;\n\n\texport let line_breaks = true;\n\texport let column_widths: string[] = [];\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t\tinput: never;\n\t\tclear_status: LoadingStatus;\n\t\tsearch: string | null;\n\t}>;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let max_height: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let interactive: boolean;\n\texport let show_fullscreen_button = false;\n\texport let max_chars: number | undefined = undefined;\n\texport let show_copy_button = false;\n\texport let show_row_numbers = false;\n\texport let show_search: \"none\" | \"search\" | \"filter\" = \"none\";\n\texport let pinned_columns = 0;\n\texport let static_columns: (string | number)[] = [];\n\texport let fullscreen = false;\n</script>\n\n<Block\n\t{visible}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\tcontainer={false}\n\t{scale}\n\t{min_width}\n\toverflow_behavior=\"visible\"\n\tbind:fullscreen\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t<Table\n\t\t{root}\n\t\t{label}\n\t\t{show_label}\n\t\t{row_count}\n\t\t{col_count}\n\t\tvalues={value.data}\n\t\tdisplay_value={value.metadata?.display_value}\n\t\tstyling={value.metadata?.styling}\n\t\theaders={value.headers}\n\t\t{fullscreen}\n\t\ton:change={(e) => {\n\t\t\tvalue.data = e.detail.data;\n\t\t\tvalue.headers = e.detail.headers;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}}\n\t\ton:input={(e) => gradio.dispatch(\"input\")}\n\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\ton:fullscreen={({ detail }) => {\n\t\t\tfullscreen = detail;\n\t\t}}\n\t\t{wrap}\n\t\t{datatype}\n\t\t{latex_delimiters}\n\t\teditable={interactive}\n\t\t{max_height}\n\t\ti18n={gradio.i18n}\n\t\t{line_breaks}\n\t\t{column_widths}\n\t\tupload={(...args) => gradio.client.upload(...args)}\n\t\tstream_handler={(...args) => gradio.client.stream(...args)}\n\t\tbind:value_is_output\n\t\t{show_fullscreen_button}\n\t\t{max_chars}\n\t\t{show_copy_button}\n\t\t{show_row_numbers}\n\t\t{show_search}\n\t\t{pinned_columns}\n\t\tcomponents={{ image: Image }}\n\t\t{static_columns}\n\t/>\n</Block>\n"], "names": ["get_sort_status", "name", "sort_columns", "headers", "sort_item", "item", "col", "sort_data", "data", "row_indices", "_", "row_a_idx", "row_b_idx", "row_a", "row_b", "sort_by", "direction", "val_a", "val_b", "comparison", "i", "sort_data_and_preserve_selection", "display_value", "styling", "selected", "get_current_indices", "id", "sort_table_data", "new_selected", "j", "filter_data", "filter_columns", "column", "_a", "_b", "filter_data_and_preserve_selection", "original_data", "original_display_value", "original_styling", "filter_table_data", "get_max", "max", "indices", "new_data", "new_display", "new_styling", "base_data", "base_display_value", "base_styling", "row", "copy_table_data", "selected_cells", "csv", "r", "c", "acc", "value", "rows", "a", "b", "cols", "text", "err", "guess_delimiter", "possibleDelimiters", "weedOut", "delimiter", "cache", "checkLength", "line", "length", "data_uri_to_blob", "data_uri", "byte_str", "mime_str", "ab", "ia", "handle_file_upload", "update_headers", "update_values", "blob", "reader", "e", "head", "rest", "dsvFormat", "is_cell_in_selection", "coords", "is_cell_selected", "cell", "up", "down", "left", "right", "get_range_selection", "start", "end", "start_row", "start_col", "end_row", "end_col", "min_row", "max_row", "min_col", "max_col", "cells", "handle_selection", "current", "event", "is_cell_match", "index", "handle_delete_key", "should_show_cell_menu", "editable", "get_next_cell_coordinates", "shift_key", "next_row", "prev_row", "_c", "move_cursor", "current_coords", "key", "dir", "arr", "_acc", "_data", "k", "handle_click_outside", "parent", "trigger", "calculate_selection_positions", "els", "table", "cell_id", "cell_el", "cell_rect", "table_rect", "col_pos", "row_pos", "DATAFRAME_KEY", "create_actions", "state", "context", "update_state", "updater", "s", "add_row", "make_id", "new_row", "add_col", "new_headers", "h", "update_array", "source", "target", "query", "sort_cols", "initial_data", "datatype", "filter", "filter_cols", "get", "comp", "_d", "position", "previous_data", "previous_headers", "value_is_output", "dispatch", "current_headers", "current_data", "dequal", "original", "menu", "editing", "header_index", "button", "actual_row", "filtered_indices", "dataRow", "idx", "tick", "current_menu", "rect", "current_button", "new_button", "create_dataframe_context", "config", "writable", "instance_id", "setContext", "ctx", "attr", "button_class_value", "insert_hydration", "anchor", "append_hydration", "span", "svg", "path", "dirty", "$$props", "on_click", "click_handler", "$$invalidate", "is_first_position", "div", "on_change", "handle_change", "bool_value", "value$1", "textarea", "toggle_class", "booleancell_changes", "markdowncode_changes", "switch_value", "func", "switch_instance_changes", "create_if_block_5", "if_block2", "create_if_block", "truncate_text", "max_length", "is_image", "str", "use_focus", "node", "edit", "header", "latex_delimiters", "line_breaks", "is_static", "max_chars", "components", "i18n", "is_dragging", "wrap_text", "show_selection_buttons", "on_select_column", "on_select_row", "el", "createEventDispatcher", "handle_blur", "handle_keydown", "handle_bool_change", "new_value", "$$value", "should_truncate", "display_content", "display_text", "t_value", "td", "set_data", "t", "th", "is_header", "is_function", "touch", "mouseEvent", "path0", "path1", "size", "path2", "path3", "path4", "create_if_block_1", "create_if_block_2", "create_if_block_3", "create_if_block_4", "create_if_block_6", "create_if_block_7", "create_if_block_8", "create_if_block_9", "create_if_block_10", "icon", "if_block0", "if_block1", "th_aria_sort_value", "set_style", "div1", "div0", "editablecell_changes", "actual_pinned_columns", "header_edit", "selected_header", "get_cell_width", "handle_header_click", "toggle_header_menu", "end_header_edit", "col_count", "get_header_position", "col_index", "click_handler_1", "can_add_columns", "sort_index", "filter_index", "sort_priority", "current_direction", "editablecell_props", "td_tabindex_value", "td_data_testid_value", "handle_cell_click", "toggle_cell_menu", "copy_flash", "active_cell_menu", "handle_select_column", "handle_select_row", "wrap", "get_cell_position", "$$self", "mousedown_handler", "contextmenu_handler", "cell_classes", "is_in_selection", "has_no_top", "has_no_bottom", "has_no_left", "has_no_right", "get_key", "height", "svelte_virtual_table_viewport", "thead", "tbody", "tfoot", "items", "max_height", "actual_height", "table_scrollbar_width", "disable_scroll", "show_scroll_button", "viewport", "average_height", "bottom", "contents", "head_height", "foot_height", "height_map", "mounted", "top", "viewport_height", "visible", "viewport_box", "is_browser", "raf", "cb", "refresh_height_map", "sortedItems", "scroll_to_index", "scrollTop", "v", "y", "row_height", "content_height", "remaining", "scrollbar_height", "filtered_height_map", "scroll_and_render", "n", "is_in_view", "viewport_top", "opts", "align_end", "_itemHeight", "distance", "_opts", "onMount", "ResizeObserverSingleton", "each_value", "ensure_array_like", "t0", "t0_value", "div5", "div4", "button0", "div3", "div2", "button1", "input", "button2", "on_filter", "menu_element", "current_filter", "filter_dropdown_open", "filter_input_value", "filter_options", "position_menu", "viewport_width", "menu_rect", "x", "handle_filter_input", "click_handler_2", "opt", "click_handler_4", "t1_value", "t5_value", "t9_value", "t12_value", "t16_value", "button3", "button4", "t1", "t5", "t9", "t12", "t16", "t4_value", "t4", "on_add_row_above", "on_add_row_below", "on_add_column_left", "on_add_column_right", "row_count", "on_delete_row", "on_delete_col", "can_delete_rows", "can_delete_cols", "on_sort", "on_clear_sort", "sort_direction", "on_clear_filter", "filter_active", "active_filter_menu", "new_x", "new_y", "toggle_filter_menu", "can_add_rows", "input_value_value", "input_placeholder_value", "show_fullscreen_button", "show_copy_button", "show_search", "fullscreen", "on_copy", "on_commit_filter", "copied", "timer", "current_search_query", "input_value", "handle_search_input", "new_query", "copy_feedback", "handle_copy", "onDestroy", "make_headers", "_head", "_h", "fill", "_id", "process_data", "values", "data_binding", "display", "cast_value_to_type", "d", "save_cell_value", "old_value", "handle_cell_blur", "input_el", "handle_header_navigation", "handle_delete_operation", "static_columns", "handle_arrow_keys", "next_coords", "handle_enter_key", "handle_tab_key", "next_cell", "handle_default_key", "handle_cell_navigation", "create_drag_handlers", "set_is_dragging", "set_selected_cells", "set_selected", "show_row_numbers", "parent_element", "start_drag", "is_checkbox_click", "update_selection", "selection_range", "end_drag", "dx", "dy", "if_block", "toolbar_changes", "p", "caption", "tr", "tablecell_props", "tablecell_changes", "virtualtable_changes", "_e", "_f", "_g", "cellmenu_changes", "if_block6", "table_1", "tr0", "tr1", "upload_1_changes", "label", "show_label", "root", "column_widths", "upload", "stream_handler", "pinned_columns", "df_ctx", "df_state", "df_actions", "get_data_at", "get_column", "get_row", "init_drag_handlers", "observer", "entries", "entry", "is_visible", "width_calculated", "handle_resize", "global_mouse_up", "drag_start", "handle_mouse_up", "_headers", "old_headers", "old_val", "search_results", "dragging", "color_accent_copied", "filtered_to_original_map", "handle_sort", "clear_sort", "handle_filter", "clear_filter", "edit_header", "_select", "result", "new_w", "handle_click_outside_util", "width_calc_timeout", "last_width_data_length", "last_width_column_count", "set_cell_widths", "column_count", "$df_state", "widths", "percentage", "pixel_width", "width", "calculated_width", "table_height", "scrollbar_width", "_display_value", "_styling", "set_copy_flash", "previous_selected_cells", "blur_event", "active_header_menu", "afterUpdate", "delete_col_at", "delete_row_at", "selected_cell_coords", "commit_filter", "filtered_data", "filtered_display_values", "filtered_styling", "data_row", "display_row", "styling_row", "change_payload", "scroll_to_top", "add_row_at", "row_index", "add_col_at", "reset_sort_state", "mouse_down_pos", "drag_state", "drag_handlers", "get_cell_display_value", "search_handler", "handle_mouse_down", "load_handler", "detail", "vals", "keydown_handler", "func_3", "func_4", "func_7", "is_reset", "is_different_structure", "cell_map", "row_idx", "col_idx", "filtered", "rowIdx", "colIdx", "dtype", "selected_index", "positions", "handle_mouse_move", "Image", "table_changes", "elem_id", "elem_classes", "scale", "min_width", "gradio", "loading_status", "interactive", "clear_status_handler", "args", "func_1", "input_handler"], "mappings": "s5DAKgB,SAAAA,GACfC,EACAC,EACAC,EAC0B,CAC1B,GAAI,CAACD,EAAa,OAAe,MAAA,OAEjC,MAAME,EAAYF,EAAa,KAAMG,GAAS,CAC7C,MAAMC,EAAMD,EAAK,IACb,OAAAC,EAAM,GAAKA,GAAOH,EAAQ,OAAe,GACtCA,EAAQG,CAAG,IAAML,CAAA,CACxB,EAED,OAAKG,EACEA,EAAU,UADM,MAExB,CAEgB,SAAAG,GACfC,EACAN,EACW,CACP,GAAA,CAACM,GAAQ,CAACA,EAAK,QAAU,CAACA,EAAK,CAAC,EACnC,MAAO,GAGJ,GAAAN,EAAa,OAAS,EAAG,CAC5B,MAAMO,EAAc,CAAC,GAAG,MAAMD,EAAK,MAAM,CAAC,EAAE,IAAI,CAACE,EAAG,IAAM,CAAC,EAC/C,OAAAD,EAAA,KAAK,CAACE,EAAWC,IAAc,CACpC,MAAAC,EAAQL,EAAKG,CAAS,EACtBG,EAAQN,EAAKI,CAAS,EAE5B,SAAW,CAAE,IAAKG,EAAS,UAAAC,CAAA,IAAed,EAAc,CACvD,GACC,CAACW,GACD,CAACC,GACDC,EAAU,GACVA,GAAWF,EAAM,QACjBE,GAAWD,EAAM,QACjB,CAACD,EAAME,CAAO,GACd,CAACD,EAAMC,CAAO,EAEd,SAGK,MAAAE,EAAQJ,EAAME,CAAO,EAAE,MACvBG,EAAQJ,EAAMC,CAAO,EAAE,MACvBI,EAAaF,EAAQC,EAAQ,GAAKD,EAAQC,EAAQ,EAAI,EAE5D,GAAIC,IAAe,EACX,OAAAH,IAAc,MAAQG,EAAa,CAACA,CAE7C,CAEO,MAAA,EAAA,CACP,EACMV,CACR,CACO,MAAA,CAAC,GAAG,MAAMD,EAAK,MAAM,CAAC,EAAE,IAAI,CAACE,EAAGU,IAAMA,CAAC,CAC/C,CAEO,SAASC,GACfb,EACAc,EACAC,EACArB,EACAsB,EACAC,EAI4D,CAC5D,IAAIC,EAAK,KACLF,GAAYA,EAAS,CAAC,IAAKhB,GAAQgB,EAAS,CAAC,IAAKhB,EAAKgB,EAAS,CAAC,CAAC,IAChEE,EAAAlB,EAAKgB,EAAS,CAAC,CAAC,EAAEA,EAAS,CAAC,CAAC,EAAE,IAGrBG,GAAAnB,EAAMc,EAAeC,EAASrB,CAAY,EAE1D,IAAI0B,EAAeJ,EACnB,GAAIE,EAAI,CACP,KAAM,CAACN,EAAGS,CAAC,EAAIJ,EAAoBC,EAAIlB,CAAI,EAC5BoB,EAAA,CAACR,EAAGS,CAAC,CACrB,CAEO,MAAA,CAAE,KAAArB,EAAM,SAAUoB,EAC1B,CCtFgB,SAAAE,GACftB,EACAuB,EAMW,CACP,GAAA,CAACvB,GAAQ,CAACA,EAAK,QAAU,CAACA,EAAK,CAAC,EACnC,MAAO,GAER,IAAIC,EAAc,CAAC,GAAG,MAAMD,EAAK,MAAM,CAAC,EAAE,IAAI,CAACE,EAAG,IAAM,CAAC,EAErD,OAAAqB,EAAe,OAAS,GACZA,EAAA,QAASC,GAAW,CAC9B,GAAAA,EAAO,WAAa,SACvB,OAAQA,EAAO,OAAQ,CACtB,IAAK,WACJvB,EAAcA,EAAY,OAAQ,GACjC,OAAA,OAAAwB,EAAAzB,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAC,EAAqB,MAAM,WAAW,SAASD,EAAO,OAAK,EAE5D,MACD,IAAK,mBACJvB,EAAcA,EAAY,OACxB,GAAA,OACA,SAACwB,EAAAzB,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,MAAAC,EAAqB,MAAM,WAAW,SAASD,EAAO,QAAK,EAE9D,MACD,IAAK,cACJvB,EAAcA,EAAY,OAAQ,GACjC,OAAA,OAAAwB,EAAAzB,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAC,EAAqB,MAAM,WAAW,WAAWD,EAAO,OAAK,EAE9D,MACD,IAAK,YACJvB,EAAcA,EAAY,OAAQ,GACjC,OAAA,OAAAwB,EAAAzB,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAC,EAAqB,MAAM,WAAW,SAASD,EAAO,OAAK,EAE5D,MACD,IAAK,KACJvB,EAAcA,EAAY,OACxB,GAAM,OAAA,QAAAwB,EAAAzB,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAC,EAAqB,MAAM,cAAeD,EAAO,MAAA,EAEzD,MACD,IAAK,SACJvB,EAAcA,EAAY,OACxB,GAAM,OAAA,QAAEwB,EAAAzB,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAC,EAAqB,MAAM,cAAeD,EAAO,MAAA,EAE3D,MACD,IAAK,WACJvB,EAAcA,EAAY,OACxB,GAAM,OAAA,QAAAwB,EAAAzB,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAC,EAAqB,MAAM,cAAe,GAAA,EAElD,MACD,IAAK,eACJxB,EAAcA,EAAY,OACxB,GAAM,OAAA,QAAEwB,EAAAzB,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAC,EAAqB,MAAM,cAAe,GAAA,EAEpD,KACF,SACUD,EAAO,WAAa,SAC9B,OAAQA,EAAO,OAAQ,CACtB,IAAK,IACUvB,EAAAA,EAAY,OAAQ,GAAM,SACvC,MACC,CAAC,MAAM,QAAOwB,EAAAzB,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAC,EAAqB,KAAK,CAAC,GACzC,CAAC,MAAM,OAAOD,EAAO,KAAK,CAAC,EAG1B,QAAOE,EAAA1B,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAE,EAAqB,KAAK,IAAM,OAAOF,EAAO,KAAK,EAGrD,EAAA,CACP,EACD,MACD,IAAK,IACUvB,EAAAA,EAAY,OAAQ,GAAM,SACvC,MACC,CAAC,MAAM,QAAOwB,EAAAzB,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAC,EAAqB,KAAK,CAAC,GACzC,CAAC,MAAM,OAAOD,EAAO,KAAK,CAAC,EAG1B,QAAOE,EAAA1B,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAE,EAAqB,KAAK,IAAM,OAAOF,EAAO,KAAK,EAGrD,EAAA,CACP,EACD,MACD,IAAK,IACUvB,EAAAA,EAAY,OAAQ,GAAM,SACvC,MACC,CAAC,MAAM,QAAOwB,EAAAzB,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAC,EAAqB,KAAK,CAAC,GACzC,CAAC,MAAM,OAAOD,EAAO,KAAK,CAAC,EAG1B,QAAOE,EAAA1B,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAE,EAAqB,KAAK,EAAI,OAAOF,EAAO,KAAK,EAGnD,EAAA,CACP,EACD,MACD,IAAK,IACUvB,EAAAA,EAAY,OAAQ,GAAM,SACvC,MACC,CAAC,MAAM,QAAOwB,EAAAzB,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAC,EAAqB,KAAK,CAAC,GACzC,CAAC,MAAM,OAAOD,EAAO,KAAK,CAAC,EAG1B,QAAOE,EAAA1B,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAE,EAAqB,KAAK,EAAI,OAAOF,EAAO,KAAK,EAGnD,EAAA,CACP,EACD,MACD,IAAK,IACUvB,EAAAA,EAAY,OAAQ,GAAM,SACvC,MACC,CAAC,MAAM,QAAOwB,EAAAzB,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAC,EAAqB,KAAK,CAAC,GACzC,CAAC,MAAM,OAAOD,EAAO,KAAK,CAAC,EAG1B,QAAOE,EAAA1B,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAE,EAAqB,KAAK,GAAK,OAAOF,EAAO,KAAK,EAGpD,EAAA,CACP,EACD,MACD,IAAK,IACUvB,EAAAA,EAAY,OAAQ,GAAM,SACvC,MACC,CAAC,MAAM,QAAOwB,EAAAzB,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAC,EAAqB,KAAK,CAAC,GACzC,CAAC,MAAM,OAAOD,EAAO,KAAK,CAAC,EAG1B,QAAOE,EAAA1B,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAE,EAAqB,KAAK,GAAK,OAAOF,EAAO,KAAK,EAGpD,EAAA,CACP,EACD,MACD,IAAK,WACJvB,EAAcA,EAAY,OACxB,GAAM,OAAA,QAAAwB,EAAAzB,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAC,EAAqB,MAAM,cAAe,GAAA,EAElD,MACD,IAAK,eACUxB,EAAAA,EAAY,OAAQ,GAAM,SACnC,OAAC,MAAM,QAAOwB,EAAAzB,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAC,EAAqB,KAAK,CAAC,EAGtC,KAFGC,EAAA1B,EAAK,CAAC,EAAEwB,EAAO,GAAG,IAAlB,YAAAE,EAAqB,MAAM,cAAe,EAE7C,CACP,EACD,KACF,CACD,CACA,EACMzB,GAED,CAAC,GAAG,MAAMD,EAAK,MAAM,CAAC,EAAE,IAAI,CAACE,EAAG,IAAM,CAAC,CAC/C,CAEgB,SAAAyB,GACf3B,EACAc,EACAC,EACAQ,EAMAP,EACAC,EAIAW,EACAC,EACAC,EAC4D,CAC5D,IAAIZ,EAAK,KACLF,GAAYA,EAAS,CAAC,IAAKhB,GAAQgB,EAAS,CAAC,IAAKhB,EAAKgB,EAAS,CAAC,CAAC,IAChEE,EAAAlB,EAAKgB,EAAS,CAAC,CAAC,EAAEA,EAAS,CAAC,CAAC,EAAE,IAGrCe,GACC/B,EACAc,EACAC,EACAQ,EACAK,EACAC,EACAC,CAAA,EAGD,IAAIV,EAAeJ,EACnB,GAAIE,EAAI,CACP,KAAM,CAACN,EAAGS,CAAC,EAAIJ,EAAoBC,EAAIlB,CAAI,EAC5BoB,EAAA,CAACR,EAAGS,CAAC,CACrB,CAEO,MAAA,CAAE,KAAArB,EAAM,SAAUoB,EAC1B,CC/LO,SAASY,GAAQhC,EAA8B,CACjD,GAAA,CAACA,GAAQ,CAACA,EAAK,OAAQ,MAAO,GAClC,IAAIiC,EAAMjC,EAAK,CAAC,EAAE,MAAM,EACxB,QAASY,EAAI,EAAGA,EAAIZ,EAAK,OAAQY,IAChC,QAASS,EAAI,EAAGA,EAAIrB,EAAKY,CAAC,EAAE,OAAQS,IAC/B,GAAGY,EAAIZ,CAAC,EAAE,KAAK,GAAG,OAAS,GAAGrB,EAAKY,CAAC,EAAES,CAAC,EAAE,KAAK,GAAG,SACpDY,EAAIZ,CAAC,EAAIrB,EAAKY,CAAC,EAAES,CAAC,GAId,OAAAY,CACR,CAEO,SAASd,GACfnB,EACAc,EACAC,EACArB,EACO,CAEH,GADA,CAACA,EAAa,QACd,CAACM,GAAQ,CAACA,EAAK,OAAQ,OAErB,MAAAkC,EAAUnC,GAAUC,EAAMN,CAAY,EAEtCyC,EAAWD,EAAQ,IAAKtB,GAAcZ,EAAKY,CAAC,CAAC,EAGnD,GAFAZ,EAAK,OAAO,EAAGA,EAAK,OAAQ,GAAGmC,CAAQ,EAEnCrB,EAAe,CAClB,MAAMsB,EAAcF,EAAQ,IAAKtB,GAAcE,EAAcF,CAAC,CAAC,EAC/DE,EAAc,OAAO,EAAGA,EAAc,OAAQ,GAAGsB,CAAW,CAC7D,CAEA,GAAIrB,EAAS,CACZ,MAAMsB,EAAcH,EAAQ,IAAKtB,GAAcG,EAAQH,CAAC,CAAC,EACzDG,EAAQ,OAAO,EAAGA,EAAQ,OAAQ,GAAGsB,CAAW,CACjD,CACD,CAEO,SAASN,GACf/B,EACAc,EACAC,EACAQ,EAMAK,EACAC,EACAC,EACO,CACP,MAAMQ,EAAYV,GAAiB5B,EAC7BuC,EAAqBV,GAA0Bf,EAC/C0B,EAAeV,GAAoBf,EAErC,GAAA,CAACQ,EAAe,OAAQ,CAC3BvB,EAAK,OAAO,EAAGA,EAAK,OAAQ,GAAGsC,EAAU,IAAKG,GAAQ,CAAC,GAAGA,CAAG,CAAC,CAAC,EAC3D3B,GAAiByB,GACNzB,EAAA,OACb,EACAA,EAAc,OACd,GAAGyB,EAAmB,IAAKE,GAAQ,CAAC,GAAGA,CAAG,CAAC,CAAA,EAGzC1B,GAAWyB,GACdzB,EAAQ,OAAO,EAAGA,EAAQ,OAAQ,GAAGyB,EAAa,IAAKC,GAAQ,CAAC,GAAGA,CAAG,CAAC,CAAC,EAEzE,MACD,CACI,GAAA,CAACzC,GAAQ,CAACA,EAAK,OAAQ,OAErB,MAAAkC,EAAUZ,GAAYgB,EAAWf,CAAc,EAE/CY,EAAWD,EAAQ,IAAKtB,GAAc0B,EAAU1B,CAAC,CAAC,EAGxD,GAFAZ,EAAK,OAAO,EAAGA,EAAK,OAAQ,GAAGmC,CAAQ,EAEnCrB,GAAiByB,EAAoB,CACxC,MAAMH,EAAcF,EAAQ,IAAKtB,GAAc2B,EAAmB3B,CAAC,CAAC,EACpEE,EAAc,OAAO,EAAGA,EAAc,OAAQ,GAAGsB,CAAW,CAC7D,CAEA,GAAIrB,GAAWyB,EAAc,CAC5B,MAAMH,EAAcH,EAAQ,IAAKtB,GAAc4B,EAAa5B,CAAC,CAAC,EAC9DG,EAAQ,OAAO,EAAGA,EAAQ,OAAQ,GAAGsB,CAAW,CACjD,CACD,CAEsB,eAAAK,GACrB1C,EACA2C,EACgB,CACZ,GAAA,CAAC3C,GAAQ,CAACA,EAAK,OAAQ,OAM3B,MAAM4C,GAHLD,GACA3C,EAAK,QAAQ,CAACyC,EAAKI,IAAMJ,EAAI,IAAI,CAACvC,EAAG4C,IAAM,CAACD,EAAGC,CAAC,CAAqB,CAAC,GAE7C,OACzB,CAACC,EAAmD,CAACN,EAAK3C,CAAG,IAAM,CAClEiD,EAAIN,CAAG,EAAIM,EAAIN,CAAG,GAAK,CAAA,EACvB,MAAMO,EAAQ,OAAOhD,EAAKyC,CAAG,EAAE3C,CAAG,EAAE,KAAK,EACrC,OAAAiD,EAAAN,CAAG,EAAE3C,CAAG,EACXkD,EAAM,SAAS,GAAG,GAAKA,EAAM,SAAS,GAAG,GAAKA,EAAM,SAAS;AAAA,CAAI,EAC9D,IAAIA,EAAM,QAAQ,KAAM,IAAI,CAAC,IAC7BA,EACGD,CACR,EACA,CAAC,CAAA,EAGIE,EAAO,OAAO,KAAKL,CAAG,EAAE,KAAK,CAACM,EAAGC,IAAM,CAACD,EAAI,CAACC,CAAC,EACpD,GAAI,CAACF,EAAK,OAAQ,OAElB,MAAMG,EAAO,OAAO,KAAKR,EAAIK,EAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAACC,EAAGC,IAAM,CAACD,EAAI,CAACC,CAAC,EACvDE,EAAOJ,EACX,IAAKJ,GAAMO,EAAK,IAAKN,GAAMF,EAAIC,CAAC,EAAEC,CAAC,GAAK,EAAE,EAAE,KAAK,GAAG,CAAC,EACrD,KAAK;AAAA,CAAI,EAEP,GAAA,CACG,MAAA,UAAU,UAAU,UAAUO,CAAI,QAChCC,EAAK,CACb,MAAM,IAAI,MAAM,gCAAmCA,EAAc,OAAO,CACzE,CACD,CAGgB,SAAAC,GACfF,EACAG,EACW,CACJ,OAAAA,EAAmB,OAAOC,CAAO,EAExC,SAASA,EAAQC,EAA4B,CAC5C,IAAIC,EAAQ,GACZ,OAAON,EAAK,MAAM;AAAA,CAAI,EAAE,MAAMO,CAAW,EAEzC,SAASA,EAAYC,EAAuB,CAC3C,GAAI,CAACA,EAAa,MAAA,GAClB,IAAIC,EAASD,EAAK,MAAMH,CAAS,EAAE,OACnC,OAAIC,EAAQ,IAAWA,EAAAG,GAChBH,IAAUG,GAAUA,EAAS,CACrC,CACD,CACD,CAEO,SAASC,GAAiBC,EAAwB,CACxD,MAAMC,EAAW,KAAKD,EAAS,MAAM,GAAG,EAAE,CAAC,CAAC,EACtCE,EAAWF,EAAS,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAC5DG,EAAK,IAAI,YAAYF,EAAS,MAAM,EACpCG,EAAK,IAAI,WAAWD,CAAE,EAC5B,QAASvD,EAAI,EAAGA,EAAIqD,EAAS,OAAQrD,IACpCwD,EAAGxD,CAAC,EAAIqD,EAAS,WAAWrD,CAAC,EAEvB,OAAA,IAAI,KAAK,CAACuD,CAAE,EAAG,CAAE,KAAMD,EAAU,CACzC,CAEgB,SAAAG,GACfL,EACAM,EACAC,EACO,CACD,MAAAC,EAAOT,GAAiBC,CAAQ,EAChCS,EAAS,IAAI,WACZA,EAAA,iBAAiB,UAAYC,GAAM,OACzC,GAAI,GAACjD,EAAAiD,GAAA,YAAAA,EAAG,SAAH,MAAAjD,EAAW,SAAU,OAAOiD,EAAE,OAAO,QAAW,SAAU,OACzD,KAAA,CAAChB,CAAS,EAAIH,GAAgBmB,EAAE,OAAO,OAAQ,CAAC,IAAK,GAAI,CAAC,EAC1D,CAACC,EAAM,GAAGC,CAAI,EAAIC,GAAUnB,CAAS,EAAE,UAAUgB,EAAE,OAAO,MAAM,EACtEJ,EAAeK,CAAI,EACnBJ,EAAcK,CAAI,CAAA,CAClB,EACDH,EAAO,WAAWD,CAAI,CACvB,CCvLgB,SAAAM,GACfC,EACApC,EACU,CACJ,KAAA,CAACF,EAAK3C,CAAG,EAAIiF,EACZ,OAAApC,EAAe,KAAK,CAAC,CAACE,EAAGC,CAAC,IAAMD,IAAMJ,GAAOK,IAAMhD,CAAG,CAC9D,CAEgB,SAAAkF,GACfC,EACAtC,EACS,CACH,KAAA,CAACF,EAAK3C,CAAG,EAAImF,EACf,GAAA,CAACtC,EAAe,KAAK,CAAC,CAACE,EAAGC,CAAC,IAAMD,IAAMJ,GAAOK,IAAMhD,CAAG,EAAU,MAAA,GAErE,MAAMoF,EAAKvC,EAAe,KAAK,CAAC,CAACE,EAAGC,CAAC,IAAMD,IAAMJ,EAAM,GAAKK,IAAMhD,CAAG,EAC/DqF,EAAOxC,EAAe,KAAK,CAAC,CAACE,EAAGC,CAAC,IAAMD,IAAMJ,EAAM,GAAKK,IAAMhD,CAAG,EACjEsF,EAAOzC,EAAe,KAAK,CAAC,CAACE,EAAGC,CAAC,IAAMD,IAAMJ,GAAOK,IAAMhD,EAAM,CAAC,EACjEuF,EAAQ1C,EAAe,KAAK,CAAC,CAACE,EAAGC,CAAC,IAAMD,IAAMJ,GAAOK,IAAMhD,EAAM,CAAC,EAExE,MAAO,gBAAgBoF,EAAK,UAAY,EAAE,GAAGC,EAAO,aAAe,EAAE,GAAGC,EAAO,WAAa,EAAE,GAAGC,EAAQ,YAAc,EAAE,EAC1H,CAEgB,SAAAC,GACfC,EACAC,EACmB,CACb,KAAA,CAACC,EAAWC,CAAS,EAAIH,EACzB,CAACI,EAASC,CAAO,EAAIJ,EACrBK,EAAU,KAAK,IAAIJ,EAAWE,CAAO,EACrCG,EAAU,KAAK,IAAIL,EAAWE,CAAO,EACrCI,EAAU,KAAK,IAAIL,EAAWE,CAAO,EACrCI,EAAU,KAAK,IAAIN,EAAWE,CAAO,EAErCK,EAA0B,CAAA,EAIhCA,EAAM,KAAKV,CAAK,EAEhB,QAAS3E,EAAIiF,EAASjF,GAAKkF,EAASlF,IACnC,QAASS,EAAI0E,EAAS1E,GAAK2E,EAAS3E,IAC/BT,IAAM6E,GAAapE,IAAMqE,GAC7BO,EAAM,KAAK,CAACrF,EAAGS,CAAC,CAAC,EAGZ,OAAA4E,CACR,CAEgB,SAAAC,GACfC,EACAxD,EACAyD,EACmB,CACnB,GAAIA,EAAM,UAAYzD,EAAe,OAAS,EACtC,OAAA2C,GACN3C,EAAeA,EAAe,OAAS,CAAC,EACxCwD,CAAA,EAIE,GAAAC,EAAM,SAAWA,EAAM,QAAS,CACnC,MAAMC,EAAgB,CAAC,CAACxD,EAAGC,CAAC,IAC3BD,IAAMsD,EAAQ,CAAC,GAAKrD,IAAMqD,EAAQ,CAAC,EAC9BG,EAAQ3D,EAAe,UAAU0D,CAAa,EACpD,OAAOC,IAAU,GACd,CAAC,GAAG3D,EAAgBwD,CAAO,EAC3BxD,EAAe,OAAO,CAACzC,EAAGU,IAAMA,IAAM0F,CAAK,CAC/C,CAEA,MAAO,CAACH,CAAO,CAChB,CAEgB,SAAAI,GACfvG,EACA2C,EACe,CACT,MAAAR,EAAWnC,EAAK,IAAKyC,GAAQ,CAAC,GAAGA,CAAG,CAAC,EAC3C,OAAAE,EAAe,QAAQ,CAAC,CAACF,EAAK3C,CAAG,IAAM,CAClCqC,EAASM,CAAG,GAAKN,EAASM,CAAG,EAAE3C,CAAG,IACrCqC,EAASM,CAAG,EAAE3C,CAAG,EAAI,CAAE,GAAGqC,EAASM,CAAG,EAAE3C,CAAG,EAAG,MAAO,EAAG,EACzD,CACA,EACMqC,CACR,CAEgB,SAAAqE,GACfvB,EACAtC,EACA8D,EACU,CACJ,KAAA,CAAChE,EAAK3C,CAAG,EAAImF,EACnB,OACCwB,GACA9D,EAAe,SAAW,GAC1BA,EAAe,CAAC,EAAE,CAAC,IAAMF,GACzBE,EAAe,CAAC,EAAE,CAAC,IAAM7C,CAE3B,CAEgB,SAAA4G,GACfP,EACAnG,EACA2G,EACyB,WACnB,KAAA,CAAClE,EAAK3C,CAAG,EAAIqG,EACb3F,EAAYmG,EAAY,GAAK,EAEnC,IAAIlF,EAAAzB,EAAKyC,CAAG,IAAR,MAAAhB,EAAY3B,EAAMU,GACd,MAAA,CAACiC,EAAK3C,EAAMU,CAAS,EAG7B,MAAMoG,EAAWnE,GAAOjC,EAAY,EAAI,EAAI,GACtCqG,EAAWpE,GAAOjC,EAAY,EAAI,GAAK,GAE7C,OAAIA,EAAY,KAAKkB,EAAA1B,EAAK4G,CAAQ,IAAb,MAAAlF,EAAiB,IAC9B,CAACkF,EAAU,CAAC,EAGhBpG,EAAY,KAAKsG,EAAA9G,EAAK6G,CAAQ,IAAb,MAAAC,EAAiB9G,EAAK,CAAC,EAAE,OAAS,IAC/C,CAAC6G,EAAU7G,EAAK,CAAC,EAAE,OAAS,CAAC,EAG9B,EACR,CAEgB,SAAA+G,GACfX,EACAY,EACAhH,EACyB,OACzB,MAAMiH,EAAMb,EAAM,IACZc,EAAM,CACX,WAAY,CAAC,EAAG,CAAC,EACjB,UAAW,CAAC,EAAG,EAAE,EACjB,UAAW,CAAC,EAAG,CAAC,EAChB,QAAS,CAAC,GAAI,CAAC,GACdD,CAAG,EAEL,IAAIrG,EAAGS,EACH,GAAA+E,EAAM,SAAWA,EAAM,QAC1B,GAAIa,IAAQ,aACXrG,EAAIoG,EAAe,CAAC,EAChB3F,EAAArB,EAAK,CAAC,EAAE,OAAS,UACXiH,IAAQ,YAClBrG,EAAIoG,EAAe,CAAC,EAChB3F,EAAA,UACM4F,IAAQ,YAClBrG,EAAIZ,EAAK,OAAS,EAClBqB,EAAI2F,EAAe,CAAC,UACVC,IAAQ,UACdrG,EAAA,EACJS,EAAI2F,EAAe,CAAC,MAEb,OAAA,QAGRpG,EAAIoG,EAAe,CAAC,EAAIE,EAAI,CAAC,EAC7B7F,EAAI2F,EAAe,CAAC,EAAIE,EAAI,CAAC,EAG1B,OAAAtG,EAAI,GAAKS,GAAK,EACV,KAGQI,EAAAzB,EAAKY,CAAC,IAAN,YAAAa,EAAUJ,IAElB,CAACT,EAAGS,CAAC,EAEN,EACR,CAEgB,SAAAJ,GACfC,EACAlB,EACmB,CACnB,OAAOA,EAAK,OACX,CAAC+C,EAAKoE,EAAK,IAAM,CAChB,MAAM9F,EAAI8F,EAAI,OACb,CAACC,EAAMC,EAAOC,IAAOpG,IAAOmG,EAAM,GAAKC,EAAIF,EAC3C,EAAA,EAED,OAAO/F,IAAM,GAAK0B,EAAM,CAAC,EAAG1B,CAAC,CAC9B,EACA,CAAC,GAAI,EAAE,CAAA,CAET,CAEgB,SAAAkG,GACfnB,EACAoB,EACU,CACV,KAAM,CAACC,CAAO,EAAIrB,EAAM,aAAa,EAC9B,MAAA,CAACoB,EAAO,SAASC,CAAO,CAChC,CAUO,SAASC,GACf1G,EACAhB,EACA2H,EACAH,EACAI,EACmD,SAC7C,KAAA,CAACnF,EAAK3C,CAAG,EAAIkB,EACnB,GAAI,GAACS,EAAAzB,EAAKyC,CAAG,IAAR,MAAAhB,EAAY3B,IAChB,MAAO,CAAE,QAAS,MAAO,QAAS,MAAU,EAG7C,MAAM+H,EAAU7H,EAAKyC,CAAG,EAAE3C,CAAG,EAAE,GACzBgI,GAAUpG,EAAAiG,EAAIE,CAAO,IAAX,YAAAnG,EAAc,KAE9B,GAAI,CAACoG,EACJ,MAAO,CAAE,QAAS,MAAO,QAAS,MAAU,EAGvC,MAAAC,EAAYD,EAAQ,wBACpBE,EAAaJ,EAAM,wBACnBK,EAAU,GAAGF,EAAU,KAAOC,EAAW,KAAOD,EAAU,MAAQ,CAAC,KACnEG,EAAU,GAAGH,EAAU,IAAMC,EAAW,IAAMD,EAAU,OAAS,CAAC,KAEjE,MAAA,CAAE,QAAAE,EAAS,QAAAC,EACnB,CC7Na,MAAAC,GAAgB,OAAO,WAAW,EAmL/C,SAASC,GACRC,EACAC,EACmB,CACnB,MAAMC,EACLC,GACUH,EAAM,OAAQI,IAAO,CAAE,GAAGA,EAAG,GAAGD,EAAQC,CAAC,GAAI,EAElDC,EAAU,CACf1I,EACA2I,EACArC,IACa,OACb,MAAMsC,GAAUnH,EAAAzB,EAAK,CAAC,IAAN,MAAAyB,EAAS,OACtB,MAAMzB,EAAK,CAAC,EAAE,MAAM,EACnB,KAAK,IAAI,EACT,IAAI,KAAO,CAAE,MAAO,GAAI,GAAI2I,KAAY,EACzC,CAAC,CAAE,MAAO,GAAI,GAAIA,EAAA,CAAW,CAAA,EAC1BxG,EAAW,CAAC,GAAGnC,CAAI,EACf,OAAAsG,IAAA,OACPnE,EAAS,OAAOmE,EAAO,EAAGsC,CAAO,EACjCzG,EAAS,KAAKyG,CAAO,EACjBzG,CAAA,EAGF0G,EAAU,CACf7I,EACAL,EACAgJ,EACArC,IAC0C,CACpC,MAAAwC,EAAcR,EAAQ,QACzB,CAAC,GAAG3I,EAAQ,IAAKoJ,GAAMT,EAAQ,QAAS3I,EAAQ,QAAQoJ,CAAC,CAAC,EAAE,KAAK,CAAC,EAClE,CAAC,GAAGpJ,EAAS,UAAUA,EAAQ,OAAS,CAAC,EAAE,EACxCwC,EAAWnC,EAAK,IAAKyC,GAAQ,CAAC,GAAGA,EAAK,CAAE,MAAO,GAAI,GAAIkG,EAAQ,CAAA,CAAG,CAAC,EACzE,OAAIrC,IAAU,SACbwC,EAAY,OAAOxC,EAAO,EAAGwC,EAAY,KAAM,EACtC3G,EAAA,QAASM,GAAQA,EAAI,OAAO6D,EAAO,EAAG7D,EAAI,IAAI,CAAE,CAAC,GAEpD,CAAE,KAAMN,EAAU,QAAS2G,CAAY,CAAA,EAGzCE,EAAe,CACpBC,EACAC,IACU,CACND,GAAUC,GACNA,EAAA,OAAO,EAAGA,EAAO,OAAQ,GAAG,KAAK,MAAM,KAAK,UAAUD,CAAM,CAAC,CAAC,CACtE,EAGM,MAAA,CACN,cAAgBE,GACfZ,EAAcE,IAAO,CAAE,qBAAsBU,CAAA,EAAQ,EACtD,YAAa,CAACrJ,EAAKU,IAClB+H,EAAcE,GAAM,CACb,MAAAW,EAAYX,EAAE,WAAW,aAAa,OAC1C3F,GAAMA,EAAE,MAAQhD,CAAA,EAGhB2I,EAAE,WAAW,aAAa,KACzB3F,GAAMA,EAAE,MAAQhD,GAAOgD,EAAE,YAActC,CAAA,GAGzC4I,EAAU,KAAK,CAAE,IAAAtJ,EAAK,UAAAU,CAAW,CAAA,EAG5B,MAAA6I,EACLZ,EAAE,WAAW,eACZH,EAAQ,MAAQc,EAAU,OAAS,EACjC,CACA,KAAM,KAAK,MAAM,KAAK,UAAUd,EAAQ,IAAI,CAAC,EAC7C,cAAeA,EAAQ,cACpB,KAAK,MAAM,KAAK,UAAUA,EAAQ,aAAa,CAAC,EAChD,KACH,QAASA,EAAQ,QACd,KAAK,MAAM,KAAK,UAAUA,EAAQ,OAAO,CAAC,EAC1C,IAEH,EAAA,MAEG,MAAA,CACN,WAAY,CACX,GAAGG,EAAE,WACL,aAAcW,EAAU,MAAM,EAAE,EAChC,aAAAC,CACD,CAAA,CACD,CACA,EACF,cAAe,CAACvJ,EAAKwJ,EAAUC,EAAQvG,IACtCuF,EAAcE,GAAM,CACb,MAAAe,EAAcf,EAAE,aAAa,eAAe,KAChD3F,GAAMA,EAAE,MAAQhD,CAAA,EAEf2I,EAAE,aAAa,eAAe,OAAQ3F,GAAMA,EAAE,MAAQhD,CAAG,EACzD,CACA,GAAG2I,EAAE,aAAa,eAClB,CAAE,IAAA3I,EAAK,SAAAwJ,EAAU,OAAAC,EAAQ,MAAAvG,CAAM,CAAA,EAG5BqG,EACLZ,EAAE,aAAa,eACdH,EAAQ,MAAQkB,EAAY,OAAS,EACnC,CACA,KAAM,KAAK,MAAM,KAAK,UAAUlB,EAAQ,IAAI,CAAC,EAC7C,cAAeA,EAAQ,cACpB,KAAK,MAAM,KAAK,UAAUA,EAAQ,aAAa,CAAC,EAChD,KACH,QAASA,EAAQ,QACd,KAAK,MAAM,KAAK,UAAUA,EAAQ,OAAO,CAAC,EAC1C,IAEH,EAAA,MAEG,MAAA,CACN,aAAc,CACb,GAAGG,EAAE,aACL,eAAgBe,EAChB,aAAAH,CACD,CAAA,CACD,CACA,EACF,gBAAiB,CAAC5J,EAAME,IAAY,CAE7B,MAAAC,EADI6J,GAAIpB,CAAK,EACC,WAAW,aAAa,KAC1CxI,GAASF,EAAQE,EAAK,GAAG,IAAMJ,CAAA,EAE1B,OAAAG,EAAYA,EAAU,UAAY,MAC1C,EACA,UAAW,CAACI,EAAMc,EAAeC,IAAY,CACtC,KAAA,CACL,WAAY,CAAE,aAAArB,CAAa,CAAA,EACxB+J,GAAIpB,CAAK,EACT3I,EAAa,QACAyB,GAAAnB,EAAMc,EAAeC,EAASrB,CAAY,CAC5D,EACA,iBAAmBM,GAClBuI,EAAcE,IAAO,CACpB,WAAY,CACX,GAAGA,EAAE,WACL,UACCA,EAAE,WAAW,aAAa,QAAUzI,EAAK,CAAC,EACvC,CAAC,GAAG,MAAMA,EAAK,MAAM,CAAC,EACrB,IAAI,CAAC,EAAGY,IAAMA,CAAC,EACf,KAAK,CAACsC,EAAGC,IAAM,aACf,SAAW,CAAE,IAAArD,EAAK,UAAAU,CAAe,IAAAiI,EAAE,WACjC,aAAc,CACf,MAAMiB,KACJhI,GAAAD,EAAAzB,EAAKkD,CAAC,IAAN,YAAAzB,EAAU3B,KAAV,YAAA4B,EAAgB,QAAS,OACzBiI,GAAA7C,EAAA9G,EAAKmD,CAAC,IAAN,YAAA2D,EAAUhH,KAAV,YAAA6J,EAAgB,QAAS,IACvB,GACA,EACA,GAAAD,EAAa,OAAAlJ,IAAc,MAAQkJ,EAAO,CAACA,CAChD,CACO,MAAA,EACP,CAAA,EACD,CAAC,GAAG,MAAM1J,EAAK,MAAM,CAAC,EAAE,IAAI,CAAC,EAAGY,IAAMA,CAAC,CAC5C,CAAA,EACC,EACH,YAAcZ,GAAS,OACtB,MAAMmJ,GAAQ1H,EAAAgI,GAAIpB,CAAK,EAAE,uBAAX,YAAA5G,EAAiC,cAC/C,OAAO0H,EACJnJ,EAAK,OAAQyC,GACbA,EAAI,KAAMwC,GACT,OAAOA,GAAA,YAAAA,EAAM,KAAK,EAAE,YAAc,EAAA,SAASkE,CAAK,CACjD,CAEA,EAAAnJ,CACJ,EACA,QAAA0I,EACA,QAAAG,EACA,WAAY,CAAC7I,EAAMsG,EAAOsD,EAAUjB,IACnCD,EAAQ1I,EAAM2I,EAASiB,IAAa,QAAUtD,EAAQA,EAAQ,CAAC,EAChE,WAAY,CAACtG,EAAML,EAAS2G,EAAOsD,EAAUjB,IAC5CE,EAAQ7I,EAAML,EAASgJ,EAASiB,IAAa,OAAStD,EAAQA,EAAQ,CAAC,EACxE,WAAY,CAACtG,EAAMsG,IAClBtG,EAAK,OAAS,EAAIA,EAAK,OAAO,CAAC,EAAGY,IAAMA,IAAM0F,CAAK,EAAItG,EACxD,WAAY,CAACA,EAAML,EAAS2G,IAC3B3G,EAAQ,OAAS,EACd,CACA,KAAMK,EAAK,IAAKyC,GAAQA,EAAI,OAAO,CAACvC,EAAGU,IAAMA,IAAM0F,CAAK,CAAC,EACzD,QAAS3G,EAAQ,OAAO,CAACO,EAAGU,IAAMA,IAAM0F,CAAK,CAAA,EAE7C,CAAE,KAAAtG,EAAM,QAAAL,CAAQ,EACpB,cAAe,CAACK,EAAMsG,IACrBtG,EAAK,OAAS,EACX,CAAC,GAAGA,EAAK,MAAM,EAAGsG,CAAK,EAAG,GAAGtG,EAAK,MAAMsG,EAAQ,CAAC,CAAC,EAClDtG,EACJ,cAAe,CAACA,EAAML,EAAS2G,IAC9B3G,EAAQ,OAAS,EACd,CACA,KAAMK,EAAK,IAAKyC,GAAQ,CACvB,GAAGA,EAAI,MAAM,EAAG6D,CAAK,EACrB,GAAG7D,EAAI,MAAM6D,EAAQ,CAAC,CAAA,CACtB,EACD,QAAS,CAAC,GAAG3G,EAAQ,MAAM,EAAG2G,CAAK,EAAG,GAAG3G,EAAQ,MAAM2G,EAAQ,CAAC,CAAC,CAAA,EAEjE,CAAE,KAAAtG,EAAM,QAAAL,CAAQ,EACpB,eAAgB,MACfK,EACAL,EACAkK,EACAC,EACAC,EACAC,IACI,CAEJ,GADUP,GAAIpB,CAAK,EACb,qBAAsB,OAE5B,MAAM4B,EAAkBtK,EAAQ,IAAKoJ,GAAMA,EAAE,KAAK,EAC5CmB,EAAelK,EAAK,IAAKyC,GAC9BA,EAAI,IAAKwC,GAAS,OAAOA,EAAK,KAAK,CAAC,CAAA,GAIpC,CAACkF,GAAOD,EAAcL,CAAa,GACnC,CAACM,GAAOF,EAAiBH,CAAgB,KAEpCK,GAAOF,EAAiBH,CAAgB,GAC5CvB,EAAcE,IAAO,CACpB,WAAY,CAAE,aAAc,GAAI,UAAW,CAAI,EAAA,aAAc,IAAK,EAClE,aAAc,CAAE,eAAgB,GAAI,aAAc,IAAK,CACtD,EAAA,EAEHuB,EAAS,SAAU,CAClB,KAAMhK,EAAK,IAAKyC,GAAQA,EAAI,IAAKwC,GAASA,EAAK,KAAK,CAAC,EACrD,QAASgF,EACT,SAAU,IAAA,CACV,EACIF,GAAiBC,EAAS,OAAO,EAExC,EACA,iBAAkB,IACjBzB,EAAcE,GAAM,CACnB,GAAIA,EAAE,WAAW,cAAgBH,EAAQ,KAAM,CACxC,MAAA8B,EAAW3B,EAAE,WAAW,aAEjBO,EAAAoB,EAAS,KAAM9B,EAAQ,IAAI,EAC3BU,EAAAoB,EAAS,cAAe9B,EAAQ,aAAa,EAC7CU,EAAAoB,EAAS,QAAS9B,EAAQ,OAAO,CAC/C,CAEO,MAAA,CACN,WAAY,CAAE,aAAc,GAAI,UAAW,CAAI,EAAA,aAAc,IAAK,CAAA,CACnE,CACA,EACF,mBAAoB,IACnBC,EAAcE,GAAM,CACnB,GAAIA,EAAE,aAAa,cAAgBH,EAAQ,KAAM,CAC1C,MAAA8B,EAAW3B,EAAE,aAAa,aAEnBO,EAAAoB,EAAS,KAAM9B,EAAQ,IAAI,EAC3BU,EAAAoB,EAAS,cAAe9B,EAAQ,aAAa,EAC7CU,EAAAoB,EAAS,QAAS9B,EAAQ,OAAO,CAC/C,CAEO,MAAA,CACN,aAAc,CAAE,eAAgB,GAAI,aAAc,IAAK,CAAA,CACxD,CACA,EACF,qBAAuB+B,GACtB9B,EAAcE,IAAO,CACpB,SAAU,CAAE,GAAGA,EAAE,SAAU,iBAAkB4B,CAAK,CAAA,EACjD,EACH,uBAAyBA,GACxB9B,EAAcE,IAAO,CACpB,SAAU,CAAE,GAAGA,EAAE,SAAU,mBAAoB4B,CAAK,CAAA,EACnD,EACH,mBAAqBpE,GACpBsC,EAAcE,IAAO,CACpB,SAAU,CAAE,GAAGA,EAAE,SAAU,eAAgBxC,CAAM,CAAA,EAChD,EACH,aAAejF,GACduH,EAAcE,IAAO,CAAE,SAAU,CAAE,GAAGA,EAAE,SAAU,SAAAzH,CAAA,CAAa,EAAA,EAChE,YAAcsJ,GACb/B,EAAcE,IAAO,CAAE,SAAU,CAAE,GAAGA,EAAE,SAAU,QAAA6B,CAAA,CAAY,EAAA,EAC/D,eAAgB,IACf/B,EAAcE,IAAO,CACpB,SAAU,CACT,iBAAkB,KAClB,mBAAoB,KACpB,eAAgB,CAAC,EACjB,SAAU,GACV,QAAS,GACT,YAAa,GACb,gBAAiB,GACjB,cAAe,KACf,WAAY,EACb,CAAA,EACC,EACH,gBAAkB8B,GACjBhC,EAAcE,IAAO,CACpB,SAAU,CACT,GAAGA,EAAE,SACL,eAAgB,CAAC,EACjB,gBAAiB8B,EACjB,YAAaA,CACd,CAAA,EACC,EACH,oBAAsBA,GACrBhC,EAAcE,IAAO,CACpB,SAAU,CACT,GAAGA,EAAE,SACL,gBAAiB8B,EACjB,SAAU,GACV,eAAgB,CAAC,CAClB,CAAA,EACC,EACH,oBAAqB,CAACzK,EAAK2G,IAC1B8B,EAAcE,IAAO,CACpB,SAAU,CACT,GAAGA,EAAE,SACL,iBAAkB,KAClB,mBAAoB,KACpB,SAAU,GACV,eAAgB,CAAC,EACjB,gBAAiB3I,EACjB,YAAa2G,EAAW3G,EAAM,EAC/B,CAAA,EACC,EACH,gBAAkBmH,GAAQ,CACrB,CAAC,SAAU,QAAS,KAAK,EAAE,SAASA,CAAG,GAC1CsB,EAAcE,IAAO,CACpB,SAAU,CAAE,GAAGA,EAAE,SAAU,SAAU,GAAO,YAAa,EAAM,CAC9D,EAAA,CAEJ,EACA,mBAAoB,IAAMgB,GAAIpB,CAAK,EAAE,SAAS,eAC9C,qBAAsB,IAAMoB,GAAIpB,CAAK,EAAE,SAAS,iBAChD,kBAAmB,IAAMoB,GAAIpB,CAAK,EAAE,SAAS,cAC7C,kBAAoBmC,GACnBjC,EAAcE,IAAO,CACpB,SAAU,CAAE,GAAGA,EAAE,SAAU,cAAe+B,CAAO,CAAA,EAChD,EACH,eAAiBxH,GAChBuF,EAAcE,IAAO,CAAE,SAAU,CAAE,GAAGA,EAAE,SAAU,WAAYzF,CAAA,CAAU,EAAA,EACzE,kBAAmB,CAACoD,EAAO3D,EAAK3C,IAAQ,OACvCsG,EAAM,eAAe,EACrBA,EAAM,gBAAgB,EAEhB,MAAAqC,EAAIgB,GAAIpB,CAAK,EACf,GAAAI,EAAE,OAAO,kBAAoB3I,IAAQ,GAAI,OAE7C,IAAI2K,EAAahI,EACb,GAAAgG,EAAE,sBAAwBH,EAAQ,KAAM,CAC3C,MAAMoC,EAA6B,CAAA,EACnCpC,EAAQ,KAAK,QAAQ,CAACqC,EAASC,IAAQ,CAErCD,EAAQ,KAAM1F,GACb,OAAA,cAAOA,GAAA,YAAAA,EAAM,KAAK,EAChB,cACA,WAASxD,EAAAgH,EAAE,uBAAF,YAAAhH,EAAwB,gBAAiB,EAAE,EAAA,GAGvDiJ,EAAiB,KAAKE,CAAG,CAC1B,CACA,EACYH,EAAAC,EAAiBjI,CAAG,GAAKA,CACvC,CAEA,MAAMwD,EAAQC,GACb,CAACuE,EAAY3K,CAAG,EAChB2I,EAAE,SAAS,eACXrC,CAAA,EAEDmC,EAAcE,IAAO,CACpB,SAAU,CACT,GAAGA,EAAE,SACL,iBAAkB,KAClB,mBAAoB,KACpB,gBAAiB,GACjB,YAAa,GACb,eAAgBxC,EAChB,SAAUA,EAAM,CAAC,CAClB,CACC,EAAA,EAEEwC,EAAE,OAAO,UAAYxC,EAAM,SAAW,GACzCsC,EAAcE,IAAO,CACpB,SAAU,CAAE,GAAGA,EAAE,SAAU,QAAS,CAACgC,EAAY3K,CAAG,CAAE,CACrD,EAAA,EACF+K,GAAO,EAAA,KAAK,IAAA,SACX,OAAAnJ,GAAAD,EAAA6G,EAAQ,IAAKA,EAAQ,KAAMmC,CAAU,EAAE3K,CAAG,EAAE,EAAE,IAA9C,YAAA2B,EAAiD,QAAjD,YAAAC,EAAwD,QAAM,GAI1DmJ,GAAA,EAAE,KAAK,IAAM,CACbvC,EAAQ,gBACXA,EAAQ,eAAe,OACxB,CACA,GAGF7G,EAAA6G,EAAQ,WAAR,MAAA7G,EAAA,KAAA6G,EAAmB,SAAU,CAC5B,MAAO,CAACmC,EAAY3K,CAAG,EACvB,UAAWwI,EAAQ,WAAYxI,CAAG,EAClC,UAAWwI,EAAQ,QAASmC,CAAU,EACtC,MAAOnC,EAAQ,YAAamC,EAAY3K,CAAG,CAAA,EAE7C,EACA,iBAAkB,CAACsG,EAAO3D,EAAK3C,IAAQ,CACtCsG,EAAM,gBAAgB,EACtB,MAAM0E,EAAerB,GAAIpB,CAAK,EAAE,SAAS,iBACzC,IAAIyC,GAAA,YAAAA,EAAc,OAAQrI,GAAOqI,EAAa,MAAQhL,EACrDyI,EAAcE,IAAO,CACpB,SAAU,CAAE,GAAGA,EAAE,SAAU,iBAAkB,IAAK,CACjD,EAAA,MACI,CACN,MAAMxD,EAAQmB,EAAM,OAAuB,QAAQ,IAAI,EACvD,GAAInB,EAAM,CACH,MAAA8F,EAAO9F,EAAK,wBAClBsD,EAAcE,IAAO,CACpB,SAAU,CACT,GAAGA,EAAE,SACL,iBAAkB,CAAE,IAAAhG,EAAK,IAAA3C,EAAK,EAAGiL,EAAK,MAAO,EAAGA,EAAK,MAAO,CAC7D,CACC,EAAA,CACH,CACD,CACD,EACA,mBAAoB,CAACtI,EAAK3C,IAAQ,CACjC,MAAMkL,EAAiBvB,GAAIpB,CAAK,EAAE,SAAS,cACrC4C,GACLD,GAAA,YAAAA,EAAgB,QAAS,QACzBA,EAAe,MAAQvI,GACvBuI,EAAe,MAAQlL,EACpB,KACA,CAAE,KAAM,OAAiB,IAAA2C,EAAK,IAAA3C,GAClCyI,EAAcE,IAAO,CACpB,SAAU,CAAE,GAAGA,EAAE,SAAU,cAAewC,CAAW,CACpD,EAAA,CACH,EACA,qBAAuBnL,GAAQ,CAC9B,GAAI,CAACwI,EAAQ,KAAM,OACb,MAAArC,EAAQqC,EAAQ,KAAK,IAAI,CAAC,EAAG7F,IAAQ,CAACA,EAAK3C,CAAG,CAAmB,EACvEyI,EAAcE,IAAO,CACpB,SAAU,CACT,GAAGA,EAAE,SACL,eAAgBxC,EAChB,SAAUA,EAAM,CAAC,EACjB,QAAS,EACV,CACC,EAAA,EACF,WAAW,IAAM,OAAA,OAAAxE,EAAA6G,EAAQ,iBAAR,YAAA7G,EAAwB,SAAS,CAAC,CACpD,EACA,kBAAoBgB,GAAQ,CAC3B,GAAI,CAAC6F,EAAQ,MAAQ,CAACA,EAAQ,KAAK,CAAC,EAAG,OACvC,MAAMrC,EAAQqC,EAAQ,KAAK,CAAC,EAAE,IAC7B,CAAC,EAAGxI,IAAQ,CAAC2C,EAAK3C,CAAG,CAAA,EAEtByI,EAAcE,IAAO,CACpB,SAAU,CACT,GAAGA,EAAE,SACL,eAAgBxC,EAChB,SAAUA,EAAM,CAAC,EACjB,QAAS,EACV,CACC,EAAA,EACF,WAAW,IAAM,OAAA,OAAAxE,EAAA6G,EAAQ,iBAAR,YAAA7G,EAAwB,SAAS,CAAC,CACpD,EACA,0BAAAiF,GACA,oBAAApB,GACA,YAAAyB,EAAA,CAEF,CAEO,SAASmE,GACfC,EACmB,CACnB,MAAM9C,EAAQ+C,GAAyB,CACtC,OAAAD,EACA,qBAAsB,KACtB,WAAY,CAAE,aAAc,GAAI,UAAW,CAAI,EAAA,aAAc,IAAK,EAClE,aAAc,CAAE,eAAgB,GAAI,aAAc,IAAK,EACvD,SAAU,CACT,iBAAkB,KAClB,mBAAoB,KACpB,eAAgB,CAAC,EACjB,SAAU,GACV,QAAS,GACT,YAAa,GACb,gBAAiB,GACjB,cAAe,KACf,WAAY,EACb,CAAA,CACA,EAEK7C,EAA4B,CAAE,MAAAD,EAAO,QAAS,IAAY,EACxDC,EAAA,QAAUF,GAAeC,EAAOC,CAAO,EAE/C,MAAM+C,EAAc,OACnB,aAAa,KAAK,SAAS,SAAS,EAAE,EAAE,UAAU,CAAC,CAAC,EAAA,EAErD,OAAAC,GAAWD,EAAa/C,CAAO,EAC/BgD,GAAWnD,GAAe,CAAE,YAAAkD,EAAa,QAAA/C,CAAS,CAAA,EAE3CA,CACR,2dCppBeiD,EAAS,CAAA,CAAA,sHAJVA,EAAS,CAAA,CAAA,EAAA,iBAAA,EANoBC,EAAAhB,EAAA,QAAAiB,EAAA,qCAAAF,EAAW,CAAA,EAAA,KAAAA,EAAA,CAAA,UAC1CA,EAAS,CAAA,CAAA,GACjB,IAAE,iBAAA,+BAEiBA,EAAQ,CAAA,CAAA,EAAA,UAL/BG,EAeQxC,EAAAsB,EAAAmB,CAAA,EARPC,EAOMpB,EAAAqB,CAAA,EANLD,EAKKC,EAAAC,CAAA,EAJJF,EAGCE,EAAAC,CAAA,oEADWR,EAAS,CAAA,CAAA,iBAJVA,EAAS,CAAA,CAAA,EAAA,mCANoBS,EAAA,IAAAP,KAAAA,EAAA,qCAAAF,EAAW,CAAA,EAAA,KAAAA,EAAA,CAAA,UAC1CA,EAAS,CAAA,CAAA,GACjB,IAAE,yDAEiBA,EAAQ,CAAA,CAAA,uFArBnB,CAAA,SAAA3B,CAAA,EAAAqC,EACA,CAAA,OAAAlH,CAAA,EAAAkH,GACA,SAAAC,EAAgC,IAAA,EAAAD,EAkBX,MAAAE,EAAA,IAAAD,GAAYA,0JAhB5CE,EAAA,EAAGC,EACFzC,IAAa,SAAW7E,EAAO,CAAC,IAAM,EAAIA,EAAO,CAAC,IAAM,CAAA,gBACzDqH,EAAA,EAAG5L,EACFoJ,IAAa,SACVyC,EACC,OACA,KACDA,EACC,QACA,MAAA,+LCOSd,EAAQ,CAAA,UAFTA,EAAU,CAAA,IAAA,iBAAVA,EAAU,CAAA,mEAGXA,EAAa,CAAA,CAAA,mPAL1BG,EAOKxC,EAAAoD,EAAAX,CAAA,6DAHUJ,EAAQ,CAAA,0BAFTA,EAAU,CAAA,2IAhBZ,MAAAvI,EAA0B,EAAA,EAAAiJ,GAC1B,SAAAxF,EAAW,EAAA,EAAAwF,EACX,CAAA,UAAAM,CAAA,EAAAN,WAKFO,EAAcpG,EAAA,CAClBK,GACH8F,EAAUnG,EAAM,MAAM,gBAOXqG,EAAUC,qKAZvBN,EAAA,EAAGK,SACKzJ,GAAU,SAAWA,EAAM,YAAkB,IAAA,OAAW,CAAA,CAAAA,CAAA,4SCiGrDuI,EAAS,CAAA,sBACJA,EAAS,CAAA,CAAA,qBACZA,EAAS,CAAA,EAAG,oBAAsB,WAAW,8EAH1DG,EAaCxC,EAAAyD,EAAAhB,CAAA,6DALSJ,EAAW,EAAA,CAAA,yFAIRA,EAAc,EAAA,CAAA,uCAXhBA,EAAS,CAAA,iCACJA,EAAS,CAAA,CAAA,mBACZA,EAAS,CAAA,EAAG,oBAAsB,kMAqCzC,OAAAA,EAAa,CAAA,IAAA,SAAWA,MAAW,MAAK,EAUnCA,OAAa,OAAM,EAEnBA,OAAa,WAAU,wSApB1BA,EAAO,CAAA,CAAA,sBACCA,EAAQ,CAAA,CAAA,uBACPA,EAAS,EAAA,CAAA,sBACVA,EAAI,CAAA,CAAA,oEAXHA,EAAW,EAAA,CAAA,kCAKXA,EAAI,CAAA,CAAA,kBACHA,EAAM,CAAA,CAAA,EAOXqB,EAAAf,EAAA,OAAAN,OAAa,KAAK,aAClBA,EAAS,EAAA,CAAA,UAftBG,EAuCMxC,EAAA2C,EAAAF,CAAA,4CArCOJ,EAAc,EAAA,CAAA,wMAOnBA,EAAO,CAAA,CAAA,sCACCA,EAAQ,CAAA,CAAA,wCACPA,EAAS,EAAA,CAAA,oCACVA,EAAI,CAAA,CAAA,kCAXHA,EAAW,EAAA,CAAA,8DAKXA,EAAI,CAAA,CAAA,iCACHA,EAAM,CAAA,CAAA,iBAOXqB,EAAAf,EAAA,OAAAN,OAAa,KAAK,+BAClBA,EAAS,EAAA,CAAA,0HApBd,MAAA,OAAOA,EAAe,EAAA,CAAA,0BAElBA,EAAkB,EAAA,gGAFtBS,EAAA,CAAA,EAAA,SAAAa,EAAA,MAAA,OAAOtB,EAAe,EAAA,CAAA,+JA0C3BA,EAAY,EAAA,CAAA,eAAZA,EAAY,EAAA,CAAA,8CAAZA,EAAY,EAAA,CAAA,2EANH,QAAAA,MAAa,eAAc,iDAG3B,iGAHAS,EAAA,CAAA,EAAA,UAAAc,EAAA,QAAAvB,MAAa,sSAHhBA,EAAY,EAAA,EAAArC,EAAAyC,CAAA,qCAAZJ,EAAY,EAAA,CAAA,6DATZ,IAAAwB,EAAAxB,MAAW,oCACR,MAAA,CAAA,IAAKA,EAAY,EAAA,CAAA,aACd,2CAEU,sBAEZ,SAAQyB,EAAA,6IANZ,GAAAhB,EAAA,CAAA,EAAA,MAAAe,KAAAA,EAAAxB,MAAW,OAAK,sKACbS,EAAA,CAAA,EAAA,UAAAiB,EAAA,MAAA,CAAA,IAAK1B,EAAY,EAAA,gxBA5CzBA,EAAI,CAAA,GAAIA,EAAQ,CAAA,IAAK,QAAM2B,GAAA3B,CAAA,uCAiB3B,OAAAA,OAAa,OAAM,0BAiDnB,IAAA4B,EAAA5B,EAA0B,EAAA,GAAAA,EAAU,EAAA,GAAAA,OAAoBA,EAAa,EAAA,GAAA6B,GAAA7B,CAAA,kMAlErEA,EAAI,CAAA,GAAIA,EAAQ,CAAA,IAAK,sNAkErBA,EAA0B,EAAA,GAAAA,EAAU,EAAA,GAAAA,OAAoBA,EAAa,EAAA,qOA5HhE8B,GACRhK,EACAiK,EAA4B,KAC5BC,EAAW,GAAA,CAEP,GAAAA,EAAA,OAAiB,OAAOlK,CAAI,EAC1B,MAAAmK,EAAM,OAAOnK,CAAI,EAEnB,MADC,CAAAiK,GAAcA,GAAc,GAC7BE,EAAI,QAAUF,EAAmBE,EAC9BA,EAAI,MAAM,EAAGF,CAAU,EAAI,eAe1BG,GAAUC,EAAA,CAClB,6BAAA,IAAA,CACCA,EAAK,MAAA,oDAhEI,CAAA,KAAAC,CAAA,EAAA1B,GACA,MAAAjJ,EAAyB,EAAA,EAAAiJ,GACzB,cAAAnL,EAA+B,IAAA,EAAAmL,GAC/B,QAAAlL,EAAU,EAAA,EAAAkL,GACV,OAAA2B,EAAS,EAAA,EAAA3B,GACT,SAAA3C,EAOE,KAAA,EAAA2C,EACF,CAAA,iBAAA4B,CAAA,EAAA5B,GAKA,YAAA6B,EAAc,EAAA,EAAA7B,GACd,SAAAxF,EAAW,EAAA,EAAAwF,GACX,UAAA8B,EAAY,EAAA,EAAA9B,GACZ,UAAA+B,EAA2B,IAAA,EAAA/B,EAC3B,CAAA,WAAAgC,EAAA,EAAA,EAAAhC,EACA,CAAA,KAAAiC,CAAA,EAAAjC,GACA,YAAAkC,EAAc,EAAA,EAAAlC,GACd,UAAAmC,EAAY,EAAA,EAAAnC,GAEZ,uBAAAoC,EAAyB,EAAA,EAAApC,EACzB,CAAA,OAAAlH,CAAA,EAAAkH,GACA,iBAAAqC,EAAmD,IAAA,EAAArC,GACnD,cAAAsC,EAAgD,IAAA,EAAAtC,EAChD,CAAA,GAAAuC,CAAA,EAAAvC,QAELjC,GAAWyE,cAqCRC,EAAYtI,EAAA,CACpB4D,GAAS,OAAA,CACR,WAAY5D,EACZ,OAAArB,CAAA,CAAA,WAIO4J,EAAevI,EAAA,CACvB4D,GAAS,UAAW5D,CAAK,WAGjBwI,EAAmBC,EAAA,CAC3BzC,EAAA,EAAApJ,EAAQ6L,EAAU,SAAA,CAAA,EAClB7E,GAAS,OAAA,CACR,WAAA,CACC,OAAA,CACC,KAAM,WACN,QAAS6E,EACT,MAAOA,EAAU,SAAA,IAGnB,OAAA9J,iJAUUyJ,EAAEM,yDAiEGR,EAAiBvJ,EAAO,CAAC,CAAA,SAKzBwJ,EAAcxJ,EAAO,CAAC,CAAA,uyBAzHvCqH,EAAA,GAAG2C,EAAmB,CAAApB,GAAQK,IAAc,MAAQA,EAAY,CAAA,0BAEhE5B,EAAA,GAAG4C,EAAkBvI,EAClBzD,EACAlC,IAAkB,KACjBA,EACAkC,CAAA,0BAEJoJ,EAAA,GAAG6C,EAAeF,EACf1B,GAAc2B,EAAiBhB,EAAW1E,IAAa,OAAO,EAC9D0F,CAAA,mbCpDDE,GAAA3D,OAAU,KAAOA,EAAQ,CAAA,EAAA,EAAI,IAAE,4OADcA,EAAK,CAAA,CAAA,uCAApDG,EAEIxC,EAAAiG,EAAAxD,CAAA,iBADFK,EAAA,GAAAkD,KAAAA,GAAA3D,OAAU,KAAOA,EAAQ,CAAA,EAAA,EAAI,IAAE,KAAA6D,GAAAC,EAAAH,CAAA,sBADc3D,EAAK,CAAA,CAAA,+WARpDG,EAMIxC,EAAAoG,EAAA3D,CAAA,mEAPAJ,EAAS,CAAA,EAAA6B,wOAJF,MAAA9G,EAAuB,IAAA,EAAA2F,GACvB,UAAAsD,EAAY,EAAA,EAAAtD,wgBCExBP,EAmBQxC,EAAAsB,EAAAmB,CAAA,iCAfG6D,GAAAjE,OAAAA,EAAQ,CAAA,EAAA,MAAA,KAAA,SAAA,yGAPP,GAAA,CAAA,SAAAW,CAAA,EAAAD,UAQK7F,GAAK,CACpBA,EAAM,eAAc,EACd,MAAAqJ,EAAQrJ,EAAM,QAAQ,CAAC,EACvBsJ,EAAU,IAAO,WAAW,SACjC,QAASD,EAAM,QACf,QAASA,EAAM,QACf,QAAS,GACT,WAAY,GACZ,KAAM,SAEPvD,EAASwD,CAAU,gsBCnBrBhE,EAeKxC,EAAAoD,EAAAX,CAAA,suBCVGJ,EAAI,CAAA,CAAA,eACHA,EAAI,CAAA,CAAA,kGAFbG,EAoBKxC,EAAA4C,EAAAH,CAAA,EAbJC,EAMCE,EAAA6D,CAAA,EACD/D,EAKCE,EAAA8D,CAAA,6BAlBMrE,EAAI,CAAA,CAAA,oBACHA,EAAI,CAAA,CAAA,mDALD,KAAAsE,EAAO,EAAA,EAAA5D,8vBCIXV,EAAI,CAAA,CAAA,eACHA,EAAI,CAAA,CAAA,kGAFbG,EAoBKxC,EAAA4C,EAAAH,CAAA,EAbJC,EAMCE,EAAA6D,CAAA,EACD/D,EAKCE,EAAA8D,CAAA,6BAlBMrE,EAAI,CAAA,CAAA,oBACHA,EAAI,CAAA,CAAA,mDALD,KAAAsE,EAAO,EAAA,EAAA5D,mpCCoNlBP,EAyBKxC,EAAA4C,EAAAH,CAAA,EAxBJC,EAKCE,EAAA6D,CAAA,EACD/D,EAKCE,EAAA8D,CAAA,EACDhE,EAKCE,EAAAgE,CAAA,EACDlE,EAKCE,EAAAiE,CAAA,40BA7CFrE,EAmBKxC,EAAA4C,EAAAH,CAAA,EAlBJC,EAKCE,EAAA6D,CAAA,EACD/D,EAKCE,EAAA8D,CAAA,EACDhE,EAKCE,EAAAgE,CAAA,kvCAnDFpE,EA+BKxC,EAAA4C,EAAAH,CAAA,EA9BJC,EAKCE,EAAA6D,CAAA,EACD/D,EAKCE,EAAA8D,CAAA,EACDhE,EAKCE,EAAAgE,CAAA,EACDlE,EAKCE,EAAAiE,CAAA,EACDnE,EAKCE,EAAAkE,CAAA,o6BArDFtE,EAqBKxC,EAAA4C,EAAAH,CAAA,EApBJC,EAOCE,EAAA6D,CAAA,EACD/D,EAKCE,EAAA8D,CAAA,EACDhE,EAKCE,EAAAgE,CAAA,q6BA3CFpE,EAqBKxC,EAAA4C,EAAAH,CAAA,EApBJC,EAOCE,EAAA6D,CAAA,EACD/D,EAKCE,EAAA8D,CAAA,EACDhE,EAKCE,EAAAgE,CAAA,8pBArCFpE,EAeKxC,EAAA4C,EAAAH,CAAA,EAdJC,EAOCE,EAAAf,CAAA,EACDa,EAKCE,EAAAC,CAAA,8pBA/BFL,EAeKxC,EAAA4C,EAAAH,CAAA,EAdJC,EAOCE,EAAAf,CAAA,EACDa,EAKCE,EAAAC,CAAA,8rBAhCFL,EAgBKxC,EAAA4C,EAAAH,CAAA,EAfJC,EAOCE,EAAAf,CAAA,EACDa,EAMCE,EAAAC,CAAA,2rBAjCFL,EAgBKxC,EAAA4C,EAAAH,CAAA,EAfJC,EAOCE,EAAAf,CAAA,EACDa,EAMCE,EAAAC,CAAA,stBAlCFL,EAiBKxC,EAAA4C,EAAAH,CAAA,EAhBJC,EAQCE,EAAAf,CAAA,EACDa,EAMCE,EAAAC,CAAA,ytBAnCFL,EAiBKxC,EAAA4C,EAAAH,CAAA,EAhBJC,EAQCE,EAAAf,CAAA,EACDa,EAMCE,EAAAC,CAAA,uDAjBE,GAAAR,MAAQ,mBAAkB,OAAA6B,GAmBrB,GAAA7B,MAAQ,kBAAiB,OAAA0E,GAmBzB,GAAA1E,MAAQ,gBAAe,OAAA2E,GAkBvB,GAAA3E,MAAQ,gBAAe,OAAA4E,GAkBvB,GAAA5E,MAAQ,aAAY,OAAA6E,GAiBpB,GAAA7E,MAAQ,gBAAe,OAAA2B,GAiBvB,GAAA3B,MAAQ,WAAU,OAAA8E,GAuBlB,GAAA9E,MAAQ,YAAW,OAAA+E,GAuBnB,GAAA/E,MAAQ,aAAY,OAAAgF,GAiCpB,GAAAhF,MAAQ,SAAQ,OAAAiF,GAqBhB,GAAAjF,MAAQ,eAAc,OAAAkF,2OAnNpB,GAAA,CAAA,KAAAC,CAAA,EAAAzE,iMC2HA,OAAAV,QAAsB,MAAK,gCAM5BA,EAAY,EAAA,EAAC,OAAS,GAAC6E,GAAA7E,CAAA,4SAR7BG,EAaKxC,EAAAoD,EAAAX,CAAA,EAZJC,EAMMU,EAAAT,CAAA,sKACDN,EAAY,EAAA,EAAC,OAAS,uLAHJ,EAAE,CAAA,CAAA,+MAFJ,EAAE,CAAA,CAAA,8MAOpBA,EAAa,EAAA,CAAA,4DAAbA,EAAa,EAAA,CAAA,gFADfG,EAEMxC,EAAA2C,EAAAF,CAAA,oCADJJ,EAAa,EAAA,CAAA,sXAMjBG,EAIKxC,EAAAoD,EAAAX,CAAA,EAHJC,EAEMU,EAAAT,CAAA,kuBApCD,KAAAN,OAAgBA,EAAC,CAAA,sDAcd,OAAA,CAAAA,KAAG,CAAC,8JAET,IAAAoF,EAAApF,YAAiB4E,GAAA5E,CAAA,EAgBjBqF,EAAArF,YAAmB2E,GAAA,IAQpB3E,EAAS,EAAA,GAAA0E,GAAA,IAIV1E,EAAe,EAAA,GAAA6B,GAAA7B,CAAA,siBApDXA,EAAK,CAAA,CAAA,qFAtBJC,EAAA8D,EAAA,YAAAuB,EAAArR,GAAgB+L,EAAK,CAAA,EAAEA,EAAc,EAAA,EAAAA,QAAa,OAC1D,OACA/L,GAAgB+L,EAAK,CAAA,EAAEA,EAAc,EAAA,EAAAA,QAAa,MACjD,YACA,YAAY,EACDuF,GAAAxB,EAAA,QAAA/D,KAAeA,EAAC,CAAA,CAAA,CAAA,EAAWuF,GAAAxB,EAAA,OAAA/D,MAAoBA,EAAC,CAAA,CAAA,CAAA,cAMxDA,EAAK,CAAA,CAAA,+BAhBSqB,EAAA0C,EAAA,gBAAA/D,KAAIA,EAAqB,CAAA,CAAA,oBAC3BA,EAAC,CAAA,IAAKA,EAAqB,CAAA,EAAG,CAAC,EACrCqB,EAAA0C,EAAA,QAAA/D,EAAgB,CAAA,IAAAA,EAAK,CAAA,GAAAA,OAAoBA,EAAC,CAAA,CAAA,EACzCqB,EAAA0C,EAAA,SAAA/D,UAAiB,EACfqB,EAAA0C,EAAA,WAAA/D,UAAmB,UALpCG,EAoFIxC,EAAAoG,EAAA3D,CAAA,EAjEHC,EAgEK0D,EAAAyB,CAAA,EA/DJnF,EA2DKmF,EAAAC,CAAA,EA1DJpF,EAsDQoF,EAAAxG,CAAA,sUAvCAwB,EAAA,CAAA,EAAA,KAAAiF,EAAA,KAAA1F,OAAgBA,EAAC,CAAA,8FAcdS,EAAA,CAAA,EAAA,IAAAiF,EAAA,OAAA,CAAA1F,KAAG,CAAC,oGAETA,iHAgBAA,mIAxCEA,EAAK,CAAA,CAAA,EAgDRA,EAAS,EAAA,+FAIVA,EAAe,EAAA,6GA1EV,CAAApF,GAAA6F,EAAA,CAAA,EAAA,MAAA6E,KAAAA,EAAArR,GAAgB+L,EAAK,CAAA,EAAEA,EAAc,EAAA,EAAAA,QAAa,OAC1D,OACA/L,GAAgB+L,EAAK,CAAA,EAAEA,EAAc,EAAA,EAAAA,QAAa,MACjD,YACA,mDACWuF,GAAAxB,EAAA,QAAA/D,KAAeA,EAAC,CAAA,CAAA,CAAA,gBAAWuF,GAAAxB,EAAA,OAAA/D,MAAoBA,EAAC,CAAA,CAAA,CAAA,4BAMxDA,EAAK,CAAA,CAAA,iBAhBSqB,EAAA0C,EAAA,gBAAA/D,KAAIA,EAAqB,CAAA,CAAA,mCAC3BA,EAAC,CAAA,IAAKA,EAAqB,CAAA,EAAG,CAAC,iBACrCqB,EAAA0C,EAAA,QAAA/D,EAAgB,CAAA,IAAAA,EAAK,CAAA,GAAAA,OAAoBA,EAAC,CAAA,CAAA,qBACzCqB,EAAA0C,EAAA,SAAA/D,UAAiB,sBACfqB,EAAA0C,EAAA,WAAA/D,UAAmB,yLAmBjBnF,GAAK,CACnBA,EAAM,eAAc,EACpBA,EAAM,gBAAe,MAbVA,GAAK,CACnBA,EAAM,eAAc,EACpBA,EAAM,gBAAe,oCAzEX,CAAA,MAAApD,CAAA,EAAAiJ,EACA,CAAA,EAAArL,CAAA,EAAAqL,EACA,CAAA,sBAAAiF,CAAA,EAAAjF,EACA,CAAA,YAAAkF,CAAA,EAAAlF,EACA,CAAA,gBAAAmF,CAAA,EAAAnF,EACA,CAAA,QAAAtM,CAAA,EAAAsM,EACA,CAAA,eAAAoF,CAAA,EAAApF,EACA,CAAA,oBAAAqF,CAAA,EAAArF,EACA,CAAA,mBAAAsF,CAAA,EAAAtF,EACA,CAAA,gBAAAuF,CAAA,EAAAvF,EACA,CAAA,aAAAvM,EAAA,EAAA,EAAAuM,EACA,CAAA,eAAA1K,EAAA,EAAA,EAAA0K,EAOA,CAAA,iBAAA4B,CAAA,EAAA5B,EAKA,CAAA,YAAA6B,CAAA,EAAA7B,EACA,CAAA,UAAA+B,CAAA,EAAA/B,EACA,CAAA,SAAAxF,CAAA,EAAAwF,EACA,CAAA,KAAAiC,CAAA,EAAAjC,EACA,CAAA,GAAAuC,CAAA,EAAAvC,EACA,CAAA,UAAA8B,EAAA,EAAA9B,EACA,CAAA,UAAAwF,CAAA,EAAAxF,WASFyF,EAAoBC,EAAA,QACxBA,GAAaT,EACT,OAGJS,IAAc,EACV,YAGgB,MAAMA,CAAS,EACrC,KAAK,CAAC,EACN,KAAKzR,EAAG0K,KACDyG,EAAezG,EAAG,CAEzB,EAAA,KAAK,KAAK,CAEkB,gEAyCdxE,GAAK,EAEhBA,EAAM,OAAO,MAAQ,SACrBA,EAAM,OAAO,MAAQ,UACrBA,EAAM,OAAO,MAAQ,QAErBoL,EAAgBpL,CAAK,GApBb+F,EAAA/F,GAAUkL,EAAoBlL,EAAOxF,CAAC,EA0DvBoM,GAAA5G,GAAUmL,EAAmBnL,EAAOxF,CAAC,EArEvDgR,GAAAxL,GAAUkL,EAAoBlL,EAAOxF,CAAC,02BAvCjDwL,EAAA,GAAGyF,EAAkBJ,GAAaA,EAAU,CAAC,IAAM,SAAA,sBACnDrF,EAAA,GAAG0F,EAAapS,EAAa,UAAWG,GAASA,EAAK,MAAQe,CAAC,CAAA,0BAC/DwL,EAAA,GAAG2F,EAAexQ,EAAe,UAAW1B,GAASA,EAAK,MAAQe,CAAC,CAAA,wBACnEwL,EAAA,GAAG4F,EAAgBF,IAAA,GAAoBA,EAAa,EAAI,IAAA,wBACxD1F,EAAA,GAAG6F,EACFH,IAAe,GAAKpS,EAAaoS,CAAU,EAAE,UAAY,IAAA,gvBCyGpDvG,EAAQ,EAAA,GAAIA,EAAuB,EAAA,EAAA,CAAAA,KAAOA,EAAC,CAAA,CAAA,EAAGA,EAAc,CAAA,EAAEA,EAAQ,EAAA,CAAA,mEA7B3D,cAAAA,EAAkB,EAAA,IAAA,OAC9BA,EAAA,EAAA,EACA,OAAOA,EAAK,CAAA,CAAA,+EAKTA,EAAO,EAAA,GAAIA,EAAO,EAAA,EAAC,CAAC,IAAMA,EAAK,CAAA,GAAIA,EAAO,EAAA,EAAC,CAAC,IAAMA,EAAC,CAAA,6DAajC,uBAAAA,KAAe,SAAW,GACjDA,KAAe,CAAC,EAAE,CAAC,IAAMA,MACzBA,EAAc,CAAA,EAAC,CAAC,EAAE,CAAC,IAAMA,EAAC,CAAA,EAClB,OAAA,CAAAA,KAAOA,EAAC,CAAA,CAAA,mBACCA,EAAoB,EAAA,gBACvBA,EAAiB,EAAA,8BAErBA,EAAI,EAAA,iCA5BNA,KAAG,QAAK,SAAR2G,EAAA,GAAA3G,KAAG,+HAiBHiE,GAAAjE,OAAAA,EAAW,CAAA,EAAA,MAAA,KAAA,SAAA,8VAxCZC,EAAA2D,EAAA,WAAAgD,EAAA5G,EAAI,CAAA,EAAAA,EAAyB,CAAA,EAAA,GAAI,CAAC,iBAElCA,EAAK,CAAA,CAAA,iBACLA,EAAC,CAAA,CAAA,EACUC,EAAA2D,EAAA,cAAAiD,EAAA,QAAA7G,QAASA,EAAC,CAAA,CAAA,EAAA,0BAGhBA,EAAc,CAAA,EAACA,EAAY,CAAA,CAAA,EAAA,WAAAA,MAAkBA,EAAC,CAAA,CAAA,EAAA,MAAKA,EAAO,EAAA,GACxE,GAAE,+BAVkBqB,EAAAuC,EAAA,gBAAA5D,KAAIA,EAAqB,CAAA,CAAA,oBAC3BA,EAAC,CAAA,IAAKA,EAAqB,CAAA,EAAG,CAAC,EAUrCqB,EAAAuC,EAAA,QAAA5D,OAAcA,EAAe,EAAA,CAAA,sBACrBA,EAAe,EAAA,CAAA,eACtBA,EAAU,EAAA,CAAA,kBACPA,EAAa,EAAA,CAAA,gBACfA,EAAW,EAAA,CAAA,iBACVA,EAAY,EAAA,CAAA,oBACTA,EAAgB,EAAA,GAClCA,EAAgB,EAAA,EAAC,MAAQA,EAAK,CAAA,GAC9BA,EAAgB,EAAA,EAAC,MAAQA,EAAC,CAAA,CAAA,iBACXA,EAAW,EAAA,CAAA,UArB5BG,EA4DIxC,EAAAiG,EAAAxD,CAAA,EArCHC,EAoCKuD,EAAA7C,CAAA,8IAhCYN,EAAA,CAAA,EAAA,WAAAiF,EAAA,cAAA1F,EAAkB,EAAA,IAAA,OAC9BA,EAAA,EAAA,EACA,OAAOA,EAAK,CAAA,CAAA,mKAKTA,EAAO,EAAA,GAAIA,EAAO,EAAA,EAAC,CAAC,IAAMA,EAAK,CAAA,GAAIA,EAAO,EAAA,EAAC,CAAC,IAAMA,EAAC,CAAA,mIAajCS,EAAA,CAAA,EAAA,KAAAiF,EAAA,uBAAA1F,KAAe,SAAW,GACjDA,KAAe,CAAC,EAAE,CAAC,IAAMA,MACzBA,EAAc,CAAA,EAAC,CAAC,EAAE,CAAC,IAAMA,EAAC,CAAA,GAClBS,EAAA,CAAA,EAAA,KAAAiF,EAAA,OAAA,CAAA1F,KAAOA,EAAC,CAAA,CAAA,qCACCA,EAAoB,EAAA,mCACvBA,EAAiB,EAAA,qEAErBA,EAAI,EAAA,kEA5BN0F,EAAA,GAAA1F,KAAG,8CA8BRA,EAAQ,EAAA,GAAIA,EAAuB,EAAA,EAAA,CAAAA,KAAOA,EAAC,CAAA,CAAA,EAAGA,EAAc,CAAA,EAAEA,EAAQ,EAAA,CAAA,+GArDlE,CAAApF,GAAA6F,EAAA,CAAA,EAAA,IAAAmG,KAAAA,EAAA5G,EAAI,CAAA,EAAAA,EAAyB,CAAA,EAAA,GAAI,oDAEjCA,EAAK,CAAA,CAAA,gCACLA,EAAC,CAAA,CAAA,GACU,CAAApF,GAAA6F,EAAA,CAAA,EAAA,IAAAoG,KAAAA,EAAA,QAAA7G,QAASA,EAAC,CAAA,CAAA,4DAGhBA,EAAc,CAAA,EAACA,EAAY,CAAA,CAAA,EAAA,WAAAA,MAAkBA,EAAC,CAAA,CAAA,EAAA,MAAKA,EAAO,EAAA,GACxE,qCAVoBqB,EAAAuC,EAAA,gBAAA5D,KAAIA,EAAqB,CAAA,CAAA,mCAC3BA,EAAC,CAAA,IAAKA,EAAqB,CAAA,EAAG,CAAC,0BAUrCqB,EAAAuC,EAAA,QAAA5D,OAAcA,EAAe,EAAA,CAAA,oCACrBA,EAAe,EAAA,CAAA,6BACtBA,EAAU,EAAA,CAAA,yCACPA,EAAa,EAAA,CAAA,sCACfA,EAAW,EAAA,CAAA,uCACVA,EAAY,EAAA,CAAA,qCACTA,EAAgB,EAAA,GAClCA,EAAgB,EAAA,EAAC,MAAQA,EAAK,CAAA,GAC9BA,EAAgB,EAAA,EAAC,MAAQA,EAAC,CAAA,CAAA,sCACXA,EAAW,EAAA,CAAA,sKA9GhB,CAAA,MAAAvI,CAAA,EAAAiJ,EACA,CAAA,MAAA3F,CAAA,EAAA2F,EACA,CAAA,EAAA5K,CAAA,EAAA4K,EACA,CAAA,sBAAAiF,CAAA,EAAAjF,EACA,CAAA,eAAAoF,CAAA,EAAApF,EACA,CAAA,kBAAAoG,CAAA,EAAApG,EAKA,CAAA,YAAAyC,CAAA,EAAAzC,EAMA,CAAA,iBAAAqG,CAAA,EAAArG,EAKA,CAAA,iBAAAjH,CAAA,EAAAiH,EAIA,CAAA,sBAAAzF,CAAA,EAAAyF,EAKA,CAAA,eAAAtJ,CAAA,EAAAsJ,EACA,CAAA,WAAAsG,CAAA,EAAAtG,EACA,CAAA,iBAAAuG,CAAA,EAAAvG,EAMA,CAAA,QAAAlL,CAAA,EAAAkL,EACA,CAAA,iBAAA4B,CAAA,EAAA5B,EAKA,CAAA,YAAA6B,CAAA,EAAA7B,EACA,CAAA,SAAA3C,CAAA,EAAA2C,EACA,CAAA,QAAA3B,EAAA,EAAA2B,EACA,CAAA,UAAA+B,CAAA,EAAA/B,EACA,CAAA,SAAAxF,CAAA,EAAAwF,GACA,UAAA8B,EAAY,EAAA,EAAA9B,EACZ,CAAA,KAAAiC,EAAA,EAAAjC,EACA,CAAA,WAAAgC,GAAA,EAAA,EAAAhC,EACA,CAAA,GAAAuC,CAAA,EAAAvC,EAIA,CAAA,qBAAAwG,EAAA,EAAAxG,EACA,CAAA,kBAAAyG,EAAA,EAAAzG,EACA,CAAA,YAAAkC,CAAA,EAAAlC,EACA,CAAA,cAAAnL,EAAA,EAAAmL,GACA,KAAA0G,EAAO,EAAA,EAAA1G,WAET2G,GAAkBjB,EAAA,QACtBA,GAAaT,EACT,OAGJS,IAAc,EACV,YAGgB,MAAMA,CAAS,EACrC,KAAK,CAAC,EACN,KAAKzR,GAAG0K,KACDyG,EAAezG,EAAG,CAEzB,EAAA,KAAK,KAAK,CAEkB,2CAqCpBiI,EAAA,GAAA,UAAArE,EAAG,MAAKxL,CAAA,IAARwL,EAAG,MAAKxL,uBAWV,MAAAP,EAAM6D,EACNxG,EAAMuB,EACPsB,EAAe,KAAO,CAAA,CAAAE,GAAGC,EAAC,IAAMD,KAAMJ,GAAOK,KAAMhD,CAAG,OAC1D6C,EAAc,CAAA,CAAKF,EAAK3C,CAAG,CAAA,CAAA,GAiBFkN,EAAA5G,GAAUkM,EAAiBlM,EAAOE,EAAOjF,CAAC,6CArD5DmN,EAAG,KAAIM,WAIH,MAAAgE,GAAApO,GAAM2N,EAAkB3N,EAAG4B,EAAOjF,CAAC,EAClB0R,GAAArO,GAAM4N,EAAiB5N,EAAG4B,EAAOjF,CAAC,kvCAjBlE+K,EAAA,GAAG4G,EAAehO,EAAA,CAAkBsB,EAAOjF,CAAC,EAAGsB,GAAA,CAAA,CAAA,CAAA,oBAC/CyJ,EAAA,GAAG6G,EAAkBnO,GAAA,CAAsBwB,EAAOjF,CAAC,EAAGsB,CAAc,CAAA,oBACjEyJ,EAAA,GAAA8G,EAAaF,EAAa,SAAS,QAAQ,CAAA,oBAC3C5G,EAAA,GAAA+G,EAAgBH,EAAa,SAAS,WAAW,CAAA,oBACjD5G,EAAA,GAAAgH,EAAcJ,EAAa,SAAS,SAAS,CAAA,oBAC7C5G,EAAA,GAAAiH,EAAeL,EAAa,SAAS,UAAU,CAAA,+3BCzFnDtH,EAEQxC,EAAAsB,EAAAmB,CAAA,gCAFiC6D,GAAAjE,OAAAA,EAAQ,CAAA,EAAA,MAAA,KAAA,SAAA,8EAHrC,GAAA,CAAA,SAAAW,CAAA,EAAAD,qRC2NmB,KAAAV,MAAK,KAAa,MAAAA,MAAK,yEAD1CA,EAAO,EAAA,CAAA,EAAU,MAAA+H,EAAA/H,GAAAA,EAAK,EAAA,EAAA,KAAK,CAAC,EAAE,mBAAnC,OAAI3K,GAAA,EAAA,wPAAC2K,EAAO,EAAA,CAAA,sFAAZ,OAAI3K,GAAA,0JACiD;AAAA,OAEtD,eAFsD;AAAA,OAEtD,6eAJG2K,EAAO,EAAA,EAAC,QAAUA,MAAQ,CAAC,EAAE,KAAK,QAAM6B,GAAA7B,CAAA,4wBAN9BgI,EAAM,wBAAoBhI,EAAG,CAAA,EAAA,IAAA,2BAAyBA,EAAM,CAAA,EAAA,IAAA,8BAA4BA,EAAW,CAAA,EAAA,IAAA,8BAA4BA,EAAW,CAAA,EAAA,IAAA,iCAA+BA,EAAc,CAAA,EAAA,IAAA,sBAAoBA,EAAU,CAAA,EAAA,IAAA,uBAJ/MA,EAAc,CAAA,CAAA,UAJvCG,EA2B+BxC,EAAAsK,EAAA7H,CAAA,EA1B9BC,EAyBK4H,EAAAlH,CAAA,EAxBJV,EAuBOU,EAAA1E,CAAA,EAfNgE,EAEOhE,EAAA6L,CAAA,8CACP7H,EAQOhE,EAAA8L,CAAA,iCACP9H,EAEOhE,EAAA+L,CAAA,uGAjBIpI,EAAkB,EAAA,CAAA,4FAOvBA,EAAO,EAAA,EAAC,QAAUA,MAAQ,CAAC,EAAE,KAAK,gOANEA,EAAG,CAAA,EAAA,IAAA,0CAAyBA,EAAM,CAAA,EAAA,IAAA,8CAA4BA,EAAW,CAAA,EAAA,IAAA,8CAA4BA,EAAW,CAAA,EAAA,IAAA,+CAA+BA,EAAc,CAAA,EAAA,IAAA,oCAAoBA,EAAU,CAAA,EAAA,IAAA,qCAJ/MA,EAAc,CAAA,CAAA,iKA5LlCgI,GAAS,2DAhBF,CAAA,MAAAK,EAAA,EAAA,EAAA3H,EAEA,CAAA,WAAA4H,CAAA,EAAA5H,EACA,CAAA,cAAA6H,CAAA,EAAA7H,EACA,CAAA,sBAAA8H,CAAA,EAAA9H,GACA,MAAA1G,EAAQ,CAAA,EAAA0G,GACR,IAAAzG,EAAM,EAAA,EAAAyG,EACN,CAAA,SAAAjL,CAAA,EAAAiL,GACA,eAAA+H,EAAiB,EAAA,EAAA/H,GACjB,mBAAAgI,EAAqB,EAAA,EAAAhI,EACrB,CAAA,SAAAiI,CAAA,EAAAjI,EAEMwC,GAAA,MAMb0F,EAAiB,GACjBC,EAAS,EACTC,EACAC,EAAc,EACdC,EAAc,EACdC,EAAA,CAAA,EACAC,EACAxR,EACAyR,EAAM,EACNC,EAAkB,IAClBC,GAAA,CAAA,EACAC,EAIE,MAAAC,EAAA,OAAoB,OAAW,IAC/BC,EAAMD,EACT,OAAO,sBACNE,GAAiCA,IAQtB,eAAAC,IAAA,CACVC,EAAY,OAAS3P,SAClB4P,GAAgBD,EAAY,OAAS,EAAA,CAAK,SAAU,MAAA,CAAA,EAGrD,MAAAE,EAAY,KAAK,IAAI,EAAGlB,EAAS,SAAS,EAChD9H,EAAA,GAAA6H,EAAqBmB,EAAY,GAAA,OACjCrB,EAAwBG,EAAS,YAAcA,EAAS,WAAA,EAG/C,QAAAmB,EAAI,EAAGA,EAAIpS,EAAK,OAAQoS,GAAK,EACrCb,EAAWjP,EAAQ8P,CAAC,EAAIpS,EAAKoS,CAAC,EAAE,sBAAwB,EAAA,WAErDzU,EAAI,EACJ0U,EAAIhB,EAED,KAAA1T,EAAIsU,EAAY,QAAA,OAChBK,EAAaf,EAAW5T,CAAC,GAAKuT,EAEhC,GAAAmB,EAAIC,EAAaH,EAAYvB,EAAA,MAChCtO,EAAQ3E,CAAA,EACRwL,EAAA,EAAAsI,EAAMY,EAAIhB,CAAA,QAGXgB,GAAKC,EACL3U,GAAK,MAGF4U,EAAiBlB,EACd,KAAA1T,EAAIsU,EAAY,QAAA,OAChBK,EAAaf,EAAW5T,CAAC,GAAKuT,EAIhC,GAHJqB,GAAkBD,EAClB3U,GAAK,EAED4U,EAAiBlB,EAAc,EAAIT,aAKxCrO,EAAM5E,CAAA,EACA,MAAA6U,GAAYP,EAAY,OAAS1P,EAEjCkQ,GAAmBxB,EAAS,aAAeA,EAAS,aACtDwB,GAAmB,IACtBF,GAAkBE,IAGf,IAAAC,GAAsBnB,EAAW,OAAQa,GAAa,OAAAA,GAAM,QAAQ,EAUjE,QATPlB,EACCwB,GAAoB,QAAQzS,EAAGC,IAAMD,EAAIC,EAAG,CAAC,EAC5CwS,GAAoB,QAAU,EAAA,EAEhCvJ,EAAA,EAAAgI,EAASqB,GAAYtB,CAAA,EAChB,SAASC,CAAM,OACnBA,EAAS,GAAA,EAEVI,EAAW,OAASU,EAAY,OACzBtU,EAAIsU,EAAY,QACtBtU,GAAK,EACL4T,EAAW5T,CAAC,EAAIuT,EAEbN,GAAc2B,EAAiB3B,OAClCC,EAAgBD,CAAA,OAEhBC,EAAgB0B,CAAA,iBAMHI,GAAkBC,EAAA,CAChCd,EAAA,SAAA,WACYc,GAAM,SAAA,OACX,MAAArV,EAAA,OAAmBqV,GAAM,SAAW,GAAQC,EAAWD,CAAC,EAC1DrV,IAAc,KAGdA,IAAc,QACX,MAAA2U,GAAgBU,GAAK,SAAU,SAAA,CAAA,EAGlCrV,IAAc,kBACX2U,GAAgBU,EAAA,CAAK,SAAU,SAAA,EAAa,EAAI,cAKhDC,EAAWD,EAAA,OACb1P,EAAUlD,GAAQA,EAAK4S,EAAItQ,CAAK,EACjC,GAAA,CAAAY,GAAW0P,EAAItQ,EACZ,MAAA,OAEH,GAAA,CAAAY,GAAW0P,GAAKrQ,EAAM,EACnB,MAAA,WAGA,KAAA,CAAA,IAAKuQ,GAAiB7B,EAAS,sBAAA,GAC/B,IAAAQ,EAAK,OAAAN,IAAWjO,EAAQ,wBAE5BuO,OAAAA,EAAMqB,EAAe,GACjB,OAGJ3B,GAAS2B,EAAepB,EACpB,WAGD,kBAGcQ,GACrB7O,EACA0P,EACAC,EAAY,GAAA,CAEN,MAAApL,GAAA,QAEAqL,EAAc/B,EAEhB,IAAAgC,GAAW7P,EAAQ4P,EACnBD,IACHE,GAAWA,GAAWxB,EAAkBuB,EAAc5B,SAGjDoB,GAAmBxB,EAAS,aAAeA,EAAS,aACtDwB,GAAmB,IACtBS,IAAYT,IAGP,MAAAU,GAAA,CACL,IAAKD,GACL,SAAU,SACP,GAAAH,GAGJ9B,EAAS,SAASkC,EAAK,EAexBC,GAAA,IAAA,CACCpT,EAAOoR,EAAS,cAChBI,EAAU,EAAA,kBAc+BH,EAAW,KAAA,8DAGjCD,EAAQvF,yBAScyF,EAAW,KAAA,8DAjBxCL,EAAQpF,+BACD+F,GAAYpT,EAAA6U,GAAA,QAAA,IAAA,IAAA,IAAA,YAAA7U,EAAA,wfA/K7B2K,EAAA,GAAAuI,GAAkBE,GAAA,YAAAA,EAAc,SAAU,GAAA,wBAuJ7CzI,EAAA,GAAG8I,EAActB,CAAA,yBA/IZa,GAAWE,GAAmBT,EAAS,cAC7Ba,EAAIE,EAAkB,wBAwElCW,GAAkB5U,CAAQ,yBAwE7BoL,EAAA,GAAGwI,GAAUE,EACVI,EAAY,MAAM3P,EAAOC,CAAG,EAAE,KAAKxF,EAAMY,KAChC,CAAA,MAAOA,EAAI2E,EAAO,KAAAvF,CAAA,IAE3BkV,EACC,MAAM,EAAIrB,EAAaqB,EAAY,OAAUf,EAAiB,CAAC,EAC/D,IAAA,CAAKnU,EAAMY,KACF,CAAA,MAAOA,EAAI2E,EAAO,KAAAvF,CAAA,qbC9GnBuW,EAAAC,GAAAjL,KAAeA,EAAQ,CAAA,CAAA,CAAA,uBAA5B,OAAI,GAAA,iQADPG,EAYKxC,EAAAoD,EAAAX,CAAA,oEAXG4K,EAAAC,GAAAjL,KAAeA,EAAQ,CAAA,CAAA,CAAA,oBAA5B,OAAI3K,GAAA,EAAA,mHAAJ,uDAQC2K,EAAG,EAAA,EAAA,wOAPLG,EAQQxC,EAAAsB,EAAAmB,CAAA,wEADNJ,EAAG,EAAA,EAAA,KAAA6D,GAAAqH,EAAAC,CAAA,sIAVHnL,EAAoB,CAAA,GAAA6B,GAAA7B,CAAA,kIAfxBA,EAAQ,CAAA,CAAA,iDAWPA,EAAc,CAAA,CAAA,yaAXfA,EAAQ,CAAA,CAAA,0KAWPA,EAAc,CAAA,CAAA,qYAb6BA,EAAQ,CAAA,CAAA,GAAA,oIAWfA,EAAc,CAAA,CAAA,GAAA,0FAyB7CA,EAAkB,CAAA,4NA9C7BG,EA6DKxC,GAAAyN,EAAAhL,EAAA,EA5DJC,EAA6B+K,EAAA3F,CAAA,SAC7BpF,EA0DK+K,EAAAC,CAAA,EAzDJhL,EAWKgL,EAAA7F,CAAA,EAVJnF,EAAqBmF,EAAAlF,CAAA,SACrBD,EAQQmF,EAAA8F,CAAA,gBAGTjL,EAoCKgL,EAAAE,CAAA,EAnCJlL,EAyBKkL,EAAAC,CAAA,EAxBJnL,EAOQmL,EAAAC,CAAA,0DAmBTpL,EAOCkL,EAAAG,CAAA,SAGFrL,EAKQgL,EAAAM,CAAA,wHAXI3L,EAAmB,CAAA,CAAA,2DApC5BA,GAAQ,CAAA,CAAA,sDAFoCA,GAAQ,CAAA,CAAA,6CAanDA,GAAc,CAAA,CAAA,8CAFsBA,GAAc,CAAA,CAAA,2BAM/CA,GAAoB,CAAA,sFAmBlBA,GAAkB,CAAA,aAAlBA,GAAkB,CAAA,oMAhGjB,GAAA,CAAA,UAAA4L,EAAA,IAAA,MAMPC,EACA9N,EAAgC,SAChC+N,EAAiB,WACjBC,EAAuB,GACvBC,EAAqB,GAEnB,MAAAC,EAAA,CACL,OAAA,CACC,WACA,mBACA,cACA,YACA,KACA,SACA,WACA,gBAED,OAAA,CAAS,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,WAAY,cAAc,GAGlEnB,GAAA,IAAA,CACCoB,MAGQ,SAAAA,GAAA,CACH,GAAA,CAAAL,EAAA,OAEC,MAAAM,EAAiB,OAAO,WACxB/C,EAAkB,OAAO,YACzBgD,EAAYP,EAAa,wBAEzBQ,GAAKF,EAAiBC,EAAU,OAAS,EACzCrC,GAAKX,EAAkBgD,EAAU,QAAU,MAEjDP,EAAa,MAAM,QAAUQ,CAAC,KAAAR,CAAA,MAC9BA,EAAa,MAAM,OAAS9B,CAAC,KAAA8B,CAAA,WAGrBS,EAAoBnT,EAAA,CACtB,MAAAwE,EAASxE,EAAE,OACjB0H,EAAA,EAAAmL,EAAqBrO,EAAO,KAAA,+CAWzBkD,EAAA,EAAA9C,EAAWA,IAAa,SAAW,SAAW,QAAQ,EACtD8C,EAAA,EAAAiL,EAAiBG,EAAelO,CAAQ,EAAE,CAAC,CAAA,GAYzCwO,EAAA,IAAA1L,EAAA,EAAAkL,GAAwBA,CAAoB,QAY1ClL,EAAA,EAAAiL,EAAiBU,CAAG,EACpB3L,EAAA,EAAAkL,GAAwBA,CAAoB,GAuBlCU,EAAA,IAAAb,EAAU7N,EAAU+N,EAAgBE,CAAkB,4CAtDxDH,EAAYtI,wMCkCzBmJ,EAAA1M,MAAK,0BAA0B,EAAA,eAW/B2M,EAAA3M,MAAK,2BAA2B,EAAA,eAOhC4M,EAAA5M,MAAK,sBAAsB,EAAA,aAQ3B6M,EAAA7M,MAAK,kBAAkB,EAAA,iBAOvB8M,GAAA9M,MAAK,wBAAwB,EAAA,iDAhCzB,IAAAoF,GAAApF,EAAmB,EAAA,IAAA,OAASA,QAAkB,MAAIgF,GAAAhF,CAAA,uCAWlD,IAAAqF,EAAArF,EAAmB,EAAA,IAAA,QAAUA,QAAkB,MAAI+E,GAAA/E,CAAA,iFAenDA,EAAa,EAAA,GAAA8E,GAAA,2/BA9BJzD,EAAAiK,EAAA,SAAAtL,QAAmB,KAAK,sDAWxBqB,EAAAoK,EAAA,SAAAzL,QAAmB,MAAM,0GAezBqB,EAAA0L,EAAA,SAAA/M,OAAiBA,EAAkB,EAAA,CAAA,8DA7BlDG,EAUQxC,EAAA2N,EAAAlL,CAAA,8DACRD,EAUQxC,EAAA8N,EAAArL,CAAA,4DACRD,EAGQxC,EAAAgO,EAAAvL,CAAA,sCACRD,EAUQxC,EAAAoP,EAAA3M,CAAA,+DACRD,EAGQxC,EAAAqP,EAAA5M,CAAA,4GAlB0B6D,GAAAjE,OAAAA,EAAa,CAAA,EAAA,MAAA,KAAA,SAAA,oBAMpBA,EAAkB,EAAA,CAAA,CAAA,0BASXiE,GAAAjE,QAAAA,EAAe,EAAA,EAAA,MAAA,KAAA,SAAA,yBA/B/C,CAAApF,IAAA6F,EAAA,CAAA,EAAA,QAAAiM,KAAAA,EAAA1M,MAAK,0BAA0B,EAAA,KAAA6D,GAAAoJ,EAAAP,CAAA,EAC3B1M,EAAmB,EAAA,IAAA,OAASA,QAAkB,yFAJrCqB,EAAAiK,EAAA,SAAAtL,QAAmB,KAAK,GAcrC,CAAApF,IAAA6F,EAAA,CAAA,EAAA,QAAAkM,KAAAA,EAAA3M,MAAK,2BAA2B,EAAA,KAAA6D,GAAAqJ,EAAAP,CAAA,EAC5B3M,EAAmB,EAAA,IAAA,QAAUA,QAAkB,iFAJtCqB,EAAAoK,EAAA,SAAAzL,QAAmB,MAAM,GAUtC,CAAApF,IAAA6F,EAAA,CAAA,EAAA,QAAAmM,KAAAA,EAAA5M,MAAK,sBAAsB,EAAA,KAAA6D,GAAAsJ,EAAAP,CAAA,GAQ3B,CAAAhS,IAAA6F,EAAA,CAAA,EAAA,QAAAoM,KAAAA,EAAA7M,MAAK,kBAAkB,EAAA,KAAA6D,GAAAuJ,EAAAP,CAAA,EACnB7M,EAAa,EAAA,8EAJJqB,EAAA0L,EAAA,SAAA/M,OAAiBA,EAAkB,EAAA,CAAA,GAUhD,CAAApF,IAAA6F,EAAA,CAAA,EAAA,QAAAqM,MAAAA,GAAA9M,MAAK,wBAAwB,EAAA,KAAA6D,GAAAwJ,EAAAP,EAAA,oZA/BL9M,EAAa,EAAA,CAAA,4DAAbA,EAAa,EAAA,CAAA,2EAArCG,EAA4CxC,EAAA2C,EAAAF,CAAA,iCAApBJ,EAAa,EAAA,CAAA,qEAWbA,EAAa,EAAA,CAAA,4DAAbA,EAAa,EAAA,CAAA,2EAArCG,EAA4CxC,EAAA2C,EAAAF,CAAA,iCAApBJ,EAAa,EAAA,CAAA,uPAerCG,EAA8BxC,EAAA2C,EAAAF,CAAA,2CAgB9BsM,EAAA1M,MAAK,yBAAyB,EAAA,aAQ9BsN,EAAAtN,MAAK,yBAAyB,EAAA,uGAE3BA,EAAe,CAAA,GAAA6E,GAAA7E,CAAA,qlBAhBpBG,EAOQxC,EAAA2N,EAAAlL,CAAA,sCACRD,EAOQxC,EAAA8N,EAAArL,CAAA,+HATN,CAAAxF,GAAA6F,EAAA,CAAA,EAAA,QAAAiM,KAAAA,EAAA1M,MAAK,yBAAyB,EAAA,KAAA6D,GAAAoJ,EAAAP,CAAA,GAQ9B,CAAA9R,GAAA6F,EAAA,CAAA,EAAA,QAAA6M,KAAAA,EAAAtN,MAAK,yBAAyB,EAAA,KAAA6D,GAAA0J,EAAAD,CAAA,EAE3BtN,EAAe,CAAA,gUAQjB0M,EAAA1M,MAAK,sBAAsB,EAAA,0VAP7BG,EAQQxC,EAAAsB,EAAAmB,CAAA,gEANG6D,GAAAjE,OAAAA,EAAa,CAAA,EAAA,MAAA,KAAA,SAAA,uBAKtB,CAAApF,GAAA6F,EAAA,CAAA,EAAA,QAAAiM,KAAAA,EAAA1M,MAAK,sBAAsB,EAAA,KAAA6D,GAAAoJ,EAAAP,CAAA,2HAW5BA,EAAA1M,MAAK,2BAA2B,EAAA,aAQhCsN,EAAAtN,MAAK,4BAA4B,EAAA,4GAE9BA,EAAe,CAAA,GAAA2E,GAAA3E,CAAA,wmBAhBpBG,EAOQxC,EAAA2N,EAAAlL,CAAA,sCACRD,EAOQxC,EAAA8N,EAAArL,CAAA,+HATN,CAAAxF,GAAA6F,EAAA,CAAA,EAAA,QAAAiM,KAAAA,EAAA1M,MAAK,2BAA2B,EAAA,KAAA6D,GAAAoJ,EAAAP,CAAA,GAQhC,CAAA9R,GAAA6F,EAAA,CAAA,EAAA,QAAA6M,KAAAA,EAAAtN,MAAK,4BAA4B,EAAA,KAAA6D,GAAA0J,EAAAD,CAAA,EAE9BtN,EAAe,CAAA,iUAQjB0M,EAAA1M,MAAK,yBAAyB,EAAA,gWAPhCG,EAQQxC,EAAAsB,EAAAmB,CAAA,gEANG6D,GAAAjE,OAAAA,EAAa,CAAA,EAAA,MAAA,KAAA,SAAA,uBAKtB,CAAApF,GAAA6F,EAAA,CAAA,EAAA,QAAAiM,KAAAA,EAAA1M,MAAK,yBAAyB,EAAA,KAAA6D,GAAAoJ,EAAAP,CAAA,0ZAlG7B1M,EAAS,EAAA,GAAA2B,GAAA3B,CAAA,EA4CRqF,EAAA,CAAArF,OAAaA,EAAY,EAAA,GAAA4E,GAAA5E,CAAA,IA6B1BA,EAAe,EAAA,GAAA0E,GAAA1E,CAAA,IA+BhBA,EAAkB,EAAA,GAAA6B,GAAA7B,CAAA,gTAzGvBG,EAuGKxC,EAAAoD,EAAAX,CAAA,iHAtGCJ,EAAS,EAAA,yGA4CR,CAAAA,OAAaA,EAAY,EAAA,yGA6B1BA,EAAe,EAAA,2GA+BhBA,EAAkB,EAAA,gSAnLX,CAAA,EAAAqM,CAAA,EAAA3L,EACA,CAAA,EAAAqJ,CAAA,EAAArJ,EACA,CAAA,iBAAA8M,CAAA,EAAA9M,EACA,CAAA,iBAAA+M,CAAA,EAAA/M,EACA,CAAA,mBAAAgN,CAAA,EAAAhN,EACA,CAAA,oBAAAiN,CAAA,EAAAjN,EACA,CAAA,IAAAxJ,CAAA,EAAAwJ,EACA,CAAA,UAAAwF,CAAA,EAAAxF,EACA,CAAA,UAAAkN,CAAA,EAAAlN,EACA,CAAA,cAAAmN,CAAA,EAAAnN,EACA,CAAA,cAAAoN,CAAA,EAAApN,EACA,CAAA,gBAAAqN,CAAA,EAAArN,EACA,CAAA,gBAAAsN,CAAA,EAAAtN,EACA,CAAA,QAAAuN,EAAA,IAAA,MACA,CAAA,cAAAC,EAAA,IAAA,OACA,eAAAC,EAAuC,IAAA,EAAAzN,GACvC,cAAA+F,EAA+B,IAAA,EAAA/F,EAC/B,CAAA,UAAAkL,EAAA,IAAA,MAKA,CAAA,gBAAAwC,EAAA,IAAA,OACA,cAAAC,EAAgC,IAAA,EAAA3N,GAChC,SAAAxF,GAAW,EAAA,EAAAwF,EAEX,CAAA,KAAAiC,CAAA,EAAAjC,EACPmL,EACAyC,EAAsD,KAM1DxD,GAAA,IAAA,CACCoB,OAGQ,SAAAA,IAAA,CACH,GAAA,CAAAL,EAAA,OAEC,MAAAM,EAAiB,OAAO,WACxB/C,EAAkB,OAAO,YACzBgD,EAAYP,EAAa,wBAE3B,IAAA0C,EAAQlC,EAAI,GACZmC,GAAQzE,EAAI,GAEZwE,EAAQnC,EAAU,MAAQD,IAC7BoC,EAAQlC,EAAID,EAAU,MAAQ,IAG3BoC,GAAQpC,EAAU,OAAShD,IAC9BoF,GAAQzE,EAAIqC,EAAU,OAAS,SAGhCP,EAAa,MAAM,QAAU0C,CAAK,KAAA1C,CAAA,OAClCA,EAAa,MAAM,OAAS2C,EAAK,KAAA3C,CAAA,EAGzB,SAAA4C,IAAA,CACJ,GAAAJ,EAAA,CACHzC,EAAU,SAAU,GAAI,EAAE,SAIrB,MAAAQ,EAAYP,EAAa,wBAC/BhL,EAAA,GAAAyN,EAAA,CACC,EAAGlC,EAAU,MACb,EAAGA,EAAU,IAAMA,EAAU,OAAS,IAStB,MAAAxL,EAAA,IAAAqN,EAAQ,KAAK,EAWb5H,GAAA,IAAA4H,EAAQ,MAAM,SAiCdT,UAQAC,WAqBAC,UAQAC,+CArFH9B,EAAYtI,08BA5C3B1C,EAAA,GAAGmD,EAAY9M,IAAQ,EAAA,2BACvB2J,EAAA,GAAG6N,EAAexT,IAAY0S,EAAU,CAAC,IAAM,SAAA,2BAC/C/M,EAAA,GAAGyF,EAAkBpL,IAAYgL,EAAU,CAAC,IAAM,SAAA,ygBCqB1ClG,EAAoB,CAAA,GAAIA,EAAW,CAAA,IAAK,UAAQ6E,GAAA7E,CAAA,+NAP7C0L,EAAA,MAAAiD,EAAA3O,MAAwB,GAElBC,EAAAyL,EAAA,cAAAkD,EAAA5O,OAAgB,SAAW,YAAc,WAAW,2EAGzCA,EAAW,CAAA,CAAA,YAAA,EADhBqB,EAAAqK,EAAA,cAAA1L,OAAgB,QAAQ,wDAP7CG,EAoBKxC,EAAAoD,EAAAX,CAAA,EAnBJC,EAQCU,EAAA2K,CAAA,gDALU1L,EAAmB,CAAA,CAAA,iBADtB,CAAApF,GAAA6F,EAAA,GAAAkO,KAAAA,EAAA3O,MAAwB,KAAE0L,EAAA,QAAAiD,iBAEpB,CAAA/T,GAAA6F,EAAA,GAAAmO,KAAAA,EAAA5O,OAAgB,SAAW,YAAc,sEAG9BA,EAAW,CAAA,CAAA,0CADhBqB,EAAAqK,EAAA,cAAA1L,OAAgB,QAAQ,EAGvCA,EAAoB,CAAA,GAAIA,EAAW,CAAA,IAAK,0jBAC5CG,EAOQxC,EAAAsB,EAAAmB,CAAA,kDALG6D,GAAAjE,OAAAA,EAAgB,CAAA,EAAA,MAAA,KAAA,SAAA,qMAgBvBA,EAAM,CAAA,EAAA,yOAHCA,EAAM,CAAA,EAAG,sBAAwB,iBAAiB,gBACvDA,EAAM,CAAA,EAAG,sBAAwB,iBAAiB,UAJ1DG,EAWQxC,EAAAsB,EAAAmB,CAAA,yCATGJ,EAAW,CAAA,CAAA,oJACTA,EAAM,CAAA,EAAG,sBAAwB,2DACtCA,EAAM,CAAA,EAAG,sBAAwB,4zBA5BrCoF,EAAApF,OAAgB,QAAM4E,GAAA5E,CAAA,IAuBtBA,EAAgB,CAAA,GAAA0E,GAAA1E,CAAA,IAchBA,EAAsB,CAAA,GAAA6B,GAAA7B,CAAA,6ZAvC7BG,EA2CKxC,EAAA6H,EAAApF,CAAA,EA1CJC,EAyCKmF,EAAAC,CAAA,4EAxCCzF,OAAgB,qGAuBhBA,EAAgB,CAAA,gGAchBA,EAAsB,CAAA,8NAnFjB,uBAAA6O,EAAyB,EAAA,EAAAnO,GACzB,iBAAAoO,EAAmB,EAAA,EAAApO,GACnB,YAAAqO,EAA4C,MAAA,EAAArO,GAC5C,WAAAsO,EAAa,EAAA,EAAAtO,EACb,CAAA,QAAAuO,CAAA,EAAAvO,EACA,CAAA,iBAAAwO,CAAA,EAAAxO,QAELjC,EAAWyE,SAIbiM,EAAS,GACTC,GACO,qBAAAC,EAAsC,IAAA,EAAA3O,EAC7C4O,EAAc,YAETC,EAAoBpW,EAAA,CAE5BmW,EADenW,EAAE,OACI,MACf,MAAAqW,EAAYF,GAAe,KAC7BD,IAAyBG,QAC5BH,EAAuBG,CAAA,EACvB/Q,EAAS,SAAU4Q,CAAoB,GAIhC,SAAAI,GAAA,KACRN,EAAS,EAAA,EACLC,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,oBACPD,EAAS,EAAA,GACP,KAGW,eAAAO,GAAA,CACR,MAAAT,EAAA,EACNQ,IAGDE,GAAA,IAAA,CACKP,GAAO,aAAaA,CAAK,ulBC5CxB,SAASQ,GACfC,EACA3J,EACA9J,EAIAgB,EACiB,CACb,IAAA0S,EAAKD,GAAS,GACd,GAAA3J,EAAU,CAAC,IAAM,SAAW4J,EAAG,OAAS5J,EAAU,CAAC,EAAG,CACzD,MAAM6J,EAAO,MAAM7J,EAAU,CAAC,EAAI4J,EAAG,MAAM,EACzC,KAAK,EAAE,EACP,IAAI,CAACnb,EAAGU,IAAM,GAAGA,EAAIya,EAAG,MAAM,EAAE,EAC7BA,EAAAA,EAAG,OAAOC,CAAI,CACpB,CAEA,MAAI,CAACD,GAAMA,EAAG,SAAW,EACjB,MAAM5J,EAAU,CAAC,CAAC,EACvB,KAAK,CAAC,EACN,IAAI,CAACvR,EAAGU,IAAM,CACd,MAAM2a,EAAM5S,IACZ,OAAAhB,EAAI4T,CAAG,EAAI,CAAE,KAAM,KAAM,MAAO,MACzB,CAAE,GAAIA,EAAK,MAAO,KAAK,UAAU3a,EAAI,CAAC,EAAE,CAC/C,EAGIya,EAAG,IAAI,CAACtS,EAAGnI,IAAM,CACvB,MAAM2a,EAAM5S,IACZ,OAAAhB,EAAI4T,CAAG,EAAI,CAAE,KAAM,KAAM,MAAO,MACzB,CAAE,GAAIA,EAAK,MAAOxS,GAAK,EAAG,CAAA,CACjC,CACF,CAEO,SAASyS,GACfC,EACA9T,EAIA+T,EACA/S,EACA7H,EAAmC,KACkC,CACrE,MAAI,CAAC2a,GAAUA,EAAO,SAAW,EACzB,GAGOA,EAAO,IAAI,CAAChZ,EAAK7B,IACxB6B,EAAI,IAAI,CAACO,EAAO3B,IAAM,OAC5B,MAAMka,EAAM5S,IACZhB,EAAI4T,CAAG,EAAI,CAAE,KAAM,KAAM,MAAO,MAChCG,EAAaH,CAAG,EAAIvY,EAEpB,IAAI2Y,GAAUla,EAAAX,GAAA,YAAAA,EAAgBF,KAAhB,YAAAa,EAAqBJ,GAEnC,OAAIsa,IAAY,SACfA,EAAU,OAAO3Y,CAAK,GAGhB,CACN,GAAIuY,EACJ,MAAAvY,EACA,cAAe2Y,CAAA,CAChB,CACA,CACD,CAGF,CCpDgB,SAAAC,GACfvG,EACAhG,EAC4B,CAC5B,GAAIA,IAAM,SAAU,CACb,MAAAwG,EAAI,OAAOR,CAAC,EACX,OAAA,MAAMQ,CAAC,EAAIR,EAAIQ,CACvB,CACA,GAAIxG,IAAM,OAAQ,CACjB,GAAI,OAAOgG,GAAM,UAAkB,OAAAA,EACnC,GAAI,OAAOA,GAAM,SAAU,OAAOA,IAAM,EACxC,MAAM5M,EAAI,OAAO4M,CAAC,EAAE,YAAY,EAC5B,OAAA5M,IAAM,QAAUA,IAAM,IAAY,GAClCA,IAAM,SAAWA,IAAM,IAAY,GAChC4M,CACR,CACA,GAAIhG,IAAM,OAAQ,CACX,MAAAwM,EAAI,IAAI,KAAKxG,CAAC,EACpB,OAAO,MAAMwG,EAAE,QAAA,CAAS,EAAIxG,EAAIwG,EAAE,aACnC,CACO,OAAAxG,CACR,CCjCA,eAAeyG,GACdjB,EACAtP,EACA9I,EACA3C,EACgB,OAChB,GAAI,CAACyL,EAAI,MAAQ,CAACA,EAAI,KAAK9I,CAAG,GAAK,CAAC8I,EAAI,KAAK9I,CAAG,EAAE3C,CAAG,EAAG,OAExD,MAAMic,EAAYxQ,EAAI,KAAK9I,CAAG,EAAE3C,CAAG,EAAE,MACrCyL,EAAI,KAAK9I,CAAG,EAAE3C,CAAG,EAAE,MAAQ+a,EAEvBkB,IAAclB,GAAetP,EAAI,UACpCA,EAAI,SAAS,SAAU,CACtB,KAAMA,EAAI,KAAK,IAAK9I,GAAQA,EAAI,IAAKwC,GAASA,EAAK,KAAK,CAAC,EACzD,UAASxD,EAAA8J,EAAI,UAAJ,YAAA9J,EAAa,IAAKsH,GAAMA,EAAE,SAAU,CAAC,EAC9C,SAAU,IAAA,CACV,EAGFwC,EAAI,QAAQ,aAAa,CAAC9I,EAAK3C,CAAG,CAAC,CACpC,CAEsB,eAAAkc,GACrB5V,EACAmF,EACAxG,EACgB,CAChB,GAAI,CAACwG,EAAI,MAAQ,CAACA,EAAI,SAAW,CAACA,EAAI,IAAK,OAE3C,MAAM0Q,EAAW7V,EAAM,OACnB,CAAC6V,GAAYA,EAAS,QAAU,QAE9B,MAAAH,GACLG,EAAS,OAAS,WAAa,OAAOA,EAAS,OAAO,EAAIA,EAAS,MACnE1Q,EACAxG,EAAO,CAAC,EACRA,EAAO,CAAC,CAAA,CAEV,CAEA,SAASmX,GACR9V,EACAmF,EACU,CACJ,MAAAlD,EAAQoB,GAAI8B,EAAI,KAAK,EACrB6F,EAAkB/I,EAAM,SAAS,gBACjC8I,EAAc9I,EAAM,SAAS,YAC7B1I,EAAU4L,EAAI,SAAW,GAE3B,GAAA6F,IAAoB,IAASD,IAAgB,GAAc,MAAA,GAE/D,OAAQ/K,EAAM,IAAK,CAClB,IAAK,YACA,OAAAmF,EAAA,QAAQ,oBAAoB,EAAK,EACrCA,EAAI,QAAQ,aAAa,CAAC,EAAG6F,CAAyB,CAAC,EACvD7F,EAAI,QAAQ,mBAAmB,CAAC,CAAC,EAAG6F,CAAyB,CAAC,CAAC,EACxD,GACR,IAAK,YACJ,OAAA7F,EAAI,QAAQ,oBACX6F,EAAkB,EAAIA,EAAkB,EAAIA,CAAA,EAEtC,GACR,IAAK,aACJ,OAAA7F,EAAI,QAAQ,oBACX6F,EAAkBzR,EAAQ,OAAS,EAChCyR,EAAkB,EAClBA,CAAA,EAEG,GACR,IAAK,SACJ,OAAAhL,EAAM,eAAe,EACjBmF,EAAA,QAAQ,oBAAoB,EAAK,EAC9B,GACR,IAAK,QACJ,OAAAnF,EAAM,eAAe,EACjBiC,EAAM,OAAO,UACZkD,EAAA,QAAQ,gBAAgB6F,CAAe,EAErC,EACT,CACO,MAAA,EACR,CAGA,SAAS+K,GACR/V,EACAmF,EACU,OACN,GAAA,CAACA,EAAI,MAAQ,CAACA,EAAI,SAAW,CAACA,EAAI,KAAO,CAACA,EAAI,SAAiB,MAAA,GAE7D,MAAAlD,EAAQoB,GAAI8B,EAAI,KAAK,EAE3B,GADI,CAAClD,EAAM,OAAO,UACdjC,EAAM,MAAQ,UAAYA,EAAM,MAAQ,YAAoB,MAAA,GAE1D,MAAAkE,EAAUjC,EAAM,SAAS,QACzB1F,EAAiB0F,EAAM,SAAS,eAEhC+T,EAAiB/T,EAAM,OAAO,gBAAkB,CAAA,EAClD,GAAA1F,EAAe,KAAK,CAAC,CAACzC,EAAGJ,CAAG,IAAMsc,EAAe,SAAStc,CAAG,CAAC,EAC1D,MAAA,GAGR,GAAIwK,EAAS,CACN,KAAA,CAAC7H,EAAK3C,CAAG,EAAIwK,EACb2R,GAAWxa,EAAA8J,EAAI,IAAIA,EAAI,KAAK9I,CAAG,EAAE3C,CAAG,EAAE,EAAE,IAA7B,YAAA2B,EAAgC,MAUjD,GATIwa,GAAYA,EAAS,iBAAmBA,EAAS,cAIpD7V,EAAM,MAAQ,WACd6V,GAAA,YAAAA,EAAU,mBAAmBA,GAAA,YAAAA,EAAU,MAAM,SAI1C7V,EAAM,MAAQ,cAAe6V,GAAA,YAAAA,EAAU,kBAAmB,EACtD,MAAA,EAET,CAGI,GADJ7V,EAAM,eAAe,EACjBzD,EAAe,OAAS,EAAG,CAC9B,MAAMR,EAAWoE,GAAkBgF,EAAI,KAAM5I,CAAc,EAC3D4I,EAAI,SAAS,SAAU,CACtB,KAAMpJ,EAAS,IAAKM,GAAQA,EAAI,IAAKwC,GAASA,EAAK,KAAK,CAAC,EACzD,QAASsG,EAAI,QAAQ,IAAKxC,GAAMA,EAAE,KAAK,EACvC,SAAU,IAAA,CACV,CACF,CACO,MAAA,EACR,CAEA,SAASsT,GACRjW,EACAmF,EACA3K,EACAS,EACU,CACJ,MAAAgH,EAAQoB,GAAI8B,EAAI,KAAK,EACrBjB,EAAUjC,EAAM,SAAS,QACzB1F,EAAiB0F,EAAM,SAAS,eAGtC,GADIiC,GACA,CAACiB,EAAI,KAAa,MAAA,GAEtBnF,EAAM,eAAe,EAEf,MAAAkW,EAAc/Q,EAAI,QAAQ,YAAYnF,EAAO,CAACxF,EAAGS,CAAC,EAAGkK,EAAI,IAAI,EACnE,OAAI+Q,GACClW,EAAM,UACTmF,EAAI,QAAQ,mBACXA,EAAI,QAAQ,oBACX5I,EAAe,OAAS,EAAIA,EAAe,CAAC,EAAI,CAAC/B,EAAGS,CAAC,EACrDib,CACD,CAAA,EAEG/Q,EAAA,QAAQ,YAAY,EAAK,IAE7BA,EAAI,QAAQ,mBAAmB,CAAC+Q,CAAW,CAAC,EACxC/Q,EAAA,QAAQ,YAAY,EAAK,GAE1BA,EAAA,QAAQ,aAAa+Q,CAAW,GAC1BA,IAAgB,IAASlW,EAAM,MAAQ,WAAaxF,IAAM,IAChE2K,EAAA,QAAQ,oBAAoBlK,CAAC,EAC7BkK,EAAA,QAAQ,aAAa,EAAK,EAC1BA,EAAA,QAAQ,mBAAmB,CAAA,CAAE,EAC7BA,EAAA,QAAQ,YAAY,EAAK,GAEvB,EACR,CAEA,eAAegR,GACdnW,EACAmF,EACA3K,EACAS,EACmB,SACnB,GAAI,CAACkK,EAAI,MAAQ,CAACA,EAAI,IAAY,MAAA,GAE5B,MAAAlD,EAAQoB,GAAI8B,EAAI,KAAK,EACvB,GAAA,CAAClD,EAAM,OAAO,SAAiB,MAAA,GAE7B,MAAAiC,EAAUjC,EAAM,SAAS,QAC/B,GAAIiC,GAAWlE,EAAM,SAAiB,MAAA,GAItC,GAFAA,EAAM,eAAe,EAEjBkE,GAAWH,GAAOG,EAAS,CAAC1J,EAAGS,CAAC,CAAC,EAAG,CACvC,MAAMwG,EAAU0D,EAAI,KAAK3K,CAAC,EAAES,CAAC,EAAE,GACzB4a,GAAWxa,EAAA8J,EAAI,IAAI1D,CAAO,IAAf,YAAApG,EAAkB,MAC/Bwa,GACH,MAAMH,GAAgBG,EAAS,MAAO1Q,EAAK3K,EAAGS,CAAC,EAE5CkK,EAAA,QAAQ,YAAY,EAAK,EAC7B,MAAMV,GAAK,GACXnJ,EAAA6J,EAAI,iBAAJ,MAAA7J,EAAoB,OAAM,MAE1B6J,EAAI,QAAQ,YAAY,CAAC3K,EAAGS,CAAC,CAAC,EAGxB,MAAA,EACR,CAEA,SAASmb,GACRpW,EACAmF,EACA3K,EACAS,EACU,CACV,GAAI,CAACkK,EAAI,KAAa,MAAA,GAEtBnF,EAAM,eAAe,EACjBmF,EAAA,QAAQ,YAAY,EAAK,EACvB,MAAAkR,EAAYlR,EAAI,QAAQ,0BAC7B,CAAC3K,EAAGS,CAAC,EACLkK,EAAI,KACJnF,EAAM,QAAA,EAEP,OAAIqW,IACHlR,EAAI,QAAQ,mBAAmB,CAACkR,CAAS,CAAC,EACtClR,EAAA,QAAQ,aAAakR,CAAS,EAC9BhT,GAAI8B,EAAI,KAAK,EAAE,OAAO,UACrBA,EAAA,QAAQ,YAAYkR,CAAS,GAG5B,EACR,CAEA,SAASC,GACRtW,EACAmF,EACA3K,EACAS,EACU,CACJ,MAAAgH,EAAQoB,GAAI8B,EAAI,KAAK,EACvB,GAAA,CAAClD,EAAM,OAAO,SAAiB,MAAA,GAE7B,MAAAiC,EAAUjC,EAAM,SAAS,QAE/B,OACE,CAACiC,GAAYA,GAAWH,GAAOG,EAAS,CAAC1J,EAAGS,CAAC,CAAC,IAC/C+E,EAAM,IAAI,SAAW,GAErBmF,EAAI,QAAQ,YAAY,CAAC3K,EAAGS,CAAC,CAAC,EACvB,IAED,EACR,CAEA,eAAesb,GACdvW,EACAmF,EACmB,CACnB,GAAI,CAACA,EAAI,KAAa,MAAA,GAEhB,MAAAlD,EAAQoB,GAAI8B,EAAI,KAAK,EACrBvK,EAAWqH,EAAM,SAAS,SAC1B1F,EAAiB0F,EAAM,SAAS,eAEtC,GAAI,CAACrH,EAAiB,MAAA,GACtB,GAAIoF,EAAM,MAAQ,MAAQA,EAAM,SAAWA,EAAM,SAChD,OAAAA,EAAM,eAAe,EACjBzD,EAAe,OAAS,GACrB,MAAAD,GAAgB6I,EAAI,KAAM5I,CAAc,EAE3C4I,EAAA,QAAQ,eAAe,EAAI,EACxB,GAGF,KAAA,CAAC3K,EAAGS,CAAC,EAAIL,EAEf,OAAQoF,EAAM,IAAK,CAClB,IAAK,aACL,IAAK,YACL,IAAK,YACL,IAAK,UACJ,OAAOiW,GAAkBjW,EAAOmF,EAAK3K,EAAGS,CAAC,EAC1C,IAAK,SACA,OAACgH,EAAM,OAAO,UAClBjC,EAAM,eAAe,EACjBmF,EAAA,QAAQ,YAAY,EAAK,EACxBV,GAAA,EAAE,KAAK,IAAM,CACbU,EAAI,gBACPA,EAAI,eAAe,OACpB,CACA,EAEM,IAT4B,GAUpC,IAAK,QACJ,OAAO,MAAMgR,GAAiBnW,EAAOmF,EAAK3K,EAAGS,CAAC,EAC/C,IAAK,MACJ,OAAOmb,GAAepW,EAAOmF,EAAK3K,EAAGS,CAAC,EACvC,IAAK,SACL,IAAK,YACG,OAAA8a,GAAwB/V,EAAOmF,CAAG,EAC1C,QACC,OAAOmR,GAAmBtW,EAAOmF,EAAK3K,EAAGS,CAAC,CAC5C,CACD,CAEsB,eAAAsN,GACrBvI,EACAkC,EACgB,CACZ4T,GAAyB9V,EAAOkC,CAAO,GACvC6T,GAAwB/V,EAAOkC,CAAO,GACpC,MAAAqU,GAAuBvW,EAAOkC,CAAO,CAC5C,CC1SO,SAASsU,GACfvU,EACAwU,EACAC,EACAC,EACA1K,EACA2K,EACAC,EACe,CACf,MAAMC,EAAa,CAAC9W,EAAmB3D,EAAa3C,IAAsB,CACzE,MAAMoJ,EAAS9C,EAAM,OACf+W,EACJjU,EAA4B,OAAS,YACtCA,EAAO,QAAQ,wBAAwB,GACvCA,EAAO,QAAQ,YAAY,EAG3B9C,EAAM,kBAAkB,mBACvB4W,GAAoBld,IAAQ,IAC7Bqd,IAID/W,EAAM,eAAe,EACrBA,EAAM,gBAAgB,EAEtBiC,EAAM,eAAiB,CAAE,EAAGjC,EAAM,QAAS,EAAGA,EAAM,SAC9CiC,EAAA,WAAa,CAAC5F,EAAK3C,CAAG,EAExB,CAACsG,EAAM,UAAY,CAACA,EAAM,SAAW,CAACA,EAAM,UAC/C0W,EAAmB,CAAC,CAACra,EAAK3C,CAAG,CAAC,CAAC,EAClBid,EAAA,CAACta,EAAK3C,CAAG,CAAC,EACLuS,EAAAjM,EAAO3D,EAAK3C,CAAG,GAClC,EAGKsd,EAAoBhX,GAA4B,CACrD,MAAMnB,EAAQmB,EAAM,OAAuB,QAAQ,IAAI,EACvD,GAAI,CAACnB,EAAM,OAEX,MAAMxC,EAAM,SAASwC,EAAK,aAAa,UAAU,GAAK,GAAG,EACnDnF,EAAM,SAASmF,EAAK,aAAa,UAAU,GAAK,GAAG,EAEzD,GAAI,MAAMxC,CAAG,GAAK,MAAM3C,CAAG,EAAG,OAE9B,MAAMud,EAAkB/X,GAAoB+C,EAAM,WAAa,CAAC5F,EAAK3C,CAAG,CAAC,EACzEgd,EAAmBO,CAAe,EACrBN,EAAA,CAACta,EAAK3C,CAAG,CAAC,CAAA,EAGlBwd,EAAYlX,GAA4B,CACzC,CAACiC,EAAM,aAAeA,EAAM,WACbgK,EAAAjM,EAAOiC,EAAM,WAAW,CAAC,EAAGA,EAAM,WAAW,CAAC,CAAC,EACvDA,EAAM,aAAe4U,GAC/BA,EAAe,MAAM,EAGtB5U,EAAM,YAAc,GACpBwU,EAAgB,EAAK,EACrBxU,EAAM,WAAa,KACnBA,EAAM,eAAiB,IAAA,EAGjB,MAAA,CACN,kBAAmB6U,EAEnB,kBAAkB9W,EAAyB,CAC1C,GAAI,CAACiC,EAAM,YAAc,CAACA,EAAM,eAAgB,OAE5C,GAAA,EAAEjC,EAAM,QAAU,GAAI,CACzBkX,EAASlX,CAAK,EACd,MACD,CAEA,MAAMmX,EAAK,KAAK,IAAInX,EAAM,QAAUiC,EAAM,eAAe,CAAC,EACpDmV,EAAK,KAAK,IAAIpX,EAAM,QAAUiC,EAAM,eAAe,CAAC,EAEtD,CAACA,EAAM,cAAgBkV,EAAK,GAAKC,EAAK,KACzCnV,EAAM,YAAc,GACpBwU,EAAgB,EAAI,GAGjBxU,EAAM,aACT+U,EAAiBhX,CAAK,CAExB,EAEA,gBAAiBkX,CAAA,CAEnB,2bCqsBQG,EAAAlS,MAASA,EAAK,CAAA,EAAC,SAAW,GAAKA,EAAU,CAAA,GAAAkF,GAAAlF,CAAA,gJAa3BA,EAAa,EAAA,EACT,qBAAAA,MAAU,4RAflCG,EAiBKxC,EAAAoD,EAAAX,CAAA,kDAhBCJ,MAASA,EAAK,CAAA,EAAC,SAAW,GAAKA,EAAU,CAAA,kQAcvBS,EAAA,CAAA,EAAA,IAAA0R,EAAA,qBAAAnS,MAAU,6LAZ3BA,EAAK,CAAA,CAAA,4FAALA,EAAK,CAAA,CAAA,oHADVG,EAEKxC,EAAAoD,EAAAX,CAAA,EADJC,EAAaU,EAAAqR,CAAA,8BAATpS,EAAK,CAAA,CAAA,wEAgCgBA,EAAK,CAAA,CAAA,+DAALA,EAAK,CAAA,CAAA,2EAA/BG,EAAyCxC,EAAA0U,EAAAjS,CAAA,8BAAfJ,EAAK,CAAA,CAAA,2EAKP,EAAI,CAAA,CAAA,+bAcXA,EAAS,EAAA,EAAC,WAAW,4BACnBA,EAAS,EAAA,EAAC,aAAa,+FAK5BA,EAAc,EAAA,EAAC,SAASA,EAAC,GAAA,CAAA,oCAhBxBA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,QAAK,iBAAjBA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,OAkBfA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,QAAK,cAAbA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,ocARHA,EAAS,EAAA,EAAC,WAAW,wCACnBA,EAAS,EAAA,EAAC,aAAa,gLAK5BA,EAAc,EAAA,EAAC,SAASA,EAAC,GAAA,CAAA,0FAhBxBA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,kDAkBfA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,4KASA,CAAC,CAAA,CAAA,kRASN,MAAM,QAAQA,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,GAAA,CAAA,EAAIA,EAAQ,CAAA,OACpD,MACF,8BAGoB,uBAAAA,MAAe,SAAW,GACjDA,MAAe,CAAC,EAAE,CAAC,IAAM,GACzBA,EAAc,EAAA,EAAC,CAAC,EAAE,CAAC,IAAMA,EAAC,GAAA,SACnBA,EAAoB,EAAA,EACV,iBAAAA,MAAW,qBACd,cAAAA,MAAW,oDAEjBA,EAAW,EAAA,CAAA,yYAlBvBG,EAqBIxC,EAAAiG,EAAAxD,CAAA,EApBHC,EAmBKuD,EAAA7C,CAAA,0LAdO,MAAM,QAAQf,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,GAAA,CAAA,EAAIA,EAAQ,CAAA,yDAKlCS,EAAA,CAAA,EAAA,SAAAiF,EAAA,uBAAA1F,MAAe,SAAW,GACjDA,MAAe,CAAC,EAAE,CAAC,IAAM,GACzBA,EAAc,EAAA,EAAC,CAAC,EAAE,CAAC,IAAMA,EAAC,GAAA,uBACnBA,EAAoB,EAAA,8NAqDLA,EAAK,CAAA,CAAA,+DAALA,EAAK,CAAA,CAAA,2EAA/BG,EAAyCxC,EAAA0U,EAAAjS,CAAA,8BAAfJ,EAAK,CAAA,CAAA,uCAD3BkS,EAAAlS,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAAC2B,GAAA3B,CAAA,yFAA3BA,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,uJAKP,EAAI,CAAA,CAAA,gcAcXA,EAAS,EAAA,EAAC,WAAW,4BACnBA,EAAS,EAAA,EAAC,aAAa,+FAK5BA,EAAc,EAAA,EAAC,SAASA,EAAC,GAAA,CAAA,oCAhBxBA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,QAAK,iBAAjBA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,OAkBfA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,QAAK,cAAbA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,ocARHA,EAAS,EAAA,EAAC,WAAW,wCACnBA,EAAS,EAAA,EAAC,aAAa,gLAK5BA,EAAc,EAAA,EAAC,SAASA,EAAC,GAAA,CAAA,0FAhBxBA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,kDAkBfA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,gKAvBdA,EAAgB,EAAA,GAAA6E,GAAA,OAGd7E,EAAQ,EAAA,CAAA,aAAsBA,EAAE,GAAA,kBAArC,OAAI3K,GAAA,EAAA,4TAJP8K,EA4BIxC,EAAA2U,EAAAlS,CAAA,sFA3BEJ,EAAgB,EAAA,uIAGdA,EAAQ,EAAA,CAAA,mFAAb,OAAI3K,GAAA,6gBAgCW2K,EAAsB,EAAA,EAACA,EAAK,GAAA,EAAEA,EAAC,GAAA,CAAA,QACvCA,EAAS,EAAA,EAAC,uBAAyB,QAC1CA,EAAwB,EAAA,EAACA,EAAW,GAAA,CAAA,IAAA,OACjCA,MAAyBA,EAAK,GAAA,CAAA,EAC9BA,EAAK,GAAA,mGAMU,iBAAAA,MAAW,2HAMpB,QAAAA,EAAe,EAAA,EAAAA,EAAO,GAAA,CAAA,EAAAA,QAAG,yDAGxB,MAAM,QAAQA,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,GAAA,CAAA,EAAIA,EAAQ,CAAA,wDAI/CA,EAAc,EAAA,EAAC,SAASA,EAAC,GAAA,CAAA,6BAGd,qBAAAA,MAAW,qBACd,kBAAAA,MAAW,gDA5BlB,OAAAA,EAAe,EAAA,EAAAA,EAAO,GAAA,CAAA,EAAAA,QAAG,QAAK,SAA9BuS,EAAA,MAAAvS,EAAe,EAAA,EAAAA,EAAO,GAAA,CAAA,EAAAA,QAAG,OA6B5BA,MAAIA,EAAE,GAAA,CAAA,IAAA,SAANuS,EAAA,GAAAvS,MAAIA,EAAE,GAAA,CAAA,wRA5BAA,EAAsB,EAAA,EAACA,EAAK,GAAA,EAAEA,EAAC,GAAA,CAAA,gDACvCA,EAAS,EAAA,EAAC,uBAAyB,QAC1CA,EAAwB,EAAA,EAACA,EAAW,GAAA,CAAA,IAAA,OACjCA,MAAyBA,EAAK,GAAA,CAAA,EAC9BA,EAAK,GAAA,yNAYCS,EAAA,CAAA,EAAA,GAAAA,EAAA,CAAA,EAAA,UAAA+R,EAAA,QAAAxS,EAAe,EAAA,EAAAA,EAAO,GAAA,CAAA,EAAAA,QAAG,gHAGxB,MAAM,QAAQA,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,GAAA,CAAA,EAAIA,EAAQ,CAAA,wIAI/CA,EAAc,EAAA,EAAC,SAASA,EAAC,GAAA,CAAA,sJAxBxBwS,EAAA,MAAAxS,EAAe,EAAA,EAAAA,EAAO,GAAA,CAAA,EAAAA,QAAG,0DA6B5BwS,EAAA,GAAAxS,MAAIA,EAAE,GAAA,CAAA,4JAlCZA,EAAgB,EAAA,GAAA4E,GAAA5E,CAAA,OAGdA,EAAI,GAAA,CAAA,aAAsBA,EAAE,GAAA,kBAAjC,OAAI3K,GAAA,EAAA,kUAJ4C2K,EAAK,GAAA,EAAG,IAAM,CAAC,UAAlEG,EAwCIxC,EAAA2U,EAAAlS,CAAA,sFAvCEJ,EAAgB,EAAA,kKAGdA,EAAI,GAAA,CAAA,mFAJuCA,EAAK,GAAA,EAAG,IAAM,CAAC,oCAI/D,OAAI3K,GAAA,8TA3CG2K,EAAc,EAAA,EACR,eAAAA,EAAqB,EAAA,IAAA,MACpCA,QAAuB,wKANZA,EAAc,EAAA,IAAA,iBAAdA,EAAc,EAAA,GAENA,EAAY,EAAA,IAAA,yBAAZA,EAAY,EAAA,GACJA,EAAe,EAAA,IAAA,iCAAfA,EAAe,EAAA,2eAL7CG,EAuFKxC,EAAAoD,EAAAX,CAAA,gGAjFOJ,EAAc,EAAA,GACRS,EAAA,CAAA,EAAA,QAAAgS,EAAA,eAAAzS,EAAqB,EAAA,IAAA,MACpCA,QAAuB,0GANZA,EAAc,EAAA,mDAENA,EAAY,EAAA,4DACJA,EAAe,EAAA,wdAqF7CG,EAEQxC,EAAAsB,EAAAmB,CAAA,qBAFoCJ,EAAa,EAAA,CAAA,uUAYvD,IAAA9J,EAAA8J,QAAA,YAAA9J,EAAkB,MAAKC,EAAA6J,EAAoB,EAAA,IAApB,YAAA7J,EAAoB,IAAK,EAChD,IAAAoF,EAAAyE,QAAA,YAAAzE,EAAkB,MAAK6C,EAAA4B,EAAoB,EAAA,IAApB,YAAA5B,EAAoB,IAAK,MAC9C4B,EAAkB,EAAA,OAAQ0S,EAAA1S,EAAgB,EAAA,IAAhB,YAAA0S,EAAkB,MAAO,6LAmBtC,gBAAA,CAAA1S,OAAsBA,EAAI,EAAA,EAAC,OAAS,GAAKA,EAAQ,CAAA,EAClD,gBAAAA,EAAK,EAAA,EAAA,OAAS,KAAK2S,EAAA3S,EAAK,EAAA,EAAA,CAAC,IAAN,YAAA2S,EAAS,QAAS,GAAK3S,EAAQ,CAAA,aAE1D,QAAAA,EAAA,EAAA,SAON,OACY,cAAAA,EAAA,EAAA,SAKZ,OACa,eAAAA,EAAA,EAAA,IACb4S,EAAA5S,EAAS,EAAA,EAAC,WAAW,aAAa,KAAIA,EAAA,GAAA,CAAA,IAAtC,YAAA4S,EAEE,YAAa,KACf,KACY,cAAA5S,EAAA,EAAA,GACZA,EAAS,EAAA,EAAC,WAAW,aAAa,UAASA,EAAA,GAAA,CAAA,EAExC,GAAK,KAEA,UAAAA,EAAA,EAAA,SAOR,OACc,gBAAAA,EAAA,EAAA,SAKd,OACY,cAAAA,EAAA,EAAA,EACZA,EAAU,EAAA,EAAA,aAAa,eAAe,KAAIA,EAAA,GAAA,CAAA,EAG1C,qHAlEAS,EAAA,CAAA,EAAA,QAAAoS,EAAA,IAAA3c,EAAA8J,QAAA,YAAA9J,EAAkB,MAAKC,EAAA6J,EAAoB,EAAA,IAApB,YAAA7J,EAAoB,IAAK,GAChDsK,EAAA,CAAA,EAAA,QAAAoS,EAAA,IAAAtX,EAAAyE,QAAA,YAAAzE,EAAkB,MAAK6C,EAAA4B,EAAoB,EAAA,IAApB,YAAA5B,EAAoB,IAAK,sBAC9C4B,EAAkB,EAAA,OAAQ0S,EAAA1S,EAAgB,EAAA,IAAhB,YAAA0S,EAAkB,MAAO,sUAmBtCjS,EAAA,CAAA,EAAA,SAAAA,EAAA,CAAA,EAAA,QAAAoS,EAAA,gBAAA,CAAA7S,OAAsBA,EAAI,EAAA,EAAC,OAAS,GAAKA,EAAQ,CAAA,GAClDS,EAAA,CAAA,EAAA,WAAAoS,EAAA,gBAAA7S,EAAK,EAAA,EAAA,OAAS,KAAK2S,EAAA3S,EAAK,EAAA,EAAA,CAAC,IAAN,YAAA2S,EAAS,QAAS,GAAK3S,EAAQ,CAAA,6BAE1DS,EAAA,CAAA,EAAA,QAAAoS,EAAA,QAAA7S,EAAA,EAAA,SAON,QACYS,EAAA,CAAA,EAAA,QAAAoS,EAAA,cAAA7S,EAAA,EAAA,SAKZ,QACaS,EAAA,CAAA,EAAA,QAAAoS,EAAA,eAAA7S,EAAA,EAAA,IACb4S,EAAA5S,EAAS,EAAA,EAAC,WAAW,aAAa,KAAIA,EAAA,GAAA,CAAA,IAAtC,YAAA4S,EAEE,YAAa,KACf,MACYnS,EAAA,CAAA,EAAA,QAAAoS,EAAA,cAAA7S,EAAA,EAAA,GACZA,EAAS,EAAA,EAAC,WAAW,aAAa,UAASA,EAAA,GAAA,CAAA,EAExC,GAAK,MAEAS,EAAA,CAAA,EAAA,QAAAoS,EAAA,UAAA7S,EAAA,EAAA,SAOR,QACcS,EAAA,CAAA,EAAA,QAAAoS,EAAA,gBAAA7S,EAAA,EAAA,SAKd,QACYS,EAAA,CAAA,EAAA,QAAAoS,EAAA,cAAA7S,EAAA,EAAA,EACZA,EAAU,EAAA,EAAA,aAAa,eAAe,KAAIA,EAAA,GAAA,CAAA,EAG1C,6LAxSEoF,GAAApF,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAAKA,EAAU,CAAA,GAAKA,EAA0B,EAAA,GAAAA,EAAoB,EAAA,GAAAA,QAAgB,SAAMiF,GAAAjF,CAAA,EAmCjHqF,EAAArF,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAACgF,GAAAhF,CAAA,IAKzBA,EAAgB,EAAA,GAAA+E,GAAA,QAGd/E,EAAQ,EAAA,CAAA,cAAsBA,EAAE,GAAA,mBAArC,OAAI3K,GAAA,EAAA,sDA4BD2K,EAAgB,EAAA,GAAA8E,GAAA,QAGd9E,EAAG,EAAA,CAAA,cAAsBA,EAAE,GAAA,mBAAhC,OAAI3K,GAAA,EAAA,uHA8BF,UACE,iBACO,iBACA,cAmBH,WAAA2K,MAAK,0BAA0B,8JA2FvCA,EAAkB,EAAA,GAAA2E,GAAA3E,CAAA,KAOpBA,EAAI,EAAA,EAAC,SAAW,GAAKA,EAAQ,CAAA,GAAIA,EAAS,CAAA,EAAC,CAAC,IAAM,WAAS0E,GAAA1E,CAAA,EAI3D8S,GAAA9S,OAAoBA,EAAkB,EAAA,IAAA6B,GAAA7B,CAAA,msCA3M1BA,EAAY,EAAA,EAAA,IAAA,wDAFXA,EAAW,EAAA,CAAA,iBACXA,EAAI,EAAA,CAAA,EAEHqB,EAAAoE,EAAA,YAAAzF,OAAoBA,EAAkB,EAAA,CAAA,wDA3BzDG,EAgOKxC,EAAA6H,EAAApF,CAAA,wBA3MJC,EA0MKmF,EAAAC,CAAA,EA5LJpF,EAkEOoF,EAAAsN,CAAA,wBA9DN1S,EA8BO0S,EAAA7K,CAAA,EA7BN7H,EA4BI6H,EAAA8K,CAAA,gFAEL3S,EA8BO0S,EAAA5K,CAAA,EA7BN9H,EA4BI8H,EAAA8K,CAAA,iRAtEQhP,GAAAjE,QAAAA,EAAiB,EAAA,EAAA,MAAA,KAAA,SAAA,8BACnBiE,GAAAjE,QAAAA,EAAe,EAAA,EAAA,MAAA,KAAA,SAAA,iCACZiE,GAAAjE,QAAAA,EAAe,EAAA,EAAA,MAAA,KAAA,SAAA,uBA9BzBA,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAAKA,EAAU,CAAA,GAAKA,EAA0B,EAAA,GAAAA,EAAoB,EAAA,GAAAA,QAAgB,8GAmC3GA,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,yDAKxBA,EAAgB,EAAA,wIAGdA,EAAQ,EAAA,CAAA,mDA4BVA,EAAgB,EAAA,uIAGdA,EAAG,EAAA,CAAA,2JAoDAS,EAAA,CAAA,EAAA,OAAAyS,EAAA,WAAAlT,MAAK,0BAA0B,wIA2FvCA,EAAkB,EAAA,yFAhMRA,EAAY,EAAA,EAAA,IAAA,kCAFXA,EAAW,EAAA,CAAA,kCACXA,EAAI,EAAA,CAAA,oBAEHqB,EAAAoE,EAAA,YAAAzF,OAAoBA,EAAkB,EAAA,CAAA,EAsMpDA,EAAI,EAAA,EAAC,SAAW,GAAKA,EAAQ,CAAA,GAAIA,EAAS,CAAA,EAAC,CAAC,IAAM,uIAIlDA,OAAoBA,EAAkB,EAAA,0JAzLpC,OAAI3K,GAAA,gCA+BJ,OAAIA,GAAA,uYA/pBD,SAAA+H,IAAA,CACD,OAAA,KAAK,SAAS,SAAS,EAAE,EAAE,UAAU,EAAG,EAAE,WA6TzC0I,GAAe/K,EAAA,2BACIA,CAAK,aAiabpG,GAAC,wDAr4BV,CAAA,SAAAoJ,CAAA,EAAA2C,GACA,MAAAyS,EAAuB,IAAA,EAAAzS,GACvB,WAAA0S,EAAa,EAAA,EAAA1S,EACb,CAAA,QAAAtM,EAAA,EAAA,EAAAsM,EACA,CAAA,OAAAwP,EAAA,EAAA,EAAAxP,EACA,CAAA,UAAAwF,CAAA,EAAAxF,EACA,CAAA,UAAAkN,CAAA,EAAAlN,EACA,CAAA,iBAAA4B,CAAA,EAAA5B,EAKA,CAAA,WAAAgC,GAAA,EAAA,EAAAhC,GAEA,SAAAxF,EAAW,EAAA,EAAAwF,GACX,KAAA0G,EAAO,EAAA,EAAA1G,EACP,CAAA,KAAA2S,CAAA,EAAA3S,EACA,CAAA,KAAAiC,EAAA,EAAAjC,GAEA,WAAA4H,GAAa,GAAA,EAAA5H,GACb,YAAA6B,EAAc,EAAA,EAAA7B,EACd,CAAA,cAAA4S,GAAA,EAAA,EAAA5S,GACA,iBAAA+Q,GAAmB,EAAA,EAAA/Q,EACnB,CAAA,OAAA6S,CAAA,EAAA7S,EACA,CAAA,eAAA8S,EAAA,EAAA9S,GACA,uBAAAmO,EAAyB,EAAA,EAAAnO,GACzB,iBAAAoO,GAAmB,EAAA,EAAApO,GACnB,gBAAAlC,EAAkB,EAAA,EAAAkC,GAClB,UAAA+B,EAAgC,MAAA,EAAA/B,GAChC,YAAAqO,EAA4C,MAAA,EAAArO,GAC5C,eAAA+S,EAAiB,CAAA,EAAA/S,EACjB,CAAA,eAAAmQ,GAAA,EAAA,EAAAnQ,GACA,WAAAsO,GAAa,EAAA,EAAAtO,QAElBgT,GAAS/T,GAAA,CACd,uBAAAkP,EACA,iBAAAC,GACA,YAAAC,EACA,iBAAA0C,GACA,SAAAvW,EACA,eAAAuY,EACA,WAAAL,EACA,YAAA7Q,EACA,KAAA6E,EACA,WAAAkB,GACA,cAAAgL,GACA,UAAA7Q,EACA,eAAAoO,MAGO,MAAO8C,EAAU,QAASC,CAAe,EAAAF,wBAgBjD5I,GAAA,IAAA,CACCjK,EAAA,GAAA6S,GAAO,eAAiBzX,GAAAyX,EAAA,EACxB7S,EAAA,GAAA6S,GAAO,YAAcG,GAAAH,EAAA,EACrB7S,EAAA,GAAA6S,GAAO,WAAaI,GAAAJ,EAAA,EACpB7S,EAAA,GAAA6S,GAAO,QAAUK,GAAAL,EAAA,EACjB7S,EAAA,GAAA6S,GAAO,SAAWjV,GAAAiV,EAAA,EAClBM,KAEM,MAAAC,EAAA,IAAe,qBAAsBC,GAAA,CAC1CA,EAAQ,QAASC,IAAA,CACZA,GAAM,gBAAmB,CAAAC,SAC5BC,GAAmB,EAAA,EAEpBD,GAAaD,GAAM,mBAGrBF,EAAS,QAAQhY,EAAM,EACvB,SAAS,iBAAiB,QAASD,EAAoB,EACvD,OAAO,iBAAiB,SAAUsY,EAAa,QAEzCC,EAAmB1Z,GAAA,EACpB+H,IAAe4R,KAClBC,EAAgB5Z,CAAK,GAGvB,gBAAS,iBAAiB,UAAW0Z,CAAe,OAGnDN,EAAS,WAAA,EACT,SAAS,oBAAoB,QAASjY,EAAoB,EAC1D,OAAO,oBAAoB,SAAUsY,EAAa,EAClD,SAAS,oBAAoB,UAAWC,CAAe,WAcnD9V,GAAWyE,KAOb,IAAA9G,GAAA,CAAA,EAIA+T,GAAA,CAAA,EACAuE,GAAW9E,GAAaxb,EAAS8R,EAAW9J,GAAKgB,EAAO,EACxDuX,GAAwBvgB,EACxBK,EAAA,CAAA,CAAA,CAAA,EAEAmgB,GACAC,GAAA,CAAA,CAAA,CAAA,EAMAC,GAAW,GACXC,GACAC,GAAA,CAAA,EAEJlK,GAAA,IAAA,CAICiK,GAHc,iBAAiB,SAAS,eAAe,EACrD,iBAAiB,gBAAgB,EACjC,OAC4B,KAC9B,SAAS,gBAAgB,MAAM,YAC9B,wBACAA,EAAA,IAII,MAAAlB,GAAA,CAAe3c,EAAa3C,IACjC,UAAA,OAAA4B,IAAAD,EAAAzB,GAAA,YAAAA,EAAOyC,KAAP,YAAAhB,EAAc3B,KAAd,YAAA4B,GAAoB,OAEf2d,GAAcvf,IACnBE,GAAA,YAAAA,EAAM,IAAKyC,UAAQ,OAAAhB,EAAAgB,EAAI3C,CAAG,IAAP,YAAA2B,EAAU,UAAK,GAE7B6d,GAAW7c,UAChB,QAAAhB,EAAAzB,GAAA,YAAAA,EAAOyC,KAAP,YAAAhB,EAAa,IAAKwD,GAASA,EAAK,SAAK,QAa3B,cAAAnE,GAAmC,IAAA,EAAAmL,GACnC,QAAAlL,GAA6B,IAAA,EAAAkL,EA4GpCnC,GAAmBmW,GAAS,IAAKlX,GAAMA,EAAE,KAAK,EAC9Cc,GAAgB7J,EAAK,IAAKyC,GAAQA,EAAI,IAAKwC,GAAS,OAAOA,EAAK,KAAK,CAAA,CAAA,EAyBhE,SAAAub,GAAY1gB,EAAaU,EAAA,CACjC2e,EAAW,YAAYrf,EAAKU,CAAS,EACrCT,GAAUC,EAAMc,GAAeC,EAAO,EAG9B,SAAA0f,IAAA,CACRtB,EAAW,iBAAA,EACXpf,GAAUC,EAAMc,GAAeC,EAAO,WAc9B2f,GACR5gB,EACAwJ,EACAC,EACAvG,GAAA,CAEAmc,EAAW,cAAcrf,EAAKwJ,EAAUC,EAAQvG,EAAK,EACrD1B,GAAYtB,EAAMc,GAAeC,EAAO,EAGhC,SAAA4f,IAAA,CACRxB,EAAW,mBAAA,EACX7d,GAAYtB,EAAMc,GAAeC,EAAO,EAG1B,eAAA6f,GAAYhgB,EAAWigB,EAAU,GAAA,CAC1C,CAAApa,GAAY0K,IAAgBvQ,GAAK6Q,EAAU,CAAC,IAAM,WACvD0N,EAAW,gBAAgBve,CAAC,EAGpB,SAAA0Q,GAAoBlL,EAAmBtG,EAAA,CAC3CsG,EAAM,kBAAkB,oBAG5BA,EAAM,eAAA,EACNA,EAAM,gBAAA,EACDK,IACL0Y,EAAW,YAAY,EAAK,EAC5BA,EAAW,oBAAoBrf,EAAK2G,CAAQ,EAC5Ce,GAAO,MAAA,aAGCgK,GAAgBpL,EAAA,CACnBK,IACL0Y,EAAW,gBAAgB/Y,EAAM,OAAO,GAAG,EAC3CoB,GAAO,MAAA,kBAGOkB,GAAQpC,EAAA,OAGlB,GAFJkB,GAAO,MAAA,EAEH2R,EAAU,CAAC,IAAM,UAAA,OAEf,MAAAvQ,EAAU,QAAMnH,EAAAzB,EAAK,CAAC,IAAN,YAAAyB,EAAS,SAAU9B,EAAQ,MAAM,EACrD,KAAK,CAAC,EACN,IAAA,CAAKO,GAAGU,KAAA,OACF2a,GAAM5S,KACZ,OAAAyD,EAAA,GAAAzE,GAAI4T,EAAG,EAAA,CAAM,KAAM,KAAM,MAAO,IAAA,EAAA5T,EAAA,EACvB,CAAA,GAAI4T,GAAK,MAAO,EAAA,IAGvBvb,EAAK,SAAW,EACnBoM,EAAA,GAAApM,EAAA,CAAQ4I,CAAO,CAAA,EACLtC,IAAA,QAAuBA,GAAS,GAAKA,GAAStG,EAAK,OAC7DA,EAAK,OAAOsG,EAAO,EAAGsC,CAAO,EAE7B5I,EAAK,KAAK4I,CAAO,EAGlBwD,EAAA,GAAApL,EAAA,CAAYsF,WAAsBA,EAAQtG,EAAK,OAAS,EAAG,CAAC,CAAA,iBAG9C6I,GAAQvC,EAAA,CAElB,GADJkB,GAAO,MAAA,EACHiK,EAAU,CAAC,IAAM,UAAA,OAEf,MAAAqP,EAAS3B,EAAW,QAAQnf,EAAML,EAASgJ,GAASrC,CAAK,EAE/Dwa,EAAO,KAAK,QAASre,GAAA,CACpBA,EAAI,QAASwC,IAAA,CACP0C,GAAI1C,GAAK,EAAE,GACfmH,EAAA,GAAAzE,GAAI1C,GAAK,EAAE,EAAM,CAAA,KAAM,KAAM,MAAO,IAAA,EAAA0C,EAAA,MAKvCyE,EAAA,GAAApM,EAAO8gB,EAAO,IAAA,EACd1U,EAAA,EAAAzM,EAAUmhB,EAAO,OAAA,EAEX,MAAAjW,GAAA,EAEN,sBAAA,IAAA,CACC+V,GAAYta,IAAA,OAAsBA,EAAQtG,EAAK,CAAC,EAAE,OAAS,EAAG,EAAI,EAC5D,MAAA+gB,EAAQvZ,GAAO,iBAAiB,OAAO,EAAE,CAAC,EAAE,YAClDA,GAAO,iBAAiB,OAAO,EAAE,CAAC,EAAE,UAAW,KAAMuZ,CAAA,CAAA,aAI9CxZ,GAAqBnB,EAAA,CACzB4a,GAA0B5a,EAAOoB,EAAM,IAC1C2X,EAAW,eAAA,OACXhO,EAAc,EAAA,OACdC,EAAkB,EAAA,GAMhB,IAAA6P,GAMArB,GAAmB,GAKnB3Z,GAAA,CAAA,EACAuB,GACAI,GACAsZ,GAAyB,EACzBC,GAA0B,EAErB,SAAAC,IAAA,aACFC,IAAe5f,EAAAzB,EAAK,CAAC,IAAN,YAAAyB,EAAS,SAAU,EAKvC,GAJG6f,EAAU,aAAa,eAAe,OAAS,GAIlDJ,KAA2BlhB,EAAK,QAChCmhB,KAA4BE,GAC5BC,EAAU,WAAW,aAAa,OAAS,SAK5CJ,GAAyBlhB,EAAK,OAC9BmhB,GAA0BE,EAEpB,MAAAE,EAAStb,GAAM,IAAKuI,KAAOA,IAAA,YAAAA,GAAI,cAAe,CAAC,EACjD,GAAA+S,EAAO,SAAW,EAElB,CAAAvE,IACHxV,GAAO,MAAM,YAA0C,0BAAA,GAAA+Z,EAAO,CAAC,CAAA,IAAA,UAGvD3gB,GAAI,EAAGA,GAAI,GAAIA,KAClB,GAAA,CAAAie,GAAcje,EAAC,EACnB4G,GAAO,MAAM,+BAA+B5G,EAAC,EAAA,UACnCie,GAAcje,EAAC,EAAE,SAAS,GAAG,EAAA,OACjC4gB,GAAa,WAAW3C,GAAcje,EAAC,CAAA,EACvC6gB,GAAc,KAAK,MAAOD,GAAa,IAAOha,GAAO,WAAW,EACtEA,GAAO,MAAM,YAAA,gBAA4B5G,EAAC,GAAA,GAAO6gB,EAAW,IAAA,OAE5Dja,GAAO,MAAM,YAAA,gBAA4B5G,EAAC,GAAIie,GAAcje,EAAC,CAAA,EAI/D2gB,EAAO,SAASG,GAAO9gB,KAAA,CACjB,GAAA,CAAAie,GAAcje,EAAC,EAAA,OACb+gB,GAAsB,GAAA,KAAK,IAAID,GAAO,EAAE,CAAA,KAC9Cla,GAAO,MAAM,YAAA,gBAA4B5G,EAAC,GAAI+gB,EAAgB,UAS7DC,GACHnG,EAAO,MAAM,EAAI5H,GAAa4H,EAAO,OAAU,EAAE,EAAE,OAAS,GAAK,GAC9DoG,GAAkB,EAEb,SAAA9hB,GACRsH,EACAya,EACAC,EAAA,CAEM,MAAAjB,GAASjgB,GACdwG,EACAya,EACAC,EACAT,EAAU,WAAW,aACrBtgB,EACAC,EAAA,EAGDmL,EAAA,GAAApM,EAAO8gB,GAAO,IAAA,EACd1U,EAAA,GAAApL,EAAW8f,GAAO,QAAA,EAGV,SAAAxf,GACR+F,EACAya,EACAC,EAAA,cAEM,MAAAjB,GAASnf,GACd0F,EACAya,EACAC,EACAT,EAAU,aAAa,eACvBtgB,EACAC,IACAQ,GAAA6f,EAAU,aAAa,eAAvB,YAAA7f,GAAqC,MACrCC,GAAA4f,EAAU,aAAa,eAAvB,YAAA5f,GAAqC,eACrCoF,GAAAwa,EAAU,aAAa,eAAvB,YAAAxa,GAAqC,OAAA,EAEtCsF,EAAA,GAAApM,EAAO8gB,GAAO,IAAA,EACd1U,EAAA,GAAApL,EAAW8f,GAAO,QAAA,MAKfnB,GAAa,SAEXqC,GAAkBhf,GAAA,CACvBmc,EAAW,eAAenc,CAAK,GAM5B,IAAAif,GAAA,CAAA,WASKvT,GACRtI,EAAA,CAKQ,KAAA,CAAA,WAAA8b,EAAY,OAAAnd,GAAWqB,EAAM,OACrC4V,GAAiBkG,EAAYjD,GAAQla,CAAM,EAGnC,SAAAwM,GAAmBnL,EAAmBtG,EAAA,CAE1C,GADJsG,EAAM,gBAAA,EACF+b,GAAsBA,EAAmB,MAAQriB,EACpDqf,EAAW,uBAAuB,IAAI,aAEhCvR,EAAUxH,EAAM,OAAuB,QAAQ,IAAI,EACrD,GAAAwH,EAAA,CACG,MAAA7C,GAAO6C,EAAO,wBACpBuR,EAAW,wBACV,IAAArf,EACA,EAAGiL,GAAK,MACR,EAAGA,GAAK,MAAA,CAAA,IAMZqX,GAAA,IAAA,MACCrY,EAAkB,EAAA,aAGVsY,GAAc/b,EAAA,IAClBmL,EAAU,CAAC,IAAM,WACjBzR,EAAK,CAAC,EAAE,QAAU,EAAA,OAEhB,MAAA8gB,EAAS3B,EAAW,cAAcnf,EAAML,EAAS2G,CAAK,EAC5D8F,EAAA,GAAApM,EAAO8gB,EAAO,IAAA,EACd1U,EAAA,EAAAzM,EAAUmhB,EAAO,OAAA,EACjB1U,EAAA,GAAA6T,GAAW9E,GAAaxb,EAAS8R,EAAW9J,GAAKgB,EAAO,CAAA,EACxDwW,EAAW,qBAAqB,IAAI,EACpCA,EAAW,uBAAuB,IAAI,EACtCA,EAAW,aAAa,EAAK,EAC7BA,EAAW,mBAAA,CAAA,CAAA,EACXA,EAAW,YAAY,EAAK,WAGpBmD,GAAchc,EAAA,MACtBtG,EAAOmf,EAAW,cAAcnf,EAAMsG,CAAK,CAAA,EAC3C6Y,EAAW,qBAAqB,IAAI,EACpCA,EAAW,uBAAuB,IAAI,EAGnC,IAAAoD,GAqBK,SAAAC,IAAA,CACJ,GAAAlB,EAAU,sBAAwBhH,IAAgB,SAAA,CAC/C,MAAAmI,EAAA,CAAA,EACAC,EAAA,CAAA,EACAC,EAAA,CAAA,EAENvC,GAAe,QAAS3d,IAAA,CACjB,MAAAmgB,GAAA,CAAA,EACAC,GAAA,CAAA,EACAC,GAAA,CAAA,EAENrgB,GAAI,QAASwC,IAAA,CACZ2d,GAAS,KAAK3d,GAAK,KAAK,EACxB4d,GAAY,KACX5d,GAAK,gBAAkB,OACpBA,GAAK,cACL,OAAOA,GAAK,KAAK,CAAA,EAErB6d,GAAY,KAAK7d,GAAK,SAAW,EAAE,IAGpCwd,EAAc,KAAKG,EAAQ,EAC3BF,EAAwB,KAAKG,EAAW,EACxCF,EAAiB,KAAKG,EAAW,IAG5B,MAAAC,GAAA,CACL,KAAMN,EACN,QAASxC,GAAS,IAAKlX,IAAMA,GAAE,KAAK,EACpC,SAAA,CACC,cAAe2Z,EACf,QAASC,IAIX3Y,GAAS,SAAU+Y,EAAc,EAE5BhZ,GACJC,GAAS,OAAO,EAGjBmV,EAAW,cAAc,IAAI,GAI3B,IAAAjL,GACAD,GAAqB,GAEhB,SAAA+O,IAAA,CACR9O,GAAS,UACR,IAAK,CAAA,CAAA,EAIE,SAAA2L,IAAA,CACRV,EAAW,qBAAqB,IAAI,EACpCA,EAAW,uBAAuB,IAAI,EACtC/S,EAAA,GAAAzJ,EAAA,CAAA,CAAA,OACA3B,EAAW,EAAA,OACXsJ,EAAU,EAAA,OACVsV,GAAmB,EAAA,EACnBwB,KAGQ,SAAA6B,GAAW3c,EAAesD,EAAA,OAC5BsZ,EAAYtZ,IAAa,QAAUtD,EAAQA,EAAQ,EACzDoC,GAAQwa,CAAS,OACjB1Q,EAAmB,IAAA,OACnB2P,EAAqB,IAAA,EAGb,SAAAgB,GAAW7c,EAAesD,EAAA,OAC5B+H,EAAY/H,IAAa,OAAStD,EAAQA,EAAQ,EACxDuC,GAAQ8I,CAAS,OACjBa,EAAmB,IAAA,OACnB2P,EAAqB,IAAA,EAGN,SAAAiB,IAAA,CACfjE,EAAW,iBAAA,MAGRhR,GAAc,GACd4R,GAAsC,KACtCsD,GAAkD,KAEhD,MAAAC,GAAA,CACL,YAAAnV,GACA,WAAA4R,GACA,eAAAsD,EAAA,EASG,IAAAE,GAEK,SAAAhE,IAAA,MACRgE,GAAgB3G,GACf0G,GACCtgB,GAAAoJ,EAAA,GAAW+B,GAAcnL,CAAA,EACzBiD,GAAUkZ,EAAW,mBAAmBlZ,CAAK,EAC7ChB,GAASka,EAAW,aAAala,CAAI,EAAA,CACrCmB,EAAO3D,EAAK3C,IAAQqf,EAAW,kBAAkB/Y,EAAO3D,EAAK3C,CAAG,EACjEkd,GACAxV,EAAA,CAAA,EAUO,SAAAgc,GAAuB/gB,EAAa3C,EAAA,kBACnBwhB,EAAU,uBAAyB,UAEpC7f,GAAA2e,IAAA,YAAAA,GAAiB3d,KAAjB,MAAAhB,GAAwB3B,IACxCsgB,GAAe3d,CAAG,EAAE3C,CAAG,EAAE,gBAAkB,OAC/CsgB,GAAe3d,CAAG,EAAE3C,CAAG,EAAE,cACzB,OAAOsgB,GAAe3d,CAAG,EAAE3C,CAAG,EAAE,KAAK,GAGrC4B,GAAA1B,GAAA,YAAAA,EAAOyC,KAAP,MAAAf,GAAc5B,GACVE,EAAKyC,CAAG,EAAE3C,CAAG,EAAE,gBAAkB,OACrCE,EAAKyC,CAAG,EAAE3C,CAAG,EAAE,cACf,OAAOE,EAAKyC,CAAG,EAAE3C,CAAG,EAAE,KAAK,EAGxB,gBAIuBshB,uBAaD1e,GAAgB1C,EAAM,IAAI,EAGzCyjB,GAAA/e,GAAMya,EAAW,cAAcza,EAAE,MAAM,mEAgCpCub,GAASrf,CAAC,EAAE,MAAKoC,CAAA,IAAjBid,GAASrf,CAAC,EAAE,MAAKoC,4EAkBpB2E,GAAIzG,CAAE,EAAE,MAAK8B,CAAA,IAAb2E,GAAIzG,CAAE,EAAE,MAAK8B,yDAYMiD,GAAM5E,CAAC,EAAAyN,wDAzCtBlH,GAAKkH,uBA0JGpK,EAAG7B,EAAGC,IAAM4gB,EAAkBhf,EAAG7B,EAAGC,CAAC,qBAT7C+P,EAAA,GAAA,UAAAuN,GAAe9Z,CAAK,EAAEjF,CAAC,EAAE,MAAK2B,CAAA,IAA9Bod,GAAe9Z,CAAK,EAAEjF,CAAC,EAAE,MAAK2B,6HA6BjC6P,EAAA,GAAA,UAAAlL,GAAIzG,CAAE,EAAA8B,CAAA,IAAN2E,GAAIzG,CAAE,EAAA8B,4CA1DHid,GAASrf,CAAC,EAAE,MAAKoC,CAAA,IAAjBid,GAASrf,CAAC,EAAE,MAAKoC,4EAkBpB2E,GAAIzG,CAAE,EAAE,MAAK8B,CAAA,IAAb2E,GAAIzG,CAAE,EAAE,MAAK8B,2BAtCbod,GAAcpd,0HAEN4e,GAAY5e,0BACJ6e,GAAe7e,kGAxBjC,MAAA2gB,GAAA,CAAA,CAAA,OAAAC,CAAM,IACjBvf,GACCuf,EAAO,KACNjf,IACAyH,EAAA,GAAA6T,GAAW9E,GACVxW,EAAK,IAAKoE,GAAMA,GAAK,EAAE,EACvB0I,EACA9J,GACAgB,EAAA,CAAA,EAEMsX,IAEP4D,GAAI,CACJzX,EAAA,EAAAqP,EAASoI,CAAI,+CArGNrc,GAAMsH,aAMJ,MAAAgV,GAAApf,GAAMiK,GAAejK,EAAGua,EAAM,SAsMZvW,KAUPqb,GAAA,IAAAd,IAAWzQ,GAAA,YAAAA,EAAkB,MAAQ,GAAG,OAAO,EAC/CwR,GAAA,IAAAf,IAAWzQ,GAAA,YAAAA,EAAkB,MAAQ,GAAG,OAAO,SAEtE2Q,IACC3Q,GAAA,YAAAA,EAAkB,OAAO2P,GAAA,YAAAA,EAAoB,MAAG,GAChD,MAAK,SAGNgB,IACC3Q,GAAA,YAAAA,EAAkB,OAAO2P,GAAA,YAAAA,EAAoB,MAAG,GAChD,OAAM,EAEa8B,GAAA,IAAA3B,IAAc9P,GAAA,YAAAA,EAAkB,QAAS,SAE7D6P,IAAc7P,GAAA,YAAAA,EAAkB,OAAO2P,GAAA,YAAAA,EAAoB,MAAG,EAAM,KAMjE3hB,GAAS,CACN2hB,IACH3B,GAAY2B,EAAmB,IAAK3hB,CAAS,EAC7C2e,EAAW,uBAAuB,IAAI,YAMvCsB,KACAtB,EAAW,uBAAuB,IAAI,MAKrCtf,GAASA,EAAK,QAASsiB,GAAA,YAAAA,EAAoB,MAAG,OAK9CtiB,GAASA,EAAK,QAASsiB,GAAA,YAAAA,EAAoB,MAAG,QAI9C7Y,EAAUC,EAAQvG,IAAK,CACpBmf,IACHzB,GAAcyB,EAAmB,IAAK7Y,EAAUC,EAAQvG,CAAK,EAC7Dmc,EAAW,uBAAuB,IAAI,YAMvCwB,KACAxB,EAAW,uBAAuB,IAAI,MAKrCrc,GAAMA,EAAE,QAASqf,GAAA,YAAAA,EAAoB,MAAG,usCA5+B1C/V,EAAA,GAAAzJ,EAAiB2e,EAAU,SAAS,cAAA,mBACpClV,EAAA,GAAApL,EAAWsgB,EAAU,SAAS,QAAA,mBAC9BlV,EAAA,GAAA9B,EAAUgX,EAAU,SAAS,OAAA,mBAC7BlV,EAAA,GAAA+E,EAAcmQ,EAAU,SAAS,WAAA,mBACjClV,EAAA,GAAAgF,EAAkBkQ,EAAU,SAAS,eAAA,mBACrClV,EAAA,GAAAoG,EAAmB8O,EAAU,SAAS,gBAAA,mBACtClV,EAAA,GAAA+V,EAAqBb,EAAU,SAAS,kBAAA,mBACxClV,EAAA,GAAAmG,EAAa+O,EAAU,SAAS,UAAA,gEAiH3B,CAAAnX,GAAOsR,EAAQ0E,EAAO,EAAA,CACzB,GAAA3Y,GAAA,OAEG0c,GACLzI,EAAO,SAAW,GAAMA,EAAO,SAAW,GAAKA,EAAO,CAAC,EAAE,SAAW,EAC/D0I,GACLhE,cACC1E,EAAO,SAAW0E,GAAQ,QACzB1E,EAAO,CAAC,GAAK0E,GAAQ,CAAC,GAAK1E,EAAO,CAAC,EAAE,SAAW0E,GAAQ,CAAC,EAAE,WAE1D+D,IAAYC,GAAAA,SACNvjB,GAAI,EAAGA,GAAI,GAAIA,KACvB4G,GAAO,MAAM,+BAA+B5G,EAAC,EAAA,EAE9CsgB,GAAyB,EACzBC,GAA0B,OAC1BvB,GAAmB,EAAA,SAKfsE,EACLzI,EAAO,SAAW,GAAMA,EAAO,SAAW,GAAKA,EAAO,CAAC,EAAE,SAAW,EAC/D0I,GACLhE,cACC1E,EAAO,SAAW0E,GAAQ,QACzB1E,EAAO,CAAC,GAAK0E,GAAQ,CAAC,GAAK1E,EAAO,CAAC,EAAE,SAAW0E,GAAQ,CAAC,EAAE,QAE9D/T,EAAA,GAAApM,EAAOwb,GACNC,EACA9T,GACA+T,GACA/S,GACA7H,EAAA,CAAA,EAEDsL,EAAA,GAAA+T,GAAU,KAAK,MAAM,KAAK,UAAU1E,CAAM,CAAA,CAAA,EAEtCyI,GAAYC,GACfhF,EAAW,iBAAA,EACDmC,EAAU,WAAW,aAAa,OAAS,EACrDvhB,GAAUC,EAAMc,GAAeC,EAAO,GAEtCoe,EAAW,YAAA,GAAgB,KAAK,EAChCA,EAAW,iBAAA,GAGRmC,EAAU,aAAa,eAAe,OAAS,EAClDhgB,GAAYtB,EAAMc,GAAeC,EAAO,EAExCoe,EAAW,mBAAA,EAGRmC,EAAU,sBACbnC,EAAW,cAAc,IAAI,EAG1B3X,IAAUvB,GAAM,OAAS,IAAMie,GAAYC,UAC9CvE,GAAmB,EAAA,gDAxKrBxT,EAAA,GAAG8E,EACF8N,KAAkBvd,EAAAzB,GAAA,YAAAA,EAAO,KAAP,MAAAyB,EAAW,QAC1B,KAAK,IAAIud,EAAgBhf,EAAK,CAAC,EAAE,MAAM,EACvC,CAAA,gDA+FEmK,GAAOxK,EAASugB,EAAW,IAC/B9T,EAAA,GAAA6T,GAAW9E,GAAaxb,EAAS8R,EAAW9J,GAAKgB,EAAO,CAAA,EACxDyD,EAAA,GAAA8T,GAAc,KAAK,MAAM,KAAK,UAAUvgB,CAAO,CAAA,CAAA,kDA3D5CK,GAAQigB,IAAYtY,MACvByE,EAAA,GAAA6S,GAAO,KAAOjf,EAAAif,EAAA,EACd7S,EAAA,GAAA6S,GAAO,QAAUgB,GAAAhB,EAAA,EACjB7S,EAAA,GAAA6S,GAAO,IAAMtX,GAAAsX,EAAA,EACb7S,EAAA,GAAA6S,GAAO,cAAgBne,GAAAme,EAAA,EACvB7S,EAAA,GAAA6S,GAAO,QAAUle,GAAAke,EAAA,+DA8HnB,GAAOqC,EAAU,uBAAyB,OAAA,OACnC8C,EAAe,IAAA,IACrBhY,EAAA,GAAAmU,GAAA,CAAA,CAAA,EAEAvgB,EAAK,SAASyC,GAAK4hB,KAAA,CAEjB5hB,GAAI,KAAMwC,IACT,QAAA,cAAOA,IAAA,YAAAA,GAAM,KAAK,EAChB,YAAA,EACA,WAASxD,GAAA6f,EAAU,uBAAV,YAAA7f,GAAgC,gBAAiB,EAAE,EAAA,GAG/D8e,GAAyB,KAAK8D,EAAO,EAEtC5hB,GAAI,SAASwC,GAAMqf,KAAA,QAClBF,EAAS,IAAInf,GAAK,GAAA,CACjB,MAAOA,GAAK,MACZ,cACCA,GAAK,gBAAkB,OACpBA,GAAK,cACL,OAAOA,GAAK,KAAK,EACrB,UAASxD,GAAAV,IAAA,YAAAA,GAAUsjB,MAAV,YAAA5iB,GAAqB6iB,MAAY,eAKvCC,GAAWpF,EAAW,YAAYnf,CAAI,EAE5CoM,EAAA,GAAAgU,GAAiBmE,GAAS,IAAK9hB,IAC9BA,GAAI,IAAKwC,IAAA,OACFmF,GAAWga,EAAS,IAAInf,GAAK,EAAE,SAEjC,GAAAA,GACH,eACCmF,IAAA,YAAAA,GAAU,iBAAkB,OACzBA,GAAS,cACT,OAAOnF,GAAK,KAAK,EACrB,SAASmF,IAAA,YAAAA,GAAU,UAAW,aAKjCgC,EAAA,GAAAmU,GAAA,CAAA,CAAA,qDAOIvgB,GAAQigB,MACXd,EAAW,eACVnf,EAAK,IAAK,CAAAyC,EAAK+hB,KACd/hB,EAAI,KAAKwC,GAAMwf,KAAA,CACR,MAAAC,GAAQ,MAAM,QAAQpb,CAAQ,EAAIA,EAASmb,EAAM,EAAInb,SAEvD,GAAArE,GACH,MAAO2W,GAAmB3W,GAAK,MAAOyf,EAAK,MAI9CzE,GACApW,GACAC,GACAC,EACAC,SAEDH,GAAgB7J,EAAK,IAAKyC,GAAQA,EAAI,IAAKwC,IAAS,OAAOA,GAAK,KAAK,CAAA,CAAA,CAAA,EACrEmH,EAAA,GAAAtC,GAAmBmW,GAAS,IAAKlX,GAAMA,EAAE,KAAK,CAAA,gEAe3CuY,EAAU,aAAa,eAAe,OAAS,GAClDhgB,GAAYtB,EAAMc,GAAeC,EAAO,EAGrCugB,EAAU,WAAW,aAAa,OAAS,IAC9CvhB,GAAUC,EAAMc,GAAeC,EAAO,EACtCoe,EAAW,iBAAiBnf,CAAI,4BAoG/BoM,EAAA,GAAAnK,EAAMD,GAAQhC,CAAI,CAAA,iDAGdiG,GAAM,CAAC,IAAKvE,EAAAuE,GAAM,CAAC,IAAP,MAAAvE,EAAU,cAC5B,aAAauf,EAAkB,OAC/BA,GAAqB,WAAA,IAAiBG,KAAmB,GAAG,CAAA,kDAItDnb,GAAM,CAAC,GAAM,CAAA2Z,KACnBwB,UACAxB,GAAmB,EAAA,2BAiGjBxT,EAAA,GAAAuY,EAAA,CAAA,CAAmB3jB,GAAYA,EAAS,CAAC,CAAA,0CAcvCuR,GAAe,CAAApI,GAAOxH,EAAgBsf,EAAuB,GAChED,GAAe,EAAK,OAErBC,GAA0Btf,CAAA,2BAwDpB3B,IAAa,SAAOuhB,GAAuBvhB,CAAA,mDAE3CA,IAAa,GAAA,CACb,MAAA4jB,EAAYld,GACjB1G,EACAhB,EACA2H,GACAH,GACAI,EAAA,EAED,SAAS,gBAAgB,MAAM,YAC9B,qBACAgd,EAAU,OAAA,EAEX,SAAS,gBAAgB,MAAM,YAC9B,qBACAA,EAAU,SAAW,KAAA,2BAoHhBpd,IAAQ+X,6BAEfnT,EAAA,GAAGsX,GAAoBH,IAAA,YAAAA,GAAe,qBAAA,IAAA,6BACtCnX,EAAA,GAAGyY,GAAoBtB,IAAA,YAAAA,GAAe,qBAAA,IAAA,6BACtCnX,EAAA,GAAG4T,GAAkBuD,IAAA,YAAAA,GAAe,mBAAA,IAAA,MAvBnCnX,EAAA,GAAA+B,GAAcmV,GAAW,WAAA,EACzBvD,GAAauD,GAAW,WACxBD,GAAiBC,GAAW,68BCprBhB,WAAA/X,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,EAAA,uMASV,OAAAA,KAAM,oBACC9J,EAAA8J,EAAK,CAAA,EAAC,WAAN,YAAA9J,EAAgB,uBACtBC,EAAA6J,EAAK,CAAA,EAAC,WAAN,YAAA7J,EAAgB,QAChB,QAAA6J,KAAM,kFAeLA,EAAW,EAAA,mBAEf,KAAAA,MAAO,+MAYC,WAAA,CAAA,MAAOuZ,EAAK,oaA3Cd,WAAAvZ,MAAO,YACbS,EAAA,CAAA,EAAA,QAAA,CAAA,KAAAT,MAAO,IAAI,mBACbA,EAAc,EAAA,CAAA,uKASVS,EAAA,CAAA,EAAA,IAAA+Y,EAAA,OAAAxZ,KAAM,gCACC9J,EAAA8J,EAAK,CAAA,EAAC,WAAN,YAAA9J,EAAgB,mCACtBC,EAAA6J,EAAK,CAAA,EAAC,WAAN,YAAA7J,EAAgB,SAChBsK,EAAA,CAAA,EAAA,IAAA+Y,EAAA,QAAAxZ,KAAM,+JAeLA,EAAW,EAAA,qCAEfS,EAAA,CAAA,EAAA,SAAA+Y,EAAA,KAAAxZ,MAAO,gsBAzCL,4CAGE,moBAnDA,QAAAyZ,EAAU,EAAA,EAAA/Y,EACV,CAAA,aAAAgZ,EAAA,EAAA,EAAAhZ,GACA,QAAA2I,EAAU,EAAA,EAAA3I,EACV,CAAA,MAAAjJ,EAAA,CACV,KAAQ,CAAA,CAAA,GAAI,GAAI,EAAE,CAAA,EAClB,QAAU,CAAA,IAAK,IAAK,GAAG,EACvB,SAAU,UAEA,gBAAA+G,EAAkB,EAAA,EAAAkC,EAClB,CAAA,UAAAwF,CAAA,EAAAxF,EACA,CAAA,UAAAkN,CAAA,EAAAlN,GACA,MAAAyS,EAAuB,IAAA,EAAAzS,GACvB,WAAA0S,EAAa,EAAA,EAAA1S,EACb,CAAA,KAAA0G,CAAA,EAAA1G,EACA,CAAA,SAAA3C,CAAA,EAAA2C,GACA,MAAAiZ,EAAuB,IAAA,EAAAjZ,GACvB,UAAAkZ,EAAgC,MAAA,EAAAlZ,EAChC,CAAA,KAAA2S,CAAA,EAAA3S,GAEA,YAAA6B,EAAc,EAAA,EAAA7B,EACd,CAAA,cAAA4S,EAAA,EAAA,EAAA5S,EACA,CAAA,OAAAmZ,CAAA,EAAAnZ,EAOA,CAAA,iBAAA4B,CAAA,EAAA5B,GAKA,WAAA4H,EAAiC,MAAA,EAAA5H,EACjC,CAAA,eAAAoZ,CAAA,EAAApZ,EACA,CAAA,YAAAqZ,CAAA,EAAArZ,GACA,uBAAAmO,EAAyB,EAAA,EAAAnO,GACzB,UAAA+B,EAAgC,MAAA,EAAA/B,GAChC,iBAAAoO,GAAmB,EAAA,EAAApO,GACnB,iBAAA+Q,EAAmB,EAAA,EAAA/Q,GACnB,YAAAqO,EAA4C,MAAA,EAAArO,GAC5C,eAAA+S,EAAiB,CAAA,EAAA/S,EACjB,CAAA,eAAAmQ,GAAA,EAAA,EAAAnQ,GACA,WAAAsO,GAAa,EAAA,EAAAtO,EAkBA,MAAAsZ,EAAA,IAAAH,EAAO,SAAS,eAAgBC,CAAc,EA+BzDrY,GAAA,IAAAwY,IAASJ,EAAO,OAAO,UAAUI,CAAI,EAC7BC,GAAA,IAAAD,IAASJ,EAAO,OAAO,UAAUI,CAAI,oCAnB7C9gB,GAAC,CACZ0H,EAAA,EAAApJ,EAAM,KAAO0B,EAAE,OAAO,KAAI1B,CAAA,EAC1BoJ,EAAA,EAAApJ,EAAM,QAAU0B,EAAE,OAAO,QAAO1B,CAAA,EAChCoiB,EAAO,SAAS,QAAQ,GAEdM,EAAAhhB,GAAM0gB,EAAO,SAAS,OAAO,KAC5B1gB,GAAM0gB,EAAO,SAAS,SAAU1gB,EAAE,MAAM,MAClC,OAAAkf,KAAM,CACvBxX,EAAA,EAAAmO,GAAaqJ,CAAM"}