import{R as i}from"./index-Dpxo-yl_.js";import{f as s}from"./KHR_interactivity-DTxiAnOo.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./declarationMapper-BZjsjg7g.js";import"./objectModelMapping-BR4RdEzn.js";class n extends s{constructor(t){super(t),this.config=t,this.executionSignals=[],this.setNumberOfOutputSignals(this.config.outputSignalCount)}_execute(t){for(let e=0;e<this.executionSignals.length;e++)this.executionSignals[e]._activateSignal(t)}setNumberOfOutputSignals(t=1){for(;this.executionSignals.length>t;){const e=this.executionSignals.pop();e&&(e.disconnectFromAll(),this._unregisterSignalOutput(e.name))}for(;this.executionSignals.length<t;)this.executionSignals.push(this._registerSignalOutput(`out_${this.executionSignals.length}`))}getClassName(){return"FlowGraphSequenceBlock"}}i("FlowGraphSequenceBlock",n);export{n as FlowGraphSequenceBlock};
//# sourceMappingURL=flowGraphSequenceBlock-Bha-RB2S.js.map
