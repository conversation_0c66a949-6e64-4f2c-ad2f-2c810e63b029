import{SvelteComponent as $,init as tt,safe_not_equal as et,flush as o,create_component as v,claim_component as k,mount_component as x,transition_in as b,transition_out as m,destroy_component as B,binding_callbacks as K,bind as L,space as lt,claim_space as st,insert_hydration as it,group_outros as nt,check_outros as ut,add_flush_callback as M,detach as at,assign as _t,get_spread_update as ot,get_spread_object as ft}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{B as ht,w as ct,S as rt}from"./2.B2AoQPnG.js";import{default as St}from"./Example.DtHlUNEL.js";function O(s){let t,l;const n=[{autoscroll:s[2].autoscroll},{i18n:s[2].i18n},s[19]];let h={};for(let u=0;u<n.length;u+=1)h=_t(h,n[u]);return t=new rt({props:h}),t.$on("clear_status",s[27]),{c(){v(t.$$.fragment)},l(u){k(t.$$.fragment,u)},m(u,a){x(t,u,a),l=!0},p(u,a){const r=a[0]&524292?ot(n,[a[0]&4&&{autoscroll:u[2].autoscroll},a[0]&4&&{i18n:u[2].i18n},a[0]&524288&&ft(u[19])]):{};t.$set(r)},i(u){l||(b(t.$$.fragment,u),l=!0)},o(u){m(t.$$.fragment,u),l=!1},d(u){B(t,u)}}}function bt(s){let t,l,n,h,u,a=s[19]&&O(s);function r(i){s[28](i)}function g(i){s[29](i)}let c={label:s[3],info:s[4],show_label:s[10],lines:s[8],type:s[12],rtl:s[20],text_align:s[21],max_lines:s[11],placeholder:s[9],submit_btn:s[16],stop_btn:s[17],show_copy_button:s[18],autofocus:s[22],container:s[13],autoscroll:s[23],max_length:s[25],html_attributes:s[26],disabled:!s[24]};return s[0]!==void 0&&(c.value=s[0]),s[1]!==void 0&&(c.value_is_output=s[1]),l=new ct({props:c}),K.push(()=>L(l,"value",r)),K.push(()=>L(l,"value_is_output",g)),l.$on("change",s[30]),l.$on("input",s[31]),l.$on("submit",s[32]),l.$on("blur",s[33]),l.$on("select",s[34]),l.$on("focus",s[35]),l.$on("stop",s[36]),l.$on("copy",s[37]),{c(){a&&a.c(),t=lt(),v(l.$$.fragment)},l(i){a&&a.l(i),t=st(i),k(l.$$.fragment,i)},m(i,_){a&&a.m(i,_),it(i,t,_),x(l,i,_),u=!0},p(i,_){i[19]?a?(a.p(i,_),_[0]&524288&&b(a,1)):(a=O(i),a.c(),b(a,1),a.m(t.parentNode,t)):a&&(nt(),m(a,1,1,()=>{a=null}),ut());const f={};_[0]&8&&(f.label=i[3]),_[0]&16&&(f.info=i[4]),_[0]&1024&&(f.show_label=i[10]),_[0]&256&&(f.lines=i[8]),_[0]&4096&&(f.type=i[12]),_[0]&1048576&&(f.rtl=i[20]),_[0]&2097152&&(f.text_align=i[21]),_[0]&2048&&(f.max_lines=i[11]),_[0]&512&&(f.placeholder=i[9]),_[0]&65536&&(f.submit_btn=i[16]),_[0]&131072&&(f.stop_btn=i[17]),_[0]&262144&&(f.show_copy_button=i[18]),_[0]&4194304&&(f.autofocus=i[22]),_[0]&8192&&(f.container=i[13]),_[0]&8388608&&(f.autoscroll=i[23]),_[0]&33554432&&(f.max_length=i[25]),_[0]&67108864&&(f.html_attributes=i[26]),_[0]&16777216&&(f.disabled=!i[24]),!n&&_[0]&1&&(n=!0,f.value=i[0],M(()=>n=!1)),!h&&_[0]&2&&(h=!0,f.value_is_output=i[1],M(()=>h=!1)),l.$set(f)},i(i){u||(b(a),b(l.$$.fragment,i),u=!0)},o(i){m(a),m(l.$$.fragment,i),u=!1},d(i){i&&at(t),a&&a.d(i),B(l,i)}}}function mt(s){let t,l;return t=new ht({props:{visible:s[7],elem_id:s[5],elem_classes:s[6],scale:s[14],min_width:s[15],allow_overflow:!1,padding:s[13],$$slots:{default:[bt]},$$scope:{ctx:s}}}),{c(){v(t.$$.fragment)},l(n){k(t.$$.fragment,n)},m(n,h){x(t,n,h),l=!0},p(n,h){const u={};h[0]&128&&(u.visible=n[7]),h[0]&32&&(u.elem_id=n[5]),h[0]&64&&(u.elem_classes=n[6]),h[0]&16384&&(u.scale=n[14]),h[0]&32768&&(u.min_width=n[15]),h[0]&8192&&(u.padding=n[13]),h[0]&134168351|h[1]&128&&(u.$$scope={dirty:h,ctx:n}),t.$set(u)},i(n){l||(b(t.$$.fragment,n),l=!0)},o(n){m(t.$$.fragment,n),l=!1},d(n){B(t,n)}}}function gt(s,t,l){let{gradio:n}=t,{label:h="Textbox"}=t,{info:u=void 0}=t,{elem_id:a=""}=t,{elem_classes:r=[]}=t,{visible:g=!0}=t,{value:c=""}=t,{lines:i}=t,{placeholder:_=""}=t,{show_label:f}=t,{max_lines:S=void 0}=t,{type:T="text"}=t,{container:j=!0}=t,{scale:q=null}=t,{min_width:C=void 0}=t,{submit_btn:E=null}=t,{stop_btn:I=null}=t,{show_copy_button:N=!1}=t,{loading_status:w=void 0}=t,{value_is_output:d=!1}=t,{rtl:z=!1}=t,{text_align:A=void 0}=t,{autofocus:D=!1}=t,{autoscroll:F=!0}=t,{interactive:G}=t,{max_length:H=void 0}=t,{html_attributes:J=null}=t;const P=()=>n.dispatch("clear_status",w);function Q(e){c=e,l(0,c)}function R(e){d=e,l(1,d)}const U=()=>n.dispatch("change",c),V=()=>n.dispatch("input"),W=()=>n.dispatch("submit"),X=()=>n.dispatch("blur"),Y=e=>n.dispatch("select",e.detail),Z=()=>n.dispatch("focus"),y=()=>n.dispatch("stop"),p=e=>n.dispatch("copy",e.detail);return s.$$set=e=>{"gradio"in e&&l(2,n=e.gradio),"label"in e&&l(3,h=e.label),"info"in e&&l(4,u=e.info),"elem_id"in e&&l(5,a=e.elem_id),"elem_classes"in e&&l(6,r=e.elem_classes),"visible"in e&&l(7,g=e.visible),"value"in e&&l(0,c=e.value),"lines"in e&&l(8,i=e.lines),"placeholder"in e&&l(9,_=e.placeholder),"show_label"in e&&l(10,f=e.show_label),"max_lines"in e&&l(11,S=e.max_lines),"type"in e&&l(12,T=e.type),"container"in e&&l(13,j=e.container),"scale"in e&&l(14,q=e.scale),"min_width"in e&&l(15,C=e.min_width),"submit_btn"in e&&l(16,E=e.submit_btn),"stop_btn"in e&&l(17,I=e.stop_btn),"show_copy_button"in e&&l(18,N=e.show_copy_button),"loading_status"in e&&l(19,w=e.loading_status),"value_is_output"in e&&l(1,d=e.value_is_output),"rtl"in e&&l(20,z=e.rtl),"text_align"in e&&l(21,A=e.text_align),"autofocus"in e&&l(22,D=e.autofocus),"autoscroll"in e&&l(23,F=e.autoscroll),"interactive"in e&&l(24,G=e.interactive),"max_length"in e&&l(25,H=e.max_length),"html_attributes"in e&&l(26,J=e.html_attributes)},[c,d,n,h,u,a,r,g,i,_,f,S,T,j,q,C,E,I,N,w,z,A,D,F,G,H,J,P,Q,R,U,V,W,X,Y,Z,y,p]}class kt extends ${constructor(t){super(),tt(this,t,gt,mt,et,{gradio:2,label:3,info:4,elem_id:5,elem_classes:6,visible:7,value:0,lines:8,placeholder:9,show_label:10,max_lines:11,type:12,container:13,scale:14,min_width:15,submit_btn:16,stop_btn:17,show_copy_button:18,loading_status:19,value_is_output:1,rtl:20,text_align:21,autofocus:22,autoscroll:23,interactive:24,max_length:25,html_attributes:26},null,[-1,-1])}get gradio(){return this.$$.ctx[2]}set gradio(t){this.$$set({gradio:t}),o()}get label(){return this.$$.ctx[3]}set label(t){this.$$set({label:t}),o()}get info(){return this.$$.ctx[4]}set info(t){this.$$set({info:t}),o()}get elem_id(){return this.$$.ctx[5]}set elem_id(t){this.$$set({elem_id:t}),o()}get elem_classes(){return this.$$.ctx[6]}set elem_classes(t){this.$$set({elem_classes:t}),o()}get visible(){return this.$$.ctx[7]}set visible(t){this.$$set({visible:t}),o()}get value(){return this.$$.ctx[0]}set value(t){this.$$set({value:t}),o()}get lines(){return this.$$.ctx[8]}set lines(t){this.$$set({lines:t}),o()}get placeholder(){return this.$$.ctx[9]}set placeholder(t){this.$$set({placeholder:t}),o()}get show_label(){return this.$$.ctx[10]}set show_label(t){this.$$set({show_label:t}),o()}get max_lines(){return this.$$.ctx[11]}set max_lines(t){this.$$set({max_lines:t}),o()}get type(){return this.$$.ctx[12]}set type(t){this.$$set({type:t}),o()}get container(){return this.$$.ctx[13]}set container(t){this.$$set({container:t}),o()}get scale(){return this.$$.ctx[14]}set scale(t){this.$$set({scale:t}),o()}get min_width(){return this.$$.ctx[15]}set min_width(t){this.$$set({min_width:t}),o()}get submit_btn(){return this.$$.ctx[16]}set submit_btn(t){this.$$set({submit_btn:t}),o()}get stop_btn(){return this.$$.ctx[17]}set stop_btn(t){this.$$set({stop_btn:t}),o()}get show_copy_button(){return this.$$.ctx[18]}set show_copy_button(t){this.$$set({show_copy_button:t}),o()}get loading_status(){return this.$$.ctx[19]}set loading_status(t){this.$$set({loading_status:t}),o()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(t){this.$$set({value_is_output:t}),o()}get rtl(){return this.$$.ctx[20]}set rtl(t){this.$$set({rtl:t}),o()}get text_align(){return this.$$.ctx[21]}set text_align(t){this.$$set({text_align:t}),o()}get autofocus(){return this.$$.ctx[22]}set autofocus(t){this.$$set({autofocus:t}),o()}get autoscroll(){return this.$$.ctx[23]}set autoscroll(t){this.$$set({autoscroll:t}),o()}get interactive(){return this.$$.ctx[24]}set interactive(t){this.$$set({interactive:t}),o()}get max_length(){return this.$$.ctx[25]}set max_length(t){this.$$set({max_length:t}),o()}get html_attributes(){return this.$$.ctx[26]}set html_attributes(t){this.$$set({html_attributes:t}),o()}}export{St as BaseExample,ct as BaseTextbox,kt as default};
//# sourceMappingURL=Index.BICvNBdW.js.map
