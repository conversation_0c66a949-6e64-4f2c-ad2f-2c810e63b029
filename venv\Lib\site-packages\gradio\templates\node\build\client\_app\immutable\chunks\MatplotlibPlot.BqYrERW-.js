import{SvelteComponent as v,init as _,safe_not_equal as f,element as d,claim_element as c,children as g,detach as p,src_url_equal as m,attr as n,insert_hydration as b,append_hydration as j,listen as y,noop as h,bubble as q}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function I(a){let e,t,r,l,u,s;return{c(){e=d("div"),t=d("img"),this.h()},l(i){e=c(i,"DIV",{"data-testid":!0,class:!0});var o=g(e);t=c(o,"IMG",{src:!0,alt:!0,class:!0}),o.forEach(p),this.h()},h(){m(t.src,r=a[1])||n(t,"src",r),n(t,"alt",l=`${a[0].chart} plot visualising provided data`),n(t,"class","svelte-j1jcu3"),n(e,"data-testid","matplotlib"),n(e,"class","matplotlib layout svelte-j1jcu3")},m(i,o){b(i,e,o),j(e,t),u||(s=y(t,"load",a[2]),u=!0)},p(i,[o]){o&2&&!m(t.src,r=i[1])&&n(t,"src",r),o&1&&l!==(l=`${i[0].chart} plot visualising provided data`)&&n(t,"alt",l)},i:h,o:h,d(i){i&&p(e),u=!1,s()}}}function M(a,e,t){let r,{value:l}=e;function u(s){q.call(this,a,s)}return a.$$set=s=>{"value"in s&&t(0,l=s.value)},a.$$.update=()=>{a.$$.dirty&1&&t(1,r=l==null?void 0:l.plot)},[l,r,u]}class E extends v{constructor(e){super(),_(this,e,M,I,f,{value:0})}}export{E as default};
//# sourceMappingURL=MatplotlibPlot.BqYrERW-.js.map
