import{SvelteComponent as G,init as J,safe_not_equal as Z,svg_element as O,claim_svg_element as B,children as A,detach as b,attr as _,insert_hydration as D,append_hydration as w,noop as W,element as P,text as F,space as ee,claim_element as T,claim_text as H,claim_space as te,toggle_class as v,set_data as R,createEventDispatcher as ce,onMount as <PERSON><PERSON>,onD<PERSON><PERSON> as <PERSON>,set_style as N,empty as q,group_outros as he,transition_out as S,check_outros as ge,transition_in as C,create_slot as me,action_destroyer as Ce,update_slot_base as pe,get_all_dirty_from_scope as ve,get_slot_changes as be,is_function as De,listen as Ie,create_component as Ne,claim_component as Se,mount_component as ze,destroy_component as Oe,tick as Be}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{A as Fe}from"./2.B2AoQPnG.js";/* empty css                                             */function He(l){let e,t;return{c(){e=O("svg"),t=O("path"),this.h()},l(n){e=B(n,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0});var r=A(e);t=B(r,"path",{fill:!0,d:!0}),A(t).forEach(b),r.forEach(b),this.h()},h(){_(t,"fill","currentColor"),_(t,"d","M200 32h-36.26a47.92 47.92 0 0 0-71.48 0H56a16 16 0 0 0-16 16v168a16 16 0 0 0 16 16h144a16 16 0 0 0 16-16V48a16 16 0 0 0-16-16m-72 0a32 32 0 0 1 32 32H96a32 32 0 0 1 32-32m72 184H56V48h26.75A47.9 47.9 0 0 0 80 64v8a8 8 0 0 0 8 8h80a8 8 0 0 0 8-8v-8a47.9 47.9 0 0 0-2.75-16H200Z"),_(e,"xmlns","http://www.w3.org/2000/svg"),_(e,"width","100%"),_(e,"height","100%"),_(e,"viewBox","0 0 256 256")},m(n,r){D(n,e,r),w(e,t)},p:W,i:W,o:W,d(n){n&&b(e)}}}class lt extends G{constructor(e){super(),J(this,e,null,He,Z,{})}}function Ve(l){let e,t,n,r;return{c(){e=O("svg"),t=O("path"),n=O("polyline"),r=O("line"),this.h()},l(u){e=B(u,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0,class:!0});var d=A(e);t=B(d,"path",{d:!0}),A(t).forEach(b),n=B(d,"polyline",{points:!0}),A(n).forEach(b),r=B(d,"line",{x1:!0,y1:!0,x2:!0,y2:!0}),A(r).forEach(b),d.forEach(b),this.h()},h(){_(t,"d","M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"),_(n,"points","17 8 12 3 7 8"),_(r,"x1","12"),_(r,"y1","3"),_(r,"x2","12"),_(r,"y2","15"),_(e,"xmlns","http://www.w3.org/2000/svg"),_(e,"width","90%"),_(e,"height","90%"),_(e,"viewBox","0 0 24 24"),_(e,"fill","none"),_(e,"stroke","currentColor"),_(e,"stroke-width","2"),_(e,"stroke-linecap","round"),_(e,"stroke-linejoin","round"),_(e,"class","feather feather-upload")},m(u,d){D(u,e,d),w(e,t),w(e,n),w(e,r)},p:W,i:W,o:W,d(u){u&&b(e)}}}let rt=class extends G{constructor(e){super(),J(this,e,null,Ve,Z,{})}};function ue(l){let e,t,n,r,u=j(l[2])+"",d,h,a,s,i=l[2].orig_name+"",f;return{c(){e=P("div"),t=P("span"),n=P("div"),r=P("progress"),d=F(u),a=ee(),s=P("span"),f=F(i),this.h()},l(c){e=T(c,"DIV",{class:!0});var g=A(e);t=T(g,"SPAN",{});var m=A(t);n=T(m,"DIV",{class:!0});var k=A(n);r=T(k,"PROGRESS",{style:!0,max:!0,class:!0});var L=A(r);d=H(L,u),L.forEach(b),k.forEach(b),m.forEach(b),a=te(g),s=T(g,"SPAN",{class:!0});var I=A(s);f=H(I,i),I.forEach(b),g.forEach(b),this.h()},h(){N(r,"visibility","hidden"),N(r,"height","0"),N(r,"width","0"),r.value=h=j(l[2]),_(r,"max","100"),_(r,"class","svelte-1vsfomn"),_(n,"class","progress-bar svelte-1vsfomn"),_(s,"class","file-name svelte-1vsfomn"),_(e,"class","file svelte-1vsfomn")},m(c,g){D(c,e,g),w(e,t),w(t,n),w(n,r),w(r,d),w(e,a),w(e,s),w(s,f)},p(c,g){g&4&&u!==(u=j(c[2])+"")&&R(d,u),g&4&&h!==(h=j(c[2]))&&(r.value=h),g&4&&i!==(i=c[2].orig_name+"")&&R(f,i)},d(c){c&&b(e)}}}function je(l){let e,t,n,r=l[0].length+"",u,d,h=l[0].length>1?"files":"file",a,s,i,f=l[2]&&ue(l);return{c(){e=P("div"),t=P("span"),n=F("Uploading "),u=F(r),d=ee(),a=F(h),s=F("..."),i=ee(),f&&f.c(),this.h()},l(c){e=T(c,"DIV",{class:!0});var g=A(e);t=T(g,"SPAN",{class:!0});var m=A(t);n=H(m,"Uploading "),u=H(m,r),d=te(m),a=H(m,h),s=H(m,"..."),m.forEach(b),i=te(g),f&&f.l(g),g.forEach(b),this.h()},h(){_(t,"class","uploading svelte-1vsfomn"),_(e,"class","wrap svelte-1vsfomn"),v(e,"progress",l[1])},m(c,g){D(c,e,g),w(e,t),w(t,n),w(t,u),w(t,d),w(t,a),w(t,s),w(e,i),f&&f.m(e,null)},p(c,[g]){g&1&&r!==(r=c[0].length+"")&&R(u,r),g&1&&h!==(h=c[0].length>1?"files":"file")&&R(a,h),c[2]?f?f.p(c,g):(f=ue(c),f.c(),f.m(e,null)):f&&(f.d(1),f=null),g&2&&v(e,"progress",c[1])},i:W,o:W,d(c){c&&b(e),f&&f.d()}}}function j(l){return l.progress*100/(l.size||0)||0}function Me(l){let e=0;return l.forEach(t=>{e+=j(t)}),document.documentElement.style.setProperty("--upload-progress-width",(e/l.length).toFixed(2)+"%"),e/l.length}function Re(l,e,t){let{upload_id:n}=e,{root:r}=e,{files:u}=e,{stream_handler:d}=e,h,a=!1,s,i,f=u.map(m=>({...m,progress:0}));const c=ce();function g(m,k){t(0,f=f.map(L=>(L.orig_name===m&&(L.progress+=k),L)))}return Ue(async()=>{if(h=await d(new URL(`${r}/gradio_api/upload_progress?upload_id=${n}`)),h==null)throw new Error("Event source is not defined");h.onmessage=async function(m){const k=JSON.parse(m.data);a||t(1,a=!0),k.msg==="done"?(h==null||h.close(),c("done")):(t(7,s=k),g(k.orig_name,k.chunk_size))}}),We(()=>{(h!=null||h!=null)&&h.close()}),l.$$set=m=>{"upload_id"in m&&t(3,n=m.upload_id),"root"in m&&t(4,r=m.root),"files"in m&&t(5,u=m.files),"stream_handler"in m&&t(6,d=m.stream_handler)},l.$$.update=()=>{l.$$.dirty&1&&Me(f),l.$$.dirty&129&&t(2,i=s||f[0])},[f,a,i,n,r,u,d,s]}class qe extends G{constructor(e){super(),J(this,e,Re,je,Z,{upload_id:3,root:4,files:5,stream_handler:6})}}function Ge(){let l,e;return{drag(t,n={}){e=n;function r(){l=document.createElement("input"),l.type="file",l.style.display="none",l.setAttribute("aria-label","File upload"),l.setAttribute("data-testid","file-upload");const f=Array.isArray(e.accepted_types)?e.accepted_types.join(","):e.accepted_types||void 0;f&&(l.accept=f),l.multiple=e.mode==="multiple"||!1,e.mode==="directory"&&(l.webkitdirectory=!0,l.setAttribute("directory",""),l.setAttribute("mozdirectory","")),t.appendChild(l)}r();function u(f){f.preventDefault(),f.stopPropagation()}function d(f){var c;f.preventDefault(),f.stopPropagation(),(c=e.on_drag_change)==null||c.call(e,!0)}function h(f){var c;f.preventDefault(),f.stopPropagation(),(c=e.on_drag_change)==null||c.call(e,!1)}function a(f){var g,m,k;if(f.preventDefault(),f.stopPropagation(),(g=e.on_drag_change)==null||g.call(e,!1),!((m=f.dataTransfer)!=null&&m.files))return;const c=Array.from(f.dataTransfer.files);c.length>0&&((k=e.on_files)==null||k.call(e,c))}function s(){e.disable_click||(l.value="",l.click())}function i(){var f;if(l.files){const c=Array.from(l.files);c.length>0&&((f=e.on_files)==null||f.call(e,c))}}return t.addEventListener("drag",u),t.addEventListener("dragstart",u),t.addEventListener("dragend",u),t.addEventListener("dragover",u),t.addEventListener("dragenter",d),t.addEventListener("dragleave",h),t.addEventListener("drop",a),t.addEventListener("click",s),l.addEventListener("change",i),{update(f){e=f,l.remove(),r(),l.addEventListener("change",i)},destroy(){t.removeEventListener("drag",u),t.removeEventListener("dragstart",u),t.removeEventListener("dragend",u),t.removeEventListener("dragover",u),t.removeEventListener("dragenter",d),t.removeEventListener("dragleave",h),t.removeEventListener("drop",a),t.removeEventListener("click",s),l.removeEventListener("change",i),l.remove()}}},open_file_upload(){l&&(l.value="",l.click())}}}function Je(l){let e,t,n,r,u,d,h;const a=l[30].default,s=me(a,l,l[29],null);return{c(){e=P("button"),s&&s.c(),this.h()},l(i){e=T(i,"BUTTON",{tabindex:!0,"aria-label":!0,"aria-dropeffect":!0,class:!0});var f=A(e);s&&s.l(f),f.forEach(b),this.h()},h(){_(e,"tabindex",t=l[9]?-1:0),_(e,"aria-label",n=l[14]||"Click to upload or drop files"),_(e,"aria-dropeffect","copy"),_(e,"class","svelte-edrmkl"),v(e,"hidden",l[9]),v(e,"center",l[4]),v(e,"boundedheight",l[3]),v(e,"flex",l[5]),v(e,"disable_click",l[7]),v(e,"icon-mode",l[12]),N(e,"height",l[12]?"":l[13]?typeof l[13]=="number"?l[13]+"px":l[13]:"100%")},m(i,f){D(i,e,f),s&&s.m(e,null),u=!0,d||(h=Ce(r=l[19].call(null,e,{on_drag_change:_e,on_files:l[31],accepted_types:l[18],mode:l[6],disable_click:l[7]})),d=!0)},p(i,f){s&&s.p&&(!u||f[0]&536870912)&&pe(s,a,i,i[29],u?be(a,i[29],f,null):ve(i[29]),null),(!u||f[0]&512&&t!==(t=i[9]?-1:0))&&_(e,"tabindex",t),(!u||f[0]&16384&&n!==(n=i[14]||"Click to upload or drop files"))&&_(e,"aria-label",n),r&&De(r.update)&&f[0]&262336&&r.update.call(null,{on_drag_change:_e,on_files:i[31],accepted_types:i[18],mode:i[6],disable_click:i[7]}),(!u||f[0]&512)&&v(e,"hidden",i[9]),(!u||f[0]&16)&&v(e,"center",i[4]),(!u||f[0]&8)&&v(e,"boundedheight",i[3]),(!u||f[0]&32)&&v(e,"flex",i[5]),(!u||f[0]&128)&&v(e,"disable_click",i[7]),(!u||f[0]&4096)&&v(e,"icon-mode",i[12]),f[0]&12288&&N(e,"height",i[12]?"":i[13]?typeof i[13]=="number"?i[13]+"px":i[13]:"100%")},i(i){u||(C(s,i),u=!0)},o(i){S(s,i),u=!1},d(i){i&&b(e),s&&s.d(i),d=!1,h()}}}function Ze(l){let e,t,n=!l[9]&&de(l);return{c(){n&&n.c(),e=q()},l(r){n&&n.l(r),e=q()},m(r,u){n&&n.m(r,u),D(r,e,u),t=!0},p(r,u){r[9]?n&&(he(),S(n,1,1,()=>{n=null}),ge()):n?(n.p(r,u),u[0]&512&&C(n,1)):(n=de(r),n.c(),C(n,1),n.m(e.parentNode,e))},i(r){t||(C(n),t=!0)},o(r){S(n),t=!1},d(r){r&&b(e),n&&n.d(r)}}}function Ke(l){let e,t,n,r,u,d;const h=l[30].default,a=me(h,l,l[29],null);return{c(){e=P("button"),a&&a.c(),this.h()},l(s){e=T(s,"BUTTON",{tabindex:!0,"aria-label":!0,class:!0});var i=A(e);a&&a.l(i),i.forEach(b),this.h()},h(){_(e,"tabindex",t=l[9]?-1:0),_(e,"aria-label",n=l[14]||"Paste from clipboard"),_(e,"class","svelte-edrmkl"),v(e,"hidden",l[9]),v(e,"center",l[4]),v(e,"boundedheight",l[3]),v(e,"flex",l[5]),v(e,"icon-mode",l[12]),N(e,"height",l[12]?"":l[13]?typeof l[13]=="number"?l[13]+"px":l[13]:"100%")},m(s,i){D(s,e,i),a&&a.m(e,null),r=!0,u||(d=Ie(e,"click",l[15]),u=!0)},p(s,i){a&&a.p&&(!r||i[0]&536870912)&&pe(a,h,s,s[29],r?be(h,s[29],i,null):ve(s[29]),null),(!r||i[0]&512&&t!==(t=s[9]?-1:0))&&_(e,"tabindex",t),(!r||i[0]&16384&&n!==(n=s[14]||"Paste from clipboard"))&&_(e,"aria-label",n),(!r||i[0]&512)&&v(e,"hidden",s[9]),(!r||i[0]&16)&&v(e,"center",s[4]),(!r||i[0]&8)&&v(e,"boundedheight",s[3]),(!r||i[0]&32)&&v(e,"flex",s[5]),(!r||i[0]&4096)&&v(e,"icon-mode",s[12]),i[0]&12288&&N(e,"height",s[12]?"":s[13]?typeof s[13]=="number"?s[13]+"px":s[13]:"100%")},i(s){r||(C(a,s),r=!0)},o(s){S(a,s),r=!1},d(s){s&&b(e),a&&a.d(s),u=!1,d()}}}function de(l){let e,t;return e=new qe({props:{root:l[8],upload_id:l[16],files:l[17],stream_handler:l[11]}}),{c(){Ne(e.$$.fragment)},l(n){Se(e.$$.fragment,n)},m(n,r){ze(e,n,r),t=!0},p(n,r){const u={};r[0]&256&&(u.root=n[8]),r[0]&65536&&(u.upload_id=n[16]),r[0]&131072&&(u.files=n[17]),r[0]&2048&&(u.stream_handler=n[11]),e.$set(u)},i(n){t||(C(e.$$.fragment,n),t=!0)},o(n){S(e.$$.fragment,n),t=!1},d(n){Oe(e,n)}}}function Qe(l){let e,t,n,r;const u=[Ke,Ze,Je],d=[];function h(a,s){return a[0]==="clipboard"?0:a[2]&&a[10]?1:2}return e=h(l),t=d[e]=u[e](l),{c(){t.c(),n=q()},l(a){t.l(a),n=q()},m(a,s){d[e].m(a,s),D(a,n,s),r=!0},p(a,s){let i=e;e=h(a),e===i?d[e].p(a,s):(he(),S(d[i],1,1,()=>{d[i]=null}),ge(),t=d[e],t?t.p(a,s):(t=d[e]=u[e](a),t.c()),C(t,1),t.m(n.parentNode,n))},i(a){r||(C(t),r=!0)},o(a){S(t),r=!1},d(a){a&&b(n),d[e].d(a)}}}function Xe(l,e,t){if(!l||l==="*"||l==="file/*"||Array.isArray(l)&&l.some(r=>r==="*"||r==="file/*"))return!0;let n;if(typeof l=="string")n=l.split(",").map(r=>r.trim());else if(Array.isArray(l))n=l;else return!1;return n.includes(e)||n.some(r=>{const[u]=r.split("/").map(d=>d.trim());return r.endsWith("/*")&&t.startsWith(u+"/")})}const _e=l=>l=l;function Ye(l,e,t){let n,{$$slots:r={},$$scope:u}=e;const{drag:d,open_file_upload:h}=Ge();let{filetype:a=null}=e,{dragging:s=!1}=e,{boundedheight:i=!0}=e,{center:f=!0}=e,{flex:c=!0}=e,{file_count:g="single"}=e,{disable_click:m=!1}=e,{root:k}=e,{hidden:L=!1}=e,{format:I="file"}=e,{uploading:V=!1}=e,{show_progress:le=!0}=e,{max_file_size:K=null}=e,{upload:Q}=e,{stream_handler:re}=e,{icon_upload:ne=!1}=e,{height:ie=void 0}=e,{aria_label:ae=void 0}=e;function ye(){h()}let X,Y,z,oe=null;const we=()=>{if(typeof navigator<"u"){const o=navigator.userAgent.toLowerCase();return o.indexOf("iphone")>-1||o.indexOf("ipad")>-1}return!1},U=ce(),ke=["image","video","audio","text","file"],x=o=>n&&o.startsWith(".")?(oe=!0,o):n&&o.includes("file/*")?"*":o.startsWith(".")||o.endsWith("/*")?o:ke.includes(o)?o+"/*":"."+o;function Ee(){navigator.clipboard.read().then(async o=>{for(let p=0;p<o.length;p++){const y=o[p].types.find(E=>E.startsWith("image/"));if(y){o[p].getType(y).then(async E=>{const $=new File([E],`clipboard.${y.replace("image/","")}`);await M([$])});break}}})}function Ae(){h()}async function Le(o){await Be(),t(16,X=Math.random().toString(36).substring(2,15)),t(2,V=!0);try{const p=await Q(o,k,X,K??1/0);return U("load",g==="single"?p==null?void 0:p[0]:p),t(2,V=!1),p||[]}catch(p){return U("error",p.message),t(2,V=!1),[]}}async function M(o){if(!o.length)return;let p=o.map(y=>new File([y],y instanceof File?y.name:"file",{type:y.type}));return n&&oe&&(p=p.filter(y=>se(y)?!0:(U("error",`Invalid file type: ${y.name}. Only ${a} allowed.`),!1)),p.length===0)?[]:(t(17,Y=await Fe(p)),await Le(Y))}function se(o){return a?(Array.isArray(a)?a:[a]).some(y=>{const E=x(y);if(E.startsWith("."))return o.name.toLowerCase().endsWith(E.toLowerCase());if(E==="*")return!0;if(E.endsWith("/*")){const[$]=E.split("/");return o.type.startsWith($+"/")}return o.type===E}):!0}async function fe(o){const p=o.filter(y=>{const E="."+y.name.split(".").pop();return E&&Xe(z,E,y.type)||(E&&Array.isArray(a)?a.includes(E):E===a)?!0:(U("error",`Invalid file type only ${a} allowed.`),!1)});if(I!="blob")await M(p);else{if(g==="single"){U("load",p[0]);return}U("load",p)}}async function Pe(o){var y;if(t(1,s=!1),!((y=o.dataTransfer)!=null&&y.files))return;const p=Array.from(o.dataTransfer.files).filter(se);if(I!="blob")await M(p);else{if(g==="single"){U("load",p[0]);return}U("load",p)}}const Te=o=>fe(o);return l.$$set=o=>{"filetype"in o&&t(0,a=o.filetype),"dragging"in o&&t(1,s=o.dragging),"boundedheight"in o&&t(3,i=o.boundedheight),"center"in o&&t(4,f=o.center),"flex"in o&&t(5,c=o.flex),"file_count"in o&&t(6,g=o.file_count),"disable_click"in o&&t(7,m=o.disable_click),"root"in o&&t(8,k=o.root),"hidden"in o&&t(9,L=o.hidden),"format"in o&&t(21,I=o.format),"uploading"in o&&t(2,V=o.uploading),"show_progress"in o&&t(10,le=o.show_progress),"max_file_size"in o&&t(22,K=o.max_file_size),"upload"in o&&t(23,Q=o.upload),"stream_handler"in o&&t(11,re=o.stream_handler),"icon_upload"in o&&t(12,ne=o.icon_upload),"height"in o&&t(13,ie=o.height),"aria_label"in o&&t(14,ae=o.aria_label),"$$scope"in o&&t(29,u=o.$$scope)},l.$$.update=()=>{l.$$.dirty[0]&268435457&&(a==null?t(18,z=null):typeof a=="string"?t(18,z=x(a)):n&&a.includes("file/*")?t(18,z="*"):(t(0,a=a.map(x)),t(18,z=a.join(", "))))},t(28,n=we()),[a,s,V,i,f,c,g,m,k,L,le,re,ne,ie,ae,Ee,X,Y,z,d,fe,I,K,Q,ye,Ae,M,Pe,n,u,r,Te]}class it extends G{constructor(e){super(),J(this,e,Ye,Qe,Z,{filetype:0,dragging:1,boundedheight:3,center:4,flex:5,file_count:6,disable_click:7,root:8,hidden:9,format:21,uploading:2,show_progress:10,max_file_size:22,upload:23,stream_handler:11,icon_upload:12,height:13,aria_label:14,open_upload:24,paste_clipboard:15,open_file_upload:25,load_files:26,load_files_from_drop:27},null,[-1,-1])}get open_upload(){return this.$$.ctx[24]}get paste_clipboard(){return this.$$.ctx[15]}get open_file_upload(){return this.$$.ctx[25]}get load_files(){return this.$$.ctx[26]}get load_files_from_drop(){return this.$$.ctx[27]}}export{lt as I,rt as U,it as a,Ge as c};
//# sourceMappingURL=Upload.yOHVlgUe.js.map
