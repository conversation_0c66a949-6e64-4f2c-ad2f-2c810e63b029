import{SvelteComponent as p,init as c,safe_not_equal as d,svg_element as l,claim_svg_element as h,children as u,detach as i,attr as e,insert_hydration as g,append_hydration as m,noop as s}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function v(a){let t,r;return{c(){t=l("svg"),r=l("polygon"),this.h()},l(o){t=h(o,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0});var n=u(t);r=h(n,"polygon",{points:!0}),u(r).forEach(i),n.forEach(i),this.h()},h(){e(r,"points","5 3 19 12 5 21 5 3"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 24 24"),e(t,"fill","currentColor"),e(t,"stroke","currentColor"),e(t,"stroke-width","1.5"),e(t,"stroke-linecap","round"),e(t,"stroke-linejoin","round")},m(o,n){g(o,t,n),m(t,r)},p:s,i:s,o:s,d(o){o&&i(t)}}}class _ extends p{constructor(t){super(),c(this,t,null,v,d,{})}}export{_ as P};
//# sourceMappingURL=Play.DJ4h2PVY.js.map
