{"version": 3, "file": "flowGraphReceiveCustomEventBlock.Zl1e7Elp.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphReceiveCustomEventBlock.js"], "sourcesContent": ["import { FlowGraphEventBlock } from \"../../flowGraphEventBlock.js\";\nimport { Tools } from \"../../../Misc/tools.js\";\nimport { RegisterClass } from \"../../../Misc/typeStore.js\";\nimport { FlowGraphCoordinator } from \"../../flowGraphCoordinator.js\";\n/**\n * A block that receives a custom event.\n * It saves the event data in the data outputs, based on the provided eventData in the configuration. For example, if the event data is\n * `{ x: { type: RichTypeNumber }, y: { type: RichTypeNumber } }`, the block will have two data outputs: x and y.\n */\nexport class FlowGraphReceiveCustomEventBlock extends FlowGraphEventBlock {\n    constructor(\n    /**\n     * the configuration of the block\n     */\n    config) {\n        super(config);\n        this.config = config;\n        this.initPriority = 1;\n        // use event data to register data outputs\n        for (const key in this.config.eventData) {\n            this.registerDataOutput(key, this.config.eventData[key].type);\n        }\n    }\n    _preparePendingTasks(context) {\n        const observable = context.configuration.coordinator.getCustomEventObservable(this.config.eventId);\n        // check if we are not exceeding the max number of events\n        if (observable && observable.hasObservers() && observable.observers.length > FlowGraphCoordinator.MaxEventsPerType) {\n            this._reportError(context, `FlowGraphReceiveCustomEventBlock: Too many observers for event ${this.config.eventId}. Max is ${FlowGraphCoordinator.MaxEventsPerType}.`);\n            return;\n        }\n        const eventObserver = observable.add((eventData) => {\n            Object.keys(eventData).forEach((key) => {\n                this.getDataOutput(key)?.setValue(eventData[key], context);\n            });\n            this._execute(context);\n        });\n        context._setExecutionVariable(this, \"_eventObserver\", eventObserver);\n    }\n    _cancelPendingTasks(context) {\n        const observable = context.configuration.coordinator.getCustomEventObservable(this.config.eventId);\n        if (observable) {\n            const eventObserver = context._getExecutionVariable(this, \"_eventObserver\", null);\n            observable.remove(eventObserver);\n        }\n        else {\n            Tools.Warn(`FlowGraphReceiveCustomEventBlock: Missing observable for event ${this.config.eventId}`);\n        }\n    }\n    _executeEvent(_context, _payload) {\n        return true;\n    }\n    /**\n     * @returns class name of the block.\n     */\n    getClassName() {\n        return \"FlowGraphReceiveCustomEventBlock\" /* FlowGraphBlockNames.ReceiveCustomEvent */;\n    }\n}\nRegisterClass(\"FlowGraphReceiveCustomEventBlock\" /* FlowGraphBlockNames.ReceiveCustomEvent */, FlowGraphReceiveCustomEventBlock);\n//# sourceMappingURL=flowGraphReceiveCustomEventBlock.js.map"], "names": ["FlowGraphReceiveCustomEventBlock", "FlowGraphEventBlock", "config", "key", "context", "observable", "FlowGraphCoordinator", "eventObserver", "eventData", "_a", "Tools", "_context", "_payload", "RegisterClass"], "mappings": "0GASO,MAAMA,UAAyCC,CAAoB,CACtE,YAIAC,EAAQ,CACJ,MAAMA,CAAM,EACZ,KAAK,OAASA,EACd,KAAK,aAAe,EAEpB,UAAWC,KAAO,KAAK,OAAO,UAC1B,KAAK,mBAAmBA,EAAK,KAAK,OAAO,UAAUA,CAAG,EAAE,IAAI,CAEnE,CACD,qBAAqBC,EAAS,CAC1B,MAAMC,EAAaD,EAAQ,cAAc,YAAY,yBAAyB,KAAK,OAAO,OAAO,EAEjG,GAAIC,GAAcA,EAAW,aAAc,GAAIA,EAAW,UAAU,OAASC,EAAqB,iBAAkB,CAChH,KAAK,aAAaF,EAAS,kEAAkE,KAAK,OAAO,OAAO,YAAYE,EAAqB,gBAAgB,GAAG,EACpK,MACH,CACD,MAAMC,EAAgBF,EAAW,IAAKG,GAAc,CAChD,OAAO,KAAKA,CAAS,EAAE,QAASL,GAAQ,QACpCM,EAAA,KAAK,cAAcN,CAAG,IAAtB,MAAAM,EAAyB,SAASD,EAAUL,CAAG,EAAGC,EAClE,CAAa,EACD,KAAK,SAASA,CAAO,CACjC,CAAS,EACDA,EAAQ,sBAAsB,KAAM,iBAAkBG,CAAa,CACtE,CACD,oBAAoBH,EAAS,CACzB,MAAMC,EAAaD,EAAQ,cAAc,YAAY,yBAAyB,KAAK,OAAO,OAAO,EACjG,GAAIC,EAAY,CACZ,MAAME,EAAgBH,EAAQ,sBAAsB,KAAM,iBAAkB,IAAI,EAChFC,EAAW,OAAOE,CAAa,CAClC,MAEGG,EAAM,KAAK,kEAAkE,KAAK,OAAO,OAAO,EAAE,CAEzG,CACD,cAAcC,EAAUC,EAAU,CAC9B,MAAO,EACV,CAID,cAAe,CACX,MAAO,kCACV,CACL,CACAC,EAAc,mCAAiFb,CAAgC", "x_google_ignoreList": [0]}