import{SvelteComponent as y,init as E,safe_not_equal as V,svg_element as s,text as T,claim_svg_element as c,children as o,claim_text as H,detach as i,attr as t,insert_hydration as B,append_hydration as d,noop as x}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function C(v){let e,u,f,p,a,l,g,n,r;return{c(){e=s("svg"),u=s("defs"),f=s("style"),p=T(`.cls-1 {
				fill: none;
			}`),a=s("rect"),l=s("rect"),g=s("path"),n=s("rect"),r=s("rect"),this.h()},l(w){e=c(w,"svg",{id:!0,xmlns:!0,viewBox:!0,fill:!0,width:!0,height:!0});var h=o(e);u=c(h,"defs",{});var _=o(u);f=c(_,"style",{});var m=o(f);p=H(m,`.cls-1 {
				fill: none;
			}`),m.forEach(i),_.forEach(i),a=c(h,"rect",{x:!0,y:!0,width:!0,height:!0}),o(a).forEach(i),l=c(h,"rect",{x:!0,y:!0,width:!0,height:!0}),o(l).forEach(i),g=c(h,"path",{d:!0}),o(g).forEach(i),n=c(h,"rect",{x:!0,y:!0,width:!0,height:!0}),o(n).forEach(i),r=c(h,"rect",{id:!0,"data-name":!0,class:!0,width:!0,height:!0}),o(r).forEach(i),h.forEach(i),this.h()},h(){t(a,"x","12"),t(a,"y","12"),t(a,"width","2"),t(a,"height","12"),t(l,"x","18"),t(l,"y","12"),t(l,"width","2"),t(l,"height","12"),t(g,"d","M4,6V8H6V28a2,2,0,0,0,2,2H24a2,2,0,0,0,2-2V8h2V6ZM8,28V8H24V28Z"),t(n,"x","12"),t(n,"y","2"),t(n,"width","8"),t(n,"height","2"),t(r,"id","_Transparent_Rectangle_"),t(r,"data-name","<Transparent Rectangle>"),t(r,"class","cls-1"),t(r,"width","32"),t(r,"height","32"),t(e,"id","icon"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"viewBox","0 0 32 32"),t(e,"fill","currentColor"),t(e,"width","100%"),t(e,"height","100%")},m(w,h){B(w,e,h),d(e,u),d(u,f),d(f,p),d(e,a),d(e,l),d(e,g),d(e,n),d(e,r)},p:x,i:x,o:x,d(w){w&&i(e)}}}class Z extends y{constructor(e){super(),E(this,e,null,C,V,{})}}export{Z as T};
//# sourceMappingURL=Trash.D4IfxcH_.js.map
