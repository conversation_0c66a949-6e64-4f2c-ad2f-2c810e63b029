import { c as create_ssr_component, e as escape } from './ssr-C3HYbsxA.js';

const i={code:"div.svelte-rgtszb{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.gallery.svelte-rgtszb{display:flex;align-items:center;cursor:pointer;padding:var(--size-1) var(--size-2);text-align:left}",map:'{"version":3,"file":"Example.svelte","sources":["Example.svelte"],"sourcesContent":["<script lang=\\"ts\\">export let value;\\nexport let type;\\nexport let selected = false;\\n<\/script>\\n\\n<div\\n\\tclass:table={type === \\"table\\"}\\n\\tclass:gallery={type === \\"gallery\\"}\\n\\tclass:selected\\n>\\n\\t{value ? (Array.isArray(value) ? value.join(\\", \\") : value) : \\"\\"}\\n</div>\\n\\n<style>\\n\\tdiv {\\n\\t\\toverflow: hidden;\\n\\t\\ttext-overflow: ellipsis;\\n\\t\\twhite-space: nowrap;\\n\\t}\\n\\t.gallery {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tcursor: pointer;\\n\\t\\tpadding: var(--size-1) var(--size-2);\\n\\t\\ttext-align: left;\\n\\t}</style>\\n"],"names":[],"mappings":"AAcC,iBAAI,CACH,QAAQ,CAAE,MAAM,CAChB,aAAa,CAAE,QAAQ,CACvB,WAAW,CAAE,MACd,CACA,sBAAS,CACR,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,OAAO,CACf,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CACpC,UAAU,CAAE,IACb"}'},o=create_ssr_component((s,e,t,C)=>{let{value:A}=e,{type:l}=e,{selected:a=!1}=e;return e.value===void 0&&t.value&&A!==void 0&&t.value(A),e.type===void 0&&t.type&&l!==void 0&&t.type(l),e.selected===void 0&&t.selected&&a!==void 0&&t.selected(a),s.css.add(i),`<div class="${["svelte-rgtszb",(l==="table"?"table":"")+" "+(l==="gallery"?"gallery":"")+" "+(a?"selected":"")].join(" ").trim()}">${escape(A?Array.isArray(A)?A.join(", "):A:"")} </div>`});

export { o as default };
//# sourceMappingURL=Example12-QUrFCQ6n.js.map
