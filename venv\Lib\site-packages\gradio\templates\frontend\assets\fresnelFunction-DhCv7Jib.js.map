{"version": 3, "file": "fresnelFunction-DhCv7Jib.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/fresnelFunction.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore.js\";\nconst name = \"fresnelFunction\";\nconst shader = `#ifdef FRESNEL\nfn computeFresnelTerm(viewDirection: vec3f,worldNormal: vec3f,bias: f32,power: f32)->f32\n{let fresnelTerm: f32=pow(bias+abs(dot(viewDirection,worldNormal)),power);return clamp(fresnelTerm,0.,1.);}\n#endif\n`;\n// Sideeffect\nif (!ShaderStore.IncludesShadersStoreWGSL[name]) {\n    ShaderStore.IncludesShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const fresnelFunctionWGSL = { name, shader };\n//# sourceMappingURL=fresnelFunction.js.map"], "names": ["name", "shader", "ShaderStore"], "mappings": "wCAEA,MAAMA,EAAO,kBACPC,EAAS;AAAA;AAAA;AAAA;AAAA,EAMVC,EAAY,yBAAyBF,CAAI,IAC1CE,EAAY,yBAAyBF,CAAI,EAAIC", "x_google_ignoreList": [0]}