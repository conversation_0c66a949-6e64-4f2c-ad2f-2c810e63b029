import{an as t,ao as r}from"./index-Dpxo-yl_.js";import{A as n}from"./objectModelMapping-BR4RdEzn.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";const o="KHR_node_visibility";n("/nodes/{}/extensions/KHR_node_visibility/visible",{get:s=>{const i=s._babylonTransformNode;return i&&i.isVisible!==void 0?i.isVisible:!0},set:(s,i)=>{i._primitiveBabylonMeshes?.forEach(e=>{e.inheritVisibility=!0}),i._babylonTransformNode&&(i._babylonTransformNode.isVisible=s),i._primitiveBabylonMeshes?.forEach(e=>{e.isVisible=s})},getTarget:s=>s._babylonTransformNode,getPropertyName:[()=>"isVisible"],type:"boolean"});class a{constructor(i){this.name=o,this._loader=i,this.enabled=i.isExtensionUsed(o)}async onReady(){this._loader.gltf.nodes?.forEach(i=>{i._primitiveBabylonMeshes?.forEach(e=>{e.inheritVisibility=!0}),i.extensions?.KHR_node_visibility&&i.extensions?.KHR_node_visibility.visible===!1&&(i._babylonTransformNode&&(i._babylonTransformNode.isVisible=!1),i._primitiveBabylonMeshes?.forEach(e=>{e.isVisible=!1}))})}dispose(){this._loader=null}}t(o);r(o,!0,s=>new a(s));export{a as KHR_node_visibility};
//# sourceMappingURL=KHR_node_visibility-B3_V6RIr.js.map
