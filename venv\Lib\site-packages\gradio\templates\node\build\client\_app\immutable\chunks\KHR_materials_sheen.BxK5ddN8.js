import{ar as d,C as i,an as c,ao as a}from"./index.BoI39RQH.js";import{GLTFLoader as m}from"./glTFLoader.BetPWe9U.js";const h="KHR_materials_sheen";class l{constructor(n){this.name=h,this.order=190,this._loader=n,this.enabled=this._loader.isExtensionUsed(h)}dispose(){this._loader=null}loadMaterialPropertiesAsync(n,s,e){return m.LoadExtensionAsync(n,s,this.name,(r,o)=>{const u=new Array;return u.push(this._loader.loadMaterialPropertiesAsync(n,s,e)),u.push(this._loadSheenPropertiesAsync(r,o,e)),Promise.all(u).then(()=>{})})}_loadSheenPropertiesAsync(n,s,e){if(!(e instanceof d))throw new Error(`${n}: Material type not supported`);const r=new Array;return e.sheen.isEnabled=!0,e.sheen.intensity=1,s.sheenColorFactor!=null?e.sheen.color=i.FromArray(s.sheenColorFactor):e.sheen.color=i.Black(),s.sheenColorTexture&&r.push(this._loader.loadTextureInfoAsync(`${n}/sheenColorTexture`,s.sheenColorTexture,o=>{o.name=`${e.name} (Sheen Color)`,e.sheen.texture=o})),s.sheenRoughnessFactor!==void 0?e.sheen.roughness=s.sheenRoughnessFactor:e.sheen.roughness=0,s.sheenRoughnessTexture&&(s.sheenRoughnessTexture.nonColorData=!0,r.push(this._loader.loadTextureInfoAsync(`${n}/sheenRoughnessTexture`,s.sheenRoughnessTexture,o=>{o.name=`${e.name} (Sheen Roughness)`,e.sheen.textureRoughness=o}))),e.sheen.albedoScaling=!0,e.sheen.useRoughnessFromMainTexture=!1,Promise.all(r).then(()=>{})}}c(h);a(h,!0,t=>new l(t));export{l as KHR_materials_sheen};
//# sourceMappingURL=KHR_materials_sheen.BxK5ddN8.js.map
