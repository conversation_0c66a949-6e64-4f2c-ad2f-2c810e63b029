import{F as a}from"./flowGraphUnaryOperationBlock-BAOMqWtD.js";import{c as n,b as s,j as p,F as e}from"./declarationMapper-BZjsjg7g.js";import{R as l}from"./index-Dpxo-yl_.js";import"./flowGraphCachedOperationBlock-DbEh2T66.js";import"./KHR_interactivity-DTxiAnOo.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class F extends a{constructor(r){super(n,s,o=>+o,"FlowGraphBooleanToFloat",r)}}l("FlowGraphBooleanToFloat",F);class u extends a{constructor(r){super(n,p,o=>e.FromValue(+o),"FlowGraphBooleanToInt",r)}}l("FlowGraphBooleanToInt",u);class c extends a{constructor(r){super(s,n,o=>!!o,"FlowGraphFloatToBoolean",r)}}l("FlowGraphFloatToBoolean",c);class h extends a{constructor(r){super(p,n,o=>!!o.value,"FlowGraphIntToBoolean",r)}}l("FlowGraphIntToBoolean",h);class w extends a{constructor(r){super(p,s,o=>o.value,"FlowGraphIntToFloat",r)}}l("FlowGraphIntToFloat",w);class G extends a{constructor(r){super(s,p,o=>{switch(r?.roundingMode){case"floor":return e.FromValue(Math.floor(o));case"ceil":return e.FromValue(Math.ceil(o));case"round":return e.FromValue(Math.round(o));default:return e.FromValue(o)}},"FlowGraphFloatToInt",r)}}l("FlowGraphFloatToInt",G);export{F as FlowGraphBooleanToFloat,u as FlowGraphBooleanToInt,c as FlowGraphFloatToBoolean,G as FlowGraphFloatToInt,h as FlowGraphIntToBoolean,w as FlowGraphIntToFloat};
//# sourceMappingURL=flowGraphTypeToTypeBlocks-CqDvUIFf.js.map
