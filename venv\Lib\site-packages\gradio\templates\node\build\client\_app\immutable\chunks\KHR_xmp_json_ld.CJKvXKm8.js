import{an as i,ao as _}from"./index.BoI39RQH.js";const s="KHR_xmp_json_ld";class d{constructor(t){this.name=s,this.order=100,this._loader=t,this.enabled=this._loader.isExtensionUsed(s)}dispose(){this._loader=null}onLoading(){var a,n,l;if(this._loader.rootBabylonMesh===null)return;const t=(a=this._loader.gltf.extensions)==null?void 0:a.KHR_xmp_json_ld,o=(l=(n=this._loader.gltf.asset)==null?void 0:n.extensions)==null?void 0:l.KHR_xmp_json_ld;if(t&&o){const r=+o.packet;t.packets&&r<t.packets.length&&(this._loader.rootBabylonMesh.metadata=this._loader.rootBabylonMesh.metadata||{},this._loader.rootBabylonMesh.metadata.xmp=t.packets[r])}}}i(s);_(s,!0,e=>new d(e));export{d as KHR_xmp_json_ld};
//# sourceMappingURL=KHR_xmp_json_ld.CJKvXKm8.js.map
