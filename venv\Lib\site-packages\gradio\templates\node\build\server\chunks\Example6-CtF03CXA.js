import { c as create_ssr_component, e as escape } from './ssr-C3HYbsxA.js';

const A={code:".gallery.svelte-1ayixqk{padding:var(--size-1) var(--size-2)}",map:'{"version":3,"file":"Example.svelte","sources":["Example.svelte"],"sourcesContent":["<script lang=\\"ts\\">export let value;\\nexport let type;\\nexport let selected = false;\\nexport let choices;\\nlet names = value.map((val) => choices.find((pair) => pair[1] === val)?.[0]).filter((name) => name !== void 0);\\nlet names_string = names.join(\\", \\");\\n<\/script>\\n\\n<div\\n\\tclass:table={type === \\"table\\"}\\n\\tclass:gallery={type === \\"gallery\\"}\\n\\tclass:selected\\n>\\n\\t{names_string}\\n</div>\\n\\n<style>\\n\\t.gallery {\\n\\t\\tpadding: var(--size-1) var(--size-2);\\n\\t}</style>\\n"],"names":[],"mappings":"AAiBC,uBAAS,CACR,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CACpC"}'},f=create_ssr_component((c,e,t,m)=>{let{value:a}=e,{type:l}=e,{selected:s=!1}=e,{choices:i}=e,v=a.map(n=>i.find(o=>o[1]===n)?.[0]).filter(n=>n!==void 0).join(", ");return e.value===void 0&&t.value&&a!==void 0&&t.value(a),e.type===void 0&&t.type&&l!==void 0&&t.type(l),e.selected===void 0&&t.selected&&s!==void 0&&t.selected(s),e.choices===void 0&&t.choices&&i!==void 0&&t.choices(i),c.css.add(A),`<div class="${["svelte-1ayixqk",(l==="table"?"table":"")+" "+(l==="gallery"?"gallery":"")+" "+(s?"selected":"")].join(" ").trim()}">${escape(v)} </div>`});

export { f as default };
//# sourceMappingURL=Example6-CtF03CXA.js.map
