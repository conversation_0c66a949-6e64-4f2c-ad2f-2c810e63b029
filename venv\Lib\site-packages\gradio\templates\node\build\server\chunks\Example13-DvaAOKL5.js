import { c as create_ssr_component, f as each, e as escape } from './ssr-C3HYbsxA.js';

const c={code:"ul.svelte-4tf8f{white-space:nowrap;max-height:100px;list-style:none;padding:0;margin:0}.extra.svelte-4tf8f{text-align:center}.gallery.svelte-4tf8f{align-items:center;cursor:pointer;padding:var(--size-1) var(--size-2);text-align:left}",map:'{"version":3,"file":"Example.svelte","sources":["Example.svelte"],"sourcesContent":["<script lang=\\"ts\\">export let value;\\nexport let type;\\nexport let selected = false;\\n<\/script>\\n\\n<ul\\n\\tclass:table={type === \\"table\\"}\\n\\tclass:gallery={type === \\"gallery\\"}\\n\\tclass:selected\\n>\\n\\t{#if value}\\n\\t\\t{#each Array.isArray(value) ? value.slice(0, 3) : [value] as path}\\n\\t\\t\\t<li><code>./{path}</code></li>\\n\\t\\t{/each}\\n\\t\\t{#if Array.isArray(value) && value.length > 3}\\n\\t\\t\\t<li class=\\"extra\\">...</li>\\n\\t\\t{/if}\\n\\t{/if}\\n</ul>\\n\\n<style>\\n\\tul {\\n\\t\\twhite-space: nowrap;\\n\\t\\tmax-height: 100px;\\n\\t\\tlist-style: none;\\n\\t\\tpadding: 0;\\n\\t\\tmargin: 0;\\n\\t}\\n\\n\\t.extra {\\n\\t\\ttext-align: center;\\n\\t}\\n\\n\\t.gallery {\\n\\t\\talign-items: center;\\n\\t\\tcursor: pointer;\\n\\t\\tpadding: var(--size-1) var(--size-2);\\n\\t\\ttext-align: left;\\n\\t}</style>\\n"],"names":[],"mappings":"AAqBC,eAAG,CACF,WAAW,CAAE,MAAM,CACnB,UAAU,CAAE,KAAK,CACjB,UAAU,CAAE,IAAI,CAChB,OAAO,CAAE,CAAC,CACV,MAAM,CAAE,CACT,CAEA,mBAAO,CACN,UAAU,CAAE,MACb,CAEA,qBAAS,CACR,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,OAAO,CACf,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CACpC,UAAU,CAAE,IACb"}'},p=create_ssr_component((n,t,A,v)=>{let{value:e}=t,{type:l}=t,{selected:a=!1}=t;return t.value===void 0&&A.value&&e!==void 0&&A.value(e),t.type===void 0&&A.type&&l!==void 0&&A.type(l),t.selected===void 0&&A.selected&&a!==void 0&&A.selected(a),n.css.add(c),`<ul class="${["svelte-4tf8f",(l==="table"?"table":"")+" "+(l==="gallery"?"gallery":"")+" "+(a?"selected":"")].join(" ").trim()}">${e?`${each(Array.isArray(e)?e.slice(0,3):[e],s=>`<li><code>./${escape(s)}</code></li>`)} ${Array.isArray(e)&&e.length>3?'<li class="extra svelte-4tf8f" data-svelte-h="svelte-17d9ayl">...</li>':""}`:""} </ul>`});

export { p as default };
//# sourceMappingURL=Example13-DvaAOKL5.js.map
