const __vite__fileDeps=["./rgbdDecode.fragment.CzHiO_Z5.js","./index.BoI39RQH.js","./preload-helper.D6kgxu3v.js","./helperFunctions.Ck4K-qjq.js","./rgbdDecode.fragment.CpYe6Awb.js","./helperFunctions.BZ696Q10.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as D}from"./preload-helper.D6kgxu3v.js";import{h as G,I as C,B as O,b as z,ag as V,ah as k,ai as H,V as m}from"./index.BoI39RQH.js";import"./dumpTools.DaNYYVzh.js";const v="image/png",E=2,S=[134,22,135,150,246,214,150,54];function J(e){const a=new DataView(e.buffer,e.byteOffset,e.byteLength);let t=0;for(let o=0;o<S.length;o++)if(a.getUint8(t++)!==S[o])return G.Error("Not a babylon environment map"),null;let n="",s=0;for(;s=a.getUint8(t++);)n+=String.fromCharCode(s);let r=JSON.parse(n);return r=R(r),r.binaryDataPosition=t,r.specular&&(r.specular.lodGenerationScale=r.specular.lodGenerationScale||.8),r}function R(e){if(e.version>E)throw new Error(`Unsupported babylon environment map version "${e.version}". Latest supported version is "${E}".`);return e.version===2||(e={...e,version:2,imageType:v}),e}function P(e,a){a=R(a);const t=a.specular;let n=Math.log2(a.width);if(n=Math.round(n)+1,t.mipmaps.length!==6*n)throw new Error(`Unsupported specular mipmaps number "${t.mipmaps.length}"`);const s=new Array(n);for(let r=0;r<n;r++){s[r]=new Array(6);for(let o=0;o<6;o++){const l=t.mipmaps[r*6+o];s[r][o]=new Uint8Array(e.buffer,e.byteOffset+a.binaryDataPosition+l.position,l.length)}}return s}function Y(e,a){var s;a=R(a);const t=new Array(6),n=(s=a.irradiance)==null?void 0:s.irradianceTexture;if(n){if(n.faces.length!==6)throw new Error(`Incorrect irradiance texture faces number "${n.faces.length}"`);for(let r=0;r<6;r++){const o=n.faces[r];t[r]=new Uint8Array(e.buffer,e.byteOffset+a.binaryDataPosition+o.position,o.length)}}return t}function q(e,a,t){var l;t=R(t);const n=t.specular;if(!n)return Promise.resolve([]);e._lodGenerationScale=n.lodGenerationScale;const s=[],r=P(a,t);s.push(F(e,r,t.imageType));const o=(l=t.irradiance)==null?void 0:l.irradianceTexture;if(o){const d=Y(a,t);s.push($(e,d,o.size,t.imageType))}return Promise.all(s)}function U(e,a,t,n,s,r,o,l,d,u,w){return new Promise((y,I)=>{if(t){const g=a.createTexture(null,!0,!0,null,1,null,i=>{I(i)},e);n==null||n.onEffectCreatedObservable.addOnce(i=>{i.executeWhenCompiled(()=>{n.externalTextureSamplerBinding=!0,n.onApply=c=>{c._bindTexture("textureSampler",g),c.setFloat2("scale",1,a._features.needsInvertingBitmap&&e instanceof ImageBitmap?-1:1)},a.scenes.length&&(a.scenes[0].postProcessManager.directRender([n],u,!0,r,o),a.restoreDefaultFramebuffer(),g.dispose(),URL.revokeObjectURL(s),y())})})}else{if(a._uploadImageToTexture(w,e,r,o),l){const g=d[o];g&&a._uploadImageToTexture(g._texture,e,r,0)}y()}})}async function F(e,a,t=v){const n=e.getEngine();e.format=5,e.type=0,e.generateMipMaps=!0,e._cachedAnisotropicFilteringLevel=null,n.updateTextureSamplingMode(3,e),await B(e,a,!0,t),e.isReady=!0}async function $(e,a,t,n=v){const s=e.getEngine(),r=new C(s,5),o=new O(s,r);e._irradianceTexture=o,r.isCube=!0,r.format=5,r.type=0,r.generateMipMaps=!0,r._cachedAnisotropicFilteringLevel=null,r.generateMipMaps=!0,r.width=t,r.height=t,s.updateTextureSamplingMode(3,r),await B(r,[a],!1,n),s.generateMipMapsForCubemap(r),r.isReady=!0}async function B(e,a,t,n=v){if(!z.IsExponentOfTwo(e.width))throw new Error("Texture size must be a power of two");const s=V(e.width)+1,r=e.getEngine();let o=!1,l=!1,d=null,u=null,w=null;const y=r.getCaps();y.textureLOD?r._features.supportRenderAndCopyToLodForFloatTextures?y.textureHalfFloatRender&&y.textureHalfFloatLinearFiltering?(o=!0,e.type=2):y.textureFloatRender&&y.textureFloatLinearFiltering&&(o=!0,e.type=1):o=!1:(o=!1,l=t);let I=0;if(o)r.isWebGPU?(I=1,await D(()=>import("./rgbdDecode.fragment.CzHiO_Z5.js"),__vite__mapDeps([0,1,2,3]),import.meta.url)):await D(()=>import("./rgbdDecode.fragment.CpYe6Awb.js"),__vite__mapDeps([4,1,2,5]),import.meta.url),d=new k("rgbdDecode","rgbdDecode",null,null,1,null,3,r,!1,void 0,e.type,void 0,null,!1,void 0,I),e._isRGBD=!1,e.invertY=!1,u=r.createRenderTargetCubeTexture(e.width,{generateDepthBuffer:!1,generateMipMaps:!0,generateStencilBuffer:!1,samplingMode:3,type:e.type,format:5});else if(e._isRGBD=!0,e.invertY=!0,l){w={};const c=e._lodGenerationScale,_=e._lodGenerationOffset;for(let p=0;p<3;p++){const b=1-p/2,f=_,M=(s-1)*c+_,L=f+(M-f)*b,A=Math.round(Math.min(Math.max(L,0),M)),x=new C(r,2);x.isCube=!0,x.invertY=!0,x.generateMipMaps=!1,r.updateTextureSamplingMode(2,x);const T=new O(null);switch(T._isCube=!0,T._texture=x,w[A]=T,p){case 0:e._lodTextureLow=T;break;case 1:e._lodTextureMid=T;break;case 2:e._lodTextureHigh=T;break}}}const g=[];for(let i=0;i<a.length;i++)for(let c=0;c<6;c++){const _=a[i][c],p=new Blob([_],{type:n}),h=URL.createObjectURL(p);let b;if(r._features.forceBitmapOverHTMLImageElement)b=r.createImageBitmap(p,{premultiplyAlpha:"none"}).then(f=>U(f,r,o,d,h,c,i,l,w,u,e));else{const f=new Image;f.src=h,b=new Promise((M,L)=>{f.onload=()=>{U(f,r,o,d,h,c,i,l,w,u,e).then(()=>M()).catch(A=>{L(A)})},f.onerror=A=>{L(A)}})}g.push(b)}if(await Promise.all(g),a.length<s){let i;const c=Math.pow(2,s-1-a.length),_=c*c*4;switch(e.type){case 0:{i=new Uint8Array(_);break}case 2:{i=new Uint16Array(_);break}case 1:{i=new Float32Array(_);break}}for(let p=a.length;p<s;p++)for(let h=0;h<6;h++)r._uploadArrayBufferViewToTexture((u==null?void 0:u.texture)||e,i,h,p)}if(u){const i=e._irradianceTexture;e._irradianceTexture=null,r._releaseTexture(e),u._swapAndDie(e),e._irradianceTexture=i}d&&d.dispose(),l&&(e._lodTextureHigh&&e._lodTextureHigh._texture&&(e._lodTextureHigh._texture.isReady=!0),e._lodTextureMid&&e._lodTextureMid._texture&&(e._lodTextureMid._texture.isReady=!0),e._lodTextureLow&&e._lodTextureLow._texture&&(e._lodTextureLow._texture.isReady=!0))}function K(e,a){a=R(a);const t=a.irradiance;if(!t)return;const n=new H;m.FromArrayToRef(t.x,0,n.x),m.FromArrayToRef(t.y,0,n.y),m.FromArrayToRef(t.z,0,n.z),m.FromArrayToRef(t.xx,0,n.xx),m.FromArrayToRef(t.yy,0,n.yy),m.FromArrayToRef(t.zz,0,n.zz),m.FromArrayToRef(t.yz,0,n.yz),m.FromArrayToRef(t.zx,0,n.zx),m.FromArrayToRef(t.xy,0,n.xy),e._sphericalPolynomial=n}function Q(e,a,t,n,s){const r=e.getEngine().createRawCubeTexture(null,e.width,e.format,e.type,e.generateMipMaps,e.invertY,e.samplingMode,e._compression),o=F(r,a).then(()=>e);return e.onRebuildCallback=l=>({proxy:o,isReady:!0,isAsync:!0}),e._source=13,e._bufferViewArrayArray=a,e._lodGenerationScale=n,e._lodGenerationOffset=s,e._sphericalPolynomial=t,F(e,a).then(()=>(e.isReady=!0,e))}export{J as G,K as U,Q as _,q as a};
//# sourceMappingURL=environmentTextureTools.DZJEvUsQ.js.map
