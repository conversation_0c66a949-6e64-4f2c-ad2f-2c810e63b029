{"version": 3, "file": "flowGraphJsonPointerParserBlock.B04B8uAV.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/flowGraphPathConverterComponent.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Transformers/flowGraphJsonPointerParserBlock.js"], "sourcesContent": ["import { FlowGraphInteger } from \"./CustomTypes/flowGraphInteger.js\";\nimport { RichTypeFlowGraphInteger } from \"./flowGraphRichTypes.js\";\nconst pathHasTemplatesRegex = new RegExp(/\\/\\{(\\w+)\\}\\//g);\n/**\n * @experimental\n * A component that converts a path to an object accessor.\n */\nexport class FlowGraphPathConverterComponent {\n    constructor(path, ownerBlock) {\n        this.path = path;\n        this.ownerBlock = ownerBlock;\n        /**\n         * The templated inputs for the provided path.\n         */\n        this.templatedInputs = [];\n        let match = pathHasTemplatesRegex.exec(path);\n        const templateSet = new Set();\n        while (match) {\n            const [, matchGroup] = match;\n            if (templateSet.has(matchGroup)) {\n                throw new Error(\"Duplicate template variable detected.\");\n            }\n            templateSet.add(matchGroup);\n            this.templatedInputs.push(ownerBlock.registerDataInput(matchGroup, RichTypeFlowGraphInteger, new FlowGraphInteger(0)));\n            match = pathHasTemplatesRegex.exec(path);\n        }\n    }\n    /**\n     * Get the accessor for the path.\n     * @param pathConverter the path converter to use to convert the path to an object accessor.\n     * @param context the context to use.\n     * @returns the accessor for the path.\n     * @throws if the value for a templated input is invalid.\n     */\n    getAccessor(pathConverter, context) {\n        let finalPath = this.path;\n        for (const templatedInput of this.templatedInputs) {\n            const valueToReplace = templatedInput.getValue(context).value;\n            if (typeof valueToReplace !== \"number\" || valueToReplace < 0) {\n                throw new Error(\"Invalid value for templated input.\");\n            }\n            finalPath = finalPath.replace(`{${templatedInput.name}}`, valueToReplace.toString());\n        }\n        return pathConverter.convert(finalPath);\n    }\n}\n//# sourceMappingURL=flowGraphPathConverterComponent.js.map", "import { FlowGraphPathConverterComponent } from \"../../../flowGraphPathConverterComponent.js\";\nimport { RichTypeAny } from \"../../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\nimport { Color3, Color4 } from \"../../../../Maths/math.color.js\";\nimport { FlowGraphCachedOperationBlock } from \"../flowGraphCachedOperationBlock.js\";\n/**\n * This block will take a JSON pointer and parse it to get the value from the JSON object.\n * The output is an object and a property name.\n * Optionally, the block can also output the value of the property. This is configurable.\n */\nexport class FlowGraphJsonPointerParserBlock extends FlowGraphCachedOperationBlock {\n    constructor(\n    /**\n     * the configuration of the block\n     */\n    config) {\n        super(RichTypeAny, config);\n        this.config = config;\n        this.object = this.registerDataOutput(\"object\", RichTypeAny);\n        this.propertyName = this.registerDataOutput(\"propertyName\", RichTypeAny);\n        this.setterFunction = this.registerDataOutput(\"setFunction\", RichTypeAny, this._setPropertyValue.bind(this));\n        this.getterFunction = this.registerDataOutput(\"getFunction\", RichTypeAny, this._getPropertyValue.bind(this));\n        this.generateAnimationsFunction = this.registerDataOutput(\"generateAnimationsFunction\", RichTypeAny, this._getInterpolationAnimationPropertyInfo.bind(this));\n        this.templateComponent = new FlowGraphPathConverterComponent(config.jsonPointer, this);\n    }\n    _doOperation(context) {\n        const accessorContainer = this.templateComponent.getAccessor(this.config.pathConverter, context);\n        const value = accessorContainer.info.get(accessorContainer.object);\n        const object = accessorContainer.info.getTarget?.(accessorContainer.object);\n        const propertyName = accessorContainer.info.getPropertyName?.[0](accessorContainer.object);\n        if (!object) {\n            throw new Error(\"Object is undefined\");\n        }\n        else {\n            this.object.setValue(object, context);\n            if (propertyName) {\n                this.propertyName.setValue(propertyName, context);\n            }\n        }\n        return value;\n    }\n    _setPropertyValue(_target, _propertyName, value, context) {\n        const accessorContainer = this.templateComponent.getAccessor(this.config.pathConverter, context);\n        const type = accessorContainer.info.type;\n        if (type.startsWith(\"Color\")) {\n            value = ToColor(value, type);\n        }\n        accessorContainer.info.set?.(value, accessorContainer.object);\n    }\n    _getPropertyValue(_target, _propertyName, context) {\n        const accessorContainer = this.templateComponent.getAccessor(this.config.pathConverter, context);\n        return accessorContainer.info.get(accessorContainer.object);\n    }\n    _getInterpolationAnimationPropertyInfo(_target, _propertyName, context) {\n        const accessorContainer = this.templateComponent.getAccessor(this.config.pathConverter, context);\n        return (keys, fps, animationType, easingFunction) => {\n            const animations = [];\n            // make sure keys are of the right type (in case of float3 color/vector)\n            const type = accessorContainer.info.type;\n            if (type.startsWith(\"Color\")) {\n                keys = keys.map((key) => {\n                    return {\n                        frame: key.frame,\n                        value: ToColor(key.value, type),\n                    };\n                });\n            }\n            accessorContainer.info.interpolation?.forEach((info, index) => {\n                const name = accessorContainer.info.getPropertyName?.[index](accessorContainer.object) || \"Animation-interpolation-\" + index;\n                // generate the keys based on interpolation info\n                let newKeys = keys;\n                if (animationType !== info.type) {\n                    // convert the keys to the right type\n                    newKeys = keys.map((key) => {\n                        return {\n                            frame: key.frame,\n                            value: info.getValue(undefined, key.value.asArray ? key.value.asArray() : [key.value], 0, 1),\n                        };\n                    });\n                }\n                const animationData = info.buildAnimations(accessorContainer.object, name, 60, newKeys);\n                animationData.forEach((animation) => {\n                    if (easingFunction) {\n                        animation.babylonAnimation.setEasingFunction(easingFunction);\n                    }\n                    animations.push(animation.babylonAnimation);\n                });\n            });\n            return animations;\n        };\n    }\n    /**\n     * Gets the class name of this block\n     * @returns the class name\n     */\n    getClassName() {\n        return \"FlowGraphJsonPointerParserBlock\" /* FlowGraphBlockNames.JsonPointerParser */;\n    }\n}\nfunction ToColor(value, expectedValue) {\n    if (value.getClassName().startsWith(\"Color\")) {\n        return value;\n    }\n    if (expectedValue === \"Color3\") {\n        return new Color3(value.x, value.y, value.z);\n    }\n    else if (expectedValue === \"Color4\") {\n        return new Color4(value.x, value.y, value.z, value.w);\n    }\n    return value;\n}\nRegisterClass(\"FlowGraphJsonPointerParserBlock\" /* FlowGraphBlockNames.JsonPointerParser */, FlowGraphJsonPointerParserBlock);\n//# sourceMappingURL=flowGraphJsonPointerParserBlock.js.map"], "names": ["pathHasTemplatesRegex", "FlowGraphPathConverterComponent", "path", "owner<PERSON>lock", "match", "templateSet", "matchGroup", "RichTypeFlowGraphInteger", "FlowGraphInteger", "pathConverter", "context", "finalPath", "templatedInput", "valueToReplace", "FlowGraphJsonPointerParserBlock", "FlowGraphCachedOperationBlock", "config", "RichTypeAny", "accessorContainer", "value", "object", "_b", "_a", "propertyName", "_c", "_target", "_propertyName", "type", "ToColor", "keys", "fps", "animationType", "easingFunction", "animations", "key", "info", "index", "name", "newKeys", "animation", "expectedValue", "Color3", "Color4", "RegisterClass"], "mappings": "yLAEA,MAAMA,EAAwB,IAAI,OAAO,gBAAgB,EAKlD,MAAMC,CAAgC,CACzC,YAAYC,EAAMC,EAAY,CAC1B,KAAK,KAAOD,EACZ,KAAK,WAAaC,EAIlB,KAAK,gBAAkB,GACvB,IAAIC,EAAQJ,EAAsB,KAAKE,CAAI,EAC3C,MAAMG,EAAc,IAAI,IACxB,KAAOD,GAAO,CACV,KAAM,CAAG,CAAAE,CAAU,EAAIF,EACvB,GAAIC,EAAY,IAAIC,CAAU,EAC1B,MAAM,IAAI,MAAM,uCAAuC,EAE3DD,EAAY,IAAIC,CAAU,EAC1B,KAAK,gBAAgB,KAAKH,EAAW,kBAAkBG,EAAYC,EAA0B,IAAIC,EAAiB,CAAC,CAAC,CAAC,EACrHJ,EAAQJ,EAAsB,KAAKE,CAAI,CAC1C,CACJ,CAQD,YAAYO,EAAeC,EAAS,CAChC,IAAIC,EAAY,KAAK,KACrB,UAAWC,KAAkB,KAAK,gBAAiB,CAC/C,MAAMC,EAAiBD,EAAe,SAASF,CAAO,EAAE,MACxD,GAAI,OAAOG,GAAmB,UAAYA,EAAiB,EACvD,MAAM,IAAI,MAAM,oCAAoC,EAExDF,EAAYA,EAAU,QAAQ,IAAIC,EAAe,IAAI,IAAKC,EAAe,SAAU,CAAA,CACtF,CACD,OAAOJ,EAAc,QAAQE,CAAS,CACzC,CACL,CCnCO,MAAMG,UAAwCC,CAA8B,CAC/E,YAIAC,EAAQ,CACJ,MAAMC,EAAaD,CAAM,EACzB,KAAK,OAASA,EACd,KAAK,OAAS,KAAK,mBAAmB,SAAUC,CAAW,EAC3D,KAAK,aAAe,KAAK,mBAAmB,eAAgBA,CAAW,EACvE,KAAK,eAAiB,KAAK,mBAAmB,cAAeA,EAAa,KAAK,kBAAkB,KAAK,IAAI,CAAC,EAC3G,KAAK,eAAiB,KAAK,mBAAmB,cAAeA,EAAa,KAAK,kBAAkB,KAAK,IAAI,CAAC,EAC3G,KAAK,2BAA6B,KAAK,mBAAmB,6BAA8BA,EAAa,KAAK,uCAAuC,KAAK,IAAI,CAAC,EAC3J,KAAK,kBAAoB,IAAIhB,EAAgCe,EAAO,YAAa,IAAI,CACxF,CACD,aAAaN,EAAS,WAClB,MAAMQ,EAAoB,KAAK,kBAAkB,YAAY,KAAK,OAAO,cAAeR,CAAO,EACzFS,EAAQD,EAAkB,KAAK,IAAIA,EAAkB,MAAM,EAC3DE,GAASC,GAAAC,EAAAJ,EAAkB,MAAK,YAAvB,YAAAG,EAAA,KAAAC,EAAmCJ,EAAkB,QAC9DK,GAAeC,EAAAN,EAAkB,KAAK,kBAAvB,YAAAM,EAAyC,GAAGN,EAAkB,QACnF,GAAKE,EAID,KAAK,OAAO,SAASA,EAAQV,CAAO,EAChCa,GACA,KAAK,aAAa,SAASA,EAAcb,CAAO,MALpD,OAAM,IAAI,MAAM,qBAAqB,EAQzC,OAAOS,CACV,CACD,kBAAkBM,EAASC,EAAeP,EAAOT,EAAS,SACtD,MAAMQ,EAAoB,KAAK,kBAAkB,YAAY,KAAK,OAAO,cAAeR,CAAO,EACzFiB,EAAOT,EAAkB,KAAK,KAChCS,EAAK,WAAW,OAAO,IACvBR,EAAQS,EAAQT,EAAOQ,CAAI,IAE/BN,GAAAC,EAAAJ,EAAkB,MAAK,MAAvB,MAAAG,EAAA,KAAAC,EAA6BH,EAAOD,EAAkB,OACzD,CACD,kBAAkBO,EAASC,EAAehB,EAAS,CAC/C,MAAMQ,EAAoB,KAAK,kBAAkB,YAAY,KAAK,OAAO,cAAeR,CAAO,EAC/F,OAAOQ,EAAkB,KAAK,IAAIA,EAAkB,MAAM,CAC7D,CACD,uCAAuCO,EAASC,EAAehB,EAAS,CACpE,MAAMQ,EAAoB,KAAK,kBAAkB,YAAY,KAAK,OAAO,cAAeR,CAAO,EAC/F,MAAO,CAACmB,EAAMC,EAAKC,EAAeC,IAAmB,OACjD,MAAMC,EAAa,CAAA,EAEbN,EAAOT,EAAkB,KAAK,KACpC,OAAIS,EAAK,WAAW,OAAO,IACvBE,EAAOA,EAAK,IAAKK,IACN,CACH,MAAOA,EAAI,MACX,MAAON,EAAQM,EAAI,MAAOP,CAAI,CACtD,EACiB,IAELL,EAAAJ,EAAkB,KAAK,gBAAvB,MAAAI,EAAsC,QAAQ,CAACa,EAAMC,IAAU,OAC3D,MAAMC,IAAOf,EAAAJ,EAAkB,KAAK,kBAAvB,YAAAI,EAAyCc,GAAOlB,EAAkB,UAAW,2BAA6BkB,EAEvH,IAAIE,EAAUT,EACVE,IAAkBI,EAAK,OAEvBG,EAAUT,EAAK,IAAKK,IACT,CACH,MAAOA,EAAI,MACX,MAAOC,EAAK,SAAS,OAAWD,EAAI,MAAM,QAAUA,EAAI,MAAM,QAAS,EAAG,CAACA,EAAI,KAAK,EAAG,EAAG,CAAC,CACvH,EACqB,GAEiBC,EAAK,gBAAgBjB,EAAkB,OAAQmB,EAAM,GAAIC,CAAO,EACxE,QAASC,GAAc,CAC7BP,GACAO,EAAU,iBAAiB,kBAAkBP,CAAc,EAE/DC,EAAW,KAAKM,EAAU,gBAAgB,CAC9D,CAAiB,CACjB,GACmBN,CACnB,CACK,CAKD,cAAe,CACX,MAAO,iCACV,CACL,CACA,SAASL,EAAQT,EAAOqB,EAAe,CACnC,OAAIrB,EAAM,aAAY,EAAG,WAAW,OAAO,EAChCA,EAEPqB,IAAkB,SACX,IAAIC,EAAOtB,EAAM,EAAGA,EAAM,EAAGA,EAAM,CAAC,EAEtCqB,IAAkB,SAChB,IAAIE,EAAOvB,EAAM,EAAGA,EAAM,EAAGA,EAAM,EAAGA,EAAM,CAAC,EAEjDA,CACX,CACAwB,EAAc,kCAA+E7B,CAA+B", "x_google_ignoreList": [0, 1]}