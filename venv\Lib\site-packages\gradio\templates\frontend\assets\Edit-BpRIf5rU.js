const{SvelteComponent:l,append:d,attr:e,detach:c,init:h,insert:p,noop:r,safe_not_equal:_,svg_element:s}=window.__gradio__svelte__internal;function u(i){let t,n;return{c(){t=s("svg"),n=s("path"),e(n,"d","M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 24 24"),e(t,"fill","none"),e(t,"stroke","currentColor"),e(t,"stroke-width","1.5"),e(t,"stroke-linecap","round"),e(t,"stroke-linejoin","round"),e(t,"class","feather feather-edit-2")},m(o,a){p(o,t,a),d(t,n)},p:r,i:r,o:r,d(o){o&&c(t)}}}class w extends l{constructor(t){super(),h(this,t,null,u,_,{})}}export{w as E};
//# sourceMappingURL=Edit-BpRIf5rU.js.map
