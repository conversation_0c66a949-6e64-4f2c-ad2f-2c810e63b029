"""
头像编辑器组件
支持照片选择、裁剪、缩放等操作
"""

import os
from pathlib import Path
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QFileDialog, QSlider, QFrame,
                            QDialog, QDialogButtonBox, QGraphicsView,
                            QGraphicsScene, QGraphicsPixmapItem, QGraphicsEllipseItem)
from PyQt6.QtCore import Qt, pyqtSignal, QRectF
from PyQt6.QtGui import QPixmap, QPainter, QBrush, QPen, QColor

from core.utils.logger import get_logger

logger = get_logger(__name__)

class AvatarCropDialog(QDialog):
    """头像裁剪对话框"""
    
    def __init__(self, image_path: str, parent=None):
        super().__init__(parent)
        self.image_path = image_path
        self.cropped_pixmap = None
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("裁剪头像")
        self.setModal(True)
        self.resize(500, 600)
        
        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1a1f2e, stop:1 #2d3548);
                color: #e2e8f0;
            }
            QPushButton {
                background: rgba(102, 126, 234, 0.8);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
                font-size: 13px;
                font-weight: 500;
            }
            QPushButton:hover {
                background: rgba(102, 126, 234, 1.0);
            }
            QSlider::groove:horizontal {
                background: rgba(45, 53, 72, 0.8);
                height: 6px;
                border-radius: 3px;
            }
            QSlider::handle:horizontal {
                background: #667eea;
                width: 18px;
                height: 18px;
                border-radius: 9px;
                margin: -6px 0;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("调整头像")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: 600;
                color: #e2e8f0;
                margin-bottom: 10px;
            }
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 图像显示区域
        self.graphics_view = QGraphicsView()
        self.graphics_view.setFixedSize(400, 400)
        self.graphics_view.setStyleSheet("""
            QGraphicsView {
                background: rgba(45, 53, 72, 0.5);
                border: 2px solid rgba(102, 126, 234, 0.3);
                border-radius: 8px;
            }
        """)
        
        self.scene = QGraphicsScene()
        self.graphics_view.setScene(self.scene)
        
        # 加载图像
        self.load_image()
        
        layout.addWidget(self.graphics_view, 0, Qt.AlignmentFlag.AlignCenter)
        
        # 缩放控制
        scale_frame = QFrame()
        scale_layout = QHBoxLayout(scale_frame)
        scale_layout.setContentsMargins(0, 0, 0, 0)
        
        scale_label = QLabel("缩放:")
        scale_label.setStyleSheet("color: #94a3b8; font-size: 13px;")
        scale_layout.addWidget(scale_label)
        
        self.scale_slider = QSlider(Qt.Orientation.Horizontal)
        self.scale_slider.setRange(50, 200)
        self.scale_slider.setValue(100)
        self.scale_slider.valueChanged.connect(self.on_scale_changed)
        scale_layout.addWidget(self.scale_slider)
        
        scale_value_label = QLabel("100%")
        scale_value_label.setStyleSheet("color: #94a3b8; font-size: 13px;")
        self.scale_value_label = scale_value_label
        scale_layout.addWidget(scale_value_label)
        
        layout.addWidget(scale_frame)
        
        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
    def load_image(self):
        """加载图像"""
        try:
            pixmap = QPixmap(self.image_path)
            if pixmap.isNull():
                logger.error(f"无法加载图像: {self.image_path}")
                return
                
            # 缩放图像以适应视图
            max_size = 350
            if pixmap.width() > max_size or pixmap.height() > max_size:
                pixmap = pixmap.scaled(max_size, max_size, 
                                     Qt.AspectRatioMode.KeepAspectRatio,
                                     Qt.TransformationMode.SmoothTransformation)
            
            self.scene.clear()
            self.pixmap_item = QGraphicsPixmapItem(pixmap)
            self.scene.addItem(self.pixmap_item)
            
            # 添加圆形裁剪框
            self.crop_circle = QGraphicsEllipseItem(0, 0, 200, 200)
            self.crop_circle.setPen(QPen(QColor(102, 126, 234), 3))
            self.crop_circle.setBrush(QBrush(QColor(0, 0, 0, 100)))
            self.scene.addItem(self.crop_circle)
            
            # 居中显示
            self.graphics_view.centerOn(self.pixmap_item)
            
        except Exception as e:
            logger.error(f"加载图像时出错: {e}")
            
    def on_scale_changed(self, value):
        """缩放变化"""
        scale = value / 100.0
        self.scale_value_label.setText(f"{value}%")
        
        if hasattr(self, 'pixmap_item'):
            self.pixmap_item.setScale(scale)
            
    def accept(self):
        """确认裁剪"""
        try:
            # 获取裁剪区域
            crop_rect = self.crop_circle.rect()
            
            # 创建裁剪后的图像
            original_pixmap = QPixmap(self.image_path)
            
            # 计算实际裁剪区域
            scale_factor = min(350 / original_pixmap.width(), 350 / original_pixmap.height())
            actual_rect = QRectF(
                crop_rect.x() / scale_factor,
                crop_rect.y() / scale_factor,
                crop_rect.width() / scale_factor,
                crop_rect.height() / scale_factor
            )
            
            # 裁剪图像
            cropped = original_pixmap.copy(actual_rect.toRect())
            
            # 创建圆形遮罩
            size = min(cropped.width(), cropped.height())
            circular_pixmap = QPixmap(size, size)
            circular_pixmap.fill(Qt.GlobalColor.transparent)
            
            painter = QPainter(circular_pixmap)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            painter.setBrush(QBrush(cropped))
            painter.setPen(Qt.PenStyle.NoPen)
            painter.drawEllipse(0, 0, size, size)
            painter.end()
            
            self.cropped_pixmap = circular_pixmap
            super().accept()
            
        except Exception as e:
            logger.error(f"裁剪图像时出错: {e}")
            super().reject()

class AvatarEditor(QWidget):
    """头像编辑器"""
    
    avatar_changed = pyqtSignal(str, str)  # avatar_type, avatar_path
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setObjectName("avatar_editor")
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 标题
        title_label = QLabel("头像设置")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: 600;
                color: #e2e8f0;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # AI头像设置
        ai_frame = self.create_avatar_section("AI助手头像", "ai")
        layout.addWidget(ai_frame)
        
        # 用户头像设置
        user_frame = self.create_avatar_section("用户头像", "user")
        layout.addWidget(user_frame)
        
    def create_avatar_section(self, title: str, avatar_type: str):
        """创建头像设置区域"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: rgba(45, 53, 72, 0.6);
                border: 1px solid rgba(102, 126, 234, 0.3);
                border-radius: 12px;
                padding: 15px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # 头像预览
        avatar_preview = QLabel()
        avatar_preview.setFixedSize(64, 64)
        avatar_preview.setStyleSheet("""
            QLabel {
                background: rgba(102, 126, 234, 0.3);
                border: 2px solid rgba(102, 126, 234, 0.5);
                border-radius: 32px;
                font-size: 24px;
                color: white;
            }
        """)
        avatar_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        avatar_preview.setText("🤖" if avatar_type == "ai" else "👤")
        
        # 存储引用
        setattr(self, f"{avatar_type}_avatar_preview", avatar_preview)
        
        layout.addWidget(avatar_preview)
        
        # 信息和按钮区域
        info_layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: 600;
                color: #e2e8f0;
            }
        """)
        info_layout.addWidget(title_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 选择图片按钮
        select_button = QPushButton("选择图片")
        select_button.setStyleSheet("""
            QPushButton {
                background: rgba(102, 126, 234, 0.8);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: 500;
            }
            QPushButton:hover {
                background: rgba(102, 126, 234, 1.0);
            }
        """)
        select_button.clicked.connect(lambda: self.select_avatar(avatar_type))
        button_layout.addWidget(select_button)
        
        # 重置按钮
        reset_button = QPushButton("重置")
        reset_button.setStyleSheet("""
            QPushButton {
                background: rgba(239, 68, 68, 0.8);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: 500;
            }
            QPushButton:hover {
                background: rgba(239, 68, 68, 1.0);
            }
        """)
        reset_button.clicked.connect(lambda: self.reset_avatar(avatar_type))
        button_layout.addWidget(reset_button)
        
        info_layout.addLayout(button_layout)
        layout.addLayout(info_layout)
        
        return frame
        
    def select_avatar(self, avatar_type: str):
        """选择头像"""
        file_dialog = QFileDialog(self)
        file_dialog.setNameFilter("图片文件 (*.png *.jpg *.jpeg *.bmp *.gif)")
        file_dialog.setFileMode(QFileDialog.FileMode.ExistingFile)
        
        if file_dialog.exec():
            file_paths = file_dialog.selectedFiles()
            if file_paths:
                image_path = file_paths[0]
                
                # 打开裁剪对话框
                crop_dialog = AvatarCropDialog(image_path, self)
                if crop_dialog.exec():
                    # 保存裁剪后的头像
                    self.save_avatar(avatar_type, crop_dialog.cropped_pixmap)
                    
    def save_avatar(self, avatar_type: str, pixmap: QPixmap):
        """保存头像"""
        try:
            # 创建头像目录
            avatar_dir = Path("assets/avatars")
            avatar_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存文件
            avatar_path = avatar_dir / f"{avatar_type}_avatar.png"
            pixmap.save(str(avatar_path))
            
            # 更新预览
            preview_label = getattr(self, f"{avatar_type}_avatar_preview")
            scaled_pixmap = pixmap.scaled(64, 64, Qt.AspectRatioMode.KeepAspectRatio,
                                        Qt.TransformationMode.SmoothTransformation)
            preview_label.setPixmap(scaled_pixmap)
            preview_label.setText("")
            
            # 发送信号
            self.avatar_changed.emit(avatar_type, str(avatar_path))
            
            logger.info(f"头像已保存: {avatar_path}")
            
        except Exception as e:
            logger.error(f"保存头像时出错: {e}")
            
    def reset_avatar(self, avatar_type: str):
        """重置头像"""
        try:
            # 删除头像文件
            avatar_path = Path("assets/avatars") / f"{avatar_type}_avatar.png"
            if avatar_path.exists():
                avatar_path.unlink()
            
            # 重置预览
            preview_label = getattr(self, f"{avatar_type}_avatar_preview")
            preview_label.clear()
            preview_label.setText("🤖" if avatar_type == "ai" else "👤")
            
            # 发送信号
            self.avatar_changed.emit(avatar_type, "")
            
            logger.info(f"头像已重置: {avatar_type}")
            
        except Exception as e:
            logger.error(f"重置头像时出错: {e}")
