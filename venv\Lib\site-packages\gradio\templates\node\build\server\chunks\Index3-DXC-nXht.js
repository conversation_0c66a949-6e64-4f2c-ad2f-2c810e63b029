import { c as create_ssr_component, v as validate_component, b as createEventDispatcher, e as escape, d as add_attribute } from './ssr-C3HYbsxA.js';
import { T as Tr$1, ap as Ke$1, m as mt, z as zA, e as bt, f as Xt$1, ab as la } from './2-DJbI4FWc.js';
import O$1 from './ImagePreview-dCWEyjhO.js';
import { s } from './tinycolor-BAU9cgwS.js';
import './index-ClteBeTX.js';
import './Component-NmRBwSfF.js';
import 'path';
import 'url';
import 'fs';

var T=(s=>(s.Application="application",s.WebGLPipes="webgl-pipes",s.WebGLPipesAdaptor="webgl-pipes-adaptor",s.WebGLSystem="webgl-system",s.WebGPUPipes="webgpu-pipes",s.WebGPUPipesAdaptor="webgpu-pipes-adaptor",s.WebGPUSystem="webgpu-system",s.CanvasSystem="canvas-system",s.CanvasPipesAdaptor="canvas-pipes-adaptor",s.CanvasPipes="canvas-pipes",s.Asset="asset",s.LoadParser="load-parser",s.ResolveParser="resolve-parser",s.CacheParser="cache-parser",s.DetectionParser="detection-parser",s.MaskEffect="mask-effect",s.BlendMode="blend-mode",s.TextureSource="texture-source",s.Environment="environment",s.ShapeBuilder="shape-builder",s.Batcher="batcher",s))(T||{});const ds=s=>{if(typeof s=="function"||typeof s=="object"&&s.extension){if(!s.extension)throw new Error("Extension class must have an extension object");s={...typeof s.extension!="object"?{type:s.extension}:s.extension,ref:s};}if(typeof s=="object")s={...s};else throw new Error("Invalid extension type");return typeof s.type=="string"&&(s.type=[s.type]),s},Qe=(s,t)=>ds(s).priority??t,yt={_addHandlers:{},_removeHandlers:{},_queue:{},remove(...s){return s.map(ds).forEach(t=>{t.type.forEach(e=>this._removeHandlers[e]?.(t));}),this},add(...s){return s.map(ds).forEach(t=>{t.type.forEach(e=>{const i=this._addHandlers,n=this._queue;i[e]?i[e]?.(t):(n[e]=n[e]||[],n[e]?.push(t));});}),this},handle(s,t,e){const i=this._addHandlers,n=this._removeHandlers;if(i[s]||n[s])throw new Error(`Extension type ${s} already has a handler`);i[s]=t,n[s]=e;const r=this._queue;return r[s]&&(r[s]?.forEach(o=>t(o)),delete r[s]),this},handleByMap(s,t){return this.handle(s,e=>{e.name&&(t[e.name]=e.ref);},e=>{e.name&&delete t[e.name];})},handleByNamedList(s,t,e=-1){return this.handle(s,i=>{t.findIndex(r=>r.name===i.name)>=0||(t.push({name:i.name,value:i.ref}),t.sort((r,o)=>Qe(o.value,e)-Qe(r.value,e)));},i=>{const n=t.findIndex(r=>r.name===i.name);n!==-1&&t.splice(n,1);})},handleByList(s,t,e=-1){return this.handle(s,i=>{t.includes(i.ref)||(t.push(i.ref),t.sort((n,r)=>Qe(r,e)-Qe(n,e)));},i=>{const n=t.indexOf(i.ref);n!==-1&&t.splice(n,1);})}},Ta={extension:{type:T.Environment,name:"browser",priority:-1},test:()=>!0,load:async()=>{await import('./browserAll-B2muYeuY.js');}},Ra={extension:{type:T.Environment,name:"webworker",priority:0},test:()=>typeof self<"u"&&self.WorkerGlobalScope!==void 0,load:async()=>{await import('./webworkerAll-B9gZsnAT.js');}};class kt{constructor(t,e,i){this._x=e||0,this._y=i||0,this._observer=t;}clone(t){return new kt(t??this._observer,this._x,this._y)}set(t=0,e=t){return (this._x!==t||this._y!==e)&&(this._x=t,this._y=e,this._observer._onUpdate(this)),this}copyFrom(t){return (this._x!==t.x||this._y!==t.y)&&(this._x=t.x,this._y=t.y,this._observer._onUpdate(this)),this}copyTo(t){return t.set(this._x,this._y),t}equals(t){return t.x===this._x&&t.y===this._y}toString(){return `[pixi.js/math:ObservablePoint x=0 y=0 scope=${this._observer}]`}get x(){return this._x}set x(t){this._x!==t&&(this._x=t,this._observer._onUpdate(this));}get y(){return this._y}set y(t){this._y!==t&&(this._y=t,this._observer._onUpdate(this));}}var yr={exports:{}};(function(s){var t=Object.prototype.hasOwnProperty,e="~";function i(){}Object.create&&(i.prototype=Object.create(null),new i().__proto__||(e=!1));function n(l,c,h){this.fn=l,this.context=c,this.once=h||!1;}function r(l,c,h,d,_){if(typeof h!="function")throw new TypeError("The listener must be a function");var u=new n(h,d||l,_),f=e?e+c:c;return l._events[f]?l._events[f].fn?l._events[f]=[l._events[f],u]:l._events[f].push(u):(l._events[f]=u,l._eventsCount++),l}function o(l,c){--l._eventsCount===0?l._events=new i:delete l._events[c];}function a(){this._events=new i,this._eventsCount=0;}a.prototype.eventNames=function(){var c=[],h,d;if(this._eventsCount===0)return c;for(d in h=this._events)t.call(h,d)&&c.push(e?d.slice(1):d);return Object.getOwnPropertySymbols?c.concat(Object.getOwnPropertySymbols(h)):c},a.prototype.listeners=function(c){var h=e?e+c:c,d=this._events[h];if(!d)return [];if(d.fn)return [d.fn];for(var _=0,u=d.length,f=new Array(u);_<u;_++)f[_]=d[_].fn;return f},a.prototype.listenerCount=function(c){var h=e?e+c:c,d=this._events[h];return d?d.fn?1:d.length:0},a.prototype.emit=function(c,h,d,_,u,f){var p=e?e+c:c;if(!this._events[p])return !1;var A=this._events[p],m=arguments.length,g,y;if(A.fn){switch(A.once&&this.removeListener(c,A.fn,void 0,!0),m){case 1:return A.fn.call(A.context),!0;case 2:return A.fn.call(A.context,h),!0;case 3:return A.fn.call(A.context,h,d),!0;case 4:return A.fn.call(A.context,h,d,_),!0;case 5:return A.fn.call(A.context,h,d,_,u),!0;case 6:return A.fn.call(A.context,h,d,_,u,f),!0}for(y=1,g=new Array(m-1);y<m;y++)g[y-1]=arguments[y];A.fn.apply(A.context,g);}else {var x=A.length,v;for(y=0;y<x;y++)switch(A[y].once&&this.removeListener(c,A[y].fn,void 0,!0),m){case 1:A[y].fn.call(A[y].context);break;case 2:A[y].fn.call(A[y].context,h);break;case 3:A[y].fn.call(A[y].context,h,d);break;case 4:A[y].fn.call(A[y].context,h,d,_);break;default:if(!g)for(v=1,g=new Array(m-1);v<m;v++)g[v-1]=arguments[v];A[y].fn.apply(A[y].context,g);}}return !0},a.prototype.on=function(c,h,d){return r(this,c,h,d,!1)},a.prototype.once=function(c,h,d){return r(this,c,h,d,!0)},a.prototype.removeListener=function(c,h,d,_){var u=e?e+c:c;if(!this._events[u])return this;if(!h)return o(this,u),this;var f=this._events[u];if(f.fn)f.fn===h&&(!_||f.once)&&(!d||f.context===d)&&o(this,u);else {for(var p=0,A=[],m=f.length;p<m;p++)(f[p].fn!==h||_&&!f[p].once||d&&f[p].context!==d)&&A.push(f[p]);A.length?this._events[u]=A.length===1?A[0]:A:o(this,u);}return this},a.prototype.removeAllListeners=function(c){var h;return c?(h=e?e+c:c,this._events[h]&&o(this,h)):(this._events=new i,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=e,a.EventEmitter=a,s.exports=a;})(yr);var Ga=yr.exports;const Lt=Tr$1(Ga),Oa=Math.PI*2,Fa=180/Math.PI,La=Math.PI/180;class L{constructor(t=0,e=0){this.x=0,this.y=0,this.x=t,this.y=e;}clone(){return new L(this.x,this.y)}copyFrom(t){return this.set(t.x,t.y),this}copyTo(t){return t.set(this.x,this.y),t}equals(t){return t.x===this.x&&t.y===this.y}set(t=0,e=t){return this.x=t,this.y=e,this}toString(){return `[pixi.js/math:Point x=${this.x} y=${this.y}]`}static get shared(){return zi.x=0,zi.y=0,zi}}const zi=new L;class K{constructor(t=1,e=0,i=0,n=1,r=0,o=0){this.array=null,this.a=t,this.b=e,this.c=i,this.d=n,this.tx=r,this.ty=o;}fromArray(t){this.a=t[0],this.b=t[1],this.c=t[3],this.d=t[4],this.tx=t[2],this.ty=t[5];}set(t,e,i,n,r,o){return this.a=t,this.b=e,this.c=i,this.d=n,this.tx=r,this.ty=o,this}toArray(t,e){this.array||(this.array=new Float32Array(9));const i=e||this.array;return t?(i[0]=this.a,i[1]=this.b,i[2]=0,i[3]=this.c,i[4]=this.d,i[5]=0,i[6]=this.tx,i[7]=this.ty,i[8]=1):(i[0]=this.a,i[1]=this.c,i[2]=this.tx,i[3]=this.b,i[4]=this.d,i[5]=this.ty,i[6]=0,i[7]=0,i[8]=1),i}apply(t,e){e=e||new L;const i=t.x,n=t.y;return e.x=this.a*i+this.c*n+this.tx,e.y=this.b*i+this.d*n+this.ty,e}applyInverse(t,e){e=e||new L;const i=this.a,n=this.b,r=this.c,o=this.d,a=this.tx,l=this.ty,c=1/(i*o+r*-n),h=t.x,d=t.y;return e.x=o*c*h+-r*c*d+(l*r-a*o)*c,e.y=i*c*d+-n*c*h+(-l*i+a*n)*c,e}translate(t,e){return this.tx+=t,this.ty+=e,this}scale(t,e){return this.a*=t,this.d*=e,this.c*=t,this.b*=e,this.tx*=t,this.ty*=e,this}rotate(t){const e=Math.cos(t),i=Math.sin(t),n=this.a,r=this.c,o=this.tx;return this.a=n*e-this.b*i,this.b=n*i+this.b*e,this.c=r*e-this.d*i,this.d=r*i+this.d*e,this.tx=o*e-this.ty*i,this.ty=o*i+this.ty*e,this}append(t){const e=this.a,i=this.b,n=this.c,r=this.d;return this.a=t.a*e+t.b*n,this.b=t.a*i+t.b*r,this.c=t.c*e+t.d*n,this.d=t.c*i+t.d*r,this.tx=t.tx*e+t.ty*n+this.tx,this.ty=t.tx*i+t.ty*r+this.ty,this}appendFrom(t,e){const i=t.a,n=t.b,r=t.c,o=t.d,a=t.tx,l=t.ty,c=e.a,h=e.b,d=e.c,_=e.d;return this.a=i*c+n*d,this.b=i*h+n*_,this.c=r*c+o*d,this.d=r*h+o*_,this.tx=a*c+l*d+e.tx,this.ty=a*h+l*_+e.ty,this}setTransform(t,e,i,n,r,o,a,l,c){return this.a=Math.cos(a+c)*r,this.b=Math.sin(a+c)*r,this.c=-Math.sin(a-l)*o,this.d=Math.cos(a-l)*o,this.tx=t-(i*this.a+n*this.c),this.ty=e-(i*this.b+n*this.d),this}prepend(t){const e=this.tx;if(t.a!==1||t.b!==0||t.c!==0||t.d!==1){const i=this.a,n=this.c;this.a=i*t.a+this.b*t.c,this.b=i*t.b+this.b*t.d,this.c=n*t.a+this.d*t.c,this.d=n*t.b+this.d*t.d;}return this.tx=e*t.a+this.ty*t.c+t.tx,this.ty=e*t.b+this.ty*t.d+t.ty,this}decompose(t){const e=this.a,i=this.b,n=this.c,r=this.d,o=t.pivot,a=-Math.atan2(-n,r),l=Math.atan2(i,e),c=Math.abs(a+l);return c<1e-5||Math.abs(Oa-c)<1e-5?(t.rotation=l,t.skew.x=t.skew.y=0):(t.rotation=0,t.skew.x=a,t.skew.y=l),t.scale.x=Math.sqrt(e*e+i*i),t.scale.y=Math.sqrt(n*n+r*r),t.position.x=this.tx+(o.x*e+o.y*n),t.position.y=this.ty+(o.x*i+o.y*r),t}invert(){const t=this.a,e=this.b,i=this.c,n=this.d,r=this.tx,o=t*n-e*i;return this.a=n/o,this.b=-e/o,this.c=-i/o,this.d=t/o,this.tx=(i*this.ty-n*r)/o,this.ty=-(t*this.ty-e*r)/o,this}isIdentity(){return this.a===1&&this.b===0&&this.c===0&&this.d===1&&this.tx===0&&this.ty===0}identity(){return this.a=1,this.b=0,this.c=0,this.d=1,this.tx=0,this.ty=0,this}clone(){const t=new K;return t.a=this.a,t.b=this.b,t.c=this.c,t.d=this.d,t.tx=this.tx,t.ty=this.ty,t}copyTo(t){return t.a=this.a,t.b=this.b,t.c=this.c,t.d=this.d,t.tx=this.tx,t.ty=this.ty,t}copyFrom(t){return this.a=t.a,this.b=t.b,this.c=t.c,this.d=t.d,this.tx=t.tx,this.ty=t.ty,this}equals(t){return t.a===this.a&&t.b===this.b&&t.c===this.c&&t.d===this.d&&t.tx===this.tx&&t.ty===this.ty}toString(){return `[pixi.js:Matrix a=${this.a} b=${this.b} c=${this.c} d=${this.d} tx=${this.tx} ty=${this.ty}]`}static get IDENTITY(){return Ua.identity()}static get shared(){return Wa.identity()}}const Wa=new K,Ua=new K,se=[1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1,0,1],ne=[0,1,1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1],re=[0,-1,-1,-1,0,1,1,1,0,1,1,1,0,-1,-1,-1],oe=[1,1,0,-1,-1,-1,0,1,-1,-1,0,1,1,1,0,-1],us=[],xr=[],Ze=Math.sign;function Da(){for(let s=0;s<16;s++){const t=[];us.push(t);for(let e=0;e<16;e++){const i=Ze(se[s]*se[e]+re[s]*ne[e]),n=Ze(ne[s]*se[e]+oe[s]*ne[e]),r=Ze(se[s]*re[e]+re[s]*oe[e]),o=Ze(ne[s]*re[e]+oe[s]*oe[e]);for(let a=0;a<16;a++)if(se[a]===i&&ne[a]===n&&re[a]===r&&oe[a]===o){t.push(a);break}}}for(let s=0;s<16;s++){const t=new K;t.set(se[s],ne[s],re[s],oe[s],0,0),xr.push(t);}}Da();const Z={E:0,SE:1,S:2,SW:3,W:4,NW:5,N:6,NE:7,MIRROR_VERTICAL:8,MAIN_DIAGONAL:10,MIRROR_HORIZONTAL:12,REVERSE_DIAGONAL:14,uX:s=>se[s],uY:s=>ne[s],vX:s=>re[s],vY:s=>oe[s],inv:s=>s&8?s&15:-s&7,add:(s,t)=>us[s][t],sub:(s,t)=>us[s][Z.inv(t)],rotate180:s=>s^4,isVertical:s=>(s&3)===2,byDirection:(s,t)=>Math.abs(s)*2<=Math.abs(t)?t>=0?Z.S:Z.N:Math.abs(t)*2<=Math.abs(s)?s>0?Z.E:Z.W:t>0?s>0?Z.SE:Z.SW:s>0?Z.NE:Z.NW,matrixAppendRotationInv:(s,t,e=0,i=0)=>{const n=xr[Z.inv(t)];n.tx=e,n.ty=i,s.append(n);}},Je=[new L,new L,new L,new L];class Q{constructor(t=0,e=0,i=0,n=0){this.type="rectangle",this.x=Number(t),this.y=Number(e),this.width=Number(i),this.height=Number(n);}get left(){return this.x}get right(){return this.x+this.width}get top(){return this.y}get bottom(){return this.y+this.height}isEmpty(){return this.left===this.right||this.top===this.bottom}static get EMPTY(){return new Q(0,0,0,0)}clone(){return new Q(this.x,this.y,this.width,this.height)}copyFromBounds(t){return this.x=t.minX,this.y=t.minY,this.width=t.maxX-t.minX,this.height=t.maxY-t.minY,this}copyFrom(t){return this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height,this}copyTo(t){return t.copyFrom(this),t}contains(t,e){return this.width<=0||this.height<=0?!1:t>=this.x&&t<this.x+this.width&&e>=this.y&&e<this.y+this.height}strokeContains(t,e,i,n=.5){const{width:r,height:o}=this;if(r<=0||o<=0)return !1;const a=this.x,l=this.y,c=i*(1-n),h=i-c,d=a-c,_=a+r+c,u=l-c,f=l+o+c,p=a+h,A=a+r-h,m=l+h,g=l+o-h;return t>=d&&t<=_&&e>=u&&e<=f&&!(t>p&&t<A&&e>m&&e<g)}intersects(t,e){if(!e){const M=this.x<t.x?t.x:this.x;if((this.right>t.right?t.right:this.right)<=M)return !1;const w=this.y<t.y?t.y:this.y;return (this.bottom>t.bottom?t.bottom:this.bottom)>w}const i=this.left,n=this.right,r=this.top,o=this.bottom;if(n<=i||o<=r)return !1;const a=Je[0].set(t.left,t.top),l=Je[1].set(t.left,t.bottom),c=Je[2].set(t.right,t.top),h=Je[3].set(t.right,t.bottom);if(c.x<=a.x||l.y<=a.y)return !1;const d=Math.sign(e.a*e.d-e.b*e.c);if(d===0||(e.apply(a,a),e.apply(l,l),e.apply(c,c),e.apply(h,h),Math.max(a.x,l.x,c.x,h.x)<=i||Math.min(a.x,l.x,c.x,h.x)>=n||Math.max(a.y,l.y,c.y,h.y)<=r||Math.min(a.y,l.y,c.y,h.y)>=o))return !1;const _=d*(l.y-a.y),u=d*(a.x-l.x),f=_*i+u*r,p=_*n+u*r,A=_*i+u*o,m=_*n+u*o;if(Math.max(f,p,A,m)<=_*a.x+u*a.y||Math.min(f,p,A,m)>=_*h.x+u*h.y)return !1;const g=d*(a.y-c.y),y=d*(c.x-a.x),x=g*i+y*r,v=g*n+y*r,b=g*i+y*o,C=g*n+y*o;return !(Math.max(x,v,b,C)<=g*a.x+y*a.y||Math.min(x,v,b,C)>=g*h.x+y*h.y)}pad(t=0,e=t){return this.x-=t,this.y-=e,this.width+=t*2,this.height+=e*2,this}fit(t){const e=Math.max(this.x,t.x),i=Math.min(this.x+this.width,t.x+t.width),n=Math.max(this.y,t.y),r=Math.min(this.y+this.height,t.y+t.height);return this.x=e,this.width=Math.max(i-e,0),this.y=n,this.height=Math.max(r-n,0),this}ceil(t=1,e=.001){const i=Math.ceil((this.x+this.width-e)*t)/t,n=Math.ceil((this.y+this.height-e)*t)/t;return this.x=Math.floor((this.x+e)*t)/t,this.y=Math.floor((this.y+e)*t)/t,this.width=i-this.x,this.height=n-this.y,this}enlarge(t){const e=Math.min(this.x,t.x),i=Math.max(this.x+this.width,t.x+t.width),n=Math.min(this.y,t.y),r=Math.max(this.y+this.height,t.y+t.height);return this.x=e,this.width=i-e,this.y=n,this.height=r-n,this}getBounds(t){return t||(t=new Q),t.copyFrom(this),t}toString(){return `[pixi.js/math:Rectangle x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`}}const Pi={default:-1};function rt(s="default"){return Pi[s]===void 0&&(Pi[s]=-1),++Pi[s]}const cn={},N="8.0.0",Ka="8.3.4";function D(s,t,e=3){if(cn[t])return;let i=new Error().stack;typeof i>"u"?console.warn("PixiJS Deprecation Warning: ",`${t}
Deprecated since v${s}`):(i=i.split(`
`).splice(e).join(`
`),console.groupCollapsed?(console.groupCollapsed("%cPixiJS Deprecation Warning: %c%s","color:#614108;background:#fffbe6","font-weight:normal;color:#614108;background:#fffbe6",`${t}
Deprecated since v${s}`),console.warn(i),console.groupEnd()):(console.warn("PixiJS Deprecation Warning: ",`${t}
Deprecated since v${s}`),console.warn(i))),cn[t]=!0;}const br=()=>{};function gi(s){return s+=s===0?1:0,--s,s|=s>>>1,s|=s>>>2,s|=s>>>4,s|=s>>>8,s|=s>>>16,s+1}function hn(s){return !(s&s-1)&&!!s}function Na(s){const t={};for(const e in s)s[e]!==void 0&&(t[e]=s[e]);return t}const dn=Object.create(null);function Ha(s){const t=dn[s];return t===void 0&&(dn[s]=rt("resource")),t}const Cr=class vr extends Lt{constructor(t={}){super(),this._resourceType="textureSampler",this._touched=0,this._maxAnisotropy=1,this.destroyed=!1,t={...vr.defaultOptions,...t},this.addressMode=t.addressMode,this.addressModeU=t.addressModeU??this.addressModeU,this.addressModeV=t.addressModeV??this.addressModeV,this.addressModeW=t.addressModeW??this.addressModeW,this.scaleMode=t.scaleMode,this.magFilter=t.magFilter??this.magFilter,this.minFilter=t.minFilter??this.minFilter,this.mipmapFilter=t.mipmapFilter??this.mipmapFilter,this.lodMinClamp=t.lodMinClamp,this.lodMaxClamp=t.lodMaxClamp,this.compare=t.compare,this.maxAnisotropy=t.maxAnisotropy??1;}set addressMode(t){this.addressModeU=t,this.addressModeV=t,this.addressModeW=t;}get addressMode(){return this.addressModeU}set wrapMode(t){D(N,"TextureStyle.wrapMode is now TextureStyle.addressMode"),this.addressMode=t;}get wrapMode(){return this.addressMode}set scaleMode(t){this.magFilter=t,this.minFilter=t,this.mipmapFilter=t;}get scaleMode(){return this.magFilter}set maxAnisotropy(t){this._maxAnisotropy=Math.min(t,16),this._maxAnisotropy>1&&(this.scaleMode="linear");}get maxAnisotropy(){return this._maxAnisotropy}get _resourceId(){return this._sharedResourceId||this._generateResourceId()}update(){this.emit("change",this),this._sharedResourceId=null;}_generateResourceId(){const t=`${this.addressModeU}-${this.addressModeV}-${this.addressModeW}-${this.magFilter}-${this.minFilter}-${this.mipmapFilter}-${this.lodMinClamp}-${this.lodMaxClamp}-${this.compare}-${this._maxAnisotropy}`;return this._sharedResourceId=Ha(t),this._resourceId}destroy(){this.destroyed=!0,this.emit("destroy",this),this.emit("change",this),this.removeAllListeners();}};Cr.defaultOptions={addressMode:"clamp-to-edge",scaleMode:"linear"};let ja=Cr;const wr=class kr extends Lt{constructor(t={}){super(),this.options=t,this.uid=rt("textureSource"),this._resourceType="textureSource",this._resourceId=rt("resource"),this.uploadMethodId="unknown",this._resolution=1,this.pixelWidth=1,this.pixelHeight=1,this.width=1,this.height=1,this.sampleCount=1,this.mipLevelCount=1,this.autoGenerateMipmaps=!1,this.format="rgba8unorm",this.dimension="2d",this.antialias=!1,this._touched=0,this._batchTick=-1,this._textureBindLocation=-1,t={...kr.defaultOptions,...t},this.label=t.label??"",this.resource=t.resource,this.autoGarbageCollect=t.autoGarbageCollect,this._resolution=t.resolution,t.width?this.pixelWidth=t.width*this._resolution:this.pixelWidth=this.resource?this.resourceWidth??1:1,t.height?this.pixelHeight=t.height*this._resolution:this.pixelHeight=this.resource?this.resourceHeight??1:1,this.width=this.pixelWidth/this._resolution,this.height=this.pixelHeight/this._resolution,this.format=t.format,this.dimension=t.dimensions,this.mipLevelCount=t.mipLevelCount,this.autoGenerateMipmaps=t.autoGenerateMipmaps,this.sampleCount=t.sampleCount,this.antialias=t.antialias,this.alphaMode=t.alphaMode,this.style=new ja(Na(t)),this.destroyed=!1,this._refreshPOT();}get source(){return this}get style(){return this._style}set style(t){this.style!==t&&(this._style?.off("change",this._onStyleChange,this),this._style=t,this._style?.on("change",this._onStyleChange,this),this._onStyleChange());}get addressMode(){return this._style.addressMode}set addressMode(t){this._style.addressMode=t;}get repeatMode(){return this._style.addressMode}set repeatMode(t){this._style.addressMode=t;}get magFilter(){return this._style.magFilter}set magFilter(t){this._style.magFilter=t;}get minFilter(){return this._style.minFilter}set minFilter(t){this._style.minFilter=t;}get mipmapFilter(){return this._style.mipmapFilter}set mipmapFilter(t){this._style.mipmapFilter=t;}get lodMinClamp(){return this._style.lodMinClamp}set lodMinClamp(t){this._style.lodMinClamp=t;}get lodMaxClamp(){return this._style.lodMaxClamp}set lodMaxClamp(t){this._style.lodMaxClamp=t;}_onStyleChange(){this.emit("styleChange",this);}update(){if(this.resource){const t=this._resolution;if(this.resize(this.resourceWidth/t,this.resourceHeight/t))return}this.emit("update",this);}destroy(){this.destroyed=!0,this.emit("destroy",this),this.emit("change",this),this._style&&(this._style.destroy(),this._style=null),this.uploadMethodId=null,this.resource=null,this.removeAllListeners();}unload(){this._resourceId=rt("resource"),this.emit("change",this),this.emit("unload",this);}get resourceWidth(){const{resource:t}=this;return t.naturalWidth||t.videoWidth||t.displayWidth||t.width}get resourceHeight(){const{resource:t}=this;return t.naturalHeight||t.videoHeight||t.displayHeight||t.height}get resolution(){return this._resolution}set resolution(t){this._resolution!==t&&(this._resolution=t,this.width=this.pixelWidth/t,this.height=this.pixelHeight/t);}resize(t,e,i){i||(i=this._resolution),t||(t=this.width),e||(e=this.height);const n=Math.round(t*i),r=Math.round(e*i);return this.width=n/i,this.height=r/i,this._resolution=i,this.pixelWidth===n&&this.pixelHeight===r?!1:(this._refreshPOT(),this.pixelWidth=n,this.pixelHeight=r,this.emit("resize",this),this._resourceId=rt("resource"),this.emit("change",this),!0)}updateMipmaps(){this.autoGenerateMipmaps&&this.mipLevelCount>1&&this.emit("updateMipmaps",this);}set wrapMode(t){this._style.wrapMode=t;}get wrapMode(){return this._style.wrapMode}set scaleMode(t){this._style.scaleMode=t;}get scaleMode(){return this._style.scaleMode}_refreshPOT(){this.isPowerOfTwo=hn(this.pixelWidth)&&hn(this.pixelHeight);}static test(t){throw new Error("Unimplemented")}};wr.defaultOptions={resolution:1,format:"bgra8unorm",alphaMode:"premultiply-alpha-on-upload",dimensions:"2d",mipLevelCount:1,autoGenerateMipmaps:!1,sampleCount:1,antialias:!1,autoGarbageCollect:!1};let Wt=wr;class Ns extends Wt{constructor(t){const e=t.resource||new Float32Array(t.width*t.height*4);let i=t.format;i||(e instanceof Float32Array?i="rgba32float":e instanceof Int32Array||e instanceof Uint32Array?i="rgba32uint":e instanceof Int16Array||e instanceof Uint16Array?i="rgba16uint":(i="bgra8unorm")),super({...t,resource:e,format:i}),this.uploadMethodId="buffer";}static test(t){return t instanceof Int8Array||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array}}Ns.extension=T.TextureSource;const un=new K;class Ya{constructor(t,e){this.mapCoord=new K,this.uClampFrame=new Float32Array(4),this.uClampOffset=new Float32Array(2),this._textureID=-1,this._updateID=0,this.clampOffset=0,typeof e>"u"?this.clampMargin=t.width<10?0:.5:this.clampMargin=e,this.isSimple=!1,this.texture=t;}get texture(){return this._texture}set texture(t){this.texture!==t&&(this._texture?.removeListener("update",this.update,this),this._texture=t,this._texture.addListener("update",this.update,this),this.update());}multiplyUvs(t,e){e===void 0&&(e=t);const i=this.mapCoord;for(let n=0;n<t.length;n+=2){const r=t[n],o=t[n+1];e[n]=r*i.a+o*i.c+i.tx,e[n+1]=r*i.b+o*i.d+i.ty;}return e}update(){const t=this._texture;this._updateID++;const e=t.uvs;this.mapCoord.set(e.x1-e.x0,e.y1-e.y0,e.x3-e.x0,e.y3-e.y0,e.x0,e.y0);const i=t.orig,n=t.trim;n&&(un.set(i.width/n.width,0,0,i.height/n.height,-n.x/n.width,-n.y/n.height),this.mapCoord.append(un));const r=t.source,o=this.uClampFrame,a=this.clampMargin/r._resolution,l=this.clampOffset/r._resolution;return o[0]=(t.frame.x+a+l)/r.width,o[1]=(t.frame.y+a+l)/r.height,o[2]=(t.frame.x+t.frame.width-a+l)/r.width,o[3]=(t.frame.y+t.frame.height-a+l)/r.height,this.uClampOffset[0]=this.clampOffset/r.pixelWidth,this.uClampOffset[1]=this.clampOffset/r.pixelHeight,this.isSimple=t.frame.width===r.width&&t.frame.height===r.height&&t.rotate===0,!0}}class W extends Lt{constructor({source:t,label:e,frame:i,orig:n,trim:r,defaultAnchor:o,defaultBorders:a,rotate:l,dynamic:c}={}){if(super(),this.uid=rt("texture"),this.uvs={x0:0,y0:0,x1:0,y1:0,x2:0,y2:0,x3:0,y3:0},this.frame=new Q,this.noFrame=!1,this.dynamic=!1,this.isTexture=!0,this.label=e,this.source=t?.source??new Wt,this.noFrame=!i,i)this.frame.copyFrom(i);else {const{width:h,height:d}=this._source;this.frame.width=h,this.frame.height=d;}this.orig=n||this.frame,this.trim=r,this.rotate=l??0,this.defaultAnchor=o,this.defaultBorders=a,this.destroyed=!1,this.dynamic=c||!1,this.updateUvs();}set source(t){this._source&&this._source.off("resize",this.update,this),this._source=t,t.on("resize",this.update,this),this.emit("update",this);}get source(){return this._source}get textureMatrix(){return this._textureMatrix||(this._textureMatrix=new Ya(this)),this._textureMatrix}get width(){return this.orig.width}get height(){return this.orig.height}updateUvs(){const{uvs:t,frame:e}=this,{width:i,height:n}=this._source,r=e.x/i,o=e.y/n,a=e.width/i,l=e.height/n;let c=this.rotate;if(c){const h=a/2,d=l/2,_=r+h,u=o+d;c=Z.add(c,Z.NW),t.x0=_+h*Z.uX(c),t.y0=u+d*Z.uY(c),c=Z.add(c,2),t.x1=_+h*Z.uX(c),t.y1=u+d*Z.uY(c),c=Z.add(c,2),t.x2=_+h*Z.uX(c),t.y2=u+d*Z.uY(c),c=Z.add(c,2),t.x3=_+h*Z.uX(c),t.y3=u+d*Z.uY(c);}else t.x0=r,t.y0=o,t.x1=r+a,t.y1=o,t.x2=r+a,t.y2=o+l,t.x3=r,t.y3=o+l;}destroy(t=!1){this._source&&t&&(this._source.destroy(),this._source=null),this._textureMatrix=null,this.destroyed=!0,this.emit("destroy",this),this.removeAllListeners();}update(){this.noFrame&&(this.frame.width=this._source.width,this.frame.height=this._source.height),this.updateUvs(),this.emit("update",this);}get baseTexture(){return D(N,"Texture.baseTexture is now Texture.source"),this._source}}W.EMPTY=new W({label:"EMPTY",source:new Wt({label:"EMPTY"})});W.EMPTY.destroy=br;W.WHITE=new W({source:new Ns({resource:new Uint8Array([255,255,255,255]),width:1,height:1,alphaMode:"premultiply-alpha-on-upload",label:"WHITE"}),label:"WHITE"});W.WHITE.destroy=br;function Xa(s,t,e){const{width:i,height:n}=e.orig,r=e.trim;if(r){const o=r.width,a=r.height;s.minX=r.x-t._x*i,s.maxX=s.minX+o,s.minY=r.y-t._y*n,s.maxY=s.minY+a;}else s.minX=-t._x*i,s.maxX=s.minX+i,s.minY=-t._y*n,s.maxY=s.minY+n;}const _n=new K;class Ft{constructor(t=1/0,e=1/0,i=-1/0,n=-1/0){this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0,this.matrix=_n,this.minX=t,this.minY=e,this.maxX=i,this.maxY=n;}isEmpty(){return this.minX>this.maxX||this.minY>this.maxY}get rectangle(){this._rectangle||(this._rectangle=new Q);const t=this._rectangle;return this.minX>this.maxX||this.minY>this.maxY?(t.x=0,t.y=0,t.width=0,t.height=0):t.copyFromBounds(this),t}clear(){return this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0,this.matrix=_n,this}set(t,e,i,n){this.minX=t,this.minY=e,this.maxX=i,this.maxY=n;}addFrame(t,e,i,n,r){r||(r=this.matrix);const o=r.a,a=r.b,l=r.c,c=r.d,h=r.tx,d=r.ty;let _=this.minX,u=this.minY,f=this.maxX,p=this.maxY,A=o*t+l*e+h,m=a*t+c*e+d;A<_&&(_=A),m<u&&(u=m),A>f&&(f=A),m>p&&(p=m),A=o*i+l*e+h,m=a*i+c*e+d,A<_&&(_=A),m<u&&(u=m),A>f&&(f=A),m>p&&(p=m),A=o*t+l*n+h,m=a*t+c*n+d,A<_&&(_=A),m<u&&(u=m),A>f&&(f=A),m>p&&(p=m),A=o*i+l*n+h,m=a*i+c*n+d,A<_&&(_=A),m<u&&(u=m),A>f&&(f=A),m>p&&(p=m),this.minX=_,this.minY=u,this.maxX=f,this.maxY=p;}addRect(t,e){this.addFrame(t.x,t.y,t.x+t.width,t.y+t.height,e);}addBounds(t,e){this.addFrame(t.minX,t.minY,t.maxX,t.maxY,e);}addBoundsMask(t){this.minX=this.minX>t.minX?this.minX:t.minX,this.minY=this.minY>t.minY?this.minY:t.minY,this.maxX=this.maxX<t.maxX?this.maxX:t.maxX,this.maxY=this.maxY<t.maxY?this.maxY:t.maxY;}applyMatrix(t){const e=this.minX,i=this.minY,n=this.maxX,r=this.maxY,{a:o,b:a,c:l,d:c,tx:h,ty:d}=t;let _=o*e+l*i+h,u=a*e+c*i+d;this.minX=_,this.minY=u,this.maxX=_,this.maxY=u,_=o*n+l*i+h,u=a*n+c*i+d,this.minX=_<this.minX?_:this.minX,this.minY=u<this.minY?u:this.minY,this.maxX=_>this.maxX?_:this.maxX,this.maxY=u>this.maxY?u:this.maxY,_=o*e+l*r+h,u=a*e+c*r+d,this.minX=_<this.minX?_:this.minX,this.minY=u<this.minY?u:this.minY,this.maxX=_>this.maxX?_:this.maxX,this.maxY=u>this.maxY?u:this.maxY,_=o*n+l*r+h,u=a*n+c*r+d,this.minX=_<this.minX?_:this.minX,this.minY=u<this.minY?u:this.minY,this.maxX=_>this.maxX?_:this.maxX,this.maxY=u>this.maxY?u:this.maxY;}fit(t){return this.minX<t.left&&(this.minX=t.left),this.maxX>t.right&&(this.maxX=t.right),this.minY<t.top&&(this.minY=t.top),this.maxY>t.bottom&&(this.maxY=t.bottom),this}fitBounds(t,e,i,n){return this.minX<t&&(this.minX=t),this.maxX>e&&(this.maxX=e),this.minY<i&&(this.minY=i),this.maxY>n&&(this.maxY=n),this}pad(t,e=t){return this.minX-=t,this.maxX+=t,this.minY-=e,this.maxY+=e,this}ceil(){return this.minX=Math.floor(this.minX),this.minY=Math.floor(this.minY),this.maxX=Math.ceil(this.maxX),this.maxY=Math.ceil(this.maxY),this}clone(){return new Ft(this.minX,this.minY,this.maxX,this.maxY)}scale(t,e=t){return this.minX*=t,this.minY*=e,this.maxX*=t,this.maxY*=e,this}get x(){return this.minX}set x(t){const e=this.maxX-this.minX;this.minX=t,this.maxX=t+e;}get y(){return this.minY}set y(t){const e=this.maxY-this.minY;this.minY=t,this.maxY=t+e;}get width(){return this.maxX-this.minX}set width(t){this.maxX=this.minX+t;}get height(){return this.maxY-this.minY}set height(t){this.maxY=this.minY+t;}get left(){return this.minX}get right(){return this.maxX}get top(){return this.minY}get bottom(){return this.maxY}get isPositive(){return this.maxX-this.minX>0&&this.maxY-this.minY>0}get isValid(){return this.minX+this.minY!==1/0}addVertexData(t,e,i,n){let r=this.minX,o=this.minY,a=this.maxX,l=this.maxY;n||(n=this.matrix);const c=n.a,h=n.b,d=n.c,_=n.d,u=n.tx,f=n.ty;for(let p=e;p<i;p+=2){const A=t[p],m=t[p+1],g=c*A+d*m+u,y=h*A+_*m+f;r=g<r?g:r,o=y<o?y:o,a=g>a?g:a,l=y>l?y:l;}this.minX=r,this.minY=o,this.maxX=a,this.maxY=l;}containsPoint(t,e){return this.minX<=t&&this.minY<=e&&this.maxX>=t&&this.maxY>=e}toString(){return `[pixi.js:Bounds minX=${this.minX} minY=${this.minY} maxX=${this.maxX} maxY=${this.maxY} width=${this.width} height=${this.height}]`}copyFrom(t){return this.minX=t.minX,this.minY=t.minY,this.maxX=t.maxX,this.maxY=t.maxY,this}}var Va={grad:.9,turn:360,rad:360/(2*Math.PI)},jt=function(s){return typeof s=="string"?s.length>0:typeof s=="number"},dt=function(s,t,e){return t===void 0&&(t=0),e===void 0&&(e=Math.pow(10,t)),Math.round(e*s)/e+0},Tt=function(s,t,e){return t===void 0&&(t=0),e===void 0&&(e=1),s>e?e:s>t?s:t},Ir=function(s){return (s=isFinite(s)?s%360:0)>0?s:s+360},fn=function(s){return {r:Tt(s.r,0,255),g:Tt(s.g,0,255),b:Tt(s.b,0,255),a:Tt(s.a)}},Ti=function(s){return {r:dt(s.r),g:dt(s.g),b:dt(s.b),a:dt(s.a,3)}},qa=/^#([0-9a-f]{3,8})$/i,$e=function(s){var t=s.toString(16);return t.length<2?"0"+t:t},Er=function(s){var t=s.r,e=s.g,i=s.b,n=s.a,r=Math.max(t,e,i),o=r-Math.min(t,e,i),a=o?r===t?(e-i)/o:r===e?2+(i-t)/o:4+(t-e)/o:0;return {h:60*(a<0?a+6:a),s:r?o/r*100:0,v:r/255*100,a:n}},Mr=function(s){var t=s.h,e=s.s,i=s.v,n=s.a;t=t/360*6,e/=100,i/=100;var r=Math.floor(t),o=i*(1-e),a=i*(1-(t-r)*e),l=i*(1-(1-t+r)*e),c=r%6;return {r:255*[i,a,o,o,l,i][c],g:255*[l,i,i,a,o,o][c],b:255*[o,o,l,i,i,a][c],a:n}},An=function(s){return {h:Ir(s.h),s:Tt(s.s,0,100),l:Tt(s.l,0,100),a:Tt(s.a)}},pn=function(s){return {h:dt(s.h),s:dt(s.s),l:dt(s.l),a:dt(s.a,3)}},mn=function(s){return Mr((e=(t=s).s,{h:t.h,s:(e*=((i=t.l)<50?i:100-i)/100)>0?2*e/(i+e)*100:0,v:i+e,a:t.a}));var t,e,i;},We=function(s){return {h:(t=Er(s)).h,s:(n=(200-(e=t.s))*(i=t.v)/100)>0&&n<200?e*i/100/(n<=100?n:200-n)*100:0,l:n/2,a:t.a};var t,e,i,n;},Qa=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s*,\s*([+-]?\d*\.?\d+)%\s*,\s*([+-]?\d*\.?\d+)%\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,Za=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s+([+-]?\d*\.?\d+)%\s+([+-]?\d*\.?\d+)%\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,Ja=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,$a=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,_s={string:[[function(s){var t=qa.exec(s);return t?(s=t[1]).length<=4?{r:parseInt(s[0]+s[0],16),g:parseInt(s[1]+s[1],16),b:parseInt(s[2]+s[2],16),a:s.length===4?dt(parseInt(s[3]+s[3],16)/255,2):1}:s.length===6||s.length===8?{r:parseInt(s.substr(0,2),16),g:parseInt(s.substr(2,2),16),b:parseInt(s.substr(4,2),16),a:s.length===8?dt(parseInt(s.substr(6,2),16)/255,2):1}:null:null},"hex"],[function(s){var t=Ja.exec(s)||$a.exec(s);return t?t[2]!==t[4]||t[4]!==t[6]?null:fn({r:Number(t[1])/(t[2]?100/255:1),g:Number(t[3])/(t[4]?100/255:1),b:Number(t[5])/(t[6]?100/255:1),a:t[7]===void 0?1:Number(t[7])/(t[8]?100:1)}):null},"rgb"],[function(s){var t=Qa.exec(s)||Za.exec(s);if(!t)return null;var e,i,n=An({h:(e=t[1],i=t[2],i===void 0&&(i="deg"),Number(e)*(Va[i]||1)),s:Number(t[3]),l:Number(t[4]),a:t[5]===void 0?1:Number(t[5])/(t[6]?100:1)});return mn(n)},"hsl"]],object:[[function(s){var t=s.r,e=s.g,i=s.b,n=s.a,r=n===void 0?1:n;return jt(t)&&jt(e)&&jt(i)?fn({r:Number(t),g:Number(e),b:Number(i),a:Number(r)}):null},"rgb"],[function(s){var t=s.h,e=s.s,i=s.l,n=s.a,r=n===void 0?1:n;if(!jt(t)||!jt(e)||!jt(i))return null;var o=An({h:Number(t),s:Number(e),l:Number(i),a:Number(r)});return mn(o)},"hsl"],[function(s){var t=s.h,e=s.s,i=s.v,n=s.a,r=n===void 0?1:n;if(!jt(t)||!jt(e)||!jt(i))return null;var o=function(a){return {h:Ir(a.h),s:Tt(a.s,0,100),v:Tt(a.v,0,100),a:Tt(a.a)}}({h:Number(t),s:Number(e),v:Number(i),a:Number(r)});return Mr(o)},"hsv"]]},gn=function(s,t){for(var e=0;e<t.length;e++){var i=t[e][0](s);if(i)return [i,t[e][1]]}return [null,void 0]},tl=function(s){return typeof s=="string"?gn(s.trim(),_s.string):typeof s=="object"&&s!==null?gn(s,_s.object):[null,void 0]},Ri=function(s,t){var e=We(s);return {h:e.h,s:Tt(e.s+100*t,0,100),l:e.l,a:e.a}},Gi=function(s){return (299*s.r+587*s.g+114*s.b)/1e3/255},yn=function(s,t){var e=We(s);return {h:e.h,s:e.s,l:Tt(e.l+100*t,0,100),a:e.a}},fs=function(){function s(t){this.parsed=tl(t)[0],this.rgba=this.parsed||{r:0,g:0,b:0,a:1};}return s.prototype.isValid=function(){return this.parsed!==null},s.prototype.brightness=function(){return dt(Gi(this.rgba),2)},s.prototype.isDark=function(){return Gi(this.rgba)<.5},s.prototype.isLight=function(){return Gi(this.rgba)>=.5},s.prototype.toHex=function(){return t=Ti(this.rgba),e=t.r,i=t.g,n=t.b,o=(r=t.a)<1?$e(dt(255*r)):"","#"+$e(e)+$e(i)+$e(n)+o;var t,e,i,n,r,o;},s.prototype.toRgb=function(){return Ti(this.rgba)},s.prototype.toRgbString=function(){return t=Ti(this.rgba),e=t.r,i=t.g,n=t.b,(r=t.a)<1?"rgba("+e+", "+i+", "+n+", "+r+")":"rgb("+e+", "+i+", "+n+")";var t,e,i,n,r;},s.prototype.toHsl=function(){return pn(We(this.rgba))},s.prototype.toHslString=function(){return t=pn(We(this.rgba)),e=t.h,i=t.s,n=t.l,(r=t.a)<1?"hsla("+e+", "+i+"%, "+n+"%, "+r+")":"hsl("+e+", "+i+"%, "+n+"%)";var t,e,i,n,r;},s.prototype.toHsv=function(){return t=Er(this.rgba),{h:dt(t.h),s:dt(t.s),v:dt(t.v),a:dt(t.a,3)};var t;},s.prototype.invert=function(){return Nt({r:255-(t=this.rgba).r,g:255-t.g,b:255-t.b,a:t.a});var t;},s.prototype.saturate=function(t){return t===void 0&&(t=.1),Nt(Ri(this.rgba,t))},s.prototype.desaturate=function(t){return t===void 0&&(t=.1),Nt(Ri(this.rgba,-t))},s.prototype.grayscale=function(){return Nt(Ri(this.rgba,-1))},s.prototype.lighten=function(t){return t===void 0&&(t=.1),Nt(yn(this.rgba,t))},s.prototype.darken=function(t){return t===void 0&&(t=.1),Nt(yn(this.rgba,-t))},s.prototype.rotate=function(t){return t===void 0&&(t=15),this.hue(this.hue()+t)},s.prototype.alpha=function(t){return typeof t=="number"?Nt({r:(e=this.rgba).r,g:e.g,b:e.b,a:t}):dt(this.rgba.a,3);var e;},s.prototype.hue=function(t){var e=We(this.rgba);return typeof t=="number"?Nt({h:t,s:e.s,l:e.l,a:e.a}):dt(e.h)},s.prototype.isEqual=function(t){return this.toHex()===Nt(t).toHex()},s}(),Nt=function(s){return s instanceof fs?s:new fs(s)},xn=[],el=function(s){s.forEach(function(t){xn.indexOf(t)<0&&(t(fs,_s),xn.push(t));});};function il(s,t){var e={white:"#ffffff",bisque:"#ffe4c4",blue:"#0000ff",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",antiquewhite:"#faebd7",aqua:"#00ffff",azure:"#f0ffff",whitesmoke:"#f5f5f5",papayawhip:"#ffefd5",plum:"#dda0dd",blanchedalmond:"#ffebcd",black:"#000000",gold:"#ffd700",goldenrod:"#daa520",gainsboro:"#dcdcdc",cornsilk:"#fff8dc",cornflowerblue:"#6495ed",burlywood:"#deb887",aquamarine:"#7fffd4",beige:"#f5f5dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkkhaki:"#bdb76b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",peachpuff:"#ffdab9",darkmagenta:"#8b008b",darkred:"#8b0000",darkorchid:"#9932cc",darkorange:"#ff8c00",darkslateblue:"#483d8b",gray:"#808080",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",deeppink:"#ff1493",deepskyblue:"#00bfff",wheat:"#f5deb3",firebrick:"#b22222",floralwhite:"#fffaf0",ghostwhite:"#f8f8ff",darkviolet:"#9400d3",magenta:"#ff00ff",green:"#008000",dodgerblue:"#1e90ff",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",blueviolet:"#8a2be2",forestgreen:"#228b22",lawngreen:"#7cfc00",indianred:"#cd5c5c",indigo:"#4b0082",fuchsia:"#ff00ff",brown:"#a52a2a",maroon:"#800000",mediumblue:"#0000cd",lightcoral:"#f08080",darkturquoise:"#00ced1",lightcyan:"#e0ffff",ivory:"#fffff0",lightyellow:"#ffffe0",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",linen:"#faf0e6",mediumaquamarine:"#66cdaa",lemonchiffon:"#fffacd",lime:"#00ff00",khaki:"#f0e68c",mediumseagreen:"#3cb371",limegreen:"#32cd32",mediumspringgreen:"#00fa9a",lightskyblue:"#87cefa",lightblue:"#add8e6",midnightblue:"#191970",lightpink:"#ffb6c1",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",mintcream:"#f5fffa",lightslategray:"#778899",lightslategrey:"#778899",navajowhite:"#ffdead",navy:"#000080",mediumvioletred:"#c71585",powderblue:"#b0e0e6",palegoldenrod:"#eee8aa",oldlace:"#fdf5e6",paleturquoise:"#afeeee",mediumturquoise:"#48d1cc",mediumorchid:"#ba55d3",rebeccapurple:"#663399",lightsteelblue:"#b0c4de",mediumslateblue:"#7b68ee",thistle:"#d8bfd8",tan:"#d2b48c",orchid:"#da70d6",mediumpurple:"#9370db",purple:"#800080",pink:"#ffc0cb",skyblue:"#87ceeb",springgreen:"#00ff7f",palegreen:"#98fb98",red:"#ff0000",yellow:"#ffff00",slateblue:"#6a5acd",lavenderblush:"#fff0f5",peru:"#cd853f",palevioletred:"#db7093",violet:"#ee82ee",teal:"#008080",slategray:"#708090",slategrey:"#708090",aliceblue:"#f0f8ff",darkseagreen:"#8fbc8f",darkolivegreen:"#556b2f",greenyellow:"#adff2f",seagreen:"#2e8b57",seashell:"#fff5ee",tomato:"#ff6347",silver:"#c0c0c0",sienna:"#a0522d",lavender:"#e6e6fa",lightgreen:"#90ee90",orange:"#ffa500",orangered:"#ff4500",steelblue:"#4682b4",royalblue:"#4169e1",turquoise:"#40e0d0",yellowgreen:"#9acd32",salmon:"#fa8072",saddlebrown:"#8b4513",sandybrown:"#f4a460",rosybrown:"#bc8f8f",darksalmon:"#e9967a",lightgoldenrodyellow:"#fafad2",snow:"#fffafa",lightgrey:"#d3d3d3",lightgray:"#d3d3d3",dimgray:"#696969",dimgrey:"#696969",olivedrab:"#6b8e23",olive:"#808000"},i={};for(var n in e)i[e[n]]=n;var r={};s.prototype.toName=function(o){if(!(this.rgba.a||this.rgba.r||this.rgba.g||this.rgba.b))return "transparent";var a,l,c=i[this.toHex()];if(c)return c;if(o?.closest){var h=this.toRgb(),d=1/0,_="black";if(!r.length)for(var u in e)r[u]=new s(e[u]).toRgb();for(var f in e){var p=(a=h,l=r[f],Math.pow(a.r-l.r,2)+Math.pow(a.g-l.g,2)+Math.pow(a.b-l.b,2));p<d&&(d=p,_=f);}return _}},t.string.push([function(o){var a=o.toLowerCase(),l=a==="transparent"?"#0000":e[a];return l?new s(l).toRgb():null},"name"]);}el([il]);const be=class Oe{constructor(t=16777215){this._value=null,this._components=new Float32Array(4),this._components.fill(1),this._int=16777215,this.value=t;}get red(){return this._components[0]}get green(){return this._components[1]}get blue(){return this._components[2]}get alpha(){return this._components[3]}setValue(t){return this.value=t,this}set value(t){if(t instanceof Oe)this._value=this._cloneSource(t._value),this._int=t._int,this._components.set(t._components);else {if(t===null)throw new Error("Cannot set Color#value to null");(this._value===null||!this._isSourceEqual(this._value,t))&&(this._value=this._cloneSource(t),this._normalize(this._value));}}get value(){return this._value}_cloneSource(t){return typeof t=="string"||typeof t=="number"||t instanceof Number||t===null?t:Array.isArray(t)||ArrayBuffer.isView(t)?t.slice(0):typeof t=="object"&&t!==null?{...t}:t}_isSourceEqual(t,e){const i=typeof t;if(i!==typeof e)return !1;if(i==="number"||i==="string"||t instanceof Number)return t===e;if(Array.isArray(t)&&Array.isArray(e)||ArrayBuffer.isView(t)&&ArrayBuffer.isView(e))return t.length!==e.length?!1:t.every((r,o)=>r===e[o]);if(t!==null&&e!==null){const r=Object.keys(t),o=Object.keys(e);return r.length!==o.length?!1:r.every(a=>t[a]===e[a])}return t===e}toRgba(){const[t,e,i,n]=this._components;return {r:t,g:e,b:i,a:n}}toRgb(){const[t,e,i]=this._components;return {r:t,g:e,b:i}}toRgbaString(){const[t,e,i]=this.toUint8RgbArray();return `rgba(${t},${e},${i},${this.alpha})`}toUint8RgbArray(t){const[e,i,n]=this._components;return this._arrayRgb||(this._arrayRgb=[]),t||(t=this._arrayRgb),t[0]=Math.round(e*255),t[1]=Math.round(i*255),t[2]=Math.round(n*255),t}toArray(t){this._arrayRgba||(this._arrayRgba=[]),t||(t=this._arrayRgba);const[e,i,n,r]=this._components;return t[0]=e,t[1]=i,t[2]=n,t[3]=r,t}toRgbArray(t){this._arrayRgb||(this._arrayRgb=[]),t||(t=this._arrayRgb);const[e,i,n]=this._components;return t[0]=e,t[1]=i,t[2]=n,t}toNumber(){return this._int}toBgrNumber(){const[t,e,i]=this.toUint8RgbArray();return (i<<16)+(e<<8)+t}toLittleEndianNumber(){const t=this._int;return (t>>16)+(t&65280)+((t&255)<<16)}multiply(t){const[e,i,n,r]=Oe._temp.setValue(t)._components;return this._components[0]*=e,this._components[1]*=i,this._components[2]*=n,this._components[3]*=r,this._refreshInt(),this._value=null,this}premultiply(t,e=!0){return e&&(this._components[0]*=t,this._components[1]*=t,this._components[2]*=t),this._components[3]=t,this._refreshInt(),this._value=null,this}toPremultiplied(t,e=!0){if(t===1)return (255<<24)+this._int;if(t===0)return e?0:this._int;let i=this._int>>16&255,n=this._int>>8&255,r=this._int&255;return e&&(i=i*t+.5|0,n=n*t+.5|0,r=r*t+.5|0),(t*255<<24)+(i<<16)+(n<<8)+r}toHex(){const t=this._int.toString(16);return `#${"000000".substring(0,6-t.length)+t}`}toHexa(){const e=Math.round(this._components[3]*255).toString(16);return this.toHex()+"00".substring(0,2-e.length)+e}setAlpha(t){return this._components[3]=this._clamp(t),this}_normalize(t){let e,i,n,r;if((typeof t=="number"||t instanceof Number)&&t>=0&&t<=16777215){const o=t;e=(o>>16&255)/255,i=(o>>8&255)/255,n=(o&255)/255,r=1;}else if((Array.isArray(t)||t instanceof Float32Array)&&t.length>=3&&t.length<=4)t=this._clamp(t),[e,i,n,r=1]=t;else if((t instanceof Uint8Array||t instanceof Uint8ClampedArray)&&t.length>=3&&t.length<=4)t=this._clamp(t,0,255),[e,i,n,r=255]=t,e/=255,i/=255,n/=255,r/=255;else if(typeof t=="string"||typeof t=="object"){if(typeof t=="string"){const a=Oe.HEX_PATTERN.exec(t);a&&(t=`#${a[2]}`);}const o=Nt(t);o.isValid()&&({r:e,g:i,b:n,a:r}=o.rgba,e/=255,i/=255,n/=255);}if(e!==void 0)this._components[0]=e,this._components[1]=i,this._components[2]=n,this._components[3]=r,this._refreshInt();else throw new Error(`Unable to convert color ${t}`)}_refreshInt(){this._clamp(this._components);const[t,e,i]=this._components;this._int=(t*255<<16)+(e*255<<8)+(i*255|0);}_clamp(t,e=0,i=1){return typeof t=="number"?Math.min(Math.max(t,e),i):(t.forEach((n,r)=>{t[r]=Math.min(Math.max(n,e),i);}),t)}static isColorLike(t){return typeof t=="number"||typeof t=="string"||t instanceof Number||t instanceof Oe||Array.isArray(t)||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Float32Array||t.r!==void 0&&t.g!==void 0&&t.b!==void 0||t.r!==void 0&&t.g!==void 0&&t.b!==void 0&&t.a!==void 0||t.h!==void 0&&t.s!==void 0&&t.l!==void 0||t.h!==void 0&&t.s!==void 0&&t.l!==void 0&&t.a!==void 0||t.h!==void 0&&t.s!==void 0&&t.v!==void 0||t.h!==void 0&&t.s!==void 0&&t.v!==void 0&&t.a!==void 0}};be.shared=new be;be._temp=new be;be.HEX_PATTERN=/^(#|0x)?(([a-f0-9]{3}){1,2}([a-f0-9]{2})?)$/i;let st=be;const sl={cullArea:null,cullable:!1,cullableChildren:!0};class Hs{constructor(t,e){this._pool=[],this._count=0,this._index=0,this._classType=t,e&&this.prepopulate(e);}prepopulate(t){for(let e=0;e<t;e++)this._pool[this._index++]=new this._classType;this._count+=t;}get(t){let e;return this._index>0?e=this._pool[--this._index]:e=new this._classType,e.init?.(t),e}return(t){t.reset?.(),this._pool[this._index++]=t;}get totalSize(){return this._count}get totalFree(){return this._index}get totalUsed(){return this._count-this._index}clear(){this._pool.length=0,this._index=0;}}class nl{constructor(){this._poolsByClass=new Map;}prepopulate(t,e){this.getPool(t).prepopulate(e);}get(t,e){return this.getPool(t).get(e)}return(t){this.getPool(t.constructor).return(t);}getPool(t){return this._poolsByClass.has(t)||this._poolsByClass.set(t,new Hs(t)),this._poolsByClass.get(t)}stats(){const t={};return this._poolsByClass.forEach(e=>{const i=t[e._classType.name]?e._classType.name+e._classType.ID:e._classType.name;t[i]={free:e.totalFree,used:e.totalUsed,size:e.totalSize};}),t}}const Yt=new nl,rl={get isCachedAsTexture(){return !!this.renderGroup?.isCachedAsTexture},cacheAsTexture(s){typeof s=="boolean"&&s===!1?this.disableRenderGroup():(this.enableRenderGroup(),this.renderGroup.enableCacheAsTexture(s===!0?{}:s));},updateCacheTexture(){this.renderGroup?.updateCacheTexture();},get cacheAsBitmap(){return this.isCachedAsTexture},set cacheAsBitmap(s){D("v8.6.0","cacheAsBitmap is deprecated, use cacheAsTexture instead."),this.cacheAsTexture(s);}};function ol(s,t,e){const i=s.length;let n;if(t>=i||e===0)return;e=t+e>i?i-t:e;const r=i-e;for(n=t;n<r;++n)s[n]=s[n+e];s.length=r;}const al={allowChildren:!0,removeChildren(s=0,t){const e=t??this.children.length,i=e-s,n=[];if(i>0&&i<=e){for(let o=e-1;o>=s;o--){const a=this.children[o];a&&(n.push(a),a.parent=null);}ol(this.children,s,e);const r=this.renderGroup||this.parentRenderGroup;r&&r.removeChildren(n);for(let o=0;o<n.length;++o)this.emit("childRemoved",n[o],this,o),n[o].emit("removed",this);return n}else if(i===0&&this.children.length===0)return n;throw new RangeError("removeChildren: numeric values are outside the acceptable range.")},removeChildAt(s){const t=this.getChildAt(s);return this.removeChild(t)},getChildAt(s){if(s<0||s>=this.children.length)throw new Error(`getChildAt: Index (${s}) does not exist.`);return this.children[s]},setChildIndex(s,t){if(t<0||t>=this.children.length)throw new Error(`The index ${t} supplied is out of bounds ${this.children.length}`);this.getChildIndex(s),this.addChildAt(s,t);},getChildIndex(s){const t=this.children.indexOf(s);if(t===-1)throw new Error("The supplied Container must be a child of the caller");return t},addChildAt(s,t){this.allowChildren||D(N,"addChildAt: Only Containers will be allowed to add children in v8.0.0");const{children:e}=this;if(t<0||t>e.length)throw new Error(`${s}addChildAt: The index ${t} supplied is out of bounds ${e.length}`);if(s.parent){const n=s.parent.children.indexOf(s);if(s.parent===this&&n===t)return s;n!==-1&&s.parent.children.splice(n,1);}t===e.length?e.push(s):e.splice(t,0,s),s.parent=this,s.didChange=!0,s._updateFlags=15;const i=this.renderGroup||this.parentRenderGroup;return i&&i.addChild(s),this.sortableChildren&&(this.sortDirty=!0),this.emit("childAdded",s,this,t),s.emit("added",this),s},swapChildren(s,t){if(s===t)return;const e=this.getChildIndex(s),i=this.getChildIndex(t);this.children[e]=t,this.children[i]=s;const n=this.renderGroup||this.parentRenderGroup;n&&(n.structureDidChange=!0),this._didContainerChangeTick++;},removeFromParent(){this.parent?.removeChild(this);},reparentChild(...s){return s.length===1?this.reparentChildAt(s[0],this.children.length):(s.forEach(t=>this.reparentChildAt(t,this.children.length)),s[0])},reparentChildAt(s,t){if(s.parent===this)return this.setChildIndex(s,t),s;const e=s.worldTransform.clone();s.removeFromParent(),this.addChildAt(s,t);const i=this.worldTransform.clone();return i.invert(),e.prepend(i),s.setFromMatrix(e),s}},ll={collectRenderables(s,t,e){this.parentRenderLayer&&this.parentRenderLayer!==e||this.globalDisplayStatus<7||!this.includeInBuild||(this.sortableChildren&&this.sortChildren(),this.isSimple?this.collectRenderablesSimple(s,t,e):this.renderGroup?t.renderPipes.renderGroup.addRenderGroup(this.renderGroup,s):this.collectRenderablesWithEffects(s,t,e));},collectRenderablesSimple(s,t,e){const i=this.children,n=i.length;for(let r=0;r<n;r++)i[r].collectRenderables(s,t,e);},collectRenderablesWithEffects(s,t,e){const{renderPipes:i}=t;for(let n=0;n<this.effects.length;n++){const r=this.effects[n];i[r.pipe].push(r,this,s);}this.collectRenderablesSimple(s,t,e);for(let n=this.effects.length-1;n>=0;n--){const r=this.effects[n];i[r.pipe].pop(r,this,s);}}};class bn{constructor(){this.pipe="filter",this.priority=1;}destroy(){for(let t=0;t<this.filters.length;t++)this.filters[t].destroy();this.filters=null,this.filterArea=null;}}class cl{constructor(){this._effectClasses=[],this._tests=[],this._initialized=!1;}init(){this._initialized||(this._initialized=!0,this._effectClasses.forEach(t=>{this.add({test:t.test,maskClass:t});}));}add(t){this._tests.push(t);}getMaskEffect(t){this._initialized||this.init();for(let e=0;e<this._tests.length;e++){const i=this._tests[e];if(i.test(t))return Yt.get(i.maskClass,t)}return t}returnMaskEffect(t){Yt.return(t);}}const As=new cl;yt.handleByList(T.MaskEffect,As._effectClasses);const hl={_maskEffect:null,_maskOptions:{inverse:!1},_filterEffect:null,effects:[],_markStructureAsChanged(){const s=this.renderGroup||this.parentRenderGroup;s&&(s.structureDidChange=!0);},addEffect(s){this.effects.indexOf(s)===-1&&(this.effects.push(s),this.effects.sort((e,i)=>e.priority-i.priority),this._markStructureAsChanged(),this._updateIsSimple());},removeEffect(s){const t=this.effects.indexOf(s);t!==-1&&(this.effects.splice(t,1),this._markStructureAsChanged(),this._updateIsSimple());},set mask(s){const t=this._maskEffect;t?.mask!==s&&(t&&(this.removeEffect(t),As.returnMaskEffect(t),this._maskEffect=null),s!=null&&(this._maskEffect=As.getMaskEffect(s),this.addEffect(this._maskEffect)));},setMask(s){this._maskOptions={...this._maskOptions,...s},s.mask&&(this.mask=s.mask),this._markStructureAsChanged();},get mask(){return this._maskEffect?.mask},set filters(s){!Array.isArray(s)&&s&&(s=[s]);const t=this._filterEffect||(this._filterEffect=new bn);s=s;const e=s?.length>0,i=t.filters?.length>0,n=e!==i;s=Array.isArray(s)?s.slice(0):s,t.filters=Object.freeze(s),n&&(e?this.addEffect(t):(this.removeEffect(t),t.filters=s??null));},get filters(){return this._filterEffect?.filters},set filterArea(s){this._filterEffect||(this._filterEffect=new bn),this._filterEffect.filterArea=s;},get filterArea(){return this._filterEffect?.filterArea}},dl={label:null,get name(){return D(N,"Container.name property has been removed, use Container.label instead"),this.label},set name(s){D(N,"Container.name property has been removed, use Container.label instead"),this.label=s;},getChildByName(s,t=!1){return this.getChildByLabel(s,t)},getChildByLabel(s,t=!1){const e=this.children;for(let i=0;i<e.length;i++){const n=e[i];if(n.label===s||s instanceof RegExp&&s.test(n.label))return n}if(t)for(let i=0;i<e.length;i++){const r=e[i].getChildByLabel(s,!0);if(r)return r}return null},getChildrenByLabel(s,t=!1,e=[]){const i=this.children;for(let n=0;n<i.length;n++){const r=i[n];(r.label===s||s instanceof RegExp&&s.test(r.label))&&e.push(r);}if(t)for(let n=0;n<i.length;n++)i[n].getChildrenByLabel(s,!0,e);return e}},gt=new Hs(K),Xt=new Hs(Ft),ul=new K,_l={getFastGlobalBounds(s,t){t||(t=new Ft),t.clear(),this._getGlobalBoundsRecursive(!!s,t,this.parentRenderLayer),t.isValid||t.set(0,0,0,0);const e=this.renderGroup||this.parentRenderGroup;return t.applyMatrix(e.worldTransform),t},_getGlobalBoundsRecursive(s,t,e){let i=t;if(s&&this.parentRenderLayer!==e||this.localDisplayStatus!==7||!this.measurable)return;const n=!!this.effects.length;if((this.renderGroup||n)&&(i=Xt.get().clear()),this.boundsArea)t.addRect(this.boundsArea,this.worldTransform);else {if(this.renderPipeId){const o=this.bounds;i.addFrame(o.minX,o.minY,o.maxX,o.maxY,this.groupTransform);}const r=this.children;for(let o=0;o<r.length;o++)r[o]._getGlobalBoundsRecursive(s,i,e);}if(n){let r=!1;const o=this.renderGroup||this.parentRenderGroup;for(let a=0;a<this.effects.length;a++)this.effects[a].addBounds&&(r||(r=!0,i.applyMatrix(o.worldTransform)),this.effects[a].addBounds(i,!0));r&&(i.applyMatrix(o.worldTransform.copyTo(ul).invert()),t.addBounds(i,this.relativeGroupTransform)),t.addBounds(i),Xt.return(i);}else this.renderGroup&&(t.addBounds(i,this.relativeGroupTransform),Xt.return(i));}};function Sr(s,t,e){e.clear();let i,n;return s.parent?t?i=s.parent.worldTransform:(n=gt.get().identity(),i=js(s,n)):i=K.IDENTITY,Br(s,e,i,t),n&&gt.return(n),e.isValid||e.set(0,0,0,0),e}function Br(s,t,e,i){if(!s.visible||!s.measurable)return;let n;i?n=s.worldTransform:(s.updateLocalTransform(),n=gt.get(),n.appendFrom(s.localTransform,e));const r=t,o=!!s.effects.length;if(o&&(t=Xt.get().clear()),s.boundsArea)t.addRect(s.boundsArea,n);else {s.bounds&&(t.matrix=n,t.addBounds(s.bounds));for(let a=0;a<s.children.length;a++)Br(s.children[a],t,n,i);}if(o){for(let a=0;a<s.effects.length;a++)s.effects[a].addBounds?.(t);r.addBounds(t,K.IDENTITY),Xt.return(t);}i||gt.return(n);}function js(s,t){const e=s.parent;return e&&(js(e,t),e.updateLocalTransform(),t.append(e.localTransform)),t}function zr(s,t){if(s===16777215||!t)return t;if(t===16777215||!s)return s;const e=s>>16&255,i=s>>8&255,n=s&255,r=t>>16&255,o=t>>8&255,a=t&255,l=e*r/255|0,c=i*o/255|0,h=n*a/255|0;return (l<<16)+(c<<8)+h}const Cn=16777215;function vn(s,t){return s===Cn?t:t===Cn?s:zr(s,t)}function di(s){return ((s&255)<<16)+(s&65280)+(s>>16&255)}const fl={getGlobalAlpha(s){if(s)return this.renderGroup?this.renderGroup.worldAlpha:this.parentRenderGroup?this.parentRenderGroup.worldAlpha*this.alpha:this.alpha;let t=this.alpha,e=this.parent;for(;e;)t*=e.alpha,e=e.parent;return t},getGlobalTransform(s,t){if(t)return s.copyFrom(this.worldTransform);this.updateLocalTransform();const e=js(this,gt.get().identity());return s.appendFrom(this.localTransform,e),gt.return(e),s},getGlobalTint(s){if(s)return this.renderGroup?di(this.renderGroup.worldColor):this.parentRenderGroup?di(vn(this.localColor,this.parentRenderGroup.worldColor)):this.tint;let t=this.localColor,e=this.parent;for(;e;)t=vn(t,e.localColor),e=e.parent;return di(t)}};let Oi=0;const wn=500;function ot(...s){Oi!==wn&&(Oi++,Oi===wn?console.warn("PixiJS Warning: too many warnings, no more warnings will be reported to the console by PixiJS."):console.warn("PixiJS Warning: ",...s));}function Pr(s,t,e){return t.clear(),e||(e=K.IDENTITY),Tr(s,t,e,s,!0),t.isValid||t.set(0,0,0,0),t}function Tr(s,t,e,i,n){let r;if(n)r=gt.get(),r=e.copyTo(r);else {if(!s.visible||!s.measurable)return;s.updateLocalTransform();const l=s.localTransform;r=gt.get(),r.appendFrom(l,e);}const o=t,a=!!s.effects.length;if(a&&(t=Xt.get().clear()),s.boundsArea)t.addRect(s.boundsArea,r);else {s.renderPipeId&&(t.matrix=r,t.addBounds(s.bounds));const l=s.children;for(let c=0;c<l.length;c++)Tr(l[c],t,r,i,!1);}if(a){for(let l=0;l<s.effects.length;l++)s.effects[l].addLocalBounds?.(t,i);o.addBounds(t,K.IDENTITY),Xt.return(t);}gt.return(r);}function Rr(s,t){const e=s.children;for(let i=0;i<e.length;i++){const n=e[i],r=n.uid,o=(n._didViewChangeTick&65535)<<16|n._didContainerChangeTick&65535,a=t.index;(t.data[a]!==r||t.data[a+1]!==o)&&(t.data[t.index]=r,t.data[t.index+1]=o,t.didChange=!0),t.index=a+2,n.children.length&&Rr(n,t);}return t.didChange}const Al=new K,pl={_localBoundsCacheId:-1,_localBoundsCacheData:null,_setWidth(s,t){const e=Math.sign(this.scale.x)||1;t!==0?this.scale.x=s/t*e:this.scale.x=e;},_setHeight(s,t){const e=Math.sign(this.scale.y)||1;t!==0?this.scale.y=s/t*e:this.scale.y=e;},getLocalBounds(){this._localBoundsCacheData||(this._localBoundsCacheData={data:[],index:1,didChange:!1,localBounds:new Ft});const s=this._localBoundsCacheData;return s.index=1,s.didChange=!1,s.data[0]!==this._didViewChangeTick&&(s.didChange=!0,s.data[0]=this._didViewChangeTick),Rr(this,s),s.didChange&&Pr(this,s.localBounds,Al),s.localBounds},getBounds(s,t){return Sr(this,s,t||new Ft)}},ml={_onRender:null,set onRender(s){const t=this.renderGroup||this.parentRenderGroup;if(!s){this._onRender&&t?.removeOnRender(this),this._onRender=null;return}this._onRender||t?.addOnRender(this),this._onRender=s;},get onRender(){return this._onRender}},gl={_zIndex:0,sortDirty:!1,sortableChildren:!1,get zIndex(){return this._zIndex},set zIndex(s){this._zIndex!==s&&(this._zIndex=s,this.depthOfChildModified());},depthOfChildModified(){this.parent&&(this.parent.sortableChildren=!0,this.parent.sortDirty=!0),this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0);},sortChildren(){this.sortDirty&&(this.sortDirty=!1,this.children.sort(yl));}};function yl(s,t){return s._zIndex-t._zIndex}const xl={getGlobalPosition(s=new L,t=!1){return this.parent?this.parent.toGlobal(this._position,s,t):(s.x=this._position.x,s.y=this._position.y),s},toGlobal(s,t,e=!1){const i=this.getGlobalTransform(gt.get(),e);return t=i.apply(s,t),gt.return(i),t},toLocal(s,t,e,i){t&&(s=t.toGlobal(s,e,i));const n=this.getGlobalTransform(gt.get(),i);return e=n.applyInverse(s,e),gt.return(n),e}};class Gr{constructor(){this.uid=rt("instructionSet"),this.instructions=[],this.instructionSize=0,this.renderables=[],this.gcTick=0;}reset(){this.instructionSize=0;}add(t){this.instructions[this.instructionSize++]=t;}log(){this.instructions.length=this.instructionSize,console.table(this.instructions,["type","action"]);}}let bl=0;class Cl{constructor(t){this._poolKeyHash=Object.create(null),this._texturePool={},this.textureOptions=t||{},this.enableFullScreen=!1;}createTexture(t,e,i){const n=new Wt({...this.textureOptions,width:t,height:e,resolution:1,antialias:i,autoGarbageCollect:!1});return new W({source:n,label:`texturePool_${bl++}`})}getOptimalTexture(t,e,i=1,n){let r=Math.ceil(t*i-1e-6),o=Math.ceil(e*i-1e-6);r=gi(r),o=gi(o);const a=(r<<17)+(o<<1)+(n?1:0);this._texturePool[a]||(this._texturePool[a]=[]);let l=this._texturePool[a].pop();return l||(l=this.createTexture(r,o,n)),l.source._resolution=i,l.source.width=r/i,l.source.height=o/i,l.source.pixelWidth=r,l.source.pixelHeight=o,l.frame.x=0,l.frame.y=0,l.frame.width=t,l.frame.height=e,l.updateUvs(),this._poolKeyHash[l.uid]=a,l}getSameSizeTexture(t,e=!1){const i=t.source;return this.getOptimalTexture(t.width,t.height,i._resolution,e)}returnTexture(t){const e=this._poolKeyHash[t.uid];this._texturePool[e].push(t);}clear(t){if(t=t!==!1,t)for(const e in this._texturePool){const i=this._texturePool[e];if(i)for(let n=0;n<i.length;n++)i[n].destroy(!0);}this._texturePool={};}}const Ke=new Cl;class vl{constructor(){this.renderPipeId="renderGroup",this.root=null,this.canBundle=!1,this.renderGroupParent=null,this.renderGroupChildren=[],this.worldTransform=new K,this.worldColorAlpha=4294967295,this.worldColor=16777215,this.worldAlpha=1,this.childrenToUpdate=Object.create(null),this.updateTick=0,this.gcTick=0,this.childrenRenderablesToUpdate={list:[],index:0},this.structureDidChange=!0,this.instructionSet=new Gr,this._onRenderContainers=[],this.textureNeedsUpdate=!0,this.isCachedAsTexture=!1,this._matrixDirty=7;}init(t){this.root=t,t._onRender&&this.addOnRender(t),t.didChange=!0;const e=t.children;for(let i=0;i<e.length;i++){const n=e[i];n._updateFlags=15,this.addChild(n);}}enableCacheAsTexture(t={}){this.textureOptions=t,this.isCachedAsTexture=!0,this.textureNeedsUpdate=!0;}disableCacheAsTexture(){this.isCachedAsTexture=!1,this.texture&&(Ke.returnTexture(this.texture),this.texture=null);}updateCacheTexture(){this.textureNeedsUpdate=!0;}reset(){this.renderGroupChildren.length=0;for(const t in this.childrenToUpdate){const e=this.childrenToUpdate[t];e.list.fill(null),e.index=0;}this.childrenRenderablesToUpdate.index=0,this.childrenRenderablesToUpdate.list.fill(null),this.root=null,this.updateTick=0,this.structureDidChange=!0,this._onRenderContainers.length=0,this.renderGroupParent=null,this.disableCacheAsTexture();}get localTransform(){return this.root.localTransform}addRenderGroupChild(t){t.renderGroupParent&&t.renderGroupParent._removeRenderGroupChild(t),t.renderGroupParent=this,this.renderGroupChildren.push(t);}_removeRenderGroupChild(t){const e=this.renderGroupChildren.indexOf(t);e>-1&&this.renderGroupChildren.splice(e,1),t.renderGroupParent=null;}addChild(t){if(this.structureDidChange=!0,t.parentRenderGroup=this,t.updateTick=-1,t.parent===this.root?t.relativeRenderGroupDepth=1:t.relativeRenderGroupDepth=t.parent.relativeRenderGroupDepth+1,t.didChange=!0,this.onChildUpdate(t),t.renderGroup){this.addRenderGroupChild(t.renderGroup);return}t._onRender&&this.addOnRender(t);const e=t.children;for(let i=0;i<e.length;i++)this.addChild(e[i]);}removeChild(t){if(this.structureDidChange=!0,t._onRender&&(t.renderGroup||this.removeOnRender(t)),t.parentRenderGroup=null,t.renderGroup){this._removeRenderGroupChild(t.renderGroup);return}const e=t.children;for(let i=0;i<e.length;i++)this.removeChild(e[i]);}removeChildren(t){for(let e=0;e<t.length;e++)this.removeChild(t[e]);}onChildUpdate(t){let e=this.childrenToUpdate[t.relativeRenderGroupDepth];e||(e=this.childrenToUpdate[t.relativeRenderGroupDepth]={index:0,list:[]}),e.list[e.index++]=t;}updateRenderable(t){t.globalDisplayStatus<7||(this.instructionSet.renderPipes[t.renderPipeId].updateRenderable(t),t.didViewUpdate=!1);}onChildViewUpdate(t){this.childrenRenderablesToUpdate.list[this.childrenRenderablesToUpdate.index++]=t;}get isRenderable(){return this.root.localDisplayStatus===7&&this.worldAlpha>0}addOnRender(t){this._onRenderContainers.push(t);}removeOnRender(t){this._onRenderContainers.splice(this._onRenderContainers.indexOf(t),1);}runOnRender(t){for(let e=0;e<this._onRenderContainers.length;e++)this._onRenderContainers[e]._onRender(t);}destroy(){this.disableCacheAsTexture(),this.renderGroupParent=null,this.root=null,this.childrenRenderablesToUpdate=null,this.childrenToUpdate=null,this.renderGroupChildren=null,this._onRenderContainers=null,this.instructionSet=null;}getChildren(t=[]){const e=this.root.children;for(let i=0;i<e.length;i++)this._getChildren(e[i],t);return t}_getChildren(t,e=[]){if(e.push(t),t.renderGroup)return e;const i=t.children;for(let n=0;n<i.length;n++)this._getChildren(i[n],e);return e}invalidateMatrices(){this._matrixDirty=7;}get inverseWorldTransform(){return this._matrixDirty&1?(this._matrixDirty&=-2,this._inverseWorldTransform||(this._inverseWorldTransform=new K),this._inverseWorldTransform.copyFrom(this.worldTransform).invert()):this._inverseWorldTransform}get textureOffsetInverseTransform(){return this._matrixDirty&2?(this._matrixDirty&=-3,this._textureOffsetInverseTransform||(this._textureOffsetInverseTransform=new K),this._textureOffsetInverseTransform.copyFrom(this.inverseWorldTransform).translate(-this._textureBounds.x,-this._textureBounds.y)):this._textureOffsetInverseTransform}get inverseParentTextureTransform(){if(!(this._matrixDirty&4))return this._inverseParentTextureTransform;this._matrixDirty&=-5;const t=this._parentCacheAsTextureRenderGroup;return t?(this._inverseParentTextureTransform||(this._inverseParentTextureTransform=new K),this._inverseParentTextureTransform.copyFrom(this.worldTransform).prepend(t.inverseWorldTransform).translate(-t._textureBounds.x,-t._textureBounds.y)):this.worldTransform}get cacheToLocalTransform(){return this._parentCacheAsTextureRenderGroup?this._parentCacheAsTextureRenderGroup.textureOffsetInverseTransform:null}}function wl(s,t,e={}){for(const i in t)!e[i]&&t[i]!==void 0&&(s[i]=t[i]);}const Fi=new kt(null),Li=new kt(null),Wi=new kt(null,1,1),kn=1,kl=2,Ui=4;class O extends Lt{constructor(t={}){super(),this.uid=rt("renderable"),this._updateFlags=15,this.renderGroup=null,this.parentRenderGroup=null,this.parentRenderGroupIndex=0,this.didChange=!1,this.didViewUpdate=!1,this.relativeRenderGroupDepth=0,this.children=[],this.parent=null,this.includeInBuild=!0,this.measurable=!0,this.isSimple=!0,this.updateTick=-1,this.localTransform=new K,this.relativeGroupTransform=new K,this.groupTransform=this.relativeGroupTransform,this.destroyed=!1,this._position=new kt(this,0,0),this._scale=Wi,this._pivot=Li,this._skew=Fi,this._cx=1,this._sx=0,this._cy=0,this._sy=1,this._rotation=0,this.localColor=16777215,this.localAlpha=1,this.groupAlpha=1,this.groupColor=16777215,this.groupColorAlpha=4294967295,this.localBlendMode="inherit",this.groupBlendMode="normal",this.localDisplayStatus=7,this.globalDisplayStatus=7,this._didContainerChangeTick=0,this._didViewChangeTick=0,this._didLocalTransformChangeId=-1,this.effects=[],wl(this,t,{children:!0,parent:!0,effects:!0}),t.children?.forEach(e=>this.addChild(e)),t.parent?.addChild(this);}static mixin(t){Object.defineProperties(O.prototype,Object.getOwnPropertyDescriptors(t));}set _didChangeId(t){this._didViewChangeTick=t>>12&4095,this._didContainerChangeTick=t&4095;}get _didChangeId(){return this._didContainerChangeTick&4095|(this._didViewChangeTick&4095)<<12}addChild(...t){if(this.allowChildren||D(N,"addChild: Only Containers will be allowed to add children in v8.0.0"),t.length>1){for(let n=0;n<t.length;n++)this.addChild(t[n]);return t[0]}const e=t[0],i=this.renderGroup||this.parentRenderGroup;return e.parent===this?(this.children.splice(this.children.indexOf(e),1),this.children.push(e),i&&(i.structureDidChange=!0),e):(e.parent&&e.parent.removeChild(e),this.children.push(e),this.sortableChildren&&(this.sortDirty=!0),e.parent=this,e.didChange=!0,e._updateFlags=15,i&&i.addChild(e),this.emit("childAdded",e,this,this.children.length-1),e.emit("added",this),this._didViewChangeTick++,e._zIndex!==0&&e.depthOfChildModified(),e)}removeChild(...t){if(t.length>1){for(let n=0;n<t.length;n++)this.removeChild(t[n]);return t[0]}const e=t[0],i=this.children.indexOf(e);return i>-1&&(this._didViewChangeTick++,this.children.splice(i,1),this.renderGroup?this.renderGroup.removeChild(e):this.parentRenderGroup&&this.parentRenderGroup.removeChild(e),e.parentRenderLayer&&e.parentRenderLayer.detach(e),e.parent=null,this.emit("childRemoved",e,this,i),e.emit("removed",this)),e}_onUpdate(t){t&&t===this._skew&&this._updateSkew(),this._didContainerChangeTick++,!this.didChange&&(this.didChange=!0,this.parentRenderGroup&&this.parentRenderGroup.onChildUpdate(this));}set isRenderGroup(t){!!this.renderGroup!==t&&(t?this.enableRenderGroup():this.disableRenderGroup());}get isRenderGroup(){return !!this.renderGroup}enableRenderGroup(){if(this.renderGroup)return;const t=this.parentRenderGroup;t?.removeChild(this),this.renderGroup=Yt.get(vl,this),this.groupTransform=K.IDENTITY,t?.addChild(this),this._updateIsSimple();}disableRenderGroup(){if(!this.renderGroup)return;const t=this.parentRenderGroup;t?.removeChild(this),Yt.return(this.renderGroup),this.renderGroup=null,this.groupTransform=this.relativeGroupTransform,t?.addChild(this),this._updateIsSimple();}_updateIsSimple(){this.isSimple=!this.renderGroup&&this.effects.length===0;}get worldTransform(){return this._worldTransform||(this._worldTransform=new K),this.renderGroup?this._worldTransform.copyFrom(this.renderGroup.worldTransform):this.parentRenderGroup&&this._worldTransform.appendFrom(this.relativeGroupTransform,this.parentRenderGroup.worldTransform),this._worldTransform}get x(){return this._position.x}set x(t){this._position.x=t;}get y(){return this._position.y}set y(t){this._position.y=t;}get position(){return this._position}set position(t){this._position.copyFrom(t);}get rotation(){return this._rotation}set rotation(t){this._rotation!==t&&(this._rotation=t,this._onUpdate(this._skew));}get angle(){return this.rotation*Fa}set angle(t){this.rotation=t*La;}get pivot(){return this._pivot===Li&&(this._pivot=new kt(this,0,0)),this._pivot}set pivot(t){this._pivot===Li&&(this._pivot=new kt(this,0,0)),typeof t=="number"?this._pivot.set(t):this._pivot.copyFrom(t);}get skew(){return this._skew===Fi&&(this._skew=new kt(this,0,0)),this._skew}set skew(t){this._skew===Fi&&(this._skew=new kt(this,0,0)),this._skew.copyFrom(t);}get scale(){return this._scale===Wi&&(this._scale=new kt(this,1,1)),this._scale}set scale(t){this._scale===Wi&&(this._scale=new kt(this,0,0)),typeof t=="number"?this._scale.set(t):this._scale.copyFrom(t);}get width(){return Math.abs(this.scale.x*this.getLocalBounds().width)}set width(t){const e=this.getLocalBounds().width;this._setWidth(t,e);}get height(){return Math.abs(this.scale.y*this.getLocalBounds().height)}set height(t){const e=this.getLocalBounds().height;this._setHeight(t,e);}getSize(t){t||(t={});const e=this.getLocalBounds();return t.width=Math.abs(this.scale.x*e.width),t.height=Math.abs(this.scale.y*e.height),t}setSize(t,e){const i=this.getLocalBounds();typeof t=="object"?(e=t.height??t.width,t=t.width):e??(e=t),t!==void 0&&this._setWidth(t,i.width),e!==void 0&&this._setHeight(e,i.height);}_updateSkew(){const t=this._rotation,e=this._skew;this._cx=Math.cos(t+e._y),this._sx=Math.sin(t+e._y),this._cy=-Math.sin(t-e._x),this._sy=Math.cos(t-e._x);}updateTransform(t){return this.position.set(typeof t.x=="number"?t.x:this.position.x,typeof t.y=="number"?t.y:this.position.y),this.scale.set(typeof t.scaleX=="number"?t.scaleX||1:this.scale.x,typeof t.scaleY=="number"?t.scaleY||1:this.scale.y),this.rotation=typeof t.rotation=="number"?t.rotation:this.rotation,this.skew.set(typeof t.skewX=="number"?t.skewX:this.skew.x,typeof t.skewY=="number"?t.skewY:this.skew.y),this.pivot.set(typeof t.pivotX=="number"?t.pivotX:this.pivot.x,typeof t.pivotY=="number"?t.pivotY:this.pivot.y),this}setFromMatrix(t){t.decompose(this);}updateLocalTransform(){const t=this._didContainerChangeTick;if(this._didLocalTransformChangeId===t)return;this._didLocalTransformChangeId=t;const e=this.localTransform,i=this._scale,n=this._pivot,r=this._position,o=i._x,a=i._y,l=n._x,c=n._y;e.a=this._cx*o,e.b=this._sx*o,e.c=this._cy*a,e.d=this._sy*a,e.tx=r._x-(l*e.a+c*e.c),e.ty=r._y-(l*e.b+c*e.d);}set alpha(t){t!==this.localAlpha&&(this.localAlpha=t,this._updateFlags|=kn,this._onUpdate());}get alpha(){return this.localAlpha}set tint(t){const i=st.shared.setValue(t??16777215).toBgrNumber();i!==this.localColor&&(this.localColor=i,this._updateFlags|=kn,this._onUpdate());}get tint(){return di(this.localColor)}set blendMode(t){this.localBlendMode!==t&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=kl,this.localBlendMode=t,this._onUpdate());}get blendMode(){return this.localBlendMode}get visible(){return !!(this.localDisplayStatus&2)}set visible(t){const e=t?2:0;(this.localDisplayStatus&2)!==e&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=Ui,this.localDisplayStatus^=2,this._onUpdate());}get culled(){return !(this.localDisplayStatus&4)}set culled(t){const e=t?0:4;(this.localDisplayStatus&4)!==e&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=Ui,this.localDisplayStatus^=4,this._onUpdate());}get renderable(){return !!(this.localDisplayStatus&1)}set renderable(t){const e=t?1:0;(this.localDisplayStatus&1)!==e&&(this._updateFlags|=Ui,this.localDisplayStatus^=1,this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._onUpdate());}get isRenderable(){return this.localDisplayStatus===7&&this.groupAlpha>0}destroy(t=!1){if(this.destroyed)return;this.destroyed=!0;let e;if(this.children.length&&(e=this.removeChildren(0,this.children.length)),this.removeFromParent(),this.parent=null,this._maskEffect=null,this._filterEffect=null,this.effects=null,this._position=null,this._scale=null,this._pivot=null,this._skew=null,this.emit("destroyed",this),this.removeAllListeners(),(typeof t=="boolean"?t:t?.children)&&e)for(let n=0;n<e.length;++n)e[n].destroy(t);this.renderGroup?.destroy(),this.renderGroup=null;}}O.mixin(al);O.mixin(_l);O.mixin(xl);O.mixin(ml);O.mixin(pl);O.mixin(hl);O.mixin(dl);O.mixin(gl);O.mixin(sl);O.mixin(rl);O.mixin(fl);O.mixin(ll);class Or extends O{constructor(){super(...arguments),this.canBundle=!0,this.allowChildren=!1,this._roundPixels=0,this._lastUsed=-1,this._bounds=new Ft(0,1,0,0),this._boundsDirty=!0;}get bounds(){return this._boundsDirty?(this.updateBounds(),this._boundsDirty=!1,this._bounds):this._bounds}get roundPixels(){return !!this._roundPixels}set roundPixels(t){this._roundPixels=t?1:0;}containsPoint(t){const e=this.bounds,{x:i,y:n}=t;return i>=e.minX&&i<=e.maxX&&n>=e.minY&&n<=e.maxY}onViewUpdate(){if(this._didViewChangeTick++,this._boundsDirty=!0,this.didViewUpdate)return;this.didViewUpdate=!0;const t=this.renderGroup||this.parentRenderGroup;t&&t.onChildViewUpdate(this);}destroy(t){super.destroy(t),this._bounds=null;}collectRenderablesSimple(t,e,i){const{renderPipes:n,renderableGC:r}=e;n.blendMode.setBlendMode(this,this.groupBlendMode,t),n[this.renderPipeId].addRenderable(this,t),r.addRenderable(this),this.didViewUpdate=!1;const a=this.children,l=a.length;for(let c=0;c<l;c++)a[c].collectRenderables(t,e,i);}}class H extends Or{constructor(t=W.EMPTY){t instanceof W&&(t={texture:t});const{texture:e=W.EMPTY,anchor:i,roundPixels:n,width:r,height:o,...a}=t;super({label:"Sprite",...a}),this.renderPipeId="sprite",this.batched=!0,this._visualBounds={minX:0,maxX:1,minY:0,maxY:0},this._anchor=new kt({_onUpdate:()=>{this.onViewUpdate();}}),i?this.anchor=i:e.defaultAnchor&&(this.anchor=e.defaultAnchor),this.texture=e,this.allowChildren=!1,this.roundPixels=n??!1,r!==void 0&&(this.width=r),o!==void 0&&(this.height=o);}static from(t,e=!1){return t instanceof W?new H(t):new H(W.from(t,e))}set texture(t){t||(t=W.EMPTY);const e=this._texture;e!==t&&(e&&e.dynamic&&e.off("update",this.onViewUpdate,this),t.dynamic&&t.on("update",this.onViewUpdate,this),this._texture=t,this._width&&this._setWidth(this._width,this._texture.orig.width),this._height&&this._setHeight(this._height,this._texture.orig.height),this.onViewUpdate());}get texture(){return this._texture}get visualBounds(){return Xa(this._visualBounds,this._anchor,this._texture),this._visualBounds}get sourceBounds(){return D("8.6.1","Sprite.sourceBounds is deprecated, use visualBounds instead."),this.visualBounds}updateBounds(){const t=this._anchor,e=this._texture,i=this._bounds,{width:n,height:r}=e.orig;i.minX=-t._x*n,i.maxX=i.minX+n,i.minY=-t._y*r,i.maxY=i.minY+r;}destroy(t=!1){if(super.destroy(t),typeof t=="boolean"?t:t?.texture){const i=typeof t=="boolean"?t:t?.textureSource;this._texture.destroy(i);}this._texture=null,this._visualBounds=null,this._bounds=null,this._anchor=null;}get anchor(){return this._anchor}set anchor(t){typeof t=="number"?this._anchor.set(t):this._anchor.copyFrom(t);}get width(){return Math.abs(this.scale.x)*this._texture.orig.width}set width(t){this._setWidth(t,this._texture.orig.width),this._width=t;}get height(){return Math.abs(this.scale.y)*this._texture.orig.height}set height(t){this._setHeight(t,this._texture.orig.height),this._height=t;}getSize(t){return t||(t={}),t.width=Math.abs(this.scale.x)*this._texture.orig.width,t.height=Math.abs(this.scale.y)*this._texture.orig.height,t}setSize(t,e){typeof t=="object"?(e=t.height??t.width,t=t.width):e??(e=t),t!==void 0&&this._setWidth(t,this._texture.orig.width),e!==void 0&&this._setHeight(e,this._texture.orig.height);}}const Il=new Ft;function Fr(s,t,e){const i=Il;s.measurable=!0,Sr(s,e,i),t.addBoundsMask(i),s.measurable=!1;}function Lr(s,t,e){const i=Xt.get();s.measurable=!0;const n=gt.get().identity(),r=Wr(s,e,n);Pr(s,i,r),s.measurable=!1,t.addBoundsMask(i),gt.return(n),Xt.return(i);}function Wr(s,t,e){return s?(s!==t&&(Wr(s.parent,t,e),s.updateLocalTransform(),e.append(s.localTransform)),e):(ot("Mask bounds, renderable is not inside the root container"),e)}class Ur{constructor(t){this.priority=0,this.inverse=!1,this.pipe="alphaMask",t?.mask&&this.init(t.mask);}init(t){this.mask=t,this.renderMaskToTexture=!(t instanceof H),this.mask.renderable=this.renderMaskToTexture,this.mask.includeInBuild=!this.renderMaskToTexture,this.mask.measurable=!1;}reset(){this.mask.measurable=!0,this.mask=null;}addBounds(t,e){this.inverse||Fr(this.mask,t,e);}addLocalBounds(t,e){Lr(this.mask,t,e);}containsPoint(t,e){const i=this.mask;return e(i,t)}destroy(){this.reset();}static test(t){return t instanceof H}}Ur.extension=T.MaskEffect;class Dr{constructor(t){this.priority=0,this.pipe="colorMask",t?.mask&&this.init(t.mask);}init(t){this.mask=t;}destroy(){}static test(t){return typeof t=="number"}}Dr.extension=T.MaskEffect;class Kr{constructor(t){this.priority=0,this.pipe="stencilMask",t?.mask&&this.init(t.mask);}init(t){this.mask=t,this.mask.includeInBuild=!1,this.mask.measurable=!1;}reset(){this.mask.measurable=!0,this.mask.includeInBuild=!0,this.mask=null;}addBounds(t,e){Fr(this.mask,t,e);}addLocalBounds(t,e){Lr(this.mask,t,e);}containsPoint(t,e){const i=this.mask;return e(i,t)}destroy(){this.reset();}static test(t){return t instanceof O}}Kr.extension=T.MaskEffect;const El={createCanvas:(s,t)=>{const e=document.createElement("canvas");return e.width=s,e.height=t,e},getCanvasRenderingContext2D:()=>CanvasRenderingContext2D,getWebGLRenderingContext:()=>WebGLRenderingContext,getNavigator:()=>navigator,getBaseUrl:()=>document.baseURI??window.location.href,getFontFaceSet:()=>document.fonts,fetch:(s,t)=>fetch(s,t),parseXML:s=>new DOMParser().parseFromString(s,"text/xml")};let In=El;const it={get(){return In},set(s){In=s;}};class Nr extends Wt{constructor(t){t.resource||(t.resource=it.get().createCanvas()),t.width||(t.width=t.resource.width,t.autoDensity||(t.width/=t.resolution)),t.height||(t.height=t.resource.height,t.autoDensity||(t.height/=t.resolution)),super(t),this.uploadMethodId="image",this.autoDensity=t.autoDensity,this.resizeCanvas(),this.transparent=!!t.transparent;}resizeCanvas(){this.autoDensity&&(this.resource.style.width=`${this.width}px`,this.resource.style.height=`${this.height}px`),(this.resource.width!==this.pixelWidth||this.resource.height!==this.pixelHeight)&&(this.resource.width=this.pixelWidth,this.resource.height=this.pixelHeight);}resize(t=this.width,e=this.height,i=this._resolution){const n=super.resize(t,e,i);return n&&this.resizeCanvas(),n}static test(t){return globalThis.HTMLCanvasElement&&t instanceof HTMLCanvasElement||globalThis.OffscreenCanvas&&t instanceof OffscreenCanvas}get context2D(){return this._context2D||(this._context2D=this.resource.getContext("2d"))}}Nr.extension=T.TextureSource;class we extends Wt{constructor(t){if(t.resource&&globalThis.HTMLImageElement&&t.resource instanceof HTMLImageElement){const e=it.get().createCanvas(t.resource.width,t.resource.height);e.getContext("2d").drawImage(t.resource,0,0,t.resource.width,t.resource.height),t.resource=e,ot("ImageSource: Image element passed, converting to canvas. Use CanvasSource instead.");}super(t),this.uploadMethodId="image",this.autoGarbageCollect=!0;}static test(t){return globalThis.HTMLImageElement&&t instanceof HTMLImageElement||typeof ImageBitmap<"u"&&t instanceof ImageBitmap||globalThis.VideoFrame&&t instanceof VideoFrame}}we.extension=T.TextureSource;var ps=(s=>(s[s.INTERACTION=50]="INTERACTION",s[s.HIGH=25]="HIGH",s[s.NORMAL=0]="NORMAL",s[s.LOW=-25]="LOW",s[s.UTILITY=-50]="UTILITY",s))(ps||{});class Di{constructor(t,e=null,i=0,n=!1){this.next=null,this.previous=null,this._destroyed=!1,this._fn=t,this._context=e,this.priority=i,this._once=n;}match(t,e=null){return this._fn===t&&this._context===e}emit(t){this._fn&&(this._context?this._fn.call(this._context,t):this._fn(t));const e=this.next;return this._once&&this.destroy(!0),this._destroyed&&(this.next=null),e}connect(t){this.previous=t,t.next&&(t.next.previous=this),this.next=t.next,t.next=this;}destroy(t=!1){this._destroyed=!0,this._fn=null,this._context=null,this.previous&&(this.previous.next=this.next),this.next&&(this.next.previous=this.previous);const e=this.next;return this.next=t?null:e,this.previous=null,e}}const Hr=class wt{constructor(){this.autoStart=!1,this.deltaTime=1,this.lastTime=-1,this.speed=1,this.started=!1,this._requestId=null,this._maxElapsedMS=100,this._minElapsedMS=0,this._protected=!1,this._lastFrame=-1,this._head=new Di(null,null,1/0),this.deltaMS=1/wt.targetFPMS,this.elapsedMS=1/wt.targetFPMS,this._tick=t=>{this._requestId=null,this.started&&(this.update(t),this.started&&this._requestId===null&&this._head.next&&(this._requestId=requestAnimationFrame(this._tick)));};}_requestIfNeeded(){this._requestId===null&&this._head.next&&(this.lastTime=performance.now(),this._lastFrame=this.lastTime,this._requestId=requestAnimationFrame(this._tick));}_cancelIfNeeded(){this._requestId!==null&&(cancelAnimationFrame(this._requestId),this._requestId=null);}_startIfPossible(){this.started?this._requestIfNeeded():this.autoStart&&this.start();}add(t,e,i=ps.NORMAL){return this._addListener(new Di(t,e,i))}addOnce(t,e,i=ps.NORMAL){return this._addListener(new Di(t,e,i,!0))}_addListener(t){let e=this._head.next,i=this._head;if(!e)t.connect(i);else {for(;e;){if(t.priority>e.priority){t.connect(i);break}i=e,e=e.next;}t.previous||t.connect(i);}return this._startIfPossible(),this}remove(t,e){let i=this._head.next;for(;i;)i.match(t,e)?i=i.destroy():i=i.next;return this._head.next||this._cancelIfNeeded(),this}get count(){if(!this._head)return 0;let t=0,e=this._head;for(;e=e.next;)t++;return t}start(){this.started||(this.started=!0,this._requestIfNeeded());}stop(){this.started&&(this.started=!1,this._cancelIfNeeded());}destroy(){if(!this._protected){this.stop();let t=this._head.next;for(;t;)t=t.destroy(!0);this._head.destroy(),this._head=null;}}update(t=performance.now()){let e;if(t>this.lastTime){if(e=this.elapsedMS=t-this.lastTime,e>this._maxElapsedMS&&(e=this._maxElapsedMS),e*=this.speed,this._minElapsedMS){const r=t-this._lastFrame|0;if(r<this._minElapsedMS)return;this._lastFrame=t-r%this._minElapsedMS;}this.deltaMS=e,this.deltaTime=this.deltaMS*wt.targetFPMS;const i=this._head;let n=i.next;for(;n;)n=n.emit(this);i.next||this._cancelIfNeeded();}else this.deltaTime=this.deltaMS=this.elapsedMS=0;this.lastTime=t;}get FPS(){return 1e3/this.elapsedMS}get minFPS(){return 1e3/this._maxElapsedMS}set minFPS(t){const e=Math.min(this.maxFPS,t),i=Math.min(Math.max(0,e)/1e3,wt.targetFPMS);this._maxElapsedMS=1/i;}get maxFPS(){return this._minElapsedMS?Math.round(1e3/this._minElapsedMS):0}set maxFPS(t){if(t===0)this._minElapsedMS=0;else {const e=Math.max(this.minFPS,t);this._minElapsedMS=1/(e/1e3);}}static get shared(){if(!wt._shared){const t=wt._shared=new wt;t.autoStart=!0,t._protected=!0;}return wt._shared}static get system(){if(!wt._system){const t=wt._system=new wt;t.autoStart=!0,t._protected=!0;}return wt._system}};Hr.targetFPMS=.06;let ti=Hr,Ki;async function jr(){return Ki??(Ki=(async()=>{const t=document.createElement("canvas").getContext("webgl");if(!t)return "premultiply-alpha-on-upload";const e=await new Promise(o=>{const a=document.createElement("video");a.onloadeddata=()=>o(a),a.onerror=()=>o(null),a.autoplay=!1,a.crossOrigin="anonymous",a.preload="auto",a.src="data:video/webm;base64,GkXfo59ChoEBQveBAULygQRC84EIQoKEd2VibUKHgQJChYECGFOAZwEAAAAAAAHTEU2bdLpNu4tTq4QVSalmU6yBoU27i1OrhBZUrmtTrIHGTbuMU6uEElTDZ1OsggEXTbuMU6uEHFO7a1OsggG97AEAAAAAAABZAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVSalmoCrXsYMPQkBNgIRMYXZmV0GETGF2ZkSJiEBEAAAAAAAAFlSua8yuAQAAAAAAAEPXgQFzxYgAAAAAAAAAAZyBACK1nIN1bmSIgQCGhVZfVlA5g4EBI+ODhAJiWgDglLCBArqBApqBAlPAgQFVsIRVuYEBElTDZ9Vzc9JjwItjxYgAAAAAAAAAAWfInEWjh0VOQ09ERVJEh49MYXZjIGxpYnZweC12cDlnyKJFo4hEVVJBVElPTkSHlDAwOjAwOjAwLjA0MDAwMDAwMAAAH0O2dcfngQCgwqGggQAAAIJJg0IAABAAFgA4JBwYSgAAICAAEb///4r+AAB1oZ2mm+6BAaWWgkmDQgAAEAAWADgkHBhKAAAgIABIQBxTu2uRu4+zgQC3iveBAfGCAXHwgQM=",a.load();});if(!e)return "premultiply-alpha-on-upload";const i=t.createTexture();t.bindTexture(t.TEXTURE_2D,i);const n=t.createFramebuffer();t.bindFramebuffer(t.FRAMEBUFFER,n),t.framebufferTexture2D(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0,t.TEXTURE_2D,i,0),t.pixelStorei(t.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),t.pixelStorei(t.UNPACK_COLORSPACE_CONVERSION_WEBGL,t.NONE),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,e);const r=new Uint8Array(4);return t.readPixels(0,0,1,1,t.RGBA,t.UNSIGNED_BYTE,r),t.deleteFramebuffer(n),t.deleteTexture(i),t.getExtension("WEBGL_lose_context")?.loseContext(),r[0]<=r[3]?"premultiplied-alpha":"premultiply-alpha-on-upload"})()),Ki}const bi=class Yr extends Wt{constructor(t){super(t),this.isReady=!1,this.uploadMethodId="video",t={...Yr.defaultOptions,...t},this._autoUpdate=!0,this._isConnectedToTicker=!1,this._updateFPS=t.updateFPS||0,this._msToNextUpdate=0,this.autoPlay=t.autoPlay!==!1,this.alphaMode=t.alphaMode??"premultiply-alpha-on-upload",this._videoFrameRequestCallback=this._videoFrameRequestCallback.bind(this),this._videoFrameRequestCallbackHandle=null,this._load=null,this._resolve=null,this._reject=null,this._onCanPlay=this._onCanPlay.bind(this),this._onCanPlayThrough=this._onCanPlayThrough.bind(this),this._onError=this._onError.bind(this),this._onPlayStart=this._onPlayStart.bind(this),this._onPlayStop=this._onPlayStop.bind(this),this._onSeeked=this._onSeeked.bind(this),t.autoLoad!==!1&&this.load();}updateFrame(){if(!this.destroyed){if(this._updateFPS){const t=ti.shared.elapsedMS*this.resource.playbackRate;this._msToNextUpdate=Math.floor(this._msToNextUpdate-t);}(!this._updateFPS||this._msToNextUpdate<=0)&&(this._msToNextUpdate=this._updateFPS?Math.floor(1e3/this._updateFPS):0),this.isValid&&this.update();}}_videoFrameRequestCallback(){this.updateFrame(),this.destroyed?this._videoFrameRequestCallbackHandle=null:this._videoFrameRequestCallbackHandle=this.resource.requestVideoFrameCallback(this._videoFrameRequestCallback);}get isValid(){return !!this.resource.videoWidth&&!!this.resource.videoHeight}async load(){if(this._load)return this._load;const t=this.resource,e=this.options;return (t.readyState===t.HAVE_ENOUGH_DATA||t.readyState===t.HAVE_FUTURE_DATA)&&t.width&&t.height&&(t.complete=!0),t.addEventListener("play",this._onPlayStart),t.addEventListener("pause",this._onPlayStop),t.addEventListener("seeked",this._onSeeked),this._isSourceReady()?this._mediaReady():(e.preload||t.addEventListener("canplay",this._onCanPlay),t.addEventListener("canplaythrough",this._onCanPlayThrough),t.addEventListener("error",this._onError,!0)),this.alphaMode=await jr(),this._load=new Promise((i,n)=>{this.isValid?i(this):(this._resolve=i,this._reject=n,e.preloadTimeoutMs!==void 0&&(this._preloadTimeout=setTimeout(()=>{this._onError(new ErrorEvent(`Preload exceeded timeout of ${e.preloadTimeoutMs}ms`));})),t.load());}),this._load}_onError(t){this.resource.removeEventListener("error",this._onError,!0),this.emit("error",t),this._reject&&(this._reject(t),this._reject=null,this._resolve=null);}_isSourcePlaying(){const t=this.resource;return !t.paused&&!t.ended}_isSourceReady(){return this.resource.readyState>2}_onPlayStart(){this.isValid||this._mediaReady(),this._configureAutoUpdate();}_onPlayStop(){this._configureAutoUpdate();}_onSeeked(){this._autoUpdate&&!this._isSourcePlaying()&&(this._msToNextUpdate=0,this.updateFrame(),this._msToNextUpdate=0);}_onCanPlay(){this.resource.removeEventListener("canplay",this._onCanPlay),this._mediaReady();}_onCanPlayThrough(){this.resource.removeEventListener("canplaythrough",this._onCanPlay),this._preloadTimeout&&(clearTimeout(this._preloadTimeout),this._preloadTimeout=void 0),this._mediaReady();}_mediaReady(){const t=this.resource;this.isValid&&(this.isReady=!0,this.resize(t.videoWidth,t.videoHeight)),this._msToNextUpdate=0,this.updateFrame(),this._msToNextUpdate=0,this._resolve&&(this._resolve(this),this._resolve=null,this._reject=null),this._isSourcePlaying()?this._onPlayStart():this.autoPlay&&this.resource.play();}destroy(){this._configureAutoUpdate();const t=this.resource;t&&(t.removeEventListener("play",this._onPlayStart),t.removeEventListener("pause",this._onPlayStop),t.removeEventListener("seeked",this._onSeeked),t.removeEventListener("canplay",this._onCanPlay),t.removeEventListener("canplaythrough",this._onCanPlayThrough),t.removeEventListener("error",this._onError,!0),t.pause(),t.src="",t.load()),super.destroy();}get autoUpdate(){return this._autoUpdate}set autoUpdate(t){t!==this._autoUpdate&&(this._autoUpdate=t,this._configureAutoUpdate());}get updateFPS(){return this._updateFPS}set updateFPS(t){t!==this._updateFPS&&(this._updateFPS=t,this._configureAutoUpdate());}_configureAutoUpdate(){this._autoUpdate&&this._isSourcePlaying()?!this._updateFPS&&this.resource.requestVideoFrameCallback?(this._isConnectedToTicker&&(ti.shared.remove(this.updateFrame,this),this._isConnectedToTicker=!1,this._msToNextUpdate=0),this._videoFrameRequestCallbackHandle===null&&(this._videoFrameRequestCallbackHandle=this.resource.requestVideoFrameCallback(this._videoFrameRequestCallback))):(this._videoFrameRequestCallbackHandle!==null&&(this.resource.cancelVideoFrameCallback(this._videoFrameRequestCallbackHandle),this._videoFrameRequestCallbackHandle=null),this._isConnectedToTicker||(ti.shared.add(this.updateFrame,this),this._isConnectedToTicker=!0,this._msToNextUpdate=0)):(this._videoFrameRequestCallbackHandle!==null&&(this.resource.cancelVideoFrameCallback(this._videoFrameRequestCallbackHandle),this._videoFrameRequestCallbackHandle=null),this._isConnectedToTicker&&(ti.shared.remove(this.updateFrame,this),this._isConnectedToTicker=!1,this._msToNextUpdate=0));}static test(t){return globalThis.HTMLVideoElement&&t instanceof HTMLVideoElement}};bi.extension=T.TextureSource;bi.defaultOptions={...Wt.defaultOptions,autoLoad:!0,autoPlay:!0,updateFPS:0,crossorigin:!0,loop:!1,muted:!0,playsinline:!0,preload:!1};bi.MIME_TYPES={ogv:"video/ogg",mov:"video/quicktime",m4v:"video/mp4"};let ui=bi;const Ot=(s,t,e=!1)=>(Array.isArray(s)||(s=[s]),t?s.map(i=>typeof i=="string"||e?t(i):i):s);class Ml{constructor(){this._parsers=[],this._cache=new Map,this._cacheMap=new Map;}reset(){this._cacheMap.clear(),this._cache.clear();}has(t){return this._cache.has(t)}get(t){const e=this._cache.get(t);return e||ot(`[Assets] Asset id ${t} was not found in the Cache`),e}set(t,e){const i=Ot(t);let n;for(let l=0;l<this.parsers.length;l++){const c=this.parsers[l];if(c.test(e)){n=c.getCacheableAssets(i,e);break}}const r=new Map(Object.entries(n||{}));n||i.forEach(l=>{r.set(l,e);});const o=[...r.keys()],a={cacheKeys:o,keys:i};i.forEach(l=>{this._cacheMap.set(l,a);}),o.forEach(l=>{const c=n?n[l]:e;this._cache.has(l)&&this._cache.get(l)!==c&&ot("[Cache] already has key:",l),this._cache.set(l,r.get(l));});}remove(t){if(!this._cacheMap.has(t)){ot(`[Assets] Asset id ${t} was not found in the Cache`);return}const e=this._cacheMap.get(t);e.cacheKeys.forEach(n=>{this._cache.delete(n);}),e.keys.forEach(n=>{this._cacheMap.delete(n);});}get parsers(){return this._parsers}}const et=new Ml,ms=[];yt.handleByList(T.TextureSource,ms);function Xr(s={}){const t=s&&s.resource,e=t?s.resource:s,i=t?s:{resource:s};for(let n=0;n<ms.length;n++){const r=ms[n];if(r.test(e))return new r(i)}throw new Error(`Could not find a source type for resource: ${i.resource}`)}function Sl(s={},t=!1){const e=s&&s.resource,i=e?s.resource:s,n=e?s:{resource:s};if(!t&&et.has(i))return et.get(i);const r=new W({source:Xr(n)});return r.on("destroy",()=>{et.has(i)&&et.remove(i);}),t||et.set(i,r),r}function Bl(s,t=!1){return typeof s=="string"?et.get(s):s instanceof Wt?new W({source:s}):Sl(s,t)}W.from=Bl;Wt.from=Xr;yt.add(Ur,Dr,Kr,ui,we,Nr,Ns);var Zt=(s=>(s[s.Low=0]="Low",s[s.Normal=1]="Normal",s[s.High=2]="High",s))(Zt||{});function Gt(s){if(typeof s!="string")throw new TypeError(`Path must be a string. Received ${JSON.stringify(s)}`)}function Pe(s){return s.split("?")[0].split("#")[0]}function zl(s){return s.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function Pl(s,t,e){return s.replace(new RegExp(zl(t),"g"),e)}function Tl(s,t){let e="",i=0,n=-1,r=0,o=-1;for(let a=0;a<=s.length;++a){if(a<s.length)o=s.charCodeAt(a);else {if(o===47)break;o=47;}if(o===47){if(!(n===a-1||r===1))if(n!==a-1&&r===2){if(e.length<2||i!==2||e.charCodeAt(e.length-1)!==46||e.charCodeAt(e.length-2)!==46){if(e.length>2){const l=e.lastIndexOf("/");if(l!==e.length-1){l===-1?(e="",i=0):(e=e.slice(0,l),i=e.length-1-e.lastIndexOf("/")),n=a,r=0;continue}}else if(e.length===2||e.length===1){e="",i=0,n=a,r=0;continue}}}else e.length>0?e+=`/${s.slice(n+1,a)}`:e=s.slice(n+1,a),i=a-n-1;n=a,r=0;}else o===46&&r!==-1?++r:r=-1;}return e}const Mt={toPosix(s){return Pl(s,"\\","/")},isUrl(s){return /^https?:/.test(this.toPosix(s))},isDataUrl(s){return /^data:([a-z]+\/[a-z0-9-+.]+(;[a-z0-9-.!#$%*+.{}|~`]+=[a-z0-9-.!#$%*+.{}()_|~`]+)*)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s<>]*?)$/i.test(s)},isBlobUrl(s){return s.startsWith("blob:")},hasProtocol(s){return /^[^/:]+:/.test(this.toPosix(s))},getProtocol(s){Gt(s),s=this.toPosix(s);const t=/^file:\/\/\//.exec(s);if(t)return t[0];const e=/^[^/:]+:\/{0,2}/.exec(s);return e?e[0]:""},toAbsolute(s,t,e){if(Gt(s),this.isDataUrl(s)||this.isBlobUrl(s))return s;const i=Pe(this.toPosix(t??it.get().getBaseUrl())),n=Pe(this.toPosix(e??this.rootname(i)));return s=this.toPosix(s),s.startsWith("/")?Mt.join(n,s.slice(1)):this.isAbsolute(s)?s:this.join(i,s)},normalize(s){if(Gt(s),s.length===0)return ".";if(this.isDataUrl(s)||this.isBlobUrl(s))return s;s=this.toPosix(s);let t="";const e=s.startsWith("/");this.hasProtocol(s)&&(t=this.rootname(s),s=s.slice(t.length));const i=s.endsWith("/");return s=Tl(s),s.length>0&&i&&(s+="/"),e?`/${s}`:t+s},isAbsolute(s){return Gt(s),s=this.toPosix(s),this.hasProtocol(s)?!0:s.startsWith("/")},join(...s){if(s.length===0)return ".";let t;for(let e=0;e<s.length;++e){const i=s[e];if(Gt(i),i.length>0)if(t===void 0)t=i;else {const n=s[e-1]??"";this.joinExtensions.includes(this.extname(n).toLowerCase())?t+=`/../${i}`:t+=`/${i}`;}}return t===void 0?".":this.normalize(t)},dirname(s){if(Gt(s),s.length===0)return ".";s=this.toPosix(s);let t=s.charCodeAt(0);const e=t===47;let i=-1,n=!0;const r=this.getProtocol(s),o=s;s=s.slice(r.length);for(let a=s.length-1;a>=1;--a)if(t=s.charCodeAt(a),t===47){if(!n){i=a;break}}else n=!1;return i===-1?e?"/":this.isUrl(o)?r+s:r:e&&i===1?"//":r+s.slice(0,i)},rootname(s){Gt(s),s=this.toPosix(s);let t="";if(s.startsWith("/")?t="/":t=this.getProtocol(s),this.isUrl(s)){const e=s.indexOf("/",t.length);e!==-1?t=s.slice(0,e):t=s,t.endsWith("/")||(t+="/");}return t},basename(s,t){Gt(s),t&&Gt(t),s=Pe(this.toPosix(s));let e=0,i=-1,n=!0,r;if(t!==void 0&&t.length>0&&t.length<=s.length){if(t.length===s.length&&t===s)return "";let o=t.length-1,a=-1;for(r=s.length-1;r>=0;--r){const l=s.charCodeAt(r);if(l===47){if(!n){e=r+1;break}}else a===-1&&(n=!1,a=r+1),o>=0&&(l===t.charCodeAt(o)?--o===-1&&(i=r):(o=-1,i=a));}return e===i?i=a:i===-1&&(i=s.length),s.slice(e,i)}for(r=s.length-1;r>=0;--r)if(s.charCodeAt(r)===47){if(!n){e=r+1;break}}else i===-1&&(n=!1,i=r+1);return i===-1?"":s.slice(e,i)},extname(s){Gt(s),s=Pe(this.toPosix(s));let t=-1,e=0,i=-1,n=!0,r=0;for(let o=s.length-1;o>=0;--o){const a=s.charCodeAt(o);if(a===47){if(!n){e=o+1;break}continue}i===-1&&(n=!1,i=o+1),a===46?t===-1?t=o:r!==1&&(r=1):t!==-1&&(r=-1);}return t===-1||i===-1||r===0||r===1&&t===i-1&&t===e+1?"":s.slice(t,i)},parse(s){Gt(s);const t={root:"",dir:"",base:"",ext:"",name:""};if(s.length===0)return t;s=Pe(this.toPosix(s));let e=s.charCodeAt(0);const i=this.isAbsolute(s);let n;t.root=this.rootname(s),i||this.hasProtocol(s)?n=1:n=0;let r=-1,o=0,a=-1,l=!0,c=s.length-1,h=0;for(;c>=n;--c){if(e=s.charCodeAt(c),e===47){if(!l){o=c+1;break}continue}a===-1&&(l=!1,a=c+1),e===46?r===-1?r=c:h!==1&&(h=1):r!==-1&&(h=-1);}return r===-1||a===-1||h===0||h===1&&r===a-1&&r===o+1?a!==-1&&(o===0&&i?t.base=t.name=s.slice(1,a):t.base=t.name=s.slice(o,a)):(o===0&&i?(t.name=s.slice(1,r),t.base=s.slice(1,a)):(t.name=s.slice(o,r),t.base=s.slice(o,a)),t.ext=s.slice(r,a)),t.dir=this.dirname(s),t},sep:"/",delimiter:":",joinExtensions:[".html"]};function Vr(s,t,e,i,n){const r=t[e];for(let o=0;o<r.length;o++){const a=r[o];e<t.length-1?Vr(s.replace(i[e],a),t,e+1,i,n):n.push(s.replace(i[e],a));}}function Rl(s){const t=/\{(.*?)\}/g,e=s.match(t),i=[];if(e){const n=[];e.forEach(r=>{const o=r.substring(1,r.length-1).split(",");n.push(o);}),Vr(s,n,0,e,i);}else i.push(s);return i}const yi=s=>!Array.isArray(s);class ke{constructor(){this._defaultBundleIdentifierOptions={connector:"-",createBundleAssetId:(t,e)=>`${t}${this._bundleIdConnector}${e}`,extractAssetIdFromBundle:(t,e)=>e.replace(`${t}${this._bundleIdConnector}`,"")},this._bundleIdConnector=this._defaultBundleIdentifierOptions.connector,this._createBundleAssetId=this._defaultBundleIdentifierOptions.createBundleAssetId,this._extractAssetIdFromBundle=this._defaultBundleIdentifierOptions.extractAssetIdFromBundle,this._assetMap={},this._preferredOrder=[],this._parsers=[],this._resolverHash={},this._bundles={};}setBundleIdentifier(t){if(this._bundleIdConnector=t.connector??this._bundleIdConnector,this._createBundleAssetId=t.createBundleAssetId??this._createBundleAssetId,this._extractAssetIdFromBundle=t.extractAssetIdFromBundle??this._extractAssetIdFromBundle,this._extractAssetIdFromBundle("foo",this._createBundleAssetId("foo","bar"))!=="bar")throw new Error("[Resolver] GenerateBundleAssetId are not working correctly")}prefer(...t){t.forEach(e=>{this._preferredOrder.push(e),e.priority||(e.priority=Object.keys(e.params));}),this._resolverHash={};}set basePath(t){this._basePath=t;}get basePath(){return this._basePath}set rootPath(t){this._rootPath=t;}get rootPath(){return this._rootPath}get parsers(){return this._parsers}reset(){this.setBundleIdentifier(this._defaultBundleIdentifierOptions),this._assetMap={},this._preferredOrder=[],this._resolverHash={},this._rootPath=null,this._basePath=null,this._manifest=null,this._bundles={},this._defaultSearchParams=null;}setDefaultSearchParams(t){if(typeof t=="string")this._defaultSearchParams=t;else {const e=t;this._defaultSearchParams=Object.keys(e).map(i=>`${encodeURIComponent(i)}=${encodeURIComponent(e[i])}`).join("&");}}getAlias(t){const{alias:e,src:i}=t;return Ot(e||i,r=>typeof r=="string"?r:Array.isArray(r)?r.map(o=>o?.src??o):r?.src?r.src:r,!0)}addManifest(t){this._manifest&&ot("[Resolver] Manifest already exists, this will be overwritten"),this._manifest=t,t.bundles.forEach(e=>{this.addBundle(e.name,e.assets);});}addBundle(t,e){const i=[];let n=e;Array.isArray(e)||(n=Object.entries(e).map(([r,o])=>typeof o=="string"||Array.isArray(o)?{alias:r,src:o}:{alias:r,...o})),n.forEach(r=>{const o=r.src,a=r.alias;let l;if(typeof a=="string"){const c=this._createBundleAssetId(t,a);i.push(c),l=[a,c];}else {const c=a.map(h=>this._createBundleAssetId(t,h));i.push(...c),l=[...a,...c];}this.add({...r,alias:l,src:o});}),this._bundles[t]=i;}add(t){const e=[];Array.isArray(t)?e.push(...t):e.push(t);let i;i=r=>{this.hasKey(r)&&ot(`[Resolver] already has key: ${r} overwriting`);},Ot(e).forEach(r=>{const{src:o}=r;let{data:a,format:l,loadParser:c}=r;const h=Ot(o).map(u=>typeof u=="string"?Rl(u):Array.isArray(u)?u:[u]),d=this.getAlias(r);Array.isArray(d)?d.forEach(i):i(d);const _=[];h.forEach(u=>{u.forEach(f=>{let p={};if(typeof f!="object"){p.src=f;for(let A=0;A<this._parsers.length;A++){const m=this._parsers[A];if(m.test(f)){p=m.parse(f);break}}}else a=f.data??a,l=f.format??l,c=f.loadParser??c,p={...p,...f};if(!d)throw new Error(`[Resolver] alias is undefined for this asset: ${p.src}`);p=this._buildResolvedAsset(p,{aliases:d,data:a,format:l,loadParser:c}),_.push(p);});}),d.forEach(u=>{this._assetMap[u]=_;});});}resolveBundle(t){const e=yi(t);t=Ot(t);const i={};return t.forEach(n=>{const r=this._bundles[n];if(r){const o=this.resolve(r),a={};for(const l in o){const c=o[l];a[this._extractAssetIdFromBundle(n,l)]=c;}i[n]=a;}}),e?i[t[0]]:i}resolveUrl(t){const e=this.resolve(t);if(typeof t!="string"){const i={};for(const n in e)i[n]=e[n].src;return i}return e.src}resolve(t){const e=yi(t);t=Ot(t);const i={};return t.forEach(n=>{if(!this._resolverHash[n])if(this._assetMap[n]){let r=this._assetMap[n];const o=this._getPreferredOrder(r);o?.priority.forEach(a=>{o.params[a].forEach(l=>{const c=r.filter(h=>h[a]?h[a]===l:!1);c.length&&(r=c);});}),this._resolverHash[n]=r[0];}else this._resolverHash[n]=this._buildResolvedAsset({alias:[n],src:n},{});i[n]=this._resolverHash[n];}),e?i[t[0]]:i}hasKey(t){return !!this._assetMap[t]}hasBundle(t){return !!this._bundles[t]}_getPreferredOrder(t){for(let e=0;e<t.length;e++){const i=t[e],n=this._preferredOrder.find(r=>r.params.format.includes(i.format));if(n)return n}return this._preferredOrder[0]}_appendDefaultSearchParams(t){if(!this._defaultSearchParams)return t;const e=/\?/.test(t)?"&":"?";return `${t}${e}${this._defaultSearchParams}`}_buildResolvedAsset(t,e){const{aliases:i,data:n,loadParser:r,format:o}=e;return (this._basePath||this._rootPath)&&(t.src=Mt.toAbsolute(t.src,this._basePath,this._rootPath)),t.alias=i??t.alias??[t.src],t.src=this._appendDefaultSearchParams(t.src),t.data={...n||{},...t.data},t.loadParser=r??t.loadParser,t.format=o??t.format??Gl(t.src),t}}ke.RETINA_PREFIX=/@([0-9\.]+)x/;function Gl(s){return s.split(".").pop().split("?").shift().split("#").shift()}const gs=(s,t)=>{const e=t.split("?")[1];return e&&(s+=`?${e}`),s},qr=class Fe{constructor(t,e){this.linkedSheets=[],this._texture=t instanceof W?t:null,this.textureSource=t.source,this.textures={},this.animations={},this.data=e;const i=parseFloat(e.meta.scale);i?(this.resolution=i,t.source.resolution=this.resolution):this.resolution=t.source._resolution,this._frames=this.data.frames,this._frameKeys=Object.keys(this._frames),this._batchIndex=0,this._callback=null;}parse(){return new Promise(t=>{this._callback=t,this._batchIndex=0,this._frameKeys.length<=Fe.BATCH_SIZE?(this._processFrames(0),this._processAnimations(),this._parseComplete()):this._nextBatch();})}_processFrames(t){let e=t;const i=Fe.BATCH_SIZE;for(;e-t<i&&e<this._frameKeys.length;){const n=this._frameKeys[e],r=this._frames[n],o=r.frame;if(o){let a=null,l=null;const c=r.trimmed!==!1&&r.sourceSize?r.sourceSize:r.frame,h=new Q(0,0,Math.floor(c.w)/this.resolution,Math.floor(c.h)/this.resolution);r.rotated?a=new Q(Math.floor(o.x)/this.resolution,Math.floor(o.y)/this.resolution,Math.floor(o.h)/this.resolution,Math.floor(o.w)/this.resolution):a=new Q(Math.floor(o.x)/this.resolution,Math.floor(o.y)/this.resolution,Math.floor(o.w)/this.resolution,Math.floor(o.h)/this.resolution),r.trimmed!==!1&&r.spriteSourceSize&&(l=new Q(Math.floor(r.spriteSourceSize.x)/this.resolution,Math.floor(r.spriteSourceSize.y)/this.resolution,Math.floor(o.w)/this.resolution,Math.floor(o.h)/this.resolution)),this.textures[n]=new W({source:this.textureSource,frame:a,orig:h,trim:l,rotate:r.rotated?2:0,defaultAnchor:r.anchor,defaultBorders:r.borders,label:n.toString()});}e++;}}_processAnimations(){const t=this.data.animations||{};for(const e in t){this.animations[e]=[];for(let i=0;i<t[e].length;i++){const n=t[e][i];this.animations[e].push(this.textures[n]);}}}_parseComplete(){const t=this._callback;this._callback=null,this._batchIndex=0,t.call(this,this.textures);}_nextBatch(){this._processFrames(this._batchIndex*Fe.BATCH_SIZE),this._batchIndex++,setTimeout(()=>{this._batchIndex*Fe.BATCH_SIZE<this._frameKeys.length?this._nextBatch():(this._processAnimations(),this._parseComplete());},0);}destroy(t=!1){for(const e in this.textures)this.textures[e].destroy();this._frames=null,this._frameKeys=null,this.data=null,this.textures=null,t&&(this._texture?.destroy(),this.textureSource.destroy()),this._texture=null,this.textureSource=null,this.linkedSheets=[];}};qr.BATCH_SIZE=1e3;let En=qr;const Ol=["jpg","png","jpeg","avif","webp","basis","etc2","bc7","bc6h","bc5","bc4","bc3","bc2","bc1","eac","astc"];function Qr(s,t,e){const i={};if(s.forEach(n=>{i[n]=t;}),Object.keys(t.textures).forEach(n=>{i[n]=t.textures[n];}),!e){const n=Mt.dirname(s[0]);t.linkedSheets.forEach((r,o)=>{const a=Qr([`${n}/${t.data.meta.related_multi_packs[o]}`],r,!0);Object.assign(i,a);});}return i}const Fl={extension:T.Asset,cache:{test:s=>s instanceof En,getCacheableAssets:(s,t)=>Qr(s,t,!1)},resolver:{extension:{type:T.ResolveParser,name:"resolveSpritesheet"},test:s=>{const e=s.split("?")[0].split("."),i=e.pop(),n=e.pop();return i==="json"&&Ol.includes(n)},parse:s=>{const t=s.split(".");return {resolution:parseFloat(ke.RETINA_PREFIX.exec(s)?.[1]??"1"),format:t[t.length-2],src:s}}},loader:{name:"spritesheetLoader",extension:{type:T.LoadParser,priority:Zt.Normal,name:"spritesheetLoader"},async testParse(s,t){return Mt.extname(t.src).toLowerCase()===".json"&&!!s.frames},async parse(s,t,e){const{texture:i,imageFilename:n,textureOptions:r}=t?.data??{};let o=Mt.dirname(t.src);o&&o.lastIndexOf("/")!==o.length-1&&(o+="/");let a;if(i instanceof W)a=i;else {const h=gs(o+(n??s.meta.image),t.src);a=(await e.load([{src:h,data:r}]))[h];}const l=new En(a.source,s);await l.parse();const c=s?.meta?.related_multi_packs;if(Array.isArray(c)){const h=[];for(const _ of c){if(typeof _!="string")continue;let u=o+_;t.data?.ignoreMultiPack||(u=gs(u,t.src),h.push(e.load({src:u,data:{ignoreMultiPack:!0}})));}const d=await Promise.all(h);l.linkedSheets=d,d.forEach(_=>{_.linkedSheets=[l].concat(l.linkedSheets.filter(u=>u!==_));});}return l},async unload(s,t,e){await e.unload(s.textureSource._sourceOrigin),s.destroy(!1);}}};yt.add(Fl);const Ni=Object.create(null),Mn=Object.create(null);function Ys(s,t){let e=Mn[s];return e===void 0&&(Ni[t]===void 0&&(Ni[t]=1),Mn[s]=e=Ni[t]++),e}let ei;function Zr(){return (!ei||ei?.isContextLost())&&(ei=it.get().createCanvas().getContext("webgl",{})),ei}let ii;function Ll(){if(!ii){ii="mediump";const s=Zr();s&&s.getShaderPrecisionFormat&&(ii=s.getShaderPrecisionFormat(s.FRAGMENT_SHADER,s.HIGH_FLOAT).precision?"highp":"mediump");}return ii}function Wl(s,t,e){return t?s:e?(s=s.replace("out vec4 finalColor;",""),`
        
        #ifdef GL_ES // This checks if it is WebGL1
        #define in varying
        #define finalColor gl_FragColor
        #define texture texture2D
        #endif
        ${s}
        `):`
        
        #ifdef GL_ES // This checks if it is WebGL1
        #define in attribute
        #define out varying
        #endif
        ${s}
        `}function Ul(s,t,e){const i=e?t.maxSupportedFragmentPrecision:t.maxSupportedVertexPrecision;if(s.substring(0,9)!=="precision"){let n=e?t.requestedFragmentPrecision:t.requestedVertexPrecision;return n==="highp"&&i!=="highp"&&(n="mediump"),`precision ${n} float;
${s}`}else if(i!=="highp"&&s.substring(0,15)==="precision highp")return s.replace("precision highp","precision mediump");return s}function Dl(s,t){return t?`#version 300 es
${s}`:s}const Kl={},Nl={};function Hl(s,{name:t="pixi-program"},e=!0){t=t.replace(/\s+/g,"-"),t+=e?"-fragment":"-vertex";const i=e?Kl:Nl;return i[t]?(i[t]++,t+=`-${i[t]}`):i[t]=1,s.indexOf("#define SHADER_NAME")!==-1?s:`${`#define SHADER_NAME ${t}`}
${s}`}function jl(s,t){return t?s.replace("#version 300 es",""):s}const Hi={stripVersion:jl,ensurePrecision:Ul,addProgramDefines:Wl,setProgramName:Hl,insertVersion:Dl},ji=Object.create(null),Jr=class ys{constructor(t){t={...ys.defaultOptions,...t};const e=t.fragment.indexOf("#version 300 es")!==-1,i={stripVersion:e,ensurePrecision:{requestedFragmentPrecision:t.preferredFragmentPrecision,requestedVertexPrecision:t.preferredVertexPrecision,maxSupportedVertexPrecision:"highp",maxSupportedFragmentPrecision:Ll()},setProgramName:{name:t.name},addProgramDefines:e,insertVersion:e};let n=t.fragment,r=t.vertex;Object.keys(Hi).forEach(o=>{const a=i[o];n=Hi[o](n,a,!0),r=Hi[o](r,a,!1);}),this.fragment=n,this.vertex=r,this.transformFeedbackVaryings=t.transformFeedbackVaryings,this._key=Ys(`${this.vertex}:${this.fragment}`,"gl-program");}destroy(){this.fragment=null,this.vertex=null,this._attributeData=null,this._uniformData=null,this._uniformBlockData=null,this.transformFeedbackVaryings=null;}static from(t){const e=`${t.vertex}:${t.fragment}`;return ji[e]||(ji[e]=new ys(t)),ji[e]}};Jr.defaultOptions={preferredVertexPrecision:"highp",preferredFragmentPrecision:"mediump"};let Ce=Jr;const Sn={uint8x2:{size:2,stride:2,normalised:!1},uint8x4:{size:4,stride:4,normalised:!1},sint8x2:{size:2,stride:2,normalised:!1},sint8x4:{size:4,stride:4,normalised:!1},unorm8x2:{size:2,stride:2,normalised:!0},unorm8x4:{size:4,stride:4,normalised:!0},snorm8x2:{size:2,stride:2,normalised:!0},snorm8x4:{size:4,stride:4,normalised:!0},uint16x2:{size:2,stride:4,normalised:!1},uint16x4:{size:4,stride:8,normalised:!1},sint16x2:{size:2,stride:4,normalised:!1},sint16x4:{size:4,stride:8,normalised:!1},unorm16x2:{size:2,stride:4,normalised:!0},unorm16x4:{size:4,stride:8,normalised:!0},snorm16x2:{size:2,stride:4,normalised:!0},snorm16x4:{size:4,stride:8,normalised:!0},float16x2:{size:2,stride:4,normalised:!1},float16x4:{size:4,stride:8,normalised:!1},float32:{size:1,stride:4,normalised:!1},float32x2:{size:2,stride:8,normalised:!1},float32x3:{size:3,stride:12,normalised:!1},float32x4:{size:4,stride:16,normalised:!1},uint32:{size:1,stride:4,normalised:!1},uint32x2:{size:2,stride:8,normalised:!1},uint32x3:{size:3,stride:12,normalised:!1},uint32x4:{size:4,stride:16,normalised:!1},sint32:{size:1,stride:4,normalised:!1},sint32x2:{size:2,stride:8,normalised:!1},sint32x3:{size:3,stride:12,normalised:!1},sint32x4:{size:4,stride:16,normalised:!1}};function Yl(s){return Sn[s]??Sn.float32}const Xl={f32:"float32","vec2<f32>":"float32x2","vec3<f32>":"float32x3","vec4<f32>":"float32x4",vec2f:"float32x2",vec3f:"float32x3",vec4f:"float32x4",i32:"sint32","vec2<i32>":"sint32x2","vec3<i32>":"sint32x3","vec4<i32>":"sint32x4",u32:"uint32","vec2<u32>":"uint32x2","vec3<u32>":"uint32x3","vec4<u32>":"uint32x4",bool:"uint32","vec2<bool>":"uint32x2","vec3<bool>":"uint32x3","vec4<bool>":"uint32x4"};function Vl({source:s,entryPoint:t}){const e={},i=s.indexOf(`fn ${t}`);if(i!==-1){const n=s.indexOf("->",i);if(n!==-1){const r=s.substring(i,n),o=/@location\((\d+)\)\s+([a-zA-Z0-9_]+)\s*:\s*([a-zA-Z0-9_<>]+)(?:,|\s|$)/g;let a;for(;(a=o.exec(r))!==null;){const l=Xl[a[3]]??"float32";e[a[2]]={location:parseInt(a[1],10),format:l,stride:Yl(l).stride,offset:0,instance:!1,start:0};}}}return e}function Yi(s){const t=/(^|[^/])@(group|binding)\(\d+\)[^;]+;/g,e=/@group\((\d+)\)/,i=/@binding\((\d+)\)/,n=/var(<[^>]+>)? (\w+)/,r=/:\s*(\w+)/,o=/struct\s+(\w+)\s*{([^}]+)}/g,a=/(\w+)\s*:\s*([\w\<\>]+)/g,l=/struct\s+(\w+)/,c=s.match(t)?.map(d=>({group:parseInt(d.match(e)[1],10),binding:parseInt(d.match(i)[1],10),name:d.match(n)[2],isUniform:d.match(n)[1]==="<uniform>",type:d.match(r)[1]}));if(!c)return {groups:[],structs:[]};const h=s.match(o)?.map(d=>{const _=d.match(l)[1],u=d.match(a).reduce((f,p)=>{const[A,m]=p.split(":");return f[A.trim()]=m.trim(),f},{});return u?{name:_,members:u}:null}).filter(({name:d})=>c.some(_=>_.type===d))??[];return {groups:c,structs:h}}var Le=(s=>(s[s.VERTEX=1]="VERTEX",s[s.FRAGMENT=2]="FRAGMENT",s[s.COMPUTE=4]="COMPUTE",s))(Le||{});function ql({groups:s}){const t=[];for(let e=0;e<s.length;e++){const i=s[e];t[i.group]||(t[i.group]=[]),i.isUniform?t[i.group].push({binding:i.binding,visibility:Le.VERTEX|Le.FRAGMENT,buffer:{type:"uniform"}}):i.type==="sampler"?t[i.group].push({binding:i.binding,visibility:Le.FRAGMENT,sampler:{type:"filtering"}}):i.type==="texture_2d"&&t[i.group].push({binding:i.binding,visibility:Le.FRAGMENT,texture:{sampleType:"float",viewDimension:"2d",multisampled:!1}});}return t}function Ql({groups:s}){const t=[];for(let e=0;e<s.length;e++){const i=s[e];t[i.group]||(t[i.group]={}),t[i.group][i.name]=i.binding;}return t}function Zl(s,t){const e=new Set,i=new Set,n=[...s.structs,...t.structs].filter(o=>e.has(o.name)?!1:(e.add(o.name),!0)),r=[...s.groups,...t.groups].filter(o=>{const a=`${o.name}-${o.binding}`;return i.has(a)?!1:(i.add(a),!0)});return {structs:n,groups:r}}const Xi=Object.create(null);class Qt{constructor(t){this._layoutKey=0,this._attributeLocationsKey=0;const{fragment:e,vertex:i,layout:n,gpuLayout:r,name:o}=t;if(this.name=o,this.fragment=e,this.vertex=i,e.source===i.source){const a=Yi(e.source);this.structsAndGroups=a;}else {const a=Yi(i.source),l=Yi(e.source);this.structsAndGroups=Zl(a,l);}this.layout=n??Ql(this.structsAndGroups),this.gpuLayout=r??ql(this.structsAndGroups),this.autoAssignGlobalUniforms=this.layout[0]?.globalUniforms!==void 0,this.autoAssignLocalUniforms=this.layout[1]?.localUniforms!==void 0,this._generateProgramKey();}_generateProgramKey(){const{vertex:t,fragment:e}=this,i=t.source+e.source+t.entryPoint+e.entryPoint;this._layoutKey=Ys(i,"program");}get attributeData(){return this._attributeData??(this._attributeData=Vl(this.vertex)),this._attributeData}destroy(){this.gpuLayout=null,this.layout=null,this.structsAndGroups=null,this.fragment=null,this.vertex=null;}static from(t){const e=`${t.vertex.source}:${t.fragment.source}:${t.fragment.entryPoint}:${t.vertex.entryPoint}`;return Xi[e]||(Xi[e]=new Qt(t)),Xi[e]}}const $r=["f32","i32","vec2<f32>","vec3<f32>","vec4<f32>","mat2x2<f32>","mat3x3<f32>","mat4x4<f32>","mat3x2<f32>","mat4x2<f32>","mat2x3<f32>","mat4x3<f32>","mat2x4<f32>","mat3x4<f32>","vec2<i32>","vec3<i32>","vec4<i32>"],Jl=$r.reduce((s,t)=>(s[t]=!0,s),{});function $l(s,t){switch(s){case"f32":return 0;case"vec2<f32>":return new Float32Array(2*t);case"vec3<f32>":return new Float32Array(3*t);case"vec4<f32>":return new Float32Array(4*t);case"mat2x2<f32>":return new Float32Array([1,0,0,1]);case"mat3x3<f32>":return new Float32Array([1,0,0,0,1,0,0,0,1]);case"mat4x4<f32>":return new Float32Array([1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1])}return null}const to=class eo{constructor(t,e){this._touched=0,this.uid=rt("uniform"),this._resourceType="uniformGroup",this._resourceId=rt("resource"),this.isUniformGroup=!0,this._dirtyId=0,this.destroyed=!1,e={...eo.defaultOptions,...e},this.uniformStructures=t;const i={};for(const n in t){const r=t[n];if(r.name=n,r.size=r.size??1,!Jl[r.type])throw new Error(`Uniform type ${r.type} is not supported. Supported uniform types are: ${$r.join(", ")}`);r.value??(r.value=$l(r.type,r.size)),i[n]=r.value;}this.uniforms=i,this._dirtyId=1,this.ubo=e.ubo,this.isStatic=e.isStatic,this._signature=Ys(Object.keys(i).map(n=>`${n}-${t[n].type}`).join("-"),"uniform-group");}update(){this._dirtyId++;}};to.defaultOptions={ubo:!1,isStatic:!1};let io=to;class _i{constructor(t){this.resources=Object.create(null),this._dirty=!0;let e=0;for(const i in t){const n=t[i];this.setResource(n,e++);}this._updateKey();}_updateKey(){if(!this._dirty)return;this._dirty=!1;const t=[];let e=0;for(const i in this.resources)t[e++]=this.resources[i]._resourceId;this._key=t.join("|");}setResource(t,e){const i=this.resources[e];t!==i&&(i&&t.off?.("change",this.onResourceChange,this),t.on?.("change",this.onResourceChange,this),this.resources[e]=t,this._dirty=!0);}getResource(t){return this.resources[t]}_touch(t){const e=this.resources;for(const i in e)e[i]._touched=t;}destroy(){const t=this.resources;for(const e in t)t[e].off?.("change",this.onResourceChange,this);this.resources=null;}onResourceChange(t){if(this._dirty=!0,t.destroyed){const e=this.resources;for(const i in e)e[i]===t&&(e[i]=null);}else this._updateKey();}}var xs=(s=>(s[s.WEBGL=1]="WEBGL",s[s.WEBGPU=2]="WEBGPU",s[s.BOTH=3]="BOTH",s))(xs||{});class Ci extends Lt{constructor(t){super(),this.uid=rt("shader"),this._uniformBindMap=Object.create(null),this._ownedBindGroups=[];let{gpuProgram:e,glProgram:i,groups:n,resources:r,compatibleRenderers:o,groupMap:a}=t;this.gpuProgram=e,this.glProgram=i,o===void 0&&(o=0,e&&(o|=xs.WEBGPU),i&&(o|=xs.WEBGL)),this.compatibleRenderers=o;const l={};if(!r&&!n&&(r={}),r&&n)throw new Error("[Shader] Cannot have both resources and groups");if(!e&&n&&!a)throw new Error("[Shader] No group map or WebGPU shader provided - consider using resources instead.");if(!e&&n&&a)for(const c in a)for(const h in a[c]){const d=a[c][h];l[d]={group:c,binding:h,name:d};}else if(e&&n&&!a){const c=e.structsAndGroups.groups;a={},c.forEach(h=>{a[h.group]=a[h.group]||{},a[h.group][h.binding]=h.name,l[h.name]=h;});}else if(r){n={},a={},e&&e.structsAndGroups.groups.forEach(d=>{a[d.group]=a[d.group]||{},a[d.group][d.binding]=d.name,l[d.name]=d;});let c=0;for(const h in r)l[h]||(n[99]||(n[99]=new _i,this._ownedBindGroups.push(n[99])),l[h]={group:99,binding:c,name:h},a[99]=a[99]||{},a[99][c]=h,c++);for(const h in r){const d=h;let _=r[h];!_.source&&!_._resourceType&&(_=new io(_));const u=l[d];u&&(n[u.group]||(n[u.group]=new _i,this._ownedBindGroups.push(n[u.group])),n[u.group].setResource(_,u.binding));}}this.groups=n,this._uniformBindMap=a,this.resources=this._buildResourceAccessor(n,l);}addResource(t,e,i){var n,r;(n=this._uniformBindMap)[e]||(n[e]={}),(r=this._uniformBindMap[e])[i]||(r[i]=t),this.groups[e]||(this.groups[e]=new _i,this._ownedBindGroups.push(this.groups[e]));}_buildResourceAccessor(t,e){const i={};for(const n in e){const r=e[n];Object.defineProperty(i,r.name,{get(){return t[r.group].getResource(r.binding)},set(o){t[r.group].setResource(o,r.binding);}});}return i}destroy(t=!1){this.emit("destroy",this),t&&(this.gpuProgram?.destroy(),this.glProgram?.destroy()),this.gpuProgram=null,this.glProgram=null,this.removeAllListeners(),this._uniformBindMap=null,this._ownedBindGroups.forEach(e=>{e.destroy();}),this._ownedBindGroups=null,this.resources=null,this.groups=null;}static from(t){const{gpu:e,gl:i,...n}=t;let r,o;return e&&(r=Qt.from(e)),i&&(o=Ce.from(i)),new Ci({gpuProgram:r,glProgram:o,...n})}}const tc={normal:0,add:1,multiply:2,screen:3,overlay:4,erase:5,"normal-npm":6,"add-npm":7,"screen-npm":8,min:9,max:10},Vi=0,qi=1,Qi=2,Zi=3,Ji=4,$i=5,bs=class so{constructor(){this.data=0,this.blendMode="normal",this.polygonOffset=0,this.blend=!0,this.depthMask=!0;}get blend(){return !!(this.data&1<<Vi)}set blend(t){!!(this.data&1<<Vi)!==t&&(this.data^=1<<Vi);}get offsets(){return !!(this.data&1<<qi)}set offsets(t){!!(this.data&1<<qi)!==t&&(this.data^=1<<qi);}set cullMode(t){if(t==="none"){this.culling=!1;return}this.culling=!0,this.clockwiseFrontFace=t==="front";}get cullMode(){return this.culling?this.clockwiseFrontFace?"front":"back":"none"}get culling(){return !!(this.data&1<<Qi)}set culling(t){!!(this.data&1<<Qi)!==t&&(this.data^=1<<Qi);}get depthTest(){return !!(this.data&1<<Zi)}set depthTest(t){!!(this.data&1<<Zi)!==t&&(this.data^=1<<Zi);}get depthMask(){return !!(this.data&1<<$i)}set depthMask(t){!!(this.data&1<<$i)!==t&&(this.data^=1<<$i);}get clockwiseFrontFace(){return !!(this.data&1<<Ji)}set clockwiseFrontFace(t){!!(this.data&1<<Ji)!==t&&(this.data^=1<<Ji);}get blendMode(){return this._blendMode}set blendMode(t){this.blend=t!=="none",this._blendMode=t,this._blendModeId=tc[t]||0;}get polygonOffset(){return this._polygonOffset}set polygonOffset(t){this.offsets=!!t,this._polygonOffset=t;}toString(){return `[pixi.js/core:State blendMode=${this.blendMode} clockwiseFrontFace=${this.clockwiseFrontFace} culling=${this.culling} depthMask=${this.depthMask} polygonOffset=${this.polygonOffset}]`}static for2d(){const t=new so;return t.depthTest=!1,t.blend=!0,t}};bs.default2d=bs.for2d();let ec=bs;const no=class Cs extends Ci{constructor(t){t={...Cs.defaultOptions,...t},super(t),this.enabled=!0,this._state=ec.for2d(),this.blendMode=t.blendMode,this.padding=t.padding,typeof t.antialias=="boolean"?this.antialias=t.antialias?"on":"off":this.antialias=t.antialias,this.resolution=t.resolution,this.blendRequired=t.blendRequired,this.clipToViewport=t.clipToViewport,this.addResource("uTexture",0,1);}apply(t,e,i,n){t.applyFilter(this,e,i,n);}get blendMode(){return this._state.blendMode}set blendMode(t){this._state.blendMode=t;}static from(t){const{gpu:e,gl:i,...n}=t;let r,o;return e&&(r=Qt.from(e)),i&&(o=Ce.from(i)),new Cs({gpuProgram:r,glProgram:o,...n})}};no.defaultOptions={blendMode:"normal",resolution:1,padding:0,antialias:"off",blendRequired:!1,clipToViewport:!0};let vs=no;const ws=[];yt.handleByNamedList(T.Environment,ws);async function ic(s){if(!s)for(let t=0;t<ws.length;t++){const e=ws[t];if(e.value.test()){await e.value.load();return}}}let Te;function sc(){if(typeof Te=="boolean")return Te;try{Te=new Function("param1","param2","param3","return param1[param2] === param3;")({a:"b"},"a","b")===!0;}catch{Te=!1;}return Te}var Xs={exports:{}};Xs.exports=vi;Xs.exports.default=vi;function vi(s,t,e){e=e||2;var i=t&&t.length,n=i?t[0]*e:s.length,r=ro(s,0,n,e,!0),o=[];if(!r||r.next===r.prev)return o;var a,l,c,h,d,_,u;if(i&&(r=lc(s,t,r,e)),s.length>80*e){a=c=s[0],l=h=s[1];for(var f=e;f<n;f+=e)d=s[f],_=s[f+1],d<a&&(a=d),_<l&&(l=_),d>c&&(c=d),_>h&&(h=_);u=Math.max(c-a,h-l),u=u!==0?32767/u:0;}return Ne(r,o,e,a,l,u,0),o}function ro(s,t,e,i,n){var r,o;if(n===Es(s,t,e,i)>0)for(r=t;r<e;r+=i)o=Bn(r,s[r],s[r+1],o);else for(r=e-i;r>=t;r-=i)o=Bn(r,s[r],s[r+1],o);return o&&wi(o,o.next)&&(je(o),o=o.next),o}function ue(s,t){if(!s)return s;t||(t=s);var e=s,i;do if(i=!1,!e.steiner&&(wi(e,e.next)||J(e.prev,e,e.next)===0)){if(je(e),e=t=e.prev,e===e.next)break;i=!0;}else e=e.next;while(i||e!==t);return t}function Ne(s,t,e,i,n,r,o){if(s){!o&&r&&_c(s,i,n,r);for(var a=s,l,c;s.prev!==s.next;){if(l=s.prev,c=s.next,r?rc(s,i,n,r):nc(s)){t.push(l.i/e|0),t.push(s.i/e|0),t.push(c.i/e|0),je(s),s=c.next,a=c.next;continue}if(s=c,s===a){o?o===1?(s=oc(ue(s),t,e),Ne(s,t,e,i,n,r,2)):o===2&&ac(s,t,e,i,n,r):Ne(ue(s),t,e,i,n,r,1);break}}}}function nc(s){var t=s.prev,e=s,i=s.next;if(J(t,e,i)>=0)return !1;for(var n=t.x,r=e.x,o=i.x,a=t.y,l=e.y,c=i.y,h=n<r?n<o?n:o:r<o?r:o,d=a<l?a<c?a:c:l<c?l:c,_=n>r?n>o?n:o:r>o?r:o,u=a>l?a>c?a:c:l>c?l:c,f=i.next;f!==t;){if(f.x>=h&&f.x<=_&&f.y>=d&&f.y<=u&&me(n,a,r,l,o,c,f.x,f.y)&&J(f.prev,f,f.next)>=0)return !1;f=f.next;}return !0}function rc(s,t,e,i){var n=s.prev,r=s,o=s.next;if(J(n,r,o)>=0)return !1;for(var a=n.x,l=r.x,c=o.x,h=n.y,d=r.y,_=o.y,u=a<l?a<c?a:c:l<c?l:c,f=h<d?h<_?h:_:d<_?d:_,p=a>l?a>c?a:c:l>c?l:c,A=h>d?h>_?h:_:d>_?d:_,m=ks(u,f,t,e,i),g=ks(p,A,t,e,i),y=s.prevZ,x=s.nextZ;y&&y.z>=m&&x&&x.z<=g;){if(y.x>=u&&y.x<=p&&y.y>=f&&y.y<=A&&y!==n&&y!==o&&me(a,h,l,d,c,_,y.x,y.y)&&J(y.prev,y,y.next)>=0||(y=y.prevZ,x.x>=u&&x.x<=p&&x.y>=f&&x.y<=A&&x!==n&&x!==o&&me(a,h,l,d,c,_,x.x,x.y)&&J(x.prev,x,x.next)>=0))return !1;x=x.nextZ;}for(;y&&y.z>=m;){if(y.x>=u&&y.x<=p&&y.y>=f&&y.y<=A&&y!==n&&y!==o&&me(a,h,l,d,c,_,y.x,y.y)&&J(y.prev,y,y.next)>=0)return !1;y=y.prevZ;}for(;x&&x.z<=g;){if(x.x>=u&&x.x<=p&&x.y>=f&&x.y<=A&&x!==n&&x!==o&&me(a,h,l,d,c,_,x.x,x.y)&&J(x.prev,x,x.next)>=0)return !1;x=x.nextZ;}return !0}function oc(s,t,e){var i=s;do{var n=i.prev,r=i.next.next;!wi(n,r)&&oo(n,i,i.next,r)&&He(n,r)&&He(r,n)&&(t.push(n.i/e|0),t.push(i.i/e|0),t.push(r.i/e|0),je(i),je(i.next),i=s=r),i=i.next;}while(i!==s);return ue(i)}function ac(s,t,e,i,n,r){var o=s;do{for(var a=o.next.next;a!==o.prev;){if(o.i!==a.i&&pc(o,a)){var l=ao(o,a);o=ue(o,o.next),l=ue(l,l.next),Ne(o,t,e,i,n,r,0),Ne(l,t,e,i,n,r,0);return}a=a.next;}o=o.next;}while(o!==s)}function lc(s,t,e,i){var n=[],r,o,a,l,c;for(r=0,o=t.length;r<o;r++)a=t[r]*i,l=r<o-1?t[r+1]*i:s.length,c=ro(s,a,l,i,!1),c===c.next&&(c.steiner=!0),n.push(Ac(c));for(n.sort(cc),r=0;r<n.length;r++)e=hc(n[r],e);return e}function cc(s,t){return s.x-t.x}function hc(s,t){var e=dc(s,t);if(!e)return t;var i=ao(e,s);return ue(i,i.next),ue(e,e.next)}function dc(s,t){var e=t,i=s.x,n=s.y,r=-1/0,o;do{if(n<=e.y&&n>=e.next.y&&e.next.y!==e.y){var a=e.x+(n-e.y)*(e.next.x-e.x)/(e.next.y-e.y);if(a<=i&&a>r&&(r=a,o=e.x<e.next.x?e:e.next,a===i))return o}e=e.next;}while(e!==t);if(!o)return null;var l=o,c=o.x,h=o.y,d=1/0,_;e=o;do i>=e.x&&e.x>=c&&i!==e.x&&me(n<h?i:r,n,c,h,n<h?r:i,n,e.x,e.y)&&(_=Math.abs(n-e.y)/(i-e.x),He(e,s)&&(_<d||_===d&&(e.x>o.x||e.x===o.x&&uc(o,e)))&&(o=e,d=_)),e=e.next;while(e!==l);return o}function uc(s,t){return J(s.prev,s,t.prev)<0&&J(t.next,s,s.next)<0}function _c(s,t,e,i){var n=s;do n.z===0&&(n.z=ks(n.x,n.y,t,e,i)),n.prevZ=n.prev,n.nextZ=n.next,n=n.next;while(n!==s);n.prevZ.nextZ=null,n.prevZ=null,fc(n);}function fc(s){var t,e,i,n,r,o,a,l,c=1;do{for(e=s,s=null,r=null,o=0;e;){for(o++,i=e,a=0,t=0;t<c&&(a++,i=i.nextZ,!!i);t++);for(l=c;a>0||l>0&&i;)a!==0&&(l===0||!i||e.z<=i.z)?(n=e,e=e.nextZ,a--):(n=i,i=i.nextZ,l--),r?r.nextZ=n:s=n,n.prevZ=r,r=n;e=i;}r.nextZ=null,c*=2;}while(o>1);return s}function ks(s,t,e,i,n){return s=(s-e)*n|0,t=(t-i)*n|0,s=(s|s<<8)&16711935,s=(s|s<<4)&252645135,s=(s|s<<2)&858993459,s=(s|s<<1)&1431655765,t=(t|t<<8)&16711935,t=(t|t<<4)&252645135,t=(t|t<<2)&858993459,t=(t|t<<1)&1431655765,s|t<<1}function Ac(s){var t=s,e=s;do(t.x<e.x||t.x===e.x&&t.y<e.y)&&(e=t),t=t.next;while(t!==s);return e}function me(s,t,e,i,n,r,o,a){return (n-o)*(t-a)>=(s-o)*(r-a)&&(s-o)*(i-a)>=(e-o)*(t-a)&&(e-o)*(r-a)>=(n-o)*(i-a)}function pc(s,t){return s.next.i!==t.i&&s.prev.i!==t.i&&!mc(s,t)&&(He(s,t)&&He(t,s)&&gc(s,t)&&(J(s.prev,s,t.prev)||J(s,t.prev,t))||wi(s,t)&&J(s.prev,s,s.next)>0&&J(t.prev,t,t.next)>0)}function J(s,t,e){return (t.y-s.y)*(e.x-t.x)-(t.x-s.x)*(e.y-t.y)}function wi(s,t){return s.x===t.x&&s.y===t.y}function oo(s,t,e,i){var n=ni(J(s,t,e)),r=ni(J(s,t,i)),o=ni(J(e,i,s)),a=ni(J(e,i,t));return !!(n!==r&&o!==a||n===0&&si(s,e,t)||r===0&&si(s,i,t)||o===0&&si(e,s,i)||a===0&&si(e,t,i))}function si(s,t,e){return t.x<=Math.max(s.x,e.x)&&t.x>=Math.min(s.x,e.x)&&t.y<=Math.max(s.y,e.y)&&t.y>=Math.min(s.y,e.y)}function ni(s){return s>0?1:s<0?-1:0}function mc(s,t){var e=s;do{if(e.i!==s.i&&e.next.i!==s.i&&e.i!==t.i&&e.next.i!==t.i&&oo(e,e.next,s,t))return !0;e=e.next;}while(e!==s);return !1}function He(s,t){return J(s.prev,s,s.next)<0?J(s,t,s.next)>=0&&J(s,s.prev,t)>=0:J(s,t,s.prev)<0||J(s,s.next,t)<0}function gc(s,t){var e=s,i=!1,n=(s.x+t.x)/2,r=(s.y+t.y)/2;do e.y>r!=e.next.y>r&&e.next.y!==e.y&&n<(e.next.x-e.x)*(r-e.y)/(e.next.y-e.y)+e.x&&(i=!i),e=e.next;while(e!==s);return i}function ao(s,t){var e=new Is(s.i,s.x,s.y),i=new Is(t.i,t.x,t.y),n=s.next,r=t.prev;return s.next=t,t.prev=s,e.next=n,n.prev=e,i.next=e,e.prev=i,r.next=i,i.prev=r,i}function Bn(s,t,e,i){var n=new Is(s,t,e);return i?(n.next=i.next,n.prev=i,i.next.prev=n,i.next=n):(n.prev=n,n.next=n),n}function je(s){s.next.prev=s.prev,s.prev.next=s.next,s.prevZ&&(s.prevZ.nextZ=s.nextZ),s.nextZ&&(s.nextZ.prevZ=s.prevZ);}function Is(s,t,e){this.i=s,this.x=t,this.y=e,this.prev=null,this.next=null,this.z=0,this.prevZ=null,this.nextZ=null,this.steiner=!1;}vi.deviation=function(s,t,e,i){var n=t&&t.length,r=n?t[0]*e:s.length,o=Math.abs(Es(s,0,r,e));if(n)for(var a=0,l=t.length;a<l;a++){var c=t[a]*e,h=a<l-1?t[a+1]*e:s.length;o-=Math.abs(Es(s,c,h,e));}var d=0;for(a=0;a<i.length;a+=3){var _=i[a]*e,u=i[a+1]*e,f=i[a+2]*e;d+=Math.abs((s[_]-s[f])*(s[u+1]-s[_+1])-(s[_]-s[u])*(s[f+1]-s[_+1]));}return o===0&&d===0?0:Math.abs((d-o)/o)};function Es(s,t,e,i){for(var n=0,r=t,o=e-i;r<e;r+=i)n+=(s[o]-s[r])*(s[r+1]+s[o+1]),o=r;return n}vi.flatten=function(s){for(var t=s[0][0].length,e={vertices:[],holes:[],dimensions:t},i=0,n=0;n<s.length;n++){for(var r=0;r<s[n].length;r++)for(var o=0;o<t;o++)e.vertices.push(s[n][r][o]);n>0&&(i+=s[n-1].length,e.holes.push(i));}return e};var yc=Xs.exports;const xc=Tr$1(yc);var lo=(s=>(s[s.NONE=0]="NONE",s[s.COLOR=16384]="COLOR",s[s.STENCIL=1024]="STENCIL",s[s.DEPTH=256]="DEPTH",s[s.COLOR_DEPTH=16640]="COLOR_DEPTH",s[s.COLOR_STENCIL=17408]="COLOR_STENCIL",s[s.DEPTH_STENCIL=1280]="DEPTH_STENCIL",s[s.ALL=17664]="ALL",s))(lo||{});class bc{constructor(t){this.items=[],this._name=t;}emit(t,e,i,n,r,o,a,l){const{name:c,items:h}=this;for(let d=0,_=h.length;d<_;d++)h[d][c](t,e,i,n,r,o,a,l);return this}add(t){return t[this._name]&&(this.remove(t),this.items.push(t)),this}remove(t){const e=this.items.indexOf(t);return e!==-1&&this.items.splice(e,1),this}contains(t){return this.items.indexOf(t)!==-1}removeAll(){return this.items.length=0,this}destroy(){this.removeAll(),this.items=null,this._name=null;}get empty(){return this.items.length===0}get name(){return this._name}}const Cc=["init","destroy","contextChange","resolutionChange","resetState","renderEnd","renderStart","render","update","postrender","prerender"],co=class ho extends Lt{constructor(t){super(),this.runners=Object.create(null),this.renderPipes=Object.create(null),this._initOptions={},this._systemsHash=Object.create(null),this.type=t.type,this.name=t.name,this.config=t;const e=[...Cc,...this.config.runners??[]];this._addRunners(...e),this._unsafeEvalCheck();}async init(t={}){const e=t.skipExtensionImports===!0?!0:t.manageImports===!1;await ic(e),this._addSystems(this.config.systems),this._addPipes(this.config.renderPipes,this.config.renderPipeAdaptors);for(const i in this._systemsHash)t={...this._systemsHash[i].constructor.defaultOptions,...t};t={...ho.defaultOptions,...t},this._roundPixels=t.roundPixels?1:0;for(let i=0;i<this.runners.init.items.length;i++)await this.runners.init.items[i].init(t);this._initOptions=t;}render(t,e){let i=t;if(i instanceof O&&(i={container:i},e&&(D(N,"passing a second argument is deprecated, please use render options instead"),i.target=e.renderTexture)),i.target||(i.target=this.view.renderTarget),i.target===this.view.renderTarget&&(this._lastObjectRendered=i.container,i.clearColor??(i.clearColor=this.background.colorRgba),i.clear??(i.clear=this.background.clearBeforeRender)),i.clearColor){const n=Array.isArray(i.clearColor)&&i.clearColor.length===4;i.clearColor=n?i.clearColor:st.shared.setValue(i.clearColor).toArray();}i.transform||(i.container.updateLocalTransform(),i.transform=i.container.localTransform),i.container.enableRenderGroup(),this.runners.prerender.emit(i),this.runners.renderStart.emit(i),this.runners.render.emit(i),this.runners.renderEnd.emit(i),this.runners.postrender.emit(i);}resize(t,e,i){const n=this.view.resolution;this.view.resize(t,e,i),this.emit("resize",this.view.screen.width,this.view.screen.height,this.view.resolution),i!==void 0&&i!==n&&this.runners.resolutionChange.emit(i);}clear(t={}){const e=this;t.target||(t.target=e.renderTarget.renderTarget),t.clearColor||(t.clearColor=this.background.colorRgba),t.clear??(t.clear=lo.ALL);const{clear:i,clearColor:n,target:r}=t;st.shared.setValue(n??this.background.colorRgba),e.renderTarget.clear(r,i,st.shared.toArray());}get resolution(){return this.view.resolution}set resolution(t){this.view.resolution=t,this.runners.resolutionChange.emit(t);}get width(){return this.view.texture.frame.width}get height(){return this.view.texture.frame.height}get canvas(){return this.view.canvas}get lastObjectRendered(){return this._lastObjectRendered}get renderingToScreen(){return this.renderTarget.renderingToScreen}get screen(){return this.view.screen}_addRunners(...t){t.forEach(e=>{this.runners[e]=new bc(e);});}_addSystems(t){let e;for(e in t){const i=t[e];this._addSystem(i.value,i.name);}}_addSystem(t,e){const i=new t(this);if(this[e])throw new Error(`Whoops! The name "${e}" is already in use`);this[e]=i,this._systemsHash[e]=i;for(const n in this.runners)this.runners[n].add(i);return this}_addPipes(t,e){const i=e.reduce((n,r)=>(n[r.name]=r.value,n),{});t.forEach(n=>{const r=n.value,o=n.name,a=i[o];this.renderPipes[o]=new r(this,a?new a:null);});}destroy(t=!1){this.runners.destroy.items.reverse(),this.runners.destroy.emit(t),Object.values(this.runners).forEach(e=>{e.destroy();}),this._systemsHash=null,this.renderPipes=null;}generateTexture(t){return this.textureGenerator.generateTexture(t)}get roundPixels(){return !!this._roundPixels}_unsafeEvalCheck(){if(!sc())throw new Error("Current environment does not allow unsafe-eval, please use pixi.js/unsafe-eval module to enable support.")}resetState(){this.runners.resetState.emit();}};co.defaultOptions={resolution:1,failIfMajorPerformanceCaveat:!1,roundPixels:!1};let uo=co,ri;function vc(s){return ri!==void 0||(ri=(()=>{const t={stencil:!0,failIfMajorPerformanceCaveat:s??uo.defaultOptions.failIfMajorPerformanceCaveat};try{if(!it.get().getWebGLRenderingContext())return !1;let i=it.get().createCanvas().getContext("webgl",t);const n=!!i?.getContextAttributes()?.stencil;if(i){const r=i.getExtension("WEBGL_lose_context");r&&r.loseContext();}return i=null,n}catch{return !1}})()),ri}let oi;async function wc(s={}){return oi!==void 0||(oi=await(async()=>{const t=it.get().getNavigator().gpu;if(!t)return !1;try{return await(await t.requestAdapter(s)).requestDevice(),!0}catch{return !1}})()),oi}const zn=["webgl","webgpu","canvas"];async function kc(s){let t=[];s.preference?(t.push(s.preference),zn.forEach(r=>{r!==s.preference&&t.push(r);})):t=zn.slice();let e,i={};for(let r=0;r<t.length;r++){const o=t[r];if(o==="webgpu"&&await wc()){const{WebGPURenderer:a}=await import('./WebGPURenderer-Q1myraQ5.js');e=a,i={...s,...s.webgpu};break}else if(o==="webgl"&&vc(s.failIfMajorPerformanceCaveat??uo.defaultOptions.failIfMajorPerformanceCaveat)){const{WebGLRenderer:a}=await import('./WebGLRenderer-BTtNwbw6.js');e=a,i={...s,...s.webgl};break}else if(o==="canvas")throw i={...s},new Error("CanvasRenderer is not yet implemented")}if(delete i.webgpu,delete i.webgl,!e)throw new Error("No available renderer for the current environment");const n=new e;return await n.init(i),n}const _o="8.7.3";class fo{static init(){globalThis.__PIXI_APP_INIT__?.(this,_o);}static destroy(){}}fo.extension=T.Application;class Ic{constructor(t){this._renderer=t;}init(){globalThis.__PIXI_RENDERER_INIT__?.(this._renderer,_o);}destroy(){this._renderer=null;}}Ic.extension={type:[T.WebGLSystem,T.WebGPUSystem],name:"initHook",priority:-10};const Ao=class Ms{constructor(...t){this.stage=new O,t[0]!==void 0&&D(N,"Application constructor options are deprecated, please use Application.init() instead.");}async init(t){t={...t},this.renderer=await kc(t),Ms._plugins.forEach(e=>{e.init.call(this,t);});}render(){this.renderer.render({container:this.stage});}get canvas(){return this.renderer.canvas}get view(){return D(N,"Application.view is deprecated, please use Application.canvas instead."),this.renderer.canvas}get screen(){return this.renderer.screen}destroy(t=!1,e=!1){const i=Ms._plugins.slice(0);i.reverse(),i.forEach(n=>{n.destroy.call(this);}),this.stage.destroy(e),this.stage=null,this.renderer.destroy(t),this.renderer=null;}};Ao._plugins=[];let po=Ao;yt.handleByList(T.Application,po._plugins);yt.add(fo);class mo extends Lt{constructor(){super(...arguments),this.chars=Object.create(null),this.lineHeight=0,this.fontFamily="",this.fontMetrics={fontSize:0,ascent:0,descent:0},this.baseLineOffset=0,this.distanceField={type:"none",range:0},this.pages=[],this.applyFillAsTint=!0,this.baseMeasurementFontSize=100,this.baseRenderedFontSize=100;}get font(){return D(N,"BitmapFont.font is deprecated, please use BitmapFont.fontFamily instead."),this.fontFamily}get pageTextures(){return D(N,"BitmapFont.pageTextures is deprecated, please use BitmapFont.pages instead."),this.pages}get size(){return D(N,"BitmapFont.size is deprecated, please use BitmapFont.fontMetrics.fontSize instead."),this.fontMetrics.fontSize}get distanceFieldRange(){return D(N,"BitmapFont.distanceFieldRange is deprecated, please use BitmapFont.distanceField.range instead."),this.distanceField.range}get distanceFieldType(){return D(N,"BitmapFont.distanceFieldType is deprecated, please use BitmapFont.distanceField.type instead."),this.distanceField.type}destroy(t=!1){this.emit("destroy",this),this.removeAllListeners();for(const e in this.chars)this.chars[e].texture?.destroy();this.chars=null,t&&(this.pages.forEach(e=>e.texture.destroy(!0)),this.pages=null);}}const go=class Ss{constructor(t,e,i,n){this.uid=rt("fillGradient"),this.type="linear",this.gradientStops=[],this._styleKey=null,this.x0=t,this.y0=e,this.x1=i,this.y1=n;}addColorStop(t,e){return this.gradientStops.push({offset:t,color:st.shared.setValue(e).toHexa()}),this._styleKey=null,this}buildLinearGradient(){if(this.texture)return;const t=Ss.defaultTextureSize,{gradientStops:e}=this,i=it.get().createCanvas();i.width=t,i.height=t;const n=i.getContext("2d"),r=n.createLinearGradient(0,0,Ss.defaultTextureSize,1);for(let p=0;p<e.length;p++){const A=e[p];r.addColorStop(A.offset,A.color);}n.fillStyle=r,n.fillRect(0,0,t,t),this.texture=new W({source:new we({resource:i,addressModeU:"clamp-to-edge",addressModeV:"repeat"})});const{x0:o,y0:a,x1:l,y1:c}=this,h=new K,d=l-o,_=c-a,u=Math.sqrt(d*d+_*_),f=Math.atan2(_,d);h.translate(-o,-a),h.scale(1/t,1/t),h.rotate(-f),h.scale(256/u,1),this.transform=h,this._styleKey=null;}get styleKey(){if(this._styleKey)return this._styleKey;const t=this.gradientStops.map(n=>`${n.offset}-${n.color}`).join("-"),e=this.texture.uid,i=this.transform.toArray().join("-");return `fill-gradient-${this.uid}-${t}-${e}-${i}-${this.x0}-${this.y0}-${this.x1}-${this.y1}`}};go.defaultTextureSize=256;let Ye=go;const Pn={repeat:{addressModeU:"repeat",addressModeV:"repeat"},"repeat-x":{addressModeU:"repeat",addressModeV:"clamp-to-edge"},"repeat-y":{addressModeU:"clamp-to-edge",addressModeV:"repeat"},"no-repeat":{addressModeU:"clamp-to-edge",addressModeV:"clamp-to-edge"}};class ki{constructor(t,e){this.uid=rt("fillPattern"),this.transform=new K,this._styleKey=null,this.texture=t,this.transform.scale(1/t.frame.width,1/t.frame.height),e&&(t.source.style.addressModeU=Pn[e].addressModeU,t.source.style.addressModeV=Pn[e].addressModeV);}setTransform(t){const e=this.texture;this.transform.copyFrom(t),this.transform.invert(),this.transform.scale(1/e.frame.width,1/e.frame.height),this._styleKey=null;}get styleKey(){return this._styleKey?this._styleKey:(this._styleKey=`fill-pattern-${this.uid}-${this.texture.uid}-${this.transform.toArray().join("-")}`,this._styleKey)}}var Ec=Sc,ts={a:7,c:6,h:1,l:2,m:2,q:4,s:4,t:2,v:1,z:0},Mc=/([astvzqmhlc])([^astvzqmhlc]*)/ig;function Sc(s){var t=[];return s.replace(Mc,function(e,i,n){var r=i.toLowerCase();for(n=zc(n),r=="m"&&n.length>2&&(t.push([i].concat(n.splice(0,2))),r="l",i=i=="m"?"l":"L");;){if(n.length==ts[r])return n.unshift(i),t.push(n);if(n.length<ts[r])throw new Error("malformed path data");t.push([i].concat(n.splice(0,ts[r])));}}),t}var Bc=/-?[0-9]*\.?[0-9]+(?:e[-+]?\d+)?/ig;function zc(s){var t=s.match(Bc);return t?t.map(Number):[]}const Pc=Tr$1(Ec);function Tc(s,t){const e=Pc(s),i=[];let n=null,r=0,o=0;for(let a=0;a<e.length;a++){const l=e[a],c=l[0],h=l;switch(c){case"M":r=h[1],o=h[2],t.moveTo(r,o);break;case"m":r+=h[1],o+=h[2],t.moveTo(r,o);break;case"H":r=h[1],t.lineTo(r,o);break;case"h":r+=h[1],t.lineTo(r,o);break;case"V":o=h[1],t.lineTo(r,o);break;case"v":o+=h[1],t.lineTo(r,o);break;case"L":r=h[1],o=h[2],t.lineTo(r,o);break;case"l":r+=h[1],o+=h[2],t.lineTo(r,o);break;case"C":r=h[5],o=h[6],t.bezierCurveTo(h[1],h[2],h[3],h[4],r,o);break;case"c":t.bezierCurveTo(r+h[1],o+h[2],r+h[3],o+h[4],r+h[5],o+h[6]),r+=h[5],o+=h[6];break;case"S":r=h[3],o=h[4],t.bezierCurveToShort(h[1],h[2],r,o);break;case"s":t.bezierCurveToShort(r+h[1],o+h[2],r+h[3],o+h[4]),r+=h[3],o+=h[4];break;case"Q":r=h[3],o=h[4],t.quadraticCurveTo(h[1],h[2],r,o);break;case"q":t.quadraticCurveTo(r+h[1],o+h[2],r+h[3],o+h[4]),r+=h[3],o+=h[4];break;case"T":r=h[1],o=h[2],t.quadraticCurveToShort(r,o);break;case"t":r+=h[1],o+=h[2],t.quadraticCurveToShort(r,o);break;case"A":r=h[6],o=h[7],t.arcToSvg(h[1],h[2],h[3],h[4],h[5],r,o);break;case"a":r+=h[6],o+=h[7],t.arcToSvg(h[1],h[2],h[3],h[4],h[5],r,o);break;case"Z":case"z":t.closePath(),i.length>0&&(n=i.pop(),n?(r=n.startX,o=n.startY):(r=0,o=0)),n=null;break;default:ot(`Unknown SVG path command: ${c}`);}c!=="Z"&&c!=="z"&&n===null&&(n={startX:r,startY:o},i.push(n));}return t}class Vs{constructor(t=0,e=0,i=0){this.type="circle",this.x=t,this.y=e,this.radius=i;}clone(){return new Vs(this.x,this.y,this.radius)}contains(t,e){if(this.radius<=0)return !1;const i=this.radius*this.radius;let n=this.x-t,r=this.y-e;return n*=n,r*=r,n+r<=i}strokeContains(t,e,i,n=.5){if(this.radius===0)return !1;const r=this.x-t,o=this.y-e,a=this.radius,l=(1-n)*i,c=Math.sqrt(r*r+o*o);return c<=a+l&&c>a-(i-l)}getBounds(t){return t||(t=new Q),t.x=this.x-this.radius,t.y=this.y-this.radius,t.width=this.radius*2,t.height=this.radius*2,t}copyFrom(t){return this.x=t.x,this.y=t.y,this.radius=t.radius,this}copyTo(t){return t.copyFrom(this),t}toString(){return `[pixi.js/math:Circle x=${this.x} y=${this.y} radius=${this.radius}]`}}class qs{constructor(t=0,e=0,i=0,n=0){this.type="ellipse",this.x=t,this.y=e,this.halfWidth=i,this.halfHeight=n;}clone(){return new qs(this.x,this.y,this.halfWidth,this.halfHeight)}contains(t,e){if(this.halfWidth<=0||this.halfHeight<=0)return !1;let i=(t-this.x)/this.halfWidth,n=(e-this.y)/this.halfHeight;return i*=i,n*=n,i+n<=1}strokeContains(t,e,i,n=.5){const{halfWidth:r,halfHeight:o}=this;if(r<=0||o<=0)return !1;const a=i*(1-n),l=i-a,c=r-l,h=o-l,d=r+a,_=o+a,u=t-this.x,f=e-this.y,p=u*u/(c*c)+f*f/(h*h),A=u*u/(d*d)+f*f/(_*_);return p>1&&A<=1}getBounds(t){return t||(t=new Q),t.x=this.x-this.halfWidth,t.y=this.y-this.halfHeight,t.width=this.halfWidth*2,t.height=this.halfHeight*2,t}copyFrom(t){return this.x=t.x,this.y=t.y,this.halfWidth=t.halfWidth,this.halfHeight=t.halfHeight,this}copyTo(t){return t.copyFrom(this),t}toString(){return `[pixi.js/math:Ellipse x=${this.x} y=${this.y} halfWidth=${this.halfWidth} halfHeight=${this.halfHeight}]`}}function Rc(s,t,e,i,n,r){const o=s-e,a=t-i,l=n-e,c=r-i,h=o*l+a*c,d=l*l+c*c;let _=-1;d!==0&&(_=h/d);let u,f;_<0?(u=e,f=i):_>1?(u=n,f=r):(u=e+_*l,f=i+_*c);const p=s-u,A=t-f;return p*p+A*A}class Ue{constructor(...t){this.type="polygon";let e=Array.isArray(t[0])?t[0]:t;if(typeof e[0]!="number"){const i=[];for(let n=0,r=e.length;n<r;n++)i.push(e[n].x,e[n].y);e=i;}this.points=e,this.closePath=!0;}clone(){const t=this.points.slice(),e=new Ue(t);return e.closePath=this.closePath,e}contains(t,e){let i=!1;const n=this.points.length/2;for(let r=0,o=n-1;r<n;o=r++){const a=this.points[r*2],l=this.points[r*2+1],c=this.points[o*2],h=this.points[o*2+1];l>e!=h>e&&t<(c-a)*((e-l)/(h-l))+a&&(i=!i);}return i}strokeContains(t,e,i,n=.5){const r=i*i,o=r*(1-n),a=r-o,{points:l}=this,c=l.length-(this.closePath?0:2);for(let h=0;h<c;h+=2){const d=l[h],_=l[h+1],u=l[(h+2)%l.length],f=l[(h+3)%l.length],p=Rc(t,e,d,_,u,f),A=Math.sign((u-d)*(e-_)-(f-_)*(t-d));if(p<=(A<0?a:o))return !0}return !1}getBounds(t){t||(t=new Q);const e=this.points;let i=1/0,n=-1/0,r=1/0,o=-1/0;for(let a=0,l=e.length;a<l;a+=2){const c=e[a],h=e[a+1];i=c<i?c:i,n=c>n?c:n,r=h<r?h:r,o=h>o?h:o;}return t.x=i,t.width=n-i,t.y=r,t.height=o-r,t}copyFrom(t){return this.points=t.points.slice(),this.closePath=t.closePath,this}copyTo(t){return t.copyFrom(this),t}toString(){return `[pixi.js/math:PolygoncloseStroke=${this.closePath}points=${this.points.reduce((t,e)=>`${t}, ${e}`,"")}]`}get lastX(){return this.points[this.points.length-2]}get lastY(){return this.points[this.points.length-1]}get x(){return this.points[this.points.length-2]}get y(){return this.points[this.points.length-1]}}const ai=(s,t,e,i,n,r,o)=>{const a=s-e,l=t-i,c=Math.sqrt(a*a+l*l);return c>=n-r&&c<=n+o};class Qs{constructor(t=0,e=0,i=0,n=0,r=20){this.type="roundedRectangle",this.x=t,this.y=e,this.width=i,this.height=n,this.radius=r;}getBounds(t){return t||(t=new Q),t.x=this.x,t.y=this.y,t.width=this.width,t.height=this.height,t}clone(){return new Qs(this.x,this.y,this.width,this.height,this.radius)}copyFrom(t){return this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height,this}copyTo(t){return t.copyFrom(this),t}contains(t,e){if(this.width<=0||this.height<=0)return !1;if(t>=this.x&&t<=this.x+this.width&&e>=this.y&&e<=this.y+this.height){const i=Math.max(0,Math.min(this.radius,Math.min(this.width,this.height)/2));if(e>=this.y+i&&e<=this.y+this.height-i||t>=this.x+i&&t<=this.x+this.width-i)return !0;let n=t-(this.x+i),r=e-(this.y+i);const o=i*i;if(n*n+r*r<=o||(n=t-(this.x+this.width-i),n*n+r*r<=o)||(r=e-(this.y+this.height-i),n*n+r*r<=o)||(n=t-(this.x+i),n*n+r*r<=o))return !0}return !1}strokeContains(t,e,i,n=.5){const{x:r,y:o,width:a,height:l,radius:c}=this,h=i*(1-n),d=i-h,_=r+c,u=o+c,f=a-c*2,p=l-c*2,A=r+a,m=o+l;return (t>=r-h&&t<=r+d||t>=A-d&&t<=A+h)&&e>=u&&e<=u+p||(e>=o-h&&e<=o+d||e>=m-d&&e<=m+h)&&t>=_&&t<=_+f?!0:t<_&&e<u&&ai(t,e,_,u,c,d,h)||t>A-c&&e<u&&ai(t,e,A-c,u,c,d,h)||t>A-c&&e>m-c&&ai(t,e,A-c,m-c,c,d,h)||t<_&&e>m-c&&ai(t,e,_,m-c,c,d,h)}toString(){return `[pixi.js/math:RoundedRectangle x=${this.x} y=${this.y}width=${this.width} height=${this.height} radius=${this.radius}]`}}const Gc=["precision mediump float;","void main(void){","float test = 0.1;","%forloop%","gl_FragColor = vec4(0.0);","}"].join(`
`);function Oc(s){let t="";for(let e=0;e<s;++e)e>0&&(t+=`
else `),e<s-1&&(t+=`if(test == ${e}.0){}`);return t}function Fc(s,t){if(s===0)throw new Error("Invalid value of `0` passed to `checkMaxIfStatementsInShader`");const e=t.createShader(t.FRAGMENT_SHADER);try{for(;;){const i=Gc.replace(/%forloop%/gi,Oc(s));if(t.shaderSource(e,i),t.compileShader(e),!t.getShaderParameter(e,t.COMPILE_STATUS))s=s/2|0;else break}}finally{t.deleteShader(e);}return s}let fe=null;function yo(){if(fe)return fe;const s=Zr();return fe=s.getParameter(s.MAX_TEXTURE_IMAGE_UNITS),fe=Fc(fe,s),s.getExtension("WEBGL_lose_context")?.loseContext(),fe}const xo={};function Lc(s,t){let e=2166136261;for(let i=0;i<t;i++)e^=s[i].uid,e=Math.imul(e,16777619),e>>>=0;return xo[e]||Wc(s,t,e)}let es=0;function Wc(s,t,e){const i={};let n=0;es||(es=yo());for(let o=0;o<es;o++){const a=o<t?s[o]:W.EMPTY.source;i[n++]=a.source,i[n++]=a.style;}const r=new _i(i);return xo[e]=r,r}class Tn{constructor(t){typeof t=="number"?this.rawBinaryData=new ArrayBuffer(t):t instanceof Uint8Array?this.rawBinaryData=t.buffer:this.rawBinaryData=t,this.uint32View=new Uint32Array(this.rawBinaryData),this.float32View=new Float32Array(this.rawBinaryData),this.size=this.rawBinaryData.byteLength;}get int8View(){return this._int8View||(this._int8View=new Int8Array(this.rawBinaryData)),this._int8View}get uint8View(){return this._uint8View||(this._uint8View=new Uint8Array(this.rawBinaryData)),this._uint8View}get int16View(){return this._int16View||(this._int16View=new Int16Array(this.rawBinaryData)),this._int16View}get int32View(){return this._int32View||(this._int32View=new Int32Array(this.rawBinaryData)),this._int32View}get float64View(){return this._float64Array||(this._float64Array=new Float64Array(this.rawBinaryData)),this._float64Array}get bigUint64View(){return this._bigUint64Array||(this._bigUint64Array=new BigUint64Array(this.rawBinaryData)),this._bigUint64Array}view(t){return this[`${t}View`]}destroy(){this.rawBinaryData=null,this._int8View=null,this._uint8View=null,this._int16View=null,this.uint16View=null,this._int32View=null,this.uint32View=null,this.float32View=null;}static sizeOf(t){switch(t){case"int8":case"uint8":return 1;case"int16":case"uint16":return 2;case"int32":case"uint32":case"float32":return 4;default:throw new Error(`${t} isn't a valid view type`)}}}function Rn(s,t){const e=s.byteLength/8|0,i=new Float64Array(s,0,e);new Float64Array(t,0,e).set(i);const r=s.byteLength-e*8;if(r>0){const o=new Uint8Array(s,e*8,r);new Uint8Array(t,e*8,r).set(o);}}const Uc={normal:"normal-npm",add:"add-npm",screen:"screen-npm"};var Dc=(s=>(s[s.DISABLED=0]="DISABLED",s[s.RENDERING_MASK_ADD=1]="RENDERING_MASK_ADD",s[s.MASK_ACTIVE=2]="MASK_ACTIVE",s[s.INVERSE_MASK_ACTIVE=3]="INVERSE_MASK_ACTIVE",s[s.RENDERING_MASK_REMOVE=4]="RENDERING_MASK_REMOVE",s[s.NONE=5]="NONE",s))(Dc||{});function Gn(s,t){return t.alphaMode==="no-premultiply-alpha"&&Uc[s]||s}class Kc{constructor(){this.ids=Object.create(null),this.textures=[],this.count=0;}clear(){for(let t=0;t<this.count;t++){const e=this.textures[t];this.textures[t]=null,this.ids[e.uid]=null;}this.count=0;}}class Nc{constructor(){this.renderPipeId="batch",this.action="startBatch",this.start=0,this.size=0,this.textures=new Kc,this.blendMode="normal",this.topology="triangle-strip",this.canBundle=!0;}destroy(){this.textures=null,this.gpuBindGroup=null,this.bindGroup=null,this.batcher=null;}}const bo=[];let Bs=0;function On(){return Bs>0?bo[--Bs]:new Nc}function Fn(s){bo[Bs++]=s;}let Re=0;const Co=class fi{constructor(t={}){this.uid=rt("batcher"),this.dirty=!0,this.batchIndex=0,this.batches=[],this._elements=[],fi.defaultOptions.maxTextures=fi.defaultOptions.maxTextures??yo(),t={...fi.defaultOptions,...t};const{maxTextures:e,attributesInitialSize:i,indicesInitialSize:n}=t;this.attributeBuffer=new Tn(i*4),this.indexBuffer=new Uint16Array(n),this.maxTextures=e;}begin(){this.elementSize=0,this.elementStart=0,this.indexSize=0,this.attributeSize=0;for(let t=0;t<this.batchIndex;t++)Fn(this.batches[t]);this.batchIndex=0,this._batchIndexStart=0,this._batchIndexSize=0,this.dirty=!0;}add(t){this._elements[this.elementSize++]=t,t._indexStart=this.indexSize,t._attributeStart=this.attributeSize,t._batcher=this,this.indexSize+=t.indexSize,this.attributeSize+=t.attributeSize*this.vertexSize;}checkAndUpdateTexture(t,e){const i=t._batch.textures.ids[e._source.uid];return !i&&i!==0?!1:(t._textureId=i,t.texture=e,!0)}updateElement(t){this.dirty=!0;const e=this.attributeBuffer;t.packAsQuad?this.packQuadAttributes(t,e.float32View,e.uint32View,t._attributeStart,t._textureId):this.packAttributes(t,e.float32View,e.uint32View,t._attributeStart,t._textureId);}break(t){const e=this._elements;if(!e[this.elementStart])return;let i=On(),n=i.textures;n.clear();const r=e[this.elementStart];let o=Gn(r.blendMode,r.texture._source),a=r.topology;this.attributeSize*4>this.attributeBuffer.size&&this._resizeAttributeBuffer(this.attributeSize*4),this.indexSize>this.indexBuffer.length&&this._resizeIndexBuffer(this.indexSize);const l=this.attributeBuffer.float32View,c=this.attributeBuffer.uint32View,h=this.indexBuffer;let d=this._batchIndexSize,_=this._batchIndexStart,u="startBatch";const f=this.maxTextures;for(let p=this.elementStart;p<this.elementSize;++p){const A=e[p];e[p]=null;const g=A.texture._source,y=Gn(A.blendMode,g),x=o!==y||a!==A.topology;if(g._batchTick===Re&&!x){A._textureId=g._textureBindLocation,d+=A.indexSize,A.packAsQuad?(this.packQuadAttributes(A,l,c,A._attributeStart,A._textureId),this.packQuadIndex(h,A._indexStart,A._attributeStart/this.vertexSize)):(this.packAttributes(A,l,c,A._attributeStart,A._textureId),this.packIndex(A,h,A._indexStart,A._attributeStart/this.vertexSize)),A._batch=i;continue}g._batchTick=Re,(n.count>=f||x)&&(this._finishBatch(i,_,d-_,n,o,a,t,u),u="renderBatch",_=d,o=y,a=A.topology,i=On(),n=i.textures,n.clear(),++Re),A._textureId=g._textureBindLocation=n.count,n.ids[g.uid]=n.count,n.textures[n.count++]=g,A._batch=i,d+=A.indexSize,A.packAsQuad?(this.packQuadAttributes(A,l,c,A._attributeStart,A._textureId),this.packQuadIndex(h,A._indexStart,A._attributeStart/this.vertexSize)):(this.packAttributes(A,l,c,A._attributeStart,A._textureId),this.packIndex(A,h,A._indexStart,A._attributeStart/this.vertexSize));}n.count>0&&(this._finishBatch(i,_,d-_,n,o,a,t,u),_=d,++Re),this.elementStart=this.elementSize,this._batchIndexStart=_,this._batchIndexSize=d;}_finishBatch(t,e,i,n,r,o,a,l){t.gpuBindGroup=null,t.bindGroup=null,t.action=l,t.batcher=this,t.textures=n,t.blendMode=r,t.topology=o,t.start=e,t.size=i,++Re,this.batches[this.batchIndex++]=t,a.add(t);}finish(t){this.break(t);}ensureAttributeBuffer(t){t*4<=this.attributeBuffer.size||this._resizeAttributeBuffer(t*4);}ensureIndexBuffer(t){t<=this.indexBuffer.length||this._resizeIndexBuffer(t);}_resizeAttributeBuffer(t){const e=Math.max(t,this.attributeBuffer.size*2),i=new Tn(e);Rn(this.attributeBuffer.rawBinaryData,i.rawBinaryData),this.attributeBuffer=i;}_resizeIndexBuffer(t){const e=this.indexBuffer;let i=Math.max(t,e.length*1.5);i+=i%2;const n=i>65535?new Uint32Array(i):new Uint16Array(i);if(n.BYTES_PER_ELEMENT!==e.BYTES_PER_ELEMENT)for(let r=0;r<e.length;r++)n[r]=e[r];else Rn(e.buffer,n.buffer);this.indexBuffer=n;}packQuadIndex(t,e,i){t[e]=i+0,t[e+1]=i+1,t[e+2]=i+2,t[e+3]=i+0,t[e+4]=i+2,t[e+5]=i+3;}packIndex(t,e,i,n){const r=t.indices,o=t.indexSize,a=t.indexOffset,l=t.attributeOffset;for(let c=0;c<o;c++)e[i++]=n+r[c+a]-l;}destroy(){for(let t=0;t<this.batches.length;t++)Fn(this.batches[t]);this.batches=null;for(let t=0;t<this._elements.length;t++)this._elements[t]._batch=null;this._elements=null,this.indexBuffer=null,this.attributeBuffer.destroy(),this.attributeBuffer=null;}};Co.defaultOptions={maxTextures:null,attributesInitialSize:4,indicesInitialSize:6};let Hc=Co;var vt=(s=>(s[s.MAP_READ=1]="MAP_READ",s[s.MAP_WRITE=2]="MAP_WRITE",s[s.COPY_SRC=4]="COPY_SRC",s[s.COPY_DST=8]="COPY_DST",s[s.INDEX=16]="INDEX",s[s.VERTEX=32]="VERTEX",s[s.UNIFORM=64]="UNIFORM",s[s.STORAGE=128]="STORAGE",s[s.INDIRECT=256]="INDIRECT",s[s.QUERY_RESOLVE=512]="QUERY_RESOLVE",s[s.STATIC=1024]="STATIC",s))(vt||{});class Xe extends Lt{constructor(t){let{data:e,size:i}=t;const{usage:n,label:r,shrinkToFit:o}=t;super(),this.uid=rt("buffer"),this._resourceType="buffer",this._resourceId=rt("resource"),this._touched=0,this._updateID=1,this._dataInt32=null,this.shrinkToFit=!0,this.destroyed=!1,e instanceof Array&&(e=new Float32Array(e)),this._data=e,i??(i=e?.byteLength);const a=!!e;this.descriptor={size:i,usage:n,mappedAtCreation:a,label:r},this.shrinkToFit=o??!0;}get data(){return this._data}set data(t){this.setDataWithSize(t,t.length,!0);}get dataInt32(){return this._dataInt32||(this._dataInt32=new Int32Array(this.data.buffer)),this._dataInt32}get static(){return !!(this.descriptor.usage&vt.STATIC)}set static(t){t?this.descriptor.usage|=vt.STATIC:this.descriptor.usage&=~vt.STATIC;}setDataWithSize(t,e,i){if(this._updateID++,this._updateSize=e*t.BYTES_PER_ELEMENT,this._data===t){i&&this.emit("update",this);return}const n=this._data;if(this._data=t,this._dataInt32=null,!n||n.length!==t.length){!this.shrinkToFit&&n&&t.byteLength<n.byteLength?i&&this.emit("update",this):(this.descriptor.size=t.byteLength,this._resourceId=rt("resource"),this.emit("change",this));return}i&&this.emit("update",this);}update(t){this._updateSize=t??this._updateSize,this._updateID++,this.emit("update",this);}destroy(){this.destroyed=!0,this.emit("destroy",this),this.emit("change",this),this._data=null,this.descriptor=null,this.removeAllListeners();}}function vo(s,t){if(!(s instanceof Xe)){let e=t?vt.INDEX:vt.VERTEX;s instanceof Array&&(t?(s=new Uint32Array(s),e=vt.INDEX|vt.COPY_DST):(s=new Float32Array(s),e=vt.VERTEX|vt.COPY_DST)),s=new Xe({data:s,label:t?"index-mesh-buffer":"vertex-mesh-buffer",usage:e});}return s}function jc(s,t,e){const i=s.getAttribute(t);if(!i)return e.minX=0,e.minY=0,e.maxX=0,e.maxY=0,e;const n=i.buffer.data;let r=1/0,o=1/0,a=-1/0,l=-1/0;const c=n.BYTES_PER_ELEMENT,h=(i.offset||0)/c,d=(i.stride||2*4)/c;for(let _=h;_<n.length;_+=d){const u=n[_],f=n[_+1];u>a&&(a=u),f>l&&(l=f),u<r&&(r=u),f<o&&(o=f);}return e.minX=r,e.minY=o,e.maxX=a,e.maxY=l,e}function Yc(s){return (s instanceof Xe||Array.isArray(s)||s.BYTES_PER_ELEMENT)&&(s={buffer:s}),s.buffer=vo(s.buffer,!1),s}class Xc extends Lt{constructor(t={}){super(),this.uid=rt("geometry"),this._layoutKey=0,this.instanceCount=1,this._bounds=new Ft,this._boundsDirty=!0;const{attributes:e,indexBuffer:i,topology:n}=t;if(this.buffers=[],this.attributes={},e)for(const r in e)this.addAttribute(r,e[r]);this.instanceCount=t.instanceCount??1,i&&this.addIndex(i),this.topology=n||"triangle-list";}onBufferUpdate(){this._boundsDirty=!0,this.emit("update",this);}getAttribute(t){return this.attributes[t]}getIndex(){return this.indexBuffer}getBuffer(t){return this.getAttribute(t).buffer}getSize(){for(const t in this.attributes){const e=this.attributes[t];return e.buffer.data.length/(e.stride/4||e.size)}return 0}addAttribute(t,e){const i=Yc(e);this.buffers.indexOf(i.buffer)===-1&&(this.buffers.push(i.buffer),i.buffer.on("update",this.onBufferUpdate,this),i.buffer.on("change",this.onBufferUpdate,this)),this.attributes[t]=i;}addIndex(t){this.indexBuffer=vo(t,!0),this.buffers.push(this.indexBuffer);}get bounds(){return this._boundsDirty?(this._boundsDirty=!1,jc(this,"aPosition",this._bounds)):this._bounds}destroy(t=!1){this.emit("destroy",this),this.removeAllListeners(),t&&this.buffers.forEach(e=>e.destroy()),this.attributes=null,this.buffers=null,this.indexBuffer=null,this._bounds=null;}}const Vc=new Float32Array(1),qc=new Uint32Array(1);class Qc extends Xc{constructor(){const e=new Xe({data:Vc,label:"attribute-batch-buffer",usage:vt.VERTEX|vt.COPY_DST,shrinkToFit:!1}),i=new Xe({data:qc,label:"index-batch-buffer",usage:vt.INDEX|vt.COPY_DST,shrinkToFit:!1}),n=6*4;super({attributes:{aPosition:{buffer:e,format:"float32x2",stride:n,offset:0},aUV:{buffer:e,format:"float32x2",stride:n,offset:2*4},aColor:{buffer:e,format:"unorm8x4",stride:n,offset:4*4},aTextureIdAndRound:{buffer:e,format:"uint16x2",stride:n,offset:5*4}},indexBuffer:i});}}function Ln(s,t,e){if(s)for(const i in s){const n=i.toLocaleLowerCase(),r=t[n];if(r){let o=s[i];i==="header"&&(o=o.replace(/@in\s+[^;]+;\s*/g,"").replace(/@out\s+[^;]+;\s*/g,"")),e&&r.push(`//----${e}----//`),r.push(o);}else ot(`${i} placement hook does not exist in shader`);}}const Zc=/\{\{(.*?)\}\}/g;function Wn(s){const t={};return (s.match(Zc)?.map(i=>i.replace(/[{()}]/g,""))??[]).forEach(i=>{t[i]=[];}),t}function Un(s,t){let e;const i=/@in\s+([^;]+);/g;for(;(e=i.exec(s))!==null;)t.push(e[1]);}function Dn(s,t,e=!1){const i=[];Un(t,i),s.forEach(a=>{a.header&&Un(a.header,i);});const n=i;e&&n.sort();const r=n.map((a,l)=>`       @location(${l}) ${a},`).join(`
`);let o=t.replace(/@in\s+[^;]+;\s*/g,"");return o=o.replace("{{in}}",`
${r}
`),o}function Kn(s,t){let e;const i=/@out\s+([^;]+);/g;for(;(e=i.exec(s))!==null;)t.push(e[1]);}function Jc(s){const e=/\b(\w+)\s*:/g.exec(s);return e?e[1]:""}function $c(s){const t=/@.*?\s+/g;return s.replace(t,"")}function th(s,t){const e=[];Kn(t,e),s.forEach(l=>{l.header&&Kn(l.header,e);});let i=0;const n=e.sort().map(l=>l.indexOf("builtin")>-1?l:`@location(${i++}) ${l}`).join(`,
`),r=e.sort().map(l=>`       var ${$c(l)};`).join(`
`),o=`return VSOutput(
            ${e.sort().map(l=>` ${Jc(l)}`).join(`,
`)});`;let a=t.replace(/@out\s+[^;]+;\s*/g,"");return a=a.replace("{{struct}}",`
${n}
`),a=a.replace("{{start}}",`
${r}
`),a=a.replace("{{return}}",`
${o}
`),a}function Nn(s,t){let e=s;for(const i in t){const n=t[i];n.join(`
`).length?e=e.replace(`{{${i}}}`,`//-----${i} START-----//
${n.join(`
`)}
//----${i} FINISH----//`):e=e.replace(`{{${i}}}`,"");}return e}const qt=Object.create(null),is=new Map;let eh=0;function ih({template:s,bits:t}){const e=wo(s,t);if(qt[e])return qt[e];const{vertex:i,fragment:n}=nh(s,t);return qt[e]=ko(i,n,t),qt[e]}function sh({template:s,bits:t}){const e=wo(s,t);return qt[e]||(qt[e]=ko(s.vertex,s.fragment,t)),qt[e]}function nh(s,t){const e=t.map(o=>o.vertex).filter(o=>!!o),i=t.map(o=>o.fragment).filter(o=>!!o);let n=Dn(e,s.vertex,!0);n=th(e,n);const r=Dn(i,s.fragment,!0);return {vertex:n,fragment:r}}function wo(s,t){return t.map(e=>(is.has(e)||is.set(e,eh++),is.get(e))).sort((e,i)=>e-i).join("-")+s.vertex+s.fragment}function ko(s,t,e){const i=Wn(s),n=Wn(t);return e.forEach(r=>{Ln(r.vertex,i,r.name),Ln(r.fragment,n,r.name);}),{vertex:Nn(s,i),fragment:Nn(t,n)}}const rh=`
    @in aPosition: vec2<f32>;
    @in aUV: vec2<f32>;

    @out @builtin(position) vPosition: vec4<f32>;
    @out vUV : vec2<f32>;
    @out vColor : vec4<f32>;

    {{header}}

    struct VSOutput {
        {{struct}}
    };

    @vertex
    fn main( {{in}} ) -> VSOutput {

        var worldTransformMatrix = globalUniforms.uWorldTransformMatrix;
        var modelMatrix = mat3x3<f32>(
            1.0, 0.0, 0.0,
            0.0, 1.0, 0.0,
            0.0, 0.0, 1.0
          );
        var position = aPosition;
        var uv = aUV;

        {{start}}
        
        vColor = vec4<f32>(1., 1., 1., 1.);

        {{main}}

        vUV = uv;

        var modelViewProjectionMatrix = globalUniforms.uProjectionMatrix * worldTransformMatrix * modelMatrix;

        vPosition =  vec4<f32>((modelViewProjectionMatrix *  vec3<f32>(position, 1.0)).xy, 0.0, 1.0);
       
        vColor *= globalUniforms.uWorldColorAlpha;

        {{end}}

        {{return}}
    };
`,oh=`
    @in vUV : vec2<f32>;
    @in vColor : vec4<f32>;
   
    {{header}}

    @fragment
    fn main(
        {{in}}
      ) -> @location(0) vec4<f32> {
        
        {{start}}

        var outColor:vec4<f32>;
      
        {{main}}
        
        var finalColor:vec4<f32> = outColor * vColor;

        {{end}}

        return finalColor;
      };
`,ah=`
    in vec2 aPosition;
    in vec2 aUV;

    out vec4 vColor;
    out vec2 vUV;

    {{header}}

    void main(void){

        mat3 worldTransformMatrix = uWorldTransformMatrix;
        mat3 modelMatrix = mat3(
            1.0, 0.0, 0.0,
            0.0, 1.0, 0.0,
            0.0, 0.0, 1.0
          );
        vec2 position = aPosition;
        vec2 uv = aUV;
        
        {{start}}
        
        vColor = vec4(1.);
        
        {{main}}
        
        vUV = uv;
        
        mat3 modelViewProjectionMatrix = uProjectionMatrix * worldTransformMatrix * modelMatrix;

        gl_Position = vec4((modelViewProjectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);

        vColor *= uWorldColorAlpha;

        {{end}}
    }
`,lh=`
   
    in vec4 vColor;
    in vec2 vUV;

    out vec4 finalColor;

    {{header}}

    void main(void) {
        
        {{start}}

        vec4 outColor;
      
        {{main}}
        
        finalColor = outColor * vColor;
        
        {{end}}
    }
`,ch={name:"global-uniforms-bit",vertex:{header:`
        struct GlobalUniforms {
            uProjectionMatrix:mat3x3<f32>,
            uWorldTransformMatrix:mat3x3<f32>,
            uWorldColorAlpha: vec4<f32>,
            uResolution: vec2<f32>,
        }

        @group(0) @binding(0) var<uniform> globalUniforms : GlobalUniforms;
        `}},hh={name:"global-uniforms-bit",vertex:{header:`
          uniform mat3 uProjectionMatrix;
          uniform mat3 uWorldTransformMatrix;
          uniform vec4 uWorldColorAlpha;
          uniform vec2 uResolution;
        `}};function dh({bits:s,name:t}){const e=ih({template:{fragment:oh,vertex:rh},bits:[ch,...s]});return Qt.from({name:t,vertex:{source:e.vertex,entryPoint:"main"},fragment:{source:e.fragment,entryPoint:"main"}})}function uh({bits:s,name:t}){return new Ce({name:t,...sh({template:{vertex:ah,fragment:lh},bits:[hh,...s]})})}const _h={name:"color-bit",vertex:{header:`
            @in aColor: vec4<f32>;
        `,main:`
            vColor *= vec4<f32>(aColor.rgb * aColor.a, aColor.a);
        `}},fh={name:"color-bit",vertex:{header:`
            in vec4 aColor;
        `,main:`
            vColor *= vec4(aColor.rgb * aColor.a, aColor.a);
        `}},ss={};function Ah(s){const t=[];if(s===1)t.push("@group(1) @binding(0) var textureSource1: texture_2d<f32>;"),t.push("@group(1) @binding(1) var textureSampler1: sampler;");else {let e=0;for(let i=0;i<s;i++)t.push(`@group(1) @binding(${e++}) var textureSource${i+1}: texture_2d<f32>;`),t.push(`@group(1) @binding(${e++}) var textureSampler${i+1}: sampler;`);}return t.join(`
`)}function ph(s){const t=[];if(s===1)t.push("outColor = textureSampleGrad(textureSource1, textureSampler1, vUV, uvDx, uvDy);");else {t.push("switch vTextureId {");for(let e=0;e<s;e++)e===s-1?t.push("  default:{"):t.push(`  case ${e}:{`),t.push(`      outColor = textureSampleGrad(textureSource${e+1}, textureSampler${e+1}, vUV, uvDx, uvDy);`),t.push("      break;}");t.push("}");}return t.join(`
`)}function mh(s){return ss[s]||(ss[s]={name:"texture-batch-bit",vertex:{header:`
                @in aTextureIdAndRound: vec2<u32>;
                @out @interpolate(flat) vTextureId : u32;
            `,main:`
                vTextureId = aTextureIdAndRound.y;
            `,end:`
                if(aTextureIdAndRound.x == 1)
                {
                    vPosition = vec4<f32>(roundPixels(vPosition.xy, globalUniforms.uResolution), vPosition.zw);
                }
            `},fragment:{header:`
                @in @interpolate(flat) vTextureId: u32;

                ${Ah(s)}
            `,main:`
                var uvDx = dpdx(vUV);
                var uvDy = dpdy(vUV);

                ${ph(s)}
            `}}),ss[s]}const ns={};function gh(s){const t=[];for(let e=0;e<s;e++)e>0&&t.push("else"),e<s-1&&t.push(`if(vTextureId < ${e}.5)`),t.push("{"),t.push(`	outColor = texture(uTextures[${e}], vUV);`),t.push("}");return t.join(`
`)}function yh(s){return ns[s]||(ns[s]={name:"texture-batch-bit",vertex:{header:`
                in vec2 aTextureIdAndRound;
                out float vTextureId;

            `,main:`
                vTextureId = aTextureIdAndRound.y;
            `,end:`
                if(aTextureIdAndRound.x == 1.)
                {
                    gl_Position.xy = roundPixels(gl_Position.xy, uResolution);
                }
            `},fragment:{header:`
                in float vTextureId;

                uniform sampler2D uTextures[${s}];

            `,main:`

                ${gh(s)}
            `}}),ns[s]}const xh={name:"round-pixels-bit",vertex:{header:`
            fn roundPixels(position: vec2<f32>, targetSize: vec2<f32>) -> vec2<f32> 
            {
                return (floor(((position * 0.5 + 0.5) * targetSize) + 0.5) / targetSize) * 2.0 - 1.0;
            }
        `}},bh={name:"round-pixels-bit",vertex:{header:`   
            vec2 roundPixels(vec2 position, vec2 targetSize)
            {       
                return (floor(((position * 0.5 + 0.5) * targetSize) + 0.5) / targetSize) * 2.0 - 1.0;
            }
        `}},Hn={};function Ch(s){let t=Hn[s];if(t)return t;const e=new Int32Array(s);for(let i=0;i<s;i++)e[i]=i;return t=Hn[s]=new io({uTextures:{value:e,type:"i32",size:s}},{isStatic:!0}),t}class vh extends Ci{constructor(t){const e=uh({name:"batch",bits:[fh,yh(t),bh]}),i=dh({name:"batch",bits:[_h,mh(t),xh]});super({glProgram:e,gpuProgram:i,resources:{batchSamplers:Ch(t)}});}}let jn=null;const Io=class Eo extends Hc{constructor(){super(...arguments),this.geometry=new Qc,this.shader=jn||(jn=new vh(this.maxTextures)),this.name=Eo.extension.name,this.vertexSize=6;}packAttributes(t,e,i,n,r){const o=r<<16|t.roundPixels&65535,a=t.transform,l=a.a,c=a.b,h=a.c,d=a.d,_=a.tx,u=a.ty,{positions:f,uvs:p}=t,A=t.color,m=t.attributeOffset,g=m+t.attributeSize;for(let y=m;y<g;y++){const x=y*2,v=f[x],b=f[x+1];e[n++]=l*v+h*b+_,e[n++]=d*b+c*v+u,e[n++]=p[x],e[n++]=p[x+1],i[n++]=A,i[n++]=o;}}packQuadAttributes(t,e,i,n,r){const o=t.texture,a=t.transform,l=a.a,c=a.b,h=a.c,d=a.d,_=a.tx,u=a.ty,f=t.bounds,p=f.maxX,A=f.minX,m=f.maxY,g=f.minY,y=o.uvs,x=t.color,v=r<<16|t.roundPixels&65535;e[n+0]=l*A+h*g+_,e[n+1]=d*g+c*A+u,e[n+2]=y.x0,e[n+3]=y.y0,i[n+4]=x,i[n+5]=v,e[n+6]=l*p+h*g+_,e[n+7]=d*g+c*p+u,e[n+8]=y.x1,e[n+9]=y.y1,i[n+10]=x,i[n+11]=v,e[n+12]=l*p+h*m+_,e[n+13]=d*m+c*p+u,e[n+14]=y.x2,e[n+15]=y.y2,i[n+16]=x,i[n+17]=v,e[n+18]=l*A+h*m+_,e[n+19]=d*m+c*A+u,e[n+20]=y.x3,e[n+21]=y.y3,i[n+22]=x,i[n+23]=v;}};Io.extension={type:[T.Batcher],name:"default"};let wh=Io;function kh(s,t,e,i,n,r,o,a=null){let l=0;e*=t,n*=r;const c=a.a,h=a.b,d=a.c,_=a.d,u=a.tx,f=a.ty;for(;l<o;){const p=s[e],A=s[e+1];i[n]=c*p+d*A+u,i[n+1]=h*p+_*A+f,n+=r,e+=t,l++;}}function Ih(s,t,e,i){let n=0;for(t*=e;n<i;)s[t]=0,s[t+1]=0,t+=e,n++;}function Mo(s,t,e,i,n){const r=t.a,o=t.b,a=t.c,l=t.d,c=t.tx,h=t.ty;e||(e=0),i||(i=2),n||(n=s.length/i-e);let d=e*i;for(let _=0;_<n;_++){const u=s[d],f=s[d+1];s[d]=r*u+a*f+c,s[d+1]=o*u+l*f+h,d+=i;}}const Eh=new K;class So{constructor(){this.packAsQuad=!1,this.batcherName="default",this.topology="triangle-list",this.applyTransform=!0,this.roundPixels=0,this._batcher=null,this._batch=null;}get uvs(){return this.geometryData.uvs}get positions(){return this.geometryData.vertices}get indices(){return this.geometryData.indices}get blendMode(){return this.applyTransform?this.renderable.groupBlendMode:"normal"}get color(){const t=this.baseColor,e=t>>16|t&65280|(t&255)<<16,i=this.renderable;return i?zr(e,i.groupColor)+(this.alpha*i.groupAlpha*255<<24):e+(this.alpha*255<<24)}get transform(){return this.renderable?.groupTransform||Eh}copyTo(t){t.indexOffset=this.indexOffset,t.indexSize=this.indexSize,t.attributeOffset=this.attributeOffset,t.attributeSize=this.attributeSize,t.baseColor=this.baseColor,t.alpha=this.alpha,t.texture=this.texture,t.geometryData=this.geometryData,t.topology=this.topology;}reset(){this.applyTransform=!0,this.renderable=null,this.topology="triangle-list";}}const Ve={extension:{type:T.ShapeBuilder,name:"circle"},build(s,t){let e,i,n,r,o,a;if(s.type==="circle"){const x=s;e=x.x,i=x.y,o=a=x.radius,n=r=0;}else if(s.type==="ellipse"){const x=s;e=x.x,i=x.y,o=x.halfWidth,a=x.halfHeight,n=r=0;}else {const x=s,v=x.width/2,b=x.height/2;e=x.x+v,i=x.y+b,o=a=Math.max(0,Math.min(x.radius,Math.min(v,b))),n=v-o,r=b-a;}if(!(o>=0&&a>=0&&n>=0&&r>=0))return t;const l=Math.ceil(2.3*Math.sqrt(o+a)),c=l*8+(n?4:0)+(r?4:0);if(c===0)return t;if(l===0)return t[0]=t[6]=e+n,t[1]=t[3]=i+r,t[2]=t[4]=e-n,t[5]=t[7]=i-r,t;let h=0,d=l*4+(n?2:0)+2,_=d,u=c,f=n+o,p=r,A=e+f,m=e-f,g=i+p;if(t[h++]=A,t[h++]=g,t[--d]=g,t[--d]=m,r){const x=i-p;t[_++]=m,t[_++]=x,t[--u]=x,t[--u]=A;}for(let x=1;x<l;x++){const v=Math.PI/2*(x/l),b=n+Math.cos(v)*o,C=r+Math.sin(v)*a,M=e+b,B=e-b,w=i+C,k=i-C;t[h++]=M,t[h++]=w,t[--d]=w,t[--d]=B,t[_++]=B,t[_++]=k,t[--u]=k,t[--u]=M;}f=n,p=r+a,A=e+f,m=e-f,g=i+p;const y=i-p;return t[h++]=A,t[h++]=g,t[--u]=y,t[--u]=A,n&&(t[h++]=m,t[h++]=g,t[--u]=y,t[--u]=m),t},triangulate(s,t,e,i,n,r){if(s.length===0)return;let o=0,a=0;for(let h=0;h<s.length;h+=2)o+=s[h],a+=s[h+1];o/=s.length/2,a/=s.length/2;let l=i;t[l*e]=o,t[l*e+1]=a;const c=l++;for(let h=0;h<s.length;h+=2)t[l*e]=s[h],t[l*e+1]=s[h+1],h>0&&(n[r++]=l,n[r++]=c,n[r++]=l-1),l++;n[r++]=c+1,n[r++]=c,n[r++]=l-1;}},Mh={...Ve,extension:{...Ve.extension,name:"ellipse"}},Sh={...Ve,extension:{...Ve.extension,name:"roundedRectangle"}},Bo=1e-4,Yn=1e-4;function Bh(s){const t=s.length;if(t<6)return 1;let e=0;for(let i=0,n=s[t-2],r=s[t-1];i<t;i+=2){const o=s[i],a=s[i+1];e+=(o-n)*(a+r),n=o,r=a;}return e<0?-1:1}function Xn(s,t,e,i,n,r,o,a){const l=s-e*n,c=t-i*n,h=s+e*r,d=t+i*r;let _,u;o?(_=i,u=-e):(_=-i,u=e);const f=l+_,p=c+u,A=h+_,m=d+u;return a.push(f,p),a.push(A,m),2}function ie(s,t,e,i,n,r,o,a){const l=e-s,c=i-t;let h=Math.atan2(l,c),d=Math.atan2(n-s,r-t);a&&h<d?h+=Math.PI*2:!a&&h>d&&(d+=Math.PI*2);let _=h;const u=d-h,f=Math.abs(u),p=Math.sqrt(l*l+c*c),A=(15*f*Math.sqrt(p)/Math.PI>>0)+1,m=u/A;if(_+=m,a){o.push(s,t),o.push(e,i);for(let g=1,y=_;g<A;g++,y+=m)o.push(s,t),o.push(s+Math.sin(y)*p,t+Math.cos(y)*p);o.push(s,t),o.push(n,r);}else {o.push(e,i),o.push(s,t);for(let g=1,y=_;g<A;g++,y+=m)o.push(s+Math.sin(y)*p,t+Math.cos(y)*p),o.push(s,t);o.push(n,r),o.push(s,t);}return A*2}function zh(s,t,e,i,n,r){const o=Bo;if(s.length===0)return;const a=t;let l=a.alignment;if(t.alignment!==.5){let I=Bh(s);l=(l-.5)*I+.5;}const c=new L(s[0],s[1]),h=new L(s[s.length-2],s[s.length-1]),d=i,_=Math.abs(c.x-h.x)<o&&Math.abs(c.y-h.y)<o;if(d){s=s.slice(),_&&(s.pop(),s.pop(),h.set(s[s.length-2],s[s.length-1]));const I=(c.x+h.x)*.5,at=(h.y+c.y)*.5;s.unshift(I,at),s.push(I,at);}const u=n,f=s.length/2;let p=s.length;const A=u.length/2,m=a.width/2,g=m*m,y=a.miterLimit*a.miterLimit;let x=s[0],v=s[1],b=s[2],C=s[3],M=0,B=0,w=-(v-C),k=x-b,S=0,U=0,V=Math.sqrt(w*w+k*k);w/=V,k/=V,w*=m,k*=m;const xt=l,z=(1-xt)*2,R=xt*2;d||(a.cap==="round"?p+=ie(x-w*(z-R)*.5,v-k*(z-R)*.5,x-w*z,v-k*z,x+w*R,v+k*R,u,!0)+2:a.cap==="square"&&(p+=Xn(x,v,w,k,z,R,!0,u))),u.push(x-w*z,v-k*z),u.push(x+w*R,v+k*R);for(let I=1;I<f-1;++I){x=s[(I-1)*2],v=s[(I-1)*2+1],b=s[I*2],C=s[I*2+1],M=s[(I+1)*2],B=s[(I+1)*2+1],w=-(v-C),k=x-b,V=Math.sqrt(w*w+k*k),w/=V,k/=V,w*=m,k*=m,S=-(C-B),U=b-M,V=Math.sqrt(S*S+U*U),S/=V,U/=V,S*=m,U*=m;const at=b-x,lt=v-C,q=b-M,ct=B-C,j=at*q+lt*ct,ut=lt*q-ct*at,bt=ut<0;if(Math.abs(ut)<.001*Math.abs(j)){u.push(b-w*z,C-k*z),u.push(b+w*R,C+k*R),j>=0&&(a.join==="round"?p+=ie(b,C,b-w*z,C-k*z,b-S*z,C-U*z,u,!1)+4:p+=2,u.push(b-S*R,C-U*R),u.push(b+S*z,C+U*z));continue}const F=(-w+x)*(-k+C)-(-w+b)*(-k+v),Dt=(-S+M)*(-U+C)-(-S+b)*(-U+B),_t=(at*Dt-q*F)/ut,Kt=(ct*F-lt*Dt)/ut,Bt=(_t-b)*(_t-b)+(Kt-C)*(Kt-C),$=b+(_t-b)*z,ft=C+(Kt-C)*z,Y=b-(_t-b)*R,At=C-(Kt-C)*R,Me=Math.min(at*at+lt*lt,q*q+ct*ct),X=bt?z:R,Se=Me+X*X*g;Bt<=Se?a.join==="bevel"||Bt/g>y?(bt?(u.push($,ft),u.push(b+w*R,C+k*R),u.push($,ft),u.push(b+S*R,C+U*R)):(u.push(b-w*z,C-k*z),u.push(Y,At),u.push(b-S*z,C-U*z),u.push(Y,At)),p+=2):a.join==="round"?bt?(u.push($,ft),u.push(b+w*R,C+k*R),p+=ie(b,C,b+w*R,C+k*R,b+S*R,C+U*R,u,!0)+4,u.push($,ft),u.push(b+S*R,C+U*R)):(u.push(b-w*z,C-k*z),u.push(Y,At),p+=ie(b,C,b-w*z,C-k*z,b-S*z,C-U*z,u,!1)+4,u.push(b-S*z,C-U*z),u.push(Y,At)):(u.push($,ft),u.push(Y,At)):(u.push(b-w*z,C-k*z),u.push(b+w*R,C+k*R),a.join==="round"?bt?p+=ie(b,C,b+w*R,C+k*R,b+S*R,C+U*R,u,!0)+2:p+=ie(b,C,b-w*z,C-k*z,b-S*z,C-U*z,u,!1)+2:a.join==="miter"&&Bt/g<=y&&(bt?(u.push(Y,At),u.push(Y,At)):(u.push($,ft),u.push($,ft)),p+=2),u.push(b-S*z,C-U*z),u.push(b+S*R,C+U*R),p+=2);}x=s[(f-2)*2],v=s[(f-2)*2+1],b=s[(f-1)*2],C=s[(f-1)*2+1],w=-(v-C),k=x-b,V=Math.sqrt(w*w+k*k),w/=V,k/=V,w*=m,k*=m,u.push(b-w*z,C-k*z),u.push(b+w*R,C+k*R),d||(a.cap==="round"?p+=ie(b-w*(z-R)*.5,C-k*(z-R)*.5,b-w*z,C-k*z,b+w*R,C+k*R,u,!1)+2:a.cap==="square"&&(p+=Xn(b,C,w,k,z,R,!1,u)));const Rt=Yn*Yn;for(let I=A;I<p+A-2;++I)x=u[I*2],v=u[I*2+1],b=u[(I+1)*2],C=u[(I+1)*2+1],M=u[(I+2)*2],B=u[(I+2)*2+1],!(Math.abs(x*(C-B)+b*(B-v)+M*(v-C))<Rt)&&r.push(I,I+1,I+2);}function Ph(s,t,e,i){const n=Bo;if(s.length===0)return;const r=s[0],o=s[1],a=s[s.length-2],l=s[s.length-1],c=t||Math.abs(r-a)<n&&Math.abs(o-l)<n,h=e,d=s.length/2,_=h.length/2;for(let u=0;u<d;u++)h.push(s[u*2]),h.push(s[u*2+1]);for(let u=0;u<d-1;u++)i.push(_+u,_+u+1);c&&i.push(_+d-1,_);}function zo(s,t,e,i,n,r,o){const a=xc(s,t,2);if(!a)return;for(let c=0;c<a.length;c+=3)r[o++]=a[c]+n,r[o++]=a[c+1]+n,r[o++]=a[c+2]+n;let l=n*i;for(let c=0;c<s.length;c+=2)e[l]=s[c],e[l+1]=s[c+1],l+=i;}const Th=[],Rh={extension:{type:T.ShapeBuilder,name:"polygon"},build(s,t){for(let e=0;e<s.points.length;e++)t[e]=s.points[e];return t},triangulate(s,t,e,i,n,r){zo(s,Th,t,e,i,n,r);}},Gh={extension:{type:T.ShapeBuilder,name:"rectangle"},build(s,t){const e=s,i=e.x,n=e.y,r=e.width,o=e.height;return r>=0&&o>=0&&(t[0]=i,t[1]=n,t[2]=i+r,t[3]=n,t[4]=i+r,t[5]=n+o,t[6]=i,t[7]=n+o),t},triangulate(s,t,e,i,n,r){let o=0;i*=e,t[i+o]=s[0],t[i+o+1]=s[1],o+=e,t[i+o]=s[2],t[i+o+1]=s[3],o+=e,t[i+o]=s[6],t[i+o+1]=s[7],o+=e,t[i+o]=s[4],t[i+o+1]=s[5],o+=e;const a=i/e;n[r++]=a,n[r++]=a+1,n[r++]=a+2,n[r++]=a+1,n[r++]=a+3,n[r++]=a+2;}},Oh={extension:{type:T.ShapeBuilder,name:"triangle"},build(s,t){return t[0]=s.x,t[1]=s.y,t[2]=s.x2,t[3]=s.y2,t[4]=s.x3,t[5]=s.y3,t},triangulate(s,t,e,i,n,r){let o=0;i*=e,t[i+o]=s[0],t[i+o+1]=s[1],o+=e,t[i+o]=s[2],t[i+o+1]=s[3],o+=e,t[i+o]=s[4],t[i+o+1]=s[5];const a=i/e;n[r++]=a,n[r++]=a+1,n[r++]=a+2;}},Ii={};yt.handleByMap(T.ShapeBuilder,Ii);yt.add(Gh,Rh,Oh,Ve,Mh,Sh);const Fh=new Q;function Lh(s,t){const{geometryData:e,batches:i}=t;i.length=0,e.indices.length=0,e.vertices.length=0,e.uvs.length=0;for(let n=0;n<s.instructions.length;n++){const r=s.instructions[n];if(r.action==="texture")Wh(r.data,i,e);else if(r.action==="fill"||r.action==="stroke"){const o=r.action==="stroke",a=r.data.path.shapePath,l=r.data.style,c=r.data.hole;o&&c&&Vn(c.shapePath,l,null,!0,i,e),Vn(a,l,c,o,i,e);}}}function Wh(s,t,e){const{vertices:i,uvs:n,indices:r}=e,o=r.length,a=i.length/2,l=[],c=Ii.rectangle,h=Fh,d=s.image;h.x=s.dx,h.y=s.dy,h.width=s.dw,h.height=s.dh;const _=s.transform;c.build(h,l),_&&Mo(l,_),c.triangulate(l,i,2,a,r,o);const u=d.uvs;n.push(u.x0,u.y0,u.x1,u.y1,u.x3,u.y3,u.x2,u.y2);const f=Yt.get(So);f.indexOffset=o,f.indexSize=r.length-o,f.attributeOffset=a,f.attributeSize=i.length/2-a,f.baseColor=s.style,f.alpha=s.alpha,f.texture=d,f.geometryData=e,t.push(f);}function Vn(s,t,e,i,n,r){const{vertices:o,uvs:a,indices:l}=r,c=s.shapePrimitives.length-1;s.shapePrimitives.forEach(({shape:h,transform:d},_)=>{const u=l.length,f=o.length/2,p=[],A=Ii[h.type];let m="triangle-list";if(A.build(h,p),d&&Mo(p,d),i){const v=h.closePath??!0,b=t;b.pixelLine?(Ph(p,v,o,l),m="line-list"):zh(p,b,!1,v,o,l);}else if(e&&c===_){c!==0&&console.warn("[Pixi Graphics] only the last shape have be cut out");const v=[],b=p.slice();Uh(e.shapePath).forEach(M=>{v.push(b.length/2),b.push(...M);}),zo(b,v,o,2,f,l,u);}else A.triangulate(p,o,2,f,l,u);const g=a.length/2,y=t.texture;if(y!==W.WHITE){const v=t.matrix;v&&(d&&v.append(d.clone().invert()),kh(o,2,f,a,g,2,o.length/2-f,v));}else Ih(a,g,2,o.length/2-f);const x=Yt.get(So);x.indexOffset=u,x.indexSize=l.length-u,x.attributeOffset=f,x.attributeSize=o.length/2-f,x.baseColor=t.color,x.alpha=t.alpha,x.texture=y,x.geometryData=r,x.topology=m,n.push(x);});}function Uh(s){if(!s)return [];const t=s.shapePrimitives,e=[];for(let i=0;i<t.length;i++){const n=t[i].shape,r=[];Ii[n.type].build(n,r),e.push(r);}return e}class Dh{constructor(){this.batches=[],this.geometryData={vertices:[],uvs:[],indices:[]};}}class Kh{constructor(){this.batcher=new wh,this.instructions=new Gr;}init(){this.instructions.reset();}get geometry(){return D(Ka,"GraphicsContextRenderData#geometry is deprecated, please use batcher.geometry instead."),this.batcher.geometry}}const Zs=class zs{constructor(t){this._gpuContextHash={},this._graphicsDataContextHash=Object.create(null),t.renderableGC.addManagedHash(this,"_gpuContextHash"),t.renderableGC.addManagedHash(this,"_graphicsDataContextHash");}init(t){zs.defaultOptions.bezierSmoothness=t?.bezierSmoothness??zs.defaultOptions.bezierSmoothness;}getContextRenderData(t){return this._graphicsDataContextHash[t.uid]||this._initContextRenderData(t)}updateGpuContext(t){let e=this._gpuContextHash[t.uid]||this._initContext(t);if(t.dirty){e?this._cleanGraphicsContextData(t):e=this._initContext(t),Lh(t,e);const i=t.batchMode;t.customShader||i==="no-batch"?e.isBatchable=!1:i==="auto"&&(e.isBatchable=e.geometryData.vertices.length<400),t.dirty=!1;}return e}getGpuContext(t){return this._gpuContextHash[t.uid]||this._initContext(t)}_initContextRenderData(t){const e=Yt.get(Kh),{batches:i,geometryData:n}=this._gpuContextHash[t.uid],r=n.vertices.length,o=n.indices.length;for(let h=0;h<i.length;h++)i[h].applyTransform=!1;const a=e.batcher;a.ensureAttributeBuffer(r),a.ensureIndexBuffer(o),a.begin();for(let h=0;h<i.length;h++){const d=i[h];a.add(d);}a.finish(e.instructions);const l=a.geometry;l.indexBuffer.setDataWithSize(a.indexBuffer,a.indexSize,!0),l.buffers[0].setDataWithSize(a.attributeBuffer.float32View,a.attributeSize,!0);const c=a.batches;for(let h=0;h<c.length;h++){const d=c[h];d.bindGroup=Lc(d.textures.textures,d.textures.count);}return this._graphicsDataContextHash[t.uid]=e,e}_initContext(t){const e=new Dh;return e.context=t,this._gpuContextHash[t.uid]=e,t.on("destroy",this.onGraphicsContextDestroy,this),this._gpuContextHash[t.uid]}onGraphicsContextDestroy(t){this._cleanGraphicsContextData(t),t.off("destroy",this.onGraphicsContextDestroy,this),this._gpuContextHash[t.uid]=null;}_cleanGraphicsContextData(t){const e=this._gpuContextHash[t.uid];e.isBatchable||this._graphicsDataContextHash[t.uid]&&(Yt.return(this.getContextRenderData(t)),this._graphicsDataContextHash[t.uid]=null),e.batches&&e.batches.forEach(i=>{Yt.return(i);});}destroy(){for(const t in this._gpuContextHash)this._gpuContextHash[t]&&this.onGraphicsContextDestroy(this._gpuContextHash[t].context);}};Zs.extension={type:[T.WebGLSystem,T.WebGPUSystem,T.CanvasSystem],name:"graphicsContext"};Zs.defaultOptions={bezierSmoothness:.5};let Po=Zs;const Nh=8,li=11920929e-14,Hh=1;function To(s,t,e,i,n,r,o,a,l,c){const d=Math.min(.99,Math.max(0,c??Po.defaultOptions.bezierSmoothness));let _=(Hh-d)/1;return _*=_,jh(t,e,i,n,r,o,a,l,s,_),s}function jh(s,t,e,i,n,r,o,a,l,c){Ps(s,t,e,i,n,r,o,a,l,c,0),l.push(o,a);}function Ps(s,t,e,i,n,r,o,a,l,c,h){if(h>Nh)return;const d=(s+e)/2,_=(t+i)/2,u=(e+n)/2,f=(i+r)/2,p=(n+o)/2,A=(r+a)/2,m=(d+u)/2,g=(_+f)/2,y=(u+p)/2,x=(f+A)/2,v=(m+y)/2,b=(g+x)/2;if(h>0){let C=o-s,M=a-t;const B=Math.abs((e-o)*M-(i-a)*C),w=Math.abs((n-o)*M-(r-a)*C);if(B>li&&w>li){if((B+w)*(B+w)<=c*(C*C+M*M)){l.push(v,b);return}}else if(B>li){if(B*B<=c*(C*C+M*M)){l.push(v,b);return}}else if(w>li){if(w*w<=c*(C*C+M*M)){l.push(v,b);return}}else if(C=v-(s+o)/2,M=b-(t+a)/2,C*C+M*M<=c){l.push(v,b);return}}Ps(s,t,d,_,m,g,v,b,l,c,h+1),Ps(v,b,y,x,p,A,o,a,l,c,h+1);}const Yh=8,Xh=11920929e-14,Vh=1;function qh(s,t,e,i,n,r,o,a){const c=Math.min(.99,Math.max(0,a??Po.defaultOptions.bezierSmoothness));let h=(Vh-c)/1;return h*=h,Qh(t,e,i,n,r,o,s,h),s}function Qh(s,t,e,i,n,r,o,a){Ts(o,s,t,e,i,n,r,a,0),o.push(n,r);}function Ts(s,t,e,i,n,r,o,a,l){if(l>Yh)return;const c=(t+i)/2,h=(e+n)/2,d=(i+r)/2,_=(n+o)/2,u=(c+d)/2,f=(h+_)/2;let p=r-t,A=o-e;const m=Math.abs((i-r)*A-(n-o)*p);if(m>Xh){if(m*m<=a*(p*p+A*A)){s.push(u,f);return}}else if(p=u-(t+r)/2,A=f-(e+o)/2,p*p+A*A<=a){s.push(u,f);return}Ts(s,t,e,c,h,u,f,a,l+1),Ts(s,u,f,d,_,r,o,a,l+1);}function Ro(s,t,e,i,n,r,o,a){let l=Math.abs(n-r);(!o&&n>r||o&&r>n)&&(l=2*Math.PI-l),a||(a=Math.max(6,Math.floor(6*Math.pow(i,1/3)*(l/Math.PI)))),a=Math.max(a,3);let c=l/a,h=n;c*=o?-1:1;for(let d=0;d<a+1;d++){const _=Math.cos(h),u=Math.sin(h),f=t+_*i,p=e+u*i;s.push(f,p),h+=c;}}function Zh(s,t,e,i,n,r){const o=s[s.length-2],l=s[s.length-1]-e,c=o-t,h=n-e,d=i-t,_=Math.abs(l*d-c*h);if(_<1e-8||r===0){(s[s.length-2]!==t||s[s.length-1]!==e)&&s.push(t,e);return}const u=l*l+c*c,f=h*h+d*d,p=l*h+c*d,A=r*Math.sqrt(u)/_,m=r*Math.sqrt(f)/_,g=A*p/u,y=m*p/f,x=A*d+m*c,v=A*h+m*l,b=c*(m+g),C=l*(m+g),M=d*(A+y),B=h*(A+y),w=Math.atan2(C-v,b-x),k=Math.atan2(B-v,M-x);Ro(s,x+t,v+e,r,w,k,c*h>d*l);}const De=Math.PI*2,rs={centerX:0,centerY:0,ang1:0,ang2:0},os=({x:s,y:t},e,i,n,r,o,a,l)=>{s*=e,t*=i;const c=n*s-r*t,h=r*s+n*t;return l.x=c+o,l.y=h+a,l};function Jh(s,t){const e=t===-1.5707963267948966?-.551915024494:1.3333333333333333*Math.tan(t/4),i=t===1.5707963267948966?.551915024494:e,n=Math.cos(s),r=Math.sin(s),o=Math.cos(s+t),a=Math.sin(s+t);return [{x:n-r*i,y:r+n*i},{x:o+a*i,y:a-o*i},{x:o,y:a}]}const qn=(s,t,e,i)=>{const n=s*i-t*e<0?-1:1;let r=s*e+t*i;return r>1&&(r=1),r<-1&&(r=-1),n*Math.acos(r)},$h=(s,t,e,i,n,r,o,a,l,c,h,d,_)=>{const u=Math.pow(n,2),f=Math.pow(r,2),p=Math.pow(h,2),A=Math.pow(d,2);let m=u*f-u*A-f*p;m<0&&(m=0),m/=u*A+f*p,m=Math.sqrt(m)*(o===a?-1:1);const g=m*n/r*d,y=m*-r/n*h,x=c*g-l*y+(s+e)/2,v=l*g+c*y+(t+i)/2,b=(h-g)/n,C=(d-y)/r,M=(-h-g)/n,B=(-d-y)/r,w=qn(1,0,b,C);let k=qn(b,C,M,B);a===0&&k>0&&(k-=De),a===1&&k<0&&(k+=De),_.centerX=x,_.centerY=v,_.ang1=w,_.ang2=k;};function td(s,t,e,i,n,r,o,a=0,l=0,c=0){if(r===0||o===0)return;const h=Math.sin(a*De/360),d=Math.cos(a*De/360),_=d*(t-i)/2+h*(e-n)/2,u=-h*(t-i)/2+d*(e-n)/2;if(_===0&&u===0)return;r=Math.abs(r),o=Math.abs(o);const f=Math.pow(_,2)/Math.pow(r,2)+Math.pow(u,2)/Math.pow(o,2);f>1&&(r*=Math.sqrt(f),o*=Math.sqrt(f)),$h(t,e,i,n,r,o,l,c,h,d,_,u,rs);let{ang1:p,ang2:A}=rs;const{centerX:m,centerY:g}=rs;let y=Math.abs(A)/(De/4);Math.abs(1-y)<1e-7&&(y=1);const x=Math.max(Math.ceil(y),1);A/=x;let v=s[s.length-2],b=s[s.length-1];const C={x:0,y:0};for(let M=0;M<x;M++){const B=Jh(p,A),{x:w,y:k}=os(B[0],r,o,d,h,m,g,C),{x:S,y:U}=os(B[1],r,o,d,h,m,g,C),{x:V,y:xt}=os(B[2],r,o,d,h,m,g,C);To(s,v,b,w,k,S,U,V,xt),v=V,b=xt,p+=A;}}function ed(s,t,e){const i=(o,a)=>{const l=a.x-o.x,c=a.y-o.y,h=Math.sqrt(l*l+c*c),d=l/h,_=c/h;return {len:h,nx:d,ny:_}},n=(o,a)=>{o===0?s.moveTo(a.x,a.y):s.lineTo(a.x,a.y);};let r=t[t.length-1];for(let o=0;o<t.length;o++){const a=t[o%t.length],l=a.radius??e;if(l<=0){n(o,a),r=a;continue}const c=t[(o+1)%t.length],h=i(a,r),d=i(a,c);if(h.len<1e-4||d.len<1e-4){n(o,a),r=a;continue}let _=Math.asin(h.nx*d.ny-h.ny*d.nx),u=1,f=!1;h.nx*d.nx-h.ny*-d.ny<0?_<0?_=Math.PI+_:(_=Math.PI-_,u=-1,f=!0):_>0&&(u=-1,f=!0);const p=_/2;let A,m=Math.abs(Math.cos(p)*l/Math.sin(p));m>Math.min(h.len/2,d.len/2)?(m=Math.min(h.len/2,d.len/2),A=Math.abs(m*Math.sin(p)/Math.cos(p))):A=l;const g=a.x+d.nx*m+-d.ny*A*u,y=a.y+d.ny*m+d.nx*A*u,x=Math.atan2(h.ny,h.nx)+Math.PI/2*u,v=Math.atan2(d.ny,d.nx)-Math.PI/2*u;o===0&&s.moveTo(g+Math.cos(x)*A,y+Math.sin(x)*A),s.arc(g,y,A,x,v,f),r=a;}}function id(s,t,e,i){const n=(a,l)=>Math.sqrt((a.x-l.x)**2+(a.y-l.y)**2),r=(a,l,c)=>({x:a.x+(l.x-a.x)*c,y:a.y+(l.y-a.y)*c}),o=t.length;for(let a=0;a<o;a++){const l=t[(a+1)%o],c=l.radius??e;if(c<=0){a===0?s.moveTo(l.x,l.y):s.lineTo(l.x,l.y);continue}const h=t[a],d=t[(a+2)%o],_=n(h,l);let u;if(_<1e-4)u=l;else {const A=Math.min(_/2,c);u=r(l,h,A/_);}const f=n(d,l);let p;if(f<1e-4)p=l;else {const A=Math.min(f/2,c);p=r(l,d,A/f);}a===0?s.moveTo(u.x,u.y):s.lineTo(u.x,u.y),s.quadraticCurveTo(l.x,l.y,p.x,p.y,i);}}const sd=new Q;class nd{constructor(t){this.shapePrimitives=[],this._currentPoly=null,this._bounds=new Ft,this._graphicsPath2D=t;}moveTo(t,e){return this.startPoly(t,e),this}lineTo(t,e){this._ensurePoly();const i=this._currentPoly.points,n=i[i.length-2],r=i[i.length-1];return (n!==t||r!==e)&&i.push(t,e),this}arc(t,e,i,n,r,o){this._ensurePoly(!1);const a=this._currentPoly.points;return Ro(a,t,e,i,n,r,o),this}arcTo(t,e,i,n,r){this._ensurePoly();const o=this._currentPoly.points;return Zh(o,t,e,i,n,r),this}arcToSvg(t,e,i,n,r,o,a){const l=this._currentPoly.points;return td(l,this._currentPoly.lastX,this._currentPoly.lastY,o,a,t,e,i,n,r),this}bezierCurveTo(t,e,i,n,r,o,a){this._ensurePoly();const l=this._currentPoly;return To(this._currentPoly.points,l.lastX,l.lastY,t,e,i,n,r,o,a),this}quadraticCurveTo(t,e,i,n,r){this._ensurePoly();const o=this._currentPoly;return qh(this._currentPoly.points,o.lastX,o.lastY,t,e,i,n,r),this}closePath(){return this.endPoly(!0),this}addPath(t,e){this.endPoly(),e&&!e.isIdentity()&&(t=t.clone(!0),t.transform(e));for(let i=0;i<t.instructions.length;i++){const n=t.instructions[i];this[n.action](...n.data);}return this}finish(t=!1){this.endPoly(t);}rect(t,e,i,n,r){return this.drawShape(new Q(t,e,i,n),r),this}circle(t,e,i,n){return this.drawShape(new Vs(t,e,i),n),this}poly(t,e,i){const n=new Ue(t);return n.closePath=e,this.drawShape(n,i),this}regularPoly(t,e,i,n,r=0,o){n=Math.max(n|0,3);const a=-1*Math.PI/2+r,l=Math.PI*2/n,c=[];for(let h=0;h<n;h++){const d=a-h*l;c.push(t+i*Math.cos(d),e+i*Math.sin(d));}return this.poly(c,!0,o),this}roundPoly(t,e,i,n,r,o=0,a){if(n=Math.max(n|0,3),r<=0)return this.regularPoly(t,e,i,n,o);const l=i*Math.sin(Math.PI/n)-.001;r=Math.min(r,l);const c=-1*Math.PI/2+o,h=Math.PI*2/n,d=(n-2)*Math.PI/n/2;for(let _=0;_<n;_++){const u=_*h+c,f=t+i*Math.cos(u),p=e+i*Math.sin(u),A=u+Math.PI+d,m=u-Math.PI-d,g=f+r*Math.cos(A),y=p+r*Math.sin(A),x=f+r*Math.cos(m),v=p+r*Math.sin(m);_===0?this.moveTo(g,y):this.lineTo(g,y),this.quadraticCurveTo(f,p,x,v,a);}return this.closePath()}roundShape(t,e,i=!1,n){return t.length<3?this:(i?id(this,t,e,n):ed(this,t,e),this.closePath())}filletRect(t,e,i,n,r){if(r===0)return this.rect(t,e,i,n);const o=Math.min(i,n)/2,a=Math.min(o,Math.max(-o,r)),l=t+i,c=e+n,h=a<0?-a:0,d=Math.abs(a);return this.moveTo(t,e+d).arcTo(t+h,e+h,t+d,e,d).lineTo(l-d,e).arcTo(l-h,e+h,l,e+d,d).lineTo(l,c-d).arcTo(l-h,c-h,t+i-d,c,d).lineTo(t+d,c).arcTo(t+h,c-h,t,c-d,d).closePath()}chamferRect(t,e,i,n,r,o){if(r<=0)return this.rect(t,e,i,n);const a=Math.min(r,Math.min(i,n)/2),l=t+i,c=e+n,h=[t+a,e,l-a,e,l,e+a,l,c-a,l-a,c,t+a,c,t,c-a,t,e+a];for(let d=h.length-1;d>=2;d-=2)h[d]===h[d-2]&&h[d-1]===h[d-3]&&h.splice(d-1,2);return this.poly(h,!0,o)}ellipse(t,e,i,n,r){return this.drawShape(new qs(t,e,i,n),r),this}roundRect(t,e,i,n,r,o){return this.drawShape(new Qs(t,e,i,n,r),o),this}drawShape(t,e){return this.endPoly(),this.shapePrimitives.push({shape:t,transform:e}),this}startPoly(t,e){let i=this._currentPoly;return i&&this.endPoly(),i=new Ue,i.points.push(t,e),this._currentPoly=i,this}endPoly(t=!1){const e=this._currentPoly;return e&&e.points.length>2&&(e.closePath=t,this.shapePrimitives.push({shape:e})),this._currentPoly=null,this}_ensurePoly(t=!0){if(!this._currentPoly&&(this._currentPoly=new Ue,t)){const e=this.shapePrimitives[this.shapePrimitives.length-1];if(e){let i=e.shape.x,n=e.shape.y;if(e.transform&&!e.transform.isIdentity()){const r=e.transform,o=i;i=r.a*i+r.c*n+r.tx,n=r.b*o+r.d*n+r.ty;}this._currentPoly.points.push(i,n);}else this._currentPoly.points.push(0,0);}}buildPath(){const t=this._graphicsPath2D;this.shapePrimitives.length=0,this._currentPoly=null;for(let e=0;e<t.instructions.length;e++){const i=t.instructions[e];this[i.action](...i.data);}this.finish();}get bounds(){const t=this._bounds;t.clear();const e=this.shapePrimitives;for(let i=0;i<e.length;i++){const n=e[i],r=n.shape.getBounds(sd);n.transform?t.addRect(r,n.transform):t.addRect(r);}return t}}class ve{constructor(t){this.instructions=[],this.uid=rt("graphicsPath"),this._dirty=!0,typeof t=="string"?Tc(t,this):this.instructions=t?.slice()??[];}get shapePath(){return this._shapePath||(this._shapePath=new nd(this)),this._dirty&&(this._dirty=!1,this._shapePath.buildPath()),this._shapePath}addPath(t,e){return t=t.clone(),this.instructions.push({action:"addPath",data:[t,e]}),this._dirty=!0,this}arc(...t){return this.instructions.push({action:"arc",data:t}),this._dirty=!0,this}arcTo(...t){return this.instructions.push({action:"arcTo",data:t}),this._dirty=!0,this}arcToSvg(...t){return this.instructions.push({action:"arcToSvg",data:t}),this._dirty=!0,this}bezierCurveTo(...t){return this.instructions.push({action:"bezierCurveTo",data:t}),this._dirty=!0,this}bezierCurveToShort(t,e,i,n,r){const o=this.instructions[this.instructions.length-1],a=this.getLastPoint(L.shared);let l=0,c=0;if(!o||o.action!=="bezierCurveTo")l=a.x,c=a.y;else {l=o.data[2],c=o.data[3];const h=a.x,d=a.y;l=h+(h-l),c=d+(d-c);}return this.instructions.push({action:"bezierCurveTo",data:[l,c,t,e,i,n,r]}),this._dirty=!0,this}closePath(){return this.instructions.push({action:"closePath",data:[]}),this._dirty=!0,this}ellipse(...t){return this.instructions.push({action:"ellipse",data:t}),this._dirty=!0,this}lineTo(...t){return this.instructions.push({action:"lineTo",data:t}),this._dirty=!0,this}moveTo(...t){return this.instructions.push({action:"moveTo",data:t}),this}quadraticCurveTo(...t){return this.instructions.push({action:"quadraticCurveTo",data:t}),this._dirty=!0,this}quadraticCurveToShort(t,e,i){const n=this.instructions[this.instructions.length-1],r=this.getLastPoint(L.shared);let o=0,a=0;if(!n||n.action!=="quadraticCurveTo")o=r.x,a=r.y;else {o=n.data[0],a=n.data[1];const l=r.x,c=r.y;o=l+(l-o),a=c+(c-a);}return this.instructions.push({action:"quadraticCurveTo",data:[o,a,t,e,i]}),this._dirty=!0,this}rect(t,e,i,n,r){return this.instructions.push({action:"rect",data:[t,e,i,n,r]}),this._dirty=!0,this}circle(t,e,i,n){return this.instructions.push({action:"circle",data:[t,e,i,n]}),this._dirty=!0,this}roundRect(...t){return this.instructions.push({action:"roundRect",data:t}),this._dirty=!0,this}poly(...t){return this.instructions.push({action:"poly",data:t}),this._dirty=!0,this}regularPoly(...t){return this.instructions.push({action:"regularPoly",data:t}),this._dirty=!0,this}roundPoly(...t){return this.instructions.push({action:"roundPoly",data:t}),this._dirty=!0,this}roundShape(...t){return this.instructions.push({action:"roundShape",data:t}),this._dirty=!0,this}filletRect(...t){return this.instructions.push({action:"filletRect",data:t}),this._dirty=!0,this}chamferRect(...t){return this.instructions.push({action:"chamferRect",data:t}),this._dirty=!0,this}star(t,e,i,n,r,o,a){r||(r=n/2);const l=-1*Math.PI/2+o,c=i*2,h=Math.PI*2/c,d=[];for(let _=0;_<c;_++){const u=_%2?r:n,f=_*h+l;d.push(t+u*Math.cos(f),e+u*Math.sin(f));}return this.poly(d,!0,a),this}clone(t=!1){const e=new ve;if(!t)e.instructions=this.instructions.slice();else for(let i=0;i<this.instructions.length;i++){const n=this.instructions[i];e.instructions.push({action:n.action,data:n.data.slice()});}return e}clear(){return this.instructions.length=0,this._dirty=!0,this}transform(t){if(t.isIdentity())return this;const e=t.a,i=t.b,n=t.c,r=t.d,o=t.tx,a=t.ty;let l=0,c=0,h=0,d=0,_=0,u=0,f=0,p=0;for(let A=0;A<this.instructions.length;A++){const m=this.instructions[A],g=m.data;switch(m.action){case"moveTo":case"lineTo":l=g[0],c=g[1],g[0]=e*l+n*c+o,g[1]=i*l+r*c+a;break;case"bezierCurveTo":h=g[0],d=g[1],_=g[2],u=g[3],l=g[4],c=g[5],g[0]=e*h+n*d+o,g[1]=i*h+r*d+a,g[2]=e*_+n*u+o,g[3]=i*_+r*u+a,g[4]=e*l+n*c+o,g[5]=i*l+r*c+a;break;case"quadraticCurveTo":h=g[0],d=g[1],l=g[2],c=g[3],g[0]=e*h+n*d+o,g[1]=i*h+r*d+a,g[2]=e*l+n*c+o,g[3]=i*l+r*c+a;break;case"arcToSvg":l=g[5],c=g[6],f=g[0],p=g[1],g[0]=e*f+n*p,g[1]=i*f+r*p,g[5]=e*l+n*c+o,g[6]=i*l+r*c+a;break;case"circle":g[4]=Ge(g[3],t);break;case"rect":g[4]=Ge(g[4],t);break;case"ellipse":g[8]=Ge(g[8],t);break;case"roundRect":g[5]=Ge(g[5],t);break;case"addPath":g[0].transform(t);break;case"poly":g[2]=Ge(g[2],t);break;default:ot("unknown transform action",m.action);break}}return this._dirty=!0,this}get bounds(){return this.shapePath.bounds}getLastPoint(t){let e=this.instructions.length-1,i=this.instructions[e];if(!i)return t.x=0,t.y=0,t;for(;i.action==="closePath";){if(e--,e<0)return t.x=0,t.y=0,t;i=this.instructions[e];}switch(i.action){case"moveTo":case"lineTo":t.x=i.data[0],t.y=i.data[1];break;case"quadraticCurveTo":t.x=i.data[2],t.y=i.data[3];break;case"bezierCurveTo":t.x=i.data[4],t.y=i.data[5];break;case"arc":case"arcToSvg":t.x=i.data[5],t.y=i.data[6];break;case"addPath":i.data[0].getLastPoint(t);break}return t}}function Ge(s,t){return s?s.prepend(t):t.clone()}function rd(s,t){if(typeof s=="string"){const i=document.createElement("div");i.innerHTML=s.trim(),s=i.querySelector("svg");}const e={context:t,path:new ve};return Go(s,e,null,null),t}function Go(s,t,e,i){const n=s.children,{fillStyle:r,strokeStyle:o}=od(s);r&&e?e={...e,...r}:r&&(e=r),o&&i?i={...i,...o}:o&&(i=o),t.context.fillStyle=e,t.context.strokeStyle=i;let a,l,c,h,d,_,u,f,p,A,m,g,y,x,v,b,C;switch(s.nodeName.toLowerCase()){case"path":x=s.getAttribute("d"),v=new ve(x),t.context.path(v),e&&t.context.fill(),i&&t.context.stroke();break;case"circle":u=ht(s,"cx",0),f=ht(s,"cy",0),p=ht(s,"r",0),t.context.ellipse(u,f,p,p),e&&t.context.fill(),i&&t.context.stroke();break;case"rect":a=ht(s,"x",0),l=ht(s,"y",0),b=ht(s,"width",0),C=ht(s,"height",0),A=ht(s,"rx",0),m=ht(s,"ry",0),A||m?t.context.roundRect(a,l,b,C,A||m):t.context.rect(a,l,b,C),e&&t.context.fill(),i&&t.context.stroke();break;case"ellipse":u=ht(s,"cx",0),f=ht(s,"cy",0),A=ht(s,"rx",0),m=ht(s,"ry",0),t.context.beginPath(),t.context.ellipse(u,f,A,m),e&&t.context.fill(),i&&t.context.stroke();break;case"line":c=ht(s,"x1",0),h=ht(s,"y1",0),d=ht(s,"x2",0),_=ht(s,"y2",0),t.context.beginPath(),t.context.moveTo(c,h),t.context.lineTo(d,_),i&&t.context.stroke();break;case"polygon":y=s.getAttribute("points"),g=y.match(/\d+/g).map(M=>parseInt(M,10)),t.context.poly(g,!0),e&&t.context.fill(),i&&t.context.stroke();break;case"polyline":y=s.getAttribute("points"),g=y.match(/\d+/g).map(M=>parseInt(M,10)),t.context.poly(g,!1),i&&t.context.stroke();break;case"g":case"svg":break;default:{console.info(`[SVG parser] <${s.nodeName}> elements unsupported`);break}}for(let M=0;M<n.length;M++)Go(n[M],t,e,i);}function ht(s,t,e){const i=s.getAttribute(t);return i?Number(i):e}function od(s){const t=s.getAttribute("style"),e={},i={};let n=!1,r=!1;if(t){const o=t.split(";");for(let a=0;a<o.length;a++){const l=o[a],[c,h]=l.split(":");switch(c){case"stroke":h!=="none"&&(e.color=st.shared.setValue(h).toNumber(),r=!0);break;case"stroke-width":e.width=Number(h);break;case"fill":h!=="none"&&(n=!0,i.color=st.shared.setValue(h).toNumber());break;case"fill-opacity":i.alpha=Number(h);break;case"stroke-opacity":e.alpha=Number(h);break;case"opacity":i.alpha=Number(h),e.alpha=Number(h);break}}}else {const o=s.getAttribute("stroke");o&&o!=="none"&&(r=!0,e.color=st.shared.setValue(o).toNumber(),e.width=ht(s,"stroke-width",1));const a=s.getAttribute("fill");a&&a!=="none"&&(n=!0,i.color=st.shared.setValue(a).toNumber());}return {strokeStyle:r?e:null,fillStyle:n?i:null}}function ad(s){return st.isColorLike(s)}function Qn(s){return s instanceof ki}function Zn(s){return s instanceof Ye}function ld(s,t,e){const i=st.shared.setValue(t??0);return s.color=i.toNumber(),s.alpha=i.alpha===1?e.alpha:i.alpha,s.texture=W.WHITE,{...e,...s}}function Jn(s,t,e){return s.fill=t,s.color=16777215,s.texture=t.texture,s.matrix=t.transform,{...e,...s}}function $n(s,t,e){return t.buildLinearGradient(),s.fill=t,s.color=16777215,s.texture=t.texture,s.matrix=t.transform,{...e,...s}}function cd(s,t){const e={...t,...s};if(e.texture){if(e.texture!==W.WHITE){const r=e.matrix?.clone().invert()||new K;r.translate(e.texture.frame.x,e.texture.frame.y),r.scale(1/e.texture.source.width,1/e.texture.source.height),e.matrix=r;}const n=e.texture.source.style;n.addressMode==="clamp-to-edge"&&(n.addressMode="repeat",n.update());}const i=st.shared.setValue(e.color);return e.alpha*=i.alpha,e.color=i.toNumber(),e.matrix=e.matrix?e.matrix.clone():null,e}function le(s,t){if(s==null)return null;const e={},i=s;return ad(s)?ld(e,s,t):Qn(s)?Jn(e,s,t):Zn(s)?$n(e,s,t):i.fill&&Qn(i.fill)?Jn(i,i.fill,t):i.fill&&Zn(i.fill)?$n(i,i.fill,t):cd(i,t)}function xi(s,t){const{width:e,alignment:i,miterLimit:n,cap:r,join:o,pixelLine:a,...l}=t,c=le(s,l);return c?{width:e,alignment:i,miterLimit:n,cap:r,join:o,pixelLine:a,...c}:null}const hd=new L,tr=new K,Js=class Ht extends Lt{constructor(){super(...arguments),this.uid=rt("graphicsContext"),this.dirty=!0,this.batchMode="auto",this.instructions=[],this._activePath=new ve,this._transform=new K,this._fillStyle={...Ht.defaultFillStyle},this._strokeStyle={...Ht.defaultStrokeStyle},this._stateStack=[],this._tick=0,this._bounds=new Ft,this._boundsDirty=!0;}clone(){const t=new Ht;return t.batchMode=this.batchMode,t.instructions=this.instructions.slice(),t._activePath=this._activePath.clone(),t._transform=this._transform.clone(),t._fillStyle={...this._fillStyle},t._strokeStyle={...this._strokeStyle},t._stateStack=this._stateStack.slice(),t._bounds=this._bounds.clone(),t._boundsDirty=!0,t}get fillStyle(){return this._fillStyle}set fillStyle(t){this._fillStyle=le(t,Ht.defaultFillStyle);}get strokeStyle(){return this._strokeStyle}set strokeStyle(t){this._strokeStyle=xi(t,Ht.defaultStrokeStyle);}setFillStyle(t){return this._fillStyle=le(t,Ht.defaultFillStyle),this}setStrokeStyle(t){return this._strokeStyle=le(t,Ht.defaultStrokeStyle),this}texture(t,e,i,n,r,o){return this.instructions.push({action:"texture",data:{image:t,dx:i||0,dy:n||0,dw:r||t.frame.width,dh:o||t.frame.height,transform:this._transform.clone(),alpha:this._fillStyle.alpha,style:e?st.shared.setValue(e).toNumber():16777215}}),this.onUpdate(),this}beginPath(){return this._activePath=new ve,this}fill(t,e){let i;const n=this.instructions[this.instructions.length-1];return this._tick===0&&n&&n.action==="stroke"?i=n.data.path:i=this._activePath.clone(),i?(t!=null&&(e!==void 0&&typeof t=="number"&&(D(N,"GraphicsContext.fill(color, alpha) is deprecated, use GraphicsContext.fill({ color, alpha }) instead"),t={color:t,alpha:e}),this._fillStyle=le(t,Ht.defaultFillStyle)),this.instructions.push({action:"fill",data:{style:this.fillStyle,path:i}}),this.onUpdate(),this._initNextPathLocation(),this._tick=0,this):this}_initNextPathLocation(){const{x:t,y:e}=this._activePath.getLastPoint(L.shared);this._activePath.clear(),this._activePath.moveTo(t,e);}stroke(t){let e;const i=this.instructions[this.instructions.length-1];return this._tick===0&&i&&i.action==="fill"?e=i.data.path:e=this._activePath.clone(),e?(t!=null&&(this._strokeStyle=xi(t,Ht.defaultStrokeStyle)),this.instructions.push({action:"stroke",data:{style:this.strokeStyle,path:e}}),this.onUpdate(),this._initNextPathLocation(),this._tick=0,this):this}cut(){for(let t=0;t<2;t++){const e=this.instructions[this.instructions.length-1-t],i=this._activePath.clone();if(e&&(e.action==="stroke"||e.action==="fill"))if(e.data.hole)e.data.hole.addPath(i);else {e.data.hole=i;break}}return this._initNextPathLocation(),this}arc(t,e,i,n,r,o){this._tick++;const a=this._transform;return this._activePath.arc(a.a*t+a.c*e+a.tx,a.b*t+a.d*e+a.ty,i,n,r,o),this}arcTo(t,e,i,n,r){this._tick++;const o=this._transform;return this._activePath.arcTo(o.a*t+o.c*e+o.tx,o.b*t+o.d*e+o.ty,o.a*i+o.c*n+o.tx,o.b*i+o.d*n+o.ty,r),this}arcToSvg(t,e,i,n,r,o,a){this._tick++;const l=this._transform;return this._activePath.arcToSvg(t,e,i,n,r,l.a*o+l.c*a+l.tx,l.b*o+l.d*a+l.ty),this}bezierCurveTo(t,e,i,n,r,o,a){this._tick++;const l=this._transform;return this._activePath.bezierCurveTo(l.a*t+l.c*e+l.tx,l.b*t+l.d*e+l.ty,l.a*i+l.c*n+l.tx,l.b*i+l.d*n+l.ty,l.a*r+l.c*o+l.tx,l.b*r+l.d*o+l.ty,a),this}closePath(){return this._tick++,this._activePath?.closePath(),this}ellipse(t,e,i,n){return this._tick++,this._activePath.ellipse(t,e,i,n,this._transform.clone()),this}circle(t,e,i){return this._tick++,this._activePath.circle(t,e,i,this._transform.clone()),this}path(t){return this._tick++,this._activePath.addPath(t,this._transform.clone()),this}lineTo(t,e){this._tick++;const i=this._transform;return this._activePath.lineTo(i.a*t+i.c*e+i.tx,i.b*t+i.d*e+i.ty),this}moveTo(t,e){this._tick++;const i=this._transform,n=this._activePath.instructions,r=i.a*t+i.c*e+i.tx,o=i.b*t+i.d*e+i.ty;return n.length===1&&n[0].action==="moveTo"?(n[0].data[0]=r,n[0].data[1]=o,this):(this._activePath.moveTo(r,o),this)}quadraticCurveTo(t,e,i,n,r){this._tick++;const o=this._transform;return this._activePath.quadraticCurveTo(o.a*t+o.c*e+o.tx,o.b*t+o.d*e+o.ty,o.a*i+o.c*n+o.tx,o.b*i+o.d*n+o.ty,r),this}rect(t,e,i,n){return this._tick++,this._activePath.rect(t,e,i,n,this._transform.clone()),this}roundRect(t,e,i,n,r){return this._tick++,this._activePath.roundRect(t,e,i,n,r,this._transform.clone()),this}poly(t,e){return this._tick++,this._activePath.poly(t,e,this._transform.clone()),this}regularPoly(t,e,i,n,r=0,o){return this._tick++,this._activePath.regularPoly(t,e,i,n,r,o),this}roundPoly(t,e,i,n,r,o){return this._tick++,this._activePath.roundPoly(t,e,i,n,r,o),this}roundShape(t,e,i,n){return this._tick++,this._activePath.roundShape(t,e,i,n),this}filletRect(t,e,i,n,r){return this._tick++,this._activePath.filletRect(t,e,i,n,r),this}chamferRect(t,e,i,n,r,o){return this._tick++,this._activePath.chamferRect(t,e,i,n,r,o),this}star(t,e,i,n,r=0,o=0){return this._tick++,this._activePath.star(t,e,i,n,r,o,this._transform.clone()),this}svg(t){return this._tick++,rd(t,this),this}restore(){const t=this._stateStack.pop();return t&&(this._transform=t.transform,this._fillStyle=t.fillStyle,this._strokeStyle=t.strokeStyle),this}save(){return this._stateStack.push({transform:this._transform.clone(),fillStyle:{...this._fillStyle},strokeStyle:{...this._strokeStyle}}),this}getTransform(){return this._transform}resetTransform(){return this._transform.identity(),this}rotate(t){return this._transform.rotate(t),this}scale(t,e=t){return this._transform.scale(t,e),this}setTransform(t,e,i,n,r,o){return t instanceof K?(this._transform.set(t.a,t.b,t.c,t.d,t.tx,t.ty),this):(this._transform.set(t,e,i,n,r,o),this)}transform(t,e,i,n,r,o){return t instanceof K?(this._transform.append(t),this):(tr.set(t,e,i,n,r,o),this._transform.append(tr),this)}translate(t,e=t){return this._transform.translate(t,e),this}clear(){return this._activePath.clear(),this.instructions.length=0,this.resetTransform(),this.onUpdate(),this}onUpdate(){this.dirty||(this.emit("update",this,16),this.dirty=!0,this._boundsDirty=!0);}get bounds(){if(!this._boundsDirty)return this._bounds;const t=this._bounds;t.clear();for(let e=0;e<this.instructions.length;e++){const i=this.instructions[e],n=i.action;if(n==="fill"){const r=i.data;t.addBounds(r.path.bounds);}else if(n==="texture"){const r=i.data;t.addFrame(r.dx,r.dy,r.dx+r.dw,r.dy+r.dh,r.transform);}if(n==="stroke"){const r=i.data,o=r.style.alignment,a=r.style.width*(1-o),l=r.path.bounds;t.addFrame(l.minX-a,l.minY-a,l.maxX+a,l.maxY+a);}}return t}containsPoint(t){if(!this.bounds.containsPoint(t.x,t.y))return !1;const e=this.instructions;let i=!1;for(let n=0;n<e.length;n++){const r=e[n],o=r.data,a=o.path;if(!r.action||!a)continue;const l=o.style,c=a.shapePath.shapePrimitives;for(let h=0;h<c.length;h++){const d=c[h].shape;if(!l||!d)continue;const _=c[h].transform,u=_?_.applyInverse(t,hd):t;if(r.action==="fill")i=d.contains(u.x,u.y);else {const p=l;i=d.strokeContains(u.x,u.y,p.width,p.alignment);}const f=o.hole;if(f){const p=f.shapePath?.shapePrimitives;if(p)for(let A=0;A<p.length;A++)p[A].shape.contains(u.x,u.y)&&(i=!1);}if(i)return !0}}return i}destroy(t=!1){if(this._stateStack.length=0,this._transform=null,this.emit("destroy",this),this.removeAllListeners(),typeof t=="boolean"?t:t?.texture){const i=typeof t=="boolean"?t:t?.textureSource;this._fillStyle.texture&&this._fillStyle.texture.destroy(i),this._strokeStyle.texture&&this._strokeStyle.texture.destroy(i);}this._fillStyle=null,this._strokeStyle=null,this.instructions=null,this._activePath=null,this._bounds=null,this._stateStack=null,this.customShader=null,this._transform=null;}};Js.defaultFillStyle={color:16777215,alpha:1,texture:W.WHITE,matrix:null,fill:null};Js.defaultStrokeStyle={width:1,color:16777215,alpha:1,alignment:.5,miterLimit:10,cap:"butt",join:"miter",texture:W.WHITE,matrix:null,fill:null,pixelLine:!1};let Pt=Js;const er=["align","breakWords","cssOverrides","fontVariant","fontWeight","leading","letterSpacing","lineHeight","padding","textBaseline","trim","whiteSpace","wordWrap","wordWrapWidth","fontFamily","fontStyle","fontSize"];function dd(s){const t=[];let e=0;for(let i=0;i<er.length;i++){const n=`_${er[i]}`;t[e++]=s[n];}return e=Oo(s._fill,t,e),e=ud(s._stroke,t,e),e=_d(s.dropShadow,t,e),t.join("-")}function Oo(s,t,e){return s&&(t[e++]=s.color,t[e++]=s.alpha,t[e++]=s.fill?.styleKey),e}function ud(s,t,e){return s&&(e=Oo(s,t,e),t[e++]=s.width,t[e++]=s.alignment,t[e++]=s.cap,t[e++]=s.join,t[e++]=s.miterLimit),e}function _d(s,t,e){return s&&(t[e++]=s.alpha,t[e++]=s.angle,t[e++]=s.blur,t[e++]=s.distance,t[e++]=st.shared.setValue(s.color).toNumber()),e}const $s=class Ae extends Lt{constructor(t={}){super(),fd(t);const e={...Ae.defaultTextStyle,...t};for(const i in e){const n=i;this[n]=e[i];}this.update();}get align(){return this._align}set align(t){this._align=t,this.update();}get breakWords(){return this._breakWords}set breakWords(t){this._breakWords=t,this.update();}get dropShadow(){return this._dropShadow}set dropShadow(t){t!==null&&typeof t=="object"?this._dropShadow=this._createProxy({...Ae.defaultDropShadow,...t}):this._dropShadow=t?this._createProxy({...Ae.defaultDropShadow}):null,this.update();}get fontFamily(){return this._fontFamily}set fontFamily(t){this._fontFamily=t,this.update();}get fontSize(){return this._fontSize}set fontSize(t){typeof t=="string"?this._fontSize=parseInt(t,10):this._fontSize=t,this.update();}get fontStyle(){return this._fontStyle}set fontStyle(t){this._fontStyle=t.toLowerCase(),this.update();}get fontVariant(){return this._fontVariant}set fontVariant(t){this._fontVariant=t,this.update();}get fontWeight(){return this._fontWeight}set fontWeight(t){this._fontWeight=t,this.update();}get leading(){return this._leading}set leading(t){this._leading=t,this.update();}get letterSpacing(){return this._letterSpacing}set letterSpacing(t){this._letterSpacing=t,this.update();}get lineHeight(){return this._lineHeight}set lineHeight(t){this._lineHeight=t,this.update();}get padding(){return this._padding}set padding(t){this._padding=t,this.update();}get trim(){return this._trim}set trim(t){this._trim=t,this.update();}get textBaseline(){return this._textBaseline}set textBaseline(t){this._textBaseline=t,this.update();}get whiteSpace(){return this._whiteSpace}set whiteSpace(t){this._whiteSpace=t,this.update();}get wordWrap(){return this._wordWrap}set wordWrap(t){this._wordWrap=t,this.update();}get wordWrapWidth(){return this._wordWrapWidth}set wordWrapWidth(t){this._wordWrapWidth=t,this.update();}get fill(){return this._originalFill}set fill(t){t!==this._originalFill&&(this._originalFill=t,this._isFillStyle(t)&&(this._originalFill=this._createProxy({...Pt.defaultFillStyle,...t},()=>{this._fill=le({...this._originalFill},Pt.defaultFillStyle);})),this._fill=le(t===0?"black":t,Pt.defaultFillStyle),this.update());}get stroke(){return this._originalStroke}set stroke(t){t!==this._originalStroke&&(this._originalStroke=t,this._isFillStyle(t)&&(this._originalStroke=this._createProxy({...Pt.defaultStrokeStyle,...t},()=>{this._stroke=xi({...this._originalStroke},Pt.defaultStrokeStyle);})),this._stroke=xi(t,Pt.defaultStrokeStyle),this.update());}_generateKey(){return this._styleKey=dd(this),this._styleKey}update(){this._styleKey=null,this.emit("update",this);}reset(){const t=Ae.defaultTextStyle;for(const e in t)this[e]=t[e];}get styleKey(){return this._styleKey||this._generateKey()}clone(){return new Ae({align:this.align,breakWords:this.breakWords,dropShadow:this._dropShadow?{...this._dropShadow}:null,fill:this._fill,fontFamily:this.fontFamily,fontSize:this.fontSize,fontStyle:this.fontStyle,fontVariant:this.fontVariant,fontWeight:this.fontWeight,leading:this.leading,letterSpacing:this.letterSpacing,lineHeight:this.lineHeight,padding:this.padding,stroke:this._stroke,textBaseline:this.textBaseline,whiteSpace:this.whiteSpace,wordWrap:this.wordWrap,wordWrapWidth:this.wordWrapWidth})}destroy(t=!1){if(this.removeAllListeners(),typeof t=="boolean"?t:t?.texture){const i=typeof t=="boolean"?t:t?.textureSource;this._fill?.texture&&this._fill.texture.destroy(i),this._originalFill?.texture&&this._originalFill.texture.destroy(i),this._stroke?.texture&&this._stroke.texture.destroy(i),this._originalStroke?.texture&&this._originalStroke.texture.destroy(i);}this._fill=null,this._stroke=null,this.dropShadow=null,this._originalStroke=null,this._originalFill=null;}_createProxy(t,e){return new Proxy(t,{set:(i,n,r)=>(i[n]=r,e?.(n,r),this.update(),!0)})}_isFillStyle(t){return (t??null)!==null&&!(st.isColorLike(t)||t instanceof Ye||t instanceof ki)}};$s.defaultDropShadow={alpha:1,angle:Math.PI/6,blur:0,color:"black",distance:5};$s.defaultTextStyle={align:"left",breakWords:!1,dropShadow:null,fill:"black",fontFamily:"Arial",fontSize:26,fontStyle:"normal",fontVariant:"normal",fontWeight:"normal",leading:0,letterSpacing:0,lineHeight:0,padding:0,stroke:null,textBaseline:"alphabetic",trim:!1,whiteSpace:"pre",wordWrap:!1,wordWrapWidth:100};let qe=$s;function fd(s){const t=s;if(typeof t.dropShadow=="boolean"&&t.dropShadow){const e=qe.defaultDropShadow;s.dropShadow={alpha:t.dropShadowAlpha??e.alpha,angle:t.dropShadowAngle??e.angle,blur:t.dropShadowBlur??e.blur,color:t.dropShadowColor??e.color,distance:t.dropShadowDistance??e.distance};}if(t.strokeThickness!==void 0){D(N,"strokeThickness is now a part of stroke");const e=t.stroke;let i={};if(st.isColorLike(e))i.color=e;else if(e instanceof Ye||e instanceof ki)i.fill=e;else if(Object.hasOwnProperty.call(e,"color")||Object.hasOwnProperty.call(e,"fill"))i=e;else throw new Error("Invalid stroke value.");s.stroke={...i,width:t.strokeThickness};}if(Array.isArray(t.fillGradientStops)){D(N,"gradient fill is now a fill pattern: `new FillGradient(...)`");let e;s.fontSize==null?s.fontSize=qe.defaultTextStyle.fontSize:typeof s.fontSize=="string"?e=parseInt(s.fontSize,10):e=s.fontSize;const i=new Ye(0,0,0,e*1.7),n=t.fillGradientStops.map(r=>st.shared.setValue(r).toNumber());n.forEach((r,o)=>{const a=o/(n.length-1);i.addColorStop(a,r);}),s.fill={fill:i};}}class Ad{constructor(t){this._canvasPool=Object.create(null),this.canvasOptions=t||{},this.enableFullScreen=!1;}_createCanvasAndContext(t,e){const i=it.get().createCanvas();i.width=t,i.height=e;const n=i.getContext("2d");return {canvas:i,context:n}}getOptimalCanvasAndContext(t,e,i=1){t=Math.ceil(t*i-1e-6),e=Math.ceil(e*i-1e-6),t=gi(t),e=gi(e);const n=(t<<17)+(e<<1);this._canvasPool[n]||(this._canvasPool[n]=[]);let r=this._canvasPool[n].pop();return r||(r=this._createCanvasAndContext(t,e)),r}returnCanvasAndContext(t){const e=t.canvas,{width:i,height:n}=e,r=(i<<17)+(n<<1);t.context.clearRect(0,0,i,n),this._canvasPool[r].push(t);}clear(){this._canvasPool={};}}const ir=new Ad,pd=["serif","sans-serif","monospace","cursive","fantasy","system-ui"];function Rs(s){const t=typeof s.fontSize=="number"?`${s.fontSize}px`:s.fontSize;let e=s.fontFamily;Array.isArray(s.fontFamily)||(e=s.fontFamily.split(","));for(let i=e.length-1;i>=0;i--){let n=e[i].trim();!/([\"\'])[^\'\"]+\1/.test(n)&&!pd.includes(n)&&(n=`"${n}"`),e[i]=n;}return `${s.fontStyle} ${s.fontVariant} ${s.fontWeight} ${t} ${e.join(",")}`}const as={willReadFrequently:!0},Ut=class P{static get experimentalLetterSpacingSupported(){let t=P._experimentalLetterSpacingSupported;if(t!==void 0){const e=it.get().getCanvasRenderingContext2D().prototype;t=P._experimentalLetterSpacingSupported="letterSpacing"in e||"textLetterSpacing"in e;}return t}constructor(t,e,i,n,r,o,a,l,c){this.text=t,this.style=e,this.width=i,this.height=n,this.lines=r,this.lineWidths=o,this.lineHeight=a,this.maxLineWidth=l,this.fontProperties=c;}static measureText(t=" ",e,i=P._canvas,n=e.wordWrap){const r=`${t}:${e.styleKey}`;if(P._measurementCache[r])return P._measurementCache[r];const o=Rs(e),a=P.measureFont(o);a.fontSize===0&&(a.fontSize=e.fontSize,a.ascent=e.fontSize);const l=P.__context;l.font=o;const h=(n?P._wordWrap(t,e,i):t).split(/(?:\r\n|\r|\n)/),d=new Array(h.length);let _=0;for(let g=0;g<h.length;g++){const y=P._measureText(h[g],e.letterSpacing,l);d[g]=y,_=Math.max(_,y);}const u=e._stroke?.width||0;let f=_+u;e.dropShadow&&(f+=e.dropShadow.distance);const p=e.lineHeight||a.fontSize;let A=Math.max(p,a.fontSize+u)+(h.length-1)*(p+e.leading);return e.dropShadow&&(A+=e.dropShadow.distance),new P(t,e,f,A,h,d,p+e.leading,_,a)}static _measureText(t,e,i){let n=!1;P.experimentalLetterSpacingSupported&&(P.experimentalLetterSpacing?(i.letterSpacing=`${e}px`,i.textLetterSpacing=`${e}px`,n=!0):(i.letterSpacing="0px",i.textLetterSpacing="0px"));const r=i.measureText(t);let o=r.width;const a=-r.actualBoundingBoxLeft;let c=r.actualBoundingBoxRight-a;if(o>0)if(n)o-=e,c-=e;else {const h=(P.graphemeSegmenter(t).length-1)*e;o+=h,c+=h;}return Math.max(o,c)}static _wordWrap(t,e,i=P._canvas){const n=i.getContext("2d",as);let r=0,o="",a="";const l=Object.create(null),{letterSpacing:c,whiteSpace:h}=e,d=P._collapseSpaces(h),_=P._collapseNewlines(h);let u=!d;const f=e.wordWrapWidth+c,p=P._tokenize(t);for(let A=0;A<p.length;A++){let m=p[A];if(P._isNewline(m)){if(!_){a+=P._addLine(o),u=!d,o="",r=0;continue}m=" ";}if(d){const y=P.isBreakingSpace(m),x=P.isBreakingSpace(o[o.length-1]);if(y&&x)continue}const g=P._getFromCache(m,c,l,n);if(g>f)if(o!==""&&(a+=P._addLine(o),o="",r=0),P.canBreakWords(m,e.breakWords)){const y=P.wordWrapSplit(m);for(let x=0;x<y.length;x++){let v=y[x],b=v,C=1;for(;y[x+C];){const B=y[x+C];if(!P.canBreakChars(b,B,m,x,e.breakWords))v+=B;else break;b=B,C++;}x+=C-1;const M=P._getFromCache(v,c,l,n);M+r>f&&(a+=P._addLine(o),u=!1,o="",r=0),o+=v,r+=M;}}else {o.length>0&&(a+=P._addLine(o),o="",r=0);const y=A===p.length-1;a+=P._addLine(m,!y),u=!1,o="",r=0;}else g+r>f&&(u=!1,a+=P._addLine(o),o="",r=0),(o.length>0||!P.isBreakingSpace(m)||u)&&(o+=m,r+=g);}return a+=P._addLine(o,!1),a}static _addLine(t,e=!0){return t=P._trimRight(t),t=e?`${t}
`:t,t}static _getFromCache(t,e,i,n){let r=i[t];return typeof r!="number"&&(r=P._measureText(t,e,n)+e,i[t]=r),r}static _collapseSpaces(t){return t==="normal"||t==="pre-line"}static _collapseNewlines(t){return t==="normal"}static _trimRight(t){if(typeof t!="string")return "";for(let e=t.length-1;e>=0;e--){const i=t[e];if(!P.isBreakingSpace(i))break;t=t.slice(0,-1);}return t}static _isNewline(t){return typeof t!="string"?!1:P._newlines.includes(t.charCodeAt(0))}static isBreakingSpace(t,e){return typeof t!="string"?!1:P._breakingSpaces.includes(t.charCodeAt(0))}static _tokenize(t){const e=[];let i="";if(typeof t!="string")return e;for(let n=0;n<t.length;n++){const r=t[n],o=t[n+1];if(P.isBreakingSpace(r,o)||P._isNewline(r)){i!==""&&(e.push(i),i=""),e.push(r);continue}i+=r;}return i!==""&&e.push(i),e}static canBreakWords(t,e){return e}static canBreakChars(t,e,i,n,r){return !0}static wordWrapSplit(t){return P.graphemeSegmenter(t)}static measureFont(t){if(P._fonts[t])return P._fonts[t];const e=P._context;e.font=t;const i=e.measureText(P.METRICS_STRING+P.BASELINE_SYMBOL),n={ascent:i.actualBoundingBoxAscent,descent:i.actualBoundingBoxDescent,fontSize:i.actualBoundingBoxAscent+i.actualBoundingBoxDescent};return P._fonts[t]=n,n}static clearMetrics(t=""){t?delete P._fonts[t]:P._fonts={};}static get _canvas(){if(!P.__canvas){let t;try{const e=new OffscreenCanvas(0,0);if(e.getContext("2d",as)?.measureText)return P.__canvas=e,e;t=it.get().createCanvas();}catch{t=it.get().createCanvas();}t.width=t.height=10,P.__canvas=t;}return P.__canvas}static get _context(){return P.__context||(P.__context=P._canvas.getContext("2d",as)),P.__context}};Ut.METRICS_STRING="|ÉqÅ";Ut.BASELINE_SYMBOL="M";Ut.BASELINE_MULTIPLIER=1.4;Ut.HEIGHT_MULTIPLIER=2;Ut.graphemeSegmenter=(()=>{if(typeof Intl?.Segmenter=="function"){const s=new Intl.Segmenter;return t=>[...s.segment(t)].map(e=>e.segment)}return s=>[...s]})();Ut.experimentalLetterSpacing=!1;Ut._fonts={};Ut._newlines=[10,13];Ut._breakingSpaces=[9,32,8192,8193,8194,8195,8196,8197,8198,8200,8201,8202,8287,12288];Ut._measurementCache={};let sr=Ut;function nr(s,t){if(s.texture===W.WHITE&&!s.fill)return st.shared.setValue(s.color).setAlpha(s.alpha??1).toHexa();if(s.fill){if(s.fill instanceof ki){const e=s.fill,i=t.createPattern(e.texture.source.resource,"repeat"),n=e.transform.copyTo(K.shared);return n.scale(e.texture.frame.width,e.texture.frame.height),i.setTransform(n),i}else if(s.fill instanceof Ye){const e=s.fill;if(e.type==="linear"){const i=t.createLinearGradient(e.x0,e.y0,e.x1,e.y1);return e.gradientStops.forEach(n=>{i.addColorStop(n.offset,st.shared.setValue(n.color).toHex());}),i}}}else {const e=t.createPattern(s.texture.source.resource,"repeat"),i=s.matrix.copyTo(K.shared);return i.scale(s.texture.frame.width,s.texture.frame.height),e.setTransform(i),e}return ot("FillStyle not recognised",s),"red"}function Fo(s){if(s==="")return [];typeof s=="string"&&(s=[s]);const t=[];for(let e=0,i=s.length;e<i;e++){const n=s[e];if(Array.isArray(n)){if(n.length!==2)throw new Error(`[BitmapFont]: Invalid character range length, expecting 2 got ${n.length}.`);if(n[0].length===0||n[1].length===0)throw new Error("[BitmapFont]: Invalid character delimiter.");const r=n[0].charCodeAt(0),o=n[1].charCodeAt(0);if(o<r)throw new Error("[BitmapFont]: Invalid character range.");for(let a=r,l=o;a<=l;a++)t.push(String.fromCharCode(a));}else t.push(...Array.from(n));}if(t.length===0)throw new Error("[BitmapFont]: Empty set when resolving characters.");return t}const Lo=class Wo extends mo{constructor(t){super(),this.resolution=1,this.pages=[],this._padding=0,this._measureCache=Object.create(null),this._currentChars=[],this._currentX=0,this._currentY=0,this._currentPageIndex=-1,this._skipKerning=!1;const e={...Wo.defaultOptions,...t};this._textureSize=e.textureSize,this._mipmap=e.mipmap;const i=e.style.clone();e.overrideFill&&(i._fill.color=16777215,i._fill.alpha=1,i._fill.texture=W.WHITE,i._fill.fill=null),this.applyFillAsTint=e.overrideFill;const n=i.fontSize;i.fontSize=this.baseMeasurementFontSize;const r=Rs(i);e.overrideSize?i._stroke&&(i._stroke.width*=this.baseRenderedFontSize/n):i.fontSize=this.baseRenderedFontSize=n,this._style=i,this._skipKerning=e.skipKerning??!1,this.resolution=e.resolution??1,this._padding=e.padding??4,this.fontMetrics=sr.measureFont(r),this.lineHeight=i.lineHeight||this.fontMetrics.fontSize||i.fontSize;}ensureCharacters(t){const e=Fo(t).filter(A=>!this._currentChars.includes(A)).filter((A,m,g)=>g.indexOf(A)===m);if(!e.length)return;this._currentChars=[...this._currentChars,...e];let i;this._currentPageIndex===-1?i=this._nextPage():i=this.pages[this._currentPageIndex];let{canvas:n,context:r}=i.canvasAndContext,o=i.texture.source;const a=this._style;let l=this._currentX,c=this._currentY;const h=this.baseRenderedFontSize/this.baseMeasurementFontSize,d=this._padding*h;let _=0,u=!1;const f=n.width/this.resolution,p=n.height/this.resolution;for(let A=0;A<e.length;A++){const m=e[A],g=sr.measureText(m,a,n,!1);g.lineHeight=g.height;const y=g.width*h,x=Math.ceil((a.fontStyle==="italic"?2:1)*y),v=g.height*h,b=x+d*2,C=v+d*2;if(u=!1,m!==`
`&&m!=="\r"&&m!=="	"&&m!==" "&&(u=!0,_=Math.ceil(Math.max(C,_))),l+b>f&&(c+=_,_=C,l=0,c+_>p)){o.update();const B=this._nextPage();n=B.canvasAndContext.canvas,r=B.canvasAndContext.context,o=B.texture.source,c=0;}const M=y/h-(a.dropShadow?.distance??0)-(a._stroke?.width??0);if(this.chars[m]={id:m.codePointAt(0),xOffset:-this._padding,yOffset:-this._padding,xAdvance:M,kerning:{}},u){this._drawGlyph(r,g,l+d,c+d,h,a);const B=o.width*h,w=o.height*h,k=new Q(l/B*o.width,c/w*o.height,b/B*o.width,C/w*o.height);this.chars[m].texture=new W({source:o,frame:k}),l+=Math.ceil(b);}}o.update(),this._currentX=l,this._currentY=c,this._skipKerning&&this._applyKerning(e,r);}get pageTextures(){return D(N,"BitmapFont.pageTextures is deprecated, please use BitmapFont.pages instead."),this.pages}_applyKerning(t,e){const i=this._measureCache;for(let n=0;n<t.length;n++){const r=t[n];for(let o=0;o<this._currentChars.length;o++){const a=this._currentChars[o];let l=i[r];l||(l=i[r]=e.measureText(r).width);let c=i[a];c||(c=i[a]=e.measureText(a).width);let h=e.measureText(r+a).width,d=h-(l+c);d&&(this.chars[r].kerning[a]=d),h=e.measureText(r+a).width,d=h-(l+c),d&&(this.chars[a].kerning[r]=d);}}}_nextPage(){this._currentPageIndex++;const t=this.resolution,e=ir.getOptimalCanvasAndContext(this._textureSize,this._textureSize,t);this._setupContext(e.context,this._style,t);const i=t*(this.baseRenderedFontSize/this.baseMeasurementFontSize),n=new W({source:new we({resource:e.canvas,resolution:i,alphaMode:"premultiply-alpha-on-upload",autoGenerateMipmaps:this._mipmap})}),r={canvasAndContext:e,texture:n};return this.pages[this._currentPageIndex]=r,r}_setupContext(t,e,i){e.fontSize=this.baseRenderedFontSize,t.scale(i,i),t.font=Rs(e),e.fontSize=this.baseMeasurementFontSize,t.textBaseline=e.textBaseline;const n=e._stroke,r=n?.width??0;if(n&&(t.lineWidth=r,t.lineJoin=n.join,t.miterLimit=n.miterLimit,t.strokeStyle=nr(n,t)),e._fill&&(t.fillStyle=nr(e._fill,t)),e.dropShadow){const o=e.dropShadow,a=st.shared.setValue(o.color).toArray(),l=o.blur*i,c=o.distance*i;t.shadowColor=`rgba(${a[0]*255},${a[1]*255},${a[2]*255},${o.alpha})`,t.shadowBlur=l,t.shadowOffsetX=Math.cos(o.angle)*c,t.shadowOffsetY=Math.sin(o.angle)*c;}else t.shadowColor="black",t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0;}_drawGlyph(t,e,i,n,r,o){const a=e.text,l=e.fontProperties,h=(o._stroke?.width??0)*r,d=i+h/2,_=n-h/2,u=l.descent*r,f=e.lineHeight*r;o.stroke&&h&&t.strokeText(a,d,_+f-u),o._fill&&t.fillText(a,d,_+f-u);}destroy(){super.destroy();for(let t=0;t<this.pages.length;t++){const{canvasAndContext:e,texture:i}=this.pages[t];ir.returnCanvasAndContext(e),i.destroy(!0);}this.pages=null;}};Lo.defaultOptions={textureSize:512,style:new qe,mipmap:!0};let rr=Lo;function md(s,t,e,i){const n={width:0,height:0,offsetY:0,scale:t.fontSize/e.baseMeasurementFontSize,lines:[{width:0,charPositions:[],spaceWidth:0,spacesIndex:[],chars:[]}]};n.offsetY=e.baseLineOffset;let r=n.lines[0],o=null,a=!0;const l={spaceWord:!1,width:0,start:0,index:0,positions:[],chars:[]},c=f=>{const p=r.width;for(let A=0;A<l.index;A++){const m=f.positions[A];r.chars.push(f.chars[A]),r.charPositions.push(m+p);}r.width+=f.width,a=!1,l.width=0,l.index=0,l.chars.length=0;},h=()=>{let f=r.chars.length-1;if(i){let p=r.chars[f];for(;p===" ";)r.width-=e.chars[p].xAdvance,p=r.chars[--f];}n.width=Math.max(n.width,r.width),r={width:0,charPositions:[],chars:[],spaceWidth:0,spacesIndex:[]},a=!0,n.lines.push(r),n.height+=e.lineHeight;},d=e.baseMeasurementFontSize/t.fontSize,_=t.letterSpacing*d,u=t.wordWrapWidth*d;for(let f=0;f<s.length+1;f++){let p;const A=f===s.length;A||(p=s[f]);const m=e.chars[p]||e.chars[" "];if(/(?:\s)/.test(p)||p==="\r"||p===`
`||A){if(!a&&t.wordWrap&&r.width+l.width-_>u?(h(),c(l),A||r.charPositions.push(0)):(l.start=r.width,c(l),A||r.charPositions.push(0)),p==="\r"||p===`
`)r.width!==0&&h();else if(!A){const v=m.xAdvance+(m.kerning[o]||0)+_;r.width+=v,r.spaceWidth=v,r.spacesIndex.push(r.charPositions.length),r.chars.push(p);}}else {const x=m.kerning[o]||0,v=m.xAdvance+x+_;l.positions[l.index++]=l.width+x,l.chars.push(p),l.width+=v;}o=p;}return h(),t.align==="center"?gd(n):t.align==="right"?yd(n):t.align==="justify"&&xd(n),n}function gd(s){for(let t=0;t<s.lines.length;t++){const e=s.lines[t],i=s.width/2-e.width/2;for(let n=0;n<e.charPositions.length;n++)e.charPositions[n]+=i;}}function yd(s){for(let t=0;t<s.lines.length;t++){const e=s.lines[t],i=s.width-e.width;for(let n=0;n<e.charPositions.length;n++)e.charPositions[n]+=i;}}function xd(s){const t=s.width;for(let e=0;e<s.lines.length;e++){const i=s.lines[e];let n=0,r=i.spacesIndex[n++],o=0;const a=i.spacesIndex.length,c=(t-i.width)/a;for(let h=0;h<i.charPositions.length;h++)h===r&&(r=i.spacesIndex[n++],o+=c),i.charPositions[h]+=o;}}let ci=0;class bd{constructor(){this.ALPHA=[["a","z"],["A","Z"]," "],this.NUMERIC=[["0","9"]],this.ALPHANUMERIC=[["a","z"],["A","Z"],["0","9"]," "],this.ASCII=[[" ","~"]],this.defaultOptions={chars:this.ALPHANUMERIC,resolution:1,padding:4,skipKerning:!1};}getFont(t,e){let i=`${e.fontFamily}-bitmap`,n=!0;if(e._fill.fill&&!e._stroke)i+=e._fill.fill.styleKey,n=!1;else if(e._stroke||e.dropShadow){let o=e.styleKey;o=o.substring(0,o.lastIndexOf("-")),i=`${o}-bitmap`,n=!1;}if(!et.has(i)){const o=new rr({style:e,overrideFill:n,overrideSize:!0,...this.defaultOptions});ci++,ci>50&&ot("BitmapText",`You have dynamically created ${ci} bitmap fonts, this can be inefficient. Try pre installing your font styles using \`BitmapFont.install({name:"style1", style})\``),o.once("destroy",()=>{ci--,et.remove(i);}),et.set(i,o);}const r=et.get(i);return r.ensureCharacters?.(t),r}getLayout(t,e,i=!0){const n=this.getFont(t,e);return md([...t],e,n,i)}measureText(t,e,i=!0){return this.getLayout(t,e,i)}install(...t){let e=t[0];typeof e=="string"&&(e={name:e,style:t[1],chars:t[2]?.chars,resolution:t[2]?.resolution,padding:t[2]?.padding,skipKerning:t[2]?.skipKerning},D(N,"BitmapFontManager.install(name, style, options) is deprecated, use BitmapFontManager.install({name, style, ...options})"));const i=e?.name;if(!i)throw new Error("[BitmapFontManager] Property `name` is required.");e={...this.defaultOptions,...e};const n=e.style,r=n instanceof qe?n:new qe(n),o=r._fill.fill!==null&&r._fill.fill!==void 0,a=new rr({style:r,overrideFill:o,skipKerning:e.skipKerning,padding:e.padding,resolution:e.resolution,overrideSize:!1}),l=Fo(e.chars);return a.ensureCharacters(l.join("")),et.set(`${i}-bitmap`,a),a.once("destroy",()=>et.remove(`${i}-bitmap`)),a}uninstall(t){const e=`${t}-bitmap`,i=et.get(e);i&&i.destroy();}}const or=new bd;class Uo extends mo{constructor(t,e){super();const{textures:i,data:n}=t;Object.keys(n.pages).forEach(r=>{const o=n.pages[parseInt(r,10)],a=i[o.id];this.pages.push({texture:a});}),Object.keys(n.chars).forEach(r=>{const o=n.chars[r],{frame:a,source:l}=i[o.page],c=new Q(o.x+a.x,o.y+a.y,o.width,o.height),h=new W({source:l,frame:c});this.chars[r]={id:r.codePointAt(0),xOffset:o.xOffset,yOffset:o.yOffset,xAdvance:o.xAdvance,kerning:o.kerning??{},texture:h};}),this.baseRenderedFontSize=n.fontSize,this.baseMeasurementFontSize=n.fontSize,this.fontMetrics={ascent:0,descent:0,fontSize:n.fontSize},this.baseLineOffset=n.baseLineOffset,this.lineHeight=n.lineHeight,this.fontFamily=n.fontFamily,this.distanceField=n.distanceField??{type:"none",range:0},this.url=e;}destroy(){super.destroy();for(let t=0;t<this.pages.length;t++){const{texture:e}=this.pages[t];e.destroy(!0);}this.pages=null;}static install(t){or.install(t);}static uninstall(t){or.uninstall(t);}}const ls={test(s){return typeof s=="string"&&s.startsWith("info face=")},parse(s){const t=s.match(/^[a-z]+\s+.+$/gm),e={info:[],common:[],page:[],char:[],chars:[],kerning:[],kernings:[],distanceField:[]};for(const d in t){const _=t[d].match(/^[a-z]+/gm)[0],u=t[d].match(/[a-zA-Z]+=([^\s"']+|"([^"]*)")/gm),f={};for(const p in u){const A=u[p].split("="),m=A[0],g=A[1].replace(/"/gm,""),y=parseFloat(g),x=isNaN(y)?g:y;f[m]=x;}e[_].push(f);}const i={chars:{},pages:[],lineHeight:0,fontSize:0,fontFamily:"",distanceField:null,baseLineOffset:0},[n]=e.info,[r]=e.common,[o]=e.distanceField??[];o&&(i.distanceField={range:parseInt(o.distanceRange,10),type:o.fieldType}),i.fontSize=parseInt(n.size,10),i.fontFamily=n.face,i.lineHeight=parseInt(r.lineHeight,10);const a=e.page;for(let d=0;d<a.length;d++)i.pages.push({id:parseInt(a[d].id,10)||0,file:a[d].file});const l={};i.baseLineOffset=i.lineHeight-parseInt(r.base,10);const c=e.char;for(let d=0;d<c.length;d++){const _=c[d],u=parseInt(_.id,10);let f=_.letter??_.char??String.fromCharCode(u);f==="space"&&(f=" "),l[u]=f,i.chars[f]={id:u,page:parseInt(_.page,10)||0,x:parseInt(_.x,10),y:parseInt(_.y,10),width:parseInt(_.width,10),height:parseInt(_.height,10),xOffset:parseInt(_.xoffset,10),yOffset:parseInt(_.yoffset,10),xAdvance:parseInt(_.xadvance,10),kerning:{}};}const h=e.kerning||[];for(let d=0;d<h.length;d++){const _=parseInt(h[d].first,10),u=parseInt(h[d].second,10),f=parseInt(h[d].amount,10);i.chars[l[u]].kerning[l[_]]=f;}return i}},ar={test(s){const t=s;return typeof t!="string"&&"getElementsByTagName"in t&&t.getElementsByTagName("page").length&&t.getElementsByTagName("info")[0].getAttribute("face")!==null},parse(s){const t={chars:{},pages:[],lineHeight:0,fontSize:0,fontFamily:"",distanceField:null,baseLineOffset:0},e=s.getElementsByTagName("info")[0],i=s.getElementsByTagName("common")[0],n=s.getElementsByTagName("distanceField")[0];n&&(t.distanceField={type:n.getAttribute("fieldType"),range:parseInt(n.getAttribute("distanceRange"),10)});const r=s.getElementsByTagName("page"),o=s.getElementsByTagName("char"),a=s.getElementsByTagName("kerning");t.fontSize=parseInt(e.getAttribute("size"),10),t.fontFamily=e.getAttribute("face"),t.lineHeight=parseInt(i.getAttribute("lineHeight"),10);for(let c=0;c<r.length;c++)t.pages.push({id:parseInt(r[c].getAttribute("id"),10)||0,file:r[c].getAttribute("file")});const l={};t.baseLineOffset=t.lineHeight-parseInt(i.getAttribute("base"),10);for(let c=0;c<o.length;c++){const h=o[c],d=parseInt(h.getAttribute("id"),10);let _=h.getAttribute("letter")??h.getAttribute("char")??String.fromCharCode(d);_==="space"&&(_=" "),l[d]=_,t.chars[_]={id:d,page:parseInt(h.getAttribute("page"),10)||0,x:parseInt(h.getAttribute("x"),10),y:parseInt(h.getAttribute("y"),10),width:parseInt(h.getAttribute("width"),10),height:parseInt(h.getAttribute("height"),10),xOffset:parseInt(h.getAttribute("xoffset"),10),yOffset:parseInt(h.getAttribute("yoffset"),10),xAdvance:parseInt(h.getAttribute("xadvance"),10),kerning:{}};}for(let c=0;c<a.length;c++){const h=parseInt(a[c].getAttribute("first"),10),d=parseInt(a[c].getAttribute("second"),10),_=parseInt(a[c].getAttribute("amount"),10);t.chars[l[d]].kerning[l[h]]=_;}return t}},lr={test(s){return typeof s=="string"&&s.includes("<font>")?ar.test(it.get().parseXML(s)):!1},parse(s){return ar.parse(it.get().parseXML(s))}},Cd=[".xml",".fnt"],vd={extension:{type:T.CacheParser,name:"cacheBitmapFont"},test:s=>s instanceof Uo,getCacheableAssets(s,t){const e={};return s.forEach(i=>{e[i]=t,e[`${i}-bitmap`]=t;}),e[`${t.fontFamily}-bitmap`]=t,e}},wd={extension:{type:T.LoadParser,priority:Zt.Normal},name:"loadBitmapFont",test(s){return Cd.includes(Mt.extname(s).toLowerCase())},async testParse(s){return ls.test(s)||lr.test(s)},async parse(s,t,e){const i=ls.test(s)?ls.parse(s):lr.parse(s),{src:n}=t,{pages:r}=i,o=[],a=i.distanceField?{scaleMode:"linear",alphaMode:"premultiply-alpha-on-upload",autoGenerateMipmaps:!1,resolution:1}:{};for(let d=0;d<r.length;++d){const _=r[d].file;let u=Mt.join(Mt.dirname(n),_);u=gs(u,n),o.push({src:u,data:a});}const l=await e.load(o),c=o.map(d=>l[d.src]);return new Uo({data:i,textures:c},n)},async load(s,t){return await(await it.get().fetch(s)).text()},async unload(s,t,e){await Promise.all(s.pages.map(i=>e.unload(i.texture.source._sourceOrigin))),s.destroy();}};class kd{constructor(t,e=!1){this._loader=t,this._assetList=[],this._isLoading=!1,this._maxConcurrent=1,this.verbose=e;}add(t){t.forEach(e=>{this._assetList.push(e);}),this.verbose&&console.log("[BackgroundLoader] assets: ",this._assetList),this._isActive&&!this._isLoading&&this._next();}async _next(){if(this._assetList.length&&this._isActive){this._isLoading=!0;const t=[],e=Math.min(this._assetList.length,this._maxConcurrent);for(let i=0;i<e;i++)t.push(this._assetList.pop());await this._loader.load(t),this._isLoading=!1,this._next();}}get active(){return this._isActive}set active(t){this._isActive!==t&&(this._isActive=t,t&&!this._isLoading&&this._next());}}const Id={extension:{type:T.CacheParser,name:"cacheTextureArray"},test:s=>Array.isArray(s)&&s.every(t=>t instanceof W),getCacheableAssets:(s,t)=>{const e={};return s.forEach(i=>{t.forEach((n,r)=>{e[i+(r===0?"":r+1)]=n;});}),e}};async function Do(s){if("Image"in globalThis)return new Promise(t=>{const e=new Image;e.onload=()=>{t(!0);},e.onerror=()=>{t(!1);},e.src=s;});if("createImageBitmap"in globalThis&&"fetch"in globalThis){try{const t=await(await fetch(s)).blob();await createImageBitmap(t);}catch{return !1}return !0}return !1}const Ed={extension:{type:T.DetectionParser,priority:1},test:async()=>Do("data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgANogQEAwgMg8f8D///8WfhwB8+ErK42A="),add:async s=>[...s,"avif"],remove:async s=>s.filter(t=>t!=="avif")},cr=["png","jpg","jpeg"],Md={extension:{type:T.DetectionParser,priority:-1},test:()=>Promise.resolve(!0),add:async s=>[...s,...cr],remove:async s=>s.filter(t=>!cr.includes(t))},Sd="WorkerGlobalScope"in globalThis&&globalThis instanceof globalThis.WorkerGlobalScope;function tn(s){return Sd?!1:document.createElement("video").canPlayType(s)!==""}const Bd={extension:{type:T.DetectionParser,priority:0},test:async()=>tn("video/mp4"),add:async s=>[...s,"mp4","m4v"],remove:async s=>s.filter(t=>t!=="mp4"&&t!=="m4v")},zd={extension:{type:T.DetectionParser,priority:0},test:async()=>tn("video/ogg"),add:async s=>[...s,"ogv"],remove:async s=>s.filter(t=>t!=="ogv")},Pd={extension:{type:T.DetectionParser,priority:0},test:async()=>tn("video/webm"),add:async s=>[...s,"webm"],remove:async s=>s.filter(t=>t!=="webm")},Td={extension:{type:T.DetectionParser,priority:0},test:async()=>Do("data:image/webp;base64,UklGRh4AAABXRUJQVlA4TBEAAAAvAAAAAAfQ//73v/+BiOh/AAA="),add:async s=>[...s,"webp"],remove:async s=>s.filter(t=>t!=="webp")};class Rd{constructor(){this._parsers=[],this._parsersValidated=!1,this.parsers=new Proxy(this._parsers,{set:(t,e,i)=>(this._parsersValidated=!1,t[e]=i,!0)}),this.promiseCache={};}reset(){this._parsersValidated=!1,this.promiseCache={};}_getLoadPromiseAndParser(t,e){const i={promise:null,parser:null};return i.promise=(async()=>{let n=null,r=null;if(e.loadParser&&(r=this._parserHash[e.loadParser],r||ot(`[Assets] specified load parser "${e.loadParser}" not found while loading ${t}`)),!r){for(let o=0;o<this.parsers.length;o++){const a=this.parsers[o];if(a.load&&a.test?.(t,e,this)){r=a;break}}if(!r)return ot(`[Assets] ${t} could not be loaded as we don't know how to parse it, ensure the correct parser has been added`),null}n=await r.load(t,e,this),i.parser=r;for(let o=0;o<this.parsers.length;o++){const a=this.parsers[o];a.parse&&a.parse&&await a.testParse?.(n,e,this)&&(n=await a.parse(n,e,this)||n,i.parser=a);}return n})(),i}async load(t,e){this._parsersValidated||this._validateParsers();let i=0;const n={},r=yi(t),o=Ot(t,c=>({alias:[c],src:c,data:{}})),a=o.length,l=o.map(async c=>{const h=Mt.toAbsolute(c.src);if(!n[c.src])try{this.promiseCache[h]||(this.promiseCache[h]=this._getLoadPromiseAndParser(h,c)),n[c.src]=await this.promiseCache[h].promise,e&&e(++i/a);}catch(d){throw delete this.promiseCache[h],delete n[c.src],new Error(`[Loader.load] Failed to load ${h}.
${d}`)}});return await Promise.all(l),r?n[o[0].src]:n}async unload(t){const i=Ot(t,n=>({alias:[n],src:n})).map(async n=>{const r=Mt.toAbsolute(n.src),o=this.promiseCache[r];if(o){const a=await o.promise;delete this.promiseCache[r],await o.parser?.unload?.(a,n,this);}});await Promise.all(i);}_validateParsers(){this._parsersValidated=!0,this._parserHash=this._parsers.filter(t=>t.name).reduce((t,e)=>(e.name?t[e.name]&&ot(`[Assets] loadParser name conflict "${e.name}"`):ot("[Assets] loadParser should have a name"),{...t,[e.name]:e}),{});}}function Ie(s,t){if(Array.isArray(t)){for(const e of t)if(s.startsWith(`data:${e}`))return !0;return !1}return s.startsWith(`data:${t}`)}function Ee(s,t){const e=s.split("?")[0],i=Mt.extname(e).toLowerCase();return Array.isArray(t)?t.includes(i):i===t}const Gd=".json",Od="application/json",Fd={extension:{type:T.LoadParser,priority:Zt.Low},name:"loadJson",test(s){return Ie(s,Od)||Ee(s,Gd)},async load(s){return await(await it.get().fetch(s)).json()}},Ld=".txt",Wd="text/plain",Ud={name:"loadTxt",extension:{type:T.LoadParser,priority:Zt.Low,name:"loadTxt"},test(s){return Ie(s,Wd)||Ee(s,Ld)},async load(s){return await(await it.get().fetch(s)).text()}},Dd=["normal","bold","100","200","300","400","500","600","700","800","900"],Kd=[".ttf",".otf",".woff",".woff2"],Nd=["font/ttf","font/otf","font/woff","font/woff2"],Hd=/^(--|-?[A-Z_])[0-9A-Z_-]*$/i;function jd(s){const t=Mt.extname(s),n=Mt.basename(s,t).replace(/(-|_)/g," ").toLowerCase().split(" ").map(a=>a.charAt(0).toUpperCase()+a.slice(1));let r=n.length>0;for(const a of n)if(!a.match(Hd)){r=!1;break}let o=n.join(" ");return r||(o=`"${o.replace(/[\\"]/g,"\\$&")}"`),o}const Yd=/^[0-9A-Za-z%:/?#\[\]@!\$&'()\*\+,;=\-._~]*$/;function Xd(s){return Yd.test(s)?s:encodeURI(s)}const Vd={extension:{type:T.LoadParser,priority:Zt.Low},name:"loadWebFont",test(s){return Ie(s,Nd)||Ee(s,Kd)},async load(s,t){const e=it.get().getFontFaceSet();if(e){const i=[],n=t.data?.family??jd(s),r=t.data?.weights?.filter(a=>Dd.includes(a))??["normal"],o=t.data??{};for(let a=0;a<r.length;a++){const l=r[a],c=new FontFace(n,`url(${Xd(s)})`,{...o,weight:l});await c.load(),e.add(c),i.push(c);}return et.set(`${n}-and-url`,{url:s,fontFaces:i}),i.length===1?i[0]:i}return ot("[loadWebFont] FontFace API is not supported. Skipping loading font"),null},unload(s){(Array.isArray(s)?s:[s]).forEach(t=>{et.remove(`${t.family}-and-url`),it.get().getFontFaceSet().delete(t);});}};function en(s,t=1){const e=ke.RETINA_PREFIX?.exec(s);return e?parseFloat(e[1]):t}function sn(s,t,e){s.label=e,s._sourceOrigin=e;const i=new W({source:s,label:e}),n=()=>{delete t.promiseCache[e],et.has(e)&&et.remove(e);};return i.source.once("destroy",()=>{t.promiseCache[e]&&(ot("[Assets] A TextureSource managed by Assets was destroyed instead of unloaded! Use Assets.unload() instead of destroying the TextureSource."),n());}),i.once("destroy",()=>{s.destroyed||(ot("[Assets] A Texture managed by Assets was destroyed instead of unloaded! Use Assets.unload() instead of destroying the Texture."),n());}),i}const qd=".svg",Qd="image/svg+xml",Zd={extension:{type:T.LoadParser,priority:Zt.Low,name:"loadSVG"},name:"loadSVG",config:{crossOrigin:"anonymous",parseAsGraphicsContext:!1},test(s){return Ie(s,Qd)||Ee(s,qd)},async load(s,t,e){return t.data?.parseAsGraphicsContext??this.config.parseAsGraphicsContext?$d(s):Jd(s,t,e,this.config.crossOrigin)},unload(s){s.destroy(!0);}};async function Jd(s,t,e,i){const r=await(await it.get().fetch(s)).blob(),o=URL.createObjectURL(r),a=new Image;a.src=o,a.crossOrigin=i,await a.decode(),URL.revokeObjectURL(o);const l=document.createElement("canvas"),c=l.getContext("2d"),h=t.data?.resolution||en(s),d=t.data?.width??a.width,_=t.data?.height??a.height;l.width=d*h,l.height=_*h,c.drawImage(a,0,0,d*h,_*h);const{parseAsGraphicsContext:u,...f}=t.data??{},p=new we({resource:l,alphaMode:"premultiply-alpha-on-upload",resolution:h,...f});return sn(p,e,s)}async function $d(s){const e=await(await it.get().fetch(s)).text(),i=new Pt;return i.svg(e),i}const tu=`(function () {
    'use strict';

    const WHITE_PNG = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAwMCAO+ip1sAAAAASUVORK5CYII=";
    async function checkImageBitmap() {
      try {
        if (typeof createImageBitmap !== "function")
          return false;
        const response = await fetch(WHITE_PNG);
        const imageBlob = await response.blob();
        const imageBitmap = await createImageBitmap(imageBlob);
        return imageBitmap.width === 1 && imageBitmap.height === 1;
      } catch (_e) {
        return false;
      }
    }
    void checkImageBitmap().then((result) => {
      self.postMessage(result);
    });

})();
`;let ye=null,Gs=class{constructor(){ye||(ye=URL.createObjectURL(new Blob([tu],{type:"application/javascript"}))),this.worker=new Worker(ye);}};Gs.revokeObjectURL=function(){ye&&(URL.revokeObjectURL(ye),ye=null);};const eu=`(function () {
    'use strict';

    async function loadImageBitmap(url, alphaMode) {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(\`[WorkerManager.loadImageBitmap] Failed to fetch \${url}: \${response.status} \${response.statusText}\`);
      }
      const imageBlob = await response.blob();
      return alphaMode === "premultiplied-alpha" ? createImageBitmap(imageBlob, { premultiplyAlpha: "none" }) : createImageBitmap(imageBlob);
    }
    self.onmessage = async (event) => {
      try {
        const imageBitmap = await loadImageBitmap(event.data.data[0], event.data.data[1]);
        self.postMessage({
          data: imageBitmap,
          uuid: event.data.uuid,
          id: event.data.id
        }, [imageBitmap]);
      } catch (e) {
        self.postMessage({
          error: e,
          uuid: event.data.uuid,
          id: event.data.id
        });
      }
    };

})();
`;let xe=null;class Ko{constructor(){xe||(xe=URL.createObjectURL(new Blob([eu],{type:"application/javascript"}))),this.worker=new Worker(xe);}}Ko.revokeObjectURL=function(){xe&&(URL.revokeObjectURL(xe),xe=null);};let hr=0,cs;class iu{constructor(){this._initialized=!1,this._createdWorkers=0,this._workerPool=[],this._queue=[],this._resolveHash={};}isImageBitmapSupported(){return this._isImageBitmapSupported!==void 0?this._isImageBitmapSupported:(this._isImageBitmapSupported=new Promise(t=>{const{worker:e}=new Gs;e.addEventListener("message",i=>{e.terminate(),Gs.revokeObjectURL(),t(i.data);});}),this._isImageBitmapSupported)}loadImageBitmap(t,e){return this._run("loadImageBitmap",[t,e?.data?.alphaMode])}async _initWorkers(){this._initialized||(this._initialized=!0);}_getWorker(){cs===void 0&&(cs=navigator.hardwareConcurrency||4);let t=this._workerPool.pop();return !t&&this._createdWorkers<cs&&(this._createdWorkers++,t=new Ko().worker,t.addEventListener("message",e=>{this._complete(e.data),this._returnWorker(e.target),this._next();})),t}_returnWorker(t){this._workerPool.push(t);}_complete(t){t.error!==void 0?this._resolveHash[t.uuid].reject(t.error):this._resolveHash[t.uuid].resolve(t.data),this._resolveHash[t.uuid]=null;}async _run(t,e){await this._initWorkers();const i=new Promise((n,r)=>{this._queue.push({id:t,arguments:e,resolve:n,reject:r});});return this._next(),i}_next(){if(!this._queue.length)return;const t=this._getWorker();if(!t)return;const e=this._queue.pop(),i=e.id;this._resolveHash[hr]={resolve:e.resolve,reject:e.reject},t.postMessage({data:e.arguments,uuid:hr++,id:i});}}const dr=new iu,su=[".jpeg",".jpg",".png",".webp",".avif"],nu=["image/jpeg","image/png","image/webp","image/avif"];async function ru(s,t){const e=await it.get().fetch(s);if(!e.ok)throw new Error(`[loadImageBitmap] Failed to fetch ${s}: ${e.status} ${e.statusText}`);const i=await e.blob();return t?.data?.alphaMode==="premultiplied-alpha"?createImageBitmap(i,{premultiplyAlpha:"none"}):createImageBitmap(i)}const No={name:"loadTextures",extension:{type:T.LoadParser,priority:Zt.High,name:"loadTextures"},config:{preferWorkers:!0,preferCreateImageBitmap:!0,crossOrigin:"anonymous"},test(s){return Ie(s,nu)||Ee(s,su)},async load(s,t,e){let i=null;globalThis.createImageBitmap&&this.config.preferCreateImageBitmap?this.config.preferWorkers&&await dr.isImageBitmapSupported()?i=await dr.loadImageBitmap(s,t):i=await ru(s,t):i=await new Promise((r,o)=>{i=new Image,i.crossOrigin=this.config.crossOrigin,i.src=s,i.complete?r(i):(i.onload=()=>{r(i);},i.onerror=o);});const n=new we({resource:i,alphaMode:"premultiply-alpha-on-upload",resolution:t.data?.resolution||en(s),...t.data});return sn(n,e,s)},unload(s){s.destroy(!0);}},Ho=[".mp4",".m4v",".webm",".ogg",".ogv",".h264",".avi",".mov"],ou=Ho.map(s=>`video/${s.substring(1)}`);function au(s,t,e){e===void 0&&!t.startsWith("data:")?s.crossOrigin=cu(t):e!==!1&&(s.crossOrigin=typeof e=="string"?e:"anonymous");}function lu(s){return new Promise((t,e)=>{s.addEventListener("canplaythrough",i),s.addEventListener("error",n),s.load();function i(){r(),t();}function n(o){r(),e(o);}function r(){s.removeEventListener("canplaythrough",i),s.removeEventListener("error",n);}})}function cu(s,t=globalThis.location){if(s.startsWith("data:"))return "";t||(t=globalThis.location);const e=new URL(s,document.baseURI);return e.hostname!==t.hostname||e.port!==t.port||e.protocol!==t.protocol?"anonymous":""}const hu={name:"loadVideo",extension:{type:T.LoadParser,name:"loadVideo"},test(s){const t=Ie(s,ou),e=Ee(s,Ho);return t||e},async load(s,t,e){const i={...ui.defaultOptions,resolution:t.data?.resolution||en(s),alphaMode:t.data?.alphaMode||await jr(),...t.data},n=document.createElement("video"),r={preload:i.autoLoad!==!1?"auto":void 0,"webkit-playsinline":i.playsinline!==!1?"":void 0,playsinline:i.playsinline!==!1?"":void 0,muted:i.muted===!0?"":void 0,loop:i.loop===!0?"":void 0,autoplay:i.autoPlay!==!1?"":void 0};Object.keys(r).forEach(l=>{const c=r[l];c!==void 0&&n.setAttribute(l,c);}),i.muted===!0&&(n.muted=!0),au(n,s,i.crossorigin);const o=document.createElement("source");let a;if(s.startsWith("data:"))a=s.slice(5,s.indexOf(";"));else if(!s.startsWith("blob:")){const l=s.split("?")[0].slice(s.lastIndexOf(".")+1).toLowerCase();a=ui.MIME_TYPES[l]||`video/${l}`;}return o.src=s,a&&(o.type=a),new Promise(l=>{const c=async()=>{const h=new ui({...i,resource:n});n.removeEventListener("canplay",c),t.data.preload&&await lu(n),l(sn(h,e,s));};n.addEventListener("canplay",c),n.appendChild(o);})},unload(s){s.destroy(!0);}},jo={extension:{type:T.ResolveParser,name:"resolveTexture"},test:No.test,parse:s=>({resolution:parseFloat(ke.RETINA_PREFIX.exec(s)?.[1]??"1"),format:s.split(".").pop(),src:s})},du={extension:{type:T.ResolveParser,priority:-2,name:"resolveJson"},test:s=>ke.RETINA_PREFIX.test(s)&&s.endsWith(".json"),parse:jo.parse};class uu{constructor(){this._detections=[],this._initialized=!1,this.resolver=new ke,this.loader=new Rd,this.cache=et,this._backgroundLoader=new kd(this.loader),this._backgroundLoader.active=!0,this.reset();}async init(t={}){if(this._initialized){ot("[Assets]AssetManager already initialized, did you load before calling this Assets.init()?");return}if(this._initialized=!0,t.defaultSearchParams&&this.resolver.setDefaultSearchParams(t.defaultSearchParams),t.basePath&&(this.resolver.basePath=t.basePath),t.bundleIdentifier&&this.resolver.setBundleIdentifier(t.bundleIdentifier),t.manifest){let r=t.manifest;typeof r=="string"&&(r=await this.load(r)),this.resolver.addManifest(r);}const e=t.texturePreference?.resolution??1,i=typeof e=="number"?[e]:e,n=await this._detectFormats({preferredFormats:t.texturePreference?.format,skipDetections:t.skipDetections,detections:this._detections});this.resolver.prefer({params:{format:n,resolution:i}}),t.preferences&&this.setPreferences(t.preferences);}add(t){this.resolver.add(t);}async load(t,e){this._initialized||await this.init();const i=yi(t),n=Ot(t).map(a=>{if(typeof a!="string"){const l=this.resolver.getAlias(a);return l.some(c=>!this.resolver.hasKey(c))&&this.add(a),Array.isArray(l)?l[0]:l}return this.resolver.hasKey(a)||this.add({alias:a,src:a}),a}),r=this.resolver.resolve(n),o=await this._mapLoadToResolve(r,e);return i?o[n[0]]:o}addBundle(t,e){this.resolver.addBundle(t,e);}async loadBundle(t,e){this._initialized||await this.init();let i=!1;typeof t=="string"&&(i=!0,t=[t]);const n=this.resolver.resolveBundle(t),r={},o=Object.keys(n);let a=0,l=0;const c=()=>{e?.(++a/l);},h=o.map(d=>{const _=n[d];return l+=Object.keys(_).length,this._mapLoadToResolve(_,c).then(u=>{r[d]=u;})});return await Promise.all(h),i?r[t[0]]:r}async backgroundLoad(t){this._initialized||await this.init(),typeof t=="string"&&(t=[t]);const e=this.resolver.resolve(t);this._backgroundLoader.add(Object.values(e));}async backgroundLoadBundle(t){this._initialized||await this.init(),typeof t=="string"&&(t=[t]);const e=this.resolver.resolveBundle(t);Object.values(e).forEach(i=>{this._backgroundLoader.add(Object.values(i));});}reset(){this.resolver.reset(),this.loader.reset(),this.cache.reset(),this._initialized=!1;}get(t){if(typeof t=="string")return et.get(t);const e={};for(let i=0;i<t.length;i++)e[i]=et.get(t[i]);return e}async _mapLoadToResolve(t,e){const i=[...new Set(Object.values(t))];this._backgroundLoader.active=!1;const n=await this.loader.load(i,e);this._backgroundLoader.active=!0;const r={};return i.forEach(o=>{const a=n[o.src],l=[o.src];o.alias&&l.push(...o.alias),l.forEach(c=>{r[c]=a;}),et.set(l,a);}),r}async unload(t){this._initialized||await this.init();const e=Ot(t).map(n=>typeof n!="string"?n.src:n),i=this.resolver.resolve(e);await this._unloadFromResolved(i);}async unloadBundle(t){this._initialized||await this.init(),t=Ot(t);const e=this.resolver.resolveBundle(t),i=Object.keys(e).map(n=>this._unloadFromResolved(e[n]));await Promise.all(i);}async _unloadFromResolved(t){const e=Object.values(t);e.forEach(i=>{et.remove(i.src);}),await this.loader.unload(e);}async _detectFormats(t){let e=[];t.preferredFormats&&(e=Array.isArray(t.preferredFormats)?t.preferredFormats:[t.preferredFormats]);for(const i of t.detections)t.skipDetections||await i.test()?e=await i.add(e):t.skipDetections||(e=await i.remove(e));return e=e.filter((i,n)=>e.indexOf(i)===n),e}get detections(){return this._detections}setPreferences(t){this.loader.parsers.forEach(e=>{e.config&&Object.keys(e.config).filter(i=>i in t).forEach(i=>{e.config[i]=t[i];});});}}const ce=new uu;yt.handleByList(T.LoadParser,ce.loader.parsers).handleByList(T.ResolveParser,ce.resolver.parsers).handleByList(T.CacheParser,ce.cache.parsers).handleByList(T.DetectionParser,ce.detections);yt.add(Id,Md,Ed,Td,Bd,zd,Pd,Fd,Ud,Vd,Zd,No,hu,wd,vd,jo,du);const ur={loader:T.LoadParser,resolver:T.ResolveParser,cache:T.CacheParser,detection:T.DetectionParser};yt.handle(T.Asset,s=>{const t=s.ref;Object.entries(ur).filter(([e])=>!!t[e]).forEach(([e,i])=>yt.add(Object.assign(t[e],{extension:t[e].extension??i})));},s=>{const t=s.ref;Object.keys(ur).filter(e=>!!t[e]).forEach(e=>yt.remove(t[e]));});class nt extends Or{constructor(t){t instanceof Pt&&(t={context:t});const{context:e,roundPixels:i,...n}=t||{};super({label:"Graphics",...n}),this.renderPipeId="graphics",e?this._context=e:this._context=this._ownedContext=new Pt,this._context.on("update",this.onViewUpdate,this),this.allowChildren=!1,this.roundPixels=i??!1;}set context(t){t!==this._context&&(this._context.off("update",this.onViewUpdate,this),this._context=t,this._context.on("update",this.onViewUpdate,this),this.onViewUpdate());}get context(){return this._context}get bounds(){return this._context.bounds}updateBounds(){}containsPoint(t){return this._context.containsPoint(t)}destroy(t){this._ownedContext&&!t?this._ownedContext.destroy(t):(t===!0||t?.context===!0)&&this._context.destroy(t),this._ownedContext=null,this._context=null,super.destroy(t);}_callContextMethod(t,e){return this.context[t](...e),this}setFillStyle(...t){return this._callContextMethod("setFillStyle",t)}setStrokeStyle(...t){return this._callContextMethod("setStrokeStyle",t)}fill(...t){return this._callContextMethod("fill",t)}stroke(...t){return this._callContextMethod("stroke",t)}texture(...t){return this._callContextMethod("texture",t)}beginPath(){return this._callContextMethod("beginPath",[])}cut(){return this._callContextMethod("cut",[])}arc(...t){return this._callContextMethod("arc",t)}arcTo(...t){return this._callContextMethod("arcTo",t)}arcToSvg(...t){return this._callContextMethod("arcToSvg",t)}bezierCurveTo(...t){return this._callContextMethod("bezierCurveTo",t)}closePath(){return this._callContextMethod("closePath",[])}ellipse(...t){return this._callContextMethod("ellipse",t)}circle(...t){return this._callContextMethod("circle",t)}path(...t){return this._callContextMethod("path",t)}lineTo(...t){return this._callContextMethod("lineTo",t)}moveTo(...t){return this._callContextMethod("moveTo",t)}quadraticCurveTo(...t){return this._callContextMethod("quadraticCurveTo",t)}rect(...t){return this._callContextMethod("rect",t)}roundRect(...t){return this._callContextMethod("roundRect",t)}poly(...t){return this._callContextMethod("poly",t)}regularPoly(...t){return this._callContextMethod("regularPoly",t)}roundPoly(...t){return this._callContextMethod("roundPoly",t)}roundShape(...t){return this._callContextMethod("roundShape",t)}filletRect(...t){return this._callContextMethod("filletRect",t)}chamferRect(...t){return this._callContextMethod("chamferRect",t)}star(...t){return this._callContextMethod("star",t)}svg(...t){return this._callContextMethod("svg",t)}restore(...t){return this._callContextMethod("restore",t)}save(){return this._callContextMethod("save",[])}getTransform(){return this.context.getTransform()}resetTransform(){return this._callContextMethod("resetTransform",[])}rotateTransform(...t){return this._callContextMethod("rotate",t)}scaleTransform(...t){return this._callContextMethod("scale",t)}setTransform(...t){return this._callContextMethod("setTransform",t)}transform(...t){return this._callContextMethod("transform",t)}translateTransform(...t){return this._callContextMethod("translate",t)}clear(){return this._callContextMethod("clear",[])}get fillStyle(){return this._context.fillStyle}set fillStyle(t){this._context.fillStyle=t;}get strokeStyle(){return this._context.strokeStyle}set strokeStyle(t){this._context.strokeStyle=t;}clone(t=!1){return t?new nt(this._context.clone()):(this._ownedContext=null,new nt(this._context))}lineStyle(t,e,i){D(N,"Graphics#lineStyle is no longer needed. Use Graphics#setStrokeStyle to set the stroke style.");const n={};return t&&(n.width=t),e&&(n.color=e),i&&(n.alpha=i),this.context.strokeStyle=n,this}beginFill(t,e){D(N,"Graphics#beginFill is no longer needed. Use Graphics#fill to fill the shape with the desired style.");const i={};return t!==void 0&&(i.color=t),e!==void 0&&(i.alpha=e),this.context.fillStyle=i,this}endFill(){D(N,"Graphics#endFill is no longer needed. Use Graphics#fill to fill the shape with the desired style."),this.context.fill();const t=this.context.strokeStyle;return (t.width!==Pt.defaultStrokeStyle.width||t.color!==Pt.defaultStrokeStyle.color||t.alpha!==Pt.defaultStrokeStyle.alpha)&&this.context.stroke(),this}drawCircle(...t){return D(N,"Graphics#drawCircle has been renamed to Graphics#circle"),this._callContextMethod("circle",t)}drawEllipse(...t){return D(N,"Graphics#drawEllipse has been renamed to Graphics#ellipse"),this._callContextMethod("ellipse",t)}drawPolygon(...t){return D(N,"Graphics#drawPolygon has been renamed to Graphics#poly"),this._callContextMethod("poly",t)}drawRect(...t){return D(N,"Graphics#drawRect has been renamed to Graphics#rect"),this._callContextMethod("rect",t)}drawRoundedRect(...t){return D(N,"Graphics#drawRoundedRect has been renamed to Graphics#roundRect"),this._callContextMethod("roundRect",t)}drawStar(...t){return D(N,"Graphics#drawStar has been renamed to Graphics#star"),this._callContextMethod("star",t)}}class pt extends W{static create(t){return new pt({source:new Wt(t)})}resize(t,e,i){return this.source.resize(t,e,i),this}}var Os=(s=>(s.CLAMP="clamp-to-edge",s.REPEAT="repeat",s.MIRRORED_REPEAT="mirror-repeat",s))(Os||{});new Proxy(Os,{get(s,t){return D(N,`DRAW_MODES.${t} is deprecated, use '${Os[t]}' instead`),s[t]}});var Fs=(s=>(s.NEAREST="nearest",s.LINEAR="linear",s))(Fs||{});new Proxy(Fs,{get(s,t){return D(N,`DRAW_MODES.${t} is deprecated, use '${Fs[t]}' instead`),s[t]}});yt.add(Ta,Ra);var Ls=`in vec2 aPosition;
out vec2 vTextureCoord;

uniform vec4 uInputSize;
uniform vec4 uOutputFrame;
uniform vec4 uOutputTexture;

vec4 filterVertexPosition( void )
{
    vec2 position = aPosition * uOutputFrame.zw + uOutputFrame.xy;
    
    position.x = position.x * (2.0 / uOutputTexture.x) - 1.0;
    position.y = position.y * (2.0*uOutputTexture.z / uOutputTexture.y) - uOutputTexture.z;

    return vec4(position, 0.0, 1.0);
}

vec2 filterTextureCoord( void )
{
    return aPosition * (uOutputFrame.zw * uInputSize.zw);
}

void main(void)
{
    gl_Position = filterVertexPosition();
    vTextureCoord = filterTextureCoord();
}
`,Ws=`struct GlobalFilterUniforms {
  uInputSize:vec4<f32>,
  uInputPixel:vec4<f32>,
  uInputClamp:vec4<f32>,
  uOutputFrame:vec4<f32>,
  uGlobalFrame:vec4<f32>,
  uOutputTexture:vec4<f32>,
};

@group(0) @binding(0) var<uniform> gfu: GlobalFilterUniforms;

struct VSOutput {
    @builtin(position) position: vec4<f32>,
    @location(0) uv : vec2<f32>
  };

fn filterVertexPosition(aPosition:vec2<f32>) -> vec4<f32>
{
    var position = aPosition * gfu.uOutputFrame.zw + gfu.uOutputFrame.xy;

    position.x = position.x * (2.0 / gfu.uOutputTexture.x) - 1.0;
    position.y = position.y * (2.0*gfu.uOutputTexture.z / gfu.uOutputTexture.y) - gfu.uOutputTexture.z;

    return vec4(position, 0.0, 1.0);
}

fn filterTextureCoord( aPosition:vec2<f32> ) -> vec2<f32>
{
    return aPosition * (gfu.uOutputFrame.zw * gfu.uInputSize.zw);
}

fn globalTextureCoord( aPosition:vec2<f32> ) -> vec2<f32>
{
  return  (aPosition.xy / gfu.uGlobalFrame.zw) + (gfu.uGlobalFrame.xy / gfu.uGlobalFrame.zw);  
}

fn getSize() -> vec2<f32>
{
  return gfu.uGlobalFrame.zw;
}
  
@vertex
fn mainVertex(
  @location(0) aPosition : vec2<f32>, 
) -> VSOutput {
  return VSOutput(
   filterVertexPosition(aPosition),
   filterTextureCoord(aPosition)
  );
}`,ku=`
in vec2 vTextureCoord;
out vec4 finalColor;

uniform sampler2D uTexture;
uniform vec2 uOffset;

void main(void)
{
    vec4 color = vec4(0.0);

    // Sample top left pixel
    color += texture(uTexture, vec2(vTextureCoord.x - uOffset.x, vTextureCoord.y + uOffset.y));

    // Sample top right pixel
    color += texture(uTexture, vec2(vTextureCoord.x + uOffset.x, vTextureCoord.y + uOffset.y));

    // Sample bottom right pixel
    color += texture(uTexture, vec2(vTextureCoord.x + uOffset.x, vTextureCoord.y - uOffset.y));

    // Sample bottom left pixel
    color += texture(uTexture, vec2(vTextureCoord.x - uOffset.x, vTextureCoord.y - uOffset.y));

    // Average
    color *= 0.25;

    finalColor = color;
}`,Iu=`struct KawaseBlurUniforms {
  uOffset:vec2<f32>,
};

@group(0) @binding(1) var uTexture: texture_2d<f32>; 
@group(0) @binding(2) var uSampler: sampler;
@group(1) @binding(0) var<uniform> kawaseBlurUniforms : KawaseBlurUniforms;

@fragment
fn mainFragment(
  @builtin(position) position: vec4<f32>,
  @location(0) uv : vec2<f32>
) -> @location(0) vec4<f32> {
  let uOffset = kawaseBlurUniforms.uOffset;
  var color: vec4<f32> = vec4<f32>(0.0);

  // Sample top left pixel
  color += textureSample(uTexture, uSampler, vec2<f32>(uv.x - uOffset.x, uv.y + uOffset.y));
  // Sample top right pixel
  color += textureSample(uTexture, uSampler, vec2<f32>(uv.x + uOffset.x, uv.y + uOffset.y));
  // Sample bottom right pixel
  color += textureSample(uTexture, uSampler, vec2<f32>(uv.x + uOffset.x, uv.y - uOffset.y));
  // Sample bottom left pixel
  color += textureSample(uTexture, uSampler, vec2<f32>(uv.x - uOffset.x, uv.y - uOffset.y));
  // Average
  color *= 0.25;

  return color;
}`,Eu=`
precision highp float;
in vec2 vTextureCoord;
out vec4 finalColor;

uniform sampler2D uTexture;
uniform vec2 uOffset;

uniform vec4 uInputClamp;

void main(void)
{
    vec4 color = vec4(0.0);

    // Sample top left pixel
    color += texture(uTexture, clamp(vec2(vTextureCoord.x - uOffset.x, vTextureCoord.y + uOffset.y), uInputClamp.xy, uInputClamp.zw));

    // Sample top right pixel
    color += texture(uTexture, clamp(vec2(vTextureCoord.x + uOffset.x, vTextureCoord.y + uOffset.y), uInputClamp.xy, uInputClamp.zw));

    // Sample bottom right pixel
    color += texture(uTexture, clamp(vec2(vTextureCoord.x + uOffset.x, vTextureCoord.y - uOffset.y), uInputClamp.xy, uInputClamp.zw));

    // Sample bottom left pixel
    color += texture(uTexture, clamp(vec2(vTextureCoord.x - uOffset.x, vTextureCoord.y - uOffset.y), uInputClamp.xy, uInputClamp.zw));

    // Average
    color *= 0.25;

    finalColor = color;
}
`,Mu=`struct KawaseBlurUniforms {
  uOffset:vec2<f32>,
};

struct GlobalFilterUniforms {
  uInputSize:vec4<f32>,
  uInputPixel:vec4<f32>,
  uInputClamp:vec4<f32>,
  uOutputFrame:vec4<f32>,
  uGlobalFrame:vec4<f32>,
  uOutputTexture:vec4<f32>,
};

@group(0) @binding(0) var<uniform> gfu: GlobalFilterUniforms;

@group(0) @binding(1) var uTexture: texture_2d<f32>; 
@group(0) @binding(2) var uSampler: sampler;
@group(1) @binding(0) var<uniform> kawaseBlurUniforms : KawaseBlurUniforms;

@fragment
fn mainFragment(
  @builtin(position) position: vec4<f32>,
  @location(0) uv : vec2<f32>
) -> @location(0) vec4<f32> {
  let uOffset = kawaseBlurUniforms.uOffset;
  var color: vec4<f32> = vec4(0.0);

  // Sample top left pixel
  color += textureSample(uTexture, uSampler, clamp(vec2<f32>(uv.x - uOffset.x, uv.y + uOffset.y), gfu.uInputClamp.xy, gfu.uInputClamp.zw));
  // Sample top right pixel
  color += textureSample(uTexture, uSampler, clamp(vec2<f32>(uv.x + uOffset.x, uv.y + uOffset.y), gfu.uInputClamp.xy, gfu.uInputClamp.zw));
  // Sample bottom right pixel
  color += textureSample(uTexture, uSampler, clamp(vec2<f32>(uv.x + uOffset.x, uv.y - uOffset.y), gfu.uInputClamp.xy, gfu.uInputClamp.zw));
  // Sample bottom left pixel
  color += textureSample(uTexture, uSampler, clamp(vec2<f32>(uv.x - uOffset.x, uv.y - uOffset.y), gfu.uInputClamp.xy, gfu.uInputClamp.zw));
  // Average
  color *= 0.25;
    
  return color;
}`,Su=Object.defineProperty,Bu=(s,t,e)=>t in s?Su(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e,ae=(s,t,e)=>(Bu(s,typeof t!="symbol"?t+"":t,e),e);const Yo=class Xo extends vs{constructor(...t){let e=t[0]??{};(typeof e=="number"||Array.isArray(e))&&(D("6.0.0","KawaseBlurFilter constructor params are now options object. See params: { strength, quality, clamp, pixelSize }"),e={strength:e},t[1]!==void 0&&(e.quality=t[1]),t[2]!==void 0&&(e.clamp=t[2])),e={...Xo.DEFAULT_OPTIONS,...e};const i=Qt.from({vertex:{source:Ws,entryPoint:"mainVertex"},fragment:{source:e?.clamp?Mu:Iu,entryPoint:"mainFragment"}}),n=Ce.from({vertex:Ls,fragment:e?.clamp?Eu:ku,name:"kawase-blur-filter"});super({gpuProgram:i,glProgram:n,resources:{kawaseBlurUniforms:{uOffset:{value:new Float32Array(2),type:"vec2<f32>"}}}}),ae(this,"uniforms"),ae(this,"_pixelSize",{x:0,y:0}),ae(this,"_clamp"),ae(this,"_kernels",[]),ae(this,"_blur"),ae(this,"_quality"),this.uniforms=this.resources.kawaseBlurUniforms.uniforms,this.pixelSize=e.pixelSize??{x:1,y:1},Array.isArray(e.strength)?this.kernels=e.strength:typeof e.strength=="number"&&(this._blur=e.strength,this.quality=e.quality??3),this._clamp=!!e.clamp;}apply(t,e,i,n){const r=this.pixelSizeX/e.source.width,o=this.pixelSizeY/e.source.height;let a;if(this._quality===1||this._blur===0)a=this._kernels[0]+.5,this.uniforms.uOffset[0]=a*r,this.uniforms.uOffset[1]=a*o,t.applyFilter(this,e,i,n);else {const l=Ke.getSameSizeTexture(e);let c=e,h=l,d;const _=this._quality-1;for(let u=0;u<_;u++)a=this._kernels[u]+.5,this.uniforms.uOffset[0]=a*r,this.uniforms.uOffset[1]=a*o,t.applyFilter(this,c,h,!0),d=c,c=h,h=d;a=this._kernels[_]+.5,this.uniforms.uOffset[0]=a*r,this.uniforms.uOffset[1]=a*o,t.applyFilter(this,c,i,n),Ke.returnTexture(l);}}get strength(){return this._blur}set strength(t){this._blur=t,this._generateKernels();}get quality(){return this._quality}set quality(t){this._quality=Math.max(1,Math.round(t)),this._generateKernels();}get kernels(){return this._kernels}set kernels(t){Array.isArray(t)&&t.length>0?(this._kernels=t,this._quality=t.length,this._blur=Math.max(...t)):(this._kernels=[0],this._quality=1);}get pixelSize(){return this._pixelSize}set pixelSize(t){if(typeof t=="number"){this.pixelSizeX=this.pixelSizeY=t;return}if(Array.isArray(t)){this.pixelSizeX=t[0],this.pixelSizeY=t[1];return}this._pixelSize=t;}get pixelSizeX(){return this.pixelSize.x}set pixelSizeX(t){this.pixelSize.x=t;}get pixelSizeY(){return this.pixelSize.y}set pixelSizeY(t){this.pixelSize.y=t;}get clamp(){return this._clamp}_updatePadding(){this.padding=Math.ceil(this._kernels.reduce((t,e)=>t+e+.5,0));}_generateKernels(){const t=this._blur,e=this._quality,i=[t];if(t>0){let n=t;const r=t/e;for(let o=1;o<e;o++)n-=r,i.push(n);}this._kernels=i,this._updatePadding();}};ae(Yo,"DEFAULT_OPTIONS",{strength:4,quality:3,clamp:!1,pixelSize:{x:1,y:1}});let zu=Yo;var Pu=`precision highp float;
in vec2 vTextureCoord;
out vec4 finalColor;

uniform sampler2D uTexture;
uniform float uAlpha;
uniform vec3 uColor;
uniform vec2 uOffset;

uniform vec4 uInputSize;

void main(void){
    vec4 sample = texture(uTexture, vTextureCoord - uOffset * uInputSize.zw);

    // Premultiply alpha
    sample.rgb = uColor.rgb * sample.a;

    // alpha user alpha
    sample *= uAlpha;

    finalColor = sample;
}`,Tu=`struct DropShadowUniforms {
  uAlpha: f32,
  uColor: vec3<f32>,
  uOffset: vec2<f32>,
};

struct GlobalFilterUniforms {
  uInputSize:vec4<f32>,
  uInputPixel:vec4<f32>,
  uInputClamp:vec4<f32>,
  uOutputFrame:vec4<f32>,
  uGlobalFrame:vec4<f32>,
  uOutputTexture:vec4<f32>,
};

@group(0) @binding(0) var<uniform> gfu: GlobalFilterUniforms;

@group(0) @binding(1) var uTexture: texture_2d<f32>; 
@group(0) @binding(2) var uSampler: sampler;
@group(1) @binding(0) var<uniform> dropShadowUniforms : DropShadowUniforms;

@fragment
fn mainFragment(
  @builtin(position) position: vec4<f32>,
  @location(0) uv : vec2<f32>
) -> @location(0) vec4<f32> {
  var color: vec4<f32> = textureSample(uTexture, uSampler, uv - dropShadowUniforms.uOffset * gfu.uInputSize.zw);

  // Premultiply alpha
  color = vec4<f32>(vec3<f32>(dropShadowUniforms.uColor.rgb * color.a), color.a);
  // alpha user alpha
  color *= dropShadowUniforms.uAlpha;

  return color;
}`,Ru=Object.defineProperty,Gu=(s,t,e)=>t in s?Ru(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e,pe=(s,t,e)=>(Gu(s,typeof t!="symbol"?t+"":t,e),e);const Vo=class qo extends vs{constructor(t){t={...qo.DEFAULT_OPTIONS,...t};const e=Qt.from({vertex:{source:Ws,entryPoint:"mainVertex"},fragment:{source:Tu,entryPoint:"mainFragment"}}),i=Ce.from({vertex:Ls,fragment:Pu,name:"drop-shadow-filter"});super({gpuProgram:e,glProgram:i,resources:{dropShadowUniforms:{uAlpha:{value:t.alpha,type:"f32"},uColor:{value:new Float32Array(3),type:"vec3<f32>"},uOffset:{value:t.offset,type:"vec2<f32>"}}},resolution:t.resolution}),pe(this,"uniforms"),pe(this,"shadowOnly",!1),pe(this,"_color"),pe(this,"_blurFilter"),pe(this,"_basePass"),this.uniforms=this.resources.dropShadowUniforms.uniforms,this._color=new st,this.color=t.color??0,this._blurFilter=new zu({strength:t.kernels??t.blur,quality:t.kernels?void 0:t.quality}),this._basePass=new vs({gpuProgram:Qt.from({vertex:{source:Ws,entryPoint:"mainVertex"},fragment:{source:`
                    @group(0) @binding(1) var uTexture: texture_2d<f32>; 
                    @group(0) @binding(2) var uSampler: sampler;
                    @fragment
                    fn mainFragment(
                        @builtin(position) position: vec4<f32>,
                        @location(0) uv : vec2<f32>
                    ) -> @location(0) vec4<f32> {
                        return textureSample(uTexture, uSampler, uv);
                    }
                    `,entryPoint:"mainFragment"}}),glProgram:Ce.from({vertex:Ls,fragment:`
                in vec2 vTextureCoord;
                out vec4 finalColor;
                uniform sampler2D uTexture;

                void main(void){
                    finalColor = texture(uTexture, vTextureCoord);
                }
                `,name:"drop-shadow-filter"}),resources:{}}),Object.assign(this,t);}apply(t,e,i,n){const r=Ke.getSameSizeTexture(e);t.applyFilter(this,e,r,!0),this._blurFilter.apply(t,r,i,n),this.shadowOnly||t.applyFilter(this._basePass,e,i,!1),Ke.returnTexture(r);}get offset(){return this.uniforms.uOffset}set offset(t){this.uniforms.uOffset=t,this._updatePadding();}get offsetX(){return this.offset.x}set offsetX(t){this.offset.x=t,this._updatePadding();}get offsetY(){return this.offset.y}set offsetY(t){this.offset.y=t,this._updatePadding();}get color(){return this._color.value}set color(t){this._color.setValue(t);const[e,i,n]=this._color.toArray();this.uniforms.uColor[0]=e,this.uniforms.uColor[1]=i,this.uniforms.uColor[2]=n;}get alpha(){return this.uniforms.uAlpha}set alpha(t){this.uniforms.uAlpha=t;}get blur(){return this._blurFilter.strength}set blur(t){this._blurFilter.strength=t,this._updatePadding();}get quality(){return this._blurFilter.quality}set quality(t){this._blurFilter.quality=t,this._updatePadding();}get kernels(){return this._blurFilter.kernels}set kernels(t){this._blurFilter.kernels=t;}get pixelSize(){return this._blurFilter.pixelSize}set pixelSize(t){typeof t=="number"&&(t={x:t,y:t}),Array.isArray(t)&&(t={x:t[0],y:t[1]}),this._blurFilter.pixelSize=t;}get pixelSizeX(){return this._blurFilter.pixelSizeX}set pixelSizeX(t){this._blurFilter.pixelSizeX=t;}get pixelSizeY(){return this._blurFilter.pixelSizeY}set pixelSizeY(t){this._blurFilter.pixelSizeY=t;}_updatePadding(){const t=Math.max(Math.abs(this.offsetX),Math.abs(this.offsetY));this.padding=t+this.blur*2+this.quality*4;}};pe(Vo,"DEFAULT_OPTIONS",{offset:{x:4,y:4},color:0,alpha:.5,shadowOnly:!1,kernels:void 0,blur:2,quality:3,pixelSize:{x:1,y:1},resolution:1});const a_={code:".image-container.svelte-h6gdep{display:flex;height:100%;flex-direction:column;justify-content:center;align-items:center;max-height:100%;border-radius:var(--radius-sm)}.pixi-target.svelte-h6gdep{width:100%;height:100%;position:absolute;top:0;left:0;z-index:1;display:block;opacity:0;pointer-events:none;border-radius:var(--radius-sm)}.pixi-target-crop.svelte-h6gdep{width:100%;height:100%;position:absolute;top:0;left:0;z-index:2;display:block;opacity:0;pointer-events:none;border-radius:var(--radius-sm)}.visible.svelte-h6gdep{opacity:1;pointer-events:auto}.pixi-target.svelte-h6gdep{width:100%;height:100%;position:absolute;top:0;left:0;right:0;bottom:0;overflow:hidden}.modal.svelte-h6gdep{position:absolute;height:100%;width:100%;left:0;right:0;margin:auto;z-index:var(--layer-top);display:flex;align-items:center}.modal-inner.svelte-h6gdep{height:100%;width:100%;background:var(--block-background-fill)}.dark-bg.svelte-h6gdep{background:#333}.crop-confirm-button.svelte-h6gdep{position:absolute;bottom:8px;left:0;right:0;margin:auto;z-index:var(--layer-top);display:flex;align-items:center;justify-content:center}",map:'{"version":3,"file":"ImageEditor.svelte","sources":["ImageEditor.svelte"],"sourcesContent":["<script lang=\\"ts\\" context=\\"module\\">export const EDITOR_KEY = Symbol(\\"editor\\");\\n<\/script>\\n\\n<script lang=\\"ts\\">import { onMount, createEventDispatcher, tick } from \\"svelte\\";\\nimport { get } from \\"svelte/store\\";\\nimport Toolbar, {} from \\"./Toolbar.svelte\\";\\nimport { CropTool } from \\"./crop/crop\\";\\nimport { ResizeTool } from \\"./resize/resize\\";\\nimport { Webcam } from \\"@gradio/image\\";\\nimport tinycolor, {} from \\"tinycolor2\\";\\nimport { ZoomTool } from \\"./zoom/zoom\\";\\nimport {} from \\"./core/commands\\";\\nimport { ImageEditor } from \\"./core/editor\\";\\nimport {} from \\"./brush/types\\";\\nimport { BrushTool } from \\"./brush/brush\\";\\nimport { create_drag } from \\"@gradio/upload\\";\\nimport SecondaryToolbar from \\"./SecondaryToolbar.svelte\\";\\nimport { Check } from \\"@gradio/icons\\";\\nimport {} from \\"./types\\";\\nimport Controls from \\"./Controls.svelte\\";\\nimport IconButton from \\"./IconButton.svelte\\";\\nconst { drag, open_file_upload } = create_drag();\\nconst dispatch = createEventDispatcher();\\nexport const antialias = true;\\nexport let changeable = false;\\nexport let sources = [\\"upload\\", \\"webcam\\", \\"clipboard\\"];\\nexport let transforms = [\\"crop\\", \\"resize\\"];\\nexport let canvas_size;\\nexport let is_dragging = false;\\nexport let background_image = false;\\nexport let brush_options;\\nexport let eraser_options;\\nexport let fixed_canvas = false;\\nexport let root;\\nexport let i18n;\\nexport let upload;\\nexport let composite;\\nexport let layers;\\nexport let background;\\nexport let border_region = 0;\\nexport let layer_options;\\nexport let current_tool;\\nexport let webcam_options;\\nexport let show_download_button = false;\\nexport let theme_mode;\\nexport let full_history = null;\\nlet pixi_target;\\nlet pixi_target_crop;\\n$: if (layer_options) {\\n    if (check_if_should_init()) {\\n        editor.set_layer_options(layer_options);\\n        refresh_tools();\\n    }\\n}\\nfunction refresh_tools() {\\n    if (!editor || !ready)\\n        return;\\n    editor.set_tool(current_tool);\\n    editor.set_subtool(current_subtool);\\n}\\n$: if (editor && ready && editor.layers) {\\n    const current_layers = get(editor.layers);\\n    if (current_layers.layers.length > 0 && !current_layers.active_layer) {\\n        refresh_tools_for_layer_changes(current_layers);\\n    }\\n}\\nfunction refresh_tools_for_layer_changes(current_layers) {\\n    if (!editor || !ready)\\n        return;\\n    if (current_layers.layers.length > 0 && !current_layers.active_layer) {\\n        editor.set_layer(current_layers.layers[0].id);\\n    }\\n    if (current_tool) {\\n        editor.set_tool(current_tool);\\n        if (current_subtool) {\\n            editor.set_subtool(current_subtool);\\n        }\\n    }\\n    if (brush && (current_tool === \\"draw\\" || current_tool === \\"erase\\")) {\\n        brush.set_tool(current_tool, current_subtool);\\n    }\\n}\\nfunction check_if_should_init() {\\n    return layer_options && editor && ready;\\n}\\nexport let has_drawn = false;\\nexport async function get_blobs() {\\n    if (!editor)\\n        return { background: null, layers: [], composite: null };\\n    if (!background_image && !has_drawn && !layers.length)\\n        return { background: null, layers: [], composite: null };\\n    const blobs = await editor.get_blobs();\\n    return blobs;\\n}\\nlet editor;\\nexport function add_image(image) {\\n    editor.add_image({ image });\\n}\\nlet pending_bg;\\nexport async function add_image_from_url(source) {\\n    if (!editor || !source || !check_if_should_init())\\n        return;\\n    let url;\\n    if (typeof source === \\"string\\") {\\n        url = source;\\n    }\\n    else if (source?.meta?._type === \\"gradio.FileData\\" && source?.url) {\\n        url = source.url;\\n    }\\n    else {\\n        console.warn(\\"Invalid source provided to add_image_from_url:\\", source);\\n        return;\\n    }\\n    try {\\n        pending_bg = editor.add_image_from_url(url);\\n        let pending_crop = crop.add_image_from_url(url);\\n        await Promise.all([pending_bg, pending_crop]);\\n        crop.set_tool(\\"image\\");\\n        crop.set_subtool(\\"crop\\");\\n        background_image = true;\\n        dispatch(\\"upload\\");\\n        dispatch(\\"input\\");\\n    }\\n    catch (error) {\\n        console.error(\\"Error adding image from URL:\\", error);\\n    }\\n}\\nexport async function add_layers_from_url(source) {\\n    if (!editor || !source.length || !check_if_should_init())\\n        return;\\n    if (Array.isArray(source) && source.every((item) => item?.meta?._type === \\"gradio.FileData\\")) {\\n        try {\\n            await pending_bg;\\n            await editor.add_layers_from_url(source.map((item) => item.url));\\n            dispatch(\\"change\\");\\n            dispatch(\\"input\\");\\n        }\\n        catch (error) {\\n            console.error(\\"Error adding layer from URL:\\", error);\\n        }\\n    }\\n}\\nlet brush;\\nlet zoom;\\nlet zoom_level = 1;\\nlet ready = false;\\nlet mounted = false;\\nlet min_zoom = true;\\nlet last_dimensions = { width: 0, height: 0 };\\nasync function handle_visibility_change() {\\n    if (!editor || !ready || !zoom)\\n        return;\\n    await tick();\\n    const is_visible = pixi_target.offsetParent !== null;\\n    if (is_visible) {\\n        const current_dimensions = pixi_target.getBoundingClientRect();\\n        if (current_dimensions.width !== last_dimensions.width || current_dimensions.height !== last_dimensions.height) {\\n            zoom.set_zoom(\\"fit\\");\\n            last_dimensions = {\\n                width: current_dimensions.width,\\n                height: current_dimensions.height\\n            };\\n        }\\n    }\\n}\\nonMount(() => {\\n    let intersection_observer;\\n    let resize_observer;\\n    init_image_editor().then(() => {\\n        mounted = true;\\n        intersection_observer = new IntersectionObserver(() => {\\n            handle_visibility_change();\\n        });\\n        resize_observer = new ResizeObserver(() => {\\n            handle_visibility_change();\\n        });\\n        intersection_observer.observe(pixi_target);\\n        resize_observer.observe(pixi_target);\\n        setTimeout(() => {\\n            if (full_history && editor) {\\n                editor.command_manager.replay(full_history, editor.context).then(() => {\\n                    refresh_tools_after_history();\\n                });\\n            }\\n        }, 0);\\n    });\\n    if (typeof window !== \\"undefined\\") {\\n        window.editor = editor;\\n    }\\n    return () => {\\n        if (intersection_observer) {\\n            intersection_observer.disconnect();\\n        }\\n        if (resize_observer) {\\n            resize_observer.disconnect();\\n        }\\n        if (editor) {\\n            editor.destroy();\\n        }\\n    };\\n});\\nfunction refresh_tools_after_history() {\\n    if (!editor || !ready)\\n        return;\\n    const current_layers = get(editor.layers);\\n    if (current_layers.layers.length > 0 && !current_layers.active_layer) {\\n        editor.set_layer(current_layers.layers[0].id);\\n    }\\n    if (current_tool) {\\n        editor.set_tool(current_tool);\\n        if (current_subtool) {\\n            editor.set_subtool(current_subtool);\\n        }\\n    }\\n    if (brush && (current_tool === \\"draw\\" || current_tool === \\"erase\\")) {\\n        brush.set_tool(current_tool, current_subtool);\\n    }\\n    full_history = editor.command_manager.history;\\n}\\nlet crop;\\nlet crop_zoom;\\nexport let can_undo = false;\\nlet can_redo = false;\\nasync function init_image_editor() {\\n    brush = new BrushTool();\\n    zoom = new ZoomTool();\\n    editor = new ImageEditor({\\n        target_element: pixi_target,\\n        width: canvas_size[0],\\n        height: canvas_size[1],\\n        tools: [\\"image\\", zoom, new ResizeTool(), brush],\\n        fixed_canvas,\\n        border_region,\\n        layer_options,\\n        theme_mode\\n    });\\n    brush.on(\\"change\\", () => {\\n        has_drawn = true;\\n    });\\n    crop_zoom = new ZoomTool();\\n    crop = new ImageEditor({\\n        target_element: pixi_target_crop,\\n        width: canvas_size[0],\\n        height: canvas_size[1],\\n        tools: [\\"image\\", crop_zoom, new CropTool()],\\n        dark: true,\\n        fixed_canvas: false,\\n        border_region: 0,\\n        pad_bottom: 40\\n    });\\n    editor.scale.subscribe((_scale) => {\\n        zoom_level = _scale;\\n    });\\n    editor.min_zoom.subscribe((is_min_zoom) => {\\n        min_zoom = is_min_zoom;\\n    });\\n    editor.dimensions.subscribe((dimensions) => {\\n        last_dimensions = { ...dimensions };\\n    });\\n    editor.command_manager.current_history.subscribe((history) => {\\n        can_undo = history.previous !== null;\\n        can_redo = history.next !== null;\\n    });\\n    await Promise.all([editor.ready, crop.ready]).then(() => {\\n        handle_tool_change({ tool: \\"image\\" });\\n        ready = true;\\n        if (sources.length > 0) {\\n            handle_tool_change({ tool: \\"image\\" });\\n        }\\n        else {\\n            handle_tool_change({ tool: \\"draw\\" });\\n        }\\n        crop.set_subtool(\\"crop\\");\\n    });\\n    editor.on(\\"change\\", () => {\\n        dispatch(\\"change\\");\\n        full_history = editor.command_manager.history;\\n    });\\n    if (background || layers.length > 0) {\\n        if (background) {\\n            await add_image_from_url(background);\\n        }\\n        if (layers.length > 0) {\\n            await add_layers_from_url(layers);\\n        }\\n        handle_tool_change({ tool: \\"draw\\" });\\n    }\\n    else if (composite) {\\n        await add_image_from_url(composite);\\n        handle_tool_change({ tool: \\"draw\\" });\\n    }\\n    refresh_tools();\\n}\\n$: if (background == null && layers.length == 0 && composite == null && editor && ready) {\\n    handle_tool_change({ tool: \\"image\\" });\\n    background_image = false;\\n    has_drawn = false;\\n}\\n$: current_tool === \\"image\\" && current_subtool === \\"crop\\" && crop_zoom.set_zoom(\\"fit\\");\\nasync function handle_files(files) {\\n    if (files == null)\\n        return;\\n    if (!sources.includes(\\"upload\\"))\\n        return;\\n    editor.reset_canvas();\\n    const _file = Array.isArray(files) ? files[0] : files;\\n    await editor.add_image({ image: _file });\\n    await crop.add_image({ image: _file });\\n    crop.reset();\\n    background_image = true;\\n    handle_tool_change({ tool: \\"draw\\" });\\n    dispatch(\\"upload\\");\\n    dispatch(\\"input\\");\\n    dispatch(\\"change\\");\\n}\\n$: background_image = can_undo && editor.command_manager.contains(\\"AddImage\\");\\nfunction handle_tool_change({ tool }) {\\n    editor.set_tool(tool);\\n    current_tool = tool;\\n    if (tool === \\"image\\") {\\n        crop.set_tool(\\"image\\");\\n        crop.set_subtool(\\"crop\\");\\n    }\\n}\\nfunction handle_subtool_change({ tool, subtool }) {\\n    editor.set_subtool(subtool);\\n    current_subtool = subtool;\\n    if (subtool === null) {\\n        return;\\n    }\\n    if (tool === \\"draw\\") {\\n        if (subtool === \\"size\\") {\\n            brush_size_visible = true;\\n        }\\n        else if (subtool === \\"color\\") {\\n            brush_color_visible = true;\\n        }\\n    }\\n    if (tool === \\"erase\\" && subtool === \\"size\\") {\\n        eraser_size_visible = true;\\n    }\\n    if (tool === \\"image\\" && subtool === \\"paste\\") {\\n        process_clipboard();\\n    }\\n    if (tool === \\"image\\" && subtool === \\"upload\\") {\\n        tick().then(() => {\\n            disable_click = false;\\n            open_file_upload();\\n        });\\n    }\\n}\\nlet eraser_size_visible = false;\\nlet selected_color;\\nlet selected_size;\\nlet selected_opacity = 1;\\nlet selected_eraser_size;\\n$: {\\n    if (brush_options) {\\n        update_brush_options();\\n    }\\n    if (eraser_options) {\\n        update_eraser_options();\\n    }\\n}\\nfunction update_brush_options() {\\n    const default_color = brush_options.default_color === \\"auto\\" ? brush_options.colors[0] : brush_options.default_color;\\n    if (Array.isArray(default_color)) {\\n        selected_color = default_color[0];\\n        selected_opacity = default_color[1];\\n    }\\n    else {\\n        selected_color = default_color;\\n        const color = tinycolor(default_color);\\n        if (color.getAlpha() < 1) {\\n            selected_opacity = color.getAlpha();\\n        }\\n        else {\\n            selected_opacity = 1;\\n        }\\n    }\\n    selected_size = typeof brush_options.default_size === \\"number\\" ? brush_options.default_size : 25;\\n}\\nfunction update_eraser_options() {\\n    selected_eraser_size = eraser_options.default_size === \\"auto\\" ? 25 : eraser_options.default_size;\\n}\\nlet brush_size_visible = false;\\nlet brush_color_visible = false;\\n$: brush?.set_brush_color((() => {\\n    let color_value;\\n    if (selected_color === \\"auto\\") {\\n        const default_color = brush_options.colors.find((color) => Array.isArray(color) ? color[0] === brush_options.default_color : color === brush_options.default_color) || brush_options.colors[0];\\n        color_value = Array.isArray(default_color) ? default_color[0] : default_color;\\n    }\\n    else {\\n        color_value = selected_color;\\n    }\\n    return color_value;\\n})());\\n$: brush?.set_brush_size(typeof selected_size === \\"number\\" ? selected_size : 25);\\n$: brush?.set_eraser_size(typeof selected_eraser_size === \\"number\\" ? selected_eraser_size : 25);\\n$: disable_click = current_tool !== \\"image\\" || current_tool === \\"image\\" && background_image || current_tool === \\"image\\" && current_subtool === \\"webcam\\" || !sources.includes(\\"upload\\");\\nlet current_subtool = null;\\nlet preview = false;\\n$: brush?.preview_brush(preview);\\n$: brush?.set_brush_opacity(selected_opacity);\\nfunction handle_zoom_change(zoom_level2) {\\n    zoom.set_zoom(zoom_level2);\\n}\\nfunction zoom_in_out(direction) {\\n    zoom.set_zoom(direction === \\"in\\" ? zoom_level + (zoom_level < 1 ? 0.1 : zoom_level * 0.1) : zoom_level - (zoom_level < 1 ? 0.1 : zoom_level * 0.1));\\n}\\nasync function process_clipboard() {\\n    const items = await navigator.clipboard.read();\\n    for (let i = 0; i < items.length; i++) {\\n        const type = items[i].types.find((t) => t.startsWith(\\"image/\\"));\\n        if (type) {\\n            const blob = await items[i].getType(type);\\n            handle_files(blob);\\n        }\\n    }\\n}\\nfunction handle_capture(e) {\\n    if (e.detail !== null) {\\n        handle_files(e.detail);\\n    }\\n    handle_subtool_change({ tool: current_tool, subtool: null });\\n}\\nfunction handle_save() {\\n    dispatch(\\"save\\");\\n}\\n$: add_image_from_url(composite || background);\\n$: add_layers_from_url(layers);\\nasync function handle_crop_confirm() {\\n    const { image } = await crop.get_crop_bounds();\\n    if (!image)\\n        return;\\n    await editor.add_image({\\n        image,\\n        resize: false\\n    });\\n    handle_subtool_change({ tool: \\"image\\", subtool: null });\\n    dispatch(\\"change\\");\\n    dispatch(\\"input\\");\\n}\\nasync function handle_download() {\\n    const blobs = await editor.get_blobs();\\n    const blob = blobs.composite;\\n    if (!blob) {\\n        dispatch(\\"download_error\\", \\"Unable to generate image to download.\\");\\n        return;\\n    }\\n    const url = URL.createObjectURL(blob);\\n    const link = document.createElement(\\"a\\");\\n    link.href = url;\\n    link.download = \\"image.png\\";\\n    link.click();\\n    URL.revokeObjectURL(url);\\n}\\nfunction handle_undo() {\\n    editor.undo();\\n}\\nfunction handle_redo() {\\n    editor.redo();\\n}\\n<\/script>\\n\\n<div\\n\\tdata-testid=\\"image\\"\\n\\tclass=\\"image-container\\"\\n\\tclass:dark-bg={current_subtool === \\"crop\\"}\\n\\tuse:drag={{\\n\\t\\ton_drag_change: (dragging) => (is_dragging = dragging),\\n\\t\\ton_files: handle_files,\\n\\t\\taccepted_types: \\"image/*\\",\\n\\t\\tdisable_click: disable_click\\n\\t}}\\n\\taria-label={\\"Click to upload or drop files\\"}\\n\\taria-dropeffect=\\"copy\\"\\n>\\n\\t{#if ready}\\n\\t\\t{#if current_subtool !== \\"crop\\"}\\n\\t\\t\\t<Controls\\n\\t\\t\\t\\t{changeable}\\n\\t\\t\\t\\ton:set_zoom={(e) => handle_zoom_change(e.detail)}\\n\\t\\t\\t\\ton:zoom_in={() => zoom_in_out(\\"in\\")}\\n\\t\\t\\t\\ton:zoom_out={() => zoom_in_out(\\"out\\")}\\n\\t\\t\\t\\t{min_zoom}\\n\\t\\t\\t\\tcurrent_zoom={zoom_level}\\n\\t\\t\\t\\ton:remove_image={() => {\\n\\t\\t\\t\\t\\tdispatch(\\"clear\\");\\n\\t\\t\\t\\t\\teditor.reset_canvas();\\n\\t\\t\\t\\t\\thandle_tool_change({ tool: \\"image\\" });\\n\\t\\t\\t\\t\\tbackground_image = false;\\n\\t\\t\\t\\t\\thas_drawn = false;\\n\\t\\t\\t\\t}}\\n\\t\\t\\t\\ttool={current_tool}\\n\\t\\t\\t\\tcan_save={true}\\n\\t\\t\\t\\ton:save={handle_save}\\n\\t\\t\\t\\ton:pan={(e) => {\\n\\t\\t\\t\\t\\thandle_tool_change({ tool: \\"pan\\" });\\n\\t\\t\\t\\t}}\\n\\t\\t\\t\\tenable_download={show_download_button}\\n\\t\\t\\t\\ton:download={() => handle_download()}\\n\\t\\t\\t\\t{can_undo}\\n\\t\\t\\t\\t{can_redo}\\n\\t\\t\\t\\ton:undo={handle_undo}\\n\\t\\t\\t\\ton:redo={handle_redo}\\n\\t\\t\\t/>\\n\\t\\t{/if}\\n\\n\\t\\t{#if current_subtool !== \\"crop\\"}\\n\\t\\t\\t<Toolbar\\n\\t\\t\\t\\t{sources}\\n\\t\\t\\t\\t{transforms}\\n\\t\\t\\t\\tbackground={background_image}\\n\\t\\t\\t\\ton:tool_change={(e) => handle_tool_change(e.detail)}\\n\\t\\t\\t\\ton:subtool_change={(e) => handle_subtool_change(e.detail)}\\n\\t\\t\\t\\tshow_brush_size={brush_size_visible}\\n\\t\\t\\t\\tshow_brush_color={brush_color_visible}\\n\\t\\t\\t\\tshow_eraser_size={eraser_size_visible}\\n\\t\\t\\t\\t{brush_options}\\n\\t\\t\\t\\t{eraser_options}\\n\\t\\t\\t\\tbind:selected_color\\n\\t\\t\\t\\tbind:selected_size\\n\\t\\t\\t\\tbind:selected_eraser_size\\n\\t\\t\\t\\tbind:selected_opacity\\n\\t\\t\\t\\tbind:preview\\n\\t\\t\\t\\ttool={current_tool}\\n\\t\\t\\t\\tsubtool={current_subtool}\\n\\t\\t\\t/>\\n\\t\\t{/if}\\n\\n\\t\\t{#if current_tool === \\"image\\" && current_subtool === \\"webcam\\"}\\n\\t\\t\\t<div class=\\"modal\\">\\n\\t\\t\\t\\t<div class=\\"modal-inner\\">\\n\\t\\t\\t\\t\\t<Webcam\\n\\t\\t\\t\\t\\t\\t{upload}\\n\\t\\t\\t\\t\\t\\t{root}\\n\\t\\t\\t\\t\\t\\ton:capture={handle_capture}\\n\\t\\t\\t\\t\\t\\ton:error\\n\\t\\t\\t\\t\\t\\ton:drag\\n\\t\\t\\t\\t\\t\\tstreaming={false}\\n\\t\\t\\t\\t\\t\\tmode=\\"image\\"\\n\\t\\t\\t\\t\\t\\tinclude_audio={false}\\n\\t\\t\\t\\t\\t\\t{i18n}\\n\\t\\t\\t\\t\\t\\tmirror_webcam={webcam_options.mirror}\\n\\t\\t\\t\\t\\t\\twebcam_constraints={webcam_options.constraints}\\n\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t</div>\\n\\t\\t{/if}\\n\\n\\t\\t{#if current_subtool !== \\"crop\\" && !layer_options.disabled}\\n\\t\\t\\t<SecondaryToolbar\\n\\t\\t\\t\\tenable_additional_layers={layer_options.allow_additional_layers}\\n\\t\\t\\t\\tlayers={editor.layers}\\n\\t\\t\\t\\ton:new_layer={() => {\\n\\t\\t\\t\\t\\teditor.add_layer();\\n\\t\\t\\t\\t}}\\n\\t\\t\\t\\ton:change_layer={(e) => {\\n\\t\\t\\t\\t\\teditor.set_layer(e.detail);\\n\\t\\t\\t\\t\\tif (current_tool === \\"draw\\") {\\n\\t\\t\\t\\t\\t\\thandle_tool_change({ tool: \\"draw\\" });\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t}}\\n\\t\\t\\t\\ton:move_layer={(e) => {\\n\\t\\t\\t\\t\\teditor.move_layer(e.detail.id, e.detail.direction);\\n\\t\\t\\t\\t}}\\n\\t\\t\\t\\ton:delete_layer={(e) => {\\n\\t\\t\\t\\t\\teditor.delete_layer(e.detail);\\n\\t\\t\\t\\t}}\\n\\t\\t\\t\\ton:toggle_layer_visibility={(e) => {\\n\\t\\t\\t\\t\\teditor.toggle_layer_visibility(e.detail);\\n\\t\\t\\t\\t}}\\n\\t\\t\\t/>\\n\\t\\t{/if}\\n\\t{/if}\\n\\t<div\\n\\t\\tclass=\\"pixi-target\\"\\n\\t\\tclass:visible={current_subtool !== \\"crop\\"}\\n\\t\\tbind:this={pixi_target}\\n\\t></div>\\n\\t<div\\n\\t\\tclass=\\"pixi-target-crop\\"\\n\\t\\tclass:visible={current_subtool === \\"crop\\"}\\n\\t\\tbind:this={pixi_target_crop}\\n\\t></div>\\n\\n\\t{#if current_subtool === \\"crop\\"}\\n\\t\\t<div class=\\"crop-confirm-button\\">\\n\\t\\t\\t<IconButton\\n\\t\\t\\t\\tIcon={Check}\\n\\t\\t\\t\\tlabel=\\"Confirm crop\\"\\n\\t\\t\\t\\tshow_label={true}\\n\\t\\t\\t\\tsize=\\"large\\"\\n\\t\\t\\t\\tpadded={true}\\n\\t\\t\\t\\tcolor=\\"white\\"\\n\\t\\t\\t\\tbackground=\\"var(--color-green-500)\\"\\n\\t\\t\\t\\tlabel_position=\\"right\\"\\n\\t\\t\\t\\ton:click={handle_crop_confirm}\\n\\t\\t\\t/>\\n\\t\\t</div>\\n\\t{/if}\\n\\t<slot></slot>\\n</div>\\n\\n<style>\\n\\t.image-container {\\n\\t\\tdisplay: flex;\\n\\t\\theight: 100%;\\n\\t\\tflex-direction: column;\\n\\t\\tjustify-content: center;\\n\\t\\talign-items: center;\\n\\t\\tmax-height: 100%;\\n\\t\\tborder-radius: var(--radius-sm);\\n\\t}\\n\\n\\t.pixi-target {\\n\\t\\twidth: 100%;\\n\\t\\theight: 100%;\\n\\t\\tposition: absolute;\\n\\t\\ttop: 0;\\n\\t\\tleft: 0;\\n\\t\\tz-index: 1;\\n\\t\\tdisplay: block;\\n\\t\\topacity: 0;\\n\\t\\tpointer-events: none;\\n\\t\\tborder-radius: var(--radius-sm);\\n\\t}\\n\\n\\t.pixi-target-crop {\\n\\t\\twidth: 100%;\\n\\t\\theight: 100%;\\n\\t\\tposition: absolute;\\n\\t\\ttop: 0;\\n\\t\\tleft: 0;\\n\\t\\tz-index: 2;\\n\\t\\tdisplay: block;\\n\\t\\topacity: 0;\\n\\t\\tpointer-events: none;\\n\\t\\tborder-radius: var(--radius-sm);\\n\\t}\\n\\n\\t.visible {\\n\\t\\topacity: 1;\\n\\t\\tpointer-events: auto;\\n\\t}\\n\\n\\t.pixi-target {\\n\\t\\twidth: 100%;\\n\\t\\theight: 100%;\\n\\t\\tposition: absolute;\\n\\t\\ttop: 0;\\n\\t\\tleft: 0;\\n\\t\\tright: 0;\\n\\t\\tbottom: 0;\\n\\t\\toverflow: hidden;\\n\\t}\\n\\n\\t.modal {\\n\\t\\tposition: absolute;\\n\\t\\theight: 100%;\\n\\t\\twidth: 100%;\\n\\t\\tleft: 0;\\n\\t\\tright: 0;\\n\\t\\tmargin: auto;\\n\\t\\tz-index: var(--layer-top);\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t}\\n\\n\\t.modal-inner {\\n\\t\\theight: 100%;\\n\\t\\twidth: 100%;\\n\\t\\tbackground: var(--block-background-fill);\\n\\t}\\n\\n\\t.dark-bg {\\n\\t\\tbackground: #333;\\n\\t}\\n\\n\\t.crop-confirm-button {\\n\\t\\tposition: absolute;\\n\\t\\tbottom: 8px;\\n\\t\\tleft: 0;\\n\\t\\tright: 0;\\n\\t\\tmargin: auto;\\n\\t\\tz-index: var(--layer-top);\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tjustify-content: center;\\n\\t}</style>\\n"],"names":[],"mappings":"AA+lBC,8BAAiB,CAChB,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,CACZ,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,UAAU,CAAE,IAAI,CAChB,aAAa,CAAE,IAAI,WAAW,CAC/B,CAEA,0BAAa,CACZ,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,CAAC,CACN,IAAI,CAAE,CAAC,CACP,OAAO,CAAE,CAAC,CACV,OAAO,CAAE,KAAK,CACd,OAAO,CAAE,CAAC,CACV,cAAc,CAAE,IAAI,CACpB,aAAa,CAAE,IAAI,WAAW,CAC/B,CAEA,+BAAkB,CACjB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,CAAC,CACN,IAAI,CAAE,CAAC,CACP,OAAO,CAAE,CAAC,CACV,OAAO,CAAE,KAAK,CACd,OAAO,CAAE,CAAC,CACV,cAAc,CAAE,IAAI,CACpB,aAAa,CAAE,IAAI,WAAW,CAC/B,CAEA,sBAAS,CACR,OAAO,CAAE,CAAC,CACV,cAAc,CAAE,IACjB,CAEA,0BAAa,CACZ,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,CAAC,CACN,IAAI,CAAE,CAAC,CACP,KAAK,CAAE,CAAC,CACR,MAAM,CAAE,CAAC,CACT,QAAQ,CAAE,MACX,CAEA,oBAAO,CACN,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IAAI,CACX,IAAI,CAAE,CAAC,CACP,KAAK,CAAE,CAAC,CACR,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,IAAI,WAAW,CAAC,CACzB,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MACd,CAEA,0BAAa,CACZ,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,IAAI,uBAAuB,CACxC,CAEA,sBAAS,CACR,UAAU,CAAE,IACb,CAEA,kCAAqB,CACpB,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,GAAG,CACX,IAAI,CAAE,CAAC,CACP,KAAK,CAAE,CAAC,CACR,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,IAAI,WAAW,CAAC,CACzB,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAClB"}'},l_=create_ssr_component((s$1,t,e,i)=>{createEventDispatcher();const r=!0;let{changeable:o=!1}=t,{sources:a=["upload","webcam","clipboard"]}=t,{transforms:l=["crop","resize"]}=t,{canvas_size:c}=t,{is_dragging:h=!1}=t,{background_image:d=!1}=t,{brush_options:_}=t,{eraser_options:u}=t,{fixed_canvas:f=!1}=t,{root:p}=t,{i18n:A}=t,{upload:m}=t,{composite:g}=t,{layers:y}=t,{background:x}=t,{border_region:v=0}=t,{layer_options:b}=t,{current_tool:C}=t,{webcam_options:M}=t,{show_download_button:B=!1}=t,{theme_mode:w}=t,{full_history:k=null}=t,S,U;function V(){}function z(){return b&&I&&F}let{has_drawn:R=!1}=t;async function Rt(){return {background:null,layers:[],composite:null}}let I;function at(E){I.add_image({image:E});}async function q(E){return;}async function ct(E){}let F=!1;let $,ft,{can_undo:Y=!1}=t;function X({tool:E}){I.set_tool(E),C=E,E==="image"&&($.set_tool("image"),$.set_subtool("crop"));}function Zo(){const E=_.default_color==="auto"?_.colors[0]:_.default_color;if(Array.isArray(E))E[0],E[1];else {const tt=s(E);tt.getAlpha()<1?tt.getAlpha():1;}typeof _.default_size=="number"?_.default_size:25;}function Jo(){u.default_size==="auto"?25:u.default_size;}let $t=null;t.antialias===void 0&&e.antialias&&r!==void 0&&e.antialias(r),t.changeable===void 0&&e.changeable&&o!==void 0&&e.changeable(o),t.sources===void 0&&e.sources&&a!==void 0&&e.sources(a),t.transforms===void 0&&e.transforms&&l!==void 0&&e.transforms(l),t.canvas_size===void 0&&e.canvas_size&&c!==void 0&&e.canvas_size(c),t.is_dragging===void 0&&e.is_dragging&&h!==void 0&&e.is_dragging(h),t.background_image===void 0&&e.background_image&&d!==void 0&&e.background_image(d),t.brush_options===void 0&&e.brush_options&&_!==void 0&&e.brush_options(_),t.eraser_options===void 0&&e.eraser_options&&u!==void 0&&e.eraser_options(u),t.fixed_canvas===void 0&&e.fixed_canvas&&f!==void 0&&e.fixed_canvas(f),t.root===void 0&&e.root&&p!==void 0&&e.root(p),t.i18n===void 0&&e.i18n&&A!==void 0&&e.i18n(A),t.upload===void 0&&e.upload&&m!==void 0&&e.upload(m),t.composite===void 0&&e.composite&&g!==void 0&&e.composite(g),t.layers===void 0&&e.layers&&y!==void 0&&e.layers(y),t.background===void 0&&e.background&&x!==void 0&&e.background(x),t.border_region===void 0&&e.border_region&&v!==void 0&&e.border_region(v),t.layer_options===void 0&&e.layer_options&&b!==void 0&&e.layer_options(b),t.current_tool===void 0&&e.current_tool&&C!==void 0&&e.current_tool(C),t.webcam_options===void 0&&e.webcam_options&&M!==void 0&&e.webcam_options(M),t.show_download_button===void 0&&e.show_download_button&&B!==void 0&&e.show_download_button(B),t.theme_mode===void 0&&e.theme_mode&&w!==void 0&&e.theme_mode(w),t.full_history===void 0&&e.full_history&&k!==void 0&&e.full_history(k),t.has_drawn===void 0&&e.has_drawn&&R!==void 0&&e.has_drawn(R),t.get_blobs===void 0&&e.get_blobs&&Rt!==void 0&&e.get_blobs(Rt),t.add_image===void 0&&e.add_image&&at!==void 0&&e.add_image(at),t.add_image_from_url===void 0&&e.add_image_from_url&&q!==void 0&&e.add_image_from_url(q),t.add_layers_from_url===void 0&&e.add_layers_from_url&&ct!==void 0&&e.add_layers_from_url(ct),t.can_undo===void 0&&e.can_undo&&Y!==void 0&&e.can_undo(Y),s$1.css.add(a_);let te,nn,ea=s$1.head;do{if(te=!0,s$1.head=ea,b&&z()&&(I.set_layer_options(b),V()),I);x==null&&y.length==0&&g==null&&I&&F&&(X({tool:"image"}),d=!1,R=!1),C==="image"&&$t==="crop"&&ft.set_zoom("fit"),d=Y&&I.command_manager.contains("AddImage"),_&&Zo(),u&&Jo(),C!=="image"||C==="image"&&d||C==="image"&&$t==="webcam"||a.includes("upload"),q(),ct(),nn=`<div data-testid="image" class="${["image-container svelte-h6gdep",""].join(" ").trim()}"${add_attribute("aria-label","Click to upload or drop files",0)} aria-dropeffect="copy">${""} <div class="${["pixi-target svelte-h6gdep","visible"].join(" ").trim()}"${add_attribute("this",S,0)}></div> <div class="${["pixi-target-crop svelte-h6gdep",""].join(" ").trim()}"${add_attribute("this",U,0)}></div>  ${i.default?i.default({}):""} </div>`;}while(!te);return nn}),c_=/^(#\s*)(.+)$/m;function h_(s){const t=s.trim(),e=t.match(c_);if(!e)return [!1,t||!1];const[i,,n]=e,r=n.trim();if(t===i)return [r,!1];const o=e.index!==void 0?e.index+i.length:0,l=t.substring(o).trim()||!1;return [r,l]}const d_={code:"h2.svelte-a0rsm5{font-size:var(--text-xl)}p.svelte-a0rsm5,h2.svelte-a0rsm5{white-space:pre-line}.empty.svelte-a0rsm5{display:flex;flex-direction:column;justify-content:center;align-items:center;position:absolute;height:100%;width:100%;left:0;right:0;margin:auto;z-index:var(--layer-1);text-align:center;color:var(--color-grey-500) !important;cursor:pointer}.wrap.svelte-a0rsm5{display:flex;flex-direction:column;justify-content:center;align-items:center;line-height:var(--line-md);font-size:var(--text-md)}.or.svelte-a0rsm5{color:var(--body-text-color-subdued)}",map:'{"version":3,"file":"InteractiveImageEditor.svelte","sources":["InteractiveImageEditor.svelte"],"sourcesContent":["<script lang=\\"ts\\" context=\\"module\\">\\"use strict\\";\\n<\/script>\\n\\n<script lang=\\"ts\\">import { createEventDispatcher } from \\"svelte\\";\\nimport {} from \\"@gradio/utils\\";\\nimport { prepare_files } from \\"@gradio/client\\";\\nimport {} from \\"./shared/core/commands\\";\\nimport ImageEditor from \\"./shared/ImageEditor.svelte\\";\\nimport {} from \\"./shared/brush/types\\";\\nimport {} from \\"./shared/Toolbar.svelte\\";\\nimport { BlockLabel } from \\"@gradio/atoms\\";\\nimport { Image as ImageIcon } from \\"@gradio/icons\\";\\nimport { inject } from \\"./shared/utils/parse_placeholder\\";\\nimport {} from \\"./shared/types\\";\\nexport let brush;\\nexport let eraser;\\nexport let sources;\\nexport let i18n;\\nexport let root;\\nexport let label = void 0;\\nexport let show_label;\\nexport let changeable = false;\\nexport let theme_mode;\\nexport let layers;\\nexport let composite;\\nexport let background;\\nexport let layer_options;\\nexport let transforms;\\nexport let accept_blobs;\\nexport let canvas_size;\\nexport let fixed_canvas = false;\\nexport let realtime;\\nexport let upload;\\nexport let is_dragging;\\nexport let placeholder = void 0;\\nexport let border_region;\\nexport let full_history = null;\\nexport let webcam_options;\\nexport let show_download_button = false;\\nconst dispatch = createEventDispatcher();\\nlet editor;\\nlet has_drawn = false;\\nfunction is_not_null(o) {\\n    return !!o;\\n}\\nfunction is_file_data(o) {\\n    return !!o;\\n}\\n$: if (background_image)\\n    dispatch(\\"upload\\");\\nexport async function get_data() {\\n    let blobs;\\n    try {\\n        blobs = await editor.get_blobs();\\n    }\\n    catch (e) {\\n        return { background: null, layers: [], composite: null };\\n    }\\n    const bg = blobs.background ? upload(await prepare_files([new File([blobs.background], \\"background.png\\")]), root) : Promise.resolve(null);\\n    const layers2 = blobs.layers.filter(is_not_null).map(async (blob, i) => upload(await prepare_files([new File([blob], `layer_${i}.png`)]), root));\\n    const composite2 = blobs.composite ? upload(await prepare_files([new File([blobs.composite], \\"composite.png\\")]), root) : Promise.resolve(null);\\n    const [background2, composite_, ...layers_] = await Promise.all([\\n        bg,\\n        composite2,\\n        ...layers2\\n    ]);\\n    return {\\n        background: Array.isArray(background2) ? background2[0] : background2,\\n        layers: layers_.flatMap((layer) => Array.isArray(layer) ? layer : [layer]).filter(is_file_data),\\n        composite: Array.isArray(composite_) ? composite_[0] : composite_\\n    };\\n}\\nfunction handle_value(value) {\\n    if (!editor)\\n        return;\\n    if (value == null) {\\n        editor.handle_remove();\\n        dispatch(\\"receive_null\\");\\n    }\\n}\\n$: handle_value({ layers, composite, background });\\nlet background_image = false;\\nlet can_undo;\\nexport let image_id = null;\\nfunction nextframe() {\\n    return new Promise((resolve) => setTimeout(() => resolve(), 30));\\n}\\nlet uploading = false;\\nlet pending = false;\\nasync function handle_change(e) {\\n    if (!realtime)\\n        return;\\n    if (uploading) {\\n        pending = true;\\n        return;\\n    }\\n    uploading = true;\\n    await nextframe();\\n    const blobs = await editor.get_blobs();\\n    const images = [];\\n    let id = Math.random().toString(36).substring(2);\\n    if (blobs.background)\\n        images.push([\\n            id,\\n            \\"background\\",\\n            new File([blobs.background], \\"background.png\\"),\\n            null\\n        ]);\\n    if (blobs.composite)\\n        images.push([\\n            id,\\n            \\"composite\\",\\n            new File([blobs.composite], \\"composite.png\\"),\\n            null\\n        ]);\\n    blobs.layers.forEach((layer, i) => {\\n        if (layer)\\n            images.push([\\n                id,\\n                `layer`,\\n                new File([layer], `layer_${i}.png`),\\n                i\\n            ]);\\n    });\\n    await Promise.all(images.map(async ([image_id2, type, data, index]) => {\\n        return accept_blobs({\\n            binary: true,\\n            data: { file: data, id: image_id2, type, index }\\n        });\\n    }));\\n    image_id = id;\\n    dispatch(\\"change\\");\\n    await nextframe();\\n    uploading = false;\\n    if (pending) {\\n        pending = false;\\n        uploading = false;\\n        handle_change(e);\\n    }\\n}\\n$: [heading, paragraph] = placeholder ? inject(placeholder) : [false, false];\\nlet current_tool;\\n<\/script>\\n\\n<BlockLabel\\n\\t{show_label}\\n\\tIcon={ImageIcon}\\n\\tlabel={label || i18n(\\"image.image\\")}\\n/>\\n<ImageEditor\\n\\t{transforms}\\n\\t{composite}\\n\\t{layers}\\n\\t{background}\\n\\ton:history\\n\\t{canvas_size}\\n\\tbind:this={editor}\\n\\t{changeable}\\n\\ton:save\\n\\ton:change={handle_change}\\n\\ton:clear={() => dispatch(\\"clear\\")}\\n\\ton:download_error\\n\\t{sources}\\n\\tbind:background_image\\n\\tbind:current_tool\\n\\tbrush_options={brush}\\n\\teraser_options={eraser}\\n\\t{fixed_canvas}\\n\\t{border_region}\\n\\t{layer_options}\\n\\t{i18n}\\n\\t{root}\\n\\t{upload}\\n\\tbind:is_dragging\\n\\tbind:has_drawn\\n\\t{webcam_options}\\n\\t{show_download_button}\\n\\t{theme_mode}\\n\\tbind:can_undo\\n\\tbind:full_history\\n>\\n\\t{#if current_tool === \\"image\\" && !can_undo}\\n\\t\\t<div class=\\"empty wrap\\">\\n\\t\\t\\t{#if sources && sources.length}\\n\\t\\t\\t\\t{#if heading || paragraph}\\n\\t\\t\\t\\t\\t{#if heading}\\n\\t\\t\\t\\t\\t\\t<h2>{heading}</h2>\\n\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t{#if paragraph}\\n\\t\\t\\t\\t\\t\\t<p>{paragraph}</p>\\n\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t<div>Upload an image</div>\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t{/if}\\n\\n\\t\\t\\t{#if sources && sources.length && brush && !placeholder}\\n\\t\\t\\t\\t<div class=\\"or\\">or</div>\\n\\t\\t\\t{/if}\\n\\t\\t\\t{#if brush && !placeholder}\\n\\t\\t\\t\\t<div>select the draw tool to start</div>\\n\\t\\t\\t{/if}\\n\\t\\t</div>\\n\\t{/if}\\n</ImageEditor>\\n\\n<style>\\n\\th2 {\\n\\t\\tfont-size: var(--text-xl);\\n\\t}\\n\\n\\tp,\\n\\th2 {\\n\\t\\twhite-space: pre-line;\\n\\t}\\n\\n\\t.empty {\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: column;\\n\\t\\tjustify-content: center;\\n\\t\\talign-items: center;\\n\\t\\tposition: absolute;\\n\\t\\theight: 100%;\\n\\t\\twidth: 100%;\\n\\t\\tleft: 0;\\n\\t\\tright: 0;\\n\\t\\tmargin: auto;\\n\\t\\tz-index: var(--layer-1);\\n\\t\\ttext-align: center;\\n\\t\\tcolor: var(--color-grey-500) !important;\\n\\t\\tcursor: pointer;\\n\\t}\\n\\n\\t.wrap {\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: column;\\n\\t\\tjustify-content: center;\\n\\t\\talign-items: center;\\n\\t\\tline-height: var(--line-md);\\n\\t\\tfont-size: var(--text-md);\\n\\t}\\n\\n\\t.or {\\n\\t\\tcolor: var(--body-text-color-subdued);\\n\\t}</style>\\n"],"names":[],"mappings":"AA+MC,gBAAG,CACF,SAAS,CAAE,IAAI,SAAS,CACzB,CAEA,eAAC,CACD,gBAAG,CACF,WAAW,CAAE,QACd,CAEA,oBAAO,CACN,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IAAI,CACX,IAAI,CAAE,CAAC,CACP,KAAK,CAAE,CAAC,CACR,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,IAAI,SAAS,CAAC,CACvB,UAAU,CAAE,MAAM,CAClB,KAAK,CAAE,IAAI,gBAAgB,CAAC,CAAC,UAAU,CACvC,MAAM,CAAE,OACT,CAEA,mBAAM,CACL,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,WAAW,CAAE,IAAI,SAAS,CAAC,CAC3B,SAAS,CAAE,IAAI,SAAS,CACzB,CAEA,iBAAI,CACH,KAAK,CAAE,IAAI,yBAAyB,CACrC"}'};function u_(s){return !!s}function __(s){return !!s}const f_=create_ssr_component((s,t,e,i)=>{let n,r,{brush:o}=t,{eraser:a}=t,{sources:l}=t,{i18n:c}=t,{root:h}=t,{label:d=void 0}=t,{show_label:_}=t,{changeable:u=!1}=t,{theme_mode:f}=t,{layers:p}=t,{composite:A}=t,{background:m}=t,{layer_options:g}=t,{transforms:y}=t,{accept_blobs:x}=t,{canvas_size:v}=t,{fixed_canvas:b=!1}=t,{realtime:C}=t,{upload:M}=t,{is_dragging:B}=t,{placeholder:w=void 0}=t,{border_region:k}=t,{full_history:S=null}=t,{webcam_options:U}=t,{show_download_button:V=!1}=t;const xt=createEventDispatcher();let z,R=!1;async function Rt(){let F;try{F=await z.get_blobs();}catch{return {background:null,layers:[],composite:null}}const Dt=F.background?M(await la([new File([F.background],"background.png")]),h):Promise.resolve(null),_t=F.layers.filter(u_).map(async(Y,At)=>M(await la([new File([Y],`layer_${At}.png`)]),h)),Kt=F.composite?M(await la([new File([F.composite],"composite.png")]),h):Promise.resolve(null),[Bt,$,...ft]=await Promise.all([Dt,Kt,..._t]);return {background:Array.isArray(Bt)?Bt[0]:Bt,layers:ft.flatMap(Y=>Array.isArray(Y)?Y:[Y]).filter(__),composite:Array.isArray($)?$[0]:$}}function I(F){z&&F==null&&(z.handle_remove(),xt("receive_null"));}let at=!1,lt,{image_id:q=null}=t,ct;t.brush===void 0&&e.brush&&o!==void 0&&e.brush(o),t.eraser===void 0&&e.eraser&&a!==void 0&&e.eraser(a),t.sources===void 0&&e.sources&&l!==void 0&&e.sources(l),t.i18n===void 0&&e.i18n&&c!==void 0&&e.i18n(c),t.root===void 0&&e.root&&h!==void 0&&e.root(h),t.label===void 0&&e.label&&d!==void 0&&e.label(d),t.show_label===void 0&&e.show_label&&_!==void 0&&e.show_label(_),t.changeable===void 0&&e.changeable&&u!==void 0&&e.changeable(u),t.theme_mode===void 0&&e.theme_mode&&f!==void 0&&e.theme_mode(f),t.layers===void 0&&e.layers&&p!==void 0&&e.layers(p),t.composite===void 0&&e.composite&&A!==void 0&&e.composite(A),t.background===void 0&&e.background&&m!==void 0&&e.background(m),t.layer_options===void 0&&e.layer_options&&g!==void 0&&e.layer_options(g),t.transforms===void 0&&e.transforms&&y!==void 0&&e.transforms(y),t.accept_blobs===void 0&&e.accept_blobs&&x!==void 0&&e.accept_blobs(x),t.canvas_size===void 0&&e.canvas_size&&v!==void 0&&e.canvas_size(v),t.fixed_canvas===void 0&&e.fixed_canvas&&b!==void 0&&e.fixed_canvas(b),t.realtime===void 0&&e.realtime&&C!==void 0&&e.realtime(C),t.upload===void 0&&e.upload&&M!==void 0&&e.upload(M),t.is_dragging===void 0&&e.is_dragging&&B!==void 0&&e.is_dragging(B),t.placeholder===void 0&&e.placeholder&&w!==void 0&&e.placeholder(w),t.border_region===void 0&&e.border_region&&k!==void 0&&e.border_region(k),t.full_history===void 0&&e.full_history&&S!==void 0&&e.full_history(S),t.webcam_options===void 0&&e.webcam_options&&U!==void 0&&e.webcam_options(U),t.show_download_button===void 0&&e.show_download_button&&V!==void 0&&e.show_download_button(V),t.get_data===void 0&&e.get_data&&Rt!==void 0&&e.get_data(Rt),t.image_id===void 0&&e.image_id&&q!==void 0&&e.image_id(q),s.css.add(d_);let j,ut,bt$1=s.head;do j=!0,s.head=bt$1,at&&xt("upload"),I({layers:p,composite:A,background:m}),[n,r]=w?h_(w):[!1,!1],ut=`${validate_component(bt,"BlockLabel").$$render(s,{show_label:_,Icon:Xt$1,label:d||c("image.image")},{},{})} ${validate_component(l_,"ImageEditor").$$render(s,{transforms:y,composite:A,layers:p,background:m,canvas_size:v,changeable:u,sources:l,brush_options:o,eraser_options:a,fixed_canvas:b,border_region:k,layer_options:g,i18n:c,root:h,upload:M,webcam_options:U,show_download_button:V,theme_mode:f,this:z,background_image:at,current_tool:ct,is_dragging:B,has_drawn:R,can_undo:lt,full_history:S},{this:F=>{z=F,j=!1;},background_image:F=>{at=F,j=!1;},current_tool:F=>{ct=F,j=!1;},is_dragging:F=>{B=F,j=!1;},has_drawn:F=>{R=F,j=!1;},can_undo:F=>{lt=F,j=!1;},full_history:F=>{S=F,j=!1;}},{default:()=>`${ct==="image"&&!lt?`<div class="empty wrap svelte-a0rsm5">${l&&l.length?`${n||r?`${n?`<h2 class="svelte-a0rsm5">${escape(n)}</h2>`:""} ${r?`<p class="svelte-a0rsm5">${escape(r)}</p>`:""}`:'<div data-svelte-h="svelte-1av7pez">Upload an image</div>'}`:""} ${l&&l.length&&o&&!w?'<div class="or svelte-a0rsm5" data-svelte-h="svelte-tbjmnz">or</div>':""} ${o&&!w?'<div data-svelte-h="svelte-jwb70i">select the draw tool to start</div>':""}</div>`:""}`})}`;while(!j);return ut}),A_=create_ssr_component((s,t,e,i)=>{let n,r,o,a,{elem_id:l=""}=t,{elem_classes:c=[]}=t,{visible:h=!0}=t,{value:d={background:null,layers:[],composite:null}}=t,{label:_}=t,{show_label:u}=t,{show_download_button:f}=t,{root:p}=t,{value_is_output:A=!1}=t,{height:m=350}=t,{width:g}=t,{_selectable:y=!1}=t,{container:x=!0}=t,{scale:v=null}=t,{min_width:b=void 0}=t,{loading_status:C}=t,{show_share_button:M=!1}=t,{sources:B=[]}=t,{interactive:w}=t,{placeholder:k}=t,{brush:S}=t,{eraser:U}=t,{transforms:V=[]}=t,{layers:xt}=t,{attached_events:z=[]}=t,{server:R}=t,{canvas_size:Rt}=t,{fixed_canvas:I=!1}=t,{show_fullscreen_button:at=!0}=t,{full_history:lt=null}=t,{gradio:q}=t,{border_region:ct=0}=t,{webcam_options:j}=t,{theme_mode:ut}=t,bt,F=null;async function Dt(){if(F){const Se={id:F};return F=null,Se}return await bt.get_data()}let _t;const Bt=typeof window<"u"?window.requestAnimationFrame:X=>X();function $(){return new Promise(X=>{Bt(()=>Bt(()=>X()));})}async function ft(){await $(),d&&(d.background||d.layers?.length||d.composite)&&q.dispatch("change");}t.elem_id===void 0&&e.elem_id&&l!==void 0&&e.elem_id(l),t.elem_classes===void 0&&e.elem_classes&&c!==void 0&&e.elem_classes(c),t.visible===void 0&&e.visible&&h!==void 0&&e.visible(h),t.value===void 0&&e.value&&d!==void 0&&e.value(d),t.label===void 0&&e.label&&_!==void 0&&e.label(_),t.show_label===void 0&&e.show_label&&u!==void 0&&e.show_label(u),t.show_download_button===void 0&&e.show_download_button&&f!==void 0&&e.show_download_button(f),t.root===void 0&&e.root&&p!==void 0&&e.root(p),t.value_is_output===void 0&&e.value_is_output&&A!==void 0&&e.value_is_output(A),t.height===void 0&&e.height&&m!==void 0&&e.height(m),t.width===void 0&&e.width&&g!==void 0&&e.width(g),t._selectable===void 0&&e._selectable&&y!==void 0&&e._selectable(y),t.container===void 0&&e.container&&x!==void 0&&e.container(x),t.scale===void 0&&e.scale&&v!==void 0&&e.scale(v),t.min_width===void 0&&e.min_width&&b!==void 0&&e.min_width(b),t.loading_status===void 0&&e.loading_status&&C!==void 0&&e.loading_status(C),t.show_share_button===void 0&&e.show_share_button&&M!==void 0&&e.show_share_button(M),t.sources===void 0&&e.sources&&B!==void 0&&e.sources(B),t.interactive===void 0&&e.interactive&&w!==void 0&&e.interactive(w),t.placeholder===void 0&&e.placeholder&&k!==void 0&&e.placeholder(k),t.brush===void 0&&e.brush&&S!==void 0&&e.brush(S),t.eraser===void 0&&e.eraser&&U!==void 0&&e.eraser(U),t.transforms===void 0&&e.transforms&&V!==void 0&&e.transforms(V),t.layers===void 0&&e.layers&&xt!==void 0&&e.layers(xt),t.attached_events===void 0&&e.attached_events&&z!==void 0&&e.attached_events(z),t.server===void 0&&e.server&&R!==void 0&&e.server(R),t.canvas_size===void 0&&e.canvas_size&&Rt!==void 0&&e.canvas_size(Rt),t.fixed_canvas===void 0&&e.fixed_canvas&&I!==void 0&&e.fixed_canvas(I),t.show_fullscreen_button===void 0&&e.show_fullscreen_button&&at!==void 0&&e.show_fullscreen_button(at),t.full_history===void 0&&e.full_history&&lt!==void 0&&e.full_history(lt),t.gradio===void 0&&e.gradio&&q!==void 0&&e.gradio(q),t.border_region===void 0&&e.border_region&&ct!==void 0&&e.border_region(ct),t.webcam_options===void 0&&e.webcam_options&&j!==void 0&&e.webcam_options(j),t.theme_mode===void 0&&e.theme_mode&&ut!==void 0&&e.theme_mode(ut),t.get_value===void 0&&e.get_value&&Dt!==void 0&&e.get_value(Dt);let Y,At,Me=s.head;do Y=!0,s.head=Me,d&&ft(),n=d?.background||d?.layers?.length||d?.composite,r=d?.background?new Ke$1(d.background):null,o=d?.composite?new Ke$1(d.composite):null,a=d?.layers?.map(X=>new Ke$1(X))||[],At=`  ${w?`${validate_component(mt,"Block").$$render(s,{visible:h,variant:n?"solid":"dashed",border_mode:_t?"focus":"base",padding:!1,elem_id:l,elem_classes:c,height:m,width:g,allow_overflow:!0,overflow_behavior:"visible",container:x,scale:v,min_width:b},{},{default:()=>`${validate_component(zA,"StatusTracker").$$render(s,Object.assign({},{autoscroll:q.autoscroll},{i18n:q.i18n},C),{},{})} ${validate_component(f_,"InteractiveImageEditor").$$render(s,{border_region:ct,canvas_size:Rt,layers:a,composite:o,background:r,root:p,sources:B,label:_,show_label:u,fixed_canvas:I,brush:S,eraser:U,changeable:z.includes("apply"),realtime:z.includes("change")||z.includes("input"),i18n:q.i18n,transforms:V,accept_blobs:R.accept_blobs,layer_options:xt,upload:(...X)=>q.client.upload(...X),placeholder:k,webcam_options:j,show_download_button:f,theme_mode:ut,is_dragging:_t,image_id:F,this:bt,full_history:lt},{is_dragging:X=>{_t=X,Y=!1;},image_id:X=>{F=X,Y=!1;},this:X=>{bt=X,Y=!1;},full_history:X=>{lt=X,Y=!1;}},{})}`})}`:`${validate_component(mt,"Block").$$render(s,{visible:h,variant:"solid",border_mode:_t?"focus":"base",padding:!1,elem_id:l,elem_classes:c,height:m,width:g,allow_overflow:!0,overflow_behavior:"visible",container:x,scale:v,min_width:b},{},{default:()=>`${validate_component(zA,"StatusTracker").$$render(s,Object.assign({},{autoscroll:q.autoscroll},{i18n:q.i18n},C),{},{})} ${validate_component(O$1,"StaticImage").$$render(s,{value:d?.composite||null,label:_,show_label:u,show_download_button:f,selectable:y,show_share_button:M,i18n:q.i18n,show_fullscreen_button:at},{},{})}`})}`}`;while(!Y);return At}),M_=Object.freeze(Object.defineProperty({__proto__:null,default:A_},Symbol.toStringTag,{value:"Module"}));

export { st as $, uo as A, vt as B, O as C, it as D, T as E, vs as F, Qt as G, Sr as H, Ke as I, Ft as J, bn as K, H as L, K as M, Yl as N, sc as O, L as P, rt as Q, xs as R, ec as S, ti as T, ps as U, Q as V, bc as W, vn as X, kn as Y, kl as Z, Ui as _, Lt as a, Pr as a0, pt as a1, _o as a2, D as a3, N as a4, Ic as a5, Xc as a6, uh as a7, fh as a8, yh as a9, bh as aa, Ch as ab, qe as ac, dd as ad, So as ae, Gn as af, Tn as ag, ja as ah, or as ai, md as aj, nt as ak, et as al, Xa as am, sr as an, Rs as ao, nr as ap, gi as aq, Po as ar, M_ as as, Dc as b, Xe as c, _i as d, yt as e, Rn as f, Lc as g, Ys as h, lo as i, Nr as j, Wt as k, io as l, ir as m, dh as n, _h as o, mh as p, yo as q, ol as r, xh as s, Ci as t, W as u, Ya as v, ot as w, Ce as x, wh as y, Yt as z };
//# sourceMappingURL=Index3-DXC-nXht.js.map
