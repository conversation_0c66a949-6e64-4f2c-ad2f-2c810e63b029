import"./index-B7J2Z2jS.js";import{I as A}from"./Image-CnqB5dbD.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";/* empty css                                                   */const{SvelteComponent:Q,append:D,attr:d,bubble:R,check_outros:w,create_component:E,create_slot:F,destroy_component:G,detach:v,element:H,empty:T,flush:b,get_all_dirty_from_scope:J,get_slot_changes:K,group_outros:z,init:U,insert:q,listen:V,mount_component:L,safe_not_equal:W,set_style:c,space:M,toggle_class:k,transition_in:h,transition_out:g,update_slot_base:O}=window.__gradio__svelte__internal;function X(i){let e,n,s,t,u,_,a=i[7]&&N(i);const l=i[12].default,f=F(l,i,i[11],null);return{c(){e=H("button"),a&&a.c(),n=M(),f&&f.c(),d(e,"class",s=i[4]+" "+i[3]+" "+i[1].join(" ")+" svelte-1ixn6qd"),d(e,"id",i[0]),e.disabled=i[8],k(e,"hidden",!i[2]),c(e,"flex-grow",i[9]),c(e,"width",i[9]===0?"fit-content":null),c(e,"min-width",typeof i[10]=="number"?`calc(min(${i[10]}px, 100%))`:null)},m(o,m){q(o,e,m),a&&a.m(e,null),D(e,n),f&&f.m(e,null),t=!0,u||(_=V(e,"click",i[13]),u=!0)},p(o,m){o[7]?a?(a.p(o,m),m&128&&h(a,1)):(a=N(o),a.c(),h(a,1),a.m(e,n)):a&&(z(),g(a,1,1,()=>{a=null}),w()),f&&f.p&&(!t||m&2048)&&O(f,l,o,o[11],t?K(l,o[11],m,null):J(o[11]),null),(!t||m&26&&s!==(s=o[4]+" "+o[3]+" "+o[1].join(" ")+" svelte-1ixn6qd"))&&d(e,"class",s),(!t||m&1)&&d(e,"id",o[0]),(!t||m&256)&&(e.disabled=o[8]),(!t||m&30)&&k(e,"hidden",!o[2]),m&512&&c(e,"flex-grow",o[9]),m&512&&c(e,"width",o[9]===0?"fit-content":null),m&1024&&c(e,"min-width",typeof o[10]=="number"?`calc(min(${o[10]}px, 100%))`:null)},i(o){t||(h(a),h(f,o),t=!0)},o(o){g(a),g(f,o),t=!1},d(o){o&&v(e),a&&a.d(),f&&f.d(o),u=!1,_()}}}function Y(i){let e,n,s,t,u=i[7]&&S(i);const _=i[12].default,a=F(_,i,i[11],null);return{c(){e=H("a"),u&&u.c(),n=M(),a&&a.c(),d(e,"href",i[6]),d(e,"rel","noopener noreferrer"),d(e,"aria-disabled",i[8]),d(e,"class",s=i[4]+" "+i[3]+" "+i[1].join(" ")+" svelte-1ixn6qd"),d(e,"id",i[0]),k(e,"hidden",!i[2]),k(e,"disabled",i[8]),c(e,"flex-grow",i[9]),c(e,"pointer-events",i[8]?"none":null),c(e,"width",i[9]===0?"fit-content":null),c(e,"min-width",typeof i[10]=="number"?`calc(min(${i[10]}px, 100%))`:null)},m(l,f){q(l,e,f),u&&u.m(e,null),D(e,n),a&&a.m(e,null),t=!0},p(l,f){l[7]?u?(u.p(l,f),f&128&&h(u,1)):(u=S(l),u.c(),h(u,1),u.m(e,n)):u&&(z(),g(u,1,1,()=>{u=null}),w()),a&&a.p&&(!t||f&2048)&&O(a,_,l,l[11],t?K(_,l[11],f,null):J(l[11]),null),(!t||f&64)&&d(e,"href",l[6]),(!t||f&256)&&d(e,"aria-disabled",l[8]),(!t||f&26&&s!==(s=l[4]+" "+l[3]+" "+l[1].join(" ")+" svelte-1ixn6qd"))&&d(e,"class",s),(!t||f&1)&&d(e,"id",l[0]),(!t||f&30)&&k(e,"hidden",!l[2]),(!t||f&282)&&k(e,"disabled",l[8]),f&512&&c(e,"flex-grow",l[9]),f&256&&c(e,"pointer-events",l[8]?"none":null),f&512&&c(e,"width",l[9]===0?"fit-content":null),f&1024&&c(e,"min-width",typeof l[10]=="number"?`calc(min(${l[10]}px, 100%))`:null)},i(l){t||(h(u),h(a,l),t=!0)},o(l){g(u),g(a,l),t=!1},d(l){l&&v(e),u&&u.d(),a&&a.d(l)}}}function N(i){let e,n;return e=new A({props:{class:`button-icon ${i[5]?"right-padded":""}`,src:i[7].url,alt:`${i[5]} icon`}}),{c(){E(e.$$.fragment)},m(s,t){L(e,s,t),n=!0},p(s,t){const u={};t&32&&(u.class=`button-icon ${s[5]?"right-padded":""}`),t&128&&(u.src=s[7].url),t&32&&(u.alt=`${s[5]} icon`),e.$set(u)},i(s){n||(h(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){G(e,s)}}}function S(i){let e,n;return e=new A({props:{class:"button-icon",src:i[7].url,alt:`${i[5]} icon`}}),{c(){E(e.$$.fragment)},m(s,t){L(e,s,t),n=!0},p(s,t){const u={};t&128&&(u.src=s[7].url),t&32&&(u.alt=`${s[5]} icon`),e.$set(u)},i(s){n||(h(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){G(e,s)}}}function Z(i){let e,n,s,t;const u=[Y,X],_=[];function a(l,f){return l[6]&&l[6].length>0?0:1}return e=a(i),n=_[e]=u[e](i),{c(){n.c(),s=T()},m(l,f){_[e].m(l,f),q(l,s,f),t=!0},p(l,[f]){let o=e;e=a(l),e===o?_[e].p(l,f):(z(),g(_[o],1,1,()=>{_[o]=null}),w(),n=_[e],n?n.p(l,f):(n=_[e]=u[e](l),n.c()),h(n,1),n.m(s.parentNode,s))},i(l){t||(h(n),t=!0)},o(l){g(n),t=!1},d(l){l&&v(s),_[e].d(l)}}}function p(i,e,n){let{$$slots:s={},$$scope:t}=e,{elem_id:u=""}=e,{elem_classes:_=[]}=e,{visible:a=!0}=e,{variant:l="secondary"}=e,{size:f="lg"}=e,{value:o=null}=e,{link:m=null}=e,{icon:j=null}=e,{disabled:B=!1}=e,{scale:I=null}=e,{min_width:C=void 0}=e;function P(r){R.call(this,i,r)}return i.$$set=r=>{"elem_id"in r&&n(0,u=r.elem_id),"elem_classes"in r&&n(1,_=r.elem_classes),"visible"in r&&n(2,a=r.visible),"variant"in r&&n(3,l=r.variant),"size"in r&&n(4,f=r.size),"value"in r&&n(5,o=r.value),"link"in r&&n(6,m=r.link),"icon"in r&&n(7,j=r.icon),"disabled"in r&&n(8,B=r.disabled),"scale"in r&&n(9,I=r.scale),"min_width"in r&&n(10,C=r.min_width),"$$scope"in r&&n(11,t=r.$$scope)},[u,_,a,l,f,o,m,j,B,I,C,t,s,P]}class ne extends Q{constructor(e){super(),U(this,e,p,Z,W,{elem_id:0,elem_classes:1,visible:2,variant:3,size:4,value:5,link:6,icon:7,disabled:8,scale:9,min_width:10})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),b()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),b()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),b()}get variant(){return this.$$.ctx[3]}set variant(e){this.$$set({variant:e}),b()}get size(){return this.$$.ctx[4]}set size(e){this.$$set({size:e}),b()}get value(){return this.$$.ctx[5]}set value(e){this.$$set({value:e}),b()}get link(){return this.$$.ctx[6]}set link(e){this.$$set({link:e}),b()}get icon(){return this.$$.ctx[7]}set icon(e){this.$$set({icon:e}),b()}get disabled(){return this.$$.ctx[8]}set disabled(e){this.$$set({disabled:e}),b()}get scale(){return this.$$.ctx[9]}set scale(e){this.$$set({scale:e}),b()}get min_width(){return this.$$.ctx[10]}set min_width(e){this.$$set({min_width:e}),b()}}export{ne as B};
//# sourceMappingURL=Button-B3gqVEq2.js.map
