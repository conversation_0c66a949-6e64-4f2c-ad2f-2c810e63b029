import{F as V}from"./KHR_interactivity.DEAVS2UW.js";import{g as k,d as T,b as F,R as p}from"./declarationMapper.UBCwU7BT.js";import{ap as A,R as c}from"./index.BoI39RQH.js";class I extends V{constructor(t={}){super(t),this.keyFrames=[];const a=typeof(t==null?void 0:t.animationType)=="string"?k(t.animationType):T((t==null?void 0:t.animationType)??0),i=(t==null?void 0:t.keyFramesCount)??1,s=this.registerDataInput("duration_0",F,0),e=this.registerDataInput("value_0",a);this.keyFrames.push({duration:s,value:e});for(let n=1;n<i+1;n++){const o=this.registerDataInput(`duration_${n}`,F,n===i?t.duration:void 0),l=this.registerDataInput(`value_${n}`,a);this.keyFrames.push({duration:o,value:l})}this.initialValue=this.keyFrames[0].value,this.endValue=this.keyFrames[i].value,this.easingFunction=this.registerDataInput("easingFunction",p),this.animation=this.registerDataOutput("animation",p),this.propertyName=this.registerDataInput("propertyName",p,t==null?void 0:t.propertyName),this.customBuildAnimation=this.registerDataInput("customBuildAnimation",p)}_updateOutputs(t){const a=t._getGlobalContextVariable("interpolationAnimations",[]),i=this.propertyName.getValue(t),s=this.easingFunction.getValue(t),e=this._createAnimation(t,i,s);if(this.animation.setValue(e,t),Array.isArray(e))for(const n of e)a.push(n.uniqueId);else a.push(e.uniqueId);t._setGlobalContextVariable("interpolationAnimations",a)}_createAnimation(t,a,i){var h,y,d;const s=this.initialValue.richType,e=[],n=this.initialValue.getValue(t)||s.defaultValue;e.push({frame:0,value:n});const o=((h=this.config)==null?void 0:h.numberOfKeyFrames)??1;for(let r=1;r<o+1;r++){const m=(y=this.keyFrames[r].duration)==null?void 0:y.getValue(t);let u=(d=this.keyFrames[r].value)==null?void 0:d.getValue(t);r===o-1&&(u=u||s.defaultValue),m!==void 0&&u&&e.push({frame:m*60,value:u})}const l=this.customBuildAnimation.getValue(t);if(l)return l()(e,60,s.animationType,i);if(typeof a=="string"){const r=A.CreateAnimation(a,s.animationType,60,i);return r.setKeys(e),[r]}else return a.map(m=>{const u=A.CreateAnimation(m,s.animationType,60,i);return u.setKeys(e),u})}getClassName(){return"FlowGraphInterpolationBlock"}}c("FlowGraphInterpolationBlock",I);export{I as FlowGraphInterpolationBlock};
//# sourceMappingURL=flowGraphInterpolationBlock.Clp4-F7o.js.map
