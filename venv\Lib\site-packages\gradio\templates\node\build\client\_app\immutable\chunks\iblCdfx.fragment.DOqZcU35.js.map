{"version": 3, "file": "iblCdfx.fragment.DOqZcU35.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Shaders/iblCdfx.fragment.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nconst name = \"iblCdfxPixelShader\";\nconst shader = `precision highp sampler2D;\n#define PI 3.1415927\nvarying vec2 vUV;uniform sampler2D cdfy;void main(void) {ivec2 cdfyRes=textureSize(cdfy,0);ivec2 currentPixel=ivec2(gl_FragCoord.xy);float cdfx=0.0;for (int x=1; x<=currentPixel.x; x++) {cdfx+=texelFetch(cdfy,ivec2(x-1,cdfyRes.y-1),0).x;}\ngl_FragColor=vec4(vec3(cdfx),1.0);}`;\n// Sideeffect\nif (!ShaderStore.ShadersStore[name]) {\n    ShaderStore.ShadersStore[name] = shader;\n}\n/** @internal */\nexport const iblCdfxPixelShader = { name, shader };\n//# sourceMappingURL=iblCdfx.fragment.js.map"], "names": ["name", "shader", "ShaderStore", "iblCdfxPixelShader"], "mappings": "wCAEA,MAAMA,EAAO,qBACPC,EAAS;AAAA;AAAA;AAAA,qCAKVC,EAAY,aAAaF,CAAI,IAC9BE,EAAY,aAAaF,CAAI,EAAIC,GAGzB,MAACE,EAAqB,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}