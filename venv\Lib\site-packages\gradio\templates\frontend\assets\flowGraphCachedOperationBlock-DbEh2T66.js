import{F as u}from"./KHR_interactivity-DTxiAnOo.js";import{c}from"./declarationMapper-BZjsjg7g.js";const l="cachedOperationValue",t="cachedExecutionId";class V extends u{constructor(e,i){super(i),this.value=this.registerDataOutput("value",e),this.isValid=this.registerDataOutput("isValid",c)}_updateOutputs(e){const i=e._getExecutionVariable(this,t,-1),s=e._getExecutionVariable(this,l,null);if(s!=null&&i===e.executionId)this.isValid.setValue(!0,e),this.value.setValue(s,e);else try{const a=this._doOperation(e);if(a==null){this.isValid.setValue(!1,e);return}e._setExecutionVariable(this,l,a),e._setExecutionVariable(this,t,e.executionId),this.value.setValue(a,e),this.isValid.setValue(!0,e)}catch{this.isValid.setValue(!1,e)}}}export{V as F};
//# sourceMappingURL=flowGraphCachedOperationBlock-DbEh2T66.js.map
