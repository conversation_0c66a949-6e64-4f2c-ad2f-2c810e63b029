{"version": 3, "file": "flowGraphPlayAnimationBlock-SS81a80Y.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/Animation/flowGraphPlayAnimationBlock.js"], "sourcesContent": ["import { FlowGraphAsyncExecutionBlock } from \"../../../flowGraphAsyncExecutionBlock.js\";\nimport { RichTypeAny, RichTypeNumber, RichTypeBoolean } from \"../../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\nimport { AnimationGroup } from \"../../../../Animations/animationGroup.js\";\n/**\n * @experimental\n * A block that plays an animation on an animatable object.\n */\nexport class FlowGraphPlayAnimationBlock extends FlowGraphAsyncExecutionBlock {\n    constructor(\n    /**\n     * the configuration of the block\n     */\n    config) {\n        super(config, [\"animationLoop\", \"animationEnd\", \"animationGroupLoop\"]);\n        this.config = config;\n        this.speed = this.registerDataInput(\"speed\", RichTypeNumber);\n        this.loop = this.registerDataInput(\"loop\", RichTypeBoolean);\n        this.from = this.registerDataInput(\"from\", RichTypeNumber, 0);\n        this.to = this.registerDataInput(\"to\", RichTypeNumber);\n        this.currentFrame = this.registerDataOutput(\"currentFrame\", RichTypeNumber);\n        this.currentTime = this.registerDataOutput(\"currentTime\", RichTypeNumber);\n        this.currentAnimationGroup = this.registerDataOutput(\"currentAnimationGroup\", RichTypeAny);\n        this.animationGroup = this.registerDataInput(\"animationGroup\", RichTypeAny, config?.animationGroup);\n        this.animation = this.registerDataInput(\"animation\", RichTypeAny);\n        this.object = this.registerDataInput(\"object\", RichTypeAny);\n    }\n    /**\n     * @internal\n     * @param context\n     */\n    _preparePendingTasks(context) {\n        const ag = this.animationGroup.getValue(context);\n        const animation = this.animation.getValue(context);\n        if (!ag && !animation) {\n            return this._reportError(context, \"No animation or animation group provided\");\n        }\n        else {\n            // if an animation group was already created, dispose it and create a new one\n            const currentAnimationGroup = this.currentAnimationGroup.getValue(context);\n            if (currentAnimationGroup && currentAnimationGroup !== ag) {\n                currentAnimationGroup.dispose();\n            }\n            let animationGroupToUse = ag;\n            // check which animation to use. If no animationGroup was defined and an animation was provided, use the animation\n            if (animation && !animationGroupToUse) {\n                const target = this.object.getValue(context);\n                if (!target) {\n                    return this._reportError(context, \"No target object provided\");\n                }\n                const animationsArray = Array.isArray(animation) ? animation : [animation];\n                const name = animationsArray[0].name;\n                animationGroupToUse = new AnimationGroup(\"flowGraphAnimationGroup-\" + name + \"-\" + target.name, context.configuration.scene);\n                let isInterpolation = false;\n                const interpolationAnimations = context._getGlobalContextVariable(\"interpolationAnimations\", []);\n                for (const anim of animationsArray) {\n                    animationGroupToUse.addTargetedAnimation(anim, target);\n                    if (interpolationAnimations.indexOf(anim.uniqueId) !== -1) {\n                        isInterpolation = true;\n                    }\n                }\n                if (isInterpolation) {\n                    this._checkInterpolationDuplications(context, animationsArray, target);\n                }\n            }\n            // not accepting 0\n            const speed = this.speed.getValue(context) || 1;\n            const from = this.from.getValue(context) ?? 0;\n            // not accepting 0\n            const to = this.to.getValue(context) || animationGroupToUse.to;\n            const loop = !isFinite(to) || this.loop.getValue(context);\n            this.currentAnimationGroup.setValue(animationGroupToUse, context);\n            const currentlyRunningAnimationGroups = context._getGlobalContextVariable(\"currentlyRunningAnimationGroups\", []);\n            // check if it already running\n            if (currentlyRunningAnimationGroups.indexOf(animationGroupToUse.uniqueId) !== -1) {\n                animationGroupToUse.stop();\n            }\n            try {\n                animationGroupToUse.start(loop, speed, from, to);\n                animationGroupToUse.onAnimationGroupEndObservable.add(() => this._onAnimationGroupEnd(context));\n                animationGroupToUse.onAnimationEndObservable.add(() => this._eventsSignalOutputs[\"animationEnd\"]._activateSignal(context));\n                animationGroupToUse.onAnimationLoopObservable.add(() => this._eventsSignalOutputs[\"animationLoop\"]._activateSignal(context));\n                animationGroupToUse.onAnimationGroupLoopObservable.add(() => this._eventsSignalOutputs[\"animationGroupLoop\"]._activateSignal(context));\n                currentlyRunningAnimationGroups.push(animationGroupToUse.uniqueId);\n                context._setGlobalContextVariable(\"currentlyRunningAnimationGroups\", currentlyRunningAnimationGroups);\n            }\n            catch (e) {\n                this._reportError(context, e);\n            }\n        }\n    }\n    _reportError(context, error) {\n        super._reportError(context, error);\n        this.currentFrame.setValue(-1, context);\n        this.currentTime.setValue(-1, context);\n    }\n    /**\n     * @internal\n     */\n    _executeOnTick(_context) {\n        const ag = this.currentAnimationGroup.getValue(_context);\n        if (ag) {\n            this.currentFrame.setValue(ag.getCurrentFrame(), _context);\n            this.currentTime.setValue(ag.animatables[0]?.elapsedTime ?? 0, _context);\n        }\n    }\n    _execute(context) {\n        this._startPendingTasks(context);\n    }\n    _onAnimationGroupEnd(context) {\n        this._removeFromCurrentlyRunning(context, this.currentAnimationGroup.getValue(context));\n        this._resetAfterCanceled(context);\n        this.done._activateSignal(context);\n    }\n    /**\n     * The idea behind this function is to check every running animation group and check if the targeted animations it uses are interpolation animations.\n     * If they are, we want to see that they don't collide with the current interpolation animations that are starting to play.\n     * If they do, we want to stop the already-running animation group.\n     * @internal\n     */\n    _checkInterpolationDuplications(context, animation, target) {\n        const currentlyRunningAnimationGroups = context._getGlobalContextVariable(\"currentlyRunningAnimationGroups\", []);\n        for (const uniqueId of currentlyRunningAnimationGroups) {\n            const ag = context.assetsContext.animationGroups.find((ag) => ag.uniqueId === uniqueId);\n            if (ag) {\n                for (const anim of ag.targetedAnimations) {\n                    for (const animToCheck of animation) {\n                        if (anim.animation.targetProperty === animToCheck.targetProperty && anim.target === target) {\n                            this._stopAnimationGroup(context, ag);\n                        }\n                    }\n                }\n            }\n        }\n    }\n    _stopAnimationGroup(context, animationGroup) {\n        // stop, while skipping the on AnimationEndObservable to avoid the \"done\" signal\n        animationGroup.stop(true);\n        animationGroup.dispose();\n        this._removeFromCurrentlyRunning(context, animationGroup);\n    }\n    _removeFromCurrentlyRunning(context, animationGroup) {\n        const currentlyRunningAnimationGroups = context._getGlobalContextVariable(\"currentlyRunningAnimationGroups\", []);\n        const idx = currentlyRunningAnimationGroups.indexOf(animationGroup.uniqueId);\n        if (idx !== -1) {\n            currentlyRunningAnimationGroups.splice(idx, 1);\n            context._setGlobalContextVariable(\"currentlyRunningAnimationGroups\", currentlyRunningAnimationGroups);\n        }\n    }\n    /**\n     * @internal\n     * Stop any currently running animations.\n     */\n    _cancelPendingTasks(context) {\n        const ag = this.currentAnimationGroup.getValue(context);\n        if (ag) {\n            this._stopAnimationGroup(context, ag);\n        }\n    }\n    /**\n     * @returns class name of the block.\n     */\n    getClassName() {\n        return \"FlowGraphPlayAnimationBlock\" /* FlowGraphBlockNames.PlayAnimation */;\n    }\n}\nRegisterClass(\"FlowGraphPlayAnimationBlock\" /* FlowGraphBlockNames.PlayAnimation */, FlowGraphPlayAnimationBlock);\n//# sourceMappingURL=flowGraphPlayAnimationBlock.js.map"], "names": ["FlowGraphPlayAnimationBlock", "FlowGraphAsyncExecutionBlock", "config", "RichTypeNumber", "RichTypeBoolean", "RichTypeAny", "context", "ag", "animation", "currentAnimationGroup", "animationGroupToUse", "target", "animationsArray", "name", "AnimationGroup", "isInterpolation", "interpolationAnimations", "anim", "speed", "from", "to", "loop", "currentlyRunningAnimationGroups", "e", "error", "_context", "uniqueId", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animationGroup", "idx", "RegisterClass"], "mappings": "uVAQO,MAAMA,UAAoCC,CAA6B,CAC1E,YAIAC,EAAQ,CACJ,MAAMA,EAAQ,CAAC,gBAAiB,eAAgB,oBAAoB,CAAC,EACrE,KAAK,OAASA,EACd,KAAK,MAAQ,KAAK,kBAAkB,QAASC,CAAc,EAC3D,KAAK,KAAO,KAAK,kBAAkB,OAAQC,CAAe,EAC1D,KAAK,KAAO,KAAK,kBAAkB,OAAQD,EAAgB,CAAC,EAC5D,KAAK,GAAK,KAAK,kBAAkB,KAAMA,CAAc,EACrD,KAAK,aAAe,KAAK,mBAAmB,eAAgBA,CAAc,EAC1E,KAAK,YAAc,KAAK,mBAAmB,cAAeA,CAAc,EACxE,KAAK,sBAAwB,KAAK,mBAAmB,wBAAyBE,CAAW,EACzF,KAAK,eAAiB,KAAK,kBAAkB,iBAAkBA,EAAaH,GAAQ,cAAc,EAClG,KAAK,UAAY,KAAK,kBAAkB,YAAaG,CAAW,EAChE,KAAK,OAAS,KAAK,kBAAkB,SAAUA,CAAW,CAC7D,CAKD,qBAAqBC,EAAS,CAC1B,MAAMC,EAAK,KAAK,eAAe,SAASD,CAAO,EACzCE,EAAY,KAAK,UAAU,SAASF,CAAO,EACjD,GAAI,CAACC,GAAM,CAACC,EACR,OAAO,KAAK,aAAaF,EAAS,0CAA0C,EAE3E,CAED,MAAMG,EAAwB,KAAK,sBAAsB,SAASH,CAAO,EACrEG,GAAyBA,IAA0BF,GACnDE,EAAsB,QAAO,EAEjC,IAAIC,EAAsBH,EAE1B,GAAIC,GAAa,CAACE,EAAqB,CACnC,MAAMC,EAAS,KAAK,OAAO,SAASL,CAAO,EAC3C,GAAI,CAACK,EACD,OAAO,KAAK,aAAaL,EAAS,2BAA2B,EAEjE,MAAMM,EAAkB,MAAM,QAAQJ,CAAS,EAAIA,EAAY,CAACA,CAAS,EACnEK,EAAOD,EAAgB,CAAC,EAAE,KAChCF,EAAsB,IAAII,EAAe,2BAA6BD,EAAO,IAAMF,EAAO,KAAML,EAAQ,cAAc,KAAK,EAC3H,IAAIS,EAAkB,GACtB,MAAMC,EAA0BV,EAAQ,0BAA0B,0BAA2B,CAAE,CAAA,EAC/F,UAAWW,KAAQL,EACfF,EAAoB,qBAAqBO,EAAMN,CAAM,EACjDK,EAAwB,QAAQC,EAAK,QAAQ,IAAM,KACnDF,EAAkB,IAGtBA,GACA,KAAK,gCAAgCT,EAASM,EAAiBD,CAAM,CAE5E,CAED,MAAMO,EAAQ,KAAK,MAAM,SAASZ,CAAO,GAAK,EACxCa,EAAO,KAAK,KAAK,SAASb,CAAO,GAAK,EAEtCc,EAAK,KAAK,GAAG,SAASd,CAAO,GAAKI,EAAoB,GACtDW,EAAO,CAAC,SAASD,CAAE,GAAK,KAAK,KAAK,SAASd,CAAO,EACxD,KAAK,sBAAsB,SAASI,EAAqBJ,CAAO,EAChE,MAAMgB,EAAkChB,EAAQ,0BAA0B,kCAAmC,CAAE,CAAA,EAE3GgB,EAAgC,QAAQZ,EAAoB,QAAQ,IAAM,IAC1EA,EAAoB,KAAI,EAE5B,GAAI,CACAA,EAAoB,MAAMW,EAAMH,EAAOC,EAAMC,CAAE,EAC/CV,EAAoB,8BAA8B,IAAI,IAAM,KAAK,qBAAqBJ,CAAO,CAAC,EAC9FI,EAAoB,yBAAyB,IAAI,IAAM,KAAK,qBAAqB,aAAgB,gBAAgBJ,CAAO,CAAC,EACzHI,EAAoB,0BAA0B,IAAI,IAAM,KAAK,qBAAqB,cAAiB,gBAAgBJ,CAAO,CAAC,EAC3HI,EAAoB,+BAA+B,IAAI,IAAM,KAAK,qBAAqB,mBAAsB,gBAAgBJ,CAAO,CAAC,EACrIgB,EAAgC,KAAKZ,EAAoB,QAAQ,EACjEJ,EAAQ,0BAA0B,kCAAmCgB,CAA+B,CACvG,OACMC,EAAG,CACN,KAAK,aAAajB,EAASiB,CAAC,CAC/B,CACJ,CACJ,CACD,aAAajB,EAASkB,EAAO,CACzB,MAAM,aAAalB,EAASkB,CAAK,EACjC,KAAK,aAAa,SAAS,GAAIlB,CAAO,EACtC,KAAK,YAAY,SAAS,GAAIA,CAAO,CACxC,CAID,eAAemB,EAAU,CACrB,MAAMlB,EAAK,KAAK,sBAAsB,SAASkB,CAAQ,EACnDlB,IACA,KAAK,aAAa,SAASA,EAAG,gBAAe,EAAIkB,CAAQ,EACzD,KAAK,YAAY,SAASlB,EAAG,YAAY,CAAC,GAAG,aAAe,EAAGkB,CAAQ,EAE9E,CACD,SAASnB,EAAS,CACd,KAAK,mBAAmBA,CAAO,CAClC,CACD,qBAAqBA,EAAS,CAC1B,KAAK,4BAA4BA,EAAS,KAAK,sBAAsB,SAASA,CAAO,CAAC,EACtF,KAAK,oBAAoBA,CAAO,EAChC,KAAK,KAAK,gBAAgBA,CAAO,CACpC,CAOD,gCAAgCA,EAASE,EAAWG,EAAQ,CACxD,MAAMW,EAAkChB,EAAQ,0BAA0B,kCAAmC,CAAE,CAAA,EAC/G,UAAWoB,KAAYJ,EAAiC,CACpD,MAAMf,EAAKD,EAAQ,cAAc,gBAAgB,KAAMC,GAAOA,EAAG,WAAamB,CAAQ,EACtF,GAAInB,EACA,UAAWU,KAAQV,EAAG,mBAClB,UAAWoB,KAAenB,EAClBS,EAAK,UAAU,iBAAmBU,EAAY,gBAAkBV,EAAK,SAAWN,GAChF,KAAK,oBAAoBL,EAASC,CAAE,CAKvD,CACJ,CACD,oBAAoBD,EAASsB,EAAgB,CAEzCA,EAAe,KAAK,EAAI,EACxBA,EAAe,QAAO,EACtB,KAAK,4BAA4BtB,EAASsB,CAAc,CAC3D,CACD,4BAA4BtB,EAASsB,EAAgB,CACjD,MAAMN,EAAkChB,EAAQ,0BAA0B,kCAAmC,CAAE,CAAA,EACzGuB,EAAMP,EAAgC,QAAQM,EAAe,QAAQ,EACvEC,IAAQ,KACRP,EAAgC,OAAOO,EAAK,CAAC,EAC7CvB,EAAQ,0BAA0B,kCAAmCgB,CAA+B,EAE3G,CAKD,oBAAoBhB,EAAS,CACzB,MAAMC,EAAK,KAAK,sBAAsB,SAASD,CAAO,EAClDC,GACA,KAAK,oBAAoBD,EAASC,CAAE,CAE3C,CAID,cAAe,CACX,MAAO,6BACV,CACL,CACAuB,EAAc,8BAAuE9B,CAA2B", "x_google_ignoreList": [0]}