/* empty css                                              */const{SvelteComponent:h,add_iframe_resize_listener:v,add_render_callback:y,append:b,attr:m,binding_callbacks:p,detach:w,element:z,flush:_,init:S,insert:k,noop:d,safe_not_equal:q,set_data:C,text:E,toggle_class:r}=window.__gradio__svelte__internal,{onMount:M}=window.__gradio__svelte__internal;function P(t){let e,l=f(t[0])+"",a,u;return{c(){e=z("div"),a=E(l),m(e,"class","svelte-1oitfqa"),y(()=>t[5].call(e)),r(e,"table",t[1]==="table"),r(e,"gallery",t[1]==="gallery"),r(e,"selected",t[2])},m(n,s){k(n,e,s),b(e,a),u=v(e,t[5].bind(e)),t[6](e)},p(n,[s]){s&1&&l!==(l=f(n[0])+"")&&C(a,l),s&2&&r(e,"table",n[1]==="table"),s&2&&r(e,"gallery",n[1]==="gallery"),s&4&&r(e,"selected",n[2])},i:d,o:d,d(n){n&&w(e),u(),t[6](null)}}}function W(t,e){t.style.setProperty("--local-text-width",`${e&&e<150?e:200}px`),t.style.whiteSpace="unset"}function f(t,e=60){if(!t)return"";const l=String(t);return l.length<=e?l:l.slice(0,e)+"..."}function j(t,e,l){let{value:a}=e,{type:u}=e,{selected:n=!1}=e,s,c;M(()=>{W(c,s)});function o(){s=this.clientWidth,l(3,s)}function g(i){p[i?"unshift":"push"](()=>{c=i,l(4,c)})}return t.$$set=i=>{"value"in i&&l(0,a=i.value),"type"in i&&l(1,u=i.type),"selected"in i&&l(2,n=i.selected)},[a,u,n,s,c,o,g]}class B extends h{constructor(e){super(),S(this,e,j,P,q,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),_()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),_()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),_()}}export{B as default};
//# sourceMappingURL=Example-BdAjEacD.js.map
