import { c as create_ssr_component, k as setContext, v as validate_component, m as missing_component } from './chunks/ssr-C3HYbsxA.js';
import { a as afterUpdate } from './chunks/ssr-BO6k2UBO.js';
import { c as c$1, r } from './chunks/index4-HDyePXPv.js';
import { m as m$1, U as U$2, T, P, b, y, O, j, S } from './chunks/exports-Cu4i9J_Z.js';
import { r as readable, w as writable } from './chunks/index-ClteBeTX.js';
import './chunks/Component-NmRBwSfF.js';

let c="",m=c;const v={base:c,assets:m};function q$1(t){c=t.base,m=t.assets;}function B$1(){c=v.base,m=v.assets;}let G$1={},M={};function N(t){G$1=t;}function H(t){M=t;}const U$1=create_ssr_component((t,e,n,b)=>{let{stores:i}=e,{page:p}=e,{constructors:s}=e,{components:a=[]}=e,{form:l}=e,{data_0:d=null}=e,{data_1:f=null}=e;setContext("__svelte__",i),afterUpdate(i.page.notify);e.stores===void 0&&n.stores&&i!==void 0&&n.stores(i),e.page===void 0&&n.page&&p!==void 0&&n.page(p),e.constructors===void 0&&n.constructors&&s!==void 0&&n.constructors(s),e.components===void 0&&n.components&&a!==void 0&&n.components(a),e.form===void 0&&n.form&&l!==void 0&&n.form(l),e.data_0===void 0&&n.data_0&&d!==void 0&&n.data_0(d),e.data_1===void 0&&n.data_1&&f!==void 0&&n.data_1(f);let r,w,x=t.head;do r=!0,t.head=x,i.page.set(p),w=`  ${s[1]?`${validate_component(s[0]||missing_component,"svelte:component").$$render(t,{data:d,this:a[0]},{this:o=>{a[0]=o,r=!1;}},{default:()=>`${validate_component(s[1]||missing_component,"svelte:component").$$render(t,{data:f,form:l,this:a[1]},{this:o=>{a[1]=o,r=!1;}},{})}`})}`:`${validate_component(s[0]||missing_component,"svelte:component").$$render(t,{data:d,form:l,this:a[0]},{this:o=>{a[0]=o,r=!1;}},{})}`} ${""}`;while(!r);return w});const D$1={app_dir:"_app",app_template_contains_nonce:!1,csp:{mode:"auto",directives:{"upgrade-insecure-requests":!1,"block-all-mixed-content":!1},reportOnly:{"upgrade-insecure-requests":!1,"block-all-mixed-content":!1}},csrf_check_origin:!0,embedded:!1,env_public_prefix:"PUBLIC_",env_private_prefix:"",hooks:null,preload_strategy:"modulepreload",root:U$1,service_worker:!1,templates:{app:({head:t,body:e,assets:n,nonce:b,env:i})=>`<!doctype html>
<html
	lang="en"
	style="
		margin: 0;
		padding: 0;
		min-height: 100%;
		display: flex;
		flex-direction: column;
	"
>
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="/favicon.ico" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<meta property="og:title" content="Gradio" />
		<meta property="og:type" content="website" />
		<meta property="og:url" content="{url}" />
		<meta property="og:description" content="Click to try out the app!" />
		<meta
			property="og:image"
			content="https://raw.githubusercontent.com/gradio-app/gradio/main/js/_website/src/lib/assets/img/header-image.jpg"
		/>
		<meta name="twitter:card" content="summary_large_image" />
		<meta name="twitter:creator" content="@Gradio" />
		<meta name="twitter:title" content="Gradio" />
		<meta name="twitter:description" content="Click to try out the app!" />
		<meta
			name="twitter:image"
			content="https://raw.githubusercontent.com/gradio-app/gradio/main/js/_website/src/lib/assets/img/header-image.jpg"
		/>
		<script data-gradio-mode>
			window.__gradio_mode__ = "app";
			window.iFrameResizer = {
				heightCalculationMethod: "taggedElement"
			};
			window.parent?.postMessage(
				{ type: "SET_SCROLLING", enabled: false },
				"*"
			);
		<\/script>
		<script
			src="https://cdnjs.cloudflare.com/ajax/libs/iframe-resizer/4.3.1/iframeResizer.contentWindow.min.js"
			async
		><\/script>

		`+t+`
	</head>
	<body
		data-sveltekit-preload-data="hover"
		style="
			width: 100%;
			margin: 0;
			padding: 0;
			display: flex;
			flex-direction: column;
			flex-grow: 1;
		"
	>
		<div style="display: contents">`+e+`</div>
	</body>
</html>
`,error:({status:t,message:e})=>`<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<title>`+e+`</title>

		<style>
			body {
				--bg: white;
				--fg: #222;
				--divider: #ccc;
				background: var(--bg);
				color: var(--fg);
				font-family:
					system-ui,
					-apple-system,
					BlinkMacSystemFont,
					'Segoe UI',
					Roboto,
					Oxygen,
					Ubuntu,
					Cantarell,
					'Open Sans',
					'Helvetica Neue',
					sans-serif;
				display: flex;
				align-items: center;
				justify-content: center;
				height: 100vh;
				margin: 0;
			}

			.error {
				display: flex;
				align-items: center;
				max-width: 32rem;
				margin: 0 1rem;
			}

			.status {
				font-weight: 200;
				font-size: 3rem;
				line-height: 1;
				position: relative;
				top: -0.05rem;
			}

			.message {
				border-left: 1px solid var(--divider);
				padding: 0 0 0 1rem;
				margin: 0 0 0 1rem;
				min-height: 2.5rem;
				display: flex;
				align-items: center;
			}

			.message h1 {
				font-weight: 400;
				font-size: 1em;
				margin: 0;
			}

			@media (prefers-color-scheme: dark) {
				body {
					--bg: #222;
					--fg: #ddd;
					--divider: #666;
				}
			}
		</style>
	</head>
	<body>
		<div class="error">
			<span class="status">`+t+`</span>
			<div class="message">
				<h1>`+e+`</h1>
			</div>
		</div>
	</body>
</html>
`},version_hash:"o680fy"};async function J(){return {}}

const Ft="/_svelte_kit_assets",rt=["GET","POST","PUT","PATCH","DELETE","OPTIONS","HEAD"],Gt=["GET","POST","HEAD"];function Oe(e,t){const r=[];e.split(",").forEach((i,o)=>{const c=/([^/ \t]+)\/([^; \t]+)[ \t]*(?:;[ \t]*q=([0-9.]+))?/.exec(i);if(c){const[,f,a,l="1"]=c;r.push({type:f,subtype:a,q:+l,i:o});}}),r.sort((i,o)=>i.q!==o.q?o.q-i.q:i.subtype==="*"!=(o.subtype==="*")?i.subtype==="*"?1:-1:i.type==="*"!=(o.type==="*")?i.type==="*"?1:-1:i.i-o.i);let s,n=1/0;for(const i of t){const[o,c]=i.split("/"),f=r.findIndex(a=>(a.type===o||a.type==="*")&&(a.subtype===c||a.subtype==="*"));f!==-1&&f<n&&(s=i,n=f);}return s}function Zt(e,...t){const r=e.headers.get("content-type")?.split(";",1)[0].trim()??"";return t.includes(r.toLowerCase())}function st(e){return Zt(e,"application/x-www-form-urlencoded","multipart/form-data","text/plain")}class Q{constructor(t,r){this.status=t,typeof r=="string"?this.body={message:r}:r?this.body=r:this.body={message:`Error: ${t}`};}toString(){return JSON.stringify(this.body)}}class z{constructor(t,r){this.status=t,this.location=r;}}class L extends Error{constructor(t,r,s){super(s),this.status=t,this.text=r;}}class Se{constructor(t,r){this.status=t,this.data=r;}}function Yt(e){return e instanceof Error||e&&e.name&&e.message?e:new Error(JSON.stringify(e))}function Z(e){return e instanceof Q||e instanceof L?e.status:500}function Kt(e){return e instanceof L?e.text:"Internal Error"}function nt(e,t){return r(`${t} method not allowed`,{status:405,headers:{allow:Xt(e).join(", ")}})}function Xt(e){const t=rt.filter(r=>r in e);return ("GET"in e||"HEAD"in e)&&t.push("HEAD"),t}function ne(e,t,r$1){let s=e.templates.error({status:t,message:r$1});return r(s,{headers:{"content-type":"text/html; charset=utf-8"},status:t})}async function De(e,t,r){r=r instanceof Q?r:Yt(r);const s=Z(r),n=await C(e,t,r),i=Oe(e.request.headers.get("accept")||"text/html",["application/json","text/html"]);return e.isDataRequest||i==="application/json"?c$1(n,{status:s}):ne(t,s,n.message)}async function C(e,t,r){if(r instanceof Q)return r.body;const s=Z(r),n=Kt(r);return await t.hooks.handleError({error:r,event:e,status:s,message:n})??{message:n}}function ae(e,t){return new Response(void 0,{status:e,headers:{location:t}})}function at(e,t){return t.path?`Data returned from \`load\` while rendering ${e.route.id} is not serializable: ${t.message} (data${t.path})`:t.path===""?`Data returned from \`load\` while rendering ${e.route.id} is not a plain object`:t.message}function ot(e){const t=[];return e.uses&&e.uses.dependencies.size>0&&t.push(`"dependencies":${JSON.stringify(Array.from(e.uses.dependencies))}`),e.uses&&e.uses.search_params.size>0&&t.push(`"search_params":${JSON.stringify(Array.from(e.uses.search_params))}`),e.uses&&e.uses.params.size>0&&t.push(`"params":${JSON.stringify(Array.from(e.uses.params))}`),e.uses?.parent&&t.push('"parent":1'),e.uses?.route&&t.push('"route":1'),e.uses?.url&&t.push('"url":1'),`"uses":{${t.join(",")}}`}async function Qt(e,t,r){const s=e.request.method;let n=t[s]||t.fallback;if(s==="HEAD"&&t.GET&&!t.HEAD&&(n=t.GET),!n)return nt(t,s);const i=t.prerender??r.prerender_default;if(i&&(t.POST||t.PATCH||t.PUT||t.DELETE))throw new Error("Cannot prerender endpoints that have mutative methods");if(r.prerendering&&!i){if(r.depth>0)throw new Error(`${e.route.id} is not prerenderable`);return new Response(void 0,{status:204})}try{let o=await n(e);if(!(o instanceof Response))throw new Error(`Invalid response from route ${e.url.pathname}: handler should return a Response object`);return r.prerendering&&(o=new Response(o.body,{status:o.status,statusText:o.statusText,headers:new Headers(o.headers)}),o.headers.set("x-sveltekit-prerender",String(i))),o}catch(o){if(o instanceof z)return new Response(void 0,{status:o.status,headers:{location:o.location}});throw o}}function er(e){const{method:t,headers:r}=e.request;if(rt.includes(t)&&!Gt.includes(t))return !0;if(t==="POST"&&r.get("x-sveltekit-action")==="true")return !1;const s=e.request.headers.get("accept")??"*/*";return Oe(s,["*","text/html"])!=="text/html"}function He(e){return e.filter(t=>t!=null)}const tr={"<":"\\u003C","\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t","\u2028":"\\u2028","\u2029":"\\u2029"};class F extends Error{constructor(t,r){super(t),this.name="DevalueError",this.path=r.join("");}}function B(e){return Object(e)!==e}const rr=Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function it(e){const t=Object.getPrototypeOf(e);return t===Object.prototype||t===null||Object.getOwnPropertyNames(t).sort().join("\0")===rr}function re(e){return Object.prototype.toString.call(e).slice(8,-1)}function sr(e){switch(e){case'"':return '\\"';case"<":return "\\u003C";case"\\":return "\\\\";case`
`:return "\\n";case"\r":return "\\r";case"	":return "\\t";case"\b":return "\\b";case"\f":return "\\f";case"\u2028":return "\\u2028";case"\u2029":return "\\u2029";default:return e<" "?`\\u${e.charCodeAt(0).toString(16).padStart(4,"0")}`:""}}function U(e){let t="",r=0;const s=e.length;for(let n=0;n<s;n+=1){const i=e[n],o=sr(i);o&&(t+=e.slice(r,n)+o,r=n+1);}return `"${r===0?e:t+e.slice(r)}"`}function ct(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.getOwnPropertyDescriptor(e,t).enumerable)}const le="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_$",nr=/[<\b\f\n\r\t\0\u2028\u2029]/g,ar=/^(?:do|if|in|for|int|let|new|try|var|byte|case|char|else|enum|goto|long|this|void|with|await|break|catch|class|const|final|float|short|super|throw|while|yield|delete|double|export|import|native|return|switch|throws|typeof|boolean|default|extends|finally|package|private|abstract|continue|debugger|function|volatile|interface|protected|transient|implements|instanceof|synchronized)$/;function G(e,t){const r=new Map,s=[],n=new Map;function i(a){if(typeof a=="function")throw new F("Cannot stringify a function",s);if(!B(a)){if(r.has(a)){r.set(a,r.get(a)+1);return}if(r.set(a,1),t){const u=t(a);if(typeof u=="string"){n.set(a,u);return}}switch(re(a)){case"Number":case"BigInt":case"String":case"Boolean":case"Date":case"RegExp":return;case"Array":a.forEach((u,_)=>{s.push(`[${_}]`),i(u),s.pop();});break;case"Set":Array.from(a).forEach(i);break;case"Map":for(const[u,_]of a)s.push(`.get(${B(u)?fe(u):"..."})`),i(_),s.pop();break;default:if(!it(a))throw new F("Cannot stringify arbitrary non-POJOs",s);if(ct(a).length>0)throw new F("Cannot stringify POJOs with symbolic keys",s);for(const u in a)s.push(`.${u}`),i(a[u]),s.pop();}}}i(e);const o=new Map;Array.from(r).filter(a=>a[1]>1).sort((a,l)=>l[1]-a[1]).forEach((a,l)=>{o.set(a[0],or(l));});function c(a){if(o.has(a))return o.get(a);if(B(a))return fe(a);if(n.has(a))return n.get(a);const l=re(a);switch(l){case"Number":case"String":case"Boolean":return `Object(${c(a.valueOf())})`;case"RegExp":return `new RegExp(${U(a.source)}, "${a.flags}")`;case"Date":return `new Date(${a.getTime()})`;case"Array":const u=a.map((h,g)=>g in a?c(h):""),_=a.length===0||a.length-1 in a?"":",";return `[${u.join(",")}${_}]`;case"Set":case"Map":return `new ${l}([${Array.from(a).map(c).join(",")}])`;default:const d=`{${Object.keys(a).map(h=>`${cr(h)}:${c(a[h])}`).join(",")}}`;return Object.getPrototypeOf(a)===null?Object.keys(a).length>0?`Object.assign(Object.create(null),${d})`:"Object.create(null)":d}}const f=c(e);if(o.size){const a=[],l=[],u=[];return o.forEach((_,d)=>{if(a.push(_),n.has(d)){u.push(n.get(d));return}if(B(d)){u.push(fe(d));return}switch(re(d)){case"Number":case"String":case"Boolean":u.push(`Object(${c(d.valueOf())})`);break;case"RegExp":u.push(d.toString());break;case"Date":u.push(`new Date(${d.getTime()})`);break;case"Array":u.push(`Array(${d.length})`),d.forEach((h,g)=>{l.push(`${_}[${g}]=${c(h)}`);});break;case"Set":u.push("new Set"),l.push(`${_}.${Array.from(d).map(h=>`add(${c(h)})`).join(".")}`);break;case"Map":u.push("new Map"),l.push(`${_}.${Array.from(d).map(([h,g])=>`set(${c(h)}, ${c(g)})`).join(".")}`);break;default:u.push(Object.getPrototypeOf(d)===null?"Object.create(null)":"{}"),Object.keys(d).forEach(h=>{l.push(`${_}${ur(h)}=${c(d[h])}`);});}}),l.push(`return ${f}`),`(function(${a.join(",")}){${l.join(";")}}(${u.join(",")}))`}else return f}function or(e){let t="";do t=le[e%le.length]+t,e=~~(e/le.length)-1;while(e>=0);return ar.test(t)?`${t}0`:t}function ir(e){return tr[e]||e}function ut(e){return e.replace(nr,ir)}function cr(e){return /^[_$a-zA-Z][_$a-zA-Z0-9]*$/.test(e)?e:ut(JSON.stringify(e))}function ur(e){return /^[_$a-zA-Z][_$a-zA-Z0-9]*$/.test(e)?`.${e}`:`[${ut(JSON.stringify(e))}]`}function fe(e){if(typeof e=="string")return U(e);if(e===void 0)return "void 0";if(e===0&&1/e<0)return "-0";const t=String(e);return typeof e=="number"?t.replace(/^(-)?0\./,"$1."):typeof e=="bigint"?e+"n":t}const lt=-1,lr=-2,fr=-3,dr=-4,pr=-5,ft=-6;function se(e,t){const r=[],s=new Map,n=[];for(const a in t)n.push({key:a,fn:t[a]});const i=[];let o=0;function c(a){if(typeof a=="function")throw new F("Cannot stringify a function",i);if(s.has(a))return s.get(a);if(a===void 0)return lt;if(Number.isNaN(a))return fr;if(a===1/0)return dr;if(a===-1/0)return pr;if(a===0&&1/a<0)return ft;const l=o++;s.set(a,l);for(const{key:_,fn:d}of n){const p=d(a);if(p)return r[l]=`["${_}",${c(p)}]`,l}let u="";if(B(a))u=de(a);else switch(re(a)){case"Number":case"String":case"Boolean":u=`["Object",${de(a)}]`;break;case"BigInt":u=`["BigInt",${a}]`;break;case"Date":u=`["Date","${!isNaN(a.getDate())?a.toISOString():""}"]`;break;case"RegExp":const{source:p,flags:h}=a;u=h?`["RegExp",${U(p)},"${h}"]`:`["RegExp",${U(p)}]`;break;case"Array":u="[";for(let g=0;g<a.length;g+=1)g>0&&(u+=","),g in a?(i.push(`[${g}]`),u+=c(a[g]),i.pop()):u+=lr;u+="]";break;case"Set":u='["Set"';for(const g of a)u+=`,${c(g)}`;u+="]";break;case"Map":u='["Map"';for(const[g,y]of a)i.push(`.get(${B(g)?de(g):"..."})`),u+=`,${c(g)},${c(y)}`,i.pop();u+="]";break;default:if(!it(a))throw new F("Cannot stringify arbitrary non-POJOs",i);if(ct(a).length>0)throw new F("Cannot stringify POJOs with symbolic keys",i);if(Object.getPrototypeOf(a)===null){u='["null"';for(const g in a)i.push(`.${g}`),u+=`,${U(g)},${c(a[g])}`,i.pop();u+="]";}else {u="{";let g=!1;for(const y in a)g&&(u+=","),g=!0,i.push(`.${y}`),u+=`${U(y)}:${c(a[y])}`,i.pop();u+="}";}}return r[l]=u,l}const f=c(e);return f<0?`${f}`:`[${r.join(",")}]`}function de(e){const t=typeof e;return t==="string"?U(e):e instanceof String?U(e.toString()):e===void 0?lt.toString():e===0&&1/e<0?ft.toString():t==="bigint"?`["BigInt","${e}"]`:String(e)}function dt(e){return Oe(e.request.headers.get("accept")??"*/*",["application/json","text/html"])==="application/json"&&e.request.method==="POST"}async function hr(e,t,r){const s=r?.actions;if(!s){const n=new L(405,"Method Not Allowed","POST method not allowed. No actions exist for this page");return K({type:"error",error:await C(e,t,n)},{status:n.status,headers:{allow:"GET"}})}mt(s);try{const n=await _t(e,s);return n instanceof Se?K({type:"failure",status:n.status,data:Ie(n.data,e.route.id)}):K({type:"success",status:n?200:204,data:Ie(n,e.route.id)})}catch(n){const i=n;return i instanceof z?ht(i):K({type:"error",error:await C(e,t,pt(i))},{status:Z(i)})}}function pt(e){return e instanceof Se?new Error('Cannot "throw fail()". Use "return fail()"'):e}function ht(e){return K({type:"redirect",status:e.status,location:e.location})}function K(e,t){return c$1(e,t)}function mr(e){return e.request.method==="POST"}async function _r(e,t){const r=t?.actions;if(!r)return e.setHeaders({allow:"GET"}),{type:"error",error:new L(405,"Method Not Allowed","POST method not allowed. No actions exist for this page")};mt(r);try{const s=await _t(e,r);return s instanceof Se?{type:"failure",status:s.status,data:s.data}:{type:"success",status:200,data:s}}catch(s){const n=s;return n instanceof z?{type:"redirect",status:n.status,location:n.location}:{type:"error",error:pt(n)}}}function mt(e){if(e.default&&Object.keys(e).length>1)throw new Error("When using named actions, the default action cannot be used. See the docs for more info: https://kit.svelte.dev/docs/form-actions#named-actions")}async function _t(e,t){const r=new URL(e.request.url);let s="default";for(const i of r.searchParams)if(i[0].startsWith("/")){if(s=i[0].slice(1),s==="default")throw new Error('Cannot use reserved action name "default"');break}const n=t[s];if(!n)throw new L(404,"Not Found",`No action with name '${s}' found`);if(!st(e.request))throw new L(415,"Unsupported Media Type",`Form actions expect form-encoded data — received ${e.request.headers.get("content-type")}`);return n(e)}function yr(e,t){return yt(e,G,t)}function Ie(e,t){return yt(e,se,t)}function yt(e,t,r){try{return t(e)}catch(s){const n=s;if("path"in n){let i=`Data returned from action inside ${r} is not serializable: ${n.message}`;throw n.path!==""&&(i+=` (data.${n.path})`),new Error(i)}throw n}}const ze="x-sveltekit-invalidated",Ue="x-sveltekit-trailing-slash";function wr(e){if(globalThis.Buffer)return Buffer.from(e).toString("base64");const t=new Uint8Array(new Uint16Array([1]).buffer)[0]>0;return btoa(new TextDecoder(t?"utf-16le":"utf-16be").decode(new Uint16Array(new Uint8Array(e))))}async function Ae({event:e,state:t,node:r,parent:s}){if(!r?.server)return null;let n=!0;const i={dependencies:new Set,params:new Set,parent:!1,route:!1,url:!1,search_params:new Set},o=j(e.url,()=>{n&&(i.url=!0);},f=>{n&&i.search_params.add(f);});return t.prerendering&&y(o),{type:"data",data:await r.server.load?.call(null,{...e,fetch:(f,a)=>(new URL(f instanceof Request?f.url:f,e.url),e.fetch(f,a)),depends:(...f)=>{for(const a of f){const{href:l}=new URL(a,e.url);i.dependencies.add(l);}},params:new Proxy(e.params,{get:(f,a)=>(n&&i.params.add(a),f[a])}),parent:async()=>(n&&(i.parent=!0),s()),route:new Proxy(e.route,{get:(f,a)=>(n&&(i.route=!0),f[a])}),url:o,untrack(f){n=!1;try{return f()}finally{n=!0;}}})??null,uses:i,slash:r.server.trailingSlash}}async function wt({event:e,fetched:t,node:r,parent:s,server_data_promise:n,state:i,resolve_opts:o,csr:c}){const f=await n;return r?.universal?.load?await r.universal.load.call(null,{url:e.url,params:e.params,data:f?.data??null,route:e.route,fetch:gr(e,i,t,c,o),setHeaders:e.setHeaders,depends:()=>{},parent:s,untrack:l=>l()})??null:f?.data??null}function gr(e,t,r,s,n){const i=async(o,c)=>{const f=o instanceof Request&&o.body?o.clone().body:null,a=o instanceof Request&&[...o.headers].length?new Headers(o.headers):c?.headers;let l=await e.fetch(o,c);const u=new URL(o instanceof Request?o.url:o,e.url),_=u.origin===e.url.origin;let d;if(_)t.prerendering&&(d={response:l,body:null},t.prerendering.dependencies.set(u.pathname,d));else if((o instanceof Request?o.mode:c?.mode??"cors")==="no-cors")l=new Response("",{status:l.status,statusText:l.statusText,headers:l.headers});else {const g=l.headers.get("access-control-allow-origin");if(!g||g!==e.url.origin&&g!=="*")throw new Error(`CORS error: ${g?"Incorrect":"No"} 'Access-Control-Allow-Origin' header is present on the requested resource`)}const p=new Proxy(l,{get(h,g,y){async function b(m,E){const w=Number(h.status);if(isNaN(w))throw new Error(`response.status is not a number. value: "${h.status}" type: ${typeof h.status}`);r.push({url:_?u.href.slice(e.url.origin.length):u.href,method:e.request.method,request_body:o instanceof Request&&f?await br(f):c?.body,request_headers:a,response_body:m,response:h,is_b64:E});}if(g==="arrayBuffer")return async()=>{const m=await h.arrayBuffer();return d&&(d.body=new Uint8Array(m)),m instanceof ArrayBuffer&&await b(wr(m),!0),m};async function v(){const m=await h.text();return (!m||typeof m=="string")&&await b(m,!1),d&&(d.body=m),m}return g==="text"?v:g==="json"?async()=>JSON.parse(await v()):Reflect.get(h,g,h)}});if(s){const h=l.headers.get;l.headers.get=g=>{const y=g.toLowerCase(),b=h.call(l.headers,y);if(b&&!y.startsWith("x-sveltekit-")&&!n.filterSerializedResponseHeaders(y,b))throw new Error(`Failed to get response header "${y}" — it must be included by the \`filterSerializedResponseHeaders\` option: https://kit.svelte.dev/docs/hooks#server-hooks-handle (at ${e.route.id})`);return b};}return p};return (o,c)=>{const f=i(o,c);return f.catch(()=>{}),f}}async function br(e){let t="";const r=e.getReader(),s=new TextDecoder;for(;;){const{done:n,value:i}=await r.read();if(n)break;t+=s.decode(i);}return t}function gt(...e){let t=5381;for(const r of e)if(typeof r=="string"){let s=r.length;for(;s;)t=t*33^r.charCodeAt(--s);}else if(ArrayBuffer.isView(r)){const s=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);let n=s.length;for(;n;)t=t*33^s[--n];}else throw new TypeError("value must be a string or TypedArray");return (t>>>0).toString(36)}const bt={"&":"&amp;",'"':"&quot;"},$r=new RegExp(`[${Object.keys(bt).join("")}]|[\\ud800-\\udbff](?![\\udc00-\\udfff])|[\\ud800-\\udbff][\\udc00-\\udfff]|[\\udc00-\\udfff]`,"g");function $t(e){return `"${e.replace($r,r=>r.length===2?r:bt[r]??`&#${r.charCodeAt(0)};`)}"`}const vt={"<":"\\u003C","\u2028":"\\u2028","\u2029":"\\u2029"},vr=new RegExp(`[${Object.keys(vt).join("")}]`,"g");function kr(e,t,r=!1){const s={};let n=null,i=null,o=!1;for(const[l,u]of e.response.headers)t(l,u)&&(s[l]=u),l==="cache-control"?n=u:l==="age"?i=u:l==="vary"&&u.trim()==="*"&&(o=!0);const c={status:e.response.status,statusText:e.response.statusText,headers:s,body:e.response_body},f=JSON.stringify(c).replace(vr,l=>vt[l]),a=['type="application/json"',"data-sveltekit-fetched",`data-url=${$t(e.url)}`];if(e.is_b64&&a.push("data-b64"),e.request_headers||e.request_body){const l=[];e.request_headers&&l.push([...new Headers(e.request_headers)].join(",")),e.request_body&&l.push(e.request_body),a.push(`data-hash="${gt(...l)}"`);}if(!r&&e.method==="GET"&&n&&!o){const l=/s-maxage=(\d+)/g.exec(n)??/max-age=(\d+)/g.exec(n);if(l){const u=+l[1]-+(i??"0");a.push(`data-ttl="${u}"`);}}return `<script ${a.join(" ")}>${f}<\/script>`}const q=JSON.stringify,Er=new TextEncoder;function Le(e){we[0]||xr();const t=kt.slice(0),r=jr(e);for(let n=0;n<r.length;n+=16){const i=r.subarray(n,n+16);let o,c,f,a=t[0],l=t[1],u=t[2],_=t[3],d=t[4],p=t[5],h=t[6],g=t[7];for(let y=0;y<64;y++)y<16?o=i[y]:(c=i[y+1&15],f=i[y+14&15],o=i[y&15]=(c>>>7^c>>>18^c>>>3^c<<25^c<<14)+(f>>>17^f>>>19^f>>>10^f<<15^f<<13)+i[y&15]+i[y+9&15]|0),o=o+g+(d>>>6^d>>>11^d>>>25^d<<26^d<<21^d<<7)+(h^d&(p^h))+we[y],g=h,h=p,p=d,d=_+o|0,_=u,u=l,l=a,a=o+(l&u^_&(l^u))+(l>>>2^l>>>13^l>>>22^l<<30^l<<19^l<<10)|0;t[0]=t[0]+a|0,t[1]=t[1]+l|0,t[2]=t[2]+u|0,t[3]=t[3]+_|0,t[4]=t[4]+d|0,t[5]=t[5]+p|0,t[6]=t[6]+h|0,t[7]=t[7]+g|0;}const s=new Uint8Array(t.buffer);return Et(s),xt(s)}const kt=new Uint32Array(8),we=new Uint32Array(64);function xr(){function e(r){return (r-Math.floor(r))*4294967296}let t=2;for(let r=0;r<64;t++){let s=!0;for(let n=2;n*n<=t;n++)if(t%n===0){s=!1;break}s&&(r<8&&(kt[r]=e(t**(1/2))),we[r]=e(t**(1/3)),r++);}}function Et(e){for(let t=0;t<e.length;t+=4){const r=e[t+0],s=e[t+1],n=e[t+2],i=e[t+3];e[t+0]=i,e[t+1]=n,e[t+2]=s,e[t+3]=r;}}function jr(e){const t=Er.encode(e),r=t.length*8,s=512*Math.ceil((r+65)/512),n=new Uint8Array(s/8);n.set(t),n[t.length]=128,Et(n);const i=new Uint32Array(n.buffer);return i[i.length-2]=Math.floor(r/4294967296),i[i.length-1]=r,i}const I="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");function xt(e){const t=e.length;let r="",s;for(s=2;s<t;s+=3)r+=I[e[s-2]>>2],r+=I[(e[s-2]&3)<<4|e[s-1]>>4],r+=I[(e[s-1]&15)<<2|e[s]>>6],r+=I[e[s]&63];return s===t+1&&(r+=I[e[s-2]>>2],r+=I[(e[s-2]&3)<<4],r+="=="),s===t&&(r+=I[e[s-2]>>2],r+=I[(e[s-2]&3)<<4|e[s-1]>>4],r+=I[(e[s-1]&15)<<2],r+="="),r}const Me=new Uint8Array(16);function Or(){return crypto.getRandomValues(Me),xt(Me)}const Sr=new Set(["self","unsafe-eval","unsafe-hashes","unsafe-inline","none","strict-dynamic","report-sample","wasm-unsafe-eval","script"]),Ar=/^(nonce|sha\d\d\d)-/;class jt{#e;#r;#u;#o;#s;#i;#n;#c;#t;#a;constructor(t,r,s){this.#e=t,this.#o=r;const n=this.#o;this.#s=[],this.#i=[],this.#n=[],this.#c=[],this.#t=[];const i=n["script-src"]||n["default-src"],o=n["script-src-elem"],c=n["style-src"]||n["default-src"],f=n["style-src-attr"],a=n["style-src-elem"];this.#r=!!i&&i.filter(l=>l!=="unsafe-inline").length>0||!!o&&o.filter(l=>l!=="unsafe-inline").length>0,this.#u=!!c&&c.filter(l=>l!=="unsafe-inline").length>0||!!f&&f.filter(l=>l!=="unsafe-inline").length>0||!!a&&a.filter(l=>l!=="unsafe-inline").length>0,this.script_needs_nonce=this.#r&&!this.#e,this.style_needs_nonce=this.#u&&!this.#e,this.#a=s;}add_script(t){if(this.#r){const r=this.#o;if(this.#e){const s=Le(t);this.#s.push(`sha256-${s}`),r["script-src-elem"]?.length&&this.#i.push(`sha256-${s}`);}else this.#s.length===0&&this.#s.push(`nonce-${this.#a}`),r["script-src-elem"]?.length&&this.#i.push(`nonce-${this.#a}`);}}add_style(t){if(this.#u){const r="9OlNO0DNEeaVzHL4RZwCLsBHA8WBQ8toBp/4F5XV2nc=",s=this.#o;if(this.#e){const n=Le(t);this.#n.push(`sha256-${n}`),s["style-src-attr"]?.length&&this.#c.push(`sha256-${n}`),s["style-src-elem"]?.length&&(n!==r&&!s["style-src-elem"].includes(`sha256-${r}`)&&this.#t.push(`sha256-${r}`),this.#t.push(`sha256-${n}`));}else this.#n.length===0&&!s["style-src"]?.includes("unsafe-inline")&&this.#n.push(`nonce-${this.#a}`),s["style-src-attr"]?.length&&this.#c.push(`nonce-${this.#a}`),s["style-src-elem"]?.length&&(s["style-src-elem"].includes(`sha256-${r}`)||this.#t.push(`sha256-${r}`),this.#t.push(`nonce-${this.#a}`));}}get_header(t=!1){const r=[],s={...this.#o};this.#n.length>0&&(s["style-src"]=[...s["style-src"]||s["default-src"]||[],...this.#n]),this.#c.length>0&&(s["style-src-attr"]=[...s["style-src-attr"]||[],...this.#c]),this.#t.length>0&&(s["style-src-elem"]=[...s["style-src-elem"]||[],...this.#t]),this.#s.length>0&&(s["script-src"]=[...s["script-src"]||s["default-src"]||[],...this.#s]),this.#i.length>0&&(s["script-src-elem"]=[...s["script-src-elem"]||[],...this.#i]);for(const n in s){if(t&&(n==="frame-ancestors"||n==="report-uri"||n==="sandbox"))continue;const i=s[n];if(!i)continue;const o=[n];Array.isArray(i)&&i.forEach(c=>{Sr.has(c)||Ar.test(c)?o.push(`'${c}'`):o.push(c);}),r.push(o.join(" "));}return r.join("; ")}}class Rr extends jt{get_meta(){const t=this.get_header(!0);if(t)return `<meta http-equiv="content-security-policy" content=${$t(t)}>`}}class Tr extends jt{constructor(t,r,s){if(super(t,r,s),Object.values(r).filter(n=>!!n).length>0){const n=r["report-to"]?.length??!1,i=r["report-uri"]?.length??!1;if(!n&&!i)throw Error("`content-security-policy-report-only` must be specified with either the `report-to` or `report-uri` directives, or both")}}}class Pr{nonce=Or();csp_provider;report_only_provider;constructor({mode:t,directives:r,reportOnly:s},{prerender:n}){const i=t==="hash"||t==="auto"&&n;this.csp_provider=new Rr(i,r,this.nonce),this.report_only_provider=new Tr(i,s,this.nonce);}get script_needs_nonce(){return this.csp_provider.script_needs_nonce||this.report_only_provider.script_needs_nonce}get style_needs_nonce(){return this.csp_provider.style_needs_nonce||this.report_only_provider.style_needs_nonce}add_script(t){this.csp_provider.add_script(t),this.report_only_provider.add_script(t);}add_style(t){this.csp_provider.add_style(t),this.report_only_provider.add_style(t);}}function We(){let e,t;return {promise:new Promise((s,n)=>{e=s,t=n;}),fulfil:e,reject:t}}function Ot(){const e=[We()];return {iterator:{[Symbol.asyncIterator](){return {next:async()=>{const t=await e[0].promise;return t.done||e.shift(),t}}}},push:t=>{e[e.length-1].fulfil({value:t,done:!1}),e.push(We());},done:()=>{e[e.length-1].fulfil({done:!0});}}}const Cr={...readable(!1),check:()=>!1},Je=new TextEncoder;async function X({branch:e,fetched:t,options:r$1,manifest:s,state:n,page_config:i,status:o,error:c$1=null,event:f,resolve_opts:a,action_result:l}){if(n.prerendering){if(r$1.csp.mode==="nonce")throw new Error('Cannot use prerendering if config.kit.csp.mode === "nonce"');if(r$1.app_template_contains_nonce)throw new Error("Cannot use prerendering if page template contains %sveltekit.nonce%")}const{client:u}=s._,_=new Set(u.imports),d=new Set(u.stylesheets),p=new Set(u.fonts),h=new Set,g=new Map;let y;const b=l?.type==="success"||l?.type==="failure"?l.data??null:null;let v=c,m$1=m,E=q(c);if(n.prerendering?.fallback||(v=f.url.pathname.slice(c.length).split("/").slice(2).map(()=>"..").join("/")||".",E=`new URL(${q(v)}, location).pathname.slice(0, -1)`,(!m||m[0]==="/"&&m!==Ft)&&(m$1=v)),i.ssr){const x={stores:{page:writable(null),navigating:writable(null),updated:Cr},constructors:await Promise.all(e.map(({node:S})=>S.component())),form:b};let j={};for(let S=0;S<e.length;S+=1)j={...j,...e[S].data},x[`data_${S}`]=j;x.page={error:c$1,params:f.params,route:f.route,status:o,url:f.url,data:j,form:b,state:{}},q$1({base:v,assets:m$1});try{y=r$1.root.render(x);}finally{B$1();}for(const{node:S}of e){for(const A of S.imports)_.add(A);for(const A of S.stylesheets)d.add(A);for(const A of S.fonts)p.add(A);S.inline_styles&&Object.entries(await S.inline_styles()).forEach(([A,Y])=>g.set(A,Y));}}else y={head:"",html:"",css:{code:"",map:null}};let w="",k=y.html;const $=new Pr(r$1.csp,{prerender:!!n.prerendering}),O=x=>x.startsWith("/")?c+x:`${m$1}/${x}`;if(g.size>0){const x=Array.from(g.values()).join(`
`),j=[];$.style_needs_nonce&&j.push(` nonce="${$.nonce}"`),$.add_style(x),w+=`
	<style${j.join("")}>${x}</style>`;}for(const x of d){const j=O(x),S=['rel="stylesheet"'];if(g.has(x))S.push("disabled",'media="(max-width: 0)"');else if(a.preload({type:"css",path:j})){const A=['rel="preload"','as="style"'];h.add(`<${encodeURI(j)}>; ${A.join(";")}; nopush`);}w+=`
		<link href="${j}" ${S.join(" ")}>`;}for(const x of p){const j=O(x);if(a.preload({type:"font",path:j})){const A=['rel="preload"','as="font"',`type="font/${x.slice(x.lastIndexOf(".")+1)}"`,`href="${j}"`,"crossorigin"];w+=`
		<link ${A.join(" ")}>`;}}const R=`__sveltekit_${r$1.version_hash}`,{data:J,chunks:N}=Nr(f,r$1,e.map(x=>x.server_data),R);if(i.ssr&&i.csr&&(k+=`
			${t.map(x=>kr(x,a.filterSerializedResponseHeaders,!!n.prerendering)).join(`
			`)}`),i.csr){u.uses_env_dynamic_public&&n.prerendering&&_.add(`${r$1.app_dir}/env.js`);const x=Array.from(_,P=>O(P)).filter(P=>a.preload({type:"js",path:P}));for(const P of x)h.add(`<${encodeURI(P)}>; rel="modulepreload"; nopush`),r$1.preload_strategy!=="modulepreload"?w+=`
		<link rel="preload" as="script" crossorigin="anonymous" href="${P}">`:n.prerendering&&(w+=`
		<link rel="modulepreload" href="${P}">`);const j=[],S=u.uses_env_dynamic_public&&n.prerendering,A=[`base: ${E}`];m&&A.push(`assets: ${q(m)}`),u.uses_env_dynamic_public&&A.push(`env: ${S?"null":q(G$1)}`),N&&(j.push("const deferred = new Map();"),A.push(`defer: (id) => new Promise((fulfil, reject) => {
							deferred.set(id, { fulfil, reject });
						})`),A.push(`resolve: ({ id, data, error }) => {
							const { fulfil, reject } = deferred.get(id);
							deferred.delete(id);

							if (error) reject(error);
							else fulfil(data);
						}`)),j.push(`${R} = {
						${A.join(`,
						`)}
					};`);const Y=["app","element"];if(j.push("const element = document.currentScript.parentElement;"),i.ssr){const P={form:"null",error:"null"};j.push(`const data = ${J};`),b&&(P.form=yr(b,f.route.id)),c$1&&(P.error=G(c$1));const ce=[`node_ids: [${e.map(({node:Ct})=>Ct.index).join(", ")}]`,"data",`form: ${P.form}`,`error: ${P.error}`];o!==200&&ce.push(`status: ${o}`),r$1.embedded&&ce.push(`params: ${G(f.params)}`,`route: ${q(f.route)}`);const ue="	".repeat(S?7:6);Y.push(`{
${ue}	${ce.join(`,
${ue}	`)}
${ue}}`);}S?j.push(`import(${q(`${v}/${r$1.app_dir}/env.js`)}).then(({ env }) => {
						${R}.env = env;

						Promise.all([
							import(${q(O(u.start))}),
							import(${q(O(u.app))})
						]).then(([kit, app]) => {
							kit.start(${Y.join(", ")});
						});
					});`):j.push(`Promise.all([
						import(${q(O(u.start))}),
						import(${q(O(u.app))})
					]).then(([kit, app]) => {
						kit.start(${Y.join(", ")});
					});`),r$1.service_worker&&j.push(`if ('serviceWorker' in navigator) {
						addEventListener('load', function () {
							navigator.serviceWorker.register('${O("service-worker.js")}');
						});
					}`);const Re=`
				{
					${j.join(`

					`)}
				}
			`;$.add_script(Re),k+=`
			<script${$.script_needs_nonce?` nonce="${$.nonce}"`:""}>${Re}<\/script>
		`;}const M$1=new Headers({"x-sveltekit-page":"true","content-type":"text/html"});if(n.prerendering){const x=[],j=$.csp_provider.get_meta();j&&x.push(j),n.prerendering.cache&&x.push(`<meta http-equiv="cache-control" content="${n.prerendering.cache}">`),x.length>0&&(w=x.join(`
`)+w);}else {const x=$.csp_provider.get_header();x&&M$1.set("content-security-policy",x);const j=$.report_only_provider.get_header();j&&M$1.set("content-security-policy-report-only",j),h.size&&M$1.set("link",Array.from(h).join(", "));}w+=y.head;const Pt=r$1.templates.app({head:w,body:k,assets:m$1,nonce:$.nonce,env:M}),ie=await a.transformPageChunk({html:Pt,done:!0})||"";return N||M$1.set("etag",`"${gt(ie)}"`),N?new Response(new ReadableStream({async start(x){x.enqueue(Je.encode(ie+`
`));for await(const j of N)x.enqueue(Je.encode(j));x.close();},type:"bytes"}),{headers:{"content-type":"text/html"}}):r(ie,{status:o,headers:M$1})}function Nr(e,t,r,s){let n=1,i=0;const{iterator:o,push:c,done:f}=Ot();function a(l){if(typeof l?.then=="function"){const u=n++;return i+=1,l.then(_=>({data:_})).catch(async _=>({error:await C(e,t,_)})).then(async({data:_,error:d})=>{i-=1;let p;try{p=G({id:u,data:_,error:d},a);}catch{d=await C(e,t,new Error(`Failed to serialize promise while rendering ${e.route.id}`)),_=void 0,p=G({id:u,data:_,error:d},a);}c(`<script>${s}.resolve(${p})<\/script>
`),i===0&&f();}),`${s}.defer(${u})`}}try{return {data:`[${r.map(u=>u?`{"type":"data","data":${G(u.data,a)},${ot(u)}${u.slash?`,"slash":${JSON.stringify(u.slash)}`:""}}`:"null").join(",")}]`,chunks:i>0?o:null}}catch(l){throw new Error(at(e,l))}}function D(e,t){return e.reduce((r,s)=>s?.universal?.[t]??s?.server?.[t]??r,void 0)}async function St({event:e,options:t,manifest:r,state:s,status:n,error:i,resolve_opts:o}){if(e.request.headers.get("x-sveltekit-error"))return ne(t,n,i.message);const c=[];try{const f=[],a=await r._.nodes[0](),l=D([a],"ssr")??!0,u=D([a],"csr")??!0;if(l){s.error=!0;const _=Ae({event:e,state:s,node:a,parent:async()=>({})}),d=await _,p=await wt({event:e,fetched:c,node:a,parent:async()=>({}),resolve_opts:o,server_data_promise:_,state:s,csr:u});f.push({node:a,server_data:d,data:p},{node:await r._.nodes[1](),data:null,server_data:null});}return await X({options:t,manifest:r,state:s,page_config:{ssr:l,csr:u},status:n,error:await C(e,t,i),branch:f,fetched:c,event:e,resolve_opts:o})}catch(f){return f instanceof z?ae(f.status,f.location):ne(t,Z(f),(await C(e,t,f)).message)}}function qr(e){let t=!1,r;return ()=>t?r:(t=!0,r=e())}const Be=new TextEncoder;async function Dr(e,t,r,s,n,i,o){if(!t.page)return new Response(void 0,{status:404});try{const c=[...t.page.layouts,t.page.leaf],f=i??c.map(()=>!0);let a=!1;const l=new URL(e.url);l.pathname=b(l.pathname,o);const u={...e,url:l},_=c.map((b,v)=>qr(async()=>{try{if(a)return {type:"skip"};const m=b==null?b:await s._.nodes[b]();return Ae({event:u,state:n,node:m,parent:async()=>{const E={};for(let w=0;w<v;w+=1){const k=await _[w]();k&&Object.assign(E,k.data);}return E}})}catch(m){throw a=!0,m}})),d=_.map(async(b,v)=>f[v]?b():{type:"skip"});let p=d.length;const h=await Promise.all(d.map((b,v)=>b.catch(async m=>{if(m instanceof z)throw m;return p=Math.min(p,v+1),{type:"error",error:await C(e,r,m),status:m instanceof Q||m instanceof L?m.status:void 0}}))),{data:g,chunks:y}=At(e,r,h);return y?new Response(new ReadableStream({async start(b){b.enqueue(Be.encode(g));for await(const v of y)b.enqueue(Be.encode(v));b.close();},type:"bytes"}),{headers:{"content-type":"text/sveltekit-data","cache-control":"private, no-store"}}):ge(g)}catch(c){const f=c;return f instanceof z?be(f):ge(await C(e,r,f),500)}}function ge(e,t=200){return r(typeof e=="string"?e:JSON.stringify(e),{status:t,headers:{"content-type":"application/json","cache-control":"private, no-store"}})}function be(e){return ge({type:"redirect",location:e.location})}function At(e,t,r){let s=1,n=0;const{iterator:i,push:o,done:c}=Ot(),f={Promise:a=>{if(typeof a?.then=="function"){const l=s++;n+=1;let u="data";return a.catch(async _=>(u="error",C(e,t,_))).then(async _=>{let d;try{d=se(_,f);}catch{const p=await C(e,t,new Error(`Failed to serialize promise while rendering ${e.route.id}`));u="error",d=se(p,f);}n-=1,o(`{"type":"chunk","id":${l},"${u}":${d}}
`),n===0&&c();}),l}}};try{return {data:`{"type":"data","nodes":[${r.map(l=>l?l.type==="error"||l.type==="skip"?JSON.stringify(l):`{"type":"data","data":${se(l.data,f)},${ot(l)}${l.slash?`,"slash":${JSON.stringify(l.slash)}`:""}}`:"null").join(",")}]}
`,chunks:n>0?i:null}}catch(a){throw new Error(at(e,a))}}function $e(e,t){return Promise.all([...e.layouts.map(r=>r==null?r:t._.nodes[r]()),t._.nodes[e.leaf]()])}const Hr=10;async function Ir(e,t,r$1,s,n,i){if(n.depth>Hr)return r(`Not found: ${e.url.pathname}`,{status:404});if(dt(e)){const o=await s._.nodes[t.leaf]();return hr(e,r$1,o?.server)}try{const o=await $e(t,s),c=o.at(-1);let f=200,a;if(mr(e)){if(a=await _r(e,c.server),a?.type==="redirect")return ae(a.status,a.location);a?.type==="error"&&(f=Z(a.error)),a?.type==="failure"&&(f=a.status);}const l=o.some(m=>m?.server?.load),u=O(e.url.pathname),_=D(o,"prerender")??!1;if(_){if(c.server?.actions)throw new Error("Cannot prerender pages with actions")}else if(n.prerendering)return new Response(void 0,{status:204});n.prerender_default=_;const d=[];if(D(o,"ssr")===!1&&!(n.prerendering&&l))return await X({branch:[],fetched:d,page_config:{ssr:!1,csr:D(o,"csr")??!0},status:f,error:null,event:e,options:r$1,manifest:s,state:n,resolve_opts:i});const p=[];let h=null;const g=o.map((m,E)=>{if(h)throw h;return Promise.resolve().then(async()=>{try{if(m===c&&a?.type==="error")throw a.error;return await Ae({event:e,state:n,node:m,parent:async()=>{const w={};for(let k=0;k<E;k+=1){const $=await g[k];$&&Object.assign(w,$.data);}return w}})}catch(w){throw h=w,h}})}),y=D(o,"csr")??!0,b=o.map((m,E)=>{if(h)throw h;return Promise.resolve().then(async()=>{try{return await wt({event:e,fetched:d,node:m,parent:async()=>{const w={};for(let k=0;k<E;k+=1)Object.assign(w,await b[k]);return w},resolve_opts:i,server_data_promise:g[E],state:n,csr:y})}catch(w){throw h=w,h}})});for(const m of g)m.catch(()=>{});for(const m of b)m.catch(()=>{});for(let m=0;m<o.length;m+=1){const E=o[m];if(E)try{const w=await g[m],k=await b[m];p.push({node:E,server_data:w,data:k});}catch(w){const k=w;if(k instanceof z){if(n.prerendering&&l){const R=JSON.stringify({type:"redirect",location:k.location});n.prerendering.dependencies.set(u,{response:r(R),body:R});}return ae(k.status,k.location)}const $=Z(k),O=await C(e,r$1,k);for(;m--;)if(t.errors[m]){const R=t.errors[m],J=await s._.nodes[R]();let N=m;for(;!p[N];)N-=1;return await X({event:e,options:r$1,manifest:s,state:n,resolve_opts:i,page_config:{ssr:!0,csr:!0},status:$,error:O,branch:He(p.slice(0,N+1)).concat({node:J,data:null,server_data:null}),fetched:d})}return ne(r$1,$,O.message)}else p.push(null);}if(n.prerendering&&l){let{data:m,chunks:E}=At(e,r$1,p.map(w=>w?.server_data));if(E)for await(const w of E)m+=w;n.prerendering.dependencies.set(u,{response:r(m),body:m});}const v=D(o,"ssr")??!0;return await X({event:e,options:r$1,manifest:s,state:n,resolve_opts:i,page_config:{csr:D(o,"csr")??!0,ssr:v},status:f,error:null,branch:v===!1?[]:He(p),action_result:a,fetched:d})}catch(o){return await St({event:e,options:r$1,manifest:s,state:n,status:500,error:o,resolve_opts:i})}}function zr(e,t,r){const s={},n=e.slice(1),i=n.filter(c=>c!==void 0);let o=0;for(let c=0;c<t.length;c+=1){const f=t[c];let a=n[c-o];if(f.chained&&f.rest&&o&&(a=n.slice(c-o,c+1).filter(l=>l).join("/"),o=0),a===void 0){f.rest&&(s[f.name]="");continue}if(!f.matcher||r[f.matcher](a)){s[f.name]=a;const l=t[c+1],u=n[c+1];l&&!l.rest&&l.optional&&u&&f.chained&&(o=0),!l&&!u&&Object.keys(s).length===i.length&&(o=0);continue}if(f.optional&&f.chained){o++;continue}return}if(!o)return s}/*!
 * cookie
 * Copyright(c) 2012-2014 Roman Shtylman
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */var ee=Lr,ve=Mr,Ur=Object.prototype.toString,te=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function Lr(e,t){if(typeof e!="string")throw new TypeError("argument str must be a string");for(var r={},s=t||{},n=s.decode||Wr,i=0;i<e.length;){var o=e.indexOf("=",i);if(o===-1)break;var c=e.indexOf(";",i);if(c===-1)c=e.length;else if(c<o){i=e.lastIndexOf(";",o-1)+1;continue}var f=e.slice(i,o).trim();if(r[f]===void 0){var a=e.slice(o+1,c).trim();a.charCodeAt(0)===34&&(a=a.slice(1,-1)),r[f]=Vr(a,n);}i=c+1;}return r}function Mr(e,t,r){var s=r||{},n=s.encode||Jr;if(typeof n!="function")throw new TypeError("option encode is invalid");if(!te.test(e))throw new TypeError("argument name is invalid");var i=n(t);if(i&&!te.test(i))throw new TypeError("argument val is invalid");var o=e+"="+i;if(s.maxAge!=null){var c=s.maxAge-0;if(isNaN(c)||!isFinite(c))throw new TypeError("option maxAge is invalid");o+="; Max-Age="+Math.floor(c);}if(s.domain){if(!te.test(s.domain))throw new TypeError("option domain is invalid");o+="; Domain="+s.domain;}if(s.path){if(!te.test(s.path))throw new TypeError("option path is invalid");o+="; Path="+s.path;}if(s.expires){var f=s.expires;if(!Br(f)||isNaN(f.valueOf()))throw new TypeError("option expires is invalid");o+="; Expires="+f.toUTCString();}if(s.httpOnly&&(o+="; HttpOnly"),s.secure&&(o+="; Secure"),s.partitioned&&(o+="; Partitioned"),s.priority){var a=typeof s.priority=="string"?s.priority.toLowerCase():s.priority;switch(a){case"low":o+="; Priority=Low";break;case"medium":o+="; Priority=Medium";break;case"high":o+="; Priority=High";break;default:throw new TypeError("option priority is invalid")}}if(s.sameSite){var l=typeof s.sameSite=="string"?s.sameSite.toLowerCase():s.sameSite;switch(l){case!0:o+="; SameSite=Strict";break;case"lax":o+="; SameSite=Lax";break;case"strict":o+="; SameSite=Strict";break;case"none":o+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}}return o}function Wr(e){return e.indexOf("%")!==-1?decodeURIComponent(e):e}function Jr(e){return encodeURIComponent(e)}function Br(e){return Ur.call(e)==="[object Date]"||e instanceof Date}function Vr(e,t){try{return t(e)}catch{return e}}function pe(e){if(e?.path===void 0)throw new Error("You must specify a `path` when setting, deleting or serializing cookies")}function Fr(e,t,r){const s=e.headers.get("cookie")??"",n=ee(s,{decode:u=>u}),i=b(t.pathname,r),o={},c={httpOnly:!0,sameSite:"lax",secure:!(t.hostname==="localhost"&&t.protocol==="http:")},f={get(u,_){const d=o[u];if(d&&he(t.hostname,d.options.domain)&&me(t.pathname,d.options.path))return d.value;const p=_?.decode||decodeURIComponent;return ee(s,{decode:p})[u]},getAll(u){const _=u?.decode||decodeURIComponent,d=ee(s,{decode:_});for(const p of Object.values(o))he(t.hostname,p.options.domain)&&me(t.pathname,p.options.path)&&(d[p.name]=p.value);return Object.entries(d).map(([p,h])=>({name:p,value:h}))},set(u,_,d){pe(d),l(u,_,{...c,...d});},delete(u,_){pe(_),f.set(u,"",{..._,maxAge:0});},serialize(u,_,d){pe(d);let p=d.path;return (!d.domain||d.domain===t.hostname)&&(p=S(i,p)),ve(u,_,{...c,...d,path:p})}};function a(u,_){const d={...n};for(const p in o){const h=o[p];if(!he(u.hostname,h.options.domain)||!me(u.pathname,h.options.path))continue;const g=h.options.encode||encodeURIComponent;d[h.name]=g(h.value);}if(_){const p=ee(_,{decode:h=>h});for(const h in p)d[h]=p[h];}return Object.entries(d).map(([p,h])=>`${p}=${h}`).join("; ")}function l(u,_,d){let p=d.path;(!d.domain||d.domain===t.hostname)&&(p=S(i,p)),o[u]={name:u,value:_,options:{...d,path:p}};}return {cookies:f,new_cookies:o,get_cookie_header:a,set_internal:l}}function he(e,t){if(!t)return !0;const r=t[0]==="."?t.slice(1):t;return e===r?!0:e.endsWith("."+r)}function me(e,t){if(!t)return !0;const r=t.endsWith("/")?t.slice(0,-1):t;return e===r?!0:e.startsWith(r+"/")}function Ve(e,t){for(const r of t){const{name:s,value:n,options:i}=r;if(e.append("set-cookie",ve(s,n,i)),i.path.endsWith(".html")){const o=O(i.path);e.append("set-cookie",ve(s,n,{...i,path:o}));}}}var oe={exports:{}},V={decodeValues:!0,map:!1,silent:!1};function ke(e){return typeof e=="string"&&!!e.trim()}function Ee(e,t){var r=e.split(";").filter(ke),s=r.shift(),n=Gr(s),i=n.name,o=n.value;t=t?Object.assign({},V,t):V;try{o=t.decodeValues?decodeURIComponent(o):o;}catch(f){console.error("set-cookie-parser encountered an error while decoding a cookie with value '"+o+"'. Set options.decodeValues to false to disable this feature.",f);}var c={name:i,value:o};return r.forEach(function(f){var a=f.split("="),l=a.shift().trimLeft().toLowerCase(),u=a.join("=");l==="expires"?c.expires=new Date(u):l==="max-age"?c.maxAge=parseInt(u,10):l==="secure"?c.secure=!0:l==="httponly"?c.httpOnly=!0:l==="samesite"?c.sameSite=u:c[l]=u;}),c}function Gr(e){var t="",r="",s=e.split("=");return s.length>1?(t=s.shift(),r=s.join("=")):r=e,{name:t,value:r}}function Rt(e,t){if(t=t?Object.assign({},V,t):V,!e)return t.map?{}:[];if(e.headers)if(typeof e.headers.getSetCookie=="function")e=e.headers.getSetCookie();else if(e.headers["set-cookie"])e=e.headers["set-cookie"];else {var r=e.headers[Object.keys(e.headers).find(function(n){return n.toLowerCase()==="set-cookie"})];!r&&e.headers.cookie&&!t.silent&&console.warn("Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning."),e=r;}if(Array.isArray(e)||(e=[e]),t=t?Object.assign({},V,t):V,t.map){var s={};return e.filter(ke).reduce(function(n,i){var o=Ee(i,t);return n[o.name]=o,n},s)}else return e.filter(ke).map(function(n){return Ee(n,t)})}function Zr(e){if(Array.isArray(e))return e;if(typeof e!="string")return [];var t=[],r=0,s,n,i,o,c;function f(){for(;r<e.length&&/\s/.test(e.charAt(r));)r+=1;return r<e.length}function a(){return n=e.charAt(r),n!=="="&&n!==";"&&n!==","}for(;r<e.length;){for(s=r,c=!1;f();)if(n=e.charAt(r),n===","){for(i=r,r+=1,f(),o=r;r<e.length&&a();)r+=1;r<e.length&&e.charAt(r)==="="?(c=!0,r=o,t.push(e.substring(s,i)),s=r):r=i+1;}else r+=1;(!c||r>=e.length)&&t.push(e.substring(s,e.length));}return t}oe.exports=Rt;oe.exports.parse=Rt;var Yr=oe.exports.parseString=Ee,Kr=oe.exports.splitCookiesString=Zr;function Xr({event:e,options:t,manifest:r,state:s,get_cookie_header:n,set_internal:i}){const o=async(c$1,f)=>{const a=Fe(c$1,f,e.url);let l=(c$1 instanceof Request?c$1.mode:f?.mode)??"cors",u=(c$1 instanceof Request?c$1.credentials:f?.credentials)??"same-origin";return t.hooks.handleFetch({event:e,request:a,fetch:async(_,d)=>{const p=Fe(_,d,e.url),h=new URL(p.url);if(p.headers.has("origin")||p.headers.set("origin",e.url.origin),_!==a&&(l=(_ instanceof Request?_.mode:d?.mode)??"cors",u=(_ instanceof Request?_.credentials:d?.credentials)??"same-origin"),(p.method==="GET"||p.method==="HEAD")&&(l==="no-cors"&&h.origin!==e.url.origin||h.origin===e.url.origin)&&p.headers.delete("origin"),h.origin!==e.url.origin){if(`.${h.hostname}`.endsWith(`.${e.url.hostname}`)&&u!=="omit"){const $=n(h,p.headers.get("cookie"));$&&p.headers.set("cookie",$);}return fetch(p)}const g=m||c,y=decodeURIComponent(h.pathname),b=(y.startsWith(g)?y.slice(g.length):y).slice(1),v=`${b}/index.html`,m$1=r.assets.has(b),E=r.assets.has(v);if(m$1||E){const $=m$1?b:v;if(s.read){const O=m$1?r.mimeTypes[b.slice(b.lastIndexOf("."))]:"text/html";return new Response(s.read($),{headers:O?{"content-type":O}:{}})}return await fetch(p)}if(u!=="omit"){const $=n(h,p.headers.get("cookie"));$&&p.headers.set("cookie",$);const O=e.request.headers.get("authorization");O&&!p.headers.has("authorization")&&p.headers.set("authorization",O);}p.headers.has("accept")||p.headers.set("accept","*/*"),p.headers.has("accept-language")||p.headers.set("accept-language",e.request.headers.get("accept-language"));const w=await Tt(p,t,r,{...s,depth:s.depth+1}),k=w.headers.get("set-cookie");if(k)for(const $ of Kr(k)){const{name:O,value:R,...J}=Yr($,{decodeValues:!1}),N=J.path??(h.pathname.split("/").slice(0,-1).join("/")||"/");i(O,R,{path:N,encode:M=>M,...J});}return w}})};return (c,f)=>{const a=o(c,f);return a.catch(()=>{}),a}}function Fe(e,t,r){return e instanceof Request?e:new Request(typeof e=="string"?new URL(e,r):e,t)}let Ge,_e,ye;function Qr(e){return Ge??=`export const env=${JSON.stringify(G$1)}`,_e??=`W/${Date.now()}`,ye??=new Headers({"content-type":"application/javascript; charset=utf-8",etag:_e}),e.headers.get("if-none-match")===_e?new Response(void 0,{status:304,headers:ye}):new Response(Ge,{headers:ye})}function es(e){let t={};for(const r of e)!r?.universal?.config&&!r?.server?.config||(t={...t,...r?.universal?.config,...r?.server?.config});return Object.keys(t).length?t:void 0}const Ze=({html:e})=>e,Ye=()=>!1,Ke=({type:e})=>e==="js"||e==="css",ts=new Set(["GET","HEAD","POST"]),rs=new Set(["GET","HEAD","OPTIONS"]);async function Tt(e,t,r$1,s){const n=new URL(e.url);if(t.csrf_check_origin&&st(e)&&(e.method==="POST"||e.method==="PUT"||e.method==="PATCH"||e.method==="DELETE")&&e.headers.get("origin")!==n.origin){const b=new Q(403,`Cross-site ${e.method} form submissions are forbidden`);return e.headers.get("accept")==="application/json"?c$1(b.body,{status:b.status}):r(b.body.message,{status:b.status})}let i;try{i=t.hooks.reroute({url:new URL(n)})??n.pathname;}catch{return r("Internal Server Error",{status:500})}let o;try{o=m$1(i);}catch{return r("Malformed URI",{status:400})}let c$2=null,f={};if(c&&!s.prerendering?.fallback){if(!o.startsWith(c))return r("Not found",{status:404});o=o.slice(c.length)||"/";}if(o===`/${t.app_dir}/env.js`)return Qr(e);if(o.startsWith(`/${t.app_dir}`)){const y=new Headers;return y.set("cache-control","public, max-age=0, must-revalidate"),r("Not found",{status:404,headers:y})}const a=U$2(o);let l;if(a&&(o=T(o)||"/",n.pathname=T(n.pathname)+(n.searchParams.get(Ue)==="1"?"/":"")||"/",n.searchParams.delete(Ue),l=n.searchParams.get(ze)?.split("").map(y=>y==="1"),n.searchParams.delete(ze)),!s.prerendering?.fallback){const y=await r$1._.matchers();for(const b of r$1._.routes){const v=b.pattern.exec(o);if(!v)continue;const m=zr(v,b.params,y);if(m){c$2=b,f=P(m);break}}}let u;const _={};let d={};const p={cookies:null,fetch:null,getClientAddress:s.getClientAddress||(()=>{throw new Error("@sveltejs/adapter-node does not specify getClientAddress. Please raise an issue")}),locals:{},params:f,platform:s.platform,request:e,route:{id:c$2?.id??null},setHeaders:y=>{for(const b in y){const v=b.toLowerCase(),m=y[b];if(v==="set-cookie")throw new Error("Use `event.cookies.set(name, value, options)` instead of `event.setHeaders` to set cookies");if(v in _)throw new Error(`"${b}" header is already set`);_[v]=m,s.prerendering&&v==="cache-control"&&(s.prerendering.cache=m);}},url:n,isDataRequest:a,isSubRequest:s.depth>0};let h={transformPageChunk:Ze,filterSerializedResponseHeaders:Ye,preload:Ke};try{if(c$2){if(n.pathname===c||n.pathname===c+"/")u="always";else if(c$2.page){const w=await $e(c$2.page,r$1);u=D(w,"trailingSlash");}else c$2.endpoint&&(u=(await c$2.endpoint()).trailingSlash);if(!a){const w=b(n.pathname,u??"never");if(w!==n.pathname&&!s.prerendering?.fallback)return new Response(void 0,{status:308,headers:{"x-sveltekit-normalize":"1",location:(w.startsWith("//")?n.origin+w:w)+(n.search==="?"?"":n.search)}})}if(s.before_handle||s.emulator?.platform){let w={},k=!1;if(c$2.endpoint){const $=await c$2.endpoint();w=$.config??w,k=$.prerender??k;}else if(c$2.page){const $=await $e(c$2.page,r$1);w=es($)??w,k=D($,"prerender")??!1;}s.before_handle&&s.before_handle(p,w,k),s.emulator?.platform&&(p.platform=await s.emulator.platform({config:w,prerender:k}));}}const{cookies:y$1,new_cookies:b$1,get_cookie_header:v,set_internal:m}=Fr(e,n,u??"never");d=b$1,p.cookies=y$1,p.fetch=Xr({event:p,options:t,manifest:r$1,state:s,get_cookie_header:v,set_internal:m}),s.prerendering&&!s.prerendering.fallback&&y(n);const E=await t.hooks.handle({event:p,resolve:(w,k)=>g(w,k).then($=>{for(const O in _){const R=_[O];$.headers.set(O,R);}return Ve($.headers,Object.values(d)),s.prerendering&&w.route.id!==null&&$.headers.set("x-sveltekit-routeid",encodeURI(w.route.id)),$})});if(E.status===200&&E.headers.has("etag")){let w=e.headers.get("if-none-match");w?.startsWith('W/"')&&(w=w.substring(2));const k=E.headers.get("etag");if(w===k){const $=new Headers({etag:k});for(const O of ["cache-control","content-location","date","expires","vary","set-cookie"]){const R=E.headers.get(O);R&&$.set(O,R);}return new Response(void 0,{status:304,headers:$})}}if(a&&E.status>=300&&E.status<=308){const w=E.headers.get("location");if(w)return be(new z(E.status,w))}return E}catch(y){if(y instanceof z){const b=a?be(y):c$2?.page&&dt(p)?ht(y):ae(y.status,y.location);return Ve(b.headers,Object.values(d)),b}return await De(p,t,y)}async function g(y,b){try{if(b&&(h={transformPageChunk:b.transformPageChunk||Ze,filterSerializedResponseHeaders:b.filterSerializedResponseHeaders||Ye,preload:b.preload||Ke}),s.prerendering?.fallback)return await X({event:y,options:t,manifest:r$1,state:s,page_config:{ssr:!1,csr:!0},status:200,error:null,branch:[],fetched:[],resolve_opts:h});if(c$2){const v=y.request.method;let m;if(a)m=await Dr(y,c$2,t,r$1,s,l,u??"never");else if(c$2.endpoint&&(!c$2.page||er(y)))m=await Qt(y,await c$2.endpoint(),s);else if(c$2.page)if(ts.has(v))m=await Ir(y,c$2.page,t,r$1,s,h);else {const E=new Set(rs);if((await r$1._.nodes[c$2.page.leaf]())?.server?.actions&&E.add("POST"),v==="OPTIONS")m=new Response(null,{status:204,headers:{allow:Array.from(E.values()).join(", ")}});else {const k=[...E].reduce(($,O)=>($[O]=!0,$),{});m=nt(k,v);}}else throw new Error("This should never happen");if(e.method==="GET"&&c$2.page&&c$2.endpoint){const E=m.headers.get("vary")?.split(",")?.map(w=>w.trim().toLowerCase());E?.includes("accept")||E?.includes("*")||(m=new Response(m.body,{status:m.status,statusText:m.statusText,headers:new Headers(m.headers)}),m.headers.append("Vary","Accept"));}return m}return s.error&&y.isSubRequest?await fetch(e,{headers:{"x-sveltekit-error":"true"}}):s.error?r("Internal Server Error",{status:500}):s.depth===0?await St({event:y,options:t,manifest:r$1,state:s,status:404,error:new L(404,"Not Found",`Not found: ${y.url.pathname}`),resolve_opts:h}):s.prerendering?r("not found",{status:404}):await fetch(e)}catch(v){return await De(y,t,v)}finally{y.cookies.set=()=>{throw new Error("Cannot use `cookies.set(...)` after the response has been generated")},y.setHeaders=()=>{throw new Error("Cannot use `setHeaders(...)` after the response has been generated")};}}}function ss(e,{public_prefix:t,private_prefix:r}){return Object.fromEntries(Object.entries(e).filter(([s])=>s.startsWith(r)&&(t===""||!s.startsWith(t))))}function ns(e,{public_prefix:t,private_prefix:r}){return Object.fromEntries(Object.entries(e).filter(([s])=>s.startsWith(t)&&(r===""||!s.startsWith(r))))}class ms{#e;#r;constructor(t){this.#e=D$1,this.#r=t;}async init({env:t,read:r}){const s={public_prefix:this.#e.env_public_prefix,private_prefix:this.#e.env_private_prefix};ss(t,s);const i=ns(t,s);if(N(i),H(i),!this.#e.hooks)try{const o=await J();this.#e.hooks={handle:o.handle||(({event:c,resolve:f})=>f(c)),handleError:o.handleError||(({error:c})=>console.error(c)),handleFetch:o.handleFetch||(({request:c,fetch:f})=>f(c)),reroute:o.reroute||(()=>{})};}catch(o){throw o}}async respond(t,r){return Tt(t,this.#e,this.#r,{...r,error:!1,depth:0})}}

export { ms as Server };
//# sourceMappingURL=index.js.map
