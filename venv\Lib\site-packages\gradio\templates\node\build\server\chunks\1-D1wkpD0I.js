const index = 1;
let component_cache;
const component = async () => component_cache ??= (await import('./error.svelte-uehjnFTh.js')).default;
const imports = ["_app/immutable/nodes/1.Cm-0Xidz.js","_app/immutable/chunks/stores.z8sZTwoA.js","_app/immutable/chunks/client.Cd1aarwx.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, stylesheets };
//# sourceMappingURL=1-D1wkpD0I.js.map
