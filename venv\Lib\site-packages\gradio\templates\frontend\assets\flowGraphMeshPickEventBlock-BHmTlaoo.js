import{c as p,_ as a}from"./KHR_interactivity-DTxiAnOo.js";import{ax as h,R as n}from"./index-Dpxo-yl_.js";import{R as s,e as r,b as o}from"./declarationMapper-BZjsjg7g.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class u extends p{constructor(e){super(e),this.config=e,this.type="MeshPick",this.asset=this.registerDataInput("asset",s,e?.targetMesh),this.pickedPoint=this.registerDataOutput("pickedPoint",r),this.pickOrigin=this.registerDataOutput("pickOrigin",r),this.pointerId=this.registerDataOutput("pointerId",o),this.pickedMesh=this.registerDataOutput("pickedMesh",s),this.pointerType=this.registerDataInput("pointerType",s,h.POINTERPICK)}_getReferencedMesh(e){return this.asset.getValue(e)}_executeEvent(e,t){if(this.pointerType.getValue(e)!==t.type)return!0;const i=this._getReferencedMesh(e);return i&&t.pickInfo?.pickedMesh&&(t.pickInfo?.pickedMesh===i||a(t.pickInfo?.pickedMesh,i))?(this.pointerId.setValue(t.event.pointerId,e),this.pickOrigin.setValue(t.pickInfo.ray?.origin,e),this.pickedPoint.setValue(t.pickInfo.pickedPoint,e),this.pickedMesh.setValue(t.pickInfo.pickedMesh,e),this._execute(e),!this.config?.stopPropagation):(this.pointerId.resetToDefaultValue(e),this.pickOrigin.resetToDefaultValue(e),this.pickedPoint.resetToDefaultValue(e),this.pickedMesh.resetToDefaultValue(e),!0)}_preparePendingTasks(e){}_cancelPendingTasks(e){}getClassName(){return"FlowGraphMeshPickEventBlock"}}n("FlowGraphMeshPickEventBlock",u);export{u as FlowGraphMeshPickEventBlock};
//# sourceMappingURL=flowGraphMeshPickEventBlock-BHmTlaoo.js.map
