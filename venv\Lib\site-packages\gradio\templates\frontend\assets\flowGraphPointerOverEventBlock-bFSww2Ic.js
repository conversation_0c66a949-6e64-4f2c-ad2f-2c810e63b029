import{c as o,_ as s}from"./KHR_interactivity-DTxiAnOo.js";import{b as h,R as i}from"./declarationMapper-BZjsjg7g.js";import{R as a}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class p extends o{constructor(e){super(e),this.type="PointerOver",this.pointerId=this.registerDataOutput("pointerId",h),this.targetMesh=this.registerDataInput("targetMesh",i,e?.targetMesh),this.meshUnderPointer=this.registerDataOutput("meshUnderPointer",i)}_executeEvent(e,t){const r=this.targetMesh.getValue(e);this.meshUnderPointer.setValue(t.mesh,e);const n=t.out&&s(t.out,r);return this.pointerId.setValue(t.pointerId,e),!n&&(t.mesh===r||s(t.mesh,r))?(this._execute(e),!this.config?.stopPropagation):!0}_preparePendingTasks(e){}_cancelPendingTasks(e){}getClassName(){return"FlowGraphPointerOverEventBlock"}}a("FlowGraphPointerOverEventBlock",p);export{p as FlowGraphPointerOverEventBlock};
//# sourceMappingURL=flowGraphPointerOverEventBlock-bFSww2Ic.js.map
