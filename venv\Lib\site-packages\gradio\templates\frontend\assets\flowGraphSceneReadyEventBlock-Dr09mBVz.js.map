{"version": 3, "file": "flowGraphSceneReadyEventBlock-Dr09mBVz.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphSceneReadyEventBlock.js"], "sourcesContent": ["import { FlowGraphEventBlock } from \"../../flowGraphEventBlock.js\";\nimport { RegisterClass } from \"../../../Misc/typeStore.js\";\n/**\n * Block that triggers when a scene is ready.\n */\nexport class FlowGraphSceneReadyEventBlock extends FlowGraphEventBlock {\n    constructor() {\n        super(...arguments);\n        this.initPriority = -1;\n        this.type = \"SceneReady\" /* FlowGraphEventType.SceneReady */;\n    }\n    _executeEvent(context, _payload) {\n        this._execute(context);\n        return true;\n    }\n    _preparePendingTasks(context) {\n        // no-op\n    }\n    _cancelPendingTasks(context) {\n        // no-op\n    }\n    /**\n     * @returns class name of the block.\n     */\n    getClassName() {\n        return \"FlowGraphSceneReadyEventBlock\" /* FlowGraphBlockNames.SceneReadyEvent */;\n    }\n}\nRegisterClass(\"FlowGraphSceneReadyEventBlock\" /* FlowGraphBlockNames.SceneReadyEvent */, FlowGraphSceneReadyEventBlock);\n//# sourceMappingURL=flowGraphSceneReadyEventBlock.js.map"], "names": ["FlowGraphSceneReadyEventBlock", "FlowGraphEventBlock", "context", "_payload", "RegisterClass"], "mappings": "oOAKO,MAAMA,UAAsCC,CAAoB,CACnE,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,aAAe,GACpB,KAAK,KAAO,YACf,CACD,cAAcC,EAASC,EAAU,CAC7B,YAAK,SAASD,CAAO,EACd,EACV,CACD,qBAAqBA,EAAS,CAE7B,CACD,oBAAoBA,EAAS,CAE5B,CAID,cAAe,CACX,MAAO,+BACV,CACL,CACAE,EAAc,gCAA2EJ,CAA6B", "x_google_ignoreList": [0]}