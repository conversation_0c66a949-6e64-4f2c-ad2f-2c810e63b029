#!/usr/bin/env python3
"""
启动检查脚本
检查系统环境和服务状态，帮助诊断问题
"""

import sys
import os
import json
import requests
import subprocess
from pathlib import Path
from datetime import datetime

def check_python_environment():
    """检查Python环境"""
    print("🐍 检查Python环境...")
    
    # Python版本
    python_version = sys.version
    print(f"  Python版本: {python_version}")
    
    # 编码设置
    default_encoding = sys.getdefaultencoding()
    filesystem_encoding = sys.getfilesystemencoding()
    print(f"  默认编码: {default_encoding}")
    print(f"  文件系统编码: {filesystem_encoding}")
    
    # 检查UTF-8支持
    try:
        test_str = "测试中文编码2024年"
        encoded = test_str.encode('utf-8').decode('utf-8')
        print("  ✅ UTF-8编码支持正常")
    except Exception as e:
        print(f"  ❌ UTF-8编码问题: {e}")
    
    print()

def check_llama_server():
    """检查llama.cpp服务器状态"""
    print("🦙 检查llama.cpp服务器...")
    
    server_urls = [
        "http://localhost:8080",
        "http://127.0.0.1:8080",
        "http://localhost:8080/v1",
        "http://127.0.0.1:8080/v1"
    ]
    
    server_running = False
    
    for url in server_urls:
        try:
            response = requests.get(f"{url}/health", timeout=3)
            if response.status_code == 200:
                print(f"  ✅ 服务器运行正常: {url}")
                server_running = True
                break
        except requests.exceptions.ConnectionError:
            print(f"  ❌ 无法连接: {url}")
        except requests.exceptions.Timeout:
            print(f"  ⏰ 连接超时: {url}")
        except Exception as e:
            print(f"  ❓ 未知错误: {url} - {e}")
    
    if not server_running:
        print("  💡 建议:")
        print("     1. 启动llama.cpp服务器: ./server -m your_model.gguf")
        print("     2. 检查端口8080是否被占用")
        print("     3. 确认模型文件路径正确")
    
    print()

def check_project_structure():
    """检查项目结构"""
    print("📁 检查项目结构...")
    
    required_dirs = [
        "core",
        "memory", 
        "ui",
        "models",
        "history",
        "config"
    ]
    
    required_files = [
        "core/ai_engine.py",
        "core/llm/model_manager.py",
        "memory/history_manager.py",
        "config/settings/app_config.yaml"
    ]
    
    # 检查目录
    for dir_name in required_dirs:
        if os.path.exists(dir_name):
            print(f"  ✅ 目录存在: {dir_name}")
        else:
            print(f"  ❌ 目录缺失: {dir_name}")
    
    # 检查文件
    for file_name in required_files:
        if os.path.exists(file_name):
            print(f"  ✅ 文件存在: {file_name}")
        else:
            print(f"  ❌ 文件缺失: {file_name}")
    
    print()

def check_config_files():
    """检查配置文件"""
    print("⚙️ 检查配置文件...")
    
    config_file = "config/settings/app_config.yaml"
    
    if os.path.exists(config_file):
        try:
            import yaml
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            print(f"  ✅ 配置文件加载成功")
            
            # 检查关键配置
            if 'models' in config:
                print("  ✅ 模型配置存在")
            else:
                print("  ❌ 模型配置缺失")
                
        except Exception as e:
            print(f"  ❌ 配置文件解析失败: {e}")
    else:
        print(f"  ❌ 配置文件不存在: {config_file}")
    
    print()

def check_model_files():
    """检查模型文件"""
    print("🤖 检查模型文件...")
    
    model_dirs = [
        "models/llm",
        "models/image"
    ]
    
    for model_dir in model_dirs:
        if os.path.exists(model_dir):
            files = list(Path(model_dir).glob("*.gguf"))
            if files:
                print(f"  ✅ 找到模型文件 ({model_dir}):")
                for file in files[:3]:  # 只显示前3个
                    print(f"     - {file.name}")
                if len(files) > 3:
                    print(f"     ... 还有 {len(files) - 3} 个文件")
            else:
                print(f"  ❌ 未找到模型文件: {model_dir}")
        else:
            print(f"  ❌ 模型目录不存在: {model_dir}")
    
    print()

def check_history_files():
    """检查历史文件"""
    print("📚 检查历史文件...")
    
    history_dir = "history"
    
    if os.path.exists(history_dir):
        json_files = list(Path(history_dir).glob("*.json"))
        if json_files:
            print(f"  ✅ 找到 {len(json_files)} 个历史文件")
            
            # 检查最近的文件
            recent_files = sorted(json_files, key=lambda x: x.stat().st_mtime, reverse=True)[:3]
            for file in recent_files:
                try:
                    with open(file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    print(f"     ✅ {file.name} (消息数: {data.get('total_messages', 0)})")
                except Exception as e:
                    print(f"     ❌ {file.name} (解析失败: {e})")
        else:
            print("  📝 暂无历史文件")
    else:
        print(f"  ❌ 历史目录不存在: {history_dir}")
    
    print()

def check_dependencies():
    """检查依赖包"""
    print("📦 检查依赖包...")
    
    required_packages = [
        "openai",
        "requests", 
        "yaml",
        "streamlit",
        "Pillow"
    ]
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} (未安装)")
    
    print()

def generate_diagnostic_report():
    """生成诊断报告"""
    print("📋 生成诊断报告...")
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "python_version": sys.version,
        "platform": sys.platform,
        "encoding": {
            "default": sys.getdefaultencoding(),
            "filesystem": sys.getfilesystemencoding()
        },
        "working_directory": os.getcwd(),
        "environment_variables": {
            "PYTHONPATH": os.environ.get("PYTHONPATH", "未设置"),
            "LANG": os.environ.get("LANG", "未设置"),
            "LC_ALL": os.environ.get("LC_ALL", "未设置")
        }
    }
    
    report_file = f"diagnostic_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"  ✅ 诊断报告已保存: {report_file}")
    except Exception as e:
        print(f"  ❌ 保存诊断报告失败: {e}")
    
    print()

def main():
    """主函数"""
    print("🚀 Reverie Agents RIO - 启动检查\n")
    print("=" * 50)
    
    check_python_environment()
    check_project_structure()
    check_config_files()
    check_model_files()
    check_llama_server()
    check_history_files()
    check_dependencies()
    generate_diagnostic_report()
    
    print("=" * 50)
    print("✨ 检查完成！")
    print("\n💡 如果发现问题，请参考以下解决方案:")
    print("1. 编码问题: 确保系统locale设置为UTF-8")
    print("2. 服务器问题: 启动llama.cpp服务器")
    print("3. 模型问题: 下载并放置GGUF格式模型文件")
    print("4. 依赖问题: pip install -r requirements.txt")
    print("5. 配置问题: 检查config/settings/app_config.yaml")

if __name__ == "__main__":
    main()