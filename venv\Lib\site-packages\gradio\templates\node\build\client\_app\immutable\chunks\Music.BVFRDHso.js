import{SvelteComponent as f,init as m,safe_not_equal as v,svg_element as n,claim_svg_element as h,children as u,detach as l,attr as t,insert_hydration as g,append_hydration as a,noop as d}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function x(p){let e,o,r,c;return{c(){e=n("svg"),o=n("path"),r=n("circle"),c=n("circle"),this.h()},l(s){e=h(s,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0,class:!0});var i=u(e);o=h(i,"path",{d:!0}),u(o).forEach(l),r=h(i,"circle",{cx:!0,cy:!0,r:!0}),u(r).forEach(l),c=h(i,"circle",{cx:!0,cy:!0,r:!0}),u(c).forEach(l),i.forEach(l),this.h()},h(){t(o,"d","M9 18V5l12-2v13"),t(r,"cx","6"),t(r,"cy","18"),t(r,"r","3"),t(c,"cx","18"),t(c,"cy","16"),t(c,"r","3"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"width","100%"),t(e,"height","100%"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","1.5"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-music")},m(s,i){g(s,e,i),a(e,o),a(e,r),a(e,c)},p:d,i:d,o:d,d(s){s&&l(e)}}}class k extends f{constructor(e){super(),m(this,e,null,x,v,{})}}export{k as M};
//# sourceMappingURL=Music.BVFRDHso.js.map
