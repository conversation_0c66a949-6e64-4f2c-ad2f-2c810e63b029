{"version": 3, "mappings": ";2IAKA,IAAIA,EACAC,EAAiB,KACrB,eAAeC,GAAsB,CACjC,OAAKD,IACDA,EAAiB,IAAI,QAAQ,CAACE,EAASC,IAAW,CAC9C,IAAIC,EACAC,EAAS,KACb,MAAMC,EAAU,CACZ,sBAAuB,GACvB,MAAO,GACP,QAAS,GACT,MAAO,GACP,mBAAoB,GACpB,UAAW,GACX,6BAA8B,EAC9C,EACWC,EAAA,IAAC,OAAO,qBAA0B,OAAAC,KAAA,IAAC,0CAC7B,KAAK,CAAC,CAAE,WAAYC,KAAsB,CAC3C,MAAMC,EAAsBC,EAAY,UAAU,OAClD,GAAI,CACAP,EAAS,IAAI,gBAAgB,IAAK,GAAG,EACrCC,EAAS,IAAII,EAAgBL,EAAQ,GAAOE,CAAO,CACtD,MACS,CACFI,EAAsBC,EAAY,UAAU,QAE5CA,EAAY,UAAU,IAAK,GAAE,QAAO,EAGxCP,EAAS,SAAS,cAAc,QAAQ,EACxCC,EAAS,IAAII,EAAgBL,EAAQ,GAAOE,CAAO,CACtD,CAEDK,EAAY,UAAU,MAEtBA,EAAY,4BAA4B,IAAKC,GAAM,CAG3CP,GAAUO,IAAMP,GAAU,CAACA,EAAO,YAAcM,EAAY,UAAU,SAAW,GAEjFE,GAExB,CAAiB,EACDR,EAAO,QAAO,EAAG,sBAAwB,OACzC,MAAMS,EAAW,IAAIC,EAAeV,CAAM,EAC1CE,EAAA,WAAO,6BAA6B,EAAC,4CAAC,KAAK,CAAC,CAAE,gBAAAS,CAAe,IAAO,CAChE,GAAI,CAACX,EAAQ,CACTF,EAAO,uBAAuB,EAC9B,MACH,CACD,MAAMc,EAAU,IAAIC,EAAc,CAC9B,OAAAb,EACA,KAAMW,EAAgB,KACtB,eAAgBA,EAAgB,OAChC,aAAc,CAAC,gBAAgB,CACvD,CAAqB,EACDjB,EAAmB,CACf,OAAAK,EACA,OAAAC,EACA,SAAAS,EACA,QAAAG,CACxB,EACoBf,EAAQH,CAAgB,CAC5C,CAAiB,CACjB,CAAa,EACI,MAAMI,CAAM,CAC7B,CAAS,GAEE,MAAMH,CACjB,CAYO,eAAemB,EAAgBC,EAAOC,EAAQhB,EAAQiB,EAAiBC,EAAW,YAAaC,EAAUC,EAAS,CAErH,MAAMC,EAAa,MAAMrB,EAAO,WAAW,EAAG,EAAGe,EAAOC,CAAM,EACxDM,EAAO,IAAI,WAAWD,EAAW,MAAM,EAC7CE,EAASR,EAAOC,EAAQM,EAAML,EAAiBC,EAAUC,EAAU,GAAM,OAAWC,CAAO,CAC/F,CAaO,SAASI,EAAcT,EAAOC,EAAQM,EAAMJ,EAAW,YAAaC,EAAUM,EAAU,GAAOC,EAAgB,GAAON,EAAS,CAClI,OAAO,IAAI,QAASvB,GAAY,CAC5B0B,EAASR,EAAOC,EAAQM,EAAOK,GAAW9B,EAAQ8B,CAAM,EAAGT,EAAUC,EAAUM,EAASC,EAAeN,CAAO,CACtH,CAAK,CACL,CAaO,SAASG,EAASR,EAAOC,EAAQM,EAAML,EAAiBC,EAAW,YAAaC,EAAUM,EAAU,GAAOC,EAAgB,GAAON,EAAS,CAC9IxB,EAAqB,EAAC,KAAMa,GAAa,CAGrC,GAFAA,EAAS,OAAO,QAAQM,EAAOC,EAAQ,EAAI,EAEvCM,aAAgB,aAAc,CAC9B,MAAMM,EAAQ,IAAI,WAAWN,EAAK,MAAM,EACxC,IAAInB,EAAImB,EAAK,OACb,KAAOnB,KAAK,CACR,MAAM0B,EAAIP,EAAKnB,CAAC,EAChByB,EAAMzB,CAAC,EAAI,KAAK,MAAM2B,EAAMD,CAAC,EAAI,GAAG,CACvC,CACDP,EAAOM,CACV,CAED,MAAMG,EAAUtB,EAAS,OAAO,iBAAiBa,EAAMP,EAAOC,EAAQ,EAAG,GAAO,CAACS,EAAS,CAAC,EAC3FhB,EAAS,SAAS,cAClBA,EAAS,SAAS,mBAAmBA,EAAS,OAAO,EACrDA,EAAS,QAAQ,OAAO,aAAa,iBAAkBsB,CAAO,EAC9DtB,EAAS,SAAS,OACdiB,EACAM,EAAM,OAAOvB,EAAS,OAASwB,GAAS,CACpC,MAAMC,EAAa,IAAI,WACvBA,EAAW,OAAUC,GAAU,CAC3B,MAAMC,EAAcD,EAAM,OAAO,OAC7BlB,GACAA,EAAgBmB,CAAW,CAEnD,EACgBF,EAAW,kBAAkBD,CAAI,CACjD,EAAef,EAAUE,CAAO,EAGpBY,EAAM,2BAA2BvB,EAAS,OAAQQ,EAAiBC,EAAUC,EAAUC,CAAO,EAElGW,EAAQ,QAAO,CACvB,CAAK,CACL,CAIO,SAASvB,GAAU,CAClBd,GACAA,EAAiB,QAAQ,UACzBA,EAAiB,SAAS,UAC1BA,EAAiB,OAAO,WAIxBC,GAAgB,KAAM0C,GAAoB,CACtCA,EAAgB,QAAQ,UACxBA,EAAgB,SAAS,UACzBA,EAAgB,OAAO,SACnC,CAAS,EAEL1C,EAAiB,KACjBD,EAAmB,IACvB,CAKY,MAAC4C,EAAY,CAErB,SAAAf,EAEA,cAAAC,EAEA,gBAAAV,EAEA,QAAAN,CACJ,EAOM+B,EAAkB,IAAM,CAE1BP,EAAM,SAAWT,EACjBS,EAAM,cAAgBR,EACtBQ,EAAM,gBAAkBlB,CAC5B,EACAyB,EAAiB", "names": ["_dumpToolsEngine", "_enginePromise", "_Create<PERSON><PERSON><PERSON><PERSON><PERSON>", "resolve", "reject", "canvas", "engine", "options", "__vitePreload", "n", "thinEngineClass", "engineInstanceCount", "EngineStore", "e", "Dispose", "renderer", "<PERSON><PERSON><PERSON><PERSON>", "passPixelShader", "wrapper", "EffectWrapper", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "width", "height", "success<PERSON>allback", "mimeType", "fileName", "quality", "bufferView", "data", "DumpData", "DumpDataAsync", "invertY", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "result", "data2", "v", "C<PERSON>", "texture", "Tools", "blob", "fileReader", "event", "arrayBuffer", "dumpToolsEngine", "DumpTools", "initSideEffects"], "ignoreList": [0], "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Misc/dumpTools.js"], "sourcesContent": ["\nimport { EffectRenderer, EffectWrapper } from \"../Materials/effectRenderer.js\";\nimport { Tools } from \"./tools.js\";\nimport { Clamp } from \"../Maths/math.scalar.functions.js\";\nimport { EngineStore } from \"../Engines/engineStore.js\";\nlet _dumpToolsEngine;\nlet _enginePromise = null;\nasync function _CreateDumpRenderer() {\n    if (!_enginePromise) {\n        _enginePromise = new Promise((resolve, reject) => {\n            let canvas;\n            let engine = null;\n            const options = {\n                preserveDrawingBuffer: true,\n                depth: false,\n                stencil: false,\n                alpha: true,\n                premultipliedAlpha: false,\n                antialias: false,\n                failIfMajorPerformanceCaveat: false,\n            };\n            import(\"../Engines/thinEngine.js\")\n                .then(({ ThinEngine: thinEngineClass }) => {\n                const engineInstanceCount = EngineStore.Instances.length;\n                try {\n                    canvas = new OffscreenCanvas(100, 100); // will be resized later\n                    engine = new thinEngineClass(canvas, false, options);\n                }\n                catch (e) {\n                    if (engineInstanceCount < EngineStore.Instances.length) {\n                        // The engine was created by another instance, let's use it\n                        EngineStore.Instances.pop()?.dispose();\n                    }\n                    // The browser either does not support OffscreenCanvas or WebGL context in OffscreenCanvas, fallback on a regular canvas\n                    canvas = document.createElement(\"canvas\");\n                    engine = new thinEngineClass(canvas, false, options);\n                }\n                // remove this engine from the list of instances to avoid using it for other purposes\n                EngineStore.Instances.pop();\n                // However, make sure to dispose it when no other engines are left\n                EngineStore.OnEnginesDisposedObservable.add((e) => {\n                    // guaranteed to run when no other instances are left\n                    // only dispose if it's not the current engine\n                    if (engine && e !== engine && !engine.isDisposed && EngineStore.Instances.length === 0) {\n                        // Dump the engine and the associated resources\n                        Dispose();\n                    }\n                });\n                engine.getCaps().parallelShaderCompile = undefined;\n                const renderer = new EffectRenderer(engine);\n                import(\"../Shaders/pass.fragment.js\").then(({ passPixelShader }) => {\n                    if (!engine) {\n                        reject(\"Engine is not defined\");\n                        return;\n                    }\n                    const wrapper = new EffectWrapper({\n                        engine,\n                        name: passPixelShader.name,\n                        fragmentShader: passPixelShader.shader,\n                        samplerNames: [\"textureSampler\"],\n                    });\n                    _dumpToolsEngine = {\n                        canvas,\n                        engine,\n                        renderer,\n                        wrapper,\n                    };\n                    resolve(_dumpToolsEngine);\n                });\n            })\n                .catch(reject);\n        });\n    }\n    return await _enginePromise;\n}\n/**\n * Dumps the current bound framebuffer\n * @param width defines the rendering width\n * @param height defines the rendering height\n * @param engine defines the hosting engine\n * @param successCallback defines the callback triggered once the data are available\n * @param mimeType defines the mime type of the result\n * @param fileName defines the filename to download. If present, the result will automatically be downloaded\n * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\n * @returns a void promise\n */\nexport async function DumpFramebuffer(width, height, engine, successCallback, mimeType = \"image/png\", fileName, quality) {\n    // Read the contents of the framebuffer\n    const bufferView = await engine.readPixels(0, 0, width, height);\n    const data = new Uint8Array(bufferView.buffer);\n    DumpData(width, height, data, successCallback, mimeType, fileName, true, undefined, quality);\n}\n/**\n * Dumps an array buffer\n * @param width defines the rendering width\n * @param height defines the rendering height\n * @param data the data array\n * @param mimeType defines the mime type of the result\n * @param fileName defines the filename to download. If present, the result will automatically be downloaded\n * @param invertY true to invert the picture in the Y dimension\n * @param toArrayBuffer true to convert the data to an ArrayBuffer (encoded as `mimeType`) instead of a base64 string\n * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\n * @returns a promise that resolve to the final data\n */\nexport function DumpDataAsync(width, height, data, mimeType = \"image/png\", fileName, invertY = false, toArrayBuffer = false, quality) {\n    return new Promise((resolve) => {\n        DumpData(width, height, data, (result) => resolve(result), mimeType, fileName, invertY, toArrayBuffer, quality);\n    });\n}\n/**\n * Dumps an array buffer\n * @param width defines the rendering width\n * @param height defines the rendering height\n * @param data the data array\n * @param successCallback defines the callback triggered once the data are available\n * @param mimeType defines the mime type of the result\n * @param fileName defines the filename to download. If present, the result will automatically be downloaded\n * @param invertY true to invert the picture in the Y dimension\n * @param toArrayBuffer true to convert the data to an ArrayBuffer (encoded as `mimeType`) instead of a base64 string\n * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\n */\nexport function DumpData(width, height, data, successCallback, mimeType = \"image/png\", fileName, invertY = false, toArrayBuffer = false, quality) {\n    _CreateDumpRenderer().then((renderer) => {\n        renderer.engine.setSize(width, height, true);\n        // Convert if data are float32\n        if (data instanceof Float32Array) {\n            const data2 = new Uint8Array(data.length);\n            let n = data.length;\n            while (n--) {\n                const v = data[n];\n                data2[n] = Math.round(Clamp(v) * 255);\n            }\n            data = data2;\n        }\n        // Create the image\n        const texture = renderer.engine.createRawTexture(data, width, height, 5, false, !invertY, 1);\n        renderer.renderer.setViewport();\n        renderer.renderer.applyEffectWrapper(renderer.wrapper);\n        renderer.wrapper.effect._bindTexture(\"textureSampler\", texture);\n        renderer.renderer.draw();\n        if (toArrayBuffer) {\n            Tools.ToBlob(renderer.canvas, (blob) => {\n                const fileReader = new FileReader();\n                fileReader.onload = (event) => {\n                    const arrayBuffer = event.target.result;\n                    if (successCallback) {\n                        successCallback(arrayBuffer);\n                    }\n                };\n                fileReader.readAsArrayBuffer(blob);\n            }, mimeType, quality);\n        }\n        else {\n            Tools.EncodeScreenshotCanvasData(renderer.canvas, successCallback, mimeType, fileName, quality);\n        }\n        texture.dispose();\n    });\n}\n/**\n * Dispose the dump tools associated resources\n */\nexport function Dispose() {\n    if (_dumpToolsEngine) {\n        _dumpToolsEngine.wrapper.dispose();\n        _dumpToolsEngine.renderer.dispose();\n        _dumpToolsEngine.engine.dispose();\n    }\n    else {\n        // in cases where the engine is not yet created, we need to wait for it to dispose it\n        _enginePromise?.then((dumpToolsEngine) => {\n            dumpToolsEngine.wrapper.dispose();\n            dumpToolsEngine.renderer.dispose();\n            dumpToolsEngine.engine.dispose();\n        });\n    }\n    _enginePromise = null;\n    _dumpToolsEngine = null;\n}\n/**\n * Object containing a set of static utilities functions to dump data from a canvas\n * @deprecated use functions\n */\nexport const DumpTools = {\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    DumpData,\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    DumpDataAsync,\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    DumpFramebuffer,\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    Dispose,\n};\n/**\n * This will be executed automatically for UMD and es5.\n * If esm dev wants the side effects to execute they will have to run it manually\n * Once we build native modules those need to be exported.\n * @internal\n */\nconst initSideEffects = () => {\n    // References the dependencies.\n    Tools.DumpData = DumpData;\n    Tools.DumpDataAsync = DumpDataAsync;\n    Tools.DumpFramebuffer = DumpFramebuffer;\n};\ninitSideEffects();\n//# sourceMappingURL=dumpTools.js.map"], "file": "assets/dumpTools-DkqtMIaE.js"}