const{SvelteComponent:g,append:_,attr:d,detach:o,element:h,flush:u,init:v,insert:y,noop:c,safe_not_equal:b,set_data:m,text:p,toggle_class:r}=window.__gradio__svelte__internal;function w(s){let e,t=f(s[0])+"",a;return{c(){e=h("pre"),a=p(t),d(e,"class","svelte-agpzo2"),r(e,"table",s[1]==="table"),r(e,"gallery",s[1]==="gallery"),r(e,"selected",s[2])},m(l,n){y(l,e,n),_(e,a)},p(l,[n]){n&1&&t!==(t=f(l[0])+"")&&m(a,t),n&2&&r(e,"table",l[1]==="table"),n&2&&r(e,"gallery",l[1]==="gallery"),n&4&&r(e,"selected",l[2])},i:c,o:c,d(l){l&&o(e)}}}function f(s,e=60){if(!s)return"";const t=String(s);return t.length<=e?t:t.slice(0,e)+"..."}function S(s,e,t){let{value:a}=e,{type:l}=e,{selected:n=!1}=e;return s.$$set=i=>{"value"in i&&t(0,a=i.value),"type"in i&&t(1,l=i.type),"selected"in i&&t(2,n=i.selected)},[a,l,n]}class q extends g{constructor(e){super(),v(this,e,S,w,b,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),u()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),u()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),u()}}export{q as default};
//# sourceMappingURL=Example-DQvZIwBt.js.map
