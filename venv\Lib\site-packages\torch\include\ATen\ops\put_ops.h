#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API put_ {
  using schema = at::Tensor & (at::Tensor &, const at::Tensor &, const at::Tensor &, bool);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::put_";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "put_(Tensor(a!) self, Tensor index, Tensor source, bool accumulate=False) -> Tensor(a!)";
  static at::Tensor & call(at::Tensor & self, const at::Tensor & index, const at::Tensor & source, bool accumulate);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, at::Tensor & self, const at::Tensor & index, const at::Tensor & source, bool accumulate);
};

struct TORCH_API put {
  using schema = at::Tensor (const at::Tensor &, const at::Tensor &, const at::Tensor &, bool);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::put";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "put(Tensor self, Tensor index, Tensor source, bool accumulate=False) -> Tensor";
  static at::Tensor call(const at::Tensor & self, const at::Tensor & index, const at::Tensor & source, bool accumulate);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & index, const at::Tensor & source, bool accumulate);
};

struct TORCH_API put_out {
  using schema = at::Tensor & (const at::Tensor &, const at::Tensor &, const at::Tensor &, bool, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::put";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "put.out(Tensor self, Tensor index, Tensor source, bool accumulate=False, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, const at::Tensor & index, const at::Tensor & source, bool accumulate, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & index, const at::Tensor & source, bool accumulate, at::Tensor & out);
};

}} // namespace at::_ops
