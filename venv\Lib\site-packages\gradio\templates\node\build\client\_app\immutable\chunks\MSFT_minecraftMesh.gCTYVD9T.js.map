{"version": 3, "file": "MSFT_minecraftMesh.gCTYVD9T.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/MSFT_minecraftMesh.js"], "sourcesContent": ["import { PBRMaterial } from \"@babylonjs/core/Materials/PBR/pbrMaterial.js\";\nimport { GLTFLoader } from \"../glTFLoader.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"MSFT_minecraftMesh\";\n/** @internal */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class MSFT_minecraftMesh {\n    /** @internal */\n    constructor(loader) {\n        /** @internal */\n        this.name = NAME;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /** @internal */\n    loadMaterialPropertiesAsync(context, material, babylonMaterial) {\n        return GLTFLoader.LoadExtraAsync(context, material, this.name, (extraContext, extra) => {\n            if (extra) {\n                if (!(babylonMaterial instanceof PBRMaterial)) {\n                    throw new Error(`${extraContext}: Material type not supported`);\n                }\n                const promise = this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial);\n                if (babylonMaterial.needAlphaBlending()) {\n                    babylonMaterial.forceDepthWrite = true;\n                    babylonMaterial.separateCullingPass = true;\n                }\n                babylonMaterial.backFaceCulling = babylonMaterial.forceDepthWrite;\n                babylonMaterial.twoSidedLighting = true;\n                return promise;\n            }\n            return null;\n        });\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new MSFT_minecraftMesh(loader));\n//# sourceMappingURL=MSFT_minecraftMesh.js.map"], "names": ["NAME", "MSFT_minecraftMesh", "loader", "context", "material", "babylonMaterial", "GLTFLoader", "extraContext", "extra", "PBRMaterial", "promise", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "+GAGA,MAAMA,EAAO,qBAGN,MAAMC,CAAmB,CAE5B,YAAYC,EAAQ,CAEhB,KAAK,KAAOF,EACZ,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,CACnD,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAED,4BAA4BG,EAASC,EAAUC,EAAiB,CAC5D,OAAOC,EAAW,eAAeH,EAASC,EAAU,KAAK,KAAM,CAACG,EAAcC,IAAU,CACpF,GAAIA,EAAO,CACP,GAAI,EAAEH,aAA2BI,GAC7B,MAAM,IAAI,MAAM,GAAGF,CAAY,+BAA+B,EAElE,MAAMG,EAAU,KAAK,QAAQ,4BAA4BP,EAASC,EAAUC,CAAe,EAC3F,OAAIA,EAAgB,sBAChBA,EAAgB,gBAAkB,GAClCA,EAAgB,oBAAsB,IAE1CA,EAAgB,gBAAkBA,EAAgB,gBAClDA,EAAgB,iBAAmB,GAC5BK,CACV,CACD,OAAO,IACnB,CAAS,CACJ,CACL,CACAC,EAAwBX,CAAI,EAC5BY,EAAsBZ,EAAM,GAAOE,GAAW,IAAID,EAAmBC,CAAM,CAAC", "x_google_ignoreList": [0]}