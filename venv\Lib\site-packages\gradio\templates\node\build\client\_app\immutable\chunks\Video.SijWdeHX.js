var ni=Object.defineProperty;var mi=(a,i,t)=>i in a?ni(a,i,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[i]=t;var h=(a,i,t)=>(mi(a,typeof i!="symbol"?i+"":i,t),t),H=(a,i,t)=>{if(!i.has(a))throw TypeError("Cannot "+t)};var p=(a,i,t)=>(H(a,i,"read from private field"),t?t.call(a):i.get(a)),k=(a,i,t)=>{if(i.has(a))throw TypeError("Cannot add the same private member more than once");i instanceof WeakSet?i.add(a):i.set(a,t)},U=(a,i,t,l)=>(H(a,i,"write to private field"),l?l.call(a,t):i.set(a,t),t);import{SvelteComponent as ci,init as ri,safe_not_equal as di,create_slot as fi,element as F,space as xi,claim_element as X,get_svelte_dataset as ui,claim_space as gi,children as vi,detach as q,attr as L,toggle_class as K,src_url_equal as J,add_render_callback as hi,insert_hydration as V,listen as d,action_destroyer as Ei,update_slot_base as bi,get_all_dirty_from_scope as ji,get_slot_changes as wi,is_function as yi,transition_in as Ri,transition_out as _i,run_all as ki,createEventDispatcher as Li,assign as Y,exclude_internal_props as Q,raf as Di,bubble as P,binding_callbacks as Oi}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{x as Ii}from"./2.B2AoQPnG.js";import{H as N}from"./hls.C_AVPmGC.js";const Ti=new Error("failed to get response body reader"),Ai=new Error("failed to complete download"),Ni="Content-Length",Ui=async(a,i)=>{var e;const t=await fetch(a);let l;try{const c=parseInt(t.headers.get(Ni)||"-1"),u=(e=t.body)==null?void 0:e.getReader();if(!u)throw Ti;const g=[];let v=0;for(;;){const{done:n,value:f}=await u.read(),w=f?f.length:0;if(n){if(c!=-1&&c!==v)throw Ai;i&&i({url:a,total:c,received:v,delta:w,done:n});break}g.push(f),v+=w,i&&i({url:a,total:c,received:v,delta:w,done:n})}const b=new Uint8Array(v);let j=0;for(const n of g)b.set(n,j),j+=n.length;l=b.buffer}catch(c){console.log("failed to send download progress event: ",c),l=await t.arrayBuffer()}return l},W=async(a,i,t=!1,l)=>{const e=t?await Ui(a,l):await(await fetch(a)).arrayBuffer(),c=new Blob([e],{type:i});return URL.createObjectURL(c)};var m;(function(a){a.LOAD="LOAD",a.EXEC="EXEC",a.WRITE_FILE="WRITE_FILE",a.READ_FILE="READ_FILE",a.DELETE_FILE="DELETE_FILE",a.RENAME="RENAME",a.CREATE_DIR="CREATE_DIR",a.LIST_DIR="LIST_DIR",a.DELETE_DIR="DELETE_DIR",a.ERROR="ERROR",a.DOWNLOAD="DOWNLOAD",a.PROGRESS="PROGRESS",a.LOG="LOG",a.MOUNT="MOUNT",a.UNMOUNT="UNMOUNT"})(m||(m={}));const qi=(()=>{let a=0;return()=>a++})(),Si=new Error("ffmpeg is not loaded, call `await ffmpeg.load()` first"),zi=new Error("called FFmpeg.terminate()");var y,D,_,T,A,S,E;class Mi{constructor(){k(this,y,null);k(this,D,{});k(this,_,{});k(this,T,[]);k(this,A,[]);h(this,"loaded",!1);k(this,S,()=>{p(this,y)&&(p(this,y).onmessage=({data:{id:i,type:t,data:l}})=>{switch(t){case m.LOAD:this.loaded=!0,p(this,D)[i](l);break;case m.MOUNT:case m.UNMOUNT:case m.EXEC:case m.WRITE_FILE:case m.READ_FILE:case m.DELETE_FILE:case m.RENAME:case m.CREATE_DIR:case m.LIST_DIR:case m.DELETE_DIR:p(this,D)[i](l);break;case m.LOG:p(this,T).forEach(e=>e(l));break;case m.PROGRESS:p(this,A).forEach(e=>e(l));break;case m.ERROR:p(this,_)[i](l);break}delete p(this,D)[i],delete p(this,_)[i]})});k(this,E,({type:i,data:t},l=[],e)=>p(this,y)?new Promise((c,u)=>{const g=qi();p(this,y)&&p(this,y).postMessage({id:g,type:i,data:t},l),p(this,D)[g]=c,p(this,_)[g]=u,e==null||e.addEventListener("abort",()=>{u(new DOMException(`Message # ${g} was aborted`,"AbortError"))},{once:!0})}):Promise.reject(Si));h(this,"load",(i={},{signal:t}={})=>(p(this,y)||(U(this,y,new Worker(new URL(""+new URL("../workers/worker-DJ3jufjD.js",import.meta.url).href,import.meta.url),{type:"module"})),p(this,S).call(this)),p(this,E).call(this,{type:m.LOAD,data:i},void 0,t)));h(this,"exec",(i,t=-1,{signal:l}={})=>p(this,E).call(this,{type:m.EXEC,data:{args:i,timeout:t}},void 0,l));h(this,"terminate",()=>{const i=Object.keys(p(this,_));for(const t of i)p(this,_)[t](zi),delete p(this,_)[t],delete p(this,D)[t];p(this,y)&&(p(this,y).terminate(),U(this,y,null),this.loaded=!1)});h(this,"writeFile",(i,t,{signal:l}={})=>{const e=[];return t instanceof Uint8Array&&e.push(t.buffer),p(this,E).call(this,{type:m.WRITE_FILE,data:{path:i,data:t}},e,l)});h(this,"mount",(i,t,l)=>{const e=[];return p(this,E).call(this,{type:m.MOUNT,data:{fsType:i,options:t,mountPoint:l}},e)});h(this,"unmount",i=>{const t=[];return p(this,E).call(this,{type:m.UNMOUNT,data:{mountPoint:i}},t)});h(this,"readFile",(i,t="binary",{signal:l}={})=>p(this,E).call(this,{type:m.READ_FILE,data:{path:i,encoding:t}},void 0,l));h(this,"deleteFile",(i,{signal:t}={})=>p(this,E).call(this,{type:m.DELETE_FILE,data:{path:i}},void 0,t));h(this,"rename",(i,t,{signal:l}={})=>p(this,E).call(this,{type:m.RENAME,data:{oldPath:i,newPath:t}},void 0,l));h(this,"createDir",(i,{signal:t}={})=>p(this,E).call(this,{type:m.CREATE_DIR,data:{path:i}},void 0,t));h(this,"listDir",(i,{signal:t}={})=>p(this,E).call(this,{type:m.LIST_DIR,data:{path:i}},void 0,t));h(this,"deleteDir",(i,{signal:t}={})=>p(this,E).call(this,{type:m.DELETE_DIR,data:{path:i}},void 0,t))}on(i,t){i==="log"?p(this,T).push(t):i==="progress"&&p(this,A).push(t)}off(i,t){i==="log"?U(this,T,p(this,T).filter(l=>l!==t)):i==="progress"&&U(this,A,p(this,A).filter(l=>l!==t))}}y=new WeakMap,D=new WeakMap,_=new WeakMap,T=new WeakMap,A=new WeakMap,S=new WeakMap,E=new WeakMap;const Bi={"3g2":"video/3gpp2","3gp":"video/3gpp","3gpp":"video/3gpp","3mf":"model/3mf",aac:"audio/aac",ac:"application/pkix-attr-cert",adp:"audio/adpcm",adts:"audio/aac",ai:"application/postscript",aml:"application/automationml-aml+xml",amlx:"application/automationml-amlx+zip",amr:"audio/amr",apng:"image/apng",appcache:"text/cache-manifest",appinstaller:"application/appinstaller",appx:"application/appx",appxbundle:"application/appxbundle",asc:"application/pgp-keys",atom:"application/atom+xml",atomcat:"application/atomcat+xml",atomdeleted:"application/atomdeleted+xml",atomsvc:"application/atomsvc+xml",au:"audio/basic",avci:"image/avci",avcs:"image/avcs",avif:"image/avif",aw:"application/applixware",bdoc:"application/bdoc",bin:"application/octet-stream",bmp:"image/bmp",bpk:"application/octet-stream",btf:"image/prs.btif",btif:"image/prs.btif",buffer:"application/octet-stream",ccxml:"application/ccxml+xml",cdfx:"application/cdfx+xml",cdmia:"application/cdmi-capability",cdmic:"application/cdmi-container",cdmid:"application/cdmi-domain",cdmio:"application/cdmi-object",cdmiq:"application/cdmi-queue",cer:"application/pkix-cert",cgm:"image/cgm",cjs:"application/node",class:"application/java-vm",coffee:"text/coffeescript",conf:"text/plain",cpl:"application/cpl+xml",cpt:"application/mac-compactpro",crl:"application/pkix-crl",css:"text/css",csv:"text/csv",cu:"application/cu-seeme",cwl:"application/cwl",cww:"application/prs.cww",davmount:"application/davmount+xml",dbk:"application/docbook+xml",deb:"application/octet-stream",def:"text/plain",deploy:"application/octet-stream",dib:"image/bmp","disposition-notification":"message/disposition-notification",dist:"application/octet-stream",distz:"application/octet-stream",dll:"application/octet-stream",dmg:"application/octet-stream",dms:"application/octet-stream",doc:"application/msword",dot:"application/msword",dpx:"image/dpx",drle:"image/dicom-rle",dsc:"text/prs.lines.tag",dssc:"application/dssc+der",dtd:"application/xml-dtd",dump:"application/octet-stream",dwd:"application/atsc-dwd+xml",ear:"application/java-archive",ecma:"application/ecmascript",elc:"application/octet-stream",emf:"image/emf",eml:"message/rfc822",emma:"application/emma+xml",emotionml:"application/emotionml+xml",eps:"application/postscript",epub:"application/epub+zip",exe:"application/octet-stream",exi:"application/exi",exp:"application/express",exr:"image/aces",ez:"application/andrew-inset",fdf:"application/fdf",fdt:"application/fdt+xml",fits:"image/fits",g3:"image/g3fax",gbr:"application/rpki-ghostbusters",geojson:"application/geo+json",gif:"image/gif",glb:"model/gltf-binary",gltf:"model/gltf+json",gml:"application/gml+xml",gpx:"application/gpx+xml",gram:"application/srgs",grxml:"application/srgs+xml",gxf:"application/gxf",gz:"application/gzip",h261:"video/h261",h263:"video/h263",h264:"video/h264",heic:"image/heic",heics:"image/heic-sequence",heif:"image/heif",heifs:"image/heif-sequence",hej2:"image/hej2k",held:"application/atsc-held+xml",hjson:"application/hjson",hlp:"application/winhlp",hqx:"application/mac-binhex40",hsj2:"image/hsj2",htm:"text/html",html:"text/html",ics:"text/calendar",ief:"image/ief",ifb:"text/calendar",iges:"model/iges",igs:"model/iges",img:"application/octet-stream",in:"text/plain",ini:"text/plain",ink:"application/inkml+xml",inkml:"application/inkml+xml",ipfix:"application/ipfix",iso:"application/octet-stream",its:"application/its+xml",jade:"text/jade",jar:"application/java-archive",jhc:"image/jphc",jls:"image/jls",jp2:"image/jp2",jpe:"image/jpeg",jpeg:"image/jpeg",jpf:"image/jpx",jpg:"image/jpeg",jpg2:"image/jp2",jpgm:"image/jpm",jpgv:"video/jpeg",jph:"image/jph",jpm:"image/jpm",jpx:"image/jpx",js:"text/javascript",json:"application/json",json5:"application/json5",jsonld:"application/ld+json",jsonml:"application/jsonml+json",jsx:"text/jsx",jt:"model/jt",jxr:"image/jxr",jxra:"image/jxra",jxrs:"image/jxrs",jxs:"image/jxs",jxsc:"image/jxsc",jxsi:"image/jxsi",jxss:"image/jxss",kar:"audio/midi",ktx:"image/ktx",ktx2:"image/ktx2",less:"text/less",lgr:"application/lgr+xml",list:"text/plain",litcoffee:"text/coffeescript",log:"text/plain",lostxml:"application/lost+xml",lrf:"application/octet-stream",m1v:"video/mpeg",m21:"application/mp21",m2a:"audio/mpeg",m2v:"video/mpeg",m3a:"audio/mpeg",m4a:"audio/mp4",m4p:"application/mp4",m4s:"video/iso.segment",ma:"application/mathematica",mads:"application/mads+xml",maei:"application/mmt-aei+xml",man:"text/troff",manifest:"text/cache-manifest",map:"application/json",mar:"application/octet-stream",markdown:"text/markdown",mathml:"application/mathml+xml",mb:"application/mathematica",mbox:"application/mbox",md:"text/markdown",mdx:"text/mdx",me:"text/troff",mesh:"model/mesh",meta4:"application/metalink4+xml",metalink:"application/metalink+xml",mets:"application/mets+xml",mft:"application/rpki-manifest",mid:"audio/midi",midi:"audio/midi",mime:"message/rfc822",mj2:"video/mj2",mjp2:"video/mj2",mjs:"text/javascript",mml:"text/mathml",mods:"application/mods+xml",mov:"video/quicktime",mp2:"audio/mpeg",mp21:"application/mp21",mp2a:"audio/mpeg",mp3:"audio/mpeg",mp4:"video/mp4",mp4a:"audio/mp4",mp4s:"application/mp4",mp4v:"video/mp4",mpd:"application/dash+xml",mpe:"video/mpeg",mpeg:"video/mpeg",mpf:"application/media-policy-dataset+xml",mpg:"video/mpeg",mpg4:"video/mp4",mpga:"audio/mpeg",mpp:"application/dash-patch+xml",mrc:"application/marc",mrcx:"application/marcxml+xml",ms:"text/troff",mscml:"application/mediaservercontrol+xml",msh:"model/mesh",msi:"application/octet-stream",msix:"application/msix",msixbundle:"application/msixbundle",msm:"application/octet-stream",msp:"application/octet-stream",mtl:"model/mtl",musd:"application/mmt-usd+xml",mxf:"application/mxf",mxmf:"audio/mobile-xmf",mxml:"application/xv+xml",n3:"text/n3",nb:"application/mathematica",nq:"application/n-quads",nt:"application/n-triples",obj:"model/obj",oda:"application/oda",oga:"audio/ogg",ogg:"audio/ogg",ogv:"video/ogg",ogx:"application/ogg",omdoc:"application/omdoc+xml",onepkg:"application/onenote",onetmp:"application/onenote",onetoc:"application/onenote",onetoc2:"application/onenote",opf:"application/oebps-package+xml",opus:"audio/ogg",otf:"font/otf",owl:"application/rdf+xml",oxps:"application/oxps",p10:"application/pkcs10",p7c:"application/pkcs7-mime",p7m:"application/pkcs7-mime",p7s:"application/pkcs7-signature",p8:"application/pkcs8",pdf:"application/pdf",pfr:"application/font-tdpfr",pgp:"application/pgp-encrypted",pkg:"application/octet-stream",pki:"application/pkixcmp",pkipath:"application/pkix-pkipath",pls:"application/pls+xml",png:"image/png",prc:"model/prc",prf:"application/pics-rules",provx:"application/provenance+xml",ps:"application/postscript",pskcxml:"application/pskc+xml",pti:"image/prs.pti",qt:"video/quicktime",raml:"application/raml+yaml",rapd:"application/route-apd+xml",rdf:"application/rdf+xml",relo:"application/p2p-overlay+xml",rif:"application/reginfo+xml",rl:"application/resource-lists+xml",rld:"application/resource-lists-diff+xml",rmi:"audio/midi",rnc:"application/relax-ng-compact-syntax",rng:"application/xml",roa:"application/rpki-roa",roff:"text/troff",rq:"application/sparql-query",rs:"application/rls-services+xml",rsat:"application/atsc-rsat+xml",rsd:"application/rsd+xml",rsheet:"application/urc-ressheet+xml",rss:"application/rss+xml",rtf:"text/rtf",rtx:"text/richtext",rusd:"application/route-usd+xml",s3m:"audio/s3m",sbml:"application/sbml+xml",scq:"application/scvp-cv-request",scs:"application/scvp-cv-response",sdp:"application/sdp",senmlx:"application/senml+xml",sensmlx:"application/sensml+xml",ser:"application/java-serialized-object",setpay:"application/set-payment-initiation",setreg:"application/set-registration-initiation",sgi:"image/sgi",sgm:"text/sgml",sgml:"text/sgml",shex:"text/shex",shf:"application/shf+xml",shtml:"text/html",sieve:"application/sieve",sig:"application/pgp-signature",sil:"audio/silk",silo:"model/mesh",siv:"application/sieve",slim:"text/slim",slm:"text/slim",sls:"application/route-s-tsid+xml",smi:"application/smil+xml",smil:"application/smil+xml",snd:"audio/basic",so:"application/octet-stream",spdx:"text/spdx",spp:"application/scvp-vp-response",spq:"application/scvp-vp-request",spx:"audio/ogg",sql:"application/sql",sru:"application/sru+xml",srx:"application/sparql-results+xml",ssdl:"application/ssdl+xml",ssml:"application/ssml+xml",stk:"application/hyperstudio",stl:"model/stl",stpx:"model/step+xml",stpxz:"model/step-xml+zip",stpz:"model/step+zip",styl:"text/stylus",stylus:"text/stylus",svg:"image/svg+xml",svgz:"image/svg+xml",swidtag:"application/swid+xml",t:"text/troff",t38:"image/t38",td:"application/urc-targetdesc+xml",tei:"application/tei+xml",teicorpus:"application/tei+xml",text:"text/plain",tfi:"application/thraud+xml",tfx:"image/tiff-fx",tif:"image/tiff",tiff:"image/tiff",toml:"application/toml",tr:"text/troff",trig:"application/trig",ts:"video/mp2t",tsd:"application/timestamped-data",tsv:"text/tab-separated-values",ttc:"font/collection",ttf:"font/ttf",ttl:"text/turtle",ttml:"application/ttml+xml",txt:"text/plain",u3d:"model/u3d",u8dsn:"message/global-delivery-status",u8hdr:"message/global-headers",u8mdn:"message/global-disposition-notification",u8msg:"message/global",ubj:"application/ubjson",uri:"text/uri-list",uris:"text/uri-list",urls:"text/uri-list",vcard:"text/vcard",vrml:"model/vrml",vtt:"text/vtt",vxml:"application/voicexml+xml",war:"application/java-archive",wasm:"application/wasm",wav:"audio/wav",weba:"audio/webm",webm:"video/webm",webmanifest:"application/manifest+json",webp:"image/webp",wgsl:"text/wgsl",wgt:"application/widget",wif:"application/watcherinfo+xml",wmf:"image/wmf",woff:"font/woff",woff2:"font/woff2",wrl:"model/vrml",wsdl:"application/wsdl+xml",wspolicy:"application/wspolicy+xml",x3d:"model/x3d+xml",x3db:"model/x3d+fastinfoset",x3dbz:"model/x3d+binary",x3dv:"model/x3d-vrml",x3dvz:"model/x3d+vrml",x3dz:"model/x3d+xml",xaml:"application/xaml+xml",xav:"application/xcap-att+xml",xca:"application/xcap-caps+xml",xcs:"application/calendar+xml",xdf:"application/xcap-diff+xml",xdssc:"application/dssc+xml",xel:"application/xcap-el+xml",xenc:"application/xenc+xml",xer:"application/patch-ops-error+xml",xfdf:"application/xfdf",xht:"application/xhtml+xml",xhtml:"application/xhtml+xml",xhvml:"application/xv+xml",xlf:"application/xliff+xml",xm:"audio/xm",xml:"text/xml",xns:"application/xcap-ns+xml",xop:"application/xop+xml",xpl:"application/xproc+xml",xsd:"application/xml",xsf:"application/prs.xsf+xml",xsl:"application/xml",xslt:"application/xml",xspf:"application/xspf+xml",xvm:"application/xv+xml",xvml:"application/xv+xml",yaml:"text/yaml",yang:"application/yang",yin:"application/yin+xml",yml:"text/yaml",zip:"application/zip"};function Ci(a){let i=(""+a).trim().toLowerCase(),t=i.lastIndexOf(".");return Bi[~t?i.substring(++t):i]}const Yi=a=>{let i=["B","KB","MB","GB","PB"],t=0;for(;a>1024;)a/=1024,t++;let l=i[t];return a.toFixed(1)+" "+l},Qi=()=>!0;function Vi(a,{autoplay:i}){async function t(){i&&await a.play()}return a.addEventListener("loadeddata",t),{destroy(){a.removeEventListener("loadeddata",t)}}}async function Zi(){const a=new Mi,i="https://unpkg.com/@ffmpeg/core@0.12.4/dist/esm";return await a.load({coreURL:await W(`${i}/ffmpeg-core.js`,"text/javascript"),wasmURL:await W(`${i}/ffmpeg-core.wasm`,"application/wasm")}),a}async function $i(a,i,t,l){const e=l.src,c=Ci(l.src)||"video/mp4",u=await W(e,c),v=await(await fetch(u)).blob(),b=Pi(c)||"mp4",j=`input.${b}`,n=`output.${b}`;try{if(i===0&&t===0)return v;await a.writeFile(j,new Uint8Array(await v.arrayBuffer()));let f=["-i",j,...i!==0?["-ss",i.toString()]:[],...t!==0?["-to",t.toString()]:[],"-c:a","copy",n];await a.exec(f);const w=await a.readFile(n);return new Blob([w],{type:`video/${b}`})}catch(f){return console.error("Error initializing FFmpeg:",f),v}}const Pi=a=>({"video/mp4":"mp4","video/webm":"webm","video/ogg":"ogv","video/quicktime":"mov","video/x-msvideo":"avi","video/x-matroska":"mkv","video/mpeg":"mpeg","video/3gpp":"3gp","video/3gpp2":"3g2","video/h261":"h261","video/h263":"h263","video/h264":"h264","video/jpeg":"jpgv","video/jpm":"jpm","video/mj2":"mj2","video/mpv":"mpv","video/vnd.ms-playready.media.pyv":"pyv","video/vnd.uvvu.mp4":"uvu","video/vnd.vivo":"viv","video/x-f4v":"f4v","video/x-fli":"fli","video/x-flv":"flv","video/x-m4v":"m4v","video/x-ms-asf":"asf","video/x-ms-wm":"wm","video/x-ms-wmv":"wmv","video/x-ms-wmx":"wmx","video/x-ms-wvx":"wvx","video/x-sgi-movie":"movie","video/x-smv":"smv"})[a]||null;function Wi(a){let i,t='<span class="load-wrap svelte-1y0s5gv"><span class="loader svelte-1y0s5gv"></span></span>',l,e,c,u,g=!1,v,b=!0,j,n,f,w;const R=a[18].default,x=fi(R,a,a[17],null);function O(){cancelAnimationFrame(v),e.paused||(v=Di(O),g=!0),a[22].call(e)}return{c(){i=F("div"),i.innerHTML=t,l=xi(),e=F("video"),x&&x.c(),this.h()},l(s){i=X(s,"DIV",{class:!0,"data-svelte-h":!0}),ui(i)!=="svelte-mez4j5"&&(i.innerHTML=t),l=gi(s),e=X(s,"VIDEO",{src:!0,preload:!0,"data-testid":!0,crossorigin:!0});var r=vi(e);x&&x.l(r),r.forEach(q),this.h()},h(){L(i,"class","overlay svelte-1y0s5gv"),K(i,"hidden",!a[10]),J(e.src,c=a[11])||L(e,"src",c),e.muted=a[4],e.playsInline=a[5],L(e,"preload",a[6]),e.autoplay=a[7],e.controls=a[8],e.loop=a[9],L(e,"data-testid",u=a[13]["data-testid"]),L(e,"crossorigin","anonymous"),a[2]===void 0&&hi(()=>a[23].call(e))},m(s,r){V(s,i,r),V(s,l,r),V(s,e,r),x&&x.m(e,null),a[25](e),n=!0,f||(w=[d(e,"loadeddata",a[12].bind(null,"loadeddata")),d(e,"click",a[12].bind(null,"click")),d(e,"play",a[12].bind(null,"play")),d(e,"pause",a[12].bind(null,"pause")),d(e,"ended",a[12].bind(null,"ended")),d(e,"mouseover",a[12].bind(null,"mouseover")),d(e,"mouseout",a[12].bind(null,"mouseout")),d(e,"focus",a[12].bind(null,"focus")),d(e,"blur",a[12].bind(null,"blur")),d(e,"error",a[12].bind(null,"error","Video not playable")),d(e,"loadstart",a[19]),d(e,"loadeddata",a[20]),d(e,"loadedmetadata",a[21]),d(e,"timeupdate",O),d(e,"durationchange",a[23]),d(e,"play",a[24]),d(e,"pause",a[24]),Ei(j=Vi.call(null,e,{autoplay:a[7]??!1}))],f=!0)},p(s,[r]){(!n||r&1024)&&K(i,"hidden",!s[10]),x&&x.p&&(!n||r&131072)&&bi(x,R,s,s[17],n?wi(R,s[17],r,null):ji(s[17]),null),(!n||r&2048&&!J(e.src,c=s[11]))&&L(e,"src",c),(!n||r&16)&&(e.muted=s[4]),(!n||r&32)&&(e.playsInline=s[5]),(!n||r&64)&&L(e,"preload",s[6]),(!n||r&128)&&(e.autoplay=s[7]),(!n||r&256)&&(e.controls=s[8]),(!n||r&512)&&(e.loop=s[9]),(!n||r&8192&&u!==(u=s[13]["data-testid"]))&&L(e,"data-testid",u),!g&&r&2&&!isNaN(s[1])&&(e.currentTime=s[1]),g=!1,r&8&&b!==(b=s[3])&&e[b?"pause":"play"](),j&&yi(j.update)&&r&128&&j.update.call(null,{autoplay:s[7]??!1})},i(s){n||(Ri(x,s),n=!0)},o(s){_i(x,s),n=!1},d(s){s&&(q(i),q(l),q(e)),x&&x.d(s),a[25](null),f=!1,ki(w)}}}function Gi(a,i,t){let{$$slots:l={},$$scope:e}=i,{src:c=void 0}=i,{muted:u=void 0}=i,{playsinline:g=void 0}=i,{preload:v=void 0}=i,{autoplay:b=void 0}=i,{controls:j=void 0}=i,{currentTime:n=void 0}=i,{duration:f=void 0}=i,{paused:w=void 0}=i,{node:R=void 0}=i,{loop:x}=i,{is_stream:O}=i,{processingVideo:s=!1}=i,r,z=!1,M;const Z=Li();function $(o,B,G){if(!(!o||!B)&&N.isSupported()&&!z){const I=new N({maxBufferLength:1,maxMaxBufferLength:1,lowLatencyMode:!0});I.loadSource(o),I.attachMedia(G),I.on(N.Events.MANIFEST_PARSED,function(){G.play()}),I.on(N.Events.ERROR,function(si,C){if(console.error("HLS error:",si,C),C.fatal)switch(C.type){case N.ErrorTypes.NETWORK_ERROR:console.error("Fatal network error encountered, trying to recover"),I.startLoad();break;case N.ErrorTypes.MEDIA_ERROR:console.error("Fatal media error encountered, trying to recover"),I.recoverMediaError();break;default:console.error("Fatal error, cannot recover"),I.destroy();break}}),z=!0}}function ii(o){P.call(this,a,o)}function ai(o){P.call(this,a,o)}function ti(o){P.call(this,a,o)}function ei(){n=this.currentTime,t(1,n)}function oi(){f=this.duration,t(2,f)}function li(){w=this.paused,t(3,w)}function pi(o){Oi[o?"unshift":"push"](()=>{R=o,t(0,R)})}return a.$$set=o=>{t(13,i=Y(Y({},i),Q(o))),"src"in o&&t(14,c=o.src),"muted"in o&&t(4,u=o.muted),"playsinline"in o&&t(5,g=o.playsinline),"preload"in o&&t(6,v=o.preload),"autoplay"in o&&t(7,b=o.autoplay),"controls"in o&&t(8,j=o.controls),"currentTime"in o&&t(1,n=o.currentTime),"duration"in o&&t(2,f=o.duration),"paused"in o&&t(3,w=o.paused),"node"in o&&t(0,R=o.node),"loop"in o&&t(9,x=o.loop),"is_stream"in o&&t(15,O=o.is_stream),"processingVideo"in o&&t(10,s=o.processingVideo),"$$scope"in o&&t(17,e=o.$$scope)},a.$$.update=()=>{if(a.$$.dirty&81920){t(11,r=c),t(16,M=c);const o=c;Ii(o).then(B=>{M===o&&t(11,r=B)})}a.$$.dirty&16384&&(z=!1),a.$$.dirty&49153&&R&&c&&O&&$(c,O,R)},i=Q(i),[R,n,f,w,u,g,v,b,j,x,s,r,Z,i,c,O,M,e,l,ii,ai,ti,ei,oi,li,pi]}class ia extends ci{constructor(i){super(),ri(this,i,Gi,Wi,di,{src:14,muted:4,playsinline:5,preload:6,autoplay:7,controls:8,currentTime:1,duration:2,paused:3,node:0,loop:9,is_stream:15,processingVideo:10})}}export{ia as V,Qi as a,Zi as b,Vi as l,Yi as p,$i as t};
//# sourceMappingURL=Video.SijWdeHX.js.map
