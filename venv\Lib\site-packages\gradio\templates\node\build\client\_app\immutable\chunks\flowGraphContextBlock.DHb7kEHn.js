import{F as s}from"./KHR_interactivity.DEAVS2UW.js";import{R as r,b as t}from"./declarationMapper.UBCwU7BT.js";import{R as a}from"./index.BoI39RQH.js";class i extends s{constructor(e){super(e),this.userVariables=this.registerDataOutput("userVariables",r),this.executionId=this.registerDataOutput("executionId",t)}_updateOutputs(e){this.userVariables.setValue(e.userVariables,e),this.executionId.setValue(e.executionId,e)}serialize(e){super.serialize(e)}getClassName(){return"FlowGraphContextBlock"}}a("FlowGraphContextBlock",i);export{i as FlowGraphContextBlock};
//# sourceMappingURL=flowGraphContextBlock.DHb7kEHn.js.map
