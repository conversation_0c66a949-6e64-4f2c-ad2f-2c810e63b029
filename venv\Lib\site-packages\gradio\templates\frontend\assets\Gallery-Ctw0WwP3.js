/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import{B as Qe}from"./BlockLabel-3KxTaaiM.js";import{I as Ee}from"./IconButton-C_HS7fTi.js";import{E as Ye}from"./Empty-ZqppqzTN.js";import{S as Ze}from"./ShareButton-BuIiIMKb.js";import{C as xe}from"./Clear-By3xiIwg.js";import{D as et}from"./Download-DVtk-Jv3.js";import{I as Le}from"./Image-Bsh8Umrh.js";import{P as Te}from"./Play-B0Q0U1Qz.js";import{I as tt}from"./IconButtonWrapper--EIOWuEM.js";import{F as lt}from"./FullscreenButton-jgOGhOHz.js";import"./index-B7J2Z2jS.js";/* empty css                                             */import{M as nt}from"./ModifyUpload-5lgGIpMc.js";import{I as fe}from"./Image-CnqB5dbD.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";/* empty css                                                   */import{V as ue}from"./Video-DtShVFLe.js";import{d as it}from"./index-tFQomdd2.js";import{u as ot}from"./utils-BsGrhMNe.js";import"./prism-python-MMh3z1bK.js";import"./Community-Dw1micSV.js";import"./svelte/svelte.js";import"./Edit-BpRIf5rU.js";import"./Undo-DCjBnnSO.js";import"./DownloadLink-QIttOhoR.js";import"./file-url-DoxvUUVV.js";import"./hls-CnVhpNcu.js";async function rt(l){return l?`<div style="display: flex; flex-wrap: wrap; gap: 16px">${(await Promise.all(l.map(async([n,t])=>n===null||!n.url?"":await ot(n.url)))).map(n=>`<img src="${n}" style="height: 400px" />`).join("")}</div>`:""}const{SvelteComponent:st,add_render_callback:ft,append:D,attr:B,binding_callbacks:re,bubble:se,check_outros:$,create_component:E,destroy_component:L,destroy_each:Me,detach:M,element:P,empty:Re,ensure_array_like:le,flush:z,globals:ut,group_outros:U,init:at,insert:R,is_function:ct,listen:Z,mount_component:T,noop:_t,run_all:mt,safe_not_equal:ht,set_data:Ce,set_style:F,space:N,text:De,toggle_class:C,transition_in:h,transition_out:d}=window.__gradio__svelte__internal,{window:Ne}=ut,{createEventDispatcher:gt,onMount:bt}=window.__gradio__svelte__internal,{tick:dt}=window.__gradio__svelte__internal;function he(l,e,n){const t=l.slice();return t[51]=e[n],t[53]=n,t}function ge(l,e,n){const t=l.slice();return t[54]=e[n],t[55]=e,t[53]=n,t}function be(l){let e,n;return e=new Qe({props:{show_label:l[2],Icon:Le,label:l[3]||"Gallery"}}),{c(){E(e.$$.fragment)},m(t,o){T(e,t,o),n=!0},p(t,o){const i={};o[0]&4&&(i.show_label=t[2]),o[0]&8&&(i.label=t[3]||"Gallery"),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function pt(l){let e,n,t,o,i,f,c=l[24]&&l[7]&&de(l),s=l[12]&&l[1]===null&&ze(l),g=le(l[17]),u=[];for(let a=0;a<g.length;a+=1)u[a]=Be(he(l,g,a));const b=a=>d(u[a],1,1,()=>{u[a]=null});return{c(){e=P("div"),c&&c.c(),n=N(),t=P("div"),s&&s.c(),o=N(),i=P("div");for(let a=0;a<u.length;a+=1)u[a].c();B(i,"class","grid-container svelte-1atirkn"),F(i,"--grid-cols",l[4]),F(i,"--grid-rows",l[5]),F(i,"--object-fit",l[8]),F(i,"height",l[6]),C(i,"pt-6",l[2]),B(t,"class","grid-wrap svelte-1atirkn"),C(t,"minimal",l[13]==="minimal"),C(t,"fixed-height",l[13]!=="minimal"&&(!l[6]||l[6]=="auto")),C(t,"hidden",l[19]),B(e,"class","gallery-container")},m(a,_){R(a,e,_),c&&c.m(e,null),D(e,n),D(e,t),s&&s.m(t,null),D(t,o),D(t,i);for(let p=0;p<u.length;p+=1)u[p]&&u[p].m(i,null);l[46](e),f=!0},p(a,_){if(a[24]&&a[7]?c?(c.p(a,_),_[0]&16777344&&h(c,1)):(c=de(a),c.c(),h(c,1),c.m(e,n)):c&&(U(),d(c,1,1,()=>{c=null}),$()),a[12]&&a[1]===null?s?(s.p(a,_),_[0]&4098&&h(s,1)):(s=ze(a),s.c(),h(s,1),s.m(t,o)):s&&(U(),d(s,1,1,()=>{s=null}),$()),_[0]&33685634){g=le(a[17]);let p;for(p=0;p<g.length;p+=1){const j=he(a,g,p);u[p]?(u[p].p(j,_),h(u[p],1)):(u[p]=Be(j),u[p].c(),h(u[p],1),u[p].m(i,null))}for(U(),p=g.length;p<u.length;p+=1)b(p);$()}(!f||_[0]&16)&&F(i,"--grid-cols",a[4]),(!f||_[0]&32)&&F(i,"--grid-rows",a[5]),(!f||_[0]&256)&&F(i,"--object-fit",a[8]),(!f||_[0]&64)&&F(i,"height",a[6]),(!f||_[0]&4)&&C(i,"pt-6",a[2]),(!f||_[0]&8192)&&C(t,"minimal",a[13]==="minimal"),(!f||_[0]&8256)&&C(t,"fixed-height",a[13]!=="minimal"&&(!a[6]||a[6]=="auto")),(!f||_[0]&524288)&&C(t,"hidden",a[19])},i(a){if(!f){h(c),h(s);for(let _=0;_<g.length;_+=1)h(u[_]);f=!0}},o(a){d(c),d(s),u=u.filter(Boolean);for(let _=0;_<u.length;_+=1)d(u[_]);f=!1},d(a){a&&M(e),c&&c.d(),s&&s.d(),Me(u,a),l[46](null)}}}function wt(l){let e,n;return e=new Ye({props:{unpadded_box:!0,size:"large",$$slots:{default:[Et]},$$scope:{ctx:l}}}),{c(){E(e.$$.fragment)},m(t,o){T(e,t,o),n=!0},p(t,o){const i={};o[1]&33554432&&(i.$$scope={dirty:o,ctx:t}),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function de(l){let e,n,t,o,i,f,c,s,g,u,b,a;n=new tt({props:{display_top_corner:l[15],$$slots:{default:[kt]},$$scope:{ctx:l}}});const _=[yt,vt],p=[];function j(k,v){return"image"in k[24]?0:1}i=j(l),f=p[i]=_[i](l);let w=l[24]?.caption&&ye(l),y=le(l[17]),m=[];for(let k=0;k<y.length;k+=1)m[k]=je(ge(l,y,k));const x=k=>d(m[k],1,1,()=>{m[k]=null});return{c(){e=P("button"),E(n.$$.fragment),t=N(),o=P("button"),f.c(),c=N(),w&&w.c(),s=N(),g=P("div");for(let k=0;k<m.length;k+=1)m[k].c();B(o,"class","media-button svelte-1atirkn"),F(o,"height","calc(100% - "+(l[24].caption?"80px":"60px")+")"),B(o,"aria-label","detailed view of selected image"),B(g,"class","thumbnails scroll-hide svelte-1atirkn"),B(g,"data-testid","container_el"),F(g,"justify-content",l[23]?"flex-start":"center"),B(e,"class","preview svelte-1atirkn"),C(e,"minimal",l[13]==="minimal")},m(k,v){R(k,e,v),T(n,e,null),D(e,t),D(e,o),p[i].m(o,null),D(e,c),w&&w.m(e,null),D(e,s),D(e,g);for(let G=0;G<m.length;G+=1)m[G]&&m[G].m(g,null);l[43](g),u=!0,b||(a=[Z(o,"click",function(){ct("image"in l[24]?l[40]:null)&&("image"in l[24]?l[40]:null).apply(this,arguments)}),Z(e,"keydown",l[27])],b=!0)},p(k,v){l=k;const G={};v[0]&32768&&(G.display_top_corner=l[15]),v[0]&17518082|v[1]&33554432&&(G.$$scope={dirty:v,ctx:l}),n.$set(G);let H=i;if(i=j(l),i===H?p[i].p(l,v):(U(),d(p[H],1,1,()=>{p[H]=null}),$(),f=p[i],f?f.p(l,v):(f=p[i]=_[i](l),f.c()),h(f,1),f.m(o,null)),(!u||v[0]&16777216)&&F(o,"height","calc(100% - "+(l[24].caption?"80px":"60px")+")"),l[24]?.caption?w?w.p(l,v):(w=ye(l),w.c(),w.m(e,s)):w&&(w.d(1),w=null),v[0]&2236418){y=le(l[17]);let I;for(I=0;I<y.length;I+=1){const X=ge(l,y,I);m[I]?(m[I].p(X,v),h(m[I],1)):(m[I]=je(X),m[I].c(),h(m[I],1),m[I].m(g,null))}for(U(),I=y.length;I<m.length;I+=1)x(I);$()}(!u||v[0]&8388608)&&F(g,"justify-content",l[23]?"flex-start":"center"),(!u||v[0]&8192)&&C(e,"minimal",l[13]==="minimal")},i(k){if(!u){h(n.$$.fragment,k),h(f);for(let v=0;v<y.length;v+=1)h(m[v]);u=!0}},o(k){d(n.$$.fragment,k),d(f),m=m.filter(Boolean);for(let v=0;v<m.length;v+=1)d(m[v]);u=!1},d(k){k&&M(e),L(n),p[i].d(),w&&w.d(),Me(m,k),l[43](null),b=!1,mt(a)}}}function pe(l){let e,n;return e=new Ee({props:{Icon:et,label:l[11]("common.download")}}),e.$on("click",l[35]),{c(){E(e.$$.fragment)},m(t,o){T(e,t,o),n=!0},p(t,o){const i={};o[0]&2048&&(i.label=t[11]("common.download")),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function we(l){let e,n;return e=new lt({props:{fullscreen:l[16]}}),e.$on("fullscreen",l[36]),{c(){E(e.$$.fragment)},m(t,o){T(e,t,o),n=!0},p(t,o){const i={};o[0]&65536&&(i.fullscreen=t[16]),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function ke(l){let e,n,t;return n=new Ze({props:{i18n:l[11],value:l[17],formatter:rt}}),n.$on("share",l[37]),n.$on("error",l[38]),{c(){e=P("div"),E(n.$$.fragment),B(e,"class","icon-button")},m(o,i){R(o,e,i),T(n,e,null),t=!0},p(o,i){const f={};i[0]&2048&&(f.i18n=o[11]),i[0]&131072&&(f.value=o[17]),n.$set(f)},i(o){t||(h(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&M(e),L(n)}}}function ve(l){let e,n;return e=new Ee({props:{Icon:xe,label:"Close"}}),e.$on("click",l[39]),{c(){E(e.$$.fragment)},m(t,o){T(e,t,o),n=!0},p:_t,i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function kt(l){let e,n,t,o,i,f=l[10]&&pe(l),c=l[14]&&we(l),s=l[9]&&ke(l),g=!l[19]&&ve(l);return{c(){f&&f.c(),e=N(),c&&c.c(),n=N(),s&&s.c(),t=N(),g&&g.c(),o=Re()},m(u,b){f&&f.m(u,b),R(u,e,b),c&&c.m(u,b),R(u,n,b),s&&s.m(u,b),R(u,t,b),g&&g.m(u,b),R(u,o,b),i=!0},p(u,b){u[10]?f?(f.p(u,b),b[0]&1024&&h(f,1)):(f=pe(u),f.c(),h(f,1),f.m(e.parentNode,e)):f&&(U(),d(f,1,1,()=>{f=null}),$()),u[14]?c?(c.p(u,b),b[0]&16384&&h(c,1)):(c=we(u),c.c(),h(c,1),c.m(n.parentNode,n)):c&&(U(),d(c,1,1,()=>{c=null}),$()),u[9]?s?(s.p(u,b),b[0]&512&&h(s,1)):(s=ke(u),s.c(),h(s,1),s.m(t.parentNode,t)):s&&(U(),d(s,1,1,()=>{s=null}),$()),u[19]?g&&(U(),d(g,1,1,()=>{g=null}),$()):g?(g.p(u,b),b[0]&524288&&h(g,1)):(g=ve(u),g.c(),h(g,1),g.m(o.parentNode,o))},i(u){i||(h(f),h(c),h(s),h(g),i=!0)},o(u){d(f),d(c),d(s),d(g),i=!1},d(u){u&&(M(e),M(n),M(t),M(o)),f&&f.d(u),c&&c.d(u),s&&s.d(u),g&&g.d(u)}}}function vt(l){let e,n;return e=new ue({props:{src:l[24].video.url,"data-testid":"detailed-video",alt:l[24].caption||"",loading:"lazy",loop:!1,is_stream:!1,muted:!1,controls:!0}}),{c(){E(e.$$.fragment)},m(t,o){T(e,t,o),n=!0},p(t,o){const i={};o[0]&16777216&&(i.src=t[24].video.url),o[0]&16777216&&(i.alt=t[24].caption||""),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function yt(l){let e,n;return e=new fe({props:{"data-testid":"detailed-image",src:l[24].image.url,alt:l[24].caption||"",title:l[24].caption||null,class:l[24].caption&&"with-caption",loading:"lazy"}}),{c(){E(e.$$.fragment)},m(t,o){T(e,t,o),n=!0},p(t,o){const i={};o[0]&16777216&&(i.src=t[24].image.url),o[0]&16777216&&(i.alt=t[24].caption||""),o[0]&16777216&&(i.title=t[24].caption||null),o[0]&16777216&&(i.class=t[24].caption&&"with-caption"),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function ye(l){let e,n=l[24].caption+"",t;return{c(){e=P("caption"),t=De(n),B(e,"class","caption svelte-1atirkn")},m(o,i){R(o,e,i),D(e,t)},p(o,i){i[0]&16777216&&n!==(n=o[24].caption+"")&&Ce(t,n)},d(o){o&&M(e)}}}function jt(l){let e,n,t,o;return e=new Te({}),t=new ue({props:{src:l[54].video.url,title:l[54].caption||null,is_stream:!1,"data-testid":"thumbnail "+(l[53]+1),alt:"",loading:"lazy",loop:!1}}),{c(){E(e.$$.fragment),n=N(),E(t.$$.fragment)},m(i,f){T(e,i,f),R(i,n,f),T(t,i,f),o=!0},p(i,f){const c={};f[0]&131072&&(c.src=i[54].video.url),f[0]&131072&&(c.title=i[54].caption||null),t.$set(c)},i(i){o||(h(e.$$.fragment,i),h(t.$$.fragment,i),o=!0)},o(i){d(e.$$.fragment,i),d(t.$$.fragment,i),o=!1},d(i){i&&M(n),L(e,i),L(t,i)}}}function zt(l){let e,n;return e=new fe({props:{src:l[54].image.url,title:l[54].caption||null,"data-testid":"thumbnail "+(l[53]+1),alt:"",loading:"lazy"}}),{c(){E(e.$$.fragment)},m(t,o){T(e,t,o),n=!0},p(t,o){const i={};o[0]&131072&&(i.src=t[54].image.url),o[0]&131072&&(i.title=t[54].caption||null),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function je(l){let e,n,t,o,i,f=l[53],c,s,g;const u=[zt,jt],b=[];function a(w,y){return"image"in w[54]?0:1}n=a(l),t=b[n]=u[n](l);const _=()=>l[41](e,f),p=()=>l[41](null,f);function j(){return l[42](l[53])}return{c(){e=P("button"),t.c(),o=N(),B(e,"class","thumbnail-item thumbnail-small svelte-1atirkn"),B(e,"aria-label",i="Thumbnail "+(l[53]+1)+" of "+l[17].length),C(e,"selected",l[1]===l[53]&&l[13]!=="minimal")},m(w,y){R(w,e,y),b[n].m(e,null),D(e,o),_(),c=!0,s||(g=Z(e,"click",j),s=!0)},p(w,y){l=w;let m=n;n=a(l),n===m?b[n].p(l,y):(U(),d(b[m],1,1,()=>{b[m]=null}),$(),t=b[n],t?t.p(l,y):(t=b[n]=u[n](l),t.c()),h(t,1),t.m(e,o)),(!c||y[0]&131072&&i!==(i="Thumbnail "+(l[53]+1)+" of "+l[17].length))&&B(e,"aria-label",i),f!==l[53]&&(p(),f=l[53],_()),(!c||y[0]&8194)&&C(e,"selected",l[1]===l[53]&&l[13]!=="minimal")},i(w){c||(h(t),c=!0)},o(w){d(t),c=!1},d(w){w&&M(e),b[n].d(),p(),s=!1,g()}}}function ze(l){let e,n;return e=new nt({props:{i18n:l[11]}}),e.$on("clear",l[44]),{c(){E(e.$$.fragment)},m(t,o){T(e,t,o),n=!0},p(t,o){const i={};o[0]&2048&&(i.i18n=t[11]),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function It(l){let e,n,t,o;return e=new Te({}),t=new ue({props:{src:l[51].video.url,title:l[51].caption||null,is_stream:!1,"data-testid":"thumbnail "+(l[53]+1),alt:"",loading:"lazy",loop:!1}}),{c(){E(e.$$.fragment),n=N(),E(t.$$.fragment)},m(i,f){T(e,i,f),R(i,n,f),T(t,i,f),o=!0},p(i,f){const c={};f[0]&131072&&(c.src=i[51].video.url),f[0]&131072&&(c.title=i[51].caption||null),t.$set(c)},i(i){o||(h(e.$$.fragment,i),h(t.$$.fragment,i),o=!0)},o(i){d(e.$$.fragment,i),d(t.$$.fragment,i),o=!1},d(i){i&&M(n),L(e,i),L(t,i)}}}function Bt(l){let e,n;return e=new fe({props:{alt:l[51].caption||"",src:typeof l[51].image=="string"?l[51].image:l[51].image.url,loading:"lazy"}}),{c(){E(e.$$.fragment)},m(t,o){T(e,t,o),n=!0},p(t,o){const i={};o[0]&131072&&(i.alt=t[51].caption||""),o[0]&131072&&(i.src=typeof t[51].image=="string"?t[51].image:t[51].image.url),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function Ie(l){let e,n=l[51].caption+"",t;return{c(){e=P("div"),t=De(n),B(e,"class","caption-label svelte-1atirkn")},m(o,i){R(o,e,i),D(e,t)},p(o,i){i[0]&131072&&n!==(n=o[51].caption+"")&&Ce(t,n)},d(o){o&&M(e)}}}function Be(l){let e,n,t,o,i,f,c,s,g;const u=[Bt,It],b=[];function a(j,w){return"image"in j[51]?0:1}n=a(l),t=b[n]=u[n](l);let _=l[51].caption&&Ie(l);function p(){return l[45](l[53])}return{c(){e=P("button"),t.c(),o=N(),_&&_.c(),i=N(),B(e,"class","thumbnail-item thumbnail-lg svelte-1atirkn"),B(e,"aria-label",f="Thumbnail "+(l[53]+1)+" of "+l[17].length),C(e,"selected",l[1]===l[53])},m(j,w){R(j,e,w),b[n].m(e,null),D(e,o),_&&_.m(e,null),D(e,i),c=!0,s||(g=Z(e,"click",p),s=!0)},p(j,w){l=j;let y=n;n=a(l),n===y?b[n].p(l,w):(U(),d(b[y],1,1,()=>{b[y]=null}),$(),t=b[n],t?t.p(l,w):(t=b[n]=u[n](l),t.c()),h(t,1),t.m(e,o)),l[51].caption?_?_.p(l,w):(_=Ie(l),_.c(),_.m(e,i)):_&&(_.d(1),_=null),(!c||w[0]&131072&&f!==(f="Thumbnail "+(l[53]+1)+" of "+l[17].length))&&B(e,"aria-label",f),(!c||w[0]&2)&&C(e,"selected",l[1]===l[53])},i(j){c||(h(t),c=!0)},o(j){d(t),c=!1},d(j){j&&M(e),b[n].d(),_&&_.d(),s=!1,g()}}}function Et(l){let e,n;return e=new Le({}),{c(){E(e.$$.fragment)},m(t,o){T(e,t,o),n=!0},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function Lt(l){let e,n,t,o,i,f,c;ft(l[34]);let s=l[2]&&be(l);const g=[wt,pt],u=[];function b(a,_){return a[0]==null||a[17]==null||a[17].length===0?0:1}return n=b(l),t=u[n]=g[n](l),{c(){s&&s.c(),e=N(),t.c(),o=Re()},m(a,_){s&&s.m(a,_),R(a,e,_),u[n].m(a,_),R(a,o,_),i=!0,f||(c=Z(Ne,"resize",l[34]),f=!0)},p(a,_){a[2]?s?(s.p(a,_),_[0]&4&&h(s,1)):(s=be(a),s.c(),h(s,1),s.m(e.parentNode,e)):s&&(U(),d(s,1,1,()=>{s=null}),$());let p=n;n=b(a),n===p?u[n].p(a,_):(U(),d(u[p],1,1,()=>{u[p]=null}),$(),t=u[n],t?t.p(a,_):(t=u[n]=g[n](a),t.c()),h(t,1),t.m(o.parentNode,o))},i(a){i||(h(s),h(t),i=!0)},o(a){d(s),d(t),i=!1},d(a){a&&(M(e),M(o)),s&&s.d(a),u[n].d(a),f=!1,c()}}}function Tt(l,e,n){let t,o,i,{show_label:f=!0}=e,{label:c}=e,{value:s=null}=e,{columns:g=[2]}=e,{rows:u=void 0}=e,{height:b="auto"}=e,{preview:a}=e,{allow_preview:_=!0}=e,{object_fit:p="cover"}=e,{show_share_button:j=!1}=e,{show_download_button:w=!1}=e,{i18n:y}=e,{selected_index:m=null}=e,{interactive:x}=e,{_fetch:k}=e,{mode:v="normal"}=e,{show_fullscreen_button:G=!0}=e,{display_icon_button_wrapper_top_corner:H=!1}=e,{fullscreen:I=!1}=e,X=!1,ne;const J=gt();let K=!0,S=null,ie=s;m==null&&a&&s?.length&&(m=0);let oe=m;function ae(r){const q=r.target,A=r.offsetX,V=q.offsetWidth/2;A<V?n(1,m=t):n(1,m=o)}function $e(r){switch(r.code){case"Escape":r.preventDefault(),n(1,m=null);break;case"ArrowLeft":r.preventDefault(),n(1,m=t);break;case"ArrowRight":r.preventDefault(),n(1,m=o);break}}let O=[],W;async function Ue(r){if(typeof r!="number"||(await dt(),O[r]===void 0))return;O[r]?.focus();const{left:q,width:A}=W.getBoundingClientRect(),{left:ee,width:V}=O[r].getBoundingClientRect(),Y=ee-q+V/2-A/2+W.scrollLeft;W&&typeof W.scrollTo=="function"&&W.scrollTo({left:Y<0?0:Y,behavior:"smooth"})}let ce=0;async function _e(r,q){let A;try{A=await k(r)}catch(Y){if(Y instanceof TypeError){window.open(r,"_blank","noreferrer");return}throw Y}const ee=await A.blob(),V=URL.createObjectURL(ee),te=document.createElement("a");te.href=V,te.download=q,te.click(),URL.revokeObjectURL(V)}let me=!1;function Q(){W&&n(23,me=W.scrollWidth>W.clientWidth)}bt(()=>(Q(),document.addEventListener("fullscreenchange",()=>{n(19,X=!!document.fullscreenElement)}),window.addEventListener("resize",Q),()=>window.removeEventListener("resize",Q)));function We(){n(22,ce=Ne.innerHeight)}const Fe=()=>{const r="image"in i?i?.image:i?.video;if(r==null)return;const{url:q,orig_name:A}=r;q&&_e(q,A??"image")};function Ge(r){se.call(this,l,r)}function Pe(r){se.call(this,l,r)}function Se(r){se.call(this,l,r)}const qe=()=>{n(1,m=null),J("preview_close")},Ae=r=>ae(r);function He(r,q){re[r?"unshift":"push"](()=>{O[q]=r,n(21,O)})}const Oe=r=>n(1,m=r);function Ve(r){re[r?"unshift":"push"](()=>{W=r,n(18,W)})}const Xe=()=>n(0,s=[]),Je=r=>{m===null&&_&&J("preview_open"),n(1,m=r)};function Ke(r){re[r?"unshift":"push"](()=>{ne=r,n(20,ne)})}return l.$$set=r=>{"show_label"in r&&n(2,f=r.show_label),"label"in r&&n(3,c=r.label),"value"in r&&n(0,s=r.value),"columns"in r&&n(4,g=r.columns),"rows"in r&&n(5,u=r.rows),"height"in r&&n(6,b=r.height),"preview"in r&&n(29,a=r.preview),"allow_preview"in r&&n(7,_=r.allow_preview),"object_fit"in r&&n(8,p=r.object_fit),"show_share_button"in r&&n(9,j=r.show_share_button),"show_download_button"in r&&n(10,w=r.show_download_button),"i18n"in r&&n(11,y=r.i18n),"selected_index"in r&&n(1,m=r.selected_index),"interactive"in r&&n(12,x=r.interactive),"_fetch"in r&&n(30,k=r._fetch),"mode"in r&&n(13,v=r.mode),"show_fullscreen_button"in r&&n(14,G=r.show_fullscreen_button),"display_icon_button_wrapper_top_corner"in r&&n(15,H=r.display_icon_button_wrapper_top_corner),"fullscreen"in r&&n(16,I=r.fullscreen)},l.$$.update=()=>{l.$$.dirty[0]&1|l.$$.dirty[1]&1&&n(31,K=s==null||s.length===0?!0:K),l.$$.dirty[0]&1&&n(17,S=s==null?null:s.map(r=>"video"in r?{video:r.video,caption:r.caption}:"image"in r?{image:r.image,caption:r.caption}:{})),l.$$.dirty[0]&536870915|l.$$.dirty[1]&3&&(it(ie,s)||(K?(n(1,m=a&&s?.length?0:null),n(31,K=!1)):m!==null&&s!==null?n(1,m=Math.max(0,Math.min(m,s.length-1))):n(1,m=null),J("change"),n(32,ie=s))),l.$$.dirty[0]&131074|l.$$.dirty[1]&4&&m!==oe&&(n(33,oe=m),m!==null&&(S!=null&&n(1,m=Math.max(0,Math.min(m,S.length-1))),J("select",{index:m,value:S?.[m]}))),l.$$.dirty[0]&131074&&(t=((m??0)+(S?.length??0)-1)%(S?.length??0)),l.$$.dirty[0]&131074&&(o=((m??0)+1)%(S?.length??0)),l.$$.dirty[0]&130&&_&&Ue(m),l.$$.dirty[0]&131074&&n(24,i=m!=null&&S!=null?S[m]:null),l.$$.dirty[0]&131072&&Q(),l.$$.dirty[0]&262144&&W&&Q()},[s,m,f,c,g,u,b,_,p,j,w,y,x,v,G,H,I,S,W,X,ne,O,ce,me,i,J,ae,$e,_e,a,k,K,ie,oe,We,Fe,Ge,Pe,Se,qe,Ae,He,Oe,Ve,Xe,Je,Ke]}class ol extends st{constructor(e){super(),at(this,e,Tt,Lt,ht,{show_label:2,label:3,value:0,columns:4,rows:5,height:6,preview:29,allow_preview:7,object_fit:8,show_share_button:9,show_download_button:10,i18n:11,selected_index:1,interactive:12,_fetch:30,mode:13,show_fullscreen_button:14,display_icon_button_wrapper_top_corner:15,fullscreen:16},null,[-1,-1])}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),z()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),z()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),z()}get columns(){return this.$$.ctx[4]}set columns(e){this.$$set({columns:e}),z()}get rows(){return this.$$.ctx[5]}set rows(e){this.$$set({rows:e}),z()}get height(){return this.$$.ctx[6]}set height(e){this.$$set({height:e}),z()}get preview(){return this.$$.ctx[29]}set preview(e){this.$$set({preview:e}),z()}get allow_preview(){return this.$$.ctx[7]}set allow_preview(e){this.$$set({allow_preview:e}),z()}get object_fit(){return this.$$.ctx[8]}set object_fit(e){this.$$set({object_fit:e}),z()}get show_share_button(){return this.$$.ctx[9]}set show_share_button(e){this.$$set({show_share_button:e}),z()}get show_download_button(){return this.$$.ctx[10]}set show_download_button(e){this.$$set({show_download_button:e}),z()}get i18n(){return this.$$.ctx[11]}set i18n(e){this.$$set({i18n:e}),z()}get selected_index(){return this.$$.ctx[1]}set selected_index(e){this.$$set({selected_index:e}),z()}get interactive(){return this.$$.ctx[12]}set interactive(e){this.$$set({interactive:e}),z()}get _fetch(){return this.$$.ctx[30]}set _fetch(e){this.$$set({_fetch:e}),z()}get mode(){return this.$$.ctx[13]}set mode(e){this.$$set({mode:e}),z()}get show_fullscreen_button(){return this.$$.ctx[14]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),z()}get display_icon_button_wrapper_top_corner(){return this.$$.ctx[15]}set display_icon_button_wrapper_top_corner(e){this.$$set({display_icon_button_wrapper_top_corner:e}),z()}get fullscreen(){return this.$$.ctx[16]}set fullscreen(e){this.$$set({fullscreen:e}),z()}}export{ol as default};
//# sourceMappingURL=Gallery-Ctw0WwP3.js.map
