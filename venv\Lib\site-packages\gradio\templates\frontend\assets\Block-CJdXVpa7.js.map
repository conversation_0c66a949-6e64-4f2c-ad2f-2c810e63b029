{"version": 3, "file": "Block-CJdXVpa7.js", "sources": ["../../../../js/atoms/src/Block.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let height: number | string | undefined = undefined;\n\texport let min_height: number | string | undefined = undefined;\n\texport let max_height: number | string | undefined = undefined;\n\texport let width: number | string | undefined = undefined;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let variant: \"solid\" | \"dashed\" | \"none\" = \"solid\";\n\texport let border_mode: \"base\" | \"focus\" | \"contrast\" = \"base\";\n\texport let padding = true;\n\texport let type: \"normal\" | \"fieldset\" = \"normal\";\n\texport let test_id: string | undefined = undefined;\n\texport let explicit_call = false;\n\texport let container = true;\n\texport let visible = true;\n\texport let allow_overflow = true;\n\texport let overflow_behavior: \"visible\" | \"auto\" = \"auto\";\n\texport let scale: number | null = null;\n\texport let min_width = 0;\n\texport let flex = false;\n\texport let resizable = false;\n\texport let rtl = false;\n\texport let fullscreen = false;\n\tlet old_fullscreen = fullscreen;\n\n\tlet element: HTMLElement;\n\n\tlet tag = type === \"fieldset\" ? \"fieldset\" : \"div\";\n\n\tlet placeholder_height = 0;\n\tlet placeholder_width = 0;\n\tlet preexpansionBoundingRect: DOMRect | null = null;\n\n\tfunction handleKeydown(event: KeyboardEvent): void {\n\t\tif (fullscreen && event.key === \"Escape\") {\n\t\t\tfullscreen = false;\n\t\t}\n\t}\n\n\t$: if (fullscreen !== old_fullscreen) {\n\t\told_fullscreen = fullscreen;\n\t\tif (fullscreen) {\n\t\t\tpreexpansionBoundingRect = element.getBoundingClientRect();\n\t\t\tplaceholder_height = element.offsetHeight;\n\t\t\tplaceholder_width = element.offsetWidth;\n\t\t\twindow.addEventListener(\"keydown\", handleKeydown);\n\t\t} else {\n\t\t\tpreexpansionBoundingRect = null;\n\t\t\twindow.removeEventListener(\"keydown\", handleKeydown);\n\t\t}\n\t}\n\n\tconst get_dimension = (\n\t\tdimension_value: string | number | undefined\n\t): string | undefined => {\n\t\tif (dimension_value === undefined) {\n\t\t\treturn undefined;\n\t\t}\n\t\tif (typeof dimension_value === \"number\") {\n\t\t\treturn dimension_value + \"px\";\n\t\t} else if (typeof dimension_value === \"string\") {\n\t\t\treturn dimension_value;\n\t\t}\n\t};\n\n\t$: if (!visible) {\n\t\tflex = false;\n\t}\n\n\tconst resize = (e: MouseEvent): void => {\n\t\tlet prevY = e.clientY;\n\t\tconst onMouseMove = (e: MouseEvent): void => {\n\t\t\tconst dy: number = e.clientY - prevY;\n\t\t\tprevY = e.clientY;\n\t\t\telement.style.height = `${element.offsetHeight + dy}px`;\n\t\t};\n\t\tconst onMouseUp = (): void => {\n\t\t\twindow.removeEventListener(\"mousemove\", onMouseMove);\n\t\t\twindow.removeEventListener(\"mouseup\", onMouseUp);\n\t\t};\n\t\twindow.addEventListener(\"mousemove\", onMouseMove);\n\t\twindow.addEventListener(\"mouseup\", onMouseUp);\n\t};\n</script>\n\n<svelte:element\n\tthis={tag}\n\tbind:this={element}\n\tdata-testid={test_id}\n\tid={elem_id}\n\tclass:hidden={visible === false}\n\tclass=\"block {elem_classes?.join(' ') || ''}\"\n\tclass:padded={padding}\n\tclass:flex\n\tclass:border_focus={border_mode === \"focus\"}\n\tclass:border_contrast={border_mode === \"contrast\"}\n\tclass:hide-container={!explicit_call && !container}\n\tstyle:height={fullscreen ? undefined : get_dimension(height)}\n\tstyle:min-height={fullscreen ? undefined : get_dimension(min_height)}\n\tstyle:max-height={fullscreen ? undefined : get_dimension(max_height)}\n\tclass:fullscreen\n\tclass:animating={fullscreen && preexpansionBoundingRect !== null}\n\tstyle:--start-top={preexpansionBoundingRect\n\t\t? `${preexpansionBoundingRect.top}px`\n\t\t: \"0px\"}\n\tstyle:--start-left={preexpansionBoundingRect\n\t\t? `${preexpansionBoundingRect.left}px`\n\t\t: \"0px\"}\n\tstyle:--start-width={preexpansionBoundingRect\n\t\t? `${preexpansionBoundingRect.width}px`\n\t\t: \"0px\"}\n\tstyle:--start-height={preexpansionBoundingRect\n\t\t? `${preexpansionBoundingRect.height}px`\n\t\t: \"0px\"}\n\tstyle:width={fullscreen\n\t\t? undefined\n\t\t: typeof width === \"number\"\n\t\t\t? `calc(min(${width}px, 100%))`\n\t\t\t: get_dimension(width)}\n\tstyle:border-style={variant}\n\tstyle:overflow={allow_overflow ? overflow_behavior : \"hidden\"}\n\tstyle:flex-grow={scale}\n\tstyle:min-width={`calc(min(${min_width}px, 100%))`}\n\tstyle:border-width=\"var(--block-border-width)\"\n\tclass:auto-margin={scale === null}\n\tdir={rtl ? \"rtl\" : \"ltr\"}\n>\n\t<slot />\n\t{#if resizable}\n\t\t<!-- svelte-ignore a11y-no-static-element-interactions -->\n\t\t<svg\n\t\t\tclass=\"resize-handle\"\n\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\tviewBox=\"0 0 10 10\"\n\t\t\ton:mousedown={resize}\n\t\t>\n\t\t\t<line x1=\"1\" y1=\"9\" x2=\"9\" y2=\"1\" stroke=\"gray\" stroke-width=\"0.5\" />\n\t\t\t<line x1=\"5\" y1=\"9\" x2=\"9\" y2=\"5\" stroke=\"gray\" stroke-width=\"0.5\" />\n\t\t</svg>\n\t{/if}\n</svelte:element>\n{#if fullscreen}\n\t<div\n\t\tclass=\"placeholder\"\n\t\tstyle:height={placeholder_height + \"px\"}\n\t\tstyle:width={placeholder_width + \"px\"}\n\t></div>\n{/if}\n\n<style>\n\t.block {\n\t\tposition: relative;\n\t\tmargin: 0;\n\t\tbox-shadow: var(--block-shadow);\n\t\tborder-width: var(--block-border-width);\n\t\tborder-color: var(--block-border-color);\n\t\tborder-radius: var(--block-radius);\n\t\tbackground: var(--block-background-fill);\n\t\twidth: 100%;\n\t\tline-height: var(--line-sm);\n\t}\n\t.block.fullscreen {\n\t\tborder-radius: 0;\n\t}\n\n\t.auto-margin {\n\t\tmargin-left: auto;\n\t\tmargin-right: auto;\n\t}\n\n\t.block.border_focus {\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.block.border_contrast {\n\t\tborder-color: var(--body-text-color);\n\t}\n\n\t.padded {\n\t\tpadding: var(--block-padding);\n\t}\n\n\t.hidden {\n\t\tdisplay: none;\n\t}\n\n\t.flex {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\t.hide-container:not(.fullscreen) {\n\t\tmargin: 0;\n\t\tbox-shadow: none;\n\t\t--block-border-width: 0;\n\t\tbackground: transparent;\n\t\tpadding: 0;\n\t\toverflow: visible;\n\t}\n\t.resize-handle {\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tright: 0;\n\t\twidth: 10px;\n\t\theight: 10px;\n\t\tfill: var(--block-border-color);\n\t\tcursor: nwse-resize;\n\t}\n\t.fullscreen {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100vw;\n\t\theight: 100vh;\n\t\tz-index: 1000;\n\t\toverflow: auto;\n\t}\n\n\t.animating {\n\t\tanimation: pop-out 0.1s ease-out forwards;\n\t}\n\n\t@keyframes pop-out {\n\t\t0% {\n\t\t\tposition: fixed;\n\t\t\ttop: var(--start-top);\n\t\t\tleft: var(--start-left);\n\t\t\twidth: var(--start-width);\n\t\t\theight: var(--start-height);\n\t\t\tz-index: 100;\n\t\t}\n\t\t100% {\n\t\t\tposition: fixed;\n\t\t\ttop: 0vh;\n\t\t\tleft: 0vw;\n\t\t\twidth: 100vw;\n\t\t\theight: 100vh;\n\t\t\tz-index: 1000;\n\t\t}\n\t}\n\n\t.placeholder {\n\t\tborder-radius: var(--block-radius);\n\t\tborder-width: var(--block-border-width);\n\t\tborder-color: var(--block-border-color);\n\t\tborder-style: dashed;\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "line0", "line1", "ctx", "create_if_block_1", "svelte_element_class_value", "svelte_element", "svelte_element_data", "toggle_class", "set_style", "get_spread_update", "svelte_element_levels", "current", "dirty", "svelte_element_dir_value", "div", "create_dynamic_element", "create_if_block", "height", "$$props", "min_height", "max_height", "width", "elem_id", "elem_classes", "variant", "border_mode", "padding", "type", "test_id", "explicit_call", "container", "visible", "allow_overflow", "overflow_behavior", "scale", "min_width", "flex", "resizable", "rtl", "fullscreen", "old_fullscreen", "element", "tag", "placeholder_height", "placeholder_width", "preexpansionBoundingRect", "handleKeydown", "event", "get_dimension", "dimension_value", "resize", "e", "prevY", "onMouseMove", "dy", "$$invalidate", "onMouseUp", "$$value"], "mappings": "g3BAkIEA,EAQKC,EAAAC,EAAAC,CAAA,EAFJC,EAAoEF,EAAAG,CAAA,EACpED,EAAoEF,EAAAI,CAAA,yBAHtDC,EAAM,EAAA,CAAA,oHANjBA,EAAS,EAAA,GAAAC,EAAAD,CAAA,oBAxCDA,EAAO,EAAA,CAAA,MAChBA,EAAO,CAAA,CAAA,GAEG,MAAAE,EAAA,UAAAF,EAAc,CAAA,GAAA,KAAK,GAAG,GAAK,IAAE,0BAkCtCA,EAAG,EAAA,EAAG,MAAQ,qEAvCbA,EAAG,EAAA,CAAA,4BAAHA,EAAG,EAAA,CAAA,EAAAG,EAAAC,CAAA,EAIKC,EAAAF,EAAA,SAAAH,QAAY,EAAK,eAEjBA,EAAO,EAAA,CAAA,mBAEDK,EAAAF,EAAA,eAAAH,OAAgB,OAAO,EACpBK,EAAAF,EAAA,kBAAAH,OAAgB,UAAU,EAC1BK,EAAAF,EAAA,iBAAA,CAAAH,QAAkBA,EAAS,EAAA,CAAA,yCAKjCA,EAAU,CAAA,GAAIA,EAAwB,EAAA,IAAK,IAAI,EAuB7CK,EAAAF,EAAA,cAAAH,QAAU,IAAI,eA3BnBA,EAAU,CAAA,EAAG,OAAYA,MAAcA,EAAM,CAAA,CAAA,CAAA,mBACzCA,EAAU,CAAA,EAAG,OAAYA,MAAcA,EAAU,CAAA,CAAA,CAAA,mBACjDA,EAAU,CAAA,EAAG,OAAYA,MAAcA,EAAU,CAAA,CAAA,CAAA,EAGhDM,EAAAH,EAAA,cAAAH,EAAA,EAAA,EACb,GAAAA,MAAyB,GAAG,KAC/B,KAAK,EACYM,EAAAH,EAAA,eAAAH,EAAA,EAAA,EACd,GAAAA,MAAyB,IAAI,KAChC,KAAK,EACaM,EAAAH,EAAA,gBAAAH,EAAA,EAAA,EACf,GAAAA,MAAyB,KAAK,KACjC,KAAK,EACcM,EAAAH,EAAA,iBAAAH,EAAA,EAAA,EAChB,GAAAA,MAAyB,MAAM,KAClC,KAAK,EACKM,EAAAH,EAAA,QAAAH,EAAA,CAAA,EACV,OACO,OAAAA,MAAU,qBACJA,EAAK,CAAA,CAAA,aACjBA,MAAcA,EAAK,CAAA,CAAA,CAAA,qBACHA,EAAO,CAAA,CAAA,iBACXA,EAAc,EAAA,EAAGA,EAAiB,EAAA,EAAG,QAAQ,kBAC5CA,EAAK,EAAA,CAAA,8BACOA,EAAS,EAAA,CAAA,YAAA,0DArCvCP,EAuDgBC,EAAAS,EAAAP,CAAA,gJAZVI,EAAS,EAAA,6DA1CRA,EAAG,EAAA,CAAA,EAAAG,EAAAC,EAAAG,GAAAC,EAAA,iCAEIR,EAAO,EAAA,CAAA,qBAChBA,EAAO,CAAA,CAAA,GAEG,CAAAS,GAAAC,EAAA,CAAA,EAAA,KAAAR,KAAAA,EAAA,UAAAF,EAAc,CAAA,GAAA,KAAK,GAAG,GAAK,IAAE,qBAAA,CAAA,MAAAE,CAAA,4BAkCtCF,EAAG,EAAA,EAAG,MAAQ,SAAK,CAAA,IAAAW,CAAA,KAnCVN,EAAAF,EAAA,SAAAH,QAAY,EAAK,eAEjBA,EAAO,EAAA,CAAA,mBAEDK,EAAAF,EAAA,eAAAH,OAAgB,OAAO,EACpBK,EAAAF,EAAA,kBAAAH,OAAgB,UAAU,EAC1BK,EAAAF,EAAA,iBAAA,CAAAH,QAAkBA,EAAS,EAAA,CAAA,yCAKjCA,EAAU,CAAA,GAAIA,EAAwB,EAAA,IAAK,IAAI,EAuB7CK,EAAAF,EAAA,cAAAH,QAAU,IAAI,uBA3BnBA,EAAU,CAAA,EAAG,OAAYA,MAAcA,EAAM,CAAA,CAAA,CAAA,2BACzCA,EAAU,CAAA,EAAG,OAAYA,MAAcA,EAAU,CAAA,CAAA,CAAA,4BACjDA,EAAU,CAAA,EAAG,OAAYA,MAAcA,EAAU,CAAA,CAAA,CAAA,iBAGhDM,EAAAH,EAAA,cAAAH,EAAA,EAAA,EACb,GAAAA,MAAyB,GAAG,KAC/B,KAAK,iBACYM,EAAAH,EAAA,eAAAH,EAAA,EAAA,EACd,GAAAA,MAAyB,IAAI,KAChC,KAAK,iBACaM,EAAAH,EAAA,gBAAAH,EAAA,EAAA,EACf,GAAAA,MAAyB,KAAK,KACjC,KAAK,iBACcM,EAAAH,EAAA,iBAAAH,EAAA,EAAA,EAChB,GAAAA,MAAyB,MAAM,KAClC,KAAK,WACKM,EAAAH,EAAA,QAAAH,EAAA,CAAA,EACV,OACO,OAAAA,MAAU,qBACJA,EAAK,CAAA,CAAA,aACjBA,MAAcA,EAAK,CAAA,CAAA,CAAA,+BACHA,EAAO,CAAA,CAAA,6BACXA,EAAc,EAAA,EAAGA,EAAiB,EAAA,EAAG,QAAQ,+BAC5CA,EAAK,EAAA,CAAA,2CACOA,EAAS,EAAA,CAAA,YAAA,8KAsBvBM,EAAAM,EAAA,SAAAZ,MAAqB,IAAI,EAC1BM,EAAAM,EAAA,QAAAZ,MAAoB,IAAI,UAHtCP,EAIMC,EAAAkB,EAAAhB,CAAA,wBAFSU,EAAAM,EAAA,SAAAZ,MAAqB,IAAI,gBAC1BM,EAAAM,EAAA,QAAAZ,MAAoB,IAAI,6CA3DhCA,EAAG,EAAA,GAAAa,GAAAb,CAAA,IAuDLA,EAAU,CAAA,GAAAc,EAAAd,CAAA,2GAvDRA,EAAG,EAAA,YAuDLA,EAAU,CAAA,yMA5IH,OAAAe,EAAsC,MAAA,EAAAC,GACtC,WAAAC,EAA0C,MAAA,EAAAD,GAC1C,WAAAE,EAA0C,MAAA,EAAAF,GAC1C,MAAAG,EAAqC,MAAA,EAAAH,GACrC,QAAAI,EAAU,EAAA,EAAAJ,EACV,CAAA,aAAAK,EAAA,EAAA,EAAAL,GACA,QAAAM,EAAuC,OAAA,EAAAN,GACvC,YAAAO,EAA6C,MAAA,EAAAP,GAC7C,QAAAQ,EAAU,EAAA,EAAAR,GACV,KAAAS,EAA8B,QAAA,EAAAT,GAC9B,QAAAU,EAA8B,MAAA,EAAAV,GAC9B,cAAAW,EAAgB,EAAA,EAAAX,GAChB,UAAAY,EAAY,EAAA,EAAAZ,GACZ,QAAAa,EAAU,EAAA,EAAAb,GACV,eAAAc,EAAiB,EAAA,EAAAd,GACjB,kBAAAe,EAAwC,MAAA,EAAAf,GACxC,MAAAgB,EAAuB,IAAA,EAAAhB,GACvB,UAAAiB,EAAY,CAAA,EAAAjB,GACZ,KAAAkB,EAAO,EAAA,EAAAlB,GACP,UAAAmB,EAAY,EAAA,EAAAnB,GACZ,IAAAoB,EAAM,EAAA,EAAApB,GACN,WAAAqB,EAAa,EAAA,EAAArB,EACpBsB,EAAiBD,EAEjBE,EAEAC,EAAMf,IAAS,WAAa,WAAa,MAEzCgB,EAAqB,EACrBC,EAAoB,EACpBC,EAA2C,cAEtCC,EAAcC,EAAA,CAClBR,GAAcQ,EAAM,MAAQ,cAC/BR,EAAa,EAAA,QAiBTS,GACLC,GAAA,IAEIA,IAAoB,kBAGbA,GAAoB,gBACvBA,EAAkB,eACRA,GAAoB,SAC9B,OAAAA,IAQHC,GAAUC,GAAA,CACX,IAAAC,EAAQD,EAAE,cACRE,EAAeF,GAAAA,CACd,MAAAG,GAAaH,EAAE,QAAUC,EAC/BA,EAAQD,EAAE,QACVI,EAAA,GAAAd,EAAQ,MAAM,OAAA,GAAYA,EAAQ,aAAea,EAAE,KAAAb,CAAA,GAE9Ce,EAAA,IAAA,CACL,OAAO,oBAAoB,YAAaH,CAAW,EACnD,OAAO,oBAAoB,UAAWG,CAAS,GAEhD,OAAO,iBAAiB,YAAaH,CAAW,EAChD,OAAO,iBAAiB,UAAWG,CAAS,8CAMlCf,EAAOgB,u3BAhDXlB,IAAeC,SACrBA,EAAiBD,CAAA,EACbA,GACHgB,EAAA,GAAAV,EAA2BJ,EAAQ,sBAAA,CAAA,EACnCc,EAAA,GAAAZ,EAAqBF,EAAQ,YAAA,EAC7Bc,EAAA,GAAAX,EAAoBH,EAAQ,WAAA,EAC5B,OAAO,iBAAiB,UAAWK,CAAa,SAEhDD,EAA2B,IAAA,EAC3B,OAAO,oBAAoB,UAAWC,CAAa,0BAiB7Cf,OACPK,EAAO,EAAA"}