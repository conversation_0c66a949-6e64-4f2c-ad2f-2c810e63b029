{"version": 3, "file": "stateDiagram-BVO7J4UH-CtaJctfV.js", "sources": ["../../../../node_modules/.pnpm/mermaid@11.5.0/node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-BVO7J4UH.mjs"], "sourcesContent": ["import {\n  StateDB,\n  stateDiagram_default,\n  styles_default\n} from \"./chunk-4IRHCMPZ.mjs\";\nimport \"./chunk-2O5F6CEG.mjs\";\nimport \"./chunk-SSJB2B2L.mjs\";\nimport \"./chunk-XWQKHCUW.mjs\";\nimport \"./chunk-JXS2JFWQ.mjs\";\nimport \"./chunk-VRARSN5C.mjs\";\nimport \"./chunk-6Q42YGA5.mjs\";\nimport \"./chunk-KIRMWWLE.mjs\";\nimport \"./chunk-4BQVQIO5.mjs\";\nimport {\n  utils_default\n} from \"./chunk-ABD7OU7K.mjs\";\nimport {\n  __name,\n  common_default,\n  configureSvgSize,\n  getConfig2 as getConfig,\n  log\n} from \"./chunk-O7R7247Q.mjs\";\n\n// src/diagrams/state/stateRenderer.js\nimport { select } from \"d3\";\nimport { layout as dagreLayout } from \"dagre-d3-es/src/dagre/index.js\";\nimport * as graphlib from \"dagre-d3-es/src/graphlib/index.js\";\n\n// src/diagrams/state/shapes.js\nimport { line, curveBasis } from \"d3\";\n\n// src/diagrams/state/id-cache.js\nvar idCache = {};\nvar set = /* @__PURE__ */ __name((key, val) => {\n  idCache[key] = val;\n}, \"set\");\nvar get = /* @__PURE__ */ __name((k) => idCache[k], \"get\");\nvar keys = /* @__PURE__ */ __name(() => Object.keys(idCache), \"keys\");\nvar size = /* @__PURE__ */ __name(() => keys().length, \"size\");\nvar id_cache_default = {\n  get,\n  set,\n  keys,\n  size\n};\n\n// src/diagrams/state/shapes.js\nvar drawStartState = /* @__PURE__ */ __name((g) => g.append(\"circle\").attr(\"class\", \"start-state\").attr(\"r\", getConfig().state.sizeUnit).attr(\"cx\", getConfig().state.padding + getConfig().state.sizeUnit).attr(\"cy\", getConfig().state.padding + getConfig().state.sizeUnit), \"drawStartState\");\nvar drawDivider = /* @__PURE__ */ __name((g) => g.append(\"line\").style(\"stroke\", \"grey\").style(\"stroke-dasharray\", \"3\").attr(\"x1\", getConfig().state.textHeight).attr(\"class\", \"divider\").attr(\"x2\", getConfig().state.textHeight * 2).attr(\"y1\", 0).attr(\"y2\", 0), \"drawDivider\");\nvar drawSimpleState = /* @__PURE__ */ __name((g, stateDef) => {\n  const state = g.append(\"text\").attr(\"x\", 2 * getConfig().state.padding).attr(\"y\", getConfig().state.textHeight + 2 * getConfig().state.padding).attr(\"font-size\", getConfig().state.fontSize).attr(\"class\", \"state-title\").text(stateDef.id);\n  const classBox = state.node().getBBox();\n  g.insert(\"rect\", \":first-child\").attr(\"x\", getConfig().state.padding).attr(\"y\", getConfig().state.padding).attr(\"width\", classBox.width + 2 * getConfig().state.padding).attr(\"height\", classBox.height + 2 * getConfig().state.padding).attr(\"rx\", getConfig().state.radius);\n  return state;\n}, \"drawSimpleState\");\nvar drawDescrState = /* @__PURE__ */ __name((g, stateDef) => {\n  const addTspan = /* @__PURE__ */ __name(function(textEl, txt, isFirst2) {\n    const tSpan = textEl.append(\"tspan\").attr(\"x\", 2 * getConfig().state.padding).text(txt);\n    if (!isFirst2) {\n      tSpan.attr(\"dy\", getConfig().state.textHeight);\n    }\n  }, \"addTspan\");\n  const title = g.append(\"text\").attr(\"x\", 2 * getConfig().state.padding).attr(\"y\", getConfig().state.textHeight + 1.3 * getConfig().state.padding).attr(\"font-size\", getConfig().state.fontSize).attr(\"class\", \"state-title\").text(stateDef.descriptions[0]);\n  const titleBox = title.node().getBBox();\n  const titleHeight = titleBox.height;\n  const description = g.append(\"text\").attr(\"x\", getConfig().state.padding).attr(\n    \"y\",\n    titleHeight + getConfig().state.padding * 0.4 + getConfig().state.dividerMargin + getConfig().state.textHeight\n  ).attr(\"class\", \"state-description\");\n  let isFirst = true;\n  let isSecond = true;\n  stateDef.descriptions.forEach(function(descr) {\n    if (!isFirst) {\n      addTspan(description, descr, isSecond);\n      isSecond = false;\n    }\n    isFirst = false;\n  });\n  const descrLine = g.append(\"line\").attr(\"x1\", getConfig().state.padding).attr(\"y1\", getConfig().state.padding + titleHeight + getConfig().state.dividerMargin / 2).attr(\"y2\", getConfig().state.padding + titleHeight + getConfig().state.dividerMargin / 2).attr(\"class\", \"descr-divider\");\n  const descrBox = description.node().getBBox();\n  const width = Math.max(descrBox.width, titleBox.width);\n  descrLine.attr(\"x2\", width + 3 * getConfig().state.padding);\n  g.insert(\"rect\", \":first-child\").attr(\"x\", getConfig().state.padding).attr(\"y\", getConfig().state.padding).attr(\"width\", width + 2 * getConfig().state.padding).attr(\"height\", descrBox.height + titleHeight + 2 * getConfig().state.padding).attr(\"rx\", getConfig().state.radius);\n  return g;\n}, \"drawDescrState\");\nvar addTitleAndBox = /* @__PURE__ */ __name((g, stateDef, altBkg) => {\n  const pad = getConfig().state.padding;\n  const dblPad = 2 * getConfig().state.padding;\n  const orgBox = g.node().getBBox();\n  const orgWidth = orgBox.width;\n  const orgX = orgBox.x;\n  const title = g.append(\"text\").attr(\"x\", 0).attr(\"y\", getConfig().state.titleShift).attr(\"font-size\", getConfig().state.fontSize).attr(\"class\", \"state-title\").text(stateDef.id);\n  const titleBox = title.node().getBBox();\n  const titleWidth = titleBox.width + dblPad;\n  let width = Math.max(titleWidth, orgWidth);\n  if (width === orgWidth) {\n    width = width + dblPad;\n  }\n  let startX;\n  const graphBox = g.node().getBBox();\n  if (stateDef.doc) {\n  }\n  startX = orgX - pad;\n  if (titleWidth > orgWidth) {\n    startX = (orgWidth - width) / 2 + pad;\n  }\n  if (Math.abs(orgX - graphBox.x) < pad && titleWidth > orgWidth) {\n    startX = orgX - (titleWidth - orgWidth) / 2;\n  }\n  const lineY = 1 - getConfig().state.textHeight;\n  g.insert(\"rect\", \":first-child\").attr(\"x\", startX).attr(\"y\", lineY).attr(\"class\", altBkg ? \"alt-composit\" : \"composit\").attr(\"width\", width).attr(\n    \"height\",\n    graphBox.height + getConfig().state.textHeight + getConfig().state.titleShift + 1\n  ).attr(\"rx\", \"0\");\n  title.attr(\"x\", startX + pad);\n  if (titleWidth <= orgWidth) {\n    title.attr(\"x\", orgX + (width - dblPad) / 2 - titleWidth / 2 + pad);\n  }\n  g.insert(\"rect\", \":first-child\").attr(\"x\", startX).attr(\n    \"y\",\n    getConfig().state.titleShift - getConfig().state.textHeight - getConfig().state.padding\n  ).attr(\"width\", width).attr(\"height\", getConfig().state.textHeight * 3).attr(\"rx\", getConfig().state.radius);\n  g.insert(\"rect\", \":first-child\").attr(\"x\", startX).attr(\n    \"y\",\n    getConfig().state.titleShift - getConfig().state.textHeight - getConfig().state.padding\n  ).attr(\"width\", width).attr(\"height\", graphBox.height + 3 + 2 * getConfig().state.textHeight).attr(\"rx\", getConfig().state.radius);\n  return g;\n}, \"addTitleAndBox\");\nvar drawEndState = /* @__PURE__ */ __name((g) => {\n  g.append(\"circle\").attr(\"class\", \"end-state-outer\").attr(\"r\", getConfig().state.sizeUnit + getConfig().state.miniPadding).attr(\n    \"cx\",\n    getConfig().state.padding + getConfig().state.sizeUnit + getConfig().state.miniPadding\n  ).attr(\n    \"cy\",\n    getConfig().state.padding + getConfig().state.sizeUnit + getConfig().state.miniPadding\n  );\n  return g.append(\"circle\").attr(\"class\", \"end-state-inner\").attr(\"r\", getConfig().state.sizeUnit).attr(\"cx\", getConfig().state.padding + getConfig().state.sizeUnit + 2).attr(\"cy\", getConfig().state.padding + getConfig().state.sizeUnit + 2);\n}, \"drawEndState\");\nvar drawForkJoinState = /* @__PURE__ */ __name((g, stateDef) => {\n  let width = getConfig().state.forkWidth;\n  let height = getConfig().state.forkHeight;\n  if (stateDef.parentId) {\n    let tmp = width;\n    width = height;\n    height = tmp;\n  }\n  return g.append(\"rect\").style(\"stroke\", \"black\").style(\"fill\", \"black\").attr(\"width\", width).attr(\"height\", height).attr(\"x\", getConfig().state.padding).attr(\"y\", getConfig().state.padding);\n}, \"drawForkJoinState\");\nvar _drawLongText = /* @__PURE__ */ __name((_text, x, y, g) => {\n  let textHeight = 0;\n  const textElem = g.append(\"text\");\n  textElem.style(\"text-anchor\", \"start\");\n  textElem.attr(\"class\", \"noteText\");\n  let text = _text.replace(/\\r\\n/g, \"<br/>\");\n  text = text.replace(/\\n/g, \"<br/>\");\n  const lines = text.split(common_default.lineBreakRegex);\n  let tHeight = 1.25 * getConfig().state.noteMargin;\n  for (const line2 of lines) {\n    const txt = line2.trim();\n    if (txt.length > 0) {\n      const span = textElem.append(\"tspan\");\n      span.text(txt);\n      if (tHeight === 0) {\n        const textBounds = span.node().getBBox();\n        tHeight += textBounds.height;\n      }\n      textHeight += tHeight;\n      span.attr(\"x\", x + getConfig().state.noteMargin);\n      span.attr(\"y\", y + textHeight + 1.25 * getConfig().state.noteMargin);\n    }\n  }\n  return { textWidth: textElem.node().getBBox().width, textHeight };\n}, \"_drawLongText\");\nvar drawNote = /* @__PURE__ */ __name((text, g) => {\n  g.attr(\"class\", \"state-note\");\n  const note = g.append(\"rect\").attr(\"x\", 0).attr(\"y\", getConfig().state.padding);\n  const rectElem = g.append(\"g\");\n  const { textWidth, textHeight } = _drawLongText(text, 0, 0, rectElem);\n  note.attr(\"height\", textHeight + 2 * getConfig().state.noteMargin);\n  note.attr(\"width\", textWidth + getConfig().state.noteMargin * 2);\n  return note;\n}, \"drawNote\");\nvar drawState = /* @__PURE__ */ __name(function(elem, stateDef) {\n  const id = stateDef.id;\n  const stateInfo = {\n    id,\n    label: stateDef.id,\n    width: 0,\n    height: 0\n  };\n  const g = elem.append(\"g\").attr(\"id\", id).attr(\"class\", \"stateGroup\");\n  if (stateDef.type === \"start\") {\n    drawStartState(g);\n  }\n  if (stateDef.type === \"end\") {\n    drawEndState(g);\n  }\n  if (stateDef.type === \"fork\" || stateDef.type === \"join\") {\n    drawForkJoinState(g, stateDef);\n  }\n  if (stateDef.type === \"note\") {\n    drawNote(stateDef.note.text, g);\n  }\n  if (stateDef.type === \"divider\") {\n    drawDivider(g);\n  }\n  if (stateDef.type === \"default\" && stateDef.descriptions.length === 0) {\n    drawSimpleState(g, stateDef);\n  }\n  if (stateDef.type === \"default\" && stateDef.descriptions.length > 0) {\n    drawDescrState(g, stateDef);\n  }\n  const stateBox = g.node().getBBox();\n  stateInfo.width = stateBox.width + 2 * getConfig().state.padding;\n  stateInfo.height = stateBox.height + 2 * getConfig().state.padding;\n  id_cache_default.set(id, stateInfo);\n  return stateInfo;\n}, \"drawState\");\nvar edgeCount = 0;\nvar drawEdge = /* @__PURE__ */ __name(function(elem, path, relation) {\n  const getRelationType = /* @__PURE__ */ __name(function(type) {\n    switch (type) {\n      case StateDB.relationType.AGGREGATION:\n        return \"aggregation\";\n      case StateDB.relationType.EXTENSION:\n        return \"extension\";\n      case StateDB.relationType.COMPOSITION:\n        return \"composition\";\n      case StateDB.relationType.DEPENDENCY:\n        return \"dependency\";\n    }\n  }, \"getRelationType\");\n  path.points = path.points.filter((p) => !Number.isNaN(p.y));\n  const lineData = path.points;\n  const lineFunction = line().x(function(d) {\n    return d.x;\n  }).y(function(d) {\n    return d.y;\n  }).curve(curveBasis);\n  const svgPath = elem.append(\"path\").attr(\"d\", lineFunction(lineData)).attr(\"id\", \"edge\" + edgeCount).attr(\"class\", \"transition\");\n  let url = \"\";\n  if (getConfig().state.arrowMarkerAbsolute) {\n    url = window.location.protocol + \"//\" + window.location.host + window.location.pathname + window.location.search;\n    url = url.replace(/\\(/g, \"\\\\(\");\n    url = url.replace(/\\)/g, \"\\\\)\");\n  }\n  svgPath.attr(\n    \"marker-end\",\n    \"url(\" + url + \"#\" + getRelationType(StateDB.relationType.DEPENDENCY) + \"End)\"\n  );\n  if (relation.title !== void 0) {\n    const label = elem.append(\"g\").attr(\"class\", \"stateLabel\");\n    const { x, y } = utils_default.calcLabelPosition(path.points);\n    const rows = common_default.getRows(relation.title);\n    let titleHeight = 0;\n    const titleRows = [];\n    let maxWidth = 0;\n    let minX = 0;\n    for (let i = 0; i <= rows.length; i++) {\n      const title = label.append(\"text\").attr(\"text-anchor\", \"middle\").text(rows[i]).attr(\"x\", x).attr(\"y\", y + titleHeight);\n      const boundsTmp = title.node().getBBox();\n      maxWidth = Math.max(maxWidth, boundsTmp.width);\n      minX = Math.min(minX, boundsTmp.x);\n      log.info(boundsTmp.x, x, y + titleHeight);\n      if (titleHeight === 0) {\n        const titleBox = title.node().getBBox();\n        titleHeight = titleBox.height;\n        log.info(\"Title height\", titleHeight, y);\n      }\n      titleRows.push(title);\n    }\n    let boxHeight = titleHeight * rows.length;\n    if (rows.length > 1) {\n      const heightAdj = (rows.length - 1) * titleHeight * 0.5;\n      titleRows.forEach((title, i) => title.attr(\"y\", y + i * titleHeight - heightAdj));\n      boxHeight = titleHeight * rows.length;\n    }\n    const bounds = label.node().getBBox();\n    label.insert(\"rect\", \":first-child\").attr(\"class\", \"box\").attr(\"x\", x - maxWidth / 2 - getConfig().state.padding / 2).attr(\"y\", y - boxHeight / 2 - getConfig().state.padding / 2 - 3.5).attr(\"width\", maxWidth + getConfig().state.padding).attr(\"height\", boxHeight + getConfig().state.padding);\n    log.info(bounds);\n  }\n  edgeCount++;\n}, \"drawEdge\");\n\n// src/diagrams/state/stateRenderer.js\nvar conf;\nvar transformationLog = {};\nvar setConf = /* @__PURE__ */ __name(function() {\n}, \"setConf\");\nvar insertMarkers = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"dependencyEnd\").attr(\"refX\", 19).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 19,7 L9,13 L14,7 L9,1 Z\");\n}, \"insertMarkers\");\nvar draw = /* @__PURE__ */ __name(function(text, id, _version, diagObj) {\n  conf = getConfig().state;\n  const securityLevel = getConfig().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const doc = securityLevel === \"sandbox\" ? sandboxElement.nodes()[0].contentDocument : document;\n  log.debug(\"Rendering diagram \" + text);\n  const diagram2 = root.select(`[id='${id}']`);\n  insertMarkers(diagram2);\n  const rootDoc = diagObj.db.getRootDoc();\n  renderDoc(rootDoc, diagram2, void 0, false, root, doc, diagObj);\n  const padding = conf.padding;\n  const bounds = diagram2.node().getBBox();\n  const width = bounds.width + padding * 2;\n  const height = bounds.height + padding * 2;\n  const svgWidth = width * 1.75;\n  configureSvgSize(diagram2, height, svgWidth, conf.useMaxWidth);\n  diagram2.attr(\n    \"viewBox\",\n    `${bounds.x - conf.padding}  ${bounds.y - conf.padding} ` + width + \" \" + height\n  );\n}, \"draw\");\nvar getLabelWidth = /* @__PURE__ */ __name((text) => {\n  return text ? text.length * conf.fontSizeFactor : 1;\n}, \"getLabelWidth\");\nvar renderDoc = /* @__PURE__ */ __name((doc, diagram2, parentId, altBkg, root, domDocument, diagObj) => {\n  const graph = new graphlib.Graph({\n    compound: true,\n    multigraph: true\n  });\n  let i;\n  let edgeFreeDoc = true;\n  for (i = 0; i < doc.length; i++) {\n    if (doc[i].stmt === \"relation\") {\n      edgeFreeDoc = false;\n      break;\n    }\n  }\n  if (parentId) {\n    graph.setGraph({\n      rankdir: \"LR\",\n      multigraph: true,\n      compound: true,\n      // acyclicer: 'greedy',\n      ranker: \"tight-tree\",\n      ranksep: edgeFreeDoc ? 1 : conf.edgeLengthFactor,\n      nodeSep: edgeFreeDoc ? 1 : 50,\n      isMultiGraph: true\n      // ranksep: 5,\n      // nodesep: 1\n    });\n  } else {\n    graph.setGraph({\n      rankdir: \"TB\",\n      multigraph: true,\n      compound: true,\n      // isCompound: true,\n      // acyclicer: 'greedy',\n      // ranker: 'longest-path'\n      ranksep: edgeFreeDoc ? 1 : conf.edgeLengthFactor,\n      nodeSep: edgeFreeDoc ? 1 : 50,\n      ranker: \"tight-tree\",\n      // ranker: 'network-simplex'\n      isMultiGraph: true\n    });\n  }\n  graph.setDefaultEdgeLabel(function() {\n    return {};\n  });\n  const states = diagObj.db.getStates();\n  const relations = diagObj.db.getRelations();\n  const keys2 = Object.keys(states);\n  let first = true;\n  for (const key of keys2) {\n    const stateDef = states[key];\n    if (parentId) {\n      stateDef.parentId = parentId;\n    }\n    let node;\n    if (stateDef.doc) {\n      let sub = diagram2.append(\"g\").attr(\"id\", stateDef.id).attr(\"class\", \"stateGroup\");\n      node = renderDoc(stateDef.doc, sub, stateDef.id, !altBkg, root, domDocument, diagObj);\n      if (first) {\n        sub = addTitleAndBox(sub, stateDef, altBkg);\n        let boxBounds = sub.node().getBBox();\n        node.width = boxBounds.width;\n        node.height = boxBounds.height + conf.padding / 2;\n        transformationLog[stateDef.id] = { y: conf.compositTitleSize };\n      } else {\n        let boxBounds = sub.node().getBBox();\n        node.width = boxBounds.width;\n        node.height = boxBounds.height;\n      }\n    } else {\n      node = drawState(diagram2, stateDef, graph);\n    }\n    if (stateDef.note) {\n      const noteDef = {\n        descriptions: [],\n        id: stateDef.id + \"-note\",\n        note: stateDef.note,\n        type: \"note\"\n      };\n      const note = drawState(diagram2, noteDef, graph);\n      if (stateDef.note.position === \"left of\") {\n        graph.setNode(node.id + \"-note\", note);\n        graph.setNode(node.id, node);\n      } else {\n        graph.setNode(node.id, node);\n        graph.setNode(node.id + \"-note\", note);\n      }\n      graph.setParent(node.id, node.id + \"-group\");\n      graph.setParent(node.id + \"-note\", node.id + \"-group\");\n    } else {\n      graph.setNode(node.id, node);\n    }\n  }\n  log.debug(\"Count=\", graph.nodeCount(), graph);\n  let cnt = 0;\n  relations.forEach(function(relation) {\n    cnt++;\n    log.debug(\"Setting edge\", relation);\n    graph.setEdge(\n      relation.id1,\n      relation.id2,\n      {\n        relation,\n        width: getLabelWidth(relation.title),\n        height: conf.labelHeight * common_default.getRows(relation.title).length,\n        labelpos: \"c\"\n      },\n      \"id\" + cnt\n    );\n  });\n  dagreLayout(graph);\n  log.debug(\"Graph after layout\", graph.nodes());\n  const svgElem = diagram2.node();\n  graph.nodes().forEach(function(v) {\n    if (v !== void 0 && graph.node(v) !== void 0) {\n      log.warn(\"Node \" + v + \": \" + JSON.stringify(graph.node(v)));\n      root.select(\"#\" + svgElem.id + \" #\" + v).attr(\n        \"transform\",\n        \"translate(\" + (graph.node(v).x - graph.node(v).width / 2) + \",\" + (graph.node(v).y + (transformationLog[v] ? transformationLog[v].y : 0) - graph.node(v).height / 2) + \" )\"\n      );\n      root.select(\"#\" + svgElem.id + \" #\" + v).attr(\"data-x-shift\", graph.node(v).x - graph.node(v).width / 2);\n      const dividers = domDocument.querySelectorAll(\"#\" + svgElem.id + \" #\" + v + \" .divider\");\n      dividers.forEach((divider) => {\n        const parent = divider.parentElement;\n        let pWidth = 0;\n        let pShift = 0;\n        if (parent) {\n          if (parent.parentElement) {\n            pWidth = parent.parentElement.getBBox().width;\n          }\n          pShift = parseInt(parent.getAttribute(\"data-x-shift\"), 10);\n          if (Number.isNaN(pShift)) {\n            pShift = 0;\n          }\n        }\n        divider.setAttribute(\"x1\", 0 - pShift + 8);\n        divider.setAttribute(\"x2\", pWidth - pShift - 8);\n      });\n    } else {\n      log.debug(\"No Node \" + v + \": \" + JSON.stringify(graph.node(v)));\n    }\n  });\n  let stateBox = svgElem.getBBox();\n  graph.edges().forEach(function(e) {\n    if (e !== void 0 && graph.edge(e) !== void 0) {\n      log.debug(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(graph.edge(e)));\n      drawEdge(diagram2, graph.edge(e), graph.edge(e).relation);\n    }\n  });\n  stateBox = svgElem.getBBox();\n  const stateInfo = {\n    id: parentId ? parentId : \"root\",\n    label: parentId ? parentId : \"root\",\n    width: 0,\n    height: 0\n  };\n  stateInfo.width = stateBox.width + 2 * conf.padding;\n  stateInfo.height = stateBox.height + 2 * conf.padding;\n  log.debug(\"Doc rendered\", stateInfo, graph);\n  return stateInfo;\n}, \"renderDoc\");\nvar stateRenderer_default = {\n  setConf,\n  draw\n};\n\n// src/diagrams/state/stateDiagram.ts\nvar diagram = {\n  parser: stateDiagram_default,\n  get db() {\n    return new StateDB(1);\n  },\n  renderer: stateRenderer_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ __name((cnf) => {\n    if (!cnf.state) {\n      cnf.state = {};\n    }\n    cnf.state.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;\n  }, \"init\")\n};\nexport {\n  diagram\n};\n"], "names": ["idCache", "set", "__name", "key", "val", "get", "k", "keys", "size", "id_cache_default", "drawStartState", "g", "getConfig", "drawDivider", "drawSimpleState", "stateDef", "state", "classBox", "drawDescrState", "addTspan", "textEl", "txt", "isFirst2", "tSpan", "titleBox", "titleHeight", "description", "<PERSON><PERSON><PERSON><PERSON>", "isSecond", "descr", "descrLine", "descrBox", "width", "addTitleAndBox", "altBkg", "pad", "dblPad", "orgBox", "orgWidth", "orgX", "title", "titleWidth", "startX", "graphBox", "lineY", "drawEndState", "drawForkJoinState", "height", "tmp", "_drawLongText", "_text", "x", "y", "textHeight", "textElem", "text", "lines", "common_default", "tHeight", "line2", "span", "textBounds", "drawNote", "note", "rectElem", "textWidth", "drawState", "elem", "id", "stateInfo", "stateBox", "edgeCount", "drawEdge", "path", "relation", "getRelationType", "type", "StateDB", "p", "lineData", "lineFunction", "line", "d", "curveBasis", "svgPath", "url", "label", "utils_default", "rows", "titleRows", "max<PERSON><PERSON><PERSON>", "minX", "i", "boundsTmp", "log", "boxHeight", "heightAdj", "bounds", "conf", "transformationLog", "setConf", "insertMarkers", "draw", "_version", "diagObj", "securityLevel", "sandboxElement", "select", "root", "doc", "diagram2", "rootDoc", "renderDoc", "padding", "svgWidth", "configureSvgSize", "<PERSON><PERSON><PERSON><PERSON>", "parentId", "domDocument", "graph", "graphlib.Graph", "edgeFreeDoc", "states", "relations", "keys2", "node", "sub", "boxBounds", "noteDef", "cnt", "dagreLayout", "svgElem", "v", "divider", "parent", "pWidth", "pShift", "e", "stateRenderer_default", "diagram", "stateDiagram_default", "styles_default", "cnf"], "mappings": "sfAiCA,IAAIA,EAAU,CAAA,EACVC,EAAsBC,EAAO,CAACC,EAAKC,IAAQ,CAC7CJ,EAAQG,CAAG,EAAIC,CACjB,EAAG,KAAK,EACJC,EAAsBH,EAAQI,GAAMN,EAAQM,CAAC,EAAG,KAAK,EACrDC,EAAuBL,EAAO,IAAM,OAAO,KAAKF,CAAO,EAAG,MAAM,EAChEQ,EAAuBN,EAAO,IAAMK,EAAM,EAAC,OAAQ,MAAM,EACzDE,EAAmB,CACrB,IAAAJ,EACA,IAAAJ,EACA,KAAAM,EACA,KAAAC,CACF,EAGIE,EAAiCR,EAAQS,GAAMA,EAAE,OAAO,QAAQ,EAAE,KAAK,QAAS,aAAa,EAAE,KAAK,IAAKC,EAAW,EAAC,MAAM,QAAQ,EAAE,KAAK,KAAMA,EAAW,EAAC,MAAM,QAAUA,EAAW,EAAC,MAAM,QAAQ,EAAE,KAAK,KAAMA,IAAY,MAAM,QAAUA,EAAS,EAAG,MAAM,QAAQ,EAAG,gBAAgB,EAC5RC,EAA8BX,EAAQS,GAAMA,EAAE,OAAO,MAAM,EAAE,MAAM,SAAU,MAAM,EAAE,MAAM,mBAAoB,GAAG,EAAE,KAAK,KAAMC,EAAW,EAAC,MAAM,UAAU,EAAE,KAAK,QAAS,SAAS,EAAE,KAAK,KAAMA,EAAS,EAAG,MAAM,WAAa,CAAC,EAAE,KAAK,KAAM,CAAC,EAAE,KAAK,KAAM,CAAC,EAAG,aAAa,EAC7QE,EAAkCZ,EAAO,CAACS,EAAGI,IAAa,CAC5D,MAAMC,EAAQL,EAAE,OAAO,MAAM,EAAE,KAAK,IAAK,EAAIC,EAAS,EAAG,MAAM,OAAO,EAAE,KAAK,IAAKA,EAAS,EAAG,MAAM,WAAa,EAAIA,EAAS,EAAG,MAAM,OAAO,EAAE,KAAK,YAAaA,IAAY,MAAM,QAAQ,EAAE,KAAK,QAAS,aAAa,EAAE,KAAKG,EAAS,EAAE,EACrOE,EAAWD,EAAM,KAAM,EAAC,QAAO,EACrC,OAAAL,EAAE,OAAO,OAAQ,cAAc,EAAE,KAAK,IAAKC,EAAW,EAAC,MAAM,OAAO,EAAE,KAAK,IAAKA,IAAY,MAAM,OAAO,EAAE,KAAK,QAASK,EAAS,MAAQ,EAAIL,EAAS,EAAG,MAAM,OAAO,EAAE,KAAK,SAAUK,EAAS,OAAS,EAAIL,EAAS,EAAG,MAAM,OAAO,EAAE,KAAK,KAAMA,EAAW,EAAC,MAAM,MAAM,EACrQI,CACT,EAAG,iBAAiB,EAChBE,EAAiChB,EAAO,CAACS,EAAGI,IAAa,CAC3D,MAAMI,EAA2BjB,EAAO,SAASkB,EAAQC,EAAKC,EAAU,CACtE,MAAMC,EAAQH,EAAO,OAAO,OAAO,EAAE,KAAK,IAAK,EAAIR,EAAS,EAAG,MAAM,OAAO,EAAE,KAAKS,CAAG,EACjFC,GACHC,EAAM,KAAK,KAAMX,EAAS,EAAG,MAAM,UAAU,CAEhD,EAAE,UAAU,EAEPY,EADQb,EAAE,OAAO,MAAM,EAAE,KAAK,IAAK,EAAIC,EAAW,EAAC,MAAM,OAAO,EAAE,KAAK,IAAKA,EAAW,EAAC,MAAM,WAAa,IAAMA,EAAW,EAAC,MAAM,OAAO,EAAE,KAAK,YAAaA,EAAW,EAAC,MAAM,QAAQ,EAAE,KAAK,QAAS,aAAa,EAAE,KAAKG,EAAS,aAAa,CAAC,CAAC,EACnO,KAAM,EAAC,QAAO,EAC/BU,EAAcD,EAAS,OACvBE,EAAcf,EAAE,OAAO,MAAM,EAAE,KAAK,IAAKC,EAAW,EAAC,MAAM,OAAO,EAAE,KACxE,IACAa,EAAcb,EAAS,EAAG,MAAM,QAAU,GAAMA,EAAW,EAAC,MAAM,cAAgBA,EAAW,EAAC,MAAM,UACxG,EAAI,KAAK,QAAS,mBAAmB,EACnC,IAAIe,EAAU,GACVC,EAAW,GACfb,EAAS,aAAa,QAAQ,SAASc,EAAO,CACvCF,IACHR,EAASO,EAAaG,EAAOD,CAAQ,EACrCA,EAAW,IAEbD,EAAU,EACd,CAAG,EACD,MAAMG,EAAYnB,EAAE,OAAO,MAAM,EAAE,KAAK,KAAMC,EAAW,EAAC,MAAM,OAAO,EAAE,KAAK,KAAMA,EAAW,EAAC,MAAM,QAAUa,EAAcb,EAAS,EAAG,MAAM,cAAgB,CAAC,EAAE,KAAK,KAAMA,EAAS,EAAG,MAAM,QAAUa,EAAcb,EAAW,EAAC,MAAM,cAAgB,CAAC,EAAE,KAAK,QAAS,eAAe,EACpRmB,EAAWL,EAAY,KAAM,EAAC,QAAO,EACrCM,EAAQ,KAAK,IAAID,EAAS,MAAOP,EAAS,KAAK,EACrD,OAAAM,EAAU,KAAK,KAAME,EAAQ,EAAIpB,EAAW,EAAC,MAAM,OAAO,EAC1DD,EAAE,OAAO,OAAQ,cAAc,EAAE,KAAK,IAAKC,EAAW,EAAC,MAAM,OAAO,EAAE,KAAK,IAAKA,IAAY,MAAM,OAAO,EAAE,KAAK,QAASoB,EAAQ,EAAIpB,IAAY,MAAM,OAAO,EAAE,KAAK,SAAUmB,EAAS,OAASN,EAAc,EAAIb,EAAS,EAAG,MAAM,OAAO,EAAE,KAAK,KAAMA,EAAW,EAAC,MAAM,MAAM,EAC1QD,CACT,EAAG,gBAAgB,EACfsB,EAAiC/B,EAAO,CAACS,EAAGI,EAAUmB,IAAW,CACnE,MAAMC,EAAMvB,IAAY,MAAM,QACxBwB,EAAS,EAAIxB,EAAS,EAAG,MAAM,QAC/ByB,EAAS1B,EAAE,KAAM,EAAC,QAAO,EACzB2B,EAAWD,EAAO,MAClBE,EAAOF,EAAO,EACdG,EAAQ7B,EAAE,OAAO,MAAM,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,IAAKC,EAAS,EAAG,MAAM,UAAU,EAAE,KAAK,YAAaA,IAAY,MAAM,QAAQ,EAAE,KAAK,QAAS,aAAa,EAAE,KAAKG,EAAS,EAAE,EAEzK0B,EADWD,EAAM,KAAM,EAAC,QAAO,EACT,MAAQJ,EACpC,IAAIJ,EAAQ,KAAK,IAAIS,EAAYH,CAAQ,EACrCN,IAAUM,IACZN,EAAQA,EAAQI,GAElB,IAAIM,EACJ,MAAMC,EAAWhC,EAAE,KAAM,EAAC,QAAO,EAC7BI,EAAS,IAEb2B,EAASH,EAAOJ,EACZM,EAAaH,IACfI,GAAUJ,EAAWN,GAAS,EAAIG,GAEhC,KAAK,IAAII,EAAOI,EAAS,CAAC,EAAIR,GAAOM,EAAaH,IACpDI,EAASH,GAAQE,EAAaH,GAAY,GAE5C,MAAMM,EAAQ,EAAIhC,EAAS,EAAG,MAAM,WACpC,OAAAD,EAAE,OAAO,OAAQ,cAAc,EAAE,KAAK,IAAK+B,CAAM,EAAE,KAAK,IAAKE,CAAK,EAAE,KAAK,QAASV,EAAS,eAAiB,UAAU,EAAE,KAAK,QAASF,CAAK,EAAE,KAC3I,SACAW,EAAS,OAAS/B,EAAS,EAAG,MAAM,WAAaA,EAAW,EAAC,MAAM,WAAa,CACpF,EAAI,KAAK,KAAM,GAAG,EAChB4B,EAAM,KAAK,IAAKE,EAASP,CAAG,EACxBM,GAAcH,GAChBE,EAAM,KAAK,IAAKD,GAAQP,EAAQI,GAAU,EAAIK,EAAa,EAAIN,CAAG,EAEpExB,EAAE,OAAO,OAAQ,cAAc,EAAE,KAAK,IAAK+B,CAAM,EAAE,KACjD,IACA9B,EAAW,EAAC,MAAM,WAAaA,EAAS,EAAG,MAAM,WAAaA,IAAY,MAAM,OACpF,EAAI,KAAK,QAASoB,CAAK,EAAE,KAAK,SAAUpB,IAAY,MAAM,WAAa,CAAC,EAAE,KAAK,KAAMA,IAAY,MAAM,MAAM,EAC3GD,EAAE,OAAO,OAAQ,cAAc,EAAE,KAAK,IAAK+B,CAAM,EAAE,KACjD,IACA9B,EAAW,EAAC,MAAM,WAAaA,EAAS,EAAG,MAAM,WAAaA,IAAY,MAAM,OACpF,EAAI,KAAK,QAASoB,CAAK,EAAE,KAAK,SAAUW,EAAS,OAAS,EAAI,EAAI/B,IAAY,MAAM,UAAU,EAAE,KAAK,KAAMA,EAAW,EAAC,MAAM,MAAM,EAC1HD,CACT,EAAG,gBAAgB,EACfkC,EAA+B3C,EAAQS,IACzCA,EAAE,OAAO,QAAQ,EAAE,KAAK,QAAS,iBAAiB,EAAE,KAAK,IAAKC,EAAS,EAAG,MAAM,SAAWA,EAAS,EAAG,MAAM,WAAW,EAAE,KACxH,KACAA,EAAW,EAAC,MAAM,QAAUA,EAAS,EAAG,MAAM,SAAWA,IAAY,MAAM,WAC/E,EAAI,KACA,KACAA,EAAW,EAAC,MAAM,QAAUA,EAAS,EAAG,MAAM,SAAWA,IAAY,MAAM,WAC/E,EACSD,EAAE,OAAO,QAAQ,EAAE,KAAK,QAAS,iBAAiB,EAAE,KAAK,IAAKC,EAAW,EAAC,MAAM,QAAQ,EAAE,KAAK,KAAMA,EAAW,EAAC,MAAM,QAAUA,EAAS,EAAG,MAAM,SAAW,CAAC,EAAE,KAAK,KAAMA,EAAS,EAAG,MAAM,QAAUA,EAAS,EAAG,MAAM,SAAW,CAAC,GAC5O,cAAc,EACbkC,GAAoC5C,EAAO,CAACS,EAAGI,IAAa,CAC9D,IAAIiB,EAAQpB,IAAY,MAAM,UAC1BmC,EAASnC,IAAY,MAAM,WAC/B,GAAIG,EAAS,SAAU,CACrB,IAAIiC,EAAMhB,EACVA,EAAQe,EACRA,EAASC,CACV,CACD,OAAOrC,EAAE,OAAO,MAAM,EAAE,MAAM,SAAU,OAAO,EAAE,MAAM,OAAQ,OAAO,EAAE,KAAK,QAASqB,CAAK,EAAE,KAAK,SAAUe,CAAM,EAAE,KAAK,IAAKnC,EAAW,EAAC,MAAM,OAAO,EAAE,KAAK,IAAKA,EAAW,EAAC,MAAM,OAAO,CAC9L,EAAG,mBAAmB,EAClBqC,GAAgC/C,EAAO,CAACgD,EAAOC,EAAGC,EAAGzC,IAAM,CAC7D,IAAI0C,EAAa,EACjB,MAAMC,EAAW3C,EAAE,OAAO,MAAM,EAChC2C,EAAS,MAAM,cAAe,OAAO,EACrCA,EAAS,KAAK,QAAS,UAAU,EACjC,IAAIC,EAAOL,EAAM,QAAQ,QAAS,OAAO,EACzCK,EAAOA,EAAK,QAAQ,MAAO,OAAO,EAClC,MAAMC,EAAQD,EAAK,MAAME,EAAe,cAAc,EACtD,IAAIC,EAAU,KAAO9C,EAAS,EAAG,MAAM,WACvC,UAAW+C,KAASH,EAAO,CACzB,MAAMnC,EAAMsC,EAAM,OAClB,GAAItC,EAAI,OAAS,EAAG,CAClB,MAAMuC,EAAON,EAAS,OAAO,OAAO,EAEpC,GADAM,EAAK,KAAKvC,CAAG,EACTqC,IAAY,EAAG,CACjB,MAAMG,EAAaD,EAAK,KAAM,EAAC,QAAO,EACtCF,GAAWG,EAAW,MACvB,CACDR,GAAcK,EACdE,EAAK,KAAK,IAAKT,EAAIvC,IAAY,MAAM,UAAU,EAC/CgD,EAAK,KAAK,IAAKR,EAAIC,EAAa,KAAOzC,EAAW,EAAC,MAAM,UAAU,CACpE,CACF,CACD,MAAO,CAAE,UAAW0C,EAAS,KAAI,EAAG,UAAU,MAAO,WAAAD,EACvD,EAAG,eAAe,EACdS,GAA2B5D,EAAO,CAACqD,EAAM5C,IAAM,CACjDA,EAAE,KAAK,QAAS,YAAY,EAC5B,MAAMoD,EAAOpD,EAAE,OAAO,MAAM,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,IAAKC,EAAW,EAAC,MAAM,OAAO,EACxEoD,EAAWrD,EAAE,OAAO,GAAG,EACvB,CAAE,UAAAsD,EAAW,WAAAZ,GAAeJ,GAAcM,EAAM,EAAG,EAAGS,CAAQ,EACpE,OAAAD,EAAK,KAAK,SAAUV,EAAa,EAAIzC,EAAW,EAAC,MAAM,UAAU,EACjEmD,EAAK,KAAK,QAASE,EAAYrD,EAAS,EAAG,MAAM,WAAa,CAAC,EACxDmD,CACT,EAAG,UAAU,EACTG,EAA4BhE,EAAO,SAASiE,EAAMpD,EAAU,CAC9D,MAAMqD,EAAKrD,EAAS,GACdsD,EAAY,CAChB,GAAAD,EACA,MAAOrD,EAAS,GAChB,MAAO,EACP,OAAQ,CACZ,EACQJ,EAAIwD,EAAK,OAAO,GAAG,EAAE,KAAK,KAAMC,CAAE,EAAE,KAAK,QAAS,YAAY,EAChErD,EAAS,OAAS,SACpBL,EAAeC,CAAC,EAEdI,EAAS,OAAS,OACpB8B,EAAalC,CAAC,GAEZI,EAAS,OAAS,QAAUA,EAAS,OAAS,SAChD+B,GAAkBnC,EAAGI,CAAQ,EAE3BA,EAAS,OAAS,QACpB+C,GAAS/C,EAAS,KAAK,KAAMJ,CAAC,EAE5BI,EAAS,OAAS,WACpBF,EAAYF,CAAC,EAEXI,EAAS,OAAS,WAAaA,EAAS,aAAa,SAAW,GAClED,EAAgBH,EAAGI,CAAQ,EAEzBA,EAAS,OAAS,WAAaA,EAAS,aAAa,OAAS,GAChEG,EAAeP,EAAGI,CAAQ,EAE5B,MAAMuD,EAAW3D,EAAE,KAAM,EAAC,QAAO,EACjC,OAAA0D,EAAU,MAAQC,EAAS,MAAQ,EAAI1D,EAAW,EAAC,MAAM,QACzDyD,EAAU,OAASC,EAAS,OAAS,EAAI1D,EAAW,EAAC,MAAM,QAC3DH,EAAiB,IAAI2D,EAAIC,CAAS,EAC3BA,CACT,EAAG,WAAW,EACVE,EAAY,EACZC,GAA2BtE,EAAO,SAASiE,EAAMM,EAAMC,EAAU,CACnE,MAAMC,EAAkCzE,EAAO,SAAS0E,EAAM,CAC5D,OAAQA,EAAI,CACV,KAAKC,EAAQ,aAAa,YACxB,MAAO,cACT,KAAKA,EAAQ,aAAa,UACxB,MAAO,YACT,KAAKA,EAAQ,aAAa,YACxB,MAAO,cACT,KAAKA,EAAQ,aAAa,WACxB,MAAO,YACV,CACF,EAAE,iBAAiB,EACpBJ,EAAK,OAASA,EAAK,OAAO,OAAQK,GAAM,CAAC,OAAO,MAAMA,EAAE,CAAC,CAAC,EAC1D,MAAMC,EAAWN,EAAK,OAChBO,EAAeC,EAAI,EAAG,EAAE,SAASC,EAAG,CACxC,OAAOA,EAAE,CACb,CAAG,EAAE,EAAE,SAASA,EAAG,CACf,OAAOA,EAAE,CACb,CAAG,EAAE,MAAMC,CAAU,EACbC,EAAUjB,EAAK,OAAO,MAAM,EAAE,KAAK,IAAKa,EAAaD,CAAQ,CAAC,EAAE,KAAK,KAAM,OAASR,CAAS,EAAE,KAAK,QAAS,YAAY,EAC/H,IAAIc,EAAM,GAUV,GATIzE,EAAS,EAAG,MAAM,sBACpByE,EAAM,OAAO,SAAS,SAAW,KAAO,OAAO,SAAS,KAAO,OAAO,SAAS,SAAW,OAAO,SAAS,OAC1GA,EAAMA,EAAI,QAAQ,MAAO,KAAK,EAC9BA,EAAMA,EAAI,QAAQ,MAAO,KAAK,GAEhCD,EAAQ,KACN,aACA,OAASC,EAAM,IAAMV,EAAgBE,EAAQ,aAAa,UAAU,EAAI,MAC5E,EACMH,EAAS,QAAU,OAAQ,CAC7B,MAAMY,EAAQnB,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,YAAY,EACnD,CAAE,EAAAhB,EAAG,EAAAC,CAAG,EAAGmC,EAAc,kBAAkBd,EAAK,MAAM,EACtDe,EAAO/B,EAAe,QAAQiB,EAAS,KAAK,EAClD,IAAIjD,EAAc,EAClB,MAAMgE,EAAY,CAAA,EAClB,IAAIC,EAAW,EACXC,EAAO,EACX,QAASC,EAAI,EAAGA,GAAKJ,EAAK,OAAQI,IAAK,CACrC,MAAMpD,EAAQ8C,EAAM,OAAO,MAAM,EAAE,KAAK,cAAe,QAAQ,EAAE,KAAKE,EAAKI,CAAC,CAAC,EAAE,KAAK,IAAKzC,CAAC,EAAE,KAAK,IAAKC,EAAI3B,CAAW,EAC/GoE,EAAYrD,EAAM,KAAM,EAAC,QAAO,EACtCkD,EAAW,KAAK,IAAIA,EAAUG,EAAU,KAAK,EAC7CF,EAAO,KAAK,IAAIA,EAAME,EAAU,CAAC,EACjCC,EAAI,KAAKD,EAAU,EAAG1C,EAAGC,EAAI3B,CAAW,EACpCA,IAAgB,IAElBA,EADiBe,EAAM,KAAM,EAAC,QAAO,EACd,OACvBsD,EAAI,KAAK,eAAgBrE,EAAa2B,CAAC,GAEzCqC,EAAU,KAAKjD,CAAK,CACrB,CACD,IAAIuD,EAAYtE,EAAc+D,EAAK,OACnC,GAAIA,EAAK,OAAS,EAAG,CACnB,MAAMQ,GAAaR,EAAK,OAAS,GAAK/D,EAAc,GACpDgE,EAAU,QAAQ,CAACjD,EAAOoD,IAAMpD,EAAM,KAAK,IAAKY,EAAIwC,EAAInE,EAAcuE,CAAS,CAAC,EAChFD,EAAYtE,EAAc+D,EAAK,MAChC,CACD,MAAMS,EAASX,EAAM,KAAM,EAAC,QAAO,EACnCA,EAAM,OAAO,OAAQ,cAAc,EAAE,KAAK,QAAS,KAAK,EAAE,KAAK,IAAKnC,EAAIuC,EAAW,EAAI9E,EAAS,EAAG,MAAM,QAAU,CAAC,EAAE,KAAK,IAAKwC,EAAI2C,EAAY,EAAInF,IAAY,MAAM,QAAU,EAAI,GAAG,EAAE,KAAK,QAAS8E,EAAW9E,EAAS,EAAG,MAAM,OAAO,EAAE,KAAK,SAAUmF,EAAYnF,EAAW,EAAC,MAAM,OAAO,EACjSkF,EAAI,KAAKG,CAAM,CAChB,CACD1B,GACF,EAAG,UAAU,EAGT2B,EACAC,EAAoB,CAAA,EACpBC,GAA0BlG,EAAO,UAAW,CAChD,EAAG,SAAS,EACRmG,GAAgCnG,EAAO,SAASiE,EAAM,CACxDA,EAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,KAAM,eAAe,EAAE,KAAK,OAAQ,EAAE,EAAE,KAAK,OAAQ,CAAC,EAAE,KAAK,cAAe,EAAE,EAAE,KAAK,eAAgB,EAAE,EAAE,KAAK,SAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,IAAK,2BAA2B,CAChO,EAAG,eAAe,EACdmC,GAAuBpG,EAAO,SAASqD,EAAMa,EAAImC,EAAUC,EAAS,CACtEN,EAAOtF,EAAW,EAAC,MACnB,MAAM6F,EAAgB7F,EAAW,EAAC,cAClC,IAAI8F,EACAD,IAAkB,YACpBC,EAAiBC,EAAO,KAAOvC,CAAE,GAEnC,MAAMwC,EAAOH,IAAkB,UAAYE,EAAOD,EAAe,MAAK,EAAG,CAAC,EAAE,gBAAgB,IAAI,EAAIC,EAAO,MAAM,EAC3GE,EAAMJ,IAAkB,UAAYC,EAAe,MAAK,EAAG,CAAC,EAAE,gBAAkB,SACtFZ,EAAI,MAAM,qBAAuBvC,CAAI,EACrC,MAAMuD,EAAWF,EAAK,OAAO,QAAQxC,CAAE,IAAI,EAC3CiC,GAAcS,CAAQ,EACtB,MAAMC,EAAUP,EAAQ,GAAG,WAAU,EACrCQ,EAAUD,EAASD,EAAU,OAAQ,GAAOF,EAAMC,EAAKL,CAAO,EAC9D,MAAMS,EAAUf,EAAK,QACfD,EAASa,EAAS,KAAM,EAAC,QAAO,EAChC9E,EAAQiE,EAAO,MAAQgB,EAAU,EACjClE,EAASkD,EAAO,OAASgB,EAAU,EACnCC,EAAWlF,EAAQ,KACzBmF,EAAiBL,EAAU/D,EAAQmE,EAAUhB,EAAK,WAAW,EAC7DY,EAAS,KACP,UACA,GAAGb,EAAO,EAAIC,EAAK,OAAO,KAAKD,EAAO,EAAIC,EAAK,OAAO,IAAMlE,EAAQ,IAAMe,CAC9E,CACA,EAAG,MAAM,EACLqE,GAAgClH,EAAQqD,GACnCA,EAAOA,EAAK,OAAS2C,EAAK,eAAiB,EACjD,eAAe,EACdc,EAA4B9G,EAAO,CAAC2G,EAAKC,EAAUO,EAAUnF,EAAQ0E,EAAMU,EAAad,IAAY,CACtG,MAAMe,EAAQ,IAAIC,EAAe,CAC/B,SAAU,GACV,WAAY,EAChB,CAAG,EACD,IAAI5B,EACA6B,EAAc,GAClB,IAAK7B,EAAI,EAAGA,EAAIiB,EAAI,OAAQjB,IAC1B,GAAIiB,EAAIjB,CAAC,EAAE,OAAS,WAAY,CAC9B6B,EAAc,GACd,KACD,CAECJ,EACFE,EAAM,SAAS,CACb,QAAS,KACT,WAAY,GACZ,SAAU,GAEV,OAAQ,aACR,QAASE,EAAc,EAAIvB,EAAK,iBAChC,QAASuB,EAAc,EAAI,GAC3B,aAAc,EAGpB,CAAK,EAEDF,EAAM,SAAS,CACb,QAAS,KACT,WAAY,GACZ,SAAU,GAIV,QAASE,EAAc,EAAIvB,EAAK,iBAChC,QAASuB,EAAc,EAAI,GAC3B,OAAQ,aAER,aAAc,EACpB,CAAK,EAEHF,EAAM,oBAAoB,UAAW,CACnC,MAAO,EACX,CAAG,EACD,MAAMG,EAASlB,EAAQ,GAAG,UAAS,EAC7BmB,EAAYnB,EAAQ,GAAG,aAAY,EACnCoB,EAAQ,OAAO,KAAKF,CAAM,EAEhC,UAAWvH,KAAOyH,EAAO,CACvB,MAAM7G,EAAW2G,EAAOvH,CAAG,EACvBkH,IACFtG,EAAS,SAAWsG,GAEtB,IAAIQ,EACJ,GAAI9G,EAAS,IAAK,CAChB,IAAI+G,EAAMhB,EAAS,OAAO,GAAG,EAAE,KAAK,KAAM/F,EAAS,EAAE,EAAE,KAAK,QAAS,YAAY,EACjF8G,EAAOb,EAAUjG,EAAS,IAAK+G,EAAK/G,EAAS,GAAI,CAACmB,EAAQ0E,EAAMU,EAAad,CAAO,EACzE,CACTsB,EAAM7F,EAAe6F,EAAK/G,EAAUmB,CAAM,EAC1C,IAAI6F,EAAYD,EAAI,KAAM,EAAC,QAAO,EAClCD,EAAK,MAAQE,EAAU,MACvBF,EAAK,OAASE,EAAU,OAAS7B,EAAK,QAAU,EAChDC,EAAkBpF,EAAS,EAAE,EAAI,CAAE,EAAGmF,EAAK,kBAK5C,CACP,MACM2B,EAAO3D,EAAU4C,EAAU/F,EAAUwG,CAAK,EAE5C,GAAIxG,EAAS,KAAM,CACjB,MAAMiH,EAAU,CACd,aAAc,CAAE,EAChB,GAAIjH,EAAS,GAAK,QAClB,KAAMA,EAAS,KACf,KAAM,MACd,EACYgD,EAAOG,EAAU4C,EAAUkB,EAAST,CAAK,EAC3CxG,EAAS,KAAK,WAAa,WAC7BwG,EAAM,QAAQM,EAAK,GAAK,QAAS9D,CAAI,EACrCwD,EAAM,QAAQM,EAAK,GAAIA,CAAI,IAE3BN,EAAM,QAAQM,EAAK,GAAIA,CAAI,EAC3BN,EAAM,QAAQM,EAAK,GAAK,QAAS9D,CAAI,GAEvCwD,EAAM,UAAUM,EAAK,GAAIA,EAAK,GAAK,QAAQ,EAC3CN,EAAM,UAAUM,EAAK,GAAK,QAASA,EAAK,GAAK,QAAQ,CAC3D,MACMN,EAAM,QAAQM,EAAK,GAAIA,CAAI,CAE9B,CACD/B,EAAI,MAAM,SAAUyB,EAAM,UAAS,EAAIA,CAAK,EAC5C,IAAIU,EAAM,EACVN,EAAU,QAAQ,SAASjD,EAAU,CACnCuD,IACAnC,EAAI,MAAM,eAAgBpB,CAAQ,EAClC6C,EAAM,QACJ7C,EAAS,IACTA,EAAS,IACT,CACE,SAAAA,EACA,MAAO0C,GAAc1C,EAAS,KAAK,EACnC,OAAQwB,EAAK,YAAczC,EAAe,QAAQiB,EAAS,KAAK,EAAE,OAClE,SAAU,GACX,EACD,KAAOuD,CACb,CACA,CAAG,EACDC,EAAYX,CAAK,EACjBzB,EAAI,MAAM,qBAAsByB,EAAM,MAAO,CAAA,EAC7C,MAAMY,EAAUrB,EAAS,OACzBS,EAAM,MAAK,EAAG,QAAQ,SAASa,EAAG,CAC5BA,IAAM,QAAUb,EAAM,KAAKa,CAAC,IAAM,QACpCtC,EAAI,KAAK,QAAUsC,EAAI,KAAO,KAAK,UAAUb,EAAM,KAAKa,CAAC,CAAC,CAAC,EAC3DxB,EAAK,OAAO,IAAMuB,EAAQ,GAAK,KAAOC,CAAC,EAAE,KACvC,YACA,cAAgBb,EAAM,KAAKa,CAAC,EAAE,EAAIb,EAAM,KAAKa,CAAC,EAAE,MAAQ,GAAK,KAAOb,EAAM,KAAKa,CAAC,EAAE,GAAKjC,EAAkBiC,CAAC,EAAIjC,EAAkBiC,CAAC,EAAE,EAAI,GAAKb,EAAM,KAAKa,CAAC,EAAE,OAAS,GAAK,IAChL,EACMxB,EAAK,OAAO,IAAMuB,EAAQ,GAAK,KAAOC,CAAC,EAAE,KAAK,eAAgBb,EAAM,KAAKa,CAAC,EAAE,EAAIb,EAAM,KAAKa,CAAC,EAAE,MAAQ,CAAC,EACtFd,EAAY,iBAAiB,IAAMa,EAAQ,GAAK,KAAOC,EAAI,WAAW,EAC9E,QAASC,GAAY,CAC5B,MAAMC,EAASD,EAAQ,cACvB,IAAIE,EAAS,EACTC,EAAS,EACTF,IACEA,EAAO,gBACTC,EAASD,EAAO,cAAc,QAAO,EAAG,OAE1CE,EAAS,SAASF,EAAO,aAAa,cAAc,EAAG,EAAE,EACrD,OAAO,MAAME,CAAM,IACrBA,EAAS,IAGbH,EAAQ,aAAa,KAAM,EAAIG,EAAS,CAAC,EACzCH,EAAQ,aAAa,KAAME,EAASC,EAAS,CAAC,CACtD,CAAO,GAED1C,EAAI,MAAM,WAAasC,EAAI,KAAO,KAAK,UAAUb,EAAM,KAAKa,CAAC,CAAC,CAAC,CAErE,CAAG,EACD,IAAI9D,EAAW6D,EAAQ,UACvBZ,EAAM,MAAK,EAAG,QAAQ,SAASkB,EAAG,CAC5BA,IAAM,QAAUlB,EAAM,KAAKkB,CAAC,IAAM,SACpC3C,EAAI,MAAM,QAAU2C,EAAE,EAAI,OAASA,EAAE,EAAI,KAAO,KAAK,UAAUlB,EAAM,KAAKkB,CAAC,CAAC,CAAC,EAC7EjE,GAASsC,EAAUS,EAAM,KAAKkB,CAAC,EAAGlB,EAAM,KAAKkB,CAAC,EAAE,QAAQ,EAE9D,CAAG,EACDnE,EAAW6D,EAAQ,UACnB,MAAM9D,EAAY,CAChB,GAAIgD,GAAsB,OAC1B,MAAOA,GAAsB,OAC7B,MAAO,EACP,OAAQ,CACZ,EACE,OAAAhD,EAAU,MAAQC,EAAS,MAAQ,EAAI4B,EAAK,QAC5C7B,EAAU,OAASC,EAAS,OAAS,EAAI4B,EAAK,QAC9CJ,EAAI,MAAM,eAAgBzB,EAAWkD,CAAK,EACnClD,CACT,EAAG,WAAW,EACVqE,GAAwB,CAC1B,QAAAtC,GACA,KAAAE,EACF,EAGIqC,GAAU,CACZ,OAAQC,EACR,IAAI,IAAK,CACP,OAAO,IAAI/D,EAAQ,CAAC,CACrB,EACD,SAAU6D,GACV,OAAQG,EACR,KAAsB3I,EAAQ4I,GAAQ,CAC/BA,EAAI,QACPA,EAAI,MAAQ,IAEdA,EAAI,MAAM,oBAAsBA,EAAI,mBACrC,EAAE,MAAM,CACX", "x_google_ignoreList": [0]}