import{F as p}from"./KHR_interactivity.DEAVS2UW.js";import{R as s}from"./declarationMapper.UBCwU7BT.js";class G extends p{constructor(n){var t,r;super();const a=n.glTF,i=((t=a.animations)==null?void 0:t.map(o=>o._babylonAnimationGroup))||[];this.animationGroups=this.registerDataOutput("animationGroups",s,i);const e=((r=a.nodes)==null?void 0:r.map(o=>o._babylonTransformNode))||[];this.nodes=this.registerDataOutput("nodes",s,e)}getClassName(){return"FlowGraphGLTFDataProvider"}}export{G as FlowGraphGLTFDataProvider};
//# sourceMappingURL=flowGraphGLTFDataProvider.BfhrUZfi.js.map
