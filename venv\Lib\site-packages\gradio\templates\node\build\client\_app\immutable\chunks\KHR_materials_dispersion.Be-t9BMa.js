import{ar as d,an as p,ao as u}from"./index.BoI39RQH.js";import{GLTFLoader as l}from"./glTFLoader.BetPWe9U.js";const o="KHR_materials_dispersion";class c{constructor(r){this.name=o,this.order=174,this._loader=r,this.enabled=this._loader.isExtensionUsed(o)}dispose(){this._loader=null}loadMaterialPropertiesAsync(r,e,s){return l.LoadExtensionAsync(r,e,this.name,(i,a)=>{const n=new Array;return n.push(this._loader.loadMaterialPropertiesAsync(r,e,s)),n.push(this._loadDispersionPropertiesAsync(i,e,s,a)),Promise.all(n).then(()=>{})})}_loadDispersionPropertiesAsync(r,e,s,i){if(!(s instanceof d))throw new Error(`${r}: Material type not supported`);return!s.subSurface.isRefractionEnabled||!i.dispersion||(s.subSurface.isDispersionEnabled=!0,s.subSurface.dispersion=i.dispersion),Promise.resolve()}}p(o);u(o,!0,t=>new c(t));export{c as KHR_materials_dispersion};
//# sourceMappingURL=KHR_materials_dispersion.Be-t9BMa.js.map
