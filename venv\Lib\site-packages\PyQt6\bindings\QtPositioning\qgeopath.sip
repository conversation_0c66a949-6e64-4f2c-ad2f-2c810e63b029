// qgeopath.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QGeoPath : public QGeoShape
{
%TypeHeaderCode
#include <qgeopath.h>
%End

public:
    QGeoPath();
    QGeoPath(const QList<QGeoCoordinate> &path, const qreal &width = 0.);
    QGeoPath(const QGeoPath &other);
    QGeoPath(const QGeoShape &other);
    ~QGeoPath();
    void setPath(const QList<QGeoCoordinate> &path);
    const QList<QGeoCoordinate> &path() const;
    void setWidth(const qreal &width);
    qreal width() const;
    void translate(double degreesLatitude, double degreesLongitude);
    QGeoPath translated(double degreesLatitude, double degreesLongitude) const;
    double length(qsizetype indexFrom = 0, qsizetype indexTo = -1) const;
    void addCoordinate(const QGeoCoordinate &coordinate);
    void insertCoordinate(qsizetype index, const QGeoCoordinate &coordinate);
    void replaceCoordinate(qsizetype index, const QGeoCoordinate &coordinate);
    QGeoCoordinate coordinateAt(qsizetype index) const;
    bool containsCoordinate(const QGeoCoordinate &coordinate) const;
    void removeCoordinate(const QGeoCoordinate &coordinate);
    void removeCoordinate(qsizetype index);
    QString toString() const;
    qsizetype size() const;
    void clearPath();
};

%End
%If (Qt_6_5_0 -)
QDataStream &operator<<(QDataStream &stream, const QGeoPath &path) /ReleaseGIL/;
%End
%If (Qt_6_5_0 -)
QDataStream &operator>>(QDataStream &stream, QGeoPath &path /Constrained/) /ReleaseGIL/;
%End
