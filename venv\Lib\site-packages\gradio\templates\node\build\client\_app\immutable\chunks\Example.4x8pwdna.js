import{SvelteComponent as u,init as d,safe_not_equal as h,element as g,create_component as b,claim_element as k,children as z,claim_component as v,detach as o,attr as y,toggle_class as m,insert_hydration as w,mount_component as q,transition_in as C,transition_out as E,destroy_component as M}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{M as S}from"./2.B2AoQPnG.js";function D(i){let e,l,s;return l=new S({props:{message:c(i[0]),latex_delimiters:i[5],sanitize_html:i[3],line_breaks:i[4],chatbot:!1}}),{c(){e=g("div"),b(l.$$.fragment),this.h()},l(t){e=k(t,"DIV",{class:!0});var n=z(e);v(l.$$.fragment,n),n.forEach(o),this.h()},h(){y(e,"class","prose svelte-1ayixqk"),m(e,"table",i[1]==="table"),m(e,"gallery",i[1]==="gallery"),m(e,"selected",i[2])},m(t,n){w(t,e,n),q(l,e,null),s=!0},p(t,[n]){const r={};n&1&&(r.message=c(t[0])),n&32&&(r.latex_delimiters=t[5]),n&8&&(r.sanitize_html=t[3]),n&16&&(r.line_breaks=t[4]),l.$set(r),(!s||n&2)&&m(e,"table",t[1]==="table"),(!s||n&2)&&m(e,"gallery",t[1]==="gallery"),(!s||n&4)&&m(e,"selected",t[2])},i(t){s||(C(l.$$.fragment,t),s=!0)},o(t){E(l.$$.fragment,t),s=!1},d(t){t&&o(e),M(l)}}}function c(i,e=60){if(!i)return"";const l=String(i);return l.length<=e?l:l.slice(0,e)+"..."}function I(i,e,l){let{value:s}=e,{type:t}=e,{selected:n=!1}=e,{sanitize_html:r}=e,{line_breaks:f}=e,{latex_delimiters:_}=e;return i.$$set=a=>{"value"in a&&l(0,s=a.value),"type"in a&&l(1,t=a.type),"selected"in a&&l(2,n=a.selected),"sanitize_html"in a&&l(3,r=a.sanitize_html),"line_breaks"in a&&l(4,f=a.line_breaks),"latex_delimiters"in a&&l(5,_=a.latex_delimiters)},[s,t,n,r,f,_]}class B extends u{constructor(e){super(),d(this,e,I,D,h,{value:0,type:1,selected:2,sanitize_html:3,line_breaks:4,latex_delimiters:5})}}export{B as default};
//# sourceMappingURL=Example.4x8pwdna.js.map
