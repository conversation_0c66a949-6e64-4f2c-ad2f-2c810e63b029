"""
Reverie Agents Web界面
基于Gradio的现代化Web应用
"""

import os
import sys
import gradio as gr
import asyncio
from pathlib import Path
from typing import List, Tuple, Optional, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from core.utils.logger import get_logger
from core.utils.config import config_manager
from core.ai_engine import ai_engine
from personas.persona_manager import persona_manager
from memory.context_engine import context_engine
from engine_2d.api import engine_2d

logger = get_logger(__name__)

class ReverieWebApp:
    """Reverie Web应用"""
    
    def __init__(self):
        self.current_session_id = None
        self.conversation_history = []
        
        # 初始化组件
        self.init_components()
    
    def init_components(self):
        """初始化组件"""
        # 加载默认人设
        personas = persona_manager.get_available_personas()
        if personas:
            default_persona = list(personas.keys())[0]
            persona_manager.switch_persona(default_persona)
            self.start_new_session()
    
    def start_new_session(self):
        """开始新会话"""
        current_persona = persona_manager.get_current_persona()
        if current_persona:
            self.current_session_id = context_engine.start_session(current_persona.id)
            self.conversation_history = []
            logger.info(f"开始新会话: {self.current_session_id}")
    
    def chat_response(self, message: str, history: List[List[str]], persona_name: str) -> Tuple[str, List[List[str]]]:
        """处理聊天回复"""
        if not message.strip():
            return "", history
        
        try:
            # 切换人设（如果需要）
            current_persona = persona_manager.get_current_persona()
            if not current_persona or current_persona.name != persona_name:
                # 查找对应的人设ID
                personas = persona_manager.get_available_personas()
                persona_id = None
                for pid, pinfo in personas.items():
                    if pinfo['name'] == persona_name:
                        persona_id = pid
                        break
                
                if persona_id and persona_manager.switch_persona(persona_id):
                    self.start_new_session()
                    current_persona = persona_manager.get_current_persona()
            
            # 添加用户消息到历史
            history.append([message, ""])
            
            # 添加到记忆
            if current_persona:
                context_engine.add_memory(message, "user", current_persona.id)
            
            # 生成AI回复
            ai_response = ""
            for chunk in ai_engine.generate_response(message, current_persona):
                if chunk.startswith("SEARCH:") or chunk.startswith("SEARCH_COMPLETE:"):
                    continue  # 跳过搜索状态信息
                ai_response += chunk
                # 实时更新最后一条消息
                history[-1][1] = ai_response
                yield "", history
            
            # 添加AI回复到记忆
            if current_persona and ai_response:
                context_engine.add_memory(ai_response, "assistant", current_persona.id)
            
            return "", history
            
        except Exception as e:
            logger.error(f"聊天回复失败: {e}")
            error_msg = f"❌ 抱歉，出现了错误: {str(e)}"
            history.append([message, error_msg])
            return "", history
    
    def change_persona(self, persona_name: str) -> Tuple[str, List[List[str]]]:
        """切换人设"""
        try:
            personas = persona_manager.get_available_personas()
            persona_id = None
            
            for pid, pinfo in personas.items():
                if pinfo['name'] == persona_name:
                    persona_id = pid
                    break
            
            if persona_id and persona_manager.switch_persona(persona_id):
                self.start_new_session()
                persona = persona_manager.get_current_persona()
                
                # 返回欢迎消息
                welcome_msg = self.get_welcome_message(persona)
                new_history = [[f"切换到人设: {persona_name}", welcome_msg]]
                
                return f"已切换到人设: {persona_name}", new_history
            else:
                return f"切换人设失败: {persona_name}", []
                
        except Exception as e:
            logger.error(f"切换人设失败: {e}")
            return f"切换人设出错: {str(e)}", []
    
    def get_welcome_message(self, persona) -> str:
        """获取欢迎消息"""
        if not persona:
            return "你好！我是Reverie，很高兴为你服务！"
        
        welcome_messages = {
            'wife': "亲爱的，我回来了～ (◕‿◕) 今天想聊什么呢？我可以陪你聊天、为你画画，或者帮你搜索信息哦！💕",
            'reasoning_expert': "你好！我是推理专家Reverie。我可以帮你分析问题、进行逻辑推理，或者搜索最新的数据和信息。有什么需要分析的问题吗？",
            'travel_guide': "你好！我是你的专属旅行向导Reverie 🗺️ 想去哪里旅行呢？我可以为你推荐目的地、制定行程，或者搜索最新的旅游信息！",
            'career_mentor': "你好！我是职场导师Reverie 💼 在职业发展方面有什么问题吗？我可以提供职业规划建议、技能提升指导，或者搜索最新的行业信息。"
        }
        
        return welcome_messages.get(persona.id, f"你好！我是{persona.name}，很高兴为你服务！")
    
    def generate_image(self, prompt: str, width: int, height: int, steps: int, guidance: float) -> Tuple[Optional[str], str]:
        """生成图像"""
        if not prompt.strip():
            return None, "请输入图像描述"
        
        try:
            # 生成图像
            image_paths = engine_2d.generate(
                prompt=prompt,
                width=width,
                height=height,
                steps=steps,
                guidance=guidance,
                save=True
            )
            
            if image_paths:
                # 返回第一张图片的路径
                return image_paths[0], f"✅ 图像生成成功！\n提示词: {prompt}"
            else:
                return None, "❌ 图像生成失败，请检查模型是否正确加载"
                
        except Exception as e:
            logger.error(f"图像生成失败: {e}")
            return None, f"❌ 图像生成出错: {str(e)}"
    
    def get_model_status(self) -> str:
        """获取模型状态"""
        try:
            status = ai_engine.get_model_status()
            
            status_text = "## 模型状态\n\n"
            
            # LLM模型状态
            llm_model = status.get('llm_model')
            if llm_model:
                status_text += f"**语言模型**: {llm_model['name']} ✅\n"
            else:
                status_text += "**语言模型**: 未加载 ❌\n"
            
            # 图像模型状态
            image_model = status.get('image_model')
            if image_model:
                status_text += f"**图像模型**: {image_model} ✅\n"
            else:
                status_text += "**图像模型**: 未加载 ❌\n"
            
            # 可用模型
            available_llm = status.get('available_llm_models', [])
            available_image = status.get('available_image_models', [])
            
            status_text += f"\n**可用语言模型**: {len(available_llm)} 个\n"
            status_text += f"**可用图像模型**: {len(available_image)} 个\n"
            
            return status_text
            
        except Exception as e:
            return f"获取模型状态失败: {str(e)}"
    
    def clear_conversation(self) -> Tuple[List[List[str]], str]:
        """清空对话"""
        self.start_new_session()
        return [], "对话已清空"
    
    def create_interface(self) -> gr.Blocks:
        """创建Gradio界面"""
        # 获取可用人设
        personas = persona_manager.get_available_personas()
        persona_choices = [info['name'] for info in personas.values()]
        
        # 自定义CSS
        custom_css = """
        .gradio-container {
            max-width: 1200px !important;
        }
        .chat-container {
            height: 600px;
        }
        .persona-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
        }
        .status-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        """
        
        with gr.Blocks(css=custom_css, title="Reverie Agents", theme=gr.themes.Soft()) as interface:
            gr.HTML("""
            <div style="text-align: center; padding: 20px;">
                <h1 style="color: #667eea; margin-bottom: 10px;">🌟 Reverie Agents</h1>
                <p style="color: #666; font-size: 18px;">AI陪伴对话系统 - 你的智能伙伴</p>
            </div>
            """)
            
            with gr.Row():
                with gr.Column(scale=3):
                    # 人设选择
                    with gr.Group():
                        gr.HTML('<div class="persona-info"><h3>🎭 人设选择</h3></div>')
                        persona_dropdown = gr.Dropdown(
                            choices=persona_choices,
                            value=persona_choices[0] if persona_choices else None,
                            label="选择AI人设",
                            info="不同人设有不同的性格和专长"
                        )
                        
                        persona_info = gr.Markdown(
                            value=self.get_persona_info(persona_choices[0] if persona_choices else ""),
                            label="人设信息"
                        )
                    
                    # 聊天界面
                    with gr.Group():
                        gr.HTML('<h3>💬 对话聊天</h3>')
                        chatbot = gr.Chatbot(
                            value=[],
                            height=400,
                            show_label=False,
                            container=True,
                            type="messages"
                        )
                        
                        with gr.Row():
                            msg_input = gr.Textbox(
                                placeholder="输入你的消息...",
                                show_label=False,
                                scale=4,
                                container=False
                            )
                            send_btn = gr.Button("发送", variant="primary", scale=1)
                            clear_btn = gr.Button("清空", variant="secondary", scale=1)
                
                with gr.Column(scale=1):
                    # 图像生成
                    with gr.Group():
                        gr.HTML('<h3>🎨 图像生成</h3>')
                        
                        image_prompt = gr.Textbox(
                            label="图像描述",
                            placeholder="描述你想要的图像...",
                            lines=3
                        )
                        
                        with gr.Row():
                            img_width = gr.Slider(256, 2048, 1024, step=64, label="宽度")
                            img_height = gr.Slider(256, 2048, 1024, step=64, label="高度")
                        
                        with gr.Row():
                            img_steps = gr.Slider(1, 100, 20, step=1, label="步数")
                            img_guidance = gr.Slider(1.0, 20.0, 7.5, step=0.5, label="引导强度")
                        
                        generate_btn = gr.Button("生成图像", variant="primary")
                        
                        generated_image = gr.Image(label="生成的图像", show_label=True)
                        image_status = gr.Markdown(value="", label="生成状态")
                    
                    # 系统状态
                    with gr.Group():
                        gr.HTML('<h3>⚙️ 系统状态</h3>')
                        status_display = gr.Markdown(
                            value=self.get_model_status(),
                            label="模型状态"
                        )
                        refresh_status_btn = gr.Button("刷新状态", size="sm")
            
            # 事件绑定
            def update_persona_info(persona_name):
                return self.get_persona_info(persona_name)
            
            def handle_persona_change(persona_name):
                status, history = self.change_persona(persona_name)
                info = self.get_persona_info(persona_name)
                return history, info, status
            
            # 人设切换
            persona_dropdown.change(
                fn=handle_persona_change,
                inputs=[persona_dropdown],
                outputs=[chatbot, persona_info, gr.Textbox(visible=False)]
            )
            
            # 聊天功能
            msg_input.submit(
                fn=self.chat_response,
                inputs=[msg_input, chatbot, persona_dropdown],
                outputs=[msg_input, chatbot]
            )
            
            send_btn.click(
                fn=self.chat_response,
                inputs=[msg_input, chatbot, persona_dropdown],
                outputs=[msg_input, chatbot]
            )
            
            clear_btn.click(
                fn=self.clear_conversation,
                outputs=[chatbot, gr.Textbox(visible=False)]
            )
            
            # 图像生成
            generate_btn.click(
                fn=self.generate_image,
                inputs=[image_prompt, img_width, img_height, img_steps, img_guidance],
                outputs=[generated_image, image_status]
            )
            
            # 状态刷新
            refresh_status_btn.click(
                fn=self.get_model_status,
                outputs=[status_display]
            )
            
            # 初始化欢迎消息
            interface.load(
                fn=lambda: self.get_initial_conversation(),
                outputs=[chatbot]
            )
        
        return interface
    
    def get_persona_info(self, persona_name: str) -> str:
        """获取人设信息"""
        personas = persona_manager.get_available_personas()
        for persona_id, persona_info in personas.items():
            if persona_info['name'] == persona_name:
                nsfw_status = "🔞 支持成人内容" if persona_info.get('nsfw_enabled', False) else "✅ 安全内容"
                return f"""
**{persona_info['name']}**

{persona_info['description']}

**内容等级**: {nsfw_status}
                """
        return "未找到人设信息"
    
    def get_initial_conversation(self) -> List[List[str]]:
        """获取初始对话"""
        current_persona = persona_manager.get_current_persona()
        if current_persona:
            welcome_msg = self.get_welcome_message(current_persona)
            return [["", welcome_msg]]
        return []

def create_app() -> gr.Blocks:
    """创建Web应用"""
    app = ReverieWebApp()
    return app.create_interface()

def main():
    """主函数"""
    logger.info("启动Reverie Agents Web界面")
    
    # 创建应用
    interface = create_app()
    
    # 启动服务器
    try:
        interface.launch(
            server_name="127.0.0.1",
            server_port=7860,
            share=False,
            debug=False,
            show_error=True,
            quiet=False,
            inbrowser=False,
            prevent_thread_lock=False
        )
    except Exception as e:
        logger.error(f"Web界面启动失败: {e}")
        print(f"❌ Web界面启动失败: {e}")
        print("💡 请尝试手动访问: http://127.0.0.1:7860")

if __name__ == "__main__":
    main()
