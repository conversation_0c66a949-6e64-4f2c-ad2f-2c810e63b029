import{ap as A,M as S,aW as I,aX as E,aY as M,aZ as R,a_ as N,a$ as B,b0 as k,O as P,Q as F,a as O,V as C,p as D,ak as W,aP as w}from"./index-Dpxo-yl_.js";import{B as K}from"./bone-kZWM5-u7.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";class Y{get currentFrame(){return this._currentFrame}get weight(){return this._weight}get currentValue(){return this._currentValue}get targetPath(){return this._targetPath}get target(){return this._currentActiveTarget}get isAdditive(){return this._host&&this._host.isAdditive}constructor(t,e,i,n){if(this._events=new Array,this._currentFrame=0,this._originalValue=new Array,this._originalBlendValue=null,this._offsetsCache={},this._highLimitsCache={},this._stopped=!1,this._blendingFactor=0,this._currentValue=null,this._currentActiveTarget=null,this._directTarget=null,this._targetPath="",this._weight=1,this._absoluteFrameOffset=0,this._previousElapsedTime=0,this._yoyoDirection=1,this._previousAbsoluteFrame=0,this._targetIsArray=!1,this._animation=e,this._target=t,this._scene=i,this._host=n,this._activeTargets=[],e._runtimeAnimations.push(this),this._animationState={key:0,repeatCount:0,loopMode:this._getCorrectLoopMode()},this._animation.dataType===A.ANIMATIONTYPE_MATRIX&&(this._animationState.workValue=S.Zero()),this._keys=this._animation.getKeys(),this._minFrame=this._keys[0].frame,this._maxFrame=this._keys[this._keys.length-1].frame,this._minValue=this._keys[0].value,this._maxValue=this._keys[this._keys.length-1].value,this._minFrame!==0){const a={frame:0,value:this._minValue};this._keys.splice(0,0,a)}if(this._target instanceof Array){let a=0;for(const r of this._target)this._preparePath(r,a),this._getOriginalValues(a),a++;this._targetIsArray=!0}else this._preparePath(this._target),this._getOriginalValues(),this._targetIsArray=!1,this._directTarget=this._activeTargets[0];const s=e.getEvents();s&&s.length>0&&s.forEach(a=>{this._events.push(a._clone())}),this._enableBlending=t&&t.animationPropertiesOverride?t.animationPropertiesOverride.enableBlending:this._animation.enableBlending}_preparePath(t,e=0){const i=this._animation.targetPropertyPath;if(i.length>1){let n=t;for(let s=0;s<i.length-1;s++){const a=i[s];if(n=n[a],n===void 0)throw new Error(`Invalid property (${a}) in property path (${i.join(".")})`)}this._targetPath=i[i.length-1],this._activeTargets[e]=n}else this._targetPath=i[0],this._activeTargets[e]=t;if(this._activeTargets[e][this._targetPath]===void 0)throw new Error(`Invalid property (${this._targetPath}) in property path (${i.join(".")})`)}get animation(){return this._animation}reset(t=!1){if(t)if(this._target instanceof Array){let e=0;for(const i of this._target)this._originalValue[e]!==void 0&&this._setValue(i,this._activeTargets[e],this._originalValue[e],-1,e),e++}else this._originalValue[0]!==void 0&&this._setValue(this._target,this._directTarget,this._originalValue[0],-1,0);this._offsetsCache={},this._highLimitsCache={},this._currentFrame=0,this._blendingFactor=0;for(let e=0;e<this._events.length;e++)this._events[e].isDone=!1}isStopped(){return this._stopped}dispose(){const t=this._animation.runtimeAnimations.indexOf(this);t>-1&&this._animation.runtimeAnimations.splice(t,1)}setValue(t,e){if(this._targetIsArray){for(let i=0;i<this._target.length;i++){const n=this._target[i];this._setValue(n,this._activeTargets[i],t,e,i)}return}this._setValue(this._target,this._directTarget,t,e,0)}_getOriginalValues(t=0){let e;const i=this._activeTargets[t];i.getLocalMatrix&&this._targetPath==="_matrix"?e=i.getLocalMatrix():e=i[this._targetPath],e&&e.clone?this._originalValue[t]=e.clone():this._originalValue[t]=e}_registerTargetForLateAnimationBinding(t,e){const i=t.target;this._scene._registeredForLateAnimationBindings.pushNoDuplicate(i),i._lateAnimationHolders||(i._lateAnimationHolders={}),i._lateAnimationHolders[t.targetPath]||(i._lateAnimationHolders[t.targetPath]={totalWeight:0,totalAdditiveWeight:0,animations:[],additiveAnimations:[],originalValue:e}),t.isAdditive?(i._lateAnimationHolders[t.targetPath].additiveAnimations.push(t),i._lateAnimationHolders[t.targetPath].totalAdditiveWeight+=t.weight):(i._lateAnimationHolders[t.targetPath].animations.push(t),i._lateAnimationHolders[t.targetPath].totalWeight+=t.weight)}_setValue(t,e,i,n,s){if(this._currentActiveTarget=e,this._weight=n,this._enableBlending&&this._blendingFactor<=1){if(!this._originalBlendValue){const r=e[this._targetPath];r.clone?this._originalBlendValue=r.clone():this._originalBlendValue=r}this._originalBlendValue.m?A.AllowMatrixDecomposeForInterpolation?this._currentValue?S.DecomposeLerpToRef(this._originalBlendValue,i,this._blendingFactor,this._currentValue):this._currentValue=S.DecomposeLerp(this._originalBlendValue,i,this._blendingFactor):this._currentValue?S.LerpToRef(this._originalBlendValue,i,this._blendingFactor,this._currentValue):this._currentValue=S.Lerp(this._originalBlendValue,i,this._blendingFactor):this._currentValue=A._UniversalLerp(this._originalBlendValue,i,this._blendingFactor);const a=t&&t.animationPropertiesOverride?t.animationPropertiesOverride.blendingSpeed:this._animation.blendingSpeed;this._blendingFactor+=a}else this._currentValue?this._currentValue.copyFrom?this._currentValue.copyFrom(i):this._currentValue=i:i?.clone?this._currentValue=i.clone():this._currentValue=i;n!==-1?this._registerTargetForLateAnimationBinding(this,this._originalValue[s]):this._animationState.loopMode===A.ANIMATIONLOOPMODE_RELATIVE_FROM_CURRENT?this._currentValue.addToRef?this._currentValue.addToRef(this._originalValue[s],e[this._targetPath]):e[this._targetPath]=this._originalValue[s]+this._currentValue:e[this._targetPath]=this._currentValue,t.markAsDirty&&t.markAsDirty(this._animation.targetProperty)}_getCorrectLoopMode(){return this._target&&this._target.animationPropertiesOverride?this._target.animationPropertiesOverride.loopMode:this._animation.loopMode}goToFrame(t,e=-1){const i=this._animation.getKeys();t<i[0].frame?t=i[0].frame:t>i[i.length-1].frame&&(t=i[i.length-1].frame);const n=this._events;if(n.length)for(let a=0;a<n.length;a++)n[a].onlyOnce||(n[a].isDone=n[a].frame<t);this._currentFrame=t;const s=this._animation._interpolate(t,this._animationState);this.setValue(s,e)}_prepareForSpeedRatioChange(t){const e=this._previousElapsedTime*(this._animation.framePerSecond*t)/1e3;this._absoluteFrameOffset=this._previousAbsoluteFrame-e}animate(t,e,i,n,s,a=-1){const r=this._animation,o=r.targetPropertyPath;if(!o||o.length<1)return this._stopped=!0,!1;let l=!0;(e<this._minFrame||e>this._maxFrame)&&(e=this._minFrame),(i<this._minFrame||i>this._maxFrame)&&(i=this._maxFrame);const m=i-e;let g,c=t*(r.framePerSecond*s)/1e3+this._absoluteFrameOffset,_=0,d=!1;const u=n&&this._animationState.loopMode===A.ANIMATIONLOOPMODE_YOYO;if(u){const f=(c-e)/m,b=Math.sin(f*Math.PI);c=Math.abs(b)*m+e;const L=b>=0?1:-1;this._yoyoDirection!==L&&(d=!0),this._yoyoDirection=L}if(this._previousElapsedTime=t,this._previousAbsoluteFrame=c,!n&&i>=e&&(c>=m&&s>0||c<=0&&s<0))l=!1,_=r._getKeyValue(this._maxValue);else if(!n&&e>=i&&(c<=m&&s<0||c>=0&&s>0))l=!1,_=r._getKeyValue(this._minValue);else if(this._animationState.loopMode!==A.ANIMATIONLOOPMODE_CYCLE){const f=i.toString()+e.toString();if(!this._offsetsCache[f]){this._animationState.repeatCount=0,this._animationState.loopMode=A.ANIMATIONLOOPMODE_CYCLE;const b=r._interpolate(e,this._animationState),T=r._interpolate(i,this._animationState);switch(this._animationState.loopMode=this._getCorrectLoopMode(),r.dataType){case A.ANIMATIONTYPE_FLOAT:this._offsetsCache[f]=T-b;break;case A.ANIMATIONTYPE_QUATERNION:this._offsetsCache[f]=T.subtract(b);break;case A.ANIMATIONTYPE_VECTOR3:this._offsetsCache[f]=T.subtract(b);break;case A.ANIMATIONTYPE_VECTOR2:this._offsetsCache[f]=T.subtract(b);break;case A.ANIMATIONTYPE_SIZE:this._offsetsCache[f]=T.subtract(b);break;case A.ANIMATIONTYPE_COLOR3:this._offsetsCache[f]=T.subtract(b);break}this._highLimitsCache[f]=T}_=this._highLimitsCache[f],g=this._offsetsCache[f]}if(g===void 0)switch(r.dataType){case A.ANIMATIONTYPE_FLOAT:g=0;break;case A.ANIMATIONTYPE_QUATERNION:g=B;break;case A.ANIMATIONTYPE_VECTOR3:g=N;break;case A.ANIMATIONTYPE_VECTOR2:g=R;break;case A.ANIMATIONTYPE_SIZE:g=M;break;case A.ANIMATIONTYPE_COLOR3:g=E;break;case A.ANIMATIONTYPE_COLOR4:g=I;break}let p;if(this._host&&this._host.syncRoot){const f=this._host.syncRoot,b=(f.masterFrame-f.fromFrame)/(f.toFrame-f.fromFrame);p=e+m*b}else c>0&&e>i||c<0&&e<i?p=l&&m!==0?i+c%m:e:p=l&&m!==0?e+c%m:i;const y=this._events;if(!u&&(s>0&&this.currentFrame>p||s<0&&this.currentFrame<p)||u&&d){this._onLoop();for(let f=0;f<y.length;f++)y[f].onlyOnce||(y[f].isDone=!1);this._animationState.key=s>0?0:r.getKeys().length-1}this._currentFrame=p,this._animationState.repeatCount=m===0?0:c/m>>0,this._animationState.highLimitValue=_,this._animationState.offsetValue=g;const v=r._interpolate(p,this._animationState);if(this.setValue(v,a),y.length){for(let f=0;f<y.length;f++)if(m>=0&&p>=y[f].frame&&y[f].frame>=e||m<0&&p<=y[f].frame&&y[f].frame<=e){const b=y[f];b.isDone||(b.onlyOnce&&(y.splice(f,1),f--),b.isDone=!0,b.action(p))}}return l||(this._stopped=!0),l}}class x{get syncRoot(){return this._syncRoot}get masterFrame(){return this._runtimeAnimations.length===0?0:this._runtimeAnimations[0].currentFrame}get weight(){return this._weight}set weight(t){if(t===-1){this._weight=-1;return}this._weight=Math.min(Math.max(t,0),1)}get speedRatio(){return this._speedRatio}set speedRatio(t){for(let e=0;e<this._runtimeAnimations.length;e++)this._runtimeAnimations[e]._prepareForSpeedRatioChange(t);this._speedRatio=t,this._goToFrame!==null&&this.goToFrame(this._goToFrame)}get elapsedTime(){return this._localDelayOffset===null?0:this._scene._animationTime-this._localDelayOffset}constructor(t,e,i=0,n=100,s=!1,a=1,r,o,l,m=!1,g=0){this.target=e,this.fromFrame=i,this.toFrame=n,this.loopAnimation=s,this.onAnimationEnd=r,this.onAnimationLoop=l,this.isAdditive=m,this.playOrder=g,this._localDelayOffset=null,this._pausedDelay=null,this._manualJumpDelay=null,this._runtimeAnimations=new Array,this._paused=!1,this._speedRatio=1,this._weight=-1,this._previousWeight=-1,this._syncRoot=null,this._frameToSyncFromJump=null,this._goToFrame=null,this.disposeOnEnd=!0,this.animationStarted=!1,this.onAnimationEndObservable=new P,this.onAnimationLoopObservable=new P,this._scene=t,o&&this.appendAnimations(e,o),this._speedRatio=a,t._activeAnimatables.push(this)}syncWith(t){if(this._syncRoot=t,t){const e=this._scene._activeAnimatables.indexOf(this);e>-1&&(this._scene._activeAnimatables.splice(e,1),this._scene._activeAnimatables.push(this))}return this}getAnimations(){return this._runtimeAnimations}appendAnimations(t,e){for(let i=0;i<e.length;i++){const n=e[i],s=new Y(t,n,this._scene,this);s._onLoop=()=>{this.onAnimationLoopObservable.notifyObservers(this),this.onAnimationLoop&&this.onAnimationLoop()},this._runtimeAnimations.push(s)}}getAnimationByTargetProperty(t){const e=this._runtimeAnimations;for(let i=0;i<e.length;i++)if(e[i].animation.targetProperty===t)return e[i].animation;return null}getRuntimeAnimationByTargetProperty(t){const e=this._runtimeAnimations;for(let i=0;i<e.length;i++)if(e[i].animation.targetProperty===t)return e[i];return null}reset(){const t=this._runtimeAnimations;for(let e=0;e<t.length;e++)t[e].reset(!0);this._localDelayOffset=null,this._pausedDelay=null}enableBlending(t){const e=this._runtimeAnimations;for(let i=0;i<e.length;i++)e[i].animation.enableBlending=!0,e[i].animation.blendingSpeed=t}disableBlending(){const t=this._runtimeAnimations;for(let e=0;e<t.length;e++)t[e].animation.enableBlending=!1}goToFrame(t,e=!1){const i=this._runtimeAnimations;if(i[0]){const n=i[0].animation.framePerSecond;this._frameToSyncFromJump=this._frameToSyncFromJump??i[0].currentFrame;const s=this.speedRatio===0?0:(t-this._frameToSyncFromJump)/n*1e3/this.speedRatio;this._manualJumpDelay=-s}for(let n=0;n<i.length;n++)i[n].goToFrame(t,e?this._weight:-1);this._goToFrame=t}get paused(){return this._paused}pause(){this._paused||(this._paused=!0)}restart(){this._paused=!1}_raiseOnAnimationEnd(){this.onAnimationEnd&&this.onAnimationEnd(),this.onAnimationEndObservable.notifyObservers(this)}stop(t,e,i=!1,n=!1){if(t||e){const s=this._scene._activeAnimatables.indexOf(this);if(s>-1){const a=this._runtimeAnimations;for(let r=a.length-1;r>=0;r--){const o=a[r];t&&o.animation.name!=t||e&&!e(o.target)||(o.dispose(),a.splice(r,1))}a.length==0&&(i||this._scene._activeAnimatables.splice(s,1),n||this._raiseOnAnimationEnd())}}else{const s=this._scene._activeAnimatables.indexOf(this);if(s>-1){i||this._scene._activeAnimatables.splice(s,1);const a=this._runtimeAnimations;for(let r=0;r<a.length;r++)a[r].dispose();this._runtimeAnimations.length=0,n||this._raiseOnAnimationEnd()}}}waitAsync(){return new Promise(t=>{this.onAnimationEndObservable.add(()=>{t(this)},void 0,void 0,this,!0)})}_animate(t){if(this._paused)return this.animationStarted=!1,this._pausedDelay===null&&(this._pausedDelay=t),!0;if(this._localDelayOffset===null?(this._localDelayOffset=t,this._pausedDelay=null):this._pausedDelay!==null&&(this._localDelayOffset+=t-this._pausedDelay,this._pausedDelay=null),this._manualJumpDelay!==null&&(this._localDelayOffset+=this._manualJumpDelay,this._manualJumpDelay=null,this._frameToSyncFromJump=null),this._goToFrame=null,this._weight===0&&this._previousWeight===0)return!0;this._previousWeight=this._weight;let e=!1;const i=this._runtimeAnimations;let n;for(n=0;n<i.length;n++){const a=i[n].animate(t-this._localDelayOffset,this.fromFrame,this.toFrame,this.loopAnimation,this._speedRatio,this._weight);e=e||a}if(this.animationStarted=e,!e){if(this.disposeOnEnd)for(n=this._scene._activeAnimatables.indexOf(this),this._scene._activeAnimatables.splice(n,1),n=0;n<i.length;n++)i[n].dispose();this._raiseOnAnimationEnd(),this.disposeOnEnd&&(this.onAnimationEnd=null,this.onAnimationLoop=null,this.onAnimationLoopObservable.clear(),this.onAnimationEndObservable.clear())}return e}}function U(h){if(h.totalWeight===0&&h.totalAdditiveWeight===0)return h.originalValue;let t=1;const e=O.Vector3[0],i=O.Vector3[1],n=O.Quaternion[0];let s=0;const a=h.animations[0],r=h.originalValue;let o=1,l=!1;if(h.totalWeight<1)o=1-h.totalWeight,r.decompose(i,n,e);else{if(s=1,t=h.totalWeight,o=a.weight/t,o==1)if(h.totalAdditiveWeight)l=!0;else return a.currentValue;a.currentValue.decompose(i,n,e)}if(!l){i.scaleInPlace(o),e.scaleInPlace(o),n.scaleInPlace(o);for(let g=s;g<h.animations.length;g++){const c=h.animations[g];if(c.weight===0)continue;o=c.weight/t;const _=O.Vector3[2],d=O.Vector3[3],u=O.Quaternion[1];c.currentValue.decompose(d,u,_),d.scaleAndAddToRef(o,i),u.scaleAndAddToRef(F.Dot(n,u)>0?o:-o,n),_.scaleAndAddToRef(o,e)}n.normalize()}for(let g=0;g<h.additiveAnimations.length;g++){const c=h.additiveAnimations[g];if(c.weight===0)continue;const _=O.Vector3[2],d=O.Vector3[3],u=O.Quaternion[1];c.currentValue.decompose(d,u,_),d.multiplyToRef(i,d),C.LerpToRef(i,d,c.weight,i),n.multiplyToRef(u,u),F.SlerpToRef(n,u,c.weight,n),_.scaleAndAddToRef(c.weight,e)}const m=a?a._animationState.workValue:O.Matrix[0].clone();return S.ComposeToRef(i,n,e,m),m}function z(h,t){if(h.totalWeight===0&&h.totalAdditiveWeight===0)return t;const e=h.animations[0],i=h.originalValue;let n=t;if(h.totalWeight===0&&h.totalAdditiveWeight>0)n.copyFrom(i);else if(h.animations.length===1){if(F.SlerpToRef(i,e.currentValue,Math.min(1,h.totalWeight),n),h.totalAdditiveWeight===0)return n}else if(h.animations.length>1){let s=1,a,r;if(h.totalWeight<1){const l=1-h.totalWeight;a=[],r=[],a.push(i),r.push(l)}else{if(h.animations.length===2&&(F.SlerpToRef(h.animations[0].currentValue,h.animations[1].currentValue,h.animations[1].weight/h.totalWeight,t),h.totalAdditiveWeight===0))return t;a=[],r=[],s=h.totalWeight}for(let l=0;l<h.animations.length;l++){const m=h.animations[l];a.push(m.currentValue),r.push(m.weight/s)}let o=0;for(let l=0;l<a.length;){if(!l){F.SlerpToRef(a[l],a[l+1],r[l+1]/(r[l]+r[l+1]),t),n=t,o=r[l]+r[l+1],l+=2;continue}o+=r[l],F.SlerpToRef(n,a[l],r[l]/o,n),l++}}for(let s=0;s<h.additiveAnimations.length;s++){const a=h.additiveAnimations[s];a.weight!==0&&(n.multiplyToRef(a.currentValue,O.Quaternion[0]),F.SlerpToRef(n,O.Quaternion[0],a.weight,n))}return n}function H(h){if(h._registeredForLateAnimationBindings.length){for(let t=0;t<h._registeredForLateAnimationBindings.length;t++){const e=h._registeredForLateAnimationBindings.data[t];for(const i in e._lateAnimationHolders){const n=e._lateAnimationHolders[i],s=n.animations[0],a=n.originalValue;if(a==null)continue;const r=A.AllowMatrixDecomposeForInterpolation&&a.m;let o=e[i];if(r)o=U(n);else if(a.w!==void 0)o=z(n,o||F.Identity());else{let m=0,g=1;const c=s&&s._animationState.loopMode===A.ANIMATIONLOOPMODE_RELATIVE_FROM_CURRENT;if(n.totalWeight<1)c?o=a.clone?a.clone():a:s&&a.scale?o=a.scale(1-n.totalWeight):s?o=a*(1-n.totalWeight):a.clone?o=a.clone():o=a;else if(s){g=n.totalWeight;const _=s.weight/g;_!==1?s.currentValue.scale?o=s.currentValue.scale(_):o=s.currentValue*_:o=s.currentValue,c&&(o.addToRef?o.addToRef(a,o):o+=a),m=1}for(let _=m;_<n.animations.length;_++){const d=n.animations[_],u=d.weight/g;if(u)d.currentValue.scaleAndAddToRef?d.currentValue.scaleAndAddToRef(u,o):o+=d.currentValue*u;else continue}for(let _=0;_<n.additiveAnimations.length;_++){const d=n.additiveAnimations[_],u=d.weight;if(u)d.currentValue.scaleAndAddToRef?d.currentValue.scaleAndAddToRef(u,o):o+=d.currentValue*u;else continue}}e[i]=o}e._lateAnimationHolders={}}h._registeredForLateAnimationBindings.reset()}}function X(h,t){t&&(t.prototype.copyAnimationRange=function(e,i,n,s=!1,a=null){this.animations.length===0&&(this.animations.push(new A(this.name,"_matrix",e.animations[0].framePerSecond,A.ANIMATIONTYPE_MATRIX,0)),this.animations[0].setKeys([]));const r=e.animations[0].getRange(i);if(!r)return!1;const o=r.from,l=r.to,m=e.animations[0].getKeys(),g=e.length,c=e.getParent(),_=this.getParent(),d=s&&c&&g&&this.length&&g!==this.length,u=d&&_&&c?_.length/c.length:1,p=s&&!_&&a&&(a.x!==1||a.y!==1||a.z!==1),y=this.animations[0].getKeys();let v,f,b;for(let T=0,L=m.length;T<L;T++)v=m[T],v.frame>=o&&v.frame<=l&&(s?(b=v.value.clone(),d?(f=b.getTranslation(),b.setTranslation(f.scaleInPlace(u))):p&&a?(f=b.getTranslation(),b.setTranslation(f.multiplyInPlace(a))):b=v.value):b=v.value,y.push({frame:v.frame+n,value:b}));return this.animations[0].createRange(i,o+n,l+n),!0}),h&&(h.prototype._animate=function(e){if(!this.animationsEnabled)return;const i=k.Now;if(!this._animationTimeLast){if(this._pendingData.length>0)return;this._animationTimeLast=i}this.deltaTime=e!==void 0?e:this.useConstantAnimationDeltaTime?16:(i-this._animationTimeLast)*this.animationTimeScale,this._animationTimeLast=i;const n=this._activeAnimatables;if(n.length===0)return;this._animationTime+=this.deltaTime;const s=this._animationTime;for(let a=0;a<n.length;a++){const r=n[a];!r._animate(s)&&r.disposeOnEnd&&a--}H(this)},h.prototype.sortActiveAnimatables=function(){this._activeAnimatables.sort((e,i)=>e.playOrder-i.playOrder)},h.prototype.beginWeightedAnimation=function(e,i,n,s=1,a,r=1,o,l,m,g,c=!1){const _=this.beginAnimation(e,i,n,a,r,o,l,!1,m,g,c);return _.weight=s,_},h.prototype.beginAnimation=function(e,i,n,s,a=1,r,o,l=!0,m,g,c=!1){if(a<0){const d=i;i=n,n=d,a=-a}i>n&&(a=-a),l&&this.stopAnimation(e,void 0,m),o||(o=new x(this,e,i,n,s,a,r,void 0,g,c));const _=m?m(e):!0;if(e.animations&&_&&o.appendAnimations(e,e.animations),e.getAnimatables){const d=e.getAnimatables();for(let u=0;u<d.length;u++)this.beginAnimation(d[u],i,n,s,a,r,o,l,m,g)}return o.reset(),o},h.prototype.beginHierarchyAnimation=function(e,i,n,s,a,r=1,o,l,m=!0,g,c,_=!1){const d=e.getDescendants(i),u=[];u.push(this.beginAnimation(e,n,s,a,r,o,l,m,g,void 0,_));for(const p of d)u.push(this.beginAnimation(p,n,s,a,r,o,l,m,g,void 0,_));return u},h.prototype.beginDirectAnimation=function(e,i,n,s,a,r=1,o,l,m=!1){if(r<0){const c=n;n=s,s=c,r=-r}return n>s&&(r=-r),new x(this,e,n,s,a,r,o,i,l,m)},h.prototype.beginDirectHierarchyAnimation=function(e,i,n,s,a,r,o,l,m,g=!1){const c=e.getDescendants(i),_=[];_.push(this.beginDirectAnimation(e,n,s,a,r,o,l,m,g));for(const d of c)_.push(this.beginDirectAnimation(d,n,s,a,r,o,l,m,g));return _},h.prototype.getAnimatableByTarget=function(e){for(let i=0;i<this._activeAnimatables.length;i++)if(this._activeAnimatables[i].target===e)return this._activeAnimatables[i];return null},h.prototype.getAllAnimatablesByTarget=function(e){const i=[];for(let n=0;n<this._activeAnimatables.length;n++)this._activeAnimatables[n].target===e&&i.push(this._activeAnimatables[n]);return i},h.prototype.stopAnimation=function(e,i,n){const s=this.getAllAnimatablesByTarget(e);for(const a of s)a.stop(i,n)},h.prototype.stopAllAnimations=function(){if(this._activeAnimatables){for(let e=0;e<this._activeAnimatables.length;e++)this._activeAnimatables[e].stop(void 0,void 0,!0);this._activeAnimatables.length=0}for(const e of this.animationGroups)e.stop()})}X(D,K);class J{getClassName(){return"TargetedAnimation"}serialize(){const t={};return t.animation=this.animation.serialize(),t.targetId=this.target.id,t}}class V{get mask(){return this._mask}set mask(t){this._mask!==t&&(this._mask=t,this.syncWithMask(!0))}syncWithMask(t=!1){if(!this.mask&&!t){this._numActiveAnimatables=this._targetedAnimations.length;return}this._numActiveAnimatables=0;for(let e=0;e<this._animatables.length;++e){const i=this._animatables[e];!this.mask||this.mask.disabled||this.mask.retainsTarget(i.target.name)?(this._numActiveAnimatables++,i.paused&&i.restart()):i.paused||i.pause()}}removeUnmaskedAnimations(){if(!(!this.mask||this.mask.disabled)){for(let t=0;t<this._animatables.length;++t){const e=this._animatables[t];this.mask.retainsTarget(e.target.name)||(e.stop(),this._animatables.splice(t,1),--t)}for(let t=0;t<this._targetedAnimations.length;t++){const e=this._targetedAnimations[t];this.mask.retainsTarget(e.target.name)||(this._targetedAnimations.splice(t,1),--t)}}}get from(){return this._from}set from(t){if(this._from!==t){this._from=t;for(let e=0;e<this._animatables.length;e++){const i=this._animatables[e];i.fromFrame=this._from}}}get to(){return this._to}set to(t){if(this._to!==t){this._to=t;for(let e=0;e<this._animatables.length;e++){const i=this._animatables[e];i.toFrame=this._to}}}get isStarted(){return this._isStarted}get isPlaying(){return this._isStarted&&!this._isPaused}get speedRatio(){return this._speedRatio}set speedRatio(t){if(this._speedRatio!==t){this._speedRatio=t;for(let e=0;e<this._animatables.length;e++){const i=this._animatables[e];i.speedRatio=this._speedRatio}}}get loopAnimation(){return this._loopAnimation}set loopAnimation(t){if(this._loopAnimation!==t){this._loopAnimation=t;for(let e=0;e<this._animatables.length;e++){const i=this._animatables[e];i.loopAnimation=this._loopAnimation}}}get isAdditive(){return this._isAdditive}set isAdditive(t){if(this._isAdditive!==t){this._isAdditive=t;for(let e=0;e<this._animatables.length;e++){const i=this._animatables[e];i.isAdditive=this._isAdditive}}}get weight(){return this._weight}set weight(t){this._weight!==t&&(this._weight=t,this.setWeightForAllAnimatables(this._weight))}get targetedAnimations(){return this._targetedAnimations}get animatables(){return this._animatables}get children(){return this._targetedAnimations}get playOrder(){return this._playOrder}set playOrder(t){if(this._playOrder!==t&&(this._playOrder=t,this._animatables.length>0)){for(let e=0;e<this._animatables.length;e++)this._animatables[e].playOrder=this._playOrder;this._scene.sortActiveAnimatables()}}get enableBlending(){return this._enableBlending}set enableBlending(t){if(this._enableBlending!==t&&(this._enableBlending=t,t!==null))for(let e=0;e<this._targetedAnimations.length;++e)this._targetedAnimations[e].animation.enableBlending=t}get blendingSpeed(){return this._blendingSpeed}set blendingSpeed(t){if(this._blendingSpeed!==t&&(this._blendingSpeed=t,t!==null))for(let e=0;e<this._targetedAnimations.length;++e)this._targetedAnimations[e].animation.blendingSpeed=t}getLength(t,e){t=t??this._from,e=e??this._to;const i=this.targetedAnimations[0].animation.framePerSecond*this._speedRatio;return(e-t)/i}static MergeAnimationGroups(t,e=!0,i=!1,n){if(t.length===0)return null;n=n??t[0].weight;let s=Number.MAX_VALUE,a=-Number.MAX_VALUE;if(i)for(const o of t)o.from<s&&(s=o.from),o.to>a&&(a=o.to);const r=new V(t[0].name+"_merged",t[0]._scene,n);for(const o of t){i&&o.normalize(s,a);for(const l of o.targetedAnimations)r.addTargetedAnimation(l.animation,l.target);e&&o.dispose()}return r}constructor(t,e=null,i=-1,n=0){this.name=t,this._targetedAnimations=new Array,this._animatables=new Array,this._from=Number.MAX_VALUE,this._to=-Number.MAX_VALUE,this._speedRatio=1,this._loopAnimation=!1,this._isAdditive=!1,this._weight=-1,this._playOrder=0,this._enableBlending=null,this._blendingSpeed=null,this._numActiveAnimatables=0,this._shouldStart=!0,this._parentContainer=null,this.onAnimationEndObservable=new P,this.onAnimationLoopObservable=new P,this.onAnimationGroupLoopObservable=new P,this.onAnimationGroupEndObservable=new P,this.onAnimationGroupPauseObservable=new P,this.onAnimationGroupPlayObservable=new P,this.metadata=null,this._mask=null,this._animationLoopFlags=[],this._scene=e||W.LastCreatedScene,this._weight=i,this._playOrder=n,this.uniqueId=this._scene.getUniqueId(),this._scene.addAnimationGroup(this)}addTargetedAnimation(t,e){const i=new J;i.animation=t,i.target=e;const n=t.getKeys();return this._from>n[0].frame&&(this._from=n[0].frame),this._to<n[n.length-1].frame&&(this._to=n[n.length-1].frame),this._enableBlending!==null&&(t.enableBlending=this._enableBlending),this._blendingSpeed!==null&&(t.blendingSpeed=this._blendingSpeed),this._targetedAnimations.push(i),this._shouldStart=!0,i}removeTargetedAnimation(t){for(let e=this._targetedAnimations.length-1;e>-1;e--)this._targetedAnimations[e].animation===t&&this._targetedAnimations.splice(e,1)}normalize(t=null,e=null){t==null&&(t=this._from),e==null&&(e=this._to);for(let i=0;i<this._targetedAnimations.length;i++){const s=this._targetedAnimations[i].animation.getKeys(),a=s[0],r=s[s.length-1];if(a.frame>t){const o={frame:t,value:a.value,inTangent:a.inTangent,outTangent:a.outTangent,interpolation:a.interpolation};s.splice(0,0,o)}if(r.frame<e){const o={frame:e,value:r.value,inTangent:r.inTangent,outTangent:r.outTangent,interpolation:r.interpolation};s.push(o)}}return this._from=t,this._to=e,this}_processLoop(t,e,i){t.onAnimationLoop=()=>{this.onAnimationLoopObservable.notifyObservers(e),!this._animationLoopFlags[i]&&(this._animationLoopFlags[i]=!0,this._animationLoopCount++,this._animationLoopCount===this._numActiveAnimatables&&(this.onAnimationGroupLoopObservable.notifyObservers(this),this._animationLoopCount=0,this._animationLoopFlags.length=0))}}start(t=!1,e=1,i,n,s){if(this._isStarted||this._targetedAnimations.length===0)return this;this._loopAnimation=t,this._shouldStart=!1,this._animationLoopCount=0,this._animationLoopFlags.length=0;for(let a=0;a<this._targetedAnimations.length;a++){const r=this._targetedAnimations[a],o=this._scene.beginDirectAnimation(r.target,[r.animation],i!==void 0?i:this._from,n!==void 0?n:this._to,t,e,void 0,void 0,s!==void 0?s:this._isAdditive);o.weight=this._weight,o.playOrder=this._playOrder,o.onAnimationEnd=()=>{this.onAnimationEndObservable.notifyObservers(r),this._checkAnimationGroupEnded(o)},this._processLoop(o,r,a),this._animatables.push(o)}return this.syncWithMask(),this._scene.sortActiveAnimatables(),this._speedRatio=e,this._isStarted=!0,this._isPaused=!1,this.onAnimationGroupPlayObservable.notifyObservers(this),this}pause(){if(!this._isStarted)return this;this._isPaused=!0;for(let t=0;t<this._animatables.length;t++)this._animatables[t].pause();return this.onAnimationGroupPauseObservable.notifyObservers(this),this}play(t){return this.isStarted&&this._animatables.length&&!this._shouldStart?(t!==void 0&&(this.loopAnimation=t),this.restart()):(this.stop(),this.start(t,this._speedRatio)),this}reset(){if(!this._isStarted)return this.play(),this.goToFrame(0),this.stop(!0),this;for(let t=0;t<this._animatables.length;t++)this._animatables[t].reset();return this}restart(){if(!this._isStarted)return this;for(let t=0;t<this._animatables.length;t++)this._animatables[t].restart();return this.syncWithMask(),this._isPaused=!1,this.onAnimationGroupPlayObservable.notifyObservers(this),this}stop(t=!1){if(!this._isStarted)return this;const e=this._animatables.slice();for(let n=0;n<e.length;n++)e[n].stop(void 0,void 0,!0,t);let i=0;for(let n=0;n<this._scene._activeAnimatables.length;n++){const s=this._scene._activeAnimatables[n];s._runtimeAnimations.length>0?this._scene._activeAnimatables[i++]=s:t&&this._checkAnimationGroupEnded(s,t)}return this._scene._activeAnimatables.length=i,this._isStarted=!1,this}setWeightForAllAnimatables(t){for(let e=0;e<this._animatables.length;e++){const i=this._animatables[e];i.weight=t}return this}syncAllAnimationsWith(t){for(let e=0;e<this._animatables.length;e++)this._animatables[e].syncWith(t);return this}goToFrame(t,e=!1){if(!this._isStarted)return this;for(let i=0;i<this._animatables.length;i++)this._animatables[i].goToFrame(t,e);return this}getCurrentFrame(){return this.animatables[0]?.masterFrame||0}dispose(){this.isStarted&&this.stop(),this._targetedAnimations.length=0,this._animatables.length=0;const t=this._scene.animationGroups.indexOf(this);if(t>-1&&this._scene.animationGroups.splice(t,1),this._parentContainer){const e=this._parentContainer.animationGroups.indexOf(this);e>-1&&this._parentContainer.animationGroups.splice(e,1),this._parentContainer=null}this.onAnimationEndObservable.clear(),this.onAnimationGroupEndObservable.clear(),this.onAnimationGroupPauseObservable.clear(),this.onAnimationGroupPlayObservable.clear(),this.onAnimationLoopObservable.clear(),this.onAnimationGroupLoopObservable.clear()}_checkAnimationGroupEnded(t,e=!1){const i=this._animatables.indexOf(t);i>-1&&this._animatables.splice(i,1),this._animatables.length===this._targetedAnimations.length-this._numActiveAnimatables&&(this._isStarted=!1,e||this.onAnimationGroupEndObservable.notifyObservers(this),this._animatables.length=0)}clone(t,e,i=!1){const n=new V(t||this.name,this._scene,this._weight,this._playOrder);n._from=this.from,n._to=this.to,n._speedRatio=this.speedRatio,n._loopAnimation=this.loopAnimation,n._isAdditive=this.isAdditive,n._enableBlending=this.enableBlending,n._blendingSpeed=this.blendingSpeed,n.metadata=this.metadata,n.mask=this.mask;for(const s of this._targetedAnimations)n.addTargetedAnimation(i?s.animation.clone():s.animation,e?e(s.target):s.target);return n}serialize(){const t={};t.name=this.name,t.from=this.from,t.to=this.to,t.speedRatio=this.speedRatio,t.loopAnimation=this.loopAnimation,t.isAdditive=this.isAdditive,t.weight=this.weight,t.playOrder=this.playOrder,t.enableBlending=this.enableBlending,t.blendingSpeed=this.blendingSpeed,t.targetedAnimations=[];for(let e=0;e<this.targetedAnimations.length;e++){const i=this.targetedAnimations[e];t.targetedAnimations[e]=i.serialize()}return w&&w.HasTags(this)&&(t.tags=w.GetTags(this)),this.metadata&&(t.metadata=this.metadata),t}static Parse(t,e){const i=new V(t.name,e,t.weight,t.playOrder);for(let n=0;n<t.targetedAnimations.length;n++){const s=t.targetedAnimations[n],a=A.Parse(s.animation),r=s.targetId;if(s.animation.property==="influence"){const o=e.getMorphTargetById(r);o&&i.addTargetedAnimation(a,o)}else{const o=e.getNodeById(r);o!=null&&i.addTargetedAnimation(a,o)}}return w&&w.AddTagsTo(i,t.tags),t.from!==null&&t.to!==null&&i.normalize(t.from,t.to),t.speedRatio!==void 0&&(i._speedRatio=t.speedRatio),t.loopAnimation!==void 0&&(i._loopAnimation=t.loopAnimation),t.isAdditive!==void 0&&(i._isAdditive=t.isAdditive),t.weight!==void 0&&(i._weight=t.weight),t.playOrder!==void 0&&(i._playOrder=t.playOrder),t.enableBlending!==void 0&&(i._enableBlending=t.enableBlending),t.blendingSpeed!==void 0&&(i._blendingSpeed=t.blendingSpeed),t.metadata!==void 0&&(i.metadata=t.metadata),i}static MakeAnimationAdditive(t,e,i,n=!1,s){let a;typeof e=="object"?a=e:a={referenceFrame:e,range:i,cloneOriginalAnimationGroup:n,clonedAnimationName:s};let r=t;a.cloneOriginalAnimationGroup&&(r=t.clone(a.clonedAnimationGroupName||r.name));const o=r.targetedAnimations;for(let l=0;l<o.length;l++){const m=o[l];m.animation=A.MakeAnimationAdditive(m.animation,a)}if(r.isAdditive=!0,a.clipKeys){let l=Number.MAX_VALUE,m=-Number.MAX_VALUE;const g=r.targetedAnimations;for(let c=0;c<g.length;c++){const u=g[c].animation.getKeys();l>u[0].frame&&(l=u[0].frame),m<u[u.length-1].frame&&(m=u[u.length-1].frame)}r._from=l,r._to=m}return r}static ClipKeys(t,e,i,n,s){const a=t.clone(n||t.name);return V.ClipKeysInPlace(a,e,i,s)}static ClipKeysInPlace(t,e,i,n){return V.ClipInPlace(t,e,i,n,!1)}static ClipFrames(t,e,i,n,s){const a=t.clone(n||t.name);return V.ClipFramesInPlace(a,e,i,s)}static ClipFramesInPlace(t,e,i,n){return V.ClipInPlace(t,e,i,n,!0)}static ClipInPlace(t,e,i,n,s=!1){let a=Number.MAX_VALUE,r=-Number.MAX_VALUE;const o=t.targetedAnimations;for(let l=0;l<o.length;l++){const m=o[l],g=n?m.animation:m.animation.clone();s&&(g.createKeyForFrame(e),g.createKeyForFrame(i));const c=g.getKeys(),_=[];let d=Number.MAX_VALUE;for(let u=0;u<c.length;u++){const p=c[u];if(!s&&u>=e&&u<=i||s&&p.frame>=e&&p.frame<=i){const y={frame:p.frame,value:p.value.clone?p.value.clone():p.value,inTangent:p.inTangent,outTangent:p.outTangent,interpolation:p.interpolation,lockedTangent:p.lockedTangent};d===Number.MAX_VALUE&&(d=y.frame),y.frame-=d,_.push(y)}}if(_.length===0){o.splice(l,1),l--;continue}a>_[0].frame&&(a=_[0].frame),r<_[_.length-1].frame&&(r=_[_.length-1].frame),g.setKeys(_,!0),m.animation=g}return t._from=a,t._to=r,t}getClassName(){return"AnimationGroup"}toString(t){let e="Name: "+this.name;return e+=", type: "+this.getClassName(),t&&(e+=", from: "+this._from,e+=", to: "+this._to,e+=", isStarted: "+this._isStarted,e+=", speedRatio: "+this._speedRatio,e+=", targetedAnimations length: "+this._targetedAnimations.length,e+=", animatables length: "+this._animatables),e}}export{V as AnimationGroup,J as TargetedAnimation};
//# sourceMappingURL=animationGroup-Bwms8CyJ.js.map
