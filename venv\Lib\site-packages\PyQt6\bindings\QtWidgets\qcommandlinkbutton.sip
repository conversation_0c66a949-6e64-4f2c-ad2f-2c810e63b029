// qcommandlinkbutton.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCommandLinkButton : public QPushButton
{
%TypeHeaderCode
#include <qcommandlinkbutton.h>
%End

public:
    explicit QCommandLinkButton(QWidget *parent /TransferThis/ = 0);
    QCommandLinkButton(const QString &text, QWidget *parent /TransferThis/ = 0);
    QCommandLinkButton(const QString &text, const QString &description, QWidget *parent /TransferThis/ = 0);
    virtual ~QCommandLinkButton();
    QString description() const;
    void setDescription(const QString &description);
    virtual QSize sizeHint() const;
    virtual int heightForWidth(int) const;
    virtual QSize minimumSizeHint() const;
%If (Qt_6_1_0 -)
    virtual void initStyleOption(QStyleOptionButton *option) const;
%End

protected:
    virtual bool event(QEvent *e);
    virtual void paintEvent(QPaintEvent *);
};
