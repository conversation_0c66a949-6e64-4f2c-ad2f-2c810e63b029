#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_reshape_from_tensor_ops.h>

namespace at {


// aten::_reshape_from_tensor(Tensor self, Tensor shape) -> Tensor
inline at::Tensor _reshape_from_tensor(const at::Tensor & self, const at::Tensor & shape) {
    return at::_ops::_reshape_from_tensor::call(self, shape);
}

}
