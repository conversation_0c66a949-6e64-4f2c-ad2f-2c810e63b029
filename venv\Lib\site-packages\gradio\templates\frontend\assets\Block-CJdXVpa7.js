/* empty css                                                        */const{SvelteComponent:ne,append:j,assign:se,attr:g,binding_callbacks:fe,create_slot:he,detach:k,element:X,empty:re,flush:_,get_all_dirty_from_scope:de,get_slot_changes:oe,get_spread_update:_e,init:ue,insert:y,listen:ae,noop:me,safe_not_equal:ge,set_dynamic_element_data:Q,set_style:h,space:Z,svg_element:M,toggle_class:a,transition_in:x,transition_out:p,update_slot_base:we}=window.__gradio__svelte__internal;function T(t){let e,n,r,u,s;return{c(){e=M("svg"),n=M("line"),r=M("line"),g(n,"x1","1"),g(n,"y1","9"),g(n,"x2","9"),g(n,"y2","1"),g(n,"stroke","gray"),g(n,"stroke-width","0.5"),g(r,"x1","5"),g(r,"y1","9"),g(r,"x2","9"),g(r,"y2","5"),g(r,"stroke","gray"),g(r,"stroke-width","0.5"),g(e,"class","resize-handle svelte-1svsvh2"),g(e,"xmlns","http://www.w3.org/2000/svg"),g(e,"viewBox","0 0 10 10")},m(f,d){y(f,e,d),j(e,n),j(e,r),u||(s=ae(e,"mousedown",t[27]),u=!0)},p:me,d(f){f&&k(e),u=!1,s()}}}function ve(t){let e,n,r,u,s;const f=t[31].default,d=he(f,t,t[30],null);let m=t[19]&&T(t),c=[{"data-testid":t[11]},{id:t[6]},{class:r="block "+(t[7]?.join(" ")||"")+" svelte-1svsvh2"},{dir:u=t[20]?"rtl":"ltr"}],b={};for(let i=0;i<c.length;i+=1)b=se(b,c[i]);return{c(){e=X(t[25]),d&&d.c(),n=Z(),m&&m.c(),Q(t[25])(e,b),a(e,"hidden",t[14]===!1),a(e,"padded",t[10]),a(e,"flex",t[1]),a(e,"border_focus",t[9]==="focus"),a(e,"border_contrast",t[9]==="contrast"),a(e,"hide-container",!t[12]&&!t[13]),a(e,"fullscreen",t[0]),a(e,"animating",t[0]&&t[24]!==null),a(e,"auto-margin",t[17]===null),h(e,"height",t[0]?void 0:t[26](t[2])),h(e,"min-height",t[0]?void 0:t[26](t[3])),h(e,"max-height",t[0]?void 0:t[26](t[4])),h(e,"--start-top",t[24]?`${t[24].top}px`:"0px"),h(e,"--start-left",t[24]?`${t[24].left}px`:"0px"),h(e,"--start-width",t[24]?`${t[24].width}px`:"0px"),h(e,"--start-height",t[24]?`${t[24].height}px`:"0px"),h(e,"width",t[0]?void 0:typeof t[5]=="number"?`calc(min(${t[5]}px, 100%))`:t[26](t[5])),h(e,"border-style",t[8]),h(e,"overflow",t[15]?t[16]:"hidden"),h(e,"flex-grow",t[17]),h(e,"min-width",`calc(min(${t[18]}px, 100%))`),h(e,"border-width","var(--block-border-width)")},m(i,o){y(i,e,o),d&&d.m(e,null),j(e,n),m&&m.m(e,null),t[32](e),s=!0},p(i,o){d&&d.p&&(!s||o[0]&1073741824)&&we(d,f,i,i[30],s?oe(f,i[30],o,null):de(i[30]),null),i[19]?m?m.p(i,o):(m=T(i),m.c(),m.m(e,null)):m&&(m.d(1),m=null),Q(i[25])(e,b=_e(c,[(!s||o[0]&2048)&&{"data-testid":i[11]},(!s||o[0]&64)&&{id:i[6]},(!s||o[0]&128&&r!==(r="block "+(i[7]?.join(" ")||"")+" svelte-1svsvh2"))&&{class:r},(!s||o[0]&1048576&&u!==(u=i[20]?"rtl":"ltr"))&&{dir:u}])),a(e,"hidden",i[14]===!1),a(e,"padded",i[10]),a(e,"flex",i[1]),a(e,"border_focus",i[9]==="focus"),a(e,"border_contrast",i[9]==="contrast"),a(e,"hide-container",!i[12]&&!i[13]),a(e,"fullscreen",i[0]),a(e,"animating",i[0]&&i[24]!==null),a(e,"auto-margin",i[17]===null),o[0]&5&&h(e,"height",i[0]?void 0:i[26](i[2])),o[0]&9&&h(e,"min-height",i[0]?void 0:i[26](i[3])),o[0]&17&&h(e,"max-height",i[0]?void 0:i[26](i[4])),o[0]&16777216&&h(e,"--start-top",i[24]?`${i[24].top}px`:"0px"),o[0]&16777216&&h(e,"--start-left",i[24]?`${i[24].left}px`:"0px"),o[0]&16777216&&h(e,"--start-width",i[24]?`${i[24].width}px`:"0px"),o[0]&16777216&&h(e,"--start-height",i[24]?`${i[24].height}px`:"0px"),o[0]&33&&h(e,"width",i[0]?void 0:typeof i[5]=="number"?`calc(min(${i[5]}px, 100%))`:i[26](i[5])),o[0]&256&&h(e,"border-style",i[8]),o[0]&98304&&h(e,"overflow",i[15]?i[16]:"hidden"),o[0]&131072&&h(e,"flex-grow",i[17]),o[0]&262144&&h(e,"min-width",`calc(min(${i[18]}px, 100%))`)},i(i){s||(x(d,i),s=!0)},o(i){p(d,i),s=!1},d(i){i&&k(e),d&&d.d(i),m&&m.d(),t[32](null)}}}function V(t){let e;return{c(){e=X("div"),g(e,"class","placeholder svelte-1svsvh2"),h(e,"height",t[22]+"px"),h(e,"width",t[23]+"px")},m(n,r){y(n,e,r)},p(n,r){r[0]&4194304&&h(e,"height",n[22]+"px"),r[0]&8388608&&h(e,"width",n[23]+"px")},d(n){n&&k(e)}}}function be(t){let e,n,r,u=t[25]&&ve(t),s=t[0]&&V(t);return{c(){u&&u.c(),e=Z(),s&&s.c(),n=re()},m(f,d){u&&u.m(f,d),y(f,e,d),s&&s.m(f,d),y(f,n,d),r=!0},p(f,d){f[25]&&u.p(f,d),f[0]?s?s.p(f,d):(s=V(f),s.c(),s.m(n.parentNode,n)):s&&(s.d(1),s=null)},i(f){r||(x(u,f),r=!0)},o(f){p(u,f),r=!1},d(f){f&&(k(e),k(n)),u&&u.d(f),s&&s.d(f)}}}function ce(t,e,n){let{$$slots:r={},$$scope:u}=e,{height:s=void 0}=e,{min_height:f=void 0}=e,{max_height:d=void 0}=e,{width:m=void 0}=e,{elem_id:c=""}=e,{elem_classes:b=[]}=e,{variant:i="solid"}=e,{border_mode:o="base"}=e,{padding:C=!0}=e,{type:z="normal"}=e,{test_id:H=void 0}=e,{explicit_call:R=!1}=e,{container:q=!0}=e,{visible:E=!0}=e,{allow_overflow:K=!0}=e,{overflow_behavior:N="auto"}=e,{scale:S=null}=e,{min_width:U=0}=e,{flex:L=!1}=e,{resizable:W=!1}=e,{rtl:A=!1}=e,{fullscreen:v=!1}=e,B=v,w,$=z==="fieldset"?"fieldset":"div",D=0,F=0,Y=null;function G(l){v&&l.key==="Escape"&&n(0,v=!1)}const ee=l=>{if(l!==void 0){if(typeof l=="number")return l+"px";if(typeof l=="string")return l}},te=l=>{let I=l.clientY;const J=P=>{const le=P.clientY-I;I=P.clientY,n(21,w.style.height=`${w.offsetHeight+le}px`,w)},O=()=>{window.removeEventListener("mousemove",J),window.removeEventListener("mouseup",O)};window.addEventListener("mousemove",J),window.addEventListener("mouseup",O)};function ie(l){fe[l?"unshift":"push"](()=>{w=l,n(21,w)})}return t.$$set=l=>{"height"in l&&n(2,s=l.height),"min_height"in l&&n(3,f=l.min_height),"max_height"in l&&n(4,d=l.max_height),"width"in l&&n(5,m=l.width),"elem_id"in l&&n(6,c=l.elem_id),"elem_classes"in l&&n(7,b=l.elem_classes),"variant"in l&&n(8,i=l.variant),"border_mode"in l&&n(9,o=l.border_mode),"padding"in l&&n(10,C=l.padding),"type"in l&&n(28,z=l.type),"test_id"in l&&n(11,H=l.test_id),"explicit_call"in l&&n(12,R=l.explicit_call),"container"in l&&n(13,q=l.container),"visible"in l&&n(14,E=l.visible),"allow_overflow"in l&&n(15,K=l.allow_overflow),"overflow_behavior"in l&&n(16,N=l.overflow_behavior),"scale"in l&&n(17,S=l.scale),"min_width"in l&&n(18,U=l.min_width),"flex"in l&&n(1,L=l.flex),"resizable"in l&&n(19,W=l.resizable),"rtl"in l&&n(20,A=l.rtl),"fullscreen"in l&&n(0,v=l.fullscreen),"$$scope"in l&&n(30,u=l.$$scope)},t.$$.update=()=>{t.$$.dirty[0]&538968065&&v!==B&&(n(29,B=v),v?(n(24,Y=w.getBoundingClientRect()),n(22,D=w.offsetHeight),n(23,F=w.offsetWidth),window.addEventListener("keydown",G)):(n(24,Y=null),window.removeEventListener("keydown",G))),t.$$.dirty[0]&16384&&(E||n(1,L=!1))},[v,L,s,f,d,m,c,b,i,o,C,H,R,q,E,K,N,S,U,W,A,w,D,F,Y,$,ee,te,z,B,u,r,ie]}class ye extends ne{constructor(e){super(),ue(this,e,ce,be,ge,{height:2,min_height:3,max_height:4,width:5,elem_id:6,elem_classes:7,variant:8,border_mode:9,padding:10,type:28,test_id:11,explicit_call:12,container:13,visible:14,allow_overflow:15,overflow_behavior:16,scale:17,min_width:18,flex:1,resizable:19,rtl:20,fullscreen:0},null,[-1,-1])}get height(){return this.$$.ctx[2]}set height(e){this.$$set({height:e}),_()}get min_height(){return this.$$.ctx[3]}set min_height(e){this.$$set({min_height:e}),_()}get max_height(){return this.$$.ctx[4]}set max_height(e){this.$$set({max_height:e}),_()}get width(){return this.$$.ctx[5]}set width(e){this.$$set({width:e}),_()}get elem_id(){return this.$$.ctx[6]}set elem_id(e){this.$$set({elem_id:e}),_()}get elem_classes(){return this.$$.ctx[7]}set elem_classes(e){this.$$set({elem_classes:e}),_()}get variant(){return this.$$.ctx[8]}set variant(e){this.$$set({variant:e}),_()}get border_mode(){return this.$$.ctx[9]}set border_mode(e){this.$$set({border_mode:e}),_()}get padding(){return this.$$.ctx[10]}set padding(e){this.$$set({padding:e}),_()}get type(){return this.$$.ctx[28]}set type(e){this.$$set({type:e}),_()}get test_id(){return this.$$.ctx[11]}set test_id(e){this.$$set({test_id:e}),_()}get explicit_call(){return this.$$.ctx[12]}set explicit_call(e){this.$$set({explicit_call:e}),_()}get container(){return this.$$.ctx[13]}set container(e){this.$$set({container:e}),_()}get visible(){return this.$$.ctx[14]}set visible(e){this.$$set({visible:e}),_()}get allow_overflow(){return this.$$.ctx[15]}set allow_overflow(e){this.$$set({allow_overflow:e}),_()}get overflow_behavior(){return this.$$.ctx[16]}set overflow_behavior(e){this.$$set({overflow_behavior:e}),_()}get scale(){return this.$$.ctx[17]}set scale(e){this.$$set({scale:e}),_()}get min_width(){return this.$$.ctx[18]}set min_width(e){this.$$set({min_width:e}),_()}get flex(){return this.$$.ctx[1]}set flex(e){this.$$set({flex:e}),_()}get resizable(){return this.$$.ctx[19]}set resizable(e){this.$$set({resizable:e}),_()}get rtl(){return this.$$.ctx[20]}set rtl(e){this.$$set({rtl:e}),_()}get fullscreen(){return this.$$.ctx[0]}set fullscreen(e){this.$$set({fullscreen:e}),_()}}export{ye as B};
//# sourceMappingURL=Block-CJdXVpa7.js.map
