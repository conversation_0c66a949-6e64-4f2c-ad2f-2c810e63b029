import{SvelteComponent as d,init as u,safe_not_equal as o,element as h,text as m,claim_element as _,children as v,claim_text as y,detach as c,attr as g,toggle_class as s,insert_hydration as b,append_hydration as q,set_data as E,noop as r}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function k(a){let e,i;return{c(){e=h("div"),i=m(a[0]),this.h()},l(t){e=_(t,"DIV",{class:!0});var l=v(e);i=y(l,a[0]),l.forEach(c),this.h()},h(){g(e,"class","svelte-1ayixqk"),s(e,"table",a[1]==="table"),s(e,"gallery",a[1]==="gallery"),s(e,"selected",a[2])},m(t,l){b(t,e,l),q(e,i)},p(t,[l]){l&1&&E(i,t[0]),l&2&&s(e,"table",t[1]==="table"),l&2&&s(e,"gallery",t[1]==="gallery"),l&4&&s(e,"selected",t[2])},i:r,o:r,d(t){t&&c(e)}}}function C(a,e,i){let{value:t}=e,{type:l}=e,{selected:f=!1}=e;return a.$$set=n=>{"value"in n&&i(0,t=n.value),"type"in n&&i(1,l=n.type),"selected"in n&&i(2,f=n.selected)},[t,l,f]}class S extends d{constructor(e){super(),u(this,e,C,k,o,{value:0,type:1,selected:2})}}export{S as default};
//# sourceMappingURL=Example.cOvhHY2p.js.map
