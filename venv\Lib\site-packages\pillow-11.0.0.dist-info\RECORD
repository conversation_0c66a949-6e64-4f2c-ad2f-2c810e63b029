PIL/BdfFontFile.py,sha256=JJLBb0JZwTmSIIkqQoe2vzus-XTczN_O47DQneXKM1o,3610
PIL/BlpImagePlugin.py,sha256=nPiWxqE_ZJJPbaYQiFigv1qBjF8v0lxMSKm_pJY3W3w,17077
PIL/BmpImagePlugin.py,sha256=kFfnW8Bg8Ijs4j4AGn27SyKTFn0wqHwN7O1JYjH-b44,20269
PIL/BufrStubImagePlugin.py,sha256=sY28XJU_Fu-UsbPpAoN-fN63FemmhCMi8rW5Kf9JioE,1829
PIL/ContainerIO.py,sha256=I6yO_YFEEqMKA1ckgEEzF2r_Ik5p_GjM-RrWOJYjSlY,4777
PIL/CurImagePlugin.py,sha256=l6aPDjo9n7-pfwGpbuYJKFaSYrpwOVnFkIZDT5tRDn8,1867
PIL/DcxImagePlugin.py,sha256=iaVs9updbtEstQKPLKKIlJVhfxFarbgCPoO8j96BmDA,2114
PIL/DdsImagePlugin.py,sha256=Y_itB3X54n7N2rL34dyDBpol2X4_RnzjtiXtg99Q0iA,17535
PIL/EpsImagePlugin.py,sha256=cKDk4eDeh8YVQ8L-AVcpTbm8F837TOxIG7MBTU3Ngy0,16848
PIL/ExifTags.py,sha256=LA3OxhNImajSkIRnCMXDTJw4MprMEeFq_Sqf-sjn20w,10134
PIL/FitsImagePlugin.py,sha256=4NPMt0uRxtyTpk2CyH7STSC-7ZmoGlSEl43hfnu5vBk,4791
PIL/FliImagePlugin.py,sha256=FgqPZZkbTGBMcxtOXAp556WxknEz3dBPaUoerCT7BCU,4856
PIL/FontFile.py,sha256=iLSV32yQetLnE4SgG8HnHb2FdqkqFBjY9n--E6u5UE0,3711
PIL/FpxImagePlugin.py,sha256=_jQMgLzfC8uxGka-KPIJ2jwD6luMFp-4Qz7RtsrruGM,7548
PIL/FtexImagePlugin.py,sha256=1I5_0O__NbbJRneo3YVyUcUxV_wLYHQu8SkOPp6CMxY,3650
PIL/GbrImagePlugin.py,sha256=x49ki3fwwQQre9Gi_Q4xb50ui4o-3TyE9S0mMqHTBR0,3109
PIL/GdImageFile.py,sha256=mTQCaQCp0s-awYAGsw-LQQyyW6zU-FS4KhUlfg1on74,2912
PIL/GifImagePlugin.py,sha256=FOyVsNiRug79eHdoBeHySZxcd_ctnn4mQQSPWkxnntI,42612
PIL/GimpGradientFile.py,sha256=AFEEGWtylUg7tIYK0MgBC4hZHq3WxSzIvdq_MAEAUq8,4047
PIL/GimpPaletteFile.py,sha256=EmKLnuvsHx0GLhWh8YnfidiTEhUm51-ZNKgQbAX1zcU,1485
PIL/GribStubImagePlugin.py,sha256=Vf_VvZltyP3QqTuz-gUfCT6I2g3F5Rh8BYMGjxwpAoM,1823
PIL/Hdf5StubImagePlugin.py,sha256=70vlB50QgPYYH2b8kE8U_QN5Q8TlmjmN8vk0FrhLkJ4,1826
PIL/IcnsImagePlugin.py,sha256=6ZH5I24DyNxev13dpqhxhrvsLYwpJgIOKcDyldqhNnQ,13365
PIL/IcoImagePlugin.py,sha256=n8-QRW5Yke9SdR1Lc50ePCoR92V--jYDZ4Us7mSAmF4,12849
PIL/ImImagePlugin.py,sha256=T9nd5jRiCoiNs80ahKA-jrQx6xXbeG8dowHOvk9N9QM,11815
PIL/Image.py,sha256=uneMCNf0W2DRYpNn-BbYnCQNNeCvk4jSjNac2WOY-xY,150163
PIL/ImageChops.py,sha256=hZ8EPUPlQIzugsEedV8trkKX0jBCDGb6Cszma6ZeMZQ,8257
PIL/ImageCms.py,sha256=l3_-tm-1WmrJftb0gPfohoFheRzmAflJh3o_6dOue_8,43135
PIL/ImageColor.py,sha256=KV-u7HnZWrrL3zuBAOLqerI-7vFcXTxdLeoaYVjsnwI,9761
PIL/ImageDraw.py,sha256=DYB7qTpWv3mnkb8QqU1m43ySUVwC0ID_X-1CBAcvbxc,43493
PIL/ImageDraw2.py,sha256=_e6I2nPxeiZwjOjBEJzUvxoyAklzmc-xF6R8z8ieOAA,7470
PIL/ImageEnhance.py,sha256=ugDU0sljaR_zaTibtumlTvf-qFH1x1W6l2QMpc386XU,3740
PIL/ImageFile.py,sha256=iiU9m9PrCREzA9QWhqoqE_bx-_8LKo5NFd-X2W3d7g8,26977
PIL/ImageFilter.py,sha256=EgZAzVpL62p5bS65vp11cI8dr3YhmXwmRTzmYh2Ntbg,19302
PIL/ImageFont.py,sha256=8hV48--3wsJMJX3esojS3K46cLPD7_QkltkhUI1f6GM,65617
PIL/ImageGrab.py,sha256=CJP_aZNA1mXU5dI77eElm4_Au198Uf7yVZ7Xw0BJ53s,6552
PIL/ImageMath.py,sha256=oHveLI5M0XwUJgX6uBPqGhQcdgoosC2-kYaVHbiDDts,12310
PIL/ImageMode.py,sha256=n4-2kSolyB7v2u6dXyIf3_vDL_LMvSNwhJvd9Do8cc8,2773
PIL/ImageMorph.py,sha256=E6kZhhpRypnHU8LoFgG4HkUoW3LfTr6rbv-fuFS9fDQ,8828
PIL/ImageOps.py,sha256=Rd3P9VMOFgKY5956gZtcWZv3uCB5oHAcssanf0pauNI,25805
PIL/ImagePalette.py,sha256=3MgwOab-209To6wP-7w04dCs1IQz84Y3X3SbvU4_muI,9287
PIL/ImagePath.py,sha256=ZnnJuvQNtbKRhCmr61TEmEh1vVV5_90WMEPL8Opy5l8,391
PIL/ImageQt.py,sha256=wjqQ_sZbUHzgGtCue4289rZYpGpg2ygrBE_Sv2orWck,6980
PIL/ImageSequence.py,sha256=5UohDzcf-2PA3NfGnMRd15zDDA3eD9Wo3SP3DxyRsCU,2286
PIL/ImageShow.py,sha256=19xEF7Gya2e-ZlrZKIekl2VBKZycuHG93ALOvOJ6qSk,10353
PIL/ImageStat.py,sha256=iA5KJrQeEpbwk-FhczGD8L4TaLWUt4VT_mp4drvMhv8,5485
PIL/ImageTk.py,sha256=mIiBdLdg3G7Y0r9zPsf5gC-QYL_7VJXGeai8LjxOFuI,9287
PIL/ImageTransform.py,sha256=cFaMTjlWklRKDEO9zxyXwfLuf9quaCSWJ79KyjxYwKY,4022
PIL/ImageWin.py,sha256=b-fO6kn4icHpy-zk-Xg-nO382zPXl-MKjZcs3vAXl1Q,8332
PIL/ImtImagePlugin.py,sha256=l8-O69RNt8CdN76UFwS1RDIFnbj7mGBmhz7HechGWJE,2776
PIL/IptcImagePlugin.py,sha256=4NKTYmrGbP90uVdCqQJzvodncRjQVUpemLiHbYsYdfk,6918
PIL/Jpeg2KImagePlugin.py,sha256=P_ztbNPsyqZIpvMNF0aaNKO5CwaUoqayHaiClwq91po,14259
PIL/JpegImagePlugin.py,sha256=dUIlg9V9rjA-5eL3fjAtOv4B86pWrrxanWNsp4CivO0,32328
PIL/JpegPresets.py,sha256=UUIsKzvyzdPzsndODd90eu_luMqauG1PJh10UOuQvmg,12621
PIL/McIdasImagePlugin.py,sha256=51zeymhkCr7Tz7b8UhxAACww5MkCCOV4X1pM-QXp8IU,2018
PIL/MicImagePlugin.py,sha256=PrA2tqLn2NLRN-llQdBOPSYNHV-FFIpxgKHA1UUNkNw,2787
PIL/MpegImagePlugin.py,sha256=SR-JGne4xNIrHTc1vKdEbuW61oI-TIar2oIP-MeRuiI,2188
PIL/MpoImagePlugin.py,sha256=oAvDIZC_KpOxOYPP5rjsYWDKfNqi3asnwIosyx-7AR8,6410
PIL/MspImagePlugin.py,sha256=NkR6_Vrn306k2drOJSKXLJ1lgvTsGj47uxObZxznAHo,6104
PIL/PSDraw.py,sha256=un7FSu3yFIDTVtO9tB7w_csZcbNYQcHFJJSOILxL5gE,7143
PIL/PaletteFile.py,sha256=lNPfuBTHraW6i1v1b9thNoFyIG2MRMMzFHxVTaxcwj8,1265
PIL/PalmImagePlugin.py,sha256=c0d23TPlT_6_iCj6YGB8xH2Ta0J__xufcHvZeTPigvw,9583
PIL/PcdImagePlugin.py,sha256=EDQGCPGebu4EpXWJc_mUIXJn_gz4YmT3BhR5r-MLHRE,1662
PIL/PcfFontFile.py,sha256=RkM5wUp3SgRpQhpsTBEtk8uuFrQPnBSYBryOmcoRphQ,7401
PIL/PcxImagePlugin.py,sha256=deEbKrJSHrQBHW2NPWnGEnxrTPrhrnDVWVYdellihok,6478
PIL/PdfImagePlugin.py,sha256=-01K9TRr_ekIRxokuBw3N-_t5gQmn23rJXj6-B2-Gpk,9660
PIL/PdfParser.py,sha256=VDCyd2NUI2dlAXHf4q5vxFIX1Icllkeiaqlsnx077jc,39053
PIL/PixarImagePlugin.py,sha256=m3Zfy0GJyrhiy94Ti591CgQPKz8OoQQMRnD3navEePw,1857
PIL/PngImagePlugin.py,sha256=3InrD2VdkQA5woeAhirV3ecYVqlnFy4PMw6RQKNnOuA,52409
PIL/PpmImagePlugin.py,sha256=xUbGR7DUieRonG7rVs8IJ0TOVUqOP6oX-rbdxB4-iTs,12729
PIL/PsdImagePlugin.py,sha256=eoOJ8GDNDzz96BBRbWEFwYRwyAHkrMrWEhV3ki84iuU,8953
PIL/QoiImagePlugin.py,sha256=Dl5pbxhNOra7_WPwpJ8S2W8plQYDgcMrbWGSpjjIeTI,4304
PIL/SgiImagePlugin.py,sha256=Guops-mEPgeP56JwqXII-kt9ZxuMYng207dkrA56N7Q,6979
PIL/SpiderImagePlugin.py,sha256=sW17K_APLOVWf_nIBuoFPZm0guR0Z9v_06M0K7ndBMg,10442
PIL/SunImagePlugin.py,sha256=YKYEvuG4QUkies34OKtXWKTYSZ8U3qzcE_vdTrOuRsw,4734
PIL/TarIO.py,sha256=pR4LqBuF2rBy8v2PYsXZHqh6QalDeoraPSBiC57t7NU,1433
PIL/TgaImagePlugin.py,sha256=OMvZn_xKjB1dZ1_4MkOquzJBHpSUIpAf5mUEJZiLBTI,7244
PIL/TiffImagePlugin.py,sha256=wGl5lXTasoP9HVXAdRadqFyImsDAgssQuXiHdP-kmW8,84422
PIL/TiffTags.py,sha256=CmDDo0yRJ4lD-tvB00RWyNlDbSjhQx8QhDzJOr1zoZI,17644
PIL/WalImageFile.py,sha256=XzvTP_kO_JuumDBXV4FTRJJG1xhx4KqMnXDkStpaYbk,5831
PIL/WebPImagePlugin.py,sha256=fkEHsDklgUQYKiFvEV9LjFXnBZO5DmnoEfnek4tWLA0,10407
PIL/WmfImagePlugin.py,sha256=ehpDBscTFVDIDjBAucagJy2Sl5TD1KzvvG9YT2HZmHk,5211
PIL/XVThumbImagePlugin.py,sha256=IjmJxrkwUDU1EMzGeiARqMF3OaWMSyvaCpcU-WdBkNs,2233
PIL/XbmImagePlugin.py,sha256=ZCmHh9Q6ytvsUE0mKAQgigNU5ReZnUEusIKoqeBl3Fs,2777
PIL/XpmImagePlugin.py,sha256=BliLDJKy-U0EHFRuzYtQWoodmH40IMvejTnLQfqUdFM,3383
PIL/__init__.py,sha256=98abxVfn8od1jJaTIr65YrYrIb7zMKbOJ5o68ryE2O0,2094
PIL/__main__.py,sha256=X8eIpGlmHfnp7zazp5mdav228Itcf2lkiMP0tLU6X9c,140
PIL/__pycache__/BdfFontFile.cpython-310.pyc,,
PIL/__pycache__/BlpImagePlugin.cpython-310.pyc,,
PIL/__pycache__/BmpImagePlugin.cpython-310.pyc,,
PIL/__pycache__/BufrStubImagePlugin.cpython-310.pyc,,
PIL/__pycache__/ContainerIO.cpython-310.pyc,,
PIL/__pycache__/CurImagePlugin.cpython-310.pyc,,
PIL/__pycache__/DcxImagePlugin.cpython-310.pyc,,
PIL/__pycache__/DdsImagePlugin.cpython-310.pyc,,
PIL/__pycache__/EpsImagePlugin.cpython-310.pyc,,
PIL/__pycache__/ExifTags.cpython-310.pyc,,
PIL/__pycache__/FitsImagePlugin.cpython-310.pyc,,
PIL/__pycache__/FliImagePlugin.cpython-310.pyc,,
PIL/__pycache__/FontFile.cpython-310.pyc,,
PIL/__pycache__/FpxImagePlugin.cpython-310.pyc,,
PIL/__pycache__/FtexImagePlugin.cpython-310.pyc,,
PIL/__pycache__/GbrImagePlugin.cpython-310.pyc,,
PIL/__pycache__/GdImageFile.cpython-310.pyc,,
PIL/__pycache__/GifImagePlugin.cpython-310.pyc,,
PIL/__pycache__/GimpGradientFile.cpython-310.pyc,,
PIL/__pycache__/GimpPaletteFile.cpython-310.pyc,,
PIL/__pycache__/GribStubImagePlugin.cpython-310.pyc,,
PIL/__pycache__/Hdf5StubImagePlugin.cpython-310.pyc,,
PIL/__pycache__/IcnsImagePlugin.cpython-310.pyc,,
PIL/__pycache__/IcoImagePlugin.cpython-310.pyc,,
PIL/__pycache__/ImImagePlugin.cpython-310.pyc,,
PIL/__pycache__/Image.cpython-310.pyc,,
PIL/__pycache__/ImageChops.cpython-310.pyc,,
PIL/__pycache__/ImageCms.cpython-310.pyc,,
PIL/__pycache__/ImageColor.cpython-310.pyc,,
PIL/__pycache__/ImageDraw.cpython-310.pyc,,
PIL/__pycache__/ImageDraw2.cpython-310.pyc,,
PIL/__pycache__/ImageEnhance.cpython-310.pyc,,
PIL/__pycache__/ImageFile.cpython-310.pyc,,
PIL/__pycache__/ImageFilter.cpython-310.pyc,,
PIL/__pycache__/ImageFont.cpython-310.pyc,,
PIL/__pycache__/ImageGrab.cpython-310.pyc,,
PIL/__pycache__/ImageMath.cpython-310.pyc,,
PIL/__pycache__/ImageMode.cpython-310.pyc,,
PIL/__pycache__/ImageMorph.cpython-310.pyc,,
PIL/__pycache__/ImageOps.cpython-310.pyc,,
PIL/__pycache__/ImagePalette.cpython-310.pyc,,
PIL/__pycache__/ImagePath.cpython-310.pyc,,
PIL/__pycache__/ImageQt.cpython-310.pyc,,
PIL/__pycache__/ImageSequence.cpython-310.pyc,,
PIL/__pycache__/ImageShow.cpython-310.pyc,,
PIL/__pycache__/ImageStat.cpython-310.pyc,,
PIL/__pycache__/ImageTk.cpython-310.pyc,,
PIL/__pycache__/ImageTransform.cpython-310.pyc,,
PIL/__pycache__/ImageWin.cpython-310.pyc,,
PIL/__pycache__/ImtImagePlugin.cpython-310.pyc,,
PIL/__pycache__/IptcImagePlugin.cpython-310.pyc,,
PIL/__pycache__/Jpeg2KImagePlugin.cpython-310.pyc,,
PIL/__pycache__/JpegImagePlugin.cpython-310.pyc,,
PIL/__pycache__/JpegPresets.cpython-310.pyc,,
PIL/__pycache__/McIdasImagePlugin.cpython-310.pyc,,
PIL/__pycache__/MicImagePlugin.cpython-310.pyc,,
PIL/__pycache__/MpegImagePlugin.cpython-310.pyc,,
PIL/__pycache__/MpoImagePlugin.cpython-310.pyc,,
PIL/__pycache__/MspImagePlugin.cpython-310.pyc,,
PIL/__pycache__/PSDraw.cpython-310.pyc,,
PIL/__pycache__/PaletteFile.cpython-310.pyc,,
PIL/__pycache__/PalmImagePlugin.cpython-310.pyc,,
PIL/__pycache__/PcdImagePlugin.cpython-310.pyc,,
PIL/__pycache__/PcfFontFile.cpython-310.pyc,,
PIL/__pycache__/PcxImagePlugin.cpython-310.pyc,,
PIL/__pycache__/PdfImagePlugin.cpython-310.pyc,,
PIL/__pycache__/PdfParser.cpython-310.pyc,,
PIL/__pycache__/PixarImagePlugin.cpython-310.pyc,,
PIL/__pycache__/PngImagePlugin.cpython-310.pyc,,
PIL/__pycache__/PpmImagePlugin.cpython-310.pyc,,
PIL/__pycache__/PsdImagePlugin.cpython-310.pyc,,
PIL/__pycache__/QoiImagePlugin.cpython-310.pyc,,
PIL/__pycache__/SgiImagePlugin.cpython-310.pyc,,
PIL/__pycache__/SpiderImagePlugin.cpython-310.pyc,,
PIL/__pycache__/SunImagePlugin.cpython-310.pyc,,
PIL/__pycache__/TarIO.cpython-310.pyc,,
PIL/__pycache__/TgaImagePlugin.cpython-310.pyc,,
PIL/__pycache__/TiffImagePlugin.cpython-310.pyc,,
PIL/__pycache__/TiffTags.cpython-310.pyc,,
PIL/__pycache__/WalImageFile.cpython-310.pyc,,
PIL/__pycache__/WebPImagePlugin.cpython-310.pyc,,
PIL/__pycache__/WmfImagePlugin.cpython-310.pyc,,
PIL/__pycache__/XVThumbImagePlugin.cpython-310.pyc,,
PIL/__pycache__/XbmImagePlugin.cpython-310.pyc,,
PIL/__pycache__/XpmImagePlugin.cpython-310.pyc,,
PIL/__pycache__/__init__.cpython-310.pyc,,
PIL/__pycache__/__main__.cpython-310.pyc,,
PIL/__pycache__/_binary.cpython-310.pyc,,
PIL/__pycache__/_deprecate.cpython-310.pyc,,
PIL/__pycache__/_tkinter_finder.cpython-310.pyc,,
PIL/__pycache__/_typing.cpython-310.pyc,,
PIL/__pycache__/_util.cpython-310.pyc,,
PIL/__pycache__/_version.cpython-310.pyc,,
PIL/__pycache__/features.cpython-310.pyc,,
PIL/__pycache__/report.cpython-310.pyc,,
PIL/_binary.py,sha256=cb9p-_mwzBYumlVsWbnoTWsrLo59towA6atLOZvjO3w,2662
PIL/_deprecate.py,sha256=Jy_3Ty-WkxQg51m4pMQ1PgjYpfpJqAzKvvgP59GTUWY,2005
PIL/_imaging.cp310-win_amd64.pyd,sha256=AR6xvpERmTMPCWhWUysti_yngOXDc8pITxrihTK_IMU,2348032
PIL/_imaging.pyi,sha256=0c3GC20XgHn8HaIrEYPErvCABBq_wibJlRa8A3RsUk8,899
PIL/_imagingcms.cp310-win_amd64.pyd,sha256=Nwtx2VGXy5jOFQdNfabgkI3rVMlnfWToOlGv20DJ_TI,264192
PIL/_imagingcms.pyi,sha256=oB0dV9kzqnZk3CtnVzgZvwpRsPUqbltBZ19xLin7uHo,4532
PIL/_imagingft.cp310-win_amd64.pyd,sha256=Q_RVLwO1OoUNEPlaLSUtC3U3IkpoG5XvgLE78bHunzM,1828352
PIL/_imagingft.pyi,sha256=1hXXgNd6d9vEaTLaJzYCJBbH_f5WnSO7MuvbGGCTEgg,1858
PIL/_imagingmath.cp310-win_amd64.pyd,sha256=rgxDHqOv_S-OqTTPiKKDl1kB7QiN8ha_4PwzfYErfqE,25088
PIL/_imagingmath.pyi,sha256=zD8vAoPC8aEIVjfckLtFskRW5saiVel3-sJUA2pHaGc,66
PIL/_imagingmorph.cp310-win_amd64.pyd,sha256=U71d0Npat4mU83841lbYQa4neBI8JnrxlyMhJG6dC0A,13824
PIL/_imagingmorph.pyi,sha256=zD8vAoPC8aEIVjfckLtFskRW5saiVel3-sJUA2pHaGc,66
PIL/_imagingtk.cp310-win_amd64.pyd,sha256=vfqQK4e8cc1GM6ym_kTssloVxOUBXY8dvftkESibD_A,15360
PIL/_imagingtk.pyi,sha256=zD8vAoPC8aEIVjfckLtFskRW5saiVel3-sJUA2pHaGc,66
PIL/_tkinter_finder.py,sha256=jKydPAxnrytggsZQHB6kAQep6A9kzRNyx_nToT4ClKY,561
PIL/_typing.py,sha256=cEhC2d5bo_KlT7gOIzWIwjHxy0deRc_iA5avYZ7q_3k,1300
PIL/_util.py,sha256=c1SFb0eh9D_Sho4-YMFDZP5YOlpkOicqY7k5TCSrj_A,661
PIL/_version.py,sha256=mkejFVcrY7PmcX4LRQtqxikQTUY4Gy8EheIcftlcKjg,91
PIL/_webp.cp310-win_amd64.pyd,sha256=IE_GHeOFu_kPXlYd6AXVxV1ZE9C4p8RdziWuOwniZj4,410112
PIL/_webp.pyi,sha256=zD8vAoPC8aEIVjfckLtFskRW5saiVel3-sJUA2pHaGc,66
PIL/features.py,sha256=RkT695WJ3Zz-8oJZVNtuYvWjSXx0o1SQZTebTXqVXDk,11320
PIL/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
PIL/report.py,sha256=6m7NOv1a24577ZiJoxX89ip5JeOgf2O1F95f6-1K5aM,105
pillow-11.0.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pillow-11.0.0.dist-info/LICENSE,sha256=Kt-eAuQb225DncVxntbF0QJ2EfiWHgq8iy81zxXx6Ic,57500
pillow-11.0.0.dist-info/METADATA,sha256=2TagS9SDj68xROTQ3ID8cKjjQYA4GPL_0vsYi0FcAh8,9285
pillow-11.0.0.dist-info/RECORD,,
pillow-11.0.0.dist-info/WHEEL,sha256=0ZjvOlAkRhiFz0IEm5kQrC9Db9zGCLzyOcgLl0kpzxU,101
pillow-11.0.0.dist-info/top_level.txt,sha256=riZqrk-hyZqh5f1Z0Zwii3dKfxEsByhu9cU9IODF-NY,4
pillow-11.0.0.dist-info/zip-safe,sha256=frcCV1k9oG9oKj3dpUqdJg1PxRT2RSN_XKdLCPjaYaY,2
