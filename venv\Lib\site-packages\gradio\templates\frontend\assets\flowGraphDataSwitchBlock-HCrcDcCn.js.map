{"version": 3, "file": "flowGraphDataSwitchBlock-HCrcDcCn.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphDataSwitchBlock.js"], "sourcesContent": ["import { FlowGraphBlock } from \"../../flowGraphBlock.js\";\nimport { RichTypeAny } from \"../../flowGraphRichTypes.js\";\nimport { getNumericValue, isNumeric } from \"../../utils.js\";\nimport { RegisterClass } from \"../../../Misc/typeStore.js\";\n/**\n * This block conditionally outputs one of its inputs, based on a condition and a list of cases.\n *\n * This of it as a passive (data) version of the switch statement in programming languages.\n */\nexport class FlowGraphDataSwitchBlock extends FlowGraphBlock {\n    constructor(\n    /**\n     * the configuration of the block\n     */\n    config) {\n        super(config);\n        this.config = config;\n        this._inputCases = new Map();\n        this.case = this.registerDataInput(\"case\", RichTypeAny, NaN);\n        this.default = this.registerDataInput(\"default\", RichTypeAny);\n        this.value = this.registerDataOutput(\"value\", RichTypeAny);\n        // iterate the set not using for of\n        (this.config.cases || []).forEach((caseValue) => {\n            // if treat as integers, make sure not to set it again if it exists\n            caseValue = getNumericValue(caseValue);\n            if (this.config.treatCasesAsIntegers) {\n                caseValue = caseValue | 0;\n                if (this._inputCases.has(caseValue)) {\n                    return;\n                }\n            }\n            this._inputCases.set(caseValue, this.registerDataInput(`in_${caseValue}`, RichTypeAny));\n        });\n    }\n    _updateOutputs(context) {\n        const selectionValue = this.case.getValue(context);\n        let outputValue;\n        if (isNumeric(selectionValue)) {\n            outputValue = this._getOutputValueForCase(getNumericValue(selectionValue), context);\n        }\n        else {\n            outputValue = this.default.getValue(context);\n        }\n        this.value.setValue(outputValue, context);\n    }\n    _getOutputValueForCase(caseValue, context) {\n        return this._inputCases.get(caseValue)?.getValue(context);\n    }\n    getClassName() {\n        return \"FlowGraphDataSwitchBlock\" /* FlowGraphBlockNames.DataSwitch */;\n    }\n}\nRegisterClass(\"FlowGraphDataSwitchBlock\" /* FlowGraphBlockNames.DataSwitch */, FlowGraphDataSwitchBlock);\n//# sourceMappingURL=flowGraphDataSwitchBlock.js.map"], "names": ["FlowGraphDataSwitchBlock", "FlowGraphBlock", "config", "RichTypeAny", "caseValue", "getNumericValue", "context", "selectionValue", "outputValue", "isNumeric", "RegisterClass"], "mappings": "8PASO,MAAMA,UAAiCC,CAAe,CACzD,YAIAC,EAAQ,CACJ,MAAMA,CAAM,EACZ,KAAK,OAASA,EACd,KAAK,YAAc,IAAI,IACvB,KAAK,KAAO,KAAK,kBAAkB,OAAQC,EAAa,GAAG,EAC3D,KAAK,QAAU,KAAK,kBAAkB,UAAWA,CAAW,EAC5D,KAAK,MAAQ,KAAK,mBAAmB,QAASA,CAAW,GAExD,KAAK,OAAO,OAAS,CAAA,GAAI,QAASC,GAAc,CAE7CA,EAAYC,EAAgBD,CAAS,EACjC,OAAK,OAAO,uBACZA,EAAYA,EAAY,EACpB,KAAK,YAAY,IAAIA,CAAS,KAItC,KAAK,YAAY,IAAIA,EAAW,KAAK,kBAAkB,MAAMA,CAAS,GAAID,CAAW,CAAC,CAClG,CAAS,CACJ,CACD,eAAeG,EAAS,CACpB,MAAMC,EAAiB,KAAK,KAAK,SAASD,CAAO,EACjD,IAAIE,EACAC,EAAUF,CAAc,EACxBC,EAAc,KAAK,uBAAuBH,EAAgBE,CAAc,EAAGD,CAAO,EAGlFE,EAAc,KAAK,QAAQ,SAASF,CAAO,EAE/C,KAAK,MAAM,SAASE,EAAaF,CAAO,CAC3C,CACD,uBAAuBF,EAAWE,EAAS,CACvC,OAAO,KAAK,YAAY,IAAIF,CAAS,GAAG,SAASE,CAAO,CAC3D,CACD,cAAe,CACX,MAAO,0BACV,CACL,CACAI,EAAc,2BAAiEV,CAAwB", "x_google_ignoreList": [0]}