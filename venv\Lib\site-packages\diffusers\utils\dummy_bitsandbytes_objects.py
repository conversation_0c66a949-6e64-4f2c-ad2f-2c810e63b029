# This file is autogenerated by the command `make fix-copies`, do not edit.
from ..utils import DummyObject, requires_backends


class BitsAndBytesConfig(metaclass=DummyObject):
    _backends = ["bitsandbytes"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["bitsandbytes"])

    @classmethod
    def from_config(cls, *args, **kwargs):
        requires_backends(cls, ["bitsandbytes"])

    @classmethod
    def from_pretrained(cls, *args, **kwargs):
        requires_backends(cls, ["bitsandbytes"])
