{"version": 3, "file": "flowGraphContextBlock-BocHg12j.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Utils/flowGraphContextBlock.js"], "sourcesContent": ["import { FlowGraphBlock } from \"../../../flowGraphBlock.js\";\nimport { RichTypeAny, RichTypeNumber } from \"../../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\n/**\n * A block that outputs elements from the context\n */\nexport class FlowGraphContextBlock extends FlowGraphBlock {\n    constructor(config) {\n        super(config);\n        this.userVariables = this.registerDataOutput(\"userVariables\", RichTypeAny);\n        this.executionId = this.registerDataOutput(\"executionId\", RichTypeNumber);\n    }\n    _updateOutputs(context) {\n        this.userVariables.setValue(context.userVariables, context);\n        this.executionId.setValue(context.executionId, context);\n    }\n    serialize(serializationObject) {\n        super.serialize(serializationObject);\n    }\n    getClassName() {\n        return \"FlowGraphContextBlock\" /* FlowGraphBlockNames.Context */;\n    }\n}\nRegisterClass(\"FlowGraphContextBlock\" /* FlowGraphBlockNames.Context */, FlowGraphContextBlock);\n//# sourceMappingURL=flowGraphContextBlock.js.map"], "names": ["FlowGraphContextBlock", "FlowGraphBlock", "config", "RichTypeAny", "RichTypeNumber", "context", "serializationObject", "RegisterClass"], "mappings": "uPAMO,MAAMA,UAA8BC,CAAe,CACtD,YAAYC,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,cAAgB,KAAK,mBAAmB,gBAAiBC,CAAW,EACzE,KAAK,YAAc,KAAK,mBAAmB,cAAeC,CAAc,CAC3E,CACD,eAAeC,EAAS,CACpB,KAAK,cAAc,SAASA,EAAQ,cAAeA,CAAO,EAC1D,KAAK,YAAY,SAASA,EAAQ,YAAaA,CAAO,CACzD,CACD,UAAUC,EAAqB,CAC3B,MAAM,UAAUA,CAAmB,CACtC,CACD,cAAe,CACX,MAAO,uBACV,CACL,CACAC,EAAc,wBAA2DP,CAAqB", "x_google_ignoreList": [0]}