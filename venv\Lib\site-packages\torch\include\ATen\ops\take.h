#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/take_ops.h>

namespace at {


// aten::take.out(Tensor self, Tensor index, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & take_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & index) {
    return at::_ops::take_out::call(self, index, out);
}
// aten::take.out(Tensor self, Tensor index, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & take_outf(const at::Tensor & self, const at::Tensor & index, at::Tensor & out) {
    return at::_ops::take_out::call(self, index, out);
}

// aten::take(Tensor self, Tensor index) -> Tensor
inline at::Tensor take(const at::Tensor & self, const at::Tensor & index) {
    return at::_ops::take::call(self, index);
}

}
