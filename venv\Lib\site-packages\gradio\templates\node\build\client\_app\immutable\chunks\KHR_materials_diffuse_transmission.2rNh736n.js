import{ar as c,C as f,an as l,ao as d}from"./index.BoI39RQH.js";import{GLTFLoader as m}from"./glTFLoader.BetPWe9U.js";const o="KHR_materials_diffuse_transmission";class T{constructor(r){this.name=o,this.order=174,this._loader=r,this.enabled=this._loader.isExtensionUsed(o),this.enabled&&(r.parent.transparencyAsCoverage=!0)}dispose(){this._loader=null}loadMaterialPropertiesAsync(r,u,n){return m.LoadExtensionAsync(r,u,this.name,(e,s)=>{const i=new Array;return i.push(this._loader.loadMaterialPropertiesAsync(r,u,n)),i.push(this._loadTranslucentPropertiesAsync(e,u,n,s)),Promise.all(i).then(()=>{})})}_loadTranslucentPropertiesAsync(r,u,n,e){if(!(n instanceof c))throw new Error(`${r}: Material type not supported`);const s=n;if(s.subSurface.isTranslucencyEnabled=!0,s.subSurface.volumeIndexOfRefraction=1,s.subSurface.minimumThickness=0,s.subSurface.maximumThickness=0,s.subSurface.useAlbedoToTintTranslucency=!1,e.diffuseTransmissionFactor!==void 0)s.subSurface.translucencyIntensity=e.diffuseTransmissionFactor;else return s.subSurface.translucencyIntensity=0,s.subSurface.isTranslucencyEnabled=!1,Promise.resolve();const i=new Array;return s.subSurface.useGltfStyleTextures=!0,e.diffuseTransmissionTexture&&(e.diffuseTransmissionTexture.nonColorData=!0,i.push(this._loader.loadTextureInfoAsync(`${r}/diffuseTransmissionTexture`,e.diffuseTransmissionTexture).then(a=>{a.name=`${n.name} (Diffuse Transmission)`,s.subSurface.translucencyIntensityTexture=a}))),e.diffuseTransmissionColorFactor!==void 0?s.subSurface.translucencyColor=f.FromArray(e.diffuseTransmissionColorFactor):s.subSurface.translucencyColor=f.White(),e.diffuseTransmissionColorTexture&&i.push(this._loader.loadTextureInfoAsync(`${r}/diffuseTransmissionColorTexture`,e.diffuseTransmissionColorTexture).then(a=>{a.name=`${n.name} (Diffuse Transmission Color)`,s.subSurface.translucencyColorTexture=a})),Promise.all(i).then(()=>{})}}l(o);d(o,!0,t=>new T(t));export{T as KHR_materials_diffuse_transmission};
//# sourceMappingURL=KHR_materials_diffuse_transmission.2rNh736n.js.map
