/* empty css                                                        */const{SvelteComponent:u,attr:a,create_slot:c,detach:f,element:d,flush:i,get_all_dirty_from_scope:h,get_slot_changes:m,init:y,insert:g,null_to_empty:p,safe_not_equal:v,transition_in:b,transition_out:w,update_slot_base:$}=window.__gradio__svelte__internal;function I(s){let e,r,o;const _=s[3].default,n=c(_,s,s[2],null);return{c(){e=d("div"),n&&n.c(),a(e,"class",r=p(`icon-button-wrapper ${s[0]?"top-panel":""} ${s[1]?"display-top-corner":"hide-top-corner"}`)+" svelte-9lsba8")},m(t,l){g(t,e,l),n&&n.m(e,null),o=!0},p(t,[l]){n&&n.p&&(!o||l&4)&&$(n,_,t,t[2],o?m(_,t[2],l,null):h(t[2]),null),(!o||l&3&&r!==(r=p(`icon-button-wrapper ${t[0]?"top-panel":""} ${t[1]?"display-top-corner":"hide-top-corner"}`)+" svelte-9lsba8"))&&a(e,"class",r)},i(t){o||(b(n,t),o=!0)},o(t){w(n,t),o=!1},d(t){t&&f(e),n&&n.d(t)}}}function q(s,e,r){let{$$slots:o={},$$scope:_}=e,{top_panel:n=!0}=e,{display_top_corner:t=!1}=e;return s.$$set=l=>{"top_panel"in l&&r(0,n=l.top_panel),"display_top_corner"in l&&r(1,t=l.display_top_corner),"$$scope"in l&&r(2,_=l.$$scope)},[n,t,_,o]}class C extends u{constructor(e){super(),y(this,e,q,I,v,{top_panel:0,display_top_corner:1})}get top_panel(){return this.$$.ctx[0]}set top_panel(e){this.$$set({top_panel:e}),i()}get display_top_corner(){return this.$$.ctx[1]}set display_top_corner(e){this.$$set({display_top_corner:e}),i()}}export{C as I};
//# sourceMappingURL=IconButtonWrapper--EIOWuEM.js.map
