import { c as create_ssr_component, v as validate_component, e as escape, b as createEventDispatcher, d as add_attribute } from './ssr-C3HYbsxA.js';
import { ah as E } from './2-DJbI4FWc.js';
import './index-ClteBeTX.js';
import './Component-NmRBwSfF.js';
import 'path';
import 'url';
import 'fs';

const z={code:".button-icon.svelte-yjn27e{width:var(--text-xl);height:var(--text-xl);margin-right:var(--spacing-xl)}",map:'{"version":3,"file":"DownloadButton.svelte","sources":["DownloadButton.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { createEventDispatcher } from \\"svelte\\";\\nimport {} from \\"@gradio/client\\";\\nimport { BaseButton } from \\"@gradio/button\\";\\nexport let elem_id = \\"\\";\\nexport let elem_classes = [];\\nexport let visible = true;\\nexport let variant = \\"secondary\\";\\nexport let size = \\"lg\\";\\nexport let value;\\nexport let icon;\\nexport let disabled = false;\\nexport let scale = null;\\nexport let min_width = void 0;\\nconst dispatch = createEventDispatcher();\\nfunction download_file() {\\n    dispatch(\\"click\\");\\n    if (!value?.url) {\\n        return;\\n    }\\n    let file_name;\\n    if (!value.orig_name && value.url) {\\n        const parts = value.url.split(\\"/\\");\\n        file_name = parts[parts.length - 1];\\n        file_name = file_name.split(\\"?\\")[0].split(\\"#\\")[0];\\n    }\\n    else {\\n        file_name = value.orig_name;\\n    }\\n    const a = document.createElement(\\"a\\");\\n    a.href = value.url;\\n    a.download = file_name || \\"file\\";\\n    document.body.appendChild(a);\\n    a.click();\\n    document.body.removeChild(a);\\n}\\n<\/script>\\n\\n<BaseButton\\n\\t{size}\\n\\t{variant}\\n\\t{elem_id}\\n\\t{elem_classes}\\n\\t{visible}\\n\\ton:click={download_file}\\n\\t{scale}\\n\\t{min_width}\\n\\t{disabled}\\n>\\n\\t{#if icon}\\n\\t\\t<img class=\\"button-icon\\" src={icon.url} alt={`${value} icon`} />\\n\\t{/if}\\n\\t<slot />\\n</BaseButton>\\n\\n<style>\\n\\t.button-icon {\\n\\t\\twidth: var(--text-xl);\\n\\t\\theight: var(--text-xl);\\n\\t\\tmargin-right: var(--spacing-xl);\\n\\t}</style>\\n"],"names":[],"mappings":"AAuDC,0BAAa,CACZ,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,MAAM,CAAE,IAAI,SAAS,CAAC,CACtB,YAAY,CAAE,IAAI,YAAY,CAC/B"}'},y=create_ssr_component((r,e,t,f)=>{let{elem_id:a=""}=e,{elem_classes:i=[]}=e,{visible:o=!0}=e,{variant:v="secondary"}=e,{size:n="lg"}=e,{value:c}=e,{icon:l}=e,{disabled:d=!1}=e,{scale:m=null}=e,{min_width:u=void 0}=e;return createEventDispatcher(),e.elem_id===void 0&&t.elem_id&&a!==void 0&&t.elem_id(a),e.elem_classes===void 0&&t.elem_classes&&i!==void 0&&t.elem_classes(i),e.visible===void 0&&t.visible&&o!==void 0&&t.visible(o),e.variant===void 0&&t.variant&&v!==void 0&&t.variant(v),e.size===void 0&&t.size&&n!==void 0&&t.size(n),e.value===void 0&&t.value&&c!==void 0&&t.value(c),e.icon===void 0&&t.icon&&l!==void 0&&t.icon(l),e.disabled===void 0&&t.disabled&&d!==void 0&&t.disabled(d),e.scale===void 0&&t.scale&&m!==void 0&&t.scale(m),e.min_width===void 0&&t.min_width&&u!==void 0&&t.min_width(u),r.css.add(z),`${validate_component(E,"BaseButton").$$render(r,{size:n,variant:v,elem_id:a,elem_classes:i,visible:o,scale:m,min_width:u,disabled:d},{},{default:()=>`${l?`<img class="button-icon svelte-yjn27e"${add_attribute("src",l.url,0)}${add_attribute("alt",`${c} icon`,0)}>`:""} ${f.default?f.default({}):""}`})}`}),D=y,k=create_ssr_component((r,e,t,f)=>{let{elem_id:a=""}=e,{elem_classes:i=[]}=e,{visible:o=!0}=e,{value:v}=e,{variant:n="secondary"}=e,{interactive:c}=e,{size:l="lg"}=e,{scale:d=null}=e,{icon:m=null}=e,{min_width:u=void 0}=e,{label:s=null}=e,{gradio:_}=e;return e.elem_id===void 0&&t.elem_id&&a!==void 0&&t.elem_id(a),e.elem_classes===void 0&&t.elem_classes&&i!==void 0&&t.elem_classes(i),e.visible===void 0&&t.visible&&o!==void 0&&t.visible(o),e.value===void 0&&t.value&&v!==void 0&&t.value(v),e.variant===void 0&&t.variant&&n!==void 0&&t.variant(n),e.interactive===void 0&&t.interactive&&c!==void 0&&t.interactive(c),e.size===void 0&&t.size&&l!==void 0&&t.size(l),e.scale===void 0&&t.scale&&d!==void 0&&t.scale(d),e.icon===void 0&&t.icon&&m!==void 0&&t.icon(m),e.min_width===void 0&&t.min_width&&u!==void 0&&t.min_width(u),e.label===void 0&&t.label&&s!==void 0&&t.label(s),e.gradio===void 0&&t.gradio&&_!==void 0&&t.gradio(_),`${validate_component(D,"DownloadButton").$$render(r,{value:v,variant:n,elem_id:a,elem_classes:i,size:l,scale:d,icon:m,min_width:u,visible:o,disabled:!c},{},{default:()=>`${escape(s??"")}`})}`});

export { D as BaseButton, k as default };
//# sourceMappingURL=Index41-D8u_1Oos.js.map
