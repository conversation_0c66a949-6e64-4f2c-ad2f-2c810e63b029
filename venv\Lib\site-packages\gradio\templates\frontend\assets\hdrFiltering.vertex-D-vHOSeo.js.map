{"version": 3, "file": "hdrFiltering.vertex-D-vHOSeo.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/hdrFiltering.vertex.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nconst name = \"hdrFilteringVertexShader\";\nconst shader = `attribute position: vec2f;varying direction: vec3f;uniform up: vec3f;uniform right: vec3f;uniform front: vec3f;\n#define CUSTOM_VERTEX_DEFINITIONS\n@vertex\nfn main(input : VertexInputs)->FragmentInputs {\n#define CUSTOM_VERTEX_MAIN_BEGIN\nvar view: mat3x3f= mat3x3f(uniforms.up,uniforms.right,uniforms.front);vertexOutputs.direction=view*vec3f(input.position,1.0);vertexOutputs.position= vec4f(input.position,0.0,1.0);\n#define CUSTOM_VERTEX_MAIN_END\n}`;\n// Sideeffect\nif (!ShaderStore.ShadersStoreWGSL[name]) {\n    ShaderStore.ShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const hdrFilteringVertexShaderWGSL = { name, shader };\n//# sourceMappingURL=hdrFiltering.vertex.js.map"], "names": ["name", "shader", "ShaderStore", "hdrFilteringVertexShaderWGSL"], "mappings": "+FAEA,MAAMA,EAAO,2BACPC,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GASVC,EAAY,iBAAiBF,CAAI,IAClCE,EAAY,iBAAiBF,CAAI,EAAIC,GAG7B,MAACE,EAA+B,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}