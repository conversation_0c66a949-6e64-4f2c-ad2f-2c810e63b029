{"version": 3, "file": "Example.DU87hEH5.js", "sources": ["../../../../../../../imageeditor/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { BaseImage as Image } from \"@gradio/image\";\n\timport type { EditorData } from \"./InteractiveImageEditor.svelte\";\n\n\texport let value: EditorData;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass=\"container\"\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t<Image src={value.composite?.url || value.background?.url} alt=\"\" />\n</div>\n\n<style>\n\t.container :global(img) {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.container.selected {\n\t\tborder-color: var(--border-color-accent);\n\t}\n\n\t.container.table {\n\t\tmargin: 0 auto;\n\t\tborder: 2px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-lg);\n\t\twidth: var(--size-20);\n\t\theight: var(--size-20);\n\t\tobject-fit: cover;\n\t}\n\n\t.container.gallery {\n\t\tborder: 2px solid var(--border-color-primary);\n\t\theight: var(--size-20);\n\t\tmax-height: var(--size-20);\n\t\tobject-fit: cover;\n\t}\n</style>\n"], "names": ["_a", "ctx", "_b", "toggle_class", "div", "insert_hydration", "target", "anchor", "value", "$$props", "type", "selected"], "mappings": "kjBAeaA,EAAAC,EAAK,CAAA,EAAC,YAAN,YAAAD,EAAiB,QAAOE,EAAAD,EAAK,CAAA,EAAC,aAAN,YAAAC,EAAkB,uLAJzCC,EAAAC,EAAA,QAAAH,OAAS,OAAO,EACdE,EAAAC,EAAA,UAAAH,OAAS,SAAS,+BAHlCI,EAOKC,EAAAF,EAAAG,CAAA,8DADQP,EAAAC,EAAK,CAAA,EAAC,YAAN,YAAAD,EAAiB,QAAOE,EAAAD,EAAK,CAAA,EAAC,aAAN,YAAAC,EAAkB,2BAJzCC,EAAAC,EAAA,QAAAH,OAAS,OAAO,aACdE,EAAAC,EAAA,UAAAH,OAAS,SAAS,0IARtB,GAAA,CAAA,MAAAO,CAAA,EAAAC,EACA,CAAA,KAAAC,CAAA,EAAAD,GACA,SAAAE,EAAW,EAAA,EAAAF"}