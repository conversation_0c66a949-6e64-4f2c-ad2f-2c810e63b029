{"version": 3, "file": "Settings-DWdlBuRz.js", "sources": ["../../../../js/core/src/api_docs/SettingsBanner.svelte", "../../../../js/core/src/api_docs/img/record.svg", "../../../../js/core/src/api_docs/Settings.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { _ } from \"svelte-i18n\";\n\timport settings_logo from \"./img/settings-logo.svg\";\n\timport Clear from \"./img/clear.svelte\";\n\timport { setupi18n } from \"../i18n\";\n\n\texport let root: string;\n\n\tconst dispatch = createEventDispatcher();\n\tsetupi18n();\n</script>\n\n<h2>\n\t<img src={settings_logo} alt=\"\" />\n\t<div class=\"title\">\n\t\t{$_(\"common.settings\")}\n\t\t<div class=\"url\">\n\t\t\t{root}\n\t\t</div>\n\t</div>\n</h2>\n\n<button on:click={() => dispatch(\"close\")}>\n\t<Clear />\n</button>\n\n<style>\n\th2 {\n\t\tdisplay: flex;\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--weight-semibold);\n\t\tfont-size: var(--text-xl);\n\t\tgap: var(--size-3);\n\t}\n\n\th2 img {\n\t\twidth: var(--size-4);\n\t\tdisplay: inline-block;\n\t\tmargin-top: 0.1rem;\n\t}\n\n\t.url {\n\t\tcolor: var(--color-accent);\n\t\tfont-weight: normal;\n\t}\n\n\tbutton {\n\t\tposition: absolute;\n\t\ttop: var(--size-5);\n\t\tright: var(--size-6);\n\t\twidth: var(--size-4);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\tbutton:hover {\n\t\tcolor: var(--color-accent);\n\t}\n\n\t@media (--screen-md) {\n\t\tbutton {\n\t\t\ttop: var(--size-6);\n\t\t}\n\n\t\th2 img {\n\t\t\twidth: var(--size-5);\n\t\t}\n\t}\n\t.title {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tgap: 0.5rem;\n\t}\n</style>\n", "export default \"data:image/svg+xml,%3csvg%20viewBox='0%200%2020%2020'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='%23000000'%3e%3cg%20id='SVGRepo_bgCarrier'%20stroke-width='0'%3e%3c/g%3e%3cg%20id='SVGRepo_tracerCarrier'%20stroke-linecap='round'%20stroke-linejoin='round'%3e%3c/g%3e%3cg%20id='SVGRepo_iconCarrier'%3e%3ctitle%3erecord%20[%23982]%3c/title%3e%3cdesc%3eCreated%20with%20Sketch.%3c/desc%3e%3cdefs%3e%3c/defs%3e%3cg%20id='Page-1'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='Dribbble-Light-Preview'%20transform='translate(-380.000000,%20-3839.000000)'%20fill='%23808080'%3e%3cg%20id='icons'%20transform='translate(56.000000,%20160.000000)'%3e%3cpath%20d='M338,3689%20C338,3691.209%20336.209,3693%20334,3693%20C331.791,3693%20330,3691.209%20330,3689%20C330,3686.791%20331.791,3685%20334,3685%20C336.209,3685%20338,3686.791%20338,3689%20M334,3697%20C329.589,3697%20326,3693.411%20326,3689%20C326,3684.589%20329.589,3681%20334,3681%20C338.411,3681%20342,3684.589%20342,3689%20C342,3693.411%20338.411,3697%20334,3697%20M334,3679%20C328.477,3679%20324,3683.477%20324,3689%20C324,3694.523%20328.477,3699%20334,3699%20C339.523,3699%20344,3694.523%20344,3689%20C344,3683.477%20339.523,3679%20334,3679'%20id='record-[%23982]'%3e%3c/path%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e\"", "<script lang=\"ts\">\n\t/* eslint-disable */\n\timport { onMount } from \"svelte\";\n\timport SettingsBanner from \"./SettingsBanner.svelte\";\n\texport let root: string;\n\texport let space_id: string | null;\n\texport let pwa_enabled: boolean | undefined;\n\timport { BaseDropdown as Dropdown } from \"@gradio/dropdown\";\n\timport { BaseCheckbox as Checkbox } from \"@gradio/checkbox\";\n\timport { language_choices, changeLocale } from \"../i18n\";\n\timport { locale, _ } from \"svelte-i18n\";\n\timport { setupi18n } from \"../i18n\";\n\timport record from \"./img/record.svg\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\tconst dispatch = createEventDispatcher();\n\tif (root === \"\") {\n\t\troot = location.protocol + \"//\" + location.host + location.pathname;\n\t}\n\tif (!root.endsWith(\"/\")) {\n\t\troot += \"/\";\n\t}\n\n\tfunction setTheme(theme: \"light\" | \"dark\" | \"system\") {\n\t\tconst url = new URL(window.location.href);\n\t\tif (theme === \"system\") {\n\t\t\turl.searchParams.delete(\"__theme\");\n\t\t\tcurrent_theme = \"system\";\n\t\t} else {\n\t\t\turl.searchParams.set(\"__theme\", theme);\n\t\t\tcurrent_theme = theme;\n\t\t}\n\t\twindow.location.href = url.toString();\n\t}\n\n\tonMount(() => {\n\t\tdocument.body.style.overflow = \"hidden\";\n\t\tif (\"parentIFrame\" in window) {\n\t\t\twindow.parentIFrame?.scrollTo(0, 0);\n\t\t}\n\t\tconst url = new URL(window.location.href);\n\t\tconst theme = url.searchParams.get(\"__theme\");\n\t\tcurrent_theme = (theme as \"light\" | \"dark\" | \"system\") || \"system\";\n\t\treturn () => {\n\t\t\tdocument.body.style.overflow = \"auto\";\n\t\t};\n\t});\n\n\tlet current_locale: string;\n\tlet current_theme: \"light\" | \"dark\" | \"system\" = \"system\";\n\texport let allow_zoom = true;\n\texport let allow_video_trim = true;\n\n\tlocale.subscribe((value) => {\n\t\tif (value) {\n\t\t\tcurrent_locale = value;\n\t\t}\n\t});\n\n\tfunction handleLanguageChange(e: CustomEvent): void {\n\t\tconst new_locale = e.detail;\n\t\tchangeLocale(new_locale);\n\t}\n\n\tfunction handleZoomChange(e: CustomEvent): void {\n\t\tallow_zoom = e.detail;\n\t}\n\n\tfunction handleVideoTrimChange(e: CustomEvent): void {\n\t\tallow_video_trim = e.detail;\n\t}\n\n\tsetupi18n();\n</script>\n\n<div class=\"banner-wrap\">\n\t<SettingsBanner on:close {root} />\n</div>\n{#if space_id === null}\n\t<!-- on Spaces, the theme is set in HF settings -->\n\t<div class=\"banner-wrap\">\n\t\t<h2>{$_(\"common.display_theme\")}</h2>\n\t\t<p class=\"padded theme-buttons\">\n\t\t\t<li\n\t\t\t\tclass=\"theme-button {current_theme === 'light'\n\t\t\t\t\t? 'current-theme'\n\t\t\t\t\t: 'inactive-theme'}\"\n\t\t\t\ton:click={() => setTheme(\"light\")}\n\t\t\t>\n\t\t\t\t<button>☀︎ &nbsp;Light</button>\n\t\t\t</li>\n\t\t\t<li\n\t\t\t\tclass=\"theme-button {current_theme === 'dark'\n\t\t\t\t\t? 'current-theme'\n\t\t\t\t\t: 'inactive-theme'}\"\n\t\t\t\ton:click={() => setTheme(\"dark\")}\n\t\t\t>\n\t\t\t\t<button>⏾ &nbsp; Dark</button>\n\t\t\t</li>\n\t\t\t<li\n\t\t\t\tclass=\"theme-button {current_theme === 'system'\n\t\t\t\t\t? 'current-theme'\n\t\t\t\t\t: 'inactive-theme'}\"\n\t\t\t\ton:click={() => setTheme(\"system\")}\n\t\t\t>\n\t\t\t\t<button>🖥︎ &nbsp;System</button>\n\t\t\t</li>\n\t\t</p>\n\t</div>\n{/if}\n<div class=\"banner-wrap\">\n\t<h2>{$_(\"common.language\")}</h2>\n\t<p class=\"padded\">\n\t\t<Dropdown\n\t\t\tlabel=\"Language\"\n\t\t\tchoices={language_choices}\n\t\t\tshow_label={false}\n\t\t\tvalue={current_locale}\n\t\t\ton:change={handleLanguageChange}\n\t\t/>\n\t</p>\n</div>\n<div class=\"banner-wrap\">\n\t<h2>{$_(\"common.pwa\")}</h2>\n\t<p class=\"padded\">\n\t\t{#if pwa_enabled}\n\t\t\tYou can install this app as a Progressive Web App on your device. Visit <a\n\t\t\t\thref={root}\n\t\t\t\ttarget=\"_blank\">{root}</a\n\t\t\t> and click the install button in the URL address bar of your browser.\n\t\t{:else}\n\t\t\tProgressive Web App is not enabled for this app. To enable it, start your\n\t\t\tGradio app with <code>launch(pwa=True)</code>.\n\t\t{/if}\n\t</p>\n</div>\n<div class=\"banner-wrap\">\n\t<h2>{$_(\"common.screen_studio\")} <span class=\"beta-tag\">beta</span></h2>\n\t<p class=\"padded\">\n\t\tScreen Studio allows you to record your screen and generates a video of your\n\t\tapp with automatically adding zoom in and zoom out effects as well as\n\t\ttrimming the video to remove the prediction time.\n\t\t<br /><br />\n\t\tStart recording by clicking the <i>Start Recording</i> button below and then\n\t\tsharing the current browser tab of your Gradio demo. Use your app as you\n\t\twould normally to generate a prediction.\n\t\t<br />\n\t\tStop recording by clicking the <i>Stop Recording</i> button in the footer of\n\t\tthe demo.\n\t\t<br /><br />\n\t\t<Checkbox\n\t\t\tlabel=\"Include automatic zoom in/out\"\n\t\t\tinteractive={true}\n\t\t\tvalue={allow_zoom}\n\t\t\ton:change={handleZoomChange}\n\t\t/>\n\t\t<Checkbox\n\t\t\tlabel=\"Include automatic video trimming\"\n\t\t\tinteractive={true}\n\t\t\tvalue={allow_video_trim}\n\t\t\ton:change={handleVideoTrimChange}\n\t\t/>\n\t</p>\n\t<button\n\t\tclass=\"record-button\"\n\t\ton:click={() => {\n\t\t\tdispatch(\"close\");\n\t\t\tdispatch(\"start_recording\");\n\t\t}}\n\t>\n\t\t<img src={record} alt=\"Start Recording\" />\n\t\tStart Recording\n\t</button>\n</div>\n\n<style>\n\t.banner-wrap {\n\t\tposition: relative;\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t\tpadding: var(--size-4) var(--size-6);\n\t\tfont-size: var(--text-md);\n\t}\n\n\t.banner-wrap h2 {\n\t\tfont-size: var(--text-xl);\n\t}\n\n\ta {\n\t\ttext-decoration: underline;\n\t}\n\n\tp.padded {\n\t\tpadding: 15px 0px;\n\t}\n\n\t.theme-buttons {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.theme-buttons > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\t.theme-button,\n\t.record-button {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-md);\n\t\tpadding: var(--size-2) var(--size-2-5);\n\t\tline-height: 1;\n\t\tuser-select: none;\n\t\ttext-transform: capitalize;\n\t\tcursor: pointer;\n\t}\n\n\t.record-button img {\n\t\tmargin-right: var(--size-1);\n\t\tmargin-left: var(--size-1);\n\t\twidth: var(--size-3);\n\t}\n\t.record-button:hover {\n\t\tborder-color: red;\n\t}\n\n\t.current-theme {\n\t\tborder: 1px solid var(--body-text-color-subdued);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.inactive-theme {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.inactive-theme:hover,\n\t.inactive-theme:focus {\n\t\tbox-shadow: var(--shadow-drop);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.theme-button button {\n\t\tall: unset;\n\t\tcursor: pointer;\n\t}\n\n\t.beta-tag {\n\t\tposition: relative;\n\t\ttop: -5px;\n\t\tfont-size: var(--text-xs);\n\t\tbackground-color: var(--color-accent);\n\t\tcolor: white;\n\t\tpadding: 2px 6px;\n\t\tborder-radius: 10px;\n\t\tmargin-left: 5px;\n\t\tfont-weight: normal;\n\t\ttext-transform: uppercase;\n\t}\n</style>\n"], "names": ["t1_value", "ctx", "settings_logo", "attr", "img", "img_src_value", "insert", "target", "h2", "anchor", "append", "div1", "div0", "button", "current", "dirty", "set_data", "t1", "root", "$$props", "dispatch", "createEventDispatcher", "setupi18n", "click_handler", "record", "t0_value", "li0", "li0_class_value", "li1", "li1_class_value", "li2", "li2_class_value", "div", "p", "button0", "button1", "button2", "t0", "code", "a", "t2_value", "t5_value", "t8_value", "if_block0", "create_if_block_1", "language_choices", "create_if_block", "h20", "p0", "div2", "h21", "p1", "div3", "h22", "span", "p2", "br0", "br1", "i0", "br2", "i1", "br3", "br4", "t2", "t5", "t8", "space_id", "pwa_enabled", "setTheme", "theme", "url", "current_theme", "onMount", "$$invalidate", "current_locale", "allow_zoom", "allow_video_trim", "locale", "value", "handleLanguageChange", "e", "new_locale", "changeLocale", "handleZoomChange", "handleVideoTrimChange", "click_handler_1", "click_handler_2"], "mappings": "imCACuC,EAAA,OAAA,wDAepCA,EAAAC,KAAG,iBAAiB,EAAA,sHAEnBA,EAAI,CAAA,CAAA,oDAJGC,EAAa,GAAAC,EAAAC,EAAA,MAAAC,CAAA,wLADxBC,GAQIC,EAAAC,EAAAC,CAAA,EAPHC,EAAiCF,EAAAJ,CAAA,SACjCM,EAKKF,EAAAG,CAAA,gBAHJD,EAEKC,EAAAC,CAAA,mBAIPN,GAEQC,EAAAM,EAAAJ,CAAA,8DATL,CAAAK,GAAAC,EAAA,IAAAf,KAAAA,EAAAC,KAAG,iBAAiB,EAAA,KAAAe,GAAAC,EAAAjB,CAAA,kBAEnBC,EAAI,CAAA,CAAA,iKAXI,GAAA,CAAA,KAAAiB,CAAA,EAAAC,QAELC,EAAWC,KACjBC,KAauB,MAAAC,EAAA,IAAAH,EAAS,OAAO,0MCvBxC,MAAeI,GAAA,2pDCEU,EAAA,OAAA,oDAWc,EAAA,OAAA,kDAoEhCC,EAAAxB,KAAG,sBAAsB,EAAA,wTAGPE,EAAAuB,EAAA,QAAAC,EAAA,iBAAA1B,OAAkB,QACpC,gBACA,kBAAgB,iBAAA,gCAMEE,EAAAyB,EAAA,QAAAC,EAAA,iBAAA5B,OAAkB,OACpC,gBACA,kBAAgB,iBAAA,gCAMEE,EAAA2B,EAAA,QAAAC,EAAA,iBAAA9B,OAAkB,SACpC,gBACA,kBAAgB,iBAAA,uGAtBtBK,EA4BKC,EAAAyB,EAAAvB,CAAA,EA3BJC,EAAoCsB,EAAAxB,CAAA,gBACpCE,EAyBGsB,EAAAC,CAAA,EAxBFvB,EAOIuB,EAAAP,CAAA,EADHhB,EAA8BgB,EAAAQ,CAAA,SAE/BxB,EAOIuB,EAAAL,CAAA,EADHlB,EAA6BkB,EAAAO,CAAA,SAE9BzB,EAOIuB,EAAAH,CAAA,EADHpB,EAAgCoB,EAAAM,CAAA,iFAxB7BrB,EAAA,KAAAU,KAAAA,EAAAxB,KAAG,sBAAsB,EAAA,KAAAe,EAAAqB,EAAAZ,CAAA,EAGPV,EAAA,IAAAY,KAAAA,EAAA,iBAAA1B,OAAkB,QACpC,gBACA,kBAAgB,mCAMEc,EAAA,IAAAc,KAAAA,EAAA,iBAAA5B,OAAkB,OACpC,gBACA,kBAAgB,mCAMEc,EAAA,IAAAgB,KAAAA,EAAA,iBAAA9B,OAAkB,SACpC,gBACA,kBAAgB,sGA4Bf;AAAA,oBAEW,mDAA6B,GAC9C,mBADiBK,EAA6BC,EAAA+B,EAAA7B,CAAA,sFAP9B,0EACyD,eAEtDR,EAAI,CAAA,CAAA,MACrB,uEACF,aAHQA,EAAI,CAAA,CAAA,wEAD6DK,EAGvEC,EAAAgC,EAAA9B,CAAA,mCADiBR,EAAI,CAAA,CAAA,kBADfA,EAAI,CAAA,CAAA,6DAhBRuC,EAAAvC,KAAG,iBAAiB,EAAA,iBAYpBwC,EAAAxC,KAAG,YAAY,EAAA,eAcfyC,EAAAzC,KAAG,sBAAsB,EAAA,4HA3D1B,IAAA0C,EAAA1C,OAAa,MAAI2C,GAAA3C,CAAA,4CAqCV4C,cACG,SACL5C,EAAc,CAAA,oBACVA,EAAoB,EAAA,CAAA,0BAO3BA,EAAW,CAAA,EAAA6C,mGA2BF,SACN7C,EAAU,CAAA,oBACNA,EAAgB,EAAA,CAAA,wEAId,SACNA,EAAgB,CAAA,oBACZA,EAAqB,EAAA,CAAA,0QAtBjB;AAAA;AAAA;AAAA,GAIhB,6BAAW;AAAA,mCACqB,gDAAsB;AAAA;AAAA;AAAA,GAGtD,kBAAK;AAAA,kCAC0B,iDAAqB;AAAA;AAAA,GAEpD,sHAqBUuB,EAAM;AAAA,+dA/FlBlB,EAEKC,EAAAK,EAAAH,CAAA,4CAiCLH,EAWKC,EAAAI,EAAAF,CAAA,EAVJC,EAA+BC,EAAAoC,CAAA,gBAC/BrC,EAQGC,EAAAqC,CAAA,uBAEJ1C,EAaKC,EAAA0C,EAAAxC,CAAA,EAZJC,EAA0BuC,EAAAC,CAAA,gBAC1BxC,EAUGuC,EAAAE,CAAA,uBAEJ7C,EAqCKC,EAAA6C,EAAA3C,CAAA,EApCJC,EAAuE0C,EAAAC,CAAA,iBAAtC3C,EAAkC2C,EAAAC,CAAA,UACnE5C,EAwBG0C,EAAAG,CAAA,UApBF7C,EAAM6C,EAAAC,EAAA,EAAA9C,EAAK6C,EAAAE,EAAA,UACqB/C,EAAsB6C,EAAAG,CAAA,UAGtDhD,EAAK6C,EAAAI,EAAA,UAC0BjD,EAAqB6C,EAAAK,EAAA,UAEpDlD,EAAM6C,EAAAM,EAAA,EAAAnD,EAAK6C,EAAAO,EAAA,kDAcZpD,EASQ0C,EAAAvC,CAAA,8FA9FJZ,OAAa,wEAiCZ,CAAAa,GAAAC,EAAA,MAAAyB,KAAAA,EAAAvC,KAAG,iBAAiB,EAAA,KAAAe,EAAA+C,EAAAvB,CAAA,8BAMhBvC,EAAc,CAAA,eAMlB,CAAAa,GAAAC,EAAA,MAAA0B,KAAAA,EAAAxC,KAAG,YAAY,EAAA,KAAAe,EAAAgD,EAAAvB,CAAA,oEAcf,CAAA3B,GAAAC,EAAA,MAAA2B,KAAAA,EAAAzC,KAAG,sBAAsB,EAAA,KAAAe,EAAAiD,EAAAvB,CAAA,6BAgBrBzC,EAAU,CAAA,yCAMVA,EAAgB,CAAA,mVA3Jd,GAAA,CAAA,KAAAiB,CAAA,EAAAC,EACA,CAAA,SAAA+C,CAAA,EAAA/C,EACA,CAAA,YAAAgD,CAAA,EAAAhD,QASLC,EAAWC,KACbH,IAAS,KACZA,EAAO,SAAS,SAAW,KAAO,SAAS,KAAO,SAAS,UAEvDA,EAAK,SAAS,GAAG,IACrBA,GAAQ,cAGAkD,EAASC,EAAA,OACXC,EAAU,IAAA,IAAI,OAAO,SAAS,IAAI,EACpCD,IAAU,UACbC,EAAI,aAAa,OAAO,SAAS,MACjCC,EAAgB,QAAA,IAEhBD,EAAI,aAAa,IAAI,UAAWD,CAAK,MACrCE,EAAgBF,CAAA,GAEjB,OAAO,SAAS,KAAOC,EAAI,SAAA,EAG5BE,GAAA,IAAA,CACC,SAAS,KAAK,MAAM,SAAW,SAC3B,iBAAkB,QACrB,OAAO,cAAc,SAAS,EAAG,CAAC,QAG7BH,EADU,IAAA,IAAI,OAAO,SAAS,IAAI,EACtB,aAAa,IAAI,SAAS,EAC5C,OAAAI,EAAA,EAAAF,EAAiBF,GAAyC,QAAA,OAEzD,SAAS,KAAK,MAAM,SAAW,UAI7B,IAAAK,EACAH,EAA6C,UACtC,WAAAI,EAAa,EAAA,EAAAxD,GACb,iBAAAyD,EAAmB,EAAA,EAAAzD,EAE9B0D,GAAO,UAAWC,GAAA,CACbA,OACHJ,EAAiBI,CAAA,aAIVC,EAAqBC,EAAA,CACvB,MAAAC,EAAaD,EAAE,OACrBE,GAAaD,CAAU,WAGfE,EAAiBH,EAAA,CACzBP,EAAA,EAAAE,EAAaK,EAAE,MAAA,WAGPI,EAAsBJ,EAAA,CAC9BP,EAAA,EAAAG,EAAmBI,EAAE,MAAA,EAGtB1D,qCAemB,MAAAC,EAAA,IAAA6C,EAAS,OAAO,EAQhBiB,EAAA,IAAAjB,EAAS,MAAM,EAQfkB,EAAA,IAAAlB,EAAS,QAAQ,SA+DlChD,EAAS,OAAO,EAChBA,EAAS,iBAAiB"}