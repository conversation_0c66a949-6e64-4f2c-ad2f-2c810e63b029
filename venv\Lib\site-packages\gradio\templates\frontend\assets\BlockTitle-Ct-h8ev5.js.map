{"version": 3, "file": "BlockTitle-Ct-h8ev5.js", "sources": ["../../../../js/atoms/src/BlockTitle.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { default as Info } from \"./Info.svelte\";\n\texport let show_label = true;\n\texport let info: string | undefined = undefined;\n\texport let rtl = false;\n</script>\n\n<span\n\tclass:sr-only={!show_label}\n\tclass:hide={!show_label}\n\tclass:has-info={info != null}\n\tdata-testid=\"block-info\"\n\tdir={rtl ? \"rtl\" : \"ltr\"}\n>\n\t<slot />\n</span>\n{#if info}\n\t<Info {info} />\n{/if}\n\n<style>\n\tspan.has-info {\n\t\tmargin-bottom: var(--spacing-xs);\n\t}\n\tspan:not(.has-info) {\n\t\tmargin-bottom: var(--spacing-lg);\n\t}\n\tspan {\n\t\tdisplay: inline-block;\n\t\tposition: relative;\n\t\tz-index: var(--layer-4);\n\t\tborder: solid var(--block-title-border-width)\n\t\t\tvar(--block-title-border-color);\n\t\tborder-radius: var(--block-title-radius);\n\t\tbackground: var(--block-title-background-fill);\n\t\tpadding: var(--block-title-padding);\n\t\tcolor: var(--block-title-text-color);\n\t\tfont-weight: var(--block-title-text-weight);\n\t\tfont-size: var(--block-title-text-size);\n\t\tline-height: var(--line-sm);\n\t}\n\n\tspan[dir=\"rtl\"] {\n\t\tdisplay: block;\n\t}\n\n\t.hide {\n\t\tmargin: 0;\n\t\theight: 0;\n\t}\n</style>\n"], "names": ["ctx", "create_if_block", "toggle_class", "span", "insert", "target", "anchor", "show_label", "$$props", "info", "rtl"], "mappings": "iwBAgBKA,EAAI,CAAA,GAAAC,EAAAD,CAAA,mGAJHA,EAAG,CAAA,EAAG,MAAQ,KAAK,8CAJRA,EAAU,CAAA,CAAA,cACbA,EAAU,CAAA,CAAA,EACPE,EAAAC,EAAA,WAAAH,MAAQ,IAAI,UAH7BI,EAQMC,EAAAF,EAAAG,CAAA,gJAHAN,EAAG,CAAA,EAAG,MAAQ,gDAJHA,EAAU,CAAA,CAAA,yBACbA,EAAU,CAAA,CAAA,aACPE,EAAAC,EAAA,WAAAH,MAAQ,IAAI,EAMxBA,EAAI,CAAA,4PAdG,WAAAO,EAAa,EAAA,EAAAC,GACb,KAAAC,EAA2B,MAAA,EAAAD,GAC3B,IAAAE,EAAM,EAAA,EAAAF"}