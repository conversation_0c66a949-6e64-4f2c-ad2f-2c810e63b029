{"version": 3, "mappings": ";wbAyIAA,EAAmCC,EAAAC,EAAAC,CAAA,2EAnI9BC,EAEO,OAAAC,CAAA,EAAAC,EACA,cAAAC,CAAA,EAAAD,EACA,aAAAE,CAAA,EAAAF,EACA,iBAAAG,CAAA,EAAAH,EACA,YAAAI,CAAA,EAAAJ,EACA,WAAAK,CAAA,EAAAL,GAKA,aAAAM,EAA2B,QAAAN,EAIlCO,EAsBAC,EACAC,EACAC,EACAC,EAAU,GAEdC,EAAA,MACO,UACLd,EAAA,MAAAe,EAAA,WAA8B,qBAAmB,wDACjDf,EAAe,sBAAsBU,EAAA,CACpC,WAAYN,EACZ,qBAAsB,GACtB,kBAAmB,GACnB,iBAAmB,QAAS,IAC5B,cAAgBY,GAAA,CACfJ,EAAgBI,KAEf,KAAMC,GAAA,CACRN,EAASM,OACTJ,EAAU,eAOXF,GAAA,MAAAA,EAAQ,aAMD,SAAAO,EAAiBC,EAAsBC,EAAA,CAC/CR,EAAc,MAAM,iBAAmBO,EACvCP,EAAc,MAAM,eAAiBQ,WAG7BC,EAAWC,GACfX,IACCW,EACHX,EACE,UAAUW,GACV,eACC,KACC,mBAAoB,IAItB,aACInB,IAAiB,cACpBe,EAAiB,GAAM,EAAK,EAClBf,IAAiB,YAC3Be,EAAiB,GAAO,EAAI,EAE5BK,EAAclB,EAAiBC,EAAYC,CAAS,IAIvDI,EAAO,cAKM,SAAAY,EACflB,EACAC,EACAC,GAEM,MAAAiB,EAASZ,EAAc,OACzBP,EAAgB,CAAC,IAAM,OAC1BmB,EAAO,MAASnB,EAAgB,CAAC,EAAI,KAAK,GAAM,KAE7CA,EAAgB,CAAC,IAAM,OAC1BmB,EAAO,KAAQnB,EAAgB,CAAC,EAAI,KAAK,GAAM,KAE5CA,EAAgB,CAAC,IAAM,OAC1BmB,EAAO,OAASnB,EAAgB,CAAC,GAElCmB,EAAO,iBAAmB,GACpB,MAAAC,EAAA,KACLD,EAAO,eAAiB,KAAOA,EAAO,OAASlB,GAC/CkB,EAAO,mBAAsB,IAAQjB,EAAaiB,EAAO,QAE1DC,IACAD,EAAO,6BAA6B,IAAIC,CAAuB,EAGhD,SAAAC,GAAA,CACXd,GACHD,EAAO,uDAKSD,EAAMiB,mVA1HxBC,EAAA,GAAGN,EAAMrB,EAAM,2BAcdO,EAAec,CAAA,EAEXA,GAAA,MACHb,EAAaa,CAAA,QACPO,EAAgBP,EACtBQ,EAAiBR,CAAG,EAAE,KAAMS,GAAA,CACvBtB,IAAeoB,EAClBD,EAAA,EAAApB,EAAeuB,GAAY,QAE3BA,GAAY,IAAI,gBAAgBA,CAAQ,qBAmCzClB,GAAWQ,EAAWb,CAAY", "names": ["insert_hydration", "target", "canvas_1", "anchor", "BABYLON_VIEWER", "value", "$$props", "display_mode", "clear_color", "camera_position", "zoom_speed", "pan_speed", "resolved_url", "latest_url", "canvas", "viewer", "viewerDetails", "mounted", "onMount", "__vitePreload", "details", "<PERSON><PERSON><PERSON><PERSON>", "setRenderingMode", "pointsCloud", "wireframe", "load_model", "url", "update_camera", "camera", "updateCameraSensibility", "reset_camera_position", "$$value", "$$invalidate", "resolving_url", "resolve_wasm_src", "resolved"], "ignoreList": [], "sources": ["../../../../../../../model3D/shared/Canvas3D.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { resolve_wasm_src } from \"@gradio/wasm/svelte\";\n\timport type { Viewer, ViewerDetails } from \"@babylonjs/viewer\";\n\n\tlet BABYLON_VIEWER: typeof import(\"@babylonjs/viewer\");\n\n\texport let value: FileData;\n\texport let display_mode: \"solid\" | \"point_cloud\" | \"wireframe\";\n\texport let clear_color: [number, number, number, number];\n\texport let camera_position: [number | null, number | null, number | null];\n\texport let zoom_speed: number;\n\texport let pan_speed: number;\n\n\t$: url = value.url;\n\n\t/* URL resolution for the Wasm mode. */\n\texport let resolved_url: typeof url = undefined; // Exposed to be bound to the download link in the parent component.\n\t// The prop can be updated before the Promise from `resolve_wasm_src` is resolved.\n\t// In such a case, the resolved url for the old `url` has to be discarded,\n\t// This variable `latest_url` is used to pick up only the value resolved for the latest `url`.\n\tlet latest_url: typeof url;\n\t$: {\n\t\t// In normal (non-Wasm) Gradio, the original `url` should be used immediately\n\t\t// without waiting for `resolve_wasm_src()` to resolve.\n\t\t// If it waits, a blank element is displayed until the async task finishes\n\t\t// and it leads to undesirable flickering.\n\t\t// So set `resolved_url` immediately above, and update it with the resolved values below later.\n\t\tresolved_url = url;\n\n\t\tif (url) {\n\t\t\tlatest_url = url;\n\t\t\tconst resolving_url = url;\n\t\t\tresolve_wasm_src(url).then((resolved) => {\n\t\t\t\tif (latest_url === resolving_url) {\n\t\t\t\t\tresolved_url = resolved ?? undefined;\n\t\t\t\t} else {\n\t\t\t\t\tresolved && URL.revokeObjectURL(resolved);\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t}\n\n\tlet canvas: HTMLCanvasElement;\n\tlet viewer: Viewer;\n\tlet viewerDetails: Readonly<ViewerDetails>;\n\tlet mounted = false;\n\n\tonMount(() => {\n\t\tconst initViewer = async (): Promise<void> => {\n\t\t\tBABYLON_VIEWER = await import(\"@babylonjs/viewer\");\n\t\t\tBABYLON_VIEWER.createViewerForCanvas(canvas, {\n\t\t\t\tclearColor: clear_color,\n\t\t\t\tuseRightHandedSystem: true,\n\t\t\t\tanimationAutoPlay: true,\n\t\t\t\tcameraAutoOrbit: { enabled: false },\n\t\t\t\tonInitialized: (details: any) => {\n\t\t\t\t\tviewerDetails = details;\n\t\t\t\t}\n\t\t\t}).then((promiseViewer: any) => {\n\t\t\t\tviewer = promiseViewer;\n\t\t\t\tmounted = true;\n\t\t\t});\n\t\t};\n\n\t\tinitViewer();\n\n\t\treturn () => {\n\t\t\tviewer?.dispose();\n\t\t};\n\t});\n\n\t$: mounted && load_model(resolved_url);\n\n\tfunction setRenderingMode(pointsCloud: boolean, wireframe: boolean): void {\n\t\tviewerDetails.scene.forcePointsCloud = pointsCloud;\n\t\tviewerDetails.scene.forceWireframe = wireframe;\n\t}\n\n\tfunction load_model(url: string | undefined): void {\n\t\tif (viewer) {\n\t\t\tif (url) {\n\t\t\t\tviewer\n\t\t\t\t\t.loadModel(url, {\n\t\t\t\t\t\tpluginOptions: {\n\t\t\t\t\t\t\tobj: {\n\t\t\t\t\t\t\t\timportVertexColors: true\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t.then(() => {\n\t\t\t\t\t\tif (display_mode === \"point_cloud\") {\n\t\t\t\t\t\t\tsetRenderingMode(true, false);\n\t\t\t\t\t\t} else if (display_mode === \"wireframe\") {\n\t\t\t\t\t\t\tsetRenderingMode(false, true);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tupdate_camera(camera_position, zoom_speed, pan_speed);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tviewer.resetModel();\n\t\t\t}\n\t\t}\n\t}\n\n\texport function update_camera(\n\t\tcamera_position: [number | null, number | null, number | null],\n\t\tzoom_speed: number,\n\t\tpan_speed: number\n\t): void {\n\t\tconst camera = viewerDetails.camera;\n\t\tif (camera_position[0] !== null) {\n\t\t\tcamera.alpha = (camera_position[0] * Math.PI) / 180;\n\t\t}\n\t\tif (camera_position[1] !== null) {\n\t\t\tcamera.beta = (camera_position[1] * Math.PI) / 180;\n\t\t}\n\t\tif (camera_position[2] !== null) {\n\t\t\tcamera.radius = camera_position[2];\n\t\t}\n\t\tcamera.lowerRadiusLimit = 0.1;\n\t\tconst updateCameraSensibility = (): void => {\n\t\t\tcamera.wheelPrecision = 250 / (camera.radius * zoom_speed);\n\t\t\tcamera.panningSensibility = (10000 * pan_speed) / camera.radius;\n\t\t};\n\t\tupdateCameraSensibility();\n\t\tcamera.onAfterCheckInputsObservable.add(updateCameraSensibility);\n\t}\n\n\texport function reset_camera_position(): void {\n\t\tif (viewerDetails) {\n\t\t\tviewer.resetCamera();\n\t\t}\n\t}\n</script>\n\n<canvas bind:this={canvas}></canvas>\n"], "file": "_app/immutable/chunks/Canvas3D.BkXXMcr1.js"}