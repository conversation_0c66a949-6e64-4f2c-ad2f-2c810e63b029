{"version": 3, "file": "init-Bz37DNdj.js", "sources": ["../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/app/ResizePlugin.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/app/TickerPlugin.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/filters/FilterPipe.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/container/bounds/getRenderableBounds.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/filters/FilterSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/mesh/shared/MeshGeometry.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text-html/utils/textStyleToCSS.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text-html/HTMLTextStyle.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text-html/HTMLTextRenderData.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text-html/utils/measureHtmlText.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/graphics/shared/GraphicsPipe.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/mesh-plane/PlaneGeometry.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/mesh/shared/BatchableMesh.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/mesh/shared/MeshPipe.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/particle-container/gl/GlParticleContainerAdaptor.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/particle-container/gpu/GpuParticleContainerAdaptor.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/particle-container/shared/utils/createIndicesForQuads.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/particle-container/shared/utils/generateParticleUpdateFunction.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/particle-container/shared/ParticleBuffer.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/particle-container/shared/shader/particles.frag.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/particle-container/shared/shader/particles.vert.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/particle-container/shared/shader/particles.wgsl.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/particle-container/shared/shader/ParticleShader.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/particle-container/shared/ParticleContainerPipe.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/particle-container/shared/GlParticleContainerPipe.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/particle-container/shared/GpuParticleContainerPipe.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/sprite-nine-slice/NineSliceGeometry.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/sprite-nine-slice/NineSliceSpritePipe.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/sprite-tiling/shader/tilingBit.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/sprite-tiling/shader/TilingSpriteShader.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/sprite-tiling/utils/QuadGeometry.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/sprite-tiling/utils/setPositions.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/sprite-tiling/utils/applyMatrix.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/sprite-tiling/utils/setUvs.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/sprite-tiling/TilingSpritePipe.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text/sdfShader/shader-bits/localUniformMSDFBit.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text/sdfShader/shader-bits/mSDFBit.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text/sdfShader/SdfShader.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text-bitmap/BitmapTextPipe.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text/utils/updateTextBounds.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text-html/HTMLTextPipe.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/utils/browser/isSafari.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text/utils/getPo2TextureFromSource.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text-html/utils/extractFontFamilies.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text-html/utils/loadFontAsBase64.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text-html/utils/loadFontCSS.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text-html/utils/getFontCss.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text-html/utils/getSVGUrl.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text-html/utils/getTemporaryCanvasFromImage.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text-html/utils/loadSVGImage.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text-html/HTMLTextSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text/canvas/CanvasTextPipe.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/utils/canvas/getCanvasBoundingBox.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text/canvas/CanvasTextSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/app/init.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/graphics/init.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/mesh/init.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/particle-container/init.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text/init.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text-bitmap/init.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/text-html/init.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/sprite-tiling/init.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/sprite-nine-slice/init.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/filters/init.mjs"], "sourcesContent": ["import { ExtensionType } from '../extensions/Extensions.mjs';\n\n\"use strict\";\nclass ResizePlugin {\n  /**\n   * Initialize the plugin with scope of application instance\n   * @static\n   * @private\n   * @param {object} [options] - See application options\n   */\n  static init(options) {\n    Object.defineProperty(\n      this,\n      \"resizeTo\",\n      /**\n       * The HTML element or window to automatically resize the\n       * renderer's view element to match width and height.\n       * @member {Window|HTMLElement}\n       * @name resizeTo\n       * @memberof app.Application#\n       */\n      {\n        set(dom) {\n          globalThis.removeEventListener(\"resize\", this.queueResize);\n          this._resizeTo = dom;\n          if (dom) {\n            globalThis.addEventListener(\"resize\", this.queueResize);\n            this.resize();\n          }\n        },\n        get() {\n          return this._resizeTo;\n        }\n      }\n    );\n    this.queueResize = () => {\n      if (!this._resizeTo) {\n        return;\n      }\n      this._cancelResize();\n      this._resizeId = requestAnimationFrame(() => this.resize());\n    };\n    this._cancelResize = () => {\n      if (this._resizeId) {\n        cancelAnimationFrame(this._resizeId);\n        this._resizeId = null;\n      }\n    };\n    this.resize = () => {\n      if (!this._resizeTo) {\n        return;\n      }\n      this._cancelResize();\n      let width;\n      let height;\n      if (this._resizeTo === globalThis.window) {\n        width = globalThis.innerWidth;\n        height = globalThis.innerHeight;\n      } else {\n        const { clientWidth, clientHeight } = this._resizeTo;\n        width = clientWidth;\n        height = clientHeight;\n      }\n      this.renderer.resize(width, height);\n      this.render();\n    };\n    this._resizeId = null;\n    this._resizeTo = null;\n    this.resizeTo = options.resizeTo || null;\n  }\n  /**\n   * Clean up the ticker, scoped to application\n   * @static\n   * @private\n   */\n  static destroy() {\n    globalThis.removeEventListener(\"resize\", this.queueResize);\n    this._cancelResize();\n    this._cancelResize = null;\n    this.queueResize = null;\n    this.resizeTo = null;\n    this.resize = null;\n  }\n}\n/** @ignore */\nResizePlugin.extension = ExtensionType.Application;\n\nexport { ResizePlugin };\n//# sourceMappingURL=ResizePlugin.mjs.map\n", "import { ExtensionType } from '../extensions/Extensions.mjs';\nimport { UPDATE_PRIORITY } from '../ticker/const.mjs';\nimport { Ticker } from '../ticker/Ticker.mjs';\n\n\"use strict\";\nclass TickerPlugin {\n  /**\n   * Initialize the plugin with scope of application instance\n   * @static\n   * @private\n   * @param {object} [options] - See application options\n   */\n  static init(options) {\n    options = Object.assign({\n      autoStart: true,\n      sharedTicker: false\n    }, options);\n    Object.defineProperty(\n      this,\n      \"ticker\",\n      {\n        set(ticker) {\n          if (this._ticker) {\n            this._ticker.remove(this.render, this);\n          }\n          this._ticker = ticker;\n          if (ticker) {\n            ticker.add(this.render, this, UPDATE_PRIORITY.LOW);\n          }\n        },\n        get() {\n          return this._ticker;\n        }\n      }\n    );\n    this.stop = () => {\n      this._ticker.stop();\n    };\n    this.start = () => {\n      this._ticker.start();\n    };\n    this._ticker = null;\n    this.ticker = options.sharedTicker ? Ticker.shared : new Ticker();\n    if (options.autoStart) {\n      this.start();\n    }\n  }\n  /**\n   * Clean up the ticker, scoped to application.\n   * @static\n   * @private\n   */\n  static destroy() {\n    if (this._ticker) {\n      const oldTicker = this._ticker;\n      this.ticker = null;\n      oldTicker.destroy();\n    }\n  }\n}\n/** @ignore */\nTickerPlugin.extension = ExtensionType.Application;\n\nexport { TickerPlugin };\n//# sourceMappingURL=TickerPlugin.mjs.map\n", "import { ExtensionType } from '../extensions/Extensions.mjs';\n\n\"use strict\";\nclass FilterPipe {\n  constructor(renderer) {\n    this._renderer = renderer;\n  }\n  push(filterEffect, container, instructionSet) {\n    const renderPipes = this._renderer.renderPipes;\n    renderPipes.batch.break(instructionSet);\n    instructionSet.add({\n      renderPipeId: \"filter\",\n      canBundle: false,\n      action: \"pushFilter\",\n      container,\n      filterEffect\n    });\n  }\n  pop(_filterEffect, _container, instructionSet) {\n    this._renderer.renderPipes.batch.break(instructionSet);\n    instructionSet.add({\n      renderPipeId: \"filter\",\n      action: \"popFilter\",\n      canBundle: false\n    });\n  }\n  execute(instruction) {\n    if (instruction.action === \"pushFilter\") {\n      this._renderer.filter.push(instruction);\n    } else if (instruction.action === \"popFilter\") {\n      this._renderer.filter.pop();\n    }\n  }\n  destroy() {\n    this._renderer = null;\n  }\n}\nFilterPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"filter\"\n};\n\nexport { FilterPipe };\n//# sourceMappingURL=FilterPipe.mjs.map\n", "\"use strict\";\nfunction getGlobalRenderableBounds(renderables, bounds) {\n  bounds.clear();\n  const tempMatrix = bounds.matrix;\n  for (let i = 0; i < renderables.length; i++) {\n    const renderable = renderables[i];\n    if (renderable.globalDisplayStatus < 7) {\n      continue;\n    }\n    bounds.matrix = renderable.worldTransform;\n    bounds.addBounds(renderable.bounds);\n  }\n  bounds.matrix = tempMatrix;\n  return bounds;\n}\n\nexport { getGlobalRenderableBounds };\n//# sourceMappingURL=getRenderableBounds.mjs.map\n", "import { ExtensionType } from '../extensions/Extensions.mjs';\nimport { Matrix } from '../maths/matrix/Matrix.mjs';\nimport { Point } from '../maths/point/Point.mjs';\nimport { BindGroup } from '../rendering/renderers/gpu/shader/BindGroup.mjs';\nimport { Geometry } from '../rendering/renderers/shared/geometry/Geometry.mjs';\nimport { UniformGroup } from '../rendering/renderers/shared/shader/UniformGroup.mjs';\nimport { Texture } from '../rendering/renderers/shared/texture/Texture.mjs';\nimport { TexturePool } from '../rendering/renderers/shared/texture/TexturePool.mjs';\nimport { RendererType } from '../rendering/renderers/types.mjs';\nimport { Bounds } from '../scene/container/bounds/Bounds.mjs';\nimport { getGlobalRenderableBounds } from '../scene/container/bounds/getRenderableBounds.mjs';\nimport { warn } from '../utils/logging/warn.mjs';\n\n\"use strict\";\nconst quadGeometry = new Geometry({\n  attributes: {\n    aPosition: {\n      buffer: new Float32Array([0, 0, 1, 0, 1, 1, 0, 1]),\n      format: \"float32x2\",\n      stride: 2 * 4,\n      offset: 0\n    }\n  },\n  indexBuffer: new Uint32Array([0, 1, 2, 0, 2, 3])\n});\nclass FilterSystem {\n  constructor(renderer) {\n    this._filterStackIndex = 0;\n    this._filterStack = [];\n    this._filterGlobalUniforms = new UniformGroup({\n      uInputSize: { value: new Float32Array(4), type: \"vec4<f32>\" },\n      uInputPixel: { value: new Float32Array(4), type: \"vec4<f32>\" },\n      uInputClamp: { value: new Float32Array(4), type: \"vec4<f32>\" },\n      uOutputFrame: { value: new Float32Array(4), type: \"vec4<f32>\" },\n      uGlobalFrame: { value: new Float32Array(4), type: \"vec4<f32>\" },\n      uOutputTexture: { value: new Float32Array(4), type: \"vec4<f32>\" }\n    });\n    this._globalFilterBindGroup = new BindGroup({});\n    this.renderer = renderer;\n  }\n  /**\n   * The back texture of the currently active filter. Requires the filter to have `blendRequired` set to true.\n   * @readonly\n   */\n  get activeBackTexture() {\n    return this._activeFilterData?.backTexture;\n  }\n  push(instruction) {\n    const renderer = this.renderer;\n    const filters = instruction.filterEffect.filters;\n    if (!this._filterStack[this._filterStackIndex]) {\n      this._filterStack[this._filterStackIndex] = this._getFilterData();\n    }\n    const filterData = this._filterStack[this._filterStackIndex];\n    this._filterStackIndex++;\n    if (filters.length === 0) {\n      filterData.skip = true;\n      return;\n    }\n    const bounds = filterData.bounds;\n    if (instruction.renderables) {\n      getGlobalRenderableBounds(instruction.renderables, bounds);\n    } else if (instruction.filterEffect.filterArea) {\n      bounds.clear();\n      bounds.addRect(instruction.filterEffect.filterArea);\n      bounds.applyMatrix(instruction.container.worldTransform);\n    } else {\n      instruction.container.getFastGlobalBounds(true, bounds);\n    }\n    if (instruction.container) {\n      const renderGroup = instruction.container.renderGroup || instruction.container.parentRenderGroup;\n      const filterFrameTransform = renderGroup.cacheToLocalTransform;\n      if (filterFrameTransform) {\n        bounds.applyMatrix(filterFrameTransform);\n      }\n    }\n    const colorTextureSource = renderer.renderTarget.renderTarget.colorTexture.source;\n    let resolution = Infinity;\n    let padding = 0;\n    let antialias = true;\n    let blendRequired = false;\n    let enabled = false;\n    let clipToViewport = true;\n    for (let i = 0; i < filters.length; i++) {\n      const filter = filters[i];\n      resolution = Math.min(resolution, filter.resolution === \"inherit\" ? colorTextureSource._resolution : filter.resolution);\n      padding += filter.padding;\n      if (filter.antialias === \"off\") {\n        antialias = false;\n      } else if (filter.antialias === \"inherit\") {\n        antialias && (antialias = colorTextureSource.antialias);\n      }\n      if (!filter.clipToViewport) {\n        clipToViewport = false;\n      }\n      const isCompatible = !!(filter.compatibleRenderers & renderer.type);\n      if (!isCompatible) {\n        enabled = false;\n        break;\n      }\n      if (filter.blendRequired && !(renderer.backBuffer?.useBackBuffer ?? true)) {\n        warn(\"Blend filter requires backBuffer on WebGL renderer to be enabled. Set `useBackBuffer: true` in the renderer options.\");\n        enabled = false;\n        break;\n      }\n      enabled = filter.enabled || enabled;\n      blendRequired || (blendRequired = filter.blendRequired);\n    }\n    if (!enabled) {\n      filterData.skip = true;\n      return;\n    }\n    if (clipToViewport) {\n      const viewPort = renderer.renderTarget.rootViewPort;\n      const rootResolution = renderer.renderTarget.renderTarget.resolution;\n      bounds.fitBounds(0, viewPort.width / rootResolution, 0, viewPort.height / rootResolution);\n    }\n    bounds.scale(resolution).ceil().scale(1 / resolution).pad(padding | 0);\n    if (!bounds.isPositive) {\n      filterData.skip = true;\n      return;\n    }\n    filterData.skip = false;\n    filterData.bounds = bounds;\n    filterData.blendRequired = blendRequired;\n    filterData.container = instruction.container;\n    filterData.filterEffect = instruction.filterEffect;\n    filterData.previousRenderSurface = renderer.renderTarget.renderSurface;\n    filterData.inputTexture = TexturePool.getOptimalTexture(\n      bounds.width,\n      bounds.height,\n      resolution,\n      antialias\n    );\n    renderer.renderTarget.bind(filterData.inputTexture, true);\n    renderer.globalUniforms.push({\n      offset: bounds\n    });\n  }\n  pop() {\n    const renderer = this.renderer;\n    this._filterStackIndex--;\n    const filterData = this._filterStack[this._filterStackIndex];\n    if (filterData.skip) {\n      return;\n    }\n    this._activeFilterData = filterData;\n    const inputTexture = filterData.inputTexture;\n    const bounds = filterData.bounds;\n    let backTexture = Texture.EMPTY;\n    renderer.renderTarget.finishRenderPass();\n    if (filterData.blendRequired) {\n      const previousBounds = this._filterStackIndex > 0 ? this._filterStack[this._filterStackIndex - 1].bounds : null;\n      const renderTarget = renderer.renderTarget.getRenderTarget(filterData.previousRenderSurface);\n      backTexture = this.getBackTexture(renderTarget, bounds, previousBounds);\n    }\n    filterData.backTexture = backTexture;\n    const filters = filterData.filterEffect.filters;\n    this._globalFilterBindGroup.setResource(inputTexture.source.style, 2);\n    this._globalFilterBindGroup.setResource(backTexture.source, 3);\n    renderer.globalUniforms.pop();\n    if (filters.length === 1) {\n      filters[0].apply(this, inputTexture, filterData.previousRenderSurface, false);\n      TexturePool.returnTexture(inputTexture);\n    } else {\n      let flip = filterData.inputTexture;\n      let flop = TexturePool.getOptimalTexture(\n        bounds.width,\n        bounds.height,\n        flip.source._resolution,\n        false\n      );\n      let i = 0;\n      for (i = 0; i < filters.length - 1; ++i) {\n        const filter = filters[i];\n        filter.apply(this, flip, flop, true);\n        const t = flip;\n        flip = flop;\n        flop = t;\n      }\n      filters[i].apply(this, flip, filterData.previousRenderSurface, false);\n      TexturePool.returnTexture(flip);\n      TexturePool.returnTexture(flop);\n    }\n    if (filterData.blendRequired) {\n      TexturePool.returnTexture(backTexture);\n    }\n  }\n  getBackTexture(lastRenderSurface, bounds, previousBounds) {\n    const backgroundResolution = lastRenderSurface.colorTexture.source._resolution;\n    const backTexture = TexturePool.getOptimalTexture(\n      bounds.width,\n      bounds.height,\n      backgroundResolution,\n      false\n    );\n    let x = bounds.minX;\n    let y = bounds.minY;\n    if (previousBounds) {\n      x -= previousBounds.minX;\n      y -= previousBounds.minY;\n    }\n    x = Math.floor(x * backgroundResolution);\n    y = Math.floor(y * backgroundResolution);\n    const width = Math.ceil(bounds.width * backgroundResolution);\n    const height = Math.ceil(bounds.height * backgroundResolution);\n    this.renderer.renderTarget.copyToTexture(\n      lastRenderSurface,\n      backTexture,\n      { x, y },\n      { width, height },\n      { x: 0, y: 0 }\n    );\n    return backTexture;\n  }\n  applyFilter(filter, input, output, clear) {\n    const renderer = this.renderer;\n    const filterData = this._filterStack[this._filterStackIndex];\n    const bounds = filterData.bounds;\n    const offset = Point.shared;\n    const previousRenderSurface = filterData.previousRenderSurface;\n    const isFinalTarget = previousRenderSurface === output;\n    let resolution = this.renderer.renderTarget.rootRenderTarget.colorTexture.source._resolution;\n    let currentIndex = this._filterStackIndex - 1;\n    while (currentIndex > 0 && this._filterStack[currentIndex].skip) {\n      --currentIndex;\n    }\n    if (currentIndex > 0) {\n      resolution = this._filterStack[currentIndex].inputTexture.source._resolution;\n    }\n    const filterUniforms = this._filterGlobalUniforms;\n    const uniforms = filterUniforms.uniforms;\n    const outputFrame = uniforms.uOutputFrame;\n    const inputSize = uniforms.uInputSize;\n    const inputPixel = uniforms.uInputPixel;\n    const inputClamp = uniforms.uInputClamp;\n    const globalFrame = uniforms.uGlobalFrame;\n    const outputTexture = uniforms.uOutputTexture;\n    if (isFinalTarget) {\n      let lastIndex = this._filterStackIndex;\n      while (lastIndex > 0) {\n        lastIndex--;\n        const filterData2 = this._filterStack[this._filterStackIndex - 1];\n        if (!filterData2.skip) {\n          offset.x = filterData2.bounds.minX;\n          offset.y = filterData2.bounds.minY;\n          break;\n        }\n      }\n      outputFrame[0] = bounds.minX - offset.x;\n      outputFrame[1] = bounds.minY - offset.y;\n    } else {\n      outputFrame[0] = 0;\n      outputFrame[1] = 0;\n    }\n    outputFrame[2] = input.frame.width;\n    outputFrame[3] = input.frame.height;\n    inputSize[0] = input.source.width;\n    inputSize[1] = input.source.height;\n    inputSize[2] = 1 / inputSize[0];\n    inputSize[3] = 1 / inputSize[1];\n    inputPixel[0] = input.source.pixelWidth;\n    inputPixel[1] = input.source.pixelHeight;\n    inputPixel[2] = 1 / inputPixel[0];\n    inputPixel[3] = 1 / inputPixel[1];\n    inputClamp[0] = 0.5 * inputPixel[2];\n    inputClamp[1] = 0.5 * inputPixel[3];\n    inputClamp[2] = input.frame.width * inputSize[2] - 0.5 * inputPixel[2];\n    inputClamp[3] = input.frame.height * inputSize[3] - 0.5 * inputPixel[3];\n    const rootTexture = this.renderer.renderTarget.rootRenderTarget.colorTexture;\n    globalFrame[0] = offset.x * resolution;\n    globalFrame[1] = offset.y * resolution;\n    globalFrame[2] = rootTexture.source.width * resolution;\n    globalFrame[3] = rootTexture.source.height * resolution;\n    const renderTarget = this.renderer.renderTarget.getRenderTarget(output);\n    renderer.renderTarget.bind(output, !!clear);\n    if (output instanceof Texture) {\n      outputTexture[0] = output.frame.width;\n      outputTexture[1] = output.frame.height;\n    } else {\n      outputTexture[0] = renderTarget.width;\n      outputTexture[1] = renderTarget.height;\n    }\n    outputTexture[2] = renderTarget.isRoot ? -1 : 1;\n    filterUniforms.update();\n    if (renderer.renderPipes.uniformBatch) {\n      const batchUniforms = renderer.renderPipes.uniformBatch.getUboResource(filterUniforms);\n      this._globalFilterBindGroup.setResource(batchUniforms, 0);\n    } else {\n      this._globalFilterBindGroup.setResource(filterUniforms, 0);\n    }\n    this._globalFilterBindGroup.setResource(input.source, 1);\n    this._globalFilterBindGroup.setResource(input.source.style, 2);\n    filter.groups[0] = this._globalFilterBindGroup;\n    renderer.encoder.draw({\n      geometry: quadGeometry,\n      shader: filter,\n      state: filter._state,\n      topology: \"triangle-list\"\n    });\n    if (renderer.type === RendererType.WEBGL) {\n      renderer.renderTarget.finishRenderPass();\n    }\n  }\n  _getFilterData() {\n    return {\n      skip: false,\n      inputTexture: null,\n      bounds: new Bounds(),\n      container: null,\n      filterEffect: null,\n      blendRequired: false,\n      previousRenderSurface: null\n    };\n  }\n  /**\n   * Multiply _input normalized coordinates_ to this matrix to get _sprite texture normalized coordinates_.\n   *\n   * Use `outputMatrix * vTextureCoord` in the shader.\n   * @param outputMatrix - The matrix to output to.\n   * @param {Sprite} sprite - The sprite to map to.\n   * @returns The mapped matrix.\n   */\n  calculateSpriteMatrix(outputMatrix, sprite) {\n    const data = this._activeFilterData;\n    const mappedMatrix = outputMatrix.set(\n      data.inputTexture._source.width,\n      0,\n      0,\n      data.inputTexture._source.height,\n      data.bounds.minX,\n      data.bounds.minY\n    );\n    const worldTransform = sprite.worldTransform.copyTo(Matrix.shared);\n    const renderGroup = sprite.renderGroup || sprite.parentRenderGroup;\n    if (renderGroup && renderGroup.cacheToLocalTransform) {\n      worldTransform.prepend(renderGroup.cacheToLocalTransform);\n    }\n    worldTransform.invert();\n    mappedMatrix.prepend(worldTransform);\n    mappedMatrix.scale(\n      1 / sprite.texture.frame.width,\n      1 / sprite.texture.frame.height\n    );\n    mappedMatrix.translate(sprite.anchor.x, sprite.anchor.y);\n    return mappedMatrix;\n  }\n}\n/** @ignore */\nFilterSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"filter\"\n};\n\nexport { FilterSystem };\n//# sourceMappingURL=FilterSystem.mjs.map\n", "import { Buffer } from '../../../rendering/renderers/shared/buffer/Buffer.mjs';\nimport { BufferUsage } from '../../../rendering/renderers/shared/buffer/const.mjs';\nimport { Geometry } from '../../../rendering/renderers/shared/geometry/Geometry.mjs';\nimport { deprecation, v8_0_0 } from '../../../utils/logging/deprecation.mjs';\n\n\"use strict\";\nconst _MeshGeometry = class _MeshGeometry extends Geometry {\n  constructor(...args) {\n    let options = args[0] ?? {};\n    if (options instanceof Float32Array) {\n      deprecation(v8_0_0, \"use new MeshGeometry({ positions, uvs, indices }) instead\");\n      options = {\n        positions: options,\n        uvs: args[1],\n        indices: args[2]\n      };\n    }\n    options = { ..._MeshGeometry.defaultOptions, ...options };\n    const positions = options.positions || new Float32Array([0, 0, 1, 0, 1, 1, 0, 1]);\n    let uvs = options.uvs;\n    if (!uvs) {\n      if (options.positions) {\n        uvs = new Float32Array(positions.length);\n      } else {\n        uvs = new Float32Array([0, 0, 1, 0, 1, 1, 0, 1]);\n      }\n    }\n    const indices = options.indices || new Uint32Array([0, 1, 2, 0, 2, 3]);\n    const shrinkToFit = options.shrinkBuffersToFit;\n    const positionBuffer = new Buffer({\n      data: positions,\n      label: \"attribute-mesh-positions\",\n      shrinkToFit,\n      usage: BufferUsage.VERTEX | BufferUsage.COPY_DST\n    });\n    const uvBuffer = new Buffer({\n      data: uvs,\n      label: \"attribute-mesh-uvs\",\n      shrinkToFit,\n      usage: BufferUsage.VERTEX | BufferUsage.COPY_DST\n    });\n    const indexBuffer = new Buffer({\n      data: indices,\n      label: \"index-mesh-buffer\",\n      shrinkToFit,\n      usage: BufferUsage.INDEX | BufferUsage.COPY_DST\n    });\n    super({\n      attributes: {\n        aPosition: {\n          buffer: positionBuffer,\n          format: \"float32x2\",\n          stride: 2 * 4,\n          offset: 0\n        },\n        aUV: {\n          buffer: uvBuffer,\n          format: \"float32x2\",\n          stride: 2 * 4,\n          offset: 0\n        }\n      },\n      indexBuffer,\n      topology: options.topology\n    });\n    this.batchMode = \"auto\";\n  }\n  /** The positions of the mesh. */\n  get positions() {\n    return this.attributes.aPosition.buffer.data;\n  }\n  /**\n   * Set the positions of the mesh.\n   * When setting the positions, its important that the uvs array is at least as long as the positions array.\n   * otherwise the geometry will not be valid.\n   * @param {Float32Array} value - The positions of the mesh.\n   */\n  set positions(value) {\n    this.attributes.aPosition.buffer.data = value;\n  }\n  /** The UVs of the mesh. */\n  get uvs() {\n    return this.attributes.aUV.buffer.data;\n  }\n  /**\n   * Set the UVs of the mesh.\n   * Its important that the uvs array you set is at least as long as the positions array.\n   * otherwise the geometry will not be valid.\n   * @param {Float32Array} value - The UVs of the mesh.\n   */\n  set uvs(value) {\n    this.attributes.aUV.buffer.data = value;\n  }\n  /** The indices of the mesh. */\n  get indices() {\n    return this.indexBuffer.data;\n  }\n  set indices(value) {\n    this.indexBuffer.data = value;\n  }\n};\n_MeshGeometry.defaultOptions = {\n  topology: \"triangle-list\",\n  shrinkBuffersToFit: false\n};\nlet MeshGeometry = _MeshGeometry;\n\nexport { MeshGeometry };\n//# sourceMappingURL=MeshGeometry.mjs.map\n", "import { Color } from '../../../color/Color.mjs';\n\n\"use strict\";\nfunction textStyleToCSS(style) {\n  const stroke = style._stroke;\n  const fill = style._fill;\n  const cssStyleString = [\n    `color: ${Color.shared.setValue(fill.color).toHex()}`,\n    `font-size: ${style.fontSize}px`,\n    `font-family: ${style.fontFamily}`,\n    `font-weight: ${style.fontWeight}`,\n    `font-style: ${style.fontStyle}`,\n    `font-variant: ${style.fontVariant}`,\n    `letter-spacing: ${style.letterSpacing}px`,\n    `text-align: ${style.align}`,\n    `padding: ${style.padding}px`,\n    `white-space: ${style.whiteSpace === \"pre\" && style.wordWrap ? \"pre-wrap\" : style.whiteSpace}`,\n    ...style.lineHeight ? [`line-height: ${style.lineHeight}px`] : [],\n    ...style.wordWrap ? [\n      `word-wrap: ${style.breakWords ? \"break-all\" : \"break-word\"}`,\n      `max-width: ${style.wordWrapWidth}px`\n    ] : [],\n    ...stroke ? [strokeToCSS(stroke)] : [],\n    ...style.dropShadow ? [dropShadowToCSS(style.dropShadow)] : [],\n    ...style.cssOverrides\n  ].join(\";\");\n  const cssStyles = [`div { ${cssStyleString} }`];\n  tagStyleToCSS(style.tagStyles, cssStyles);\n  return cssStyles.join(\" \");\n}\nfunction dropShadowToCSS(dropShadowStyle) {\n  const color = Color.shared.setValue(dropShadowStyle.color).setAlpha(dropShadowStyle.alpha).toHexa();\n  const x = Math.round(Math.cos(dropShadowStyle.angle) * dropShadowStyle.distance);\n  const y = Math.round(Math.sin(dropShadowStyle.angle) * dropShadowStyle.distance);\n  const position = `${x}px ${y}px`;\n  if (dropShadowStyle.blur > 0) {\n    return `text-shadow: ${position} ${dropShadowStyle.blur}px ${color}`;\n  }\n  return `text-shadow: ${position} ${color}`;\n}\nfunction strokeToCSS(stroke) {\n  return [\n    `-webkit-text-stroke-width: ${stroke.width}px`,\n    `-webkit-text-stroke-color: ${Color.shared.setValue(stroke.color).toHex()}`,\n    `text-stroke-width: ${stroke.width}px`,\n    `text-stroke-color: ${Color.shared.setValue(stroke.color).toHex()}`,\n    \"paint-order: stroke\"\n  ].join(\";\");\n}\nconst templates = {\n  fontSize: `font-size: {{VALUE}}px`,\n  fontFamily: `font-family: {{VALUE}}`,\n  fontWeight: `font-weight: {{VALUE}}`,\n  fontStyle: `font-style: {{VALUE}}`,\n  fontVariant: `font-variant: {{VALUE}}`,\n  letterSpacing: `letter-spacing: {{VALUE}}px`,\n  align: `text-align: {{VALUE}}`,\n  padding: `padding: {{VALUE}}px`,\n  whiteSpace: `white-space: {{VALUE}}`,\n  lineHeight: `line-height: {{VALUE}}px`,\n  wordWrapWidth: `max-width: {{VALUE}}px`\n};\nconst transform = {\n  fill: (value) => `color: ${Color.shared.setValue(value).toHex()}`,\n  breakWords: (value) => `word-wrap: ${value ? \"break-all\" : \"break-word\"}`,\n  stroke: strokeToCSS,\n  dropShadow: dropShadowToCSS\n};\nfunction tagStyleToCSS(tagStyles, out) {\n  for (const i in tagStyles) {\n    const tagStyle = tagStyles[i];\n    const cssTagStyle = [];\n    for (const j in tagStyle) {\n      if (transform[j]) {\n        cssTagStyle.push(transform[j](tagStyle[j]));\n      } else if (templates[j]) {\n        cssTagStyle.push(templates[j].replace(\"{{VALUE}}\", tagStyle[j]));\n      }\n    }\n    out.push(`${i} { ${cssTagStyle.join(\";\")} }`);\n  }\n}\n\nexport { textStyleToCSS };\n//# sourceMappingURL=textStyleToCSS.mjs.map\n", "import { warn } from '../../utils/logging/warn.mjs';\nimport { TextStyle } from '../text/TextStyle.mjs';\nimport { generateTextStyleKey } from '../text/utils/generateTextStyleKey.mjs';\nimport { textStyleToCSS } from './utils/textStyleToCSS.mjs';\n\n\"use strict\";\nclass HTMLTextStyle extends TextStyle {\n  constructor(options = {}) {\n    super(options);\n    this._cssOverrides = [];\n    this.cssOverrides ?? (this.cssOverrides = options.cssOverrides);\n    this.tagStyles = options.tagStyles ?? {};\n  }\n  /** List of style overrides that will be applied to the HTML text. */\n  set cssOverrides(value) {\n    this._cssOverrides = value instanceof Array ? value : [value];\n    this.update();\n  }\n  get cssOverrides() {\n    return this._cssOverrides;\n  }\n  _generateKey() {\n    this._styleKey = generateTextStyleKey(this) + this._cssOverrides.join(\"-\");\n    return this._styleKey;\n  }\n  update() {\n    this._cssStyle = null;\n    super.update();\n  }\n  /**\n   * Creates a new HTMLTextStyle object with the same values as this one.\n   * @returns New cloned HTMLTextStyle object\n   */\n  clone() {\n    return new HTMLTextStyle({\n      align: this.align,\n      breakWords: this.breakWords,\n      dropShadow: this.dropShadow ? { ...this.dropShadow } : null,\n      fill: this._fill,\n      fontFamily: this.fontFamily,\n      fontSize: this.fontSize,\n      fontStyle: this.fontStyle,\n      fontVariant: this.fontVariant,\n      fontWeight: this.fontWeight,\n      letterSpacing: this.letterSpacing,\n      lineHeight: this.lineHeight,\n      padding: this.padding,\n      stroke: this._stroke,\n      whiteSpace: this.whiteSpace,\n      wordWrap: this.wordWrap,\n      wordWrapWidth: this.wordWrapWidth,\n      cssOverrides: this.cssOverrides\n    });\n  }\n  get cssStyle() {\n    if (!this._cssStyle) {\n      this._cssStyle = textStyleToCSS(this);\n    }\n    return this._cssStyle;\n  }\n  /**\n   * Add a style override, this can be any CSS property\n   * it will override any built-in style. This is the\n   * property and the value as a string (e.g., `color: red`).\n   * This will override any other internal style.\n   * @param {string} value - CSS style(s) to add.\n   * @example\n   * style.addOverride('background-color: red');\n   */\n  addOverride(...value) {\n    const toAdd = value.filter((v) => !this.cssOverrides.includes(v));\n    if (toAdd.length > 0) {\n      this.cssOverrides.push(...toAdd);\n      this.update();\n    }\n  }\n  /**\n   * Remove any overrides that match the value.\n   * @param {string} value - CSS style to remove.\n   * @example\n   * style.removeOverride('background-color: red');\n   */\n  removeOverride(...value) {\n    const toRemove = value.filter((v) => this.cssOverrides.includes(v));\n    if (toRemove.length > 0) {\n      this.cssOverrides = this.cssOverrides.filter((v) => !toRemove.includes(v));\n      this.update();\n    }\n  }\n  set fill(value) {\n    if (typeof value !== \"string\" && typeof value !== \"number\") {\n      warn(\"[HTMLTextStyle] only color fill is not supported by HTMLText\");\n    }\n    super.fill = value;\n  }\n  set stroke(value) {\n    if (value && typeof value !== \"string\" && typeof value !== \"number\") {\n      warn(\"[HTMLTextStyle] only color stroke is not supported by HTMLText\");\n    }\n    super.stroke = value;\n  }\n}\n\nexport { HTMLTextStyle };\n//# sourceMappingURL=HTMLTextStyle.mjs.map\n", "\"use strict\";\nconst nssvg = \"http://www.w3.org/2000/svg\";\nconst nsxhtml = \"http://www.w3.org/1999/xhtml\";\nclass HTMLTextRenderData {\n  constructor() {\n    this.svgRoot = document.createElementNS(nssvg, \"svg\");\n    this.foreignObject = document.createElementNS(nssvg, \"foreignObject\");\n    this.domElement = document.createElementNS(nsxhtml, \"div\");\n    this.styleElement = document.createElementNS(nsxhtml, \"style\");\n    this.image = new Image();\n    const { foreignObject, svgRoot, styleElement, domElement } = this;\n    foreignObject.setAttribute(\"width\", \"10000\");\n    foreignObject.setAttribute(\"height\", \"10000\");\n    foreignObject.style.overflow = \"hidden\";\n    svgRoot.appendChild(foreignObject);\n    foreignObject.appendChild(styleElement);\n    foreignObject.appendChild(domElement);\n  }\n}\n\nexport { HTMLTextRenderData, nssvg, nsxhtml };\n//# sourceMappingURL=HTMLTextRenderData.mjs.map\n", "import { HTMLTextRenderData } from '../HTMLTextRenderData.mjs';\n\n\"use strict\";\nlet tempHTMLTextRenderData;\nfunction measureHtmlText(text, style, fontStyleCSS, htmlTextRenderData) {\n  htmlTextRenderData || (htmlTextRenderData = tempHTMLTextRenderData || (tempHTMLTextRenderData = new HTMLTextRenderData()));\n  const { domElement, styleElement, svgRoot } = htmlTextRenderData;\n  domElement.innerHTML = `<style>${style.cssStyle};</style><div style='padding:0'>${text}</div>`;\n  domElement.setAttribute(\"style\", \"transform-origin: top left; display: inline-block\");\n  if (fontStyleCSS) {\n    styleElement.textContent = fontStyleCSS;\n  }\n  document.body.appendChild(svgRoot);\n  const contentBounds = domElement.getBoundingClientRect();\n  svgRoot.remove();\n  const doublePadding = style.padding * 2;\n  return {\n    width: contentBounds.width - doublePadding,\n    height: contentBounds.height - doublePadding\n  };\n}\n\nexport { measureHtmlText };\n//# sourceMappingURL=measureHtmlText.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { State } from '../../../rendering/renderers/shared/state/State.mjs';\nimport { BigPool } from '../../../utils/pool/PoolGroup.mjs';\nimport { color32BitToUniform } from '../gpu/colorToUniform.mjs';\nimport { BatchableGraphics } from './BatchableGraphics.mjs';\n\n\"use strict\";\nclass GraphicsPipe {\n  constructor(renderer, adaptor) {\n    this.state = State.for2d();\n    // batchable graphics list, used to render batches\n    this._graphicsBatchesHash = /* @__PURE__ */ Object.create(null);\n    this._destroyRenderableBound = this.destroyRenderable.bind(this);\n    this.renderer = renderer;\n    this._adaptor = adaptor;\n    this._adaptor.init();\n    this.renderer.renderableGC.addManagedHash(this, \"_graphicsBatchesHash\");\n  }\n  validateRenderable(graphics) {\n    const context = graphics.context;\n    const wasBatched = !!this._graphicsBatchesHash[graphics.uid];\n    const gpuContext = this.renderer.graphicsContext.updateGpuContext(context);\n    if (gpuContext.isBatchable || wasBatched !== gpuContext.isBatchable) {\n      return true;\n    }\n    return false;\n  }\n  addRenderable(graphics, instructionSet) {\n    const gpuContext = this.renderer.graphicsContext.updateGpuContext(graphics.context);\n    if (graphics.didViewUpdate) {\n      this._rebuild(graphics);\n    }\n    if (gpuContext.isBatchable) {\n      this._addToBatcher(graphics, instructionSet);\n    } else {\n      this.renderer.renderPipes.batch.break(instructionSet);\n      instructionSet.add(graphics);\n    }\n  }\n  updateRenderable(graphics) {\n    const batches = this._graphicsBatchesHash[graphics.uid];\n    if (batches) {\n      for (let i = 0; i < batches.length; i++) {\n        const batch = batches[i];\n        batch._batcher.updateElement(batch);\n      }\n    }\n  }\n  destroyRenderable(graphics) {\n    if (this._graphicsBatchesHash[graphics.uid]) {\n      this._removeBatchForRenderable(graphics.uid);\n    }\n    graphics.off(\"destroyed\", this._destroyRenderableBound);\n  }\n  execute(graphics) {\n    if (!graphics.isRenderable)\n      return;\n    const renderer = this.renderer;\n    const context = graphics.context;\n    const contextSystem = renderer.graphicsContext;\n    if (!contextSystem.getGpuContext(context).batches.length) {\n      return;\n    }\n    const shader = context.customShader || this._adaptor.shader;\n    this.state.blendMode = graphics.groupBlendMode;\n    const localUniforms = shader.resources.localUniforms.uniforms;\n    localUniforms.uTransformMatrix = graphics.groupTransform;\n    localUniforms.uRound = renderer._roundPixels | graphics._roundPixels;\n    color32BitToUniform(\n      graphics.groupColorAlpha,\n      localUniforms.uColor,\n      0\n    );\n    this._adaptor.execute(this, graphics);\n  }\n  _rebuild(graphics) {\n    const wasBatched = !!this._graphicsBatchesHash[graphics.uid];\n    const gpuContext = this.renderer.graphicsContext.updateGpuContext(graphics.context);\n    if (wasBatched) {\n      this._removeBatchForRenderable(graphics.uid);\n    }\n    if (gpuContext.isBatchable) {\n      this._initBatchesForRenderable(graphics);\n    }\n    graphics.batched = gpuContext.isBatchable;\n  }\n  _addToBatcher(graphics, instructionSet) {\n    const batchPipe = this.renderer.renderPipes.batch;\n    const batches = this._getBatchesForRenderable(graphics);\n    for (let i = 0; i < batches.length; i++) {\n      const batch = batches[i];\n      batchPipe.addToBatch(batch, instructionSet);\n    }\n  }\n  _getBatchesForRenderable(graphics) {\n    return this._graphicsBatchesHash[graphics.uid] || this._initBatchesForRenderable(graphics);\n  }\n  _initBatchesForRenderable(graphics) {\n    const context = graphics.context;\n    const gpuContext = this.renderer.graphicsContext.getGpuContext(context);\n    const roundPixels = this.renderer._roundPixels | graphics._roundPixels;\n    const batches = gpuContext.batches.map((batch) => {\n      const batchClone = BigPool.get(BatchableGraphics);\n      batch.copyTo(batchClone);\n      batchClone.renderable = graphics;\n      batchClone.roundPixels = roundPixels;\n      return batchClone;\n    });\n    if (this._graphicsBatchesHash[graphics.uid] === void 0) {\n      graphics.on(\"destroyed\", this._destroyRenderableBound);\n    }\n    this._graphicsBatchesHash[graphics.uid] = batches;\n    return batches;\n  }\n  _removeBatchForRenderable(graphicsUid) {\n    this._graphicsBatchesHash[graphicsUid].forEach((batch) => {\n      BigPool.return(batch);\n    });\n    this._graphicsBatchesHash[graphicsUid] = null;\n  }\n  destroy() {\n    this.renderer = null;\n    this._adaptor.destroy();\n    this._adaptor = null;\n    this.state = null;\n    for (const i in this._graphicsBatchesHash) {\n      this._removeBatchForRenderable(i);\n    }\n    this._graphicsBatchesHash = null;\n  }\n}\n/** @ignore */\nGraphicsPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"graphics\"\n};\n\nexport { GraphicsPipe };\n//# sourceMappingURL=GraphicsPipe.mjs.map\n", "import { deprecation, v8_0_0 } from '../../utils/logging/deprecation.mjs';\nimport { MeshGeometry } from '../mesh/shared/MeshGeometry.mjs';\n\n\"use strict\";\nconst _PlaneGeometry = class _PlaneGeometry extends MeshGeometry {\n  constructor(...args) {\n    super({});\n    let options = args[0] ?? {};\n    if (typeof options === \"number\") {\n      deprecation(v8_0_0, \"PlaneGeometry constructor changed please use { width, height, verticesX, verticesY } instead\");\n      options = {\n        width: options,\n        height: args[1],\n        verticesX: args[2],\n        verticesY: args[3]\n      };\n    }\n    this.build(options);\n  }\n  /**\n   * Refreshes plane coordinates\n   * @param options - Options to be applied to plane geometry\n   */\n  build(options) {\n    options = { ..._PlaneGeometry.defaultOptions, ...options };\n    this.verticesX = this.verticesX ?? options.verticesX;\n    this.verticesY = this.verticesY ?? options.verticesY;\n    this.width = this.width ?? options.width;\n    this.height = this.height ?? options.height;\n    const total = this.verticesX * this.verticesY;\n    const verts = [];\n    const uvs = [];\n    const indices = [];\n    const verticesX = this.verticesX - 1;\n    const verticesY = this.verticesY - 1;\n    const sizeX = this.width / verticesX;\n    const sizeY = this.height / verticesY;\n    for (let i = 0; i < total; i++) {\n      const x = i % this.verticesX;\n      const y = i / this.verticesX | 0;\n      verts.push(x * sizeX, y * sizeY);\n      uvs.push(x / verticesX, y / verticesY);\n    }\n    const totalSub = verticesX * verticesY;\n    for (let i = 0; i < totalSub; i++) {\n      const xpos = i % verticesX;\n      const ypos = i / verticesX | 0;\n      const value = ypos * this.verticesX + xpos;\n      const value2 = ypos * this.verticesX + xpos + 1;\n      const value3 = (ypos + 1) * this.verticesX + xpos;\n      const value4 = (ypos + 1) * this.verticesX + xpos + 1;\n      indices.push(\n        value,\n        value2,\n        value3,\n        value2,\n        value4,\n        value3\n      );\n    }\n    this.buffers[0].data = new Float32Array(verts);\n    this.buffers[1].data = new Float32Array(uvs);\n    this.indexBuffer.data = new Uint32Array(indices);\n    this.buffers[0].update();\n    this.buffers[1].update();\n    this.indexBuffer.update();\n  }\n};\n_PlaneGeometry.defaultOptions = {\n  width: 100,\n  height: 100,\n  verticesX: 10,\n  verticesY: 10\n};\nlet PlaneGeometry = _PlaneGeometry;\n\nexport { PlaneGeometry };\n//# sourceMappingURL=PlaneGeometry.mjs.map\n", "\"use strict\";\nclass BatchableMesh {\n  constructor() {\n    this.batcherName = \"default\";\n    this.packAsQuad = false;\n    this.indexOffset = 0;\n    this.attributeOffset = 0;\n    this.roundPixels = 0;\n    this._batcher = null;\n    this._batch = null;\n    this._uvUpdateId = -1;\n    this._textureMatrixUpdateId = -1;\n  }\n  get blendMode() {\n    return this.renderable.groupBlendMode;\n  }\n  get topology() {\n    return this._topology || this.geometry.topology;\n  }\n  set topology(value) {\n    this._topology = value;\n  }\n  reset() {\n    this.renderable = null;\n    this.texture = null;\n    this._batcher = null;\n    this._batch = null;\n    this.geometry = null;\n    this._uvUpdateId = -1;\n    this._textureMatrixUpdateId = -1;\n  }\n  /**\n   * Sets the texture for the batchable mesh.\n   * As it does so, it resets the texture matrix update ID.\n   * this is to ensure that the texture matrix is recalculated when the uvs are referenced\n   * @param value - The texture to set.\n   */\n  setTexture(value) {\n    if (this.texture === value)\n      return;\n    this.texture = value;\n    this._textureMatrixUpdateId = -1;\n  }\n  get uvs() {\n    const geometry = this.geometry;\n    const uvBuffer = geometry.getBuffer(\"aUV\");\n    const uvs = uvBuffer.data;\n    let transformedUvs = uvs;\n    const textureMatrix = this.texture.textureMatrix;\n    if (!textureMatrix.isSimple) {\n      transformedUvs = this._transformedUvs;\n      if (this._textureMatrixUpdateId !== textureMatrix._updateID || this._uvUpdateId !== uvBuffer._updateID) {\n        if (!transformedUvs || transformedUvs.length < uvs.length) {\n          transformedUvs = this._transformedUvs = new Float32Array(uvs.length);\n        }\n        this._textureMatrixUpdateId = textureMatrix._updateID;\n        this._uvUpdateId = uvBuffer._updateID;\n        textureMatrix.multiplyUvs(uvs, transformedUvs);\n      }\n    }\n    return transformedUvs;\n  }\n  get positions() {\n    return this.geometry.positions;\n  }\n  get indices() {\n    return this.geometry.indices;\n  }\n  get color() {\n    return this.renderable.groupColorAlpha;\n  }\n  get groupTransform() {\n    return this.renderable.groupTransform;\n  }\n  get attributeSize() {\n    return this.geometry.positions.length / 2;\n  }\n  get indexSize() {\n    return this.geometry.indices.length;\n  }\n}\n\nexport { BatchableMesh };\n//# sourceMappingURL=BatchableMesh.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { Matrix } from '../../../maths/matrix/Matrix.mjs';\nimport { BindGroup } from '../../../rendering/renderers/gpu/shader/BindGroup.mjs';\nimport { UniformGroup } from '../../../rendering/renderers/shared/shader/UniformGroup.mjs';\nimport { getAdjustedBlendModeBlend } from '../../../rendering/renderers/shared/state/getAdjustedBlendModeBlend.mjs';\nimport { BigPool } from '../../../utils/pool/PoolGroup.mjs';\nimport { color32BitToUniform } from '../../graphics/gpu/colorToUniform.mjs';\nimport { BatchableMesh } from './BatchableMesh.mjs';\n\n\"use strict\";\nclass MeshPipe {\n  constructor(renderer, adaptor) {\n    this.localUniforms = new UniformGroup({\n      uTransformMatrix: { value: new Matrix(), type: \"mat3x3<f32>\" },\n      uColor: { value: new Float32Array([1, 1, 1, 1]), type: \"vec4<f32>\" },\n      uRound: { value: 0, type: \"f32\" }\n    });\n    this.localUniformsBindGroup = new BindGroup({\n      0: this.localUniforms\n    });\n    this._meshDataHash = /* @__PURE__ */ Object.create(null);\n    this._gpuBatchableMeshHash = /* @__PURE__ */ Object.create(null);\n    this._destroyRenderableBound = this.destroyRenderable.bind(this);\n    this.renderer = renderer;\n    this._adaptor = adaptor;\n    this._adaptor.init();\n    renderer.renderableGC.addManagedHash(this, \"_gpuBatchableMeshHash\");\n    renderer.renderableGC.addManagedHash(this, \"_meshDataHash\");\n  }\n  validateRenderable(mesh) {\n    const meshData = this._getMeshData(mesh);\n    const wasBatched = meshData.batched;\n    const isBatched = mesh.batched;\n    meshData.batched = isBatched;\n    if (wasBatched !== isBatched) {\n      return true;\n    } else if (isBatched) {\n      const geometry = mesh._geometry;\n      if (geometry.indices.length !== meshData.indexSize || geometry.positions.length !== meshData.vertexSize) {\n        meshData.indexSize = geometry.indices.length;\n        meshData.vertexSize = geometry.positions.length;\n        return true;\n      }\n      const batchableMesh = this._getBatchableMesh(mesh);\n      return !batchableMesh._batcher.checkAndUpdateTexture(\n        batchableMesh,\n        mesh.texture\n      );\n    }\n    return false;\n  }\n  addRenderable(mesh, instructionSet) {\n    const batcher = this.renderer.renderPipes.batch;\n    const { batched } = this._getMeshData(mesh);\n    if (batched) {\n      const gpuBatchableMesh = this._getBatchableMesh(mesh);\n      gpuBatchableMesh.texture = mesh._texture;\n      gpuBatchableMesh.geometry = mesh._geometry;\n      batcher.addToBatch(gpuBatchableMesh, instructionSet);\n    } else {\n      batcher.break(instructionSet);\n      instructionSet.add(mesh);\n    }\n  }\n  updateRenderable(mesh) {\n    if (mesh.batched) {\n      const gpuBatchableMesh = this._gpuBatchableMeshHash[mesh.uid];\n      gpuBatchableMesh.setTexture(mesh._texture);\n      gpuBatchableMesh.geometry = mesh._geometry;\n      gpuBatchableMesh._batcher.updateElement(gpuBatchableMesh);\n    }\n  }\n  destroyRenderable(mesh) {\n    this._meshDataHash[mesh.uid] = null;\n    const gpuMesh = this._gpuBatchableMeshHash[mesh.uid];\n    if (gpuMesh) {\n      BigPool.return(gpuMesh);\n      this._gpuBatchableMeshHash[mesh.uid] = null;\n    }\n    mesh.off(\"destroyed\", this._destroyRenderableBound);\n  }\n  execute(mesh) {\n    if (!mesh.isRenderable)\n      return;\n    mesh.state.blendMode = getAdjustedBlendModeBlend(mesh.groupBlendMode, mesh.texture._source);\n    const localUniforms = this.localUniforms;\n    localUniforms.uniforms.uTransformMatrix = mesh.groupTransform;\n    localUniforms.uniforms.uRound = this.renderer._roundPixels | mesh._roundPixels;\n    localUniforms.update();\n    color32BitToUniform(\n      mesh.groupColorAlpha,\n      localUniforms.uniforms.uColor,\n      0\n    );\n    this._adaptor.execute(this, mesh);\n  }\n  _getMeshData(mesh) {\n    return this._meshDataHash[mesh.uid] || this._initMeshData(mesh);\n  }\n  _initMeshData(mesh) {\n    this._meshDataHash[mesh.uid] = {\n      batched: mesh.batched,\n      indexSize: mesh._geometry.indices?.length,\n      vertexSize: mesh._geometry.positions?.length\n    };\n    mesh.on(\"destroyed\", this._destroyRenderableBound);\n    return this._meshDataHash[mesh.uid];\n  }\n  _getBatchableMesh(mesh) {\n    return this._gpuBatchableMeshHash[mesh.uid] || this._initBatchableMesh(mesh);\n  }\n  _initBatchableMesh(mesh) {\n    const gpuMesh = BigPool.get(BatchableMesh);\n    gpuMesh.renderable = mesh;\n    gpuMesh.texture = mesh._texture;\n    gpuMesh.transform = mesh.groupTransform;\n    gpuMesh.roundPixels = this.renderer._roundPixels | mesh._roundPixels;\n    this._gpuBatchableMeshHash[mesh.uid] = gpuMesh;\n    return gpuMesh;\n  }\n  destroy() {\n    for (const i in this._gpuBatchableMeshHash) {\n      if (this._gpuBatchableMeshHash[i]) {\n        BigPool.return(this._gpuBatchableMeshHash[i]);\n      }\n    }\n    this._gpuBatchableMeshHash = null;\n    this._meshDataHash = null;\n    this.localUniforms = null;\n    this.localUniformsBindGroup = null;\n    this._adaptor.destroy();\n    this._adaptor = null;\n    this.renderer = null;\n  }\n}\n/** @ignore */\nMeshPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"mesh\"\n};\n\nexport { MeshPipe };\n//# sourceMappingURL=MeshPipe.mjs.map\n", "\"use strict\";\nclass GlParticleContainerAdaptor {\n  execute(particleContainerPipe, container) {\n    const state = particleContainerPipe.state;\n    const renderer = particleContainerPipe.renderer;\n    const shader = container.shader || particleContainerPipe.defaultShader;\n    shader.resources.uTexture = container.texture._source;\n    shader.resources.uniforms = particleContainerPipe.localUniforms;\n    const gl = renderer.gl;\n    const buffer = particleContainerPipe.getBuffers(container);\n    renderer.shader.bind(shader);\n    renderer.state.set(state);\n    renderer.geometry.bind(buffer.geometry, shader.glProgram);\n    const byteSize = buffer.geometry.indexBuffer.data.BYTES_PER_ELEMENT;\n    const glType = byteSize === 2 ? gl.UNSIGNED_SHORT : gl.UNSIGNED_INT;\n    gl.drawElements(gl.TRIANGLES, container.particleChildren.length * 6, glType, 0);\n  }\n}\n\nexport { GlParticleContainerAdaptor };\n//# sourceMappingURL=GlParticleContainerAdaptor.mjs.map\n", "\"use strict\";\nclass GpuParticleContainerAdaptor {\n  execute(particleContainerPipe, container) {\n    const renderer = particleContainerPipe.renderer;\n    const shader = container.shader || particleContainerPipe.defaultShader;\n    shader.groups[0] = renderer.renderPipes.uniformBatch.getUniformBindGroup(particleContainerPipe.localUniforms, true);\n    shader.groups[1] = renderer.texture.getTextureBindGroup(container.texture);\n    const state = particleContainerPipe.state;\n    const buffer = particleContainerPipe.getBuffers(container);\n    renderer.encoder.draw({\n      geometry: buffer.geometry,\n      shader: container.shader || particleContainerPipe.defaultShader,\n      state,\n      size: container.particleChildren.length * 6\n    });\n  }\n}\n\nexport { GpuParticleContainerAdaptor };\n//# sourceMappingURL=GpuParticleContainerAdaptor.mjs.map\n", "\"use strict\";\nfunction createIndicesForQuads(size, outBuffer = null) {\n  const totalIndices = size * 6;\n  if (totalIndices > 65535) {\n    outBuffer || (outBuffer = new Uint32Array(totalIndices));\n  } else {\n    outBuffer || (outBuffer = new Uint16Array(totalIndices));\n  }\n  if (outBuffer.length !== totalIndices) {\n    throw new Error(`Out buffer length is incorrect, got ${outBuffer.length} and expected ${totalIndices}`);\n  }\n  for (let i = 0, j = 0; i < totalIndices; i += 6, j += 4) {\n    outBuffer[i + 0] = j + 0;\n    outBuffer[i + 1] = j + 1;\n    outBuffer[i + 2] = j + 2;\n    outBuffer[i + 3] = j + 0;\n    outBuffer[i + 4] = j + 2;\n    outBuffer[i + 5] = j + 3;\n  }\n  return outBuffer;\n}\n\nexport { createIndicesForQuads };\n//# sourceMappingURL=createIndicesForQuads.mjs.map\n", "import { getAttributeInfoFromFormat } from '../../../../rendering/renderers/shared/geometry/utils/getAttributeInfoFromFormat.mjs';\n\n\"use strict\";\nfunction generateParticleUpdateFunction(properties) {\n  return {\n    dynamicUpdate: generateUpdateFunction(properties, true),\n    staticUpdate: generateUpdateFunction(properties, false)\n  };\n}\nfunction generateUpdateFunction(properties, dynamic) {\n  const funcFragments = [];\n  funcFragments.push(`\n      \n        var index = 0;\n\n        for (let i = 0; i < ps.length; ++i)\n        {\n            const p = ps[i];\n\n            `);\n  let offset = 0;\n  for (const i in properties) {\n    const property = properties[i];\n    if (dynamic !== property.dynamic)\n      continue;\n    funcFragments.push(`offset = index + ${offset}`);\n    funcFragments.push(property.code);\n    const attributeInfo = getAttributeInfoFromFormat(property.format);\n    offset += attributeInfo.stride / 4;\n  }\n  funcFragments.push(`\n            index += stride * 4;\n        }\n    `);\n  funcFragments.unshift(`\n        var stride = ${offset};\n    `);\n  const functionSource = funcFragments.join(\"\\n\");\n  return new Function(\"ps\", \"f32v\", \"u32v\", functionSource);\n}\n\nexport { generateParticleUpdateFunction };\n//# sourceMappingURL=generateParticleUpdateFunction.mjs.map\n", "import { Buffer } from '../../../rendering/renderers/shared/buffer/Buffer.mjs';\nimport { BufferUsage } from '../../../rendering/renderers/shared/buffer/const.mjs';\nimport { Geometry } from '../../../rendering/renderers/shared/geometry/Geometry.mjs';\nimport { getAttributeInfoFromFormat } from '../../../rendering/renderers/shared/geometry/utils/getAttributeInfoFromFormat.mjs';\nimport { ViewableBuffer } from '../../../utils/data/ViewableBuffer.mjs';\nimport { createIndicesForQuads } from './utils/createIndicesForQuads.mjs';\nimport { generateParticleUpdateFunction } from './utils/generateParticleUpdateFunction.mjs';\n\n\"use strict\";\nclass ParticleBuffer {\n  constructor(options) {\n    this._size = 0;\n    this._generateParticleUpdateCache = {};\n    const size = this._size = options.size ?? 1e3;\n    const properties = options.properties;\n    let staticVertexSize = 0;\n    let dynamicVertexSize = 0;\n    for (const i in properties) {\n      const property = properties[i];\n      const attributeInfo = getAttributeInfoFromFormat(property.format);\n      if (property.dynamic) {\n        dynamicVertexSize += attributeInfo.stride;\n      } else {\n        staticVertexSize += attributeInfo.stride;\n      }\n    }\n    this._dynamicStride = dynamicVertexSize / 4;\n    this._staticStride = staticVertexSize / 4;\n    this.staticAttributeBuffer = new ViewableBuffer(size * 4 * staticVertexSize);\n    this.dynamicAttributeBuffer = new ViewableBuffer(size * 4 * dynamicVertexSize);\n    this.indexBuffer = createIndicesForQuads(size);\n    const geometry = new Geometry();\n    let dynamicOffset = 0;\n    let staticOffset = 0;\n    this._staticBuffer = new Buffer({\n      data: new Float32Array(1),\n      label: \"static-particle-buffer\",\n      shrinkToFit: false,\n      usage: BufferUsage.VERTEX | BufferUsage.COPY_DST\n    });\n    this._dynamicBuffer = new Buffer({\n      data: new Float32Array(1),\n      label: \"dynamic-particle-buffer\",\n      shrinkToFit: false,\n      usage: BufferUsage.VERTEX | BufferUsage.COPY_DST\n    });\n    for (const i in properties) {\n      const property = properties[i];\n      const attributeInfo = getAttributeInfoFromFormat(property.format);\n      if (property.dynamic) {\n        geometry.addAttribute(property.attributeName, {\n          buffer: this._dynamicBuffer,\n          stride: this._dynamicStride * 4,\n          offset: dynamicOffset * 4,\n          format: property.format\n        });\n        dynamicOffset += attributeInfo.size;\n      } else {\n        geometry.addAttribute(property.attributeName, {\n          buffer: this._staticBuffer,\n          stride: this._staticStride * 4,\n          offset: staticOffset * 4,\n          format: property.format\n        });\n        staticOffset += attributeInfo.size;\n      }\n    }\n    geometry.addIndex(this.indexBuffer);\n    const uploadFunction = this.getParticleUpdate(properties);\n    this._dynamicUpload = uploadFunction.dynamicUpdate;\n    this._staticUpload = uploadFunction.staticUpdate;\n    this.geometry = geometry;\n  }\n  getParticleUpdate(properties) {\n    const key = getParticleSyncKey(properties);\n    if (this._generateParticleUpdateCache[key]) {\n      return this._generateParticleUpdateCache[key];\n    }\n    this._generateParticleUpdateCache[key] = this.generateParticleUpdate(properties);\n    return this._generateParticleUpdateCache[key];\n  }\n  generateParticleUpdate(properties) {\n    return generateParticleUpdateFunction(properties);\n  }\n  update(particles, uploadStatic) {\n    if (particles.length > this._size) {\n      uploadStatic = true;\n      this._size = Math.max(particles.length, this._size * 1.5 | 0);\n      this.staticAttributeBuffer = new ViewableBuffer(this._size * this._staticStride * 4 * 4);\n      this.dynamicAttributeBuffer = new ViewableBuffer(this._size * this._dynamicStride * 4 * 4);\n      this.indexBuffer = createIndicesForQuads(this._size);\n      this.geometry.indexBuffer.setDataWithSize(\n        this.indexBuffer,\n        this.indexBuffer.byteLength,\n        true\n      );\n    }\n    const dynamicAttributeBuffer = this.dynamicAttributeBuffer;\n    this._dynamicUpload(particles, dynamicAttributeBuffer.float32View, dynamicAttributeBuffer.uint32View);\n    this._dynamicBuffer.setDataWithSize(\n      this.dynamicAttributeBuffer.float32View,\n      particles.length * this._dynamicStride * 4,\n      true\n    );\n    if (uploadStatic) {\n      const staticAttributeBuffer = this.staticAttributeBuffer;\n      this._staticUpload(particles, staticAttributeBuffer.float32View, staticAttributeBuffer.uint32View);\n      this._staticBuffer.setDataWithSize(\n        staticAttributeBuffer.float32View,\n        particles.length * this._staticStride * 4,\n        true\n      );\n    }\n  }\n  destroy() {\n    this._staticBuffer.destroy();\n    this._dynamicBuffer.destroy();\n    this.geometry.destroy();\n  }\n}\nfunction getParticleSyncKey(properties) {\n  const keyGen = [];\n  for (const key in properties) {\n    const property = properties[key];\n    keyGen.push(key, property.code, property.dynamic ? \"d\" : \"s\");\n  }\n  return keyGen.join(\"_\");\n}\n\nexport { ParticleBuffer };\n//# sourceMappingURL=ParticleBuffer.mjs.map\n", "var fragment = \"varying vec2 vUV;\\nvarying vec4 vColor;\\n\\nuniform sampler2D uTexture;\\n\\nvoid main(void){\\n    vec4 color = texture2D(uTexture, vUV) * vColor;\\n    gl_FragColor = color;\\n}\";\n\nexport { fragment as default };\n//# sourceMappingURL=particles.frag.mjs.map\n", "var vertex = \"attribute vec2 aVertex;\\nattribute vec2 aUV;\\nattribute vec4 aColor;\\n\\nattribute vec2 aPosition;\\nattribute float aRotation;\\n\\nuniform mat3 uTranslationMatrix;\\nuniform float uRound;\\nuniform vec2 uResolution;\\nuniform vec4 uColor;\\n\\nvarying vec2 vUV;\\nvarying vec4 vColor;\\n\\nvec2 roundPixels(vec2 position, vec2 targetSize)\\n{       \\n    return (floor(((position * 0.5 + 0.5) * targetSize) + 0.5) / targetSize) * 2.0 - 1.0;\\n}\\n\\nvoid main(void){\\n    float cosRotation = cos(aRotation);\\n    float sinRotation = sin(aRotation);\\n    float x = aVertex.x * cosRotation - aVertex.y * sinRotation;\\n    float y = aVertex.x * sinRotation + aVertex.y * cosRotation;\\n\\n    vec2 v = vec2(x, y);\\n    v = v + aPosition;\\n\\n    gl_Position = vec4((uTranslationMatrix * vec3(v, 1.0)).xy, 0.0, 1.0);\\n\\n    if(uRound == 1.0)\\n    {\\n        gl_Position.xy = roundPixels(gl_Position.xy, uResolution);\\n    }\\n\\n    vUV = aUV;\\n    vColor = vec4(aColor.rgb * aColor.a, aColor.a) * uColor;\\n}\\n\";\n\nexport { vertex as default };\n//# sourceMappingURL=particles.vert.mjs.map\n", "var wgsl = \"\\nstruct ParticleUniforms {\\n  uProjectionMatrix:mat3x3<f32>,\\n  uColor:vec4<f32>,\\n  uResolution:vec2<f32>,\\n  uRoundPixels:f32,\\n};\\n\\n@group(0) @binding(0) var<uniform> uniforms: ParticleUniforms;\\n\\n@group(1) @binding(0) var uTexture: texture_2d<f32>;\\n@group(1) @binding(1) var uSampler : sampler;\\n\\nstruct VSOutput {\\n    @builtin(position) position: vec4<f32>,\\n    @location(0) uv : vec2<f32>,\\n    @location(1) color : vec4<f32>,\\n  };\\n@vertex\\nfn mainVertex(\\n  @location(0) aVertex: vec2<f32>,\\n  @location(1) aPosition: vec2<f32>,\\n  @location(2) aUV: vec2<f32>,\\n  @location(3) aColor: vec4<f32>,\\n  @location(4) aRotation: f32,\\n) -> VSOutput {\\n  \\n   let v = vec2(\\n       aVertex.x * cos(aRotation) - aVertex.y * sin(aRotation),\\n       aVertex.x * sin(aRotation) + aVertex.y * cos(aRotation)\\n   ) + aPosition;\\n\\n   let position = vec4((uniforms.uProjectionMatrix * vec3(v, 1.0)).xy, 0.0, 1.0);\\n\\n    let vColor = vec4(aColor.rgb * aColor.a, aColor.a) * uniforms.uColor;\\n\\n  return VSOutput(\\n   position,\\n   aUV,\\n   vColor,\\n  );\\n}\\n\\n@fragment\\nfn mainFragment(\\n  @location(0) uv: vec2<f32>,\\n  @location(1) color: vec4<f32>,\\n  @builtin(position) position: vec4<f32>,\\n) -> @location(0) vec4<f32> {\\n\\n    var sample = textureSample(uTexture, uSampler, uv) * color;\\n   \\n    return sample;\\n}\";\n\nexport { wgsl as default };\n//# sourceMappingURL=particles.wgsl.mjs.map\n", "import { Color } from '../../../../color/Color.mjs';\nimport { Matrix } from '../../../../maths/matrix/Matrix.mjs';\nimport { GlProgram } from '../../../../rendering/renderers/gl/shader/GlProgram.mjs';\nimport { GpuProgram } from '../../../../rendering/renderers/gpu/shader/GpuProgram.mjs';\nimport { Shader } from '../../../../rendering/renderers/shared/shader/Shader.mjs';\nimport { Texture } from '../../../../rendering/renderers/shared/texture/Texture.mjs';\nimport { TextureStyle } from '../../../../rendering/renderers/shared/texture/TextureStyle.mjs';\nimport fragment from './particles.frag.mjs';\nimport vertex from './particles.vert.mjs';\nimport wgsl from './particles.wgsl.mjs';\n\n\"use strict\";\nclass ParticleShader extends Shader {\n  constructor() {\n    const glProgram = GlProgram.from({\n      vertex,\n      fragment\n    });\n    const gpuProgram = GpuProgram.from({\n      fragment: {\n        source: wgsl,\n        entryPoint: \"mainFragment\"\n      },\n      vertex: {\n        source: wgsl,\n        entryPoint: \"mainVertex\"\n      }\n    });\n    super({\n      glProgram,\n      gpuProgram,\n      resources: {\n        // this will be replaced with the texture from the particle container\n        uTexture: Texture.WHITE.source,\n        // this will be replaced with the texture style from the particle container\n        uSampler: new TextureStyle({}),\n        // this will be replaced with the local uniforms from the particle container\n        uniforms: {\n          uTranslationMatrix: { value: new Matrix(), type: \"mat3x3<f32>\" },\n          uColor: { value: new Color(16777215), type: \"vec4<f32>\" },\n          uRound: { value: 1, type: \"f32\" },\n          uResolution: { value: [0, 0], type: \"vec2<f32>\" }\n        }\n      }\n    });\n  }\n}\n\nexport { ParticleShader };\n//# sourceMappingURL=ParticleShader.mjs.map\n", "import { Matrix } from '../../../maths/matrix/Matrix.mjs';\nimport { UniformGroup } from '../../../rendering/renderers/shared/shader/UniformGroup.mjs';\nimport { getAdjustedBlendModeBlend } from '../../../rendering/renderers/shared/state/getAdjustedBlendModeBlend.mjs';\nimport { State } from '../../../rendering/renderers/shared/state/State.mjs';\nimport { color32BitToUniform } from '../../graphics/gpu/colorToUniform.mjs';\nimport { ParticleBuffer } from './ParticleBuffer.mjs';\nimport { ParticleShader } from './shader/ParticleShader.mjs';\n\n\"use strict\";\nclass ParticleContainerPipe {\n  /**\n   * @param renderer - The renderer this sprite batch works for.\n   * @param adaptor\n   */\n  constructor(renderer, adaptor) {\n    this.state = State.for2d();\n    this._gpuBufferHash = /* @__PURE__ */ Object.create(null);\n    // eslint-disable-next-line max-len\n    this._destroyRenderableBound = this.destroyRenderable.bind(this);\n    this.localUniforms = new UniformGroup({\n      uTranslationMatrix: { value: new Matrix(), type: \"mat3x3<f32>\" },\n      uColor: { value: new Float32Array(4), type: \"vec4<f32>\" },\n      uRound: { value: 1, type: \"f32\" },\n      uResolution: { value: [0, 0], type: \"vec2<f32>\" }\n    });\n    this.renderer = renderer;\n    this.adaptor = adaptor;\n    this.defaultShader = new ParticleShader();\n    this.state = State.for2d();\n  }\n  validateRenderable(_renderable) {\n    return false;\n  }\n  addRenderable(renderable, instructionSet) {\n    this.renderer.renderPipes.batch.break(instructionSet);\n    instructionSet.add(renderable);\n  }\n  getBuffers(renderable) {\n    return this._gpuBufferHash[renderable.uid] || this._initBuffer(renderable);\n  }\n  _initBuffer(renderable) {\n    this._gpuBufferHash[renderable.uid] = new ParticleBuffer({\n      size: renderable.particleChildren.length,\n      properties: renderable._properties\n    });\n    renderable.on(\"destroyed\", this._destroyRenderableBound);\n    return this._gpuBufferHash[renderable.uid];\n  }\n  updateRenderable(_renderable) {\n  }\n  destroyRenderable(renderable) {\n    const buffer = this._gpuBufferHash[renderable.uid];\n    buffer.destroy();\n    this._gpuBufferHash[renderable.uid] = null;\n    renderable.off(\"destroyed\", this._destroyRenderableBound);\n  }\n  execute(container) {\n    const children = container.particleChildren;\n    if (children.length === 0) {\n      return;\n    }\n    const renderer = this.renderer;\n    const buffer = this.getBuffers(container);\n    container.texture || (container.texture = children[0].texture);\n    const state = this.state;\n    buffer.update(children, container._childrenDirty);\n    container._childrenDirty = false;\n    state.blendMode = getAdjustedBlendModeBlend(container.blendMode, container.texture._source);\n    const uniforms = this.localUniforms.uniforms;\n    const transformationMatrix = uniforms.uTranslationMatrix;\n    container.worldTransform.copyTo(transformationMatrix);\n    transformationMatrix.prepend(renderer.globalUniforms.globalUniformData.projectionMatrix);\n    uniforms.uResolution = renderer.globalUniforms.globalUniformData.resolution;\n    uniforms.uRound = renderer._roundPixels | container._roundPixels;\n    color32BitToUniform(\n      container.groupColorAlpha,\n      uniforms.uColor,\n      0\n    );\n    this.adaptor.execute(this, container);\n  }\n  /** Destroys the ParticleRenderer. */\n  destroy() {\n    if (this.defaultShader) {\n      this.defaultShader.destroy();\n      this.defaultShader = null;\n    }\n  }\n}\n\nexport { ParticleContainerPipe };\n//# sourceMappingURL=ParticleContainerPipe.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { GlParticleContainerAdaptor } from '../gl/GlParticleContainerAdaptor.mjs';\nimport { ParticleContainerPipe } from './ParticleContainerPipe.mjs';\n\n\"use strict\";\nclass GlParticleContainerPipe extends ParticleContainerPipe {\n  constructor(renderer) {\n    super(renderer, new GlParticleContainerAdaptor());\n  }\n}\n/** @ignore */\nGlParticleContainerPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes\n  ],\n  name: \"particle\"\n};\n\nexport { GlParticleContainerPipe };\n//# sourceMappingURL=GlParticleContainerPipe.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { GpuParticleContainerAdaptor } from '../gpu/GpuParticleContainerAdaptor.mjs';\nimport { ParticleContainerPipe } from './ParticleContainerPipe.mjs';\n\n\"use strict\";\nclass GpuParticleContainerPipe extends ParticleContainerPipe {\n  constructor(renderer) {\n    super(renderer, new GpuParticleContainerAdaptor());\n  }\n}\n/** @ignore */\nGpuParticleContainerPipe.extension = {\n  type: [\n    ExtensionType.WebGPUPipes\n  ],\n  name: \"particle\"\n};\n\nexport { GpuParticleContainerPipe };\n//# sourceMappingURL=GpuParticleContainerPipe.mjs.map\n", "import { PlaneGeometry } from '../mesh-plane/PlaneGeometry.mjs';\n\n\"use strict\";\nconst _NineSliceGeometry = class _NineSliceGeometry extends PlaneGeometry {\n  constructor(options = {}) {\n    options = { ..._NineSliceGeometry.defaultOptions, ...options };\n    super({\n      width: options.width,\n      height: options.height,\n      verticesX: 4,\n      verticesY: 4\n    });\n    this.update(options);\n  }\n  /**\n   * Updates the NineSliceGeometry with the options.\n   * @param options - The options of the NineSliceGeometry.\n   */\n  update(options) {\n    this.width = options.width ?? this.width;\n    this.height = options.height ?? this.height;\n    this._originalWidth = options.originalWidth ?? this._originalWidth;\n    this._originalHeight = options.originalHeight ?? this._originalHeight;\n    this._leftWidth = options.leftWidth ?? this._leftWidth;\n    this._rightWidth = options.rightWidth ?? this._rightWidth;\n    this._topHeight = options.topHeight ?? this._topHeight;\n    this._bottomHeight = options.bottomHeight ?? this._bottomHeight;\n    this.updateUvs();\n    this.updatePositions();\n  }\n  /** Updates the positions of the vertices. */\n  updatePositions() {\n    const positions = this.positions;\n    const w = this._leftWidth + this._rightWidth;\n    const scaleW = this.width > w ? 1 : this.width / w;\n    const h = this._topHeight + this._bottomHeight;\n    const scaleH = this.height > h ? 1 : this.height / h;\n    const scale = Math.min(scaleW, scaleH);\n    positions[9] = positions[11] = positions[13] = positions[15] = this._topHeight * scale;\n    positions[17] = positions[19] = positions[21] = positions[23] = this.height - this._bottomHeight * scale;\n    positions[25] = positions[27] = positions[29] = positions[31] = this.height;\n    positions[2] = positions[10] = positions[18] = positions[26] = this._leftWidth * scale;\n    positions[4] = positions[12] = positions[20] = positions[28] = this.width - this._rightWidth * scale;\n    positions[6] = positions[14] = positions[22] = positions[30] = this.width;\n    this.getBuffer(\"aPosition\").update();\n  }\n  /** Updates the UVs of the vertices. */\n  updateUvs() {\n    const uvs = this.uvs;\n    uvs[0] = uvs[8] = uvs[16] = uvs[24] = 0;\n    uvs[1] = uvs[3] = uvs[5] = uvs[7] = 0;\n    uvs[6] = uvs[14] = uvs[22] = uvs[30] = 1;\n    uvs[25] = uvs[27] = uvs[29] = uvs[31] = 1;\n    const _uvw = 1 / this._originalWidth;\n    const _uvh = 1 / this._originalHeight;\n    uvs[2] = uvs[10] = uvs[18] = uvs[26] = _uvw * this._leftWidth;\n    uvs[9] = uvs[11] = uvs[13] = uvs[15] = _uvh * this._topHeight;\n    uvs[4] = uvs[12] = uvs[20] = uvs[28] = 1 - _uvw * this._rightWidth;\n    uvs[17] = uvs[19] = uvs[21] = uvs[23] = 1 - _uvh * this._bottomHeight;\n    this.getBuffer(\"aUV\").update();\n  }\n};\n/** The default options for the NineSliceGeometry. */\n_NineSliceGeometry.defaultOptions = {\n  /** The width of the NineSlicePlane, setting this will actually modify the vertices and UV's of this plane. */\n  width: 100,\n  /** The height of the NineSlicePlane, setting this will actually modify the vertices and UV's of this plane. */\n  height: 100,\n  /** The width of the left column. */\n  leftWidth: 10,\n  /** The height of the top row. */\n  topHeight: 10,\n  /** The width of the right column. */\n  rightWidth: 10,\n  /** The height of the bottom row. */\n  bottomHeight: 10,\n  /** The original width of the texture */\n  originalWidth: 100,\n  /** The original height of the texture */\n  originalHeight: 100\n};\nlet NineSliceGeometry = _NineSliceGeometry;\n\nexport { NineSliceGeometry };\n//# sourceMappingURL=NineSliceGeometry.mjs.map\n", "import { ExtensionType } from '../../extensions/Extensions.mjs';\nimport { BigPool } from '../../utils/pool/PoolGroup.mjs';\nimport { BatchableMesh } from '../mesh/shared/BatchableMesh.mjs';\nimport { NineSliceGeometry } from './NineSliceGeometry.mjs';\n\n\"use strict\";\nclass NineSliceSpritePipe {\n  constructor(renderer) {\n    this._gpuSpriteHash = /* @__PURE__ */ Object.create(null);\n    this._destroyRenderableBound = this.destroyRenderable.bind(this);\n    this._renderer = renderer;\n    this._renderer.renderableGC.addManagedHash(this, \"_gpuSpriteHash\");\n  }\n  addRenderable(sprite, instructionSet) {\n    const gpuSprite = this._getGpuSprite(sprite);\n    if (sprite.didViewUpdate)\n      this._updateBatchableSprite(sprite, gpuSprite);\n    this._renderer.renderPipes.batch.addToBatch(gpuSprite, instructionSet);\n  }\n  updateRenderable(sprite) {\n    const gpuSprite = this._gpuSpriteHash[sprite.uid];\n    if (sprite.didViewUpdate)\n      this._updateBatchableSprite(sprite, gpuSprite);\n    gpuSprite._batcher.updateElement(gpuSprite);\n  }\n  validateRenderable(sprite) {\n    const gpuSprite = this._getGpuSprite(sprite);\n    return !gpuSprite._batcher.checkAndUpdateTexture(\n      gpuSprite,\n      sprite._texture\n    );\n  }\n  destroyRenderable(sprite) {\n    const batchableMesh = this._gpuSpriteHash[sprite.uid];\n    BigPool.return(batchableMesh.geometry);\n    BigPool.return(batchableMesh);\n    this._gpuSpriteHash[sprite.uid] = null;\n    sprite.off(\"destroyed\", this._destroyRenderableBound);\n  }\n  _updateBatchableSprite(sprite, batchableSprite) {\n    batchableSprite.geometry.update(sprite);\n    batchableSprite.setTexture(sprite._texture);\n  }\n  _getGpuSprite(sprite) {\n    return this._gpuSpriteHash[sprite.uid] || this._initGPUSprite(sprite);\n  }\n  _initGPUSprite(sprite) {\n    const batchableMesh = BigPool.get(BatchableMesh);\n    batchableMesh.geometry = BigPool.get(NineSliceGeometry);\n    batchableMesh.renderable = sprite;\n    batchableMesh.transform = sprite.groupTransform;\n    batchableMesh.texture = sprite._texture;\n    batchableMesh.roundPixels = this._renderer._roundPixels | sprite._roundPixels;\n    this._gpuSpriteHash[sprite.uid] = batchableMesh;\n    if (!sprite.didViewUpdate) {\n      this._updateBatchableSprite(sprite, batchableMesh);\n    }\n    sprite.on(\"destroyed\", this._destroyRenderableBound);\n    return batchableMesh;\n  }\n  destroy() {\n    for (const i in this._gpuSpriteHash) {\n      const batchableMesh = this._gpuSpriteHash[i];\n      batchableMesh.geometry.destroy();\n    }\n    this._gpuSpriteHash = null;\n    this._renderer = null;\n  }\n}\n/** @ignore */\nNineSliceSpritePipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"nineSliceSprite\"\n};\n\nexport { NineSliceSpritePipe };\n//# sourceMappingURL=NineSliceSpritePipe.mjs.map\n", "\"use strict\";\nconst tilingBit = {\n  name: \"tiling-bit\",\n  vertex: {\n    header: (\n      /* wgsl */\n      `\n            struct TilingUniforms {\n                uMapCoord:mat3x3<f32>,\n                uClampFrame:vec4<f32>,\n                uClampOffset:vec2<f32>,\n                uTextureTransform:mat3x3<f32>,\n                uSizeAnchor:vec4<f32>\n            };\n\n            @group(2) @binding(0) var<uniform> tilingUniforms: TilingUniforms;\n            @group(2) @binding(1) var uTexture: texture_2d<f32>;\n            @group(2) @binding(2) var uSampler: sampler;\n        `\n    ),\n    main: (\n      /* wgsl */\n      `\n            uv = (tilingUniforms.uTextureTransform * vec3(uv, 1.0)).xy;\n\n            position = (position - tilingUniforms.uSizeAnchor.zw) * tilingUniforms.uSizeAnchor.xy;\n        `\n    )\n  },\n  fragment: {\n    header: (\n      /* wgsl */\n      `\n            struct TilingUniforms {\n                uMapCoord:mat3x3<f32>,\n                uClampFrame:vec4<f32>,\n                uClampOffset:vec2<f32>,\n                uTextureTransform:mat3x3<f32>,\n                uSizeAnchor:vec4<f32>\n            };\n\n            @group(2) @binding(0) var<uniform> tilingUniforms: TilingUniforms;\n            @group(2) @binding(1) var uTexture: texture_2d<f32>;\n            @group(2) @binding(2) var uSampler: sampler;\n        `\n    ),\n    main: (\n      /* wgsl */\n      `\n\n            var coord = vUV + ceil(tilingUniforms.uClampOffset - vUV);\n            coord = (tilingUniforms.uMapCoord * vec3(coord, 1.0)).xy;\n            var unclamped = coord;\n            coord = clamp(coord, tilingUniforms.uClampFrame.xy, tilingUniforms.uClampFrame.zw);\n\n            var bias = 0.;\n\n            if(unclamped.x == coord.x && unclamped.y == coord.y)\n            {\n                bias = -32.;\n            } \n\n            outColor = textureSampleBias(uTexture, uSampler, coord, bias);\n        `\n    )\n  }\n};\nconst tilingBitGl = {\n  name: \"tiling-bit\",\n  vertex: {\n    header: (\n      /* glsl */\n      `\n            uniform mat3 uTextureTransform;\n            uniform vec4 uSizeAnchor;\n        \n        `\n    ),\n    main: (\n      /* glsl */\n      `\n            uv = (uTextureTransform * vec3(aUV, 1.0)).xy;\n\n            position = (position - uSizeAnchor.zw) * uSizeAnchor.xy;\n        `\n    )\n  },\n  fragment: {\n    header: (\n      /* glsl */\n      `\n            uniform sampler2D uTexture;\n            uniform mat3 uMapCoord;\n            uniform vec4 uClampFrame;\n            uniform vec2 uClampOffset;\n        `\n    ),\n    main: (\n      /* glsl */\n      `\n\n        vec2 coord = vUV + ceil(uClampOffset - vUV);\n        coord = (uMapCoord * vec3(coord, 1.0)).xy;\n        vec2 unclamped = coord;\n        coord = clamp(coord, uClampFrame.xy, uClampFrame.zw);\n        \n        outColor = texture(uTexture, coord, unclamped == coord ? 0.0 : -32.0);// lod-bias very negative to force lod 0\n    \n        `\n    )\n  }\n};\n\nexport { tilingBit, tilingBitGl };\n//# sourceMappingURL=tilingBit.mjs.map\n", "import { Matrix } from '../../../maths/matrix/Matrix.mjs';\nimport { compileHighShaderGpuProgram, compileHighShaderGlProgram } from '../../../rendering/high-shader/compileHighShaderToProgram.mjs';\nimport { localUniformBit, localUniformBitGl } from '../../../rendering/high-shader/shader-bits/localUniformBit.mjs';\nimport { roundPixelsBit, roundPixelsBitGl } from '../../../rendering/high-shader/shader-bits/roundPixelsBit.mjs';\nimport { Shader } from '../../../rendering/renderers/shared/shader/Shader.mjs';\nimport { UniformGroup } from '../../../rendering/renderers/shared/shader/UniformGroup.mjs';\nimport { Texture } from '../../../rendering/renderers/shared/texture/Texture.mjs';\nimport { tilingBit, tilingBitGl } from './tilingBit.mjs';\n\n\"use strict\";\nlet gpuProgram;\nlet glProgram;\nclass TilingSpriteShader extends Shader {\n  constructor() {\n    gpuProgram ?? (gpuProgram = compileHighShaderGpuProgram({\n      name: \"tiling-sprite-shader\",\n      bits: [\n        localUniformBit,\n        tilingBit,\n        roundPixelsBit\n      ]\n    }));\n    glProgram ?? (glProgram = compileHighShaderGlProgram({\n      name: \"tiling-sprite-shader\",\n      bits: [\n        localUniformBitGl,\n        tilingBitGl,\n        roundPixelsBitGl\n      ]\n    }));\n    const tilingUniforms = new UniformGroup({\n      uMapCoord: { value: new Matrix(), type: \"mat3x3<f32>\" },\n      uClampFrame: { value: new Float32Array([0, 0, 1, 1]), type: \"vec4<f32>\" },\n      uClampOffset: { value: new Float32Array([0, 0]), type: \"vec2<f32>\" },\n      uTextureTransform: { value: new Matrix(), type: \"mat3x3<f32>\" },\n      uSizeAnchor: { value: new Float32Array([100, 100, 0.5, 0.5]), type: \"vec4<f32>\" }\n    });\n    super({\n      glProgram,\n      gpuProgram,\n      resources: {\n        localUniforms: new UniformGroup({\n          uTransformMatrix: { value: new Matrix(), type: \"mat3x3<f32>\" },\n          uColor: { value: new Float32Array([1, 1, 1, 1]), type: \"vec4<f32>\" },\n          uRound: { value: 0, type: \"f32\" }\n        }),\n        tilingUniforms,\n        uTexture: Texture.EMPTY.source,\n        uSampler: Texture.EMPTY.source.style\n      }\n    });\n  }\n  updateUniforms(width, height, matrix, anchorX, anchorY, texture) {\n    const tilingUniforms = this.resources.tilingUniforms;\n    const textureWidth = texture.width;\n    const textureHeight = texture.height;\n    const textureMatrix = texture.textureMatrix;\n    const uTextureTransform = tilingUniforms.uniforms.uTextureTransform;\n    uTextureTransform.set(\n      matrix.a * textureWidth / width,\n      matrix.b * textureWidth / height,\n      matrix.c * textureHeight / width,\n      matrix.d * textureHeight / height,\n      matrix.tx / width,\n      matrix.ty / height\n    );\n    uTextureTransform.invert();\n    tilingUniforms.uniforms.uMapCoord = textureMatrix.mapCoord;\n    tilingUniforms.uniforms.uClampFrame = textureMatrix.uClampFrame;\n    tilingUniforms.uniforms.uClampOffset = textureMatrix.uClampOffset;\n    tilingUniforms.uniforms.uTextureTransform = uTextureTransform;\n    tilingUniforms.uniforms.uSizeAnchor[0] = width;\n    tilingUniforms.uniforms.uSizeAnchor[1] = height;\n    tilingUniforms.uniforms.uSizeAnchor[2] = anchorX;\n    tilingUniforms.uniforms.uSizeAnchor[3] = anchorY;\n    if (texture) {\n      this.resources.uTexture = texture.source;\n      this.resources.uSampler = texture.source.style;\n    }\n  }\n}\n\nexport { TilingSpriteShader };\n//# sourceMappingURL=TilingSpriteShader.mjs.map\n", "import { MeshGeometry } from '../../mesh/shared/MeshGeometry.mjs';\n\n\"use strict\";\nclass QuadGeometry extends MeshGeometry {\n  constructor() {\n    super({\n      positions: new Float32Array([0, 0, 1, 0, 1, 1, 0, 1]),\n      uvs: new Float32Array([0, 0, 1, 0, 1, 1, 0, 1]),\n      indices: new Uint32Array([0, 1, 2, 0, 2, 3])\n    });\n  }\n}\n\nexport { QuadGeometry };\n//# sourceMappingURL=QuadGeometry.mjs.map\n", "\"use strict\";\nfunction setPositions(tilingSprite, positions) {\n  const anchorX = tilingSprite.anchor.x;\n  const anchorY = tilingSprite.anchor.y;\n  positions[0] = -anchorX * tilingSprite.width;\n  positions[1] = -anchorY * tilingSprite.height;\n  positions[2] = (1 - anchorX) * tilingSprite.width;\n  positions[3] = -anchorY * tilingSprite.height;\n  positions[4] = (1 - anchorX) * tilingSprite.width;\n  positions[5] = (1 - anchorY) * tilingSprite.height;\n  positions[6] = -anchorX * tilingSprite.width;\n  positions[7] = (1 - anchorY) * tilingSprite.height;\n}\n\nexport { setPositions };\n//# sourceMappingURL=setPositions.mjs.map\n", "\"use strict\";\nfunction applyMatrix(array, stride, offset, matrix) {\n  let index = 0;\n  const size = array.length / (stride || 2);\n  const a = matrix.a;\n  const b = matrix.b;\n  const c = matrix.c;\n  const d = matrix.d;\n  const tx = matrix.tx;\n  const ty = matrix.ty;\n  offset *= stride;\n  while (index < size) {\n    const x = array[offset];\n    const y = array[offset + 1];\n    array[offset] = a * x + c * y + tx;\n    array[offset + 1] = b * x + d * y + ty;\n    offset += stride;\n    index++;\n  }\n}\n\nexport { applyMatrix };\n//# sourceMappingURL=applyMatrix.mjs.map\n", "import { Matrix } from '../../../maths/matrix/Matrix.mjs';\nimport { applyMatrix } from './applyMatrix.mjs';\n\n\"use strict\";\nfunction setUvs(tilingSprite, uvs) {\n  const texture = tilingSprite.texture;\n  const width = texture.frame.width;\n  const height = texture.frame.height;\n  let anchorX = 0;\n  let anchorY = 0;\n  if (tilingSprite.applyAnchorToTexture) {\n    anchorX = tilingSprite.anchor.x;\n    anchorY = tilingSprite.anchor.y;\n  }\n  uvs[0] = uvs[6] = -anchorX;\n  uvs[2] = uvs[4] = 1 - anchorX;\n  uvs[1] = uvs[3] = -anchorY;\n  uvs[5] = uvs[7] = 1 - anchorY;\n  const textureMatrix = Matrix.shared;\n  textureMatrix.copyFrom(tilingSprite._tileTransform.matrix);\n  textureMatrix.tx /= tilingSprite.width;\n  textureMatrix.ty /= tilingSprite.height;\n  textureMatrix.invert();\n  textureMatrix.scale(tilingSprite.width / width, tilingSprite.height / height);\n  applyMatrix(uvs, 2, 0, textureMatrix);\n}\n\nexport { setUvs };\n//# sourceMappingURL=setUvs.mjs.map\n", "import { ExtensionType } from '../../extensions/Extensions.mjs';\nimport { getAdjustedBlendModeBlend } from '../../rendering/renderers/shared/state/getAdjustedBlendModeBlend.mjs';\nimport { State } from '../../rendering/renderers/shared/state/State.mjs';\nimport { RendererType } from '../../rendering/renderers/types.mjs';\nimport { color32BitToUniform } from '../graphics/gpu/colorToUniform.mjs';\nimport { BatchableMesh } from '../mesh/shared/BatchableMesh.mjs';\nimport { MeshGeometry } from '../mesh/shared/MeshGeometry.mjs';\nimport { TilingSpriteShader } from './shader/TilingSpriteShader.mjs';\nimport { QuadGeometry } from './utils/QuadGeometry.mjs';\nimport { setPositions } from './utils/setPositions.mjs';\nimport { setUvs } from './utils/setUvs.mjs';\n\n\"use strict\";\nconst sharedQuad = new QuadGeometry();\nclass TilingSpritePipe {\n  constructor(renderer) {\n    this._state = State.default2d;\n    this._tilingSpriteDataHash = /* @__PURE__ */ Object.create(null);\n    this._destroyRenderableBound = this.destroyRenderable.bind(this);\n    this._renderer = renderer;\n    this._renderer.renderableGC.addManagedHash(this, \"_tilingSpriteDataHash\");\n  }\n  validateRenderable(renderable) {\n    const tilingSpriteData = this._getTilingSpriteData(renderable);\n    const couldBatch = tilingSpriteData.canBatch;\n    this._updateCanBatch(renderable);\n    const canBatch = tilingSpriteData.canBatch;\n    if (canBatch && canBatch === couldBatch) {\n      const { batchableMesh } = tilingSpriteData;\n      return !batchableMesh._batcher.checkAndUpdateTexture(\n        batchableMesh,\n        renderable.texture\n      );\n    }\n    return couldBatch !== canBatch;\n  }\n  addRenderable(tilingSprite, instructionSet) {\n    const batcher = this._renderer.renderPipes.batch;\n    this._updateCanBatch(tilingSprite);\n    const tilingSpriteData = this._getTilingSpriteData(tilingSprite);\n    const { geometry, canBatch } = tilingSpriteData;\n    if (canBatch) {\n      tilingSpriteData.batchableMesh || (tilingSpriteData.batchableMesh = new BatchableMesh());\n      const batchableMesh = tilingSpriteData.batchableMesh;\n      if (tilingSprite.didViewUpdate) {\n        this._updateBatchableMesh(tilingSprite);\n        batchableMesh.geometry = geometry;\n        batchableMesh.renderable = tilingSprite;\n        batchableMesh.transform = tilingSprite.groupTransform;\n        batchableMesh.setTexture(tilingSprite._texture);\n      }\n      batchableMesh.roundPixels = this._renderer._roundPixels | tilingSprite._roundPixels;\n      batcher.addToBatch(batchableMesh, instructionSet);\n    } else {\n      batcher.break(instructionSet);\n      tilingSpriteData.shader || (tilingSpriteData.shader = new TilingSpriteShader());\n      this.updateRenderable(tilingSprite);\n      instructionSet.add(tilingSprite);\n    }\n  }\n  execute(tilingSprite) {\n    const { shader } = this._tilingSpriteDataHash[tilingSprite.uid];\n    shader.groups[0] = this._renderer.globalUniforms.bindGroup;\n    const localUniforms = shader.resources.localUniforms.uniforms;\n    localUniforms.uTransformMatrix = tilingSprite.groupTransform;\n    localUniforms.uRound = this._renderer._roundPixels | tilingSprite._roundPixels;\n    color32BitToUniform(\n      tilingSprite.groupColorAlpha,\n      localUniforms.uColor,\n      0\n    );\n    this._state.blendMode = getAdjustedBlendModeBlend(tilingSprite.groupBlendMode, tilingSprite.texture._source);\n    this._renderer.encoder.draw({\n      geometry: sharedQuad,\n      shader,\n      state: this._state\n    });\n  }\n  updateRenderable(tilingSprite) {\n    const tilingSpriteData = this._getTilingSpriteData(tilingSprite);\n    const { canBatch } = tilingSpriteData;\n    if (canBatch) {\n      const { batchableMesh } = tilingSpriteData;\n      if (tilingSprite.didViewUpdate)\n        this._updateBatchableMesh(tilingSprite);\n      batchableMesh._batcher.updateElement(batchableMesh);\n    } else if (tilingSprite.didViewUpdate) {\n      const { shader } = tilingSpriteData;\n      shader.updateUniforms(\n        tilingSprite.width,\n        tilingSprite.height,\n        tilingSprite._tileTransform.matrix,\n        tilingSprite.anchor.x,\n        tilingSprite.anchor.y,\n        tilingSprite.texture\n      );\n    }\n  }\n  destroyRenderable(tilingSprite) {\n    const tilingSpriteData = this._getTilingSpriteData(tilingSprite);\n    tilingSpriteData.batchableMesh = null;\n    tilingSpriteData.shader?.destroy();\n    this._tilingSpriteDataHash[tilingSprite.uid] = null;\n    tilingSprite.off(\"destroyed\", this._destroyRenderableBound);\n  }\n  _getTilingSpriteData(renderable) {\n    return this._tilingSpriteDataHash[renderable.uid] || this._initTilingSpriteData(renderable);\n  }\n  _initTilingSpriteData(tilingSprite) {\n    const geometry = new MeshGeometry({\n      indices: sharedQuad.indices,\n      positions: sharedQuad.positions.slice(),\n      uvs: sharedQuad.uvs.slice()\n    });\n    this._tilingSpriteDataHash[tilingSprite.uid] = {\n      canBatch: true,\n      renderable: tilingSprite,\n      geometry\n    };\n    tilingSprite.on(\"destroyed\", this._destroyRenderableBound);\n    return this._tilingSpriteDataHash[tilingSprite.uid];\n  }\n  _updateBatchableMesh(tilingSprite) {\n    const renderableData = this._getTilingSpriteData(tilingSprite);\n    const { geometry } = renderableData;\n    const style = tilingSprite.texture.source.style;\n    if (style.addressMode !== \"repeat\") {\n      style.addressMode = \"repeat\";\n      style.update();\n    }\n    setUvs(tilingSprite, geometry.uvs);\n    setPositions(tilingSprite, geometry.positions);\n  }\n  destroy() {\n    for (const i in this._tilingSpriteDataHash) {\n      this.destroyRenderable(this._tilingSpriteDataHash[i].renderable);\n    }\n    this._tilingSpriteDataHash = null;\n    this._renderer = null;\n  }\n  _updateCanBatch(tilingSprite) {\n    const renderableData = this._getTilingSpriteData(tilingSprite);\n    const texture = tilingSprite.texture;\n    let _nonPowOf2wrapping = true;\n    if (this._renderer.type === RendererType.WEBGL) {\n      _nonPowOf2wrapping = this._renderer.context.supports.nonPowOf2wrapping;\n    }\n    renderableData.canBatch = texture.textureMatrix.isSimple && (_nonPowOf2wrapping || texture.source.isPowerOfTwo);\n    return renderableData.canBatch;\n  }\n}\n/** @ignore */\nTilingSpritePipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"tilingSprite\"\n};\n\nexport { TilingSpritePipe };\n//# sourceMappingURL=TilingSpritePipe.mjs.map\n", "\"use strict\";\nconst localUniformMSDFBit = {\n  name: \"local-uniform-msdf-bit\",\n  vertex: {\n    header: (\n      /* wgsl */\n      `\n            struct LocalUniforms {\n                uColor:vec4<f32>,\n                uTransformMatrix:mat3x3<f32>,\n                uDistance: f32,\n                uRound:f32,\n            }\n\n            @group(2) @binding(0) var<uniform> localUniforms : LocalUniforms;\n        `\n    ),\n    main: (\n      /* wgsl */\n      `\n            vColor *= localUniforms.uColor;\n            modelMatrix *= localUniforms.uTransformMatrix;\n        `\n    ),\n    end: (\n      /* wgsl */\n      `\n            if(localUniforms.uRound == 1)\n            {\n                vPosition = vec4(roundPixels(vPosition.xy, globalUniforms.uResolution), vPosition.zw);\n            }\n        `\n    )\n  },\n  fragment: {\n    header: (\n      /* wgsl */\n      `\n            struct LocalUniforms {\n                uColor:vec4<f32>,\n                uTransformMatrix:mat3x3<f32>,\n                uDistance: f32\n            }\n\n            @group(2) @binding(0) var<uniform> localUniforms : LocalUniforms;\n         `\n    ),\n    main: (\n      /* wgsl */\n      ` \n            outColor = vec4<f32>(calculateMSDFAlpha(outColor, localUniforms.uColor, localUniforms.uDistance));\n        `\n    )\n  }\n};\nconst localUniformMSDFBitGl = {\n  name: \"local-uniform-msdf-bit\",\n  vertex: {\n    header: (\n      /* glsl */\n      `\n            uniform mat3 uTransformMatrix;\n            uniform vec4 uColor;\n            uniform float uRound;\n        `\n    ),\n    main: (\n      /* glsl */\n      `\n            vColor *= uColor;\n            modelMatrix *= uTransformMatrix;\n        `\n    ),\n    end: (\n      /* glsl */\n      `\n            if(uRound == 1.)\n            {\n                gl_Position.xy = roundPixels(gl_Position.xy, uResolution);\n            }\n        `\n    )\n  },\n  fragment: {\n    header: (\n      /* glsl */\n      `\n            uniform float uDistance;\n         `\n    ),\n    main: (\n      /* glsl */\n      ` \n            outColor = vec4(calculateMSDFAlpha(outColor, vColor, uDistance));\n        `\n    )\n  }\n};\n\nexport { localUniformMSDFBit, localUniformMSDFBitGl };\n//# sourceMappingURL=localUniformMSDFBit.mjs.map\n", "\"use strict\";\nconst mSDFBit = {\n  name: \"msdf-bit\",\n  fragment: {\n    header: (\n      /* wgsl */\n      `\n            fn calculateMSDFAlpha(msdfColor:vec4<f32>, shapeColor:vec4<f32>, distance:f32) -> f32 {\n                \n                // MSDF\n                var median = msdfColor.r + msdfColor.g + msdfColor.b -\n                    min(msdfColor.r, min(msdfColor.g, msdfColor.b)) -\n                    max(msdfColor.r, max(msdfColor.g, msdfColor.b));\n            \n                // SDF\n                median = min(median, msdfColor.a);\n\n                var screenPxDistance = distance * (median - 0.5);\n                var alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);\n                if (median < 0.01) {\n                    alpha = 0.0;\n                } else if (median > 0.99) {\n                    alpha = 1.0;\n                }\n\n                // Gamma correction for coverage-like alpha\n                var luma: f32 = dot(shapeColor.rgb, vec3<f32>(0.299, 0.587, 0.114));\n                var gamma: f32 = mix(1.0, 1.0 / 2.2, luma);\n                var coverage: f32 = pow(shapeColor.a * alpha, gamma);\n\n                return coverage;\n             \n            }\n        `\n    )\n  }\n};\nconst mSDFBitGl = {\n  name: \"msdf-bit\",\n  fragment: {\n    header: (\n      /* glsl */\n      `\n            float calculateMSDFAlpha(vec4 msdfColor, vec4 shapeColor, float distance) {\n                \n                // MSDF\n                float median = msdfColor.r + msdfColor.g + msdfColor.b -\n                                min(msdfColor.r, min(msdfColor.g, msdfColor.b)) -\n                                max(msdfColor.r, max(msdfColor.g, msdfColor.b));\n               \n                // SDF\n                median = min(median, msdfColor.a);\n            \n                float screenPxDistance = distance * (median - 0.5);\n                float alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);\n           \n                if (median < 0.01) {\n                    alpha = 0.0;\n                } else if (median > 0.99) {\n                    alpha = 1.0;\n                }\n\n                // Gamma correction for coverage-like alpha\n                float luma = dot(shapeColor.rgb, vec3(0.299, 0.587, 0.114));\n                float gamma = mix(1.0, 1.0 / 2.2, luma);\n                float coverage = pow(shapeColor.a * alpha, gamma);  \n              \n                return coverage;\n            }\n        `\n    )\n  }\n};\n\nexport { mSDFBit, mSDFBitGl };\n//# sourceMappingURL=mSDFBit.mjs.map\n", "import { Matrix } from '../../../maths/matrix/Matrix.mjs';\nimport { getMaxTexturesPerBatch } from '../../../rendering/batcher/gl/utils/maxRecommendedTextures.mjs';\nimport { compileHighShaderGpuProgram, compileHighShaderGlProgram } from '../../../rendering/high-shader/compileHighShaderToProgram.mjs';\nimport { colorBit, colorBitGl } from '../../../rendering/high-shader/shader-bits/colorBit.mjs';\nimport { generateTextureBatchBit, generateTextureBatchBitGl } from '../../../rendering/high-shader/shader-bits/generateTextureBatchBit.mjs';\nimport { roundPixelsBit, roundPixelsBitGl } from '../../../rendering/high-shader/shader-bits/roundPixelsBit.mjs';\nimport { getBatchSamplersUniformGroup } from '../../../rendering/renderers/gl/shader/getBatchSamplersUniformGroup.mjs';\nimport { Shader } from '../../../rendering/renderers/shared/shader/Shader.mjs';\nimport { UniformGroup } from '../../../rendering/renderers/shared/shader/UniformGroup.mjs';\nimport { localUniformMSDFBit, localUniformMSDFBitGl } from './shader-bits/localUniformMSDFBit.mjs';\nimport { mSDFBit, mSDFBitGl } from './shader-bits/mSDFBit.mjs';\n\n\"use strict\";\nlet gpuProgram;\nlet glProgram;\nclass SdfShader extends Shader {\n  constructor() {\n    const uniforms = new UniformGroup({\n      uColor: { value: new Float32Array([1, 1, 1, 1]), type: \"vec4<f32>\" },\n      uTransformMatrix: { value: new Matrix(), type: \"mat3x3<f32>\" },\n      uDistance: { value: 4, type: \"f32\" },\n      uRound: { value: 0, type: \"f32\" }\n    });\n    const maxTextures = getMaxTexturesPerBatch();\n    gpuProgram ?? (gpuProgram = compileHighShaderGpuProgram({\n      name: \"sdf-shader\",\n      bits: [\n        colorBit,\n        generateTextureBatchBit(maxTextures),\n        localUniformMSDFBit,\n        mSDFBit,\n        roundPixelsBit\n      ]\n    }));\n    glProgram ?? (glProgram = compileHighShaderGlProgram({\n      name: \"sdf-shader\",\n      bits: [\n        colorBitGl,\n        generateTextureBatchBitGl(maxTextures),\n        localUniformMSDFBitGl,\n        mSDFBitGl,\n        roundPixelsBitGl\n      ]\n    }));\n    super({\n      glProgram,\n      gpuProgram,\n      resources: {\n        localUniforms: uniforms,\n        batchSamplers: getBatchSamplersUniformGroup(maxTextures)\n      }\n    });\n  }\n}\n\nexport { SdfShader };\n//# sourceMappingURL=SdfShader.mjs.map\n", "import { Cache } from '../../assets/cache/Cache.mjs';\nimport { ExtensionType } from '../../extensions/Extensions.mjs';\nimport { BigPool } from '../../utils/pool/PoolGroup.mjs';\nimport { Graphics } from '../graphics/shared/Graphics.mjs';\nimport { SdfShader } from '../text/sdfShader/SdfShader.mjs';\nimport { BitmapFontManager } from './BitmapFontManager.mjs';\nimport { getBitmapTextLayout } from './utils/getBitmapTextLayout.mjs';\n\n\"use strict\";\nclass BitmapTextPipe {\n  constructor(renderer) {\n    this._gpuBitmapText = {};\n    this._destroyRenderableBound = this.destroyRenderable.bind(this);\n    this._renderer = renderer;\n    this._renderer.renderableGC.addManagedHash(this, \"_gpuBitmapText\");\n  }\n  validateRenderable(bitmapText) {\n    const graphicsRenderable = this._getGpuBitmapText(bitmapText);\n    if (bitmapText._didTextUpdate) {\n      bitmapText._didTextUpdate = false;\n      this._updateContext(bitmapText, graphicsRenderable);\n    }\n    return this._renderer.renderPipes.graphics.validateRenderable(graphicsRenderable);\n  }\n  addRenderable(bitmapText, instructionSet) {\n    const graphicsRenderable = this._getGpuBitmapText(bitmapText);\n    syncWithProxy(bitmapText, graphicsRenderable);\n    if (bitmapText._didTextUpdate) {\n      bitmapText._didTextUpdate = false;\n      this._updateContext(bitmapText, graphicsRenderable);\n    }\n    this._renderer.renderPipes.graphics.addRenderable(graphicsRenderable, instructionSet);\n    if (graphicsRenderable.context.customShader) {\n      this._updateDistanceField(bitmapText);\n    }\n  }\n  destroyRenderable(bitmapText) {\n    bitmapText.off(\"destroyed\", this._destroyRenderableBound);\n    this._destroyRenderableByUid(bitmapText.uid);\n  }\n  _destroyRenderableByUid(renderableUid) {\n    const context = this._gpuBitmapText[renderableUid].context;\n    if (context.customShader) {\n      BigPool.return(context.customShader);\n      context.customShader = null;\n    }\n    BigPool.return(this._gpuBitmapText[renderableUid]);\n    this._gpuBitmapText[renderableUid] = null;\n  }\n  updateRenderable(bitmapText) {\n    const graphicsRenderable = this._getGpuBitmapText(bitmapText);\n    syncWithProxy(bitmapText, graphicsRenderable);\n    this._renderer.renderPipes.graphics.updateRenderable(graphicsRenderable);\n    if (graphicsRenderable.context.customShader) {\n      this._updateDistanceField(bitmapText);\n    }\n  }\n  _updateContext(bitmapText, proxyGraphics) {\n    const { context } = proxyGraphics;\n    const bitmapFont = BitmapFontManager.getFont(bitmapText.text, bitmapText._style);\n    context.clear();\n    if (bitmapFont.distanceField.type !== \"none\") {\n      if (!context.customShader) {\n        context.customShader = BigPool.get(SdfShader);\n      }\n    }\n    const chars = Array.from(bitmapText.text);\n    const style = bitmapText._style;\n    let currentY = bitmapFont.baseLineOffset;\n    const bitmapTextLayout = getBitmapTextLayout(chars, style, bitmapFont, true);\n    let index = 0;\n    const padding = style.padding;\n    const scale = bitmapTextLayout.scale;\n    let tx = bitmapTextLayout.width;\n    let ty = bitmapTextLayout.height + bitmapTextLayout.offsetY;\n    if (style._stroke) {\n      tx += style._stroke.width / scale;\n      ty += style._stroke.width / scale;\n    }\n    context.translate(-bitmapText._anchor._x * tx - padding, -bitmapText._anchor._y * ty - padding).scale(scale, scale);\n    const tint = bitmapFont.applyFillAsTint ? style._fill.color : 16777215;\n    for (let i = 0; i < bitmapTextLayout.lines.length; i++) {\n      const line = bitmapTextLayout.lines[i];\n      for (let j = 0; j < line.charPositions.length; j++) {\n        const char = chars[index++];\n        const charData = bitmapFont.chars[char];\n        if (charData?.texture) {\n          context.texture(\n            charData.texture,\n            tint ? tint : \"black\",\n            Math.round(line.charPositions[j] + charData.xOffset),\n            Math.round(currentY + charData.yOffset)\n          );\n        }\n      }\n      currentY += bitmapFont.lineHeight;\n    }\n  }\n  _getGpuBitmapText(bitmapText) {\n    return this._gpuBitmapText[bitmapText.uid] || this.initGpuText(bitmapText);\n  }\n  initGpuText(bitmapText) {\n    const proxyRenderable = BigPool.get(Graphics);\n    this._gpuBitmapText[bitmapText.uid] = proxyRenderable;\n    this._updateContext(bitmapText, proxyRenderable);\n    bitmapText.on(\"destroyed\", this._destroyRenderableBound);\n    return this._gpuBitmapText[bitmapText.uid];\n  }\n  _updateDistanceField(bitmapText) {\n    const context = this._getGpuBitmapText(bitmapText).context;\n    const fontFamily = bitmapText._style.fontFamily;\n    const dynamicFont = Cache.get(`${fontFamily}-bitmap`);\n    const { a, b, c, d } = bitmapText.groupTransform;\n    const dx = Math.sqrt(a * a + b * b);\n    const dy = Math.sqrt(c * c + d * d);\n    const worldScale = (Math.abs(dx) + Math.abs(dy)) / 2;\n    const fontScale = dynamicFont.baseRenderedFontSize / bitmapText._style.fontSize;\n    const distance = worldScale * dynamicFont.distanceField.range * (1 / fontScale);\n    context.customShader.resources.localUniforms.uniforms.uDistance = distance;\n  }\n  destroy() {\n    for (const uid in this._gpuBitmapText) {\n      this._destroyRenderableByUid(uid);\n    }\n    this._gpuBitmapText = null;\n    this._renderer = null;\n  }\n}\n/** @ignore */\nBitmapTextPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"bitmapText\"\n};\nfunction syncWithProxy(container, proxy) {\n  proxy.groupTransform = container.groupTransform;\n  proxy.groupColorAlpha = container.groupColorAlpha;\n  proxy.groupColor = container.groupColor;\n  proxy.groupBlendMode = container.groupBlendMode;\n  proxy.globalDisplayStatus = container.globalDisplayStatus;\n  proxy.groupTransform = container.groupTransform;\n  proxy.localDisplayStatus = container.localDisplayStatus;\n  proxy.groupAlpha = container.groupAlpha;\n  proxy._roundPixels = container._roundPixels;\n}\n\nexport { BitmapTextPipe };\n//# sourceMappingURL=BitmapTextPipe.mjs.map\n", "import { updateQuadBounds } from '../../../utils/data/updateQuadBounds.mjs';\n\n\"use strict\";\nfunction updateTextBounds(batchableSprite, text) {\n  const { texture, bounds } = batchableSprite;\n  updateQuadBounds(bounds, text._anchor, texture);\n  const padding = text._style.padding;\n  bounds.minX -= padding;\n  bounds.minY -= padding;\n  bounds.maxX -= padding;\n  bounds.maxY -= padding;\n}\n\nexport { updateTextBounds };\n//# sourceMappingURL=updateTextBounds.mjs.map\n", "import { ExtensionType } from '../../extensions/Extensions.mjs';\nimport { Texture } from '../../rendering/renderers/shared/texture/Texture.mjs';\nimport { BigPool } from '../../utils/pool/PoolGroup.mjs';\nimport { BatchableSprite } from '../sprite/BatchableSprite.mjs';\nimport { updateTextBounds } from '../text/utils/updateTextBounds.mjs';\n\n\"use strict\";\nclass HTMLTextPipe {\n  constructor(renderer) {\n    this._gpuText = /* @__PURE__ */ Object.create(null);\n    this._destroyRenderableBound = this.destroyRenderable.bind(this);\n    this._renderer = renderer;\n    this._renderer.runners.resolutionChange.add(this);\n    this._renderer.renderableGC.addManagedHash(this, \"_gpuText\");\n  }\n  resolutionChange() {\n    for (const i in this._gpuText) {\n      const gpuText = this._gpuText[i];\n      if (!gpuText)\n        continue;\n      const text = gpuText.batchableSprite.renderable;\n      if (text._autoResolution) {\n        text._resolution = this._renderer.resolution;\n        text.onViewUpdate();\n      }\n    }\n  }\n  validateRenderable(htmlText) {\n    const gpuText = this._getGpuText(htmlText);\n    const newKey = htmlText._getKey();\n    if (gpuText.textureNeedsUploading) {\n      gpuText.textureNeedsUploading = false;\n      return true;\n    }\n    if (gpuText.currentKey !== newKey) {\n      return true;\n    }\n    return false;\n  }\n  addRenderable(htmlText, instructionSet) {\n    const gpuText = this._getGpuText(htmlText);\n    const batchableSprite = gpuText.batchableSprite;\n    if (htmlText._didTextUpdate) {\n      this._updateText(htmlText);\n    }\n    this._renderer.renderPipes.batch.addToBatch(batchableSprite, instructionSet);\n  }\n  updateRenderable(htmlText) {\n    const gpuText = this._getGpuText(htmlText);\n    const batchableSprite = gpuText.batchableSprite;\n    if (htmlText._didTextUpdate) {\n      this._updateText(htmlText);\n    }\n    batchableSprite._batcher.updateElement(batchableSprite);\n  }\n  destroyRenderable(htmlText) {\n    htmlText.off(\"destroyed\", this._destroyRenderableBound);\n    this._destroyRenderableById(htmlText.uid);\n  }\n  _destroyRenderableById(htmlTextUid) {\n    const gpuText = this._gpuText[htmlTextUid];\n    this._renderer.htmlText.decreaseReferenceCount(gpuText.currentKey);\n    BigPool.return(gpuText.batchableSprite);\n    this._gpuText[htmlTextUid] = null;\n  }\n  _updateText(htmlText) {\n    const newKey = htmlText._getKey();\n    const gpuText = this._getGpuText(htmlText);\n    const batchableSprite = gpuText.batchableSprite;\n    if (gpuText.currentKey !== newKey) {\n      this._updateGpuText(htmlText).catch((e) => {\n        console.error(e);\n      });\n    }\n    htmlText._didTextUpdate = false;\n    updateTextBounds(batchableSprite, htmlText);\n  }\n  async _updateGpuText(htmlText) {\n    htmlText._didTextUpdate = false;\n    const gpuText = this._getGpuText(htmlText);\n    if (gpuText.generatingTexture)\n      return;\n    const newKey = htmlText._getKey();\n    this._renderer.htmlText.decreaseReferenceCount(gpuText.currentKey);\n    gpuText.generatingTexture = true;\n    gpuText.currentKey = newKey;\n    const resolution = htmlText.resolution ?? this._renderer.resolution;\n    const texture = await this._renderer.htmlText.getManagedTexture(\n      htmlText.text,\n      resolution,\n      htmlText._style,\n      htmlText._getKey()\n    );\n    const batchableSprite = gpuText.batchableSprite;\n    batchableSprite.texture = gpuText.texture = texture;\n    gpuText.generatingTexture = false;\n    gpuText.textureNeedsUploading = true;\n    htmlText.onViewUpdate();\n    updateTextBounds(batchableSprite, htmlText);\n  }\n  _getGpuText(htmlText) {\n    return this._gpuText[htmlText.uid] || this.initGpuText(htmlText);\n  }\n  initGpuText(htmlText) {\n    const gpuTextData = {\n      texture: Texture.EMPTY,\n      currentKey: \"--\",\n      batchableSprite: BigPool.get(BatchableSprite),\n      textureNeedsUploading: false,\n      generatingTexture: false\n    };\n    const batchableSprite = gpuTextData.batchableSprite;\n    batchableSprite.renderable = htmlText;\n    batchableSprite.transform = htmlText.groupTransform;\n    batchableSprite.texture = Texture.EMPTY;\n    batchableSprite.bounds = { minX: 0, maxX: 1, minY: 0, maxY: 0 };\n    batchableSprite.roundPixels = this._renderer._roundPixels | htmlText._roundPixels;\n    htmlText._resolution = htmlText._autoResolution ? this._renderer.resolution : htmlText.resolution;\n    this._gpuText[htmlText.uid] = gpuTextData;\n    htmlText.on(\"destroyed\", this._destroyRenderableBound);\n    return gpuTextData;\n  }\n  destroy() {\n    for (const i in this._gpuText) {\n      this._destroyRenderableById(i);\n    }\n    this._gpuText = null;\n    this._renderer = null;\n  }\n}\n/** @ignore */\nHTMLTextPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"htmlText\"\n};\n\nexport { HTMLTextPipe };\n//# sourceMappingURL=HTMLTextPipe.mjs.map\n", "import { DOMAdapter } from '../../environment/adapter.mjs';\n\n\"use strict\";\nfunction isSafari() {\n  const { userAgent } = DOMAdapter.get().getNavigator();\n  return /^((?!chrome|android).)*safari/i.test(userAgent);\n}\n\nexport { isSafari };\n//# sourceMappingURL=isSafari.mjs.map\n", "import { TexturePool } from '../../../rendering/renderers/shared/texture/TexturePool.mjs';\nimport { Bounds } from '../../container/bounds/Bounds.mjs';\n\n\"use strict\";\nconst tempBounds = new Bounds();\nfunction getPo2TextureFromSource(image, width, height, resolution) {\n  const bounds = tempBounds;\n  bounds.minX = 0;\n  bounds.minY = 0;\n  bounds.maxX = image.width / resolution | 0;\n  bounds.maxY = image.height / resolution | 0;\n  const texture = TexturePool.getOptimalTexture(\n    bounds.width,\n    bounds.height,\n    resolution,\n    false\n  );\n  texture.source.uploadMethodId = \"image\";\n  texture.source.resource = image;\n  texture.source.alphaMode = \"premultiply-alpha-on-upload\";\n  texture.frame.width = width / resolution;\n  texture.frame.height = height / resolution;\n  texture.source.emit(\"update\", texture.source);\n  texture.updateUvs();\n  return texture;\n}\n\nexport { getPo2TextureFromSource };\n//# sourceMappingURL=getPo2TextureFromSource.mjs.map\n", "\"use strict\";\nfunction extractFontFamilies(text, style) {\n  const fontFamily = style.fontFamily;\n  const fontFamilies = [];\n  const dedupe = {};\n  const regex = /font-family:([^;\"\\s]+)/g;\n  const matches = text.match(regex);\n  function addFontFamily(fontFamily2) {\n    if (!dedupe[fontFamily2]) {\n      fontFamilies.push(fontFamily2);\n      dedupe[fontFamily2] = true;\n    }\n  }\n  if (Array.isArray(fontFamily)) {\n    for (let i = 0; i < fontFamily.length; i++) {\n      addFontFamily(fontFamily[i]);\n    }\n  } else {\n    addFontFamily(fontFamily);\n  }\n  if (matches) {\n    matches.forEach((match) => {\n      const fontFamily2 = match.split(\":\")[1].trim();\n      addFontFamily(fontFamily2);\n    });\n  }\n  for (const i in style.tagStyles) {\n    const fontFamily2 = style.tagStyles[i].fontFamily;\n    addFontFamily(fontFamily2);\n  }\n  return fontFamilies;\n}\n\nexport { extractFontFamilies };\n//# sourceMappingURL=extractFontFamilies.mjs.map\n", "import { DOMAdapter } from '../../../environment/adapter.mjs';\n\n\"use strict\";\nasync function loadFontAsBase64(url) {\n  const response = await DOMAdapter.get().fetch(url);\n  const blob = await response.blob();\n  const reader = new FileReader();\n  const dataSrc = await new Promise((resolve, reject) => {\n    reader.onloadend = () => resolve(reader.result);\n    reader.onerror = reject;\n    reader.readAsDataURL(blob);\n  });\n  return dataSrc;\n}\n\nexport { loadFontAsBase64 };\n//# sourceMappingURL=loadFontAsBase64.mjs.map\n", "import { loadFontAsBase64 } from './loadFontAsBase64.mjs';\n\n\"use strict\";\nasync function loadFontCSS(style, url) {\n  const dataSrc = await loadFontAsBase64(url);\n  return `@font-face {\n        font-family: \"${style.fontFamily}\";\n        src: url('${dataSrc}');\n        font-weight: ${style.fontWeight};\n        font-style: ${style.fontStyle};\n    }`;\n}\n\nexport { loadFontCSS };\n//# sourceMappingURL=loadFontCSS.mjs.map\n", "import { Cache } from '../../../assets/cache/Cache.mjs';\nimport { loadFontCSS } from './loadFontCSS.mjs';\n\n\"use strict\";\nconst FontStylePromiseCache = /* @__PURE__ */ new Map();\nasync function getFontCss(fontFamilies, style, defaultOptions) {\n  const fontPromises = fontFamilies.filter((fontFamily) => Cache.has(`${fontFamily}-and-url`)).map((fontFamily, i) => {\n    if (!FontStylePromiseCache.has(fontFamily)) {\n      const { url } = Cache.get(`${fontFamily}-and-url`);\n      if (i === 0) {\n        FontStylePromiseCache.set(fontFamily, loadFontCSS({\n          fontWeight: style.fontWeight,\n          fontStyle: style.fontStyle,\n          fontFamily\n        }, url));\n      } else {\n        FontStylePromiseCache.set(fontFamily, loadFontCSS({\n          fontWeight: defaultOptions.fontWeight,\n          fontStyle: defaultOptions.fontStyle,\n          fontFamily\n        }, url));\n      }\n    }\n    return FontStylePromiseCache.get(fontFamily);\n  });\n  return (await Promise.all(fontPromises)).join(\"\\n\");\n}\n\nexport { FontStylePromiseCache, getFontCss };\n//# sourceMappingURL=getFontCss.mjs.map\n", "\"use strict\";\nfunction getSVGUrl(text, style, resolution, fontCSS, htmlTextData) {\n  const { domElement, styleElement, svgRoot } = htmlTextData;\n  domElement.innerHTML = `<style>${style.cssStyle}</style><div style='padding:0;'>${text}</div>`;\n  domElement.setAttribute(\"style\", `transform: scale(${resolution});transform-origin: top left; display: inline-block`);\n  styleElement.textContent = fontCSS;\n  const { width, height } = htmlTextData.image;\n  svgRoot.setAttribute(\"width\", width.toString());\n  svgRoot.setAttribute(\"height\", height.toString());\n  return new XMLSerializer().serializeToString(svgRoot);\n}\n\nexport { getSVGUrl };\n//# sourceMappingURL=getSVGUrl.mjs.map\n", "import { CanvasPool } from '../../../rendering/renderers/shared/texture/CanvasPool.mjs';\n\n\"use strict\";\nfunction getTemporaryCanvasFromImage(image, resolution) {\n  const canvasAndContext = CanvasPool.getOptimalCanvasAndContext(\n    image.width,\n    image.height,\n    resolution\n  );\n  const { context } = canvasAndContext;\n  context.clearRect(0, 0, image.width, image.height);\n  context.drawImage(image, 0, 0);\n  return canvasAndContext;\n}\n\nexport { getTemporaryCanvasFromImage };\n//# sourceMappingURL=getTemporaryCanvasFromImage.mjs.map\n", "\"use strict\";\nfunction loadSVGImage(image, url, delay) {\n  return new Promise(async (resolve) => {\n    if (delay) {\n      await new Promise((resolve2) => setTimeout(resolve2, 100));\n    }\n    image.onload = () => {\n      resolve();\n    };\n    image.src = `data:image/svg+xml;charset=utf8,${encodeURIComponent(url)}`;\n    image.crossOrigin = \"anonymous\";\n  });\n}\n\nexport { loadSVGImage };\n//# sourceMappingURL=loadSVGImage.mjs.map\n", "import { ExtensionType } from '../../extensions/Extensions.mjs';\nimport { CanvasPool } from '../../rendering/renderers/shared/texture/CanvasPool.mjs';\nimport { TexturePool } from '../../rendering/renderers/shared/texture/TexturePool.mjs';\nimport { RendererType } from '../../rendering/renderers/types.mjs';\nimport { isSafari } from '../../utils/browser/isSafari.mjs';\nimport { warn } from '../../utils/logging/warn.mjs';\nimport { BigPool } from '../../utils/pool/PoolGroup.mjs';\nimport { getPo2TextureFromSource } from '../text/utils/getPo2TextureFromSource.mjs';\nimport { HTMLTextRenderData } from './HTMLTextRenderData.mjs';\nimport { HTMLTextStyle } from './HTMLTextStyle.mjs';\nimport { extractFontFamilies } from './utils/extractFontFamilies.mjs';\nimport { getFontCss } from './utils/getFontCss.mjs';\nimport { getSVGUrl } from './utils/getSVGUrl.mjs';\nimport { getTemporaryCanvasFromImage } from './utils/getTemporaryCanvasFromImage.mjs';\nimport { loadSVGImage } from './utils/loadSVGImage.mjs';\nimport { measureHtmlText } from './utils/measureHtmlText.mjs';\n\n\"use strict\";\nclass HTMLTextSystem {\n  constructor(renderer) {\n    this._activeTextures = {};\n    this._renderer = renderer;\n    this._createCanvas = renderer.type === RendererType.WEBGPU;\n  }\n  getTexture(options) {\n    return this._buildTexturePromise(\n      options.text,\n      options.resolution,\n      options.style\n    );\n  }\n  getManagedTexture(text, resolution, style, textKey) {\n    if (this._activeTextures[textKey]) {\n      this._increaseReferenceCount(textKey);\n      return this._activeTextures[textKey].promise;\n    }\n    const promise = this._buildTexturePromise(text, resolution, style).then((texture) => {\n      this._activeTextures[textKey].texture = texture;\n      return texture;\n    });\n    this._activeTextures[textKey] = {\n      texture: null,\n      promise,\n      usageCount: 1\n    };\n    return promise;\n  }\n  async _buildTexturePromise(text, resolution, style) {\n    const htmlTextData = BigPool.get(HTMLTextRenderData);\n    const fontFamilies = extractFontFamilies(text, style);\n    const fontCSS = await getFontCss(\n      fontFamilies,\n      style,\n      HTMLTextStyle.defaultTextStyle\n    );\n    const measured = measureHtmlText(text, style, fontCSS, htmlTextData);\n    const width = Math.ceil(Math.ceil(Math.max(1, measured.width) + style.padding * 2) * resolution);\n    const height = Math.ceil(Math.ceil(Math.max(1, measured.height) + style.padding * 2) * resolution);\n    const image = htmlTextData.image;\n    const uvSafeOffset = 2;\n    image.width = (width | 0) + uvSafeOffset;\n    image.height = (height | 0) + uvSafeOffset;\n    const svgURL = getSVGUrl(text, style, resolution, fontCSS, htmlTextData);\n    await loadSVGImage(image, svgURL, isSafari() && fontFamilies.length > 0);\n    const resource = image;\n    let canvasAndContext;\n    if (this._createCanvas) {\n      canvasAndContext = getTemporaryCanvasFromImage(image, resolution);\n    }\n    const texture = getPo2TextureFromSource(\n      canvasAndContext ? canvasAndContext.canvas : resource,\n      image.width - uvSafeOffset,\n      image.height - uvSafeOffset,\n      resolution\n    );\n    if (this._createCanvas) {\n      this._renderer.texture.initSource(texture.source);\n      CanvasPool.returnCanvasAndContext(canvasAndContext);\n    }\n    BigPool.return(htmlTextData);\n    return texture;\n  }\n  _increaseReferenceCount(textKey) {\n    this._activeTextures[textKey].usageCount++;\n  }\n  decreaseReferenceCount(textKey) {\n    const activeTexture = this._activeTextures[textKey];\n    if (!activeTexture)\n      return;\n    activeTexture.usageCount--;\n    if (activeTexture.usageCount === 0) {\n      if (activeTexture.texture) {\n        this._cleanUp(activeTexture);\n      } else {\n        activeTexture.promise.then((texture) => {\n          activeTexture.texture = texture;\n          this._cleanUp(activeTexture);\n        }).catch(() => {\n          warn(\"HTMLTextSystem: Failed to clean texture\");\n        });\n      }\n      this._activeTextures[textKey] = null;\n    }\n  }\n  _cleanUp(activeTexture) {\n    TexturePool.returnTexture(activeTexture.texture);\n    activeTexture.texture.source.resource = null;\n    activeTexture.texture.source.uploadMethodId = \"unknown\";\n  }\n  getReferenceCount(textKey) {\n    return this._activeTextures[textKey].usageCount;\n  }\n  destroy() {\n    this._activeTextures = null;\n  }\n}\n/** @ignore */\nHTMLTextSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem,\n    ExtensionType.CanvasSystem\n  ],\n  name: \"htmlText\"\n};\nHTMLTextSystem.defaultFontOptions = {\n  fontFamily: \"Arial\",\n  fontStyle: \"normal\",\n  fontWeight: \"normal\"\n};\n\nexport { HTMLTextSystem };\n//# sourceMappingURL=HTMLTextSystem.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { BigPool } from '../../../utils/pool/PoolGroup.mjs';\nimport { BatchableSprite } from '../../sprite/BatchableSprite.mjs';\nimport { updateTextBounds } from '../utils/updateTextBounds.mjs';\n\n\"use strict\";\nclass CanvasTextPipe {\n  constructor(renderer) {\n    this._gpuText = /* @__PURE__ */ Object.create(null);\n    this._destroyRenderableBound = this.destroyRenderable.bind(this);\n    this._renderer = renderer;\n    this._renderer.runners.resolutionChange.add(this);\n    this._renderer.renderableGC.addManagedHash(this, \"_gpuText\");\n  }\n  resolutionChange() {\n    for (const i in this._gpuText) {\n      const gpuText = this._gpuText[i];\n      if (!gpuText)\n        continue;\n      const text = gpuText.batchableSprite.renderable;\n      if (text._autoResolution) {\n        text._resolution = this._renderer.resolution;\n        text.onViewUpdate();\n      }\n    }\n  }\n  validateRenderable(text) {\n    const gpuText = this._getGpuText(text);\n    const newKey = text._getKey();\n    if (gpuText.currentKey !== newKey) {\n      return true;\n    }\n    return false;\n  }\n  addRenderable(text, instructionSet) {\n    const gpuText = this._getGpuText(text);\n    const batchableSprite = gpuText.batchableSprite;\n    if (text._didTextUpdate) {\n      this._updateText(text);\n    }\n    this._renderer.renderPipes.batch.addToBatch(batchableSprite, instructionSet);\n  }\n  updateRenderable(text) {\n    const gpuText = this._getGpuText(text);\n    const batchableSprite = gpuText.batchableSprite;\n    if (text._didTextUpdate) {\n      this._updateText(text);\n    }\n    batchableSprite._batcher.updateElement(batchableSprite);\n  }\n  destroyRenderable(text) {\n    text.off(\"destroyed\", this._destroyRenderableBound);\n    this._destroyRenderableById(text.uid);\n  }\n  _destroyRenderableById(textUid) {\n    const gpuText = this._gpuText[textUid];\n    this._renderer.canvasText.decreaseReferenceCount(gpuText.currentKey);\n    BigPool.return(gpuText.batchableSprite);\n    this._gpuText[textUid] = null;\n  }\n  _updateText(text) {\n    const newKey = text._getKey();\n    const gpuText = this._getGpuText(text);\n    const batchableSprite = gpuText.batchableSprite;\n    if (gpuText.currentKey !== newKey) {\n      this._updateGpuText(text);\n    }\n    text._didTextUpdate = false;\n    updateTextBounds(batchableSprite, text);\n  }\n  _updateGpuText(text) {\n    const gpuText = this._getGpuText(text);\n    const batchableSprite = gpuText.batchableSprite;\n    if (gpuText.texture) {\n      this._renderer.canvasText.decreaseReferenceCount(gpuText.currentKey);\n    }\n    gpuText.texture = batchableSprite.texture = this._renderer.canvasText.getManagedTexture(text);\n    gpuText.currentKey = text._getKey();\n    batchableSprite.texture = gpuText.texture;\n  }\n  _getGpuText(text) {\n    return this._gpuText[text.uid] || this.initGpuText(text);\n  }\n  initGpuText(text) {\n    const gpuTextData = {\n      texture: null,\n      currentKey: \"--\",\n      batchableSprite: BigPool.get(BatchableSprite)\n    };\n    gpuTextData.batchableSprite.renderable = text;\n    gpuTextData.batchableSprite.transform = text.groupTransform;\n    gpuTextData.batchableSprite.bounds = { minX: 0, maxX: 1, minY: 0, maxY: 0 };\n    gpuTextData.batchableSprite.roundPixels = this._renderer._roundPixels | text._roundPixels;\n    this._gpuText[text.uid] = gpuTextData;\n    text._resolution = text._autoResolution ? this._renderer.resolution : text.resolution;\n    this._updateText(text);\n    text.on(\"destroyed\", this._destroyRenderableBound);\n    return gpuTextData;\n  }\n  destroy() {\n    for (const i in this._gpuText) {\n      this._destroyRenderableById(i);\n    }\n    this._gpuText = null;\n    this._renderer = null;\n  }\n}\n/** @ignore */\nCanvasTextPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"text\"\n};\n\nexport { CanvasTextPipe };\n//# sourceMappingURL=CanvasTextPipe.mjs.map\n", "import { Rectangle } from '../../maths/shapes/Rectangle.mjs';\n\n\"use strict\";\nfunction checkRow(data, width, y) {\n  for (let x = 0, index = 4 * y * width; x < width; ++x, index += 4) {\n    if (data[index + 3] !== 0)\n      return false;\n  }\n  return true;\n}\nfunction checkColumn(data, width, x, top, bottom) {\n  const stride = 4 * width;\n  for (let y = top, index = top * stride + 4 * x; y <= bottom; ++y, index += stride) {\n    if (data[index + 3] !== 0)\n      return false;\n  }\n  return true;\n}\nfunction getCanvasBoundingBox(canvas, resolution = 1) {\n  const { width, height } = canvas;\n  const context = canvas.getContext(\"2d\", {\n    willReadFrequently: true\n  });\n  if (context === null) {\n    throw new TypeError(\"Failed to get canvas 2D context\");\n  }\n  const imageData = context.getImageData(0, 0, width, height);\n  const data = imageData.data;\n  let left = 0;\n  let top = 0;\n  let right = width - 1;\n  let bottom = height - 1;\n  while (top < height && checkRow(data, width, top))\n    ++top;\n  if (top === height)\n    return Rectangle.EMPTY;\n  while (checkRow(data, width, bottom))\n    --bottom;\n  while (checkColumn(data, width, left, top, bottom))\n    ++left;\n  while (checkColumn(data, width, right, top, bottom))\n    --right;\n  ++right;\n  ++bottom;\n  return new Rectangle(left / resolution, top / resolution, (right - left) / resolution, (bottom - top) / resolution);\n}\n\nexport { getCanvasBoundingBox };\n//# sourceMappingURL=getCanvasBoundingBox.mjs.map\n", "import { Color } from '../../../color/Color.mjs';\nimport { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { nextPow2 } from '../../../maths/misc/pow2.mjs';\nimport { CanvasPool } from '../../../rendering/renderers/shared/texture/CanvasPool.mjs';\nimport { TexturePool } from '../../../rendering/renderers/shared/texture/TexturePool.mjs';\nimport { getCanvasBoundingBox } from '../../../utils/canvas/getCanvasBoundingBox.mjs';\nimport { deprecation } from '../../../utils/logging/deprecation.mjs';\nimport { TextStyle } from '../TextStyle.mjs';\nimport { getPo2TextureFromSource } from '../utils/getPo2TextureFromSource.mjs';\nimport { CanvasTextMetrics } from './CanvasTextMetrics.mjs';\nimport { fontStringFromTextStyle } from './utils/fontStringFromTextStyle.mjs';\nimport { getCanvasFillStyle } from './utils/getCanvasFillStyle.mjs';\n\n\"use strict\";\nclass CanvasTextSystem {\n  constructor(_renderer) {\n    this._activeTextures = {};\n    this._renderer = _renderer;\n  }\n  getTextureSize(text, resolution, style) {\n    const measured = CanvasTextMetrics.measureText(text || \" \", style);\n    let width = Math.ceil(Math.ceil(Math.max(1, measured.width) + style.padding * 2) * resolution);\n    let height = Math.ceil(Math.ceil(Math.max(1, measured.height) + style.padding * 2) * resolution);\n    width = Math.ceil(width - 1e-6);\n    height = Math.ceil(height - 1e-6);\n    width = nextPow2(width);\n    height = nextPow2(height);\n    return { width, height };\n  }\n  getTexture(options, resolution, style, _textKey) {\n    if (typeof options === \"string\") {\n      deprecation(\"8.0.0\", \"CanvasTextSystem.getTexture: Use object TextOptions instead of separate arguments\");\n      options = {\n        text: options,\n        style,\n        resolution\n      };\n    }\n    if (!(options.style instanceof TextStyle)) {\n      options.style = new TextStyle(options.style);\n    }\n    const { texture, canvasAndContext } = this.createTextureAndCanvas(\n      options\n    );\n    this._renderer.texture.initSource(texture._source);\n    CanvasPool.returnCanvasAndContext(canvasAndContext);\n    return texture;\n  }\n  createTextureAndCanvas(options) {\n    const { text, style } = options;\n    const resolution = options.resolution ?? this._renderer.resolution;\n    const measured = CanvasTextMetrics.measureText(text || \" \", style);\n    const width = Math.ceil(Math.ceil(Math.max(1, measured.width) + style.padding * 2) * resolution);\n    const height = Math.ceil(Math.ceil(Math.max(1, measured.height) + style.padding * 2) * resolution);\n    const canvasAndContext = CanvasPool.getOptimalCanvasAndContext(width, height);\n    const { canvas } = canvasAndContext;\n    this.renderTextToCanvas(text, style, resolution, canvasAndContext);\n    const texture = getPo2TextureFromSource(canvas, width, height, resolution);\n    if (style.trim) {\n      const trimmed = getCanvasBoundingBox(canvas, resolution);\n      texture.frame.copyFrom(trimmed);\n      texture.updateUvs();\n    }\n    return { texture, canvasAndContext };\n  }\n  getManagedTexture(text) {\n    text._resolution = text._autoResolution ? this._renderer.resolution : text.resolution;\n    const textKey = text._getKey();\n    if (this._activeTextures[textKey]) {\n      this._increaseReferenceCount(textKey);\n      return this._activeTextures[textKey].texture;\n    }\n    const { texture, canvasAndContext } = this.createTextureAndCanvas(text);\n    this._activeTextures[textKey] = {\n      canvasAndContext,\n      texture,\n      usageCount: 1\n    };\n    return texture;\n  }\n  _increaseReferenceCount(textKey) {\n    this._activeTextures[textKey].usageCount++;\n  }\n  decreaseReferenceCount(textKey) {\n    const activeTexture = this._activeTextures[textKey];\n    activeTexture.usageCount--;\n    if (activeTexture.usageCount === 0) {\n      CanvasPool.returnCanvasAndContext(activeTexture.canvasAndContext);\n      TexturePool.returnTexture(activeTexture.texture);\n      const source = activeTexture.texture.source;\n      source.resource = null;\n      source.uploadMethodId = \"unknown\";\n      source.alphaMode = \"no-premultiply-alpha\";\n      this._activeTextures[textKey] = null;\n    }\n  }\n  getReferenceCount(textKey) {\n    return this._activeTextures[textKey].usageCount;\n  }\n  /**\n   * Renders text to its canvas, and updates its texture.\n   *\n   * By default this is used internally to ensure the texture is correct before rendering,\n   * but it can be used called externally, for example from this class to 'pre-generate' the texture from a piece of text,\n   * and then shared across multiple Sprites.\n   * @param text\n   * @param style\n   * @param resolution\n   * @param canvasAndContext\n   */\n  renderTextToCanvas(text, style, resolution, canvasAndContext) {\n    const { canvas, context } = canvasAndContext;\n    const font = fontStringFromTextStyle(style);\n    const measured = CanvasTextMetrics.measureText(text || \" \", style);\n    const lines = measured.lines;\n    const lineHeight = measured.lineHeight;\n    const lineWidths = measured.lineWidths;\n    const maxLineWidth = measured.maxLineWidth;\n    const fontProperties = measured.fontProperties;\n    const height = canvas.height;\n    context.resetTransform();\n    context.scale(resolution, resolution);\n    context.textBaseline = style.textBaseline;\n    if (style._stroke?.width) {\n      const strokeStyle = style._stroke;\n      context.lineWidth = strokeStyle.width;\n      context.miterLimit = strokeStyle.miterLimit;\n      context.lineJoin = strokeStyle.join;\n      context.lineCap = strokeStyle.cap;\n    }\n    context.font = font;\n    let linePositionX;\n    let linePositionY;\n    const passesCount = style.dropShadow ? 2 : 1;\n    for (let i = 0; i < passesCount; ++i) {\n      const isShadowPass = style.dropShadow && i === 0;\n      const dsOffsetText = isShadowPass ? Math.ceil(Math.max(1, height) + style.padding * 2) : 0;\n      const dsOffsetShadow = dsOffsetText * resolution;\n      if (isShadowPass) {\n        context.fillStyle = \"black\";\n        context.strokeStyle = \"black\";\n        const shadowOptions = style.dropShadow;\n        const dropShadowColor = shadowOptions.color;\n        const dropShadowAlpha = shadowOptions.alpha;\n        context.shadowColor = Color.shared.setValue(dropShadowColor).setAlpha(dropShadowAlpha).toRgbaString();\n        const dropShadowBlur = shadowOptions.blur * resolution;\n        const dropShadowDistance = shadowOptions.distance * resolution;\n        context.shadowBlur = dropShadowBlur;\n        context.shadowOffsetX = Math.cos(shadowOptions.angle) * dropShadowDistance;\n        context.shadowOffsetY = Math.sin(shadowOptions.angle) * dropShadowDistance + dsOffsetShadow;\n      } else {\n        context.fillStyle = style._fill ? getCanvasFillStyle(style._fill, context) : null;\n        if (style._stroke?.width) {\n          context.strokeStyle = getCanvasFillStyle(style._stroke, context);\n        }\n        context.shadowColor = \"black\";\n      }\n      let linePositionYShift = (lineHeight - fontProperties.fontSize) / 2;\n      if (lineHeight - fontProperties.fontSize < 0) {\n        linePositionYShift = 0;\n      }\n      const strokeWidth = style._stroke?.width ?? 0;\n      for (let i2 = 0; i2 < lines.length; i2++) {\n        linePositionX = strokeWidth / 2;\n        linePositionY = strokeWidth / 2 + i2 * lineHeight + fontProperties.ascent + linePositionYShift;\n        if (style.align === \"right\") {\n          linePositionX += maxLineWidth - lineWidths[i2];\n        } else if (style.align === \"center\") {\n          linePositionX += (maxLineWidth - lineWidths[i2]) / 2;\n        }\n        if (style._stroke?.width) {\n          this._drawLetterSpacing(\n            lines[i2],\n            style,\n            canvasAndContext,\n            linePositionX + style.padding,\n            linePositionY + style.padding - dsOffsetText,\n            true\n          );\n        }\n        if (style._fill !== void 0) {\n          this._drawLetterSpacing(\n            lines[i2],\n            style,\n            canvasAndContext,\n            linePositionX + style.padding,\n            linePositionY + style.padding - dsOffsetText\n          );\n        }\n      }\n    }\n  }\n  /**\n   * Render the text with letter-spacing.\n   * @param text - The text to draw\n   * @param style\n   * @param canvasAndContext\n   * @param x - Horizontal position to draw the text\n   * @param y - Vertical position to draw the text\n   * @param isStroke - Is this drawing for the outside stroke of the\n   *  text? If not, it's for the inside fill\n   */\n  _drawLetterSpacing(text, style, canvasAndContext, x, y, isStroke = false) {\n    const { context } = canvasAndContext;\n    const letterSpacing = style.letterSpacing;\n    let useExperimentalLetterSpacing = false;\n    if (CanvasTextMetrics.experimentalLetterSpacingSupported) {\n      if (CanvasTextMetrics.experimentalLetterSpacing) {\n        context.letterSpacing = `${letterSpacing}px`;\n        context.textLetterSpacing = `${letterSpacing}px`;\n        useExperimentalLetterSpacing = true;\n      } else {\n        context.letterSpacing = \"0px\";\n        context.textLetterSpacing = \"0px\";\n      }\n    }\n    if (letterSpacing === 0 || useExperimentalLetterSpacing) {\n      if (isStroke) {\n        context.strokeText(text, x, y);\n      } else {\n        context.fillText(text, x, y);\n      }\n      return;\n    }\n    let currentPosition = x;\n    const stringArray = CanvasTextMetrics.graphemeSegmenter(text);\n    let previousWidth = context.measureText(text).width;\n    let currentWidth = 0;\n    for (let i = 0; i < stringArray.length; ++i) {\n      const currentChar = stringArray[i];\n      if (isStroke) {\n        context.strokeText(currentChar, currentPosition, y);\n      } else {\n        context.fillText(currentChar, currentPosition, y);\n      }\n      let textStr = \"\";\n      for (let j = i + 1; j < stringArray.length; ++j) {\n        textStr += stringArray[j];\n      }\n      currentWidth = context.measureText(textStr).width;\n      currentPosition += previousWidth - currentWidth + letterSpacing;\n      previousWidth = currentWidth;\n    }\n  }\n  destroy() {\n    this._activeTextures = null;\n  }\n}\n/** @ignore */\nCanvasTextSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem,\n    ExtensionType.CanvasSystem\n  ],\n  name: \"canvasText\"\n};\n\nexport { CanvasTextSystem };\n//# sourceMappingURL=CanvasTextSystem.mjs.map\n", "import { extensions } from '../extensions/Extensions.mjs';\nimport { ResizePlugin } from './ResizePlugin.mjs';\nimport { TickerPlugin } from './TickerPlugin.mjs';\n\n\"use strict\";\nextensions.add(ResizePlugin);\nextensions.add(TickerPlugin);\n//# sourceMappingURL=init.mjs.map\n", "import { extensions } from '../../extensions/Extensions.mjs';\nimport { GraphicsContextSystem } from './shared/GraphicsContextSystem.mjs';\nimport { GraphicsPipe } from './shared/GraphicsPipe.mjs';\n\n\"use strict\";\nextensions.add(GraphicsPipe);\nextensions.add(GraphicsContextSystem);\n//# sourceMappingURL=init.mjs.map\n", "import { extensions } from '../../extensions/Extensions.mjs';\nimport { MeshPipe } from './shared/MeshPipe.mjs';\n\n\"use strict\";\nextensions.add(MeshPipe);\n//# sourceMappingURL=init.mjs.map\n", "import { extensions } from '../../extensions/Extensions.mjs';\nimport { GlParticleContainerPipe } from './shared/GlParticleContainerPipe.mjs';\nimport { GpuParticleContainerPipe } from './shared/GpuParticleContainerPipe.mjs';\n\n\"use strict\";\nextensions.add(GlParticleContainerPipe);\nextensions.add(GpuParticleContainerPipe);\n//# sourceMappingURL=init.mjs.map\n", "import { extensions } from '../../extensions/Extensions.mjs';\nimport { CanvasTextPipe } from './canvas/CanvasTextPipe.mjs';\nimport { CanvasTextSystem } from './canvas/CanvasTextSystem.mjs';\n\n\"use strict\";\nextensions.add(CanvasTextSystem);\nextensions.add(CanvasTextPipe);\n//# sourceMappingURL=init.mjs.map\n", "import { extensions } from '../../extensions/Extensions.mjs';\nimport { BitmapTextPipe } from './BitmapTextPipe.mjs';\n\n\"use strict\";\nextensions.add(BitmapTextPipe);\n//# sourceMappingURL=init.mjs.map\n", "import { extensions } from '../../extensions/Extensions.mjs';\nimport { HTMLTextPipe } from './HTMLTextPipe.mjs';\nimport { HTMLTextSystem } from './HTMLTextSystem.mjs';\n\n\"use strict\";\nextensions.add(HTMLTextSystem);\nextensions.add(HTMLTextPipe);\n//# sourceMappingURL=init.mjs.map\n", "import { extensions } from '../../extensions/Extensions.mjs';\nimport { TilingSpritePipe } from './TilingSpritePipe.mjs';\n\n\"use strict\";\nextensions.add(TilingSpritePipe);\n//# sourceMappingURL=init.mjs.map\n", "import { extensions } from '../../extensions/Extensions.mjs';\nimport { NineSliceSpritePipe } from './NineSliceSpritePipe.mjs';\n\n\"use strict\";\nextensions.add(NineSliceSpritePipe);\n//# sourceMappingURL=init.mjs.map\n", "import { extensions } from '../extensions/Extensions.mjs';\nimport { FilterPipe } from './FilterPipe.mjs';\nimport { FilterSystem } from './FilterSystem.mjs';\n\n\"use strict\";\nextensions.add(FilterSystem);\nextensions.add(FilterPipe);\n//# sourceMappingURL=init.mjs.map\n"], "names": ["ResizePlugin", "options", "dom", "width", "height", "clientWidth", "clientHeight", "ExtensionType", "TickerPlugin", "ticker", "UPDATE_PRIORITY", "Ticker", "oldTicker", "FilterPipe", "renderer", "filterEffect", "container", "instructionSet", "_filterEffect", "_container", "instruction", "getGlobalRenderableBounds", "renderables", "bounds", "tempMatrix", "i", "renderable", "quadGeometry", "Geometry", "FilterSystem", "UniformGroup", "BindGroup", "filters", "filterData", "filterFrameTransform", "colorTextureSource", "resolution", "padding", "antialias", "blendRequired", "enabled", "clipToViewport", "filter", "warn", "viewPort", "rootResolution", "TexturePool", "inputTexture", "backTexture", "Texture", "previousBounds", "renderTarget", "flip", "flop", "t", "lastRenderSurface", "backgroundResolution", "x", "y", "input", "output", "clear", "offset", "Point", "is<PERSON>inal<PERSON>arget", "currentIndex", "filterUniforms", "uniforms", "outputFrame", "inputSize", "inputPixel", "inputClamp", "globalFrame", "outputTexture", "lastIndex", "filterData2", "rootTexture", "batchUniforms", "RendererType", "Bounds", "outputMatrix", "sprite", "data", "mappedMatrix", "worldTransform", "Matrix", "renderGroup", "_MeshGeometry", "args", "deprecation", "v8_0_0", "positions", "uvs", "indices", "shrinkToFit", "position<PERSON><PERSON>er", "<PERSON><PERSON><PERSON>", "BufferUsage", "uv<PERSON><PERSON><PERSON>", "indexBuffer", "value", "MeshGeometry", "textStyleToCSS", "style", "stroke", "fill", "cssStyles", "Color", "strokeToCSS", "dropShadowToCSS", "tagStyleToCSS", "dropShadowStyle", "color", "position", "templates", "transform", "tagStyles", "out", "tagStyle", "cssTagStyle", "j", "HTMLTextStyle", "TextStyle", "generateTextStyleKey", "toAdd", "v", "toRemove", "nssvg", "nsxhtml", "HTMLTextRenderData", "foreignObject", "svgRoot", "styleElement", "dom<PERSON>lement", "tempHTMLTextRenderData", "measureHtmlText", "text", "fontStyleCSS", "htmlTextRenderData", "contentBounds", "doublePadding", "GraphicsPipe", "adaptor", "State", "graphics", "context", "wasBatched", "gpuContext", "batches", "batch", "shader", "localUniforms", "color32BitToUniform", "batchPipe", "roundPixels", "batchClone", "BigPool", "BatchableGraphics", "graphicsUid", "_PlaneGeometry", "total", "verts", "verticesX", "verticesY", "sizeX", "sizeY", "totalSub", "xpos", "ypos", "value2", "value3", "value4", "PlaneGeometry", "BatchableMesh", "transformedUvs", "textureMatrix", "MeshPipe", "mesh", "meshData", "isBatched", "geometry", "batch<PERSON><PERSON><PERSON>", "batcher", "batched", "gpuBatchableMesh", "gpuMesh", "getAdjustedBlendModeBlend", "GlParticleContainerAdaptor", "particleContainerPipe", "state", "gl", "buffer", "glType", "GpuParticleContainerAdaptor", "createIndicesForQuads", "size", "outBuffer", "totalIndices", "generateParticleUpdateFunction", "properties", "generateUpdateFunction", "dynamic", "funcFragments", "property", "attributeInfo", "getAttributeInfoFromFormat", "functionSource", "ParticleBuffer", "staticVertexSize", "dynamicVertexSize", "ViewableBuffer", "dynamicOffset", "staticOffset", "uploadFunction", "key", "getParticleSyncKey", "particles", "uploadStatic", "dynamicAttributeBuffer", "staticAttributeBuffer", "keyGen", "fragment", "vertex", "wgsl", "ParticleShader", "Shader", "glProgram", "GlProgram", "gpuProgram", "GpuProgram", "TextureStyle", "ParticleContainerPipe", "_renderable", "children", "transformationMatrix", "GlParticleContainerPipe", "GpuParticleContainerPipe", "_NineSliceGeometry", "w", "scaleW", "h", "scaleH", "scale", "_uvw", "_uvh", "NineSliceGeometry", "NineSliceSpritePipe", "gpuSprite", "batchableSprite", "tilingBit", "tilingBitGl", "TilingSpriteShader", "compileHighShaderGpuProgram", "localUniformBit", "roundPixelsBit", "compileHighShaderGlProgram", "localUniformBitGl", "roundPixelsBitGl", "tilingUniforms", "matrix", "anchorX", "anchorY", "texture", "textureWidth", "textureHeight", "uTextureTransform", "QuadGeometry", "setPositions", "tilingSprite", "applyMatrix", "array", "stride", "index", "a", "b", "c", "d", "tx", "ty", "setUvs", "sharedQuad", "TilingSpritePipe", "tilingSpriteData", "couldBatch", "canBatch", "renderableData", "_nonPowOf2wrapping", "localUniformMSDFBit", "localUniformMSDFBitGl", "mSDFBit", "mSDFBitGl", "SdfShader", "maxTextures", "getMaxTexturesPerBatch", "colorBit", "generateTextureBatchBit", "colorBitGl", "generateTextureBatchBitGl", "getBatchSamplersUniformGroup", "BitmapTextPipe", "bitmapText", "graphicsRenderable", "syncWithProxy", "renderableUid", "proxyGraphics", "bitmapFont", "BitmapFontManager", "chars", "currentY", "bitmapTextLayout", "getBitmapTextLayout", "tint", "line", "char", "char<PERSON><PERSON>", "proxyRenderable", "Graphics", "fontFamily", "dynamicFont", "<PERSON><PERSON>", "dx", "dy", "worldScale", "fontScale", "distance", "uid", "proxy", "updateTextBounds", "updateQuadBounds", "HTMLTextPipe", "gpuText", "htmlText", "new<PERSON>ey", "htmlTextUid", "e", "gpuTextData", "BatchableSprite", "<PERSON><PERSON><PERSON><PERSON>", "userAgent", "DOMAdapter", "tempBounds", "getPo2TextureFromSource", "image", "extractFontFamilies", "fontFamilies", "dedupe", "regex", "matches", "addFontFamily", "fontFamily2", "match", "loadFontAsBase64", "url", "blob", "reader", "resolve", "reject", "loadFontCSS", "dataSrc", "FontStylePromiseCache", "getFontCss", "defaultOptions", "fontPromises", "getSVGUrl", "fontCSS", "htmlTextData", "getTemporaryCanvasFromImage", "canvasAndContext", "CanvasPool", "loadSVGImage", "delay", "resolve2", "HTMLTextSystem", "<PERSON><PERSON><PERSON>", "promise", "measured", "uvSafeOffset", "svgURL", "resource", "activeTexture", "CanvasTextPipe", "textUid", "checkRow", "checkColumn", "top", "bottom", "getCanvasBoundingBox", "canvas", "left", "right", "Rectangle", "CanvasTextSystem", "_renderer", "CanvasTextMetrics", "nextPow2", "_text<PERSON>ey", "trimmed", "source", "font", "fontStringFromTextStyle", "lines", "lineHeight", "lineWidths", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "fontProperties", "strokeStyle", "linePositionX", "linePositionY", "passesCount", "isShadowPass", "dsOffsetText", "dsOffsetShadow", "shadowOptions", "dropShadowColor", "dropShadowAlpha", "dropShadowBlur", "dropShadowDistance", "getCanvasFillStyle", "linePositionYShift", "strokeWidth", "i2", "isStroke", "letterSpacing", "useExperimentalLetterSpacing", "currentPosition", "stringArray", "previousWidth", "currentWidth", "currentChar", "textStr", "extensions", "GraphicsContextSystem"], "mappings": "whBAGA,MAAMA,EAAa,CAOjB,OAAO,KAAKC,EAAS,CACnB,OAAO,eACL,KACA,WAQA,CACE,IAAIC,EAAK,CACP,WAAW,oBAAoB,SAAU,KAAK,WAAW,EACzD,KAAK,UAAYA,EACbA,IACF,WAAW,iBAAiB,SAAU,KAAK,WAAW,EACtD,KAAK,OAAM,EAEd,EACD,KAAM,CACJ,OAAO,KAAK,SACb,CACF,CACP,EACI,KAAK,YAAc,IAAM,CAClB,KAAK,YAGV,KAAK,cAAa,EAClB,KAAK,UAAY,sBAAsB,IAAM,KAAK,OAAQ,CAAA,EAChE,EACI,KAAK,cAAgB,IAAM,CACrB,KAAK,YACP,qBAAqB,KAAK,SAAS,EACnC,KAAK,UAAY,KAEzB,EACI,KAAK,OAAS,IAAM,CAClB,GAAI,CAAC,KAAK,UACR,OAEF,KAAK,cAAa,EAClB,IAAIC,EACAC,EACJ,GAAI,KAAK,YAAc,WAAW,OAChCD,EAAQ,WAAW,WACnBC,EAAS,WAAW,gBACf,CACL,KAAM,CAAE,YAAAC,EAAa,aAAAC,GAAiB,KAAK,UAC3CH,EAAQE,EACRD,EAASE,CACV,CACD,KAAK,SAAS,OAAOH,EAAOC,CAAM,EAClC,KAAK,OAAM,CACjB,EACI,KAAK,UAAY,KACjB,KAAK,UAAY,KACjB,KAAK,SAAWH,EAAQ,UAAY,IACrC,CAMD,OAAO,SAAU,CACf,WAAW,oBAAoB,SAAU,KAAK,WAAW,EACzD,KAAK,cAAa,EAClB,KAAK,cAAgB,KACrB,KAAK,YAAc,KACnB,KAAK,SAAW,KAChB,KAAK,OAAS,IACf,CACH,CAEAD,GAAa,UAAYO,EAAc,YChFvC,MAAMC,EAAa,CAOjB,OAAO,KAAKP,EAAS,CACnBA,EAAU,OAAO,OAAO,CACtB,UAAW,GACX,aAAc,EACf,EAAEA,CAAO,EACV,OAAO,eACL,KACA,SACA,CACE,IAAIQ,EAAQ,CACN,KAAK,SACP,KAAK,QAAQ,OAAO,KAAK,OAAQ,IAAI,EAEvC,KAAK,QAAUA,EACXA,GACFA,EAAO,IAAI,KAAK,OAAQ,KAAMC,GAAgB,GAAG,CAEpD,EACD,KAAM,CACJ,OAAO,KAAK,OACb,CACF,CACP,EACI,KAAK,KAAO,IAAM,CAChB,KAAK,QAAQ,MACnB,EACI,KAAK,MAAQ,IAAM,CACjB,KAAK,QAAQ,OACnB,EACI,KAAK,QAAU,KACf,KAAK,OAAST,EAAQ,aAAeU,GAAO,OAAS,IAAIA,GACrDV,EAAQ,WACV,KAAK,MAAK,CAEb,CAMD,OAAO,SAAU,CACf,GAAI,KAAK,QAAS,CAChB,MAAMW,EAAY,KAAK,QACvB,KAAK,OAAS,KACdA,EAAU,QAAO,CAClB,CACF,CACH,CAEAJ,GAAa,UAAYD,EAAc,YC1DvC,MAAMM,EAAW,CACf,YAAYC,EAAU,CACpB,KAAK,UAAYA,CAClB,CACD,KAAKC,EAAcC,EAAWC,EAAgB,CACxB,KAAK,UAAU,YACvB,MAAM,MAAMA,CAAc,EACtCA,EAAe,IAAI,CACjB,aAAc,SACd,UAAW,GACX,OAAQ,aACR,UAAAD,EACA,aAAAD,CACN,CAAK,CACF,CACD,IAAIG,EAAeC,EAAYF,EAAgB,CAC7C,KAAK,UAAU,YAAY,MAAM,MAAMA,CAAc,EACrDA,EAAe,IAAI,CACjB,aAAc,SACd,OAAQ,YACR,UAAW,EACjB,CAAK,CACF,CACD,QAAQG,EAAa,CACfA,EAAY,SAAW,aACzB,KAAK,UAAU,OAAO,KAAKA,CAAW,EAC7BA,EAAY,SAAW,aAChC,KAAK,UAAU,OAAO,KAEzB,CACD,SAAU,CACR,KAAK,UAAY,IAClB,CACH,CACAP,GAAW,UAAY,CACrB,KAAM,CACJN,EAAc,WACdA,EAAc,YACdA,EAAc,WACf,EACD,KAAM,QACR,EC3CA,SAASc,GAA0BC,EAAaC,EAAQ,CACtDA,EAAO,MAAK,EACZ,MAAMC,EAAaD,EAAO,OAC1B,QAASE,EAAI,EAAGA,EAAIH,EAAY,OAAQG,IAAK,CAC3C,MAAMC,EAAaJ,EAAYG,CAAC,EAC5BC,EAAW,oBAAsB,IAGrCH,EAAO,OAASG,EAAW,eAC3BH,EAAO,UAAUG,EAAW,MAAM,EACnC,CACD,OAAAH,EAAO,OAASC,EACTD,CACT,CCAA,MAAMI,GAAe,IAAIC,EAAS,CAChC,WAAY,CACV,UAAW,CACT,OAAQ,IAAI,aAAa,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,CAAC,EACjD,OAAQ,YACR,OAAQ,EAAI,EACZ,OAAQ,CACT,CACF,EACD,YAAa,IAAI,YAAY,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,CAAC,CACjD,CAAC,EACD,MAAMC,EAAa,CACjB,YAAYf,EAAU,CACpB,KAAK,kBAAoB,EACzB,KAAK,aAAe,GACpB,KAAK,sBAAwB,IAAIgB,EAAa,CAC5C,WAAY,CAAE,MAAO,IAAI,aAAa,CAAC,EAAG,KAAM,WAAa,EAC7D,YAAa,CAAE,MAAO,IAAI,aAAa,CAAC,EAAG,KAAM,WAAa,EAC9D,YAAa,CAAE,MAAO,IAAI,aAAa,CAAC,EAAG,KAAM,WAAa,EAC9D,aAAc,CAAE,MAAO,IAAI,aAAa,CAAC,EAAG,KAAM,WAAa,EAC/D,aAAc,CAAE,MAAO,IAAI,aAAa,CAAC,EAAG,KAAM,WAAa,EAC/D,eAAgB,CAAE,MAAO,IAAI,aAAa,CAAC,EAAG,KAAM,WAAa,CACvE,CAAK,EACD,KAAK,uBAAyB,IAAIC,GAAU,CAAE,CAAA,EAC9C,KAAK,SAAWjB,CACjB,CAKD,IAAI,mBAAoB,CACtB,OAAO,KAAK,mBAAmB,WAChC,CACD,KAAKM,EAAa,CAChB,MAAMN,EAAW,KAAK,SAChBkB,EAAUZ,EAAY,aAAa,QACpC,KAAK,aAAa,KAAK,iBAAiB,IAC3C,KAAK,aAAa,KAAK,iBAAiB,EAAI,KAAK,kBAEnD,MAAMa,EAAa,KAAK,aAAa,KAAK,iBAAiB,EAE3D,GADA,KAAK,oBACDD,EAAQ,SAAW,EAAG,CACxBC,EAAW,KAAO,GAClB,MACD,CACD,MAAMV,EAASU,EAAW,OAU1B,GATIb,EAAY,YACdC,GAA0BD,EAAY,YAAaG,CAAM,EAChDH,EAAY,aAAa,YAClCG,EAAO,MAAK,EACZA,EAAO,QAAQH,EAAY,aAAa,UAAU,EAClDG,EAAO,YAAYH,EAAY,UAAU,cAAc,GAEvDA,EAAY,UAAU,oBAAoB,GAAMG,CAAM,EAEpDH,EAAY,UAAW,CAEzB,MAAMc,GADcd,EAAY,UAAU,aAAeA,EAAY,UAAU,mBACtC,sBACrCc,GACFX,EAAO,YAAYW,CAAoB,CAE1C,CACD,MAAMC,EAAqBrB,EAAS,aAAa,aAAa,aAAa,OAC3E,IAAIsB,EAAa,IACbC,EAAU,EACVC,EAAY,GACZC,EAAgB,GAChBC,EAAU,GACVC,EAAiB,GACrB,QAAShB,EAAI,EAAGA,EAAIO,EAAQ,OAAQP,IAAK,CACvC,MAAMiB,EAASV,EAAQP,CAAC,EAYxB,GAXAW,EAAa,KAAK,IAAIA,EAAYM,EAAO,aAAe,UAAYP,EAAmB,YAAcO,EAAO,UAAU,EACtHL,GAAWK,EAAO,QACdA,EAAO,YAAc,MACvBJ,EAAY,GACHI,EAAO,YAAc,WAC9BJ,IAAcA,EAAYH,EAAmB,WAE1CO,EAAO,iBACVD,EAAiB,IAGf,CADiB,CAAC,EAAEC,EAAO,oBAAsB5B,EAAS,MAC3C,CACjB0B,EAAU,GACV,KACD,CACD,GAAIE,EAAO,eAAiB,EAAE5B,EAAS,YAAY,eAAiB,IAAO,CACzE6B,EAAK,sHAAsH,EAC3HH,EAAU,GACV,KACD,CACDA,EAAUE,EAAO,SAAWF,EAC5BD,IAAkBA,EAAgBG,EAAO,cAC1C,CACD,GAAI,CAACF,EAAS,CACZP,EAAW,KAAO,GAClB,MACD,CACD,GAAIQ,EAAgB,CAClB,MAAMG,EAAW9B,EAAS,aAAa,aACjC+B,EAAiB/B,EAAS,aAAa,aAAa,WAC1DS,EAAO,UAAU,EAAGqB,EAAS,MAAQC,EAAgB,EAAGD,EAAS,OAASC,CAAc,CACzF,CAED,GADAtB,EAAO,MAAMa,CAAU,EAAE,KAAI,EAAG,MAAM,EAAIA,CAAU,EAAE,IAAIC,EAAU,CAAC,EACjE,CAACd,EAAO,WAAY,CACtBU,EAAW,KAAO,GAClB,MACD,CACDA,EAAW,KAAO,GAClBA,EAAW,OAASV,EACpBU,EAAW,cAAgBM,EAC3BN,EAAW,UAAYb,EAAY,UACnCa,EAAW,aAAeb,EAAY,aACtCa,EAAW,sBAAwBnB,EAAS,aAAa,cACzDmB,EAAW,aAAea,EAAY,kBACpCvB,EAAO,MACPA,EAAO,OACPa,EACAE,CACN,EACIxB,EAAS,aAAa,KAAKmB,EAAW,aAAc,EAAI,EACxDnB,EAAS,eAAe,KAAK,CAC3B,OAAQS,CACd,CAAK,CACF,CACD,KAAM,CACJ,MAAMT,EAAW,KAAK,SACtB,KAAK,oBACL,MAAMmB,EAAa,KAAK,aAAa,KAAK,iBAAiB,EAC3D,GAAIA,EAAW,KACb,OAEF,KAAK,kBAAoBA,EACzB,MAAMc,EAAed,EAAW,aAC1BV,EAASU,EAAW,OAC1B,IAAIe,EAAcC,EAAQ,MAE1B,GADAnC,EAAS,aAAa,mBAClBmB,EAAW,cAAe,CAC5B,MAAMiB,EAAiB,KAAK,kBAAoB,EAAI,KAAK,aAAa,KAAK,kBAAoB,CAAC,EAAE,OAAS,KACrGC,EAAerC,EAAS,aAAa,gBAAgBmB,EAAW,qBAAqB,EAC3Fe,EAAc,KAAK,eAAeG,EAAc5B,EAAQ2B,CAAc,CACvE,CACDjB,EAAW,YAAce,EACzB,MAAMhB,EAAUC,EAAW,aAAa,QAIxC,GAHA,KAAK,uBAAuB,YAAYc,EAAa,OAAO,MAAO,CAAC,EACpE,KAAK,uBAAuB,YAAYC,EAAY,OAAQ,CAAC,EAC7DlC,EAAS,eAAe,MACpBkB,EAAQ,SAAW,EACrBA,EAAQ,CAAC,EAAE,MAAM,KAAMe,EAAcd,EAAW,sBAAuB,EAAK,EAC5Ea,EAAY,cAAcC,CAAY,MACjC,CACL,IAAIK,EAAOnB,EAAW,aAClBoB,EAAOP,EAAY,kBACrBvB,EAAO,MACPA,EAAO,OACP6B,EAAK,OAAO,YACZ,EACR,EACU3B,EAAI,EACR,IAAKA,EAAI,EAAGA,EAAIO,EAAQ,OAAS,EAAG,EAAEP,EAAG,CACxBO,EAAQP,CAAC,EACjB,MAAM,KAAM2B,EAAMC,EAAM,EAAI,EACnC,MAAMC,EAAIF,EACVA,EAAOC,EACPA,EAAOC,CACR,CACDtB,EAAQP,CAAC,EAAE,MAAM,KAAM2B,EAAMnB,EAAW,sBAAuB,EAAK,EACpEa,EAAY,cAAcM,CAAI,EAC9BN,EAAY,cAAcO,CAAI,CAC/B,CACGpB,EAAW,eACba,EAAY,cAAcE,CAAW,CAExC,CACD,eAAeO,EAAmBhC,EAAQ2B,EAAgB,CACxD,MAAMM,EAAuBD,EAAkB,aAAa,OAAO,YAC7DP,EAAcF,EAAY,kBAC9BvB,EAAO,MACPA,EAAO,OACPiC,EACA,EACN,EACI,IAAIC,EAAIlC,EAAO,KACXmC,EAAInC,EAAO,KACX2B,IACFO,GAAKP,EAAe,KACpBQ,GAAKR,EAAe,MAEtBO,EAAI,KAAK,MAAMA,EAAID,CAAoB,EACvCE,EAAI,KAAK,MAAMA,EAAIF,CAAoB,EACvC,MAAMrD,EAAQ,KAAK,KAAKoB,EAAO,MAAQiC,CAAoB,EACrDpD,EAAS,KAAK,KAAKmB,EAAO,OAASiC,CAAoB,EAC7D,YAAK,SAAS,aAAa,cACzBD,EACAP,EACA,CAAE,EAAAS,EAAG,EAAAC,CAAG,EACR,CAAE,MAAAvD,EAAO,OAAAC,CAAQ,EACjB,CAAE,EAAG,EAAG,EAAG,CAAG,CACpB,EACW4C,CACR,CACD,YAAYN,EAAQiB,EAAOC,EAAQC,EAAO,CACxC,MAAM/C,EAAW,KAAK,SAChBmB,EAAa,KAAK,aAAa,KAAK,iBAAiB,EACrDV,EAASU,EAAW,OACpB6B,EAASC,GAAM,OAEfC,EADwB/B,EAAW,wBACO2B,EAChD,IAAIxB,EAAa,KAAK,SAAS,aAAa,iBAAiB,aAAa,OAAO,YAC7E6B,EAAe,KAAK,kBAAoB,EAC5C,KAAOA,EAAe,GAAK,KAAK,aAAaA,CAAY,EAAE,MACzD,EAAEA,EAEAA,EAAe,IACjB7B,EAAa,KAAK,aAAa6B,CAAY,EAAE,aAAa,OAAO,aAEnE,MAAMC,EAAiB,KAAK,sBACtBC,EAAWD,EAAe,SAC1BE,EAAcD,EAAS,aACvBE,EAAYF,EAAS,WACrBG,EAAaH,EAAS,YACtBI,EAAaJ,EAAS,YACtBK,EAAcL,EAAS,aACvBM,EAAgBN,EAAS,eAC/B,GAAIH,EAAe,CACjB,IAAIU,EAAY,KAAK,kBACrB,KAAOA,EAAY,GAAG,CACpBA,IACA,MAAMC,EAAc,KAAK,aAAa,KAAK,kBAAoB,CAAC,EAChE,GAAI,CAACA,EAAY,KAAM,CACrBb,EAAO,EAAIa,EAAY,OAAO,KAC9Bb,EAAO,EAAIa,EAAY,OAAO,KAC9B,KACD,CACF,CACDP,EAAY,CAAC,EAAI7C,EAAO,KAAOuC,EAAO,EACtCM,EAAY,CAAC,EAAI7C,EAAO,KAAOuC,EAAO,CAC5C,MACMM,EAAY,CAAC,EAAI,EACjBA,EAAY,CAAC,EAAI,EAEnBA,EAAY,CAAC,EAAIT,EAAM,MAAM,MAC7BS,EAAY,CAAC,EAAIT,EAAM,MAAM,OAC7BU,EAAU,CAAC,EAAIV,EAAM,OAAO,MAC5BU,EAAU,CAAC,EAAIV,EAAM,OAAO,OAC5BU,EAAU,CAAC,EAAI,EAAIA,EAAU,CAAC,EAC9BA,EAAU,CAAC,EAAI,EAAIA,EAAU,CAAC,EAC9BC,EAAW,CAAC,EAAIX,EAAM,OAAO,WAC7BW,EAAW,CAAC,EAAIX,EAAM,OAAO,YAC7BW,EAAW,CAAC,EAAI,EAAIA,EAAW,CAAC,EAChCA,EAAW,CAAC,EAAI,EAAIA,EAAW,CAAC,EAChCC,EAAW,CAAC,EAAI,GAAMD,EAAW,CAAC,EAClCC,EAAW,CAAC,EAAI,GAAMD,EAAW,CAAC,EAClCC,EAAW,CAAC,EAAIZ,EAAM,MAAM,MAAQU,EAAU,CAAC,EAAI,GAAMC,EAAW,CAAC,EACrEC,EAAW,CAAC,EAAIZ,EAAM,MAAM,OAASU,EAAU,CAAC,EAAI,GAAMC,EAAW,CAAC,EACtE,MAAMM,EAAc,KAAK,SAAS,aAAa,iBAAiB,aAChEJ,EAAY,CAAC,EAAIV,EAAO,EAAI1B,EAC5BoC,EAAY,CAAC,EAAIV,EAAO,EAAI1B,EAC5BoC,EAAY,CAAC,EAAII,EAAY,OAAO,MAAQxC,EAC5CoC,EAAY,CAAC,EAAII,EAAY,OAAO,OAASxC,EAC7C,MAAMe,EAAe,KAAK,SAAS,aAAa,gBAAgBS,CAAM,EAWtE,GAVA9C,EAAS,aAAa,KAAK8C,EAAQ,CAAC,CAACC,CAAK,EACtCD,aAAkBX,GACpBwB,EAAc,CAAC,EAAIb,EAAO,MAAM,MAChCa,EAAc,CAAC,EAAIb,EAAO,MAAM,SAEhCa,EAAc,CAAC,EAAItB,EAAa,MAChCsB,EAAc,CAAC,EAAItB,EAAa,QAElCsB,EAAc,CAAC,EAAItB,EAAa,OAAS,GAAK,EAC9Ce,EAAe,OAAM,EACjBpD,EAAS,YAAY,aAAc,CACrC,MAAM+D,EAAgB/D,EAAS,YAAY,aAAa,eAAeoD,CAAc,EACrF,KAAK,uBAAuB,YAAYW,EAAe,CAAC,CAC9D,MACM,KAAK,uBAAuB,YAAYX,EAAgB,CAAC,EAE3D,KAAK,uBAAuB,YAAYP,EAAM,OAAQ,CAAC,EACvD,KAAK,uBAAuB,YAAYA,EAAM,OAAO,MAAO,CAAC,EAC7DjB,EAAO,OAAO,CAAC,EAAI,KAAK,uBACxB5B,EAAS,QAAQ,KAAK,CACpB,SAAUa,GACV,OAAQe,EACR,MAAOA,EAAO,OACd,SAAU,eAChB,CAAK,EACG5B,EAAS,OAASgE,EAAa,OACjChE,EAAS,aAAa,kBAEzB,CACD,gBAAiB,CACf,MAAO,CACL,KAAM,GACN,aAAc,KACd,OAAQ,IAAIiE,GACZ,UAAW,KACX,aAAc,KACd,cAAe,GACf,sBAAuB,IAC7B,CACG,CASD,sBAAsBC,EAAcC,EAAQ,CAC1C,MAAMC,EAAO,KAAK,kBACZC,EAAeH,EAAa,IAChCE,EAAK,aAAa,QAAQ,MAC1B,EACA,EACAA,EAAK,aAAa,QAAQ,OAC1BA,EAAK,OAAO,KACZA,EAAK,OAAO,IAClB,EACUE,EAAiBH,EAAO,eAAe,OAAOI,EAAO,MAAM,EAC3DC,EAAcL,EAAO,aAAeA,EAAO,kBACjD,OAAIK,GAAeA,EAAY,uBAC7BF,EAAe,QAAQE,EAAY,qBAAqB,EAE1DF,EAAe,OAAM,EACrBD,EAAa,QAAQC,CAAc,EACnCD,EAAa,MACX,EAAIF,EAAO,QAAQ,MAAM,MACzB,EAAIA,EAAO,QAAQ,MAAM,MAC/B,EACIE,EAAa,UAAUF,EAAO,OAAO,EAAGA,EAAO,OAAO,CAAC,EAChDE,CACR,CACH,CAEAtD,GAAa,UAAY,CACvB,KAAM,CACJtB,EAAc,YACdA,EAAc,YACf,EACD,KAAM,QACR,EC7VA,MAAMgF,GAAgB,MAAMA,WAAsB3D,CAAS,CACzD,eAAe4D,EAAM,CACnB,IAAIvF,EAAUuF,EAAK,CAAC,GAAK,CAAA,EACrBvF,aAAmB,eACrBwF,EAAYC,GAAQ,2DAA2D,EAC/EzF,EAAU,CACR,UAAWA,EACX,IAAKuF,EAAK,CAAC,EACX,QAASA,EAAK,CAAC,CACvB,GAEIvF,EAAU,CAAE,GAAGsF,GAAc,eAAgB,GAAGtF,CAAO,EACvD,MAAM0F,EAAY1F,EAAQ,WAAa,IAAI,aAAa,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,CAAC,EAChF,IAAI2F,EAAM3F,EAAQ,IACb2F,IACC3F,EAAQ,UACV2F,EAAM,IAAI,aAAaD,EAAU,MAAM,EAEvCC,EAAM,IAAI,aAAa,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,CAAC,GAGnD,MAAMC,EAAU5F,EAAQ,SAAW,IAAI,YAAY,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,CAAC,EAC/D6F,EAAc7F,EAAQ,mBACtB8F,EAAiB,IAAIC,EAAO,CAChC,KAAML,EACN,MAAO,2BACP,YAAAG,EACA,MAAOG,EAAY,OAASA,EAAY,QAC9C,CAAK,EACKC,EAAW,IAAIF,EAAO,CAC1B,KAAMJ,EACN,MAAO,qBACP,YAAAE,EACA,MAAOG,EAAY,OAASA,EAAY,QAC9C,CAAK,EACKE,EAAc,IAAIH,EAAO,CAC7B,KAAMH,EACN,MAAO,oBACP,YAAAC,EACA,MAAOG,EAAY,MAAQA,EAAY,QAC7C,CAAK,EACD,MAAM,CACJ,WAAY,CACV,UAAW,CACT,OAAQF,EACR,OAAQ,YACR,OAAQ,EAAI,EACZ,OAAQ,CACT,EACD,IAAK,CACH,OAAQG,EACR,OAAQ,YACR,OAAQ,EAAI,EACZ,OAAQ,CACT,CACF,EACD,YAAAC,EACA,SAAUlG,EAAQ,QACxB,CAAK,EACD,KAAK,UAAY,MAClB,CAED,IAAI,WAAY,CACd,OAAO,KAAK,WAAW,UAAU,OAAO,IACzC,CAOD,IAAI,UAAUmG,EAAO,CACnB,KAAK,WAAW,UAAU,OAAO,KAAOA,CACzC,CAED,IAAI,KAAM,CACR,OAAO,KAAK,WAAW,IAAI,OAAO,IACnC,CAOD,IAAI,IAAIA,EAAO,CACb,KAAK,WAAW,IAAI,OAAO,KAAOA,CACnC,CAED,IAAI,SAAU,CACZ,OAAO,KAAK,YAAY,IACzB,CACD,IAAI,QAAQA,EAAO,CACjB,KAAK,YAAY,KAAOA,CACzB,CACH,EACAb,GAAc,eAAiB,CAC7B,SAAU,gBACV,mBAAoB,EACtB,EACA,IAAIc,GAAed,GCtGnB,SAASe,GAAeC,EAAO,CAC7B,MAAMC,EAASD,EAAM,QACfE,EAAOF,EAAM,MAqBbG,EAAY,CAAC,SApBI,CACrB,UAAUC,EAAM,OAAO,SAASF,EAAK,KAAK,EAAE,MAAK,CAAE,GACnD,cAAcF,EAAM,QAAQ,KAC5B,gBAAgBA,EAAM,UAAU,GAChC,gBAAgBA,EAAM,UAAU,GAChC,eAAeA,EAAM,SAAS,GAC9B,iBAAiBA,EAAM,WAAW,GAClC,mBAAmBA,EAAM,aAAa,KACtC,eAAeA,EAAM,KAAK,GAC1B,YAAYA,EAAM,OAAO,KACzB,gBAAgBA,EAAM,aAAe,OAASA,EAAM,SAAW,WAAaA,EAAM,UAAU,GAC5F,GAAGA,EAAM,WAAa,CAAC,gBAAgBA,EAAM,UAAU,IAAI,EAAI,CAAE,EACjE,GAAGA,EAAM,SAAW,CAClB,cAAcA,EAAM,WAAa,YAAc,YAAY,GAC3D,cAAcA,EAAM,aAAa,IACvC,EAAQ,CAAE,EACN,GAAGC,EAAS,CAACI,GAAYJ,CAAM,CAAC,EAAI,CAAE,EACtC,GAAGD,EAAM,WAAa,CAACM,GAAgBN,EAAM,UAAU,CAAC,EAAI,CAAE,EAC9D,GAAGA,EAAM,YACb,EAAI,KAAK,GAAG,CACgC,IAAI,EAC9C,OAAAO,GAAcP,EAAM,UAAWG,CAAS,EACjCA,EAAU,KAAK,GAAG,CAC3B,CACA,SAASG,GAAgBE,EAAiB,CACxC,MAAMC,EAAQL,EAAM,OAAO,SAASI,EAAgB,KAAK,EAAE,SAASA,EAAgB,KAAK,EAAE,OAAM,EAC3FtD,EAAI,KAAK,MAAM,KAAK,IAAIsD,EAAgB,KAAK,EAAIA,EAAgB,QAAQ,EACzErD,EAAI,KAAK,MAAM,KAAK,IAAIqD,EAAgB,KAAK,EAAIA,EAAgB,QAAQ,EACzEE,EAAW,GAAGxD,CAAC,MAAMC,CAAC,KAC5B,OAAIqD,EAAgB,KAAO,EAClB,gBAAgBE,CAAQ,IAAIF,EAAgB,IAAI,MAAMC,CAAK,GAE7D,gBAAgBC,CAAQ,IAAID,CAAK,EAC1C,CACA,SAASJ,GAAYJ,EAAQ,CAC3B,MAAO,CACL,8BAA8BA,EAAO,KAAK,KAC1C,8BAA8BG,EAAM,OAAO,SAASH,EAAO,KAAK,EAAE,MAAK,CAAE,GACzE,sBAAsBA,EAAO,KAAK,KAClC,sBAAsBG,EAAM,OAAO,SAASH,EAAO,KAAK,EAAE,MAAK,CAAE,GACjE,qBACJ,EAAI,KAAK,GAAG,CACZ,CACA,MAAMU,GAAY,CAChB,SAAU,yBACV,WAAY,yBACZ,WAAY,yBACZ,UAAW,wBACX,YAAa,0BACb,cAAe,8BACf,MAAO,wBACP,QAAS,uBACT,WAAY,yBACZ,WAAY,2BACZ,cAAe,wBACjB,EACMC,GAAY,CAChB,KAAOf,GAAU,UAAUO,EAAM,OAAO,SAASP,CAAK,EAAE,MAAK,CAAE,GAC/D,WAAaA,GAAU,cAAcA,EAAQ,YAAc,YAAY,GACvE,OAAQQ,GACR,WAAYC,EACd,EACA,SAASC,GAAcM,EAAWC,EAAK,CACrC,UAAW5F,KAAK2F,EAAW,CACzB,MAAME,EAAWF,EAAU3F,CAAC,EACtB8F,EAAc,CAAA,EACpB,UAAWC,KAAKF,EACVH,GAAUK,CAAC,EACbD,EAAY,KAAKJ,GAAUK,CAAC,EAAEF,EAASE,CAAC,CAAC,CAAC,EACjCN,GAAUM,CAAC,GACpBD,EAAY,KAAKL,GAAUM,CAAC,EAAE,QAAQ,YAAaF,EAASE,CAAC,CAAC,CAAC,EAGnEH,EAAI,KAAK,GAAG5F,CAAC,MAAM8F,EAAY,KAAK,GAAG,CAAC,IAAI,CAC7C,CACH,CC3EA,MAAME,WAAsBC,CAAU,CACpC,YAAYzH,EAAU,GAAI,CACxB,MAAMA,CAAO,EACb,KAAK,cAAgB,GACrB,KAAK,eAAiB,KAAK,aAAeA,EAAQ,cAClD,KAAK,UAAYA,EAAQ,WAAa,CAAA,CACvC,CAED,IAAI,aAAamG,EAAO,CACtB,KAAK,cAAgBA,aAAiB,MAAQA,EAAQ,CAACA,CAAK,EAC5D,KAAK,OAAM,CACZ,CACD,IAAI,cAAe,CACjB,OAAO,KAAK,aACb,CACD,cAAe,CACb,YAAK,UAAYuB,GAAqB,IAAI,EAAI,KAAK,cAAc,KAAK,GAAG,EAClE,KAAK,SACb,CACD,QAAS,CACP,KAAK,UAAY,KACjB,MAAM,OAAM,CACb,CAKD,OAAQ,CACN,OAAO,IAAIF,GAAc,CACvB,MAAO,KAAK,MACZ,WAAY,KAAK,WACjB,WAAY,KAAK,WAAa,CAAE,GAAG,KAAK,UAAU,EAAK,KACvD,KAAM,KAAK,MACX,WAAY,KAAK,WACjB,SAAU,KAAK,SACf,UAAW,KAAK,UAChB,YAAa,KAAK,YAClB,WAAY,KAAK,WACjB,cAAe,KAAK,cACpB,WAAY,KAAK,WACjB,QAAS,KAAK,QACd,OAAQ,KAAK,QACb,WAAY,KAAK,WACjB,SAAU,KAAK,SACf,cAAe,KAAK,cACpB,aAAc,KAAK,YACzB,CAAK,CACF,CACD,IAAI,UAAW,CACb,OAAK,KAAK,YACR,KAAK,UAAYnB,GAAe,IAAI,GAE/B,KAAK,SACb,CAUD,eAAeF,EAAO,CACpB,MAAMwB,EAAQxB,EAAM,OAAQyB,GAAM,CAAC,KAAK,aAAa,SAASA,CAAC,CAAC,EAC5DD,EAAM,OAAS,IACjB,KAAK,aAAa,KAAK,GAAGA,CAAK,EAC/B,KAAK,OAAM,EAEd,CAOD,kBAAkBxB,EAAO,CACvB,MAAM0B,EAAW1B,EAAM,OAAQyB,GAAM,KAAK,aAAa,SAASA,CAAC,CAAC,EAC9DC,EAAS,OAAS,IACpB,KAAK,aAAe,KAAK,aAAa,OAAQD,GAAM,CAACC,EAAS,SAASD,CAAC,CAAC,EACzE,KAAK,OAAM,EAEd,CACD,IAAI,KAAKzB,EAAO,CACV,OAAOA,GAAU,UAAY,OAAOA,GAAU,UAChDzD,EAAK,8DAA8D,EAErE,MAAM,KAAOyD,CACd,CACD,IAAI,OAAOA,EAAO,CACZA,GAAS,OAAOA,GAAU,UAAY,OAAOA,GAAU,UACzDzD,EAAK,gEAAgE,EAEvE,MAAM,OAASyD,CAChB,CACH,CCpGA,MAAM2B,GAAQ,6BACRC,GAAU,+BAChB,MAAMC,EAAmB,CACvB,aAAc,CACZ,KAAK,QAAU,SAAS,gBAAgBF,GAAO,KAAK,EACpD,KAAK,cAAgB,SAAS,gBAAgBA,GAAO,eAAe,EACpE,KAAK,WAAa,SAAS,gBAAgBC,GAAS,KAAK,EACzD,KAAK,aAAe,SAAS,gBAAgBA,GAAS,OAAO,EAC7D,KAAK,MAAQ,IAAI,MACjB,KAAM,CAAE,cAAAE,EAAe,QAAAC,EAAS,aAAAC,EAAc,WAAAC,CAAU,EAAK,KAC7DH,EAAc,aAAa,QAAS,OAAO,EAC3CA,EAAc,aAAa,SAAU,OAAO,EAC5CA,EAAc,MAAM,SAAW,SAC/BC,EAAQ,YAAYD,CAAa,EACjCA,EAAc,YAAYE,CAAY,EACtCF,EAAc,YAAYG,CAAU,CACrC,CACH,CCfA,IAAIC,GACJ,SAASC,GAAgBC,EAAMjC,EAAOkC,EAAcC,EAAoB,CACtEA,IAAuBA,EAAqBJ,KAA2BA,GAAyB,IAAIL,KACpG,KAAM,CAAE,WAAAI,EAAY,aAAAD,EAAc,QAAAD,CAAO,EAAKO,EAC9CL,EAAW,UAAY,UAAU9B,EAAM,QAAQ,mCAAmCiC,CAAI,SACtFH,EAAW,aAAa,QAAS,mDAAmD,EAChFI,IACFL,EAAa,YAAcK,GAE7B,SAAS,KAAK,YAAYN,CAAO,EACjC,MAAMQ,EAAgBN,EAAW,wBACjCF,EAAQ,OAAM,EACd,MAAMS,EAAgBrC,EAAM,QAAU,EACtC,MAAO,CACL,MAAOoC,EAAc,MAAQC,EAC7B,OAAQD,EAAc,OAASC,CACnC,CACA,CCbA,MAAMC,EAAa,CACjB,YAAY/H,EAAUgI,EAAS,CAC7B,KAAK,MAAQC,EAAM,QAEnB,KAAK,qBAAuC,OAAO,OAAO,IAAI,EAC9D,KAAK,wBAA0B,KAAK,kBAAkB,KAAK,IAAI,EAC/D,KAAK,SAAWjI,EAChB,KAAK,SAAWgI,EAChB,KAAK,SAAS,OACd,KAAK,SAAS,aAAa,eAAe,KAAM,sBAAsB,CACvE,CACD,mBAAmBE,EAAU,CAC3B,MAAMC,EAAUD,EAAS,QACnBE,EAAa,CAAC,CAAC,KAAK,qBAAqBF,EAAS,GAAG,EACrDG,EAAa,KAAK,SAAS,gBAAgB,iBAAiBF,CAAO,EACzE,MAAI,GAAAE,EAAW,aAAeD,IAAeC,EAAW,YAIzD,CACD,cAAcH,EAAU/H,EAAgB,CACtC,MAAMkI,EAAa,KAAK,SAAS,gBAAgB,iBAAiBH,EAAS,OAAO,EAC9EA,EAAS,eACX,KAAK,SAASA,CAAQ,EAEpBG,EAAW,YACb,KAAK,cAAcH,EAAU/H,CAAc,GAE3C,KAAK,SAAS,YAAY,MAAM,MAAMA,CAAc,EACpDA,EAAe,IAAI+H,CAAQ,EAE9B,CACD,iBAAiBA,EAAU,CACzB,MAAMI,EAAU,KAAK,qBAAqBJ,EAAS,GAAG,EACtD,GAAII,EACF,QAAS3H,EAAI,EAAGA,EAAI2H,EAAQ,OAAQ3H,IAAK,CACvC,MAAM4H,EAAQD,EAAQ3H,CAAC,EACvB4H,EAAM,SAAS,cAAcA,CAAK,CACnC,CAEJ,CACD,kBAAkBL,EAAU,CACtB,KAAK,qBAAqBA,EAAS,GAAG,GACxC,KAAK,0BAA0BA,EAAS,GAAG,EAE7CA,EAAS,IAAI,YAAa,KAAK,uBAAuB,CACvD,CACD,QAAQA,EAAU,CAChB,GAAI,CAACA,EAAS,aACZ,OACF,MAAMlI,EAAW,KAAK,SAChBmI,EAAUD,EAAS,QAEzB,GAAI,CADkBlI,EAAS,gBACZ,cAAcmI,CAAO,EAAE,QAAQ,OAChD,OAEF,MAAMK,EAASL,EAAQ,cAAgB,KAAK,SAAS,OACrD,KAAK,MAAM,UAAYD,EAAS,eAChC,MAAMO,EAAgBD,EAAO,UAAU,cAAc,SACrDC,EAAc,iBAAmBP,EAAS,eAC1CO,EAAc,OAASzI,EAAS,aAAekI,EAAS,aACxDQ,EACER,EAAS,gBACTO,EAAc,OACd,CACN,EACI,KAAK,SAAS,QAAQ,KAAMP,CAAQ,CACrC,CACD,SAASA,EAAU,CACjB,MAAME,EAAa,CAAC,CAAC,KAAK,qBAAqBF,EAAS,GAAG,EACrDG,EAAa,KAAK,SAAS,gBAAgB,iBAAiBH,EAAS,OAAO,EAC9EE,GACF,KAAK,0BAA0BF,EAAS,GAAG,EAEzCG,EAAW,aACb,KAAK,0BAA0BH,CAAQ,EAEzCA,EAAS,QAAUG,EAAW,WAC/B,CACD,cAAcH,EAAU/H,EAAgB,CACtC,MAAMwI,EAAY,KAAK,SAAS,YAAY,MACtCL,EAAU,KAAK,yBAAyBJ,CAAQ,EACtD,QAASvH,EAAI,EAAGA,EAAI2H,EAAQ,OAAQ3H,IAAK,CACvC,MAAM4H,EAAQD,EAAQ3H,CAAC,EACvBgI,EAAU,WAAWJ,EAAOpI,CAAc,CAC3C,CACF,CACD,yBAAyB+H,EAAU,CACjC,OAAO,KAAK,qBAAqBA,EAAS,GAAG,GAAK,KAAK,0BAA0BA,CAAQ,CAC1F,CACD,0BAA0BA,EAAU,CAClC,MAAMC,EAAUD,EAAS,QACnBG,EAAa,KAAK,SAAS,gBAAgB,cAAcF,CAAO,EAChES,EAAc,KAAK,SAAS,aAAeV,EAAS,aACpDI,EAAUD,EAAW,QAAQ,IAAKE,GAAU,CAChD,MAAMM,EAAaC,EAAQ,IAAIC,EAAiB,EAChD,OAAAR,EAAM,OAAOM,CAAU,EACvBA,EAAW,WAAaX,EACxBW,EAAW,YAAcD,EAClBC,CACb,CAAK,EACD,OAAI,KAAK,qBAAqBX,EAAS,GAAG,IAAM,QAC9CA,EAAS,GAAG,YAAa,KAAK,uBAAuB,EAEvD,KAAK,qBAAqBA,EAAS,GAAG,EAAII,EACnCA,CACR,CACD,0BAA0BU,EAAa,CACrC,KAAK,qBAAqBA,CAAW,EAAE,QAAST,GAAU,CACxDO,EAAQ,OAAOP,CAAK,CAC1B,CAAK,EACD,KAAK,qBAAqBS,CAAW,EAAI,IAC1C,CACD,SAAU,CACR,KAAK,SAAW,KAChB,KAAK,SAAS,UACd,KAAK,SAAW,KAChB,KAAK,MAAQ,KACb,UAAWrI,KAAK,KAAK,qBACnB,KAAK,0BAA0BA,CAAC,EAElC,KAAK,qBAAuB,IAC7B,CACH,CAEAoH,GAAa,UAAY,CACvB,KAAM,CACJtI,EAAc,WACdA,EAAc,YACdA,EAAc,WACf,EACD,KAAM,UACR,ECvIA,MAAMwJ,GAAiB,MAAMA,WAAuB1D,EAAa,CAC/D,eAAeb,EAAM,CACnB,MAAM,CAAE,CAAA,EACR,IAAIvF,EAAUuF,EAAK,CAAC,GAAK,CAAA,EACrB,OAAOvF,GAAY,WACrBwF,EAAYC,GAAQ,8FAA8F,EAClHzF,EAAU,CACR,MAAOA,EACP,OAAQuF,EAAK,CAAC,EACd,UAAWA,EAAK,CAAC,EACjB,UAAWA,EAAK,CAAC,CACzB,GAEI,KAAK,MAAMvF,CAAO,CACnB,CAKD,MAAMA,EAAS,CACbA,EAAU,CAAE,GAAG8J,GAAe,eAAgB,GAAG9J,CAAO,EACxD,KAAK,UAAY,KAAK,WAAaA,EAAQ,UAC3C,KAAK,UAAY,KAAK,WAAaA,EAAQ,UAC3C,KAAK,MAAQ,KAAK,OAASA,EAAQ,MACnC,KAAK,OAAS,KAAK,QAAUA,EAAQ,OACrC,MAAM+J,EAAQ,KAAK,UAAY,KAAK,UAC9BC,EAAQ,CAAA,EACRrE,EAAM,CAAA,EACNC,EAAU,CAAA,EACVqE,EAAY,KAAK,UAAY,EAC7BC,EAAY,KAAK,UAAY,EAC7BC,EAAQ,KAAK,MAAQF,EACrBG,EAAQ,KAAK,OAASF,EAC5B,QAAS1I,EAAI,EAAGA,EAAIuI,EAAOvI,IAAK,CAC9B,MAAMgC,EAAIhC,EAAI,KAAK,UACbiC,EAAIjC,EAAI,KAAK,UAAY,EAC/BwI,EAAM,KAAKxG,EAAI2G,EAAO1G,EAAI2G,CAAK,EAC/BzE,EAAI,KAAKnC,EAAIyG,EAAWxG,EAAIyG,CAAS,CACtC,CACD,MAAMG,EAAWJ,EAAYC,EAC7B,QAAS1I,EAAI,EAAGA,EAAI6I,EAAU7I,IAAK,CACjC,MAAM8I,EAAO9I,EAAIyI,EACXM,EAAO/I,EAAIyI,EAAY,EACvB9D,EAAQoE,EAAO,KAAK,UAAYD,EAChCE,EAASD,EAAO,KAAK,UAAYD,EAAO,EACxCG,GAAUF,EAAO,GAAK,KAAK,UAAYD,EACvCI,GAAUH,EAAO,GAAK,KAAK,UAAYD,EAAO,EACpD1E,EAAQ,KACNO,EACAqE,EACAC,EACAD,EACAE,EACAD,CACR,CACK,CACD,KAAK,QAAQ,CAAC,EAAE,KAAO,IAAI,aAAaT,CAAK,EAC7C,KAAK,QAAQ,CAAC,EAAE,KAAO,IAAI,aAAarE,CAAG,EAC3C,KAAK,YAAY,KAAO,IAAI,YAAYC,CAAO,EAC/C,KAAK,QAAQ,CAAC,EAAE,OAAM,EACtB,KAAK,QAAQ,CAAC,EAAE,OAAM,EACtB,KAAK,YAAY,QAClB,CACH,EACAkE,GAAe,eAAiB,CAC9B,MAAO,IACP,OAAQ,IACR,UAAW,GACX,UAAW,EACb,EACA,IAAIa,GAAgBb,GCzEpB,MAAMc,EAAc,CAClB,aAAc,CACZ,KAAK,YAAc,UACnB,KAAK,WAAa,GAClB,KAAK,YAAc,EACnB,KAAK,gBAAkB,EACvB,KAAK,YAAc,EACnB,KAAK,SAAW,KAChB,KAAK,OAAS,KACd,KAAK,YAAc,GACnB,KAAK,uBAAyB,EAC/B,CACD,IAAI,WAAY,CACd,OAAO,KAAK,WAAW,cACxB,CACD,IAAI,UAAW,CACb,OAAO,KAAK,WAAa,KAAK,SAAS,QACxC,CACD,IAAI,SAASzE,EAAO,CAClB,KAAK,UAAYA,CAClB,CACD,OAAQ,CACN,KAAK,WAAa,KAClB,KAAK,QAAU,KACf,KAAK,SAAW,KAChB,KAAK,OAAS,KACd,KAAK,SAAW,KAChB,KAAK,YAAc,GACnB,KAAK,uBAAyB,EAC/B,CAOD,WAAWA,EAAO,CACZ,KAAK,UAAYA,IAErB,KAAK,QAAUA,EACf,KAAK,uBAAyB,GAC/B,CACD,IAAI,KAAM,CAER,MAAMF,EADW,KAAK,SACI,UAAU,KAAK,EACnCN,EAAMM,EAAS,KACrB,IAAI4E,EAAiBlF,EACrB,MAAMmF,EAAgB,KAAK,QAAQ,cACnC,OAAKA,EAAc,WACjBD,EAAiB,KAAK,iBAClB,KAAK,yBAA2BC,EAAc,WAAa,KAAK,cAAgB7E,EAAS,cACvF,CAAC4E,GAAkBA,EAAe,OAASlF,EAAI,UACjDkF,EAAiB,KAAK,gBAAkB,IAAI,aAAalF,EAAI,MAAM,GAErE,KAAK,uBAAyBmF,EAAc,UAC5C,KAAK,YAAc7E,EAAS,UAC5B6E,EAAc,YAAYnF,EAAKkF,CAAc,IAG1CA,CACR,CACD,IAAI,WAAY,CACd,OAAO,KAAK,SAAS,SACtB,CACD,IAAI,SAAU,CACZ,OAAO,KAAK,SAAS,OACtB,CACD,IAAI,OAAQ,CACV,OAAO,KAAK,WAAW,eACxB,CACD,IAAI,gBAAiB,CACnB,OAAO,KAAK,WAAW,cACxB,CACD,IAAI,eAAgB,CAClB,OAAO,KAAK,SAAS,UAAU,OAAS,CACzC,CACD,IAAI,WAAY,CACd,OAAO,KAAK,SAAS,QAAQ,MAC9B,CACH,CCtEA,MAAME,EAAS,CACb,YAAYlK,EAAUgI,EAAS,CAC7B,KAAK,cAAgB,IAAIhH,EAAa,CACpC,iBAAkB,CAAE,MAAO,IAAIuD,EAAU,KAAM,aAAe,EAC9D,OAAQ,CAAE,MAAO,IAAI,aAAa,CAAC,EAAG,EAAG,EAAG,CAAC,CAAC,EAAG,KAAM,WAAa,EACpE,OAAQ,CAAE,MAAO,EAAG,KAAM,KAAO,CACvC,CAAK,EACD,KAAK,uBAAyB,IAAItD,GAAU,CAC1C,EAAG,KAAK,aACd,CAAK,EACD,KAAK,cAAgC,OAAO,OAAO,IAAI,EACvD,KAAK,sBAAwC,OAAO,OAAO,IAAI,EAC/D,KAAK,wBAA0B,KAAK,kBAAkB,KAAK,IAAI,EAC/D,KAAK,SAAWjB,EAChB,KAAK,SAAWgI,EAChB,KAAK,SAAS,OACdhI,EAAS,aAAa,eAAe,KAAM,uBAAuB,EAClEA,EAAS,aAAa,eAAe,KAAM,eAAe,CAC3D,CACD,mBAAmBmK,EAAM,CACvB,MAAMC,EAAW,KAAK,aAAaD,CAAI,EACjC/B,EAAagC,EAAS,QACtBC,EAAYF,EAAK,QAEvB,GADAC,EAAS,QAAUC,EACfjC,IAAeiC,EACjB,MAAO,GACF,GAAIA,EAAW,CACpB,MAAMC,EAAWH,EAAK,UACtB,GAAIG,EAAS,QAAQ,SAAWF,EAAS,WAAaE,EAAS,UAAU,SAAWF,EAAS,WAC3F,OAAAA,EAAS,UAAYE,EAAS,QAAQ,OACtCF,EAAS,WAAaE,EAAS,UAAU,OAClC,GAET,MAAMC,EAAgB,KAAK,kBAAkBJ,CAAI,EACjD,MAAO,CAACI,EAAc,SAAS,sBAC7BA,EACAJ,EAAK,OACb,CACK,CACD,MAAO,EACR,CACD,cAAcA,EAAMhK,EAAgB,CAClC,MAAMqK,EAAU,KAAK,SAAS,YAAY,MACpC,CAAE,QAAAC,CAAS,EAAG,KAAK,aAAaN,CAAI,EAC1C,GAAIM,EAAS,CACX,MAAMC,EAAmB,KAAK,kBAAkBP,CAAI,EACpDO,EAAiB,QAAUP,EAAK,SAChCO,EAAiB,SAAWP,EAAK,UACjCK,EAAQ,WAAWE,EAAkBvK,CAAc,CACzD,MACMqK,EAAQ,MAAMrK,CAAc,EAC5BA,EAAe,IAAIgK,CAAI,CAE1B,CACD,iBAAiBA,EAAM,CACrB,GAAIA,EAAK,QAAS,CAChB,MAAMO,EAAmB,KAAK,sBAAsBP,EAAK,GAAG,EAC5DO,EAAiB,WAAWP,EAAK,QAAQ,EACzCO,EAAiB,SAAWP,EAAK,UACjCO,EAAiB,SAAS,cAAcA,CAAgB,CACzD,CACF,CACD,kBAAkBP,EAAM,CACtB,KAAK,cAAcA,EAAK,GAAG,EAAI,KAC/B,MAAMQ,EAAU,KAAK,sBAAsBR,EAAK,GAAG,EAC/CQ,IACF7B,EAAQ,OAAO6B,CAAO,EACtB,KAAK,sBAAsBR,EAAK,GAAG,EAAI,MAEzCA,EAAK,IAAI,YAAa,KAAK,uBAAuB,CACnD,CACD,QAAQA,EAAM,CACZ,GAAI,CAACA,EAAK,aACR,OACFA,EAAK,MAAM,UAAYS,GAA0BT,EAAK,eAAgBA,EAAK,QAAQ,OAAO,EAC1F,MAAM1B,EAAgB,KAAK,cAC3BA,EAAc,SAAS,iBAAmB0B,EAAK,eAC/C1B,EAAc,SAAS,OAAS,KAAK,SAAS,aAAe0B,EAAK,aAClE1B,EAAc,OAAM,EACpBC,EACEyB,EAAK,gBACL1B,EAAc,SAAS,OACvB,CACN,EACI,KAAK,SAAS,QAAQ,KAAM0B,CAAI,CACjC,CACD,aAAaA,EAAM,CACjB,OAAO,KAAK,cAAcA,EAAK,GAAG,GAAK,KAAK,cAAcA,CAAI,CAC/D,CACD,cAAcA,EAAM,CAClB,YAAK,cAAcA,EAAK,GAAG,EAAI,CAC7B,QAASA,EAAK,QACd,UAAWA,EAAK,UAAU,SAAS,OACnC,WAAYA,EAAK,UAAU,WAAW,MAC5C,EACIA,EAAK,GAAG,YAAa,KAAK,uBAAuB,EAC1C,KAAK,cAAcA,EAAK,GAAG,CACnC,CACD,kBAAkBA,EAAM,CACtB,OAAO,KAAK,sBAAsBA,EAAK,GAAG,GAAK,KAAK,mBAAmBA,CAAI,CAC5E,CACD,mBAAmBA,EAAM,CACvB,MAAMQ,EAAU7B,EAAQ,IAAIiB,EAAa,EACzC,OAAAY,EAAQ,WAAaR,EACrBQ,EAAQ,QAAUR,EAAK,SACvBQ,EAAQ,UAAYR,EAAK,eACzBQ,EAAQ,YAAc,KAAK,SAAS,aAAeR,EAAK,aACxD,KAAK,sBAAsBA,EAAK,GAAG,EAAIQ,EAChCA,CACR,CACD,SAAU,CACR,UAAWhK,KAAK,KAAK,sBACf,KAAK,sBAAsBA,CAAC,GAC9BmI,EAAQ,OAAO,KAAK,sBAAsBnI,CAAC,CAAC,EAGhD,KAAK,sBAAwB,KAC7B,KAAK,cAAgB,KACrB,KAAK,cAAgB,KACrB,KAAK,uBAAyB,KAC9B,KAAK,SAAS,UACd,KAAK,SAAW,KAChB,KAAK,SAAW,IACjB,CACH,CAEAuJ,GAAS,UAAY,CACnB,KAAM,CACJzK,EAAc,WACdA,EAAc,YACdA,EAAc,WACf,EACD,KAAM,MACR,EC9IA,MAAMoL,EAA2B,CAC/B,QAAQC,EAAuB5K,EAAW,CACxC,MAAM6K,EAAQD,EAAsB,MAC9B9K,EAAW8K,EAAsB,SACjCtC,EAAStI,EAAU,QAAU4K,EAAsB,cACzDtC,EAAO,UAAU,SAAWtI,EAAU,QAAQ,QAC9CsI,EAAO,UAAU,SAAWsC,EAAsB,cAClD,MAAME,EAAKhL,EAAS,GACdiL,EAASH,EAAsB,WAAW5K,CAAS,EACzDF,EAAS,OAAO,KAAKwI,CAAM,EAC3BxI,EAAS,MAAM,IAAI+K,CAAK,EACxB/K,EAAS,SAAS,KAAKiL,EAAO,SAAUzC,EAAO,SAAS,EAExD,MAAM0C,EADWD,EAAO,SAAS,YAAY,KAAK,oBACtB,EAAID,EAAG,eAAiBA,EAAG,aACvDA,EAAG,aAAaA,EAAG,UAAW9K,EAAU,iBAAiB,OAAS,EAAGgL,EAAQ,CAAC,CAC/E,CACH,CChBA,MAAMC,EAA4B,CAChC,QAAQL,EAAuB5K,EAAW,CACxC,MAAMF,EAAW8K,EAAsB,SACjCtC,EAAStI,EAAU,QAAU4K,EAAsB,cACzDtC,EAAO,OAAO,CAAC,EAAIxI,EAAS,YAAY,aAAa,oBAAoB8K,EAAsB,cAAe,EAAI,EAClHtC,EAAO,OAAO,CAAC,EAAIxI,EAAS,QAAQ,oBAAoBE,EAAU,OAAO,EACzE,MAAM6K,EAAQD,EAAsB,MAC9BG,EAASH,EAAsB,WAAW5K,CAAS,EACzDF,EAAS,QAAQ,KAAK,CACpB,SAAUiL,EAAO,SACjB,OAAQ/K,EAAU,QAAU4K,EAAsB,cAClD,MAAAC,EACA,KAAM7K,EAAU,iBAAiB,OAAS,CAChD,CAAK,CACF,CACH,CCfA,SAASkL,GAAsBC,EAAMC,EAAY,KAAM,CACrD,MAAMC,EAAeF,EAAO,EAM5B,GALIE,EAAe,MACjBD,IAAcA,EAAY,IAAI,YAAYC,CAAY,GAEtDD,IAAcA,EAAY,IAAI,YAAYC,CAAY,GAEpDD,EAAU,SAAWC,EACvB,MAAM,IAAI,MAAM,uCAAuCD,EAAU,MAAM,iBAAiBC,CAAY,EAAE,EAExG,QAAS5K,EAAI,EAAG+F,EAAI,EAAG/F,EAAI4K,EAAc5K,GAAK,EAAG+F,GAAK,EACpD4E,EAAU3K,EAAI,CAAC,EAAI+F,EAAI,EACvB4E,EAAU3K,EAAI,CAAC,EAAI+F,EAAI,EACvB4E,EAAU3K,EAAI,CAAC,EAAI+F,EAAI,EACvB4E,EAAU3K,EAAI,CAAC,EAAI+F,EAAI,EACvB4E,EAAU3K,EAAI,CAAC,EAAI+F,EAAI,EACvB4E,EAAU3K,EAAI,CAAC,EAAI+F,EAAI,EAEzB,OAAO4E,CACT,CCjBA,SAASE,GAA+BC,EAAY,CAClD,MAAO,CACL,cAAeC,GAAuBD,EAAY,EAAI,EACtD,aAAcC,GAAuBD,EAAY,EAAK,CAC1D,CACA,CACA,SAASC,GAAuBD,EAAYE,EAAS,CACnD,MAAMC,EAAgB,CAAA,EACtBA,EAAc,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAQR,EACX,IAAI5I,EAAS,EACb,UAAWrC,KAAK8K,EAAY,CAC1B,MAAMI,EAAWJ,EAAW9K,CAAC,EAC7B,GAAIgL,IAAYE,EAAS,QACvB,SACFD,EAAc,KAAK,oBAAoB5I,CAAM,EAAE,EAC/C4I,EAAc,KAAKC,EAAS,IAAI,EAChC,MAAMC,EAAgBC,EAA2BF,EAAS,MAAM,EAChE7I,GAAU8I,EAAc,OAAS,CAClC,CACDF,EAAc,KAAK;AAAA;AAAA;AAAA,KAGhB,EACHA,EAAc,QAAQ;AAAA,uBACD5I,CAAM;AAAA,KACxB,EACH,MAAMgJ,EAAiBJ,EAAc,KAAK;AAAA,CAAI,EAC9C,OAAO,IAAI,SAAS,KAAM,OAAQ,OAAQI,CAAc,CAC1D,CC9BA,MAAMC,EAAe,CACnB,YAAY9M,EAAS,CACnB,KAAK,MAAQ,EACb,KAAK,6BAA+B,GACpC,MAAMkM,EAAO,KAAK,MAAQlM,EAAQ,MAAQ,IACpCsM,EAAatM,EAAQ,WAC3B,IAAI+M,EAAmB,EACnBC,EAAoB,EACxB,UAAWxL,KAAK8K,EAAY,CAC1B,MAAMI,EAAWJ,EAAW9K,CAAC,EACvBmL,EAAgBC,EAA2BF,EAAS,MAAM,EAC5DA,EAAS,QACXM,GAAqBL,EAAc,OAEnCI,GAAoBJ,EAAc,MAErC,CACD,KAAK,eAAiBK,EAAoB,EAC1C,KAAK,cAAgBD,EAAmB,EACxC,KAAK,sBAAwB,IAAIE,EAAef,EAAO,EAAIa,CAAgB,EAC3E,KAAK,uBAAyB,IAAIE,EAAef,EAAO,EAAIc,CAAiB,EAC7E,KAAK,YAAcf,GAAsBC,CAAI,EAC7C,MAAMf,EAAW,IAAIxJ,EACrB,IAAIuL,EAAgB,EAChBC,EAAe,EACnB,KAAK,cAAgB,IAAIpH,EAAO,CAC9B,KAAM,IAAI,aAAa,CAAC,EACxB,MAAO,yBACP,YAAa,GACb,MAAOC,EAAY,OAASA,EAAY,QAC9C,CAAK,EACD,KAAK,eAAiB,IAAID,EAAO,CAC/B,KAAM,IAAI,aAAa,CAAC,EACxB,MAAO,0BACP,YAAa,GACb,MAAOC,EAAY,OAASA,EAAY,QAC9C,CAAK,EACD,UAAWxE,KAAK8K,EAAY,CAC1B,MAAMI,EAAWJ,EAAW9K,CAAC,EACvBmL,EAAgBC,EAA2BF,EAAS,MAAM,EAC5DA,EAAS,SACXvB,EAAS,aAAauB,EAAS,cAAe,CAC5C,OAAQ,KAAK,eACb,OAAQ,KAAK,eAAiB,EAC9B,OAAQQ,EAAgB,EACxB,OAAQR,EAAS,MAC3B,CAAS,EACDQ,GAAiBP,EAAc,OAE/BxB,EAAS,aAAauB,EAAS,cAAe,CAC5C,OAAQ,KAAK,cACb,OAAQ,KAAK,cAAgB,EAC7B,OAAQS,EAAe,EACvB,OAAQT,EAAS,MAC3B,CAAS,EACDS,GAAgBR,EAAc,KAEjC,CACDxB,EAAS,SAAS,KAAK,WAAW,EAClC,MAAMiC,EAAiB,KAAK,kBAAkBd,CAAU,EACxD,KAAK,eAAiBc,EAAe,cACrC,KAAK,cAAgBA,EAAe,aACpC,KAAK,SAAWjC,CACjB,CACD,kBAAkBmB,EAAY,CAC5B,MAAMe,EAAMC,GAAmBhB,CAAU,EACzC,OAAI,KAAK,6BAA6Be,CAAG,EAChC,KAAK,6BAA6BA,CAAG,GAE9C,KAAK,6BAA6BA,CAAG,EAAI,KAAK,uBAAuBf,CAAU,EACxE,KAAK,6BAA6Be,CAAG,EAC7C,CACD,uBAAuBf,EAAY,CACjC,OAAOD,GAA+BC,CAAU,CACjD,CACD,OAAOiB,EAAWC,EAAc,CAC1BD,EAAU,OAAS,KAAK,QAC1BC,EAAe,GACf,KAAK,MAAQ,KAAK,IAAID,EAAU,OAAQ,KAAK,MAAQ,IAAM,CAAC,EAC5D,KAAK,sBAAwB,IAAIN,EAAe,KAAK,MAAQ,KAAK,cAAgB,EAAI,CAAC,EACvF,KAAK,uBAAyB,IAAIA,EAAe,KAAK,MAAQ,KAAK,eAAiB,EAAI,CAAC,EACzF,KAAK,YAAchB,GAAsB,KAAK,KAAK,EACnD,KAAK,SAAS,YAAY,gBACxB,KAAK,YACL,KAAK,YAAY,WACjB,EACR,GAEI,MAAMwB,EAAyB,KAAK,uBAOpC,GANA,KAAK,eAAeF,EAAWE,EAAuB,YAAaA,EAAuB,UAAU,EACpG,KAAK,eAAe,gBAClB,KAAK,uBAAuB,YAC5BF,EAAU,OAAS,KAAK,eAAiB,EACzC,EACN,EACQC,EAAc,CAChB,MAAME,EAAwB,KAAK,sBACnC,KAAK,cAAcH,EAAWG,EAAsB,YAAaA,EAAsB,UAAU,EACjG,KAAK,cAAc,gBACjBA,EAAsB,YACtBH,EAAU,OAAS,KAAK,cAAgB,EACxC,EACR,CACK,CACF,CACD,SAAU,CACR,KAAK,cAAc,UACnB,KAAK,eAAe,UACpB,KAAK,SAAS,SACf,CACH,CACA,SAASD,GAAmBhB,EAAY,CACtC,MAAMqB,EAAS,CAAA,EACf,UAAWN,KAAOf,EAAY,CAC5B,MAAMI,EAAWJ,EAAWe,CAAG,EAC/BM,EAAO,KAAKN,EAAKX,EAAS,KAAMA,EAAS,QAAU,IAAM,GAAG,CAC7D,CACD,OAAOiB,EAAO,KAAK,GAAG,CACxB,CC/HA,IAAIC,GAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GCAXC,GAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,ECATC,GAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GCYX,MAAMC,WAAuBC,EAAO,CAClC,aAAc,CACZ,MAAMC,EAAYC,GAAU,KAAK,CAC/B,OAAAL,GACA,SAAAD,EACN,CAAK,EACKO,EAAaC,GAAW,KAAK,CACjC,SAAU,CACR,OAAQN,GACR,WAAY,cACb,EACD,OAAQ,CACN,OAAQA,GACR,WAAY,YACb,CACP,CAAK,EACD,MAAM,CACJ,UAAAG,EACA,WAAAE,EACA,UAAW,CAET,SAAUnL,EAAQ,MAAM,OAExB,SAAU,IAAIqL,GAAa,EAAE,EAE7B,SAAU,CACR,mBAAoB,CAAE,MAAO,IAAIjJ,EAAU,KAAM,aAAe,EAChE,OAAQ,CAAE,MAAO,IAAIsB,EAAM,QAAQ,EAAG,KAAM,WAAa,EACzD,OAAQ,CAAE,MAAO,EAAG,KAAM,KAAO,EACjC,YAAa,CAAE,MAAO,CAAC,EAAG,CAAC,EAAG,KAAM,WAAa,CAClD,CACF,CACP,CAAK,CACF,CACH,CCrCA,MAAM4H,EAAsB,CAK1B,YAAYzN,EAAUgI,EAAS,CAC7B,KAAK,MAAQC,EAAM,QACnB,KAAK,eAAiC,OAAO,OAAO,IAAI,EAExD,KAAK,wBAA0B,KAAK,kBAAkB,KAAK,IAAI,EAC/D,KAAK,cAAgB,IAAIjH,EAAa,CACpC,mBAAoB,CAAE,MAAO,IAAIuD,EAAU,KAAM,aAAe,EAChE,OAAQ,CAAE,MAAO,IAAI,aAAa,CAAC,EAAG,KAAM,WAAa,EACzD,OAAQ,CAAE,MAAO,EAAG,KAAM,KAAO,EACjC,YAAa,CAAE,MAAO,CAAC,EAAG,CAAC,EAAG,KAAM,WAAa,CACvD,CAAK,EACD,KAAK,SAAWvE,EAChB,KAAK,QAAUgI,EACf,KAAK,cAAgB,IAAIkF,GACzB,KAAK,MAAQjF,EAAM,OACpB,CACD,mBAAmByF,EAAa,CAC9B,MAAO,EACR,CACD,cAAc9M,EAAYT,EAAgB,CACxC,KAAK,SAAS,YAAY,MAAM,MAAMA,CAAc,EACpDA,EAAe,IAAIS,CAAU,CAC9B,CACD,WAAWA,EAAY,CACrB,OAAO,KAAK,eAAeA,EAAW,GAAG,GAAK,KAAK,YAAYA,CAAU,CAC1E,CACD,YAAYA,EAAY,CACtB,YAAK,eAAeA,EAAW,GAAG,EAAI,IAAIqL,GAAe,CACvD,KAAMrL,EAAW,iBAAiB,OAClC,WAAYA,EAAW,WAC7B,CAAK,EACDA,EAAW,GAAG,YAAa,KAAK,uBAAuB,EAChD,KAAK,eAAeA,EAAW,GAAG,CAC1C,CACD,iBAAiB8M,EAAa,CAC7B,CACD,kBAAkB9M,EAAY,CACb,KAAK,eAAeA,EAAW,GAAG,EAC1C,QAAO,EACd,KAAK,eAAeA,EAAW,GAAG,EAAI,KACtCA,EAAW,IAAI,YAAa,KAAK,uBAAuB,CACzD,CACD,QAAQV,EAAW,CACjB,MAAMyN,EAAWzN,EAAU,iBAC3B,GAAIyN,EAAS,SAAW,EACtB,OAEF,MAAM3N,EAAW,KAAK,SAChBiL,EAAS,KAAK,WAAW/K,CAAS,EACxCA,EAAU,UAAYA,EAAU,QAAUyN,EAAS,CAAC,EAAE,SACtD,MAAM5C,EAAQ,KAAK,MACnBE,EAAO,OAAO0C,EAAUzN,EAAU,cAAc,EAChDA,EAAU,eAAiB,GAC3B6K,EAAM,UAAYH,GAA0B1K,EAAU,UAAWA,EAAU,QAAQ,OAAO,EAC1F,MAAMmD,EAAW,KAAK,cAAc,SAC9BuK,EAAuBvK,EAAS,mBACtCnD,EAAU,eAAe,OAAO0N,CAAoB,EACpDA,EAAqB,QAAQ5N,EAAS,eAAe,kBAAkB,gBAAgB,EACvFqD,EAAS,YAAcrD,EAAS,eAAe,kBAAkB,WACjEqD,EAAS,OAASrD,EAAS,aAAeE,EAAU,aACpDwI,EACExI,EAAU,gBACVmD,EAAS,OACT,CACN,EACI,KAAK,QAAQ,QAAQ,KAAMnD,CAAS,CACrC,CAED,SAAU,CACJ,KAAK,gBACP,KAAK,cAAc,UACnB,KAAK,cAAgB,KAExB,CACH,CCnFA,MAAM2N,WAAgCJ,EAAsB,CAC1D,YAAYzN,EAAU,CACpB,MAAMA,EAAU,IAAI6K,EAA4B,CACjD,CACH,CAEAgD,GAAwB,UAAY,CAClC,KAAM,CACJpO,EAAc,UACf,EACD,KAAM,UACR,ECXA,MAAMqO,WAAiCL,EAAsB,CAC3D,YAAYzN,EAAU,CACpB,MAAMA,EAAU,IAAImL,EAA6B,CAClD,CACH,CAEA2C,GAAyB,UAAY,CACnC,KAAM,CACJrO,EAAc,WACf,EACD,KAAM,UACR,ECbA,MAAMsO,GAAqB,MAAMA,WAA2BjE,EAAc,CACxE,YAAY3K,EAAU,GAAI,CACxBA,EAAU,CAAE,GAAG4O,GAAmB,eAAgB,GAAG5O,CAAO,EAC5D,MAAM,CACJ,MAAOA,EAAQ,MACf,OAAQA,EAAQ,OAChB,UAAW,EACX,UAAW,CACjB,CAAK,EACD,KAAK,OAAOA,CAAO,CACpB,CAKD,OAAOA,EAAS,CACd,KAAK,MAAQA,EAAQ,OAAS,KAAK,MACnC,KAAK,OAASA,EAAQ,QAAU,KAAK,OACrC,KAAK,eAAiBA,EAAQ,eAAiB,KAAK,eACpD,KAAK,gBAAkBA,EAAQ,gBAAkB,KAAK,gBACtD,KAAK,WAAaA,EAAQ,WAAa,KAAK,WAC5C,KAAK,YAAcA,EAAQ,YAAc,KAAK,YAC9C,KAAK,WAAaA,EAAQ,WAAa,KAAK,WAC5C,KAAK,cAAgBA,EAAQ,cAAgB,KAAK,cAClD,KAAK,UAAS,EACd,KAAK,gBAAe,CACrB,CAED,iBAAkB,CAChB,MAAM0F,EAAY,KAAK,UACjBmJ,EAAI,KAAK,WAAa,KAAK,YAC3BC,EAAS,KAAK,MAAQD,EAAI,EAAI,KAAK,MAAQA,EAC3CE,EAAI,KAAK,WAAa,KAAK,cAC3BC,EAAS,KAAK,OAASD,EAAI,EAAI,KAAK,OAASA,EAC7CE,EAAQ,KAAK,IAAIH,EAAQE,CAAM,EACrCtJ,EAAU,CAAC,EAAIA,EAAU,EAAE,EAAIA,EAAU,EAAE,EAAIA,EAAU,EAAE,EAAI,KAAK,WAAauJ,EACjFvJ,EAAU,EAAE,EAAIA,EAAU,EAAE,EAAIA,EAAU,EAAE,EAAIA,EAAU,EAAE,EAAI,KAAK,OAAS,KAAK,cAAgBuJ,EACnGvJ,EAAU,EAAE,EAAIA,EAAU,EAAE,EAAIA,EAAU,EAAE,EAAIA,EAAU,EAAE,EAAI,KAAK,OACrEA,EAAU,CAAC,EAAIA,EAAU,EAAE,EAAIA,EAAU,EAAE,EAAIA,EAAU,EAAE,EAAI,KAAK,WAAauJ,EACjFvJ,EAAU,CAAC,EAAIA,EAAU,EAAE,EAAIA,EAAU,EAAE,EAAIA,EAAU,EAAE,EAAI,KAAK,MAAQ,KAAK,YAAcuJ,EAC/FvJ,EAAU,CAAC,EAAIA,EAAU,EAAE,EAAIA,EAAU,EAAE,EAAIA,EAAU,EAAE,EAAI,KAAK,MACpE,KAAK,UAAU,WAAW,EAAE,OAAM,CACnC,CAED,WAAY,CACV,MAAMC,EAAM,KAAK,IACjBA,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAIA,EAAI,EAAE,EAAIA,EAAI,EAAE,EAAI,EACtCA,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAI,EACpCA,EAAI,CAAC,EAAIA,EAAI,EAAE,EAAIA,EAAI,EAAE,EAAIA,EAAI,EAAE,EAAI,EACvCA,EAAI,EAAE,EAAIA,EAAI,EAAE,EAAIA,EAAI,EAAE,EAAIA,EAAI,EAAE,EAAI,EACxC,MAAMuJ,EAAO,EAAI,KAAK,eAChBC,EAAO,EAAI,KAAK,gBACtBxJ,EAAI,CAAC,EAAIA,EAAI,EAAE,EAAIA,EAAI,EAAE,EAAIA,EAAI,EAAE,EAAIuJ,EAAO,KAAK,WACnDvJ,EAAI,CAAC,EAAIA,EAAI,EAAE,EAAIA,EAAI,EAAE,EAAIA,EAAI,EAAE,EAAIwJ,EAAO,KAAK,WACnDxJ,EAAI,CAAC,EAAIA,EAAI,EAAE,EAAIA,EAAI,EAAE,EAAIA,EAAI,EAAE,EAAI,EAAIuJ,EAAO,KAAK,YACvDvJ,EAAI,EAAE,EAAIA,EAAI,EAAE,EAAIA,EAAI,EAAE,EAAIA,EAAI,EAAE,EAAI,EAAIwJ,EAAO,KAAK,cACxD,KAAK,UAAU,KAAK,EAAE,OAAM,CAC7B,CACH,EAEAP,GAAmB,eAAiB,CAElC,MAAO,IAEP,OAAQ,IAER,UAAW,GAEX,UAAW,GAEX,WAAY,GAEZ,aAAc,GAEd,cAAe,IAEf,eAAgB,GAClB,EACA,IAAIQ,GAAoBR,GC3ExB,MAAMS,EAAoB,CACxB,YAAYxO,EAAU,CACpB,KAAK,eAAiC,OAAO,OAAO,IAAI,EACxD,KAAK,wBAA0B,KAAK,kBAAkB,KAAK,IAAI,EAC/D,KAAK,UAAYA,EACjB,KAAK,UAAU,aAAa,eAAe,KAAM,gBAAgB,CAClE,CACD,cAAcmE,EAAQhE,EAAgB,CACpC,MAAMsO,EAAY,KAAK,cAActK,CAAM,EACvCA,EAAO,eACT,KAAK,uBAAuBA,EAAQsK,CAAS,EAC/C,KAAK,UAAU,YAAY,MAAM,WAAWA,EAAWtO,CAAc,CACtE,CACD,iBAAiBgE,EAAQ,CACvB,MAAMsK,EAAY,KAAK,eAAetK,EAAO,GAAG,EAC5CA,EAAO,eACT,KAAK,uBAAuBA,EAAQsK,CAAS,EAC/CA,EAAU,SAAS,cAAcA,CAAS,CAC3C,CACD,mBAAmBtK,EAAQ,CACzB,MAAMsK,EAAY,KAAK,cAActK,CAAM,EAC3C,MAAO,CAACsK,EAAU,SAAS,sBACzBA,EACAtK,EAAO,QACb,CACG,CACD,kBAAkBA,EAAQ,CACxB,MAAMoG,EAAgB,KAAK,eAAepG,EAAO,GAAG,EACpD2E,EAAQ,OAAOyB,EAAc,QAAQ,EACrCzB,EAAQ,OAAOyB,CAAa,EAC5B,KAAK,eAAepG,EAAO,GAAG,EAAI,KAClCA,EAAO,IAAI,YAAa,KAAK,uBAAuB,CACrD,CACD,uBAAuBA,EAAQuK,EAAiB,CAC9CA,EAAgB,SAAS,OAAOvK,CAAM,EACtCuK,EAAgB,WAAWvK,EAAO,QAAQ,CAC3C,CACD,cAAcA,EAAQ,CACpB,OAAO,KAAK,eAAeA,EAAO,GAAG,GAAK,KAAK,eAAeA,CAAM,CACrE,CACD,eAAeA,EAAQ,CACrB,MAAMoG,EAAgBzB,EAAQ,IAAIiB,EAAa,EAC/C,OAAAQ,EAAc,SAAWzB,EAAQ,IAAIyF,EAAiB,EACtDhE,EAAc,WAAapG,EAC3BoG,EAAc,UAAYpG,EAAO,eACjCoG,EAAc,QAAUpG,EAAO,SAC/BoG,EAAc,YAAc,KAAK,UAAU,aAAepG,EAAO,aACjE,KAAK,eAAeA,EAAO,GAAG,EAAIoG,EAC7BpG,EAAO,eACV,KAAK,uBAAuBA,EAAQoG,CAAa,EAEnDpG,EAAO,GAAG,YAAa,KAAK,uBAAuB,EAC5CoG,CACR,CACD,SAAU,CACR,UAAW5J,KAAK,KAAK,eACG,KAAK,eAAeA,CAAC,EAC7B,SAAS,UAEzB,KAAK,eAAiB,KACtB,KAAK,UAAY,IAClB,CACH,CAEA6N,GAAoB,UAAY,CAC9B,KAAM,CACJ/O,EAAc,WACdA,EAAc,YACdA,EAAc,WACf,EACD,KAAM,iBACR,EC5EA,MAAMkP,GAAY,CAChB,KAAM,aACN,OAAQ,CACN,OAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAcF,KAEE;AAAA;AAAA;AAAA;AAAA,SAMH,EACD,SAAU,CACR,OAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAcF,KAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAiBH,CACH,EACMC,GAAc,CAClB,KAAM,aACN,OAAQ,CACN,OAEE;AAAA;AAAA;AAAA;AAAA,UAMF,KAEE;AAAA;AAAA;AAAA;AAAA,SAMH,EACD,SAAU,CACR,OAEE;AAAA;AAAA;AAAA;AAAA;AAAA,UAOF,KAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAWH,CACH,ECrGA,IAAItB,EACAF,EACJ,MAAMyB,WAA2B1B,EAAO,CACtC,aAAc,CACZG,IAAeA,EAAawB,GAA4B,CACtD,KAAM,uBACN,KAAM,CACJC,GACAJ,GACAK,EACD,CACF,CAAA,GACD5B,IAAcA,EAAY6B,GAA2B,CACnD,KAAM,uBACN,KAAM,CACJC,GACAN,GACAO,EACD,CACF,CAAA,GACD,MAAMC,EAAiB,IAAIpO,EAAa,CACtC,UAAW,CAAE,MAAO,IAAIuD,EAAU,KAAM,aAAe,EACvD,YAAa,CAAE,MAAO,IAAI,aAAa,CAAC,EAAG,EAAG,EAAG,CAAC,CAAC,EAAG,KAAM,WAAa,EACzE,aAAc,CAAE,MAAO,IAAI,aAAa,CAAC,EAAG,CAAC,CAAC,EAAG,KAAM,WAAa,EACpE,kBAAmB,CAAE,MAAO,IAAIA,EAAU,KAAM,aAAe,EAC/D,YAAa,CAAE,MAAO,IAAI,aAAa,CAAC,IAAK,IAAK,GAAK,EAAG,CAAC,EAAG,KAAM,WAAa,CACvF,CAAK,EACD,MAAM,CACV,UAAM6I,EACN,WAAME,EACA,UAAW,CACT,cAAe,IAAItM,EAAa,CAC9B,iBAAkB,CAAE,MAAO,IAAIuD,EAAU,KAAM,aAAe,EAC9D,OAAQ,CAAE,MAAO,IAAI,aAAa,CAAC,EAAG,EAAG,EAAG,CAAC,CAAC,EAAG,KAAM,WAAa,EACpE,OAAQ,CAAE,MAAO,EAAG,KAAM,KAAO,CAC3C,CAAS,EACD,eAAA6K,EACA,SAAUjN,EAAQ,MAAM,OACxB,SAAUA,EAAQ,MAAM,OAAO,KAChC,CACP,CAAK,CACF,CACD,eAAe9C,EAAOC,EAAQ+P,EAAQC,EAASC,EAASC,EAAS,CAC/D,MAAMJ,EAAiB,KAAK,UAAU,eAChCK,EAAeD,EAAQ,MACvBE,EAAgBF,EAAQ,OACxBvF,EAAgBuF,EAAQ,cACxBG,EAAoBP,EAAe,SAAS,kBAClDO,EAAkB,IAChBN,EAAO,EAAII,EAAepQ,EAC1BgQ,EAAO,EAAII,EAAenQ,EAC1B+P,EAAO,EAAIK,EAAgBrQ,EAC3BgQ,EAAO,EAAIK,EAAgBpQ,EAC3B+P,EAAO,GAAKhQ,EACZgQ,EAAO,GAAK/P,CAClB,EACIqQ,EAAkB,OAAM,EACxBP,EAAe,SAAS,UAAYnF,EAAc,SAClDmF,EAAe,SAAS,YAAcnF,EAAc,YACpDmF,EAAe,SAAS,aAAenF,EAAc,aACrDmF,EAAe,SAAS,kBAAoBO,EAC5CP,EAAe,SAAS,YAAY,CAAC,EAAI/P,EACzC+P,EAAe,SAAS,YAAY,CAAC,EAAI9P,EACzC8P,EAAe,SAAS,YAAY,CAAC,EAAIE,EACzCF,EAAe,SAAS,YAAY,CAAC,EAAIG,EACrCC,IACF,KAAK,UAAU,SAAWA,EAAQ,OAClC,KAAK,UAAU,SAAWA,EAAQ,OAAO,MAE5C,CACH,CC7EA,MAAMI,WAAqBrK,EAAa,CACtC,aAAc,CACZ,MAAM,CACJ,UAAW,IAAI,aAAa,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,CAAC,EACpD,IAAK,IAAI,aAAa,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,CAAC,EAC9C,QAAS,IAAI,YAAY,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,CAAC,CACjD,CAAK,CACF,CACH,CCVA,SAASsK,GAAaC,EAAcjL,EAAW,CAC7C,MAAMyK,EAAUQ,EAAa,OAAO,EAC9BP,EAAUO,EAAa,OAAO,EACpCjL,EAAU,CAAC,EAAI,CAACyK,EAAUQ,EAAa,MACvCjL,EAAU,CAAC,EAAI,CAAC0K,EAAUO,EAAa,OACvCjL,EAAU,CAAC,GAAK,EAAIyK,GAAWQ,EAAa,MAC5CjL,EAAU,CAAC,EAAI,CAAC0K,EAAUO,EAAa,OACvCjL,EAAU,CAAC,GAAK,EAAIyK,GAAWQ,EAAa,MAC5CjL,EAAU,CAAC,GAAK,EAAI0K,GAAWO,EAAa,OAC5CjL,EAAU,CAAC,EAAI,CAACyK,EAAUQ,EAAa,MACvCjL,EAAU,CAAC,GAAK,EAAI0K,GAAWO,EAAa,MAC9C,CCXA,SAASC,GAAYC,EAAOC,EAAQjN,EAAQqM,EAAQ,CAClD,IAAIa,EAAQ,EACZ,MAAM7E,EAAO2E,EAAM,OAAUC,EACvBE,EAAId,EAAO,EACXe,EAAIf,EAAO,EACXgB,EAAIhB,EAAO,EACXiB,EAAIjB,EAAO,EACXkB,EAAKlB,EAAO,GACZmB,EAAKnB,EAAO,GAElB,IADArM,GAAUiN,EACHC,EAAQ7E,GAAM,CACnB,MAAM1I,EAAIqN,EAAMhN,CAAM,EAChBJ,EAAIoN,EAAMhN,EAAS,CAAC,EAC1BgN,EAAMhN,CAAM,EAAImN,EAAIxN,EAAI0N,EAAIzN,EAAI2N,EAChCP,EAAMhN,EAAS,CAAC,EAAIoN,EAAIzN,EAAI2N,EAAI1N,EAAI4N,EACpCxN,GAAUiN,EACVC,GACD,CACH,CCfA,SAASO,GAAOX,EAAchL,EAAK,CACjC,MAAM0K,EAAUM,EAAa,QACvBzQ,EAAQmQ,EAAQ,MAAM,MACtBlQ,EAASkQ,EAAQ,MAAM,OAC7B,IAAIF,EAAU,EACVC,EAAU,EACVO,EAAa,uBACfR,EAAUQ,EAAa,OAAO,EAC9BP,EAAUO,EAAa,OAAO,GAEhChL,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAI,CAACwK,EACnBxK,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAI,EAAIwK,EACtBxK,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAI,CAACyK,EACnBzK,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAI,EAAIyK,EACtB,MAAMtF,EAAgB1F,EAAO,OAC7B0F,EAAc,SAAS6F,EAAa,eAAe,MAAM,EACzD7F,EAAc,IAAM6F,EAAa,MACjC7F,EAAc,IAAM6F,EAAa,OACjC7F,EAAc,OAAM,EACpBA,EAAc,MAAM6F,EAAa,MAAQzQ,EAAOyQ,EAAa,OAASxQ,CAAM,EAC5EyQ,GAAYjL,EAAK,EAAG,EAAGmF,CAAa,CACtC,CCZA,MAAMyG,EAAa,IAAId,GACvB,MAAMe,EAAiB,CACrB,YAAY3Q,EAAU,CACpB,KAAK,OAASiI,EAAM,UACpB,KAAK,sBAAwC,OAAO,OAAO,IAAI,EAC/D,KAAK,wBAA0B,KAAK,kBAAkB,KAAK,IAAI,EAC/D,KAAK,UAAYjI,EACjB,KAAK,UAAU,aAAa,eAAe,KAAM,uBAAuB,CACzE,CACD,mBAAmBY,EAAY,CAC7B,MAAMgQ,EAAmB,KAAK,qBAAqBhQ,CAAU,EACvDiQ,EAAaD,EAAiB,SACpC,KAAK,gBAAgBhQ,CAAU,EAC/B,MAAMkQ,EAAWF,EAAiB,SAClC,GAAIE,GAAYA,IAAaD,EAAY,CACvC,KAAM,CAAE,cAAAtG,CAAe,EAAGqG,EAC1B,MAAO,CAACrG,EAAc,SAAS,sBAC7BA,EACA3J,EAAW,OACnB,CACK,CACD,OAAOiQ,IAAeC,CACvB,CACD,cAAchB,EAAc3P,EAAgB,CAC1C,MAAMqK,EAAU,KAAK,UAAU,YAAY,MAC3C,KAAK,gBAAgBsF,CAAY,EACjC,MAAMc,EAAmB,KAAK,qBAAqBd,CAAY,EACzD,CAAE,SAAAxF,EAAU,SAAAwG,CAAU,EAAGF,EAC/B,GAAIE,EAAU,CACZF,EAAiB,gBAAkBA,EAAiB,cAAgB,IAAI7G,IACxE,MAAMQ,EAAgBqG,EAAiB,cACnCd,EAAa,gBACf,KAAK,qBAAqBA,CAAY,EACtCvF,EAAc,SAAWD,EACzBC,EAAc,WAAauF,EAC3BvF,EAAc,UAAYuF,EAAa,eACvCvF,EAAc,WAAWuF,EAAa,QAAQ,GAEhDvF,EAAc,YAAc,KAAK,UAAU,aAAeuF,EAAa,aACvEtF,EAAQ,WAAWD,EAAepK,CAAc,CACtD,MACMqK,EAAQ,MAAMrK,CAAc,EAC5ByQ,EAAiB,SAAWA,EAAiB,OAAS,IAAI/B,IAC1D,KAAK,iBAAiBiB,CAAY,EAClC3P,EAAe,IAAI2P,CAAY,CAElC,CACD,QAAQA,EAAc,CACpB,KAAM,CAAE,OAAAtH,CAAM,EAAK,KAAK,sBAAsBsH,EAAa,GAAG,EAC9DtH,EAAO,OAAO,CAAC,EAAI,KAAK,UAAU,eAAe,UACjD,MAAMC,EAAgBD,EAAO,UAAU,cAAc,SACrDC,EAAc,iBAAmBqH,EAAa,eAC9CrH,EAAc,OAAS,KAAK,UAAU,aAAeqH,EAAa,aAClEpH,EACEoH,EAAa,gBACbrH,EAAc,OACd,CACN,EACI,KAAK,OAAO,UAAYmC,GAA0BkF,EAAa,eAAgBA,EAAa,QAAQ,OAAO,EAC3G,KAAK,UAAU,QAAQ,KAAK,CAC1B,SAAUY,EACV,OAAAlI,EACA,MAAO,KAAK,MAClB,CAAK,CACF,CACD,iBAAiBsH,EAAc,CAC7B,MAAMc,EAAmB,KAAK,qBAAqBd,CAAY,EACzD,CAAE,SAAAgB,CAAU,EAAGF,EACrB,GAAIE,EAAU,CACZ,KAAM,CAAE,cAAAvG,CAAe,EAAGqG,EACtBd,EAAa,eACf,KAAK,qBAAqBA,CAAY,EACxCvF,EAAc,SAAS,cAAcA,CAAa,CACxD,SAAeuF,EAAa,cAAe,CACrC,KAAM,CAAE,OAAAtH,CAAQ,EAAGoI,EACnBpI,EAAO,eACLsH,EAAa,MACbA,EAAa,OACbA,EAAa,eAAe,OAC5BA,EAAa,OAAO,EACpBA,EAAa,OAAO,EACpBA,EAAa,OACrB,CACK,CACF,CACD,kBAAkBA,EAAc,CAC9B,MAAMc,EAAmB,KAAK,qBAAqBd,CAAY,EAC/Dc,EAAiB,cAAgB,KACjCA,EAAiB,QAAQ,UACzB,KAAK,sBAAsBd,EAAa,GAAG,EAAI,KAC/CA,EAAa,IAAI,YAAa,KAAK,uBAAuB,CAC3D,CACD,qBAAqBlP,EAAY,CAC/B,OAAO,KAAK,sBAAsBA,EAAW,GAAG,GAAK,KAAK,sBAAsBA,CAAU,CAC3F,CACD,sBAAsBkP,EAAc,CAClC,MAAMxF,EAAW,IAAI/E,GAAa,CAChC,QAASmL,EAAW,QACpB,UAAWA,EAAW,UAAU,MAAO,EACvC,IAAKA,EAAW,IAAI,MAAO,CACjC,CAAK,EACD,YAAK,sBAAsBZ,EAAa,GAAG,EAAI,CAC7C,SAAU,GACV,WAAYA,EACZ,SAAAxF,CACN,EACIwF,EAAa,GAAG,YAAa,KAAK,uBAAuB,EAClD,KAAK,sBAAsBA,EAAa,GAAG,CACnD,CACD,qBAAqBA,EAAc,CACjC,MAAMiB,EAAiB,KAAK,qBAAqBjB,CAAY,EACvD,CAAE,SAAAxF,CAAU,EAAGyG,EACftL,EAAQqK,EAAa,QAAQ,OAAO,MACtCrK,EAAM,cAAgB,WACxBA,EAAM,YAAc,SACpBA,EAAM,OAAM,GAEdgL,GAAOX,EAAcxF,EAAS,GAAG,EACjCuF,GAAaC,EAAcxF,EAAS,SAAS,CAC9C,CACD,SAAU,CACR,UAAW3J,KAAK,KAAK,sBACnB,KAAK,kBAAkB,KAAK,sBAAsBA,CAAC,EAAE,UAAU,EAEjE,KAAK,sBAAwB,KAC7B,KAAK,UAAY,IAClB,CACD,gBAAgBmP,EAAc,CAC5B,MAAMiB,EAAiB,KAAK,qBAAqBjB,CAAY,EACvDN,EAAUM,EAAa,QAC7B,IAAIkB,EAAqB,GACzB,OAAI,KAAK,UAAU,OAAShN,EAAa,QACvCgN,EAAqB,KAAK,UAAU,QAAQ,SAAS,mBAEvDD,EAAe,SAAWvB,EAAQ,cAAc,WAAawB,GAAsBxB,EAAQ,OAAO,cAC3FuB,EAAe,QACvB,CACH,CAEAJ,GAAiB,UAAY,CAC3B,KAAM,CACJlR,EAAc,WACdA,EAAc,YACdA,EAAc,WACf,EACD,KAAM,cACR,EC9JA,MAAMwR,GAAsB,CAC1B,KAAM,yBACN,OAAQ,CACN,OAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAWF,KAEE;AAAA;AAAA;AAAA,UAKF,IAEE;AAAA;AAAA;AAAA;AAAA;AAAA,SAOH,EACD,SAAU,CACR,OAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAUF,KAEE;AAAA;AAAA,SAIH,CACH,EACMC,GAAwB,CAC5B,KAAM,yBACN,OAAQ,CACN,OAEE;AAAA;AAAA;AAAA;AAAA,UAMF,KAEE;AAAA;AAAA;AAAA,UAKF,IAEE;AAAA;AAAA;AAAA;AAAA;AAAA,SAOH,EACD,SAAU,CACR,OAEE;AAAA;AAAA,WAIF,KAEE;AAAA;AAAA,SAIH,CACH,EChGMC,GAAU,CACd,KAAM,WACN,SAAU,CACR,OAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SA6BH,CACH,EACMC,GAAY,CAChB,KAAM,WACN,SAAU,CACR,OAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SA6BH,CACH,EC3DA,IAAI9D,EACAF,EACJ,MAAMiE,WAAkBlE,EAAO,CAC7B,aAAc,CACZ,MAAM9J,EAAW,IAAIrC,EAAa,CAChC,OAAQ,CAAE,MAAO,IAAI,aAAa,CAAC,EAAG,EAAG,EAAG,CAAC,CAAC,EAAG,KAAM,WAAa,EACpE,iBAAkB,CAAE,MAAO,IAAIuD,EAAU,KAAM,aAAe,EAC9D,UAAW,CAAE,MAAO,EAAG,KAAM,KAAO,EACpC,OAAQ,CAAE,MAAO,EAAG,KAAM,KAAO,CACvC,CAAK,EACK+M,EAAcC,KACpBjE,IAAeA,EAAawB,GAA4B,CACtD,KAAM,aACN,KAAM,CACJ0C,GACAC,GAAwBH,CAAW,EACnCL,GACAE,GACAnC,EACD,CACF,CAAA,GACD5B,IAAcA,EAAY6B,GAA2B,CACnD,KAAM,aACN,KAAM,CACJyC,GACAC,GAA0BL,CAAW,EACrCJ,GACAE,GACAjC,EACD,CACF,CAAA,GACD,MAAM,CACJ,UAAA/B,EACA,WAAAE,EACA,UAAW,CACT,cAAejK,EACf,cAAeuO,GAA6BN,CAAW,CACxD,CACP,CAAK,CACF,CACH,CC5CA,MAAMO,EAAe,CACnB,YAAY7R,EAAU,CACpB,KAAK,eAAiB,GACtB,KAAK,wBAA0B,KAAK,kBAAkB,KAAK,IAAI,EAC/D,KAAK,UAAYA,EACjB,KAAK,UAAU,aAAa,eAAe,KAAM,gBAAgB,CAClE,CACD,mBAAmB8R,EAAY,CAC7B,MAAMC,EAAqB,KAAK,kBAAkBD,CAAU,EAC5D,OAAIA,EAAW,iBACbA,EAAW,eAAiB,GAC5B,KAAK,eAAeA,EAAYC,CAAkB,GAE7C,KAAK,UAAU,YAAY,SAAS,mBAAmBA,CAAkB,CACjF,CACD,cAAcD,EAAY3R,EAAgB,CACxC,MAAM4R,EAAqB,KAAK,kBAAkBD,CAAU,EAC5DE,GAAcF,EAAYC,CAAkB,EACxCD,EAAW,iBACbA,EAAW,eAAiB,GAC5B,KAAK,eAAeA,EAAYC,CAAkB,GAEpD,KAAK,UAAU,YAAY,SAAS,cAAcA,EAAoB5R,CAAc,EAChF4R,EAAmB,QAAQ,cAC7B,KAAK,qBAAqBD,CAAU,CAEvC,CACD,kBAAkBA,EAAY,CAC5BA,EAAW,IAAI,YAAa,KAAK,uBAAuB,EACxD,KAAK,wBAAwBA,EAAW,GAAG,CAC5C,CACD,wBAAwBG,EAAe,CACrC,MAAM9J,EAAU,KAAK,eAAe8J,CAAa,EAAE,QAC/C9J,EAAQ,eACVW,EAAQ,OAAOX,EAAQ,YAAY,EACnCA,EAAQ,aAAe,MAEzBW,EAAQ,OAAO,KAAK,eAAemJ,CAAa,CAAC,EACjD,KAAK,eAAeA,CAAa,EAAI,IACtC,CACD,iBAAiBH,EAAY,CAC3B,MAAMC,EAAqB,KAAK,kBAAkBD,CAAU,EAC5DE,GAAcF,EAAYC,CAAkB,EAC5C,KAAK,UAAU,YAAY,SAAS,iBAAiBA,CAAkB,EACnEA,EAAmB,QAAQ,cAC7B,KAAK,qBAAqBD,CAAU,CAEvC,CACD,eAAeA,EAAYI,EAAe,CACxC,KAAM,CAAE,QAAA/J,CAAS,EAAG+J,EACdC,EAAaC,GAAkB,QAAQN,EAAW,KAAMA,EAAW,MAAM,EAC/E3J,EAAQ,MAAK,EACTgK,EAAW,cAAc,OAAS,SAC/BhK,EAAQ,eACXA,EAAQ,aAAeW,EAAQ,IAAIuI,EAAS,IAGhD,MAAMgB,EAAQ,MAAM,KAAKP,EAAW,IAAI,EAClCrM,EAAQqM,EAAW,OACzB,IAAIQ,EAAWH,EAAW,eAC1B,MAAMI,EAAmBC,GAAoBH,EAAO5M,EAAO0M,EAAY,EAAI,EAC3E,IAAIjC,EAAQ,EACZ,MAAM3O,EAAUkE,EAAM,QAChB2I,EAAQmE,EAAiB,MAC/B,IAAIhC,EAAKgC,EAAiB,MACtB/B,EAAK+B,EAAiB,OAASA,EAAiB,QAChD9M,EAAM,UACR8K,GAAM9K,EAAM,QAAQ,MAAQ2I,EAC5BoC,GAAM/K,EAAM,QAAQ,MAAQ2I,GAE9BjG,EAAQ,UAAU,CAAC2J,EAAW,QAAQ,GAAKvB,EAAKhP,EAAS,CAACuQ,EAAW,QAAQ,GAAKtB,EAAKjP,CAAO,EAAE,MAAM6M,EAAOA,CAAK,EAClH,MAAMqE,EAAON,EAAW,gBAAkB1M,EAAM,MAAM,MAAQ,SAC9D,QAAS9E,EAAI,EAAGA,EAAI4R,EAAiB,MAAM,OAAQ5R,IAAK,CACtD,MAAM+R,EAAOH,EAAiB,MAAM5R,CAAC,EACrC,QAAS+F,EAAI,EAAGA,EAAIgM,EAAK,cAAc,OAAQhM,IAAK,CAClD,MAAMiM,EAAON,EAAMnC,GAAO,EACpB0C,EAAWT,EAAW,MAAMQ,CAAI,EAClCC,GAAU,SACZzK,EAAQ,QACNyK,EAAS,QACTH,GAAc,QACd,KAAK,MAAMC,EAAK,cAAchM,CAAC,EAAIkM,EAAS,OAAO,EACnD,KAAK,MAAMN,EAAWM,EAAS,OAAO,CAClD,CAEO,CACDN,GAAYH,EAAW,UACxB,CACF,CACD,kBAAkBL,EAAY,CAC5B,OAAO,KAAK,eAAeA,EAAW,GAAG,GAAK,KAAK,YAAYA,CAAU,CAC1E,CACD,YAAYA,EAAY,CACtB,MAAMe,EAAkB/J,EAAQ,IAAIgK,EAAQ,EAC5C,YAAK,eAAehB,EAAW,GAAG,EAAIe,EACtC,KAAK,eAAef,EAAYe,CAAe,EAC/Cf,EAAW,GAAG,YAAa,KAAK,uBAAuB,EAChD,KAAK,eAAeA,EAAW,GAAG,CAC1C,CACD,qBAAqBA,EAAY,CAC/B,MAAM3J,EAAU,KAAK,kBAAkB2J,CAAU,EAAE,QAC7CiB,EAAajB,EAAW,OAAO,WAC/BkB,EAAcC,EAAM,IAAI,GAAGF,CAAU,SAAS,EAC9C,CAAE,EAAA5C,EAAG,EAAAC,EAAG,EAAAC,EAAG,CAAG,EAAGyB,EAAW,eAC5BoB,EAAK,KAAK,KAAK/C,EAAIA,EAAIC,EAAIA,CAAC,EAC5B+C,EAAK,KAAK,KAAK9C,EAAIA,EAAI,EAAI,CAAC,EAC5B+C,GAAc,KAAK,IAAIF,CAAE,EAAI,KAAK,IAAIC,CAAE,GAAK,EAC7CE,EAAYL,EAAY,qBAAuBlB,EAAW,OAAO,SACjEwB,EAAWF,EAAaJ,EAAY,cAAc,OAAS,EAAIK,GACrElL,EAAQ,aAAa,UAAU,cAAc,SAAS,UAAYmL,CACnE,CACD,SAAU,CACR,UAAWC,KAAO,KAAK,eACrB,KAAK,wBAAwBA,CAAG,EAElC,KAAK,eAAiB,KACtB,KAAK,UAAY,IAClB,CACH,CAEA1B,GAAe,UAAY,CACzB,KAAM,CACJpS,EAAc,WACdA,EAAc,YACdA,EAAc,WACf,EACD,KAAM,YACR,EACA,SAASuS,GAAc9R,EAAWsT,EAAO,CACvCA,EAAM,eAAiBtT,EAAU,eACjCsT,EAAM,gBAAkBtT,EAAU,gBAClCsT,EAAM,WAAatT,EAAU,WAC7BsT,EAAM,eAAiBtT,EAAU,eACjCsT,EAAM,oBAAsBtT,EAAU,oBACtCsT,EAAM,eAAiBtT,EAAU,eACjCsT,EAAM,mBAAqBtT,EAAU,mBACrCsT,EAAM,WAAatT,EAAU,WAC7BsT,EAAM,aAAetT,EAAU,YACjC,CChJA,SAASuT,EAAiB/E,EAAiBhH,EAAM,CAC/C,KAAM,CAAE,QAAA8H,EAAS,OAAA/O,CAAQ,EAAGiO,EAC5BgF,GAAiBjT,EAAQiH,EAAK,QAAS8H,CAAO,EAC9C,MAAMjO,EAAUmG,EAAK,OAAO,QAC5BjH,EAAO,MAAQc,EACfd,EAAO,MAAQc,EACfd,EAAO,MAAQc,EACfd,EAAO,MAAQc,CACjB,CCJA,MAAMoS,EAAa,CACjB,YAAY3T,EAAU,CACpB,KAAK,SAA2B,OAAO,OAAO,IAAI,EAClD,KAAK,wBAA0B,KAAK,kBAAkB,KAAK,IAAI,EAC/D,KAAK,UAAYA,EACjB,KAAK,UAAU,QAAQ,iBAAiB,IAAI,IAAI,EAChD,KAAK,UAAU,aAAa,eAAe,KAAM,UAAU,CAC5D,CACD,kBAAmB,CACjB,UAAWW,KAAK,KAAK,SAAU,CAC7B,MAAMiT,EAAU,KAAK,SAASjT,CAAC,EAC/B,GAAI,CAACiT,EACH,SACF,MAAMlM,EAAOkM,EAAQ,gBAAgB,WACjClM,EAAK,kBACPA,EAAK,YAAc,KAAK,UAAU,WAClCA,EAAK,aAAY,EAEpB,CACF,CACD,mBAAmBmM,EAAU,CAC3B,MAAMD,EAAU,KAAK,YAAYC,CAAQ,EACnCC,EAASD,EAAS,UACxB,OAAID,EAAQ,uBACVA,EAAQ,sBAAwB,GACzB,IAELA,EAAQ,aAAeE,CAI5B,CACD,cAAcD,EAAU1T,EAAgB,CAEtC,MAAMuO,EADU,KAAK,YAAYmF,CAAQ,EACT,gBAC5BA,EAAS,gBACX,KAAK,YAAYA,CAAQ,EAE3B,KAAK,UAAU,YAAY,MAAM,WAAWnF,EAAiBvO,CAAc,CAC5E,CACD,iBAAiB0T,EAAU,CAEzB,MAAMnF,EADU,KAAK,YAAYmF,CAAQ,EACT,gBAC5BA,EAAS,gBACX,KAAK,YAAYA,CAAQ,EAE3BnF,EAAgB,SAAS,cAAcA,CAAe,CACvD,CACD,kBAAkBmF,EAAU,CAC1BA,EAAS,IAAI,YAAa,KAAK,uBAAuB,EACtD,KAAK,uBAAuBA,EAAS,GAAG,CACzC,CACD,uBAAuBE,EAAa,CAClC,MAAMH,EAAU,KAAK,SAASG,CAAW,EACzC,KAAK,UAAU,SAAS,uBAAuBH,EAAQ,UAAU,EACjE9K,EAAQ,OAAO8K,EAAQ,eAAe,EACtC,KAAK,SAASG,CAAW,EAAI,IAC9B,CACD,YAAYF,EAAU,CACpB,MAAMC,EAASD,EAAS,UAClBD,EAAU,KAAK,YAAYC,CAAQ,EACnCnF,EAAkBkF,EAAQ,gBAC5BA,EAAQ,aAAeE,GACzB,KAAK,eAAeD,CAAQ,EAAE,MAAOG,GAAM,CACzC,QAAQ,MAAMA,CAAC,CACvB,CAAO,EAEHH,EAAS,eAAiB,GAC1BJ,EAAiB/E,EAAiBmF,CAAQ,CAC3C,CACD,MAAM,eAAeA,EAAU,CAC7BA,EAAS,eAAiB,GAC1B,MAAMD,EAAU,KAAK,YAAYC,CAAQ,EACzC,GAAID,EAAQ,kBACV,OACF,MAAME,EAASD,EAAS,UACxB,KAAK,UAAU,SAAS,uBAAuBD,EAAQ,UAAU,EACjEA,EAAQ,kBAAoB,GAC5BA,EAAQ,WAAaE,EACrB,MAAMxS,EAAauS,EAAS,YAAc,KAAK,UAAU,WACnDrE,EAAU,MAAM,KAAK,UAAU,SAAS,kBAC5CqE,EAAS,KACTvS,EACAuS,EAAS,OACTA,EAAS,QAAS,CACxB,EACUnF,EAAkBkF,EAAQ,gBAChClF,EAAgB,QAAUkF,EAAQ,QAAUpE,EAC5CoE,EAAQ,kBAAoB,GAC5BA,EAAQ,sBAAwB,GAChCC,EAAS,aAAY,EACrBJ,EAAiB/E,EAAiBmF,CAAQ,CAC3C,CACD,YAAYA,EAAU,CACpB,OAAO,KAAK,SAASA,EAAS,GAAG,GAAK,KAAK,YAAYA,CAAQ,CAChE,CACD,YAAYA,EAAU,CACpB,MAAMI,EAAc,CAClB,QAAS9R,EAAQ,MACjB,WAAY,KACZ,gBAAiB2G,EAAQ,IAAIoL,EAAe,EAC5C,sBAAuB,GACvB,kBAAmB,EACzB,EACUxF,EAAkBuF,EAAY,gBACpC,OAAAvF,EAAgB,WAAamF,EAC7BnF,EAAgB,UAAYmF,EAAS,eACrCnF,EAAgB,QAAUvM,EAAQ,MAClCuM,EAAgB,OAAS,CAAE,KAAM,EAAG,KAAM,EAAG,KAAM,EAAG,KAAM,CAAC,EAC7DA,EAAgB,YAAc,KAAK,UAAU,aAAemF,EAAS,aACrEA,EAAS,YAAcA,EAAS,gBAAkB,KAAK,UAAU,WAAaA,EAAS,WACvF,KAAK,SAASA,EAAS,GAAG,EAAII,EAC9BJ,EAAS,GAAG,YAAa,KAAK,uBAAuB,EAC9CI,CACR,CACD,SAAU,CACR,UAAWtT,KAAK,KAAK,SACnB,KAAK,uBAAuBA,CAAC,EAE/B,KAAK,SAAW,KAChB,KAAK,UAAY,IAClB,CACH,CAEAgT,GAAa,UAAY,CACvB,KAAM,CACJlU,EAAc,WACdA,EAAc,YACdA,EAAc,WACf,EACD,KAAM,UACR,ECvIA,SAAS0U,IAAW,CAClB,KAAM,CAAE,UAAAC,CAAS,EAAKC,GAAW,IAAK,EAAC,aAAY,EACnD,MAAO,iCAAiC,KAAKD,CAAS,CACxD,CCFA,MAAME,GAAa,IAAIrQ,GACvB,SAASsQ,GAAwBC,EAAOnV,EAAOC,EAAQgC,EAAY,CACjE,MAAMb,EAAS6T,GACf7T,EAAO,KAAO,EACdA,EAAO,KAAO,EACdA,EAAO,KAAO+T,EAAM,MAAQlT,EAAa,EACzCb,EAAO,KAAO+T,EAAM,OAASlT,EAAa,EAC1C,MAAMkO,EAAUxN,EAAY,kBAC1BvB,EAAO,MACPA,EAAO,OACPa,EACA,EACJ,EACE,OAAAkO,EAAQ,OAAO,eAAiB,QAChCA,EAAQ,OAAO,SAAWgF,EAC1BhF,EAAQ,OAAO,UAAY,8BAC3BA,EAAQ,MAAM,MAAQnQ,EAAQiC,EAC9BkO,EAAQ,MAAM,OAASlQ,EAASgC,EAChCkO,EAAQ,OAAO,KAAK,SAAUA,EAAQ,MAAM,EAC5CA,EAAQ,UAAS,EACVA,CACT,CCxBA,SAASiF,GAAoB/M,EAAMjC,EAAO,CACxC,MAAMsN,EAAatN,EAAM,WACnBiP,EAAe,CAAA,EACfC,EAAS,CAAA,EACTC,EAAQ,0BACRC,EAAUnN,EAAK,MAAMkN,CAAK,EAChC,SAASE,EAAcC,EAAa,CAC7BJ,EAAOI,CAAW,IACrBL,EAAa,KAAKK,CAAW,EAC7BJ,EAAOI,CAAW,EAAI,GAEzB,CACD,GAAI,MAAM,QAAQhC,CAAU,EAC1B,QAASpS,EAAI,EAAGA,EAAIoS,EAAW,OAAQpS,IACrCmU,EAAc/B,EAAWpS,CAAC,CAAC,OAG7BmU,EAAc/B,CAAU,EAEtB8B,GACFA,EAAQ,QAASG,GAAU,CACzB,MAAMD,EAAcC,EAAM,MAAM,GAAG,EAAE,CAAC,EAAE,OACxCF,EAAcC,CAAW,CAC/B,CAAK,EAEH,UAAWpU,KAAK8E,EAAM,UAAW,CAC/B,MAAMsP,EAActP,EAAM,UAAU9E,CAAC,EAAE,WACvCmU,EAAcC,CAAW,CAC1B,CACD,OAAOL,CACT,CC5BA,eAAeO,GAAiBC,EAAK,CAEnC,MAAMC,EAAO,MADI,MAAMd,GAAW,IAAG,EAAG,MAAMa,CAAG,GACrB,OACtBE,EAAS,IAAI,WAMnB,OALgB,MAAM,IAAI,QAAQ,CAACC,EAASC,IAAW,CACrDF,EAAO,UAAY,IAAMC,EAAQD,EAAO,MAAM,EAC9CA,EAAO,QAAUE,EACjBF,EAAO,cAAcD,CAAI,CAC7B,CAAG,CAEH,CCVA,eAAeI,GAAY9P,EAAOyP,EAAK,CACrC,MAAMM,EAAU,MAAMP,GAAiBC,CAAG,EAC1C,MAAO;AAAA,wBACezP,EAAM,UAAU;AAAA,oBACpB+P,CAAO;AAAA,uBACJ/P,EAAM,UAAU;AAAA,sBACjBA,EAAM,SAAS;AAAA,MAErC,CCPA,MAAMgQ,EAAwC,IAAI,IAClD,eAAeC,GAAWhB,EAAcjP,EAAOkQ,EAAgB,CAC7D,MAAMC,EAAelB,EAAa,OAAQ3B,GAAeE,EAAM,IAAI,GAAGF,CAAU,UAAU,CAAC,EAAE,IAAI,CAACA,EAAYpS,IAAM,CAClH,GAAI,CAAC8U,EAAsB,IAAI1C,CAAU,EAAG,CAC1C,KAAM,CAAE,IAAAmC,CAAK,EAAGjC,EAAM,IAAI,GAAGF,CAAU,UAAU,EAC7CpS,IAAM,EACR8U,EAAsB,IAAI1C,EAAYwC,GAAY,CAChD,WAAY9P,EAAM,WAClB,UAAWA,EAAM,UACjB,WAAAsN,CACV,EAAWmC,CAAG,CAAC,EAEPO,EAAsB,IAAI1C,EAAYwC,GAAY,CAChD,WAAYI,EAAe,WAC3B,UAAWA,EAAe,UAC1B,WAAA5C,CACV,EAAWmC,CAAG,CAAC,CAEV,CACD,OAAOO,EAAsB,IAAI1C,CAAU,CAC/C,CAAG,EACD,OAAQ,MAAM,QAAQ,IAAI6C,CAAY,GAAG,KAAK;AAAA,CAAI,CACpD,CCzBA,SAASC,GAAUnO,EAAMjC,EAAOnE,EAAYwU,EAASC,EAAc,CACjE,KAAM,CAAE,WAAAxO,EAAY,aAAAD,EAAc,QAAAD,CAAO,EAAK0O,EAC9CxO,EAAW,UAAY,UAAU9B,EAAM,QAAQ,mCAAmCiC,CAAI,SACtFH,EAAW,aAAa,QAAS,oBAAoBjG,CAAU,qDAAqD,EACpHgG,EAAa,YAAcwO,EAC3B,KAAM,CAAE,MAAAzW,EAAO,OAAAC,GAAWyW,EAAa,MACvC,OAAA1O,EAAQ,aAAa,QAAShI,EAAM,SAAU,CAAA,EAC9CgI,EAAQ,aAAa,SAAU/H,EAAO,SAAU,CAAA,EACzC,IAAI,cAAa,EAAG,kBAAkB+H,CAAO,CACtD,CCPA,SAAS2O,GAA4BxB,EAAOlT,EAAY,CACtD,MAAM2U,EAAmBC,EAAW,2BAClC1B,EAAM,MACNA,EAAM,OACNlT,CACJ,EACQ,CAAE,QAAA6G,CAAS,EAAG8N,EACpB,OAAA9N,EAAQ,UAAU,EAAG,EAAGqM,EAAM,MAAOA,EAAM,MAAM,EACjDrM,EAAQ,UAAUqM,EAAO,EAAG,CAAC,EACtByB,CACT,CCZA,SAASE,GAAa3B,EAAOU,EAAKkB,EAAO,CACvC,OAAO,IAAI,QAAQ,MAAOf,GAAY,CAChCe,GACF,MAAM,IAAI,QAASC,GAAa,WAAWA,EAAU,GAAG,CAAC,EAE3D7B,EAAM,OAAS,IAAM,CACnBa,GACN,EACIb,EAAM,IAAM,mCAAmC,mBAAmBU,CAAG,CAAC,GACtEV,EAAM,YAAc,WACxB,CAAG,CACH,CCMA,MAAM8B,EAAe,CACnB,YAAYtW,EAAU,CACpB,KAAK,gBAAkB,GACvB,KAAK,UAAYA,EACjB,KAAK,cAAgBA,EAAS,OAASgE,EAAa,MACrD,CACD,WAAW7E,EAAS,CAClB,OAAO,KAAK,qBACVA,EAAQ,KACRA,EAAQ,WACRA,EAAQ,KACd,CACG,CACD,kBAAkBuI,EAAMpG,EAAYmE,EAAO8Q,EAAS,CAClD,GAAI,KAAK,gBAAgBA,CAAO,EAC9B,YAAK,wBAAwBA,CAAO,EAC7B,KAAK,gBAAgBA,CAAO,EAAE,QAEvC,MAAMC,EAAU,KAAK,qBAAqB9O,EAAMpG,EAAYmE,CAAK,EAAE,KAAM+J,IACvE,KAAK,gBAAgB+G,CAAO,EAAE,QAAU/G,EACjCA,EACR,EACD,YAAK,gBAAgB+G,CAAO,EAAI,CAC9B,QAAS,KACT,QAAAC,EACA,WAAY,CAClB,EACWA,CACR,CACD,MAAM,qBAAqB9O,EAAMpG,EAAYmE,EAAO,CAClD,MAAMsQ,EAAejN,EAAQ,IAAI3B,EAAkB,EAC7CuN,EAAeD,GAAoB/M,EAAMjC,CAAK,EAC9CqQ,EAAU,MAAMJ,GACpBhB,EACAjP,EACAkB,GAAc,gBACpB,EACU8P,EAAWhP,GAAgBC,EAAMjC,EAAOqQ,EAASC,CAAY,EAC7D1W,EAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,EAAGoX,EAAS,KAAK,EAAIhR,EAAM,QAAU,CAAC,EAAInE,CAAU,EACzFhC,EAAS,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,EAAGmX,EAAS,MAAM,EAAIhR,EAAM,QAAU,CAAC,EAAInE,CAAU,EAC3FkT,EAAQuB,EAAa,MACrBW,EAAe,EACrBlC,EAAM,OAASnV,EAAQ,GAAKqX,EAC5BlC,EAAM,QAAUlV,EAAS,GAAKoX,EAC9B,MAAMC,EAASd,GAAUnO,EAAMjC,EAAOnE,EAAYwU,EAASC,CAAY,EACvE,MAAMI,GAAa3B,EAAOmC,EAAQxC,GAAU,GAAIO,EAAa,OAAS,CAAC,EACvE,MAAMkC,EAAWpC,EACjB,IAAIyB,EACA,KAAK,gBACPA,EAAmBD,GAA4BxB,EAAOlT,CAAU,GAElE,MAAMkO,EAAU+E,GACd0B,EAAmBA,EAAiB,OAASW,EAC7CpC,EAAM,MAAQkC,EACdlC,EAAM,OAASkC,EACfpV,CACN,EACI,OAAI,KAAK,gBACP,KAAK,UAAU,QAAQ,WAAWkO,EAAQ,MAAM,EAChD0G,EAAW,uBAAuBD,CAAgB,GAEpDnN,EAAQ,OAAOiN,CAAY,EACpBvG,CACR,CACD,wBAAwB+G,EAAS,CAC/B,KAAK,gBAAgBA,CAAO,EAAE,YAC/B,CACD,uBAAuBA,EAAS,CAC9B,MAAMM,EAAgB,KAAK,gBAAgBN,CAAO,EAC7CM,IAELA,EAAc,aACVA,EAAc,aAAe,IAC3BA,EAAc,QAChB,KAAK,SAASA,CAAa,EAE3BA,EAAc,QAAQ,KAAMrH,GAAY,CACtCqH,EAAc,QAAUrH,EACxB,KAAK,SAASqH,CAAa,CACrC,CAAS,EAAE,MAAM,IAAM,CACbhV,EAAK,yCAAyC,CACxD,CAAS,EAEH,KAAK,gBAAgB0U,CAAO,EAAI,MAEnC,CACD,SAASM,EAAe,CACtB7U,EAAY,cAAc6U,EAAc,OAAO,EAC/CA,EAAc,QAAQ,OAAO,SAAW,KACxCA,EAAc,QAAQ,OAAO,eAAiB,SAC/C,CACD,kBAAkBN,EAAS,CACzB,OAAO,KAAK,gBAAgBA,CAAO,EAAE,UACtC,CACD,SAAU,CACR,KAAK,gBAAkB,IACxB,CACH,CAEAD,GAAe,UAAY,CACzB,KAAM,CACJ7W,EAAc,YACdA,EAAc,aACdA,EAAc,YACf,EACD,KAAM,UACR,EACA6W,GAAe,mBAAqB,CAClC,WAAY,QACZ,UAAW,SACX,WAAY,QACd,EC3HA,MAAMQ,EAAe,CACnB,YAAY9W,EAAU,CACpB,KAAK,SAA2B,OAAO,OAAO,IAAI,EAClD,KAAK,wBAA0B,KAAK,kBAAkB,KAAK,IAAI,EAC/D,KAAK,UAAYA,EACjB,KAAK,UAAU,QAAQ,iBAAiB,IAAI,IAAI,EAChD,KAAK,UAAU,aAAa,eAAe,KAAM,UAAU,CAC5D,CACD,kBAAmB,CACjB,UAAWW,KAAK,KAAK,SAAU,CAC7B,MAAMiT,EAAU,KAAK,SAASjT,CAAC,EAC/B,GAAI,CAACiT,EACH,SACF,MAAMlM,EAAOkM,EAAQ,gBAAgB,WACjClM,EAAK,kBACPA,EAAK,YAAc,KAAK,UAAU,WAClCA,EAAK,aAAY,EAEpB,CACF,CACD,mBAAmBA,EAAM,CACvB,MAAMkM,EAAU,KAAK,YAAYlM,CAAI,EAC/BoM,EAASpM,EAAK,UACpB,OAAIkM,EAAQ,aAAeE,CAI5B,CACD,cAAcpM,EAAMvH,EAAgB,CAElC,MAAMuO,EADU,KAAK,YAAYhH,CAAI,EACL,gBAC5BA,EAAK,gBACP,KAAK,YAAYA,CAAI,EAEvB,KAAK,UAAU,YAAY,MAAM,WAAWgH,EAAiBvO,CAAc,CAC5E,CACD,iBAAiBuH,EAAM,CAErB,MAAMgH,EADU,KAAK,YAAYhH,CAAI,EACL,gBAC5BA,EAAK,gBACP,KAAK,YAAYA,CAAI,EAEvBgH,EAAgB,SAAS,cAAcA,CAAe,CACvD,CACD,kBAAkBhH,EAAM,CACtBA,EAAK,IAAI,YAAa,KAAK,uBAAuB,EAClD,KAAK,uBAAuBA,EAAK,GAAG,CACrC,CACD,uBAAuBqP,EAAS,CAC9B,MAAMnD,EAAU,KAAK,SAASmD,CAAO,EACrC,KAAK,UAAU,WAAW,uBAAuBnD,EAAQ,UAAU,EACnE9K,EAAQ,OAAO8K,EAAQ,eAAe,EACtC,KAAK,SAASmD,CAAO,EAAI,IAC1B,CACD,YAAYrP,EAAM,CAChB,MAAMoM,EAASpM,EAAK,UACdkM,EAAU,KAAK,YAAYlM,CAAI,EAC/BgH,EAAkBkF,EAAQ,gBAC5BA,EAAQ,aAAeE,GACzB,KAAK,eAAepM,CAAI,EAE1BA,EAAK,eAAiB,GACtB+L,EAAiB/E,EAAiBhH,CAAI,CACvC,CACD,eAAeA,EAAM,CACnB,MAAMkM,EAAU,KAAK,YAAYlM,CAAI,EAC/BgH,EAAkBkF,EAAQ,gBAC5BA,EAAQ,SACV,KAAK,UAAU,WAAW,uBAAuBA,EAAQ,UAAU,EAErEA,EAAQ,QAAUlF,EAAgB,QAAU,KAAK,UAAU,WAAW,kBAAkBhH,CAAI,EAC5FkM,EAAQ,WAAalM,EAAK,UAC1BgH,EAAgB,QAAUkF,EAAQ,OACnC,CACD,YAAYlM,EAAM,CAChB,OAAO,KAAK,SAASA,EAAK,GAAG,GAAK,KAAK,YAAYA,CAAI,CACxD,CACD,YAAYA,EAAM,CAChB,MAAMuM,EAAc,CAClB,QAAS,KACT,WAAY,KACZ,gBAAiBnL,EAAQ,IAAIoL,EAAe,CAClD,EACI,OAAAD,EAAY,gBAAgB,WAAavM,EACzCuM,EAAY,gBAAgB,UAAYvM,EAAK,eAC7CuM,EAAY,gBAAgB,OAAS,CAAE,KAAM,EAAG,KAAM,EAAG,KAAM,EAAG,KAAM,CAAC,EACzEA,EAAY,gBAAgB,YAAc,KAAK,UAAU,aAAevM,EAAK,aAC7E,KAAK,SAASA,EAAK,GAAG,EAAIuM,EAC1BvM,EAAK,YAAcA,EAAK,gBAAkB,KAAK,UAAU,WAAaA,EAAK,WAC3E,KAAK,YAAYA,CAAI,EACrBA,EAAK,GAAG,YAAa,KAAK,uBAAuB,EAC1CuM,CACR,CACD,SAAU,CACR,UAAWtT,KAAK,KAAK,SACnB,KAAK,uBAAuBA,CAAC,EAE/B,KAAK,SAAW,KAChB,KAAK,UAAY,IAClB,CACH,CAEAmW,GAAe,UAAY,CACzB,KAAM,CACJrX,EAAc,WACdA,EAAc,YACdA,EAAc,WACf,EACD,KAAM,MACR,EChHA,SAASuX,GAAS5S,EAAM/E,EAAOuD,EAAG,CAChC,QAASD,EAAI,EAAGuN,EAAQ,EAAItN,EAAIvD,EAAOsD,EAAItD,EAAO,EAAEsD,EAAGuN,GAAS,EAC9D,GAAI9L,EAAK8L,EAAQ,CAAC,IAAM,EACtB,MAAO,GAEX,MAAO,EACT,CACA,SAAS+G,GAAY7S,EAAM/E,EAAOsD,EAAGuU,EAAKC,EAAQ,CAChD,MAAMlH,EAAS,EAAI5Q,EACnB,QAASuD,EAAIsU,EAAKhH,EAAQgH,EAAMjH,EAAS,EAAItN,EAAGC,GAAKuU,EAAQ,EAAEvU,EAAGsN,GAASD,EACzE,GAAI7L,EAAK8L,EAAQ,CAAC,IAAM,EACtB,MAAO,GAEX,MAAO,EACT,CACA,SAASkH,GAAqBC,EAAQ/V,EAAa,EAAG,CACpD,KAAM,CAAE,MAAAjC,EAAO,OAAAC,CAAQ,EAAG+X,EACpBlP,EAAUkP,EAAO,WAAW,KAAM,CACtC,mBAAoB,EACxB,CAAG,EACD,GAAIlP,IAAY,KACd,MAAM,IAAI,UAAU,iCAAiC,EAGvD,MAAM/D,EADY+D,EAAQ,aAAa,EAAG,EAAG9I,EAAOC,CAAM,EACnC,KACvB,IAAIgY,EAAO,EACPJ,EAAM,EACNK,EAAQlY,EAAQ,EAChB8X,EAAS7X,EAAS,EACtB,KAAO4X,EAAM5X,GAAU0X,GAAS5S,EAAM/E,EAAO6X,CAAG,GAC9C,EAAEA,EACJ,GAAIA,IAAQ5X,EACV,OAAOkY,GAAU,MACnB,KAAOR,GAAS5S,EAAM/E,EAAO8X,CAAM,GACjC,EAAEA,EACJ,KAAOF,GAAY7S,EAAM/E,EAAOiY,EAAMJ,EAAKC,CAAM,GAC/C,EAAEG,EACJ,KAAOL,GAAY7S,EAAM/E,EAAOkY,EAAOL,EAAKC,CAAM,GAChD,EAAEI,EACJ,QAAEA,EACF,EAAEJ,EACK,IAAIK,GAAUF,EAAOhW,EAAY4V,EAAM5V,GAAaiW,EAAQD,GAAQhW,GAAa6V,EAASD,GAAO5V,CAAU,CACpH,CC/BA,MAAMmW,EAAiB,CACrB,YAAYC,EAAW,CACrB,KAAK,gBAAkB,GACvB,KAAK,UAAYA,CAClB,CACD,eAAehQ,EAAMpG,EAAYmE,EAAO,CACtC,MAAMgR,EAAWkB,EAAkB,YAAYjQ,GAAQ,IAAKjC,CAAK,EACjE,IAAIpG,EAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,EAAGoX,EAAS,KAAK,EAAIhR,EAAM,QAAU,CAAC,EAAInE,CAAU,EACzFhC,EAAS,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,EAAGmX,EAAS,MAAM,EAAIhR,EAAM,QAAU,CAAC,EAAInE,CAAU,EAC/F,OAAAjC,EAAQ,KAAK,KAAKA,EAAQ,IAAI,EAC9BC,EAAS,KAAK,KAAKA,EAAS,IAAI,EAChCD,EAAQuY,GAASvY,CAAK,EACtBC,EAASsY,GAAStY,CAAM,EACjB,CAAE,MAAAD,EAAO,OAAAC,EACjB,CACD,WAAWH,EAASmC,EAAYmE,EAAOoS,EAAU,CAC3C,OAAO1Y,GAAY,WACrBwF,EAAY,QAAS,mFAAmF,EACxGxF,EAAU,CACR,KAAMA,EACN,MAAAsG,EACA,WAAAnE,CACR,GAEUnC,EAAQ,iBAAiByH,IAC7BzH,EAAQ,MAAQ,IAAIyH,EAAUzH,EAAQ,KAAK,GAE7C,KAAM,CAAE,QAAAqQ,EAAS,iBAAAyG,CAAkB,EAAG,KAAK,uBACzC9W,CACN,EACI,YAAK,UAAU,QAAQ,WAAWqQ,EAAQ,OAAO,EACjD0G,EAAW,uBAAuBD,CAAgB,EAC3CzG,CACR,CACD,uBAAuBrQ,EAAS,CAC9B,KAAM,CAAE,KAAAuI,EAAM,MAAAjC,CAAO,EAAGtG,EAClBmC,EAAanC,EAAQ,YAAc,KAAK,UAAU,WAClDsX,EAAWkB,EAAkB,YAAYjQ,GAAQ,IAAKjC,CAAK,EAC3DpG,EAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,EAAGoX,EAAS,KAAK,EAAIhR,EAAM,QAAU,CAAC,EAAInE,CAAU,EACzFhC,EAAS,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,EAAGmX,EAAS,MAAM,EAAIhR,EAAM,QAAU,CAAC,EAAInE,CAAU,EAC3F2U,EAAmBC,EAAW,2BAA2B7W,EAAOC,CAAM,EACtE,CAAE,OAAA+X,CAAQ,EAAGpB,EACnB,KAAK,mBAAmBvO,EAAMjC,EAAOnE,EAAY2U,CAAgB,EACjE,MAAMzG,EAAU+E,GAAwB8C,EAAQhY,EAAOC,EAAQgC,CAAU,EACzE,GAAImE,EAAM,KAAM,CACd,MAAMqS,EAAUV,GAAqBC,EAAQ/V,CAAU,EACvDkO,EAAQ,MAAM,SAASsI,CAAO,EAC9BtI,EAAQ,UAAS,CAClB,CACD,MAAO,CAAE,QAAAA,EAAS,iBAAAyG,EACnB,CACD,kBAAkBvO,EAAM,CACtBA,EAAK,YAAcA,EAAK,gBAAkB,KAAK,UAAU,WAAaA,EAAK,WAC3E,MAAM6O,EAAU7O,EAAK,UACrB,GAAI,KAAK,gBAAgB6O,CAAO,EAC9B,YAAK,wBAAwBA,CAAO,EAC7B,KAAK,gBAAgBA,CAAO,EAAE,QAEvC,KAAM,CAAE,QAAA/G,EAAS,iBAAAyG,CAAgB,EAAK,KAAK,uBAAuBvO,CAAI,EACtE,YAAK,gBAAgB6O,CAAO,EAAI,CAC9B,iBAAAN,EACA,QAAAzG,EACA,WAAY,CAClB,EACWA,CACR,CACD,wBAAwB+G,EAAS,CAC/B,KAAK,gBAAgBA,CAAO,EAAE,YAC/B,CACD,uBAAuBA,EAAS,CAC9B,MAAMM,EAAgB,KAAK,gBAAgBN,CAAO,EAElD,GADAM,EAAc,aACVA,EAAc,aAAe,EAAG,CAClCX,EAAW,uBAAuBW,EAAc,gBAAgB,EAChE7U,EAAY,cAAc6U,EAAc,OAAO,EAC/C,MAAMkB,EAASlB,EAAc,QAAQ,OACrCkB,EAAO,SAAW,KAClBA,EAAO,eAAiB,UACxBA,EAAO,UAAY,uBACnB,KAAK,gBAAgBxB,CAAO,EAAI,IACjC,CACF,CACD,kBAAkBA,EAAS,CACzB,OAAO,KAAK,gBAAgBA,CAAO,EAAE,UACtC,CAYD,mBAAmB7O,EAAMjC,EAAOnE,EAAY2U,EAAkB,CAC5D,KAAM,CAAE,OAAAoB,EAAQ,QAAAlP,CAAS,EAAG8N,EACtB+B,EAAOC,GAAwBxS,CAAK,EACpCgR,EAAWkB,EAAkB,YAAYjQ,GAAQ,IAAKjC,CAAK,EAC3DyS,EAAQzB,EAAS,MACjB0B,EAAa1B,EAAS,WACtB2B,EAAa3B,EAAS,WACtB4B,EAAe5B,EAAS,aACxB6B,EAAiB7B,EAAS,eAC1BnX,EAAS+X,EAAO,OAItB,GAHAlP,EAAQ,eAAc,EACtBA,EAAQ,MAAM7G,EAAYA,CAAU,EACpC6G,EAAQ,aAAe1C,EAAM,aACzBA,EAAM,SAAS,MAAO,CACxB,MAAM8S,EAAc9S,EAAM,QAC1B0C,EAAQ,UAAYoQ,EAAY,MAChCpQ,EAAQ,WAAaoQ,EAAY,WACjCpQ,EAAQ,SAAWoQ,EAAY,KAC/BpQ,EAAQ,QAAUoQ,EAAY,GAC/B,CACDpQ,EAAQ,KAAO6P,EACf,IAAIQ,EACAC,EACJ,MAAMC,EAAcjT,EAAM,WAAa,EAAI,EAC3C,QAAS9E,EAAI,EAAGA,EAAI+X,EAAa,EAAE/X,EAAG,CACpC,MAAMgY,EAAelT,EAAM,YAAc9E,IAAM,EACzCiY,EAAeD,EAAe,KAAK,KAAK,KAAK,IAAI,EAAGrZ,CAAM,EAAImG,EAAM,QAAU,CAAC,EAAI,EACnFoT,EAAiBD,EAAetX,EACtC,GAAIqX,EAAc,CAChBxQ,EAAQ,UAAY,QACpBA,EAAQ,YAAc,QACtB,MAAM2Q,EAAgBrT,EAAM,WACtBsT,GAAkBD,EAAc,MAChCE,GAAkBF,EAAc,MACtC3Q,EAAQ,YAActC,EAAM,OAAO,SAASkT,EAAe,EAAE,SAASC,EAAe,EAAE,eACvF,MAAMC,GAAiBH,EAAc,KAAOxX,EACtC4X,GAAqBJ,EAAc,SAAWxX,EACpD6G,EAAQ,WAAa8Q,GACrB9Q,EAAQ,cAAgB,KAAK,IAAI2Q,EAAc,KAAK,EAAII,GACxD/Q,EAAQ,cAAgB,KAAK,IAAI2Q,EAAc,KAAK,EAAII,GAAqBL,CACrF,MACQ1Q,EAAQ,UAAY1C,EAAM,MAAQ0T,GAAmB1T,EAAM,MAAO0C,CAAO,EAAI,KACzE1C,EAAM,SAAS,QACjB0C,EAAQ,YAAcgR,GAAmB1T,EAAM,QAAS0C,CAAO,GAEjEA,EAAQ,YAAc,QAExB,IAAIiR,GAAsBjB,EAAaG,EAAe,UAAY,EAC9DH,EAAaG,EAAe,SAAW,IACzCc,EAAqB,GAEvB,MAAMC,EAAc5T,EAAM,SAAS,OAAS,EAC5C,QAAS6T,EAAK,EAAGA,EAAKpB,EAAM,OAAQoB,IAClCd,EAAgBa,EAAc,EAC9BZ,EAAgBY,EAAc,EAAIC,EAAKnB,EAAaG,EAAe,OAASc,EACxE3T,EAAM,QAAU,QAClB+S,GAAiBH,EAAeD,EAAWkB,CAAE,EACpC7T,EAAM,QAAU,WACzB+S,IAAkBH,EAAeD,EAAWkB,CAAE,GAAK,GAEjD7T,EAAM,SAAS,OACjB,KAAK,mBACHyS,EAAMoB,CAAE,EACR7T,EACAwQ,EACAuC,EAAgB/S,EAAM,QACtBgT,EAAgBhT,EAAM,QAAUmT,EAChC,EACZ,EAEYnT,EAAM,QAAU,QAClB,KAAK,mBACHyS,EAAMoB,CAAE,EACR7T,EACAwQ,EACAuC,EAAgB/S,EAAM,QACtBgT,EAAgBhT,EAAM,QAAUmT,CAC5C,CAGK,CACF,CAWD,mBAAmBlR,EAAMjC,EAAOwQ,EAAkBtT,EAAGC,EAAG2W,EAAW,GAAO,CACxE,KAAM,CAAE,QAAApR,CAAS,EAAG8N,EACduD,EAAgB/T,EAAM,cAC5B,IAAIgU,EAA+B,GAWnC,GAVI9B,EAAkB,qCAChBA,EAAkB,2BACpBxP,EAAQ,cAAgB,GAAGqR,CAAa,KACxCrR,EAAQ,kBAAoB,GAAGqR,CAAa,KAC5CC,EAA+B,KAE/BtR,EAAQ,cAAgB,MACxBA,EAAQ,kBAAoB,QAG5BqR,IAAkB,GAAKC,EAA8B,CACnDF,EACFpR,EAAQ,WAAWT,EAAM/E,EAAGC,CAAC,EAE7BuF,EAAQ,SAAST,EAAM/E,EAAGC,CAAC,EAE7B,MACD,CACD,IAAI8W,EAAkB/W,EACtB,MAAMgX,EAAchC,EAAkB,kBAAkBjQ,CAAI,EAC5D,IAAIkS,EAAgBzR,EAAQ,YAAYT,CAAI,EAAE,MAC1CmS,EAAe,EACnB,QAASlZ,EAAI,EAAGA,EAAIgZ,EAAY,OAAQ,EAAEhZ,EAAG,CAC3C,MAAMmZ,EAAcH,EAAYhZ,CAAC,EAC7B4Y,EACFpR,EAAQ,WAAW2R,EAAaJ,EAAiB9W,CAAC,EAElDuF,EAAQ,SAAS2R,EAAaJ,EAAiB9W,CAAC,EAElD,IAAImX,EAAU,GACd,QAASrT,EAAI/F,EAAI,EAAG+F,EAAIiT,EAAY,OAAQ,EAAEjT,EAC5CqT,GAAWJ,EAAYjT,CAAC,EAE1BmT,EAAe1R,EAAQ,YAAY4R,CAAO,EAAE,MAC5CL,GAAmBE,EAAgBC,EAAeL,EAClDI,EAAgBC,CACjB,CACF,CACD,SAAU,CACR,KAAK,gBAAkB,IACxB,CACH,CAEApC,GAAiB,UAAY,CAC3B,KAAM,CACJhY,EAAc,YACdA,EAAc,aACdA,EAAc,YACf,EACD,KAAM,YACR,EC3PAua,EAAW,IAAI9a,EAAY,EAC3B8a,EAAW,IAAIta,EAAY,ECD3Bsa,EAAW,IAAIjS,EAAY,EAC3BiS,EAAW,IAAIC,EAAqB,ECFpCD,EAAW,IAAI9P,EAAQ,ECCvB8P,EAAW,IAAInM,EAAuB,EACtCmM,EAAW,IAAIlM,EAAwB,ECDvCkM,EAAW,IAAIvC,EAAgB,EAC/BuC,EAAW,IAAIlD,EAAc,ECF7BkD,EAAW,IAAInI,EAAc,ECC7BmI,EAAW,IAAI1D,EAAc,EAC7B0D,EAAW,IAAIrG,EAAY,ECF3BqG,EAAW,IAAIrJ,EAAgB,ECA/BqJ,EAAW,IAAIxL,EAAmB,ECClCwL,EAAW,IAAIjZ,EAAY,EAC3BiZ,EAAW,IAAIja,EAAU", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63]}