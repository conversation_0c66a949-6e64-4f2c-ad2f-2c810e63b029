import{I as p}from"./Info-IGMCDo7y.js";/* empty css                                                        */const{SvelteComponent:k,attr:m,check_outros:$,create_component:v,create_slot:B,destroy_component:I,detach:b,element:q,empty:C,flush:d,get_all_dirty_from_scope:N,get_slot_changes:S,group_outros:T,init:j,insert:g,mount_component:z,safe_not_equal:A,space:D,toggle_class:a,transition_in:c,transition_out:h,update_slot_base:E}=window.__gradio__svelte__internal;function w(s){let e,i;return e=new p({props:{info:s[1]}}),{c(){v(e.$$.fragment)},m(o,_){z(e,o,_),i=!0},p(o,_){const n={};_&2&&(n.info=o[1]),e.$set(n)},i(o){i||(c(e.$$.fragment,o),i=!0)},o(o){h(e.$$.fragment,o),i=!1},d(o){I(e,o)}}}function F(s){let e,i,o,_,n;const u=s[4].default,f=B(u,s,s[3],null);let t=s[1]&&w(s);return{c(){e=q("span"),f&&f.c(),o=D(),t&&t.c(),_=C(),m(e,"data-testid","block-info"),m(e,"dir",i=s[2]?"rtl":"ltr"),m(e,"class","svelte-g2oxp3"),a(e,"sr-only",!s[0]),a(e,"hide",!s[0]),a(e,"has-info",s[1]!=null)},m(l,r){g(l,e,r),f&&f.m(e,null),g(l,o,r),t&&t.m(l,r),g(l,_,r),n=!0},p(l,[r]){f&&f.p&&(!n||r&8)&&E(f,u,l,l[3],n?S(u,l[3],r,null):N(l[3]),null),(!n||r&4&&i!==(i=l[2]?"rtl":"ltr"))&&m(e,"dir",i),(!n||r&1)&&a(e,"sr-only",!l[0]),(!n||r&1)&&a(e,"hide",!l[0]),(!n||r&2)&&a(e,"has-info",l[1]!=null),l[1]?t?(t.p(l,r),r&2&&c(t,1)):(t=w(l),t.c(),c(t,1),t.m(_.parentNode,_)):t&&(T(),h(t,1,1,()=>{t=null}),$())},i(l){n||(c(f,l),c(t),n=!0)},o(l){h(f,l),h(t),n=!1},d(l){l&&(b(e),b(o),b(_)),f&&f.d(l),t&&t.d(l)}}}function G(s,e,i){let{$$slots:o={},$$scope:_}=e,{show_label:n=!0}=e,{info:u=void 0}=e,{rtl:f=!1}=e;return s.$$set=t=>{"show_label"in t&&i(0,n=t.show_label),"info"in t&&i(1,u=t.info),"rtl"in t&&i(2,f=t.rtl),"$$scope"in t&&i(3,_=t.$$scope)},[n,u,f,_,o]}class K extends k{constructor(e){super(),j(this,e,G,F,A,{show_label:0,info:1,rtl:2})}get show_label(){return this.$$.ctx[0]}set show_label(e){this.$$set({show_label:e}),d()}get info(){return this.$$.ctx[1]}set info(e){this.$$set({info:e}),d()}get rtl(){return this.$$.ctx[2]}set rtl(e){this.$$set({rtl:e}),d()}}export{K as B};
//# sourceMappingURL=BlockTitle-Ct-h8ev5.js.map
