{"version": 3, "file": "Index.Dzsrn0Y_.js", "sources": ["../../../../../../../downloadbutton/shared/DownloadButton.svelte", "../../../../../../../downloadbutton/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { type FileData } from \"@gradio/client\";\n\timport { BaseButton } from \"@gradio/button\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let variant: \"primary\" | \"secondary\" | \"stop\" = \"secondary\";\n\texport let size: \"sm\" | \"md\" | \"lg\" = \"lg\";\n\texport let value: null | FileData;\n\texport let icon: null | FileData;\n\texport let disabled = false;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\tconst dispatch = createEventDispatcher();\n\n\tfunction download_file(): void {\n\t\tdispatch(\"click\");\n\t\tif (!value?.url) {\n\t\t\treturn;\n\t\t}\n\t\tlet file_name;\n\t\tif (!value.orig_name && value.url) {\n\t\t\tconst parts = value.url.split(\"/\");\n\t\t\tfile_name = parts[parts.length - 1];\n\t\t\tfile_name = file_name.split(\"?\")[0].split(\"#\")[0];\n\t\t} else {\n\t\t\tfile_name = value.orig_name;\n\t\t}\n\t\tconst a = document.createElement(\"a\");\n\t\ta.href = value.url;\n\t\ta.download = file_name || \"file\";\n\t\tdocument.body.appendChild(a);\n\t\ta.click();\n\t\tdocument.body.removeChild(a);\n\t}\n</script>\n\n<BaseButton\n\t{size}\n\t{variant}\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\ton:click={download_file}\n\t{scale}\n\t{min_width}\n\t{disabled}\n>\n\t{#if icon}\n\t\t<img class=\"button-icon\" src={icon.url} alt={`${value} icon`} />\n\t{/if}\n\t<slot />\n</BaseButton>\n\n<style>\n\t.button-icon {\n\t\twidth: var(--text-xl);\n\t\theight: var(--text-xl);\n\t\tmargin-right: var(--spacing-xl);\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseButton } from \"./shared/DownloadButton.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport { type FileData } from \"@gradio/client\";\n\n\timport DownloadButton from \"./shared/DownloadButton.svelte\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: null | FileData;\n\texport let variant: \"primary\" | \"secondary\" | \"stop\" = \"secondary\";\n\texport let interactive: boolean;\n\texport let size: \"sm\" | \"lg\" = \"lg\";\n\texport let scale: number | null = null;\n\texport let icon: null | FileData = null;\n\texport let min_width: number | undefined = undefined;\n\texport let label: string | null = null;\n\texport let gradio: Gradio<{\n\t\tclick: never;\n\t}>;\n</script>\n\n<DownloadButton\n\t{value}\n\t{variant}\n\t{elem_id}\n\t{elem_classes}\n\t{size}\n\t{scale}\n\t{icon}\n\t{min_width}\n\t{visible}\n\tdisabled={!interactive}\n\ton:click={() => gradio.dispatch(\"click\")}\n>\n\t{label ?? \"\"}\n</DownloadButton>\n"], "names": ["src_url_equal", "img", "img_src_value", "ctx", "attr", "insert_hydration", "target", "anchor", "dirty", "create_if_block", "elem_id", "$$props", "elem_classes", "visible", "variant", "size", "value", "icon", "disabled", "scale", "min_width", "dispatch", "createEventDispatcher", "download_file", "file_name", "parts", "a", "t_value", "set_data", "t", "interactive", "label", "gradio"], "mappings": "4tBAmDgCA,EAAAC,EAAA,IAAAC,EAAAC,KAAK,GAAG,GAAAC,EAAAH,EAAA,MAAAC,CAAA,iBAAUC,EAAK,CAAA,CAAA,OAAA,UAArDE,EAA+DC,EAAAL,EAAAM,CAAA,UAAjCC,EAAA,IAAA,CAAAR,EAAAC,EAAA,IAAAC,EAAAC,KAAK,GAAG,gCAAUA,EAAK,CAAA,CAAA,+DADjDA,EAAI,CAAA,GAAAM,EAAAN,CAAA,sKAAJA,EAAI,CAAA,saALCA,EAAa,EAAA,CAAA,qcAxCZ,QAAAO,EAAU,EAAA,EAAAC,EACV,CAAA,aAAAC,EAAA,EAAA,EAAAD,GACA,QAAAE,EAAU,EAAA,EAAAF,GACV,QAAAG,EAA4C,WAAA,EAAAH,GAC5C,KAAAI,EAA2B,IAAA,EAAAJ,EAC3B,CAAA,MAAAK,CAAA,EAAAL,EACA,CAAA,KAAAM,CAAA,EAAAN,GACA,SAAAO,EAAW,EAAA,EAAAP,GACX,MAAAQ,EAAuB,IAAA,EAAAR,GACvB,UAAAS,EAAgC,MAAA,EAAAT,QACrCU,EAAWC,IAER,SAAAC,GAAA,IACRF,EAAS,OAAO,IACXL,GAAA,MAAAA,EAAO,YAGR,IAAAQ,EACC,GAAA,CAAAR,EAAM,WAAaA,EAAM,IAAA,OACvBS,EAAQT,EAAM,IAAI,MAAM,GAAG,EACjCQ,EAAYC,EAAMA,EAAM,OAAS,CAAC,EAClCD,EAAYA,EAAU,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,OAEhDA,EAAYR,EAAM,gBAEbU,EAAI,SAAS,cAAc,GAAG,EACpCA,EAAE,KAAOV,EAAM,IACfU,EAAE,SAAWF,GAAa,OAC1B,SAAS,KAAK,YAAYE,CAAC,EAC3BA,EAAE,MAAA,EACF,SAAS,KAAK,YAAYA,CAAC,qkBCI3B,IAAAC,GAAAxB,OAAS,IAAE,+DAAXK,EAAA,MAAAmB,KAAAA,GAAAxB,OAAS,IAAE,KAAAyB,EAAAC,EAAAF,CAAA,2LAHDxB,EAAW,CAAA,wXAAXA,EAAW,CAAA,wJA1BX,QAAAO,EAAU,EAAA,EAAAC,EACV,CAAA,aAAAC,EAAA,EAAA,EAAAD,GACA,QAAAE,EAAU,EAAA,EAAAF,EACV,CAAA,MAAAK,CAAA,EAAAL,GACA,QAAAG,EAA4C,WAAA,EAAAH,EAC5C,CAAA,YAAAmB,CAAA,EAAAnB,GACA,KAAAI,EAAoB,IAAA,EAAAJ,GACpB,MAAAQ,EAAuB,IAAA,EAAAR,GACvB,KAAAM,EAAwB,IAAA,EAAAN,GACxB,UAAAS,EAAgC,MAAA,EAAAT,GAChC,MAAAoB,EAAuB,IAAA,EAAApB,EACvB,CAAA,OAAAqB,CAAA,EAAArB,cAgBKqB,EAAO,SAAS,OAAO"}