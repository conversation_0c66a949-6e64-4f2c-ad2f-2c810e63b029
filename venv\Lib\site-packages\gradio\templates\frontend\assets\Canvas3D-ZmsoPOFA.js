const __vite__fileDeps=["./index-Dpxo-yl_.js","./index-B7J2Z2jS.js","./index-CJsBH6a-.css"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as O}from"./index-B7J2Z2jS.js";import{r as V}from"./file-url-DoxvUUVV.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import"./svelte/svelte.js";const{SvelteComponent:A,binding_callbacks:L,detach:E,element:S,flush:a,init:k,insert:D,noop:y,safe_not_equal:j}=window.__gradio__svelte__internal,{onMount:B}=window.__gradio__svelte__internal;function U(n){let t;return{c(){t=S("canvas")},m(i,s){D(i,t,s),n[13](t)},p:y,i:y,o:y,d(i){i&&E(t),n[13](null)}}}function W(n,t,i){let s,b,{value:m}=t,{display_mode:c}=t,{clear_color:f}=t,{camera_position:h}=t,{zoom_speed:g}=t,{pan_speed:v}=t,{resolved_url:_=void 0}=t,p,d,l,u,w=!1;B(()=>((async()=>{b=await O(()=>import("./index-Dpxo-yl_.js").then(r=>r.bY),__vite__mapDeps([0,1,2]),import.meta.url),b.createViewerForCanvas(d,{clearColor:f,useRightHandedSystem:!0,animationAutoPlay:!0,cameraAutoOrbit:{enabled:!1},onInitialized:r=>{u=r}}).then(r=>{l=r,i(11,w=!0)})})(),()=>{l?.dispose()}));function x(e,r){u.scene.forcePointsCloud=e,u.scene.forceWireframe=r}function P(e){l&&(e?l.loadModel(e,{pluginOptions:{obj:{importVertexColors:!0}}}).then(()=>{c==="point_cloud"?x(!0,!1):c==="wireframe"?x(!1,!0):C(h,g,v)}):l.resetModel())}function C(e,r,M){const o=u.camera;e[0]!==null&&(o.alpha=e[0]*Math.PI/180),e[1]!==null&&(o.beta=e[1]*Math.PI/180),e[2]!==null&&(o.radius=e[2]),o.lowerRadiusLimit=.1;const z=()=>{o.wheelPrecision=250/(o.radius*r),o.panningSensibility=1e4*M/o.radius};z(),o.onAfterCheckInputsObservable.add(z)}function R(){u&&l.resetCamera()}function I(e){L[e?"unshift":"push"](()=>{d=e,i(0,d)})}return n.$$set=e=>{"value"in e&&i(2,m=e.value),"display_mode"in e&&i(3,c=e.display_mode),"clear_color"in e&&i(4,f=e.clear_color),"camera_position"in e&&i(5,h=e.camera_position),"zoom_speed"in e&&i(6,g=e.zoom_speed),"pan_speed"in e&&i(7,v=e.pan_speed),"resolved_url"in e&&i(1,_=e.resolved_url)},n.$$.update=()=>{if(n.$$.dirty&4&&i(12,s=m.url),n.$$.dirty&5120&&(i(1,_=s),s)){i(10,p=s);const e=s;V(s).then(r=>{p===e?i(1,_=r??void 0):r&&URL.revokeObjectURL(r)})}n.$$.dirty&2050&&w&&P(_)},[d,_,m,c,f,h,g,v,C,R,p,w,s,I]}class N extends A{constructor(t){super(),k(this,t,W,U,j,{value:2,display_mode:3,clear_color:4,camera_position:5,zoom_speed:6,pan_speed:7,resolved_url:1,update_camera:8,reset_camera_position:9})}get value(){return this.$$.ctx[2]}set value(t){this.$$set({value:t}),a()}get display_mode(){return this.$$.ctx[3]}set display_mode(t){this.$$set({display_mode:t}),a()}get clear_color(){return this.$$.ctx[4]}set clear_color(t){this.$$set({clear_color:t}),a()}get camera_position(){return this.$$.ctx[5]}set camera_position(t){this.$$set({camera_position:t}),a()}get zoom_speed(){return this.$$.ctx[6]}set zoom_speed(t){this.$$set({zoom_speed:t}),a()}get pan_speed(){return this.$$.ctx[7]}set pan_speed(t){this.$$set({pan_speed:t}),a()}get resolved_url(){return this.$$.ctx[1]}set resolved_url(t){this.$$set({resolved_url:t}),a()}get update_camera(){return this.$$.ctx[8]}get reset_camera_position(){return this.$$.ctx[9]}}export{N as default};
//# sourceMappingURL=Canvas3D-ZmsoPOFA.js.map
