#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>
#include <ATen/ops/reflection_pad1d_meta.h>

namespace at {
namespace native {
struct TORCH_API structured_reflection_pad1d_out_cpu : public at::meta::structured_reflection_pad1d {
void impl(const at::Tensor & self, at::ArrayRef<int64_t> padding, const at::Tensor & out);
};
struct TORCH_API structured_reflection_pad1d_out_cuda : public at::meta::structured_reflection_pad1d {
void impl(const at::Tensor & self, at::ArrayRef<int64_t> padding, const at::Tensor & out);
};
TORCH_API at::Tensor & reflection_pad1d_out_quantized_cpu(const at::Tensor & self, at::IntArrayRef padding, at::Tensor & out);
} // namespace native
} // namespace at
