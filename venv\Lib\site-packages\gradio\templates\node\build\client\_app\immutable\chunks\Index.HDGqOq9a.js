const __vite__fileDeps=["./vega-embed.module.BU0P0uZZ.js","./vega-tooltip.module.sYf3DH7j.js","./2.B2AoQPnG.js","./preload-helper.D6kgxu3v.js","./stores.z8sZTwoA.js","./client.Cd1aarwx.js","../assets/2.BTQDGmJF.css","./time.Ddv6ux-b.js","./step.Ce-xBr2D.js","./linear.BVb3otZY.js","./init.Dmth1JHB.js","./dsv.DB8NKgIY.js","./range.OtVwhkKS.js","./ordinal.BJp8kCrd.js","./arc.CL2LCPr_.js","./dispatch.kxCwF96_.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as ot}from"./preload-helper.D6kgxu3v.js";import{SvelteComponent as rt,init as at,safe_not_equal as ft,binding_callbacks as qe,bind as st,create_component as Y,claim_component as U,mount_component as G,add_flush_callback as ut,transition_in as p,transition_out as N,destroy_component as J,onMount as ct,space as ue,empty as _e,claim_space as ce,insert_hydration as q,group_outros as Ie,check_outros as ze,detach as M,assign as _t,get_spread_update as mt,get_spread_object as dt,text as xe,claim_text as Be,set_data as Re,element as Ye,claim_element as Ue,children as Ge,attr as Je,noop as Me,append_hydration as gt}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{B as bt,y as ht,S as yt}from"./2.B2AoQPnG.js";import{E as kt}from"./Empty.DwQ6nkN6.js";import{L as vt}from"./LineChart.C8bDl53w.js";import{I as wt}from"./IconButtonWrapper.D5aGR59h.js";import{F as Ft}from"./FullscreenButton.g_8wwg6y.js";function De(n){let e,t;const i=[{autoscroll:n[3].autoscroll},{i18n:n[3].i18n},n[11]];let a={};for(let o=0;o<i.length;o+=1)a=_t(a,i[o]);return e=new yt({props:a}),e.$on("clear_status",n[48]),{c(){Y(e.$$.fragment)},l(o){U(e.$$.fragment,o)},m(o,f){G(e,o,f),t=!0},p(o,f){const c=f[0]&2056?mt(i,[f[0]&8&&{autoscroll:o[3].autoscroll},f[0]&8&&{i18n:o[3].i18n},f[0]&2048&&dt(o[11])]):{};e.$set(c)},i(o){t||(p(e.$$.fragment,o),t=!0)},o(o){N(e.$$.fragment,o),t=!1},d(o){J(e,o)}}}function Le(n){let e,t;return e=new wt({props:{$$slots:{default:[Et]},$$scope:{ctx:n}}}),{c(){Y(e.$$.fragment)},l(i){U(e.$$.fragment,i)},m(i,a){G(e,i,a),t=!0},p(i,a){const o={};a[0]&8192|a[2]&32&&(o.$$scope={dirty:a,ctx:i}),e.$set(o)},i(i){t||(p(e.$$.fragment,i),t=!0)},o(i){N(e.$$.fragment,i),t=!1},d(i){J(e,i)}}}function Et(n){let e,t;return e=new Ft({props:{fullscreen:n[13]}}),e.$on("fullscreen",n[49]),{c(){Y(e.$$.fragment)},l(i){U(e.$$.fragment,i)},m(i,a){G(e,i,a),t=!0},p(i,a){const o={};a[0]&8192&&(o.fullscreen=i[13]),e.$set(o)},i(i){t||(p(e.$$.fragment,i),t=!0)},o(i){N(e.$$.fragment,i),t=!1},d(i){J(e,i)}}}function It(n){let e;return{c(){e=xe(n[4])},l(t){e=Be(t,n[4])},m(t,i){q(t,e,i)},p(t,i){i[0]&16&&Re(e,t[4])},d(t){t&&M(e)}}}function zt(n){let e,t;return e=new kt({props:{unpadded_box:!0,$$slots:{default:[Pt]},$$scope:{ctx:n}}}),{c(){Y(e.$$.fragment)},l(i){U(e.$$.fragment,i)},m(i,a){G(e,i,a),t=!0},p(i,a){const o={};a[2]&32&&(o.$$scope={dirty:a,ctx:i}),e.$set(o)},i(i){t||(p(e.$$.fragment,i),t=!0)},o(i){N(e.$$.fragment,i),t=!1},d(i){J(e,i)}}}function Ot(n){let e,t,i,a=n[1]&&He(n);return{c(){e=Ye("div"),t=ue(),a&&a.c(),i=_e(),this.h()},l(o){e=Ue(o,"DIV",{class:!0}),Ge(e).forEach(M),t=ce(o),a&&a.l(o),i=_e(),this.h()},h(){Je(e,"class","svelte-19qacdz")},m(o,f){q(o,e,f),n[50](e),q(o,t,f),a&&a.m(o,f),q(o,i,f)},p(o,f){o[1]?a?a.p(o,f):(a=He(o),a.c(),a.m(i.parentNode,i)):a&&(a.d(1),a=null)},i:Me,o:Me,d(o){o&&(M(e),M(t),M(i)),n[50](null),a&&a.d(o)}}}function Pt(n){let e,t;return e=new vt({}),{c(){Y(e.$$.fragment)},l(i){U(e.$$.fragment,i)},m(i,a){G(e,i,a),t=!0},i(i){t||(p(e.$$.fragment,i),t=!0)},o(i){N(e.$$.fragment,i),t=!1},d(i){J(e,i)}}}function He(n){let e,t;return{c(){e=Ye("p"),t=xe(n[1]),this.h()},l(i){e=Ue(i,"P",{class:!0});var a=Ge(e);t=Be(a,n[1]),a.forEach(M),this.h()},h(){Je(e,"class","caption svelte-19qacdz")},m(i,a){q(i,e,a),gt(e,t)},p(i,a){a[0]&2&&Re(t,i[1])},d(i){i&&M(e)}}}function St(n){let e,t,i,a,o,f,c,E,_=n[11]&&De(n),m=n[2]&&Le(n);i=new ht({props:{show_label:n[8],info:void 0,$$slots:{default:[It]},$$scope:{ctx:n}}});const O=[Ot,zt],W=[];function s(r,u){return r[0]&&r[15]?0:1}return o=s(n),f=W[o]=O[o](n),{c(){_&&_.c(),e=ue(),m&&m.c(),t=ue(),Y(i.$$.fragment),a=ue(),f.c(),c=_e()},l(r){_&&_.l(r),e=ce(r),m&&m.l(r),t=ce(r),U(i.$$.fragment,r),a=ce(r),f.l(r),c=_e()},m(r,u){_&&_.m(r,u),q(r,e,u),m&&m.m(r,u),q(r,t,u),G(i,r,u),q(r,a,u),W[o].m(r,u),q(r,c,u),E=!0},p(r,u){r[11]?_?(_.p(r,u),u[0]&2048&&p(_,1)):(_=De(r),_.c(),p(_,1),_.m(e.parentNode,e)):_&&(Ie(),N(_,1,1,()=>{_=null}),ze()),r[2]?m?(m.p(r,u),u[0]&4&&p(m,1)):(m=Le(r),m.c(),p(m,1),m.m(t.parentNode,t)):m&&(Ie(),N(m,1,1,()=>{m=null}),ze());const g={};u[0]&256&&(g.show_label=r[8]),u[0]&16|u[2]&32&&(g.$$scope={dirty:u,ctx:r}),i.$set(g);let R=o;o=s(r),o===R?W[o].p(r,u):(Ie(),N(W[R],1,1,()=>{W[R]=null}),ze(),f=W[o],f?f.p(r,u):(f=W[o]=O[o](r),f.c()),p(f,1),f.m(c.parentNode,c))},i(r){E||(p(_),p(m),p(i.$$.fragment,r),p(f),E=!0)},o(r){N(_),N(m),N(i.$$.fragment,r),N(f),E=!1},d(r){r&&(M(e),M(t),M(a),M(c)),_&&_.d(r),m&&m.d(r),J(i,r),W[o].d(r)}}}function pt(n){let e,t,i;function a(f){n[51](f)}let o={visible:n[7],elem_id:n[5],elem_classes:n[6],scale:n[9],min_width:n[10],allow_overflow:!1,padding:!0,height:n[12],$$slots:{default:[St]},$$scope:{ctx:n}};return n[13]!==void 0&&(o.fullscreen=n[13]),e=new bt({props:o}),qe.push(()=>st(e,"fullscreen",a)),{c(){Y(e.$$.fragment)},l(f){U(e.$$.fragment,f)},m(f,c){G(e,f,c),i=!0},p(f,c){const E={};c[0]&128&&(E.visible=f[7]),c[0]&32&&(E.elem_id=f[5]),c[0]&64&&(E.elem_classes=f[6]),c[0]&512&&(E.scale=f[9]),c[0]&1024&&(E.min_width=f[10]),c[0]&4096&&(E.height=f[12]),c[0]&26911|c[2]&32&&(E.$$scope={dirty:c,ctx:f}),!t&&c[0]&8192&&(t=!0,E.fullscreen=f[13],ut(()=>t=!1)),e.$set(E)},i(f){i||(p(e.$$.fragment,f),i=!0)},o(f){N(e.$$.fragment,f),i=!1},d(f){J(e,f)}}}function At(n,e,t){let i,a,o,f,c,E,_,m,O,W,{value:s}=e,{x:r}=e,{y:u}=e,{color:g=null}=e,{title:R=null}=e,{x_title:ie=null}=e,{y_title:ne=null}=e,{color_title:me=null}=e,{x_bin:D=null}=e,{y_aggregate:Z=void 0}=e,{color_map:$=null}=e,{x_lim:C=null}=e,{y_lim:X=null}=e,{x_label_angle:oe=null}=e,{y_label_angle:re=null}=e,{x_axis_labels_visible:ae=!0}=e,{caption:Oe=null}=e,{sort:de=null}=e,{tooltip:x="axis"}=e,{show_fullscreen_button:Pe=!1}=e,K=!1;function Xe(l){if(l==="x")return"ascending";if(l==="-x")return"descending";if(l==="y")return{field:u,order:"ascending"};if(l==="-y")return{field:u,order:"descending"};if(l===null)return null;if(Array.isArray(l))return l}let{_selectable:fe=!1}=e,ee,{gradio:te}=e,B,ge=!1;const Ke={s:1,m:60,h:60*60,d:24*60*60};let le,Q;function Se(l,b,j,z,I,h){if(l.length<1e3||D!==null||(s==null?void 0:s.mark)!=="line"||(s==null?void 0:s.datatypes[r])==="nominal")return l;const w=250;let d={};if((I===void 0||h===void 0)&&l.forEach(V=>{let P=V[b];(I===void 0||P<I)&&(I=P),(h===void 0||P>h)&&(h=P)}),I===void 0||h===void 0)return l;const v=(h-I)/w;l.forEach((V,P)=>{const H=V[b],S=V[j],y=z!==null?V[z]:"any",k=Math.floor((H-I)/v);d[y]===void 0&&(d[y]=[]),d[y][k]=d[y][k]||[null,Number.POSITIVE_INFINITY,null,Number.NEGATIVE_INFINITY],S<d[y][k][1]&&(d[y][k][0]=P,d[y][k][1]=S),S>d[y][k][3]&&(d[y][k][2]=P,d[y][k][3]=S)});const L=[];return Object.values(d).forEach(V=>{V.forEach(([P,H,S,y])=>{let k=[];P!==null&&S!==null?k=[Math.min(P,S),Math.max(P,S)]:P!==null?k=[P]:S!==null&&(k=[S]),k.forEach(nt=>{L.push(l[nt])})})}),L}function Qe(l,b,j){let z=l.columns.indexOf(r),I=l.columns.indexOf(u),h=g?l.columns.indexOf(g):null,w=l.data;if(b!==void 0&&j!==void 0){const d=l.datatypes[r]==="temporal"?1e3:1,F=b*d,v=j*d;let L={},V={};const P=w.filter((H,S)=>{const y=H[z],k=h!==null?H[h]:"any";return y<F&&(L[k]===void 0||y>L[k][1])&&(L[k]=[S,y]),y>v&&(V[k]===void 0||y<V[k][1])&&(V[k]=[S,y]),y>=F&&y<=v});w=[...Object.values(L).map(([H,S])=>w[H]),...Se(P,z,I,h,F,v),...Object.values(V).map(([H,S])=>w[H])]}else w=Se(w,z,I,h,void 0,void 0);return x=="all"||Array.isArray(x)?w.map(d=>{const F={};return l.columns.forEach((v,L)=>{F[v]=d[L]}),F}):w.map(d=>{const F={[r]:d[z],[u]:d[I]};return g&&h!==null&&(F[g]=d[h]),F})}let be=s;const Ze=typeof window<"u";let A,T,he=!1,pe,ye,se,ke;async function ve(){if(ge){we=!0;return}if(T&&T.finalize(),!s||!A)return;pe=A.offsetWidth,ye=A.offsetHeight;const l=$e();l&&(se=new ResizeObserver(b=>{!b[0].target||!(b[0].target instanceof HTMLElement)||(pe===0&&A.offsetWidth!==0&&s.datatypes[r]==="nominal"?ve():T.signal("width",b[0].target.offsetWidth).run(),ye!==b[0].target.offsetHeight&&K&&(T.signal("height",b[0].target.offsetHeight).run(),ye=b[0].target.offsetHeight))}),ke||(ke=(await ot(()=>import("./vega-embed.module.BU0P0uZZ.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]),import.meta.url)).default),ke(A,l,{actions:!1}).then(function(b){t(39,T=b.view),se.observe(A);var j,z=0;T.addEventListener("dblclick",()=>{te.dispatch("double_click")}),A.addEventListener("mousedown",function(I){I.detail>1&&I.preventDefault()},!1),fe&&T.addSignalListener("brush",function(I,h){if(Date.now()-z<1e3||(ge=!0,Object.keys(h).length===0))return;clearTimeout(j);let w=h[Object.keys(h)[0]];_&&(w=[w[0]/1e3,w[1]/1e3]),j=setTimeout(function(){ge=!1,z=Date.now(),te.dispatch("select",{value:w,index:w,selected:!0}),we&&(we=!1,ve())},250)})}))}let we=!1;ct(()=>(t(40,he=!0),()=>{t(40,he=!1),T&&T.finalize(),se&&se.disconnect()}));function $e(){if(!s||!O)return null;let l=O.getPropertyValue("--color-accent"),b=O.getPropertyValue("--body-text-color"),j=O.getPropertyValue("--border-color-primary"),z=O.fontFamily,I=O.getPropertyValue("--block-title-text-weight");const h=F=>F.endsWith("px")?parseFloat(F.slice(0,-2)):12;let w=h(O.getPropertyValue("--text-md")),d=h(O.getPropertyValue("--text-sm"));return{$schema:"https://vega.github.io/schema/vega-lite/v5.17.0.json",background:"transparent",config:{autosize:{type:"fit",contains:"padding"},axis:{labelFont:z,labelColor:b,titleFont:z,titleColor:b,titlePadding:8,tickColor:j,labelFontSize:d,gridColor:j,titleFontWeight:"normal",titleFontSize:d,labelFontWeight:"normal",domain:!1,labelAngle:0},legend:{labelColor:b,labelFont:z,titleColor:b,titleFont:z,titleFontWeight:"normal",titleFontSize:d,labelFontWeight:"normal",offset:2},title:{color:b,font:z,fontSize:w,fontWeight:I,anchor:"middle"},view:{stroke:j},mark:{stroke:s.mark!=="bar"?l:void 0,fill:s.mark==="bar"?l:void 0,cursor:"crosshair"}},data:{name:"data"},datasets:{data:ee},layer:["plot",...s.mark==="line"?["hover"]:[]].map(F=>({encoding:{size:s.mark==="line"?F=="plot"?{condition:{empty:!1,param:"hoverPlot",value:3},value:2}:{condition:{empty:!1,param:"hover",value:100},value:0}:void 0,opacity:F==="plot"?void 0:{condition:{empty:!1,param:"hover",value:1},value:0},x:{axis:{...oe!==null&&{labelAngle:oe},labels:ae,ticks:ae},field:r,title:ie||r,type:s.datatypes[r],scale:m?{domain:m}:void 0,bin:B?{step:B}:void 0,sort:E},y:{axis:re?{labelAngle:re}:{},field:u,title:ne||u,type:s.datatypes[u],scale:{zero:!1,domainMin:f??void 0,domainMax:c??void 0},aggregate:Q?le:void 0},color:g?{field:g,legend:{orient:"bottom",title:me},scale:s.datatypes[g]==="nominal"?{domain:i,range:$?i.map(v=>$[v]):void 0}:{range:[100,200,300,400,500,600,700,800,900].map(v=>O.getPropertyValue("--primary-"+v)),interpolate:"hsl"},type:s.datatypes[g]}:void 0,tooltip:x=="none"?void 0:[{field:u,type:s.datatypes[u],aggregate:Q?le:void 0,title:ne||u},{field:r,type:s.datatypes[r],title:ie||r,format:_?"%Y-%m-%d %H:%M:%S":void 0,bin:B?{step:B}:void 0},...g?[{field:g,type:s.datatypes[g]}]:[],...x==="axis"?[]:s==null?void 0:s.columns.filter(v=>v!==r&&v!==u&&v!==g&&(x==="all"||x.includes(v))).map(v=>({field:v,type:s.datatypes[v]}))]},strokeDash:{},mark:{clip:!0,type:F==="hover"?"point":s.mark},name:F})),params:[...s.mark==="line"?[{name:"hoverPlot",select:{clear:"mouseout",fields:g?[g]:[],nearest:!0,on:"mouseover",type:"point"},views:["hover"]},{name:"hover",select:{clear:"mouseout",nearest:!0,on:"mouseover",type:"point"},views:["hover"]}]:[],...fe?[{name:"brush",select:{encodings:["x"],mark:{fill:"gray",fillOpacity:.3,stroke:"none"},type:"interval"},views:["plot"]}]:[]],width:A.offsetWidth,height:Ee||K?"container":void 0,title:R||void 0}}let{label:Ae="Textbox"}=e,{elem_id:Ne=""}=e,{elem_classes:Te=[]}=e,{visible:Ve=!0}=e,{show_label:We}=e,{scale:Ce=null}=e,{min_width:je=void 0}=e,{loading_status:Fe=void 0}=e,{height:Ee=void 0}=e;const et=()=>te.dispatch("clear_status",Fe),tt=({detail:l})=>{t(13,K=l)};function lt(l){qe[l?"unshift":"push"](()=>{A=l,t(14,A)})}function it(l){K=l,t(13,K)}return n.$$set=l=>{"value"in l&&t(0,s=l.value),"x"in l&&t(18,r=l.x),"y"in l&&t(19,u=l.y),"color"in l&&t(20,g=l.color),"title"in l&&t(21,R=l.title),"x_title"in l&&t(22,ie=l.x_title),"y_title"in l&&t(23,ne=l.y_title),"color_title"in l&&t(24,me=l.color_title),"x_bin"in l&&t(25,D=l.x_bin),"y_aggregate"in l&&t(26,Z=l.y_aggregate),"color_map"in l&&t(27,$=l.color_map),"x_lim"in l&&t(16,C=l.x_lim),"y_lim"in l&&t(17,X=l.y_lim),"x_label_angle"in l&&t(28,oe=l.x_label_angle),"y_label_angle"in l&&t(29,re=l.y_label_angle),"x_axis_labels_visible"in l&&t(30,ae=l.x_axis_labels_visible),"caption"in l&&t(1,Oe=l.caption),"sort"in l&&t(31,de=l.sort),"tooltip"in l&&t(32,x=l.tooltip),"show_fullscreen_button"in l&&t(2,Pe=l.show_fullscreen_button),"_selectable"in l&&t(33,fe=l._selectable),"gradio"in l&&t(3,te=l.gradio),"label"in l&&t(4,Ae=l.label),"elem_id"in l&&t(5,Ne=l.elem_id),"elem_classes"in l&&t(6,Te=l.elem_classes),"visible"in l&&t(7,Ve=l.visible),"show_label"in l&&t(8,We=l.show_label),"scale"in l&&t(9,Ce=l.scale),"min_width"in l&&t(10,je=l.min_width),"loading_status"in l&&t(11,Fe=l.loading_status),"height"in l&&t(12,Ee=l.height)},n.$$.update=()=>{n.$$.dirty[0]&65536&&t(16,C=C||null),n.$$.dirty[0]&65536&&t(46,[a,o]=C===null?[void 0,void 0]:C,a,(t(45,o),t(16,C))),n.$$.dirty[0]&1|n.$$.dirty[1]&49152&&t(34,ee=s?Qe(s,a,o):[]),n.$$.dirty[0]&1048577|n.$$.dirty[1]&8&&(i=g&&s&&s.datatypes[g]==="nominal"?Array.from(new Set(ee.map(l=>l[g]))):[]),n.$$.dirty[0]&131072&&t(17,X=X||null),n.$$.dirty[0]&131072&&t(44,[f,c]=X||[void 0,void 0],f,(t(43,c),t(17,X))),n.$$.dirty[1]&1&&(E=Xe(de)),n.$$.dirty[0]&262145&&t(41,_=s&&s.datatypes[r]==="temporal"),n.$$.dirty[0]&65536|n.$$.dirty[1]&1024&&(m=C&&_?[C[0]*1e3,C[1]*1e3]:C),n.$$.dirty[0]&33554432&&t(35,B=D?typeof D=="string"?1e3*parseInt(D.substring(0,D.length-1))*Ke[D[D.length-1]]:D:void 0),n.$$.dirty[0]&67371009|n.$$.dirty[1]&80&&s&&(s.mark==="point"?(t(37,Q=B!==void 0),t(36,le=Z||Q?"sum":void 0)):(t(37,Q=B!==void 0||s.datatypes[r]==="nominal"),t(36,le=Z||"sum"))),n.$$.dirty[0]&1|n.$$.dirty[1]&392&&be!==s&&T&&(t(38,be=s),T.data("data",ee).runAsync()),n.$$.dirty[0]&16384&&t(42,O=A?window.getComputedStyle(A):null),n.$$.dirty[0]&134217728&&t(47,W=JSON.stringify($)),n.$$.dirty[0]&66871298|n.$$.dirty[1]&129569&&O&&requestAnimationFrame(ve)},[s,Oe,Pe,te,Ae,Ne,Te,Ve,We,Ce,je,Fe,Ee,K,A,Ze,C,X,r,u,g,R,ie,ne,me,D,Z,$,oe,re,ae,de,x,fe,ee,B,le,Q,be,T,he,_,O,c,f,o,a,W,et,tt,lt,it]}class Lt extends rt{constructor(e){super(),at(this,e,At,pt,ft,{value:0,x:18,y:19,color:20,title:21,x_title:22,y_title:23,color_title:24,x_bin:25,y_aggregate:26,color_map:27,x_lim:16,y_lim:17,x_label_angle:28,y_label_angle:29,x_axis_labels_visible:30,caption:1,sort:31,tooltip:32,show_fullscreen_button:2,_selectable:33,gradio:3,label:4,elem_id:5,elem_classes:6,visible:7,show_label:8,scale:9,min_width:10,loading_status:11,height:12},null,[-1,-1,-1])}}export{Lt as default};
//# sourceMappingURL=Index.HDGqOq9a.js.map
