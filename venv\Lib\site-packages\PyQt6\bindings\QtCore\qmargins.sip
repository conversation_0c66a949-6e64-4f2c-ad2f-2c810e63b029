// qmargins.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qmargins.h>
%End

class QMargins
{
%TypeHeaderCode
#include <qmargins.h>
%End

public:
    QMargins();
    QMargins(int aleft, int atop, int aright, int abottom);
    bool isNull() const;
    int left() const;
    int top() const;
    int right() const;
    int bottom() const;
    void setLeft(int aleft);
    void setTop(int atop);
    void setRight(int aright);
    void setBottom(int abottom);
    QMargins &operator+=(const QMargins &margins);
    QMargins &operator-=(const QMargins &margins);
    QMargins &operator*=(int factor /Constrained/);
    QMargins &operator/=(int divisor /Constrained/);
    QMargins &operator*=(qreal factor);
    QMargins &operator/=(qreal divisor);
    QMargins &operator+=(int margin);
    QMargins &operator-=(int margin);
%If (Qt_6_4_0 -)
    QMarginsF toMarginsF() const;
%End
};

%If (Qt_6_8_0 -)
bool operator==(const QMarginsF &lhs, const QMargins &rhs);
%End
%If (Qt_6_8_0 -)
bool operator==(const QMargins &lhs, const QMarginsF &rhs);
%End
bool operator==(const QMargins &m1, const QMargins &m2);
%If (Qt_6_8_0 -)
bool operator!=(const QMarginsF &lhs, const QMargins &rhs);
%End
%If (Qt_6_8_0 -)
bool operator!=(const QMargins &lhs, const QMarginsF &rhs);
%End
bool operator!=(const QMargins &m1, const QMargins &m2);
QDataStream &operator<<(QDataStream &, const QMargins & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QMargins & /Constrained/) /ReleaseGIL/;
QMargins operator+(const QMargins &m1, const QMargins &m2);
QMargins operator-(const QMargins &m1, const QMargins &m2);
QMargins operator*(const QMargins &margins, int factor /Constrained/);
QMargins operator*(const QMargins &margins, qreal factor);
QMargins operator/(const QMargins &margins, int divisor /Constrained/);
QMargins operator/(const QMargins &margins, qreal divisor);
QMargins operator+(const QMargins &lhs, int rhs);
QMargins operator+(int lhs, const QMargins &rhs);
QMargins operator-(const QMargins &lhs, int rhs);
QMargins operator+(const QMargins &margins);
QMargins operator-(const QMargins &margins);
QMargins operator|(const QMargins &m1, const QMargins &m2);

class QMarginsF
{
%TypeHeaderCode
#include <qmargins.h>
%End

public:
    QMarginsF();
    QMarginsF(qreal aleft, qreal atop, qreal aright, qreal abottom);
    QMarginsF(const QMargins &margins);
    bool isNull() const;
    qreal left() const;
    qreal top() const;
    qreal right() const;
    qreal bottom() const;
    void setLeft(qreal aleft);
    void setTop(qreal atop);
    void setRight(qreal aright);
    void setBottom(qreal abottom);
    QMarginsF &operator+=(const QMarginsF &margins);
    QMarginsF &operator-=(const QMarginsF &margins);
    QMarginsF &operator+=(qreal addend);
    QMarginsF &operator-=(qreal subtrahend);
    QMarginsF &operator*=(qreal factor);
    QMarginsF &operator/=(qreal divisor);
    QMargins toMargins() const;
};

QDataStream &operator<<(QDataStream &, const QMarginsF & /Constrained/);
QDataStream &operator>>(QDataStream &, QMarginsF & /Constrained/);
bool operator==(const QMarginsF &lhs, const QMarginsF &rhs);
bool operator!=(const QMarginsF &lhs, const QMarginsF &rhs);
QMarginsF operator+(const QMarginsF &lhs, const QMarginsF &rhs);
QMarginsF operator-(const QMarginsF &lhs, const QMarginsF &rhs);
QMarginsF operator+(const QMarginsF &lhs, qreal rhs);
QMarginsF operator+(qreal lhs, const QMarginsF &rhs);
QMarginsF operator-(const QMarginsF &lhs, qreal rhs);
QMarginsF operator*(const QMarginsF &lhs, qreal rhs);
QMarginsF operator*(qreal lhs, const QMarginsF &rhs);
QMarginsF operator/(const QMarginsF &lhs, qreal divisor);
QMarginsF operator+(const QMarginsF &margins);
QMarginsF operator-(const QMarginsF &margins);
QMarginsF operator|(const QMarginsF &m1, const QMarginsF &m2);
%If (Qt_6_8_0 -)
bool qFuzzyCompare(const QMarginsF &lhs, const QMarginsF &rhs);
%End
%If (Qt_6_8_0 -)
bool qFuzzyIsNull(const QMarginsF &m);
%End
