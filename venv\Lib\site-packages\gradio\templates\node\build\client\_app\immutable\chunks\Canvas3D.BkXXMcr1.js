const __vite__fileDeps=["./index.BoI39RQH.js","./preload-helper.D6kgxu3v.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as p}from"./preload-helper.D6kgxu3v.js";import{SvelteComponent as x,init as z,safe_not_equal as E,element as L,claim_element as S,children as k,detach as R,insert_hydration as D,noop as C,onMount as j,binding_callbacks as B}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{x as N}from"./2.B2AoQPnG.js";function U(r){let i;return{c(){i=L("canvas")},l(t){i=S(t,"CANVAS",{}),k(i).forEach(R)},m(t,a){D(t,i,a),r[13](i)},p:C,i:C,o:C,d(t){t&&R(i),r[13](null)}}}function W(r,i,t){let a,v,{value:d}=i,{display_mode:c}=i,{clear_color:m}=i,{camera_position:f}=i,{zoom_speed:h}=i,{pan_speed:b}=i,{resolved_url:s=void 0}=i,y,_,o,u,g=!1;j(()=>((async()=>{v=await p(()=>import("./index.BoI39RQH.js").then(n=>n.bY),__vite__mapDeps([0,1]),import.meta.url),v.createViewerForCanvas(_,{clearColor:m,useRightHandedSystem:!0,animationAutoPlay:!0,cameraAutoOrbit:{enabled:!1},onInitialized:n=>{u=n}}).then(n=>{o=n,t(11,g=!0)})})(),()=>{o==null||o.dispose()}));function w(e,n){u.scene.forcePointsCloud=e,u.scene.forceWireframe=n}function V(e){o&&(e?o.loadModel(e,{pluginOptions:{obj:{importVertexColors:!0}}}).then(()=>{c==="point_cloud"?w(!0,!1):c==="wireframe"?w(!1,!0):A(f,h,b)}):o.resetModel())}function A(e,n,O){const l=u.camera;e[0]!==null&&(l.alpha=e[0]*Math.PI/180),e[1]!==null&&(l.beta=e[1]*Math.PI/180),e[2]!==null&&(l.radius=e[2]),l.lowerRadiusLimit=.1;const P=()=>{l.wheelPrecision=250/(l.radius*n),l.panningSensibility=1e4*O/l.radius};P(),l.onAfterCheckInputsObservable.add(P)}function I(){u&&o.resetCamera()}function M(e){B[e?"unshift":"push"](()=>{_=e,t(0,_)})}return r.$$set=e=>{"value"in e&&t(2,d=e.value),"display_mode"in e&&t(3,c=e.display_mode),"clear_color"in e&&t(4,m=e.clear_color),"camera_position"in e&&t(5,f=e.camera_position),"zoom_speed"in e&&t(6,h=e.zoom_speed),"pan_speed"in e&&t(7,b=e.pan_speed),"resolved_url"in e&&t(1,s=e.resolved_url)},r.$$.update=()=>{if(r.$$.dirty&4&&t(12,a=d.url),r.$$.dirty&5120&&(t(1,s=a),a)){t(10,y=a);const e=a;N(a).then(n=>{y===e?t(1,s=n??void 0):n&&URL.revokeObjectURL(n)})}r.$$.dirty&2050&&g&&V(s)},[_,s,d,c,m,f,h,b,A,I,y,g,a,M]}class T extends x{constructor(i){super(),z(this,i,W,U,E,{value:2,display_mode:3,clear_color:4,camera_position:5,zoom_speed:6,pan_speed:7,resolved_url:1,update_camera:8,reset_camera_position:9})}get update_camera(){return this.$$.ctx[8]}get reset_camera_position(){return this.$$.ctx[9]}}export{T as default};
//# sourceMappingURL=Canvas3D.BkXXMcr1.js.map
