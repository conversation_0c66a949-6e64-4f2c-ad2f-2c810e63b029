{"version": 3, "file": "flowGraphCounterBlock-T1Bzg_-q.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphCounterBlock.js"], "sourcesContent": ["import { RichTypeNumber } from \"../../../flowGraphRichTypes.js\";\nimport { FlowGraphExecutionBlockWithOutSignal } from \"../../../flowGraphExecutionBlockWithOutSignal.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\n/**\n * A block that counts the number of times it has been called.\n * Afterwards it activates its out signal.\n */\nexport class FlowGraphCallCounterBlock extends FlowGraphExecutionBlockWithOutSignal {\n    constructor(config) {\n        super(config);\n        this.count = this.registerDataOutput(\"count\", RichTypeNumber);\n        this.reset = this._registerSignalInput(\"reset\");\n    }\n    _execute(context, callingSignal) {\n        if (callingSignal === this.reset) {\n            context._setExecutionVariable(this, \"count\", 0);\n            this.count.setValue(0, context);\n            return;\n        }\n        const countValue = context._getExecutionVariable(this, \"count\", 0) + 1;\n        context._setExecutionVariable(this, \"count\", countValue);\n        this.count.setValue(countValue, context);\n        this.out._activateSignal(context);\n    }\n    /**\n     * @returns class name of the block.\n     */\n    getClassName() {\n        return \"FlowGraphCallCounterBlock\" /* FlowGraphBlockNames.CallCounter */;\n    }\n}\nRegisterClass(\"FlowGraphCallCounterBlock\" /* FlowGraphBlockNames.CallCounter */, FlowGraphCallCounterBlock);\n//# sourceMappingURL=flowGraphCounterBlock.js.map"], "names": ["FlowGraphCallCounterBlock", "FlowGraphExecutionBlockWithOutSignal", "config", "RichTypeNumber", "context", "callingSignal", "countValue", "RegisterClass"], "mappings": "gPAOO,MAAMA,UAAkCC,CAAqC,CAChF,YAAYC,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,MAAQ,KAAK,mBAAmB,QAASC,CAAc,EAC5D,KAAK,MAAQ,KAAK,qBAAqB,OAAO,CACjD,CACD,SAASC,EAASC,EAAe,CAC7B,GAAIA,IAAkB,KAAK,MAAO,CAC9BD,EAAQ,sBAAsB,KAAM,QAAS,CAAC,EAC9C,KAAK,MAAM,SAAS,EAAGA,CAAO,EAC9B,MACH,CACD,MAAME,EAAaF,EAAQ,sBAAsB,KAAM,QAAS,CAAC,EAAI,EACrEA,EAAQ,sBAAsB,KAAM,QAASE,CAAU,EACvD,KAAK,MAAM,SAASA,EAAYF,CAAO,EACvC,KAAK,IAAI,gBAAgBA,CAAO,CACnC,CAID,cAAe,CACX,MAAO,2BACV,CACL,CACAG,EAAc,4BAAmEP,CAAyB", "x_google_ignoreList": [0]}