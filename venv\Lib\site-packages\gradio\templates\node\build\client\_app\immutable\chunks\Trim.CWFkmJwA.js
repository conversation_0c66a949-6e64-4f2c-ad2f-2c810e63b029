import{SvelteComponent as f,init as y,safe_not_equal as v,svg_element as s,claim_svg_element as c,children as n,detach as l,attr as e,insert_hydration as p,append_hydration as x,noop as d}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function k(w){let t,r,i;return{c(){t=s("svg"),r=s("rect"),i=s("rect"),this.h()},l(o){t=c(o,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0});var u=n(t);r=c(u,"rect",{x:!0,y:!0,width:!0,height:!0}),n(r).forEach(l),i=c(u,"rect",{x:!0,y:!0,width:!0,height:!0}),n(i).forEach(l),u.forEach(l),this.h()},h(){e(r,"x","6"),e(r,"y","4"),e(r,"width","4"),e(r,"height","16"),e(i,"x","14"),e(i,"y","4"),e(i,"width","4"),e(i,"height","16"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 24 24"),e(t,"fill","currentColor"),e(t,"stroke","currentColor"),e(t,"stroke-width","1.5"),e(t,"stroke-linecap","round"),e(t,"stroke-linejoin","round")},m(o,u){p(o,t,u),x(t,r),x(t,i)},p:d,i:d,o:d,d(o){o&&l(t)}}}class j extends f{constructor(t){super(),y(this,t,null,k,v,{})}}function m(w){let t,r,i,o,u,h;return{c(){t=s("svg"),r=s("circle"),i=s("circle"),o=s("line"),u=s("line"),h=s("line"),this.h()},l(g){t=c(g,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0,class:!0});var a=n(t);r=c(a,"circle",{cx:!0,cy:!0,r:!0}),n(r).forEach(l),i=c(a,"circle",{cx:!0,cy:!0,r:!0}),n(i).forEach(l),o=c(a,"line",{x1:!0,y1:!0,x2:!0,y2:!0}),n(o).forEach(l),u=c(a,"line",{x1:!0,y1:!0,x2:!0,y2:!0}),n(u).forEach(l),h=c(a,"line",{x1:!0,y1:!0,x2:!0,y2:!0}),n(h).forEach(l),a.forEach(l),this.h()},h(){e(r,"cx","6"),e(r,"cy","6"),e(r,"r","3"),e(i,"cx","6"),e(i,"cy","18"),e(i,"r","3"),e(o,"x1","20"),e(o,"y1","4"),e(o,"x2","8.12"),e(o,"y2","15.88"),e(u,"x1","14.47"),e(u,"y1","14.48"),e(u,"x2","20"),e(u,"y2","20"),e(h,"x1","8.12"),e(h,"y1","8.12"),e(h,"x2","12"),e(h,"y2","12"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 24 24"),e(t,"fill","none"),e(t,"stroke","currentColor"),e(t,"stroke-width","2"),e(t,"stroke-linecap","round"),e(t,"stroke-linejoin","round"),e(t,"class","feather feather-scissors")},m(g,a){p(g,t,a),x(t,r),x(t,i),x(t,o),x(t,u),x(t,h)},p:d,i:d,o:d,d(g){g&&l(t)}}}class B extends f{constructor(t){super(),y(this,t,null,m,v,{})}}export{j as P,B as T};
//# sourceMappingURL=Trim.CWFkmJwA.js.map
