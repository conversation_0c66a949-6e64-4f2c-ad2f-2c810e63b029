import{b as g}from"./KHR_interactivity-DTxiAnOo.js";import{R as o}from"./declarationMapper-BZjsjg7g.js";import{h as r,R as i}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class h extends g{constructor(e){if(super(e),this.message=this.registerDataInput("message",o),this.logType=this.registerDataInput("logType",o,"log"),e?.messageTemplate){const t=this._getTemplateMatches(e.messageTemplate);for(const s of t)this.registerDataInput(s,o)}}_execute(e){const t=this.logType.getValue(e),s=this._getMessageValue(e);t==="warn"?r.Warn(s):t==="error"?r.Error(s):r.Log(s),this.out._activateSignal(e)}getClassName(){return"FlowGraphConsoleLogBlock"}_getMessageValue(e){if(this.config?.messageTemplate){let t=this.config.messageTemplate;const s=this._getTemplateMatches(t);for(const a of s){const l=this.getDataInput(a)?.getValue(e);l!==void 0&&(t=t.replace(new RegExp(`\\{${a}\\}`,"g"),l.toString()))}return t}else return this.message.getValue(e)}_getTemplateMatches(e){const t=/\{([^}]+)\}/g,s=[];let a;for(;(a=t.exec(e))!==null;)s.push(a[1]);return s}}i("FlowGraphConsoleLogBlock",h);export{h as FlowGraphConsoleLogBlock};
//# sourceMappingURL=flowGraphConsoleLogBlock-NucOGDCN.js.map
