import{R as N,ay as I,V,az as S,M as D,C as q,Q as w,aS as L,h as f}from"./index-Dpxo-yl_.js";class p{constructor(e){this.value=this._toInt(e)}_toInt(e){return e|0}add(e){return new p(this.value+e.value)}subtract(e){return new p(this.value-e.value)}multiply(e){return new p(Math.imul(this.value,e.value))}divide(e){return new p(this.value/e.value)}getClassName(){return p.ClassName}equals(e){return this.value===e.value}static FromValue(e){return new p(e)}toString(){return this.value.toString()}}p.ClassName="FlowGraphInteger";N("FlowGraphInteger",p);class m{constructor(e=[1,0,0,1]){this._m=e}get m(){return this._m}transformVector(e){return this.transformVectorToRef(e,new I)}transformVectorToRef(e,n){return n.x=e.x*this._m[0]+e.y*this._m[1],n.y=e.x*this._m[2]+e.y*this._m[3],n}asArray(){return this.toArray()}toArray(e=[]){for(let n=0;n<4;n++)e[n]=this._m[n];return e}fromArray(e){for(let n=0;n<4;n++)this._m[n]=e[n];return this}multiplyToRef(e,n){const o=e._m,t=this._m,r=n._m;return r[0]=o[0]*t[0]+o[1]*t[2],r[1]=o[0]*t[1]+o[1]*t[3],r[2]=o[2]*t[0]+o[3]*t[2],r[3]=o[2]*t[1]+o[3]*t[3],n}multiply(e){return this.multiplyToRef(e,new m)}divideToRef(e,n){const o=this._m,t=e._m,r=n._m;return r[0]=o[0]/t[0],r[1]=o[1]/t[1],r[2]=o[2]/t[2],r[3]=o[3]/t[3],n}divide(e){return this.divideToRef(e,new m)}addToRef(e,n){const o=this._m,t=e.m,r=n.m;return r[0]=o[0]+t[0],r[1]=o[1]+t[1],r[2]=o[2]+t[2],r[3]=o[3]+t[3],n}add(e){return this.addToRef(e,new m)}subtractToRef(e,n){const o=this._m,t=e.m,r=n.m;return r[0]=o[0]-t[0],r[1]=o[1]-t[1],r[2]=o[2]-t[2],r[3]=o[3]-t[3],n}subtract(e){return this.subtractToRef(e,new m)}transpose(){const e=this._m;return new m([e[0],e[2],e[1],e[3]])}determinant(){const e=this._m;return e[0]*e[3]-e[1]*e[2]}inverse(){const e=this.determinant();if(e===0)throw new Error("Matrix is not invertible");const n=this._m,o=1/e;return new m([n[3]*o,-n[1]*o,-n[2]*o,n[0]*o])}equals(e,n=0){const o=this._m,t=e.m;return n===0?o[0]===t[0]&&o[1]===t[1]&&o[2]===t[2]&&o[3]===t[3]:Math.abs(o[0]-t[0])<n&&Math.abs(o[1]-t[1])<n&&Math.abs(o[2]-t[2])<n&&Math.abs(o[3]-t[3])<n}getClassName(){return"FlowGraphMatrix2D"}toString(){return`FlowGraphMatrix2D(${this._m.join(", ")})`}}class h{constructor(e=[1,0,0,0,1,0,0,0,1]){this._m=e}get m(){return this._m}transformVector(e){return this.transformVectorToRef(e,new V)}transformVectorToRef(e,n){const o=this._m;return n.x=e.x*o[0]+e.y*o[1]+e.z*o[2],n.y=e.x*o[3]+e.y*o[4]+e.z*o[5],n.z=e.x*o[6]+e.y*o[7]+e.z*o[8],n}multiplyToRef(e,n){const o=e._m,t=this._m,r=n.m;return r[0]=o[0]*t[0]+o[1]*t[3]+o[2]*t[6],r[1]=o[0]*t[1]+o[1]*t[4]+o[2]*t[7],r[2]=o[0]*t[2]+o[1]*t[5]+o[2]*t[8],r[3]=o[3]*t[0]+o[4]*t[3]+o[5]*t[6],r[4]=o[3]*t[1]+o[4]*t[4]+o[5]*t[7],r[5]=o[3]*t[2]+o[4]*t[5]+o[5]*t[8],r[6]=o[6]*t[0]+o[7]*t[3]+o[8]*t[6],r[7]=o[6]*t[1]+o[7]*t[4]+o[8]*t[7],r[8]=o[6]*t[2]+o[7]*t[5]+o[8]*t[8],n}multiply(e){return this.multiplyToRef(e,new h)}divideToRef(e,n){const o=this._m,t=e.m,r=n.m;return r[0]=o[0]/t[0],r[1]=o[1]/t[1],r[2]=o[2]/t[2],r[3]=o[3]/t[3],r[4]=o[4]/t[4],r[5]=o[5]/t[5],r[6]=o[6]/t[6],r[7]=o[7]/t[7],r[8]=o[8]/t[8],n}divide(e){return this.divideToRef(e,new h)}addToRef(e,n){const o=this._m,t=e.m,r=n.m;return r[0]=o[0]+t[0],r[1]=o[1]+t[1],r[2]=o[2]+t[2],r[3]=o[3]+t[3],r[4]=o[4]+t[4],r[5]=o[5]+t[5],r[6]=o[6]+t[6],r[7]=o[7]+t[7],r[8]=o[8]+t[8],n}add(e){return this.addToRef(e,new h)}subtractToRef(e,n){const o=this._m,t=e.m,r=n.m;return r[0]=o[0]-t[0],r[1]=o[1]-t[1],r[2]=o[2]-t[2],r[3]=o[3]-t[3],r[4]=o[4]-t[4],r[5]=o[5]-t[5],r[6]=o[6]-t[6],r[7]=o[7]-t[7],r[8]=o[8]-t[8],n}subtract(e){return this.subtractToRef(e,new h)}toArray(e=[]){for(let n=0;n<9;n++)e[n]=this._m[n];return e}asArray(){return this.toArray()}fromArray(e){for(let n=0;n<9;n++)this._m[n]=e[n];return this}transpose(){const e=this._m;return new h([e[0],e[3],e[6],e[1],e[4],e[7],e[2],e[5],e[8]])}determinant(){const e=this._m;return e[0]*(e[4]*e[8]-e[5]*e[7])-e[1]*(e[3]*e[8]-e[5]*e[6])+e[2]*(e[3]*e[7]-e[4]*e[6])}inverse(){const e=this.determinant();if(e===0)throw new Error("Matrix is not invertible");const n=this._m,o=1/e;return new h([(n[4]*n[8]-n[5]*n[7])*o,(n[2]*n[7]-n[1]*n[8])*o,(n[1]*n[5]-n[2]*n[4])*o,(n[5]*n[6]-n[3]*n[8])*o,(n[0]*n[8]-n[2]*n[6])*o,(n[2]*n[3]-n[0]*n[5])*o,(n[3]*n[7]-n[4]*n[6])*o,(n[1]*n[6]-n[0]*n[7])*o,(n[0]*n[4]-n[1]*n[3])*o])}equals(e,n=0){const o=this._m,t=e.m;return n===0?o[0]===t[0]&&o[1]===t[1]&&o[2]===t[2]&&o[3]===t[3]&&o[4]===t[4]&&o[5]===t[5]&&o[6]===t[6]&&o[7]===t[7]&&o[8]===t[8]:Math.abs(o[0]-t[0])<n&&Math.abs(o[1]-t[1])<n&&Math.abs(o[2]-t[2])<n&&Math.abs(o[3]-t[3])<n&&Math.abs(o[4]-t[4])<n&&Math.abs(o[5]-t[5])<n&&Math.abs(o[6]-t[6])<n&&Math.abs(o[7]-t[7])<n&&Math.abs(o[8]-t[8])<n}getClassName(){return"FlowGraphMatrix3D"}toString(){return`FlowGraphMatrix3D(${this._m.join(", ")})`}}var x;(function(a){a.Any="any",a.String="string",a.Number="number",a.Boolean="boolean",a.Object="object",a.Integer="FlowGraphInteger",a.Vector2="Vector2",a.Vector3="Vector3",a.Vector4="Vector4",a.Quaternion="Quaternion",a.Matrix="Matrix",a.Matrix2D="Matrix2D",a.Matrix3D="Matrix3D",a.Color3="Color3",a.Color4="Color4"})(x||(x={}));class c{constructor(e,n,o=-1){this.typeName=e,this.defaultValue=n,this.animationType=o}serialize(e){e.typeName=this.typeName,e.defaultValue=this.defaultValue}}const v=new c("any",void 0),C=new c("string",""),B=new c("number",0,0),P=new c("boolean",!1),d=new c("Vector2",I.Zero(),5),F=new c("Vector3",V.Zero(),1),M=new c("Vector4",S.Zero()),_=new c("Matrix",D.Identity(),3),R=new c("Matrix2D",new m),A=new c("Matrix3D",new h),G=new c("Color3",q.Black(),4),T=new c("Color4",new L(0,0,0,0),7),y=new c("Quaternion",w.Identity(),2);y.typeTransformer=a=>a.getClassName&&a.getClassName()==="Vector4"?w.FromArray(a.asArray()):a.getClassName&&a.getClassName()==="Vector3"?w.FromEulerVector(a):a.getClassName&&a.getClassName()==="Matrix"?w.FromRotationMatrix(a):a;const E=new c("FlowGraphInteger",new p(0),0);function H(a){const e=a;switch(typeof a){case"string":return C;case"number":return B;case"boolean":return P;case"object":if(e.getClassName)switch(e.getClassName()){case"Vector2":return d;case"Vector3":return F;case"Vector4":return M;case"Matrix":return _;case"Color3":return G;case"Color4":return T;case"Quaternion":return y;case"FlowGraphInteger":return E;case"Matrix2D":return R;case"Matrix3D":return A}return v;default:return v}}function K(a){switch(a){case"string":return C;case"number":return B;case"boolean":return P;case"Vector2":return d;case"Vector3":return F;case"Vector4":return M;case"Matrix":return _;case"Color3":return G;case"Color4":return T;case"Quaternion":return y;case"FlowGraphInteger":return E;case"Matrix2D":return R;case"Matrix3D":return A;default:return v}}function Q(a){switch(a){case"number":return 0;case"Vector2":return 5;case"Vector3":return 1;case"Matrix":return 3;case"Color3":return 4;case"Color4":return 7;case"Quaternion":return 2;default:return 0}}function W(a){switch(a){case 0:return B;case 5:return d;case 1:return F;case 3:return _;case 4:return G;case 7:return T;case 2:return y;default:return v}}function X(a){const[e,n]=a.split(":");return J({op:e,extension:n})}function J(a,e=!0){const n=a.extension?b[a.extension]?.[a.op]:$[a.op];if(!n&&(f.Warn(`No mapping found for operation ${a.op} and extension ${a.extension||"KHR_interactivity"}`),e)){const o={},t={flows:{}};if(a.inputValueSockets){o.values={};for(const r in a.inputValueSockets)o.values[r]={name:r}}return a.outputValueSockets&&(t.values={},Object.keys(a.outputValueSockets).forEach(r=>{t.values[r]={name:r}})),{blocks:[],inputs:o,outputs:t}}return n}function Y(a,e,n){b[e]||(b[e]={}),b[e][a]=n}const b={BABYLON:{"flow/log":{blocks:["FlowGraphConsoleLogBlock"],inputs:{values:{message:{name:"message"}}}}}},$={"event/onStart":{blocks:["FlowGraphSceneReadyEventBlock"],outputs:{flows:{out:{name:"done"}}}},"event/onTick":{blocks:["FlowGraphSceneTickEventBlock"],inputs:{},outputs:{values:{timeSinceLastTick:{name:"deltaTime",gltfType:"number"}},flows:{out:{name:"done"}}}},"event/send":{blocks:["FlowGraphSendCustomEventBlock"],outputs:{flows:{out:{name:"done"}}},extraProcessor(a,e,n,o,t){if(e.op!=="event/send"||!a.configuration||Object.keys(a.configuration).length!==1)throw new Error("Receive event should have a single configuration object, the event itself");const i=a.configuration.event.value[0];if(typeof i!="number")throw new Error("Event id should be a number");const l=o.arrays.events[i],s=t[0];return s.config||(s.config={}),s.config.eventId=l.eventId,s.config.eventData=l.eventData,t}},"event/receive":{blocks:["FlowGraphReceiveCustomEventBlock"],outputs:{flows:{out:{name:"done"}}},validation(a,e){if(!a.configuration)return f.Error("Receive event should have a configuration object"),!1;const n=a.configuration.event;if(!n)return f.Error("Receive event should have a single configuration object, the event itself"),!1;const o=n.value[0];return typeof o!="number"?(f.Error("Event id should be a number"),!1):e.events?.[o]?!0:(f.Error(`Event with id ${o} not found`),!1)},extraProcessor(a,e,n,o,t){if(e.op!=="event/receive"||!a.configuration||Object.keys(a.configuration).length!==1)throw new Error("Receive event should have a single configuration object, the event itself");const i=a.configuration.event.value[0];if(typeof i!="number")throw new Error("Event id should be a number");const l=o.arrays.events[i],s=t[0];return s.config||(s.config={}),s.config.eventId=l.eventId,s.config.eventData=l.eventData,t}},"math/e":u("FlowGraphEBlock"),"math/pi":u("FlowGraphPIBlock"),"math/inf":u("FlowGraphInfBlock"),"math/nan":u("FlowGraphNaNBlock"),"math/abs":u("FlowGraphAbsBlock"),"math/sign":u("FlowGraphSignBlock"),"math/trunc":u("FlowGraphTruncBlock"),"math/floor":u("FlowGraphFloorBlock"),"math/ceil":u("FlowGraphCeilBlock"),"math/round":{blocks:["FlowGraphRoundBlock"],configuration:{},inputs:{values:{a:{name:"a"}}},outputs:{values:{value:{name:"value"}}},extraProcessor(a,e,n,o,t){return t[0].config=t[0].config||{},t[0].config.roundHalfAwayFromZero=!0,t}},"math/fract":u("FlowGraphFractBlock"),"math/neg":u("FlowGraphNegationBlock"),"math/add":u("FlowGraphAddBlock",["a","b"],!0),"math/sub":u("FlowGraphSubtractBlock",["a","b"],!0),"math/mul":{blocks:["FlowGraphMultiplyBlock"],extraProcessor(a,e,n,o,t){t[0].config=t[0].config||{},t[0].config.useMatrixPerComponent=!0;let r=-1;return Object.keys(a.values||{}).find(i=>a.values?.[i].type!==void 0?(r=a.values[i].type,!0):!1),r!==-1&&(t[0].config.type=o.arrays.types[r].flowGraphType),t}},"math/div":u("FlowGraphDivideBlock",["a","b"],!0),"math/rem":u("FlowGraphModuloBlock",["a","b"]),"math/min":u("FlowGraphMinBlock",["a","b"]),"math/max":u("FlowGraphMaxBlock",["a","b"]),"math/clamp":u("FlowGraphClampBlock",["a","b","c"]),"math/saturate":u("FlowGraphSaturateBlock"),"math/mix":u("FlowGraphMathInterpolationBlock",["a","b","c"]),"math/eq":u("FlowGraphEqualityBlock",["a","b"]),"math/lt":u("FlowGraphLessThanBlock",["a","b"]),"math/le":u("FlowGraphLessThanOrEqualBlock",["a","b"]),"math/gt":u("FlowGraphGreaterThanBlock",["a","b"]),"math/ge":u("FlowGraphGreaterThanOrEqualBlock",["a","b"]),"math/isnan":u("FlowGraphIsNaNBlock"),"math/isinf":u("FlowGraphIsInfBlock"),"math/select":{blocks:["FlowGraphConditionalBlock"],inputs:{values:{condition:{name:"condition"},a:{name:"onTrue"},b:{name:"onFalse"}}},outputs:{values:{value:{name:"output"}}}},"math/random":{blocks:["FlowGraphRandomBlock"],outputs:{values:{value:{name:"value"}}}},"math/sin":u("FlowGraphSinBlock"),"math/cos":u("FlowGraphCosBlock"),"math/tan":u("FlowGraphTanBlock"),"math/asin":u("FlowGraphASinBlock"),"math/acos":u("FlowGraphACosBlock"),"math/atan":u("FlowGraphATanBlock"),"math/atan2":u("FlowGraphATan2Block",["a","b"]),"math/sinh":u("FlowGraphSinhBlock"),"math/cosh":u("FlowGraphCoshBlock"),"math/tanh":u("FlowGraphTanhBlock"),"math/asinh":u("FlowGraphASinhBlock"),"math/acosh":u("FlowGraphACoshBlock"),"math/atanh":u("FlowGraphATanhBlock"),"math/exp":u("FlowGraphExponentialBlock"),"math/log":u("FlowGraphLogBlock"),"math/log2":u("FlowGraphLog2Block"),"math/log10":u("FlowGraphLog10Block"),"math/sqrt":u("FlowGraphSquareRootBlock"),"math/cbrt":u("FlowGraphCubeRootBlock"),"math/pow":u("FlowGraphPowerBlock",["a","b"]),"math/length":u("FlowGraphLengthBlock"),"math/normalize":u("FlowGraphNormalizeBlock"),"math/dot":u("FlowGraphDotBlock",["a","b"]),"math/cross":u("FlowGraphCrossBlock",["a","b"]),"math/rotate2d":u("FlowGraphRotate2DBlock",["a","b"]),"math/rotate3d":u("FlowGraphRotate3DBlock",["a","b","c"]),"math/transform":{blocks:["FlowGraphTransformVectorBlock"],inputs:{values:{a:{name:"a"},b:{name:"b"}}},outputs:{values:{value:{name:"value"}}}},"math/combine2":{blocks:["FlowGraphCombineVector2Block"],inputs:{values:{a:{name:"input_0",gltfType:"number"},b:{name:"input_1",gltfType:"number"}}},outputs:{values:{value:{name:"value"}}}},"math/combine3":{blocks:["FlowGraphCombineVector3Block"],inputs:{values:{a:{name:"input_0",gltfType:"number"},b:{name:"input_1",gltfType:"number"},c:{name:"input_2",gltfType:"number"}}},outputs:{values:{value:{name:"value"}}}},"math/combine4":{blocks:["FlowGraphCombineVector4Block"],inputs:{values:{a:{name:"input_0",gltfType:"number"},b:{name:"input_1",gltfType:"number"},c:{name:"input_2",gltfType:"number"},d:{name:"input_3",gltfType:"number"}}},outputs:{values:{value:{name:"value"}}}},"math/extract2":{blocks:["FlowGraphExtractVector2Block"],inputs:{values:{a:{name:"input",gltfType:"number"}}},outputs:{values:{0:{name:"output_0"},1:{name:"output_1"}}}},"math/extract3":{blocks:["FlowGraphExtractVector3Block"],inputs:{values:{a:{name:"input",gltfType:"number"}}},outputs:{values:{0:{name:"output_0"},1:{name:"output_1"},2:{name:"output_2"}}}},"math/extract4":{blocks:["FlowGraphExtractVector4Block"],inputs:{values:{a:{name:"input",gltfType:"number"}}},outputs:{values:{0:{name:"output_0"},1:{name:"output_1"},2:{name:"output_2"},3:{name:"output_3"}}}},"math/transpose":u("FlowGraphTransposeBlock"),"math/determinant":u("FlowGraphDeterminantBlock"),"math/inverse":u("FlowGraphInvertMatrixBlock"),"math/matmul":u("FlowGraphMatrixMultiplicationBlock",["a","b"]),"math/matCompose":{blocks:["FlowGraphMatrixCompose"],inputs:{values:{translation:{name:"position",gltfType:"float3"},rotation:{name:"rotationQuaternion",gltfType:"float4"},scale:{name:"scaling",gltfType:"float3"}}},outputs:{values:{value:{name:"value"}}},extraProcessor(a,e,n,o,t,r){const i=t[0].dataInputs.find(l=>l.name==="rotationQuaternion");if(!i)throw new Error("Rotation quaternion input not found");return r._connectionValues[i.uniqueId]&&(r._connectionValues[i.uniqueId].type="Quaternion"),t}},"math/matDecompose":{blocks:["FlowGraphMatrixDecompose"],inputs:{values:{a:{name:"input",gltfType:"number"}}},outputs:{values:{translation:{name:"position"},rotation:{name:"rotationQuaternion"},scale:{name:"scaling"}}}},"math/combine2x2":{blocks:["FlowGraphCombineMatrix2DBlock"],inputs:{values:{a:{name:"input_0",gltfType:"number"},b:{name:"input_1",gltfType:"number"},c:{name:"input_2",gltfType:"number"},d:{name:"input_3",gltfType:"number"}}},outputs:{values:{value:{name:"value"}}},extraProcessor(a,e,n,o,t){return t[0].config=t[0].config||{},t[0].config.inputIsColumnMajor=!0,t}},"math/extract2x2":{blocks:["FlowGraphExtractMatrix2DBlock"],inputs:{values:{a:{name:"input",gltfType:"float2x2"}}},outputs:{values:{0:{name:"output_0"},1:{name:"output_1"},2:{name:"output_2"},3:{name:"output_3"}}}},"math/combine3x3":{blocks:["FlowGraphCombineMatrix3DBlock"],inputs:{values:{a:{name:"input_0",gltfType:"number"},b:{name:"input_1",gltfType:"number"},c:{name:"input_2",gltfType:"number"},d:{name:"input_3",gltfType:"number"},e:{name:"input_4",gltfType:"number"},f:{name:"input_5",gltfType:"number"},g:{name:"input_6",gltfType:"number"},h:{name:"input_7",gltfType:"number"},i:{name:"input_8",gltfType:"number"}}},outputs:{values:{value:{name:"value"}}},extraProcessor(a,e,n,o,t){return t[0].config=t[0].config||{},t[0].config.inputIsColumnMajor=!0,t}},"math/extract3x3":{blocks:["FlowGraphExtractMatrix3DBlock"],inputs:{values:{a:{name:"input",gltfType:"float3x3"}}},outputs:{values:{0:{name:"output_0"},1:{name:"output_1"},2:{name:"output_2"},3:{name:"output_3"},4:{name:"output_4"},5:{name:"output_5"},6:{name:"output_6"},7:{name:"output_7"},8:{name:"output_8"}}}},"math/combine4x4":{blocks:["FlowGraphCombineMatrixBlock"],inputs:{values:{a:{name:"input_0",gltfType:"number"},b:{name:"input_1",gltfType:"number"},c:{name:"input_2",gltfType:"number"},d:{name:"input_3",gltfType:"number"},e:{name:"input_4",gltfType:"number"},f:{name:"input_5",gltfType:"number"},g:{name:"input_6",gltfType:"number"},h:{name:"input_7",gltfType:"number"},i:{name:"input_8",gltfType:"number"},j:{name:"input_9",gltfType:"number"},k:{name:"input_10",gltfType:"number"},l:{name:"input_11",gltfType:"number"},m:{name:"input_12",gltfType:"number"},n:{name:"input_13",gltfType:"number"},o:{name:"input_14",gltfType:"number"},p:{name:"input_15",gltfType:"number"}}},outputs:{values:{value:{name:"value"}}},extraProcessor(a,e,n,o,t){return t[0].config=t[0].config||{},t[0].config.inputIsColumnMajor=!0,t}},"math/extract4x4":{blocks:["FlowGraphExtractMatrixBlock"],configuration:{},inputs:{values:{a:{name:"input",gltfType:"number"}}},outputs:{values:{0:{name:"output_0"},1:{name:"output_1"},2:{name:"output_2"},3:{name:"output_3"},4:{name:"output_4"},5:{name:"output_5"},6:{name:"output_6"},7:{name:"output_7"},8:{name:"output_8"},9:{name:"output_9"},10:{name:"output_10"},11:{name:"output_11"},12:{name:"output_12"},13:{name:"output_13"},14:{name:"output_14"},15:{name:"output_15"}}}},"math/compose":{blocks:["FlowGraphMatrixCompose"],configuration:{},inputs:{values:{translation:{name:"position",gltfType:"float3"},rotation:{name:"rotationQuaternion",gltfType:"float4"},scale:{name:"scaling",gltfType:"float3"}}},outputs:{values:{value:{name:"output"}}}},"math/decompose":{blocks:["FlowGraphMatrixDecompose"],configuration:{},inputs:{values:{a:{name:"input"}}},outputs:{values:{translation:{name:"position"},rotation:{name:"rotationQuaternion"},scale:{name:"scaling"}}}},"math/not":{blocks:["FlowGraphBitwiseNotBlock"],inputs:{values:{a:{name:"a"}}},outputs:{values:{value:{name:"value"}}},extraProcessor(a,e,n,o,t,r){t[0].config=t[0].config||{};const i=t[0].dataInputs[0];return t[0].config.valueType=r._connectionValues[i.uniqueId]?.type??"FlowGraphInteger",t}},"math/and":{blocks:["FlowGraphBitwiseAndBlock"],inputs:{values:{a:{name:"a"},b:{name:"b"}}},outputs:{values:{value:{name:"value"}}},extraProcessor(a,e,n,o,t,r){t[0].config=t[0].config||{};const i=t[0].dataInputs[0],l=t[0].dataInputs[1];return t[0].config.valueType=r._connectionValues[i.uniqueId]?.type??r._connectionValues[l.uniqueId]?.type??"FlowGraphInteger",t}},"math/or":{blocks:["FlowGraphBitwiseOrBlock"],inputs:{values:{a:{name:"a"},b:{name:"b"}}},outputs:{values:{value:{name:"value"}}},extraProcessor(a,e,n,o,t,r){t[0].config=t[0].config||{};const i=t[0].dataInputs[0],l=t[0].dataInputs[1];return t[0].config.valueType=r._connectionValues[i.uniqueId]?.type??r._connectionValues[l.uniqueId]?.type??"FlowGraphInteger",t}},"math/xor":{blocks:["FlowGraphBitwiseXorBlock"],inputs:{values:{a:{name:"a"},b:{name:"b"}}},outputs:{values:{value:{name:"value"}}},extraProcessor(a,e,n,o,t,r){t[0].config=t[0].config||{};const i=t[0].dataInputs[0],l=t[0].dataInputs[1];return t[0].config.valueType=r._connectionValues[i.uniqueId]?.type??r._connectionValues[l.uniqueId]?.type??"FlowGraphInteger",t}},"math/asr":u("FlowGraphBitwiseRightShiftBlock",["a","b"]),"math/lsl":u("FlowGraphBitwiseLeftShiftBlock",["a","b"]),"math/clz":u("FlowGraphLeadingZerosBlock"),"math/ctz":u("FlowGraphTrailingZerosBlock"),"math/popcnt":u("FlowGraphOneBitsCounterBlock"),"math/rad":u("FlowGraphDegToRadBlock"),"math/deg":u("FlowGraphRadToDegBlock"),"type/boolToInt":u("FlowGraphBooleanToInt"),"type/boolToFloat":u("FlowGraphBooleanToFloat"),"type/intToBool":u("FlowGraphIntToBoolean"),"type/intToFloat":u("FlowGraphIntToFloat"),"type/floatToInt":u("FlowGraphFloatToInt"),"type/floatToBool":u("FlowGraphFloatToBoolean"),"flow/sequence":{blocks:["FlowGraphSequenceBlock"],extraProcessor(a,e,n,o,t){const r=t[0];return r.config||(r.config={}),r.config.outputSignalCount=Object.keys(a.flows||[]).length,r.signalOutputs.forEach((i,l)=>{i.name="out_"+l}),t}},"flow/branch":{blocks:["FlowGraphBranchBlock"],outputs:{flows:{true:{name:"onTrue"},false:{name:"onFalse"}}}},"flow/switch":{blocks:["FlowGraphSwitchBlock"],configuration:{cases:{name:"cases",inOptions:!0,defaultValue:[]}},inputs:{values:{selection:{name:"case"}}},validation(a){if(a.configuration&&a.configuration.cases){const e=a.configuration.cases.value;if(!e.every(t=>typeof t=="number"&&/^\d+$/.test(t.toString())))return a.configuration.cases.value=[],!0;const o=new Set(e);a.configuration.cases.value=Array.from(o)}return!0},extraProcessor(a,e,n,o,t){if(e.op!=="flow/switch"||!a.flows||Object.keys(a.flows).length===0)throw new Error("Switch should have a single configuration object, the cases array");return t[0].signalOutputs.forEach(i=>{i.name!=="default"&&(i.name="out_"+i.name)}),t}},"flow/while":{blocks:["FlowGraphWhileLoopBlock"],outputs:{flows:{loopBody:{name:"executionFlow"}}}},"flow/for":{blocks:["FlowGraphForLoopBlock"],configuration:{initialIndex:{name:"initialIndex",gltfType:"number",inOptions:!0,defaultValue:0}},inputs:{values:{startIndex:{name:"startIndex",gltfType:"number"},endIndex:{name:"endIndex",gltfType:"number"}}},outputs:{values:{index:{name:"index"}},flows:{loopBody:{name:"executionFlow"}}}},"flow/doN":{blocks:["FlowGraphDoNBlock"],configuration:{},inputs:{values:{n:{name:"maxExecutions",gltfType:"number"}}},outputs:{values:{currentCount:{name:"executionCount"}}}},"flow/multiGate":{blocks:["FlowGraphMultiGateBlock"],configuration:{isRandom:{name:"isRandom",gltfType:"boolean",inOptions:!0,defaultValue:!1},isLoop:{name:"isLoop",gltfType:"boolean",inOptions:!0,defaultValue:!1}},extraProcessor(a,e,n,o,t){if(e.op!=="flow/multiGate"||!a.flows||Object.keys(a.flows).length===0)throw new Error("MultiGate should have a single configuration object, the number of output flows");const r=t[0];return r.config||(r.config={}),r.config.outputSignalCount=Object.keys(a.flows).length,r.signalOutputs.forEach((i,l)=>{i.name="out_"+l}),t}},"flow/waitAll":{blocks:["FlowGraphWaitAllBlock"],configuration:{inputFlows:{name:"inputSignalCount",gltfType:"number",inOptions:!0,defaultValue:0}},inputs:{flows:{"[segment]":{name:"in_$1"}}},validation(a){return typeof a.configuration?.inputFlows?.value[0]!="number"&&(a.configuration=a.configuration||{inputFlows:{value:[0]}},a.configuration.inputFlows.value=[0]),!0}},"flow/throttle":{blocks:["FlowGraphThrottleBlock"],outputs:{flows:{err:{name:"error"}}}},"flow/setDelay":{blocks:["FlowGraphSetDelayBlock"],outputs:{flows:{err:{name:"error"}}}},"flow/cancelDelay":{blocks:["FlowGraphCancelDelayBlock"]},"variable/get":{blocks:["FlowGraphGetVariableBlock"],validation(a){return a.configuration?.variable?.value?!0:(f.Error("Variable get block should have a variable configuration"),!1)},configuration:{variable:{name:"variable",gltfType:"number",flowGraphType:"string",inOptions:!0,isVariable:!0,dataTransformer(a,e){return[e.getVariableName(a[0])]}}}},"variable/set":{blocks:["FlowGraphSetVariableBlock"],configuration:{variable:{name:"variable",gltfType:"number",flowGraphType:"string",inOptions:!0,isVariable:!0,dataTransformer(a,e){return[e.getVariableName(a[0])]}}}},"variable/setMultiple":{blocks:["FlowGraphSetVariableBlock"],configuration:{variables:{name:"variables",gltfType:"number",flowGraphType:"string",inOptions:!0,dataTransformer(a,e){return[a[0].map(n=>e.getVariableName(n))]}}},extraProcessor(a,e,n,o,t){return t[0].dataInputs.forEach(i=>{i.name=o.getVariableName(+i.name)}),t}},"variable/interpolate":{blocks:["FlowGraphInterpolationBlock","FlowGraphContextBlock","FlowGraphPlayAnimationBlock","FlowGraphBezierCurveEasing","FlowGraphGetVariableBlock"],configuration:{variable:{name:"propertyName",inOptions:!0,isVariable:!0,dataTransformer(a,e){return[e.getVariableName(a[0])]}},useSlerp:{name:"animationType",inOptions:!0,defaultValue:!1,dataTransformer:a=>a[0]===!0?["Quaternion"]:[void 0]}},inputs:{values:{value:{name:"value_1"},duration:{name:"duration_1",gltfType:"number"},p1:{name:"controlPoint1",toBlock:"FlowGraphBezierCurveEasing"},p2:{name:"controlPoint2",toBlock:"FlowGraphBezierCurveEasing"}},flows:{in:{name:"in",toBlock:"FlowGraphPlayAnimationBlock"}}},outputs:{flows:{err:{name:"error",toBlock:"FlowGraphPlayAnimationBlock"},out:{name:"out",toBlock:"FlowGraphPlayAnimationBlock"},done:{name:"done",toBlock:"FlowGraphPlayAnimationBlock"}}},interBlockConnectors:[{input:"object",output:"userVariables",inputBlockIndex:2,outputBlockIndex:1,isVariable:!0},{input:"animation",output:"animation",inputBlockIndex:2,outputBlockIndex:0,isVariable:!0},{input:"easingFunction",output:"easingFunction",inputBlockIndex:0,outputBlockIndex:3,isVariable:!0},{input:"value_0",output:"value",inputBlockIndex:0,outputBlockIndex:4,isVariable:!0}],extraProcessor(a,e,n,o,t){var r,i;const l=t[0],s=a.configuration?.variable.value[0];if(typeof s!="number")throw f.Error("Variable index is not defined for variable interpolation block"),new Error("Variable index is not defined for variable interpolation block");const g=o.arrays.staticVariables[s];typeof l.config.animationType.value>"u"&&(o.arrays.staticVariables,l.config.animationType.value=Q(g.type));const k=t[4];return k.config||(k.config={}),(r=k.config).variable||(r.variable={}),k.config.variable.value=o.getVariableName(s),(i=t[3]).config||(i.config={}),t}},"pointer/get":{blocks:["FlowGraphGetPropertyBlock","FlowGraphJsonPointerParserBlock"],configuration:{pointer:{name:"jsonPointer",toBlock:"FlowGraphJsonPointerParserBlock"}},inputs:{values:{"[segment]":{name:"$1",toBlock:"FlowGraphJsonPointerParserBlock"}}},interBlockConnectors:[{input:"object",output:"object",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"propertyName",output:"propertyName",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"customGetFunction",output:"getFunction",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0}],extraProcessor(a,e,n,o,t){return t.forEach(r=>{r.className==="FlowGraphJsonPointerParserBlock"&&(r.config||(r.config={}),r.config.outputValue=!0)}),t}},"pointer/set":{blocks:["FlowGraphSetPropertyBlock","FlowGraphJsonPointerParserBlock"],configuration:{pointer:{name:"jsonPointer",toBlock:"FlowGraphJsonPointerParserBlock"}},inputs:{values:{value:{name:"value"},"[segment]":{name:"$1",toBlock:"FlowGraphJsonPointerParserBlock"}}},outputs:{flows:{err:{name:"error"}}},interBlockConnectors:[{input:"object",output:"object",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"propertyName",output:"propertyName",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"customSetFunction",output:"setFunction",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0}],extraProcessor(a,e,n,o,t){return t.forEach(r=>{r.className==="FlowGraphJsonPointerParserBlock"&&(r.config||(r.config={}),r.config.outputValue=!0)}),t}},"pointer/interpolate":{blocks:["FlowGraphInterpolationBlock","FlowGraphJsonPointerParserBlock","FlowGraphPlayAnimationBlock","FlowGraphEasingBlock"],configuration:{pointer:{name:"jsonPointer",toBlock:"FlowGraphJsonPointerParserBlock"}},inputs:{values:{value:{name:"value_1"},"[segment]":{name:"$1",toBlock:"FlowGraphJsonPointerParserBlock"},duration:{name:"duration_1",gltfType:"number"},p1:{name:"controlPoint1",toBlock:"FlowGraphEasingBlock"},p2:{name:"controlPoint2",toBlock:"FlowGraphEasingBlock"}},flows:{in:{name:"in",toBlock:"FlowGraphPlayAnimationBlock"}}},outputs:{flows:{err:{name:"error",toBlock:"FlowGraphPlayAnimationBlock"},out:{name:"out",toBlock:"FlowGraphPlayAnimationBlock"},done:{name:"done",toBlock:"FlowGraphPlayAnimationBlock"}}},interBlockConnectors:[{input:"object",output:"object",inputBlockIndex:2,outputBlockIndex:1,isVariable:!0},{input:"propertyName",output:"propertyName",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"customBuildAnimation",output:"generateAnimationsFunction",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"animation",output:"animation",inputBlockIndex:2,outputBlockIndex:0,isVariable:!0},{input:"easingFunction",output:"easingFunction",inputBlockIndex:0,outputBlockIndex:3,isVariable:!0},{input:"value_0",output:"value",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0}],extraProcessor(a,e,n,o,t){return t.forEach(r=>{r.className==="FlowGraphJsonPointerParserBlock"?(r.config||(r.config={}),r.config.outputValue=!0):r.className==="FlowGraphInterpolationBlock"&&(r.config||(r.config={}),Object.keys(a.values||[]).forEach(i=>{const l=a.values?.[i];if(i==="value"&&l){const s=l.type;s!==void 0&&(r.config.animationType=o.arrays.types[s].flowGraphType)}}))}),t}},"animation/start":{blocks:["FlowGraphPlayAnimationBlock","FlowGraphArrayIndexBlock","KHR_interactivity/FlowGraphGLTFDataProvider"],inputs:{values:{animation:{name:"index",gltfType:"number",toBlock:"FlowGraphArrayIndexBlock"},speed:{name:"speed",gltfType:"number"},startTime:{name:"from",gltfType:"number",dataTransformer:(a,e)=>[a[0]*e._loader.parent.targetFps]},endTime:{name:"to",gltfType:"number",dataTransformer:(a,e)=>[a[0]*e._loader.parent.targetFps]}}},outputs:{flows:{err:{name:"error"}}},interBlockConnectors:[{input:"animationGroup",output:"value",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"array",output:"animationGroups",inputBlockIndex:1,outputBlockIndex:2,isVariable:!0}],extraProcessor(a,e,n,o,t,r,i){const l=t[t.length-1];return l.config||(l.config={}),l.config.glTF=i,t}},"animation/stop":{blocks:["FlowGraphStopAnimationBlock","FlowGraphArrayIndexBlock","KHR_interactivity/FlowGraphGLTFDataProvider"],inputs:{values:{animation:{name:"index",gltfType:"number",toBlock:"FlowGraphArrayIndexBlock"}}},outputs:{flows:{err:{name:"error"}}},interBlockConnectors:[{input:"animationGroup",output:"value",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"array",output:"animationGroups",inputBlockIndex:1,outputBlockIndex:2,isVariable:!0}],extraProcessor(a,e,n,o,t,r,i){const l=t[t.length-1];return l.config||(l.config={}),l.config.glTF=i,t}},"animation/stopAt":{blocks:["FlowGraphStopAnimationBlock","FlowGraphArrayIndexBlock","KHR_interactivity/FlowGraphGLTFDataProvider"],configuration:{},inputs:{values:{animation:{name:"index",gltfType:"number",toBlock:"FlowGraphArrayIndexBlock"},stopTime:{name:"stopAtFrame",gltfType:"number",dataTransformer:(a,e)=>[a[0]*e._loader.parent.targetFps]}}},outputs:{flows:{err:{name:"error"}}},interBlockConnectors:[{input:"animationGroup",output:"value",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"array",output:"animationGroups",inputBlockIndex:1,outputBlockIndex:2,isVariable:!0}],extraProcessor(a,e,n,o,t,r,i){const l=t[t.length-1];return l.config||(l.config={}),l.config.glTF=i,t}},"math/switch":{blocks:["FlowGraphDataSwitchBlock"],configuration:{cases:{name:"cases",inOptions:!0,defaultValue:[]}},inputs:{values:{selection:{name:"case"}}},validation(a){if(a.configuration&&a.configuration.cases){const e=a.configuration.cases.value;if(!e.every(t=>typeof t=="number"&&/^\d+$/.test(t.toString())))return a.configuration.cases.value=[],!0;const o=new Set(e);a.configuration.cases.value=Array.from(o)}return!0},extraProcessor(a,e,n,o,t){return t[0].dataInputs.forEach(i=>{i.name!=="default"&&i.name!=="case"&&(i.name="in_"+i.name)}),t}},"debug/log":{blocks:["FlowGraphConsoleLogBlock"],configuration:{message:{name:"messageTemplate",inOptions:!0}}}};function u(a,e=["a"],n){return{blocks:[a],inputs:{values:e.reduce((o,t)=>(o[t]={name:t},o),{})},outputs:{values:{value:{name:"value"}}},extraProcessor(o,t,r,i,l){if(n){l[0].config=l[0].config||{};let s=-1;Object.keys(o.values||{}).find(g=>o.values?.[g].type!==void 0?(s=o.values[g].type,!0):!1),s!==-1&&(l[0].config.type=i.arrays.types[s].flowGraphType)}return l}}}export{p as F,v as R,Y as a,B as b,P as c,W as d,F as e,d as f,K as g,_ as h,y as i,E as j,H as k,M as l,R as m,m as n,A as o,h as p,C as q,J as r,X as s};
//# sourceMappingURL=declarationMapper-BZjsjg7g.js.map
