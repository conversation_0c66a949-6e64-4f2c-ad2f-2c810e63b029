import{SvelteComponent as Ge,init as Ne,safe_not_equal as He,element as u,space as P,text as G,create_component as ae,claim_element as d,children as S,claim_space as V,claim_text as N,detach as i,claim_component as oe,src_url_equal as We,attr as h,insert_hydration as R,append_hydration as l,mount_component as re,listen as de,set_data as Q,transition_in as ie,transition_out as ce,destroy_component as ue,component_subscribe as Me,createEventDispatcher as qe,get_svelte_dataset as j,onMount as Fe,run_all as Ye,bubble as $e,noop as je}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{K as Ke,$ as Ae,s as Oe,l as Ze,N as Je,d as Qe}from"./2.B2AoQPnG.js";import{C as Xe}from"./clear.D_TEXRKD.js";import{c as xe}from"./Dropdown.BTvmrOlN.js";/* empty css                                              */import{C as ze}from"./Checkbox.BBJAZbOw.js";function et(o){let n,e,_,c,t,r=o[1]("common.settings")+"",v,k,B,y,C,b,p,T,D,q;return p=new Xe({}),{c(){n=u("h2"),e=u("img"),c=P(),t=u("div"),v=G(r),k=P(),B=u("div"),y=G(o[0]),C=P(),b=u("button"),ae(p.$$.fragment),this.h()},l(m){n=d(m,"H2",{class:!0});var f=S(n);e=d(f,"IMG",{src:!0,alt:!0,class:!0}),c=V(f),t=d(f,"DIV",{class:!0});var M=S(t);v=N(M,r),k=V(M),B=d(M,"DIV",{class:!0});var E=S(B);y=N(E,o[0]),E.forEach(i),M.forEach(i),f.forEach(i),C=V(m),b=d(m,"BUTTON",{class:!0});var a=S(b);oe(p.$$.fragment,a),a.forEach(i),this.h()},h(){We(e.src,_=Ke)||h(e,"src",_),h(e,"alt",""),h(e,"class","svelte-1b2d8fn"),h(B,"class","url svelte-1b2d8fn"),h(t,"class","title svelte-1b2d8fn"),h(n,"class","svelte-1b2d8fn"),h(b,"class","svelte-1b2d8fn")},m(m,f){R(m,n,f),l(n,e),l(n,c),l(n,t),l(t,v),l(t,k),l(t,B),l(B,y),R(m,C,f),R(m,b,f),re(p,b,null),T=!0,D||(q=de(b,"click",o[3]),D=!0)},p(m,[f]){(!T||f&2)&&r!==(r=m[1]("common.settings")+"")&&Q(v,r),(!T||f&1)&&Q(y,m[0])},i(m){T||(ie(p.$$.fragment,m),T=!0)},o(m){ce(p.$$.fragment,m),T=!1},d(m){m&&(i(n),i(C),i(b)),ue(p),D=!1,q()}}}function tt(o,n,e){let _;Me(o,Ae,v=>e(1,_=v));let{root:c}=n;const t=qe();Oe();const r=()=>t("close");return o.$$set=v=>{"root"in v&&e(0,c=v.root)},[c,_,t,r]}class lt extends Ge{constructor(n){super(),Ne(this,n,tt,et,He,{root:0})}}const nt="data:image/svg+xml,%3csvg%20viewBox='0%200%2020%2020'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='%23000000'%3e%3cg%20id='SVGRepo_bgCarrier'%20stroke-width='0'%3e%3c/g%3e%3cg%20id='SVGRepo_tracerCarrier'%20stroke-linecap='round'%20stroke-linejoin='round'%3e%3c/g%3e%3cg%20id='SVGRepo_iconCarrier'%3e%3ctitle%3erecord%20[%23982]%3c/title%3e%3cdesc%3eCreated%20with%20Sketch.%3c/desc%3e%3cdefs%3e%3c/defs%3e%3cg%20id='Page-1'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='Dribbble-Light-Preview'%20transform='translate(-380.000000,%20-3839.000000)'%20fill='%23808080'%3e%3cg%20id='icons'%20transform='translate(56.000000,%20160.000000)'%3e%3cpath%20d='M338,3689%20C338,3691.209%20336.209,3693%20334,3693%20C331.791,3693%20330,3691.209%20330,3689%20C330,3686.791%20331.791,3685%20334,3685%20C336.209,3685%20338,3686.791%20338,3689%20M334,3697%20C329.589,3697%20326,3693.411%20326,3689%20C326,3684.589%20329.589,3681%20334,3681%20C338.411,3681%20342,3684.589%20342,3689%20C342,3693.411%20338.411,3697%20334,3697%20M334,3679%20C328.477,3679%20324,3683.477%20324,3689%20C324,3694.523%20328.477,3699%20334,3699%20C339.523,3699%20344,3694.523%20344,3689%20C344,3683.477%20339.523,3679%20334,3679'%20id='record-[%23982]'%3e%3c/path%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e";function Ue(o){let n,e,_=o[7]("common.display_theme")+"",c,t,r,v,k,B="☀︎  Light",y,C,b,p,T="⏾   Dark",D,q,m,f,M="🖥︎  System",E,a,z;return{c(){n=u("div"),e=u("h2"),c=G(_),t=P(),r=u("p"),v=u("li"),k=u("button"),k.textContent=B,C=P(),b=u("li"),p=u("button"),p.textContent=T,q=P(),m=u("li"),f=u("button"),f.textContent=M,this.h()},l(I){n=d(I,"DIV",{class:!0});var H=S(n);e=d(H,"H2",{class:!0});var O=S(e);c=N(O,_),O.forEach(i),t=V(H),r=d(H,"P",{class:!0});var W=S(r);v=d(W,"LI",{class:!0});var X=S(v);k=d(X,"BUTTON",{class:!0,"data-svelte-h":!0}),j(k)!=="svelte-vhdrzm"&&(k.textContent=B),X.forEach(i),C=V(W),b=d(W,"LI",{class:!0});var g=S(b);p=d(g,"BUTTON",{class:!0,"data-svelte-h":!0}),j(p)!=="svelte-psqkhq"&&(p.textContent=T),g.forEach(i),q=V(W),m=d(W,"LI",{class:!0});var x=S(m);f=d(x,"BUTTON",{class:!0,"data-svelte-h":!0}),j(f)!=="svelte-qtl7qp"&&(f.textContent=M),x.forEach(i),W.forEach(i),H.forEach(i),this.h()},h(){h(e,"class","svelte-1asl00n"),h(k,"class","svelte-1asl00n"),h(v,"class",y="theme-button "+(o[6]==="light"?"current-theme":"inactive-theme")+" svelte-1asl00n"),h(p,"class","svelte-1asl00n"),h(b,"class",D="theme-button "+(o[6]==="dark"?"current-theme":"inactive-theme")+" svelte-1asl00n"),h(f,"class","svelte-1asl00n"),h(m,"class",E="theme-button "+(o[6]==="system"?"current-theme":"inactive-theme")+" svelte-1asl00n"),h(r,"class","padded theme-buttons svelte-1asl00n"),h(n,"class","banner-wrap svelte-1asl00n")},m(I,H){R(I,n,H),l(n,e),l(e,c),l(n,t),l(n,r),l(r,v),l(v,k),l(r,C),l(r,b),l(b,p),l(r,q),l(r,m),l(m,f),a||(z=[de(v,"click",o[14]),de(b,"click",o[15]),de(m,"click",o[16])],a=!0)},p(I,H){H&128&&_!==(_=I[7]("common.display_theme")+"")&&Q(c,_),H&64&&y!==(y="theme-button "+(I[6]==="light"?"current-theme":"inactive-theme")+" svelte-1asl00n")&&h(v,"class",y),H&64&&D!==(D="theme-button "+(I[6]==="dark"?"current-theme":"inactive-theme")+" svelte-1asl00n")&&h(b,"class",D),H&64&&E!==(E="theme-button "+(I[6]==="system"?"current-theme":"inactive-theme")+" svelte-1asl00n")&&h(m,"class",E)},d(I){I&&i(n),a=!1,Ye(z)}}}function st(o){let n,e,_="launch(pwa=True)",c;return{c(){n=G(`Progressive Web App is not enabled for this app. To enable it, start your
			Gradio app with `),e=u("code"),e.textContent=_,c=G(".")},l(t){n=N(t,`Progressive Web App is not enabled for this app. To enable it, start your
			Gradio app with `),e=d(t,"CODE",{"data-svelte-h":!0}),j(e)!=="svelte-11lvcdl"&&(e.textContent=_),c=N(t,".")},m(t,r){R(t,n,r),R(t,e,r),R(t,c,r)},p:je,d(t){t&&(i(n),i(e),i(c))}}}function at(o){let n,e,_,c;return{c(){n=G("You can install this app as a Progressive Web App on your device. Visit "),e=u("a"),_=G(o[0]),c=G(" and click the install button in the URL address bar of your browser."),this.h()},l(t){n=N(t,"You can install this app as a Progressive Web App on your device. Visit "),e=d(t,"A",{href:!0,target:!0,class:!0});var r=S(e);_=N(r,o[0]),r.forEach(i),c=N(t," and click the install button in the URL address bar of your browser."),this.h()},h(){h(e,"href",o[0]),h(e,"target","_blank"),h(e,"class","svelte-1asl00n")},m(t,r){R(t,n,r),R(t,e,r),l(e,_),R(t,c,r)},p(t,r){r&1&&Q(_,t[0]),r&1&&h(e,"href",t[0])},d(t){t&&(i(n),i(e),i(c))}}}function ot(o){let n,e,_,c,t,r,v=o[7]("common.language")+"",k,B,y,C,b,p,T,D=o[7]("common.pwa")+"",q,m,f,M,E,a,z=o[7]("common.screen_studio")+"",I,H,O,W="beta",X,g,x,fe,ve,_e,ee,Ie="Start Recording",pe,be,ge,te,Re="Stop Recording",we,Ce,ke,ye,F,Ee,Y,Se,$,Le=`<img src="${nt}" alt="Start Recording" class="svelte-1asl00n"/>
		Start Recording`,K,Te,Pe;e=new lt({props:{root:o[0]}}),e.$on("close",o[13]);let U=o[3]===null&&Ue(o);C=new xe({props:{label:"Language",choices:Ze,show_label:!1,value:o[5]}}),C.$on("change",o[10]);function Ve(s,w){return s[4]?at:st}let he=Ve(o),A=he(o);return F=new ze({props:{label:"Include automatic zoom in/out",interactive:!0,value:o[1]}}),F.$on("change",o[11]),Y=new ze({props:{label:"Include automatic video trimming",interactive:!0,value:o[2]}}),Y.$on("change",o[12]),{c(){n=u("div"),ae(e.$$.fragment),_=P(),U&&U.c(),c=P(),t=u("div"),r=u("h2"),k=G(v),B=P(),y=u("p"),ae(C.$$.fragment),b=P(),p=u("div"),T=u("h2"),q=G(D),m=P(),f=u("p"),A.c(),M=P(),E=u("div"),a=u("h2"),I=G(z),H=P(),O=u("span"),O.textContent=W,X=P(),g=u("p"),x=G(`Screen Studio allows you to record your screen and generates a video of your
		app with automatically adding zoom in and zoom out effects as well as
		trimming the video to remove the prediction time.
		`),fe=u("br"),ve=u("br"),_e=G(`
		Start recording by clicking the `),ee=u("i"),ee.textContent=Ie,pe=G(` button below and then
		sharing the current browser tab of your Gradio demo. Use your app as you
		would normally to generate a prediction.
		`),be=u("br"),ge=G(`
		Stop recording by clicking the `),te=u("i"),te.textContent=Re,we=G(` button in the footer of
		the demo.
		`),Ce=u("br"),ke=u("br"),ye=P(),ae(F.$$.fragment),Ee=P(),ae(Y.$$.fragment),Se=P(),$=u("button"),$.innerHTML=Le,this.h()},l(s){n=d(s,"DIV",{class:!0});var w=S(n);oe(e.$$.fragment,w),w.forEach(i),_=V(s),U&&U.l(s),c=V(s),t=d(s,"DIV",{class:!0});var Z=S(t);r=d(Z,"H2",{class:!0});var ne=S(r);k=N(ne,v),ne.forEach(i),B=V(Z),y=d(Z,"P",{class:!0});var se=S(y);oe(C.$$.fragment,se),se.forEach(i),Z.forEach(i),b=V(s),p=d(s,"DIV",{class:!0});var J=S(p);T=d(J,"H2",{class:!0});var Be=S(T);q=N(Be,D),Be.forEach(i),m=V(J),f=d(J,"P",{class:!0});var De=S(f);A.l(De),De.forEach(i),J.forEach(i),M=V(s),E=d(s,"DIV",{class:!0});var le=S(E);a=d(le,"H2",{class:!0});var me=S(a);I=N(me,z),H=V(me),O=d(me,"SPAN",{class:!0,"data-svelte-h":!0}),j(O)!=="svelte-7xk80m"&&(O.textContent=W),me.forEach(i),X=V(le),g=d(le,"P",{class:!0});var L=S(g);x=N(L,`Screen Studio allows you to record your screen and generates a video of your
		app with automatically adding zoom in and zoom out effects as well as
		trimming the video to remove the prediction time.
		`),fe=d(L,"BR",{}),ve=d(L,"BR",{}),_e=N(L,`
		Start recording by clicking the `),ee=d(L,"I",{"data-svelte-h":!0}),j(ee)!=="svelte-166xaih"&&(ee.textContent=Ie),pe=N(L,` button below and then
		sharing the current browser tab of your Gradio demo. Use your app as you
		would normally to generate a prediction.
		`),be=d(L,"BR",{}),ge=N(L,`
		Stop recording by clicking the `),te=d(L,"I",{"data-svelte-h":!0}),j(te)!=="svelte-2rpx65"&&(te.textContent=Re),we=N(L,` button in the footer of
		the demo.
		`),Ce=d(L,"BR",{}),ke=d(L,"BR",{}),ye=V(L),oe(F.$$.fragment,L),Ee=V(L),oe(Y.$$.fragment,L),L.forEach(i),Se=V(le),$=d(le,"BUTTON",{class:!0,"data-svelte-h":!0}),j($)!=="svelte-4km0bz"&&($.innerHTML=Le),le.forEach(i),this.h()},h(){h(n,"class","banner-wrap svelte-1asl00n"),h(r,"class","svelte-1asl00n"),h(y,"class","padded svelte-1asl00n"),h(t,"class","banner-wrap svelte-1asl00n"),h(T,"class","svelte-1asl00n"),h(f,"class","padded svelte-1asl00n"),h(p,"class","banner-wrap svelte-1asl00n"),h(O,"class","beta-tag svelte-1asl00n"),h(a,"class","svelte-1asl00n"),h(g,"class","padded svelte-1asl00n"),h($,"class","record-button svelte-1asl00n"),h(E,"class","banner-wrap svelte-1asl00n")},m(s,w){R(s,n,w),re(e,n,null),R(s,_,w),U&&U.m(s,w),R(s,c,w),R(s,t,w),l(t,r),l(r,k),l(t,B),l(t,y),re(C,y,null),R(s,b,w),R(s,p,w),l(p,T),l(T,q),l(p,m),l(p,f),A.m(f,null),R(s,M,w),R(s,E,w),l(E,a),l(a,I),l(a,H),l(a,O),l(E,X),l(E,g),l(g,x),l(g,fe),l(g,ve),l(g,_e),l(g,ee),l(g,pe),l(g,be),l(g,ge),l(g,te),l(g,we),l(g,Ce),l(g,ke),l(g,ye),re(F,g,null),l(g,Ee),re(Y,g,null),l(E,Se),l(E,$),K=!0,Te||(Pe=de($,"click",o[17]),Te=!0)},p(s,[w]){const Z={};w&1&&(Z.root=s[0]),e.$set(Z),s[3]===null?U?U.p(s,w):(U=Ue(s),U.c(),U.m(c.parentNode,c)):U&&(U.d(1),U=null),(!K||w&128)&&v!==(v=s[7]("common.language")+"")&&Q(k,v);const ne={};w&32&&(ne.value=s[5]),C.$set(ne),(!K||w&128)&&D!==(D=s[7]("common.pwa")+"")&&Q(q,D),he===(he=Ve(s))&&A?A.p(s,w):(A.d(1),A=he(s),A&&(A.c(),A.m(f,null))),(!K||w&128)&&z!==(z=s[7]("common.screen_studio")+"")&&Q(I,z);const se={};w&2&&(se.value=s[1]),F.$set(se);const J={};w&4&&(J.value=s[2]),Y.$set(J)},i(s){K||(ie(e.$$.fragment,s),ie(C.$$.fragment,s),ie(F.$$.fragment,s),ie(Y.$$.fragment,s),K=!0)},o(s){ce(e.$$.fragment,s),ce(C.$$.fragment,s),ce(F.$$.fragment,s),ce(Y.$$.fragment,s),K=!1},d(s){s&&(i(n),i(_),i(c),i(t),i(b),i(p),i(M),i(E)),ue(e),U&&U.d(s),ue(C),A.d(),ue(F),ue(Y),Te=!1,Pe()}}}function rt(o,n,e){let _;Me(o,Ae,a=>e(7,_=a));let{root:c}=n,{space_id:t}=n,{pwa_enabled:r}=n;const v=qe();c===""&&(c=location.protocol+"//"+location.host+location.pathname),c.endsWith("/")||(c+="/");function k(a){const z=new URL(window.location.href);a==="system"?(z.searchParams.delete("__theme"),e(6,y="system")):(z.searchParams.set("__theme",a),e(6,y=a)),window.location.href=z.toString()}Fe(()=>{var I;document.body.style.overflow="hidden","parentIFrame"in window&&((I=window.parentIFrame)==null||I.scrollTo(0,0));const z=new URL(window.location.href).searchParams.get("__theme");return e(6,y=z||"system"),()=>{document.body.style.overflow="auto"}});let B,y="system",{allow_zoom:C=!0}=n,{allow_video_trim:b=!0}=n;Je.subscribe(a=>{a&&e(5,B=a)});function p(a){const z=a.detail;Qe(z)}function T(a){e(1,C=a.detail)}function D(a){e(2,b=a.detail)}Oe();function q(a){$e.call(this,o,a)}const m=()=>k("light"),f=()=>k("dark"),M=()=>k("system"),E=()=>{v("close"),v("start_recording")};return o.$$set=a=>{"root"in a&&e(0,c=a.root),"space_id"in a&&e(3,t=a.space_id),"pwa_enabled"in a&&e(4,r=a.pwa_enabled),"allow_zoom"in a&&e(1,C=a.allow_zoom),"allow_video_trim"in a&&e(2,b=a.allow_video_trim)},[c,C,b,t,r,B,y,_,v,k,p,T,D,q,m,f,M,E]}class vt extends Ge{constructor(n){super(),Ne(this,n,rt,ot,He,{root:0,space_id:3,pwa_enabled:4,allow_zoom:1,allow_video_trim:2})}}export{vt as default};
//# sourceMappingURL=Settings.BXvpy3uo.js.map
