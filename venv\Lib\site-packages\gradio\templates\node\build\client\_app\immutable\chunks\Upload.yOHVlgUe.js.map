{"version": 3, "file": "Upload.yOHVlgUe.js", "sources": ["../../../../../../../icons/src/ImagePaste.svelte", "../../../../../../../icons/src/Upload.svelte", "../../../../../../../upload/src/UploadProgress.svelte", "../../../../../../../upload/src/utils.ts", "../../../../../../../upload/src/Upload.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 256 256\"\n\t><path\n\t\tfill=\"currentColor\"\n\t\td=\"M200 32h-36.26a47.92 47.92 0 0 0-71.48 0H56a16 16 0 0 0-16 16v168a16 16 0 0 0 16 16h144a16 16 0 0 0 16-16V48a16 16 0 0 0-16-16m-72 0a32 32 0 0 1 32 32H96a32 32 0 0 1 32-32m72 184H56V48h26.75A47.9 47.9 0 0 0 80 64v8a8 8 0 0 0 8 8h80a8 8 0 0 0 8-8v-8a47.9 47.9 0 0 0-2.75-16H200Z\"\n\t/></svg\n>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"90%\"\n\theight=\"90%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"2\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-upload\"\n\t><path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\" /><polyline\n\t\tpoints=\"17 8 12 3 7 8\"\n\t/><line x1=\"12\" y1=\"3\" x2=\"12\" y2=\"15\" /></svg\n>\n", "<script lang=\"ts\">\n\timport { FileData, type Client } from \"@gradio/client\";\n\timport { onMount, createEventDispatcher, onDestroy } from \"svelte\";\n\n\ttype FileDataWithProgress = FileData & { progress: number };\n\n\texport let upload_id: string;\n\texport let root: string;\n\texport let files: FileData[];\n\texport let stream_handler: Client[\"stream\"];\n\n\tlet stream: Awaited<ReturnType<Client[\"stream\"]>>;\n\tlet progress = false;\n\tlet current_file_upload: FileDataWithProgress;\n\tlet file_to_display: FileDataWithProgress;\n\n\tlet files_with_progress: FileDataWithProgress[] = files.map((file) => {\n\t\treturn {\n\t\t\t...file,\n\t\t\tprogress: 0\n\t\t};\n\t});\n\n\tconst dispatch = createEventDispatcher();\n\n\tfunction handleProgress(filename: string, chunk_size: number): void {\n\t\t// Find the corresponding file in the array and update its progress\n\t\tfiles_with_progress = files_with_progress.map((file) => {\n\t\t\tif (file.orig_name === filename) {\n\t\t\t\tfile.progress += chunk_size;\n\t\t\t}\n\t\t\treturn file;\n\t\t});\n\t}\n\n\tfunction getProgress(file: FileDataWithProgress): number {\n\t\treturn (file.progress * 100) / (file.size || 0) || 0;\n\t}\n\n\tonMount(async () => {\n\t\tstream = await stream_handler(\n\t\t\tnew URL(`${root}/gradio_api/upload_progress?upload_id=${upload_id}`)\n\t\t);\n\n\t\tif (stream == null) {\n\t\t\tthrow new Error(\"Event source is not defined\");\n\t\t}\n\t\t// Event listener for progress updates\n\t\tstream.onmessage = async function (event) {\n\t\t\tconst _data = JSON.parse(event.data);\n\t\t\tif (!progress) progress = true;\n\t\t\tif (_data.msg === \"done\") {\n\t\t\t\t// the stream will close itself but is here for clarity; remove .close() in 5.0\n\t\t\t\tstream?.close();\n\t\t\t\tdispatch(\"done\");\n\t\t\t} else {\n\t\t\t\tcurrent_file_upload = _data;\n\t\t\t\thandleProgress(_data.orig_name, _data.chunk_size);\n\t\t\t}\n\t\t};\n\t});\n\tonDestroy(() => {\n\t\t// the stream will close itself but is here for clarity; remove .close() in 5.0\n\t\tif (stream != null || stream != undefined) stream.close();\n\t});\n\n\tfunction calculateTotalProgress(files: FileData[]): number {\n\t\tlet totalProgress = 0;\n\t\tfiles.forEach((file) => {\n\t\t\ttotalProgress += getProgress(file as FileDataWithProgress);\n\t\t});\n\n\t\tdocument.documentElement.style.setProperty(\n\t\t\t\"--upload-progress-width\",\n\t\t\t(totalProgress / files.length).toFixed(2) + \"%\"\n\t\t);\n\n\t\treturn totalProgress / files.length;\n\t}\n\n\t$: calculateTotalProgress(files_with_progress);\n\n\t$: file_to_display = current_file_upload || files_with_progress[0];\n</script>\n\n<div class=\"wrap\" class:progress>\n\t<span class=\"uploading\"\n\t\t>Uploading {files_with_progress.length}\n\t\t{files_with_progress.length > 1 ? \"files\" : \"file\"}...</span\n\t>\n\n\t{#if file_to_display}\n\t\t<div class=\"file\">\n\t\t\t<span>\n\t\t\t\t<div class=\"progress-bar\">\n\t\t\t\t\t<progress\n\t\t\t\t\t\tstyle=\"visibility:hidden;height:0;width:0;\"\n\t\t\t\t\t\tvalue={getProgress(file_to_display)}\n\t\t\t\t\t\tmax=\"100\">{getProgress(file_to_display)}</progress\n\t\t\t\t\t>\n\t\t\t\t</div>\n\t\t\t</span>\n\t\t\t<span class=\"file-name\">\n\t\t\t\t{file_to_display.orig_name}\n\t\t\t</span>\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.wrap {\n\t\toverflow-y: auto;\n\t\ttransition: opacity 0.5s ease-in-out;\n\t\tbackground: var(--block-background-fill);\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmin-height: var(--size-40);\n\t\twidth: var(--size-full);\n\t}\n\n\t.wrap::after {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: var(--upload-progress-width);\n\t\theight: 100%;\n\t\ttransition: all 0.5s ease-in-out;\n\t\tz-index: 1;\n\t}\n\n\t.uploading {\n\t\tfont-size: var(--text-lg);\n\t\tfont-family: var(--font);\n\t\tz-index: 2;\n\t}\n\n\t.file-name {\n\t\tmargin: var(--spacing-md);\n\t\tfont-size: var(--text-lg);\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\t.file {\n\t\tfont-size: var(--text-md);\n\t\tz-index: 2;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.file progress {\n\t\tdisplay: inline;\n\t\theight: var(--size-1);\n\t\twidth: 100%;\n\t\ttransition: all 0.5s ease-in-out;\n\t\tcolor: var(--color-accent);\n\t\tborder: none;\n\t}\n\n\t.file progress[value]::-webkit-progress-value {\n\t\tbackground-color: var(--color-accent);\n\t\tborder-radius: 20px;\n\t}\n\n\t.file progress[value]::-webkit-progress-bar {\n\t\tbackground-color: var(--border-color-accent);\n\t\tborder-radius: 20px;\n\t}\n\n\t.progress-bar {\n\t\twidth: 14px;\n\t\theight: 14px;\n\t\tborder-radius: 50%;\n\t\tbackground: radial-gradient(\n\t\t\t\tclosest-side,\n\t\t\t\tvar(--block-background-fill) 64%,\n\t\t\t\ttransparent 53% 100%\n\t\t\t),\n\t\t\tconic-gradient(\n\t\t\t\tvar(--color-accent) var(--upload-progress-width),\n\t\t\t\tvar(--border-color-accent) 0\n\t\t\t);\n\t\ttransition: all 0.5s ease-in-out;\n\t}\n</style>\n", "interface DragActionOptions {\n\tdisable_click?: boolean;\n\taccepted_types?: string | string[] | null;\n\tmode?: \"single\" | \"multiple\" | \"directory\";\n\ton_drag_change?: (dragging: boolean) => void;\n\ton_files?: (files: File[]) => void;\n}\n\ntype ActionReturn = {\n\tupdate: (new_options: DragActionOptions) => void;\n\tdestroy: () => void;\n};\n\nexport function create_drag(): {\n\tdrag: (node: HTMLElement, options: DragActionOptions) => ActionReturn;\n\topen_file_upload: () => void;\n} {\n\tlet hidden_input: HTMLInputElement;\n\tlet _options: DragActionOptions;\n\treturn {\n\t\tdrag(\n\t\t\tnode: HTMLElement,\n\t\t\toptions: DragActionOptions = {}\n\t\t): {\n\t\t\tupdate: (new_options: DragActionOptions) => void;\n\t\t\tdestroy: () => void;\n\t\t} {\n\t\t\t_options = options;\n\n\t\t\t// Create and configure hidden file input\n\t\t\tfunction setup_hidden_input(): void {\n\t\t\t\thidden_input = document.createElement(\"input\");\n\t\t\t\thidden_input.type = \"file\";\n\t\t\t\thidden_input.style.display = \"none\";\n\t\t\t\thidden_input.setAttribute(\"aria-label\", \"File upload\");\n\t\t\t\thidden_input.setAttribute(\"data-testid\", \"file-upload\");\n\t\t\t\tconst accept_options = Array.isArray(_options.accepted_types)\n\t\t\t\t\t? _options.accepted_types.join(\",\")\n\t\t\t\t\t: _options.accepted_types || undefined;\n\n\t\t\t\tif (accept_options) {\n\t\t\t\t\thidden_input.accept = accept_options;\n\t\t\t\t}\n\n\t\t\t\thidden_input.multiple = _options.mode === \"multiple\" || false;\n\t\t\t\tif (_options.mode === \"directory\") {\n\t\t\t\t\thidden_input.webkitdirectory = true;\n\t\t\t\t\thidden_input.setAttribute(\"directory\", \"\");\n\t\t\t\t\thidden_input.setAttribute(\"mozdirectory\", \"\");\n\t\t\t\t}\n\t\t\t\tnode.appendChild(hidden_input);\n\t\t\t}\n\n\t\t\tsetup_hidden_input();\n\n\t\t\tfunction handle_drag(e: DragEvent): void {\n\t\t\t\te.preventDefault();\n\t\t\t\te.stopPropagation();\n\t\t\t}\n\n\t\t\tfunction handle_drag_enter(e: DragEvent): void {\n\t\t\t\te.preventDefault();\n\t\t\t\te.stopPropagation();\n\t\t\t\t_options.on_drag_change?.(true);\n\t\t\t}\n\n\t\t\tfunction handle_drag_leave(e: DragEvent): void {\n\t\t\t\te.preventDefault();\n\t\t\t\te.stopPropagation();\n\t\t\t\t_options.on_drag_change?.(false);\n\t\t\t}\n\n\t\t\tfunction handle_drop(e: DragEvent): void {\n\t\t\t\te.preventDefault();\n\t\t\t\te.stopPropagation();\n\t\t\t\t_options.on_drag_change?.(false);\n\n\t\t\t\tif (!e.dataTransfer?.files) return;\n\t\t\t\tconst files = Array.from(e.dataTransfer.files);\n\t\t\t\tif (files.length > 0) {\n\t\t\t\t\t_options.on_files?.(files);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfunction handle_click(): void {\n\t\t\t\tif (!_options.disable_click) {\n\t\t\t\t\thidden_input.value = \"\";\n\t\t\t\t\thidden_input.click();\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfunction handle_file_input_change(): void {\n\t\t\t\tif (hidden_input.files) {\n\t\t\t\t\tconst files = Array.from(hidden_input.files);\n\t\t\t\t\tif (files.length > 0) {\n\t\t\t\t\t\t_options.on_files?.(files);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Add all event listeners\n\t\t\tnode.addEventListener(\"drag\", handle_drag);\n\t\t\tnode.addEventListener(\"dragstart\", handle_drag);\n\t\t\tnode.addEventListener(\"dragend\", handle_drag);\n\t\t\tnode.addEventListener(\"dragover\", handle_drag);\n\t\t\tnode.addEventListener(\"dragenter\", handle_drag_enter);\n\t\t\tnode.addEventListener(\"dragleave\", handle_drag_leave);\n\t\t\tnode.addEventListener(\"drop\", handle_drop);\n\t\t\tnode.addEventListener(\"click\", handle_click);\n\t\t\thidden_input!.addEventListener(\"change\", handle_file_input_change);\n\n\t\t\treturn {\n\t\t\t\tupdate(new_options: DragActionOptions) {\n\t\t\t\t\t_options = new_options;\n\t\t\t\t\t// Recreate hidden input with new options\n\t\t\t\t\thidden_input.remove();\n\t\t\t\t\tsetup_hidden_input();\n\t\t\t\t\thidden_input.addEventListener(\"change\", handle_file_input_change);\n\t\t\t\t},\n\t\t\t\tdestroy() {\n\t\t\t\t\tnode.removeEventListener(\"drag\", handle_drag);\n\t\t\t\t\tnode.removeEventListener(\"dragstart\", handle_drag);\n\t\t\t\t\tnode.removeEventListener(\"dragend\", handle_drag);\n\t\t\t\t\tnode.removeEventListener(\"dragover\", handle_drag);\n\t\t\t\t\tnode.removeEventListener(\"dragenter\", handle_drag_enter);\n\t\t\t\t\tnode.removeEventListener(\"dragleave\", handle_drag_leave);\n\t\t\t\t\tnode.removeEventListener(\"drop\", handle_drop);\n\t\t\t\t\tnode.removeEventListener(\"click\", handle_click);\n\t\t\t\t\thidden_input.removeEventListener(\"change\", handle_file_input_change);\n\t\t\t\t\thidden_input.remove();\n\t\t\t\t}\n\t\t\t};\n\t\t},\n\t\topen_file_upload(): void {\n\t\t\tif (hidden_input) {\n\t\t\t\thidden_input.value = \"\";\n\t\t\t\thidden_input.click();\n\t\t\t}\n\t\t}\n\t};\n}\n", "<script lang=\"ts\">\n\timport { createEventDispatcher, tick, getContext } from \"svelte\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { prepare_files, type Client } from \"@gradio/client\";\n\timport { _ } from \"svelte-i18n\";\n\timport UploadProgress from \"./UploadProgress.svelte\";\n\timport { create_drag } from \"./utils\";\n\n\tconst { drag, open_file_upload: _open_file_upload } = create_drag();\n\n\texport let filetype: string | string[] | null = null;\n\texport let dragging = false;\n\texport let boundedheight = true;\n\texport let center = true;\n\texport let flex = true;\n\texport let file_count: \"single\" | \"multiple\" | \"directory\" = \"single\";\n\texport let disable_click = false;\n\texport let root: string;\n\texport let hidden = false;\n\texport let format: \"blob\" | \"file\" = \"file\";\n\texport let uploading = false;\n\texport let show_progress = true;\n\texport let max_file_size: number | null = null;\n\texport let upload: Client[\"upload\"];\n\texport let stream_handler: Client[\"stream\"];\n\texport let icon_upload = false;\n\texport let height: number | string | undefined = undefined;\n\texport let aria_label: string | undefined = undefined;\n\texport function open_upload(): void {\n\t\t_open_file_upload();\n\t}\n\tlet upload_id: string;\n\tlet file_data: FileData[];\n\tlet accept_file_types: string | null;\n\tlet use_post_upload_validation: boolean | null = null;\n\n\tconst get_ios = (): boolean => {\n\t\tif (typeof navigator !== \"undefined\") {\n\t\t\tconst userAgent = navigator.userAgent.toLowerCase();\n\t\t\treturn userAgent.indexOf(\"iphone\") > -1 || userAgent.indexOf(\"ipad\") > -1;\n\t\t}\n\t\treturn false;\n\t};\n\n\t$: ios = get_ios();\n\n\tconst dispatch = createEventDispatcher();\n\tconst validFileTypes = [\"image\", \"video\", \"audio\", \"text\", \"file\"];\n\tconst process_file_type = (type: string): string => {\n\t\tif (ios && type.startsWith(\".\")) {\n\t\t\tuse_post_upload_validation = true;\n\t\t\treturn type;\n\t\t}\n\t\tif (ios && type.includes(\"file/*\")) {\n\t\t\treturn \"*\";\n\t\t}\n\t\tif (type.startsWith(\".\") || type.endsWith(\"/*\")) {\n\t\t\treturn type;\n\t\t}\n\t\tif (validFileTypes.includes(type)) {\n\t\t\treturn type + \"/*\";\n\t\t}\n\t\treturn \".\" + type;\n\t};\n\n\t$: if (filetype == null) {\n\t\taccept_file_types = null;\n\t} else if (typeof filetype === \"string\") {\n\t\taccept_file_types = process_file_type(filetype);\n\t} else if (ios && filetype.includes(\"file/*\")) {\n\t\taccept_file_types = \"*\";\n\t} else {\n\t\tfiletype = filetype.map(process_file_type);\n\t\taccept_file_types = filetype.join(\", \");\n\t}\n\n\texport function paste_clipboard(): void {\n\t\tnavigator.clipboard.read().then(async (items) => {\n\t\t\tfor (let i = 0; i < items.length; i++) {\n\t\t\t\tconst type = items[i].types.find((t) => t.startsWith(\"image/\"));\n\t\t\t\tif (type) {\n\t\t\t\t\titems[i].getType(type).then(async (blob) => {\n\t\t\t\t\t\tconst file = new File(\n\t\t\t\t\t\t\t[blob],\n\t\t\t\t\t\t\t`clipboard.${type.replace(\"image/\", \"\")}`\n\t\t\t\t\t\t);\n\t\t\t\t\t\tawait load_files([file]);\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n\n\texport function open_file_upload(): void {\n\t\t_open_file_upload();\n\t}\n\n\tasync function handle_upload(\n\t\tfile_data: FileData[]\n\t): Promise<(FileData | null)[]> {\n\t\tawait tick();\n\t\tupload_id = Math.random().toString(36).substring(2, 15);\n\t\tuploading = true;\n\t\ttry {\n\t\t\tconst _file_data = await upload(\n\t\t\t\tfile_data,\n\t\t\t\troot,\n\t\t\t\tupload_id,\n\t\t\t\tmax_file_size ?? Infinity\n\t\t\t);\n\t\t\tdispatch(\"load\", file_count === \"single\" ? _file_data?.[0] : _file_data);\n\t\t\tuploading = false;\n\t\t\treturn _file_data || [];\n\t\t} catch (e) {\n\t\t\tdispatch(\"error\", (e as Error).message);\n\t\t\tuploading = false;\n\t\t\treturn [];\n\t\t}\n\t}\n\n\tfunction is_valid_mimetype(\n\t\tfile_accept: string | string[] | null,\n\t\tuploaded_file_extension: string,\n\t\tuploaded_file_type: string\n\t): boolean {\n\t\tif (\n\t\t\t!file_accept ||\n\t\t\tfile_accept === \"*\" ||\n\t\t\tfile_accept === \"file/*\" ||\n\t\t\t(Array.isArray(file_accept) &&\n\t\t\t\tfile_accept.some((accept) => accept === \"*\" || accept === \"file/*\"))\n\t\t) {\n\t\t\treturn true;\n\t\t}\n\t\tlet acceptArray: string[];\n\t\tif (typeof file_accept === \"string\") {\n\t\t\tacceptArray = file_accept.split(\",\").map((s) => s.trim());\n\t\t} else if (Array.isArray(file_accept)) {\n\t\t\tacceptArray = file_accept;\n\t\t} else {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn (\n\t\t\tacceptArray.includes(uploaded_file_extension) ||\n\t\t\tacceptArray.some((type) => {\n\t\t\t\tconst [category] = type.split(\"/\").map((s) => s.trim());\n\t\t\t\treturn (\n\t\t\t\t\ttype.endsWith(\"/*\") && uploaded_file_type.startsWith(category + \"/\")\n\t\t\t\t);\n\t\t\t})\n\t\t);\n\t}\n\n\texport async function load_files(\n\t\tfiles: File[] | Blob[]\n\t): Promise<(FileData | null)[] | void> {\n\t\tif (!files.length) {\n\t\t\treturn;\n\t\t}\n\t\tlet _files: File[] = files.map(\n\t\t\t(f) =>\n\t\t\t\tnew File([f], f instanceof File ? f.name : \"file\", { type: f.type })\n\t\t);\n\n\t\tif (ios && use_post_upload_validation) {\n\t\t\t_files = _files.filter((file) => {\n\t\t\t\tif (is_valid_file(file)) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\tdispatch(\n\t\t\t\t\t\"error\",\n\t\t\t\t\t`Invalid file type: ${file.name}. Only ${filetype} allowed.`\n\t\t\t\t);\n\t\t\t\treturn false;\n\t\t\t});\n\n\t\t\tif (_files.length === 0) {\n\t\t\t\treturn [];\n\t\t\t}\n\t\t}\n\n\t\tfile_data = await prepare_files(_files);\n\t\treturn await handle_upload(file_data);\n\t}\n\n\tfunction is_valid_file(file: File): boolean {\n\t\tif (!filetype) return true;\n\n\t\tconst allowed_types = Array.isArray(filetype) ? filetype : [filetype];\n\n\t\treturn allowed_types.some((type) => {\n\t\t\tconst processed_type = process_file_type(type);\n\n\t\t\tif (processed_type.startsWith(\".\")) {\n\t\t\t\treturn file.name.toLowerCase().endsWith(processed_type.toLowerCase());\n\t\t\t}\n\n\t\t\tif (processed_type === \"*\") {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tif (processed_type.endsWith(\"/*\")) {\n\t\t\t\tconst [category] = processed_type.split(\"/\");\n\t\t\t\treturn file.type.startsWith(category + \"/\");\n\t\t\t}\n\n\t\t\treturn file.type === processed_type;\n\t\t});\n\t}\n\n\tasync function load_files_from_upload(files: File[]): Promise<void> {\n\t\tconst files_to_load = files.filter((file) => {\n\t\t\tconst file_extension = \".\" + file.name.split(\".\").pop();\n\t\t\tif (\n\t\t\t\tfile_extension &&\n\t\t\t\tis_valid_mimetype(accept_file_types, file_extension, file.type)\n\t\t\t) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tif (\n\t\t\t\tfile_extension && Array.isArray(filetype)\n\t\t\t\t\t? filetype.includes(file_extension)\n\t\t\t\t\t: file_extension === filetype\n\t\t\t) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tdispatch(\"error\", `Invalid file type only ${filetype} allowed.`);\n\t\t\treturn false;\n\t\t});\n\n\t\tif (format != \"blob\") {\n\t\t\tawait load_files(files_to_load);\n\t\t} else {\n\t\t\tif (file_count === \"single\") {\n\t\t\t\tdispatch(\"load\", files_to_load[0]);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tdispatch(\"load\", files_to_load);\n\t\t}\n\t}\n\n\texport async function load_files_from_drop(e: DragEvent): Promise<void> {\n\t\tdragging = false;\n\t\tif (!e.dataTransfer?.files) return;\n\t\tconst files_to_load = Array.from(e.dataTransfer.files).filter(\n\t\t\tis_valid_file\n\t\t);\n\n\t\tif (format != \"blob\") {\n\t\t\tawait load_files(files_to_load);\n\t\t} else {\n\t\t\tif (file_count === \"single\") {\n\t\t\t\tdispatch(\"load\", files_to_load[0]);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tdispatch(\"load\", files_to_load);\n\t\t}\n\t}\n</script>\n\n{#if filetype === \"clipboard\"}\n\t<button\n\t\tclass:hidden\n\t\tclass:center\n\t\tclass:boundedheight\n\t\tclass:flex\n\t\tclass:icon-mode={icon_upload}\n\t\tstyle:height={icon_upload\n\t\t\t? \"\"\n\t\t\t: height\n\t\t\t\t? typeof height === \"number\"\n\t\t\t\t\t? height + \"px\"\n\t\t\t\t\t: height\n\t\t\t\t: \"100%\"}\n\t\ttabindex={hidden ? -1 : 0}\n\t\ton:click={paste_clipboard}\n\t\taria-label={aria_label || \"Paste from clipboard\"}\n\t>\n\t\t<slot />\n\t</button>\n{:else if uploading && show_progress}\n\t{#if !hidden}\n\t\t<UploadProgress {root} {upload_id} files={file_data} {stream_handler} />\n\t{/if}\n{:else}\n\t<button\n\t\tclass:hidden\n\t\tclass:center\n\t\tclass:boundedheight\n\t\tclass:flex\n\t\tclass:disable_click\n\t\tclass:icon-mode={icon_upload}\n\t\tstyle:height={icon_upload\n\t\t\t? \"\"\n\t\t\t: height\n\t\t\t\t? typeof height === \"number\"\n\t\t\t\t\t? height + \"px\"\n\t\t\t\t\t: height\n\t\t\t\t: \"100%\"}\n\t\ttabindex={hidden ? -1 : 0}\n\t\tuse:drag={{\n\t\t\ton_drag_change: (dragging) => (dragging = dragging),\n\t\t\ton_files: (files) => load_files_from_upload(files),\n\t\t\taccepted_types: accept_file_types,\n\t\t\tmode: file_count,\n\t\t\tdisable_click\n\t\t}}\n\t\taria-label={aria_label || \"Click to upload or drop files\"}\n\t\taria-dropeffect=\"copy\"\n\t>\n\t\t<slot />\n\t</button>\n{/if}\n\n<style>\n\tbutton {\n\t\tcursor: pointer;\n\t\twidth: var(--size-full);\n\t}\n\n\t.center {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t}\n\t.flex {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\t.hidden {\n\t\tdisplay: none;\n\t\tposition: absolute;\n\t\tflex-grow: 0;\n\t}\n\n\t.hidden :global(svg) {\n\t\tdisplay: none;\n\t}\n\n\t.disable_click {\n\t\tcursor: default;\n\t}\n\n\t.icon-mode {\n\t\tposition: absolute !important;\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t\tpadding: 0;\n\t\tmin-height: 0;\n\t\tborder-radius: var(--radius-circle);\n\t}\n\n\t.icon-mode :global(svg) {\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t}\n</style>\n"], "names": ["insert_hydration", "target", "svg", "anchor", "append_hydration", "path", "polyline", "line", "t0_value", "getProgress", "ctx", "t2_value", "progress_1", "progress_1_value_value", "div1", "span0", "div0", "span1", "dirty", "set_data", "t0", "t2", "t1_value", "create_if_block", "div", "span", "t1", "t3", "t3_value", "file", "calculateTotalProgress", "files", "totalProgress", "upload_id", "$$props", "root", "stream_handler", "stream", "progress", "current_file_upload", "file_to_display", "files_with_progress", "dispatch", "createEventDispatcher", "handleProgress", "filename", "chunk_size", "$$invalidate", "onMount", "event", "_data", "onDestroy", "create_drag", "hidden_input", "_options", "node", "options", "setup_hidden_input", "accept_options", "handle_drag", "e", "handle_drag_enter", "_a", "handle_drag_leave", "handle_drop", "_b", "_c", "handle_click", "handle_file_input_change", "new_options", "attr", "button", "button_aria_label_value", "set_style", "drag_function", "current", "create_if_block_2", "is_valid_mimetype", "file_accept", "uploaded_file_extension", "uploaded_file_type", "accept", "acceptArray", "s", "type", "category", "dragging", "drag", "_open_file_upload", "filetype", "boundedheight", "center", "flex", "file_count", "disable_click", "hidden", "format", "uploading", "show_progress", "max_file_size", "upload", "icon_upload", "height", "aria_label", "open_upload", "file_data", "accept_file_types", "use_post_upload_validation", "get_ios", "userAgent", "validFileTypes", "process_file_type", "ios", "paste_clipboard", "items", "i", "t", "blob", "load_files", "open_file_upload", "handle_upload", "tick", "_file_data", "_files", "f", "is_valid_file", "prepare_files", "processed_type", "load_files_from_upload", "files_to_load", "file_extension", "load_files_from_drop"], "mappings": "kgDAAAA,EASAC,EAAAC,EAAAC,CAAA,EAJEC,EAGCF,EAAAG,CAAA,k7BCRHL,EAcAC,EAAAC,EAAAC,CAAA,EAHEC,EAAsDF,EAAAG,CAAA,EAAAD,EAErDF,EAAAI,CAAA,EAAAF,EAAuCF,EAAAK,CAAA,gICqFzBC,EAAAC,EAAYC,EAAe,CAAA,CAAA,EAAA,WAKvCC,EAAAD,KAAgB,UAAS,udANjBE,EAAA,MAAAC,EAAAJ,EAAYC,EAAe,CAAA,CAAA,+KALtCV,EAaKC,EAAAa,EAAAX,CAAA,EAZJC,EAQMU,EAAAC,CAAA,EAPLX,EAMKW,EAAAC,CAAA,EALJZ,EAIAY,EAAAJ,CAAA,gBAGFR,EAEMU,EAAAG,CAAA,iBANQC,EAAA,GAAAV,KAAAA,EAAAC,EAAYC,EAAe,CAAA,CAAA,EAAA,KAAAS,EAAAC,EAAAZ,CAAA,EAD/BU,EAAA,GAAAL,KAAAA,EAAAJ,EAAYC,EAAe,CAAA,CAAA,gBAMnCQ,EAAA,GAAAP,KAAAA,EAAAD,KAAgB,UAAS,KAAAS,EAAAE,EAAAV,CAAA,2CAhBhBW,EAAAZ,KAAoB,OAAM,SACrCA,EAAmB,CAAA,EAAC,OAAS,EAAI,QAAU,eAGxCA,EAAe,CAAA,GAAAa,GAAAb,CAAA,wCAJlB,YAAU,2BACwC,KAAG,8GADrD,YAAU,kCACwC,KAAG,oKAHxDV,EAsBKC,EAAAuB,EAAArB,CAAA,EArBJC,EAGAoB,EAAAC,CAAA,qEAFaP,EAAA,GAAAI,KAAAA,EAAAZ,KAAoB,OAAM,KAAAS,EAAAO,EAAAJ,CAAA,cACrCZ,EAAmB,CAAA,EAAC,OAAS,EAAI,QAAU,SAAMS,EAAAQ,EAAAC,CAAA,EAG9ClB,EAAe,CAAA,gIAxDXD,EAAYoB,EAAA,CACZ,OAAAA,EAAK,SAAW,KAAQA,EAAK,MAAQ,IAAM,WA8B3CC,GAAuBC,EAAAA,KAC3BC,EAAgB,EACpBD,OAAAA,EAAM,QAASF,GAAA,CACdG,GAAiBvB,EAAYoB,CAA4B,IAG1D,SAAS,gBAAgB,MAAM,YAC9B,2BACCG,EAAgBD,EAAM,QAAQ,QAAQ,CAAC,EAAI,GAAA,EAGtCC,EAAgBD,EAAM,0BAvEnB,GAAA,CAAA,UAAAE,CAAA,EAAAC,EACA,CAAA,KAAAC,CAAA,EAAAD,EACA,CAAA,MAAAH,CAAA,EAAAG,EACA,CAAA,eAAAE,CAAA,EAAAF,EAEPG,EACAC,EAAW,GACXC,EACAC,EAEAC,EAA8CV,EAAM,IAAKF,IAExD,CAAA,GAAAA,EACH,SAAU,CAAA,UAINa,EAAWC,KAER,SAAAC,EAAeC,EAAkBC,EAAA,CAEzCC,EAAA,EAAAN,EAAsBA,EAAoB,IAAKZ,IAC1CA,EAAK,YAAcgB,IACtBhB,EAAK,UAAYiB,GAEXjB,KAQT,OAAAmB,GAAA,SAAA,IACCX,EAAA,MAAeD,EACV,IAAA,IAAA,GAAOD,CAAI,yCAAyCF,CAAS,EAAA,CAAA,EAG9DI,GAAU,KACH,MAAA,IAAA,MAAM,6BAA6B,EAG9CA,EAAO,UAA4B,eAAAY,EAAA,OAC5BC,EAAQ,KAAK,MAAMD,EAAM,IAAI,EAC9BX,GAAAS,EAAA,EAAUT,EAAW,EAAA,EACtBY,EAAM,MAAQ,QAEjBb,GAAA,MAAAA,EAAQ,QACRK,EAAS,MAAM,QAEfH,EAAsBW,CAAA,EACtBN,EAAeM,EAAM,UAAWA,EAAM,UAAU,MAInDC,GAAA,IAAA,EAEKd,GAAU,MAAQA,GAAU,OAAWA,EAAO,8LAiBhDP,GAAuBW,CAAmB,kBAE1CM,EAAA,EAAAP,EAAkBD,GAAuBE,EAAoB,CAAC,CAAA,gICrE3D,SAASW,IAGd,CACG,IAAAC,EACAC,EACG,MAAA,CACN,KACCC,EACAC,EAA6B,GAI5B,CACUF,EAAAE,EAGX,SAASC,GAA2B,CACpBJ,EAAA,SAAS,cAAc,OAAO,EAC7CA,EAAa,KAAO,OACpBA,EAAa,MAAM,QAAU,OAChBA,EAAA,aAAa,aAAc,aAAa,EACxCA,EAAA,aAAa,cAAe,aAAa,EACtD,MAAMK,EAAiB,MAAM,QAAQJ,EAAS,cAAc,EACzDA,EAAS,eAAe,KAAK,GAAG,EAChCA,EAAS,gBAAkB,OAE1BI,IACHL,EAAa,OAASK,GAGVL,EAAA,SAAWC,EAAS,OAAS,YAAc,GACpDA,EAAS,OAAS,cACrBD,EAAa,gBAAkB,GAClBA,EAAA,aAAa,YAAa,EAAE,EAC5BA,EAAA,aAAa,eAAgB,EAAE,GAE7CE,EAAK,YAAYF,CAAY,CAC9B,CAEmBI,IAEnB,SAASE,EAAYC,EAAoB,CACxCA,EAAE,eAAe,EACjBA,EAAE,gBAAgB,CACnB,CAEA,SAASC,EAAkBD,EAAoB,OAC9CA,EAAE,eAAe,EACjBA,EAAE,gBAAgB,GAClBE,EAAAR,EAAS,iBAAT,MAAAQ,EAAA,KAAAR,EAA0B,GAC3B,CAEA,SAASS,EAAkBH,EAAoB,OAC9CA,EAAE,eAAe,EACjBA,EAAE,gBAAgB,GAClBE,EAAAR,EAAS,iBAAT,MAAAQ,EAAA,KAAAR,EAA0B,GAC3B,CAEA,SAASU,EAAYJ,EAAoB,WAKpC,GAJJA,EAAE,eAAe,EACjBA,EAAE,gBAAgB,GAClBE,EAAAR,EAAS,iBAAT,MAAAQ,EAAA,KAAAR,EAA0B,IAEtB,GAACW,EAAAL,EAAE,eAAF,MAAAK,EAAgB,OAAO,OAC5B,MAAMlC,EAAQ,MAAM,KAAK6B,EAAE,aAAa,KAAK,EACzC7B,EAAM,OAAS,KAClBmC,EAAAZ,EAAS,WAAT,MAAAY,EAAA,KAAAZ,EAAoBvB,GAEtB,CAEA,SAASoC,GAAqB,CACxBb,EAAS,gBACbD,EAAa,MAAQ,GACrBA,EAAa,MAAM,EAErB,CAEA,SAASe,GAAiC,OACzC,GAAIf,EAAa,MAAO,CACvB,MAAMtB,EAAQ,MAAM,KAAKsB,EAAa,KAAK,EACvCtB,EAAM,OAAS,KAClB+B,EAAAR,EAAS,WAAT,MAAAQ,EAAA,KAAAR,EAAoBvB,GAEtB,CACD,CAGK,OAAAwB,EAAA,iBAAiB,OAAQI,CAAW,EACpCJ,EAAA,iBAAiB,YAAaI,CAAW,EACzCJ,EAAA,iBAAiB,UAAWI,CAAW,EACvCJ,EAAA,iBAAiB,WAAYI,CAAW,EACxCJ,EAAA,iBAAiB,YAAaM,CAAiB,EAC/CN,EAAA,iBAAiB,YAAaQ,CAAiB,EAC/CR,EAAA,iBAAiB,OAAQS,CAAW,EACpCT,EAAA,iBAAiB,QAASY,CAAY,EAC7Bd,EAAA,iBAAiB,SAAUe,CAAwB,EAE1D,CACN,OAAOC,EAAgC,CAC3Bf,EAAAe,EAEXhB,EAAa,OAAO,EACDI,IACNJ,EAAA,iBAAiB,SAAUe,CAAwB,CACjE,EACA,SAAU,CACJb,EAAA,oBAAoB,OAAQI,CAAW,EACvCJ,EAAA,oBAAoB,YAAaI,CAAW,EAC5CJ,EAAA,oBAAoB,UAAWI,CAAW,EAC1CJ,EAAA,oBAAoB,WAAYI,CAAW,EAC3CJ,EAAA,oBAAoB,YAAaM,CAAiB,EAClDN,EAAA,oBAAoB,YAAaQ,CAAiB,EAClDR,EAAA,oBAAoB,OAAQS,CAAW,EACvCT,EAAA,oBAAoB,QAASY,CAAY,EACjCd,EAAA,oBAAoB,SAAUe,CAAwB,EACnEf,EAAa,OAAO,CACrB,CAAA,CAEF,EACA,kBAAyB,CACpBA,IACHA,EAAa,MAAQ,GACrBA,EAAa,MAAM,EAErB,CAAA,CAEF,2QCiKY3C,EAAM,CAAA,EAAA,GAAQ,CAAC,EAQb4D,EAAAC,EAAA,aAAAC,EAAA9D,OAAc,+BAA+B,wLAhBxCA,EAAW,EAAA,CAAA,EACd+D,EAAAF,EAAA,SAAA7D,EAAA,EAAA,EACX,GACAA,EAAA,EAAA,EACQ,OAAAA,OAAW,SACjBA,MAAS,KACTA,EAAA,EAAA,EACD,MAAM,UAbXV,EA0BQC,EAAAsE,EAAApE,CAAA,oDAVN,eAAcuE,GACd,SAAQhE,EAAA,EAAA,EACR,eAAgBA,EAAiB,EAAA,EACjC,KAAMA,EAAU,CAAA,EAChB,cAAAA,EAAA,CAAA,2HANSA,EAAM,CAAA,EAAA,GAAQ,wBAQZ,CAAAiE,GAAAzD,EAAA,CAAA,EAAA,OAAAsD,KAAAA,EAAA9D,OAAc,yGANzB,eAAcgE,GACd,SAAQhE,EAAA,EAAA,EACR,eAAgBA,EAAiB,EAAA,EACjC,KAAMA,EAAU,CAAA,EAChB,cAAAA,EAAA,CAAA,4NAdgBA,EAAW,EAAA,CAAA,cACd+D,EAAAF,EAAA,SAAA7D,EAAA,EAAA,EACX,GACAA,EAAA,EAAA,EACQ,OAAAA,OAAW,SACjBA,MAAS,KACTA,EAAA,EAAA,EACD,MAAM,wGAjBLA,EAAM,CAAA,GAAAkE,GAAAlE,CAAA,4FAANA,EAAM,CAAA,kaAPDA,EAAM,CAAA,EAAA,GAAQ,CAAC,EAEb4D,EAAAC,EAAA,aAAAC,EAAA9D,OAAc,sBAAsB,gIAV/BA,EAAW,EAAA,CAAA,EACd+D,EAAAF,EAAA,SAAA7D,EAAA,EAAA,EACX,GACAA,EAAA,EAAA,EACQ,OAAAA,OAAW,SACjBA,MAAS,KACTA,EAAA,EAAA,EACD,MAAM,UAZXV,EAkBQC,EAAAsE,EAAApE,CAAA,yCAJGO,EAAe,EAAA,CAAA,wHADfA,EAAM,CAAA,EAAA,GAAQ,wBAEZ,CAAAiE,GAAAzD,EAAA,CAAA,EAAA,OAAAsD,KAAAA,EAAA9D,OAAc,4NAVTA,EAAW,EAAA,CAAA,cACd+D,EAAAF,EAAA,SAAA7D,EAAA,EAAA,EACX,GACAA,EAAA,EAAA,EACQ,OAAAA,OAAW,SACjBA,MAAS,KACTA,EAAA,EAAA,EACD,MAAM,6JASgCA,EAAS,EAAA,mMAATA,EAAS,EAAA,qMAtBhD,OAAAA,OAAa,YAAW,EAoBnBA,MAAaA,EAAa,EAAA,EAAA,2TAjK1B,SAAAmE,GACRC,EACAC,EACAC,EAAA,CAGE,GAAA,CAAAF,GACDA,IAAgB,KAChBA,IAAgB,UACf,MAAM,QAAQA,CAAW,GACzBA,EAAY,KAAMG,GAAWA,IAAW,KAAOA,IAAW,QAAQ,EAE5D,MAAA,GAEJ,IAAAC,YACOJ,GAAgB,SAC1BI,EAAcJ,EAAY,MAAM,GAAG,EAAE,IAAKK,GAAMA,EAAE,KAAA,CAAA,UACxC,MAAM,QAAQL,CAAW,EACnCI,EAAcJ,MAEP,OAAA,GAIP,OAAAI,EAAY,SAASH,CAAuB,GAC5CG,EAAY,KAAME,GAAA,CACV,KAAA,CAAAC,CAAQ,EAAID,EAAK,MAAM,GAAG,EAAE,IAAKD,GAAMA,EAAE,KAAA,CAAA,EAE/C,OAAAC,EAAK,SAAS,IAAI,GAAKJ,EAAmB,WAAWK,EAAW,GAAG,aA0JpDC,GAAcA,EAAWA,sDAvSpC,KAAA,CAAA,KAAAC,EAAM,iBAAkBC,CAAsB,EAAApC,GAAA,MAE3C,SAAAqC,EAAqC,IAAA,EAAAvD,GACrC,SAAAoD,EAAW,EAAA,EAAApD,GACX,cAAAwD,EAAgB,EAAA,EAAAxD,GAChB,OAAAyD,EAAS,EAAA,EAAAzD,GACT,KAAA0D,EAAO,EAAA,EAAA1D,GACP,WAAA2D,EAAkD,QAAA,EAAA3D,GAClD,cAAA4D,EAAgB,EAAA,EAAA5D,EAChB,CAAA,KAAAC,CAAA,EAAAD,GACA,OAAA6D,EAAS,EAAA,EAAA7D,GACT,OAAA8D,EAA0B,MAAA,EAAA9D,GAC1B,UAAA+D,EAAY,EAAA,EAAA/D,GACZ,cAAAgE,GAAgB,EAAA,EAAAhE,GAChB,cAAAiE,EAA+B,IAAA,EAAAjE,EAC/B,CAAA,OAAAkE,CAAA,EAAAlE,EACA,CAAA,eAAAE,EAAA,EAAAF,GACA,YAAAmE,GAAc,EAAA,EAAAnE,GACd,OAAAoE,GAAsC,MAAA,EAAApE,GACtC,WAAAqE,GAAiC,MAAA,EAAArE,EAC5B,SAAAsE,IAAA,CACfhB,IAEG,IAAAvD,EACAwE,EACAC,EACAC,GAA6C,KAE3C,MAAAC,GAAA,IAAA,WACM,UAAc,IAAA,CAClB,MAAAC,EAAY,UAAU,UAAU,YAAA,EAC/B,OAAAA,EAAU,QAAQ,QAAQ,EAAA,IAAUA,EAAU,QAAQ,MAAM,EAAI,GAEjE,MAAA,IAKFnE,EAAWC,KACXmE,GAAA,CAAkB,QAAS,QAAS,QAAS,OAAQ,MAAM,EAC3DC,EAAqB3B,GACtB4B,GAAO5B,EAAK,WAAW,GAAG,GAC7BuB,GAA6B,GACtBvB,GAEJ4B,GAAO5B,EAAK,SAAS,QAAQ,EACzB,IAEJA,EAAK,WAAW,GAAG,GAAKA,EAAK,SAAS,IAAI,EACtCA,EAEJ0B,GAAe,SAAS1B,CAAI,EACxBA,EAAO,KAER,IAAMA,EAcE,SAAA6B,IAAA,CACf,UAAU,UAAU,OAAO,KAAY,MAAAC,GAAA,SAC7BC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAAA,OAC3B/B,EAAO8B,EAAMC,CAAC,EAAE,MAAM,KAAMC,GAAMA,EAAE,WAAW,QAAQ,CAAA,EACzD,GAAAhC,EAAA,CACH8B,EAAMC,CAAC,EAAE,QAAQ/B,CAAI,EAAE,KAAY,MAAAiC,GAAA,CAC5B,MAAAxF,EAAA,IAAW,MACfwF,CAAI,EAAA,aACQjC,EAAK,QAAQ,SAAU,EAAE,CAAA,EAAA,EAEjC,MAAAkC,EAAA,CAAYzF,CAAI,CAAA,cAQX,SAAA0F,IAAA,CACf/B,mBAGcgC,GACdf,EAAAA,CAEM,MAAAgB,GAAA,EACN1E,EAAA,GAAAd,EAAY,KAAK,OAAS,EAAA,SAAS,EAAE,EAAE,UAAU,EAAG,EAAE,CAAA,MACtDgE,EAAY,EAAA,MAEL,MAAAyB,EAAA,MAAmBtB,EACxBK,EACAtE,EACAF,EACAkE,GAAiB,GAAA,EAElB,OAAAzD,EAAS,OAAQmD,IAAe,SAAW6B,GAAA,YAAAA,EAAa,GAAKA,CAAU,MACvEzB,EAAY,EAAA,EACLyB,GAAA,CAAA,CACC,OAAA9D,EAAA,CACR,OAAAlB,EAAS,QAAUkB,EAAY,OAAO,MACtCqC,EAAY,EAAA,qBAuCQqB,EACrBvF,EAAA,KAEKA,EAAM,cAGP,IAAA4F,EAAiB5F,EAAM,IACzB6F,GACI,IAAA,KAAA,CAAMA,CAAC,EAAGA,aAAa,KAAOA,EAAE,KAAO,OAAU,CAAA,KAAMA,EAAE,IAAA,CAAA,CAAA,SAG3DZ,GAAOL,KACVgB,EAASA,EAAO,OAAQ9F,GACnBgG,GAAchG,CAAI,EACd,IAERa,EACC,QACsB,sBAAAb,EAAK,IAAI,UAAU4D,CAAQ,WAAA,EAE3C,KAGJkC,EAAO,SAAW,OAKvB5E,EAAA,GAAA0D,EAAA,MAAkBqB,GAAcH,CAAM,CAAA,EACzB,MAAAH,GAAcf,CAAS,YAG5BoB,GAAchG,EAAA,QACjB4D,GAEiB,MAAM,QAAQA,CAAQ,EAAIA,GAAYA,CAAQ,GAE/C,KAAML,GAAA,CACpB,MAAA2C,EAAiBhB,EAAkB3B,CAAI,EAEzC,GAAA2C,EAAe,WAAW,GAAG,SACzBlG,EAAK,KAAK,YAAc,EAAA,SAASkG,EAAe,YAAA,CAAA,KAGpDA,IAAmB,IACf,MAAA,GAGJ,GAAAA,EAAe,SAAS,IAAI,EAAA,OACxB1C,CAAQ,EAAI0C,EAAe,MAAM,GAAG,SACpClG,EAAK,KAAK,WAAWwD,EAAW,GAAG,EAGpC,OAAAxD,EAAK,OAASkG,IApBA,kBAwBRC,GAAuBjG,EAAA,CAC/B,MAAAkG,EAAgBlG,EAAM,OAAQF,GAAA,CAC7B,MAAAqG,EAAiB,IAAMrG,EAAK,KAAK,MAAM,GAAG,EAAE,aAEjDqG,GACArD,GAAkB6B,EAAmBwB,EAAgBrG,EAAK,IAAI,IAK9DqG,GAAkB,MAAM,QAAQzC,CAAQ,EACrCA,EAAS,SAASyC,CAAc,EAChCA,IAAmBzC,GAEf,IAER/C,EAAS,kCAAmC+C,CAAQ,WAAA,EAC7C,SAGJO,GAAU,OACP,MAAAsB,EAAWW,CAAa,UAE1BpC,IAAe,SAAA,CAClBnD,EAAS,OAAQuF,EAAc,CAAC,CAAA,SAGjCvF,EAAS,OAAQuF,CAAa,kBAIVE,GAAqBvE,EAAA,OAErC,OADL0B,EAAW,EAAA,EACN,GAAAxB,EAAAF,EAAE,eAAF,MAAAE,EAAgB,OAAA,OACf,MAAAmE,EAAgB,MAAM,KAAKrE,EAAE,aAAa,KAAK,EAAE,OACtDiE,EAAA,KAGG7B,GAAU,OACP,MAAAsB,EAAWW,CAAa,UAE1BpC,IAAe,SAAA,CAClBnD,EAAS,OAAQuF,EAAc,CAAC,CAAA,SAGjCvF,EAAS,OAAQuF,CAAa,YA+CnBlG,GAAUiG,GAAuBjG,CAAK,mvBA/O5C0D,GAAY,UAClBiB,EAAoB,IAAA,SACHjB,GAAa,SAC9B1C,EAAA,GAAA2D,EAAoBK,EAAkBtB,CAAQ,CAAA,EACpCuB,GAAOvB,EAAS,SAAS,QAAQ,OAC3CiB,EAAoB,GAAA,OAEpBjB,EAAWA,EAAS,IAAIsB,CAAiB,CAAA,OACzCL,EAAoBjB,EAAS,KAAK,IAAI,CAAA,KA7BvC1C,EAAA,GAAGiE,EAAMJ,GAAA,CAAA"}