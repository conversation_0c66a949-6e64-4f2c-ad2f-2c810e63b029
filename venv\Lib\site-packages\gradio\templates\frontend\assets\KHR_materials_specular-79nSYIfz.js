import{ar as u,C as n,an as i,ao as m}from"./index-Dpxo-yl_.js";import{GLTFLoader as p}from"./glTFLoader-9Z3KGax5.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./bone-kZWM5-u7.js";import"./rawTexture-DmvUfjqF.js";import"./assetContainer-BRzQBugc.js";import"./objectModelMapping-BR4RdEzn.js";const t="KHR_materials_specular";class d{constructor(s){this.name=t,this.order=190,this._loader=s,this.enabled=this._loader.isExtensionUsed(t)}dispose(){this._loader=null}loadMaterialPropertiesAsync(s,e,r){return p.LoadExtensionAsync(s,e,this.name,(a,o)=>{const l=new Array;return l.push(this._loader.loadMaterialPropertiesAsync(s,e,r)),l.push(this._loadSpecularPropertiesAsync(a,o,r)),Promise.all(l).then(()=>{})})}_loadSpecularPropertiesAsync(s,e,r){if(!(r instanceof u))throw new Error(`${s}: Material type not supported`);const a=new Array;return e.specularFactor!==void 0&&(r.metallicF0Factor=e.specularFactor),e.specularColorFactor!==void 0&&(r.metallicReflectanceColor=n.FromArray(e.specularColorFactor)),e.specularTexture&&(e.specularTexture.nonColorData=!0,a.push(this._loader.loadTextureInfoAsync(`${s}/specularTexture`,e.specularTexture,o=>{o.name=`${r.name} (Specular)`,r.metallicReflectanceTexture=o,r.useOnlyMetallicFromMetallicReflectanceTexture=!0}))),e.specularColorTexture&&a.push(this._loader.loadTextureInfoAsync(`${s}/specularColorTexture`,e.specularColorTexture,o=>{o.name=`${r.name} (Specular Color)`,r.reflectanceTexture=o})),Promise.all(a).then(()=>{})}}i(t);m(t,!0,c=>new d(c));export{d as KHR_materials_specular};
//# sourceMappingURL=KHR_materials_specular-79nSYIfz.js.map
