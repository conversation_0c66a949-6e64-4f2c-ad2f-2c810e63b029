{"version": 3, "file": "flowGraphFlipFlopBlock-Dk2c7Wfx.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphFlipFlopBlock.js"], "sourcesContent": ["import { FlowGraphExecutionBlock } from \"../../../flowGraphExecutionBlock.js\";\nimport { RichTypeBoolean } from \"../../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\n/**\n * This block flip flops between two outputs.\n */\nexport class FlowGraphFlipFlopBlock extends FlowGraphExecutionBlock {\n    constructor(config) {\n        super(config);\n        this.onOn = this._registerSignalOutput(\"onOn\");\n        this.onOff = this._registerSignalOutput(\"onOff\");\n        this.value = this.registerDataOutput(\"value\", RichTypeBoolean);\n    }\n    _execute(context, _callingSignal) {\n        let value = context._getExecutionVariable(this, \"value\", typeof this.config?.startValue === \"boolean\" ? !this.config.startValue : false);\n        value = !value;\n        context._setExecutionVariable(this, \"value\", value);\n        this.value.setValue(value, context);\n        if (value) {\n            this.onOn._activateSignal(context);\n        }\n        else {\n            this.onOff._activateSignal(context);\n        }\n    }\n    /**\n     * @returns class name of the block.\n     */\n    getClassName() {\n        return \"FlowGraphFlipFlopBlock\" /* FlowGraphBlockNames.FlipFlop */;\n    }\n}\nRegisterClass(\"FlowGraphFlipFlopBlock\" /* FlowGraphBlockNames.FlipFlop */, FlowGraphFlipFlopBlock);\n//# sourceMappingURL=flowGraphFlipFlopBlock.js.map"], "names": ["FlowGraphFlipFlopBlock", "FlowGraphExecutionBlock", "config", "RichTypeBoolean", "context", "_callingSignal", "value", "RegisterClass"], "mappings": "gPAMO,MAAMA,UAA+BC,CAAwB,CAChE,YAAYC,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,KAAO,KAAK,sBAAsB,MAAM,EAC7C,KAAK,MAAQ,KAAK,sBAAsB,OAAO,EAC/C,KAAK,MAAQ,KAAK,mBAAmB,QAASC,CAAe,CAChE,CACD,SAASC,EAASC,EAAgB,CAC9B,IAAIC,EAAQF,EAAQ,sBAAsB,KAAM,QAAS,OAAO,KAAK,QAAQ,YAAe,UAAY,CAAC,KAAK,OAAO,WAAa,EAAK,EACvIE,EAAQ,CAACA,EACTF,EAAQ,sBAAsB,KAAM,QAASE,CAAK,EAClD,KAAK,MAAM,SAASA,EAAOF,CAAO,EAC9BE,EACA,KAAK,KAAK,gBAAgBF,CAAO,EAGjC,KAAK,MAAM,gBAAgBA,CAAO,CAEzC,CAID,cAAe,CACX,MAAO,wBACV,CACL,CACAG,EAAc,yBAA6DP,CAAsB", "x_google_ignoreList": [0]}