const __vite__fileDeps=["./index.Ck8lZcnN.js","./index.DbXlKk6X.js","./index.CzquxtZ2.js","./index.CADHlz5M.js","./index.HZFcoZEE.js","./index.CuPf8lR7.js","./frontmatter.DYpCVtLj.js","./yaml.DsCXHVTH.js","./index.DbWXTr9t.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{SvelteComponent as Zi,init as tn,safe_not_equal as en,element as Lo,claim_element as Ro,children as Io,detach as Me,attr as vs,insert_hydration as ni,append_hydration as Uc,noop as No,createEventDispatcher as Gc,onMount as Yc,binding_callbacks as ja,create_component as Pt,claim_component as Lt,mount_component as Rt,transition_in as st,transition_out as ft,destroy_component as It,onDestroy as Ka,space as Kn,claim_space as Un,afterUpdate as Jc,assign as Xc,empty as _o,get_spread_update as $c,get_spread_object as Qc,group_outros as Fo,check_outros as Vo,bind as Zc,add_flush_callback as tu}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{_ as nt}from"./preload-helper.D6kgxu3v.js";import{j as eu,I as Ua,H as Gn,J as Ho,B as iu,S as nu}from"./2.B2AoQPnG.js";import{D as Wo}from"./Download.CpfEFmFf.js";import{D as su}from"./DownloadLink.D1g3Q1HV.js";import{I as ru}from"./IconButtonWrapper.D5aGR59h.js";import{C as Ga}from"./Code.DWo5KduI.js";import{B as ou}from"./BlockLabel.BTSz9r5s.js";import{E as lu}from"./Empty.DwQ6nkN6.js";import au from"./Example.HHpueynA.js";let Zs=[],Ya=[];(()=>{let s="lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o".split(",").map(t=>t?parseInt(t,36):1);for(let t=0,e=0;t<s.length;t++)(t%2?Ya:Zs).push(e=e+s[t])})();function hu(s){if(s<768)return!1;for(let t=0,e=Zs.length;;){let i=t+e>>1;if(s<Zs[i])e=i;else if(s>=Ya[i])t=i+1;else return!0;if(t==e)return!1}}function zo(s){return s>=127462&&s<=127487}const qo=8205;function fu(s,t,e=!0,i=!0){return(e?Ja:cu)(s,t,i)}function Ja(s,t,e){if(t==s.length)return t;t&&Xa(s.charCodeAt(t))&&$a(s.charCodeAt(t-1))&&t--;let i=Ss(s,t);for(t+=jo(i);t<s.length;){let n=Ss(s,t);if(i==qo||n==qo||e&&hu(n))t+=jo(n),i=n;else if(zo(n)){let r=0,o=t-2;for(;o>=0&&zo(Ss(s,o));)r++,o-=2;if(r%2==0)break;t+=2}else break}return t}function cu(s,t,e){for(;t>0;){let i=Ja(s,t-2,e);if(i<t)return i;t--}return 0}function Ss(s,t){let e=s.charCodeAt(t);if(!$a(e)||t+1==s.length)return e;let i=s.charCodeAt(t+1);return Xa(i)?(e-55296<<10)+(i-56320)+65536:e}function Xa(s){return s>=56320&&s<57344}function $a(s){return s>=55296&&s<56320}function jo(s){return s<65536?1:2}class V{lineAt(t){if(t<0||t>this.length)throw new RangeError(`Invalid position ${t} in document of length ${this.length}`);return this.lineInner(t,!1,1,0)}line(t){if(t<1||t>this.lines)throw new RangeError(`Invalid line number ${t} in ${this.lines}-line document`);return this.lineInner(t,!0,1,0)}replace(t,e,i){[t,e]=ai(this,t,e);let n=[];return this.decompose(0,t,n,2),i.length&&i.decompose(0,i.length,n,3),this.decompose(e,this.length,n,1),re.from(n,this.length-(e-t)+i.length)}append(t){return this.replace(this.length,this.length,t)}slice(t,e=this.length){[t,e]=ai(this,t,e);let i=[];return this.decompose(t,e,i,0),re.from(i,e-t)}eq(t){if(t==this)return!0;if(t.length!=this.length||t.lines!=this.lines)return!1;let e=this.scanIdentical(t,1),i=this.length-this.scanIdentical(t,-1),n=new Pi(this),r=new Pi(t);for(let o=e,l=e;;){if(n.next(o),r.next(o),o=0,n.lineBreak!=r.lineBreak||n.done!=r.done||n.value!=r.value)return!1;if(l+=n.value.length,n.done||l>=i)return!0}}iter(t=1){return new Pi(this,t)}iterRange(t,e=this.length){return new Qa(this,t,e)}iterLines(t,e){let i;if(t==null)i=this.iter();else{e==null&&(e=this.lines+1);let n=this.line(t).from;i=this.iterRange(n,Math.max(n,e==this.lines+1?this.length:e<=1?0:this.line(e-1).to))}return new Za(i)}toString(){return this.sliceString(0)}toJSON(){let t=[];return this.flatten(t),t}constructor(){}static of(t){if(t.length==0)throw new RangeError("A document must have at least one line");return t.length==1&&!t[0]?V.empty:t.length<=32?new Q(t):re.from(Q.split(t,[]))}}class Q extends V{constructor(t,e=uu(t)){super(),this.text=t,this.length=e}get lines(){return this.text.length}get children(){return null}lineInner(t,e,i,n){for(let r=0;;r++){let o=this.text[r],l=n+o.length;if((e?i:l)>=t)return new du(n,l,i,o);n=l+1,i++}}decompose(t,e,i,n){let r=t<=0&&e>=this.length?this:new Q(Ko(this.text,t,e),Math.min(e,this.length)-Math.max(0,t));if(n&1){let o=i.pop(),l=Nn(r.text,o.text.slice(),0,r.length);if(l.length<=32)i.push(new Q(l,o.length+r.length));else{let a=l.length>>1;i.push(new Q(l.slice(0,a)),new Q(l.slice(a)))}}else i.push(r)}replace(t,e,i){if(!(i instanceof Q))return super.replace(t,e,i);[t,e]=ai(this,t,e);let n=Nn(this.text,Nn(i.text,Ko(this.text,0,t)),e),r=this.length+i.length-(e-t);return n.length<=32?new Q(n,r):re.from(Q.split(n,[]),r)}sliceString(t,e=this.length,i=`
`){[t,e]=ai(this,t,e);let n="";for(let r=0,o=0;r<=e&&o<this.text.length;o++){let l=this.text[o],a=r+l.length;r>t&&o&&(n+=i),t<a&&e>r&&(n+=l.slice(Math.max(0,t-r),e-r)),r=a+1}return n}flatten(t){for(let e of this.text)t.push(e)}scanIdentical(){return 0}static split(t,e){let i=[],n=-1;for(let r of t)i.push(r),n+=r.length+1,i.length==32&&(e.push(new Q(i,n)),i=[],n=-1);return n>-1&&e.push(new Q(i,n)),e}}class re extends V{constructor(t,e){super(),this.children=t,this.length=e,this.lines=0;for(let i of t)this.lines+=i.lines}lineInner(t,e,i,n){for(let r=0;;r++){let o=this.children[r],l=n+o.length,a=i+o.lines-1;if((e?a:l)>=t)return o.lineInner(t,e,i,n);n=l+1,i=a+1}}decompose(t,e,i,n){for(let r=0,o=0;o<=e&&r<this.children.length;r++){let l=this.children[r],a=o+l.length;if(t<=a&&e>=o){let h=n&((o<=t?1:0)|(a>=e?2:0));o>=t&&a<=e&&!h?i.push(l):l.decompose(t-o,e-o,i,h)}o=a+1}}replace(t,e,i){if([t,e]=ai(this,t,e),i.lines<this.lines)for(let n=0,r=0;n<this.children.length;n++){let o=this.children[n],l=r+o.length;if(t>=r&&e<=l){let a=o.replace(t-r,e-r,i),h=this.lines-o.lines+a.lines;if(a.lines<h>>4&&a.lines>h>>6){let f=this.children.slice();return f[n]=a,new re(f,this.length-(e-t)+i.length)}return super.replace(r,l,a)}r=l+1}return super.replace(t,e,i)}sliceString(t,e=this.length,i=`
`){[t,e]=ai(this,t,e);let n="";for(let r=0,o=0;r<this.children.length&&o<=e;r++){let l=this.children[r],a=o+l.length;o>t&&r&&(n+=i),t<a&&e>o&&(n+=l.sliceString(t-o,e-o,i)),o=a+1}return n}flatten(t){for(let e of this.children)e.flatten(t)}scanIdentical(t,e){if(!(t instanceof re))return 0;let i=0,[n,r,o,l]=e>0?[0,0,this.children.length,t.children.length]:[this.children.length-1,t.children.length-1,-1,-1];for(;;n+=e,r+=e){if(n==o||r==l)return i;let a=this.children[n],h=t.children[r];if(a!=h)return i+a.scanIdentical(h,e);i+=a.length+1}}static from(t,e=t.reduce((i,n)=>i+n.length+1,-1)){let i=0;for(let d of t)i+=d.lines;if(i<32){let d=[];for(let p of t)p.flatten(d);return new Q(d,e)}let n=Math.max(32,i>>5),r=n<<1,o=n>>1,l=[],a=0,h=-1,f=[];function c(d){let p;if(d.lines>r&&d instanceof re)for(let m of d.children)c(m);else d.lines>o&&(a>o||!a)?(u(),l.push(d)):d instanceof Q&&a&&(p=f[f.length-1])instanceof Q&&d.lines+p.lines<=32?(a+=d.lines,h+=d.length+1,f[f.length-1]=new Q(p.text.concat(d.text),p.length+1+d.length)):(a+d.lines>n&&u(),a+=d.lines,h+=d.length+1,f.push(d))}function u(){a!=0&&(l.push(f.length==1?f[0]:re.from(f,h)),h=-1,a=f.length=0)}for(let d of t)c(d);return u(),l.length==1?l[0]:new re(l,e)}}V.empty=new Q([""],0);function uu(s){let t=-1;for(let e of s)t+=e.length+1;return t}function Nn(s,t,e=0,i=1e9){for(let n=0,r=0,o=!0;r<s.length&&n<=i;r++){let l=s[r],a=n+l.length;a>=e&&(a>i&&(l=l.slice(0,i-n)),n<e&&(l=l.slice(e-n)),o?(t[t.length-1]+=l,o=!1):t.push(l)),n=a+1}return t}function Ko(s,t,e){return Nn(s,[""],t,e)}class Pi{constructor(t,e=1){this.dir=e,this.done=!1,this.lineBreak=!1,this.value="",this.nodes=[t],this.offsets=[e>0?1:(t instanceof Q?t.text.length:t.children.length)<<1]}nextInner(t,e){for(this.done=this.lineBreak=!1;;){let i=this.nodes.length-1,n=this.nodes[i],r=this.offsets[i],o=r>>1,l=n instanceof Q?n.text.length:n.children.length;if(o==(e>0?l:0)){if(i==0)return this.done=!0,this.value="",this;e>0&&this.offsets[i-1]++,this.nodes.pop(),this.offsets.pop()}else if((r&1)==(e>0?0:1)){if(this.offsets[i]+=e,t==0)return this.lineBreak=!0,this.value=`
`,this;t--}else if(n instanceof Q){let a=n.text[o+(e<0?-1:0)];if(this.offsets[i]+=e,a.length>Math.max(0,t))return this.value=t==0?a:e>0?a.slice(t):a.slice(0,a.length-t),this;t-=a.length}else{let a=n.children[o+(e<0?-1:0)];t>a.length?(t-=a.length,this.offsets[i]+=e):(e<0&&this.offsets[i]--,this.nodes.push(a),this.offsets.push(e>0?1:(a instanceof Q?a.text.length:a.children.length)<<1))}}}next(t=0){return t<0&&(this.nextInner(-t,-this.dir),t=this.value.length),this.nextInner(t,this.dir)}}class Qa{constructor(t,e,i){this.value="",this.done=!1,this.cursor=new Pi(t,e>i?-1:1),this.pos=e>i?t.length:0,this.from=Math.min(e,i),this.to=Math.max(e,i)}nextInner(t,e){if(e<0?this.pos<=this.from:this.pos>=this.to)return this.value="",this.done=!0,this;t+=Math.max(0,e<0?this.pos-this.to:this.from-this.pos);let i=e<0?this.pos-this.from:this.to-this.pos;t>i&&(t=i),i-=t;let{value:n}=this.cursor.next(t);return this.pos+=(n.length+t)*e,this.value=n.length<=i?n:e<0?n.slice(n.length-i):n.slice(0,i),this.done=!this.value,this}next(t=0){return t<0?t=Math.max(t,this.from-this.pos):t>0&&(t=Math.min(t,this.to-this.pos)),this.nextInner(t,this.cursor.dir)}get lineBreak(){return this.cursor.lineBreak&&this.value!=""}}class Za{constructor(t){this.inner=t,this.afterBreak=!0,this.value="",this.done=!1}next(t=0){let{done:e,lineBreak:i,value:n}=this.inner.next(t);return e&&this.afterBreak?(this.value="",this.afterBreak=!1):e?(this.done=!0,this.value=""):i?this.afterBreak?this.value="":(this.afterBreak=!0,this.next()):(this.value=n,this.afterBreak=!1),this}get lineBreak(){return!1}}typeof Symbol<"u"&&(V.prototype[Symbol.iterator]=function(){return this.iter()},Pi.prototype[Symbol.iterator]=Qa.prototype[Symbol.iterator]=Za.prototype[Symbol.iterator]=function(){return this});class du{constructor(t,e,i,n){this.from=t,this.to=e,this.number=i,this.text=n}get length(){return this.to-this.from}}function ai(s,t,e){return t=Math.max(0,Math.min(s.length,t)),[t,Math.max(t,Math.min(s.length,e))]}function wt(s,t,e=!0,i=!0){return fu(s,t,e,i)}function pu(s){return s>=56320&&s<57344}function mu(s){return s>=55296&&s<56320}function Bt(s,t){let e=s.charCodeAt(t);if(!mu(e)||t+1==s.length)return e;let i=s.charCodeAt(t+1);return pu(i)?(e-55296<<10)+(i-56320)+65536:e}function th(s){return s<=65535?String.fromCharCode(s):(s-=65536,String.fromCharCode((s>>10)+55296,(s&1023)+56320))}function pe(s){return s<65536?1:2}const tr=/\r\n?|\n/;var ct=function(s){return s[s.Simple=0]="Simple",s[s.TrackDel=1]="TrackDel",s[s.TrackBefore=2]="TrackBefore",s[s.TrackAfter=3]="TrackAfter",s}(ct||(ct={}));class fe{constructor(t){this.sections=t}get length(){let t=0;for(let e=0;e<this.sections.length;e+=2)t+=this.sections[e];return t}get newLength(){let t=0;for(let e=0;e<this.sections.length;e+=2){let i=this.sections[e+1];t+=i<0?this.sections[e]:i}return t}get empty(){return this.sections.length==0||this.sections.length==2&&this.sections[1]<0}iterGaps(t){for(let e=0,i=0,n=0;e<this.sections.length;){let r=this.sections[e++],o=this.sections[e++];o<0?(t(i,n,r),n+=r):n+=o,i+=r}}iterChangedRanges(t,e=!1){er(this,t,e)}get invertedDesc(){let t=[];for(let e=0;e<this.sections.length;){let i=this.sections[e++],n=this.sections[e++];n<0?t.push(i,n):t.push(n,i)}return new fe(t)}composeDesc(t){return this.empty?t:t.empty?this:eh(this,t)}mapDesc(t,e=!1){return t.empty?this:ir(this,t,e)}mapPos(t,e=-1,i=ct.Simple){let n=0,r=0;for(let o=0;o<this.sections.length;){let l=this.sections[o++],a=this.sections[o++],h=n+l;if(a<0){if(h>t)return r+(t-n);r+=l}else{if(i!=ct.Simple&&h>=t&&(i==ct.TrackDel&&n<t&&h>t||i==ct.TrackBefore&&n<t||i==ct.TrackAfter&&h>t))return null;if(h>t||h==t&&e<0&&!l)return t==n||e<0?r:r+a;r+=a}n=h}if(t>n)throw new RangeError(`Position ${t} is out of range for changeset of length ${n}`);return r}touchesRange(t,e=t){for(let i=0,n=0;i<this.sections.length&&n<=e;){let r=this.sections[i++],o=this.sections[i++],l=n+r;if(o>=0&&n<=e&&l>=t)return n<t&&l>e?"cover":!0;n=l}return!1}toString(){let t="";for(let e=0;e<this.sections.length;){let i=this.sections[e++],n=this.sections[e++];t+=(t?" ":"")+i+(n>=0?":"+n:"")}return t}toJSON(){return this.sections}static fromJSON(t){if(!Array.isArray(t)||t.length%2||t.some(e=>typeof e!="number"))throw new RangeError("Invalid JSON representation of ChangeDesc");return new fe(t)}static create(t){return new fe(t)}}class it extends fe{constructor(t,e){super(t),this.inserted=e}apply(t){if(this.length!=t.length)throw new RangeError("Applying change set to a document with the wrong length");return er(this,(e,i,n,r,o)=>t=t.replace(n,n+(i-e),o),!1),t}mapDesc(t,e=!1){return ir(this,t,e,!0)}invert(t){let e=this.sections.slice(),i=[];for(let n=0,r=0;n<e.length;n+=2){let o=e[n],l=e[n+1];if(l>=0){e[n]=l,e[n+1]=o;let a=n>>1;for(;i.length<a;)i.push(V.empty);i.push(o?t.slice(r,r+o):V.empty)}r+=o}return new it(e,i)}compose(t){return this.empty?t:t.empty?this:eh(this,t,!0)}map(t,e=!1){return t.empty?this:ir(this,t,e,!0)}iterChanges(t,e=!1){er(this,t,e)}get desc(){return fe.create(this.sections)}filter(t){let e=[],i=[],n=[],r=new Fi(this);t:for(let o=0,l=0;;){let a=o==t.length?1e9:t[o++];for(;l<a||l==a&&r.len==0;){if(r.done)break t;let f=Math.min(r.len,a-l);pt(n,f,-1);let c=r.ins==-1?-1:r.off==0?r.ins:0;pt(e,f,c),c>0&&Se(i,e,r.text),r.forward(f),l+=f}let h=t[o++];for(;l<h;){if(r.done)break t;let f=Math.min(r.len,h-l);pt(e,f,-1),pt(n,f,r.ins==-1?-1:r.off==0?r.ins:0),r.forward(f),l+=f}}return{changes:new it(e,i),filtered:fe.create(n)}}toJSON(){let t=[];for(let e=0;e<this.sections.length;e+=2){let i=this.sections[e],n=this.sections[e+1];n<0?t.push(i):n==0?t.push([i]):t.push([i].concat(this.inserted[e>>1].toJSON()))}return t}static of(t,e,i){let n=[],r=[],o=0,l=null;function a(f=!1){if(!f&&!n.length)return;o<e&&pt(n,e-o,-1);let c=new it(n,r);l=l?l.compose(c.map(l)):c,n=[],r=[],o=0}function h(f){if(Array.isArray(f))for(let c of f)h(c);else if(f instanceof it){if(f.length!=e)throw new RangeError(`Mismatched change set length (got ${f.length}, expected ${e})`);a(),l=l?l.compose(f.map(l)):f}else{let{from:c,to:u=c,insert:d}=f;if(c>u||c<0||u>e)throw new RangeError(`Invalid change range ${c} to ${u} (in doc of length ${e})`);let p=d?typeof d=="string"?V.of(d.split(i||tr)):d:V.empty,m=p.length;if(c==u&&m==0)return;c<o&&a(),c>o&&pt(n,c-o,-1),pt(n,u-c,m),Se(r,n,p),o=u}}return h(t),a(!l),l}static empty(t){return new it(t?[t,-1]:[],[])}static fromJSON(t){if(!Array.isArray(t))throw new RangeError("Invalid JSON representation of ChangeSet");let e=[],i=[];for(let n=0;n<t.length;n++){let r=t[n];if(typeof r=="number")e.push(r,-1);else{if(!Array.isArray(r)||typeof r[0]!="number"||r.some((o,l)=>l&&typeof o!="string"))throw new RangeError("Invalid JSON representation of ChangeSet");if(r.length==1)e.push(r[0],0);else{for(;i.length<n;)i.push(V.empty);i[n]=V.of(r.slice(1)),e.push(r[0],i[n].length)}}}return new it(e,i)}static createSet(t,e){return new it(t,e)}}function pt(s,t,e,i=!1){if(t==0&&e<=0)return;let n=s.length-2;n>=0&&e<=0&&e==s[n+1]?s[n]+=t:n>=0&&t==0&&s[n]==0?s[n+1]+=e:i?(s[n]+=t,s[n+1]+=e):s.push(t,e)}function Se(s,t,e){if(e.length==0)return;let i=t.length-2>>1;if(i<s.length)s[s.length-1]=s[s.length-1].append(e);else{for(;s.length<i;)s.push(V.empty);s.push(e)}}function er(s,t,e){let i=s.inserted;for(let n=0,r=0,o=0;o<s.sections.length;){let l=s.sections[o++],a=s.sections[o++];if(a<0)n+=l,r+=l;else{let h=n,f=r,c=V.empty;for(;h+=l,f+=a,a&&i&&(c=c.append(i[o-2>>1])),!(e||o==s.sections.length||s.sections[o+1]<0);)l=s.sections[o++],a=s.sections[o++];t(n,h,r,f,c),n=h,r=f}}}function ir(s,t,e,i=!1){let n=[],r=i?[]:null,o=new Fi(s),l=new Fi(t);for(let a=-1;;){if(o.done&&l.len||l.done&&o.len)throw new Error("Mismatched change set lengths");if(o.ins==-1&&l.ins==-1){let h=Math.min(o.len,l.len);pt(n,h,-1),o.forward(h),l.forward(h)}else if(l.ins>=0&&(o.ins<0||a==o.i||o.off==0&&(l.len<o.len||l.len==o.len&&!e))){let h=l.len;for(pt(n,l.ins,-1);h;){let f=Math.min(o.len,h);o.ins>=0&&a<o.i&&o.len<=f&&(pt(n,0,o.ins),r&&Se(r,n,o.text),a=o.i),o.forward(f),h-=f}l.next()}else if(o.ins>=0){let h=0,f=o.len;for(;f;)if(l.ins==-1){let c=Math.min(f,l.len);h+=c,f-=c,l.forward(c)}else if(l.ins==0&&l.len<f)f-=l.len,l.next();else break;pt(n,h,a<o.i?o.ins:0),r&&a<o.i&&Se(r,n,o.text),a=o.i,o.forward(o.len-f)}else{if(o.done&&l.done)return r?it.createSet(n,r):fe.create(n);throw new Error("Mismatched change set lengths")}}}function eh(s,t,e=!1){let i=[],n=e?[]:null,r=new Fi(s),o=new Fi(t);for(let l=!1;;){if(r.done&&o.done)return n?it.createSet(i,n):fe.create(i);if(r.ins==0)pt(i,r.len,0,l),r.next();else if(o.len==0&&!o.done)pt(i,0,o.ins,l),n&&Se(n,i,o.text),o.next();else{if(r.done||o.done)throw new Error("Mismatched change set lengths");{let a=Math.min(r.len2,o.len),h=i.length;if(r.ins==-1){let f=o.ins==-1?-1:o.off?0:o.ins;pt(i,a,f,l),n&&f&&Se(n,i,o.text)}else o.ins==-1?(pt(i,r.off?0:r.len,a,l),n&&Se(n,i,r.textBit(a))):(pt(i,r.off?0:r.len,o.off?0:o.ins,l),n&&!o.off&&Se(n,i,o.text));l=(r.ins>a||o.ins>=0&&o.len>a)&&(l||i.length>h),r.forward2(a),o.forward(a)}}}}class Fi{constructor(t){this.set=t,this.i=0,this.next()}next(){let{sections:t}=this.set;this.i<t.length?(this.len=t[this.i++],this.ins=t[this.i++]):(this.len=0,this.ins=-2),this.off=0}get done(){return this.ins==-2}get len2(){return this.ins<0?this.len:this.ins}get text(){let{inserted:t}=this.set,e=this.i-2>>1;return e>=t.length?V.empty:t[e]}textBit(t){let{inserted:e}=this.set,i=this.i-2>>1;return i>=e.length&&!t?V.empty:e[i].slice(this.off,t==null?void 0:this.off+t)}forward(t){t==this.len?this.next():(this.len-=t,this.off+=t)}forward2(t){this.ins==-1?this.forward(t):t==this.ins?this.next():(this.ins-=t,this.off+=t)}}class He{constructor(t,e,i){this.from=t,this.to=e,this.flags=i}get anchor(){return this.flags&32?this.to:this.from}get head(){return this.flags&32?this.from:this.to}get empty(){return this.from==this.to}get assoc(){return this.flags&8?-1:this.flags&16?1:0}get bidiLevel(){let t=this.flags&7;return t==7?null:t}get goalColumn(){let t=this.flags>>6;return t==16777215?void 0:t}map(t,e=-1){let i,n;return this.empty?i=n=t.mapPos(this.from,e):(i=t.mapPos(this.from,1),n=t.mapPos(this.to,-1)),i==this.from&&n==this.to?this:new He(i,n,this.flags)}extend(t,e=t){if(t<=this.anchor&&e>=this.anchor)return k.range(t,e);let i=Math.abs(t-this.anchor)>Math.abs(e-this.anchor)?t:e;return k.range(this.anchor,i)}eq(t,e=!1){return this.anchor==t.anchor&&this.head==t.head&&(!e||!this.empty||this.assoc==t.assoc)}toJSON(){return{anchor:this.anchor,head:this.head}}static fromJSON(t){if(!t||typeof t.anchor!="number"||typeof t.head!="number")throw new RangeError("Invalid JSON representation for SelectionRange");return k.range(t.anchor,t.head)}static create(t,e,i){return new He(t,e,i)}}class k{constructor(t,e){this.ranges=t,this.mainIndex=e}map(t,e=-1){return t.empty?this:k.create(this.ranges.map(i=>i.map(t,e)),this.mainIndex)}eq(t,e=!1){if(this.ranges.length!=t.ranges.length||this.mainIndex!=t.mainIndex)return!1;for(let i=0;i<this.ranges.length;i++)if(!this.ranges[i].eq(t.ranges[i],e))return!1;return!0}get main(){return this.ranges[this.mainIndex]}asSingle(){return this.ranges.length==1?this:new k([this.main],0)}addRange(t,e=!0){return k.create([t].concat(this.ranges),e?0:this.mainIndex+1)}replaceRange(t,e=this.mainIndex){let i=this.ranges.slice();return i[e]=t,k.create(i,this.mainIndex)}toJSON(){return{ranges:this.ranges.map(t=>t.toJSON()),main:this.mainIndex}}static fromJSON(t){if(!t||!Array.isArray(t.ranges)||typeof t.main!="number"||t.main>=t.ranges.length)throw new RangeError("Invalid JSON representation for EditorSelection");return new k(t.ranges.map(e=>He.fromJSON(e)),t.main)}static single(t,e=t){return new k([k.range(t,e)],0)}static create(t,e=0){if(t.length==0)throw new RangeError("A selection needs at least one range");for(let i=0,n=0;n<t.length;n++){let r=t[n];if(r.empty?r.from<=i:r.from<i)return k.normalized(t.slice(),e);i=r.to}return new k(t,e)}static cursor(t,e=0,i,n){return He.create(t,t,(e==0?0:e<0?8:16)|(i==null?7:Math.min(6,i))|(n??16777215)<<6)}static range(t,e,i,n){let r=(i??16777215)<<6|(n==null?7:Math.min(6,n));return e<t?He.create(e,t,48|r):He.create(t,e,(e>t?8:0)|r)}static normalized(t,e=0){let i=t[e];t.sort((n,r)=>n.from-r.from),e=t.indexOf(i);for(let n=1;n<t.length;n++){let r=t[n],o=t[n-1];if(r.empty?r.from<=o.to:r.from<o.to){let l=o.from,a=Math.max(r.to,o.to);n<=e&&e--,t.splice(--n,2,r.anchor>r.head?k.range(a,l):k.range(l,a))}}return new k(t,e)}}function ih(s,t){for(let e of s.ranges)if(e.to>t)throw new RangeError("Selection points outside of document")}let Jr=0;class B{constructor(t,e,i,n,r){this.combine=t,this.compareInput=e,this.compare=i,this.isStatic=n,this.id=Jr++,this.default=t([]),this.extensions=typeof r=="function"?r(this):r}get reader(){return this}static define(t={}){return new B(t.combine||(e=>e),t.compareInput||((e,i)=>e===i),t.compare||(t.combine?(e,i)=>e===i:Xr),!!t.static,t.enables)}of(t){return new _n([],this,0,t)}compute(t,e){if(this.isStatic)throw new Error("Can't compute a static facet");return new _n(t,this,1,e)}computeN(t,e){if(this.isStatic)throw new Error("Can't compute a static facet");return new _n(t,this,2,e)}from(t,e){return e||(e=i=>i),this.compute([t],i=>e(i.field(t)))}}function Xr(s,t){return s==t||s.length==t.length&&s.every((e,i)=>e===t[i])}class _n{constructor(t,e,i,n){this.dependencies=t,this.facet=e,this.type=i,this.value=n,this.id=Jr++}dynamicSlot(t){var e;let i=this.value,n=this.facet.compareInput,r=this.id,o=t[r]>>1,l=this.type==2,a=!1,h=!1,f=[];for(let c of this.dependencies)c=="doc"?a=!0:c=="selection"?h=!0:((e=t[c.id])!==null&&e!==void 0?e:1)&1||f.push(t[c.id]);return{create(c){return c.values[o]=i(c),1},update(c,u){if(a&&u.docChanged||h&&(u.docChanged||u.selection)||nr(c,f)){let d=i(c);if(l?!Uo(d,c.values[o],n):!n(d,c.values[o]))return c.values[o]=d,1}return 0},reconfigure:(c,u)=>{let d,p=u.config.address[r];if(p!=null){let m=Jn(u,p);if(this.dependencies.every(g=>g instanceof B?u.facet(g)===c.facet(g):g instanceof Tt?u.field(g,!1)==c.field(g,!1):!0)||(l?Uo(d=i(c),m,n):n(d=i(c),m)))return c.values[o]=m,0}else d=i(c);return c.values[o]=d,1}}}}function Uo(s,t,e){if(s.length!=t.length)return!1;for(let i=0;i<s.length;i++)if(!e(s[i],t[i]))return!1;return!0}function nr(s,t){let e=!1;for(let i of t)Li(s,i)&1&&(e=!0);return e}function gu(s,t,e){let i=e.map(a=>s[a.id]),n=e.map(a=>a.type),r=i.filter(a=>!(a&1)),o=s[t.id]>>1;function l(a){let h=[];for(let f=0;f<i.length;f++){let c=Jn(a,i[f]);if(n[f]==2)for(let u of c)h.push(u);else h.push(c)}return t.combine(h)}return{create(a){for(let h of i)Li(a,h);return a.values[o]=l(a),1},update(a,h){if(!nr(a,r))return 0;let f=l(a);return t.compare(f,a.values[o])?0:(a.values[o]=f,1)},reconfigure(a,h){let f=nr(a,i),c=h.config.facets[t.id],u=h.facet(t);if(c&&!f&&Xr(e,c))return a.values[o]=u,0;let d=l(a);return t.compare(d,u)?(a.values[o]=u,0):(a.values[o]=d,1)}}}const un=B.define({static:!0});class Tt{constructor(t,e,i,n,r){this.id=t,this.createF=e,this.updateF=i,this.compareF=n,this.spec=r,this.provides=void 0}static define(t){let e=new Tt(Jr++,t.create,t.update,t.compare||((i,n)=>i===n),t);return t.provide&&(e.provides=t.provide(e)),e}create(t){let e=t.facet(un).find(i=>i.field==this);return((e==null?void 0:e.create)||this.createF)(t)}slot(t){let e=t[this.id]>>1;return{create:i=>(i.values[e]=this.create(i),1),update:(i,n)=>{let r=i.values[e],o=this.updateF(r,n);return this.compareF(r,o)?0:(i.values[e]=o,1)},reconfigure:(i,n)=>{let r=i.facet(un),o=n.facet(un),l;return(l=r.find(a=>a.field==this))&&l!=o.find(a=>a.field==this)?(i.values[e]=l.create(i),1):n.config.address[this.id]!=null?(i.values[e]=n.field(this),0):(i.values[e]=this.create(i),1)}}}init(t){return[this,un.of({field:this,create:t})]}get extension(){return this}}const Fe={lowest:4,low:3,default:2,high:1,highest:0};function xi(s){return t=>new nh(t,s)}const Xe={highest:xi(Fe.highest),high:xi(Fe.high),default:xi(Fe.default),low:xi(Fe.low),lowest:xi(Fe.lowest)};class nh{constructor(t,e){this.inner=t,this.prec=e}}class fs{of(t){return new sr(this,t)}reconfigure(t){return fs.reconfigure.of({compartment:this,extension:t})}get(t){return t.config.compartments.get(this)}}class sr{constructor(t,e){this.compartment=t,this.inner=e}}class Yn{constructor(t,e,i,n,r,o){for(this.base=t,this.compartments=e,this.dynamicSlots=i,this.address=n,this.staticValues=r,this.facets=o,this.statusTemplate=[];this.statusTemplate.length<i.length;)this.statusTemplate.push(0)}staticFacet(t){let e=this.address[t.id];return e==null?t.default:this.staticValues[e>>1]}static resolve(t,e,i){let n=[],r=Object.create(null),o=new Map;for(let u of bu(t,e,o))u instanceof Tt?n.push(u):(r[u.facet.id]||(r[u.facet.id]=[])).push(u);let l=Object.create(null),a=[],h=[];for(let u of n)l[u.id]=h.length<<1,h.push(d=>u.slot(d));let f=i==null?void 0:i.config.facets;for(let u in r){let d=r[u],p=d[0].facet,m=f&&f[u]||[];if(d.every(g=>g.type==0))if(l[p.id]=a.length<<1|1,Xr(m,d))a.push(i.facet(p));else{let g=p.combine(d.map(y=>y.value));a.push(i&&p.compare(g,i.facet(p))?i.facet(p):g)}else{for(let g of d)g.type==0?(l[g.id]=a.length<<1|1,a.push(g.value)):(l[g.id]=h.length<<1,h.push(y=>g.dynamicSlot(y)));l[p.id]=h.length<<1,h.push(g=>gu(g,p,d))}}let c=h.map(u=>u(l));return new Yn(t,o,c,l,a,r)}}function bu(s,t,e){let i=[[],[],[],[],[]],n=new Map;function r(o,l){let a=n.get(o);if(a!=null){if(a<=l)return;let h=i[a].indexOf(o);h>-1&&i[a].splice(h,1),o instanceof sr&&e.delete(o.compartment)}if(n.set(o,l),Array.isArray(o))for(let h of o)r(h,l);else if(o instanceof sr){if(e.has(o.compartment))throw new RangeError("Duplicate use of compartment in extensions");let h=t.get(o.compartment)||o.inner;e.set(o.compartment,h),r(h,l)}else if(o instanceof nh)r(o.inner,o.prec);else if(o instanceof Tt)i[l].push(o),o.provides&&r(o.provides,l);else if(o instanceof _n)i[l].push(o),o.facet.extensions&&r(o.facet.extensions,Fe.default);else{let h=o.extension;if(!h)throw new Error(`Unrecognized extension value in extension set (${o}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);r(h,l)}}return r(s,Fe.default),i.reduce((o,l)=>o.concat(l))}function Li(s,t){if(t&1)return 2;let e=t>>1,i=s.status[e];if(i==4)throw new Error("Cyclic dependency between fields and/or facets");if(i&2)return i;s.status[e]=4;let n=s.computeSlot(s,s.config.dynamicSlots[e]);return s.status[e]=2|n}function Jn(s,t){return t&1?s.config.staticValues[t>>1]:s.values[t>>1]}const sh=B.define(),rr=B.define({combine:s=>s.some(t=>t),static:!0}),rh=B.define({combine:s=>s.length?s[0]:void 0,static:!0}),oh=B.define(),lh=B.define(),ah=B.define(),hh=B.define({combine:s=>s.length?s[0]:!1});class we{constructor(t,e){this.type=t,this.value=e}static define(){return new yu}}class yu{of(t){return new we(this,t)}}class wu{constructor(t){this.map=t}of(t){return new I(this,t)}}class I{constructor(t,e){this.type=t,this.value=e}map(t){let e=this.type.map(this.value,t);return e===void 0?void 0:e==this.value?this:new I(this.type,e)}is(t){return this.type==t}static define(t={}){return new wu(t.map||(e=>e))}static mapEffects(t,e){if(!t.length)return t;let i=[];for(let n of t){let r=n.map(e);r&&i.push(r)}return i}}I.reconfigure=I.define();I.appendConfig=I.define();class tt{constructor(t,e,i,n,r,o){this.startState=t,this.changes=e,this.selection=i,this.effects=n,this.annotations=r,this.scrollIntoView=o,this._doc=null,this._state=null,i&&ih(i,e.newLength),r.some(l=>l.type==tt.time)||(this.annotations=r.concat(tt.time.of(Date.now())))}static create(t,e,i,n,r,o){return new tt(t,e,i,n,r,o)}get newDoc(){return this._doc||(this._doc=this.changes.apply(this.startState.doc))}get newSelection(){return this.selection||this.startState.selection.map(this.changes)}get state(){return this._state||this.startState.applyTransaction(this),this._state}annotation(t){for(let e of this.annotations)if(e.type==t)return e.value}get docChanged(){return!this.changes.empty}get reconfigured(){return this.startState.config!=this.state.config}isUserEvent(t){let e=this.annotation(tt.userEvent);return!!(e&&(e==t||e.length>t.length&&e.slice(0,t.length)==t&&e[t.length]=="."))}}tt.time=we.define();tt.userEvent=we.define();tt.addToHistory=we.define();tt.remote=we.define();function xu(s,t){let e=[];for(let i=0,n=0;;){let r,o;if(i<s.length&&(n==t.length||t[n]>=s[i]))r=s[i++],o=s[i++];else if(n<t.length)r=t[n++],o=t[n++];else return e;!e.length||e[e.length-1]<r?e.push(r,o):e[e.length-1]<o&&(e[e.length-1]=o)}}function fh(s,t,e){var i;let n,r,o;return e?(n=t.changes,r=it.empty(t.changes.length),o=s.changes.compose(t.changes)):(n=t.changes.map(s.changes),r=s.changes.mapDesc(t.changes,!0),o=s.changes.compose(n)),{changes:o,selection:t.selection?t.selection.map(r):(i=s.selection)===null||i===void 0?void 0:i.map(n),effects:I.mapEffects(s.effects,n).concat(I.mapEffects(t.effects,r)),annotations:s.annotations.length?s.annotations.concat(t.annotations):t.annotations,scrollIntoView:s.scrollIntoView||t.scrollIntoView}}function or(s,t,e){let i=t.selection,n=si(t.annotations);return t.userEvent&&(n=n.concat(tt.userEvent.of(t.userEvent))),{changes:t.changes instanceof it?t.changes:it.of(t.changes||[],e,s.facet(rh)),selection:i&&(i instanceof k?i:k.single(i.anchor,i.head)),effects:si(t.effects),annotations:n,scrollIntoView:!!t.scrollIntoView}}function ch(s,t,e){let i=or(s,t.length?t[0]:{},s.doc.length);t.length&&t[0].filter===!1&&(e=!1);for(let r=1;r<t.length;r++){t[r].filter===!1&&(e=!1);let o=!!t[r].sequential;i=fh(i,or(s,t[r],o?i.changes.newLength:s.doc.length),o)}let n=tt.create(s,i.changes,i.selection,i.effects,i.annotations,i.scrollIntoView);return vu(e?ku(n):n)}function ku(s){let t=s.startState,e=!0;for(let n of t.facet(oh)){let r=n(s);if(r===!1){e=!1;break}Array.isArray(r)&&(e=e===!0?r:xu(e,r))}if(e!==!0){let n,r;if(e===!1)r=s.changes.invertedDesc,n=it.empty(t.doc.length);else{let o=s.changes.filter(e);n=o.changes,r=o.filtered.mapDesc(o.changes).invertedDesc}s=tt.create(t,n,s.selection&&s.selection.map(r),I.mapEffects(s.effects,r),s.annotations,s.scrollIntoView)}let i=t.facet(lh);for(let n=i.length-1;n>=0;n--){let r=i[n](s);r instanceof tt?s=r:Array.isArray(r)&&r.length==1&&r[0]instanceof tt?s=r[0]:s=ch(t,si(r),!1)}return s}function vu(s){let t=s.startState,e=t.facet(ah),i=s;for(let n=e.length-1;n>=0;n--){let r=e[n](s);r&&Object.keys(r).length&&(i=fh(i,or(t,r,s.changes.newLength),!0))}return i==s?s:tt.create(t,s.changes,s.selection,i.effects,i.annotations,i.scrollIntoView)}const Su=[];function si(s){return s==null?Su:Array.isArray(s)?s:[s]}var Vt=function(s){return s[s.Word=0]="Word",s[s.Space=1]="Space",s[s.Other=2]="Other",s}(Vt||(Vt={}));const Cu=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;let lr;try{lr=new RegExp("[\\p{Alphabetic}\\p{Number}_]","u")}catch{}function Au(s){if(lr)return lr.test(s);for(let t=0;t<s.length;t++){let e=s[t];if(/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||Cu.test(e)))return!0}return!1}function Mu(s){return t=>{if(!/\S/.test(t))return Vt.Space;if(Au(t))return Vt.Word;for(let e=0;e<s.length;e++)if(t.indexOf(s[e])>-1)return Vt.Word;return Vt.Other}}class H{constructor(t,e,i,n,r,o){this.config=t,this.doc=e,this.selection=i,this.values=n,this.status=t.statusTemplate.slice(),this.computeSlot=r,o&&(o._state=this);for(let l=0;l<this.config.dynamicSlots.length;l++)Li(this,l<<1);this.computeSlot=null}field(t,e=!0){let i=this.config.address[t.id];if(i==null){if(e)throw new RangeError("Field is not present in this state");return}return Li(this,i),Jn(this,i)}update(...t){return ch(this,t,!0)}applyTransaction(t){let e=this.config,{base:i,compartments:n}=e;for(let l of t.effects)l.is(fs.reconfigure)?(e&&(n=new Map,e.compartments.forEach((a,h)=>n.set(h,a)),e=null),n.set(l.value.compartment,l.value.extension)):l.is(I.reconfigure)?(e=null,i=l.value):l.is(I.appendConfig)&&(e=null,i=si(i).concat(l.value));let r;e?r=t.startState.values.slice():(e=Yn.resolve(i,n,this),r=new H(e,this.doc,this.selection,e.dynamicSlots.map(()=>null),(a,h)=>h.reconfigure(a,this),null).values);let o=t.startState.facet(rr)?t.newSelection:t.newSelection.asSingle();new H(e,t.newDoc,o,r,(l,a)=>a.update(l,t),t)}replaceSelection(t){return typeof t=="string"&&(t=this.toText(t)),this.changeByRange(e=>({changes:{from:e.from,to:e.to,insert:t},range:k.cursor(e.from+t.length)}))}changeByRange(t){let e=this.selection,i=t(e.ranges[0]),n=this.changes(i.changes),r=[i.range],o=si(i.effects);for(let l=1;l<e.ranges.length;l++){let a=t(e.ranges[l]),h=this.changes(a.changes),f=h.map(n);for(let u=0;u<l;u++)r[u]=r[u].map(f);let c=n.mapDesc(h,!0);r.push(a.range.map(c)),n=n.compose(f),o=I.mapEffects(o,f).concat(I.mapEffects(si(a.effects),c))}return{changes:n,selection:k.create(r,e.mainIndex),effects:o}}changes(t=[]){return t instanceof it?t:it.of(t,this.doc.length,this.facet(H.lineSeparator))}toText(t){return V.of(t.split(this.facet(H.lineSeparator)||tr))}sliceDoc(t=0,e=this.doc.length){return this.doc.sliceString(t,e,this.lineBreak)}facet(t){let e=this.config.address[t.id];return e==null?t.default:(Li(this,e),Jn(this,e))}toJSON(t){let e={doc:this.sliceDoc(),selection:this.selection.toJSON()};if(t)for(let i in t){let n=t[i];n instanceof Tt&&this.config.address[n.id]!=null&&(e[i]=n.spec.toJSON(this.field(t[i]),this))}return e}static fromJSON(t,e={},i){if(!t||typeof t.doc!="string")throw new RangeError("Invalid JSON representation for EditorState");let n=[];if(i){for(let r in i)if(Object.prototype.hasOwnProperty.call(t,r)){let o=i[r],l=t[r];n.push(o.init(a=>o.spec.fromJSON(l,a)))}}return H.create({doc:t.doc,selection:k.fromJSON(t.selection),extensions:e.extensions?n.concat([e.extensions]):n})}static create(t={}){let e=Yn.resolve(t.extensions||[],new Map),i=t.doc instanceof V?t.doc:V.of((t.doc||"").split(e.staticFacet(H.lineSeparator)||tr)),n=t.selection?t.selection instanceof k?t.selection:k.single(t.selection.anchor,t.selection.head):k.single(0);return ih(n,i.length),e.staticFacet(rr)||(n=n.asSingle()),new H(e,i,n,e.dynamicSlots.map(()=>null),(r,o)=>o.create(r),null)}get tabSize(){return this.facet(H.tabSize)}get lineBreak(){return this.facet(H.lineSeparator)||`
`}get readOnly(){return this.facet(hh)}phrase(t,...e){for(let i of this.facet(H.phrases))if(Object.prototype.hasOwnProperty.call(i,t)){t=i[t];break}return e.length&&(t=t.replace(/\$(\$|\d*)/g,(i,n)=>{if(n=="$")return"$";let r=+(n||1);return!r||r>e.length?i:e[r-1]})),t}languageDataAt(t,e,i=-1){let n=[];for(let r of this.facet(sh))for(let o of r(this,e,i))Object.prototype.hasOwnProperty.call(o,t)&&n.push(o[t]);return n}charCategorizer(t){return Mu(this.languageDataAt("wordChars",t).join(""))}wordAt(t){let{text:e,from:i,length:n}=this.doc.lineAt(t),r=this.charCategorizer(t),o=t-i,l=t-i;for(;o>0;){let a=wt(e,o,!1);if(r(e.slice(a,o))!=Vt.Word)break;o=a}for(;l<n;){let a=wt(e,l);if(r(e.slice(l,a))!=Vt.Word)break;l=a}return o==l?null:k.range(o+i,l+i)}}H.allowMultipleSelections=rr;H.tabSize=B.define({combine:s=>s.length?s[0]:4});H.lineSeparator=rh;H.readOnly=hh;H.phrases=B.define({compare(s,t){let e=Object.keys(s),i=Object.keys(t);return e.length==i.length&&e.every(n=>s[n]==t[n])}});H.languageData=sh;H.changeFilter=oh;H.transactionFilter=lh;H.transactionExtender=ah;fs.reconfigure=I.define();function $e(s,t,e={}){let i={};for(let n of s)for(let r of Object.keys(n)){let o=n[r],l=i[r];if(l===void 0)i[r]=o;else if(!(l===o||o===void 0))if(Object.hasOwnProperty.call(e,r))i[r]=e[r](l,o);else throw new Error("Config merge conflict for field "+r)}for(let n in t)i[n]===void 0&&(i[n]=t[n]);return i}class je{eq(t){return this==t}range(t,e=t){return ar.create(t,e,this)}}je.prototype.startSide=je.prototype.endSide=0;je.prototype.point=!1;je.prototype.mapMode=ct.TrackDel;let ar=class uh{constructor(t,e,i){this.from=t,this.to=e,this.value=i}static create(t,e,i){return new uh(t,e,i)}};function hr(s,t){return s.from-t.from||s.value.startSide-t.value.startSide}class $r{constructor(t,e,i,n){this.from=t,this.to=e,this.value=i,this.maxPoint=n}get length(){return this.to[this.to.length-1]}findIndex(t,e,i,n=0){let r=i?this.to:this.from;for(let o=n,l=r.length;;){if(o==l)return o;let a=o+l>>1,h=r[a]-t||(i?this.value[a].endSide:this.value[a].startSide)-e;if(a==o)return h>=0?o:l;h>=0?l=a:o=a+1}}between(t,e,i,n){for(let r=this.findIndex(e,-1e9,!0),o=this.findIndex(i,1e9,!1,r);r<o;r++)if(n(this.from[r]+t,this.to[r]+t,this.value[r])===!1)return!1}map(t,e){let i=[],n=[],r=[],o=-1,l=-1;for(let a=0;a<this.value.length;a++){let h=this.value[a],f=this.from[a]+t,c=this.to[a]+t,u,d;if(f==c){let p=e.mapPos(f,h.startSide,h.mapMode);if(p==null||(u=d=p,h.startSide!=h.endSide&&(d=e.mapPos(f,h.endSide),d<u)))continue}else if(u=e.mapPos(f,h.startSide),d=e.mapPos(c,h.endSide),u>d||u==d&&h.startSide>0&&h.endSide<=0)continue;(d-u||h.endSide-h.startSide)<0||(o<0&&(o=u),h.point&&(l=Math.max(l,d-u)),i.push(h),n.push(u-o),r.push(d-o))}return{mapped:i.length?new $r(n,r,i,l):null,pos:o}}}class W{constructor(t,e,i,n){this.chunkPos=t,this.chunk=e,this.nextLayer=i,this.maxPoint=n}static create(t,e,i,n){return new W(t,e,i,n)}get length(){let t=this.chunk.length-1;return t<0?0:Math.max(this.chunkEnd(t),this.nextLayer.length)}get size(){if(this.isEmpty)return 0;let t=this.nextLayer.size;for(let e of this.chunk)t+=e.value.length;return t}chunkEnd(t){return this.chunkPos[t]+this.chunk[t].length}update(t){let{add:e=[],sort:i=!1,filterFrom:n=0,filterTo:r=this.length}=t,o=t.filter;if(e.length==0&&!o)return this;if(i&&(e=e.slice().sort(hr)),this.isEmpty)return e.length?W.of(e):this;let l=new dh(this,null,-1).goto(0),a=0,h=[],f=new De;for(;l.value||a<e.length;)if(a<e.length&&(l.from-e[a].from||l.startSide-e[a].value.startSide)>=0){let c=e[a++];f.addInner(c.from,c.to,c.value)||h.push(c)}else l.rangeIndex==1&&l.chunkIndex<this.chunk.length&&(a==e.length||this.chunkEnd(l.chunkIndex)<e[a].from)&&(!o||n>this.chunkEnd(l.chunkIndex)||r<this.chunkPos[l.chunkIndex])&&f.addChunk(this.chunkPos[l.chunkIndex],this.chunk[l.chunkIndex])?l.nextChunk():((!o||n>l.to||r<l.from||o(l.from,l.to,l.value))&&(f.addInner(l.from,l.to,l.value)||h.push(ar.create(l.from,l.to,l.value))),l.next());return f.finishInner(this.nextLayer.isEmpty&&!h.length?W.empty:this.nextLayer.update({add:h,filter:o,filterFrom:n,filterTo:r}))}map(t){if(t.empty||this.isEmpty)return this;let e=[],i=[],n=-1;for(let o=0;o<this.chunk.length;o++){let l=this.chunkPos[o],a=this.chunk[o],h=t.touchesRange(l,l+a.length);if(h===!1)n=Math.max(n,a.maxPoint),e.push(a),i.push(t.mapPos(l));else if(h===!0){let{mapped:f,pos:c}=a.map(l,t);f&&(n=Math.max(n,f.maxPoint),e.push(f),i.push(c))}}let r=this.nextLayer.map(t);return e.length==0?r:new W(i,e,r||W.empty,n)}between(t,e,i){if(!this.isEmpty){for(let n=0;n<this.chunk.length;n++){let r=this.chunkPos[n],o=this.chunk[n];if(e>=r&&t<=r+o.length&&o.between(r,t-r,e-r,i)===!1)return}this.nextLayer.between(t,e,i)}}iter(t=0){return Vi.from([this]).goto(t)}get isEmpty(){return this.nextLayer==this}static iter(t,e=0){return Vi.from(t).goto(e)}static compare(t,e,i,n,r=-1){let o=t.filter(c=>c.maxPoint>0||!c.isEmpty&&c.maxPoint>=r),l=e.filter(c=>c.maxPoint>0||!c.isEmpty&&c.maxPoint>=r),a=Go(o,l,i),h=new ki(o,a,r),f=new ki(l,a,r);i.iterGaps((c,u,d)=>Yo(h,c,f,u,d,n)),i.empty&&i.length==0&&Yo(h,0,f,0,0,n)}static eq(t,e,i=0,n){n==null&&(n=999999999);let r=t.filter(f=>!f.isEmpty&&e.indexOf(f)<0),o=e.filter(f=>!f.isEmpty&&t.indexOf(f)<0);if(r.length!=o.length)return!1;if(!r.length)return!0;let l=Go(r,o),a=new ki(r,l,0).goto(i),h=new ki(o,l,0).goto(i);for(;;){if(a.to!=h.to||!fr(a.active,h.active)||a.point&&(!h.point||!a.point.eq(h.point)))return!1;if(a.to>n)return!0;a.next(),h.next()}}static spans(t,e,i,n,r=-1){let o=new ki(t,null,r).goto(e),l=e,a=o.openStart;for(;;){let h=Math.min(o.to,i);if(o.point){let f=o.activeForPoint(o.to),c=o.pointFrom<e?f.length+1:o.point.startSide<0?f.length:Math.min(f.length,a);n.point(l,h,o.point,f,c,o.pointRank),a=Math.min(o.openEnd(h),f.length)}else h>l&&(n.span(l,h,o.active,a),a=o.openEnd(h));if(o.to>i)return a+(o.point&&o.to>i?1:0);l=o.to,o.next()}}static of(t,e=!1){let i=new De;for(let n of t instanceof ar?[t]:e?Du(t):t)i.add(n.from,n.to,n.value);return i.finish()}static join(t){if(!t.length)return W.empty;let e=t[t.length-1];for(let i=t.length-2;i>=0;i--)for(let n=t[i];n!=W.empty;n=n.nextLayer)e=new W(n.chunkPos,n.chunk,e,Math.max(n.maxPoint,e.maxPoint));return e}}W.empty=new W([],[],null,-1);function Du(s){if(s.length>1)for(let t=s[0],e=1;e<s.length;e++){let i=s[e];if(hr(t,i)>0)return s.slice().sort(hr);t=i}return s}W.empty.nextLayer=W.empty;class De{finishChunk(t){this.chunks.push(new $r(this.from,this.to,this.value,this.maxPoint)),this.chunkPos.push(this.chunkStart),this.chunkStart=-1,this.setMaxPoint=Math.max(this.setMaxPoint,this.maxPoint),this.maxPoint=-1,t&&(this.from=[],this.to=[],this.value=[])}constructor(){this.chunks=[],this.chunkPos=[],this.chunkStart=-1,this.last=null,this.lastFrom=-1e9,this.lastTo=-1e9,this.from=[],this.to=[],this.value=[],this.maxPoint=-1,this.setMaxPoint=-1,this.nextLayer=null}add(t,e,i){this.addInner(t,e,i)||(this.nextLayer||(this.nextLayer=new De)).add(t,e,i)}addInner(t,e,i){let n=t-this.lastTo||i.startSide-this.last.endSide;if(n<=0&&(t-this.lastFrom||i.startSide-this.last.startSide)<0)throw new Error("Ranges must be added sorted by `from` position and `startSide`");return n<0?!1:(this.from.length==250&&this.finishChunk(!0),this.chunkStart<0&&(this.chunkStart=t),this.from.push(t-this.chunkStart),this.to.push(e-this.chunkStart),this.last=i,this.lastFrom=t,this.lastTo=e,this.value.push(i),i.point&&(this.maxPoint=Math.max(this.maxPoint,e-t)),!0)}addChunk(t,e){if((t-this.lastTo||e.value[0].startSide-this.last.endSide)<0)return!1;this.from.length&&this.finishChunk(!0),this.setMaxPoint=Math.max(this.setMaxPoint,e.maxPoint),this.chunks.push(e),this.chunkPos.push(t);let i=e.value.length-1;return this.last=e.value[i],this.lastFrom=e.from[i]+t,this.lastTo=e.to[i]+t,!0}finish(){return this.finishInner(W.empty)}finishInner(t){if(this.from.length&&this.finishChunk(!1),this.chunks.length==0)return t;let e=W.create(this.chunkPos,this.chunks,this.nextLayer?this.nextLayer.finishInner(t):t,this.setMaxPoint);return this.from=null,e}}function Go(s,t,e){let i=new Map;for(let r of s)for(let o=0;o<r.chunk.length;o++)r.chunk[o].maxPoint<=0&&i.set(r.chunk[o],r.chunkPos[o]);let n=new Set;for(let r of t)for(let o=0;o<r.chunk.length;o++){let l=i.get(r.chunk[o]);l!=null&&(e?e.mapPos(l):l)==r.chunkPos[o]&&!(e!=null&&e.touchesRange(l,l+r.chunk[o].length))&&n.add(r.chunk[o])}return n}class dh{constructor(t,e,i,n=0){this.layer=t,this.skip=e,this.minPoint=i,this.rank=n}get startSide(){return this.value?this.value.startSide:0}get endSide(){return this.value?this.value.endSide:0}goto(t,e=-1e9){return this.chunkIndex=this.rangeIndex=0,this.gotoInner(t,e,!1),this}gotoInner(t,e,i){for(;this.chunkIndex<this.layer.chunk.length;){let n=this.layer.chunk[this.chunkIndex];if(!(this.skip&&this.skip.has(n)||this.layer.chunkEnd(this.chunkIndex)<t||n.maxPoint<this.minPoint))break;this.chunkIndex++,i=!1}if(this.chunkIndex<this.layer.chunk.length){let n=this.layer.chunk[this.chunkIndex].findIndex(t-this.layer.chunkPos[this.chunkIndex],e,!0);(!i||this.rangeIndex<n)&&this.setRangeIndex(n)}this.next()}forward(t,e){(this.to-t||this.endSide-e)<0&&this.gotoInner(t,e,!0)}next(){for(;;)if(this.chunkIndex==this.layer.chunk.length){this.from=this.to=1e9,this.value=null;break}else{let t=this.layer.chunkPos[this.chunkIndex],e=this.layer.chunk[this.chunkIndex],i=t+e.from[this.rangeIndex];if(this.from=i,this.to=t+e.to[this.rangeIndex],this.value=e.value[this.rangeIndex],this.setRangeIndex(this.rangeIndex+1),this.minPoint<0||this.value.point&&this.to-this.from>=this.minPoint)break}}setRangeIndex(t){if(t==this.layer.chunk[this.chunkIndex].value.length){if(this.chunkIndex++,this.skip)for(;this.chunkIndex<this.layer.chunk.length&&this.skip.has(this.layer.chunk[this.chunkIndex]);)this.chunkIndex++;this.rangeIndex=0}else this.rangeIndex=t}nextChunk(){this.chunkIndex++,this.rangeIndex=0,this.next()}compare(t){return this.from-t.from||this.startSide-t.startSide||this.rank-t.rank||this.to-t.to||this.endSide-t.endSide}}class Vi{constructor(t){this.heap=t}static from(t,e=null,i=-1){let n=[];for(let r=0;r<t.length;r++)for(let o=t[r];!o.isEmpty;o=o.nextLayer)o.maxPoint>=i&&n.push(new dh(o,e,i,r));return n.length==1?n[0]:new Vi(n)}get startSide(){return this.value?this.value.startSide:0}goto(t,e=-1e9){for(let i of this.heap)i.goto(t,e);for(let i=this.heap.length>>1;i>=0;i--)Cs(this.heap,i);return this.next(),this}forward(t,e){for(let i of this.heap)i.forward(t,e);for(let i=this.heap.length>>1;i>=0;i--)Cs(this.heap,i);(this.to-t||this.value.endSide-e)<0&&this.next()}next(){if(this.heap.length==0)this.from=this.to=1e9,this.value=null,this.rank=-1;else{let t=this.heap[0];this.from=t.from,this.to=t.to,this.value=t.value,this.rank=t.rank,t.value&&t.next(),Cs(this.heap,0)}}}function Cs(s,t){for(let e=s[t];;){let i=(t<<1)+1;if(i>=s.length)break;let n=s[i];if(i+1<s.length&&n.compare(s[i+1])>=0&&(n=s[i+1],i++),e.compare(n)<0)break;s[i]=e,s[t]=n,t=i}}class ki{constructor(t,e,i){this.minPoint=i,this.active=[],this.activeTo=[],this.activeRank=[],this.minActive=-1,this.point=null,this.pointFrom=0,this.pointRank=0,this.to=-1e9,this.endSide=0,this.openStart=-1,this.cursor=Vi.from(t,e,i)}goto(t,e=-1e9){return this.cursor.goto(t,e),this.active.length=this.activeTo.length=this.activeRank.length=0,this.minActive=-1,this.to=t,this.endSide=e,this.openStart=-1,this.next(),this}forward(t,e){for(;this.minActive>-1&&(this.activeTo[this.minActive]-t||this.active[this.minActive].endSide-e)<0;)this.removeActive(this.minActive);this.cursor.forward(t,e)}removeActive(t){dn(this.active,t),dn(this.activeTo,t),dn(this.activeRank,t),this.minActive=Jo(this.active,this.activeTo)}addActive(t){let e=0,{value:i,to:n,rank:r}=this.cursor;for(;e<this.activeRank.length&&(r-this.activeRank[e]||n-this.activeTo[e])>0;)e++;pn(this.active,e,i),pn(this.activeTo,e,n),pn(this.activeRank,e,r),t&&pn(t,e,this.cursor.from),this.minActive=Jo(this.active,this.activeTo)}next(){let t=this.to,e=this.point;this.point=null;let i=this.openStart<0?[]:null;for(;;){let n=this.minActive;if(n>-1&&(this.activeTo[n]-this.cursor.from||this.active[n].endSide-this.cursor.startSide)<0){if(this.activeTo[n]>t){this.to=this.activeTo[n],this.endSide=this.active[n].endSide;break}this.removeActive(n),i&&dn(i,n)}else if(this.cursor.value)if(this.cursor.from>t){this.to=this.cursor.from,this.endSide=this.cursor.startSide;break}else{let r=this.cursor.value;if(!r.point)this.addActive(i),this.cursor.next();else if(e&&this.cursor.to==this.to&&this.cursor.from<this.cursor.to)this.cursor.next();else{this.point=r,this.pointFrom=this.cursor.from,this.pointRank=this.cursor.rank,this.to=this.cursor.to,this.endSide=r.endSide,this.cursor.next(),this.forward(this.to,this.endSide);break}}else{this.to=this.endSide=1e9;break}}if(i){this.openStart=0;for(let n=i.length-1;n>=0&&i[n]<t;n--)this.openStart++}}activeForPoint(t){if(!this.active.length)return this.active;let e=[];for(let i=this.active.length-1;i>=0&&!(this.activeRank[i]<this.pointRank);i--)(this.activeTo[i]>t||this.activeTo[i]==t&&this.active[i].endSide>=this.point.endSide)&&e.push(this.active[i]);return e.reverse()}openEnd(t){let e=0;for(let i=this.activeTo.length-1;i>=0&&this.activeTo[i]>t;i--)e++;return e}}function Yo(s,t,e,i,n,r){s.goto(t),e.goto(i);let o=i+n,l=i,a=i-t;for(;;){let h=s.to+a-e.to,f=h||s.endSide-e.endSide,c=f<0?s.to+a:e.to,u=Math.min(c,o);if(s.point||e.point?s.point&&e.point&&(s.point==e.point||s.point.eq(e.point))&&fr(s.activeForPoint(s.to),e.activeForPoint(e.to))||r.comparePoint(l,u,s.point,e.point):u>l&&!fr(s.active,e.active)&&r.compareRange(l,u,s.active,e.active),c>o)break;(h||s.openEnd!=e.openEnd)&&r.boundChange&&r.boundChange(c),l=c,f<=0&&s.next(),f>=0&&e.next()}}function fr(s,t){if(s.length!=t.length)return!1;for(let e=0;e<s.length;e++)if(s[e]!=t[e]&&!s[e].eq(t[e]))return!1;return!0}function dn(s,t){for(let e=t,i=s.length-1;e<i;e++)s[e]=s[e+1];s.pop()}function pn(s,t,e){for(let i=s.length-1;i>=t;i--)s[i+1]=s[i];s[t]=e}function Jo(s,t){let e=-1,i=1e9;for(let n=0;n<t.length;n++)(t[n]-i||s[n].endSide-s[e].endSide)<0&&(e=n,i=t[n]);return e}function gi(s,t,e=s.length){let i=0;for(let n=0;n<e&&n<s.length;)s.charCodeAt(n)==9?(i+=t-i%t,n++):(i++,n=wt(s,n));return i}function cr(s,t,e,i){for(let n=0,r=0;;){if(r>=t)return n;if(n==s.length)break;r+=s.charCodeAt(n)==9?e-r%e:1,n=wt(s,n)}return i===!0?-1:s.length}const ur="ͼ",Xo=typeof Symbol>"u"?"__"+ur:Symbol.for(ur),dr=typeof Symbol>"u"?"__styleSet"+Math.floor(Math.random()*1e8):Symbol("styleSet"),$o=typeof globalThis<"u"?globalThis:typeof window<"u"?window:{};class Te{constructor(t,e){this.rules=[];let{finish:i}=e||{};function n(o){return/^@/.test(o)?[o]:o.split(/,\s*/)}function r(o,l,a,h){let f=[],c=/^@(\w+)\b/.exec(o[0]),u=c&&c[1]=="keyframes";if(c&&l==null)return a.push(o[0]+";");for(let d in l){let p=l[d];if(/&/.test(d))r(d.split(/,\s*/).map(m=>o.map(g=>m.replace(/&/,g))).reduce((m,g)=>m.concat(g)),p,a);else if(p&&typeof p=="object"){if(!c)throw new RangeError("The value of a property ("+d+") should be a primitive value.");r(n(d),p,f,u)}else p!=null&&f.push(d.replace(/_.*/,"").replace(/[A-Z]/g,m=>"-"+m.toLowerCase())+": "+p+";")}(f.length||u)&&a.push((i&&!c&&!h?o.map(i):o).join(", ")+" {"+f.join(" ")+"}")}for(let o in t)r(n(o),t[o],this.rules)}getRules(){return this.rules.join(`
`)}static newName(){let t=$o[Xo]||1;return $o[Xo]=t+1,ur+t.toString(36)}static mount(t,e,i){let n=t[dr],r=i&&i.nonce;n?r&&n.setNonce(r):n=new Tu(t,r),n.mount(Array.isArray(e)?e:[e])}}let Qo=new Map;class Tu{constructor(t,e){let i=t.ownerDocument||t,n=i.defaultView;if(!t.head&&t.adoptedStyleSheets&&n.CSSStyleSheet){let r=Qo.get(i);if(r)return t.adoptedStyleSheets=[r.sheet,...t.adoptedStyleSheets],t[dr]=r;this.sheet=new n.CSSStyleSheet,t.adoptedStyleSheets=[this.sheet,...t.adoptedStyleSheets],Qo.set(i,this)}else{this.styleTag=i.createElement("style"),e&&this.styleTag.setAttribute("nonce",e);let r=t.head||t;r.insertBefore(this.styleTag,r.firstChild)}this.modules=[],t[dr]=this}mount(t){let e=this.sheet,i=0,n=0;for(let r=0;r<t.length;r++){let o=t[r],l=this.modules.indexOf(o);if(l<n&&l>-1&&(this.modules.splice(l,1),n--,l=-1),l==-1){if(this.modules.splice(n++,0,o),e)for(let a=0;a<o.rules.length;a++)e.insertRule(o.rules[a],i++)}else{for(;n<l;)i+=this.modules[n++].rules.length;i+=o.rules.length,n++}}if(!e){let r="";for(let o=0;o<this.modules.length;o++)r+=this.modules[o].getRules()+`
`;this.styleTag.textContent=r}}setNonce(t){this.styleTag&&this.styleTag.getAttribute("nonce")!=t&&this.styleTag.setAttribute("nonce",t)}}var Oe={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},Hi={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},Ou=typeof navigator<"u"&&/Mac/.test(navigator.platform),Bu=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent);for(var ht=0;ht<10;ht++)Oe[48+ht]=Oe[96+ht]=String(ht);for(var ht=1;ht<=24;ht++)Oe[ht+111]="F"+ht;for(var ht=65;ht<=90;ht++)Oe[ht]=String.fromCharCode(ht+32),Hi[ht]=String.fromCharCode(ht);for(var As in Oe)Hi.hasOwnProperty(As)||(Hi[As]=Oe[As]);function Eu(s){var t=Ou&&s.metaKey&&s.shiftKey&&!s.ctrlKey&&!s.altKey||Bu&&s.shiftKey&&s.key&&s.key.length==1||s.key=="Unidentified",e=!t&&s.key||(s.shiftKey?Hi:Oe)[s.keyCode]||s.key||"Unidentified";return e=="Esc"&&(e="Escape"),e=="Del"&&(e="Delete"),e=="Left"&&(e="ArrowLeft"),e=="Up"&&(e="ArrowUp"),e=="Right"&&(e="ArrowRight"),e=="Down"&&(e="ArrowDown"),e}function Wi(s){let t;return s.nodeType==11?t=s.getSelection?s:s.ownerDocument:t=s,t.getSelection()}function pr(s,t){return t?s==t||s.contains(t.nodeType!=1?t.parentNode:t):!1}function Fn(s,t){if(!t.anchorNode)return!1;try{return pr(s,t.anchorNode)}catch{return!1}}function hi(s){return s.nodeType==3?Ue(s,0,s.nodeValue.length).getClientRects():s.nodeType==1?s.getClientRects():[]}function Ri(s,t,e,i){return e?Zo(s,t,e,i,-1)||Zo(s,t,e,i,1):!1}function Ke(s){for(var t=0;;t++)if(s=s.previousSibling,!s)return t}function Xn(s){return s.nodeType==1&&/^(DIV|P|LI|UL|OL|BLOCKQUOTE|DD|DT|H\d|SECTION|PRE)$/.test(s.nodeName)}function Zo(s,t,e,i,n){for(;;){if(s==e&&t==i)return!0;if(t==(n<0?0:ce(s))){if(s.nodeName=="DIV")return!1;let r=s.parentNode;if(!r||r.nodeType!=1)return!1;t=Ke(s)+(n<0?0:1),s=r}else if(s.nodeType==1){if(s=s.childNodes[t+(n<0?-1:0)],s.nodeType==1&&s.contentEditable=="false")return!1;t=n<0?ce(s):0}else return!1}}function ce(s){return s.nodeType==3?s.nodeValue.length:s.childNodes.length}function nn(s,t){let e=t?s.left:s.right;return{left:e,right:e,top:s.top,bottom:s.bottom}}function Pu(s){let t=s.visualViewport;return t?{left:0,right:t.width,top:0,bottom:t.height}:{left:0,right:s.innerWidth,top:0,bottom:s.innerHeight}}function ph(s,t){let e=t.width/s.offsetWidth,i=t.height/s.offsetHeight;return(e>.995&&e<1.005||!isFinite(e)||Math.abs(t.width-s.offsetWidth)<1)&&(e=1),(i>.995&&i<1.005||!isFinite(i)||Math.abs(t.height-s.offsetHeight)<1)&&(i=1),{scaleX:e,scaleY:i}}function Lu(s,t,e,i,n,r,o,l){let a=s.ownerDocument,h=a.defaultView||window;for(let f=s,c=!1;f&&!c;)if(f.nodeType==1){let u,d=f==a.body,p=1,m=1;if(d)u=Pu(h);else{if(/^(fixed|sticky)$/.test(getComputedStyle(f).position)&&(c=!0),f.scrollHeight<=f.clientHeight&&f.scrollWidth<=f.clientWidth){f=f.assignedSlot||f.parentNode;continue}let w=f.getBoundingClientRect();({scaleX:p,scaleY:m}=ph(f,w)),u={left:w.left,right:w.left+f.clientWidth*p,top:w.top,bottom:w.top+f.clientHeight*m}}let g=0,y=0;if(n=="nearest")t.top<u.top?(y=t.top-(u.top+o),e>0&&t.bottom>u.bottom+y&&(y=t.bottom-u.bottom+o)):t.bottom>u.bottom&&(y=t.bottom-u.bottom+o,e<0&&t.top-y<u.top&&(y=t.top-(u.top+o)));else{let w=t.bottom-t.top,S=u.bottom-u.top;y=(n=="center"&&w<=S?t.top+w/2-S/2:n=="start"||n=="center"&&e<0?t.top-o:t.bottom-S+o)-u.top}if(i=="nearest"?t.left<u.left?(g=t.left-(u.left+r),e>0&&t.right>u.right+g&&(g=t.right-u.right+r)):t.right>u.right&&(g=t.right-u.right+r,e<0&&t.left<u.left+g&&(g=t.left-(u.left+r))):g=(i=="center"?t.left+(t.right-t.left)/2-(u.right-u.left)/2:i=="start"==l?t.left-r:t.right-(u.right-u.left)+r)-u.left,g||y)if(d)h.scrollBy(g,y);else{let w=0,S=0;if(y){let v=f.scrollTop;f.scrollTop+=y/m,S=(f.scrollTop-v)*m}if(g){let v=f.scrollLeft;f.scrollLeft+=g/p,w=(f.scrollLeft-v)*p}t={left:t.left-w,top:t.top-S,right:t.right-w,bottom:t.bottom-S},w&&Math.abs(w-g)<1&&(i="nearest"),S&&Math.abs(S-y)<1&&(n="nearest")}if(d)break;(t.top<u.top||t.bottom>u.bottom||t.left<u.left||t.right>u.right)&&(t={left:Math.max(t.left,u.left),right:Math.min(t.right,u.right),top:Math.max(t.top,u.top),bottom:Math.min(t.bottom,u.bottom)}),f=f.assignedSlot||f.parentNode}else if(f.nodeType==11)f=f.host;else break}function Ru(s){let t=s.ownerDocument,e,i;for(let n=s.parentNode;n&&!(n==t.body||e&&i);)if(n.nodeType==1)!i&&n.scrollHeight>n.clientHeight&&(i=n),!e&&n.scrollWidth>n.clientWidth&&(e=n),n=n.assignedSlot||n.parentNode;else if(n.nodeType==11)n=n.host;else break;return{x:e,y:i}}class Iu{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}eq(t){return this.anchorNode==t.anchorNode&&this.anchorOffset==t.anchorOffset&&this.focusNode==t.focusNode&&this.focusOffset==t.focusOffset}setRange(t){let{anchorNode:e,focusNode:i}=t;this.set(e,Math.min(t.anchorOffset,e?ce(e):0),i,Math.min(t.focusOffset,i?ce(i):0))}set(t,e,i,n){this.anchorNode=t,this.anchorOffset=e,this.focusNode=i,this.focusOffset=n}}let Qe=null;function mh(s){if(s.setActive)return s.setActive();if(Qe)return s.focus(Qe);let t=[];for(let e=s;e&&(t.push(e,e.scrollTop,e.scrollLeft),e!=e.ownerDocument);e=e.parentNode);if(s.focus(Qe==null?{get preventScroll(){return Qe={preventScroll:!0},!0}}:void 0),!Qe){Qe=!1;for(let e=0;e<t.length;){let i=t[e++],n=t[e++],r=t[e++];i.scrollTop!=n&&(i.scrollTop=n),i.scrollLeft!=r&&(i.scrollLeft=r)}}}let tl;function Ue(s,t,e=t){let i=tl||(tl=document.createRange());return i.setEnd(s,e),i.setStart(s,t),i}function ri(s,t,e,i){let n={key:t,code:t,keyCode:e,which:e,cancelable:!0};i&&({altKey:n.altKey,ctrlKey:n.ctrlKey,shiftKey:n.shiftKey,metaKey:n.metaKey}=i);let r=new KeyboardEvent("keydown",n);r.synthetic=!0,s.dispatchEvent(r);let o=new KeyboardEvent("keyup",n);return o.synthetic=!0,s.dispatchEvent(o),r.defaultPrevented||o.defaultPrevented}function Nu(s){for(;s;){if(s&&(s.nodeType==9||s.nodeType==11&&s.host))return s;s=s.assignedSlot||s.parentNode}return null}function gh(s){for(;s.attributes.length;)s.removeAttributeNode(s.attributes[0])}function _u(s,t){let e=t.focusNode,i=t.focusOffset;if(!e||t.anchorNode!=e||t.anchorOffset!=i)return!1;for(i=Math.min(i,ce(e));;)if(i){if(e.nodeType!=1)return!1;let n=e.childNodes[i-1];n.contentEditable=="false"?i--:(e=n,i=ce(e))}else{if(e==s)return!0;i=Ke(e),e=e.parentNode}}function bh(s){return s.scrollTop>Math.max(1,s.scrollHeight-s.clientHeight-4)}function yh(s,t){for(let e=s,i=t;;){if(e.nodeType==3&&i>0)return{node:e,offset:i};if(e.nodeType==1&&i>0){if(e.contentEditable=="false")return null;e=e.childNodes[i-1],i=ce(e)}else if(e.parentNode&&!Xn(e))i=Ke(e),e=e.parentNode;else return null}}function wh(s,t){for(let e=s,i=t;;){if(e.nodeType==3&&i<e.nodeValue.length)return{node:e,offset:i};if(e.nodeType==1&&i<e.childNodes.length){if(e.contentEditable=="false")return null;e=e.childNodes[i],i=0}else if(e.parentNode&&!Xn(e))i=Ke(e)+1,e=e.parentNode;else return null}}class mt{constructor(t,e,i=!0){this.node=t,this.offset=e,this.precise=i}static before(t,e){return new mt(t.parentNode,Ke(t),e)}static after(t,e){return new mt(t.parentNode,Ke(t)+1,e)}}const Qr=[];class U{constructor(){this.parent=null,this.dom=null,this.flags=2}get overrideDOMText(){return null}get posAtStart(){return this.parent?this.parent.posBefore(this):0}get posAtEnd(){return this.posAtStart+this.length}posBefore(t){let e=this.posAtStart;for(let i of this.children){if(i==t)return e;e+=i.length+i.breakAfter}throw new RangeError("Invalid child in posBefore")}posAfter(t){return this.posBefore(t)+t.length}sync(t,e){if(this.flags&2){let i=this.dom,n=null,r;for(let o of this.children){if(o.flags&7){if(!o.dom&&(r=n?n.nextSibling:i.firstChild)){let l=U.get(r);(!l||!l.parent&&l.canReuseDOM(o))&&o.reuseDOM(r)}o.sync(t,e),o.flags&=-8}if(r=n?n.nextSibling:i.firstChild,e&&!e.written&&e.node==i&&r!=o.dom&&(e.written=!0),o.dom.parentNode==i)for(;r&&r!=o.dom;)r=el(r);else i.insertBefore(o.dom,r);n=o.dom}for(r=n?n.nextSibling:i.firstChild,r&&e&&e.node==i&&(e.written=!0);r;)r=el(r)}else if(this.flags&1)for(let i of this.children)i.flags&7&&(i.sync(t,e),i.flags&=-8)}reuseDOM(t){}localPosFromDOM(t,e){let i;if(t==this.dom)i=this.dom.childNodes[e];else{let n=ce(t)==0?0:e==0?-1:1;for(;;){let r=t.parentNode;if(r==this.dom)break;n==0&&r.firstChild!=r.lastChild&&(t==r.firstChild?n=-1:n=1),t=r}n<0?i=t:i=t.nextSibling}if(i==this.dom.firstChild)return 0;for(;i&&!U.get(i);)i=i.nextSibling;if(!i)return this.length;for(let n=0,r=0;;n++){let o=this.children[n];if(o.dom==i)return r;r+=o.length+o.breakAfter}}domBoundsAround(t,e,i=0){let n=-1,r=-1,o=-1,l=-1;for(let a=0,h=i,f=i;a<this.children.length;a++){let c=this.children[a],u=h+c.length;if(h<t&&u>e)return c.domBoundsAround(t,e,h);if(u>=t&&n==-1&&(n=a,r=h),h>e&&c.dom.parentNode==this.dom){o=a,l=f;break}f=u,h=u+c.breakAfter}return{from:r,to:l<0?i+this.length:l,startDOM:(n?this.children[n-1].dom.nextSibling:null)||this.dom.firstChild,endDOM:o<this.children.length&&o>=0?this.children[o].dom:null}}markDirty(t=!1){this.flags|=2,this.markParentsDirty(t)}markParentsDirty(t){for(let e=this.parent;e;e=e.parent){if(t&&(e.flags|=2),e.flags&1)return;e.flags|=1,t=!1}}setParent(t){this.parent!=t&&(this.parent=t,this.flags&7&&this.markParentsDirty(!0))}setDOM(t){this.dom!=t&&(this.dom&&(this.dom.cmView=null),this.dom=t,t.cmView=this)}get rootView(){for(let t=this;;){let e=t.parent;if(!e)return t;t=e}}replaceChildren(t,e,i=Qr){this.markDirty();for(let n=t;n<e;n++){let r=this.children[n];r.parent==this&&i.indexOf(r)<0&&r.destroy()}i.length<250?this.children.splice(t,e-t,...i):this.children=[].concat(this.children.slice(0,t),i,this.children.slice(e));for(let n=0;n<i.length;n++)i[n].setParent(this)}ignoreMutation(t){return!1}ignoreEvent(t){return!1}childCursor(t=this.length){return new xh(this.children,t,this.children.length)}childPos(t,e=1){return this.childCursor().findPos(t,e)}toString(){let t=this.constructor.name.replace("View","");return t+(this.children.length?"("+this.children.join()+")":this.length?"["+(t=="Text"?this.text:this.length)+"]":"")+(this.breakAfter?"#":"")}static get(t){return t.cmView}get isEditable(){return!0}get isWidget(){return!1}get isHidden(){return!1}merge(t,e,i,n,r,o){return!1}become(t){return!1}canReuseDOM(t){return t.constructor==this.constructor&&!((this.flags|t.flags)&8)}getSide(){return 0}destroy(){for(let t of this.children)t.parent==this&&t.destroy();this.parent=null}}U.prototype.breakAfter=0;function el(s){let t=s.nextSibling;return s.parentNode.removeChild(s),t}class xh{constructor(t,e,i){this.children=t,this.pos=e,this.i=i,this.off=0}findPos(t,e=1){for(;;){if(t>this.pos||t==this.pos&&(e>0||this.i==0||this.children[this.i-1].breakAfter))return this.off=t-this.pos,this;let i=this.children[--this.i];this.pos-=i.length+i.breakAfter}}}function kh(s,t,e,i,n,r,o,l,a){let{children:h}=s,f=h.length?h[t]:null,c=r.length?r[r.length-1]:null,u=c?c.breakAfter:o;if(!(t==i&&f&&!o&&!u&&r.length<2&&f.merge(e,n,r.length?c:null,e==0,l,a))){if(i<h.length){let d=h[i];d&&(n<d.length||d.breakAfter&&(c!=null&&c.breakAfter))?(t==i&&(d=d.split(n),n=0),!u&&c&&d.merge(0,n,c,!0,0,a)?r[r.length-1]=d:((n||d.children.length&&!d.children[0].length)&&d.merge(0,n,null,!1,0,a),r.push(d))):d!=null&&d.breakAfter&&(c?c.breakAfter=1:o=1),i++}for(f&&(f.breakAfter=o,e>0&&(!o&&r.length&&f.merge(e,f.length,r[0],!1,l,0)?f.breakAfter=r.shift().breakAfter:(e<f.length||f.children.length&&f.children[f.children.length-1].length==0)&&f.merge(e,f.length,null,!1,l,0),t++));t<i&&r.length;)if(h[i-1].become(r[r.length-1]))i--,r.pop(),a=r.length?0:l;else if(h[t].become(r[0]))t++,r.shift(),l=r.length?0:a;else break;!r.length&&t&&i<h.length&&!h[t-1].breakAfter&&h[i].merge(0,0,h[t-1],!1,l,a)&&t--,(t<i||r.length)&&s.replaceChildren(t,i,r)}}function vh(s,t,e,i,n,r){let o=s.childCursor(),{i:l,off:a}=o.findPos(e,1),{i:h,off:f}=o.findPos(t,-1),c=t-e;for(let u of i)c+=u.length;s.length+=c,kh(s,h,f,l,a,i,0,n,r)}let Ct=typeof navigator<"u"?navigator:{userAgent:"",vendor:"",platform:""},mr=typeof document<"u"?document:{documentElement:{style:{}}};const gr=/Edge\/(\d+)/.exec(Ct.userAgent),Sh=/MSIE \d/.test(Ct.userAgent),br=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(Ct.userAgent),cs=!!(Sh||br||gr),il=!cs&&/gecko\/(\d+)/i.test(Ct.userAgent),Ms=!cs&&/Chrome\/(\d+)/.exec(Ct.userAgent),nl="webkitFontSmoothing"in mr.documentElement.style,Ch=!cs&&/Apple Computer/.test(Ct.vendor),sl=Ch&&(/Mobile\/\w+/.test(Ct.userAgent)||Ct.maxTouchPoints>2);var T={mac:sl||/Mac/.test(Ct.platform),windows:/Win/.test(Ct.platform),linux:/Linux|X11/.test(Ct.platform),ie:cs,ie_version:Sh?mr.documentMode||6:br?+br[1]:gr?+gr[1]:0,gecko:il,gecko_version:il?+(/Firefox\/(\d+)/.exec(Ct.userAgent)||[0,0])[1]:0,chrome:!!Ms,chrome_version:Ms?+Ms[1]:0,ios:sl,android:/Android\b/.test(Ct.userAgent),webkit:nl,safari:Ch,webkit_version:nl?+(/\bAppleWebKit\/(\d+)/.exec(Ct.userAgent)||[0,0])[1]:0,tabSize:mr.documentElement.style.tabSize!=null?"tab-size":"-moz-tab-size"};const Fu=256;class Xt extends U{constructor(t){super(),this.text=t}get length(){return this.text.length}createDOM(t){this.setDOM(t||document.createTextNode(this.text))}sync(t,e){this.dom||this.createDOM(),this.dom.nodeValue!=this.text&&(e&&e.node==this.dom&&(e.written=!0),this.dom.nodeValue=this.text)}reuseDOM(t){t.nodeType==3&&this.createDOM(t)}merge(t,e,i){return this.flags&8||i&&(!(i instanceof Xt)||this.length-(e-t)+i.length>Fu||i.flags&8)?!1:(this.text=this.text.slice(0,t)+(i?i.text:"")+this.text.slice(e),this.markDirty(),!0)}split(t){let e=new Xt(this.text.slice(t));return this.text=this.text.slice(0,t),this.markDirty(),e.flags|=this.flags&8,e}localPosFromDOM(t,e){return t==this.dom?e:e?this.text.length:0}domAtPos(t){return new mt(this.dom,t)}domBoundsAround(t,e,i){return{from:i,to:i+this.length,startDOM:this.dom,endDOM:this.dom.nextSibling}}coordsAt(t,e){return Vu(this.dom,t,e)}}class ye extends U{constructor(t,e=[],i=0){super(),this.mark=t,this.children=e,this.length=i;for(let n of e)n.setParent(this)}setAttrs(t){if(gh(t),this.mark.class&&(t.className=this.mark.class),this.mark.attrs)for(let e in this.mark.attrs)t.setAttribute(e,this.mark.attrs[e]);return t}canReuseDOM(t){return super.canReuseDOM(t)&&!((this.flags|t.flags)&8)}reuseDOM(t){t.nodeName==this.mark.tagName.toUpperCase()&&(this.setDOM(t),this.flags|=6)}sync(t,e){this.dom?this.flags&4&&this.setAttrs(this.dom):this.setDOM(this.setAttrs(document.createElement(this.mark.tagName))),super.sync(t,e)}merge(t,e,i,n,r,o){return i&&(!(i instanceof ye&&i.mark.eq(this.mark))||t&&r<=0||e<this.length&&o<=0)?!1:(vh(this,t,e,i?i.children.slice():[],r-1,o-1),this.markDirty(),!0)}split(t){let e=[],i=0,n=-1,r=0;for(let l of this.children){let a=i+l.length;a>t&&e.push(i<t?l.split(t-i):l),n<0&&i>=t&&(n=r),i=a,r++}let o=this.length-t;return this.length=t,n>-1&&(this.children.length=n,this.markDirty()),new ye(this.mark,e,o)}domAtPos(t){return Ah(this,t)}coordsAt(t,e){return Dh(this,t,e)}}function Vu(s,t,e){let i=s.nodeValue.length;t>i&&(t=i);let n=t,r=t,o=0;t==0&&e<0||t==i&&e>=0?T.chrome||T.gecko||(t?(n--,o=1):r<i&&(r++,o=-1)):e<0?n--:r<i&&r++;let l=Ue(s,n,r).getClientRects();if(!l.length)return null;let a=l[(o?o<0:e>=0)?0:l.length-1];return T.safari&&!o&&a.width==0&&(a=Array.prototype.find.call(l,h=>h.width)||a),o?nn(a,o<0):a||null}class Ce extends U{static create(t,e,i){return new Ce(t,e,i)}constructor(t,e,i){super(),this.widget=t,this.length=e,this.side=i,this.prevWidget=null}split(t){let e=Ce.create(this.widget,this.length-t,this.side);return this.length-=t,e}sync(t){(!this.dom||!this.widget.updateDOM(this.dom,t))&&(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"))}getSide(){return this.side}merge(t,e,i,n,r,o){return i&&(!(i instanceof Ce)||!this.widget.compare(i.widget)||t>0&&r<=0||e<this.length&&o<=0)?!1:(this.length=t+(i?i.length:0)+(this.length-e),!0)}become(t){return t instanceof Ce&&t.side==this.side&&this.widget.constructor==t.widget.constructor?(this.widget.compare(t.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,!0):!1}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get overrideDOMText(){if(this.length==0)return V.empty;let t=this;for(;t.parent;)t=t.parent;let{view:e}=t,i=e&&e.state.doc,n=this.posAtStart;return i?i.slice(n,n+this.length):V.empty}domAtPos(t){return(this.length?t==0:this.side>0)?mt.before(this.dom):mt.after(this.dom,t==this.length)}domBoundsAround(){return null}coordsAt(t,e){let i=this.widget.coordsAt(this.dom,t,e);if(i)return i;let n=this.dom.getClientRects(),r=null;if(!n.length)return null;let o=this.side?this.side<0:t>0;for(let l=o?n.length-1:0;r=n[l],!(t>0?l==0:l==n.length-1||r.top<r.bottom);l+=o?-1:1);return nn(r,!o)}get isEditable(){return!1}get isWidget(){return!0}get isHidden(){return this.widget.isHidden}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class fi extends U{constructor(t){super(),this.side=t}get length(){return 0}merge(){return!1}become(t){return t instanceof fi&&t.side==this.side}split(){return new fi(this.side)}sync(){if(!this.dom){let t=document.createElement("img");t.className="cm-widgetBuffer",t.setAttribute("aria-hidden","true"),this.setDOM(t)}}getSide(){return this.side}domAtPos(t){return this.side>0?mt.before(this.dom):mt.after(this.dom)}localPosFromDOM(){return 0}domBoundsAround(){return null}coordsAt(t){return this.dom.getBoundingClientRect()}get overrideDOMText(){return V.empty}get isHidden(){return!0}}Xt.prototype.children=Ce.prototype.children=fi.prototype.children=Qr;function Ah(s,t){let e=s.dom,{children:i}=s,n=0;for(let r=0;n<i.length;n++){let o=i[n],l=r+o.length;if(!(l==r&&o.getSide()<=0)){if(t>r&&t<l&&o.dom.parentNode==e)return o.domAtPos(t-r);if(t<=r)break;r=l}}for(let r=n;r>0;r--){let o=i[r-1];if(o.dom.parentNode==e)return o.domAtPos(o.length)}for(let r=n;r<i.length;r++){let o=i[r];if(o.dom.parentNode==e)return o.domAtPos(0)}return new mt(e,0)}function Mh(s,t,e){let i,{children:n}=s;e>0&&t instanceof ye&&n.length&&(i=n[n.length-1])instanceof ye&&i.mark.eq(t.mark)?Mh(i,t.children[0],e-1):(n.push(t),t.setParent(s)),s.length+=t.length}function Dh(s,t,e){let i=null,n=-1,r=null,o=-1;function l(h,f){for(let c=0,u=0;c<h.children.length&&u<=f;c++){let d=h.children[c],p=u+d.length;p>=f&&(d.children.length?l(d,f-u):(!r||r.isHidden&&e>0)&&(p>f||u==p&&d.getSide()>0)?(r=d,o=f-u):(u<f||u==p&&d.getSide()<0&&!d.isHidden)&&(i=d,n=f-u)),u=p}}l(s,t);let a=(e<0?i:r)||i||r;return a?a.coordsAt(Math.max(0,a==i?n:o),e):Hu(s)}function Hu(s){let t=s.dom.lastChild;if(!t)return s.dom.getBoundingClientRect();let e=hi(t);return e[e.length-1]||null}function yr(s,t){for(let e in s)e=="class"&&t.class?t.class+=" "+s.class:e=="style"&&t.style?t.style+=";"+s.style:t[e]=s[e];return t}const rl=Object.create(null);function $n(s,t,e){if(s==t)return!0;s||(s=rl),t||(t=rl);let i=Object.keys(s),n=Object.keys(t);if(i.length-(e&&i.indexOf(e)>-1?1:0)!=n.length-(e&&n.indexOf(e)>-1?1:0))return!1;for(let r of i)if(r!=e&&(n.indexOf(r)==-1||s[r]!==t[r]))return!1;return!0}function wr(s,t,e){let i=!1;if(t)for(let n in t)e&&n in e||(i=!0,n=="style"?s.style.cssText="":s.removeAttribute(n));if(e)for(let n in e)t&&t[n]==e[n]||(i=!0,n=="style"?s.style.cssText=e[n]:s.setAttribute(n,e[n]));return i}function Wu(s){let t=Object.create(null);for(let e=0;e<s.attributes.length;e++){let i=s.attributes[e];t[i.name]=i.value}return t}class ue{eq(t){return!1}updateDOM(t,e){return!1}compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}get estimatedHeight(){return-1}get lineBreaks(){return 0}ignoreEvent(t){return!0}coordsAt(t,e,i){return null}get isHidden(){return!1}get editable(){return!1}destroy(t){}}var xt=function(s){return s[s.Text=0]="Text",s[s.WidgetBefore=1]="WidgetBefore",s[s.WidgetAfter=2]="WidgetAfter",s[s.WidgetRange=3]="WidgetRange",s}(xt||(xt={}));class N extends je{constructor(t,e,i,n){super(),this.startSide=t,this.endSide=e,this.widget=i,this.spec=n}get heightRelevant(){return!1}static mark(t){return new sn(t)}static widget(t){let e=Math.max(-1e4,Math.min(1e4,t.side||0)),i=!!t.block;return e+=i&&!t.inlineOrder?e>0?3e8:-4e8:e>0?1e8:-1e8,new Be(t,e,e,i,t.widget||null,!1)}static replace(t){let e=!!t.block,i,n;if(t.isBlockGap)i=-5e8,n=4e8;else{let{start:r,end:o}=Th(t,e);i=(r?e?-3e8:-1:5e8)-1,n=(o?e?2e8:1:-6e8)+1}return new Be(t,i,n,e,t.widget||null,!0)}static line(t){return new rn(t)}static set(t,e=!1){return W.of(t,e)}hasHeight(){return this.widget?this.widget.estimatedHeight>-1:!1}}N.none=W.empty;class sn extends N{constructor(t){let{start:e,end:i}=Th(t);super(e?-1:5e8,i?1:-6e8,null,t),this.tagName=t.tagName||"span",this.class=t.class||"",this.attrs=t.attributes||null}eq(t){var e,i;return this==t||t instanceof sn&&this.tagName==t.tagName&&(this.class||((e=this.attrs)===null||e===void 0?void 0:e.class))==(t.class||((i=t.attrs)===null||i===void 0?void 0:i.class))&&$n(this.attrs,t.attrs,"class")}range(t,e=t){if(t>=e)throw new RangeError("Mark decorations may not be empty");return super.range(t,e)}}sn.prototype.point=!1;class rn extends N{constructor(t){super(-2e8,-2e8,null,t)}eq(t){return t instanceof rn&&this.spec.class==t.spec.class&&$n(this.spec.attributes,t.spec.attributes)}range(t,e=t){if(e!=t)throw new RangeError("Line decoration ranges must be zero-length");return super.range(t,e)}}rn.prototype.mapMode=ct.TrackBefore;rn.prototype.point=!0;class Be extends N{constructor(t,e,i,n,r,o){super(e,i,r,t),this.block=n,this.isReplace=o,this.mapMode=n?e<=0?ct.TrackBefore:ct.TrackAfter:ct.TrackDel}get type(){return this.startSide!=this.endSide?xt.WidgetRange:this.startSide<=0?xt.WidgetBefore:xt.WidgetAfter}get heightRelevant(){return this.block||!!this.widget&&(this.widget.estimatedHeight>=5||this.widget.lineBreaks>0)}eq(t){return t instanceof Be&&zu(this.widget,t.widget)&&this.block==t.block&&this.startSide==t.startSide&&this.endSide==t.endSide}range(t,e=t){if(this.isReplace&&(t>e||t==e&&this.startSide>0&&this.endSide<=0))throw new RangeError("Invalid range for replacement decoration");if(!this.isReplace&&e!=t)throw new RangeError("Widget decorations can only have zero-length ranges");return super.range(t,e)}}Be.prototype.point=!0;function Th(s,t=!1){let{inclusiveStart:e,inclusiveEnd:i}=s;return e==null&&(e=s.inclusive),i==null&&(i=s.inclusive),{start:e??t,end:i??t}}function zu(s,t){return s==t||!!(s&&t&&s.compare(t))}function Vn(s,t,e,i=0){let n=e.length-1;n>=0&&e[n]+i>=s?e[n]=Math.max(e[n],t):e.push(s,t)}class Z extends U{constructor(){super(...arguments),this.children=[],this.length=0,this.prevAttrs=void 0,this.attrs=null,this.breakAfter=0}merge(t,e,i,n,r,o){if(i){if(!(i instanceof Z))return!1;this.dom||i.transferDOM(this)}return n&&this.setDeco(i?i.attrs:null),vh(this,t,e,i?i.children.slice():[],r,o),!0}split(t){let e=new Z;if(e.breakAfter=this.breakAfter,this.length==0)return e;let{i,off:n}=this.childPos(t);n&&(e.append(this.children[i].split(n),0),this.children[i].merge(n,this.children[i].length,null,!1,0,0),i++);for(let r=i;r<this.children.length;r++)e.append(this.children[r],0);for(;i>0&&this.children[i-1].length==0;)this.children[--i].destroy();return this.children.length=i,this.markDirty(),this.length=t,e}transferDOM(t){this.dom&&(this.markDirty(),t.setDOM(this.dom),t.prevAttrs=this.prevAttrs===void 0?this.attrs:this.prevAttrs,this.prevAttrs=void 0,this.dom=null)}setDeco(t){$n(this.attrs,t)||(this.dom&&(this.prevAttrs=this.attrs,this.markDirty()),this.attrs=t)}append(t,e){Mh(this,t,e)}addLineDeco(t){let e=t.spec.attributes,i=t.spec.class;e&&(this.attrs=yr(e,this.attrs||{})),i&&(this.attrs=yr({class:i},this.attrs||{}))}domAtPos(t){return Ah(this,t)}reuseDOM(t){t.nodeName=="DIV"&&(this.setDOM(t),this.flags|=6)}sync(t,e){var i;this.dom?this.flags&4&&(gh(this.dom),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0):(this.setDOM(document.createElement("div")),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0),this.prevAttrs!==void 0&&(wr(this.dom,this.prevAttrs,this.attrs),this.dom.classList.add("cm-line"),this.prevAttrs=void 0),super.sync(t,e);let n=this.dom.lastChild;for(;n&&U.get(n)instanceof ye;)n=n.lastChild;if(!n||!this.length||n.nodeName!="BR"&&((i=U.get(n))===null||i===void 0?void 0:i.isEditable)==!1&&(!T.ios||!this.children.some(r=>r instanceof Xt))){let r=document.createElement("BR");r.cmIgnore=!0,this.dom.appendChild(r)}}measureTextSize(){if(this.children.length==0||this.length>20)return null;let t=0,e;for(let i of this.children){if(!(i instanceof Xt)||/[^ -~]/.test(i.text))return null;let n=hi(i.dom);if(n.length!=1)return null;t+=n[0].width,e=n[0].height}return t?{lineHeight:this.dom.getBoundingClientRect().height,charWidth:t/this.length,textHeight:e}:null}coordsAt(t,e){let i=Dh(this,t,e);if(!this.children.length&&i&&this.parent){let{heightOracle:n}=this.parent.view.viewState,r=i.bottom-i.top;if(Math.abs(r-n.lineHeight)<2&&n.textHeight<r){let o=(r-n.textHeight)/2;return{top:i.top+o,bottom:i.bottom-o,left:i.left,right:i.left}}}return i}become(t){return t instanceof Z&&this.children.length==0&&t.children.length==0&&$n(this.attrs,t.attrs)&&this.breakAfter==t.breakAfter}covers(){return!0}static find(t,e){for(let i=0,n=0;i<t.children.length;i++){let r=t.children[i],o=n+r.length;if(o>=e){if(r instanceof Z)return r;if(o>e)break}n=o+r.breakAfter}return null}}class ge extends U{constructor(t,e,i){super(),this.widget=t,this.length=e,this.deco=i,this.breakAfter=0,this.prevWidget=null}merge(t,e,i,n,r,o){return i&&(!(i instanceof ge)||!this.widget.compare(i.widget)||t>0&&r<=0||e<this.length&&o<=0)?!1:(this.length=t+(i?i.length:0)+(this.length-e),!0)}domAtPos(t){return t==0?mt.before(this.dom):mt.after(this.dom,t==this.length)}split(t){let e=this.length-t;this.length=t;let i=new ge(this.widget,e,this.deco);return i.breakAfter=this.breakAfter,i}get children(){return Qr}sync(t){(!this.dom||!this.widget.updateDOM(this.dom,t))&&(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"))}get overrideDOMText(){return this.parent?this.parent.view.state.doc.slice(this.posAtStart,this.posAtEnd):V.empty}domBoundsAround(){return null}become(t){return t instanceof ge&&t.widget.constructor==this.widget.constructor?(t.widget.compare(this.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,this.deco=t.deco,this.breakAfter=t.breakAfter,!0):!1}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get isEditable(){return!1}get isWidget(){return!0}coordsAt(t,e){let i=this.widget.coordsAt(this.dom,t,e);return i||(this.widget instanceof xr?null:nn(this.dom.getBoundingClientRect(),this.length?t==0:e<=0))}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}covers(t){let{startSide:e,endSide:i}=this.deco;return e==i?!1:t<0?e<0:i>0}}class xr extends ue{constructor(t){super(),this.height=t}toDOM(){let t=document.createElement("div");return t.className="cm-gap",this.updateDOM(t),t}eq(t){return t.height==this.height}updateDOM(t){return t.style.height=this.height+"px",!0}get editable(){return!0}get estimatedHeight(){return this.height}ignoreEvent(){return!1}}class Ii{constructor(t,e,i,n){this.doc=t,this.pos=e,this.end=i,this.disallowBlockEffectsFor=n,this.content=[],this.curLine=null,this.breakAtStart=0,this.pendingBuffer=0,this.bufferMarks=[],this.atCursorPos=!0,this.openStart=-1,this.openEnd=-1,this.text="",this.textOff=0,this.cursor=t.iter(),this.skip=e}posCovered(){if(this.content.length==0)return!this.breakAtStart&&this.doc.lineAt(this.pos).from!=this.pos;let t=this.content[this.content.length-1];return!(t.breakAfter||t instanceof ge&&t.deco.endSide<0)}getLine(){return this.curLine||(this.content.push(this.curLine=new Z),this.atCursorPos=!0),this.curLine}flushBuffer(t=this.bufferMarks){this.pendingBuffer&&(this.curLine.append(mn(new fi(-1),t),t.length),this.pendingBuffer=0)}addBlockWidget(t){this.flushBuffer(),this.curLine=null,this.content.push(t)}finish(t){this.pendingBuffer&&t<=this.bufferMarks.length?this.flushBuffer():this.pendingBuffer=0,!this.posCovered()&&!(t&&this.content.length&&this.content[this.content.length-1]instanceof ge)&&this.getLine()}buildText(t,e,i){for(;t>0;){if(this.textOff==this.text.length){let{value:r,lineBreak:o,done:l}=this.cursor.next(this.skip);if(this.skip=0,l)throw new Error("Ran out of text content when drawing inline views");if(o){this.posCovered()||this.getLine(),this.content.length?this.content[this.content.length-1].breakAfter=1:this.breakAtStart=1,this.flushBuffer(),this.curLine=null,this.atCursorPos=!0,t--;continue}else this.text=r,this.textOff=0}let n=Math.min(this.text.length-this.textOff,t,512);this.flushBuffer(e.slice(e.length-i)),this.getLine().append(mn(new Xt(this.text.slice(this.textOff,this.textOff+n)),e),i),this.atCursorPos=!0,this.textOff+=n,t-=n,i=0}}span(t,e,i,n){this.buildText(e-t,i,n),this.pos=e,this.openStart<0&&(this.openStart=n)}point(t,e,i,n,r,o){if(this.disallowBlockEffectsFor[o]&&i instanceof Be){if(i.block)throw new RangeError("Block decorations may not be specified via plugins");if(e>this.doc.lineAt(this.pos).to)throw new RangeError("Decorations that replace line breaks may not be specified via plugins")}let l=e-t;if(i instanceof Be)if(i.block)i.startSide>0&&!this.posCovered()&&this.getLine(),this.addBlockWidget(new ge(i.widget||ci.block,l,i));else{let a=Ce.create(i.widget||ci.inline,l,l?0:i.startSide),h=this.atCursorPos&&!a.isEditable&&r<=n.length&&(t<e||i.startSide>0),f=!a.isEditable&&(t<e||r>n.length||i.startSide<=0),c=this.getLine();this.pendingBuffer==2&&!h&&!a.isEditable&&(this.pendingBuffer=0),this.flushBuffer(n),h&&(c.append(mn(new fi(1),n),r),r=n.length+Math.max(0,r-n.length)),c.append(mn(a,n),r),this.atCursorPos=f,this.pendingBuffer=f?t<e||r>n.length?1:2:0,this.pendingBuffer&&(this.bufferMarks=n.slice())}else this.doc.lineAt(this.pos).from==this.pos&&this.getLine().addLineDeco(i);l&&(this.textOff+l<=this.text.length?this.textOff+=l:(this.skip+=l-(this.text.length-this.textOff),this.text="",this.textOff=0),this.pos=e),this.openStart<0&&(this.openStart=r)}static build(t,e,i,n,r){let o=new Ii(t,e,i,r);return o.openEnd=W.spans(n,e,i,o),o.openStart<0&&(o.openStart=o.openEnd),o.finish(o.openEnd),o}}function mn(s,t){for(let e of t)s=new ye(e,[s],s.length);return s}class ci extends ue{constructor(t){super(),this.tag=t}eq(t){return t.tag==this.tag}toDOM(){return document.createElement(this.tag)}updateDOM(t){return t.nodeName.toLowerCase()==this.tag}get isHidden(){return!0}}ci.inline=new ci("span");ci.block=new ci("div");var J=function(s){return s[s.LTR=0]="LTR",s[s.RTL=1]="RTL",s}(J||(J={}));const Ge=J.LTR,Zr=J.RTL;function Oh(s){let t=[];for(let e=0;e<s.length;e++)t.push(1<<+s[e]);return t}const qu=Oh("88888888888888888888888888888888888666888888787833333333337888888000000000000000000000000008888880000000000000000000000000088888888888888888888888888888888888887866668888088888663380888308888800000000000000000000000800000000000000000000000000000008"),ju=Oh("4444448826627288999999999992222222222222222222222222222222222222222222222229999999999999999999994444444444644222822222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222999999949999999229989999223333333333"),kr=Object.create(null),te=[];for(let s of["()","[]","{}"]){let t=s.charCodeAt(0),e=s.charCodeAt(1);kr[t]=e,kr[e]=-t}function Bh(s){return s<=247?qu[s]:1424<=s&&s<=1524?2:1536<=s&&s<=1785?ju[s-1536]:1774<=s&&s<=2220?4:8192<=s&&s<=8204?256:64336<=s&&s<=65023?4:1}const Ku=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\ufb50-\ufdff]/;class Ae{get dir(){return this.level%2?Zr:Ge}constructor(t,e,i){this.from=t,this.to=e,this.level=i}side(t,e){return this.dir==e==t?this.to:this.from}forward(t,e){return t==(this.dir==e)}static find(t,e,i,n){let r=-1;for(let o=0;o<t.length;o++){let l=t[o];if(l.from<=e&&l.to>=e){if(l.level==i)return o;(r<0||(n!=0?n<0?l.from<e:l.to>e:t[r].level>l.level))&&(r=o)}}if(r<0)throw new RangeError("Index out of range");return r}}function Eh(s,t){if(s.length!=t.length)return!1;for(let e=0;e<s.length;e++){let i=s[e],n=t[e];if(i.from!=n.from||i.to!=n.to||i.direction!=n.direction||!Eh(i.inner,n.inner))return!1}return!0}const K=[];function Uu(s,t,e,i,n){for(let r=0;r<=i.length;r++){let o=r?i[r-1].to:t,l=r<i.length?i[r].from:e,a=r?256:n;for(let h=o,f=a,c=a;h<l;h++){let u=Bh(s.charCodeAt(h));u==512?u=f:u==8&&c==4&&(u=16),K[h]=u==4?2:u,u&7&&(c=u),f=u}for(let h=o,f=a,c=a;h<l;h++){let u=K[h];if(u==128)h<l-1&&f==K[h+1]&&f&24?u=K[h]=f:K[h]=256;else if(u==64){let d=h+1;for(;d<l&&K[d]==64;)d++;let p=h&&f==8||d<e&&K[d]==8?c==1?1:8:256;for(let m=h;m<d;m++)K[m]=p;h=d-1}else u==8&&c==1&&(K[h]=1);f=u,u&7&&(c=u)}}}function Gu(s,t,e,i,n){let r=n==1?2:1;for(let o=0,l=0,a=0;o<=i.length;o++){let h=o?i[o-1].to:t,f=o<i.length?i[o].from:e;for(let c=h,u,d,p;c<f;c++)if(d=kr[u=s.charCodeAt(c)])if(d<0){for(let m=l-3;m>=0;m-=3)if(te[m+1]==-d){let g=te[m+2],y=g&2?n:g&4?g&1?r:n:0;y&&(K[c]=K[te[m]]=y),l=m;break}}else{if(te.length==189)break;te[l++]=c,te[l++]=u,te[l++]=a}else if((p=K[c])==2||p==1){let m=p==n;a=m?0:1;for(let g=l-3;g>=0;g-=3){let y=te[g+2];if(y&2)break;if(m)te[g+2]|=2;else{if(y&4)break;te[g+2]|=4}}}}}function Yu(s,t,e,i){for(let n=0,r=i;n<=e.length;n++){let o=n?e[n-1].to:s,l=n<e.length?e[n].from:t;for(let a=o;a<l;){let h=K[a];if(h==256){let f=a+1;for(;;)if(f==l){if(n==e.length)break;f=e[n++].to,l=n<e.length?e[n].from:t}else if(K[f]==256)f++;else break;let c=r==1,u=(f<t?K[f]:i)==1,d=c==u?c?1:2:i;for(let p=f,m=n,g=m?e[m-1].to:s;p>a;)p==g&&(p=e[--m].from,g=m?e[m-1].to:s),K[--p]=d;a=f}else r=h,a++}}}function vr(s,t,e,i,n,r,o){let l=i%2?2:1;if(i%2==n%2)for(let a=t,h=0;a<e;){let f=!0,c=!1;if(h==r.length||a<r[h].from){let m=K[a];m!=l&&(f=!1,c=m==16)}let u=!f&&l==1?[]:null,d=f?i:i+1,p=a;t:for(;;)if(h<r.length&&p==r[h].from){if(c)break t;let m=r[h];if(!f)for(let g=m.to,y=h+1;;){if(g==e)break t;if(y<r.length&&r[y].from==g)g=r[y++].to;else{if(K[g]==l)break t;break}}if(h++,u)u.push(m);else{m.from>a&&o.push(new Ae(a,m.from,d));let g=m.direction==Ge!=!(d%2);Sr(s,g?i+1:i,n,m.inner,m.from,m.to,o),a=m.to}p=m.to}else{if(p==e||(f?K[p]!=l:K[p]==l))break;p++}u?vr(s,a,p,i+1,n,u,o):a<p&&o.push(new Ae(a,p,d)),a=p}else for(let a=e,h=r.length;a>t;){let f=!0,c=!1;if(!h||a>r[h-1].to){let m=K[a-1];m!=l&&(f=!1,c=m==16)}let u=!f&&l==1?[]:null,d=f?i:i+1,p=a;t:for(;;)if(h&&p==r[h-1].to){if(c)break t;let m=r[--h];if(!f)for(let g=m.from,y=h;;){if(g==t)break t;if(y&&r[y-1].to==g)g=r[--y].from;else{if(K[g-1]==l)break t;break}}if(u)u.push(m);else{m.to<a&&o.push(new Ae(m.to,a,d));let g=m.direction==Ge!=!(d%2);Sr(s,g?i+1:i,n,m.inner,m.from,m.to,o),a=m.from}p=m.from}else{if(p==t||(f?K[p-1]!=l:K[p-1]==l))break;p--}u?vr(s,p,a,i+1,n,u,o):p<a&&o.push(new Ae(p,a,d)),a=p}}function Sr(s,t,e,i,n,r,o){let l=t%2?2:1;Uu(s,n,r,i,l),Gu(s,n,r,i,l),Yu(n,r,i,l),vr(s,n,r,t,e,i,o)}function Ju(s,t,e){if(!s)return[new Ae(0,0,t==Zr?1:0)];if(t==Ge&&!e.length&&!Ku.test(s))return Ph(s.length);if(e.length)for(;s.length>K.length;)K[K.length]=256;let i=[],n=t==Ge?0:1;return Sr(s,n,n,e,0,s.length,i),i}function Ph(s){return[new Ae(0,s,0)]}let Lh="";function Xu(s,t,e,i,n){var r;let o=i.head-s.from,l=Ae.find(t,o,(r=i.bidiLevel)!==null&&r!==void 0?r:-1,i.assoc),a=t[l],h=a.side(n,e);if(o==h){let u=l+=n?1:-1;if(u<0||u>=t.length)return null;a=t[l=u],o=a.side(!n,e),h=a.side(n,e)}let f=wt(s.text,o,a.forward(n,e));(f<a.from||f>a.to)&&(f=h),Lh=s.text.slice(Math.min(o,f),Math.max(o,f));let c=l==(n?t.length-1:0)?null:t[l+(n?1:-1)];return c&&f==h&&c.level+(n?0:1)<a.level?k.cursor(c.side(!n,e)+s.from,c.forward(n,e)?1:-1,c.level):k.cursor(f+s.from,a.forward(n,e)?-1:1,a.level)}function $u(s,t,e){for(let i=t;i<e;i++){let n=Bh(s.charCodeAt(i));if(n==1)return Ge;if(n==2||n==4)return Zr}return Ge}const Rh=B.define(),Ih=B.define(),Nh=B.define(),_h=B.define(),Cr=B.define(),Fh=B.define(),Vh=B.define(),to=B.define(),eo=B.define(),Hh=B.define({combine:s=>s.some(t=>t)}),Wh=B.define({combine:s=>s.some(t=>t)}),zh=B.define();class oi{constructor(t,e="nearest",i="nearest",n=5,r=5,o=!1){this.range=t,this.y=e,this.x=i,this.yMargin=n,this.xMargin=r,this.isSnapshot=o}map(t){return t.empty?this:new oi(this.range.map(t),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}clip(t){return this.range.to<=t.doc.length?this:new oi(k.cursor(t.doc.length),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}}const gn=I.define({map:(s,t)=>s.map(t)}),qh=I.define();function Mt(s,t,e){let i=s.facet(_h);i.length?i[0](t):window.onerror?window.onerror(String(t),e,void 0,void 0,t):e?console.error(e+":",t):console.error(t)}const me=B.define({combine:s=>s.length?s[0]:!0});let Qu=0;const Ti=B.define();class ut{constructor(t,e,i,n,r){this.id=t,this.create=e,this.domEventHandlers=i,this.domEventObservers=n,this.extension=r(this)}static define(t,e){const{eventHandlers:i,eventObservers:n,provide:r,decorations:o}=e||{};return new ut(Qu++,t,i,n,l=>{let a=[Ti.of(l)];return o&&a.push(zi.of(h=>{let f=h.plugin(l);return f?o(f):N.none})),r&&a.push(r(l)),a})}static fromClass(t,e){return ut.define(i=>new t(i),e)}}class Ds{constructor(t){this.spec=t,this.mustUpdate=null,this.value=null}update(t){if(this.value){if(this.mustUpdate){let e=this.mustUpdate;if(this.mustUpdate=null,this.value.update)try{this.value.update(e)}catch(i){if(Mt(e.state,i,"CodeMirror plugin crashed"),this.value.destroy)try{this.value.destroy()}catch{}this.deactivate()}}}else if(this.spec)try{this.value=this.spec.create(t)}catch(e){Mt(t.state,e,"CodeMirror plugin crashed"),this.deactivate()}return this}destroy(t){var e;if(!((e=this.value)===null||e===void 0)&&e.destroy)try{this.value.destroy()}catch(i){Mt(t.state,i,"CodeMirror plugin crashed")}}deactivate(){this.spec=this.value=null}}const jh=B.define(),io=B.define(),zi=B.define(),Kh=B.define(),no=B.define(),Uh=B.define();function ol(s,t){let e=s.state.facet(Uh);if(!e.length)return e;let i=e.map(r=>r instanceof Function?r(s):r),n=[];return W.spans(i,t.from,t.to,{point(){},span(r,o,l,a){let h=r-t.from,f=o-t.from,c=n;for(let u=l.length-1;u>=0;u--,a--){let d=l[u].spec.bidiIsolate,p;if(d==null&&(d=$u(t.text,h,f)),a>0&&c.length&&(p=c[c.length-1]).to==h&&p.direction==d)p.to=f,c=p.inner;else{let m={from:h,to:f,direction:d,inner:[]};c.push(m),c=m.inner}}}}),n}const Gh=B.define();function so(s){let t=0,e=0,i=0,n=0;for(let r of s.state.facet(Gh)){let o=r(s);o&&(o.left!=null&&(t=Math.max(t,o.left)),o.right!=null&&(e=Math.max(e,o.right)),o.top!=null&&(i=Math.max(i,o.top)),o.bottom!=null&&(n=Math.max(n,o.bottom)))}return{left:t,right:e,top:i,bottom:n}}const Oi=B.define();class jt{constructor(t,e,i,n){this.fromA=t,this.toA=e,this.fromB=i,this.toB=n}join(t){return new jt(Math.min(this.fromA,t.fromA),Math.max(this.toA,t.toA),Math.min(this.fromB,t.fromB),Math.max(this.toB,t.toB))}addToSet(t){let e=t.length,i=this;for(;e>0;e--){let n=t[e-1];if(!(n.fromA>i.toA)){if(n.toA<i.fromA)break;i=i.join(n),t.splice(e-1,1)}}return t.splice(e,0,i),t}static extendWithRanges(t,e){if(e.length==0)return t;let i=[];for(let n=0,r=0,o=0,l=0;;n++){let a=n==t.length?null:t[n],h=o-l,f=a?a.fromB:1e9;for(;r<e.length&&e[r]<f;){let c=e[r],u=e[r+1],d=Math.max(l,c),p=Math.min(f,u);if(d<=p&&new jt(d+h,p+h,d,p).addToSet(i),u>f)break;r+=2}if(!a)return i;new jt(a.fromA,a.toA,a.fromB,a.toB).addToSet(i),o=a.toA,l=a.toB}}}class Qn{constructor(t,e,i){this.view=t,this.state=e,this.transactions=i,this.flags=0,this.startState=t.state,this.changes=it.empty(this.startState.doc.length);for(let r of i)this.changes=this.changes.compose(r.changes);let n=[];this.changes.iterChangedRanges((r,o,l,a)=>n.push(new jt(r,o,l,a))),this.changedRanges=n}static create(t,e,i){return new Qn(t,e,i)}get viewportChanged(){return(this.flags&4)>0}get viewportMoved(){return(this.flags&8)>0}get heightChanged(){return(this.flags&2)>0}get geometryChanged(){return this.docChanged||(this.flags&18)>0}get focusChanged(){return(this.flags&1)>0}get docChanged(){return!this.changes.empty}get selectionSet(){return this.transactions.some(t=>t.selection)}get empty(){return this.flags==0&&this.transactions.length==0}}class ll extends U{get length(){return this.view.state.doc.length}constructor(t){super(),this.view=t,this.decorations=[],this.dynamicDecorationMap=[!1],this.domChanged=null,this.hasComposition=null,this.markedForComposition=new Set,this.editContextFormatting=N.none,this.lastCompositionAfterCursor=!1,this.minWidth=0,this.minWidthFrom=0,this.minWidthTo=0,this.impreciseAnchor=null,this.impreciseHead=null,this.forceSelection=!1,this.lastUpdate=Date.now(),this.setDOM(t.contentDOM),this.children=[new Z],this.children[0].setParent(this),this.updateDeco(),this.updateInner([new jt(0,0,0,t.state.doc.length)],0,null)}update(t){var e;let i=t.changedRanges;this.minWidth>0&&i.length&&(i.every(({fromA:h,toA:f})=>f<this.minWidthFrom||h>this.minWidthTo)?(this.minWidthFrom=t.changes.mapPos(this.minWidthFrom,1),this.minWidthTo=t.changes.mapPos(this.minWidthTo,1)):this.minWidth=this.minWidthFrom=this.minWidthTo=0),this.updateEditContextFormatting(t);let n=-1;this.view.inputState.composing>=0&&!this.view.observer.editContext&&(!((e=this.domChanged)===null||e===void 0)&&e.newSel?n=this.domChanged.newSel.head:!rd(t.changes,this.hasComposition)&&!t.selectionSet&&(n=t.state.selection.main.head));let r=n>-1?td(this.view,t.changes,n):null;if(this.domChanged=null,this.hasComposition){this.markedForComposition.clear();let{from:h,to:f}=this.hasComposition;i=new jt(h,f,t.changes.mapPos(h,-1),t.changes.mapPos(f,1)).addToSet(i.slice())}this.hasComposition=r?{from:r.range.fromB,to:r.range.toB}:null,(T.ie||T.chrome)&&!r&&t&&t.state.doc.lines!=t.startState.doc.lines&&(this.forceSelection=!0);let o=this.decorations,l=this.updateDeco(),a=nd(o,l,t.changes);return i=jt.extendWithRanges(i,a),!(this.flags&7)&&i.length==0?!1:(this.updateInner(i,t.startState.doc.length,r),t.transactions.length&&(this.lastUpdate=Date.now()),!0)}updateInner(t,e,i){this.view.viewState.mustMeasureContent=!0,this.updateChildren(t,e,i);let{observer:n}=this.view;n.ignore(()=>{this.dom.style.height=this.view.viewState.contentHeight/this.view.scaleY+"px",this.dom.style.flexBasis=this.minWidth?this.minWidth+"px":"";let o=T.chrome||T.ios?{node:n.selectionRange.focusNode,written:!1}:void 0;this.sync(this.view,o),this.flags&=-8,o&&(o.written||n.selectionRange.focusNode!=o.node)&&(this.forceSelection=!0),this.dom.style.height=""}),this.markedForComposition.forEach(o=>o.flags&=-9);let r=[];if(this.view.viewport.from||this.view.viewport.to<this.view.state.doc.length)for(let o of this.children)o instanceof ge&&o.widget instanceof xr&&r.push(o.dom);n.updateGaps(r)}updateChildren(t,e,i){let n=i?i.range.addToSet(t.slice()):t,r=this.childCursor(e);for(let o=n.length-1;;o--){let l=o>=0?n[o]:null;if(!l)break;let{fromA:a,toA:h,fromB:f,toB:c}=l,u,d,p,m;if(i&&i.range.fromB<c&&i.range.toB>f){let v=Ii.build(this.view.state.doc,f,i.range.fromB,this.decorations,this.dynamicDecorationMap),x=Ii.build(this.view.state.doc,i.range.toB,c,this.decorations,this.dynamicDecorationMap);d=v.breakAtStart,p=v.openStart,m=x.openEnd;let C=this.compositionView(i);x.breakAtStart?C.breakAfter=1:x.content.length&&C.merge(C.length,C.length,x.content[0],!1,x.openStart,0)&&(C.breakAfter=x.content[0].breakAfter,x.content.shift()),v.content.length&&C.merge(0,0,v.content[v.content.length-1],!0,0,v.openEnd)&&v.content.pop(),u=v.content.concat(C).concat(x.content)}else({content:u,breakAtStart:d,openStart:p,openEnd:m}=Ii.build(this.view.state.doc,f,c,this.decorations,this.dynamicDecorationMap));let{i:g,off:y}=r.findPos(h,1),{i:w,off:S}=r.findPos(a,-1);kh(this,w,S,g,y,u,d,p,m)}i&&this.fixCompositionDOM(i)}updateEditContextFormatting(t){this.editContextFormatting=this.editContextFormatting.map(t.changes);for(let e of t.transactions)for(let i of e.effects)i.is(qh)&&(this.editContextFormatting=i.value)}compositionView(t){let e=new Xt(t.text.nodeValue);e.flags|=8;for(let{deco:n}of t.marks)e=new ye(n,[e],e.length);let i=new Z;return i.append(e,0),i}fixCompositionDOM(t){let e=(r,o)=>{o.flags|=8|(o.children.some(a=>a.flags&7)?1:0),this.markedForComposition.add(o);let l=U.get(r);l&&l!=o&&(l.dom=null),o.setDOM(r)},i=this.childPos(t.range.fromB,1),n=this.children[i.i];e(t.line,n);for(let r=t.marks.length-1;r>=-1;r--)i=n.childPos(i.off,1),n=n.children[i.i],e(r>=0?t.marks[r].node:t.text,n)}updateSelection(t=!1,e=!1){(t||!this.view.observer.selectionRange.focusNode)&&this.view.observer.readSelectionRange();let i=this.view.root.activeElement,n=i==this.dom,r=!n&&!(this.view.state.facet(me)||this.dom.tabIndex>-1)&&Fn(this.dom,this.view.observer.selectionRange)&&!(i&&this.dom.contains(i));if(!(n||e||r))return;let o=this.forceSelection;this.forceSelection=!1;let l=this.view.state.selection.main,a=this.moveToLine(this.domAtPos(l.anchor)),h=l.empty?a:this.moveToLine(this.domAtPos(l.head));if(T.gecko&&l.empty&&!this.hasComposition&&Zu(a)){let c=document.createTextNode("");this.view.observer.ignore(()=>a.node.insertBefore(c,a.node.childNodes[a.offset]||null)),a=h=new mt(c,0),o=!0}let f=this.view.observer.selectionRange;(o||!f.focusNode||(!Ri(a.node,a.offset,f.anchorNode,f.anchorOffset)||!Ri(h.node,h.offset,f.focusNode,f.focusOffset))&&!this.suppressWidgetCursorChange(f,l))&&(this.view.observer.ignore(()=>{T.android&&T.chrome&&this.dom.contains(f.focusNode)&&sd(f.focusNode,this.dom)&&(this.dom.blur(),this.dom.focus({preventScroll:!0}));let c=Wi(this.view.root);if(c)if(l.empty){if(T.gecko){let u=ed(a.node,a.offset);if(u&&u!=3){let d=(u==1?yh:wh)(a.node,a.offset);d&&(a=new mt(d.node,d.offset))}}c.collapse(a.node,a.offset),l.bidiLevel!=null&&c.caretBidiLevel!==void 0&&(c.caretBidiLevel=l.bidiLevel)}else if(c.extend){c.collapse(a.node,a.offset);try{c.extend(h.node,h.offset)}catch{}}else{let u=document.createRange();l.anchor>l.head&&([a,h]=[h,a]),u.setEnd(h.node,h.offset),u.setStart(a.node,a.offset),c.removeAllRanges(),c.addRange(u)}r&&this.view.root.activeElement==this.dom&&(this.dom.blur(),i&&i.focus())}),this.view.observer.setSelectionRange(a,h)),this.impreciseAnchor=a.precise?null:new mt(f.anchorNode,f.anchorOffset),this.impreciseHead=h.precise?null:new mt(f.focusNode,f.focusOffset)}suppressWidgetCursorChange(t,e){return this.hasComposition&&e.empty&&Ri(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset)&&this.posFromDOM(t.focusNode,t.focusOffset)==e.head}enforceCursorAssoc(){if(this.hasComposition)return;let{view:t}=this,e=t.state.selection.main,i=Wi(t.root),{anchorNode:n,anchorOffset:r}=t.observer.selectionRange;if(!i||!e.empty||!e.assoc||!i.modify)return;let o=Z.find(this,e.head);if(!o)return;let l=o.posAtStart;if(e.head==l||e.head==l+o.length)return;let a=this.coordsAt(e.head,-1),h=this.coordsAt(e.head,1);if(!a||!h||a.bottom>h.top)return;let f=this.domAtPos(e.head+e.assoc);i.collapse(f.node,f.offset),i.modify("move",e.assoc<0?"forward":"backward","lineboundary"),t.observer.readSelectionRange();let c=t.observer.selectionRange;t.docView.posFromDOM(c.anchorNode,c.anchorOffset)!=e.from&&i.collapse(n,r)}moveToLine(t){let e=this.dom,i;if(t.node!=e)return t;for(let n=t.offset;!i&&n<e.childNodes.length;n++){let r=U.get(e.childNodes[n]);r instanceof Z&&(i=r.domAtPos(0))}for(let n=t.offset-1;!i&&n>=0;n--){let r=U.get(e.childNodes[n]);r instanceof Z&&(i=r.domAtPos(r.length))}return i?new mt(i.node,i.offset,!0):t}nearest(t){for(let e=t;e;){let i=U.get(e);if(i&&i.rootView==this)return i;e=e.parentNode}return null}posFromDOM(t,e){let i=this.nearest(t);if(!i)throw new RangeError("Trying to find position for a DOM position outside of the document");return i.localPosFromDOM(t,e)+i.posAtStart}domAtPos(t){let{i:e,off:i}=this.childCursor().findPos(t,-1);for(;e<this.children.length-1;){let n=this.children[e];if(i<n.length||n instanceof Z)break;e++,i=0}return this.children[e].domAtPos(i)}coordsAt(t,e){let i=null,n=0;for(let r=this.length,o=this.children.length-1;o>=0;o--){let l=this.children[o],a=r-l.breakAfter,h=a-l.length;if(a<t)break;if(h<=t&&(h<t||l.covers(-1))&&(a>t||l.covers(1))&&(!i||l instanceof Z&&!(i instanceof Z&&e>=0)))i=l,n=h;else if(i&&h==t&&a==t&&l instanceof ge&&Math.abs(e)<2){if(l.deco.startSide<0)break;o&&(i=null)}r=h}return i?i.coordsAt(t-n,e):null}coordsForChar(t){let{i:e,off:i}=this.childPos(t,1),n=this.children[e];if(!(n instanceof Z))return null;for(;n.children.length;){let{i:l,off:a}=n.childPos(i,1);for(;;l++){if(l==n.children.length)return null;if((n=n.children[l]).length)break}i=a}if(!(n instanceof Xt))return null;let r=wt(n.text,i);if(r==i)return null;let o=Ue(n.dom,i,r).getClientRects();for(let l=0;l<o.length;l++){let a=o[l];if(l==o.length-1||a.top<a.bottom&&a.left<a.right)return a}return null}measureVisibleLineHeights(t){let e=[],{from:i,to:n}=t,r=this.view.contentDOM.clientWidth,o=r>Math.max(this.view.scrollDOM.clientWidth,this.minWidth)+1,l=-1,a=this.view.textDirection==J.LTR;for(let h=0,f=0;f<this.children.length;f++){let c=this.children[f],u=h+c.length;if(u>n)break;if(h>=i){let d=c.dom.getBoundingClientRect();if(e.push(d.height),o){let p=c.dom.lastChild,m=p?hi(p):[];if(m.length){let g=m[m.length-1],y=a?g.right-d.left:d.right-g.left;y>l&&(l=y,this.minWidth=r,this.minWidthFrom=h,this.minWidthTo=u)}}}h=u+c.breakAfter}return e}textDirectionAt(t){let{i:e}=this.childPos(t,1);return getComputedStyle(this.children[e].dom).direction=="rtl"?J.RTL:J.LTR}measureTextSize(){for(let r of this.children)if(r instanceof Z){let o=r.measureTextSize();if(o)return o}let t=document.createElement("div"),e,i,n;return t.className="cm-line",t.style.width="99999px",t.style.position="absolute",t.textContent="abc def ghi jkl mno pqr stu",this.view.observer.ignore(()=>{this.dom.appendChild(t);let r=hi(t.firstChild)[0];e=t.getBoundingClientRect().height,i=r?r.width/27:7,n=r?r.height:e,t.remove()}),{lineHeight:e,charWidth:i,textHeight:n}}childCursor(t=this.length){let e=this.children.length;return e&&(t-=this.children[--e].length),new xh(this.children,t,e)}computeBlockGapDeco(){let t=[],e=this.view.viewState;for(let i=0,n=0;;n++){let r=n==e.viewports.length?null:e.viewports[n],o=r?r.from-1:this.length;if(o>i){let l=(e.lineBlockAt(o).bottom-e.lineBlockAt(i).top)/this.view.scaleY;t.push(N.replace({widget:new xr(l),block:!0,inclusive:!0,isBlockGap:!0}).range(i,o))}if(!r)break;i=r.to+1}return N.set(t)}updateDeco(){let t=1,e=this.view.state.facet(zi).map(r=>(this.dynamicDecorationMap[t++]=typeof r=="function")?r(this.view):r),i=!1,n=this.view.state.facet(Kh).map((r,o)=>{let l=typeof r=="function";return l&&(i=!0),l?r(this.view):r});for(n.length&&(this.dynamicDecorationMap[t++]=i,e.push(W.join(n))),this.decorations=[this.editContextFormatting,...e,this.computeBlockGapDeco(),this.view.viewState.lineGapDeco];t<this.decorations.length;)this.dynamicDecorationMap[t++]=!1;return this.decorations}scrollIntoView(t){if(t.isSnapshot){let h=this.view.viewState.lineBlockAt(t.range.head);this.view.scrollDOM.scrollTop=h.top-t.yMargin,this.view.scrollDOM.scrollLeft=t.xMargin;return}for(let h of this.view.state.facet(zh))try{if(h(this.view,t.range,t))return!0}catch(f){Mt(this.view.state,f,"scroll handler")}let{range:e}=t,i=this.coordsAt(e.head,e.empty?e.assoc:e.head>e.anchor?-1:1),n;if(!i)return;!e.empty&&(n=this.coordsAt(e.anchor,e.anchor>e.head?-1:1))&&(i={left:Math.min(i.left,n.left),top:Math.min(i.top,n.top),right:Math.max(i.right,n.right),bottom:Math.max(i.bottom,n.bottom)});let r=so(this.view),o={left:i.left-r.left,top:i.top-r.top,right:i.right+r.right,bottom:i.bottom+r.bottom},{offsetWidth:l,offsetHeight:a}=this.view.scrollDOM;Lu(this.view.scrollDOM,o,e.head<e.anchor?-1:1,t.x,t.y,Math.max(Math.min(t.xMargin,l),-l),Math.max(Math.min(t.yMargin,a),-a),this.view.textDirection==J.LTR)}}function Zu(s){return s.node.nodeType==1&&s.node.firstChild&&(s.offset==0||s.node.childNodes[s.offset-1].contentEditable=="false")&&(s.offset==s.node.childNodes.length||s.node.childNodes[s.offset].contentEditable=="false")}function Yh(s,t){let e=s.observer.selectionRange;if(!e.focusNode)return null;let i=yh(e.focusNode,e.focusOffset),n=wh(e.focusNode,e.focusOffset),r=i||n;if(n&&i&&n.node!=i.node){let l=U.get(n.node);if(!l||l instanceof Xt&&l.text!=n.node.nodeValue)r=n;else if(s.docView.lastCompositionAfterCursor){let a=U.get(i.node);!a||a instanceof Xt&&a.text!=i.node.nodeValue||(r=n)}}if(s.docView.lastCompositionAfterCursor=r!=i,!r)return null;let o=t-r.offset;return{from:o,to:o+r.node.nodeValue.length,node:r.node}}function td(s,t,e){let i=Yh(s,e);if(!i)return null;let{node:n,from:r,to:o}=i,l=n.nodeValue;if(/[\n\r]/.test(l)||s.state.doc.sliceString(i.from,i.to)!=l)return null;let a=t.invertedDesc,h=new jt(a.mapPos(r),a.mapPos(o),r,o),f=[];for(let c=n.parentNode;;c=c.parentNode){let u=U.get(c);if(u instanceof ye)f.push({node:c,deco:u.mark});else{if(u instanceof Z||c.nodeName=="DIV"&&c.parentNode==s.contentDOM)return{range:h,text:n,marks:f,line:c};if(c!=s.contentDOM)f.push({node:c,deco:new sn({inclusive:!0,attributes:Wu(c),tagName:c.tagName.toLowerCase()})});else return null}}}function ed(s,t){return s.nodeType!=1?0:(t&&s.childNodes[t-1].contentEditable=="false"?1:0)|(t<s.childNodes.length&&s.childNodes[t].contentEditable=="false"?2:0)}let id=class{constructor(){this.changes=[]}compareRange(t,e){Vn(t,e,this.changes)}comparePoint(t,e){Vn(t,e,this.changes)}boundChange(t){Vn(t,t,this.changes)}};function nd(s,t,e){let i=new id;return W.compare(s,t,e,i),i.changes}function sd(s,t){for(let e=s;e&&e!=t;e=e.assignedSlot||e.parentNode)if(e.nodeType==1&&e.contentEditable=="false")return!0;return!1}function rd(s,t){let e=!1;return t&&s.iterChangedRanges((i,n)=>{i<t.to&&n>t.from&&(e=!0)}),e}function od(s,t,e=1){let i=s.charCategorizer(t),n=s.doc.lineAt(t),r=t-n.from;if(n.length==0)return k.cursor(t);r==0?e=1:r==n.length&&(e=-1);let o=r,l=r;e<0?o=wt(n.text,r,!1):l=wt(n.text,r);let a=i(n.text.slice(o,l));for(;o>0;){let h=wt(n.text,o,!1);if(i(n.text.slice(h,o))!=a)break;o=h}for(;l<n.length;){let h=wt(n.text,l);if(i(n.text.slice(l,h))!=a)break;l=h}return k.range(o+n.from,l+n.from)}function ld(s,t){return t.left>s?t.left-s:Math.max(0,s-t.right)}function ad(s,t){return t.top>s?t.top-s:Math.max(0,s-t.bottom)}function Ts(s,t){return s.top<t.bottom-1&&s.bottom>t.top+1}function al(s,t){return t<s.top?{top:t,left:s.left,right:s.right,bottom:s.bottom}:s}function hl(s,t){return t>s.bottom?{top:s.top,left:s.left,right:s.right,bottom:t}:s}function Ar(s,t,e){let i,n,r,o,l=!1,a,h,f,c;for(let p=s.firstChild;p;p=p.nextSibling){let m=hi(p);for(let g=0;g<m.length;g++){let y=m[g];n&&Ts(n,y)&&(y=al(hl(y,n.bottom),n.top));let w=ld(t,y),S=ad(e,y);if(w==0&&S==0)return p.nodeType==3?fl(p,t,e):Ar(p,t,e);if(!i||o>S||o==S&&r>w){i=p,n=y,r=w,o=S;let v=S?e<y.top?-1:1:w?t<y.left?-1:1:0;l=!v||(v>0?g<m.length-1:g>0)}w==0?e>y.bottom&&(!f||f.bottom<y.bottom)?(a=p,f=y):e<y.top&&(!c||c.top>y.top)&&(h=p,c=y):f&&Ts(f,y)?f=hl(f,y.bottom):c&&Ts(c,y)&&(c=al(c,y.top))}}if(f&&f.bottom>=e?(i=a,n=f):c&&c.top<=e&&(i=h,n=c),!i)return{node:s,offset:0};let u=Math.max(n.left,Math.min(n.right,t));if(i.nodeType==3)return fl(i,u,e);if(l&&i.contentEditable!="false")return Ar(i,u,e);let d=Array.prototype.indexOf.call(s.childNodes,i)+(t>=(n.left+n.right)/2?1:0);return{node:s,offset:d}}function fl(s,t,e){let i=s.nodeValue.length,n=-1,r=1e9,o=0;for(let l=0;l<i;l++){let a=Ue(s,l,l+1).getClientRects();for(let h=0;h<a.length;h++){let f=a[h];if(f.top==f.bottom)continue;o||(o=t-f.left);let c=(f.top>e?f.top-e:e-f.bottom)-1;if(f.left-1<=t&&f.right+1>=t&&c<r){let u=t>=(f.left+f.right)/2,d=u;if((T.chrome||T.gecko)&&Ue(s,l).getBoundingClientRect().left==f.right&&(d=!u),c<=0)return{node:s,offset:l+(d?1:0)};n=l+(d?1:0),r=c}}}return{node:s,offset:n>-1?n:o>0?s.nodeValue.length:0}}function Jh(s,t,e,i=-1){var n,r;let o=s.contentDOM.getBoundingClientRect(),l=o.top+s.viewState.paddingTop,a,{docHeight:h}=s.viewState,{x:f,y:c}=t,u=c-l;if(u<0)return 0;if(u>h)return s.state.doc.length;for(let v=s.viewState.heightOracle.textHeight/2,x=!1;a=s.elementAtHeight(u),a.type!=xt.Text;)for(;u=i>0?a.bottom+v:a.top-v,!(u>=0&&u<=h);){if(x)return e?null:0;x=!0,i=-i}c=l+u;let d=a.from;if(d<s.viewport.from)return s.viewport.from==0?0:e?null:cl(s,o,a,f,c);if(d>s.viewport.to)return s.viewport.to==s.state.doc.length?s.state.doc.length:e?null:cl(s,o,a,f,c);let p=s.dom.ownerDocument,m=s.root.elementFromPoint?s.root:p,g=m.elementFromPoint(f,c);g&&!s.contentDOM.contains(g)&&(g=null),g||(f=Math.max(o.left+1,Math.min(o.right-1,f)),g=m.elementFromPoint(f,c),g&&!s.contentDOM.contains(g)&&(g=null));let y,w=-1;if(g&&((n=s.docView.nearest(g))===null||n===void 0?void 0:n.isEditable)!=!1){if(p.caretPositionFromPoint){let v=p.caretPositionFromPoint(f,c);v&&({offsetNode:y,offset:w}=v)}else if(p.caretRangeFromPoint){let v=p.caretRangeFromPoint(f,c);v&&({startContainer:y,startOffset:w}=v,(!s.contentDOM.contains(y)||T.safari&&hd(y,w,f)||T.chrome&&fd(y,w,f))&&(y=void 0))}y&&(w=Math.min(ce(y),w))}if(!y||!s.docView.dom.contains(y)){let v=Z.find(s.docView,d);if(!v)return u>a.top+a.height/2?a.to:a.from;({node:y,offset:w}=Ar(v.dom,f,c))}let S=s.docView.nearest(y);if(!S)return null;if(S.isWidget&&((r=S.dom)===null||r===void 0?void 0:r.nodeType)==1){let v=S.dom.getBoundingClientRect();return t.y<v.top||t.y<=v.bottom&&t.x<=(v.left+v.right)/2?S.posAtStart:S.posAtEnd}else return S.localPosFromDOM(y,w)+S.posAtStart}function cl(s,t,e,i,n){let r=Math.round((i-t.left)*s.defaultCharacterWidth);if(s.lineWrapping&&e.height>s.defaultLineHeight*1.5){let l=s.viewState.heightOracle.textHeight,a=Math.floor((n-e.top-(s.defaultLineHeight-l)*.5)/l);r+=a*s.viewState.heightOracle.lineLength}let o=s.state.sliceDoc(e.from,e.to);return e.from+cr(o,r,s.state.tabSize)}function hd(s,t,e){let i;if(s.nodeType!=3||t!=(i=s.nodeValue.length))return!1;for(let n=s.nextSibling;n;n=n.nextSibling)if(n.nodeType!=1||n.nodeName!="BR")return!1;return Ue(s,i-1,i).getBoundingClientRect().left>e}function fd(s,t,e){if(t!=0)return!1;for(let n=s;;){let r=n.parentNode;if(!r||r.nodeType!=1||r.firstChild!=n)return!1;if(r.classList.contains("cm-line"))break;n=r}let i=s.nodeType==1?s.getBoundingClientRect():Ue(s,0,Math.max(s.nodeValue.length,1)).getBoundingClientRect();return e-i.left>5}function Mr(s,t){let e=s.lineBlockAt(t);if(Array.isArray(e.type)){for(let i of e.type)if(i.to>t||i.to==t&&(i.to==e.to||i.type==xt.Text))return i}return e}function cd(s,t,e,i){let n=Mr(s,t.head),r=!i||n.type!=xt.Text||!(s.lineWrapping||n.widgetLineBreaks)?null:s.coordsAtPos(t.assoc<0&&t.head>n.from?t.head-1:t.head);if(r){let o=s.dom.getBoundingClientRect(),l=s.textDirectionAt(n.from),a=s.posAtCoords({x:e==(l==J.LTR)?o.right-1:o.left+1,y:(r.top+r.bottom)/2});if(a!=null)return k.cursor(a,e?-1:1)}return k.cursor(e?n.to:n.from,e?-1:1)}function ul(s,t,e,i){let n=s.state.doc.lineAt(t.head),r=s.bidiSpans(n),o=s.textDirectionAt(n.from);for(let l=t,a=null;;){let h=Xu(n,r,o,l,e),f=Lh;if(!h){if(n.number==(e?s.state.doc.lines:1))return l;f=`
`,n=s.state.doc.line(n.number+(e?1:-1)),r=s.bidiSpans(n),h=s.visualLineSide(n,!e)}if(a){if(!a(f))return l}else{if(!i)return h;a=i(f)}l=h}}function ud(s,t,e){let i=s.state.charCategorizer(t),n=i(e);return r=>{let o=i(r);return n==Vt.Space&&(n=o),n==o}}function dd(s,t,e,i){let n=t.head,r=e?1:-1;if(n==(e?s.state.doc.length:0))return k.cursor(n,t.assoc);let o=t.goalColumn,l,a=s.contentDOM.getBoundingClientRect(),h=s.coordsAtPos(n,t.assoc||-1),f=s.documentTop;if(h)o==null&&(o=h.left-a.left),l=r<0?h.top:h.bottom;else{let d=s.viewState.lineBlockAt(n);o==null&&(o=Math.min(a.right-a.left,s.defaultCharacterWidth*(n-d.from))),l=(r<0?d.top:d.bottom)+f}let c=a.left+o,u=i??s.viewState.heightOracle.textHeight>>1;for(let d=0;;d+=10){let p=l+(u+d)*r,m=Jh(s,{x:c,y:p},!1,r);if(p<a.top||p>a.bottom||(r<0?m<n:m>n)){let g=s.docView.coordsForChar(m),y=!g||p<g.top?-1:1;return k.cursor(m,y,void 0,o)}}}function Hn(s,t,e){for(;;){let i=0;for(let n of s)n.between(t-1,t+1,(r,o,l)=>{if(t>r&&t<o){let a=i||e||(t-r<o-t?-1:1);t=a<0?r:o,i=a}});if(!i)return t}}function Os(s,t,e){let i=Hn(s.state.facet(no).map(n=>n(s)),e.from,t.head>e.from?-1:1);return i==e.from?e:k.cursor(i,i<e.from?1:-1)}const Bi="￿";class pd{constructor(t,e){this.points=t,this.text="",this.lineSeparator=e.facet(H.lineSeparator)}append(t){this.text+=t}lineBreak(){this.text+=Bi}readRange(t,e){if(!t)return this;let i=t.parentNode;for(let n=t;;){this.findPointBefore(i,n);let r=this.text.length;this.readNode(n);let o=n.nextSibling;if(o==e)break;let l=U.get(n),a=U.get(o);(l&&a?l.breakAfter:(l?l.breakAfter:Xn(n))||Xn(o)&&(n.nodeName!="BR"||n.cmIgnore)&&this.text.length>r)&&this.lineBreak(),n=o}return this.findPointBefore(i,e),this}readTextNode(t){let e=t.nodeValue;for(let i of this.points)i.node==t&&(i.pos=this.text.length+Math.min(i.offset,e.length));for(let i=0,n=this.lineSeparator?null:/\r\n?|\n/g;;){let r=-1,o=1,l;if(this.lineSeparator?(r=e.indexOf(this.lineSeparator,i),o=this.lineSeparator.length):(l=n.exec(e))&&(r=l.index,o=l[0].length),this.append(e.slice(i,r<0?e.length:r)),r<0)break;if(this.lineBreak(),o>1)for(let a of this.points)a.node==t&&a.pos>this.text.length&&(a.pos-=o-1);i=r+o}}readNode(t){if(t.cmIgnore)return;let e=U.get(t),i=e&&e.overrideDOMText;if(i!=null){this.findPointInside(t,i.length);for(let n=i.iter();!n.next().done;)n.lineBreak?this.lineBreak():this.append(n.value)}else t.nodeType==3?this.readTextNode(t):t.nodeName=="BR"?t.nextSibling&&this.lineBreak():t.nodeType==1&&this.readRange(t.firstChild,null)}findPointBefore(t,e){for(let i of this.points)i.node==t&&t.childNodes[i.offset]==e&&(i.pos=this.text.length)}findPointInside(t,e){for(let i of this.points)(t.nodeType==3?i.node==t:t.contains(i.node))&&(i.pos=this.text.length+(md(t,i.node,i.offset)?e:0))}}function md(s,t,e){for(;;){if(!t||e<ce(t))return!1;if(t==s)return!0;e=Ke(t)+1,t=t.parentNode}}class dl{constructor(t,e){this.node=t,this.offset=e,this.pos=-1}}class gd{constructor(t,e,i,n){this.typeOver=n,this.bounds=null,this.text="",this.domChanged=e>-1;let{impreciseHead:r,impreciseAnchor:o}=t.docView;if(t.state.readOnly&&e>-1)this.newSel=null;else if(e>-1&&(this.bounds=t.docView.domBoundsAround(e,i,0))){let l=r||o?[]:wd(t),a=new pd(l,t.state);a.readRange(this.bounds.startDOM,this.bounds.endDOM),this.text=a.text,this.newSel=xd(l,this.bounds.from)}else{let l=t.observer.selectionRange,a=r&&r.node==l.focusNode&&r.offset==l.focusOffset||!pr(t.contentDOM,l.focusNode)?t.state.selection.main.head:t.docView.posFromDOM(l.focusNode,l.focusOffset),h=o&&o.node==l.anchorNode&&o.offset==l.anchorOffset||!pr(t.contentDOM,l.anchorNode)?t.state.selection.main.anchor:t.docView.posFromDOM(l.anchorNode,l.anchorOffset),f=t.viewport;if((T.ios||T.chrome)&&t.state.selection.main.empty&&a!=h&&(f.from>0||f.to<t.state.doc.length)){let c=Math.min(a,h),u=Math.max(a,h),d=f.from-c,p=f.to-u;(d==0||d==1||c==0)&&(p==0||p==-1||u==t.state.doc.length)&&(a=0,h=t.state.doc.length)}this.newSel=k.single(h,a)}}}function Xh(s,t){let e,{newSel:i}=t,n=s.state.selection.main,r=s.inputState.lastKeyTime>Date.now()-100?s.inputState.lastKeyCode:-1;if(t.bounds){let{from:o,to:l}=t.bounds,a=n.from,h=null;(r===8||T.android&&t.text.length<l-o)&&(a=n.to,h="end");let f=yd(s.state.doc.sliceString(o,l,Bi),t.text,a-o,h);f&&(T.chrome&&r==13&&f.toB==f.from+2&&t.text.slice(f.from,f.toB)==Bi+Bi&&f.toB--,e={from:o+f.from,to:o+f.toA,insert:V.of(t.text.slice(f.from,f.toB).split(Bi))})}else i&&(!s.hasFocus&&s.state.facet(me)||i.main.eq(n))&&(i=null);if(!e&&!i)return!1;if(!e&&t.typeOver&&!n.empty&&i&&i.main.empty?e={from:n.from,to:n.to,insert:s.state.doc.slice(n.from,n.to)}:(T.mac||T.android)&&e&&e.from==e.to&&e.from==n.head-1&&/^\. ?$/.test(e.insert.toString())&&s.contentDOM.getAttribute("autocorrect")=="off"?(i&&e.insert.length==2&&(i=k.single(i.main.anchor-1,i.main.head-1)),e={from:e.from,to:e.to,insert:V.of([e.insert.toString().replace("."," ")])}):e&&e.from>=n.from&&e.to<=n.to&&(e.from!=n.from||e.to!=n.to)&&n.to-n.from-(e.to-e.from)<=4?e={from:n.from,to:n.to,insert:s.state.doc.slice(n.from,e.from).append(e.insert).append(s.state.doc.slice(e.to,n.to))}:T.chrome&&e&&e.from==e.to&&e.from==n.head&&e.insert.toString()==`
 `&&s.lineWrapping&&(i&&(i=k.single(i.main.anchor-1,i.main.head-1)),e={from:n.from,to:n.to,insert:V.of([" "])}),e)return ro(s,e,i,r);if(i&&!i.main.eq(n)){let o=!1,l="select";return s.inputState.lastSelectionTime>Date.now()-50&&(s.inputState.lastSelectionOrigin=="select"&&(o=!0),l=s.inputState.lastSelectionOrigin),s.dispatch({selection:i,scrollIntoView:o,userEvent:l}),!0}else return!1}function ro(s,t,e,i=-1){if(T.ios&&s.inputState.flushIOSKey(t))return!0;let n=s.state.selection.main;if(T.android&&(t.to==n.to&&(t.from==n.from||t.from==n.from-1&&s.state.sliceDoc(t.from,n.from)==" ")&&t.insert.length==1&&t.insert.lines==2&&ri(s.contentDOM,"Enter",13)||(t.from==n.from-1&&t.to==n.to&&t.insert.length==0||i==8&&t.insert.length<t.to-t.from&&t.to>n.head)&&ri(s.contentDOM,"Backspace",8)||t.from==n.from&&t.to==n.to+1&&t.insert.length==0&&ri(s.contentDOM,"Delete",46)))return!0;let r=t.insert.toString();s.inputState.composing>=0&&s.inputState.composing++;let o,l=()=>o||(o=bd(s,t,e));return s.state.facet(Fh).some(a=>a(s,t.from,t.to,r,l))||s.dispatch(l()),!0}function bd(s,t,e){let i,n=s.state,r=n.selection.main;if(t.from>=r.from&&t.to<=r.to&&t.to-t.from>=(r.to-r.from)/3&&(!e||e.main.empty&&e.main.from==t.from+t.insert.length)&&s.inputState.composing<0){let l=r.from<t.from?n.sliceDoc(r.from,t.from):"",a=r.to>t.to?n.sliceDoc(t.to,r.to):"";i=n.replaceSelection(s.state.toText(l+t.insert.sliceString(0,void 0,s.state.lineBreak)+a))}else{let l=n.changes(t),a=e&&e.main.to<=l.newLength?e.main:void 0;if(n.selection.ranges.length>1&&s.inputState.composing>=0&&t.to<=r.to&&t.to>=r.to-10){let h=s.state.sliceDoc(t.from,t.to),f,c=e&&Yh(s,e.main.head);if(c){let p=t.insert.length-(t.to-t.from);f={from:c.from,to:c.to-p}}else f=s.state.doc.lineAt(r.head);let u=r.to-t.to,d=r.to-r.from;i=n.changeByRange(p=>{if(p.from==r.from&&p.to==r.to)return{changes:l,range:a||p.map(l)};let m=p.to-u,g=m-h.length;if(p.to-p.from!=d||s.state.sliceDoc(g,m)!=h||p.to>=f.from&&p.from<=f.to)return{range:p};let y=n.changes({from:g,to:m,insert:t.insert}),w=p.to-r.to;return{changes:y,range:a?k.range(Math.max(0,a.anchor+w),Math.max(0,a.head+w)):p.map(y)}})}else i={changes:l,selection:a&&n.selection.replaceRange(a)}}let o="input.type";return(s.composing||s.inputState.compositionPendingChange&&s.inputState.compositionEndedAt>Date.now()-50)&&(s.inputState.compositionPendingChange=!1,o+=".compose",s.inputState.compositionFirstChange&&(o+=".start",s.inputState.compositionFirstChange=!1)),n.update(i,{userEvent:o,scrollIntoView:!0})}function yd(s,t,e,i){let n=Math.min(s.length,t.length),r=0;for(;r<n&&s.charCodeAt(r)==t.charCodeAt(r);)r++;if(r==n&&s.length==t.length)return null;let o=s.length,l=t.length;for(;o>0&&l>0&&s.charCodeAt(o-1)==t.charCodeAt(l-1);)o--,l--;if(i=="end"){let a=Math.max(0,r-Math.min(o,l));e-=o+a-r}if(o<r&&s.length<t.length){let a=e<=r&&e>=o?r-e:0;r-=a,l=r+(l-o),o=r}else if(l<r){let a=e<=r&&e>=l?r-e:0;r-=a,o=r+(o-l),l=r}return{from:r,toA:o,toB:l}}function wd(s){let t=[];if(s.root.activeElement!=s.contentDOM)return t;let{anchorNode:e,anchorOffset:i,focusNode:n,focusOffset:r}=s.observer.selectionRange;return e&&(t.push(new dl(e,i)),(n!=e||r!=i)&&t.push(new dl(n,r))),t}function xd(s,t){if(s.length==0)return null;let e=s[0].pos,i=s.length==2?s[1].pos:e;return e>-1&&i>-1?k.single(e+t,i+t):null}class kd{setSelectionOrigin(t){this.lastSelectionOrigin=t,this.lastSelectionTime=Date.now()}constructor(t){this.view=t,this.lastKeyCode=0,this.lastKeyTime=0,this.lastTouchTime=0,this.lastFocusTime=0,this.lastScrollTop=0,this.lastScrollLeft=0,this.pendingIOSKey=void 0,this.tabFocusMode=-1,this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastContextMenu=0,this.scrollHandlers=[],this.handlers=Object.create(null),this.composing=-1,this.compositionFirstChange=null,this.compositionEndedAt=0,this.compositionPendingKey=!1,this.compositionPendingChange=!1,this.mouseSelection=null,this.draggedContent=null,this.handleEvent=this.handleEvent.bind(this),this.notifiedFocused=t.hasFocus,T.safari&&t.contentDOM.addEventListener("input",()=>null),T.gecko&&_d(t.contentDOM.ownerDocument)}handleEvent(t){!Od(this.view,t)||this.ignoreDuringComposition(t)||t.type=="keydown"&&this.keydown(t)||(this.view.updateState!=0?Promise.resolve().then(()=>this.runHandlers(t.type,t)):this.runHandlers(t.type,t))}runHandlers(t,e){let i=this.handlers[t];if(i){for(let n of i.observers)n(this.view,e);for(let n of i.handlers){if(e.defaultPrevented)break;if(n(this.view,e)){e.preventDefault();break}}}}ensureHandlers(t){let e=vd(t),i=this.handlers,n=this.view.contentDOM;for(let r in e)if(r!="scroll"){let o=!e[r].handlers.length,l=i[r];l&&o!=!l.handlers.length&&(n.removeEventListener(r,this.handleEvent),l=null),l||n.addEventListener(r,this.handleEvent,{passive:o})}for(let r in i)r!="scroll"&&!e[r]&&n.removeEventListener(r,this.handleEvent);this.handlers=e}keydown(t){if(this.lastKeyCode=t.keyCode,this.lastKeyTime=Date.now(),t.keyCode==9&&this.tabFocusMode>-1&&(!this.tabFocusMode||Date.now()<=this.tabFocusMode))return!0;if(this.tabFocusMode>0&&t.keyCode!=27&&Qh.indexOf(t.keyCode)<0&&(this.tabFocusMode=-1),T.android&&T.chrome&&!t.synthetic&&(t.keyCode==13||t.keyCode==8))return this.view.observer.delayAndroidKey(t.key,t.keyCode),!0;let e;return T.ios&&!t.synthetic&&!t.altKey&&!t.metaKey&&((e=$h.find(i=>i.keyCode==t.keyCode))&&!t.ctrlKey||Sd.indexOf(t.key)>-1&&t.ctrlKey&&!t.shiftKey)?(this.pendingIOSKey=e||t,setTimeout(()=>this.flushIOSKey(),250),!0):(t.keyCode!=229&&this.view.observer.forceFlush(),!1)}flushIOSKey(t){let e=this.pendingIOSKey;return!e||e.key=="Enter"&&t&&t.from<t.to&&/^\S+$/.test(t.insert.toString())?!1:(this.pendingIOSKey=void 0,ri(this.view.contentDOM,e.key,e.keyCode,e instanceof KeyboardEvent?e:void 0))}ignoreDuringComposition(t){return/^key/.test(t.type)?this.composing>0?!0:T.safari&&!T.ios&&this.compositionPendingKey&&Date.now()-this.compositionEndedAt<100?(this.compositionPendingKey=!1,!0):!1:!1}startMouseSelection(t){this.mouseSelection&&this.mouseSelection.destroy(),this.mouseSelection=t}update(t){this.view.observer.update(t),this.mouseSelection&&this.mouseSelection.update(t),this.draggedContent&&t.docChanged&&(this.draggedContent=this.draggedContent.map(t.changes)),t.transactions.length&&(this.lastKeyCode=this.lastSelectionTime=0)}destroy(){this.mouseSelection&&this.mouseSelection.destroy()}}function pl(s,t){return(e,i)=>{try{return t.call(s,i,e)}catch(n){Mt(e.state,n)}}}function vd(s){let t=Object.create(null);function e(i){return t[i]||(t[i]={observers:[],handlers:[]})}for(let i of s){let n=i.spec;if(n&&n.domEventHandlers)for(let r in n.domEventHandlers){let o=n.domEventHandlers[r];o&&e(r).handlers.push(pl(i.value,o))}if(n&&n.domEventObservers)for(let r in n.domEventObservers){let o=n.domEventObservers[r];o&&e(r).observers.push(pl(i.value,o))}}for(let i in $t)e(i).handlers.push($t[i]);for(let i in Kt)e(i).observers.push(Kt[i]);return t}const $h=[{key:"Backspace",keyCode:8,inputType:"deleteContentBackward"},{key:"Enter",keyCode:13,inputType:"insertParagraph"},{key:"Enter",keyCode:13,inputType:"insertLineBreak"},{key:"Delete",keyCode:46,inputType:"deleteContentForward"}],Sd="dthko",Qh=[16,17,18,20,91,92,224,225],bn=6;function yn(s){return Math.max(0,s)*.7+8}function Cd(s,t){return Math.max(Math.abs(s.clientX-t.clientX),Math.abs(s.clientY-t.clientY))}class Ad{constructor(t,e,i,n){this.view=t,this.startEvent=e,this.style=i,this.mustSelect=n,this.scrollSpeed={x:0,y:0},this.scrolling=-1,this.lastEvent=e,this.scrollParents=Ru(t.contentDOM),this.atoms=t.state.facet(no).map(o=>o(t));let r=t.contentDOM.ownerDocument;r.addEventListener("mousemove",this.move=this.move.bind(this)),r.addEventListener("mouseup",this.up=this.up.bind(this)),this.extend=e.shiftKey,this.multiple=t.state.facet(H.allowMultipleSelections)&&Md(t,e),this.dragging=Td(t,e)&&ef(e)==1?null:!1}start(t){this.dragging===!1&&this.select(t)}move(t){if(t.buttons==0)return this.destroy();if(this.dragging||this.dragging==null&&Cd(this.startEvent,t)<10)return;this.select(this.lastEvent=t);let e=0,i=0,n=0,r=0,o=this.view.win.innerWidth,l=this.view.win.innerHeight;this.scrollParents.x&&({left:n,right:o}=this.scrollParents.x.getBoundingClientRect()),this.scrollParents.y&&({top:r,bottom:l}=this.scrollParents.y.getBoundingClientRect());let a=so(this.view);t.clientX-a.left<=n+bn?e=-yn(n-t.clientX):t.clientX+a.right>=o-bn&&(e=yn(t.clientX-o)),t.clientY-a.top<=r+bn?i=-yn(r-t.clientY):t.clientY+a.bottom>=l-bn&&(i=yn(t.clientY-l)),this.setScrollSpeed(e,i)}up(t){this.dragging==null&&this.select(this.lastEvent),this.dragging||t.preventDefault(),this.destroy()}destroy(){this.setScrollSpeed(0,0);let t=this.view.contentDOM.ownerDocument;t.removeEventListener("mousemove",this.move),t.removeEventListener("mouseup",this.up),this.view.inputState.mouseSelection=this.view.inputState.draggedContent=null}setScrollSpeed(t,e){this.scrollSpeed={x:t,y:e},t||e?this.scrolling<0&&(this.scrolling=setInterval(()=>this.scroll(),50)):this.scrolling>-1&&(clearInterval(this.scrolling),this.scrolling=-1)}scroll(){let{x:t,y:e}=this.scrollSpeed;t&&this.scrollParents.x&&(this.scrollParents.x.scrollLeft+=t,t=0),e&&this.scrollParents.y&&(this.scrollParents.y.scrollTop+=e,e=0),(t||e)&&this.view.win.scrollBy(t,e),this.dragging===!1&&this.select(this.lastEvent)}skipAtoms(t){let e=null;for(let i=0;i<t.ranges.length;i++){let n=t.ranges[i],r=null;if(n.empty){let o=Hn(this.atoms,n.from,0);o!=n.from&&(r=k.cursor(o,-1))}else{let o=Hn(this.atoms,n.from,-1),l=Hn(this.atoms,n.to,1);(o!=n.from||l!=n.to)&&(r=k.range(n.from==n.anchor?o:l,n.from==n.head?o:l))}r&&(e||(e=t.ranges.slice()),e[i]=r)}return e?k.create(e,t.mainIndex):t}select(t){let{view:e}=this,i=this.skipAtoms(this.style.get(t,this.extend,this.multiple));(this.mustSelect||!i.eq(e.state.selection,this.dragging===!1))&&this.view.dispatch({selection:i,userEvent:"select.pointer"}),this.mustSelect=!1}update(t){t.transactions.some(e=>e.isUserEvent("input.type"))?this.destroy():this.style.update(t)&&setTimeout(()=>this.select(this.lastEvent),20)}}function Md(s,t){let e=s.state.facet(Rh);return e.length?e[0](t):T.mac?t.metaKey:t.ctrlKey}function Dd(s,t){let e=s.state.facet(Ih);return e.length?e[0](t):T.mac?!t.altKey:!t.ctrlKey}function Td(s,t){let{main:e}=s.state.selection;if(e.empty)return!1;let i=Wi(s.root);if(!i||i.rangeCount==0)return!0;let n=i.getRangeAt(0).getClientRects();for(let r=0;r<n.length;r++){let o=n[r];if(o.left<=t.clientX&&o.right>=t.clientX&&o.top<=t.clientY&&o.bottom>=t.clientY)return!0}return!1}function Od(s,t){if(!t.bubbles)return!0;if(t.defaultPrevented)return!1;for(let e=t.target,i;e!=s.contentDOM;e=e.parentNode)if(!e||e.nodeType==11||(i=U.get(e))&&i.ignoreEvent(t))return!1;return!0}const $t=Object.create(null),Kt=Object.create(null),Zh=T.ie&&T.ie_version<15||T.ios&&T.webkit_version<604;function Bd(s){let t=s.dom.parentNode;if(!t)return;let e=t.appendChild(document.createElement("textarea"));e.style.cssText="position: fixed; left: -10000px; top: 10px",e.focus(),setTimeout(()=>{s.focus(),e.remove(),tf(s,e.value)},50)}function us(s,t,e){for(let i of s.facet(t))e=i(e,s);return e}function tf(s,t){t=us(s.state,to,t);let{state:e}=s,i,n=1,r=e.toText(t),o=r.lines==e.selection.ranges.length;if(Dr!=null&&e.selection.ranges.every(a=>a.empty)&&Dr==r.toString()){let a=-1;i=e.changeByRange(h=>{let f=e.doc.lineAt(h.from);if(f.from==a)return{range:h};a=f.from;let c=e.toText((o?r.line(n++).text:t)+e.lineBreak);return{changes:{from:f.from,insert:c},range:k.cursor(h.from+c.length)}})}else o?i=e.changeByRange(a=>{let h=r.line(n++);return{changes:{from:a.from,to:a.to,insert:h.text},range:k.cursor(a.from+h.length)}}):i=e.replaceSelection(r);s.dispatch(i,{userEvent:"input.paste",scrollIntoView:!0})}Kt.scroll=s=>{s.inputState.lastScrollTop=s.scrollDOM.scrollTop,s.inputState.lastScrollLeft=s.scrollDOM.scrollLeft};$t.keydown=(s,t)=>(s.inputState.setSelectionOrigin("select"),t.keyCode==27&&s.inputState.tabFocusMode!=0&&(s.inputState.tabFocusMode=Date.now()+2e3),!1);Kt.touchstart=(s,t)=>{s.inputState.lastTouchTime=Date.now(),s.inputState.setSelectionOrigin("select.pointer")};Kt.touchmove=s=>{s.inputState.setSelectionOrigin("select.pointer")};$t.mousedown=(s,t)=>{if(s.observer.flush(),s.inputState.lastTouchTime>Date.now()-2e3)return!1;let e=null;for(let i of s.state.facet(Nh))if(e=i(s,t),e)break;if(!e&&t.button==0&&(e=Ld(s,t)),e){let i=!s.hasFocus;s.inputState.startMouseSelection(new Ad(s,t,e,i)),i&&s.observer.ignore(()=>{mh(s.contentDOM);let r=s.root.activeElement;r&&!r.contains(s.contentDOM)&&r.blur()});let n=s.inputState.mouseSelection;if(n)return n.start(t),n.dragging===!1}return!1};function ml(s,t,e,i){if(i==1)return k.cursor(t,e);if(i==2)return od(s.state,t,e);{let n=Z.find(s.docView,t),r=s.state.doc.lineAt(n?n.posAtEnd:t),o=n?n.posAtStart:r.from,l=n?n.posAtEnd:r.to;return l<s.state.doc.length&&l==r.to&&l++,k.range(o,l)}}let gl=(s,t,e)=>t>=e.top&&t<=e.bottom&&s>=e.left&&s<=e.right;function Ed(s,t,e,i){let n=Z.find(s.docView,t);if(!n)return 1;let r=t-n.posAtStart;if(r==0)return 1;if(r==n.length)return-1;let o=n.coordsAt(r,-1);if(o&&gl(e,i,o))return-1;let l=n.coordsAt(r,1);return l&&gl(e,i,l)?1:o&&o.bottom>=i?-1:1}function bl(s,t){let e=s.posAtCoords({x:t.clientX,y:t.clientY},!1);return{pos:e,bias:Ed(s,e,t.clientX,t.clientY)}}const Pd=T.ie&&T.ie_version<=11;let yl=null,wl=0,xl=0;function ef(s){if(!Pd)return s.detail;let t=yl,e=xl;return yl=s,xl=Date.now(),wl=!t||e>Date.now()-400&&Math.abs(t.clientX-s.clientX)<2&&Math.abs(t.clientY-s.clientY)<2?(wl+1)%3:1}function Ld(s,t){let e=bl(s,t),i=ef(t),n=s.state.selection;return{update(r){r.docChanged&&(e.pos=r.changes.mapPos(e.pos),n=n.map(r.changes))},get(r,o,l){let a=bl(s,r),h,f=ml(s,a.pos,a.bias,i);if(e.pos!=a.pos&&!o){let c=ml(s,e.pos,e.bias,i),u=Math.min(c.from,f.from),d=Math.max(c.to,f.to);f=u<f.from?k.range(u,d):k.range(d,u)}return o?n.replaceRange(n.main.extend(f.from,f.to)):l&&i==1&&n.ranges.length>1&&(h=Rd(n,a.pos))?h:l?n.addRange(f):k.create([f])}}}function Rd(s,t){for(let e=0;e<s.ranges.length;e++){let{from:i,to:n}=s.ranges[e];if(i<=t&&n>=t)return k.create(s.ranges.slice(0,e).concat(s.ranges.slice(e+1)),s.mainIndex==e?0:s.mainIndex-(s.mainIndex>e?1:0))}return null}$t.dragstart=(s,t)=>{let{selection:{main:e}}=s.state;if(t.target.draggable){let n=s.docView.nearest(t.target);if(n&&n.isWidget){let r=n.posAtStart,o=r+n.length;(r>=e.to||o<=e.from)&&(e=k.range(r,o))}}let{inputState:i}=s;return i.mouseSelection&&(i.mouseSelection.dragging=!0),i.draggedContent=e,t.dataTransfer&&(t.dataTransfer.setData("Text",us(s.state,eo,s.state.sliceDoc(e.from,e.to))),t.dataTransfer.effectAllowed="copyMove"),!1};$t.dragend=s=>(s.inputState.draggedContent=null,!1);function kl(s,t,e,i){if(e=us(s.state,to,e),!e)return;let n=s.posAtCoords({x:t.clientX,y:t.clientY},!1),{draggedContent:r}=s.inputState,o=i&&r&&Dd(s,t)?{from:r.from,to:r.to}:null,l={from:n,insert:e},a=s.state.changes(o?[o,l]:l);s.focus(),s.dispatch({changes:a,selection:{anchor:a.mapPos(n,-1),head:a.mapPos(n,1)},userEvent:o?"move.drop":"input.drop"}),s.inputState.draggedContent=null}$t.drop=(s,t)=>{if(!t.dataTransfer)return!1;if(s.state.readOnly)return!0;let e=t.dataTransfer.files;if(e&&e.length){let i=Array(e.length),n=0,r=()=>{++n==e.length&&kl(s,t,i.filter(o=>o!=null).join(s.state.lineBreak),!1)};for(let o=0;o<e.length;o++){let l=new FileReader;l.onerror=r,l.onload=()=>{/[\x00-\x08\x0e-\x1f]{2}/.test(l.result)||(i[o]=l.result),r()},l.readAsText(e[o])}return!0}else{let i=t.dataTransfer.getData("Text");if(i)return kl(s,t,i,!0),!0}return!1};$t.paste=(s,t)=>{if(s.state.readOnly)return!0;s.observer.flush();let e=Zh?null:t.clipboardData;return e?(tf(s,e.getData("text/plain")||e.getData("text/uri-list")),!0):(Bd(s),!1)};function Id(s,t){let e=s.dom.parentNode;if(!e)return;let i=e.appendChild(document.createElement("textarea"));i.style.cssText="position: fixed; left: -10000px; top: 10px",i.value=t,i.focus(),i.selectionEnd=t.length,i.selectionStart=0,setTimeout(()=>{i.remove(),s.focus()},50)}function Nd(s){let t=[],e=[],i=!1;for(let n of s.selection.ranges)n.empty||(t.push(s.sliceDoc(n.from,n.to)),e.push(n));if(!t.length){let n=-1;for(let{from:r}of s.selection.ranges){let o=s.doc.lineAt(r);o.number>n&&(t.push(o.text),e.push({from:o.from,to:Math.min(s.doc.length,o.to+1)})),n=o.number}i=!0}return{text:us(s,eo,t.join(s.lineBreak)),ranges:e,linewise:i}}let Dr=null;$t.copy=$t.cut=(s,t)=>{let{text:e,ranges:i,linewise:n}=Nd(s.state);if(!e&&!n)return!1;Dr=n?e:null,t.type=="cut"&&!s.state.readOnly&&s.dispatch({changes:i,scrollIntoView:!0,userEvent:"delete.cut"});let r=Zh?null:t.clipboardData;return r?(r.clearData(),r.setData("text/plain",e),!0):(Id(s,e),!1)};const nf=we.define();function sf(s,t){let e=[];for(let i of s.facet(Vh)){let n=i(s,t);n&&e.push(n)}return e?s.update({effects:e,annotations:nf.of(!0)}):null}function rf(s){setTimeout(()=>{let t=s.hasFocus;if(t!=s.inputState.notifiedFocused){let e=sf(s.state,t);e?s.dispatch(e):s.update([])}},10)}Kt.focus=s=>{s.inputState.lastFocusTime=Date.now(),!s.scrollDOM.scrollTop&&(s.inputState.lastScrollTop||s.inputState.lastScrollLeft)&&(s.scrollDOM.scrollTop=s.inputState.lastScrollTop,s.scrollDOM.scrollLeft=s.inputState.lastScrollLeft),rf(s)};Kt.blur=s=>{s.observer.clearSelectionRange(),rf(s)};Kt.compositionstart=Kt.compositionupdate=s=>{s.observer.editContext||(s.inputState.compositionFirstChange==null&&(s.inputState.compositionFirstChange=!0),s.inputState.composing<0&&(s.inputState.composing=0))};Kt.compositionend=s=>{s.observer.editContext||(s.inputState.composing=-1,s.inputState.compositionEndedAt=Date.now(),s.inputState.compositionPendingKey=!0,s.inputState.compositionPendingChange=s.observer.pendingRecords().length>0,s.inputState.compositionFirstChange=null,T.chrome&&T.android?s.observer.flushSoon():s.inputState.compositionPendingChange?Promise.resolve().then(()=>s.observer.flush()):setTimeout(()=>{s.inputState.composing<0&&s.docView.hasComposition&&s.update([])},50))};Kt.contextmenu=s=>{s.inputState.lastContextMenu=Date.now()};$t.beforeinput=(s,t)=>{var e,i;if(t.inputType=="insertReplacementText"&&s.observer.editContext){let r=(e=t.dataTransfer)===null||e===void 0?void 0:e.getData("text/plain"),o=t.getTargetRanges();if(r&&o.length){let l=o[0],a=s.posAtDOM(l.startContainer,l.startOffset),h=s.posAtDOM(l.endContainer,l.endOffset);return ro(s,{from:a,to:h,insert:s.state.toText(r)},null),!0}}let n;if(T.chrome&&T.android&&(n=$h.find(r=>r.inputType==t.inputType))&&(s.observer.delayAndroidKey(n.key,n.keyCode),n.key=="Backspace"||n.key=="Delete")){let r=((i=window.visualViewport)===null||i===void 0?void 0:i.height)||0;setTimeout(()=>{var o;(((o=window.visualViewport)===null||o===void 0?void 0:o.height)||0)>r+10&&s.hasFocus&&(s.contentDOM.blur(),s.focus())},100)}return T.ios&&t.inputType=="deleteContentForward"&&s.observer.flushSoon(),T.safari&&t.inputType=="insertText"&&s.inputState.composing>=0&&setTimeout(()=>Kt.compositionend(s,t),20),!1};const vl=new Set;function _d(s){vl.has(s)||(vl.add(s),s.addEventListener("copy",()=>{}),s.addEventListener("cut",()=>{}))}const Sl=["pre-wrap","normal","pre-line","break-spaces"];let ui=!1;function Cl(){ui=!1}class Fd{constructor(t){this.lineWrapping=t,this.doc=V.empty,this.heightSamples={},this.lineHeight=14,this.charWidth=7,this.textHeight=14,this.lineLength=30}heightForGap(t,e){let i=this.doc.lineAt(e).number-this.doc.lineAt(t).number+1;return this.lineWrapping&&(i+=Math.max(0,Math.ceil((e-t-i*this.lineLength*.5)/this.lineLength))),this.lineHeight*i}heightForLine(t){return this.lineWrapping?(1+Math.max(0,Math.ceil((t-this.lineLength)/(this.lineLength-5))))*this.lineHeight:this.lineHeight}setDoc(t){return this.doc=t,this}mustRefreshForWrapping(t){return Sl.indexOf(t)>-1!=this.lineWrapping}mustRefreshForHeights(t){let e=!1;for(let i=0;i<t.length;i++){let n=t[i];n<0?i++:this.heightSamples[Math.floor(n*10)]||(e=!0,this.heightSamples[Math.floor(n*10)]=!0)}return e}refresh(t,e,i,n,r,o){let l=Sl.indexOf(t)>-1,a=Math.round(e)!=Math.round(this.lineHeight)||this.lineWrapping!=l;if(this.lineWrapping=l,this.lineHeight=e,this.charWidth=i,this.textHeight=n,this.lineLength=r,a){this.heightSamples={};for(let h=0;h<o.length;h++){let f=o[h];f<0?h++:this.heightSamples[Math.floor(f*10)]=!0}}return a}}class Vd{constructor(t,e){this.from=t,this.heights=e,this.index=0}get more(){return this.index<this.heights.length}}class oe{constructor(t,e,i,n,r){this.from=t,this.length=e,this.top=i,this.height=n,this._content=r}get type(){return typeof this._content=="number"?xt.Text:Array.isArray(this._content)?this._content:this._content.type}get to(){return this.from+this.length}get bottom(){return this.top+this.height}get widget(){return this._content instanceof Be?this._content.widget:null}get widgetLineBreaks(){return typeof this._content=="number"?this._content:0}join(t){let e=(Array.isArray(this._content)?this._content:[this]).concat(Array.isArray(t._content)?t._content:[t]);return new oe(this.from,this.length+t.length,this.top,this.height+t.height,e)}}var Y=function(s){return s[s.ByPos=0]="ByPos",s[s.ByHeight=1]="ByHeight",s[s.ByPosNoHeight=2]="ByPosNoHeight",s}(Y||(Y={}));const Wn=.001;class kt{constructor(t,e,i=2){this.length=t,this.height=e,this.flags=i}get outdated(){return(this.flags&2)>0}set outdated(t){this.flags=(t?2:0)|this.flags&-3}setHeight(t){this.height!=t&&(Math.abs(this.height-t)>Wn&&(ui=!0),this.height=t)}replace(t,e,i){return kt.of(i)}decomposeLeft(t,e){e.push(this)}decomposeRight(t,e){e.push(this)}applyChanges(t,e,i,n){let r=this,o=i.doc;for(let l=n.length-1;l>=0;l--){let{fromA:a,toA:h,fromB:f,toB:c}=n[l],u=r.lineAt(a,Y.ByPosNoHeight,i.setDoc(e),0,0),d=u.to>=h?u:r.lineAt(h,Y.ByPosNoHeight,i,0,0);for(c+=d.to-h,h=d.to;l>0&&u.from<=n[l-1].toA;)a=n[l-1].fromA,f=n[l-1].fromB,l--,a<u.from&&(u=r.lineAt(a,Y.ByPosNoHeight,i,0,0));f+=u.from-a,a=u.from;let p=oo.build(i.setDoc(o),t,f,c);r=Zn(r,r.replace(a,h,p))}return r.updateHeight(i,0)}static empty(){return new Ot(0,0)}static of(t){if(t.length==1)return t[0];let e=0,i=t.length,n=0,r=0;for(;;)if(e==i)if(n>r*2){let l=t[e-1];l.break?t.splice(--e,1,l.left,null,l.right):t.splice(--e,1,l.left,l.right),i+=1+l.break,n-=l.size}else if(r>n*2){let l=t[i];l.break?t.splice(i,1,l.left,null,l.right):t.splice(i,1,l.left,l.right),i+=2+l.break,r-=l.size}else break;else if(n<r){let l=t[e++];l&&(n+=l.size)}else{let l=t[--i];l&&(r+=l.size)}let o=0;return t[e-1]==null?(o=1,e--):t[e]==null&&(o=1,i++),new Hd(kt.of(t.slice(0,e)),o,kt.of(t.slice(i)))}}function Zn(s,t){return s==t?s:(s.constructor!=t.constructor&&(ui=!0),t)}kt.prototype.size=1;class of extends kt{constructor(t,e,i){super(t,e),this.deco=i}blockAt(t,e,i,n){return new oe(n,this.length,i,this.height,this.deco||0)}lineAt(t,e,i,n,r){return this.blockAt(0,i,n,r)}forEachLine(t,e,i,n,r,o){t<=r+this.length&&e>=r&&o(this.blockAt(0,i,n,r))}updateHeight(t,e=0,i=!1,n){return n&&n.from<=e&&n.more&&this.setHeight(n.heights[n.index++]),this.outdated=!1,this}toString(){return`block(${this.length})`}}class Ot extends of{constructor(t,e){super(t,e,null),this.collapsed=0,this.widgetHeight=0,this.breaks=0}blockAt(t,e,i,n){return new oe(n,this.length,i,this.height,this.breaks)}replace(t,e,i){let n=i[0];return i.length==1&&(n instanceof Ot||n instanceof at&&n.flags&4)&&Math.abs(this.length-n.length)<10?(n instanceof at?n=new Ot(n.length,this.height):n.height=this.height,this.outdated||(n.outdated=!1),n):kt.of(i)}updateHeight(t,e=0,i=!1,n){return n&&n.from<=e&&n.more?this.setHeight(n.heights[n.index++]):(i||this.outdated)&&this.setHeight(Math.max(this.widgetHeight,t.heightForLine(this.length-this.collapsed))+this.breaks*t.lineHeight),this.outdated=!1,this}toString(){return`line(${this.length}${this.collapsed?-this.collapsed:""}${this.widgetHeight?":"+this.widgetHeight:""})`}}class at extends kt{constructor(t){super(t,0)}heightMetrics(t,e){let i=t.doc.lineAt(e).number,n=t.doc.lineAt(e+this.length).number,r=n-i+1,o,l=0;if(t.lineWrapping){let a=Math.min(this.height,t.lineHeight*r);o=a/r,this.length>r+1&&(l=(this.height-a)/(this.length-r-1))}else o=this.height/r;return{firstLine:i,lastLine:n,perLine:o,perChar:l}}blockAt(t,e,i,n){let{firstLine:r,lastLine:o,perLine:l,perChar:a}=this.heightMetrics(e,n);if(e.lineWrapping){let h=n+(t<e.lineHeight?0:Math.round(Math.max(0,Math.min(1,(t-i)/this.height))*this.length)),f=e.doc.lineAt(h),c=l+f.length*a,u=Math.max(i,t-c/2);return new oe(f.from,f.length,u,c,0)}else{let h=Math.max(0,Math.min(o-r,Math.floor((t-i)/l))),{from:f,length:c}=e.doc.line(r+h);return new oe(f,c,i+l*h,l,0)}}lineAt(t,e,i,n,r){if(e==Y.ByHeight)return this.blockAt(t,i,n,r);if(e==Y.ByPosNoHeight){let{from:d,to:p}=i.doc.lineAt(t);return new oe(d,p-d,0,0,0)}let{firstLine:o,perLine:l,perChar:a}=this.heightMetrics(i,r),h=i.doc.lineAt(t),f=l+h.length*a,c=h.number-o,u=n+l*c+a*(h.from-r-c);return new oe(h.from,h.length,Math.max(n,Math.min(u,n+this.height-f)),f,0)}forEachLine(t,e,i,n,r,o){t=Math.max(t,r),e=Math.min(e,r+this.length);let{firstLine:l,perLine:a,perChar:h}=this.heightMetrics(i,r);for(let f=t,c=n;f<=e;){let u=i.doc.lineAt(f);if(f==t){let p=u.number-l;c+=a*p+h*(t-r-p)}let d=a+h*u.length;o(new oe(u.from,u.length,c,d,0)),c+=d,f=u.to+1}}replace(t,e,i){let n=this.length-e;if(n>0){let r=i[i.length-1];r instanceof at?i[i.length-1]=new at(r.length+n):i.push(null,new at(n-1))}if(t>0){let r=i[0];r instanceof at?i[0]=new at(t+r.length):i.unshift(new at(t-1),null)}return kt.of(i)}decomposeLeft(t,e){e.push(new at(t-1),null)}decomposeRight(t,e){e.push(null,new at(this.length-t-1))}updateHeight(t,e=0,i=!1,n){let r=e+this.length;if(n&&n.from<=e+this.length&&n.more){let o=[],l=Math.max(e,n.from),a=-1;for(n.from>e&&o.push(new at(n.from-e-1).updateHeight(t,e));l<=r&&n.more;){let f=t.doc.lineAt(l).length;o.length&&o.push(null);let c=n.heights[n.index++];a==-1?a=c:Math.abs(c-a)>=Wn&&(a=-2);let u=new Ot(f,c);u.outdated=!1,o.push(u),l+=f+1}l<=r&&o.push(null,new at(r-l).updateHeight(t,l));let h=kt.of(o);return(a<0||Math.abs(h.height-this.height)>=Wn||Math.abs(a-this.heightMetrics(t,e).perLine)>=Wn)&&(ui=!0),Zn(this,h)}else(i||this.outdated)&&(this.setHeight(t.heightForGap(e,e+this.length)),this.outdated=!1);return this}toString(){return`gap(${this.length})`}}class Hd extends kt{constructor(t,e,i){super(t.length+e+i.length,t.height+i.height,e|(t.outdated||i.outdated?2:0)),this.left=t,this.right=i,this.size=t.size+i.size}get break(){return this.flags&1}blockAt(t,e,i,n){let r=i+this.left.height;return t<r?this.left.blockAt(t,e,i,n):this.right.blockAt(t,e,r,n+this.left.length+this.break)}lineAt(t,e,i,n,r){let o=n+this.left.height,l=r+this.left.length+this.break,a=e==Y.ByHeight?t<o:t<l,h=a?this.left.lineAt(t,e,i,n,r):this.right.lineAt(t,e,i,o,l);if(this.break||(a?h.to<l:h.from>l))return h;let f=e==Y.ByPosNoHeight?Y.ByPosNoHeight:Y.ByPos;return a?h.join(this.right.lineAt(l,f,i,o,l)):this.left.lineAt(l,f,i,n,r).join(h)}forEachLine(t,e,i,n,r,o){let l=n+this.left.height,a=r+this.left.length+this.break;if(this.break)t<a&&this.left.forEachLine(t,e,i,n,r,o),e>=a&&this.right.forEachLine(t,e,i,l,a,o);else{let h=this.lineAt(a,Y.ByPos,i,n,r);t<h.from&&this.left.forEachLine(t,h.from-1,i,n,r,o),h.to>=t&&h.from<=e&&o(h),e>h.to&&this.right.forEachLine(h.to+1,e,i,l,a,o)}}replace(t,e,i){let n=this.left.length+this.break;if(e<n)return this.balanced(this.left.replace(t,e,i),this.right);if(t>this.left.length)return this.balanced(this.left,this.right.replace(t-n,e-n,i));let r=[];t>0&&this.decomposeLeft(t,r);let o=r.length;for(let l of i)r.push(l);if(t>0&&Al(r,o-1),e<this.length){let l=r.length;this.decomposeRight(e,r),Al(r,l)}return kt.of(r)}decomposeLeft(t,e){let i=this.left.length;if(t<=i)return this.left.decomposeLeft(t,e);e.push(this.left),this.break&&(i++,t>=i&&e.push(null)),t>i&&this.right.decomposeLeft(t-i,e)}decomposeRight(t,e){let i=this.left.length,n=i+this.break;if(t>=n)return this.right.decomposeRight(t-n,e);t<i&&this.left.decomposeRight(t,e),this.break&&t<n&&e.push(null),e.push(this.right)}balanced(t,e){return t.size>2*e.size||e.size>2*t.size?kt.of(this.break?[t,null,e]:[t,e]):(this.left=Zn(this.left,t),this.right=Zn(this.right,e),this.setHeight(t.height+e.height),this.outdated=t.outdated||e.outdated,this.size=t.size+e.size,this.length=t.length+this.break+e.length,this)}updateHeight(t,e=0,i=!1,n){let{left:r,right:o}=this,l=e+r.length+this.break,a=null;return n&&n.from<=e+r.length&&n.more?a=r=r.updateHeight(t,e,i,n):r.updateHeight(t,e,i),n&&n.from<=l+o.length&&n.more?a=o=o.updateHeight(t,l,i,n):o.updateHeight(t,l,i),a?this.balanced(r,o):(this.height=this.left.height+this.right.height,this.outdated=!1,this)}toString(){return this.left+(this.break?" ":"-")+this.right}}function Al(s,t){let e,i;s[t]==null&&(e=s[t-1])instanceof at&&(i=s[t+1])instanceof at&&s.splice(t-1,3,new at(e.length+1+i.length))}const Wd=5;class oo{constructor(t,e){this.pos=t,this.oracle=e,this.nodes=[],this.lineStart=-1,this.lineEnd=-1,this.covering=null,this.writtenTo=t}get isCovered(){return this.covering&&this.nodes[this.nodes.length-1]==this.covering}span(t,e){if(this.lineStart>-1){let i=Math.min(e,this.lineEnd),n=this.nodes[this.nodes.length-1];n instanceof Ot?n.length+=i-this.pos:(i>this.pos||!this.isCovered)&&this.nodes.push(new Ot(i-this.pos,-1)),this.writtenTo=i,e>i&&(this.nodes.push(null),this.writtenTo++,this.lineStart=-1)}this.pos=e}point(t,e,i){if(t<e||i.heightRelevant){let n=i.widget?i.widget.estimatedHeight:0,r=i.widget?i.widget.lineBreaks:0;n<0&&(n=this.oracle.lineHeight);let o=e-t;i.block?this.addBlock(new of(o,n,i)):(o||r||n>=Wd)&&this.addLineDeco(n,r,o)}else e>t&&this.span(t,e);this.lineEnd>-1&&this.lineEnd<this.pos&&(this.lineEnd=this.oracle.doc.lineAt(this.pos).to)}enterLine(){if(this.lineStart>-1)return;let{from:t,to:e}=this.oracle.doc.lineAt(this.pos);this.lineStart=t,this.lineEnd=e,this.writtenTo<t&&((this.writtenTo<t-1||this.nodes[this.nodes.length-1]==null)&&this.nodes.push(this.blankContent(this.writtenTo,t-1)),this.nodes.push(null)),this.pos>t&&this.nodes.push(new Ot(this.pos-t,-1)),this.writtenTo=this.pos}blankContent(t,e){let i=new at(e-t);return this.oracle.doc.lineAt(t).to==e&&(i.flags|=4),i}ensureLine(){this.enterLine();let t=this.nodes.length?this.nodes[this.nodes.length-1]:null;if(t instanceof Ot)return t;let e=new Ot(0,-1);return this.nodes.push(e),e}addBlock(t){this.enterLine();let e=t.deco;e&&e.startSide>0&&!this.isCovered&&this.ensureLine(),this.nodes.push(t),this.writtenTo=this.pos=this.pos+t.length,e&&e.endSide>0&&(this.covering=t)}addLineDeco(t,e,i){let n=this.ensureLine();n.length+=i,n.collapsed+=i,n.widgetHeight=Math.max(n.widgetHeight,t),n.breaks+=e,this.writtenTo=this.pos=this.pos+i}finish(t){let e=this.nodes.length==0?null:this.nodes[this.nodes.length-1];this.lineStart>-1&&!(e instanceof Ot)&&!this.isCovered?this.nodes.push(new Ot(0,-1)):(this.writtenTo<this.pos||e==null)&&this.nodes.push(this.blankContent(this.writtenTo,this.pos));let i=t;for(let n of this.nodes)n instanceof Ot&&n.updateHeight(this.oracle,i),i+=n?n.length:1;return this.nodes}static build(t,e,i,n){let r=new oo(i,t);return W.spans(e,i,n,r,0),r.finish(i)}}function zd(s,t,e){let i=new qd;return W.compare(s,t,e,i,0),i.changes}class qd{constructor(){this.changes=[]}compareRange(){}comparePoint(t,e,i,n){(t<e||i&&i.heightRelevant||n&&n.heightRelevant)&&Vn(t,e,this.changes,5)}}function jd(s,t){let e=s.getBoundingClientRect(),i=s.ownerDocument,n=i.defaultView||window,r=Math.max(0,e.left),o=Math.min(n.innerWidth,e.right),l=Math.max(0,e.top),a=Math.min(n.innerHeight,e.bottom);for(let h=s.parentNode;h&&h!=i.body;)if(h.nodeType==1){let f=h,c=window.getComputedStyle(f);if((f.scrollHeight>f.clientHeight||f.scrollWidth>f.clientWidth)&&c.overflow!="visible"){let u=f.getBoundingClientRect();r=Math.max(r,u.left),o=Math.min(o,u.right),l=Math.max(l,u.top),a=Math.min(h==s.parentNode?n.innerHeight:a,u.bottom)}h=c.position=="absolute"||c.position=="fixed"?f.offsetParent:f.parentNode}else if(h.nodeType==11)h=h.host;else break;return{left:r-e.left,right:Math.max(r,o)-e.left,top:l-(e.top+t),bottom:Math.max(l,a)-(e.top+t)}}function Kd(s){let t=s.getBoundingClientRect(),e=s.ownerDocument.defaultView||window;return t.left<e.innerWidth&&t.right>0&&t.top<e.innerHeight&&t.bottom>0}function Ud(s,t){let e=s.getBoundingClientRect();return{left:0,right:e.right-e.left,top:t,bottom:e.bottom-(e.top+t)}}class Bs{constructor(t,e,i,n){this.from=t,this.to=e,this.size=i,this.displaySize=n}static same(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++){let n=t[i],r=e[i];if(n.from!=r.from||n.to!=r.to||n.size!=r.size)return!1}return!0}draw(t,e){return N.replace({widget:new Gd(this.displaySize*(e?t.scaleY:t.scaleX),e)}).range(this.from,this.to)}}class Gd extends ue{constructor(t,e){super(),this.size=t,this.vertical=e}eq(t){return t.size==this.size&&t.vertical==this.vertical}toDOM(){let t=document.createElement("div");return this.vertical?t.style.height=this.size+"px":(t.style.width=this.size+"px",t.style.height="2px",t.style.display="inline-block"),t}get estimatedHeight(){return this.vertical?this.size:-1}}class Ml{constructor(t){this.state=t,this.pixelViewport={left:0,right:window.innerWidth,top:0,bottom:0},this.inView=!0,this.paddingTop=0,this.paddingBottom=0,this.contentDOMWidth=0,this.contentDOMHeight=0,this.editorHeight=0,this.editorWidth=0,this.scrollTop=0,this.scrolledToBottom=!1,this.scaleX=1,this.scaleY=1,this.scrollAnchorPos=0,this.scrollAnchorHeight=-1,this.scaler=Dl,this.scrollTarget=null,this.printing=!1,this.mustMeasureContent=!0,this.defaultTextDirection=J.LTR,this.visibleRanges=[],this.mustEnforceCursorAssoc=!1;let e=t.facet(io).some(i=>typeof i!="function"&&i.class=="cm-lineWrapping");this.heightOracle=new Fd(e),this.stateDeco=t.facet(zi).filter(i=>typeof i!="function"),this.heightMap=kt.empty().applyChanges(this.stateDeco,V.empty,this.heightOracle.setDoc(t.doc),[new jt(0,0,0,t.doc.length)]);for(let i=0;i<2&&(this.viewport=this.getViewport(0,null),!!this.updateForViewport());i++);this.updateViewportLines(),this.lineGaps=this.ensureLineGaps([]),this.lineGapDeco=N.set(this.lineGaps.map(i=>i.draw(this,!1))),this.computeVisibleRanges()}updateForViewport(){let t=[this.viewport],{main:e}=this.state.selection;for(let i=0;i<=1;i++){let n=i?e.head:e.anchor;if(!t.some(({from:r,to:o})=>n>=r&&n<=o)){let{from:r,to:o}=this.lineBlockAt(n);t.push(new wn(r,o))}}return this.viewports=t.sort((i,n)=>i.from-n.from),this.updateScaler()}updateScaler(){let t=this.scaler;return this.scaler=this.heightMap.height<=7e6?Dl:new lo(this.heightOracle,this.heightMap,this.viewports),t.eq(this.scaler)?0:2}updateViewportLines(){this.viewportLines=[],this.heightMap.forEachLine(this.viewport.from,this.viewport.to,this.heightOracle.setDoc(this.state.doc),0,0,t=>{this.viewportLines.push(Ei(t,this.scaler))})}update(t,e=null){this.state=t.state;let i=this.stateDeco;this.stateDeco=this.state.facet(zi).filter(f=>typeof f!="function");let n=t.changedRanges,r=jt.extendWithRanges(n,zd(i,this.stateDeco,t?t.changes:it.empty(this.state.doc.length))),o=this.heightMap.height,l=this.scrolledToBottom?null:this.scrollAnchorAt(this.scrollTop);Cl(),this.heightMap=this.heightMap.applyChanges(this.stateDeco,t.startState.doc,this.heightOracle.setDoc(this.state.doc),r),(this.heightMap.height!=o||ui)&&(t.flags|=2),l?(this.scrollAnchorPos=t.changes.mapPos(l.from,-1),this.scrollAnchorHeight=l.top):(this.scrollAnchorPos=-1,this.scrollAnchorHeight=this.heightMap.height);let a=r.length?this.mapViewport(this.viewport,t.changes):this.viewport;(e&&(e.range.head<a.from||e.range.head>a.to)||!this.viewportIsAppropriate(a))&&(a=this.getViewport(0,e));let h=a.from!=this.viewport.from||a.to!=this.viewport.to;this.viewport=a,t.flags|=this.updateForViewport(),(h||!t.changes.empty||t.flags&2)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(this.mapLineGaps(this.lineGaps,t.changes))),t.flags|=this.computeVisibleRanges(t.changes),e&&(this.scrollTarget=e),!this.mustEnforceCursorAssoc&&t.selectionSet&&t.view.lineWrapping&&t.state.selection.main.empty&&t.state.selection.main.assoc&&!t.state.facet(Wh)&&(this.mustEnforceCursorAssoc=!0)}measure(t){let e=t.contentDOM,i=window.getComputedStyle(e),n=this.heightOracle,r=i.whiteSpace;this.defaultTextDirection=i.direction=="rtl"?J.RTL:J.LTR;let o=this.heightOracle.mustRefreshForWrapping(r),l=e.getBoundingClientRect(),a=o||this.mustMeasureContent||this.contentDOMHeight!=l.height;this.contentDOMHeight=l.height,this.mustMeasureContent=!1;let h=0,f=0;if(l.width&&l.height){let{scaleX:v,scaleY:x}=ph(e,l);(v>.005&&Math.abs(this.scaleX-v)>.005||x>.005&&Math.abs(this.scaleY-x)>.005)&&(this.scaleX=v,this.scaleY=x,h|=16,o=a=!0)}let c=(parseInt(i.paddingTop)||0)*this.scaleY,u=(parseInt(i.paddingBottom)||0)*this.scaleY;(this.paddingTop!=c||this.paddingBottom!=u)&&(this.paddingTop=c,this.paddingBottom=u,h|=18),this.editorWidth!=t.scrollDOM.clientWidth&&(n.lineWrapping&&(a=!0),this.editorWidth=t.scrollDOM.clientWidth,h|=16);let d=t.scrollDOM.scrollTop*this.scaleY;this.scrollTop!=d&&(this.scrollAnchorHeight=-1,this.scrollTop=d),this.scrolledToBottom=bh(t.scrollDOM);let p=(this.printing?Ud:jd)(e,this.paddingTop),m=p.top-this.pixelViewport.top,g=p.bottom-this.pixelViewport.bottom;this.pixelViewport=p;let y=this.pixelViewport.bottom>this.pixelViewport.top&&this.pixelViewport.right>this.pixelViewport.left;if(y!=this.inView&&(this.inView=y,y&&(a=!0)),!this.inView&&!this.scrollTarget&&!Kd(t.dom))return 0;let w=l.width;if((this.contentDOMWidth!=w||this.editorHeight!=t.scrollDOM.clientHeight)&&(this.contentDOMWidth=l.width,this.editorHeight=t.scrollDOM.clientHeight,h|=16),a){let v=t.docView.measureVisibleLineHeights(this.viewport);if(n.mustRefreshForHeights(v)&&(o=!0),o||n.lineWrapping&&Math.abs(w-this.contentDOMWidth)>n.charWidth){let{lineHeight:x,charWidth:C,textHeight:A}=t.docView.measureTextSize();o=x>0&&n.refresh(r,x,C,A,w/C,v),o&&(t.docView.minWidth=0,h|=16)}m>0&&g>0?f=Math.max(m,g):m<0&&g<0&&(f=Math.min(m,g)),Cl();for(let x of this.viewports){let C=x.from==this.viewport.from?v:t.docView.measureVisibleLineHeights(x);this.heightMap=(o?kt.empty().applyChanges(this.stateDeco,V.empty,this.heightOracle,[new jt(0,0,0,t.state.doc.length)]):this.heightMap).updateHeight(n,0,o,new Vd(x.from,C))}ui&&(h|=2)}let S=!this.viewportIsAppropriate(this.viewport,f)||this.scrollTarget&&(this.scrollTarget.range.head<this.viewport.from||this.scrollTarget.range.head>this.viewport.to);return S&&(h&2&&(h|=this.updateScaler()),this.viewport=this.getViewport(f,this.scrollTarget),h|=this.updateForViewport()),(h&2||S)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(o?[]:this.lineGaps,t)),h|=this.computeVisibleRanges(),this.mustEnforceCursorAssoc&&(this.mustEnforceCursorAssoc=!1,t.docView.enforceCursorAssoc()),h}get visibleTop(){return this.scaler.fromDOM(this.pixelViewport.top)}get visibleBottom(){return this.scaler.fromDOM(this.pixelViewport.bottom)}getViewport(t,e){let i=.5-Math.max(-.5,Math.min(.5,t/1e3/2)),n=this.heightMap,r=this.heightOracle,{visibleTop:o,visibleBottom:l}=this,a=new wn(n.lineAt(o-i*1e3,Y.ByHeight,r,0,0).from,n.lineAt(l+(1-i)*1e3,Y.ByHeight,r,0,0).to);if(e){let{head:h}=e.range;if(h<a.from||h>a.to){let f=Math.min(this.editorHeight,this.pixelViewport.bottom-this.pixelViewport.top),c=n.lineAt(h,Y.ByPos,r,0,0),u;e.y=="center"?u=(c.top+c.bottom)/2-f/2:e.y=="start"||e.y=="nearest"&&h<a.from?u=c.top:u=c.bottom-f,a=new wn(n.lineAt(u-1e3/2,Y.ByHeight,r,0,0).from,n.lineAt(u+f+1e3/2,Y.ByHeight,r,0,0).to)}}return a}mapViewport(t,e){let i=e.mapPos(t.from,-1),n=e.mapPos(t.to,1);return new wn(this.heightMap.lineAt(i,Y.ByPos,this.heightOracle,0,0).from,this.heightMap.lineAt(n,Y.ByPos,this.heightOracle,0,0).to)}viewportIsAppropriate({from:t,to:e},i=0){if(!this.inView)return!0;let{top:n}=this.heightMap.lineAt(t,Y.ByPos,this.heightOracle,0,0),{bottom:r}=this.heightMap.lineAt(e,Y.ByPos,this.heightOracle,0,0),{visibleTop:o,visibleBottom:l}=this;return(t==0||n<=o-Math.max(10,Math.min(-i,250)))&&(e==this.state.doc.length||r>=l+Math.max(10,Math.min(i,250)))&&n>o-2*1e3&&r<l+2*1e3}mapLineGaps(t,e){if(!t.length||e.empty)return t;let i=[];for(let n of t)e.touchesRange(n.from,n.to)||i.push(new Bs(e.mapPos(n.from),e.mapPos(n.to),n.size,n.displaySize));return i}ensureLineGaps(t,e){let i=this.heightOracle.lineWrapping,n=i?1e4:2e3,r=n>>1,o=n<<1;if(this.defaultTextDirection!=J.LTR&&!i)return[];let l=[],a=(f,c,u,d)=>{if(c-f<r)return;let p=this.state.selection.main,m=[p.from];p.empty||m.push(p.to);for(let y of m)if(y>f&&y<c){a(f,y-10,u,d),a(y+10,c,u,d);return}let g=Jd(t,y=>y.from>=u.from&&y.to<=u.to&&Math.abs(y.from-f)<r&&Math.abs(y.to-c)<r&&!m.some(w=>y.from<w&&y.to>w));if(!g){if(c<u.to&&e&&i&&e.visibleRanges.some(S=>S.from<=c&&S.to>=c)){let S=e.moveToLineBoundary(k.cursor(c),!1,!0).head;S>f&&(c=S)}let y=this.gapSize(u,f,c,d),w=i||y<2e6?y:2e6;g=new Bs(f,c,y,w)}l.push(g)},h=f=>{if(f.length<o||f.type!=xt.Text)return;let c=Yd(f.from,f.to,this.stateDeco);if(c.total<o)return;let u=this.scrollTarget?this.scrollTarget.range.head:null,d,p;if(i){let m=n/this.heightOracle.lineLength*this.heightOracle.lineHeight,g,y;if(u!=null){let w=kn(c,u),S=((this.visibleBottom-this.visibleTop)/2+m)/f.height;g=w-S,y=w+S}else g=(this.visibleTop-f.top-m)/f.height,y=(this.visibleBottom-f.top+m)/f.height;d=xn(c,g),p=xn(c,y)}else{let m=c.total*this.heightOracle.charWidth,g=n*this.heightOracle.charWidth,y=0;if(m>2e6)for(let C of t)C.from>=f.from&&C.from<f.to&&C.size!=C.displaySize&&C.from*this.heightOracle.charWidth+y<this.pixelViewport.left&&(y=C.size-C.displaySize);let w=this.pixelViewport.left+y,S=this.pixelViewport.right+y,v,x;if(u!=null){let C=kn(c,u),A=((S-w)/2+g)/m;v=C-A,x=C+A}else v=(w-g)/m,x=(S+g)/m;d=xn(c,v),p=xn(c,x)}d>f.from&&a(f.from,d,f,c),p<f.to&&a(p,f.to,f,c)};for(let f of this.viewportLines)Array.isArray(f.type)?f.type.forEach(h):h(f);return l}gapSize(t,e,i,n){let r=kn(n,i)-kn(n,e);return this.heightOracle.lineWrapping?t.height*r:n.total*this.heightOracle.charWidth*r}updateLineGaps(t){Bs.same(t,this.lineGaps)||(this.lineGaps=t,this.lineGapDeco=N.set(t.map(e=>e.draw(this,this.heightOracle.lineWrapping))))}computeVisibleRanges(t){let e=this.stateDeco;this.lineGaps.length&&(e=e.concat(this.lineGapDeco));let i=[];W.spans(e,this.viewport.from,this.viewport.to,{span(r,o){i.push({from:r,to:o})},point(){}},20);let n=0;if(i.length!=this.visibleRanges.length)n=12;else for(let r=0;r<i.length&&!(n&8);r++){let o=this.visibleRanges[r],l=i[r];(o.from!=l.from||o.to!=l.to)&&(n|=4,t&&t.mapPos(o.from,-1)==l.from&&t.mapPos(o.to,1)==l.to||(n|=8))}return this.visibleRanges=i,n}lineBlockAt(t){return t>=this.viewport.from&&t<=this.viewport.to&&this.viewportLines.find(e=>e.from<=t&&e.to>=t)||Ei(this.heightMap.lineAt(t,Y.ByPos,this.heightOracle,0,0),this.scaler)}lineBlockAtHeight(t){return t>=this.viewportLines[0].top&&t<=this.viewportLines[this.viewportLines.length-1].bottom&&this.viewportLines.find(e=>e.top<=t&&e.bottom>=t)||Ei(this.heightMap.lineAt(this.scaler.fromDOM(t),Y.ByHeight,this.heightOracle,0,0),this.scaler)}scrollAnchorAt(t){let e=this.lineBlockAtHeight(t+8);return e.from>=this.viewport.from||this.viewportLines[0].top-t>200?e:this.viewportLines[0]}elementAtHeight(t){return Ei(this.heightMap.blockAt(this.scaler.fromDOM(t),this.heightOracle,0,0),this.scaler)}get docHeight(){return this.scaler.toDOM(this.heightMap.height)}get contentHeight(){return this.docHeight+this.paddingTop+this.paddingBottom}}class wn{constructor(t,e){this.from=t,this.to=e}}function Yd(s,t,e){let i=[],n=s,r=0;return W.spans(e,s,t,{span(){},point(o,l){o>n&&(i.push({from:n,to:o}),r+=o-n),n=l}},20),n<t&&(i.push({from:n,to:t}),r+=t-n),{total:r,ranges:i}}function xn({total:s,ranges:t},e){if(e<=0)return t[0].from;if(e>=1)return t[t.length-1].to;let i=Math.floor(s*e);for(let n=0;;n++){let{from:r,to:o}=t[n],l=o-r;if(i<=l)return r+i;i-=l}}function kn(s,t){let e=0;for(let{from:i,to:n}of s.ranges){if(t<=n){e+=t-i;break}e+=n-i}return e/s.total}function Jd(s,t){for(let e of s)if(t(e))return e}const Dl={toDOM(s){return s},fromDOM(s){return s},scale:1,eq(s){return s==this}};class lo{constructor(t,e,i){let n=0,r=0,o=0;this.viewports=i.map(({from:l,to:a})=>{let h=e.lineAt(l,Y.ByPos,t,0,0).top,f=e.lineAt(a,Y.ByPos,t,0,0).bottom;return n+=f-h,{from:l,to:a,top:h,bottom:f,domTop:0,domBottom:0}}),this.scale=(7e6-n)/(e.height-n);for(let l of this.viewports)l.domTop=o+(l.top-r)*this.scale,o=l.domBottom=l.domTop+(l.bottom-l.top),r=l.bottom}toDOM(t){for(let e=0,i=0,n=0;;e++){let r=e<this.viewports.length?this.viewports[e]:null;if(!r||t<r.top)return n+(t-i)*this.scale;if(t<=r.bottom)return r.domTop+(t-r.top);i=r.bottom,n=r.domBottom}}fromDOM(t){for(let e=0,i=0,n=0;;e++){let r=e<this.viewports.length?this.viewports[e]:null;if(!r||t<r.domTop)return i+(t-n)/this.scale;if(t<=r.domBottom)return r.top+(t-r.domTop);i=r.bottom,n=r.domBottom}}eq(t){return t instanceof lo?this.scale==t.scale&&this.viewports.length==t.viewports.length&&this.viewports.every((e,i)=>e.from==t.viewports[i].from&&e.to==t.viewports[i].to):!1}}function Ei(s,t){if(t.scale==1)return s;let e=t.toDOM(s.top),i=t.toDOM(s.bottom);return new oe(s.from,s.length,e,i-e,Array.isArray(s._content)?s._content.map(n=>Ei(n,t)):s._content)}const vn=B.define({combine:s=>s.join(" ")}),Tr=B.define({combine:s=>s.indexOf(!0)>-1}),Or=Te.newName(),lf=Te.newName(),af=Te.newName(),hf={"&light":"."+lf,"&dark":"."+af};function Br(s,t,e){return new Te(t,{finish(i){return/&/.test(i)?i.replace(/&\w*/,n=>{if(n=="&")return s;if(!e||!e[n])throw new RangeError(`Unsupported selector: ${n}`);return e[n]}):s+" "+i}})}const Xd=Br("."+Or,{"&":{position:"relative !important",boxSizing:"border-box","&.cm-focused":{outline:"1px dotted #212121"},display:"flex !important",flexDirection:"column"},".cm-scroller":{display:"flex !important",alignItems:"flex-start !important",fontFamily:"monospace",lineHeight:1.4,height:"100%",overflowX:"auto",position:"relative",zIndex:0,overflowAnchor:"none"},".cm-content":{margin:0,flexGrow:2,flexShrink:0,display:"block",whiteSpace:"pre",wordWrap:"normal",boxSizing:"border-box",minHeight:"100%",padding:"4px 0",outline:"none","&[contenteditable=true]":{WebkitUserModify:"read-write-plaintext-only"}},".cm-lineWrapping":{whiteSpace_fallback:"pre-wrap",whiteSpace:"break-spaces",wordBreak:"break-word",overflowWrap:"anywhere",flexShrink:1},"&light .cm-content":{caretColor:"black"},"&dark .cm-content":{caretColor:"white"},".cm-line":{display:"block",padding:"0 2px 0 6px"},".cm-layer":{position:"absolute",left:0,top:0,contain:"size style","& > *":{position:"absolute"}},"&light .cm-selectionBackground":{background:"#d9d9d9"},"&dark .cm-selectionBackground":{background:"#222"},"&light.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#d7d4f0"},"&dark.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#233"},".cm-cursorLayer":{pointerEvents:"none"},"&.cm-focused > .cm-scroller > .cm-cursorLayer":{animation:"steps(1) cm-blink 1.2s infinite"},"@keyframes cm-blink":{"0%":{},"50%":{opacity:0},"100%":{}},"@keyframes cm-blink2":{"0%":{},"50%":{opacity:0},"100%":{}},".cm-cursor, .cm-dropCursor":{borderLeft:"1.2px solid black",marginLeft:"-0.6px",pointerEvents:"none"},".cm-cursor":{display:"none"},"&dark .cm-cursor":{borderLeftColor:"#ddd"},".cm-dropCursor":{position:"absolute"},"&.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor":{display:"block"},".cm-iso":{unicodeBidi:"isolate"},".cm-announced":{position:"fixed",top:"-10000px"},"@media print":{".cm-announced":{display:"none"}},"&light .cm-activeLine":{backgroundColor:"#cceeff44"},"&dark .cm-activeLine":{backgroundColor:"#99eeff33"},"&light .cm-specialChar":{color:"red"},"&dark .cm-specialChar":{color:"#f78"},".cm-gutters":{flexShrink:0,display:"flex",height:"100%",boxSizing:"border-box",insetInlineStart:0,zIndex:200},"&light .cm-gutters":{backgroundColor:"#f5f5f5",color:"#6c6c6c",borderRight:"1px solid #ddd"},"&dark .cm-gutters":{backgroundColor:"#333338",color:"#ccc"},".cm-gutter":{display:"flex !important",flexDirection:"column",flexShrink:0,boxSizing:"border-box",minHeight:"100%",overflow:"hidden"},".cm-gutterElement":{boxSizing:"border-box"},".cm-lineNumbers .cm-gutterElement":{padding:"0 3px 0 5px",minWidth:"20px",textAlign:"right",whiteSpace:"nowrap"},"&light .cm-activeLineGutter":{backgroundColor:"#e2f2ff"},"&dark .cm-activeLineGutter":{backgroundColor:"#222227"},".cm-panels":{boxSizing:"border-box",position:"sticky",left:0,right:0,zIndex:300},"&light .cm-panels":{backgroundColor:"#f5f5f5",color:"black"},"&light .cm-panels-top":{borderBottom:"1px solid #ddd"},"&light .cm-panels-bottom":{borderTop:"1px solid #ddd"},"&dark .cm-panels":{backgroundColor:"#333338",color:"white"},".cm-tab":{display:"inline-block",overflow:"hidden",verticalAlign:"bottom"},".cm-widgetBuffer":{verticalAlign:"text-top",height:"1em",width:0,display:"inline"},".cm-placeholder":{color:"#888",display:"inline-block",verticalAlign:"top"},".cm-highlightSpace":{backgroundImage:"radial-gradient(circle at 50% 55%, #aaa 20%, transparent 5%)",backgroundPosition:"center"},".cm-highlightTab":{backgroundImage:`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>')`,backgroundSize:"auto 100%",backgroundPosition:"right 90%",backgroundRepeat:"no-repeat"},".cm-trailingSpace":{backgroundColor:"#ff332255"},".cm-button":{verticalAlign:"middle",color:"inherit",fontSize:"70%",padding:".2em 1em",borderRadius:"1px"},"&light .cm-button":{backgroundImage:"linear-gradient(#eff1f5, #d9d9df)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#b4b4b4, #d0d3d6)"}},"&dark .cm-button":{backgroundImage:"linear-gradient(#393939, #111)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#111, #333)"}},".cm-textfield":{verticalAlign:"middle",color:"inherit",fontSize:"70%",border:"1px solid silver",padding:".2em .5em"},"&light .cm-textfield":{backgroundColor:"white"},"&dark .cm-textfield":{border:"1px solid #555",backgroundColor:"inherit"}},hf),$d={childList:!0,characterData:!0,subtree:!0,attributes:!0,characterDataOldValue:!0},Es=T.ie&&T.ie_version<=11;class Qd{constructor(t){this.view=t,this.active=!1,this.editContext=null,this.selectionRange=new Iu,this.selectionChanged=!1,this.delayedFlush=-1,this.resizeTimeout=-1,this.queue=[],this.delayedAndroidKey=null,this.flushingAndroidKey=-1,this.lastChange=0,this.scrollTargets=[],this.intersection=null,this.resizeScroll=null,this.intersecting=!1,this.gapIntersection=null,this.gaps=[],this.printQuery=null,this.parentCheck=-1,this.dom=t.contentDOM,this.observer=new MutationObserver(e=>{for(let i of e)this.queue.push(i);(T.ie&&T.ie_version<=11||T.ios&&t.composing)&&e.some(i=>i.type=="childList"&&i.removedNodes.length||i.type=="characterData"&&i.oldValue.length>i.target.nodeValue.length)?this.flushSoon():this.flush()}),window.EditContext&&t.constructor.EDIT_CONTEXT!==!1&&!(T.chrome&&T.chrome_version<126)&&(this.editContext=new tp(t),t.state.facet(me)&&(t.contentDOM.editContext=this.editContext.editContext)),Es&&(this.onCharData=e=>{this.queue.push({target:e.target,type:"characterData",oldValue:e.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this),this.onResize=this.onResize.bind(this),this.onPrint=this.onPrint.bind(this),this.onScroll=this.onScroll.bind(this),window.matchMedia&&(this.printQuery=window.matchMedia("print")),typeof ResizeObserver=="function"&&(this.resizeScroll=new ResizeObserver(()=>{var e;((e=this.view.docView)===null||e===void 0?void 0:e.lastUpdate)<Date.now()-75&&this.onResize()}),this.resizeScroll.observe(t.scrollDOM)),this.addWindowListeners(this.win=t.win),this.start(),typeof IntersectionObserver=="function"&&(this.intersection=new IntersectionObserver(e=>{this.parentCheck<0&&(this.parentCheck=setTimeout(this.listenForScroll.bind(this),1e3)),e.length>0&&e[e.length-1].intersectionRatio>0!=this.intersecting&&(this.intersecting=!this.intersecting,this.intersecting!=this.view.inView&&this.onScrollChanged(document.createEvent("Event")))},{threshold:[0,.001]}),this.intersection.observe(this.dom),this.gapIntersection=new IntersectionObserver(e=>{e.length>0&&e[e.length-1].intersectionRatio>0&&this.onScrollChanged(document.createEvent("Event"))},{})),this.listenForScroll(),this.readSelectionRange()}onScrollChanged(t){this.view.inputState.runHandlers("scroll",t),this.intersecting&&this.view.measure()}onScroll(t){this.intersecting&&this.flush(!1),this.editContext&&this.view.requestMeasure(this.editContext.measureReq),this.onScrollChanged(t)}onResize(){this.resizeTimeout<0&&(this.resizeTimeout=setTimeout(()=>{this.resizeTimeout=-1,this.view.requestMeasure()},50))}onPrint(t){(t.type=="change"||!t.type)&&!t.matches||(this.view.viewState.printing=!0,this.view.measure(),setTimeout(()=>{this.view.viewState.printing=!1,this.view.requestMeasure()},500))}updateGaps(t){if(this.gapIntersection&&(t.length!=this.gaps.length||this.gaps.some((e,i)=>e!=t[i]))){this.gapIntersection.disconnect();for(let e of t)this.gapIntersection.observe(e);this.gaps=t}}onSelectionChange(t){let e=this.selectionChanged;if(!this.readSelectionRange()||this.delayedAndroidKey)return;let{view:i}=this,n=this.selectionRange;if(i.state.facet(me)?i.root.activeElement!=this.dom:!Fn(this.dom,n))return;let r=n.anchorNode&&i.docView.nearest(n.anchorNode);if(r&&r.ignoreEvent(t)){e||(this.selectionChanged=!1);return}(T.ie&&T.ie_version<=11||T.android&&T.chrome)&&!i.state.selection.main.empty&&n.focusNode&&Ri(n.focusNode,n.focusOffset,n.anchorNode,n.anchorOffset)?this.flushSoon():this.flush(!1)}readSelectionRange(){let{view:t}=this,e=Wi(t.root);if(!e)return!1;let i=T.safari&&t.root.nodeType==11&&t.root.activeElement==this.dom&&Zd(this.view,e)||e;if(!i||this.selectionRange.eq(i))return!1;let n=Fn(this.dom,i);return n&&!this.selectionChanged&&t.inputState.lastFocusTime>Date.now()-200&&t.inputState.lastTouchTime<Date.now()-300&&_u(this.dom,i)?(this.view.inputState.lastFocusTime=0,t.docView.updateSelection(),!1):(this.selectionRange.setRange(i),n&&(this.selectionChanged=!0),!0)}setSelectionRange(t,e){this.selectionRange.set(t.node,t.offset,e.node,e.offset),this.selectionChanged=!1}clearSelectionRange(){this.selectionRange.set(null,0,null,0)}listenForScroll(){this.parentCheck=-1;let t=0,e=null;for(let i=this.dom;i;)if(i.nodeType==1)!e&&t<this.scrollTargets.length&&this.scrollTargets[t]==i?t++:e||(e=this.scrollTargets.slice(0,t)),e&&e.push(i),i=i.assignedSlot||i.parentNode;else if(i.nodeType==11)i=i.host;else break;if(t<this.scrollTargets.length&&!e&&(e=this.scrollTargets.slice(0,t)),e){for(let i of this.scrollTargets)i.removeEventListener("scroll",this.onScroll);for(let i of this.scrollTargets=e)i.addEventListener("scroll",this.onScroll)}}ignore(t){if(!this.active)return t();try{return this.stop(),t()}finally{this.start(),this.clear()}}start(){this.active||(this.observer.observe(this.dom,$d),Es&&this.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.active=!0)}stop(){this.active&&(this.active=!1,this.observer.disconnect(),Es&&this.dom.removeEventListener("DOMCharacterDataModified",this.onCharData))}clear(){this.processRecords(),this.queue.length=0,this.selectionChanged=!1}delayAndroidKey(t,e){var i;if(!this.delayedAndroidKey){let n=()=>{let r=this.delayedAndroidKey;r&&(this.clearDelayedAndroidKey(),this.view.inputState.lastKeyCode=r.keyCode,this.view.inputState.lastKeyTime=Date.now(),!this.flush()&&r.force&&ri(this.dom,r.key,r.keyCode))};this.flushingAndroidKey=this.view.win.requestAnimationFrame(n)}(!this.delayedAndroidKey||t=="Enter")&&(this.delayedAndroidKey={key:t,keyCode:e,force:this.lastChange<Date.now()-50||!!(!((i=this.delayedAndroidKey)===null||i===void 0)&&i.force)})}clearDelayedAndroidKey(){this.win.cancelAnimationFrame(this.flushingAndroidKey),this.delayedAndroidKey=null,this.flushingAndroidKey=-1}flushSoon(){this.delayedFlush<0&&(this.delayedFlush=this.view.win.requestAnimationFrame(()=>{this.delayedFlush=-1,this.flush()}))}forceFlush(){this.delayedFlush>=0&&(this.view.win.cancelAnimationFrame(this.delayedFlush),this.delayedFlush=-1),this.flush()}pendingRecords(){for(let t of this.observer.takeRecords())this.queue.push(t);return this.queue}processRecords(){let t=this.pendingRecords();t.length&&(this.queue=[]);let e=-1,i=-1,n=!1;for(let r of t){let o=this.readMutation(r);o&&(o.typeOver&&(n=!0),e==-1?{from:e,to:i}=o:(e=Math.min(o.from,e),i=Math.max(o.to,i)))}return{from:e,to:i,typeOver:n}}readChange(){let{from:t,to:e,typeOver:i}=this.processRecords(),n=this.selectionChanged&&Fn(this.dom,this.selectionRange);if(t<0&&!n)return null;t>-1&&(this.lastChange=Date.now()),this.view.inputState.lastFocusTime=0,this.selectionChanged=!1;let r=new gd(this.view,t,e,i);return this.view.docView.domChanged={newSel:r.newSel?r.newSel.main:null},r}flush(t=!0){if(this.delayedFlush>=0||this.delayedAndroidKey)return!1;t&&this.readSelectionRange();let e=this.readChange();if(!e)return this.view.requestMeasure(),!1;let i=this.view.state,n=Xh(this.view,e);return this.view.state==i&&(e.domChanged||e.newSel&&!e.newSel.main.eq(this.view.state.selection.main))&&this.view.update([]),n}readMutation(t){let e=this.view.docView.nearest(t.target);if(!e||e.ignoreMutation(t))return null;if(e.markDirty(t.type=="attributes"),t.type=="attributes"&&(e.flags|=4),t.type=="childList"){let i=Tl(e,t.previousSibling||t.target.previousSibling,-1),n=Tl(e,t.nextSibling||t.target.nextSibling,1);return{from:i?e.posAfter(i):e.posAtStart,to:n?e.posBefore(n):e.posAtEnd,typeOver:!1}}else return t.type=="characterData"?{from:e.posAtStart,to:e.posAtEnd,typeOver:t.target.nodeValue==t.oldValue}:null}setWindow(t){t!=this.win&&(this.removeWindowListeners(this.win),this.win=t,this.addWindowListeners(this.win))}addWindowListeners(t){t.addEventListener("resize",this.onResize),this.printQuery?this.printQuery.addEventListener?this.printQuery.addEventListener("change",this.onPrint):this.printQuery.addListener(this.onPrint):t.addEventListener("beforeprint",this.onPrint),t.addEventListener("scroll",this.onScroll),t.document.addEventListener("selectionchange",this.onSelectionChange)}removeWindowListeners(t){t.removeEventListener("scroll",this.onScroll),t.removeEventListener("resize",this.onResize),this.printQuery?this.printQuery.removeEventListener?this.printQuery.removeEventListener("change",this.onPrint):this.printQuery.removeListener(this.onPrint):t.removeEventListener("beforeprint",this.onPrint),t.document.removeEventListener("selectionchange",this.onSelectionChange)}update(t){this.editContext&&(this.editContext.update(t),t.startState.facet(me)!=t.state.facet(me)&&(t.view.contentDOM.editContext=t.state.facet(me)?this.editContext.editContext:null))}destroy(){var t,e,i;this.stop(),(t=this.intersection)===null||t===void 0||t.disconnect(),(e=this.gapIntersection)===null||e===void 0||e.disconnect(),(i=this.resizeScroll)===null||i===void 0||i.disconnect();for(let n of this.scrollTargets)n.removeEventListener("scroll",this.onScroll);this.removeWindowListeners(this.win),clearTimeout(this.parentCheck),clearTimeout(this.resizeTimeout),this.win.cancelAnimationFrame(this.delayedFlush),this.win.cancelAnimationFrame(this.flushingAndroidKey),this.editContext&&(this.view.contentDOM.editContext=null,this.editContext.destroy())}}function Tl(s,t,e){for(;t;){let i=U.get(t);if(i&&i.parent==s)return i;let n=t.parentNode;t=n!=s.dom?n:e>0?t.nextSibling:t.previousSibling}return null}function Ol(s,t){let e=t.startContainer,i=t.startOffset,n=t.endContainer,r=t.endOffset,o=s.docView.domAtPos(s.state.selection.main.anchor);return Ri(o.node,o.offset,n,r)&&([e,i,n,r]=[n,r,e,i]),{anchorNode:e,anchorOffset:i,focusNode:n,focusOffset:r}}function Zd(s,t){if(t.getComposedRanges){let n=t.getComposedRanges(s.root)[0];if(n)return Ol(s,n)}let e=null;function i(n){n.preventDefault(),n.stopImmediatePropagation(),e=n.getTargetRanges()[0]}return s.contentDOM.addEventListener("beforeinput",i,!0),s.dom.ownerDocument.execCommand("indent"),s.contentDOM.removeEventListener("beforeinput",i,!0),e?Ol(s,e):null}class tp{constructor(t){this.from=0,this.to=0,this.pendingContextChange=null,this.handlers=Object.create(null),this.composing=null,this.resetRange(t.state);let e=this.editContext=new window.EditContext({text:t.state.doc.sliceString(this.from,this.to),selectionStart:this.toContextPos(Math.max(this.from,Math.min(this.to,t.state.selection.main.anchor))),selectionEnd:this.toContextPos(t.state.selection.main.head)});this.handlers.textupdate=i=>{let n=t.state.selection.main,{anchor:r,head:o}=n,l=this.toEditorPos(i.updateRangeStart),a=this.toEditorPos(i.updateRangeEnd);t.inputState.composing>=0&&!this.composing&&(this.composing={contextBase:i.updateRangeStart,editorBase:l,drifted:!1});let h={from:l,to:a,insert:V.of(i.text.split(`
`))};if(h.from==this.from&&r<this.from?h.from=r:h.to==this.to&&r>this.to&&(h.to=r),h.from==h.to&&!h.insert.length){let f=k.single(this.toEditorPos(i.selectionStart),this.toEditorPos(i.selectionEnd));f.main.eq(n)||t.dispatch({selection:f,userEvent:"select"});return}if((T.mac||T.android)&&h.from==o-1&&/^\. ?$/.test(i.text)&&t.contentDOM.getAttribute("autocorrect")=="off"&&(h={from:l,to:a,insert:V.of([i.text.replace("."," ")])}),this.pendingContextChange=h,!t.state.readOnly){let f=this.to-this.from+(h.to-h.from+h.insert.length);ro(t,h,k.single(this.toEditorPos(i.selectionStart,f),this.toEditorPos(i.selectionEnd,f)))}this.pendingContextChange&&(this.revertPending(t.state),this.setSelection(t.state))},this.handlers.characterboundsupdate=i=>{let n=[],r=null;for(let o=this.toEditorPos(i.rangeStart),l=this.toEditorPos(i.rangeEnd);o<l;o++){let a=t.coordsForChar(o);r=a&&new DOMRect(a.left,a.top,a.right-a.left,a.bottom-a.top)||r||new DOMRect,n.push(r)}e.updateCharacterBounds(i.rangeStart,n)},this.handlers.textformatupdate=i=>{let n=[];for(let r of i.getTextFormats()){let o=r.underlineStyle,l=r.underlineThickness;if(o!="None"&&l!="None"){let a=this.toEditorPos(r.rangeStart),h=this.toEditorPos(r.rangeEnd);if(a<h){let f=`text-decoration: underline ${o=="Dashed"?"dashed ":o=="Squiggle"?"wavy ":""}${l=="Thin"?1:2}px`;n.push(N.mark({attributes:{style:f}}).range(a,h))}}}t.dispatch({effects:qh.of(N.set(n))})},this.handlers.compositionstart=()=>{t.inputState.composing<0&&(t.inputState.composing=0,t.inputState.compositionFirstChange=!0)},this.handlers.compositionend=()=>{if(t.inputState.composing=-1,t.inputState.compositionFirstChange=null,this.composing){let{drifted:i}=this.composing;this.composing=null,i&&this.reset(t.state)}};for(let i in this.handlers)e.addEventListener(i,this.handlers[i]);this.measureReq={read:i=>{this.editContext.updateControlBounds(i.contentDOM.getBoundingClientRect());let n=Wi(i.root);n&&n.rangeCount&&this.editContext.updateSelectionBounds(n.getRangeAt(0).getBoundingClientRect())}}}applyEdits(t){let e=0,i=!1,n=this.pendingContextChange;return t.changes.iterChanges((r,o,l,a,h)=>{if(i)return;let f=h.length-(o-r);if(n&&o>=n.to)if(n.from==r&&n.to==o&&n.insert.eq(h)){n=this.pendingContextChange=null,e+=f,this.to+=f;return}else n=null,this.revertPending(t.state);if(r+=e,o+=e,o<=this.from)this.from+=f,this.to+=f;else if(r<this.to){if(r<this.from||o>this.to||this.to-this.from+h.length>3e4){i=!0;return}this.editContext.updateText(this.toContextPos(r),this.toContextPos(o),h.toString()),this.to+=f}e+=f}),n&&!i&&this.revertPending(t.state),!i}update(t){let e=this.pendingContextChange,i=t.startState.selection.main;this.composing&&(this.composing.drifted||!t.changes.touchesRange(i.from,i.to)&&t.transactions.some(n=>!n.isUserEvent("input.type")&&n.changes.touchesRange(this.from,this.to)))?(this.composing.drifted=!0,this.composing.editorBase=t.changes.mapPos(this.composing.editorBase)):!this.applyEdits(t)||!this.rangeIsValid(t.state)?(this.pendingContextChange=null,this.reset(t.state)):(t.docChanged||t.selectionSet||e)&&this.setSelection(t.state),(t.geometryChanged||t.docChanged||t.selectionSet)&&t.view.requestMeasure(this.measureReq)}resetRange(t){let{head:e}=t.selection.main;this.from=Math.max(0,e-1e4),this.to=Math.min(t.doc.length,e+1e4)}reset(t){this.resetRange(t),this.editContext.updateText(0,this.editContext.text.length,t.doc.sliceString(this.from,this.to)),this.setSelection(t)}revertPending(t){let e=this.pendingContextChange;this.pendingContextChange=null,this.editContext.updateText(this.toContextPos(e.from),this.toContextPos(e.from+e.insert.length),t.doc.sliceString(e.from,e.to))}setSelection(t){let{main:e}=t.selection,i=this.toContextPos(Math.max(this.from,Math.min(this.to,e.anchor))),n=this.toContextPos(e.head);(this.editContext.selectionStart!=i||this.editContext.selectionEnd!=n)&&this.editContext.updateSelection(i,n)}rangeIsValid(t){let{head:e}=t.selection.main;return!(this.from>0&&e-this.from<500||this.to<t.doc.length&&this.to-e<500||this.to-this.from>1e4*3)}toEditorPos(t,e=this.to-this.from){t=Math.min(t,e);let i=this.composing;return i&&i.drifted?i.editorBase+(t-i.contextBase):t+this.from}toContextPos(t){let e=this.composing;return e&&e.drifted?e.contextBase+(t-e.editorBase):t-this.from}destroy(){for(let t in this.handlers)this.editContext.removeEventListener(t,this.handlers[t])}}class E{get state(){return this.viewState.state}get viewport(){return this.viewState.viewport}get visibleRanges(){return this.viewState.visibleRanges}get inView(){return this.viewState.inView}get composing(){return this.inputState.composing>0}get compositionStarted(){return this.inputState.composing>=0}get root(){return this._root}get win(){return this.dom.ownerDocument.defaultView||window}constructor(t={}){var e;this.plugins=[],this.pluginMap=new Map,this.editorAttrs={},this.contentAttrs={},this.bidiCache=[],this.destroyed=!1,this.updateState=2,this.measureScheduled=-1,this.measureRequests=[],this.contentDOM=document.createElement("div"),this.scrollDOM=document.createElement("div"),this.scrollDOM.tabIndex=-1,this.scrollDOM.className="cm-scroller",this.scrollDOM.appendChild(this.contentDOM),this.announceDOM=document.createElement("div"),this.announceDOM.className="cm-announced",this.announceDOM.setAttribute("aria-live","polite"),this.dom=document.createElement("div"),this.dom.appendChild(this.announceDOM),this.dom.appendChild(this.scrollDOM),t.parent&&t.parent.appendChild(this.dom);let{dispatch:i}=t;this.dispatchTransactions=t.dispatchTransactions||i&&(n=>n.forEach(r=>i(r,this)))||(n=>this.update(n)),this.dispatch=this.dispatch.bind(this),this._root=t.root||Nu(t.parent)||document,this.viewState=new Ml(t.state||H.create(t)),t.scrollTo&&t.scrollTo.is(gn)&&(this.viewState.scrollTarget=t.scrollTo.value.clip(this.viewState.state)),this.plugins=this.state.facet(Ti).map(n=>new Ds(n));for(let n of this.plugins)n.update(this);this.observer=new Qd(this),this.inputState=new kd(this),this.inputState.ensureHandlers(this.plugins),this.docView=new ll(this),this.mountStyles(),this.updateAttrs(),this.updateState=0,this.requestMeasure(),!((e=document.fonts)===null||e===void 0)&&e.ready&&document.fonts.ready.then(()=>this.requestMeasure())}dispatch(...t){let e=t.length==1&&t[0]instanceof tt?t:t.length==1&&Array.isArray(t[0])?t[0]:[this.state.update(...t)];this.dispatchTransactions(e,this)}update(t){if(this.updateState!=0)throw new Error("Calls to EditorView.update are not allowed while an update is in progress");let e=!1,i=!1,n,r=this.state;for(let u of t){if(u.startState!=r)throw new RangeError("Trying to update state with a transaction that doesn't start from the previous state.");r=u.state}if(this.destroyed){this.viewState.state=r;return}let o=this.hasFocus,l=0,a=null;t.some(u=>u.annotation(nf))?(this.inputState.notifiedFocused=o,l=1):o!=this.inputState.notifiedFocused&&(this.inputState.notifiedFocused=o,a=sf(r,o),a||(l=1));let h=this.observer.delayedAndroidKey,f=null;if(h?(this.observer.clearDelayedAndroidKey(),f=this.observer.readChange(),(f&&!this.state.doc.eq(r.doc)||!this.state.selection.eq(r.selection))&&(f=null)):this.observer.clear(),r.facet(H.phrases)!=this.state.facet(H.phrases))return this.setState(r);n=Qn.create(this,r,t),n.flags|=l;let c=this.viewState.scrollTarget;try{this.updateState=2;for(let u of t){if(c&&(c=c.map(u.changes)),u.scrollIntoView){let{main:d}=u.state.selection;c=new oi(d.empty?d:k.cursor(d.head,d.head>d.anchor?-1:1))}for(let d of u.effects)d.is(gn)&&(c=d.value.clip(this.state))}this.viewState.update(n,c),this.bidiCache=ts.update(this.bidiCache,n.changes),n.empty||(this.updatePlugins(n),this.inputState.update(n)),e=this.docView.update(n),this.state.facet(Oi)!=this.styleModules&&this.mountStyles(),i=this.updateAttrs(),this.showAnnouncements(t),this.docView.updateSelection(e,t.some(u=>u.isUserEvent("select.pointer")))}finally{this.updateState=0}if(n.startState.facet(vn)!=n.state.facet(vn)&&(this.viewState.mustMeasureContent=!0),(e||i||c||this.viewState.mustEnforceCursorAssoc||this.viewState.mustMeasureContent)&&this.requestMeasure(),e&&this.docViewUpdate(),!n.empty)for(let u of this.state.facet(Cr))try{u(n)}catch(d){Mt(this.state,d,"update listener")}(a||f)&&Promise.resolve().then(()=>{a&&this.state==a.startState&&this.dispatch(a),f&&!Xh(this,f)&&h.force&&ri(this.contentDOM,h.key,h.keyCode)})}setState(t){if(this.updateState!=0)throw new Error("Calls to EditorView.setState are not allowed while an update is in progress");if(this.destroyed){this.viewState.state=t;return}this.updateState=2;let e=this.hasFocus;try{for(let i of this.plugins)i.destroy(this);this.viewState=new Ml(t),this.plugins=t.facet(Ti).map(i=>new Ds(i)),this.pluginMap.clear();for(let i of this.plugins)i.update(this);this.docView.destroy(),this.docView=new ll(this),this.inputState.ensureHandlers(this.plugins),this.mountStyles(),this.updateAttrs(),this.bidiCache=[]}finally{this.updateState=0}e&&this.focus(),this.requestMeasure()}updatePlugins(t){let e=t.startState.facet(Ti),i=t.state.facet(Ti);if(e!=i){let n=[];for(let r of i){let o=e.indexOf(r);if(o<0)n.push(new Ds(r));else{let l=this.plugins[o];l.mustUpdate=t,n.push(l)}}for(let r of this.plugins)r.mustUpdate!=t&&r.destroy(this);this.plugins=n,this.pluginMap.clear()}else for(let n of this.plugins)n.mustUpdate=t;for(let n=0;n<this.plugins.length;n++)this.plugins[n].update(this);e!=i&&this.inputState.ensureHandlers(this.plugins)}docViewUpdate(){for(let t of this.plugins){let e=t.value;if(e&&e.docViewUpdate)try{e.docViewUpdate(this)}catch(i){Mt(this.state,i,"doc view update listener")}}}measure(t=!0){if(this.destroyed)return;if(this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.observer.delayedAndroidKey){this.measureScheduled=-1,this.requestMeasure();return}this.measureScheduled=0,t&&this.observer.forceFlush();let e=null,i=this.scrollDOM,n=i.scrollTop*this.scaleY,{scrollAnchorPos:r,scrollAnchorHeight:o}=this.viewState;Math.abs(n-this.viewState.scrollTop)>1&&(o=-1),this.viewState.scrollAnchorHeight=-1;try{for(let l=0;;l++){if(o<0)if(bh(i))r=-1,o=this.viewState.heightMap.height;else{let d=this.viewState.scrollAnchorAt(n);r=d.from,o=d.top}this.updateState=1;let a=this.viewState.measure(this);if(!a&&!this.measureRequests.length&&this.viewState.scrollTarget==null)break;if(l>5){console.warn(this.measureRequests.length?"Measure loop restarted more than 5 times":"Viewport failed to stabilize");break}let h=[];a&4||([this.measureRequests,h]=[h,this.measureRequests]);let f=h.map(d=>{try{return d.read(this)}catch(p){return Mt(this.state,p),Bl}}),c=Qn.create(this,this.state,[]),u=!1;c.flags|=a,e?e.flags|=a:e=c,this.updateState=2,c.empty||(this.updatePlugins(c),this.inputState.update(c),this.updateAttrs(),u=this.docView.update(c),u&&this.docViewUpdate());for(let d=0;d<h.length;d++)if(f[d]!=Bl)try{let p=h[d];p.write&&p.write(f[d],this)}catch(p){Mt(this.state,p)}if(u&&this.docView.updateSelection(!0),!c.viewportChanged&&this.measureRequests.length==0){if(this.viewState.editorHeight)if(this.viewState.scrollTarget){this.docView.scrollIntoView(this.viewState.scrollTarget),this.viewState.scrollTarget=null,o=-1;continue}else{let p=(r<0?this.viewState.heightMap.height:this.viewState.lineBlockAt(r).top)-o;if(p>1||p<-1){n=n+p,i.scrollTop=n/this.scaleY,o=-1;continue}}break}}}finally{this.updateState=0,this.measureScheduled=-1}if(e&&!e.empty)for(let l of this.state.facet(Cr))l(e)}get themeClasses(){return Or+" "+(this.state.facet(Tr)?af:lf)+" "+this.state.facet(vn)}updateAttrs(){let t=El(this,jh,{class:"cm-editor"+(this.hasFocus?" cm-focused ":" ")+this.themeClasses}),e={spellcheck:"false",autocorrect:"off",autocapitalize:"off",writingsuggestions:"false",translate:"no",contenteditable:this.state.facet(me)?"true":"false",class:"cm-content",style:`${T.tabSize}: ${this.state.tabSize}`,role:"textbox","aria-multiline":"true"};this.state.readOnly&&(e["aria-readonly"]="true"),El(this,io,e);let i=this.observer.ignore(()=>{let n=wr(this.contentDOM,this.contentAttrs,e),r=wr(this.dom,this.editorAttrs,t);return n||r});return this.editorAttrs=t,this.contentAttrs=e,i}showAnnouncements(t){let e=!0;for(let i of t)for(let n of i.effects)if(n.is(E.announce)){e&&(this.announceDOM.textContent=""),e=!1;let r=this.announceDOM.appendChild(document.createElement("div"));r.textContent=n.value}}mountStyles(){this.styleModules=this.state.facet(Oi);let t=this.state.facet(E.cspNonce);Te.mount(this.root,this.styleModules.concat(Xd).reverse(),t?{nonce:t}:void 0)}readMeasured(){if(this.updateState==2)throw new Error("Reading the editor layout isn't allowed during an update");this.updateState==0&&this.measureScheduled>-1&&this.measure(!1)}requestMeasure(t){if(this.measureScheduled<0&&(this.measureScheduled=this.win.requestAnimationFrame(()=>this.measure())),t){if(this.measureRequests.indexOf(t)>-1)return;if(t.key!=null){for(let e=0;e<this.measureRequests.length;e++)if(this.measureRequests[e].key===t.key){this.measureRequests[e]=t;return}}this.measureRequests.push(t)}}plugin(t){let e=this.pluginMap.get(t);return(e===void 0||e&&e.spec!=t)&&this.pluginMap.set(t,e=this.plugins.find(i=>i.spec==t)||null),e&&e.update(this).value}get documentTop(){return this.contentDOM.getBoundingClientRect().top+this.viewState.paddingTop}get documentPadding(){return{top:this.viewState.paddingTop,bottom:this.viewState.paddingBottom}}get scaleX(){return this.viewState.scaleX}get scaleY(){return this.viewState.scaleY}elementAtHeight(t){return this.readMeasured(),this.viewState.elementAtHeight(t)}lineBlockAtHeight(t){return this.readMeasured(),this.viewState.lineBlockAtHeight(t)}get viewportLineBlocks(){return this.viewState.viewportLines}lineBlockAt(t){return this.viewState.lineBlockAt(t)}get contentHeight(){return this.viewState.contentHeight}moveByChar(t,e,i){return Os(this,t,ul(this,t,e,i))}moveByGroup(t,e){return Os(this,t,ul(this,t,e,i=>ud(this,t.head,i)))}visualLineSide(t,e){let i=this.bidiSpans(t),n=this.textDirectionAt(t.from),r=i[e?i.length-1:0];return k.cursor(r.side(e,n)+t.from,r.forward(!e,n)?1:-1)}moveToLineBoundary(t,e,i=!0){return cd(this,t,e,i)}moveVertically(t,e,i){return Os(this,t,dd(this,t,e,i))}domAtPos(t){return this.docView.domAtPos(t)}posAtDOM(t,e=0){return this.docView.posFromDOM(t,e)}posAtCoords(t,e=!0){return this.readMeasured(),Jh(this,t,e)}coordsAtPos(t,e=1){this.readMeasured();let i=this.docView.coordsAt(t,e);if(!i||i.left==i.right)return i;let n=this.state.doc.lineAt(t),r=this.bidiSpans(n),o=r[Ae.find(r,t-n.from,-1,e)];return nn(i,o.dir==J.LTR==e>0)}coordsForChar(t){return this.readMeasured(),this.docView.coordsForChar(t)}get defaultCharacterWidth(){return this.viewState.heightOracle.charWidth}get defaultLineHeight(){return this.viewState.heightOracle.lineHeight}get textDirection(){return this.viewState.defaultTextDirection}textDirectionAt(t){return!this.state.facet(Hh)||t<this.viewport.from||t>this.viewport.to?this.textDirection:(this.readMeasured(),this.docView.textDirectionAt(t))}get lineWrapping(){return this.viewState.heightOracle.lineWrapping}bidiSpans(t){if(t.length>ep)return Ph(t.length);let e=this.textDirectionAt(t.from),i;for(let r of this.bidiCache)if(r.from==t.from&&r.dir==e&&(r.fresh||Eh(r.isolates,i=ol(this,t))))return r.order;i||(i=ol(this,t));let n=Ju(t.text,e,i);return this.bidiCache.push(new ts(t.from,t.to,e,i,!0,n)),n}get hasFocus(){var t;return(this.dom.ownerDocument.hasFocus()||T.safari&&((t=this.inputState)===null||t===void 0?void 0:t.lastContextMenu)>Date.now()-3e4)&&this.root.activeElement==this.contentDOM}focus(){this.observer.ignore(()=>{mh(this.contentDOM),this.docView.updateSelection()})}setRoot(t){this._root!=t&&(this._root=t,this.observer.setWindow((t.nodeType==9?t:t.ownerDocument).defaultView||window),this.mountStyles())}destroy(){this.root.activeElement==this.contentDOM&&this.contentDOM.blur();for(let t of this.plugins)t.destroy(this);this.plugins=[],this.inputState.destroy(),this.docView.destroy(),this.dom.remove(),this.observer.destroy(),this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.destroyed=!0}static scrollIntoView(t,e={}){return gn.of(new oi(typeof t=="number"?k.cursor(t):t,e.y,e.x,e.yMargin,e.xMargin))}scrollSnapshot(){let{scrollTop:t,scrollLeft:e}=this.scrollDOM,i=this.viewState.scrollAnchorAt(t);return gn.of(new oi(k.cursor(i.from),"start","start",i.top-t,e,!0))}setTabFocusMode(t){t==null?this.inputState.tabFocusMode=this.inputState.tabFocusMode<0?0:-1:typeof t=="boolean"?this.inputState.tabFocusMode=t?0:-1:this.inputState.tabFocusMode!=0&&(this.inputState.tabFocusMode=Date.now()+t)}static domEventHandlers(t){return ut.define(()=>({}),{eventHandlers:t})}static domEventObservers(t){return ut.define(()=>({}),{eventObservers:t})}static theme(t,e){let i=Te.newName(),n=[vn.of(i),Oi.of(Br(`.${i}`,t))];return e&&e.dark&&n.push(Tr.of(!0)),n}static baseTheme(t){return Xe.lowest(Oi.of(Br("."+Or,t,hf)))}static findFromDOM(t){var e;let i=t.querySelector(".cm-content"),n=i&&U.get(i)||U.get(t);return((e=n==null?void 0:n.rootView)===null||e===void 0?void 0:e.view)||null}}E.styleModule=Oi;E.inputHandler=Fh;E.clipboardInputFilter=to;E.clipboardOutputFilter=eo;E.scrollHandler=zh;E.focusChangeEffect=Vh;E.perLineTextDirection=Hh;E.exceptionSink=_h;E.updateListener=Cr;E.editable=me;E.mouseSelectionStyle=Nh;E.dragMovesSelection=Ih;E.clickAddsSelectionRange=Rh;E.decorations=zi;E.outerDecorations=Kh;E.atomicRanges=no;E.bidiIsolatedRanges=Uh;E.scrollMargins=Gh;E.darkTheme=Tr;E.cspNonce=B.define({combine:s=>s.length?s[0]:""});E.contentAttributes=io;E.editorAttributes=jh;E.lineWrapping=E.contentAttributes.of({class:"cm-lineWrapping"});E.announce=I.define();const ep=4096,Bl={};class ts{constructor(t,e,i,n,r,o){this.from=t,this.to=e,this.dir=i,this.isolates=n,this.fresh=r,this.order=o}static update(t,e){if(e.empty&&!t.some(r=>r.fresh))return t;let i=[],n=t.length?t[t.length-1].dir:J.LTR;for(let r=Math.max(0,t.length-10);r<t.length;r++){let o=t[r];o.dir==n&&!e.touchesRange(o.from,o.to)&&i.push(new ts(e.mapPos(o.from,1),e.mapPos(o.to,-1),o.dir,o.isolates,!1,o.order))}return i}}function El(s,t,e){for(let i=s.state.facet(t),n=i.length-1;n>=0;n--){let r=i[n],o=typeof r=="function"?r(s):r;o&&yr(o,e)}return e}const ip=T.mac?"mac":T.windows?"win":T.linux?"linux":"key";function np(s,t){const e=s.split(/-(?!$)/);let i=e[e.length-1];i=="Space"&&(i=" ");let n,r,o,l;for(let a=0;a<e.length-1;++a){const h=e[a];if(/^(cmd|meta|m)$/i.test(h))l=!0;else if(/^a(lt)?$/i.test(h))n=!0;else if(/^(c|ctrl|control)$/i.test(h))r=!0;else if(/^s(hift)?$/i.test(h))o=!0;else if(/^mod$/i.test(h))t=="mac"?l=!0:r=!0;else throw new Error("Unrecognized modifier name: "+h)}return n&&(i="Alt-"+i),r&&(i="Ctrl-"+i),l&&(i="Meta-"+i),o&&(i="Shift-"+i),i}function Sn(s,t,e){return t.altKey&&(s="Alt-"+s),t.ctrlKey&&(s="Ctrl-"+s),t.metaKey&&(s="Meta-"+s),e!==!1&&t.shiftKey&&(s="Shift-"+s),s}const sp=Xe.default(E.domEventHandlers({keydown(s,t){return ap(rp(t.state),s,t,"editor")}})),on=B.define({enables:sp}),Pl=new WeakMap;function rp(s){let t=s.facet(on),e=Pl.get(t);return e||Pl.set(t,e=lp(t.reduce((i,n)=>i.concat(n),[]))),e}let ve=null;const op=4e3;function lp(s,t=ip){let e=Object.create(null),i=Object.create(null),n=(o,l)=>{let a=i[o];if(a==null)i[o]=l;else if(a!=l)throw new Error("Key binding "+o+" is used both as a regular binding and as a multi-stroke prefix")},r=(o,l,a,h,f)=>{var c,u;let d=e[o]||(e[o]=Object.create(null)),p=l.split(/ (?!$)/).map(y=>np(y,t));for(let y=1;y<p.length;y++){let w=p.slice(0,y).join(" ");n(w,!0),d[w]||(d[w]={preventDefault:!0,stopPropagation:!1,run:[S=>{let v=ve={view:S,prefix:w,scope:o};return setTimeout(()=>{ve==v&&(ve=null)},op),!0}]})}let m=p.join(" ");n(m,!1);let g=d[m]||(d[m]={preventDefault:!1,stopPropagation:!1,run:((u=(c=d._any)===null||c===void 0?void 0:c.run)===null||u===void 0?void 0:u.slice())||[]});a&&g.run.push(a),h&&(g.preventDefault=!0),f&&(g.stopPropagation=!0)};for(let o of s){let l=o.scope?o.scope.split(" "):["editor"];if(o.any)for(let h of l){let f=e[h]||(e[h]=Object.create(null));f._any||(f._any={preventDefault:!1,stopPropagation:!1,run:[]});let{any:c}=o;for(let u in f)f[u].run.push(d=>c(d,Er))}let a=o[t]||o.key;if(a)for(let h of l)r(h,a,o.run,o.preventDefault,o.stopPropagation),o.shift&&r(h,"Shift-"+a,o.shift,o.preventDefault,o.stopPropagation)}return e}let Er=null;function ap(s,t,e,i){Er=t;let n=Eu(t),r=Bt(n,0),o=pe(r)==n.length&&n!=" ",l="",a=!1,h=!1,f=!1;ve&&ve.view==e&&ve.scope==i&&(l=ve.prefix+" ",Qh.indexOf(t.keyCode)<0&&(h=!0,ve=null));let c=new Set,u=g=>{if(g){for(let y of g.run)if(!c.has(y)&&(c.add(y),y(e)))return g.stopPropagation&&(f=!0),!0;g.preventDefault&&(g.stopPropagation&&(f=!0),h=!0)}return!1},d=s[i],p,m;return d&&(u(d[l+Sn(n,t,!o)])?a=!0:o&&(t.altKey||t.metaKey||t.ctrlKey)&&!(T.windows&&t.ctrlKey&&t.altKey)&&(p=Oe[t.keyCode])&&p!=n?(u(d[l+Sn(p,t,!0)])||t.shiftKey&&(m=Hi[t.keyCode])!=n&&m!=p&&u(d[l+Sn(m,t,!1)]))&&(a=!0):o&&t.shiftKey&&u(d[l+Sn(n,t,!0)])&&(a=!0),!a&&u(d._any)&&(a=!0)),h&&(a=!0),a&&f&&t.stopPropagation(),Er=null,a}class ln{constructor(t,e,i,n,r){this.className=t,this.left=e,this.top=i,this.width=n,this.height=r}draw(){let t=document.createElement("div");return t.className=this.className,this.adjust(t),t}update(t,e){return e.className!=this.className?!1:(this.adjust(t),!0)}adjust(t){t.style.left=this.left+"px",t.style.top=this.top+"px",this.width!=null&&(t.style.width=this.width+"px"),t.style.height=this.height+"px"}eq(t){return this.left==t.left&&this.top==t.top&&this.width==t.width&&this.height==t.height&&this.className==t.className}static forRange(t,e,i){if(i.empty){let n=t.coordsAtPos(i.head,i.assoc||1);if(!n)return[];let r=ff(t);return[new ln(e,n.left-r.left,n.top-r.top,null,n.bottom-n.top)]}else return hp(t,e,i)}}function ff(s){let t=s.scrollDOM.getBoundingClientRect();return{left:(s.textDirection==J.LTR?t.left:t.right-s.scrollDOM.clientWidth*s.scaleX)-s.scrollDOM.scrollLeft*s.scaleX,top:t.top-s.scrollDOM.scrollTop*s.scaleY}}function Ll(s,t,e,i){let n=s.coordsAtPos(t,e*2);if(!n)return i;let r=s.dom.getBoundingClientRect(),o=(n.top+n.bottom)/2,l=s.posAtCoords({x:r.left+1,y:o}),a=s.posAtCoords({x:r.right-1,y:o});return l==null||a==null?i:{from:Math.max(i.from,Math.min(l,a)),to:Math.min(i.to,Math.max(l,a))}}function hp(s,t,e){if(e.to<=s.viewport.from||e.from>=s.viewport.to)return[];let i=Math.max(e.from,s.viewport.from),n=Math.min(e.to,s.viewport.to),r=s.textDirection==J.LTR,o=s.contentDOM,l=o.getBoundingClientRect(),a=ff(s),h=o.querySelector(".cm-line"),f=h&&window.getComputedStyle(h),c=l.left+(f?parseInt(f.paddingLeft)+Math.min(0,parseInt(f.textIndent)):0),u=l.right-(f?parseInt(f.paddingRight):0),d=Mr(s,i),p=Mr(s,n),m=d.type==xt.Text?d:null,g=p.type==xt.Text?p:null;if(m&&(s.lineWrapping||d.widgetLineBreaks)&&(m=Ll(s,i,1,m)),g&&(s.lineWrapping||p.widgetLineBreaks)&&(g=Ll(s,n,-1,g)),m&&g&&m.from==g.from&&m.to==g.to)return w(S(e.from,e.to,m));{let x=m?S(e.from,null,m):v(d,!1),C=g?S(null,e.to,g):v(p,!0),A=[];return(m||d).to<(g||p).from-(m&&g?1:0)||d.widgetLineBreaks>1&&x.bottom+s.defaultLineHeight/2<C.top?A.push(y(c,x.bottom,u,C.top)):x.bottom<C.top&&s.elementAtHeight((x.bottom+C.top)/2).type==xt.Text&&(x.bottom=C.top=(x.bottom+C.top)/2),w(x).concat(A).concat(w(C))}function y(x,C,A,L){return new ln(t,x-a.left,C-a.top,A-x,L-C)}function w({top:x,bottom:C,horizontal:A}){let L=[];for(let R=0;R<A.length;R+=2)L.push(y(A[R],x,A[R+1],C));return L}function S(x,C,A){let L=1e9,R=-1e9,z=[];function M(F,j,ot,yt,Nt){let et=s.coordsAtPos(F,F==A.to?-2:2),St=s.coordsAtPos(ot,ot==A.from?2:-2);!et||!St||(L=Math.min(et.top,St.top,L),R=Math.max(et.bottom,St.bottom,R),Nt==J.LTR?z.push(r&&j?c:et.left,r&&yt?u:St.right):z.push(!r&&yt?c:St.left,!r&&j?u:et.right))}let P=x??A.from,q=C??A.to;for(let F of s.visibleRanges)if(F.to>P&&F.from<q)for(let j=Math.max(F.from,P),ot=Math.min(F.to,q);;){let yt=s.state.doc.lineAt(j);for(let Nt of s.bidiSpans(yt)){let et=Nt.from+yt.from,St=Nt.to+yt.from;if(et>=ot)break;St>j&&M(Math.max(et,j),x==null&&et<=P,Math.min(St,ot),C==null&&St>=q,Nt.dir)}if(j=yt.to+1,j>=ot)break}return z.length==0&&M(P,x==null,q,C==null,s.textDirection),{top:L,bottom:R,horizontal:z}}function v(x,C){let A=l.top+(C?x.top:x.bottom);return{top:A,bottom:A,horizontal:[]}}}function fp(s,t){return s.constructor==t.constructor&&s.eq(t)}class cp{constructor(t,e){this.view=t,this.layer=e,this.drawn=[],this.scaleX=1,this.scaleY=1,this.measureReq={read:this.measure.bind(this),write:this.draw.bind(this)},this.dom=t.scrollDOM.appendChild(document.createElement("div")),this.dom.classList.add("cm-layer"),e.above&&this.dom.classList.add("cm-layer-above"),e.class&&this.dom.classList.add(e.class),this.scale(),this.dom.setAttribute("aria-hidden","true"),this.setOrder(t.state),t.requestMeasure(this.measureReq),e.mount&&e.mount(this.dom,t)}update(t){t.startState.facet(zn)!=t.state.facet(zn)&&this.setOrder(t.state),(this.layer.update(t,this.dom)||t.geometryChanged)&&(this.scale(),t.view.requestMeasure(this.measureReq))}docViewUpdate(t){this.layer.updateOnDocViewUpdate!==!1&&t.requestMeasure(this.measureReq)}setOrder(t){let e=0,i=t.facet(zn);for(;e<i.length&&i[e]!=this.layer;)e++;this.dom.style.zIndex=String((this.layer.above?150:-1)-e)}measure(){return this.layer.markers(this.view)}scale(){let{scaleX:t,scaleY:e}=this.view;(t!=this.scaleX||e!=this.scaleY)&&(this.scaleX=t,this.scaleY=e,this.dom.style.transform=`scale(${1/t}, ${1/e})`)}draw(t){if(t.length!=this.drawn.length||t.some((e,i)=>!fp(e,this.drawn[i]))){let e=this.dom.firstChild,i=0;for(let n of t)n.update&&e&&n.constructor&&this.drawn[i].constructor&&n.update(e,this.drawn[i])?(e=e.nextSibling,i++):this.dom.insertBefore(n.draw(),e);for(;e;){let n=e.nextSibling;e.remove(),e=n}this.drawn=t}}destroy(){this.layer.destroy&&this.layer.destroy(this.dom,this.view),this.dom.remove()}}const zn=B.define();function cf(s){return[ut.define(t=>new cp(t,s)),zn.of(s)]}const uf=!(T.ios&&T.webkit&&T.webkit_version<534),qi=B.define({combine(s){return $e(s,{cursorBlinkRate:1200,drawRangeCursor:!0},{cursorBlinkRate:(t,e)=>Math.min(t,e),drawRangeCursor:(t,e)=>t||e})}});function up(s={}){return[qi.of(s),dp,pp,mp,Wh.of(!0)]}function df(s){return s.startState.facet(qi)!=s.state.facet(qi)}const dp=cf({above:!0,markers(s){let{state:t}=s,e=t.facet(qi),i=[];for(let n of t.selection.ranges){let r=n==t.selection.main;if(n.empty?!r||uf:e.drawRangeCursor){let o=r?"cm-cursor cm-cursor-primary":"cm-cursor cm-cursor-secondary",l=n.empty?n:k.cursor(n.head,n.head>n.anchor?-1:1);for(let a of ln.forRange(s,o,l))i.push(a)}}return i},update(s,t){s.transactions.some(i=>i.selection)&&(t.style.animationName=t.style.animationName=="cm-blink"?"cm-blink2":"cm-blink");let e=df(s);return e&&Rl(s.state,t),s.docChanged||s.selectionSet||e},mount(s,t){Rl(t.state,s)},class:"cm-cursorLayer"});function Rl(s,t){t.style.animationDuration=s.facet(qi).cursorBlinkRate+"ms"}const pp=cf({above:!1,markers(s){return s.state.selection.ranges.map(t=>t.empty?[]:ln.forRange(s,"cm-selectionBackground",t)).reduce((t,e)=>t.concat(e))},update(s,t){return s.docChanged||s.selectionSet||s.viewportChanged||df(s)},class:"cm-selectionLayer"}),Pr={".cm-line":{"& ::selection, &::selection":{backgroundColor:"transparent !important"}},".cm-content":{"& :focus":{caretColor:"initial !important","&::selection, & ::selection":{backgroundColor:"Highlight !important"}}}};uf&&(Pr[".cm-line"].caretColor=Pr[".cm-content"].caretColor="transparent !important");const mp=Xe.highest(E.theme(Pr));function Il(s,t,e,i,n){t.lastIndex=0;for(let r=s.iterRange(e,i),o=e,l;!r.next().done;o+=r.value.length)if(!r.lineBreak)for(;l=t.exec(r.value);)n(o+l.index,l)}function gp(s,t){let e=s.visibleRanges;if(e.length==1&&e[0].from==s.viewport.from&&e[0].to==s.viewport.to)return e;let i=[];for(let{from:n,to:r}of e)n=Math.max(s.state.doc.lineAt(n).from,n-t),r=Math.min(s.state.doc.lineAt(r).to,r+t),i.length&&i[i.length-1].to>=n?i[i.length-1].to=r:i.push({from:n,to:r});return i}class bp{constructor(t){const{regexp:e,decoration:i,decorate:n,boundary:r,maxLength:o=1e3}=t;if(!e.global)throw new RangeError("The regular expression given to MatchDecorator should have its 'g' flag set");if(this.regexp=e,n)this.addMatch=(l,a,h,f)=>n(f,h,h+l[0].length,l,a);else if(typeof i=="function")this.addMatch=(l,a,h,f)=>{let c=i(l,a,h);c&&f(h,h+l[0].length,c)};else if(i)this.addMatch=(l,a,h,f)=>f(h,h+l[0].length,i);else throw new RangeError("Either 'decorate' or 'decoration' should be provided to MatchDecorator");this.boundary=r,this.maxLength=o}createDeco(t){let e=new De,i=e.add.bind(e);for(let{from:n,to:r}of gp(t,this.maxLength))Il(t.state.doc,this.regexp,n,r,(o,l)=>this.addMatch(l,t,o,i));return e.finish()}updateDeco(t,e){let i=1e9,n=-1;return t.docChanged&&t.changes.iterChanges((r,o,l,a)=>{a>=t.view.viewport.from&&l<=t.view.viewport.to&&(i=Math.min(l,i),n=Math.max(a,n))}),t.viewportMoved||n-i>1e3?this.createDeco(t.view):n>-1?this.updateRange(t.view,e.map(t.changes),i,n):e}updateRange(t,e,i,n){for(let r of t.visibleRanges){let o=Math.max(r.from,i),l=Math.min(r.to,n);if(l>o){let a=t.state.doc.lineAt(o),h=a.to<l?t.state.doc.lineAt(l):a,f=Math.max(r.from,a.from),c=Math.min(r.to,h.to);if(this.boundary){for(;o>a.from;o--)if(this.boundary.test(a.text[o-1-a.from])){f=o;break}for(;l<h.to;l++)if(this.boundary.test(h.text[l-h.from])){c=l;break}}let u=[],d,p=(m,g,y)=>u.push(y.range(m,g));if(a==h)for(this.regexp.lastIndex=f-a.from;(d=this.regexp.exec(a.text))&&d.index<c-a.from;)this.addMatch(d,t,d.index+a.from,p);else Il(t.state.doc,this.regexp,f,c,(m,g)=>this.addMatch(g,t,m,p));e=e.update({filterFrom:f,filterTo:c,filter:(m,g)=>m<f||g>c,add:u})}}return e}}const Lr=/x/.unicode!=null?"gu":"g",yp=new RegExp(`[\0-\b
--­؜​‎‏\u2028\u2029‭‮⁦⁧⁩\uFEFF￹-￼]`,Lr),wp={0:"null",7:"bell",8:"backspace",10:"newline",11:"vertical tab",13:"carriage return",27:"escape",8203:"zero width space",8204:"zero width non-joiner",8205:"zero width joiner",8206:"left-to-right mark",8207:"right-to-left mark",8232:"line separator",8237:"left-to-right override",8238:"right-to-left override",8294:"left-to-right isolate",8295:"right-to-left isolate",8297:"pop directional isolate",8233:"paragraph separator",65279:"zero width no-break space",65532:"object replacement"};let Ps=null;function xp(){var s;if(Ps==null&&typeof document<"u"&&document.body){let t=document.body.style;Ps=((s=t.tabSize)!==null&&s!==void 0?s:t.MozTabSize)!=null}return Ps||!1}const qn=B.define({combine(s){let t=$e(s,{render:null,specialChars:yp,addSpecialChars:null});return(t.replaceTabs=!xp())&&(t.specialChars=new RegExp("	|"+t.specialChars.source,Lr)),t.addSpecialChars&&(t.specialChars=new RegExp(t.specialChars.source+"|"+t.addSpecialChars.source,Lr)),t}});function kp(s={}){return[qn.of(s),vp()]}let Nl=null;function vp(){return Nl||(Nl=ut.fromClass(class{constructor(s){this.view=s,this.decorations=N.none,this.decorationCache=Object.create(null),this.decorator=this.makeDecorator(s.state.facet(qn)),this.decorations=this.decorator.createDeco(s)}makeDecorator(s){return new bp({regexp:s.specialChars,decoration:(t,e,i)=>{let{doc:n}=e.state,r=Bt(t[0],0);if(r==9){let o=n.lineAt(i),l=e.state.tabSize,a=gi(o.text,l,i-o.from);return N.replace({widget:new Mp((l-a%l)*this.view.defaultCharacterWidth/this.view.scaleX)})}return this.decorationCache[r]||(this.decorationCache[r]=N.replace({widget:new Ap(s,r)}))},boundary:s.replaceTabs?void 0:/[^]/})}update(s){let t=s.state.facet(qn);s.startState.facet(qn)!=t?(this.decorator=this.makeDecorator(t),this.decorations=this.decorator.createDeco(s.view)):this.decorations=this.decorator.updateDeco(s,this.decorations)}},{decorations:s=>s.decorations}))}const Sp="•";function Cp(s){return s>=32?Sp:s==10?"␤":String.fromCharCode(9216+s)}class Ap extends ue{constructor(t,e){super(),this.options=t,this.code=e}eq(t){return t.code==this.code}toDOM(t){let e=Cp(this.code),i=t.state.phrase("Control character")+" "+(wp[this.code]||"0x"+this.code.toString(16)),n=this.options.render&&this.options.render(this.code,i,e);if(n)return n;let r=document.createElement("span");return r.textContent=e,r.title=i,r.setAttribute("aria-label",i),r.className="cm-specialChar",r}ignoreEvent(){return!1}}class Mp extends ue{constructor(t){super(),this.width=t}eq(t){return t.width==this.width}toDOM(){let t=document.createElement("span");return t.textContent="	",t.className="cm-tab",t.style.width=this.width+"px",t}ignoreEvent(){return!1}}class Dp extends ue{constructor(t){super(),this.content=t}toDOM(t){let e=document.createElement("span");return e.className="cm-placeholder",e.style.pointerEvents="none",e.appendChild(typeof this.content=="string"?document.createTextNode(this.content):typeof this.content=="function"?this.content(t):this.content.cloneNode(!0)),typeof this.content=="string"?e.setAttribute("aria-label","placeholder "+this.content):e.setAttribute("aria-hidden","true"),e}coordsAt(t){let e=t.firstChild?hi(t.firstChild):[];if(!e.length)return null;let i=window.getComputedStyle(t.parentNode),n=nn(e[0],i.direction!="rtl"),r=parseInt(i.lineHeight);return n.bottom-n.top>r*1.5?{left:n.left,right:n.right,top:n.top,bottom:n.top+r}:n}ignoreEvent(){return!1}}function Tp(s){return ut.fromClass(class{constructor(t){this.view=t,this.placeholder=s?N.set([N.widget({widget:new Dp(s),side:1}).range(0)]):N.none}get decorations(){return this.view.state.doc.length?N.none:this.placeholder}},{decorations:t=>t.decorations})}const Rr=2e3;function Op(s,t,e){let i=Math.min(t.line,e.line),n=Math.max(t.line,e.line),r=[];if(t.off>Rr||e.off>Rr||t.col<0||e.col<0){let o=Math.min(t.off,e.off),l=Math.max(t.off,e.off);for(let a=i;a<=n;a++){let h=s.doc.line(a);h.length<=l&&r.push(k.range(h.from+o,h.to+l))}}else{let o=Math.min(t.col,e.col),l=Math.max(t.col,e.col);for(let a=i;a<=n;a++){let h=s.doc.line(a),f=cr(h.text,o,s.tabSize,!0);if(f<0)r.push(k.cursor(h.to));else{let c=cr(h.text,l,s.tabSize);r.push(k.range(h.from+f,h.from+c))}}}return r}function Bp(s,t){let e=s.coordsAtPos(s.viewport.from);return e?Math.round(Math.abs((e.left-t)/s.defaultCharacterWidth)):-1}function _l(s,t){let e=s.posAtCoords({x:t.clientX,y:t.clientY},!1),i=s.state.doc.lineAt(e),n=e-i.from,r=n>Rr?-1:n==i.length?Bp(s,t.clientX):gi(i.text,s.state.tabSize,e-i.from);return{line:i.number,col:r,off:n}}function Ep(s,t){let e=_l(s,t),i=s.state.selection;return e?{update(n){if(n.docChanged){let r=n.changes.mapPos(n.startState.doc.line(e.line).from),o=n.state.doc.lineAt(r);e={line:o.number,col:e.col,off:Math.min(e.off,o.length)},i=i.map(n.changes)}},get(n,r,o){let l=_l(s,n);if(!l)return i;let a=Op(s.state,e,l);return a.length?o?k.create(a.concat(i.ranges)):k.create(a):i}}:null}function Pp(s){let t=e=>e.altKey&&e.button==0;return E.mouseSelectionStyle.of((e,i)=>t(i)?Ep(e,i):null)}const Lp={Alt:[18,s=>!!s.altKey],Control:[17,s=>!!s.ctrlKey],Shift:[16,s=>!!s.shiftKey],Meta:[91,s=>!!s.metaKey]},Rp={style:"cursor: crosshair"};function Ip(s={}){let[t,e]=Lp[s.key||"Alt"],i=ut.fromClass(class{constructor(n){this.view=n,this.isDown=!1}set(n){this.isDown!=n&&(this.isDown=n,this.view.update([]))}},{eventObservers:{keydown(n){this.set(n.keyCode==t||e(n))},keyup(n){(n.keyCode==t||!e(n))&&this.set(!1)},mousemove(n){this.set(e(n))}}});return[i,E.contentAttributes.of(n=>{var r;return!((r=n.plugin(i))===null||r===void 0)&&r.isDown?Rp:null})]}const vi="-10000px";class pf{constructor(t,e,i,n){this.facet=e,this.createTooltipView=i,this.removeTooltipView=n,this.input=t.state.facet(e),this.tooltips=this.input.filter(o=>o);let r=null;this.tooltipViews=this.tooltips.map(o=>r=i(o,r))}update(t,e){var i;let n=t.state.facet(this.facet),r=n.filter(a=>a);if(n===this.input){for(let a of this.tooltipViews)a.update&&a.update(t);return!1}let o=[],l=e?[]:null;for(let a=0;a<r.length;a++){let h=r[a],f=-1;if(h){for(let c=0;c<this.tooltips.length;c++){let u=this.tooltips[c];u&&u.create==h.create&&(f=c)}if(f<0)o[a]=this.createTooltipView(h,a?o[a-1]:null),l&&(l[a]=!!h.above);else{let c=o[a]=this.tooltipViews[f];l&&(l[a]=e[f]),c.update&&c.update(t)}}}for(let a of this.tooltipViews)o.indexOf(a)<0&&(this.removeTooltipView(a),(i=a.destroy)===null||i===void 0||i.call(a));return e&&(l.forEach((a,h)=>e[h]=a),e.length=l.length),this.input=n,this.tooltips=r,this.tooltipViews=o,!0}}function Np(s){let t=s.dom.ownerDocument.documentElement;return{top:0,left:0,bottom:t.clientHeight,right:t.clientWidth}}const Ls=B.define({combine:s=>{var t,e,i;return{position:T.ios?"absolute":((t=s.find(n=>n.position))===null||t===void 0?void 0:t.position)||"fixed",parent:((e=s.find(n=>n.parent))===null||e===void 0?void 0:e.parent)||null,tooltipSpace:((i=s.find(n=>n.tooltipSpace))===null||i===void 0?void 0:i.tooltipSpace)||Np}}}),Fl=new WeakMap,ao=ut.fromClass(class{constructor(s){this.view=s,this.above=[],this.inView=!0,this.madeAbsolute=!1,this.lastTransaction=0,this.measureTimeout=-1;let t=s.state.facet(Ls);this.position=t.position,this.parent=t.parent,this.classes=s.themeClasses,this.createContainer(),this.measureReq={read:this.readMeasure.bind(this),write:this.writeMeasure.bind(this),key:this},this.resizeObserver=typeof ResizeObserver=="function"?new ResizeObserver(()=>this.measureSoon()):null,this.manager=new pf(s,ho,(e,i)=>this.createTooltip(e,i),e=>{this.resizeObserver&&this.resizeObserver.unobserve(e.dom),e.dom.remove()}),this.above=this.manager.tooltips.map(e=>!!e.above),this.intersectionObserver=typeof IntersectionObserver=="function"?new IntersectionObserver(e=>{Date.now()>this.lastTransaction-50&&e.length>0&&e[e.length-1].intersectionRatio<1&&this.measureSoon()},{threshold:[1]}):null,this.observeIntersection(),s.win.addEventListener("resize",this.measureSoon=this.measureSoon.bind(this)),this.maybeMeasure()}createContainer(){this.parent?(this.container=document.createElement("div"),this.container.style.position="relative",this.container.className=this.view.themeClasses,this.parent.appendChild(this.container)):this.container=this.view.dom}observeIntersection(){if(this.intersectionObserver){this.intersectionObserver.disconnect();for(let s of this.manager.tooltipViews)this.intersectionObserver.observe(s.dom)}}measureSoon(){this.measureTimeout<0&&(this.measureTimeout=setTimeout(()=>{this.measureTimeout=-1,this.maybeMeasure()},50))}update(s){s.transactions.length&&(this.lastTransaction=Date.now());let t=this.manager.update(s,this.above);t&&this.observeIntersection();let e=t||s.geometryChanged,i=s.state.facet(Ls);if(i.position!=this.position&&!this.madeAbsolute){this.position=i.position;for(let n of this.manager.tooltipViews)n.dom.style.position=this.position;e=!0}if(i.parent!=this.parent){this.parent&&this.container.remove(),this.parent=i.parent,this.createContainer();for(let n of this.manager.tooltipViews)this.container.appendChild(n.dom);e=!0}else this.parent&&this.view.themeClasses!=this.classes&&(this.classes=this.container.className=this.view.themeClasses);e&&this.maybeMeasure()}createTooltip(s,t){let e=s.create(this.view),i=t?t.dom:null;if(e.dom.classList.add("cm-tooltip"),s.arrow&&!e.dom.querySelector(".cm-tooltip > .cm-tooltip-arrow")){let n=document.createElement("div");n.className="cm-tooltip-arrow",e.dom.appendChild(n)}return e.dom.style.position=this.position,e.dom.style.top=vi,e.dom.style.left="0px",this.container.insertBefore(e.dom,i),e.mount&&e.mount(this.view),this.resizeObserver&&this.resizeObserver.observe(e.dom),e}destroy(){var s,t,e;this.view.win.removeEventListener("resize",this.measureSoon);for(let i of this.manager.tooltipViews)i.dom.remove(),(s=i.destroy)===null||s===void 0||s.call(i);this.parent&&this.container.remove(),(t=this.resizeObserver)===null||t===void 0||t.disconnect(),(e=this.intersectionObserver)===null||e===void 0||e.disconnect(),clearTimeout(this.measureTimeout)}readMeasure(){let s=1,t=1,e=!1;if(this.position=="fixed"&&this.manager.tooltipViews.length){let{dom:r}=this.manager.tooltipViews[0];if(T.gecko)e=r.offsetParent!=this.container.ownerDocument.body;else if(r.style.top==vi&&r.style.left=="0px"){let o=r.getBoundingClientRect();e=Math.abs(o.top+1e4)>1||Math.abs(o.left)>1}}if(e||this.position=="absolute")if(this.parent){let r=this.parent.getBoundingClientRect();r.width&&r.height&&(s=r.width/this.parent.offsetWidth,t=r.height/this.parent.offsetHeight)}else({scaleX:s,scaleY:t}=this.view.viewState);let i=this.view.scrollDOM.getBoundingClientRect(),n=so(this.view);return{visible:{left:i.left+n.left,top:i.top+n.top,right:i.right-n.right,bottom:i.bottom-n.bottom},parent:this.parent?this.container.getBoundingClientRect():this.view.dom.getBoundingClientRect(),pos:this.manager.tooltips.map((r,o)=>{let l=this.manager.tooltipViews[o];return l.getCoords?l.getCoords(r.pos):this.view.coordsAtPos(r.pos)}),size:this.manager.tooltipViews.map(({dom:r})=>r.getBoundingClientRect()),space:this.view.state.facet(Ls).tooltipSpace(this.view),scaleX:s,scaleY:t,makeAbsolute:e}}writeMeasure(s){var t;if(s.makeAbsolute){this.madeAbsolute=!0,this.position="absolute";for(let l of this.manager.tooltipViews)l.dom.style.position="absolute"}let{visible:e,space:i,scaleX:n,scaleY:r}=s,o=[];for(let l=0;l<this.manager.tooltips.length;l++){let a=this.manager.tooltips[l],h=this.manager.tooltipViews[l],{dom:f}=h,c=s.pos[l],u=s.size[l];if(!c||a.clip!==!1&&(c.bottom<=Math.max(e.top,i.top)||c.top>=Math.min(e.bottom,i.bottom)||c.right<Math.max(e.left,i.left)-.1||c.left>Math.min(e.right,i.right)+.1)){f.style.top=vi;continue}let d=a.arrow?h.dom.querySelector(".cm-tooltip-arrow"):null,p=d?7:0,m=u.right-u.left,g=(t=Fl.get(h))!==null&&t!==void 0?t:u.bottom-u.top,y=h.offset||Fp,w=this.view.textDirection==J.LTR,S=u.width>i.right-i.left?w?i.left:i.right-u.width:w?Math.max(i.left,Math.min(c.left-(d?14:0)+y.x,i.right-m)):Math.min(Math.max(i.left,c.left-m+(d?14:0)-y.x),i.right-m),v=this.above[l];!a.strictSide&&(v?c.top-g-p-y.y<i.top:c.bottom+g+p+y.y>i.bottom)&&v==i.bottom-c.bottom>c.top-i.top&&(v=this.above[l]=!v);let x=(v?c.top-i.top:i.bottom-c.bottom)-p;if(x<g&&h.resize!==!1){if(x<this.view.defaultLineHeight){f.style.top=vi;continue}Fl.set(h,g),f.style.height=(g=x)/r+"px"}else f.style.height&&(f.style.height="");let C=v?c.top-g-p-y.y:c.bottom+p+y.y,A=S+m;if(h.overlap!==!0)for(let L of o)L.left<A&&L.right>S&&L.top<C+g&&L.bottom>C&&(C=v?L.top-g-2-p:L.bottom+p+2);if(this.position=="absolute"?(f.style.top=(C-s.parent.top)/r+"px",Vl(f,(S-s.parent.left)/n)):(f.style.top=C/r+"px",Vl(f,S/n)),d){let L=c.left+(w?y.x:-y.x)-(S+14-7);d.style.left=L/n+"px"}h.overlap!==!0&&o.push({left:S,top:C,right:A,bottom:C+g}),f.classList.toggle("cm-tooltip-above",v),f.classList.toggle("cm-tooltip-below",!v),h.positioned&&h.positioned(s.space)}}maybeMeasure(){if(this.manager.tooltips.length&&(this.view.inView&&this.view.requestMeasure(this.measureReq),this.inView!=this.view.inView&&(this.inView=this.view.inView,!this.inView)))for(let s of this.manager.tooltipViews)s.dom.style.top=vi}},{eventObservers:{scroll(){this.maybeMeasure()}}});function Vl(s,t){let e=parseInt(s.style.left,10);(isNaN(e)||Math.abs(t-e)>1)&&(s.style.left=t+"px")}const _p=E.baseTheme({".cm-tooltip":{zIndex:500,boxSizing:"border-box"},"&light .cm-tooltip":{border:"1px solid #bbb",backgroundColor:"#f5f5f5"},"&light .cm-tooltip-section:not(:first-child)":{borderTop:"1px solid #bbb"},"&dark .cm-tooltip":{backgroundColor:"#333338",color:"white"},".cm-tooltip-arrow":{height:"7px",width:`${7*2}px`,position:"absolute",zIndex:-1,overflow:"hidden","&:before, &:after":{content:"''",position:"absolute",width:0,height:0,borderLeft:"7px solid transparent",borderRight:"7px solid transparent"},".cm-tooltip-above &":{bottom:"-7px","&:before":{borderTop:"7px solid #bbb"},"&:after":{borderTop:"7px solid #f5f5f5",bottom:"1px"}},".cm-tooltip-below &":{top:"-7px","&:before":{borderBottom:"7px solid #bbb"},"&:after":{borderBottom:"7px solid #f5f5f5",top:"1px"}}},"&dark .cm-tooltip .cm-tooltip-arrow":{"&:before":{borderTopColor:"#333338",borderBottomColor:"#333338"},"&:after":{borderTopColor:"transparent",borderBottomColor:"transparent"}}}),Fp={x:0,y:0},ho=B.define({enables:[ao,_p]}),es=B.define({combine:s=>s.reduce((t,e)=>t.concat(e),[])});class ds{static create(t){return new ds(t)}constructor(t){this.view=t,this.mounted=!1,this.dom=document.createElement("div"),this.dom.classList.add("cm-tooltip-hover"),this.manager=new pf(t,es,(e,i)=>this.createHostedView(e,i),e=>e.dom.remove())}createHostedView(t,e){let i=t.create(this.view);return i.dom.classList.add("cm-tooltip-section"),this.dom.insertBefore(i.dom,e?e.dom.nextSibling:this.dom.firstChild),this.mounted&&i.mount&&i.mount(this.view),i}mount(t){for(let e of this.manager.tooltipViews)e.mount&&e.mount(t);this.mounted=!0}positioned(t){for(let e of this.manager.tooltipViews)e.positioned&&e.positioned(t)}update(t){this.manager.update(t)}destroy(){var t;for(let e of this.manager.tooltipViews)(t=e.destroy)===null||t===void 0||t.call(e)}passProp(t){let e;for(let i of this.manager.tooltipViews){let n=i[t];if(n!==void 0){if(e===void 0)e=n;else if(e!==n)return}}return e}get offset(){return this.passProp("offset")}get getCoords(){return this.passProp("getCoords")}get overlap(){return this.passProp("overlap")}get resize(){return this.passProp("resize")}}const Vp=ho.compute([es],s=>{let t=s.facet(es);return t.length===0?null:{pos:Math.min(...t.map(e=>e.pos)),end:Math.max(...t.map(e=>{var i;return(i=e.end)!==null&&i!==void 0?i:e.pos})),create:ds.create,above:t[0].above,arrow:t.some(e=>e.arrow)}});class Hp{constructor(t,e,i,n,r){this.view=t,this.source=e,this.field=i,this.setHover=n,this.hoverTime=r,this.hoverTimeout=-1,this.restartTimeout=-1,this.pending=null,this.lastMove={x:0,y:0,target:t.dom,time:0},this.checkHover=this.checkHover.bind(this),t.dom.addEventListener("mouseleave",this.mouseleave=this.mouseleave.bind(this)),t.dom.addEventListener("mousemove",this.mousemove=this.mousemove.bind(this))}update(){this.pending&&(this.pending=null,clearTimeout(this.restartTimeout),this.restartTimeout=setTimeout(()=>this.startHover(),20))}get active(){return this.view.state.field(this.field)}checkHover(){if(this.hoverTimeout=-1,this.active.length)return;let t=Date.now()-this.lastMove.time;t<this.hoverTime?this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime-t):this.startHover()}startHover(){clearTimeout(this.restartTimeout);let{view:t,lastMove:e}=this,i=t.docView.nearest(e.target);if(!i)return;let n,r=1;if(i instanceof Ce)n=i.posAtStart;else{if(n=t.posAtCoords(e),n==null)return;let l=t.coordsAtPos(n);if(!l||e.y<l.top||e.y>l.bottom||e.x<l.left-t.defaultCharacterWidth||e.x>l.right+t.defaultCharacterWidth)return;let a=t.bidiSpans(t.state.doc.lineAt(n)).find(f=>f.from<=n&&f.to>=n),h=a&&a.dir==J.RTL?-1:1;r=e.x<l.left?-h:h}let o=this.source(t,n,r);if(o!=null&&o.then){let l=this.pending={pos:n};o.then(a=>{this.pending==l&&(this.pending=null,a&&!(Array.isArray(a)&&!a.length)&&t.dispatch({effects:this.setHover.of(Array.isArray(a)?a:[a])}))},a=>Mt(t.state,a,"hover tooltip"))}else o&&!(Array.isArray(o)&&!o.length)&&t.dispatch({effects:this.setHover.of(Array.isArray(o)?o:[o])})}get tooltip(){let t=this.view.plugin(ao),e=t?t.manager.tooltips.findIndex(i=>i.create==ds.create):-1;return e>-1?t.manager.tooltipViews[e]:null}mousemove(t){var e,i;this.lastMove={x:t.clientX,y:t.clientY,target:t.target,time:Date.now()},this.hoverTimeout<0&&(this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime));let{active:n,tooltip:r}=this;if(n.length&&r&&!Wp(r.dom,t)||this.pending){let{pos:o}=n[0]||this.pending,l=(i=(e=n[0])===null||e===void 0?void 0:e.end)!==null&&i!==void 0?i:o;(o==l?this.view.posAtCoords(this.lastMove)!=o:!zp(this.view,o,l,t.clientX,t.clientY))&&(this.view.dispatch({effects:this.setHover.of([])}),this.pending=null)}}mouseleave(t){clearTimeout(this.hoverTimeout),this.hoverTimeout=-1;let{active:e}=this;if(e.length){let{tooltip:i}=this;i&&i.dom.contains(t.relatedTarget)?this.watchTooltipLeave(i.dom):this.view.dispatch({effects:this.setHover.of([])})}}watchTooltipLeave(t){let e=i=>{t.removeEventListener("mouseleave",e),this.active.length&&!this.view.dom.contains(i.relatedTarget)&&this.view.dispatch({effects:this.setHover.of([])})};t.addEventListener("mouseleave",e)}destroy(){clearTimeout(this.hoverTimeout),this.view.dom.removeEventListener("mouseleave",this.mouseleave),this.view.dom.removeEventListener("mousemove",this.mousemove)}}const Cn=4;function Wp(s,t){let{left:e,right:i,top:n,bottom:r}=s.getBoundingClientRect(),o;if(o=s.querySelector(".cm-tooltip-arrow")){let l=o.getBoundingClientRect();n=Math.min(l.top,n),r=Math.max(l.bottom,r)}return t.clientX>=e-Cn&&t.clientX<=i+Cn&&t.clientY>=n-Cn&&t.clientY<=r+Cn}function zp(s,t,e,i,n,r){let o=s.scrollDOM.getBoundingClientRect(),l=s.documentTop+s.documentPadding.top+s.contentHeight;if(o.left>i||o.right<i||o.top>n||Math.min(o.bottom,l)<n)return!1;let a=s.posAtCoords({x:i,y:n},!1);return a>=t&&a<=e}function qp(s,t={}){let e=I.define(),i=Tt.define({create(){return[]},update(n,r){if(n.length&&(t.hideOnChange&&(r.docChanged||r.selection)?n=[]:t.hideOn&&(n=n.filter(o=>!t.hideOn(r,o))),r.docChanged)){let o=[];for(let l of n){let a=r.changes.mapPos(l.pos,-1,ct.TrackDel);if(a!=null){let h=Object.assign(Object.create(null),l);h.pos=a,h.end!=null&&(h.end=r.changes.mapPos(h.end)),o.push(h)}}n=o}for(let o of r.effects)o.is(e)&&(n=o.value),o.is(jp)&&(n=[]);return n},provide:n=>es.from(n)});return{active:i,extension:[i,ut.define(n=>new Hp(n,s,i,e,t.hoverTime||300)),Vp]}}function mf(s,t){let e=s.plugin(ao);if(!e)return null;let i=e.manager.tooltips.indexOf(t);return i<0?null:e.manager.tooltipViews[i]}const jp=I.define(),Hl=B.define({combine(s){let t,e;for(let i of s)t=t||i.topContainer,e=e||i.bottomContainer;return{topContainer:t,bottomContainer:e}}});function Kp(s,t){let e=s.plugin(gf),i=e?e.specs.indexOf(t):-1;return i>-1?e.panels[i]:null}const gf=ut.fromClass(class{constructor(s){this.input=s.state.facet(Ir),this.specs=this.input.filter(e=>e),this.panels=this.specs.map(e=>e(s));let t=s.state.facet(Hl);this.top=new An(s,!0,t.topContainer),this.bottom=new An(s,!1,t.bottomContainer),this.top.sync(this.panels.filter(e=>e.top)),this.bottom.sync(this.panels.filter(e=>!e.top));for(let e of this.panels)e.dom.classList.add("cm-panel"),e.mount&&e.mount()}update(s){let t=s.state.facet(Hl);this.top.container!=t.topContainer&&(this.top.sync([]),this.top=new An(s.view,!0,t.topContainer)),this.bottom.container!=t.bottomContainer&&(this.bottom.sync([]),this.bottom=new An(s.view,!1,t.bottomContainer)),this.top.syncClasses(),this.bottom.syncClasses();let e=s.state.facet(Ir);if(e!=this.input){let i=e.filter(a=>a),n=[],r=[],o=[],l=[];for(let a of i){let h=this.specs.indexOf(a),f;h<0?(f=a(s.view),l.push(f)):(f=this.panels[h],f.update&&f.update(s)),n.push(f),(f.top?r:o).push(f)}this.specs=i,this.panels=n,this.top.sync(r),this.bottom.sync(o);for(let a of l)a.dom.classList.add("cm-panel"),a.mount&&a.mount()}else for(let i of this.panels)i.update&&i.update(s)}destroy(){this.top.sync([]),this.bottom.sync([])}},{provide:s=>E.scrollMargins.of(t=>{let e=t.plugin(s);return e&&{top:e.top.scrollMargin(),bottom:e.bottom.scrollMargin()}})});class An{constructor(t,e,i){this.view=t,this.top=e,this.container=i,this.dom=void 0,this.classes="",this.panels=[],this.syncClasses()}sync(t){for(let e of this.panels)e.destroy&&t.indexOf(e)<0&&e.destroy();this.panels=t,this.syncDOM()}syncDOM(){if(this.panels.length==0){this.dom&&(this.dom.remove(),this.dom=void 0);return}if(!this.dom){this.dom=document.createElement("div"),this.dom.className=this.top?"cm-panels cm-panels-top":"cm-panels cm-panels-bottom",this.dom.style[this.top?"top":"bottom"]="0";let e=this.container||this.view.dom;e.insertBefore(this.dom,this.top?e.firstChild:null)}let t=this.dom.firstChild;for(let e of this.panels)if(e.dom.parentNode==this.dom){for(;t!=e.dom;)t=Wl(t);t=t.nextSibling}else this.dom.insertBefore(e.dom,t);for(;t;)t=Wl(t)}scrollMargin(){return!this.dom||this.container?0:Math.max(0,this.top?this.dom.getBoundingClientRect().bottom-Math.max(0,this.view.scrollDOM.getBoundingClientRect().top):Math.min(innerHeight,this.view.scrollDOM.getBoundingClientRect().bottom)-this.dom.getBoundingClientRect().top)}syncClasses(){if(!(!this.container||this.classes==this.view.themeClasses)){for(let t of this.classes.split(" "))t&&this.container.classList.remove(t);for(let t of(this.classes=this.view.themeClasses).split(" "))t&&this.container.classList.add(t)}}}function Wl(s){let t=s.nextSibling;return s.remove(),t}const Ir=B.define({enables:gf});class Ee extends je{compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}eq(t){return!1}destroy(t){}}Ee.prototype.elementClass="";Ee.prototype.toDOM=void 0;Ee.prototype.mapMode=ct.TrackBefore;Ee.prototype.startSide=Ee.prototype.endSide=-1;Ee.prototype.point=!0;const Rs=B.define(),Up=B.define(),Gp={class:"",renderEmptyElements:!1,elementStyle:"",markers:()=>W.empty,lineMarker:()=>null,widgetMarker:()=>null,lineMarkerChange:null,initialSpacer:null,updateSpacer:null,domEventHandlers:{}},Ni=B.define();function Yp(s){return[bf(),Ni.of(Object.assign(Object.assign({},Gp),s))]}const zl=B.define({combine:s=>s.some(t=>t)});function bf(s){return[Jp]}const Jp=ut.fromClass(class{constructor(s){this.view=s,this.prevViewport=s.viewport,this.dom=document.createElement("div"),this.dom.className="cm-gutters",this.dom.setAttribute("aria-hidden","true"),this.dom.style.minHeight=this.view.contentHeight/this.view.scaleY+"px",this.gutters=s.state.facet(Ni).map(t=>new jl(s,t));for(let t of this.gutters)this.dom.appendChild(t.dom);this.fixed=!s.state.facet(zl),this.fixed&&(this.dom.style.position="sticky"),this.syncGutters(!1),s.scrollDOM.insertBefore(this.dom,s.contentDOM)}update(s){if(this.updateGutters(s)){let t=this.prevViewport,e=s.view.viewport,i=Math.min(t.to,e.to)-Math.max(t.from,e.from);this.syncGutters(i<(e.to-e.from)*.8)}s.geometryChanged&&(this.dom.style.minHeight=this.view.contentHeight/this.view.scaleY+"px"),this.view.state.facet(zl)!=!this.fixed&&(this.fixed=!this.fixed,this.dom.style.position=this.fixed?"sticky":""),this.prevViewport=s.view.viewport}syncGutters(s){let t=this.dom.nextSibling;s&&this.dom.remove();let e=W.iter(this.view.state.facet(Rs),this.view.viewport.from),i=[],n=this.gutters.map(r=>new Xp(r,this.view.viewport,-this.view.documentPadding.top));for(let r of this.view.viewportLineBlocks)if(i.length&&(i=[]),Array.isArray(r.type)){let o=!0;for(let l of r.type)if(l.type==xt.Text&&o){Nr(e,i,l.from);for(let a of n)a.line(this.view,l,i);o=!1}else if(l.widget)for(let a of n)a.widget(this.view,l)}else if(r.type==xt.Text){Nr(e,i,r.from);for(let o of n)o.line(this.view,r,i)}else if(r.widget)for(let o of n)o.widget(this.view,r);for(let r of n)r.finish();s&&this.view.scrollDOM.insertBefore(this.dom,t)}updateGutters(s){let t=s.startState.facet(Ni),e=s.state.facet(Ni),i=s.docChanged||s.heightChanged||s.viewportChanged||!W.eq(s.startState.facet(Rs),s.state.facet(Rs),s.view.viewport.from,s.view.viewport.to);if(t==e)for(let n of this.gutters)n.update(s)&&(i=!0);else{i=!0;let n=[];for(let r of e){let o=t.indexOf(r);o<0?n.push(new jl(this.view,r)):(this.gutters[o].update(s),n.push(this.gutters[o]))}for(let r of this.gutters)r.dom.remove(),n.indexOf(r)<0&&r.destroy();for(let r of n)this.dom.appendChild(r.dom);this.gutters=n}return i}destroy(){for(let s of this.gutters)s.destroy();this.dom.remove()}},{provide:s=>E.scrollMargins.of(t=>{let e=t.plugin(s);return!e||e.gutters.length==0||!e.fixed?null:t.textDirection==J.LTR?{left:e.dom.offsetWidth*t.scaleX}:{right:e.dom.offsetWidth*t.scaleX}})});function ql(s){return Array.isArray(s)?s:[s]}function Nr(s,t,e){for(;s.value&&s.from<=e;)s.from==e&&t.push(s.value),s.next()}class Xp{constructor(t,e,i){this.gutter=t,this.height=i,this.i=0,this.cursor=W.iter(t.markers,e.from)}addElement(t,e,i){let{gutter:n}=this,r=(e.top-this.height)/t.scaleY,o=e.height/t.scaleY;if(this.i==n.elements.length){let l=new yf(t,o,r,i);n.elements.push(l),n.dom.appendChild(l.dom)}else n.elements[this.i].update(t,o,r,i);this.height=e.bottom,this.i++}line(t,e,i){let n=[];Nr(this.cursor,n,e.from),i.length&&(n=n.concat(i));let r=this.gutter.config.lineMarker(t,e,n);r&&n.unshift(r);let o=this.gutter;n.length==0&&!o.config.renderEmptyElements||this.addElement(t,e,n)}widget(t,e){let i=this.gutter.config.widgetMarker(t,e.widget,e),n=i?[i]:null;for(let r of t.state.facet(Up)){let o=r(t,e.widget,e);o&&(n||(n=[])).push(o)}n&&this.addElement(t,e,n)}finish(){let t=this.gutter;for(;t.elements.length>this.i;){let e=t.elements.pop();t.dom.removeChild(e.dom),e.destroy()}}}class jl{constructor(t,e){this.view=t,this.config=e,this.elements=[],this.spacer=null,this.dom=document.createElement("div"),this.dom.className="cm-gutter"+(this.config.class?" "+this.config.class:"");for(let i in e.domEventHandlers)this.dom.addEventListener(i,n=>{let r=n.target,o;if(r!=this.dom&&this.dom.contains(r)){for(;r.parentNode!=this.dom;)r=r.parentNode;let a=r.getBoundingClientRect();o=(a.top+a.bottom)/2}else o=n.clientY;let l=t.lineBlockAtHeight(o-t.documentTop);e.domEventHandlers[i](t,l,n)&&n.preventDefault()});this.markers=ql(e.markers(t)),e.initialSpacer&&(this.spacer=new yf(t,0,0,[e.initialSpacer(t)]),this.dom.appendChild(this.spacer.dom),this.spacer.dom.style.cssText+="visibility: hidden; pointer-events: none")}update(t){let e=this.markers;if(this.markers=ql(this.config.markers(t.view)),this.spacer&&this.config.updateSpacer){let n=this.config.updateSpacer(this.spacer.markers[0],t);n!=this.spacer.markers[0]&&this.spacer.update(t.view,0,0,[n])}let i=t.view.viewport;return!W.eq(this.markers,e,i.from,i.to)||(this.config.lineMarkerChange?this.config.lineMarkerChange(t):!1)}destroy(){for(let t of this.elements)t.destroy()}}class yf{constructor(t,e,i,n){this.height=-1,this.above=0,this.markers=[],this.dom=document.createElement("div"),this.dom.className="cm-gutterElement",this.update(t,e,i,n)}update(t,e,i,n){this.height!=e&&(this.height=e,this.dom.style.height=e+"px"),this.above!=i&&(this.dom.style.marginTop=(this.above=i)?i+"px":""),$p(this.markers,n)||this.setMarkers(t,n)}setMarkers(t,e){let i="cm-gutterElement",n=this.dom.firstChild;for(let r=0,o=0;;){let l=o,a=r<e.length?e[r++]:null,h=!1;if(a){let f=a.elementClass;f&&(i+=" "+f);for(let c=o;c<this.markers.length;c++)if(this.markers[c].compare(a)){l=c,h=!0;break}}else l=this.markers.length;for(;o<l;){let f=this.markers[o++];if(f.toDOM){f.destroy(n);let c=n.nextSibling;n.remove(),n=c}}if(!a)break;a.toDOM&&(h?n=n.nextSibling:this.dom.insertBefore(a.toDOM(t),n)),h&&o++}this.dom.className=i,this.markers=e}destroy(){this.setMarkers(null,[])}}function $p(s,t){if(s.length!=t.length)return!1;for(let e=0;e<s.length;e++)if(!s[e].compare(t[e]))return!1;return!0}const Qp=B.define(),Zp=B.define(),ti=B.define({combine(s){return $e(s,{formatNumber:String,domEventHandlers:{}},{domEventHandlers(t,e){let i=Object.assign({},t);for(let n in e){let r=i[n],o=e[n];i[n]=r?(l,a,h)=>r(l,a,h)||o(l,a,h):o}return i}})}});class Is extends Ee{constructor(t){super(),this.number=t}eq(t){return this.number==t.number}toDOM(){return document.createTextNode(this.number)}}function Ns(s,t){return s.state.facet(ti).formatNumber(t,s.state)}const tm=Ni.compute([ti],s=>({class:"cm-lineNumbers",renderEmptyElements:!1,markers(t){return t.state.facet(Qp)},lineMarker(t,e,i){return i.some(n=>n.toDOM)?null:new Is(Ns(t,t.state.doc.lineAt(e.from).number))},widgetMarker:(t,e,i)=>{for(let n of t.state.facet(Zp)){let r=n(t,e,i);if(r)return r}return null},lineMarkerChange:t=>t.startState.facet(ti)!=t.state.facet(ti),initialSpacer(t){return new Is(Ns(t,Kl(t.state.doc.lines)))},updateSpacer(t,e){let i=Ns(e.view,Kl(e.view.state.doc.lines));return i==t.number?t:new Is(i)},domEventHandlers:s.facet(ti).domEventHandlers}));function em(s={}){return[ti.of(s),bf(),tm]}function Kl(s){let t=9;for(;t<s;)t=t*10+9;return t}const im=1024;let nm=0;class Ht{constructor(t,e){this.from=t,this.to=e}}class _{constructor(t={}){this.id=nm++,this.perNode=!!t.perNode,this.deserialize=t.deserialize||(()=>{throw new Error("This node type doesn't define a deserialize function")})}add(t){if(this.perNode)throw new RangeError("Can't add per-node props to node types");return typeof t!="function"&&(t=vt.match(t)),e=>{let i=t(e);return i===void 0?null:[this,i]}}}_.closedBy=new _({deserialize:s=>s.split(" ")});_.openedBy=new _({deserialize:s=>s.split(" ")});_.group=new _({deserialize:s=>s.split(" ")});_.isolate=new _({deserialize:s=>{if(s&&s!="rtl"&&s!="ltr"&&s!="auto")throw new RangeError("Invalid value for isolate: "+s);return s||"auto"}});_.contextHash=new _({perNode:!0});_.lookAhead=new _({perNode:!0});_.mounted=new _({perNode:!0});class ji{constructor(t,e,i){this.tree=t,this.overlay=e,this.parser=i}static get(t){return t&&t.props&&t.props[_.mounted.id]}}const sm=Object.create(null);class vt{constructor(t,e,i,n=0){this.name=t,this.props=e,this.id=i,this.flags=n}static define(t){let e=t.props&&t.props.length?Object.create(null):sm,i=(t.top?1:0)|(t.skipped?2:0)|(t.error?4:0)|(t.name==null?8:0),n=new vt(t.name||"",e,t.id,i);if(t.props){for(let r of t.props)if(Array.isArray(r)||(r=r(n)),r){if(r[0].perNode)throw new RangeError("Can't store a per-node prop on a node type");e[r[0].id]=r[1]}}return n}prop(t){return this.props[t.id]}get isTop(){return(this.flags&1)>0}get isSkipped(){return(this.flags&2)>0}get isError(){return(this.flags&4)>0}get isAnonymous(){return(this.flags&8)>0}is(t){if(typeof t=="string"){if(this.name==t)return!0;let e=this.prop(_.group);return e?e.indexOf(t)>-1:!1}return this.id==t}static match(t){let e=Object.create(null);for(let i in t)for(let n of i.split(" "))e[n]=t[i];return i=>{for(let n=i.prop(_.group),r=-1;r<(n?n.length:0);r++){let o=e[r<0?i.name:n[r]];if(o)return o}}}}vt.none=new vt("",Object.create(null),0,8);class fo{constructor(t){this.types=t;for(let e=0;e<t.length;e++)if(t[e].id!=e)throw new RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...t){let e=[];for(let i of this.types){let n=null;for(let r of t){let o=r(i);o&&(n||(n=Object.assign({},i.props)),n[o[0].id]=o[1])}e.push(n?new vt(i.name,n,i.id,i.flags):i)}return new fo(e)}}const Mn=new WeakMap,Ul=new WeakMap;var X;(function(s){s[s.ExcludeBuffers=1]="ExcludeBuffers",s[s.IncludeAnonymous=2]="IncludeAnonymous",s[s.IgnoreMounts=4]="IgnoreMounts",s[s.IgnoreOverlays=8]="IgnoreOverlays"})(X||(X={}));class G{constructor(t,e,i,n,r){if(this.type=t,this.children=e,this.positions=i,this.length=n,this.props=null,r&&r.length){this.props=Object.create(null);for(let[o,l]of r)this.props[typeof o=="number"?o:o.id]=l}}toString(){let t=ji.get(this);if(t&&!t.overlay)return t.tree.toString();let e="";for(let i of this.children){let n=i.toString();n&&(e&&(e+=","),e+=n)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(e.length?"("+e+")":""):e}cursor(t=0){return new is(this.topNode,t)}cursorAt(t,e=0,i=0){let n=Mn.get(this)||this.topNode,r=new is(n);return r.moveTo(t,e),Mn.set(this,r._tree),r}get topNode(){return new gt(this,0,0,null)}resolve(t,e=0){let i=Ki(Mn.get(this)||this.topNode,t,e,!1);return Mn.set(this,i),i}resolveInner(t,e=0){let i=Ki(Ul.get(this)||this.topNode,t,e,!0);return Ul.set(this,i),i}resolveStack(t,e=0){return lm(this,t,e)}iterate(t){let{enter:e,leave:i,from:n=0,to:r=this.length}=t,o=t.mode||0,l=(o&X.IncludeAnonymous)>0;for(let a=this.cursor(o|X.IncludeAnonymous);;){let h=!1;if(a.from<=r&&a.to>=n&&(!l&&a.type.isAnonymous||e(a)!==!1)){if(a.firstChild())continue;h=!0}for(;h&&i&&(l||!a.type.isAnonymous)&&i(a),!a.nextSibling();){if(!a.parent())return;h=!0}}}prop(t){return t.perNode?this.props?this.props[t.id]:void 0:this.type.prop(t)}get propValues(){let t=[];if(this.props)for(let e in this.props)t.push([+e,this.props[e]]);return t}balance(t={}){return this.children.length<=8?this:po(vt.none,this.children,this.positions,0,this.children.length,0,this.length,(e,i,n)=>new G(this.type,e,i,n,this.propValues),t.makeTree||((e,i,n)=>new G(vt.none,e,i,n)))}static build(t){return am(t)}}G.empty=new G(vt.none,[],[],0);class co{constructor(t,e){this.buffer=t,this.index=e}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new co(this.buffer,this.index)}}class Pe{constructor(t,e,i){this.buffer=t,this.length=e,this.set=i}get type(){return vt.none}toString(){let t=[];for(let e=0;e<this.buffer.length;)t.push(this.childString(e)),e=this.buffer[e+3];return t.join(",")}childString(t){let e=this.buffer[t],i=this.buffer[t+3],n=this.set.types[e],r=n.name;if(/\W/.test(r)&&!n.isError&&(r=JSON.stringify(r)),t+=4,i==t)return r;let o=[];for(;t<i;)o.push(this.childString(t)),t=this.buffer[t+3];return r+"("+o.join(",")+")"}findChild(t,e,i,n,r){let{buffer:o}=this,l=-1;for(let a=t;a!=e&&!(wf(r,n,o[a+1],o[a+2])&&(l=a,i>0));a=o[a+3]);return l}slice(t,e,i){let n=this.buffer,r=new Uint16Array(e-t),o=0;for(let l=t,a=0;l<e;){r[a++]=n[l++],r[a++]=n[l++]-i;let h=r[a++]=n[l++]-i;r[a++]=n[l++]-t,o=Math.max(o,h)}return new Pe(r,o,this.set)}}function wf(s,t,e,i){switch(s){case-2:return e<t;case-1:return i>=t&&e<t;case 0:return e<t&&i>t;case 1:return e<=t&&i>t;case 2:return i>t;case 4:return!0}}function Ki(s,t,e,i){for(var n;s.from==s.to||(e<1?s.from>=t:s.from>t)||(e>-1?s.to<=t:s.to<t);){let o=!i&&s instanceof gt&&s.index<0?null:s.parent;if(!o)return s;s=o}let r=i?0:X.IgnoreOverlays;if(i)for(let o=s,l=o.parent;l;o=l,l=o.parent)o instanceof gt&&o.index<0&&((n=l.enter(t,e,r))===null||n===void 0?void 0:n.from)!=o.from&&(s=l);for(;;){let o=s.enter(t,e,r);if(!o)return s;s=o}}class xf{cursor(t=0){return new is(this,t)}getChild(t,e=null,i=null){let n=Gl(this,t,e,i);return n.length?n[0]:null}getChildren(t,e=null,i=null){return Gl(this,t,e,i)}resolve(t,e=0){return Ki(this,t,e,!1)}resolveInner(t,e=0){return Ki(this,t,e,!0)}matchContext(t){return _r(this.parent,t)}enterUnfinishedNodesBefore(t){let e=this.childBefore(t),i=this;for(;e;){let n=e.lastChild;if(!n||n.to!=e.to)break;n.type.isError&&n.from==n.to?(i=e,e=n.prevSibling):e=n}return i}get node(){return this}get next(){return this.parent}}class gt extends xf{constructor(t,e,i,n){super(),this._tree=t,this.from=e,this.index=i,this._parent=n}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(t,e,i,n,r=0){for(let o=this;;){for(let{children:l,positions:a}=o._tree,h=e>0?l.length:-1;t!=h;t+=e){let f=l[t],c=a[t]+o.from;if(wf(n,i,c,c+f.length)){if(f instanceof Pe){if(r&X.ExcludeBuffers)continue;let u=f.findChild(0,f.buffer.length,e,i-c,n);if(u>-1)return new ae(new rm(o,f,t,c),null,u)}else if(r&X.IncludeAnonymous||!f.type.isAnonymous||uo(f)){let u;if(!(r&X.IgnoreMounts)&&(u=ji.get(f))&&!u.overlay)return new gt(u.tree,c,t,o);let d=new gt(f,c,t,o);return r&X.IncludeAnonymous||!d.type.isAnonymous?d:d.nextChild(e<0?f.children.length-1:0,e,i,n)}}}if(r&X.IncludeAnonymous||!o.type.isAnonymous||(o.index>=0?t=o.index+e:t=e<0?-1:o._parent._tree.children.length,o=o._parent,!o))return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(t){return this.nextChild(0,1,t,2)}childBefore(t){return this.nextChild(this._tree.children.length-1,-1,t,-2)}enter(t,e,i=0){let n;if(!(i&X.IgnoreOverlays)&&(n=ji.get(this._tree))&&n.overlay){let r=t-this.from;for(let{from:o,to:l}of n.overlay)if((e>0?o<=r:o<r)&&(e<0?l>=r:l>r))return new gt(n.tree,n.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,t,e,i)}nextSignificantParent(){let t=this;for(;t.type.isAnonymous&&t._parent;)t=t._parent;return t}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}get tree(){return this._tree}toTree(){return this._tree}toString(){return this._tree.toString()}}function Gl(s,t,e,i){let n=s.cursor(),r=[];if(!n.firstChild())return r;if(e!=null){for(let o=!1;!o;)if(o=n.type.is(e),!n.nextSibling())return r}for(;;){if(i!=null&&n.type.is(i))return r;if(n.type.is(t)&&r.push(n.node),!n.nextSibling())return i==null?r:[]}}function _r(s,t,e=t.length-1){for(let i=s;e>=0;i=i.parent){if(!i)return!1;if(!i.type.isAnonymous){if(t[e]&&t[e]!=i.name)return!1;e--}}return!0}class rm{constructor(t,e,i,n){this.parent=t,this.buffer=e,this.index=i,this.start=n}}class ae extends xf{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(t,e,i){super(),this.context=t,this._parent=e,this.index=i,this.type=t.buffer.set.types[t.buffer.buffer[i]]}child(t,e,i){let{buffer:n}=this.context,r=n.findChild(this.index+4,n.buffer[this.index+3],t,e-this.context.start,i);return r<0?null:new ae(this.context,this,r)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(t){return this.child(1,t,2)}childBefore(t){return this.child(-1,t,-2)}enter(t,e,i=0){if(i&X.ExcludeBuffers)return null;let{buffer:n}=this.context,r=n.findChild(this.index+4,n.buffer[this.index+3],e>0?1:-1,t-this.context.start,e);return r<0?null:new ae(this.context,this,r)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(t){return this._parent?null:this.context.parent.nextChild(this.context.index+t,t,0,4)}get nextSibling(){let{buffer:t}=this.context,e=t.buffer[this.index+3];return e<(this._parent?t.buffer[this._parent.index+3]:t.buffer.length)?new ae(this.context,this._parent,e):this.externalSibling(1)}get prevSibling(){let{buffer:t}=this.context,e=this._parent?this._parent.index+4:0;return this.index==e?this.externalSibling(-1):new ae(this.context,this._parent,t.findChild(e,this.index,-1,0,4))}get tree(){return null}toTree(){let t=[],e=[],{buffer:i}=this.context,n=this.index+4,r=i.buffer[this.index+3];if(r>n){let o=i.buffer[this.index+1];t.push(i.slice(n,r,o)),e.push(0)}return new G(this.type,t,e,this.to-this.from)}toString(){return this.context.buffer.childString(this.index)}}function kf(s){if(!s.length)return null;let t=0,e=s[0];for(let r=1;r<s.length;r++){let o=s[r];(o.from>e.from||o.to<e.to)&&(e=o,t=r)}let i=e instanceof gt&&e.index<0?null:e.parent,n=s.slice();return i?n[t]=i:n.splice(t,1),new om(n,e)}class om{constructor(t,e){this.heads=t,this.node=e}get next(){return kf(this.heads)}}function lm(s,t,e){let i=s.resolveInner(t,e),n=null;for(let r=i instanceof gt?i:i.context.parent;r;r=r.parent)if(r.index<0){let o=r.parent;(n||(n=[i])).push(o.resolve(t,e)),r=o}else{let o=ji.get(r.tree);if(o&&o.overlay&&o.overlay[0].from<=t&&o.overlay[o.overlay.length-1].to>=t){let l=new gt(o.tree,o.overlay[0].from+r.from,-1,r);(n||(n=[i])).push(Ki(l,t,e,!1))}}return n?kf(n):i}class is{get name(){return this.type.name}constructor(t,e=0){if(this.mode=e,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,t instanceof gt)this.yieldNode(t);else{this._tree=t.context.parent,this.buffer=t.context;for(let i=t._parent;i;i=i._parent)this.stack.unshift(i.index);this.bufferNode=t,this.yieldBuf(t.index)}}yieldNode(t){return t?(this._tree=t,this.type=t.type,this.from=t.from,this.to=t.to,!0):!1}yieldBuf(t,e){this.index=t;let{start:i,buffer:n}=this.buffer;return this.type=e||n.set.types[n.buffer[t]],this.from=i+n.buffer[t+1],this.to=i+n.buffer[t+2],!0}yield(t){return t?t instanceof gt?(this.buffer=null,this.yieldNode(t)):(this.buffer=t.context,this.yieldBuf(t.index,t.type)):!1}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(t,e,i){if(!this.buffer)return this.yield(this._tree.nextChild(t<0?this._tree._tree.children.length-1:0,t,e,i,this.mode));let{buffer:n}=this.buffer,r=n.findChild(this.index+4,n.buffer[this.index+3],t,e-this.buffer.start,i);return r<0?!1:(this.stack.push(this.index),this.yieldBuf(r))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(t){return this.enterChild(1,t,2)}childBefore(t){return this.enterChild(-1,t,-2)}enter(t,e,i=this.mode){return this.buffer?i&X.ExcludeBuffers?!1:this.enterChild(1,t,e):this.yield(this._tree.enter(t,e,i))}parent(){if(!this.buffer)return this.yieldNode(this.mode&X.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let t=this.mode&X.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(t)}sibling(t){if(!this.buffer)return this._tree._parent?this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+t,t,0,4,this.mode)):!1;let{buffer:e}=this.buffer,i=this.stack.length-1;if(t<0){let n=i<0?0:this.stack[i]+4;if(this.index!=n)return this.yieldBuf(e.findChild(n,this.index,-1,0,4))}else{let n=e.buffer[this.index+3];if(n<(i<0?e.buffer.length:e.buffer[this.stack[i]+3]))return this.yieldBuf(n)}return i<0?this.yield(this.buffer.parent.nextChild(this.buffer.index+t,t,0,4,this.mode)):!1}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(t){let e,i,{buffer:n}=this;if(n){if(t>0){if(this.index<n.buffer.buffer.length)return!1}else for(let r=0;r<this.index;r++)if(n.buffer.buffer[r+3]<this.index)return!1;({index:e,parent:i}=n)}else({index:e,_parent:i}=this._tree);for(;i;{index:e,_parent:i}=i)if(e>-1)for(let r=e+t,o=t<0?-1:i._tree.children.length;r!=o;r+=t){let l=i._tree.children[r];if(this.mode&X.IncludeAnonymous||l instanceof Pe||!l.type.isAnonymous||uo(l))return!1}return!0}move(t,e){if(e&&this.enterChild(t,0,4))return!0;for(;;){if(this.sibling(t))return!0;if(this.atLastNode(t)||!this.parent())return!1}}next(t=!0){return this.move(1,t)}prev(t=!0){return this.move(-1,t)}moveTo(t,e=0){for(;(this.from==this.to||(e<1?this.from>=t:this.from>t)||(e>-1?this.to<=t:this.to<t))&&this.parent(););for(;this.enterChild(1,t,e););return this}get node(){if(!this.buffer)return this._tree;let t=this.bufferNode,e=null,i=0;if(t&&t.context==this.buffer)t:for(let n=this.index,r=this.stack.length;r>=0;){for(let o=t;o;o=o._parent)if(o.index==n){if(n==this.index)return o;e=o,i=r+1;break t}n=this.stack[--r]}for(let n=i;n<this.stack.length;n++)e=new ae(this.buffer,e,this.stack[n]);return this.bufferNode=new ae(this.buffer,e,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(t,e){for(let i=0;;){let n=!1;if(this.type.isAnonymous||t(this)!==!1){if(this.firstChild()){i++;continue}this.type.isAnonymous||(n=!0)}for(;;){if(n&&e&&e(this),n=this.type.isAnonymous,!i)return;if(this.nextSibling())break;this.parent(),i--,n=!0}}}matchContext(t){if(!this.buffer)return _r(this.node.parent,t);let{buffer:e}=this.buffer,{types:i}=e.set;for(let n=t.length-1,r=this.stack.length-1;n>=0;r--){if(r<0)return _r(this._tree,t,n);let o=i[e.buffer[this.stack[r]]];if(!o.isAnonymous){if(t[n]&&t[n]!=o.name)return!1;n--}}return!0}}function uo(s){return s.children.some(t=>t instanceof Pe||!t.type.isAnonymous||uo(t))}function am(s){var t;let{buffer:e,nodeSet:i,maxBufferLength:n=im,reused:r=[],minRepeatType:o=i.types.length}=s,l=Array.isArray(e)?new co(e,e.length):e,a=i.types,h=0,f=0;function c(x,C,A,L,R,z){let{id:M,start:P,end:q,size:F}=l,j=f,ot=h;for(;F<0;)if(l.next(),F==-1){let Gt=r[M];A.push(Gt),L.push(P-x);return}else if(F==-3){h=M;return}else if(F==-4){f=M;return}else throw new RangeError(`Unrecognized record size: ${F}`);let yt=a[M],Nt,et,St=P-x;if(q-P<=n&&(et=g(l.pos-C,R))){let Gt=new Uint16Array(et.size-et.skip),O=l.pos-et.size,$=Gt.length;for(;l.pos>O;)$=y(et.start,Gt,$);Nt=new Pe(Gt,q-et.start,i),St=et.start-x}else{let Gt=l.pos-F;l.next();let O=[],$=[],lt=M>=o?M:-1,Yt=0,Ie=q;for(;l.pos>Gt;)lt>=0&&l.id==lt&&l.size>=0?(l.end<=Ie-n&&(p(O,$,P,Yt,l.end,Ie,lt,j,ot),Yt=O.length,Ie=l.end),l.next()):z>2500?u(P,Gt,O,$):c(P,Gt,O,$,lt,z+1);if(lt>=0&&Yt>0&&Yt<O.length&&p(O,$,P,Yt,P,Ie,lt,j,ot),O.reverse(),$.reverse(),lt>-1&&Yt>0){let cn=d(yt,ot);Nt=po(yt,O,$,0,O.length,0,q-P,cn,cn)}else Nt=m(yt,O,$,q-P,j-q,ot)}A.push(Nt),L.push(St)}function u(x,C,A,L){let R=[],z=0,M=-1;for(;l.pos>C;){let{id:P,start:q,end:F,size:j}=l;if(j>4)l.next();else{if(M>-1&&q<M)break;M<0&&(M=F-n),R.push(P,q,F),z++,l.next()}}if(z){let P=new Uint16Array(z*4),q=R[R.length-2];for(let F=R.length-3,j=0;F>=0;F-=3)P[j++]=R[F],P[j++]=R[F+1]-q,P[j++]=R[F+2]-q,P[j++]=j;A.push(new Pe(P,R[2]-q,i)),L.push(q-x)}}function d(x,C){return(A,L,R)=>{let z=0,M=A.length-1,P,q;if(M>=0&&(P=A[M])instanceof G){if(!M&&P.type==x&&P.length==R)return P;(q=P.prop(_.lookAhead))&&(z=L[M]+P.length+q)}return m(x,A,L,R,z,C)}}function p(x,C,A,L,R,z,M,P,q){let F=[],j=[];for(;x.length>L;)F.push(x.pop()),j.push(C.pop()+A-R);x.push(m(i.types[M],F,j,z-R,P-z,q)),C.push(R-A)}function m(x,C,A,L,R,z,M){if(z){let P=[_.contextHash,z];M=M?[P].concat(M):[P]}if(R>25){let P=[_.lookAhead,R];M=M?[P].concat(M):[P]}return new G(x,C,A,L,M)}function g(x,C){let A=l.fork(),L=0,R=0,z=0,M=A.end-n,P={size:0,start:0,skip:0};t:for(let q=A.pos-x;A.pos>q;){let F=A.size;if(A.id==C&&F>=0){P.size=L,P.start=R,P.skip=z,z+=4,L+=4,A.next();continue}let j=A.pos-F;if(F<0||j<q||A.start<M)break;let ot=A.id>=o?4:0,yt=A.start;for(A.next();A.pos>j;){if(A.size<0)if(A.size==-3)ot+=4;else break t;else A.id>=o&&(ot+=4);A.next()}R=yt,L+=F,z+=ot}return(C<0||L==x)&&(P.size=L,P.start=R,P.skip=z),P.size>4?P:void 0}function y(x,C,A){let{id:L,start:R,end:z,size:M}=l;if(l.next(),M>=0&&L<o){let P=A;if(M>4){let q=l.pos-(M-4);for(;l.pos>q;)A=y(x,C,A)}C[--A]=P,C[--A]=z-x,C[--A]=R-x,C[--A]=L}else M==-3?h=L:M==-4&&(f=L);return A}let w=[],S=[];for(;l.pos>0;)c(s.start||0,s.bufferStart||0,w,S,-1,0);let v=(t=s.length)!==null&&t!==void 0?t:w.length?S[0]+w[0].length:0;return new G(a[s.topID],w.reverse(),S.reverse(),v)}const Yl=new WeakMap;function jn(s,t){if(!s.isAnonymous||t instanceof Pe||t.type!=s)return 1;let e=Yl.get(t);if(e==null){e=1;for(let i of t.children){if(i.type!=s||!(i instanceof G)){e=1;break}e+=jn(s,i)}Yl.set(t,e)}return e}function po(s,t,e,i,n,r,o,l,a){let h=0;for(let p=i;p<n;p++)h+=jn(s,t[p]);let f=Math.ceil(h*1.5/8),c=[],u=[];function d(p,m,g,y,w){for(let S=g;S<y;){let v=S,x=m[S],C=jn(s,p[S]);for(S++;S<y;S++){let A=jn(s,p[S]);if(C+A>=f)break;C+=A}if(S==v+1){if(C>f){let A=p[v];d(A.children,A.positions,0,A.children.length,m[v]+w);continue}c.push(p[v])}else{let A=m[S-1]+p[S-1].length-x;c.push(po(s,p,m,v,S,x,A,null,a))}u.push(x+w-r)}}return d(t,e,i,n,0),(l||a)(c,u,o)}class Fy{constructor(){this.map=new WeakMap}setBuffer(t,e,i){let n=this.map.get(t);n||this.map.set(t,n=new Map),n.set(e,i)}getBuffer(t,e){let i=this.map.get(t);return i&&i.get(e)}set(t,e){t instanceof ae?this.setBuffer(t.context.buffer,t.index,e):t instanceof gt&&this.map.set(t.tree,e)}get(t){return t instanceof ae?this.getBuffer(t.context.buffer,t.index):t instanceof gt?this.map.get(t.tree):void 0}cursorSet(t,e){t.buffer?this.setBuffer(t.buffer.buffer,t.index,e):this.map.set(t.tree,e)}cursorGet(t){return t.buffer?this.getBuffer(t.buffer.buffer,t.index):this.map.get(t.tree)}}class be{constructor(t,e,i,n,r=!1,o=!1){this.from=t,this.to=e,this.tree=i,this.offset=n,this.open=(r?1:0)|(o?2:0)}get openStart(){return(this.open&1)>0}get openEnd(){return(this.open&2)>0}static addTree(t,e=[],i=!1){let n=[new be(0,t.length,t,0,!1,i)];for(let r of e)r.to>t.length&&n.push(r);return n}static applyChanges(t,e,i=128){if(!e.length)return t;let n=[],r=1,o=t.length?t[0]:null;for(let l=0,a=0,h=0;;l++){let f=l<e.length?e[l]:null,c=f?f.fromA:1e9;if(c-a>=i)for(;o&&o.from<c;){let u=o;if(a>=u.from||c<=u.to||h){let d=Math.max(u.from,a)-h,p=Math.min(u.to,c)-h;u=d>=p?null:new be(d,p,u.tree,u.offset+h,l>0,!!f)}if(u&&n.push(u),o.to>c)break;o=r<t.length?t[r++]:null}if(!f)break;a=f.toA,h=f.toA-f.toB}return n}}class vf{startParse(t,e,i){return typeof t=="string"&&(t=new hm(t)),i=i?i.length?i.map(n=>new Ht(n.from,n.to)):[new Ht(0,0)]:[new Ht(0,t.length)],this.createParse(t,e||[],i)}parse(t,e,i){let n=this.startParse(t,e,i);for(;;){let r=n.advance();if(r)return r}}}class hm{constructor(t){this.string=t}get length(){return this.string.length}chunk(t){return this.string.slice(t)}get lineChunks(){return!1}read(t,e){return this.string.slice(t,e)}}function Vy(s){return(t,e,i,n)=>new cm(t,s,e,i,n)}class Jl{constructor(t,e,i,n,r){this.parser=t,this.parse=e,this.overlay=i,this.target=n,this.from=r}}function Xl(s){if(!s.length||s.some(t=>t.from>=t.to))throw new RangeError("Invalid inner parse ranges given: "+JSON.stringify(s))}class fm{constructor(t,e,i,n,r,o,l){this.parser=t,this.predicate=e,this.mounts=i,this.index=n,this.start=r,this.target=o,this.prev=l,this.depth=0,this.ranges=[]}}const Fr=new _({perNode:!0});class cm{constructor(t,e,i,n,r){this.nest=e,this.input=i,this.fragments=n,this.ranges=r,this.inner=[],this.innerDone=0,this.baseTree=null,this.stoppedAt=null,this.baseParse=t}advance(){if(this.baseParse){let i=this.baseParse.advance();if(!i)return null;if(this.baseParse=null,this.baseTree=i,this.startInner(),this.stoppedAt!=null)for(let n of this.inner)n.parse.stopAt(this.stoppedAt)}if(this.innerDone==this.inner.length){let i=this.baseTree;return this.stoppedAt!=null&&(i=new G(i.type,i.children,i.positions,i.length,i.propValues.concat([[Fr,this.stoppedAt]]))),i}let t=this.inner[this.innerDone],e=t.parse.advance();if(e){this.innerDone++;let i=Object.assign(Object.create(null),t.target.props);i[_.mounted.id]=new ji(e,t.overlay,t.parser),t.target.props=i}return null}get parsedPos(){if(this.baseParse)return 0;let t=this.input.length;for(let e=this.innerDone;e<this.inner.length;e++)this.inner[e].from<t&&(t=Math.min(t,this.inner[e].parse.parsedPos));return t}stopAt(t){if(this.stoppedAt=t,this.baseParse)this.baseParse.stopAt(t);else for(let e=this.innerDone;e<this.inner.length;e++)this.inner[e].parse.stopAt(t)}startInner(){let t=new pm(this.fragments),e=null,i=null,n=new is(new gt(this.baseTree,this.ranges[0].from,0,null),X.IncludeAnonymous|X.IgnoreMounts);t:for(let r,o;;){let l=!0,a;if(this.stoppedAt!=null&&n.from>=this.stoppedAt)l=!1;else if(t.hasNode(n)){if(e){let h=e.mounts.find(f=>f.frag.from<=n.from&&f.frag.to>=n.to&&f.mount.overlay);if(h)for(let f of h.mount.overlay){let c=f.from+h.pos,u=f.to+h.pos;c>=n.from&&u<=n.to&&!e.ranges.some(d=>d.from<u&&d.to>c)&&e.ranges.push({from:c,to:u})}}l=!1}else if(i&&(o=um(i.ranges,n.from,n.to)))l=o!=2;else if(!n.type.isAnonymous&&(r=this.nest(n,this.input))&&(n.from<n.to||!r.overlay)){n.tree||dm(n);let h=t.findMounts(n.from,r.parser);if(typeof r.overlay=="function")e=new fm(r.parser,r.overlay,h,this.inner.length,n.from,n.tree,e);else{let f=Zl(this.ranges,r.overlay||(n.from<n.to?[new Ht(n.from,n.to)]:[]));f.length&&Xl(f),(f.length||!r.overlay)&&this.inner.push(new Jl(r.parser,f.length?r.parser.startParse(this.input,ta(h,f),f):r.parser.startParse(""),r.overlay?r.overlay.map(c=>new Ht(c.from-n.from,c.to-n.from)):null,n.tree,f.length?f[0].from:n.from)),r.overlay?f.length&&(i={ranges:f,depth:0,prev:i}):l=!1}}else if(e&&(a=e.predicate(n))&&(a===!0&&(a=new Ht(n.from,n.to)),a.from<a.to)){let h=e.ranges.length-1;h>=0&&e.ranges[h].to==a.from?e.ranges[h]={from:e.ranges[h].from,to:a.to}:e.ranges.push(a)}if(l&&n.firstChild())e&&e.depth++,i&&i.depth++;else for(;!n.nextSibling();){if(!n.parent())break t;if(e&&!--e.depth){let h=Zl(this.ranges,e.ranges);h.length&&(Xl(h),this.inner.splice(e.index,0,new Jl(e.parser,e.parser.startParse(this.input,ta(e.mounts,h),h),e.ranges.map(f=>new Ht(f.from-e.start,f.to-e.start)),e.target,h[0].from))),e=e.prev}i&&!--i.depth&&(i=i.prev)}}}}function um(s,t,e){for(let i of s){if(i.from>=e)break;if(i.to>t)return i.from<=t&&i.to>=e?2:1}return 0}function $l(s,t,e,i,n,r){if(t<e){let o=s.buffer[t+1];i.push(s.slice(t,e,o)),n.push(o-r)}}function dm(s){let{node:t}=s,e=[],i=t.context.buffer;do e.push(s.index),s.parent();while(!s.tree);let n=s.tree,r=n.children.indexOf(i),o=n.children[r],l=o.buffer,a=[r];function h(f,c,u,d,p,m){let g=e[m],y=[],w=[];$l(o,f,g,y,w,d);let S=l[g+1],v=l[g+2];a.push(y.length);let x=m?h(g+4,l[g+3],o.set.types[l[g]],S,v-S,m-1):t.toTree();return y.push(x),w.push(S-d),$l(o,l[g+3],c,y,w,d),new G(u,y,w,p)}n.children[r]=h(0,l.length,vt.none,0,o.length,e.length-1);for(let f of a){let c=s.tree.children[f],u=s.tree.positions[f];s.yield(new gt(c,u+s.from,f,s._tree))}}class Ql{constructor(t,e){this.offset=e,this.done=!1,this.cursor=t.cursor(X.IncludeAnonymous|X.IgnoreMounts)}moveTo(t){let{cursor:e}=this,i=t-this.offset;for(;!this.done&&e.from<i;)e.to>=t&&e.enter(i,1,X.IgnoreOverlays|X.ExcludeBuffers)||e.next(!1)||(this.done=!0)}hasNode(t){if(this.moveTo(t.from),!this.done&&this.cursor.from+this.offset==t.from&&this.cursor.tree)for(let e=this.cursor.tree;;){if(e==t.tree)return!0;if(e.children.length&&e.positions[0]==0&&e.children[0]instanceof G)e=e.children[0];else break}return!1}}class pm{constructor(t){var e;if(this.fragments=t,this.curTo=0,this.fragI=0,t.length){let i=this.curFrag=t[0];this.curTo=(e=i.tree.prop(Fr))!==null&&e!==void 0?e:i.to,this.inner=new Ql(i.tree,-i.offset)}else this.curFrag=this.inner=null}hasNode(t){for(;this.curFrag&&t.from>=this.curTo;)this.nextFrag();return this.curFrag&&this.curFrag.from<=t.from&&this.curTo>=t.to&&this.inner.hasNode(t)}nextFrag(){var t;if(this.fragI++,this.fragI==this.fragments.length)this.curFrag=this.inner=null;else{let e=this.curFrag=this.fragments[this.fragI];this.curTo=(t=e.tree.prop(Fr))!==null&&t!==void 0?t:e.to,this.inner=new Ql(e.tree,-e.offset)}}findMounts(t,e){var i;let n=[];if(this.inner){this.inner.cursor.moveTo(t,1);for(let r=this.inner.cursor.node;r;r=r.parent){let o=(i=r.tree)===null||i===void 0?void 0:i.prop(_.mounted);if(o&&o.parser==e)for(let l=this.fragI;l<this.fragments.length;l++){let a=this.fragments[l];if(a.from>=r.to)break;a.tree==this.curFrag.tree&&n.push({frag:a,pos:r.from-a.offset,mount:o})}}}return n}}function Zl(s,t){let e=null,i=t;for(let n=1,r=0;n<s.length;n++){let o=s[n-1].to,l=s[n].from;for(;r<i.length;r++){let a=i[r];if(a.from>=l)break;a.to<=o||(e||(i=e=t.slice()),a.from<o?(e[r]=new Ht(a.from,o),a.to>l&&e.splice(r+1,0,new Ht(l,a.to))):a.to>l?e[r--]=new Ht(l,a.to):e.splice(r--,1))}}return i}function mm(s,t,e,i){let n=0,r=0,o=!1,l=!1,a=-1e9,h=[];for(;;){let f=n==s.length?1e9:o?s[n].to:s[n].from,c=r==t.length?1e9:l?t[r].to:t[r].from;if(o!=l){let u=Math.max(a,e),d=Math.min(f,c,i);u<d&&h.push(new Ht(u,d))}if(a=Math.min(f,c),a==1e9)break;f==a&&(o?(o=!1,n++):o=!0),c==a&&(l?(l=!1,r++):l=!0)}return h}function ta(s,t){let e=[];for(let{pos:i,mount:n,frag:r}of s){let o=i+(n.overlay?n.overlay[0].from:0),l=o+n.tree.length,a=Math.max(r.from,o),h=Math.min(r.to,l);if(n.overlay){let f=n.overlay.map(u=>new Ht(u.from+i,u.to+i)),c=mm(t,f,a,h);for(let u=0,d=a;;u++){let p=u==c.length,m=p?h:c[u].from;if(m>d&&e.push(new be(d,m,n.tree,-o,r.from>=d||r.openStart,r.to<=m||r.openEnd)),p)break;d=c[u].to}}else e.push(new be(a,h,n.tree,-o,r.from>=o||r.openStart,r.to<=l||r.openEnd))}return e}let gm=0;class Ft{constructor(t,e,i,n){this.name=t,this.set=e,this.base=i,this.modified=n,this.id=gm++}toString(){let{name:t}=this;for(let e of this.modified)e.name&&(t=`${e.name}(${t})`);return t}static define(t,e){let i=typeof t=="string"?t:"?";if(t instanceof Ft&&(e=t),e!=null&&e.base)throw new Error("Can not derive from a modified tag");let n=new Ft(i,[],null,[]);if(n.set.push(n),e)for(let r of e.set)n.set.push(r);return n}static defineModifier(t){let e=new ns(t);return i=>i.modified.indexOf(e)>-1?i:ns.get(i.base||i,i.modified.concat(e).sort((n,r)=>n.id-r.id))}}let bm=0;class ns{constructor(t){this.name=t,this.instances=[],this.id=bm++}static get(t,e){if(!e.length)return t;let i=e[0].instances.find(l=>l.base==t&&ym(e,l.modified));if(i)return i;let n=[],r=new Ft(t.name,n,t,e);for(let l of e)l.instances.push(r);let o=wm(e);for(let l of t.set)if(!l.modified.length)for(let a of o)n.push(ns.get(l,a));return r}}function ym(s,t){return s.length==t.length&&s.every((e,i)=>e==t[i])}function wm(s){let t=[[]];for(let e=0;e<s.length;e++)for(let i=0,n=t.length;i<n;i++)t.push(t[i].concat(s[e]));return t.sort((e,i)=>i.length-e.length)}function xm(s){let t=Object.create(null);for(let e in s){let i=s[e];Array.isArray(i)||(i=[i]);for(let n of e.split(" "))if(n){let r=[],o=2,l=n;for(let c=0;;){if(l=="..."&&c>0&&c+3==n.length){o=1;break}let u=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(l);if(!u)throw new RangeError("Invalid path: "+n);if(r.push(u[0]=="*"?"":u[0][0]=='"'?JSON.parse(u[0]):u[0]),c+=u[0].length,c==n.length)break;let d=n[c++];if(c==n.length&&d=="!"){o=0;break}if(d!="/")throw new RangeError("Invalid path: "+n);l=n.slice(c)}let a=r.length-1,h=r[a];if(!h)throw new RangeError("Invalid path: "+n);let f=new ss(i,o,a>0?r.slice(0,a):null);t[h]=f.sort(t[h])}}return Sf.add(t)}const Sf=new _;class ss{constructor(t,e,i,n){this.tags=t,this.mode=e,this.context=i,this.next=n}get opaque(){return this.mode==0}get inherit(){return this.mode==1}sort(t){return!t||t.depth<this.depth?(this.next=t,this):(t.next=this.sort(t.next),t)}get depth(){return this.context?this.context.length:0}}ss.empty=new ss([],2,null);function Cf(s,t){let e=Object.create(null);for(let r of s)if(!Array.isArray(r.tag))e[r.tag.id]=r.class;else for(let o of r.tag)e[o.id]=r.class;let{scope:i,all:n=null}=t||{};return{style:r=>{let o=n;for(let l of r)for(let a of l.set){let h=e[a.id];if(h){o=o?o+" "+h:h;break}}return o},scope:i}}function km(s,t){let e=null;for(let i of s){let n=i.style(t);n&&(e=e?e+" "+n:n)}return e}function vm(s,t,e,i=0,n=s.length){let r=new Sm(i,Array.isArray(t)?t:[t],e);r.highlightRange(s.cursor(),i,n,"",r.highlighters),r.flush(n)}class Sm{constructor(t,e,i){this.at=t,this.highlighters=e,this.span=i,this.class=""}startSpan(t,e){e!=this.class&&(this.flush(t),t>this.at&&(this.at=t),this.class=e)}flush(t){t>this.at&&this.class&&this.span(this.at,t,this.class)}highlightRange(t,e,i,n,r){let{type:o,from:l,to:a}=t;if(l>=i||a<=e)return;o.isTop&&(r=this.highlighters.filter(d=>!d.scope||d.scope(o)));let h=n,f=Cm(t)||ss.empty,c=km(r,f.tags);if(c&&(h&&(h+=" "),h+=c,f.mode==1&&(n+=(n?" ":"")+c)),this.startSpan(Math.max(e,l),h),f.opaque)return;let u=t.tree&&t.tree.prop(_.mounted);if(u&&u.overlay){let d=t.node.enter(u.overlay[0].from+l,1),p=this.highlighters.filter(g=>!g.scope||g.scope(u.tree.type)),m=t.firstChild();for(let g=0,y=l;;g++){let w=g<u.overlay.length?u.overlay[g]:null,S=w?w.from+l:a,v=Math.max(e,y),x=Math.min(i,S);if(v<x&&m)for(;t.from<x&&(this.highlightRange(t,v,x,n,r),this.startSpan(Math.min(x,t.to),h),!(t.to>=S||!t.nextSibling())););if(!w||S>i)break;y=w.to+l,y>e&&(this.highlightRange(d.cursor(),Math.max(e,w.from+l),Math.min(i,y),"",p),this.startSpan(Math.min(i,y),h))}m&&t.parent()}else if(t.firstChild()){u&&(n="");do if(!(t.to<=e)){if(t.from>=i)break;this.highlightRange(t,e,i,n,r),this.startSpan(Math.min(i,t.to),h)}while(t.nextSibling());t.parent()}}}function Cm(s){let t=s.type.prop(Sf);for(;t&&t.context&&!s.matchContext(t.context);)t=t.next;return t||null}const D=Ft.define,Dn=D(),xe=D(),ea=D(xe),ia=D(xe),ke=D(),Tn=D(ke),_s=D(ke),ne=D(),Ne=D(ne),ee=D(),ie=D(),Vr=D(),Si=D(Vr),On=D(),b={comment:Dn,lineComment:D(Dn),blockComment:D(Dn),docComment:D(Dn),name:xe,variableName:D(xe),typeName:ea,tagName:D(ea),propertyName:ia,attributeName:D(ia),className:D(xe),labelName:D(xe),namespace:D(xe),macroName:D(xe),literal:ke,string:Tn,docString:D(Tn),character:D(Tn),attributeValue:D(Tn),number:_s,integer:D(_s),float:D(_s),bool:D(ke),regexp:D(ke),escape:D(ke),color:D(ke),url:D(ke),keyword:ee,self:D(ee),null:D(ee),atom:D(ee),unit:D(ee),modifier:D(ee),operatorKeyword:D(ee),controlKeyword:D(ee),definitionKeyword:D(ee),moduleKeyword:D(ee),operator:ie,derefOperator:D(ie),arithmeticOperator:D(ie),logicOperator:D(ie),bitwiseOperator:D(ie),compareOperator:D(ie),updateOperator:D(ie),definitionOperator:D(ie),typeOperator:D(ie),controlOperator:D(ie),punctuation:Vr,separator:D(Vr),bracket:Si,angleBracket:D(Si),squareBracket:D(Si),paren:D(Si),brace:D(Si),content:ne,heading:Ne,heading1:D(Ne),heading2:D(Ne),heading3:D(Ne),heading4:D(Ne),heading5:D(Ne),heading6:D(Ne),contentSeparator:D(ne),list:D(ne),quote:D(ne),emphasis:D(ne),strong:D(ne),link:D(ne),monospace:D(ne),strikethrough:D(ne),inserted:D(),deleted:D(),changed:D(),invalid:D(),meta:On,documentMeta:D(On),annotation:D(On),processingInstruction:D(On),definition:Ft.defineModifier("definition"),constant:Ft.defineModifier("constant"),function:Ft.defineModifier("function"),standard:Ft.defineModifier("standard"),local:Ft.defineModifier("local"),special:Ft.defineModifier("special")};for(let s in b){let t=b[s];t instanceof Ft&&(t.name=s)}Cf([{tag:b.link,class:"tok-link"},{tag:b.heading,class:"tok-heading"},{tag:b.emphasis,class:"tok-emphasis"},{tag:b.strong,class:"tok-strong"},{tag:b.keyword,class:"tok-keyword"},{tag:b.atom,class:"tok-atom"},{tag:b.bool,class:"tok-bool"},{tag:b.url,class:"tok-url"},{tag:b.labelName,class:"tok-labelName"},{tag:b.inserted,class:"tok-inserted"},{tag:b.deleted,class:"tok-deleted"},{tag:b.literal,class:"tok-literal"},{tag:b.string,class:"tok-string"},{tag:b.number,class:"tok-number"},{tag:[b.regexp,b.escape,b.special(b.string)],class:"tok-string2"},{tag:b.variableName,class:"tok-variableName"},{tag:b.local(b.variableName),class:"tok-variableName tok-local"},{tag:b.definition(b.variableName),class:"tok-variableName tok-definition"},{tag:b.special(b.variableName),class:"tok-variableName2"},{tag:b.definition(b.propertyName),class:"tok-propertyName tok-definition"},{tag:b.typeName,class:"tok-typeName"},{tag:b.namespace,class:"tok-namespace"},{tag:b.className,class:"tok-className"},{tag:b.macroName,class:"tok-macroName"},{tag:b.propertyName,class:"tok-propertyName"},{tag:b.operator,class:"tok-operator"},{tag:b.comment,class:"tok-comment"},{tag:b.meta,class:"tok-meta"},{tag:b.invalid,class:"tok-invalid"},{tag:b.punctuation,class:"tok-punctuation"}]);var Fs;const We=new _;function Af(s){return B.define({combine:s?t=>t.concat(s):void 0})}const Am=new _;class Wt{constructor(t,e,i=[],n=""){this.data=t,this.name=n,H.prototype.hasOwnProperty("tree")||Object.defineProperty(H.prototype,"tree",{get(){return dt(this)}}),this.parser=e,this.extension=[Le.of(this),H.languageData.of((r,o,l)=>{let a=na(r,o,l),h=a.type.prop(We);if(!h)return[];let f=r.facet(h),c=a.type.prop(Am);if(c){let u=a.resolve(o-a.from,l);for(let d of c)if(d.test(u,r)){let p=r.facet(d.facet);return d.type=="replace"?p:p.concat(f)}}return f})].concat(i)}isActiveAt(t,e,i=-1){return na(t,e,i).type.prop(We)==this.data}findRegions(t){let e=t.facet(Le);if((e==null?void 0:e.data)==this.data)return[{from:0,to:t.doc.length}];if(!e||!e.allowsNesting)return[];let i=[],n=(r,o)=>{if(r.prop(We)==this.data){i.push({from:o,to:o+r.length});return}let l=r.prop(_.mounted);if(l){if(l.tree.prop(We)==this.data){if(l.overlay)for(let a of l.overlay)i.push({from:a.from+o,to:a.to+o});else i.push({from:o,to:o+r.length});return}else if(l.overlay){let a=i.length;if(n(l.tree,l.overlay[0].from+o),i.length>a)return}}for(let a=0;a<r.children.length;a++){let h=r.children[a];h instanceof G&&n(h,r.positions[a]+o)}};return n(dt(t),0),i}get allowsNesting(){return!0}}Wt.setState=I.define();function na(s,t,e){let i=s.facet(Le),n=dt(s).topNode;if(!i||i.allowsNesting)for(let r=n;r;r=r.enter(t,e,X.ExcludeBuffers))r.type.isTop&&(n=r);return n}class Hr extends Wt{constructor(t,e,i){super(t,e,[],i),this.parser=e}static define(t){let e=Af(t.languageData);return new Hr(e,t.parser.configure({props:[We.add(i=>i.isTop?e:void 0)]}),t.name)}configure(t,e){return new Hr(this.data,this.parser.configure(t),e||this.name)}get allowsNesting(){return this.parser.hasWrappers()}}function dt(s){let t=s.field(Wt.state,!1);return t?t.tree:G.empty}class Mm{constructor(t){this.doc=t,this.cursorPos=0,this.string="",this.cursor=t.iter()}get length(){return this.doc.length}syncTo(t){return this.string=this.cursor.next(t-this.cursorPos).value,this.cursorPos=t+this.string.length,this.cursorPos-this.string.length}chunk(t){return this.syncTo(t),this.string}get lineChunks(){return!0}read(t,e){let i=this.cursorPos-this.string.length;return t<i||e>=this.cursorPos?this.doc.sliceString(t,e):this.string.slice(t-i,e-i)}}let Ci=null;class di{constructor(t,e,i=[],n,r,o,l,a){this.parser=t,this.state=e,this.fragments=i,this.tree=n,this.treeLen=r,this.viewport=o,this.skipped=l,this.scheduleOn=a,this.parse=null,this.tempSkipped=[]}static create(t,e,i){return new di(t,e,[],G.empty,0,i,[],null)}startParse(){return this.parser.startParse(new Mm(this.state.doc),this.fragments)}work(t,e){return e!=null&&e>=this.state.doc.length&&(e=void 0),this.tree!=G.empty&&this.isDone(e??this.state.doc.length)?(this.takeTree(),!0):this.withContext(()=>{var i;if(typeof t=="number"){let n=Date.now()+t;t=()=>Date.now()>n}for(this.parse||(this.parse=this.startParse()),e!=null&&(this.parse.stoppedAt==null||this.parse.stoppedAt>e)&&e<this.state.doc.length&&this.parse.stopAt(e);;){let n=this.parse.advance();if(n)if(this.fragments=this.withoutTempSkipped(be.addTree(n,this.fragments,this.parse.stoppedAt!=null)),this.treeLen=(i=this.parse.stoppedAt)!==null&&i!==void 0?i:this.state.doc.length,this.tree=n,this.parse=null,this.treeLen<(e??this.state.doc.length))this.parse=this.startParse();else return!0;if(t())return!1}})}takeTree(){let t,e;this.parse&&(t=this.parse.parsedPos)>=this.treeLen&&((this.parse.stoppedAt==null||this.parse.stoppedAt>t)&&this.parse.stopAt(t),this.withContext(()=>{for(;!(e=this.parse.advance()););}),this.treeLen=t,this.tree=e,this.fragments=this.withoutTempSkipped(be.addTree(this.tree,this.fragments,!0)),this.parse=null)}withContext(t){let e=Ci;Ci=this;try{return t()}finally{Ci=e}}withoutTempSkipped(t){for(let e;e=this.tempSkipped.pop();)t=sa(t,e.from,e.to);return t}changes(t,e){let{fragments:i,tree:n,treeLen:r,viewport:o,skipped:l}=this;if(this.takeTree(),!t.empty){let a=[];if(t.iterChangedRanges((h,f,c,u)=>a.push({fromA:h,toA:f,fromB:c,toB:u})),i=be.applyChanges(i,a),n=G.empty,r=0,o={from:t.mapPos(o.from,-1),to:t.mapPos(o.to,1)},this.skipped.length){l=[];for(let h of this.skipped){let f=t.mapPos(h.from,1),c=t.mapPos(h.to,-1);f<c&&l.push({from:f,to:c})}}}return new di(this.parser,e,i,n,r,o,l,this.scheduleOn)}updateViewport(t){if(this.viewport.from==t.from&&this.viewport.to==t.to)return!1;this.viewport=t;let e=this.skipped.length;for(let i=0;i<this.skipped.length;i++){let{from:n,to:r}=this.skipped[i];n<t.to&&r>t.from&&(this.fragments=sa(this.fragments,n,r),this.skipped.splice(i--,1))}return this.skipped.length>=e?!1:(this.reset(),!0)}reset(){this.parse&&(this.takeTree(),this.parse=null)}skipUntilInView(t,e){this.skipped.push({from:t,to:e})}static getSkippingParser(t){return new class extends vf{createParse(e,i,n){let r=n[0].from,o=n[n.length-1].to;return{parsedPos:r,advance(){let a=Ci;if(a){for(let h of n)a.tempSkipped.push(h);t&&(a.scheduleOn=a.scheduleOn?Promise.all([a.scheduleOn,t]):t)}return this.parsedPos=o,new G(vt.none,[],[],o-r)},stoppedAt:null,stopAt(){}}}}}isDone(t){t=Math.min(t,this.state.doc.length);let e=this.fragments;return this.treeLen>=t&&e.length&&e[0].from==0&&e[0].to>=t}static get(){return Ci}}function sa(s,t,e){return be.applyChanges(s,[{fromA:t,toA:e,fromB:t,toB:e}])}class pi{constructor(t){this.context=t,this.tree=t.tree}apply(t){if(!t.docChanged&&this.tree==this.context.tree)return this;let e=this.context.changes(t.changes,t.state),i=this.context.treeLen==t.startState.doc.length?void 0:Math.max(t.changes.mapPos(this.context.treeLen),e.viewport.to);return e.work(20,i)||e.takeTree(),new pi(e)}static init(t){let e=Math.min(3e3,t.doc.length),i=di.create(t.facet(Le).parser,t,{from:0,to:e});return i.work(20,e)||i.takeTree(),new pi(i)}}Wt.state=Tt.define({create:pi.init,update(s,t){for(let e of t.effects)if(e.is(Wt.setState))return e.value;return t.startState.facet(Le)!=t.state.facet(Le)?pi.init(t.state):s.apply(t)}});let Mf=s=>{let t=setTimeout(()=>s(),500);return()=>clearTimeout(t)};typeof requestIdleCallback<"u"&&(Mf=s=>{let t=-1,e=setTimeout(()=>{t=requestIdleCallback(s,{timeout:400})},100);return()=>t<0?clearTimeout(e):cancelIdleCallback(t)});const Vs=typeof navigator<"u"&&(!((Fs=navigator.scheduling)===null||Fs===void 0)&&Fs.isInputPending)?()=>navigator.scheduling.isInputPending():null,Dm=ut.fromClass(class{constructor(t){this.view=t,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork()}update(t){let e=this.view.state.field(Wt.state).context;(e.updateViewport(t.view.viewport)||this.view.viewport.to>e.treeLen)&&this.scheduleWork(),(t.docChanged||t.selectionSet)&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(e)}scheduleWork(){if(this.working)return;let{state:t}=this.view,e=t.field(Wt.state);(e.tree!=e.context.tree||!e.context.isDone(t.doc.length))&&(this.working=Mf(this.work))}work(t){this.working=null;let e=Date.now();if(this.chunkEnd<e&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=e+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:i,viewport:{to:n}}=this.view,r=i.field(Wt.state);if(r.tree==r.context.tree&&r.context.isDone(n+1e5))return;let o=Date.now()+Math.min(this.chunkBudget,100,t&&!Vs?Math.max(25,t.timeRemaining()-5):1e9),l=r.context.treeLen<n&&i.doc.length>n+1e3,a=r.context.work(()=>Vs&&Vs()||Date.now()>o,n+(l?0:1e5));this.chunkBudget-=Date.now()-e,(a||this.chunkBudget<=0)&&(r.context.takeTree(),this.view.dispatch({effects:Wt.setState.of(new pi(r.context))})),this.chunkBudget>0&&!(a&&!l)&&this.scheduleWork(),this.checkAsyncSchedule(r.context)}checkAsyncSchedule(t){t.scheduleOn&&(this.workScheduled++,t.scheduleOn.then(()=>this.scheduleWork()).catch(e=>Mt(this.view.state,e)).then(()=>this.workScheduled--),t.scheduleOn=null)}destroy(){this.working&&this.working()}isWorking(){return!!(this.working||this.workScheduled>0)}},{eventHandlers:{focus(){this.scheduleWork()}}}),Le=B.define({combine(s){return s.length?s[0]:null},enables:s=>[Wt.state,Dm,E.contentAttributes.compute([s],t=>{let e=t.facet(s);return e&&e.name?{"data-language":e.name}:{}})]});class Tm{constructor(t,e=[]){this.language=t,this.support=e,this.extension=[t,e]}}class Df{constructor(t,e,i,n,r,o=void 0){this.name=t,this.alias=e,this.extensions=i,this.filename=n,this.loadFunc=r,this.support=o,this.loading=null}load(){return this.loading||(this.loading=this.loadFunc().then(t=>this.support=t,t=>{throw this.loading=null,t}))}static of(t){let{load:e,support:i}=t;if(!e){if(!i)throw new RangeError("Must pass either 'load' or 'support' to LanguageDescription.of");e=()=>Promise.resolve(i)}return new Df(t.name,(t.alias||[]).concat(t.name).map(n=>n.toLowerCase()),t.extensions||[],t.filename,e,i)}static matchFilename(t,e){for(let n of t)if(n.filename&&n.filename.test(e))return n;let i=/\.([^.]+)$/.exec(e);if(i){for(let n of t)if(n.extensions.indexOf(i[1])>-1)return n}return null}static matchLanguageName(t,e,i=!0){e=e.toLowerCase();for(let n of t)if(n.alias.some(r=>r==e))return n;if(i)for(let n of t)for(let r of n.alias){let o=e.indexOf(r);if(o>-1&&(r.length>2||!/\w/.test(e[o-1])&&!/\w/.test(e[o+r.length])))return n}return null}}const Om=B.define(),ps=B.define({combine:s=>{if(!s.length)return"  ";let t=s[0];if(!t||/\S/.test(t)||Array.from(t).some(e=>e!=t[0]))throw new Error("Invalid indent unit: "+JSON.stringify(s[0]));return t}});function Ye(s){let t=s.facet(ps);return t.charCodeAt(0)==9?s.tabSize*t.length:t.length}function Ui(s,t){let e="",i=s.tabSize,n=s.facet(ps)[0];if(n=="	"){for(;t>=i;)e+="	",t-=i;n=" "}for(let r=0;r<t;r++)e+=n;return e}function mo(s,t){s instanceof H&&(s=new ms(s));for(let i of s.state.facet(Om)){let n=i(s,t);if(n!==void 0)return n}let e=dt(s.state);return e.length>=t?Bm(s,e,t):null}class ms{constructor(t,e={}){this.state=t,this.options=e,this.unit=Ye(t)}lineAt(t,e=1){let i=this.state.doc.lineAt(t),{simulateBreak:n,simulateDoubleBreak:r}=this.options;return n!=null&&n>=i.from&&n<=i.to?r&&n==t?{text:"",from:t}:(e<0?n<t:n<=t)?{text:i.text.slice(n-i.from),from:n}:{text:i.text.slice(0,n-i.from),from:i.from}:i}textAfterPos(t,e=1){if(this.options.simulateDoubleBreak&&t==this.options.simulateBreak)return"";let{text:i,from:n}=this.lineAt(t,e);return i.slice(t-n,Math.min(i.length,t+100-n))}column(t,e=1){let{text:i,from:n}=this.lineAt(t,e),r=this.countColumn(i,t-n),o=this.options.overrideIndentation?this.options.overrideIndentation(n):-1;return o>-1&&(r+=o-this.countColumn(i,i.search(/\S|$/))),r}countColumn(t,e=t.length){return gi(t,this.state.tabSize,e)}lineIndent(t,e=1){let{text:i,from:n}=this.lineAt(t,e),r=this.options.overrideIndentation;if(r){let o=r(n);if(o>-1)return o}return this.countColumn(i,i.search(/\S|$/))}get simulatedBreak(){return this.options.simulateBreak||null}}const Tf=new _;function Bm(s,t,e){let i=t.resolveStack(e),n=t.resolveInner(e,-1).resolve(e,0).enterUnfinishedNodesBefore(e);if(n!=i.node){let r=[];for(let o=n;o&&!(o.from==i.node.from&&o.type==i.node.type);o=o.parent)r.push(o);for(let o=r.length-1;o>=0;o--)i={node:r[o],next:i}}return Of(i,s,e)}function Of(s,t,e){for(let i=s;i;i=i.next){let n=Pm(i.node);if(n)return n(go.create(t,e,i))}return 0}function Em(s){return s.pos==s.options.simulateBreak&&s.options.simulateDoubleBreak}function Pm(s){let t=s.type.prop(Tf);if(t)return t;let e=s.firstChild,i;if(e&&(i=e.type.prop(_.closedBy))){let n=s.lastChild,r=n&&i.indexOf(n.name)>-1;return o=>Bf(o,!0,1,void 0,r&&!Em(o)?n.from:void 0)}return s.parent==null?Lm:null}function Lm(){return 0}class go extends ms{constructor(t,e,i){super(t.state,t.options),this.base=t,this.pos=e,this.context=i}get node(){return this.context.node}static create(t,e,i){return new go(t,e,i)}get textAfter(){return this.textAfterPos(this.pos)}get baseIndent(){return this.baseIndentFor(this.node)}baseIndentFor(t){let e=this.state.doc.lineAt(t.from);for(;;){let i=t.resolve(e.from);for(;i.parent&&i.parent.from==i.from;)i=i.parent;if(Rm(i,t))break;e=this.state.doc.lineAt(i.from)}return this.lineIndent(e.from)}continue(){return Of(this.context.next,this.base,this.pos)}}function Rm(s,t){for(let e=t;e;e=e.parent)if(s==e)return!0;return!1}function Im(s){let t=s.node,e=t.childAfter(t.from),i=t.lastChild;if(!e)return null;let n=s.options.simulateBreak,r=s.state.doc.lineAt(e.from),o=n==null||n<=r.from?r.to:Math.min(r.to,n);for(let l=e.to;;){let a=t.childAfter(l);if(!a||a==i)return null;if(!a.type.isSkipped){if(a.from>=o)return null;let h=/^ */.exec(r.text.slice(e.to-r.from))[0].length;return{from:e.from,to:e.to+h}}l=a.to}}function Wy({closing:s,align:t=!0,units:e=1}){return i=>Bf(i,t,e,s)}function Bf(s,t,e,i,n){let r=s.textAfter,o=r.match(/^\s*/)[0].length,l=i&&r.slice(o,o+i.length)==i||n==s.pos+o,a=t?Im(s):null;return a?l?s.column(a.from):s.column(a.to):s.baseIndent+(l?0:s.unit*e)}const zy=s=>s.baseIndent;function qy({except:s,units:t=1}={}){return e=>{let i=s&&s.test(e.textAfter);return e.baseIndent+(i?0:t*e.unit)}}const Nm=200;function _m(){return H.transactionFilter.of(s=>{if(!s.docChanged||!s.isUserEvent("input.type")&&!s.isUserEvent("input.complete"))return s;let t=s.startState.languageDataAt("indentOnInput",s.startState.selection.main.head);if(!t.length)return s;let e=s.newDoc,{head:i}=s.newSelection.main,n=e.lineAt(i);if(i>n.from+Nm)return s;let r=e.sliceString(n.from,i);if(!t.some(h=>h.test(r)))return s;let{state:o}=s,l=-1,a=[];for(let{head:h}of o.selection.ranges){let f=o.doc.lineAt(h);if(f.from==l)continue;l=f.from;let c=mo(o,f.from);if(c==null)continue;let u=/^\s*/.exec(f.text)[0],d=Ui(o,c);u!=d&&a.push({from:f.from,to:f.from+u.length,insert:d})}return a.length?[s,{changes:a,sequential:!0}]:s})}const Fm=B.define(),Vm=new _;function jy(s){let t=s.firstChild,e=s.lastChild;return t&&t.to<e.from?{from:t.to,to:e.type.isError?s.to:e.from}:null}function Hm(s,t,e){let i=dt(s);if(i.length<e)return null;let n=i.resolveStack(e,1),r=null;for(let o=n;o;o=o.next){let l=o.node;if(l.to<=e||l.from>e)continue;if(r&&l.from<t)break;let a=l.type.prop(Vm);if(a&&(l.to<i.length-50||i.length==s.doc.length||!Wm(l))){let h=a(l,s);h&&h.from<=e&&h.from>=t&&h.to>e&&(r=h)}}return r}function Wm(s){let t=s.lastChild;return t&&t.to==s.to&&t.type.isError}function rs(s,t,e){for(let i of s.facet(Fm)){let n=i(s,t,e);if(n)return n}return Hm(s,t,e)}function Ef(s,t){let e=t.mapPos(s.from,1),i=t.mapPos(s.to,-1);return e>=i?void 0:{from:e,to:i}}const gs=I.define({map:Ef}),an=I.define({map:Ef});function Pf(s){let t=[];for(let{head:e}of s.state.selection.ranges)t.some(i=>i.from<=e&&i.to>=e)||t.push(s.lineBlockAt(e));return t}const Je=Tt.define({create(){return N.none},update(s,t){s=s.map(t.changes);for(let e of t.effects)if(e.is(gs)&&!zm(s,e.value.from,e.value.to)){let{preparePlaceholder:i}=t.state.facet(If),n=i?N.replace({widget:new Jm(i(t.state,e.value))}):ra;s=s.update({add:[n.range(e.value.from,e.value.to)]})}else e.is(an)&&(s=s.update({filter:(i,n)=>e.value.from!=i||e.value.to!=n,filterFrom:e.value.from,filterTo:e.value.to}));if(t.selection){let e=!1,{head:i}=t.selection.main;s.between(i,i,(n,r)=>{n<i&&r>i&&(e=!0)}),e&&(s=s.update({filterFrom:i,filterTo:i,filter:(n,r)=>r<=i||n>=i}))}return s},provide:s=>E.decorations.from(s),toJSON(s,t){let e=[];return s.between(0,t.doc.length,(i,n)=>{e.push(i,n)}),e},fromJSON(s){if(!Array.isArray(s)||s.length%2)throw new RangeError("Invalid JSON for fold state");let t=[];for(let e=0;e<s.length;){let i=s[e++],n=s[e++];if(typeof i!="number"||typeof n!="number")throw new RangeError("Invalid JSON for fold state");t.push(ra.range(i,n))}return N.set(t,!0)}});function os(s,t,e){var i;let n=null;return(i=s.field(Je,!1))===null||i===void 0||i.between(t,e,(r,o)=>{(!n||n.from>r)&&(n={from:r,to:o})}),n}function zm(s,t,e){let i=!1;return s.between(t,t,(n,r)=>{n==t&&r==e&&(i=!0)}),i}function Lf(s,t){return s.field(Je,!1)?t:t.concat(I.appendConfig.of(Nf()))}const qm=s=>{for(let t of Pf(s)){let e=rs(s.state,t.from,t.to);if(e)return s.dispatch({effects:Lf(s.state,[gs.of(e),Rf(s,e)])}),!0}return!1},jm=s=>{if(!s.state.field(Je,!1))return!1;let t=[];for(let e of Pf(s)){let i=os(s.state,e.from,e.to);i&&t.push(an.of(i),Rf(s,i,!1))}return t.length&&s.dispatch({effects:t}),t.length>0};function Rf(s,t,e=!0){let i=s.state.doc.lineAt(t.from).number,n=s.state.doc.lineAt(t.to).number;return E.announce.of(`${s.state.phrase(e?"Folded lines":"Unfolded lines")} ${i} ${s.state.phrase("to")} ${n}.`)}const Km=s=>{let{state:t}=s,e=[];for(let i=0;i<t.doc.length;){let n=s.lineBlockAt(i),r=rs(t,n.from,n.to);r&&e.push(gs.of(r)),i=(r?s.lineBlockAt(r.to):n).to+1}return e.length&&s.dispatch({effects:Lf(s.state,e)}),!!e.length},Um=s=>{let t=s.state.field(Je,!1);if(!t||!t.size)return!1;let e=[];return t.between(0,s.state.doc.length,(i,n)=>{e.push(an.of({from:i,to:n}))}),s.dispatch({effects:e}),!0},Gm=[{key:"Ctrl-Shift-[",mac:"Cmd-Alt-[",run:qm},{key:"Ctrl-Shift-]",mac:"Cmd-Alt-]",run:jm},{key:"Ctrl-Alt-[",run:Km},{key:"Ctrl-Alt-]",run:Um}],Ym={placeholderDOM:null,preparePlaceholder:null,placeholderText:"…"},If=B.define({combine(s){return $e(s,Ym)}});function Nf(s){return[Je,Qm]}function _f(s,t){let{state:e}=s,i=e.facet(If),n=o=>{let l=s.lineBlockAt(s.posAtDOM(o.target)),a=os(s.state,l.from,l.to);a&&s.dispatch({effects:an.of(a)}),o.preventDefault()};if(i.placeholderDOM)return i.placeholderDOM(s,n,t);let r=document.createElement("span");return r.textContent=i.placeholderText,r.setAttribute("aria-label",e.phrase("folded code")),r.title=e.phrase("unfold"),r.className="cm-foldPlaceholder",r.onclick=n,r}const ra=N.replace({widget:new class extends ue{toDOM(s){return _f(s,null)}}});class Jm extends ue{constructor(t){super(),this.value=t}eq(t){return this.value==t.value}toDOM(t){return _f(t,this.value)}}const Xm={openText:"⌄",closedText:"›",markerDOM:null,domEventHandlers:{},foldingChanged:()=>!1};class Hs extends Ee{constructor(t,e){super(),this.config=t,this.open=e}eq(t){return this.config==t.config&&this.open==t.open}toDOM(t){if(this.config.markerDOM)return this.config.markerDOM(this.open);let e=document.createElement("span");return e.textContent=this.open?this.config.openText:this.config.closedText,e.title=t.state.phrase(this.open?"Fold line":"Unfold line"),e}}function $m(s={}){let t=Object.assign(Object.assign({},Xm),s),e=new Hs(t,!0),i=new Hs(t,!1),n=ut.fromClass(class{constructor(o){this.from=o.viewport.from,this.markers=this.buildMarkers(o)}update(o){(o.docChanged||o.viewportChanged||o.startState.facet(Le)!=o.state.facet(Le)||o.startState.field(Je,!1)!=o.state.field(Je,!1)||dt(o.startState)!=dt(o.state)||t.foldingChanged(o))&&(this.markers=this.buildMarkers(o.view))}buildMarkers(o){let l=new De;for(let a of o.viewportLineBlocks){let h=os(o.state,a.from,a.to)?i:rs(o.state,a.from,a.to)?e:null;h&&l.add(a.from,a.from,h)}return l.finish()}}),{domEventHandlers:r}=t;return[n,Yp({class:"cm-foldGutter",markers(o){var l;return((l=o.plugin(n))===null||l===void 0?void 0:l.markers)||W.empty},initialSpacer(){return new Hs(t,!1)},domEventHandlers:Object.assign(Object.assign({},r),{click:(o,l,a)=>{if(r.click&&r.click(o,l,a))return!0;let h=os(o.state,l.from,l.to);if(h)return o.dispatch({effects:an.of(h)}),!0;let f=rs(o.state,l.from,l.to);return f?(o.dispatch({effects:gs.of(f)}),!0):!1}})}),Nf()]}const Qm=E.baseTheme({".cm-foldPlaceholder":{backgroundColor:"#eee",border:"1px solid #ddd",color:"#888",borderRadius:".2em",margin:"0 1px",padding:"0 1px",cursor:"pointer"},".cm-foldGutter span":{padding:"0 1px",cursor:"pointer"}});class bi{constructor(t,e){this.specs=t;let i;function n(l){let a=Te.newName();return(i||(i=Object.create(null)))["."+a]=l,a}const r=typeof e.all=="string"?e.all:e.all?n(e.all):void 0,o=e.scope;this.scope=o instanceof Wt?l=>l.prop(We)==o.data:o?l=>l==o:void 0,this.style=Cf(t.map(l=>({tag:l.tag,class:l.class||n(Object.assign({},l,{tag:null}))})),{all:r}).style,this.module=i?new Te(i):null,this.themeType=e.themeType}static define(t,e){return new bi(t,e||{})}}const Wr=B.define(),Ff=B.define({combine(s){return s.length?[s[0]]:null}});function Ws(s){let t=s.facet(Wr);return t.length?t:s.facet(Ff)}function bo(s,t){let e=[tg],i;return s instanceof bi&&(s.module&&e.push(E.styleModule.of(s.module)),i=s.themeType),t!=null&&t.fallback?e.push(Ff.of(s)):i?e.push(Wr.computeN([E.darkTheme],n=>n.facet(E.darkTheme)==(i=="dark")?[s]:[])):e.push(Wr.of(s)),e}class Zm{constructor(t){this.markCache=Object.create(null),this.tree=dt(t.state),this.decorations=this.buildDeco(t,Ws(t.state)),this.decoratedTo=t.viewport.to}update(t){let e=dt(t.state),i=Ws(t.state),n=i!=Ws(t.startState),{viewport:r}=t.view,o=t.changes.mapPos(this.decoratedTo,1);e.length<r.to&&!n&&e.type==this.tree.type&&o>=r.to?(this.decorations=this.decorations.map(t.changes),this.decoratedTo=o):(e!=this.tree||t.viewportChanged||n)&&(this.tree=e,this.decorations=this.buildDeco(t.view,i),this.decoratedTo=r.to)}buildDeco(t,e){if(!e||!this.tree.length)return N.none;let i=new De;for(let{from:n,to:r}of t.visibleRanges)vm(this.tree,e,(o,l,a)=>{i.add(o,l,this.markCache[a]||(this.markCache[a]=N.mark({class:a})))},n,r);return i.finish()}}const tg=Xe.high(ut.fromClass(Zm,{decorations:s=>s.decorations})),eg=bi.define([{tag:b.meta,color:"#404740"},{tag:b.link,textDecoration:"underline"},{tag:b.heading,textDecoration:"underline",fontWeight:"bold"},{tag:b.emphasis,fontStyle:"italic"},{tag:b.strong,fontWeight:"bold"},{tag:b.strikethrough,textDecoration:"line-through"},{tag:b.keyword,color:"#708"},{tag:[b.atom,b.bool,b.url,b.contentSeparator,b.labelName],color:"#219"},{tag:[b.literal,b.inserted],color:"#164"},{tag:[b.string,b.deleted],color:"#a11"},{tag:[b.regexp,b.escape,b.special(b.string)],color:"#e40"},{tag:b.definition(b.variableName),color:"#00f"},{tag:b.local(b.variableName),color:"#30a"},{tag:[b.typeName,b.namespace],color:"#085"},{tag:b.className,color:"#167"},{tag:[b.special(b.variableName),b.macroName],color:"#256"},{tag:b.definition(b.propertyName),color:"#00c"},{tag:b.comment,color:"#940"},{tag:b.invalid,color:"#f00"}]),ig=1e4,ng="()[]{}",sg=new _;function zr(s,t,e){let i=s.prop(t<0?_.openedBy:_.closedBy);if(i)return i;if(s.name.length==1){let n=e.indexOf(s.name);if(n>-1&&n%2==(t<0?1:0))return[e[n+t]]}return null}function qr(s){let t=s.type.prop(sg);return t?t(s.node):s}function ei(s,t,e,i={}){let n=i.maxScanDistance||ig,r=i.brackets||ng,o=dt(s),l=o.resolveInner(t,e);for(let a=l;a;a=a.parent){let h=zr(a.type,e,r);if(h&&a.from<a.to){let f=qr(a);if(f&&(e>0?t>=f.from&&t<f.to:t>f.from&&t<=f.to))return rg(s,t,e,a,f,h,r)}}return og(s,t,e,o,l.type,n,r)}function rg(s,t,e,i,n,r,o){let l=i.parent,a={from:n.from,to:n.to},h=0,f=l==null?void 0:l.cursor();if(f&&(e<0?f.childBefore(i.from):f.childAfter(i.to)))do if(e<0?f.to<=i.from:f.from>=i.to){if(h==0&&r.indexOf(f.type.name)>-1&&f.from<f.to){let c=qr(f);return{start:a,end:c?{from:c.from,to:c.to}:void 0,matched:!0}}else if(zr(f.type,e,o))h++;else if(zr(f.type,-e,o)){if(h==0){let c=qr(f);return{start:a,end:c&&c.from<c.to?{from:c.from,to:c.to}:void 0,matched:!1}}h--}}while(e<0?f.prevSibling():f.nextSibling());return{start:a,matched:!1}}function og(s,t,e,i,n,r,o){let l=e<0?s.sliceDoc(t-1,t):s.sliceDoc(t,t+1),a=o.indexOf(l);if(a<0||a%2==0!=e>0)return null;let h={from:e<0?t-1:t,to:e>0?t+1:t},f=s.doc.iterRange(t,e>0?s.doc.length:0),c=0;for(let u=0;!f.next().done&&u<=r;){let d=f.value;e<0&&(u+=d.length);let p=t+u*e;for(let m=e>0?0:d.length-1,g=e>0?d.length:-1;m!=g;m+=e){let y=o.indexOf(d[m]);if(!(y<0||i.resolveInner(p+m,1).type!=n))if(y%2==0==e>0)c++;else{if(c==1)return{start:h,end:{from:p+m,to:p+m+1},matched:y>>1==a>>1};c--}}e>0&&(u+=d.length)}return f.done?{start:h,matched:!1}:null}function oa(s,t,e,i=0,n=0){t==null&&(t=s.search(/[^\s\u00a0]/),t==-1&&(t=s.length));let r=n;for(let o=i;o<t;o++)s.charCodeAt(o)==9?r+=e-r%e:r++;return r}class Vf{constructor(t,e,i,n){this.string=t,this.tabSize=e,this.indentUnit=i,this.overrideIndent=n,this.pos=0,this.start=0,this.lastColumnPos=0,this.lastColumnValue=0}eol(){return this.pos>=this.string.length}sol(){return this.pos==0}peek(){return this.string.charAt(this.pos)||void 0}next(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)}eat(t){let e=this.string.charAt(this.pos),i;if(typeof t=="string"?i=e==t:i=e&&(t instanceof RegExp?t.test(e):t(e)),i)return++this.pos,e}eatWhile(t){let e=this.pos;for(;this.eat(t););return this.pos>e}eatSpace(){let t=this.pos;for(;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>t}skipToEnd(){this.pos=this.string.length}skipTo(t){let e=this.string.indexOf(t,this.pos);if(e>-1)return this.pos=e,!0}backUp(t){this.pos-=t}column(){return this.lastColumnPos<this.start&&(this.lastColumnValue=oa(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue}indentation(){var t;return(t=this.overrideIndent)!==null&&t!==void 0?t:oa(this.string,null,this.tabSize)}match(t,e,i){if(typeof t=="string"){let n=o=>i?o.toLowerCase():o,r=this.string.substr(this.pos,t.length);return n(r)==n(t)?(e!==!1&&(this.pos+=t.length),!0):null}else{let n=this.string.slice(this.pos).match(t);return n&&n.index>0?null:(n&&e!==!1&&(this.pos+=n[0].length),n)}}current(){return this.string.slice(this.start,this.pos)}}function lg(s){return{name:s.name||"",token:s.token,blankLine:s.blankLine||(()=>{}),startState:s.startState||(()=>!0),copyState:s.copyState||ag,indent:s.indent||(()=>null),languageData:s.languageData||{},tokenTable:s.tokenTable||wo}}function ag(s){if(typeof s!="object")return s;let t={};for(let e in s){let i=s[e];t[e]=i instanceof Array?i.slice():i}return t}const la=new WeakMap;class _t extends Wt{constructor(t){let e=Af(t.languageData),i=lg(t),n,r=new class extends vf{createParse(o,l,a){return new fg(n,o,l,a)}};super(e,r,[],t.name),this.topNode=dg(e,this),n=this,this.streamParser=i,this.stateAfter=new _({perNode:!0}),this.tokenTable=t.tokenTable?new qf(i.tokenTable):ug}static define(t){return new _t(t)}getIndent(t){let e,{overrideIndentation:i}=t.options;i&&(e=la.get(t.state),e!=null&&e<t.pos-1e4&&(e=void 0));let n=yo(this,t.node.tree,t.node.from,t.node.from,e??t.pos),r,o;if(n?(o=n.state,r=n.pos+1):(o=this.streamParser.startState(t.unit),r=t.node.from),t.pos-r>1e4)return null;for(;r<t.pos;){let a=t.state.doc.lineAt(r),h=Math.min(t.pos,a.to);if(a.length){let f=i?i(a.from):-1,c=new Vf(a.text,t.state.tabSize,t.unit,f<0?void 0:f);for(;c.pos<h-a.from;)Wf(this.streamParser.token,c,o)}else this.streamParser.blankLine(o,t.unit);if(h==t.pos)break;r=a.to+1}let l=t.lineAt(t.pos);return i&&e==null&&la.set(t.state,l.from),this.streamParser.indent(o,/^\s*(.*)/.exec(l.text)[1],t)}get allowsNesting(){return!1}}function yo(s,t,e,i,n){let r=e>=i&&e+t.length<=n&&t.prop(s.stateAfter);if(r)return{state:s.streamParser.copyState(r),pos:e+t.length};for(let o=t.children.length-1;o>=0;o--){let l=t.children[o],a=e+t.positions[o],h=l instanceof G&&a<n&&yo(s,l,a,i,n);if(h)return h}return null}function Hf(s,t,e,i,n){if(n&&e<=0&&i>=t.length)return t;!n&&e==0&&t.type==s.topNode&&(n=!0);for(let r=t.children.length-1;r>=0;r--){let o=t.positions[r],l=t.children[r],a;if(o<i&&l instanceof G){if(!(a=Hf(s,l,e-o,i-o,n)))break;return n?new G(t.type,t.children.slice(0,r).concat(a),t.positions.slice(0,r+1),o+a.length):a}}return null}function hg(s,t,e,i,n){for(let r of t){let o=r.from+(r.openStart?25:0),l=r.to-(r.openEnd?25:0),a=o<=e&&l>e&&yo(s,r.tree,0-r.offset,e,l),h;if(a&&a.pos<=i&&(h=Hf(s,r.tree,e+r.offset,a.pos+r.offset,!1)))return{state:a.state,tree:h}}return{state:s.streamParser.startState(n?Ye(n):4),tree:G.empty}}class fg{constructor(t,e,i,n){this.lang=t,this.input=e,this.fragments=i,this.ranges=n,this.stoppedAt=null,this.chunks=[],this.chunkPos=[],this.chunk=[],this.chunkReused=void 0,this.rangeIndex=0,this.to=n[n.length-1].to;let r=di.get(),o=n[0].from,{state:l,tree:a}=hg(t,i,o,this.to,r==null?void 0:r.state);this.state=l,this.parsedPos=this.chunkStart=o+a.length;for(let h=0;h<a.children.length;h++)this.chunks.push(a.children[h]),this.chunkPos.push(a.positions[h]);r&&this.parsedPos<r.viewport.from-1e5&&n.some(h=>h.from<=r.viewport.from&&h.to>=r.viewport.from)&&(this.state=this.lang.streamParser.startState(Ye(r.state)),r.skipUntilInView(this.parsedPos,r.viewport.from),this.parsedPos=r.viewport.from),this.moveRangeIndex()}advance(){let t=di.get(),e=this.stoppedAt==null?this.to:Math.min(this.to,this.stoppedAt),i=Math.min(e,this.chunkStart+2048);for(t&&(i=Math.min(i,t.viewport.to));this.parsedPos<i;)this.parseLine(t);return this.chunkStart<this.parsedPos&&this.finishChunk(),this.parsedPos>=e?this.finish():t&&this.parsedPos>=t.viewport.to?(t.skipUntilInView(this.parsedPos,e),this.finish()):null}stopAt(t){this.stoppedAt=t}lineAfter(t){let e=this.input.chunk(t);if(this.input.lineChunks)e==`
`&&(e="");else{let i=e.indexOf(`
`);i>-1&&(e=e.slice(0,i))}return t+e.length<=this.to?e:e.slice(0,this.to-t)}nextLine(){let t=this.parsedPos,e=this.lineAfter(t),i=t+e.length;for(let n=this.rangeIndex;;){let r=this.ranges[n].to;if(r>=i||(e=e.slice(0,r-(i-e.length)),n++,n==this.ranges.length))break;let o=this.ranges[n].from,l=this.lineAfter(o);e+=l,i=o+l.length}return{line:e,end:i}}skipGapsTo(t,e,i){for(;;){let n=this.ranges[this.rangeIndex].to,r=t+e;if(i>0?n>r:n>=r)break;let o=this.ranges[++this.rangeIndex].from;e+=o-n}return e}moveRangeIndex(){for(;this.ranges[this.rangeIndex].to<this.parsedPos;)this.rangeIndex++}emitToken(t,e,i,n){let r=4;if(this.ranges.length>1){n=this.skipGapsTo(e,n,1),e+=n;let l=this.chunk.length;n=this.skipGapsTo(i,n,-1),i+=n,r+=this.chunk.length-l}let o=this.chunk.length-4;return r==4&&o>=0&&this.chunk[o]==t&&this.chunk[o+2]==e?this.chunk[o+2]=i:this.chunk.push(t,e,i,r),n}parseLine(t){let{line:e,end:i}=this.nextLine(),n=0,{streamParser:r}=this.lang,o=new Vf(e,t?t.state.tabSize:4,t?Ye(t.state):2);if(o.eol())r.blankLine(this.state,o.indentUnit);else for(;!o.eol();){let l=Wf(r.token,o,this.state);if(l&&(n=this.emitToken(this.lang.tokenTable.resolve(l),this.parsedPos+o.start,this.parsedPos+o.pos,n)),o.start>1e4)break}this.parsedPos=i,this.moveRangeIndex(),this.parsedPos<this.to&&this.parsedPos++}finishChunk(){let t=G.build({buffer:this.chunk,start:this.chunkStart,length:this.parsedPos-this.chunkStart,nodeSet:cg,topID:0,maxBufferLength:2048,reused:this.chunkReused});t=new G(t.type,t.children,t.positions,t.length,[[this.lang.stateAfter,this.lang.streamParser.copyState(this.state)]]),this.chunks.push(t),this.chunkPos.push(this.chunkStart-this.ranges[0].from),this.chunk=[],this.chunkReused=void 0,this.chunkStart=this.parsedPos}finish(){return new G(this.lang.topNode,this.chunks,this.chunkPos,this.parsedPos-this.ranges[0].from).balance()}}function Wf(s,t,e){t.start=t.pos;for(let i=0;i<10;i++){let n=s(t,e);if(t.pos>t.start)return n}throw new Error("Stream parser failed to advance stream.")}const wo=Object.create(null),Gi=[vt.none],cg=new fo(Gi),aa=[],ha=Object.create(null),zf=Object.create(null);for(let[s,t]of[["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","tagName"],["attribute","attributeName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])zf[s]=jf(wo,t);class qf{constructor(t){this.extra=t,this.table=Object.assign(Object.create(null),zf)}resolve(t){return t?this.table[t]||(this.table[t]=jf(this.extra,t)):0}}const ug=new qf(wo);function zs(s,t){aa.indexOf(s)>-1||(aa.push(s),console.warn(t))}function jf(s,t){let e=[];for(let l of t.split(" ")){let a=[];for(let h of l.split(".")){let f=s[h]||b[h];f?typeof f=="function"?a.length?a=a.map(f):zs(h,`Modifier ${h} used at start of tag`):a.length?zs(h,`Tag ${h} used as modifier`):a=Array.isArray(f)?f:[f]:zs(h,`Unknown highlighting tag ${h}`)}for(let h of a)e.push(h)}if(!e.length)return 0;let i=t.replace(/ /g,"_"),n=i+" "+e.map(l=>l.id),r=ha[n];if(r)return r.id;let o=ha[n]=vt.define({id:Gi.length,name:i,props:[xm({[i]:e})]});return Gi.push(o),o.id}function dg(s,t){let e=vt.define({id:Gi.length,name:"Document",props:[We.add(()=>s),Tf.add(()=>i=>t.getIndent(i))],top:!0});return Gi.push(e),e}J.RTL,J.LTR;const pg=s=>{let{state:t}=s,e=t.doc.lineAt(t.selection.main.from),i=ko(s.state,e.from);return i.line?mg(s):i.block?bg(s):!1};function xo(s,t){return({state:e,dispatch:i})=>{if(e.readOnly)return!1;let n=s(t,e);return n?(i(e.update(n)),!0):!1}}const mg=xo(xg,0),gg=xo(Kf,0),bg=xo((s,t)=>Kf(s,t,wg(t)),0);function ko(s,t){let e=s.languageDataAt("commentTokens",t);return e.length?e[0]:{}}const Ai=50;function yg(s,{open:t,close:e},i,n){let r=s.sliceDoc(i-Ai,i),o=s.sliceDoc(n,n+Ai),l=/\s*$/.exec(r)[0].length,a=/^\s*/.exec(o)[0].length,h=r.length-l;if(r.slice(h-t.length,h)==t&&o.slice(a,a+e.length)==e)return{open:{pos:i-l,margin:l&&1},close:{pos:n+a,margin:a&&1}};let f,c;n-i<=2*Ai?f=c=s.sliceDoc(i,n):(f=s.sliceDoc(i,i+Ai),c=s.sliceDoc(n-Ai,n));let u=/^\s*/.exec(f)[0].length,d=/\s*$/.exec(c)[0].length,p=c.length-d-e.length;return f.slice(u,u+t.length)==t&&c.slice(p,p+e.length)==e?{open:{pos:i+u+t.length,margin:/\s/.test(f.charAt(u+t.length))?1:0},close:{pos:n-d-e.length,margin:/\s/.test(c.charAt(p-1))?1:0}}:null}function wg(s){let t=[];for(let e of s.selection.ranges){let i=s.doc.lineAt(e.from),n=e.to<=i.to?i:s.doc.lineAt(e.to);n.from>i.from&&n.from==e.to&&(n=e.to==i.to+1?i:s.doc.lineAt(e.to-1));let r=t.length-1;r>=0&&t[r].to>i.from?t[r].to=n.to:t.push({from:i.from+/^\s*/.exec(i.text)[0].length,to:n.to})}return t}function Kf(s,t,e=t.selection.ranges){let i=e.map(r=>ko(t,r.from).block);if(!i.every(r=>r))return null;let n=e.map((r,o)=>yg(t,i[o],r.from,r.to));if(s!=2&&!n.every(r=>r))return{changes:t.changes(e.map((r,o)=>n[o]?[]:[{from:r.from,insert:i[o].open+" "},{from:r.to,insert:" "+i[o].close}]))};if(s!=1&&n.some(r=>r)){let r=[];for(let o=0,l;o<n.length;o++)if(l=n[o]){let a=i[o],{open:h,close:f}=l;r.push({from:h.pos-a.open.length,to:h.pos+h.margin},{from:f.pos-f.margin,to:f.pos+a.close.length})}return{changes:r}}return null}function xg(s,t,e=t.selection.ranges){let i=[],n=-1;for(let{from:r,to:o}of e){let l=i.length,a=1e9,h=ko(t,r).line;if(h){for(let f=r;f<=o;){let c=t.doc.lineAt(f);if(c.from>n&&(r==o||o>c.from)){n=c.from;let u=/^\s*/.exec(c.text)[0].length,d=u==c.length,p=c.text.slice(u,u+h.length)==h?u:-1;u<c.text.length&&u<a&&(a=u),i.push({line:c,comment:p,token:h,indent:u,empty:d,single:!1})}f=c.to+1}if(a<1e9)for(let f=l;f<i.length;f++)i[f].indent<i[f].line.text.length&&(i[f].indent=a);i.length==l+1&&(i[l].single=!0)}}if(s!=2&&i.some(r=>r.comment<0&&(!r.empty||r.single))){let r=[];for(let{line:l,token:a,indent:h,empty:f,single:c}of i)(c||!f)&&r.push({from:l.from+h,insert:a+" "});let o=t.changes(r);return{changes:o,selection:t.selection.map(o,1)}}else if(s!=1&&i.some(r=>r.comment>=0)){let r=[];for(let{line:o,comment:l,token:a}of i)if(l>=0){let h=o.from+l,f=h+a.length;o.text[f-o.from]==" "&&f++,r.push({from:h,to:f})}return{changes:r}}return null}const jr=we.define(),kg=we.define(),vg=B.define(),Uf=B.define({combine(s){return $e(s,{minDepth:100,newGroupDelay:500,joinToEvent:(t,e)=>e},{minDepth:Math.max,newGroupDelay:Math.min,joinToEvent:(t,e)=>(i,n)=>t(i,n)||e(i,n)})}}),Gf=Tt.define({create(){return he.empty},update(s,t){let e=t.state.facet(Uf),i=t.annotation(jr);if(i){let a=Dt.fromTransaction(t,i.selection),h=i.side,f=h==0?s.undone:s.done;return a?f=ls(f,f.length,e.minDepth,a):f=Xf(f,t.startState.selection),new he(h==0?i.rest:f,h==0?f:i.rest)}let n=t.annotation(kg);if((n=="full"||n=="before")&&(s=s.isolate()),t.annotation(tt.addToHistory)===!1)return t.changes.empty?s:s.addMapping(t.changes.desc);let r=Dt.fromTransaction(t),o=t.annotation(tt.time),l=t.annotation(tt.userEvent);return r?s=s.addChanges(r,o,l,e,t):t.selection&&(s=s.addSelection(t.startState.selection,o,l,e.newGroupDelay)),(n=="full"||n=="after")&&(s=s.isolate()),s},toJSON(s){return{done:s.done.map(t=>t.toJSON()),undone:s.undone.map(t=>t.toJSON())}},fromJSON(s){return new he(s.done.map(Dt.fromJSON),s.undone.map(Dt.fromJSON))}});function Sg(s={}){return[Gf,Uf.of(s),E.domEventHandlers({beforeinput(t,e){let i=t.inputType=="historyUndo"?Yf:t.inputType=="historyRedo"?Kr:null;return i?(t.preventDefault(),i(e)):!1}})]}function bs(s,t){return function({state:e,dispatch:i}){if(!t&&e.readOnly)return!1;let n=e.field(Gf,!1);if(!n)return!1;let r=n.pop(s,e,t);return r?(i(r),!0):!1}}const Yf=bs(0,!1),Kr=bs(1,!1),Cg=bs(0,!0),Ag=bs(1,!0);class Dt{constructor(t,e,i,n,r){this.changes=t,this.effects=e,this.mapped=i,this.startSelection=n,this.selectionsAfter=r}setSelAfter(t){return new Dt(this.changes,this.effects,this.mapped,this.startSelection,t)}toJSON(){var t,e,i;return{changes:(t=this.changes)===null||t===void 0?void 0:t.toJSON(),mapped:(e=this.mapped)===null||e===void 0?void 0:e.toJSON(),startSelection:(i=this.startSelection)===null||i===void 0?void 0:i.toJSON(),selectionsAfter:this.selectionsAfter.map(n=>n.toJSON())}}static fromJSON(t){return new Dt(t.changes&&it.fromJSON(t.changes),[],t.mapped&&fe.fromJSON(t.mapped),t.startSelection&&k.fromJSON(t.startSelection),t.selectionsAfter.map(k.fromJSON))}static fromTransaction(t,e){let i=zt;for(let n of t.startState.facet(vg)){let r=n(t);r.length&&(i=i.concat(r))}return!i.length&&t.changes.empty?null:new Dt(t.changes.invert(t.startState.doc),i,void 0,e||t.startState.selection,zt)}static selection(t){return new Dt(void 0,zt,void 0,void 0,t)}}function ls(s,t,e,i){let n=t+1>e+20?t-e-1:0,r=s.slice(n,t);return r.push(i),r}function Mg(s,t){let e=[],i=!1;return s.iterChangedRanges((n,r)=>e.push(n,r)),t.iterChangedRanges((n,r,o,l)=>{for(let a=0;a<e.length;){let h=e[a++],f=e[a++];l>=h&&o<=f&&(i=!0)}}),i}function Dg(s,t){return s.ranges.length==t.ranges.length&&s.ranges.filter((e,i)=>e.empty!=t.ranges[i].empty).length===0}function Jf(s,t){return s.length?t.length?s.concat(t):s:t}const zt=[],Tg=200;function Xf(s,t){if(s.length){let e=s[s.length-1],i=e.selectionsAfter.slice(Math.max(0,e.selectionsAfter.length-Tg));return i.length&&i[i.length-1].eq(t)?s:(i.push(t),ls(s,s.length-1,1e9,e.setSelAfter(i)))}else return[Dt.selection([t])]}function Og(s){let t=s[s.length-1],e=s.slice();return e[s.length-1]=t.setSelAfter(t.selectionsAfter.slice(0,t.selectionsAfter.length-1)),e}function qs(s,t){if(!s.length)return s;let e=s.length,i=zt;for(;e;){let n=Bg(s[e-1],t,i);if(n.changes&&!n.changes.empty||n.effects.length){let r=s.slice(0,e);return r[e-1]=n,r}else t=n.mapped,e--,i=n.selectionsAfter}return i.length?[Dt.selection(i)]:zt}function Bg(s,t,e){let i=Jf(s.selectionsAfter.length?s.selectionsAfter.map(l=>l.map(t)):zt,e);if(!s.changes)return Dt.selection(i);let n=s.changes.map(t),r=t.mapDesc(s.changes,!0),o=s.mapped?s.mapped.composeDesc(r):r;return new Dt(n,I.mapEffects(s.effects,t),o,s.startSelection.map(r),i)}const Eg=/^(input\.type|delete)($|\.)/;class he{constructor(t,e,i=0,n=void 0){this.done=t,this.undone=e,this.prevTime=i,this.prevUserEvent=n}isolate(){return this.prevTime?new he(this.done,this.undone):this}addChanges(t,e,i,n,r){let o=this.done,l=o[o.length-1];return l&&l.changes&&!l.changes.empty&&t.changes&&(!i||Eg.test(i))&&(!l.selectionsAfter.length&&e-this.prevTime<n.newGroupDelay&&n.joinToEvent(r,Mg(l.changes,t.changes))||i=="input.type.compose")?o=ls(o,o.length-1,n.minDepth,new Dt(t.changes.compose(l.changes),Jf(I.mapEffects(t.effects,l.changes),l.effects),l.mapped,l.startSelection,zt)):o=ls(o,o.length,n.minDepth,t),new he(o,zt,e,i)}addSelection(t,e,i,n){let r=this.done.length?this.done[this.done.length-1].selectionsAfter:zt;return r.length>0&&e-this.prevTime<n&&i==this.prevUserEvent&&i&&/^select($|\.)/.test(i)&&Dg(r[r.length-1],t)?this:new he(Xf(this.done,t),this.undone,e,i)}addMapping(t){return new he(qs(this.done,t),qs(this.undone,t),this.prevTime,this.prevUserEvent)}pop(t,e,i){let n=t==0?this.done:this.undone;if(n.length==0)return null;let r=n[n.length-1],o=r.selectionsAfter[0]||e.selection;if(i&&r.selectionsAfter.length)return e.update({selection:r.selectionsAfter[r.selectionsAfter.length-1],annotations:jr.of({side:t,rest:Og(n),selection:o}),userEvent:t==0?"select.undo":"select.redo",scrollIntoView:!0});if(r.changes){let l=n.length==1?zt:n.slice(0,n.length-1);return r.mapped&&(l=qs(l,r.mapped)),e.update({changes:r.changes,selection:r.startSelection,effects:r.effects,annotations:jr.of({side:t,rest:l,selection:o}),filter:!1,userEvent:t==0?"undo":"redo",scrollIntoView:!0})}else return null}}he.empty=new he(zt,zt);const Pg=[{key:"Mod-z",run:Yf,preventDefault:!0},{key:"Mod-y",mac:"Mod-Shift-z",run:Kr,preventDefault:!0},{linux:"Ctrl-Shift-z",run:Kr,preventDefault:!0},{key:"Mod-u",run:Cg,preventDefault:!0},{key:"Alt-u",mac:"Mod-Shift-u",run:Ag,preventDefault:!0}];function yi(s,t){return k.create(s.ranges.map(t),s.mainIndex)}function de(s,t){return s.update({selection:t,scrollIntoView:!0,userEvent:"select"})}function Qt({state:s,dispatch:t},e){let i=yi(s.selection,e);return i.eq(s.selection,!0)?!1:(t(de(s,i)),!0)}function ys(s,t){return k.cursor(t?s.to:s.from)}function $f(s,t){return Qt(s,e=>e.empty?s.moveByChar(e,t):ys(e,t))}function bt(s){return s.textDirectionAt(s.state.selection.main.head)==J.LTR}const Qf=s=>$f(s,!bt(s)),Zf=s=>$f(s,bt(s));function tc(s,t){return Qt(s,e=>e.empty?s.moveByGroup(e,t):ys(e,t))}const Lg=s=>tc(s,!bt(s)),Rg=s=>tc(s,bt(s));function Ig(s,t,e){if(t.type.prop(e))return!0;let i=t.to-t.from;return i&&(i>2||/[^\s,.;:]/.test(s.sliceDoc(t.from,t.to)))||t.firstChild}function ws(s,t,e){let i=dt(s).resolveInner(t.head),n=e?_.closedBy:_.openedBy;for(let a=t.head;;){let h=e?i.childAfter(a):i.childBefore(a);if(!h)break;Ig(s,h,n)?i=h:a=e?h.to:h.from}let r=i.type.prop(n),o,l;return r&&(o=e?ei(s,i.from,1):ei(s,i.to,-1))&&o.matched?l=e?o.end.to:o.end.from:l=e?i.to:i.from,k.cursor(l,e?-1:1)}const Ng=s=>Qt(s,t=>ws(s.state,t,!bt(s))),_g=s=>Qt(s,t=>ws(s.state,t,bt(s)));function ec(s,t){return Qt(s,e=>{if(!e.empty)return ys(e,t);let i=s.moveVertically(e,t);return i.head!=e.head?i:s.moveToLineBoundary(e,t)})}const ic=s=>ec(s,!1),nc=s=>ec(s,!0);function sc(s){let t=s.scrollDOM.clientHeight<s.scrollDOM.scrollHeight-2,e=0,i=0,n;if(t){for(let r of s.state.facet(E.scrollMargins)){let o=r(s);o!=null&&o.top&&(e=Math.max(o==null?void 0:o.top,e)),o!=null&&o.bottom&&(i=Math.max(o==null?void 0:o.bottom,i))}n=s.scrollDOM.clientHeight-e-i}else n=(s.dom.ownerDocument.defaultView||window).innerHeight;return{marginTop:e,marginBottom:i,selfScroll:t,height:Math.max(s.defaultLineHeight,n-5)}}function rc(s,t){let e=sc(s),{state:i}=s,n=yi(i.selection,o=>o.empty?s.moveVertically(o,t,e.height):ys(o,t));if(n.eq(i.selection))return!1;let r;if(e.selfScroll){let o=s.coordsAtPos(i.selection.main.head),l=s.scrollDOM.getBoundingClientRect(),a=l.top+e.marginTop,h=l.bottom-e.marginBottom;o&&o.top>a&&o.bottom<h&&(r=E.scrollIntoView(n.main.head,{y:"start",yMargin:o.top-a}))}return s.dispatch(de(i,n),{effects:r}),!0}const fa=s=>rc(s,!1),Ur=s=>rc(s,!0);function Re(s,t,e){let i=s.lineBlockAt(t.head),n=s.moveToLineBoundary(t,e);if(n.head==t.head&&n.head!=(e?i.to:i.from)&&(n=s.moveToLineBoundary(t,e,!1)),!e&&n.head==i.from&&i.length){let r=/^\s*/.exec(s.state.sliceDoc(i.from,Math.min(i.from+100,i.to)))[0].length;r&&t.head!=i.from+r&&(n=k.cursor(i.from+r))}return n}const Fg=s=>Qt(s,t=>Re(s,t,!0)),Vg=s=>Qt(s,t=>Re(s,t,!1)),Hg=s=>Qt(s,t=>Re(s,t,!bt(s))),Wg=s=>Qt(s,t=>Re(s,t,bt(s))),zg=s=>Qt(s,t=>k.cursor(s.lineBlockAt(t.head).from,1)),qg=s=>Qt(s,t=>k.cursor(s.lineBlockAt(t.head).to,-1));function jg(s,t,e){let i=!1,n=yi(s.selection,r=>{let o=ei(s,r.head,-1)||ei(s,r.head,1)||r.head>0&&ei(s,r.head-1,1)||r.head<s.doc.length&&ei(s,r.head+1,-1);if(!o||!o.end)return r;i=!0;let l=o.start.from==r.head?o.end.to:o.end.from;return k.cursor(l)});return i?(t(de(s,n)),!0):!1}const Kg=({state:s,dispatch:t})=>jg(s,t);function Ut(s,t){let e=yi(s.state.selection,i=>{let n=t(i);return k.range(i.anchor,n.head,n.goalColumn,n.bidiLevel||void 0)});return e.eq(s.state.selection)?!1:(s.dispatch(de(s.state,e)),!0)}function oc(s,t){return Ut(s,e=>s.moveByChar(e,t))}const lc=s=>oc(s,!bt(s)),ac=s=>oc(s,bt(s));function hc(s,t){return Ut(s,e=>s.moveByGroup(e,t))}const Ug=s=>hc(s,!bt(s)),Gg=s=>hc(s,bt(s)),Yg=s=>Ut(s,t=>ws(s.state,t,!bt(s))),Jg=s=>Ut(s,t=>ws(s.state,t,bt(s)));function fc(s,t){return Ut(s,e=>s.moveVertically(e,t))}const cc=s=>fc(s,!1),uc=s=>fc(s,!0);function dc(s,t){return Ut(s,e=>s.moveVertically(e,t,sc(s).height))}const ca=s=>dc(s,!1),ua=s=>dc(s,!0),Xg=s=>Ut(s,t=>Re(s,t,!0)),$g=s=>Ut(s,t=>Re(s,t,!1)),Qg=s=>Ut(s,t=>Re(s,t,!bt(s))),Zg=s=>Ut(s,t=>Re(s,t,bt(s))),t0=s=>Ut(s,t=>k.cursor(s.lineBlockAt(t.head).from)),e0=s=>Ut(s,t=>k.cursor(s.lineBlockAt(t.head).to)),da=({state:s,dispatch:t})=>(t(de(s,{anchor:0})),!0),pa=({state:s,dispatch:t})=>(t(de(s,{anchor:s.doc.length})),!0),ma=({state:s,dispatch:t})=>(t(de(s,{anchor:s.selection.main.anchor,head:0})),!0),ga=({state:s,dispatch:t})=>(t(de(s,{anchor:s.selection.main.anchor,head:s.doc.length})),!0),i0=({state:s,dispatch:t})=>(t(s.update({selection:{anchor:0,head:s.doc.length},userEvent:"select"})),!0),n0=({state:s,dispatch:t})=>{let e=xs(s).map(({from:i,to:n})=>k.range(i,Math.min(n+1,s.doc.length)));return t(s.update({selection:k.create(e),userEvent:"select"})),!0},s0=({state:s,dispatch:t})=>{let e=yi(s.selection,i=>{let n=dt(s),r=n.resolveStack(i.from,1);if(i.empty){let o=n.resolveStack(i.from,-1);o.node.from>=r.node.from&&o.node.to<=r.node.to&&(r=o)}for(let o=r;o;o=o.next){let{node:l}=o;if((l.from<i.from&&l.to>=i.to||l.to>i.to&&l.from<=i.from)&&o.next)return k.range(l.to,l.from)}return i});return e.eq(s.selection)?!1:(t(de(s,e)),!0)},r0=({state:s,dispatch:t})=>{let e=s.selection,i=null;return e.ranges.length>1?i=k.create([e.main]):e.main.empty||(i=k.create([k.cursor(e.main.head)])),i?(t(de(s,i)),!0):!1};function hn(s,t){if(s.state.readOnly)return!1;let e="delete.selection",{state:i}=s,n=i.changeByRange(r=>{let{from:o,to:l}=r;if(o==l){let a=t(r);a<o?(e="delete.backward",a=Bn(s,a,!1)):a>o&&(e="delete.forward",a=Bn(s,a,!0)),o=Math.min(o,a),l=Math.max(l,a)}else o=Bn(s,o,!1),l=Bn(s,l,!0);return o==l?{range:r}:{changes:{from:o,to:l},range:k.cursor(o,o<r.head?-1:1)}});return n.changes.empty?!1:(s.dispatch(i.update(n,{scrollIntoView:!0,userEvent:e,effects:e=="delete.selection"?E.announce.of(i.phrase("Selection deleted")):void 0})),!0)}function Bn(s,t,e){if(s instanceof E)for(let i of s.state.facet(E.atomicRanges).map(n=>n(s)))i.between(t,t,(n,r)=>{n<t&&r>t&&(t=e?r:n)});return t}const pc=(s,t,e)=>hn(s,i=>{let n=i.from,{state:r}=s,o=r.doc.lineAt(n),l,a;if(e&&!t&&n>o.from&&n<o.from+200&&!/[^ \t]/.test(l=o.text.slice(0,n-o.from))){if(l[l.length-1]=="	")return n-1;let h=gi(l,r.tabSize),f=h%Ye(r)||Ye(r);for(let c=0;c<f&&l[l.length-1-c]==" ";c++)n--;a=n}else a=wt(o.text,n-o.from,t,t)+o.from,a==n&&o.number!=(t?r.doc.lines:1)?a+=t?1:-1:!t&&/[\ufe00-\ufe0f]/.test(o.text.slice(a-o.from,n-o.from))&&(a=wt(o.text,a-o.from,!1,!1)+o.from);return a}),Gr=s=>pc(s,!1,!0),mc=s=>pc(s,!0,!1),gc=(s,t)=>hn(s,e=>{let i=e.head,{state:n}=s,r=n.doc.lineAt(i),o=n.charCategorizer(i);for(let l=null;;){if(i==(t?r.to:r.from)){i==e.head&&r.number!=(t?n.doc.lines:1)&&(i+=t?1:-1);break}let a=wt(r.text,i-r.from,t)+r.from,h=r.text.slice(Math.min(i,a)-r.from,Math.max(i,a)-r.from),f=o(h);if(l!=null&&f!=l)break;(h!=" "||i!=e.head)&&(l=f),i=a}return i}),bc=s=>gc(s,!1),o0=s=>gc(s,!0),l0=s=>hn(s,t=>{let e=s.lineBlockAt(t.head).to;return t.head<e?e:Math.min(s.state.doc.length,t.head+1)}),a0=s=>hn(s,t=>{let e=s.moveToLineBoundary(t,!1).head;return t.head>e?e:Math.max(0,t.head-1)}),h0=s=>hn(s,t=>{let e=s.moveToLineBoundary(t,!0).head;return t.head<e?e:Math.min(s.state.doc.length,t.head+1)}),f0=({state:s,dispatch:t})=>{if(s.readOnly)return!1;let e=s.changeByRange(i=>({changes:{from:i.from,to:i.to,insert:V.of(["",""])},range:k.cursor(i.from)}));return t(s.update(e,{scrollIntoView:!0,userEvent:"input"})),!0},c0=({state:s,dispatch:t})=>{if(s.readOnly)return!1;let e=s.changeByRange(i=>{if(!i.empty||i.from==0||i.from==s.doc.length)return{range:i};let n=i.from,r=s.doc.lineAt(n),o=n==r.from?n-1:wt(r.text,n-r.from,!1)+r.from,l=n==r.to?n+1:wt(r.text,n-r.from,!0)+r.from;return{changes:{from:o,to:l,insert:s.doc.slice(n,l).append(s.doc.slice(o,n))},range:k.cursor(l)}});return e.changes.empty?!1:(t(s.update(e,{scrollIntoView:!0,userEvent:"move.character"})),!0)};function xs(s){let t=[],e=-1;for(let i of s.selection.ranges){let n=s.doc.lineAt(i.from),r=s.doc.lineAt(i.to);if(!i.empty&&i.to==r.from&&(r=s.doc.lineAt(i.to-1)),e>=n.number){let o=t[t.length-1];o.to=r.to,o.ranges.push(i)}else t.push({from:n.from,to:r.to,ranges:[i]});e=r.number+1}return t}function yc(s,t,e){if(s.readOnly)return!1;let i=[],n=[];for(let r of xs(s)){if(e?r.to==s.doc.length:r.from==0)continue;let o=s.doc.lineAt(e?r.to+1:r.from-1),l=o.length+1;if(e){i.push({from:r.to,to:o.to},{from:r.from,insert:o.text+s.lineBreak});for(let a of r.ranges)n.push(k.range(Math.min(s.doc.length,a.anchor+l),Math.min(s.doc.length,a.head+l)))}else{i.push({from:o.from,to:r.from},{from:r.to,insert:s.lineBreak+o.text});for(let a of r.ranges)n.push(k.range(a.anchor-l,a.head-l))}}return i.length?(t(s.update({changes:i,scrollIntoView:!0,selection:k.create(n,s.selection.mainIndex),userEvent:"move.line"})),!0):!1}const u0=({state:s,dispatch:t})=>yc(s,t,!1),d0=({state:s,dispatch:t})=>yc(s,t,!0);function wc(s,t,e){if(s.readOnly)return!1;let i=[];for(let n of xs(s))e?i.push({from:n.from,insert:s.doc.slice(n.from,n.to)+s.lineBreak}):i.push({from:n.to,insert:s.lineBreak+s.doc.slice(n.from,n.to)});return t(s.update({changes:i,scrollIntoView:!0,userEvent:"input.copyline"})),!0}const p0=({state:s,dispatch:t})=>wc(s,t,!1),m0=({state:s,dispatch:t})=>wc(s,t,!0),g0=s=>{if(s.state.readOnly)return!1;let{state:t}=s,e=t.changes(xs(t).map(({from:n,to:r})=>(n>0?n--:r<t.doc.length&&r++,{from:n,to:r}))),i=yi(t.selection,n=>{let r;if(s.lineWrapping){let o=s.lineBlockAt(n.head),l=s.coordsAtPos(n.head,n.assoc||1);l&&(r=o.bottom+s.documentTop-l.bottom+s.defaultLineHeight/2)}return s.moveVertically(n,!0,r)}).map(e);return s.dispatch({changes:e,selection:i,scrollIntoView:!0,userEvent:"delete.line"}),!0};function b0(s,t){if(/\(\)|\[\]|\{\}/.test(s.sliceDoc(t-1,t+1)))return{from:t,to:t};let e=dt(s).resolveInner(t),i=e.childBefore(t),n=e.childAfter(t),r;return i&&n&&i.to<=t&&n.from>=t&&(r=i.type.prop(_.closedBy))&&r.indexOf(n.name)>-1&&s.doc.lineAt(i.to).from==s.doc.lineAt(n.from).from&&!/\S/.test(s.sliceDoc(i.to,n.from))?{from:i.to,to:n.from}:null}const ba=xc(!1),y0=xc(!0);function xc(s){return({state:t,dispatch:e})=>{if(t.readOnly)return!1;let i=t.changeByRange(n=>{let{from:r,to:o}=n,l=t.doc.lineAt(r),a=!s&&r==o&&b0(t,r);s&&(r=o=(o<=l.to?l:t.doc.lineAt(o)).to);let h=new ms(t,{simulateBreak:r,simulateDoubleBreak:!!a}),f=mo(h,r);for(f==null&&(f=gi(/^\s*/.exec(t.doc.lineAt(r).text)[0],t.tabSize));o<l.to&&/\s/.test(l.text[o-l.from]);)o++;a?{from:r,to:o}=a:r>l.from&&r<l.from+100&&!/\S/.test(l.text.slice(0,r))&&(r=l.from);let c=["",Ui(t,f)];return a&&c.push(Ui(t,h.lineIndent(l.from,-1))),{changes:{from:r,to:o,insert:V.of(c)},range:k.cursor(r+1+c[1].length)}});return e(t.update(i,{scrollIntoView:!0,userEvent:"input"})),!0}}function vo(s,t){let e=-1;return s.changeByRange(i=>{let n=[];for(let o=i.from;o<=i.to;){let l=s.doc.lineAt(o);l.number>e&&(i.empty||i.to>l.from)&&(t(l,n,i),e=l.number),o=l.to+1}let r=s.changes(n);return{changes:n,range:k.range(r.mapPos(i.anchor,1),r.mapPos(i.head,1))}})}const w0=({state:s,dispatch:t})=>{if(s.readOnly)return!1;let e=Object.create(null),i=new ms(s,{overrideIndentation:r=>{let o=e[r];return o??-1}}),n=vo(s,(r,o,l)=>{let a=mo(i,r.from);if(a==null)return;/\S/.test(r.text)||(a=0);let h=/^\s*/.exec(r.text)[0],f=Ui(s,a);(h!=f||l.from<r.from+h.length)&&(e[r.from]=a,o.push({from:r.from,to:r.from+h.length,insert:f}))});return n.changes.empty||t(s.update(n,{userEvent:"indent"})),!0},kc=({state:s,dispatch:t})=>s.readOnly?!1:(t(s.update(vo(s,(e,i)=>{i.push({from:e.from,insert:s.facet(ps)})}),{userEvent:"input.indent"})),!0),vc=({state:s,dispatch:t})=>s.readOnly?!1:(t(s.update(vo(s,(e,i)=>{let n=/^\s*/.exec(e.text)[0];if(!n)return;let r=gi(n,s.tabSize),o=0,l=Ui(s,Math.max(0,r-Ye(s)));for(;o<n.length&&o<l.length&&n.charCodeAt(o)==l.charCodeAt(o);)o++;i.push({from:e.from+o,to:e.from+n.length,insert:l.slice(o)})}),{userEvent:"delete.dedent"})),!0),x0=s=>(s.setTabFocusMode(),!0),k0=[{key:"Ctrl-b",run:Qf,shift:lc,preventDefault:!0},{key:"Ctrl-f",run:Zf,shift:ac},{key:"Ctrl-p",run:ic,shift:cc},{key:"Ctrl-n",run:nc,shift:uc},{key:"Ctrl-a",run:zg,shift:t0},{key:"Ctrl-e",run:qg,shift:e0},{key:"Ctrl-d",run:mc},{key:"Ctrl-h",run:Gr},{key:"Ctrl-k",run:l0},{key:"Ctrl-Alt-h",run:bc},{key:"Ctrl-o",run:f0},{key:"Ctrl-t",run:c0},{key:"Ctrl-v",run:Ur}],v0=[{key:"ArrowLeft",run:Qf,shift:lc,preventDefault:!0},{key:"Mod-ArrowLeft",mac:"Alt-ArrowLeft",run:Lg,shift:Ug,preventDefault:!0},{mac:"Cmd-ArrowLeft",run:Hg,shift:Qg,preventDefault:!0},{key:"ArrowRight",run:Zf,shift:ac,preventDefault:!0},{key:"Mod-ArrowRight",mac:"Alt-ArrowRight",run:Rg,shift:Gg,preventDefault:!0},{mac:"Cmd-ArrowRight",run:Wg,shift:Zg,preventDefault:!0},{key:"ArrowUp",run:ic,shift:cc,preventDefault:!0},{mac:"Cmd-ArrowUp",run:da,shift:ma},{mac:"Ctrl-ArrowUp",run:fa,shift:ca},{key:"ArrowDown",run:nc,shift:uc,preventDefault:!0},{mac:"Cmd-ArrowDown",run:pa,shift:ga},{mac:"Ctrl-ArrowDown",run:Ur,shift:ua},{key:"PageUp",run:fa,shift:ca},{key:"PageDown",run:Ur,shift:ua},{key:"Home",run:Vg,shift:$g,preventDefault:!0},{key:"Mod-Home",run:da,shift:ma},{key:"End",run:Fg,shift:Xg,preventDefault:!0},{key:"Mod-End",run:pa,shift:ga},{key:"Enter",run:ba,shift:ba},{key:"Mod-a",run:i0},{key:"Backspace",run:Gr,shift:Gr},{key:"Delete",run:mc},{key:"Mod-Backspace",mac:"Alt-Backspace",run:bc},{key:"Mod-Delete",mac:"Alt-Delete",run:o0},{mac:"Mod-Backspace",run:a0},{mac:"Mod-Delete",run:h0}].concat(k0.map(s=>({mac:s.key,run:s.run,shift:s.shift}))),S0=[{key:"Alt-ArrowLeft",mac:"Ctrl-ArrowLeft",run:Ng,shift:Yg},{key:"Alt-ArrowRight",mac:"Ctrl-ArrowRight",run:_g,shift:Jg},{key:"Alt-ArrowUp",run:u0},{key:"Shift-Alt-ArrowUp",run:p0},{key:"Alt-ArrowDown",run:d0},{key:"Shift-Alt-ArrowDown",run:m0},{key:"Escape",run:r0},{key:"Mod-Enter",run:y0},{key:"Alt-l",mac:"Ctrl-l",run:n0},{key:"Mod-i",run:s0,preventDefault:!0},{key:"Mod-[",run:vc},{key:"Mod-]",run:kc},{key:"Mod-Alt-\\",run:w0},{key:"Shift-Mod-k",run:g0},{key:"Shift-Mod-\\",run:Kg},{key:"Mod-/",run:pg},{key:"Alt-A",run:gg},{key:"Ctrl-m",mac:"Shift-Alt-m",run:x0}].concat(v0),C0={key:"Tab",run:kc,shift:vc};class Sc{constructor(t,e,i,n){this.state=t,this.pos=e,this.explicit=i,this.view=n,this.abortListeners=[],this.abortOnDocChange=!1}tokenBefore(t){let e=dt(this.state).resolveInner(this.pos,-1);for(;e&&t.indexOf(e.name)<0;)e=e.parent;return e?{from:e.from,to:this.pos,text:this.state.sliceDoc(e.from,this.pos),type:e.type}:null}matchBefore(t){let e=this.state.doc.lineAt(this.pos),i=Math.max(e.from,this.pos-250),n=e.text.slice(i-e.from,this.pos-e.from),r=n.search(Cc(t,!1));return r<0?null:{from:i+r,to:this.pos,text:n.slice(r)}}get aborted(){return this.abortListeners==null}addEventListener(t,e,i){t=="abort"&&this.abortListeners&&(this.abortListeners.push(e),i&&i.onDocChange&&(this.abortOnDocChange=!0))}}function ya(s){let t=Object.keys(s).join(""),e=/\w/.test(t);return e&&(t=t.replace(/\w/g,"")),`[${e?"\\w":""}${t.replace(/[^\w\s]/g,"\\$&")}]`}function A0(s){let t=Object.create(null),e=Object.create(null);for(let{label:n}of s){t[n[0]]=!0;for(let r=1;r<n.length;r++)e[n[r]]=!0}let i=ya(t)+ya(e)+"*$";return[new RegExp("^"+i),new RegExp(i)]}function M0(s){let t=s.map(n=>typeof n=="string"?{label:n}:n),[e,i]=t.every(n=>/^\w+$/.test(n.label))?[/\w*$/,/\w+$/]:A0(t);return n=>{let r=n.matchBefore(i);return r||n.explicit?{from:r?r.from:n.pos,options:t,validFor:e}:null}}function Ky(s,t){return e=>{for(let i=dt(e.state).resolveInner(e.pos,-1);i;i=i.parent){if(s.indexOf(i.name)>-1)return null;if(i.type.isTop)break}return t(e)}}class wa{constructor(t,e,i,n){this.completion=t,this.source=e,this.match=i,this.score=n}}function qe(s){return s.selection.main.from}function Cc(s,t){var e;let{source:i}=s,n=t&&i[0]!="^",r=i[i.length-1]!="$";return!n&&!r?s:new RegExp(`${n?"^":""}(?:${i})${r?"$":""}`,(e=s.flags)!==null&&e!==void 0?e:s.ignoreCase?"i":"")}const So=we.define();function D0(s,t,e,i){let{main:n}=s.selection,r=e-n.from,o=i-n.from;return Object.assign(Object.assign({},s.changeByRange(l=>{if(l!=n&&e!=i&&s.sliceDoc(l.from+r,l.from+o)!=s.sliceDoc(e,i))return{range:l};let a=s.toText(t);return{changes:{from:l.from+r,to:i==n.from?l.to:l.from+o,insert:a},range:k.cursor(l.from+r+a.length)}})),{scrollIntoView:!0,userEvent:"input.complete"})}const xa=new WeakMap;function T0(s){if(!Array.isArray(s))return s;let t=xa.get(s);return t||xa.set(s,t=M0(s)),t}const as=I.define(),Yi=I.define();class O0{constructor(t){this.pattern=t,this.chars=[],this.folded=[],this.any=[],this.precise=[],this.byWord=[],this.score=0,this.matched=[];for(let e=0;e<t.length;){let i=Bt(t,e),n=pe(i);this.chars.push(i);let r=t.slice(e,e+n),o=r.toUpperCase();this.folded.push(Bt(o==r?r.toLowerCase():o,0)),e+=n}this.astral=t.length!=this.chars.length}ret(t,e){return this.score=t,this.matched=e,this}match(t){if(this.pattern.length==0)return this.ret(-100,[]);if(t.length<this.pattern.length)return null;let{chars:e,folded:i,any:n,precise:r,byWord:o}=this;if(e.length==1){let w=Bt(t,0),S=pe(w),v=S==t.length?0:-100;if(w!=e[0])if(w==i[0])v+=-200;else return null;return this.ret(v,[0,S])}let l=t.indexOf(this.pattern);if(l==0)return this.ret(t.length==this.pattern.length?0:-100,[0,this.pattern.length]);let a=e.length,h=0;if(l<0){for(let w=0,S=Math.min(t.length,200);w<S&&h<a;){let v=Bt(t,w);(v==e[h]||v==i[h])&&(n[h++]=w),w+=pe(v)}if(h<a)return null}let f=0,c=0,u=!1,d=0,p=-1,m=-1,g=/[a-z]/.test(t),y=!0;for(let w=0,S=Math.min(t.length,200),v=0;w<S&&c<a;){let x=Bt(t,w);l<0&&(f<a&&x==e[f]&&(r[f++]=w),d<a&&(x==e[d]||x==i[d]?(d==0&&(p=w),m=w+1,d++):d=0));let C,A=x<255?x>=48&&x<=57||x>=97&&x<=122?2:x>=65&&x<=90?1:0:(C=th(x))!=C.toLowerCase()?1:C!=C.toUpperCase()?2:0;(!w||A==1&&g||v==0&&A!=0)&&(e[c]==x||i[c]==x&&(u=!0)?o[c++]=w:o.length&&(y=!1)),v=A,w+=pe(x)}return c==a&&o[0]==0&&y?this.result(-100+(u?-200:0),o,t):d==a&&p==0?this.ret(-200-t.length+(m==t.length?0:-100),[0,m]):l>-1?this.ret(-700-t.length,[l,l+this.pattern.length]):d==a?this.ret(-900-t.length,[p,m]):c==a?this.result(-100+(u?-200:0)+-700+(y?0:-1100),o,t):e.length==2?null:this.result((n[0]?-700:0)+-200+-1100,n,t)}result(t,e,i){let n=[],r=0;for(let o of e){let l=o+(this.astral?pe(Bt(i,o)):1);r&&n[r-1]==o?n[r-1]=l:(n[r++]=o,n[r++]=l)}return this.ret(t-i.length,n)}}class B0{constructor(t){this.pattern=t,this.matched=[],this.score=0,this.folded=t.toLowerCase()}match(t){if(t.length<this.pattern.length)return null;let e=t.slice(0,this.pattern.length),i=e==this.pattern?0:e.toLowerCase()==this.folded?-200:null;return i==null?null:(this.matched=[0,e.length],this.score=i+(t.length==this.pattern.length?0:-100),this)}}const rt=B.define({combine(s){return $e(s,{activateOnTyping:!0,activateOnCompletion:()=>!1,activateOnTypingDelay:100,selectOnOpen:!0,override:null,closeOnBlur:!0,maxRenderedOptions:100,defaultKeymap:!0,tooltipClass:()=>"",optionClass:()=>"",aboveCursor:!1,icons:!0,addToOptions:[],positionInfo:E0,filterStrict:!1,compareCompletions:(t,e)=>t.label.localeCompare(e.label),interactionDelay:75,updateSyncTime:100},{defaultKeymap:(t,e)=>t&&e,closeOnBlur:(t,e)=>t&&e,icons:(t,e)=>t&&e,tooltipClass:(t,e)=>i=>ka(t(i),e(i)),optionClass:(t,e)=>i=>ka(t(i),e(i)),addToOptions:(t,e)=>t.concat(e),filterStrict:(t,e)=>t||e})}});function ka(s,t){return s?t?s+" "+t:s:t}function E0(s,t,e,i,n,r){let o=s.textDirection==J.RTL,l=o,a=!1,h="top",f,c,u=t.left-n.left,d=n.right-t.right,p=i.right-i.left,m=i.bottom-i.top;if(l&&u<Math.min(p,d)?l=!1:!l&&d<Math.min(p,u)&&(l=!0),p<=(l?u:d))f=Math.max(n.top,Math.min(e.top,n.bottom-m))-t.top,c=Math.min(400,l?u:d);else{a=!0,c=Math.min(400,(o?t.right:n.right-t.left)-30);let w=n.bottom-t.bottom;w>=m||w>t.top?f=e.bottom-t.top:(h="bottom",f=t.bottom-e.top)}let g=(t.bottom-t.top)/r.offsetHeight,y=(t.right-t.left)/r.offsetWidth;return{style:`${h}: ${f/g}px; max-width: ${c/y}px`,class:"cm-completionInfo-"+(a?o?"left-narrow":"right-narrow":l?"left":"right")}}function P0(s){let t=s.addToOptions.slice();return s.icons&&t.push({render(e){let i=document.createElement("div");return i.classList.add("cm-completionIcon"),e.type&&i.classList.add(...e.type.split(/\s+/g).map(n=>"cm-completionIcon-"+n)),i.setAttribute("aria-hidden","true"),i},position:20}),t.push({render(e,i,n,r){let o=document.createElement("span");o.className="cm-completionLabel";let l=e.displayLabel||e.label,a=0;for(let h=0;h<r.length;){let f=r[h++],c=r[h++];f>a&&o.appendChild(document.createTextNode(l.slice(a,f)));let u=o.appendChild(document.createElement("span"));u.appendChild(document.createTextNode(l.slice(f,c))),u.className="cm-completionMatchedText",a=c}return a<l.length&&o.appendChild(document.createTextNode(l.slice(a))),o},position:50},{render(e){if(!e.detail)return null;let i=document.createElement("span");return i.className="cm-completionDetail",i.textContent=e.detail,i},position:80}),t.sort((e,i)=>e.position-i.position).map(e=>e.render)}function js(s,t,e){if(s<=e)return{from:0,to:s};if(t<0&&(t=0),t<=s>>1){let n=Math.floor(t/e);return{from:n*e,to:(n+1)*e}}let i=Math.floor((s-t)/e);return{from:s-(i+1)*e,to:s-i*e}}class L0{constructor(t,e,i){this.view=t,this.stateField=e,this.applyCompletion=i,this.info=null,this.infoDestroy=null,this.placeInfoReq={read:()=>this.measureInfo(),write:a=>this.placeInfo(a),key:this},this.space=null,this.currentClass="";let n=t.state.field(e),{options:r,selected:o}=n.open,l=t.state.facet(rt);this.optionContent=P0(l),this.optionClass=l.optionClass,this.tooltipClass=l.tooltipClass,this.range=js(r.length,o,l.maxRenderedOptions),this.dom=document.createElement("div"),this.dom.className="cm-tooltip-autocomplete",this.updateTooltipClass(t.state),this.dom.addEventListener("mousedown",a=>{let{options:h}=t.state.field(e).open;for(let f=a.target,c;f&&f!=this.dom;f=f.parentNode)if(f.nodeName=="LI"&&(c=/-(\d+)$/.exec(f.id))&&+c[1]<h.length){this.applyCompletion(t,h[+c[1]]),a.preventDefault();return}}),this.dom.addEventListener("focusout",a=>{let h=t.state.field(this.stateField,!1);h&&h.tooltip&&t.state.facet(rt).closeOnBlur&&a.relatedTarget!=t.contentDOM&&t.dispatch({effects:Yi.of(null)})}),this.showOptions(r,n.id)}mount(){this.updateSel()}showOptions(t,e){this.list&&this.list.remove(),this.list=this.dom.appendChild(this.createListBox(t,e,this.range)),this.list.addEventListener("scroll",()=>{this.info&&this.view.requestMeasure(this.placeInfoReq)})}update(t){var e;let i=t.state.field(this.stateField),n=t.startState.field(this.stateField);if(this.updateTooltipClass(t.state),i!=n){let{options:r,selected:o,disabled:l}=i.open;(!n.open||n.open.options!=r)&&(this.range=js(r.length,o,t.state.facet(rt).maxRenderedOptions),this.showOptions(r,i.id)),this.updateSel(),l!=((e=n.open)===null||e===void 0?void 0:e.disabled)&&this.dom.classList.toggle("cm-tooltip-autocomplete-disabled",!!l)}}updateTooltipClass(t){let e=this.tooltipClass(t);if(e!=this.currentClass){for(let i of this.currentClass.split(" "))i&&this.dom.classList.remove(i);for(let i of e.split(" "))i&&this.dom.classList.add(i);this.currentClass=e}}positioned(t){this.space=t,this.info&&this.view.requestMeasure(this.placeInfoReq)}updateSel(){let t=this.view.state.field(this.stateField),e=t.open;if((e.selected>-1&&e.selected<this.range.from||e.selected>=this.range.to)&&(this.range=js(e.options.length,e.selected,this.view.state.facet(rt).maxRenderedOptions),this.showOptions(e.options,t.id)),this.updateSelectedOption(e.selected)){this.destroyInfo();let{completion:i}=e.options[e.selected],{info:n}=i;if(!n)return;let r=typeof n=="string"?document.createTextNode(n):n(i);if(!r)return;"then"in r?r.then(o=>{o&&this.view.state.field(this.stateField,!1)==t&&this.addInfoPane(o,i)}).catch(o=>Mt(this.view.state,o,"completion info")):this.addInfoPane(r,i)}}addInfoPane(t,e){this.destroyInfo();let i=this.info=document.createElement("div");if(i.className="cm-tooltip cm-completionInfo",t.nodeType!=null)i.appendChild(t),this.infoDestroy=null;else{let{dom:n,destroy:r}=t;i.appendChild(n),this.infoDestroy=r||null}this.dom.appendChild(i),this.view.requestMeasure(this.placeInfoReq)}updateSelectedOption(t){let e=null;for(let i=this.list.firstChild,n=this.range.from;i;i=i.nextSibling,n++)i.nodeName!="LI"||!i.id?n--:n==t?i.hasAttribute("aria-selected")||(i.setAttribute("aria-selected","true"),e=i):i.hasAttribute("aria-selected")&&i.removeAttribute("aria-selected");return e&&I0(this.list,e),e}measureInfo(){let t=this.dom.querySelector("[aria-selected]");if(!t||!this.info)return null;let e=this.dom.getBoundingClientRect(),i=this.info.getBoundingClientRect(),n=t.getBoundingClientRect(),r=this.space;if(!r){let o=this.dom.ownerDocument.documentElement;r={left:0,top:0,right:o.clientWidth,bottom:o.clientHeight}}return n.top>Math.min(r.bottom,e.bottom)-10||n.bottom<Math.max(r.top,e.top)+10?null:this.view.state.facet(rt).positionInfo(this.view,e,n,i,r,this.dom)}placeInfo(t){this.info&&(t?(t.style&&(this.info.style.cssText=t.style),this.info.className="cm-tooltip cm-completionInfo "+(t.class||"")):this.info.style.cssText="top: -1e6px")}createListBox(t,e,i){const n=document.createElement("ul");n.id=e,n.setAttribute("role","listbox"),n.setAttribute("aria-expanded","true"),n.setAttribute("aria-label",this.view.state.phrase("Completions")),n.addEventListener("mousedown",o=>{o.target==n&&o.preventDefault()});let r=null;for(let o=i.from;o<i.to;o++){let{completion:l,match:a}=t[o],{section:h}=l;if(h){let u=typeof h=="string"?h:h.name;if(u!=r&&(o>i.from||i.from==0))if(r=u,typeof h!="string"&&h.header)n.appendChild(h.header(h));else{let d=n.appendChild(document.createElement("completion-section"));d.textContent=u}}const f=n.appendChild(document.createElement("li"));f.id=e+"-"+o,f.setAttribute("role","option");let c=this.optionClass(l);c&&(f.className=c);for(let u of this.optionContent){let d=u(l,this.view.state,this.view,a);d&&f.appendChild(d)}}return i.from&&n.classList.add("cm-completionListIncompleteTop"),i.to<t.length&&n.classList.add("cm-completionListIncompleteBottom"),n}destroyInfo(){this.info&&(this.infoDestroy&&this.infoDestroy(),this.info.remove(),this.info=null)}destroy(){this.destroyInfo()}}function R0(s,t){return e=>new L0(e,s,t)}function I0(s,t){let e=s.getBoundingClientRect(),i=t.getBoundingClientRect(),n=e.height/s.offsetHeight;i.top<e.top?s.scrollTop-=(e.top-i.top)/n:i.bottom>e.bottom&&(s.scrollTop+=(i.bottom-e.bottom)/n)}function va(s){return(s.boost||0)*100+(s.apply?10:0)+(s.info?5:0)+(s.type?1:0)}function N0(s,t){let e=[],i=null,n=h=>{e.push(h);let{section:f}=h.completion;if(f){i||(i=[]);let c=typeof f=="string"?f:f.name;i.some(u=>u.name==c)||i.push(typeof f=="string"?{name:c}:f)}},r=t.facet(rt);for(let h of s)if(h.hasResult()){let f=h.result.getMatch;if(h.result.filter===!1)for(let c of h.result.options)n(new wa(c,h.source,f?f(c):[],1e9-e.length));else{let c=t.sliceDoc(h.from,h.to),u,d=r.filterStrict?new B0(c):new O0(c);for(let p of h.result.options)if(u=d.match(p.label)){let m=p.displayLabel?f?f(p,u.matched):[]:u.matched;n(new wa(p,h.source,m,u.score+(p.boost||0)))}}}if(i){let h=Object.create(null),f=0,c=(u,d)=>{var p,m;return((p=u.rank)!==null&&p!==void 0?p:1e9)-((m=d.rank)!==null&&m!==void 0?m:1e9)||(u.name<d.name?-1:1)};for(let u of i.sort(c))f-=1e5,h[u.name]=f;for(let u of e){let{section:d}=u.completion;d&&(u.score+=h[typeof d=="string"?d:d.name])}}let o=[],l=null,a=r.compareCompletions;for(let h of e.sort((f,c)=>c.score-f.score||a(f.completion,c.completion))){let f=h.completion;!l||l.label!=f.label||l.detail!=f.detail||l.type!=null&&f.type!=null&&l.type!=f.type||l.apply!=f.apply||l.boost!=f.boost?o.push(h):va(h.completion)>va(l)&&(o[o.length-1]=h),l=h.completion}return o}class ii{constructor(t,e,i,n,r,o){this.options=t,this.attrs=e,this.tooltip=i,this.timestamp=n,this.selected=r,this.disabled=o}setSelected(t,e){return t==this.selected||t>=this.options.length?this:new ii(this.options,Sa(e,t),this.tooltip,this.timestamp,t,this.disabled)}static build(t,e,i,n,r,o){if(n&&!o&&t.some(h=>h.isPending))return n.setDisabled();let l=N0(t,e);if(!l.length)return n&&t.some(h=>h.isPending)?n.setDisabled():null;let a=e.facet(rt).selectOnOpen?0:-1;if(n&&n.selected!=a&&n.selected!=-1){let h=n.options[n.selected].completion;for(let f=0;f<l.length;f++)if(l[f].completion==h){a=f;break}}return new ii(l,Sa(i,a),{pos:t.reduce((h,f)=>f.hasResult()?Math.min(h,f.from):h,1e8),create:z0,above:r.aboveCursor},n?n.timestamp:Date.now(),a,!1)}map(t){return new ii(this.options,this.attrs,Object.assign(Object.assign({},this.tooltip),{pos:t.mapPos(this.tooltip.pos)}),this.timestamp,this.selected,this.disabled)}setDisabled(){return new ii(this.options,this.attrs,this.tooltip,this.timestamp,this.selected,!0)}}class hs{constructor(t,e,i){this.active=t,this.id=e,this.open=i}static start(){return new hs(H0,"cm-ac-"+Math.floor(Math.random()*2e6).toString(36),null)}update(t){let{state:e}=t,i=e.facet(rt),r=(i.override||e.languageDataAt("autocomplete",qe(e)).map(T0)).map(a=>(this.active.find(f=>f.source==a)||new qt(a,this.active.some(f=>f.state!=0)?1:0)).update(t,i));r.length==this.active.length&&r.every((a,h)=>a==this.active[h])&&(r=this.active);let o=this.open,l=t.effects.some(a=>a.is(Co));o&&t.docChanged&&(o=o.map(t.changes)),t.selection||r.some(a=>a.hasResult()&&t.changes.touchesRange(a.from,a.to))||!_0(r,this.active)||l?o=ii.build(r,e,this.id,o,i,l):o&&o.disabled&&!r.some(a=>a.isPending)&&(o=null),!o&&r.every(a=>!a.isPending)&&r.some(a=>a.hasResult())&&(r=r.map(a=>a.hasResult()?new qt(a.source,0):a));for(let a of t.effects)a.is(Mc)&&(o=o&&o.setSelected(a.value,this.id));return r==this.active&&o==this.open?this:new hs(r,this.id,o)}get tooltip(){return this.open?this.open.tooltip:null}get attrs(){return this.open?this.open.attrs:this.active.length?F0:V0}}function _0(s,t){if(s==t)return!0;for(let e=0,i=0;;){for(;e<s.length&&!s[e].hasResult();)e++;for(;i<t.length&&!t[i].hasResult();)i++;let n=e==s.length,r=i==t.length;if(n||r)return n==r;if(s[e++].result!=t[i++].result)return!1}}const F0={"aria-autocomplete":"list"},V0={};function Sa(s,t){let e={"aria-autocomplete":"list","aria-haspopup":"listbox","aria-controls":s};return t>-1&&(e["aria-activedescendant"]=s+"-"+t),e}const H0=[];function Ac(s,t){if(s.isUserEvent("input.complete")){let i=s.annotation(So);if(i&&t.activateOnCompletion(i))return 12}let e=s.isUserEvent("input.type");return e&&t.activateOnTyping?5:e?1:s.isUserEvent("delete.backward")?2:s.selection?8:s.docChanged?16:0}class qt{constructor(t,e,i=!1){this.source=t,this.state=e,this.explicit=i}hasResult(){return!1}get isPending(){return this.state==1}update(t,e){let i=Ac(t,e),n=this;(i&8||i&16&&this.touches(t))&&(n=new qt(n.source,0)),i&4&&n.state==0&&(n=new qt(this.source,1)),n=n.updateFor(t,i);for(let r of t.effects)if(r.is(as))n=new qt(n.source,1,r.value);else if(r.is(Yi))n=new qt(n.source,0);else if(r.is(Co))for(let o of r.value)o.source==n.source&&(n=o);return n}updateFor(t,e){return this.map(t.changes)}map(t){return this}touches(t){return t.changes.touchesRange(qe(t.state))}}class li extends qt{constructor(t,e,i,n,r,o){super(t,3,e),this.limit=i,this.result=n,this.from=r,this.to=o}hasResult(){return!0}updateFor(t,e){var i;if(!(e&3))return this.map(t.changes);let n=this.result;n.map&&!t.changes.empty&&(n=n.map(n,t.changes));let r=t.changes.mapPos(this.from),o=t.changes.mapPos(this.to,1),l=qe(t.state);if(l>o||!n||e&2&&(qe(t.startState)==this.from||l<this.limit))return new qt(this.source,e&4?1:0);let a=t.changes.mapPos(this.limit);return W0(n.validFor,t.state,r,o)?new li(this.source,this.explicit,a,n,r,o):n.update&&(n=n.update(n,r,o,new Sc(t.state,l,!1)))?new li(this.source,this.explicit,a,n,n.from,(i=n.to)!==null&&i!==void 0?i:qe(t.state)):new qt(this.source,1,this.explicit)}map(t){return t.empty?this:(this.result.map?this.result.map(this.result,t):this.result)?new li(this.source,this.explicit,t.mapPos(this.limit),this.result,t.mapPos(this.from),t.mapPos(this.to,1)):new qt(this.source,0)}touches(t){return t.changes.touchesRange(this.from,this.to)}}function W0(s,t,e,i){if(!s)return!1;let n=t.sliceDoc(e,i);return typeof s=="function"?s(n,e,i,t):Cc(s,!0).test(n)}const Co=I.define({map(s,t){return s.map(e=>e.map(t))}}),Mc=I.define(),At=Tt.define({create(){return hs.start()},update(s,t){return s.update(t)},provide:s=>[ho.from(s,t=>t.tooltip),E.contentAttributes.from(s,t=>t.attrs)]});function Ao(s,t){const e=t.completion.apply||t.completion.label;let i=s.state.field(At).active.find(n=>n.source==t.source);return i instanceof li?(typeof e=="string"?s.dispatch(Object.assign(Object.assign({},D0(s.state,e,i.from,i.to)),{annotations:So.of(t.completion)})):e(s,t.completion,i.from,i.to),!0):!1}const z0=R0(At,Ao);function En(s,t="option"){return e=>{let i=e.state.field(At,!1);if(!i||!i.open||i.open.disabled||Date.now()-i.open.timestamp<e.state.facet(rt).interactionDelay)return!1;let n=1,r;t=="page"&&(r=mf(e,i.open.tooltip))&&(n=Math.max(2,Math.floor(r.dom.offsetHeight/r.dom.querySelector("li").offsetHeight)-1));let{length:o}=i.open.options,l=i.open.selected>-1?i.open.selected+n*(s?1:-1):s?0:o-1;return l<0?l=t=="page"?0:o-1:l>=o&&(l=t=="page"?o-1:0),e.dispatch({effects:Mc.of(l)}),!0}}const Dc=s=>{let t=s.state.field(At,!1);return s.state.readOnly||!t||!t.open||t.open.selected<0||t.open.disabled||Date.now()-t.open.timestamp<s.state.facet(rt).interactionDelay?!1:Ao(s,t.open.options[t.open.selected])},Ca=s=>s.state.field(At,!1)?(s.dispatch({effects:as.of(!0)}),!0):!1,q0=s=>{let t=s.state.field(At,!1);return!t||!t.active.some(e=>e.state!=0)?!1:(s.dispatch({effects:Yi.of(null)}),!0)};class j0{constructor(t,e){this.active=t,this.context=e,this.time=Date.now(),this.updates=[],this.done=void 0}}const K0=50,U0=1e3,G0=ut.fromClass(class{constructor(s){this.view=s,this.debounceUpdate=-1,this.running=[],this.debounceAccept=-1,this.pendingStart=!1,this.composing=0;for(let t of s.state.field(At).active)t.isPending&&this.startQuery(t)}update(s){let t=s.state.field(At),e=s.state.facet(rt);if(!s.selectionSet&&!s.docChanged&&s.startState.field(At)==t)return;let i=s.transactions.some(r=>{let o=Ac(r,e);return o&8||(r.selection||r.docChanged)&&!(o&3)});for(let r=0;r<this.running.length;r++){let o=this.running[r];if(i||o.context.abortOnDocChange&&s.docChanged||o.updates.length+s.transactions.length>K0&&Date.now()-o.time>U0){for(let l of o.context.abortListeners)try{l()}catch(a){Mt(this.view.state,a)}o.context.abortListeners=null,this.running.splice(r--,1)}else o.updates.push(...s.transactions)}this.debounceUpdate>-1&&clearTimeout(this.debounceUpdate),s.transactions.some(r=>r.effects.some(o=>o.is(as)))&&(this.pendingStart=!0);let n=this.pendingStart?50:e.activateOnTypingDelay;if(this.debounceUpdate=t.active.some(r=>r.isPending&&!this.running.some(o=>o.active.source==r.source))?setTimeout(()=>this.startUpdate(),n):-1,this.composing!=0)for(let r of s.transactions)r.isUserEvent("input.type")?this.composing=2:this.composing==2&&r.selection&&(this.composing=3)}startUpdate(){this.debounceUpdate=-1,this.pendingStart=!1;let{state:s}=this.view,t=s.field(At);for(let e of t.active)e.isPending&&!this.running.some(i=>i.active.source==e.source)&&this.startQuery(e);this.running.length&&t.open&&t.open.disabled&&(this.debounceAccept=setTimeout(()=>this.accept(),this.view.state.facet(rt).updateSyncTime))}startQuery(s){let{state:t}=this.view,e=qe(t),i=new Sc(t,e,s.explicit,this.view),n=new j0(s,i);this.running.push(n),Promise.resolve(s.source(i)).then(r=>{n.context.aborted||(n.done=r||null,this.scheduleAccept())},r=>{this.view.dispatch({effects:Yi.of(null)}),Mt(this.view.state,r)})}scheduleAccept(){this.running.every(s=>s.done!==void 0)?this.accept():this.debounceAccept<0&&(this.debounceAccept=setTimeout(()=>this.accept(),this.view.state.facet(rt).updateSyncTime))}accept(){var s;this.debounceAccept>-1&&clearTimeout(this.debounceAccept),this.debounceAccept=-1;let t=[],e=this.view.state.facet(rt),i=this.view.state.field(At);for(let n=0;n<this.running.length;n++){let r=this.running[n];if(r.done===void 0)continue;if(this.running.splice(n--,1),r.done){let l=qe(r.updates.length?r.updates[0].startState:this.view.state),a=Math.min(l,r.done.from+(r.active.explicit?0:1)),h=new li(r.active.source,r.active.explicit,a,r.done,r.done.from,(s=r.done.to)!==null&&s!==void 0?s:l);for(let f of r.updates)h=h.update(f,e);if(h.hasResult()){t.push(h);continue}}let o=i.active.find(l=>l.source==r.active.source);if(o&&o.isPending)if(r.done==null){let l=new qt(r.active.source,0);for(let a of r.updates)l=l.update(a,e);l.isPending||t.push(l)}else this.startQuery(o)}(t.length||i.open&&i.open.disabled)&&this.view.dispatch({effects:Co.of(t)})}},{eventHandlers:{blur(s){let t=this.view.state.field(At,!1);if(t&&t.tooltip&&this.view.state.facet(rt).closeOnBlur){let e=t.open&&mf(this.view,t.open.tooltip);(!e||!e.dom.contains(s.relatedTarget))&&setTimeout(()=>this.view.dispatch({effects:Yi.of(null)}),10)}},compositionstart(){this.composing=1},compositionend(){this.composing==3&&setTimeout(()=>this.view.dispatch({effects:as.of(!1)}),20),this.composing=0}}}),Y0=typeof navigator=="object"&&/Win/.test(navigator.platform),J0=Xe.highest(E.domEventHandlers({keydown(s,t){let e=t.state.field(At,!1);if(!e||!e.open||e.open.disabled||e.open.selected<0||s.key.length>1||s.ctrlKey&&!(Y0&&s.altKey)||s.metaKey)return!1;let i=e.open.options[e.open.selected],n=e.active.find(o=>o.source==i.source),r=i.completion.commitCharacters||n.result.commitCharacters;return r&&r.indexOf(s.key)>-1&&Ao(t,i),!1}})),Tc=E.baseTheme({".cm-tooltip.cm-tooltip-autocomplete":{"& > ul":{fontFamily:"monospace",whiteSpace:"nowrap",overflow:"hidden auto",maxWidth_fallback:"700px",maxWidth:"min(700px, 95vw)",minWidth:"250px",maxHeight:"10em",height:"100%",listStyle:"none",margin:0,padding:0,"& > li, & > completion-section":{padding:"1px 3px",lineHeight:1.2},"& > li":{overflowX:"hidden",textOverflow:"ellipsis",cursor:"pointer"},"& > completion-section":{display:"list-item",borderBottom:"1px solid silver",paddingLeft:"0.5em",opacity:.7}}},"&light .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#17c",color:"white"},"&light .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#777"},"&dark .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#347",color:"white"},"&dark .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#444"},".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after":{content:'"···"',opacity:.5,display:"block",textAlign:"center"},".cm-tooltip.cm-completionInfo":{position:"absolute",padding:"3px 9px",width:"max-content",maxWidth:"400px",boxSizing:"border-box",whiteSpace:"pre-line"},".cm-completionInfo.cm-completionInfo-left":{right:"100%"},".cm-completionInfo.cm-completionInfo-right":{left:"100%"},".cm-completionInfo.cm-completionInfo-left-narrow":{right:"30px"},".cm-completionInfo.cm-completionInfo-right-narrow":{left:"30px"},"&light .cm-snippetField":{backgroundColor:"#00000022"},"&dark .cm-snippetField":{backgroundColor:"#ffffff22"},".cm-snippetFieldPosition":{verticalAlign:"text-top",width:0,height:"1.15em",display:"inline-block",margin:"0 -0.7px -.7em",borderLeft:"1.4px dotted #888"},".cm-completionMatchedText":{textDecoration:"underline"},".cm-completionDetail":{marginLeft:"0.5em",fontStyle:"italic"},".cm-completionIcon":{fontSize:"90%",width:".8em",display:"inline-block",textAlign:"center",paddingRight:".6em",opacity:"0.6",boxSizing:"content-box"},".cm-completionIcon-function, .cm-completionIcon-method":{"&:after":{content:"'ƒ'"}},".cm-completionIcon-class":{"&:after":{content:"'○'"}},".cm-completionIcon-interface":{"&:after":{content:"'◌'"}},".cm-completionIcon-variable":{"&:after":{content:"'𝑥'"}},".cm-completionIcon-constant":{"&:after":{content:"'𝐶'"}},".cm-completionIcon-type":{"&:after":{content:"'𝑡'"}},".cm-completionIcon-enum":{"&:after":{content:"'∪'"}},".cm-completionIcon-property":{"&:after":{content:"'□'"}},".cm-completionIcon-keyword":{"&:after":{content:"'🔑︎'"}},".cm-completionIcon-namespace":{"&:after":{content:"'▢'"}},".cm-completionIcon-text":{"&:after":{content:"'abc'",fontSize:"50%",verticalAlign:"middle"}}});class X0{constructor(t,e,i,n){this.field=t,this.line=e,this.from=i,this.to=n}}class Mo{constructor(t,e,i){this.field=t,this.from=e,this.to=i}map(t){let e=t.mapPos(this.from,-1,ct.TrackDel),i=t.mapPos(this.to,1,ct.TrackDel);return e==null||i==null?null:new Mo(this.field,e,i)}}class Do{constructor(t,e){this.lines=t,this.fieldPositions=e}instantiate(t,e){let i=[],n=[e],r=t.doc.lineAt(e),o=/^\s*/.exec(r.text)[0];for(let a of this.lines){if(i.length){let h=o,f=/^\t*/.exec(a)[0].length;for(let c=0;c<f;c++)h+=t.facet(ps);n.push(e+h.length-f),a=h+a.slice(f)}i.push(a),e+=a.length+1}let l=this.fieldPositions.map(a=>new Mo(a.field,n[a.line]+a.from,n[a.line]+a.to));return{text:i,ranges:l}}static parse(t){let e=[],i=[],n=[],r;for(let o of t.split(/\r\n?|\n/)){for(;r=/[#$]\{(?:(\d+)(?::([^}]*))?|((?:\\[{}]|[^}])*))\}/.exec(o);){let l=r[1]?+r[1]:null,a=r[2]||r[3]||"",h=-1,f=a.replace(/\\[{}]/g,c=>c[1]);for(let c=0;c<e.length;c++)(l!=null?e[c].seq==l:f&&e[c].name==f)&&(h=c);if(h<0){let c=0;for(;c<e.length&&(l==null||e[c].seq!=null&&e[c].seq<l);)c++;e.splice(c,0,{seq:l,name:f}),h=c;for(let u of n)u.field>=h&&u.field++}n.push(new X0(h,i.length,r.index,r.index+f.length)),o=o.slice(0,r.index)+a+o.slice(r.index+r[0].length)}o=o.replace(/\\([{}])/g,(l,a,h)=>{for(let f of n)f.line==i.length&&f.from>h&&(f.from--,f.to--);return a}),i.push(o)}return new Do(i,n)}}let $0=N.widget({widget:new class extends ue{toDOM(){let s=document.createElement("span");return s.className="cm-snippetFieldPosition",s}ignoreEvent(){return!1}}}),Q0=N.mark({class:"cm-snippetField"});class wi{constructor(t,e){this.ranges=t,this.active=e,this.deco=N.set(t.map(i=>(i.from==i.to?$0:Q0).range(i.from,i.to)))}map(t){let e=[];for(let i of this.ranges){let n=i.map(t);if(!n)return null;e.push(n)}return new wi(e,this.active)}selectionInsideField(t){return t.ranges.every(e=>this.ranges.some(i=>i.field==this.active&&i.from<=e.from&&i.to>=e.to))}}const fn=I.define({map(s,t){return s&&s.map(t)}}),Z0=I.define(),Ji=Tt.define({create(){return null},update(s,t){for(let e of t.effects){if(e.is(fn))return e.value;if(e.is(Z0)&&s)return new wi(s.ranges,e.value)}return s&&t.docChanged&&(s=s.map(t.changes)),s&&t.selection&&!s.selectionInsideField(t.selection)&&(s=null),s},provide:s=>E.decorations.from(s,t=>t?t.deco:N.none)});function To(s,t){return k.create(s.filter(e=>e.field==t).map(e=>k.range(e.from,e.to)))}function tb(s){let t=Do.parse(s);return(e,i,n,r)=>{let{text:o,ranges:l}=t.instantiate(e.state,n),{main:a}=e.state.selection,h={changes:{from:n,to:r==a.from?a.to:r,insert:V.of(o)},scrollIntoView:!0,annotations:i?[So.of(i),tt.userEvent.of("input.complete")]:void 0};if(l.length&&(h.selection=To(l,0)),l.some(f=>f.field>0)){let f=new wi(l,0),c=h.effects=[fn.of(f)];e.state.field(Ji,!1)===void 0&&c.push(I.appendConfig.of([Ji,rb,ob,Tc]))}e.dispatch(e.state.update(h))}}function Oc(s){return({state:t,dispatch:e})=>{let i=t.field(Ji,!1);if(!i||s<0&&i.active==0)return!1;let n=i.active+s,r=s>0&&!i.ranges.some(o=>o.field==n+s);return e(t.update({selection:To(i.ranges,n),effects:fn.of(r?null:new wi(i.ranges,n)),scrollIntoView:!0})),!0}}const eb=({state:s,dispatch:t})=>s.field(Ji,!1)?(t(s.update({effects:fn.of(null)})),!0):!1,ib=Oc(1),nb=Oc(-1),sb=[{key:"Tab",run:ib,shift:nb},{key:"Escape",run:eb}],Aa=B.define({combine(s){return s.length?s[0]:sb}}),rb=Xe.highest(on.compute([Aa],s=>s.facet(Aa)));function Uy(s,t){return Object.assign(Object.assign({},t),{apply:tb(s)})}const ob=E.domEventHandlers({mousedown(s,t){let e=t.state.field(Ji,!1),i;if(!e||(i=t.posAtCoords({x:s.clientX,y:s.clientY}))==null)return!1;let n=e.ranges.find(r=>r.from<=i&&r.to>=i);return!n||n.field==e.active?!1:(t.dispatch({selection:To(e.ranges,n.field),effects:fn.of(e.ranges.some(r=>r.field>n.field)?new wi(e.ranges,n.field):null),scrollIntoView:!0}),!0)}}),Xi={brackets:["(","[","{","'",'"'],before:")]}:;>",stringPrefixes:[]},ze=I.define({map(s,t){let e=t.mapPos(s,-1,ct.TrackAfter);return e??void 0}}),Oo=new class extends je{};Oo.startSide=1;Oo.endSide=-1;const Bc=Tt.define({create(){return W.empty},update(s,t){if(s=s.map(t.changes),t.selection){let e=t.state.doc.lineAt(t.selection.main.head);s=s.update({filter:i=>i>=e.from&&i<=e.to})}for(let e of t.effects)e.is(ze)&&(s=s.update({add:[Oo.range(e.value,e.value+1)]}));return s}});function lb(){return[hb,Bc]}const Ks="()[]{}<>«»»«［］｛｝";function Ec(s){for(let t=0;t<Ks.length;t+=2)if(Ks.charCodeAt(t)==s)return Ks.charAt(t+1);return th(s<128?s:s+1)}function Pc(s,t){return s.languageDataAt("closeBrackets",t)[0]||Xi}const ab=typeof navigator=="object"&&/Android\b/.test(navigator.userAgent),hb=E.inputHandler.of((s,t,e,i)=>{if((ab?s.composing:s.compositionStarted)||s.state.readOnly)return!1;let n=s.state.selection.main;if(i.length>2||i.length==2&&pe(Bt(i,0))==1||t!=n.from||e!=n.to)return!1;let r=ub(s.state,i);return r?(s.dispatch(r),!0):!1}),fb=({state:s,dispatch:t})=>{if(s.readOnly)return!1;let i=Pc(s,s.selection.main.head).brackets||Xi.brackets,n=null,r=s.changeByRange(o=>{if(o.empty){let l=db(s.doc,o.head);for(let a of i)if(a==l&&ks(s.doc,o.head)==Ec(Bt(a,0)))return{changes:{from:o.head-a.length,to:o.head+a.length},range:k.cursor(o.head-a.length)}}return{range:n=o}});return n||t(s.update(r,{scrollIntoView:!0,userEvent:"delete.backward"})),!n},cb=[{key:"Backspace",run:fb}];function ub(s,t){let e=Pc(s,s.selection.main.head),i=e.brackets||Xi.brackets;for(let n of i){let r=Ec(Bt(n,0));if(t==n)return r==n?gb(s,n,i.indexOf(n+n+n)>-1,e):pb(s,n,r,e.before||Xi.before);if(t==r&&Lc(s,s.selection.main.from))return mb(s,n,r)}return null}function Lc(s,t){let e=!1;return s.field(Bc).between(0,s.doc.length,i=>{i==t&&(e=!0)}),e}function ks(s,t){let e=s.sliceString(t,t+2);return e.slice(0,pe(Bt(e,0)))}function db(s,t){let e=s.sliceString(t-2,t);return pe(Bt(e,0))==e.length?e:e.slice(1)}function pb(s,t,e,i){let n=null,r=s.changeByRange(o=>{if(!o.empty)return{changes:[{insert:t,from:o.from},{insert:e,from:o.to}],effects:ze.of(o.to+t.length),range:k.range(o.anchor+t.length,o.head+t.length)};let l=ks(s.doc,o.head);return!l||/\s/.test(l)||i.indexOf(l)>-1?{changes:{insert:t+e,from:o.head},effects:ze.of(o.head+t.length),range:k.cursor(o.head+t.length)}:{range:n=o}});return n?null:s.update(r,{scrollIntoView:!0,userEvent:"input.type"})}function mb(s,t,e){let i=null,n=s.changeByRange(r=>r.empty&&ks(s.doc,r.head)==e?{changes:{from:r.head,to:r.head+e.length,insert:e},range:k.cursor(r.head+e.length)}:i={range:r});return i?null:s.update(n,{scrollIntoView:!0,userEvent:"input.type"})}function gb(s,t,e,i){let n=i.stringPrefixes||Xi.stringPrefixes,r=null,o=s.changeByRange(l=>{if(!l.empty)return{changes:[{insert:t,from:l.from},{insert:t,from:l.to}],effects:ze.of(l.to+t.length),range:k.range(l.anchor+t.length,l.head+t.length)};let a=l.head,h=ks(s.doc,a),f;if(h==t){if(Ma(s,a))return{changes:{insert:t+t,from:a},effects:ze.of(a+t.length),range:k.cursor(a+t.length)};if(Lc(s,a)){let u=e&&s.sliceDoc(a,a+t.length*3)==t+t+t?t+t+t:t;return{changes:{from:a,to:a+u.length,insert:u},range:k.cursor(a+u.length)}}}else{if(e&&s.sliceDoc(a-2*t.length,a)==t+t&&(f=Da(s,a-2*t.length,n))>-1&&Ma(s,f))return{changes:{insert:t+t+t+t,from:a},effects:ze.of(a+t.length),range:k.cursor(a+t.length)};if(s.charCategorizer(a)(h)!=Vt.Word&&Da(s,a,n)>-1&&!bb(s,a,t,n))return{changes:{insert:t+t,from:a},effects:ze.of(a+t.length),range:k.cursor(a+t.length)}}return{range:r=l}});return r?null:s.update(o,{scrollIntoView:!0,userEvent:"input.type"})}function Ma(s,t){let e=dt(s).resolveInner(t+1);return e.parent&&e.from==t}function bb(s,t,e,i){let n=dt(s).resolveInner(t,-1),r=i.reduce((o,l)=>Math.max(o,l.length),0);for(let o=0;o<5;o++){let l=s.sliceDoc(n.from,Math.min(n.to,n.from+e.length+r)),a=l.indexOf(e);if(!a||a>-1&&i.indexOf(l.slice(0,a))>-1){let f=n.firstChild;for(;f&&f.from==n.from&&f.to-f.from>e.length+a;){if(s.sliceDoc(f.to-e.length,f.to)==e)return!1;f=f.firstChild}return!0}let h=n.to==t&&n.parent;if(!h)break;n=h}return!1}function Da(s,t,e){let i=s.charCategorizer(t);if(i(s.sliceDoc(t-1,t))!=Vt.Word)return t;for(let n of e){let r=t-n.length;if(s.sliceDoc(r,t)==n&&i(s.sliceDoc(r-1,r))!=Vt.Word)return r}return-1}function yb(s={}){return[J0,At,rt.of(s),G0,wb,Tc]}const Rc=[{key:"Ctrl-Space",run:Ca},{mac:"Alt-`",run:Ca},{key:"Escape",run:q0},{key:"ArrowDown",run:En(!0)},{key:"ArrowUp",run:En(!1)},{key:"PageDown",run:En(!0,"page")},{key:"PageUp",run:En(!1,"page")},{key:"Enter",run:Dc}],wb=Xe.highest(on.computeN([rt],s=>s.facet(rt).defaultKeymap?[Rc]:[])),xb="#2E3235",se="#DDDDDD",_i="#B9D2FF",Pn="#b0b0b0",kb="#e0e0e0",Ic="#808080",Us="#000000",vb="#A54543",Nc="#fc6d24",_e="#fda331",Gs="#8abeb7",Ta="#b5bd68",Mi="#6fb3d2",Di="#cc99cc",Sb="#6987AF",Oa=Nc,Ba="#292d30",Ln=_i+"30",Cb=xb,Ys=se,Ab="#202325",Ea=se,Mb=E.theme({"&":{color:se,backgroundColor:Cb},".cm-content":{caretColor:Ea},".cm-cursor, .cm-dropCursor":{borderLeftColor:Ea},"&.cm-focused .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection":{backgroundColor:Ab},".cm-panels":{backgroundColor:Ba,color:Pn},".cm-panels.cm-panels-top":{borderBottom:"2px solid black"},".cm-panels.cm-panels-bottom":{borderTop:"2px solid black"},".cm-searchMatch":{backgroundColor:_i,outline:`1px solid ${Pn}`,color:Us},".cm-searchMatch.cm-searchMatch-selected":{backgroundColor:kb,color:Us},".cm-activeLine":{backgroundColor:Ln},".cm-selectionMatch":{backgroundColor:Ln},"&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket":{outline:`1px solid ${Pn}`},"&.cm-focused .cm-matchingBracket":{backgroundColor:_i,color:Us},".cm-gutters":{borderRight:"1px solid #ffffff10",color:Ic,backgroundColor:Ba},".cm-activeLineGutter":{backgroundColor:Ln},".cm-foldPlaceholder":{backgroundColor:"transparent",border:"none",color:_i},".cm-tooltip":{border:"none",backgroundColor:Ys},".cm-tooltip .cm-tooltip-arrow:before":{borderTopColor:"transparent",borderBottomColor:"transparent"},".cm-tooltip .cm-tooltip-arrow:after":{borderTopColor:Ys,borderBottomColor:Ys},".cm-tooltip-autocomplete":{"& > ul > li[aria-selected]":{backgroundColor:Ln,color:Pn}}},{dark:!0}),Db=bi.define([{tag:b.keyword,color:_e},{tag:[b.name,b.deleted,b.character,b.propertyName,b.macroName],color:Ta},{tag:[b.variableName],color:Mi},{tag:[b.function(b.variableName)],color:_e},{tag:[b.labelName],color:Nc},{tag:[b.color,b.constant(b.name),b.standard(b.name)],color:_e},{tag:[b.definition(b.name),b.separator],color:Di},{tag:[b.brace],color:Di},{tag:[b.annotation],color:Oa},{tag:[b.number,b.changed,b.annotation,b.modifier,b.self,b.namespace],color:_e},{tag:[b.typeName,b.className],color:Mi},{tag:[b.operator,b.operatorKeyword],color:Di},{tag:[b.tagName],color:_e},{tag:[b.squareBracket],color:Di},{tag:[b.angleBracket],color:Di},{tag:[b.attributeName],color:Mi},{tag:[b.regexp],color:_e},{tag:[b.quote],color:se},{tag:[b.string],color:Ta},{tag:b.link,color:Sb,textDecoration:"underline",textUnderlinePosition:"under"},{tag:[b.url,b.escape,b.special(b.string)],color:Gs},{tag:[b.meta],color:vb},{tag:[b.comment],color:Ic,fontStyle:"italic"},{tag:b.monospace,color:se},{tag:b.strong,fontWeight:"bold",color:_e},{tag:b.emphasis,fontStyle:"italic",color:Mi},{tag:b.strikethrough,textDecoration:"line-through"},{tag:b.heading,fontWeight:"bold",color:se},{tag:b.special(b.heading1),fontWeight:"bold",color:se},{tag:b.heading1,fontWeight:"bold",color:se},{tag:[b.heading2,b.heading3,b.heading4],fontWeight:"bold",color:se},{tag:[b.heading5,b.heading6],color:se},{tag:[b.atom,b.bool,b.special(b.variableName)],color:Gs},{tag:[b.processingInstruction,b.inserted],color:Gs},{tag:[b.contentSeparator],color:Mi},{tag:b.invalid,color:_i,borderBottom:`1px dotted ${Oa}`}]),Tb=[Mb,bo(Db)],Pa="#2e3440",Bo="#3b4252",La="#434c5e",Rn="#4c566a",Ra="#e5e9f0",Yr="#eceff4",Js="#8fbcbb",Ia="#88c0d0",Ob="#81a1c1",Jt="#5e81ac",Bb="#bf616a",Ze="#d08770",Xs="#ebcb8b",Na="#a3be8c",Eb="#b48ead",_a="#d30102",Eo=Yr,$s=Eo,Pb="#ffffff",Qs=Bo,Lb=Eo,Fa=Bo,Rb=E.theme({"&":{color:Pa,backgroundColor:Pb},".cm-content":{caretColor:Fa},".cm-cursor, .cm-dropCursor":{borderLeftColor:Fa},"&.cm-focused .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection":{backgroundColor:Lb},".cm-panels":{backgroundColor:Eo,color:Rn},".cm-panels.cm-panels-top":{borderBottom:"2px solid black"},".cm-panels.cm-panels-bottom":{borderTop:"2px solid black"},".cm-searchMatch":{backgroundColor:"#72a1ff59",outline:`1px solid ${Rn}`},".cm-searchMatch.cm-searchMatch-selected":{backgroundColor:Ra},".cm-activeLine":{backgroundColor:$s},".cm-selectionMatch":{backgroundColor:Ra},"&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket":{outline:`1px solid ${Rn}`},"&.cm-focused .cm-matchingBracket":{backgroundColor:Yr},".cm-gutters":{backgroundColor:Yr,color:Pa,border:"none"},".cm-activeLineGutter":{backgroundColor:$s},".cm-foldPlaceholder":{backgroundColor:"transparent",border:"none",color:"#ddd"},".cm-tooltip":{border:"none",backgroundColor:Qs},".cm-tooltip .cm-tooltip-arrow:before":{borderTopColor:"transparent",borderBottomColor:"transparent"},".cm-tooltip .cm-tooltip-arrow:after":{borderTopColor:Qs,borderBottomColor:Qs},".cm-tooltip-autocomplete":{"& > ul > li[aria-selected]":{backgroundColor:$s,color:Rn}}},{dark:!1}),Ib=bi.define([{tag:b.keyword,color:Jt},{tag:[b.name,b.deleted,b.character,b.propertyName,b.macroName],color:Ze},{tag:[b.variableName],color:Ze},{tag:[b.function(b.variableName)],color:Jt},{tag:[b.labelName],color:Ob},{tag:[b.color,b.constant(b.name),b.standard(b.name)],color:Jt},{tag:[b.definition(b.name),b.separator],color:Na},{tag:[b.brace],color:Js},{tag:[b.annotation],color:_a},{tag:[b.number,b.changed,b.annotation,b.modifier,b.self,b.namespace],color:Ia},{tag:[b.typeName,b.className],color:Xs},{tag:[b.operator,b.operatorKeyword],color:Na},{tag:[b.tagName],color:Eb},{tag:[b.squareBracket],color:Bb},{tag:[b.angleBracket],color:Ze},{tag:[b.attributeName],color:Xs},{tag:[b.regexp],color:Jt},{tag:[b.quote],color:Bo},{tag:[b.string],color:Ze},{tag:b.link,color:Js,textDecoration:"underline",textUnderlinePosition:"under"},{tag:[b.url,b.escape,b.special(b.string)],color:Ze},{tag:[b.meta],color:Ia},{tag:[b.comment],color:La,fontStyle:"italic"},{tag:b.strong,fontWeight:"bold",color:Jt},{tag:b.emphasis,fontStyle:"italic",color:Jt},{tag:b.strikethrough,textDecoration:"line-through"},{tag:b.heading,fontWeight:"bold",color:Jt},{tag:b.special(b.heading1),fontWeight:"bold",color:Jt},{tag:b.heading1,fontWeight:"bold",color:Jt},{tag:[b.heading2,b.heading3,b.heading4],fontWeight:"bold",color:Jt},{tag:[b.heading5,b.heading6],color:Jt},{tag:[b.atom,b.bool,b.special(b.variableName)],color:Ze},{tag:[b.processingInstruction,b.inserted],color:Js},{tag:[b.contentSeparator],color:Xs},{tag:b.invalid,color:La,borderBottom:`1px dotted ${_a}`}]),Nb=[Rb,bo(Ib)];function le(){var s=arguments[0];typeof s=="string"&&(s=document.createElement(s));var t=1,e=arguments[1];if(e&&typeof e=="object"&&e.nodeType==null&&!Array.isArray(e)){for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var n=e[i];typeof n=="string"?s.setAttribute(i,n):n!=null&&(s[i]=n)}t++}for(;t<arguments.length;t++)_c(s,arguments[t]);return s}function _c(s,t){if(typeof t=="string")s.appendChild(document.createTextNode(t));else if(t!=null)if(t.nodeType!=null)s.appendChild(t);else if(Array.isArray(t))for(var e=0;e<t.length;e++)_c(s,t[e]);else throw new RangeError("Unsupported child node: "+t)}class Va{constructor(t,e,i){this.from=t,this.to=e,this.diagnostic=i}}class Ve{constructor(t,e,i){this.diagnostics=t,this.panel=e,this.selected=i}static init(t,e,i){let n=t,r=i.facet($i).markerFilter;r&&(n=r(n,i));let o=t.slice().sort((c,u)=>c.from-u.from||c.to-u.to),l=new De,a=[],h=0;for(let c=0;;){let u=c==o.length?null:o[c];if(!u&&!a.length)break;let d,p;for(a.length?(d=h,p=a.reduce((g,y)=>Math.min(g,y.to),u&&u.from>d?u.from:1e8)):(d=u.from,p=u.to,a.push(u),c++);c<o.length;){let g=o[c];if(g.from==d&&(g.to>g.from||g.to==d))a.push(g),c++,p=Math.min(g.to,p);else{p=Math.min(g.from,p);break}}let m=Jb(a);if(a.some(g=>g.from==g.to||g.from==g.to-1&&i.doc.lineAt(g.from).to==g.from))l.add(d,d,N.widget({widget:new Kb(m),diagnostics:a.slice()}));else{let g=a.reduce((y,w)=>w.markClass?y+" "+w.markClass:y,"");l.add(d,p,N.mark({class:"cm-lintRange cm-lintRange-"+m+g,diagnostics:a.slice(),inclusiveEnd:a.some(y=>y.to>p)}))}h=p;for(let g=0;g<a.length;g++)a[g].to<=h&&a.splice(g--,1)}let f=l.finish();return new Ve(f,e,mi(f))}}function mi(s,t=null,e=0){let i=null;return s.between(e,1e9,(n,r,{spec:o})=>{if(!(t&&o.diagnostics.indexOf(t)<0))if(!i)i=new Va(n,r,t||o.diagnostics[0]);else{if(o.diagnostics.indexOf(i.diagnostic)<0)return!1;i=new Va(i.from,r,i.diagnostic)}}),i}function _b(s,t){let e=t.pos,i=t.end||e,n=s.state.facet($i).hideOn(s,e,i);if(n!=null)return n;let r=s.startState.doc.lineAt(t.pos);return!!(s.effects.some(o=>o.is(Fc))||s.changes.touchesRange(r.from,Math.max(r.to,i)))}function Fb(s,t){return s.field(Et,!1)?t:t.concat(I.appendConfig.of(Xb))}const Fc=I.define(),Po=I.define(),Vc=I.define(),Et=Tt.define({create(){return new Ve(N.none,null,null)},update(s,t){if(t.docChanged&&s.diagnostics.size){let e=s.diagnostics.map(t.changes),i=null,n=s.panel;if(s.selected){let r=t.changes.mapPos(s.selected.from,1);i=mi(e,s.selected.diagnostic,r)||mi(e,null,r)}!e.size&&n&&t.state.facet($i).autoPanel&&(n=null),s=new Ve(e,n,i)}for(let e of t.effects)if(e.is(Fc)){let i=t.state.facet($i).autoPanel?e.value.length?Qi.open:null:s.panel;s=Ve.init(e.value,i,t.state)}else e.is(Po)?s=new Ve(s.diagnostics,e.value?Qi.open:null,s.selected):e.is(Vc)&&(s=new Ve(s.diagnostics,s.panel,e.value));return s},provide:s=>[Ir.from(s,t=>t.panel),E.decorations.from(s,t=>t.diagnostics)]}),Vb=N.mark({class:"cm-lintRange cm-lintRange-active"});function Hb(s,t,e){let{diagnostics:i}=s.state.field(Et),n,r=-1,o=-1;i.between(t-(e<0?1:0),t+(e>0?1:0),(a,h,{spec:f})=>{if(t>=a&&t<=h&&(a==h||(t>a||e>0)&&(t<h||e<0)))return n=f.diagnostics,r=a,o=h,!1});let l=s.state.facet($i).tooltipFilter;return n&&l&&(n=l(n,s.state)),n?{pos:r,end:o,above:s.state.doc.lineAt(r).to<o,create(){return{dom:Wb(s,n)}}}:null}function Wb(s,t){return le("ul",{class:"cm-tooltip-lint"},t.map(e=>Wc(s,e,!1)))}const zb=s=>{let t=s.state.field(Et,!1);(!t||!t.panel)&&s.dispatch({effects:Fb(s.state,[Po.of(!0)])});let e=Kp(s,Qi.open);return e&&e.dom.querySelector(".cm-panel-lint ul").focus(),!0},Ha=s=>{let t=s.state.field(Et,!1);return!t||!t.panel?!1:(s.dispatch({effects:Po.of(!1)}),!0)},qb=s=>{let t=s.state.field(Et,!1);if(!t)return!1;let e=s.state.selection.main,i=t.diagnostics.iter(e.to+1);return!i.value&&(i=t.diagnostics.iter(0),!i.value||i.from==e.from&&i.to==e.to)?!1:(s.dispatch({selection:{anchor:i.from,head:i.to},scrollIntoView:!0}),!0)},jb=[{key:"Mod-Shift-m",run:zb,preventDefault:!0},{key:"F8",run:qb}],$i=B.define({combine(s){return Object.assign({sources:s.map(t=>t.source).filter(t=>t!=null)},$e(s.map(t=>t.config),{delay:750,markerFilter:null,tooltipFilter:null,needsRefresh:null,hideOn:()=>null},{needsRefresh:(t,e)=>t?e?i=>t(i)||e(i):t:e}))}});function Hc(s){let t=[];if(s)t:for(let{name:e}of s){for(let i=0;i<e.length;i++){let n=e[i];if(/[a-zA-Z]/.test(n)&&!t.some(r=>r.toLowerCase()==n.toLowerCase())){t.push(n);continue t}}t.push("")}return t}function Wc(s,t,e){var i;let n=e?Hc(t.actions):[];return le("li",{class:"cm-diagnostic cm-diagnostic-"+t.severity},le("span",{class:"cm-diagnosticText"},t.renderMessage?t.renderMessage(s):t.message),(i=t.actions)===null||i===void 0?void 0:i.map((r,o)=>{let l=!1,a=u=>{if(u.preventDefault(),l)return;l=!0;let d=mi(s.state.field(Et).diagnostics,t);d&&r.apply(s,d.from,d.to)},{name:h}=r,f=n[o]?h.indexOf(n[o]):-1,c=f<0?h:[h.slice(0,f),le("u",h.slice(f,f+1)),h.slice(f+1)];return le("button",{type:"button",class:"cm-diagnosticAction",onclick:a,onmousedown:a,"aria-label":` Action: ${h}${f<0?"":` (access key "${n[o]})"`}.`},c)}),t.source&&le("div",{class:"cm-diagnosticSource"},t.source))}class Kb extends ue{constructor(t){super(),this.sev=t}eq(t){return t.sev==this.sev}toDOM(){return le("span",{class:"cm-lintPoint cm-lintPoint-"+this.sev})}}class Wa{constructor(t,e){this.diagnostic=e,this.id="item_"+Math.floor(Math.random()*4294967295).toString(16),this.dom=Wc(t,e,!0),this.dom.id=this.id,this.dom.setAttribute("role","option")}}class Qi{constructor(t){this.view=t,this.items=[];let e=n=>{if(n.keyCode==27)Ha(this.view),this.view.focus();else if(n.keyCode==38||n.keyCode==33)this.moveSelection((this.selectedIndex-1+this.items.length)%this.items.length);else if(n.keyCode==40||n.keyCode==34)this.moveSelection((this.selectedIndex+1)%this.items.length);else if(n.keyCode==36)this.moveSelection(0);else if(n.keyCode==35)this.moveSelection(this.items.length-1);else if(n.keyCode==13)this.view.focus();else if(n.keyCode>=65&&n.keyCode<=90&&this.selectedIndex>=0){let{diagnostic:r}=this.items[this.selectedIndex],o=Hc(r.actions);for(let l=0;l<o.length;l++)if(o[l].toUpperCase().charCodeAt(0)==n.keyCode){let a=mi(this.view.state.field(Et).diagnostics,r);a&&r.actions[l].apply(t,a.from,a.to)}}else return;n.preventDefault()},i=n=>{for(let r=0;r<this.items.length;r++)this.items[r].dom.contains(n.target)&&this.moveSelection(r)};this.list=le("ul",{tabIndex:0,role:"listbox","aria-label":this.view.state.phrase("Diagnostics"),onkeydown:e,onclick:i}),this.dom=le("div",{class:"cm-panel-lint"},this.list,le("button",{type:"button",name:"close","aria-label":this.view.state.phrase("close"),onclick:()=>Ha(this.view)},"×")),this.update()}get selectedIndex(){let t=this.view.state.field(Et).selected;if(!t)return-1;for(let e=0;e<this.items.length;e++)if(this.items[e].diagnostic==t.diagnostic)return e;return-1}update(){let{diagnostics:t,selected:e}=this.view.state.field(Et),i=0,n=!1,r=null,o=new Set;for(t.between(0,this.view.state.doc.length,(l,a,{spec:h})=>{for(let f of h.diagnostics){if(o.has(f))continue;o.add(f);let c=-1,u;for(let d=i;d<this.items.length;d++)if(this.items[d].diagnostic==f){c=d;break}c<0?(u=new Wa(this.view,f),this.items.splice(i,0,u),n=!0):(u=this.items[c],c>i&&(this.items.splice(i,c-i),n=!0)),e&&u.diagnostic==e.diagnostic?u.dom.hasAttribute("aria-selected")||(u.dom.setAttribute("aria-selected","true"),r=u):u.dom.hasAttribute("aria-selected")&&u.dom.removeAttribute("aria-selected"),i++}});i<this.items.length&&!(this.items.length==1&&this.items[0].diagnostic.from<0);)n=!0,this.items.pop();this.items.length==0&&(this.items.push(new Wa(this.view,{from:-1,to:-1,severity:"info",message:this.view.state.phrase("No diagnostics")})),n=!0),r?(this.list.setAttribute("aria-activedescendant",r.id),this.view.requestMeasure({key:this,read:()=>({sel:r.dom.getBoundingClientRect(),panel:this.list.getBoundingClientRect()}),write:({sel:l,panel:a})=>{let h=a.height/this.list.offsetHeight;l.top<a.top?this.list.scrollTop-=(a.top-l.top)/h:l.bottom>a.bottom&&(this.list.scrollTop+=(l.bottom-a.bottom)/h)}})):this.selectedIndex<0&&this.list.removeAttribute("aria-activedescendant"),n&&this.sync()}sync(){let t=this.list.firstChild;function e(){let i=t;t=i.nextSibling,i.remove()}for(let i of this.items)if(i.dom.parentNode==this.list){for(;t!=i.dom;)e();t=i.dom.nextSibling}else this.list.insertBefore(i.dom,t);for(;t;)e()}moveSelection(t){if(this.selectedIndex<0)return;let e=this.view.state.field(Et),i=mi(e.diagnostics,this.items[t].diagnostic);i&&this.view.dispatch({selection:{anchor:i.from,head:i.to},scrollIntoView:!0,effects:Vc.of(i)})}static open(t){return new Qi(t)}}function Ub(s,t='viewBox="0 0 40 40"'){return`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" ${t}>${encodeURIComponent(s)}</svg>')`}function In(s){return Ub(`<path d="m0 2.5 l2 -1.5 l1 0 l2 1.5 l1 0" stroke="${s}" fill="none" stroke-width=".7"/>`,'width="6" height="3"')}const Gb=E.baseTheme({".cm-diagnostic":{padding:"3px 6px 3px 8px",marginLeft:"-1px",display:"block",whiteSpace:"pre-wrap"},".cm-diagnostic-error":{borderLeft:"5px solid #d11"},".cm-diagnostic-warning":{borderLeft:"5px solid orange"},".cm-diagnostic-info":{borderLeft:"5px solid #999"},".cm-diagnostic-hint":{borderLeft:"5px solid #66d"},".cm-diagnosticAction":{font:"inherit",border:"none",padding:"2px 4px",backgroundColor:"#444",color:"white",borderRadius:"3px",marginLeft:"8px",cursor:"pointer"},".cm-diagnosticSource":{fontSize:"70%",opacity:.7},".cm-lintRange":{backgroundPosition:"left bottom",backgroundRepeat:"repeat-x",paddingBottom:"0.7px"},".cm-lintRange-error":{backgroundImage:In("#d11")},".cm-lintRange-warning":{backgroundImage:In("orange")},".cm-lintRange-info":{backgroundImage:In("#999")},".cm-lintRange-hint":{backgroundImage:In("#66d")},".cm-lintRange-active":{backgroundColor:"#ffdd9980"},".cm-tooltip-lint":{padding:0,margin:0},".cm-lintPoint":{position:"relative","&:after":{content:'""',position:"absolute",bottom:0,left:"-2px",borderLeft:"3px solid transparent",borderRight:"3px solid transparent",borderBottom:"4px solid #d11"}},".cm-lintPoint-warning":{"&:after":{borderBottomColor:"orange"}},".cm-lintPoint-info":{"&:after":{borderBottomColor:"#999"}},".cm-lintPoint-hint":{"&:after":{borderBottomColor:"#66d"}},".cm-panel.cm-panel-lint":{position:"relative","& ul":{maxHeight:"100px",overflowY:"auto","& [aria-selected]":{backgroundColor:"#ddd","& u":{textDecoration:"underline"}},"&:focus [aria-selected]":{background_fallback:"#bdf",backgroundColor:"Highlight",color_fallback:"white",color:"HighlightText"},"& u":{textDecoration:"none"},padding:0,margin:0},"& [name=close]":{position:"absolute",top:"0",right:"2px",background:"inherit",border:"none",font:"inherit",padding:0,margin:0}}});function Yb(s){return s=="error"?4:s=="warning"?3:s=="info"?2:1}function Jb(s){let t="hint",e=1;for(let i of s){let n=Yb(i.severity);n>e&&(e=n,t=i.severity)}return t}const Xb=[Et,E.decorations.compute([Et],s=>{let{selected:t,panel:e}=s.field(Et);return!t||!e||t.from==t.to?N.none:N.set([Vb.range(t.from,t.to)])}),qp(Hb,{hideOn:_b}),Gb],$b=[kp(),Sg(),$m(),up(),H.allowMultipleSelections.of(!0),_m(),bo(eg,{fallback:!0}),lb(),Pp(),Ip(),on.of([...cb,...S0,...Pg,...Gm,...Rc,...jb])],Qb=["standardSQL","msSQL","mySQL","mariaDB","sqlite","cassandra","plSQL","hive","pgSQL","gql","gpSQL","sparkSQL","esper"],za={python:()=>nt(()=>import("./index.Ck8lZcnN.js"),__vite__mapDeps([0,1]),import.meta.url).then(s=>s.python()),c:()=>nt(()=>import("./clike.wD8xDpL-.js"),[],import.meta.url).then(s=>_t.define(s.c)),cpp:()=>nt(()=>import("./clike.wD8xDpL-.js"),[],import.meta.url).then(s=>_t.define(s.cpp)),markdown:async()=>{const[s,t]=await Promise.all([nt(()=>import("./index.CzquxtZ2.js"),__vite__mapDeps([2,3,1,4,5]),import.meta.url),nt(()=>import("./frontmatter.DYpCVtLj.js"),__vite__mapDeps([6,7]),import.meta.url)]);return s.markdown({extensions:[t.frontmatter]})},latex:()=>nt(()=>import("./stex.C3f8Ysf7.js"),[],import.meta.url).then(s=>_t.define(s.stex)),json:()=>nt(()=>import("./index.DbWXTr9t.js"),__vite__mapDeps([8,1]),import.meta.url).then(s=>s.json()),html:()=>nt(()=>import("./index.CADHlz5M.js"),__vite__mapDeps([3,1,4,5]),import.meta.url).then(s=>s.html()),css:()=>nt(()=>import("./index.HZFcoZEE.js"),__vite__mapDeps([4,1]),import.meta.url).then(s=>s.css()),javascript:()=>nt(()=>import("./index.CuPf8lR7.js"),__vite__mapDeps([5,1]),import.meta.url).then(s=>s.javascript()),jinja2:()=>nt(()=>import("./jinja2.C4DGRd-O.js"),[],import.meta.url).then(s=>_t.define(s.jinja2)),typescript:()=>nt(()=>import("./index.CuPf8lR7.js"),__vite__mapDeps([5,1]),import.meta.url).then(s=>s.javascript({typescript:!0})),yaml:()=>nt(()=>import("./yaml.DsCXHVTH.js"),[],import.meta.url).then(s=>_t.define(s.yaml)),dockerfile:()=>nt(()=>import("./dockerfile.D3l6Kuvz.js"),[],import.meta.url).then(s=>_t.define(s.dockerFile)),shell:()=>nt(()=>import("./shell.CjFT_Tl9.js"),[],import.meta.url).then(s=>_t.define(s.shell)),r:()=>nt(()=>import("./r.DUYO_cvP.js"),[],import.meta.url).then(s=>_t.define(s.r)),sql:()=>nt(()=>import("./sql.C4g8LzGK.js"),[],import.meta.url).then(s=>_t.define(s.standardSQL)),...Object.fromEntries(Qb.map(s=>["sql-"+s,()=>nt(()=>import("./sql.C4g8LzGK.js"),[],import.meta.url).then(t=>_t.define(t[s]))]))},Zb={py:"python",md:"markdown",js:"javascript",ts:"typescript",sh:"shell"};async function ty(s){const t=za[s]||za[Zb[s]]||void 0;if(t)return t()}const ey={module:"namespace"};function iy(){let s;try{s=eu()}catch{return console.debug("Not in the Wasm env. Context-aware autocomplete disabled."),null}if(!s)return null;const t=s;return async function(i){try{const n=await t.getCodeCompletions({code:i.state.doc.toString(),line:i.state.doc.lineAt(i.state.selection.main.head).number,column:i.state.selection.main.head-i.state.doc.lineAt(i.state.selection.main.head).from});return n.length===0?null:{from:i.state.selection.main.head-n[0].completion_prefix_length,options:n.map(r=>({label:r.label,type:ey[r.type]??r.type,documentation:r.docstring,boost:r.label.startsWith("_")?-1:0}))}}catch(n){return console.error("Error getting completions",n),null}}}function ny(s){let t,e,i;return{c(){t=Lo("div"),e=Lo("div"),this.h()},l(n){t=Ro(n,"DIV",{class:!0});var r=Io(t);e=Ro(r,"DIV",{class:!0}),Io(e).forEach(Me),r.forEach(Me),this.h()},h(){vs(e,"class",i="codemirror-wrapper "+s[0]+" svelte-scxcch"),vs(t,"class","wrap svelte-scxcch")},m(n,r){ni(n,t,r),Uc(t,e),s[16](e)},p(n,r){r[0]&1&&i!==(i="codemirror-wrapper "+n[0]+" svelte-scxcch")&&vs(e,"class",i)},i:No,o:No,d(n){n&&Me(t),s[16](null)}}}function sy(s){var i;let t=s.dom.querySelectorAll(".cm-gutterElement");if(t.length===0)return null;for(var e=0;e<t.length;e++){let n=t[e],r=((i=getComputedStyle(n))==null?void 0:i.height)??"0px";if(r!="0px")return r}return null}function ry(s,t,e){let{class_names:i=""}=t,{value:n=""}=t,{dark_mode:r}=t,{basic:o=!0}=t,{language:l}=t,{lines:a=5}=t,{max_lines:h=null}=t,{extensions:f=[]}=t,{use_tab:c=!0}=t,{readonly:u=!1}=t,{placeholder:d=void 0}=t,{wrap_lines:p=!1}=t,{show_line_numbers:m=!0}=t,{autocomplete:g=!1}=t;const y=Gc();let w,S,v;const x=iy();async function C(O){const $=await ty(O);x&&O==="python"&&$ instanceof Tm&&$.support.push($.language.data.of({autocomplete:x})),e(15,w=$)}function A(O){v&&O!==v.state.doc.toString()&&v.dispatch({changes:{from:0,to:v.state.doc.length,insert:O}})}function L(){v&&v.requestMeasure({read:P})}function R(){const O=new E({parent:S,state:yt(n)});return O.dom.addEventListener("focus",z,!0),O.dom.addEventListener("blur",M,!0),O}function z(){y("focus")}function M(){y("blur")}function P(O){let $=O.dom.querySelector(".cm-scroller");if(!$)return null;const lt=sy(O);if(!lt)return null;const Yt=a==1?1:a+1;$.style.minHeight=`calc(${lt} * ${Yt})`,h&&($.style.maxHeight=`calc(${lt} * ${h+1})`)}function q(O){if(O.docChanged){const lt=O.state.doc.toString();e(2,n=lt),y("change",lt)}v.requestMeasure({read:P})}function F(){return[...Nt(o,c,d,u,w,m),j,...et(),...f]}const j=E.theme({"&":{fontSize:"var(--text-sm)",backgroundColor:"var(--border-color-secondary)"},".cm-content":{paddingTop:"5px",paddingBottom:"5px",color:"var(--body-text-color)",fontFamily:"var(--font-mono)",minHeight:"100%"},".cm-gutterElement":{marginRight:"var(--spacing-xs)"},".cm-gutters":{marginRight:"1px",borderRight:"1px solid var(--border-color-primary)",backgroundColor:"var(--block-background-fill);",color:"var(--body-text-color-subdued)"},".cm-focused":{outline:"none"},".cm-scroller":{height:"auto"},".cm-cursor":{borderLeftColor:"var(--body-text-color)"}}),ot=E.theme({".cm-tooltip-autocomplete":{"& > ul":{backgroundColor:"var(--background-fill-primary)",color:"var(--body-text-color)"},"& > ul > li[aria-selected]":{backgroundColor:"var(--color-accent-soft)",color:"var(--body-text-color)"}}});function yt(O){return H.create({doc:O??void 0,extensions:F()})}function Nt(O,$,lt,Yt,Ie,cn){const Zt=[E.editable.of(!Yt),H.readOnly.of(Yt),E.contentAttributes.of({"aria-label":"Code input container"})];return O&&Zt.push($b),$&&Zt.push(on.of([{key:"Tab",run:Dc},C0])),lt&&Zt.push(Tp(lt)),Ie&&Zt.push(Ie),cn&&Zt.push(em()),g&&(Zt.push(yb()),Zt.push(ot)),Zt.push(E.updateListener.of(q)),p&&Zt.push(E.lineWrapping),Zt}function et(){const O=[];return r?O.push(Tb):O.push(Nb),O}function St(){v==null||v.dispatch({effects:I.reconfigure.of(F())})}Yc(()=>(v=R(),()=>v==null?void 0:v.destroy()));function Gt(O){ja[O?"unshift":"push"](()=>{S=O,e(1,S)})}return s.$$set=O=>{"class_names"in O&&e(0,i=O.class_names),"value"in O&&e(2,n=O.value),"dark_mode"in O&&e(3,r=O.dark_mode),"basic"in O&&e(4,o=O.basic),"language"in O&&e(5,l=O.language),"lines"in O&&e(6,a=O.lines),"max_lines"in O&&e(7,h=O.max_lines),"extensions"in O&&e(8,f=O.extensions),"use_tab"in O&&e(9,c=O.use_tab),"readonly"in O&&e(10,u=O.readonly),"placeholder"in O&&e(11,d=O.placeholder),"wrap_lines"in O&&e(12,p=O.wrap_lines),"show_line_numbers"in O&&e(13,m=O.show_line_numbers),"autocomplete"in O&&e(14,g=O.autocomplete)},s.$$.update=()=>{s.$$.dirty[0]&32&&C(l),s.$$.dirty[0]&33792&&St(),s.$$.dirty[0]&4&&A(n)},L(),[i,S,n,r,o,l,a,h,f,c,u,d,p,m,g,w,Gt]}class oy extends Zi{constructor(t){super(),tn(this,t,ry,ny,en,{class_names:0,value:2,dark_mode:3,basic:4,language:5,lines:6,max_lines:7,extensions:8,use_tab:9,readonly:10,placeholder:11,wrap_lines:12,show_line_numbers:13,autocomplete:14},null,[-1,-1])}}const zc=oy;function ly(s){let t,e;return t=new Ua({props:{Icon:s[0]?Gn:Ho}}),t.$on("click",s[1]),{c(){Pt(t.$$.fragment)},l(i){Lt(t.$$.fragment,i)},m(i,n){Rt(t,i,n),e=!0},p(i,[n]){const r={};n&1&&(r.Icon=i[0]?Gn:Ho),t.$set(r)},i(i){e||(st(t.$$.fragment,i),e=!0)},o(i){ft(t.$$.fragment,i),e=!1},d(i){It(t,i)}}}function ay(s,t,e){let i=!1,{value:n}=t,r;function o(){e(0,i=!0),r&&clearTimeout(r),r=setTimeout(()=>{e(0,i=!1)},2e3)}async function l(){"clipboard"in navigator&&(await navigator.clipboard.writeText(n),o())}return Ka(()=>{r&&clearTimeout(r)}),s.$$set=a=>{"value"in a&&e(2,n=a.value)},[i,l,n]}class hy extends Zi{constructor(t){super(),tn(this,t,ay,ly,en,{value:2})}}const qc=hy;function fy(s){let t,e;return t=new Ua({props:{Icon:s[0]?Gn:Wo}}),{c(){Pt(t.$$.fragment)},l(i){Lt(t.$$.fragment,i)},m(i,n){Rt(t,i,n),e=!0},p(i,n){const r={};n&1&&(r.Icon=i[0]?Gn:Wo),t.$set(r)},i(i){e||(st(t.$$.fragment,i),e=!0)},o(i){ft(t.$$.fragment,i),e=!1},d(i){It(t,i)}}}function cy(s){let t,e;return t=new su({props:{download:"file."+s[2],href:s[1],$$slots:{default:[fy]},$$scope:{ctx:s}}}),t.$on("click",s[3]),{c(){Pt(t.$$.fragment)},l(i){Lt(t.$$.fragment,i)},m(i,n){Rt(t,i,n),e=!0},p(i,[n]){const r={};n&4&&(r.download="file."+i[2]),n&2&&(r.href=i[1]),n&129&&(r.$$scope={dirty:n,ctx:i}),t.$set(r)},i(i){e||(st(t.$$.fragment,i),e=!0)},o(i){ft(t.$$.fragment,i),e=!1},d(i){It(t,i)}}}function uy(s){return{py:"py",python:"py",md:"md",markdown:"md",json:"json",html:"html",css:"css",js:"js",javascript:"js",ts:"ts",typescript:"ts",yaml:"yaml",yml:"yml",dockerfile:"dockerfile",sh:"sh",shell:"sh",r:"r",c:"c",cpp:"cpp",latex:"tex"}[s]||"txt"}function dy(s,t,e){let i,n,{value:r}=t,{language:o}=t,l=!1,a;function h(){e(0,l=!0),a&&clearTimeout(a),a=setTimeout(()=>{e(0,l=!1)},2e3)}return Ka(()=>{a&&clearTimeout(a)}),s.$$set=f=>{"value"in f&&e(4,r=f.value),"language"in f&&e(5,o=f.language)},s.$$.update=()=>{s.$$.dirty&32&&e(2,i=uy(o)),s.$$.dirty&16&&e(1,n=URL.createObjectURL(new Blob([r])))},[l,n,i,h,r,o]}class py extends Zi{constructor(t){super(),tn(this,t,dy,cy,en,{value:4,language:5})}}const jc=py;function my(s){let t,e,i,n;return t=new jc({props:{value:s[0],language:s[1]}}),i=new qc({props:{value:s[0]}}),{c(){Pt(t.$$.fragment),e=Kn(),Pt(i.$$.fragment)},l(r){Lt(t.$$.fragment,r),e=Un(r),Lt(i.$$.fragment,r)},m(r,o){Rt(t,r,o),ni(r,e,o),Rt(i,r,o),n=!0},p(r,o){const l={};o&1&&(l.value=r[0]),o&2&&(l.language=r[1]),t.$set(l);const a={};o&1&&(a.value=r[0]),i.$set(a)},i(r){n||(st(t.$$.fragment,r),st(i.$$.fragment,r),n=!0)},o(r){ft(t.$$.fragment,r),ft(i.$$.fragment,r),n=!1},d(r){r&&Me(e),It(t,r),It(i,r)}}}function gy(s){let t,e;return t=new ru({props:{$$slots:{default:[my]},$$scope:{ctx:s}}}),{c(){Pt(t.$$.fragment)},l(i){Lt(t.$$.fragment,i)},m(i,n){Rt(t,i,n),e=!0},p(i,[n]){const r={};n&7&&(r.$$scope={dirty:n,ctx:i}),t.$set(r)},i(i){e||(st(t.$$.fragment,i),e=!0)},o(i){ft(t.$$.fragment,i),e=!1},d(i){It(t,i)}}}function by(s,t,e){let{value:i}=t,{language:n}=t;return s.$$set=r=>{"value"in r&&e(0,i=r.value),"language"in r&&e(1,n=r.language)},[i,n]}class yy extends Zi{constructor(t){super(),tn(this,t,by,gy,en,{value:0,language:1})}}const Kc=yy;function qa(s){let t,e;return t=new ou({props:{Icon:Ga,show_label:s[9],label:s[8],float:!1}}),{c(){Pt(t.$$.fragment)},l(i){Lt(t.$$.fragment,i)},m(i,n){Rt(t,i,n),e=!0},p(i,n){const r={};n&512&&(r.show_label=i[9]),n&256&&(r.label=i[8]),t.$set(r)},i(i){e||(st(t.$$.fragment,i),e=!0)},o(i){ft(t.$$.fragment,i),e=!1},d(i){It(t,i)}}}function wy(s){let t,e,i,n,r;t=new Kc({props:{language:s[2],value:s[0]}});function o(a){s[20](a)}let l={language:s[2],lines:s[3],max_lines:s[4],dark_mode:s[17],wrap_lines:s[13],show_line_numbers:s[14],autocomplete:s[15],readonly:!s[16]};return s[0]!==void 0&&(l.value=s[0]),i=new zc({props:l}),ja.push(()=>Zc(i,"value",o)),i.$on("blur",s[21]),i.$on("focus",s[22]),{c(){Pt(t.$$.fragment),e=Kn(),Pt(i.$$.fragment)},l(a){Lt(t.$$.fragment,a),e=Un(a),Lt(i.$$.fragment,a)},m(a,h){Rt(t,a,h),ni(a,e,h),Rt(i,a,h),r=!0},p(a,h){const f={};h&4&&(f.language=a[2]),h&1&&(f.value=a[0]),t.$set(f);const c={};h&4&&(c.language=a[2]),h&8&&(c.lines=a[3]),h&16&&(c.max_lines=a[4]),h&8192&&(c.wrap_lines=a[13]),h&16384&&(c.show_line_numbers=a[14]),h&32768&&(c.autocomplete=a[15]),h&65536&&(c.readonly=!a[16]),!n&&h&1&&(n=!0,c.value=a[0],tu(()=>n=!1)),i.$set(c)},i(a){r||(st(t.$$.fragment,a),st(i.$$.fragment,a),r=!0)},o(a){ft(t.$$.fragment,a),ft(i.$$.fragment,a),r=!1},d(a){a&&Me(e),It(t,a),It(i,a)}}}function xy(s){let t,e;return t=new lu({props:{unpadded_box:!0,size:"large",$$slots:{default:[ky]},$$scope:{ctx:s}}}),{c(){Pt(t.$$.fragment)},l(i){Lt(t.$$.fragment,i)},m(i,n){Rt(t,i,n),e=!0},p(i,n){const r={};n&16777216&&(r.$$scope={dirty:n,ctx:i}),t.$set(r)},i(i){e||(st(t.$$.fragment,i),e=!0)},o(i){ft(t.$$.fragment,i),e=!1},d(i){It(t,i)}}}function ky(s){let t,e;return t=new Ga({}),{c(){Pt(t.$$.fragment)},l(i){Lt(t.$$.fragment,i)},m(i,n){Rt(t,i,n),e=!0},i(i){e||(st(t.$$.fragment,i),e=!0)},o(i){ft(t.$$.fragment,i),e=!1},d(i){It(t,i)}}}function vy(s){let t,e,i,n,r,o,l;const a=[{autoscroll:s[1].autoscroll},{i18n:s[1].i18n},s[10]];let h={};for(let p=0;p<a.length;p+=1)h=Xc(h,a[p]);t=new nu({props:h}),t.$on("clear_status",s[19]);let f=s[9]&&qa(s);const c=[xy,wy],u=[];function d(p,m){return!p[0]&&!p[16]?0:1}return n=d(s),r=u[n]=c[n](s),{c(){Pt(t.$$.fragment),e=Kn(),f&&f.c(),i=Kn(),r.c(),o=_o()},l(p){Lt(t.$$.fragment,p),e=Un(p),f&&f.l(p),i=Un(p),r.l(p),o=_o()},m(p,m){Rt(t,p,m),ni(p,e,m),f&&f.m(p,m),ni(p,i,m),u[n].m(p,m),ni(p,o,m),l=!0},p(p,m){const g=m&1026?$c(a,[m&2&&{autoscroll:p[1].autoscroll},m&2&&{i18n:p[1].i18n},m&1024&&Qc(p[10])]):{};t.$set(g),p[9]?f?(f.p(p,m),m&512&&st(f,1)):(f=qa(p),f.c(),st(f,1),f.m(i.parentNode,i)):f&&(Fo(),ft(f,1,1,()=>{f=null}),Vo());let y=n;n=d(p),n===y?u[n].p(p,m):(Fo(),ft(u[y],1,1,()=>{u[y]=null}),Vo(),r=u[n],r?r.p(p,m):(r=u[n]=c[n](p),r.c()),st(r,1),r.m(o.parentNode,o))},i(p){l||(st(t.$$.fragment,p),st(f),st(r),l=!0)},o(p){ft(t.$$.fragment,p),ft(f),ft(r),l=!1},d(p){p&&(Me(e),Me(i),Me(o)),It(t,p),f&&f.d(p),u[n].d(p)}}}function Sy(s){let t,e;return t=new iu({props:{height:s[4]&&"fit-content",variant:"solid",padding:!1,elem_id:s[5],elem_classes:s[6],visible:s[7],scale:s[11],min_width:s[12],$$slots:{default:[vy]},$$scope:{ctx:s}}}),{c(){Pt(t.$$.fragment)},l(i){Lt(t.$$.fragment,i)},m(i,n){Rt(t,i,n),e=!0},p(i,[n]){const r={};n&16&&(r.height=i[4]&&"fit-content"),n&32&&(r.elem_id=i[5]),n&64&&(r.elem_classes=i[6]),n&128&&(r.visible=i[7]),n&2048&&(r.scale=i[11]),n&4096&&(r.min_width=i[12]),n&16901919&&(r.$$scope={dirty:n,ctx:i}),t.$set(r)},i(i){e||(st(t.$$.fragment,i),e=!0)},o(i){ft(t.$$.fragment,i),e=!1},d(i){It(t,i)}}}function Cy(s,t,e){let{gradio:i}=t,{value:n=""}=t,{value_is_output:r=!1}=t,{language:o=""}=t,{lines:l=5}=t,{max_lines:a=void 0}=t,{elem_id:h=""}=t,{elem_classes:f=[]}=t,{visible:c=!0}=t,{label:u=i.i18n("code.code")}=t,{show_label:d=!0}=t,{loading_status:p}=t,{scale:m=null}=t,{min_width:g=void 0}=t,{wrap_lines:y=!1}=t,{show_line_numbers:w=!0}=t,{autocomplete:S=!1}=t,{interactive:v}=t,x=i.theme==="dark";function C(){i.dispatch("change",n),r||i.dispatch("input")}Jc(()=>{e(18,r=!1)});const A=()=>i.dispatch("clear_status",p);function L(M){n=M,e(0,n)}const R=()=>i.dispatch("blur"),z=()=>i.dispatch("focus");return s.$$set=M=>{"gradio"in M&&e(1,i=M.gradio),"value"in M&&e(0,n=M.value),"value_is_output"in M&&e(18,r=M.value_is_output),"language"in M&&e(2,o=M.language),"lines"in M&&e(3,l=M.lines),"max_lines"in M&&e(4,a=M.max_lines),"elem_id"in M&&e(5,h=M.elem_id),"elem_classes"in M&&e(6,f=M.elem_classes),"visible"in M&&e(7,c=M.visible),"label"in M&&e(8,u=M.label),"show_label"in M&&e(9,d=M.show_label),"loading_status"in M&&e(10,p=M.loading_status),"scale"in M&&e(11,m=M.scale),"min_width"in M&&e(12,g=M.min_width),"wrap_lines"in M&&e(13,y=M.wrap_lines),"show_line_numbers"in M&&e(14,w=M.show_line_numbers),"autocomplete"in M&&e(15,S=M.autocomplete),"interactive"in M&&e(16,v=M.interactive)},s.$$.update=()=>{s.$$.dirty&1&&C()},[n,i,o,l,a,h,f,c,u,d,p,m,g,y,w,S,v,x,r,A,L,R,z]}class Ay extends Zi{constructor(t){super(),tn(this,t,Cy,Sy,en,{gradio:1,value:0,value_is_output:18,language:2,lines:3,max_lines:4,elem_id:5,elem_classes:6,visible:7,label:8,show_label:9,loading_status:10,scale:11,min_width:12,wrap_lines:13,show_line_numbers:14,autocomplete:15,interactive:16})}}const Gy=Object.freeze(Object.defineProperty({__proto__:null,BaseCode:zc,BaseCopy:qc,BaseDownload:jc,BaseExample:au,BaseWidget:Kc,default:Ay},Symbol.toStringTag,{value:"Module"}));export{di as A,sg as B,Sc as C,im as D,k as E,E as F,qy as G,zy as H,X as I,Am as J,Gy as K,Hr as L,fo as N,vf as P,_t as S,G as T,vt as a,_ as b,jy as c,dt as d,Tf as e,Vm as f,Wy as g,Tm as h,Ky as i,M0 as j,Fy as k,Uy as l,Ft as m,gi as n,Xe as o,Vy as p,on as q,H as r,xm as s,b as t,Wt as u,ps as v,Af as w,We as x,Fm as y,Df as z};
//# sourceMappingURL=Index.B7Jp9MPG.js.map
