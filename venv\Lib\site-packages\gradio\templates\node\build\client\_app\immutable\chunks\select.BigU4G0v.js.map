{"version": 3, "file": "select.BigU4G0v.js", "sources": ["../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/namespaces.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/namespace.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/creator.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selector.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/select.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/array.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selectorAll.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/selectAll.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/matcher.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/selectChild.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/selectChildren.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/filter.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/sparse.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/enter.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/constant.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/data.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/exit.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/join.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/merge.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/order.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/sort.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/call.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/nodes.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/node.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/size.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/empty.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/each.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/attr.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/window.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/style.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/property.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/classed.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/text.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/html.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/raise.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/lower.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/append.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/insert.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/remove.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/clone.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/datum.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/on.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/dispatch.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/iterator.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/selection/index.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/select.js"], "sourcesContent": ["export var xhtml = \"http://www.w3.org/1999/xhtml\";\n\nexport default {\n  svg: \"http://www.w3.org/2000/svg\",\n  xhtml: xhtml,\n  xlink: \"http://www.w3.org/1999/xlink\",\n  xml: \"http://www.w3.org/XML/1998/namespace\",\n  xmlns: \"http://www.w3.org/2000/xmlns/\"\n};\n", "import namespaces from \"./namespaces.js\";\n\nexport default function(name) {\n  var prefix = name += \"\", i = prefix.indexOf(\":\");\n  if (i >= 0 && (prefix = name.slice(0, i)) !== \"xmlns\") name = name.slice(i + 1);\n  return namespaces.hasOwnProperty(prefix) ? {space: namespaces[prefix], local: name} : name; // eslint-disable-line no-prototype-builtins\n}\n", "import namespace from \"./namespace.js\";\nimport {xhtml} from \"./namespaces.js\";\n\nfunction creatorInherit(name) {\n  return function() {\n    var document = this.ownerDocument,\n        uri = this.namespaceURI;\n    return uri === xhtml && document.documentElement.namespaceURI === xhtml\n        ? document.createElement(name)\n        : document.createElementNS(uri, name);\n  };\n}\n\nfunction creatorFixed(fullname) {\n  return function() {\n    return this.ownerDocument.createElementNS(fullname.space, fullname.local);\n  };\n}\n\nexport default function(name) {\n  var fullname = namespace(name);\n  return (fullname.local\n      ? creatorFixed\n      : creatorInherit)(fullname);\n}\n", "function none() {}\n\nexport default function(selector) {\n  return selector == null ? none : function() {\n    return this.querySelector(selector);\n  };\n}\n", "import {Selection} from \"./index.js\";\nimport selector from \"../selector.js\";\n\nexport default function(select) {\n  if (typeof select !== \"function\") select = selector(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {\n      if ((node = group[i]) && (subnode = select.call(node, node.__data__, i, group))) {\n        if (\"__data__\" in node) subnode.__data__ = node.__data__;\n        subgroup[i] = subnode;\n      }\n    }\n  }\n\n  return new Selection(subgroups, this._parents);\n}\n", "// Given something array like (or null), returns something that is strictly an\n// array. This is used to ensure that array-like objects passed to d3.selectAll\n// or selection.selectAll are converted into proper arrays when creating a\n// selection; we don’t ever want to create a selection backed by a live\n// HTMLCollection or NodeList. However, note that selection.selectAll will use a\n// static NodeList as a group, since it safely derived from querySelectorAll.\nexport default function array(x) {\n  return x == null ? [] : Array.isArray(x) ? x : Array.from(x);\n}\n", "function empty() {\n  return [];\n}\n\nexport default function(selector) {\n  return selector == null ? empty : function() {\n    return this.querySelectorAll(selector);\n  };\n}\n", "import {Selection} from \"./index.js\";\nimport array from \"../array.js\";\nimport selectorAll from \"../selectorAll.js\";\n\nfunction arrayAll(select) {\n  return function() {\n    return array(select.apply(this, arguments));\n  };\n}\n\nexport default function(select) {\n  if (typeof select === \"function\") select = arrayAll(select);\n  else select = selectorAll(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        subgroups.push(select.call(node, node.__data__, i, group));\n        parents.push(node);\n      }\n    }\n  }\n\n  return new Selection(subgroups, parents);\n}\n", "export default function(selector) {\n  return function() {\n    return this.matches(selector);\n  };\n}\n\nexport function childMatcher(selector) {\n  return function(node) {\n    return node.matches(selector);\n  };\n}\n\n", "import {childMatcher} from \"../matcher.js\";\n\nvar find = Array.prototype.find;\n\nfunction childFind(match) {\n  return function() {\n    return find.call(this.children, match);\n  };\n}\n\nfunction childFirst() {\n  return this.firstElementChild;\n}\n\nexport default function(match) {\n  return this.select(match == null ? childFirst\n      : childFind(typeof match === \"function\" ? match : childMatcher(match)));\n}\n", "import {childMatcher} from \"../matcher.js\";\n\nvar filter = Array.prototype.filter;\n\nfunction children() {\n  return Array.from(this.children);\n}\n\nfunction childrenFilter(match) {\n  return function() {\n    return filter.call(this.children, match);\n  };\n}\n\nexport default function(match) {\n  return this.selectAll(match == null ? children\n      : childrenFilter(typeof match === \"function\" ? match : childMatcher(match)));\n}\n", "import {Selection} from \"./index.js\";\nimport matcher from \"../matcher.js\";\n\nexport default function(match) {\n  if (typeof match !== \"function\") match = matcher(match);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {\n      if ((node = group[i]) && match.call(node, node.__data__, i, group)) {\n        subgroup.push(node);\n      }\n    }\n  }\n\n  return new Selection(subgroups, this._parents);\n}\n", "export default function(update) {\n  return new Array(update.length);\n}\n", "import sparse from \"./sparse.js\";\nimport {Selection} from \"./index.js\";\n\nexport default function() {\n  return new Selection(this._enter || this._groups.map(sparse), this._parents);\n}\n\nexport function EnterNode(parent, datum) {\n  this.ownerDocument = parent.ownerDocument;\n  this.namespaceURI = parent.namespaceURI;\n  this._next = null;\n  this._parent = parent;\n  this.__data__ = datum;\n}\n\nEnterNode.prototype = {\n  constructor: EnterNode,\n  appendChild: function(child) { return this._parent.insertBefore(child, this._next); },\n  insertBefore: function(child, next) { return this._parent.insertBefore(child, next); },\n  querySelector: function(selector) { return this._parent.querySelector(selector); },\n  querySelectorAll: function(selector) { return this._parent.querySelectorAll(selector); }\n};\n", "export default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {Selection} from \"./index.js\";\nimport {EnterNode} from \"./enter.js\";\nimport constant from \"../constant.js\";\n\nfunction bindIndex(parent, group, enter, update, exit, data) {\n  var i = 0,\n      node,\n      groupLength = group.length,\n      dataLength = data.length;\n\n  // Put any non-null nodes that fit into update.\n  // Put any null nodes into enter.\n  // Put any remaining data into enter.\n  for (; i < dataLength; ++i) {\n    if (node = group[i]) {\n      node.__data__ = data[i];\n      update[i] = node;\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n\n  // Put any non-null nodes that don’t fit into exit.\n  for (; i < groupLength; ++i) {\n    if (node = group[i]) {\n      exit[i] = node;\n    }\n  }\n}\n\nfunction bindKey(parent, group, enter, update, exit, data, key) {\n  var i,\n      node,\n      nodeByKeyValue = new Map,\n      groupLength = group.length,\n      dataLength = data.length,\n      keyValues = new Array(groupLength),\n      keyValue;\n\n  // Compute the key for each node.\n  // If multiple nodes have the same key, the duplicates are added to exit.\n  for (i = 0; i < groupLength; ++i) {\n    if (node = group[i]) {\n      keyValues[i] = keyValue = key.call(node, node.__data__, i, group) + \"\";\n      if (nodeByKeyValue.has(keyValue)) {\n        exit[i] = node;\n      } else {\n        nodeByKeyValue.set(keyValue, node);\n      }\n    }\n  }\n\n  // Compute the key for each datum.\n  // If there a node associated with this key, join and add it to update.\n  // If there is not (or the key is a duplicate), add it to enter.\n  for (i = 0; i < dataLength; ++i) {\n    keyValue = key.call(parent, data[i], i, data) + \"\";\n    if (node = nodeByKeyValue.get(keyValue)) {\n      update[i] = node;\n      node.__data__ = data[i];\n      nodeByKeyValue.delete(keyValue);\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n\n  // Add any remaining nodes that were not bound to data to exit.\n  for (i = 0; i < groupLength; ++i) {\n    if ((node = group[i]) && (nodeByKeyValue.get(keyValues[i]) === node)) {\n      exit[i] = node;\n    }\n  }\n}\n\nfunction datum(node) {\n  return node.__data__;\n}\n\nexport default function(value, key) {\n  if (!arguments.length) return Array.from(this, datum);\n\n  var bind = key ? bindKey : bindIndex,\n      parents = this._parents,\n      groups = this._groups;\n\n  if (typeof value !== \"function\") value = constant(value);\n\n  for (var m = groups.length, update = new Array(m), enter = new Array(m), exit = new Array(m), j = 0; j < m; ++j) {\n    var parent = parents[j],\n        group = groups[j],\n        groupLength = group.length,\n        data = arraylike(value.call(parent, parent && parent.__data__, j, parents)),\n        dataLength = data.length,\n        enterGroup = enter[j] = new Array(dataLength),\n        updateGroup = update[j] = new Array(dataLength),\n        exitGroup = exit[j] = new Array(groupLength);\n\n    bind(parent, group, enterGroup, updateGroup, exitGroup, data, key);\n\n    // Now connect the enter nodes to their following update node, such that\n    // appendChild can insert the materialized enter node before this node,\n    // rather than at the end of the parent node.\n    for (var i0 = 0, i1 = 0, previous, next; i0 < dataLength; ++i0) {\n      if (previous = enterGroup[i0]) {\n        if (i0 >= i1) i1 = i0 + 1;\n        while (!(next = updateGroup[i1]) && ++i1 < dataLength);\n        previous._next = next || null;\n      }\n    }\n  }\n\n  update = new Selection(update, parents);\n  update._enter = enter;\n  update._exit = exit;\n  return update;\n}\n\n// Given some data, this returns an array-like view of it: an object that\n// exposes a length property and allows numeric indexing. Note that unlike\n// selectAll, this isn’t worried about “live” collections because the resulting\n// array will only be used briefly while data is being bound. (It is possible to\n// cause the data to change while iterating by using a key function, but please\n// don’t; we’d rather avoid a gratuitous copy.)\nfunction arraylike(data) {\n  return typeof data === \"object\" && \"length\" in data\n    ? data // Array, TypedArray, NodeList, array-like\n    : Array.from(data); // Map, Set, iterable, string, or anything else\n}\n", "import sparse from \"./sparse.js\";\nimport {Selection} from \"./index.js\";\n\nexport default function() {\n  return new Selection(this._exit || this._groups.map(sparse), this._parents);\n}\n", "export default function(onenter, onupdate, onexit) {\n  var enter = this.enter(), update = this, exit = this.exit();\n  if (typeof onenter === \"function\") {\n    enter = onenter(enter);\n    if (enter) enter = enter.selection();\n  } else {\n    enter = enter.append(onenter + \"\");\n  }\n  if (onupdate != null) {\n    update = onupdate(update);\n    if (update) update = update.selection();\n  }\n  if (onexit == null) exit.remove(); else onexit(exit);\n  return enter && update ? enter.merge(update).order() : update;\n}\n", "import {Selection} from \"./index.js\";\n\nexport default function(context) {\n  var selection = context.selection ? context.selection() : context;\n\n  for (var groups0 = this._groups, groups1 = selection._groups, m0 = groups0.length, m1 = groups1.length, m = Math.min(m0, m1), merges = new Array(m0), j = 0; j < m; ++j) {\n    for (var group0 = groups0[j], group1 = groups1[j], n = group0.length, merge = merges[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group0[i] || group1[i]) {\n        merge[i] = node;\n      }\n    }\n  }\n\n  for (; j < m0; ++j) {\n    merges[j] = groups0[j];\n  }\n\n  return new Selection(merges, this._parents);\n}\n", "export default function() {\n\n  for (var groups = this._groups, j = -1, m = groups.length; ++j < m;) {\n    for (var group = groups[j], i = group.length - 1, next = group[i], node; --i >= 0;) {\n      if (node = group[i]) {\n        if (next && node.compareDocumentPosition(next) ^ 4) next.parentNode.insertBefore(node, next);\n        next = node;\n      }\n    }\n  }\n\n  return this;\n}\n", "import {Selection} from \"./index.js\";\n\nexport default function(compare) {\n  if (!compare) compare = ascending;\n\n  function compareNode(a, b) {\n    return a && b ? compare(a.__data__, b.__data__) : !a - !b;\n  }\n\n  for (var groups = this._groups, m = groups.length, sortgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, sortgroup = sortgroups[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        sortgroup[i] = node;\n      }\n    }\n    sortgroup.sort(compareNode);\n  }\n\n  return new Selection(sortgroups, this._parents).order();\n}\n\nfunction ascending(a, b) {\n  return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n", "export default function() {\n  var callback = arguments[0];\n  arguments[0] = this;\n  callback.apply(null, arguments);\n  return this;\n}\n", "export default function() {\n  return Array.from(this);\n}\n", "export default function() {\n\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length; i < n; ++i) {\n      var node = group[i];\n      if (node) return node;\n    }\n  }\n\n  return null;\n}\n", "export default function() {\n  let size = 0;\n  for (const node of this) ++size; // eslint-disable-line no-unused-vars\n  return size;\n}\n", "export default function() {\n  return !this.node();\n}\n", "export default function(callback) {\n\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i]) callback.call(node, node.__data__, i, group);\n    }\n  }\n\n  return this;\n}\n", "import namespace from \"../namespace.js\";\n\nfunction attrRemove(name) {\n  return function() {\n    this.removeAttribute(name);\n  };\n}\n\nfunction attrRemoveNS(fullname) {\n  return function() {\n    this.removeAttributeNS(fullname.space, fullname.local);\n  };\n}\n\nfunction attrConstant(name, value) {\n  return function() {\n    this.setAttribute(name, value);\n  };\n}\n\nfunction attrConstantNS(fullname, value) {\n  return function() {\n    this.setAttributeNS(fullname.space, fullname.local, value);\n  };\n}\n\nfunction attrFunction(name, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.removeAttribute(name);\n    else this.setAttribute(name, v);\n  };\n}\n\nfunction attrFunctionNS(fullname, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.removeAttributeNS(fullname.space, fullname.local);\n    else this.setAttributeNS(fullname.space, fullname.local, v);\n  };\n}\n\nexport default function(name, value) {\n  var fullname = namespace(name);\n\n  if (arguments.length < 2) {\n    var node = this.node();\n    return fullname.local\n        ? node.getAttributeNS(fullname.space, fullname.local)\n        : node.getAttribute(fullname);\n  }\n\n  return this.each((value == null\n      ? (fullname.local ? attrRemoveNS : attrRemove) : (typeof value === \"function\"\n      ? (fullname.local ? attrFunctionNS : attrFunction)\n      : (fullname.local ? attrConstantNS : attrConstant)))(fullname, value));\n}\n", "export default function(node) {\n  return (node.ownerDocument && node.ownerDocument.defaultView) // node is a Node\n      || (node.document && node) // node is a Window\n      || node.defaultView; // node is a Document\n}\n", "import defaultView from \"../window.js\";\n\nfunction styleRemove(name) {\n  return function() {\n    this.style.removeProperty(name);\n  };\n}\n\nfunction styleConstant(name, value, priority) {\n  return function() {\n    this.style.setProperty(name, value, priority);\n  };\n}\n\nfunction styleFunction(name, value, priority) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.style.removeProperty(name);\n    else this.style.setProperty(name, v, priority);\n  };\n}\n\nexport default function(name, value, priority) {\n  return arguments.length > 1\n      ? this.each((value == null\n            ? styleRemove : typeof value === \"function\"\n            ? styleFunction\n            : styleConstant)(name, value, priority == null ? \"\" : priority))\n      : styleValue(this.node(), name);\n}\n\nexport function styleValue(node, name) {\n  return node.style.getPropertyValue(name)\n      || defaultView(node).getComputedStyle(node, null).getPropertyValue(name);\n}\n", "function propertyRemove(name) {\n  return function() {\n    delete this[name];\n  };\n}\n\nfunction propertyConstant(name, value) {\n  return function() {\n    this[name] = value;\n  };\n}\n\nfunction propertyFunction(name, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) delete this[name];\n    else this[name] = v;\n  };\n}\n\nexport default function(name, value) {\n  return arguments.length > 1\n      ? this.each((value == null\n          ? propertyRemove : typeof value === \"function\"\n          ? propertyFunction\n          : propertyConstant)(name, value))\n      : this.node()[name];\n}\n", "function classArray(string) {\n  return string.trim().split(/^|\\s+/);\n}\n\nfunction classList(node) {\n  return node.classList || new ClassList(node);\n}\n\nfunction ClassList(node) {\n  this._node = node;\n  this._names = classArray(node.getAttribute(\"class\") || \"\");\n}\n\nClassList.prototype = {\n  add: function(name) {\n    var i = this._names.indexOf(name);\n    if (i < 0) {\n      this._names.push(name);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  remove: function(name) {\n    var i = this._names.indexOf(name);\n    if (i >= 0) {\n      this._names.splice(i, 1);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  contains: function(name) {\n    return this._names.indexOf(name) >= 0;\n  }\n};\n\nfunction classedAdd(node, names) {\n  var list = classList(node), i = -1, n = names.length;\n  while (++i < n) list.add(names[i]);\n}\n\nfunction classedRemove(node, names) {\n  var list = classList(node), i = -1, n = names.length;\n  while (++i < n) list.remove(names[i]);\n}\n\nfunction classedTrue(names) {\n  return function() {\n    classedAdd(this, names);\n  };\n}\n\nfunction classedFalse(names) {\n  return function() {\n    classedRemove(this, names);\n  };\n}\n\nfunction classedFunction(names, value) {\n  return function() {\n    (value.apply(this, arguments) ? classedAdd : classedRemove)(this, names);\n  };\n}\n\nexport default function(name, value) {\n  var names = classArray(name + \"\");\n\n  if (arguments.length < 2) {\n    var list = classList(this.node()), i = -1, n = names.length;\n    while (++i < n) if (!list.contains(names[i])) return false;\n    return true;\n  }\n\n  return this.each((typeof value === \"function\"\n      ? classedFunction : value\n      ? classedTrue\n      : classedFalse)(names, value));\n}\n", "function textRemove() {\n  this.textContent = \"\";\n}\n\nfunction textConstant(value) {\n  return function() {\n    this.textContent = value;\n  };\n}\n\nfunction textFunction(value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    this.textContent = v == null ? \"\" : v;\n  };\n}\n\nexport default function(value) {\n  return arguments.length\n      ? this.each(value == null\n          ? textRemove : (typeof value === \"function\"\n          ? textFunction\n          : textConstant)(value))\n      : this.node().textContent;\n}\n", "function htmlRemove() {\n  this.innerHTML = \"\";\n}\n\nfunction htmlConstant(value) {\n  return function() {\n    this.innerHTML = value;\n  };\n}\n\nfunction htmlFunction(value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    this.innerHTML = v == null ? \"\" : v;\n  };\n}\n\nexport default function(value) {\n  return arguments.length\n      ? this.each(value == null\n          ? htmlRemove : (typeof value === \"function\"\n          ? htmlFunction\n          : htmlConstant)(value))\n      : this.node().innerHTML;\n}\n", "function raise() {\n  if (this.nextSibling) this.parentNode.appendChild(this);\n}\n\nexport default function() {\n  return this.each(raise);\n}\n", "function lower() {\n  if (this.previousSibling) this.parentNode.insertBefore(this, this.parentNode.firstChild);\n}\n\nexport default function() {\n  return this.each(lower);\n}\n", "import creator from \"../creator.js\";\n\nexport default function(name) {\n  var create = typeof name === \"function\" ? name : creator(name);\n  return this.select(function() {\n    return this.appendChild(create.apply(this, arguments));\n  });\n}\n", "import creator from \"../creator.js\";\nimport selector from \"../selector.js\";\n\nfunction constantNull() {\n  return null;\n}\n\nexport default function(name, before) {\n  var create = typeof name === \"function\" ? name : creator(name),\n      select = before == null ? constantNull : typeof before === \"function\" ? before : selector(before);\n  return this.select(function() {\n    return this.insertBefore(create.apply(this, arguments), select.apply(this, arguments) || null);\n  });\n}\n", "function remove() {\n  var parent = this.parentNode;\n  if (parent) parent.removeChild(this);\n}\n\nexport default function() {\n  return this.each(remove);\n}\n", "function selection_cloneShallow() {\n  var clone = this.cloneNode(false), parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\n\nfunction selection_cloneDeep() {\n  var clone = this.cloneNode(true), parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\n\nexport default function(deep) {\n  return this.select(deep ? selection_cloneDeep : selection_cloneShallow);\n}\n", "export default function(value) {\n  return arguments.length\n      ? this.property(\"__data__\", value)\n      : this.node().__data__;\n}\n", "function contextListener(listener) {\n  return function(event) {\n    listener.call(this, event, this.__data__);\n  };\n}\n\nfunction parseTypenames(typenames) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    return {type: t, name: name};\n  });\n}\n\nfunction onRemove(typename) {\n  return function() {\n    var on = this.__on;\n    if (!on) return;\n    for (var j = 0, i = -1, m = on.length, o; j < m; ++j) {\n      if (o = on[j], (!typename.type || o.type === typename.type) && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.options);\n      } else {\n        on[++i] = o;\n      }\n    }\n    if (++i) on.length = i;\n    else delete this.__on;\n  };\n}\n\nfunction onAdd(typename, value, options) {\n  return function() {\n    var on = this.__on, o, listener = contextListener(value);\n    if (on) for (var j = 0, m = on.length; j < m; ++j) {\n      if ((o = on[j]).type === typename.type && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.options);\n        this.addEventListener(o.type, o.listener = listener, o.options = options);\n        o.value = value;\n        return;\n      }\n    }\n    this.addEventListener(typename.type, listener, options);\n    o = {type: typename.type, name: typename.name, value: value, listener: listener, options: options};\n    if (!on) this.__on = [o];\n    else on.push(o);\n  };\n}\n\nexport default function(typename, value, options) {\n  var typenames = parseTypenames(typename + \"\"), i, n = typenames.length, t;\n\n  if (arguments.length < 2) {\n    var on = this.node().__on;\n    if (on) for (var j = 0, m = on.length, o; j < m; ++j) {\n      for (i = 0, o = on[j]; i < n; ++i) {\n        if ((t = typenames[i]).type === o.type && t.name === o.name) {\n          return o.value;\n        }\n      }\n    }\n    return;\n  }\n\n  on = value ? onAdd : onRemove;\n  for (i = 0; i < n; ++i) this.each(on(typenames[i], value, options));\n  return this;\n}\n", "import defaultView from \"../window.js\";\n\nfunction dispatchEvent(node, type, params) {\n  var window = defaultView(node),\n      event = window.CustomEvent;\n\n  if (typeof event === \"function\") {\n    event = new event(type, params);\n  } else {\n    event = window.document.createEvent(\"Event\");\n    if (params) event.initEvent(type, params.bubbles, params.cancelable), event.detail = params.detail;\n    else event.initEvent(type, false, false);\n  }\n\n  node.dispatchEvent(event);\n}\n\nfunction dispatchConstant(type, params) {\n  return function() {\n    return dispatchEvent(this, type, params);\n  };\n}\n\nfunction dispatchFunction(type, params) {\n  return function() {\n    return dispatchEvent(this, type, params.apply(this, arguments));\n  };\n}\n\nexport default function(type, params) {\n  return this.each((typeof params === \"function\"\n      ? dispatchFunction\n      : dispatchConstant)(type, params));\n}\n", "export default function*() {\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i]) yield node;\n    }\n  }\n}\n", "import selection_select from \"./select.js\";\nimport selection_selectAll from \"./selectAll.js\";\nimport selection_selectChild from \"./selectChild.js\";\nimport selection_selectChildren from \"./selectChildren.js\";\nimport selection_filter from \"./filter.js\";\nimport selection_data from \"./data.js\";\nimport selection_enter from \"./enter.js\";\nimport selection_exit from \"./exit.js\";\nimport selection_join from \"./join.js\";\nimport selection_merge from \"./merge.js\";\nimport selection_order from \"./order.js\";\nimport selection_sort from \"./sort.js\";\nimport selection_call from \"./call.js\";\nimport selection_nodes from \"./nodes.js\";\nimport selection_node from \"./node.js\";\nimport selection_size from \"./size.js\";\nimport selection_empty from \"./empty.js\";\nimport selection_each from \"./each.js\";\nimport selection_attr from \"./attr.js\";\nimport selection_style from \"./style.js\";\nimport selection_property from \"./property.js\";\nimport selection_classed from \"./classed.js\";\nimport selection_text from \"./text.js\";\nimport selection_html from \"./html.js\";\nimport selection_raise from \"./raise.js\";\nimport selection_lower from \"./lower.js\";\nimport selection_append from \"./append.js\";\nimport selection_insert from \"./insert.js\";\nimport selection_remove from \"./remove.js\";\nimport selection_clone from \"./clone.js\";\nimport selection_datum from \"./datum.js\";\nimport selection_on from \"./on.js\";\nimport selection_dispatch from \"./dispatch.js\";\nimport selection_iterator from \"./iterator.js\";\n\nexport var root = [null];\n\nexport function Selection(groups, parents) {\n  this._groups = groups;\n  this._parents = parents;\n}\n\nfunction selection() {\n  return new Selection([[document.documentElement]], root);\n}\n\nfunction selection_selection() {\n  return this;\n}\n\nSelection.prototype = selection.prototype = {\n  constructor: Selection,\n  select: selection_select,\n  selectAll: selection_selectAll,\n  selectChild: selection_selectChild,\n  selectChildren: selection_selectChildren,\n  filter: selection_filter,\n  data: selection_data,\n  enter: selection_enter,\n  exit: selection_exit,\n  join: selection_join,\n  merge: selection_merge,\n  selection: selection_selection,\n  order: selection_order,\n  sort: selection_sort,\n  call: selection_call,\n  nodes: selection_nodes,\n  node: selection_node,\n  size: selection_size,\n  empty: selection_empty,\n  each: selection_each,\n  attr: selection_attr,\n  style: selection_style,\n  property: selection_property,\n  classed: selection_classed,\n  text: selection_text,\n  html: selection_html,\n  raise: selection_raise,\n  lower: selection_lower,\n  append: selection_append,\n  insert: selection_insert,\n  remove: selection_remove,\n  clone: selection_clone,\n  datum: selection_datum,\n  on: selection_on,\n  dispatch: selection_dispatch,\n  [Symbol.iterator]: selection_iterator\n};\n\nexport default selection;\n", "import {Selection, root} from \"./selection/index.js\";\n\nexport default function(selector) {\n  return typeof selector === \"string\"\n      ? new Selection([[document.querySelector(selector)]], [document.documentElement])\n      : new Selection([[selector]], root);\n}\n"], "names": ["xhtml", "namespaces", "namespace", "name", "prefix", "i", "creator<PERSON><PERSON><PERSON><PERSON>", "document", "uri", "creatorFixed", "fullname", "creator", "none", "selector", "selection_select", "select", "groups", "m", "subgroups", "j", "group", "n", "subgroup", "node", "subnode", "Selection", "array", "x", "empty", "selectorAll", "arrayAll", "selection_selectAll", "parents", "matcher", "child<PERSON><PERSON><PERSON>", "find", "child<PERSON><PERSON>", "match", "<PERSON><PERSON><PERSON><PERSON>", "selection_select<PERSON><PERSON>d", "filter", "children", "childrenFilter", "selection_select<PERSON><PERSON><PERSON><PERSON>", "selection_filter", "sparse", "update", "selection_enter", "EnterNode", "parent", "datum", "child", "next", "constant", "bindIndex", "enter", "exit", "data", "groupLength", "dataLength", "<PERSON><PERSON><PERSON>", "key", "nodeByKeyValue", "keyV<PERSON><PERSON>", "keyValue", "selection_data", "value", "bind", "arraylike", "enterGroup", "updateGroup", "exitGroup", "i0", "i1", "previous", "selection_exit", "selection_join", "onenter", "onupdate", "onexit", "selection_merge", "context", "selection", "groups0", "groups1", "m0", "m1", "merges", "group0", "group1", "merge", "selection_order", "selection_sort", "compare", "ascending", "compareNode", "a", "b", "sortgroups", "sortgroup", "selection_call", "callback", "selection_nodes", "selection_node", "selection_size", "size", "selection_empty", "selection_each", "attrRemove", "attrRemoveNS", "attrConstant", "attrConstantNS", "attrFunction", "v", "attrFunctionNS", "selection_attr", "defaultView", "styleRemove", "styleConstant", "priority", "styleFunction", "selection_style", "styleValue", "propertyRemove", "propertyConstant", "propertyFunction", "selection_property", "classArray", "string", "classList", "ClassList", "classedAdd", "names", "list", "classedRemove", "classedTrue", "classedFalse", "classedFunction", "selection_classed", "textRemove", "textConstant", "textFunction", "selection_text", "htmlRemove", "htmlConstant", "htmlFunction", "selection_html", "raise", "selection_raise", "lower", "selection_lower", "selection_append", "create", "constant<PERSON><PERSON>", "selection_insert", "before", "remove", "selection_remove", "selection_cloneShallow", "clone", "selection_cloneDeep", "selection_clone", "deep", "selection_datum", "contextListener", "listener", "event", "parseTypenames", "typenames", "t", "onRemove", "typename", "on", "o", "onAdd", "options", "selection_on", "dispatchEvent", "type", "params", "window", "dispatchConstant", "dispatchFunction", "selection_dispatch", "selection_iterator", "root", "selection_selection"], "mappings": "AAAO,IAAIA,EAAQ,+BAEnB,MAAeC,EAAA,CACb,IAAK,6BACL,MAAOD,EACP,MAAO,+BACP,IAAK,uCACL,MAAO,+BACT,ECNe,SAAQE,EAACC,EAAM,CAC5B,IAAIC,EAASD,GAAQ,GAAIE,EAAID,EAAO,QAAQ,GAAG,EAC/C,OAAIC,GAAK,IAAMD,EAASD,EAAK,MAAM,EAAGE,CAAC,KAAO,UAASF,EAAOA,EAAK,MAAME,EAAI,CAAC,GACvEJ,EAAW,eAAeG,CAAM,EAAI,CAAC,MAAOH,EAAWG,CAAM,EAAG,MAAOD,CAAI,EAAIA,CACxF,CCHA,SAASG,EAAeH,EAAM,CAC5B,OAAO,UAAW,CAChB,IAAII,EAAW,KAAK,cAChBC,EAAM,KAAK,aACf,OAAOA,IAAQR,GAASO,EAAS,gBAAgB,eAAiBP,EAC5DO,EAAS,cAAcJ,CAAI,EAC3BI,EAAS,gBAAgBC,EAAKL,CAAI,CAC5C,CACA,CAEA,SAASM,EAAaC,EAAU,CAC9B,OAAO,UAAW,CAChB,OAAO,KAAK,cAAc,gBAAgBA,EAAS,MAAOA,EAAS,KAAK,CAC5E,CACA,CAEe,SAAQC,EAACR,EAAM,CAC5B,IAAIO,EAAWR,EAAUC,CAAI,EAC7B,OAAQO,EAAS,MACXD,EACAH,GAAgBI,CAAQ,CAChC,CCxBA,SAASE,GAAO,CAAE,CAEH,SAAQC,EAACA,EAAU,CAChC,OAAOA,GAAY,KAAOD,EAAO,UAAW,CAC1C,OAAO,KAAK,cAAcC,CAAQ,CACtC,CACA,CCHe,SAAQC,EAACC,EAAQ,CAC1B,OAAOA,GAAW,aAAYA,EAASF,EAASE,CAAM,GAE1D,QAASC,EAAS,KAAK,QAASC,EAAID,EAAO,OAAQE,EAAY,IAAI,MAAMD,CAAC,EAAGE,EAAI,EAAGA,EAAIF,EAAG,EAAEE,EAC3F,QAASC,EAAQJ,EAAOG,CAAC,EAAGE,EAAID,EAAM,OAAQE,EAAWJ,EAAUC,CAAC,EAAI,IAAI,MAAME,CAAC,EAAGE,EAAMC,EAASnB,EAAI,EAAGA,EAAIgB,EAAG,EAAEhB,GAC9GkB,EAAOH,EAAMf,CAAC,KAAOmB,EAAUT,EAAO,KAAKQ,EAAMA,EAAK,SAAUlB,EAAGe,CAAK,KACvE,aAAcG,IAAMC,EAAQ,SAAWD,EAAK,UAChDD,EAASjB,CAAC,EAAImB,GAKpB,OAAO,IAAIC,EAAUP,EAAW,KAAK,QAAQ,CAC/C,CCVe,SAASQ,EAAMC,EAAG,CAC/B,OAAOA,GAAK,KAAO,CAAE,EAAG,MAAM,QAAQA,CAAC,EAAIA,EAAI,MAAM,KAAKA,CAAC,CAC7D,CCRA,SAASC,GAAQ,CACf,MAAO,EACT,CAEe,SAAQC,EAAChB,EAAU,CAChC,OAAOA,GAAY,KAAOe,EAAQ,UAAW,CAC3C,OAAO,KAAK,iBAAiBf,CAAQ,CACzC,CACA,CCJA,SAASiB,EAASf,EAAQ,CACxB,OAAO,UAAW,CAChB,OAAOW,EAAMX,EAAO,MAAM,KAAM,SAAS,CAAC,CAC9C,CACA,CAEe,SAAQgB,EAAChB,EAAQ,CAC1B,OAAOA,GAAW,WAAYA,EAASe,EAASf,CAAM,EACrDA,EAASc,EAAYd,CAAM,EAEhC,QAASC,EAAS,KAAK,QAASC,EAAID,EAAO,OAAQE,EAAY,CAAE,EAAEc,EAAU,CAAE,EAAEb,EAAI,EAAGA,EAAIF,EAAG,EAAEE,EAC/F,QAASC,EAAQJ,EAAOG,CAAC,EAAGE,EAAID,EAAM,OAAQG,EAAMlB,EAAI,EAAGA,EAAIgB,EAAG,EAAEhB,GAC9DkB,EAAOH,EAAMf,CAAC,KAChBa,EAAU,KAAKH,EAAO,KAAKQ,EAAMA,EAAK,SAAUlB,EAAGe,CAAK,CAAC,EACzDY,EAAQ,KAAKT,CAAI,GAKvB,OAAO,IAAIE,EAAUP,EAAWc,CAAO,CACzC,CCxBe,SAAQC,EAACpB,EAAU,CAChC,OAAO,UAAW,CAChB,OAAO,KAAK,QAAQA,CAAQ,CAChC,CACA,CAEO,SAASqB,EAAarB,EAAU,CACrC,OAAO,SAASU,EAAM,CACpB,OAAOA,EAAK,QAAQV,CAAQ,CAChC,CACA,CCRA,IAAIsB,EAAO,MAAM,UAAU,KAE3B,SAASC,EAAUC,EAAO,CACxB,OAAO,UAAW,CAChB,OAAOF,EAAK,KAAK,KAAK,SAAUE,CAAK,CACzC,CACA,CAEA,SAASC,GAAa,CACpB,OAAO,KAAK,iBACd,CAEe,SAAQC,EAACF,EAAO,CAC7B,OAAO,KAAK,OAAOA,GAAS,KAAOC,EAC7BF,EAAU,OAAOC,GAAU,WAAaA,EAAQH,EAAaG,CAAK,CAAC,CAAC,CAC5E,CCfA,IAAIG,EAAS,MAAM,UAAU,OAE7B,SAASC,IAAW,CAClB,OAAO,MAAM,KAAK,KAAK,QAAQ,CACjC,CAEA,SAASC,GAAeL,EAAO,CAC7B,OAAO,UAAW,CAChB,OAAOG,EAAO,KAAK,KAAK,SAAUH,CAAK,CAC3C,CACA,CAEe,SAAQM,GAACN,EAAO,CAC7B,OAAO,KAAK,UAAUA,GAAS,KAAOI,GAChCC,GAAe,OAAOL,GAAU,WAAaA,EAAQH,EAAaG,CAAK,CAAC,CAAC,CACjF,CCde,SAAQO,GAACP,EAAO,CACzB,OAAOA,GAAU,aAAYA,EAAQJ,EAAQI,CAAK,GAEtD,QAASrB,EAAS,KAAK,QAASC,EAAID,EAAO,OAAQE,EAAY,IAAI,MAAMD,CAAC,EAAGE,EAAI,EAAGA,EAAIF,EAAG,EAAEE,EAC3F,QAASC,EAAQJ,EAAOG,CAAC,EAAGE,EAAID,EAAM,OAAQE,EAAWJ,EAAUC,CAAC,EAAI,CAAA,EAAII,EAAMlB,EAAI,EAAGA,EAAIgB,EAAG,EAAEhB,GAC3FkB,EAAOH,EAAMf,CAAC,IAAMgC,EAAM,KAAKd,EAAMA,EAAK,SAAUlB,EAAGe,CAAK,GAC/DE,EAAS,KAAKC,CAAI,EAKxB,OAAO,IAAIE,EAAUP,EAAW,KAAK,QAAQ,CAC/C,CCfe,SAAQ2B,EAACC,EAAQ,CAC9B,OAAO,IAAI,MAAMA,EAAO,MAAM,CAChC,CCCe,SAAAC,IAAW,CACxB,OAAO,IAAItB,EAAU,KAAK,QAAU,KAAK,QAAQ,IAAIoB,CAAM,EAAG,KAAK,QAAQ,CAC7E,CAEO,SAASG,EAAUC,EAAQC,EAAO,CACvC,KAAK,cAAgBD,EAAO,cAC5B,KAAK,aAAeA,EAAO,aAC3B,KAAK,MAAQ,KACb,KAAK,QAAUA,EACf,KAAK,SAAWC,CAClB,CAEAF,EAAU,UAAY,CACpB,YAAaA,EACb,YAAa,SAASG,EAAO,CAAE,OAAO,KAAK,QAAQ,aAAaA,EAAO,KAAK,KAAK,CAAI,EACrF,aAAc,SAASA,EAAOC,EAAM,CAAE,OAAO,KAAK,QAAQ,aAAaD,EAAOC,CAAI,CAAI,EACtF,cAAe,SAASvC,EAAU,CAAE,OAAO,KAAK,QAAQ,cAAcA,CAAQ,CAAI,EAClF,iBAAkB,SAASA,EAAU,CAAE,OAAO,KAAK,QAAQ,iBAAiBA,CAAQ,CAAI,CAC1F,ECrBe,SAAQwC,GAAC1B,EAAG,CACzB,OAAO,UAAW,CAChB,OAAOA,CACX,CACA,CCAA,SAAS2B,GAAUL,EAAQ7B,EAAOmC,EAAOT,EAAQU,EAAMC,EAAM,CAS3D,QARIpD,EAAI,EACJkB,EACAmC,EAActC,EAAM,OACpBuC,EAAaF,EAAK,OAKfpD,EAAIsD,EAAY,EAAEtD,GACnBkB,EAAOH,EAAMf,CAAC,IAChBkB,EAAK,SAAWkC,EAAKpD,CAAC,EACtByC,EAAOzC,CAAC,EAAIkB,GAEZgC,EAAMlD,CAAC,EAAI,IAAI2C,EAAUC,EAAQQ,EAAKpD,CAAC,CAAC,EAK5C,KAAOA,EAAIqD,EAAa,EAAErD,GACpBkB,EAAOH,EAAMf,CAAC,KAChBmD,EAAKnD,CAAC,EAAIkB,EAGhB,CAEA,SAASqC,GAAQX,EAAQ7B,EAAOmC,EAAOT,EAAQU,EAAMC,EAAMI,EAAK,CAC9D,IAAIxD,EACAkB,EACAuC,EAAiB,IAAI,IACrBJ,EAActC,EAAM,OACpBuC,EAAaF,EAAK,OAClBM,EAAY,IAAI,MAAML,CAAW,EACjCM,EAIJ,IAAK3D,EAAI,EAAGA,EAAIqD,EAAa,EAAErD,GACzBkB,EAAOH,EAAMf,CAAC,KAChB0D,EAAU1D,CAAC,EAAI2D,EAAWH,EAAI,KAAKtC,EAAMA,EAAK,SAAUlB,EAAGe,CAAK,EAAI,GAChE0C,EAAe,IAAIE,CAAQ,EAC7BR,EAAKnD,CAAC,EAAIkB,EAEVuC,EAAe,IAAIE,EAAUzC,CAAI,GAQvC,IAAKlB,EAAI,EAAGA,EAAIsD,EAAY,EAAEtD,EAC5B2D,EAAWH,EAAI,KAAKZ,EAAQQ,EAAKpD,CAAC,EAAGA,EAAGoD,CAAI,EAAI,IAC5ClC,EAAOuC,EAAe,IAAIE,CAAQ,IACpClB,EAAOzC,CAAC,EAAIkB,EACZA,EAAK,SAAWkC,EAAKpD,CAAC,EACtByD,EAAe,OAAOE,CAAQ,GAE9BT,EAAMlD,CAAC,EAAI,IAAI2C,EAAUC,EAAQQ,EAAKpD,CAAC,CAAC,EAK5C,IAAKA,EAAI,EAAGA,EAAIqD,EAAa,EAAErD,GACxBkB,EAAOH,EAAMf,CAAC,IAAOyD,EAAe,IAAIC,EAAU1D,CAAC,CAAC,IAAMkB,IAC7DiC,EAAKnD,CAAC,EAAIkB,EAGhB,CAEA,SAAS2B,GAAM3B,EAAM,CACnB,OAAOA,EAAK,QACd,CAEe,SAAA0C,GAASC,EAAOL,EAAK,CAClC,GAAI,CAAC,UAAU,OAAQ,OAAO,MAAM,KAAK,KAAMX,EAAK,EAEpD,IAAIiB,EAAON,EAAMD,GAAUN,GACvBtB,EAAU,KAAK,SACfhB,EAAS,KAAK,QAEd,OAAOkD,GAAU,aAAYA,EAAQb,GAASa,CAAK,GAEvD,QAASjD,EAAID,EAAO,OAAQ8B,EAAS,IAAI,MAAM7B,CAAC,EAAGsC,EAAQ,IAAI,MAAMtC,CAAC,EAAGuC,EAAO,IAAI,MAAMvC,CAAC,EAAGE,EAAI,EAAGA,EAAIF,EAAG,EAAEE,EAAG,CAC/G,IAAI8B,EAASjB,EAAQb,CAAC,EAClBC,EAAQJ,EAAOG,CAAC,EAChBuC,EAActC,EAAM,OACpBqC,EAAOW,GAAUF,EAAM,KAAKjB,EAAQA,GAAUA,EAAO,SAAU9B,EAAGa,CAAO,CAAC,EAC1E2B,EAAaF,EAAK,OAClBY,EAAad,EAAMpC,CAAC,EAAI,IAAI,MAAMwC,CAAU,EAC5CW,EAAcxB,EAAO3B,CAAC,EAAI,IAAI,MAAMwC,CAAU,EAC9CY,EAAYf,EAAKrC,CAAC,EAAI,IAAI,MAAMuC,CAAW,EAE/CS,EAAKlB,EAAQ7B,EAAOiD,EAAYC,EAAaC,EAAWd,EAAMI,CAAG,EAKjE,QAASW,EAAK,EAAGC,EAAK,EAAGC,EAAUtB,EAAMoB,EAAKb,EAAY,EAAEa,EAC1D,GAAIE,EAAWL,EAAWG,CAAE,EAAG,CAE7B,IADIA,GAAMC,IAAIA,EAAKD,EAAK,GACjB,EAAEpB,EAAOkB,EAAYG,CAAE,IAAM,EAAEA,EAAKd,GAAW,CACtDe,EAAS,MAAQtB,GAAQ,IAC1B,CAEJ,CAED,OAAAN,EAAS,IAAIrB,EAAUqB,EAAQd,CAAO,EACtCc,EAAO,OAASS,EAChBT,EAAO,MAAQU,EACRV,CACT,CAQA,SAASsB,GAAUX,EAAM,CACvB,OAAO,OAAOA,GAAS,UAAY,WAAYA,EAC3CA,EACA,MAAM,KAAKA,CAAI,CACrB,CC5He,SAAAkB,IAAW,CACxB,OAAO,IAAIlD,EAAU,KAAK,OAAS,KAAK,QAAQ,IAAIoB,CAAM,EAAG,KAAK,QAAQ,CAC5E,CCLe,SAAA+B,GAASC,EAASC,EAAUC,EAAQ,CACjD,IAAIxB,EAAQ,KAAK,QAAST,EAAS,KAAMU,EAAO,KAAK,OACrD,OAAI,OAAOqB,GAAY,YACrBtB,EAAQsB,EAAQtB,CAAK,EACjBA,IAAOA,EAAQA,EAAM,UAAS,IAElCA,EAAQA,EAAM,OAAOsB,EAAU,EAAE,EAE/BC,GAAY,OACdhC,EAASgC,EAAShC,CAAM,EACpBA,IAAQA,EAASA,EAAO,UAAS,IAEnCiC,GAAU,KAAMvB,EAAK,OAAM,EAASuB,EAAOvB,CAAI,EAC5CD,GAAST,EAASS,EAAM,MAAMT,CAAM,EAAE,MAAO,EAAGA,CACzD,CCZe,SAAQkC,GAACC,EAAS,CAG/B,QAFIC,EAAYD,EAAQ,UAAYA,EAAQ,UAAW,EAAGA,EAEjDE,EAAU,KAAK,QAASC,EAAUF,EAAU,QAASG,EAAKF,EAAQ,OAAQG,EAAKF,EAAQ,OAAQnE,EAAI,KAAK,IAAIoE,EAAIC,CAAE,EAAGC,EAAS,IAAI,MAAMF,CAAE,EAAGlE,EAAI,EAAGA,EAAIF,EAAG,EAAEE,EACpK,QAASqE,EAASL,EAAQhE,CAAC,EAAGsE,EAASL,EAAQjE,CAAC,EAAGE,EAAImE,EAAO,OAAQE,EAAQH,EAAOpE,CAAC,EAAI,IAAI,MAAME,CAAC,EAAGE,EAAMlB,EAAI,EAAGA,EAAIgB,EAAG,EAAEhB,GACxHkB,EAAOiE,EAAOnF,CAAC,GAAKoF,EAAOpF,CAAC,KAC9BqF,EAAMrF,CAAC,EAAIkB,GAKjB,KAAOJ,EAAIkE,EAAI,EAAElE,EACfoE,EAAOpE,CAAC,EAAIgE,EAAQhE,CAAC,EAGvB,OAAO,IAAIM,EAAU8D,EAAQ,KAAK,QAAQ,CAC5C,CClBe,SAAAI,IAAW,CAExB,QAAS3E,EAAS,KAAK,QAASG,EAAI,GAAIF,EAAID,EAAO,OAAQ,EAAEG,EAAIF,GAC/D,QAASG,EAAQJ,EAAOG,CAAC,EAAGd,EAAIe,EAAM,OAAS,EAAGgC,EAAOhC,EAAMf,CAAC,EAAGkB,EAAM,EAAElB,GAAK,IAC1EkB,EAAOH,EAAMf,CAAC,KACZ+C,GAAQ7B,EAAK,wBAAwB6B,CAAI,EAAI,GAAGA,EAAK,WAAW,aAAa7B,EAAM6B,CAAI,EAC3FA,EAAO7B,GAKb,OAAO,IACT,CCVe,SAAQqE,GAACC,EAAS,CAC1BA,IAASA,EAAUC,IAExB,SAASC,EAAYC,EAAGC,EAAG,CACzB,OAAOD,GAAKC,EAAIJ,EAAQG,EAAE,SAAUC,EAAE,QAAQ,EAAI,CAACD,EAAI,CAACC,CACzD,CAED,QAASjF,EAAS,KAAK,QAASC,EAAID,EAAO,OAAQkF,EAAa,IAAI,MAAMjF,CAAC,EAAGE,EAAI,EAAGA,EAAIF,EAAG,EAAEE,EAAG,CAC/F,QAASC,EAAQJ,EAAOG,CAAC,EAAGE,EAAID,EAAM,OAAQ+E,EAAYD,EAAW/E,CAAC,EAAI,IAAI,MAAME,CAAC,EAAGE,EAAMlB,EAAI,EAAGA,EAAIgB,EAAG,EAAEhB,GACxGkB,EAAOH,EAAMf,CAAC,KAChB8F,EAAU9F,CAAC,EAAIkB,GAGnB4E,EAAU,KAAKJ,CAAW,CAC3B,CAED,OAAO,IAAItE,EAAUyE,EAAY,KAAK,QAAQ,EAAE,OAClD,CAEA,SAASJ,GAAUE,EAAGC,EAAG,CACvB,OAAOD,EAAIC,EAAI,GAAKD,EAAIC,EAAI,EAAID,GAAKC,EAAI,EAAI,GAC/C,CCvBe,SAAAG,IAAW,CACxB,IAAIC,EAAW,UAAU,CAAC,EAC1B,iBAAU,CAAC,EAAI,KACfA,EAAS,MAAM,KAAM,SAAS,EACvB,IACT,CCLe,SAAAC,IAAW,CACxB,OAAO,MAAM,KAAK,IAAI,CACxB,CCFe,SAAAC,IAAW,CAExB,QAASvF,EAAS,KAAK,QAASG,EAAI,EAAGF,EAAID,EAAO,OAAQG,EAAIF,EAAG,EAAEE,EACjE,QAASC,EAAQJ,EAAOG,CAAC,EAAGd,EAAI,EAAGgB,EAAID,EAAM,OAAQf,EAAIgB,EAAG,EAAEhB,EAAG,CAC/D,IAAIkB,EAAOH,EAAMf,CAAC,EAClB,GAAIkB,EAAM,OAAOA,CAClB,CAGH,OAAO,IACT,CCVe,SAAAiF,IAAW,CACxB,IAAIC,EAAO,EACX,UAAWlF,KAAQ,KAAM,EAAEkF,EAC3B,OAAOA,CACT,CCJe,SAAAC,IAAW,CACxB,MAAO,CAAC,KAAK,MACf,CCFe,SAAQC,GAACN,EAAU,CAEhC,QAASrF,EAAS,KAAK,QAASG,EAAI,EAAGF,EAAID,EAAO,OAAQG,EAAIF,EAAG,EAAEE,EACjE,QAASC,EAAQJ,EAAOG,CAAC,EAAGd,EAAI,EAAGgB,EAAID,EAAM,OAAQG,EAAMlB,EAAIgB,EAAG,EAAEhB,GAC9DkB,EAAOH,EAAMf,CAAC,IAAGgG,EAAS,KAAK9E,EAAMA,EAAK,SAAUlB,EAAGe,CAAK,EAIpE,OAAO,IACT,CCPA,SAASwF,GAAWzG,EAAM,CACxB,OAAO,UAAW,CAChB,KAAK,gBAAgBA,CAAI,CAC7B,CACA,CAEA,SAAS0G,GAAanG,EAAU,CAC9B,OAAO,UAAW,CAChB,KAAK,kBAAkBA,EAAS,MAAOA,EAAS,KAAK,CACzD,CACA,CAEA,SAASoG,GAAa3G,EAAM+D,EAAO,CACjC,OAAO,UAAW,CAChB,KAAK,aAAa/D,EAAM+D,CAAK,CACjC,CACA,CAEA,SAAS6C,GAAerG,EAAUwD,EAAO,CACvC,OAAO,UAAW,CAChB,KAAK,eAAexD,EAAS,MAAOA,EAAS,MAAOwD,CAAK,CAC7D,CACA,CAEA,SAAS8C,GAAa7G,EAAM+D,EAAO,CACjC,OAAO,UAAW,CAChB,IAAI+C,EAAI/C,EAAM,MAAM,KAAM,SAAS,EAC/B+C,GAAK,KAAM,KAAK,gBAAgB9G,CAAI,EACnC,KAAK,aAAaA,EAAM8G,CAAC,CAClC,CACA,CAEA,SAASC,GAAexG,EAAUwD,EAAO,CACvC,OAAO,UAAW,CAChB,IAAI+C,EAAI/C,EAAM,MAAM,KAAM,SAAS,EAC/B+C,GAAK,KAAM,KAAK,kBAAkBvG,EAAS,MAAOA,EAAS,KAAK,EAC/D,KAAK,eAAeA,EAAS,MAAOA,EAAS,MAAOuG,CAAC,CAC9D,CACA,CAEe,SAAAE,GAAShH,EAAM+D,EAAO,CACnC,IAAIxD,EAAWR,EAAUC,CAAI,EAE7B,GAAI,UAAU,OAAS,EAAG,CACxB,IAAIoB,EAAO,KAAK,OAChB,OAAOb,EAAS,MACVa,EAAK,eAAeb,EAAS,MAAOA,EAAS,KAAK,EAClDa,EAAK,aAAab,CAAQ,CACjC,CAED,OAAO,KAAK,MAAMwD,GAAS,KACpBxD,EAAS,MAAQmG,GAAeD,GAAe,OAAO1C,GAAU,WAChExD,EAAS,MAAQwG,GAAiBF,GAClCtG,EAAS,MAAQqG,GAAiBD,IAAgBpG,EAAUwD,CAAK,CAAC,CAC3E,CCxDe,SAAQkD,EAAC7F,EAAM,CAC5B,OAAQA,EAAK,eAAiBA,EAAK,cAAc,aACzCA,EAAK,UAAYA,GAClBA,EAAK,WACd,CCFA,SAAS8F,GAAYlH,EAAM,CACzB,OAAO,UAAW,CAChB,KAAK,MAAM,eAAeA,CAAI,CAClC,CACA,CAEA,SAASmH,GAAcnH,EAAM+D,EAAOqD,EAAU,CAC5C,OAAO,UAAW,CAChB,KAAK,MAAM,YAAYpH,EAAM+D,EAAOqD,CAAQ,CAChD,CACA,CAEA,SAASC,GAAcrH,EAAM+D,EAAOqD,EAAU,CAC5C,OAAO,UAAW,CAChB,IAAIN,EAAI/C,EAAM,MAAM,KAAM,SAAS,EAC/B+C,GAAK,KAAM,KAAK,MAAM,eAAe9G,CAAI,EACxC,KAAK,MAAM,YAAYA,EAAM8G,EAAGM,CAAQ,CACjD,CACA,CAEe,SAAAE,GAAStH,EAAM+D,EAAOqD,EAAU,CAC7C,OAAO,UAAU,OAAS,EACpB,KAAK,MAAMrD,GAAS,KACdmD,GAAc,OAAOnD,GAAU,WAC/BsD,GACAF,IAAenH,EAAM+D,EAAOqD,GAAmB,EAAa,CAAC,EACnEG,GAAW,KAAK,KAAM,EAAEvH,CAAI,CACpC,CAEO,SAASuH,GAAWnG,EAAMpB,EAAM,CACrC,OAAOoB,EAAK,MAAM,iBAAiBpB,CAAI,GAChCiH,EAAY7F,CAAI,EAAE,iBAAiBA,EAAM,IAAI,EAAE,iBAAiBpB,CAAI,CAC7E,CClCA,SAASwH,GAAexH,EAAM,CAC5B,OAAO,UAAW,CAChB,OAAO,KAAKA,CAAI,CACpB,CACA,CAEA,SAASyH,GAAiBzH,EAAM+D,EAAO,CACrC,OAAO,UAAW,CAChB,KAAK/D,CAAI,EAAI+D,CACjB,CACA,CAEA,SAAS2D,GAAiB1H,EAAM+D,EAAO,CACrC,OAAO,UAAW,CAChB,IAAI+C,EAAI/C,EAAM,MAAM,KAAM,SAAS,EAC/B+C,GAAK,KAAM,OAAO,KAAK9G,CAAI,EAC1B,KAAKA,CAAI,EAAI8G,CACtB,CACA,CAEe,SAAAa,GAAS3H,EAAM+D,EAAO,CACnC,OAAO,UAAU,OAAS,EACpB,KAAK,MAAMA,GAAS,KAChByD,GAAiB,OAAOzD,GAAU,WAClC2D,GACAD,IAAkBzH,EAAM+D,CAAK,CAAC,EAClC,KAAK,OAAO/D,CAAI,CACxB,CC3BA,SAAS4H,EAAWC,EAAQ,CAC1B,OAAOA,EAAO,KAAI,EAAG,MAAM,OAAO,CACpC,CAEA,SAASC,EAAU1G,EAAM,CACvB,OAAOA,EAAK,WAAa,IAAI2G,EAAU3G,CAAI,CAC7C,CAEA,SAAS2G,EAAU3G,EAAM,CACvB,KAAK,MAAQA,EACb,KAAK,OAASwG,EAAWxG,EAAK,aAAa,OAAO,GAAK,EAAE,CAC3D,CAEA2G,EAAU,UAAY,CACpB,IAAK,SAAS/H,EAAM,CAClB,IAAIE,EAAI,KAAK,OAAO,QAAQF,CAAI,EAC5BE,EAAI,IACN,KAAK,OAAO,KAAKF,CAAI,EACrB,KAAK,MAAM,aAAa,QAAS,KAAK,OAAO,KAAK,GAAG,CAAC,EAEzD,EACD,OAAQ,SAASA,EAAM,CACrB,IAAIE,EAAI,KAAK,OAAO,QAAQF,CAAI,EAC5BE,GAAK,IACP,KAAK,OAAO,OAAOA,EAAG,CAAC,EACvB,KAAK,MAAM,aAAa,QAAS,KAAK,OAAO,KAAK,GAAG,CAAC,EAEzD,EACD,SAAU,SAASF,EAAM,CACvB,OAAO,KAAK,OAAO,QAAQA,CAAI,GAAK,CACrC,CACH,EAEA,SAASgI,EAAW5G,EAAM6G,EAAO,CAE/B,QADIC,EAAOJ,EAAU1G,CAAI,EAAG,EAAI,GAAIF,EAAI+G,EAAM,OACvC,EAAE,EAAI/G,GAAGgH,EAAK,IAAID,EAAM,CAAC,CAAC,CACnC,CAEA,SAASE,EAAc/G,EAAM6G,EAAO,CAElC,QADIC,EAAOJ,EAAU1G,CAAI,EAAG,EAAI,GAAIF,EAAI+G,EAAM,OACvC,EAAE,EAAI/G,GAAGgH,EAAK,OAAOD,EAAM,CAAC,CAAC,CACtC,CAEA,SAASG,GAAYH,EAAO,CAC1B,OAAO,UAAW,CAChBD,EAAW,KAAMC,CAAK,CAC1B,CACA,CAEA,SAASI,GAAaJ,EAAO,CAC3B,OAAO,UAAW,CAChBE,EAAc,KAAMF,CAAK,CAC7B,CACA,CAEA,SAASK,GAAgBL,EAAOlE,EAAO,CACrC,OAAO,UAAW,EACfA,EAAM,MAAM,KAAM,SAAS,EAAIiE,EAAaG,GAAe,KAAMF,CAAK,CAC3E,CACA,CAEe,SAAAM,GAASvI,EAAM+D,EAAO,CACnC,IAAIkE,EAAQL,EAAW5H,EAAO,EAAE,EAEhC,GAAI,UAAU,OAAS,EAAG,CAExB,QADIkI,EAAOJ,EAAU,KAAK,KAAM,CAAA,EAAG5H,EAAI,GAAIgB,EAAI+G,EAAM,OAC9C,EAAE/H,EAAIgB,GAAG,GAAI,CAACgH,EAAK,SAASD,EAAM/H,CAAC,CAAC,EAAG,MAAO,GACrD,MAAO,EACR,CAED,OAAO,KAAK,MAAM,OAAO6D,GAAU,WAC7BuE,GAAkBvE,EAClBqE,GACAC,IAAcJ,EAAOlE,CAAK,CAAC,CACnC,CC1EA,SAASyE,IAAa,CACpB,KAAK,YAAc,EACrB,CAEA,SAASC,GAAa1E,EAAO,CAC3B,OAAO,UAAW,CAChB,KAAK,YAAcA,CACvB,CACA,CAEA,SAAS2E,GAAa3E,EAAO,CAC3B,OAAO,UAAW,CAChB,IAAI+C,EAAI/C,EAAM,MAAM,KAAM,SAAS,EACnC,KAAK,YAAc+C,GAAY,EACnC,CACA,CAEe,SAAQ6B,GAAC5E,EAAO,CAC7B,OAAO,UAAU,OACX,KAAK,KAAKA,GAAS,KACfyE,IAAc,OAAOzE,GAAU,WAC/B2E,GACAD,IAAc1E,CAAK,CAAC,EACxB,KAAK,KAAM,EAAC,WACpB,CCxBA,SAAS6E,IAAa,CACpB,KAAK,UAAY,EACnB,CAEA,SAASC,GAAa9E,EAAO,CAC3B,OAAO,UAAW,CAChB,KAAK,UAAYA,CACrB,CACA,CAEA,SAAS+E,GAAa/E,EAAO,CAC3B,OAAO,UAAW,CAChB,IAAI+C,EAAI/C,EAAM,MAAM,KAAM,SAAS,EACnC,KAAK,UAAY+C,GAAY,EACjC,CACA,CAEe,SAAQiC,GAAChF,EAAO,CAC7B,OAAO,UAAU,OACX,KAAK,KAAKA,GAAS,KACf6E,IAAc,OAAO7E,GAAU,WAC/B+E,GACAD,IAAc9E,CAAK,CAAC,EACxB,KAAK,KAAM,EAAC,SACpB,CCxBA,SAASiF,IAAQ,CACX,KAAK,aAAa,KAAK,WAAW,YAAY,IAAI,CACxD,CAEe,SAAAC,IAAW,CACxB,OAAO,KAAK,KAAKD,EAAK,CACxB,CCNA,SAASE,IAAQ,CACX,KAAK,iBAAiB,KAAK,WAAW,aAAa,KAAM,KAAK,WAAW,UAAU,CACzF,CAEe,SAAAC,IAAW,CACxB,OAAO,KAAK,KAAKD,EAAK,CACxB,CCJe,SAAQE,GAACpJ,EAAM,CAC5B,IAAIqJ,EAAS,OAAOrJ,GAAS,WAAaA,EAAOQ,EAAQR,CAAI,EAC7D,OAAO,KAAK,OAAO,UAAW,CAC5B,OAAO,KAAK,YAAYqJ,EAAO,MAAM,KAAM,SAAS,CAAC,CACzD,CAAG,CACH,CCJA,SAASC,IAAe,CACtB,OAAO,IACT,CAEe,SAAAC,GAASvJ,EAAMwJ,EAAQ,CACpC,IAAIH,EAAS,OAAOrJ,GAAS,WAAaA,EAAOQ,EAAQR,CAAI,EACzDY,EAAS4I,GAAU,KAAOF,GAAe,OAAOE,GAAW,WAAaA,EAAS9I,EAAS8I,CAAM,EACpG,OAAO,KAAK,OAAO,UAAW,CAC5B,OAAO,KAAK,aAAaH,EAAO,MAAM,KAAM,SAAS,EAAGzI,EAAO,MAAM,KAAM,SAAS,GAAK,IAAI,CACjG,CAAG,CACH,CCbA,SAAS6I,IAAS,CAChB,IAAI3G,EAAS,KAAK,WACdA,GAAQA,EAAO,YAAY,IAAI,CACrC,CAEe,SAAA4G,IAAW,CACxB,OAAO,KAAK,KAAKD,EAAM,CACzB,CCPA,SAASE,IAAyB,CAChC,IAAIC,EAAQ,KAAK,UAAU,EAAK,EAAG9G,EAAS,KAAK,WACjD,OAAOA,EAASA,EAAO,aAAa8G,EAAO,KAAK,WAAW,EAAIA,CACjE,CAEA,SAASC,IAAsB,CAC7B,IAAID,EAAQ,KAAK,UAAU,EAAI,EAAG9G,EAAS,KAAK,WAChD,OAAOA,EAASA,EAAO,aAAa8G,EAAO,KAAK,WAAW,EAAIA,CACjE,CAEe,SAAQE,GAACC,EAAM,CAC5B,OAAO,KAAK,OAAOA,EAAOF,GAAsBF,EAAsB,CACxE,CCZe,SAAQK,GAACjG,EAAO,CAC7B,OAAO,UAAU,OACX,KAAK,SAAS,WAAYA,CAAK,EAC/B,KAAK,KAAM,EAAC,QACpB,CCJA,SAASkG,GAAgBC,EAAU,CACjC,OAAO,SAASC,EAAO,CACrBD,EAAS,KAAK,KAAMC,EAAO,KAAK,QAAQ,CAC5C,CACA,CAEA,SAASC,GAAeC,EAAW,CACjC,OAAOA,EAAU,OAAO,MAAM,OAAO,EAAE,IAAI,SAASC,EAAG,CACrD,IAAItK,EAAO,GAAI,EAAIsK,EAAE,QAAQ,GAAG,EAChC,OAAI,GAAK,IAAGtK,EAAOsK,EAAE,MAAM,EAAI,CAAC,EAAGA,EAAIA,EAAE,MAAM,EAAG,CAAC,GAC5C,CAAC,KAAMA,EAAG,KAAMtK,CAAI,CAC/B,CAAG,CACH,CAEA,SAASuK,GAASC,EAAU,CAC1B,OAAO,UAAW,CAChB,IAAIC,EAAK,KAAK,KACd,GAAKA,EACL,SAASzJ,EAAI,EAAG,EAAI,GAAIF,EAAI2J,EAAG,OAAQC,EAAG1J,EAAIF,EAAG,EAAEE,EAC7C0J,EAAID,EAAGzJ,CAAC,GAAI,CAACwJ,EAAS,MAAQE,EAAE,OAASF,EAAS,OAASE,EAAE,OAASF,EAAS,KACjF,KAAK,oBAAoBE,EAAE,KAAMA,EAAE,SAAUA,EAAE,OAAO,EAEtDD,EAAG,EAAE,CAAC,EAAIC,EAGV,EAAE,EAAGD,EAAG,OAAS,EAChB,OAAO,KAAK,KACrB,CACA,CAEA,SAASE,GAAMH,EAAUzG,EAAO6G,EAAS,CACvC,OAAO,UAAW,CAChB,IAAIH,EAAK,KAAK,KAAMC,EAAGR,EAAWD,GAAgBlG,CAAK,EACvD,GAAI0G,GAAI,QAASzJ,EAAI,EAAGF,EAAI2J,EAAG,OAAQzJ,EAAIF,EAAG,EAAEE,EAC9C,IAAK0J,EAAID,EAAGzJ,CAAC,GAAG,OAASwJ,EAAS,MAAQE,EAAE,OAASF,EAAS,KAAM,CAClE,KAAK,oBAAoBE,EAAE,KAAMA,EAAE,SAAUA,EAAE,OAAO,EACtD,KAAK,iBAAiBA,EAAE,KAAMA,EAAE,SAAWR,EAAUQ,EAAE,QAAUE,CAAO,EACxEF,EAAE,MAAQ3G,EACV,MACD,EAEH,KAAK,iBAAiByG,EAAS,KAAMN,EAAUU,CAAO,EACtDF,EAAI,CAAC,KAAMF,EAAS,KAAM,KAAMA,EAAS,KAAM,MAAOzG,EAAO,SAAUmG,EAAU,QAASU,CAAO,EAC5FH,EACAA,EAAG,KAAKC,CAAC,EADL,KAAK,KAAO,CAACA,CAAC,CAE3B,CACA,CAEe,SAAAG,GAASL,EAAUzG,EAAO6G,EAAS,CAChD,IAAIP,EAAYD,GAAeI,EAAW,EAAE,EAAGtK,EAAGgB,EAAImJ,EAAU,OAAQC,EAExE,GAAI,UAAU,OAAS,EAAG,CACxB,IAAIG,EAAK,KAAK,KAAI,EAAG,KACrB,GAAIA,GAAI,QAASzJ,EAAI,EAAGF,EAAI2J,EAAG,OAAQC,EAAG1J,EAAIF,EAAG,EAAEE,EACjD,IAAKd,EAAI,EAAGwK,EAAID,EAAGzJ,CAAC,EAAGd,EAAIgB,EAAG,EAAEhB,EAC9B,IAAKoK,EAAID,EAAUnK,CAAC,GAAG,OAASwK,EAAE,MAAQJ,EAAE,OAASI,EAAE,KACrD,OAAOA,EAAE,MAIf,MACD,CAGD,IADAD,EAAK1G,EAAQ4G,GAAQJ,GAChBrK,EAAI,EAAGA,EAAIgB,EAAG,EAAEhB,EAAG,KAAK,KAAKuK,EAAGJ,EAAUnK,CAAC,EAAG6D,EAAO6G,CAAO,CAAC,EAClE,OAAO,IACT,CChEA,SAASE,EAAc1J,EAAM2J,EAAMC,EAAQ,CACzC,IAAIC,EAAShE,EAAY7F,CAAI,EACzB+I,EAAQc,EAAO,YAEf,OAAOd,GAAU,WACnBA,EAAQ,IAAIA,EAAMY,EAAMC,CAAM,GAE9Bb,EAAQc,EAAO,SAAS,YAAY,OAAO,EACvCD,GAAQb,EAAM,UAAUY,EAAMC,EAAO,QAASA,EAAO,UAAU,EAAGb,EAAM,OAASa,EAAO,QACvFb,EAAM,UAAUY,EAAM,GAAO,EAAK,GAGzC3J,EAAK,cAAc+I,CAAK,CAC1B,CAEA,SAASe,GAAiBH,EAAMC,EAAQ,CACtC,OAAO,UAAW,CAChB,OAAOF,EAAc,KAAMC,EAAMC,CAAM,CAC3C,CACA,CAEA,SAASG,GAAiBJ,EAAMC,EAAQ,CACtC,OAAO,UAAW,CAChB,OAAOF,EAAc,KAAMC,EAAMC,EAAO,MAAM,KAAM,SAAS,CAAC,CAClE,CACA,CAEe,SAAAI,GAASL,EAAMC,EAAQ,CACpC,OAAO,KAAK,MAAM,OAAOA,GAAW,WAC9BG,GACAD,IAAkBH,EAAMC,CAAM,CAAC,CACvC,CCjCe,SAAAK,IAAY,CACzB,QAASxK,EAAS,KAAK,QAASG,EAAI,EAAGF,EAAID,EAAO,OAAQG,EAAIF,EAAG,EAAEE,EACjE,QAASC,EAAQJ,EAAOG,CAAC,EAAGd,EAAI,EAAGgB,EAAID,EAAM,OAAQG,EAAMlB,EAAIgB,EAAG,EAAEhB,GAC9DkB,EAAOH,EAAMf,CAAC,KAAG,MAAMkB,EAGjC,CC6BO,IAAIkK,EAAO,CAAC,IAAI,EAEhB,SAAShK,EAAUT,EAAQgB,EAAS,CACzC,KAAK,QAAUhB,EACf,KAAK,SAAWgB,CAClB,CAEA,SAASkD,IAAY,CACnB,OAAO,IAAIzD,EAAU,CAAC,CAAC,SAAS,eAAe,CAAC,EAAGgK,CAAI,CACzD,CAEA,SAASC,IAAsB,CAC7B,OAAO,IACT,CAEAjK,EAAU,UAAYyD,GAAU,UAAY,CAC1C,YAAazD,EACb,OAAQX,EACR,UAAWiB,EACX,YAAaQ,EACb,eAAgBI,GAChB,OAAQC,GACR,KAAMqB,GACN,MAAOlB,GACP,KAAM4B,GACN,KAAMC,GACN,MAAOI,GACP,UAAW0G,GACX,MAAO/F,GACP,KAAMC,GACN,KAAMQ,GACN,MAAOE,GACP,KAAMC,GACN,KAAMC,GACN,MAAOE,GACP,KAAMC,GACN,KAAMQ,GACN,MAAOM,GACP,SAAUK,GACV,QAASY,GACT,KAAMI,GACN,KAAMI,GACN,MAAOE,GACP,MAAOE,GACP,OAAQC,GACR,OAAQG,GACR,OAAQG,GACR,MAAOI,GACP,MAAOE,GACP,GAAIa,GACJ,SAAUO,GACV,CAAC,OAAO,QAAQ,EAAGC,EACrB,ECrFe,SAAQzK,GAACF,EAAU,CAChC,OAAO,OAAOA,GAAa,SACrB,IAAIY,EAAU,CAAC,CAAC,SAAS,cAAcZ,CAAQ,CAAC,CAAC,EAAG,CAAC,SAAS,eAAe,CAAC,EAC9E,IAAIY,EAAU,CAAC,CAACZ,CAAQ,CAAC,EAAG4K,CAAI,CACxC", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45]}