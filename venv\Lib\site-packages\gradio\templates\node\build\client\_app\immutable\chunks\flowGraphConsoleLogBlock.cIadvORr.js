import{b as i}from"./KHR_interactivity.DEAVS2UW.js";import{R as l}from"./declarationMapper.UBCwU7BT.js";import{h as o,R as m}from"./index.BoI39RQH.js";class p extends i{constructor(e){if(super(e),this.message=this.registerDataInput("message",l),this.logType=this.registerDataInput("logType",l,"log"),e!=null&&e.messageTemplate){const s=this._getTemplateMatches(e.messageTemplate);for(const t of s)this.registerDataInput(t,l)}}_execute(e){const s=this.logType.getValue(e),t=this._getMessageValue(e);s==="warn"?o.Warn(t):s==="error"?o.Error(t):o.Log(t),this.out._activateSignal(e)}getClassName(){return"FlowGraphConsoleLogBlock"}_getMessageValue(e){var s,t;if((s=this.config)!=null&&s.messageTemplate){let a=this.config.messageTemplate;const h=this._getTemplateMatches(a);for(const r of h){const g=(t=this.getDataInput(r))==null?void 0:t.getValue(e);g!==void 0&&(a=a.replace(new RegExp(`\\{${r}\\}`,"g"),g.toString()))}return a}else return this.message.getValue(e)}_getTemplateMatches(e){const s=/\{([^}]+)\}/g,t=[];let a;for(;(a=s.exec(e))!==null;)t.push(a[1]);return t}}m("FlowGraphConsoleLogBlock",p);export{p as FlowGraphConsoleLogBlock};
//# sourceMappingURL=flowGraphConsoleLogBlock.cIadvORr.js.map
