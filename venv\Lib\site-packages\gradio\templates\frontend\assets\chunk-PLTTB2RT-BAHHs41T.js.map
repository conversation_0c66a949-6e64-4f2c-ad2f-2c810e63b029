{"version": 3, "file": "chunk-PLTTB2RT-BAHHs41T.js", "sources": ["../../../../node_modules/.pnpm/mermaid@11.5.0/node_modules/mermaid/dist/chunks/mermaid.core/chunk-PLTTB2RT.mjs"], "sourcesContent": ["import {\n  __name,\n  lineBreakRegex\n} from \"./chunk-O7R7247Q.mjs\";\n\n// src/diagrams/common/svgDrawCommon.ts\nimport { sanitizeUrl } from \"@braintree/sanitize-url\";\nvar drawRect = /* @__PURE__ */ __name((element, rectData) => {\n  const rectElement = element.append(\"rect\");\n  rectElement.attr(\"x\", rectData.x);\n  rectElement.attr(\"y\", rectData.y);\n  rectElement.attr(\"fill\", rectData.fill);\n  rectElement.attr(\"stroke\", rectData.stroke);\n  rectElement.attr(\"width\", rectData.width);\n  rectElement.attr(\"height\", rectData.height);\n  if (rectData.name) {\n    rectElement.attr(\"name\", rectData.name);\n  }\n  if (rectData.rx) {\n    rectElement.attr(\"rx\", rectData.rx);\n  }\n  if (rectData.ry) {\n    rectElement.attr(\"ry\", rectData.ry);\n  }\n  if (rectData.attrs !== void 0) {\n    for (const attrKey in rectData.attrs) {\n      rectElement.attr(attrKey, rectData.attrs[attrKey]);\n    }\n  }\n  if (rectData.class) {\n    rectElement.attr(\"class\", rectData.class);\n  }\n  return rectElement;\n}, \"drawRect\");\nvar drawBackgroundRect = /* @__PURE__ */ __name((element, bounds) => {\n  const rectData = {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    stroke: bounds.stroke,\n    class: \"rect\"\n  };\n  const rectElement = drawRect(element, rectData);\n  rectElement.lower();\n}, \"drawBackgroundRect\");\nvar drawText = /* @__PURE__ */ __name((element, textData) => {\n  const nText = textData.text.replace(lineBreakRegex, \" \");\n  const textElem = element.append(\"text\");\n  textElem.attr(\"x\", textData.x);\n  textElem.attr(\"y\", textData.y);\n  textElem.attr(\"class\", \"legend\");\n  textElem.style(\"text-anchor\", textData.anchor);\n  if (textData.class) {\n    textElem.attr(\"class\", textData.class);\n  }\n  const tspan = textElem.append(\"tspan\");\n  tspan.attr(\"x\", textData.x + textData.textMargin * 2);\n  tspan.text(nText);\n  return textElem;\n}, \"drawText\");\nvar drawImage = /* @__PURE__ */ __name((elem, x, y, link) => {\n  const imageElement = elem.append(\"image\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = sanitizeUrl(link);\n  imageElement.attr(\"xlink:href\", sanitizedLink);\n}, \"drawImage\");\nvar drawEmbeddedImage = /* @__PURE__ */ __name((element, x, y, link) => {\n  const imageElement = element.append(\"use\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = sanitizeUrl(link);\n  imageElement.attr(\"xlink:href\", `#${sanitizedLink}`);\n}, \"drawEmbeddedImage\");\nvar getNoteRect = /* @__PURE__ */ __name(() => {\n  const noteRectData = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    fill: \"#EDF2AE\",\n    stroke: \"#666\",\n    anchor: \"start\",\n    rx: 0,\n    ry: 0\n  };\n  return noteRectData;\n}, \"getNoteRect\");\nvar getTextObj = /* @__PURE__ */ __name(() => {\n  const testObject = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    \"text-anchor\": \"start\",\n    style: \"#666\",\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    tspan: true\n  };\n  return testObject;\n}, \"getTextObj\");\n\nexport {\n  drawRect,\n  drawBackgroundRect,\n  drawText,\n  drawImage,\n  drawEmbeddedImage,\n  getNoteRect,\n  getTextObj\n};\n"], "names": ["drawRect", "__name", "element", "rectData", "rectElement", "attrKey", "drawBackgroundRect", "bounds", "drawText", "textData", "nText", "lineBreakRegex", "textElem", "tspan", "drawImage", "elem", "x", "y", "link", "imageElement", "sanitizedLink", "sanitizeUrl", "drawEmbeddedImage", "getNoteRect", "getTextObj"], "mappings": "6DAOG,IAACA,EAA2BC,EAAO,CAACC,EAASC,IAAa,CAC3D,MAAMC,EAAcF,EAAQ,OAAO,MAAM,EAgBzC,GAfAE,EAAY,KAAK,IAAKD,EAAS,CAAC,EAChCC,EAAY,KAAK,IAAKD,EAAS,CAAC,EAChCC,EAAY,KAAK,OAAQD,EAAS,IAAI,EACtCC,EAAY,KAAK,SAAUD,EAAS,MAAM,EAC1CC,EAAY,KAAK,QAASD,EAAS,KAAK,EACxCC,EAAY,KAAK,SAAUD,EAAS,MAAM,EACtCA,EAAS,MACXC,EAAY,KAAK,OAAQD,EAAS,IAAI,EAEpCA,EAAS,IACXC,EAAY,KAAK,KAAMD,EAAS,EAAE,EAEhCA,EAAS,IACXC,EAAY,KAAK,KAAMD,EAAS,EAAE,EAEhCA,EAAS,QAAU,OACrB,UAAWE,KAAWF,EAAS,MAC7BC,EAAY,KAAKC,EAASF,EAAS,MAAME,CAAO,CAAC,EAGrD,OAAIF,EAAS,OACXC,EAAY,KAAK,QAASD,EAAS,KAAK,EAEnCC,CACT,EAAG,UAAU,EACTE,EAAqCL,EAAO,CAACC,EAASK,IAAW,CACnE,MAAMJ,EAAW,CACf,EAAGI,EAAO,OACV,EAAGA,EAAO,OACV,MAAOA,EAAO,MAAQA,EAAO,OAC7B,OAAQA,EAAO,MAAQA,EAAO,OAC9B,KAAMA,EAAO,KACb,OAAQA,EAAO,OACf,MAAO,MACX,EACsBP,EAASE,EAASC,CAAQ,EAClC,MAAK,CACnB,EAAG,oBAAoB,EACnBK,EAA2BP,EAAO,CAACC,EAASO,IAAa,CAC3D,MAAMC,EAAQD,EAAS,KAAK,QAAQE,EAAgB,GAAG,EACjDC,EAAWV,EAAQ,OAAO,MAAM,EACtCU,EAAS,KAAK,IAAKH,EAAS,CAAC,EAC7BG,EAAS,KAAK,IAAKH,EAAS,CAAC,EAC7BG,EAAS,KAAK,QAAS,QAAQ,EAC/BA,EAAS,MAAM,cAAeH,EAAS,MAAM,EACzCA,EAAS,OACXG,EAAS,KAAK,QAASH,EAAS,KAAK,EAEvC,MAAMI,EAAQD,EAAS,OAAO,OAAO,EACrC,OAAAC,EAAM,KAAK,IAAKJ,EAAS,EAAIA,EAAS,WAAa,CAAC,EACpDI,EAAM,KAAKH,CAAK,EACTE,CACT,EAAG,UAAU,EACTE,EAA4Bb,EAAO,CAACc,EAAMC,EAAGC,EAAGC,IAAS,CAC3D,MAAMC,EAAeJ,EAAK,OAAO,OAAO,EACxCI,EAAa,KAAK,IAAKH,CAAC,EACxBG,EAAa,KAAK,IAAKF,CAAC,EACxB,MAAMG,EAAgBC,EAAYH,CAAI,EACtCC,EAAa,KAAK,aAAcC,CAAa,CAC/C,EAAG,WAAW,EACVE,EAAoCrB,EAAO,CAACC,EAASc,EAAGC,EAAGC,IAAS,CACtE,MAAMC,EAAejB,EAAQ,OAAO,KAAK,EACzCiB,EAAa,KAAK,IAAKH,CAAC,EACxBG,EAAa,KAAK,IAAKF,CAAC,EACxB,MAAMG,EAAgBC,EAAYH,CAAI,EACtCC,EAAa,KAAK,aAAc,IAAIC,CAAa,EAAE,CACrD,EAAG,mBAAmB,EAClBG,EAA8BtB,EAAO,KAClB,CACnB,EAAG,EACH,EAAG,EACH,MAAO,IACP,OAAQ,IACR,KAAM,UACN,OAAQ,OACR,OAAQ,QACR,GAAI,EACJ,GAAI,CACR,GAEG,aAAa,EACZuB,EAA6BvB,EAAO,KACnB,CACjB,EAAG,EACH,EAAG,EACH,MAAO,IACP,OAAQ,IACR,cAAe,QACf,MAAO,OACP,WAAY,EACZ,GAAI,EACJ,GAAI,EACJ,MAAO,EACX,GAEG,YAAY", "x_google_ignoreList": [0]}