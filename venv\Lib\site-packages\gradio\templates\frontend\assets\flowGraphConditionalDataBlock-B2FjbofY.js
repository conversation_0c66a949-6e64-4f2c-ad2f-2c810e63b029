import{F as s}from"./KHR_interactivity-DTxiAnOo.js";import{c as a,R as o}from"./declarationMapper-BZjsjg7g.js";import{R as e}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class r extends s{constructor(t){super(t),this.condition=this.registerDataInput("condition",a),this.onTrue=this.registerDataInput("onTrue",o),this.onFalse=this.registerDataInput("onFalse",o),this.output=this.registerDataOutput("output",o)}_updateOutputs(t){const i=this.condition.getValue(t);this.output.setValue(i?this.onTrue.getValue(t):this.onFalse.getValue(t),t)}getClassName(){return"FlowGraphConditionalBlock"}}e("FlowGraphConditionalBlock",r);export{r as FlowGraphConditionalDataBlock};
//# sourceMappingURL=flowGraphConditionalDataBlock-B2FjbofY.js.map
