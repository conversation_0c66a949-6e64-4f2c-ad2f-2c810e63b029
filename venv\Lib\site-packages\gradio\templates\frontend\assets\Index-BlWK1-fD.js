const{SvelteComponent:M,append:b,attr:w,create_slot:O,destroy_each:Q,detach:k,element:p,empty:J,ensure_array_like:B,flush:q,get_all_dirty_from_scope:R,get_slot_changes:T,init:U,insert:v,is_function:K,listen:I,noop:V,run_all:A,safe_not_equal:W,set_data:z,space:P,text:S,toggle_class:N,transition_in:X,transition_out:Y,update_slot_base:Z}=window.__gradio__svelte__internal;function D(t,e,n){const i=t.slice();return i[16]=e[n],i}function E(t){let e,n,i,l,r,d;return{c(){e=p("div"),n=p("span"),i=S(t[2]),l=S(":"),r=S(" "),d=S(t[3]),w(n,"class","svelte-1ccw3kh"),w(e,"class","component-name svelte-1ccw3kh")},m(u,o){v(u,e,o),b(e,n),b(n,i),b(n,l),b(e,r),b(e,d)},p(u,o){o&4&&z(i,u[2]),o&8&&z(d,u[3])},d(u){u&&k(e)}}}function x(t){let e,n,i,l,r,d,u,o,c,f,a,s=!t[1]&&F(t);return{c(){e=p("button"),e.textContent="+",n=P(),i=p("button"),i.textContent="+",l=P(),r=p("button"),r.textContent="+",d=P(),u=p("button"),u.textContent="+",o=P(),s&&s.c(),c=J(),w(e,"class","add up svelte-1ccw3kh"),w(i,"class","add left svelte-1ccw3kh"),w(r,"class","add right svelte-1ccw3kh"),w(u,"class","add down svelte-1ccw3kh")},m(_,m){v(_,e,m),v(_,n,m),v(_,i,m),v(_,l,m),v(_,r,m),v(_,d,m),v(_,u,m),v(_,o,m),s&&s.m(_,m),v(_,c,m),f||(a=[I(e,"click",t[11]("up")),I(i,"click",t[11]("left")),I(r,"click",t[11]("right")),I(u,"click",t[11]("down"))],f=!0)},p(_,m){_[1]?s&&(s.d(1),s=null):s?s.p(_,m):(s=F(_),s.c(),s.m(c.parentNode,c))},d(_){_&&(k(e),k(n),k(i),k(l),k(r),k(d),k(u),k(o),k(c)),s&&s.d(_),f=!1,A(a)}}}function $(t){let e,n=!t[10]&&!t[1]&&G(t);return{c(){n&&n.c(),e=J()},m(i,l){n&&n.m(i,l),v(i,e,l)},p(i,l){!i[10]&&!i[1]?n?n.p(i,l):(n=G(i),n.c(),n.m(e.parentNode,e)):n&&(n.d(1),n=null)},d(i){i&&k(e),n&&n.d(i)}}}function F(t){let e,n,i,l,r;return{c(){e=p("button"),e.textContent="✎",n=P(),i=p("button"),i.textContent="✗",w(e,"class","action modify svelte-1ccw3kh"),w(i,"class","action delete svelte-1ccw3kh")},m(d,u){v(d,e,u),v(d,n,u),v(d,i,u),l||(r=[I(e,"click",t[11]("modify")),I(i,"click",t[11]("delete"))],l=!0)},p:V,d(d){d&&(k(e),k(n),k(i)),l=!1,A(r)}}}function G(t){let e,n,i,l,r,d,u,o=B(t[6]),c=[];for(let f=0;f<o.length;f+=1)c[f]=H(D(t,o,f));return{c(){e=p("div"),n=p("button"),n.textContent="input",i=P(),l=p("button"),l.textContent="output",r=S(`
					| `);for(let f=0;f<c.length;f+=1)c[f].c();w(n,"class","function input svelte-1ccw3kh"),N(n,"selected",t[7]),w(l,"class","function output svelte-1ccw3kh"),N(l,"selected",t[8]),w(e,"class","button-set svelte-1ccw3kh")},m(f,a){v(f,e,a),b(e,n),b(e,i),b(e,l),b(e,r);for(let s=0;s<c.length;s+=1)c[s]&&c[s].m(e,null);d||(u=[I(n,"click",t[11]("input")),I(l,"click",t[11]("output"))],d=!0)},p(f,a){if(a&128&&N(n,"selected",f[7]),a&256&&N(l,"selected",f[8]),a&2624){o=B(f[6]);let s;for(s=0;s<o.length;s+=1){const _=D(f,o,s);c[s]?c[s].p(_,a):(c[s]=H(_),c[s].c(),c[s].m(e,null))}for(;s<c.length;s+=1)c[s].d(1);c.length=o.length}},d(f){f&&k(e),Q(c,f),d=!1,A(u)}}}function H(t){let e,n,i=t[16]+"",l,r,d;return{c(){e=p("button"),n=S("on:"),l=S(i),w(e,"class","function event svelte-1ccw3kh"),N(e,"selected",t[9].includes(t[16]))},m(u,o){v(u,e,o),b(e,n),b(e,l),r||(d=I(e,"click",function(){K(t[11]("on:"+t[16]))&&t[11]("on:"+t[16]).apply(this,arguments)}),r=!0)},p(u,o){t=u,o&64&&i!==(i=t[16]+"")&&z(l,i),o&576&&N(e,"selected",t[9].includes(t[16]))},d(u){u&&k(e),r=!1,d()}}}function ee(t){let e,n,i,l,r=t[12].includes(t[2]),d,u,o,c,f,a=r&&E(t);function s(C,y){return C[5]?$:x}let _=s(t),m=_(t);const j=t[15].default,g=O(j,t,t[14],null);return{c(){e=p("div"),n=p("div"),i=P(),l=p("div"),a&&a.c(),d=P(),m.c(),u=P(),g&&g.c(),w(n,"class","cover svelte-1ccw3kh"),w(l,"class","interaction svelte-1ccw3kh"),w(e,"class","sketchbox svelte-1ccw3kh"),N(e,"function_mode",t[5]),N(e,"row",t[0]),N(e,"active",t[4])},m(C,y){v(C,e,y),b(e,n),b(e,i),b(e,l),a&&a.m(l,null),b(l,d),m.m(l,null),b(e,u),g&&g.m(e,null),o=!0,c||(f=I(l,"click",function(){K(t[1]?void 0:t[11]("modify"))&&(t[1]?void 0:t[11]("modify")).apply(this,arguments)}),c=!0)},p(C,[y]){t=C,y&4&&(r=t[12].includes(t[2])),r?a?a.p(t,y):(a=E(t),a.c(),a.m(l,d)):a&&(a.d(1),a=null),_===(_=s(t))&&m?m.p(t,y):(m.d(1),m=_(t),m&&(m.c(),m.m(l,null))),g&&g.p&&(!o||y&16384)&&Z(g,j,t,t[14],o?T(j,t[14],y,null):R(t[14]),null),(!o||y&32)&&N(e,"function_mode",t[5]),(!o||y&1)&&N(e,"row",t[0]),(!o||y&16)&&N(e,"active",t[4])},i(C){o||(X(g,C),o=!0)},o(C){Y(g,C),o=!1},d(C){C&&k(e),a&&a.d(),m.d(),g&&g.d(C),c=!1,f()}}}function te(t,e,n){let i,{$$slots:l={},$$scope:r}=e,{row:d}=e,{is_container:u}=e,{component_type:o}=e,{var_name:c}=e,{active:f=!1}=e,{function_mode:a=!1}=e,{event_list:s}=e,{is_input:_=!1}=e,{is_output:m=!1}=e,{triggers:j=[]}=e,{gradio:g}=e;const C=h=>L=>{L.stopPropagation(),g.dispatch("select",{index:0,value:h})},y=["state","browserstate","function"];return t.$$set=h=>{"row"in h&&n(0,d=h.row),"is_container"in h&&n(1,u=h.is_container),"component_type"in h&&n(2,o=h.component_type),"var_name"in h&&n(3,c=h.var_name),"active"in h&&n(4,f=h.active),"function_mode"in h&&n(5,a=h.function_mode),"event_list"in h&&n(6,s=h.event_list),"is_input"in h&&n(7,_=h.is_input),"is_output"in h&&n(8,m=h.is_output),"triggers"in h&&n(9,j=h.triggers),"gradio"in h&&n(13,g=h.gradio),"$$scope"in h&&n(14,r=h.$$scope)},t.$$.update=()=>{t.$$.dirty&4&&n(10,i=o==="function")},[d,u,o,c,f,a,s,_,m,j,i,C,y,g,r,l]}class ne extends M{constructor(e){super(),U(this,e,te,ee,W,{row:0,is_container:1,component_type:2,var_name:3,active:4,function_mode:5,event_list:6,is_input:7,is_output:8,triggers:9,gradio:13})}get row(){return this.$$.ctx[0]}set row(e){this.$$set({row:e}),q()}get is_container(){return this.$$.ctx[1]}set is_container(e){this.$$set({is_container:e}),q()}get component_type(){return this.$$.ctx[2]}set component_type(e){this.$$set({component_type:e}),q()}get var_name(){return this.$$.ctx[3]}set var_name(e){this.$$set({var_name:e}),q()}get active(){return this.$$.ctx[4]}set active(e){this.$$set({active:e}),q()}get function_mode(){return this.$$.ctx[5]}set function_mode(e){this.$$set({function_mode:e}),q()}get event_list(){return this.$$.ctx[6]}set event_list(e){this.$$set({event_list:e}),q()}get is_input(){return this.$$.ctx[7]}set is_input(e){this.$$set({is_input:e}),q()}get is_output(){return this.$$.ctx[8]}set is_output(e){this.$$set({is_output:e}),q()}get triggers(){return this.$$.ctx[9]}set triggers(e){this.$$set({triggers:e}),q()}get gradio(){return this.$$.ctx[13]}set gradio(e){this.$$set({gradio:e}),q()}}export{ne as default};
//# sourceMappingURL=Index-BlWK1-fD.js.map
