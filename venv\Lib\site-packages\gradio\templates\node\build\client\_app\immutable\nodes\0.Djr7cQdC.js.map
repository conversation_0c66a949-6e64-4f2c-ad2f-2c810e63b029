{"version": 3, "file": "0.Djr7cQdC.js", "sources": ["../../../../../../src/routes/svelte_init.ts"], "sourcesContent": ["import * as svelte from \"svelte\";\nconst is_browser = typeof window !== \"undefined\";\nif (is_browser) {\n\tconst o = {\n\t\tSvelteComponent: svelte.SvelteComponent\n\t};\n\tfor (const key in svelte) {\n\t\tif (key === \"SvelteComponent\") continue;\n\t\tif (key === \"SvelteComponentDev\") {\n\t\t\t//@ts-ignore\n\t\t\to[key] = o[\"SvelteComponent\"];\n\t\t} else {\n\t\t\t//@ts-ignore\n\t\t\to[key] = svelte[key];\n\t\t}\n\t}\n\twindow.__gradio__svelte__internal = o;\n\twindow.__gradio__svelte__internal[\"globals\"] = {};\n\twindow.globals = window;\n}\n"], "names": ["is_browser", "o", "svelte", "key"], "mappings": "qTACA,MAAMA,EAAa,OAAO,OAAW,IACrC,GAAIA,EAAY,CACf,MAAMC,EAAI,CACT,gBAAiBC,EAAO,eAAA,EAEzB,UAAWC,KAAOD,EACbC,IAAQ,oBACRA,IAAQ,qBAETF,EAAAE,CAAG,EAAIF,EAAE,gBAGTA,EAAAE,CAAG,EAAID,EAAOC,CAAG,GAGrB,OAAO,2BAA6BF,EAC7B,OAAA,2BAA2B,QAAa,GAC/C,OAAO,QAAU,MAClB"}