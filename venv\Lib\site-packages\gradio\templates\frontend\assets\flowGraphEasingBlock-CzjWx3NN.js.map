{"version": 3, "file": "flowGraphEasingBlock-CzjWx3NN.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/Animation/flowGraphEasingBlock.js"], "sourcesContent": ["import { BackEase, BezierCurveEase, BounceEase, CircleEase, CubicEase, ElasticEase, ExponentialEase } from \"../../../../Animations/easing.js\";\nimport { FlowGraphBlock } from \"../../../flowGraphBlock.js\";\nimport { RichTypeAny, RichTypeNumber } from \"../../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\n/**\n * The type of the easing function.\n */\nexport var EasingFunctionType;\n(function (EasingFunctionType) {\n    EasingFunctionType[EasingFunctionType[\"CircleEase\"] = 0] = \"CircleEase\";\n    EasingFunctionType[EasingFunctionType[\"BackEase\"] = 1] = \"BackEase\";\n    EasingFunctionType[EasingFunctionType[\"BounceEase\"] = 2] = \"BounceEase\";\n    EasingFunctionType[EasingFunctionType[\"CubicEase\"] = 3] = \"CubicEase\";\n    EasingFunctionType[EasingFunctionType[\"ElasticEase\"] = 4] = \"ElasticEase\";\n    EasingFunctionType[EasingFunctionType[\"ExponentialEase\"] = 5] = \"ExponentialEase\";\n    EasingFunctionType[EasingFunctionType[\"PowerEase\"] = 6] = \"PowerEase\";\n    EasingFunctionType[EasingFunctionType[\"QuadraticEase\"] = 7] = \"QuadraticEase\";\n    EasingFunctionType[EasingFunctionType[\"QuarticEase\"] = 8] = \"QuarticEase\";\n    EasingFunctionType[EasingFunctionType[\"QuinticEase\"] = 9] = \"QuinticEase\";\n    EasingFunctionType[EasingFunctionType[\"SineEase\"] = 10] = \"SineEase\";\n    EasingFunctionType[EasingFunctionType[\"BezierCurveEase\"] = 11] = \"BezierCurveEase\";\n})(EasingFunctionType || (EasingFunctionType = {}));\n/**\n * @internal\n * Creates an easing function object based on the type and parameters provided.\n * This is not tree-shaking friendly, so if you need cubic bezier, use the dedicated bezier block.\n * @param type The type of the easing function.\n * @param controlPoint1 The first control point for the bezier curve.\n * @param controlPoint2 The second control point for the bezier curve.\n * @returns The easing function object.\n */\nfunction CreateEasingFunction(type, ...parameters) {\n    switch (type) {\n        case 11 /* EasingFunctionType.BezierCurveEase */:\n            return new BezierCurveEase(...parameters);\n        case 0 /* EasingFunctionType.CircleEase */:\n            return new CircleEase();\n        case 1 /* EasingFunctionType.BackEase */:\n            return new BackEase(...parameters);\n        case 2 /* EasingFunctionType.BounceEase */:\n            return new BounceEase(...parameters);\n        case 3 /* EasingFunctionType.CubicEase */:\n            return new CubicEase();\n        case 4 /* EasingFunctionType.ElasticEase */:\n            return new ElasticEase(...parameters);\n        case 5 /* EasingFunctionType.ExponentialEase */:\n            return new ExponentialEase(...parameters);\n        default:\n            throw new Error(\"Easing type not yet implemented\");\n    }\n}\n/**\n * An easing block that generates an easingFunction object based on the data provided.\n */\nexport class FlowGraphEasingBlock extends FlowGraphBlock {\n    constructor(\n    /**\n     * the configuration of the block\n     */\n    config) {\n        super(config);\n        this.config = config;\n        /**\n         * Internal cache of reusable easing functions.\n         * key is type-mode-properties\n         */\n        this._easingFunctions = {};\n        this.type = this.registerDataInput(\"type\", RichTypeAny, 11);\n        this.mode = this.registerDataInput(\"mode\", RichTypeNumber, 0);\n        this.parameters = this.registerDataInput(\"parameters\", RichTypeAny, [1, 0, 0, 1]);\n        this.easingFunction = this.registerDataOutput(\"easingFunction\", RichTypeAny);\n    }\n    _updateOutputs(context) {\n        const type = this.type.getValue(context);\n        const mode = this.mode.getValue(context);\n        const parameters = this.parameters.getValue(context);\n        if (type === undefined || mode === undefined) {\n            return;\n        }\n        const key = `${type}-${mode}-${parameters.join(\"-\")}`;\n        if (!this._easingFunctions[key]) {\n            const easing = CreateEasingFunction(type, ...parameters);\n            easing.setEasingMode(mode);\n            this._easingFunctions[key] = easing;\n        }\n        this.easingFunction.setValue(this._easingFunctions[key], context);\n    }\n    getClassName() {\n        return \"FlowGraphEasingBlock\" /* FlowGraphBlockNames.Easing */;\n    }\n}\nRegisterClass(\"FlowGraphEasingBlock\" /* FlowGraphBlockNames.Easing */, FlowGraphEasingBlock);\n//# sourceMappingURL=flowGraphEasingBlock.js.map"], "names": ["EasingFunctionType", "CreateEasingFunction", "type", "parameters", "BezierCurveEase", "CircleEase", "BackEase", "BounceEase", "CubicEase", "ElasticEase", "ExponentialEase", "FlowGraphEasingBlock", "FlowGraphBlock", "config", "RichTypeAny", "RichTypeNumber", "context", "mode", "key", "easing", "RegisterClass"], "mappings": "+SAOU,IAACA,GACV,SAAUA,EAAoB,CAC3BA,EAAmBA,EAAmB,WAAgB,CAAC,EAAI,aAC3DA,EAAmBA,EAAmB,SAAc,CAAC,EAAI,WACzDA,EAAmBA,EAAmB,WAAgB,CAAC,EAAI,aAC3DA,EAAmBA,EAAmB,UAAe,CAAC,EAAI,YAC1DA,EAAmBA,EAAmB,YAAiB,CAAC,EAAI,cAC5DA,EAAmBA,EAAmB,gBAAqB,CAAC,EAAI,kBAChEA,EAAmBA,EAAmB,UAAe,CAAC,EAAI,YAC1DA,EAAmBA,EAAmB,cAAmB,CAAC,EAAI,gBAC9DA,EAAmBA,EAAmB,YAAiB,CAAC,EAAI,cAC5DA,EAAmBA,EAAmB,YAAiB,CAAC,EAAI,cAC5DA,EAAmBA,EAAmB,SAAc,EAAE,EAAI,WAC1DA,EAAmBA,EAAmB,gBAAqB,EAAE,EAAI,iBACrE,GAAGA,IAAuBA,EAAqB,CAAE,EAAC,EAUlD,SAASC,EAAqBC,KAASC,EAAY,CAC/C,OAAQD,EAAI,CACR,IAAK,IACD,OAAO,IAAIE,EAAgB,GAAGD,CAAU,EAC5C,IAAK,GACD,OAAO,IAAIE,EACf,IAAK,GACD,OAAO,IAAIC,EAAS,GAAGH,CAAU,EACrC,IAAK,GACD,OAAO,IAAII,EAAW,GAAGJ,CAAU,EACvC,IAAK,GACD,OAAO,IAAIK,EACf,IAAK,GACD,OAAO,IAAIC,EAAY,GAAGN,CAAU,EACxC,IAAK,GACD,OAAO,IAAIO,EAAgB,GAAGP,CAAU,EAC5C,QACI,MAAM,IAAI,MAAM,iCAAiC,CACxD,CACL,CAIO,MAAMQ,UAA6BC,CAAe,CACrD,YAIAC,EAAQ,CACJ,MAAMA,CAAM,EACZ,KAAK,OAASA,EAKd,KAAK,iBAAmB,GACxB,KAAK,KAAO,KAAK,kBAAkB,OAAQC,EAAa,EAAE,EAC1D,KAAK,KAAO,KAAK,kBAAkB,OAAQC,EAAgB,CAAC,EAC5D,KAAK,WAAa,KAAK,kBAAkB,aAAcD,EAAa,CAAC,EAAG,EAAG,EAAG,CAAC,CAAC,EAChF,KAAK,eAAiB,KAAK,mBAAmB,iBAAkBA,CAAW,CAC9E,CACD,eAAeE,EAAS,CACpB,MAAMd,EAAO,KAAK,KAAK,SAASc,CAAO,EACjCC,EAAO,KAAK,KAAK,SAASD,CAAO,EACjCb,EAAa,KAAK,WAAW,SAASa,CAAO,EACnD,GAAId,IAAS,QAAae,IAAS,OAC/B,OAEJ,MAAMC,EAAM,GAAGhB,CAAI,IAAIe,CAAI,IAAId,EAAW,KAAK,GAAG,CAAC,GACnD,GAAI,CAAC,KAAK,iBAAiBe,CAAG,EAAG,CAC7B,MAAMC,EAASlB,EAAqBC,EAAM,GAAGC,CAAU,EACvDgB,EAAO,cAAcF,CAAI,EACzB,KAAK,iBAAiBC,CAAG,EAAIC,CAChC,CACD,KAAK,eAAe,SAAS,KAAK,iBAAiBD,CAAG,EAAGF,CAAO,CACnE,CACD,cAAe,CACX,MAAO,sBACV,CACL,CACAI,EAAc,uBAAyDT,CAAoB", "x_google_ignoreList": [0]}