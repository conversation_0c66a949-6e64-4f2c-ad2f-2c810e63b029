import{c as o,_ as c}from"./KHR_interactivity.DEAVS2UW.js";import{ax as l,R as k}from"./index.BoI39RQH.js";import{R as i,e as u,b as d}from"./declarationMapper.UBCwU7BT.js";class g extends o{constructor(e){super(e),this.config=e,this.type="MeshPick",this.asset=this.registerDataInput("asset",i,e==null?void 0:e.targetMesh),this.pickedPoint=this.registerDataOutput("pickedPoint",u),this.pickOrigin=this.registerDataOutput("pickOrigin",u),this.pointerId=this.registerDataOutput("pointerId",d),this.pickedMesh=this.registerDataOutput("pickedMesh",i),this.pointerType=this.registerDataInput("pointerType",i,l.POINTERPICK)}_getReferencedMesh(e){return this.asset.getValue(e)}_executeEvent(e,t){var r,p,a,h,n;if(this.pointerType.getValue(e)!==t.type)return!0;const s=this._getReferencedMesh(e);return s&&((r=t.pickInfo)!=null&&r.pickedMesh)&&(((p=t.pickInfo)==null?void 0:p.pickedMesh)===s||c((a=t.pickInfo)==null?void 0:a.pickedMesh,s))?(this.pointerId.setValue(t.event.pointerId,e),this.pickOrigin.setValue((h=t.pickInfo.ray)==null?void 0:h.origin,e),this.pickedPoint.setValue(t.pickInfo.pickedPoint,e),this.pickedMesh.setValue(t.pickInfo.pickedMesh,e),this._execute(e),!((n=this.config)!=null&&n.stopPropagation)):(this.pointerId.resetToDefaultValue(e),this.pickOrigin.resetToDefaultValue(e),this.pickedPoint.resetToDefaultValue(e),this.pickedMesh.resetToDefaultValue(e),!0)}_preparePendingTasks(e){}_cancelPendingTasks(e){}getClassName(){return"FlowGraphMeshPickEventBlock"}}k("FlowGraphMeshPickEventBlock",g);export{g as FlowGraphMeshPickEventBlock};
//# sourceMappingURL=flowGraphMeshPickEventBlock.Bkgm0TYl.js.map
