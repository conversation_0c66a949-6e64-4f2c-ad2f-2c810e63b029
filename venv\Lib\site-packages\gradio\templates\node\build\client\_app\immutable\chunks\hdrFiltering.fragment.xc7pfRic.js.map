{"version": 3, "file": "hdrFiltering.fragment.xc7pfRic.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Shaders/hdrFiltering.fragment.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nimport \"./ShadersInclude/helperFunctions.js\";\nimport \"./ShadersInclude/importanceSampling.js\";\nimport \"./ShadersInclude/pbrBRDFFunctions.js\";\nimport \"./ShadersInclude/hdrFilteringFunctions.js\";\nconst name = \"hdrFilteringPixelShader\";\nconst shader = `#include<helperFunctions>\n#include<importanceSampling>\n#include<pbrBRDFFunctions>\n#include<hdrFilteringFunctions>\nuniform float alphaG;uniform samplerCube inputTexture;uniform vec2 vFilteringInfo;uniform float hdrScale;varying vec3 direction;void main() {vec3 color=radiance(alphaG,inputTexture,direction,vFilteringInfo);gl_FragColor=vec4(color*hdrScale,1.0);}`;\n// Sideeffect\nif (!ShaderStore.ShadersStore[name]) {\n    ShaderStore.ShadersStore[name] = shader;\n}\n/** @internal */\nexport const hdrFilteringPixelShader = { name, shader };\n//# sourceMappingURL=hdrFiltering.fragment.js.map"], "names": ["name", "shader", "ShaderStore", "hdrFilteringPixelShader"], "mappings": "0HAMA,MAAMA,EAAO,0BACPC,EAAS;AAAA;AAAA;AAAA;AAAA,wPAMVC,EAAY,aAAaF,CAAI,IAC9BE,EAAY,aAAaF,CAAI,EAAIC,GAGzB,MAACE,EAA0B,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}