import{c as a,d as i}from"./KHR_interactivity.DEAVS2UW.js";import{b as v,R as c}from"./index.BoI39RQH.js";class l extends a{constructor(e){super(e),this.config=e,this.initPriority=1;for(const t in this.config.eventData)this.registerDataOutput(t,this.config.eventData[t].type)}_preparePendingTasks(e){const t=e.configuration.coordinator.getCustomEventObservable(this.config.eventId);if(t&&t.hasObservers()&&t.observers.length>i.MaxEventsPerType){this._reportError(e,`FlowGraphReceiveCustomEventBlock: Too many observers for event ${this.config.eventId}. Max is ${i.MaxEventsPerType}.`);return}const r=t.add(s=>{Object.keys(s).forEach(o=>{var n;(n=this.getDataOutput(o))==null||n.setValue(s[o],e)}),this._execute(e)});e._setExecutionVariable(this,"_eventObserver",r)}_cancelPendingTasks(e){const t=e.configuration.coordinator.getCustomEventObservable(this.config.eventId);if(t){const r=e._getExecutionVariable(this,"_eventObserver",null);t.remove(r)}else v.Warn(`FlowGraphReceiveCustomEventBlock: Missing observable for event ${this.config.eventId}`)}_executeEvent(e,t){return!0}getClassName(){return"FlowGraphReceiveCustomEventBlock"}}c("FlowGraphReceiveCustomEventBlock",l);export{l as FlowGraphReceiveCustomEventBlock};
//# sourceMappingURL=flowGraphReceiveCustomEventBlock.Zl1e7Elp.js.map
