import{R as p}from"./index.BoI39RQH.js";import{b as v}from"./KHR_interactivity.DEAVS2UW.js";import{R as u}from"./declarationMapper.UBCwU7BT.js";class f extends v{constructor(a){if(super(a),!a.variable&&!a.variables)throw new Error("FlowGraphSetVariableBlock: variable/variables is not defined");if(a.variables&&a.variable)throw new Error("FlowGraphSetVariableBlock: variable and variables are both defined");if(a.variables)for(const i of a.variables)this.registerDataInput(i,u);else this.registerDataInput("value",u)}_execute(a,i){var r,e;if((r=this.config)!=null&&r.variables)for(const t of this.config.variables)this._saveVariable(a,t);else this._saveVariable(a,(e=this.config)==null?void 0:e.variable,"value");this.out._activateSignal(a)}_saveVariable(a,i,r){var s;const e=a._getGlobalContextVariable("currentlyRunningAnimationGroups",[]);for(const o of e){const n=a.assetsContext.animationGroups[o];for(const l of n.targetedAnimations)if(l.target===a&&l.target===a&&l.animation.targetProperty===i){n.stop();const b=e.indexOf(o);b>-1&&e.splice(b,1),a._setGlobalContextVariable("currentlyRunningAnimationGroups",e);break}}const t=(s=this.getDataInput(r||i))==null?void 0:s.getValue(a);a.setVariable(i,t)}getClassName(){return"FlowGraphSetVariableBlock"}serialize(a){var i;super.serialize(a),a.config.variable=(i=this.config)==null?void 0:i.variable}}p("FlowGraphSetVariableBlock",f);export{f as FlowGraphSetVariableBlock};
//# sourceMappingURL=flowGraphSetVariableBlock.CWwy6I9C.js.map
