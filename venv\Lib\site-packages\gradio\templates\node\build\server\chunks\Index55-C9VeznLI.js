import { c as create_ssr_component, v as validate_component, d as add_attribute, m as missing_component, b as createEventDispatcher } from './ssr-C3HYbsxA.js';
import { m as mt, z as zA, _ as _e, e as bt, R as Rt, g as kt, as as Ne, j as Ke, q as j, J as pe, t as Ut } from './2-DJbI4FWc.js';
import { g as ge, h as he } from './ModifyUpload-wYzp15o5.js';
export { default as BaseExample } from './Example19-7u--UOCG.js';
import './index-ClteBeTX.js';
import './Component-NmRBwSfF.js';
import 'path';
import 'url';
import 'fs';

const Z={code:".model3D.svelte-1mxwah3{display:flex;position:relative;width:var(--size-full);height:var(--size-full);border-radius:var(--block-radius);overflow:hidden}.model3D.svelte-1mxwah3 canvas{width:var(--size-full);height:var(--size-full);object-fit:contain;overflow:hidden}",map:`{"version":3,"file":"Model3D.svelte","sources":["Model3D.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { BlockLabel, IconButton, IconButtonWrapper } from \\"@gradio/atoms\\";\\nimport { File, Download, Undo } from \\"@gradio/icons\\";\\nimport { dequal } from \\"dequal\\";\\nexport let value;\\nexport let display_mode = \\"solid\\";\\nexport let clear_color = [0, 0, 0, 0];\\nexport let label = \\"\\";\\nexport let show_label;\\nexport let i18n;\\nexport let zoom_speed = 1;\\nexport let pan_speed = 1;\\nexport let camera_position = [\\n    null,\\n    null,\\n    null\\n];\\nexport let has_change_history = false;\\nlet current_settings = { camera_position, zoom_speed, pan_speed };\\nlet use_3dgs = false;\\nlet Canvas3DGSComponent;\\nlet Canvas3DComponent;\\nasync function loadCanvas3D() {\\n    const module = await import(\\"./Canvas3D.svelte\\");\\n    return module.default;\\n}\\nasync function loadCanvas3DGS() {\\n    const module = await import(\\"./Canvas3DGS.svelte\\");\\n    return module.default;\\n}\\n$: if (value) {\\n    use_3dgs = value.path.endsWith(\\".splat\\") || value.path.endsWith(\\".ply\\");\\n    if (use_3dgs) {\\n        loadCanvas3DGS().then((component) => {\\n            Canvas3DGSComponent = component;\\n        });\\n    }\\n    else {\\n        loadCanvas3D().then((component) => {\\n            Canvas3DComponent = component;\\n        });\\n    }\\n}\\nlet canvas3d;\\nfunction handle_undo() {\\n    canvas3d?.reset_camera_position();\\n}\\n$: {\\n    if (!dequal(current_settings.camera_position, camera_position) || current_settings.zoom_speed !== zoom_speed || current_settings.pan_speed !== pan_speed) {\\n        canvas3d?.update_camera(camera_position, zoom_speed, pan_speed);\\n        current_settings = { camera_position, zoom_speed, pan_speed };\\n    }\\n}\\nlet resolved_url;\\n<\/script>\\n\\n<BlockLabel\\n\\t{show_label}\\n\\tIcon={File}\\n\\tlabel={label || i18n(\\"3D_model.3d_model\\")}\\n/>\\n{#if value}\\n\\t<div class=\\"model3D\\" data-testid=\\"model3d\\">\\n\\t\\t<IconButtonWrapper>\\n\\t\\t\\t{#if !use_3dgs}\\n\\t\\t\\t\\t<!-- Canvas3DGS doesn't implement the undo method (reset_camera_position) -->\\n\\t\\t\\t\\t<IconButton\\n\\t\\t\\t\\t\\tIcon={Undo}\\n\\t\\t\\t\\t\\tlabel=\\"Undo\\"\\n\\t\\t\\t\\t\\ton:click={() => handle_undo()}\\n\\t\\t\\t\\t\\tdisabled={!has_change_history}\\n\\t\\t\\t\\t/>\\n\\t\\t\\t{/if}\\n\\t\\t\\t<a\\n\\t\\t\\t\\thref={resolved_url}\\n\\t\\t\\t\\ttarget={window.__is_colab__ ? \\"_blank\\" : null}\\n\\t\\t\\t\\tdownload={window.__is_colab__ ? null : value.orig_name || value.path}\\n\\t\\t\\t>\\n\\t\\t\\t\\t<IconButton Icon={Download} label={i18n(\\"common.download\\")} />\\n\\t\\t\\t</a>\\n\\t\\t</IconButtonWrapper>\\n\\n\\t\\t{#if use_3dgs}\\n\\t\\t\\t<svelte:component\\n\\t\\t\\t\\tthis={Canvas3DGSComponent}\\n\\t\\t\\t\\tbind:resolved_url\\n\\t\\t\\t\\t{value}\\n\\t\\t\\t\\t{zoom_speed}\\n\\t\\t\\t\\t{pan_speed}\\n\\t\\t\\t/>\\n\\t\\t{:else}\\n\\t\\t\\t<svelte:component\\n\\t\\t\\t\\tthis={Canvas3DComponent}\\n\\t\\t\\t\\tbind:this={canvas3d}\\n\\t\\t\\t\\tbind:resolved_url\\n\\t\\t\\t\\t{value}\\n\\t\\t\\t\\t{display_mode}\\n\\t\\t\\t\\t{clear_color}\\n\\t\\t\\t\\t{camera_position}\\n\\t\\t\\t\\t{zoom_speed}\\n\\t\\t\\t\\t{pan_speed}\\n\\t\\t\\t/>\\n\\t\\t{/if}\\n\\t</div>\\n{/if}\\n\\n<style>\\n\\t.model3D {\\n\\t\\tdisplay: flex;\\n\\t\\tposition: relative;\\n\\t\\twidth: var(--size-full);\\n\\t\\theight: var(--size-full);\\n\\t\\tborder-radius: var(--block-radius);\\n\\t\\toverflow: hidden;\\n\\t}\\n\\t.model3D :global(canvas) {\\n\\t\\twidth: var(--size-full);\\n\\t\\theight: var(--size-full);\\n\\t\\tobject-fit: contain;\\n\\t\\toverflow: hidden;\\n\\t}</style>\\n"],"names":[],"mappings":"AA0GC,uBAAS,CACR,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,aAAa,CAAE,IAAI,cAAc,CAAC,CAClC,QAAQ,CAAE,MACX,CACA,uBAAQ,CAAS,MAAQ,CACxB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,UAAU,CAAE,OAAO,CACnB,QAAQ,CAAE,MACX"}`};async function J(){return (await import('./Canvas3D-DPGN8rC5.js')).default}async function N(){return (await import('./Canvas3DGS-FGrh_U4G.js')).default}const P=create_ssr_component((l,e,t,W)=>{let{value:a}=e,{display_mode:h="solid"}=e,{clear_color:C=[0,0,0,0]}=e,{label:c=""}=e,{show_label:y}=e,{i18n:v}=e,{zoom_speed:d=1}=e,{pan_speed:i=1}=e,{camera_position:s=[null,null,null]}=e,{has_change_history:u=!1}=e,_={camera_position:s,zoom_speed:d,pan_speed:i},m=!1,D,n,A,f;e.value===void 0&&t.value&&a!==void 0&&t.value(a),e.display_mode===void 0&&t.display_mode&&h!==void 0&&t.display_mode(h),e.clear_color===void 0&&t.clear_color&&C!==void 0&&t.clear_color(C),e.label===void 0&&t.label&&c!==void 0&&t.label(c),e.show_label===void 0&&t.show_label&&y!==void 0&&t.show_label(y),e.i18n===void 0&&t.i18n&&v!==void 0&&t.i18n(v),e.zoom_speed===void 0&&t.zoom_speed&&d!==void 0&&t.zoom_speed(d),e.pan_speed===void 0&&t.pan_speed&&i!==void 0&&t.pan_speed(i),e.camera_position===void 0&&t.camera_position&&s!==void 0&&t.camera_position(s),e.has_change_history===void 0&&t.has_change_history&&u!==void 0&&t.has_change_history(u),l.css.add(Z);let p,x,B=l.head;do p=!0,l.head=B,a&&(m=a.path.endsWith(".splat")||a.path.endsWith(".ply"),m?N().then(r=>{D=r;}):J().then(r=>{n=r;})),(!Ne(_.camera_position,s)||_.zoom_speed!==d||_.pan_speed!==i)&&(A?.update_camera(s,d,i),_={camera_position:s,zoom_speed:d,pan_speed:i}),x=`${validate_component(bt,"BlockLabel").$$render(l,{show_label:y,Icon:Rt,label:c||v("3D_model.3d_model")},{},{})} ${a?`<div class="model3D svelte-1mxwah3" data-testid="model3d">${validate_component(Ke,"IconButtonWrapper").$$render(l,{},{},{default:()=>`${m?"":` ${validate_component(j,"IconButton").$$render(l,{Icon:pe,label:"Undo",disabled:!u},{},{})}`} <a${add_attribute("href",f,0)}${add_attribute("target",window.__is_colab__?"_blank":null,0)}${add_attribute("download",window.__is_colab__?null:a.orig_name||a.path,0)}>${validate_component(j,"IconButton").$$render(l,{Icon:Ut,label:v("common.download")},{},{})}</a>`})} ${m?`${validate_component(D||missing_component,"svelte:component").$$render(l,{value:a,zoom_speed:d,pan_speed:i,resolved_url:f},{resolved_url:r=>{f=r,p=!1;}},{})}`:`${validate_component(n||missing_component,"svelte:component").$$render(l,{value:a,display_mode:h,clear_color:C,camera_position:s,zoom_speed:d,pan_speed:i,this:A,resolved_url:f},{this:r=>{A=r,p=!1;},resolved_url:r=>{f=r,p=!1;}},{})}`}</div>`:""}`;while(!p);return x}),V=P,$={code:".input-model.svelte-jub4pj{display:flex;position:relative;justify-content:center;align-items:center;width:var(--size-full);height:var(--size-full);border-radius:var(--block-radius);overflow:hidden}.input-model.svelte-jub4pj canvas{width:var(--size-full);height:var(--size-full);object-fit:contain;overflow:hidden}",map:'{"version":3,"file":"Model3DUpload.svelte","sources":["Model3DUpload.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { createEventDispatcher, tick } from \\"svelte\\";\\nimport { Upload, ModifyUpload } from \\"@gradio/upload\\";\\nimport { BlockLabel } from \\"@gradio/atoms\\";\\nimport { File } from \\"@gradio/icons\\";\\nexport let value;\\nexport let display_mode = \\"solid\\";\\nexport let clear_color = [0, 0, 0, 0];\\nexport let label = \\"\\";\\nexport let show_label;\\nexport let root;\\nexport let i18n;\\nexport let zoom_speed = 1;\\nexport let pan_speed = 1;\\nexport let max_file_size = null;\\nexport let uploading = false;\\nexport let camera_position = [\\n    null,\\n    null,\\n    null\\n];\\nexport let upload;\\nexport let stream_handler;\\nasync function handle_upload({ detail }) {\\n    value = detail;\\n    await tick();\\n    dispatch(\\"change\\", value);\\n    dispatch(\\"load\\", value);\\n}\\nasync function handle_clear() {\\n    value = null;\\n    await tick();\\n    dispatch(\\"clear\\");\\n    dispatch(\\"change\\");\\n}\\nlet use_3dgs = false;\\nlet Canvas3DGSComponent;\\nlet Canvas3DComponent;\\nasync function loadCanvas3D() {\\n    const module = await import(\\"./Canvas3D.svelte\\");\\n    return module.default;\\n}\\nasync function loadCanvas3DGS() {\\n    const module = await import(\\"./Canvas3DGS.svelte\\");\\n    return module.default;\\n}\\n$: if (value) {\\n    use_3dgs = value.path.endsWith(\\".splat\\") || value.path.endsWith(\\".ply\\");\\n    if (use_3dgs) {\\n        loadCanvas3DGS().then((component) => {\\n            Canvas3DGSComponent = component;\\n        });\\n    }\\n    else {\\n        loadCanvas3D().then((component) => {\\n            Canvas3DComponent = component;\\n        });\\n    }\\n}\\nlet canvas3d;\\nasync function handle_undo() {\\n    canvas3d?.reset_camera_position();\\n}\\nconst dispatch = createEventDispatcher();\\nlet dragging = false;\\n$: dispatch(\\"drag\\", dragging);\\n<\/script>\\n\\n<BlockLabel {show_label} Icon={File} label={label || \\"3D Model\\"} />\\n\\n{#if value === null}\\n\\t<Upload\\n\\t\\t{upload}\\n\\t\\t{stream_handler}\\n\\t\\ton:load={handle_upload}\\n\\t\\t{root}\\n\\t\\t{max_file_size}\\n\\t\\tfiletype={[\\".stl\\", \\".obj\\", \\".gltf\\", \\".glb\\", \\"model/obj\\", \\".splat\\", \\".ply\\"]}\\n\\t\\tbind:dragging\\n\\t\\tbind:uploading\\n\\t\\ton:error\\n\\t\\taria_label={i18n(\\"model3d.drop_to_upload\\")}\\n\\t>\\n\\t\\t<slot />\\n\\t</Upload>\\n{:else}\\n\\t<div class=\\"input-model\\">\\n\\t\\t<ModifyUpload\\n\\t\\t\\tundoable={!use_3dgs}\\n\\t\\t\\ton:clear={handle_clear}\\n\\t\\t\\t{i18n}\\n\\t\\t\\ton:undo={handle_undo}\\n\\t\\t/>\\n\\n\\t\\t{#if use_3dgs}\\n\\t\\t\\t<svelte:component\\n\\t\\t\\t\\tthis={Canvas3DGSComponent}\\n\\t\\t\\t\\t{value}\\n\\t\\t\\t\\t{zoom_speed}\\n\\t\\t\\t\\t{pan_speed}\\n\\t\\t\\t/>\\n\\t\\t{:else}\\n\\t\\t\\t<svelte:component\\n\\t\\t\\t\\tthis={Canvas3DComponent}\\n\\t\\t\\t\\tbind:this={canvas3d}\\n\\t\\t\\t\\t{value}\\n\\t\\t\\t\\t{display_mode}\\n\\t\\t\\t\\t{clear_color}\\n\\t\\t\\t\\t{camera_position}\\n\\t\\t\\t\\t{zoom_speed}\\n\\t\\t\\t\\t{pan_speed}\\n\\t\\t\\t/>\\n\\t\\t{/if}\\n\\t</div>\\n{/if}\\n\\n<style>\\n\\t.input-model {\\n\\t\\tdisplay: flex;\\n\\t\\tposition: relative;\\n\\t\\tjustify-content: center;\\n\\t\\talign-items: center;\\n\\t\\twidth: var(--size-full);\\n\\t\\theight: var(--size-full);\\n\\t\\tborder-radius: var(--block-radius);\\n\\t\\toverflow: hidden;\\n\\t}\\n\\n\\t.input-model :global(canvas) {\\n\\t\\twidth: var(--size-full);\\n\\t\\theight: var(--size-full);\\n\\t\\tobject-fit: contain;\\n\\t\\toverflow: hidden;\\n\\t}</style>\\n"],"names":[],"mappings":"AAoHC,0BAAa,CACZ,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,aAAa,CAAE,IAAI,cAAc,CAAC,CAClC,QAAQ,CAAE,MACX,CAEA,0BAAY,CAAS,MAAQ,CAC5B,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,UAAU,CAAE,OAAO,CACnB,QAAQ,CAAE,MACX"}'};async function ee(){return (await import('./Canvas3D-DPGN8rC5.js')).default}async function te(){return (await import('./Canvas3DGS-FGrh_U4G.js')).default}const le=create_ssr_component((l,e,t,W)=>{let{value:a}=e,{display_mode:h="solid"}=e,{clear_color:C=[0,0,0,0]}=e,{label:c=""}=e,{show_label:y}=e,{root:v}=e,{i18n:d}=e,{zoom_speed:i=1}=e,{pan_speed:s=1}=e,{max_file_size:u=null}=e,{uploading:_=!1}=e,{camera_position:m=[null,null,null]}=e,{upload:D}=e,{stream_handler:n}=e,A=!1,f,p,x;const B=createEventDispatcher();let r=!1;e.value===void 0&&t.value&&a!==void 0&&t.value(a),e.display_mode===void 0&&t.display_mode&&h!==void 0&&t.display_mode(h),e.clear_color===void 0&&t.clear_color&&C!==void 0&&t.clear_color(C),e.label===void 0&&t.label&&c!==void 0&&t.label(c),e.show_label===void 0&&t.show_label&&y!==void 0&&t.show_label(y),e.root===void 0&&t.root&&v!==void 0&&t.root(v),e.i18n===void 0&&t.i18n&&d!==void 0&&t.i18n(d),e.zoom_speed===void 0&&t.zoom_speed&&i!==void 0&&t.zoom_speed(i),e.pan_speed===void 0&&t.pan_speed&&s!==void 0&&t.pan_speed(s),e.max_file_size===void 0&&t.max_file_size&&u!==void 0&&t.max_file_size(u),e.uploading===void 0&&t.uploading&&_!==void 0&&t.uploading(_),e.camera_position===void 0&&t.camera_position&&m!==void 0&&t.camera_position(m),e.upload===void 0&&t.upload&&D!==void 0&&t.upload(D),e.stream_handler===void 0&&t.stream_handler&&n!==void 0&&t.stream_handler(n),l.css.add($);let z,E,I=l.head;do z=!0,l.head=I,a&&(A=a.path.endsWith(".splat")||a.path.endsWith(".ply"),A?te().then(w=>{f=w;}):ee().then(w=>{p=w;})),B("drag",r),E=`${validate_component(bt,"BlockLabel").$$render(l,{show_label:y,Icon:Rt,label:c||"3D Model"},{},{})} ${a===null?`${validate_component(ge,"Upload").$$render(l,{upload:D,stream_handler:n,root:v,max_file_size:u,filetype:[".stl",".obj",".gltf",".glb","model/obj",".splat",".ply"],aria_label:d("model3d.drop_to_upload"),dragging:r,uploading:_},{dragging:w=>{r=w,z=!1;},uploading:w=>{_=w,z=!1;}},{default:()=>`${W.default?W.default({}):""}`})}`:`<div class="input-model svelte-jub4pj">${validate_component(he,"ModifyUpload").$$render(l,{undoable:!A,i18n:d},{},{})} ${A?`${validate_component(f||missing_component,"svelte:component").$$render(l,{value:a,zoom_speed:i,pan_speed:s},{},{})}`:`${validate_component(p||missing_component,"svelte:component").$$render(l,{value:a,display_mode:h,clear_color:C,camera_position:m,zoom_speed:i,pan_speed:s,this:x},{this:w=>{x=w,z=!1;}},{})}`}</div>`}`;while(!z);return E}),ae=le,ce=create_ssr_component((l,e,t,W)=>{let{elem_id:a=""}=e,{elem_classes:h=[]}=e,{visible:C=!0}=e,{value:c=null}=e,{root:y}=e,{display_mode:v="solid"}=e,{clear_color:d}=e,{loading_status:i}=e,{label:s}=e,{show_label:u}=e,{container:_=!0}=e,{scale:m=null}=e,{min_width:D=void 0}=e,{gradio:n}=e,{height:A=void 0}=e,{zoom_speed:f=1}=e,{input_ready:p}=e,x=!1,{has_change_history:B=!1}=e,{camera_position:r=[null,null,null]}=e,{interactive:z}=e;const E=typeof window<"u";e.elem_id===void 0&&t.elem_id&&a!==void 0&&t.elem_id(a),e.elem_classes===void 0&&t.elem_classes&&h!==void 0&&t.elem_classes(h),e.visible===void 0&&t.visible&&C!==void 0&&t.visible(C),e.value===void 0&&t.value&&c!==void 0&&t.value(c),e.root===void 0&&t.root&&y!==void 0&&t.root(y),e.display_mode===void 0&&t.display_mode&&v!==void 0&&t.display_mode(v),e.clear_color===void 0&&t.clear_color&&d!==void 0&&t.clear_color(d),e.loading_status===void 0&&t.loading_status&&i!==void 0&&t.loading_status(i),e.label===void 0&&t.label&&s!==void 0&&t.label(s),e.show_label===void 0&&t.show_label&&u!==void 0&&t.show_label(u),e.container===void 0&&t.container&&_!==void 0&&t.container(_),e.scale===void 0&&t.scale&&m!==void 0&&t.scale(m),e.min_width===void 0&&t.min_width&&D!==void 0&&t.min_width(D),e.gradio===void 0&&t.gradio&&n!==void 0&&t.gradio(n),e.height===void 0&&t.height&&A!==void 0&&t.height(A),e.zoom_speed===void 0&&t.zoom_speed&&f!==void 0&&t.zoom_speed(f),e.input_ready===void 0&&t.input_ready&&p!==void 0&&t.input_ready(p),e.has_change_history===void 0&&t.has_change_history&&B!==void 0&&t.has_change_history(B),e.camera_position===void 0&&t.camera_position&&r!==void 0&&t.camera_position(r),e.interactive===void 0&&t.interactive&&z!==void 0&&t.interactive(z);let I,w,O=l.head;do I=!0,l.head=O,p=!x,w=`${z?`${validate_component(mt,"Block").$$render(l,{visible:C,variant:c===null?"dashed":"solid",border_mode:"base",padding:!1,elem_id:a,elem_classes:h,container:_,scale:m,min_width:D,height:A},{},{default:()=>`${validate_component(zA,"StatusTracker").$$render(l,Object.assign({},{autoscroll:n.autoscroll},{i18n:n.i18n},i),{},{})} ${validate_component(ae,"Model3DUpload").$$render(l,{label:s,show_label:u,root:y,display_mode:v,clear_color:d,value:c,camera_position:r,zoom_speed:f,i18n:n.i18n,max_file_size:n.max_file_size,upload:(...M)=>n.client.upload(...M),stream_handler:(...M)=>n.client.stream(...M),uploading:x},{uploading:M=>{x=M,I=!1;}},{default:()=>`${validate_component(_e,"UploadText").$$render(l,{i18n:n.i18n,type:"file"},{},{})}`})}`})}`:`${validate_component(mt,"Block").$$render(l,{visible:C,variant:c===null?"dashed":"solid",border_mode:"base",padding:!1,elem_id:a,elem_classes:h,container:_,scale:m,min_width:D,height:A},{},{default:()=>`${validate_component(zA,"StatusTracker").$$render(l,Object.assign({},{autoscroll:n.autoscroll},{i18n:n.i18n},i),{},{})} ${c&&E?`${validate_component(V,"Model3D").$$render(l,{value:c,i18n:n.i18n,display_mode:v,clear_color:d,label:s,show_label:u,camera_position:r,zoom_speed:f,has_change_history:B},{},{})}`:` ${validate_component(bt,"BlockLabel").$$render(l,{show_label:u,Icon:Rt,label:s||"3D Model"},{},{})} ${validate_component(kt,"Empty").$$render(l,{unpadded_box:!0,size:"large"},{},{default:()=>`${validate_component(Rt,"File").$$render(l,{},{},{})}`})}`}`})}`}`;while(!I);return w});

export { V as BaseModel3D, ae as BaseModel3DUpload, ce as default };
//# sourceMappingURL=Index55-C9VeznLI.js.map
