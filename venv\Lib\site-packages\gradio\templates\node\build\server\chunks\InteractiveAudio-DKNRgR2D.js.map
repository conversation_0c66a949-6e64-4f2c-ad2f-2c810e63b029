{"version": 3, "file": "InteractiveAudio-DKNRgR2D.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/InteractiveAudio.js"], "sourcesContent": ["import{create_ssr_component as U,escape as I,each as ut,add_attribute as y,validate_component as w,add_styles as vt,null_to_empty as mt}from\"svelte/internal\";import{createEventDispatcher as F,onMount as it,onDestroy as pt}from\"svelte\";import{M as rt,U as ft}from\"./ModifyUpload.js\";import{n as gt}from\"./client.js\";import{x as ht,t as bt,B as _t,M as wt,o as It}from\"./FullscreenButton.js\";import{a as Bt}from\"./StreamingBar.js\";import{s as At,A as Et}from\"./AudioPlayer.js\";function N(A,t,e,o){return new(e||(e=Promise))(function(n,r){function l(a){try{i(o.next(a))}catch(c){r(c)}}function s(a){try{i(o.throw(a))}catch(c){r(c)}}function i(a){var c;a.done?n(a.value):(c=a.value,c instanceof e?c:new e(function(C){C(c)})).then(l,s)}i((o=o.apply(A,[])).next())})}class yt{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(t,e,o){if(this.listeners[t]||(this.listeners[t]=new Set),this.listeners[t].add(e),o?.once){const n=()=>{this.removeEventListener(t,n),this.removeEventListener(t,e)};return this.addEventListener(t,n),n}return()=>this.removeEventListener(t,e)}removeEventListener(t,e){var o;(o=this.listeners[t])===null||o===void 0||o.delete(e)}once(t,e){return this.on(t,e,{once:!0})}unAll(){this.listeners={}}emit(t,...e){this.listeners[t]&&this.listeners[t].forEach(o=>o(...e))}}class xt extends yt{constructor(t){super(),this.subscriptions=[],this.options=t}onInit(){}init(t){this.wavesurfer=t,this.onInit()}destroy(){this.emit(\"destroy\"),this.subscriptions.forEach(t=>t())}}const Mt=[\"audio/webm\",\"audio/wav\",\"audio/mpeg\",\"audio/mp4\",\"audio/mp3\"];class Z extends xt{constructor(t){var e;super(Object.assign(Object.assign({},t),{audioBitsPerSecond:(e=t.audioBitsPerSecond)!==null&&e!==void 0?e:128e3})),this.stream=null,this.mediaRecorder=null}static create(t){return new Z(t||{})}renderMicStream(t){const e=new AudioContext,o=e.createMediaStreamSource(t),n=e.createAnalyser();o.connect(n);const r=n.frequencyBinCount,l=new Float32Array(r),s=r/e.sampleRate;let i;const a=()=>{n.getFloatTimeDomainData(l),this.wavesurfer&&(this.wavesurfer.options.cursorWidth=0,this.wavesurfer.options.interact=!1,this.wavesurfer.load(\"\",[l],s)),i=requestAnimationFrame(a)};return a(),()=>{cancelAnimationFrame(i),o?.disconnect(),e?.close()}}startMic(t){return N(this,void 0,void 0,function*(){let e;try{e=yield navigator.mediaDevices.getUserMedia({audio:!t?.deviceId||{deviceId:t.deviceId}})}catch(n){throw new Error(\"Error accessing the microphone: \"+n.message)}const o=this.renderMicStream(e);return this.subscriptions.push(this.once(\"destroy\",o)),this.stream=e,e})}stopMic(){this.stream&&(this.stream.getTracks().forEach(t=>t.stop()),this.stream=null,this.mediaRecorder=null)}startRecording(t){return N(this,void 0,void 0,function*(){const e=this.stream||(yield this.startMic(t)),o=this.mediaRecorder||new MediaRecorder(e,{mimeType:this.options.mimeType||Mt.find(r=>MediaRecorder.isTypeSupported(r)),audioBitsPerSecond:this.options.audioBitsPerSecond});this.mediaRecorder=o,this.stopRecording();const n=[];o.ondataavailable=r=>{r.data.size>0&&n.push(r.data)},o.onstop=()=>{var r;const l=new Blob(n,{type:o.mimeType});this.emit(\"record-end\",l),this.options.renderRecordedAudio!==!1&&((r=this.wavesurfer)===null||r===void 0||r.load(URL.createObjectURL(l)))},o.start(),this.emit(\"record-start\")})}isRecording(){var t;return((t=this.mediaRecorder)===null||t===void 0?void 0:t.state)===\"recording\"}isPaused(){var t;return((t=this.mediaRecorder)===null||t===void 0?void 0:t.state)===\"paused\"}stopRecording(){var t;this.isRecording()&&((t=this.mediaRecorder)===null||t===void 0||t.stop())}pauseRecording(){var t;this.isRecording()&&((t=this.mediaRecorder)===null||t===void 0||t.pause(),this.emit(\"record-pause\"))}resumeRecording(){var t;this.isPaused()&&((t=this.mediaRecorder)===null||t===void 0||t.resume(),this.emit(\"record-resume\"))}static getAvailableAudioDevices(){return N(this,void 0,void 0,function*(){return navigator.mediaDevices.enumerateDevices().then(t=>t.filter(e=>e.kind===\"audioinput\"))})}destroy(){super.destroy(),this.stopRecording(),this.stopMic()}}const kt={code:\".mic-select.svelte-1ya9x7a{height:var(--size-8);background:var(--block-background-fill);padding:0px var(--spacing-xxl);border-radius:var(--button-large-radius);font-size:var(--text-md);border:1px solid var(--block-border-color);gap:var(--size-1)}select.svelte-1ya9x7a{text-overflow:ellipsis;max-width:var(--size-40)}@media(max-width: 375px){select.svelte-1ya9x7a{width:100%}}\",map:'{\"version\":3,\"file\":\"DeviceSelect.svelte\",\"sources\":[\"DeviceSelect.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import RecordPlugin from \\\\\"wavesurfer.js/dist/plugins/record.js\\\\\";\\\\nimport { createEventDispatcher } from \\\\\"svelte\\\\\";\\\\nexport let i18n;\\\\nexport let micDevices = [];\\\\nconst dispatch = createEventDispatcher();\\\\n$: if (typeof window !== \\\\\"undefined\\\\\") {\\\\n    try {\\\\n        let tempDevices = [];\\\\n        RecordPlugin.getAvailableAudioDevices().then((devices) => {\\\\n            micDevices = devices;\\\\n            devices.forEach((device) => {\\\\n                if (device.deviceId) {\\\\n                    tempDevices.push(device);\\\\n                }\\\\n            });\\\\n            micDevices = tempDevices;\\\\n        });\\\\n    }\\\\n    catch (err) {\\\\n        if (err instanceof DOMException && err.name == \\\\\"NotAllowedError\\\\\") {\\\\n            dispatch(\\\\\"error\\\\\", i18n(\\\\\"audio.allow_recording_access\\\\\"));\\\\n        }\\\\n        throw err;\\\\n    }\\\\n}\\\\n<\\/script>\\\\n\\\\n<select\\\\n\\\\tclass=\\\\\"mic-select\\\\\"\\\\n\\\\taria-label=\\\\\"Select input device\\\\\"\\\\n\\\\tdisabled={micDevices.length === 0}\\\\n>\\\\n\\\\t{#if micDevices.length === 0}\\\\n\\\\t\\\\t<option value=\\\\\"\\\\\">{i18n(\\\\\"audio.no_microphone\\\\\")}</option>\\\\n\\\\t{:else}\\\\n\\\\t\\\\t{#each micDevices as micDevice}\\\\n\\\\t\\\\t\\\\t<option value={micDevice.deviceId}>{micDevice.label}</option>\\\\n\\\\t\\\\t{/each}\\\\n\\\\t{/if}\\\\n</select>\\\\n\\\\n<style>\\\\n\\\\t.mic-select {\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\tbackground: var(--block-background-fill);\\\\n\\\\t\\\\tpadding: 0px var(--spacing-xxl);\\\\n\\\\t\\\\tborder-radius: var(--button-large-radius);\\\\n\\\\t\\\\tfont-size: var(--text-md);\\\\n\\\\t\\\\tborder: 1px solid var(--block-border-color);\\\\n\\\\t\\\\tgap: var(--size-1);\\\\n\\\\t}\\\\n\\\\n\\\\tselect {\\\\n\\\\t\\\\ttext-overflow: ellipsis;\\\\n\\\\t\\\\tmax-width: var(--size-40);\\\\n\\\\t}\\\\n\\\\n\\\\t@media (max-width: 375px) {\\\\n\\\\t\\\\tselect {\\\\n\\\\t\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\t}\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA0CC,0BAAY,CACX,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,UAAU,CAAE,IAAI,uBAAuB,CAAC,CACxC,OAAO,CAAE,GAAG,CAAC,IAAI,aAAa,CAAC,CAC/B,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CAC3C,GAAG,CAAE,IAAI,QAAQ,CAClB,CAEA,qBAAO,CACN,aAAa,CAAE,QAAQ,CACvB,SAAS,CAAE,IAAI,SAAS,CACzB,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,qBAAO,CACN,KAAK,CAAE,IACR,CACD\"}'},at=U((A,t,e,o)=>{let{i18n:n}=t,{micDevices:r=[]}=t;const l=F();if(t.i18n===void 0&&e.i18n&&n!==void 0&&e.i18n(n),t.micDevices===void 0&&e.micDevices&&r!==void 0&&e.micDevices(r),A.css.add(kt),typeof window<\"u\")try{let s=[];Z.getAvailableAudioDevices().then(i=>{r=i,i.forEach(a=>{a.deviceId&&s.push(a)}),r=s})}catch(s){throw s instanceof DOMException&&s.name==\"NotAllowedError\"&&l(\"error\",n(\"audio.allow_recording_access\")),s}return`<select class=\"mic-select svelte-1ya9x7a\" aria-label=\"Select input device\" ${r.length===0?\"disabled\":\"\"}>${r.length===0?`<option value=\"\">${I(n(\"audio.no_microphone\"))}</option>`:`${ut(r,s=>`<option${y(\"value\",s.deviceId,0)}>${I(s.label)}</option>`)}`}</select>`}),zt={code:'.controls.svelte-1oiuk2f{display:flex;align-items:center;justify-content:space-between;flex-wrap:wrap}.wrapper.svelte-1oiuk2f{display:flex;align-items:center;flex-wrap:wrap}.record.svelte-1oiuk2f{margin-right:var(--spacing-md)}.stop-button-paused.svelte-1oiuk2f{display:none;height:var(--size-8);width:var(--size-20);background-color:var(--block-background-fill);border-radius:var(--button-large-radius);align-items:center;border:1px solid var(--block-border-color);margin:var(--size-1) var(--size-1) 0 0}.stop-button-paused.svelte-1oiuk2f::before{content:\"\";height:var(--size-4);width:var(--size-4);border-radius:var(--radius-full);background:var(--primary-600);margin:0 var(--spacing-xl)}.stop-button.svelte-1oiuk2f::before{content:\"\";height:var(--size-4);width:var(--size-4);border-radius:var(--radius-full);background:var(--primary-600);margin:0 var(--spacing-xl);animation:svelte-1oiuk2f-scaling 1800ms infinite}.stop-button.svelte-1oiuk2f{display:none;height:var(--size-8);width:var(--size-20);background-color:var(--block-background-fill);border-radius:var(--button-large-radius);align-items:center;border:1px solid var(--primary-600);margin:var(--size-1) var(--size-1) 0 0}.record-button.svelte-1oiuk2f::before{content:\"\";height:var(--size-4);width:var(--size-4);border-radius:var(--radius-full);background:var(--primary-600);margin:0 var(--spacing-xl)}.record-button.svelte-1oiuk2f{height:var(--size-8);width:var(--size-24);background-color:var(--block-background-fill);border-radius:var(--button-large-radius);display:flex;align-items:center;border:1px solid var(--block-border-color)}.duration-button.svelte-1oiuk2f{border-radius:var(--button-large-radius)}.stop-button.svelte-1oiuk2f:disabled{cursor:not-allowed}.record-button.svelte-1oiuk2f:disabled{cursor:not-allowed;opacity:0.5}@keyframes svelte-1oiuk2f-scaling{0%{background-color:var(--primary-600);scale:1}50%{background-color:var(--primary-600);scale:1.2}100%{background-color:var(--primary-600);scale:1}}.pause-button.svelte-1oiuk2f{display:none;height:var(--size-8);width:var(--size-20);border:1px solid var(--block-border-color);border-radius:var(--button-large-radius);padding:var(--spacing-md);margin:var(--size-1) var(--size-1) 0 0}.resume-button.svelte-1oiuk2f{display:none;height:var(--size-8);width:var(--size-20);border:1px solid var(--block-border-color);border-radius:var(--button-large-radius);padding:var(--spacing-xl);line-height:1px;font-size:var(--text-md);margin:var(--size-1) var(--size-1) 0 0}.duration.svelte-1oiuk2f{display:flex;height:var(--size-8);width:var(--size-20);border:1px solid var(--block-border-color);padding:var(--spacing-md);align-items:center;justify-content:center;margin:var(--size-1) var(--size-1) 0 0}::part(region){border-radius:var(--radius-md);height:98% !important;border:1px solid var(--trim-region-color);background-color:unset;border-width:1px 3px}::part(region)::after{content:\"\";position:absolute;top:0;left:0;width:100%;height:100%;background:var(--trim-region-color);opacity:0.2;border-radius:var(--radius-md)}::part(region-handle){width:5px !important;border:none}',map:`{\"version\":3,\"file\":\"WaveformRecordControls.svelte\",\"sources\":[\"WaveformRecordControls.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { Pause } from \\\\\"@gradio/icons\\\\\";\\\\nimport RecordPlugin from \\\\\"wavesurfer.js/dist/plugins/record.js\\\\\";\\\\nimport DeviceSelect from \\\\\"./DeviceSelect.svelte\\\\\";\\\\nexport let record;\\\\nexport let i18n;\\\\nexport let recording = false;\\\\nlet micDevices = [];\\\\nlet recordButton;\\\\nlet pauseButton;\\\\nlet resumeButton;\\\\nlet stopButton;\\\\nlet stopButtonPaused;\\\\nlet recording_ongoing = false;\\\\nexport let record_time;\\\\nexport let show_recording_waveform;\\\\nexport let timing = false;\\\\n$: record.on(\\\\\"record-start\\\\\", () => {\\\\n    record.startMic();\\\\n    recordButton.style.display = \\\\\"none\\\\\";\\\\n    stopButton.style.display = \\\\\"flex\\\\\";\\\\n    pauseButton.style.display = \\\\\"block\\\\\";\\\\n});\\\\n$: record.on(\\\\\"record-end\\\\\", () => {\\\\n    if (record.isPaused()) {\\\\n        record.resumeRecording();\\\\n        record.stopRecording();\\\\n    }\\\\n    record.stopMic();\\\\n    recordButton.style.display = \\\\\"flex\\\\\";\\\\n    stopButton.style.display = \\\\\"none\\\\\";\\\\n    pauseButton.style.display = \\\\\"none\\\\\";\\\\n    recordButton.disabled = false;\\\\n});\\\\n$: record.on(\\\\\"record-pause\\\\\", () => {\\\\n    pauseButton.style.display = \\\\\"none\\\\\";\\\\n    resumeButton.style.display = \\\\\"block\\\\\";\\\\n    stopButton.style.display = \\\\\"none\\\\\";\\\\n    stopButtonPaused.style.display = \\\\\"flex\\\\\";\\\\n});\\\\n$: record.on(\\\\\"record-resume\\\\\", () => {\\\\n    pauseButton.style.display = \\\\\"block\\\\\";\\\\n    resumeButton.style.display = \\\\\"none\\\\\";\\\\n    recordButton.style.display = \\\\\"none\\\\\";\\\\n    stopButton.style.display = \\\\\"flex\\\\\";\\\\n    stopButtonPaused.style.display = \\\\\"none\\\\\";\\\\n});\\\\n$: if (recording && !recording_ongoing) {\\\\n    record.startRecording();\\\\n    recording_ongoing = true;\\\\n}\\\\nelse {\\\\n    record.stopRecording();\\\\n    recording_ongoing = false;\\\\n}\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"controls\\\\\">\\\\n\\\\t<div class=\\\\\"wrapper\\\\\">\\\\n\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\tbind:this={recordButton}\\\\n\\\\t\\\\t\\\\tclass=\\\\\"record record-button\\\\\"\\\\n\\\\t\\\\t\\\\ton:click={() => record.startRecording()}>{i18n(\\\\\"audio.record\\\\\")}</button\\\\n\\\\t\\\\t>\\\\n\\\\n\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\tbind:this={stopButton}\\\\n\\\\t\\\\t\\\\tclass=\\\\\"stop-button {record.isPaused() ? 'stop-button-paused' : ''}\\\\\"\\\\n\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\tif (record.isPaused()) {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\trecord.resumeRecording();\\\\n\\\\t\\\\t\\\\t\\\\t\\\\trecord.stopRecording();\\\\n\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\n\\\\t\\\\t\\\\t\\\\trecord.stopRecording();\\\\n\\\\t\\\\t\\\\t}}>{i18n(\\\\\"audio.stop\\\\\")}</button\\\\n\\\\t\\\\t>\\\\n\\\\n\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\tbind:this={stopButtonPaused}\\\\n\\\\t\\\\t\\\\tid=\\\\\"stop-paused\\\\\"\\\\n\\\\t\\\\t\\\\tclass=\\\\\"stop-button-paused\\\\\"\\\\n\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\tif (record.isPaused()) {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\trecord.resumeRecording();\\\\n\\\\t\\\\t\\\\t\\\\t\\\\trecord.stopRecording();\\\\n\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\n\\\\t\\\\t\\\\t\\\\trecord.stopRecording();\\\\n\\\\t\\\\t\\\\t}}>{i18n(\\\\\"audio.stop\\\\\")}</button\\\\n\\\\t\\\\t>\\\\n\\\\n\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\taria-label=\\\\\"pause\\\\\"\\\\n\\\\t\\\\t\\\\tbind:this={pauseButton}\\\\n\\\\t\\\\t\\\\tclass=\\\\\"pause-button\\\\\"\\\\n\\\\t\\\\t\\\\ton:click={() => record.pauseRecording()}><Pause /></button\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\tbind:this={resumeButton}\\\\n\\\\t\\\\t\\\\tclass=\\\\\"resume-button\\\\\"\\\\n\\\\t\\\\t\\\\ton:click={() => record.resumeRecording()}>{i18n(\\\\\"audio.resume\\\\\")}</button\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t{#if timing && !show_recording_waveform}\\\\n\\\\t\\\\t\\\\t<time class=\\\\\"duration-button duration\\\\\">{record_time}</time>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t</div>\\\\n\\\\t<DeviceSelect bind:micDevices {i18n} />\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.controls {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\tflex-wrap: wrap;\\\\n\\\\t}\\\\n\\\\n\\\\t.wrapper {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tflex-wrap: wrap;\\\\n\\\\t}\\\\n\\\\n\\\\t.record {\\\\n\\\\t\\\\tmargin-right: var(--spacing-md);\\\\n\\\\t}\\\\n\\\\n\\\\t.stop-button-paused {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tborder-radius: var(--button-large-radius);\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tborder: 1px solid var(--block-border-color);\\\\n\\\\t\\\\tmargin: var(--size-1) var(--size-1) 0 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.stop-button-paused::before {\\\\n\\\\t\\\\tcontent: \\\\\"\\\\\";\\\\n\\\\t\\\\theight: var(--size-4);\\\\n\\\\t\\\\twidth: var(--size-4);\\\\n\\\\t\\\\tborder-radius: var(--radius-full);\\\\n\\\\t\\\\tbackground: var(--primary-600);\\\\n\\\\t\\\\tmargin: 0 var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\t.stop-button::before {\\\\n\\\\t\\\\tcontent: \\\\\"\\\\\";\\\\n\\\\t\\\\theight: var(--size-4);\\\\n\\\\t\\\\twidth: var(--size-4);\\\\n\\\\t\\\\tborder-radius: var(--radius-full);\\\\n\\\\t\\\\tbackground: var(--primary-600);\\\\n\\\\t\\\\tmargin: 0 var(--spacing-xl);\\\\n\\\\t\\\\tanimation: scaling 1800ms infinite;\\\\n\\\\t}\\\\n\\\\n\\\\t.stop-button {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tborder-radius: var(--button-large-radius);\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tborder: 1px solid var(--primary-600);\\\\n\\\\t\\\\tmargin: var(--size-1) var(--size-1) 0 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.record-button::before {\\\\n\\\\t\\\\tcontent: \\\\\"\\\\\";\\\\n\\\\t\\\\theight: var(--size-4);\\\\n\\\\t\\\\twidth: var(--size-4);\\\\n\\\\t\\\\tborder-radius: var(--radius-full);\\\\n\\\\t\\\\tbackground: var(--primary-600);\\\\n\\\\t\\\\tmargin: 0 var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.record-button {\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\twidth: var(--size-24);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tborder-radius: var(--button-large-radius);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tborder: 1px solid var(--block-border-color);\\\\n\\\\t}\\\\n\\\\n\\\\t.duration-button {\\\\n\\\\t\\\\tborder-radius: var(--button-large-radius);\\\\n\\\\t}\\\\n\\\\n\\\\t.stop-button:disabled {\\\\n\\\\t\\\\tcursor: not-allowed;\\\\n\\\\t}\\\\n\\\\n\\\\t.record-button:disabled {\\\\n\\\\t\\\\tcursor: not-allowed;\\\\n\\\\t\\\\topacity: 0.5;\\\\n\\\\t}\\\\n\\\\n\\\\t@keyframes scaling {\\\\n\\\\t\\\\t0% {\\\\n\\\\t\\\\t\\\\tbackground-color: var(--primary-600);\\\\n\\\\t\\\\t\\\\tscale: 1;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t50% {\\\\n\\\\t\\\\t\\\\tbackground-color: var(--primary-600);\\\\n\\\\t\\\\t\\\\tscale: 1.2;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t100% {\\\\n\\\\t\\\\t\\\\tbackground-color: var(--primary-600);\\\\n\\\\t\\\\t\\\\tscale: 1;\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.pause-button {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\tborder: 1px solid var(--block-border-color);\\\\n\\\\t\\\\tborder-radius: var(--button-large-radius);\\\\n\\\\t\\\\tpadding: var(--spacing-md);\\\\n\\\\t\\\\tmargin: var(--size-1) var(--size-1) 0 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.resume-button {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\tborder: 1px solid var(--block-border-color);\\\\n\\\\t\\\\tborder-radius: var(--button-large-radius);\\\\n\\\\t\\\\tpadding: var(--spacing-xl);\\\\n\\\\t\\\\tline-height: 1px;\\\\n\\\\t\\\\tfont-size: var(--text-md);\\\\n\\\\t\\\\tmargin: var(--size-1) var(--size-1) 0 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.duration {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\tborder: 1px solid var(--block-border-color);\\\\n\\\\t\\\\tpadding: var(--spacing-md);\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\tmargin: var(--size-1) var(--size-1) 0 0;\\\\n\\\\t}\\\\n\\\\n\\\\t:global(::part(region)) {\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t\\\\theight: 98% !important;\\\\n\\\\t\\\\tborder: 1px solid var(--trim-region-color);\\\\n\\\\t\\\\tbackground-color: unset;\\\\n\\\\t\\\\tborder-width: 1px 3px;\\\\n\\\\t}\\\\n\\\\n\\\\t:global(::part(region))::after {\\\\n\\\\t\\\\tcontent: \\\\\"\\\\\";\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\ttop: 0;\\\\n\\\\t\\\\tleft: 0;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tbackground: var(--trim-region-color);\\\\n\\\\t\\\\topacity: 0.2;\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t}\\\\n\\\\n\\\\t:global(::part(region-handle)) {\\\\n\\\\t\\\\twidth: 5px !important;\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA8GC,wBAAU,CACT,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,aAAa,CAC9B,SAAS,CAAE,IACZ,CAEA,uBAAS,CACR,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,SAAS,CAAE,IACZ,CAEA,sBAAQ,CACP,YAAY,CAAE,IAAI,YAAY,CAC/B,CAEA,kCAAoB,CACnB,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CAC3C,MAAM,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CACvC,CAEA,kCAAmB,QAAS,CAC3B,OAAO,CAAE,EAAE,CACX,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,aAAa,CAAE,IAAI,aAAa,CAAC,CACjC,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,MAAM,CAAE,CAAC,CAAC,IAAI,YAAY,CAC3B,CACA,2BAAY,QAAS,CACpB,OAAO,CAAE,EAAE,CACX,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,aAAa,CAAE,IAAI,aAAa,CAAC,CACjC,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,MAAM,CAAE,CAAC,CAAC,IAAI,YAAY,CAAC,CAC3B,SAAS,CAAE,sBAAO,CAAC,MAAM,CAAC,QAC3B,CAEA,2BAAa,CACZ,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,CACpC,MAAM,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CACvC,CAEA,6BAAc,QAAS,CACtB,OAAO,CAAE,EAAE,CACX,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,aAAa,CAAE,IAAI,aAAa,CAAC,CACjC,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,MAAM,CAAE,CAAC,CAAC,IAAI,YAAY,CAC3B,CAEA,6BAAe,CACd,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAC3C,CAEA,+BAAiB,CAChB,aAAa,CAAE,IAAI,qBAAqB,CACzC,CAEA,2BAAY,SAAU,CACrB,MAAM,CAAE,WACT,CAEA,6BAAc,SAAU,CACvB,MAAM,CAAE,WAAW,CACnB,OAAO,CAAE,GACV,CAEA,WAAW,sBAAQ,CAClB,EAAG,CACF,gBAAgB,CAAE,IAAI,aAAa,CAAC,CACpC,KAAK,CAAE,CACR,CACA,GAAI,CACH,gBAAgB,CAAE,IAAI,aAAa,CAAC,CACpC,KAAK,CAAE,GACR,CACA,IAAK,CACJ,gBAAgB,CAAE,IAAI,aAAa,CAAC,CACpC,KAAK,CAAE,CACR,CACD,CAEA,4BAAc,CACb,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CAC3C,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,OAAO,CAAE,IAAI,YAAY,CAAC,CAC1B,MAAM,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CACvC,CAEA,6BAAe,CACd,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CAC3C,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,OAAO,CAAE,IAAI,YAAY,CAAC,CAC1B,WAAW,CAAE,GAAG,CAChB,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,MAAM,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CACvC,CAEA,wBAAU,CACT,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CAC3C,OAAO,CAAE,IAAI,YAAY,CAAC,CAC1B,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,MAAM,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CACvC,CAEQ,cAAgB,CACvB,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,MAAM,CAAE,GAAG,CAAC,UAAU,CACtB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,mBAAmB,CAAC,CAC1C,gBAAgB,CAAE,KAAK,CACvB,YAAY,CAAE,GAAG,CAAC,GACnB,CAEQ,cAAe,OAAQ,CAC9B,OAAO,CAAE,EAAE,CACX,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,CAAC,CACN,IAAI,CAAE,CAAC,CACP,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,IAAI,mBAAmB,CAAC,CACpC,OAAO,CAAE,GAAG,CACZ,aAAa,CAAE,IAAI,WAAW,CAC/B,CAEQ,qBAAuB,CAC9B,KAAK,CAAE,GAAG,CAAC,UAAU,CACrB,MAAM,CAAE,IACT\"}`};U((A,t,e,o)=>{let{record:n}=t,{i18n:r}=t,{recording:l=!1}=t,s=[],i,a,c,C,v,u=!1,{record_time:h}=t,{show_recording_waveform:f}=t,{timing:m=!1}=t;t.record===void 0&&e.record&&n!==void 0&&e.record(n),t.i18n===void 0&&e.i18n&&r!==void 0&&e.i18n(r),t.recording===void 0&&e.recording&&l!==void 0&&e.recording(l),t.record_time===void 0&&e.record_time&&h!==void 0&&e.record_time(h),t.show_recording_waveform===void 0&&e.show_recording_waveform&&f!==void 0&&e.show_recording_waveform(f),t.timing===void 0&&e.timing&&m!==void 0&&e.timing(m),A.css.add(zt);let b,p,z=A.head;do b=!0,A.head=z,n.on(\"record-start\",()=>{n.startMic(),i.style.display=\"none\",C.style.display=\"flex\",a.style.display=\"block\"}),n.on(\"record-end\",()=>{n.isPaused()&&(n.resumeRecording(),n.stopRecording()),n.stopMic(),i.style.display=\"flex\",C.style.display=\"none\",a.style.display=\"none\",i.disabled=!1}),n.on(\"record-pause\",()=>{a.style.display=\"none\",c.style.display=\"block\",C.style.display=\"none\",v.style.display=\"flex\"}),n.on(\"record-resume\",()=>{a.style.display=\"block\",c.style.display=\"none\",i.style.display=\"none\",C.style.display=\"flex\",v.style.display=\"none\"}),l&&!u?(n.startRecording(),u=!0):(n.stopRecording(),u=!1),p=`<div class=\"controls svelte-1oiuk2f\"><div class=\"wrapper svelte-1oiuk2f\"><button class=\"record record-button svelte-1oiuk2f\"${y(\"this\",i,0)}>${I(r(\"audio.record\"))}</button> <button class=\"${\"stop-button \"+I(n.isPaused()?\"stop-button-paused\":\"\",!0)+\" svelte-1oiuk2f\"}\"${y(\"this\",C,0)}>${I(r(\"audio.stop\"))}</button> <button id=\"stop-paused\" class=\"stop-button-paused svelte-1oiuk2f\"${y(\"this\",v,0)}>${I(r(\"audio.stop\"))}</button> <button aria-label=\"pause\" class=\"pause-button svelte-1oiuk2f\"${y(\"this\",a,0)}>${w(ht,\"Pause\").$$render(A,{},{},{})}</button> <button class=\"resume-button svelte-1oiuk2f\"${y(\"this\",c,0)}>${I(r(\"audio.resume\"))}</button> ${m&&!f?`<time class=\"duration-button duration svelte-1oiuk2f\">${I(h)}</time>`:\"\"}</div> ${w(at,\"DeviceSelect\").$$render(A,{i18n:r,micDevices:s},{micDevices:S=>{s=S,b=!1}},{})} </div>`;while(!b);return p});const St={code:\".microphone.svelte-9n45fh{width:100%;display:none}.component-wrapper.svelte-9n45fh{padding:var(--size-3);width:100%}.timestamps.svelte-9n45fh{display:flex;justify-content:space-between;align-items:center;width:100%;padding:var(--size-1) 0;margin:var(--spacing-md) 0}.time.svelte-9n45fh{color:var(--neutral-400)}.duration.svelte-9n45fh{color:var(--neutral-400)}.trim-duration.svelte-9n45fh{color:var(--color-accent);margin-right:var(--spacing-sm)}\",map:'{\"version\":3,\"file\":\"AudioRecorder.svelte\",\"sources\":[\"AudioRecorder.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { onMount } from \\\\\"svelte\\\\\";\\\\nimport { createEventDispatcher } from \\\\\"svelte\\\\\";\\\\nimport WaveSurfer from \\\\\"wavesurfer.js\\\\\";\\\\nimport { skip_audio, process_audio } from \\\\\"../shared/utils\\\\\";\\\\nimport WSRecord from \\\\\"wavesurfer.js/dist/plugins/record.js\\\\\";\\\\nimport WaveformControls from \\\\\"../shared/WaveformControls.svelte\\\\\";\\\\nimport WaveformRecordControls from \\\\\"../shared/WaveformRecordControls.svelte\\\\\";\\\\nimport RecordPlugin from \\\\\"wavesurfer.js/dist/plugins/record.js\\\\\";\\\\nimport { format_time } from \\\\\"@gradio/utils\\\\\";\\\\nexport let mode;\\\\nexport let i18n;\\\\nexport let dispatch_blob;\\\\nexport let waveform_settings;\\\\nexport let waveform_options = {\\\\n    show_recording_waveform: true\\\\n};\\\\nexport let handle_reset_value;\\\\nexport let editable = true;\\\\nexport let recording = false;\\\\nlet micWaveform;\\\\nlet recordingWaveform;\\\\nlet playing = false;\\\\nlet recordingContainer;\\\\nlet microphoneContainer;\\\\nlet record;\\\\nlet recordedAudio = null;\\\\nlet timeRef;\\\\nlet durationRef;\\\\nlet audio_duration;\\\\nlet seconds = 0;\\\\nlet interval;\\\\nlet timing = false;\\\\nlet trimDuration = 0;\\\\nconst start_interval = () => {\\\\n    clearInterval(interval);\\\\n    interval = setInterval(() => {\\\\n        seconds++;\\\\n    }, 1e3);\\\\n};\\\\nconst dispatch = createEventDispatcher();\\\\nfunction record_start_callback() {\\\\n    start_interval();\\\\n    timing = true;\\\\n    dispatch(\\\\\"start_recording\\\\\");\\\\n    if (waveform_options.show_recording_waveform) {\\\\n        let waveformCanvas = microphoneContainer;\\\\n        if (waveformCanvas)\\\\n            waveformCanvas.style.display = \\\\\"block\\\\\";\\\\n    }\\\\n}\\\\nasync function record_end_callback(blob) {\\\\n    seconds = 0;\\\\n    timing = false;\\\\n    clearInterval(interval);\\\\n    try {\\\\n        const array_buffer = await blob.arrayBuffer();\\\\n        const context = new AudioContext({\\\\n            sampleRate: waveform_settings.sampleRate\\\\n        });\\\\n        const audio_buffer = await context.decodeAudioData(array_buffer);\\\\n        if (audio_buffer)\\\\n            await process_audio(audio_buffer).then(async (audio) => {\\\\n                await dispatch_blob([audio], \\\\\"change\\\\\");\\\\n                await dispatch_blob([audio], \\\\\"stop_recording\\\\\");\\\\n            });\\\\n    }\\\\n    catch (e) {\\\\n        console.error(e);\\\\n    }\\\\n}\\\\n$: record?.on(\\\\\"record-resume\\\\\", () => {\\\\n    start_interval();\\\\n});\\\\n$: recordingWaveform?.on(\\\\\"decode\\\\\", (duration) => {\\\\n    audio_duration = duration;\\\\n    durationRef && (durationRef.textContent = format_time(duration));\\\\n});\\\\n$: recordingWaveform?.on(\\\\\"timeupdate\\\\\", (currentTime) => timeRef && (timeRef.textContent = format_time(currentTime)));\\\\n$: recordingWaveform?.on(\\\\\"pause\\\\\", () => {\\\\n    dispatch(\\\\\"pause\\\\\");\\\\n    playing = false;\\\\n});\\\\n$: recordingWaveform?.on(\\\\\"play\\\\\", () => {\\\\n    dispatch(\\\\\"play\\\\\");\\\\n    playing = true;\\\\n});\\\\n$: recordingWaveform?.on(\\\\\"finish\\\\\", () => {\\\\n    dispatch(\\\\\"stop\\\\\");\\\\n    playing = false;\\\\n});\\\\nconst create_mic_waveform = () => {\\\\n    if (microphoneContainer)\\\\n        microphoneContainer.innerHTML = \\\\\"\\\\\";\\\\n    if (micWaveform !== void 0)\\\\n        micWaveform.destroy();\\\\n    if (!microphoneContainer)\\\\n        return;\\\\n    micWaveform = WaveSurfer.create({\\\\n        ...waveform_settings,\\\\n        normalize: false,\\\\n        container: microphoneContainer\\\\n    });\\\\n    record = micWaveform.registerPlugin(RecordPlugin.create());\\\\n    record?.on(\\\\\"record-end\\\\\", record_end_callback);\\\\n    record?.on(\\\\\"record-start\\\\\", record_start_callback);\\\\n    record?.on(\\\\\"record-pause\\\\\", () => {\\\\n        dispatch(\\\\\"pause_recording\\\\\");\\\\n        clearInterval(interval);\\\\n    });\\\\n    record?.on(\\\\\"record-end\\\\\", (blob) => {\\\\n        recordedAudio = URL.createObjectURL(blob);\\\\n        const microphone = microphoneContainer;\\\\n        const recording2 = recordingContainer;\\\\n        if (microphone)\\\\n            microphone.style.display = \\\\\"none\\\\\";\\\\n        if (recording2 && recordedAudio) {\\\\n            recording2.innerHTML = \\\\\"\\\\\";\\\\n            create_recording_waveform();\\\\n        }\\\\n    });\\\\n};\\\\nconst create_recording_waveform = () => {\\\\n    let recording2 = recordingContainer;\\\\n    if (!recordedAudio || !recording2)\\\\n        return;\\\\n    recordingWaveform = WaveSurfer.create({\\\\n        container: recording2,\\\\n        url: recordedAudio,\\\\n        ...waveform_settings\\\\n    });\\\\n};\\\\nconst handle_trim_audio = async (start, end) => {\\\\n    mode = \\\\\"edit\\\\\";\\\\n    const decodedData = recordingWaveform.getDecodedData();\\\\n    if (decodedData)\\\\n        await process_audio(decodedData, start, end).then(async (trimmedAudio) => {\\\\n            await dispatch_blob([trimmedAudio], \\\\\"change\\\\\");\\\\n            await dispatch_blob([trimmedAudio], \\\\\"stop_recording\\\\\");\\\\n            recordingWaveform.destroy();\\\\n            create_recording_waveform();\\\\n        });\\\\n    dispatch(\\\\\"edit\\\\\");\\\\n};\\\\nonMount(() => {\\\\n    create_mic_waveform();\\\\n    window.addEventListener(\\\\\"keydown\\\\\", (e) => {\\\\n        if (e.key === \\\\\"ArrowRight\\\\\") {\\\\n            skip_audio(recordingWaveform, 0.1);\\\\n        }\\\\n        else if (e.key === \\\\\"ArrowLeft\\\\\") {\\\\n            skip_audio(recordingWaveform, -0.1);\\\\n        }\\\\n    });\\\\n});\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"component-wrapper\\\\\">\\\\n\\\\t<div\\\\n\\\\t\\\\tclass=\\\\\"microphone\\\\\"\\\\n\\\\t\\\\tbind:this={microphoneContainer}\\\\n\\\\t\\\\tdata-testid=\\\\\"microphone-waveform\\\\\"\\\\n\\\\t/>\\\\n\\\\t<div bind:this={recordingContainer} data-testid=\\\\\"recording-waveform\\\\\" />\\\\n\\\\n\\\\t{#if (timing || recordedAudio) && waveform_options.show_recording_waveform}\\\\n\\\\t\\\\t<div class=\\\\\"timestamps\\\\\">\\\\n\\\\t\\\\t\\\\t<time bind:this={timeRef} class=\\\\\"time\\\\\">0:00</time>\\\\n\\\\t\\\\t\\\\t<div>\\\\n\\\\t\\\\t\\\\t\\\\t{#if mode === \\\\\"edit\\\\\" && trimDuration > 0}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<time class=\\\\\"trim-duration\\\\\">{format_time(trimDuration)}</time>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t{#if timing}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<time class=\\\\\"duration\\\\\">{format_time(seconds)}</time>\\\\n\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<time bind:this={durationRef} class=\\\\\"duration\\\\\">0:00</time>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n\\\\n\\\\t{#if microphoneContainer && !recordedAudio}\\\\n\\\\t\\\\t<WaveformRecordControls\\\\n\\\\t\\\\t\\\\tbind:record\\\\n\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t{timing}\\\\n\\\\t\\\\t\\\\t{recording}\\\\n\\\\t\\\\t\\\\tshow_recording_waveform={waveform_options.show_recording_waveform}\\\\n\\\\t\\\\t\\\\trecord_time={format_time(seconds)}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n\\\\n\\\\t{#if recordingWaveform && recordedAudio}\\\\n\\\\t\\\\t<WaveformControls\\\\n\\\\t\\\\t\\\\tbind:waveform={recordingWaveform}\\\\n\\\\t\\\\t\\\\tcontainer={recordingContainer}\\\\n\\\\t\\\\t\\\\t{playing}\\\\n\\\\t\\\\t\\\\t{audio_duration}\\\\n\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t{editable}\\\\n\\\\t\\\\t\\\\tinteractive={true}\\\\n\\\\t\\\\t\\\\t{handle_trim_audio}\\\\n\\\\t\\\\t\\\\tbind:trimDuration\\\\n\\\\t\\\\t\\\\tbind:mode\\\\n\\\\t\\\\t\\\\tshow_redo\\\\n\\\\t\\\\t\\\\t{handle_reset_value}\\\\n\\\\t\\\\t\\\\t{waveform_options}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.microphone {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.component-wrapper {\\\\n\\\\t\\\\tpadding: var(--size-3);\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.timestamps {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tpadding: var(--size-1) 0;\\\\n\\\\t\\\\tmargin: var(--spacing-md) 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.time {\\\\n\\\\t\\\\tcolor: var(--neutral-400);\\\\n\\\\t}\\\\n\\\\n\\\\t.duration {\\\\n\\\\t\\\\tcolor: var(--neutral-400);\\\\n\\\\t}\\\\n\\\\n\\\\t.trim-duration {\\\\n\\\\t\\\\tcolor: var(--color-accent);\\\\n\\\\t\\\\tmargin-right: var(--spacing-sm);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAmNC,yBAAY,CACX,KAAK,CAAE,IAAI,CACX,OAAO,CAAE,IACV,CAEA,gCAAmB,CAClB,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,KAAK,CAAE,IACR,CAEA,yBAAY,CACX,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,aAAa,CAC9B,WAAW,CAAE,MAAM,CACnB,KAAK,CAAE,IAAI,CACX,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,CAAC,CACxB,MAAM,CAAE,IAAI,YAAY,CAAC,CAAC,CAC3B,CAEA,mBAAM,CACL,KAAK,CAAE,IAAI,aAAa,CACzB,CAEA,uBAAU,CACT,KAAK,CAAE,IAAI,aAAa,CACzB,CAEA,4BAAe,CACd,KAAK,CAAE,IAAI,cAAc,CAAC,CAC1B,YAAY,CAAE,IAAI,YAAY,CAC/B\"}'},Qt=U((A,t,e,o)=>{let{mode:n}=t,{i18n:r}=t,{dispatch_blob:l}=t,{waveform_settings:s}=t,{waveform_options:i={show_recording_waveform:!0}}=t,{handle_reset_value:a}=t,{editable:c=!0}=t,{recording:C=!1}=t,v,u,h;F(),it(()=>{window.addEventListener(\"keydown\",p=>{p.key===\"ArrowRight\"?At(v,.1):p.key===\"ArrowLeft\"&&At(v,-.1)})}),t.mode===void 0&&e.mode&&n!==void 0&&e.mode(n),t.i18n===void 0&&e.i18n&&r!==void 0&&e.i18n(r),t.dispatch_blob===void 0&&e.dispatch_blob&&l!==void 0&&e.dispatch_blob(l),t.waveform_settings===void 0&&e.waveform_settings&&s!==void 0&&e.waveform_settings(s),t.waveform_options===void 0&&e.waveform_options&&i!==void 0&&e.waveform_options(i),t.handle_reset_value===void 0&&e.handle_reset_value&&a!==void 0&&e.handle_reset_value(a),t.editable===void 0&&e.editable&&c!==void 0&&e.editable(c),t.recording===void 0&&e.recording&&C!==void 0&&e.recording(C),A.css.add(St);let f,m,b=A.head;do f=!0,A.head=b,m=`<div class=\"component-wrapper svelte-9n45fh\"><div class=\"microphone svelte-9n45fh\" data-testid=\"microphone-waveform\"${y(\"this\",h,0)}></div> <div data-testid=\"recording-waveform\"${y(\"this\",u,0)}></div>    </div>`;while(!f);return m}),Kt={code:'.controls.svelte-1fz19cj{display:flex;align-items:center;justify-content:space-between;flex-wrap:wrap}.mic-wrap.svelte-1fz19cj{display:block;align-items:center;margin:var(--spacing-xl)}.icon.svelte-1fz19cj{width:var(--size-4);height:var(--size-4);fill:var(--primary-600);stroke:var(--primary-600)}.stop-button-paused.svelte-1fz19cj{display:none;height:var(--size-8);width:var(--size-20);background-color:var(--block-background-fill);border-radius:var(--button-large-radius);align-items:center;border:1px solid var(--block-border-color);margin-right:5px}.stop-button-paused.svelte-1fz19cj::before{content:\"\";height:var(--size-4);width:var(--size-4);border-radius:var(--radius-full);background:var(--primary-600);margin:0 var(--spacing-xl)}.stop-button.svelte-1fz19cj::before{content:\"\";height:var(--size-4);width:var(--size-4);border-radius:var(--radius-full);background:var(--primary-600);margin:0 var(--spacing-xl);animation:svelte-1fz19cj-scaling 1800ms infinite}.stop-button.svelte-1fz19cj{height:var(--size-8);width:var(--size-20);background-color:var(--block-background-fill);border-radius:var(--button-large-radius);align-items:center;border:1px solid var(--primary-600);margin-right:5px;display:flex}.spinner-button.svelte-1fz19cj{height:var(--size-8);width:var(--size-24);background-color:var(--block-background-fill);border-radius:var(--radius-3xl);align-items:center;border:1px solid var(--primary-600);margin:0 var(--spacing-xl);display:flex;justify-content:space-evenly}.record-button.svelte-1fz19cj::before{content:\"\";height:var(--size-4);width:var(--size-4);border-radius:var(--radius-full);background:var(--primary-600);margin:0 var(--spacing-xl)}.record-button.svelte-1fz19cj{height:var(--size-8);width:var(--size-24);background-color:var(--block-background-fill);border-radius:var(--button-large-radius);display:flex;align-items:center;border:1px solid var(--block-border-color)}@keyframes svelte-1fz19cj-scaling{0%{background-color:var(--primary-600);scale:1}50%{background-color:var(--primary-600);scale:1.2}100%{background-color:var(--primary-600);scale:1}}',map:'{\"version\":3,\"file\":\"StreamAudio.svelte\",\"sources\":[\"StreamAudio.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { onMount } from \\\\\"svelte\\\\\";\\\\nimport { Spinner } from \\\\\"@gradio/icons\\\\\";\\\\nimport WaveSurfer from \\\\\"wavesurfer.js\\\\\";\\\\nimport RecordPlugin from \\\\\"wavesurfer.js/dist/plugins/record.js\\\\\";\\\\nimport DeviceSelect from \\\\\"../shared/DeviceSelect.svelte\\\\\";\\\\nexport let recording = false;\\\\nexport let paused_recording = false;\\\\nexport let stop;\\\\nexport let record;\\\\nexport let i18n;\\\\nexport let waveform_settings;\\\\nexport let waveform_options = {\\\\n    show_recording_waveform: true\\\\n};\\\\nexport let waiting = false;\\\\nlet micWaveform;\\\\nlet waveformRecord;\\\\nlet microphoneContainer;\\\\nlet micDevices = [];\\\\nonMount(() => {\\\\n    create_mic_waveform();\\\\n});\\\\nconst create_mic_waveform = () => {\\\\n    if (micWaveform !== void 0)\\\\n        micWaveform.destroy();\\\\n    if (!microphoneContainer)\\\\n        return;\\\\n    micWaveform = WaveSurfer.create({\\\\n        ...waveform_settings,\\\\n        height: 100,\\\\n        container: microphoneContainer\\\\n    });\\\\n    waveformRecord = micWaveform.registerPlugin(RecordPlugin.create());\\\\n};\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"mic-wrap\\\\\">\\\\n\\\\t{#if waveform_options.show_recording_waveform}\\\\n\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\tbind:this={microphoneContainer}\\\\n\\\\t\\\\t\\\\tstyle:display={recording ? \\\\\"block\\\\\" : \\\\\"none\\\\\"}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n\\\\t<div class=\\\\\"controls\\\\\">\\\\n\\\\t\\\\t{#if recording && !waiting}\\\\n\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\tclass={paused_recording ? \\\\\"stop-button-paused\\\\\" : \\\\\"stop-button\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\twaveformRecord?.stopMic();\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tstop();\\\\n\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"record-icon\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"pinger\\\\\" />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"dot\\\\\" />\\\\n\\\\t\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t\\\\t\\\\t{paused_recording ? i18n(\\\\\"audio.pause\\\\\") : i18n(\\\\\"audio.stop\\\\\")}\\\\n\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t{:else if recording && waiting}\\\\n\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"spinner-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tstop();\\\\n\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"icon\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<Spinner />\\\\n\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t{i18n(\\\\\"audio.waiting\\\\\")}\\\\n\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"record-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\twaveformRecord?.startMic();\\\\n\\\\t\\\\t\\\\t\\\\t\\\\trecord();\\\\n\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"record-icon\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"dot\\\\\" />\\\\n\\\\t\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t\\\\t\\\\t{i18n(\\\\\"audio.record\\\\\")}\\\\n\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t{/if}\\\\n\\\\n\\\\t\\\\t<DeviceSelect bind:micDevices {i18n} />\\\\n\\\\t</div>\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.controls {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\tflex-wrap: wrap;\\\\n\\\\t}\\\\n\\\\n\\\\t.mic-wrap {\\\\n\\\\t\\\\tdisplay: block;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tmargin: var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.icon {\\\\n\\\\t\\\\twidth: var(--size-4);\\\\n\\\\t\\\\theight: var(--size-4);\\\\n\\\\t\\\\tfill: var(--primary-600);\\\\n\\\\t\\\\tstroke: var(--primary-600);\\\\n\\\\t}\\\\n\\\\n\\\\t.stop-button-paused {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tborder-radius: var(--button-large-radius);\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tborder: 1px solid var(--block-border-color);\\\\n\\\\t\\\\tmargin-right: 5px;\\\\n\\\\t}\\\\n\\\\n\\\\t.stop-button-paused::before {\\\\n\\\\t\\\\tcontent: \\\\\"\\\\\";\\\\n\\\\t\\\\theight: var(--size-4);\\\\n\\\\t\\\\twidth: var(--size-4);\\\\n\\\\t\\\\tborder-radius: var(--radius-full);\\\\n\\\\t\\\\tbackground: var(--primary-600);\\\\n\\\\t\\\\tmargin: 0 var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.stop-button::before {\\\\n\\\\t\\\\tcontent: \\\\\"\\\\\";\\\\n\\\\t\\\\theight: var(--size-4);\\\\n\\\\t\\\\twidth: var(--size-4);\\\\n\\\\t\\\\tborder-radius: var(--radius-full);\\\\n\\\\t\\\\tbackground: var(--primary-600);\\\\n\\\\t\\\\tmargin: 0 var(--spacing-xl);\\\\n\\\\t\\\\tanimation: scaling 1800ms infinite;\\\\n\\\\t}\\\\n\\\\n\\\\t.stop-button {\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tborder-radius: var(--button-large-radius);\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tborder: 1px solid var(--primary-600);\\\\n\\\\t\\\\tmargin-right: 5px;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t}\\\\n\\\\n\\\\t.spinner-button {\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\twidth: var(--size-24);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tborder-radius: var(--radius-3xl);\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tborder: 1px solid var(--primary-600);\\\\n\\\\t\\\\tmargin: 0 var(--spacing-xl);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: space-evenly;\\\\n\\\\t}\\\\n\\\\n\\\\t.record-button::before {\\\\n\\\\t\\\\tcontent: \\\\\"\\\\\";\\\\n\\\\t\\\\theight: var(--size-4);\\\\n\\\\t\\\\twidth: var(--size-4);\\\\n\\\\t\\\\tborder-radius: var(--radius-full);\\\\n\\\\t\\\\tbackground: var(--primary-600);\\\\n\\\\t\\\\tmargin: 0 var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.record-button {\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\twidth: var(--size-24);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tborder-radius: var(--button-large-radius);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tborder: 1px solid var(--block-border-color);\\\\n\\\\t}\\\\n\\\\n\\\\t@keyframes scaling {\\\\n\\\\t\\\\t0% {\\\\n\\\\t\\\\t\\\\tbackground-color: var(--primary-600);\\\\n\\\\t\\\\t\\\\tscale: 1;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t50% {\\\\n\\\\t\\\\t\\\\tbackground-color: var(--primary-600);\\\\n\\\\t\\\\t\\\\tscale: 1.2;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t100% {\\\\n\\\\t\\\\t\\\\tbackground-color: var(--primary-600);\\\\n\\\\t\\\\t\\\\tscale: 1;\\\\n\\\\t\\\\t}\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA0FC,wBAAU,CACT,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,aAAa,CAC9B,SAAS,CAAE,IACZ,CAEA,wBAAU,CACT,OAAO,CAAE,KAAK,CACd,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,IAAI,YAAY,CACzB,CAEA,oBAAM,CACL,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,IAAI,CAAE,IAAI,aAAa,CAAC,CACxB,MAAM,CAAE,IAAI,aAAa,CAC1B,CAEA,kCAAoB,CACnB,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CAC3C,YAAY,CAAE,GACf,CAEA,kCAAmB,QAAS,CAC3B,OAAO,CAAE,EAAE,CACX,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,aAAa,CAAE,IAAI,aAAa,CAAC,CACjC,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,MAAM,CAAE,CAAC,CAAC,IAAI,YAAY,CAC3B,CAEA,2BAAY,QAAS,CACpB,OAAO,CAAE,EAAE,CACX,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,aAAa,CAAE,IAAI,aAAa,CAAC,CACjC,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,MAAM,CAAE,CAAC,CAAC,IAAI,YAAY,CAAC,CAC3B,SAAS,CAAE,sBAAO,CAAC,MAAM,CAAC,QAC3B,CAEA,2BAAa,CACZ,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,CACpC,YAAY,CAAE,GAAG,CACjB,OAAO,CAAE,IACV,CAEA,8BAAgB,CACf,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,aAAa,CAAE,IAAI,YAAY,CAAC,CAChC,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,CACpC,MAAM,CAAE,CAAC,CAAC,IAAI,YAAY,CAAC,CAC3B,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,YAClB,CAEA,6BAAc,QAAS,CACtB,OAAO,CAAE,EAAE,CACX,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,aAAa,CAAE,IAAI,aAAa,CAAC,CACjC,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,MAAM,CAAE,CAAC,CAAC,IAAI,YAAY,CAC3B,CAEA,6BAAe,CACd,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAC3C,CAEA,WAAW,sBAAQ,CAClB,EAAG,CACF,gBAAgB,CAAE,IAAI,aAAa,CAAC,CACpC,KAAK,CAAE,CACR,CACA,GAAI,CACH,gBAAgB,CAAE,IAAI,aAAa,CAAC,CACpC,KAAK,CAAE,GACR,CACA,IAAK,CACJ,gBAAgB,CAAE,IAAI,aAAa,CAAC,CACpC,KAAK,CAAE,CACR,CACD\"}'},Rt=U((A,t,e,o)=>{let{recording:n=!1}=t,{paused_recording:r=!1}=t,{stop:l}=t,{record:s}=t,{i18n:i}=t,{waveform_settings:a}=t,{waveform_options:c={show_recording_waveform:!0}}=t,{waiting:C=!1}=t,v,u=[];it(()=>{h()});const h=()=>{};t.recording===void 0&&e.recording&&n!==void 0&&e.recording(n),t.paused_recording===void 0&&e.paused_recording&&r!==void 0&&e.paused_recording(r),t.stop===void 0&&e.stop&&l!==void 0&&e.stop(l),t.record===void 0&&e.record&&s!==void 0&&e.record(s),t.i18n===void 0&&e.i18n&&i!==void 0&&e.i18n(i),t.waveform_settings===void 0&&e.waveform_settings&&a!==void 0&&e.waveform_settings(a),t.waveform_options===void 0&&e.waveform_options&&c!==void 0&&e.waveform_options(c),t.waiting===void 0&&e.waiting&&C!==void 0&&e.waiting(C),A.css.add(Kt);let f,m,b=A.head;do f=!0,A.head=b,m=`<div class=\"mic-wrap svelte-1fz19cj\">${c.show_recording_waveform?`<div${vt({display:n?\"block\":\"none\"})}${y(\"this\",v,0)}></div>`:\"\"} <div class=\"controls svelte-1fz19cj\">${n&&!C?`<button class=\"${I(mt(r?\"stop-button-paused\":\"stop-button\"),!0)+\" svelte-1fz19cj\"}\"><span class=\"record-icon\" data-svelte-h=\"svelte-bla7qm\"><span class=\"pinger\"></span> <span class=\"dot\"></span></span> ${I(i(r?\"audio.pause\":\"audio.stop\"))}</button>`:`${n&&C?`<button class=\"spinner-button svelte-1fz19cj\"><div class=\"icon svelte-1fz19cj\">${w(bt,\"Spinner\").$$render(A,{},{},{})}</div> ${I(i(\"audio.waiting\"))}</button>`:`<button class=\"record-button svelte-1fz19cj\"><span class=\"record-icon\" data-svelte-h=\"svelte-1dwz2xe\"><span class=\"dot\"></span></span> ${I(i(\"audio.record\"))}</button>`}`} ${w(at,\"DeviceSelect\").$$render(A,{i18n:i,micDevices:u},{micDevices:p=>{u=p,f=!1}},{})}</div> </div>`;while(!f);return m}),Ot={code:\".audio-container.svelte-1ud6e7m{height:calc(var(--size-full) - var(--size-6));display:flex;flex-direction:column;justify-content:space-between}.audio-container.compact-audio.svelte-1ud6e7m{margin-top:calc(var(--size-8) * -1);height:auto;padding:0px;gap:var(--size-2);min-height:var(--size-5)}.compact-audio.svelte-1ud6e7m .audio-player{padding:0px}.compact-audio.svelte-1ud6e7m .controls{gap:0px;padding:0px}.compact-audio.svelte-1ud6e7m .waveform-container{height:var(--size-12) !important}.compact-audio.svelte-1ud6e7m .player-container{min-height:unset;height:auto}\",map:'{\"version\":3,\"file\":\"InteractiveAudio.svelte\",\"sources\":[\"InteractiveAudio.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { onDestroy, createEventDispatcher, tick } from \\\\\"svelte\\\\\";\\\\nimport { Upload, ModifyUpload } from \\\\\"@gradio/upload\\\\\";\\\\nimport { prepare_files } from \\\\\"@gradio/client\\\\\";\\\\nimport { BlockLabel } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { Music } from \\\\\"@gradio/icons\\\\\";\\\\nimport { StreamingBar } from \\\\\"@gradio/statustracker\\\\\";\\\\nimport AudioPlayer from \\\\\"../player/AudioPlayer.svelte\\\\\";\\\\nimport AudioRecorder from \\\\\"../recorder/AudioRecorder.svelte\\\\\";\\\\nimport StreamAudio from \\\\\"../streaming/StreamAudio.svelte\\\\\";\\\\nimport { SelectSource } from \\\\\"@gradio/atoms\\\\\";\\\\nexport let value = null;\\\\nexport let label;\\\\nexport let root;\\\\nexport let loop;\\\\nexport let show_label = true;\\\\nexport let show_download_button = false;\\\\nexport let sources = [\\\\\"microphone\\\\\", \\\\\"upload\\\\\"];\\\\nexport let pending = false;\\\\nexport let streaming = false;\\\\nexport let i18n;\\\\nexport let waveform_settings;\\\\nexport let trim_region_settings = {};\\\\nexport let waveform_options = {};\\\\nexport let dragging;\\\\nexport let active_source;\\\\nexport let handle_reset_value = () => {\\\\n};\\\\nexport let editable = true;\\\\nexport let max_file_size = null;\\\\nexport let upload;\\\\nexport let stream_handler;\\\\nexport let stream_every;\\\\nexport let uploading = false;\\\\nexport let recording = false;\\\\nexport let class_name = \\\\\"\\\\\";\\\\nlet time_limit = null;\\\\nlet stream_state = \\\\\"closed\\\\\";\\\\nexport const modify_stream = (state) => {\\\\n    if (state === \\\\\"closed\\\\\") {\\\\n        time_limit = null;\\\\n        stream_state = \\\\\"closed\\\\\";\\\\n    }\\\\n    else if (state === \\\\\"waiting\\\\\") {\\\\n        stream_state = \\\\\"waiting\\\\\";\\\\n    }\\\\n    else {\\\\n        stream_state = \\\\\"open\\\\\";\\\\n    }\\\\n};\\\\nexport const set_time_limit = (time) => {\\\\n    if (recording)\\\\n        time_limit = time;\\\\n};\\\\n$: dispatch(\\\\\"drag\\\\\", dragging);\\\\nlet recorder;\\\\nlet mode = \\\\\"\\\\\";\\\\nlet header = void 0;\\\\nlet pending_stream = [];\\\\nlet submit_pending_stream_on_pending_end = false;\\\\nlet inited = false;\\\\nconst NUM_HEADER_BYTES = 44;\\\\nlet audio_chunks = [];\\\\nlet module_promises;\\\\nfunction get_modules() {\\\\n    module_promises = [\\\\n        import(\\\\\"extendable-media-recorder\\\\\"),\\\\n        import(\\\\\"extendable-media-recorder-wav-encoder\\\\\")\\\\n    ];\\\\n}\\\\nconst is_browser = typeof window !== \\\\\"undefined\\\\\";\\\\nif (is_browser && streaming) {\\\\n    get_modules();\\\\n}\\\\nconst dispatch = createEventDispatcher();\\\\nconst dispatch_blob = async (blobs, event) => {\\\\n    let _audio_blob = new File(blobs, \\\\\"audio.wav\\\\\");\\\\n    const val = await prepare_files([_audio_blob], event === \\\\\"stream\\\\\");\\\\n    value = ((await upload(val, root, void 0, max_file_size || void 0))?.filter(Boolean))[0];\\\\n    dispatch(event, value);\\\\n};\\\\nonDestroy(() => {\\\\n    if (streaming && recorder && recorder.state !== \\\\\"inactive\\\\\") {\\\\n        recorder.stop();\\\\n    }\\\\n});\\\\nasync function prepare_audio() {\\\\n    let stream;\\\\n    try {\\\\n        stream = await navigator.mediaDevices.getUserMedia({ audio: true });\\\\n    }\\\\n    catch (err) {\\\\n        if (!navigator.mediaDevices) {\\\\n            dispatch(\\\\\"error\\\\\", i18n(\\\\\"audio.no_device_support\\\\\"));\\\\n            return;\\\\n        }\\\\n        if (err instanceof DOMException && err.name == \\\\\"NotAllowedError\\\\\") {\\\\n            dispatch(\\\\\"error\\\\\", i18n(\\\\\"audio.allow_recording_access\\\\\"));\\\\n            return;\\\\n        }\\\\n        throw err;\\\\n    }\\\\n    if (stream == null)\\\\n        return;\\\\n    if (streaming) {\\\\n        const [{ MediaRecorder: MediaRecorder2, register }, { connect }] = await Promise.all(module_promises);\\\\n        await register(await connect());\\\\n        recorder = new MediaRecorder2(stream, { mimeType: \\\\\"audio/wav\\\\\" });\\\\n        recorder.addEventListener(\\\\\"dataavailable\\\\\", handle_chunk);\\\\n    }\\\\n    else {\\\\n        recorder = new MediaRecorder(stream);\\\\n        recorder.addEventListener(\\\\\"dataavailable\\\\\", (event) => {\\\\n            audio_chunks.push(event.data);\\\\n        });\\\\n    }\\\\n    recorder.addEventListener(\\\\\"stop\\\\\", async () => {\\\\n        recording = false;\\\\n        await dispatch_blob(audio_chunks, \\\\\"change\\\\\");\\\\n        await dispatch_blob(audio_chunks, \\\\\"stop_recording\\\\\");\\\\n        audio_chunks = [];\\\\n    });\\\\n    inited = true;\\\\n}\\\\nasync function handle_chunk(event) {\\\\n    let buffer = await event.data.arrayBuffer();\\\\n    let payload = new Uint8Array(buffer);\\\\n    if (!header) {\\\\n        header = new Uint8Array(buffer.slice(0, NUM_HEADER_BYTES));\\\\n        payload = new Uint8Array(buffer.slice(NUM_HEADER_BYTES));\\\\n    }\\\\n    if (pending) {\\\\n        pending_stream.push(payload);\\\\n    }\\\\n    else {\\\\n        let blobParts = [header].concat(pending_stream, [payload]);\\\\n        if (!recording || stream_state === \\\\\"waiting\\\\\")\\\\n            return;\\\\n        dispatch_blob(blobParts, \\\\\"stream\\\\\");\\\\n        pending_stream = [];\\\\n    }\\\\n}\\\\n$: if (submit_pending_stream_on_pending_end && pending === false) {\\\\n    submit_pending_stream_on_pending_end = false;\\\\n    if (header && pending_stream) {\\\\n        let blobParts = [header].concat(pending_stream);\\\\n        pending_stream = [];\\\\n        dispatch_blob(blobParts, \\\\\"stream\\\\\");\\\\n    }\\\\n}\\\\nasync function record() {\\\\n    recording = true;\\\\n    dispatch(\\\\\"start_recording\\\\\");\\\\n    if (!inited)\\\\n        await prepare_audio();\\\\n    header = void 0;\\\\n    if (streaming && recorder.state != \\\\\"recording\\\\\") {\\\\n        recorder.start(stream_every * 1e3);\\\\n    }\\\\n}\\\\nfunction clear() {\\\\n    dispatch(\\\\\"change\\\\\", null);\\\\n    dispatch(\\\\\"clear\\\\\");\\\\n    mode = \\\\\"\\\\\";\\\\n    value = null;\\\\n}\\\\nfunction handle_load({ detail }) {\\\\n    value = detail;\\\\n    dispatch(\\\\\"change\\\\\", detail);\\\\n    dispatch(\\\\\"upload\\\\\", detail);\\\\n}\\\\nasync function stop() {\\\\n    recording = false;\\\\n    if (streaming) {\\\\n        dispatch(\\\\\"close_stream\\\\\");\\\\n        dispatch(\\\\\"stop_recording\\\\\");\\\\n        recorder.stop();\\\\n        if (pending) {\\\\n            submit_pending_stream_on_pending_end = true;\\\\n        }\\\\n        dispatch_blob(audio_chunks, \\\\\"stop_recording\\\\\");\\\\n        dispatch(\\\\\"clear\\\\\");\\\\n        mode = \\\\\"\\\\\";\\\\n    }\\\\n}\\\\n$: if (!recording && recorder)\\\\n    stop();\\\\n$: if (recording && recorder)\\\\n    record();\\\\n<\\/script>\\\\n\\\\n<BlockLabel\\\\n\\\\t{show_label}\\\\n\\\\tIcon={Music}\\\\n\\\\tfloat={active_source === \\\\\"upload\\\\\" && value === null}\\\\n\\\\tlabel={label || i18n(\\\\\"audio.audio\\\\\")}\\\\n/>\\\\n<div class=\\\\\"audio-container {class_name}\\\\\">\\\\n\\\\t<StreamingBar {time_limit} />\\\\n\\\\t{#if value === null || streaming}\\\\n\\\\t\\\\t{#if active_source === \\\\\"microphone\\\\\"}\\\\n\\\\t\\\\t\\\\t<ModifyUpload {i18n} on:clear={clear} />\\\\n\\\\t\\\\t\\\\t{#if streaming}\\\\n\\\\t\\\\t\\\\t\\\\t<StreamAudio\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{record}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{recording}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{stop}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{waveform_settings}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{waveform_options}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\twaiting={stream_state === \\\\\"waiting\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t<AudioRecorder\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tbind:mode\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{editable}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{recording}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{dispatch_blob}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{waveform_settings}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{waveform_options}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{handle_reset_value}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:start_recording\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:pause_recording\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:stop_recording\\\\n\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t{:else if active_source === \\\\\"upload\\\\\"}\\\\n\\\\t\\\\t\\\\t<!-- explicitly listed out audio mimetypes due to iOS bug not recognizing audio/* -->\\\\n\\\\t\\\\t\\\\t<Upload\\\\n\\\\t\\\\t\\\\t\\\\tfiletype=\\\\\"audio/aac,audio/midi,audio/mpeg,audio/ogg,audio/wav,audio/x-wav,audio/opus,audio/webm,audio/flac,audio/vnd.rn-realaudio,audio/x-ms-wma,audio/x-aiff,audio/amr,audio/*\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\ton:load={handle_load}\\\\n\\\\t\\\\t\\\\t\\\\tbind:dragging\\\\n\\\\t\\\\t\\\\t\\\\tbind:uploading\\\\n\\\\t\\\\t\\\\t\\\\ton:error={({ detail }) => dispatch(\\\\\"error\\\\\", detail)}\\\\n\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\t{max_file_size}\\\\n\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\t{stream_handler}\\\\n\\\\t\\\\t\\\\t\\\\taria_label={i18n(\\\\\"audio.drop_to_upload\\\\\")}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<slot />\\\\n\\\\t\\\\t\\\\t</Upload>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t{:else}\\\\n\\\\t\\\\t<ModifyUpload\\\\n\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\ton:clear={clear}\\\\n\\\\t\\\\t\\\\ton:edit={() => (mode = \\\\\"edit\\\\\")}\\\\n\\\\t\\\\t\\\\tdownload={show_download_button ? value.url : null}\\\\n\\\\t\\\\t/>\\\\n\\\\n\\\\t\\\\t<AudioPlayer\\\\n\\\\t\\\\t\\\\tbind:mode\\\\n\\\\t\\\\t\\\\t{value}\\\\n\\\\t\\\\t\\\\t{label}\\\\n\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t{dispatch_blob}\\\\n\\\\t\\\\t\\\\t{waveform_settings}\\\\n\\\\t\\\\t\\\\t{waveform_options}\\\\n\\\\t\\\\t\\\\t{trim_region_settings}\\\\n\\\\t\\\\t\\\\t{handle_reset_value}\\\\n\\\\t\\\\t\\\\t{editable}\\\\n\\\\t\\\\t\\\\t{loop}\\\\n\\\\t\\\\t\\\\tinteractive\\\\n\\\\t\\\\t\\\\ton:stop\\\\n\\\\t\\\\t\\\\ton:play\\\\n\\\\t\\\\t\\\\ton:pause\\\\n\\\\t\\\\t\\\\ton:edit\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n\\\\t<SelectSource {sources} bind:active_source handle_clear={clear} />\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.audio-container {\\\\n\\\\t\\\\theight: calc(var(--size-full) - var(--size-6));\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t}\\\\n\\\\n\\\\t.audio-container.compact-audio {\\\\n\\\\t\\\\tmargin-top: calc(var(--size-8) * -1);\\\\n\\\\t\\\\theight: auto;\\\\n\\\\t\\\\tpadding: 0px;\\\\n\\\\t\\\\tgap: var(--size-2);\\\\n\\\\t\\\\tmin-height: var(--size-5);\\\\n\\\\t}\\\\n\\\\n\\\\t.compact-audio :global(.audio-player) {\\\\n\\\\t\\\\tpadding: 0px;\\\\n\\\\t}\\\\n\\\\n\\\\t.compact-audio :global(.controls) {\\\\n\\\\t\\\\tgap: 0px;\\\\n\\\\t\\\\tpadding: 0px;\\\\n\\\\t}\\\\n\\\\n\\\\t.compact-audio :global(.waveform-container) {\\\\n\\\\t\\\\theight: var(--size-12) !important;\\\\n\\\\t}\\\\n\\\\n\\\\t.compact-audio :global(.player-container) {\\\\n\\\\t\\\\tmin-height: unset;\\\\n\\\\t\\\\theight: auto;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAkRC,+BAAiB,CAChB,MAAM,CAAE,KAAK,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAC9C,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,aAClB,CAEA,gBAAgB,6BAAe,CAC9B,UAAU,CAAE,KAAK,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CACpC,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,GAAG,CACZ,GAAG,CAAE,IAAI,QAAQ,CAAC,CAClB,UAAU,CAAE,IAAI,QAAQ,CACzB,CAEA,6BAAc,CAAS,aAAe,CACrC,OAAO,CAAE,GACV,CAEA,6BAAc,CAAS,SAAW,CACjC,GAAG,CAAE,GAAG,CACR,OAAO,CAAE,GACV,CAEA,6BAAc,CAAS,mBAAqB,CAC3C,MAAM,CAAE,IAAI,SAAS,CAAC,CAAC,UACxB,CAEA,6BAAc,CAAS,iBAAmB,CACzC,UAAU,CAAE,KAAK,CACjB,MAAM,CAAE,IACT\"}'},ot=44,Wt=U((A,t,e,o)=>{let{value:n=null}=t,{label:r}=t,{root:l}=t,{loop:s}=t,{show_label:i=!0}=t,{show_download_button:a=!1}=t,{sources:c=[\"microphone\",\"upload\"]}=t,{pending:C=!1}=t,{streaming:v=!1}=t,{i18n:u}=t,{waveform_settings:h}=t,{trim_region_settings:f={}}=t,{waveform_options:m={}}=t,{dragging:b}=t,{active_source:p}=t,{handle_reset_value:z=()=>{}}=t,{editable:S=!0}=t,{max_file_size:G=null}=t,{upload:j}=t,{stream_handler:P}=t,{stream_every:L}=t,{uploading:Y=!1}=t,{recording:B=!1}=t,{class_name:q=\"\"}=t,T=null,O=\"closed\";const H=d=>{d===\"closed\"?(T=null,O=\"closed\"):d===\"waiting\"?O=\"waiting\":O=\"open\"},V=d=>{B&&(T=d)};let _,Q=\"\",K,R=[],X=!1,J=!1,W=[],$;function st(){$=[import(\"./module3.js\"),import(\"./module.js\")]}typeof window<\"u\"&&v&&st();const E=F(),x=async(d,g)=>{let k=new File(d,\"audio.wav\");const D=await gt([k],g===\"stream\");n=(await j(D,l,void 0,G||void 0))?.filter(Boolean)[0],E(g,n)};pt(()=>{v&&_&&_.state!==\"inactive\"&&_.stop()});async function dt(){let d;try{d=await navigator.mediaDevices.getUserMedia({audio:!0})}catch(g){if(!navigator.mediaDevices){E(\"error\",u(\"audio.no_device_support\"));return}if(g instanceof DOMException&&g.name==\"NotAllowedError\"){E(\"error\",u(\"audio.allow_recording_access\"));return}throw g}if(d!=null){if(v){const[{MediaRecorder:g,register:k},{connect:D}]=await Promise.all($);await k(await D()),_=new g(d,{mimeType:\"audio/wav\"}),_.addEventListener(\"dataavailable\",lt)}else _=new MediaRecorder(d),_.addEventListener(\"dataavailable\",g=>{W.push(g.data)});_.addEventListener(\"stop\",async()=>{B=!1,await x(W,\"change\"),await x(W,\"stop_recording\"),W=[]}),J=!0}}async function lt(d){let g=await d.data.arrayBuffer(),k=new Uint8Array(g);if(K||(K=new Uint8Array(g.slice(0,ot)),k=new Uint8Array(g.slice(ot))),C)R.push(k);else{let D=[K].concat(R,[k]);if(!B||O===\"waiting\")return;x(D,\"stream\"),R=[]}}async function tt(){B=!0,E(\"start_recording\"),J||await dt(),K=void 0,v&&_.state!=\"recording\"&&_.start(L*1e3)}function ct(){E(\"change\",null),E(\"clear\"),Q=\"\",n=null}async function et(){B=!1,v&&(E(\"close_stream\"),E(\"stop_recording\"),_.stop(),C&&(X=!0),x(W,\"stop_recording\"),E(\"clear\"),Q=\"\")}t.value===void 0&&e.value&&n!==void 0&&e.value(n),t.label===void 0&&e.label&&r!==void 0&&e.label(r),t.root===void 0&&e.root&&l!==void 0&&e.root(l),t.loop===void 0&&e.loop&&s!==void 0&&e.loop(s),t.show_label===void 0&&e.show_label&&i!==void 0&&e.show_label(i),t.show_download_button===void 0&&e.show_download_button&&a!==void 0&&e.show_download_button(a),t.sources===void 0&&e.sources&&c!==void 0&&e.sources(c),t.pending===void 0&&e.pending&&C!==void 0&&e.pending(C),t.streaming===void 0&&e.streaming&&v!==void 0&&e.streaming(v),t.i18n===void 0&&e.i18n&&u!==void 0&&e.i18n(u),t.waveform_settings===void 0&&e.waveform_settings&&h!==void 0&&e.waveform_settings(h),t.trim_region_settings===void 0&&e.trim_region_settings&&f!==void 0&&e.trim_region_settings(f),t.waveform_options===void 0&&e.waveform_options&&m!==void 0&&e.waveform_options(m),t.dragging===void 0&&e.dragging&&b!==void 0&&e.dragging(b),t.active_source===void 0&&e.active_source&&p!==void 0&&e.active_source(p),t.handle_reset_value===void 0&&e.handle_reset_value&&z!==void 0&&e.handle_reset_value(z),t.editable===void 0&&e.editable&&S!==void 0&&e.editable(S),t.max_file_size===void 0&&e.max_file_size&&G!==void 0&&e.max_file_size(G),t.upload===void 0&&e.upload&&j!==void 0&&e.upload(j),t.stream_handler===void 0&&e.stream_handler&&P!==void 0&&e.stream_handler(P),t.stream_every===void 0&&e.stream_every&&L!==void 0&&e.stream_every(L),t.uploading===void 0&&e.uploading&&Y!==void 0&&e.uploading(Y),t.recording===void 0&&e.recording&&B!==void 0&&e.recording(B),t.class_name===void 0&&e.class_name&&q!==void 0&&e.class_name(q),t.modify_stream===void 0&&e.modify_stream&&H!==void 0&&e.modify_stream(H),t.set_time_limit===void 0&&e.set_time_limit&&V!==void 0&&e.set_time_limit(V),A.css.add(Ot);let M,nt,Ct=A.head;do{if(M=!0,A.head=Ct,E(\"drag\",b),X&&C===!1&&(X=!1,K&&R)){let d=[K].concat(R);R=[],x(d,\"stream\")}!B&&_&&et(),B&&_&&tt(),nt=`${w(_t,\"BlockLabel\").$$render(A,{show_label:i,Icon:wt,float:p===\"upload\"&&n===null,label:r||u(\"audio.audio\")},{},{})} <div class=\"${\"audio-container \"+I(q,!0)+\" svelte-1ud6e7m\"}\">${w(Bt,\"StreamingBar\").$$render(A,{time_limit:T},{},{})} ${n===null||v?`${p===\"microphone\"?`${w(rt,\"ModifyUpload\").$$render(A,{i18n:u},{},{})} ${v?`${w(Rt,\"StreamAudio\").$$render(A,{record:tt,recording:B,stop:et,i18n:u,waveform_settings:h,waveform_options:m,waiting:O===\"waiting\"},{},{})}`:`${w(Qt,\"AudioRecorder\").$$render(A,{i18n:u,editable:S,recording:B,dispatch_blob:x,waveform_settings:h,waveform_options:m,handle_reset_value:z,mode:Q},{mode:d=>{Q=d,M=!1}},{})}`}`:`${p===\"upload\"?` ${w(ft,\"Upload\").$$render(A,{filetype:\"audio/aac,audio/midi,audio/mpeg,audio/ogg,audio/wav,audio/x-wav,audio/opus,audio/webm,audio/flac,audio/vnd.rn-realaudio,audio/x-ms-wma,audio/x-aiff,audio/amr,audio/*\",root:l,max_file_size:G,upload:j,stream_handler:P,aria_label:u(\"audio.drop_to_upload\"),dragging:b,uploading:Y},{dragging:d=>{b=d,M=!1},uploading:d=>{Y=d,M=!1}},{default:()=>`${o.default?o.default({}):\"\"}`})}`:\"\"}`}`:`${w(rt,\"ModifyUpload\").$$render(A,{i18n:u,download:a?n.url:null},{},{})} ${w(Et,\"AudioPlayer\").$$render(A,{value:n,label:r,i18n:u,dispatch_blob:x,waveform_settings:h,waveform_options:m,trim_region_settings:f,handle_reset_value:z,editable:S,loop:s,interactive:!0,mode:Q},{mode:d=>{Q=d,M=!1}},{})}`} ${w(It,\"SelectSource\").$$render(A,{sources:c,handle_clear:ct,active_source:p},{active_source:d=>{p=d,M=!1}},{})} </div>`}while(!M);return nt}),Tt=Wt;export{Tt as I};\n//# sourceMappingURL=InteractiveAudio.js.map\n"], "names": ["U", "F", "I", "ut", "y", "vt", "mt", "w", "bt", "gt", "pt", "_t", "wt", "Bt", "rt", "ft", "Et", "It"], "mappings": ";;;;;AAA2d,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,oBAAmB,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM,IAAI,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,GAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,EAAC,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAE,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAI,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,EAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,GAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,EAAC,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,EAAC,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,EAAC,CAAC,OAAO,wBAAwB,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,OAAO,SAAS,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,OAAO,GAAE,CAAC,CAAM,MAAC,EAAE,CAAC,CAAC,IAAI,CAAC,yXAAyX,CAAC,GAAG,CAAC,uyEAAuyE,CAAC,CAAC,EAAE,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAACC,qBAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,wBAAwB,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,YAAY,EAAE,CAAC,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,2EAA2E,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,iBAAiB,EAAEC,MAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAEC,IAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,EAAEC,aAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEF,MAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAA8lgB,MAAC,EAAE,CAAC,CAAC,IAAI,CAAC,gcAAgc,CAAC,GAAG,CAAC,uhQAAuhQ,CAAC,CAAC,EAAE,CAACF,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAG,IAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAG,CAAC,CAAC,EAAEC,qBAAC,EAAE,CAAgH,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kBAAkB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oHAAoH,EAAEG,aAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,6CAA6C,EAAEA,aAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,+hEAA+hE,CAAC,GAAG,CAAC,mtPAAmtP,CAAC,CAAC,EAAE,CAACJ,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAA8B,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,qCAAqC,EAAE,CAAC,CAAC,uBAAuB,CAAC,CAAC,IAAI,EAAEK,UAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAED,aAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,sCAAsC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,eAAe,EAAEF,MAAC,CAACI,aAAE,CAAC,CAAC,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,wHAAwH,EAAEJ,MAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,+EAA+E,EAAEK,kBAAC,CAACC,EAAE,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,EAAEN,MAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,uIAAuI,EAAEA,MAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEK,kBAAC,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,0jBAA0jB,CAAC,GAAG,CAAC,woUAAwoU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAACP,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,uBAAc,CAAC,CAAC,OAAO,sBAAa,CAAC,EAAC,CAAC,OAAO,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,MAAM,CAAC,CAACC,qBAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,MAAMQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAACC,SAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,UAAU,EAAE,CAAC,CAAC,IAAI,GAAE,CAAC,CAAC,CAAC,eAAe,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,YAAY,EAAE,CAAC,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,eAAe,CAAC,EAAE,EAAC,CAAC,KAAK,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAE,CAAC,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,WAAW,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAI,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kBAAkB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEH,kBAAC,CAACI,EAAE,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAACC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,kBAAkB,CAACV,MAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,EAAE,EAAEK,kBAAC,CAACM,EAAE,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,CAAC,EAAEN,kBAAC,CAACO,EAAE,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEP,kBAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,kBAAC,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAEA,kBAAC,CAACQ,EAAE,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,uKAAuK,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAER,kBAAC,CAACO,EAAE,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEP,kBAAC,CAACS,EAAE,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAET,kBAAC,CAACU,EAAE,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,EAAC,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;;;"}