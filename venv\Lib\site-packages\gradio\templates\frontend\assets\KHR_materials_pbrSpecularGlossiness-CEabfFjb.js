import{ar as c,C as t,an as a,ao as d}from"./index-Dpxo-yl_.js";import{GLTFLoader as f}from"./glTFLoader-9Z3KGax5.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./bone-kZWM5-u7.js";import"./rawTexture-DmvUfjqF.js";import"./assetContainer-BRzQBugc.js";import"./objectModelMapping-BR4RdEzn.js";const u="KHR_materials_pbrSpecularGlossiness";class h{constructor(r){this.name=u,this.order=200,this._loader=r,this.enabled=this._loader.isExtensionUsed(u)}dispose(){this._loader=null}loadMaterialPropertiesAsync(r,e,s){return f.LoadExtensionAsync(r,e,this.name,(i,o)=>{const l=new Array;return l.push(this._loader.loadMaterialBasePropertiesAsync(r,e,s)),l.push(this._loadSpecularGlossinessPropertiesAsync(i,o,s)),this._loader.loadMaterialAlphaProperties(r,e,s),Promise.all(l).then(()=>{})})}_loadSpecularGlossinessPropertiesAsync(r,e,s){if(!(s instanceof c))throw new Error(`${r}: Material type not supported`);const i=new Array;return s.metallic=null,s.roughness=null,e.diffuseFactor?(s.albedoColor=t.FromArray(e.diffuseFactor),s.alpha=e.diffuseFactor[3]):s.albedoColor=t.White(),s.reflectivityColor=e.specularFactor?t.FromArray(e.specularFactor):t.White(),s.microSurface=e.glossinessFactor==null?1:e.glossinessFactor,e.diffuseTexture&&i.push(this._loader.loadTextureInfoAsync(`${r}/diffuseTexture`,e.diffuseTexture,o=>{o.name=`${s.name} (Diffuse)`,s.albedoTexture=o})),e.specularGlossinessTexture&&(i.push(this._loader.loadTextureInfoAsync(`${r}/specularGlossinessTexture`,e.specularGlossinessTexture,o=>{o.name=`${s.name} (Specular Glossiness)`,s.reflectivityTexture=o,s.reflectivityTexture.hasAlpha=!0})),s.useMicroSurfaceFromReflectivityMapAlpha=!0),Promise.all(i).then(()=>{})}}a(u);d(u,!0,n=>new h(n));export{h as KHR_materials_pbrSpecularGlossiness};
//# sourceMappingURL=KHR_materials_pbrSpecularGlossiness-CEabfFjb.js.map
