import{SvelteComponent as c,init as d,safe_not_equal as m,svg_element as h,claim_svg_element as l,children as s,detach as n,attr as e,insert_hydration as p,append_hydration as g,noop as o}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function v(u){let t,r;return{c(){t=h("svg"),r=h("path"),this.h()},l(i){t=l(i,"svg",{id:!0,xmlns:!0,viewBox:!0,width:!0,height:!0});var a=s(t);r=l(a,"path",{d:!0,fill:!0}),s(r).forEach(n),a.forEach(n),this.h()},h(){e(r,"d","M23,20a5,5,0,0,0-3.89,1.89L11.8,17.32a4.46,4.46,0,0,0,0-2.64l7.31-4.57A5,5,0,1,0,18,7a4.79,4.79,0,0,0,.2,1.32l-7.31,4.57a5,5,0,1,0,0,6.22l7.31,4.57A4.79,4.79,0,0,0,18,25a5,5,0,1,0,5-5ZM23,4a3,3,0,1,1-3,3A3,3,0,0,1,23,4ZM7,19a3,3,0,1,1,3-3A3,3,0,0,1,7,19Zm16,9a3,3,0,1,1,3-3A3,3,0,0,1,23,28Z"),e(r,"fill","currentColor"),e(t,"id","icon"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"viewBox","0 0 32 32"),e(t,"width","100%"),e(t,"height","100%")},m(i,a){p(i,t,a),g(t,r)},p:o,i:o,o,d(i){i&&n(t)}}}class w extends c{constructor(t){super(),d(this,t,null,v,m,{})}}export{w as C};
//# sourceMappingURL=Community.i_uzCNAp.js.map
