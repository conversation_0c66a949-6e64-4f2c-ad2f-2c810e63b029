var vt=Object.defineProperty;var kt=(i,e,n)=>e in i?vt(i,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):i[e]=n;var q=(i,e,n)=>(kt(i,typeof e!="symbol"?e+"":e,n),n);import{SvelteComponent as ge,init as de,safe_not_equal as me,create_slot as Me,element as K,space as x,claim_element as Z,children as $,detach as C,claim_space as ee,get_svelte_dataset as Ge,attr as G,set_style as Q,toggle_class as V,insert_hydration as A,append_hydration as y,listen as nt,update_slot_base as Ie,get_all_dirty_from_scope as Te,get_slot_changes as Re,transition_in as z,transition_out as D,onMount as je,binding_callbacks as j,assign as ve,set_attributes as Je,get_spread_update as Fe,src_url_equal as zt,noop as we,compute_rest_props as qe,createEventDispatcher as Se,tick as it,exclude_internal_props as Xt,create_component as L,empty as _e,claim_component as B,mount_component as P,group_outros as se,check_outros as oe,destroy_component as N,component_subscribe as Yt,bubble as ce,bind as te,add_render_callback as lt,add_iframe_resize_listener as st,add_flush_callback as ne,flush as S,afterUpdate as Et,get_spread_object as ot}from"../../../svelte/svelte.js";import{tweened as Dt}from"../../../svelte/svelte-submodules.js";import{s as ke}from"./select.BigU4G0v.js";import{d as Mt}from"./dispatch.kxCwF96_.js";import{x as It,I as Xe,C as at,B as rt,S as ut}from"./2.B2AoQPnG.js";import{B as ft}from"./BlockLabel.BTSz9r5s.js";import{E as He}from"./Empty.DwQ6nkN6.js";import{D as ht}from"./Download.CpfEFmFf.js";import{I as Ye}from"./Image.CTVzPhL7.js";import{U as Tt}from"./Undo.LhwFM5M8.js";import{I as Rt}from"./IconButtonWrapper.D5aGR59h.js";import{F as St}from"./FullscreenButton.g_8wwg6y.js";import{D as _t}from"./DownloadLink.D1g3Q1HV.js";import{a as ct}from"./Upload.yOHVlgUe.js";import{U as gt}from"./UploadText.CJcy9n89.js";function Ct(i){let e;for(;e=i.sourceEvent;)i=e;return i}function Ke(i,e){if(i=Ct(i),e===void 0&&(e=i.currentTarget),e){var n=e.ownerSVGElement||e;if(n.createSVGPoint){var t=n.createSVGPoint();return t.x=i.clientX,t.y=i.clientY,t=t.matrixTransform(e.getScreenCTM().inverse()),[t.x,t.y]}if(e.getBoundingClientRect){var l=e.getBoundingClientRect();return[i.clientX-l.left-e.clientLeft,i.clientY-l.top-e.clientTop]}}return[i.pageX,i.pageY]}const Lt={passive:!1},ze={capture:!0,passive:!1};function We(i){i.stopImmediatePropagation()}function be(i){i.preventDefault(),i.stopImmediatePropagation()}function Bt(i){var e=i.document.documentElement,n=ke(i).on("dragstart.drag",be,ze);"onselectstart"in e?n.on("selectstart.drag",be,ze):(e.__noselect=e.style.MozUserSelect,e.style.MozUserSelect="none")}function Pt(i,e){var n=i.document.documentElement,t=ke(i).on("dragstart.drag",null);e&&(t.on("click.drag",be,ze),setTimeout(function(){t.on("click.drag",null)},0)),"onselectstart"in n?t.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}const Ee=i=>()=>i;function Ae(i,{sourceEvent:e,subject:n,target:t,identifier:l,active:s,x:u,y:r,dx:o,dy:f,dispatch:a}){Object.defineProperties(this,{type:{value:i,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:t,enumerable:!0,configurable:!0},identifier:{value:l,enumerable:!0,configurable:!0},active:{value:s,enumerable:!0,configurable:!0},x:{value:u,enumerable:!0,configurable:!0},y:{value:r,enumerable:!0,configurable:!0},dx:{value:o,enumerable:!0,configurable:!0},dy:{value:f,enumerable:!0,configurable:!0},_:{value:a}})}Ae.prototype.on=function(){var i=this._.on.apply(this._,arguments);return i===this._?this:i};function Nt(i){return!i.ctrlKey&&!i.button}function Ut(){return this.parentNode}function Vt(i,e){return e??{x:i.x,y:i.y}}function Wt(){return navigator.maxTouchPoints||"ontouchstart"in this}function Ot(){var i=Nt,e=Ut,n=Vt,t=Wt,l={},s=Mt("start","drag","end"),u=0,r,o,f,a,_=0;function k(m){m.on("mousedown.drag",g).filter(t).on("touchstart.drag",v).on("touchmove.drag",Y,Lt).on("touchend.drag touchcancel.drag",b).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function g(m,w){if(!(a||!i.call(this,m,w))){var X=I(this,e.call(this,m,w),m,w,"mouse");X&&(ke(m.view).on("mousemove.drag",h,ze).on("mouseup.drag",d,ze),Bt(m.view),We(m),f=!1,r=m.clientX,o=m.clientY,X("start",m))}}function h(m){if(be(m),!f){var w=m.clientX-r,X=m.clientY-o;f=w*w+X*X>_}l.mouse("drag",m)}function d(m){ke(m.view).on("mousemove.drag mouseup.drag",null),Pt(m.view,f),be(m),l.mouse("end",m)}function v(m,w){if(i.call(this,m,w)){var X=m.changedTouches,T=e.call(this,m,w),R=X.length,c,H;for(c=0;c<R;++c)(H=I(this,T,m,w,X[c].identifier,X[c]))&&(We(m),H("start",m,X[c]))}}function Y(m){var w=m.changedTouches,X=w.length,T,R;for(T=0;T<X;++T)(R=l[w[T].identifier])&&(be(m),R("drag",m,w[T]))}function b(m){var w=m.changedTouches,X=w.length,T,R;for(a&&clearTimeout(a),a=setTimeout(function(){a=null},500),T=0;T<X;++T)(R=l[w[T].identifier])&&(We(m),R("end",m,w[T]))}function I(m,w,X,T,R,c){var H=s.copy(),U=Ke(c||X,w),F,W,O;if((O=n.call(m,new Ae("beforestart",{sourceEvent:X,target:k,identifier:R,active:u,x:U[0],y:U[1],dx:0,dy:0,dispatch:H}),T))!=null)return F=O.x-U[0]||0,W=O.y-U[1]||0,function J(ae,fe,M){var le=U,ie;switch(ae){case"start":l[R]=J,ie=u++;break;case"end":delete l[R],--u;case"drag":U=Ke(M||fe,w),ie=u;break}H.call(ae,m,new Ae(ae,{sourceEvent:fe,subject:O,target:k,identifier:R,active:ie,x:U[0]+F,y:U[1]+W,dx:U[0]-le[0],dy:U[1]-le[1],dispatch:H}),T)}}return k.filter=function(m){return arguments.length?(i=typeof m=="function"?m:Ee(!!m),k):i},k.container=function(m){return arguments.length?(e=typeof m=="function"?m:Ee(m),k):e},k.subject=function(m){return arguments.length?(n=typeof m=="function"?m:Ee(m),k):n},k.touchable=function(m){return arguments.length?(t=typeof m=="function"?m:Ee(!!m),k):t},k.on=function(){var m=s.on.apply(s,arguments);return m===s?k:m},k.clickDistance=function(m){return arguments.length?(_=(m=+m)*m,k):Math.sqrt(_)},k}function At(i){let e,n,t,l,s,u,r="◢",o,f,a="◢",_,k,g,h,d;const v=i[11].default,Y=Me(v,i,i[10],null);return{c(){e=K("div"),n=K("div"),Y&&Y.c(),t=x(),l=K("div"),s=K("span"),u=K("span"),u.textContent=r,o=K("span"),f=K("span"),f.textContent=a,_=x(),k=K("div"),this.h()},l(b){e=Z(b,"DIV",{class:!0,role:!0});var I=$(e);n=Z(I,"DIV",{class:!0});var m=$(n);Y&&Y.l(m),m.forEach(C),t=ee(I),l=Z(I,"DIV",{class:!0,role:!0,style:!0});var w=$(l);s=Z(w,"SPAN",{class:!0});var X=$(s);u=Z(X,"SPAN",{class:!0,"data-svelte-h":!0}),Ge(u)!=="svelte-9lsvah"&&(u.textContent=r),o=Z(X,"SPAN",{class:!0}),$(o).forEach(C),f=Z(X,"SPAN",{class:!0,"data-svelte-h":!0}),Ge(f)!=="svelte-1lu38by"&&(f.textContent=a),X.forEach(C),_=ee(w),k=Z(w,"DIV",{class:!0}),$(k).forEach(C),w.forEach(C),I.forEach(C),this.h()},h(){G(n,"class","content svelte-fpmna9"),G(u,"class","icon left svelte-fpmna9"),G(o,"class","icon center svelte-fpmna9"),Q(o,"--color",i[4]),G(f,"class","icon right svelte-fpmna9"),G(s,"class","icon-wrap svelte-fpmna9"),V(s,"active",i[7]),V(s,"disabled",i[3]),G(k,"class","inner svelte-fpmna9"),Q(k,"--color",i[4]),G(l,"class","outer svelte-fpmna9"),G(l,"role","none"),Q(l,"transform","translateX("+i[6]+"px)"),V(l,"disabled",i[3]),V(l,"grab",i[7]),G(e,"class","wrap svelte-fpmna9"),G(e,"role","none")},m(b,I){A(b,e,I),y(e,n),Y&&Y.m(n,null),i[13](n),y(e,t),y(e,l),y(l,s),y(s,u),y(s,o),y(s,f),y(l,_),y(l,k),i[14](l),i[15](e),g=!0,h||(d=nt(window,"resize",i[12]),h=!0)},p(b,[I]){Y&&Y.p&&(!g||I&1024)&&Ie(Y,v,b,b[10],g?Re(v,b[10],I,null):Te(b[10]),null),I&16&&Q(o,"--color",b[4]),(!g||I&128)&&V(s,"active",b[7]),(!g||I&8)&&V(s,"disabled",b[3]),I&16&&Q(k,"--color",b[4]),(!g||I&64)&&Q(l,"transform","translateX("+b[6]+"px)"),(!g||I&8)&&V(l,"disabled",b[3]),(!g||I&128)&&V(l,"grab",b[7])},i(b){g||(z(Y,b),g=!0)},o(b){D(Y,b),g=!1},d(b){b&&C(e),Y&&Y.d(b),i[13](null),i[14](null),i[15](null),h=!1,d()}}}function Oe(i,e,n){return Math.min(Math.max(i,e),n)}function jt(i,e){const n=Math.pow(10,e);return Math.round((i+Number.EPSILON)*n)/n}function Ft(i,e,n){let{$$slots:t={},$$scope:l}=e,{position:s=.5}=e,{disabled:u=!1}=e,{slider_color:r="var(--border-color-primary)"}=e,{image_size:o={top:0,left:0,width:0,height:0}}=e,{el:f=void 0}=e,{parent_el:a=void 0}=e,_,k=0,g=!1,h=0;function d(c){h=(a==null?void 0:a.getBoundingClientRect().width)||0,c===0&&n(0,o.width=(f==null?void 0:f.getBoundingClientRect().width)||0,o),n(6,k=Oe(o.width*s+o.left,0,h))}function v(c){n(6,k=Oe(c,0,h)),n(9,s=jt((c-o.left)/o.width,5))}function Y(c){u||(n(7,g=!0),v(c.x))}function b(c){u||v(c.x)}function I(){u||n(7,g=!1)}function m(c){n(6,k=Oe(o.width*c+o.left,0,h))}je(()=>{d(o.width);const c=Ot().on("start",Y).on("drag",b).on("end",I);ke(_).call(c)});const w=()=>d(o.width);function X(c){j[c?"unshift":"push"](()=>{f=c,n(1,f)})}function T(c){j[c?"unshift":"push"](()=>{_=c,n(5,_)})}function R(c){j[c?"unshift":"push"](()=>{a=c,n(2,a)})}return i.$$set=c=>{"position"in c&&n(9,s=c.position),"disabled"in c&&n(3,u=c.disabled),"slider_color"in c&&n(4,r=c.slider_color),"image_size"in c&&n(0,o=c.image_size),"el"in c&&n(1,f=c.el),"parent_el"in c&&n(2,a=c.parent_el),"$$scope"in c&&n(10,l=c.$$scope)},i.$$.update=()=>{i.$$.dirty&1&&d(o.width),i.$$.dirty&512&&m(s)},[o,f,a,u,r,_,k,g,d,s,l,t,w,X,T,R]}class dt extends ge{constructor(e){super(),de(this,e,Ft,At,me,{position:9,disabled:3,slider_color:4,image_size:0,el:1,parent_el:2})}}function Ht(i){let e,n,t,l,s=[{src:n=i[7]},i[9]],u={};for(let r=0;r<s.length;r+=1)u=ve(u,s[r]);return{c(){e=K("img"),this.h()},l(r){e=Z(r,"IMG",{src:!0}),this.h()},h(){Je(e,u),V(e,"fixed",i[2]),V(e,"hidden",i[4]),V(e,"preview",i[5]==="preview"),V(e,"slider",i[5]==="upload"),V(e,"fullscreen",i[1]),V(e,"small",!i[1]),Q(e,"transform",i[3]),Q(e,"max-height",i[6]&&!i[1]?`${i[6]}px`:null),V(e,"svelte-k63p1v",!0)},m(r,o){A(r,e,o),i[12](e),t||(l=nt(e,"load",i[13]),t=!0)},p(r,[o]){Je(e,u=Fe(s,[o&128&&!zt(e.src,n=r[7])&&{src:n},o&512&&r[9]])),V(e,"fixed",r[2]),V(e,"hidden",r[4]),V(e,"preview",r[5]==="preview"),V(e,"slider",r[5]==="upload"),V(e,"fullscreen",r[1]),V(e,"small",!r[1]),Q(e,"transform",r[3]),Q(e,"max-height",r[6]&&!r[1]?`${r[6]}px`:null),V(e,"svelte-k63p1v",!0)},i:we,o:we,d(r){r&&C(e),i[12](null),t=!1,l()}}}function Ze(i){var o;if(!i)return{top:0,left:0,width:0,height:0};const e=(o=i.parentElement)==null?void 0:o.getBoundingClientRect();if(!e)return{top:0,left:0,width:0,height:0};const n=i.naturalWidth/i.naturalHeight,t=e.width/e.height;let l,s;n>t?(l=e.width,s=e.width/n):(s=e.height,l=e.height*n);const u=(e.width-l)/2;return{top:(e.height-s)/2,left:u,width:l,height:s}}function Gt(i,e,n){const t=["src","fullscreen","fixed","transform","img_el","hidden","variant","max_height"];let l=qe(e,t),{src:s=void 0}=e,{fullscreen:u=!1}=e,r,{fixed:o=!1}=e,{transform:f="translate(0px, 0px) scale(1)"}=e,{img_el:a=null}=e,{hidden:_=!1}=e,{variant:k="upload"}=e,{max_height:g=500}=e,h;const d=Se();je(()=>{const b=new ResizeObserver(async I=>{for(const m of I)await it(),d("load",Ze(a))});return b.observe(a),()=>{b.disconnect()}});function v(b){j[b?"unshift":"push"](()=>{a=b,n(0,a)})}const Y=()=>d("load",Ze(a));return i.$$set=b=>{e=ve(ve({},e),Xt(b)),n(9,l=qe(e,t)),"src"in b&&n(10,s=b.src),"fullscreen"in b&&n(1,u=b.fullscreen),"fixed"in b&&n(2,o=b.fixed),"transform"in b&&n(3,f=b.transform),"img_el"in b&&n(0,a=b.img_el),"hidden"in b&&n(4,_=b.hidden),"variant"in b&&n(5,k=b.variant),"max_height"in b&&n(6,g=b.max_height)},i.$$.update=()=>{if(i.$$.dirty&3072){n(7,r=s),n(11,h=s);const b=s;It(b).then(I=>{h===b&&n(7,r=I)})}},[a,u,o,f,_,k,g,r,d,l,s,h,v,Y]}class De extends ge{constructor(e){super(),de(this,e,Gt,Ht,me,{src:10,fullscreen:1,fixed:2,transform:3,img_el:0,hidden:4,variant:5,max_height:6})}}class Jt{constructor(e,n){q(this,"container");q(this,"image");q(this,"scale");q(this,"offsetX");q(this,"offsetY");q(this,"isDragging");q(this,"lastX");q(this,"lastY");q(this,"initial_left_padding");q(this,"initial_top_padding");q(this,"initial_width");q(this,"initial_height");q(this,"subscribers");q(this,"handleImageLoad");q(this,"real_image_size",{top:0,left:0,width:0,height:0});q(this,"last_touch_distance");this.container=e,this.image=n,this.scale=1,this.offsetX=0,this.offsetY=0,this.isDragging=!1,this.lastX=0,this.lastY=0,this.initial_left_padding=0,this.initial_top_padding=0,this.initial_width=0,this.initial_height=0,this.subscribers=[],this.last_touch_distance=0,this.handleWheel=this.handleWheel.bind(this),this.handleMouseDown=this.handleMouseDown.bind(this),this.handleMouseMove=this.handleMouseMove.bind(this),this.handleMouseUp=this.handleMouseUp.bind(this),this.handleImageLoad=this.init.bind(this),this.handleTouchStart=this.handleTouchStart.bind(this),this.handleTouchMove=this.handleTouchMove.bind(this),this.handleTouchEnd=this.handleTouchEnd.bind(this),this.image.addEventListener("load",this.handleImageLoad),this.container.addEventListener("wheel",this.handleWheel),this.container.addEventListener("mousedown",this.handleMouseDown),document.addEventListener("mousemove",this.handleMouseMove),document.addEventListener("mouseup",this.handleMouseUp),this.container.addEventListener("touchstart",this.handleTouchStart),document.addEventListener("touchmove",this.handleTouchMove),document.addEventListener("touchend",this.handleTouchEnd),new ResizeObserver(l=>{for(const s of l)s.target===this.container&&(this.handleResize(),this.get_image_size(this.image))}).observe(this.container)}handleResize(){this.init()}init(){const e=this.container.getBoundingClientRect(),n=this.image.getBoundingClientRect();this.initial_left_padding=n.left-e.left,this.initial_top_padding=n.top-e.top,this.initial_width=n.width,this.initial_height=n.height,this.reset_zoom(),this.updateTransform()}reset_zoom(){this.scale=1,this.offsetX=0,this.offsetY=0,this.updateTransform()}handleMouseDown(e){const n=this.image.getBoundingClientRect();if(e.clientX>=n.left&&e.clientX<=n.right&&e.clientY>=n.top&&e.clientY<=n.bottom){if(e.preventDefault(),this.scale===1)return;this.isDragging=!0,this.lastX=e.clientX,this.lastY=e.clientY,this.image.style.cursor="grabbing"}}handleMouseMove(e){if(!this.isDragging)return;const n=e.clientX-this.lastX,t=e.clientY-this.lastY;this.offsetX+=n,this.offsetY+=t,this.lastX=e.clientX,this.lastY=e.clientY,this.updateTransform(),this.updateTransform()}handleMouseUp(){this.isDragging&&(this.constrain_to_bounds(!0),this.updateTransform(),this.isDragging=!1,this.image.style.cursor=this.scale>1?"grab":"zoom-in")}async handleWheel(e){e.preventDefault();const n=this.container.getBoundingClientRect(),t=this.image.getBoundingClientRect();if(e.clientX<t.left||e.clientX>t.right||e.clientY<t.top||e.clientY>t.bottom)return;const l=1.05,s=this.scale,u=-Math.sign(e.deltaY)>0?Math.min(15,s*l):Math.max(1,s/l);if(u===s)return;const r=e.clientX-n.left-this.initial_left_padding,o=e.clientY-n.top-this.initial_top_padding;this.scale=u,this.offsetX=this.compute_new_offset({cursor_position:r,current_offset:this.offsetX,new_scale:u,old_scale:s}),this.offsetY=this.compute_new_offset({cursor_position:o,current_offset:this.offsetY,new_scale:u,old_scale:s}),this.updateTransform(),this.constrain_to_bounds(),this.updateTransform(),this.image.style.cursor=this.scale>1?"grab":"zoom-in"}compute_new_position({position:e,scale:n,anchor_position:t}){return e-(e-t)*(n/this.scale)}compute_new_offset({cursor_position:e,current_offset:n,new_scale:t,old_scale:l}){return e-t/l*(e-n)}constrain_to_bounds(e=!1){if(this.scale===1){this.offsetX=0,this.offsetY=0;return}const n={top:this.real_image_size.top*this.scale+this.offsetY,left:this.real_image_size.left*this.scale+this.offsetX,width:this.real_image_size.width*this.scale,height:this.real_image_size.height*this.scale,bottom:this.real_image_size.top*this.scale+this.offsetY+this.real_image_size.height*this.scale,right:this.real_image_size.left*this.scale+this.offsetX+this.real_image_size.width*this.scale},t=this.real_image_size.left+this.real_image_size.width,l=this.real_image_size.top+this.real_image_size.height;e&&(n.top>this.real_image_size.top?this.offsetY=this.calculate_position(this.real_image_size.top,0,"y"):n.bottom<l&&(this.offsetY=this.calculate_position(l,1,"y")),n.left>this.real_image_size.left?this.offsetX=this.calculate_position(this.real_image_size.left,0,"x"):n.right<t&&(this.offsetX=this.calculate_position(t,1,"x")))}updateTransform(){this.notify({x:this.offsetX,y:this.offsetY,scale:this.scale})}destroy(){this.container.removeEventListener("wheel",this.handleWheel),this.container.removeEventListener("mousedown",this.handleMouseDown),document.removeEventListener("mousemove",this.handleMouseMove),document.removeEventListener("mouseup",this.handleMouseUp),this.container.removeEventListener("touchstart",this.handleTouchStart),document.removeEventListener("touchmove",this.handleTouchMove),document.removeEventListener("touchend",this.handleTouchEnd),this.image.removeEventListener("load",this.handleImageLoad)}subscribe(e){this.subscribers.push(e)}unsubscribe(e){this.subscribers=this.subscribers.filter(n=>n!==e)}notify({x:e,y:n,scale:t}){this.subscribers.forEach(l=>l({x:e,y:n,scale:t}))}handleTouchStart(e){e.preventDefault();const n=this.image.getBoundingClientRect(),t=e.touches[0];if(t.clientX>=n.left&&t.clientX<=n.right&&t.clientY>=n.top&&t.clientY<=n.bottom){if(e.touches.length===1&&this.scale>1)this.isDragging=!0,this.lastX=t.clientX,this.lastY=t.clientY;else if(e.touches.length===2){const l=e.touches[0],s=e.touches[1];this.last_touch_distance=Math.hypot(s.clientX-l.clientX,s.clientY-l.clientY)}}}get_image_size(e){var f;if(!e)return;const n=(f=e.parentElement)==null?void 0:f.getBoundingClientRect();if(!n)return;const t=e.naturalWidth/e.naturalHeight,l=n.width/n.height;let s,u;t>l?(s=n.width,u=n.width/t):(u=n.height,s=n.height*t);const r=(n.width-s)/2,o=(n.height-u)/2;this.real_image_size={top:o,left:r,width:s,height:u}}handleTouchMove(e){if(e.touches.length===1&&this.isDragging){e.preventDefault();const n=e.touches[0],t=n.clientX-this.lastX,l=n.clientY-this.lastY;this.offsetX+=t,this.offsetY+=l,this.lastX=n.clientX,this.lastY=n.clientY,this.updateTransform()}else if(e.touches.length===2){e.preventDefault();const n=e.touches[0],t=e.touches[1],l=Math.hypot(t.clientX-n.clientX,t.clientY-n.clientY);if(this.last_touch_distance===0){this.last_touch_distance=l;return}const s=l/this.last_touch_distance,u=this.scale,r=Math.min(15,Math.max(1,u*s));if(r===u){this.last_touch_distance=l;return}const o=this.container.getBoundingClientRect(),f=(n.clientX+t.clientX)/2-o.left-this.initial_left_padding,a=(n.clientY+t.clientY)/2-o.top-this.initial_top_padding;this.scale=r,this.offsetX=this.compute_new_offset({cursor_position:f,current_offset:this.offsetX,new_scale:r,old_scale:u}),this.offsetY=this.compute_new_offset({cursor_position:a,current_offset:this.offsetY,new_scale:r,old_scale:u}),this.updateTransform(),this.constrain_to_bounds(),this.updateTransform(),this.last_touch_distance=l,this.image.style.cursor=this.scale>1?"grab":"zoom-in"}}handleTouchEnd(e){this.isDragging&&(this.constrain_to_bounds(!0),this.updateTransform(),this.isDragging=!1),e.touches.length===0&&(this.last_touch_distance=0)}calculate_position(e,n,t){if(this.container.getBoundingClientRect(),t==="x"){const l=e,s=this.real_image_size.left+n*this.real_image_size.width;return l-s*this.scale}if(t==="y"){const l=e,s=this.real_image_size.top+n*this.real_image_size.height;return l-s*this.scale}return 0}}function qt(i){let e,n,t,l,s,u,r,o,f,a;n=new Rt({props:{$$slots:{default:[Qt]},$$scope:{ctx:i}}});function _(d){i[32](d)}function k(d){i[33](d)}function g(d){i[34](d)}let h={slider_color:i[9],image_size:i[16],$$slots:{default:[yt]},$$scope:{ctx:i}};return i[0]!==void 0&&(h.position=i[0]),i[15]!==void 0&&(h.el=i[15]),i[19]!==void 0&&(h.parent_el=i[19]),s=new dt({props:h}),j.push(()=>te(s,"position",_)),j.push(()=>te(s,"el",k)),j.push(()=>te(s,"parent_el",g)),{c(){e=K("div"),L(n.$$.fragment),t=x(),l=K("div"),L(s.$$.fragment),this.h()},l(d){e=Z(d,"DIV",{class:!0});var v=$(e);B(n.$$.fragment,v),t=ee(v),l=Z(v,"DIV",{class:!0});var Y=$(l);B(s.$$.fragment,Y),Y.forEach(C),v.forEach(C),this.h()},h(){G(l,"class","slider-wrap svelte-eb87wk"),lt(()=>i[36].call(l)),V(l,"limit_height",!i[11]),G(e,"class","image-container svelte-eb87wk")},m(d,v){A(d,e,v),P(n,e,null),y(e,t),y(e,l),P(s,l,null),i[35](l),f=st(l,i[36].bind(l)),i[37](e),a=!0},p(d,v){const Y={};v[0]&1190994|v[1]&1024&&(Y.$$scope={dirty:v,ctx:d}),n.$set(Y);const b={};v[0]&512&&(b.slider_color=d[9]),v[0]&65536&&(b.image_size=d[16]),v[0]&4348034|v[1]&1024&&(b.$$scope={dirty:v,ctx:d}),!u&&v[0]&1&&(u=!0,b.position=d[0],ne(()=>u=!1)),!r&&v[0]&32768&&(r=!0,b.el=d[15],ne(()=>r=!1)),!o&&v[0]&524288&&(o=!0,b.parent_el=d[19],ne(()=>o=!1)),s.$set(b),(!a||v[0]&2048)&&V(l,"limit_height",!d[11])},i(d){a||(z(n.$$.fragment,d),z(s.$$.fragment,d),a=!0)},o(d){D(n.$$.fragment,d),D(s.$$.fragment,d),a=!1},d(d){d&&C(e),N(n),N(s),i[35](null),f(),i[37](null)}}}function Kt(i){let e,n;return e=new He({props:{unpadded_box:!0,size:"large",$$slots:{default:[$t]},$$scope:{ctx:i}}}),{c(){L(e.$$.fragment)},l(t){B(e.$$.fragment,t)},m(t,l){P(e,t,l),n=!0},p(t,l){const s={};l[1]&1024&&(s.$$scope={dirty:l,ctx:t}),e.$set(s)},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){N(e,t)}}}function Qe(i){let e,n;return e=new St({props:{fullscreen:i[11]}}),e.$on("fullscreen",i[29]),{c(){L(e.$$.fragment)},l(t){B(e.$$.fragment,t)},m(t,l){P(e,t,l),n=!0},p(t,l){const s={};l[0]&2048&&(s.fullscreen=t[11]),e.$set(s)},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){N(e,t)}}}function ye(i){var t,l;let e,n;return e=new _t({props:{href:(t=i[1][1])==null?void 0:t.url,download:((l=i[1][1])==null?void 0:l.orig_name)||"image",$$slots:{default:[Zt]},$$scope:{ctx:i}}}),{c(){L(e.$$.fragment)},l(s){B(e.$$.fragment,s)},m(s,u){P(e,s,u),n=!0},p(s,u){var o,f;const r={};u[0]&2&&(r.href=(o=s[1][1])==null?void 0:o.url),u[0]&2&&(r.download=((f=s[1][1])==null?void 0:f.orig_name)||"image"),u[0]&64|u[1]&1024&&(r.$$scope={dirty:u,ctx:s}),e.$set(r)},i(s){n||(z(e.$$.fragment,s),n=!0)},o(s){D(e.$$.fragment,s),n=!1},d(s){N(e,s)}}}function Zt(i){let e,n;return e=new Xe({props:{Icon:ht,label:i[6]("common.download")}}),{c(){L(e.$$.fragment)},l(t){B(e.$$.fragment,t)},m(t,l){P(e,t,l),n=!0},p(t,l){const s={};l[0]&64&&(s.label=t[6]("common.download")),e.$set(s)},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){N(e,t)}}}function $e(i){let e,n;return e=new Xe({props:{Icon:at,label:"Remove Image"}}),e.$on("click",i[30]),{c(){L(e.$$.fragment)},l(t){B(e.$$.fragment,t)},m(t,l){P(e,t,l),n=!0},p:we,i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){N(e,t)}}}function Qt(i){let e,n,t,l,s,u;e=new Xe({props:{Icon:Tt,label:i[6]("common.undo"),disabled:i[17].z===1}}),e.$on("click",i[28]);let r=i[10]&&Qe(i),o=i[4]&&ye(i),f=i[13]&&$e(i);return{c(){L(e.$$.fragment),n=x(),r&&r.c(),t=x(),o&&o.c(),l=x(),f&&f.c(),s=_e()},l(a){B(e.$$.fragment,a),n=ee(a),r&&r.l(a),t=ee(a),o&&o.l(a),l=ee(a),f&&f.l(a),s=_e()},m(a,_){P(e,a,_),A(a,n,_),r&&r.m(a,_),A(a,t,_),o&&o.m(a,_),A(a,l,_),f&&f.m(a,_),A(a,s,_),u=!0},p(a,_){const k={};_[0]&64&&(k.label=a[6]("common.undo")),_[0]&131072&&(k.disabled=a[17].z===1),e.$set(k),a[10]?r?(r.p(a,_),_[0]&1024&&z(r,1)):(r=Qe(a),r.c(),z(r,1),r.m(t.parentNode,t)):r&&(se(),D(r,1,1,()=>{r=null}),oe()),a[4]?o?(o.p(a,_),_[0]&16&&z(o,1)):(o=ye(a),o.c(),z(o,1),o.m(l.parentNode,l)):o&&(se(),D(o,1,1,()=>{o=null}),oe()),a[13]?f?(f.p(a,_),_[0]&8192&&z(f,1)):(f=$e(a),f.c(),z(f,1),f.m(s.parentNode,s)):f&&(se(),D(f,1,1,()=>{f=null}),oe())},i(a){u||(z(e.$$.fragment,a),z(r),z(o),z(f),u=!0)},o(a){D(e.$$.fragment,a),D(r),D(o),D(f),u=!1},d(a){a&&(C(n),C(t),C(l),C(s)),N(e,a),r&&r.d(a),o&&o.d(a),f&&f.d(a)}}}function yt(i){var o,f,a,_,k,g;let e,n,t,l,s;function u(h){i[31](h)}let r={src:(f=(o=i[1])==null?void 0:o[0])==null?void 0:f.url,alt:"",loading:"lazy",variant:"preview",transform:"translate("+i[17].x+"px, "+i[17].y+"px) scale("+i[17].z+")",fullscreen:i[11],max_height:i[12]};return i[14]!==void 0&&(r.img_el=i[14]),e=new De({props:r}),j.push(()=>te(e,"img_el",u)),e.$on("load",i[25]),l=new De({props:{variant:"preview",fixed:i[7],hidden:!((_=(a=i[1])==null?void 0:a[1])!=null&&_.url),src:(g=(k=i[1])==null?void 0:k[1])==null?void 0:g.url,alt:"",loading:"lazy",style:i[22]+"; background: var(--block-background-fill);",transform:"translate("+i[17].x+"px, "+i[17].y+"px) scale("+i[17].z+")",fullscreen:i[11],max_height:i[12]}}),l.$on("load",i[25]),{c(){L(e.$$.fragment),t=x(),L(l.$$.fragment)},l(h){B(e.$$.fragment,h),t=ee(h),B(l.$$.fragment,h)},m(h,d){P(e,h,d),A(h,t,d),P(l,h,d),s=!0},p(h,d){var b,I,m,w,X,T;const v={};d[0]&2&&(v.src=(I=(b=h[1])==null?void 0:b[0])==null?void 0:I.url),d[0]&131072&&(v.transform="translate("+h[17].x+"px, "+h[17].y+"px) scale("+h[17].z+")"),d[0]&2048&&(v.fullscreen=h[11]),d[0]&4096&&(v.max_height=h[12]),!n&&d[0]&16384&&(n=!0,v.img_el=h[14],ne(()=>n=!1)),e.$set(v);const Y={};d[0]&128&&(Y.fixed=h[7]),d[0]&2&&(Y.hidden=!((w=(m=h[1])==null?void 0:m[1])!=null&&w.url)),d[0]&2&&(Y.src=(T=(X=h[1])==null?void 0:X[1])==null?void 0:T.url),d[0]&4194304&&(Y.style=h[22]+"; background: var(--block-background-fill);"),d[0]&131072&&(Y.transform="translate("+h[17].x+"px, "+h[17].y+"px) scale("+h[17].z+")"),d[0]&2048&&(Y.fullscreen=h[11]),d[0]&4096&&(Y.max_height=h[12]),l.$set(Y)},i(h){s||(z(e.$$.fragment,h),z(l.$$.fragment,h),s=!0)},o(h){D(e.$$.fragment,h),D(l.$$.fragment,h),s=!1},d(h){h&&C(t),N(e,h),N(l,h)}}}function $t(i){let e,n;return e=new Ye({}),{c(){L(e.$$.fragment)},l(t){B(e.$$.fragment,t)},m(t,l){P(e,t,l),n=!0},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){N(e,t)}}}function xt(i){let e,n,t,l,s,u;e=new ft({props:{show_label:i[5],Icon:Ye,label:i[3]||i[6]("image.image")}});const r=[Kt,qt],o=[];function f(a,_){return(a[1]===null||a[1][0]===null||a[1][1]===null)&&!a[8]?0:1}return t=f(i),l=o[t]=r[t](i),{c(){L(e.$$.fragment),n=x(),l.c(),s=_e()},l(a){B(e.$$.fragment,a),n=ee(a),l.l(a),s=_e()},m(a,_){P(e,a,_),A(a,n,_),o[t].m(a,_),A(a,s,_),u=!0},p(a,_){const k={};_[0]&32&&(k.show_label=a[5]),_[0]&72&&(k.label=a[3]||a[6]("image.image")),e.$set(k);let g=t;t=f(a),t===g?o[t].p(a,_):(se(),D(o[g],1,1,()=>{o[g]=null}),oe(),l=o[t],l?l.p(a,_):(l=o[t]=r[t](a),l.c()),z(l,1),l.m(s.parentNode,s))},i(a){u||(z(e.$$.fragment,a),z(l),u=!0)},o(a){D(e.$$.fragment,a),D(l),u=!1},d(a){a&&(C(n),C(s)),N(e,a),o[t].d(a)}}}function en(i,e,n,t,l,s){return(i*n+t-l)/s/e}function tn(i,e,n){let t,l,s,{value:u=[null,null]}=e,{label:r=void 0}=e,{show_download_button:o=!0}=e,{show_label:f}=e,{i18n:a}=e,{position:_}=e,{layer_images:k=!0}=e,{show_single:g=!1}=e,{slider_color:h}=e,{show_fullscreen_button:d=!0}=e,{fullscreen:v=!1}=e,{el_width:Y=0}=e,{max_height:b}=e,{interactive:I=!0}=e;const m=Se();let w,X,T,R=Dt({x:0,y:0,z:1},{duration:75});Yt(i,R,E=>n(17,s=E));let c,H=0,U=null,F=null;function W(E,re){!E||!re||(U==null||U.destroy(),F==null||F.disconnect(),E!=null&&E.getBoundingClientRect().width,n(26,H=(re==null?void 0:re.getBoundingClientRect().width)||0),n(20,U=new Jt(re,E)),U.subscribe(({x:pe,y:he,scale:Ve})=>{R.set({x:pe,y:he,z:Ve})}),F=new ResizeObserver(pe=>{for(const he of pe)he.target===re&&n(26,H=he.contentRect.width),he.target===E&&he.contentRect.width}),F.observe(re),F.observe(E))}je(()=>()=>{U==null||U.destroy(),F==null||F.disconnect()});let O,J={top:0,left:0,width:0,height:0};function ae(E){n(16,J=E.detail)}const fe=()=>U==null?void 0:U.reset_zoom();function M(E){ce.call(this,i,E)}const le=E=>{n(1,u=[null,null]),m("clear"),E.stopPropagation()};function ie(E){w=E,n(14,w)}function Ce(E){_=E,n(0,_)}function Le(E){X=E,n(15,X)}function Be(E){c=E,n(19,c)}function Pe(E){j[E?"unshift":"push"](()=>{O=E,n(21,O)})}function Ne(){Y=this.clientWidth,n(2,Y)}function Ue(E){j[E?"unshift":"push"](()=>{T=E,n(18,T)})}return i.$$set=E=>{"value"in E&&n(1,u=E.value),"label"in E&&n(3,r=E.label),"show_download_button"in E&&n(4,o=E.show_download_button),"show_label"in E&&n(5,f=E.show_label),"i18n"in E&&n(6,a=E.i18n),"position"in E&&n(0,_=E.position),"layer_images"in E&&n(7,k=E.layer_images),"show_single"in E&&n(8,g=E.show_single),"slider_color"in E&&n(9,h=E.slider_color),"show_fullscreen_button"in E&&n(10,d=E.show_fullscreen_button),"fullscreen"in E&&n(11,v=E.fullscreen),"el_width"in E&&n(2,Y=E.el_width),"max_height"in E&&n(12,b=E.max_height),"interactive"in E&&n(13,I=E.interactive)},i.$$.update=()=>{i.$$.dirty[0]&67305473&&n(27,t=en(_,H,J.width,J.left,s.x,s.z)),i.$$.dirty[0]&134217856&&n(22,l=k?`clip-path: inset(0 0 0 ${t*100}%)`:""),i.$$.dirty[0]&49152&&W(w,X)},[_,u,Y,r,o,f,a,k,g,h,d,v,b,I,w,X,J,s,T,c,U,O,l,m,R,ae,H,t,fe,M,le,ie,Ce,Le,Be,Pe,Ne,Ue]}class nn extends ge{constructor(e){super(),de(this,e,tn,xt,me,{value:1,label:3,show_download_button:4,show_label:5,i18n:6,position:0,layer_images:7,show_single:8,slider_color:9,show_fullscreen_button:10,fullscreen:11,el_width:2,max_height:12,interactive:13},null,[-1,-1])}}function ln(i){let e,n,t;return n=new Xe({props:{Icon:at,label:"Remove Image"}}),n.$on("click",i[1]),{c(){e=K("div"),L(n.$$.fragment),this.h()},l(l){e=Z(l,"DIV",{class:!0});var s=$(e);B(n.$$.fragment,s),s.forEach(C),this.h()},h(){G(e,"class","svelte-s6ybro")},m(l,s){A(l,e,s),P(n,e,null),t=!0},p:we,i(l){t||(z(n.$$.fragment,l),t=!0)},o(l){D(n.$$.fragment,l),t=!1},d(l){l&&C(e),N(n)}}}function sn(i){const e=Se();return[e,t=>{e("remove_image"),t.stopPropagation()}]}class on extends ge{constructor(e){super(),de(this,e,sn,ln,me,{})}}function xe(i){let e,n;return e=new on({}),e.$on("remove_image",i[22]),{c(){L(e.$$.fragment)},l(t){B(e.$$.fragment,t)},m(t,l){P(e,t,l),n=!0},p:we,i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){N(e,t)}}}function et(i){let e,n,t=i[7]&&tt(i);return{c(){e=K("div"),t&&t.c(),this.h()},l(l){e=Z(l,"DIV",{class:!0});var s=$(e);t&&t.l(s),s.forEach(C),this.h()},h(){G(e,"class","icon-buttons svelte-143b07a")},m(l,s){A(l,e,s),t&&t.m(e,null),n=!0},p(l,s){l[7]?t?(t.p(l,s),s&128&&z(t,1)):(t=tt(l),t.c(),z(t,1),t.m(e,null)):t&&(se(),D(t,1,1,()=>{t=null}),oe())},i(l){n||(z(t),n=!0)},o(l){D(t),n=!1},d(l){l&&C(e),t&&t.d()}}}function tt(i){let e,n;return e=new _t({props:{href:i[0][1].url,download:i[0][1].orig_name||"image",$$slots:{default:[an]},$$scope:{ctx:i}}}),{c(){L(e.$$.fragment)},l(t){B(e.$$.fragment,t)},m(t,l){P(e,t,l),n=!0},p(t,l){const s={};l&1&&(s.href=t[0][1].url),l&1&&(s.download=t[0][1].orig_name||"image"),l&1073741824&&(s.$$scope={dirty:l,ctx:t}),e.$set(s)},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){N(e,t)}}}function an(i){let e,n;return e=new Xe({props:{Icon:ht}}),{c(){L(e.$$.fragment)},l(t){B(e.$$.fragment,t)},m(t,l){P(e,t,l),n=!0},p:we,i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){N(e,t)}}}function rn(i){var u;let e,n,t;function l(r){i[25](r)}let s={variant:"upload",src:(u=i[14][0])==null?void 0:u.url,alt:"",max_height:i[13]};return i[15]!==void 0&&(s.img_el=i[15]),e=new De({props:s}),j.push(()=>te(e,"img_el",l)),{c(){L(e.$$.fragment)},l(r){B(e.$$.fragment,r)},m(r,o){P(e,r,o),t=!0},p(r,o){var a;const f={};o&16384&&(f.src=(a=r[14][0])==null?void 0:a.url),o&8192&&(f.max_height=r[13]),!n&&o&32768&&(n=!0,f.img_el=r[15],ne(()=>n=!1)),e.$set(f)},i(r){t||(z(e.$$.fragment,r),t=!0)},o(r){D(e.$$.fragment,r),t=!1},d(r){N(e,r)}}}function un(i){var r;let e,n,t,l;function s(o){i[23](o)}let u={filetype:"image/*",disable_click:!!((r=i[0])!=null&&r[0]),root:i[5],file_count:"multiple",upload:i[9],stream_handler:i[10],max_file_size:i[11],$$slots:{default:[fn]},$$scope:{ctx:i}};return i[1]!==void 0&&(u.dragging=i[1]),n=new ct({props:u}),j.push(()=>te(n,"dragging",s)),n.$on("load",i[24]),{c(){e=K("div"),L(n.$$.fragment),this.h()},l(o){e=Z(o,"DIV",{class:!0});var f=$(e);B(n.$$.fragment,f),f.forEach(C),this.h()},h(){G(e,"class","wrap svelte-143b07a"),V(e,"half-wrap",i[6]===1)},m(o,f){A(o,e,f),P(n,e,null),l=!0},p(o,f){var _;const a={};f&1&&(a.disable_click=!!((_=o[0])!=null&&_[0])),f&32&&(a.root=o[5]),f&512&&(a.upload=o[9]),f&1024&&(a.stream_handler=o[10]),f&2048&&(a.max_file_size=o[11]),f&1073741824&&(a.$$scope={dirty:f,ctx:o}),!t&&f&2&&(t=!0,a.dragging=o[1],ne(()=>t=!1)),n.$set(a),(!l||f&64)&&V(e,"half-wrap",o[6]===1)},i(o){l||(z(n.$$.fragment,o),l=!0)},o(o){D(n.$$.fragment,o),l=!1},d(o){o&&C(e),N(n)}}}function fn(i){let e;const n=i[21].default,t=Me(n,i,i[30],null);return{c(){t&&t.c()},l(l){t&&t.l(l)},m(l,s){t&&t.m(l,s),e=!0},p(l,s){t&&t.p&&(!e||s&1073741824)&&Ie(t,n,l,l[30],e?Re(n,l[30],s,null):Te(l[30]),null)},i(l){e||(z(t,l),e=!0)},o(l){D(t,l),e=!1},d(l){t&&t.d(l)}}}function hn(i){let e,n;return e=new De({props:{variant:"upload",src:i[14][1].url,alt:"",fixed:i[6]===1,transform:"translate(0px, 0px) scale(1)",max_height:i[13]}}),{c(){L(e.$$.fragment)},l(t){B(e.$$.fragment,t)},m(t,l){P(e,t,l),n=!0},p(t,l){const s={};l&16384&&(s.src=t[14][1].url),l&64&&(s.fixed=t[6]===1),l&8192&&(s.max_height=t[13]),e.$set(s)},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){N(e,t)}}}function _n(i){let e,n,t=`${i[16]*(1-i[2])}px`,l=`translateX(${i[16]*i[2]}px)`,s;return n=new He({props:{unpadded_box:!0,size:"large",$$slots:{default:[gn]},$$scope:{ctx:i}}}),{c(){e=K("div"),L(n.$$.fragment),this.h()},l(u){e=Z(u,"DIV",{class:!0});var r=$(e);B(n.$$.fragment,r),r.forEach(C),this.h()},h(){var u,r;G(e,"class","empty-wrap fixed svelte-143b07a"),V(e,"white-icon",!((r=(u=i[0])==null?void 0:u[0])!=null&&r.url)),Q(e,"width",t),Q(e,"transform",l)},m(u,r){A(u,e,r),P(n,e,null),s=!0},p(u,r){var f,a;const o={};r&1073741824&&(o.$$scope={dirty:r,ctx:u}),n.$set(o),(!s||r&1)&&V(e,"white-icon",!((a=(f=u[0])==null?void 0:f[0])!=null&&a.url)),r&65540&&t!==(t=`${u[16]*(1-u[2])}px`)&&Q(e,"width",t),r&65540&&l!==(l=`translateX(${u[16]*u[2]}px)`)&&Q(e,"transform",l)},i(u){s||(z(n.$$.fragment,u),s=!0)},o(u){D(n.$$.fragment,u),s=!1},d(u){u&&C(e),N(n)}}}function cn(i){var u;let e,n,t;function l(r){i[26](r)}let s={filetype:"image/*",disable_click:!!((u=i[0])!=null&&u[1]),root:i[5],file_count:"multiple",upload:i[9],stream_handler:i[10],max_file_size:i[11],$$slots:{default:[dn]},$$scope:{ctx:i}};return i[1]!==void 0&&(s.dragging=i[1]),e=new ct({props:s}),j.push(()=>te(e,"dragging",l)),e.$on("load",i[27]),{c(){L(e.$$.fragment)},l(r){B(e.$$.fragment,r)},m(r,o){P(e,r,o),t=!0},p(r,o){var a;const f={};o&1&&(f.disable_click=!!((a=r[0])!=null&&a[1])),o&32&&(f.root=r[5]),o&512&&(f.upload=r[9]),o&1024&&(f.stream_handler=r[10]),o&2048&&(f.max_file_size=r[11]),o&1073741824&&(f.$$scope={dirty:o,ctx:r}),!n&&o&2&&(n=!0,f.dragging=r[1],ne(()=>n=!1)),e.$set(f)},i(r){t||(z(e.$$.fragment,r),t=!0)},o(r){D(e.$$.fragment,r),t=!1},d(r){N(e,r)}}}function gn(i){let e,n;return e=new Ye({}),{c(){L(e.$$.fragment)},l(t){B(e.$$.fragment,t)},m(t,l){P(e,t,l),n=!0},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){N(e,t)}}}function dn(i){let e;const n=i[21].default,t=Me(n,i,i[30],null);return{c(){t&&t.c()},l(l){t&&t.l(l)},m(l,s){t&&t.m(l,s),e=!0},p(l,s){t&&t.p&&(!e||s&1073741824)&&Ie(t,n,l,l[30],e?Re(n,l[30],s,null):Te(l[30]),null)},i(l){e||(z(t,l),e=!0)},o(l){D(t,l),e=!1},d(l){t&&t.d(l)}}}function mn(i){let e,n,t,l,s,u,r;const o=[un,rn],f=[];function a(h,d){var v;return(v=h[14])!=null&&v[0]?1:0}n=a(i),t=f[n]=o[n](i);const _=[cn,_n,hn],k=[];function g(h,d){var v,Y,b;return!((v=h[14])!=null&&v[1])&&h[6]===2?0:!((Y=h[14])!=null&&Y[1])&&h[6]===1?1:(b=h[14])!=null&&b[1]?2:-1}return~(s=g(i))&&(u=k[s]=_[s](i)),{c(){e=K("div"),t.c(),l=x(),u&&u.c(),this.h()},l(h){e=Z(h,"DIV",{class:!0});var d=$(e);t.l(d),l=ee(d),u&&u.l(d),d.forEach(C),this.h()},h(){G(e,"class","upload-wrap svelte-143b07a"),V(e,"side-by-side",i[6]===2),Q(e,"display",i[6]===2?"flex":"block")},m(h,d){A(h,e,d),f[n].m(e,null),y(e,l),~s&&k[s].m(e,null),r=!0},p(h,d){let v=n;n=a(h),n===v?f[n].p(h,d):(se(),D(f[v],1,1,()=>{f[v]=null}),oe(),t=f[n],t?t.p(h,d):(t=f[n]=o[n](h),t.c()),z(t,1),t.m(e,l));let Y=s;s=g(h),s===Y?~s&&k[s].p(h,d):(u&&(se(),D(k[Y],1,1,()=>{k[Y]=null}),oe()),~s?(u=k[s],u?u.p(h,d):(u=k[s]=_[s](h),u.c()),z(u,1),u.m(e,null)):u=null),(!r||d&64)&&V(e,"side-by-side",h[6]===2),d&64&&Q(e,"display",h[6]===2?"flex":"block")},i(h){r||(z(t),z(u),r=!0)},o(h){D(t),D(u),r=!1},d(h){h&&C(e),f[n].d(),~s&&k[s].d()}}}function bn(i){var h,d,v,Y,b,I,m;let e,n,t,l,s,u,r,o,f;e=new ft({props:{show_label:i[4],Icon:Ye,label:i[3]||i[12]("image.image")}});let a=(((d=(h=i[0])==null?void 0:h[0])==null?void 0:d.url)||((Y=(v=i[0])==null?void 0:v[1])==null?void 0:Y.url))&&xe(i),_=((I=(b=i[0])==null?void 0:b[1])==null?void 0:I.url)&&et(i);function k(w){i[28](w)}let g={disabled:i[6]==2||!((m=i[0])!=null&&m[0]),slider_color:i[8],$$slots:{default:[mn]},$$scope:{ctx:i}};return i[2]!==void 0&&(g.position=i[2]),u=new dt({props:g}),j.push(()=>te(u,"position",k)),{c(){L(e.$$.fragment),n=x(),t=K("div"),a&&a.c(),l=x(),_&&_.c(),s=x(),L(u.$$.fragment),this.h()},l(w){B(e.$$.fragment,w),n=ee(w),t=Z(w,"DIV",{"data-testid":!0,class:!0});var X=$(t);a&&a.l(X),l=ee(X),_&&_.l(X),s=ee(X),B(u.$$.fragment,X),X.forEach(C),this.h()},h(){G(t,"data-testid","image"),G(t,"class","image-container svelte-143b07a"),lt(()=>i[29].call(t))},m(w,X){P(e,w,X),A(w,n,X),A(w,t,X),a&&a.m(t,null),y(t,l),_&&_.m(t,null),y(t,s),P(u,t,null),o=st(t,i[29].bind(t)),f=!0},p(w,[X]){var c,H,U,F,W,O,J;const T={};X&16&&(T.show_label=w[4]),X&4104&&(T.label=w[3]||w[12]("image.image")),e.$set(T),(H=(c=w[0])==null?void 0:c[0])!=null&&H.url||(F=(U=w[0])==null?void 0:U[1])!=null&&F.url?a?(a.p(w,X),X&1&&z(a,1)):(a=xe(w),a.c(),z(a,1),a.m(t,l)):a&&(se(),D(a,1,1,()=>{a=null}),oe()),(O=(W=w[0])==null?void 0:W[1])!=null&&O.url?_?(_.p(w,X),X&1&&z(_,1)):(_=et(w),_.c(),z(_,1),_.m(t,s)):_&&(se(),D(_,1,1,()=>{_=null}),oe());const R={};X&65&&(R.disabled=w[6]==2||!((J=w[0])!=null&&J[0])),X&256&&(R.slider_color=w[8]),X&1073868391&&(R.$$scope={dirty:X,ctx:w}),!r&&X&4&&(r=!0,R.position=w[2],ne(()=>r=!1)),u.$set(R)},i(w){f||(z(e.$$.fragment,w),z(a),z(_),z(u.$$.fragment,w),f=!0)},o(w){D(e.$$.fragment,w),D(a),D(_),D(u.$$.fragment,w),f=!1},d(w){w&&(C(n),C(t)),N(e,w),a&&a.d(),_&&_.d(),N(u),o()}}}function wn(i,e,n){let{$$slots:t={},$$scope:l}=e,{value:s}=e,{label:u=void 0}=e,{show_label:r}=e,{root:o}=e,{position:f}=e,{upload_count:a=2}=e,{show_download_button:_=!0}=e,{slider_color:k}=e,{upload:g}=e,{stream_handler:h}=e,{max_file_size:d=null}=e,{i18n:v}=e,{max_height:Y}=e,b=s||[null,null],I,m,w;async function X({detail:M},le){const ie=[s[0],s[1]];M.length>1?ie[le]=M[0]:ie[le]=M[le],n(0,s=ie),await it(),R("upload",ie)}let T="";const R=Se();let{dragging:c=!1}=e;const H=()=>{n(2,f=.5),n(0,s=[null,null]),R("clear")};function U(M){c=M,n(1,c)}const F=M=>X(M,0);function W(M){I=M,n(15,I)}function O(M){c=M,n(1,c)}const J=M=>X(M,1);function ae(M){f=M,n(2,f)}function fe(){m=this.clientWidth,w=this.clientHeight,n(16,m),n(17,w)}return i.$$set=M=>{"value"in M&&n(0,s=M.value),"label"in M&&n(3,u=M.label),"show_label"in M&&n(4,r=M.show_label),"root"in M&&n(5,o=M.root),"position"in M&&n(2,f=M.position),"upload_count"in M&&n(6,a=M.upload_count),"show_download_button"in M&&n(7,_=M.show_download_button),"slider_color"in M&&n(8,k=M.slider_color),"upload"in M&&n(9,g=M.upload),"stream_handler"in M&&n(10,h=M.stream_handler),"max_file_size"in M&&n(11,d=M.max_file_size),"i18n"in M&&n(12,v=M.i18n),"max_height"in M&&n(13,Y=M.max_height),"dragging"in M&&n(1,c=M.dragging),"$$scope"in M&&n(30,l=M.$$scope)},i.$$.update=()=>{i.$$.dirty&1048577&&JSON.stringify(s)!==T&&(n(20,T=JSON.stringify(s)),n(14,b=s)),i.$$.dirty&2&&R("drag",c)},[s,c,f,u,r,o,a,_,k,g,h,d,v,Y,b,I,m,w,X,R,T,t,H,U,F,W,O,J,ae,fe,l]}class pn extends ge{constructor(e){super(),de(this,e,wn,bn,me,{value:0,label:3,show_label:4,root:5,position:2,upload_count:6,show_download_button:7,slider_color:8,upload:9,stream_handler:10,max_file_size:11,i18n:12,max_height:13,dragging:1})}}function vn(i){let e;const n=i[11].default,t=Me(n,i,i[21],null);return{c(){t&&t.c()},l(l){t&&t.l(l)},m(l,s){t&&t.m(l,s),e=!0},p(l,s){t&&t.p&&(!e||s&2097152)&&Ie(t,n,l,l[21],e?Re(n,l[21],s,null):Te(l[21]),null)},i(l){e||(z(t,l),e=!0)},o(l){D(t,l),e=!1},d(l){t&&t.d(l)}}}function kn(i){let e,n,t,l;function s(o){i[12](o)}function u(o){i[13](o)}let r={slider_color:"var(--border-color-primary)",position:.5,root:i[7],label:i[4],show_label:i[5],upload_count:i[8],stream_handler:i[3],upload:i[2],max_file_size:i[10],max_height:i[9],i18n:i[6],$$slots:{default:[vn]},$$scope:{ctx:i}};return i[0]!==void 0&&(r.value=i[0]),i[1]!==void 0&&(r.dragging=i[1]),e=new pn({props:r}),j.push(()=>te(e,"value",s)),j.push(()=>te(e,"dragging",u)),e.$on("edit",i[14]),e.$on("clear",i[15]),e.$on("stream",i[16]),e.$on("drag",i[17]),e.$on("upload",i[18]),e.$on("select",i[19]),e.$on("share",i[20]),{c(){L(e.$$.fragment)},l(o){B(e.$$.fragment,o)},m(o,f){P(e,o,f),l=!0},p(o,[f]){const a={};f&128&&(a.root=o[7]),f&16&&(a.label=o[4]),f&32&&(a.show_label=o[5]),f&256&&(a.upload_count=o[8]),f&8&&(a.stream_handler=o[3]),f&4&&(a.upload=o[2]),f&1024&&(a.max_file_size=o[10]),f&512&&(a.max_height=o[9]),f&64&&(a.i18n=o[6]),f&2097152&&(a.$$scope={dirty:f,ctx:o}),!n&&f&1&&(n=!0,a.value=o[0],ne(()=>n=!1)),!t&&f&2&&(t=!0,a.dragging=o[1],ne(()=>t=!1)),e.$set(a)},i(o){l||(z(e.$$.fragment,o),l=!0)},o(o){D(e.$$.fragment,o),l=!1},d(o){N(e,o)}}}function zn(i,e,n){let{$$slots:t={},$$scope:l}=e,{value:s=[null,null]}=e,{upload:u}=e,{stream_handler:r}=e,{label:o}=e,{show_label:f}=e,{i18n:a}=e,{root:_}=e,{upload_count:k=1}=e,{dragging:g}=e,{max_height:h}=e,{max_file_size:d=null}=e;function v(c){s=c,n(0,s)}function Y(c){g=c,n(1,g)}function b(c){ce.call(this,i,c)}function I(c){ce.call(this,i,c)}function m(c){ce.call(this,i,c)}const w=({detail:c})=>n(1,g=c);function X(c){ce.call(this,i,c)}function T(c){ce.call(this,i,c)}function R(c){ce.call(this,i,c)}return i.$$set=c=>{"value"in c&&n(0,s=c.value),"upload"in c&&n(2,u=c.upload),"stream_handler"in c&&n(3,r=c.stream_handler),"label"in c&&n(4,o=c.label),"show_label"in c&&n(5,f=c.show_label),"i18n"in c&&n(6,a=c.i18n),"root"in c&&n(7,_=c.root),"upload_count"in c&&n(8,k=c.upload_count),"dragging"in c&&n(1,g=c.dragging),"max_height"in c&&n(9,h=c.max_height),"max_file_size"in c&&n(10,d=c.max_file_size),"$$scope"in c&&n(21,l=c.$$scope)},[s,g,u,r,o,f,a,_,k,h,d,t,v,Y,b,I,m,w,X,T,R,l]}class Xn extends ge{constructor(e){super(),de(this,e,zn,kn,me,{value:0,upload:2,stream_handler:3,label:4,show_label:5,i18n:6,root:7,upload_count:8,dragging:1,max_height:9,max_file_size:10})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),S()}get upload(){return this.$$.ctx[2]}set upload(e){this.$$set({upload:e}),S()}get stream_handler(){return this.$$.ctx[3]}set stream_handler(e){this.$$set({stream_handler:e}),S()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),S()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),S()}get i18n(){return this.$$.ctx[6]}set i18n(e){this.$$set({i18n:e}),S()}get root(){return this.$$.ctx[7]}set root(e){this.$$set({root:e}),S()}get upload_count(){return this.$$.ctx[8]}set upload_count(e){this.$$set({upload_count:e}),S()}get dragging(){return this.$$.ctx[1]}set dragging(e){this.$$set({dragging:e}),S()}get max_height(){return this.$$.ctx[9]}set max_height(e){this.$$set({max_height:e}),S()}get max_file_size(){return this.$$.ctx[10]}set max_file_size(e){this.$$set({max_file_size:e}),S()}}function Yn(i){let e,n;return e=new rt({props:{visible:i[4],variant:i[0]===null?"dashed":"solid",border_mode:i[22]?"focus":"base",padding:!1,elem_id:i[2],elem_classes:i[3],height:i[9]||void 0,width:i[10],allow_overflow:!1,container:i[11],scale:i[12],min_width:i[13],$$slots:{default:[Sn]},$$scope:{ctx:i}}}),e.$on("dragenter",i[25]),e.$on("dragleave",i[25]),e.$on("dragover",i[25]),e.$on("drop",i[26]),{c(){L(e.$$.fragment)},l(t){B(e.$$.fragment,t)},m(t,l){P(e,t,l),n=!0},p(t,l){const s={};l[0]&16&&(s.visible=t[4]),l[0]&1&&(s.variant=t[0]===null?"dashed":"solid"),l[0]&4194304&&(s.border_mode=t[22]?"focus":"base"),l[0]&4&&(s.elem_id=t[2]),l[0]&8&&(s.elem_classes=t[3]),l[0]&512&&(s.height=t[9]||void 0),l[0]&1024&&(s.width=t[10]),l[0]&2048&&(s.container=t[11]),l[0]&4096&&(s.scale=t[12]),l[0]&8192&&(s.min_width=t[13]),l[0]&14319971|l[1]&262144&&(s.$$scope={dirty:l,ctx:t}),e.$set(s)},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){N(e,t)}}}function En(i){let e,n,t;function l(u){i[37](u)}let s={visible:i[4],variant:"solid",border_mode:i[22]?"focus":"base",padding:!1,elem_id:i[2],elem_classes:i[3],height:i[9]||void 0,width:i[10],allow_overflow:!1,container:i[11],scale:i[12],min_width:i[13],$$slots:{default:[Cn]},$$scope:{ctx:i}};return i[21]!==void 0&&(s.fullscreen=i[21]),e=new rt({props:s}),j.push(()=>te(e,"fullscreen",l)),{c(){L(e.$$.fragment)},l(u){B(e.$$.fragment,u)},m(u,r){P(e,u,r),t=!0},p(u,r){const o={};r[0]&16&&(o.visible=u[4]),r[0]&4194304&&(o.border_mode=u[22]?"focus":"base"),r[0]&4&&(o.elem_id=u[2]),r[0]&8&&(o.elem_classes=u[3]),r[0]&512&&(o.height=u[9]||void 0),r[0]&1024&&(o.width=u[10]),r[0]&2048&&(o.container=u[11]),r[0]&4096&&(o.scale=u[12]),r[0]&8192&&(o.min_width=u[13]),r[0]&20791523|r[1]&262144&&(o.$$scope={dirty:r,ctx:u}),!n&&r[0]&2097152&&(n=!0,o.fullscreen=u[21],ne(()=>n=!1)),e.$set(o)},i(u){t||(z(e.$$.fragment,u),t=!0)},o(u){D(e.$$.fragment,u),t=!1},d(u){N(e,u)}}}function Dn(i){let e,n;return e=new He({props:{unpadded_box:!0,size:"large",$$slots:{default:[Tn]},$$scope:{ctx:i}}}),{c(){L(e.$$.fragment)},l(t){B(e.$$.fragment,t)},m(t,l){P(e,t,l),n=!0},p(t,l){const s={};l[1]&262144&&(s.$$scope={dirty:l,ctx:t}),e.$set(s)},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){N(e,t)}}}function Mn(i){let e,n;return e=new gt({props:{i18n:i[20].i18n,type:"clipboard",mode:"short"}}),{c(){L(e.$$.fragment)},l(t){B(e.$$.fragment,t)},m(t,l){P(e,t,l),n=!0},p(t,l){const s={};l[0]&1048576&&(s.i18n=t[20].i18n),e.$set(s)},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){N(e,t)}}}function In(i){let e,n;return e=new gt({props:{i18n:i[20].i18n,type:"image",placeholder:i[15]}}),{c(){L(e.$$.fragment)},l(t){B(e.$$.fragment,t)},m(t,l){P(e,t,l),n=!0},p(t,l){const s={};l[0]&1048576&&(s.i18n=t[20].i18n),l[0]&32768&&(s.placeholder=t[15]),e.$set(s)},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){N(e,t)}}}function Tn(i){let e,n;return e=new Ye({}),{c(){L(e.$$.fragment)},l(t){B(e.$$.fragment,t)},m(t,l){P(e,t,l),n=!0},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){N(e,t)}}}function Rn(i){let e,n,t,l;const s=[In,Mn,Dn],u=[];function r(o,f){return 0}return e=r(),n=u[e]=s[e](i),{c(){n.c(),t=_e()},l(o){n.l(o),t=_e()},m(o,f){u[e].m(o,f),A(o,t,f),l=!0},p(o,f){n.p(o,f)},i(o){l||(z(n),l=!0)},o(o){D(n),l=!1},d(o){o&&C(t),u[e].d(o)}}}function Sn(i){var k;let e,n,t,l,s,u;const r=[{autoscroll:i[20].autoscroll},{i18n:i[20].i18n},i[1]];let o={};for(let g=0;g<r.length;g+=1)o=ve(o,r[g]);e=new ut({props:o}),e.$on("clear_status",i[38]);function f(g){i[41](g)}function a(g){i[42](g)}let _={root:i[8],label:i[5],show_label:i[6],upload_count:i[17],max_file_size:i[20].max_file_size,i18n:i[20].i18n,upload:i[39],stream_handler:(k=i[20].client)==null?void 0:k.stream,max_height:i[19],$$slots:{default:[Rn]},$$scope:{ctx:i}};return i[0]!==void 0&&(_.value=i[0]),i[22]!==void 0&&(_.dragging=i[22]),t=new Xn({props:_}),i[40](t),j.push(()=>te(t,"value",f)),j.push(()=>te(t,"dragging",a)),t.$on("edit",i[43]),t.$on("clear",i[44]),t.$on("drag",i[45]),t.$on("upload",i[46]),t.$on("error",i[47]),t.$on("close_stream",i[48]),{c(){L(e.$$.fragment),n=x(),L(t.$$.fragment)},l(g){B(e.$$.fragment,g),n=ee(g),B(t.$$.fragment,g)},m(g,h){P(e,g,h),A(g,n,h),P(t,g,h),u=!0},p(g,h){var Y;const d=h[0]&1048578?Fe(r,[h[0]&1048576&&{autoscroll:g[20].autoscroll},h[0]&1048576&&{i18n:g[20].i18n},h[0]&2&&ot(g[1])]):{};e.$set(d);const v={};h[0]&256&&(v.root=g[8]),h[0]&32&&(v.label=g[5]),h[0]&64&&(v.show_label=g[6]),h[0]&131072&&(v.upload_count=g[17]),h[0]&1048576&&(v.max_file_size=g[20].max_file_size),h[0]&1048576&&(v.i18n=g[20].i18n),h[0]&1048576&&(v.upload=g[39]),h[0]&1048576&&(v.stream_handler=(Y=g[20].client)==null?void 0:Y.stream),h[0]&524288&&(v.max_height=g[19]),h[0]&1081344|h[1]&262144&&(v.$$scope={dirty:h,ctx:g}),!l&&h[0]&1&&(l=!0,v.value=g[0],ne(()=>l=!1)),!s&&h[0]&4194304&&(s=!0,v.dragging=g[22],ne(()=>s=!1)),t.$set(v)},i(g){u||(z(e.$$.fragment,g),z(t.$$.fragment,g),u=!0)},o(g){D(e.$$.fragment,g),D(t.$$.fragment,g),u=!1},d(g){g&&C(n),N(e,g),i[40](null),N(t,g)}}}function Cn(i){let e,n,t,l,s;const u=[{autoscroll:i[20].autoscroll},{i18n:i[20].i18n},i[1]];let r={};for(let a=0;a<u.length;a+=1)r=ve(r,u[a]);e=new ut({props:r});function o(a){i[31](a)}let f={fullscreen:i[21],interactive:i[14],label:i[5],show_label:i[6],show_download_button:i[7],i18n:i[20].i18n,show_fullscreen_button:i[16],position:i[24],slider_color:i[18],max_height:i[19]};return i[0]!==void 0&&(f.value=i[0]),t=new nn({props:f}),j.push(()=>te(t,"value",o)),t.$on("select",i[32]),t.$on("share",i[33]),t.$on("error",i[34]),t.$on("clear",i[35]),t.$on("fullscreen",i[36]),{c(){L(e.$$.fragment),n=x(),L(t.$$.fragment)},l(a){B(e.$$.fragment,a),n=ee(a),B(t.$$.fragment,a)},m(a,_){P(e,a,_),A(a,n,_),P(t,a,_),s=!0},p(a,_){const k=_[0]&1048578?Fe(u,[_[0]&1048576&&{autoscroll:a[20].autoscroll},_[0]&1048576&&{i18n:a[20].i18n},_[0]&2&&ot(a[1])]):{};e.$set(k);const g={};_[0]&2097152&&(g.fullscreen=a[21]),_[0]&16384&&(g.interactive=a[14]),_[0]&32&&(g.label=a[5]),_[0]&64&&(g.show_label=a[6]),_[0]&128&&(g.show_download_button=a[7]),_[0]&1048576&&(g.i18n=a[20].i18n),_[0]&65536&&(g.show_fullscreen_button=a[16]),_[0]&16777216&&(g.position=a[24]),_[0]&262144&&(g.slider_color=a[18]),_[0]&524288&&(g.max_height=a[19]),!l&&_[0]&1&&(l=!0,g.value=a[0],ne(()=>l=!1)),t.$set(g)},i(a){s||(z(e.$$.fragment,a),z(t.$$.fragment,a),s=!0)},o(a){D(e.$$.fragment,a),D(t.$$.fragment,a),s=!1},d(a){a&&C(n),N(e,a),N(t,a)}}}function Ln(i){let e,n,t,l;const s=[En,Yn],u=[];function r(o,f){var a,_;return!o[14]||(a=o[0])!=null&&a[1]&&((_=o[0])!=null&&_[0])?0:1}return e=r(i),n=u[e]=s[e](i),{c(){n.c(),t=_e()},l(o){n.l(o),t=_e()},m(o,f){u[e].m(o,f),A(o,t,f),l=!0},p(o,f){let a=e;e=r(o),e===a?u[e].p(o,f):(se(),D(u[a],1,1,()=>{u[a]=null}),oe(),n=u[e],n?n.p(o,f):(n=u[e]=s[e](o),n.c()),z(n,1),n.m(t.parentNode,t))},i(o){l||(z(n),l=!0)},o(o){D(n),l=!1},d(o){o&&C(t),u[e].d(o)}}}let Bn=!1;function Pn(i,e,n){let t,{value_is_output:l=!1}=e,{elem_id:s=""}=e,{elem_classes:u=[]}=e,{visible:r=!0}=e,{value:o=[null,null]}=e,f=[null,null],{label:a}=e,{show_label:_}=e,{show_download_button:k}=e,{root:g}=e,{height:h}=e,{width:d}=e,{container:v=!0}=e,{scale:Y=null}=e,{min_width:b=void 0}=e,{loading_status:I}=e,{interactive:m}=e,{placeholder:w=void 0}=e,{show_fullscreen_button:X}=e,T=!1,{input_ready:R}=e,{slider_position:c}=e,{upload_count:H=1}=e,{slider_color:U="var(--border-color-primary)"}=e,{max_height:F}=e,{gradio:W}=e;Et(()=>{n(27,l=!1)});let O,J;const ae=p=>{const ue=p;ue.preventDefault(),ue.stopPropagation(),ue.type==="dragenter"||ue.type==="dragover"?n(22,O=!0):ue.type==="dragleave"&&n(22,O=!1)},fe=p=>{if(m){const ue=p;ue.preventDefault(),ue.stopPropagation(),n(22,O=!1),J&&J.loadFilesFromDrop(ue)}};function M(p){o=p,n(0,o)}const le=({detail:p})=>W.dispatch("select",p),ie=({detail:p})=>W.dispatch("share",p),Ce=({detail:p})=>W.dispatch("error",p),Le=()=>W.dispatch("clear"),Be=({detail:p})=>{n(21,T=p)};function Pe(p){T=p,n(21,T)}const Ne=()=>W.dispatch("clear_status",I),Ue=(...p)=>W.client.upload(...p);function E(p){j[p?"unshift":"push"](()=>{J=p,n(23,J)})}function re(p){o=p,n(0,o)}function pe(p){O=p,n(22,O)}const he=()=>W.dispatch("edit"),Ve=()=>{W.dispatch("clear")},mt=({detail:p})=>n(22,O=p),bt=()=>W.dispatch("upload"),wt=({detail:p})=>{n(1,I=I||{}),n(1,I.status="error",I),W.dispatch("error",p)},pt=()=>{W.dispatch("close_stream","stream")};return i.$$set=p=>{"value_is_output"in p&&n(27,l=p.value_is_output),"elem_id"in p&&n(2,s=p.elem_id),"elem_classes"in p&&n(3,u=p.elem_classes),"visible"in p&&n(4,r=p.visible),"value"in p&&n(0,o=p.value),"label"in p&&n(5,a=p.label),"show_label"in p&&n(6,_=p.show_label),"show_download_button"in p&&n(7,k=p.show_download_button),"root"in p&&n(8,g=p.root),"height"in p&&n(9,h=p.height),"width"in p&&n(10,d=p.width),"container"in p&&n(11,v=p.container),"scale"in p&&n(12,Y=p.scale),"min_width"in p&&n(13,b=p.min_width),"loading_status"in p&&n(1,I=p.loading_status),"interactive"in p&&n(14,m=p.interactive),"placeholder"in p&&n(15,w=p.placeholder),"show_fullscreen_button"in p&&n(16,X=p.show_fullscreen_button),"input_ready"in p&&n(28,R=p.input_ready),"slider_position"in p&&n(29,c=p.slider_position),"upload_count"in p&&n(17,H=p.upload_count),"slider_color"in p&&n(18,U=p.slider_color),"max_height"in p&&n(19,F=p.max_height),"gradio"in p&&n(20,W=p.gradio)},i.$$.update=()=>{i.$$.dirty[0]&536870912&&n(24,t=Math.max(0,Math.min(100,c))/100),i.$$.dirty[0]&1209008129&&JSON.stringify(o)!==JSON.stringify(f)&&(n(30,f=o),W.dispatch("change"),l||W.dispatch("input"))},n(28,R=!Bn),[o,I,s,u,r,a,_,k,g,h,d,v,Y,b,m,w,X,H,U,F,W,T,O,J,t,ae,fe,l,R,c,f,M,le,ie,Ce,Le,Be,Pe,Ne,Ue,E,re,pe,he,Ve,mt,bt,wt,pt]}class $n extends ge{constructor(e){super(),de(this,e,Pn,Ln,me,{value_is_output:27,elem_id:2,elem_classes:3,visible:4,value:0,label:5,show_label:6,show_download_button:7,root:8,height:9,width:10,container:11,scale:12,min_width:13,loading_status:1,interactive:14,placeholder:15,show_fullscreen_button:16,input_ready:28,slider_position:29,upload_count:17,slider_color:18,max_height:19,gradio:20},null,[-1,-1])}get value_is_output(){return this.$$.ctx[27]}set value_is_output(e){this.$$set({value_is_output:e}),S()}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),S()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),S()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),S()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),S()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),S()}get show_label(){return this.$$.ctx[6]}set show_label(e){this.$$set({show_label:e}),S()}get show_download_button(){return this.$$.ctx[7]}set show_download_button(e){this.$$set({show_download_button:e}),S()}get root(){return this.$$.ctx[8]}set root(e){this.$$set({root:e}),S()}get height(){return this.$$.ctx[9]}set height(e){this.$$set({height:e}),S()}get width(){return this.$$.ctx[10]}set width(e){this.$$set({width:e}),S()}get container(){return this.$$.ctx[11]}set container(e){this.$$set({container:e}),S()}get scale(){return this.$$.ctx[12]}set scale(e){this.$$set({scale:e}),S()}get min_width(){return this.$$.ctx[13]}set min_width(e){this.$$set({min_width:e}),S()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),S()}get interactive(){return this.$$.ctx[14]}set interactive(e){this.$$set({interactive:e}),S()}get placeholder(){return this.$$.ctx[15]}set placeholder(e){this.$$set({placeholder:e}),S()}get show_fullscreen_button(){return this.$$.ctx[16]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),S()}get input_ready(){return this.$$.ctx[28]}set input_ready(e){this.$$set({input_ready:e}),S()}get slider_position(){return this.$$.ctx[29]}set slider_position(e){this.$$set({slider_position:e}),S()}get upload_count(){return this.$$.ctx[17]}set upload_count(e){this.$$set({upload_count:e}),S()}get slider_color(){return this.$$.ctx[18]}set slider_color(e){this.$$set({slider_color:e}),S()}get max_height(){return this.$$.ctx[19]}set max_height(e){this.$$set({max_height:e}),S()}get gradio(){return this.$$.ctx[20]}set gradio(e){this.$$set({gradio:e}),S()}}export{$n as default};
//# sourceMappingURL=Index.Cm0Yofoi.js.map
