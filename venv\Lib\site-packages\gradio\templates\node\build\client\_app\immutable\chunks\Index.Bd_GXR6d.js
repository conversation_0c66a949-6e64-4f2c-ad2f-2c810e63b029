const __vite__fileDeps=["./browserAll.C_UrN5Lo.js","./init.Dwzg8m1W.js","./colorToUniform.KTpA7KSL.js","./webworkerAll.BJf8EwlM.js","./WebGPURenderer.BOjjzCCO.js","./SharedSystems.QZGTx25_.js","./WebGLRenderer.Cz64BPQU.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
var Eh=Object.defineProperty;var zh=(s,e,t)=>e in s?Eh(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var v=(s,e,t)=>(zh(s,typeof e!="symbol"?e+"":e,t),t);import{SvelteComponent as ke,init as Ae,safe_not_equal as Se,svg_element as ue,claim_svg_element as de,children as L,detach as k,attr as x,insert_hydration as D,append_hydration as O,noop as $,set_style as pe,element as Y,space as Z,create_component as ee,claim_element as j,claim_space as Q,claim_component as te,toggle_class as oe,mount_component as ie,listen as Fe,transition_in as C,transition_out as I,destroy_component as se,text as St,claim_text as Ct,set_data as ii,bubble as yt,run_all as mi,onMount as ga,tick as ls,binding_callbacks as ye,ensure_array_like as Ft,group_outros as he,check_outros as ce,destroy_each as _s,createEventDispatcher as Nt,set_input_value as nn,action_destroyer as gs,to_number as Rh,add_render_callback as Bh,empty as Oe,bind as Ce,add_flush_callback as Me,stop_propagation as ms,is_function as ma,update_keyed_each as Fh,outro_and_destroy_block as Lh,subscribe as Oh,get_svelte_dataset as ps,create_slot as Gh,update_slot_base as Dh,get_all_dirty_from_scope as Nh,get_slot_changes as Uh,not_equal as Wh,flush as me,assign as pa,get_spread_update as ba,get_spread_object as ya}from"../../../svelte/svelte.js";import{writable as ci,get as ei,spring as Ms}from"../../../svelte/svelte-submodules.js";import{f as Ur,I as ot,C as Hh,H as wa,A as Ts,Y as Ps,B as xa,S as va}from"./2.B2AoQPnG.js";import Vh from"./ImagePreview.B6xPgKdu.js";import{C as ka,W as Xh}from"./ImageUploader.Dvb2Mtrn.js";/* empty css                                              */import{I as Aa}from"./Image.CTVzPhL7.js";import{U as Yh,I as jh,c as qh}from"./Upload.yOHVlgUe.js";import{W as Kh}from"./SelectSource.CTC8Kkgx.js";import{t as ft,E as Zh}from"./tinycolor.CowIdatr.js";import{_ as hs}from"./preload-helper.D6kgxu3v.js";/* empty css                                             */import{D as Qh}from"./Download.CpfEFmFf.js";import{T as Jh}from"./Trash.D4IfxcH_.js";import{U as $h}from"./Undo.LhwFM5M8.js";import{I as ec}from"./IconButtonWrapper.D5aGR59h.js";import{B as tc}from"./BlockLabel.BTSz9r5s.js";function ic(s){let e,t,i;return{c(){e=ue("svg"),t=ue("line"),i=ue("polyline"),this.h()},l(r){e=de(r,"svg",{width:!0,height:!0,xmlns:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0});var n=L(e);t=de(n,"line",{x1:!0,y1:!0,x2:!0,y2:!0}),L(t).forEach(k),i=de(n,"polyline",{points:!0}),L(i).forEach(k),n.forEach(k),this.h()},h(){x(t,"x1","12"),x(t,"y1","19"),x(t,"x2","12"),x(t,"y2","5"),x(i,"points","5 12 12 5 19 12"),x(e,"width","100%"),x(e,"height","100%"),x(e,"xmlns","http://www.w3.org/2000/svg"),x(e,"viewBox","0 0 24 24"),x(e,"fill","none"),x(e,"stroke","currentColor"),x(e,"stroke-width","2"),x(e,"stroke-linecap","round"),x(e,"stroke-linejoin","round")},m(r,n){D(r,e,n),O(e,t),O(e,i)},p:$,i:$,o:$,d(r){r&&k(e)}}}class sc extends ke{constructor(e){super(),Ae(this,e,null,ic,Se,{})}}function rc(s){let e,t,i;return{c(){e=ue("svg"),t=ue("line"),i=ue("polyline"),this.h()},l(r){e=de(r,"svg",{width:!0,height:!0,xmlns:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0});var n=L(e);t=de(n,"line",{x1:!0,y1:!0,x2:!0,y2:!0}),L(t).forEach(k),i=de(n,"polyline",{points:!0}),L(i).forEach(k),n.forEach(k),this.h()},h(){x(t,"x1","12"),x(t,"y1","5"),x(t,"x2","12"),x(t,"y2","19"),x(i,"points","19 12 12 19 5 12"),x(e,"width","100%"),x(e,"height","100%"),x(e,"xmlns","http://www.w3.org/2000/svg"),x(e,"viewBox","0 0 24 24"),x(e,"fill","none"),x(e,"stroke","currentColor"),x(e,"stroke-width","2"),x(e,"stroke-linecap","round"),x(e,"stroke-linejoin","round")},m(r,n){D(r,e,n),O(e,t),O(e,i)},p:$,i:$,o:$,d(r){r&&k(e)}}}class nc extends ke{constructor(e){super(),Ae(this,e,null,rc,Se,{})}}function oc(s){let e,t,i;return{c(){e=ue("svg"),t=ue("path"),i=ue("path"),this.h()},l(r){e=de(r,"svg",{width:!0,height:!0,viewBox:!0});var n=L(e);t=de(n,"path",{d:!0,fill:!0}),L(t).forEach(k),i=de(n,"path",{d:!0,fill:!0}),L(i).forEach(k),n.forEach(k),this.h()},h(){x(t,"d","M28.828 3.172a4.094 4.094 0 0 0-5.656 0L4.05 22.292A6.954 6.954 0 0 0 2 27.242V30h2.756a6.952 6.952 0 0 0 4.95-2.05L28.828 8.829a3.999 3.999 0 0 0 0-5.657zM10.91 18.26l2.829 2.829l-2.122 2.121l-2.828-2.828zm-2.619 8.276A4.966 4.966 0 0 1 4.756 28H4v-.759a4.967 4.967 0 0 1 1.464-3.535l1.91-1.91l2.829 2.828zM27.415 7.414l-12.261 12.26l-2.829-2.828l12.262-12.26a2.047 2.047 0 0 1 2.828 0a2 2 0 0 1 0 2.828z"),x(t,"fill","currentColor"),x(i,"d","M6.5 15a3.5 3.5 0 0 1-2.475-5.974l3.5-3.5a1.502 1.502 0 0 0 0-2.121a1.537 1.537 0 0 0-2.121 0L3.415 5.394L2 3.98l1.99-1.988a3.585 3.585 0 0 1 4.95 0a3.504 3.504 0 0 1 0 4.949L5.439 10.44a1.502 1.502 0 0 0 0 2.121a1.537 1.537 0 0 0 2.122 0l4.024-4.024L13 9.95l-4.025 4.024A3.475 3.475 0 0 1 6.5 15z"),x(i,"fill","currentColor"),x(e,"width","100%"),x(e,"height","100%"),x(e,"viewBox","0 0 32 32")},m(r,n){D(r,e,n),O(e,t),O(e,i)},p:$,i:$,o:$,d(r){r&&k(e)}}}class ac extends ke{constructor(e){super(),Ae(this,e,null,oc,Se,{})}}function lc(s){let e,t;return{c(){e=ue("svg"),t=ue("path"),this.h()},l(i){e=de(i,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0});var r=L(e);t=de(r,"path",{fill:!0,d:!0}),L(t).forEach(k),r.forEach(k),this.h()},h(){x(t,"fill","currentColor"),x(t,"d","M2.753 2.933a.75.75 0 0 1 .814-.68l3.043.272c2.157.205 4.224.452 5.922.732c1.66.273 3.073.594 3.844.983c.197.1.412.233.578.415c.176.192.352.506.28.9c-.067.356-.304.59-.487.729a3.001 3.001 0 0 1-.695.369c-1.02.404-2.952.79-5.984 1.169c-1.442.18-2.489.357-3.214.522c.205.045.43.089.674.132c.992.174 2.241.323 3.568.437a31.21 31.21 0 0 1 3.016.398c.46.087.893.186 1.261.296c.352.105.707.236.971.412c.13.086.304.225.42.437a.988.988 0 0 1 .063.141A1.75 1.75 0 0 0 14.5 12.25v.158c-.758.154-1.743.302-2.986.444c-2.124.243-3.409.55-4.117.859c-.296.128-.442.236-.508.3c.026.037.073.094.156.17c.15.138.369.29.65.45c.56.316 1.282.61 1.979.838l2.637.814a.75.75 0 1 1-.443 1.433l-2.655-.819c-.754-.247-1.58-.578-2.257-.96a5.082 5.082 0 0 1-.924-.65c-.255-.233-.513-.544-.62-.935c-.12-.441-.016-.88.274-1.244c.261-.328.656-.574 1.113-.773c.92-.4 2.387-.727 4.545-.974c1.366-.156 2.354-.313 3.041-.462a16.007 16.007 0 0 0-.552-.114a29.716 29.716 0 0 0-2.865-.378c-1.352-.116-2.649-.27-3.7-.454c-.524-.092-1-.194-1.395-.307c-.376-.106-.75-.241-1.021-.426a1.186 1.186 0 0 1-.43-.49a.934.934 0 0 1 .059-.873c.13-.213.32-.352.472-.442a3.23 3.23 0 0 1 .559-.251c.807-.287 2.222-.562 4.37-.83c2.695-.338 4.377-.666 5.295-.962c-.638-.21-1.623-.427-2.89-.635c-1.65-.273-3.679-.515-5.816-.718l-3.038-.272a.75.75 0 0 1-.68-.814M17 12.25a.75.75 0 0 0-1.5 0v4.19l-.72-.72a.75.75 0 1 0-1.06 1.06l2 2a.75.75 0 0 0 1.06 0l2-2a.75.75 0 1 0-1.06-1.06l-.72.72z"),x(e,"xmlns","http://www.w3.org/2000/svg"),x(e,"width","100%"),x(e,"height","100%"),x(e,"viewBox","0 0 24 24")},m(i,r){D(i,e,r),O(e,t)},p:$,i:$,o:$,d(i){i&&k(e)}}}class hc extends ke{constructor(e){super(),Ae(this,e,null,lc,Se,{})}}function cc(s){let e,t;return{c(){e=ue("svg"),t=ue("path"),this.h()},l(i){e=de(i,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0});var r=L(e);t=de(r,"path",{fill:!0,d:!0}),L(t).forEach(k),r.forEach(k),this.h()},h(){x(t,"fill","currentColor"),x(t,"d","M12 22q-2.05 0-3.875-.788t-3.187-2.15t-2.15-3.187T2 12q0-2.075.813-3.9t2.2-3.175T8.25 2.788T12.2 2q2 0 3.775.688t3.113 1.9t2.125 2.875T22 11.05q0 2.875-1.75 4.413T16 17h-1.85q-.225 0-.312.125t-.088.275q0 .3.375.863t.375 1.287q0 1.25-.687 1.85T12 22m-5.5-9q.65 0 1.075-.425T8 11.5t-.425-1.075T6.5 10t-1.075.425T5 11.5t.425 1.075T6.5 13m3-4q.65 0 1.075-.425T11 7.5t-.425-1.075T9.5 6t-1.075.425T8 7.5t.425 1.075T9.5 9m5 0q.65 0 1.075-.425T16 7.5t-.425-1.075T14.5 6t-1.075.425T13 7.5t.425 1.075T14.5 9m3 4q.65 0 1.075-.425T19 11.5t-.425-1.075T17.5 10t-1.075.425T16 11.5t.425 1.075T17.5 13"),x(e,"xmlns","http://www.w3.org/2000/svg"),x(e,"width","100%"),x(e,"height","100%"),x(e,"viewBox","0 0 24 24")},m(i,r){D(i,e,r),O(e,t)},p:$,i:$,o:$,d(i){i&&k(e)}}}class uc extends ke{constructor(e){super(),Ae(this,e,null,cc,Se,{})}}function dc(s){let e,t;return{c(){e=ue("svg"),t=ue("path"),this.h()},l(i){e=de(i,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0});var r=L(e);t=de(r,"path",{fill:!0,d:!0}),L(t).forEach(k),r.forEach(k),this.h()},h(){x(t,"fill","currentColor"),x(t,"d","M240 192a8 8 0 0 1-8 8h-32v32a8 8 0 0 1-16 0v-32H64a8 8 0 0 1-8-8V72H24a8 8 0 0 1 0-16h32V24a8 8 0 0 1 16 0v160h160a8 8 0 0 1 8 8M96 72h88v88a8 8 0 0 0 16 0V64a8 8 0 0 0-8-8H96a8 8 0 0 0 0 16"),x(e,"xmlns","http://www.w3.org/2000/svg"),x(e,"width","100%"),x(e,"height","100%"),x(e,"viewBox","0 0 256 256")},m(i,r){D(i,e,r),O(e,t)},p:$,i:$,o:$,d(i){i&&k(e)}}}class fc extends ke{constructor(e){super(),Ae(this,e,null,dc,Se,{})}}function _c(s){let e,t,i,r;return{c(){e=ue("svg"),t=ue("g"),i=ue("path"),r=ue("path"),this.h()},l(n){e=de(n,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0});var o=L(e);t=de(o,"g",{fill:!0});var a=L(t);i=de(a,"path",{fill:!0,d:!0}),L(i).forEach(k),r=de(a,"path",{stroke:!0,"stroke-linecap":!0,"stroke-width":!0,d:!0}),L(r).forEach(k),a.forEach(k),o.forEach(k),this.h()},h(){x(i,"fill","currentColor"),x(i,"d","m5.505 11.41l.53.53l-.53-.53ZM3 14.952h-.75H3ZM9.048 21v.75V21ZM11.41 5.505l-.53-.53l.53.53Zm1.831 12.34a.75.75 0 0 0 1.06-1.061l-1.06 1.06ZM7.216 9.697a.75.75 0 1 0-1.06 1.061l1.06-1.06Zm10.749 2.362l-5.905 5.905l1.06 1.06l5.905-5.904l-1.06-1.06Zm-11.93-.12l5.905-5.905l-1.06-1.06l-5.905 5.904l1.06 1.06Zm0 6.025c-.85-.85-1.433-1.436-1.812-1.933c-.367-.481-.473-.79-.473-1.08h-1.5c0 .749.312 1.375.78 1.99c.455.596 1.125 1.263 1.945 2.083l1.06-1.06Zm-1.06-7.086c-.82.82-1.49 1.488-1.945 2.084c-.468.614-.78 1.24-.78 1.99h1.5c0-.29.106-.6.473-1.08c.38-.498.962-1.083 1.812-1.933l-1.06-1.06Zm7.085 7.086c-.85.85-1.435 1.433-1.933 1.813c-.48.366-.79.472-1.08.472v1.5c.75 0 1.376-.312 1.99-.78c.596-.455 1.264-1.125 2.084-1.945l-1.06-1.06Zm-7.085 1.06c.82.82 1.487 1.49 2.084 1.945c.614.468 1.24.78 1.989.78v-1.5c-.29 0-.599-.106-1.08-.473c-.497-.38-1.083-.962-1.933-1.812l-1.06 1.06Zm12.99-12.99c.85.85 1.433 1.436 1.813 1.933c.366.481.472.79.472 1.08h1.5c0-.749-.312-1.375-.78-1.99c-.455-.596-1.125-1.263-1.945-2.083l-1.06 1.06Zm1.06 7.086c.82-.82 1.49-1.488 1.945-2.084c.468-.614.78-1.24.78-1.99h-1.5c0 .29-.106.6-.473 1.08c-.38.498-.962 1.083-1.812 1.933l1.06 1.06Zm0-8.146c-.82-.82-1.487-1.49-2.084-1.945c-.614-.468-1.24-.78-1.989-.78v1.5c.29 0 .599.106 1.08.473c.497.38 1.083.962 1.933 1.812l1.06-1.06Zm-7.085 1.06c.85-.85 1.435-1.433 1.933-1.812c.48-.367.79-.473 1.08-.473v-1.5c-.75 0-1.376.312-1.99.78c-.596.455-1.264 1.125-2.084 1.945l1.06 1.06Zm2.362 10.749L7.216 9.698l-1.06 1.061l7.085 7.085l1.06-1.06Z"),x(r,"stroke","currentColor"),x(r,"stroke-linecap","round"),x(r,"stroke-width","1.5"),x(r,"d","M9 21h12"),x(t,"fill","none"),x(e,"xmlns","http://www.w3.org/2000/svg"),x(e,"width","100%"),x(e,"height","100%"),x(e,"viewBox","0 0 24 24")},m(n,o){D(n,e,o),O(e,t),O(t,i),O(t,r)},p:$,i:$,o:$,d(n){n&&k(e)}}}class gc extends ke{constructor(e){super(),Ae(this,e,null,_c,Se,{})}}function mc(s){let e,t,i;return{c(){e=ue("svg"),t=ue("path"),i=ue("path"),this.h()},l(r){e=de(r,"svg",{width:!0,height:!0,viewBox:!0,fill:!0,xmlns:!0});var n=L(e);t=de(n,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),L(t).forEach(k),i=de(n,"path",{d:!0,stroke:!0,"stroke-width":!0}),L(i).forEach(k),n.forEach(k),this.h()},h(){x(t,"d","M1.35327 10.9495L6.77663 15.158C7.12221 15.4229 7.50051 15.5553 7.91154 15.5553C8.32258 15.5553 8.70126 15.4229 9.0476 15.158L14.471 10.9495"),x(t,"stroke","currentColor"),x(t,"stroke-width","1.5"),x(t,"stroke-linecap","round"),x(i,"d","M7.23461 11.4324C7.23406 11.432 7.2335 11.4316 7.23295 11.4312L1.81496 7.2268C1.81471 7.22661 1.81446 7.22641 1.8142 7.22621C1.52269 6.99826 1.39429 6.73321 1.39429 6.37014C1.39429 6.00782 1.52236 5.74301 1.81325 5.51507C1.8136 5.5148 1.81394 5.51453 1.81428 5.51426L7.2331 1.30812C7.45645 1.13785 7.67632 1.06653 7.91159 1.06653C8.14692 1.06653 8.36622 1.13787 8.58861 1.30787C8.58915 1.30828 8.58969 1.30869 8.59023 1.30911L14.0082 5.51462C14.0085 5.51485 14.0088 5.51507 14.0091 5.51529C14.3008 5.74345 14.4289 6.00823 14.4289 6.37014C14.4289 6.73356 14.3006 6.99862 14.01 7.22634C14.0096 7.22662 14.0093 7.22689 14.0089 7.22717L8.59007 11.4322C8.36672 11.6024 8.14686 11.6738 7.91159 11.6738C7.67628 11.6738 7.45699 11.6024 7.23461 11.4324Z"),x(i,"stroke","currentColor"),x(i,"stroke-width","1.5"),x(e,"width","100%"),x(e,"height","100%"),x(e,"viewBox","0 0 17 17"),x(e,"fill","none"),x(e,"xmlns","http://www.w3.org/2000/svg")},m(r,n){D(r,e,n),O(e,t),O(e,i)},p:$,i:$,o:$,d(r){r&&k(e)}}}class pc extends ke{constructor(e){super(),Ae(this,e,null,mc,Se,{})}}function bc(s){let e,t;return{c(){e=ue("svg"),t=ue("path"),this.h()},l(i){e=de(i,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0});var r=L(e);t=de(r,"path",{fill:!0,d:!0}),L(t).forEach(k),r.forEach(k),this.h()},h(){x(t,"fill","currentColor"),x(t,"d","M10.05 23q-.75 0-1.4-.337T7.575 21.7l-5.9-8.65q-.2-.3-.175-.65t.3-.6q.475-.475 1.125-.55t1.175.3L7 13.575V4q0-.425.288-.712T8 3t.713.288T9 4v11.5q0 .6-.537.888t-1.038-.063l-2.125-1.5l3.925 5.725q.125.2.35.325t.475.125H17q.825 0 1.413-.587T19 19V5q0-.425.288-.712T20 4t.713.288T21 5v14q0 1.65-1.175 2.825T17 23zM12 1q.425 0 .713.288T13 2v9q0 .425-.288.713T12 12t-.712-.288T11 11V2q0-.425.288-.712T12 1m4 1q.425 0 .713.288T17 3v8q0 .425-.288.713T16 12t-.712-.288T15 11V3q0-.425.288-.712T16 2m-3.85 14.5"),x(e,"xmlns","http://www.w3.org/2000/svg"),x(e,"width","100%"),x(e,"height","100%"),x(e,"viewBox","0 0 24 24")},m(i,r){D(i,e,r),O(e,t)},p:$,i:$,o:$,d(i){i&&k(e)}}}class yc extends ke{constructor(e){super(),Ae(this,e,null,bc,Se,{})}}function wc(s){let e,t;return{c(){e=ue("svg"),t=ue("path"),this.h()},l(i){e=de(i,"svg",{width:!0,height:!0,viewBox:!0,fill:!0,xmlns:!0});var r=L(e);t=de(r,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0}),L(t).forEach(k),r.forEach(k),this.h()},h(){x(t,"d","M6 12H12M18 12H12M12 12V6M12 12V18"),x(t,"stroke","currentColor"),x(t,"stroke-width","1.5"),x(t,"stroke-linecap","round"),x(t,"stroke-linejoin","round"),x(e,"width","100%"),x(e,"height","100%"),x(e,"viewBox","0 0 24 24"),x(e,"fill","none"),x(e,"xmlns","http://www.w3.org/2000/svg")},m(i,r){D(i,e,r),O(e,t)},p:$,i:$,o:$,d(i){i&&k(e)}}}class Sa extends ke{constructor(e){super(),Ae(this,e,null,wc,Se,{})}}function xc(s){let e,t,i;return{c(){e=ue("svg"),t=ue("polyline"),i=ue("path"),this.h()},l(r){e=de(r,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0,class:!0,style:!0});var n=L(e);t=de(n,"polyline",{points:!0}),L(t).forEach(k),i=de(n,"path",{d:!0}),L(i).forEach(k),n.forEach(k),this.h()},h(){x(t,"points","1 4 1 10 7 10"),x(i,"d","M3.51 15a9 9 0 1 0 2.13-9.36L1 10"),x(e,"xmlns","http://www.w3.org/2000/svg"),x(e,"width","100%"),x(e,"height","100%"),x(e,"viewBox","0 0 24 24"),x(e,"fill","none"),x(e,"stroke","currentColor"),x(e,"stroke-width","2"),x(e,"stroke-linecap","round"),x(e,"stroke-linejoin","round"),x(e,"class","feather feather-rotate-ccw"),pe(e,"transform","rotateY(180deg)")},m(r,n){D(r,e,n),O(e,t),O(e,i)},p:$,i:$,o:$,d(r){r&&k(e)}}}class vc extends ke{constructor(e){super(),Ae(this,e,null,xc,Se,{})}}function kc(s){let e,t;return{c(){e=ue("svg"),t=ue("path"),this.h()},l(i){e=de(i,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0});var r=L(e);t=de(r,"path",{fill:!0,d:!0}),L(t).forEach(k),r.forEach(k),this.h()},h(){x(t,"fill","currentColor"),x(t,"d","M12 16q1.875 0 3.188-1.312T16.5 11.5t-1.312-3.187T12 7T8.813 8.313T7.5 11.5t1.313 3.188T12 16m0-1.8q-1.125 0-1.912-.788T9.3 11.5t.788-1.912T12 8.8t1.913.788t.787 1.912t-.787 1.913T12 14.2m0 4.8q-3.65 0-6.65-2.037T1 11.5q1.35-3.425 4.35-5.462T12 4t6.65 2.038T23 11.5q-1.35 3.425-4.35 5.463T12 19"),x(e,"xmlns","http://www.w3.org/2000/svg"),x(e,"width","100%"),x(e,"height","100%"),x(e,"viewBox","0 0 24 24")},m(i,r){D(i,e,r),O(e,t)},p:$,i:$,o:$,d(i){i&&k(e)}}}class Ac extends ke{constructor(e){super(),Ae(this,e,null,kc,Se,{})}}function Sc(s){let e,t;return{c(){e=ue("svg"),t=ue("path"),this.h()},l(i){e=de(i,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0});var r=L(e);t=de(r,"path",{fill:!0,d:!0}),L(t).forEach(k),r.forEach(k),this.h()},h(){x(t,"fill","currentColor"),x(t,"d","m19.8 22.6l-4.2-4.15q-.875.275-1.762.413T12 19q-3.775 0-6.725-2.087T1 11.5q.525-1.325 1.325-2.463T4.15 7L1.4 4.2l1.4-1.4l18.4 18.4zM12 16q.275 0 .513-.025t.512-.1l-5.4-5.4q-.075.275-.1.513T7.5 11.5q0 1.875 1.313 3.188T12 16m7.3.45l-3.175-3.15q.175-.425.275-.862t.1-.938q0-1.875-1.312-3.187T12 7q-.5 0-.937.1t-.863.3L7.65 4.85q1.025-.425 2.1-.637T12 4q3.775 0 6.725 2.088T23 11.5q-.575 1.475-1.513 2.738T19.3 16.45m-4.625-4.6l-3-3q.7-.125 1.288.113t1.012.687t.613 1.038t.087 1.162"),x(e,"xmlns","http://www.w3.org/2000/svg"),x(e,"width","100%"),x(e,"height","100%"),x(e,"viewBox","0 0 24 24")},m(i,r){D(i,e,r),O(e,t)},p:$,i:$,o:$,d(i){i&&k(e)}}}class Cc extends ke{constructor(e){super(),Ae(this,e,null,Sc,Se,{})}}function Mc(s){let e,t;return{c(){e=ue("svg"),t=ue("path"),this.h()},l(i){e=de(i,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0});var r=L(e);t=de(r,"path",{fill:!0,d:!0}),L(t).forEach(k),r.forEach(k),this.h()},h(){x(t,"fill","currentColor"),x(t,"d","M144 120v88a8 8 0 0 1-8 8H48a8 8 0 0 1-8-8v-88a8 8 0 0 1 8-8h88a8 8 0 0 1 8 8m64 56a8 8 0 0 0-8 8v16h-24a8 8 0 0 0 0 16h24a16 16 0 0 0 16-16v-16a8 8 0 0 0-8-8m0-72a8 8 0 0 0-8 8v32a8 8 0 0 0 16 0v-32a8 8 0 0 0-8-8m-8-64h-16a8 8 0 0 0 0 16h16v16a8 8 0 0 0 16 0V56a16 16 0 0 0-16-16m-56 0h-32a8 8 0 0 0 0 16h32a8 8 0 0 0 0-16M48 88a8 8 0 0 0 8-8V56h16a8 8 0 0 0 0-16H56a16 16 0 0 0-16 16v24a8 8 0 0 0 8 8"),x(e,"xmlns","http://www.w3.org/2000/svg"),x(e,"width","100%"),x(e,"height","100%"),x(e,"viewBox","0 0 256 256")},m(i,r){D(i,e,r),O(e,t)},p:$,i:$,o:$,d(i){i&&k(e)}}}class Tc extends ke{constructor(e){super(),Ae(this,e,null,Mc,Se,{})}}function Pc(s){let e,t;return{c(){e=ue("svg"),t=ue("path"),this.h()},l(i){e=de(i,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0});var r=L(e);t=de(r,"path",{fill:!0,stroke:!0,"stroke-linecap":!0,"stroke-linejoin":!0,"stroke-width":!0,d:!0}),L(t).forEach(k),r.forEach(k),this.h()},h(){x(t,"fill","none"),x(t,"stroke","currentColor"),x(t,"stroke-linecap","round"),x(t,"stroke-linejoin","round"),x(t,"stroke-width","2"),x(t,"d","m21 21l-4.343-4.343m0 0A8 8 0 1 0 5.343 5.343a8 8 0 0 0 11.314 11.314M11 8v6m-3-3h6"),x(e,"xmlns","http://www.w3.org/2000/svg"),x(e,"width","100%"),x(e,"height","100%"),x(e,"viewBox","0 0 24 24")},m(i,r){D(i,e,r),O(e,t)},p:$,i:$,o:$,d(i){i&&k(e)}}}class Ic extends ke{constructor(e){super(),Ae(this,e,null,Pc,Se,{})}}function Ec(s){let e,t;return{c(){e=ue("svg"),t=ue("path"),this.h()},l(i){e=de(i,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0});var r=L(e);t=de(r,"path",{fill:!0,stroke:!0,"stroke-linecap":!0,"stroke-linejoin":!0,"stroke-width":!0,d:!0}),L(t).forEach(k),r.forEach(k),this.h()},h(){x(t,"fill","none"),x(t,"stroke","currentColor"),x(t,"stroke-linecap","round"),x(t,"stroke-linejoin","round"),x(t,"stroke-width","2"),x(t,"d","m21 21l-4.343-4.343m0 0A8 8 0 1 0 5.343 5.343a8 8 0 0 0 11.314 11.314M8 11h6"),x(e,"xmlns","http://www.w3.org/2000/svg"),x(e,"width","100%"),x(e,"height","100%"),x(e,"viewBox","0 0 24 24")},m(i,r){D(i,e,r),O(e,t)},p:$,i:$,o:$,d(i){i&&k(e)}}}class zc extends ke{constructor(e){super(),Ae(this,e,null,Ec,Se,{})}}class cs{constructor(e){v(this,"command");v(this,"next");v(this,"previous");this.command=e||null,this.next=null,this.previous=null}push(e){this.next=null;const t=new cs(e);t.previous=this,this.next=t}}class Rc{constructor(){v(this,"history",new cs);v(this,"current_history",ci(this.history))}undo(){var e;this.history.previous&&((e=this.history.command)==null||e.undo(),this.history=this.history.previous,this.current_history.update(()=>this.history))}redo(e){var t;this.history.next&&(this.history=this.history.next,(t=this.history.command)==null||t.execute(e),this.current_history.update(()=>this.history))}async execute(e,t){await e.execute(t),this.history.push(e),this.history=this.history.next,this.current_history.update(()=>this.history)}async wait_for_next_frame(){return new Promise(e=>{requestAnimationFrame(()=>{e()})})}async replay(e,t){for(;e.previous;)e=e.previous;for(;e.next;)await e.next.command.execute(t),e=e.next;this.history=e,this.current_history.update(()=>this.history)}contains(e){var i;let t=this.history;for(;t;){if(((i=t.command)==null?void 0:i.name)===e)return!0;t=t.next}return!1}reset(){this.history=new cs,this.current_history.update(()=>this.history)}}function on(s){let e,t;return{c(){e=Y("span"),t=St(s[1]),this.h()},l(i){e=j(i,"SPAN",{style:!0,class:!0});var r=L(e);t=Ct(r,s[1]),r.forEach(k),this.h()},h(){pe(e,"margin-left","4px"),x(e,"class","svelte-rrog8b")},m(i,r){D(i,e,r),O(e,t)},p(i,r){r&2&&ii(t,i[1])},d(i){i&&k(e)}}}function an(s){let e,t;return{c(){e=Y("span"),t=St(s[1]),this.h()},l(i){e=j(i,"SPAN",{style:!0,class:!0});var r=L(e);t=Ct(r,s[1]),r.forEach(k),this.h()},h(){pe(e,"margin-right","4px"),x(e,"class","svelte-rrog8b")},m(i,r){D(i,e,r),O(e,t)},p(i,r){r&2&&ii(t,i[1])},d(i){i&&k(e)}}}function Bc(s){let e,t,i,r,n,o,a,l,h,c=s[2]&&s[12]==="left"&&on(s);r=new s[0]({});let u=s[2]&&s[12]==="right"&&an(s);return{c(){e=Y("button"),c&&c.c(),t=Z(),i=Y("div"),ee(r.$$.fragment),n=Z(),u&&u.c(),this.h()},l(f){e=j(f,"BUTTON",{"aria-label":!0,"aria-haspopup":!0,title:!0,class:!0});var d=L(e);c&&c.l(d),t=Q(d),i=j(d,"DIV",{class:!0});var _=L(i);te(r.$$.fragment,_),_.forEach(k),n=Q(d),u&&u.l(d),d.forEach(k),this.h()},h(){x(i,"class","svelte-rrog8b"),oe(i,"small",s[4]==="small"),oe(i,"large",s[4]==="large"),oe(i,"medium",s[4]==="medium"),e.disabled=s[7],x(e,"aria-label",s[1]),x(e,"aria-haspopup",s[8]),x(e,"title",s[1]),x(e,"class",o=s[13]+"-round svelte-rrog8b"),oe(e,"pending",s[3]),oe(e,"padded",s[5]),oe(e,"highlight",s[6]),oe(e,"transparent",s[9]),pe(e,"color",!s[7]&&s[14]?s[14]:"var(--block-label-text-color)"),pe(e,"--bg-color",s[7]?"auto":s[10]),pe(e,"margin-left",s[11]+"px")},m(f,d){D(f,e,d),c&&c.m(e,null),O(e,t),O(e,i),ie(r,i,null),O(e,n),u&&u.m(e,null),a=!0,l||(h=Fe(e,"click",s[16]),l=!0)},p(f,[d]){f[2]&&f[12]==="left"?c?c.p(f,d):(c=on(f),c.c(),c.m(e,t)):c&&(c.d(1),c=null),(!a||d&16)&&oe(i,"small",f[4]==="small"),(!a||d&16)&&oe(i,"large",f[4]==="large"),(!a||d&16)&&oe(i,"medium",f[4]==="medium"),f[2]&&f[12]==="right"?u?u.p(f,d):(u=an(f),u.c(),u.m(e,null)):u&&(u.d(1),u=null),(!a||d&128)&&(e.disabled=f[7]),(!a||d&2)&&x(e,"aria-label",f[1]),(!a||d&256)&&x(e,"aria-haspopup",f[8]),(!a||d&2)&&x(e,"title",f[1]),(!a||d&8192&&o!==(o=f[13]+"-round svelte-rrog8b"))&&x(e,"class",o),(!a||d&8200)&&oe(e,"pending",f[3]),(!a||d&8224)&&oe(e,"padded",f[5]),(!a||d&8256)&&oe(e,"highlight",f[6]),(!a||d&8704)&&oe(e,"transparent",f[9]),d&16512&&pe(e,"color",!f[7]&&f[14]?f[14]:"var(--block-label-text-color)"),d&1152&&pe(e,"--bg-color",f[7]?"auto":f[10]),d&2048&&pe(e,"margin-left",f[11]+"px")},i(f){a||(C(r.$$.fragment,f),a=!0)},o(f){I(r.$$.fragment,f),a=!1},d(f){f&&k(e),c&&c.d(),se(r),u&&u.d(),l=!1,h()}}}function Fc(s,e,t){let i,{Icon:r}=e,{label:n=""}=e,{show_label:o=!1}=e,{pending:a=!1}=e,{size:l="small"}=e,{padded:h=!0}=e,{highlight:c=!1}=e,{disabled:u=!1}=e,{hasPopup:f=!1}=e,{color:d="var(--block-label-text-color)"}=e,{transparent:_=!1}=e,{background:g="var(--background-fill-primary)"}=e,{offset:m=0}=e,{label_position:p="left"}=e,{roundedness:b="quite"}=e;function y(w){yt.call(this,s,w)}return s.$$set=w=>{"Icon"in w&&t(0,r=w.Icon),"label"in w&&t(1,n=w.label),"show_label"in w&&t(2,o=w.show_label),"pending"in w&&t(3,a=w.pending),"size"in w&&t(4,l=w.size),"padded"in w&&t(5,h=w.padded),"highlight"in w&&t(6,c=w.highlight),"disabled"in w&&t(7,u=w.disabled),"hasPopup"in w&&t(8,f=w.hasPopup),"color"in w&&t(15,d=w.color),"transparent"in w&&t(9,_=w.transparent),"background"in w&&t(10,g=w.background),"offset"in w&&t(11,m=w.offset),"label_position"in w&&t(12,p=w.label_position),"roundedness"in w&&t(13,b=w.roundedness)},s.$$.update=()=>{s.$$.dirty&32832&&t(14,i=c?"var(--color-accent)":d.toString())},[r,n,o,a,l,h,c,u,f,_,g,m,p,b,i,d,y]}class gt extends ke{constructor(e){super(),Ae(this,e,Fc,Bc,Se,{Icon:0,label:1,show_label:2,pending:3,size:4,padded:5,highlight:6,disabled:7,hasPopup:8,color:15,transparent:9,background:10,offset:11,label_position:12,roundedness:13})}}function Wr(s,e){const t=i=>{s&&!s.contains(i.target)&&!i.defaultPrevented&&e(i)};return document.addEventListener("mousedown",t,!0),{destroy(){document.removeEventListener("mousedown",t,!0)}}}var J=(s=>(s.Application="application",s.WebGLPipes="webgl-pipes",s.WebGLPipesAdaptor="webgl-pipes-adaptor",s.WebGLSystem="webgl-system",s.WebGPUPipes="webgpu-pipes",s.WebGPUPipesAdaptor="webgpu-pipes-adaptor",s.WebGPUSystem="webgpu-system",s.CanvasSystem="canvas-system",s.CanvasPipesAdaptor="canvas-pipes-adaptor",s.CanvasPipes="canvas-pipes",s.Asset="asset",s.LoadParser="load-parser",s.ResolveParser="resolve-parser",s.CacheParser="cache-parser",s.DetectionParser="detection-parser",s.MaskEffect="mask-effect",s.BlendMode="blend-mode",s.TextureSource="texture-source",s.Environment="environment",s.ShapeBuilder="shape-builder",s.Batcher="batcher",s))(J||{});const cr=s=>{if(typeof s=="function"||typeof s=="object"&&s.extension){if(!s.extension)throw new Error("Extension class must have an extension object");s={...typeof s.extension!="object"?{type:s.extension}:s.extension,ref:s}}if(typeof s=="object")s={...s};else throw new Error("Invalid extension type");return typeof s.type=="string"&&(s.type=[s.type]),s},Hi=(s,e)=>cr(s).priority??e,rt={_addHandlers:{},_removeHandlers:{},_queue:{},remove(...s){return s.map(cr).forEach(e=>{e.type.forEach(t=>{var i,r;return(r=(i=this._removeHandlers)[t])==null?void 0:r.call(i,e)})}),this},add(...s){return s.map(cr).forEach(e=>{e.type.forEach(t=>{var n,o;const i=this._addHandlers,r=this._queue;i[t]?(o=i[t])==null||o.call(i,e):(r[t]=r[t]||[],(n=r[t])==null||n.push(e))})}),this},handle(s,e,t){var o;const i=this._addHandlers,r=this._removeHandlers;if(i[s]||r[s])throw new Error(`Extension type ${s} already has a handler`);i[s]=e,r[s]=t;const n=this._queue;return n[s]&&((o=n[s])==null||o.forEach(a=>e(a)),delete n[s]),this},handleByMap(s,e){return this.handle(s,t=>{t.name&&(e[t.name]=t.ref)},t=>{t.name&&delete e[t.name]})},handleByNamedList(s,e,t=-1){return this.handle(s,i=>{e.findIndex(n=>n.name===i.name)>=0||(e.push({name:i.name,value:i.ref}),e.sort((n,o)=>Hi(o.value,t)-Hi(n.value,t)))},i=>{const r=e.findIndex(n=>n.name===i.name);r!==-1&&e.splice(r,1)})},handleByList(s,e,t=-1){return this.handle(s,i=>{e.includes(i.ref)||(e.push(i.ref),e.sort((r,n)=>Hi(n,t)-Hi(r,t)))},i=>{const r=e.indexOf(i.ref);r!==-1&&e.splice(r,1)})}},Lc={extension:{type:J.Environment,name:"browser",priority:-1},test:()=>!0,load:async()=>{await hs(()=>import("./browserAll.C_UrN5Lo.js"),__vite__mapDeps([0,1,2]),import.meta.url)}},Oc={extension:{type:J.Environment,name:"webworker",priority:0},test:()=>typeof self<"u"&&self.WorkerGlobalScope!==void 0,load:async()=>{await hs(()=>import("./webworkerAll.BJf8EwlM.js"),__vite__mapDeps([3,1,2]),import.meta.url)}};class dt{constructor(e,t,i){this._x=t||0,this._y=i||0,this._observer=e}clone(e){return new dt(e??this._observer,this._x,this._y)}set(e=0,t=e){return(this._x!==e||this._y!==t)&&(this._x=e,this._y=t,this._observer._onUpdate(this)),this}copyFrom(e){return(this._x!==e.x||this._y!==e.y)&&(this._x=e.x,this._y=e.y,this._observer._onUpdate(this)),this}copyTo(e){return e.set(this._x,this._y),e}equals(e){return e.x===this._x&&e.y===this._y}toString(){return`[pixi.js/math:ObservablePoint x=0 y=0 scope=${this._observer}]`}get x(){return this._x}set x(e){this._x!==e&&(this._x=e,this._observer._onUpdate(this))}get y(){return this._y}set y(e){this._y!==e&&(this._y=e,this._observer._onUpdate(this))}}var Ca={exports:{}};(function(s){var e=Object.prototype.hasOwnProperty,t="~";function i(){}Object.create&&(i.prototype=Object.create(null),new i().__proto__||(t=!1));function r(l,h,c){this.fn=l,this.context=h,this.once=c||!1}function n(l,h,c,u,f){if(typeof c!="function")throw new TypeError("The listener must be a function");var d=new r(c,u||l,f),_=t?t+h:h;return l._events[_]?l._events[_].fn?l._events[_]=[l._events[_],d]:l._events[_].push(d):(l._events[_]=d,l._eventsCount++),l}function o(l,h){--l._eventsCount===0?l._events=new i:delete l._events[h]}function a(){this._events=new i,this._eventsCount=0}a.prototype.eventNames=function(){var h=[],c,u;if(this._eventsCount===0)return h;for(u in c=this._events)e.call(c,u)&&h.push(t?u.slice(1):u);return Object.getOwnPropertySymbols?h.concat(Object.getOwnPropertySymbols(c)):h},a.prototype.listeners=function(h){var c=t?t+h:h,u=this._events[c];if(!u)return[];if(u.fn)return[u.fn];for(var f=0,d=u.length,_=new Array(d);f<d;f++)_[f]=u[f].fn;return _},a.prototype.listenerCount=function(h){var c=t?t+h:h,u=this._events[c];return u?u.fn?1:u.length:0},a.prototype.emit=function(h,c,u,f,d,_){var g=t?t+h:h;if(!this._events[g])return!1;var m=this._events[g],p=arguments.length,b,y;if(m.fn){switch(m.once&&this.removeListener(h,m.fn,void 0,!0),p){case 1:return m.fn.call(m.context),!0;case 2:return m.fn.call(m.context,c),!0;case 3:return m.fn.call(m.context,c,u),!0;case 4:return m.fn.call(m.context,c,u,f),!0;case 5:return m.fn.call(m.context,c,u,f,d),!0;case 6:return m.fn.call(m.context,c,u,f,d,_),!0}for(y=1,b=new Array(p-1);y<p;y++)b[y-1]=arguments[y];m.fn.apply(m.context,b)}else{var w=m.length,A;for(y=0;y<w;y++)switch(m[y].once&&this.removeListener(h,m[y].fn,void 0,!0),p){case 1:m[y].fn.call(m[y].context);break;case 2:m[y].fn.call(m[y].context,c);break;case 3:m[y].fn.call(m[y].context,c,u);break;case 4:m[y].fn.call(m[y].context,c,u,f);break;default:if(!b)for(A=1,b=new Array(p-1);A<p;A++)b[A-1]=arguments[A];m[y].fn.apply(m[y].context,b)}}return!0},a.prototype.on=function(h,c,u){return n(this,h,c,u,!1)},a.prototype.once=function(h,c,u){return n(this,h,c,u,!0)},a.prototype.removeListener=function(h,c,u,f){var d=t?t+h:h;if(!this._events[d])return this;if(!c)return o(this,d),this;var _=this._events[d];if(_.fn)_.fn===c&&(!f||_.once)&&(!u||_.context===u)&&o(this,d);else{for(var g=0,m=[],p=_.length;g<p;g++)(_[g].fn!==c||f&&!_[g].once||u&&_[g].context!==u)&&m.push(_[g]);m.length?this._events[d]=m.length===1?m[0]:m:o(this,d)}return this},a.prototype.removeAllListeners=function(h){var c;return h?(c=t?t+h:h,this._events[c]&&o(this,c)):(this._events=new i,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=t,a.EventEmitter=a,s.exports=a})(Ca);var Gc=Ca.exports;const Tt=Ur(Gc),Dc=Math.PI*2,Nc=180/Math.PI,Uc=Math.PI/180;class fe{constructor(e=0,t=0){this.x=0,this.y=0,this.x=e,this.y=t}clone(){return new fe(this.x,this.y)}copyFrom(e){return this.set(e.x,e.y),this}copyTo(e){return e.set(this.x,this.y),e}equals(e){return e.x===this.x&&e.y===this.y}set(e=0,t=e){return this.x=e,this.y=t,this}toString(){return`[pixi.js/math:Point x=${this.x} y=${this.y}]`}static get shared(){return Is.x=0,Is.y=0,Is}}const Is=new fe;class we{constructor(e=1,t=0,i=0,r=1,n=0,o=0){this.array=null,this.a=e,this.b=t,this.c=i,this.d=r,this.tx=n,this.ty=o}fromArray(e){this.a=e[0],this.b=e[1],this.c=e[3],this.d=e[4],this.tx=e[2],this.ty=e[5]}set(e,t,i,r,n,o){return this.a=e,this.b=t,this.c=i,this.d=r,this.tx=n,this.ty=o,this}toArray(e,t){this.array||(this.array=new Float32Array(9));const i=t||this.array;return e?(i[0]=this.a,i[1]=this.b,i[2]=0,i[3]=this.c,i[4]=this.d,i[5]=0,i[6]=this.tx,i[7]=this.ty,i[8]=1):(i[0]=this.a,i[1]=this.c,i[2]=this.tx,i[3]=this.b,i[4]=this.d,i[5]=this.ty,i[6]=0,i[7]=0,i[8]=1),i}apply(e,t){t=t||new fe;const i=e.x,r=e.y;return t.x=this.a*i+this.c*r+this.tx,t.y=this.b*i+this.d*r+this.ty,t}applyInverse(e,t){t=t||new fe;const i=this.a,r=this.b,n=this.c,o=this.d,a=this.tx,l=this.ty,h=1/(i*o+n*-r),c=e.x,u=e.y;return t.x=o*h*c+-n*h*u+(l*n-a*o)*h,t.y=i*h*u+-r*h*c+(-l*i+a*r)*h,t}translate(e,t){return this.tx+=e,this.ty+=t,this}scale(e,t){return this.a*=e,this.d*=t,this.c*=e,this.b*=t,this.tx*=e,this.ty*=t,this}rotate(e){const t=Math.cos(e),i=Math.sin(e),r=this.a,n=this.c,o=this.tx;return this.a=r*t-this.b*i,this.b=r*i+this.b*t,this.c=n*t-this.d*i,this.d=n*i+this.d*t,this.tx=o*t-this.ty*i,this.ty=o*i+this.ty*t,this}append(e){const t=this.a,i=this.b,r=this.c,n=this.d;return this.a=e.a*t+e.b*r,this.b=e.a*i+e.b*n,this.c=e.c*t+e.d*r,this.d=e.c*i+e.d*n,this.tx=e.tx*t+e.ty*r+this.tx,this.ty=e.tx*i+e.ty*n+this.ty,this}appendFrom(e,t){const i=e.a,r=e.b,n=e.c,o=e.d,a=e.tx,l=e.ty,h=t.a,c=t.b,u=t.c,f=t.d;return this.a=i*h+r*u,this.b=i*c+r*f,this.c=n*h+o*u,this.d=n*c+o*f,this.tx=a*h+l*u+t.tx,this.ty=a*c+l*f+t.ty,this}setTransform(e,t,i,r,n,o,a,l,h){return this.a=Math.cos(a+h)*n,this.b=Math.sin(a+h)*n,this.c=-Math.sin(a-l)*o,this.d=Math.cos(a-l)*o,this.tx=e-(i*this.a+r*this.c),this.ty=t-(i*this.b+r*this.d),this}prepend(e){const t=this.tx;if(e.a!==1||e.b!==0||e.c!==0||e.d!==1){const i=this.a,r=this.c;this.a=i*e.a+this.b*e.c,this.b=i*e.b+this.b*e.d,this.c=r*e.a+this.d*e.c,this.d=r*e.b+this.d*e.d}return this.tx=t*e.a+this.ty*e.c+e.tx,this.ty=t*e.b+this.ty*e.d+e.ty,this}decompose(e){const t=this.a,i=this.b,r=this.c,n=this.d,o=e.pivot,a=-Math.atan2(-r,n),l=Math.atan2(i,t),h=Math.abs(a+l);return h<1e-5||Math.abs(Dc-h)<1e-5?(e.rotation=l,e.skew.x=e.skew.y=0):(e.rotation=0,e.skew.x=a,e.skew.y=l),e.scale.x=Math.sqrt(t*t+i*i),e.scale.y=Math.sqrt(r*r+n*n),e.position.x=this.tx+(o.x*t+o.y*r),e.position.y=this.ty+(o.x*i+o.y*n),e}invert(){const e=this.a,t=this.b,i=this.c,r=this.d,n=this.tx,o=e*r-t*i;return this.a=r/o,this.b=-t/o,this.c=-i/o,this.d=e/o,this.tx=(i*this.ty-r*n)/o,this.ty=-(e*this.ty-t*n)/o,this}isIdentity(){return this.a===1&&this.b===0&&this.c===0&&this.d===1&&this.tx===0&&this.ty===0}identity(){return this.a=1,this.b=0,this.c=0,this.d=1,this.tx=0,this.ty=0,this}clone(){const e=new we;return e.a=this.a,e.b=this.b,e.c=this.c,e.d=this.d,e.tx=this.tx,e.ty=this.ty,e}copyTo(e){return e.a=this.a,e.b=this.b,e.c=this.c,e.d=this.d,e.tx=this.tx,e.ty=this.ty,e}copyFrom(e){return this.a=e.a,this.b=e.b,this.c=e.c,this.d=e.d,this.tx=e.tx,this.ty=e.ty,this}equals(e){return e.a===this.a&&e.b===this.b&&e.c===this.c&&e.d===this.d&&e.tx===this.tx&&e.ty===this.ty}toString(){return`[pixi.js:Matrix a=${this.a} b=${this.b} c=${this.c} d=${this.d} tx=${this.tx} ty=${this.ty}]`}static get IDENTITY(){return Hc.identity()}static get shared(){return Wc.identity()}}const Wc=new we,Hc=new we,jt=[1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1,0,1],qt=[0,1,1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1],Kt=[0,-1,-1,-1,0,1,1,1,0,1,1,1,0,-1,-1,-1],Zt=[1,1,0,-1,-1,-1,0,1,-1,-1,0,1,1,1,0,-1],ur=[],Ma=[],Vi=Math.sign;function Vc(){for(let s=0;s<16;s++){const e=[];ur.push(e);for(let t=0;t<16;t++){const i=Vi(jt[s]*jt[t]+Kt[s]*qt[t]),r=Vi(qt[s]*jt[t]+Zt[s]*qt[t]),n=Vi(jt[s]*Kt[t]+Kt[s]*Zt[t]),o=Vi(qt[s]*Kt[t]+Zt[s]*Zt[t]);for(let a=0;a<16;a++)if(jt[a]===i&&qt[a]===r&&Kt[a]===n&&Zt[a]===o){e.push(a);break}}}for(let s=0;s<16;s++){const e=new we;e.set(jt[s],qt[s],Kt[s],Zt[s],0,0),Ma.push(e)}}Vc();const ze={E:0,SE:1,S:2,SW:3,W:4,NW:5,N:6,NE:7,MIRROR_VERTICAL:8,MAIN_DIAGONAL:10,MIRROR_HORIZONTAL:12,REVERSE_DIAGONAL:14,uX:s=>jt[s],uY:s=>qt[s],vX:s=>Kt[s],vY:s=>Zt[s],inv:s=>s&8?s&15:-s&7,add:(s,e)=>ur[s][e],sub:(s,e)=>ur[s][ze.inv(e)],rotate180:s=>s^4,isVertical:s=>(s&3)===2,byDirection:(s,e)=>Math.abs(s)*2<=Math.abs(e)?e>=0?ze.S:ze.N:Math.abs(e)*2<=Math.abs(s)?s>0?ze.E:ze.W:e>0?s>0?ze.SE:ze.SW:s>0?ze.NE:ze.NW,matrixAppendRotationInv:(s,e,t=0,i=0)=>{const r=Ma[ze.inv(e)];r.tx=t,r.ty=i,s.append(r)}},Xi=[new fe,new fe,new fe,new fe];class Pe{constructor(e=0,t=0,i=0,r=0){this.type="rectangle",this.x=Number(e),this.y=Number(t),this.width=Number(i),this.height=Number(r)}get left(){return this.x}get right(){return this.x+this.width}get top(){return this.y}get bottom(){return this.y+this.height}isEmpty(){return this.left===this.right||this.top===this.bottom}static get EMPTY(){return new Pe(0,0,0,0)}clone(){return new Pe(this.x,this.y,this.width,this.height)}copyFromBounds(e){return this.x=e.minX,this.y=e.minY,this.width=e.maxX-e.minX,this.height=e.maxY-e.minY,this}copyFrom(e){return this.x=e.x,this.y=e.y,this.width=e.width,this.height=e.height,this}copyTo(e){return e.copyFrom(this),e}contains(e,t){return this.width<=0||this.height<=0?!1:e>=this.x&&e<this.x+this.width&&t>=this.y&&t<this.y+this.height}strokeContains(e,t,i,r=.5){const{width:n,height:o}=this;if(n<=0||o<=0)return!1;const a=this.x,l=this.y,h=i*(1-r),c=i-h,u=a-h,f=a+n+h,d=l-h,_=l+o+h,g=a+c,m=a+n-c,p=l+c,b=l+o-c;return e>=u&&e<=f&&t>=d&&t<=_&&!(e>g&&e<m&&t>p&&t<b)}intersects(e,t){if(!t){const U=this.x<e.x?e.x:this.x;if((this.right>e.right?e.right:this.right)<=U)return!1;const E=this.y<e.y?e.y:this.y;return(this.bottom>e.bottom?e.bottom:this.bottom)>E}const i=this.left,r=this.right,n=this.top,o=this.bottom;if(r<=i||o<=n)return!1;const a=Xi[0].set(e.left,e.top),l=Xi[1].set(e.left,e.bottom),h=Xi[2].set(e.right,e.top),c=Xi[3].set(e.right,e.bottom);if(h.x<=a.x||l.y<=a.y)return!1;const u=Math.sign(t.a*t.d-t.b*t.c);if(u===0||(t.apply(a,a),t.apply(l,l),t.apply(h,h),t.apply(c,c),Math.max(a.x,l.x,h.x,c.x)<=i||Math.min(a.x,l.x,h.x,c.x)>=r||Math.max(a.y,l.y,h.y,c.y)<=n||Math.min(a.y,l.y,h.y,c.y)>=o))return!1;const f=u*(l.y-a.y),d=u*(a.x-l.x),_=f*i+d*n,g=f*r+d*n,m=f*i+d*o,p=f*r+d*o;if(Math.max(_,g,m,p)<=f*a.x+d*a.y||Math.min(_,g,m,p)>=f*c.x+d*c.y)return!1;const b=u*(a.y-h.y),y=u*(h.x-a.x),w=b*i+y*n,A=b*r+y*n,T=b*i+y*o,P=b*r+y*o;return!(Math.max(w,A,T,P)<=b*a.x+y*a.y||Math.min(w,A,T,P)>=b*c.x+y*c.y)}pad(e=0,t=e){return this.x-=e,this.y-=t,this.width+=e*2,this.height+=t*2,this}fit(e){const t=Math.max(this.x,e.x),i=Math.min(this.x+this.width,e.x+e.width),r=Math.max(this.y,e.y),n=Math.min(this.y+this.height,e.y+e.height);return this.x=t,this.width=Math.max(i-t,0),this.y=r,this.height=Math.max(n-r,0),this}ceil(e=1,t=.001){const i=Math.ceil((this.x+this.width-t)*e)/e,r=Math.ceil((this.y+this.height-t)*e)/e;return this.x=Math.floor((this.x+t)*e)/e,this.y=Math.floor((this.y+t)*e)/e,this.width=i-this.x,this.height=r-this.y,this}enlarge(e){const t=Math.min(this.x,e.x),i=Math.max(this.x+this.width,e.x+e.width),r=Math.min(this.y,e.y),n=Math.max(this.y+this.height,e.y+e.height);return this.x=t,this.width=i-t,this.y=r,this.height=n-r,this}getBounds(e){return e||(e=new Pe),e.copyFrom(this),e}toString(){return`[pixi.js/math:Rectangle x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`}}const Es={default:-1};function He(s="default"){return Es[s]===void 0&&(Es[s]=-1),++Es[s]}const ln={},xe="8.0.0",Xc="8.3.4";function be(s,e,t=3){if(ln[e])return;let i=new Error().stack;typeof i>"u"?console.warn("PixiJS Deprecation Warning: ",`${e}
Deprecated since v${s}`):(i=i.split(`
`).splice(t).join(`
`),console.groupCollapsed?(console.groupCollapsed("%cPixiJS Deprecation Warning: %c%s","color:#614108;background:#fffbe6","font-weight:normal;color:#614108;background:#fffbe6",`${e}
Deprecated since v${s}`),console.warn(i),console.groupEnd()):(console.warn("PixiJS Deprecation Warning: ",`${e}
Deprecated since v${s}`),console.warn(i))),ln[e]=!0}const Ta=()=>{};function us(s){return s+=s===0?1:0,--s,s|=s>>>1,s|=s>>>2,s|=s>>>4,s|=s>>>8,s|=s>>>16,s+1}function hn(s){return!(s&s-1)&&!!s}function Yc(s){const e={};for(const t in s)s[t]!==void 0&&(e[t]=s[t]);return e}const cn=Object.create(null);function jc(s){const e=cn[s];return e===void 0&&(cn[s]=He("resource")),e}const Pa=class Ia extends Tt{constructor(e={}){super(),this._resourceType="textureSampler",this._touched=0,this._maxAnisotropy=1,this.destroyed=!1,e={...Ia.defaultOptions,...e},this.addressMode=e.addressMode,this.addressModeU=e.addressModeU??this.addressModeU,this.addressModeV=e.addressModeV??this.addressModeV,this.addressModeW=e.addressModeW??this.addressModeW,this.scaleMode=e.scaleMode,this.magFilter=e.magFilter??this.magFilter,this.minFilter=e.minFilter??this.minFilter,this.mipmapFilter=e.mipmapFilter??this.mipmapFilter,this.lodMinClamp=e.lodMinClamp,this.lodMaxClamp=e.lodMaxClamp,this.compare=e.compare,this.maxAnisotropy=e.maxAnisotropy??1}set addressMode(e){this.addressModeU=e,this.addressModeV=e,this.addressModeW=e}get addressMode(){return this.addressModeU}set wrapMode(e){be(xe,"TextureStyle.wrapMode is now TextureStyle.addressMode"),this.addressMode=e}get wrapMode(){return this.addressMode}set scaleMode(e){this.magFilter=e,this.minFilter=e,this.mipmapFilter=e}get scaleMode(){return this.magFilter}set maxAnisotropy(e){this._maxAnisotropy=Math.min(e,16),this._maxAnisotropy>1&&(this.scaleMode="linear")}get maxAnisotropy(){return this._maxAnisotropy}get _resourceId(){return this._sharedResourceId||this._generateResourceId()}update(){this.emit("change",this),this._sharedResourceId=null}_generateResourceId(){const e=`${this.addressModeU}-${this.addressModeV}-${this.addressModeW}-${this.magFilter}-${this.minFilter}-${this.mipmapFilter}-${this.lodMinClamp}-${this.lodMaxClamp}-${this.compare}-${this._maxAnisotropy}`;return this._sharedResourceId=jc(e),this._resourceId}destroy(){this.destroyed=!0,this.emit("destroy",this),this.emit("change",this),this.removeAllListeners()}};Pa.defaultOptions={addressMode:"clamp-to-edge",scaleMode:"linear"};let qc=Pa;const Ea=class za extends Tt{constructor(e={}){super(),this.options=e,this.uid=He("textureSource"),this._resourceType="textureSource",this._resourceId=He("resource"),this.uploadMethodId="unknown",this._resolution=1,this.pixelWidth=1,this.pixelHeight=1,this.width=1,this.height=1,this.sampleCount=1,this.mipLevelCount=1,this.autoGenerateMipmaps=!1,this.format="rgba8unorm",this.dimension="2d",this.antialias=!1,this._touched=0,this._batchTick=-1,this._textureBindLocation=-1,e={...za.defaultOptions,...e},this.label=e.label??"",this.resource=e.resource,this.autoGarbageCollect=e.autoGarbageCollect,this._resolution=e.resolution,e.width?this.pixelWidth=e.width*this._resolution:this.pixelWidth=this.resource?this.resourceWidth??1:1,e.height?this.pixelHeight=e.height*this._resolution:this.pixelHeight=this.resource?this.resourceHeight??1:1,this.width=this.pixelWidth/this._resolution,this.height=this.pixelHeight/this._resolution,this.format=e.format,this.dimension=e.dimensions,this.mipLevelCount=e.mipLevelCount,this.autoGenerateMipmaps=e.autoGenerateMipmaps,this.sampleCount=e.sampleCount,this.antialias=e.antialias,this.alphaMode=e.alphaMode,this.style=new qc(Yc(e)),this.destroyed=!1,this._refreshPOT()}get source(){return this}get style(){return this._style}set style(e){var t,i;this.style!==e&&((t=this._style)==null||t.off("change",this._onStyleChange,this),this._style=e,(i=this._style)==null||i.on("change",this._onStyleChange,this),this._onStyleChange())}get addressMode(){return this._style.addressMode}set addressMode(e){this._style.addressMode=e}get repeatMode(){return this._style.addressMode}set repeatMode(e){this._style.addressMode=e}get magFilter(){return this._style.magFilter}set magFilter(e){this._style.magFilter=e}get minFilter(){return this._style.minFilter}set minFilter(e){this._style.minFilter=e}get mipmapFilter(){return this._style.mipmapFilter}set mipmapFilter(e){this._style.mipmapFilter=e}get lodMinClamp(){return this._style.lodMinClamp}set lodMinClamp(e){this._style.lodMinClamp=e}get lodMaxClamp(){return this._style.lodMaxClamp}set lodMaxClamp(e){this._style.lodMaxClamp=e}_onStyleChange(){this.emit("styleChange",this)}update(){if(this.resource){const e=this._resolution;if(this.resize(this.resourceWidth/e,this.resourceHeight/e))return}this.emit("update",this)}destroy(){this.destroyed=!0,this.emit("destroy",this),this.emit("change",this),this._style&&(this._style.destroy(),this._style=null),this.uploadMethodId=null,this.resource=null,this.removeAllListeners()}unload(){this._resourceId=He("resource"),this.emit("change",this),this.emit("unload",this)}get resourceWidth(){const{resource:e}=this;return e.naturalWidth||e.videoWidth||e.displayWidth||e.width}get resourceHeight(){const{resource:e}=this;return e.naturalHeight||e.videoHeight||e.displayHeight||e.height}get resolution(){return this._resolution}set resolution(e){this._resolution!==e&&(this._resolution=e,this.width=this.pixelWidth/e,this.height=this.pixelHeight/e)}resize(e,t,i){i||(i=this._resolution),e||(e=this.width),t||(t=this.height);const r=Math.round(e*i),n=Math.round(t*i);return this.width=r/i,this.height=n/i,this._resolution=i,this.pixelWidth===r&&this.pixelHeight===n?!1:(this._refreshPOT(),this.pixelWidth=r,this.pixelHeight=n,this.emit("resize",this),this._resourceId=He("resource"),this.emit("change",this),!0)}updateMipmaps(){this.autoGenerateMipmaps&&this.mipLevelCount>1&&this.emit("updateMipmaps",this)}set wrapMode(e){this._style.wrapMode=e}get wrapMode(){return this._style.wrapMode}set scaleMode(e){this._style.scaleMode=e}get scaleMode(){return this._style.scaleMode}_refreshPOT(){this.isPowerOfTwo=hn(this.pixelWidth)&&hn(this.pixelHeight)}static test(e){throw new Error("Unimplemented")}};Ea.defaultOptions={resolution:1,format:"bgra8unorm",alphaMode:"premultiply-alpha-on-upload",dimensions:"2d",mipLevelCount:1,autoGenerateMipmaps:!1,sampleCount:1,antialias:!1,autoGarbageCollect:!1};let Pt=Ea;class Hr extends Pt{constructor(e){const t=e.resource||new Float32Array(e.width*e.height*4);let i=e.format;i||(t instanceof Float32Array?i="rgba32float":t instanceof Int32Array||t instanceof Uint32Array?i="rgba32uint":t instanceof Int16Array||t instanceof Uint16Array?i="rgba16uint":(t instanceof Int8Array,i="bgra8unorm")),super({...e,resource:t,format:i}),this.uploadMethodId="buffer"}static test(e){return e instanceof Int8Array||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array}}Hr.extension=J.TextureSource;const un=new we;class Kc{constructor(e,t){this.mapCoord=new we,this.uClampFrame=new Float32Array(4),this.uClampOffset=new Float32Array(2),this._textureID=-1,this._updateID=0,this.clampOffset=0,typeof t>"u"?this.clampMargin=e.width<10?0:.5:this.clampMargin=t,this.isSimple=!1,this.texture=e}get texture(){return this._texture}set texture(e){var t;this.texture!==e&&((t=this._texture)==null||t.removeListener("update",this.update,this),this._texture=e,this._texture.addListener("update",this.update,this),this.update())}multiplyUvs(e,t){t===void 0&&(t=e);const i=this.mapCoord;for(let r=0;r<e.length;r+=2){const n=e[r],o=e[r+1];t[r]=n*i.a+o*i.c+i.tx,t[r+1]=n*i.b+o*i.d+i.ty}return t}update(){const e=this._texture;this._updateID++;const t=e.uvs;this.mapCoord.set(t.x1-t.x0,t.y1-t.y0,t.x3-t.x0,t.y3-t.y0,t.x0,t.y0);const i=e.orig,r=e.trim;r&&(un.set(i.width/r.width,0,0,i.height/r.height,-r.x/r.width,-r.y/r.height),this.mapCoord.append(un));const n=e.source,o=this.uClampFrame,a=this.clampMargin/n._resolution,l=this.clampOffset/n._resolution;return o[0]=(e.frame.x+a+l)/n.width,o[1]=(e.frame.y+a+l)/n.height,o[2]=(e.frame.x+e.frame.width-a+l)/n.width,o[3]=(e.frame.y+e.frame.height-a+l)/n.height,this.uClampOffset[0]=this.clampOffset/n.pixelWidth,this.uClampOffset[1]=this.clampOffset/n.pixelHeight,this.isSimple=e.frame.width===n.width&&e.frame.height===n.height&&e.rotate===0,!0}}class _e extends Tt{constructor({source:e,label:t,frame:i,orig:r,trim:n,defaultAnchor:o,defaultBorders:a,rotate:l,dynamic:h}={}){if(super(),this.uid=He("texture"),this.uvs={x0:0,y0:0,x1:0,y1:0,x2:0,y2:0,x3:0,y3:0},this.frame=new Pe,this.noFrame=!1,this.dynamic=!1,this.isTexture=!0,this.label=t,this.source=(e==null?void 0:e.source)??new Pt,this.noFrame=!i,i)this.frame.copyFrom(i);else{const{width:c,height:u}=this._source;this.frame.width=c,this.frame.height=u}this.orig=r||this.frame,this.trim=n,this.rotate=l??0,this.defaultAnchor=o,this.defaultBorders=a,this.destroyed=!1,this.dynamic=h||!1,this.updateUvs()}set source(e){this._source&&this._source.off("resize",this.update,this),this._source=e,e.on("resize",this.update,this),this.emit("update",this)}get source(){return this._source}get textureMatrix(){return this._textureMatrix||(this._textureMatrix=new Kc(this)),this._textureMatrix}get width(){return this.orig.width}get height(){return this.orig.height}updateUvs(){const{uvs:e,frame:t}=this,{width:i,height:r}=this._source,n=t.x/i,o=t.y/r,a=t.width/i,l=t.height/r;let h=this.rotate;if(h){const c=a/2,u=l/2,f=n+c,d=o+u;h=ze.add(h,ze.NW),e.x0=f+c*ze.uX(h),e.y0=d+u*ze.uY(h),h=ze.add(h,2),e.x1=f+c*ze.uX(h),e.y1=d+u*ze.uY(h),h=ze.add(h,2),e.x2=f+c*ze.uX(h),e.y2=d+u*ze.uY(h),h=ze.add(h,2),e.x3=f+c*ze.uX(h),e.y3=d+u*ze.uY(h)}else e.x0=n,e.y0=o,e.x1=n+a,e.y1=o,e.x2=n+a,e.y2=o+l,e.x3=n,e.y3=o+l}destroy(e=!1){this._source&&e&&(this._source.destroy(),this._source=null),this._textureMatrix=null,this.destroyed=!0,this.emit("destroy",this),this.removeAllListeners()}update(){this.noFrame&&(this.frame.width=this._source.width,this.frame.height=this._source.height),this.updateUvs(),this.emit("update",this)}get baseTexture(){return be(xe,"Texture.baseTexture is now Texture.source"),this._source}}_e.EMPTY=new _e({label:"EMPTY",source:new Pt({label:"EMPTY"})});_e.EMPTY.destroy=Ta;_e.WHITE=new _e({source:new Hr({resource:new Uint8Array([255,255,255,255]),width:1,height:1,alphaMode:"premultiply-alpha-on-upload",label:"WHITE"}),label:"WHITE"});_e.WHITE.destroy=Ta;function Zc(s,e,t){const{width:i,height:r}=t.orig,n=t.trim;if(n){const o=n.width,a=n.height;s.minX=n.x-e._x*i,s.maxX=s.minX+o,s.minY=n.y-e._y*r,s.maxY=s.minY+a}else s.minX=-e._x*i,s.maxX=s.minX+i,s.minY=-e._y*r,s.maxY=s.minY+r}const dn=new we;class Mt{constructor(e=1/0,t=1/0,i=-1/0,r=-1/0){this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0,this.matrix=dn,this.minX=e,this.minY=t,this.maxX=i,this.maxY=r}isEmpty(){return this.minX>this.maxX||this.minY>this.maxY}get rectangle(){this._rectangle||(this._rectangle=new Pe);const e=this._rectangle;return this.minX>this.maxX||this.minY>this.maxY?(e.x=0,e.y=0,e.width=0,e.height=0):e.copyFromBounds(this),e}clear(){return this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0,this.matrix=dn,this}set(e,t,i,r){this.minX=e,this.minY=t,this.maxX=i,this.maxY=r}addFrame(e,t,i,r,n){n||(n=this.matrix);const o=n.a,a=n.b,l=n.c,h=n.d,c=n.tx,u=n.ty;let f=this.minX,d=this.minY,_=this.maxX,g=this.maxY,m=o*e+l*t+c,p=a*e+h*t+u;m<f&&(f=m),p<d&&(d=p),m>_&&(_=m),p>g&&(g=p),m=o*i+l*t+c,p=a*i+h*t+u,m<f&&(f=m),p<d&&(d=p),m>_&&(_=m),p>g&&(g=p),m=o*e+l*r+c,p=a*e+h*r+u,m<f&&(f=m),p<d&&(d=p),m>_&&(_=m),p>g&&(g=p),m=o*i+l*r+c,p=a*i+h*r+u,m<f&&(f=m),p<d&&(d=p),m>_&&(_=m),p>g&&(g=p),this.minX=f,this.minY=d,this.maxX=_,this.maxY=g}addRect(e,t){this.addFrame(e.x,e.y,e.x+e.width,e.y+e.height,t)}addBounds(e,t){this.addFrame(e.minX,e.minY,e.maxX,e.maxY,t)}addBoundsMask(e){this.minX=this.minX>e.minX?this.minX:e.minX,this.minY=this.minY>e.minY?this.minY:e.minY,this.maxX=this.maxX<e.maxX?this.maxX:e.maxX,this.maxY=this.maxY<e.maxY?this.maxY:e.maxY}applyMatrix(e){const t=this.minX,i=this.minY,r=this.maxX,n=this.maxY,{a:o,b:a,c:l,d:h,tx:c,ty:u}=e;let f=o*t+l*i+c,d=a*t+h*i+u;this.minX=f,this.minY=d,this.maxX=f,this.maxY=d,f=o*r+l*i+c,d=a*r+h*i+u,this.minX=f<this.minX?f:this.minX,this.minY=d<this.minY?d:this.minY,this.maxX=f>this.maxX?f:this.maxX,this.maxY=d>this.maxY?d:this.maxY,f=o*t+l*n+c,d=a*t+h*n+u,this.minX=f<this.minX?f:this.minX,this.minY=d<this.minY?d:this.minY,this.maxX=f>this.maxX?f:this.maxX,this.maxY=d>this.maxY?d:this.maxY,f=o*r+l*n+c,d=a*r+h*n+u,this.minX=f<this.minX?f:this.minX,this.minY=d<this.minY?d:this.minY,this.maxX=f>this.maxX?f:this.maxX,this.maxY=d>this.maxY?d:this.maxY}fit(e){return this.minX<e.left&&(this.minX=e.left),this.maxX>e.right&&(this.maxX=e.right),this.minY<e.top&&(this.minY=e.top),this.maxY>e.bottom&&(this.maxY=e.bottom),this}fitBounds(e,t,i,r){return this.minX<e&&(this.minX=e),this.maxX>t&&(this.maxX=t),this.minY<i&&(this.minY=i),this.maxY>r&&(this.maxY=r),this}pad(e,t=e){return this.minX-=e,this.maxX+=e,this.minY-=t,this.maxY+=t,this}ceil(){return this.minX=Math.floor(this.minX),this.minY=Math.floor(this.minY),this.maxX=Math.ceil(this.maxX),this.maxY=Math.ceil(this.maxY),this}clone(){return new Mt(this.minX,this.minY,this.maxX,this.maxY)}scale(e,t=e){return this.minX*=e,this.minY*=t,this.maxX*=e,this.maxY*=t,this}get x(){return this.minX}set x(e){const t=this.maxX-this.minX;this.minX=e,this.maxX=e+t}get y(){return this.minY}set y(e){const t=this.maxY-this.minY;this.minY=e,this.maxY=e+t}get width(){return this.maxX-this.minX}set width(e){this.maxX=this.minX+e}get height(){return this.maxY-this.minY}set height(e){this.maxY=this.minY+e}get left(){return this.minX}get right(){return this.maxX}get top(){return this.minY}get bottom(){return this.maxY}get isPositive(){return this.maxX-this.minX>0&&this.maxY-this.minY>0}get isValid(){return this.minX+this.minY!==1/0}addVertexData(e,t,i,r){let n=this.minX,o=this.minY,a=this.maxX,l=this.maxY;r||(r=this.matrix);const h=r.a,c=r.b,u=r.c,f=r.d,d=r.tx,_=r.ty;for(let g=t;g<i;g+=2){const m=e[g],p=e[g+1],b=h*m+u*p+d,y=c*m+f*p+_;n=b<n?b:n,o=y<o?y:o,a=b>a?b:a,l=y>l?y:l}this.minX=n,this.minY=o,this.maxX=a,this.maxY=l}containsPoint(e,t){return this.minX<=e&&this.minY<=t&&this.maxX>=e&&this.maxY>=t}toString(){return`[pixi.js:Bounds minX=${this.minX} minY=${this.minY} maxX=${this.maxX} maxY=${this.maxY} width=${this.width} height=${this.height}]`}copyFrom(e){return this.minX=e.minX,this.minY=e.minY,this.maxX=e.maxX,this.maxY=e.maxY,this}}var Qc={grad:.9,turn:360,rad:360/(2*Math.PI)},Ot=function(s){return typeof s=="string"?s.length>0:typeof s=="number"},qe=function(s,e,t){return e===void 0&&(e=0),t===void 0&&(t=Math.pow(10,e)),Math.round(t*s)/t+0},wt=function(s,e,t){return e===void 0&&(e=0),t===void 0&&(t=1),s>t?t:s>e?s:e},Ra=function(s){return(s=isFinite(s)?s%360:0)>0?s:s+360},fn=function(s){return{r:wt(s.r,0,255),g:wt(s.g,0,255),b:wt(s.b,0,255),a:wt(s.a)}},zs=function(s){return{r:qe(s.r),g:qe(s.g),b:qe(s.b),a:qe(s.a,3)}},Jc=/^#([0-9a-f]{3,8})$/i,Yi=function(s){var e=s.toString(16);return e.length<2?"0"+e:e},Ba=function(s){var e=s.r,t=s.g,i=s.b,r=s.a,n=Math.max(e,t,i),o=n-Math.min(e,t,i),a=o?n===e?(t-i)/o:n===t?2+(i-e)/o:4+(e-t)/o:0;return{h:60*(a<0?a+6:a),s:n?o/n*100:0,v:n/255*100,a:r}},Fa=function(s){var e=s.h,t=s.s,i=s.v,r=s.a;e=e/360*6,t/=100,i/=100;var n=Math.floor(e),o=i*(1-t),a=i*(1-(e-n)*t),l=i*(1-(1-e+n)*t),h=n%6;return{r:255*[i,a,o,o,l,i][h],g:255*[l,i,i,a,o,o][h],b:255*[o,o,l,i,i,a][h],a:r}},_n=function(s){return{h:Ra(s.h),s:wt(s.s,0,100),l:wt(s.l,0,100),a:wt(s.a)}},gn=function(s){return{h:qe(s.h),s:qe(s.s),l:qe(s.l),a:qe(s.a,3)}},mn=function(s){return Fa((t=(e=s).s,{h:e.h,s:(t*=((i=e.l)<50?i:100-i)/100)>0?2*t/(i+t)*100:0,v:i+t,a:e.a}));var e,t,i},Ti=function(s){return{h:(e=Ba(s)).h,s:(r=(200-(t=e.s))*(i=e.v)/100)>0&&r<200?t*i/100/(r<=100?r:200-r)*100:0,l:r/2,a:e.a};var e,t,i,r},$c=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s*,\s*([+-]?\d*\.?\d+)%\s*,\s*([+-]?\d*\.?\d+)%\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,eu=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s+([+-]?\d*\.?\d+)%\s+([+-]?\d*\.?\d+)%\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,tu=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,iu=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,dr={string:[[function(s){var e=Jc.exec(s);return e?(s=e[1]).length<=4?{r:parseInt(s[0]+s[0],16),g:parseInt(s[1]+s[1],16),b:parseInt(s[2]+s[2],16),a:s.length===4?qe(parseInt(s[3]+s[3],16)/255,2):1}:s.length===6||s.length===8?{r:parseInt(s.substr(0,2),16),g:parseInt(s.substr(2,2),16),b:parseInt(s.substr(4,2),16),a:s.length===8?qe(parseInt(s.substr(6,2),16)/255,2):1}:null:null},"hex"],[function(s){var e=tu.exec(s)||iu.exec(s);return e?e[2]!==e[4]||e[4]!==e[6]?null:fn({r:Number(e[1])/(e[2]?100/255:1),g:Number(e[3])/(e[4]?100/255:1),b:Number(e[5])/(e[6]?100/255:1),a:e[7]===void 0?1:Number(e[7])/(e[8]?100:1)}):null},"rgb"],[function(s){var e=$c.exec(s)||eu.exec(s);if(!e)return null;var t,i,r=_n({h:(t=e[1],i=e[2],i===void 0&&(i="deg"),Number(t)*(Qc[i]||1)),s:Number(e[3]),l:Number(e[4]),a:e[5]===void 0?1:Number(e[5])/(e[6]?100:1)});return mn(r)},"hsl"]],object:[[function(s){var e=s.r,t=s.g,i=s.b,r=s.a,n=r===void 0?1:r;return Ot(e)&&Ot(t)&&Ot(i)?fn({r:Number(e),g:Number(t),b:Number(i),a:Number(n)}):null},"rgb"],[function(s){var e=s.h,t=s.s,i=s.l,r=s.a,n=r===void 0?1:r;if(!Ot(e)||!Ot(t)||!Ot(i))return null;var o=_n({h:Number(e),s:Number(t),l:Number(i),a:Number(n)});return mn(o)},"hsl"],[function(s){var e=s.h,t=s.s,i=s.v,r=s.a,n=r===void 0?1:r;if(!Ot(e)||!Ot(t)||!Ot(i))return null;var o=function(a){return{h:Ra(a.h),s:wt(a.s,0,100),v:wt(a.v,0,100),a:wt(a.a)}}({h:Number(e),s:Number(t),v:Number(i),a:Number(n)});return Fa(o)},"hsv"]]},pn=function(s,e){for(var t=0;t<e.length;t++){var i=e[t][0](s);if(i)return[i,e[t][1]]}return[null,void 0]},su=function(s){return typeof s=="string"?pn(s.trim(),dr.string):typeof s=="object"&&s!==null?pn(s,dr.object):[null,void 0]},Rs=function(s,e){var t=Ti(s);return{h:t.h,s:wt(t.s+100*e,0,100),l:t.l,a:t.a}},Bs=function(s){return(299*s.r+587*s.g+114*s.b)/1e3/255},bn=function(s,e){var t=Ti(s);return{h:t.h,s:t.s,l:wt(t.l+100*e,0,100),a:t.a}},fr=function(){function s(e){this.parsed=su(e)[0],this.rgba=this.parsed||{r:0,g:0,b:0,a:1}}return s.prototype.isValid=function(){return this.parsed!==null},s.prototype.brightness=function(){return qe(Bs(this.rgba),2)},s.prototype.isDark=function(){return Bs(this.rgba)<.5},s.prototype.isLight=function(){return Bs(this.rgba)>=.5},s.prototype.toHex=function(){return e=zs(this.rgba),t=e.r,i=e.g,r=e.b,o=(n=e.a)<1?Yi(qe(255*n)):"","#"+Yi(t)+Yi(i)+Yi(r)+o;var e,t,i,r,n,o},s.prototype.toRgb=function(){return zs(this.rgba)},s.prototype.toRgbString=function(){return e=zs(this.rgba),t=e.r,i=e.g,r=e.b,(n=e.a)<1?"rgba("+t+", "+i+", "+r+", "+n+")":"rgb("+t+", "+i+", "+r+")";var e,t,i,r,n},s.prototype.toHsl=function(){return gn(Ti(this.rgba))},s.prototype.toHslString=function(){return e=gn(Ti(this.rgba)),t=e.h,i=e.s,r=e.l,(n=e.a)<1?"hsla("+t+", "+i+"%, "+r+"%, "+n+")":"hsl("+t+", "+i+"%, "+r+"%)";var e,t,i,r,n},s.prototype.toHsv=function(){return e=Ba(this.rgba),{h:qe(e.h),s:qe(e.s),v:qe(e.v),a:qe(e.a,3)};var e},s.prototype.invert=function(){return Rt({r:255-(e=this.rgba).r,g:255-e.g,b:255-e.b,a:e.a});var e},s.prototype.saturate=function(e){return e===void 0&&(e=.1),Rt(Rs(this.rgba,e))},s.prototype.desaturate=function(e){return e===void 0&&(e=.1),Rt(Rs(this.rgba,-e))},s.prototype.grayscale=function(){return Rt(Rs(this.rgba,-1))},s.prototype.lighten=function(e){return e===void 0&&(e=.1),Rt(bn(this.rgba,e))},s.prototype.darken=function(e){return e===void 0&&(e=.1),Rt(bn(this.rgba,-e))},s.prototype.rotate=function(e){return e===void 0&&(e=15),this.hue(this.hue()+e)},s.prototype.alpha=function(e){return typeof e=="number"?Rt({r:(t=this.rgba).r,g:t.g,b:t.b,a:e}):qe(this.rgba.a,3);var t},s.prototype.hue=function(e){var t=Ti(this.rgba);return typeof e=="number"?Rt({h:e,s:t.s,l:t.l,a:t.a}):qe(t.h)},s.prototype.isEqual=function(e){return this.toHex()===Rt(e).toHex()},s}(),Rt=function(s){return s instanceof fr?s:new fr(s)},yn=[],ru=function(s){s.forEach(function(e){yn.indexOf(e)<0&&(e(fr,dr),yn.push(e))})};function nu(s,e){var t={white:"#ffffff",bisque:"#ffe4c4",blue:"#0000ff",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",antiquewhite:"#faebd7",aqua:"#00ffff",azure:"#f0ffff",whitesmoke:"#f5f5f5",papayawhip:"#ffefd5",plum:"#dda0dd",blanchedalmond:"#ffebcd",black:"#000000",gold:"#ffd700",goldenrod:"#daa520",gainsboro:"#dcdcdc",cornsilk:"#fff8dc",cornflowerblue:"#6495ed",burlywood:"#deb887",aquamarine:"#7fffd4",beige:"#f5f5dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkkhaki:"#bdb76b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",peachpuff:"#ffdab9",darkmagenta:"#8b008b",darkred:"#8b0000",darkorchid:"#9932cc",darkorange:"#ff8c00",darkslateblue:"#483d8b",gray:"#808080",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",deeppink:"#ff1493",deepskyblue:"#00bfff",wheat:"#f5deb3",firebrick:"#b22222",floralwhite:"#fffaf0",ghostwhite:"#f8f8ff",darkviolet:"#9400d3",magenta:"#ff00ff",green:"#008000",dodgerblue:"#1e90ff",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",blueviolet:"#8a2be2",forestgreen:"#228b22",lawngreen:"#7cfc00",indianred:"#cd5c5c",indigo:"#4b0082",fuchsia:"#ff00ff",brown:"#a52a2a",maroon:"#800000",mediumblue:"#0000cd",lightcoral:"#f08080",darkturquoise:"#00ced1",lightcyan:"#e0ffff",ivory:"#fffff0",lightyellow:"#ffffe0",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",linen:"#faf0e6",mediumaquamarine:"#66cdaa",lemonchiffon:"#fffacd",lime:"#00ff00",khaki:"#f0e68c",mediumseagreen:"#3cb371",limegreen:"#32cd32",mediumspringgreen:"#00fa9a",lightskyblue:"#87cefa",lightblue:"#add8e6",midnightblue:"#191970",lightpink:"#ffb6c1",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",mintcream:"#f5fffa",lightslategray:"#778899",lightslategrey:"#778899",navajowhite:"#ffdead",navy:"#000080",mediumvioletred:"#c71585",powderblue:"#b0e0e6",palegoldenrod:"#eee8aa",oldlace:"#fdf5e6",paleturquoise:"#afeeee",mediumturquoise:"#48d1cc",mediumorchid:"#ba55d3",rebeccapurple:"#663399",lightsteelblue:"#b0c4de",mediumslateblue:"#7b68ee",thistle:"#d8bfd8",tan:"#d2b48c",orchid:"#da70d6",mediumpurple:"#9370db",purple:"#800080",pink:"#ffc0cb",skyblue:"#87ceeb",springgreen:"#00ff7f",palegreen:"#98fb98",red:"#ff0000",yellow:"#ffff00",slateblue:"#6a5acd",lavenderblush:"#fff0f5",peru:"#cd853f",palevioletred:"#db7093",violet:"#ee82ee",teal:"#008080",slategray:"#708090",slategrey:"#708090",aliceblue:"#f0f8ff",darkseagreen:"#8fbc8f",darkolivegreen:"#556b2f",greenyellow:"#adff2f",seagreen:"#2e8b57",seashell:"#fff5ee",tomato:"#ff6347",silver:"#c0c0c0",sienna:"#a0522d",lavender:"#e6e6fa",lightgreen:"#90ee90",orange:"#ffa500",orangered:"#ff4500",steelblue:"#4682b4",royalblue:"#4169e1",turquoise:"#40e0d0",yellowgreen:"#9acd32",salmon:"#fa8072",saddlebrown:"#8b4513",sandybrown:"#f4a460",rosybrown:"#bc8f8f",darksalmon:"#e9967a",lightgoldenrodyellow:"#fafad2",snow:"#fffafa",lightgrey:"#d3d3d3",lightgray:"#d3d3d3",dimgray:"#696969",dimgrey:"#696969",olivedrab:"#6b8e23",olive:"#808000"},i={};for(var r in t)i[t[r]]=r;var n={};s.prototype.toName=function(o){if(!(this.rgba.a||this.rgba.r||this.rgba.g||this.rgba.b))return"transparent";var a,l,h=i[this.toHex()];if(h)return h;if(o!=null&&o.closest){var c=this.toRgb(),u=1/0,f="black";if(!n.length)for(var d in t)n[d]=new s(t[d]).toRgb();for(var _ in t){var g=(a=c,l=n[_],Math.pow(a.r-l.r,2)+Math.pow(a.g-l.g,2)+Math.pow(a.b-l.b,2));g<u&&(u=g,f=_)}return f}},e.string.push([function(o){var a=o.toLowerCase(),l=a==="transparent"?"#0000":t[a];return l?new s(l).toRgb():null},"name"])}ru([nu]);const fi=class Si{constructor(e=16777215){this._value=null,this._components=new Float32Array(4),this._components.fill(1),this._int=16777215,this.value=e}get red(){return this._components[0]}get green(){return this._components[1]}get blue(){return this._components[2]}get alpha(){return this._components[3]}setValue(e){return this.value=e,this}set value(e){if(e instanceof Si)this._value=this._cloneSource(e._value),this._int=e._int,this._components.set(e._components);else{if(e===null)throw new Error("Cannot set Color#value to null");(this._value===null||!this._isSourceEqual(this._value,e))&&(this._value=this._cloneSource(e),this._normalize(this._value))}}get value(){return this._value}_cloneSource(e){return typeof e=="string"||typeof e=="number"||e instanceof Number||e===null?e:Array.isArray(e)||ArrayBuffer.isView(e)?e.slice(0):typeof e=="object"&&e!==null?{...e}:e}_isSourceEqual(e,t){const i=typeof e;if(i!==typeof t)return!1;if(i==="number"||i==="string"||e instanceof Number)return e===t;if(Array.isArray(e)&&Array.isArray(t)||ArrayBuffer.isView(e)&&ArrayBuffer.isView(t))return e.length!==t.length?!1:e.every((n,o)=>n===t[o]);if(e!==null&&t!==null){const n=Object.keys(e),o=Object.keys(t);return n.length!==o.length?!1:n.every(a=>e[a]===t[a])}return e===t}toRgba(){const[e,t,i,r]=this._components;return{r:e,g:t,b:i,a:r}}toRgb(){const[e,t,i]=this._components;return{r:e,g:t,b:i}}toRgbaString(){const[e,t,i]=this.toUint8RgbArray();return`rgba(${e},${t},${i},${this.alpha})`}toUint8RgbArray(e){const[t,i,r]=this._components;return this._arrayRgb||(this._arrayRgb=[]),e||(e=this._arrayRgb),e[0]=Math.round(t*255),e[1]=Math.round(i*255),e[2]=Math.round(r*255),e}toArray(e){this._arrayRgba||(this._arrayRgba=[]),e||(e=this._arrayRgba);const[t,i,r,n]=this._components;return e[0]=t,e[1]=i,e[2]=r,e[3]=n,e}toRgbArray(e){this._arrayRgb||(this._arrayRgb=[]),e||(e=this._arrayRgb);const[t,i,r]=this._components;return e[0]=t,e[1]=i,e[2]=r,e}toNumber(){return this._int}toBgrNumber(){const[e,t,i]=this.toUint8RgbArray();return(i<<16)+(t<<8)+e}toLittleEndianNumber(){const e=this._int;return(e>>16)+(e&65280)+((e&255)<<16)}multiply(e){const[t,i,r,n]=Si._temp.setValue(e)._components;return this._components[0]*=t,this._components[1]*=i,this._components[2]*=r,this._components[3]*=n,this._refreshInt(),this._value=null,this}premultiply(e,t=!0){return t&&(this._components[0]*=e,this._components[1]*=e,this._components[2]*=e),this._components[3]=e,this._refreshInt(),this._value=null,this}toPremultiplied(e,t=!0){if(e===1)return(255<<24)+this._int;if(e===0)return t?0:this._int;let i=this._int>>16&255,r=this._int>>8&255,n=this._int&255;return t&&(i=i*e+.5|0,r=r*e+.5|0,n=n*e+.5|0),(e*255<<24)+(i<<16)+(r<<8)+n}toHex(){const e=this._int.toString(16);return`#${"000000".substring(0,6-e.length)+e}`}toHexa(){const t=Math.round(this._components[3]*255).toString(16);return this.toHex()+"00".substring(0,2-t.length)+t}setAlpha(e){return this._components[3]=this._clamp(e),this}_normalize(e){let t,i,r,n;if((typeof e=="number"||e instanceof Number)&&e>=0&&e<=16777215){const o=e;t=(o>>16&255)/255,i=(o>>8&255)/255,r=(o&255)/255,n=1}else if((Array.isArray(e)||e instanceof Float32Array)&&e.length>=3&&e.length<=4)e=this._clamp(e),[t,i,r,n=1]=e;else if((e instanceof Uint8Array||e instanceof Uint8ClampedArray)&&e.length>=3&&e.length<=4)e=this._clamp(e,0,255),[t,i,r,n=255]=e,t/=255,i/=255,r/=255,n/=255;else if(typeof e=="string"||typeof e=="object"){if(typeof e=="string"){const a=Si.HEX_PATTERN.exec(e);a&&(e=`#${a[2]}`)}const o=Rt(e);o.isValid()&&({r:t,g:i,b:r,a:n}=o.rgba,t/=255,i/=255,r/=255)}if(t!==void 0)this._components[0]=t,this._components[1]=i,this._components[2]=r,this._components[3]=n,this._refreshInt();else throw new Error(`Unable to convert color ${e}`)}_refreshInt(){this._clamp(this._components);const[e,t,i]=this._components;this._int=(e*255<<16)+(t*255<<8)+(i*255|0)}_clamp(e,t=0,i=1){return typeof e=="number"?Math.min(Math.max(e,t),i):(e.forEach((r,n)=>{e[n]=Math.min(Math.max(r,t),i)}),e)}static isColorLike(e){return typeof e=="number"||typeof e=="string"||e instanceof Number||e instanceof Si||Array.isArray(e)||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Float32Array||e.r!==void 0&&e.g!==void 0&&e.b!==void 0||e.r!==void 0&&e.g!==void 0&&e.b!==void 0&&e.a!==void 0||e.h!==void 0&&e.s!==void 0&&e.l!==void 0||e.h!==void 0&&e.s!==void 0&&e.l!==void 0&&e.a!==void 0||e.h!==void 0&&e.s!==void 0&&e.v!==void 0||e.h!==void 0&&e.s!==void 0&&e.v!==void 0&&e.a!==void 0}};fi.shared=new fi;fi._temp=new fi;fi.HEX_PATTERN=/^(#|0x)?(([a-f0-9]{3}){1,2}([a-f0-9]{2})?)$/i;let Ne=fi;const ou={cullArea:null,cullable:!1,cullableChildren:!0};class Vr{constructor(e,t){this._pool=[],this._count=0,this._index=0,this._classType=e,t&&this.prepopulate(t)}prepopulate(e){for(let t=0;t<e;t++)this._pool[this._index++]=new this._classType;this._count+=e}get(e){var i;let t;return this._index>0?t=this._pool[--this._index]:t=new this._classType,(i=t.init)==null||i.call(t,e),t}return(e){var t;(t=e.reset)==null||t.call(e),this._pool[this._index++]=e}get totalSize(){return this._count}get totalFree(){return this._index}get totalUsed(){return this._count-this._index}clear(){this._pool.length=0,this._index=0}}class au{constructor(){this._poolsByClass=new Map}prepopulate(e,t){this.getPool(e).prepopulate(t)}get(e,t){return this.getPool(e).get(t)}return(e){this.getPool(e.constructor).return(e)}getPool(e){return this._poolsByClass.has(e)||this._poolsByClass.set(e,new Vr(e)),this._poolsByClass.get(e)}stats(){const e={};return this._poolsByClass.forEach(t=>{const i=e[t._classType.name]?t._classType.name+t._classType.ID:t._classType.name;e[i]={free:t.totalFree,used:t.totalUsed,size:t.totalSize}}),e}}const Gt=new au,lu={get isCachedAsTexture(){var s;return!!((s=this.renderGroup)!=null&&s.isCachedAsTexture)},cacheAsTexture(s){typeof s=="boolean"&&s===!1?this.disableRenderGroup():(this.enableRenderGroup(),this.renderGroup.enableCacheAsTexture(s===!0?{}:s))},updateCacheTexture(){var s;(s=this.renderGroup)==null||s.updateCacheTexture()},get cacheAsBitmap(){return this.isCachedAsTexture},set cacheAsBitmap(s){be("v8.6.0","cacheAsBitmap is deprecated, use cacheAsTexture instead."),this.cacheAsTexture(s)}};function hu(s,e,t){const i=s.length;let r;if(e>=i||t===0)return;t=e+t>i?i-e:t;const n=i-t;for(r=e;r<n;++r)s[r]=s[r+t];s.length=n}const cu={allowChildren:!0,removeChildren(s=0,e){const t=e??this.children.length,i=t-s,r=[];if(i>0&&i<=t){for(let o=t-1;o>=s;o--){const a=this.children[o];a&&(r.push(a),a.parent=null)}hu(this.children,s,t);const n=this.renderGroup||this.parentRenderGroup;n&&n.removeChildren(r);for(let o=0;o<r.length;++o)this.emit("childRemoved",r[o],this,o),r[o].emit("removed",this);return r}else if(i===0&&this.children.length===0)return r;throw new RangeError("removeChildren: numeric values are outside the acceptable range.")},removeChildAt(s){const e=this.getChildAt(s);return this.removeChild(e)},getChildAt(s){if(s<0||s>=this.children.length)throw new Error(`getChildAt: Index (${s}) does not exist.`);return this.children[s]},setChildIndex(s,e){if(e<0||e>=this.children.length)throw new Error(`The index ${e} supplied is out of bounds ${this.children.length}`);this.getChildIndex(s),this.addChildAt(s,e)},getChildIndex(s){const e=this.children.indexOf(s);if(e===-1)throw new Error("The supplied Container must be a child of the caller");return e},addChildAt(s,e){this.allowChildren||be(xe,"addChildAt: Only Containers will be allowed to add children in v8.0.0");const{children:t}=this;if(e<0||e>t.length)throw new Error(`${s}addChildAt: The index ${e} supplied is out of bounds ${t.length}`);if(s.parent){const r=s.parent.children.indexOf(s);if(s.parent===this&&r===e)return s;r!==-1&&s.parent.children.splice(r,1)}e===t.length?t.push(s):t.splice(e,0,s),s.parent=this,s.didChange=!0,s._updateFlags=15;const i=this.renderGroup||this.parentRenderGroup;return i&&i.addChild(s),this.sortableChildren&&(this.sortDirty=!0),this.emit("childAdded",s,this,e),s.emit("added",this),s},swapChildren(s,e){if(s===e)return;const t=this.getChildIndex(s),i=this.getChildIndex(e);this.children[t]=e,this.children[i]=s;const r=this.renderGroup||this.parentRenderGroup;r&&(r.structureDidChange=!0),this._didContainerChangeTick++},removeFromParent(){var s;(s=this.parent)==null||s.removeChild(this)},reparentChild(...s){return s.length===1?this.reparentChildAt(s[0],this.children.length):(s.forEach(e=>this.reparentChildAt(e,this.children.length)),s[0])},reparentChildAt(s,e){if(s.parent===this)return this.setChildIndex(s,e),s;const t=s.worldTransform.clone();s.removeFromParent(),this.addChildAt(s,e);const i=this.worldTransform.clone();return i.invert(),t.prepend(i),s.setFromMatrix(t),s}},uu={collectRenderables(s,e,t){this.parentRenderLayer&&this.parentRenderLayer!==t||this.globalDisplayStatus<7||!this.includeInBuild||(this.sortableChildren&&this.sortChildren(),this.isSimple?this.collectRenderablesSimple(s,e,t):this.renderGroup?e.renderPipes.renderGroup.addRenderGroup(this.renderGroup,s):this.collectRenderablesWithEffects(s,e,t))},collectRenderablesSimple(s,e,t){const i=this.children,r=i.length;for(let n=0;n<r;n++)i[n].collectRenderables(s,e,t)},collectRenderablesWithEffects(s,e,t){const{renderPipes:i}=e;for(let r=0;r<this.effects.length;r++){const n=this.effects[r];i[n.pipe].push(n,this,s)}this.collectRenderablesSimple(s,e,t);for(let r=this.effects.length-1;r>=0;r--){const n=this.effects[r];i[n.pipe].pop(n,this,s)}}};class wn{constructor(){this.pipe="filter",this.priority=1}destroy(){for(let e=0;e<this.filters.length;e++)this.filters[e].destroy();this.filters=null,this.filterArea=null}}class du{constructor(){this._effectClasses=[],this._tests=[],this._initialized=!1}init(){this._initialized||(this._initialized=!0,this._effectClasses.forEach(e=>{this.add({test:e.test,maskClass:e})}))}add(e){this._tests.push(e)}getMaskEffect(e){this._initialized||this.init();for(let t=0;t<this._tests.length;t++){const i=this._tests[t];if(i.test(e))return Gt.get(i.maskClass,e)}return e}returnMaskEffect(e){Gt.return(e)}}const _r=new du;rt.handleByList(J.MaskEffect,_r._effectClasses);const fu={_maskEffect:null,_maskOptions:{inverse:!1},_filterEffect:null,effects:[],_markStructureAsChanged(){const s=this.renderGroup||this.parentRenderGroup;s&&(s.structureDidChange=!0)},addEffect(s){this.effects.indexOf(s)===-1&&(this.effects.push(s),this.effects.sort((t,i)=>t.priority-i.priority),this._markStructureAsChanged(),this._updateIsSimple())},removeEffect(s){const e=this.effects.indexOf(s);e!==-1&&(this.effects.splice(e,1),this._markStructureAsChanged(),this._updateIsSimple())},set mask(s){const e=this._maskEffect;(e==null?void 0:e.mask)!==s&&(e&&(this.removeEffect(e),_r.returnMaskEffect(e),this._maskEffect=null),s!=null&&(this._maskEffect=_r.getMaskEffect(s),this.addEffect(this._maskEffect)))},setMask(s){this._maskOptions={...this._maskOptions,...s},s.mask&&(this.mask=s.mask),this._markStructureAsChanged()},get mask(){var s;return(s=this._maskEffect)==null?void 0:s.mask},set filters(s){var n;!Array.isArray(s)&&s&&(s=[s]);const e=this._filterEffect||(this._filterEffect=new wn);s=s;const t=(s==null?void 0:s.length)>0,i=((n=e.filters)==null?void 0:n.length)>0,r=t!==i;s=Array.isArray(s)?s.slice(0):s,e.filters=Object.freeze(s),r&&(t?this.addEffect(e):(this.removeEffect(e),e.filters=s??null))},get filters(){var s;return(s=this._filterEffect)==null?void 0:s.filters},set filterArea(s){this._filterEffect||(this._filterEffect=new wn),this._filterEffect.filterArea=s},get filterArea(){var s;return(s=this._filterEffect)==null?void 0:s.filterArea}},_u={label:null,get name(){return be(xe,"Container.name property has been removed, use Container.label instead"),this.label},set name(s){be(xe,"Container.name property has been removed, use Container.label instead"),this.label=s},getChildByName(s,e=!1){return this.getChildByLabel(s,e)},getChildByLabel(s,e=!1){const t=this.children;for(let i=0;i<t.length;i++){const r=t[i];if(r.label===s||s instanceof RegExp&&s.test(r.label))return r}if(e)for(let i=0;i<t.length;i++){const n=t[i].getChildByLabel(s,!0);if(n)return n}return null},getChildrenByLabel(s,e=!1,t=[]){const i=this.children;for(let r=0;r<i.length;r++){const n=i[r];(n.label===s||s instanceof RegExp&&s.test(n.label))&&t.push(n)}if(e)for(let r=0;r<i.length;r++)i[r].getChildrenByLabel(s,!0,t);return t}},st=new Vr(we),Dt=new Vr(Mt),gu=new we,mu={getFastGlobalBounds(s,e){e||(e=new Mt),e.clear(),this._getGlobalBoundsRecursive(!!s,e,this.parentRenderLayer),e.isValid||e.set(0,0,0,0);const t=this.renderGroup||this.parentRenderGroup;return e.applyMatrix(t.worldTransform),e},_getGlobalBoundsRecursive(s,e,t){let i=e;if(s&&this.parentRenderLayer!==t||this.localDisplayStatus!==7||!this.measurable)return;const r=!!this.effects.length;if((this.renderGroup||r)&&(i=Dt.get().clear()),this.boundsArea)e.addRect(this.boundsArea,this.worldTransform);else{if(this.renderPipeId){const o=this.bounds;i.addFrame(o.minX,o.minY,o.maxX,o.maxY,this.groupTransform)}const n=this.children;for(let o=0;o<n.length;o++)n[o]._getGlobalBoundsRecursive(s,i,t)}if(r){let n=!1;const o=this.renderGroup||this.parentRenderGroup;for(let a=0;a<this.effects.length;a++)this.effects[a].addBounds&&(n||(n=!0,i.applyMatrix(o.worldTransform)),this.effects[a].addBounds(i,!0));n&&(i.applyMatrix(o.worldTransform.copyTo(gu).invert()),e.addBounds(i,this.relativeGroupTransform)),e.addBounds(i),Dt.return(i)}else this.renderGroup&&(e.addBounds(i,this.relativeGroupTransform),Dt.return(i))}};function La(s,e,t){t.clear();let i,r;return s.parent?e?i=s.parent.worldTransform:(r=st.get().identity(),i=Xr(s,r)):i=we.IDENTITY,Oa(s,t,i,e),r&&st.return(r),t.isValid||t.set(0,0,0,0),t}function Oa(s,e,t,i){var a,l;if(!s.visible||!s.measurable)return;let r;i?r=s.worldTransform:(s.updateLocalTransform(),r=st.get(),r.appendFrom(s.localTransform,t));const n=e,o=!!s.effects.length;if(o&&(e=Dt.get().clear()),s.boundsArea)e.addRect(s.boundsArea,r);else{s.bounds&&(e.matrix=r,e.addBounds(s.bounds));for(let h=0;h<s.children.length;h++)Oa(s.children[h],e,r,i)}if(o){for(let h=0;h<s.effects.length;h++)(l=(a=s.effects[h]).addBounds)==null||l.call(a,e);n.addBounds(e,we.IDENTITY),Dt.return(e)}i||st.return(r)}function Xr(s,e){const t=s.parent;return t&&(Xr(t,e),t.updateLocalTransform(),e.append(t.localTransform)),e}function Ga(s,e){if(s===16777215||!e)return e;if(e===16777215||!s)return s;const t=s>>16&255,i=s>>8&255,r=s&255,n=e>>16&255,o=e>>8&255,a=e&255,l=t*n/255|0,h=i*o/255|0,c=r*a/255|0;return(l<<16)+(h<<8)+c}const xn=16777215;function vn(s,e){return s===xn?e:e===xn?s:Ga(s,e)}function ss(s){return((s&255)<<16)+(s&65280)+(s>>16&255)}const pu={getGlobalAlpha(s){if(s)return this.renderGroup?this.renderGroup.worldAlpha:this.parentRenderGroup?this.parentRenderGroup.worldAlpha*this.alpha:this.alpha;let e=this.alpha,t=this.parent;for(;t;)e*=t.alpha,t=t.parent;return e},getGlobalTransform(s,e){if(e)return s.copyFrom(this.worldTransform);this.updateLocalTransform();const t=Xr(this,st.get().identity());return s.appendFrom(this.localTransform,t),st.return(t),s},getGlobalTint(s){if(s)return this.renderGroup?ss(this.renderGroup.worldColor):this.parentRenderGroup?ss(vn(this.localColor,this.parentRenderGroup.worldColor)):this.tint;let e=this.localColor,t=this.parent;for(;t;)e=vn(e,t.localColor),t=t.parent;return ss(e)}};let Fs=0;const kn=500;function Ve(...s){Fs!==kn&&(Fs++,Fs===kn?console.warn("PixiJS Warning: too many warnings, no more warnings will be reported to the console by PixiJS."):console.warn("PixiJS Warning: ",...s))}function Da(s,e,t){return e.clear(),t||(t=we.IDENTITY),Na(s,e,t,s,!0),e.isValid||e.set(0,0,0,0),e}function Na(s,e,t,i,r){var l,h;let n;if(r)n=st.get(),n=t.copyTo(n);else{if(!s.visible||!s.measurable)return;s.updateLocalTransform();const c=s.localTransform;n=st.get(),n.appendFrom(c,t)}const o=e,a=!!s.effects.length;if(a&&(e=Dt.get().clear()),s.boundsArea)e.addRect(s.boundsArea,n);else{s.renderPipeId&&(e.matrix=n,e.addBounds(s.bounds));const c=s.children;for(let u=0;u<c.length;u++)Na(c[u],e,n,i,!1)}if(a){for(let c=0;c<s.effects.length;c++)(h=(l=s.effects[c]).addLocalBounds)==null||h.call(l,e,i);o.addBounds(e,we.IDENTITY),Dt.return(e)}st.return(n)}function Ua(s,e){const t=s.children;for(let i=0;i<t.length;i++){const r=t[i],n=r.uid,o=(r._didViewChangeTick&65535)<<16|r._didContainerChangeTick&65535,a=e.index;(e.data[a]!==n||e.data[a+1]!==o)&&(e.data[e.index]=n,e.data[e.index+1]=o,e.didChange=!0),e.index=a+2,r.children.length&&Ua(r,e)}return e.didChange}const bu=new we,yu={_localBoundsCacheId:-1,_localBoundsCacheData:null,_setWidth(s,e){const t=Math.sign(this.scale.x)||1;e!==0?this.scale.x=s/e*t:this.scale.x=t},_setHeight(s,e){const t=Math.sign(this.scale.y)||1;e!==0?this.scale.y=s/e*t:this.scale.y=t},getLocalBounds(){this._localBoundsCacheData||(this._localBoundsCacheData={data:[],index:1,didChange:!1,localBounds:new Mt});const s=this._localBoundsCacheData;return s.index=1,s.didChange=!1,s.data[0]!==this._didViewChangeTick&&(s.didChange=!0,s.data[0]=this._didViewChangeTick),Ua(this,s),s.didChange&&Da(this,s.localBounds,bu),s.localBounds},getBounds(s,e){return La(this,s,e||new Mt)}},wu={_onRender:null,set onRender(s){const e=this.renderGroup||this.parentRenderGroup;if(!s){this._onRender&&(e==null||e.removeOnRender(this)),this._onRender=null;return}this._onRender||e==null||e.addOnRender(this),this._onRender=s},get onRender(){return this._onRender}},xu={_zIndex:0,sortDirty:!1,sortableChildren:!1,get zIndex(){return this._zIndex},set zIndex(s){this._zIndex!==s&&(this._zIndex=s,this.depthOfChildModified())},depthOfChildModified(){this.parent&&(this.parent.sortableChildren=!0,this.parent.sortDirty=!0),this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0)},sortChildren(){this.sortDirty&&(this.sortDirty=!1,this.children.sort(vu))}};function vu(s,e){return s._zIndex-e._zIndex}const ku={getGlobalPosition(s=new fe,e=!1){return this.parent?this.parent.toGlobal(this._position,s,e):(s.x=this._position.x,s.y=this._position.y),s},toGlobal(s,e,t=!1){const i=this.getGlobalTransform(st.get(),t);return e=i.apply(s,e),st.return(i),e},toLocal(s,e,t,i){e&&(s=e.toGlobal(s,t,i));const r=this.getGlobalTransform(st.get(),i);return t=r.applyInverse(s,t),st.return(r),t}};class Wa{constructor(){this.uid=He("instructionSet"),this.instructions=[],this.instructionSize=0,this.renderables=[],this.gcTick=0}reset(){this.instructionSize=0}add(e){this.instructions[this.instructionSize++]=e}log(){this.instructions.length=this.instructionSize,console.table(this.instructions,["type","action"])}}let Au=0;class Su{constructor(e){this._poolKeyHash=Object.create(null),this._texturePool={},this.textureOptions=e||{},this.enableFullScreen=!1}createTexture(e,t,i){const r=new Pt({...this.textureOptions,width:e,height:t,resolution:1,antialias:i,autoGarbageCollect:!1});return new _e({source:r,label:`texturePool_${Au++}`})}getOptimalTexture(e,t,i=1,r){let n=Math.ceil(e*i-1e-6),o=Math.ceil(t*i-1e-6);n=us(n),o=us(o);const a=(n<<17)+(o<<1)+(r?1:0);this._texturePool[a]||(this._texturePool[a]=[]);let l=this._texturePool[a].pop();return l||(l=this.createTexture(n,o,r)),l.source._resolution=i,l.source.width=n/i,l.source.height=o/i,l.source.pixelWidth=n,l.source.pixelHeight=o,l.frame.x=0,l.frame.y=0,l.frame.width=e,l.frame.height=t,l.updateUvs(),this._poolKeyHash[l.uid]=a,l}getSameSizeTexture(e,t=!1){const i=e.source;return this.getOptimalTexture(e.width,e.height,i._resolution,t)}returnTexture(e){const t=this._poolKeyHash[e.uid];this._texturePool[t].push(e)}clear(e){if(e=e!==!1,e)for(const t in this._texturePool){const i=this._texturePool[t];if(i)for(let r=0;r<i.length;r++)i[r].destroy(!0)}this._texturePool={}}}const Ei=new Su;class Cu{constructor(){this.renderPipeId="renderGroup",this.root=null,this.canBundle=!1,this.renderGroupParent=null,this.renderGroupChildren=[],this.worldTransform=new we,this.worldColorAlpha=4294967295,this.worldColor=16777215,this.worldAlpha=1,this.childrenToUpdate=Object.create(null),this.updateTick=0,this.gcTick=0,this.childrenRenderablesToUpdate={list:[],index:0},this.structureDidChange=!0,this.instructionSet=new Wa,this._onRenderContainers=[],this.textureNeedsUpdate=!0,this.isCachedAsTexture=!1,this._matrixDirty=7}init(e){this.root=e,e._onRender&&this.addOnRender(e),e.didChange=!0;const t=e.children;for(let i=0;i<t.length;i++){const r=t[i];r._updateFlags=15,this.addChild(r)}}enableCacheAsTexture(e={}){this.textureOptions=e,this.isCachedAsTexture=!0,this.textureNeedsUpdate=!0}disableCacheAsTexture(){this.isCachedAsTexture=!1,this.texture&&(Ei.returnTexture(this.texture),this.texture=null)}updateCacheTexture(){this.textureNeedsUpdate=!0}reset(){this.renderGroupChildren.length=0;for(const e in this.childrenToUpdate){const t=this.childrenToUpdate[e];t.list.fill(null),t.index=0}this.childrenRenderablesToUpdate.index=0,this.childrenRenderablesToUpdate.list.fill(null),this.root=null,this.updateTick=0,this.structureDidChange=!0,this._onRenderContainers.length=0,this.renderGroupParent=null,this.disableCacheAsTexture()}get localTransform(){return this.root.localTransform}addRenderGroupChild(e){e.renderGroupParent&&e.renderGroupParent._removeRenderGroupChild(e),e.renderGroupParent=this,this.renderGroupChildren.push(e)}_removeRenderGroupChild(e){const t=this.renderGroupChildren.indexOf(e);t>-1&&this.renderGroupChildren.splice(t,1),e.renderGroupParent=null}addChild(e){if(this.structureDidChange=!0,e.parentRenderGroup=this,e.updateTick=-1,e.parent===this.root?e.relativeRenderGroupDepth=1:e.relativeRenderGroupDepth=e.parent.relativeRenderGroupDepth+1,e.didChange=!0,this.onChildUpdate(e),e.renderGroup){this.addRenderGroupChild(e.renderGroup);return}e._onRender&&this.addOnRender(e);const t=e.children;for(let i=0;i<t.length;i++)this.addChild(t[i])}removeChild(e){if(this.structureDidChange=!0,e._onRender&&(e.renderGroup||this.removeOnRender(e)),e.parentRenderGroup=null,e.renderGroup){this._removeRenderGroupChild(e.renderGroup);return}const t=e.children;for(let i=0;i<t.length;i++)this.removeChild(t[i])}removeChildren(e){for(let t=0;t<e.length;t++)this.removeChild(e[t])}onChildUpdate(e){let t=this.childrenToUpdate[e.relativeRenderGroupDepth];t||(t=this.childrenToUpdate[e.relativeRenderGroupDepth]={index:0,list:[]}),t.list[t.index++]=e}updateRenderable(e){e.globalDisplayStatus<7||(this.instructionSet.renderPipes[e.renderPipeId].updateRenderable(e),e.didViewUpdate=!1)}onChildViewUpdate(e){this.childrenRenderablesToUpdate.list[this.childrenRenderablesToUpdate.index++]=e}get isRenderable(){return this.root.localDisplayStatus===7&&this.worldAlpha>0}addOnRender(e){this._onRenderContainers.push(e)}removeOnRender(e){this._onRenderContainers.splice(this._onRenderContainers.indexOf(e),1)}runOnRender(e){for(let t=0;t<this._onRenderContainers.length;t++)this._onRenderContainers[t]._onRender(e)}destroy(){this.disableCacheAsTexture(),this.renderGroupParent=null,this.root=null,this.childrenRenderablesToUpdate=null,this.childrenToUpdate=null,this.renderGroupChildren=null,this._onRenderContainers=null,this.instructionSet=null}getChildren(e=[]){const t=this.root.children;for(let i=0;i<t.length;i++)this._getChildren(t[i],e);return e}_getChildren(e,t=[]){if(t.push(e),e.renderGroup)return t;const i=e.children;for(let r=0;r<i.length;r++)this._getChildren(i[r],t);return t}invalidateMatrices(){this._matrixDirty=7}get inverseWorldTransform(){return this._matrixDirty&1?(this._matrixDirty&=-2,this._inverseWorldTransform||(this._inverseWorldTransform=new we),this._inverseWorldTransform.copyFrom(this.worldTransform).invert()):this._inverseWorldTransform}get textureOffsetInverseTransform(){return this._matrixDirty&2?(this._matrixDirty&=-3,this._textureOffsetInverseTransform||(this._textureOffsetInverseTransform=new we),this._textureOffsetInverseTransform.copyFrom(this.inverseWorldTransform).translate(-this._textureBounds.x,-this._textureBounds.y)):this._textureOffsetInverseTransform}get inverseParentTextureTransform(){if(!(this._matrixDirty&4))return this._inverseParentTextureTransform;this._matrixDirty&=-5;const e=this._parentCacheAsTextureRenderGroup;return e?(this._inverseParentTextureTransform||(this._inverseParentTextureTransform=new we),this._inverseParentTextureTransform.copyFrom(this.worldTransform).prepend(e.inverseWorldTransform).translate(-e._textureBounds.x,-e._textureBounds.y)):this.worldTransform}get cacheToLocalTransform(){return this._parentCacheAsTextureRenderGroup?this._parentCacheAsTextureRenderGroup.textureOffsetInverseTransform:null}}function Mu(s,e,t={}){for(const i in e)!t[i]&&e[i]!==void 0&&(s[i]=e[i])}const Ls=new dt(null),Os=new dt(null),Gs=new dt(null,1,1),An=1,Tu=2,Ds=4;class re extends Tt{constructor(e={}){var t,i;super(),this.uid=He("renderable"),this._updateFlags=15,this.renderGroup=null,this.parentRenderGroup=null,this.parentRenderGroupIndex=0,this.didChange=!1,this.didViewUpdate=!1,this.relativeRenderGroupDepth=0,this.children=[],this.parent=null,this.includeInBuild=!0,this.measurable=!0,this.isSimple=!0,this.updateTick=-1,this.localTransform=new we,this.relativeGroupTransform=new we,this.groupTransform=this.relativeGroupTransform,this.destroyed=!1,this._position=new dt(this,0,0),this._scale=Gs,this._pivot=Os,this._skew=Ls,this._cx=1,this._sx=0,this._cy=0,this._sy=1,this._rotation=0,this.localColor=16777215,this.localAlpha=1,this.groupAlpha=1,this.groupColor=16777215,this.groupColorAlpha=4294967295,this.localBlendMode="inherit",this.groupBlendMode="normal",this.localDisplayStatus=7,this.globalDisplayStatus=7,this._didContainerChangeTick=0,this._didViewChangeTick=0,this._didLocalTransformChangeId=-1,this.effects=[],Mu(this,e,{children:!0,parent:!0,effects:!0}),(t=e.children)==null||t.forEach(r=>this.addChild(r)),(i=e.parent)==null||i.addChild(this)}static mixin(e){Object.defineProperties(re.prototype,Object.getOwnPropertyDescriptors(e))}set _didChangeId(e){this._didViewChangeTick=e>>12&4095,this._didContainerChangeTick=e&4095}get _didChangeId(){return this._didContainerChangeTick&4095|(this._didViewChangeTick&4095)<<12}addChild(...e){if(this.allowChildren||be(xe,"addChild: Only Containers will be allowed to add children in v8.0.0"),e.length>1){for(let r=0;r<e.length;r++)this.addChild(e[r]);return e[0]}const t=e[0],i=this.renderGroup||this.parentRenderGroup;return t.parent===this?(this.children.splice(this.children.indexOf(t),1),this.children.push(t),i&&(i.structureDidChange=!0),t):(t.parent&&t.parent.removeChild(t),this.children.push(t),this.sortableChildren&&(this.sortDirty=!0),t.parent=this,t.didChange=!0,t._updateFlags=15,i&&i.addChild(t),this.emit("childAdded",t,this,this.children.length-1),t.emit("added",this),this._didViewChangeTick++,t._zIndex!==0&&t.depthOfChildModified(),t)}removeChild(...e){if(e.length>1){for(let r=0;r<e.length;r++)this.removeChild(e[r]);return e[0]}const t=e[0],i=this.children.indexOf(t);return i>-1&&(this._didViewChangeTick++,this.children.splice(i,1),this.renderGroup?this.renderGroup.removeChild(t):this.parentRenderGroup&&this.parentRenderGroup.removeChild(t),t.parentRenderLayer&&t.parentRenderLayer.detach(t),t.parent=null,this.emit("childRemoved",t,this,i),t.emit("removed",this)),t}_onUpdate(e){e&&e===this._skew&&this._updateSkew(),this._didContainerChangeTick++,!this.didChange&&(this.didChange=!0,this.parentRenderGroup&&this.parentRenderGroup.onChildUpdate(this))}set isRenderGroup(e){!!this.renderGroup!==e&&(e?this.enableRenderGroup():this.disableRenderGroup())}get isRenderGroup(){return!!this.renderGroup}enableRenderGroup(){if(this.renderGroup)return;const e=this.parentRenderGroup;e==null||e.removeChild(this),this.renderGroup=Gt.get(Cu,this),this.groupTransform=we.IDENTITY,e==null||e.addChild(this),this._updateIsSimple()}disableRenderGroup(){if(!this.renderGroup)return;const e=this.parentRenderGroup;e==null||e.removeChild(this),Gt.return(this.renderGroup),this.renderGroup=null,this.groupTransform=this.relativeGroupTransform,e==null||e.addChild(this),this._updateIsSimple()}_updateIsSimple(){this.isSimple=!this.renderGroup&&this.effects.length===0}get worldTransform(){return this._worldTransform||(this._worldTransform=new we),this.renderGroup?this._worldTransform.copyFrom(this.renderGroup.worldTransform):this.parentRenderGroup&&this._worldTransform.appendFrom(this.relativeGroupTransform,this.parentRenderGroup.worldTransform),this._worldTransform}get x(){return this._position.x}set x(e){this._position.x=e}get y(){return this._position.y}set y(e){this._position.y=e}get position(){return this._position}set position(e){this._position.copyFrom(e)}get rotation(){return this._rotation}set rotation(e){this._rotation!==e&&(this._rotation=e,this._onUpdate(this._skew))}get angle(){return this.rotation*Nc}set angle(e){this.rotation=e*Uc}get pivot(){return this._pivot===Os&&(this._pivot=new dt(this,0,0)),this._pivot}set pivot(e){this._pivot===Os&&(this._pivot=new dt(this,0,0)),typeof e=="number"?this._pivot.set(e):this._pivot.copyFrom(e)}get skew(){return this._skew===Ls&&(this._skew=new dt(this,0,0)),this._skew}set skew(e){this._skew===Ls&&(this._skew=new dt(this,0,0)),this._skew.copyFrom(e)}get scale(){return this._scale===Gs&&(this._scale=new dt(this,1,1)),this._scale}set scale(e){this._scale===Gs&&(this._scale=new dt(this,0,0)),typeof e=="number"?this._scale.set(e):this._scale.copyFrom(e)}get width(){return Math.abs(this.scale.x*this.getLocalBounds().width)}set width(e){const t=this.getLocalBounds().width;this._setWidth(e,t)}get height(){return Math.abs(this.scale.y*this.getLocalBounds().height)}set height(e){const t=this.getLocalBounds().height;this._setHeight(e,t)}getSize(e){e||(e={});const t=this.getLocalBounds();return e.width=Math.abs(this.scale.x*t.width),e.height=Math.abs(this.scale.y*t.height),e}setSize(e,t){const i=this.getLocalBounds();typeof e=="object"?(t=e.height??e.width,e=e.width):t??(t=e),e!==void 0&&this._setWidth(e,i.width),t!==void 0&&this._setHeight(t,i.height)}_updateSkew(){const e=this._rotation,t=this._skew;this._cx=Math.cos(e+t._y),this._sx=Math.sin(e+t._y),this._cy=-Math.sin(e-t._x),this._sy=Math.cos(e-t._x)}updateTransform(e){return this.position.set(typeof e.x=="number"?e.x:this.position.x,typeof e.y=="number"?e.y:this.position.y),this.scale.set(typeof e.scaleX=="number"?e.scaleX||1:this.scale.x,typeof e.scaleY=="number"?e.scaleY||1:this.scale.y),this.rotation=typeof e.rotation=="number"?e.rotation:this.rotation,this.skew.set(typeof e.skewX=="number"?e.skewX:this.skew.x,typeof e.skewY=="number"?e.skewY:this.skew.y),this.pivot.set(typeof e.pivotX=="number"?e.pivotX:this.pivot.x,typeof e.pivotY=="number"?e.pivotY:this.pivot.y),this}setFromMatrix(e){e.decompose(this)}updateLocalTransform(){const e=this._didContainerChangeTick;if(this._didLocalTransformChangeId===e)return;this._didLocalTransformChangeId=e;const t=this.localTransform,i=this._scale,r=this._pivot,n=this._position,o=i._x,a=i._y,l=r._x,h=r._y;t.a=this._cx*o,t.b=this._sx*o,t.c=this._cy*a,t.d=this._sy*a,t.tx=n._x-(l*t.a+h*t.c),t.ty=n._y-(l*t.b+h*t.d)}set alpha(e){e!==this.localAlpha&&(this.localAlpha=e,this._updateFlags|=An,this._onUpdate())}get alpha(){return this.localAlpha}set tint(e){const i=Ne.shared.setValue(e??16777215).toBgrNumber();i!==this.localColor&&(this.localColor=i,this._updateFlags|=An,this._onUpdate())}get tint(){return ss(this.localColor)}set blendMode(e){this.localBlendMode!==e&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=Tu,this.localBlendMode=e,this._onUpdate())}get blendMode(){return this.localBlendMode}get visible(){return!!(this.localDisplayStatus&2)}set visible(e){const t=e?2:0;(this.localDisplayStatus&2)!==t&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=Ds,this.localDisplayStatus^=2,this._onUpdate())}get culled(){return!(this.localDisplayStatus&4)}set culled(e){const t=e?0:4;(this.localDisplayStatus&4)!==t&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=Ds,this.localDisplayStatus^=4,this._onUpdate())}get renderable(){return!!(this.localDisplayStatus&1)}set renderable(e){const t=e?1:0;(this.localDisplayStatus&1)!==t&&(this._updateFlags|=Ds,this.localDisplayStatus^=1,this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._onUpdate())}get isRenderable(){return this.localDisplayStatus===7&&this.groupAlpha>0}destroy(e=!1){var r;if(this.destroyed)return;this.destroyed=!0;let t;if(this.children.length&&(t=this.removeChildren(0,this.children.length)),this.removeFromParent(),this.parent=null,this._maskEffect=null,this._filterEffect=null,this.effects=null,this._position=null,this._scale=null,this._pivot=null,this._skew=null,this.emit("destroyed",this),this.removeAllListeners(),(typeof e=="boolean"?e:e==null?void 0:e.children)&&t)for(let n=0;n<t.length;++n)t[n].destroy(e);(r=this.renderGroup)==null||r.destroy(),this.renderGroup=null}}re.mixin(cu);re.mixin(mu);re.mixin(ku);re.mixin(wu);re.mixin(yu);re.mixin(fu);re.mixin(_u);re.mixin(xu);re.mixin(ou);re.mixin(lu);re.mixin(pu);re.mixin(uu);class Ha extends re{constructor(){super(...arguments),this.canBundle=!0,this.allowChildren=!1,this._roundPixels=0,this._lastUsed=-1,this._bounds=new Mt(0,1,0,0),this._boundsDirty=!0}get bounds(){return this._boundsDirty?(this.updateBounds(),this._boundsDirty=!1,this._bounds):this._bounds}get roundPixels(){return!!this._roundPixels}set roundPixels(e){this._roundPixels=e?1:0}containsPoint(e){const t=this.bounds,{x:i,y:r}=e;return i>=t.minX&&i<=t.maxX&&r>=t.minY&&r<=t.maxY}onViewUpdate(){if(this._didViewChangeTick++,this._boundsDirty=!0,this.didViewUpdate)return;this.didViewUpdate=!0;const e=this.renderGroup||this.parentRenderGroup;e&&e.onChildViewUpdate(this)}destroy(e){super.destroy(e),this._bounds=null}collectRenderablesSimple(e,t,i){const{renderPipes:r,renderableGC:n}=t;r.blendMode.setBlendMode(this,this.groupBlendMode,e),r[this.renderPipeId].addRenderable(this,e),n.addRenderable(this),this.didViewUpdate=!1;const a=this.children,l=a.length;for(let h=0;h<l;h++)a[h].collectRenderables(e,t,i)}}class ve extends Ha{constructor(e=_e.EMPTY){e instanceof _e&&(e={texture:e});const{texture:t=_e.EMPTY,anchor:i,roundPixels:r,width:n,height:o,...a}=e;super({label:"Sprite",...a}),this.renderPipeId="sprite",this.batched=!0,this._visualBounds={minX:0,maxX:1,minY:0,maxY:0},this._anchor=new dt({_onUpdate:()=>{this.onViewUpdate()}}),i?this.anchor=i:t.defaultAnchor&&(this.anchor=t.defaultAnchor),this.texture=t,this.allowChildren=!1,this.roundPixels=r??!1,n!==void 0&&(this.width=n),o!==void 0&&(this.height=o)}static from(e,t=!1){return e instanceof _e?new ve(e):new ve(_e.from(e,t))}set texture(e){e||(e=_e.EMPTY);const t=this._texture;t!==e&&(t&&t.dynamic&&t.off("update",this.onViewUpdate,this),e.dynamic&&e.on("update",this.onViewUpdate,this),this._texture=e,this._width&&this._setWidth(this._width,this._texture.orig.width),this._height&&this._setHeight(this._height,this._texture.orig.height),this.onViewUpdate())}get texture(){return this._texture}get visualBounds(){return Zc(this._visualBounds,this._anchor,this._texture),this._visualBounds}get sourceBounds(){return be("8.6.1","Sprite.sourceBounds is deprecated, use visualBounds instead."),this.visualBounds}updateBounds(){const e=this._anchor,t=this._texture,i=this._bounds,{width:r,height:n}=t.orig;i.minX=-e._x*r,i.maxX=i.minX+r,i.minY=-e._y*n,i.maxY=i.minY+n}destroy(e=!1){if(super.destroy(e),typeof e=="boolean"?e:e==null?void 0:e.texture){const i=typeof e=="boolean"?e:e==null?void 0:e.textureSource;this._texture.destroy(i)}this._texture=null,this._visualBounds=null,this._bounds=null,this._anchor=null}get anchor(){return this._anchor}set anchor(e){typeof e=="number"?this._anchor.set(e):this._anchor.copyFrom(e)}get width(){return Math.abs(this.scale.x)*this._texture.orig.width}set width(e){this._setWidth(e,this._texture.orig.width),this._width=e}get height(){return Math.abs(this.scale.y)*this._texture.orig.height}set height(e){this._setHeight(e,this._texture.orig.height),this._height=e}getSize(e){return e||(e={}),e.width=Math.abs(this.scale.x)*this._texture.orig.width,e.height=Math.abs(this.scale.y)*this._texture.orig.height,e}setSize(e,t){typeof e=="object"?(t=e.height??e.width,e=e.width):t??(t=e),e!==void 0&&this._setWidth(e,this._texture.orig.width),t!==void 0&&this._setHeight(t,this._texture.orig.height)}}const Pu=new Mt;function Va(s,e,t){const i=Pu;s.measurable=!0,La(s,t,i),e.addBoundsMask(i),s.measurable=!1}function Xa(s,e,t){const i=Dt.get();s.measurable=!0;const r=st.get().identity(),n=Ya(s,t,r);Da(s,i,n),s.measurable=!1,e.addBoundsMask(i),st.return(r),Dt.return(i)}function Ya(s,e,t){return s?(s!==e&&(Ya(s.parent,e,t),s.updateLocalTransform(),t.append(s.localTransform)),t):(Ve("Mask bounds, renderable is not inside the root container"),t)}class ja{constructor(e){this.priority=0,this.inverse=!1,this.pipe="alphaMask",e!=null&&e.mask&&this.init(e.mask)}init(e){this.mask=e,this.renderMaskToTexture=!(e instanceof ve),this.mask.renderable=this.renderMaskToTexture,this.mask.includeInBuild=!this.renderMaskToTexture,this.mask.measurable=!1}reset(){this.mask.measurable=!0,this.mask=null}addBounds(e,t){this.inverse||Va(this.mask,e,t)}addLocalBounds(e,t){Xa(this.mask,e,t)}containsPoint(e,t){const i=this.mask;return t(i,e)}destroy(){this.reset()}static test(e){return e instanceof ve}}ja.extension=J.MaskEffect;class qa{constructor(e){this.priority=0,this.pipe="colorMask",e!=null&&e.mask&&this.init(e.mask)}init(e){this.mask=e}destroy(){}static test(e){return typeof e=="number"}}qa.extension=J.MaskEffect;class Ka{constructor(e){this.priority=0,this.pipe="stencilMask",e!=null&&e.mask&&this.init(e.mask)}init(e){this.mask=e,this.mask.includeInBuild=!1,this.mask.measurable=!1}reset(){this.mask.measurable=!0,this.mask.includeInBuild=!0,this.mask=null}addBounds(e,t){Va(this.mask,e,t)}addLocalBounds(e,t){Xa(this.mask,e,t)}containsPoint(e,t){const i=this.mask;return t(i,e)}destroy(){this.reset()}static test(e){return e instanceof re}}Ka.extension=J.MaskEffect;const Iu={createCanvas:(s,e)=>{const t=document.createElement("canvas");return t.width=s,t.height=e,t},getCanvasRenderingContext2D:()=>CanvasRenderingContext2D,getWebGLRenderingContext:()=>WebGLRenderingContext,getNavigator:()=>navigator,getBaseUrl:()=>document.baseURI??window.location.href,getFontFaceSet:()=>document.fonts,fetch:(s,e)=>fetch(s,e),parseXML:s=>new DOMParser().parseFromString(s,"text/xml")};let Sn=Iu;const Le={get(){return Sn},set(s){Sn=s}};class Za extends Pt{constructor(e){e.resource||(e.resource=Le.get().createCanvas()),e.width||(e.width=e.resource.width,e.autoDensity||(e.width/=e.resolution)),e.height||(e.height=e.resource.height,e.autoDensity||(e.height/=e.resolution)),super(e),this.uploadMethodId="image",this.autoDensity=e.autoDensity,this.resizeCanvas(),this.transparent=!!e.transparent}resizeCanvas(){this.autoDensity&&(this.resource.style.width=`${this.width}px`,this.resource.style.height=`${this.height}px`),(this.resource.width!==this.pixelWidth||this.resource.height!==this.pixelHeight)&&(this.resource.width=this.pixelWidth,this.resource.height=this.pixelHeight)}resize(e=this.width,t=this.height,i=this._resolution){const r=super.resize(e,t,i);return r&&this.resizeCanvas(),r}static test(e){return globalThis.HTMLCanvasElement&&e instanceof HTMLCanvasElement||globalThis.OffscreenCanvas&&e instanceof OffscreenCanvas}get context2D(){return this._context2D||(this._context2D=this.resource.getContext("2d"))}}Za.extension=J.TextureSource;class pi extends Pt{constructor(e){if(e.resource&&globalThis.HTMLImageElement&&e.resource instanceof HTMLImageElement){const t=Le.get().createCanvas(e.resource.width,e.resource.height);t.getContext("2d").drawImage(e.resource,0,0,e.resource.width,e.resource.height),e.resource=t,Ve("ImageSource: Image element passed, converting to canvas. Use CanvasSource instead.")}super(e),this.uploadMethodId="image",this.autoGarbageCollect=!0}static test(e){return globalThis.HTMLImageElement&&e instanceof HTMLImageElement||typeof ImageBitmap<"u"&&e instanceof ImageBitmap||globalThis.VideoFrame&&e instanceof VideoFrame}}pi.extension=J.TextureSource;var gr=(s=>(s[s.INTERACTION=50]="INTERACTION",s[s.HIGH=25]="HIGH",s[s.NORMAL=0]="NORMAL",s[s.LOW=-25]="LOW",s[s.UTILITY=-50]="UTILITY",s))(gr||{});class Ns{constructor(e,t=null,i=0,r=!1){this.next=null,this.previous=null,this._destroyed=!1,this._fn=e,this._context=t,this.priority=i,this._once=r}match(e,t=null){return this._fn===e&&this._context===t}emit(e){this._fn&&(this._context?this._fn.call(this._context,e):this._fn(e));const t=this.next;return this._once&&this.destroy(!0),this._destroyed&&(this.next=null),t}connect(e){this.previous=e,e.next&&(e.next.previous=this),this.next=e.next,e.next=this}destroy(e=!1){this._destroyed=!0,this._fn=null,this._context=null,this.previous&&(this.previous.next=this.next),this.next&&(this.next.previous=this.previous);const t=this.next;return this.next=e?null:t,this.previous=null,t}}const Qa=class ut{constructor(){this.autoStart=!1,this.deltaTime=1,this.lastTime=-1,this.speed=1,this.started=!1,this._requestId=null,this._maxElapsedMS=100,this._minElapsedMS=0,this._protected=!1,this._lastFrame=-1,this._head=new Ns(null,null,1/0),this.deltaMS=1/ut.targetFPMS,this.elapsedMS=1/ut.targetFPMS,this._tick=e=>{this._requestId=null,this.started&&(this.update(e),this.started&&this._requestId===null&&this._head.next&&(this._requestId=requestAnimationFrame(this._tick)))}}_requestIfNeeded(){this._requestId===null&&this._head.next&&(this.lastTime=performance.now(),this._lastFrame=this.lastTime,this._requestId=requestAnimationFrame(this._tick))}_cancelIfNeeded(){this._requestId!==null&&(cancelAnimationFrame(this._requestId),this._requestId=null)}_startIfPossible(){this.started?this._requestIfNeeded():this.autoStart&&this.start()}add(e,t,i=gr.NORMAL){return this._addListener(new Ns(e,t,i))}addOnce(e,t,i=gr.NORMAL){return this._addListener(new Ns(e,t,i,!0))}_addListener(e){let t=this._head.next,i=this._head;if(!t)e.connect(i);else{for(;t;){if(e.priority>t.priority){e.connect(i);break}i=t,t=t.next}e.previous||e.connect(i)}return this._startIfPossible(),this}remove(e,t){let i=this._head.next;for(;i;)i.match(e,t)?i=i.destroy():i=i.next;return this._head.next||this._cancelIfNeeded(),this}get count(){if(!this._head)return 0;let e=0,t=this._head;for(;t=t.next;)e++;return e}start(){this.started||(this.started=!0,this._requestIfNeeded())}stop(){this.started&&(this.started=!1,this._cancelIfNeeded())}destroy(){if(!this._protected){this.stop();let e=this._head.next;for(;e;)e=e.destroy(!0);this._head.destroy(),this._head=null}}update(e=performance.now()){let t;if(e>this.lastTime){if(t=this.elapsedMS=e-this.lastTime,t>this._maxElapsedMS&&(t=this._maxElapsedMS),t*=this.speed,this._minElapsedMS){const n=e-this._lastFrame|0;if(n<this._minElapsedMS)return;this._lastFrame=e-n%this._minElapsedMS}this.deltaMS=t,this.deltaTime=this.deltaMS*ut.targetFPMS;const i=this._head;let r=i.next;for(;r;)r=r.emit(this);i.next||this._cancelIfNeeded()}else this.deltaTime=this.deltaMS=this.elapsedMS=0;this.lastTime=e}get FPS(){return 1e3/this.elapsedMS}get minFPS(){return 1e3/this._maxElapsedMS}set minFPS(e){const t=Math.min(this.maxFPS,e),i=Math.min(Math.max(0,t)/1e3,ut.targetFPMS);this._maxElapsedMS=1/i}get maxFPS(){return this._minElapsedMS?Math.round(1e3/this._minElapsedMS):0}set maxFPS(e){if(e===0)this._minElapsedMS=0;else{const t=Math.max(this.minFPS,e);this._minElapsedMS=1/(t/1e3)}}static get shared(){if(!ut._shared){const e=ut._shared=new ut;e.autoStart=!0,e._protected=!0}return ut._shared}static get system(){if(!ut._system){const e=ut._system=new ut;e.autoStart=!0,e._protected=!0}return ut._system}};Qa.targetFPMS=.06;let ji=Qa,Us;async function Ja(){return Us??(Us=(async()=>{var o;const e=document.createElement("canvas").getContext("webgl");if(!e)return"premultiply-alpha-on-upload";const t=await new Promise(a=>{const l=document.createElement("video");l.onloadeddata=()=>a(l),l.onerror=()=>a(null),l.autoplay=!1,l.crossOrigin="anonymous",l.preload="auto",l.src="data:video/webm;base64,GkXfo59ChoEBQveBAULygQRC84EIQoKEd2VibUKHgQJChYECGFOAZwEAAAAAAAHTEU2bdLpNu4tTq4QVSalmU6yBoU27i1OrhBZUrmtTrIHGTbuMU6uEElTDZ1OsggEXTbuMU6uEHFO7a1OsggG97AEAAAAAAABZAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVSalmoCrXsYMPQkBNgIRMYXZmV0GETGF2ZkSJiEBEAAAAAAAAFlSua8yuAQAAAAAAAEPXgQFzxYgAAAAAAAAAAZyBACK1nIN1bmSIgQCGhVZfVlA5g4EBI+ODhAJiWgDglLCBArqBApqBAlPAgQFVsIRVuYEBElTDZ9Vzc9JjwItjxYgAAAAAAAAAAWfInEWjh0VOQ09ERVJEh49MYXZjIGxpYnZweC12cDlnyKJFo4hEVVJBVElPTkSHlDAwOjAwOjAwLjA0MDAwMDAwMAAAH0O2dcfngQCgwqGggQAAAIJJg0IAABAAFgA4JBwYSgAAICAAEb///4r+AAB1oZ2mm+6BAaWWgkmDQgAAEAAWADgkHBhKAAAgIABIQBxTu2uRu4+zgQC3iveBAfGCAXHwgQM=",l.load()});if(!t)return"premultiply-alpha-on-upload";const i=e.createTexture();e.bindTexture(e.TEXTURE_2D,i);const r=e.createFramebuffer();e.bindFramebuffer(e.FRAMEBUFFER,r),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,i,0),e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),e.pixelStorei(e.UNPACK_COLORSPACE_CONVERSION_WEBGL,e.NONE),e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,t);const n=new Uint8Array(4);return e.readPixels(0,0,1,1,e.RGBA,e.UNSIGNED_BYTE,n),e.deleteFramebuffer(r),e.deleteTexture(i),(o=e.getExtension("WEBGL_lose_context"))==null||o.loseContext(),n[0]<=n[3]?"premultiplied-alpha":"premultiply-alpha-on-upload"})()),Us}const bs=class $a extends Pt{constructor(e){super(e),this.isReady=!1,this.uploadMethodId="video",e={...$a.defaultOptions,...e},this._autoUpdate=!0,this._isConnectedToTicker=!1,this._updateFPS=e.updateFPS||0,this._msToNextUpdate=0,this.autoPlay=e.autoPlay!==!1,this.alphaMode=e.alphaMode??"premultiply-alpha-on-upload",this._videoFrameRequestCallback=this._videoFrameRequestCallback.bind(this),this._videoFrameRequestCallbackHandle=null,this._load=null,this._resolve=null,this._reject=null,this._onCanPlay=this._onCanPlay.bind(this),this._onCanPlayThrough=this._onCanPlayThrough.bind(this),this._onError=this._onError.bind(this),this._onPlayStart=this._onPlayStart.bind(this),this._onPlayStop=this._onPlayStop.bind(this),this._onSeeked=this._onSeeked.bind(this),e.autoLoad!==!1&&this.load()}updateFrame(){if(!this.destroyed){if(this._updateFPS){const e=ji.shared.elapsedMS*this.resource.playbackRate;this._msToNextUpdate=Math.floor(this._msToNextUpdate-e)}(!this._updateFPS||this._msToNextUpdate<=0)&&(this._msToNextUpdate=this._updateFPS?Math.floor(1e3/this._updateFPS):0),this.isValid&&this.update()}}_videoFrameRequestCallback(){this.updateFrame(),this.destroyed?this._videoFrameRequestCallbackHandle=null:this._videoFrameRequestCallbackHandle=this.resource.requestVideoFrameCallback(this._videoFrameRequestCallback)}get isValid(){return!!this.resource.videoWidth&&!!this.resource.videoHeight}async load(){if(this._load)return this._load;const e=this.resource,t=this.options;return(e.readyState===e.HAVE_ENOUGH_DATA||e.readyState===e.HAVE_FUTURE_DATA)&&e.width&&e.height&&(e.complete=!0),e.addEventListener("play",this._onPlayStart),e.addEventListener("pause",this._onPlayStop),e.addEventListener("seeked",this._onSeeked),this._isSourceReady()?this._mediaReady():(t.preload||e.addEventListener("canplay",this._onCanPlay),e.addEventListener("canplaythrough",this._onCanPlayThrough),e.addEventListener("error",this._onError,!0)),this.alphaMode=await Ja(),this._load=new Promise((i,r)=>{this.isValid?i(this):(this._resolve=i,this._reject=r,t.preloadTimeoutMs!==void 0&&(this._preloadTimeout=setTimeout(()=>{this._onError(new ErrorEvent(`Preload exceeded timeout of ${t.preloadTimeoutMs}ms`))})),e.load())}),this._load}_onError(e){this.resource.removeEventListener("error",this._onError,!0),this.emit("error",e),this._reject&&(this._reject(e),this._reject=null,this._resolve=null)}_isSourcePlaying(){const e=this.resource;return!e.paused&&!e.ended}_isSourceReady(){return this.resource.readyState>2}_onPlayStart(){this.isValid||this._mediaReady(),this._configureAutoUpdate()}_onPlayStop(){this._configureAutoUpdate()}_onSeeked(){this._autoUpdate&&!this._isSourcePlaying()&&(this._msToNextUpdate=0,this.updateFrame(),this._msToNextUpdate=0)}_onCanPlay(){this.resource.removeEventListener("canplay",this._onCanPlay),this._mediaReady()}_onCanPlayThrough(){this.resource.removeEventListener("canplaythrough",this._onCanPlay),this._preloadTimeout&&(clearTimeout(this._preloadTimeout),this._preloadTimeout=void 0),this._mediaReady()}_mediaReady(){const e=this.resource;this.isValid&&(this.isReady=!0,this.resize(e.videoWidth,e.videoHeight)),this._msToNextUpdate=0,this.updateFrame(),this._msToNextUpdate=0,this._resolve&&(this._resolve(this),this._resolve=null,this._reject=null),this._isSourcePlaying()?this._onPlayStart():this.autoPlay&&this.resource.play()}destroy(){this._configureAutoUpdate();const e=this.resource;e&&(e.removeEventListener("play",this._onPlayStart),e.removeEventListener("pause",this._onPlayStop),e.removeEventListener("seeked",this._onSeeked),e.removeEventListener("canplay",this._onCanPlay),e.removeEventListener("canplaythrough",this._onCanPlayThrough),e.removeEventListener("error",this._onError,!0),e.pause(),e.src="",e.load()),super.destroy()}get autoUpdate(){return this._autoUpdate}set autoUpdate(e){e!==this._autoUpdate&&(this._autoUpdate=e,this._configureAutoUpdate())}get updateFPS(){return this._updateFPS}set updateFPS(e){e!==this._updateFPS&&(this._updateFPS=e,this._configureAutoUpdate())}_configureAutoUpdate(){this._autoUpdate&&this._isSourcePlaying()?!this._updateFPS&&this.resource.requestVideoFrameCallback?(this._isConnectedToTicker&&(ji.shared.remove(this.updateFrame,this),this._isConnectedToTicker=!1,this._msToNextUpdate=0),this._videoFrameRequestCallbackHandle===null&&(this._videoFrameRequestCallbackHandle=this.resource.requestVideoFrameCallback(this._videoFrameRequestCallback))):(this._videoFrameRequestCallbackHandle!==null&&(this.resource.cancelVideoFrameCallback(this._videoFrameRequestCallbackHandle),this._videoFrameRequestCallbackHandle=null),this._isConnectedToTicker||(ji.shared.add(this.updateFrame,this),this._isConnectedToTicker=!0,this._msToNextUpdate=0)):(this._videoFrameRequestCallbackHandle!==null&&(this.resource.cancelVideoFrameCallback(this._videoFrameRequestCallbackHandle),this._videoFrameRequestCallbackHandle=null),this._isConnectedToTicker&&(ji.shared.remove(this.updateFrame,this),this._isConnectedToTicker=!1,this._msToNextUpdate=0))}static test(e){return globalThis.HTMLVideoElement&&e instanceof HTMLVideoElement}};bs.extension=J.TextureSource;bs.defaultOptions={...Pt.defaultOptions,autoLoad:!0,autoPlay:!0,updateFPS:0,crossorigin:!0,loop:!1,muted:!0,playsinline:!0,preload:!1};bs.MIME_TYPES={ogv:"video/ogg",mov:"video/quicktime",m4v:"video/mp4"};let rs=bs;const At=(s,e,t=!1)=>(Array.isArray(s)||(s=[s]),e?s.map(i=>typeof i=="string"||t?e(i):i):s);class Eu{constructor(){this._parsers=[],this._cache=new Map,this._cacheMap=new Map}reset(){this._cacheMap.clear(),this._cache.clear()}has(e){return this._cache.has(e)}get(e){const t=this._cache.get(e);return t||Ve(`[Assets] Asset id ${e} was not found in the Cache`),t}set(e,t){const i=At(e);let r;for(let l=0;l<this.parsers.length;l++){const h=this.parsers[l];if(h.test(t)){r=h.getCacheableAssets(i,t);break}}const n=new Map(Object.entries(r||{}));r||i.forEach(l=>{n.set(l,t)});const o=[...n.keys()],a={cacheKeys:o,keys:i};i.forEach(l=>{this._cacheMap.set(l,a)}),o.forEach(l=>{const h=r?r[l]:t;this._cache.has(l)&&this._cache.get(l)!==h&&Ve("[Cache] already has key:",l),this._cache.set(l,n.get(l))})}remove(e){if(!this._cacheMap.has(e)){Ve(`[Assets] Asset id ${e} was not found in the Cache`);return}const t=this._cacheMap.get(e);t.cacheKeys.forEach(r=>{this._cache.delete(r)}),t.keys.forEach(r=>{this._cacheMap.delete(r)})}get parsers(){return this._parsers}}const Be=new Eu,mr=[];rt.handleByList(J.TextureSource,mr);function el(s={}){const e=s&&s.resource,t=e?s.resource:s,i=e?s:{resource:s};for(let r=0;r<mr.length;r++){const n=mr[r];if(n.test(t))return new n(i)}throw new Error(`Could not find a source type for resource: ${i.resource}`)}function zu(s={},e=!1){const t=s&&s.resource,i=t?s.resource:s,r=t?s:{resource:s};if(!e&&Be.has(i))return Be.get(i);const n=new _e({source:el(r)});return n.on("destroy",()=>{Be.has(i)&&Be.remove(i)}),e||Be.set(i,n),n}function Ru(s,e=!1){return typeof s=="string"?Be.get(s):s instanceof Pt?new _e({source:s}):zu(s,e)}_e.from=Ru;Pt.from=el;rt.add(ja,qa,Ka,rs,pi,Za,Hr);var Xt=(s=>(s[s.Low=0]="Low",s[s.Normal=1]="Normal",s[s.High=2]="High",s))(Xt||{});function kt(s){if(typeof s!="string")throw new TypeError(`Path must be a string. Received ${JSON.stringify(s)}`)}function xi(s){return s.split("?")[0].split("#")[0]}function Bu(s){return s.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function Fu(s,e,t){return s.replace(new RegExp(Bu(e),"g"),t)}function Lu(s,e){let t="",i=0,r=-1,n=0,o=-1;for(let a=0;a<=s.length;++a){if(a<s.length)o=s.charCodeAt(a);else{if(o===47)break;o=47}if(o===47){if(!(r===a-1||n===1))if(r!==a-1&&n===2){if(t.length<2||i!==2||t.charCodeAt(t.length-1)!==46||t.charCodeAt(t.length-2)!==46){if(t.length>2){const l=t.lastIndexOf("/");if(l!==t.length-1){l===-1?(t="",i=0):(t=t.slice(0,l),i=t.length-1-t.lastIndexOf("/")),r=a,n=0;continue}}else if(t.length===2||t.length===1){t="",i=0,r=a,n=0;continue}}}else t.length>0?t+=`/${s.slice(r+1,a)}`:t=s.slice(r+1,a),i=a-r-1;r=a,n=0}else o===46&&n!==-1?++n:n=-1}return t}const _t={toPosix(s){return Fu(s,"\\","/")},isUrl(s){return/^https?:/.test(this.toPosix(s))},isDataUrl(s){return/^data:([a-z]+\/[a-z0-9-+.]+(;[a-z0-9-.!#$%*+.{}|~`]+=[a-z0-9-.!#$%*+.{}()_|~`]+)*)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s<>]*?)$/i.test(s)},isBlobUrl(s){return s.startsWith("blob:")},hasProtocol(s){return/^[^/:]+:/.test(this.toPosix(s))},getProtocol(s){kt(s),s=this.toPosix(s);const e=/^file:\/\/\//.exec(s);if(e)return e[0];const t=/^[^/:]+:\/{0,2}/.exec(s);return t?t[0]:""},toAbsolute(s,e,t){if(kt(s),this.isDataUrl(s)||this.isBlobUrl(s))return s;const i=xi(this.toPosix(e??Le.get().getBaseUrl())),r=xi(this.toPosix(t??this.rootname(i)));return s=this.toPosix(s),s.startsWith("/")?_t.join(r,s.slice(1)):this.isAbsolute(s)?s:this.join(i,s)},normalize(s){if(kt(s),s.length===0)return".";if(this.isDataUrl(s)||this.isBlobUrl(s))return s;s=this.toPosix(s);let e="";const t=s.startsWith("/");this.hasProtocol(s)&&(e=this.rootname(s),s=s.slice(e.length));const i=s.endsWith("/");return s=Lu(s),s.length>0&&i&&(s+="/"),t?`/${s}`:e+s},isAbsolute(s){return kt(s),s=this.toPosix(s),this.hasProtocol(s)?!0:s.startsWith("/")},join(...s){if(s.length===0)return".";let e;for(let t=0;t<s.length;++t){const i=s[t];if(kt(i),i.length>0)if(e===void 0)e=i;else{const r=s[t-1]??"";this.joinExtensions.includes(this.extname(r).toLowerCase())?e+=`/../${i}`:e+=`/${i}`}}return e===void 0?".":this.normalize(e)},dirname(s){if(kt(s),s.length===0)return".";s=this.toPosix(s);let e=s.charCodeAt(0);const t=e===47;let i=-1,r=!0;const n=this.getProtocol(s),o=s;s=s.slice(n.length);for(let a=s.length-1;a>=1;--a)if(e=s.charCodeAt(a),e===47){if(!r){i=a;break}}else r=!1;return i===-1?t?"/":this.isUrl(o)?n+s:n:t&&i===1?"//":n+s.slice(0,i)},rootname(s){kt(s),s=this.toPosix(s);let e="";if(s.startsWith("/")?e="/":e=this.getProtocol(s),this.isUrl(s)){const t=s.indexOf("/",e.length);t!==-1?e=s.slice(0,t):e=s,e.endsWith("/")||(e+="/")}return e},basename(s,e){kt(s),e&&kt(e),s=xi(this.toPosix(s));let t=0,i=-1,r=!0,n;if(e!==void 0&&e.length>0&&e.length<=s.length){if(e.length===s.length&&e===s)return"";let o=e.length-1,a=-1;for(n=s.length-1;n>=0;--n){const l=s.charCodeAt(n);if(l===47){if(!r){t=n+1;break}}else a===-1&&(r=!1,a=n+1),o>=0&&(l===e.charCodeAt(o)?--o===-1&&(i=n):(o=-1,i=a))}return t===i?i=a:i===-1&&(i=s.length),s.slice(t,i)}for(n=s.length-1;n>=0;--n)if(s.charCodeAt(n)===47){if(!r){t=n+1;break}}else i===-1&&(r=!1,i=n+1);return i===-1?"":s.slice(t,i)},extname(s){kt(s),s=xi(this.toPosix(s));let e=-1,t=0,i=-1,r=!0,n=0;for(let o=s.length-1;o>=0;--o){const a=s.charCodeAt(o);if(a===47){if(!r){t=o+1;break}continue}i===-1&&(r=!1,i=o+1),a===46?e===-1?e=o:n!==1&&(n=1):e!==-1&&(n=-1)}return e===-1||i===-1||n===0||n===1&&e===i-1&&e===t+1?"":s.slice(e,i)},parse(s){kt(s);const e={root:"",dir:"",base:"",ext:"",name:""};if(s.length===0)return e;s=xi(this.toPosix(s));let t=s.charCodeAt(0);const i=this.isAbsolute(s);let r;e.root=this.rootname(s),i||this.hasProtocol(s)?r=1:r=0;let n=-1,o=0,a=-1,l=!0,h=s.length-1,c=0;for(;h>=r;--h){if(t=s.charCodeAt(h),t===47){if(!l){o=h+1;break}continue}a===-1&&(l=!1,a=h+1),t===46?n===-1?n=h:c!==1&&(c=1):n!==-1&&(c=-1)}return n===-1||a===-1||c===0||c===1&&n===a-1&&n===o+1?a!==-1&&(o===0&&i?e.base=e.name=s.slice(1,a):e.base=e.name=s.slice(o,a)):(o===0&&i?(e.name=s.slice(1,n),e.base=s.slice(1,a)):(e.name=s.slice(o,n),e.base=s.slice(o,a)),e.ext=s.slice(n,a)),e.dir=this.dirname(s),e},sep:"/",delimiter:":",joinExtensions:[".html"]};function tl(s,e,t,i,r){const n=e[t];for(let o=0;o<n.length;o++){const a=n[o];t<e.length-1?tl(s.replace(i[t],a),e,t+1,i,r):r.push(s.replace(i[t],a))}}function Ou(s){const e=/\{(.*?)\}/g,t=s.match(e),i=[];if(t){const r=[];t.forEach(n=>{const o=n.substring(1,n.length-1).split(",");r.push(o)}),tl(s,r,0,t,i)}else i.push(s);return i}const ds=s=>!Array.isArray(s);class bi{constructor(){this._defaultBundleIdentifierOptions={connector:"-",createBundleAssetId:(e,t)=>`${e}${this._bundleIdConnector}${t}`,extractAssetIdFromBundle:(e,t)=>t.replace(`${e}${this._bundleIdConnector}`,"")},this._bundleIdConnector=this._defaultBundleIdentifierOptions.connector,this._createBundleAssetId=this._defaultBundleIdentifierOptions.createBundleAssetId,this._extractAssetIdFromBundle=this._defaultBundleIdentifierOptions.extractAssetIdFromBundle,this._assetMap={},this._preferredOrder=[],this._parsers=[],this._resolverHash={},this._bundles={}}setBundleIdentifier(e){if(this._bundleIdConnector=e.connector??this._bundleIdConnector,this._createBundleAssetId=e.createBundleAssetId??this._createBundleAssetId,this._extractAssetIdFromBundle=e.extractAssetIdFromBundle??this._extractAssetIdFromBundle,this._extractAssetIdFromBundle("foo",this._createBundleAssetId("foo","bar"))!=="bar")throw new Error("[Resolver] GenerateBundleAssetId are not working correctly")}prefer(...e){e.forEach(t=>{this._preferredOrder.push(t),t.priority||(t.priority=Object.keys(t.params))}),this._resolverHash={}}set basePath(e){this._basePath=e}get basePath(){return this._basePath}set rootPath(e){this._rootPath=e}get rootPath(){return this._rootPath}get parsers(){return this._parsers}reset(){this.setBundleIdentifier(this._defaultBundleIdentifierOptions),this._assetMap={},this._preferredOrder=[],this._resolverHash={},this._rootPath=null,this._basePath=null,this._manifest=null,this._bundles={},this._defaultSearchParams=null}setDefaultSearchParams(e){if(typeof e=="string")this._defaultSearchParams=e;else{const t=e;this._defaultSearchParams=Object.keys(t).map(i=>`${encodeURIComponent(i)}=${encodeURIComponent(t[i])}`).join("&")}}getAlias(e){const{alias:t,src:i}=e;return At(t||i,n=>typeof n=="string"?n:Array.isArray(n)?n.map(o=>(o==null?void 0:o.src)??o):n!=null&&n.src?n.src:n,!0)}addManifest(e){this._manifest&&Ve("[Resolver] Manifest already exists, this will be overwritten"),this._manifest=e,e.bundles.forEach(t=>{this.addBundle(t.name,t.assets)})}addBundle(e,t){const i=[];let r=t;Array.isArray(t)||(r=Object.entries(t).map(([n,o])=>typeof o=="string"||Array.isArray(o)?{alias:n,src:o}:{alias:n,...o})),r.forEach(n=>{const o=n.src,a=n.alias;let l;if(typeof a=="string"){const h=this._createBundleAssetId(e,a);i.push(h),l=[a,h]}else{const h=a.map(c=>this._createBundleAssetId(e,c));i.push(...h),l=[...a,...h]}this.add({...n,alias:l,src:o})}),this._bundles[e]=i}add(e){const t=[];Array.isArray(e)?t.push(...e):t.push(e);let i;i=n=>{this.hasKey(n)&&Ve(`[Resolver] already has key: ${n} overwriting`)},At(t).forEach(n=>{const{src:o}=n;let{data:a,format:l,loadParser:h}=n;const c=At(o).map(d=>typeof d=="string"?Ou(d):Array.isArray(d)?d:[d]),u=this.getAlias(n);Array.isArray(u)?u.forEach(i):i(u);const f=[];c.forEach(d=>{d.forEach(_=>{let g={};if(typeof _!="object"){g.src=_;for(let m=0;m<this._parsers.length;m++){const p=this._parsers[m];if(p.test(_)){g=p.parse(_);break}}}else a=_.data??a,l=_.format??l,h=_.loadParser??h,g={...g,..._};if(!u)throw new Error(`[Resolver] alias is undefined for this asset: ${g.src}`);g=this._buildResolvedAsset(g,{aliases:u,data:a,format:l,loadParser:h}),f.push(g)})}),u.forEach(d=>{this._assetMap[d]=f})})}resolveBundle(e){const t=ds(e);e=At(e);const i={};return e.forEach(r=>{const n=this._bundles[r];if(n){const o=this.resolve(n),a={};for(const l in o){const h=o[l];a[this._extractAssetIdFromBundle(r,l)]=h}i[r]=a}}),t?i[e[0]]:i}resolveUrl(e){const t=this.resolve(e);if(typeof e!="string"){const i={};for(const r in t)i[r]=t[r].src;return i}return t.src}resolve(e){const t=ds(e);e=At(e);const i={};return e.forEach(r=>{if(!this._resolverHash[r])if(this._assetMap[r]){let n=this._assetMap[r];const o=this._getPreferredOrder(n);o==null||o.priority.forEach(a=>{o.params[a].forEach(l=>{const h=n.filter(c=>c[a]?c[a]===l:!1);h.length&&(n=h)})}),this._resolverHash[r]=n[0]}else this._resolverHash[r]=this._buildResolvedAsset({alias:[r],src:r},{});i[r]=this._resolverHash[r]}),t?i[e[0]]:i}hasKey(e){return!!this._assetMap[e]}hasBundle(e){return!!this._bundles[e]}_getPreferredOrder(e){for(let t=0;t<e.length;t++){const i=e[t],r=this._preferredOrder.find(n=>n.params.format.includes(i.format));if(r)return r}return this._preferredOrder[0]}_appendDefaultSearchParams(e){if(!this._defaultSearchParams)return e;const t=/\?/.test(e)?"&":"?";return`${e}${t}${this._defaultSearchParams}`}_buildResolvedAsset(e,t){const{aliases:i,data:r,loadParser:n,format:o}=t;return(this._basePath||this._rootPath)&&(e.src=_t.toAbsolute(e.src,this._basePath,this._rootPath)),e.alias=i??e.alias??[e.src],e.src=this._appendDefaultSearchParams(e.src),e.data={...r||{},...e.data},e.loadParser=n??e.loadParser,e.format=o??e.format??Gu(e.src),e}}bi.RETINA_PREFIX=/@([0-9\.]+)x/;function Gu(s){return s.split(".").pop().split("?").shift().split("#").shift()}const pr=(s,e)=>{const t=e.split("?")[1];return t&&(s+=`?${t}`),s},il=class Ci{constructor(e,t){this.linkedSheets=[],this._texture=e instanceof _e?e:null,this.textureSource=e.source,this.textures={},this.animations={},this.data=t;const i=parseFloat(t.meta.scale);i?(this.resolution=i,e.source.resolution=this.resolution):this.resolution=e.source._resolution,this._frames=this.data.frames,this._frameKeys=Object.keys(this._frames),this._batchIndex=0,this._callback=null}parse(){return new Promise(e=>{this._callback=e,this._batchIndex=0,this._frameKeys.length<=Ci.BATCH_SIZE?(this._processFrames(0),this._processAnimations(),this._parseComplete()):this._nextBatch()})}_processFrames(e){let t=e;const i=Ci.BATCH_SIZE;for(;t-e<i&&t<this._frameKeys.length;){const r=this._frameKeys[t],n=this._frames[r],o=n.frame;if(o){let a=null,l=null;const h=n.trimmed!==!1&&n.sourceSize?n.sourceSize:n.frame,c=new Pe(0,0,Math.floor(h.w)/this.resolution,Math.floor(h.h)/this.resolution);n.rotated?a=new Pe(Math.floor(o.x)/this.resolution,Math.floor(o.y)/this.resolution,Math.floor(o.h)/this.resolution,Math.floor(o.w)/this.resolution):a=new Pe(Math.floor(o.x)/this.resolution,Math.floor(o.y)/this.resolution,Math.floor(o.w)/this.resolution,Math.floor(o.h)/this.resolution),n.trimmed!==!1&&n.spriteSourceSize&&(l=new Pe(Math.floor(n.spriteSourceSize.x)/this.resolution,Math.floor(n.spriteSourceSize.y)/this.resolution,Math.floor(o.w)/this.resolution,Math.floor(o.h)/this.resolution)),this.textures[r]=new _e({source:this.textureSource,frame:a,orig:c,trim:l,rotate:n.rotated?2:0,defaultAnchor:n.anchor,defaultBorders:n.borders,label:r.toString()})}t++}}_processAnimations(){const e=this.data.animations||{};for(const t in e){this.animations[t]=[];for(let i=0;i<e[t].length;i++){const r=e[t][i];this.animations[t].push(this.textures[r])}}}_parseComplete(){const e=this._callback;this._callback=null,this._batchIndex=0,e.call(this,this.textures)}_nextBatch(){this._processFrames(this._batchIndex*Ci.BATCH_SIZE),this._batchIndex++,setTimeout(()=>{this._batchIndex*Ci.BATCH_SIZE<this._frameKeys.length?this._nextBatch():(this._processAnimations(),this._parseComplete())},0)}destroy(e=!1){var t;for(const i in this.textures)this.textures[i].destroy();this._frames=null,this._frameKeys=null,this.data=null,this.textures=null,e&&((t=this._texture)==null||t.destroy(),this.textureSource.destroy()),this._texture=null,this.textureSource=null,this.linkedSheets=[]}};il.BATCH_SIZE=1e3;let Cn=il;const Du=["jpg","png","jpeg","avif","webp","basis","etc2","bc7","bc6h","bc5","bc4","bc3","bc2","bc1","eac","astc"];function sl(s,e,t){const i={};if(s.forEach(r=>{i[r]=e}),Object.keys(e.textures).forEach(r=>{i[r]=e.textures[r]}),!t){const r=_t.dirname(s[0]);e.linkedSheets.forEach((n,o)=>{const a=sl([`${r}/${e.data.meta.related_multi_packs[o]}`],n,!0);Object.assign(i,a)})}return i}const Nu={extension:J.Asset,cache:{test:s=>s instanceof Cn,getCacheableAssets:(s,e)=>sl(s,e,!1)},resolver:{extension:{type:J.ResolveParser,name:"resolveSpritesheet"},test:s=>{const t=s.split("?")[0].split("."),i=t.pop(),r=t.pop();return i==="json"&&Du.includes(r)},parse:s=>{var t;const e=s.split(".");return{resolution:parseFloat(((t=bi.RETINA_PREFIX.exec(s))==null?void 0:t[1])??"1"),format:e[e.length-2],src:s}}},loader:{name:"spritesheetLoader",extension:{type:J.LoadParser,priority:Xt.Normal,name:"spritesheetLoader"},async testParse(s,e){return _t.extname(e.src).toLowerCase()===".json"&&!!s.frames},async parse(s,e,t){var c,u;const{texture:i,imageFilename:r,textureOptions:n}=(e==null?void 0:e.data)??{};let o=_t.dirname(e.src);o&&o.lastIndexOf("/")!==o.length-1&&(o+="/");let a;if(i instanceof _e)a=i;else{const f=pr(o+(r??s.meta.image),e.src);a=(await t.load([{src:f,data:n}]))[f]}const l=new Cn(a.source,s);await l.parse();const h=(c=s==null?void 0:s.meta)==null?void 0:c.related_multi_packs;if(Array.isArray(h)){const f=[];for(const _ of h){if(typeof _!="string")continue;let g=o+_;(u=e.data)!=null&&u.ignoreMultiPack||(g=pr(g,e.src),f.push(t.load({src:g,data:{ignoreMultiPack:!0}})))}const d=await Promise.all(f);l.linkedSheets=d,d.forEach(_=>{_.linkedSheets=[l].concat(l.linkedSheets.filter(g=>g!==_))})}return l},async unload(s,e,t){await t.unload(s.textureSource._sourceOrigin),s.destroy(!1)}}};rt.add(Nu);const Ws=Object.create(null),Mn=Object.create(null);function Yr(s,e){let t=Mn[s];return t===void 0&&(Ws[e]===void 0&&(Ws[e]=1),Mn[s]=t=Ws[e]++),t}let ni;function rl(){return(!ni||ni!=null&&ni.isContextLost())&&(ni=Le.get().createCanvas().getContext("webgl",{})),ni}let qi;function Uu(){if(!qi){qi="mediump";const s=rl();s&&s.getShaderPrecisionFormat&&(qi=s.getShaderPrecisionFormat(s.FRAGMENT_SHADER,s.HIGH_FLOAT).precision?"highp":"mediump")}return qi}function Wu(s,e,t){return e?s:t?(s=s.replace("out vec4 finalColor;",""),`
        
        #ifdef GL_ES // This checks if it is WebGL1
        #define in varying
        #define finalColor gl_FragColor
        #define texture texture2D
        #endif
        ${s}
        `):`
        
        #ifdef GL_ES // This checks if it is WebGL1
        #define in attribute
        #define out varying
        #endif
        ${s}
        `}function Hu(s,e,t){const i=t?e.maxSupportedFragmentPrecision:e.maxSupportedVertexPrecision;if(s.substring(0,9)!=="precision"){let r=t?e.requestedFragmentPrecision:e.requestedVertexPrecision;return r==="highp"&&i!=="highp"&&(r="mediump"),`precision ${r} float;
${s}`}else if(i!=="highp"&&s.substring(0,15)==="precision highp")return s.replace("precision highp","precision mediump");return s}function Vu(s,e){return e?`#version 300 es
${s}`:s}const Xu={},Yu={};function ju(s,{name:e="pixi-program"},t=!0){e=e.replace(/\s+/g,"-"),e+=t?"-fragment":"-vertex";const i=t?Xu:Yu;return i[e]?(i[e]++,e+=`-${i[e]}`):i[e]=1,s.indexOf("#define SHADER_NAME")!==-1?s:`${`#define SHADER_NAME ${e}`}
${s}`}function qu(s,e){return e?s.replace("#version 300 es",""):s}const Hs={stripVersion:qu,ensurePrecision:Hu,addProgramDefines:Wu,setProgramName:ju,insertVersion:Vu},Vs=Object.create(null),nl=class br{constructor(e){e={...br.defaultOptions,...e};const t=e.fragment.indexOf("#version 300 es")!==-1,i={stripVersion:t,ensurePrecision:{requestedFragmentPrecision:e.preferredFragmentPrecision,requestedVertexPrecision:e.preferredVertexPrecision,maxSupportedVertexPrecision:"highp",maxSupportedFragmentPrecision:Uu()},setProgramName:{name:e.name},addProgramDefines:t,insertVersion:t};let r=e.fragment,n=e.vertex;Object.keys(Hs).forEach(o=>{const a=i[o];r=Hs[o](r,a,!0),n=Hs[o](n,a,!1)}),this.fragment=r,this.vertex=n,this.transformFeedbackVaryings=e.transformFeedbackVaryings,this._key=Yr(`${this.vertex}:${this.fragment}`,"gl-program")}destroy(){this.fragment=null,this.vertex=null,this._attributeData=null,this._uniformData=null,this._uniformBlockData=null,this.transformFeedbackVaryings=null}static from(e){const t=`${e.vertex}:${e.fragment}`;return Vs[t]||(Vs[t]=new br(e)),Vs[t]}};nl.defaultOptions={preferredVertexPrecision:"highp",preferredFragmentPrecision:"mediump"};let _i=nl;const Tn={uint8x2:{size:2,stride:2,normalised:!1},uint8x4:{size:4,stride:4,normalised:!1},sint8x2:{size:2,stride:2,normalised:!1},sint8x4:{size:4,stride:4,normalised:!1},unorm8x2:{size:2,stride:2,normalised:!0},unorm8x4:{size:4,stride:4,normalised:!0},snorm8x2:{size:2,stride:2,normalised:!0},snorm8x4:{size:4,stride:4,normalised:!0},uint16x2:{size:2,stride:4,normalised:!1},uint16x4:{size:4,stride:8,normalised:!1},sint16x2:{size:2,stride:4,normalised:!1},sint16x4:{size:4,stride:8,normalised:!1},unorm16x2:{size:2,stride:4,normalised:!0},unorm16x4:{size:4,stride:8,normalised:!0},snorm16x2:{size:2,stride:4,normalised:!0},snorm16x4:{size:4,stride:8,normalised:!0},float16x2:{size:2,stride:4,normalised:!1},float16x4:{size:4,stride:8,normalised:!1},float32:{size:1,stride:4,normalised:!1},float32x2:{size:2,stride:8,normalised:!1},float32x3:{size:3,stride:12,normalised:!1},float32x4:{size:4,stride:16,normalised:!1},uint32:{size:1,stride:4,normalised:!1},uint32x2:{size:2,stride:8,normalised:!1},uint32x3:{size:3,stride:12,normalised:!1},uint32x4:{size:4,stride:16,normalised:!1},sint32:{size:1,stride:4,normalised:!1},sint32x2:{size:2,stride:8,normalised:!1},sint32x3:{size:3,stride:12,normalised:!1},sint32x4:{size:4,stride:16,normalised:!1}};function Ku(s){return Tn[s]??Tn.float32}const Zu={f32:"float32","vec2<f32>":"float32x2","vec3<f32>":"float32x3","vec4<f32>":"float32x4",vec2f:"float32x2",vec3f:"float32x3",vec4f:"float32x4",i32:"sint32","vec2<i32>":"sint32x2","vec3<i32>":"sint32x3","vec4<i32>":"sint32x4",u32:"uint32","vec2<u32>":"uint32x2","vec3<u32>":"uint32x3","vec4<u32>":"uint32x4",bool:"uint32","vec2<bool>":"uint32x2","vec3<bool>":"uint32x3","vec4<bool>":"uint32x4"};function Qu({source:s,entryPoint:e}){const t={},i=s.indexOf(`fn ${e}`);if(i!==-1){const r=s.indexOf("->",i);if(r!==-1){const n=s.substring(i,r),o=/@location\((\d+)\)\s+([a-zA-Z0-9_]+)\s*:\s*([a-zA-Z0-9_<>]+)(?:,|\s|$)/g;let a;for(;(a=o.exec(n))!==null;){const l=Zu[a[3]]??"float32";t[a[2]]={location:parseInt(a[1],10),format:l,stride:Ku(l).stride,offset:0,instance:!1,start:0}}}}return t}function Xs(s){var u,f;const e=/(^|[^/])@(group|binding)\(\d+\)[^;]+;/g,t=/@group\((\d+)\)/,i=/@binding\((\d+)\)/,r=/var(<[^>]+>)? (\w+)/,n=/:\s*(\w+)/,o=/struct\s+(\w+)\s*{([^}]+)}/g,a=/(\w+)\s*:\s*([\w\<\>]+)/g,l=/struct\s+(\w+)/,h=(u=s.match(e))==null?void 0:u.map(d=>({group:parseInt(d.match(t)[1],10),binding:parseInt(d.match(i)[1],10),name:d.match(r)[2],isUniform:d.match(r)[1]==="<uniform>",type:d.match(n)[1]}));if(!h)return{groups:[],structs:[]};const c=((f=s.match(o))==null?void 0:f.map(d=>{const _=d.match(l)[1],g=d.match(a).reduce((m,p)=>{const[b,y]=p.split(":");return m[b.trim()]=y.trim(),m},{});return g?{name:_,members:g}:null}).filter(({name:d})=>h.some(_=>_.type===d)))??[];return{groups:h,structs:c}}var Mi=(s=>(s[s.VERTEX=1]="VERTEX",s[s.FRAGMENT=2]="FRAGMENT",s[s.COMPUTE=4]="COMPUTE",s))(Mi||{});function Ju({groups:s}){const e=[];for(let t=0;t<s.length;t++){const i=s[t];e[i.group]||(e[i.group]=[]),i.isUniform?e[i.group].push({binding:i.binding,visibility:Mi.VERTEX|Mi.FRAGMENT,buffer:{type:"uniform"}}):i.type==="sampler"?e[i.group].push({binding:i.binding,visibility:Mi.FRAGMENT,sampler:{type:"filtering"}}):i.type==="texture_2d"&&e[i.group].push({binding:i.binding,visibility:Mi.FRAGMENT,texture:{sampleType:"float",viewDimension:"2d",multisampled:!1}})}return e}function $u({groups:s}){const e=[];for(let t=0;t<s.length;t++){const i=s[t];e[i.group]||(e[i.group]={}),e[i.group][i.name]=i.binding}return e}function ed(s,e){const t=new Set,i=new Set,r=[...s.structs,...e.structs].filter(o=>t.has(o.name)?!1:(t.add(o.name),!0)),n=[...s.groups,...e.groups].filter(o=>{const a=`${o.name}-${o.binding}`;return i.has(a)?!1:(i.add(a),!0)});return{structs:r,groups:n}}const Ys=Object.create(null);class Vt{constructor(e){var a,l;this._layoutKey=0,this._attributeLocationsKey=0;const{fragment:t,vertex:i,layout:r,gpuLayout:n,name:o}=e;if(this.name=o,this.fragment=t,this.vertex=i,t.source===i.source){const h=Xs(t.source);this.structsAndGroups=h}else{const h=Xs(i.source),c=Xs(t.source);this.structsAndGroups=ed(h,c)}this.layout=r??$u(this.structsAndGroups),this.gpuLayout=n??Ju(this.structsAndGroups),this.autoAssignGlobalUniforms=((a=this.layout[0])==null?void 0:a.globalUniforms)!==void 0,this.autoAssignLocalUniforms=((l=this.layout[1])==null?void 0:l.localUniforms)!==void 0,this._generateProgramKey()}_generateProgramKey(){const{vertex:e,fragment:t}=this,i=e.source+t.source+e.entryPoint+t.entryPoint;this._layoutKey=Yr(i,"program")}get attributeData(){return this._attributeData??(this._attributeData=Qu(this.vertex)),this._attributeData}destroy(){this.gpuLayout=null,this.layout=null,this.structsAndGroups=null,this.fragment=null,this.vertex=null}static from(e){const t=`${e.vertex.source}:${e.fragment.source}:${e.fragment.entryPoint}:${e.vertex.entryPoint}`;return Ys[t]||(Ys[t]=new Vt(e)),Ys[t]}}const ol=["f32","i32","vec2<f32>","vec3<f32>","vec4<f32>","mat2x2<f32>","mat3x3<f32>","mat4x4<f32>","mat3x2<f32>","mat4x2<f32>","mat2x3<f32>","mat4x3<f32>","mat2x4<f32>","mat3x4<f32>","vec2<i32>","vec3<i32>","vec4<i32>"],td=ol.reduce((s,e)=>(s[e]=!0,s),{});function id(s,e){switch(s){case"f32":return 0;case"vec2<f32>":return new Float32Array(2*e);case"vec3<f32>":return new Float32Array(3*e);case"vec4<f32>":return new Float32Array(4*e);case"mat2x2<f32>":return new Float32Array([1,0,0,1]);case"mat3x3<f32>":return new Float32Array([1,0,0,0,1,0,0,0,1]);case"mat4x4<f32>":return new Float32Array([1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1])}return null}const al=class ll{constructor(e,t){this._touched=0,this.uid=He("uniform"),this._resourceType="uniformGroup",this._resourceId=He("resource"),this.isUniformGroup=!0,this._dirtyId=0,this.destroyed=!1,t={...ll.defaultOptions,...t},this.uniformStructures=e;const i={};for(const r in e){const n=e[r];if(n.name=r,n.size=n.size??1,!td[n.type])throw new Error(`Uniform type ${n.type} is not supported. Supported uniform types are: ${ol.join(", ")}`);n.value??(n.value=id(n.type,n.size)),i[r]=n.value}this.uniforms=i,this._dirtyId=1,this.ubo=t.ubo,this.isStatic=t.isStatic,this._signature=Yr(Object.keys(i).map(r=>`${r}-${e[r].type}`).join("-"),"uniform-group")}update(){this._dirtyId++}};al.defaultOptions={ubo:!1,isStatic:!1};let hl=al;class ns{constructor(e){this.resources=Object.create(null),this._dirty=!0;let t=0;for(const i in e){const r=e[i];this.setResource(r,t++)}this._updateKey()}_updateKey(){if(!this._dirty)return;this._dirty=!1;const e=[];let t=0;for(const i in this.resources)e[t++]=this.resources[i]._resourceId;this._key=e.join("|")}setResource(e,t){var r,n;const i=this.resources[t];e!==i&&(i&&((r=e.off)==null||r.call(e,"change",this.onResourceChange,this)),(n=e.on)==null||n.call(e,"change",this.onResourceChange,this),this.resources[t]=e,this._dirty=!0)}getResource(e){return this.resources[e]}_touch(e){const t=this.resources;for(const i in t)t[i]._touched=e}destroy(){var t;const e=this.resources;for(const i in e){const r=e[i];(t=r.off)==null||t.call(r,"change",this.onResourceChange,this)}this.resources=null}onResourceChange(e){if(this._dirty=!0,e.destroyed){const t=this.resources;for(const i in t)t[i]===e&&(t[i]=null)}else this._updateKey()}}var yr=(s=>(s[s.WEBGL=1]="WEBGL",s[s.WEBGPU=2]="WEBGPU",s[s.BOTH=3]="BOTH",s))(yr||{});class ys extends Tt{constructor(e){super(),this.uid=He("shader"),this._uniformBindMap=Object.create(null),this._ownedBindGroups=[];let{gpuProgram:t,glProgram:i,groups:r,resources:n,compatibleRenderers:o,groupMap:a}=e;this.gpuProgram=t,this.glProgram=i,o===void 0&&(o=0,t&&(o|=yr.WEBGPU),i&&(o|=yr.WEBGL)),this.compatibleRenderers=o;const l={};if(!n&&!r&&(n={}),n&&r)throw new Error("[Shader] Cannot have both resources and groups");if(!t&&r&&!a)throw new Error("[Shader] No group map or WebGPU shader provided - consider using resources instead.");if(!t&&r&&a)for(const h in a)for(const c in a[h]){const u=a[h][c];l[u]={group:h,binding:c,name:u}}else if(t&&r&&!a){const h=t.structsAndGroups.groups;a={},h.forEach(c=>{a[c.group]=a[c.group]||{},a[c.group][c.binding]=c.name,l[c.name]=c})}else if(n){r={},a={},t&&t.structsAndGroups.groups.forEach(u=>{a[u.group]=a[u.group]||{},a[u.group][u.binding]=u.name,l[u.name]=u});let h=0;for(const c in n)l[c]||(r[99]||(r[99]=new ns,this._ownedBindGroups.push(r[99])),l[c]={group:99,binding:h,name:c},a[99]=a[99]||{},a[99][h]=c,h++);for(const c in n){const u=c;let f=n[c];!f.source&&!f._resourceType&&(f=new hl(f));const d=l[u];d&&(r[d.group]||(r[d.group]=new ns,this._ownedBindGroups.push(r[d.group])),r[d.group].setResource(f,d.binding))}}this.groups=r,this._uniformBindMap=a,this.resources=this._buildResourceAccessor(r,l)}addResource(e,t,i){var r,n;(r=this._uniformBindMap)[t]||(r[t]={}),(n=this._uniformBindMap[t])[i]||(n[i]=e),this.groups[t]||(this.groups[t]=new ns,this._ownedBindGroups.push(this.groups[t]))}_buildResourceAccessor(e,t){const i={};for(const r in t){const n=t[r];Object.defineProperty(i,n.name,{get(){return e[n.group].getResource(n.binding)},set(o){e[n.group].setResource(o,n.binding)}})}return i}destroy(e=!1){var t,i;this.emit("destroy",this),e&&((t=this.gpuProgram)==null||t.destroy(),(i=this.glProgram)==null||i.destroy()),this.gpuProgram=null,this.glProgram=null,this.removeAllListeners(),this._uniformBindMap=null,this._ownedBindGroups.forEach(r=>{r.destroy()}),this._ownedBindGroups=null,this.resources=null,this.groups=null}static from(e){const{gpu:t,gl:i,...r}=e;let n,o;return t&&(n=Vt.from(t)),i&&(o=_i.from(i)),new ys({gpuProgram:n,glProgram:o,...r})}}const sd={normal:0,add:1,multiply:2,screen:3,overlay:4,erase:5,"normal-npm":6,"add-npm":7,"screen-npm":8,min:9,max:10},js=0,qs=1,Ks=2,Zs=3,Qs=4,Js=5,wr=class cl{constructor(){this.data=0,this.blendMode="normal",this.polygonOffset=0,this.blend=!0,this.depthMask=!0}get blend(){return!!(this.data&1<<js)}set blend(e){!!(this.data&1<<js)!==e&&(this.data^=1<<js)}get offsets(){return!!(this.data&1<<qs)}set offsets(e){!!(this.data&1<<qs)!==e&&(this.data^=1<<qs)}set cullMode(e){if(e==="none"){this.culling=!1;return}this.culling=!0,this.clockwiseFrontFace=e==="front"}get cullMode(){return this.culling?this.clockwiseFrontFace?"front":"back":"none"}get culling(){return!!(this.data&1<<Ks)}set culling(e){!!(this.data&1<<Ks)!==e&&(this.data^=1<<Ks)}get depthTest(){return!!(this.data&1<<Zs)}set depthTest(e){!!(this.data&1<<Zs)!==e&&(this.data^=1<<Zs)}get depthMask(){return!!(this.data&1<<Js)}set depthMask(e){!!(this.data&1<<Js)!==e&&(this.data^=1<<Js)}get clockwiseFrontFace(){return!!(this.data&1<<Qs)}set clockwiseFrontFace(e){!!(this.data&1<<Qs)!==e&&(this.data^=1<<Qs)}get blendMode(){return this._blendMode}set blendMode(e){this.blend=e!=="none",this._blendMode=e,this._blendModeId=sd[e]||0}get polygonOffset(){return this._polygonOffset}set polygonOffset(e){this.offsets=!!e,this._polygonOffset=e}toString(){return`[pixi.js/core:State blendMode=${this.blendMode} clockwiseFrontFace=${this.clockwiseFrontFace} culling=${this.culling} depthMask=${this.depthMask} polygonOffset=${this.polygonOffset}]`}static for2d(){const e=new cl;return e.depthTest=!1,e.blend=!0,e}};wr.default2d=wr.for2d();let rd=wr;const ul=class xr extends ys{constructor(e){e={...xr.defaultOptions,...e},super(e),this.enabled=!0,this._state=rd.for2d(),this.blendMode=e.blendMode,this.padding=e.padding,typeof e.antialias=="boolean"?this.antialias=e.antialias?"on":"off":this.antialias=e.antialias,this.resolution=e.resolution,this.blendRequired=e.blendRequired,this.clipToViewport=e.clipToViewport,this.addResource("uTexture",0,1)}apply(e,t,i,r){e.applyFilter(this,t,i,r)}get blendMode(){return this._state.blendMode}set blendMode(e){this._state.blendMode=e}static from(e){const{gpu:t,gl:i,...r}=e;let n,o;return t&&(n=Vt.from(t)),i&&(o=_i.from(i)),new xr({gpuProgram:n,glProgram:o,...r})}};ul.defaultOptions={blendMode:"normal",resolution:1,padding:0,antialias:"off",blendRequired:!1,clipToViewport:!0};let vr=ul;const kr=[];rt.handleByNamedList(J.Environment,kr);async function nd(s){if(!s)for(let e=0;e<kr.length;e++){const t=kr[e];if(t.value.test()){await t.value.load();return}}}let vi;function od(){if(typeof vi=="boolean")return vi;try{vi=new Function("param1","param2","param3","return param1[param2] === param3;")({a:"b"},"a","b")===!0}catch{vi=!1}return vi}var jr={exports:{}};jr.exports=ws;jr.exports.default=ws;function ws(s,e,t){t=t||2;var i=e&&e.length,r=i?e[0]*t:s.length,n=dl(s,0,r,t,!0),o=[];if(!n||n.next===n.prev)return o;var a,l,h,c,u,f,d;if(i&&(n=ud(s,e,n,t)),s.length>80*t){a=h=s[0],l=c=s[1];for(var _=t;_<r;_+=t)u=s[_],f=s[_+1],u<a&&(a=u),f<l&&(l=f),u>h&&(h=u),f>c&&(c=f);d=Math.max(h-a,c-l),d=d!==0?32767/d:0}return zi(n,o,t,a,l,d,0),o}function dl(s,e,t,i,r){var n,o;if(r===Cr(s,e,t,i)>0)for(n=e;n<t;n+=i)o=Pn(n,s[n],s[n+1],o);else for(n=t-i;n>=e;n-=i)o=Pn(n,s[n],s[n+1],o);return o&&xs(o,o.next)&&(Bi(o),o=o.next),o}function ti(s,e){if(!s)return s;e||(e=s);var t=s,i;do if(i=!1,!t.steiner&&(xs(t,t.next)||Re(t.prev,t,t.next)===0)){if(Bi(t),t=e=t.prev,t===t.next)break;i=!0}else t=t.next;while(i||t!==e);return e}function zi(s,e,t,i,r,n,o){if(s){!o&&n&&md(s,i,r,n);for(var a=s,l,h;s.prev!==s.next;){if(l=s.prev,h=s.next,n?ld(s,i,r,n):ad(s)){e.push(l.i/t|0),e.push(s.i/t|0),e.push(h.i/t|0),Bi(s),s=h.next,a=h.next;continue}if(s=h,s===a){o?o===1?(s=hd(ti(s),e,t),zi(s,e,t,i,r,n,2)):o===2&&cd(s,e,t,i,r,n):zi(ti(s),e,t,i,r,n,1);break}}}}function ad(s){var e=s.prev,t=s,i=s.next;if(Re(e,t,i)>=0)return!1;for(var r=e.x,n=t.x,o=i.x,a=e.y,l=t.y,h=i.y,c=r<n?r<o?r:o:n<o?n:o,u=a<l?a<h?a:h:l<h?l:h,f=r>n?r>o?r:o:n>o?n:o,d=a>l?a>h?a:h:l>h?l:h,_=i.next;_!==e;){if(_.x>=c&&_.x<=f&&_.y>=u&&_.y<=d&&hi(r,a,n,l,o,h,_.x,_.y)&&Re(_.prev,_,_.next)>=0)return!1;_=_.next}return!0}function ld(s,e,t,i){var r=s.prev,n=s,o=s.next;if(Re(r,n,o)>=0)return!1;for(var a=r.x,l=n.x,h=o.x,c=r.y,u=n.y,f=o.y,d=a<l?a<h?a:h:l<h?l:h,_=c<u?c<f?c:f:u<f?u:f,g=a>l?a>h?a:h:l>h?l:h,m=c>u?c>f?c:f:u>f?u:f,p=Ar(d,_,e,t,i),b=Ar(g,m,e,t,i),y=s.prevZ,w=s.nextZ;y&&y.z>=p&&w&&w.z<=b;){if(y.x>=d&&y.x<=g&&y.y>=_&&y.y<=m&&y!==r&&y!==o&&hi(a,c,l,u,h,f,y.x,y.y)&&Re(y.prev,y,y.next)>=0||(y=y.prevZ,w.x>=d&&w.x<=g&&w.y>=_&&w.y<=m&&w!==r&&w!==o&&hi(a,c,l,u,h,f,w.x,w.y)&&Re(w.prev,w,w.next)>=0))return!1;w=w.nextZ}for(;y&&y.z>=p;){if(y.x>=d&&y.x<=g&&y.y>=_&&y.y<=m&&y!==r&&y!==o&&hi(a,c,l,u,h,f,y.x,y.y)&&Re(y.prev,y,y.next)>=0)return!1;y=y.prevZ}for(;w&&w.z<=b;){if(w.x>=d&&w.x<=g&&w.y>=_&&w.y<=m&&w!==r&&w!==o&&hi(a,c,l,u,h,f,w.x,w.y)&&Re(w.prev,w,w.next)>=0)return!1;w=w.nextZ}return!0}function hd(s,e,t){var i=s;do{var r=i.prev,n=i.next.next;!xs(r,n)&&fl(r,i,i.next,n)&&Ri(r,n)&&Ri(n,r)&&(e.push(r.i/t|0),e.push(i.i/t|0),e.push(n.i/t|0),Bi(i),Bi(i.next),i=s=n),i=i.next}while(i!==s);return ti(i)}function cd(s,e,t,i,r,n){var o=s;do{for(var a=o.next.next;a!==o.prev;){if(o.i!==a.i&&yd(o,a)){var l=_l(o,a);o=ti(o,o.next),l=ti(l,l.next),zi(o,e,t,i,r,n,0),zi(l,e,t,i,r,n,0);return}a=a.next}o=o.next}while(o!==s)}function ud(s,e,t,i){var r=[],n,o,a,l,h;for(n=0,o=e.length;n<o;n++)a=e[n]*i,l=n<o-1?e[n+1]*i:s.length,h=dl(s,a,l,i,!1),h===h.next&&(h.steiner=!0),r.push(bd(h));for(r.sort(dd),n=0;n<r.length;n++)t=fd(r[n],t);return t}function dd(s,e){return s.x-e.x}function fd(s,e){var t=_d(s,e);if(!t)return e;var i=_l(t,s);return ti(i,i.next),ti(t,t.next)}function _d(s,e){var t=e,i=s.x,r=s.y,n=-1/0,o;do{if(r<=t.y&&r>=t.next.y&&t.next.y!==t.y){var a=t.x+(r-t.y)*(t.next.x-t.x)/(t.next.y-t.y);if(a<=i&&a>n&&(n=a,o=t.x<t.next.x?t:t.next,a===i))return o}t=t.next}while(t!==e);if(!o)return null;var l=o,h=o.x,c=o.y,u=1/0,f;t=o;do i>=t.x&&t.x>=h&&i!==t.x&&hi(r<c?i:n,r,h,c,r<c?n:i,r,t.x,t.y)&&(f=Math.abs(r-t.y)/(i-t.x),Ri(t,s)&&(f<u||f===u&&(t.x>o.x||t.x===o.x&&gd(o,t)))&&(o=t,u=f)),t=t.next;while(t!==l);return o}function gd(s,e){return Re(s.prev,s,e.prev)<0&&Re(e.next,s,s.next)<0}function md(s,e,t,i){var r=s;do r.z===0&&(r.z=Ar(r.x,r.y,e,t,i)),r.prevZ=r.prev,r.nextZ=r.next,r=r.next;while(r!==s);r.prevZ.nextZ=null,r.prevZ=null,pd(r)}function pd(s){var e,t,i,r,n,o,a,l,h=1;do{for(t=s,s=null,n=null,o=0;t;){for(o++,i=t,a=0,e=0;e<h&&(a++,i=i.nextZ,!!i);e++);for(l=h;a>0||l>0&&i;)a!==0&&(l===0||!i||t.z<=i.z)?(r=t,t=t.nextZ,a--):(r=i,i=i.nextZ,l--),n?n.nextZ=r:s=r,r.prevZ=n,n=r;t=i}n.nextZ=null,h*=2}while(o>1);return s}function Ar(s,e,t,i,r){return s=(s-t)*r|0,e=(e-i)*r|0,s=(s|s<<8)&16711935,s=(s|s<<4)&252645135,s=(s|s<<2)&858993459,s=(s|s<<1)&1431655765,e=(e|e<<8)&16711935,e=(e|e<<4)&252645135,e=(e|e<<2)&858993459,e=(e|e<<1)&1431655765,s|e<<1}function bd(s){var e=s,t=s;do(e.x<t.x||e.x===t.x&&e.y<t.y)&&(t=e),e=e.next;while(e!==s);return t}function hi(s,e,t,i,r,n,o,a){return(r-o)*(e-a)>=(s-o)*(n-a)&&(s-o)*(i-a)>=(t-o)*(e-a)&&(t-o)*(n-a)>=(r-o)*(i-a)}function yd(s,e){return s.next.i!==e.i&&s.prev.i!==e.i&&!wd(s,e)&&(Ri(s,e)&&Ri(e,s)&&xd(s,e)&&(Re(s.prev,s,e.prev)||Re(s,e.prev,e))||xs(s,e)&&Re(s.prev,s,s.next)>0&&Re(e.prev,e,e.next)>0)}function Re(s,e,t){return(e.y-s.y)*(t.x-e.x)-(e.x-s.x)*(t.y-e.y)}function xs(s,e){return s.x===e.x&&s.y===e.y}function fl(s,e,t,i){var r=Zi(Re(s,e,t)),n=Zi(Re(s,e,i)),o=Zi(Re(t,i,s)),a=Zi(Re(t,i,e));return!!(r!==n&&o!==a||r===0&&Ki(s,t,e)||n===0&&Ki(s,i,e)||o===0&&Ki(t,s,i)||a===0&&Ki(t,e,i))}function Ki(s,e,t){return e.x<=Math.max(s.x,t.x)&&e.x>=Math.min(s.x,t.x)&&e.y<=Math.max(s.y,t.y)&&e.y>=Math.min(s.y,t.y)}function Zi(s){return s>0?1:s<0?-1:0}function wd(s,e){var t=s;do{if(t.i!==s.i&&t.next.i!==s.i&&t.i!==e.i&&t.next.i!==e.i&&fl(t,t.next,s,e))return!0;t=t.next}while(t!==s);return!1}function Ri(s,e){return Re(s.prev,s,s.next)<0?Re(s,e,s.next)>=0&&Re(s,s.prev,e)>=0:Re(s,e,s.prev)<0||Re(s,s.next,e)<0}function xd(s,e){var t=s,i=!1,r=(s.x+e.x)/2,n=(s.y+e.y)/2;do t.y>n!=t.next.y>n&&t.next.y!==t.y&&r<(t.next.x-t.x)*(n-t.y)/(t.next.y-t.y)+t.x&&(i=!i),t=t.next;while(t!==s);return i}function _l(s,e){var t=new Sr(s.i,s.x,s.y),i=new Sr(e.i,e.x,e.y),r=s.next,n=e.prev;return s.next=e,e.prev=s,t.next=r,r.prev=t,i.next=t,t.prev=i,n.next=i,i.prev=n,i}function Pn(s,e,t,i){var r=new Sr(s,e,t);return i?(r.next=i.next,r.prev=i,i.next.prev=r,i.next=r):(r.prev=r,r.next=r),r}function Bi(s){s.next.prev=s.prev,s.prev.next=s.next,s.prevZ&&(s.prevZ.nextZ=s.nextZ),s.nextZ&&(s.nextZ.prevZ=s.prevZ)}function Sr(s,e,t){this.i=s,this.x=e,this.y=t,this.prev=null,this.next=null,this.z=0,this.prevZ=null,this.nextZ=null,this.steiner=!1}ws.deviation=function(s,e,t,i){var r=e&&e.length,n=r?e[0]*t:s.length,o=Math.abs(Cr(s,0,n,t));if(r)for(var a=0,l=e.length;a<l;a++){var h=e[a]*t,c=a<l-1?e[a+1]*t:s.length;o-=Math.abs(Cr(s,h,c,t))}var u=0;for(a=0;a<i.length;a+=3){var f=i[a]*t,d=i[a+1]*t,_=i[a+2]*t;u+=Math.abs((s[f]-s[_])*(s[d+1]-s[f+1])-(s[f]-s[d])*(s[_+1]-s[f+1]))}return o===0&&u===0?0:Math.abs((u-o)/o)};function Cr(s,e,t,i){for(var r=0,n=e,o=t-i;n<t;n+=i)r+=(s[o]-s[n])*(s[n+1]+s[o+1]),o=n;return r}ws.flatten=function(s){for(var e=s[0][0].length,t={vertices:[],holes:[],dimensions:e},i=0,r=0;r<s.length;r++){for(var n=0;n<s[r].length;n++)for(var o=0;o<e;o++)t.vertices.push(s[r][n][o]);r>0&&(i+=s[r-1].length,t.holes.push(i))}return t};var vd=jr.exports;const kd=Ur(vd);var gl=(s=>(s[s.NONE=0]="NONE",s[s.COLOR=16384]="COLOR",s[s.STENCIL=1024]="STENCIL",s[s.DEPTH=256]="DEPTH",s[s.COLOR_DEPTH=16640]="COLOR_DEPTH",s[s.COLOR_STENCIL=17408]="COLOR_STENCIL",s[s.DEPTH_STENCIL=1280]="DEPTH_STENCIL",s[s.ALL=17664]="ALL",s))(gl||{});class Ad{constructor(e){this.items=[],this._name=e}emit(e,t,i,r,n,o,a,l){const{name:h,items:c}=this;for(let u=0,f=c.length;u<f;u++)c[u][h](e,t,i,r,n,o,a,l);return this}add(e){return e[this._name]&&(this.remove(e),this.items.push(e)),this}remove(e){const t=this.items.indexOf(e);return t!==-1&&this.items.splice(t,1),this}contains(e){return this.items.indexOf(e)!==-1}removeAll(){return this.items.length=0,this}destroy(){this.removeAll(),this.items=null,this._name=null}get empty(){return this.items.length===0}get name(){return this._name}}const Sd=["init","destroy","contextChange","resolutionChange","resetState","renderEnd","renderStart","render","update","postrender","prerender"],ml=class pl extends Tt{constructor(e){super(),this.runners=Object.create(null),this.renderPipes=Object.create(null),this._initOptions={},this._systemsHash=Object.create(null),this.type=e.type,this.name=e.name,this.config=e;const t=[...Sd,...this.config.runners??[]];this._addRunners(...t),this._unsafeEvalCheck()}async init(e={}){const t=e.skipExtensionImports===!0?!0:e.manageImports===!1;await nd(t),this._addSystems(this.config.systems),this._addPipes(this.config.renderPipes,this.config.renderPipeAdaptors);for(const i in this._systemsHash)e={...this._systemsHash[i].constructor.defaultOptions,...e};e={...pl.defaultOptions,...e},this._roundPixels=e.roundPixels?1:0;for(let i=0;i<this.runners.init.items.length;i++)await this.runners.init.items[i].init(e);this._initOptions=e}render(e,t){let i=e;if(i instanceof re&&(i={container:i},t&&(be(xe,"passing a second argument is deprecated, please use render options instead"),i.target=t.renderTexture)),i.target||(i.target=this.view.renderTarget),i.target===this.view.renderTarget&&(this._lastObjectRendered=i.container,i.clearColor??(i.clearColor=this.background.colorRgba),i.clear??(i.clear=this.background.clearBeforeRender)),i.clearColor){const r=Array.isArray(i.clearColor)&&i.clearColor.length===4;i.clearColor=r?i.clearColor:Ne.shared.setValue(i.clearColor).toArray()}i.transform||(i.container.updateLocalTransform(),i.transform=i.container.localTransform),i.container.enableRenderGroup(),this.runners.prerender.emit(i),this.runners.renderStart.emit(i),this.runners.render.emit(i),this.runners.renderEnd.emit(i),this.runners.postrender.emit(i)}resize(e,t,i){const r=this.view.resolution;this.view.resize(e,t,i),this.emit("resize",this.view.screen.width,this.view.screen.height,this.view.resolution),i!==void 0&&i!==r&&this.runners.resolutionChange.emit(i)}clear(e={}){const t=this;e.target||(e.target=t.renderTarget.renderTarget),e.clearColor||(e.clearColor=this.background.colorRgba),e.clear??(e.clear=gl.ALL);const{clear:i,clearColor:r,target:n}=e;Ne.shared.setValue(r??this.background.colorRgba),t.renderTarget.clear(n,i,Ne.shared.toArray())}get resolution(){return this.view.resolution}set resolution(e){this.view.resolution=e,this.runners.resolutionChange.emit(e)}get width(){return this.view.texture.frame.width}get height(){return this.view.texture.frame.height}get canvas(){return this.view.canvas}get lastObjectRendered(){return this._lastObjectRendered}get renderingToScreen(){return this.renderTarget.renderingToScreen}get screen(){return this.view.screen}_addRunners(...e){e.forEach(t=>{this.runners[t]=new Ad(t)})}_addSystems(e){let t;for(t in e){const i=e[t];this._addSystem(i.value,i.name)}}_addSystem(e,t){const i=new e(this);if(this[t])throw new Error(`Whoops! The name "${t}" is already in use`);this[t]=i,this._systemsHash[t]=i;for(const r in this.runners)this.runners[r].add(i);return this}_addPipes(e,t){const i=t.reduce((r,n)=>(r[n.name]=n.value,r),{});e.forEach(r=>{const n=r.value,o=r.name,a=i[o];this.renderPipes[o]=new n(this,a?new a:null)})}destroy(e=!1){this.runners.destroy.items.reverse(),this.runners.destroy.emit(e),Object.values(this.runners).forEach(t=>{t.destroy()}),this._systemsHash=null,this.renderPipes=null}generateTexture(e){return this.textureGenerator.generateTexture(e)}get roundPixels(){return!!this._roundPixels}_unsafeEvalCheck(){if(!od())throw new Error("Current environment does not allow unsafe-eval, please use pixi.js/unsafe-eval module to enable support.")}resetState(){this.runners.resetState.emit()}};ml.defaultOptions={resolution:1,failIfMajorPerformanceCaveat:!1,roundPixels:!1};let bl=ml,Qi;function Cd(s){return Qi!==void 0||(Qi=(()=>{var t;const e={stencil:!0,failIfMajorPerformanceCaveat:s??bl.defaultOptions.failIfMajorPerformanceCaveat};try{if(!Le.get().getWebGLRenderingContext())return!1;let r=Le.get().createCanvas().getContext("webgl",e);const n=!!((t=r==null?void 0:r.getContextAttributes())!=null&&t.stencil);if(r){const o=r.getExtension("WEBGL_lose_context");o&&o.loseContext()}return r=null,n}catch{return!1}})()),Qi}let Ji;async function Md(s={}){return Ji!==void 0||(Ji=await(async()=>{const e=Le.get().getNavigator().gpu;if(!e)return!1;try{return await(await e.requestAdapter(s)).requestDevice(),!0}catch{return!1}})()),Ji}const In=["webgl","webgpu","canvas"];async function Td(s){let e=[];s.preference?(e.push(s.preference),In.forEach(n=>{n!==s.preference&&e.push(n)})):e=In.slice();let t,i={};for(let n=0;n<e.length;n++){const o=e[n];if(o==="webgpu"&&await Md()){const{WebGPURenderer:a}=await hs(()=>import("./WebGPURenderer.BOjjzCCO.js"),__vite__mapDeps([4,2,5]),import.meta.url);t=a,i={...s,...s.webgpu};break}else if(o==="webgl"&&Cd(s.failIfMajorPerformanceCaveat??bl.defaultOptions.failIfMajorPerformanceCaveat)){const{WebGLRenderer:a}=await hs(()=>import("./WebGLRenderer.Cz64BPQU.js"),__vite__mapDeps([6,2,5]),import.meta.url);t=a,i={...s,...s.webgl};break}else if(o==="canvas")throw i={...s},new Error("CanvasRenderer is not yet implemented")}if(delete i.webgpu,delete i.webgl,!t)throw new Error("No available renderer for the current environment");const r=new t;return await r.init(i),r}const yl="8.7.3";class wl{static init(){var e;(e=globalThis.__PIXI_APP_INIT__)==null||e.call(globalThis,this,yl)}static destroy(){}}wl.extension=J.Application;class Pd{constructor(e){this._renderer=e}init(){var e;(e=globalThis.__PIXI_RENDERER_INIT__)==null||e.call(globalThis,this._renderer,yl)}destroy(){this._renderer=null}}Pd.extension={type:[J.WebGLSystem,J.WebGPUSystem],name:"initHook",priority:-10};const xl=class Mr{constructor(...e){this.stage=new re,e[0]!==void 0&&be(xe,"Application constructor options are deprecated, please use Application.init() instead.")}async init(e){e={...e},this.renderer=await Td(e),Mr._plugins.forEach(t=>{t.init.call(this,e)})}render(){this.renderer.render({container:this.stage})}get canvas(){return this.renderer.canvas}get view(){return be(xe,"Application.view is deprecated, please use Application.canvas instead."),this.renderer.canvas}get screen(){return this.renderer.screen}destroy(e=!1,t=!1){const i=Mr._plugins.slice(0);i.reverse(),i.forEach(r=>{r.destroy.call(this)}),this.stage.destroy(t),this.stage=null,this.renderer.destroy(e),this.renderer=null}};xl._plugins=[];let vl=xl;rt.handleByList(J.Application,vl._plugins);rt.add(wl);class kl extends Tt{constructor(){super(...arguments),this.chars=Object.create(null),this.lineHeight=0,this.fontFamily="",this.fontMetrics={fontSize:0,ascent:0,descent:0},this.baseLineOffset=0,this.distanceField={type:"none",range:0},this.pages=[],this.applyFillAsTint=!0,this.baseMeasurementFontSize=100,this.baseRenderedFontSize=100}get font(){return be(xe,"BitmapFont.font is deprecated, please use BitmapFont.fontFamily instead."),this.fontFamily}get pageTextures(){return be(xe,"BitmapFont.pageTextures is deprecated, please use BitmapFont.pages instead."),this.pages}get size(){return be(xe,"BitmapFont.size is deprecated, please use BitmapFont.fontMetrics.fontSize instead."),this.fontMetrics.fontSize}get distanceFieldRange(){return be(xe,"BitmapFont.distanceFieldRange is deprecated, please use BitmapFont.distanceField.range instead."),this.distanceField.range}get distanceFieldType(){return be(xe,"BitmapFont.distanceFieldType is deprecated, please use BitmapFont.distanceField.type instead."),this.distanceField.type}destroy(e=!1){var t;this.emit("destroy",this),this.removeAllListeners();for(const i in this.chars)(t=this.chars[i].texture)==null||t.destroy();this.chars=null,e&&(this.pages.forEach(i=>i.texture.destroy(!0)),this.pages=null)}}const Al=class Tr{constructor(e,t,i,r){this.uid=He("fillGradient"),this.type="linear",this.gradientStops=[],this._styleKey=null,this.x0=e,this.y0=t,this.x1=i,this.y1=r}addColorStop(e,t){return this.gradientStops.push({offset:e,color:Ne.shared.setValue(t).toHexa()}),this._styleKey=null,this}buildLinearGradient(){if(this.texture)return;const e=Tr.defaultTextureSize,{gradientStops:t}=this,i=Le.get().createCanvas();i.width=e,i.height=e;const r=i.getContext("2d"),n=r.createLinearGradient(0,0,Tr.defaultTextureSize,1);for(let g=0;g<t.length;g++){const m=t[g];n.addColorStop(m.offset,m.color)}r.fillStyle=n,r.fillRect(0,0,e,e),this.texture=new _e({source:new pi({resource:i,addressModeU:"clamp-to-edge",addressModeV:"repeat"})});const{x0:o,y0:a,x1:l,y1:h}=this,c=new we,u=l-o,f=h-a,d=Math.sqrt(u*u+f*f),_=Math.atan2(f,u);c.translate(-o,-a),c.scale(1/e,1/e),c.rotate(-_),c.scale(256/d,1),this.transform=c,this._styleKey=null}get styleKey(){if(this._styleKey)return this._styleKey;const e=this.gradientStops.map(r=>`${r.offset}-${r.color}`).join("-"),t=this.texture.uid,i=this.transform.toArray().join("-");return`fill-gradient-${this.uid}-${e}-${t}-${i}-${this.x0}-${this.y0}-${this.x1}-${this.y1}`}};Al.defaultTextureSize=256;let Fi=Al;const En={repeat:{addressModeU:"repeat",addressModeV:"repeat"},"repeat-x":{addressModeU:"repeat",addressModeV:"clamp-to-edge"},"repeat-y":{addressModeU:"clamp-to-edge",addressModeV:"repeat"},"no-repeat":{addressModeU:"clamp-to-edge",addressModeV:"clamp-to-edge"}};class vs{constructor(e,t){this.uid=He("fillPattern"),this.transform=new we,this._styleKey=null,this.texture=e,this.transform.scale(1/e.frame.width,1/e.frame.height),t&&(e.source.style.addressModeU=En[t].addressModeU,e.source.style.addressModeV=En[t].addressModeV)}setTransform(e){const t=this.texture;this.transform.copyFrom(e),this.transform.invert(),this.transform.scale(1/t.frame.width,1/t.frame.height),this._styleKey=null}get styleKey(){return this._styleKey?this._styleKey:(this._styleKey=`fill-pattern-${this.uid}-${this.texture.uid}-${this.transform.toArray().join("-")}`,this._styleKey)}}var Id=zd,$s={a:7,c:6,h:1,l:2,m:2,q:4,s:4,t:2,v:1,z:0},Ed=/([astvzqmhlc])([^astvzqmhlc]*)/ig;function zd(s){var e=[];return s.replace(Ed,function(t,i,r){var n=i.toLowerCase();for(r=Bd(r),n=="m"&&r.length>2&&(e.push([i].concat(r.splice(0,2))),n="l",i=i=="m"?"l":"L");;){if(r.length==$s[n])return r.unshift(i),e.push(r);if(r.length<$s[n])throw new Error("malformed path data");e.push([i].concat(r.splice(0,$s[n])))}}),e}var Rd=/-?[0-9]*\.?[0-9]+(?:e[-+]?\d+)?/ig;function Bd(s){var e=s.match(Rd);return e?e.map(Number):[]}const Fd=Ur(Id);function Ld(s,e){const t=Fd(s),i=[];let r=null,n=0,o=0;for(let a=0;a<t.length;a++){const l=t[a],h=l[0],c=l;switch(h){case"M":n=c[1],o=c[2],e.moveTo(n,o);break;case"m":n+=c[1],o+=c[2],e.moveTo(n,o);break;case"H":n=c[1],e.lineTo(n,o);break;case"h":n+=c[1],e.lineTo(n,o);break;case"V":o=c[1],e.lineTo(n,o);break;case"v":o+=c[1],e.lineTo(n,o);break;case"L":n=c[1],o=c[2],e.lineTo(n,o);break;case"l":n+=c[1],o+=c[2],e.lineTo(n,o);break;case"C":n=c[5],o=c[6],e.bezierCurveTo(c[1],c[2],c[3],c[4],n,o);break;case"c":e.bezierCurveTo(n+c[1],o+c[2],n+c[3],o+c[4],n+c[5],o+c[6]),n+=c[5],o+=c[6];break;case"S":n=c[3],o=c[4],e.bezierCurveToShort(c[1],c[2],n,o);break;case"s":e.bezierCurveToShort(n+c[1],o+c[2],n+c[3],o+c[4]),n+=c[3],o+=c[4];break;case"Q":n=c[3],o=c[4],e.quadraticCurveTo(c[1],c[2],n,o);break;case"q":e.quadraticCurveTo(n+c[1],o+c[2],n+c[3],o+c[4]),n+=c[3],o+=c[4];break;case"T":n=c[1],o=c[2],e.quadraticCurveToShort(n,o);break;case"t":n+=c[1],o+=c[2],e.quadraticCurveToShort(n,o);break;case"A":n=c[6],o=c[7],e.arcToSvg(c[1],c[2],c[3],c[4],c[5],n,o);break;case"a":n+=c[6],o+=c[7],e.arcToSvg(c[1],c[2],c[3],c[4],c[5],n,o);break;case"Z":case"z":e.closePath(),i.length>0&&(r=i.pop(),r?(n=r.startX,o=r.startY):(n=0,o=0)),r=null;break;default:Ve(`Unknown SVG path command: ${h}`)}h!=="Z"&&h!=="z"&&r===null&&(r={startX:n,startY:o},i.push(r))}return e}class qr{constructor(e=0,t=0,i=0){this.type="circle",this.x=e,this.y=t,this.radius=i}clone(){return new qr(this.x,this.y,this.radius)}contains(e,t){if(this.radius<=0)return!1;const i=this.radius*this.radius;let r=this.x-e,n=this.y-t;return r*=r,n*=n,r+n<=i}strokeContains(e,t,i,r=.5){if(this.radius===0)return!1;const n=this.x-e,o=this.y-t,a=this.radius,l=(1-r)*i,h=Math.sqrt(n*n+o*o);return h<=a+l&&h>a-(i-l)}getBounds(e){return e||(e=new Pe),e.x=this.x-this.radius,e.y=this.y-this.radius,e.width=this.radius*2,e.height=this.radius*2,e}copyFrom(e){return this.x=e.x,this.y=e.y,this.radius=e.radius,this}copyTo(e){return e.copyFrom(this),e}toString(){return`[pixi.js/math:Circle x=${this.x} y=${this.y} radius=${this.radius}]`}}class Kr{constructor(e=0,t=0,i=0,r=0){this.type="ellipse",this.x=e,this.y=t,this.halfWidth=i,this.halfHeight=r}clone(){return new Kr(this.x,this.y,this.halfWidth,this.halfHeight)}contains(e,t){if(this.halfWidth<=0||this.halfHeight<=0)return!1;let i=(e-this.x)/this.halfWidth,r=(t-this.y)/this.halfHeight;return i*=i,r*=r,i+r<=1}strokeContains(e,t,i,r=.5){const{halfWidth:n,halfHeight:o}=this;if(n<=0||o<=0)return!1;const a=i*(1-r),l=i-a,h=n-l,c=o-l,u=n+a,f=o+a,d=e-this.x,_=t-this.y,g=d*d/(h*h)+_*_/(c*c),m=d*d/(u*u)+_*_/(f*f);return g>1&&m<=1}getBounds(e){return e||(e=new Pe),e.x=this.x-this.halfWidth,e.y=this.y-this.halfHeight,e.width=this.halfWidth*2,e.height=this.halfHeight*2,e}copyFrom(e){return this.x=e.x,this.y=e.y,this.halfWidth=e.halfWidth,this.halfHeight=e.halfHeight,this}copyTo(e){return e.copyFrom(this),e}toString(){return`[pixi.js/math:Ellipse x=${this.x} y=${this.y} halfWidth=${this.halfWidth} halfHeight=${this.halfHeight}]`}}function Od(s,e,t,i,r,n){const o=s-t,a=e-i,l=r-t,h=n-i,c=o*l+a*h,u=l*l+h*h;let f=-1;u!==0&&(f=c/u);let d,_;f<0?(d=t,_=i):f>1?(d=r,_=n):(d=t+f*l,_=i+f*h);const g=s-d,m=e-_;return g*g+m*m}class Pi{constructor(...e){this.type="polygon";let t=Array.isArray(e[0])?e[0]:e;if(typeof t[0]!="number"){const i=[];for(let r=0,n=t.length;r<n;r++)i.push(t[r].x,t[r].y);t=i}this.points=t,this.closePath=!0}clone(){const e=this.points.slice(),t=new Pi(e);return t.closePath=this.closePath,t}contains(e,t){let i=!1;const r=this.points.length/2;for(let n=0,o=r-1;n<r;o=n++){const a=this.points[n*2],l=this.points[n*2+1],h=this.points[o*2],c=this.points[o*2+1];l>t!=c>t&&e<(h-a)*((t-l)/(c-l))+a&&(i=!i)}return i}strokeContains(e,t,i,r=.5){const n=i*i,o=n*(1-r),a=n-o,{points:l}=this,h=l.length-(this.closePath?0:2);for(let c=0;c<h;c+=2){const u=l[c],f=l[c+1],d=l[(c+2)%l.length],_=l[(c+3)%l.length],g=Od(e,t,u,f,d,_),m=Math.sign((d-u)*(t-f)-(_-f)*(e-u));if(g<=(m<0?a:o))return!0}return!1}getBounds(e){e||(e=new Pe);const t=this.points;let i=1/0,r=-1/0,n=1/0,o=-1/0;for(let a=0,l=t.length;a<l;a+=2){const h=t[a],c=t[a+1];i=h<i?h:i,r=h>r?h:r,n=c<n?c:n,o=c>o?c:o}return e.x=i,e.width=r-i,e.y=n,e.height=o-n,e}copyFrom(e){return this.points=e.points.slice(),this.closePath=e.closePath,this}copyTo(e){return e.copyFrom(this),e}toString(){return`[pixi.js/math:PolygoncloseStroke=${this.closePath}points=${this.points.reduce((e,t)=>`${e}, ${t}`,"")}]`}get lastX(){return this.points[this.points.length-2]}get lastY(){return this.points[this.points.length-1]}get x(){return this.points[this.points.length-2]}get y(){return this.points[this.points.length-1]}}const $i=(s,e,t,i,r,n,o)=>{const a=s-t,l=e-i,h=Math.sqrt(a*a+l*l);return h>=r-n&&h<=r+o};class Zr{constructor(e=0,t=0,i=0,r=0,n=20){this.type="roundedRectangle",this.x=e,this.y=t,this.width=i,this.height=r,this.radius=n}getBounds(e){return e||(e=new Pe),e.x=this.x,e.y=this.y,e.width=this.width,e.height=this.height,e}clone(){return new Zr(this.x,this.y,this.width,this.height,this.radius)}copyFrom(e){return this.x=e.x,this.y=e.y,this.width=e.width,this.height=e.height,this}copyTo(e){return e.copyFrom(this),e}contains(e,t){if(this.width<=0||this.height<=0)return!1;if(e>=this.x&&e<=this.x+this.width&&t>=this.y&&t<=this.y+this.height){const i=Math.max(0,Math.min(this.radius,Math.min(this.width,this.height)/2));if(t>=this.y+i&&t<=this.y+this.height-i||e>=this.x+i&&e<=this.x+this.width-i)return!0;let r=e-(this.x+i),n=t-(this.y+i);const o=i*i;if(r*r+n*n<=o||(r=e-(this.x+this.width-i),r*r+n*n<=o)||(n=t-(this.y+this.height-i),r*r+n*n<=o)||(r=e-(this.x+i),r*r+n*n<=o))return!0}return!1}strokeContains(e,t,i,r=.5){const{x:n,y:o,width:a,height:l,radius:h}=this,c=i*(1-r),u=i-c,f=n+h,d=o+h,_=a-h*2,g=l-h*2,m=n+a,p=o+l;return(e>=n-c&&e<=n+u||e>=m-u&&e<=m+c)&&t>=d&&t<=d+g||(t>=o-c&&t<=o+u||t>=p-u&&t<=p+c)&&e>=f&&e<=f+_?!0:e<f&&t<d&&$i(e,t,f,d,h,u,c)||e>m-h&&t<d&&$i(e,t,m-h,d,h,u,c)||e>m-h&&t>p-h&&$i(e,t,m-h,p-h,h,u,c)||e<f&&t>p-h&&$i(e,t,f,p-h,h,u,c)}toString(){return`[pixi.js/math:RoundedRectangle x=${this.x} y=${this.y}width=${this.width} height=${this.height} radius=${this.radius}]`}}const Gd=["precision mediump float;","void main(void){","float test = 0.1;","%forloop%","gl_FragColor = vec4(0.0);","}"].join(`
`);function Dd(s){let e="";for(let t=0;t<s;++t)t>0&&(e+=`
else `),t<s-1&&(e+=`if(test == ${t}.0){}`);return e}function Nd(s,e){if(s===0)throw new Error("Invalid value of `0` passed to `checkMaxIfStatementsInShader`");const t=e.createShader(e.FRAGMENT_SHADER);try{for(;;){const i=Gd.replace(/%forloop%/gi,Dd(s));if(e.shaderSource(t,i),e.compileShader(t),!e.getShaderParameter(t,e.COMPILE_STATUS))s=s/2|0;else break}}finally{e.deleteShader(t)}return s}let oi=null;function Sl(){var e;if(oi)return oi;const s=rl();return oi=s.getParameter(s.MAX_TEXTURE_IMAGE_UNITS),oi=Nd(oi,s),(e=s.getExtension("WEBGL_lose_context"))==null||e.loseContext(),oi}const Cl={};function Ud(s,e){let t=2166136261;for(let i=0;i<e;i++)t^=s[i].uid,t=Math.imul(t,16777619),t>>>=0;return Cl[t]||Wd(s,e,t)}let er=0;function Wd(s,e,t){const i={};let r=0;er||(er=Sl());for(let o=0;o<er;o++){const a=o<e?s[o]:_e.EMPTY.source;i[r++]=a.source,i[r++]=a.style}const n=new ns(i);return Cl[t]=n,n}class zn{constructor(e){typeof e=="number"?this.rawBinaryData=new ArrayBuffer(e):e instanceof Uint8Array?this.rawBinaryData=e.buffer:this.rawBinaryData=e,this.uint32View=new Uint32Array(this.rawBinaryData),this.float32View=new Float32Array(this.rawBinaryData),this.size=this.rawBinaryData.byteLength}get int8View(){return this._int8View||(this._int8View=new Int8Array(this.rawBinaryData)),this._int8View}get uint8View(){return this._uint8View||(this._uint8View=new Uint8Array(this.rawBinaryData)),this._uint8View}get int16View(){return this._int16View||(this._int16View=new Int16Array(this.rawBinaryData)),this._int16View}get int32View(){return this._int32View||(this._int32View=new Int32Array(this.rawBinaryData)),this._int32View}get float64View(){return this._float64Array||(this._float64Array=new Float64Array(this.rawBinaryData)),this._float64Array}get bigUint64View(){return this._bigUint64Array||(this._bigUint64Array=new BigUint64Array(this.rawBinaryData)),this._bigUint64Array}view(e){return this[`${e}View`]}destroy(){this.rawBinaryData=null,this._int8View=null,this._uint8View=null,this._int16View=null,this.uint16View=null,this._int32View=null,this.uint32View=null,this.float32View=null}static sizeOf(e){switch(e){case"int8":case"uint8":return 1;case"int16":case"uint16":return 2;case"int32":case"uint32":case"float32":return 4;default:throw new Error(`${e} isn't a valid view type`)}}}function Rn(s,e){const t=s.byteLength/8|0,i=new Float64Array(s,0,t);new Float64Array(e,0,t).set(i);const n=s.byteLength-t*8;if(n>0){const o=new Uint8Array(s,t*8,n);new Uint8Array(e,t*8,n).set(o)}}const Hd={normal:"normal-npm",add:"add-npm",screen:"screen-npm"};var Vd=(s=>(s[s.DISABLED=0]="DISABLED",s[s.RENDERING_MASK_ADD=1]="RENDERING_MASK_ADD",s[s.MASK_ACTIVE=2]="MASK_ACTIVE",s[s.INVERSE_MASK_ACTIVE=3]="INVERSE_MASK_ACTIVE",s[s.RENDERING_MASK_REMOVE=4]="RENDERING_MASK_REMOVE",s[s.NONE=5]="NONE",s))(Vd||{});function Bn(s,e){return e.alphaMode==="no-premultiply-alpha"&&Hd[s]||s}class Xd{constructor(){this.ids=Object.create(null),this.textures=[],this.count=0}clear(){for(let e=0;e<this.count;e++){const t=this.textures[e];this.textures[e]=null,this.ids[t.uid]=null}this.count=0}}class Yd{constructor(){this.renderPipeId="batch",this.action="startBatch",this.start=0,this.size=0,this.textures=new Xd,this.blendMode="normal",this.topology="triangle-strip",this.canBundle=!0}destroy(){this.textures=null,this.gpuBindGroup=null,this.bindGroup=null,this.batcher=null}}const Ml=[];let Pr=0;function Fn(){return Pr>0?Ml[--Pr]:new Yd}function Ln(s){Ml[Pr++]=s}let ki=0;const Tl=class os{constructor(e={}){this.uid=He("batcher"),this.dirty=!0,this.batchIndex=0,this.batches=[],this._elements=[],os.defaultOptions.maxTextures=os.defaultOptions.maxTextures??Sl(),e={...os.defaultOptions,...e};const{maxTextures:t,attributesInitialSize:i,indicesInitialSize:r}=e;this.attributeBuffer=new zn(i*4),this.indexBuffer=new Uint16Array(r),this.maxTextures=t}begin(){this.elementSize=0,this.elementStart=0,this.indexSize=0,this.attributeSize=0;for(let e=0;e<this.batchIndex;e++)Ln(this.batches[e]);this.batchIndex=0,this._batchIndexStart=0,this._batchIndexSize=0,this.dirty=!0}add(e){this._elements[this.elementSize++]=e,e._indexStart=this.indexSize,e._attributeStart=this.attributeSize,e._batcher=this,this.indexSize+=e.indexSize,this.attributeSize+=e.attributeSize*this.vertexSize}checkAndUpdateTexture(e,t){const i=e._batch.textures.ids[t._source.uid];return!i&&i!==0?!1:(e._textureId=i,e.texture=t,!0)}updateElement(e){this.dirty=!0;const t=this.attributeBuffer;e.packAsQuad?this.packQuadAttributes(e,t.float32View,t.uint32View,e._attributeStart,e._textureId):this.packAttributes(e,t.float32View,t.uint32View,e._attributeStart,e._textureId)}break(e){const t=this._elements;if(!t[this.elementStart])return;let i=Fn(),r=i.textures;r.clear();const n=t[this.elementStart];let o=Bn(n.blendMode,n.texture._source),a=n.topology;this.attributeSize*4>this.attributeBuffer.size&&this._resizeAttributeBuffer(this.attributeSize*4),this.indexSize>this.indexBuffer.length&&this._resizeIndexBuffer(this.indexSize);const l=this.attributeBuffer.float32View,h=this.attributeBuffer.uint32View,c=this.indexBuffer;let u=this._batchIndexSize,f=this._batchIndexStart,d="startBatch";const _=this.maxTextures;for(let g=this.elementStart;g<this.elementSize;++g){const m=t[g];t[g]=null;const b=m.texture._source,y=Bn(m.blendMode,b),w=o!==y||a!==m.topology;if(b._batchTick===ki&&!w){m._textureId=b._textureBindLocation,u+=m.indexSize,m.packAsQuad?(this.packQuadAttributes(m,l,h,m._attributeStart,m._textureId),this.packQuadIndex(c,m._indexStart,m._attributeStart/this.vertexSize)):(this.packAttributes(m,l,h,m._attributeStart,m._textureId),this.packIndex(m,c,m._indexStart,m._attributeStart/this.vertexSize)),m._batch=i;continue}b._batchTick=ki,(r.count>=_||w)&&(this._finishBatch(i,f,u-f,r,o,a,e,d),d="renderBatch",f=u,o=y,a=m.topology,i=Fn(),r=i.textures,r.clear(),++ki),m._textureId=b._textureBindLocation=r.count,r.ids[b.uid]=r.count,r.textures[r.count++]=b,m._batch=i,u+=m.indexSize,m.packAsQuad?(this.packQuadAttributes(m,l,h,m._attributeStart,m._textureId),this.packQuadIndex(c,m._indexStart,m._attributeStart/this.vertexSize)):(this.packAttributes(m,l,h,m._attributeStart,m._textureId),this.packIndex(m,c,m._indexStart,m._attributeStart/this.vertexSize))}r.count>0&&(this._finishBatch(i,f,u-f,r,o,a,e,d),f=u,++ki),this.elementStart=this.elementSize,this._batchIndexStart=f,this._batchIndexSize=u}_finishBatch(e,t,i,r,n,o,a,l){e.gpuBindGroup=null,e.bindGroup=null,e.action=l,e.batcher=this,e.textures=r,e.blendMode=n,e.topology=o,e.start=t,e.size=i,++ki,this.batches[this.batchIndex++]=e,a.add(e)}finish(e){this.break(e)}ensureAttributeBuffer(e){e*4<=this.attributeBuffer.size||this._resizeAttributeBuffer(e*4)}ensureIndexBuffer(e){e<=this.indexBuffer.length||this._resizeIndexBuffer(e)}_resizeAttributeBuffer(e){const t=Math.max(e,this.attributeBuffer.size*2),i=new zn(t);Rn(this.attributeBuffer.rawBinaryData,i.rawBinaryData),this.attributeBuffer=i}_resizeIndexBuffer(e){const t=this.indexBuffer;let i=Math.max(e,t.length*1.5);i+=i%2;const r=i>65535?new Uint32Array(i):new Uint16Array(i);if(r.BYTES_PER_ELEMENT!==t.BYTES_PER_ELEMENT)for(let n=0;n<t.length;n++)r[n]=t[n];else Rn(t.buffer,r.buffer);this.indexBuffer=r}packQuadIndex(e,t,i){e[t]=i+0,e[t+1]=i+1,e[t+2]=i+2,e[t+3]=i+0,e[t+4]=i+2,e[t+5]=i+3}packIndex(e,t,i,r){const n=e.indices,o=e.indexSize,a=e.indexOffset,l=e.attributeOffset;for(let h=0;h<o;h++)t[i++]=r+n[h+a]-l}destroy(){for(let e=0;e<this.batches.length;e++)Ln(this.batches[e]);this.batches=null;for(let e=0;e<this._elements.length;e++)this._elements[e]._batch=null;this._elements=null,this.indexBuffer=null,this.attributeBuffer.destroy(),this.attributeBuffer=null}};Tl.defaultOptions={maxTextures:null,attributesInitialSize:4,indicesInitialSize:6};let jd=Tl;var at=(s=>(s[s.MAP_READ=1]="MAP_READ",s[s.MAP_WRITE=2]="MAP_WRITE",s[s.COPY_SRC=4]="COPY_SRC",s[s.COPY_DST=8]="COPY_DST",s[s.INDEX=16]="INDEX",s[s.VERTEX=32]="VERTEX",s[s.UNIFORM=64]="UNIFORM",s[s.STORAGE=128]="STORAGE",s[s.INDIRECT=256]="INDIRECT",s[s.QUERY_RESOLVE=512]="QUERY_RESOLVE",s[s.STATIC=1024]="STATIC",s))(at||{});class Li extends Tt{constructor(e){let{data:t,size:i}=e;const{usage:r,label:n,shrinkToFit:o}=e;super(),this.uid=He("buffer"),this._resourceType="buffer",this._resourceId=He("resource"),this._touched=0,this._updateID=1,this._dataInt32=null,this.shrinkToFit=!0,this.destroyed=!1,t instanceof Array&&(t=new Float32Array(t)),this._data=t,i??(i=t==null?void 0:t.byteLength);const a=!!t;this.descriptor={size:i,usage:r,mappedAtCreation:a,label:n},this.shrinkToFit=o??!0}get data(){return this._data}set data(e){this.setDataWithSize(e,e.length,!0)}get dataInt32(){return this._dataInt32||(this._dataInt32=new Int32Array(this.data.buffer)),this._dataInt32}get static(){return!!(this.descriptor.usage&at.STATIC)}set static(e){e?this.descriptor.usage|=at.STATIC:this.descriptor.usage&=~at.STATIC}setDataWithSize(e,t,i){if(this._updateID++,this._updateSize=t*e.BYTES_PER_ELEMENT,this._data===e){i&&this.emit("update",this);return}const r=this._data;if(this._data=e,this._dataInt32=null,!r||r.length!==e.length){!this.shrinkToFit&&r&&e.byteLength<r.byteLength?i&&this.emit("update",this):(this.descriptor.size=e.byteLength,this._resourceId=He("resource"),this.emit("change",this));return}i&&this.emit("update",this)}update(e){this._updateSize=e??this._updateSize,this._updateID++,this.emit("update",this)}destroy(){this.destroyed=!0,this.emit("destroy",this),this.emit("change",this),this._data=null,this.descriptor=null,this.removeAllListeners()}}function Pl(s,e){if(!(s instanceof Li)){let t=e?at.INDEX:at.VERTEX;s instanceof Array&&(e?(s=new Uint32Array(s),t=at.INDEX|at.COPY_DST):(s=new Float32Array(s),t=at.VERTEX|at.COPY_DST)),s=new Li({data:s,label:e?"index-mesh-buffer":"vertex-mesh-buffer",usage:t})}return s}function qd(s,e,t){const i=s.getAttribute(e);if(!i)return t.minX=0,t.minY=0,t.maxX=0,t.maxY=0,t;const r=i.buffer.data;let n=1/0,o=1/0,a=-1/0,l=-1/0;const h=r.BYTES_PER_ELEMENT,c=(i.offset||0)/h,u=(i.stride||2*4)/h;for(let f=c;f<r.length;f+=u){const d=r[f],_=r[f+1];d>a&&(a=d),_>l&&(l=_),d<n&&(n=d),_<o&&(o=_)}return t.minX=n,t.minY=o,t.maxX=a,t.maxY=l,t}function Kd(s){return(s instanceof Li||Array.isArray(s)||s.BYTES_PER_ELEMENT)&&(s={buffer:s}),s.buffer=Pl(s.buffer,!1),s}class Zd extends Tt{constructor(e={}){super(),this.uid=He("geometry"),this._layoutKey=0,this.instanceCount=1,this._bounds=new Mt,this._boundsDirty=!0;const{attributes:t,indexBuffer:i,topology:r}=e;if(this.buffers=[],this.attributes={},t)for(const n in t)this.addAttribute(n,t[n]);this.instanceCount=e.instanceCount??1,i&&this.addIndex(i),this.topology=r||"triangle-list"}onBufferUpdate(){this._boundsDirty=!0,this.emit("update",this)}getAttribute(e){return this.attributes[e]}getIndex(){return this.indexBuffer}getBuffer(e){return this.getAttribute(e).buffer}getSize(){for(const e in this.attributes){const t=this.attributes[e];return t.buffer.data.length/(t.stride/4||t.size)}return 0}addAttribute(e,t){const i=Kd(t);this.buffers.indexOf(i.buffer)===-1&&(this.buffers.push(i.buffer),i.buffer.on("update",this.onBufferUpdate,this),i.buffer.on("change",this.onBufferUpdate,this)),this.attributes[e]=i}addIndex(e){this.indexBuffer=Pl(e,!0),this.buffers.push(this.indexBuffer)}get bounds(){return this._boundsDirty?(this._boundsDirty=!1,qd(this,"aPosition",this._bounds)):this._bounds}destroy(e=!1){this.emit("destroy",this),this.removeAllListeners(),e&&this.buffers.forEach(t=>t.destroy()),this.attributes=null,this.buffers=null,this.indexBuffer=null,this._bounds=null}}const Qd=new Float32Array(1),Jd=new Uint32Array(1);class $d extends Zd{constructor(){const t=new Li({data:Qd,label:"attribute-batch-buffer",usage:at.VERTEX|at.COPY_DST,shrinkToFit:!1}),i=new Li({data:Jd,label:"index-batch-buffer",usage:at.INDEX|at.COPY_DST,shrinkToFit:!1}),r=6*4;super({attributes:{aPosition:{buffer:t,format:"float32x2",stride:r,offset:0},aUV:{buffer:t,format:"float32x2",stride:r,offset:2*4},aColor:{buffer:t,format:"unorm8x4",stride:r,offset:4*4},aTextureIdAndRound:{buffer:t,format:"uint16x2",stride:r,offset:5*4}},indexBuffer:i})}}function On(s,e,t){if(s)for(const i in s){const r=i.toLocaleLowerCase(),n=e[r];if(n){let o=s[i];i==="header"&&(o=o.replace(/@in\s+[^;]+;\s*/g,"").replace(/@out\s+[^;]+;\s*/g,"")),t&&n.push(`//----${t}----//`),n.push(o)}else Ve(`${i} placement hook does not exist in shader`)}}const ef=/\{\{(.*?)\}\}/g;function Gn(s){var i;const e={};return(((i=s.match(ef))==null?void 0:i.map(r=>r.replace(/[{()}]/g,"")))??[]).forEach(r=>{e[r]=[]}),e}function Dn(s,e){let t;const i=/@in\s+([^;]+);/g;for(;(t=i.exec(s))!==null;)e.push(t[1])}function Nn(s,e,t=!1){const i=[];Dn(e,i),s.forEach(a=>{a.header&&Dn(a.header,i)});const r=i;t&&r.sort();const n=r.map((a,l)=>`       @location(${l}) ${a},`).join(`
`);let o=e.replace(/@in\s+[^;]+;\s*/g,"");return o=o.replace("{{in}}",`
${n}
`),o}function Un(s,e){let t;const i=/@out\s+([^;]+);/g;for(;(t=i.exec(s))!==null;)e.push(t[1])}function tf(s){const t=/\b(\w+)\s*:/g.exec(s);return t?t[1]:""}function sf(s){const e=/@.*?\s+/g;return s.replace(e,"")}function rf(s,e){const t=[];Un(e,t),s.forEach(l=>{l.header&&Un(l.header,t)});let i=0;const r=t.sort().map(l=>l.indexOf("builtin")>-1?l:`@location(${i++}) ${l}`).join(`,
`),n=t.sort().map(l=>`       var ${sf(l)};`).join(`
`),o=`return VSOutput(
            ${t.sort().map(l=>` ${tf(l)}`).join(`,
`)});`;let a=e.replace(/@out\s+[^;]+;\s*/g,"");return a=a.replace("{{struct}}",`
${r}
`),a=a.replace("{{start}}",`
${n}
`),a=a.replace("{{return}}",`
${o}
`),a}function Wn(s,e){let t=s;for(const i in e){const r=e[i];r.join(`
`).length?t=t.replace(`{{${i}}}`,`//-----${i} START-----//
${r.join(`
`)}
//----${i} FINISH----//`):t=t.replace(`{{${i}}}`,"")}return t}const Ht=Object.create(null),tr=new Map;let nf=0;function of({template:s,bits:e}){const t=Il(s,e);if(Ht[t])return Ht[t];const{vertex:i,fragment:r}=lf(s,e);return Ht[t]=El(i,r,e),Ht[t]}function af({template:s,bits:e}){const t=Il(s,e);return Ht[t]||(Ht[t]=El(s.vertex,s.fragment,e)),Ht[t]}function lf(s,e){const t=e.map(o=>o.vertex).filter(o=>!!o),i=e.map(o=>o.fragment).filter(o=>!!o);let r=Nn(t,s.vertex,!0);r=rf(t,r);const n=Nn(i,s.fragment,!0);return{vertex:r,fragment:n}}function Il(s,e){return e.map(t=>(tr.has(t)||tr.set(t,nf++),tr.get(t))).sort((t,i)=>t-i).join("-")+s.vertex+s.fragment}function El(s,e,t){const i=Gn(s),r=Gn(e);return t.forEach(n=>{On(n.vertex,i,n.name),On(n.fragment,r,n.name)}),{vertex:Wn(s,i),fragment:Wn(e,r)}}const hf=`
    @in aPosition: vec2<f32>;
    @in aUV: vec2<f32>;

    @out @builtin(position) vPosition: vec4<f32>;
    @out vUV : vec2<f32>;
    @out vColor : vec4<f32>;

    {{header}}

    struct VSOutput {
        {{struct}}
    };

    @vertex
    fn main( {{in}} ) -> VSOutput {

        var worldTransformMatrix = globalUniforms.uWorldTransformMatrix;
        var modelMatrix = mat3x3<f32>(
            1.0, 0.0, 0.0,
            0.0, 1.0, 0.0,
            0.0, 0.0, 1.0
          );
        var position = aPosition;
        var uv = aUV;

        {{start}}
        
        vColor = vec4<f32>(1., 1., 1., 1.);

        {{main}}

        vUV = uv;

        var modelViewProjectionMatrix = globalUniforms.uProjectionMatrix * worldTransformMatrix * modelMatrix;

        vPosition =  vec4<f32>((modelViewProjectionMatrix *  vec3<f32>(position, 1.0)).xy, 0.0, 1.0);
       
        vColor *= globalUniforms.uWorldColorAlpha;

        {{end}}

        {{return}}
    };
`,cf=`
    @in vUV : vec2<f32>;
    @in vColor : vec4<f32>;
   
    {{header}}

    @fragment
    fn main(
        {{in}}
      ) -> @location(0) vec4<f32> {
        
        {{start}}

        var outColor:vec4<f32>;
      
        {{main}}
        
        var finalColor:vec4<f32> = outColor * vColor;

        {{end}}

        return finalColor;
      };
`,uf=`
    in vec2 aPosition;
    in vec2 aUV;

    out vec4 vColor;
    out vec2 vUV;

    {{header}}

    void main(void){

        mat3 worldTransformMatrix = uWorldTransformMatrix;
        mat3 modelMatrix = mat3(
            1.0, 0.0, 0.0,
            0.0, 1.0, 0.0,
            0.0, 0.0, 1.0
          );
        vec2 position = aPosition;
        vec2 uv = aUV;
        
        {{start}}
        
        vColor = vec4(1.);
        
        {{main}}
        
        vUV = uv;
        
        mat3 modelViewProjectionMatrix = uProjectionMatrix * worldTransformMatrix * modelMatrix;

        gl_Position = vec4((modelViewProjectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);

        vColor *= uWorldColorAlpha;

        {{end}}
    }
`,df=`
   
    in vec4 vColor;
    in vec2 vUV;

    out vec4 finalColor;

    {{header}}

    void main(void) {
        
        {{start}}

        vec4 outColor;
      
        {{main}}
        
        finalColor = outColor * vColor;
        
        {{end}}
    }
`,ff={name:"global-uniforms-bit",vertex:{header:`
        struct GlobalUniforms {
            uProjectionMatrix:mat3x3<f32>,
            uWorldTransformMatrix:mat3x3<f32>,
            uWorldColorAlpha: vec4<f32>,
            uResolution: vec2<f32>,
        }

        @group(0) @binding(0) var<uniform> globalUniforms : GlobalUniforms;
        `}},_f={name:"global-uniforms-bit",vertex:{header:`
          uniform mat3 uProjectionMatrix;
          uniform mat3 uWorldTransformMatrix;
          uniform vec4 uWorldColorAlpha;
          uniform vec2 uResolution;
        `}};function gf({bits:s,name:e}){const t=of({template:{fragment:cf,vertex:hf},bits:[ff,...s]});return Vt.from({name:e,vertex:{source:t.vertex,entryPoint:"main"},fragment:{source:t.fragment,entryPoint:"main"}})}function mf({bits:s,name:e}){return new _i({name:e,...af({template:{vertex:uf,fragment:df},bits:[_f,...s]})})}const pf={name:"color-bit",vertex:{header:`
            @in aColor: vec4<f32>;
        `,main:`
            vColor *= vec4<f32>(aColor.rgb * aColor.a, aColor.a);
        `}},bf={name:"color-bit",vertex:{header:`
            in vec4 aColor;
        `,main:`
            vColor *= vec4(aColor.rgb * aColor.a, aColor.a);
        `}},ir={};function yf(s){const e=[];if(s===1)e.push("@group(1) @binding(0) var textureSource1: texture_2d<f32>;"),e.push("@group(1) @binding(1) var textureSampler1: sampler;");else{let t=0;for(let i=0;i<s;i++)e.push(`@group(1) @binding(${t++}) var textureSource${i+1}: texture_2d<f32>;`),e.push(`@group(1) @binding(${t++}) var textureSampler${i+1}: sampler;`)}return e.join(`
`)}function wf(s){const e=[];if(s===1)e.push("outColor = textureSampleGrad(textureSource1, textureSampler1, vUV, uvDx, uvDy);");else{e.push("switch vTextureId {");for(let t=0;t<s;t++)t===s-1?e.push("  default:{"):e.push(`  case ${t}:{`),e.push(`      outColor = textureSampleGrad(textureSource${t+1}, textureSampler${t+1}, vUV, uvDx, uvDy);`),e.push("      break;}");e.push("}")}return e.join(`
`)}function xf(s){return ir[s]||(ir[s]={name:"texture-batch-bit",vertex:{header:`
                @in aTextureIdAndRound: vec2<u32>;
                @out @interpolate(flat) vTextureId : u32;
            `,main:`
                vTextureId = aTextureIdAndRound.y;
            `,end:`
                if(aTextureIdAndRound.x == 1)
                {
                    vPosition = vec4<f32>(roundPixels(vPosition.xy, globalUniforms.uResolution), vPosition.zw);
                }
            `},fragment:{header:`
                @in @interpolate(flat) vTextureId: u32;

                ${yf(s)}
            `,main:`
                var uvDx = dpdx(vUV);
                var uvDy = dpdy(vUV);

                ${wf(s)}
            `}}),ir[s]}const sr={};function vf(s){const e=[];for(let t=0;t<s;t++)t>0&&e.push("else"),t<s-1&&e.push(`if(vTextureId < ${t}.5)`),e.push("{"),e.push(`	outColor = texture(uTextures[${t}], vUV);`),e.push("}");return e.join(`
`)}function kf(s){return sr[s]||(sr[s]={name:"texture-batch-bit",vertex:{header:`
                in vec2 aTextureIdAndRound;
                out float vTextureId;

            `,main:`
                vTextureId = aTextureIdAndRound.y;
            `,end:`
                if(aTextureIdAndRound.x == 1.)
                {
                    gl_Position.xy = roundPixels(gl_Position.xy, uResolution);
                }
            `},fragment:{header:`
                in float vTextureId;

                uniform sampler2D uTextures[${s}];

            `,main:`

                ${vf(s)}
            `}}),sr[s]}const Af={name:"round-pixels-bit",vertex:{header:`
            fn roundPixels(position: vec2<f32>, targetSize: vec2<f32>) -> vec2<f32> 
            {
                return (floor(((position * 0.5 + 0.5) * targetSize) + 0.5) / targetSize) * 2.0 - 1.0;
            }
        `}},Sf={name:"round-pixels-bit",vertex:{header:`   
            vec2 roundPixels(vec2 position, vec2 targetSize)
            {       
                return (floor(((position * 0.5 + 0.5) * targetSize) + 0.5) / targetSize) * 2.0 - 1.0;
            }
        `}},Hn={};function Cf(s){let e=Hn[s];if(e)return e;const t=new Int32Array(s);for(let i=0;i<s;i++)t[i]=i;return e=Hn[s]=new hl({uTextures:{value:t,type:"i32",size:s}},{isStatic:!0}),e}class Mf extends ys{constructor(e){const t=mf({name:"batch",bits:[bf,kf(e),Sf]}),i=gf({name:"batch",bits:[pf,xf(e),Af]});super({glProgram:t,gpuProgram:i,resources:{batchSamplers:Cf(e)}})}}let Vn=null;const zl=class Rl extends jd{constructor(){super(...arguments),this.geometry=new $d,this.shader=Vn||(Vn=new Mf(this.maxTextures)),this.name=Rl.extension.name,this.vertexSize=6}packAttributes(e,t,i,r,n){const o=n<<16|e.roundPixels&65535,a=e.transform,l=a.a,h=a.b,c=a.c,u=a.d,f=a.tx,d=a.ty,{positions:_,uvs:g}=e,m=e.color,p=e.attributeOffset,b=p+e.attributeSize;for(let y=p;y<b;y++){const w=y*2,A=_[w],T=_[w+1];t[r++]=l*A+c*T+f,t[r++]=u*T+h*A+d,t[r++]=g[w],t[r++]=g[w+1],i[r++]=m,i[r++]=o}}packQuadAttributes(e,t,i,r,n){const o=e.texture,a=e.transform,l=a.a,h=a.b,c=a.c,u=a.d,f=a.tx,d=a.ty,_=e.bounds,g=_.maxX,m=_.minX,p=_.maxY,b=_.minY,y=o.uvs,w=e.color,A=n<<16|e.roundPixels&65535;t[r+0]=l*m+c*b+f,t[r+1]=u*b+h*m+d,t[r+2]=y.x0,t[r+3]=y.y0,i[r+4]=w,i[r+5]=A,t[r+6]=l*g+c*b+f,t[r+7]=u*b+h*g+d,t[r+8]=y.x1,t[r+9]=y.y1,i[r+10]=w,i[r+11]=A,t[r+12]=l*g+c*p+f,t[r+13]=u*p+h*g+d,t[r+14]=y.x2,t[r+15]=y.y2,i[r+16]=w,i[r+17]=A,t[r+18]=l*m+c*p+f,t[r+19]=u*p+h*m+d,t[r+20]=y.x3,t[r+21]=y.y3,i[r+22]=w,i[r+23]=A}};zl.extension={type:[J.Batcher],name:"default"};let Tf=zl;function Pf(s,e,t,i,r,n,o,a=null){let l=0;t*=e,r*=n;const h=a.a,c=a.b,u=a.c,f=a.d,d=a.tx,_=a.ty;for(;l<o;){const g=s[t],m=s[t+1];i[r]=h*g+u*m+d,i[r+1]=c*g+f*m+_,r+=n,t+=e,l++}}function If(s,e,t,i){let r=0;for(e*=t;r<i;)s[e]=0,s[e+1]=0,e+=t,r++}function Bl(s,e,t,i,r){const n=e.a,o=e.b,a=e.c,l=e.d,h=e.tx,c=e.ty;t||(t=0),i||(i=2),r||(r=s.length/i-t);let u=t*i;for(let f=0;f<r;f++){const d=s[u],_=s[u+1];s[u]=n*d+a*_+h,s[u+1]=o*d+l*_+c,u+=i}}const Ef=new we;class Fl{constructor(){this.packAsQuad=!1,this.batcherName="default",this.topology="triangle-list",this.applyTransform=!0,this.roundPixels=0,this._batcher=null,this._batch=null}get uvs(){return this.geometryData.uvs}get positions(){return this.geometryData.vertices}get indices(){return this.geometryData.indices}get blendMode(){return this.applyTransform?this.renderable.groupBlendMode:"normal"}get color(){const e=this.baseColor,t=e>>16|e&65280|(e&255)<<16,i=this.renderable;return i?Ga(t,i.groupColor)+(this.alpha*i.groupAlpha*255<<24):t+(this.alpha*255<<24)}get transform(){var e;return((e=this.renderable)==null?void 0:e.groupTransform)||Ef}copyTo(e){e.indexOffset=this.indexOffset,e.indexSize=this.indexSize,e.attributeOffset=this.attributeOffset,e.attributeSize=this.attributeSize,e.baseColor=this.baseColor,e.alpha=this.alpha,e.texture=this.texture,e.geometryData=this.geometryData,e.topology=this.topology}reset(){this.applyTransform=!0,this.renderable=null,this.topology="triangle-list"}}const Oi={extension:{type:J.ShapeBuilder,name:"circle"},build(s,e){let t,i,r,n,o,a;if(s.type==="circle"){const w=s;t=w.x,i=w.y,o=a=w.radius,r=n=0}else if(s.type==="ellipse"){const w=s;t=w.x,i=w.y,o=w.halfWidth,a=w.halfHeight,r=n=0}else{const w=s,A=w.width/2,T=w.height/2;t=w.x+A,i=w.y+T,o=a=Math.max(0,Math.min(w.radius,Math.min(A,T))),r=A-o,n=T-a}if(!(o>=0&&a>=0&&r>=0&&n>=0))return e;const l=Math.ceil(2.3*Math.sqrt(o+a)),h=l*8+(r?4:0)+(n?4:0);if(h===0)return e;if(l===0)return e[0]=e[6]=t+r,e[1]=e[3]=i+n,e[2]=e[4]=t-r,e[5]=e[7]=i-n,e;let c=0,u=l*4+(r?2:0)+2,f=u,d=h,_=r+o,g=n,m=t+_,p=t-_,b=i+g;if(e[c++]=m,e[c++]=b,e[--u]=b,e[--u]=p,n){const w=i-g;e[f++]=p,e[f++]=w,e[--d]=w,e[--d]=m}for(let w=1;w<l;w++){const A=Math.PI/2*(w/l),T=r+Math.cos(A)*o,P=n+Math.sin(A)*a,U=t+T,R=t-T,E=i+P,M=i-P;e[c++]=U,e[c++]=E,e[--u]=E,e[--u]=R,e[f++]=R,e[f++]=M,e[--d]=M,e[--d]=U}_=r,g=n+a,m=t+_,p=t-_,b=i+g;const y=i-g;return e[c++]=m,e[c++]=b,e[--d]=y,e[--d]=m,r&&(e[c++]=p,e[c++]=b,e[--d]=y,e[--d]=p),e},triangulate(s,e,t,i,r,n){if(s.length===0)return;let o=0,a=0;for(let c=0;c<s.length;c+=2)o+=s[c],a+=s[c+1];o/=s.length/2,a/=s.length/2;let l=i;e[l*t]=o,e[l*t+1]=a;const h=l++;for(let c=0;c<s.length;c+=2)e[l*t]=s[c],e[l*t+1]=s[c+1],c>0&&(r[n++]=l,r[n++]=h,r[n++]=l-1),l++;r[n++]=h+1,r[n++]=h,r[n++]=l-1}},zf={...Oi,extension:{...Oi.extension,name:"ellipse"}},Rf={...Oi,extension:{...Oi.extension,name:"roundedRectangle"}},Ll=1e-4,Xn=1e-4;function Bf(s){const e=s.length;if(e<6)return 1;let t=0;for(let i=0,r=s[e-2],n=s[e-1];i<e;i+=2){const o=s[i],a=s[i+1];t+=(o-r)*(a+n),r=o,n=a}return t<0?-1:1}function Yn(s,e,t,i,r,n,o,a){const l=s-t*r,h=e-i*r,c=s+t*n,u=e+i*n;let f,d;o?(f=i,d=-t):(f=-i,d=t);const _=l+f,g=h+d,m=c+f,p=u+d;return a.push(_,g),a.push(m,p),2}function Yt(s,e,t,i,r,n,o,a){const l=t-s,h=i-e;let c=Math.atan2(l,h),u=Math.atan2(r-s,n-e);a&&c<u?c+=Math.PI*2:!a&&c>u&&(u+=Math.PI*2);let f=c;const d=u-c,_=Math.abs(d),g=Math.sqrt(l*l+h*h),m=(15*_*Math.sqrt(g)/Math.PI>>0)+1,p=d/m;if(f+=p,a){o.push(s,e),o.push(t,i);for(let b=1,y=f;b<m;b++,y+=p)o.push(s,e),o.push(s+Math.sin(y)*g,e+Math.cos(y)*g);o.push(s,e),o.push(r,n)}else{o.push(t,i),o.push(s,e);for(let b=1,y=f;b<m;b++,y+=p)o.push(s+Math.sin(y)*g,e+Math.cos(y)*g),o.push(s,e);o.push(r,n),o.push(s,e)}return m*2}function Ff(s,e,t,i,r,n){const o=Ll;if(s.length===0)return;const a=e;let l=a.alignment;if(e.alignment!==.5){let le=Bf(s);l=(l-.5)*le+.5}const h=new fe(s[0],s[1]),c=new fe(s[s.length-2],s[s.length-1]),u=i,f=Math.abs(h.x-c.x)<o&&Math.abs(h.y-c.y)<o;if(u){s=s.slice(),f&&(s.pop(),s.pop(),c.set(s[s.length-2],s[s.length-1]));const le=(h.x+c.x)*.5,Te=(c.y+h.y)*.5;s.unshift(le,Te),s.push(le,Te)}const d=r,_=s.length/2;let g=s.length;const m=d.length/2,p=a.width/2,b=p*p,y=a.miterLimit*a.miterLimit;let w=s[0],A=s[1],T=s[2],P=s[3],U=0,R=0,E=-(A-P),M=w-T,z=0,V=0,W=Math.sqrt(E*E+M*M);E/=W,M/=W,E*=p,M*=p;const ae=l,H=(1-ae)*2,q=ae*2;u||(a.cap==="round"?g+=Yt(w-E*(H-q)*.5,A-M*(H-q)*.5,w-E*H,A-M*H,w+E*q,A+M*q,d,!0)+2:a.cap==="square"&&(g+=Yn(w,A,E,M,H,q,!0,d))),d.push(w-E*H,A-M*H),d.push(w+E*q,A+M*q);for(let le=1;le<_-1;++le){w=s[(le-1)*2],A=s[(le-1)*2+1],T=s[le*2],P=s[le*2+1],U=s[(le+1)*2],R=s[(le+1)*2+1],E=-(A-P),M=w-T,W=Math.sqrt(E*E+M*M),E/=W,M/=W,E*=p,M*=p,z=-(P-R),V=T-U,W=Math.sqrt(z*z+V*V),z/=W,V/=W,z*=p,V*=p;const Te=T-w,N=A-P,Ge=T-U,X=R-P,$e=Te*Ge+N*X,Xe=N*Ge-X*Te,De=Xe<0;if(Math.abs(Xe)<.001*Math.abs($e)){d.push(T-E*H,P-M*H),d.push(T+E*q,P+M*q),$e>=0&&(a.join==="round"?g+=Yt(T,P,T-E*H,P-M*H,T-z*H,P-V*H,d,!1)+4:g+=2,d.push(T-z*q,P-V*q),d.push(T+z*H,P+V*H));continue}const mt=(-E+w)*(-M+P)-(-E+T)*(-M+A),ge=(-z+U)*(-V+P)-(-z+T)*(-V+R),Qe=(Te*ge-Ge*mt)/Xe,We=(X*mt-N*ge)/Xe,Ye=(Qe-T)*(Qe-T)+(We-P)*(We-P),F=T+(Qe-T)*H,Ie=P+(We-P)*H,et=T-(Qe-T)*q,lt=P-(We-P)*q,Ze=Math.min(Te*Te+N*N,Ge*Ge+X*X),G=De?H:q,Je=Ze+G*G*b;Ye<=Je?a.join==="bevel"||Ye/b>y?(De?(d.push(F,Ie),d.push(T+E*q,P+M*q),d.push(F,Ie),d.push(T+z*q,P+V*q)):(d.push(T-E*H,P-M*H),d.push(et,lt),d.push(T-z*H,P-V*H),d.push(et,lt)),g+=2):a.join==="round"?De?(d.push(F,Ie),d.push(T+E*q,P+M*q),g+=Yt(T,P,T+E*q,P+M*q,T+z*q,P+V*q,d,!0)+4,d.push(F,Ie),d.push(T+z*q,P+V*q)):(d.push(T-E*H,P-M*H),d.push(et,lt),g+=Yt(T,P,T-E*H,P-M*H,T-z*H,P-V*H,d,!1)+4,d.push(T-z*H,P-V*H),d.push(et,lt)):(d.push(F,Ie),d.push(et,lt)):(d.push(T-E*H,P-M*H),d.push(T+E*q,P+M*q),a.join==="round"?De?g+=Yt(T,P,T+E*q,P+M*q,T+z*q,P+V*q,d,!0)+2:g+=Yt(T,P,T-E*H,P-M*H,T-z*H,P-V*H,d,!1)+2:a.join==="miter"&&Ye/b<=y&&(De?(d.push(et,lt),d.push(et,lt)):(d.push(F,Ie),d.push(F,Ie)),g+=2),d.push(T-z*H,P-V*H),d.push(T+z*q,P+V*q),g+=2)}w=s[(_-2)*2],A=s[(_-2)*2+1],T=s[(_-1)*2],P=s[(_-1)*2+1],E=-(A-P),M=w-T,W=Math.sqrt(E*E+M*M),E/=W,M/=W,E*=p,M*=p,d.push(T-E*H,P-M*H),d.push(T+E*q,P+M*q),u||(a.cap==="round"?g+=Yt(T-E*(H-q)*.5,P-M*(H-q)*.5,T-E*H,P-M*H,T+E*q,P+M*q,d,!1)+2:a.cap==="square"&&(g+=Yn(T,P,E,M,H,q,!1,d)));const Ke=Xn*Xn;for(let le=m;le<g+m-2;++le)w=d[le*2],A=d[le*2+1],T=d[(le+1)*2],P=d[(le+1)*2+1],U=d[(le+2)*2],R=d[(le+2)*2+1],!(Math.abs(w*(P-R)+T*(R-A)+U*(A-P))<Ke)&&n.push(le,le+1,le+2)}function Lf(s,e,t,i){const r=Ll;if(s.length===0)return;const n=s[0],o=s[1],a=s[s.length-2],l=s[s.length-1],h=e||Math.abs(n-a)<r&&Math.abs(o-l)<r,c=t,u=s.length/2,f=c.length/2;for(let d=0;d<u;d++)c.push(s[d*2]),c.push(s[d*2+1]);for(let d=0;d<u-1;d++)i.push(f+d,f+d+1);h&&i.push(f+u-1,f)}function Ol(s,e,t,i,r,n,o){const a=kd(s,e,2);if(!a)return;for(let h=0;h<a.length;h+=3)n[o++]=a[h]+r,n[o++]=a[h+1]+r,n[o++]=a[h+2]+r;let l=r*i;for(let h=0;h<s.length;h+=2)t[l]=s[h],t[l+1]=s[h+1],l+=i}const Of=[],Gf={extension:{type:J.ShapeBuilder,name:"polygon"},build(s,e){for(let t=0;t<s.points.length;t++)e[t]=s.points[t];return e},triangulate(s,e,t,i,r,n){Ol(s,Of,e,t,i,r,n)}},Df={extension:{type:J.ShapeBuilder,name:"rectangle"},build(s,e){const t=s,i=t.x,r=t.y,n=t.width,o=t.height;return n>=0&&o>=0&&(e[0]=i,e[1]=r,e[2]=i+n,e[3]=r,e[4]=i+n,e[5]=r+o,e[6]=i,e[7]=r+o),e},triangulate(s,e,t,i,r,n){let o=0;i*=t,e[i+o]=s[0],e[i+o+1]=s[1],o+=t,e[i+o]=s[2],e[i+o+1]=s[3],o+=t,e[i+o]=s[6],e[i+o+1]=s[7],o+=t,e[i+o]=s[4],e[i+o+1]=s[5],o+=t;const a=i/t;r[n++]=a,r[n++]=a+1,r[n++]=a+2,r[n++]=a+1,r[n++]=a+3,r[n++]=a+2}},Nf={extension:{type:J.ShapeBuilder,name:"triangle"},build(s,e){return e[0]=s.x,e[1]=s.y,e[2]=s.x2,e[3]=s.y2,e[4]=s.x3,e[5]=s.y3,e},triangulate(s,e,t,i,r,n){let o=0;i*=t,e[i+o]=s[0],e[i+o+1]=s[1],o+=t,e[i+o]=s[2],e[i+o+1]=s[3],o+=t,e[i+o]=s[4],e[i+o+1]=s[5];const a=i/t;r[n++]=a,r[n++]=a+1,r[n++]=a+2}},ks={};rt.handleByMap(J.ShapeBuilder,ks);rt.add(Df,Gf,Nf,Oi,zf,Rf);const Uf=new Pe;function Wf(s,e){const{geometryData:t,batches:i}=e;i.length=0,t.indices.length=0,t.vertices.length=0,t.uvs.length=0;for(let r=0;r<s.instructions.length;r++){const n=s.instructions[r];if(n.action==="texture")Hf(n.data,i,t);else if(n.action==="fill"||n.action==="stroke"){const o=n.action==="stroke",a=n.data.path.shapePath,l=n.data.style,h=n.data.hole;o&&h&&jn(h.shapePath,l,null,!0,i,t),jn(a,l,h,o,i,t)}}}function Hf(s,e,t){const{vertices:i,uvs:r,indices:n}=t,o=n.length,a=i.length/2,l=[],h=ks.rectangle,c=Uf,u=s.image;c.x=s.dx,c.y=s.dy,c.width=s.dw,c.height=s.dh;const f=s.transform;h.build(c,l),f&&Bl(l,f),h.triangulate(l,i,2,a,n,o);const d=u.uvs;r.push(d.x0,d.y0,d.x1,d.y1,d.x3,d.y3,d.x2,d.y2);const _=Gt.get(Fl);_.indexOffset=o,_.indexSize=n.length-o,_.attributeOffset=a,_.attributeSize=i.length/2-a,_.baseColor=s.style,_.alpha=s.alpha,_.texture=u,_.geometryData=t,e.push(_)}function jn(s,e,t,i,r,n){const{vertices:o,uvs:a,indices:l}=n,h=s.shapePrimitives.length-1;s.shapePrimitives.forEach(({shape:c,transform:u},f)=>{const d=l.length,_=o.length/2,g=[],m=ks[c.type];let p="triangle-list";if(m.build(c,g),u&&Bl(g,u),i){const A=c.closePath??!0,T=e;T.pixelLine?(Lf(g,A,o,l),p="line-list"):Ff(g,T,!1,A,o,l)}else if(t&&h===f){h!==0&&console.warn("[Pixi Graphics] only the last shape have be cut out");const A=[],T=g.slice();Vf(t.shapePath).forEach(U=>{A.push(T.length/2),T.push(...U)}),Ol(T,A,o,2,_,l,d)}else m.triangulate(g,o,2,_,l,d);const b=a.length/2,y=e.texture;if(y!==_e.WHITE){const A=e.matrix;A&&(u&&A.append(u.clone().invert()),Pf(o,2,_,a,b,2,o.length/2-_,A))}else If(a,b,2,o.length/2-_);const w=Gt.get(Fl);w.indexOffset=d,w.indexSize=l.length-d,w.attributeOffset=_,w.attributeSize=o.length/2-_,w.baseColor=e.color,w.alpha=e.alpha,w.texture=y,w.geometryData=n,w.topology=p,r.push(w)})}function Vf(s){if(!s)return[];const e=s.shapePrimitives,t=[];for(let i=0;i<e.length;i++){const r=e[i].shape,n=[];ks[r.type].build(r,n),t.push(n)}return t}class Xf{constructor(){this.batches=[],this.geometryData={vertices:[],uvs:[],indices:[]}}}class Yf{constructor(){this.batcher=new Tf,this.instructions=new Wa}init(){this.instructions.reset()}get geometry(){return be(Xc,"GraphicsContextRenderData#geometry is deprecated, please use batcher.geometry instead."),this.batcher.geometry}}const Qr=class Ir{constructor(e){this._gpuContextHash={},this._graphicsDataContextHash=Object.create(null),e.renderableGC.addManagedHash(this,"_gpuContextHash"),e.renderableGC.addManagedHash(this,"_graphicsDataContextHash")}init(e){Ir.defaultOptions.bezierSmoothness=(e==null?void 0:e.bezierSmoothness)??Ir.defaultOptions.bezierSmoothness}getContextRenderData(e){return this._graphicsDataContextHash[e.uid]||this._initContextRenderData(e)}updateGpuContext(e){let t=this._gpuContextHash[e.uid]||this._initContext(e);if(e.dirty){t?this._cleanGraphicsContextData(e):t=this._initContext(e),Wf(e,t);const i=e.batchMode;e.customShader||i==="no-batch"?t.isBatchable=!1:i==="auto"&&(t.isBatchable=t.geometryData.vertices.length<400),e.dirty=!1}return t}getGpuContext(e){return this._gpuContextHash[e.uid]||this._initContext(e)}_initContextRenderData(e){const t=Gt.get(Yf),{batches:i,geometryData:r}=this._gpuContextHash[e.uid],n=r.vertices.length,o=r.indices.length;for(let c=0;c<i.length;c++)i[c].applyTransform=!1;const a=t.batcher;a.ensureAttributeBuffer(n),a.ensureIndexBuffer(o),a.begin();for(let c=0;c<i.length;c++){const u=i[c];a.add(u)}a.finish(t.instructions);const l=a.geometry;l.indexBuffer.setDataWithSize(a.indexBuffer,a.indexSize,!0),l.buffers[0].setDataWithSize(a.attributeBuffer.float32View,a.attributeSize,!0);const h=a.batches;for(let c=0;c<h.length;c++){const u=h[c];u.bindGroup=Ud(u.textures.textures,u.textures.count)}return this._graphicsDataContextHash[e.uid]=t,t}_initContext(e){const t=new Xf;return t.context=e,this._gpuContextHash[e.uid]=t,e.on("destroy",this.onGraphicsContextDestroy,this),this._gpuContextHash[e.uid]}onGraphicsContextDestroy(e){this._cleanGraphicsContextData(e),e.off("destroy",this.onGraphicsContextDestroy,this),this._gpuContextHash[e.uid]=null}_cleanGraphicsContextData(e){const t=this._gpuContextHash[e.uid];t.isBatchable||this._graphicsDataContextHash[e.uid]&&(Gt.return(this.getContextRenderData(e)),this._graphicsDataContextHash[e.uid]=null),t.batches&&t.batches.forEach(i=>{Gt.return(i)})}destroy(){for(const e in this._gpuContextHash)this._gpuContextHash[e]&&this.onGraphicsContextDestroy(this._gpuContextHash[e].context)}};Qr.extension={type:[J.WebGLSystem,J.WebGPUSystem,J.CanvasSystem],name:"graphicsContext"};Qr.defaultOptions={bezierSmoothness:.5};let Gl=Qr;const jf=8,es=11920929e-14,qf=1;function Dl(s,e,t,i,r,n,o,a,l,h){const u=Math.min(.99,Math.max(0,h??Gl.defaultOptions.bezierSmoothness));let f=(qf-u)/1;return f*=f,Kf(e,t,i,r,n,o,a,l,s,f),s}function Kf(s,e,t,i,r,n,o,a,l,h){Er(s,e,t,i,r,n,o,a,l,h,0),l.push(o,a)}function Er(s,e,t,i,r,n,o,a,l,h,c){if(c>jf)return;const u=(s+t)/2,f=(e+i)/2,d=(t+r)/2,_=(i+n)/2,g=(r+o)/2,m=(n+a)/2,p=(u+d)/2,b=(f+_)/2,y=(d+g)/2,w=(_+m)/2,A=(p+y)/2,T=(b+w)/2;if(c>0){let P=o-s,U=a-e;const R=Math.abs((t-o)*U-(i-a)*P),E=Math.abs((r-o)*U-(n-a)*P);if(R>es&&E>es){if((R+E)*(R+E)<=h*(P*P+U*U)){l.push(A,T);return}}else if(R>es){if(R*R<=h*(P*P+U*U)){l.push(A,T);return}}else if(E>es){if(E*E<=h*(P*P+U*U)){l.push(A,T);return}}else if(P=A-(s+o)/2,U=T-(e+a)/2,P*P+U*U<=h){l.push(A,T);return}}Er(s,e,u,f,p,b,A,T,l,h,c+1),Er(A,T,y,w,g,m,o,a,l,h,c+1)}const Zf=8,Qf=11920929e-14,Jf=1;function $f(s,e,t,i,r,n,o,a){const h=Math.min(.99,Math.max(0,a??Gl.defaultOptions.bezierSmoothness));let c=(Jf-h)/1;return c*=c,e_(e,t,i,r,n,o,s,c),s}function e_(s,e,t,i,r,n,o,a){zr(o,s,e,t,i,r,n,a,0),o.push(r,n)}function zr(s,e,t,i,r,n,o,a,l){if(l>Zf)return;const h=(e+i)/2,c=(t+r)/2,u=(i+n)/2,f=(r+o)/2,d=(h+u)/2,_=(c+f)/2;let g=n-e,m=o-t;const p=Math.abs((i-n)*m-(r-o)*g);if(p>Qf){if(p*p<=a*(g*g+m*m)){s.push(d,_);return}}else if(g=d-(e+n)/2,m=_-(t+o)/2,g*g+m*m<=a){s.push(d,_);return}zr(s,e,t,h,c,d,_,a,l+1),zr(s,d,_,u,f,n,o,a,l+1)}function Nl(s,e,t,i,r,n,o,a){let l=Math.abs(r-n);(!o&&r>n||o&&n>r)&&(l=2*Math.PI-l),a||(a=Math.max(6,Math.floor(6*Math.pow(i,1/3)*(l/Math.PI)))),a=Math.max(a,3);let h=l/a,c=r;h*=o?-1:1;for(let u=0;u<a+1;u++){const f=Math.cos(c),d=Math.sin(c),_=e+f*i,g=t+d*i;s.push(_,g),c+=h}}function t_(s,e,t,i,r,n){const o=s[s.length-2],l=s[s.length-1]-t,h=o-e,c=r-t,u=i-e,f=Math.abs(l*u-h*c);if(f<1e-8||n===0){(s[s.length-2]!==e||s[s.length-1]!==t)&&s.push(e,t);return}const d=l*l+h*h,_=c*c+u*u,g=l*c+h*u,m=n*Math.sqrt(d)/f,p=n*Math.sqrt(_)/f,b=m*g/d,y=p*g/_,w=m*u+p*h,A=m*c+p*l,T=h*(p+b),P=l*(p+b),U=u*(m+y),R=c*(m+y),E=Math.atan2(P-A,T-w),M=Math.atan2(R-A,U-w);Nl(s,w+e,A+t,n,E,M,h*c>u*l)}const Ii=Math.PI*2,rr={centerX:0,centerY:0,ang1:0,ang2:0},nr=({x:s,y:e},t,i,r,n,o,a,l)=>{s*=t,e*=i;const h=r*s-n*e,c=n*s+r*e;return l.x=h+o,l.y=c+a,l};function i_(s,e){const t=e===-1.5707963267948966?-.551915024494:1.3333333333333333*Math.tan(e/4),i=e===1.5707963267948966?.551915024494:t,r=Math.cos(s),n=Math.sin(s),o=Math.cos(s+e),a=Math.sin(s+e);return[{x:r-n*i,y:n+r*i},{x:o+a*i,y:a-o*i},{x:o,y:a}]}const qn=(s,e,t,i)=>{const r=s*i-e*t<0?-1:1;let n=s*t+e*i;return n>1&&(n=1),n<-1&&(n=-1),r*Math.acos(n)},s_=(s,e,t,i,r,n,o,a,l,h,c,u,f)=>{const d=Math.pow(r,2),_=Math.pow(n,2),g=Math.pow(c,2),m=Math.pow(u,2);let p=d*_-d*m-_*g;p<0&&(p=0),p/=d*m+_*g,p=Math.sqrt(p)*(o===a?-1:1);const b=p*r/n*u,y=p*-n/r*c,w=h*b-l*y+(s+t)/2,A=l*b+h*y+(e+i)/2,T=(c-b)/r,P=(u-y)/n,U=(-c-b)/r,R=(-u-y)/n,E=qn(1,0,T,P);let M=qn(T,P,U,R);a===0&&M>0&&(M-=Ii),a===1&&M<0&&(M+=Ii),f.centerX=w,f.centerY=A,f.ang1=E,f.ang2=M};function r_(s,e,t,i,r,n,o,a=0,l=0,h=0){if(n===0||o===0)return;const c=Math.sin(a*Ii/360),u=Math.cos(a*Ii/360),f=u*(e-i)/2+c*(t-r)/2,d=-c*(e-i)/2+u*(t-r)/2;if(f===0&&d===0)return;n=Math.abs(n),o=Math.abs(o);const _=Math.pow(f,2)/Math.pow(n,2)+Math.pow(d,2)/Math.pow(o,2);_>1&&(n*=Math.sqrt(_),o*=Math.sqrt(_)),s_(e,t,i,r,n,o,l,h,c,u,f,d,rr);let{ang1:g,ang2:m}=rr;const{centerX:p,centerY:b}=rr;let y=Math.abs(m)/(Ii/4);Math.abs(1-y)<1e-7&&(y=1);const w=Math.max(Math.ceil(y),1);m/=w;let A=s[s.length-2],T=s[s.length-1];const P={x:0,y:0};for(let U=0;U<w;U++){const R=i_(g,m),{x:E,y:M}=nr(R[0],n,o,u,c,p,b,P),{x:z,y:V}=nr(R[1],n,o,u,c,p,b,P),{x:W,y:ae}=nr(R[2],n,o,u,c,p,b,P);Dl(s,A,T,E,M,z,V,W,ae),A=W,T=ae,g+=m}}function n_(s,e,t){const i=(o,a)=>{const l=a.x-o.x,h=a.y-o.y,c=Math.sqrt(l*l+h*h),u=l/c,f=h/c;return{len:c,nx:u,ny:f}},r=(o,a)=>{o===0?s.moveTo(a.x,a.y):s.lineTo(a.x,a.y)};let n=e[e.length-1];for(let o=0;o<e.length;o++){const a=e[o%e.length],l=a.radius??t;if(l<=0){r(o,a),n=a;continue}const h=e[(o+1)%e.length],c=i(a,n),u=i(a,h);if(c.len<1e-4||u.len<1e-4){r(o,a),n=a;continue}let f=Math.asin(c.nx*u.ny-c.ny*u.nx),d=1,_=!1;c.nx*u.nx-c.ny*-u.ny<0?f<0?f=Math.PI+f:(f=Math.PI-f,d=-1,_=!0):f>0&&(d=-1,_=!0);const g=f/2;let m,p=Math.abs(Math.cos(g)*l/Math.sin(g));p>Math.min(c.len/2,u.len/2)?(p=Math.min(c.len/2,u.len/2),m=Math.abs(p*Math.sin(g)/Math.cos(g))):m=l;const b=a.x+u.nx*p+-u.ny*m*d,y=a.y+u.ny*p+u.nx*m*d,w=Math.atan2(c.ny,c.nx)+Math.PI/2*d,A=Math.atan2(u.ny,u.nx)-Math.PI/2*d;o===0&&s.moveTo(b+Math.cos(w)*m,y+Math.sin(w)*m),s.arc(b,y,m,w,A,_),n=a}}function o_(s,e,t,i){const r=(a,l)=>Math.sqrt((a.x-l.x)**2+(a.y-l.y)**2),n=(a,l,h)=>({x:a.x+(l.x-a.x)*h,y:a.y+(l.y-a.y)*h}),o=e.length;for(let a=0;a<o;a++){const l=e[(a+1)%o],h=l.radius??t;if(h<=0){a===0?s.moveTo(l.x,l.y):s.lineTo(l.x,l.y);continue}const c=e[a],u=e[(a+2)%o],f=r(c,l);let d;if(f<1e-4)d=l;else{const m=Math.min(f/2,h);d=n(l,c,m/f)}const _=r(u,l);let g;if(_<1e-4)g=l;else{const m=Math.min(_/2,h);g=n(l,u,m/_)}a===0?s.moveTo(d.x,d.y):s.lineTo(d.x,d.y),s.quadraticCurveTo(l.x,l.y,g.x,g.y,i)}}const a_=new Pe;class l_{constructor(e){this.shapePrimitives=[],this._currentPoly=null,this._bounds=new Mt,this._graphicsPath2D=e}moveTo(e,t){return this.startPoly(e,t),this}lineTo(e,t){this._ensurePoly();const i=this._currentPoly.points,r=i[i.length-2],n=i[i.length-1];return(r!==e||n!==t)&&i.push(e,t),this}arc(e,t,i,r,n,o){this._ensurePoly(!1);const a=this._currentPoly.points;return Nl(a,e,t,i,r,n,o),this}arcTo(e,t,i,r,n){this._ensurePoly();const o=this._currentPoly.points;return t_(o,e,t,i,r,n),this}arcToSvg(e,t,i,r,n,o,a){const l=this._currentPoly.points;return r_(l,this._currentPoly.lastX,this._currentPoly.lastY,o,a,e,t,i,r,n),this}bezierCurveTo(e,t,i,r,n,o,a){this._ensurePoly();const l=this._currentPoly;return Dl(this._currentPoly.points,l.lastX,l.lastY,e,t,i,r,n,o,a),this}quadraticCurveTo(e,t,i,r,n){this._ensurePoly();const o=this._currentPoly;return $f(this._currentPoly.points,o.lastX,o.lastY,e,t,i,r,n),this}closePath(){return this.endPoly(!0),this}addPath(e,t){this.endPoly(),t&&!t.isIdentity()&&(e=e.clone(!0),e.transform(t));for(let i=0;i<e.instructions.length;i++){const r=e.instructions[i];this[r.action](...r.data)}return this}finish(e=!1){this.endPoly(e)}rect(e,t,i,r,n){return this.drawShape(new Pe(e,t,i,r),n),this}circle(e,t,i,r){return this.drawShape(new qr(e,t,i),r),this}poly(e,t,i){const r=new Pi(e);return r.closePath=t,this.drawShape(r,i),this}regularPoly(e,t,i,r,n=0,o){r=Math.max(r|0,3);const a=-1*Math.PI/2+n,l=Math.PI*2/r,h=[];for(let c=0;c<r;c++){const u=a-c*l;h.push(e+i*Math.cos(u),t+i*Math.sin(u))}return this.poly(h,!0,o),this}roundPoly(e,t,i,r,n,o=0,a){if(r=Math.max(r|0,3),n<=0)return this.regularPoly(e,t,i,r,o);const l=i*Math.sin(Math.PI/r)-.001;n=Math.min(n,l);const h=-1*Math.PI/2+o,c=Math.PI*2/r,u=(r-2)*Math.PI/r/2;for(let f=0;f<r;f++){const d=f*c+h,_=e+i*Math.cos(d),g=t+i*Math.sin(d),m=d+Math.PI+u,p=d-Math.PI-u,b=_+n*Math.cos(m),y=g+n*Math.sin(m),w=_+n*Math.cos(p),A=g+n*Math.sin(p);f===0?this.moveTo(b,y):this.lineTo(b,y),this.quadraticCurveTo(_,g,w,A,a)}return this.closePath()}roundShape(e,t,i=!1,r){return e.length<3?this:(i?o_(this,e,t,r):n_(this,e,t),this.closePath())}filletRect(e,t,i,r,n){if(n===0)return this.rect(e,t,i,r);const o=Math.min(i,r)/2,a=Math.min(o,Math.max(-o,n)),l=e+i,h=t+r,c=a<0?-a:0,u=Math.abs(a);return this.moveTo(e,t+u).arcTo(e+c,t+c,e+u,t,u).lineTo(l-u,t).arcTo(l-c,t+c,l,t+u,u).lineTo(l,h-u).arcTo(l-c,h-c,e+i-u,h,u).lineTo(e+u,h).arcTo(e+c,h-c,e,h-u,u).closePath()}chamferRect(e,t,i,r,n,o){if(n<=0)return this.rect(e,t,i,r);const a=Math.min(n,Math.min(i,r)/2),l=e+i,h=t+r,c=[e+a,t,l-a,t,l,t+a,l,h-a,l-a,h,e+a,h,e,h-a,e,t+a];for(let u=c.length-1;u>=2;u-=2)c[u]===c[u-2]&&c[u-1]===c[u-3]&&c.splice(u-1,2);return this.poly(c,!0,o)}ellipse(e,t,i,r,n){return this.drawShape(new Kr(e,t,i,r),n),this}roundRect(e,t,i,r,n,o){return this.drawShape(new Zr(e,t,i,r,n),o),this}drawShape(e,t){return this.endPoly(),this.shapePrimitives.push({shape:e,transform:t}),this}startPoly(e,t){let i=this._currentPoly;return i&&this.endPoly(),i=new Pi,i.points.push(e,t),this._currentPoly=i,this}endPoly(e=!1){const t=this._currentPoly;return t&&t.points.length>2&&(t.closePath=e,this.shapePrimitives.push({shape:t})),this._currentPoly=null,this}_ensurePoly(e=!0){if(!this._currentPoly&&(this._currentPoly=new Pi,e)){const t=this.shapePrimitives[this.shapePrimitives.length-1];if(t){let i=t.shape.x,r=t.shape.y;if(t.transform&&!t.transform.isIdentity()){const n=t.transform,o=i;i=n.a*i+n.c*r+n.tx,r=n.b*o+n.d*r+n.ty}this._currentPoly.points.push(i,r)}else this._currentPoly.points.push(0,0)}}buildPath(){const e=this._graphicsPath2D;this.shapePrimitives.length=0,this._currentPoly=null;for(let t=0;t<e.instructions.length;t++){const i=e.instructions[t];this[i.action](...i.data)}this.finish()}get bounds(){const e=this._bounds;e.clear();const t=this.shapePrimitives;for(let i=0;i<t.length;i++){const r=t[i],n=r.shape.getBounds(a_);r.transform?e.addRect(n,r.transform):e.addRect(n)}return e}}class gi{constructor(e){this.instructions=[],this.uid=He("graphicsPath"),this._dirty=!0,typeof e=="string"?Ld(e,this):this.instructions=(e==null?void 0:e.slice())??[]}get shapePath(){return this._shapePath||(this._shapePath=new l_(this)),this._dirty&&(this._dirty=!1,this._shapePath.buildPath()),this._shapePath}addPath(e,t){return e=e.clone(),this.instructions.push({action:"addPath",data:[e,t]}),this._dirty=!0,this}arc(...e){return this.instructions.push({action:"arc",data:e}),this._dirty=!0,this}arcTo(...e){return this.instructions.push({action:"arcTo",data:e}),this._dirty=!0,this}arcToSvg(...e){return this.instructions.push({action:"arcToSvg",data:e}),this._dirty=!0,this}bezierCurveTo(...e){return this.instructions.push({action:"bezierCurveTo",data:e}),this._dirty=!0,this}bezierCurveToShort(e,t,i,r,n){const o=this.instructions[this.instructions.length-1],a=this.getLastPoint(fe.shared);let l=0,h=0;if(!o||o.action!=="bezierCurveTo")l=a.x,h=a.y;else{l=o.data[2],h=o.data[3];const c=a.x,u=a.y;l=c+(c-l),h=u+(u-h)}return this.instructions.push({action:"bezierCurveTo",data:[l,h,e,t,i,r,n]}),this._dirty=!0,this}closePath(){return this.instructions.push({action:"closePath",data:[]}),this._dirty=!0,this}ellipse(...e){return this.instructions.push({action:"ellipse",data:e}),this._dirty=!0,this}lineTo(...e){return this.instructions.push({action:"lineTo",data:e}),this._dirty=!0,this}moveTo(...e){return this.instructions.push({action:"moveTo",data:e}),this}quadraticCurveTo(...e){return this.instructions.push({action:"quadraticCurveTo",data:e}),this._dirty=!0,this}quadraticCurveToShort(e,t,i){const r=this.instructions[this.instructions.length-1],n=this.getLastPoint(fe.shared);let o=0,a=0;if(!r||r.action!=="quadraticCurveTo")o=n.x,a=n.y;else{o=r.data[0],a=r.data[1];const l=n.x,h=n.y;o=l+(l-o),a=h+(h-a)}return this.instructions.push({action:"quadraticCurveTo",data:[o,a,e,t,i]}),this._dirty=!0,this}rect(e,t,i,r,n){return this.instructions.push({action:"rect",data:[e,t,i,r,n]}),this._dirty=!0,this}circle(e,t,i,r){return this.instructions.push({action:"circle",data:[e,t,i,r]}),this._dirty=!0,this}roundRect(...e){return this.instructions.push({action:"roundRect",data:e}),this._dirty=!0,this}poly(...e){return this.instructions.push({action:"poly",data:e}),this._dirty=!0,this}regularPoly(...e){return this.instructions.push({action:"regularPoly",data:e}),this._dirty=!0,this}roundPoly(...e){return this.instructions.push({action:"roundPoly",data:e}),this._dirty=!0,this}roundShape(...e){return this.instructions.push({action:"roundShape",data:e}),this._dirty=!0,this}filletRect(...e){return this.instructions.push({action:"filletRect",data:e}),this._dirty=!0,this}chamferRect(...e){return this.instructions.push({action:"chamferRect",data:e}),this._dirty=!0,this}star(e,t,i,r,n,o,a){n||(n=r/2);const l=-1*Math.PI/2+o,h=i*2,c=Math.PI*2/h,u=[];for(let f=0;f<h;f++){const d=f%2?n:r,_=f*c+l;u.push(e+d*Math.cos(_),t+d*Math.sin(_))}return this.poly(u,!0,a),this}clone(e=!1){const t=new gi;if(!e)t.instructions=this.instructions.slice();else for(let i=0;i<this.instructions.length;i++){const r=this.instructions[i];t.instructions.push({action:r.action,data:r.data.slice()})}return t}clear(){return this.instructions.length=0,this._dirty=!0,this}transform(e){if(e.isIdentity())return this;const t=e.a,i=e.b,r=e.c,n=e.d,o=e.tx,a=e.ty;let l=0,h=0,c=0,u=0,f=0,d=0,_=0,g=0;for(let m=0;m<this.instructions.length;m++){const p=this.instructions[m],b=p.data;switch(p.action){case"moveTo":case"lineTo":l=b[0],h=b[1],b[0]=t*l+r*h+o,b[1]=i*l+n*h+a;break;case"bezierCurveTo":c=b[0],u=b[1],f=b[2],d=b[3],l=b[4],h=b[5],b[0]=t*c+r*u+o,b[1]=i*c+n*u+a,b[2]=t*f+r*d+o,b[3]=i*f+n*d+a,b[4]=t*l+r*h+o,b[5]=i*l+n*h+a;break;case"quadraticCurveTo":c=b[0],u=b[1],l=b[2],h=b[3],b[0]=t*c+r*u+o,b[1]=i*c+n*u+a,b[2]=t*l+r*h+o,b[3]=i*l+n*h+a;break;case"arcToSvg":l=b[5],h=b[6],_=b[0],g=b[1],b[0]=t*_+r*g,b[1]=i*_+n*g,b[5]=t*l+r*h+o,b[6]=i*l+n*h+a;break;case"circle":b[4]=Ai(b[3],e);break;case"rect":b[4]=Ai(b[4],e);break;case"ellipse":b[8]=Ai(b[8],e);break;case"roundRect":b[5]=Ai(b[5],e);break;case"addPath":b[0].transform(e);break;case"poly":b[2]=Ai(b[2],e);break;default:Ve("unknown transform action",p.action);break}}return this._dirty=!0,this}get bounds(){return this.shapePath.bounds}getLastPoint(e){let t=this.instructions.length-1,i=this.instructions[t];if(!i)return e.x=0,e.y=0,e;for(;i.action==="closePath";){if(t--,t<0)return e.x=0,e.y=0,e;i=this.instructions[t]}switch(i.action){case"moveTo":case"lineTo":e.x=i.data[0],e.y=i.data[1];break;case"quadraticCurveTo":e.x=i.data[2],e.y=i.data[3];break;case"bezierCurveTo":e.x=i.data[4],e.y=i.data[5];break;case"arc":case"arcToSvg":e.x=i.data[5],e.y=i.data[6];break;case"addPath":i.data[0].getLastPoint(e);break}return e}}function Ai(s,e){return s?s.prepend(e):e.clone()}function h_(s,e){if(typeof s=="string"){const i=document.createElement("div");i.innerHTML=s.trim(),s=i.querySelector("svg")}const t={context:e,path:new gi};return Ul(s,t,null,null),e}function Ul(s,e,t,i){const r=s.children,{fillStyle:n,strokeStyle:o}=c_(s);n&&t?t={...t,...n}:n&&(t=n),o&&i?i={...i,...o}:o&&(i=o),e.context.fillStyle=t,e.context.strokeStyle=i;let a,l,h,c,u,f,d,_,g,m,p,b,y,w,A,T,P;switch(s.nodeName.toLowerCase()){case"path":w=s.getAttribute("d"),A=new gi(w),e.context.path(A),t&&e.context.fill(),i&&e.context.stroke();break;case"circle":d=je(s,"cx",0),_=je(s,"cy",0),g=je(s,"r",0),e.context.ellipse(d,_,g,g),t&&e.context.fill(),i&&e.context.stroke();break;case"rect":a=je(s,"x",0),l=je(s,"y",0),T=je(s,"width",0),P=je(s,"height",0),m=je(s,"rx",0),p=je(s,"ry",0),m||p?e.context.roundRect(a,l,T,P,m||p):e.context.rect(a,l,T,P),t&&e.context.fill(),i&&e.context.stroke();break;case"ellipse":d=je(s,"cx",0),_=je(s,"cy",0),m=je(s,"rx",0),p=je(s,"ry",0),e.context.beginPath(),e.context.ellipse(d,_,m,p),t&&e.context.fill(),i&&e.context.stroke();break;case"line":h=je(s,"x1",0),c=je(s,"y1",0),u=je(s,"x2",0),f=je(s,"y2",0),e.context.beginPath(),e.context.moveTo(h,c),e.context.lineTo(u,f),i&&e.context.stroke();break;case"polygon":y=s.getAttribute("points"),b=y.match(/\d+/g).map(U=>parseInt(U,10)),e.context.poly(b,!0),t&&e.context.fill(),i&&e.context.stroke();break;case"polyline":y=s.getAttribute("points"),b=y.match(/\d+/g).map(U=>parseInt(U,10)),e.context.poly(b,!1),i&&e.context.stroke();break;case"g":case"svg":break;default:{console.info(`[SVG parser] <${s.nodeName}> elements unsupported`);break}}for(let U=0;U<r.length;U++)Ul(r[U],e,t,i)}function je(s,e,t){const i=s.getAttribute(e);return i?Number(i):t}function c_(s){const e=s.getAttribute("style"),t={},i={};let r=!1,n=!1;if(e){const o=e.split(";");for(let a=0;a<o.length;a++){const l=o[a],[h,c]=l.split(":");switch(h){case"stroke":c!=="none"&&(t.color=Ne.shared.setValue(c).toNumber(),n=!0);break;case"stroke-width":t.width=Number(c);break;case"fill":c!=="none"&&(r=!0,i.color=Ne.shared.setValue(c).toNumber());break;case"fill-opacity":i.alpha=Number(c);break;case"stroke-opacity":t.alpha=Number(c);break;case"opacity":i.alpha=Number(c),t.alpha=Number(c);break}}}else{const o=s.getAttribute("stroke");o&&o!=="none"&&(n=!0,t.color=Ne.shared.setValue(o).toNumber(),t.width=je(s,"stroke-width",1));const a=s.getAttribute("fill");a&&a!=="none"&&(r=!0,i.color=Ne.shared.setValue(a).toNumber())}return{strokeStyle:n?t:null,fillStyle:r?i:null}}function u_(s){return Ne.isColorLike(s)}function Kn(s){return s instanceof vs}function Zn(s){return s instanceof Fi}function d_(s,e,t){const i=Ne.shared.setValue(e??0);return s.color=i.toNumber(),s.alpha=i.alpha===1?t.alpha:i.alpha,s.texture=_e.WHITE,{...t,...s}}function Qn(s,e,t){return s.fill=e,s.color=16777215,s.texture=e.texture,s.matrix=e.transform,{...t,...s}}function Jn(s,e,t){return e.buildLinearGradient(),s.fill=e,s.color=16777215,s.texture=e.texture,s.matrix=e.transform,{...t,...s}}function f_(s,e){var r;const t={...e,...s};if(t.texture){if(t.texture!==_e.WHITE){const o=((r=t.matrix)==null?void 0:r.clone().invert())||new we;o.translate(t.texture.frame.x,t.texture.frame.y),o.scale(1/t.texture.source.width,1/t.texture.source.height),t.matrix=o}const n=t.texture.source.style;n.addressMode==="clamp-to-edge"&&(n.addressMode="repeat",n.update())}const i=Ne.shared.setValue(t.color);return t.alpha*=i.alpha,t.color=i.toNumber(),t.matrix=t.matrix?t.matrix.clone():null,t}function Jt(s,e){if(s==null)return null;const t={},i=s;return u_(s)?d_(t,s,e):Kn(s)?Qn(t,s,e):Zn(s)?Jn(t,s,e):i.fill&&Kn(i.fill)?Qn(i,i.fill,e):i.fill&&Zn(i.fill)?Jn(i,i.fill,e):f_(i,e)}function fs(s,e){const{width:t,alignment:i,miterLimit:r,cap:n,join:o,pixelLine:a,...l}=e,h=Jt(s,l);return h?{width:t,alignment:i,miterLimit:r,cap:n,join:o,pixelLine:a,...h}:null}const __=new fe,$n=new we,Jr=class Bt extends Tt{constructor(){super(...arguments),this.uid=He("graphicsContext"),this.dirty=!0,this.batchMode="auto",this.instructions=[],this._activePath=new gi,this._transform=new we,this._fillStyle={...Bt.defaultFillStyle},this._strokeStyle={...Bt.defaultStrokeStyle},this._stateStack=[],this._tick=0,this._bounds=new Mt,this._boundsDirty=!0}clone(){const e=new Bt;return e.batchMode=this.batchMode,e.instructions=this.instructions.slice(),e._activePath=this._activePath.clone(),e._transform=this._transform.clone(),e._fillStyle={...this._fillStyle},e._strokeStyle={...this._strokeStyle},e._stateStack=this._stateStack.slice(),e._bounds=this._bounds.clone(),e._boundsDirty=!0,e}get fillStyle(){return this._fillStyle}set fillStyle(e){this._fillStyle=Jt(e,Bt.defaultFillStyle)}get strokeStyle(){return this._strokeStyle}set strokeStyle(e){this._strokeStyle=fs(e,Bt.defaultStrokeStyle)}setFillStyle(e){return this._fillStyle=Jt(e,Bt.defaultFillStyle),this}setStrokeStyle(e){return this._strokeStyle=Jt(e,Bt.defaultStrokeStyle),this}texture(e,t,i,r,n,o){return this.instructions.push({action:"texture",data:{image:e,dx:i||0,dy:r||0,dw:n||e.frame.width,dh:o||e.frame.height,transform:this._transform.clone(),alpha:this._fillStyle.alpha,style:t?Ne.shared.setValue(t).toNumber():16777215}}),this.onUpdate(),this}beginPath(){return this._activePath=new gi,this}fill(e,t){let i;const r=this.instructions[this.instructions.length-1];return this._tick===0&&r&&r.action==="stroke"?i=r.data.path:i=this._activePath.clone(),i?(e!=null&&(t!==void 0&&typeof e=="number"&&(be(xe,"GraphicsContext.fill(color, alpha) is deprecated, use GraphicsContext.fill({ color, alpha }) instead"),e={color:e,alpha:t}),this._fillStyle=Jt(e,Bt.defaultFillStyle)),this.instructions.push({action:"fill",data:{style:this.fillStyle,path:i}}),this.onUpdate(),this._initNextPathLocation(),this._tick=0,this):this}_initNextPathLocation(){const{x:e,y:t}=this._activePath.getLastPoint(fe.shared);this._activePath.clear(),this._activePath.moveTo(e,t)}stroke(e){let t;const i=this.instructions[this.instructions.length-1];return this._tick===0&&i&&i.action==="fill"?t=i.data.path:t=this._activePath.clone(),t?(e!=null&&(this._strokeStyle=fs(e,Bt.defaultStrokeStyle)),this.instructions.push({action:"stroke",data:{style:this.strokeStyle,path:t}}),this.onUpdate(),this._initNextPathLocation(),this._tick=0,this):this}cut(){for(let e=0;e<2;e++){const t=this.instructions[this.instructions.length-1-e],i=this._activePath.clone();if(t&&(t.action==="stroke"||t.action==="fill"))if(t.data.hole)t.data.hole.addPath(i);else{t.data.hole=i;break}}return this._initNextPathLocation(),this}arc(e,t,i,r,n,o){this._tick++;const a=this._transform;return this._activePath.arc(a.a*e+a.c*t+a.tx,a.b*e+a.d*t+a.ty,i,r,n,o),this}arcTo(e,t,i,r,n){this._tick++;const o=this._transform;return this._activePath.arcTo(o.a*e+o.c*t+o.tx,o.b*e+o.d*t+o.ty,o.a*i+o.c*r+o.tx,o.b*i+o.d*r+o.ty,n),this}arcToSvg(e,t,i,r,n,o,a){this._tick++;const l=this._transform;return this._activePath.arcToSvg(e,t,i,r,n,l.a*o+l.c*a+l.tx,l.b*o+l.d*a+l.ty),this}bezierCurveTo(e,t,i,r,n,o,a){this._tick++;const l=this._transform;return this._activePath.bezierCurveTo(l.a*e+l.c*t+l.tx,l.b*e+l.d*t+l.ty,l.a*i+l.c*r+l.tx,l.b*i+l.d*r+l.ty,l.a*n+l.c*o+l.tx,l.b*n+l.d*o+l.ty,a),this}closePath(){var e;return this._tick++,(e=this._activePath)==null||e.closePath(),this}ellipse(e,t,i,r){return this._tick++,this._activePath.ellipse(e,t,i,r,this._transform.clone()),this}circle(e,t,i){return this._tick++,this._activePath.circle(e,t,i,this._transform.clone()),this}path(e){return this._tick++,this._activePath.addPath(e,this._transform.clone()),this}lineTo(e,t){this._tick++;const i=this._transform;return this._activePath.lineTo(i.a*e+i.c*t+i.tx,i.b*e+i.d*t+i.ty),this}moveTo(e,t){this._tick++;const i=this._transform,r=this._activePath.instructions,n=i.a*e+i.c*t+i.tx,o=i.b*e+i.d*t+i.ty;return r.length===1&&r[0].action==="moveTo"?(r[0].data[0]=n,r[0].data[1]=o,this):(this._activePath.moveTo(n,o),this)}quadraticCurveTo(e,t,i,r,n){this._tick++;const o=this._transform;return this._activePath.quadraticCurveTo(o.a*e+o.c*t+o.tx,o.b*e+o.d*t+o.ty,o.a*i+o.c*r+o.tx,o.b*i+o.d*r+o.ty,n),this}rect(e,t,i,r){return this._tick++,this._activePath.rect(e,t,i,r,this._transform.clone()),this}roundRect(e,t,i,r,n){return this._tick++,this._activePath.roundRect(e,t,i,r,n,this._transform.clone()),this}poly(e,t){return this._tick++,this._activePath.poly(e,t,this._transform.clone()),this}regularPoly(e,t,i,r,n=0,o){return this._tick++,this._activePath.regularPoly(e,t,i,r,n,o),this}roundPoly(e,t,i,r,n,o){return this._tick++,this._activePath.roundPoly(e,t,i,r,n,o),this}roundShape(e,t,i,r){return this._tick++,this._activePath.roundShape(e,t,i,r),this}filletRect(e,t,i,r,n){return this._tick++,this._activePath.filletRect(e,t,i,r,n),this}chamferRect(e,t,i,r,n,o){return this._tick++,this._activePath.chamferRect(e,t,i,r,n,o),this}star(e,t,i,r,n=0,o=0){return this._tick++,this._activePath.star(e,t,i,r,n,o,this._transform.clone()),this}svg(e){return this._tick++,h_(e,this),this}restore(){const e=this._stateStack.pop();return e&&(this._transform=e.transform,this._fillStyle=e.fillStyle,this._strokeStyle=e.strokeStyle),this}save(){return this._stateStack.push({transform:this._transform.clone(),fillStyle:{...this._fillStyle},strokeStyle:{...this._strokeStyle}}),this}getTransform(){return this._transform}resetTransform(){return this._transform.identity(),this}rotate(e){return this._transform.rotate(e),this}scale(e,t=e){return this._transform.scale(e,t),this}setTransform(e,t,i,r,n,o){return e instanceof we?(this._transform.set(e.a,e.b,e.c,e.d,e.tx,e.ty),this):(this._transform.set(e,t,i,r,n,o),this)}transform(e,t,i,r,n,o){return e instanceof we?(this._transform.append(e),this):($n.set(e,t,i,r,n,o),this._transform.append($n),this)}translate(e,t=e){return this._transform.translate(e,t),this}clear(){return this._activePath.clear(),this.instructions.length=0,this.resetTransform(),this.onUpdate(),this}onUpdate(){this.dirty||(this.emit("update",this,16),this.dirty=!0,this._boundsDirty=!0)}get bounds(){if(!this._boundsDirty)return this._bounds;const e=this._bounds;e.clear();for(let t=0;t<this.instructions.length;t++){const i=this.instructions[t],r=i.action;if(r==="fill"){const n=i.data;e.addBounds(n.path.bounds)}else if(r==="texture"){const n=i.data;e.addFrame(n.dx,n.dy,n.dx+n.dw,n.dy+n.dh,n.transform)}if(r==="stroke"){const n=i.data,o=n.style.alignment,a=n.style.width*(1-o),l=n.path.bounds;e.addFrame(l.minX-a,l.minY-a,l.maxX+a,l.maxY+a)}}return e}containsPoint(e){var r;if(!this.bounds.containsPoint(e.x,e.y))return!1;const t=this.instructions;let i=!1;for(let n=0;n<t.length;n++){const o=t[n],a=o.data,l=a.path;if(!o.action||!l)continue;const h=a.style,c=l.shapePath.shapePrimitives;for(let u=0;u<c.length;u++){const f=c[u].shape;if(!h||!f)continue;const d=c[u].transform,_=d?d.applyInverse(e,__):e;if(o.action==="fill")i=f.contains(_.x,_.y);else{const m=h;i=f.strokeContains(_.x,_.y,m.width,m.alignment)}const g=a.hole;if(g){const m=(r=g.shapePath)==null?void 0:r.shapePrimitives;if(m)for(let p=0;p<m.length;p++)m[p].shape.contains(_.x,_.y)&&(i=!1)}if(i)return!0}}return i}destroy(e=!1){if(this._stateStack.length=0,this._transform=null,this.emit("destroy",this),this.removeAllListeners(),typeof e=="boolean"?e:e==null?void 0:e.texture){const i=typeof e=="boolean"?e:e==null?void 0:e.textureSource;this._fillStyle.texture&&this._fillStyle.texture.destroy(i),this._strokeStyle.texture&&this._strokeStyle.texture.destroy(i)}this._fillStyle=null,this._strokeStyle=null,this.instructions=null,this._activePath=null,this._bounds=null,this._stateStack=null,this.customShader=null,this._transform=null}};Jr.defaultFillStyle={color:16777215,alpha:1,texture:_e.WHITE,matrix:null,fill:null};Jr.defaultStrokeStyle={width:1,color:16777215,alpha:1,alignment:.5,miterLimit:10,cap:"butt",join:"miter",texture:_e.WHITE,matrix:null,fill:null,pixelLine:!1};let bt=Jr;const eo=["align","breakWords","cssOverrides","fontVariant","fontWeight","leading","letterSpacing","lineHeight","padding","textBaseline","trim","whiteSpace","wordWrap","wordWrapWidth","fontFamily","fontStyle","fontSize"];function g_(s){const e=[];let t=0;for(let i=0;i<eo.length;i++){const r=`_${eo[i]}`;e[t++]=s[r]}return t=Wl(s._fill,e,t),t=m_(s._stroke,e,t),t=p_(s.dropShadow,e,t),e.join("-")}function Wl(s,e,t){var i;return s&&(e[t++]=s.color,e[t++]=s.alpha,e[t++]=(i=s.fill)==null?void 0:i.styleKey),t}function m_(s,e,t){return s&&(t=Wl(s,e,t),e[t++]=s.width,e[t++]=s.alignment,e[t++]=s.cap,e[t++]=s.join,e[t++]=s.miterLimit),t}function p_(s,e,t){return s&&(e[t++]=s.alpha,e[t++]=s.angle,e[t++]=s.blur,e[t++]=s.distance,e[t++]=Ne.shared.setValue(s.color).toNumber()),t}const $r=class ai extends Tt{constructor(e={}){super(),b_(e);const t={...ai.defaultTextStyle,...e};for(const i in t){const r=i;this[r]=t[i]}this.update()}get align(){return this._align}set align(e){this._align=e,this.update()}get breakWords(){return this._breakWords}set breakWords(e){this._breakWords=e,this.update()}get dropShadow(){return this._dropShadow}set dropShadow(e){e!==null&&typeof e=="object"?this._dropShadow=this._createProxy({...ai.defaultDropShadow,...e}):this._dropShadow=e?this._createProxy({...ai.defaultDropShadow}):null,this.update()}get fontFamily(){return this._fontFamily}set fontFamily(e){this._fontFamily=e,this.update()}get fontSize(){return this._fontSize}set fontSize(e){typeof e=="string"?this._fontSize=parseInt(e,10):this._fontSize=e,this.update()}get fontStyle(){return this._fontStyle}set fontStyle(e){this._fontStyle=e.toLowerCase(),this.update()}get fontVariant(){return this._fontVariant}set fontVariant(e){this._fontVariant=e,this.update()}get fontWeight(){return this._fontWeight}set fontWeight(e){this._fontWeight=e,this.update()}get leading(){return this._leading}set leading(e){this._leading=e,this.update()}get letterSpacing(){return this._letterSpacing}set letterSpacing(e){this._letterSpacing=e,this.update()}get lineHeight(){return this._lineHeight}set lineHeight(e){this._lineHeight=e,this.update()}get padding(){return this._padding}set padding(e){this._padding=e,this.update()}get trim(){return this._trim}set trim(e){this._trim=e,this.update()}get textBaseline(){return this._textBaseline}set textBaseline(e){this._textBaseline=e,this.update()}get whiteSpace(){return this._whiteSpace}set whiteSpace(e){this._whiteSpace=e,this.update()}get wordWrap(){return this._wordWrap}set wordWrap(e){this._wordWrap=e,this.update()}get wordWrapWidth(){return this._wordWrapWidth}set wordWrapWidth(e){this._wordWrapWidth=e,this.update()}get fill(){return this._originalFill}set fill(e){e!==this._originalFill&&(this._originalFill=e,this._isFillStyle(e)&&(this._originalFill=this._createProxy({...bt.defaultFillStyle,...e},()=>{this._fill=Jt({...this._originalFill},bt.defaultFillStyle)})),this._fill=Jt(e===0?"black":e,bt.defaultFillStyle),this.update())}get stroke(){return this._originalStroke}set stroke(e){e!==this._originalStroke&&(this._originalStroke=e,this._isFillStyle(e)&&(this._originalStroke=this._createProxy({...bt.defaultStrokeStyle,...e},()=>{this._stroke=fs({...this._originalStroke},bt.defaultStrokeStyle)})),this._stroke=fs(e,bt.defaultStrokeStyle),this.update())}_generateKey(){return this._styleKey=g_(this),this._styleKey}update(){this._styleKey=null,this.emit("update",this)}reset(){const e=ai.defaultTextStyle;for(const t in e)this[t]=e[t]}get styleKey(){return this._styleKey||this._generateKey()}clone(){return new ai({align:this.align,breakWords:this.breakWords,dropShadow:this._dropShadow?{...this._dropShadow}:null,fill:this._fill,fontFamily:this.fontFamily,fontSize:this.fontSize,fontStyle:this.fontStyle,fontVariant:this.fontVariant,fontWeight:this.fontWeight,leading:this.leading,letterSpacing:this.letterSpacing,lineHeight:this.lineHeight,padding:this.padding,stroke:this._stroke,textBaseline:this.textBaseline,whiteSpace:this.whiteSpace,wordWrap:this.wordWrap,wordWrapWidth:this.wordWrapWidth})}destroy(e=!1){var i,r,n,o;if(this.removeAllListeners(),typeof e=="boolean"?e:e==null?void 0:e.texture){const a=typeof e=="boolean"?e:e==null?void 0:e.textureSource;(i=this._fill)!=null&&i.texture&&this._fill.texture.destroy(a),(r=this._originalFill)!=null&&r.texture&&this._originalFill.texture.destroy(a),(n=this._stroke)!=null&&n.texture&&this._stroke.texture.destroy(a),(o=this._originalStroke)!=null&&o.texture&&this._originalStroke.texture.destroy(a)}this._fill=null,this._stroke=null,this.dropShadow=null,this._originalStroke=null,this._originalFill=null}_createProxy(e,t){return new Proxy(e,{set:(i,r,n)=>(i[r]=n,t==null||t(r,n),this.update(),!0)})}_isFillStyle(e){return(e??null)!==null&&!(Ne.isColorLike(e)||e instanceof Fi||e instanceof vs)}};$r.defaultDropShadow={alpha:1,angle:Math.PI/6,blur:0,color:"black",distance:5};$r.defaultTextStyle={align:"left",breakWords:!1,dropShadow:null,fill:"black",fontFamily:"Arial",fontSize:26,fontStyle:"normal",fontVariant:"normal",fontWeight:"normal",leading:0,letterSpacing:0,lineHeight:0,padding:0,stroke:null,textBaseline:"alphabetic",trim:!1,whiteSpace:"pre",wordWrap:!1,wordWrapWidth:100};let Gi=$r;function b_(s){const e=s;if(typeof e.dropShadow=="boolean"&&e.dropShadow){const t=Gi.defaultDropShadow;s.dropShadow={alpha:e.dropShadowAlpha??t.alpha,angle:e.dropShadowAngle??t.angle,blur:e.dropShadowBlur??t.blur,color:e.dropShadowColor??t.color,distance:e.dropShadowDistance??t.distance}}if(e.strokeThickness!==void 0){be(xe,"strokeThickness is now a part of stroke");const t=e.stroke;let i={};if(Ne.isColorLike(t))i.color=t;else if(t instanceof Fi||t instanceof vs)i.fill=t;else if(Object.hasOwnProperty.call(t,"color")||Object.hasOwnProperty.call(t,"fill"))i=t;else throw new Error("Invalid stroke value.");s.stroke={...i,width:e.strokeThickness}}if(Array.isArray(e.fillGradientStops)){be(xe,"gradient fill is now a fill pattern: `new FillGradient(...)`");let t;s.fontSize==null?s.fontSize=Gi.defaultTextStyle.fontSize:typeof s.fontSize=="string"?t=parseInt(s.fontSize,10):t=s.fontSize;const i=new Fi(0,0,0,t*1.7),r=e.fillGradientStops.map(n=>Ne.shared.setValue(n).toNumber());r.forEach((n,o)=>{const a=o/(r.length-1);i.addColorStop(a,n)}),s.fill={fill:i}}}class y_{constructor(e){this._canvasPool=Object.create(null),this.canvasOptions=e||{},this.enableFullScreen=!1}_createCanvasAndContext(e,t){const i=Le.get().createCanvas();i.width=e,i.height=t;const r=i.getContext("2d");return{canvas:i,context:r}}getOptimalCanvasAndContext(e,t,i=1){e=Math.ceil(e*i-1e-6),t=Math.ceil(t*i-1e-6),e=us(e),t=us(t);const r=(e<<17)+(t<<1);this._canvasPool[r]||(this._canvasPool[r]=[]);let n=this._canvasPool[r].pop();return n||(n=this._createCanvasAndContext(e,t)),n}returnCanvasAndContext(e){const t=e.canvas,{width:i,height:r}=t,n=(i<<17)+(r<<1);e.context.clearRect(0,0,i,r),this._canvasPool[n].push(e)}clear(){this._canvasPool={}}}const to=new y_,w_=["serif","sans-serif","monospace","cursive","fantasy","system-ui"];function Rr(s){const e=typeof s.fontSize=="number"?`${s.fontSize}px`:s.fontSize;let t=s.fontFamily;Array.isArray(s.fontFamily)||(t=s.fontFamily.split(","));for(let i=t.length-1;i>=0;i--){let r=t[i].trim();!/([\"\'])[^\'\"]+\1/.test(r)&&!w_.includes(r)&&(r=`"${r}"`),t[i]=r}return`${s.fontStyle} ${s.fontVariant} ${s.fontWeight} ${e} ${t.join(",")}`}const or={willReadFrequently:!0},It=class K{static get experimentalLetterSpacingSupported(){let e=K._experimentalLetterSpacingSupported;if(e!==void 0){const t=Le.get().getCanvasRenderingContext2D().prototype;e=K._experimentalLetterSpacingSupported="letterSpacing"in t||"textLetterSpacing"in t}return e}constructor(e,t,i,r,n,o,a,l,h){this.text=e,this.style=t,this.width=i,this.height=r,this.lines=n,this.lineWidths=o,this.lineHeight=a,this.maxLineWidth=l,this.fontProperties=h}static measureText(e=" ",t,i=K._canvas,r=t.wordWrap){var b;const n=`${e}:${t.styleKey}`;if(K._measurementCache[n])return K._measurementCache[n];const o=Rr(t),a=K.measureFont(o);a.fontSize===0&&(a.fontSize=t.fontSize,a.ascent=t.fontSize);const l=K.__context;l.font=o;const c=(r?K._wordWrap(e,t,i):e).split(/(?:\r\n|\r|\n)/),u=new Array(c.length);let f=0;for(let y=0;y<c.length;y++){const w=K._measureText(c[y],t.letterSpacing,l);u[y]=w,f=Math.max(f,w)}const d=((b=t._stroke)==null?void 0:b.width)||0;let _=f+d;t.dropShadow&&(_+=t.dropShadow.distance);const g=t.lineHeight||a.fontSize;let m=Math.max(g,a.fontSize+d)+(c.length-1)*(g+t.leading);return t.dropShadow&&(m+=t.dropShadow.distance),new K(e,t,_,m,c,u,g+t.leading,f,a)}static _measureText(e,t,i){let r=!1;K.experimentalLetterSpacingSupported&&(K.experimentalLetterSpacing?(i.letterSpacing=`${t}px`,i.textLetterSpacing=`${t}px`,r=!0):(i.letterSpacing="0px",i.textLetterSpacing="0px"));const n=i.measureText(e);let o=n.width;const a=-n.actualBoundingBoxLeft;let h=n.actualBoundingBoxRight-a;if(o>0)if(r)o-=t,h-=t;else{const c=(K.graphemeSegmenter(e).length-1)*t;o+=c,h+=c}return Math.max(o,h)}static _wordWrap(e,t,i=K._canvas){const r=i.getContext("2d",or);let n=0,o="",a="";const l=Object.create(null),{letterSpacing:h,whiteSpace:c}=t,u=K._collapseSpaces(c),f=K._collapseNewlines(c);let d=!u;const _=t.wordWrapWidth+h,g=K._tokenize(e);for(let m=0;m<g.length;m++){let p=g[m];if(K._isNewline(p)){if(!f){a+=K._addLine(o),d=!u,o="",n=0;continue}p=" "}if(u){const y=K.isBreakingSpace(p),w=K.isBreakingSpace(o[o.length-1]);if(y&&w)continue}const b=K._getFromCache(p,h,l,r);if(b>_)if(o!==""&&(a+=K._addLine(o),o="",n=0),K.canBreakWords(p,t.breakWords)){const y=K.wordWrapSplit(p);for(let w=0;w<y.length;w++){let A=y[w],T=A,P=1;for(;y[w+P];){const R=y[w+P];if(!K.canBreakChars(T,R,p,w,t.breakWords))A+=R;else break;T=R,P++}w+=P-1;const U=K._getFromCache(A,h,l,r);U+n>_&&(a+=K._addLine(o),d=!1,o="",n=0),o+=A,n+=U}}else{o.length>0&&(a+=K._addLine(o),o="",n=0);const y=m===g.length-1;a+=K._addLine(p,!y),d=!1,o="",n=0}else b+n>_&&(d=!1,a+=K._addLine(o),o="",n=0),(o.length>0||!K.isBreakingSpace(p)||d)&&(o+=p,n+=b)}return a+=K._addLine(o,!1),a}static _addLine(e,t=!0){return e=K._trimRight(e),e=t?`${e}
`:e,e}static _getFromCache(e,t,i,r){let n=i[e];return typeof n!="number"&&(n=K._measureText(e,t,r)+t,i[e]=n),n}static _collapseSpaces(e){return e==="normal"||e==="pre-line"}static _collapseNewlines(e){return e==="normal"}static _trimRight(e){if(typeof e!="string")return"";for(let t=e.length-1;t>=0;t--){const i=e[t];if(!K.isBreakingSpace(i))break;e=e.slice(0,-1)}return e}static _isNewline(e){return typeof e!="string"?!1:K._newlines.includes(e.charCodeAt(0))}static isBreakingSpace(e,t){return typeof e!="string"?!1:K._breakingSpaces.includes(e.charCodeAt(0))}static _tokenize(e){const t=[];let i="";if(typeof e!="string")return t;for(let r=0;r<e.length;r++){const n=e[r],o=e[r+1];if(K.isBreakingSpace(n,o)||K._isNewline(n)){i!==""&&(t.push(i),i=""),t.push(n);continue}i+=n}return i!==""&&t.push(i),t}static canBreakWords(e,t){return t}static canBreakChars(e,t,i,r,n){return!0}static wordWrapSplit(e){return K.graphemeSegmenter(e)}static measureFont(e){if(K._fonts[e])return K._fonts[e];const t=K._context;t.font=e;const i=t.measureText(K.METRICS_STRING+K.BASELINE_SYMBOL),r={ascent:i.actualBoundingBoxAscent,descent:i.actualBoundingBoxDescent,fontSize:i.actualBoundingBoxAscent+i.actualBoundingBoxDescent};return K._fonts[e]=r,r}static clearMetrics(e=""){e?delete K._fonts[e]:K._fonts={}}static get _canvas(){if(!K.__canvas){let e;try{const t=new OffscreenCanvas(0,0),i=t.getContext("2d",or);if(i!=null&&i.measureText)return K.__canvas=t,t;e=Le.get().createCanvas()}catch{e=Le.get().createCanvas()}e.width=e.height=10,K.__canvas=e}return K.__canvas}static get _context(){return K.__context||(K.__context=K._canvas.getContext("2d",or)),K.__context}};It.METRICS_STRING="|ÉqÅ";It.BASELINE_SYMBOL="M";It.BASELINE_MULTIPLIER=1.4;It.HEIGHT_MULTIPLIER=2;It.graphemeSegmenter=(()=>{if(typeof(Intl==null?void 0:Intl.Segmenter)=="function"){const s=new Intl.Segmenter;return e=>[...s.segment(e)].map(t=>t.segment)}return s=>[...s]})();It.experimentalLetterSpacing=!1;It._fonts={};It._newlines=[10,13];It._breakingSpaces=[9,32,8192,8193,8194,8195,8196,8197,8198,8200,8201,8202,8287,12288];It._measurementCache={};let io=It;function so(s,e){if(s.texture===_e.WHITE&&!s.fill)return Ne.shared.setValue(s.color).setAlpha(s.alpha??1).toHexa();if(s.fill){if(s.fill instanceof vs){const t=s.fill,i=e.createPattern(t.texture.source.resource,"repeat"),r=t.transform.copyTo(we.shared);return r.scale(t.texture.frame.width,t.texture.frame.height),i.setTransform(r),i}else if(s.fill instanceof Fi){const t=s.fill;if(t.type==="linear"){const i=e.createLinearGradient(t.x0,t.y0,t.x1,t.y1);return t.gradientStops.forEach(r=>{i.addColorStop(r.offset,Ne.shared.setValue(r.color).toHex())}),i}}}else{const t=e.createPattern(s.texture.source.resource,"repeat"),i=s.matrix.copyTo(we.shared);return i.scale(s.texture.frame.width,s.texture.frame.height),t.setTransform(i),t}return Ve("FillStyle not recognised",s),"red"}function Hl(s){if(s==="")return[];typeof s=="string"&&(s=[s]);const e=[];for(let t=0,i=s.length;t<i;t++){const r=s[t];if(Array.isArray(r)){if(r.length!==2)throw new Error(`[BitmapFont]: Invalid character range length, expecting 2 got ${r.length}.`);if(r[0].length===0||r[1].length===0)throw new Error("[BitmapFont]: Invalid character delimiter.");const n=r[0].charCodeAt(0),o=r[1].charCodeAt(0);if(o<n)throw new Error("[BitmapFont]: Invalid character range.");for(let a=n,l=o;a<=l;a++)e.push(String.fromCharCode(a))}else e.push(...Array.from(r))}if(e.length===0)throw new Error("[BitmapFont]: Empty set when resolving characters.");return e}const Vl=class Xl extends kl{constructor(e){super(),this.resolution=1,this.pages=[],this._padding=0,this._measureCache=Object.create(null),this._currentChars=[],this._currentX=0,this._currentY=0,this._currentPageIndex=-1,this._skipKerning=!1;const t={...Xl.defaultOptions,...e};this._textureSize=t.textureSize,this._mipmap=t.mipmap;const i=t.style.clone();t.overrideFill&&(i._fill.color=16777215,i._fill.alpha=1,i._fill.texture=_e.WHITE,i._fill.fill=null),this.applyFillAsTint=t.overrideFill;const r=i.fontSize;i.fontSize=this.baseMeasurementFontSize;const n=Rr(i);t.overrideSize?i._stroke&&(i._stroke.width*=this.baseRenderedFontSize/r):i.fontSize=this.baseRenderedFontSize=r,this._style=i,this._skipKerning=t.skipKerning??!1,this.resolution=t.resolution??1,this._padding=t.padding??4,this.fontMetrics=io.measureFont(n),this.lineHeight=i.lineHeight||this.fontMetrics.fontSize||i.fontSize}ensureCharacters(e){var m,p;const t=Hl(e).filter(b=>!this._currentChars.includes(b)).filter((b,y,w)=>w.indexOf(b)===y);if(!t.length)return;this._currentChars=[...this._currentChars,...t];let i;this._currentPageIndex===-1?i=this._nextPage():i=this.pages[this._currentPageIndex];let{canvas:r,context:n}=i.canvasAndContext,o=i.texture.source;const a=this._style;let l=this._currentX,h=this._currentY;const c=this.baseRenderedFontSize/this.baseMeasurementFontSize,u=this._padding*c;let f=0,d=!1;const _=r.width/this.resolution,g=r.height/this.resolution;for(let b=0;b<t.length;b++){const y=t[b],w=io.measureText(y,a,r,!1);w.lineHeight=w.height;const A=w.width*c,T=Math.ceil((a.fontStyle==="italic"?2:1)*A),P=w.height*c,U=T+u*2,R=P+u*2;if(d=!1,y!==`
`&&y!=="\r"&&y!=="	"&&y!==" "&&(d=!0,f=Math.ceil(Math.max(R,f))),l+U>_&&(h+=f,f=R,l=0,h+f>g)){o.update();const M=this._nextPage();r=M.canvasAndContext.canvas,n=M.canvasAndContext.context,o=M.texture.source,h=0}const E=A/c-(((m=a.dropShadow)==null?void 0:m.distance)??0)-(((p=a._stroke)==null?void 0:p.width)??0);if(this.chars[y]={id:y.codePointAt(0),xOffset:-this._padding,yOffset:-this._padding,xAdvance:E,kerning:{}},d){this._drawGlyph(n,w,l+u,h+u,c,a);const M=o.width*c,z=o.height*c,V=new Pe(l/M*o.width,h/z*o.height,U/M*o.width,R/z*o.height);this.chars[y].texture=new _e({source:o,frame:V}),l+=Math.ceil(U)}}o.update(),this._currentX=l,this._currentY=h,this._skipKerning&&this._applyKerning(t,n)}get pageTextures(){return be(xe,"BitmapFont.pageTextures is deprecated, please use BitmapFont.pages instead."),this.pages}_applyKerning(e,t){const i=this._measureCache;for(let r=0;r<e.length;r++){const n=e[r];for(let o=0;o<this._currentChars.length;o++){const a=this._currentChars[o];let l=i[n];l||(l=i[n]=t.measureText(n).width);let h=i[a];h||(h=i[a]=t.measureText(a).width);let c=t.measureText(n+a).width,u=c-(l+h);u&&(this.chars[n].kerning[a]=u),c=t.measureText(n+a).width,u=c-(l+h),u&&(this.chars[a].kerning[n]=u)}}}_nextPage(){this._currentPageIndex++;const e=this.resolution,t=to.getOptimalCanvasAndContext(this._textureSize,this._textureSize,e);this._setupContext(t.context,this._style,e);const i=e*(this.baseRenderedFontSize/this.baseMeasurementFontSize),r=new _e({source:new pi({resource:t.canvas,resolution:i,alphaMode:"premultiply-alpha-on-upload",autoGenerateMipmaps:this._mipmap})}),n={canvasAndContext:t,texture:r};return this.pages[this._currentPageIndex]=n,n}_setupContext(e,t,i){t.fontSize=this.baseRenderedFontSize,e.scale(i,i),e.font=Rr(t),t.fontSize=this.baseMeasurementFontSize,e.textBaseline=t.textBaseline;const r=t._stroke,n=(r==null?void 0:r.width)??0;if(r&&(e.lineWidth=n,e.lineJoin=r.join,e.miterLimit=r.miterLimit,e.strokeStyle=so(r,e)),t._fill&&(e.fillStyle=so(t._fill,e)),t.dropShadow){const o=t.dropShadow,a=Ne.shared.setValue(o.color).toArray(),l=o.blur*i,h=o.distance*i;e.shadowColor=`rgba(${a[0]*255},${a[1]*255},${a[2]*255},${o.alpha})`,e.shadowBlur=l,e.shadowOffsetX=Math.cos(o.angle)*h,e.shadowOffsetY=Math.sin(o.angle)*h}else e.shadowColor="black",e.shadowBlur=0,e.shadowOffsetX=0,e.shadowOffsetY=0}_drawGlyph(e,t,i,r,n,o){const a=t.text,l=t.fontProperties,h=o._stroke,c=((h==null?void 0:h.width)??0)*n,u=i+c/2,f=r-c/2,d=l.descent*n,_=t.lineHeight*n;o.stroke&&c&&e.strokeText(a,u,f+_-d),o._fill&&e.fillText(a,u,f+_-d)}destroy(){super.destroy();for(let e=0;e<this.pages.length;e++){const{canvasAndContext:t,texture:i}=this.pages[e];to.returnCanvasAndContext(t),i.destroy(!0)}this.pages=null}};Vl.defaultOptions={textureSize:512,style:new Gi,mipmap:!0};let ro=Vl;function x_(s,e,t,i){const r={width:0,height:0,offsetY:0,scale:e.fontSize/t.baseMeasurementFontSize,lines:[{width:0,charPositions:[],spaceWidth:0,spacesIndex:[],chars:[]}]};r.offsetY=t.baseLineOffset;let n=r.lines[0],o=null,a=!0;const l={spaceWord:!1,width:0,start:0,index:0,positions:[],chars:[]},h=_=>{const g=n.width;for(let m=0;m<l.index;m++){const p=_.positions[m];n.chars.push(_.chars[m]),n.charPositions.push(p+g)}n.width+=_.width,a=!1,l.width=0,l.index=0,l.chars.length=0},c=()=>{let _=n.chars.length-1;if(i){let g=n.chars[_];for(;g===" ";)n.width-=t.chars[g].xAdvance,g=n.chars[--_]}r.width=Math.max(r.width,n.width),n={width:0,charPositions:[],chars:[],spaceWidth:0,spacesIndex:[]},a=!0,r.lines.push(n),r.height+=t.lineHeight},u=t.baseMeasurementFontSize/e.fontSize,f=e.letterSpacing*u,d=e.wordWrapWidth*u;for(let _=0;_<s.length+1;_++){let g;const m=_===s.length;m||(g=s[_]);const p=t.chars[g]||t.chars[" "];if(/(?:\s)/.test(g)||g==="\r"||g===`
`||m){if(!a&&e.wordWrap&&n.width+l.width-f>d?(c(),h(l),m||n.charPositions.push(0)):(l.start=n.width,h(l),m||n.charPositions.push(0)),g==="\r"||g===`
`)n.width!==0&&c();else if(!m){const A=p.xAdvance+(p.kerning[o]||0)+f;n.width+=A,n.spaceWidth=A,n.spacesIndex.push(n.charPositions.length),n.chars.push(g)}}else{const w=p.kerning[o]||0,A=p.xAdvance+w+f;l.positions[l.index++]=l.width+w,l.chars.push(g),l.width+=A}o=g}return c(),e.align==="center"?v_(r):e.align==="right"?k_(r):e.align==="justify"&&A_(r),r}function v_(s){for(let e=0;e<s.lines.length;e++){const t=s.lines[e],i=s.width/2-t.width/2;for(let r=0;r<t.charPositions.length;r++)t.charPositions[r]+=i}}function k_(s){for(let e=0;e<s.lines.length;e++){const t=s.lines[e],i=s.width-t.width;for(let r=0;r<t.charPositions.length;r++)t.charPositions[r]+=i}}function A_(s){const e=s.width;for(let t=0;t<s.lines.length;t++){const i=s.lines[t];let r=0,n=i.spacesIndex[r++],o=0;const a=i.spacesIndex.length,h=(e-i.width)/a;for(let c=0;c<i.charPositions.length;c++)c===n&&(n=i.spacesIndex[r++],o+=h),i.charPositions[c]+=o}}let ts=0;class S_{constructor(){this.ALPHA=[["a","z"],["A","Z"]," "],this.NUMERIC=[["0","9"]],this.ALPHANUMERIC=[["a","z"],["A","Z"],["0","9"]," "],this.ASCII=[[" ","~"]],this.defaultOptions={chars:this.ALPHANUMERIC,resolution:1,padding:4,skipKerning:!1}}getFont(e,t){var o;let i=`${t.fontFamily}-bitmap`,r=!0;if(t._fill.fill&&!t._stroke)i+=t._fill.fill.styleKey,r=!1;else if(t._stroke||t.dropShadow){let a=t.styleKey;a=a.substring(0,a.lastIndexOf("-")),i=`${a}-bitmap`,r=!1}if(!Be.has(i)){const a=new ro({style:t,overrideFill:r,overrideSize:!0,...this.defaultOptions});ts++,ts>50&&Ve("BitmapText",`You have dynamically created ${ts} bitmap fonts, this can be inefficient. Try pre installing your font styles using \`BitmapFont.install({name:"style1", style})\``),a.once("destroy",()=>{ts--,Be.remove(i)}),Be.set(i,a)}const n=Be.get(i);return(o=n.ensureCharacters)==null||o.call(n,e),n}getLayout(e,t,i=!0){const r=this.getFont(e,t);return x_([...e],t,r,i)}measureText(e,t,i=!0){return this.getLayout(e,t,i)}install(...e){var h,c,u,f;let t=e[0];typeof t=="string"&&(t={name:t,style:e[1],chars:(h=e[2])==null?void 0:h.chars,resolution:(c=e[2])==null?void 0:c.resolution,padding:(u=e[2])==null?void 0:u.padding,skipKerning:(f=e[2])==null?void 0:f.skipKerning},be(xe,"BitmapFontManager.install(name, style, options) is deprecated, use BitmapFontManager.install({name, style, ...options})"));const i=t==null?void 0:t.name;if(!i)throw new Error("[BitmapFontManager] Property `name` is required.");t={...this.defaultOptions,...t};const r=t.style,n=r instanceof Gi?r:new Gi(r),o=n._fill.fill!==null&&n._fill.fill!==void 0,a=new ro({style:n,overrideFill:o,skipKerning:t.skipKerning,padding:t.padding,resolution:t.resolution,overrideSize:!1}),l=Hl(t.chars);return a.ensureCharacters(l.join("")),Be.set(`${i}-bitmap`,a),a.once("destroy",()=>Be.remove(`${i}-bitmap`)),a}uninstall(e){const t=`${e}-bitmap`,i=Be.get(t);i&&i.destroy()}}const no=new S_;class Yl extends kl{constructor(e,t){super();const{textures:i,data:r}=e;Object.keys(r.pages).forEach(n=>{const o=r.pages[parseInt(n,10)],a=i[o.id];this.pages.push({texture:a})}),Object.keys(r.chars).forEach(n=>{const o=r.chars[n],{frame:a,source:l}=i[o.page],h=new Pe(o.x+a.x,o.y+a.y,o.width,o.height),c=new _e({source:l,frame:h});this.chars[n]={id:n.codePointAt(0),xOffset:o.xOffset,yOffset:o.yOffset,xAdvance:o.xAdvance,kerning:o.kerning??{},texture:c}}),this.baseRenderedFontSize=r.fontSize,this.baseMeasurementFontSize=r.fontSize,this.fontMetrics={ascent:0,descent:0,fontSize:r.fontSize},this.baseLineOffset=r.baseLineOffset,this.lineHeight=r.lineHeight,this.fontFamily=r.fontFamily,this.distanceField=r.distanceField??{type:"none",range:0},this.url=t}destroy(){super.destroy();for(let e=0;e<this.pages.length;e++){const{texture:t}=this.pages[e];t.destroy(!0)}this.pages=null}static install(e){no.install(e)}static uninstall(e){no.uninstall(e)}}const ar={test(s){return typeof s=="string"&&s.startsWith("info face=")},parse(s){const e=s.match(/^[a-z]+\s+.+$/gm),t={info:[],common:[],page:[],char:[],chars:[],kerning:[],kernings:[],distanceField:[]};for(const u in e){const f=e[u].match(/^[a-z]+/gm)[0],d=e[u].match(/[a-zA-Z]+=([^\s"']+|"([^"]*)")/gm),_={};for(const g in d){const m=d[g].split("="),p=m[0],b=m[1].replace(/"/gm,""),y=parseFloat(b),w=isNaN(y)?b:y;_[p]=w}t[f].push(_)}const i={chars:{},pages:[],lineHeight:0,fontSize:0,fontFamily:"",distanceField:null,baseLineOffset:0},[r]=t.info,[n]=t.common,[o]=t.distanceField??[];o&&(i.distanceField={range:parseInt(o.distanceRange,10),type:o.fieldType}),i.fontSize=parseInt(r.size,10),i.fontFamily=r.face,i.lineHeight=parseInt(n.lineHeight,10);const a=t.page;for(let u=0;u<a.length;u++)i.pages.push({id:parseInt(a[u].id,10)||0,file:a[u].file});const l={};i.baseLineOffset=i.lineHeight-parseInt(n.base,10);const h=t.char;for(let u=0;u<h.length;u++){const f=h[u],d=parseInt(f.id,10);let _=f.letter??f.char??String.fromCharCode(d);_==="space"&&(_=" "),l[d]=_,i.chars[_]={id:d,page:parseInt(f.page,10)||0,x:parseInt(f.x,10),y:parseInt(f.y,10),width:parseInt(f.width,10),height:parseInt(f.height,10),xOffset:parseInt(f.xoffset,10),yOffset:parseInt(f.yoffset,10),xAdvance:parseInt(f.xadvance,10),kerning:{}}}const c=t.kerning||[];for(let u=0;u<c.length;u++){const f=parseInt(c[u].first,10),d=parseInt(c[u].second,10),_=parseInt(c[u].amount,10);i.chars[l[d]].kerning[l[f]]=_}return i}},oo={test(s){const e=s;return typeof e!="string"&&"getElementsByTagName"in e&&e.getElementsByTagName("page").length&&e.getElementsByTagName("info")[0].getAttribute("face")!==null},parse(s){const e={chars:{},pages:[],lineHeight:0,fontSize:0,fontFamily:"",distanceField:null,baseLineOffset:0},t=s.getElementsByTagName("info")[0],i=s.getElementsByTagName("common")[0],r=s.getElementsByTagName("distanceField")[0];r&&(e.distanceField={type:r.getAttribute("fieldType"),range:parseInt(r.getAttribute("distanceRange"),10)});const n=s.getElementsByTagName("page"),o=s.getElementsByTagName("char"),a=s.getElementsByTagName("kerning");e.fontSize=parseInt(t.getAttribute("size"),10),e.fontFamily=t.getAttribute("face"),e.lineHeight=parseInt(i.getAttribute("lineHeight"),10);for(let h=0;h<n.length;h++)e.pages.push({id:parseInt(n[h].getAttribute("id"),10)||0,file:n[h].getAttribute("file")});const l={};e.baseLineOffset=e.lineHeight-parseInt(i.getAttribute("base"),10);for(let h=0;h<o.length;h++){const c=o[h],u=parseInt(c.getAttribute("id"),10);let f=c.getAttribute("letter")??c.getAttribute("char")??String.fromCharCode(u);f==="space"&&(f=" "),l[u]=f,e.chars[f]={id:u,page:parseInt(c.getAttribute("page"),10)||0,x:parseInt(c.getAttribute("x"),10),y:parseInt(c.getAttribute("y"),10),width:parseInt(c.getAttribute("width"),10),height:parseInt(c.getAttribute("height"),10),xOffset:parseInt(c.getAttribute("xoffset"),10),yOffset:parseInt(c.getAttribute("yoffset"),10),xAdvance:parseInt(c.getAttribute("xadvance"),10),kerning:{}}}for(let h=0;h<a.length;h++){const c=parseInt(a[h].getAttribute("first"),10),u=parseInt(a[h].getAttribute("second"),10),f=parseInt(a[h].getAttribute("amount"),10);e.chars[l[u]].kerning[l[c]]=f}return e}},ao={test(s){return typeof s=="string"&&s.includes("<font>")?oo.test(Le.get().parseXML(s)):!1},parse(s){return oo.parse(Le.get().parseXML(s))}},C_=[".xml",".fnt"],M_={extension:{type:J.CacheParser,name:"cacheBitmapFont"},test:s=>s instanceof Yl,getCacheableAssets(s,e){const t={};return s.forEach(i=>{t[i]=e,t[`${i}-bitmap`]=e}),t[`${e.fontFamily}-bitmap`]=e,t}},T_={extension:{type:J.LoadParser,priority:Xt.Normal},name:"loadBitmapFont",test(s){return C_.includes(_t.extname(s).toLowerCase())},async testParse(s){return ar.test(s)||ao.test(s)},async parse(s,e,t){const i=ar.test(s)?ar.parse(s):ao.parse(s),{src:r}=e,{pages:n}=i,o=[],a=i.distanceField?{scaleMode:"linear",alphaMode:"premultiply-alpha-on-upload",autoGenerateMipmaps:!1,resolution:1}:{};for(let u=0;u<n.length;++u){const f=n[u].file;let d=_t.join(_t.dirname(r),f);d=pr(d,r),o.push({src:d,data:a})}const l=await t.load(o),h=o.map(u=>l[u.src]);return new Yl({data:i,textures:h},r)},async load(s,e){return await(await Le.get().fetch(s)).text()},async unload(s,e,t){await Promise.all(s.pages.map(i=>t.unload(i.texture.source._sourceOrigin))),s.destroy()}};class P_{constructor(e,t=!1){this._loader=e,this._assetList=[],this._isLoading=!1,this._maxConcurrent=1,this.verbose=t}add(e){e.forEach(t=>{this._assetList.push(t)}),this.verbose&&console.log("[BackgroundLoader] assets: ",this._assetList),this._isActive&&!this._isLoading&&this._next()}async _next(){if(this._assetList.length&&this._isActive){this._isLoading=!0;const e=[],t=Math.min(this._assetList.length,this._maxConcurrent);for(let i=0;i<t;i++)e.push(this._assetList.pop());await this._loader.load(e),this._isLoading=!1,this._next()}}get active(){return this._isActive}set active(e){this._isActive!==e&&(this._isActive=e,e&&!this._isLoading&&this._next())}}const I_={extension:{type:J.CacheParser,name:"cacheTextureArray"},test:s=>Array.isArray(s)&&s.every(e=>e instanceof _e),getCacheableAssets:(s,e)=>{const t={};return s.forEach(i=>{e.forEach((r,n)=>{t[i+(n===0?"":n+1)]=r})}),t}};async function jl(s){if("Image"in globalThis)return new Promise(e=>{const t=new Image;t.onload=()=>{e(!0)},t.onerror=()=>{e(!1)},t.src=s});if("createImageBitmap"in globalThis&&"fetch"in globalThis){try{const e=await(await fetch(s)).blob();await createImageBitmap(e)}catch{return!1}return!0}return!1}const E_={extension:{type:J.DetectionParser,priority:1},test:async()=>jl("data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgANogQEAwgMg8f8D///8WfhwB8+ErK42A="),add:async s=>[...s,"avif"],remove:async s=>s.filter(e=>e!=="avif")},lo=["png","jpg","jpeg"],z_={extension:{type:J.DetectionParser,priority:-1},test:()=>Promise.resolve(!0),add:async s=>[...s,...lo],remove:async s=>s.filter(e=>!lo.includes(e))},R_="WorkerGlobalScope"in globalThis&&globalThis instanceof globalThis.WorkerGlobalScope;function en(s){return R_?!1:document.createElement("video").canPlayType(s)!==""}const B_={extension:{type:J.DetectionParser,priority:0},test:async()=>en("video/mp4"),add:async s=>[...s,"mp4","m4v"],remove:async s=>s.filter(e=>e!=="mp4"&&e!=="m4v")},F_={extension:{type:J.DetectionParser,priority:0},test:async()=>en("video/ogg"),add:async s=>[...s,"ogv"],remove:async s=>s.filter(e=>e!=="ogv")},L_={extension:{type:J.DetectionParser,priority:0},test:async()=>en("video/webm"),add:async s=>[...s,"webm"],remove:async s=>s.filter(e=>e!=="webm")},O_={extension:{type:J.DetectionParser,priority:0},test:async()=>jl("data:image/webp;base64,UklGRh4AAABXRUJQVlA4TBEAAAAvAAAAAAfQ//73v/+BiOh/AAA="),add:async s=>[...s,"webp"],remove:async s=>s.filter(e=>e!=="webp")};class G_{constructor(){this._parsers=[],this._parsersValidated=!1,this.parsers=new Proxy(this._parsers,{set:(e,t,i)=>(this._parsersValidated=!1,e[t]=i,!0)}),this.promiseCache={}}reset(){this._parsersValidated=!1,this.promiseCache={}}_getLoadPromiseAndParser(e,t){const i={promise:null,parser:null};return i.promise=(async()=>{var o,a;let r=null,n=null;if(t.loadParser&&(n=this._parserHash[t.loadParser],n||Ve(`[Assets] specified load parser "${t.loadParser}" not found while loading ${e}`)),!n){for(let l=0;l<this.parsers.length;l++){const h=this.parsers[l];if(h.load&&((o=h.test)!=null&&o.call(h,e,t,this))){n=h;break}}if(!n)return Ve(`[Assets] ${e} could not be loaded as we don't know how to parse it, ensure the correct parser has been added`),null}r=await n.load(e,t,this),i.parser=n;for(let l=0;l<this.parsers.length;l++){const h=this.parsers[l];h.parse&&h.parse&&await((a=h.testParse)==null?void 0:a.call(h,r,t,this))&&(r=await h.parse(r,t,this)||r,i.parser=h)}return r})(),i}async load(e,t){this._parsersValidated||this._validateParsers();let i=0;const r={},n=ds(e),o=At(e,h=>({alias:[h],src:h,data:{}})),a=o.length,l=o.map(async h=>{const c=_t.toAbsolute(h.src);if(!r[h.src])try{this.promiseCache[c]||(this.promiseCache[c]=this._getLoadPromiseAndParser(c,h)),r[h.src]=await this.promiseCache[c].promise,t&&t(++i/a)}catch(u){throw delete this.promiseCache[c],delete r[h.src],new Error(`[Loader.load] Failed to load ${c}.
${u}`)}});return await Promise.all(l),n?r[o[0].src]:r}async unload(e){const i=At(e,r=>({alias:[r],src:r})).map(async r=>{var a,l;const n=_t.toAbsolute(r.src),o=this.promiseCache[n];if(o){const h=await o.promise;delete this.promiseCache[n],await((l=(a=o.parser)==null?void 0:a.unload)==null?void 0:l.call(a,h,r,this))}});await Promise.all(i)}_validateParsers(){this._parsersValidated=!0,this._parserHash=this._parsers.filter(e=>e.name).reduce((e,t)=>(t.name?e[t.name]&&Ve(`[Assets] loadParser name conflict "${t.name}"`):Ve("[Assets] loadParser should have a name"),{...e,[t.name]:t}),{})}}function yi(s,e){if(Array.isArray(e)){for(const t of e)if(s.startsWith(`data:${t}`))return!0;return!1}return s.startsWith(`data:${e}`)}function wi(s,e){const t=s.split("?")[0],i=_t.extname(t).toLowerCase();return Array.isArray(e)?e.includes(i):i===e}const D_=".json",N_="application/json",U_={extension:{type:J.LoadParser,priority:Xt.Low},name:"loadJson",test(s){return yi(s,N_)||wi(s,D_)},async load(s){return await(await Le.get().fetch(s)).json()}},W_=".txt",H_="text/plain",V_={name:"loadTxt",extension:{type:J.LoadParser,priority:Xt.Low,name:"loadTxt"},test(s){return yi(s,H_)||wi(s,W_)},async load(s){return await(await Le.get().fetch(s)).text()}},X_=["normal","bold","100","200","300","400","500","600","700","800","900"],Y_=[".ttf",".otf",".woff",".woff2"],j_=["font/ttf","font/otf","font/woff","font/woff2"],q_=/^(--|-?[A-Z_])[0-9A-Z_-]*$/i;function K_(s){const e=_t.extname(s),r=_t.basename(s,e).replace(/(-|_)/g," ").toLowerCase().split(" ").map(a=>a.charAt(0).toUpperCase()+a.slice(1));let n=r.length>0;for(const a of r)if(!a.match(q_)){n=!1;break}let o=r.join(" ");return n||(o=`"${o.replace(/[\\"]/g,"\\$&")}"`),o}const Z_=/^[0-9A-Za-z%:/?#\[\]@!\$&'()\*\+,;=\-._~]*$/;function Q_(s){return Z_.test(s)?s:encodeURI(s)}const J_={extension:{type:J.LoadParser,priority:Xt.Low},name:"loadWebFont",test(s){return yi(s,j_)||wi(s,Y_)},async load(s,e){var i,r,n;const t=Le.get().getFontFaceSet();if(t){const o=[],a=((i=e.data)==null?void 0:i.family)??K_(s),l=((n=(r=e.data)==null?void 0:r.weights)==null?void 0:n.filter(c=>X_.includes(c)))??["normal"],h=e.data??{};for(let c=0;c<l.length;c++){const u=l[c],f=new FontFace(a,`url(${Q_(s)})`,{...h,weight:u});await f.load(),t.add(f),o.push(f)}return Be.set(`${a}-and-url`,{url:s,fontFaces:o}),o.length===1?o[0]:o}return Ve("[loadWebFont] FontFace API is not supported. Skipping loading font"),null},unload(s){(Array.isArray(s)?s:[s]).forEach(e=>{Be.remove(`${e.family}-and-url`),Le.get().getFontFaceSet().delete(e)})}};function tn(s,e=1){var i;const t=(i=bi.RETINA_PREFIX)==null?void 0:i.exec(s);return t?parseFloat(t[1]):e}function sn(s,e,t){s.label=t,s._sourceOrigin=t;const i=new _e({source:s,label:t}),r=()=>{delete e.promiseCache[t],Be.has(t)&&Be.remove(t)};return i.source.once("destroy",()=>{e.promiseCache[t]&&(Ve("[Assets] A TextureSource managed by Assets was destroyed instead of unloaded! Use Assets.unload() instead of destroying the TextureSource."),r())}),i.once("destroy",()=>{s.destroyed||(Ve("[Assets] A Texture managed by Assets was destroyed instead of unloaded! Use Assets.unload() instead of destroying the Texture."),r())}),i}const $_=".svg",eg="image/svg+xml",tg={extension:{type:J.LoadParser,priority:Xt.Low,name:"loadSVG"},name:"loadSVG",config:{crossOrigin:"anonymous",parseAsGraphicsContext:!1},test(s){return yi(s,eg)||wi(s,$_)},async load(s,e,t){var i;return((i=e.data)==null?void 0:i.parseAsGraphicsContext)??this.config.parseAsGraphicsContext?sg(s):ig(s,e,t,this.config.crossOrigin)},unload(s){s.destroy(!0)}};async function ig(s,e,t,i){var m,p,b;const n=await(await Le.get().fetch(s)).blob(),o=URL.createObjectURL(n),a=new Image;a.src=o,a.crossOrigin=i,await a.decode(),URL.revokeObjectURL(o);const l=document.createElement("canvas"),h=l.getContext("2d"),c=((m=e.data)==null?void 0:m.resolution)||tn(s),u=((p=e.data)==null?void 0:p.width)??a.width,f=((b=e.data)==null?void 0:b.height)??a.height;l.width=u*c,l.height=f*c,h.drawImage(a,0,0,u*c,f*c);const{parseAsGraphicsContext:d,..._}=e.data??{},g=new pi({resource:l,alphaMode:"premultiply-alpha-on-upload",resolution:c,..._});return sn(g,t,s)}async function sg(s){const t=await(await Le.get().fetch(s)).text(),i=new bt;return i.svg(t),i}const rg=`(function () {
    'use strict';

    const WHITE_PNG = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAwMCAO+ip1sAAAAASUVORK5CYII=";
    async function checkImageBitmap() {
      try {
        if (typeof createImageBitmap !== "function")
          return false;
        const response = await fetch(WHITE_PNG);
        const imageBlob = await response.blob();
        const imageBitmap = await createImageBitmap(imageBlob);
        return imageBitmap.width === 1 && imageBitmap.height === 1;
      } catch (_e) {
        return false;
      }
    }
    void checkImageBitmap().then((result) => {
      self.postMessage(result);
    });

})();
`;let ui=null,Br=class{constructor(){ui||(ui=URL.createObjectURL(new Blob([rg],{type:"application/javascript"}))),this.worker=new Worker(ui)}};Br.revokeObjectURL=function(){ui&&(URL.revokeObjectURL(ui),ui=null)};const ng=`(function () {
    'use strict';

    async function loadImageBitmap(url, alphaMode) {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(\`[WorkerManager.loadImageBitmap] Failed to fetch \${url}: \${response.status} \${response.statusText}\`);
      }
      const imageBlob = await response.blob();
      return alphaMode === "premultiplied-alpha" ? createImageBitmap(imageBlob, { premultiplyAlpha: "none" }) : createImageBitmap(imageBlob);
    }
    self.onmessage = async (event) => {
      try {
        const imageBitmap = await loadImageBitmap(event.data.data[0], event.data.data[1]);
        self.postMessage({
          data: imageBitmap,
          uuid: event.data.uuid,
          id: event.data.id
        }, [imageBitmap]);
      } catch (e) {
        self.postMessage({
          error: e,
          uuid: event.data.uuid,
          id: event.data.id
        });
      }
    };

})();
`;let di=null;class ql{constructor(){di||(di=URL.createObjectURL(new Blob([ng],{type:"application/javascript"}))),this.worker=new Worker(di)}}ql.revokeObjectURL=function(){di&&(URL.revokeObjectURL(di),di=null)};let ho=0,lr;class og{constructor(){this._initialized=!1,this._createdWorkers=0,this._workerPool=[],this._queue=[],this._resolveHash={}}isImageBitmapSupported(){return this._isImageBitmapSupported!==void 0?this._isImageBitmapSupported:(this._isImageBitmapSupported=new Promise(e=>{const{worker:t}=new Br;t.addEventListener("message",i=>{t.terminate(),Br.revokeObjectURL(),e(i.data)})}),this._isImageBitmapSupported)}loadImageBitmap(e,t){var i;return this._run("loadImageBitmap",[e,(i=t==null?void 0:t.data)==null?void 0:i.alphaMode])}async _initWorkers(){this._initialized||(this._initialized=!0)}_getWorker(){lr===void 0&&(lr=navigator.hardwareConcurrency||4);let e=this._workerPool.pop();return!e&&this._createdWorkers<lr&&(this._createdWorkers++,e=new ql().worker,e.addEventListener("message",t=>{this._complete(t.data),this._returnWorker(t.target),this._next()})),e}_returnWorker(e){this._workerPool.push(e)}_complete(e){e.error!==void 0?this._resolveHash[e.uuid].reject(e.error):this._resolveHash[e.uuid].resolve(e.data),this._resolveHash[e.uuid]=null}async _run(e,t){await this._initWorkers();const i=new Promise((r,n)=>{this._queue.push({id:e,arguments:t,resolve:r,reject:n})});return this._next(),i}_next(){if(!this._queue.length)return;const e=this._getWorker();if(!e)return;const t=this._queue.pop(),i=t.id;this._resolveHash[ho]={resolve:t.resolve,reject:t.reject},e.postMessage({data:t.arguments,uuid:ho++,id:i})}}const co=new og,ag=[".jpeg",".jpg",".png",".webp",".avif"],lg=["image/jpeg","image/png","image/webp","image/avif"];async function hg(s,e){var r;const t=await Le.get().fetch(s);if(!t.ok)throw new Error(`[loadImageBitmap] Failed to fetch ${s}: ${t.status} ${t.statusText}`);const i=await t.blob();return((r=e==null?void 0:e.data)==null?void 0:r.alphaMode)==="premultiplied-alpha"?createImageBitmap(i,{premultiplyAlpha:"none"}):createImageBitmap(i)}const Kl={name:"loadTextures",extension:{type:J.LoadParser,priority:Xt.High,name:"loadTextures"},config:{preferWorkers:!0,preferCreateImageBitmap:!0,crossOrigin:"anonymous"},test(s){return yi(s,lg)||wi(s,ag)},async load(s,e,t){var n;let i=null;globalThis.createImageBitmap&&this.config.preferCreateImageBitmap?this.config.preferWorkers&&await co.isImageBitmapSupported()?i=await co.loadImageBitmap(s,e):i=await hg(s,e):i=await new Promise((o,a)=>{i=new Image,i.crossOrigin=this.config.crossOrigin,i.src=s,i.complete?o(i):(i.onload=()=>{o(i)},i.onerror=a)});const r=new pi({resource:i,alphaMode:"premultiply-alpha-on-upload",resolution:((n=e.data)==null?void 0:n.resolution)||tn(s),...e.data});return sn(r,t,s)},unload(s){s.destroy(!0)}},Zl=[".mp4",".m4v",".webm",".ogg",".ogv",".h264",".avi",".mov"],cg=Zl.map(s=>`video/${s.substring(1)}`);function ug(s,e,t){t===void 0&&!e.startsWith("data:")?s.crossOrigin=fg(e):t!==!1&&(s.crossOrigin=typeof t=="string"?t:"anonymous")}function dg(s){return new Promise((e,t)=>{s.addEventListener("canplaythrough",i),s.addEventListener("error",r),s.load();function i(){n(),e()}function r(o){n(),t(o)}function n(){s.removeEventListener("canplaythrough",i),s.removeEventListener("error",r)}})}function fg(s,e=globalThis.location){if(s.startsWith("data:"))return"";e||(e=globalThis.location);const t=new URL(s,document.baseURI);return t.hostname!==e.hostname||t.port!==e.port||t.protocol!==e.protocol?"anonymous":""}const _g={name:"loadVideo",extension:{type:J.LoadParser,name:"loadVideo"},test(s){const e=yi(s,cg),t=wi(s,Zl);return e||t},async load(s,e,t){var l,h;const i={...rs.defaultOptions,resolution:((l=e.data)==null?void 0:l.resolution)||tn(s),alphaMode:((h=e.data)==null?void 0:h.alphaMode)||await Ja(),...e.data},r=document.createElement("video"),n={preload:i.autoLoad!==!1?"auto":void 0,"webkit-playsinline":i.playsinline!==!1?"":void 0,playsinline:i.playsinline!==!1?"":void 0,muted:i.muted===!0?"":void 0,loop:i.loop===!0?"":void 0,autoplay:i.autoPlay!==!1?"":void 0};Object.keys(n).forEach(c=>{const u=n[c];u!==void 0&&r.setAttribute(c,u)}),i.muted===!0&&(r.muted=!0),ug(r,s,i.crossorigin);const o=document.createElement("source");let a;if(s.startsWith("data:"))a=s.slice(5,s.indexOf(";"));else if(!s.startsWith("blob:")){const c=s.split("?")[0].slice(s.lastIndexOf(".")+1).toLowerCase();a=rs.MIME_TYPES[c]||`video/${c}`}return o.src=s,a&&(o.type=a),new Promise(c=>{const u=async()=>{const f=new rs({...i,resource:r});r.removeEventListener("canplay",u),e.data.preload&&await dg(r),c(sn(f,t,s))};r.addEventListener("canplay",u),r.appendChild(o)})},unload(s){s.destroy(!0)}},Ql={extension:{type:J.ResolveParser,name:"resolveTexture"},test:Kl.test,parse:s=>{var e;return{resolution:parseFloat(((e=bi.RETINA_PREFIX.exec(s))==null?void 0:e[1])??"1"),format:s.split(".").pop(),src:s}}},gg={extension:{type:J.ResolveParser,priority:-2,name:"resolveJson"},test:s=>bi.RETINA_PREFIX.test(s)&&s.endsWith(".json"),parse:Ql.parse};class mg{constructor(){this._detections=[],this._initialized=!1,this.resolver=new bi,this.loader=new G_,this.cache=Be,this._backgroundLoader=new P_(this.loader),this._backgroundLoader.active=!0,this.reset()}async init(e={}){var n,o;if(this._initialized){Ve("[Assets]AssetManager already initialized, did you load before calling this Assets.init()?");return}if(this._initialized=!0,e.defaultSearchParams&&this.resolver.setDefaultSearchParams(e.defaultSearchParams),e.basePath&&(this.resolver.basePath=e.basePath),e.bundleIdentifier&&this.resolver.setBundleIdentifier(e.bundleIdentifier),e.manifest){let a=e.manifest;typeof a=="string"&&(a=await this.load(a)),this.resolver.addManifest(a)}const t=((n=e.texturePreference)==null?void 0:n.resolution)??1,i=typeof t=="number"?[t]:t,r=await this._detectFormats({preferredFormats:(o=e.texturePreference)==null?void 0:o.format,skipDetections:e.skipDetections,detections:this._detections});this.resolver.prefer({params:{format:r,resolution:i}}),e.preferences&&this.setPreferences(e.preferences)}add(e){this.resolver.add(e)}async load(e,t){this._initialized||await this.init();const i=ds(e),r=At(e).map(a=>{if(typeof a!="string"){const l=this.resolver.getAlias(a);return l.some(h=>!this.resolver.hasKey(h))&&this.add(a),Array.isArray(l)?l[0]:l}return this.resolver.hasKey(a)||this.add({alias:a,src:a}),a}),n=this.resolver.resolve(r),o=await this._mapLoadToResolve(n,t);return i?o[r[0]]:o}addBundle(e,t){this.resolver.addBundle(e,t)}async loadBundle(e,t){this._initialized||await this.init();let i=!1;typeof e=="string"&&(i=!0,e=[e]);const r=this.resolver.resolveBundle(e),n={},o=Object.keys(r);let a=0,l=0;const h=()=>{t==null||t(++a/l)},c=o.map(u=>{const f=r[u];return l+=Object.keys(f).length,this._mapLoadToResolve(f,h).then(d=>{n[u]=d})});return await Promise.all(c),i?n[e[0]]:n}async backgroundLoad(e){this._initialized||await this.init(),typeof e=="string"&&(e=[e]);const t=this.resolver.resolve(e);this._backgroundLoader.add(Object.values(t))}async backgroundLoadBundle(e){this._initialized||await this.init(),typeof e=="string"&&(e=[e]);const t=this.resolver.resolveBundle(e);Object.values(t).forEach(i=>{this._backgroundLoader.add(Object.values(i))})}reset(){this.resolver.reset(),this.loader.reset(),this.cache.reset(),this._initialized=!1}get(e){if(typeof e=="string")return Be.get(e);const t={};for(let i=0;i<e.length;i++)t[i]=Be.get(e[i]);return t}async _mapLoadToResolve(e,t){const i=[...new Set(Object.values(e))];this._backgroundLoader.active=!1;const r=await this.loader.load(i,t);this._backgroundLoader.active=!0;const n={};return i.forEach(o=>{const a=r[o.src],l=[o.src];o.alias&&l.push(...o.alias),l.forEach(h=>{n[h]=a}),Be.set(l,a)}),n}async unload(e){this._initialized||await this.init();const t=At(e).map(r=>typeof r!="string"?r.src:r),i=this.resolver.resolve(t);await this._unloadFromResolved(i)}async unloadBundle(e){this._initialized||await this.init(),e=At(e);const t=this.resolver.resolveBundle(e),i=Object.keys(t).map(r=>this._unloadFromResolved(t[r]));await Promise.all(i)}async _unloadFromResolved(e){const t=Object.values(e);t.forEach(i=>{Be.remove(i.src)}),await this.loader.unload(t)}async _detectFormats(e){let t=[];e.preferredFormats&&(t=Array.isArray(e.preferredFormats)?e.preferredFormats:[e.preferredFormats]);for(const i of e.detections)e.skipDetections||await i.test()?t=await i.add(t):e.skipDetections||(t=await i.remove(t));return t=t.filter((i,r)=>t.indexOf(i)===r),t}get detections(){return this._detections}setPreferences(e){this.loader.parsers.forEach(t=>{t.config&&Object.keys(t.config).filter(i=>i in e).forEach(i=>{t.config[i]=e[i]})})}}const $t=new mg;rt.handleByList(J.LoadParser,$t.loader.parsers).handleByList(J.ResolveParser,$t.resolver.parsers).handleByList(J.CacheParser,$t.cache.parsers).handleByList(J.DetectionParser,$t.detections);rt.add(I_,z_,E_,O_,B_,F_,L_,U_,V_,J_,tg,Kl,_g,T_,M_,Ql,gg);const uo={loader:J.LoadParser,resolver:J.ResolveParser,cache:J.CacheParser,detection:J.DetectionParser};rt.handle(J.Asset,s=>{const e=s.ref;Object.entries(uo).filter(([t])=>!!e[t]).forEach(([t,i])=>rt.add(Object.assign(e[t],{extension:e[t].extension??i})))},s=>{const e=s.ref;Object.keys(uo).filter(t=>!!e[t]).forEach(t=>rt.remove(e[t]))});class Ue extends Ha{constructor(e){e instanceof bt&&(e={context:e});const{context:t,roundPixels:i,...r}=e||{};super({label:"Graphics",...r}),this.renderPipeId="graphics",t?this._context=t:this._context=this._ownedContext=new bt,this._context.on("update",this.onViewUpdate,this),this.allowChildren=!1,this.roundPixels=i??!1}set context(e){e!==this._context&&(this._context.off("update",this.onViewUpdate,this),this._context=e,this._context.on("update",this.onViewUpdate,this),this.onViewUpdate())}get context(){return this._context}get bounds(){return this._context.bounds}updateBounds(){}containsPoint(e){return this._context.containsPoint(e)}destroy(e){this._ownedContext&&!e?this._ownedContext.destroy(e):(e===!0||(e==null?void 0:e.context)===!0)&&this._context.destroy(e),this._ownedContext=null,this._context=null,super.destroy(e)}_callContextMethod(e,t){return this.context[e](...t),this}setFillStyle(...e){return this._callContextMethod("setFillStyle",e)}setStrokeStyle(...e){return this._callContextMethod("setStrokeStyle",e)}fill(...e){return this._callContextMethod("fill",e)}stroke(...e){return this._callContextMethod("stroke",e)}texture(...e){return this._callContextMethod("texture",e)}beginPath(){return this._callContextMethod("beginPath",[])}cut(){return this._callContextMethod("cut",[])}arc(...e){return this._callContextMethod("arc",e)}arcTo(...e){return this._callContextMethod("arcTo",e)}arcToSvg(...e){return this._callContextMethod("arcToSvg",e)}bezierCurveTo(...e){return this._callContextMethod("bezierCurveTo",e)}closePath(){return this._callContextMethod("closePath",[])}ellipse(...e){return this._callContextMethod("ellipse",e)}circle(...e){return this._callContextMethod("circle",e)}path(...e){return this._callContextMethod("path",e)}lineTo(...e){return this._callContextMethod("lineTo",e)}moveTo(...e){return this._callContextMethod("moveTo",e)}quadraticCurveTo(...e){return this._callContextMethod("quadraticCurveTo",e)}rect(...e){return this._callContextMethod("rect",e)}roundRect(...e){return this._callContextMethod("roundRect",e)}poly(...e){return this._callContextMethod("poly",e)}regularPoly(...e){return this._callContextMethod("regularPoly",e)}roundPoly(...e){return this._callContextMethod("roundPoly",e)}roundShape(...e){return this._callContextMethod("roundShape",e)}filletRect(...e){return this._callContextMethod("filletRect",e)}chamferRect(...e){return this._callContextMethod("chamferRect",e)}star(...e){return this._callContextMethod("star",e)}svg(...e){return this._callContextMethod("svg",e)}restore(...e){return this._callContextMethod("restore",e)}save(){return this._callContextMethod("save",[])}getTransform(){return this.context.getTransform()}resetTransform(){return this._callContextMethod("resetTransform",[])}rotateTransform(...e){return this._callContextMethod("rotate",e)}scaleTransform(...e){return this._callContextMethod("scale",e)}setTransform(...e){return this._callContextMethod("setTransform",e)}transform(...e){return this._callContextMethod("transform",e)}translateTransform(...e){return this._callContextMethod("translate",e)}clear(){return this._callContextMethod("clear",[])}get fillStyle(){return this._context.fillStyle}set fillStyle(e){this._context.fillStyle=e}get strokeStyle(){return this._context.strokeStyle}set strokeStyle(e){this._context.strokeStyle=e}clone(e=!1){return e?new Ue(this._context.clone()):(this._ownedContext=null,new Ue(this._context))}lineStyle(e,t,i){be(xe,"Graphics#lineStyle is no longer needed. Use Graphics#setStrokeStyle to set the stroke style.");const r={};return e&&(r.width=e),t&&(r.color=t),i&&(r.alpha=i),this.context.strokeStyle=r,this}beginFill(e,t){be(xe,"Graphics#beginFill is no longer needed. Use Graphics#fill to fill the shape with the desired style.");const i={};return e!==void 0&&(i.color=e),t!==void 0&&(i.alpha=t),this.context.fillStyle=i,this}endFill(){be(xe,"Graphics#endFill is no longer needed. Use Graphics#fill to fill the shape with the desired style."),this.context.fill();const e=this.context.strokeStyle;return(e.width!==bt.defaultStrokeStyle.width||e.color!==bt.defaultStrokeStyle.color||e.alpha!==bt.defaultStrokeStyle.alpha)&&this.context.stroke(),this}drawCircle(...e){return be(xe,"Graphics#drawCircle has been renamed to Graphics#circle"),this._callContextMethod("circle",e)}drawEllipse(...e){return be(xe,"Graphics#drawEllipse has been renamed to Graphics#ellipse"),this._callContextMethod("ellipse",e)}drawPolygon(...e){return be(xe,"Graphics#drawPolygon has been renamed to Graphics#poly"),this._callContextMethod("poly",e)}drawRect(...e){return be(xe,"Graphics#drawRect has been renamed to Graphics#rect"),this._callContextMethod("rect",e)}drawRoundedRect(...e){return be(xe,"Graphics#drawRoundedRect has been renamed to Graphics#roundRect"),this._callContextMethod("roundRect",e)}drawStar(...e){return be(xe,"Graphics#drawStar has been renamed to Graphics#star"),this._callContextMethod("star",e)}}class it extends _e{static create(e){return new it({source:new Pt(e)})}resize(e,t,i){return this.source.resize(e,t,i),this}}var Fr=(s=>(s.CLAMP="clamp-to-edge",s.REPEAT="repeat",s.MIRRORED_REPEAT="mirror-repeat",s))(Fr||{});new Proxy(Fr,{get(s,e){return be(xe,`DRAW_MODES.${e} is deprecated, use '${Fr[e]}' instead`),s[e]}});var Lr=(s=>(s.NEAREST="nearest",s.LINEAR="linear",s))(Lr||{});const fo=new Proxy(Lr,{get(s,e){return be(xe,`DRAW_MODES.${e} is deprecated, use '${Lr[e]}' instead`),s[e]}});rt.add(Lc,Oc);function is(s,e,t){return s<e?e:s>t?t:s}function as(s,e,t){return new Promise(i=>{var a;if(!e){i(null);return}const r=e.getLocalBounds(),n=t?new Pe(t.x,t.y,t.width,t.height):new Pe(0,0,r.width,r.height),o=s.extract.canvas({target:e,resolution:1,frame:n});(a=o.toBlob)==null||a.call(o,l=>{l||i(null),i(l)})})}function pg(s){var _;let e,t,i=`translate(${s[2][0]}px,${s[2][1]}px)`,r,n,o,a=`translateX(${s[1]*(((_=s[8])==null?void 0:_.width)??1)}px)`,l,h,c,u=`translateX(${s[4]}px)`,f,d;return{c(){e=Y("div"),t=Y("div"),r=Z(),n=Y("div"),o=Y("div"),l=Z(),h=Y("div"),c=Y("div"),this.h()},l(g){e=j(g,"DIV",{class:!0,style:!0});var m=L(e);t=j(m,"DIV",{class:!0}),L(t).forEach(k),m.forEach(k),r=Q(g),n=j(g,"DIV",{class:!0,style:!0});var p=L(n);o=j(p,"DIV",{class:!0}),L(o).forEach(k),p.forEach(k),l=Q(g),h=j(g,"DIV",{class:!0});var b=L(h);c=j(b,"DIV",{class:!0}),L(c).forEach(k),b.forEach(k),this.h()},h(){x(t,"class","marker svelte-9hvvq8"),pe(t,"transform",i),pe(t,"background",s[0]),x(e,"class","color-gradient svelte-9hvvq8"),pe(e,"--hue",s[3]),x(o,"class","opacity-marker svelte-9hvvq8"),pe(o,"background","white"),pe(o,"transform",a),x(n,"class","opacity-slider svelte-9hvvq8"),pe(n,"--color",s[0]==="auto"?"transparent":s[0]),x(c,"class","marker svelte-9hvvq8"),pe(c,"background","hsl("+s[3]+", 100%, 50%)"),pe(c,"transform",u),x(h,"class","hue-slider svelte-9hvvq8")},m(g,m){D(g,e,m),O(e,t),s[14](e),D(g,r,m),D(g,n,m),O(n,o),s[15](n),D(g,l,m),D(g,h,m),O(h,c),s[16](h),f||(d=[Fe(window,"mousemove",s[11]),Fe(window,"mouseup",s[12]),Fe(e,"mousedown",s[10]),Fe(n,"mousedown",s[13]),Fe(h,"mousedown",s[9])],f=!0)},p(g,[m]){var p;m&4&&i!==(i=`translate(${g[2][0]}px,${g[2][1]}px)`)&&pe(t,"transform",i),m&1&&pe(t,"background",g[0]),m&8&&pe(e,"--hue",g[3]),m&258&&a!==(a=`translateX(${g[1]*(((p=g[8])==null?void 0:p.width)??1)}px)`)&&pe(o,"transform",a),m&1&&pe(n,"--color",g[0]==="auto"?"transparent":g[0]),m&8&&pe(c,"background","hsl("+g[3]+", 100%, 50%)"),m&16&&u!==(u=`translateX(${g[4]}px)`)&&pe(c,"transform",u)},i:$,o:$,d(g){g&&(k(e),k(r),k(n),k(l),k(h)),s[14](null),s[15](null),s[16](null),f=!1,mi(d)}}}function _o(s){const e=s.s,t=s.v;let i=e*t;const r=s.h/60;let n=i*(1-Math.abs(r%2-1));const o=t-i;i=i+o,n=n+o;const a=Math.floor(r)%6,l=[i,n,o,o,n,i][a],h=[n,i,i,n,o,o][a],c=[o,o,n,i,i,n][a];return`rgba(${l*255}, ${h*255}, ${c*255}, ${s.a})`}function bg(s,e,t){let{color:i="rgb(255, 255, 255)"}=e,r=[0,0],n=null,o=!1,a=[0,0],l=0,h=0,c=null,u=!1,f=!1;function d(W){c=W.currentTarget.getBoundingClientRect(),u=!0,_(W.clientX)}function _(W){if(!c)return;const ae=is(W-c.left,0,c.width);t(4,h=ae);const H=ae/c.width*360;t(3,l=H),t(0,i=_o({h:H,s:a[0],v:a[1],a:1}))}function g(W,ae){if(!n)return;const H=is(W-n.left,0,n.width),q=is(ae-n.top,0,n.height);t(2,r=[H,q]);const Ke={h:l*1,s:H/n.width,v:1-q/n.height,a:1};a=[Ke.s,Ke.v],t(0,i=_o(Ke))}function m(W){o=!0,n=W.currentTarget.getBoundingClientRect(),g(W.clientX,W.clientY)}function p(W){o&&g(W.clientX,W.clientY),u&&_(W.clientX),f&&E(W.clientX)}function b(){o&&(o=!1),u&&(u=!1),f&&(f=!1)}async function y(W){if(o||u||(await ls(),!W))return;n||(n=w.getBoundingClientRect()),c||(c=A.getBoundingClientRect());const ae=ft(W).toHsv(),H=ae.s*n.width,q=(1-ae.v)*n.height;t(2,r=[H,q]),a=[ae.s,ae.v],t(3,l=ae.h),t(4,h=ae.h/360*c.width)}let w,A,{opacity:T=1}=e,P;function U(W){t(8,R=W.currentTarget.getBoundingClientRect()),f=!0,E(W.clientX)}let R=null;function E(W){if(!R)return;const ae=is(W-R.left,0,R.width);t(1,T=ae/R.width)}ga(()=>{t(8,R=P.getBoundingClientRect())});function M(W){ye[W?"unshift":"push"](()=>{w=W,t(5,w)})}function z(W){ye[W?"unshift":"push"](()=>{P=W,t(7,P)})}function V(W){ye[W?"unshift":"push"](()=>{A=W,t(6,A)})}return s.$$set=W=>{"color"in W&&t(0,i=W.color),"opacity"in W&&t(1,T=W.opacity)},s.$$.update=()=>{s.$$.dirty&1&&y(i)},[i,T,r,l,h,w,A,P,R,d,m,p,b,U,M,z,V]}class yg extends ke{constructor(e){super(),Ae(this,e,bg,pg,Se,{color:0,opacity:1})}}function go(s,e,t){const i=s.slice();i[17]=e[t],i[21]=t;const r=Di(i[17]);i[18]=r;const n=i[6](i[17]);return i[19]=n,i}function mo(s,e,t){const i=s.slice();i[22]=e[t],i[21]=t;const r=Di(i[22]);i[18]=r;const n=i[6](i[22]);return i[19]=n,i}function po(s){let e;return{c(){e=Y("span"),this.h()},l(t){e=j(t,"SPAN",{class:!0}),L(e).forEach(k),this.h()},h(){x(e,"class","svelte-1ngpqve"),oe(e,"lg",s[2])},m(t,i){D(t,e,i)},p(t,i){i&4&&oe(e,"lg",t[2])},d(t){t&&k(e)}}}function bo(s){let e,t,i,r,n,o,a,l=s[0]&&yo(s),h=Ft(s[2]),c=[];for(let u=0;u<h.length;u+=1)c[u]=wo(mo(s,h,u));return{c(){e=Y("div"),l&&l.c(),t=Z();for(let u=0;u<c.length;u+=1)c[u].c();i=Z(),r=Y("button"),this.h()},l(u){e=j(u,"DIV",{class:!0});var f=L(e);l&&l.l(f),t=Q(f);for(let d=0;d<c.length;d+=1)c[d].l(f);i=Q(f),r=j(f,"BUTTON",{class:!0}),L(r).forEach(k),f.forEach(k),this.h()},h(){x(r,"class","color colorpicker svelte-1ngpqve"),oe(r,"hidden",!s[0]),x(e,"class","swatch svelte-1ngpqve")},m(u,f){D(u,e,f),l&&l.m(e,null),O(e,t);for(let d=0;d<c.length;d+=1)c[d]&&c[d].m(e,null);O(e,i),O(e,r),n=!0,o||(a=Fe(r,"click",s[8]),o=!0)},p(u,f){if(u[0]?l?(l.p(u,f),f&1&&C(l,1)):(l=yo(u),l.c(),C(l,1),l.m(e,t)):l&&(he(),I(l,1,1,()=>{l=null}),ce()),f&206){h=Ft(u[2]);let d;for(d=0;d<h.length;d+=1){const _=mo(u,h,d);c[d]?c[d].p(_,f):(c[d]=wo(_),c[d].c(),c[d].m(e,i))}for(;d<c.length;d+=1)c[d].d(1);c.length=h.length}(!n||f&1)&&oe(r,"hidden",!u[0])},i(u){n||(C(l),n=!0)},o(u){I(l),n=!1},d(u){u&&k(e),l&&l.d(),_s(c,u),o=!1,a()}}}function yo(s){let e,t;return e=new gt({props:{Icon:Sa,roundedness:"very",background:Di(s[1]),color:"white"}}),e.$on("click",s[12]),{c(){ee(e.$$.fragment)},l(i){te(e.$$.fragment,i)},m(i,r){ie(e,i,r),t=!0},p(i,r){const n={};r&2&&(n.background=Di(i[1])),e.$set(n)},i(i){t||(C(e.$$.fragment,i),t=!0)},o(i){I(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function wo(s){let e,t,i;function r(){return s[13](s[21],s[18],s[19])}return{c(){e=Y("button"),this.h()},l(n){e=j(n,"BUTTON",{class:!0,style:!0}),L(e).forEach(k),this.h()},h(){x(e,"class","color svelte-1ngpqve"),pe(e,"background-color",s[18]),oe(e,"empty",s[22]===null),oe(e,"selected",`${s[18]}-${s[19]}`==`${s[1]}-${s[3]}`),pe(e,"opacity",s[19])},m(n,o){D(n,e,o),t||(i=Fe(e,"click",r),t=!0)},p(n,o){s=n,o&4&&pe(e,"background-color",s[18]),o&4&&oe(e,"empty",s[22]===null),o&78&&oe(e,"selected",`${s[18]}-${s[19]}`==`${s[1]}-${s[3]}`);const a=o&4;(o&4||a)&&pe(e,"opacity",s[19])},d(n){n&&k(e),t=!1,i()}}}function xo(s){let e,t,i;function r(){return s[14](s[21],s[18],s[19])}return{c(){e=Y("button"),this.h()},l(n){e=j(n,"BUTTON",{class:!0,style:!0}),L(e).forEach(k),this.h()},h(){x(e,"class","color svelte-1ngpqve"),pe(e,"background-color",s[18]),oe(e,"empty",s[17]===null),oe(e,"selected",`${s[18]}-${s[19]}`==`${s[1]}-${s[3]}`),pe(e,"opacity",s[19])},m(n,o){D(n,e,o),t||(i=Fe(e,"click",r),t=!0)},p(n,o){s=n,o&16&&pe(e,"background-color",s[18]),o&16&&oe(e,"empty",s[17]===null),o&90&&oe(e,"selected",`${s[18]}-${s[19]}`==`${s[1]}-${s[3]}`);const a=o&16;(o&16||a)&&pe(e,"opacity",s[19])},d(n){n&&k(e),t=!1,i()}}}function wg(s){let e,t,i,r,n,o,a=!s[0]&&po(s),l=s[2]&&bo(s),h=Ft(s[4]),c=[];for(let u=0;u<h.length;u+=1)c[u]=xo(go(s,h,u));return{c(){a&&a.c(),e=Z(),t=Y("div"),i=Y("div"),l&&l.c(),r=Z(),n=Y("menu");for(let u=0;u<c.length;u+=1)c[u].c();this.h()},l(u){a&&a.l(u),e=Q(u),t=j(u,"DIV",{class:!0});var f=L(t);i=j(f,"DIV",{class:!0});var d=L(i);l&&l.l(d),r=Q(d),n=j(d,"MENU",{class:!0});var _=L(n);for(let g=0;g<c.length;g+=1)c[g].l(_);_.forEach(k),d.forEach(k),f.forEach(k),this.h()},h(){x(n,"class","swatch svelte-1ngpqve"),x(i,"class","swatch-container svelte-1ngpqve"),x(t,"class","swatch-wrap svelte-1ngpqve")},m(u,f){a&&a.m(u,f),D(u,e,f),D(u,t,f),O(t,i),l&&l.m(i,null),O(i,r),O(i,n);for(let d=0;d<c.length;d+=1)c[d]&&c[d].m(n,null);o=!0},p(u,[f]){if(u[0]?a&&(a.d(1),a=null):a?a.p(u,f):(a=po(u),a.c(),a.m(e.parentNode,e)),u[2]?l?(l.p(u,f),f&4&&C(l,1)):(l=bo(u),l.c(),C(l,1),l.m(i,r)):l&&(he(),I(l,1,1,()=>{l=null}),ce()),f&218){h=Ft(u[4]);let d;for(d=0;d<h.length;d+=1){const _=go(u,h,d);c[d]?c[d].p(_,f):(c[d]=xo(_),c[d].c(),c[d].m(n,null))}for(;d<c.length;d+=1)c[d].d(1);c.length=h.length}},i(u){o||(C(l),o=!0)},o(u){I(l),o=!1},d(u){u&&(k(e),k(t)),a&&a.d(u),l&&l.d(),_s(c,u)}}}function Di(s){return Array.isArray(s)?s[0]:s}function xg(s,e,t){let i,r,{selected_color:n}=e,{colors:o}=e,{user_colors:a=[]}=e,{show_empty:l=!1}=e,{current_mode:h="hex"}=e,{color_picker:c=!1}=e;const u=Nt();function f(y){return Array.isArray(y)?y[1]:ft(y).getAlpha()}function d(y,w){const A=Di(y);return w==="hex"?ft(A).toHexString():w==="rgb"?ft(A).toRgbString():ft(A).toHslString()}`${o.findIndex(y=>d(y,h)===d(n,h))}`;function _(y,w){`${y}${w.index}`,u(y,w)}function g(){u("select",{index:null,color:n}),t(0,c=!c)}const m=()=>u("add_color"),p=(y,w,A)=>_("edit",{index:y,color:w,opacity:A}),b=(y,w,A)=>_("select",{index:y,color:w,opacity:A});return s.$$set=y=>{"selected_color"in y&&t(1,n=y.selected_color),"colors"in y&&t(9,o=y.colors),"user_colors"in y&&t(2,a=y.user_colors),"show_empty"in y&&t(10,l=y.show_empty),"current_mode"in y&&t(11,h=y.current_mode),"color_picker"in y&&t(0,c=y.color_picker)},s.$$.update=()=>{s.$$.dirty&1536&&t(4,i=l?o:o.filter(y=>y)),s.$$.dirty&2&&t(3,r=f(n))},[c,n,a,r,i,u,f,_,g,o,l,h,m,p,b]}class vg extends ke{constructor(e){super(),Ae(this,e,xg,wg,Se,{selected_color:1,colors:9,user_colors:2,show_empty:10,current_mode:11,color_picker:0})}}function vo(s,e,t){const i=s.slice();return i[11]=e[t][0],i[12]=e[t][1],i}function kg(s){let e,t;return e=new Zh({}),{c(){ee(e.$$.fragment)},l(i){te(e.$$.fragment,i)},m(i,r){ie(e,i,r),t=!0},i(i){t||(C(e.$$.fragment,i),t=!0)},o(i){I(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function ko(s){let e,t=s[11]+"",i,r,n;function o(){return s[8](s[12])}return{c(){e=Y("button"),i=St(t),this.h()},l(a){e=j(a,"BUTTON",{class:!0});var l=L(e);i=Ct(l,t),l.forEach(k),this.h()},h(){x(e,"class","button svelte-w84deg"),oe(e,"active",s[1]===s[12])},m(a,l){D(a,e,l),O(e,i),r||(n=Fe(e,"click",o),r=!0)},p(a,l){s=a,l&10&&oe(e,"active",s[1]===s[12])},d(a){a&&k(e),r=!1,n()}}}function Ag(s){let e,t,i,r,n,o,a,l,h,c,u,f,d,_=s[5]&&kg(),g=Ft(s[3]),m=[];for(let p=0;p<g.length;p+=1)m[p]=ko(vo(s,g,p));return{c(){e=Y("div"),t=Y("button"),i=Z(),r=Y("div"),n=Y("div"),o=Y("input"),a=Z(),l=Y("button"),_&&_.c(),h=Z(),c=Y("div");for(let p=0;p<m.length;p+=1)m[p].c();this.h()},l(p){e=j(p,"DIV",{class:!0});var b=L(e);t=j(b,"BUTTON",{class:!0}),L(t).forEach(k),i=Q(b),r=j(b,"DIV",{});var y=L(r);n=j(y,"DIV",{class:!0});var w=L(n);o=j(w,"INPUT",{type:!0,class:!0}),a=Q(w),l=j(w,"BUTTON",{class:!0});var A=L(l);_&&_.l(A),A.forEach(k),w.forEach(k),h=Q(y),c=j(y,"DIV",{class:!0});var T=L(c);for(let P=0;P<m.length;P+=1)m[P].l(T);T.forEach(k),y.forEach(k),b.forEach(k),this.h()},h(){x(t,"class","swatch svelte-w84deg"),pe(t,"background",s[0]),x(o,"type","text"),o.value=s[2],x(o,"class","svelte-w84deg"),x(l,"class","eyedropper svelte-w84deg"),x(n,"class","input-wrap svelte-w84deg"),x(c,"class","buttons svelte-w84deg"),x(e,"class","input svelte-w84deg")},m(p,b){D(p,e,b),O(e,t),O(e,i),O(e,r),O(r,n),O(n,o),O(n,a),O(n,l),_&&_.m(l,null),O(r,h),O(r,c);for(let y=0;y<m.length;y+=1)m[y]&&m[y].m(c,null);u=!0,f||(d=[Fe(t,"click",s[6]),Fe(o,"change",s[7]),Fe(l,"click",s[4])],f=!0)},p(p,[b]){if(b&1&&pe(t,"background",p[0]),(!u||b&4&&o.value!==p[2])&&(o.value=p[2]),b&10){g=Ft(p[3]);let y;for(y=0;y<g.length;y+=1){const w=vo(p,g,y);m[y]?m[y].p(w,b):(m[y]=ko(w),m[y].c(),m[y].m(c,null))}for(;y<m.length;y+=1)m[y].d(1);m.length=g.length}},i(p){u||(C(_),u=!0)},o(p){I(_),u=!1},d(p){p&&k(e),_&&_.d(),_s(m,p),f=!1,mi(d)}}}function Sg(s,e,t){let i,{color:r}=e,{current_mode:n="hex"}=e;const o=Nt(),a=[["Hex","hex"],["RGB","rgb"],["HSL","hsl"]];function l(_,g){return g==="hex"?ft(_).toHexString():g==="rgb"?ft(_).toRgbString():ft(_).toHslString()}function h(){new EyeDropper().open().then(g=>{t(0,r=g.sRGBHex)})}const c=!!window.EyeDropper;function u(){o("selected",i),o("close")}const f=_=>t(0,r=_.currentTarget.value),d=_=>t(1,n=_);return s.$$set=_=>{"color"in _&&t(0,r=_.color),"current_mode"in _&&t(1,n=_.current_mode)},s.$$.update=()=>{s.$$.dirty&3&&t(2,i=l(r,n)),s.$$.dirty&4&&i&&o("selected",i)},[r,n,i,a,h,c,u,f,d]}class Cg extends ke{constructor(e){super(),Ae(this,e,Sg,Ag,Se,{color:0,current_mode:1})}}function Mg(s){let e,t,i,r,n,o,a,l;return i=new hc({}),{c(){e=Y("div"),t=Y("span"),ee(i.$$.fragment),r=Z(),n=Y("input"),this.h()},l(h){e=j(h,"DIV",{class:!0});var c=L(e);t=j(c,"SPAN",{class:!0});var u=L(t);te(i.$$.fragment,u),u.forEach(k),r=Q(c),n=j(c,"INPUT",{type:!0,min:!0,max:!0,step:!0,class:!0}),c.forEach(k),this.h()},h(){x(t,"class","svelte-1ktybyw"),x(n,"type","range"),x(n,"min",s[1]),x(n,"max",s[2]),x(n,"step",1),x(n,"class","svelte-1ktybyw"),x(e,"class","wrap svelte-1ktybyw")},m(h,c){D(h,e,c),O(e,t),ie(i,t,null),O(e,r),O(e,n),nn(n,s[0]),o=!0,a||(l=[Fe(n,"change",s[4]),Fe(n,"input",s[4]),gs(Wr.call(null,e,s[5]))],a=!0)},p(h,[c]){(!o||c&2)&&x(n,"min",h[1]),(!o||c&4)&&x(n,"max",h[2]),c&1&&nn(n,h[0])},i(h){o||(C(i.$$.fragment,h),o=!0)},o(h){I(i.$$.fragment,h),o=!1},d(h){h&&k(e),se(i),a=!1,mi(l)}}}function Tg(s,e,t){let{selected_size:i}=e,{min:r}=e,{max:n}=e;const o=Nt();function a(){i=Rh(this.value),t(0,i)}const l=()=>o("click_outside");return s.$$set=h=>{"selected_size"in h&&t(0,i=h.selected_size),"min"in h&&t(1,r=h.min),"max"in h&&t(2,n=h.max)},[i,r,n,o,a,l]}class Pg extends ke{constructor(e){super(),Ae(this,e,Tg,Mg,Se,{selected_size:0,min:1,max:2})}}function Ao(s){let e,t,i=s[9]&&So(s);return{c(){i&&i.c(),e=Oe()},l(r){i&&i.l(r),e=Oe()},m(r,n){i&&i.m(r,n),D(r,e,n),t=!0},p(r,n){r[9]?i?(i.p(r,n),n[0]&512&&C(i,1)):(i=So(r),i.c(),C(i,1),i.m(e.parentNode,e)):i&&(he(),I(i,1,1,()=>{i=null}),ce())},i(r){t||(C(i),t=!0)},o(r){I(i),t=!1},d(r){r&&k(e),i&&i.d(r)}}}function So(s){let e,t,i,r,n,o,a;function l(d){s[19](d)}function h(d){s[20](d)}let c={};s[0]!==void 0&&(c.color=s[0]),s[3]!==void 0&&(c.opacity=s[3]),e=new yg({props:c}),ye.push(()=>Ce(e,"color",l)),ye.push(()=>Ce(e,"opacity",h));function u(d){s[21](d)}let f={color:s[0]};return s[10]!==void 0&&(f.current_mode=s[10]),n=new Cg({props:f}),ye.push(()=>Ce(n,"current_mode",u)),n.$on("close",s[22]),n.$on("selected",s[23]),{c(){ee(e.$$.fragment),r=Z(),ee(n.$$.fragment)},l(d){te(e.$$.fragment,d),r=Q(d),te(n.$$.fragment,d)},m(d,_){ie(e,d,_),D(d,r,_),ie(n,d,_),a=!0},p(d,_){const g={};!t&&_[0]&1&&(t=!0,g.color=d[0],Me(()=>t=!1)),!i&&_[0]&8&&(i=!0,g.opacity=d[3],Me(()=>i=!1)),e.$set(g);const m={};_[0]&1&&(m.color=d[0]),!o&&_[0]&1024&&(o=!0,m.current_mode=d[10],Me(()=>o=!1)),n.$set(m)},i(d){a||(C(e.$$.fragment,d),C(n.$$.fragment,d),a=!0)},o(d){I(e.$$.fragment,d),I(n.$$.fragment,d),a=!1},d(d){d&&k(r),se(e,d),se(n,d)}}}function Co(s){let e,t,i;function r(o){s[24](o)}let n={colors:s[4],user_colors:s[5]==="defaults"?s[2]:null,selected_color:s[0],current_mode:s[10]};return s[9]!==void 0&&(n.color_picker=s[9]),e=new vg({props:n}),ye.push(()=>Ce(e,"color_picker",r)),e.$on("select",s[25]),e.$on("edit",s[26]),e.$on("add_color",s[16]),{c(){ee(e.$$.fragment)},l(o){te(e.$$.fragment,o)},m(o,a){ie(e,o,a),i=!0},p(o,a){const l={};a[0]&16&&(l.colors=o[4]),a[0]&36&&(l.user_colors=o[5]==="defaults"?o[2]:null),a[0]&1&&(l.selected_color=o[0]),a[0]&1024&&(l.current_mode=o[10]),!t&&a[0]&512&&(t=!0,l.color_picker=o[9],Me(()=>t=!1)),e.$set(l)},i(o){i||(C(e.$$.fragment,o),i=!0)},o(o){I(e.$$.fragment,o),i=!1},d(o){se(e,o)}}}function Mo(s){let e,t,i;function r(o){s[27](o)}let n={max:100,min:1};return s[1]!==void 0&&(n.selected_size=s[1]),e=new Pg({props:n}),ye.push(()=>Ce(e,"selected_size",r)),{c(){ee(e.$$.fragment)},l(o){te(e.$$.fragment,o)},m(o,a){ie(e,o,a),i=!0},p(o,a){const l={};!t&&a[0]&2&&(t=!0,l.selected_size=o[1],Me(()=>t=!1)),e.$set(l)},i(o){i||(C(e.$$.fragment,o),i=!0)},o(o){I(e.$$.fragment,o),i=!1},d(o){se(e,o)}}}function Ig(s){let e,t,i,r,n,o;Bh(s[18]);let a=s[5]==="defaults"&&Ao(s),l=s[6]&&Co(s),h=s[7]&&Mo(s);return{c(){e=Y("div"),a&&a.c(),t=Z(),l&&l.c(),i=Z(),h&&h.c(),this.h()},l(c){e=j(c,"DIV",{class:!0});var u=L(e);a&&a.l(u),t=Q(u),l&&l.l(u),i=Q(u),h&&h.l(u),u.forEach(k),this.h()},h(){x(e,"class","wrap svelte-qigu2l"),oe(e,"padded",!s[9]),oe(e,"color_picker",s[9]),oe(e,"size_picker",s[7]&&s[8]==="brush"),oe(e,"eraser_picker",s[8]==="eraser")},m(c,u){D(c,e,u),a&&a.m(e,null),O(e,t),l&&l.m(e,null),O(e,i),h&&h.m(e,null),r=!0,n||(o=[Fe(window,"resize",s[18]),gs(Wr.call(null,e,s[28]))],n=!0)},p(c,u){c[5]==="defaults"?a?(a.p(c,u),u[0]&32&&C(a,1)):(a=Ao(c),a.c(),C(a,1),a.m(e,t)):a&&(he(),I(a,1,1,()=>{a=null}),ce()),c[6]?l?(l.p(c,u),u[0]&64&&C(l,1)):(l=Co(c),l.c(),C(l,1),l.m(e,i)):l&&(he(),I(l,1,1,()=>{l=null}),ce()),c[7]?h?(h.p(c,u),u[0]&128&&C(h,1)):(h=Mo(c),h.c(),C(h,1),h.m(e,null)):h&&(he(),I(h,1,1,()=>{h=null}),ce()),(!r||u[0]&512)&&oe(e,"padded",!c[9]),(!r||u[0]&512)&&oe(e,"color_picker",c[9]),(!r||u[0]&384)&&oe(e,"size_picker",c[7]&&c[8]==="brush"),(!r||u[0]&256)&&oe(e,"eraser_picker",c[8]==="eraser")},i(c){r||(C(a),C(l),C(h),r=!0)},o(c){I(a),I(l),I(h),r=!1},d(c){c&&k(e),a&&a.d(),l&&l.d(),h&&h.d(),n=!1,mi(o)}}}function Eg(s,e){let t;return function(...i){clearTimeout(t),t=setTimeout(()=>s(...i),e)}}function zg(s,e,t){let{colors:i}=e,{selected_color:r}=e,{color_mode:n=void 0}=e,{recent_colors:o=[]}=e,{selected_size:a}=e,{selected_opacity:l}=e,{show_swatch:h}=e,{show_size:c}=e,{mode:u="brush"}=e,f=!1,d="hex",_=null;const g=Nt();function m({index:N,color:Ge,opacity:X},$e){$e==="user"&&!Ge&&(_=N,t(9,f=!0)),Ge&&(t(0,r=Ge),X!==void 0&&t(3,l=X),$e==="core"&&t(9,f=!1))}function p(N){_!==null&&t(2,o[_]=N,o)}let b=0,y=0,{preview:w=!1}=e;function A(){w||t(17,w=!0),P()}function T(){t(17,w=!1)}const P=Eg(T,1e3);function U(N){t(0,r=N)}function R(){o.length>=5&&o.pop(),!o.some(N=>Array.isArray(N)?N[0]===r&&N[1]===l:N===r)&&(o.push([r,l]),t(2,o))}function E(){t(12,y=window.innerHeight),t(11,b=window.innerWidth)}function M(N){r=N,t(0,r)}function z(N){l=N,t(3,l)}function V(N){d=N,t(10,d)}const W=()=>t(9,f=!1),ae=({detail:N})=>U(N);function H(N){f=N,t(9,f)}const q=({detail:N})=>m(N,"core"),Ke=({detail:N})=>m(N,"user");function le(N){a=N,t(1,a)}const Te=()=>g("click_outside");return s.$$set=N=>{"colors"in N&&t(4,i=N.colors),"selected_color"in N&&t(0,r=N.selected_color),"color_mode"in N&&t(5,n=N.color_mode),"recent_colors"in N&&t(2,o=N.recent_colors),"selected_size"in N&&t(1,a=N.selected_size),"selected_opacity"in N&&t(3,l=N.selected_opacity),"show_swatch"in N&&t(6,h=N.show_swatch),"show_size"in N&&t(7,c=N.show_size),"mode"in N&&t(8,u=N.mode),"preview"in N&&t(17,w=N.preview)},s.$$.update=()=>{s.$$.dirty[0]&1&&p(r),s.$$.dirty[0]&3&&A()},[r,a,o,l,i,n,h,c,u,f,d,b,y,g,m,U,R,w,E,M,z,V,W,ae,H,q,Ke,le,Te]}class Jl extends ke{constructor(e){super(),Ae(this,e,zg,Ig,Se,{colors:4,selected_color:0,color_mode:5,recent_colors:2,selected_size:1,selected_opacity:3,show_swatch:6,show_size:7,mode:8,preview:17},null,[-1,-1])}}function To(s){let e,t;return e=new gt({props:{Icon:Aa,label:"Image",highlight:s[8]==="image",size:"medium",padded:!1,transparent:!0}}),e.$on("click",s[23]),{c(){ee(e.$$.fragment)},l(i){te(e.$$.fragment,i)},m(i,r){ie(e,i,r),t=!0},p(i,r){const n={};r[0]&256&&(n.highlight=i[8]==="image"),e.$set(n)},i(i){t||(C(e.$$.fragment,i),t=!0)},o(i){I(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function Po(s){let e,t;return e=new gt({props:{Icon:ac,label:"Brush",highlight:s[8]==="draw",size:"medium",padded:!1,transparent:!0}}),e.$on("click",s[24]),{c(){ee(e.$$.fragment)},l(i){te(e.$$.fragment,i)},m(i,r){ie(e,i,r),t=!0},p(i,r){const n={};r[0]&256&&(n.highlight=i[8]==="draw"),e.$set(n)},i(i){t||(C(e.$$.fragment,i),t=!0)},o(i){I(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function Io(s){let e,t;return e=new gt({props:{Icon:gc,label:"Erase",highlight:s[8]==="erase",size:"medium",padded:!1,transparent:!0}}),e.$on("click",s[25]),{c(){ee(e.$$.fragment)},l(i){te(e.$$.fragment,i)},m(i,r){ie(e,i,r),t=!0},p(i,r){const n={};r[0]&256&&(n.highlight=i[8]==="erase"),e.$set(n)},i(i){t||(C(e.$$.fragment,i),t=!0)},o(i){I(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function Eo(s){let e,t,i,r;const n=[Bg,Rg],o=[];function a(l,h){return l[10]?0:1}return e=a(s),t=o[e]=n[e](s),{c(){t.c(),i=Oe()},l(l){t.l(l),i=Oe()},m(l,h){o[e].m(l,h),D(l,i,h),r=!0},p(l,h){let c=e;e=a(l),e===c?o[e].p(l,h):(he(),I(o[c],1,1,()=>{o[c]=null}),ce(),t=o[e],t?t.p(l,h):(t=o[e]=n[e](l),t.c()),C(t,1),t.m(i.parentNode,i))},i(l){r||(C(t),r=!0)},o(l){I(t),r=!1},d(l){l&&k(i),o[e].d(l)}}}function Rg(s){let e,t,i,r,n=s[17]&&zo(s),o=s[15]&&Ro(s),a=s[16]&&Bo(s);return{c(){n&&n.c(),e=Z(),o&&o.c(),t=Z(),a&&a.c(),i=Oe()},l(l){n&&n.l(l),e=Q(l),o&&o.l(l),t=Q(l),a&&a.l(l),i=Oe()},m(l,h){n&&n.m(l,h),D(l,e,h),o&&o.m(l,h),D(l,t,h),a&&a.m(l,h),D(l,i,h),r=!0},p(l,h){l[17]?n?(n.p(l,h),h[0]&131072&&C(n,1)):(n=zo(l),n.c(),C(n,1),n.m(e.parentNode,e)):n&&(he(),I(n,1,1,()=>{n=null}),ce()),l[15]?o?(o.p(l,h),h[0]&32768&&C(o,1)):(o=Ro(l),o.c(),C(o,1),o.m(t.parentNode,t)):o&&(he(),I(o,1,1,()=>{o=null}),ce()),l[16]?a?(a.p(l,h),h[0]&65536&&C(a,1)):(a=Bo(l),a.c(),C(a,1),a.m(i.parentNode,i)):a&&(he(),I(a,1,1,()=>{a=null}),ce())},i(l){r||(C(n),C(o),C(a),r=!0)},o(l){I(n),I(o),I(a),r=!1},d(l){l&&(k(e),k(t),k(i)),n&&n.d(l),o&&o.d(l),a&&a.d(l)}}}function Bg(s){let e,t,i,r=s[19]&&Fo(s),n=s[18]&&Lo(s);return{c(){r&&r.c(),e=Z(),n&&n.c(),t=Oe()},l(o){r&&r.l(o),e=Q(o),n&&n.l(o),t=Oe()},m(o,a){r&&r.m(o,a),D(o,e,a),n&&n.m(o,a),D(o,t,a),i=!0},p(o,a){o[19]?r?(r.p(o,a),a[0]&524288&&C(r,1)):(r=Fo(o),r.c(),C(r,1),r.m(e.parentNode,e)):r&&(he(),I(r,1,1,()=>{r=null}),ce()),o[18]?n?(n.p(o,a),a[0]&262144&&C(n,1)):(n=Lo(o),n.c(),C(n,1),n.m(t.parentNode,t)):n&&(he(),I(n,1,1,()=>{n=null}),ce())},i(o){i||(C(r),C(n),i=!0)},o(o){I(r),I(n),i=!1},d(o){o&&(k(e),k(t)),r&&r.d(o),n&&n.d(o)}}}function zo(s){let e,t;return e=new gt({props:{Icon:Yh,label:"Upload",highlight:s[9]==="upload",size:"medium",padded:!1,transparent:!0,offset:0}}),e.$on("click",s[28]),{c(){ee(e.$$.fragment)},l(i){te(e.$$.fragment,i)},m(i,r){ie(e,i,r),t=!0},p(i,r){const n={};r[0]&512&&(n.highlight=i[9]==="upload"),e.$set(n)},i(i){t||(C(e.$$.fragment,i),t=!0)},o(i){I(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function Ro(s){let e,t;return e=new gt({props:{Icon:jh,label:"Paste",highlight:s[9]==="paste",size:"large",padded:!1,transparent:!0,offset:0}}),e.$on("click",s[29]),{c(){ee(e.$$.fragment)},l(i){te(e.$$.fragment,i)},m(i,r){ie(e,i,r),t=!0},p(i,r){const n={};r[0]&512&&(n.highlight=i[9]==="paste"),e.$set(n)},i(i){t||(C(e.$$.fragment,i),t=!0)},o(i){I(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function Bo(s){let e,t;return e=new gt({props:{Icon:Kh,label:"Webcam",highlight:s[9]==="webcam",size:"medium",padded:!1,transparent:!0,offset:0}}),e.$on("click",s[30]),{c(){ee(e.$$.fragment)},l(i){te(e.$$.fragment,i)},m(i,r){ie(e,i,r),t=!0},p(i,r){const n={};r[0]&512&&(n.highlight=i[9]==="webcam"),e.$set(n)},i(i){t||(C(e.$$.fragment,i),t=!0)},o(i){I(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function Fo(s){let e,t;return e=new gt({props:{Icon:fc,label:"Crop",highlight:s[9]==="crop",size:"medium",padded:!1,transparent:!0,offset:0}}),e.$on("click",s[26]),{c(){ee(e.$$.fragment)},l(i){te(e.$$.fragment,i)},m(i,r){ie(e,i,r),t=!0},p(i,r){const n={};r[0]&512&&(n.highlight=i[9]==="crop"),e.$set(n)},i(i){t||(C(e.$$.fragment,i),t=!0)},o(i){I(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function Lo(s){let e,t;return e=new gt({props:{Icon:Tc,label:"Resize",highlight:s[9]==="size",size:"medium",padded:!1,transparent:!0,offset:0}}),e.$on("click",s[27]),{c(){ee(e.$$.fragment)},l(i){te(e.$$.fragment,i)},m(i,r){ie(e,i,r),t=!0},p(i,r){const n={};r[0]&512&&(n.highlight=i[9]==="size"),e.$set(n)},i(i){t||(C(e.$$.fragment,i),t=!0)},o(i){I(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function Oo(s){let e,t,i,r,n,o;e=new gt({props:{Icon:uc,label:"Color",color:s[2],size:"medium",padded:!1,transparent:!0,offset:0}}),e.$on("click",s[31]),i=new gt({props:{Icon:ka,label:"Brush Size",highlight:s[9]==="size",size:"medium",padded:!1,transparent:!0,offset:0}}),i.$on("click",s[32]);let a=(s[5]||s[6])&&Go(s);return{c(){ee(e.$$.fragment),t=Z(),ee(i.$$.fragment),r=Z(),a&&a.c(),n=Oe()},l(l){te(e.$$.fragment,l),t=Q(l),te(i.$$.fragment,l),r=Q(l),a&&a.l(l),n=Oe()},m(l,h){ie(e,l,h),D(l,t,h),ie(i,l,h),D(l,r,h),a&&a.m(l,h),D(l,n,h),o=!0},p(l,h){const c={};h[0]&4&&(c.color=l[2]),e.$set(c);const u={};h[0]&512&&(u.highlight=l[9]==="size"),i.$set(u),l[5]||l[6]?a?(a.p(l,h),h[0]&96&&C(a,1)):(a=Go(l),a.c(),C(a,1),a.m(n.parentNode,n)):a&&(he(),I(a,1,1,()=>{a=null}),ce())},i(l){o||(C(e.$$.fragment,l),C(i.$$.fragment,l),C(a),o=!0)},o(l){I(e.$$.fragment,l),I(i.$$.fragment,l),I(a),o=!1},d(l){l&&(k(t),k(r),k(n)),se(e,l),se(i,l),a&&a.d(l)}}}function Go(s){let e,t,i,r,n,o;function a(f){s[33](f)}function l(f){s[34](f)}function h(f){s[35](f)}function c(f){s[36](f)}let u={colors:s[11].colors,color_mode:s[11].color_mode,recent_colors:s[20],show_swatch:s[5],show_size:s[6],mode:"brush"};return s[0]!==void 0&&(u.selected_size=s[0]),s[2]!==void 0&&(u.selected_color=s[2]),s[3]!==void 0&&(u.selected_opacity=s[3]),s[4]!==void 0&&(u.preview=s[4]),e=new Jl({props:u}),ye.push(()=>Ce(e,"selected_size",a)),ye.push(()=>Ce(e,"selected_color",l)),ye.push(()=>Ce(e,"selected_opacity",h)),ye.push(()=>Ce(e,"preview",c)),e.$on("click_outside",s[37]),{c(){ee(e.$$.fragment)},l(f){te(e.$$.fragment,f)},m(f,d){ie(e,f,d),o=!0},p(f,d){const _={};d[0]&2048&&(_.colors=f[11].colors),d[0]&2048&&(_.color_mode=f[11].color_mode),d[0]&32&&(_.show_swatch=f[5]),d[0]&64&&(_.show_size=f[6]),!t&&d[0]&1&&(t=!0,_.selected_size=f[0],Me(()=>t=!1)),!i&&d[0]&4&&(i=!0,_.selected_color=f[2],Me(()=>i=!1)),!r&&d[0]&8&&(r=!0,_.selected_opacity=f[3],Me(()=>r=!1)),!n&&d[0]&16&&(n=!0,_.preview=f[4],Me(()=>n=!1)),e.$set(_)},i(f){o||(C(e.$$.fragment,f),o=!0)},o(f){I(e.$$.fragment,f),o=!1},d(f){se(e,f)}}}function Do(s){let e,t;return e=new gt({props:{Icon:ka,label:"Eraser Size",highlight:s[9]==="size",size:"medium",padded:!1,transparent:!0,offset:0}}),e.$on("click",s[38]),{c(){ee(e.$$.fragment)},l(i){te(e.$$.fragment,i)},m(i,r){ie(e,i,r),t=!0},p(i,r){const n={};r[0]&512&&(n.highlight=i[9]==="size"),e.$set(n)},i(i){t||(C(e.$$.fragment,i),t=!0)},o(i){I(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function No(s){let e,t,i,r,n,o;function a(f){s[39](f)}function l(f){s[40](f)}function h(f){s[41](f)}function c(f){s[42](f)}let u={colors:[],show_swatch:!1,show_size:!0,mode:"eraser"};return s[1]!==void 0&&(u.selected_size=s[1]),s[2]!==void 0&&(u.selected_color=s[2]),s[3]!==void 0&&(u.selected_opacity=s[3]),s[4]!==void 0&&(u.preview=s[4]),e=new Jl({props:u}),ye.push(()=>Ce(e,"selected_size",a)),ye.push(()=>Ce(e,"selected_color",l)),ye.push(()=>Ce(e,"selected_opacity",h)),ye.push(()=>Ce(e,"preview",c)),e.$on("click_outside",s[43]),{c(){ee(e.$$.fragment)},l(f){te(e.$$.fragment,f)},m(f,d){ie(e,f,d),o=!0},p(f,d){const _={};!t&&d[0]&2&&(t=!0,_.selected_size=f[1],Me(()=>t=!1)),!i&&d[0]&4&&(i=!0,_.selected_color=f[2],Me(()=>i=!1)),!r&&d[0]&8&&(r=!0,_.selected_opacity=f[3],Me(()=>r=!1)),!n&&d[0]&16&&(n=!0,_.preview=f[4],Me(()=>n=!1)),e.$set(_)},i(f){o||(C(e.$$.fragment,f),o=!0)},o(f){I(e.$$.fragment,f),o=!1},d(f){se(e,f)}}}function Fg(s){let e,t,i,r,n,o,a,l,h,c,u=s[13].length>0&&To(s),f=s[11]&&Po(s),d=s[12]&&Io(s),_=s[8]==="image"&&Eo(s),g=s[8]==="draw"&&s[11]&&Oo(s),m=s[8]==="erase"&&s[12]&&Do(s),p=s[7]&&No(s);return{c(){e=Y("div"),t=Y("div"),u&&u.c(),i=Z(),f&&f.c(),r=Z(),d&&d.c(),n=Z(),o=Y("div"),_&&_.c(),a=Z(),g&&g.c(),l=Z(),m&&m.c(),h=Z(),p&&p.c(),this.h()},l(b){e=j(b,"DIV",{class:!0});var y=L(e);t=j(y,"DIV",{class:!0});var w=L(t);u&&u.l(w),i=Q(w),f&&f.l(w),r=Q(w),d&&d.l(w),w.forEach(k),n=Q(y),o=j(y,"DIV",{class:!0});var A=L(o);_&&_.l(A),a=Q(A),g&&g.l(A),l=Q(A),m&&m.l(A),h=Q(A),p&&p.l(A),A.forEach(k),y.forEach(k),this.h()},h(){x(t,"class","half-container svelte-1m5h684"),x(o,"class","half-container right svelte-1m5h684"),oe(o,"hide",s[8]==="pan"||s[8]==="image"&&!s[10]&&s[13].length===0||s[8]==="image"&&s[10]&&s[14].length===0),x(e,"class","toolbar-wrap svelte-1m5h684")},m(b,y){D(b,e,y),O(e,t),u&&u.m(t,null),O(t,i),f&&f.m(t,null),O(t,r),d&&d.m(t,null),O(e,n),O(e,o),_&&_.m(o,null),O(o,a),g&&g.m(o,null),O(o,l),m&&m.m(o,null),O(o,h),p&&p.m(o,null),c=!0},p(b,y){b[13].length>0?u?(u.p(b,y),y[0]&8192&&C(u,1)):(u=To(b),u.c(),C(u,1),u.m(t,i)):u&&(he(),I(u,1,1,()=>{u=null}),ce()),b[11]?f?(f.p(b,y),y[0]&2048&&C(f,1)):(f=Po(b),f.c(),C(f,1),f.m(t,r)):f&&(he(),I(f,1,1,()=>{f=null}),ce()),b[12]?d?(d.p(b,y),y[0]&4096&&C(d,1)):(d=Io(b),d.c(),C(d,1),d.m(t,null)):d&&(he(),I(d,1,1,()=>{d=null}),ce()),b[8]==="image"?_?(_.p(b,y),y[0]&256&&C(_,1)):(_=Eo(b),_.c(),C(_,1),_.m(o,a)):_&&(he(),I(_,1,1,()=>{_=null}),ce()),b[8]==="draw"&&b[11]?g?(g.p(b,y),y[0]&2304&&C(g,1)):(g=Oo(b),g.c(),C(g,1),g.m(o,l)):g&&(he(),I(g,1,1,()=>{g=null}),ce()),b[8]==="erase"&&b[12]?m?(m.p(b,y),y[0]&4352&&C(m,1)):(m=Do(b),m.c(),C(m,1),m.m(o,h)):m&&(he(),I(m,1,1,()=>{m=null}),ce()),b[7]?p?(p.p(b,y),y[0]&128&&C(p,1)):(p=No(b),p.c(),C(p,1),p.m(o,null)):p&&(he(),I(p,1,1,()=>{p=null}),ce()),(!c||y[0]&25856)&&oe(o,"hide",b[8]==="pan"||b[8]==="image"&&!b[10]&&b[13].length===0||b[8]==="image"&&b[10]&&b[14].length===0)},i(b){c||(C(u),C(f),C(d),C(_),C(g),C(m),C(p),c=!0)},o(b){I(u),I(f),I(d),I(_),I(g),I(m),I(p),c=!1},d(b){b&&k(e),u&&u.d(),f&&f.d(),d&&d.d(),_&&_.d(),g&&g.d(),m&&m.d(),p&&p.d()}}}function Lg(s,e,t){let i,r,n,o,a,{tool:l="image"}=e,{subtool:h=null}=e,{background:c=!1}=e,{brush_options:u}=e,{selected_size:f=u&&typeof u.default_size=="number"?u.default_size:25}=e,{eraser_options:d}=e,{selected_eraser_size:_=d&&typeof d.default_size=="number"?d.default_size:25}=e,{selected_color:g=u&&(()=>{const F=u.default_color;return Array.isArray(F)?F[0]:F})()}=e,{selected_opacity:m=u&&(()=>{const F=u.default_color;if(Array.isArray(F))return F[1];const Ie=ft(F);return Ie.getAlpha()<1?Ie.getAlpha():1})()}=e,{preview:p=!1}=e,{show_brush_color:b=!1}=e,{show_brush_size:y=!1}=e,{show_eraser_size:w=!1}=e,{sources:A}=e,{transforms:T}=e,P=[];const U=Nt();function R(F,Ie){F.stopPropagation(),U("tool_change",{tool:Ie})}function E(F,Ie){F.stopPropagation(),U("subtool_change",{tool:l,subtool:Ie})}const M=F=>R(F,"image"),z=F=>R(F,"draw"),V=F=>R(F,"erase"),W=F=>E(F,"crop"),ae=F=>E(F,"size"),H=F=>E(F,"upload"),q=F=>E(F,"paste"),Ke=F=>E(F,"webcam"),le=F=>E(F,"color"),Te=F=>E(F,"size");function N(F){f=F,t(0,f)}function Ge(F){g=F,t(2,g)}function X(F){m=F,t(3,m)}function $e(F){p=F,t(4,p)}const Xe=F=>{F.stopPropagation(),t(4,p=!1),t(5,b=!1),t(6,y=!1),E(F,null)},De=F=>E(F,"size");function mt(F){_=F,t(1,_)}function ge(F){g=F,t(2,g)}function Qe(F){m=F,t(3,m)}function We(F){p=F,t(4,p)}const Ye=F=>{F.stopPropagation(),t(4,p=!1),t(7,w=!1),E(F,null)};return s.$$set=F=>{"tool"in F&&t(8,l=F.tool),"subtool"in F&&t(9,h=F.subtool),"background"in F&&t(10,c=F.background),"brush_options"in F&&t(11,u=F.brush_options),"selected_size"in F&&t(0,f=F.selected_size),"eraser_options"in F&&t(12,d=F.eraser_options),"selected_eraser_size"in F&&t(1,_=F.selected_eraser_size),"selected_color"in F&&t(2,g=F.selected_color),"selected_opacity"in F&&t(3,m=F.selected_opacity),"preview"in F&&t(4,p=F.preview),"show_brush_color"in F&&t(5,b=F.show_brush_color),"show_brush_size"in F&&t(6,y=F.show_brush_size),"show_eraser_size"in F&&t(7,w=F.show_eraser_size),"sources"in F&&t(13,A=F.sources),"transforms"in F&&t(14,T=F.transforms)},s.$$.update=()=>{s.$$.dirty[0]&768&&t(6,y=l==="draw"&&h==="size"),s.$$.dirty[0]&768&&t(5,b=l==="draw"&&h==="color"),s.$$.dirty[0]&768&&t(7,w=l==="erase"&&h==="size"),s.$$.dirty[0]&16384&&t(19,i=T.includes("crop")),s.$$.dirty[0]&16384&&t(18,r=T.includes("resize")),s.$$.dirty[0]&8192&&t(17,n=A.includes("upload")),s.$$.dirty[0]&8192&&t(16,o=A.includes("webcam")),s.$$.dirty[0]&8192&&t(15,a=A.includes("clipboard"))},[f,_,g,m,p,b,y,w,l,h,c,u,d,A,T,a,o,n,r,i,P,R,E,M,z,V,W,ae,H,q,Ke,le,Te,N,Ge,X,$e,Xe,De,mt,ge,Qe,We,Ye]}class Og extends ke{constructor(e){super(),Ae(this,e,Lg,Fg,Se,{tool:8,subtool:9,background:10,brush_options:11,selected_size:0,eraser_options:12,selected_eraser_size:1,selected_color:2,selected_opacity:3,preview:4,show_brush_color:5,show_brush_size:6,show_eraser_size:7,sources:13,transforms:14},null,[-1,-1])}}var Or=`in vec2 aPosition;
out vec2 vTextureCoord;

uniform vec4 uInputSize;
uniform vec4 uOutputFrame;
uniform vec4 uOutputTexture;

vec4 filterVertexPosition( void )
{
    vec2 position = aPosition * uOutputFrame.zw + uOutputFrame.xy;
    
    position.x = position.x * (2.0 / uOutputTexture.x) - 1.0;
    position.y = position.y * (2.0*uOutputTexture.z / uOutputTexture.y) - uOutputTexture.z;

    return vec4(position, 0.0, 1.0);
}

vec2 filterTextureCoord( void )
{
    return aPosition * (uOutputFrame.zw * uInputSize.zw);
}

void main(void)
{
    gl_Position = filterVertexPosition();
    vTextureCoord = filterTextureCoord();
}
`,Gr=`struct GlobalFilterUniforms {
  uInputSize:vec4<f32>,
  uInputPixel:vec4<f32>,
  uInputClamp:vec4<f32>,
  uOutputFrame:vec4<f32>,
  uGlobalFrame:vec4<f32>,
  uOutputTexture:vec4<f32>,
};

@group(0) @binding(0) var<uniform> gfu: GlobalFilterUniforms;

struct VSOutput {
    @builtin(position) position: vec4<f32>,
    @location(0) uv : vec2<f32>
  };

fn filterVertexPosition(aPosition:vec2<f32>) -> vec4<f32>
{
    var position = aPosition * gfu.uOutputFrame.zw + gfu.uOutputFrame.xy;

    position.x = position.x * (2.0 / gfu.uOutputTexture.x) - 1.0;
    position.y = position.y * (2.0*gfu.uOutputTexture.z / gfu.uOutputTexture.y) - gfu.uOutputTexture.z;

    return vec4(position, 0.0, 1.0);
}

fn filterTextureCoord( aPosition:vec2<f32> ) -> vec2<f32>
{
    return aPosition * (gfu.uOutputFrame.zw * gfu.uInputSize.zw);
}

fn globalTextureCoord( aPosition:vec2<f32> ) -> vec2<f32>
{
  return  (aPosition.xy / gfu.uGlobalFrame.zw) + (gfu.uGlobalFrame.xy / gfu.uGlobalFrame.zw);  
}

fn getSize() -> vec2<f32>
{
  return gfu.uGlobalFrame.zw;
}
  
@vertex
fn mainVertex(
  @location(0) aPosition : vec2<f32>, 
) -> VSOutput {
  return VSOutput(
   filterVertexPosition(aPosition),
   filterTextureCoord(aPosition)
  );
}`,Gg=`
in vec2 vTextureCoord;
out vec4 finalColor;

uniform sampler2D uTexture;
uniform vec2 uOffset;

void main(void)
{
    vec4 color = vec4(0.0);

    // Sample top left pixel
    color += texture(uTexture, vec2(vTextureCoord.x - uOffset.x, vTextureCoord.y + uOffset.y));

    // Sample top right pixel
    color += texture(uTexture, vec2(vTextureCoord.x + uOffset.x, vTextureCoord.y + uOffset.y));

    // Sample bottom right pixel
    color += texture(uTexture, vec2(vTextureCoord.x + uOffset.x, vTextureCoord.y - uOffset.y));

    // Sample bottom left pixel
    color += texture(uTexture, vec2(vTextureCoord.x - uOffset.x, vTextureCoord.y - uOffset.y));

    // Average
    color *= 0.25;

    finalColor = color;
}`,Dg=`struct KawaseBlurUniforms {
  uOffset:vec2<f32>,
};

@group(0) @binding(1) var uTexture: texture_2d<f32>; 
@group(0) @binding(2) var uSampler: sampler;
@group(1) @binding(0) var<uniform> kawaseBlurUniforms : KawaseBlurUniforms;

@fragment
fn mainFragment(
  @builtin(position) position: vec4<f32>,
  @location(0) uv : vec2<f32>
) -> @location(0) vec4<f32> {
  let uOffset = kawaseBlurUniforms.uOffset;
  var color: vec4<f32> = vec4<f32>(0.0);

  // Sample top left pixel
  color += textureSample(uTexture, uSampler, vec2<f32>(uv.x - uOffset.x, uv.y + uOffset.y));
  // Sample top right pixel
  color += textureSample(uTexture, uSampler, vec2<f32>(uv.x + uOffset.x, uv.y + uOffset.y));
  // Sample bottom right pixel
  color += textureSample(uTexture, uSampler, vec2<f32>(uv.x + uOffset.x, uv.y - uOffset.y));
  // Sample bottom left pixel
  color += textureSample(uTexture, uSampler, vec2<f32>(uv.x - uOffset.x, uv.y - uOffset.y));
  // Average
  color *= 0.25;

  return color;
}`,Ng=`
precision highp float;
in vec2 vTextureCoord;
out vec4 finalColor;

uniform sampler2D uTexture;
uniform vec2 uOffset;

uniform vec4 uInputClamp;

void main(void)
{
    vec4 color = vec4(0.0);

    // Sample top left pixel
    color += texture(uTexture, clamp(vec2(vTextureCoord.x - uOffset.x, vTextureCoord.y + uOffset.y), uInputClamp.xy, uInputClamp.zw));

    // Sample top right pixel
    color += texture(uTexture, clamp(vec2(vTextureCoord.x + uOffset.x, vTextureCoord.y + uOffset.y), uInputClamp.xy, uInputClamp.zw));

    // Sample bottom right pixel
    color += texture(uTexture, clamp(vec2(vTextureCoord.x + uOffset.x, vTextureCoord.y - uOffset.y), uInputClamp.xy, uInputClamp.zw));

    // Sample bottom left pixel
    color += texture(uTexture, clamp(vec2(vTextureCoord.x - uOffset.x, vTextureCoord.y - uOffset.y), uInputClamp.xy, uInputClamp.zw));

    // Average
    color *= 0.25;

    finalColor = color;
}
`,Ug=`struct KawaseBlurUniforms {
  uOffset:vec2<f32>,
};

struct GlobalFilterUniforms {
  uInputSize:vec4<f32>,
  uInputPixel:vec4<f32>,
  uInputClamp:vec4<f32>,
  uOutputFrame:vec4<f32>,
  uGlobalFrame:vec4<f32>,
  uOutputTexture:vec4<f32>,
};

@group(0) @binding(0) var<uniform> gfu: GlobalFilterUniforms;

@group(0) @binding(1) var uTexture: texture_2d<f32>; 
@group(0) @binding(2) var uSampler: sampler;
@group(1) @binding(0) var<uniform> kawaseBlurUniforms : KawaseBlurUniforms;

@fragment
fn mainFragment(
  @builtin(position) position: vec4<f32>,
  @location(0) uv : vec2<f32>
) -> @location(0) vec4<f32> {
  let uOffset = kawaseBlurUniforms.uOffset;
  var color: vec4<f32> = vec4(0.0);

  // Sample top left pixel
  color += textureSample(uTexture, uSampler, clamp(vec2<f32>(uv.x - uOffset.x, uv.y + uOffset.y), gfu.uInputClamp.xy, gfu.uInputClamp.zw));
  // Sample top right pixel
  color += textureSample(uTexture, uSampler, clamp(vec2<f32>(uv.x + uOffset.x, uv.y + uOffset.y), gfu.uInputClamp.xy, gfu.uInputClamp.zw));
  // Sample bottom right pixel
  color += textureSample(uTexture, uSampler, clamp(vec2<f32>(uv.x + uOffset.x, uv.y - uOffset.y), gfu.uInputClamp.xy, gfu.uInputClamp.zw));
  // Sample bottom left pixel
  color += textureSample(uTexture, uSampler, clamp(vec2<f32>(uv.x - uOffset.x, uv.y - uOffset.y), gfu.uInputClamp.xy, gfu.uInputClamp.zw));
  // Average
  color *= 0.25;
    
  return color;
}`,Wg=Object.defineProperty,Hg=(s,e,t)=>e in s?Wg(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t,Qt=(s,e,t)=>(Hg(s,typeof e!="symbol"?e+"":e,t),t);const $l=class eh extends vr{constructor(...e){let t=e[0]??{};(typeof t=="number"||Array.isArray(t))&&(be("6.0.0","KawaseBlurFilter constructor params are now options object. See params: { strength, quality, clamp, pixelSize }"),t={strength:t},e[1]!==void 0&&(t.quality=e[1]),e[2]!==void 0&&(t.clamp=e[2])),t={...eh.DEFAULT_OPTIONS,...t};const i=Vt.from({vertex:{source:Gr,entryPoint:"mainVertex"},fragment:{source:t!=null&&t.clamp?Ug:Dg,entryPoint:"mainFragment"}}),r=_i.from({vertex:Or,fragment:t!=null&&t.clamp?Ng:Gg,name:"kawase-blur-filter"});super({gpuProgram:i,glProgram:r,resources:{kawaseBlurUniforms:{uOffset:{value:new Float32Array(2),type:"vec2<f32>"}}}}),Qt(this,"uniforms"),Qt(this,"_pixelSize",{x:0,y:0}),Qt(this,"_clamp"),Qt(this,"_kernels",[]),Qt(this,"_blur"),Qt(this,"_quality"),this.uniforms=this.resources.kawaseBlurUniforms.uniforms,this.pixelSize=t.pixelSize??{x:1,y:1},Array.isArray(t.strength)?this.kernels=t.strength:typeof t.strength=="number"&&(this._blur=t.strength,this.quality=t.quality??3),this._clamp=!!t.clamp}apply(e,t,i,r){const n=this.pixelSizeX/t.source.width,o=this.pixelSizeY/t.source.height;let a;if(this._quality===1||this._blur===0)a=this._kernels[0]+.5,this.uniforms.uOffset[0]=a*n,this.uniforms.uOffset[1]=a*o,e.applyFilter(this,t,i,r);else{const l=Ei.getSameSizeTexture(t);let h=t,c=l,u;const f=this._quality-1;for(let d=0;d<f;d++)a=this._kernels[d]+.5,this.uniforms.uOffset[0]=a*n,this.uniforms.uOffset[1]=a*o,e.applyFilter(this,h,c,!0),u=h,h=c,c=u;a=this._kernels[f]+.5,this.uniforms.uOffset[0]=a*n,this.uniforms.uOffset[1]=a*o,e.applyFilter(this,h,i,r),Ei.returnTexture(l)}}get strength(){return this._blur}set strength(e){this._blur=e,this._generateKernels()}get quality(){return this._quality}set quality(e){this._quality=Math.max(1,Math.round(e)),this._generateKernels()}get kernels(){return this._kernels}set kernels(e){Array.isArray(e)&&e.length>0?(this._kernels=e,this._quality=e.length,this._blur=Math.max(...e)):(this._kernels=[0],this._quality=1)}get pixelSize(){return this._pixelSize}set pixelSize(e){if(typeof e=="number"){this.pixelSizeX=this.pixelSizeY=e;return}if(Array.isArray(e)){this.pixelSizeX=e[0],this.pixelSizeY=e[1];return}this._pixelSize=e}get pixelSizeX(){return this.pixelSize.x}set pixelSizeX(e){this.pixelSize.x=e}get pixelSizeY(){return this.pixelSize.y}set pixelSizeY(e){this.pixelSize.y=e}get clamp(){return this._clamp}_updatePadding(){this.padding=Math.ceil(this._kernels.reduce((e,t)=>e+t+.5,0))}_generateKernels(){const e=this._blur,t=this._quality,i=[e];if(e>0){let r=e;const n=e/t;for(let o=1;o<t;o++)r-=n,i.push(r)}this._kernels=i,this._updatePadding()}};Qt($l,"DEFAULT_OPTIONS",{strength:4,quality:3,clamp:!1,pixelSize:{x:1,y:1}});let Vg=$l;var Xg=`precision highp float;
in vec2 vTextureCoord;
out vec4 finalColor;

uniform sampler2D uTexture;
uniform float uAlpha;
uniform vec3 uColor;
uniform vec2 uOffset;

uniform vec4 uInputSize;

void main(void){
    vec4 sample = texture(uTexture, vTextureCoord - uOffset * uInputSize.zw);

    // Premultiply alpha
    sample.rgb = uColor.rgb * sample.a;

    // alpha user alpha
    sample *= uAlpha;

    finalColor = sample;
}`,Yg=`struct DropShadowUniforms {
  uAlpha: f32,
  uColor: vec3<f32>,
  uOffset: vec2<f32>,
};

struct GlobalFilterUniforms {
  uInputSize:vec4<f32>,
  uInputPixel:vec4<f32>,
  uInputClamp:vec4<f32>,
  uOutputFrame:vec4<f32>,
  uGlobalFrame:vec4<f32>,
  uOutputTexture:vec4<f32>,
};

@group(0) @binding(0) var<uniform> gfu: GlobalFilterUniforms;

@group(0) @binding(1) var uTexture: texture_2d<f32>; 
@group(0) @binding(2) var uSampler: sampler;
@group(1) @binding(0) var<uniform> dropShadowUniforms : DropShadowUniforms;

@fragment
fn mainFragment(
  @builtin(position) position: vec4<f32>,
  @location(0) uv : vec2<f32>
) -> @location(0) vec4<f32> {
  var color: vec4<f32> = textureSample(uTexture, uSampler, uv - dropShadowUniforms.uOffset * gfu.uInputSize.zw);

  // Premultiply alpha
  color = vec4<f32>(vec3<f32>(dropShadowUniforms.uColor.rgb * color.a), color.a);
  // alpha user alpha
  color *= dropShadowUniforms.uAlpha;

  return color;
}`,jg=Object.defineProperty,qg=(s,e,t)=>e in s?jg(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t,li=(s,e,t)=>(qg(s,typeof e!="symbol"?e+"":e,t),t);const th=class ih extends vr{constructor(e){e={...ih.DEFAULT_OPTIONS,...e};const t=Vt.from({vertex:{source:Gr,entryPoint:"mainVertex"},fragment:{source:Yg,entryPoint:"mainFragment"}}),i=_i.from({vertex:Or,fragment:Xg,name:"drop-shadow-filter"});super({gpuProgram:t,glProgram:i,resources:{dropShadowUniforms:{uAlpha:{value:e.alpha,type:"f32"},uColor:{value:new Float32Array(3),type:"vec3<f32>"},uOffset:{value:e.offset,type:"vec2<f32>"}}},resolution:e.resolution}),li(this,"uniforms"),li(this,"shadowOnly",!1),li(this,"_color"),li(this,"_blurFilter"),li(this,"_basePass"),this.uniforms=this.resources.dropShadowUniforms.uniforms,this._color=new Ne,this.color=e.color??0,this._blurFilter=new Vg({strength:e.kernels??e.blur,quality:e.kernels?void 0:e.quality}),this._basePass=new vr({gpuProgram:Vt.from({vertex:{source:Gr,entryPoint:"mainVertex"},fragment:{source:`
                    @group(0) @binding(1) var uTexture: texture_2d<f32>; 
                    @group(0) @binding(2) var uSampler: sampler;
                    @fragment
                    fn mainFragment(
                        @builtin(position) position: vec4<f32>,
                        @location(0) uv : vec2<f32>
                    ) -> @location(0) vec4<f32> {
                        return textureSample(uTexture, uSampler, uv);
                    }
                    `,entryPoint:"mainFragment"}}),glProgram:_i.from({vertex:Or,fragment:`
                in vec2 vTextureCoord;
                out vec4 finalColor;
                uniform sampler2D uTexture;

                void main(void){
                    finalColor = texture(uTexture, vTextureCoord);
                }
                `,name:"drop-shadow-filter"}),resources:{}}),Object.assign(this,e)}apply(e,t,i,r){const n=Ei.getSameSizeTexture(t);e.applyFilter(this,t,n,!0),this._blurFilter.apply(e,n,i,r),this.shadowOnly||e.applyFilter(this._basePass,t,i,!1),Ei.returnTexture(n)}get offset(){return this.uniforms.uOffset}set offset(e){this.uniforms.uOffset=e,this._updatePadding()}get offsetX(){return this.offset.x}set offsetX(e){this.offset.x=e,this._updatePadding()}get offsetY(){return this.offset.y}set offsetY(e){this.offset.y=e,this._updatePadding()}get color(){return this._color.value}set color(e){this._color.setValue(e);const[t,i,r]=this._color.toArray();this.uniforms.uColor[0]=t,this.uniforms.uColor[1]=i,this.uniforms.uColor[2]=r}get alpha(){return this.uniforms.uAlpha}set alpha(e){this.uniforms.uAlpha=e}get blur(){return this._blurFilter.strength}set blur(e){this._blurFilter.strength=e,this._updatePadding()}get quality(){return this._blurFilter.quality}set quality(e){this._blurFilter.quality=e,this._updatePadding()}get kernels(){return this._blurFilter.kernels}set kernels(e){this._blurFilter.kernels=e}get pixelSize(){return this._blurFilter.pixelSize}set pixelSize(e){typeof e=="number"&&(e={x:e,y:e}),Array.isArray(e)&&(e={x:e[0],y:e[1]}),this._blurFilter.pixelSize=e}get pixelSizeX(){return this._blurFilter.pixelSizeX}set pixelSizeX(e){this._blurFilter.pixelSizeX=e}get pixelSizeY(){return this._blurFilter.pixelSizeY}set pixelSizeY(e){this._blurFilter.pixelSizeY=e}_updatePadding(){const e=Math.max(Math.abs(this.offsetX),Math.abs(this.offsetY));this.padding=e+this.blur*2+this.quality*4}};li(th,"DEFAULT_OPTIONS",{offset:{x:4,y:4},color:0,alpha:.5,shadowOnly:!1,kernels:void 0,blur:2,quality:3,pixelSize:{x:1,y:1},resolution:1});let Kg=th;class Zg{constructor(){v(this,"name","image");v(this,"context");v(this,"current_tool");v(this,"current_subtool")}async setup(e,t,i){this.context=e,this.current_tool=t,this.current_subtool=i}cleanup(){}async add_image({image:e,fixed_canvas:t,border_region:i=0}){const r=new Qg(this.context,e,t,i);await this.context.execute_command(r)}set_tool(e,t){this.current_tool=e,this.current_subtool=t}}class Qg{constructor(e,t,i,r=0){v(this,"sprite",null);v(this,"fixed_canvas");v(this,"context");v(this,"background");v(this,"current_canvas_size");v(this,"computed_dimensions");v(this,"current_scale");v(this,"current_position");v(this,"border_region");v(this,"scaled_width");v(this,"scaled_height");v(this,"previous_image",null);v(this,"name");if(this.name="AddImage",this.context=e,this.background=t,this.fixed_canvas=i,this.border_region=r,this.current_canvas_size=ei(this.context.dimensions),this.current_scale=ei(this.context.scale),this.current_position=ei(this.context.position),this.computed_dimensions={width:0,height:0},this.context.background_image&&this.context.background_image.texture){const n=this.context.background_image,o=n.borderRegion||0;this.previous_image={texture:this.clone_texture(n.texture),width:n.width,height:n.height,x:n.position.x,y:n.position.y,border_region:o}}}clone_texture(e){const t=it.create({width:e.width,height:e.height,resolution:window.devicePixelRatio||1}),i=new ve(e),r=new re;return r.addChild(i),this.context.app.renderer.render(r,{renderTexture:t}),r.destroy({children:!0}),t}async start(){let e;if(this.background instanceof _e)e=this.background;else{const r=await createImageBitmap(this.background);e=_e.from(r)}this.sprite=new ve(e);const[t,i]=this.handle_image();this.computed_dimensions={width:t,height:i}}handle_image(){if(this.sprite===null)return[0,0];if(this.fixed_canvas){const e=Math.max(this.current_canvas_size.width-this.border_region*2,10),t=Math.max(this.current_canvas_size.height-this.border_region*2,10),{width:i,height:r,x:n,y:o}=Jg(this.sprite.width,this.sprite.height,e,t);this.sprite.width=i,this.sprite.height=r,this.sprite.x=n+this.border_region,this.sprite.y=o+this.border_region}else{const e=this.sprite.width,t=this.sprite.height;return this.sprite.x=this.border_region,this.sprite.y=this.border_region,[e+this.border_region*2,t+this.border_region*2]}return[this.current_canvas_size.width,this.current_canvas_size.height]}async execute(e){if(e&&(this.context=e),await this.start(),this.sprite===null)return;const{width:t,height:i}=this.computed_dimensions;await this.context.set_image_properties({scale:1,position:{x:this.context.app.screen.width/2,y:this.context.app.screen.height/2},width:this.fixed_canvas?this.current_canvas_size.width:t,height:this.fixed_canvas?this.current_canvas_size.height:i});const r=this.context.layer_manager.create_background_layer(this.fixed_canvas?this.current_canvas_size.width:t,this.fixed_canvas?this.current_canvas_size.height:i);this.sprite.zIndex=0,r.addChild(this.sprite),this.context.layer_manager.reset_layers(this.fixed_canvas?this.current_canvas_size.width:t,this.fixed_canvas?this.current_canvas_size.height:i,!0),this.border_region>0&&(this.sprite.borderRegion=this.border_region),this.context.set_background_image(this.sprite),this.context.reset()}async undo(){if(this.sprite){if(this.sprite.destroy(),this.previous_image){const e=new ve(this.previous_image.texture);e.width=this.previous_image.width,e.height=this.previous_image.height,e.position.set(this.previous_image.x,this.previous_image.y),this.previous_image.border_region>0&&(e.borderRegion=this.previous_image.border_region),await this.context.set_image_properties({scale:1,position:{x:this.context.app.screen.width/2,y:this.context.app.screen.height/2},width:this.previous_image.width,height:this.previous_image.height});const t=this.context.layer_manager.create_background_layer(this.previous_image.width,this.previous_image.height);e.zIndex=0,t.addChild(e),this.context.layer_manager.reset_layers(this.previous_image.width,this.previous_image.height,!0),this.context.set_background_image(e)}else await this.context.set_image_properties({scale:1,position:{x:this.context.app.screen.width/2,y:this.context.app.screen.height/2},width:this.current_canvas_size.width,height:this.current_canvas_size.height}),this.context.layer_manager.create_background_layer(this.current_canvas_size.width,this.current_canvas_size.height),this.context.layer_manager.reset_layers(this.current_canvas_size.width,this.current_canvas_size.height);this.context.reset()}}}function Jg(s,e,t,i){const r=s/e,n=t/i;let o,a;r>n?(o=t,a=t/r):(a=i,o=i*r);const l=Math.round((t-o)/2),h=Math.round((i-a)/2);return{width:Math.round(o),height:Math.round(a),x:l,y:h}}class Dr{constructor(){v(this,"name","zoom");v(this,"min_zoom",ci(!0));v(this,"image_editor_context");v(this,"max_zoom",10);v(this,"border_padding",30);v(this,"pad_bottom",0);v(this,"is_pinching",!1);v(this,"is_dragging",!1);v(this,"is_pointer_dragging",!1);v(this,"last_touch_position",null);v(this,"last_pinch_distance",0);v(this,"drag_start",new fe);v(this,"current_tool");v(this,"current_subtool");v(this,"local_scale",1);v(this,"local_dimensions",{width:0,height:0});v(this,"local_position",{x:0,y:0})}prevent_default(e){e.preventDefault(),e.stopPropagation()}set_tool(e,t){this.current_tool=e,this.current_subtool=t}set_zoom(e){const t=this.calculate_min_zoom(this.local_dimensions.width,this.local_dimensions.height);let i;const r=e==="fit";r?i=t:i=Math.max(0,Math.min(this.max_zoom,e));const n=this.image_editor_context.app.screen,o=n.width,a=n.height;let l;l={x:o/2,y:a/2},this.zoom_to_point(i,l,!0,r)}async setup(e,t,i){this.image_editor_context=e,this.current_tool=t,this.current_subtool=i,this.pad_bottom=e.pad_bottom;const{width:r,height:n}=await this.get_container_dimensions(),o=this.calculate_min_zoom(r,n),a=Math.min(o,1);this.local_scale=a;const l=this.image_editor_context.app.screen,h=l.width/2,c=l.height/2,u=r*a,f=n*a,d=h-u/2,_=c-f/2;this.local_position={x:d,y:_},this.setup_event_listeners(),await this.image_editor_context.set_image_properties({scale:a,position:{x:d,y:_}}),this.image_editor_context.dimensions.subscribe(g=>{this.local_dimensions=g}),this.image_editor_context.scale.subscribe(g=>{this.local_scale=g}),this.image_editor_context.position.subscribe(g=>{this.local_position=g})}setup_event_listeners(){const e=this.image_editor_context.app.stage;this.image_editor_context.app.canvas.addEventListener("wheel",this.prevent_default,{passive:!1}),e.eventMode="static",e.hitArea=this.image_editor_context.app.screen;const i=this.handle_wheel.bind(this);e.addEventListener("wheel",i,{passive:!1}),"ontouchstart"in window?(e.addEventListener("touchstart",this.handle_touch_start.bind(this)),e.addEventListener("touchmove",this.handle_touch_move.bind(this)),e.addEventListener("touchend",this.handle_touch_end.bind(this))):(e.addEventListener("pointerdown",this.handle_pointer_down.bind(this)),e.addEventListener("pointermove",this.handle_pointer_move.bind(this)),e.addEventListener("pointerup",this.handle_pointer_up.bind(this)),e.addEventListener("pointerupoutside",this.handle_pointer_up.bind(this)))}handle_wheel(e){const t=e.deltaMode===0&&Math.abs(e.deltaY)<50,i=t?30:10;if(e.altKey||e.metaKey){const r=this.image_editor_context.app.stage.toLocal(e.global),n=-e.deltaY*(t?.001:5e-4),o=this.local_scale*(1+n);this.zoom_to_point(o,r,!0)}else{const r=e.deltaX,n=e.deltaY,o={x:this.local_position.x-r/100*i,y:this.local_position.y-n/100*i},a=this.image_editor_context.app.screen,l=this.local_dimensions.width*this.local_scale,h=this.local_dimensions.height*this.local_scale,c=a.width-this.border_padding*2,u=a.height-this.border_padding*2-this.pad_bottom;let f={...o};if(l<=c)f.x=(a.width-l)/2;else{const d=a.width/2,_=d,g=a.width-d-l;f.x=Math.min(Math.max(o.x,g),_)}if(h<=u)f.y=(a.height-this.pad_bottom-h)/2;else{const d=(a.height-this.pad_bottom)/2,_=d,g=a.height-this.pad_bottom-d-h;f.y=Math.min(Math.max(o.y,g),_)}this.image_editor_context.set_image_properties({scale:this.local_scale,position:f})}}cleanup(){var t,i;const e=(i=(t=this==null?void 0:this.image_editor_context)==null?void 0:t.app)==null?void 0:i.stage;e&&(e.removeEventListener("wheel",this.handle_wheel.bind(this)),"ontouchstart"in window?(e.removeEventListener("touchstart",this.handle_touch_start.bind(this)),e.removeEventListener("touchmove",this.handle_touch_move.bind(this)),e.removeEventListener("touchend",this.handle_touch_end.bind(this))):(e.removeEventListener("pointerdown",this.handle_pointer_down.bind(this)),e.removeEventListener("pointermove",this.handle_pointer_move.bind(this)),e.removeEventListener("pointerup",this.handle_pointer_up.bind(this)),e.removeEventListener("pointerupoutside",this.handle_pointer_up.bind(this))))}async get_container_dimensions(){const e=this.image_editor_context.image_container.getLocalBounds();return{width:e.width,height:e.height}}calculate_min_zoom(e,t){const i=this.image_editor_context.app.screen,r=i.width,n=i.height;if(!e||!r||!t||!n)return 1;const o=r-this.border_padding*2,a=n-this.border_padding*2-this.pad_bottom,l=o/e,h=a/t;return Math.min(l,h)}handle_touch_start(e){e.preventDefault();const t=e;if(t.touches&&t.touches.length===2)this.is_pinching=!0,this.last_pinch_distance=Math.hypot(t.touches[0].pageX-t.touches[1].pageX,t.touches[0].pageY-t.touches[1].pageY);else if(t.touches&&t.touches.length===1){this.is_dragging=!0;const i=this.image_editor_context.app.view.getBoundingClientRect();this.last_touch_position=new fe(t.touches[0].pageX-i.left,t.touches[0].pageY-i.top)}}handle_touch_move(e){e.preventDefault();const t=e;if(this.is_pinching&&t.touches&&t.touches.length===2){const i=this.image_editor_context.app.view.getBoundingClientRect(),r=Math.hypot(t.touches[0].pageX-t.touches[1].pageX,t.touches[0].pageY-t.touches[1].pageY),n={x:(t.touches[0].pageX+t.touches[1].pageX)/2-i.left,y:(t.touches[0].pageY+t.touches[1].pageY)/2-i.top},o=r/this.last_pinch_distance;this.last_pinch_distance=r,this.zoom_to_point(this.local_scale*o,n)}else if(this.is_dragging&&t.touches&&t.touches.length===1&&this.last_touch_position){const i=this.image_editor_context.app.view.getBoundingClientRect(),r=new fe(t.touches[0].pageX-i.left,t.touches[0].pageY-i.top),n=r.x-this.last_touch_position.x,o=r.y-this.last_touch_position.y;this.image_editor_context.set_image_properties({position:{x:this.local_position.x+n,y:this.local_position.y+o}}),this.last_touch_position=r}}handle_touch_end(e){e.preventDefault();const t=e;if(t.touches&&t.touches.length<2&&(this.is_pinching=!1,t.touches&&t.touches.length===1)){const i=this.image_editor_context.app.view.getBoundingClientRect();this.last_touch_position=new fe(t.touches[0].pageX-i.left,t.touches[0].pageY-i.top),this.is_dragging=!0}t.touches&&t.touches.length===0&&(this.is_dragging=!1,this.last_touch_position=null,this.last_pinch_distance=0)}get_bounded_position(e){const t=this.image_editor_context.app.screen,i=this.local_dimensions.width*this.local_scale,r=this.local_dimensions.height*this.local_scale,n={x:(t.width-i)/2,y:(t.height-r-this.pad_bottom)/2};if(i<=t.width&&r<=t.height)return n;let o=e.x,a=e.y;if(i<=t.width)o=n.x;else{const l=t.width-i;o=Math.max(l,Math.min(0,e.x))}if(r<=t.height-this.pad_bottom)a=n.y;else{const l=t.height-r-this.pad_bottom;a=Math.max(l,Math.min(0,e.y))}return{x:o,y:a}}zoom_to_point(e,t,i,r){const n={x:(t.x-this.local_position.x)/this.local_scale,y:(t.y-this.local_position.y)/this.local_scale},o={x:n.x/this.local_dimensions.width,y:n.y/this.local_dimensions.height},a=this.calculate_min_zoom(this.local_dimensions.width,this.local_dimensions.height),l=Math.min(a,1);e=Math.min(Math.max(e,l),this.max_zoom);const h=this.local_dimensions.width*e,c=this.local_dimensions.height*e;let u={x:t.x-h*o.x,y:t.y-c*o.y};if(e===l||r){const f=this.image_editor_context.app.screen.width,d=this.image_editor_context.app.screen.height;u={x:(f-h)/2,y:(d-this.pad_bottom-c)/2}}this.image_editor_context.set_image_properties({scale:e,position:u,animate:typeof i=="boolean"?!i:e===l}),this.min_zoom.set(e===l)}handle_pointer_down(e){e.button===0&&this.current_tool==="pan"&&(this.is_pointer_dragging=!0,this.drag_start.copyFrom(e.global),this.drag_start.x-=this.local_position.x,this.drag_start.y-=this.local_position.y)}handle_pointer_move(e){if(this.is_pointer_dragging&&this.current_tool==="pan"){const t={x:e.global.x-this.drag_start.x,y:e.global.y-this.drag_start.y},i=this.image_editor_context.app.screen,r=this.local_dimensions.width*this.local_scale,n=this.local_dimensions.height*this.local_scale,o=i.width-this.border_padding*2,a=i.height-this.border_padding*2-this.pad_bottom;let l={...t};r<=o?l.x=(i.width-r)/2:l.x=t.x,n<=a?l.y=(i.height-this.pad_bottom-n)/2:l.y=t.y,this.image_editor_context.set_image_properties({scale:this.local_scale,position:l})}}handle_pointer_up(e){if(this.is_pointer_dragging&&this.current_tool==="pan"){this.is_pointer_dragging=!1;const t={x:e.global.x-this.drag_start.x,y:e.global.y-this.drag_start.y},i=this.image_editor_context.app.screen,r=this.local_dimensions.width*this.local_scale,n=this.local_dimensions.height*this.local_scale,o=i.width-this.border_padding*2,a=i.height-this.border_padding*2-this.pad_bottom;let l={...t};if(r<=o)l.x=(i.width-r)/2;else{const h=i.width/2,c=h,u=i.width-h-r;l.x=Math.min(Math.max(t.x,u),c)}if(n<=a)l.y=(i.height-this.pad_bottom-n)/2;else{const h=(i.height-this.pad_bottom)/2,c=h,u=i.height-this.pad_bottom-h-n;l.y=Math.min(Math.max(t.y,u),c)}this.image_editor_context.set_image_properties({scale:this.local_scale,position:l,animate:!0})}}}class $g{constructor(e,t,i,r,n,o){v(this,"layers",[]);v(this,"active_layer",null);v(this,"active_layer_id",null);v(this,"draw_textures",new Map);v(this,"layer_store",ci({active_layer:"",layers:[]}));v(this,"background_layer",null);v(this,"image_container");v(this,"app");v(this,"fixed_canvas");v(this,"dark");v(this,"border_region");v(this,"layer_options");this.image_container=e,this.app=t,this.fixed_canvas=i,this.dark=r,this.border_region=n,this.layer_options=o}toggle_layer_visibility(e){const t=this.layers.find(i=>i.id===e);t&&(t.container.visible=!t.container.visible,t.visible=t.container.visible,this.layer_store.update(i=>({active_layer:i.active_layer,layers:this.layers})))}create_background_layer(e,t){this.background_layer&&this.background_layer.destroy();const i=new re;this.background_layer=i;const r=it.create({width:e,height:t,resolution:window.devicePixelRatio,antialias:!0,scaleMode:fo.NEAREST}),n=new ve(r);i.addChild(n);const o=new Ue;return o.clear(),o.rect(0,0,e,t).fill({color:this.dark?3355443:16777215,alpha:1}),this.app.renderer.render({container:o,target:r,clear:!0}),this.image_container.addChild(i),i.zIndex=-1,this.update_layer_order(),i}set_layer_options(e,t,i){this.layer_options=e,this.reset_layers(t,i)}async create_background_layer_from_url(e,t,i){const r=this.create_background_layer(t||this.image_container.width,i||this.image_container.height);try{const n=await _e.from(e),o=new ve(n),a=o.texture.width,l=o.texture.height,h=t||this.image_container.width,c=i||this.image_container.height;if(this.fixed_canvas){const u=Math.max(h-this.border_region*2,10),f=Math.max(c-this.border_region*2,10),d=a/l,_=u/f;let g,m,p=this.border_region,b=this.border_region;a<=u&&l<=f?(g=a,m=l):d>_?(g=u,m=u/d):(m=f,g=f*d),p+=Math.round((u-g)/2),b+=Math.round((f-m)/2),o.width=g,o.height=m,o.position.set(p,b)}else{o.position.set(this.border_region,this.border_region),this.background_layer&&this.background_layer.destroy();const u=a+this.border_region*2,f=l+this.border_region*2,d=this.create_background_layer(u,f);return o.width=a,o.height=l,d.addChild(o),d}return r.addChild(o),r}catch(n){return console.error("Error loading image from URL:",n),r}}create_layer({width:e,height:t,layer_name:i,user_created:r,layer_id:n=void 0,make_active:o=!1}){const a=new re,l=n||Math.random().toString(36).substring(2,15),h=i||`Layer ${this.layers.length+1}`;this.layers.push({name:h,id:l,container:a,user_created:r,visible:!0}),this.image_container.addChild(a);const c=it.create({width:e,height:t,resolution:window.devicePixelRatio,antialias:!0,scaleMode:fo.NEAREST}),u=new ve(c);a.addChild(u);const f=new Ue;return f.clear(),f.beginFill(0,0),f.drawRect(0,0,e,t),f.endFill(),this.app.renderer.render({container:f,target:c,clear:!0}),this.draw_textures.set(a,c),this.update_layer_order(),o&&this.set_active_layer(l),this.layer_store.set({active_layer:this.active_layer_id||"",layers:this.layers}),a}async add_layer_from_url(e){const{width:t,height:i}=this.image_container.getLocalBounds(),r=this.create_layer({width:t,height:i,layer_name:"Layer 1",user_created:!0}),n=this.layers.findIndex(a=>a.container===r);if(n===-1)return console.error("Could not find newly created layer"),"";const o=this.layers[n].id;try{const a=await $t.load(e),l=this.draw_textures.get(r);if(!l)return console.error("No draw texture found for layer"),o;const h=new ve(a),c=h.width,u=h.height;let f=this.border_region,d=this.border_region;const _=this.fixed_canvas?t-this.border_region*2:t,g=this.fixed_canvas?i-this.border_region*2:i;if((c<_||u<g)&&(f=Math.floor((_-c)/2),d=Math.floor((g-u)/2)),h.position.set(f,d),c>_||u>g){const m=c/u,p=_/g;let b,y;m>p?(b=_,y=_/m):(y=g,b=g*m),h.width=b,h.height=y,f=this.border_region+Math.floor((_-b)/2),d=this.border_region+Math.floor((g-y)/2),h.position.set(f,d)}return this.app.renderer.render(h,{renderTexture:l}),this.set_active_layer(o),o}catch(a){return console.error("Error loading image from URL:",a),o}}get_active_layer(){return this.active_layer}set_active_layer(e){var t,i;this.layers.some(r=>r.id===e)&&(this.active_layer=((t=this.layers.find(r=>r.id===e))==null?void 0:t.container)||((i=this.layers[0])==null?void 0:i.container)||null,this.active_layer_id=e,this.layer_store.set({active_layer:e,layers:this.layers}))}get_layers(){return this.layers}get_layer_textures(e){const t=this.layers.find(i=>i.id===e);if(t){const i=this.draw_textures.get(t.container);if(i)return{draw:i}}return null}delete_layer(e){const t=this.layers.findIndex(i=>i.id===e);if(t>-1){const i=this.draw_textures.get(this.layers[t].container);if(i&&(i.destroy(),this.draw_textures.delete(this.layers[t].container)),this.layers[t].container.destroy(),this.active_layer===this.layers[t].container){const r=this.layers[Math.max(0,t-1)]||null;this.active_layer=(r==null?void 0:r.container)||null,this.active_layer_id=(r==null?void 0:r.id)||null}this.layers=this.layers.filter(r=>r.id!==e),this.layer_store.update(r=>{var n;return{active_layer:r.active_layer===e?(n=this.layers[this.layers.length-1])==null?void 0:n.id:r.active_layer,layers:this.layers}}),this.update_layer_order()}}update_layer_order(){this.background_layer&&(this.background_layer.zIndex=-1),this.layers.forEach((e,t)=>{e.container.zIndex=t})}move_layer(e,t){const i=this.layers.findIndex(r=>r.id===e);if(i>-1){const r=t==="up"?i-1:i+1;this.layers=this.layers.map((n,o)=>o===i?this.layers[r]:o===r?this.layers[i]:n),this.update_layer_order(),this.layer_store.update(n=>({active_layer:e,layers:this.layers}))}}resize_all_layers(e,t,i,r,n,o){new Map(this.layers.map(d=>[d.id,d]));const a=this.background_layer,l=()=>{let d=0,_=0;const g=e-n,m=t-o;return r.includes("left")?d=0:r.includes("right")?d=g:d=Math.floor(g/2),r.includes("top")?_=0:r.includes("bottom")?_=m:_=Math.floor(m/2),{offsetX:d,offsetY:_}};this.background_layer=this._resize_background_layer(a,e,t,i,l);const h=[],c=this.layers.map(d=>({id:d.id,name:d.name,user_created:d.user_created,texture:this.draw_textures.get(d.container),container:d.container}));this.layers=[],this.draw_textures.clear();for(const d of c){const _=this._resize_single_layer(d,e,t,i,l);_&&h.push(_)}const u=ei(this.layer_store).active_layer;!h.some(d=>d.id===u)&&h.length>0?this.set_active_layer(h[0].id):h.length===0?this.layer_store.update(d=>({...d,active_layer:""})):this.layer_store.update(d=>({...d})),this.update_layer_order(),setTimeout(()=>{$t.cache.reset(),this.app.renderer.textureGC.run()},100)}_resize_background_layer(e,t,i,r,n){if(!e)return this.create_background_layer(t,i);let o=e.children.find(l=>{var h;return l instanceof ve&&l.texture!==((h=e.children[0])==null?void 0:h.texture)});const a=this.create_background_layer(t,i);if(o){const l=new ve(o.texture);if(l.width=o.width,l.height=o.height,r)l.width=t,l.height=i,l.position.set(0,0);else{const{offsetX:h,offsetY:c}=n();l.position.set(o.x+h,o.y+c)}a.addChild(l)}return a}_resize_single_layer(e,t,i,r,n){if(!e.texture)return console.warn(`No texture found for layer ${e.id}, skipping cleanup.`),e.container&&!e.container.destroyed&&(this.image_container.children.includes(e.container)&&this.image_container.removeChild(e.container),e.container.destroy({children:!0})),null;const o=this.create_layer({width:t,height:i,layer_name:e.name,user_created:e.user_created}),a=this.layers[this.layers.length-1];a.id=e.id;const l=this.draw_textures.get(o);if(!l){if(console.error(`Failed to get texture for newly created layer ${a.id}. Cleaning up.`),o&&!o.destroyed){this.image_container.children.includes(o)&&this.image_container.removeChild(o),o.destroy({children:!0});const c=this.layers.findIndex(u=>u.container===o);c>-1&&this.layers.splice(c,1),this.draw_textures.delete(o)}return e.texture&&!e.texture.destroyed&&e.texture.destroy(!0),e.container&&!e.container.destroyed&&e.container.destroy({children:!0}),null}this.app.renderer.clear({target:l,clearColor:[0,0,0,0]});const h=new ve(e.texture);if(r)h.width=t,h.height=i,h.position.set(0,0);else{const{offsetX:c,offsetY:u}=n();h.position.set(c,u)}return this.app.renderer.render(h,{renderTexture:l}),h.destroy(),e.texture&&!e.texture.destroyed&&e.texture.destroy(!0),e.container&&!e.container.destroyed&&(this.image_container.children.includes(e.container)&&this.image_container.removeChild(e.container),e.container.destroy({children:!0})),a}async get_blobs(e,t){return{background:await as(this.app.renderer,this.background_layer,{width:e,height:t,x:0,y:0}),layers:await Promise.all(this.layers.map(async r=>{const n=await as(this.app.renderer,r.container,{width:e,height:t,x:0,y:0});return n||null})),composite:await as(this.app.renderer,this.image_container,{width:e,height:t,x:0,y:0})}}reset_layers(e,t,i=!1){var n,o;const r=i?this.layers.map(a=>[a.name,a.id]):this.layer_options.layers.map(a=>[a,void 0]);this.layers.forEach(a=>{this.delete_layer(a.id)});for(const[a,l]of r)this.create_layer({width:e,height:t,layer_name:a,user_created:!this.layer_options.layers.find(h=>h===a),layer_id:l});if(!i)this.active_layer=this.layers[0].container,this.active_layer_id=this.layers[0].id;else if(this.active_layer=((n=this.layers.find(a=>a.id===this.active_layer_id))==null?void 0:n.container)||((o=this.layers[0])==null?void 0:o.container),!this.active_layer)return;this.layer_store.update(a=>({active_layer:this.active_layer_id||this.layers[0].id,layers:this.layers}))}init_layers(e,t){for(const r of this.layers)this.delete_layer(r.id);let i=0;for(const r of this.layer_options.layers)this.create_layer({width:e,height:t,layer_name:r,user_created:!1,layer_id:`layer-${i}`}),i++;this.active_layer=this.layers[0].container,this.active_layer_id=this.layers[0].id,this.layer_store.update(r=>({active_layer:this.layers[0].id,layers:this.layers}))}}class em{constructor(e,t){v(this,"layer_id");v(this,"layer_name");v(this,"width");v(this,"height");v(this,"user_created");v(this,"make_active");v(this,"previous_active_layer",null);v(this,"name");this.context=e,this.width=t.width,this.height=t.height,this.layer_name=t.layer_name||`Layer ${this.context.layer_manager.get_layers().length+1}`,this.user_created=t.user_created,this.layer_id=t.layer_id||Math.random().toString(36).substring(2,15),this.make_active=t.make_active||!1,this.name="AddLayer";const r=this.context.layer_manager.get_layers().find(n=>n.container===this.context.layer_manager.get_active_layer());this.previous_active_layer=(r==null?void 0:r.id)||null}async execute(e){e&&(this.context=e),this.context.layer_manager.create_layer({width:this.width,height:this.height,layer_name:this.layer_name,user_created:this.user_created,layer_id:this.layer_id,make_active:this.make_active})}async undo(){this.context.layer_manager.delete_layer(this.layer_id),this.previous_active_layer&&this.context.layer_manager.set_active_layer(this.previous_active_layer)}}class tm{constructor(e,t){v(this,"layer_data");v(this,"previous_active_layer",null);v(this,"texture_copy",null);v(this,"name");this.context=e,this.name="RemoveLayer";const i=this.context.layer_manager.get_layers(),r=i.find(a=>a.id===t);if(!r)throw new Error(`Layer with ID ${t} not found`);const n=this.context.layer_manager.get_active_layer(),o=r.container===n;if(o){const a=i.findIndex(h=>h.id===t),l=i[Math.max(0,a-1)];this.previous_active_layer=(l==null?void 0:l.id)||null}this.layer_data={id:r.id,name:r.name,user_created:r.user_created,visible:r.visible,was_active:o},this.captureTextureData(t)}captureTextureData(e){const t=this.context.layer_manager.get_layer_textures(e);if(t)try{const i=t.draw,r=it.create({width:i.width,height:i.height,resolution:window.devicePixelRatio||1}),n=new ve(i);this.context.app.renderer.render(n,{renderTexture:r}),this.texture_copy=r,n.destroy()}catch(i){console.error("Failed to copy layer texture:",i),this.texture_copy=null}}async execute(e){e&&(this.context=e),this.context.layer_manager.delete_layer(this.layer_data.id)}async undo(){const e=ei(this.context.dimensions);if(this.context.layer_manager.create_layer({width:e.width,height:e.height,layer_name:this.layer_data.name,user_created:this.layer_data.user_created,layer_id:this.layer_data.id,make_active:this.layer_data.was_active}),this.texture_copy)try{const t=this.context.layer_manager.get_layer_textures(this.layer_data.id);if(t){const i=new ve(this.texture_copy);this.context.app.renderer.render(i,{renderTexture:t.draw}),i.destroy()}}catch(t){console.error("Failed to restore layer content:",t)}this.layer_data.visible||this.context.layer_manager.toggle_layer_visibility(this.layer_data.id),!this.layer_data.was_active&&this.previous_active_layer&&this.context.layer_manager.set_active_layer(this.previous_active_layer)}destroy(){this.texture_copy&&(this.texture_copy.destroy(),this.texture_copy=null)}}class im{constructor(e,t,i){v(this,"original_order");v(this,"new_order");v(this,"layer_id");v(this,"direction");v(this,"name");this.context=e,this.layer_id=t,this.direction=i,this.name="ReorderLayer";const r=this.context.layer_manager.get_layers();this.original_order=r.map(a=>a.id);const n=r.findIndex(a=>a.id===t);if(n===-1)throw new Error(`Layer with ID ${t} not found`);const o=i==="up"?n-1:n+1;o<0||o>=r.length?this.new_order=[...this.original_order]:(this.new_order=[...this.original_order],[this.new_order[n],this.new_order[o]]=[this.new_order[o],this.new_order[n]])}async execute(e){e&&(this.context=e),this.context.layer_manager.move_layer(this.layer_id,this.direction)}async undo(){const e=this.context.layer_manager.get_layers();if(this.original_order.join(",")===e.map(n=>n.id).join(","))return;const t=e.findIndex(n=>n.id===this.layer_id);if(t===-1)return;const i=this.original_order.indexOf(this.layer_id),r=t>i?"up":"down";this.context.layer_manager.move_layer(this.layer_id,r)}}const sm={image:()=>new Zg,zoom:()=>new Dr},hr={stiffness:.45,damping:.8};class rm{constructor(e){v(this,"state");v(this,"scale");v(this,"position");v(this,"subscribers");if(!(e instanceof Nr))throw new Error("EditorState must be created by ImageEditor");this.scale=1,this.position={x:0,y:0},this.subscribers=new Set,this.state=Object.freeze({get position(){return{...this.position}},get scale(){return this.scale},get current_tool(){return this.current_tool},get current_subtool(){return this.current_subtool},subscribe:this.subscribe.bind(this)})}_set_position(e,t){const i={...this.position};this.position={x:e,y:t},this._notify_subscribers("position",i,this.position)}_set_scale(e){const t=this.scale;this.scale=e,this._notify_subscribers("scale",t,e)}_notify_subscribers(e,t,i){this.subscribers.forEach(r=>{r({property:e,oldValue:t,newValue:i,timestamp:Date.now()})})}subscribe(e){return this.subscribers.add(e),()=>this.subscribers.delete(e)}}class Nr{constructor(e){v(this,"ready");v(this,"background_image_present",ci(!1));v(this,"min_zoom",ci(!0));v(this,"app");v(this,"ui_container");v(this,"image_container");v(this,"command_manager");v(this,"layer_manager");v(this,"tools",new Map);v(this,"current_tool");v(this,"current_subtool");v(this,"target_element");v(this,"width");v(this,"height");v(this,"dimensions");v(this,"scale");v(this,"position");v(this,"state");v(this,"scale_value",1);v(this,"position_value",{x:0,y:0});v(this,"dimensions_value",{width:0,height:0});v(this,"layers",ci({active_layer:"",layers:[]}));v(this,"outline_container");v(this,"outline_graphics");v(this,"background_image");v(this,"ready_resolve");v(this,"event_callbacks",new Map);v(this,"fixed_canvas");v(this,"dark");v(this,"border_region");v(this,"layer_options");v(this,"overlay_container");v(this,"overlay_graphics");v(this,"pad_bottom");v(this,"theme_mode");this.pad_bottom=e.pad_bottom||0,this.dark=e.dark||!1,this.theme_mode=e.theme_mode||"dark",this.target_element=e.target_element,this.width=e.width,this.height=e.height,this.command_manager=new Rc,this.ready=new Promise(t=>{this.ready_resolve=t}),this.fixed_canvas=e.fixed_canvas||!1,this.tools=new Map(e.tools.map(t=>typeof t=="string"?[t,sm[t]()]:[t.name,t]));for(const t of this.tools.values())t!=null&&t.on&&t.on("change",()=>{this.notify("change")});this.dimensions=Ms({width:this.width,height:this.height},hr),this.scale=Ms(1,hr),this.position=Ms({x:0,y:0},hr),this.state=new rm(this),this.border_region=e.border_region||0,this.layer_options=e.layer_options||{allow_additional_layers:!0,layers:["Layer 1"],disabled:!1},this.scale.subscribe(t=>{this.state._set_scale(t)}),this.position.subscribe(t=>{this.state._set_position(t.x,t.y)}),this.init()}get context(){const e=this;return{app:this.app,ui_container:this.ui_container,image_container:this.image_container,get background_image(){return e.background_image},pad_bottom:this.pad_bottom,command_manager:this.command_manager,layer_manager:this.layer_manager,dimensions:{subscribe:this.dimensions.subscribe},scale:{subscribe:this.scale.subscribe},position:{subscribe:this.position.subscribe},set_image_properties:this.set_image_properties.bind(this),execute_command:this.execute_command.bind(this),resize_canvas:this.resize_canvas.bind(this),reset:this.reset.bind(this),set_background_image:this.set_background_image.bind(this)}}async init(){const e=this.target_element.getBoundingClientRect();new ResizeObserver(r=>{r.forEach(n=>{this.resize_canvas(n.contentBoxSize[0].inlineSize,n.contentBoxSize[0].blockSize)})}).observe(this.target_element),this.app=new vl,this.dark||(globalThis.__PIXI_APP__=this.app),await this.app.init({width:e.width,height:e.height,backgroundAlpha:this.dark?0:1,backgroundColor:this.theme_mode==="dark"?"#27272a":"#ffffff",resolution:window.devicePixelRatio,autoDensity:!0,antialias:!0,powerPreference:"high-performance"});const t=this.app.canvas;t.style.background="transparent",this.setup_containers(),this.layer_manager.create_background_layer(this.width,this.height),this.layer_manager.init_layers(this.width,this.height);for(const r of this.tools.values())await r.setup(this.context,this.current_tool,this.current_subtool);const i=this.tools.get("zoom");i&&i.min_zoom.subscribe(r=>{this.min_zoom.set(r)}),this.target_element.appendChild(t),this.target_element.style.background="transparent",this.dimensions.subscribe(r=>{this.dimensions_value=r,this.image_container.width=r.width,this.image_container.height=r.height}),this.scale.subscribe(r=>{this.scale_value=r}),this.position.subscribe(r=>{this.position_value=r}),this.app.ticker.add(()=>{this.image_container.scale.set(this.scale_value),this.image_container.position.set(this.position_value.x,this.position_value.y);const r=this.dimensions_value.width*this.scale_value,n=this.dimensions_value.height*this.scale_value,o=Math.round(this.image_container.position.x-this.outline_container.position.x),a=Math.round(this.image_container.position.y-this.outline_container.position.y);if(this.overlay_container.position.set(this.outline_container.position.x,this.outline_container.position.y),this.outline_graphics.clear(),this.outline_graphics.rect(o,a,r,n).fill({color:this.dark?3355443:16777215,alpha:1}),this.border_region>0){const l=this.border_region*this.scale_value,h=o+l-1,c=a+l-1,u=r-l*2+1,f=n-l*2+1;this.overlay_graphics.clear(),this.overlay_graphics.rect(h,c,u,f).stroke({color:10066329,width:1,alpha:0,pixelLine:!0});const d=5,g=d+5,m=10066329;for(let p=h;p<h+u;p+=g)this.overlay_graphics.rect(p,c,Math.min(d,h+u-p),1).fill({color:m,alpha:.7}),this.overlay_graphics.rect(p,c+f,Math.min(d,h+u-p),1).fill({color:m,alpha:.7});for(let p=c;p<c+f;p+=g)this.overlay_graphics.rect(h,p,1,Math.min(d,c+f-p)).fill({color:m,alpha:.7}),this.overlay_graphics.rect(h+u,p,1,Math.min(d,c+f-p)).fill({color:m,alpha:.7})}else this.overlay_graphics.clear()}),this.ready_resolve()}setup_containers(){if(this.image_container=new re({eventMode:"static",sortableChildren:!0}),this.image_container.width=this.width,this.image_container.height=this.height,this.app.stage.sortableChildren=!0,this.app.stage.alpha=1,this.app.stage.addChild(this.image_container),this.image_container.scale.set(1),this.ui_container=new re({eventMode:"static"}),this.app.stage.addChild(this.ui_container),this.ui_container.width=this.width,this.ui_container.height=this.height,this.outline_container=new re,this.outline_container.zIndex=-10,this.outline_graphics=new Ue,this.outline_graphics.rect(0,0,this.width,this.height).fill({color:this.dark?3355443:16777215,alpha:0}),!this.dark){const i=new Kg({alpha:.1,blur:2,color:0,offset:{x:0,y:0},quality:4,shadowOnly:!1});this.outline_graphics.filters=[i]}this.outline_container.addChild(this.outline_graphics),this.app.stage.addChild(this.outline_container),this.overlay_container=new re,this.app.stage.addChild(this.overlay_container),this.overlay_container.width=this.width,this.overlay_container.height=this.height,this.overlay_container.zIndex=999,this.overlay_container.eventMode="static",this.overlay_container.interactiveChildren=!0,this.overlay_graphics=new Ue,this.overlay_container.addChild(this.overlay_graphics),this.overlay_graphics.rect(0,0,this.width,this.height).fill({color:0,alpha:0});const e=this.app.screen.width/2,t=this.app.screen.height/2;this.image_container.position.set(e,t),this.outline_container.position.set(e,t),this.layer_manager=new $g(this.image_container,this.app,this.fixed_canvas,this.dark,this.border_region,this.layer_options),this.layers=this.layer_manager.layer_store}resize_canvas(e,t){this.app.renderer&&this.app.renderer.resize(e,t);const i=e/2,r=t/2;this.image_container&&this.image_container.position.set(i,r),this.outline_container&&this.outline_container.position.set(i,r)}reset(){const e=this.tools.get("zoom");e&&(e.cleanup(),e.setup(this.context,this.current_tool,this.current_subtool)),this.tools.get("brush")&&this.set_tool("draw")}async set_image_properties(e){let t=typeof e.animate<"u"?!e.animate:!0;if(e.position){const i=this.position.set(e.position,{hard:t});t&&await i}if(e.scale){const i=this.scale.set(e.scale,{hard:t});t&&await i}if(e.width&&e.height){this.width=e.width,this.height=e.height;const i=this.dimensions.set({width:e.width,height:e.height},{hard:t});t&&await i}}async execute_command(e){await this.command_manager.execute(e,this.context)}undo(){this.command_manager.undo(),this.notify("change")}redo(){this.command_manager.redo(this.context),this.notify("change")}async add_image({image:e,resize:t=!0}){await this.tools.get("image").add_image({image:e,fixed_canvas:this.fixed_canvas,border_region:this.border_region})}async add_image_from_url(e){const t=this.tools.get("image"),i=await $t.load(e);await t.add_image({image:i,fixed_canvas:this.fixed_canvas,border_region:this.border_region});const r=this.tools.get("resize");r&&typeof r.set_border_region=="function"&&r.set_border_region(this.border_region)}set_tool(e){this.current_tool=e;for(const t of this.tools.values())t.set_tool(this.current_tool,this.current_subtool)}set_subtool(e){this.current_subtool=e;for(const t of this.tools.values())t.set_tool(this.current_tool,this.current_subtool)}set_background_image(e){this.background_image=e}async reset_canvas(){this.layer_manager.reset_layers(this.width,this.height),this.background_image=void 0,this.background_image_present.set(!1),this.command_manager.reset(),await this.set_image_properties({width:this.width,height:this.height,scale:1,position:{x:0,y:0},animate:!1}),this.layer_manager.create_background_layer(this.width,this.height);for(const t of this.tools.values())t.cleanup(),t.setup(this.context,this.current_tool,this.current_subtool);const e=this.tools.get("zoom");e&&e.min_zoom.subscribe(t=>{this.min_zoom.set(t)}),this.notify("change")}add_layer(){const e=new em(this.context,{width:this.width,height:this.height,user_created:!0,make_active:!0});this.execute_command(e),this.notify("change")}async add_layers_from_url(e){if(this.command_manager.reset(),this.layer_manager.get_layers().forEach(n=>this.layer_manager.delete_layer(n.id)),e===void 0||e.length===0){this.layer_manager.create_layer({width:this.width,height:this.height,layer_name:void 0,user_created:!1});return}const i=[];for await(const n of e){const o=await this.layer_manager.add_layer_from_url(n);o&&i.push(o)}i.length>0&&this.layer_manager.set_active_layer(i[0]);const r=this.tools.get("resize");r&&typeof r.set_border_region=="function"&&r.set_border_region(this.border_region),this.notify("change"),this.notify("input")}set_layer(e){this.layer_manager.set_active_layer(e),this.notify("change")}move_layer(e,t){const i=new im(this.context,e,t);this.execute_command(i),this.notify("change")}delete_layer(e){const t=new tm(this.context,e);this.execute_command(t),this.notify("change")}modify_canvas_size(e,t,i,r){const n=this.width,o=this.height;this.layer_manager.resize_all_layers(e,t,r,i,n,o),this.width=e,this.height=t,this.set_image_properties({width:e,height:t,scale:1,position:{x:0,y:0},animate:!1}),this.notify("change")}async get_blobs(){return await this.layer_manager.get_blobs(this.width,this.height)}on(e,t){this.event_callbacks.set(e,[...this.event_callbacks.get(e)||[],t])}off(e,t){var i;this.event_callbacks.set(e,((i=this.event_callbacks.get(e))==null?void 0:i.filter(r=>r!==t))||[])}notify(e){for(const t of this.event_callbacks.get(e)||[])t()}destroy(){var e;this.app&&((e=this.app)==null||e.destroy())}resize(e,t){this.set_image_properties({width:e,height:t}),this.reset()}async get_crop_bounds(){const e=this.tools.get("crop"),t=e.get_crop_bounds();return{image:await e.get_image(),...t}}get background_image_sprite(){return this.background_image}set_layer_options(e){this.layer_options=e,this.layer_manager.set_layer_options(e,this.width,this.height)}toggle_layer_visibility(e){this.layer_manager.toggle_layer_visibility(e)}}class nm{constructor(){v(this,"name","crop");v(this,"image_editor_context");v(this,"current_tool","image");v(this,"current_subtool","crop");v(this,"CORNER_SIZE",25);v(this,"LINE_THICKNESS",5);v(this,"HANDLE_COLOR",0);v(this,"HIT_AREA_SIZE",40);v(this,"is_dragging",!1);v(this,"selected_handle",null);v(this,"active_corner_index",-1);v(this,"active_edge_index",-1);v(this,"last_pointer_position",null);v(this,"crop_bounds",{x:0,y:0,width:0,height:0});v(this,"crop_ui_container",null);v(this,"crop_mask",null);v(this,"dimensions",{width:0,height:0});v(this,"position",{x:0,y:0});v(this,"scale",1);v(this,"is_dragging_window",!1);v(this,"drag_start_position",null);v(this,"drag_start_bounds",null);v(this,"background_image_watcher",null);v(this,"has_been_manually_changed",!1);v(this,"event_callbacks",new Map)}async setup(e,t,i){this.image_editor_context=e,this.current_tool=t,this.current_subtool=i,e.dimensions.subscribe(n=>{this.dimensions=n,this.crop_bounds={x:0,y:0,width:n.width,height:n.height},this.crop_ui_container&&this.update_crop_ui(),this.set_crop_mask(),this.update_crop_mask()}),e.position.subscribe(n=>{this.position=n}),e.scale.subscribe(n=>{this.scale=n}),this.background_image_watcher=()=>{this.crop_mask&&this.current_tool==="image"&&this.current_subtool==="crop"&&this.image_editor_context.background_image&&this.image_editor_context.background_image.setMask(this.crop_mask)},this.image_editor_context.app.ticker.add(this.background_image_watcher),await this.init_crop_ui(),this.setup_event_listeners();const r=t==="image"&&i==="crop";this.crop_ui_container&&(this.crop_ui_container.visible=r)}cleanup(){this.crop_ui_container&&this.image_editor_context.app.stage.removeChild(this.crop_ui_container),this.crop_mask&&(this.crop_mask.parent&&this.crop_mask.parent.removeChild(this.crop_mask),this.image_editor_context.background_image&&this.image_editor_context.background_image.mask===this.crop_mask&&(this.image_editor_context.background_image.mask=null),this.image_editor_context.image_container.mask===this.crop_mask&&(this.image_editor_context.image_container.mask=null)),this.crop_mask=null,this.crop_ui_container=null,this.background_image_watcher&&(this.image_editor_context.app.ticker.remove(this.background_image_watcher),this.background_image_watcher=null),this.cleanup_event_listeners()}set_tool(e,t){this.current_tool=e,this.current_subtool=t;const i=e==="image"&&t==="crop";this.crop_ui_container&&(this.crop_ui_container.visible=i),i&&this.crop_mask&&this.update_crop_mask()}set_crop_mask(){this.crop_mask&&(this.crop_mask.parent&&this.crop_mask.parent.removeChild(this.crop_mask),this.image_editor_context.image_container.mask===this.crop_mask&&(this.image_editor_context.image_container.mask=null),this.image_editor_context.background_image&&this.image_editor_context.background_image.mask===this.crop_mask&&(this.image_editor_context.background_image.mask=null)),this.crop_mask=new Ue,this.image_editor_context.image_container.addChild(this.crop_mask)}async init_crop_ui(){var e;(e=this.image_editor_context.background_image)!=null&&e.getLocalBounds(),this.crop_ui_container=this.make_crop_ui(this.dimensions.width*this.scale,this.dimensions.height*this.scale),this.crop_ui_container.position.set(this.position.x,this.position.y),this.crop_ui_container.visible=!1,this.image_editor_context.app.stage.addChild(this.crop_ui_container),this.set_crop_mask(),this.image_editor_context.app.ticker.add(this.update_crop_ui.bind(this)),this.update_crop_mask()}make_crop_ui(e,t){const i=new re;i.eventMode="static",i.interactiveChildren=!0;const r=new Ue().rect(0,0,e,t).stroke({width:1,color:0,alignment:0,alpha:.5});return r.eventMode="static",r.cursor="move",r.hitArea=new Pe(0,0,e,t),r.on("pointerdown",this.handle_window_drag_start.bind(this)),i.addChild(r),this.create_corner_handles(i,e,t),this.create_edge_handles(i,e,t),i}create_handle(e=!1){const t=new re;t.eventMode="static";const i=new Ue;e?i.rect(0,0,this.CORNER_SIZE*1.5,this.LINE_THICKNESS).fill(this.HANDLE_COLOR):i.rect(0,0,this.CORNER_SIZE,this.LINE_THICKNESS).rect(0,0,this.LINE_THICKNESS,this.CORNER_SIZE).fill(this.HANDLE_COLOR),t.addChild(i);const r=e?this.HIT_AREA_SIZE*1.5:this.HIT_AREA_SIZE;return t.hitArea=new Pe(-r/2+this.LINE_THICKNESS,-r/2+this.LINE_THICKNESS,r,r),t}create_edge_handles(e,t,i){[-1,1].forEach((r,n)=>{const o=this.create_handle(!0);o.rotation=0,o.on("pointerdown",a=>{this.handle_pointer_down(a,o,-1,n)}),o.x=t/2-this.CORNER_SIZE*3/2,o.y=r<0?-this.LINE_THICKNESS:i,o.cursor="ns-resize",e.addChild(o)}),[-1,1].forEach((r,n)=>{const o=this.create_handle(!0);o.rotation=Math.PI/2,o.on("pointerdown",a=>{this.handle_pointer_down(a,o,-1,n+2)}),o.x=r<0?-this.LINE_THICKNESS:t,o.y=i/2-this.CORNER_SIZE*3/2,o.cursor="ew-resize",e.addChild(o)})}create_corner_handles(e,t,i){[{x:0,y:0,xScale:1,yScale:1,cursor:"nwse-resize"},{x:t,y:0,xScale:-1,yScale:1,cursor:"nesw-resize"},{x:0,y:i,xScale:1,yScale:-1,cursor:"nesw-resize"},{x:t,y:i,xScale:-1,yScale:-1,cursor:"nwse-resize"}].forEach(({x:n,y:o,xScale:a,yScale:l,cursor:h},c)=>{const u=this.create_handle(!1);u.x=n-(a<0?-this.LINE_THICKNESS:this.LINE_THICKNESS),u.y=o-(l<0?-this.LINE_THICKNESS:this.LINE_THICKNESS),u.scale.set(a,l),u.on("pointerdown",f=>{this.handle_pointer_down(f,u,c,-1)}),u.cursor=h,e.addChild(u)})}handle_pointer_down(e,t,i,r){if(this.current_subtool!=="crop")return;e.stopPropagation(),this.is_dragging=!0,this.selected_handle=t,this.active_corner_index=i,this.active_edge_index=r;const n=this.image_editor_context.image_container.toLocal(e.global);this.last_pointer_position=new fe(n.x,n.y)}update_crop_bounds(e){this.has_been_manually_changed=!0;const t=new fe(e.x,e.y),i={x:this.crop_bounds.x,y:this.crop_bounds.y,width:this.crop_bounds.width,height:this.crop_bounds.height};if(this.active_corner_index!==-1)switch(this.active_corner_index){case 0:const r=Math.max(20,i.width-t.x),n=i.width-r;this.crop_bounds.width=r,this.crop_bounds.x=i.x+n;const o=Math.max(20,i.height-t.y),a=i.height-o;this.crop_bounds.height=o,this.crop_bounds.y=i.y+a;break;case 1:this.crop_bounds.width=Math.max(20,i.width+t.x);const l=Math.max(20,i.height-t.y),h=i.height-l;this.crop_bounds.height=l,this.crop_bounds.y=i.y+h;break;case 2:const c=Math.max(20,i.width-t.x),u=i.width-c;this.crop_bounds.width=c,this.crop_bounds.x=i.x+u,this.crop_bounds.height=Math.max(20,i.height+t.y);break;case 3:this.crop_bounds.width=Math.max(20,i.width+t.x),this.crop_bounds.height=Math.max(20,i.height+t.y);break}else if(this.active_edge_index!==-1)switch(this.active_edge_index){case 0:const r=Math.max(20,i.height-t.y),n=i.height-r;this.crop_bounds.height=r,this.crop_bounds.y=i.y+n;break;case 1:this.crop_bounds.height=Math.max(20,i.height+t.y);break;case 2:const o=Math.max(20,i.width-t.x),a=i.width-o;this.crop_bounds.width=o,this.crop_bounds.x=i.x+a;break;case 3:this.crop_bounds.width=Math.max(20,i.width+t.x);break}this.constrain_crop_bounds(),this.update_crop_ui(),this.update_crop_mask(),this.crop_mask&&this.image_editor_context.background_image&&this.image_editor_context.background_image.setMask(this.crop_mask)}constrain_crop_bounds(){this.crop_bounds.x,this.crop_bounds.y,this.crop_bounds.width,this.crop_bounds.height,this.crop_bounds.width=Math.max(20,Math.min(this.crop_bounds.width,this.dimensions.width)),this.crop_bounds.height=Math.max(20,Math.min(this.crop_bounds.height,this.dimensions.height));const e=this.crop_bounds.x;if(this.crop_bounds.x=Math.max(0,Math.min(this.crop_bounds.x,this.dimensions.width-this.crop_bounds.width)),this.crop_bounds.x!==e&&this.active_corner_index===0||this.active_edge_index===2){const i=this.crop_bounds.x-e;this.crop_bounds.width-=i}const t=this.crop_bounds.y;if(this.crop_bounds.y=Math.max(0,Math.min(this.crop_bounds.y,this.dimensions.height-this.crop_bounds.height)),this.crop_bounds.y!==t&&(this.active_corner_index===0||this.active_corner_index===1||this.active_edge_index===0)){const i=this.crop_bounds.y-t;this.crop_bounds.height-=i}this.crop_bounds.width=Math.max(20,Math.min(this.crop_bounds.width,this.dimensions.width-this.crop_bounds.x)),this.crop_bounds.height=Math.max(20,Math.min(this.crop_bounds.height,this.dimensions.height-this.crop_bounds.y))}update_crop_mask(){var i;if(!this.crop_mask)return;this.crop_mask.clear();const{width:e,height:t}=((i=this.image_editor_context.background_image)==null?void 0:i.getLocalBounds())??{width:0,height:0};this.crop_mask.rect(0,0,e,t).fill({color:0,alpha:.4}).rect(this.crop_bounds.x,this.crop_bounds.y,this.crop_bounds.width,this.crop_bounds.height).cut(),this.image_editor_context.background_image&&(this.image_editor_context.background_image.mask=null),this.image_editor_context.image_container.mask===this.crop_mask&&(this.image_editor_context.image_container.mask=null)}update_crop_ui(){if(!this.crop_mask||!this.crop_ui_container)return;this.crop_ui_container.position.set(this.position.x+this.crop_bounds.x*this.scale,this.position.y+this.crop_bounds.y*this.scale);const e=this.crop_bounds.width*this.scale,t=this.crop_bounds.height*this.scale,i=this.crop_ui_container.getChildAt(0);i.clear().rect(0,0,e,t).stroke({width:1,color:0,alignment:0,alpha:.5}),i.hitArea=new Pe(0,0,e,t),this.update_handle_positions(e,t)}update_handle_positions(e,t){if(!this.crop_ui_container)return;const i=this.crop_ui_container.children.slice(1),r=i.slice(0,4),n=[{x:0,y:0},{x:e,y:0},{x:0,y:t},{x:e,y:t}];r.forEach((a,l)=>{const h=l%2===0?1:-1,c=l<2?1:-1;a.position.set(n[l].x-(h<0?-this.LINE_THICKNESS:this.LINE_THICKNESS),n[l].y-(c<0?-this.LINE_THICKNESS:this.LINE_THICKNESS))}),i.slice(4).forEach((a,l)=>{l<2?a.position.set(e/2-this.CORNER_SIZE*1.5/2,l===0?-this.LINE_THICKNESS:t):a.position.set(l===2?0:e+this.LINE_THICKNESS,t/2-this.CORNER_SIZE*1.5/2)})}setup_event_listeners(){const e=this.image_editor_context.app.stage;e.eventMode="static",e.on("pointermove",this.handle_pointer_move.bind(this)),e.on("pointerup",this.handle_pointer_up.bind(this)),e.on("pointerupoutside",this.handle_pointer_up.bind(this))}cleanup_event_listeners(){const e=this.image_editor_context.app.stage;e.off("pointermove",this.handle_pointer_move.bind(this)),e.off("pointerup",this.handle_pointer_up.bind(this)),e.off("pointerupoutside",this.handle_pointer_up.bind(this))}handle_pointer_move(e){if(this.current_subtool==="crop"){if(this.is_dragging_window&&this.drag_start_position&&this.drag_start_bounds){const t=this.image_editor_context.image_container.toLocal(e.global),i=new fe(t.x-this.drag_start_position.x,t.y-this.drag_start_position.y);this.crop_bounds={x:this.drag_start_bounds.x+i.x,y:this.drag_start_bounds.y+i.y,width:this.drag_start_bounds.width,height:this.drag_start_bounds.height},this.constrain_crop_bounds(),this.update_crop_mask(),this.crop_mask&&this.image_editor_context.background_image&&this.image_editor_context.background_image.setMask(this.crop_mask);return}if(this.is_dragging&&this.selected_handle&&this.last_pointer_position){const t=this.image_editor_context.image_container.toLocal(e.global),i=new fe(t.x,t.y),r=new fe(i.x-this.last_pointer_position.x,i.y-this.last_pointer_position.y);this.update_crop_bounds(r),this.last_pointer_position=i,this.update_crop_mask()}}}handle_pointer_up(){this.current_subtool==="crop"&&(this.is_dragging=!1,this.is_dragging_window=!1,this.selected_handle=null,this.last_pointer_position=null,this.drag_start_position=null,this.drag_start_bounds=null,this.active_corner_index=-1,this.active_edge_index=-1,this.notify("change"))}handle_window_drag_start(e){if(this.current_subtool!=="crop")return;e.stopPropagation(),this.is_dragging_window=!0,this.has_been_manually_changed=!0;const t=this.image_editor_context.image_container.toLocal(e.global);this.drag_start_position=new fe(t.x,t.y),this.drag_start_bounds={...this.crop_bounds}}get crop_manually_changed(){return this.has_been_manually_changed}on(e,t){this.event_callbacks.set(e,[...this.event_callbacks.get(e)||[],t])}off(e,t){var i;this.event_callbacks.set(e,((i=this.event_callbacks.get(e))==null?void 0:i.filter(r=>r!==t))||[])}notify(e){for(const t of this.event_callbacks.get(e)||[])t()}get_crop_bounds(){var i;const{width:e,height:t}=((i=this.image_editor_context.background_image)==null?void 0:i.getLocalBounds())??{width:0,height:0};return{x:this.crop_bounds.x,y:this.crop_bounds.y,crop_dimensions:{width:this.crop_bounds.width,height:this.crop_bounds.height},image_dimensions:{width:e,height:t}}}get_image(){if(!this.image_editor_context.background_image)return Promise.resolve(null);const e=new re,t=new ve(this.image_editor_context.background_image.texture);return e.addChild(t),as(this.image_editor_context.app.renderer,e,this.crop_bounds)}}class om{constructor(e,t,i,r){v(this,"original_width");v(this,"original_height");v(this,"original_x");v(this,"original_y");v(this,"new_width");v(this,"new_height");v(this,"new_x");v(this,"new_y");v(this,"reset_ui");v(this,"name");this.context=e,this.name="Resize",this.original_width=t.width,this.original_height=t.height,this.original_x=t.x,this.original_y=t.y,this.new_width=i.width,this.new_height=i.height,this.new_x=i.x,this.new_y=i.y,this.reset_ui=r}async execute(e){e&&(this.context=e),this.context.background_image&&(this.context.background_image.width=this.new_width,this.context.background_image.height=this.new_height,this.context.background_image.position.set(this.new_x,this.new_y),this.reset_ui())}async undo(){this.context.background_image&&(this.context.background_image.width=this.original_width,this.context.background_image.height=this.original_height,this.context.background_image.position.set(this.original_x,this.original_y),this.reset_ui())}}class am{constructor(){v(this,"name","resize");v(this,"image_editor_context");v(this,"current_tool","image");v(this,"current_subtool","size");v(this,"current_cursor","unset");v(this,"CORNER_SIZE",25);v(this,"LINE_THICKNESS",5);v(this,"HANDLE_COLOR",0);v(this,"HIT_AREA_SIZE",40);v(this,"borderRegion",0);v(this,"is_dragging",!1);v(this,"selected_handle",null);v(this,"active_corner_index",-1);v(this,"active_edge_index",-1);v(this,"last_pointer_position",null);v(this,"resize_ui_container",null);v(this,"dimensions",{width:0,height:0});v(this,"position",{x:0,y:0});v(this,"scale",1);v(this,"is_moving",!1);v(this,"dom_mousedown_handler",null);v(this,"dom_mousemove_handler",null);v(this,"dom_mouseup_handler",null);v(this,"event_callbacks",new Map);v(this,"last_scale",1);v(this,"original_state",null)}async setup(e,t,i){if(this.image_editor_context=e,this.current_tool=t,this.current_subtool=i,e.background_image)if(e.background_image.borderRegion!==void 0)this.borderRegion=e.background_image.borderRegion;else{const r=e.background_image,n=e.image_container,o=Math.abs(r.position.x-(n.width-r.width)/2)<2,a=Math.abs(r.position.y-(n.height-r.height)/2)<2;(!o||!a)&&(this.borderRegion=Math.max(r.position.x,r.position.y),e.background_image.borderRegion=this.borderRegion)}if(e.dimensions.subscribe(r=>{this.dimensions=r,this.resize_ui_container&&this.update_resize_ui()}),e.position.subscribe(r=>{this.position=r,this.resize_ui_container&&this.current_tool==="image"&&this.current_subtool==="size"&&this.update_resize_ui()}),e.scale.subscribe(r=>{this.scale=r,this.resize_ui_container&&this.current_tool==="image"&&this.current_subtool==="size"&&this.update_resize_ui()}),await this.init_resize_ui(),this.setup_event_listeners(),this.current_subtool==="size"&&this.image_editor_context.background_image){const r=this.image_editor_context.background_image,n=this.image_editor_context.image_container,o=r.width,a=r.height,l=n.getLocalBounds(),h=l.width,c=l.height,u=-(o-h),f=-(a-c),d=0,_=0,g=o>h&&(r.position.x<u||r.position.x>d),m=a>c&&(r.position.y<f||r.position.y>_);(g||m)&&this.apply_boundary_constraints()}}cleanup(){this.resize_ui_container&&this.image_editor_context.app.stage.removeChild(this.resize_ui_container),this.cleanup_event_listeners()}set_tool(e,t){if(this.current_tool=e,this.current_subtool=t,e==="image"&&t==="size"){if(this.image_editor_context.background_image){const i=this.image_editor_context.background_image.borderRegion;typeof i=="number"&&i>0&&(this.borderRegion=i)}if(this.show_resize_ui(),this.image_editor_context.background_image){const i=this.image_editor_context.background_image,r=this.image_editor_context.image_container,n=i.width,o=i.height,a=r.getLocalBounds(),l=a.width,h=a.height,c=l-this.borderRegion*2,u=h-this.borderRegion*2,f=n>c?-(n-c)+this.borderRegion:this.borderRegion,d=n>c?this.borderRegion:l-n-this.borderRegion,_=o>u?-(o-u)+this.borderRegion:this.borderRegion,g=o>u?this.borderRegion:h-o-this.borderRegion,m=i.position.x<f||i.position.x>d,p=i.position.y<_||i.position.y>g;(m||p)&&this.apply_boundary_constraints()}}else this.hide_resize_ui()}async init_resize_ui(){this.resize_ui_container=this.make_resize_ui(this.dimensions.width*this.scale,this.dimensions.height*this.scale),this.resize_ui_container.position.set(this.position.x,this.position.y),this.resize_ui_container.visible=!1,this.image_editor_context.app.stage.addChild(this.resize_ui_container)}make_resize_ui(e,t){const i=new re;i.eventMode="static",i.interactiveChildren=!0;const r=new Ue().rect(0,0,e,t).stroke({width:1,color:0,alignment:0,alpha:.3}),n=new Ue;n.rect(0,0,e,t).stroke({width:1,color:0,alpha:.7,pixelLine:!0});const o=5,l=o+5;for(let c=0;c<e;c+=l*2)n.rect(c,0,Math.min(o,e-c),1).fill(0),n.rect(c,t-1,Math.min(o,e-c),1).fill(0);for(let c=0;c<t;c+=l*2)n.rect(0,c,1,Math.min(o,t-c)).fill(0),n.rect(e-1,c,1,Math.min(o,t-c)).fill(0);i.addChild(r),i.addChild(n);const h=new Ue().rect(0,0,e,t).fill(16777215,0);return h.eventMode="static",h.cursor="move",i.addChild(h),this.create_corner_handles(i,e,t),this.create_edge_handles(i,e,t),i}create_handle(e=!1){const t=new re;t.eventMode="static",t.cursor="pointer";const i=new Ue,r=e?8:10;i.rect(-r/2,-r/2,r,r).fill(16777215).stroke({width:1,color:this.HANDLE_COLOR}),t.addChild(i);const n=e?this.HIT_AREA_SIZE*1.5:this.HIT_AREA_SIZE;return t.hitArea=new Pe(-n/2,-n/2,n,n),t}create_edge_handles(e,t,i){[-1,1].forEach((r,n)=>{const o=this.create_handle(!0);o.rotation=0,o.on("pointerdown",a=>{this.handle_pointer_down(a,o,-1,n)}),o.x=t/2,o.y=r<0?0:i,o.cursor="ns-resize",e.addChild(o)}),[-1,1].forEach((r,n)=>{const o=this.create_handle(!0);o.rotation=0,o.on("pointerdown",a=>{this.handle_pointer_down(a,o,-1,n+2)}),o.x=r<0?0:t,o.y=i/2,o.cursor="ew-resize",e.addChild(o)})}create_corner_handles(e,t,i){[{x:0,y:0,cursor:"nwse-resize"},{x:t,y:0,cursor:"nesw-resize"},{x:0,y:i,cursor:"nesw-resize"},{x:t,y:i,cursor:"nwse-resize"}].forEach(({x:n,y:o,cursor:a},l)=>{const h=this.create_handle(!1);h.x=n,h.y=o,h.on("pointerdown",c=>{this.handle_pointer_down(c,h,l,-1)}),h.cursor=a,e.addChild(h)})}handle_pointer_down(e,t,i,r){if(this.current_subtool!=="size")return;e.stopPropagation(),this.is_dragging=!0,this.selected_handle=t,this.active_corner_index=i,this.active_edge_index=r;const n=this.image_editor_context.image_container.toLocal(e.global);if(this.last_pointer_position=new fe(n.x,n.y),this.image_editor_context.background_image){const o=this.image_editor_context.background_image;this.original_state={width:o.width,height:o.height,x:o.position.x,y:o.position.y}}}maintain_aspect_ratio(e,t,i,r){const n=Math.abs(r.x),o=Math.abs(r.y);return n>o*1.2?t=e/i:o>n*1.2?e=t*i:e/i>t?t=e/i:e=t*i,{width:e,height:t}}limit_dimensions(e,t,i){const r=this.dimensions.width,n=this.dimensions.height;let o=e,a=t;return o>r&&(i&&this.active_corner_index!==-1&&(a=r/o*a),o=r),a>n&&(i&&this.active_corner_index!==-1&&(o=n/a*o),a=n),o=Math.max(20,o),a=Math.max(20,a),{width:o,height:a}}calculate_position_deltas(e,t,i,r){let n=0,o=0;return this.active_corner_index===0||this.active_edge_index===2?n=e-i:this.active_corner_index===2&&(n=e-i,o=0),(this.active_corner_index===0||this.active_corner_index===1||this.active_edge_index===0)&&(o=t-r),{x:n,y:o}}apply_boundary_constraints(){if(!this.image_editor_context.background_image)return;const e=this.image_editor_context.background_image,t=this.image_editor_context.image_container;let i=e.position.x,r=e.position.y;const n=t.width,o=t.height,a=e.width,l=e.height,h=n-this.borderRegion*2,c=o-this.borderRegion*2;if(a>h){const u=-(a-h)+this.borderRegion,f=this.borderRegion;i=Math.max(u,Math.min(f,i))}else{const u=this.borderRegion,f=n-a-this.borderRegion;(i<u||i>f)&&(i=Math.max(u,Math.min(f,i)))}if(l>c){const u=-(l-c)+this.borderRegion,f=this.borderRegion;r=Math.max(u,Math.min(f,r))}else{const u=this.borderRegion,f=o-l-this.borderRegion;(r<u||r>f)&&(r=Math.max(u,Math.min(f,r)))}if(e.position.set(i,r),this.resize_ui_container){const u=t.position.x+i*this.scale,f=t.position.y+r*this.scale;this.resize_ui_container.position.set(u,f)}}update_resize_bounds(e,t=!1){const i=this.image_editor_context.background_image;if(!i)return;const r=i.width,n=i.height,o=i.position.x,a=i.position.y,l=r/n;let h;if(this.active_corner_index!==-1){switch(this.active_corner_index){case 0:h=new fe(o-e.x,a-e.y);break;case 1:h=new fe(o+r+e.x,a-e.y);break;case 2:h=new fe(o-e.x,a+n+e.y);break;case 3:h=new fe(o+r+e.x,a+n+e.y);break;default:return}const c=this.handle_direct_corner_resize(h,r,n,o,a,l,t);i.width=c.width,i.height=c.height,i.position.set(c.x,c.y)}else if(this.active_edge_index!==-1){switch(this.active_edge_index){case 0:h=new fe(o+r/2,a-e.y);break;case 1:h=new fe(o+r/2,a+n+e.y);break;case 2:h=new fe(o-e.x,a+n/2);break;case 3:h=new fe(o+r+e.x,a+n/2);break;default:return}const c=this.handle_direct_edge_resize(h,r,n,o,a);i.width=c.width,i.height=c.height,i.position.set(c.x,c.y)}this.update_resize_ui()}update_resize_ui(){if(!this.resize_ui_container||!this.image_editor_context.background_image||!this.image_editor_context.background_image.position)return;const e=this.image_editor_context.background_image,i=this.image_editor_context.image_container.getGlobalPosition(),r=e.position.x*this.scale,n=e.position.y*this.scale;this.resize_ui_container.position.set(i.x+r,i.y+n);const o=e.width*this.scale,a=e.height*this.scale;this.resize_ui_container.getChildAt(0).clear().rect(0,0,o,a).stroke({width:1,color:0,alignment:0,alpha:.3});const h=this.resize_ui_container.getChildAt(1);if(h.clear(),h.rect(0,0,o,a).stroke({width:1,color:0,alpha:.7,pixelLine:!0}),!this.is_dragging&&!this.is_moving&&Math.abs(this.scale-this.last_scale)<.001){for(let _=0;_<o;_+=10*2)h.rect(_,0,Math.min(5,o-_),1).fill(0),h.rect(_,a-1,Math.min(5,o-_),1).fill(0);for(let _=0;_<a;_+=10*2)h.rect(0,_,1,Math.min(5,a-_)).fill(0),h.rect(o-1,_,1,Math.min(5,a-_)).fill(0)}this.last_scale=this.scale,this.resize_ui_container.getChildAt(2).clear().rect(0,0,o,a).fill(16777215,0),this.update_handle_positions(o,a)}update_handle_positions(e,t){if(!this.resize_ui_container)return;const i=this.resize_ui_container.children.slice(3);this.resize_ui_container.getChildAt(2).clear().rect(0,0,e,t).fill(16777215,0);const n=i.slice(0,4),o=[{x:0,y:0},{x:e,y:0},{x:0,y:t},{x:e,y:t}];n.forEach((l,h)=>{l.position.set(o[h].x,o[h].y)}),i.slice(4).forEach((l,h)=>{h<2?l.position.set(e/2,h===0?0:t):l.position.set(h===2?0:e,t/2)})}setup_event_listeners(){const e=this.image_editor_context.app.stage;e.eventMode="static",e.on("pointermove",this.handle_pointer_move.bind(this)),e.on("pointerup",this.handle_pointer_up.bind(this)),e.on("pointerupoutside",this.handle_pointer_up.bind(this)),this.setup_dom_event_listeners()}setup_dom_event_listeners(){this.cleanup_dom_event_listeners();const e=this.image_editor_context.app.canvas;this.dom_mousedown_handler=t=>{if(this.current_subtool!=="size"||this.is_dragging||this.selected_handle||!this.image_editor_context.background_image)return;const i=this.image_editor_context.background_image,n=this.image_editor_context.image_container.getBounds(),o=e.getBoundingClientRect(),a=t.clientX-o.left,l=t.clientY-o.top,h=(a-n.x)/this.scale,c=(l-n.y)/this.scale;h>=i.position.x&&h<=i.position.x+i.width&&c>=i.position.y&&c<=i.position.y+i.height&&(this.is_moving=!0,this.last_pointer_position=new fe(h,c),this.original_state={width:i.width,height:i.height,x:i.position.x,y:i.position.y},t.preventDefault(),t.stopPropagation())},this.dom_mousemove_handler=t=>{if(this.current_subtool!=="size"||!this.is_moving||!this.last_pointer_position||!this.image_editor_context.background_image)return;const r=this.image_editor_context.image_container.getBounds(),n=e.getBoundingClientRect(),o=t.clientX-n.left,a=t.clientY-n.top,l=(o-r.x)/this.scale,h=(a-r.y)/this.scale,c=new fe(l,h);this.handle_image_dragging(c),this.last_pointer_position=c,t.preventDefault(),t.stopPropagation()},this.dom_mouseup_handler=t=>{if(this.current_subtool==="size"){if(this.original_state&&this.image_editor_context.background_image){const i=this.image_editor_context.background_image,r={width:i.width,height:i.height,x:i.position.x,y:i.position.y};if(this.original_state.width!==r.width||this.original_state.height!==r.height||this.original_state.x!==r.x||this.original_state.y!==r.y){const n=new om(this.image_editor_context,this.original_state,r,this.update_resize_ui.bind(this));this.image_editor_context.execute_command(n)}this.original_state=null}this.is_moving=!1,this.last_pointer_position=null,this.update_resize_ui(),t.preventDefault(),t.stopPropagation()}},e.addEventListener("mousedown",this.dom_mousedown_handler),window.addEventListener("mousemove",this.dom_mousemove_handler),window.addEventListener("mouseup",this.dom_mouseup_handler)}cleanup_dom_event_listeners(){const e=this.image_editor_context.app.canvas;this.dom_mousedown_handler&&(e.removeEventListener("mousedown",this.dom_mousedown_handler),this.dom_mousedown_handler=null),this.dom_mousemove_handler&&(window.removeEventListener("mousemove",this.dom_mousemove_handler),this.dom_mousemove_handler=null),this.dom_mouseup_handler&&(window.removeEventListener("mouseup",this.dom_mouseup_handler),this.dom_mouseup_handler=null)}cleanup_event_listeners(){const e=this.image_editor_context.app.stage;e.off("pointermove",this.handle_pointer_move.bind(this)),e.off("pointerup",this.handle_pointer_up.bind(this)),e.off("pointerupoutside",this.handle_pointer_up.bind(this)),this.cleanup_dom_event_listeners()}handle_pointer_move(e){if(this.current_subtool!=="size")return;const t=this.image_editor_context.image_container.toLocal(e.global),i=new fe(t.x,t.y);if(this.is_moving&&this.last_pointer_position)this.handle_image_dragging(i),this.last_pointer_position=i;else if(this.is_dragging&&this.selected_handle){if(!this.image_editor_context.background_image)return;const r=this.image_editor_context.background_image,n=this.active_corner_index!==-1&&!e.shiftKey,o=r.width,a=r.height,l=r.position.x,h=r.position.y,c=o/a;let u;if(this.active_corner_index!==-1)u=this.handle_direct_corner_resize(i,o,a,l,h,c,n);else if(this.active_edge_index!==-1)u=this.handle_direct_edge_resize(i,o,a,l,h);else return;u=this.limit_dimensions_to_container(u,l,h,o,a,n),r.width=u.width,r.height=u.height,r.position.set(u.x,u.y),this.update_resize_ui(),this.last_pointer_position=i}}handle_pointer_up(){this.current_subtool==="size"&&(this.is_dragging=!1,this.is_moving=!1,this.selected_handle=null,this.last_pointer_position=null,this.active_corner_index=-1,this.active_edge_index=-1,this.update_resize_ui(),this.notify("change"))}handle_image_dragging(e){if(!this.last_pointer_position||!this.image_editor_context.background_image)return;const t=this.image_editor_context.background_image,i=this.image_editor_context.image_container.getLocalBounds(),r=e.x-this.last_pointer_position.x,n=e.y-this.last_pointer_position.y;let o=t.position.x+r,a=t.position.y+n;const l=i.width,h=i.height,c=t.width,u=t.height,f=l-this.borderRegion*2,d=h-this.borderRegion*2;if(c>f){const _=-(c-f)+this.borderRegion,g=this.borderRegion;o=Math.max(_,Math.min(g,o))}else{const _=this.borderRegion,g=l-c-this.borderRegion;o=Math.max(_,Math.min(g,o))}if(u>d){const _=-(u-d)+this.borderRegion,g=this.borderRegion;a=Math.max(_,Math.min(g,a))}else{const _=this.borderRegion,g=h-u-this.borderRegion;a=Math.max(_,Math.min(g,a))}t.position.set(o,a),this.update_resize_ui()}handle_direct_corner_resize(e,t,i,r,n,o,a){let l=0,h=0,c=r,u=n;switch(this.active_corner_index){case 0:l=r+t-e.x,h=n+i-e.y;break;case 1:l=e.x-r,h=n+i-e.y;break;case 2:l=r+t-e.x,h=e.y-n;break;case 3:l=e.x-r,h=e.y-n;break}l=Math.max(20,l),h=Math.max(20,h);let f=l,d=h;if(a){const _={x:e.x-(r+t/2),y:e.y-(n+i/2)},g=Math.abs(Math.atan2(_.y,_.x));g<Math.PI/4||g>3*Math.PI/4?d=f/o:f=d*o}switch(this.active_corner_index){case 0:c=r+t-f,u=n+i-d;break;case 1:u=n+i-d;break;case 2:c=r+t-f;break}return{width:f,height:d,x:c,y:u}}handle_direct_edge_resize(e,t,i,r,n){let o=t,a=i,l=r,h=n;switch(this.active_edge_index){case 0:a=Math.max(20,n+i-e.y),h=e.y;break;case 1:a=Math.max(20,e.y-n);break;case 2:o=Math.max(20,r+t-e.x),l=e.x;break;case 3:o=Math.max(20,e.x-r);break}return{width:o,height:a,x:l,y:h}}limit_dimensions_to_container(e,t,i,r,n,o){let{width:a,height:l,x:h,y:c}=e;const u=this.dimensions.width-this.borderRegion*2,f=this.dimensions.height-this.borderRegion*2,d=a,_=l,g=this.apply_size_limits(a,l,u,f,o,d/_);a=g.width,l=g.height;const m=this.adjust_position_for_resizing(a,l,t,i,r,n);return h=m.x,c=m.y,this.apply_border_constraints(a,l,h,c,o,d/_)}apply_size_limits(e,t,i,r,n,o){let a=e,l=t;const h=a>i,c=l>r;if(h&&c){const u=i/a,f=r/l;u<f?(a=i,n&&(l=a/o)):(l=r,n&&(a=l*o))}else h?(a=i,n&&(l=a/o)):c&&(l=r,n&&(a=l*o));return{width:a,height:l}}adjust_position_for_resizing(e,t,i,r,n,o){let a=i,l=r;switch(this.active_corner_index){case 0:a=i+n-e,l=r+o-t;break;case 1:l=r+o-t;break;case 2:a=i+n-e;break}if(this.active_edge_index!==-1)switch(this.active_edge_index){case 0:l=r+o-t;break;case 2:a=i+n-e;break}return{x:a,y:l}}apply_border_constraints(e,t,i,r,n,o){const a=this.apply_top_left_constraints(e,t,i,r,n,o),l=this.apply_bottom_right_constraints(a.width,a.height,a.x,a.y,n,o);return l.width=Math.max(20,l.width),l.height=Math.max(20,l.height),l}apply_top_left_constraints(e,t,i,r,n,o){let a=e,l=t,h=i,c=r;return h<this.borderRegion&&((this.active_corner_index===0||this.active_corner_index===2||this.active_edge_index===2)&&(a-=this.borderRegion-h,n&&(l=a/o)),h=this.borderRegion),c<this.borderRegion&&((this.active_corner_index===0||this.active_corner_index===1||this.active_edge_index===0)&&(l-=this.borderRegion-c,n&&(a=l*o)),c=this.borderRegion),{width:a,height:l,x:h,y:c}}apply_bottom_right_constraints(e,t,i,r,n,o){let a=e,l=t,h=i,c=r;if(h+a>this.dimensions.width-this.borderRegion)if(this.active_corner_index===1||this.active_corner_index===3||this.active_edge_index===3)a=this.dimensions.width-this.borderRegion-h,n&&(l=a/o);else{const f=h+a-(this.dimensions.width-this.borderRegion);h=Math.max(this.borderRegion,h-f)}if(c+l>this.dimensions.height-this.borderRegion)if(this.active_corner_index===2||this.active_corner_index===3||this.active_edge_index===1)l=this.dimensions.height-this.borderRegion-c,n&&(a=l*o);else{const f=c+l-(this.dimensions.height-this.borderRegion);c=Math.max(this.borderRegion,c-f)}return{width:a,height:l,x:h,y:c}}show_resize_ui(){if(this.resize_ui_container&&(this.resize_ui_container.visible=!0,this.update_resize_ui()),this.image_editor_context.background_image){const e=this.image_editor_context.background_image;this.current_cursor=e.cursor,e.cursor="move"}this.setup_dom_event_listeners()}hide_resize_ui(){if(this.resize_ui_container&&(this.resize_ui_container.visible=!1),this.image_editor_context.background_image){const e=this.image_editor_context.background_image;e.cursor=this.current_cursor}this.cleanup_dom_event_listeners()}on(e,t){this.event_callbacks.set(e,[...this.event_callbacks.get(e)||[],t])}off(e,t){var i;this.event_callbacks.set(e,((i=this.event_callbacks.get(e))==null?void 0:i.filter(r=>r!==t))||[])}notify(e){for(const t of this.event_callbacks.get(e)||[])t()}set_border_region(e){var t,i;this.borderRegion=e,(t=this.image_editor_context)!=null&&t.background_image&&(this.image_editor_context.background_image.borderRegion=e),this.resize_ui_container&&this.update_resize_ui(),this.current_subtool==="size"&&((i=this.image_editor_context)!=null&&i.background_image)&&this.apply_boundary_constraints()}}function lm(s,e){(s instanceof re||"cursor"in s)&&(s.cursor=e)}function sh(s,e){for(const t of s)lm(t,e),t instanceof re&&t.children.length>0&&sh(t.children,e)}function hm(s){return s!==null&&window.clearTimeout(s),null}class cm{constructor(e,t,i){v(this,"cursor_graphics",null);v(this,"cursor_container",null);v(this,"brush_preview_container",null);v(this,"brush_preview_graphics",null);v(this,"is_preview_visible",!1);v(this,"is_cursor_over_image",!1);v(this,"cursor_position_check_timeout",null);v(this,"is_brush_or_erase_active",!1);v(this,"_bound_update_cursor",null);v(this,"_bound_check_cursor_over_image",null);this.image_editor_context=e,this.state=t,this.scale=i,this.initialize_cursor(),this.initialize_brush_preview()}is_over_image(){return this.is_cursor_over_image}setup_event_listeners(){this.cleanup_event_listeners(),this._bound_update_cursor=this.update_cursor_position.bind(this),this._bound_check_cursor_over_image=this.check_cursor_over_image.bind(this);const e=this.image_editor_context.app.stage;e.on("pointermove",this._bound_update_cursor),e.on("pointermove",this._bound_check_cursor_over_image),this.image_editor_context.image_container.on("pointerenter",this.on_image_container_pointer_enter.bind(this)),this.image_editor_context.image_container.on("pointerleave",this.on_image_container_pointer_leave.bind(this))}cleanup_event_listeners(){const e=this.image_editor_context.app.stage;this._bound_update_cursor&&(e.off("pointermove",this._bound_update_cursor),this._bound_update_cursor=null),this._bound_check_cursor_over_image&&(e.off("pointermove",this._bound_check_cursor_over_image),this._bound_check_cursor_over_image=null),this.image_editor_context.image_container.off("pointerenter"),this.image_editor_context.image_container.off("pointerleave"),this.cursor_position_check_timeout=hm(this.cursor_position_check_timeout)}initialize_cursor(){this.cursor_container&&(this.cursor_container.parent&&this.cursor_container.parent.removeChild(this.cursor_container),this.cursor_container.destroy({children:!0}),this.cursor_container=null),this.cursor_container=new re,this.image_editor_context.ui_container.addChild(this.cursor_container),this.cursor_graphics=new Ue,this.cursor_container.addChild(this.cursor_graphics),this.update_cursor_appearance()}update_cursor_appearance(){if(!this.cursor_graphics)return;this.cursor_graphics.clear();const e=this.state.mode==="draw"?ft(this.state.color).toString():16777215;this.cursor_graphics.circle(0,0,this.state.brush_size*this.scale).stroke({width:1.5,color:e,alpha:.8}),this.cursor_graphics.circle(0,0,1).fill({color:e,alpha:.8})}update_cursor_position(e){if(!this.cursor_container)return;const t=this.image_editor_context.image_container.toLocal(e.global),i=this.image_editor_context.ui_container.toLocal(this.image_editor_context.image_container.toGlobal(t));this.cursor_container.position.set(i.x,i.y)}check_cursor_over_image(e){this.cursor_position_check_timeout!==null&&(window.clearTimeout(this.cursor_position_check_timeout),this.cursor_position_check_timeout=null);const i=this.image_editor_context.image_container.getBounds(),r=this.is_cursor_over_image;this.is_cursor_over_image=e.global.x>=i.x&&e.global.x<=i.x+i.width&&e.global.y>=i.y&&e.global.y<=i.y+i.height,r!==this.is_cursor_over_image&&this.update_cursor_and_preview_visibility()}update_cursor_size(){this.update_cursor_appearance()}set_active(e){this.is_brush_or_erase_active=e,this.update_cursor_and_preview_visibility()}update_cursor_visibility(){this.cursor_container&&(this.cursor_container.visible=this.is_cursor_over_image&&this.is_brush_or_erase_active)}preview_brush(e){this.is_preview_visible=e,this.brush_preview_container&&(this.brush_preview_container.visible=e&&this.is_brush_or_erase_active),e&&(this.update_brush_preview(),this.update_brush_preview_position())}initialize_brush_preview(){this.brush_preview_container&&(this.brush_preview_container.parent&&this.brush_preview_container.parent.removeChild(this.brush_preview_container),this.brush_preview_container.destroy({children:!0}),this.brush_preview_container=null),this.brush_preview_container=new re,this.image_editor_context.ui_container.addChild(this.brush_preview_container),this.brush_preview_graphics=new Ue,this.brush_preview_container.addChild(this.brush_preview_graphics),this.brush_preview_container.visible=!1}update_brush_preview(){if(!this.brush_preview_graphics)return;this.brush_preview_graphics.clear();const e=this.state.brush_size*this.scale,t=this.state.mode==="draw"?ft(this.state.color).setAlpha(this.state.opacity).toString():16777215;this.brush_preview_graphics.circle(0,0,e).fill({color:t,alpha:this.state.mode==="draw"?this.state.opacity:.3}),this.brush_preview_graphics.circle(0,0,e+1).stroke({width:1,color:0,alpha:.5})}update_brush_preview_position(){if(!this.brush_preview_container)return;const e=this.image_editor_context.image_container,t=e.width/2,i=e.height/2,r=e.toGlobal({x:t,y:i}),n=this.image_editor_context.ui_container.toLocal(r);this.brush_preview_container.position.set(n.x,n.y)}on_image_container_pointer_enter(){this.is_cursor_over_image=!0,this.update_cursor_and_preview_visibility()}on_image_container_pointer_leave(){this.is_cursor_over_image=!1,this.update_cursor_and_preview_visibility()}update_cursor_and_preview_visibility(){this.update_cursor_visibility(),this.brush_preview_container&&(this.brush_preview_container.visible=this.is_preview_visible&&this.is_brush_or_erase_active)}cleanup(){this.is_brush_or_erase_active=!1,this.cursor_container&&(this.cursor_container.parent&&this.cursor_container.parent.removeChild(this.cursor_container),this.cursor_container.destroy({children:!0}),this.cursor_container=null),this.brush_preview_container&&(this.brush_preview_container.parent&&this.brush_preview_container.parent.removeChild(this.brush_preview_container),this.brush_preview_container.destroy({children:!0}),this.brush_preview_container=null),this.cursor_position_check_timeout!==null&&(window.clearTimeout(this.cursor_position_check_timeout),this.cursor_position_check_timeout=null)}update_state(e,t){this.state=e,this.scale=t,this.update_cursor_appearance(),this.is_preview_visible&&this.update_brush_preview()}}class um{constructor(e,t,i){v(this,"stroke_data");v(this,"context");v(this,"original_texture",null);v(this,"name");this.name="Draw",this.stroke_data=t,this.context=e,i&&(this.original_texture=this.create_texture_from(i))}create_texture_from(e){const t=it.create({width:e.width,height:e.height,resolution:window.devicePixelRatio||1}),i=new ve(e),r=new re;return r.addChild(i),this.context.app.renderer.render(r,{renderTexture:t}),r.destroy({children:!0}),t}render_stroke_from_data(e,t){const i=e.segments.filter(n=>n.mode==="draw"),r=e.segments.filter(n=>n.mode==="erase");if(i.length>0){const n=new Ue,o=new re;o.addChild(n);let a=1;for(const c of i){c.opacity<a&&(a=c.opacity);let u=16777215;c.color.startsWith("#")&&(u=parseInt(c.color.replace("#","0x"),16)),n.setFillStyle({color:u,alpha:1}),this.render_segment_to_graphics(n,c)}const l=it.create({width:t.width,height:t.height,resolution:window.devicePixelRatio||1}),h=new ve(l);this.context.app.renderer.render({container:o,target:l}),h.alpha=a,this.context.app.renderer.render({container:h,target:t,clear:!1}),o.destroy({children:!0}),h.destroy(),l.destroy()}if(r.length>0){const n=it.create({width:t.width,height:t.height,resolution:window.devicePixelRatio||1}),o=new ve(t),a=new re;a.addChild(o),this.context.app.renderer.render({container:a,target:n,clear:!0});const l=new Ue,h=new re;h.addChild(l),l.setFillStyle({color:16777215,alpha:1});for(const _ of r)this.render_segment_to_graphics(l,_);const c=it.create({width:t.width,height:t.height,resolution:window.devicePixelRatio||1});this.context.app.renderer.render({container:h,target:c,clear:!0});const u=new ve(n),f=new ve(c),d=new re;d.addChild(u),d.setMask({mask:f,inverse:!0}),this.context.app.renderer.render({container:d,target:t,clear:!0}),a.destroy({children:!0}),h.destroy({children:!0}),d.destroy({children:!0}),n.destroy(),c.destroy()}}render_segment_to_graphics(e,t){const i=Math.sqrt(Math.pow(t.to_x-t.from_x,2)+Math.pow(t.to_y-t.from_y,2));if(i<.1)e.circle(t.from_x,t.from_y,t.size).fill();else{const r=Math.max(t.size/3,2),n=Math.max(Math.ceil(i/r),2);for(let o=0;o<n;o++){const a=o/(n-1),l=t.from_x+(t.to_x-t.from_x)*a,h=t.from_y+(t.to_y-t.from_y)*a;e.circle(l,h,t.size).fill()}}}async execute(e){e&&(this.context=e);let t=this.context.layer_manager.get_layer_textures(this.stroke_data.layer_id);if(!t){const l=this.context.layer_manager.get_layers(),h=l[l.length-1];t=this.context.layer_manager.get_layer_textures(h.id)}if(!t)return;const i=it.create({width:t.draw.width,height:t.draw.height,resolution:window.devicePixelRatio||1}),r=new ve(t.draw),n=new re;n.addChild(r),this.context.app.renderer.render({container:n,target:i,clear:!0}),n.destroy({children:!0}),this.render_stroke_from_data(this.stroke_data,i);const o=new ve(i),a=new re;a.addChild(o),this.context.app.renderer.render({container:a,target:t.draw,clear:!0}),a.destroy({children:!0}),i.destroy()}async undo(){if(!this.original_texture)return;const e=this.context.layer_manager.get_layer_textures(this.stroke_data.layer_id);if(!e)return;const t=new ve(this.original_texture),i=new re;i.addChild(t),this.context.app.renderer.render({container:i,target:e.draw,clear:!0}),i.destroy({children:!0})}}class dm{constructor(e,t){v(this,"stroke_texture",null);v(this,"erase_texture",null);v(this,"display_container",null);v(this,"stroke_container",null);v(this,"stroke_graphics",null);v(this,"preview_sprite",null);v(this,"erase_graphics",null);v(this,"dimensions");v(this,"image_editor_context");v(this,"app");v(this,"is_new_stroke",!0);v(this,"current_opacity",1);v(this,"original_layer_texture",null);v(this,"active_layer_id",null);v(this,"current_stroke_segments",[]);this.image_editor_context=e,this.app=t,this.dimensions={width:this.image_editor_context.image_container.width,height:this.image_editor_context.image_container.height}}initialize_textures(){this.cleanup_textures();const e=this.image_editor_context.image_container.getLocalBounds();this.dimensions={width:e.width,height:e.height},this.stroke_texture=it.create({width:this.dimensions.width,height:this.dimensions.height,resolution:window.devicePixelRatio||1}),this.erase_texture=it.create({width:this.dimensions.width,height:this.dimensions.height,resolution:window.devicePixelRatio||1}),this.display_container=new re,this.image_editor_context.image_container.addChild(this.display_container),this.stroke_container=new re,this.stroke_graphics=new Ue,this.stroke_container.addChild(this.stroke_graphics),this.erase_graphics=new Ue,this.preview_sprite=new ve(this.stroke_texture),this.preview_sprite.alpha=0,this.display_container.addChild(this.preview_sprite);const t=new re;this.app.renderer.render(t,{renderTexture:this.stroke_texture}),this.app.renderer.render(t,{renderTexture:this.erase_texture}),t.destroy(),this.is_new_stroke=!0,this.current_opacity=1}reinitialize(){(this.image_editor_context.image_container.width!==this.dimensions.width||this.image_editor_context.image_container.height!==this.dimensions.height)&&this.initialize_textures()}cleanup_textures(){this.stroke_texture&&(this.stroke_texture.destroy(),this.stroke_texture=null),this.erase_texture&&(this.erase_texture.destroy(),this.erase_texture=null),this.display_container&&(this.display_container.parent&&this.display_container.parent.removeChild(this.display_container),this.display_container.destroy({children:!0}),this.display_container=null),this.original_layer_texture&&(this.original_layer_texture.destroy(),this.original_layer_texture=null),this.stroke_container=null,this.stroke_graphics=null,this.preview_sprite=null,this.erase_graphics=null,this.active_layer_id=null}preserve_canvas_state(){const e=this.image_editor_context.layer_manager.get_active_layer();if(!e)return;const i=this.image_editor_context.layer_manager.get_layers().find(a=>a.container===e);if(!i)return;this.active_layer_id=i.id;const r=this.image_editor_context.layer_manager.get_layer_textures(i.id);if(!r)return;this.original_layer_texture&&this.original_layer_texture.destroy(),this.original_layer_texture=it.create({width:this.dimensions.width,height:this.dimensions.height,resolution:window.devicePixelRatio||1});const n=new ve(r.draw),o=new re;o.addChild(n),this.app.renderer.render(o,{renderTexture:this.original_layer_texture}),o.destroy({children:!0}),this.is_new_stroke=!0}reset_eraser_mask(){!this.erase_graphics||!this.erase_texture||(this.erase_graphics.clear(),this.erase_graphics.setFillStyle({color:16777215,alpha:1}),this.erase_graphics.rect(0,0,this.dimensions.width,this.dimensions.height).fill(),this.erase_graphics.endFill(),this.app.renderer.render(this.erase_graphics,{renderTexture:this.erase_texture}))}commit_stroke(){if(!this.stroke_texture||!this.preview_sprite||!this.stroke_graphics||!this.original_layer_texture||!this.active_layer_id)return;this.preview_sprite.visible=!1;const e=this.image_editor_context.layer_manager.get_active_layer();if(!e)return;const i=this.image_editor_context.layer_manager.get_layers().find(l=>l.container===e);if(!i||i.id!==this.active_layer_id||!this.image_editor_context.layer_manager.get_layer_textures(i.id))return;const n={segments:[...this.current_stroke_segments],layer_id:this.active_layer_id},o=new um(this.image_editor_context,n,this.original_layer_texture);this.stroke_graphics&&this.stroke_graphics.clear();const a=new re;this.app.renderer.render(a,{renderTexture:this.stroke_texture}),a.destroy(),this.is_new_stroke=!0,this.original_layer_texture=null,this.active_layer_id=null,this.current_stroke_segments=[],this.image_editor_context.command_manager.execute(o,this.image_editor_context)}calculate_distance(e,t,i,r){return Math.sqrt(Math.pow(i-e,2)+Math.pow(r-t,2))}draw_segment(e,t,i,r,n,o,a,l){if(!this.stroke_graphics||!this.stroke_texture||!this.stroke_container||!this.preview_sprite)return;this.is_new_stroke&&!this.original_layer_texture&&this.preserve_canvas_state(),this.current_opacity=l==="draw"?Math.min(Math.max(a,0),1):.5;const h=n;if(this.is_new_stroke){this.stroke_graphics.clear(),this.current_stroke_segments=[];const u=new re;this.app.renderer.render(u,{renderTexture:this.stroke_texture}),u.destroy(),this.is_new_stroke=!1}if(this.current_stroke_segments.push({from_x:e,from_y:t,to_x:i,to_y:r,size:h,color:o,opacity:this.current_opacity,mode:l}),l==="draw"){let u=16777215;try{o.startsWith("#")&&(u=parseInt(o.replace("#","0x"),16))}catch{u=16777215}this.stroke_graphics.setFillStyle({color:u,alpha:1})}else this.stroke_graphics.setFillStyle({color:16777215,alpha:1});const c=this.calculate_distance(e,t,i,r);if(c<.1)this.stroke_graphics.circle(e,t,h).fill();else{const u=Math.max(h/3,2),f=Math.max(Math.ceil(c/u),2);for(let d=0;d<f;d++){const _=d/(f-1),g=e+(i-e)*_,m=t+(r-t)*_;this.stroke_graphics.circle(g,m,h).fill()}}if(this.stroke_graphics.endFill(),this.app.renderer.render(this.stroke_container,{renderTexture:this.stroke_texture}),l==="draw")this.preview_sprite.texture=this.stroke_texture,this.preview_sprite.alpha=this.current_opacity,this.preview_sprite.tint=16777215;else{const u=this.image_editor_context.layer_manager.get_active_layer();if(!u)return;const d=this.image_editor_context.layer_manager.get_layers().find(A=>A.container===u);if(!d)return;const _=this.image_editor_context.layer_manager.get_layer_textures(d.id);if(!_)return;const g=it.create({width:this.dimensions.width,height:this.dimensions.height,resolution:window.devicePixelRatio||1}),m=new re,p=new ve(_.draw);m.addChild(p),this.app.renderer.render(m,{renderTexture:g}),m.destroy({children:!0});const b=new re,y=new ve(this.stroke_texture),w=new Ue;w.setFillStyle({color:16777215,alpha:.5}),w.rect(0,0,this.dimensions.width,this.dimensions.height).fill(),w.setMask({mask:y,inverse:!1}),b.addChild(w),this.app.renderer.render(b,{renderTexture:g}),this.preview_sprite.texture=g,this.preview_sprite.alpha=1,b.destroy({children:!0}),g.destroy()}this.preview_sprite.visible=!0}get_dimensions(){return this.dimensions}get textures_initialized(){return!!(this.stroke_texture&&this.display_container&&this.preview_sprite)}cleanup(){this.cleanup_textures()}}class fm{constructor(){v(this,"name","brush");v(this,"image_editor_context");v(this,"current_tool");v(this,"current_subtool");v(this,"state",{opacity:1,brush_size:10,color:"#000000",mode:"draw"});v(this,"brush_size",10);v(this,"eraser_size",20);v(this,"is_drawing",!1);v(this,"last_x",0);v(this,"last_y",0);v(this,"scale",1);v(this,"_bound_pointer_down",null);v(this,"_bound_pointer_move",null);v(this,"_bound_pointer_up",null);v(this,"event_callbacks",new Map);v(this,"brush_cursor",null);v(this,"brush_textures",null)}async setup(e,t,i){this.image_editor_context=e,this.current_tool=t,this.current_subtool=i,this.state.mode=t==="erase"?"erase":"draw",this.state.mode==="draw"?this.state.brush_size=this.brush_size:this.state.brush_size=this.eraser_size,e.scale.subscribe(r=>{this.scale=r,this.brush_cursor&&this.brush_cursor.update_state(this.state,this.scale)}),this.brush_cursor=new cm(this.image_editor_context,this.state,this.scale),this.brush_cursor.set_active(t==="draw"||t==="erase"),this.brush_textures=new dm(this.image_editor_context,e.app),this.brush_textures.initialize_textures(),this.setup_event_listeners(),this.handle_cursors(t)}handle_cursors(e){sh(this.image_editor_context.image_container.children,"none")}set_tool(e,t){var a,l;if(this.current_tool=e,this.current_subtool=t,this.current_tool!=="erase"&&this.current_tool!=="draw"&&this.commit_pending_changes(),this.brush_cursor){const h=e==="draw"||e==="erase";this.brush_cursor.set_active(h)}const i=e==="erase"?"erase":"draw",r=this.state.mode!==i,n=e==="erase"||e==="draw",o=((a=this.brush_textures)==null?void 0:a.textures_initialized)??!1;n&&(r||!o)&&((l=this.brush_textures)==null||l.initialize_textures()),this.state.mode!==i&&(this.state.mode=i,this.state.mode==="draw"?this.state.brush_size=this.brush_size:this.state.brush_size=this.eraser_size,this.brush_cursor&&this.brush_cursor.update_state(this.state,this.scale))}commit_pending_changes(){this.is_drawing&&this.on_pointer_up()}on_pointer_down(e){const t=this.image_editor_context.layer_manager.get_active_layer();if(!(t!=null&&t.visible)||this.current_tool!=="erase"&&this.current_tool!=="draw"||this.brush_cursor&&!this.brush_cursor.is_over_image())return;this.brush_textures&&this.brush_textures.preserve_canvas_state();const i=this.image_editor_context.image_container.toLocal(e.global);this.is_drawing=!0,this.last_x=i.x,this.last_y=i.y,this.brush_textures&&this.brush_textures.draw_segment(i.x,i.y,i.x,i.y,this.state.brush_size,this.state.color,this.state.opacity,this.state.mode)}on_pointer_move(e){if(this.brush_cursor&&this.brush_cursor.update_cursor_position(e),!this.is_drawing)return;const t=this.image_editor_context.image_container.toLocal(e.global);this.brush_textures&&this.brush_textures.draw_segment(this.last_x,this.last_y,t.x,t.y,this.state.brush_size,this.state.color,this.state.opacity,this.state.mode),this.last_x=t.x,this.last_y=t.y}on_pointer_up(){this.is_drawing&&(this.is_drawing=!1,this.brush_textures&&this.brush_textures.commit_stroke(),this.notify("change"))}setup_event_listeners(){this.cleanup_event_listeners(),this._bound_pointer_down=this.on_pointer_down.bind(this),this._bound_pointer_move=this.on_pointer_move.bind(this),this._bound_pointer_up=this.on_pointer_up.bind(this);const e=this.image_editor_context.image_container;e.eventMode="static",e.interactiveChildren=!0;const t=this.image_editor_context.app.stage;t.eventMode="static",t.on("pointerdown",this._bound_pointer_down),t.on("pointermove",this._bound_pointer_move),t.on("pointerup",this._bound_pointer_up),t.on("pointerupoutside",this._bound_pointer_up),this.brush_cursor&&this.brush_cursor.setup_event_listeners()}cleanup_event_listeners(){const e=this.image_editor_context.app.stage;this._bound_pointer_down&&(e.off("pointerdown",this._bound_pointer_down),this._bound_pointer_down=null),this._bound_pointer_move&&(e.off("pointermove",this._bound_pointer_move),this._bound_pointer_move=null),this._bound_pointer_up&&(e.off("pointerup",this._bound_pointer_up),e.off("pointerupoutside",this._bound_pointer_up),this._bound_pointer_up=null),this.brush_cursor&&this.brush_cursor.cleanup_event_listeners()}set_brush_size(e){this.brush_size=e,this.state.mode==="draw"&&(this.state.brush_size=e,this.brush_cursor&&this.brush_cursor.update_state(this.state,this.scale))}set_brush_color(e){const t=ft(e).toHexString();this.state.color=t,this.brush_cursor&&this.brush_cursor.update_state(this.state,this.scale)}set_brush_opacity(e){const t=Math.max(0,Math.min(1,e));this.state.opacity=t,this.brush_cursor&&this.brush_cursor.update_state(this.state,this.scale)}set_eraser_size(e){this.eraser_size=e,this.state.mode==="erase"&&(this.state.brush_size=e,this.brush_cursor&&this.brush_cursor.update_state(this.state,this.scale))}get_current_size(){return this.state.mode==="draw"?this.brush_size:this.eraser_size}preview_brush(e){this.brush_cursor&&this.brush_cursor.preview_brush(e)}cleanup(){this.commit_pending_changes(),this.cleanup_event_listeners(),this.brush_textures&&(this.brush_textures.cleanup(),this.brush_textures=null),this.brush_cursor&&(this.brush_cursor.cleanup(),this.brush_cursor=null)}on(e,t){this.event_callbacks.set(e,[...this.event_callbacks.get(e)||[],t])}off(e,t){var i;this.event_callbacks.set(e,((i=this.event_callbacks.get(e))==null?void 0:i.filter(r=>r!==t))||[])}notify(e){for(const t of this.event_callbacks.get(e)||[])t()}}function Uo(s,e,t){const i=s.slice();return i[20]=e[t].id,i[21]=e[t].name,i[22]=e[t].user_created,i[23]=e[t].visible,i[25]=t,i}function Wo(s){var _;let e,t,i=(s[0]?"Layers":(_=s[4].layers.find(s[10]))==null?void 0:_.name)+"",r,n,o,a,l,h,c,u,f;a=new pc({});let d=s[0]&&Ho(s);return{c(){e=Y("div"),t=Y("button"),r=St(i),n=Z(),o=Y("span"),ee(a.$$.fragment),l=Z(),d&&d.c(),this.h()},l(g){e=j(g,"DIV",{class:!0});var m=L(e);t=j(m,"BUTTON",{class:!0,"aria-label":!0});var p=L(t);r=Ct(p,i),n=Q(p),o=j(p,"SPAN",{class:!0});var b=L(o);te(a.$$.fragment,b),b.forEach(k),p.forEach(k),l=Q(m),d&&d.l(m),m.forEach(k),this.h()},h(){x(o,"class","icon svelte-1peog37"),x(t,"class","layer-title-button svelte-1peog37"),x(t,"aria-label","Show Layers"),x(e,"class","layer-wrap svelte-1peog37"),oe(e,"closed",!s[0])},m(g,m){D(g,e,m),O(e,t),O(t,r),O(t,n),O(t,o),ie(a,o,null),O(e,l),d&&d.m(e,null),c=!0,u||(f=[Fe(t,"click",ms(s[11])),gs(h=Wr.call(null,e,s[19]))],u=!0)},p(g,m){var p;(!c||m&17)&&i!==(i=(g[0]?"Layers":(p=g[4].layers.find(g[10]))==null?void 0:p.name)+"")&&ii(r,i),g[0]?d?(d.p(g,m),m&1&&C(d,1)):(d=Ho(g),d.c(),C(d,1),d.m(e,null)):d&&(he(),I(d,1,1,()=>{d=null}),ce()),h&&ma(h.update)&&m&1&&h.update.call(null,g[19]),(!c||m&1)&&oe(e,"closed",!g[0])},i(g){c||(C(a.$$.fragment,g),C(d),c=!0)},o(g){I(a.$$.fragment,g),I(d),c=!1},d(g){g&&k(e),se(a),d&&d.d(),u=!1,mi(f)}}}function Ho(s){let e,t=[],i=new Map,r,n,o=Ft(s[4].layers);const a=h=>h[25];for(let h=0;h<o.length;h+=1){let c=Uo(s,o,h),u=a(c);i.set(u,t[h]=qo(u,c))}let l=s[2]&&Ko(s);return{c(){e=Y("ul");for(let h=0;h<t.length;h+=1)t[h].c();r=Z(),l&&l.c(),this.h()},l(h){e=j(h,"UL",{class:!0});var c=L(e);for(let u=0;u<t.length;u+=1)t[u].l(c);r=Q(c),l&&l.l(c),c.forEach(k),this.h()},h(){x(e,"class","svelte-1peog37")},m(h,c){D(h,e,c);for(let u=0;u<t.length;u+=1)t[u]&&t[u].m(e,null);O(e,r),l&&l.m(e,null),n=!0},p(h,c){c&944&&(o=Ft(h[4].layers),he(),t=Fh(t,c,a,1,h,o,i,e,Lh,qo,r,Uo),ce()),h[2]?l?(l.p(h,c),c&4&&C(l,1)):(l=Ko(h),l.c(),C(l,1),l.m(e,null)):l&&(he(),I(l,1,1,()=>{l=null}),ce())},i(h){if(!n){for(let c=0;c<o.length;c+=1)C(t[c]);C(l),n=!0}},o(h){for(let c=0;c<t.length;c+=1)I(t[c]);I(l),n=!1},d(h){h&&k(e);for(let c=0;c<t.length;c+=1)t[c].d();l&&l.d()}}}function _m(s){let e,t;function i(...r){return s[13](s[20],...r)}return e=new ot({props:{Icon:Ac,size:"small"}}),e.$on("click",i),{c(){ee(e.$$.fragment)},l(r){te(e.$$.fragment,r)},m(r,n){ie(e,r,n),t=!0},p(r,n){s=r},i(r){t||(C(e.$$.fragment,r),t=!0)},o(r){I(e.$$.fragment,r),t=!1},d(r){se(e,r)}}}function gm(s){let e,t;function i(...r){return s[12](s[20],...r)}return e=new ot({props:{Icon:Cc,size:"small"}}),e.$on("click",i),{c(){ee(e.$$.fragment)},l(r){te(e.$$.fragment,r)},m(r,n){ie(e,r,n),t=!0},p(r,n){s=r},i(r){t||(C(e.$$.fragment,r),t=!0)},o(r){I(e.$$.fragment,r),t=!1},d(r){se(e,r)}}}function Vo(s){let e,t,i,r,n=s[25]>0&&Xo(s),o=s[25]<s[4].layers.length-1&&Yo(s),a=s[4].layers.length>1&&s[22]&&jo(s);return{c(){e=Y("div"),n&&n.c(),t=Z(),o&&o.c(),i=Z(),a&&a.c(),this.h()},l(l){e=j(l,"DIV",{class:!0});var h=L(e);n&&n.l(h),t=Q(h),o&&o.l(h),i=Q(h),a&&a.l(h),h.forEach(k),this.h()},h(){x(e,"class","svelte-1peog37")},m(l,h){D(l,e,h),n&&n.m(e,null),O(e,t),o&&o.m(e,null),O(e,i),a&&a.m(e,null),r=!0},p(l,h){l[25]>0?n?(n.p(l,h),h&16&&C(n,1)):(n=Xo(l),n.c(),C(n,1),n.m(e,t)):n&&(he(),I(n,1,1,()=>{n=null}),ce()),l[25]<l[4].layers.length-1?o?(o.p(l,h),h&16&&C(o,1)):(o=Yo(l),o.c(),C(o,1),o.m(e,i)):o&&(he(),I(o,1,1,()=>{o=null}),ce()),l[4].layers.length>1&&l[22]?a?(a.p(l,h),h&16&&C(a,1)):(a=jo(l),a.c(),C(a,1),a.m(e,null)):a&&(he(),I(a,1,1,()=>{a=null}),ce())},i(l){r||(C(n),C(o),C(a),r=!0)},o(l){I(n),I(o),I(a),r=!1},d(l){l&&k(e),n&&n.d(),o&&o.d(),a&&a.d()}}}function Xo(s){let e,t;function i(...r){return s[15](s[20],...r)}return e=new ot({props:{Icon:sc,size:"x-small"}}),e.$on("click",i),{c(){ee(e.$$.fragment)},l(r){te(e.$$.fragment,r)},m(r,n){ie(e,r,n),t=!0},p(r,n){s=r},i(r){t||(C(e.$$.fragment,r),t=!0)},o(r){I(e.$$.fragment,r),t=!1},d(r){se(e,r)}}}function Yo(s){let e,t;function i(...r){return s[16](s[20],...r)}return e=new ot({props:{Icon:nc,size:"x-small"}}),e.$on("click",i),{c(){ee(e.$$.fragment)},l(r){te(e.$$.fragment,r)},m(r,n){ie(e,r,n),t=!0},p(r,n){s=r},i(r){t||(C(e.$$.fragment,r),t=!0)},o(r){I(e.$$.fragment,r),t=!1},d(r){se(e,r)}}}function jo(s){let e,t;function i(...r){return s[17](s[20],...r)}return e=new ot({props:{Icon:Hh,size:"x-small"}}),e.$on("click",i),{c(){ee(e.$$.fragment)},l(r){te(e.$$.fragment,r)},m(r,n){ie(e,r,n),t=!0},p(r,n){s=r},i(r){t||(C(e.$$.fragment,r),t=!0)},o(r){I(e.$$.fragment,r),t=!1},d(r){se(e,r)}}}function qo(s,e){let t,i,r,n,o,a=e[21]+"",l,h,c,u,f,d;const _=[gm,_m],g=[];function m(y,w){return y[23]?1:0}i=m(e),r=g[i]=_[i](e);function p(){return e[14](e[20])}let b=e[4].layers.length>1&&Vo(e);return{key:s,first:null,c(){t=Y("li"),r.c(),n=Z(),o=Y("button"),l=St(a),c=Z(),b&&b.c(),this.h()},l(y){t=j(y,"LI",{class:!0});var w=L(t);r.l(w),n=Q(w),o=j(w,"BUTTON",{"aria-label":!0,class:!0});var A=L(o);l=Ct(A,a),A.forEach(k),c=Q(w),b&&b.l(w),w.forEach(k),this.h()},h(){x(o,"aria-label",h=`layer-${e[25]+1}`),x(o,"class","svelte-1peog37"),oe(o,"selected_layer",e[4].active_layer===e[20]),x(t,"class","svelte-1peog37"),this.first=t},m(y,w){D(y,t,w),g[i].m(t,null),O(t,n),O(t,o),O(o,l),O(t,c),b&&b.m(t,null),u=!0,f||(d=Fe(o,"click",ms(p)),f=!0)},p(y,w){e=y;let A=i;i=m(e),i===A?g[i].p(e,w):(he(),I(g[A],1,1,()=>{g[A]=null}),ce(),r=g[i],r?r.p(e,w):(r=g[i]=_[i](e),r.c()),C(r,1),r.m(t,n)),(!u||w&16)&&a!==(a=e[21]+"")&&ii(l,a),(!u||w&16&&h!==(h=`layer-${e[25]+1}`))&&x(o,"aria-label",h),(!u||w&16)&&oe(o,"selected_layer",e[4].active_layer===e[20]),e[4].layers.length>1?b?(b.p(e,w),w&16&&C(b,1)):(b=Vo(e),b.c(),C(b,1),b.m(t,null)):b&&(he(),I(b,1,1,()=>{b=null}),ce())},i(y){u||(C(r),C(b),u=!0)},o(y){I(r),I(b),u=!1},d(y){y&&k(t),g[i].d(),b&&b.d(),f=!1,d()}}}function Ko(s){let e,t,i;return t=new ot({props:{Icon:Sa,label:"Add Layer",size:"x-small"}}),t.$on("click",s[18]),{c(){e=Y("li"),ee(t.$$.fragment),this.h()},l(r){e=j(r,"LI",{class:!0});var n=L(e);te(t.$$.fragment,n),n.forEach(k),this.h()},h(){x(e,"class","add-layer svelte-1peog37")},m(r,n){D(r,e,n),ie(t,e,null),i=!0},p:$,i(r){i||(C(t.$$.fragment,r),i=!0)},o(r){I(t.$$.fragment,r),i=!1},d(r){r&&k(e),se(t)}}}function mm(s){let e,t,i=s[3]&&Wo(s);return{c(){i&&i.c(),e=Oe()},l(r){i&&i.l(r),e=Oe()},m(r,n){i&&i.m(r,n),D(r,e,n),t=!0},p(r,[n]){r[3]?i?(i.p(r,n),n&8&&C(i,1)):(i=Wo(r),i.c(),C(i,1),i.m(e.parentNode,e)):i&&(he(),I(i,1,1,()=>{i=null}),ce())},i(r){t||(C(i),t=!0)},o(r){I(i),t=!1},d(r){r&&k(e),i&&i.d(r)}}}function pm(s,e,t){let i,r=$,n=()=>(r(),r=Oh(a,R=>t(4,i=R)),a);s.$$.on_destroy.push(()=>r());const o=Nt();let{layers:a}=e;n();let{enable_additional_layers:l=!0}=e,{enable_layers:h=!0}=e,{show_layers:c=!1}=e;function u(){o("new_layer")}function f(R){o("change_layer",R),t(0,c=!1)}function d(R,E){o("move_layer",{id:R,direction:E})}function _(R){o("delete_layer",R)}const g=R=>R.id===i.active_layer,m=()=>t(0,c=!c),p=(R,E)=>{E.stopPropagation(),o("toggle_layer_visibility",R)},b=(R,E)=>{E.stopPropagation(),o("toggle_layer_visibility",R)},y=R=>f(R),w=(R,E)=>{E.stopPropagation(),d(R,"up")},A=(R,E)=>{E.stopPropagation(),d(R,"down")},T=(R,E)=>{E.stopPropagation(),_(R)},P=R=>{R.stopPropagation(),u()},U=()=>t(0,c=!1);return s.$$set=R=>{"layers"in R&&n(t(1,a=R.layers)),"enable_additional_layers"in R&&t(2,l=R.enable_additional_layers),"enable_layers"in R&&t(3,h=R.enable_layers),"show_layers"in R&&t(0,c=R.show_layers)},[c,a,l,h,i,o,u,f,d,_,g,m,p,b,y,w,A,T,P,U]}class bm extends ke{constructor(e){super(),Ae(this,e,pm,mm,Se,{layers:1,enable_additional_layers:2,enable_layers:3,show_layers:0})}}function Zo(s){let e,t;return e=new bm({props:{layers:s[0],enable_additional_layers:s[1],enable_layers:s[2],show_layers:s[3]}}),e.$on("new_layer",s[4]),e.$on("change_layer",s[5]),e.$on("move_layer",s[6]),e.$on("delete_layer",s[7]),e.$on("toggle_layer_visibility",s[8]),{c(){ee(e.$$.fragment)},l(i){te(e.$$.fragment,i)},m(i,r){ie(e,i,r),t=!0},p(i,r){const n={};r&1&&(n.layers=i[0]),r&2&&(n.enable_additional_layers=i[1]),r&4&&(n.enable_layers=i[2]),r&8&&(n.show_layers=i[3]),e.$set(n)},i(i){t||(C(e.$$.fragment,i),t=!0)},o(i){I(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function ym(s){let e,t,i,r=s[2]&&Zo(s);return{c(){e=Y("div"),t=Y("div"),r&&r.c(),this.h()},l(n){e=j(n,"DIV",{class:!0});var o=L(e);t=j(o,"DIV",{class:!0});var a=L(t);r&&r.l(a),a.forEach(k),o.forEach(k),this.h()},h(){x(t,"class","toolbar-wrap svelte-nkklfl"),x(e,"class","toolbar-wrap-wrap svelte-nkklfl")},m(n,o){D(n,e,o),O(e,t),r&&r.m(t,null),i=!0},p(n,[o]){n[2]?r?(r.p(n,o),o&4&&C(r,1)):(r=Zo(n),r.c(),C(r,1),r.m(t,null)):r&&(he(),I(r,1,1,()=>{r=null}),ce())},i(n){i||(C(r),i=!0)},o(n){I(r),i=!1},d(n){n&&k(e),r&&r.d()}}}function wm(s,e,t){let{layers:i}=e,{enable_additional_layers:r=!0}=e,{enable_layers:n=!0}=e,{show_layers:o=!1}=e;function a(f){yt.call(this,s,f)}function l(f){yt.call(this,s,f)}function h(f){yt.call(this,s,f)}function c(f){yt.call(this,s,f)}function u(f){yt.call(this,s,f)}return s.$$set=f=>{"layers"in f&&t(0,i=f.layers),"enable_additional_layers"in f&&t(1,r=f.enable_additional_layers),"enable_layers"in f&&t(2,n=f.enable_layers),"show_layers"in f&&t(3,o=f.show_layers)},[i,r,n,o,a,l,h,c,u]}class xm extends ke{constructor(e){super(),Ae(this,e,wm,ym,Se,{layers:0,enable_additional_layers:1,enable_layers:2,show_layers:3})}}function Qo(s,e,t){const i=s.slice();return i[24]=e[t],i}function Jo(s){let e,t;return e=new ot({props:{Icon:Qh,label:"Download"}}),e.$on("click",s[14]),{c(){ee(e.$$.fragment)},l(i){te(e.$$.fragment,i)},m(i,r){ie(e,i,r),t=!0},p:$,i(i){t||(C(e.$$.fragment,i),t=!0)},o(i){I(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function $o(s){let e,t,i,r,n="Fit to screen",o,a,l,h=Ft([.25,.5,1,2,4]),c=[];for(let u=0;u<5;u+=1)c[u]=ea(Qo(s,h,u));return{c(){e=Y("div"),t=Y("ul"),i=Y("li"),r=Y("button"),r.textContent=n,o=Z();for(let u=0;u<5;u+=1)c[u].c();this.h()},l(u){e=j(u,"DIV",{class:!0});var f=L(e);t=j(f,"UL",{class:!0});var d=L(t);i=j(d,"LI",{class:!0});var _=L(i);r=j(_,"BUTTON",{class:!0,"data-svelte-h":!0}),ps(r)!=="svelte-1s3yq7u"&&(r.textContent=n),_.forEach(k),o=Q(d);for(let g=0;g<5;g+=1)c[g].l(d);d.forEach(k),f.forEach(k),this.h()},h(){x(r,"class","svelte-eqjom0"),x(i,"class","svelte-eqjom0"),x(t,"class","svelte-eqjom0"),x(e,"class","zoom-controls svelte-eqjom0")},m(u,f){D(u,e,f),O(e,t),O(t,i),O(i,r),O(t,o);for(let d=0;d<5;d+=1)c[d]&&c[d].m(t,null);a||(l=Fe(r,"click",ms(s[18])),a=!0)},p(u,f){if(f&2048){h=Ft([.25,.5,1,2,4]);let d;for(d=0;d<5;d+=1){const _=Qo(u,h,d);c[d]?c[d].p(_,f):(c[d]=ea(_),c[d].c(),c[d].m(t,null))}for(;d<5;d+=1)c[d].d(1)}},d(u){u&&k(e),_s(c,u),a=!1,l()}}}function ea(s){let e,t,i=s[24]*100+"",r,n,o,a,l;function h(){return s[19](s[24])}return{c(){e=Y("li"),t=Y("button"),r=St(i),n=St("%"),o=Z(),this.h()},l(c){e=j(c,"LI",{class:!0});var u=L(e);t=j(u,"BUTTON",{class:!0});var f=L(t);r=Ct(f,i),n=Ct(f,"%"),f.forEach(k),o=Q(u),u.forEach(k),this.h()},h(){x(t,"class","svelte-eqjom0"),x(e,"class","svelte-eqjom0")},m(c,u){D(c,e,u),O(e,t),O(t,r),O(t,n),O(e,o),a||(l=Fe(t,"click",ms(h)),a=!0)},p(c,u){s=c},d(c){c&&k(e),a=!1,l()}}}function ta(s){let e,t;return e=new ot({props:{disabled:!s[0],Icon:wa,label:"Save changes",color:"var(--color-accent)"}}),e.$on("click",s[22]),{c(){ee(e.$$.fragment)},l(i){te(e.$$.fragment,i)},m(i,r){ie(e,i,r),t=!0},p(i,r){const n={};r&1&&(n.disabled=!i[0]),e.$set(n)},i(i){t||(C(e.$$.fragment,i),t=!0)},o(i){I(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function vm(s){let e,t,i,r,n,o,a,l,h,c,u,f,d,_,g,m,p,b,y,w,A,T,P,U,R=s[4]&&Jo(s);t=new ot({props:{Icon:yc,label:"Pan",highlight:s[2]==="pan",size:"small",padded:!1,transparent:!0,disabled:s[3]}}),t.$on("click",s[15]),r=new ot({props:{Icon:zc,label:"Zoom out"}}),r.$on("click",s[16]),o=new ot({props:{Icon:Ic,label:"Zoom in"}}),o.$on("click",s[17]);let E=s[7]&&$o(s);m=new ot({props:{Icon:$h,label:"Undo",disabled:!s[5]}}),m.$on("click",s[20]),b=new ot({props:{Icon:vc,label:"Redo",disabled:!s[6]}}),b.$on("click",s[21]);let M=s[1]&&ta(s);return A=new ot({props:{Icon:Jh,label:"Clear canvas"}}),A.$on("click",s[23]),{c(){R&&R.c(),e=Z(),ee(t.$$.fragment),i=Z(),ee(r.$$.fragment),n=Z(),ee(o.$$.fragment),a=Z(),l=Y("div"),h=Y("span"),c=St(s[8]),u=St("%"),f=Z(),E&&E.c(),d=Z(),_=Y("div"),g=Z(),ee(m.$$.fragment),p=Z(),ee(b.$$.fragment),y=Z(),M&&M.c(),w=Z(),ee(A.$$.fragment),this.h()},l(z){R&&R.l(z),e=Q(z),te(t.$$.fragment,z),i=Q(z),te(r.$$.fragment,z),n=Q(z),te(o.$$.fragment,z),a=Q(z),l=j(z,"DIV",{class:!0});var V=L(l);h=j(V,"SPAN",{role:!0,tabindex:!0,class:!0});var W=L(h);c=Ct(W,s[8]),u=Ct(W,"%"),W.forEach(k),f=Q(V),E&&E.l(V),V.forEach(k),d=Q(z),_=j(z,"DIV",{class:!0}),L(_).forEach(k),g=Q(z),te(m.$$.fragment,z),p=Q(z),te(b.$$.fragment,z),y=Q(z),M&&M.l(z),w=Q(z),te(A.$$.fragment,z),this.h()},h(){x(h,"role","button"),x(h,"tabindex","0"),x(h,"class","svelte-eqjom0"),x(l,"class","zoom-number svelte-eqjom0"),x(_,"class","separator svelte-eqjom0")},m(z,V){R&&R.m(z,V),D(z,e,V),ie(t,z,V),D(z,i,V),ie(r,z,V),D(z,n,V),ie(o,z,V),D(z,a,V),D(z,l,V),O(l,h),O(h,c),O(h,u),O(l,f),E&&E.m(l,null),D(z,d,V),D(z,_,V),D(z,g,V),ie(m,z,V),D(z,p,V),ie(b,z,V),D(z,y,V),M&&M.m(z,V),D(z,w,V),ie(A,z,V),T=!0,P||(U=[Fe(h,"click",s[10]),Fe(h,"keydown",s[12])],P=!0)},p(z,V){z[4]?R?(R.p(z,V),V&16&&C(R,1)):(R=Jo(z),R.c(),C(R,1),R.m(e.parentNode,e)):R&&(he(),I(R,1,1,()=>{R=null}),ce());const W={};V&4&&(W.highlight=z[2]==="pan"),V&8&&(W.disabled=z[3]),t.$set(W),(!T||V&256)&&ii(c,z[8]),z[7]?E?E.p(z,V):(E=$o(z),E.c(),E.m(l,null)):E&&(E.d(1),E=null);const ae={};V&32&&(ae.disabled=!z[5]),m.$set(ae);const H={};V&64&&(H.disabled=!z[6]),b.$set(H),z[1]?M?(M.p(z,V),V&2&&C(M,1)):(M=ta(z),M.c(),C(M,1),M.m(w.parentNode,w)):M&&(he(),I(M,1,1,()=>{M=null}),ce())},i(z){T||(C(R),C(t.$$.fragment,z),C(r.$$.fragment,z),C(o.$$.fragment,z),C(m.$$.fragment,z),C(b.$$.fragment,z),C(M),C(A.$$.fragment,z),T=!0)},o(z){I(R),I(t.$$.fragment,z),I(r.$$.fragment,z),I(o.$$.fragment,z),I(m.$$.fragment,z),I(b.$$.fragment,z),I(M),I(A.$$.fragment,z),T=!1},d(z){z&&(k(e),k(i),k(n),k(a),k(l),k(d),k(_),k(g),k(p),k(y),k(w)),R&&R.d(z),se(t,z),se(r,z),se(o,z),E&&E.d(),se(m,z),se(b,z),M&&M.d(z),se(A,z),P=!1,mi(U)}}}function km(s){let e,t;return e=new ec({props:{$$slots:{default:[vm]},$$scope:{ctx:s}}}),{c(){ee(e.$$.fragment)},l(i){te(e.$$.fragment,i)},m(i,r){ie(e,i,r),t=!0},p(i,[r]){const n={};r&134218239&&(n.$$scope={dirty:r,ctx:i}),e.$set(n)},i(i){t||(C(e.$$.fragment,i),t=!0)},o(i){I(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function Am(s,e,t){let i,{can_save:r=!1}=e,{changeable:n=!1}=e,{current_zoom:o=1}=e,{tool:a}=e,{min_zoom:l=!0}=e,{enable_download:h=!1}=e,{can_undo:c}=e,{can_redo:u}=e;const f=Nt();let d=!1;function _(M){M.stopPropagation(),t(7,d=!d)}function g(M){f("set_zoom",M),t(7,d=!1)}function m(M){M.key==="Enter"&&g(o)}const p=M=>{f("download"),M.stopPropagation()},b=M=>{M.stopPropagation(),f("pan")},y=M=>{f("zoom_out"),M.stopPropagation()},w=M=>{f("zoom_in"),M.stopPropagation()},A=()=>g("fit"),T=M=>g(M),P=M=>{f("undo"),M.stopPropagation()},U=M=>{f("redo"),M.stopPropagation()},R=M=>{f("save"),M.stopPropagation()},E=M=>{f("remove_image"),M.stopPropagation()};return s.$$set=M=>{"can_save"in M&&t(0,r=M.can_save),"changeable"in M&&t(1,n=M.changeable),"current_zoom"in M&&t(13,o=M.current_zoom),"tool"in M&&t(2,a=M.tool),"min_zoom"in M&&t(3,l=M.min_zoom),"enable_download"in M&&t(4,h=M.enable_download),"can_undo"in M&&t(5,c=M.can_undo),"can_redo"in M&&t(6,u=M.can_redo)},s.$$.update=()=>{s.$$.dirty&8192&&t(8,i=Math.round(o*100))},[r,n,a,l,h,c,u,d,i,f,_,g,m,o,p,b,y,w,A,T,P,U,R,E]}class Sm extends ke{constructor(e){super(),Ae(this,e,Am,km,Se,{can_save:0,changeable:1,current_zoom:13,tool:2,min_zoom:3,enable_download:4,can_undo:5,can_redo:6})}}function ia(s){let e,t,i,r,n,o=s[22]!=="crop"&&sa(s),a=s[22]!=="crop"&&ra(s),l=s[1]==="image"&&s[22]==="webcam"&&na(s),h=s[22]!=="crop"&&!s[13].disabled&&oa(s);return{c(){o&&o.c(),e=Z(),a&&a.c(),t=Z(),l&&l.c(),i=Z(),h&&h.c(),r=Oe()},l(c){o&&o.l(c),e=Q(c),a&&a.l(c),t=Q(c),l&&l.l(c),i=Q(c),h&&h.l(c),r=Oe()},m(c,u){o&&o.m(c,u),D(c,e,u),a&&a.m(c,u),D(c,t,u),l&&l.m(c,u),D(c,i,u),h&&h.m(c,u),D(c,r,u),n=!0},p(c,u){c[22]!=="crop"?o?(o.p(c,u),u[0]&4194304&&C(o,1)):(o=sa(c),o.c(),C(o,1),o.m(e.parentNode,e)):o&&(he(),I(o,1,1,()=>{o=null}),ce()),c[22]!=="crop"?a?(a.p(c,u),u[0]&4194304&&C(a,1)):(a=ra(c),a.c(),C(a,1),a.m(t.parentNode,t)):a&&(he(),I(a,1,1,()=>{a=null}),ce()),c[1]==="image"&&c[22]==="webcam"?l?(l.p(c,u),u[0]&4194306&&C(l,1)):(l=na(c),l.c(),C(l,1),l.m(i.parentNode,i)):l&&(he(),I(l,1,1,()=>{l=null}),ce()),c[22]!=="crop"&&!c[13].disabled?h?(h.p(c,u),u[0]&4202496&&C(h,1)):(h=oa(c),h.c(),C(h,1),h.m(r.parentNode,r)):h&&(he(),I(h,1,1,()=>{h=null}),ce())},i(c){n||(C(o),C(a),C(l),C(h),n=!0)},o(c){I(o),I(a),I(l),I(h),n=!1},d(c){c&&(k(e),k(t),k(i),k(r)),o&&o.d(c),a&&a.d(c),l&&l.d(c),h&&h.d(c)}}}function sa(s){let e,t;return e=new Sm({props:{changeable:s[5],min_zoom:s[27],current_zoom:s[26],tool:s[1],can_save:!0,enable_download:s[15],can_undo:s[2],can_redo:s[28]}}),e.$on("set_zoom",s[63]),e.$on("zoom_in",s[64]),e.$on("zoom_out",s[65]),e.$on("remove_image",s[66]),e.$on("save",s[41]),e.$on("pan",s[67]),e.$on("download",s[68]),e.$on("undo",s[44]),e.$on("redo",s[45]),{c(){ee(e.$$.fragment)},l(i){te(e.$$.fragment,i)},m(i,r){ie(e,i,r),t=!0},p(i,r){const n={};r[0]&32&&(n.changeable=i[5]),r[0]&134217728&&(n.min_zoom=i[27]),r[0]&67108864&&(n.current_zoom=i[26]),r[0]&2&&(n.tool=i[1]),r[0]&32768&&(n.enable_download=i[15]),r[0]&4&&(n.can_undo=i[2]),r[0]&268435456&&(n.can_redo=i[28]),e.$set(n)},i(i){t||(C(e.$$.fragment,i),t=!0)},o(i){I(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function ra(s){let e,t,i,r,n,o,a;function l(_){s[69](_)}function h(_){s[70](_)}function c(_){s[71](_)}function u(_){s[72](_)}function f(_){s[73](_)}let d={sources:s[6],transforms:s[7],background:s[0],show_brush_size:s[30],show_brush_color:s[31],show_eraser_size:s[29],brush_options:s[8],eraser_options:s[9],tool:s[1],subtool:s[22]};return s[18]!==void 0&&(d.selected_color=s[18]),s[19]!==void 0&&(d.selected_size=s[19]),s[21]!==void 0&&(d.selected_eraser_size=s[21]),s[20]!==void 0&&(d.selected_opacity=s[20]),s[23]!==void 0&&(d.preview=s[23]),e=new Og({props:d}),ye.push(()=>Ce(e,"selected_color",l)),ye.push(()=>Ce(e,"selected_size",h)),ye.push(()=>Ce(e,"selected_eraser_size",c)),ye.push(()=>Ce(e,"selected_opacity",u)),ye.push(()=>Ce(e,"preview",f)),e.$on("tool_change",s[74]),e.$on("subtool_change",s[75]),{c(){ee(e.$$.fragment)},l(_){te(e.$$.fragment,_)},m(_,g){ie(e,_,g),a=!0},p(_,g){const m={};g[0]&64&&(m.sources=_[6]),g[0]&128&&(m.transforms=_[7]),g[0]&1&&(m.background=_[0]),g[0]&1073741824&&(m.show_brush_size=_[30]),g[1]&1&&(m.show_brush_color=_[31]),g[0]&536870912&&(m.show_eraser_size=_[29]),g[0]&256&&(m.brush_options=_[8]),g[0]&512&&(m.eraser_options=_[9]),g[0]&2&&(m.tool=_[1]),g[0]&4194304&&(m.subtool=_[22]),!t&&g[0]&262144&&(t=!0,m.selected_color=_[18],Me(()=>t=!1)),!i&&g[0]&524288&&(i=!0,m.selected_size=_[19],Me(()=>i=!1)),!r&&g[0]&2097152&&(r=!0,m.selected_eraser_size=_[21],Me(()=>r=!1)),!n&&g[0]&1048576&&(n=!0,m.selected_opacity=_[20],Me(()=>n=!1)),!o&&g[0]&8388608&&(o=!0,m.preview=_[23],Me(()=>o=!1)),e.$set(m)},i(_){a||(C(e.$$.fragment,_),a=!0)},o(_){I(e.$$.fragment,_),a=!1},d(_){se(e,_)}}}function na(s){let e,t,i,r;return i=new Xh({props:{upload:s[12],root:s[10],streaming:!1,mode:"image",include_audio:!1,i18n:s[11],mirror_webcam:s[14].mirror,webcam_constraints:s[14].constraints}}),i.$on("capture",s[40]),i.$on("error",s[76]),i.$on("drag",s[77]),{c(){e=Y("div"),t=Y("div"),ee(i.$$.fragment),this.h()},l(n){e=j(n,"DIV",{class:!0});var o=L(e);t=j(o,"DIV",{class:!0});var a=L(t);te(i.$$.fragment,a),a.forEach(k),o.forEach(k),this.h()},h(){x(t,"class","modal-inner svelte-h6gdep"),x(e,"class","modal svelte-h6gdep")},m(n,o){D(n,e,o),O(e,t),ie(i,t,null),r=!0},p(n,o){const a={};o[0]&4096&&(a.upload=n[12]),o[0]&1024&&(a.root=n[10]),o[0]&2048&&(a.i18n=n[11]),o[0]&16384&&(a.mirror_webcam=n[14].mirror),o[0]&16384&&(a.webcam_constraints=n[14].constraints),i.$set(a)},i(n){r||(C(i.$$.fragment,n),r=!0)},o(n){I(i.$$.fragment,n),r=!1},d(n){n&&k(e),se(i)}}}function oa(s){let e,t;return e=new xm({props:{enable_additional_layers:s[13].allow_additional_layers,layers:s[16].layers}}),e.$on("new_layer",s[78]),e.$on("change_layer",s[79]),e.$on("move_layer",s[80]),e.$on("delete_layer",s[81]),e.$on("toggle_layer_visibility",s[82]),{c(){ee(e.$$.fragment)},l(i){te(e.$$.fragment,i)},m(i,r){ie(e,i,r),t=!0},p(i,r){const n={};r[0]&8192&&(n.enable_additional_layers=i[13].allow_additional_layers),r[0]&65536&&(n.layers=i[16].layers),e.$set(n)},i(i){t||(C(e.$$.fragment,i),t=!0)},o(i){I(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function aa(s){let e,t,i;return t=new gt({props:{Icon:wa,label:"Confirm crop",show_label:!0,size:"large",padded:!0,color:"white",background:"var(--color-green-500)",label_position:"right"}}),t.$on("click",s[42]),{c(){e=Y("div"),ee(t.$$.fragment),this.h()},l(r){e=j(r,"DIV",{class:!0});var n=L(e);te(t.$$.fragment,n),n.forEach(k),this.h()},h(){x(e,"class","crop-confirm-button svelte-h6gdep")},m(r,n){D(r,e,n),ie(t,e,null),i=!0},p:$,i(r){i||(C(t.$$.fragment,r),i=!0)},o(r){I(t.$$.fragment,r),i=!1},d(r){r&&k(e),se(t)}}}function Cm(s){let e,t,i,r,n,o,a,l,h,c,u,f=s[17]&&ia(s),d=s[22]==="crop"&&aa(s);const _=s[62].default,g=Gh(_,s,s[61],null);return{c(){e=Y("div"),f&&f.c(),t=Z(),i=Y("div"),r=Z(),n=Y("div"),o=Z(),d&&d.c(),a=Z(),g&&g.c(),this.h()},l(m){e=j(m,"DIV",{"data-testid":!0,class:!0,"aria-label":!0,"aria-dropeffect":!0});var p=L(e);f&&f.l(p),t=Q(p),i=j(p,"DIV",{class:!0}),L(i).forEach(k),r=Q(p),n=j(p,"DIV",{class:!0}),L(n).forEach(k),o=Q(p),d&&d.l(p),a=Q(p),g&&g.l(p),p.forEach(k),this.h()},h(){x(i,"class","pixi-target svelte-h6gdep"),oe(i,"visible",s[22]!=="crop"),x(n,"class","pixi-target-crop svelte-h6gdep"),oe(n,"visible",s[22]==="crop"),x(e,"data-testid","image"),x(e,"class","image-container svelte-h6gdep"),x(e,"aria-label","Click to upload or drop files"),x(e,"aria-dropeffect","copy"),oe(e,"dark-bg",s[22]==="crop")},m(m,p){D(m,e,p),f&&f.m(e,null),O(e,t),O(e,i),s[83](i),O(e,r),O(e,n),s[84](n),O(e,o),d&&d.m(e,null),O(e,a),g&&g.m(e,null),h=!0,c||(u=gs(l=s[33].call(null,e,{on_drag_change:s[85],on_files:s[35],accepted_types:"image/*",disable_click:s[32]})),c=!0)},p(m,p){m[17]?f?(f.p(m,p),p[0]&131072&&C(f,1)):(f=ia(m),f.c(),C(f,1),f.m(e,t)):f&&(he(),I(f,1,1,()=>{f=null}),ce()),(!h||p[0]&4194304)&&oe(i,"visible",m[22]!=="crop"),(!h||p[0]&4194304)&&oe(n,"visible",m[22]==="crop"),m[22]==="crop"?d?(d.p(m,p),p[0]&4194304&&C(d,1)):(d=aa(m),d.c(),C(d,1),d.m(e,a)):d&&(he(),I(d,1,1,()=>{d=null}),ce()),g&&g.p&&(!h||p[1]&1073741824)&&Dh(g,_,m,m[61],h?Uh(_,m[61],p,null):Nh(m[61]),null),l&&ma(l.update)&&p[0]&8|p[1]&2&&l.update.call(null,{on_drag_change:m[85],on_files:m[35],accepted_types:"image/*",disable_click:m[32]}),(!h||p[0]&4194304)&&oe(e,"dark-bg",m[22]==="crop")},i(m){h||(C(f),C(d),C(g,m),h=!0)},o(m){I(f),I(d),I(g,m),h=!1},d(m){m&&k(e),f&&f.d(),s[83](null),s[84](null),d&&d.d(),g&&g.d(m),c=!1,u()}}}function Mm(s,e,t){let i,{$$slots:r={},$$scope:n}=e;const{drag:o,open_file_upload:a}=qh(),l=Nt(),h=!0;let{changeable:c=!1}=e,{sources:u=["upload","webcam","clipboard"]}=e,{transforms:f=["crop","resize"]}=e,{canvas_size:d}=e,{is_dragging:_=!1}=e,{background_image:g=!1}=e,{brush_options:m}=e,{eraser_options:p}=e,{fixed_canvas:b=!1}=e,{root:y}=e,{i18n:w}=e,{upload:A}=e,{composite:T}=e,{layers:P}=e,{background:U}=e,{border_region:R=0}=e,{layer_options:E}=e,{current_tool:M}=e,{webcam_options:z}=e,{show_download_button:V=!1}=e,{theme_mode:W}=e,{full_history:ae=null}=e,H,q;function Ke(){!X||!Ye||(X.set_tool(M),X.set_subtool(ct))}function le(S){!X||!Ye||(S.layers.length>0&&!S.active_layer&&X.set_layer(S.layers[0].id),M&&(X.set_tool(M),ct&&X.set_subtool(ct)),ge&&(M==="draw"||M==="erase")&&ge.set_tool(M,ct))}function Te(){return E&&X&&Ye}let{has_drawn:N=!1}=e;async function Ge(){return X?!g&&!N&&!P.length?{background:null,layers:[],composite:null}:await X.get_blobs():{background:null,layers:[],composite:null}}let X;function $e(S){X.add_image({image:S})}let Xe;async function De(S){var tt;if(!X||!S||!Te())return;let ne;if(typeof S=="string")ne=S;else if(((tt=S==null?void 0:S.meta)==null?void 0:tt._type)==="gradio.FileData"&&(S!=null&&S.url))ne=S.url;else{console.warn("Invalid source provided to add_image_from_url:",S);return}try{Xe=X.add_image_from_url(ne);let vt=Ze.add_image_from_url(ne);await Promise.all([Xe,vt]),Ze.set_tool("image"),Ze.set_subtool("crop"),t(0,g=!0),l("upload"),l("input")}catch(vt){console.error("Error adding image from URL:",vt)}}async function mt(S){if(!(!X||!S.length||!Te())&&Array.isArray(S)&&S.every(ne=>{var tt;return((tt=ne==null?void 0:ne.meta)==null?void 0:tt._type)==="gradio.FileData"}))try{await Xe,await X.add_layers_from_url(S.map(ne=>ne.url)),l("change"),l("input")}catch(ne){console.error("Error adding layer from URL:",ne)}}let ge,Qe,We=1,Ye=!1,F=!0,Ie={width:0,height:0};async function et(){if(!X||!Ye||!Qe)return;if(await ls(),H.offsetParent!==null){const ne=H.getBoundingClientRect();(ne.width!==Ie.width||ne.height!==Ie.height)&&(Qe.set_zoom("fit"),Ie={width:ne.width,height:ne.height})}}ga(()=>{let S,ne;return Et().then(()=>{S=new IntersectionObserver(()=>{et()}),ne=new ResizeObserver(()=>{et()}),S.observe(H),ne.observe(H),setTimeout(()=>{ae&&X&&X.command_manager.replay(ae,X.context).then(()=>{lt()})},0)}),typeof window<"u"&&(window.editor=X),()=>{S&&S.disconnect(),ne&&ne.disconnect(),X&&X.destroy()}});function lt(){if(!X||!Ye)return;const S=ei(X.layers);S.layers.length>0&&!S.active_layer&&X.set_layer(S.layers[0].id),M&&(X.set_tool(M),ct&&X.set_subtool(ct)),ge&&(M==="draw"||M==="erase")&&ge.set_tool(M,ct),t(46,ae=X.command_manager.history)}let Ze,G,{can_undo:Je=!1}=e,pt=!1;async function Et(){t(59,ge=new fm),Qe=new Dr,t(16,X=new Nr({target_element:H,width:d[0],height:d[1],tools:["image",Qe,new am,ge],fixed_canvas:b,border_region:R,layer_options:E,theme_mode:W})),ge.on("change",()=>{t(4,N=!0)}),t(60,G=new Dr),Ze=new Nr({target_element:q,width:d[0],height:d[1],tools:["image",G,new nm],dark:!0,fixed_canvas:!1,border_region:0,pad_bottom:40}),X.scale.subscribe(S=>{t(26,We=S)}),X.min_zoom.subscribe(S=>{t(27,F=S)}),X.dimensions.subscribe(S=>{Ie={...S}}),X.command_manager.current_history.subscribe(S=>{t(2,Je=S.previous!==null),t(28,pt=S.next!==null)}),await Promise.all([X.ready,Ze.ready]).then(()=>{Ee({tool:"image"}),t(17,Ye=!0),u.length>0?Ee({tool:"image"}):Ee({tool:"draw"}),Ze.set_subtool("crop")}),X.on("change",()=>{l("change"),t(46,ae=X.command_manager.history)}),U||P.length>0?(U&&await De(U),P.length>0&&await mt(P),Ee({tool:"draw"})):T&&(await De(T),Ee({tool:"draw"})),Ke()}async function nt(S){if(S==null||!u.includes("upload"))return;X.reset_canvas();const ne=Array.isArray(S)?S[0]:S;await X.add_image({image:ne}),await Ze.add_image({image:ne}),Ze.reset(),t(0,g=!0),Ee({tool:"draw"}),l("upload"),l("input"),l("change")}function Ee({tool:S}){X.set_tool(S),t(1,M=S),S==="image"&&(Ze.set_tool("image"),Ze.set_subtool("crop"))}function zt({tool:S,subtool:ne}){X.set_subtool(ne),t(22,ct=ne),ne!==null&&(S==="draw"&&(ne==="size"?t(30,Ni=!0):ne==="color"&&t(31,Ui=!0)),S==="erase"&&ne==="size"&&t(29,ht=!0),S==="image"&&ne==="paste"&&ri(),S==="image"&&ne==="upload"&&ls().then(()=>{t(32,i=!1),a()}))}let ht=!1,xt,Ut,Lt=1,Wt;function As(){const S=m.default_color==="auto"?m.colors[0]:m.default_color;if(Array.isArray(S))t(18,xt=S[0]),t(20,Lt=S[1]);else{t(18,xt=S);const ne=ft(S);ne.getAlpha()<1?t(20,Lt=ne.getAlpha()):t(20,Lt=1)}t(19,Ut=typeof m.default_size=="number"?m.default_size:25)}function Ss(){t(21,Wt=p.default_size==="auto"?25:p.default_size)}let Ni=!1,Ui=!1,ct=null,si=!1;function Wi(S){Qe.set_zoom(S)}function B(S){Qe.set_zoom(S==="in"?We+(We<1?.1:We*.1):We-(We<1?.1:We*.1))}async function ri(){const S=await navigator.clipboard.read();for(let ne=0;ne<S.length;ne++){const tt=S[ne].types.find(vt=>vt.startsWith("image/"));if(tt){const vt=await S[ne].getType(tt);nt(vt)}}}function Cs(S){S.detail!==null&&nt(S.detail),zt({tool:M,subtool:null})}function rh(){l("save")}async function nh(){const{image:S}=await Ze.get_crop_bounds();S&&(await X.add_image({image:S,resize:!1}),zt({tool:"image",subtool:null}),l("change"),l("input"))}async function rn(){const ne=(await X.get_blobs()).composite;if(!ne){l("download_error","Unable to generate image to download.");return}const tt=URL.createObjectURL(ne),vt=document.createElement("a");vt.href=tt,vt.download="image.png",vt.click(),URL.revokeObjectURL(tt)}function oh(){X.undo()}function ah(){X.redo()}const lh=S=>Wi(S.detail),hh=()=>B("in"),ch=()=>B("out"),uh=()=>{l("clear"),X.reset_canvas(),Ee({tool:"image"}),t(0,g=!1),t(4,N=!1)},dh=S=>{Ee({tool:"pan"})},fh=()=>rn();function _h(S){xt=S,t(18,xt)}function gh(S){Ut=S,t(19,Ut)}function mh(S){Wt=S,t(21,Wt)}function ph(S){Lt=S,t(20,Lt)}function bh(S){si=S,t(23,si)}const yh=S=>Ee(S.detail),wh=S=>zt(S.detail);function xh(S){yt.call(this,s,S)}function vh(S){yt.call(this,s,S)}const kh=()=>{X.add_layer()},Ah=S=>{X.set_layer(S.detail),M==="draw"&&Ee({tool:"draw"})},Sh=S=>{X.move_layer(S.detail.id,S.detail.direction)},Ch=S=>{X.delete_layer(S.detail)},Mh=S=>{X.toggle_layer_visibility(S.detail)};function Th(S){ye[S?"unshift":"push"](()=>{H=S,t(24,H)})}function Ph(S){ye[S?"unshift":"push"](()=>{q=S,t(25,q)})}const Ih=S=>t(3,_=S);return s.$$set=S=>{"changeable"in S&&t(5,c=S.changeable),"sources"in S&&t(6,u=S.sources),"transforms"in S&&t(7,f=S.transforms),"canvas_size"in S&&t(48,d=S.canvas_size),"is_dragging"in S&&t(3,_=S.is_dragging),"background_image"in S&&t(0,g=S.background_image),"brush_options"in S&&t(8,m=S.brush_options),"eraser_options"in S&&t(9,p=S.eraser_options),"fixed_canvas"in S&&t(49,b=S.fixed_canvas),"root"in S&&t(10,y=S.root),"i18n"in S&&t(11,w=S.i18n),"upload"in S&&t(12,A=S.upload),"composite"in S&&t(50,T=S.composite),"layers"in S&&t(51,P=S.layers),"background"in S&&t(52,U=S.background),"border_region"in S&&t(53,R=S.border_region),"layer_options"in S&&t(13,E=S.layer_options),"current_tool"in S&&t(1,M=S.current_tool),"webcam_options"in S&&t(14,z=S.webcam_options),"show_download_button"in S&&t(15,V=S.show_download_button),"theme_mode"in S&&t(54,W=S.theme_mode),"full_history"in S&&t(46,ae=S.full_history),"has_drawn"in S&&t(4,N=S.has_drawn),"can_undo"in S&&t(2,Je=S.can_undo),"$$scope"in S&&t(61,n=S.$$scope)},s.$$.update=()=>{if(s.$$.dirty[0]&73728&&E&&Te()&&(X.set_layer_options(E),Ke()),s.$$.dirty[0]&196608&&X&&Ye&&X.layers){const S=ei(X.layers);S.layers.length>0&&!S.active_layer&&le(S)}s.$$.dirty[0]&196608|s.$$.dirty[1]&3670016&&U==null&&P.length==0&&T==null&&X&&Ye&&(Ee({tool:"image"}),t(0,g=!1),t(4,N=!1)),s.$$.dirty[0]&4194306|s.$$.dirty[1]&536870912&&M==="image"&&ct==="crop"&&G.set_zoom("fit"),s.$$.dirty[0]&65540&&t(0,g=Je&&X.command_manager.contains("AddImage")),s.$$.dirty[0]&768&&(m&&As(),p&&Ss()),s.$$.dirty[0]&262400|s.$$.dirty[1]&268435456&&(ge==null||ge.set_brush_color((()=>{let S;if(xt==="auto"){const ne=m.colors.find(tt=>Array.isArray(tt)?tt[0]===m.default_color:tt===m.default_color)||m.colors[0];S=Array.isArray(ne)?ne[0]:ne}else S=xt;return S})())),s.$$.dirty[0]&524288|s.$$.dirty[1]&268435456&&(ge==null||ge.set_brush_size(typeof Ut=="number"?Ut:25)),s.$$.dirty[0]&2097152|s.$$.dirty[1]&268435456&&(ge==null||ge.set_eraser_size(typeof Wt=="number"?Wt:25)),s.$$.dirty[0]&4194371&&t(32,i=M!=="image"||M==="image"&&g||M==="image"&&ct==="webcam"||!u.includes("upload")),s.$$.dirty[0]&8388608|s.$$.dirty[1]&268435456&&(ge==null||ge.preview_brush(si)),s.$$.dirty[0]&1048576|s.$$.dirty[1]&268435456&&(ge==null||ge.set_brush_opacity(Lt)),s.$$.dirty[1]&2621440&&De(T||U),s.$$.dirty[1]&1048576&&mt(P)},[g,M,Je,_,N,c,u,f,m,p,y,w,A,E,z,V,X,Ye,xt,Ut,Lt,Wt,ct,si,H,q,We,F,pt,ht,Ni,Ui,i,o,l,nt,Ee,zt,Wi,B,Cs,rh,nh,rn,oh,ah,ae,h,d,b,T,P,U,R,W,Ge,$e,De,mt,ge,G,n,r,lh,hh,ch,uh,dh,fh,_h,gh,mh,ph,bh,yh,wh,xh,vh,kh,Ah,Sh,Ch,Mh,Th,Ph,Ih]}class Tm extends ke{constructor(e){super(),Ae(this,e,Mm,Cm,Se,{antialias:47,changeable:5,sources:6,transforms:7,canvas_size:48,is_dragging:3,background_image:0,brush_options:8,eraser_options:9,fixed_canvas:49,root:10,i18n:11,upload:12,composite:50,layers:51,background:52,border_region:53,layer_options:13,current_tool:1,webcam_options:14,show_download_button:15,theme_mode:54,full_history:46,has_drawn:4,get_blobs:55,add_image:56,add_image_from_url:57,add_layers_from_url:58,can_undo:2},null,[-1,-1,-1,-1])}get antialias(){return this.$$.ctx[47]}get get_blobs(){return this.$$.ctx[55]}get add_image(){return this.$$.ctx[56]}get add_image_from_url(){return this.$$.ctx[57]}get add_layers_from_url(){return this.$$.ctx[58]}}const Pm=/^(#\s*)(.+)$/m;function Im(s){const e=s.trim(),t=e.match(Pm);if(!t)return[!1,e||!1];const[i,,r]=t,n=r.trim();if(e===i)return[n,!1];const o=t.index!==void 0?t.index+i.length:0,l=e.substring(o).trim()||!1;return[n,l]}function la(s){let e,t,i,r=s[4]&&s[4].length&&ha(s),n=s[4]&&s[4].length&&s[2]&&!s[19]&&da(),o=s[2]&&!s[19]&&fa();return{c(){e=Y("div"),r&&r.c(),t=Z(),n&&n.c(),i=Z(),o&&o.c(),this.h()},l(a){e=j(a,"DIV",{class:!0});var l=L(e);r&&r.l(l),t=Q(l),n&&n.l(l),i=Q(l),o&&o.l(l),l.forEach(k),this.h()},h(){x(e,"class","empty wrap svelte-a0rsm5")},m(a,l){D(a,e,l),r&&r.m(e,null),O(e,t),n&&n.m(e,null),O(e,i),o&&o.m(e,null)},p(a,l){a[4]&&a[4].length?r?r.p(a,l):(r=ha(a),r.c(),r.m(e,t)):r&&(r.d(1),r=null),a[4]&&a[4].length&&a[2]&&!a[19]?n||(n=da(),n.c(),n.m(e,i)):n&&(n.d(1),n=null),a[2]&&!a[19]?o||(o=fa(),o.c(),o.m(e,null)):o&&(o.d(1),o=null)},d(a){a&&k(e),r&&r.d(),n&&n.d(),o&&o.d()}}}function ha(s){let e;function t(n,o){return n[29]||n[28]?zm:Em}let i=t(s),r=i(s);return{c(){r.c(),e=Oe()},l(n){r.l(n),e=Oe()},m(n,o){r.m(n,o),D(n,e,o)},p(n,o){i===(i=t(n))&&r?r.p(n,o):(r.d(1),r=i(n),r&&(r.c(),r.m(e.parentNode,e)))},d(n){n&&k(e),r.d(n)}}}function Em(s){let e,t="Upload an image";return{c(){e=Y("div"),e.textContent=t},l(i){e=j(i,"DIV",{"data-svelte-h":!0}),ps(e)!=="svelte-1av7pez"&&(e.textContent=t)},m(i,r){D(i,e,r)},p:$,d(i){i&&k(e)}}}function zm(s){let e,t,i=s[29]&&ca(s),r=s[28]&&ua(s);return{c(){i&&i.c(),e=Z(),r&&r.c(),t=Oe()},l(n){i&&i.l(n),e=Q(n),r&&r.l(n),t=Oe()},m(n,o){i&&i.m(n,o),D(n,e,o),r&&r.m(n,o),D(n,t,o)},p(n,o){n[29]?i?i.p(n,o):(i=ca(n),i.c(),i.m(e.parentNode,e)):i&&(i.d(1),i=null),n[28]?r?r.p(n,o):(r=ua(n),r.c(),r.m(t.parentNode,t)):r&&(r.d(1),r=null)},d(n){n&&(k(e),k(t)),i&&i.d(n),r&&r.d(n)}}}function ca(s){let e,t;return{c(){e=Y("h2"),t=St(s[29]),this.h()},l(i){e=j(i,"H2",{class:!0});var r=L(e);t=Ct(r,s[29]),r.forEach(k),this.h()},h(){x(e,"class","svelte-a0rsm5")},m(i,r){D(i,e,r),O(e,t)},p(i,r){r[0]&536870912&&ii(t,i[29])},d(i){i&&k(e)}}}function ua(s){let e,t;return{c(){e=Y("p"),t=St(s[28]),this.h()},l(i){e=j(i,"P",{class:!0});var r=L(e);t=Ct(r,s[28]),r.forEach(k),this.h()},h(){x(e,"class","svelte-a0rsm5")},m(i,r){D(i,e,r),O(e,t)},p(i,r){r[0]&268435456&&ii(t,i[28])},d(i){i&&k(e)}}}function da(s){let e,t="or";return{c(){e=Y("div"),e.textContent=t,this.h()},l(i){e=j(i,"DIV",{class:!0,"data-svelte-h":!0}),ps(e)!=="svelte-tbjmnz"&&(e.textContent=t),this.h()},h(){x(e,"class","or svelte-a0rsm5")},m(i,r){D(i,e,r)},d(i){i&&k(e)}}}function fa(s){let e,t="select the draw tool to start";return{c(){e=Y("div"),e.textContent=t},l(i){e=j(i,"DIV",{"data-svelte-h":!0}),ps(e)!=="svelte-jwb70i"&&(e.textContent=t)},m(i,r){D(i,e,r)},d(i){i&&k(e)}}}function Rm(s){let e,t=s[27]==="image"&&!s[26]&&la(s);return{c(){t&&t.c(),e=Oe()},l(i){t&&t.l(i),e=Oe()},m(i,r){t&&t.m(i,r),D(i,e,r)},p(i,r){i[27]==="image"&&!i[26]?t?t.p(i,r):(t=la(i),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null)},d(i){i&&k(e),t&&t.d(i)}}}function Bm(s){let e,t,i,r,n,o,a,l,h,c;e=new tc({props:{show_label:s[8],Icon:Aa,label:s[7]||s[5]("image.image")}});function u(b){s[37](b)}function f(b){s[38](b)}function d(b){s[39](b)}function _(b){s[40](b)}function g(b){s[41](b)}function m(b){s[42](b)}let p={transforms:s[15],composite:s[12],layers:s[11],background:s[13],canvas_size:s[16],changeable:s[9],sources:s[4],brush_options:s[2],eraser_options:s[3],fixed_canvas:s[17],border_region:s[20],layer_options:s[14],i18n:s[5],root:s[6],upload:s[18],webcam_options:s[21],show_download_button:s[22],theme_mode:s[10],$$slots:{default:[Rm]},$$scope:{ctx:s}};return s[23]!==void 0&&(p.background_image=s[23]),s[27]!==void 0&&(p.current_tool=s[27]),s[0]!==void 0&&(p.is_dragging=s[0]),s[25]!==void 0&&(p.has_drawn=s[25]),s[26]!==void 0&&(p.can_undo=s[26]),s[1]!==void 0&&(p.full_history=s[1]),i=new Tm({props:p}),s[36](i),ye.push(()=>Ce(i,"background_image",u)),ye.push(()=>Ce(i,"current_tool",f)),ye.push(()=>Ce(i,"is_dragging",d)),ye.push(()=>Ce(i,"has_drawn",_)),ye.push(()=>Ce(i,"can_undo",g)),ye.push(()=>Ce(i,"full_history",m)),i.$on("history",s[43]),i.$on("save",s[44]),i.$on("change",s[31]),i.$on("clear",s[45]),i.$on("download_error",s[46]),{c(){ee(e.$$.fragment),t=Z(),ee(i.$$.fragment)},l(b){te(e.$$.fragment,b),t=Q(b),te(i.$$.fragment,b)},m(b,y){ie(e,b,y),D(b,t,y),ie(i,b,y),c=!0},p(b,y){const w={};y[0]&256&&(w.show_label=b[8]),y[0]&160&&(w.label=b[7]||b[5]("image.image")),e.$set(w);const A={};y[0]&32768&&(A.transforms=b[15]),y[0]&4096&&(A.composite=b[12]),y[0]&2048&&(A.layers=b[11]),y[0]&8192&&(A.background=b[13]),y[0]&65536&&(A.canvas_size=b[16]),y[0]&512&&(A.changeable=b[9]),y[0]&16&&(A.sources=b[4]),y[0]&4&&(A.brush_options=b[2]),y[0]&8&&(A.eraser_options=b[3]),y[0]&131072&&(A.fixed_canvas=b[17]),y[0]&1048576&&(A.border_region=b[20]),y[0]&16384&&(A.layer_options=b[14]),y[0]&32&&(A.i18n=b[5]),y[0]&64&&(A.root=b[6]),y[0]&262144&&(A.upload=b[18]),y[0]&2097152&&(A.webcam_options=b[21]),y[0]&4194304&&(A.show_download_button=b[22]),y[0]&1024&&(A.theme_mode=b[10]),y[0]&1007157268|y[1]&524288&&(A.$$scope={dirty:y,ctx:b}),!r&&y[0]&8388608&&(r=!0,A.background_image=b[23],Me(()=>r=!1)),!n&&y[0]&134217728&&(n=!0,A.current_tool=b[27],Me(()=>n=!1)),!o&&y[0]&1&&(o=!0,A.is_dragging=b[0],Me(()=>o=!1)),!a&&y[0]&33554432&&(a=!0,A.has_drawn=b[25],Me(()=>a=!1)),!l&&y[0]&67108864&&(l=!0,A.can_undo=b[26],Me(()=>l=!1)),!h&&y[0]&2&&(h=!0,A.full_history=b[1],Me(()=>h=!1)),i.$set(A)},i(b){c||(C(e.$$.fragment,b),C(i.$$.fragment,b),c=!0)},o(b){I(e.$$.fragment,b),I(i.$$.fragment,b),c=!1},d(b){b&&k(t),se(e,b),s[36](null),se(i,b)}}}function Fm(s){return!!s}function Lm(s){return!!s}function _a(){return new Promise(s=>setTimeout(()=>s(),30))}function Om(s,e,t){let i,r,{brush:n}=e,{eraser:o}=e,{sources:a}=e,{i18n:l}=e,{root:h}=e,{label:c=void 0}=e,{show_label:u}=e,{changeable:f=!1}=e,{theme_mode:d}=e,{layers:_}=e,{composite:g}=e,{background:m}=e,{layer_options:p}=e,{transforms:b}=e,{accept_blobs:y}=e,{canvas_size:w}=e,{fixed_canvas:A=!1}=e,{realtime:T}=e,{upload:P}=e,{is_dragging:U}=e,{placeholder:R=void 0}=e,{border_region:E}=e,{full_history:M=null}=e,{webcam_options:z}=e,{show_download_button:V=!1}=e;const W=Nt();let ae,H=!1;async function q(){let G;try{G=await ae.get_blobs()}catch{return{background:null,layers:[],composite:null}}const Je=G.background?P(await Ts([new File([G.background],"background.png")]),h):Promise.resolve(null),pt=G.layers.filter(Fm).map(async(ht,xt)=>P(await Ts([new File([ht],`layer_${xt}.png`)]),h)),Et=G.composite?P(await Ts([new File([G.composite],"composite.png")]),h):Promise.resolve(null),[nt,Ee,...zt]=await Promise.all([Je,Et,...pt]);return{background:Array.isArray(nt)?nt[0]:nt,layers:zt.flatMap(ht=>Array.isArray(ht)?ht:[ht]).filter(Lm),composite:Array.isArray(Ee)?Ee[0]:Ee}}function Ke(G){ae&&G==null&&(ae.handle_remove(),W("receive_null"))}let le=!1,Te,{image_id:N=null}=e,Ge=!1,X=!1;async function $e(G){if(!T)return;if(Ge){X=!0;return}Ge=!0,await _a();const Je=await ae.get_blobs(),pt=[];let Et=Math.random().toString(36).substring(2);Je.background&&pt.push([Et,"background",new File([Je.background],"background.png"),null]),Je.composite&&pt.push([Et,"composite",new File([Je.composite],"composite.png"),null]),Je.layers.forEach((nt,Ee)=>{nt&&pt.push([Et,"layer",new File([nt],`layer_${Ee}.png`),Ee])}),await Promise.all(pt.map(async([nt,Ee,zt,ht])=>y({binary:!0,data:{file:zt,id:nt,type:Ee,index:ht}}))),t(32,N=Et),W("change"),await _a(),Ge=!1,X&&(X=!1,Ge=!1,$e())}let Xe;function De(G){ye[G?"unshift":"push"](()=>{ae=G,t(24,ae)})}function mt(G){le=G,t(23,le)}function ge(G){Xe=G,t(27,Xe)}function Qe(G){U=G,t(0,U)}function We(G){H=G,t(25,H)}function Ye(G){Te=G,t(26,Te)}function F(G){M=G,t(1,M)}function Ie(G){yt.call(this,s,G)}function et(G){yt.call(this,s,G)}const lt=()=>W("clear");function Ze(G){yt.call(this,s,G)}return s.$$set=G=>{"brush"in G&&t(2,n=G.brush),"eraser"in G&&t(3,o=G.eraser),"sources"in G&&t(4,a=G.sources),"i18n"in G&&t(5,l=G.i18n),"root"in G&&t(6,h=G.root),"label"in G&&t(7,c=G.label),"show_label"in G&&t(8,u=G.show_label),"changeable"in G&&t(9,f=G.changeable),"theme_mode"in G&&t(10,d=G.theme_mode),"layers"in G&&t(11,_=G.layers),"composite"in G&&t(12,g=G.composite),"background"in G&&t(13,m=G.background),"layer_options"in G&&t(14,p=G.layer_options),"transforms"in G&&t(15,b=G.transforms),"accept_blobs"in G&&t(33,y=G.accept_blobs),"canvas_size"in G&&t(16,w=G.canvas_size),"fixed_canvas"in G&&t(17,A=G.fixed_canvas),"realtime"in G&&t(34,T=G.realtime),"upload"in G&&t(18,P=G.upload),"is_dragging"in G&&t(0,U=G.is_dragging),"placeholder"in G&&t(19,R=G.placeholder),"border_region"in G&&t(20,E=G.border_region),"full_history"in G&&t(1,M=G.full_history),"webcam_options"in G&&t(21,z=G.webcam_options),"show_download_button"in G&&t(22,V=G.show_download_button),"image_id"in G&&t(32,N=G.image_id)},s.$$.update=()=>{s.$$.dirty[0]&8388608&&le&&W("upload"),s.$$.dirty[0]&14336&&Ke({layers:_,composite:g,background:m}),s.$$.dirty[0]&524288&&t(29,[i,r]=R?Im(R):[!1,!1],i,(t(28,r),t(19,R)))},[U,M,n,o,a,l,h,c,u,f,d,_,g,m,p,b,w,A,P,R,E,z,V,le,ae,H,Te,Xe,r,i,W,$e,N,y,T,q,De,mt,ge,Qe,We,Ye,F,Ie,et,lt,Ze]}class Gm extends ke{constructor(e){super(),Ae(this,e,Om,Bm,Se,{brush:2,eraser:3,sources:4,i18n:5,root:6,label:7,show_label:8,changeable:9,theme_mode:10,layers:11,composite:12,background:13,layer_options:14,transforms:15,accept_blobs:33,canvas_size:16,fixed_canvas:17,realtime:34,upload:18,is_dragging:0,placeholder:19,border_region:20,full_history:1,webcam_options:21,show_download_button:22,get_data:35,image_id:32},null,[-1,-1])}get get_data(){return this.$$.ctx[35]}}function Dm(s){let e,t;return e=new xa({props:{visible:s[5],variant:s[39]?"solid":"dashed",border_mode:s[35]?"focus":"base",padding:!1,elem_id:s[3],elem_classes:s[4],height:s[10],width:s[11],allow_overflow:!0,overflow_behavior:"visible",container:s[13],scale:s[14],min_width:s[15],$$slots:{default:[Um]},$$scope:{ctx:s}}}),{c(){ee(e.$$.fragment)},l(i){te(e.$$.fragment,i)},m(i,r){ie(e,i,r),t=!0},p(i,r){const n={};r[0]&32&&(n.visible=i[5]),r[1]&256&&(n.variant=i[39]?"solid":"dashed"),r[1]&16&&(n.border_mode=i[35]?"focus":"base"),r[0]&8&&(n.elem_id=i[3]),r[0]&16&&(n.elem_classes=i[4]),r[0]&1024&&(n.height=i[10]),r[0]&2048&&(n.width=i[11]),r[0]&8192&&(n.container=i[13]),r[0]&16384&&(n.scale=i[14]),r[0]&32768&&(n.min_width=i[15]),r[0]&1878655943|r[1]&255|r[2]&128&&(n.$$scope={dirty:r,ctx:i}),e.$set(n)},i(i){t||(C(e.$$.fragment,i),t=!0)},o(i){I(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function Nm(s){let e,t;return e=new xa({props:{visible:s[5],variant:"solid",border_mode:s[35]?"focus":"base",padding:!1,elem_id:s[3],elem_classes:s[4],height:s[10],width:s[11],allow_overflow:!0,overflow_behavior:"visible",container:s[13],scale:s[14],min_width:s[15],$$slots:{default:[Wm]},$$scope:{ctx:s}}}),{c(){ee(e.$$.fragment)},l(i){te(e.$$.fragment,i)},m(i,r){ie(e,i,r),t=!0},p(i,r){const n={};r[0]&32&&(n.visible=i[5]),r[1]&16&&(n.border_mode=i[35]?"focus":"base"),r[0]&8&&(n.elem_id=i[3]),r[0]&16&&(n.elem_classes=i[4]),r[0]&1024&&(n.height=i[10]),r[0]&2048&&(n.width=i[11]),r[0]&8192&&(n.container=i[13]),r[0]&16384&&(n.scale=i[14]),r[0]&32768&&(n.min_width=i[15]),r[0]&805376451|r[2]&128&&(n.$$scope={dirty:r,ctx:i}),e.$set(n)},i(i){t||(C(e.$$.fragment,i),t=!0)},o(i){I(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function Um(s){let e,t,i,r,n,o,a;const l=[{autoscroll:s[29].autoscroll},{i18n:s[29].i18n},s[1]];let h={};for(let _=0;_<l.length;_+=1)h=pa(h,l[_]);e=new va({props:h}),e.$on("clear_status",s[48]);function c(_){s[50](_)}function u(_){s[51](_)}function f(_){s[53](_)}let d={border_region:s[30],canvas_size:s[26],layers:s[36],composite:s[37],background:s[38],root:s[9],sources:s[17],label:s[6],show_label:s[7],fixed_canvas:s[27],brush:s[20],eraser:s[21],changeable:s[24].includes("apply"),realtime:s[24].includes("change")||s[24].includes("input"),i18n:s[29].i18n,transforms:s[22],accept_blobs:s[25].accept_blobs,layer_options:s[23],upload:s[49],placeholder:s[19],webcam_options:s[31],show_download_button:s[8],theme_mode:s[32]};return s[35]!==void 0&&(d.is_dragging=s[35]),s[34]!==void 0&&(d.image_id=s[34]),s[2]!==void 0&&(d.full_history=s[2]),i=new Gm({props:d}),ye.push(()=>Ce(i,"is_dragging",c)),ye.push(()=>Ce(i,"image_id",u)),s[52](i),ye.push(()=>Ce(i,"full_history",f)),i.$on("change",s[54]),i.$on("save",s[55]),i.$on("edit",s[56]),i.$on("clear",s[57]),i.$on("drag",s[58]),i.$on("upload",s[59]),i.$on("share",s[60]),i.$on("error",s[61]),i.$on("receive_null",s[62]),i.$on("error",s[63]),i.$on("download_error",s[64]),{c(){ee(e.$$.fragment),t=Z(),ee(i.$$.fragment)},l(_){te(e.$$.fragment,_),t=Q(_),te(i.$$.fragment,_)},m(_,g){ie(e,_,g),D(_,t,g),ie(i,_,g),a=!0},p(_,g){const m=g[0]&536870914?ba(l,[g[0]&536870912&&{autoscroll:_[29].autoscroll},g[0]&536870912&&{i18n:_[29].i18n},g[0]&2&&ya(_[1])]):{};e.$set(m);const p={};g[0]&1073741824&&(p.border_region=_[30]),g[0]&67108864&&(p.canvas_size=_[26]),g[1]&32&&(p.layers=_[36]),g[1]&64&&(p.composite=_[37]),g[1]&128&&(p.background=_[38]),g[0]&512&&(p.root=_[9]),g[0]&131072&&(p.sources=_[17]),g[0]&64&&(p.label=_[6]),g[0]&128&&(p.show_label=_[7]),g[0]&134217728&&(p.fixed_canvas=_[27]),g[0]&1048576&&(p.brush=_[20]),g[0]&2097152&&(p.eraser=_[21]),g[0]&16777216&&(p.changeable=_[24].includes("apply")),g[0]&16777216&&(p.realtime=_[24].includes("change")||_[24].includes("input")),g[0]&536870912&&(p.i18n=_[29].i18n),g[0]&4194304&&(p.transforms=_[22]),g[0]&33554432&&(p.accept_blobs=_[25].accept_blobs),g[0]&8388608&&(p.layer_options=_[23]),g[0]&536870912&&(p.upload=_[49]),g[0]&524288&&(p.placeholder=_[19]),g[1]&1&&(p.webcam_options=_[31]),g[0]&256&&(p.show_download_button=_[8]),g[1]&2&&(p.theme_mode=_[32]),!r&&g[1]&16&&(r=!0,p.is_dragging=_[35],Me(()=>r=!1)),!n&&g[1]&8&&(n=!0,p.image_id=_[34],Me(()=>n=!1)),!o&&g[0]&4&&(o=!0,p.full_history=_[2],Me(()=>o=!1)),i.$set(p)},i(_){a||(C(e.$$.fragment,_),C(i.$$.fragment,_),a=!0)},o(_){I(e.$$.fragment,_),I(i.$$.fragment,_),a=!1},d(_){_&&k(t),se(e,_),s[52](null),se(i,_)}}}function Wm(s){var a;let e,t,i,r;const n=[{autoscroll:s[29].autoscroll},{i18n:s[29].i18n},s[1]];let o={};for(let l=0;l<n.length;l+=1)o=pa(o,n[l]);return e=new va({props:o}),e.$on("clear_status",s[44]),i=new Vh({props:{value:((a=s[0])==null?void 0:a.composite)||null,label:s[6],show_label:s[7],show_download_button:s[8],selectable:s[12],show_share_button:s[16],i18n:s[29].i18n,show_fullscreen_button:s[28]}}),i.$on("select",s[45]),i.$on("share",s[46]),i.$on("error",s[47]),{c(){ee(e.$$.fragment),t=Z(),ee(i.$$.fragment)},l(l){te(e.$$.fragment,l),t=Q(l),te(i.$$.fragment,l)},m(l,h){ie(e,l,h),D(l,t,h),ie(i,l,h),r=!0},p(l,h){var f;const c=h[0]&536870914?ba(n,[h[0]&536870912&&{autoscroll:l[29].autoscroll},h[0]&536870912&&{i18n:l[29].i18n},h[0]&2&&ya(l[1])]):{};e.$set(c);const u={};h[0]&1&&(u.value=((f=l[0])==null?void 0:f.composite)||null),h[0]&64&&(u.label=l[6]),h[0]&128&&(u.show_label=l[7]),h[0]&256&&(u.show_download_button=l[8]),h[0]&4096&&(u.selectable=l[12]),h[0]&65536&&(u.show_share_button=l[16]),h[0]&536870912&&(u.i18n=l[29].i18n),h[0]&268435456&&(u.show_fullscreen_button=l[28]),i.$set(u)},i(l){r||(C(e.$$.fragment,l),C(i.$$.fragment,l),r=!0)},o(l){I(e.$$.fragment,l),I(i.$$.fragment,l),r=!1},d(l){l&&k(t),se(e,l),se(i,l)}}}function Hm(s){let e,t,i,r;const n=[Nm,Dm],o=[];function a(l,h){return l[18]?1:0}return e=a(s),t=o[e]=n[e](s),{c(){t.c(),i=Oe()},l(l){t.l(l),i=Oe()},m(l,h){o[e].m(l,h),D(l,i,h),r=!0},p(l,h){let c=e;e=a(l),e===c?o[e].p(l,h):(he(),I(o[c],1,1,()=>{o[c]=null}),ce(),t=o[e],t?t.p(l,h):(t=o[e]=n[e](l),t.c()),C(t,1),t.m(i.parentNode,i))},i(l){r||(C(t),r=!0)},o(l){I(t),r=!1},d(l){l&&k(i),o[e].d(l)}}}function Vm(s,e,t){let i,r,n,o,{elem_id:a=""}=e,{elem_classes:l=[]}=e,{visible:h=!0}=e,{value:c={background:null,layers:[],composite:null}}=e,{label:u}=e,{show_label:f}=e,{show_download_button:d}=e,{root:_}=e,{value_is_output:g=!1}=e,{height:m=350}=e,{width:p}=e,{_selectable:b=!1}=e,{container:y=!0}=e,{scale:w=null}=e,{min_width:A=void 0}=e,{loading_status:T}=e,{show_share_button:P=!1}=e,{sources:U=[]}=e,{interactive:R}=e,{placeholder:E}=e,{brush:M}=e,{eraser:z}=e,{transforms:V=[]}=e,{layers:W}=e,{attached_events:ae=[]}=e,{server:H}=e,{canvas_size:q}=e,{fixed_canvas:Ke=!1}=e,{show_fullscreen_button:le=!0}=e,{full_history:Te=null}=e,{gradio:N}=e,{border_region:Ge=0}=e,{webcam_options:X}=e,{theme_mode:$e}=e,Xe,De=null;async function mt(){if(De){const ri={id:De};return t(34,De=null),ri}return await Xe.get_data()}let ge;const We=typeof window<"u"?window.requestAnimationFrame:B=>B();function Ye(){return new Promise(B=>{We(()=>We(()=>B()))})}async function F(){var B;await Ye(),c&&(c.background||(B=c.layers)!=null&&B.length||c.composite)&&N.dispatch("change")}function Ie(){N.dispatch("apply")}function et(){N.dispatch("change"),g||(N.dispatch("input"),ls().then(B=>t(42,g=!1)))}const lt=()=>N.dispatch("clear_status",T),Ze=({detail:B})=>N.dispatch("select",B),G=({detail:B})=>N.dispatch("share",B),Je=({detail:B})=>N.dispatch("error",B),pt=()=>N.dispatch("clear_status",T),Et=(...B)=>N.client.upload(...B);function nt(B){ge=B,t(35,ge)}function Ee(B){De=B,t(34,De)}function zt(B){ye[B?"unshift":"push"](()=>{Xe=B,t(33,Xe)})}function ht(B){Te=B,t(2,Te)}const xt=()=>et(),Ut=B=>Ie(),Lt=()=>N.dispatch("edit"),Wt=()=>N.dispatch("clear"),As=({detail:B})=>t(35,ge=B),Ss=()=>N.dispatch("upload"),Ni=({detail:B})=>N.dispatch("share",B),Ui=({detail:B})=>{t(1,T=T||{}),t(1,T.status="error",T),N.dispatch("error",B)},ct=()=>t(0,c={background:null,layers:[],composite:null});function si(B){yt.call(this,s,B)}const Wi=B=>N.dispatch("error",B.detail);return s.$$set=B=>{"elem_id"in B&&t(3,a=B.elem_id),"elem_classes"in B&&t(4,l=B.elem_classes),"visible"in B&&t(5,h=B.visible),"value"in B&&t(0,c=B.value),"label"in B&&t(6,u=B.label),"show_label"in B&&t(7,f=B.show_label),"show_download_button"in B&&t(8,d=B.show_download_button),"root"in B&&t(9,_=B.root),"value_is_output"in B&&t(42,g=B.value_is_output),"height"in B&&t(10,m=B.height),"width"in B&&t(11,p=B.width),"_selectable"in B&&t(12,b=B._selectable),"container"in B&&t(13,y=B.container),"scale"in B&&t(14,w=B.scale),"min_width"in B&&t(15,A=B.min_width),"loading_status"in B&&t(1,T=B.loading_status),"show_share_button"in B&&t(16,P=B.show_share_button),"sources"in B&&t(17,U=B.sources),"interactive"in B&&t(18,R=B.interactive),"placeholder"in B&&t(19,E=B.placeholder),"brush"in B&&t(20,M=B.brush),"eraser"in B&&t(21,z=B.eraser),"transforms"in B&&t(22,V=B.transforms),"layers"in B&&t(23,W=B.layers),"attached_events"in B&&t(24,ae=B.attached_events),"server"in B&&t(25,H=B.server),"canvas_size"in B&&t(26,q=B.canvas_size),"fixed_canvas"in B&&t(27,Ke=B.fixed_canvas),"show_fullscreen_button"in B&&t(28,le=B.show_fullscreen_button),"full_history"in B&&t(2,Te=B.full_history),"gradio"in B&&t(29,N=B.gradio),"border_region"in B&&t(30,Ge=B.border_region),"webcam_options"in B&&t(31,X=B.webcam_options),"theme_mode"in B&&t(32,$e=B.theme_mode)},s.$$.update=()=>{var B,ri;s.$$.dirty[0]&1&&c&&F(),s.$$.dirty[0]&1&&t(39,i=(c==null?void 0:c.background)||((B=c==null?void 0:c.layers)==null?void 0:B.length)||(c==null?void 0:c.composite)),s.$$.dirty[0]&1&&t(38,r=c!=null&&c.background?new Ps(c.background):null),s.$$.dirty[0]&1&&t(37,n=c!=null&&c.composite?new Ps(c.composite):null),s.$$.dirty[0]&1&&t(36,o=((ri=c==null?void 0:c.layers)==null?void 0:ri.map(Cs=>new Ps(Cs)))||[])},[c,T,Te,a,l,h,u,f,d,_,m,p,b,y,w,A,P,U,R,E,M,z,V,W,ae,H,q,Ke,le,N,Ge,X,$e,Xe,De,ge,o,n,r,i,Ie,et,g,mt,lt,Ze,G,Je,pt,Et,nt,Ee,zt,ht,xt,Ut,Lt,Wt,As,Ss,Ni,Ui,ct,si,Wi]}class Xm extends ke{constructor(e){super(),Ae(this,e,Vm,Hm,Wh,{elem_id:3,elem_classes:4,visible:5,value:0,label:6,show_label:7,show_download_button:8,root:9,value_is_output:42,height:10,width:11,_selectable:12,container:13,scale:14,min_width:15,loading_status:1,show_share_button:16,sources:17,interactive:18,placeholder:19,brush:20,eraser:21,transforms:22,layers:23,attached_events:24,server:25,canvas_size:26,fixed_canvas:27,show_fullscreen_button:28,full_history:2,gradio:29,border_region:30,webcam_options:31,theme_mode:32,get_value:43},null,[-1,-1,-1])}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),me()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),me()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),me()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),me()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),me()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),me()}get show_download_button(){return this.$$.ctx[8]}set show_download_button(e){this.$$set({show_download_button:e}),me()}get root(){return this.$$.ctx[9]}set root(e){this.$$set({root:e}),me()}get value_is_output(){return this.$$.ctx[42]}set value_is_output(e){this.$$set({value_is_output:e}),me()}get height(){return this.$$.ctx[10]}set height(e){this.$$set({height:e}),me()}get width(){return this.$$.ctx[11]}set width(e){this.$$set({width:e}),me()}get _selectable(){return this.$$.ctx[12]}set _selectable(e){this.$$set({_selectable:e}),me()}get container(){return this.$$.ctx[13]}set container(e){this.$$set({container:e}),me()}get scale(){return this.$$.ctx[14]}set scale(e){this.$$set({scale:e}),me()}get min_width(){return this.$$.ctx[15]}set min_width(e){this.$$set({min_width:e}),me()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),me()}get show_share_button(){return this.$$.ctx[16]}set show_share_button(e){this.$$set({show_share_button:e}),me()}get sources(){return this.$$.ctx[17]}set sources(e){this.$$set({sources:e}),me()}get interactive(){return this.$$.ctx[18]}set interactive(e){this.$$set({interactive:e}),me()}get placeholder(){return this.$$.ctx[19]}set placeholder(e){this.$$set({placeholder:e}),me()}get brush(){return this.$$.ctx[20]}set brush(e){this.$$set({brush:e}),me()}get eraser(){return this.$$.ctx[21]}set eraser(e){this.$$set({eraser:e}),me()}get transforms(){return this.$$.ctx[22]}set transforms(e){this.$$set({transforms:e}),me()}get layers(){return this.$$.ctx[23]}set layers(e){this.$$set({layers:e}),me()}get attached_events(){return this.$$.ctx[24]}set attached_events(e){this.$$set({attached_events:e}),me()}get server(){return this.$$.ctx[25]}set server(e){this.$$set({server:e}),me()}get canvas_size(){return this.$$.ctx[26]}set canvas_size(e){this.$$set({canvas_size:e}),me()}get fixed_canvas(){return this.$$.ctx[27]}set fixed_canvas(e){this.$$set({fixed_canvas:e}),me()}get show_fullscreen_button(){return this.$$.ctx[28]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),me()}get full_history(){return this.$$.ctx[2]}set full_history(e){this.$$set({full_history:e}),me()}get gradio(){return this.$$.ctx[29]}set gradio(e){this.$$set({gradio:e}),me()}get border_region(){return this.$$.ctx[30]}set border_region(e){this.$$set({border_region:e}),me()}get webcam_options(){return this.$$.ctx[31]}set webcam_options(e){this.$$set({webcam_options:e}),me()}get theme_mode(){return this.$$.ctx[32]}set theme_mode(e){this.$$set({theme_mode:e}),me()}get get_value(){return this.$$.ctx[43]}}const up=Object.freeze(Object.defineProperty({__proto__:null,default:Xm},Symbol.toStringTag,{value:"Module"}));export{Ne as $,bl as A,at as B,re as C,Le as D,J as E,vr as F,Vt as G,La as H,Ei as I,Mt as J,wn as K,ve as L,we as M,Ku as N,od as O,fe as P,He as Q,yr as R,rd as S,ji as T,gr as U,Pe as V,Ad as W,vn as X,An as Y,Tu as Z,Ds as _,Tt as a,Da as a0,it as a1,yl as a2,be as a3,xe as a4,Pd as a5,Zd as a6,mf as a7,bf as a8,kf as a9,Sf as aa,Cf as ab,Gi as ac,g_ as ad,Fl as ae,Bn as af,zn as ag,qc as ah,no as ai,x_ as aj,Ue as ak,Be as al,Zc as am,io as an,Rr as ao,so as ap,us as aq,Gl as ar,up as as,Vd as b,Li as c,ns as d,rt as e,Rn as f,Ud as g,Yr as h,gl as i,Za as j,Pt as k,hl as l,to as m,gf as n,pf as o,xf as p,Sl as q,hu as r,Af as s,ys as t,_e as u,Kc as v,Ve as w,_i as x,Tf as y,Gt as z};
//# sourceMappingURL=Index.Bd_GXR6d.js.map
