import{ar as d,C as i,an as m,ao as c}from"./index-Dpxo-yl_.js";import{GLTFLoader as a}from"./glTFLoader-9Z3KGax5.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./bone-kZWM5-u7.js";import"./rawTexture-DmvUfjqF.js";import"./assetContainer-BRzQBugc.js";import"./objectModelMapping-BR4RdEzn.js";const h="KHR_materials_sheen";class l{constructor(n){this.name=h,this.order=190,this._loader=n,this.enabled=this._loader.isExtensionUsed(h)}dispose(){this._loader=null}loadMaterialPropertiesAsync(n,s,e){return a.LoadExtensionAsync(n,s,this.name,(r,o)=>{const t=new Array;return t.push(this._loader.loadMaterialPropertiesAsync(n,s,e)),t.push(this._loadSheenPropertiesAsync(r,o,e)),Promise.all(t).then(()=>{})})}_loadSheenPropertiesAsync(n,s,e){if(!(e instanceof d))throw new Error(`${n}: Material type not supported`);const r=new Array;return e.sheen.isEnabled=!0,e.sheen.intensity=1,s.sheenColorFactor!=null?e.sheen.color=i.FromArray(s.sheenColorFactor):e.sheen.color=i.Black(),s.sheenColorTexture&&r.push(this._loader.loadTextureInfoAsync(`${n}/sheenColorTexture`,s.sheenColorTexture,o=>{o.name=`${e.name} (Sheen Color)`,e.sheen.texture=o})),s.sheenRoughnessFactor!==void 0?e.sheen.roughness=s.sheenRoughnessFactor:e.sheen.roughness=0,s.sheenRoughnessTexture&&(s.sheenRoughnessTexture.nonColorData=!0,r.push(this._loader.loadTextureInfoAsync(`${n}/sheenRoughnessTexture`,s.sheenRoughnessTexture,o=>{o.name=`${e.name} (Sheen Roughness)`,e.sheen.textureRoughness=o}))),e.sheen.albedoScaling=!0,e.sheen.useRoughnessFromMainTexture=!1,Promise.all(r).then(()=>{})}}m(h);c(h,!0,u=>new l(u));export{l as KHR_materials_sheen};
//# sourceMappingURL=KHR_materials_sheen-CQCHkYr-.js.map
