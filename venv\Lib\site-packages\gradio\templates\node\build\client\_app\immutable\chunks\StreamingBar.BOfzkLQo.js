import{SvelteComponent as c,init as _,safe_not_equal as u,empty as o,insert_hydration as f,noop as a,detach as r,element as d,claim_element as p,children as h,attr as b,set_style as m}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import"./2.B2AoQPnG.js";function s(l){let e,i=`${l[0]}s`;return{c(){e=d("div"),this.h()},l(t){e=p(t,"DIV",{class:!0}),h(e).forEach(r),this.h()},h(){b(e,"class","streaming-bar svelte-roz8lq"),m(e,"animation-duration",i)},m(t,n){f(t,e,n)},p(t,n){n&1&&i!==(i=`${t[0]}s`)&&m(e,"animation-duration",i)},d(t){t&&r(e)}}}function v(l){let e,i=l[0]&&s(l);return{c(){i&&i.c(),e=o()},l(t){i&&i.l(t),e=o()},m(t,n){i&&i.m(t,n),f(t,e,n)},p(t,[n]){t[0]?i?i.p(t,n):(i=s(t),i.c(),i.m(e.parentNode,e)):i&&(i.d(1),i=null)},i:a,o:a,d(t){t&&r(e),i&&i.d(t)}}}function y(l,e,i){let{time_limit:t}=e;return l.$$set=n=>{"time_limit"in n&&i(0,t=n.time_limit)},[t]}class $ extends c{constructor(e){super(),_(this,e,y,v,u,{time_limit:0})}}export{$ as S};
//# sourceMappingURL=StreamingBar.BOfzkLQo.js.map
