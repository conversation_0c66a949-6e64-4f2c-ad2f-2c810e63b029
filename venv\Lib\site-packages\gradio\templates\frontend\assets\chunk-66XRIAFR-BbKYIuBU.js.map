{"version": 3, "file": "chunk-66XRIAFR-BbKYIuBU.js", "sources": ["../../../../node_modules/.pnpm/mermaid@11.5.0/node_modules/mermaid/dist/chunks/mermaid.core/chunk-66XRIAFR.mjs"], "sourcesContent": ["import {\n  __name\n} from \"./chunk-O7R7247Q.mjs\";\n\n// src/utils/imperativeState.ts\nvar ImperativeState = class {\n  /**\n   * @param init - Function that creates the default state.\n   */\n  constructor(init) {\n    this.init = init;\n    this.records = this.init();\n  }\n  static {\n    __name(this, \"ImperativeState\");\n  }\n  reset() {\n    this.records = this.init();\n  }\n};\n\nexport {\n  ImperativeState\n};\n"], "names": ["ImperativeState", "init", "__name"], "mappings": "+CAKG,IAACA,EAAkB,KAAM,CAI1B,YAAYC,EAAM,CAChB,KAAK,KAAOA,EACZ,KAAK,QAAU,KAAK,MACrB,CACD,MAAA,CACEC,EAAO,KAAM,iBAAiB,CAC/B,CACD,OAAQ,CACN,KAAK,QAAU,KAAK,MACrB,CACH", "x_google_ignoreList": [0]}