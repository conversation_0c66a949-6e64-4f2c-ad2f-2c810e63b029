import{SvelteComponent as Ve,init as De,safe_not_equal as Fe,flush as m,empty as x,insert_hydration as L,group_outros as He,transition_out as p,check_outros as Ke,transition_in as k,detach as Q,onMount as Le,afterUpdate as Qe,create_component as z,claim_component as B,mount_component as C,destroy_component as I,assign as ee,binding_callbacks as E,bind as N,space as te,claim_space as se,get_spread_update as ie,get_spread_object as ne,add_flush_callback as U}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import Xe from"./StaticAudio.CEB3a_-s.js";import{I as Ye}from"./InteractiveAudio.BOik-Knm.js";import{B as oe,S as ae}from"./2.B2AoQPnG.js";import{U as Ze}from"./UploadText.CJcy9n89.js";import{A as dt}from"./AudioPlayer.CnAk5fND.js";import{default as ht}from"./Example.BioRcyBn.js";function ye(t){let e,i;return e=new oe({props:{variant:t[0]===null&&t[25]==="upload"?"dashed":"solid",border_mode:t[27]?"focus":"base",padding:!1,allow_overflow:!1,elem_id:t[4],elem_classes:t[5],visible:t[6],container:t[12],scale:t[13],min_width:t[14],$$slots:{default:[et]},$$scope:{ctx:t}}}),{c(){z(e.$$.fragment)},l(s){B(e.$$.fragment,s)},m(s,r){C(e,s,r),i=!0},p(s,r){const _={};r[0]&33554433&&(_.variant=s[0]===null&&s[25]==="upload"?"dashed":"solid"),r[0]&134217728&&(_.border_mode=s[27]?"focus":"base"),r[0]&16&&(_.elem_id=s[4]),r[0]&32&&(_.elem_classes=s[5]),r[0]&64&&(_.visible=s[6]),r[0]&4096&&(_.container=s[12]),r[0]&8192&&(_.scale=s[13]),r[0]&16384&&(_.min_width=s[14]),r[0]&536710927|r[2]&128&&(_.$$scope={dirty:r,ctx:s}),e.$set(_)},i(s){i||(k(e.$$.fragment,s),i=!0)},o(s){p(e.$$.fragment,s),i=!1},d(s){I(e,s)}}}function $e(t){let e,i;return e=new oe({props:{variant:"solid",border_mode:t[27]?"focus":"base",padding:!1,allow_overflow:!1,elem_id:t[4],elem_classes:t[5],visible:t[6],container:t[12],scale:t[13],min_width:t[14],$$slots:{default:[tt]},$$scope:{ctx:t}}}),{c(){z(e.$$.fragment)},l(s){B(e.$$.fragment,s)},m(s,r){C(e,s,r),i=!0},p(s,r){const _={};r[0]&134217728&&(_.border_mode=s[27]?"focus":"base"),r[0]&16&&(_.elem_id=s[4]),r[0]&32&&(_.elem_classes=s[5]),r[0]&64&&(_.visible=s[6]),r[0]&4096&&(_.container=s[12]),r[0]&8192&&(_.scale=s[13]),r[0]&16384&&(_.min_width=s[14]),r[0]&277842435|r[2]&128&&(_.$$scope={dirty:r,ctx:s}),e.$set(_)},i(s){i||(k(e.$$.fragment,s),i=!0)},o(s){p(e.$$.fragment,s),i=!1},d(s){I(e,s)}}}function xe(t){let e,i;return e=new Ze({props:{i18n:t[23].i18n,type:"audio"}}),{c(){z(e.$$.fragment)},l(s){B(e.$$.fragment,s)},m(s,r){C(e,s,r),i=!0},p(s,r){const _={};r[0]&8388608&&(_.i18n=s[23].i18n),e.$set(_)},i(s){i||(k(e.$$.fragment,s),i=!0)},o(s){p(e.$$.fragment,s),i=!1},d(s){I(e,s)}}}function et(t){let e,i,s,r,_,c,l,a,h;const d=[{autoscroll:t[23].autoscroll},{i18n:t[23].i18n},t[1]];let A={};for(let o=0;o<d.length;o+=1)A=ee(A,d[o]);e=new ae({props:A}),e.$on("clear_status",t[45]);function J(o){t[48](o)}function O(o){t[49](o)}function R(o){t[50](o)}function T(o){t[51](o)}function b(o){t[52](o)}let w={label:t[9],show_label:t[11],show_download_button:t[16],value:t[0],root:t[10],sources:t[8],active_source:t[25],pending:t[20],streaming:t[21],loop:t[15],max_file_size:t[23].max_file_size,handle_reset_value:t[29],editable:t[18],i18n:t[23].i18n,waveform_settings:t[28],waveform_options:t[19],trim_region_settings:t[30],stream_every:t[22],upload:t[46],stream_handler:t[47],$$slots:{default:[xe]},$$scope:{ctx:t}};return t[2]!==void 0&&(w.recording=t[2]),t[27]!==void 0&&(w.dragging=t[27]),t[24]!==void 0&&(w.uploading=t[24]),t[26]!==void 0&&(w.modify_stream=t[26]),t[3]!==void 0&&(w.set_time_limit=t[3]),s=new Ye({props:w}),E.push(()=>N(s,"recording",J)),E.push(()=>N(s,"dragging",O)),E.push(()=>N(s,"uploading",R)),E.push(()=>N(s,"modify_stream",T)),E.push(()=>N(s,"set_time_limit",b)),s.$on("change",t[53]),s.$on("stream",t[54]),s.$on("drag",t[55]),s.$on("edit",t[56]),s.$on("play",t[57]),s.$on("pause",t[58]),s.$on("stop",t[59]),s.$on("start_recording",t[60]),s.$on("pause_recording",t[61]),s.$on("stop_recording",t[62]),s.$on("upload",t[63]),s.$on("clear",t[64]),s.$on("error",t[31]),s.$on("close_stream",t[65]),{c(){z(e.$$.fragment),i=te(),z(s.$$.fragment)},l(o){B(e.$$.fragment,o),i=se(o),B(s.$$.fragment,o)},m(o,u){C(e,o,u),L(o,i,u),C(s,o,u),h=!0},p(o,u){const W=u[0]&8388610?ie(d,[u[0]&8388608&&{autoscroll:o[23].autoscroll},u[0]&8388608&&{i18n:o[23].i18n},u[0]&2&&ne(o[1])]):{};e.$set(W);const f={};u[0]&512&&(f.label=o[9]),u[0]&2048&&(f.show_label=o[11]),u[0]&65536&&(f.show_download_button=o[16]),u[0]&1&&(f.value=o[0]),u[0]&1024&&(f.root=o[10]),u[0]&256&&(f.sources=o[8]),u[0]&33554432&&(f.active_source=o[25]),u[0]&1048576&&(f.pending=o[20]),u[0]&2097152&&(f.streaming=o[21]),u[0]&32768&&(f.loop=o[15]),u[0]&8388608&&(f.max_file_size=o[23].max_file_size),u[0]&262144&&(f.editable=o[18]),u[0]&8388608&&(f.i18n=o[23].i18n),u[0]&268435456&&(f.waveform_settings=o[28]),u[0]&524288&&(f.waveform_options=o[19]),u[0]&4194304&&(f.stream_every=o[22]),u[0]&8388608&&(f.upload=o[46]),u[0]&8388608&&(f.stream_handler=o[47]),u[0]&8388608|u[2]&128&&(f.$$scope={dirty:u,ctx:o}),!r&&u[0]&4&&(r=!0,f.recording=o[2],U(()=>r=!1)),!_&&u[0]&134217728&&(_=!0,f.dragging=o[27],U(()=>_=!1)),!c&&u[0]&16777216&&(c=!0,f.uploading=o[24],U(()=>c=!1)),!l&&u[0]&67108864&&(l=!0,f.modify_stream=o[26],U(()=>l=!1)),!a&&u[0]&8&&(a=!0,f.set_time_limit=o[3],U(()=>a=!1)),s.$set(f)},i(o){h||(k(e.$$.fragment,o),k(s.$$.fragment,o),h=!0)},o(o){p(e.$$.fragment,o),p(s.$$.fragment,o),h=!1},d(o){o&&Q(i),I(e,o),I(s,o)}}}function tt(t){let e,i,s,r;const _=[{autoscroll:t[23].autoscroll},{i18n:t[23].i18n},t[1]];let c={};for(let l=0;l<_.length;l+=1)c=ee(c,_[l]);return e=new ae({props:c}),e.$on("clear_status",t[39]),s=new Xe({props:{i18n:t[23].i18n,show_label:t[11],show_download_button:t[16],show_share_button:t[17],value:t[0],label:t[9],loop:t[15],waveform_settings:t[28],waveform_options:t[19],editable:t[18]}}),s.$on("share",t[40]),s.$on("error",t[41]),s.$on("play",t[42]),s.$on("pause",t[43]),s.$on("stop",t[44]),{c(){z(e.$$.fragment),i=te(),z(s.$$.fragment)},l(l){B(e.$$.fragment,l),i=se(l),B(s.$$.fragment,l)},m(l,a){C(e,l,a),L(l,i,a),C(s,l,a),r=!0},p(l,a){const h=a[0]&8388610?ie(_,[a[0]&8388608&&{autoscroll:l[23].autoscroll},a[0]&8388608&&{i18n:l[23].i18n},a[0]&2&&ne(l[1])]):{};e.$set(h);const d={};a[0]&8388608&&(d.i18n=l[23].i18n),a[0]&2048&&(d.show_label=l[11]),a[0]&65536&&(d.show_download_button=l[16]),a[0]&131072&&(d.show_share_button=l[17]),a[0]&1&&(d.value=l[0]),a[0]&512&&(d.label=l[9]),a[0]&32768&&(d.loop=l[15]),a[0]&268435456&&(d.waveform_settings=l[28]),a[0]&524288&&(d.waveform_options=l[19]),a[0]&262144&&(d.editable=l[18]),s.$set(d)},i(l){r||(k(e.$$.fragment,l),k(s.$$.fragment,l),r=!0)},o(l){p(e.$$.fragment,l),p(s.$$.fragment,l),r=!1},d(l){l&&Q(i),I(e,l),I(s,l)}}}function st(t){let e,i,s,r;const _=[$e,ye],c=[];function l(a,h){return a[7]?1:0}return e=l(t),i=c[e]=_[e](t),{c(){i.c(),s=x()},l(a){i.l(a),s=x()},m(a,h){c[e].m(a,h),L(a,s,h),r=!0},p(a,h){let d=e;e=l(a),e===d?c[e].p(a,h):(He(),p(c[d],1,1,()=>{c[d]=null}),Ke(),i=c[e],i?i.p(a,h):(i=c[e]=_[e](a),i.c()),k(i,1),i.m(s.parentNode,s))},i(a){r||(k(i),r=!0)},o(a){p(i),r=!1},d(a){a&&Q(s),c[e].d(a)}}}function it(t,e,i){let{value_is_output:s=!1}=e,{elem_id:r=""}=e,{elem_classes:_=[]}=e,{visible:c=!0}=e,{interactive:l}=e,{value:a=null}=e,{sources:h}=e,{label:d}=e,{root:A}=e,{show_label:J}=e,{container:O=!0}=e,{scale:R=null}=e,{min_width:T=void 0}=e,{loading_status:b}=e,{autoplay:w=!1}=e,{loop:o=!1}=e,{show_download_button:u}=e,{show_share_button:W=!1}=e,{editable:f=!0}=e,{waveform_options:S={show_recording_waveform:!0}}=e,{pending:X}=e,{streaming:Y}=e,{stream_every:Z}=e,{input_ready:D}=e,{recording:j=!1}=e,q=!1,y="closed",G;function re(n){y=n,G(n)}const le=()=>y;let{set_time_limit:M}=e,{gradio:g}=e,F=null,H,P=a;const _e=()=>{P===null||a===P||i(0,a=P)};let V,v,K="darkorange";Le(()=>{K=getComputedStyle(document==null?void 0:document.documentElement).getPropertyValue("--color-accent"),ue(),i(28,v.waveColor=S.waveform_color||"#9ca3af",v),i(28,v.progressColor=S.waveform_progress_color||K,v),i(28,v.mediaControls=S.show_controls,v),i(28,v.sampleRate=S.sample_rate||44100,v)});const $={color:S.trim_region_color,drag:!0,resize:!0};function ue(){document.documentElement.style.setProperty("--trim-region-color",$.color||K)}function fe({detail:n}){const[Ge,Me]=n.includes("Invalid file type")?["warning","complete"]:["error","error"];i(1,b=b||{}),i(1,b.status=Me,b),i(1,b.message=n,b),g.dispatch(Ge,n)}Qe(()=>{i(32,s=!1)});const me=()=>g.dispatch("clear_status",b),ge=n=>g.dispatch("share",n.detail),de=n=>g.dispatch("error",n.detail),ce=()=>g.dispatch("play"),he=()=>g.dispatch("pause"),be=()=>g.dispatch("stop"),we=()=>g.dispatch("clear_status",b),ve=(...n)=>g.client.upload(...n),pe=(...n)=>g.client.stream(...n);function ke(n){j=n,i(2,j)}function Se(n){V=n,i(27,V)}function ze(n){q=n,i(24,q)}function Be(n){G=n,i(26,G)}function Ce(n){M=n,i(3,M)}const Ie=({detail:n})=>i(0,a=n),Ae=({detail:n})=>{i(0,a=n),g.dispatch("stream",a)},Pe=({detail:n})=>i(27,V=n),Ee=()=>g.dispatch("edit"),Ne=()=>g.dispatch("play"),Ue=()=>g.dispatch("pause"),Je=()=>g.dispatch("stop"),Oe=()=>g.dispatch("start_recording"),Re=()=>g.dispatch("pause_recording"),Te=n=>g.dispatch("stop_recording"),We=()=>g.dispatch("upload"),je=()=>g.dispatch("clear"),qe=()=>g.dispatch("close_stream","stream");return t.$$set=n=>{"value_is_output"in n&&i(32,s=n.value_is_output),"elem_id"in n&&i(4,r=n.elem_id),"elem_classes"in n&&i(5,_=n.elem_classes),"visible"in n&&i(6,c=n.visible),"interactive"in n&&i(7,l=n.interactive),"value"in n&&i(0,a=n.value),"sources"in n&&i(8,h=n.sources),"label"in n&&i(9,d=n.label),"root"in n&&i(10,A=n.root),"show_label"in n&&i(11,J=n.show_label),"container"in n&&i(12,O=n.container),"scale"in n&&i(13,R=n.scale),"min_width"in n&&i(14,T=n.min_width),"loading_status"in n&&i(1,b=n.loading_status),"autoplay"in n&&i(34,w=n.autoplay),"loop"in n&&i(15,o=n.loop),"show_download_button"in n&&i(16,u=n.show_download_button),"show_share_button"in n&&i(17,W=n.show_share_button),"editable"in n&&i(18,f=n.editable),"waveform_options"in n&&i(19,S=n.waveform_options),"pending"in n&&i(20,X=n.pending),"streaming"in n&&i(21,Y=n.streaming),"stream_every"in n&&i(22,Z=n.stream_every),"input_ready"in n&&i(33,D=n.input_ready),"recording"in n&&i(2,j=n.recording),"set_time_limit"in n&&i(3,M=n.set_time_limit),"gradio"in n&&i(23,g=n.gradio)},t.$$.update=()=>{t.$$.dirty[0]&16777216&&i(33,D=!q),t.$$.dirty[0]&1|t.$$.dirty[1]&128&&a&&P===null&&i(38,P=a),t.$$.dirty[0]&8388609|t.$$.dirty[1]&66&&JSON.stringify(a)!==JSON.stringify(F)&&(i(37,F=a),g.dispatch("change"),s||g.dispatch("input")),t.$$.dirty[0]&33554688&&!H&&h&&i(25,H=h[0]),t.$$.dirty[1]&8&&i(28,v={height:50,barWidth:2,barGap:3,cursorWidth:2,cursorColor:"#ddd5e9",autoplay:w,barRadius:10,dragToSeek:!0,normalize:!0,minPxPerSec:20})},[a,b,j,M,r,_,c,l,h,d,A,J,O,R,T,o,u,W,f,S,X,Y,Z,g,q,H,G,V,v,_e,$,fe,s,D,w,re,le,F,P,me,ge,de,ce,he,be,we,ve,pe,ke,Se,ze,Be,Ce,Ie,Ae,Pe,Ee,Ne,Ue,Je,Oe,Re,Te,We,je,qe]}class nt extends Ve{constructor(e){super(),De(this,e,it,st,Fe,{value_is_output:32,elem_id:4,elem_classes:5,visible:6,interactive:7,value:0,sources:8,label:9,root:10,show_label:11,container:12,scale:13,min_width:14,loading_status:1,autoplay:34,loop:15,show_download_button:16,show_share_button:17,editable:18,waveform_options:19,pending:20,streaming:21,stream_every:22,input_ready:33,recording:2,modify_stream_state:35,get_stream_state:36,set_time_limit:3,gradio:23},null,[-1,-1,-1])}get value_is_output(){return this.$$.ctx[32]}set value_is_output(e){this.$$set({value_is_output:e}),m()}get elem_id(){return this.$$.ctx[4]}set elem_id(e){this.$$set({elem_id:e}),m()}get elem_classes(){return this.$$.ctx[5]}set elem_classes(e){this.$$set({elem_classes:e}),m()}get visible(){return this.$$.ctx[6]}set visible(e){this.$$set({visible:e}),m()}get interactive(){return this.$$.ctx[7]}set interactive(e){this.$$set({interactive:e}),m()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),m()}get sources(){return this.$$.ctx[8]}set sources(e){this.$$set({sources:e}),m()}get label(){return this.$$.ctx[9]}set label(e){this.$$set({label:e}),m()}get root(){return this.$$.ctx[10]}set root(e){this.$$set({root:e}),m()}get show_label(){return this.$$.ctx[11]}set show_label(e){this.$$set({show_label:e}),m()}get container(){return this.$$.ctx[12]}set container(e){this.$$set({container:e}),m()}get scale(){return this.$$.ctx[13]}set scale(e){this.$$set({scale:e}),m()}get min_width(){return this.$$.ctx[14]}set min_width(e){this.$$set({min_width:e}),m()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),m()}get autoplay(){return this.$$.ctx[34]}set autoplay(e){this.$$set({autoplay:e}),m()}get loop(){return this.$$.ctx[15]}set loop(e){this.$$set({loop:e}),m()}get show_download_button(){return this.$$.ctx[16]}set show_download_button(e){this.$$set({show_download_button:e}),m()}get show_share_button(){return this.$$.ctx[17]}set show_share_button(e){this.$$set({show_share_button:e}),m()}get editable(){return this.$$.ctx[18]}set editable(e){this.$$set({editable:e}),m()}get waveform_options(){return this.$$.ctx[19]}set waveform_options(e){this.$$set({waveform_options:e}),m()}get pending(){return this.$$.ctx[20]}set pending(e){this.$$set({pending:e}),m()}get streaming(){return this.$$.ctx[21]}set streaming(e){this.$$set({streaming:e}),m()}get stream_every(){return this.$$.ctx[22]}set stream_every(e){this.$$set({stream_every:e}),m()}get input_ready(){return this.$$.ctx[33]}set input_ready(e){this.$$set({input_ready:e}),m()}get recording(){return this.$$.ctx[2]}set recording(e){this.$$set({recording:e}),m()}get modify_stream_state(){return this.$$.ctx[35]}get get_stream_state(){return this.$$.ctx[36]}get set_time_limit(){return this.$$.ctx[3]}set set_time_limit(e){this.$$set({set_time_limit:e}),m()}get gradio(){return this.$$.ctx[23]}set gradio(e){this.$$set({gradio:e}),m()}}const ft=nt;export{ht as BaseExample,Ye as BaseInteractiveAudio,dt as BasePlayer,Xe as BaseStaticAudio,ft as default};
//# sourceMappingURL=index.-LvVKGMO.js.map
