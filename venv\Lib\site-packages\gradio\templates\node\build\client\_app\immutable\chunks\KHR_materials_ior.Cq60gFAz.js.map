{"version": 3, "file": "KHR_materials_ior.Cq60gFAz.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_materials_ior.js"], "sourcesContent": ["import { PBRMaterial } from \"@babylonjs/core/Materials/PBR/pbrMaterial.js\";\nimport { GLTFLoader } from \"../glTFLoader.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"KHR_materials_ior\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_ior/README.md)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_materials_ior {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        /**\n         * Defines a number that determines the order the extensions are applied.\n         */\n        this.order = 180;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /**\n     * @internal\n     */\n    loadMaterialPropertiesAsync(context, material, babylonMaterial) {\n        return GLTFLoader.LoadExtensionAsync(context, material, this.name, (extensionContext, extension) => {\n            const promises = new Array();\n            promises.push(this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial));\n            promises.push(this._loadIorPropertiesAsync(extensionContext, extension, babylonMaterial));\n            return Promise.all(promises).then(() => { });\n        });\n    }\n    _loadIorPropertiesAsync(context, properties, babylonMaterial) {\n        if (!(babylonMaterial instanceof PBRMaterial)) {\n            throw new Error(`${context}: Material type not supported`);\n        }\n        if (properties.ior !== undefined) {\n            babylonMaterial.indexOfRefraction = properties.ior;\n        }\n        else {\n            babylonMaterial.indexOfRefraction = KHR_materials_ior._DEFAULT_IOR;\n        }\n        return Promise.resolve();\n    }\n}\n/**\n * Default ior Value from the spec.\n */\nKHR_materials_ior._DEFAULT_IOR = 1.5;\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_materials_ior(loader));\n//# sourceMappingURL=KHR_materials_ior.js.map"], "names": ["NAME", "KHR_materials_ior", "loader", "context", "material", "babylonMaterial", "GLTFLoader", "extensionContext", "extension", "promises", "properties", "PBRMaterial", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "+GAGA,MAAMA,EAAO,oBAKN,MAAMC,CAAkB,CAI3B,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EAIZ,KAAK,MAAQ,IACb,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,CACnD,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAID,4BAA4BG,EAASC,EAAUC,EAAiB,CAC5D,OAAOC,EAAW,mBAAmBH,EAASC,EAAU,KAAK,KAAM,CAACG,EAAkBC,IAAc,CAChG,MAAMC,EAAW,IAAI,MACrB,OAAAA,EAAS,KAAK,KAAK,QAAQ,4BAA4BN,EAASC,EAAUC,CAAe,CAAC,EAC1FI,EAAS,KAAK,KAAK,wBAAwBF,EAAkBC,EAAWH,CAAe,CAAC,EACjF,QAAQ,IAAII,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CACvD,CAAS,CACJ,CACD,wBAAwBN,EAASO,EAAYL,EAAiB,CAC1D,GAAI,EAAEA,aAA2BM,GAC7B,MAAM,IAAI,MAAM,GAAGR,CAAO,+BAA+B,EAE7D,OAAIO,EAAW,MAAQ,OACnBL,EAAgB,kBAAoBK,EAAW,IAG/CL,EAAgB,kBAAoBJ,EAAkB,aAEnD,QAAQ,SAClB,CACL,CAIAA,EAAkB,aAAe,IACjCW,EAAwBZ,CAAI,EAC5Ba,EAAsBb,EAAM,GAAOE,GAAW,IAAID,EAAkBC,CAAM,CAAC", "x_google_ignoreList": [0]}