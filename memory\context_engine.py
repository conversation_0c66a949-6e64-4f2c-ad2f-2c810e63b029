"""
Context Engine - 类似Augment的上下文引擎
支持长对话记忆、角色扮演记忆和智能上下文管理
"""

import json
import sqlite3
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib
import numpy as np

from core.utils.logger import get_logger
from core.utils.config import config_manager
from memory.history_manager import history_manager, ChatMessage, ChatSession

logger = get_logger(__name__)

@dataclass
class MemoryEntry:
    """记忆条目"""
    id: str
    content: str
    role: str  # user, assistant, system
    timestamp: datetime
    persona: str
    importance: float  # 重要性评分 0-1
    embedding: Optional[List[float]] = None
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class ConversationContext:
    """对话上下文"""
    session_id: str
    persona: str
    messages: List[MemoryEntry]
    summary: str
    last_updated: datetime
    total_tokens: int

class ContextEngine:
    """上下文引擎"""
    
    def __init__(self, storage_path: str = None):
        self.storage_path = storage_path or config_manager.get('memory.storage_path', 'memory/storage')
        self.max_memory_size = config_manager.get('memory.max_memory_size', 10000)
        self.context_window = config_manager.get('memory.context_window', 8000)
        self.enable_long_term = config_manager.get('memory.enable_long_term_memory', True)
        
        self.lock = threading.Lock()
        self.current_session: Optional[ConversationContext] = None
        
        # 初始化存储
        self._init_storage()
        
        # 内存缓存
        self.memory_cache: Dict[str, MemoryEntry] = {}
        self.session_cache: Dict[str, ConversationContext] = {}
    
    def _init_storage(self):
        """初始化存储"""
        Path(self.storage_path).mkdir(parents=True, exist_ok=True)
        
        self.db_path = Path(self.storage_path) / "memory.db"
        
        with sqlite3.connect(self.db_path) as conn:
            # 创建记忆表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS memories (
                    id TEXT PRIMARY KEY,
                    content TEXT NOT NULL,
                    role TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    persona TEXT NOT NULL,
                    importance REAL NOT NULL,
                    embedding TEXT,
                    metadata TEXT
                )
            """)
            
            # 创建会话表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS sessions (
                    session_id TEXT PRIMARY KEY,
                    persona TEXT NOT NULL,
                    summary TEXT,
                    last_updated TEXT NOT NULL,
                    total_tokens INTEGER DEFAULT 0
                )
            """)
            
            # 创建会话消息关联表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS session_messages (
                    session_id TEXT,
                    memory_id TEXT,
                    message_order INTEGER,
                    FOREIGN KEY (session_id) REFERENCES sessions (session_id),
                    FOREIGN KEY (memory_id) REFERENCES memories (id)
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_memories_persona ON memories (persona)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_memories_timestamp ON memories (timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_memories_importance ON memories (importance)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_sessions_persona ON sessions (persona)")
            
            conn.commit()
    
    def _generate_memory_id(self, content: str, timestamp: datetime) -> str:
        """生成记忆ID"""
        data = f"{content}{timestamp.isoformat()}"
        return hashlib.md5(data.encode()).hexdigest()
    
    def _calculate_importance(self, content: str, role: str, persona: str) -> float:
        """计算记忆重要性"""
        importance = 0.5  # 基础重要性
        
        # 根据角色调整
        if role == "user":
            importance += 0.2
        elif role == "system":
            importance += 0.3
        
        # 根据内容长度调整
        if len(content) > 100:
            importance += 0.1
        
        # 关键词检测
        keywords = ["重要", "记住", "不要忘记", "关键", "核心", "必须"]
        for keyword in keywords:
            if keyword in content:
                importance += 0.2
                break
        
        # 问题检测
        if "?" in content or "？" in content:
            importance += 0.1
        
        return min(importance, 1.0)
    
    def add_memory(self, content: str, role: str, persona: str, 
                   metadata: Optional[Dict[str, Any]] = None) -> str:
        """添加记忆"""
        timestamp = datetime.now()
        memory_id = self._generate_memory_id(content, timestamp)
        importance = self._calculate_importance(content, role, persona)
        
        memory = MemoryEntry(
            id=memory_id,
            content=content,
            role=role,
            timestamp=timestamp,
            persona=persona,
            importance=importance,
            metadata=metadata or {}
        )
        
        with self.lock:
            # 保存到数据库
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO memories 
                    (id, content, role, timestamp, persona, importance, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    memory.id,
                    memory.content,
                    memory.role,
                    memory.timestamp.isoformat(),
                    memory.persona,
                    memory.importance,
                    json.dumps(memory.metadata)
                ))
                conn.commit()
            
            # 添加到缓存
            self.memory_cache[memory_id] = memory
            
            # 添加到当前会话
            if self.current_session:
                self.current_session.messages.append(memory)
                self._update_session_in_db()

                # 同时保存到JSON历史文件
                try:
                    history_manager.add_message_to_session(
                        session_id=self.current_session.session_id,
                        role=role,
                        content=content,
                        persona=persona
                    )
                except Exception as e:
                    logger.error(f"保存到历史文件失败: {e}")

        logger.info(f"添加记忆: {memory_id[:8]}... (重要性: {importance:.2f})")
        return memory_id
    
    def get_relevant_memories(self, query: str, persona: str, 
                            limit: int = 10) -> List[MemoryEntry]:
        """获取相关记忆"""
        with sqlite3.connect(self.db_path) as conn:
            # 简单的关键词匹配（可以后续升级为向量搜索）
            cursor = conn.execute("""
                SELECT id, content, role, timestamp, persona, importance, metadata
                FROM memories
                WHERE persona = ? AND (
                    content LIKE ? OR 
                    content LIKE ? OR
                    content LIKE ?
                )
                ORDER BY importance DESC, timestamp DESC
                LIMIT ?
            """, (persona, f"%{query}%", f"%{query.lower()}%", f"%{query.upper()}%", limit))
            
            memories = []
            for row in cursor.fetchall():
                memory = MemoryEntry(
                    id=row[0],
                    content=row[1],
                    role=row[2],
                    timestamp=datetime.fromisoformat(row[3]),
                    persona=row[4],
                    importance=row[5],
                    metadata=json.loads(row[6]) if row[6] else {}
                )
                memories.append(memory)
            
            return memories
    
    def start_session(self, persona: str, session_id: Optional[str] = None) -> str:
        """开始新会话"""
        if not session_id:
            session_id = f"{persona}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.current_session = ConversationContext(
            session_id=session_id,
            persona=persona,
            messages=[],
            summary="",
            last_updated=datetime.now(),
            total_tokens=0
        )
        
        # 保存到数据库
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO sessions 
                (session_id, persona, summary, last_updated, total_tokens)
                VALUES (?, ?, ?, ?, ?)
            """, (
                session_id,
                persona,
                "",
                datetime.now().isoformat(),
                0
            ))
            conn.commit()
        
        logger.info(f"开始新会话: {session_id}")
        return session_id
    
    def get_context_for_generation(self, max_tokens: int = None) -> List[Dict[str, str]]:
        """获取用于生成的上下文"""
        if not self.current_session:
            return []
        
        max_tokens = max_tokens or self.context_window
        context = []
        current_tokens = 0
        
        # 从最新消息开始倒序添加
        for memory in reversed(self.current_session.messages):
            # 简单的token估算（1个字符约等于1个token）
            estimated_tokens = len(memory.content)
            
            if current_tokens + estimated_tokens > max_tokens:
                break
            
            context.insert(0, {
                "role": memory.role,
                "content": memory.content
            })
            current_tokens += estimated_tokens
        
        return context
    
    def _update_session_in_db(self):
        """更新会话到数据库"""
        if not self.current_session:
            return
        
        with sqlite3.connect(self.db_path) as conn:
            # 更新会话信息
            conn.execute("""
                UPDATE sessions 
                SET last_updated = ?, total_tokens = ?
                WHERE session_id = ?
            """, (
                datetime.now().isoformat(),
                self.current_session.total_tokens,
                self.current_session.session_id
            ))
            
            # 清除旧的消息关联
            conn.execute("""
                DELETE FROM session_messages WHERE session_id = ?
            """, (self.current_session.session_id,))
            
            # 添加新的消息关联
            for i, memory in enumerate(self.current_session.messages):
                conn.execute("""
                    INSERT INTO session_messages (session_id, memory_id, message_order)
                    VALUES (?, ?, ?)
                """, (self.current_session.session_id, memory.id, i))
            
            conn.commit()
    
    def load_session(self, session_id: str) -> bool:
        """加载会话 - 从JSON历史文件中加载"""
        try:
            # 从JSON历史文件加载会话
            chat_session = history_manager.load_session(session_id)
            if not chat_session:
                logger.warning(f"会话不存在: {session_id}")
                return False

            # 转换ChatMessage到MemoryEntry
            messages = []
            for chat_msg in chat_session.messages:
                memory = MemoryEntry(
                    id=self._generate_memory_id(chat_msg.content,
                                               datetime.fromisoformat(chat_msg.timestamp)),
                    content=chat_msg.content,
                    role=chat_msg.role,
                    timestamp=datetime.fromisoformat(chat_msg.timestamp),
                    persona=chat_msg.persona or chat_session.persona,
                    importance=self._calculate_importance(chat_msg.content, chat_msg.role,
                                                        chat_msg.persona or chat_session.persona),
                    metadata=chat_msg.metadata or {}
                )
                messages.append(memory)

            # 创建会话对象
            self.current_session = ConversationContext(
                session_id=chat_session.session_id,
                persona=chat_session.persona,
                messages=messages,
                summary=chat_session.title,
                last_updated=datetime.fromisoformat(chat_session.last_updated),
                total_tokens=chat_session.total_messages * 50  # 估算token数
            )

            logger.info(f"会话加载成功: {session_id}")
            return True

        except Exception as e:
            logger.error(f"会话加载失败: {e}")
            return False
    
    def get_session_list(self, persona: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取会话列表 - 从JSON历史文件中读取"""
        try:
            # 从JSON历史文件获取会话列表
            sessions = history_manager.get_session_list()

            # 如果指定了persona，进行过滤
            if persona:
                sessions = [s for s in sessions if s.get('persona') == persona]

            # 转换格式以保持兼容性
            result = []
            for session in sessions:
                result.append({
                    'session_id': session['session_id'],
                    'persona': session['persona'],
                    'summary': session['title'],  # 使用title作为summary
                    'last_updated': session['last_updated'],
                    'total_tokens': session['total_messages'] * 50  # 估算token数
                })

            return result

        except Exception as e:
            logger.error(f"获取会话列表失败: {e}")
            return []
    
    def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        try:
            # 从JSON历史文件中删除
            success = history_manager.delete_session(session_id)
            
            if success:
                # 从数据库中删除相关记录
                with sqlite3.connect(self.db_path) as conn:
                    # 删除会话消息关联
                    conn.execute("DELETE FROM session_messages WHERE session_id = ?", (session_id,))
                    
                    # 删除会话记录
                    conn.execute("DELETE FROM sessions WHERE session_id = ?", (session_id,))
                    
                    conn.commit()
                
                # 如果是当前会话，清空当前会话
                if self.current_session and self.current_session.session_id == session_id:
                    self.current_session = None
                
                logger.info(f"会话删除成功: {session_id}")
                return True
            else:
                logger.warning(f"会话删除失败: {session_id}")
                return False
                
        except Exception as e:
            logger.error(f"删除会话时出错: {e}")
            return False
    
    def clear_old_memories(self, days: int = 30):
        """清理旧记忆"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                DELETE FROM memories 
                WHERE timestamp < ? AND importance < 0.7
            """, (cutoff_date.isoformat(),))
            
            deleted_count = cursor.rowcount
            conn.commit()
        
        logger.info(f"清理了 {deleted_count} 条旧记忆")

# 全局上下文引擎实例
context_engine = ContextEngine()
