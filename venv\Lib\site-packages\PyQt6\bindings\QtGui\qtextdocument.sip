// qtextdocument.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace Qt
{
%TypeHeaderCode
#include <qtextdocument.h>
%End

%If (Qt_6_7_0 -)
    bool mightBeRichText(QAnyStringView);
%End
%If (- Qt_6_7_0)
    bool mightBeRichText(const QString &);
%End
    QString convertFromPlainText(const QString &plain, Qt::WhiteSpaceMode mode = Qt::WhiteSpacePre);
};

class QTextDocument : public QObject
{
%TypeHeaderCode
#include <qtextdocument.h>
%End

public:
    explicit QTextDocument(QObject *parent /TransferThis/ = 0);
    QTextDocument(const QString &text, QObject *parent /TransferThis/ = 0);
    virtual ~QTextDocument();
    QTextDocument *clone(QObject *parent /TransferThis/ = 0) const /Factory/;
    bool isEmpty() const;
    virtual void clear();
    void setUndoRedoEnabled(bool enable);
    bool isUndoRedoEnabled() const;
    bool isUndoAvailable() const;
    bool isRedoAvailable() const;
    void setDocumentLayout(QAbstractTextDocumentLayout *layout /Transfer/);
    QAbstractTextDocumentLayout *documentLayout() const;

    enum MetaInformation
    {
        DocumentTitle,
        DocumentUrl,
%If (Qt_6_3_0 -)
        CssMedia,
%End
%If (Qt_6_8_0 -)
        FrontMatter,
%End
    };

    void setMetaInformation(QTextDocument::MetaInformation info, const QString &);
    QString metaInformation(QTextDocument::MetaInformation info) const;
    QString toHtml() const;
    void setHtml(const QString &html);
    QString toPlainText() const;
    void setPlainText(const QString &text);

    enum FindFlag /BaseType=Flag/
    {
        FindBackward,
        FindCaseSensitively,
        FindWholeWords,
    };

    typedef QFlags<QTextDocument::FindFlag> FindFlags;
    QTextCursor find(const QRegularExpression &expr, const QTextCursor &cursor, QTextDocument::FindFlags options = QTextDocument::FindFlags()) const;
    QTextCursor find(const QRegularExpression &expr, int position = 0, QTextDocument::FindFlags options = QTextDocument::FindFlags()) const;
    QTextCursor find(const QString &subString, const QTextCursor &cursor, QTextDocument::FindFlags options = QTextDocument::FindFlags()) const;
    QTextCursor find(const QString &subString, int position = 0, QTextDocument::FindFlags options = QTextDocument::FindFlags()) const;
    QTextFrame *rootFrame() const;
    QTextObject *object(int objectIndex) const;
    QTextObject *objectForFormat(const QTextFormat &) const;
    QTextBlock findBlock(int pos) const;
    QTextBlock begin() const;
    QTextBlock end() const;
    void setPageSize(const QSizeF &size);
    QSizeF pageSize() const;
    void setDefaultFont(const QFont &font);
    QFont defaultFont() const;
    int pageCount() const;
    bool isModified() const;
%If (PyQt_Printer)
    void print(QPagedPaintDevice *printer) const;
%End

    enum ResourceType /BaseType=IntEnum/
    {
        UnknownResource,
        HtmlResource,
        ImageResource,
        StyleSheetResource,
        MarkdownResource,
        UserResource,
    };

    QVariant resource(int type, const QUrl &name) const;
    void addResource(int type, const QUrl &name, const QVariant &resource);
    QList<QTextFormat> allFormats() const;
    void markContentsDirty(int from, int length);
    void setUseDesignMetrics(bool b);
    bool useDesignMetrics() const;

signals:
    void blockCountChanged(int newBlockCount);
    void contentsChange(int from, int charsRemoves, int charsAdded);
    void contentsChanged();
    void cursorPositionChanged(const QTextCursor &cursor);
    void modificationChanged(bool m);
    void redoAvailable(bool);
    void undoAvailable(bool);

public slots:
    void undo();
    void redo();
    void setModified(bool on = true);

protected:
    virtual QTextObject *createObject(const QTextFormat &f) /Factory/;
    virtual QVariant loadResource(int type, const QUrl &name);

public:
    void drawContents(QPainter *p, const QRectF &rect = QRectF());
    void setTextWidth(qreal width);
    qreal textWidth() const;
    qreal idealWidth() const;
    void adjustSize();
    QSizeF size() const;
    int blockCount() const;
    void setDefaultStyleSheet(const QString &sheet);
    QString defaultStyleSheet() const;
    void undo(QTextCursor *cursor);
    void redo(QTextCursor *cursor);
    int maximumBlockCount() const;
    void setMaximumBlockCount(int maximum);
    QTextOption defaultTextOption() const;
    void setDefaultTextOption(const QTextOption &option);
    int revision() const;
    QTextBlock findBlockByNumber(int blockNumber) const;
    QTextBlock findBlockByLineNumber(int blockNumber) const;
    QTextBlock firstBlock() const;
    QTextBlock lastBlock() const;
    qreal indentWidth() const;
    void setIndentWidth(qreal width);

signals:
    void undoCommandAdded();
    void documentLayoutChanged();

public:
    QChar characterAt(int pos) const;
    qreal documentMargin() const;
    void setDocumentMargin(qreal margin);
    int lineCount() const;
    int characterCount() const;
    int availableUndoSteps() const;
    int availableRedoSteps() const;

    enum Stacks
    {
        UndoStack,
        RedoStack,
        UndoAndRedoStacks,
    };

    void clearUndoRedoStacks(QTextDocument::Stacks stacks = QTextDocument::UndoAndRedoStacks);
    Qt::CursorMoveStyle defaultCursorMoveStyle() const;
    void setDefaultCursorMoveStyle(Qt::CursorMoveStyle style);
    QUrl baseUrl() const;
    void setBaseUrl(const QUrl &url);

signals:
    void baseUrlChanged(const QUrl &url);

public:
    QString toRawText() const;

    enum MarkdownFeature /BaseType=Flag/
    {
        MarkdownNoHTML,
        MarkdownDialectCommonMark,
        MarkdownDialectGitHub,
    };

    typedef QFlags<QTextDocument::MarkdownFeature> MarkdownFeatures;
    QString toMarkdown(QTextDocument::MarkdownFeatures features = QTextDocument::MarkdownDialectGitHub) const;
    void setMarkdown(const QString &markdown, QTextDocument::MarkdownFeatures features = QTextDocument::MarkdownDialectGitHub);
    void setSuperScriptBaseline(qreal baseline);
    qreal superScriptBaseline() const;
    void setSubScriptBaseline(qreal baseline);
    qreal subScriptBaseline() const;
    void setBaselineOffset(qreal baseline);
    qreal baselineOffset() const;
%If (Qt_6_1_0 -)
    SIP_PYCALLABLE resourceProvider() const /TypeHint="Callable[[QUrl], QVariant]"/;
%MethodCode
        if (sipCpp->resourceProvider())
            sipRes = sipGetUserObject((sipSimpleWrapper *)sipSelf);
        else
            sipRes = SIP_NULLPTR;
        
        if (!sipRes)
            sipRes = Py_None;
        
        Py_INCREF(sipRes);
%End

%End
%If (Qt_6_1_0 -)
    void setResourceProvider(SIP_PYCALLABLE provider /TypeHint="Callable[[QUrl], QVariant]"/);
%MethodCode
        // Remove any existing callable.
        Py_XDECREF(sipGetUserObject((sipSimpleWrapper *)sipSelf));
        
        if (a0 == Py_None)
        {
            sipSetUserObject((sipSimpleWrapper *)sipSelf, SIP_NULLPTR);
            sipCpp->setResourceProvider(SIP_NULLPTR);
        }
        else
        {
            // Save the callable so that resourceProvider() can return it.
            Py_INCREF(a0);
            sipSetUserObject((sipSimpleWrapper *)sipSelf, a0);
        
            Py_BEGIN_ALLOW_THREADS
        
            sipCpp->setResourceProvider([a0] (const QUrl &arg) {
                QUrl *arg_heap = new QUrl(arg);
                QVariant qv;
                int is_err = 1;
        
                SIP_BLOCK_THREADS
        
                PyObject *arg_obj = sipConvertFromNewType(arg_heap, sipType_QUrl, NULL);
        
                if (arg_obj)
                {
                    PyObject *res_obj = PyObject_CallFunctionObjArgs(a0, arg_obj, NULL);
        
                    Py_DECREF(arg_obj);
        
                    if (res_obj)
                    {
                        is_err = 0;
        
                        QVariant *res = reinterpret_cast<QVariant *>(
                                sipConvertToType(res_obj, sipType_QVariant, NULL, 0,
                                        NULL, &is_err));
        
                        if (!is_err)
                        {
                            qv = *res;
                            delete res;
                        }
                    }
                }
                else
                {
                    delete arg_heap;
                }
        
                if (is_err)
                {
                    pyqt6_qtgui_err_print();
                }
        
                SIP_UNBLOCK_THREADS
        
                return qv;
            });
        
            Py_END_ALLOW_THREADS
        }
%End

%End
%If (Qt_6_1_0 -)
    static SIP_PYCALLABLE defaultResourceProvider() /TypeHint="Callable[[QUrl], QVariant]"/;
%MethodCode
        if (QTextDocument::defaultResourceProvider())
            sipRes = (PyObject *)sipGetTypeUserData((sipWrapperType *)sipTypeAsPyTypeObject(sipType_QTextDocument));
        else
            sipRes = SIP_NULLPTR;
        
        if (!sipRes)
            sipRes = Py_None;
        
        Py_INCREF(sipRes);
%End

%End
%If (Qt_6_1_0 -)
    static void setDefaultResourceProvider(SIP_PYCALLABLE provider /TypeHint="Callable[[QUrl], QVariant]"/);
%MethodCode
        sipWrapperType *wt = (sipWrapperType *)sipTypeAsPyTypeObject(sipType_QTextDocument);
        
        // Remove any existing callable.
        Py_XDECREF((PyObject *)sipGetTypeUserData(wt));
        
        if (a0 == Py_None)
        {
            sipSetTypeUserData(wt, SIP_NULLPTR);
            QTextDocument::setDefaultResourceProvider(SIP_NULLPTR);
        }
        else
        {
            // Save the callable so that defaultResourceProvider() can return it.
            Py_INCREF(a0);
            sipSetTypeUserData(wt, a0);
        
            Py_BEGIN_ALLOW_THREADS
        
            QTextDocument::setDefaultResourceProvider([a0] (const QUrl &arg) {
                QUrl *arg_heap = new QUrl(arg);
                QVariant qv;
                int is_err = 1;
        
                SIP_BLOCK_THREADS
        
                PyObject *arg_obj = sipConvertFromNewType(arg_heap, sipType_QUrl, NULL);
        
                if (arg_obj)
                {
                    PyObject *res_obj = PyObject_CallFunctionObjArgs(a0, arg_obj, NULL);
        
                    Py_DECREF(arg_obj);
        
                    if (res_obj)
                    {
                        is_err = 0;
        
                        QVariant *res = reinterpret_cast<QVariant *>(
                                sipConvertToType(res_obj, sipType_QVariant, NULL, 0,
                                        NULL, &is_err));
        
                        if (!is_err)
                        {
                            qv = *res;
                            delete res;
                        }
                    }
                }
                else
                {
                    delete arg_heap;
                }
        
                if (is_err)
                {
                    pyqt6_qtgui_err_print();
                }
        
                SIP_UNBLOCK_THREADS
        
                return qv;
            });
        
            Py_END_ALLOW_THREADS
        }
%End

%End
%If (Qt_6_4_0 -)
    void setLayoutEnabled(bool b);
%End
%If (Qt_6_4_0 -)
    bool isLayoutEnabled() const;
%End
};
