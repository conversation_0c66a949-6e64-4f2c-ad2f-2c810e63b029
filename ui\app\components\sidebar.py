"""
侧边栏组件
包含对话历史、新建对话、设置等功能
"""

from typing import List, Dict, Any
from datetime import datetime

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QListWidget, QListWidgetItem, QLabel, QFrame, 
                            QScrollArea, QMenu, QMessageBox)
from PyQt6.QtCore import Qt, pyqtSignal, QSize
from PyQt6.QtGui import QFont, QIcon, QAction

from core.utils.logger import get_logger
from memory.context_engine import context_engine

logger = get_logger(__name__)

class ChatHistoryItem(QFrame):
    """对话历史项"""
    
    clicked = pyqtSignal(str)  # session_id
    delete_requested = pyqtSignal(str)  # session_id
    
    def __init__(self, session_id: str, title: str, last_message: str, timestamp: datetime):
        super().__init__()
        self.session_id = session_id
        self.title = title
        self.last_message = last_message
        self.timestamp = timestamp
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.setObjectName("chat_history_item")
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        self.setMinimumHeight(85)

        # 设置现代化样式
        self.setStyleSheet("""
            QWidget#chat_history_item {
                background: rgba(45, 53, 72, 0.6);
                border: 1px solid rgba(102, 126, 234, 0.2);
                border-radius: 12px;
                margin: 3px;
                padding: 0;
            }
            QWidget#chat_history_item:hover {
                background: rgba(45, 53, 72, 0.8);
                border: 1px solid rgba(102, 126, 234, 0.4);
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 12, 15, 12)
        layout.setSpacing(6)

        # 标题行
        title_row = QHBoxLayout()
        title_row.setSpacing(8)

        # 对话图标
        icon_label = QLabel("💬")
        icon_label.setFixedSize(18, 18)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet("background: transparent; font-size: 12px; border: none;")
        title_row.addWidget(icon_label)

        # 标题
        title_label = QLabel(self.title)
        title_label.setObjectName("chat_title")
        title_label.setWordWrap(True)
        title_label.setStyleSheet("""
            QLabel#chat_title {
                background: transparent;
                color: #e2e8f0;
                font-size: 13px;
                font-weight: 600;
                border: none;
            }
        """)
        title_row.addWidget(title_label, 1)

        # 时间戳
        time_label = QLabel(self.format_timestamp())
        time_label.setObjectName("chat_timestamp")
        time_label.setStyleSheet("""
            QLabel#chat_timestamp {
                background: transparent;
                color: #94a3b8;
                font-size: 10px;
                border: none;
            }
        """)
        title_row.addWidget(time_label)

        layout.addLayout(title_row)

        # 最后一条消息预览
        if self.last_message:
            preview_text = self.last_message[:55] + "..." if len(self.last_message) > 55 else self.last_message
            preview_label = QLabel(preview_text)
            preview_label.setObjectName("chat_preview")
            preview_label.setWordWrap(True)
            preview_label.setStyleSheet("""
                QLabel#chat_preview {
                    background: transparent;
                    color: #94a3b8;
                    font-size: 11px;
                    line-height: 1.3;
                    border: none;
                }
            """)
            layout.addWidget(preview_label)
    
    def format_timestamp(self) -> str:
        """格式化时间戳"""
        now = datetime.now()
        diff = now - self.timestamp
        
        if diff.days > 0:
            return f"{diff.days}天前"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours}小时前"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes}分钟前"
        else:
            return "刚刚"
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit(self.session_id)
        elif event.button() == Qt.MouseButton.RightButton:
            self.show_context_menu(event.globalPosition().toPoint())
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        menu = QMenu(self)
        
        delete_action = QAction("删除对话", self)
        delete_action.triggered.connect(lambda: self.delete_requested.emit(self.session_id))
        menu.addAction(delete_action)
        
        menu.exec(position)

class Sidebar(QWidget):
    """侧边栏"""
    
    new_chat_requested = pyqtSignal()
    chat_selected = pyqtSignal(str)  # session_id
    settings_requested = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.chat_items = {}  # session_id -> ChatHistoryItem
        
        self.init_ui()
        self.load_chat_history()
    
    def init_ui(self):
        """初始化UI"""
        self.setObjectName("sidebar")
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 顶部区域
        top_frame = QFrame()
        top_frame.setObjectName("sidebar_top")
        top_layout = QVBoxLayout(top_frame)
        top_layout.setContentsMargins(12, 12, 12, 12)
        
        # 现代化应用标题
        title_container = QWidget()
        title_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 15px;
                margin: 5px 0;
                padding: 0;
            }
        """)
        title_container_layout = QVBoxLayout(title_container)
        title_container_layout.setContentsMargins(15, 12, 15, 12)

        title_label = QLabel("🌟 Reverie Agents")
        title_label.setObjectName("app_title")
        title_label.setStyleSheet("""
            QLabel#app_title {
                background: transparent;
                color: white;
                font-size: 16px;
                font-weight: 700;
                text-align: center;
                border: none;
            }
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_container_layout.addWidget(title_label)

        top_layout.addWidget(title_container)

        # 现代化新建对话按钮
        self.new_chat_button = QPushButton("✨ 新建对话")
        self.new_chat_button.setObjectName("new_chat_button")
        self.new_chat_button.setFixedHeight(45)
        self.new_chat_button.setStyleSheet("""
            QPushButton#new_chat_button {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #06d6a0, stop:1 #39d353);
                color: white;
                border: none;
                border-radius: 22px;
                font-size: 14px;
                font-weight: 600;
                margin: 8px 0;
                padding: 0;
            }
            QPushButton#new_chat_button:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #059669, stop:1 #16a34a);
                border: 2px solid rgba(255,255,255,0.3);
            }
            QPushButton#new_chat_button:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #047857, stop:1 #15803d);
            }
        """)
        self.new_chat_button.clicked.connect(self.new_chat_requested.emit)
        top_layout.addWidget(self.new_chat_button)
        
        layout.addWidget(top_frame)
        
        # 对话历史区域
        history_frame = QFrame()
        history_frame.setObjectName("sidebar_history")
        history_layout = QVBoxLayout(history_frame)
        history_layout.setContentsMargins(0, 0, 0, 0)
        
        # 历史标题
        history_title = QLabel("对话历史")
        history_title.setObjectName("history_title")
        history_title.setContentsMargins(12, 8, 12, 4)
        history_layout.addWidget(history_title)
        
        # 滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # 历史列表容器
        self.history_widget = QWidget()
        self.history_layout = QVBoxLayout(self.history_widget)
        self.history_layout.setContentsMargins(0, 0, 0, 0)
        self.history_layout.setSpacing(2)
        self.history_layout.addStretch()  # 底部弹簧
        
        self.scroll_area.setWidget(self.history_widget)
        history_layout.addWidget(self.scroll_area, 1)
        
        layout.addWidget(history_frame, 1)
        
        # 底部区域
        bottom_frame = QFrame()
        bottom_frame.setObjectName("sidebar_bottom")
        bottom_layout = QVBoxLayout(bottom_frame)
        bottom_layout.setContentsMargins(12, 12, 12, 12)
        
        # 设置按钮
        self.settings_button = QPushButton("⚙️ 设置")
        self.settings_button.setObjectName("settings_button")
        self.settings_button.clicked.connect(self.settings_requested.emit)
        bottom_layout.addWidget(self.settings_button)

        # 添加弹簧，为卡通形象留出空间
        bottom_layout.addStretch()
        
        layout.addWidget(bottom_frame)
    
    def load_chat_history(self):
        """加载对话历史"""
        try:
            sessions = context_engine.get_session_list()

            # 会话已经按时间排序（最新的在前）
            
            for session in sessions:
                # 转换时间戳
                try:
                    timestamp = datetime.fromisoformat(session['last_updated'])
                except:
                    timestamp = datetime.now()

                self.add_chat_item(
                    session['session_id'],
                    session['summary'] or "默认对话",
                    f"{session['persona']} - {session['total_tokens']} tokens",
                    timestamp
                )
                
        except Exception as e:
            logger.error(f"加载对话历史失败: {e}")
    
    def add_chat_item(self, session_id: str, title: str, last_message: str, timestamp: datetime):
        """添加对话项"""
        if session_id in self.chat_items:
            return  # 已存在
        
        chat_item = ChatHistoryItem(session_id, title, last_message, timestamp)
        chat_item.clicked.connect(self.chat_selected.emit)
        chat_item.delete_requested.connect(self.delete_chat)
        
        # 插入到列表顶部（最新的在前）
        self.history_layout.insertWidget(0, chat_item)
        self.chat_items[session_id] = chat_item
    
    def add_new_chat(self, session_id: str, persona_name: str):
        """添加新对话"""
        self.add_chat_item(session_id, persona_name, "", datetime.now())
        
        # 选中新对话
        self.select_chat(session_id)
    
    def select_chat(self, session_id: str):
        """选中对话"""
        # 清除所有选中状态
        for item in self.chat_items.values():
            item.setProperty("selected", False)
            item.style().unpolish(item)
            item.style().polish(item)
        
        # 设置选中状态
        if session_id in self.chat_items:
            item = self.chat_items[session_id]
            item.setProperty("selected", True)
            item.style().unpolish(item)
            item.style().polish(item)
    
    def delete_chat(self, session_id: str):
        """删除对话"""
        reply = QMessageBox.question(
            self,
            "确认删除",
            "确定要删除这个对话吗？此操作无法撤销。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 从数据库删除
                context_engine.delete_session(session_id)
                
                # 从UI删除
                if session_id in self.chat_items:
                    item = self.chat_items[session_id]
                    self.history_layout.removeWidget(item)
                    item.deleteLater()
                    del self.chat_items[session_id]
                
                logger.info(f"删除对话: {session_id}")
                
            except Exception as e:
                logger.error(f"删除对话失败: {e}")
                QMessageBox.warning(self, "删除失败", f"删除对话时出现错误: {str(e)}")
    
    def update_chat_preview(self, session_id: str, last_message: str):
        """更新对话预览"""
        if session_id in self.chat_items:
            item = self.chat_items[session_id]
            item.last_message = last_message
            
            # 更新预览文本
            for child in item.children():
                if isinstance(child, QLabel) and child.objectName() == "chat_preview":
                    preview_text = last_message[:50] + "..." if len(last_message) > 50 else last_message
                    child.setText(preview_text)
                    break
    
    def update_chat_title(self, session_id: str, new_title: str):
        """更新对话标题"""
        if session_id in self.chat_items:
            item = self.chat_items[session_id]
            item.title = new_title
            
            # 查找并更新标题标签
            def find_and_update_title(widget):
                for child in widget.children():
                    if isinstance(child, QLabel) and child.objectName() == "chat_title":
                        child.setText(new_title)
                        logger.info(f"更新对话标题: {session_id} -> {new_title}")
                        return True
                    elif hasattr(child, 'children'):
                        if find_and_update_title(child):
                            return True
                return False
            
            find_and_update_title(item)
    
    def clear_selection(self):
        """清除选中状态"""
        for item in self.chat_items.values():
            item.setProperty("selected", False)
            item.style().unpolish(item)
            item.style().polish(item)
    
    def apply_theme(self, theme):
        """应用主题"""
        self.setStyleSheet(theme.get_sidebar_stylesheet())
