import{F as u}from"./KHR_interactivity-DTxiAnOo.js";import{R as e}from"./declarationMapper-BZjsjg7g.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./index-Dpxo-yl_.js";import"./objectModelMapping-BR4RdEzn.js";class h extends u{constructor(t){super(t),this.config=t,this.executionFunction=this.registerDataInput("function",e),this.value=this.registerDataInput("value",e),this.result=this.registerDataOutput("result",e)}_updateOutputs(t){const s=this.executionFunction.getValue(t),i=this.value.getValue(t);s&&this.result.setValue(s(i,t),t)}getClassName(){return"FlowGraphCodeExecutionBlock"}}export{h as FlowGraphCodeExecutionBlock};
//# sourceMappingURL=flowGraphCodeExecutionBlock-CeFPkOnh.js.map
