{"version": 3, "file": "dispatch.kxCwF96_.js", "sources": ["../../../../../../../../node_modules/.pnpm/d3-dispatch@3.0.1/node_modules/d3-dispatch/src/dispatch.js"], "sourcesContent": ["var noop = {value: () => {}};\n\nfunction dispatch() {\n  for (var i = 0, n = arguments.length, _ = {}, t; i < n; ++i) {\n    if (!(t = arguments[i] + \"\") || (t in _) || /[\\s.]/.test(t)) throw new Error(\"illegal type: \" + t);\n    _[t] = [];\n  }\n  return new Dispatch(_);\n}\n\nfunction Dispatch(_) {\n  this._ = _;\n}\n\nfunction parseTypenames(typenames, types) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    if (t && !types.hasOwnProperty(t)) throw new Error(\"unknown type: \" + t);\n    return {type: t, name: name};\n  });\n}\n\nDispatch.prototype = dispatch.prototype = {\n  constructor: Dispatch,\n  on: function(typename, callback) {\n    var _ = this._,\n        T = parseTypenames(typename + \"\", _),\n        t,\n        i = -1,\n        n = T.length;\n\n    // If no callback was specified, return the callback of the given type and name.\n    if (arguments.length < 2) {\n      while (++i < n) if ((t = (typename = T[i]).type) && (t = get(_[t], typename.name))) return t;\n      return;\n    }\n\n    // If a type was specified, set the callback for the given type and name.\n    // Otherwise, if a null callback was specified, remove callbacks of the given name.\n    if (callback != null && typeof callback !== \"function\") throw new Error(\"invalid callback: \" + callback);\n    while (++i < n) {\n      if (t = (typename = T[i]).type) _[t] = set(_[t], typename.name, callback);\n      else if (callback == null) for (t in _) _[t] = set(_[t], typename.name, null);\n    }\n\n    return this;\n  },\n  copy: function() {\n    var copy = {}, _ = this._;\n    for (var t in _) copy[t] = _[t].slice();\n    return new Dispatch(copy);\n  },\n  call: function(type, that) {\n    if ((n = arguments.length - 2) > 0) for (var args = new Array(n), i = 0, n, t; i < n; ++i) args[i] = arguments[i + 2];\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  },\n  apply: function(type, that, args) {\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (var t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  }\n};\n\nfunction get(type, name) {\n  for (var i = 0, n = type.length, c; i < n; ++i) {\n    if ((c = type[i]).name === name) {\n      return c.value;\n    }\n  }\n}\n\nfunction set(type, name, callback) {\n  for (var i = 0, n = type.length; i < n; ++i) {\n    if (type[i].name === name) {\n      type[i] = noop, type = type.slice(0, i).concat(type.slice(i + 1));\n      break;\n    }\n  }\n  if (callback != null) type.push({name: name, value: callback});\n  return type;\n}\n\nexport default dispatch;\n"], "names": ["noop", "dispatch", "i", "n", "_", "t", "Dispatch", "parseTypenames", "typenames", "types", "name", "typename", "callback", "T", "get", "set", "copy", "type", "that", "args", "c"], "mappings": "AAAA,IAAIA,EAAO,CAAC,MAAO,IAAM,CAAA,CAAE,EAE3B,SAASC,GAAW,CAClB,QAASC,EAAI,EAAGC,EAAI,UAAU,OAAQC,EAAI,CAAA,EAAIC,EAAGH,EAAIC,EAAG,EAAED,EAAG,CAC3D,GAAI,EAAEG,EAAI,UAAUH,CAAC,EAAI,KAAQG,KAAKD,GAAM,QAAQ,KAAKC,CAAC,EAAG,MAAM,IAAI,MAAM,iBAAmBA,CAAC,EACjGD,EAAEC,CAAC,EAAI,EACR,CACD,OAAO,IAAIC,EAASF,CAAC,CACvB,CAEA,SAASE,EAASF,EAAG,CACnB,KAAK,EAAIA,CACX,CAEA,SAASG,EAAeC,EAAWC,EAAO,CACxC,OAAOD,EAAU,OAAO,MAAM,OAAO,EAAE,IAAI,SAASH,EAAG,CACrD,IAAIK,EAAO,GAAIR,EAAIG,EAAE,QAAQ,GAAG,EAEhC,GADIH,GAAK,IAAGQ,EAAOL,EAAE,MAAMH,EAAI,CAAC,EAAGG,EAAIA,EAAE,MAAM,EAAGH,CAAC,GAC/CG,GAAK,CAACI,EAAM,eAAeJ,CAAC,EAAG,MAAM,IAAI,MAAM,iBAAmBA,CAAC,EACvE,MAAO,CAAC,KAAMA,EAAG,KAAMK,CAAI,CAC/B,CAAG,CACH,CAEAJ,EAAS,UAAYL,EAAS,UAAY,CACxC,YAAaK,EACb,GAAI,SAASK,EAAUC,EAAU,CAC/B,IAAIR,EAAI,KAAK,EACTS,EAAIN,EAAeI,EAAW,GAAIP,CAAC,EACnCC,EACAH,EAAI,GACJC,EAAIU,EAAE,OAGV,GAAI,UAAU,OAAS,EAAG,CACxB,KAAO,EAAEX,EAAIC,GAAG,IAAKE,GAAKM,EAAWE,EAAEX,CAAC,GAAG,QAAUG,EAAIS,EAAIV,EAAEC,CAAC,EAAGM,EAAS,IAAI,GAAI,OAAON,EAC3F,MACD,CAID,GAAIO,GAAY,MAAQ,OAAOA,GAAa,WAAY,MAAM,IAAI,MAAM,qBAAuBA,CAAQ,EACvG,KAAO,EAAEV,EAAIC,GACX,GAAIE,GAAKM,EAAWE,EAAEX,CAAC,GAAG,KAAME,EAAEC,CAAC,EAAIU,EAAIX,EAAEC,CAAC,EAAGM,EAAS,KAAMC,CAAQ,UAC/DA,GAAY,KAAM,IAAKP,KAAKD,EAAGA,EAAEC,CAAC,EAAIU,EAAIX,EAAEC,CAAC,EAAGM,EAAS,KAAM,IAAI,EAG9E,OAAO,IACR,EACD,KAAM,UAAW,CACf,IAAIK,EAAO,CAAE,EAAEZ,EAAI,KAAK,EACxB,QAASC,KAAKD,EAAGY,EAAKX,CAAC,EAAID,EAAEC,CAAC,EAAE,QAChC,OAAO,IAAIC,EAASU,CAAI,CACzB,EACD,KAAM,SAASC,EAAMC,EAAM,CACzB,IAAKf,EAAI,UAAU,OAAS,GAAK,EAAG,QAASgB,EAAO,IAAI,MAAMhB,CAAC,EAAGD,EAAI,EAAGC,EAAGE,EAAGH,EAAIC,EAAG,EAAED,EAAGiB,EAAKjB,CAAC,EAAI,UAAUA,EAAI,CAAC,EACpH,GAAI,CAAC,KAAK,EAAE,eAAee,CAAI,EAAG,MAAM,IAAI,MAAM,iBAAmBA,CAAI,EACzE,IAAKZ,EAAI,KAAK,EAAEY,CAAI,EAAGf,EAAI,EAAGC,EAAIE,EAAE,OAAQH,EAAIC,EAAG,EAAED,EAAGG,EAAEH,CAAC,EAAE,MAAM,MAAMgB,EAAMC,CAAI,CACpF,EACD,MAAO,SAASF,EAAMC,EAAMC,EAAM,CAChC,GAAI,CAAC,KAAK,EAAE,eAAeF,CAAI,EAAG,MAAM,IAAI,MAAM,iBAAmBA,CAAI,EACzE,QAASZ,EAAI,KAAK,EAAEY,CAAI,EAAGf,EAAI,EAAGC,EAAIE,EAAE,OAAQH,EAAIC,EAAG,EAAED,EAAGG,EAAEH,CAAC,EAAE,MAAM,MAAMgB,EAAMC,CAAI,CACxF,CACH,EAEA,SAASL,EAAIG,EAAMP,EAAM,CACvB,QAASR,EAAI,EAAGC,EAAIc,EAAK,OAAQG,EAAGlB,EAAIC,EAAG,EAAED,EAC3C,IAAKkB,EAAIH,EAAKf,CAAC,GAAG,OAASQ,EACzB,OAAOU,EAAE,KAGf,CAEA,SAASL,EAAIE,EAAMP,EAAME,EAAU,CACjC,QAASV,EAAI,EAAGC,EAAIc,EAAK,OAAQf,EAAIC,EAAG,EAAED,EACxC,GAAIe,EAAKf,CAAC,EAAE,OAASQ,EAAM,CACzBO,EAAKf,CAAC,EAAIF,EAAMiB,EAAOA,EAAK,MAAM,EAAGf,CAAC,EAAE,OAAOe,EAAK,MAAMf,EAAI,CAAC,CAAC,EAChE,KACD,CAEH,OAAIU,GAAY,MAAMK,EAAK,KAAK,CAAC,KAAMP,EAAM,MAAOE,CAAQ,CAAC,EACtDK,CACT", "x_google_ignoreList": [0]}