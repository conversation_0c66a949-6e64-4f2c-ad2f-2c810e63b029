#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _foreach_add_Scalar {
  using schema = ::std::vector<at::Tensor> (at::TensorList, const at::Scalar &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_add";
  static constexpr const char* overload_name = "Scalar";
  static constexpr const char* schema_str = "_foreach_add.Scalar(Tensor[] self, Scalar scalar) -> Tensor[]";
  static ::std::vector<at::Tensor> call(at::TensorList self, const at::Scalar & scalar);
  static ::std::vector<at::Tensor> redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, const at::Scalar & scalar);
};

struct TORCH_API _foreach_add__Scalar {
  using schema = void (at::TensorList, const at::Scalar &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_add_";
  static constexpr const char* overload_name = "Scalar";
  static constexpr const char* schema_str = "_foreach_add_.Scalar(Tensor(a!)[] self, Scalar scalar) -> ()";
  static void call(at::TensorList self, const at::Scalar & scalar);
  static void redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, const at::Scalar & scalar);
};

struct TORCH_API _foreach_add_List {
  using schema = ::std::vector<at::Tensor> (at::TensorList, at::TensorList, const at::Scalar &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_add";
  static constexpr const char* overload_name = "List";
  static constexpr const char* schema_str = "_foreach_add.List(Tensor[] self, Tensor[] other, *, Scalar alpha=1) -> Tensor[]";
  static ::std::vector<at::Tensor> call(at::TensorList self, at::TensorList other, const at::Scalar & alpha);
  static ::std::vector<at::Tensor> redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, at::TensorList other, const at::Scalar & alpha);
};

struct TORCH_API _foreach_add__List {
  using schema = void (at::TensorList, at::TensorList, const at::Scalar &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_add_";
  static constexpr const char* overload_name = "List";
  static constexpr const char* schema_str = "_foreach_add_.List(Tensor(a!)[] self, Tensor[] other, *, Scalar alpha=1) -> ()";
  static void call(at::TensorList self, at::TensorList other, const at::Scalar & alpha);
  static void redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, at::TensorList other, const at::Scalar & alpha);
};

struct TORCH_API _foreach_add_ScalarList {
  using schema = ::std::vector<at::Tensor> (at::TensorList, at::ArrayRef<at::Scalar>);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_add";
  static constexpr const char* overload_name = "ScalarList";
  static constexpr const char* schema_str = "_foreach_add.ScalarList(Tensor[] self, Scalar[] scalars) -> Tensor[]";
  static ::std::vector<at::Tensor> call(at::TensorList self, at::ArrayRef<at::Scalar> scalars);
  static ::std::vector<at::Tensor> redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, at::ArrayRef<at::Scalar> scalars);
};

struct TORCH_API _foreach_add__ScalarList {
  using schema = void (at::TensorList, at::ArrayRef<at::Scalar>);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_add_";
  static constexpr const char* overload_name = "ScalarList";
  static constexpr const char* schema_str = "_foreach_add_.ScalarList(Tensor(a!)[] self, Scalar[] scalars) -> ()";
  static void call(at::TensorList self, at::ArrayRef<at::Scalar> scalars);
  static void redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, at::ArrayRef<at::Scalar> scalars);
};

struct TORCH_API _foreach_add_Tensor {
  using schema = ::std::vector<at::Tensor> (at::TensorList, const at::Tensor &, const at::Scalar &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_add";
  static constexpr const char* overload_name = "Tensor";
  static constexpr const char* schema_str = "_foreach_add.Tensor(Tensor[] self, Tensor other, *, Scalar alpha=1) -> Tensor[]";
  static ::std::vector<at::Tensor> call(at::TensorList self, const at::Tensor & other, const at::Scalar & alpha);
  static ::std::vector<at::Tensor> redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, const at::Tensor & other, const at::Scalar & alpha);
};

struct TORCH_API _foreach_add__Tensor {
  using schema = void (at::TensorList, const at::Tensor &, const at::Scalar &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_add_";
  static constexpr const char* overload_name = "Tensor";
  static constexpr const char* schema_str = "_foreach_add_.Tensor(Tensor(a!)[] self, Tensor other, *, Scalar alpha=1) -> ()";
  static void call(at::TensorList self, const at::Tensor & other, const at::Scalar & alpha);
  static void redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, const at::Tensor & other, const at::Scalar & alpha);
};

struct TORCH_API _foreach_add_Scalar_out {
  using schema = void (at::TensorList, const at::Scalar &, at::TensorList);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_add";
  static constexpr const char* overload_name = "Scalar_out";
  static constexpr const char* schema_str = "_foreach_add.Scalar_out(Tensor[] self, Scalar scalar, *, Tensor(a!)[] out) -> ()";
  static void call(at::TensorList self, const at::Scalar & scalar, at::TensorList out);
  static void redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, const at::Scalar & scalar, at::TensorList out);
};

struct TORCH_API _foreach_add_List_out {
  using schema = void (at::TensorList, at::TensorList, const at::Scalar &, at::TensorList);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_add";
  static constexpr const char* overload_name = "List_out";
  static constexpr const char* schema_str = "_foreach_add.List_out(Tensor[] self, Tensor[] other, *, Scalar alpha=1, Tensor(a!)[] out) -> ()";
  static void call(at::TensorList self, at::TensorList other, const at::Scalar & alpha, at::TensorList out);
  static void redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, at::TensorList other, const at::Scalar & alpha, at::TensorList out);
};

struct TORCH_API _foreach_add_ScalarList_out {
  using schema = void (at::TensorList, at::ArrayRef<at::Scalar>, at::TensorList);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_add";
  static constexpr const char* overload_name = "ScalarList_out";
  static constexpr const char* schema_str = "_foreach_add.ScalarList_out(Tensor[] self, Scalar[] scalars, *, Tensor(a!)[] out) -> ()";
  static void call(at::TensorList self, at::ArrayRef<at::Scalar> scalars, at::TensorList out);
  static void redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, at::ArrayRef<at::Scalar> scalars, at::TensorList out);
};

struct TORCH_API _foreach_add_Tensor_out {
  using schema = void (at::TensorList, const at::Tensor &, const at::Scalar &, at::TensorList);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_add";
  static constexpr const char* overload_name = "Tensor_out";
  static constexpr const char* schema_str = "_foreach_add.Tensor_out(Tensor[] self, Tensor other, *, Scalar alpha=1, Tensor(a!)[] out) -> ()";
  static void call(at::TensorList self, const at::Tensor & other, const at::Scalar & alpha, at::TensorList out);
  static void redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, const at::Tensor & other, const at::Scalar & alpha, at::TensorList out);
};

}} // namespace at::_ops
