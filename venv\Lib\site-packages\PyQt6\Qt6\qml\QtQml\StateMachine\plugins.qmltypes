import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/finalstate_p.h"
        name: "FinalState"
        accessSemantics: "reference"
        defaultProperty: "children"
        prototype: "QFinalState"
        exports: [
            "QtQml.StateMachine/FinalState 1.0",
            "QtQml.StateMachine/FinalState 6.0"
        ]
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "children"
            type: "QObject"
            isList: true
            bindable: "bindableChildren"
            read: "children"
            notify: "childrenChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "childrenChanged" }
    }
    Component {
        file: "private/statemachineforeign_p.h"
        name: "QAbstractState"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQml.StateMachine/QAbstractState 1.0",
            "QtQml.StateMachine/QAbstractState 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "active"
            type: "bool"
            bindable: "bindableActive"
            read: "active"
            notify: "activeChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "entered" }
        Signal { name: "exited" }
        Signal {
            name: "activeChanged"
            Parameter { name: "active"; type: "bool" }
        }
    }
    Component {
        file: "qabstracttransition.h"
        name: "QAbstractTransition"
        accessSemantics: "reference"
        prototype: "QObject"
        Enum {
            name: "TransitionType"
            values: ["ExternalTransition", "InternalTransition"]
        }
        Property {
            name: "sourceState"
            type: "QState"
            isPointer: true
            read: "sourceState"
            index: 0
            isReadonly: true
        }
        Property {
            name: "targetState"
            type: "QAbstractState"
            isPointer: true
            read: "targetState"
            write: "setTargetState"
            notify: "targetStateChanged"
            index: 1
        }
        Property {
            name: "targetStates"
            type: "QList<QAbstractState*>"
            read: "targetStates"
            write: "setTargetStates"
            notify: "targetStatesChanged"
            index: 2
        }
        Property {
            name: "transitionType"
            revision: 257
            type: "TransitionType"
            bindable: "bindableTransitionType"
            read: "transitionType"
            write: "setTransitionType"
            index: 3
        }
        Signal { name: "triggered" }
        Signal { name: "targetStateChanged" }
        Signal { name: "targetStatesChanged" }
    }
    Component {
        file: "qfinalstate.h"
        name: "QFinalState"
        accessSemantics: "reference"
        prototype: "QAbstractState"
    }
    Component {
        file: "private/statemachineforeign_p.h"
        name: "QHistoryState"
        accessSemantics: "reference"
        prototype: "QAbstractState"
        exports: [
            "QtQml.StateMachine/HistoryState 1.0",
            "QtQml.StateMachine/HistoryState 6.0"
        ]
        exportMetaObjectRevisions: [256, 1536]
        Enum {
            name: "HistoryType"
            values: ["ShallowHistory", "DeepHistory"]
        }
        Property {
            name: "defaultState"
            type: "QAbstractState"
            isPointer: true
            read: "defaultState"
            write: "setDefaultState"
            notify: "defaultStateChanged"
            index: 0
        }
        Property {
            name: "defaultTransition"
            type: "QAbstractTransition"
            isPointer: true
            bindable: "bindableDefaultTransition"
            read: "defaultTransition"
            write: "setDefaultTransition"
            notify: "defaultTransitionChanged"
            index: 1
        }
        Property {
            name: "historyType"
            type: "HistoryType"
            bindable: "bindableHistoryType"
            read: "historyType"
            write: "setHistoryType"
            notify: "historyTypeChanged"
            index: 2
        }
        Signal { name: "defaultTransitionChanged" }
        Signal { name: "defaultStateChanged" }
        Signal { name: "historyTypeChanged" }
    }
    Component {
        file: "private/statemachineforeign_p.h"
        name: "QSignalTransition"
        accessSemantics: "reference"
        prototype: "QAbstractTransition"
        exports: [
            "QtQml.StateMachine/QSignalTransition 1.0",
            "QtQml.StateMachine/QSignalTransition 1.1",
            "QtQml.StateMachine/QSignalTransition 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 257, 1536]
        Property {
            name: "senderObject"
            type: "QObject"
            isPointer: true
            isTypeConstant: true
            bindable: "bindableSenderObject"
            read: "senderObject"
            write: "setSenderObject"
            notify: "senderObjectChanged"
            index: 0
        }
        Property {
            name: "signal"
            type: "QByteArray"
            bindable: "bindableSignal"
            read: "signal"
            write: "setSignal"
            notify: "signalChanged"
            index: 1
        }
        Signal { name: "senderObjectChanged" }
        Signal { name: "signalChanged" }
    }
    Component {
        file: "private/statemachineforeign_p.h"
        name: "QState"
        accessSemantics: "reference"
        prototype: "QAbstractState"
        exports: [
            "QtQml.StateMachine/QState 1.0",
            "QtQml.StateMachine/QState 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536]
        Enum {
            name: "ChildMode"
            values: ["ExclusiveStates", "ParallelStates"]
        }
        Enum {
            name: "RestorePolicy"
            values: ["DontRestoreProperties", "RestoreProperties"]
        }
        Property {
            name: "initialState"
            type: "QAbstractState"
            isPointer: true
            bindable: "bindableInitialState"
            read: "initialState"
            write: "setInitialState"
            notify: "initialStateChanged"
            index: 0
        }
        Property {
            name: "errorState"
            type: "QAbstractState"
            isPointer: true
            bindable: "bindableErrorState"
            read: "errorState"
            write: "setErrorState"
            notify: "errorStateChanged"
            index: 1
        }
        Property {
            name: "childMode"
            type: "ChildMode"
            bindable: "bindableChildMode"
            read: "childMode"
            write: "setChildMode"
            notify: "childModeChanged"
            index: 2
        }
        Signal { name: "finished" }
        Signal { name: "propertiesAssigned" }
        Signal { name: "childModeChanged" }
        Signal { name: "initialStateChanged" }
        Signal { name: "errorStateChanged" }
    }
    Component {
        file: "qstatemachine.h"
        name: "QStateMachine"
        accessSemantics: "reference"
        prototype: "QState"
        Property {
            name: "errorString"
            type: "QString"
            bindable: "bindableErrorString"
            read: "errorString"
            index: 0
            isReadonly: true
        }
        Property {
            name: "globalRestorePolicy"
            type: "QState::RestorePolicy"
            bindable: "bindableGlobalRestorePolicy"
            read: "globalRestorePolicy"
            write: "setGlobalRestorePolicy"
            index: 1
        }
        Property {
            name: "running"
            type: "bool"
            read: "isRunning"
            write: "setRunning"
            notify: "runningChanged"
            index: 2
        }
        Property {
            name: "animated"
            type: "bool"
            bindable: "bindableAnimated"
            read: "isAnimated"
            write: "setAnimated"
            index: 3
        }
        Signal { name: "started" }
        Signal { name: "stopped" }
        Signal {
            name: "runningChanged"
            Parameter { name: "running"; type: "bool" }
        }
        Method { name: "start" }
        Method { name: "stop" }
        Method {
            name: "setRunning"
            Parameter { name: "running"; type: "bool" }
        }
        Method { name: "_q_start" }
        Method { name: "_q_process" }
        Method { name: "_q_animationFinished" }
        Method {
            name: "_q_startDelayedEventTimer"
            Parameter { type: "int" }
            Parameter { type: "int" }
        }
        Method {
            name: "_q_killDelayedEventTimer"
            Parameter { type: "int" }
            Parameter { type: "int" }
        }
    }
    Component {
        file: "private/signaltransition_p.h"
        name: "SignalTransition"
        accessSemantics: "reference"
        prototype: "QSignalTransition"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQml.StateMachine/SignalTransition 1.0",
            "QtQml.StateMachine/SignalTransition 1.1",
            "QtQml.StateMachine/SignalTransition 6.0"
        ]
        hasCustomParser: true
        exportMetaObjectRevisions: [256, 257, 1536]
        Property {
            name: "signal"
            type: "QJSValue"
            bindable: "bindableSignal"
            read: "signal"
            write: "setSignal"
            notify: "qmlSignalChanged"
            index: 0
        }
        Property {
            name: "guard"
            type: "QQmlScriptString"
            bindable: "bindableGuard"
            read: "guard"
            write: "setGuard"
            notify: "guardChanged"
            index: 1
        }
        Signal { name: "guardChanged" }
        Signal { name: "invokeYourself" }
        Signal { name: "qmlSignalChanged" }
        Method { name: "invoke" }
    }
    Component {
        file: "private/state_p.h"
        name: "State"
        accessSemantics: "reference"
        defaultProperty: "children"
        prototype: "QState"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQml.StateMachine/State 1.0",
            "QtQml.StateMachine/State 6.0"
        ]
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "children"
            type: "QObject"
            isList: true
            bindable: "bindableChildren"
            read: "children"
            notify: "childrenChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "childrenChanged" }
    }
    Component {
        file: "private/statemachine_p.h"
        name: "StateMachine"
        accessSemantics: "reference"
        defaultProperty: "children"
        prototype: "QStateMachine"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQml.StateMachine/StateMachine 1.0",
            "QtQml.StateMachine/StateMachine 6.0"
        ]
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "children"
            type: "QObject"
            isList: true
            bindable: "bindableChildren"
            read: "children"
            notify: "childrenChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "running"
            type: "bool"
            read: "isRunning"
            write: "setRunning"
            notify: "qmlRunningChanged"
            index: 1
        }
        Signal { name: "childrenChanged" }
        Signal { name: "qmlRunningChanged" }
        Method { name: "checkChildMode" }
    }
    Component {
        file: "private/timeouttransition_p.h"
        name: "TimeoutTransition"
        accessSemantics: "reference"
        prototype: "QSignalTransition"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQml.StateMachine/TimeoutTransition 1.0",
            "QtQml.StateMachine/TimeoutTransition 1.1",
            "QtQml.StateMachine/TimeoutTransition 6.0"
        ]
        exportMetaObjectRevisions: [256, 257, 1536]
        Property {
            name: "timeout"
            type: "int"
            bindable: "bindableTimeout"
            read: "timeout"
            write: "setTimeout"
            index: 0
        }
    }
}
