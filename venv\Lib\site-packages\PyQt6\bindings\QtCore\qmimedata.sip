// qmimedata.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMimeData : public QObject
{
%TypeHeaderCode
#include <qmimedata.h>
%End

public:
    QMimeData();
    virtual ~QMimeData();
    QList<QUrl> urls() const;
    void setUrls(const QList<QUrl> &urls);
    bool hasUrls() const;
    QString text() const;
    void setText(const QString &text);
    bool hasText() const;
    QString html() const;
    void setHtml(const QString &html);
    bool hasHtml() const;
    QVariant imageData() const;
    void setImageData(const QVariant &image);
    bool hasImage() const;
    QVariant colorData() const;
    void setColorData(const QVariant &color);
    bool hasColor() const;
    QByteArray data(const QString &mimetype) const;
    void setData(const QString &mimetype, const QByteArray &data);
    virtual bool hasFormat(const QString &mimetype) const;
    virtual QStringList formats() const;
    void clear();
    void removeFormat(const QString &mimetype);

protected:
    virtual QVariant retrieveData(const QString &mimetype, QMetaType preferredType) const;
};
