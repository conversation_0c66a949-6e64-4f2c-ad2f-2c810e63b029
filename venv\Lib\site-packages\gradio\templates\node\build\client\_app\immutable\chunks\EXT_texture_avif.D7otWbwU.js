import{GLTFLoader as a,ArrayItem as n}from"./glTFLoader.BetPWe9U.js";import{an as _,ao as c}from"./index.BoI39RQH.js";const r="EXT_texture_avif";class f{constructor(e){this.name=r,this._loader=e,this.enabled=e.isExtensionUsed(r)}dispose(){this._loader=null}_loadTextureAsync(e,s,o){return a.LoadExtensionAsync(e,s,this.name,(i,l)=>{const m=s.sampler==null?a.DefaultSampler:n.Get(`${e}/sampler`,this._loader.gltf.samplers,s.sampler),d=n.Get(`${i}/source`,this._loader.gltf.images,l.source);return this._loader._createTextureAsync(e,m,d,u=>{o(u)},void 0,!s._textureInfo.nonColorData)})}}_(r);c(r,!0,t=>new f(t));export{f as EXT_texture_avif};
//# sourceMappingURL=EXT_texture_avif.D7otWbwU.js.map
