{"version": 3, "file": "Example16-BSaUMSck.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Example16.js"], "sourcesContent": ["import{create_ssr_component as i,add_attribute as s}from\"svelte/internal\";const o={code:\".wrap.svelte-11djrz8.svelte-11djrz8{position:relative;height:var(--size-64);width:var(--size-40);overflow:hidden;border-radius:var(--radius-lg)}img.svelte-11djrz8.svelte-11djrz8{height:var(--size-64);width:var(--size-40);position:absolute;object-fit:cover}.wrap.selected.svelte-11djrz8.svelte-11djrz8{border-color:var(--color-accent)}.wrap.svelte-11djrz8 img.svelte-11djrz8:first-child{clip-path:inset(0 50% 0 0%)}.wrap.svelte-11djrz8 img.svelte-11djrz8:nth-of-type(2){clip-path:inset(0 0 0 50%)}span.svelte-11djrz8.svelte-11djrz8{position:absolute;top:0;left:calc(50% - 0.75px);height:var(--size-64);width:1.5px;background:var(--border-color-primary)}.table.svelte-11djrz8.svelte-11djrz8{margin:0 auto;border:2px solid var(--border-color-primary);border-radius:var(--radius-lg)}.gallery.svelte-11djrz8.svelte-11djrz8{border:2px solid var(--border-color-primary);object-fit:cover}\",map:'{\"version\":3,\"file\":\"Example.svelte\",\"sources\":[\"Example.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">export let value;\\\\nexport let samples_dir;\\\\nexport let type;\\\\nexport let selected = false;\\\\n<\\/script>\\\\n\\\\n<!-- TODO: fix -->\\\\n<!-- svelte-ignore a11y-missing-attribute -->\\\\n<div\\\\n\\\\tclass=\\\\\"wrap\\\\\"\\\\n\\\\tclass:table={type === \\\\\"table\\\\\"}\\\\n\\\\tclass:gallery={type === \\\\\"gallery\\\\\"}\\\\n\\\\tclass:selected\\\\n>\\\\n\\\\t<img src={samples_dir + value[0]} />\\\\n\\\\n\\\\t<img src={samples_dir + value[1]} />\\\\n\\\\t<span></span>\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.wrap {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\theight: var(--size-64);\\\\n\\\\t\\\\twidth: var(--size-40);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t}\\\\n\\\\timg {\\\\n\\\\t\\\\theight: var(--size-64);\\\\n\\\\t\\\\twidth: var(--size-40);\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\t/* border-radius: var(--radius-lg); */\\\\n\\\\t\\\\t/* max-width: none; */\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t}\\\\n\\\\n\\\\t.wrap.selected {\\\\n\\\\t\\\\tborder-color: var(--color-accent);\\\\n\\\\t}\\\\n\\\\t.wrap img:first-child {\\\\n\\\\t\\\\tclip-path: inset(0 50% 0 0%);\\\\n\\\\t}\\\\n\\\\n\\\\t.wrap img:nth-of-type(2) {\\\\n\\\\t\\\\tclip-path: inset(0 0 0 50%);\\\\n\\\\t}\\\\n\\\\tspan {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\ttop: 0;\\\\n\\\\t\\\\tleft: calc(50% - 0.75px);\\\\n\\\\t\\\\theight: var(--size-64);\\\\n\\\\t\\\\twidth: 1.5px;\\\\n\\\\t\\\\tbackground: var(--border-color-primary);\\\\n\\\\t}\\\\n\\\\n\\\\t.table {\\\\n\\\\t\\\\tmargin: 0 auto;\\\\n\\\\t\\\\tborder: 2px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t}\\\\n\\\\n\\\\t.gallery {\\\\n\\\\t\\\\tborder: 2px solid var(--border-color-primary);\\\\n\\\\t\\\\t/* max-height: var(--size-20); */\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAqBC,mCAAM,CACL,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,IAAI,SAAS,CAAC,CACtB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,QAAQ,CAAE,MAAM,CAChB,aAAa,CAAE,IAAI,WAAW,CAC/B,CACA,iCAAI,CACH,MAAM,CAAE,IAAI,SAAS,CAAC,CACtB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,QAAQ,CAAE,QAAQ,CAGlB,UAAU,CAAE,KACb,CAEA,KAAK,uCAAU,CACd,YAAY,CAAE,IAAI,cAAc,CACjC,CACA,oBAAK,CAAC,kBAAG,YAAa,CACrB,SAAS,CAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAC5B,CAEA,oBAAK,CAAC,kBAAG,aAAa,CAAC,CAAE,CACxB,SAAS,CAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAC3B,CACA,kCAAK,CACJ,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,CAAC,CACN,IAAI,CAAE,KAAK,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CACxB,MAAM,CAAE,IAAI,SAAS,CAAC,CACtB,KAAK,CAAE,KAAK,CACZ,UAAU,CAAE,IAAI,sBAAsB,CACvC,CAEA,oCAAO,CACN,MAAM,CAAE,CAAC,CAAC,IAAI,CACd,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,WAAW,CAC/B,CAEA,sCAAS,CACR,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAE7C,UAAU,CAAE,KACb\"}'},v=i((l,A,t,d)=>{let{value:e}=A,{samples_dir:C}=A,{type:r}=A,{selected:a=!1}=A;return A.value===void 0&&t.value&&e!==void 0&&t.value(e),A.samples_dir===void 0&&t.samples_dir&&C!==void 0&&t.samples_dir(C),A.type===void 0&&t.type&&r!==void 0&&t.type(r),A.selected===void 0&&t.selected&&a!==void 0&&t.selected(a),l.css.add(o),`  <div class=\"${[\"wrap svelte-11djrz8\",(r===\"table\"?\"table\":\"\")+\" \"+(r===\"gallery\"?\"gallery\":\"\")+\" \"+(a?\"selected\":\"\")].join(\" \").trim()}\"><img${s(\"src\",C+e[0],0)} class=\"svelte-11djrz8\"> <img${s(\"src\",C+e[1],0)} class=\"svelte-11djrz8\"> <span class=\"svelte-11djrz8\"></span> </div>`});export{v as default};\n//# sourceMappingURL=Example16.js.map\n"], "names": ["i", "s"], "mappings": ";;AAA+E,MAAC,CAAC,CAAC,CAAC,IAAI,CAAC,k3BAAk3B,CAAC,GAAG,CAAC,oiFAAoiF,CAAC,CAAC,CAAC,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,EAAEC,aAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,6BAA6B,EAAEA,aAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oEAAoE,CAAC,CAAC;;;;"}