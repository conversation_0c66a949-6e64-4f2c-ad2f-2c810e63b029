{"version": 3, "file": "browserAll-CafffKlT.js", "sources": ["../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/events/FederatedEvent.mjs", "../../../../node_modules/.pnpm/ismobilejs@1.1.1/node_modules/ismobilejs/esm/isMobile.js", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/utils/browser/isMobile.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/accessibility/AccessibilitySystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/accessibility/accessibilityTarget.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/events/EventTicker.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/events/FederatedMouseEvent.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/events/FederatedPointerEvent.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/events/FederatedWheelEvent.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/events/EventBoundary.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/events/EventSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/events/FederatedEventTarget.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/accessibility/init.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/events/init.mjs"], "sourcesContent": ["import { Point } from '../maths/point/Point.mjs';\n\n\"use strict\";\nclass FederatedEvent {\n  /**\n   * @param manager - The event boundary which manages this event. Propagation can only occur\n   *  within the boundary's jurisdiction.\n   */\n  constructor(manager) {\n    /** Flags whether this event bubbles. This will take effect only if it is set before propagation. */\n    this.bubbles = true;\n    /** @deprecated since 7.0.0 */\n    this.cancelBubble = true;\n    /**\n     * Flags whether this event can be canceled using {@link FederatedEvent.preventDefault}. This is always\n     * false (for now).\n     */\n    this.cancelable = false;\n    /**\n     * Flag added for compatibility with DOM {@code Event}. It is not used in the Federated Events\n     * API.\n     * @see https://dom.spec.whatwg.org/#dom-event-composed\n     */\n    this.composed = false;\n    /** Flags whether the default response of the user agent was prevent through this event. */\n    this.defaultPrevented = false;\n    /**\n     * The propagation phase.\n     * @default {@link FederatedEvent.NONE}\n     */\n    this.eventPhase = FederatedEvent.prototype.NONE;\n    /** Flags whether propagation was stopped. */\n    this.propagationStopped = false;\n    /** Flags whether propagation was immediately stopped. */\n    this.propagationImmediatelyStopped = false;\n    /** The coordinates of the event relative to the nearest DOM layer. This is a non-standard property. */\n    this.layer = new Point();\n    /** The coordinates of the event relative to the DOM document. This is a non-standard property. */\n    this.page = new Point();\n    this.NONE = 0;\n    this.CAPTURING_PHASE = 1;\n    this.AT_TARGET = 2;\n    this.BUBBLING_PHASE = 3;\n    this.manager = manager;\n  }\n  /** @readonly */\n  get layerX() {\n    return this.layer.x;\n  }\n  /** @readonly */\n  get layerY() {\n    return this.layer.y;\n  }\n  /** @readonly */\n  get pageX() {\n    return this.page.x;\n  }\n  /** @readonly */\n  get pageY() {\n    return this.page.y;\n  }\n  /**\n   * Fallback for the deprecated @code{InteractionEvent.data}.\n   * @deprecated since 7.0.0\n   */\n  get data() {\n    return this;\n  }\n  /** The propagation path for this event. Alias for {@link EventBoundary.propagationPath}. */\n  composedPath() {\n    if (this.manager && (!this.path || this.path[this.path.length - 1] !== this.target)) {\n      this.path = this.target ? this.manager.propagationPath(this.target) : [];\n    }\n    return this.path;\n  }\n  /**\n   * Unimplemented method included for implementing the DOM interface {@code Event}. It will throw an {@code Error}.\n   * @deprecated\n   * @param _type\n   * @param _bubbles\n   * @param _cancelable\n   */\n  initEvent(_type, _bubbles, _cancelable) {\n    throw new Error(\"initEvent() is a legacy DOM API. It is not implemented in the Federated Events API.\");\n  }\n  /**\n   * Unimplemented method included for implementing the DOM interface {@code UIEvent}. It will throw an {@code Error}.\n   * @deprecated\n   * @param _typeArg\n   * @param _bubblesArg\n   * @param _cancelableArg\n   * @param _viewArg\n   * @param _detailArg\n   */\n  initUIEvent(_typeArg, _bubblesArg, _cancelableArg, _viewArg, _detailArg) {\n    throw new Error(\"initUIEvent() is a legacy DOM API. It is not implemented in the Federated Events API.\");\n  }\n  /** Prevent default behavior of PixiJS and the user agent. */\n  preventDefault() {\n    if (this.nativeEvent instanceof Event && this.nativeEvent.cancelable) {\n      this.nativeEvent.preventDefault();\n    }\n    this.defaultPrevented = true;\n  }\n  /**\n   * Stop this event from propagating to any addition listeners, including on the\n   * {@link FederatedEventTarget.currentTarget currentTarget} and also the following\n   * event targets on the propagation path.\n   */\n  stopImmediatePropagation() {\n    this.propagationImmediatelyStopped = true;\n  }\n  /**\n   * Stop this event from propagating to the next {@link FederatedEventTarget}. The rest of the listeners\n   * on the {@link FederatedEventTarget.currentTarget currentTarget} will still be notified.\n   */\n  stopPropagation() {\n    this.propagationStopped = true;\n  }\n}\n\nexport { FederatedEvent };\n//# sourceMappingURL=FederatedEvent.mjs.map\n", "var appleIphone = /iPhone/i;\nvar appleIpod = /iPod/i;\nvar appleTablet = /iPad/i;\nvar appleUniversal = /\\biOS-universal(?:.+)Mac\\b/i;\nvar androidPhone = /\\bAndroid(?:.+)Mobile\\b/i;\nvar androidTablet = /Android/i;\nvar amazonPhone = /(?:SD4930UR|\\bSilk(?:.+)Mobile\\b)/i;\nvar amazonTablet = /Silk/i;\nvar windowsPhone = /Windows Phone/i;\nvar windowsTablet = /\\bWindows(?:.+)ARM\\b/i;\nvar otherBlackBerry = /BlackBerry/i;\nvar otherBlackBerry10 = /BB10/i;\nvar otherOpera = /Opera Mini/i;\nvar otherChrome = /\\b(CriOS|Chrome)(?:.+)Mobile/i;\nvar otherFirefox = /Mobile(?:.+)Firefox\\b/i;\nvar isAppleTabletOnIos13 = function (navigator) {\n    return (typeof navigator !== 'undefined' &&\n        navigator.platform === 'MacIntel' &&\n        typeof navigator.maxTouchPoints === 'number' &&\n        navigator.maxTouchPoints > 1 &&\n        typeof MSStream === 'undefined');\n};\nfunction createMatch(userAgent) {\n    return function (regex) { return regex.test(userAgent); };\n}\nexport default function isMobile(param) {\n    var nav = {\n        userAgent: '',\n        platform: '',\n        maxTouchPoints: 0\n    };\n    if (!param && typeof navigator !== 'undefined') {\n        nav = {\n            userAgent: navigator.userAgent,\n            platform: navigator.platform,\n            maxTouchPoints: navigator.maxTouchPoints || 0\n        };\n    }\n    else if (typeof param === 'string') {\n        nav.userAgent = param;\n    }\n    else if (param && param.userAgent) {\n        nav = {\n            userAgent: param.userAgent,\n            platform: param.platform,\n            maxTouchPoints: param.maxTouchPoints || 0\n        };\n    }\n    var userAgent = nav.userAgent;\n    var tmp = userAgent.split('[FBAN');\n    if (typeof tmp[1] !== 'undefined') {\n        userAgent = tmp[0];\n    }\n    tmp = userAgent.split('Twitter');\n    if (typeof tmp[1] !== 'undefined') {\n        userAgent = tmp[0];\n    }\n    var match = createMatch(userAgent);\n    var result = {\n        apple: {\n            phone: match(appleIphone) && !match(windowsPhone),\n            ipod: match(appleIpod),\n            tablet: !match(appleIphone) &&\n                (match(appleTablet) || isAppleTabletOnIos13(nav)) &&\n                !match(windowsPhone),\n            universal: match(appleUniversal),\n            device: (match(appleIphone) ||\n                match(appleIpod) ||\n                match(appleTablet) ||\n                match(appleUniversal) ||\n                isAppleTabletOnIos13(nav)) &&\n                !match(windowsPhone)\n        },\n        amazon: {\n            phone: match(amazonPhone),\n            tablet: !match(amazonPhone) && match(amazonTablet),\n            device: match(amazonPhone) || match(amazonTablet)\n        },\n        android: {\n            phone: (!match(windowsPhone) && match(amazonPhone)) ||\n                (!match(windowsPhone) && match(androidPhone)),\n            tablet: !match(windowsPhone) &&\n                !match(amazonPhone) &&\n                !match(androidPhone) &&\n                (match(amazonTablet) || match(androidTablet)),\n            device: (!match(windowsPhone) &&\n                (match(amazonPhone) ||\n                    match(amazonTablet) ||\n                    match(androidPhone) ||\n                    match(androidTablet))) ||\n                match(/\\bokhttp\\b/i)\n        },\n        windows: {\n            phone: match(windowsPhone),\n            tablet: match(windowsTablet),\n            device: match(windowsPhone) || match(windowsTablet)\n        },\n        other: {\n            blackberry: match(otherBlackBerry),\n            blackberry10: match(otherBlackBerry10),\n            opera: match(otherOpera),\n            firefox: match(otherFirefox),\n            chrome: match(otherChrome),\n            device: match(otherBlackBerry) ||\n                match(otherBlackBerry10) ||\n                match(otherOpera) ||\n                match(otherFirefox) ||\n                match(otherChrome)\n        },\n        any: false,\n        phone: false,\n        tablet: false\n    };\n    result.any =\n        result.apple.device ||\n            result.android.device ||\n            result.windows.device ||\n            result.other.device;\n    result.phone =\n        result.apple.phone || result.android.phone || result.windows.phone;\n    result.tablet =\n        result.apple.tablet || result.android.tablet || result.windows.tablet;\n    return result;\n}\n//# sourceMappingURL=isMobile.js.map", "import isMobileJs from 'ismobilejs';\n\n\"use strict\";\nconst isMobileCall = isMobileJs.default ?? isMobileJs;\nconst isMobile = isMobileCall(globalThis.navigator);\n\nexport { isMobile };\n//# sourceMappingURL=isMobile.mjs.map\n", "import { FederatedEvent } from '../events/FederatedEvent.mjs';\nimport { ExtensionType } from '../extensions/Extensions.mjs';\nimport { isMobile } from '../utils/browser/isMobile.mjs';\nimport { removeItems } from '../utils/data/removeItems.mjs';\n\n\"use strict\";\nconst KEY_CODE_TAB = 9;\nconst DIV_TOUCH_SIZE = 100;\nconst DIV_TOUCH_POS_X = 0;\nconst DIV_TOUCH_POS_Y = 0;\nconst DIV_TOUCH_ZINDEX = 2;\nconst DIV_HOOK_SIZE = 1;\nconst DIV_HOOK_POS_X = -1e3;\nconst DIV_HOOK_POS_Y = -1e3;\nconst DIV_HOOK_ZINDEX = 2;\nconst _AccessibilitySystem = class _AccessibilitySystem {\n  // 2fps\n  // eslint-disable-next-line jsdoc/require-param\n  /**\n   * @param {WebGLRenderer|WebGPURenderer} renderer - A reference to the current renderer\n   */\n  constructor(renderer, _mobileInfo = isMobile) {\n    this._mobileInfo = _mobileInfo;\n    /** Whether accessibility divs are visible for debugging */\n    this.debug = false;\n    /** Whether to activate on tab key press */\n    this._activateOnTab = true;\n    /** Whether to deactivate accessibility when mouse moves */\n    this._deactivateOnMouseMove = true;\n    /** Internal variable, see isActive getter. */\n    this._isActive = false;\n    /** Internal variable, see isMobileAccessibility getter. */\n    this._isMobileAccessibility = false;\n    /** This is the dom element that will sit over the PixiJS element. This is where the div overlays will go. */\n    this._div = null;\n    /** A simple pool for storing divs. */\n    this._pool = [];\n    /** This is a tick used to check if an object is no longer being rendered. */\n    this._renderId = 0;\n    /** The array of currently active accessible items. */\n    this._children = [];\n    /** Count to throttle div updates on android devices. */\n    this._androidUpdateCount = 0;\n    /**  The frequency to update the div elements. */\n    this._androidUpdateFrequency = 500;\n    this._hookDiv = null;\n    if (_mobileInfo.tablet || _mobileInfo.phone) {\n      this._createTouchHook();\n    }\n    this._renderer = renderer;\n  }\n  /**\n   * Value of `true` if accessibility is currently active and accessibility layers are showing.\n   * @member {boolean}\n   * @readonly\n   */\n  get isActive() {\n    return this._isActive;\n  }\n  /**\n   * Value of `true` if accessibility is enabled for touch devices.\n   * @member {boolean}\n   * @readonly\n   */\n  get isMobileAccessibility() {\n    return this._isMobileAccessibility;\n  }\n  get hookDiv() {\n    return this._hookDiv;\n  }\n  /**\n   * Creates the touch hooks.\n   * @private\n   */\n  _createTouchHook() {\n    const hookDiv = document.createElement(\"button\");\n    hookDiv.style.width = `${DIV_HOOK_SIZE}px`;\n    hookDiv.style.height = `${DIV_HOOK_SIZE}px`;\n    hookDiv.style.position = \"absolute\";\n    hookDiv.style.top = `${DIV_HOOK_POS_X}px`;\n    hookDiv.style.left = `${DIV_HOOK_POS_Y}px`;\n    hookDiv.style.zIndex = DIV_HOOK_ZINDEX.toString();\n    hookDiv.style.backgroundColor = \"#FF0000\";\n    hookDiv.title = \"select to enable accessibility for this content\";\n    hookDiv.addEventListener(\"focus\", () => {\n      this._isMobileAccessibility = true;\n      this._activate();\n      this._destroyTouchHook();\n    });\n    document.body.appendChild(hookDiv);\n    this._hookDiv = hookDiv;\n  }\n  /**\n   * Destroys the touch hooks.\n   * @private\n   */\n  _destroyTouchHook() {\n    if (!this._hookDiv) {\n      return;\n    }\n    document.body.removeChild(this._hookDiv);\n    this._hookDiv = null;\n  }\n  /**\n   * Activating will cause the Accessibility layer to be shown.\n   * This is called when a user presses the tab key.\n   * @private\n   */\n  _activate() {\n    if (this._isActive) {\n      return;\n    }\n    this._isActive = true;\n    if (!this._div) {\n      this._div = document.createElement(\"div\");\n      this._div.style.width = `${DIV_TOUCH_SIZE}px`;\n      this._div.style.height = `${DIV_TOUCH_SIZE}px`;\n      this._div.style.position = \"absolute\";\n      this._div.style.top = `${DIV_TOUCH_POS_X}px`;\n      this._div.style.left = `${DIV_TOUCH_POS_Y}px`;\n      this._div.style.zIndex = DIV_TOUCH_ZINDEX.toString();\n      this._div.style.pointerEvents = \"none\";\n    }\n    if (this._activateOnTab) {\n      this._onKeyDown = this._onKeyDown.bind(this);\n      globalThis.addEventListener(\"keydown\", this._onKeyDown, false);\n    }\n    if (this._deactivateOnMouseMove) {\n      this._onMouseMove = this._onMouseMove.bind(this);\n      globalThis.document.addEventListener(\"mousemove\", this._onMouseMove, true);\n    }\n    const canvas = this._renderer.view.canvas;\n    if (!canvas.parentNode) {\n      const observer = new MutationObserver(() => {\n        if (canvas.parentNode) {\n          canvas.parentNode.appendChild(this._div);\n          observer.disconnect();\n          this._initAccessibilitySetup();\n        }\n      });\n      observer.observe(document.body, { childList: true, subtree: true });\n    } else {\n      canvas.parentNode.appendChild(this._div);\n      this._initAccessibilitySetup();\n    }\n  }\n  // New method to handle initialization after div is ready\n  _initAccessibilitySetup() {\n    this._renderer.runners.postrender.add(this);\n    if (this._renderer.lastObjectRendered) {\n      this._updateAccessibleObjects(this._renderer.lastObjectRendered);\n    }\n  }\n  /**\n   * Deactivates the accessibility system. Removes listeners and accessibility elements.\n   * @private\n   */\n  _deactivate() {\n    if (!this._isActive || this._isMobileAccessibility) {\n      return;\n    }\n    this._isActive = false;\n    globalThis.document.removeEventListener(\"mousemove\", this._onMouseMove, true);\n    if (this._activateOnTab) {\n      globalThis.addEventListener(\"keydown\", this._onKeyDown, false);\n    }\n    this._renderer.runners.postrender.remove(this);\n    for (const child of this._children) {\n      if (child._accessibleDiv && child._accessibleDiv.parentNode) {\n        child._accessibleDiv.parentNode.removeChild(child._accessibleDiv);\n        child._accessibleDiv = null;\n      }\n      child._accessibleActive = false;\n    }\n    this._pool.forEach((div) => {\n      if (div.parentNode) {\n        div.parentNode.removeChild(div);\n      }\n    });\n    if (this._div && this._div.parentNode) {\n      this._div.parentNode.removeChild(this._div);\n    }\n    this._pool = [];\n    this._children = [];\n  }\n  /**\n   * This recursive function will run through the scene graph and add any new accessible objects to the DOM layer.\n   * @private\n   * @param {Container} container - The Container to check.\n   */\n  _updateAccessibleObjects(container) {\n    if (!container.visible || !container.accessibleChildren) {\n      return;\n    }\n    if (container.accessible) {\n      if (!container._accessibleActive) {\n        this._addChild(container);\n      }\n      container._renderId = this._renderId;\n    }\n    const children = container.children;\n    if (children) {\n      for (let i = 0; i < children.length; i++) {\n        this._updateAccessibleObjects(children[i]);\n      }\n    }\n  }\n  /**\n   * Runner init called, view is available at this point.\n   * @ignore\n   */\n  init(options) {\n    const defaultOpts = _AccessibilitySystem.defaultOptions;\n    const mergedOptions = {\n      accessibilityOptions: {\n        ...defaultOpts,\n        ...options?.accessibilityOptions || {}\n      }\n    };\n    this.debug = mergedOptions.accessibilityOptions.debug;\n    this._activateOnTab = mergedOptions.accessibilityOptions.activateOnTab;\n    this._deactivateOnMouseMove = mergedOptions.accessibilityOptions.deactivateOnMouseMove;\n    if (mergedOptions.accessibilityOptions.enabledByDefault) {\n      this._activate();\n    } else if (this._activateOnTab) {\n      this._onKeyDown = this._onKeyDown.bind(this);\n      globalThis.addEventListener(\"keydown\", this._onKeyDown, false);\n    }\n    this._renderer.runners.postrender.remove(this);\n  }\n  /**\n   * Updates the accessibility layer during rendering.\n   * - Removes divs for containers no longer in the scene\n   * - Updates the position and dimensions of the root div\n   * - Updates positions of active accessibility divs\n   * Only fires while the accessibility system is active.\n   * @ignore\n   */\n  postrender() {\n    const now = performance.now();\n    if (this._mobileInfo.android.device && now < this._androidUpdateCount) {\n      return;\n    }\n    this._androidUpdateCount = now + this._androidUpdateFrequency;\n    if (!this._renderer.renderingToScreen || !this._renderer.view.canvas) {\n      return;\n    }\n    const activeIds = /* @__PURE__ */ new Set();\n    if (this._renderer.lastObjectRendered) {\n      this._updateAccessibleObjects(this._renderer.lastObjectRendered);\n      for (const child of this._children) {\n        if (child._renderId === this._renderId) {\n          activeIds.add(this._children.indexOf(child));\n        }\n      }\n    }\n    for (let i = this._children.length - 1; i >= 0; i--) {\n      const child = this._children[i];\n      if (!activeIds.has(i)) {\n        if (child._accessibleDiv && child._accessibleDiv.parentNode) {\n          child._accessibleDiv.parentNode.removeChild(child._accessibleDiv);\n          this._pool.push(child._accessibleDiv);\n          child._accessibleDiv = null;\n        }\n        child._accessibleActive = false;\n        removeItems(this._children, i, 1);\n      }\n    }\n    if (this._renderer.renderingToScreen) {\n      const { x, y, width: viewWidth, height: viewHeight } = this._renderer.screen;\n      const div = this._div;\n      div.style.left = `${x}px`;\n      div.style.top = `${y}px`;\n      div.style.width = `${viewWidth}px`;\n      div.style.height = `${viewHeight}px`;\n    }\n    for (let i = 0; i < this._children.length; i++) {\n      const child = this._children[i];\n      if (!child._accessibleActive || !child._accessibleDiv) {\n        continue;\n      }\n      const div = child._accessibleDiv;\n      const hitArea = child.hitArea || child.getBounds().rectangle;\n      if (child.hitArea) {\n        const wt = child.worldTransform;\n        const sx = this._renderer.resolution;\n        const sy = this._renderer.resolution;\n        div.style.left = `${(wt.tx + hitArea.x * wt.a) * sx}px`;\n        div.style.top = `${(wt.ty + hitArea.y * wt.d) * sy}px`;\n        div.style.width = `${hitArea.width * wt.a * sx}px`;\n        div.style.height = `${hitArea.height * wt.d * sy}px`;\n      } else {\n        this._capHitArea(hitArea);\n        const sx = this._renderer.resolution;\n        const sy = this._renderer.resolution;\n        div.style.left = `${hitArea.x * sx}px`;\n        div.style.top = `${hitArea.y * sy}px`;\n        div.style.width = `${hitArea.width * sx}px`;\n        div.style.height = `${hitArea.height * sy}px`;\n      }\n    }\n    this._renderId++;\n  }\n  /**\n   * private function that will visually add the information to the\n   * accessibility div\n   * @param {HTMLElement} div -\n   */\n  _updateDebugHTML(div) {\n    div.innerHTML = `type: ${div.type}</br> title : ${div.title}</br> tabIndex: ${div.tabIndex}`;\n  }\n  /**\n   * Adjust the hit area based on the bounds of a display object\n   * @param {Rectangle} hitArea - Bounds of the child\n   */\n  _capHitArea(hitArea) {\n    if (hitArea.x < 0) {\n      hitArea.width += hitArea.x;\n      hitArea.x = 0;\n    }\n    if (hitArea.y < 0) {\n      hitArea.height += hitArea.y;\n      hitArea.y = 0;\n    }\n    const { width: viewWidth, height: viewHeight } = this._renderer;\n    if (hitArea.x + hitArea.width > viewWidth) {\n      hitArea.width = viewWidth - hitArea.x;\n    }\n    if (hitArea.y + hitArea.height > viewHeight) {\n      hitArea.height = viewHeight - hitArea.y;\n    }\n  }\n  /**\n   * Creates or reuses a div element for a Container and adds it to the accessibility layer.\n   * Sets up ARIA attributes, event listeners, and positioning based on the container's properties.\n   * @private\n   * @param {Container} container - The child to make accessible.\n   */\n  _addChild(container) {\n    let div = this._pool.pop();\n    if (!div) {\n      if (container.accessibleType === \"button\") {\n        div = document.createElement(\"button\");\n      } else {\n        div = document.createElement(container.accessibleType);\n        div.style.cssText = `\n                        color: transparent;\n                        pointer-events: none;\n                        padding: 0;\n                        margin: 0;\n                        border: 0;\n                        outline: 0;\n                        background: transparent;\n                        box-sizing: border-box;\n                        user-select: none;\n                        -webkit-user-select: none;\n                        -moz-user-select: none;\n                        -ms-user-select: none;\n                    `;\n        if (container.accessibleText) {\n          div.innerText = container.accessibleText;\n        }\n      }\n      div.style.width = `${DIV_TOUCH_SIZE}px`;\n      div.style.height = `${DIV_TOUCH_SIZE}px`;\n      div.style.backgroundColor = this.debug ? \"rgba(255,255,255,0.5)\" : \"transparent\";\n      div.style.position = \"absolute\";\n      div.style.zIndex = DIV_TOUCH_ZINDEX.toString();\n      div.style.borderStyle = \"none\";\n      if (navigator.userAgent.toLowerCase().includes(\"chrome\")) {\n        div.setAttribute(\"aria-live\", \"off\");\n      } else {\n        div.setAttribute(\"aria-live\", \"polite\");\n      }\n      if (navigator.userAgent.match(/rv:.*Gecko\\//)) {\n        div.setAttribute(\"aria-relevant\", \"additions\");\n      } else {\n        div.setAttribute(\"aria-relevant\", \"text\");\n      }\n      div.addEventListener(\"click\", this._onClick.bind(this));\n      div.addEventListener(\"focus\", this._onFocus.bind(this));\n      div.addEventListener(\"focusout\", this._onFocusOut.bind(this));\n    }\n    div.style.pointerEvents = container.accessiblePointerEvents;\n    div.type = container.accessibleType;\n    if (container.accessibleTitle && container.accessibleTitle !== null) {\n      div.title = container.accessibleTitle;\n    } else if (!container.accessibleHint || container.accessibleHint === null) {\n      div.title = `container ${container.tabIndex}`;\n    }\n    if (container.accessibleHint && container.accessibleHint !== null) {\n      div.setAttribute(\"aria-label\", container.accessibleHint);\n    }\n    if (this.debug) {\n      this._updateDebugHTML(div);\n    }\n    container._accessibleActive = true;\n    container._accessibleDiv = div;\n    div.container = container;\n    this._children.push(container);\n    this._div.appendChild(container._accessibleDiv);\n    if (container.interactive) {\n      container._accessibleDiv.tabIndex = container.tabIndex;\n    }\n  }\n  /**\n   * Dispatch events with the EventSystem.\n   * @param e\n   * @param type\n   * @private\n   */\n  _dispatchEvent(e, type) {\n    const { container: target } = e.target;\n    const boundary = this._renderer.events.rootBoundary;\n    const event = Object.assign(new FederatedEvent(boundary), { target });\n    boundary.rootTarget = this._renderer.lastObjectRendered;\n    type.forEach((type2) => boundary.dispatchEvent(event, type2));\n  }\n  /**\n   * Maps the div button press to pixi's EventSystem (click)\n   * @private\n   * @param {MouseEvent} e - The click event.\n   */\n  _onClick(e) {\n    this._dispatchEvent(e, [\"click\", \"pointertap\", \"tap\"]);\n  }\n  /**\n   * Maps the div focus events to pixi's EventSystem (mouseover)\n   * @private\n   * @param {FocusEvent} e - The focus event.\n   */\n  _onFocus(e) {\n    if (!e.target.getAttribute(\"aria-live\")) {\n      e.target.setAttribute(\"aria-live\", \"assertive\");\n    }\n    this._dispatchEvent(e, [\"mouseover\"]);\n  }\n  /**\n   * Maps the div focus events to pixi's EventSystem (mouseout)\n   * @private\n   * @param {FocusEvent} e - The focusout event.\n   */\n  _onFocusOut(e) {\n    if (!e.target.getAttribute(\"aria-live\")) {\n      e.target.setAttribute(\"aria-live\", \"polite\");\n    }\n    this._dispatchEvent(e, [\"mouseout\"]);\n  }\n  /**\n   * Is called when a key is pressed\n   * @private\n   * @param {KeyboardEvent} e - The keydown event.\n   */\n  _onKeyDown(e) {\n    if (e.keyCode !== KEY_CODE_TAB || !this._activateOnTab) {\n      return;\n    }\n    this._activate();\n  }\n  /**\n   * Is called when the mouse moves across the renderer element\n   * @private\n   * @param {MouseEvent} e - The mouse event.\n   */\n  _onMouseMove(e) {\n    if (e.movementX === 0 && e.movementY === 0) {\n      return;\n    }\n    this._deactivate();\n  }\n  /** Destroys the accessibility system. Removes all elements and listeners. */\n  destroy() {\n    this._deactivate();\n    this._destroyTouchHook();\n    this._div = null;\n    this._pool = null;\n    this._children = null;\n    this._renderer = null;\n    if (this._activateOnTab) {\n      globalThis.removeEventListener(\"keydown\", this._onKeyDown);\n    }\n  }\n  /**\n   * Enables or disables the accessibility system.\n   * @param enabled - Whether to enable or disable accessibility.\n   */\n  setAccessibilityEnabled(enabled) {\n    if (enabled) {\n      this._activate();\n    } else {\n      this._deactivate();\n    }\n  }\n};\n/** @ignore */\n_AccessibilitySystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"accessibility\"\n};\n/** default options used by the system */\n_AccessibilitySystem.defaultOptions = {\n  /**\n   * Whether to enable accessibility features on initialization\n   * @default false\n   */\n  enabledByDefault: false,\n  /**\n   * Whether to visually show the accessibility divs for debugging\n   * @default false\n   */\n  debug: false,\n  /**\n   * Whether to activate accessibility when tab key is pressed\n   * @default true\n   */\n  activateOnTab: true,\n  /**\n   * Whether to deactivate accessibility when mouse moves\n   * @default true\n   */\n  deactivateOnMouseMove: true\n};\nlet AccessibilitySystem = _AccessibilitySystem;\n\nexport { AccessibilitySystem };\n//# sourceMappingURL=AccessibilitySystem.mjs.map\n", "\"use strict\";\nconst accessibilityTarget = {\n  /**\n   * Flag for if the object is accessible. If true AccessibilityManager will overlay a\n   * shadow div with attributes set\n   * @member {boolean}\n   * @memberof scene.Container#\n   */\n  accessible: false,\n  /**\n   * Sets the title attribute of the shadow div\n   * If accessibleTitle AND accessibleHint has not been this will default to 'container [tabIndex]'\n   * @member {string}\n   * @memberof scene.Container#\n   */\n  accessibleTitle: null,\n  /**\n   * Sets the aria-label attribute of the shadow div\n   * @member {string}\n   * @memberof scene.Container#\n   */\n  accessibleHint: null,\n  /**\n   * @member {number}\n   * @memberof scene.Container#\n   * @todo Needs docs.\n   */\n  tabIndex: 0,\n  /**\n   * @member {boolean}\n   * @memberof scene.Container#\n   * @private\n   */\n  _accessibleActive: false,\n  /**\n   * @memberof scene.Container#\n   * @private\n   */\n  _accessibleDiv: null,\n  /**\n   * Specify the type of div the accessible layer is. Screen readers treat the element differently\n   * depending on this type. Defaults to button.\n   * @member {string}\n   * @memberof scene.Container#\n   * @default 'button'\n   */\n  accessibleType: \"button\",\n  /**\n   * Sets the text content of the shadow div\n   * @member {string}\n   * @memberof scene.Container#\n   */\n  accessibleText: null,\n  /**\n   * Specify the pointer-events the accessible div will use\n   * Defaults to auto.\n   * @type {PointerEvents}\n   * @memberof scene.Container#\n   * @default 'auto'\n   */\n  accessiblePointerEvents: \"auto\",\n  /**\n   * Setting to false will prevent any children inside this container to\n   * be accessible. Defaults to true.\n   * @member {boolean}\n   * @memberof scene.Container#\n   * @default true\n   */\n  accessibleChildren: true,\n  /**\n   * @member {number}\n   * @memberof scene.Container#\n   * @private\n   */\n  _renderId: -1\n};\n\nexport { accessibilityTarget };\n//# sourceMappingURL=accessibilityTarget.mjs.map\n", "import { UPDATE_PRIORITY } from '../ticker/const.mjs';\nimport { Ticker } from '../ticker/Ticker.mjs';\n\n\"use strict\";\nclass EventsTickerClass {\n  constructor() {\n    /** The frequency that fake events will be fired. */\n    this.interactionFrequency = 10;\n    this._deltaTime = 0;\n    this._didMove = false;\n    this._tickerAdded = false;\n    this._pauseUpdate = true;\n  }\n  /**\n   * Initializes the event ticker.\n   * @param events - The event system.\n   */\n  init(events) {\n    this.removeTickerListener();\n    this.events = events;\n    this.interactionFrequency = 10;\n    this._deltaTime = 0;\n    this._didMove = false;\n    this._tickerAdded = false;\n    this._pauseUpdate = true;\n  }\n  /** Whether to pause the update checks or not. */\n  get pauseUpdate() {\n    return this._pauseUpdate;\n  }\n  set pauseUpdate(paused) {\n    this._pauseUpdate = paused;\n  }\n  /** Adds the ticker listener. */\n  addTickerListener() {\n    if (this._tickerAdded || !this.domElement) {\n      return;\n    }\n    Ticker.system.add(this._tickerUpdate, this, UPDATE_PRIORITY.INTERACTION);\n    this._tickerAdded = true;\n  }\n  /** Removes the ticker listener. */\n  removeTickerListener() {\n    if (!this._tickerAdded) {\n      return;\n    }\n    Ticker.system.remove(this._tickerUpdate, this);\n    this._tickerAdded = false;\n  }\n  /** Sets flag to not fire extra events when the user has already moved there mouse */\n  pointerMoved() {\n    this._didMove = true;\n  }\n  /** Updates the state of interactive objects. */\n  _update() {\n    if (!this.domElement || this._pauseUpdate) {\n      return;\n    }\n    if (this._didMove) {\n      this._didMove = false;\n      return;\n    }\n    const rootPointerEvent = this.events[\"_rootPointerEvent\"];\n    if (this.events.supportsTouchEvents && rootPointerEvent.pointerType === \"touch\") {\n      return;\n    }\n    globalThis.document.dispatchEvent(new PointerEvent(\"pointermove\", {\n      clientX: rootPointerEvent.clientX,\n      clientY: rootPointerEvent.clientY,\n      pointerType: rootPointerEvent.pointerType,\n      pointerId: rootPointerEvent.pointerId\n    }));\n  }\n  /**\n   * Updates the state of interactive objects if at least {@link interactionFrequency}\n   * milliseconds have passed since the last invocation.\n   *\n   * Invoked by a throttled ticker update from {@link Ticker.system}.\n   * @param ticker - The throttled ticker.\n   */\n  _tickerUpdate(ticker) {\n    this._deltaTime += ticker.deltaTime;\n    if (this._deltaTime < this.interactionFrequency) {\n      return;\n    }\n    this._deltaTime = 0;\n    this._update();\n  }\n}\nconst EventsTicker = new EventsTickerClass();\n\nexport { EventsTicker };\n//# sourceMappingURL=EventTicker.mjs.map\n", "import { Point } from '../maths/point/Point.mjs';\nimport { FederatedEvent } from './FederatedEvent.mjs';\n\n\"use strict\";\nclass FederatedMouseEvent extends FederatedEvent {\n  constructor() {\n    super(...arguments);\n    /** The coordinates of the mouse event relative to the canvas. */\n    this.client = new Point();\n    /** The movement in this pointer relative to the last `mousemove` event. */\n    this.movement = new Point();\n    /** The offset of the pointer coordinates w.r.t. target Container in world space. This is not supported at the moment. */\n    this.offset = new Point();\n    /** The pointer coordinates in world space. */\n    this.global = new Point();\n    /**\n     * The pointer coordinates in the renderer's {@link Renderer.screen screen}. This has slightly\n     * different semantics than native PointerEvent screenX/screenY.\n     */\n    this.screen = new Point();\n  }\n  /** @readonly */\n  get clientX() {\n    return this.client.x;\n  }\n  /** @readonly */\n  get clientY() {\n    return this.client.y;\n  }\n  /**\n   * Alias for {@link FederatedMouseEvent.clientX this.clientX}.\n   * @readonly\n   */\n  get x() {\n    return this.clientX;\n  }\n  /**\n   * <PERSON>as for {@link FederatedMouseEvent.clientY this.clientY}.\n   * @readonly\n   */\n  get y() {\n    return this.clientY;\n  }\n  /** @readonly */\n  get movementX() {\n    return this.movement.x;\n  }\n  /** @readonly */\n  get movementY() {\n    return this.movement.y;\n  }\n  /** @readonly */\n  get offsetX() {\n    return this.offset.x;\n  }\n  /** @readonly */\n  get offsetY() {\n    return this.offset.y;\n  }\n  /** @readonly */\n  get globalX() {\n    return this.global.x;\n  }\n  /** @readonly */\n  get globalY() {\n    return this.global.y;\n  }\n  /**\n   * The pointer coordinates in the renderer's screen. Alias for {@code screen.x}.\n   * @readonly\n   */\n  get screenX() {\n    return this.screen.x;\n  }\n  /**\n   * The pointer coordinates in the renderer's screen. Alias for {@code screen.y}.\n   * @readonly\n   */\n  get screenY() {\n    return this.screen.y;\n  }\n  /**\n   * This will return the local coordinates of the specified container for this InteractionData\n   * @param {Container} container - The Container that you would like the local\n   *  coords off\n   * @param {PointData} point - A Point object in which to store the value, optional (otherwise\n   *  will create a new point)\n   * @param {PointData} globalPos - A Point object containing your custom global coords, optional\n   *  (otherwise will use the current global coords)\n   * @returns - A point containing the coordinates of the InteractionData position relative\n   *  to the Container\n   */\n  getLocalPosition(container, point, globalPos) {\n    return container.worldTransform.applyInverse(globalPos || this.global, point);\n  }\n  /**\n   * Whether the modifier key was pressed when this event natively occurred.\n   * @param key - The modifier key.\n   */\n  getModifierState(key) {\n    return \"getModifierState\" in this.nativeEvent && this.nativeEvent.getModifierState(key);\n  }\n  /**\n   * Not supported.\n   * @param _typeArg\n   * @param _canBubbleArg\n   * @param _cancelableArg\n   * @param _viewArg\n   * @param _detailArg\n   * @param _screenXArg\n   * @param _screenYArg\n   * @param _clientXArg\n   * @param _clientYArg\n   * @param _ctrlKeyArg\n   * @param _altKeyArg\n   * @param _shiftKeyArg\n   * @param _metaKeyArg\n   * @param _buttonArg\n   * @param _relatedTargetArg\n   * @deprecated since 7.0.0\n   */\n  // eslint-disable-next-line max-params\n  initMouseEvent(_typeArg, _canBubbleArg, _cancelableArg, _viewArg, _detailArg, _screenXArg, _screenYArg, _clientXArg, _clientYArg, _ctrlKeyArg, _altKeyArg, _shiftKeyArg, _metaKeyArg, _buttonArg, _relatedTargetArg) {\n    throw new Error(\"Method not implemented.\");\n  }\n}\n\nexport { FederatedMouseEvent };\n//# sourceMappingURL=FederatedMouseEvent.mjs.map\n", "import { FederatedMouseEvent } from './FederatedMouseEvent.mjs';\n\n\"use strict\";\nclass FederatedPointerEvent extends FederatedMouseEvent {\n  constructor() {\n    super(...arguments);\n    /**\n     * The width of the pointer's contact along the x-axis, measured in CSS pixels.\n     * radiusX of TouchEvents will be represented by this value.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/width\n     */\n    this.width = 0;\n    /**\n     * The height of the pointer's contact along the y-axis, measured in CSS pixels.\n     * radiusY of TouchEvents will be represented by this value.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/height\n     */\n    this.height = 0;\n    /**\n     * Indicates whether or not the pointer device that created the event is the primary pointer.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/isPrimary\n     */\n    this.isPrimary = false;\n  }\n  // Only included for completeness for now\n  getCoalescedEvents() {\n    if (this.type === \"pointermove\" || this.type === \"mousemove\" || this.type === \"touchmove\") {\n      return [this];\n    }\n    return [];\n  }\n  // Only included for completeness for now\n  getPredictedEvents() {\n    throw new Error(\"getPredictedEvents is not supported!\");\n  }\n}\n\nexport { FederatedPointerEvent };\n//# sourceMappingURL=FederatedPointerEvent.mjs.map\n", "import { FederatedMouseEvent } from './FederatedMouseEvent.mjs';\n\n\"use strict\";\nclass FederatedWheelEvent extends FederatedMouseEvent {\n  constructor() {\n    super(...arguments);\n    /** Units specified in pixels. */\n    this.DOM_DELTA_PIXEL = 0;\n    /** Units specified in lines. */\n    this.DOM_DELTA_LINE = 1;\n    /** Units specified in pages. */\n    this.DOM_DELTA_PAGE = 2;\n  }\n}\n/** Units specified in pixels. */\nFederatedWheelEvent.DOM_DELTA_PIXEL = 0;\n/** Units specified in lines. */\nFederatedWheelEvent.DOM_DELTA_LINE = 1;\n/** Units specified in pages. */\nFederatedWheelEvent.DOM_DELTA_PAGE = 2;\n\nexport { FederatedWheelEvent };\n//# sourceMappingURL=FederatedWheelEvent.mjs.map\n", "import EventEmitter from 'eventemitter3';\nimport { Point } from '../maths/point/Point.mjs';\nimport { warn } from '../utils/logging/warn.mjs';\nimport { EventsTicker } from './EventTicker.mjs';\nimport { FederatedMouseEvent } from './FederatedMouseEvent.mjs';\nimport { FederatedPointerEvent } from './FederatedPointerEvent.mjs';\nimport { FederatedWheelEvent } from './FederatedWheelEvent.mjs';\n\n\"use strict\";\nconst PROPAGATION_LIMIT = 2048;\nconst tempHitLocation = new Point();\nconst tempLocalMapping = new Point();\nclass EventBoundary {\n  /**\n   * @param rootTarget - The holder of the event boundary.\n   */\n  constructor(rootTarget) {\n    /**\n     * Emits events after they were dispatched into the scene graph.\n     *\n     * This can be used for global events listening, regardless of the scene graph being used. It should\n     * not be used by interactive libraries for normal use.\n     *\n     * Special events that do not bubble all the way to the root target are not emitted from here,\n     * e.g. pointerenter, pointerleave, click.\n     */\n    this.dispatch = new EventEmitter();\n    /**\n     * This flag would emit `pointermove`, `touchmove`, and `mousemove` events on all Containers.\n     *\n     * The `moveOnAll` semantics mirror those of earlier versions of PixiJS. This was disabled in favor of\n     * the Pointer Event API's approach.\n     */\n    this.moveOnAll = false;\n    /** Enables the global move events. `globalpointermove`, `globaltouchmove`, and `globalmousemove` */\n    this.enableGlobalMoveEvents = true;\n    /**\n     * State object for mapping methods.\n     * @see EventBoundary#trackingData\n     */\n    this.mappingState = {\n      trackingData: {}\n    };\n    /**\n     * The event pool maps event constructors to an free pool of instances of those specific events.\n     * @see EventBoundary#allocateEvent\n     * @see EventBoundary#freeEvent\n     */\n    this.eventPool = /* @__PURE__ */ new Map();\n    /** Every interactive element gathered from the scene. Only used in `pointermove` */\n    this._allInteractiveElements = [];\n    /** Every element that passed the hit test. Only used in `pointermove` */\n    this._hitElements = [];\n    /** Whether or not to collect all the interactive elements from the scene. Enabled in `pointermove` */\n    this._isPointerMoveEvent = false;\n    this.rootTarget = rootTarget;\n    this.hitPruneFn = this.hitPruneFn.bind(this);\n    this.hitTestFn = this.hitTestFn.bind(this);\n    this.mapPointerDown = this.mapPointerDown.bind(this);\n    this.mapPointerMove = this.mapPointerMove.bind(this);\n    this.mapPointerOut = this.mapPointerOut.bind(this);\n    this.mapPointerOver = this.mapPointerOver.bind(this);\n    this.mapPointerUp = this.mapPointerUp.bind(this);\n    this.mapPointerUpOutside = this.mapPointerUpOutside.bind(this);\n    this.mapWheel = this.mapWheel.bind(this);\n    this.mappingTable = {};\n    this.addEventMapping(\"pointerdown\", this.mapPointerDown);\n    this.addEventMapping(\"pointermove\", this.mapPointerMove);\n    this.addEventMapping(\"pointerout\", this.mapPointerOut);\n    this.addEventMapping(\"pointerleave\", this.mapPointerOut);\n    this.addEventMapping(\"pointerover\", this.mapPointerOver);\n    this.addEventMapping(\"pointerup\", this.mapPointerUp);\n    this.addEventMapping(\"pointerupoutside\", this.mapPointerUpOutside);\n    this.addEventMapping(\"wheel\", this.mapWheel);\n  }\n  /**\n   * Adds an event mapping for the event `type` handled by `fn`.\n   *\n   * Event mappings can be used to implement additional or custom events. They take an event\n   * coming from the upstream scene (or directly from the {@link EventSystem}) and dispatch new downstream events\n   * generally trickling down and bubbling up to {@link EventBoundary.rootTarget this.rootTarget}.\n   *\n   * To modify the semantics of existing events, the built-in mapping methods of EventBoundary should be overridden\n   * instead.\n   * @param type - The type of upstream event to map.\n   * @param fn - The mapping method. The context of this function must be bound manually, if desired.\n   */\n  addEventMapping(type, fn) {\n    if (!this.mappingTable[type]) {\n      this.mappingTable[type] = [];\n    }\n    this.mappingTable[type].push({\n      fn,\n      priority: 0\n    });\n    this.mappingTable[type].sort((a, b) => a.priority - b.priority);\n  }\n  /**\n   * Dispatches the given event\n   * @param e - The event to dispatch.\n   * @param type - The type of event to dispatch. Defaults to `e.type`.\n   */\n  dispatchEvent(e, type) {\n    e.propagationStopped = false;\n    e.propagationImmediatelyStopped = false;\n    this.propagate(e, type);\n    this.dispatch.emit(type || e.type, e);\n  }\n  /**\n   * Maps the given upstream event through the event boundary and propagates it downstream.\n   * @param e - The event to map.\n   */\n  mapEvent(e) {\n    if (!this.rootTarget) {\n      return;\n    }\n    const mappers = this.mappingTable[e.type];\n    if (mappers) {\n      for (let i = 0, j = mappers.length; i < j; i++) {\n        mappers[i].fn(e);\n      }\n    } else {\n      warn(`[EventBoundary]: Event mapping not defined for ${e.type}`);\n    }\n  }\n  /**\n   * Finds the Container that is the target of a event at the given coordinates.\n   *\n   * The passed (x,y) coordinates are in the world space above this event boundary.\n   * @param x - The x coordinate of the event.\n   * @param y - The y coordinate of the event.\n   */\n  hitTest(x, y) {\n    EventsTicker.pauseUpdate = true;\n    const useMove = this._isPointerMoveEvent && this.enableGlobalMoveEvents;\n    const fn = useMove ? \"hitTestMoveRecursive\" : \"hitTestRecursive\";\n    const invertedPath = this[fn](\n      this.rootTarget,\n      this.rootTarget.eventMode,\n      tempHitLocation.set(x, y),\n      this.hitTestFn,\n      this.hitPruneFn\n    );\n    return invertedPath && invertedPath[0];\n  }\n  /**\n   * Propagate the passed event from from {@link EventBoundary.rootTarget this.rootTarget} to its\n   * target {@code e.target}.\n   * @param e - The event to propagate.\n   * @param type - The type of event to propagate. Defaults to `e.type`.\n   */\n  propagate(e, type) {\n    if (!e.target) {\n      return;\n    }\n    const composedPath = e.composedPath();\n    e.eventPhase = e.CAPTURING_PHASE;\n    for (let i = 0, j = composedPath.length - 1; i < j; i++) {\n      e.currentTarget = composedPath[i];\n      this.notifyTarget(e, type);\n      if (e.propagationStopped || e.propagationImmediatelyStopped)\n        return;\n    }\n    e.eventPhase = e.AT_TARGET;\n    e.currentTarget = e.target;\n    this.notifyTarget(e, type);\n    if (e.propagationStopped || e.propagationImmediatelyStopped)\n      return;\n    e.eventPhase = e.BUBBLING_PHASE;\n    for (let i = composedPath.length - 2; i >= 0; i--) {\n      e.currentTarget = composedPath[i];\n      this.notifyTarget(e, type);\n      if (e.propagationStopped || e.propagationImmediatelyStopped)\n        return;\n    }\n  }\n  /**\n   * Emits the event {@code e} to all interactive containers. The event is propagated in the bubbling phase always.\n   *\n   * This is used in the `globalpointermove` event.\n   * @param e - The emitted event.\n   * @param type - The listeners to notify.\n   * @param targets - The targets to notify.\n   */\n  all(e, type, targets = this._allInteractiveElements) {\n    if (targets.length === 0)\n      return;\n    e.eventPhase = e.BUBBLING_PHASE;\n    const events = Array.isArray(type) ? type : [type];\n    for (let i = targets.length - 1; i >= 0; i--) {\n      events.forEach((event) => {\n        e.currentTarget = targets[i];\n        this.notifyTarget(e, event);\n      });\n    }\n  }\n  /**\n   * Finds the propagation path from {@link EventBoundary.rootTarget rootTarget} to the passed\n   * {@code target}. The last element in the path is {@code target}.\n   * @param target - The target to find the propagation path to.\n   */\n  propagationPath(target) {\n    const propagationPath = [target];\n    for (let i = 0; i < PROPAGATION_LIMIT && (target !== this.rootTarget && target.parent); i++) {\n      if (!target.parent) {\n        throw new Error(\"Cannot find propagation path to disconnected target\");\n      }\n      propagationPath.push(target.parent);\n      target = target.parent;\n    }\n    propagationPath.reverse();\n    return propagationPath;\n  }\n  hitTestMoveRecursive(currentTarget, eventMode, location, testFn, pruneFn, ignore = false) {\n    let shouldReturn = false;\n    if (this._interactivePrune(currentTarget))\n      return null;\n    if (currentTarget.eventMode === \"dynamic\" || eventMode === \"dynamic\") {\n      EventsTicker.pauseUpdate = false;\n    }\n    if (currentTarget.interactiveChildren && currentTarget.children) {\n      const children = currentTarget.children;\n      for (let i = children.length - 1; i >= 0; i--) {\n        const child = children[i];\n        const nestedHit = this.hitTestMoveRecursive(\n          child,\n          this._isInteractive(eventMode) ? eventMode : child.eventMode,\n          location,\n          testFn,\n          pruneFn,\n          ignore || pruneFn(currentTarget, location)\n        );\n        if (nestedHit) {\n          if (nestedHit.length > 0 && !nestedHit[nestedHit.length - 1].parent) {\n            continue;\n          }\n          const isInteractive = currentTarget.isInteractive();\n          if (nestedHit.length > 0 || isInteractive) {\n            if (isInteractive)\n              this._allInteractiveElements.push(currentTarget);\n            nestedHit.push(currentTarget);\n          }\n          if (this._hitElements.length === 0)\n            this._hitElements = nestedHit;\n          shouldReturn = true;\n        }\n      }\n    }\n    const isInteractiveMode = this._isInteractive(eventMode);\n    const isInteractiveTarget = currentTarget.isInteractive();\n    if (isInteractiveTarget && isInteractiveTarget)\n      this._allInteractiveElements.push(currentTarget);\n    if (ignore || this._hitElements.length > 0)\n      return null;\n    if (shouldReturn)\n      return this._hitElements;\n    if (isInteractiveMode && (!pruneFn(currentTarget, location) && testFn(currentTarget, location))) {\n      return isInteractiveTarget ? [currentTarget] : [];\n    }\n    return null;\n  }\n  /**\n   * Recursive implementation for {@link EventBoundary.hitTest hitTest}.\n   * @param currentTarget - The Container that is to be hit tested.\n   * @param eventMode - The event mode for the `currentTarget` or one of its parents.\n   * @param location - The location that is being tested for overlap.\n   * @param testFn - Callback that determines whether the target passes hit testing. This callback\n   *  can assume that `pruneFn` failed to prune the container.\n   * @param pruneFn - Callback that determiness whether the target and all of its children\n   *  cannot pass the hit test. It is used as a preliminary optimization to prune entire subtrees\n   *  of the scene graph.\n   * @returns An array holding the hit testing target and all its ancestors in order. The first element\n   *  is the target itself and the last is {@link EventBoundary.rootTarget rootTarget}. This is the opposite\n   *  order w.r.t. the propagation path. If no hit testing target is found, null is returned.\n   */\n  hitTestRecursive(currentTarget, eventMode, location, testFn, pruneFn) {\n    if (this._interactivePrune(currentTarget) || pruneFn(currentTarget, location)) {\n      return null;\n    }\n    if (currentTarget.eventMode === \"dynamic\" || eventMode === \"dynamic\") {\n      EventsTicker.pauseUpdate = false;\n    }\n    if (currentTarget.interactiveChildren && currentTarget.children) {\n      const children = currentTarget.children;\n      const relativeLocation = location;\n      for (let i = children.length - 1; i >= 0; i--) {\n        const child = children[i];\n        const nestedHit = this.hitTestRecursive(\n          child,\n          this._isInteractive(eventMode) ? eventMode : child.eventMode,\n          relativeLocation,\n          testFn,\n          pruneFn\n        );\n        if (nestedHit) {\n          if (nestedHit.length > 0 && !nestedHit[nestedHit.length - 1].parent) {\n            continue;\n          }\n          const isInteractive = currentTarget.isInteractive();\n          if (nestedHit.length > 0 || isInteractive)\n            nestedHit.push(currentTarget);\n          return nestedHit;\n        }\n      }\n    }\n    const isInteractiveMode = this._isInteractive(eventMode);\n    const isInteractiveTarget = currentTarget.isInteractive();\n    if (isInteractiveMode && testFn(currentTarget, location)) {\n      return isInteractiveTarget ? [currentTarget] : [];\n    }\n    return null;\n  }\n  _isInteractive(int) {\n    return int === \"static\" || int === \"dynamic\";\n  }\n  _interactivePrune(container) {\n    if (!container || !container.visible || !container.renderable || !container.measurable) {\n      return true;\n    }\n    if (container.eventMode === \"none\") {\n      return true;\n    }\n    if (container.eventMode === \"passive\" && !container.interactiveChildren) {\n      return true;\n    }\n    return false;\n  }\n  /**\n   * Checks whether the container or any of its children cannot pass the hit test at all.\n   *\n   * {@link EventBoundary}'s implementation uses the {@link Container.hitArea hitArea}\n   * and {@link Container._maskEffect} for pruning.\n   * @param container - The container to prune.\n   * @param location - The location to test for overlap.\n   */\n  hitPruneFn(container, location) {\n    if (container.hitArea) {\n      container.worldTransform.applyInverse(location, tempLocalMapping);\n      if (!container.hitArea.contains(tempLocalMapping.x, tempLocalMapping.y)) {\n        return true;\n      }\n    }\n    if (container.effects && container.effects.length) {\n      for (let i = 0; i < container.effects.length; i++) {\n        const effect = container.effects[i];\n        if (effect.containsPoint) {\n          const effectContainsPoint = effect.containsPoint(location, this.hitTestFn);\n          if (!effectContainsPoint) {\n            return true;\n          }\n        }\n      }\n    }\n    return false;\n  }\n  /**\n   * Checks whether the container passes hit testing for the given location.\n   * @param container - The container to test.\n   * @param location - The location to test for overlap.\n   * @returns - Whether `container` passes hit testing for `location`.\n   */\n  hitTestFn(container, location) {\n    if (container.hitArea) {\n      return true;\n    }\n    if (container?.containsPoint) {\n      container.worldTransform.applyInverse(location, tempLocalMapping);\n      return container.containsPoint(tempLocalMapping);\n    }\n    return false;\n  }\n  /**\n   * Notify all the listeners to the event's `currentTarget`.\n   *\n   * If the `currentTarget` contains the property `on<type>`, then it is called here,\n   * simulating the behavior from version 6.x and prior.\n   * @param e - The event passed to the target.\n   * @param type - The type of event to notify. Defaults to `e.type`.\n   */\n  notifyTarget(e, type) {\n    if (!e.currentTarget.isInteractive()) {\n      return;\n    }\n    type ?? (type = e.type);\n    const handlerKey = `on${type}`;\n    e.currentTarget[handlerKey]?.(e);\n    const key = e.eventPhase === e.CAPTURING_PHASE || e.eventPhase === e.AT_TARGET ? `${type}capture` : type;\n    this._notifyListeners(e, key);\n    if (e.eventPhase === e.AT_TARGET) {\n      this._notifyListeners(e, type);\n    }\n  }\n  /**\n   * Maps the upstream `pointerdown` events to a downstream `pointerdown` event.\n   *\n   * `touchstart`, `rightdown`, `mousedown` events are also dispatched for specific pointer types.\n   * @param from - The upstream `pointerdown` event.\n   */\n  mapPointerDown(from) {\n    if (!(from instanceof FederatedPointerEvent)) {\n      warn(\"EventBoundary cannot map a non-pointer event as a pointer event\");\n      return;\n    }\n    const e = this.createPointerEvent(from);\n    this.dispatchEvent(e, \"pointerdown\");\n    if (e.pointerType === \"touch\") {\n      this.dispatchEvent(e, \"touchstart\");\n    } else if (e.pointerType === \"mouse\" || e.pointerType === \"pen\") {\n      const isRightButton = e.button === 2;\n      this.dispatchEvent(e, isRightButton ? \"rightdown\" : \"mousedown\");\n    }\n    const trackingData = this.trackingData(from.pointerId);\n    trackingData.pressTargetsByButton[from.button] = e.composedPath();\n    this.freeEvent(e);\n  }\n  /**\n   * Maps the upstream `pointermove` to downstream `pointerout`, `pointerover`, and `pointermove` events, in that order.\n   *\n   * The tracking data for the specific pointer has an updated `overTarget`. `mouseout`, `mouseover`,\n   * `mousemove`, and `touchmove` events are fired as well for specific pointer types.\n   * @param from - The upstream `pointermove` event.\n   */\n  mapPointerMove(from) {\n    if (!(from instanceof FederatedPointerEvent)) {\n      warn(\"EventBoundary cannot map a non-pointer event as a pointer event\");\n      return;\n    }\n    this._allInteractiveElements.length = 0;\n    this._hitElements.length = 0;\n    this._isPointerMoveEvent = true;\n    const e = this.createPointerEvent(from);\n    this._isPointerMoveEvent = false;\n    const isMouse = e.pointerType === \"mouse\" || e.pointerType === \"pen\";\n    const trackingData = this.trackingData(from.pointerId);\n    const outTarget = this.findMountedTarget(trackingData.overTargets);\n    if (trackingData.overTargets?.length > 0 && outTarget !== e.target) {\n      const outType = from.type === \"mousemove\" ? \"mouseout\" : \"pointerout\";\n      const outEvent = this.createPointerEvent(from, outType, outTarget);\n      this.dispatchEvent(outEvent, \"pointerout\");\n      if (isMouse)\n        this.dispatchEvent(outEvent, \"mouseout\");\n      if (!e.composedPath().includes(outTarget)) {\n        const leaveEvent = this.createPointerEvent(from, \"pointerleave\", outTarget);\n        leaveEvent.eventPhase = leaveEvent.AT_TARGET;\n        while (leaveEvent.target && !e.composedPath().includes(leaveEvent.target)) {\n          leaveEvent.currentTarget = leaveEvent.target;\n          this.notifyTarget(leaveEvent);\n          if (isMouse)\n            this.notifyTarget(leaveEvent, \"mouseleave\");\n          leaveEvent.target = leaveEvent.target.parent;\n        }\n        this.freeEvent(leaveEvent);\n      }\n      this.freeEvent(outEvent);\n    }\n    if (outTarget !== e.target) {\n      const overType = from.type === \"mousemove\" ? \"mouseover\" : \"pointerover\";\n      const overEvent = this.clonePointerEvent(e, overType);\n      this.dispatchEvent(overEvent, \"pointerover\");\n      if (isMouse)\n        this.dispatchEvent(overEvent, \"mouseover\");\n      let overTargetAncestor = outTarget?.parent;\n      while (overTargetAncestor && overTargetAncestor !== this.rootTarget.parent) {\n        if (overTargetAncestor === e.target)\n          break;\n        overTargetAncestor = overTargetAncestor.parent;\n      }\n      const didPointerEnter = !overTargetAncestor || overTargetAncestor === this.rootTarget.parent;\n      if (didPointerEnter) {\n        const enterEvent = this.clonePointerEvent(e, \"pointerenter\");\n        enterEvent.eventPhase = enterEvent.AT_TARGET;\n        while (enterEvent.target && enterEvent.target !== outTarget && enterEvent.target !== this.rootTarget.parent) {\n          enterEvent.currentTarget = enterEvent.target;\n          this.notifyTarget(enterEvent);\n          if (isMouse)\n            this.notifyTarget(enterEvent, \"mouseenter\");\n          enterEvent.target = enterEvent.target.parent;\n        }\n        this.freeEvent(enterEvent);\n      }\n      this.freeEvent(overEvent);\n    }\n    const allMethods = [];\n    const allowGlobalPointerEvents = this.enableGlobalMoveEvents ?? true;\n    this.moveOnAll ? allMethods.push(\"pointermove\") : this.dispatchEvent(e, \"pointermove\");\n    allowGlobalPointerEvents && allMethods.push(\"globalpointermove\");\n    if (e.pointerType === \"touch\") {\n      this.moveOnAll ? allMethods.splice(1, 0, \"touchmove\") : this.dispatchEvent(e, \"touchmove\");\n      allowGlobalPointerEvents && allMethods.push(\"globaltouchmove\");\n    }\n    if (isMouse) {\n      this.moveOnAll ? allMethods.splice(1, 0, \"mousemove\") : this.dispatchEvent(e, \"mousemove\");\n      allowGlobalPointerEvents && allMethods.push(\"globalmousemove\");\n      this.cursor = e.target?.cursor;\n    }\n    if (allMethods.length > 0) {\n      this.all(e, allMethods);\n    }\n    this._allInteractiveElements.length = 0;\n    this._hitElements.length = 0;\n    trackingData.overTargets = e.composedPath();\n    this.freeEvent(e);\n  }\n  /**\n   * Maps the upstream `pointerover` to downstream `pointerover` and `pointerenter` events, in that order.\n   *\n   * The tracking data for the specific pointer gets a new `overTarget`.\n   * @param from - The upstream `pointerover` event.\n   */\n  mapPointerOver(from) {\n    if (!(from instanceof FederatedPointerEvent)) {\n      warn(\"EventBoundary cannot map a non-pointer event as a pointer event\");\n      return;\n    }\n    const trackingData = this.trackingData(from.pointerId);\n    const e = this.createPointerEvent(from);\n    const isMouse = e.pointerType === \"mouse\" || e.pointerType === \"pen\";\n    this.dispatchEvent(e, \"pointerover\");\n    if (isMouse)\n      this.dispatchEvent(e, \"mouseover\");\n    if (e.pointerType === \"mouse\")\n      this.cursor = e.target?.cursor;\n    const enterEvent = this.clonePointerEvent(e, \"pointerenter\");\n    enterEvent.eventPhase = enterEvent.AT_TARGET;\n    while (enterEvent.target && enterEvent.target !== this.rootTarget.parent) {\n      enterEvent.currentTarget = enterEvent.target;\n      this.notifyTarget(enterEvent);\n      if (isMouse)\n        this.notifyTarget(enterEvent, \"mouseenter\");\n      enterEvent.target = enterEvent.target.parent;\n    }\n    trackingData.overTargets = e.composedPath();\n    this.freeEvent(e);\n    this.freeEvent(enterEvent);\n  }\n  /**\n   * Maps the upstream `pointerout` to downstream `pointerout`, `pointerleave` events, in that order.\n   *\n   * The tracking data for the specific pointer is cleared of a `overTarget`.\n   * @param from - The upstream `pointerout` event.\n   */\n  mapPointerOut(from) {\n    if (!(from instanceof FederatedPointerEvent)) {\n      warn(\"EventBoundary cannot map a non-pointer event as a pointer event\");\n      return;\n    }\n    const trackingData = this.trackingData(from.pointerId);\n    if (trackingData.overTargets) {\n      const isMouse = from.pointerType === \"mouse\" || from.pointerType === \"pen\";\n      const outTarget = this.findMountedTarget(trackingData.overTargets);\n      const outEvent = this.createPointerEvent(from, \"pointerout\", outTarget);\n      this.dispatchEvent(outEvent);\n      if (isMouse)\n        this.dispatchEvent(outEvent, \"mouseout\");\n      const leaveEvent = this.createPointerEvent(from, \"pointerleave\", outTarget);\n      leaveEvent.eventPhase = leaveEvent.AT_TARGET;\n      while (leaveEvent.target && leaveEvent.target !== this.rootTarget.parent) {\n        leaveEvent.currentTarget = leaveEvent.target;\n        this.notifyTarget(leaveEvent);\n        if (isMouse)\n          this.notifyTarget(leaveEvent, \"mouseleave\");\n        leaveEvent.target = leaveEvent.target.parent;\n      }\n      trackingData.overTargets = null;\n      this.freeEvent(outEvent);\n      this.freeEvent(leaveEvent);\n    }\n    this.cursor = null;\n  }\n  /**\n   * Maps the upstream `pointerup` event to downstream `pointerup`, `pointerupoutside`,\n   * and `click`/`rightclick`/`pointertap` events, in that order.\n   *\n   * The `pointerupoutside` event bubbles from the original `pointerdown` target to the most specific\n   * ancestor of the `pointerdown` and `pointerup` targets, which is also the `click` event's target. `touchend`,\n   * `rightup`, `mouseup`, `touchendoutside`, `rightupoutside`, `mouseupoutside`, and `tap` are fired as well for\n   * specific pointer types.\n   * @param from - The upstream `pointerup` event.\n   */\n  mapPointerUp(from) {\n    if (!(from instanceof FederatedPointerEvent)) {\n      warn(\"EventBoundary cannot map a non-pointer event as a pointer event\");\n      return;\n    }\n    const now = performance.now();\n    const e = this.createPointerEvent(from);\n    this.dispatchEvent(e, \"pointerup\");\n    if (e.pointerType === \"touch\") {\n      this.dispatchEvent(e, \"touchend\");\n    } else if (e.pointerType === \"mouse\" || e.pointerType === \"pen\") {\n      const isRightButton = e.button === 2;\n      this.dispatchEvent(e, isRightButton ? \"rightup\" : \"mouseup\");\n    }\n    const trackingData = this.trackingData(from.pointerId);\n    const pressTarget = this.findMountedTarget(trackingData.pressTargetsByButton[from.button]);\n    let clickTarget = pressTarget;\n    if (pressTarget && !e.composedPath().includes(pressTarget)) {\n      let currentTarget = pressTarget;\n      while (currentTarget && !e.composedPath().includes(currentTarget)) {\n        e.currentTarget = currentTarget;\n        this.notifyTarget(e, \"pointerupoutside\");\n        if (e.pointerType === \"touch\") {\n          this.notifyTarget(e, \"touchendoutside\");\n        } else if (e.pointerType === \"mouse\" || e.pointerType === \"pen\") {\n          const isRightButton = e.button === 2;\n          this.notifyTarget(e, isRightButton ? \"rightupoutside\" : \"mouseupoutside\");\n        }\n        currentTarget = currentTarget.parent;\n      }\n      delete trackingData.pressTargetsByButton[from.button];\n      clickTarget = currentTarget;\n    }\n    if (clickTarget) {\n      const clickEvent = this.clonePointerEvent(e, \"click\");\n      clickEvent.target = clickTarget;\n      clickEvent.path = null;\n      if (!trackingData.clicksByButton[from.button]) {\n        trackingData.clicksByButton[from.button] = {\n          clickCount: 0,\n          target: clickEvent.target,\n          timeStamp: now\n        };\n      }\n      const clickHistory = trackingData.clicksByButton[from.button];\n      if (clickHistory.target === clickEvent.target && now - clickHistory.timeStamp < 200) {\n        ++clickHistory.clickCount;\n      } else {\n        clickHistory.clickCount = 1;\n      }\n      clickHistory.target = clickEvent.target;\n      clickHistory.timeStamp = now;\n      clickEvent.detail = clickHistory.clickCount;\n      if (clickEvent.pointerType === \"mouse\") {\n        const isRightButton = clickEvent.button === 2;\n        this.dispatchEvent(clickEvent, isRightButton ? \"rightclick\" : \"click\");\n      } else if (clickEvent.pointerType === \"touch\") {\n        this.dispatchEvent(clickEvent, \"tap\");\n      }\n      this.dispatchEvent(clickEvent, \"pointertap\");\n      this.freeEvent(clickEvent);\n    }\n    this.freeEvent(e);\n  }\n  /**\n   * Maps the upstream `pointerupoutside` event to a downstream `pointerupoutside` event, bubbling from the original\n   * `pointerdown` target to `rootTarget`.\n   *\n   * (The most specific ancestor of the `pointerdown` event and the `pointerup` event must the\n   * `{@link EventBoundary}'s root because the `pointerup` event occurred outside of the boundary.)\n   *\n   * `touchendoutside`, `mouseupoutside`, and `rightupoutside` events are fired as well for specific pointer\n   * types. The tracking data for the specific pointer is cleared of a `pressTarget`.\n   * @param from - The upstream `pointerupoutside` event.\n   */\n  mapPointerUpOutside(from) {\n    if (!(from instanceof FederatedPointerEvent)) {\n      warn(\"EventBoundary cannot map a non-pointer event as a pointer event\");\n      return;\n    }\n    const trackingData = this.trackingData(from.pointerId);\n    const pressTarget = this.findMountedTarget(trackingData.pressTargetsByButton[from.button]);\n    const e = this.createPointerEvent(from);\n    if (pressTarget) {\n      let currentTarget = pressTarget;\n      while (currentTarget) {\n        e.currentTarget = currentTarget;\n        this.notifyTarget(e, \"pointerupoutside\");\n        if (e.pointerType === \"touch\") {\n          this.notifyTarget(e, \"touchendoutside\");\n        } else if (e.pointerType === \"mouse\" || e.pointerType === \"pen\") {\n          this.notifyTarget(e, e.button === 2 ? \"rightupoutside\" : \"mouseupoutside\");\n        }\n        currentTarget = currentTarget.parent;\n      }\n      delete trackingData.pressTargetsByButton[from.button];\n    }\n    this.freeEvent(e);\n  }\n  /**\n   * Maps the upstream `wheel` event to a downstream `wheel` event.\n   * @param from - The upstream `wheel` event.\n   */\n  mapWheel(from) {\n    if (!(from instanceof FederatedWheelEvent)) {\n      warn(\"EventBoundary cannot map a non-wheel event as a wheel event\");\n      return;\n    }\n    const wheelEvent = this.createWheelEvent(from);\n    this.dispatchEvent(wheelEvent);\n    this.freeEvent(wheelEvent);\n  }\n  /**\n   * Finds the most specific event-target in the given propagation path that is still mounted in the scene graph.\n   *\n   * This is used to find the correct `pointerup` and `pointerout` target in the case that the original `pointerdown`\n   * or `pointerover` target was unmounted from the scene graph.\n   * @param propagationPath - The propagation path was valid in the past.\n   * @returns - The most specific event-target still mounted at the same location in the scene graph.\n   */\n  findMountedTarget(propagationPath) {\n    if (!propagationPath) {\n      return null;\n    }\n    let currentTarget = propagationPath[0];\n    for (let i = 1; i < propagationPath.length; i++) {\n      if (propagationPath[i].parent === currentTarget) {\n        currentTarget = propagationPath[i];\n      } else {\n        break;\n      }\n    }\n    return currentTarget;\n  }\n  /**\n   * Creates an event whose {@code originalEvent} is {@code from}, with an optional `type` and `target` override.\n   *\n   * The event is allocated using {@link EventBoundary#allocateEvent this.allocateEvent}.\n   * @param from - The {@code originalEvent} for the returned event.\n   * @param [type=from.type] - The type of the returned event.\n   * @param target - The target of the returned event.\n   */\n  createPointerEvent(from, type, target) {\n    const event = this.allocateEvent(FederatedPointerEvent);\n    this.copyPointerData(from, event);\n    this.copyMouseData(from, event);\n    this.copyData(from, event);\n    event.nativeEvent = from.nativeEvent;\n    event.originalEvent = from;\n    event.target = target ?? this.hitTest(event.global.x, event.global.y) ?? this._hitElements[0];\n    if (typeof type === \"string\") {\n      event.type = type;\n    }\n    return event;\n  }\n  /**\n   * Creates a wheel event whose {@code originalEvent} is {@code from}.\n   *\n   * The event is allocated using {@link EventBoundary#allocateEvent this.allocateEvent}.\n   * @param from - The upstream wheel event.\n   */\n  createWheelEvent(from) {\n    const event = this.allocateEvent(FederatedWheelEvent);\n    this.copyWheelData(from, event);\n    this.copyMouseData(from, event);\n    this.copyData(from, event);\n    event.nativeEvent = from.nativeEvent;\n    event.originalEvent = from;\n    event.target = this.hitTest(event.global.x, event.global.y);\n    return event;\n  }\n  /**\n   * Clones the event {@code from}, with an optional {@code type} override.\n   *\n   * The event is allocated using {@link EventBoundary#allocateEvent this.allocateEvent}.\n   * @param from - The event to clone.\n   * @param [type=from.type] - The type of the returned event.\n   */\n  clonePointerEvent(from, type) {\n    const event = this.allocateEvent(FederatedPointerEvent);\n    event.nativeEvent = from.nativeEvent;\n    event.originalEvent = from.originalEvent;\n    this.copyPointerData(from, event);\n    this.copyMouseData(from, event);\n    this.copyData(from, event);\n    event.target = from.target;\n    event.path = from.composedPath().slice();\n    event.type = type ?? event.type;\n    return event;\n  }\n  /**\n   * Copies wheel {@link FederatedWheelEvent} data from {@code from} into {@code to}.\n   *\n   * The following properties are copied:\n   * + deltaMode\n   * + deltaX\n   * + deltaY\n   * + deltaZ\n   * @param from - The event to copy data from.\n   * @param to - The event to copy data into.\n   */\n  copyWheelData(from, to) {\n    to.deltaMode = from.deltaMode;\n    to.deltaX = from.deltaX;\n    to.deltaY = from.deltaY;\n    to.deltaZ = from.deltaZ;\n  }\n  /**\n   * Copies pointer {@link FederatedPointerEvent} data from {@code from} into {@code to}.\n   *\n   * The following properties are copied:\n   * + pointerId\n   * + width\n   * + height\n   * + isPrimary\n   * + pointerType\n   * + pressure\n   * + tangentialPressure\n   * + tiltX\n   * + tiltY\n   * @param from - The event to copy data from.\n   * @param to - The event to copy data into.\n   */\n  copyPointerData(from, to) {\n    if (!(from instanceof FederatedPointerEvent && to instanceof FederatedPointerEvent))\n      return;\n    to.pointerId = from.pointerId;\n    to.width = from.width;\n    to.height = from.height;\n    to.isPrimary = from.isPrimary;\n    to.pointerType = from.pointerType;\n    to.pressure = from.pressure;\n    to.tangentialPressure = from.tangentialPressure;\n    to.tiltX = from.tiltX;\n    to.tiltY = from.tiltY;\n    to.twist = from.twist;\n  }\n  /**\n   * Copies mouse {@link FederatedMouseEvent} data from {@code from} to {@code to}.\n   *\n   * The following properties are copied:\n   * + altKey\n   * + button\n   * + buttons\n   * + clientX\n   * + clientY\n   * + metaKey\n   * + movementX\n   * + movementY\n   * + pageX\n   * + pageY\n   * + x\n   * + y\n   * + screen\n   * + shiftKey\n   * + global\n   * @param from - The event to copy data from.\n   * @param to - The event to copy data into.\n   */\n  copyMouseData(from, to) {\n    if (!(from instanceof FederatedMouseEvent && to instanceof FederatedMouseEvent))\n      return;\n    to.altKey = from.altKey;\n    to.button = from.button;\n    to.buttons = from.buttons;\n    to.client.copyFrom(from.client);\n    to.ctrlKey = from.ctrlKey;\n    to.metaKey = from.metaKey;\n    to.movement.copyFrom(from.movement);\n    to.screen.copyFrom(from.screen);\n    to.shiftKey = from.shiftKey;\n    to.global.copyFrom(from.global);\n  }\n  /**\n   * Copies base {@link FederatedEvent} data from {@code from} into {@code to}.\n   *\n   * The following properties are copied:\n   * + isTrusted\n   * + srcElement\n   * + timeStamp\n   * + type\n   * @param from - The event to copy data from.\n   * @param to - The event to copy data into.\n   */\n  copyData(from, to) {\n    to.isTrusted = from.isTrusted;\n    to.srcElement = from.srcElement;\n    to.timeStamp = performance.now();\n    to.type = from.type;\n    to.detail = from.detail;\n    to.view = from.view;\n    to.which = from.which;\n    to.layer.copyFrom(from.layer);\n    to.page.copyFrom(from.page);\n  }\n  /**\n   * @param id - The pointer ID.\n   * @returns The tracking data stored for the given pointer. If no data exists, a blank\n   *  state will be created.\n   */\n  trackingData(id) {\n    if (!this.mappingState.trackingData[id]) {\n      this.mappingState.trackingData[id] = {\n        pressTargetsByButton: {},\n        clicksByButton: {},\n        overTarget: null\n      };\n    }\n    return this.mappingState.trackingData[id];\n  }\n  /**\n   * Allocate a specific type of event from {@link EventBoundary#eventPool this.eventPool}.\n   *\n   * This allocation is constructor-agnostic, as long as it only takes one argument - this event\n   * boundary.\n   * @param constructor - The event's constructor.\n   */\n  allocateEvent(constructor) {\n    if (!this.eventPool.has(constructor)) {\n      this.eventPool.set(constructor, []);\n    }\n    const event = this.eventPool.get(constructor).pop() || new constructor(this);\n    event.eventPhase = event.NONE;\n    event.currentTarget = null;\n    event.defaultPrevented = false;\n    event.path = null;\n    event.target = null;\n    return event;\n  }\n  /**\n   * Frees the event and puts it back into the event pool.\n   *\n   * It is illegal to reuse the event until it is allocated again, using `this.allocateEvent`.\n   *\n   * It is also advised that events not allocated from {@link EventBoundary#allocateEvent this.allocateEvent}\n   * not be freed. This is because of the possibility that the same event is freed twice, which can cause\n   * it to be allocated twice & result in overwriting.\n   * @param event - The event to be freed.\n   * @throws Error if the event is managed by another event boundary.\n   */\n  freeEvent(event) {\n    if (event.manager !== this)\n      throw new Error(\"It is illegal to free an event not managed by this EventBoundary!\");\n    const constructor = event.constructor;\n    if (!this.eventPool.has(constructor)) {\n      this.eventPool.set(constructor, []);\n    }\n    this.eventPool.get(constructor).push(event);\n  }\n  /**\n   * Similar to {@link EventEmitter.emit}, except it stops if the `propagationImmediatelyStopped` flag\n   * is set on the event.\n   * @param e - The event to call each listener with.\n   * @param type - The event key.\n   */\n  _notifyListeners(e, type) {\n    const listeners = e.currentTarget._events[type];\n    if (!listeners)\n      return;\n    if (\"fn\" in listeners) {\n      if (listeners.once)\n        e.currentTarget.removeListener(type, listeners.fn, void 0, true);\n      listeners.fn.call(listeners.context, e);\n    } else {\n      for (let i = 0, j = listeners.length; i < j && !e.propagationImmediatelyStopped; i++) {\n        if (listeners[i].once)\n          e.currentTarget.removeListener(type, listeners[i].fn, void 0, true);\n        listeners[i].fn.call(listeners[i].context, e);\n      }\n    }\n  }\n}\n\nexport { EventBoundary };\n//# sourceMappingURL=EventBoundary.mjs.map\n", "import { ExtensionType } from '../extensions/Extensions.mjs';\nimport { EventBoundary } from './EventBoundary.mjs';\nimport { EventsTicker } from './EventTicker.mjs';\nimport { FederatedPointerEvent } from './FederatedPointerEvent.mjs';\nimport { FederatedWheelEvent } from './FederatedWheelEvent.mjs';\n\n\"use strict\";\nconst MOUSE_POINTER_ID = 1;\nconst TOUCH_TO_POINTER = {\n  touchstart: \"pointerdown\",\n  touchend: \"pointerup\",\n  touchendoutside: \"pointerupoutside\",\n  touchmove: \"pointermove\",\n  touchcancel: \"pointercancel\"\n};\nconst _EventSystem = class _EventSystem {\n  /**\n   * @param {Renderer} renderer\n   */\n  constructor(renderer) {\n    /** Does the device support touch events https://www.w3.org/TR/touch-events/ */\n    this.supportsTouchEvents = \"ontouchstart\" in globalThis;\n    /** Does the device support pointer events https://www.w3.org/Submission/pointer-events/ */\n    this.supportsPointerEvents = !!globalThis.PointerEvent;\n    /**\n     * The DOM element to which the root event listeners are bound. This is automatically set to\n     * the renderer's {@link Renderer#view view}.\n     */\n    this.domElement = null;\n    /** The resolution used to convert between the DOM client space into world space. */\n    this.resolution = 1;\n    this.renderer = renderer;\n    this.rootBoundary = new EventBoundary(null);\n    EventsTicker.init(this);\n    this.autoPreventDefault = true;\n    this._eventsAdded = false;\n    this._rootPointerEvent = new FederatedPointerEvent(null);\n    this._rootWheelEvent = new FederatedWheelEvent(null);\n    this.cursorStyles = {\n      default: \"inherit\",\n      pointer: \"pointer\"\n    };\n    this.features = new Proxy({ ..._EventSystem.defaultEventFeatures }, {\n      set: (target, key, value) => {\n        if (key === \"globalMove\") {\n          this.rootBoundary.enableGlobalMoveEvents = value;\n        }\n        target[key] = value;\n        return true;\n      }\n    });\n    this._onPointerDown = this._onPointerDown.bind(this);\n    this._onPointerMove = this._onPointerMove.bind(this);\n    this._onPointerUp = this._onPointerUp.bind(this);\n    this._onPointerOverOut = this._onPointerOverOut.bind(this);\n    this.onWheel = this.onWheel.bind(this);\n  }\n  /**\n   * The default interaction mode for all display objects.\n   * @see Container.eventMode\n   * @type {EventMode}\n   * @readonly\n   * @since 7.2.0\n   */\n  static get defaultEventMode() {\n    return this._defaultEventMode;\n  }\n  /**\n   * Runner init called, view is available at this point.\n   * @ignore\n   */\n  init(options) {\n    const { canvas, resolution } = this.renderer;\n    this.setTargetElement(canvas);\n    this.resolution = resolution;\n    _EventSystem._defaultEventMode = options.eventMode ?? \"passive\";\n    Object.assign(this.features, options.eventFeatures ?? {});\n    this.rootBoundary.enableGlobalMoveEvents = this.features.globalMove;\n  }\n  /**\n   * Handle changing resolution.\n   * @ignore\n   */\n  resolutionChange(resolution) {\n    this.resolution = resolution;\n  }\n  /** Destroys all event listeners and detaches the renderer. */\n  destroy() {\n    this.setTargetElement(null);\n    this.renderer = null;\n    this._currentCursor = null;\n  }\n  /**\n   * Sets the current cursor mode, handling any callbacks or CSS style changes.\n   * @param mode - cursor mode, a key from the cursorStyles dictionary\n   */\n  setCursor(mode) {\n    mode || (mode = \"default\");\n    let applyStyles = true;\n    if (globalThis.OffscreenCanvas && this.domElement instanceof OffscreenCanvas) {\n      applyStyles = false;\n    }\n    if (this._currentCursor === mode) {\n      return;\n    }\n    this._currentCursor = mode;\n    const style = this.cursorStyles[mode];\n    if (style) {\n      switch (typeof style) {\n        case \"string\":\n          if (applyStyles) {\n            this.domElement.style.cursor = style;\n          }\n          break;\n        case \"function\":\n          style(mode);\n          break;\n        case \"object\":\n          if (applyStyles) {\n            Object.assign(this.domElement.style, style);\n          }\n          break;\n      }\n    } else if (applyStyles && typeof mode === \"string\" && !Object.prototype.hasOwnProperty.call(this.cursorStyles, mode)) {\n      this.domElement.style.cursor = mode;\n    }\n  }\n  /**\n   * The global pointer event.\n   * Useful for getting the pointer position without listening to events.\n   * @since 7.2.0\n   */\n  get pointer() {\n    return this._rootPointerEvent;\n  }\n  /**\n   * Event handler for pointer down events on {@link EventSystem#domElement this.domElement}.\n   * @param nativeEvent - The native mouse/pointer/touch event.\n   */\n  _onPointerDown(nativeEvent) {\n    if (!this.features.click)\n      return;\n    this.rootBoundary.rootTarget = this.renderer.lastObjectRendered;\n    const events = this._normalizeToPointerData(nativeEvent);\n    if (this.autoPreventDefault && events[0].isNormalized) {\n      const cancelable = nativeEvent.cancelable || !(\"cancelable\" in nativeEvent);\n      if (cancelable) {\n        nativeEvent.preventDefault();\n      }\n    }\n    for (let i = 0, j = events.length; i < j; i++) {\n      const nativeEvent2 = events[i];\n      const federatedEvent = this._bootstrapEvent(this._rootPointerEvent, nativeEvent2);\n      this.rootBoundary.mapEvent(federatedEvent);\n    }\n    this.setCursor(this.rootBoundary.cursor);\n  }\n  /**\n   * Event handler for pointer move events on on {@link EventSystem#domElement this.domElement}.\n   * @param nativeEvent - The native mouse/pointer/touch events.\n   */\n  _onPointerMove(nativeEvent) {\n    if (!this.features.move)\n      return;\n    this.rootBoundary.rootTarget = this.renderer.lastObjectRendered;\n    EventsTicker.pointerMoved();\n    const normalizedEvents = this._normalizeToPointerData(nativeEvent);\n    for (let i = 0, j = normalizedEvents.length; i < j; i++) {\n      const event = this._bootstrapEvent(this._rootPointerEvent, normalizedEvents[i]);\n      this.rootBoundary.mapEvent(event);\n    }\n    this.setCursor(this.rootBoundary.cursor);\n  }\n  /**\n   * Event handler for pointer up events on {@link EventSystem#domElement this.domElement}.\n   * @param nativeEvent - The native mouse/pointer/touch event.\n   */\n  _onPointerUp(nativeEvent) {\n    if (!this.features.click)\n      return;\n    this.rootBoundary.rootTarget = this.renderer.lastObjectRendered;\n    let target = nativeEvent.target;\n    if (nativeEvent.composedPath && nativeEvent.composedPath().length > 0) {\n      target = nativeEvent.composedPath()[0];\n    }\n    const outside = target !== this.domElement ? \"outside\" : \"\";\n    const normalizedEvents = this._normalizeToPointerData(nativeEvent);\n    for (let i = 0, j = normalizedEvents.length; i < j; i++) {\n      const event = this._bootstrapEvent(this._rootPointerEvent, normalizedEvents[i]);\n      event.type += outside;\n      this.rootBoundary.mapEvent(event);\n    }\n    this.setCursor(this.rootBoundary.cursor);\n  }\n  /**\n   * Event handler for pointer over & out events on {@link EventSystem#domElement this.domElement}.\n   * @param nativeEvent - The native mouse/pointer/touch event.\n   */\n  _onPointerOverOut(nativeEvent) {\n    if (!this.features.click)\n      return;\n    this.rootBoundary.rootTarget = this.renderer.lastObjectRendered;\n    const normalizedEvents = this._normalizeToPointerData(nativeEvent);\n    for (let i = 0, j = normalizedEvents.length; i < j; i++) {\n      const event = this._bootstrapEvent(this._rootPointerEvent, normalizedEvents[i]);\n      this.rootBoundary.mapEvent(event);\n    }\n    this.setCursor(this.rootBoundary.cursor);\n  }\n  /**\n   * Passive handler for `wheel` events on {@link EventSystem.domElement this.domElement}.\n   * @param nativeEvent - The native wheel event.\n   */\n  onWheel(nativeEvent) {\n    if (!this.features.wheel)\n      return;\n    const wheelEvent = this.normalizeWheelEvent(nativeEvent);\n    this.rootBoundary.rootTarget = this.renderer.lastObjectRendered;\n    this.rootBoundary.mapEvent(wheelEvent);\n  }\n  /**\n   * Sets the {@link EventSystem#domElement domElement} and binds event listeners.\n   *\n   * To deregister the current DOM element without setting a new one, pass {@code null}.\n   * @param element - The new DOM element.\n   */\n  setTargetElement(element) {\n    this._removeEvents();\n    this.domElement = element;\n    EventsTicker.domElement = element;\n    this._addEvents();\n  }\n  /** Register event listeners on {@link Renderer#domElement this.domElement}. */\n  _addEvents() {\n    if (this._eventsAdded || !this.domElement) {\n      return;\n    }\n    EventsTicker.addTickerListener();\n    const style = this.domElement.style;\n    if (style) {\n      if (globalThis.navigator.msPointerEnabled) {\n        style.msContentZooming = \"none\";\n        style.msTouchAction = \"none\";\n      } else if (this.supportsPointerEvents) {\n        style.touchAction = \"none\";\n      }\n    }\n    if (this.supportsPointerEvents) {\n      globalThis.document.addEventListener(\"pointermove\", this._onPointerMove, true);\n      this.domElement.addEventListener(\"pointerdown\", this._onPointerDown, true);\n      this.domElement.addEventListener(\"pointerleave\", this._onPointerOverOut, true);\n      this.domElement.addEventListener(\"pointerover\", this._onPointerOverOut, true);\n      globalThis.addEventListener(\"pointerup\", this._onPointerUp, true);\n    } else {\n      globalThis.document.addEventListener(\"mousemove\", this._onPointerMove, true);\n      this.domElement.addEventListener(\"mousedown\", this._onPointerDown, true);\n      this.domElement.addEventListener(\"mouseout\", this._onPointerOverOut, true);\n      this.domElement.addEventListener(\"mouseover\", this._onPointerOverOut, true);\n      globalThis.addEventListener(\"mouseup\", this._onPointerUp, true);\n      if (this.supportsTouchEvents) {\n        this.domElement.addEventListener(\"touchstart\", this._onPointerDown, true);\n        this.domElement.addEventListener(\"touchend\", this._onPointerUp, true);\n        this.domElement.addEventListener(\"touchmove\", this._onPointerMove, true);\n      }\n    }\n    this.domElement.addEventListener(\"wheel\", this.onWheel, {\n      passive: true,\n      capture: true\n    });\n    this._eventsAdded = true;\n  }\n  /** Unregister event listeners on {@link EventSystem#domElement this.domElement}. */\n  _removeEvents() {\n    if (!this._eventsAdded || !this.domElement) {\n      return;\n    }\n    EventsTicker.removeTickerListener();\n    const style = this.domElement.style;\n    if (style) {\n      if (globalThis.navigator.msPointerEnabled) {\n        style.msContentZooming = \"\";\n        style.msTouchAction = \"\";\n      } else if (this.supportsPointerEvents) {\n        style.touchAction = \"\";\n      }\n    }\n    if (this.supportsPointerEvents) {\n      globalThis.document.removeEventListener(\"pointermove\", this._onPointerMove, true);\n      this.domElement.removeEventListener(\"pointerdown\", this._onPointerDown, true);\n      this.domElement.removeEventListener(\"pointerleave\", this._onPointerOverOut, true);\n      this.domElement.removeEventListener(\"pointerover\", this._onPointerOverOut, true);\n      globalThis.removeEventListener(\"pointerup\", this._onPointerUp, true);\n    } else {\n      globalThis.document.removeEventListener(\"mousemove\", this._onPointerMove, true);\n      this.domElement.removeEventListener(\"mousedown\", this._onPointerDown, true);\n      this.domElement.removeEventListener(\"mouseout\", this._onPointerOverOut, true);\n      this.domElement.removeEventListener(\"mouseover\", this._onPointerOverOut, true);\n      globalThis.removeEventListener(\"mouseup\", this._onPointerUp, true);\n      if (this.supportsTouchEvents) {\n        this.domElement.removeEventListener(\"touchstart\", this._onPointerDown, true);\n        this.domElement.removeEventListener(\"touchend\", this._onPointerUp, true);\n        this.domElement.removeEventListener(\"touchmove\", this._onPointerMove, true);\n      }\n    }\n    this.domElement.removeEventListener(\"wheel\", this.onWheel, true);\n    this.domElement = null;\n    this._eventsAdded = false;\n  }\n  /**\n   * Maps x and y coords from a DOM object and maps them correctly to the PixiJS view. The\n   * resulting value is stored in the point. This takes into account the fact that the DOM\n   * element could be scaled and positioned anywhere on the screen.\n   * @param  {PointData} point - the point that the result will be stored in\n   * @param  {number} x - the x coord of the position to map\n   * @param  {number} y - the y coord of the position to map\n   */\n  mapPositionToPoint(point, x, y) {\n    const rect = this.domElement.isConnected ? this.domElement.getBoundingClientRect() : {\n      x: 0,\n      y: 0,\n      width: this.domElement.width,\n      height: this.domElement.height,\n      left: 0,\n      top: 0\n    };\n    const resolutionMultiplier = 1 / this.resolution;\n    point.x = (x - rect.left) * (this.domElement.width / rect.width) * resolutionMultiplier;\n    point.y = (y - rect.top) * (this.domElement.height / rect.height) * resolutionMultiplier;\n  }\n  /**\n   * Ensures that the original event object contains all data that a regular pointer event would have\n   * @param event - The original event data from a touch or mouse event\n   * @returns An array containing a single normalized pointer event, in the case of a pointer\n   *  or mouse event, or a multiple normalized pointer events if there are multiple changed touches\n   */\n  _normalizeToPointerData(event) {\n    const normalizedEvents = [];\n    if (this.supportsTouchEvents && event instanceof TouchEvent) {\n      for (let i = 0, li = event.changedTouches.length; i < li; i++) {\n        const touch = event.changedTouches[i];\n        if (typeof touch.button === \"undefined\")\n          touch.button = 0;\n        if (typeof touch.buttons === \"undefined\")\n          touch.buttons = 1;\n        if (typeof touch.isPrimary === \"undefined\") {\n          touch.isPrimary = event.touches.length === 1 && event.type === \"touchstart\";\n        }\n        if (typeof touch.width === \"undefined\")\n          touch.width = touch.radiusX || 1;\n        if (typeof touch.height === \"undefined\")\n          touch.height = touch.radiusY || 1;\n        if (typeof touch.tiltX === \"undefined\")\n          touch.tiltX = 0;\n        if (typeof touch.tiltY === \"undefined\")\n          touch.tiltY = 0;\n        if (typeof touch.pointerType === \"undefined\")\n          touch.pointerType = \"touch\";\n        if (typeof touch.pointerId === \"undefined\")\n          touch.pointerId = touch.identifier || 0;\n        if (typeof touch.pressure === \"undefined\")\n          touch.pressure = touch.force || 0.5;\n        if (typeof touch.twist === \"undefined\")\n          touch.twist = 0;\n        if (typeof touch.tangentialPressure === \"undefined\")\n          touch.tangentialPressure = 0;\n        if (typeof touch.layerX === \"undefined\")\n          touch.layerX = touch.offsetX = touch.clientX;\n        if (typeof touch.layerY === \"undefined\")\n          touch.layerY = touch.offsetY = touch.clientY;\n        touch.isNormalized = true;\n        touch.type = event.type;\n        normalizedEvents.push(touch);\n      }\n    } else if (!globalThis.MouseEvent || event instanceof MouseEvent && (!this.supportsPointerEvents || !(event instanceof globalThis.PointerEvent))) {\n      const tempEvent = event;\n      if (typeof tempEvent.isPrimary === \"undefined\")\n        tempEvent.isPrimary = true;\n      if (typeof tempEvent.width === \"undefined\")\n        tempEvent.width = 1;\n      if (typeof tempEvent.height === \"undefined\")\n        tempEvent.height = 1;\n      if (typeof tempEvent.tiltX === \"undefined\")\n        tempEvent.tiltX = 0;\n      if (typeof tempEvent.tiltY === \"undefined\")\n        tempEvent.tiltY = 0;\n      if (typeof tempEvent.pointerType === \"undefined\")\n        tempEvent.pointerType = \"mouse\";\n      if (typeof tempEvent.pointerId === \"undefined\")\n        tempEvent.pointerId = MOUSE_POINTER_ID;\n      if (typeof tempEvent.pressure === \"undefined\")\n        tempEvent.pressure = 0.5;\n      if (typeof tempEvent.twist === \"undefined\")\n        tempEvent.twist = 0;\n      if (typeof tempEvent.tangentialPressure === \"undefined\")\n        tempEvent.tangentialPressure = 0;\n      tempEvent.isNormalized = true;\n      normalizedEvents.push(tempEvent);\n    } else {\n      normalizedEvents.push(event);\n    }\n    return normalizedEvents;\n  }\n  /**\n   * Normalizes the native {@link https://w3c.github.io/uievents/#interface-wheelevent WheelEvent}.\n   *\n   * The returned {@link FederatedWheelEvent} is a shared instance. It will not persist across\n   * multiple native wheel events.\n   * @param nativeEvent - The native wheel event that occurred on the canvas.\n   * @returns A federated wheel event.\n   */\n  normalizeWheelEvent(nativeEvent) {\n    const event = this._rootWheelEvent;\n    this._transferMouseData(event, nativeEvent);\n    event.deltaX = nativeEvent.deltaX;\n    event.deltaY = nativeEvent.deltaY;\n    event.deltaZ = nativeEvent.deltaZ;\n    event.deltaMode = nativeEvent.deltaMode;\n    this.mapPositionToPoint(event.screen, nativeEvent.clientX, nativeEvent.clientY);\n    event.global.copyFrom(event.screen);\n    event.offset.copyFrom(event.screen);\n    event.nativeEvent = nativeEvent;\n    event.type = nativeEvent.type;\n    return event;\n  }\n  /**\n   * Normalizes the `nativeEvent` into a federateed {@link FederatedPointerEvent}.\n   * @param event\n   * @param nativeEvent\n   */\n  _bootstrapEvent(event, nativeEvent) {\n    event.originalEvent = null;\n    event.nativeEvent = nativeEvent;\n    event.pointerId = nativeEvent.pointerId;\n    event.width = nativeEvent.width;\n    event.height = nativeEvent.height;\n    event.isPrimary = nativeEvent.isPrimary;\n    event.pointerType = nativeEvent.pointerType;\n    event.pressure = nativeEvent.pressure;\n    event.tangentialPressure = nativeEvent.tangentialPressure;\n    event.tiltX = nativeEvent.tiltX;\n    event.tiltY = nativeEvent.tiltY;\n    event.twist = nativeEvent.twist;\n    this._transferMouseData(event, nativeEvent);\n    this.mapPositionToPoint(event.screen, nativeEvent.clientX, nativeEvent.clientY);\n    event.global.copyFrom(event.screen);\n    event.offset.copyFrom(event.screen);\n    event.isTrusted = nativeEvent.isTrusted;\n    if (event.type === \"pointerleave\") {\n      event.type = \"pointerout\";\n    }\n    if (event.type.startsWith(\"mouse\")) {\n      event.type = event.type.replace(\"mouse\", \"pointer\");\n    }\n    if (event.type.startsWith(\"touch\")) {\n      event.type = TOUCH_TO_POINTER[event.type] || event.type;\n    }\n    return event;\n  }\n  /**\n   * Transfers base & mouse event data from the {@code nativeEvent} to the federated event.\n   * @param event\n   * @param nativeEvent\n   */\n  _transferMouseData(event, nativeEvent) {\n    event.isTrusted = nativeEvent.isTrusted;\n    event.srcElement = nativeEvent.srcElement;\n    event.timeStamp = performance.now();\n    event.type = nativeEvent.type;\n    event.altKey = nativeEvent.altKey;\n    event.button = nativeEvent.button;\n    event.buttons = nativeEvent.buttons;\n    event.client.x = nativeEvent.clientX;\n    event.client.y = nativeEvent.clientY;\n    event.ctrlKey = nativeEvent.ctrlKey;\n    event.metaKey = nativeEvent.metaKey;\n    event.movement.x = nativeEvent.movementX;\n    event.movement.y = nativeEvent.movementY;\n    event.page.x = nativeEvent.pageX;\n    event.page.y = nativeEvent.pageY;\n    event.relatedTarget = null;\n    event.shiftKey = nativeEvent.shiftKey;\n  }\n};\n/** @ignore */\n_EventSystem.extension = {\n  name: \"events\",\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.CanvasSystem,\n    ExtensionType.WebGPUSystem\n  ],\n  priority: -1\n};\n/**\n * The event features that are enabled by the EventSystem\n * (included in the **pixi.js** and **pixi.js-legacy** bundle), otherwise it will be ignored.\n * @since 7.2.0\n */\n_EventSystem.defaultEventFeatures = {\n  /** Enables pointer events associated with pointer movement. */\n  move: true,\n  /** Enables global pointer move events. */\n  globalMove: true,\n  /** Enables pointer events associated with clicking. */\n  click: true,\n  /** Enables wheel events. */\n  wheel: true\n};\nlet EventSystem = _EventSystem;\n\nexport { EventSystem };\n//# sourceMappingURL=EventSystem.mjs.map\n", "import { EventSystem } from './EventSystem.mjs';\nimport { FederatedEvent } from './FederatedEvent.mjs';\n\n\"use strict\";\nconst FederatedContainer = {\n  /**\n   * Property-based event handler for the `click` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onclick = (event) => {\n   *  //some function here that happens on click\n   * }\n   */\n  onclick: null,\n  /**\n   * Property-based event handler for the `mousedown` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onmousedown = (event) => {\n   *  //some function here that happens on mousedown\n   * }\n   */\n  onmousedown: null,\n  /**\n   * Property-based event handler for the `mouseenter` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onmouseenter = (event) => {\n   *  //some function here that happens on mouseenter\n   * }\n   */\n  onmouseenter: null,\n  /**\n   * Property-based event handler for the `mouseleave` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onmouseleave = (event) => {\n   *  //some function here that happens on mouseleave\n   * }\n   */\n  onmouseleave: null,\n  /**\n   * Property-based event handler for the `mousemove` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onmousemove = (event) => {\n   *  //some function here that happens on mousemove\n   * }\n   */\n  onmousemove: null,\n  /**\n   * Property-based event handler for the `globalmousemove` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onglobalmousemove = (event) => {\n   *  //some function here that happens on globalmousemove\n   * }\n   */\n  onglobalmousemove: null,\n  /**\n   * Property-based event handler for the `mouseout` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onmouseout = (event) => {\n   *  //some function here that happens on mouseout\n   * }\n   */\n  onmouseout: null,\n  /**\n   * Property-based event handler for the `mouseover` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onmouseover = (event) => {\n   *  //some function here that happens on mouseover\n   * }\n   */\n  onmouseover: null,\n  /**\n   * Property-based event handler for the `mouseup` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onmouseup = (event) => {\n   *  //some function here that happens on mouseup\n   * }\n   */\n  onmouseup: null,\n  /**\n   * Property-based event handler for the `mouseupoutside` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onmouseupoutside = (event) => {\n   *  //some function here that happens on mouseupoutside\n   * }\n   */\n  onmouseupoutside: null,\n  /**\n   * Property-based event handler for the `pointercancel` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onpointercancel = (event) => {\n   *  //some function here that happens on pointercancel\n   * }\n   */\n  onpointercancel: null,\n  /**\n   * Property-based event handler for the `pointerdown` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onpointerdown = (event) => {\n   *  //some function here that happens on pointerdown\n   * }\n   */\n  onpointerdown: null,\n  /**\n   * Property-based event handler for the `pointerenter` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onpointerenter = (event) => {\n   *  //some function here that happens on pointerenter\n   * }\n   */\n  onpointerenter: null,\n  /**\n   * Property-based event handler for the `pointerleave` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onpointerleave = (event) => {\n   *  //some function here that happens on pointerleave\n   * }\n   */\n  onpointerleave: null,\n  /**\n   * Property-based event handler for the `pointermove` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onpointermove = (event) => {\n   *  //some function here that happens on pointermove\n   * }\n   */\n  onpointermove: null,\n  /**\n   * Property-based event handler for the `globalpointermove` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onglobalpointermove = (event) => {\n   *  //some function here that happens on globalpointermove\n   * }\n   */\n  onglobalpointermove: null,\n  /**\n   * Property-based event handler for the `pointerout` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onpointerout = (event) => {\n   *  //some function here that happens on pointerout\n   * }\n   */\n  onpointerout: null,\n  /**\n   * Property-based event handler for the `pointerover` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onpointerover = (event) => {\n   *  //some function here that happens on pointerover\n   * }\n   */\n  onpointerover: null,\n  /**\n   * Property-based event handler for the `pointertap` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onpointertap = (event) => {\n   *  //some function here that happens on pointertap\n   * }\n   */\n  onpointertap: null,\n  /**\n   * Property-based event handler for the `pointerup` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onpointerup = (event) => {\n   *  //some function here that happens on pointerup\n   * }\n   */\n  onpointerup: null,\n  /**\n   * Property-based event handler for the `pointerupoutside` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onpointerupoutside = (event) => {\n   *  //some function here that happens on pointerupoutside\n   * }\n   */\n  onpointerupoutside: null,\n  /**\n   * Property-based event handler for the `rightclick` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onrightclick = (event) => {\n   *  //some function here that happens on rightclick\n   * }\n   */\n  onrightclick: null,\n  /**\n   * Property-based event handler for the `rightdown` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onrightdown = (event) => {\n   *  //some function here that happens on rightdown\n   * }\n   */\n  onrightdown: null,\n  /**\n   * Property-based event handler for the `rightup` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onrightup = (event) => {\n   *  //some function here that happens on rightup\n   * }\n   */\n  onrightup: null,\n  /**\n   * Property-based event handler for the `rightupoutside` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onrightupoutside = (event) => {\n   *  //some function here that happens on rightupoutside\n   * }\n   */\n  onrightupoutside: null,\n  /**\n   * Property-based event handler for the `tap` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.ontap = (event) => {\n   *  //some function here that happens on tap\n   * }\n   */\n  ontap: null,\n  /**\n   * Property-based event handler for the `touchcancel` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.ontouchcancel = (event) => {\n   *  //some function here that happens on touchcancel\n   * }\n   */\n  ontouchcancel: null,\n  /**\n   * Property-based event handler for the `touchend` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.ontouchend = (event) => {\n   *  //some function here that happens on touchend\n   * }\n   */\n  ontouchend: null,\n  /**\n   * Property-based event handler for the `touchendoutside` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.ontouchendoutside = (event) => {\n   *  //some function here that happens on touchendoutside\n   * }\n   */\n  ontouchendoutside: null,\n  /**\n   * Property-based event handler for the `touchmove` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.ontouchmove = (event) => {\n   *  //some function here that happens on touchmove\n   * }\n   */\n  ontouchmove: null,\n  /**\n   * Property-based event handler for the `globaltouchmove` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onglobaltouchmove = (event) => {\n   *  //some function here that happens on globaltouchmove\n   * }\n   */\n  onglobaltouchmove: null,\n  /**\n   * Property-based event handler for the `touchstart` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.ontouchstart = (event) => {\n   *  //some function here that happens on touchstart\n   * }\n   */\n  ontouchstart: null,\n  /**\n   * Property-based event handler for the `wheel` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onwheel = (event) => {\n   *  //some function here that happens on wheel\n   * }\n   */\n  onwheel: null,\n  /**\n   * Enable interaction events for the Container. Touch, pointer and mouse\n   * @memberof scene.Container#\n   */\n  get interactive() {\n    return this.eventMode === \"dynamic\" || this.eventMode === \"static\";\n  },\n  set interactive(value) {\n    this.eventMode = value ? \"static\" : \"passive\";\n  },\n  /**\n   * @ignore\n   */\n  _internalEventMode: void 0,\n  /**\n   * Enable interaction events for the Container. Touch, pointer and mouse.\n   * There are 5 types of interaction settings:\n   * - `'none'`: Ignores all interaction events, even on its children.\n   * - `'passive'`: **(default)** Does not emit events and ignores all hit testing on itself and non-interactive children.\n   * Interactive children will still emit events.\n   * - `'auto'`: Does not emit events but is hit tested if parent is interactive. Same as `interactive = false` in v7\n   * - `'static'`: Emit events and is hit tested. Same as `interaction = true` in v7\n   * - `'dynamic'`: Emits events and is hit tested but will also receive mock interaction events fired from a ticker to\n   * allow for interaction when the mouse isn't moving\n   * @example\n   * import { Sprite } from 'pixi.js';\n   *\n   * const sprite = new Sprite(texture);\n   * sprite.eventMode = 'static';\n   * sprite.on('tap', (event) => {\n   *     // Handle event\n   * });\n   * @memberof scene.Container#\n   * @since 7.2.0\n   */\n  get eventMode() {\n    return this._internalEventMode ?? EventSystem.defaultEventMode;\n  },\n  set eventMode(value) {\n    this._internalEventMode = value;\n  },\n  /**\n   * Determines if the container is interactive or not\n   * @returns {boolean} Whether the container is interactive or not\n   * @memberof scene.Container#\n   * @since 7.2.0\n   * @example\n   * import { Sprite } from 'pixi.js';\n   *\n   * const sprite = new Sprite(texture);\n   * sprite.eventMode = 'static';\n   * sprite.isInteractive(); // true\n   *\n   * sprite.eventMode = 'dynamic';\n   * sprite.isInteractive(); // true\n   *\n   * sprite.eventMode = 'none';\n   * sprite.isInteractive(); // false\n   *\n   * sprite.eventMode = 'passive';\n   * sprite.isInteractive(); // false\n   *\n   * sprite.eventMode = 'auto';\n   * sprite.isInteractive(); // false\n   */\n  isInteractive() {\n    return this.eventMode === \"static\" || this.eventMode === \"dynamic\";\n  },\n  /**\n   * Determines if the children to the container can be clicked/touched\n   * Setting this to false allows PixiJS to bypass a recursive `hitTest` function\n   * @memberof scene.Container#\n   */\n  interactiveChildren: true,\n  /**\n   * Interaction shape. Children will be hit first, then this shape will be checked.\n   * Setting this will cause this shape to be checked in hit tests rather than the container's bounds.\n   * @example\n   * import { Rectangle, Sprite } from 'pixi.js';\n   *\n   * const sprite = new Sprite(texture);\n   * sprite.interactive = true;\n   * sprite.hitArea = new Rectangle(0, 0, 100, 100);\n   * @member {IHitArea}\n   * @memberof scene.Container#\n   */\n  hitArea: null,\n  /**\n   * Unlike `on` or `addListener` which are methods from EventEmitter, `addEventListener`\n   * seeks to be compatible with the DOM's `addEventListener` with support for options.\n   * @memberof scene.Container\n   * @param type - The type of event to listen to.\n   * @param listener - The listener callback or object.\n   * @param options - Listener options, used for capture phase.\n   * @example\n   * // Tell the user whether they did a single, double, triple, or nth click.\n   * button.addEventListener('click', {\n   *     handleEvent(e): {\n   *         let prefix;\n   *\n   *         switch (e.detail) {\n   *             case 1: prefix = 'single'; break;\n   *             case 2: prefix = 'double'; break;\n   *             case 3: prefix = 'triple'; break;\n   *             default: prefix = e.detail + 'th'; break;\n   *         }\n   *\n   *         console.log('That was a ' + prefix + 'click');\n   *     }\n   * });\n   *\n   * // But skip the first click!\n   * button.parent.addEventListener('click', function blockClickOnce(e) {\n   *     e.stopImmediatePropagation();\n   *     button.parent.removeEventListener('click', blockClickOnce, true);\n   * }, {\n   *     capture: true,\n   * });\n   */\n  addEventListener(type, listener, options) {\n    const capture = typeof options === \"boolean\" && options || typeof options === \"object\" && options.capture;\n    const signal = typeof options === \"object\" ? options.signal : void 0;\n    const once = typeof options === \"object\" ? options.once === true : false;\n    const context = typeof listener === \"function\" ? void 0 : listener;\n    type = capture ? `${type}capture` : type;\n    const listenerFn = typeof listener === \"function\" ? listener : listener.handleEvent;\n    const emitter = this;\n    if (signal) {\n      signal.addEventListener(\"abort\", () => {\n        emitter.off(type, listenerFn, context);\n      });\n    }\n    if (once) {\n      emitter.once(type, listenerFn, context);\n    } else {\n      emitter.on(type, listenerFn, context);\n    }\n  },\n  /**\n   * Unlike `off` or `removeListener` which are methods from EventEmitter, `removeEventListener`\n   * seeks to be compatible with the DOM's `removeEventListener` with support for options.\n   * @memberof scene.Container\n   * @param type - The type of event the listener is bound to.\n   * @param listener - The listener callback or object.\n   * @param options - The original listener options. This is required to deregister a capture phase listener.\n   */\n  removeEventListener(type, listener, options) {\n    const capture = typeof options === \"boolean\" && options || typeof options === \"object\" && options.capture;\n    const context = typeof listener === \"function\" ? void 0 : listener;\n    type = capture ? `${type}capture` : type;\n    listener = typeof listener === \"function\" ? listener : listener.handleEvent;\n    this.off(type, listener, context);\n  },\n  /**\n   * Dispatch the event on this {@link Container} using the event's {@link EventBoundary}.\n   *\n   * The target of the event is set to `this` and the `defaultPrevented` flag is cleared before dispatch.\n   * @memberof scene.Container\n   * @param e - The event to dispatch.\n   * @returns Whether the {@link FederatedEvent.preventDefault preventDefault}() method was not invoked.\n   * @example\n   * // Reuse a click event!\n   * button.dispatchEvent(clickEvent);\n   */\n  dispatchEvent(e) {\n    if (!(e instanceof FederatedEvent)) {\n      throw new Error(\"Container cannot propagate events outside of the Federated Events API\");\n    }\n    e.defaultPrevented = false;\n    e.path = null;\n    e.target = this;\n    e.manager.dispatchEvent(e);\n    return !e.defaultPrevented;\n  }\n};\n\nexport { FederatedContainer };\n//# sourceMappingURL=FederatedEventTarget.mjs.map\n", "import { extensions } from '../extensions/Extensions.mjs';\nimport { Container } from '../scene/container/Container.mjs';\nimport { AccessibilitySystem } from './AccessibilitySystem.mjs';\nimport { accessibilityTarget } from './accessibilityTarget.mjs';\n\n\"use strict\";\nextensions.add(AccessibilitySystem);\nContainer.mixin(accessibilityTarget);\n//# sourceMappingURL=init.mjs.map\n", "import { extensions } from '../extensions/Extensions.mjs';\nimport { Container } from '../scene/container/Container.mjs';\nimport { EventSystem } from './EventSystem.mjs';\nimport { FederatedContainer } from './FederatedEventTarget.mjs';\n\n\"use strict\";\nextensions.add(EventSystem);\nContainer.mixin(FederatedContainer);\n//# sourceMappingURL=init.mjs.map\n"], "names": ["FederatedEvent", "manager", "Point", "_type", "_bubbles", "_cancelable", "_typeArg", "_bubblesArg", "_cancelableArg", "_viewArg", "_detailArg", "appleIphone", "appleIpod", "appleTablet", "appleUniversal", "androidPhone", "androidTablet", "amazonPhone", "amazonTablet", "windowsPhone", "windowsTablet", "otherBlackBerry", "otherBlackBerry10", "otherOpera", "otherChrome", "otherFirefox", "isAppleTabletOnIos13", "navigator", "createMatch", "userAgent", "regex", "isMobile", "param", "nav", "tmp", "match", "result", "isMobileCall", "isMobileJs", "KEY_CODE_TAB", "DIV_TOUCH_SIZE", "DIV_TOUCH_POS_X", "DIV_TOUCH_POS_Y", "DIV_TOUCH_ZINDEX", "DIV_HOOK_SIZE", "DIV_HOOK_POS_X", "DIV_HOOK_POS_Y", "DIV_HOOK_ZINDEX", "_AccessibilitySystem", "renderer", "_mobileInfo", "hookDiv", "canvas", "observer", "child", "div", "container", "children", "options", "mergedOptions", "now", "activeIds", "removeItems", "x", "y", "viewWidth", "viewHeight", "hitArea", "wt", "sx", "sy", "type", "target", "boundary", "event", "type2", "enabled", "ExtensionType", "AccessibilitySystem", "accessibilityTarget", "EventsTickerClass", "events", "paused", "Ticker", "UPDATE_PRIORITY", "rootPointerEvent", "ticker", "EventsTicker", "FederatedMouseEvent", "point", "globalPos", "key", "_canBubbleArg", "_screenXArg", "_screenYArg", "_clientXArg", "_clientYArg", "_ctrlKeyArg", "_altKeyArg", "_shiftKeyArg", "_metaKeyArg", "_buttonArg", "_relatedTargetArg", "FederatedPointerEvent", "FederatedWheelEvent", "PROPAGATION_LIMIT", "tempHitLocation", "tempLocalMapping", "EventBoundary", "rootTarget", "EventEmitter", "fn", "a", "b", "mappers", "j", "warn", "invertedPath", "<PERSON><PERSON><PERSON>", "i", "targets", "propagationPath", "currentTarget", "eventMode", "location", "testFn", "pruneFn", "ignore", "shouldReturn", "nestedHit", "isInteractive", "isInteractiveMode", "isInteractiveTarget", "relativeLocation", "int", "effect", "handler<PERSON><PERSON>", "from", "e", "isRightButton", "trackingData", "isMouse", "outTarget", "outType", "outEvent", "leaveEvent", "overType", "overEvent", "overTargetA<PERSON>tor", "enterEvent", "allMethods", "allowGlobalPointerEvents", "pressTarget", "clickTarget", "clickEvent", "clickHistory", "wheelEvent", "to", "id", "constructor", "listeners", "MOUSE_POINTER_ID", "TOUCH_TO_POINTER", "_EventSystem", "value", "resolution", "mode", "applyStyles", "style", "nativeEvent", "nativeEvent2", "federatedEvent", "normalizedEvents", "outside", "element", "rect", "resolutionMultiplier", "li", "touch", "tempEvent", "EventSystem", "FederatedContainer", "listener", "capture", "signal", "once", "context", "listenerFn", "emitter", "extensions", "Container"], "mappings": "0kDAGA,MAAMA,CAAe,CAKnB,YAAYC,EAAS,CAEnB,KAAK,QAAU,GAEf,KAAK,aAAe,GAKpB,KAAK,WAAa,GAMlB,KAAK,SAAW,GAEhB,KAAK,iBAAmB,GAKxB,KAAK,WAAaD,EAAe,UAAU,KAE3C,KAAK,mBAAqB,GAE1B,KAAK,8BAAgC,GAErC,KAAK,MAAQ,IAAIE,EAEjB,KAAK,KAAO,IAAIA,EAChB,KAAK,KAAO,EACZ,KAAK,gBAAkB,EACvB,KAAK,UAAY,EACjB,KAAK,eAAiB,EACtB,KAAK,QAAUD,CAChB,CAED,IAAI,QAAS,CACX,OAAO,KAAK,MAAM,CACnB,CAED,IAAI,QAAS,CACX,OAAO,KAAK,MAAM,CACnB,CAED,IAAI,OAAQ,CACV,OAAO,KAAK,KAAK,CAClB,CAED,IAAI,OAAQ,CACV,OAAO,KAAK,KAAK,CAClB,CAKD,IAAI,MAAO,CACT,OAAO,IACR,CAED,cAAe,CACb,OAAI,KAAK,UAAY,CAAC,KAAK,MAAQ,KAAK,KAAK,KAAK,KAAK,OAAS,CAAC,IAAM,KAAK,UAC1E,KAAK,KAAO,KAAK,OAAS,KAAK,QAAQ,gBAAgB,KAAK,MAAM,EAAI,IAEjE,KAAK,IACb,CAQD,UAAUE,EAAOC,EAAUC,EAAa,CACtC,MAAM,IAAI,MAAM,qFAAqF,CACtG,CAUD,YAAYC,EAAUC,EAAaC,EAAgBC,EAAUC,EAAY,CACvE,MAAM,IAAI,MAAM,uFAAuF,CACxG,CAED,gBAAiB,CACX,KAAK,uBAAuB,OAAS,KAAK,YAAY,YACxD,KAAK,YAAY,iBAEnB,KAAK,iBAAmB,EACzB,CAMD,0BAA2B,CACzB,KAAK,8BAAgC,EACtC,CAKD,iBAAkB,CAChB,KAAK,mBAAqB,EAC3B,CACH,CCvHA,IAAIC,EAAc,UACdC,EAAY,QACZC,EAAc,QACdC,EAAiB,8BACjBC,EAAe,2BACfC,EAAgB,WAChBC,EAAc,qCACdC,EAAe,QACfC,EAAe,iBACfC,EAAgB,wBAChBC,EAAkB,cAClBC,EAAoB,QACpBC,EAAa,cACbC,EAAc,gCACdC,EAAe,yBACfC,EAAuB,SAAUC,EAAW,CAC5C,OAAQ,OAAOA,EAAc,KACzBA,EAAU,WAAa,YACvB,OAAOA,EAAU,gBAAmB,UACpCA,EAAU,eAAiB,GAC3B,OAAO,SAAa,GAC5B,EACA,SAASC,GAAYC,EAAW,CAC5B,OAAO,SAAUC,EAAO,CAAE,OAAOA,EAAM,KAAKD,CAAS,EACzD,CACe,SAASE,EAASC,EAAO,CACpC,IAAIC,EAAM,CACN,UAAW,GACX,SAAU,GACV,eAAgB,CACxB,EACQ,CAACD,GAAS,OAAO,UAAc,IAC/BC,EAAM,CACF,UAAW,UAAU,UACrB,SAAU,UAAU,SACpB,eAAgB,UAAU,gBAAkB,CACxD,EAEa,OAAOD,GAAU,SACtBC,EAAI,UAAYD,EAEXA,GAASA,EAAM,YACpBC,EAAM,CACF,UAAWD,EAAM,UACjB,SAAUA,EAAM,SAChB,eAAgBA,EAAM,gBAAkB,CACpD,GAEI,IAAIH,EAAYI,EAAI,UAChBC,EAAML,EAAU,MAAM,OAAO,EAC7B,OAAOK,EAAI,CAAC,EAAM,MAClBL,EAAYK,EAAI,CAAC,GAErBA,EAAML,EAAU,MAAM,SAAS,EAC3B,OAAOK,EAAI,CAAC,EAAM,MAClBL,EAAYK,EAAI,CAAC,GAErB,IAAIC,EAAQP,GAAYC,CAAS,EAC7BO,EAAS,CACT,MAAO,CACH,MAAOD,EAAMxB,CAAW,GAAK,CAACwB,EAAMhB,CAAY,EAChD,KAAMgB,EAAMvB,CAAS,EACrB,OAAQ,CAACuB,EAAMxB,CAAW,IACrBwB,EAAMtB,CAAW,GAAKa,EAAqBO,CAAG,IAC/C,CAACE,EAAMhB,CAAY,EACvB,UAAWgB,EAAMrB,CAAc,EAC/B,QAASqB,EAAMxB,CAAW,GACtBwB,EAAMvB,CAAS,GACfuB,EAAMtB,CAAW,GACjBsB,EAAMrB,CAAc,GACpBY,EAAqBO,CAAG,IACxB,CAACE,EAAMhB,CAAY,CAC1B,EACD,OAAQ,CACJ,MAAOgB,EAAMlB,CAAW,EACxB,OAAQ,CAACkB,EAAMlB,CAAW,GAAKkB,EAAMjB,CAAY,EACjD,OAAQiB,EAAMlB,CAAW,GAAKkB,EAAMjB,CAAY,CACnD,EACD,QAAS,CACL,MAAQ,CAACiB,EAAMhB,CAAY,GAAKgB,EAAMlB,CAAW,GAC5C,CAACkB,EAAMhB,CAAY,GAAKgB,EAAMpB,CAAY,EAC/C,OAAQ,CAACoB,EAAMhB,CAAY,GACvB,CAACgB,EAAMlB,CAAW,GAClB,CAACkB,EAAMpB,CAAY,IAClBoB,EAAMjB,CAAY,GAAKiB,EAAMnB,CAAa,GAC/C,OAAS,CAACmB,EAAMhB,CAAY,IACvBgB,EAAMlB,CAAW,GACdkB,EAAMjB,CAAY,GAClBiB,EAAMpB,CAAY,GAClBoB,EAAMnB,CAAa,IACvBmB,EAAM,aAAa,CAC1B,EACD,QAAS,CACL,MAAOA,EAAMhB,CAAY,EACzB,OAAQgB,EAAMf,CAAa,EAC3B,OAAQe,EAAMhB,CAAY,GAAKgB,EAAMf,CAAa,CACrD,EACD,MAAO,CACH,WAAYe,EAAMd,CAAe,EACjC,aAAcc,EAAMb,CAAiB,EACrC,MAAOa,EAAMZ,CAAU,EACvB,QAASY,EAAMV,CAAY,EAC3B,OAAQU,EAAMX,CAAW,EACzB,OAAQW,EAAMd,CAAe,GACzBc,EAAMb,CAAiB,GACvBa,EAAMZ,CAAU,GAChBY,EAAMV,CAAY,GAClBU,EAAMX,CAAW,CACxB,EACD,IAAK,GACL,MAAO,GACP,OAAQ,EAChB,EACI,OAAAY,EAAO,IACHA,EAAO,MAAM,QACTA,EAAO,QAAQ,QACfA,EAAO,QAAQ,QACfA,EAAO,MAAM,OACrBA,EAAO,MACHA,EAAO,MAAM,OAASA,EAAO,QAAQ,OAASA,EAAO,QAAQ,MACjEA,EAAO,OACHA,EAAO,MAAM,QAAUA,EAAO,QAAQ,QAAUA,EAAO,QAAQ,OAC5DA,CACX,CCxHA,MAAMC,GAAeC,EAAW,SAAWA,EACrCP,GAAWM,GAAa,WAAW,SAAS,ECE5CE,GAAe,EACfC,EAAiB,IACjBC,GAAkB,EAClBC,GAAkB,EAClBC,EAAmB,EACnBC,EAAgB,EAChBC,GAAiB,KACjBC,GAAiB,KACjBC,GAAkB,EAClBC,EAAuB,MAAMA,CAAqB,CAMtD,YAAYC,EAAUC,EAAcnB,GAAU,CAC5C,KAAK,YAAcmB,EAEnB,KAAK,MAAQ,GAEb,KAAK,eAAiB,GAEtB,KAAK,uBAAyB,GAE9B,KAAK,UAAY,GAEjB,KAAK,uBAAyB,GAE9B,KAAK,KAAO,KAEZ,KAAK,MAAQ,GAEb,KAAK,UAAY,EAEjB,KAAK,UAAY,GAEjB,KAAK,oBAAsB,EAE3B,KAAK,wBAA0B,IAC/B,KAAK,SAAW,MACZA,EAAY,QAAUA,EAAY,QACpC,KAAK,iBAAgB,EAEvB,KAAK,UAAYD,CAClB,CAMD,IAAI,UAAW,CACb,OAAO,KAAK,SACb,CAMD,IAAI,uBAAwB,CAC1B,OAAO,KAAK,sBACb,CACD,IAAI,SAAU,CACZ,OAAO,KAAK,QACb,CAKD,kBAAmB,CACjB,MAAME,EAAU,SAAS,cAAc,QAAQ,EAC/CA,EAAQ,MAAM,MAAQ,GAAGP,CAAa,KACtCO,EAAQ,MAAM,OAAS,GAAGP,CAAa,KACvCO,EAAQ,MAAM,SAAW,WACzBA,EAAQ,MAAM,IAAM,GAAGN,EAAc,KACrCM,EAAQ,MAAM,KAAO,GAAGL,EAAc,KACtCK,EAAQ,MAAM,OAASJ,GAAgB,SAAQ,EAC/CI,EAAQ,MAAM,gBAAkB,UAChCA,EAAQ,MAAQ,kDAChBA,EAAQ,iBAAiB,QAAS,IAAM,CACtC,KAAK,uBAAyB,GAC9B,KAAK,UAAS,EACd,KAAK,kBAAiB,CAC5B,CAAK,EACD,SAAS,KAAK,YAAYA,CAAO,EACjC,KAAK,SAAWA,CACjB,CAKD,mBAAoB,CACb,KAAK,WAGV,SAAS,KAAK,YAAY,KAAK,QAAQ,EACvC,KAAK,SAAW,KACjB,CAMD,WAAY,CACV,GAAI,KAAK,UACP,OAEF,KAAK,UAAY,GACZ,KAAK,OACR,KAAK,KAAO,SAAS,cAAc,KAAK,EACxC,KAAK,KAAK,MAAM,MAAQ,GAAGX,CAAc,KACzC,KAAK,KAAK,MAAM,OAAS,GAAGA,CAAc,KAC1C,KAAK,KAAK,MAAM,SAAW,WAC3B,KAAK,KAAK,MAAM,IAAM,GAAGC,EAAe,KACxC,KAAK,KAAK,MAAM,KAAO,GAAGC,EAAe,KACzC,KAAK,KAAK,MAAM,OAASC,EAAiB,SAAQ,EAClD,KAAK,KAAK,MAAM,cAAgB,QAE9B,KAAK,iBACP,KAAK,WAAa,KAAK,WAAW,KAAK,IAAI,EAC3C,WAAW,iBAAiB,UAAW,KAAK,WAAY,EAAK,GAE3D,KAAK,yBACP,KAAK,aAAe,KAAK,aAAa,KAAK,IAAI,EAC/C,WAAW,SAAS,iBAAiB,YAAa,KAAK,aAAc,EAAI,GAE3E,MAAMS,EAAS,KAAK,UAAU,KAAK,OACnC,GAAKA,EAAO,WAUVA,EAAO,WAAW,YAAY,KAAK,IAAI,EACvC,KAAK,wBAAuB,MAXN,CACtB,MAAMC,EAAW,IAAI,iBAAiB,IAAM,CACtCD,EAAO,aACTA,EAAO,WAAW,YAAY,KAAK,IAAI,EACvCC,EAAS,WAAU,EACnB,KAAK,wBAAuB,EAEtC,CAAO,EACDA,EAAS,QAAQ,SAAS,KAAM,CAAE,UAAW,GAAM,QAAS,EAAI,CAAE,CACxE,CAIG,CAED,yBAA0B,CACxB,KAAK,UAAU,QAAQ,WAAW,IAAI,IAAI,EACtC,KAAK,UAAU,oBACjB,KAAK,yBAAyB,KAAK,UAAU,kBAAkB,CAElE,CAKD,aAAc,CACZ,GAAI,GAAC,KAAK,WAAa,KAAK,wBAG5B,MAAK,UAAY,GACjB,WAAW,SAAS,oBAAoB,YAAa,KAAK,aAAc,EAAI,EACxE,KAAK,gBACP,WAAW,iBAAiB,UAAW,KAAK,WAAY,EAAK,EAE/D,KAAK,UAAU,QAAQ,WAAW,OAAO,IAAI,EAC7C,UAAWC,KAAS,KAAK,UACnBA,EAAM,gBAAkBA,EAAM,eAAe,aAC/CA,EAAM,eAAe,WAAW,YAAYA,EAAM,cAAc,EAChEA,EAAM,eAAiB,MAEzBA,EAAM,kBAAoB,GAE5B,KAAK,MAAM,QAASC,GAAQ,CACtBA,EAAI,YACNA,EAAI,WAAW,YAAYA,CAAG,CAEtC,CAAK,EACG,KAAK,MAAQ,KAAK,KAAK,YACzB,KAAK,KAAK,WAAW,YAAY,KAAK,IAAI,EAE5C,KAAK,MAAQ,GACb,KAAK,UAAY,GAClB,CAMD,yBAAyBC,EAAW,CAClC,GAAI,CAACA,EAAU,SAAW,CAACA,EAAU,mBACnC,OAEEA,EAAU,aACPA,EAAU,mBACb,KAAK,UAAUA,CAAS,EAE1BA,EAAU,UAAY,KAAK,WAE7B,MAAMC,EAAWD,EAAU,SAC3B,GAAIC,EACF,QAAS,EAAI,EAAG,EAAIA,EAAS,OAAQ,IACnC,KAAK,yBAAyBA,EAAS,CAAC,CAAC,CAG9C,CAKD,KAAKC,EAAS,CAEZ,MAAMC,EAAgB,CACpB,qBAAsB,CACpB,GAHgBX,EAAqB,eAIrC,GAAGU,GAAS,sBAAwB,CAAE,CACvC,CACP,EACI,KAAK,MAAQC,EAAc,qBAAqB,MAChD,KAAK,eAAiBA,EAAc,qBAAqB,cACzD,KAAK,uBAAyBA,EAAc,qBAAqB,sBAC7DA,EAAc,qBAAqB,iBACrC,KAAK,UAAS,EACL,KAAK,iBACd,KAAK,WAAa,KAAK,WAAW,KAAK,IAAI,EAC3C,WAAW,iBAAiB,UAAW,KAAK,WAAY,EAAK,GAE/D,KAAK,UAAU,QAAQ,WAAW,OAAO,IAAI,CAC9C,CASD,YAAa,CACX,MAAMC,EAAM,YAAY,MAKxB,GAJI,KAAK,YAAY,QAAQ,QAAUA,EAAM,KAAK,sBAGlD,KAAK,oBAAsBA,EAAM,KAAK,wBAClC,CAAC,KAAK,UAAU,mBAAqB,CAAC,KAAK,UAAU,KAAK,QAC5D,OAEF,MAAMC,EAA4B,IAAI,IACtC,GAAI,KAAK,UAAU,mBAAoB,CACrC,KAAK,yBAAyB,KAAK,UAAU,kBAAkB,EAC/D,UAAWP,KAAS,KAAK,UACnBA,EAAM,YAAc,KAAK,WAC3BO,EAAU,IAAI,KAAK,UAAU,QAAQP,CAAK,CAAC,CAGhD,CACD,QAAS,EAAI,KAAK,UAAU,OAAS,EAAG,GAAK,EAAG,IAAK,CACnD,MAAMA,EAAQ,KAAK,UAAU,CAAC,EACzBO,EAAU,IAAI,CAAC,IACdP,EAAM,gBAAkBA,EAAM,eAAe,aAC/CA,EAAM,eAAe,WAAW,YAAYA,EAAM,cAAc,EAChE,KAAK,MAAM,KAAKA,EAAM,cAAc,EACpCA,EAAM,eAAiB,MAEzBA,EAAM,kBAAoB,GAC1BQ,EAAY,KAAK,UAAW,EAAG,CAAC,EAEnC,CACD,GAAI,KAAK,UAAU,kBAAmB,CACpC,KAAM,CAAE,EAAAC,EAAG,EAAAC,EAAG,MAAOC,EAAW,OAAQC,GAAe,KAAK,UAAU,OAChEX,EAAM,KAAK,KACjBA,EAAI,MAAM,KAAO,GAAGQ,CAAC,KACrBR,EAAI,MAAM,IAAM,GAAGS,CAAC,KACpBT,EAAI,MAAM,MAAQ,GAAGU,CAAS,KAC9BV,EAAI,MAAM,OAAS,GAAGW,CAAU,IACjC,CACD,QAAS,EAAI,EAAG,EAAI,KAAK,UAAU,OAAQ,IAAK,CAC9C,MAAMZ,EAAQ,KAAK,UAAU,CAAC,EAC9B,GAAI,CAACA,EAAM,mBAAqB,CAACA,EAAM,eACrC,SAEF,MAAMC,EAAMD,EAAM,eACZa,EAAUb,EAAM,SAAWA,EAAM,UAAW,EAAC,UACnD,GAAIA,EAAM,QAAS,CACjB,MAAMc,EAAKd,EAAM,eACXe,EAAK,KAAK,UAAU,WACpBC,EAAK,KAAK,UAAU,WAC1Bf,EAAI,MAAM,KAAO,IAAIa,EAAG,GAAKD,EAAQ,EAAIC,EAAG,GAAKC,CAAE,KACnDd,EAAI,MAAM,IAAM,IAAIa,EAAG,GAAKD,EAAQ,EAAIC,EAAG,GAAKE,CAAE,KAClDf,EAAI,MAAM,MAAQ,GAAGY,EAAQ,MAAQC,EAAG,EAAIC,CAAE,KAC9Cd,EAAI,MAAM,OAAS,GAAGY,EAAQ,OAASC,EAAG,EAAIE,CAAE,IACxD,KAAa,CACL,KAAK,YAAYH,CAAO,EACxB,MAAME,EAAK,KAAK,UAAU,WACpBC,EAAK,KAAK,UAAU,WAC1Bf,EAAI,MAAM,KAAO,GAAGY,EAAQ,EAAIE,CAAE,KAClCd,EAAI,MAAM,IAAM,GAAGY,EAAQ,EAAIG,CAAE,KACjCf,EAAI,MAAM,MAAQ,GAAGY,EAAQ,MAAQE,CAAE,KACvCd,EAAI,MAAM,OAAS,GAAGY,EAAQ,OAASG,CAAE,IAC1C,CACF,CACD,KAAK,WACN,CAMD,iBAAiBf,EAAK,CACpBA,EAAI,UAAY,SAASA,EAAI,IAAI,iBAAiBA,EAAI,KAAK,mBAAmBA,EAAI,QAAQ,EAC3F,CAKD,YAAYY,EAAS,CACfA,EAAQ,EAAI,IACdA,EAAQ,OAASA,EAAQ,EACzBA,EAAQ,EAAI,GAEVA,EAAQ,EAAI,IACdA,EAAQ,QAAUA,EAAQ,EAC1BA,EAAQ,EAAI,GAEd,KAAM,CAAE,MAAOF,EAAW,OAAQC,CAAY,EAAG,KAAK,UAClDC,EAAQ,EAAIA,EAAQ,MAAQF,IAC9BE,EAAQ,MAAQF,EAAYE,EAAQ,GAElCA,EAAQ,EAAIA,EAAQ,OAASD,IAC/BC,EAAQ,OAASD,EAAaC,EAAQ,EAEzC,CAOD,UAAUX,EAAW,CACnB,IAAID,EAAM,KAAK,MAAM,IAAG,EACnBA,IACCC,EAAU,iBAAmB,SAC/BD,EAAM,SAAS,cAAc,QAAQ,GAErCA,EAAM,SAAS,cAAcC,EAAU,cAAc,EACrDD,EAAI,MAAM,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAchBC,EAAU,iBACZD,EAAI,UAAYC,EAAU,iBAG9BD,EAAI,MAAM,MAAQ,GAAGf,CAAc,KACnCe,EAAI,MAAM,OAAS,GAAGf,CAAc,KACpCe,EAAI,MAAM,gBAAkB,KAAK,MAAQ,wBAA0B,cACnEA,EAAI,MAAM,SAAW,WACrBA,EAAI,MAAM,OAASZ,EAAiB,SAAQ,EAC5CY,EAAI,MAAM,YAAc,OACpB,UAAU,UAAU,YAAa,EAAC,SAAS,QAAQ,EACrDA,EAAI,aAAa,YAAa,KAAK,EAEnCA,EAAI,aAAa,YAAa,QAAQ,EAEpC,UAAU,UAAU,MAAM,cAAc,EAC1CA,EAAI,aAAa,gBAAiB,WAAW,EAE7CA,EAAI,aAAa,gBAAiB,MAAM,EAE1CA,EAAI,iBAAiB,QAAS,KAAK,SAAS,KAAK,IAAI,CAAC,EACtDA,EAAI,iBAAiB,QAAS,KAAK,SAAS,KAAK,IAAI,CAAC,EACtDA,EAAI,iBAAiB,WAAY,KAAK,YAAY,KAAK,IAAI,CAAC,GAE9DA,EAAI,MAAM,cAAgBC,EAAU,wBACpCD,EAAI,KAAOC,EAAU,eACjBA,EAAU,iBAAmBA,EAAU,kBAAoB,KAC7DD,EAAI,MAAQC,EAAU,iBACb,CAACA,EAAU,gBAAkBA,EAAU,iBAAmB,QACnED,EAAI,MAAQ,aAAaC,EAAU,QAAQ,IAEzCA,EAAU,gBAAkBA,EAAU,iBAAmB,MAC3DD,EAAI,aAAa,aAAcC,EAAU,cAAc,EAErD,KAAK,OACP,KAAK,iBAAiBD,CAAG,EAE3BC,EAAU,kBAAoB,GAC9BA,EAAU,eAAiBD,EAC3BA,EAAI,UAAYC,EAChB,KAAK,UAAU,KAAKA,CAAS,EAC7B,KAAK,KAAK,YAAYA,EAAU,cAAc,EAC1CA,EAAU,cACZA,EAAU,eAAe,SAAWA,EAAU,SAEjD,CAOD,eAAe,EAAGe,EAAM,CACtB,KAAM,CAAE,UAAWC,GAAW,EAAE,OAC1BC,EAAW,KAAK,UAAU,OAAO,aACjCC,EAAQ,OAAO,OAAO,IAAI1E,EAAeyE,CAAQ,EAAG,CAAE,OAAAD,CAAM,CAAE,EACpEC,EAAS,WAAa,KAAK,UAAU,mBACrCF,EAAK,QAASI,GAAUF,EAAS,cAAcC,EAAOC,CAAK,CAAC,CAC7D,CAMD,SAAS,EAAG,CACV,KAAK,eAAe,EAAG,CAAC,QAAS,aAAc,KAAK,CAAC,CACtD,CAMD,SAAS,EAAG,CACL,EAAE,OAAO,aAAa,WAAW,GACpC,EAAE,OAAO,aAAa,YAAa,WAAW,EAEhD,KAAK,eAAe,EAAG,CAAC,WAAW,CAAC,CACrC,CAMD,YAAY,EAAG,CACR,EAAE,OAAO,aAAa,WAAW,GACpC,EAAE,OAAO,aAAa,YAAa,QAAQ,EAE7C,KAAK,eAAe,EAAG,CAAC,UAAU,CAAC,CACpC,CAMD,WAAW,EAAG,CACR,EAAE,UAAYpC,IAAgB,CAAC,KAAK,gBAGxC,KAAK,UAAS,CACf,CAMD,aAAa,EAAG,CACV,EAAE,YAAc,GAAK,EAAE,YAAc,GAGzC,KAAK,YAAW,CACjB,CAED,SAAU,CACR,KAAK,YAAW,EAChB,KAAK,kBAAiB,EACtB,KAAK,KAAO,KACZ,KAAK,MAAQ,KACb,KAAK,UAAY,KACjB,KAAK,UAAY,KACb,KAAK,gBACP,WAAW,oBAAoB,UAAW,KAAK,UAAU,CAE5D,CAKD,wBAAwBqC,EAAS,CAC3BA,EACF,KAAK,UAAS,EAEd,KAAK,YAAW,CAEnB,CACH,EAEA5B,EAAqB,UAAY,CAC/B,KAAM,CACJ6B,EAAc,YACdA,EAAc,YACf,EACD,KAAM,eACR,EAEA7B,EAAqB,eAAiB,CAKpC,iBAAkB,GAKlB,MAAO,GAKP,cAAe,GAKf,sBAAuB,EACzB,EACA,IAAI8B,GAAsB9B,EC5gB1B,MAAM+B,GAAsB,CAO1B,WAAY,GAOZ,gBAAiB,KAMjB,eAAgB,KAMhB,SAAU,EAMV,kBAAmB,GAKnB,eAAgB,KAQhB,eAAgB,SAMhB,eAAgB,KAQhB,wBAAyB,OAQzB,mBAAoB,GAMpB,UAAW,EACb,ECvEA,MAAMC,EAAkB,CACtB,aAAc,CAEZ,KAAK,qBAAuB,GAC5B,KAAK,WAAa,EAClB,KAAK,SAAW,GAChB,KAAK,aAAe,GACpB,KAAK,aAAe,EACrB,CAKD,KAAKC,EAAQ,CACX,KAAK,qBAAoB,EACzB,KAAK,OAASA,EACd,KAAK,qBAAuB,GAC5B,KAAK,WAAa,EAClB,KAAK,SAAW,GAChB,KAAK,aAAe,GACpB,KAAK,aAAe,EACrB,CAED,IAAI,aAAc,CAChB,OAAO,KAAK,YACb,CACD,IAAI,YAAYC,EAAQ,CACtB,KAAK,aAAeA,CACrB,CAED,mBAAoB,CACd,KAAK,cAAgB,CAAC,KAAK,aAG/BC,EAAO,OAAO,IAAI,KAAK,cAAe,KAAMC,EAAgB,WAAW,EACvE,KAAK,aAAe,GACrB,CAED,sBAAuB,CAChB,KAAK,eAGVD,EAAO,OAAO,OAAO,KAAK,cAAe,IAAI,EAC7C,KAAK,aAAe,GACrB,CAED,cAAe,CACb,KAAK,SAAW,EACjB,CAED,SAAU,CACR,GAAI,CAAC,KAAK,YAAc,KAAK,aAC3B,OAEF,GAAI,KAAK,SAAU,CACjB,KAAK,SAAW,GAChB,MACD,CACD,MAAME,EAAmB,KAAK,OAAO,kBACjC,KAAK,OAAO,qBAAuBA,EAAiB,cAAgB,SAGxE,WAAW,SAAS,cAAc,IAAI,aAAa,cAAe,CAChE,QAASA,EAAiB,QAC1B,QAASA,EAAiB,QAC1B,YAAaA,EAAiB,YAC9B,UAAWA,EAAiB,SAC7B,CAAA,CAAC,CACH,CAQD,cAAcC,EAAQ,CACpB,KAAK,YAAcA,EAAO,UACtB,OAAK,WAAa,KAAK,wBAG3B,KAAK,WAAa,EAClB,KAAK,QAAO,EACb,CACH,CACA,MAAMC,EAAe,IAAIP,GCrFzB,MAAMQ,UAA4BxF,CAAe,CAC/C,aAAc,CACZ,MAAM,GAAG,SAAS,EAElB,KAAK,OAAS,IAAIE,EAElB,KAAK,SAAW,IAAIA,EAEpB,KAAK,OAAS,IAAIA,EAElB,KAAK,OAAS,IAAIA,EAKlB,KAAK,OAAS,IAAIA,CACnB,CAED,IAAI,SAAU,CACZ,OAAO,KAAK,OAAO,CACpB,CAED,IAAI,SAAU,CACZ,OAAO,KAAK,OAAO,CACpB,CAKD,IAAI,GAAI,CACN,OAAO,KAAK,OACb,CAKD,IAAI,GAAI,CACN,OAAO,KAAK,OACb,CAED,IAAI,WAAY,CACd,OAAO,KAAK,SAAS,CACtB,CAED,IAAI,WAAY,CACd,OAAO,KAAK,SAAS,CACtB,CAED,IAAI,SAAU,CACZ,OAAO,KAAK,OAAO,CACpB,CAED,IAAI,SAAU,CACZ,OAAO,KAAK,OAAO,CACpB,CAED,IAAI,SAAU,CACZ,OAAO,KAAK,OAAO,CACpB,CAED,IAAI,SAAU,CACZ,OAAO,KAAK,OAAO,CACpB,CAKD,IAAI,SAAU,CACZ,OAAO,KAAK,OAAO,CACpB,CAKD,IAAI,SAAU,CACZ,OAAO,KAAK,OAAO,CACpB,CAYD,iBAAiBsD,EAAWiC,EAAOC,EAAW,CAC5C,OAAOlC,EAAU,eAAe,aAAakC,GAAa,KAAK,OAAQD,CAAK,CAC7E,CAKD,iBAAiBE,EAAK,CACpB,MAAO,qBAAsB,KAAK,aAAe,KAAK,YAAY,iBAAiBA,CAAG,CACvF,CAqBD,eAAerF,EAAUsF,EAAepF,EAAgBC,EAAUC,EAAYmF,EAAaC,EAAaC,EAAaC,EAAaC,EAAaC,EAAYC,EAAcC,EAAaC,EAAYC,GAAmB,CACnN,MAAM,IAAI,MAAM,yBAAyB,CAC1C,CACH,CC1HA,MAAMC,UAA8Bf,CAAoB,CACtD,aAAc,CACZ,MAAM,GAAG,SAAS,EAMlB,KAAK,MAAQ,EAMb,KAAK,OAAS,EAKd,KAAK,UAAY,EAClB,CAED,oBAAqB,CACnB,OAAI,KAAK,OAAS,eAAiB,KAAK,OAAS,aAAe,KAAK,OAAS,YACrE,CAAC,IAAI,EAEP,EACR,CAED,oBAAqB,CACnB,MAAM,IAAI,MAAM,sCAAsC,CACvD,CACH,CChCA,MAAMgB,UAA4BhB,CAAoB,CACpD,aAAc,CACZ,MAAM,GAAG,SAAS,EAElB,KAAK,gBAAkB,EAEvB,KAAK,eAAiB,EAEtB,KAAK,eAAiB,CACvB,CACH,CAEAgB,EAAoB,gBAAkB,EAEtCA,EAAoB,eAAiB,EAErCA,EAAoB,eAAiB,ECVrC,MAAMC,GAAoB,KACpBC,GAAkB,IAAIxG,EACtByG,EAAmB,IAAIzG,EAC7B,MAAM0G,EAAc,CAIlB,YAAYC,EAAY,CAUtB,KAAK,SAAW,IAAIC,GAOpB,KAAK,UAAY,GAEjB,KAAK,uBAAyB,GAK9B,KAAK,aAAe,CAClB,aAAc,CAAE,CACtB,EAMI,KAAK,UAA4B,IAAI,IAErC,KAAK,wBAA0B,GAE/B,KAAK,aAAe,GAEpB,KAAK,oBAAsB,GAC3B,KAAK,WAAaD,EAClB,KAAK,WAAa,KAAK,WAAW,KAAK,IAAI,EAC3C,KAAK,UAAY,KAAK,UAAU,KAAK,IAAI,EACzC,KAAK,eAAiB,KAAK,eAAe,KAAK,IAAI,EACnD,KAAK,eAAiB,KAAK,eAAe,KAAK,IAAI,EACnD,KAAK,cAAgB,KAAK,cAAc,KAAK,IAAI,EACjD,KAAK,eAAiB,KAAK,eAAe,KAAK,IAAI,EACnD,KAAK,aAAe,KAAK,aAAa,KAAK,IAAI,EAC/C,KAAK,oBAAsB,KAAK,oBAAoB,KAAK,IAAI,EAC7D,KAAK,SAAW,KAAK,SAAS,KAAK,IAAI,EACvC,KAAK,aAAe,GACpB,KAAK,gBAAgB,cAAe,KAAK,cAAc,EACvD,KAAK,gBAAgB,cAAe,KAAK,cAAc,EACvD,KAAK,gBAAgB,aAAc,KAAK,aAAa,EACrD,KAAK,gBAAgB,eAAgB,KAAK,aAAa,EACvD,KAAK,gBAAgB,cAAe,KAAK,cAAc,EACvD,KAAK,gBAAgB,YAAa,KAAK,YAAY,EACnD,KAAK,gBAAgB,mBAAoB,KAAK,mBAAmB,EACjE,KAAK,gBAAgB,QAAS,KAAK,QAAQ,CAC5C,CAaD,gBAAgBtC,EAAMwC,EAAI,CACnB,KAAK,aAAaxC,CAAI,IACzB,KAAK,aAAaA,CAAI,EAAI,IAE5B,KAAK,aAAaA,CAAI,EAAE,KAAK,CAC3B,GAAAwC,EACA,SAAU,CAChB,CAAK,EACD,KAAK,aAAaxC,CAAI,EAAE,KAAK,CAACyC,EAAGC,IAAMD,EAAE,SAAWC,EAAE,QAAQ,CAC/D,CAMD,cAAc,EAAG1C,EAAM,CACrB,EAAE,mBAAqB,GACvB,EAAE,8BAAgC,GAClC,KAAK,UAAU,EAAGA,CAAI,EACtB,KAAK,SAAS,KAAKA,GAAQ,EAAE,KAAM,CAAC,CACrC,CAKD,SAAS,EAAG,CACV,GAAI,CAAC,KAAK,WACR,OAEF,MAAM2C,EAAU,KAAK,aAAa,EAAE,IAAI,EACxC,GAAIA,EACF,QAAS,EAAI,EAAGC,EAAID,EAAQ,OAAQ,EAAIC,EAAG,IACzCD,EAAQ,CAAC,EAAE,GAAG,CAAC,OAGjBE,EAAK,kDAAkD,EAAE,IAAI,EAAE,CAElE,CAQD,QAAQrD,EAAGC,EAAG,CACZuB,EAAa,YAAc,GAE3B,MAAMwB,EADU,KAAK,qBAAuB,KAAK,uBAC5B,uBAAyB,mBACxCM,EAAe,KAAKN,CAAE,EAC1B,KAAK,WACL,KAAK,WAAW,UAChBL,GAAgB,IAAI3C,EAAGC,CAAC,EACxB,KAAK,UACL,KAAK,UACX,EACI,OAAOqD,GAAgBA,EAAa,CAAC,CACtC,CAOD,UAAU,EAAG9C,EAAM,CACjB,GAAI,CAAC,EAAE,OACL,OAEF,MAAM+C,EAAe,EAAE,eACvB,EAAE,WAAa,EAAE,gBACjB,QAASC,EAAI,EAAGJ,EAAIG,EAAa,OAAS,EAAGC,EAAIJ,EAAGI,IAGlD,GAFA,EAAE,cAAgBD,EAAaC,CAAC,EAChC,KAAK,aAAa,EAAGhD,CAAI,EACrB,EAAE,oBAAsB,EAAE,8BAC5B,OAKJ,GAHA,EAAE,WAAa,EAAE,UACjB,EAAE,cAAgB,EAAE,OACpB,KAAK,aAAa,EAAGA,CAAI,EACrB,IAAE,oBAAsB,EAAE,+BAE9B,GAAE,WAAa,EAAE,eACjB,QAASgD,EAAID,EAAa,OAAS,EAAGC,GAAK,EAAGA,IAG5C,GAFA,EAAE,cAAgBD,EAAaC,CAAC,EAChC,KAAK,aAAa,EAAGhD,CAAI,EACrB,EAAE,oBAAsB,EAAE,8BAC5B,OAEL,CASD,IAAI,EAAGA,EAAMiD,EAAU,KAAK,wBAAyB,CACnD,GAAIA,EAAQ,SAAW,EACrB,OACF,EAAE,WAAa,EAAE,eACjB,MAAMvC,EAAS,MAAM,QAAQV,CAAI,EAAIA,EAAO,CAACA,CAAI,EACjD,QAASgD,EAAIC,EAAQ,OAAS,EAAGD,GAAK,EAAGA,IACvCtC,EAAO,QAASP,GAAU,CACxB,EAAE,cAAgB8C,EAAQD,CAAC,EAC3B,KAAK,aAAa,EAAG7C,CAAK,CAClC,CAAO,CAEJ,CAMD,gBAAgBF,EAAQ,CACtB,MAAMiD,EAAkB,CAACjD,CAAM,EAC/B,QAAS,EAAI,EAAG,EAAIiC,IAAsBjC,IAAW,KAAK,YAAcA,EAAO,OAAS,IAAK,CAC3F,GAAI,CAACA,EAAO,OACV,MAAM,IAAI,MAAM,qDAAqD,EAEvEiD,EAAgB,KAAKjD,EAAO,MAAM,EAClCA,EAASA,EAAO,MACjB,CACD,OAAAiD,EAAgB,QAAO,EAChBA,CACR,CACD,qBAAqBC,EAAeC,EAAWC,EAAUC,EAAQC,EAASC,EAAS,GAAO,CACxF,IAAIC,EAAe,GACnB,GAAI,KAAK,kBAAkBN,CAAa,EACtC,OAAO,KAIT,IAHIA,EAAc,YAAc,WAAaC,IAAc,aACzDpC,EAAa,YAAc,IAEzBmC,EAAc,qBAAuBA,EAAc,SAAU,CAC/D,MAAMjE,EAAWiE,EAAc,SAC/B,QAASH,EAAI9D,EAAS,OAAS,EAAG8D,GAAK,EAAGA,IAAK,CAC7C,MAAMjE,EAAQG,EAAS8D,CAAC,EAClBU,EAAY,KAAK,qBACrB3E,EACA,KAAK,eAAeqE,CAAS,EAAIA,EAAYrE,EAAM,UACnDsE,EACAC,EACAC,EACAC,GAAUD,EAAQJ,EAAeE,CAAQ,CACnD,EACQ,GAAIK,EAAW,CACb,GAAIA,EAAU,OAAS,GAAK,CAACA,EAAUA,EAAU,OAAS,CAAC,EAAE,OAC3D,SAEF,MAAMC,EAAgBR,EAAc,iBAChCO,EAAU,OAAS,GAAKC,KACtBA,GACF,KAAK,wBAAwB,KAAKR,CAAa,EACjDO,EAAU,KAAKP,CAAa,GAE1B,KAAK,aAAa,SAAW,IAC/B,KAAK,aAAeO,GACtBD,EAAe,EAChB,CACF,CACF,CACD,MAAMG,EAAoB,KAAK,eAAeR,CAAS,EACjDS,EAAsBV,EAAc,gBAG1C,OAFIU,GAAuBA,GACzB,KAAK,wBAAwB,KAAKV,CAAa,EAC7CK,GAAU,KAAK,aAAa,OAAS,EAChC,KACLC,EACK,KAAK,aACVG,GAAsB,CAACL,EAAQJ,EAAeE,CAAQ,GAAKC,EAAOH,EAAeE,CAAQ,EACpFQ,EAAsB,CAACV,CAAa,EAAI,GAE1C,IACR,CAeD,iBAAiBA,EAAeC,EAAWC,EAAUC,EAAQC,EAAS,CACpE,GAAI,KAAK,kBAAkBJ,CAAa,GAAKI,EAAQJ,EAAeE,CAAQ,EAC1E,OAAO,KAKT,IAHIF,EAAc,YAAc,WAAaC,IAAc,aACzDpC,EAAa,YAAc,IAEzBmC,EAAc,qBAAuBA,EAAc,SAAU,CAC/D,MAAMjE,EAAWiE,EAAc,SACzBW,EAAmBT,EACzB,QAASL,EAAI9D,EAAS,OAAS,EAAG8D,GAAK,EAAGA,IAAK,CAC7C,MAAMjE,EAAQG,EAAS8D,CAAC,EAClBU,EAAY,KAAK,iBACrB3E,EACA,KAAK,eAAeqE,CAAS,EAAIA,EAAYrE,EAAM,UACnD+E,EACAR,EACAC,CACV,EACQ,GAAIG,EAAW,CACb,GAAIA,EAAU,OAAS,GAAK,CAACA,EAAUA,EAAU,OAAS,CAAC,EAAE,OAC3D,SAEF,MAAMC,EAAgBR,EAAc,gBACpC,OAAIO,EAAU,OAAS,GAAKC,IAC1BD,EAAU,KAAKP,CAAa,EACvBO,CACR,CACF,CACF,CACD,MAAME,EAAoB,KAAK,eAAeR,CAAS,EACjDS,EAAsBV,EAAc,gBAC1C,OAAIS,GAAqBN,EAAOH,EAAeE,CAAQ,EAC9CQ,EAAsB,CAACV,CAAa,EAAI,GAE1C,IACR,CACD,eAAeY,EAAK,CAClB,OAAOA,IAAQ,UAAYA,IAAQ,SACpC,CACD,kBAAkB9E,EAAW,CAO3B,MANI,CAACA,GAAa,CAACA,EAAU,SAAW,CAACA,EAAU,YAAc,CAACA,EAAU,YAGxEA,EAAU,YAAc,QAGxBA,EAAU,YAAc,WAAa,CAACA,EAAU,mBAIrD,CASD,WAAWA,EAAWoE,EAAU,CAC9B,GAAIpE,EAAU,UACZA,EAAU,eAAe,aAAaoE,EAAUjB,CAAgB,EAC5D,CAACnD,EAAU,QAAQ,SAASmD,EAAiB,EAAGA,EAAiB,CAAC,GACpE,MAAO,GAGX,GAAInD,EAAU,SAAWA,EAAU,QAAQ,OACzC,QAAS,EAAI,EAAG,EAAIA,EAAU,QAAQ,OAAQ,IAAK,CACjD,MAAM+E,EAAS/E,EAAU,QAAQ,CAAC,EAClC,GAAI+E,EAAO,eAEL,CADwBA,EAAO,cAAcX,EAAU,KAAK,SAAS,EAEvE,MAAO,EAGZ,CAEH,MAAO,EACR,CAOD,UAAUpE,EAAWoE,EAAU,CAC7B,OAAIpE,EAAU,QACL,GAELA,GAAW,eACbA,EAAU,eAAe,aAAaoE,EAAUjB,CAAgB,EACzDnD,EAAU,cAAcmD,CAAgB,GAE1C,EACR,CASD,aAAa,EAAGpC,EAAM,CACpB,GAAI,CAAC,EAAE,cAAc,gBACnB,OAEFA,IAASA,EAAO,EAAE,MAClB,MAAMiE,EAAa,KAAKjE,CAAI,GAC5B,EAAE,cAAciE,CAAU,IAAI,CAAC,EAC/B,MAAM7C,EAAM,EAAE,aAAe,EAAE,iBAAmB,EAAE,aAAe,EAAE,UAAY,GAAGpB,CAAI,UAAYA,EACpG,KAAK,iBAAiB,EAAGoB,CAAG,EACxB,EAAE,aAAe,EAAE,WACrB,KAAK,iBAAiB,EAAGpB,CAAI,CAEhC,CAOD,eAAekE,EAAM,CACnB,GAAI,EAAEA,aAAgBlC,GAAwB,CAC5Ca,EAAK,iEAAiE,EACtE,MACD,CACD,MAAMsB,EAAI,KAAK,mBAAmBD,CAAI,EAEtC,GADA,KAAK,cAAcC,EAAG,aAAa,EAC/BA,EAAE,cAAgB,QACpB,KAAK,cAAcA,EAAG,YAAY,UACzBA,EAAE,cAAgB,SAAWA,EAAE,cAAgB,MAAO,CAC/D,MAAMC,EAAgBD,EAAE,SAAW,EACnC,KAAK,cAAcA,EAAGC,EAAgB,YAAc,WAAW,CAChE,CACD,MAAMC,EAAe,KAAK,aAAaH,EAAK,SAAS,EACrDG,EAAa,qBAAqBH,EAAK,MAAM,EAAIC,EAAE,eACnD,KAAK,UAAUA,CAAC,CACjB,CAQD,eAAeD,EAAM,CACnB,GAAI,EAAEA,aAAgBlC,GAAwB,CAC5Ca,EAAK,iEAAiE,EACtE,MACD,CACD,KAAK,wBAAwB,OAAS,EACtC,KAAK,aAAa,OAAS,EAC3B,KAAK,oBAAsB,GAC3B,MAAMsB,EAAI,KAAK,mBAAmBD,CAAI,EACtC,KAAK,oBAAsB,GAC3B,MAAMI,EAAUH,EAAE,cAAgB,SAAWA,EAAE,cAAgB,MACzDE,EAAe,KAAK,aAAaH,EAAK,SAAS,EAC/CK,EAAY,KAAK,kBAAkBF,EAAa,WAAW,EACjE,GAAIA,EAAa,aAAa,OAAS,GAAKE,IAAcJ,EAAE,OAAQ,CAClE,MAAMK,EAAUN,EAAK,OAAS,YAAc,WAAa,aACnDO,EAAW,KAAK,mBAAmBP,EAAMM,EAASD,CAAS,EAIjE,GAHA,KAAK,cAAcE,EAAU,YAAY,EACrCH,GACF,KAAK,cAAcG,EAAU,UAAU,EACrC,CAACN,EAAE,aAAc,EAAC,SAASI,CAAS,EAAG,CACzC,MAAMG,EAAa,KAAK,mBAAmBR,EAAM,eAAgBK,CAAS,EAE1E,IADAG,EAAW,WAAaA,EAAW,UAC5BA,EAAW,QAAU,CAACP,EAAE,aAAY,EAAG,SAASO,EAAW,MAAM,GACtEA,EAAW,cAAgBA,EAAW,OACtC,KAAK,aAAaA,CAAU,EACxBJ,GACF,KAAK,aAAaI,EAAY,YAAY,EAC5CA,EAAW,OAASA,EAAW,OAAO,OAExC,KAAK,UAAUA,CAAU,CAC1B,CACD,KAAK,UAAUD,CAAQ,CACxB,CACD,GAAIF,IAAcJ,EAAE,OAAQ,CAC1B,MAAMQ,EAAWT,EAAK,OAAS,YAAc,YAAc,cACrDU,EAAY,KAAK,kBAAkBT,EAAGQ,CAAQ,EACpD,KAAK,cAAcC,EAAW,aAAa,EACvCN,GACF,KAAK,cAAcM,EAAW,WAAW,EAC3C,IAAIC,EAAqBN,GAAW,OACpC,KAAOM,GAAsBA,IAAuB,KAAK,WAAW,QAC9DA,IAAuBV,EAAE,QAE7BU,EAAqBA,EAAmB,OAG1C,GADwB,CAACA,GAAsBA,IAAuB,KAAK,WAAW,OACjE,CACnB,MAAMC,EAAa,KAAK,kBAAkBX,EAAG,cAAc,EAE3D,IADAW,EAAW,WAAaA,EAAW,UAC5BA,EAAW,QAAUA,EAAW,SAAWP,GAAaO,EAAW,SAAW,KAAK,WAAW,QACnGA,EAAW,cAAgBA,EAAW,OACtC,KAAK,aAAaA,CAAU,EACxBR,GACF,KAAK,aAAaQ,EAAY,YAAY,EAC5CA,EAAW,OAASA,EAAW,OAAO,OAExC,KAAK,UAAUA,CAAU,CAC1B,CACD,KAAK,UAAUF,CAAS,CACzB,CACD,MAAMG,EAAa,CAAA,EACbC,EAA2B,KAAK,wBAA0B,GAChE,KAAK,UAAYD,EAAW,KAAK,aAAa,EAAI,KAAK,cAAcZ,EAAG,aAAa,EACrFa,GAA4BD,EAAW,KAAK,mBAAmB,EAC3DZ,EAAE,cAAgB,UACpB,KAAK,UAAYY,EAAW,OAAO,EAAG,EAAG,WAAW,EAAI,KAAK,cAAcZ,EAAG,WAAW,EACzFa,GAA4BD,EAAW,KAAK,iBAAiB,GAE3DT,IACF,KAAK,UAAYS,EAAW,OAAO,EAAG,EAAG,WAAW,EAAI,KAAK,cAAcZ,EAAG,WAAW,EACzFa,GAA4BD,EAAW,KAAK,iBAAiB,EAC7D,KAAK,OAASZ,EAAE,QAAQ,QAEtBY,EAAW,OAAS,GACtB,KAAK,IAAIZ,EAAGY,CAAU,EAExB,KAAK,wBAAwB,OAAS,EACtC,KAAK,aAAa,OAAS,EAC3BV,EAAa,YAAcF,EAAE,eAC7B,KAAK,UAAUA,CAAC,CACjB,CAOD,eAAeD,EAAM,CACnB,GAAI,EAAEA,aAAgBlC,GAAwB,CAC5Ca,EAAK,iEAAiE,EACtE,MACD,CACD,MAAMwB,EAAe,KAAK,aAAaH,EAAK,SAAS,EAC/CC,EAAI,KAAK,mBAAmBD,CAAI,EAChCI,EAAUH,EAAE,cAAgB,SAAWA,EAAE,cAAgB,MAC/D,KAAK,cAAcA,EAAG,aAAa,EAC/BG,GACF,KAAK,cAAcH,EAAG,WAAW,EAC/BA,EAAE,cAAgB,UACpB,KAAK,OAASA,EAAE,QAAQ,QAC1B,MAAMW,EAAa,KAAK,kBAAkBX,EAAG,cAAc,EAE3D,IADAW,EAAW,WAAaA,EAAW,UAC5BA,EAAW,QAAUA,EAAW,SAAW,KAAK,WAAW,QAChEA,EAAW,cAAgBA,EAAW,OACtC,KAAK,aAAaA,CAAU,EACxBR,GACF,KAAK,aAAaQ,EAAY,YAAY,EAC5CA,EAAW,OAASA,EAAW,OAAO,OAExCT,EAAa,YAAcF,EAAE,eAC7B,KAAK,UAAUA,CAAC,EAChB,KAAK,UAAUW,CAAU,CAC1B,CAOD,cAAcZ,EAAM,CAClB,GAAI,EAAEA,aAAgBlC,GAAwB,CAC5Ca,EAAK,iEAAiE,EACtE,MACD,CACD,MAAMwB,EAAe,KAAK,aAAaH,EAAK,SAAS,EACrD,GAAIG,EAAa,YAAa,CAC5B,MAAMC,EAAUJ,EAAK,cAAgB,SAAWA,EAAK,cAAgB,MAC/DK,EAAY,KAAK,kBAAkBF,EAAa,WAAW,EAC3DI,EAAW,KAAK,mBAAmBP,EAAM,aAAcK,CAAS,EACtE,KAAK,cAAcE,CAAQ,EACvBH,GACF,KAAK,cAAcG,EAAU,UAAU,EACzC,MAAMC,EAAa,KAAK,mBAAmBR,EAAM,eAAgBK,CAAS,EAE1E,IADAG,EAAW,WAAaA,EAAW,UAC5BA,EAAW,QAAUA,EAAW,SAAW,KAAK,WAAW,QAChEA,EAAW,cAAgBA,EAAW,OACtC,KAAK,aAAaA,CAAU,EACxBJ,GACF,KAAK,aAAaI,EAAY,YAAY,EAC5CA,EAAW,OAASA,EAAW,OAAO,OAExCL,EAAa,YAAc,KAC3B,KAAK,UAAUI,CAAQ,EACvB,KAAK,UAAUC,CAAU,CAC1B,CACD,KAAK,OAAS,IACf,CAWD,aAAaR,EAAM,CACjB,GAAI,EAAEA,aAAgBlC,GAAwB,CAC5Ca,EAAK,iEAAiE,EACtE,MACD,CACD,MAAMxD,EAAM,YAAY,MAClB8E,EAAI,KAAK,mBAAmBD,CAAI,EAEtC,GADA,KAAK,cAAcC,EAAG,WAAW,EAC7BA,EAAE,cAAgB,QACpB,KAAK,cAAcA,EAAG,UAAU,UACvBA,EAAE,cAAgB,SAAWA,EAAE,cAAgB,MAAO,CAC/D,MAAMC,EAAgBD,EAAE,SAAW,EACnC,KAAK,cAAcA,EAAGC,EAAgB,UAAY,SAAS,CAC5D,CACD,MAAMC,EAAe,KAAK,aAAaH,EAAK,SAAS,EAC/Ce,EAAc,KAAK,kBAAkBZ,EAAa,qBAAqBH,EAAK,MAAM,CAAC,EACzF,IAAIgB,EAAcD,EAClB,GAAIA,GAAe,CAACd,EAAE,aAAY,EAAG,SAASc,CAAW,EAAG,CAC1D,IAAI9B,EAAgB8B,EACpB,KAAO9B,GAAiB,CAACgB,EAAE,aAAY,EAAG,SAAShB,CAAa,GAAG,CAGjE,GAFAgB,EAAE,cAAgBhB,EAClB,KAAK,aAAagB,EAAG,kBAAkB,EACnCA,EAAE,cAAgB,QACpB,KAAK,aAAaA,EAAG,iBAAiB,UAC7BA,EAAE,cAAgB,SAAWA,EAAE,cAAgB,MAAO,CAC/D,MAAMC,EAAgBD,EAAE,SAAW,EACnC,KAAK,aAAaA,EAAGC,EAAgB,iBAAmB,gBAAgB,CACzE,CACDjB,EAAgBA,EAAc,MAC/B,CACD,OAAOkB,EAAa,qBAAqBH,EAAK,MAAM,EACpDgB,EAAc/B,CACf,CACD,GAAI+B,EAAa,CACf,MAAMC,EAAa,KAAK,kBAAkBhB,EAAG,OAAO,EACpDgB,EAAW,OAASD,EACpBC,EAAW,KAAO,KACbd,EAAa,eAAeH,EAAK,MAAM,IAC1CG,EAAa,eAAeH,EAAK,MAAM,EAAI,CACzC,WAAY,EACZ,OAAQiB,EAAW,OACnB,UAAW9F,CACrB,GAEM,MAAM+F,EAAef,EAAa,eAAeH,EAAK,MAAM,EAS5D,GARIkB,EAAa,SAAWD,EAAW,QAAU9F,EAAM+F,EAAa,UAAY,IAC9E,EAAEA,EAAa,WAEfA,EAAa,WAAa,EAE5BA,EAAa,OAASD,EAAW,OACjCC,EAAa,UAAY/F,EACzB8F,EAAW,OAASC,EAAa,WAC7BD,EAAW,cAAgB,QAAS,CACtC,MAAMf,EAAgBe,EAAW,SAAW,EAC5C,KAAK,cAAcA,EAAYf,EAAgB,aAAe,OAAO,CAC7E,MAAiBe,EAAW,cAAgB,SACpC,KAAK,cAAcA,EAAY,KAAK,EAEtC,KAAK,cAAcA,EAAY,YAAY,EAC3C,KAAK,UAAUA,CAAU,CAC1B,CACD,KAAK,UAAUhB,CAAC,CACjB,CAYD,oBAAoBD,EAAM,CACxB,GAAI,EAAEA,aAAgBlC,GAAwB,CAC5Ca,EAAK,iEAAiE,EACtE,MACD,CACD,MAAMwB,EAAe,KAAK,aAAaH,EAAK,SAAS,EAC/Ce,EAAc,KAAK,kBAAkBZ,EAAa,qBAAqBH,EAAK,MAAM,CAAC,EACnFC,EAAI,KAAK,mBAAmBD,CAAI,EACtC,GAAIe,EAAa,CACf,IAAI9B,EAAgB8B,EACpB,KAAO9B,GACLgB,EAAE,cAAgBhB,EAClB,KAAK,aAAagB,EAAG,kBAAkB,EACnCA,EAAE,cAAgB,QACpB,KAAK,aAAaA,EAAG,iBAAiB,GAC7BA,EAAE,cAAgB,SAAWA,EAAE,cAAgB,QACxD,KAAK,aAAaA,EAAGA,EAAE,SAAW,EAAI,iBAAmB,gBAAgB,EAE3EhB,EAAgBA,EAAc,OAEhC,OAAOkB,EAAa,qBAAqBH,EAAK,MAAM,CACrD,CACD,KAAK,UAAUC,CAAC,CACjB,CAKD,SAASD,EAAM,CACb,GAAI,EAAEA,aAAgBjC,GAAsB,CAC1CY,EAAK,6DAA6D,EAClE,MACD,CACD,MAAMwC,EAAa,KAAK,iBAAiBnB,CAAI,EAC7C,KAAK,cAAcmB,CAAU,EAC7B,KAAK,UAAUA,CAAU,CAC1B,CASD,kBAAkBnC,EAAiB,CACjC,GAAI,CAACA,EACH,OAAO,KAET,IAAIC,EAAgBD,EAAgB,CAAC,EACrC,QAAS,EAAI,EAAG,EAAIA,EAAgB,QAC9BA,EAAgB,CAAC,EAAE,SAAWC,EADQ,IAExCA,EAAgBD,EAAgB,CAAC,EAKrC,OAAOC,CACR,CASD,mBAAmBe,EAAMlE,EAAMC,EAAQ,CACrC,MAAME,EAAQ,KAAK,cAAc6B,CAAqB,EACtD,YAAK,gBAAgBkC,EAAM/D,CAAK,EAChC,KAAK,cAAc+D,EAAM/D,CAAK,EAC9B,KAAK,SAAS+D,EAAM/D,CAAK,EACzBA,EAAM,YAAc+D,EAAK,YACzB/D,EAAM,cAAgB+D,EACtB/D,EAAM,OAASF,GAAU,KAAK,QAAQE,EAAM,OAAO,EAAGA,EAAM,OAAO,CAAC,GAAK,KAAK,aAAa,CAAC,EACxF,OAAOH,GAAS,WAClBG,EAAM,KAAOH,GAERG,CACR,CAOD,iBAAiB+D,EAAM,CACrB,MAAM/D,EAAQ,KAAK,cAAc8B,CAAmB,EACpD,YAAK,cAAciC,EAAM/D,CAAK,EAC9B,KAAK,cAAc+D,EAAM/D,CAAK,EAC9B,KAAK,SAAS+D,EAAM/D,CAAK,EACzBA,EAAM,YAAc+D,EAAK,YACzB/D,EAAM,cAAgB+D,EACtB/D,EAAM,OAAS,KAAK,QAAQA,EAAM,OAAO,EAAGA,EAAM,OAAO,CAAC,EACnDA,CACR,CAQD,kBAAkB+D,EAAMlE,EAAM,CAC5B,MAAMG,EAAQ,KAAK,cAAc6B,CAAqB,EACtD,OAAA7B,EAAM,YAAc+D,EAAK,YACzB/D,EAAM,cAAgB+D,EAAK,cAC3B,KAAK,gBAAgBA,EAAM/D,CAAK,EAChC,KAAK,cAAc+D,EAAM/D,CAAK,EAC9B,KAAK,SAAS+D,EAAM/D,CAAK,EACzBA,EAAM,OAAS+D,EAAK,OACpB/D,EAAM,KAAO+D,EAAK,aAAc,EAAC,MAAK,EACtC/D,EAAM,KAAOH,GAAQG,EAAM,KACpBA,CACR,CAYD,cAAc+D,EAAMoB,EAAI,CACtBA,EAAG,UAAYpB,EAAK,UACpBoB,EAAG,OAASpB,EAAK,OACjBoB,EAAG,OAASpB,EAAK,OACjBoB,EAAG,OAASpB,EAAK,MAClB,CAiBD,gBAAgBA,EAAMoB,EAAI,CAClBpB,aAAgBlC,GAAyBsD,aAActD,IAE7DsD,EAAG,UAAYpB,EAAK,UACpBoB,EAAG,MAAQpB,EAAK,MAChBoB,EAAG,OAASpB,EAAK,OACjBoB,EAAG,UAAYpB,EAAK,UACpBoB,EAAG,YAAcpB,EAAK,YACtBoB,EAAG,SAAWpB,EAAK,SACnBoB,EAAG,mBAAqBpB,EAAK,mBAC7BoB,EAAG,MAAQpB,EAAK,MAChBoB,EAAG,MAAQpB,EAAK,MAChBoB,EAAG,MAAQpB,EAAK,MACjB,CAuBD,cAAcA,EAAMoB,EAAI,CAChBpB,aAAgBjD,GAAuBqE,aAAcrE,IAE3DqE,EAAG,OAASpB,EAAK,OACjBoB,EAAG,OAASpB,EAAK,OACjBoB,EAAG,QAAUpB,EAAK,QAClBoB,EAAG,OAAO,SAASpB,EAAK,MAAM,EAC9BoB,EAAG,QAAUpB,EAAK,QAClBoB,EAAG,QAAUpB,EAAK,QAClBoB,EAAG,SAAS,SAASpB,EAAK,QAAQ,EAClCoB,EAAG,OAAO,SAASpB,EAAK,MAAM,EAC9BoB,EAAG,SAAWpB,EAAK,SACnBoB,EAAG,OAAO,SAASpB,EAAK,MAAM,EAC/B,CAYD,SAASA,EAAMoB,EAAI,CACjBA,EAAG,UAAYpB,EAAK,UACpBoB,EAAG,WAAapB,EAAK,WACrBoB,EAAG,UAAY,YAAY,MAC3BA,EAAG,KAAOpB,EAAK,KACfoB,EAAG,OAASpB,EAAK,OACjBoB,EAAG,KAAOpB,EAAK,KACfoB,EAAG,MAAQpB,EAAK,MAChBoB,EAAG,MAAM,SAASpB,EAAK,KAAK,EAC5BoB,EAAG,KAAK,SAASpB,EAAK,IAAI,CAC3B,CAMD,aAAaqB,EAAI,CACf,OAAK,KAAK,aAAa,aAAaA,CAAE,IACpC,KAAK,aAAa,aAAaA,CAAE,EAAI,CACnC,qBAAsB,CAAE,EACxB,eAAgB,CAAE,EAClB,WAAY,IACpB,GAEW,KAAK,aAAa,aAAaA,CAAE,CACzC,CAQD,cAAcC,EAAa,CACpB,KAAK,UAAU,IAAIA,CAAW,GACjC,KAAK,UAAU,IAAIA,EAAa,CAAE,CAAA,EAEpC,MAAMrF,EAAQ,KAAK,UAAU,IAAIqF,CAAW,EAAE,OAAS,IAAIA,EAAY,IAAI,EAC3E,OAAArF,EAAM,WAAaA,EAAM,KACzBA,EAAM,cAAgB,KACtBA,EAAM,iBAAmB,GACzBA,EAAM,KAAO,KACbA,EAAM,OAAS,KACRA,CACR,CAYD,UAAUA,EAAO,CACf,GAAIA,EAAM,UAAY,KACpB,MAAM,IAAI,MAAM,mEAAmE,EACrF,MAAMqF,EAAcrF,EAAM,YACrB,KAAK,UAAU,IAAIqF,CAAW,GACjC,KAAK,UAAU,IAAIA,EAAa,CAAE,CAAA,EAEpC,KAAK,UAAU,IAAIA,CAAW,EAAE,KAAKrF,CAAK,CAC3C,CAOD,iBAAiB,EAAGH,EAAM,CACxB,MAAMyF,EAAY,EAAE,cAAc,QAAQzF,CAAI,EAC9C,GAAKyF,EAEL,GAAI,OAAQA,EACNA,EAAU,MACZ,EAAE,cAAc,eAAezF,EAAMyF,EAAU,GAAI,OAAQ,EAAI,EACjEA,EAAU,GAAG,KAAKA,EAAU,QAAS,CAAC,MAEtC,SAASzC,EAAI,EAAGJ,EAAI6C,EAAU,OAAQzC,EAAIJ,GAAK,CAAC,EAAE,8BAA+BI,IAC3EyC,EAAUzC,CAAC,EAAE,MACf,EAAE,cAAc,eAAehD,EAAMyF,EAAUzC,CAAC,EAAE,GAAI,OAAQ,EAAI,EACpEyC,EAAUzC,CAAC,EAAE,GAAG,KAAKyC,EAAUzC,CAAC,EAAE,QAAS,CAAC,CAGjD,CACH,CC/6BA,MAAM0C,GAAmB,EACnBC,GAAmB,CACvB,WAAY,cACZ,SAAU,YACV,gBAAiB,mBACjB,UAAW,cACX,YAAa,eACf,EACMC,EAAe,MAAMA,CAAa,CAItC,YAAYlH,EAAU,CAEpB,KAAK,oBAAsB,iBAAkB,WAE7C,KAAK,sBAAwB,CAAC,CAAC,WAAW,aAK1C,KAAK,WAAa,KAElB,KAAK,WAAa,EAClB,KAAK,SAAWA,EAChB,KAAK,aAAe,IAAI2D,GAAc,IAAI,EAC1CrB,EAAa,KAAK,IAAI,EACtB,KAAK,mBAAqB,GAC1B,KAAK,aAAe,GACpB,KAAK,kBAAoB,IAAIgB,EAAsB,IAAI,EACvD,KAAK,gBAAkB,IAAIC,EAAoB,IAAI,EACnD,KAAK,aAAe,CAClB,QAAS,UACT,QAAS,SACf,EACI,KAAK,SAAW,IAAI,MAAM,CAAE,GAAG2D,EAAa,sBAAwB,CAClE,IAAK,CAAC3F,EAAQmB,EAAKyE,KACbzE,IAAQ,eACV,KAAK,aAAa,uBAAyByE,GAE7C5F,EAAOmB,CAAG,EAAIyE,EACP,GAEf,CAAK,EACD,KAAK,eAAiB,KAAK,eAAe,KAAK,IAAI,EACnD,KAAK,eAAiB,KAAK,eAAe,KAAK,IAAI,EACnD,KAAK,aAAe,KAAK,aAAa,KAAK,IAAI,EAC/C,KAAK,kBAAoB,KAAK,kBAAkB,KAAK,IAAI,EACzD,KAAK,QAAU,KAAK,QAAQ,KAAK,IAAI,CACtC,CAQD,WAAW,kBAAmB,CAC5B,OAAO,KAAK,iBACb,CAKD,KAAK1G,EAAS,CACZ,KAAM,CAAE,OAAAN,EAAQ,WAAAiH,GAAe,KAAK,SACpC,KAAK,iBAAiBjH,CAAM,EAC5B,KAAK,WAAaiH,EAClBF,EAAa,kBAAoBzG,EAAQ,WAAa,UACtD,OAAO,OAAO,KAAK,SAAUA,EAAQ,eAAiB,CAAA,CAAE,EACxD,KAAK,aAAa,uBAAyB,KAAK,SAAS,UAC1D,CAKD,iBAAiB2G,EAAY,CAC3B,KAAK,WAAaA,CACnB,CAED,SAAU,CACR,KAAK,iBAAiB,IAAI,EAC1B,KAAK,SAAW,KAChB,KAAK,eAAiB,IACvB,CAKD,UAAUC,EAAM,CACdA,IAASA,EAAO,WAChB,IAAIC,EAAc,GAIlB,GAHI,WAAW,iBAAmB,KAAK,sBAAsB,kBAC3DA,EAAc,IAEZ,KAAK,iBAAmBD,EAC1B,OAEF,KAAK,eAAiBA,EACtB,MAAME,EAAQ,KAAK,aAAaF,CAAI,EACpC,GAAIE,EACF,OAAQ,OAAOA,EAAK,CAClB,IAAK,SACCD,IACF,KAAK,WAAW,MAAM,OAASC,GAEjC,MACF,IAAK,WACHA,EAAMF,CAAI,EACV,MACF,IAAK,SACCC,GACF,OAAO,OAAO,KAAK,WAAW,MAAOC,CAAK,EAE5C,KACH,MACQD,GAAe,OAAOD,GAAS,UAAY,CAAC,OAAO,UAAU,eAAe,KAAK,KAAK,aAAcA,CAAI,IACjH,KAAK,WAAW,MAAM,OAASA,EAElC,CAMD,IAAI,SAAU,CACZ,OAAO,KAAK,iBACb,CAKD,eAAeG,EAAa,CAC1B,GAAI,CAAC,KAAK,SAAS,MACjB,OACF,KAAK,aAAa,WAAa,KAAK,SAAS,mBAC7C,MAAMxF,EAAS,KAAK,wBAAwBwF,CAAW,EACnD,KAAK,oBAAsBxF,EAAO,CAAC,EAAE,eACpBwF,EAAY,YAAc,EAAE,eAAgBA,KAE7DA,EAAY,eAAc,EAG9B,QAAS,EAAI,EAAGtD,EAAIlC,EAAO,OAAQ,EAAIkC,EAAG,IAAK,CAC7C,MAAMuD,EAAezF,EAAO,CAAC,EACvB0F,EAAiB,KAAK,gBAAgB,KAAK,kBAAmBD,CAAY,EAChF,KAAK,aAAa,SAASC,CAAc,CAC1C,CACD,KAAK,UAAU,KAAK,aAAa,MAAM,CACxC,CAKD,eAAeF,EAAa,CAC1B,GAAI,CAAC,KAAK,SAAS,KACjB,OACF,KAAK,aAAa,WAAa,KAAK,SAAS,mBAC7ClF,EAAa,aAAY,EACzB,MAAMqF,EAAmB,KAAK,wBAAwBH,CAAW,EACjE,QAAS,EAAI,EAAGtD,EAAIyD,EAAiB,OAAQ,EAAIzD,EAAG,IAAK,CACvD,MAAMzC,EAAQ,KAAK,gBAAgB,KAAK,kBAAmBkG,EAAiB,CAAC,CAAC,EAC9E,KAAK,aAAa,SAASlG,CAAK,CACjC,CACD,KAAK,UAAU,KAAK,aAAa,MAAM,CACxC,CAKD,aAAa+F,EAAa,CACxB,GAAI,CAAC,KAAK,SAAS,MACjB,OACF,KAAK,aAAa,WAAa,KAAK,SAAS,mBAC7C,IAAIjG,EAASiG,EAAY,OACrBA,EAAY,cAAgBA,EAAY,aAAc,EAAC,OAAS,IAClEjG,EAASiG,EAAY,aAAc,EAAC,CAAC,GAEvC,MAAMI,EAAUrG,IAAW,KAAK,WAAa,UAAY,GACnDoG,EAAmB,KAAK,wBAAwBH,CAAW,EACjE,QAASlD,EAAI,EAAGJ,EAAIyD,EAAiB,OAAQrD,EAAIJ,EAAGI,IAAK,CACvD,MAAM7C,EAAQ,KAAK,gBAAgB,KAAK,kBAAmBkG,EAAiBrD,CAAC,CAAC,EAC9E7C,EAAM,MAAQmG,EACd,KAAK,aAAa,SAASnG,CAAK,CACjC,CACD,KAAK,UAAU,KAAK,aAAa,MAAM,CACxC,CAKD,kBAAkB+F,EAAa,CAC7B,GAAI,CAAC,KAAK,SAAS,MACjB,OACF,KAAK,aAAa,WAAa,KAAK,SAAS,mBAC7C,MAAMG,EAAmB,KAAK,wBAAwBH,CAAW,EACjE,QAAS,EAAI,EAAGtD,EAAIyD,EAAiB,OAAQ,EAAIzD,EAAG,IAAK,CACvD,MAAMzC,EAAQ,KAAK,gBAAgB,KAAK,kBAAmBkG,EAAiB,CAAC,CAAC,EAC9E,KAAK,aAAa,SAASlG,CAAK,CACjC,CACD,KAAK,UAAU,KAAK,aAAa,MAAM,CACxC,CAKD,QAAQ+F,EAAa,CACnB,GAAI,CAAC,KAAK,SAAS,MACjB,OACF,MAAMb,EAAa,KAAK,oBAAoBa,CAAW,EACvD,KAAK,aAAa,WAAa,KAAK,SAAS,mBAC7C,KAAK,aAAa,SAASb,CAAU,CACtC,CAOD,iBAAiBkB,EAAS,CACxB,KAAK,cAAa,EAClB,KAAK,WAAaA,EAClBvF,EAAa,WAAauF,EAC1B,KAAK,WAAU,CAChB,CAED,YAAa,CACX,GAAI,KAAK,cAAgB,CAAC,KAAK,WAC7B,OAEFvF,EAAa,kBAAiB,EAC9B,MAAMiF,EAAQ,KAAK,WAAW,MAC1BA,IACE,WAAW,UAAU,kBACvBA,EAAM,iBAAmB,OACzBA,EAAM,cAAgB,QACb,KAAK,wBACdA,EAAM,YAAc,SAGpB,KAAK,uBACP,WAAW,SAAS,iBAAiB,cAAe,KAAK,eAAgB,EAAI,EAC7E,KAAK,WAAW,iBAAiB,cAAe,KAAK,eAAgB,EAAI,EACzE,KAAK,WAAW,iBAAiB,eAAgB,KAAK,kBAAmB,EAAI,EAC7E,KAAK,WAAW,iBAAiB,cAAe,KAAK,kBAAmB,EAAI,EAC5E,WAAW,iBAAiB,YAAa,KAAK,aAAc,EAAI,IAEhE,WAAW,SAAS,iBAAiB,YAAa,KAAK,eAAgB,EAAI,EAC3E,KAAK,WAAW,iBAAiB,YAAa,KAAK,eAAgB,EAAI,EACvE,KAAK,WAAW,iBAAiB,WAAY,KAAK,kBAAmB,EAAI,EACzE,KAAK,WAAW,iBAAiB,YAAa,KAAK,kBAAmB,EAAI,EAC1E,WAAW,iBAAiB,UAAW,KAAK,aAAc,EAAI,EAC1D,KAAK,sBACP,KAAK,WAAW,iBAAiB,aAAc,KAAK,eAAgB,EAAI,EACxE,KAAK,WAAW,iBAAiB,WAAY,KAAK,aAAc,EAAI,EACpE,KAAK,WAAW,iBAAiB,YAAa,KAAK,eAAgB,EAAI,IAG3E,KAAK,WAAW,iBAAiB,QAAS,KAAK,QAAS,CACtD,QAAS,GACT,QAAS,EACf,CAAK,EACD,KAAK,aAAe,EACrB,CAED,eAAgB,CACd,GAAI,CAAC,KAAK,cAAgB,CAAC,KAAK,WAC9B,OAEFjF,EAAa,qBAAoB,EACjC,MAAMiF,EAAQ,KAAK,WAAW,MAC1BA,IACE,WAAW,UAAU,kBACvBA,EAAM,iBAAmB,GACzBA,EAAM,cAAgB,IACb,KAAK,wBACdA,EAAM,YAAc,KAGpB,KAAK,uBACP,WAAW,SAAS,oBAAoB,cAAe,KAAK,eAAgB,EAAI,EAChF,KAAK,WAAW,oBAAoB,cAAe,KAAK,eAAgB,EAAI,EAC5E,KAAK,WAAW,oBAAoB,eAAgB,KAAK,kBAAmB,EAAI,EAChF,KAAK,WAAW,oBAAoB,cAAe,KAAK,kBAAmB,EAAI,EAC/E,WAAW,oBAAoB,YAAa,KAAK,aAAc,EAAI,IAEnE,WAAW,SAAS,oBAAoB,YAAa,KAAK,eAAgB,EAAI,EAC9E,KAAK,WAAW,oBAAoB,YAAa,KAAK,eAAgB,EAAI,EAC1E,KAAK,WAAW,oBAAoB,WAAY,KAAK,kBAAmB,EAAI,EAC5E,KAAK,WAAW,oBAAoB,YAAa,KAAK,kBAAmB,EAAI,EAC7E,WAAW,oBAAoB,UAAW,KAAK,aAAc,EAAI,EAC7D,KAAK,sBACP,KAAK,WAAW,oBAAoB,aAAc,KAAK,eAAgB,EAAI,EAC3E,KAAK,WAAW,oBAAoB,WAAY,KAAK,aAAc,EAAI,EACvE,KAAK,WAAW,oBAAoB,YAAa,KAAK,eAAgB,EAAI,IAG9E,KAAK,WAAW,oBAAoB,QAAS,KAAK,QAAS,EAAI,EAC/D,KAAK,WAAa,KAClB,KAAK,aAAe,EACrB,CASD,mBAAmB/E,EAAO1B,EAAGC,EAAG,CAC9B,MAAM+G,EAAO,KAAK,WAAW,YAAc,KAAK,WAAW,wBAA0B,CACnF,EAAG,EACH,EAAG,EACH,MAAO,KAAK,WAAW,MACvB,OAAQ,KAAK,WAAW,OACxB,KAAM,EACN,IAAK,CACX,EACUC,EAAuB,EAAI,KAAK,WACtCvF,EAAM,GAAK1B,EAAIgH,EAAK,OAAS,KAAK,WAAW,MAAQA,EAAK,OAASC,EACnEvF,EAAM,GAAKzB,EAAI+G,EAAK,MAAQ,KAAK,WAAW,OAASA,EAAK,QAAUC,CACrE,CAOD,wBAAwBtG,EAAO,CAC7B,MAAMkG,EAAmB,CAAA,EACzB,GAAI,KAAK,qBAAuBlG,aAAiB,WAC/C,QAAS,EAAI,EAAGuG,EAAKvG,EAAM,eAAe,OAAQ,EAAIuG,EAAI,IAAK,CAC7D,MAAMC,EAAQxG,EAAM,eAAe,CAAC,EAChC,OAAOwG,EAAM,OAAW,MAC1BA,EAAM,OAAS,GACb,OAAOA,EAAM,QAAY,MAC3BA,EAAM,QAAU,GACd,OAAOA,EAAM,UAAc,MAC7BA,EAAM,UAAYxG,EAAM,QAAQ,SAAW,GAAKA,EAAM,OAAS,cAE7D,OAAOwG,EAAM,MAAU,MACzBA,EAAM,MAAQA,EAAM,SAAW,GAC7B,OAAOA,EAAM,OAAW,MAC1BA,EAAM,OAASA,EAAM,SAAW,GAC9B,OAAOA,EAAM,MAAU,MACzBA,EAAM,MAAQ,GACZ,OAAOA,EAAM,MAAU,MACzBA,EAAM,MAAQ,GACZ,OAAOA,EAAM,YAAgB,MAC/BA,EAAM,YAAc,SAClB,OAAOA,EAAM,UAAc,MAC7BA,EAAM,UAAYA,EAAM,YAAc,GACpC,OAAOA,EAAM,SAAa,MAC5BA,EAAM,SAAWA,EAAM,OAAS,IAC9B,OAAOA,EAAM,MAAU,MACzBA,EAAM,MAAQ,GACZ,OAAOA,EAAM,mBAAuB,MACtCA,EAAM,mBAAqB,GACzB,OAAOA,EAAM,OAAW,MAC1BA,EAAM,OAASA,EAAM,QAAUA,EAAM,SACnC,OAAOA,EAAM,OAAW,MAC1BA,EAAM,OAASA,EAAM,QAAUA,EAAM,SACvCA,EAAM,aAAe,GACrBA,EAAM,KAAOxG,EAAM,KACnBkG,EAAiB,KAAKM,CAAK,CAC5B,SACQ,CAAC,WAAW,YAAcxG,aAAiB,aAAe,CAAC,KAAK,uBAAyB,EAAEA,aAAiB,WAAW,eAAgB,CAChJ,MAAMyG,EAAYzG,EACd,OAAOyG,EAAU,UAAc,MACjCA,EAAU,UAAY,IACpB,OAAOA,EAAU,MAAU,MAC7BA,EAAU,MAAQ,GAChB,OAAOA,EAAU,OAAW,MAC9BA,EAAU,OAAS,GACjB,OAAOA,EAAU,MAAU,MAC7BA,EAAU,MAAQ,GAChB,OAAOA,EAAU,MAAU,MAC7BA,EAAU,MAAQ,GAChB,OAAOA,EAAU,YAAgB,MACnCA,EAAU,YAAc,SACtB,OAAOA,EAAU,UAAc,MACjCA,EAAU,UAAYlB,IACpB,OAAOkB,EAAU,SAAa,MAChCA,EAAU,SAAW,IACnB,OAAOA,EAAU,MAAU,MAC7BA,EAAU,MAAQ,GAChB,OAAOA,EAAU,mBAAuB,MAC1CA,EAAU,mBAAqB,GACjCA,EAAU,aAAe,GACzBP,EAAiB,KAAKO,CAAS,CACrC,MACMP,EAAiB,KAAKlG,CAAK,EAE7B,OAAOkG,CACR,CASD,oBAAoBH,EAAa,CAC/B,MAAM/F,EAAQ,KAAK,gBACnB,YAAK,mBAAmBA,EAAO+F,CAAW,EAC1C/F,EAAM,OAAS+F,EAAY,OAC3B/F,EAAM,OAAS+F,EAAY,OAC3B/F,EAAM,OAAS+F,EAAY,OAC3B/F,EAAM,UAAY+F,EAAY,UAC9B,KAAK,mBAAmB/F,EAAM,OAAQ+F,EAAY,QAASA,EAAY,OAAO,EAC9E/F,EAAM,OAAO,SAASA,EAAM,MAAM,EAClCA,EAAM,OAAO,SAASA,EAAM,MAAM,EAClCA,EAAM,YAAc+F,EACpB/F,EAAM,KAAO+F,EAAY,KAClB/F,CACR,CAMD,gBAAgBA,EAAO+F,EAAa,CAClC,OAAA/F,EAAM,cAAgB,KACtBA,EAAM,YAAc+F,EACpB/F,EAAM,UAAY+F,EAAY,UAC9B/F,EAAM,MAAQ+F,EAAY,MAC1B/F,EAAM,OAAS+F,EAAY,OAC3B/F,EAAM,UAAY+F,EAAY,UAC9B/F,EAAM,YAAc+F,EAAY,YAChC/F,EAAM,SAAW+F,EAAY,SAC7B/F,EAAM,mBAAqB+F,EAAY,mBACvC/F,EAAM,MAAQ+F,EAAY,MAC1B/F,EAAM,MAAQ+F,EAAY,MAC1B/F,EAAM,MAAQ+F,EAAY,MAC1B,KAAK,mBAAmB/F,EAAO+F,CAAW,EAC1C,KAAK,mBAAmB/F,EAAM,OAAQ+F,EAAY,QAASA,EAAY,OAAO,EAC9E/F,EAAM,OAAO,SAASA,EAAM,MAAM,EAClCA,EAAM,OAAO,SAASA,EAAM,MAAM,EAClCA,EAAM,UAAY+F,EAAY,UAC1B/F,EAAM,OAAS,iBACjBA,EAAM,KAAO,cAEXA,EAAM,KAAK,WAAW,OAAO,IAC/BA,EAAM,KAAOA,EAAM,KAAK,QAAQ,QAAS,SAAS,GAEhDA,EAAM,KAAK,WAAW,OAAO,IAC/BA,EAAM,KAAOwF,GAAiBxF,EAAM,IAAI,GAAKA,EAAM,MAE9CA,CACR,CAMD,mBAAmBA,EAAO+F,EAAa,CACrC/F,EAAM,UAAY+F,EAAY,UAC9B/F,EAAM,WAAa+F,EAAY,WAC/B/F,EAAM,UAAY,YAAY,MAC9BA,EAAM,KAAO+F,EAAY,KACzB/F,EAAM,OAAS+F,EAAY,OAC3B/F,EAAM,OAAS+F,EAAY,OAC3B/F,EAAM,QAAU+F,EAAY,QAC5B/F,EAAM,OAAO,EAAI+F,EAAY,QAC7B/F,EAAM,OAAO,EAAI+F,EAAY,QAC7B/F,EAAM,QAAU+F,EAAY,QAC5B/F,EAAM,QAAU+F,EAAY,QAC5B/F,EAAM,SAAS,EAAI+F,EAAY,UAC/B/F,EAAM,SAAS,EAAI+F,EAAY,UAC/B/F,EAAM,KAAK,EAAI+F,EAAY,MAC3B/F,EAAM,KAAK,EAAI+F,EAAY,MAC3B/F,EAAM,cAAgB,KACtBA,EAAM,SAAW+F,EAAY,QAC9B,CACH,EAEAN,EAAa,UAAY,CACvB,KAAM,SACN,KAAM,CACJtF,EAAc,YACdA,EAAc,aACdA,EAAc,YACf,EACD,SAAU,EACZ,EAMAsF,EAAa,qBAAuB,CAElC,KAAM,GAEN,WAAY,GAEZ,MAAO,GAEP,MAAO,EACT,EACA,IAAIiB,EAAcjB,ECxflB,MAAMkB,GAAqB,CAUzB,QAAS,KAUT,YAAa,KAUb,aAAc,KAUd,aAAc,KAUd,YAAa,KAUb,kBAAmB,KAUnB,WAAY,KAUZ,YAAa,KAUb,UAAW,KAUX,iBAAkB,KAUlB,gBAAiB,KAUjB,cAAe,KAUf,eAAgB,KAUhB,eAAgB,KAUhB,cAAe,KAUf,oBAAqB,KAUrB,aAAc,KAUd,cAAe,KAUf,aAAc,KAUd,YAAa,KAUb,mBAAoB,KAUpB,aAAc,KAUd,YAAa,KAUb,UAAW,KAUX,iBAAkB,KAUlB,MAAO,KAUP,cAAe,KAUf,WAAY,KAUZ,kBAAmB,KAUnB,YAAa,KAUb,kBAAmB,KAUnB,aAAc,KAUd,QAAS,KAKT,IAAI,aAAc,CAChB,OAAO,KAAK,YAAc,WAAa,KAAK,YAAc,QAC3D,EACD,IAAI,YAAYjB,EAAO,CACrB,KAAK,UAAYA,EAAQ,SAAW,SACrC,EAID,mBAAoB,OAsBpB,IAAI,WAAY,CACd,OAAO,KAAK,oBAAsBgB,EAAY,gBAC/C,EACD,IAAI,UAAUhB,EAAO,CACnB,KAAK,mBAAqBA,CAC3B,EAyBD,eAAgB,CACd,OAAO,KAAK,YAAc,UAAY,KAAK,YAAc,SAC1D,EAMD,oBAAqB,GAarB,QAAS,KAiCT,iBAAiB7F,EAAM+G,EAAU5H,EAAS,CACxC,MAAM6H,EAAU,OAAO7H,GAAY,WAAaA,GAAW,OAAOA,GAAY,UAAYA,EAAQ,QAC5F8H,EAAS,OAAO9H,GAAY,SAAWA,EAAQ,OAAS,OACxD+H,EAAO,OAAO/H,GAAY,SAAWA,EAAQ,OAAS,GAAO,GAC7DgI,EAAU,OAAOJ,GAAa,WAAa,OAASA,EAC1D/G,EAAOgH,EAAU,GAAGhH,CAAI,UAAYA,EACpC,MAAMoH,EAAa,OAAOL,GAAa,WAAaA,EAAWA,EAAS,YAClEM,EAAU,KACZJ,GACFA,EAAO,iBAAiB,QAAS,IAAM,CACrCI,EAAQ,IAAIrH,EAAMoH,EAAYD,CAAO,CAC7C,CAAO,EAECD,EACFG,EAAQ,KAAKrH,EAAMoH,EAAYD,CAAO,EAEtCE,EAAQ,GAAGrH,EAAMoH,EAAYD,CAAO,CAEvC,EASD,oBAAoBnH,EAAM+G,EAAU5H,EAAS,CAC3C,MAAM6H,EAAU,OAAO7H,GAAY,WAAaA,GAAW,OAAOA,GAAY,UAAYA,EAAQ,QAC5FgI,EAAU,OAAOJ,GAAa,WAAa,OAASA,EAC1D/G,EAAOgH,EAAU,GAAGhH,CAAI,UAAYA,EACpC+G,EAAW,OAAOA,GAAa,WAAaA,EAAWA,EAAS,YAChE,KAAK,IAAI/G,EAAM+G,EAAUI,CAAO,CACjC,EAYD,cAAchD,EAAG,CACf,GAAI,EAAEA,aAAa1I,GACjB,MAAM,IAAI,MAAM,uEAAuE,EAEzF,OAAA0I,EAAE,iBAAmB,GACrBA,EAAE,KAAO,KACTA,EAAE,OAAS,KACXA,EAAE,QAAQ,cAAcA,CAAC,EAClB,CAACA,EAAE,gBACX,CACH,ECvfAmD,EAAW,IAAI/G,EAAmB,EAClCgH,EAAU,MAAM/G,EAAmB,ECDnC8G,EAAW,IAAIT,CAAW,EAC1BU,EAAU,MAAMT,EAAkB", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]}