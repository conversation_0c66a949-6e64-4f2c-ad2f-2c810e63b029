import{R as a}from"./declarationMapper.UBCwU7BT.js";import{R as p}from"./index.BoI39RQH.js";import{F as i}from"./flowGraphCachedOperationBlock.CtP7sxiu.js";class u extends i{constructor(t){super(a,t),this.config=t,this.object=this.registerDataInput("object",a,t.object),this.propertyName=this.registerDataInput("propertyName",a,t.propertyName),this.customGetFunction=this.registerDataInput("customGetFunction",a)}_doOperation(t){const o=this.customGetFunction.getValue(t);let r;if(o)r=o(this.object.getValue(t),this.propertyName.getValue(t),t);else{const e=this.object.getValue(t),s=this.propertyName.getValue(t);r=e&&s?this._getPropertyValue(e,s):void 0}return r}_getPropertyValue(t,o){const r=o.split(".");let e=t;for(const s of r)if(e=e[s],e===void 0)return;return e}getClassName(){return"FlowGraphGetPropertyBlock"}}p("FlowGraphGetPropertyBlock",u);export{u as FlowGraphGetPropertyBlock};
//# sourceMappingURL=flowGraphGetPropertyBlock.CvJub6Jr.js.map
