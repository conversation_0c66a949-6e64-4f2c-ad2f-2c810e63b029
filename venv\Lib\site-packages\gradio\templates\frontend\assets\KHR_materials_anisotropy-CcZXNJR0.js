import{ar as p,an as d,ao as m}from"./index-Dpxo-yl_.js";import{GLTFLoader as u}from"./glTFLoader-9Z3KGax5.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./bone-kZWM5-u7.js";import"./rawTexture-DmvUfjqF.js";import"./assetContainer-BRzQBugc.js";import"./objectModelMapping-BR4RdEzn.js";const n="KHR_materials_anisotropy";class y{constructor(s){this.name=n,this.order=195,this._loader=s,this.enabled=this._loader.isExtensionUsed(n)}dispose(){this._loader=null}loadMaterialPropertiesAsync(s,o,r){return u.LoadExtensionAsync(s,o,this.name,(t,e)=>{const i=new Array;return i.push(this._loader.loadMaterialPropertiesAsync(s,o,r)),i.push(this._loadIridescencePropertiesAsync(t,e,r)),Promise.all(i).then(()=>{})})}_loadIridescencePropertiesAsync(s,o,r){if(!(r instanceof p))throw new Error(`${s}: Material type not supported`);const t=new Array;return r.anisotropy.isEnabled=!0,r.anisotropy.intensity=o.anisotropyStrength??0,r.anisotropy.angle=o.anisotropyRotation??0,o.anisotropyTexture&&(o.anisotropyTexture.nonColorData=!0,t.push(this._loader.loadTextureInfoAsync(`${s}/anisotropyTexture`,o.anisotropyTexture,e=>{e.name=`${r.name} (Anisotropy Intensity)`,r.anisotropy.texture=e}))),Promise.all(t).then(()=>{})}}d(n);m(n,!0,a=>new y(a));export{y as KHR_materials_anisotropy};
//# sourceMappingURL=KHR_materials_anisotropy-CcZXNJR0.js.map
