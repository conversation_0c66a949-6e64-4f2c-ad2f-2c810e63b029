{"version": 3, "file": "Index.DHWTYgOZ.js", "sources": ["../../../../../../../dataset/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { Block } from \"@gradio/atoms\";\n\timport type { SvelteComponent, ComponentType } from \"svelte\";\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { BaseExample } from \"@gradio/textbox\";\n\texport let components: string[];\n\texport let component_props: Record<string, any>[];\n\texport let component_map: Map<\n\t\tstring,\n\t\tPromise<{\n\t\t\tdefault: ComponentType<SvelteComponent>;\n\t\t}>\n\t>;\n\texport let label = \"Examples\";\n\texport let show_label = true;\n\texport let headers: string[];\n\texport let samples: any[][] | null = null;\n\tlet old_samples: any[][] | null = null;\n\texport let sample_labels: string[] | null = null;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: number | null = null;\n\texport let root: string;\n\texport let proxy_url: null | string;\n\texport let samples_per_page = 10;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let gradio: Gradio<{\n\t\tclick: number;\n\t\tselect: SelectData;\n\t}>;\n\texport let layout: \"gallery\" | \"table\" | null = null;\n\n\t// Although the `samples_dir` prop is not used in any of the core Gradio component, it is kept for backward compatibility\n\t// with any custom components created with gradio<=4.20.0\n\tlet samples_dir: string = proxy_url\n\t\t? `/proxy=${proxy_url}file=`\n\t\t: `${root}/file=`;\n\tlet page = 0;\n\n\t$: gallery =\n\t\t(components.length < 2 || sample_labels !== null) && layout !== \"table\";\n\tlet paginate = samples ? samples.length > samples_per_page : false;\n\n\tlet selected_samples: any[][];\n\tlet page_count: number;\n\tlet visible_pages: number[] = [];\n\n\tlet current_hover = -1;\n\n\tfunction handle_mouseenter(i: number): void {\n\t\tcurrent_hover = i;\n\t}\n\n\tfunction handle_mouseleave(): void {\n\t\tcurrent_hover = -1;\n\t}\n\n\t$: {\n\t\tif (sample_labels) {\n\t\t\tsamples = sample_labels.map((e) => [e]);\n\t\t} else if (!samples) {\n\t\t\tsamples = [];\n\t\t}\n\t\tif (samples !== old_samples) {\n\t\t\tpage = 0;\n\t\t\told_samples = samples;\n\t\t}\n\t\tpaginate = samples.length > samples_per_page;\n\t\tif (paginate) {\n\t\t\tvisible_pages = [];\n\t\t\tselected_samples = samples.slice(\n\t\t\t\tpage * samples_per_page,\n\t\t\t\t(page + 1) * samples_per_page\n\t\t\t);\n\t\t\tpage_count = Math.ceil(samples.length / samples_per_page);\n\t\t\t[0, page, page_count - 1].forEach((anchor) => {\n\t\t\t\tfor (let i = anchor - 2; i <= anchor + 2; i++) {\n\t\t\t\t\tif (i >= 0 && i < page_count && !visible_pages.includes(i)) {\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\tvisible_pages.length > 0 &&\n\t\t\t\t\t\t\ti - visible_pages[visible_pages.length - 1] > 1\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tvisible_pages.push(-1);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvisible_pages.push(i);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\tselected_samples = samples.slice();\n\t\t}\n\t}\n\n\tlet component_meta: {\n\t\tvalue: any;\n\t\tcomponent: ComponentType<SvelteComponent>;\n\t}[][] = [];\n\n\tasync function get_component_meta(selected_samples: any[][]): Promise<void> {\n\t\tcomponent_meta = await Promise.all(\n\t\t\tselected_samples &&\n\t\t\t\tselected_samples.map(\n\t\t\t\t\tasync (sample_row) =>\n\t\t\t\t\t\tawait Promise.all(\n\t\t\t\t\t\t\tsample_row.map(async (sample_cell, j) => {\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\tvalue: sample_cell,\n\t\t\t\t\t\t\t\t\tcomponent: (await component_map.get(components[j]))\n\t\t\t\t\t\t\t\t\t\t?.default as ComponentType<SvelteComponent>\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t)\n\t\t\t\t)\n\t\t);\n\t}\n\n\t$: component_map, get_component_meta(selected_samples);\n</script>\n\n<Block\n\t{visible}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n\tcontainer={false}\n>\n\t{#if show_label}\n\t\t<div class=\"label\">\n\t\t\t<svg\n\t\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\t\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\t\t\t\taria-hidden=\"true\"\n\t\t\t\trole=\"img\"\n\t\t\t\twidth=\"1em\"\n\t\t\t\theight=\"1em\"\n\t\t\t\tpreserveAspectRatio=\"xMidYMid meet\"\n\t\t\t\tviewBox=\"0 0 32 32\"\n\t\t\t>\n\t\t\t\t<path\n\t\t\t\t\tfill=\"currentColor\"\n\t\t\t\t\td=\"M10 6h18v2H10zm0 18h18v2H10zm0-9h18v2H10zm-6 0h2v2H4zm0-9h2v2H4zm0 18h2v2H4z\"\n\t\t\t\t/>\n\t\t\t</svg>\n\t\t\t{label}\n\t\t</div>\n\t{/if}\n\t{#if gallery}\n\t\t<div class=\"gallery\">\n\t\t\t{#each selected_samples as sample_row, i}\n\t\t\t\t{#if sample_row[0] != null}\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass=\"gallery-item\"\n\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\tvalue = i + page * samples_per_page;\n\t\t\t\t\t\t\tgradio.dispatch(\"click\", value);\n\t\t\t\t\t\t\tgradio.dispatch(\"select\", { index: value, value: sample_row });\n\t\t\t\t\t\t}}\n\t\t\t\t\t\ton:mouseenter={() => handle_mouseenter(i)}\n\t\t\t\t\t\ton:mouseleave={() => handle_mouseleave()}\n\t\t\t\t\t>\n\t\t\t\t\t\t{#if sample_labels}\n\t\t\t\t\t\t\t<BaseExample\n\t\t\t\t\t\t\t\tvalue={sample_row[0]}\n\t\t\t\t\t\t\t\tselected={current_hover === i}\n\t\t\t\t\t\t\t\ttype=\"gallery\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t{:else if component_meta.length && component_map.get(components[0])}\n\t\t\t\t\t\t\t<svelte:component\n\t\t\t\t\t\t\t\tthis={component_meta[0][0].component}\n\t\t\t\t\t\t\t\t{...component_props[0]}\n\t\t\t\t\t\t\t\tvalue={sample_row[0]}\n\t\t\t\t\t\t\t\t{samples_dir}\n\t\t\t\t\t\t\t\ttype=\"gallery\"\n\t\t\t\t\t\t\t\tselected={current_hover === i}\n\t\t\t\t\t\t\t\tindex={i}\n\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</button>\n\t\t\t\t{/if}\n\t\t\t{/each}\n\t\t</div>\n\t{:else if selected_samples.length > 0}\n\t\t<div class=\"table-wrap\">\n\t\t\t<table tabindex=\"0\" role=\"grid\">\n\t\t\t\t<thead>\n\t\t\t\t\t<tr class=\"tr-head\">\n\t\t\t\t\t\t{#each headers as header}\n\t\t\t\t\t\t\t<th>\n\t\t\t\t\t\t\t\t{header}\n\t\t\t\t\t\t\t</th>\n\t\t\t\t\t\t{/each}\n\t\t\t\t\t</tr>\n\t\t\t\t</thead>\n\t\t\t\t<tbody>\n\t\t\t\t\t{#each component_meta as sample_row, i}\n\t\t\t\t\t\t<tr\n\t\t\t\t\t\t\tclass=\"tr-body\"\n\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\tvalue = i + page * samples_per_page;\n\t\t\t\t\t\t\t\tgradio.dispatch(\"click\", value);\n\t\t\t\t\t\t\t\tgradio.dispatch(\"select\", {\n\t\t\t\t\t\t\t\t\tindex: value,\n\t\t\t\t\t\t\t\t\tvalue: selected_samples[i]\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\ton:mouseenter={() => handle_mouseenter(i)}\n\t\t\t\t\t\t\ton:mouseleave={() => handle_mouseleave()}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{#each sample_row as { value, component }, j}\n\t\t\t\t\t\t\t\t{@const component_name = components[j]}\n\t\t\t\t\t\t\t\t{#if component_name !== undefined && component_map.get(component_name) !== undefined}\n\t\t\t\t\t\t\t\t\t<td\n\t\t\t\t\t\t\t\t\t\tstyle=\"max-width: {component_name === 'textbox'\n\t\t\t\t\t\t\t\t\t\t\t? '35ch'\n\t\t\t\t\t\t\t\t\t\t\t: 'auto'}\"\n\t\t\t\t\t\t\t\t\t\tclass={component_name}\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<svelte:component\n\t\t\t\t\t\t\t\t\t\t\tthis={component}\n\t\t\t\t\t\t\t\t\t\t\t{...component_props[j]}\n\t\t\t\t\t\t\t\t\t\t\t{value}\n\t\t\t\t\t\t\t\t\t\t\t{samples_dir}\n\t\t\t\t\t\t\t\t\t\t\ttype=\"table\"\n\t\t\t\t\t\t\t\t\t\t\tselected={current_hover === i}\n\t\t\t\t\t\t\t\t\t\t\tindex={i}\n\t\t\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t{/each}\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t{/each}\n\t\t\t\t</tbody>\n\t\t\t</table>\n\t\t</div>\n\t{/if}\n\t{#if paginate}\n\t\t<div class=\"paginate\">\n\t\t\tPages:\n\t\t\t{#each visible_pages as visible_page}\n\t\t\t\t{#if visible_page === -1}\n\t\t\t\t\t<div>...</div>\n\t\t\t\t{:else}\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass:current-page={page === visible_page}\n\t\t\t\t\t\ton:click={() => (page = visible_page)}\n\t\t\t\t\t>\n\t\t\t\t\t\t{visible_page + 1}\n\t\t\t\t\t</button>\n\t\t\t\t{/if}\n\t\t\t{/each}\n\t\t</div>\n\t{/if}\n</Block>\n\n<style>\n\t.wrap {\n\t\tdisplay: inline-block;\n\t\twidth: var(--size-full);\n\t\tmax-width: var(--size-full);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.hide {\n\t\tdisplay: none;\n\t}\n\n\t.label {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: var(--size-2);\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-weight: var(--block-label-text-weight);\n\t\tfont-size: var(--block-label-text-size);\n\t\tline-height: var(--line-sm);\n\t}\n\n\tsvg {\n\t\tmargin-right: var(--size-1);\n\t}\n\n\t.gallery {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: var(--spacing-lg);\n\t}\n\n\t.gallery-item {\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--button-large-radius);\n\t\toverflow: hidden;\n\t}\n\n\t.gallery-item:hover {\n\t\tborder-color: var(--border-color-accent);\n\t\tbackground: var(--table-row-focus);\n\t}\n\n\t.table-wrap {\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--table-radius);\n\t\twidth: var(--size-full);\n\t\ttable-layout: auto;\n\t\toverflow-x: auto;\n\t\tline-height: var(--line-sm);\n\t\tcolor: var(--table-text-color);\n\t}\n\ttable {\n\t\twidth: var(--size-full);\n\t}\n\n\t.tr-head {\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t}\n\n\t.tr-head > * + * {\n\t\tborder-right-width: 0px;\n\t\tborder-left-width: 1px;\n\t\tborder-color: var(--border-color-primary);\n\t}\n\n\tth {\n\t\tpadding: var(--size-2);\n\t\twhite-space: nowrap;\n\t}\n\n\t.tr-body {\n\t\tcursor: pointer;\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\t.tr-body:last-child {\n\t\tborder: none;\n\t}\n\n\t.tr-body:nth-child(odd) {\n\t\tbackground: var(--table-odd-background-fill);\n\t}\n\n\t.tr-body:hover {\n\t\tbackground: var(--table-row-focus);\n\t}\n\n\t.tr-body > * + * {\n\t\tborder-right-width: 0px;\n\t\tborder-left-width: 1px;\n\t\tborder-color: var(--border-color-primary);\n\t}\n\n\t.tr-body:hover > * + * {\n\t\tborder-color: var(--border-color-accent);\n\t}\n\n\ttd {\n\t\tpadding: var(--size-2);\n\t\ttext-align: center;\n\t}\n\n\t.paginate {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tgap: var(--spacing-sm);\n\t\tmargin-top: var(--size-2);\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-size: var(--text-sm);\n\t}\n\n\tbutton.current-page {\n\t\tfont-weight: var(--weight-bold);\n\t}\n</style>\n"], "names": ["constants_0", "child_ctx", "ctx", "insert_hydration", "target", "div", "anchor", "append_hydration", "svg", "path", "i", "table", "thead", "tr", "tbody", "each_blocks", "th", "set_data", "t0", "t0_value", "dirty", "get_spread_object", "set_style", "td", "button", "switch_value", "baseexample_changes", "create_if_block_3", "toggle_class", "create_if_block_1", "create_if_block_8", "create_if_block", "components", "$$props", "component_props", "component_map", "label", "show_label", "headers", "samples", "old_samples", "sample_labels", "elem_id", "elem_classes", "visible", "value", "root", "proxy_url", "samples_per_page", "scale", "min_width", "gradio", "layout", "samples_dir", "page", "paginate", "selected_samples", "page_count", "visible_pages", "current_hover", "handle_mouseenter", "handle_mouseleave", "component_meta", "get_component_meta", "sample_row", "sample_cell", "j", "_a", "$$invalidate", "mouseenter_handler", "mouseenter_handler_1", "click_handler_2", "visible_page", "gallery", "e"], "mappings": "qiCAuNiC,MAAAA,EAAAC,KAAWA,EAAC,EAAA,CAAA,mOAnEzCC,EAAK,CAAA,CAAA,wQAALA,EAAK,CAAA,CAAA,6cAhBPC,EAiBKC,EAAAC,EAAAC,CAAA,EAhBJC,EAcKF,EAAAG,CAAA,EAJJD,EAGCC,EAAAC,CAAA,sCAEDP,EAAK,CAAA,CAAA,uDA4CIA,EAAO,CAAA,CAAA,uBAAZ,OAAIQ,GAAA,6BAQAR,EAAc,EAAA,CAAA,uBAAnB,OAAIQ,GAAA,qtBAZTP,EAoDKC,EAAAC,EAAAC,CAAA,EAnDJC,EAkDOF,EAAAM,CAAA,EAjDNJ,EAQOI,EAAAC,CAAA,EAPNL,EAMIK,EAAAC,CAAA,0DAELN,EAuCOI,EAAAG,CAAA,gFA9CEZ,EAAO,CAAA,CAAA,oBAAZ,OAAIQ,GAAA,EAAA,mHAAJ,6BAQIR,EAAc,EAAA,CAAA,oBAAnB,OAAIQ,GAAA,EAAA,2GAAJ,OAAIA,EAAAK,EAAA,OAAAL,GAAA,yCAAJ,OAAIA,GAAA,4IA/CDR,EAAgB,EAAA,CAAA,uBAArB,OAAIQ,GAAA,uRADPP,EAkCKC,EAAAC,EAAAC,CAAA,sFAjCGJ,EAAgB,EAAA,CAAA,oBAArB,OAAIQ,GAAA,EAAA,2GAAJ,OAAIA,EAAAK,EAAA,OAAAL,GAAA,yCAAJ,OAAI,GAAA,iIAyCAR,EAAM,EAAA,EAAA,0KADRC,EAEIC,EAAAY,EAAAV,CAAA,wCADFJ,EAAM,EAAA,EAAA,KAAAe,GAAAC,EAAAC,CAAA,sDA+BAjB,KAAgBA,EAAC,EAAA,CAAA,mDAIX,SAAAA,QAAkBA,EAAC,EAAA,UACtBA,EAAC,EAAA,CAAA,sBANFA,EAAS,EAAA,iHACXkB,EAAA,CAAA,EAAA,GAAAC,EAAAnB,KAAgBA,EAAC,EAAA,CAAA,CAAA,mFAIX,SAAAA,QAAkBA,EAAC,EAAA,oNAXXoB,GAAAC,EAAA,YAAArB,QAAmB,UACnC,OACA,MAAM,mBACFA,EAAc,EAAA,CAAA,EAAA,gBAAA,UAJtBC,EAgBIC,EAAAmB,EAAAjB,CAAA,sDATIJ,EAAS,EAAA,GAAA,0KACXkB,EAAA,CAAA,EAAA,GAAAC,EAAAnB,KAAgBA,EAAC,EAAA,CAAA,CAAA,mFAIX,SAAAA,QAAkBA,EAAC,EAAA,6DAXXoB,GAAAC,EAAA,YAAArB,QAAmB,UACnC,OACA,MAAM,yBACFA,EAAc,EAAA,CAAA,EAAA,uJALlBA,EAAc,EAAA,IAAK,QAAaA,KAAc,IAAIA,EAAc,EAAA,CAAA,IAAM,4HAAtEA,EAAc,EAAA,IAAK,QAAaA,KAAc,IAAIA,EAAc,EAAA,CAAA,IAAM,qNAFrEA,EAAU,EAAA,CAAA,uBAAf,OAAIQ,GAAA,oWAbPP,EAmCIC,EAAAS,EAAAP,CAAA,0KAtBIJ,EAAU,EAAA,CAAA,oBAAf,OAAIQ,GAAA,EAAA,wGAAJ,OAAIA,EAAAK,EAAA,OAAAL,GAAA,yCAAJ,OAAIA,GAAA,2NAjDFR,EAAa,CAAA,EAAA,kBAMRA,EAAc,EAAA,EAAC,QAAUA,KAAc,IAAIA,EAAU,CAAA,EAAC,CAAC,CAAA,mTAhBlEC,EA4BQC,EAAAoB,EAAAlB,CAAA,4XATDJ,KAAgB,CAAC,EACd,CAAA,MAAAA,MAAW,CAAC,CAAA,wCAGT,SAAAA,QAAkBA,EAAC,EAAA,UACtBA,EAAC,EAAA,CAAA,gBANF,IAAAuB,EAAAvB,EAAe,EAAA,EAAA,CAAC,EAAE,CAAC,EAAE,wHACvBkB,EAAA,CAAA,EAAA,GAAAC,EAAAnB,KAAgB,CAAC,CAAA,EACdkB,EAAA,CAAA,EAAA,QAAA,CAAA,MAAAlB,MAAW,CAAC,CAAA,wDAGT,SAAAA,QAAkBA,EAAC,EAAA,iLALvB,GAAAkB,EAAA,CAAA,EAAA,SAAAK,KAAAA,EAAAvB,EAAe,EAAA,EAAA,CAAC,EAAE,CAAC,EAAE,WAAS,iLAChCkB,EAAA,CAAA,EAAA,GAAAC,EAAAnB,KAAgB,CAAC,CAAA,EACdkB,EAAA,CAAA,EAAA,QAAA,CAAA,MAAAlB,MAAW,CAAC,CAAA,wDAGT,SAAAA,QAAkBA,EAAC,EAAA,gMAXtB,MAAAA,MAAW,CAAC,EACT,SAAAA,QAAkBA,EAAC,EAAA,4GADtBkB,EAAA,CAAA,EAAA,SAAAM,EAAA,MAAAxB,MAAW,CAAC,GACTkB,EAAA,CAAA,EAAA,UAAAM,EAAA,SAAAxB,QAAkBA,EAAC,EAAA,sHAd5BA,EAAU,EAAA,EAAC,CAAC,GAAK,MAAIyB,GAAAzB,CAAA,4FAArBA,EAAU,EAAA,EAAC,CAAC,GAAK,8MA2FhBA,EAAa,EAAA,CAAA,uBAAlB,OAAIQ,GAAA,+CAFc;AAAA,IAEpB,+FAFoB;AAAA,IAEpB,iHAFDP,EAcKC,EAAAC,EAAAC,CAAA,sFAZGJ,EAAa,EAAA,CAAA,oBAAlB,OAAI,GAAA,EAAA,mHAAJ,oDAQEiB,EAAAjB,MAAe,EAAC,+MAHG0B,GAAAJ,EAAA,eAAAtB,QAASA,EAAY,EAAA,CAAA,UAD1CC,EAKQC,EAAAoB,EAAAlB,CAAA,uDADNc,EAAA,CAAA,EAAA,QAAAD,KAAAA,EAAAjB,MAAe,EAAC,KAAAe,GAAAC,EAAAC,CAAA,eAHGS,GAAAJ,EAAA,eAAAtB,QAASA,EAAY,EAAA,CAAA,0LAH1CC,EAAaC,EAAAC,EAAAC,CAAA,4DADT,OAAAJ,WAAmB2B,oOAnHtB3B,EAAU,CAAA,GAAA4B,GAAA5B,CAAA,8CAoBVA,EAAO,EAAA,EAAA,EAoCFA,EAAgB,EAAA,EAAC,OAAS,EAAC,uCAuDhCA,EAAQ,EAAA,GAAA6B,GAAA7B,CAAA,0MA/GRA,EAAU,CAAA,oOA+GVA,EAAQ,EAAA,4OAvHJ,6EAKO,aACL,kcA5HA,CAAA,WAAA8B,CAAA,EAAAC,EACA,CAAA,gBAAAC,CAAA,EAAAD,EACA,CAAA,cAAAE,CAAA,EAAAF,GAMA,MAAAG,EAAQ,UAAA,EAAAH,GACR,WAAAI,EAAa,EAAA,EAAAJ,EACb,CAAA,QAAAK,CAAA,EAAAL,GACA,QAAAM,EAA0B,IAAA,EAAAN,EACjCO,EAA8B,MACvB,cAAAC,EAAiC,IAAA,EAAAR,GACjC,QAAAS,EAAU,EAAA,EAAAT,EACV,CAAA,aAAAU,EAAA,EAAA,EAAAV,GACA,QAAAW,EAAU,EAAA,EAAAX,GACV,MAAAY,EAAuB,IAAA,EAAAZ,EACvB,CAAA,KAAAa,CAAA,EAAAb,EACA,CAAA,UAAAc,CAAA,EAAAd,GACA,iBAAAe,EAAmB,EAAA,EAAAf,GACnB,MAAAgB,GAAuB,IAAA,EAAAhB,GACvB,UAAAiB,GAAgC,MAAA,EAAAjB,EAChC,CAAA,OAAAkB,CAAA,EAAAlB,GAIA,OAAAmB,GAAqC,IAAA,EAAAnB,EAI5CoB,GAAsBN,EAAA,UACbA,CAAS,QAAA,GAChBD,CAAI,SACNQ,EAAO,EAIPC,GAAWhB,EAAUA,EAAQ,OAASS,EAAmB,GAEzDQ,EACAC,EACAC,EAAA,CAAA,EAEAC,GAAgB,YAEXC,GAAkBlD,EAAA,MAC1BiD,GAAgBjD,CAAA,EAGR,SAAAmD,IAAA,MACRF,GAAgB,EAAA,EAuCb,IAAAG,GAAA,CAAA,iBAKWC,GAAmBP,EAAAA,MACjCM,GAAuB,MAAA,QAAQ,IAC9BN,GACCA,EAAiB,IAAA,MACTQ,SACA,QAAQ,IACbA,EAAW,IAAA,MAAWC,GAAaC,KAAA,eAEjC,MAAOD,GACP,WAAAE,GAAA,MAAkBhC,EAAc,IAAIH,EAAWkC,EAAC,CAC7C,IADH,YAAAC,GACG,gCAgDLC,EAAA,EAAAvB,EAAQnC,EAAI4C,EAAON,CAAgB,EACnCG,EAAO,SAAS,QAASN,CAAK,EAC9BM,EAAO,SAAS,SAAQ,CAAI,MAAON,EAAO,MAAOmB,CAAU,CAAA,GAEvCK,GAAA3D,GAAAkD,GAAkBlD,CAAC,SACnBmD,YAyCnBO,EAAA,EAAAvB,EAAQnC,EAAI4C,EAAON,CAAgB,EACnCG,EAAO,SAAS,QAASN,CAAK,EAC9BM,EAAO,SAAS,SACf,CAAA,MAAON,EACP,MAAOW,EAAiB9C,CAAC,CAAA,CAAA,GAGN4D,GAAA5D,GAAAkD,GAAkBlD,CAAC,SACnBmD,KAuCLU,GAAAC,GAAAJ,EAAA,GAAAd,EAAOkB,CAAY,8uBAlNzCJ,EAAA,GAAGK,GACDzC,EAAW,OAAS,GAAKS,IAAkB,OAASW,KAAW,OAAA,6BAkB5DX,OACHF,EAAUE,EAAc,IAAKiC,GAAA,CAAOA,CAAC,CAAA,CAAA,EAC1BnC,GACX6B,EAAA,GAAA7B,EAAA,CAAA,CAAA,EAEGA,IAAYC,SACfc,EAAO,CAAA,OACPd,EAAcD,CAAA,GAEf6B,EAAA,GAAAb,GAAWhB,EAAQ,OAASS,CAAA,EACxBO,IACHa,EAAA,GAAAV,EAAA,CAAA,CAAA,EACAU,EAAA,GAAAZ,EAAmBjB,EAAQ,MAC1Be,EAAON,GACNM,EAAO,GAAKN,CAAA,CAAA,EAEdoB,EAAA,GAAAX,EAAa,KAAK,KAAKlB,EAAQ,OAASS,CAAgB,CAAA,EACvD,CAAA,EAAGM,EAAMG,EAAa,CAAC,EAAE,QAASnD,GAAA,CACzB,QAAAI,EAAIJ,EAAS,EAAGI,GAAKJ,EAAS,EAAGI,IACrCA,GAAK,GAAKA,EAAI+C,GAAe,CAAAC,EAAc,SAAShD,CAAC,IAEvDgD,EAAc,OAAS,GACvBhD,EAAIgD,EAAcA,EAAc,OAAS,CAAC,EAAI,GAE9CA,EAAc,OAAO,EAEtBA,EAAc,KAAKhD,CAAC,MAKvB0D,EAAA,GAAAZ,EAAmBjB,EAAQ,MAAA,CAAA,yBA2BXwB,GAAmBP,CAAgB"}