import{SvelteComponent as oe,init as ae,safe_not_equal as _e,binding_callbacks as S,bind as A,create_component as j,claim_component as B,mount_component as z,add_flush_callback as G,transition_in as w,transition_out as b,destroy_component as C,createEventDispatcher as fe,assign as ue,space as re,empty as W,claim_space as ce,insert_hydration as H,get_spread_update as he,get_spread_object as me,group_outros as de,check_outros as we,detach as J}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{B as be,S as ge}from"./2.B2AoQPnG.js";import{U as ve}from"./UploadText.CJcy9n89.js";import ke from"./Gallery.BgdW0y-K.js";import{B as je}from"./FileUpload.DUgXd8x6.js";/* empty css                                              */function Be(n){let e,i,t,o;function u(s){n[32](s)}function _(s){n[33](s)}let c={label:n[5],show_label:n[4],columns:n[14],rows:n[15],height:n[16],preview:n[17],object_fit:n[19],interactive:n[21],allow_preview:n[18],show_share_button:n[20],show_download_button:n[22],i18n:n[23].i18n,_fetch:n[31],show_fullscreen_button:n[24],fullscreen:n[3]};return n[1]!==void 0&&(c.selected_index=n[1]),n[0]!==void 0&&(c.value=n[0]),e=new ke({props:c}),S.push(()=>A(e,"selected_index",u)),S.push(()=>A(e,"value",_)),e.$on("change",n[34]),e.$on("select",n[35]),e.$on("share",n[36]),e.$on("error",n[37]),e.$on("preview_open",n[38]),e.$on("preview_close",n[39]),e.$on("fullscreen",n[40]),{c(){j(e.$$.fragment)},l(s){B(e.$$.fragment,s)},m(s,r){z(e,s,r),o=!0},p(s,r){const f={};r[0]&32&&(f.label=s[5]),r[0]&16&&(f.show_label=s[4]),r[0]&16384&&(f.columns=s[14]),r[0]&32768&&(f.rows=s[15]),r[0]&65536&&(f.height=s[16]),r[0]&131072&&(f.preview=s[17]),r[0]&524288&&(f.object_fit=s[19]),r[0]&2097152&&(f.interactive=s[21]),r[0]&262144&&(f.allow_preview=s[18]),r[0]&1048576&&(f.show_share_button=s[20]),r[0]&4194304&&(f.show_download_button=s[22]),r[0]&8388608&&(f.i18n=s[23].i18n),r[0]&8388608&&(f._fetch=s[31]),r[0]&16777216&&(f.show_fullscreen_button=s[24]),r[0]&8&&(f.fullscreen=s[3]),!i&&r[0]&2&&(i=!0,f.selected_index=s[1],G(()=>i=!1)),!t&&r[0]&1&&(t=!0,f.value=s[0],G(()=>t=!1)),e.$set(f)},i(s){o||(w(e.$$.fragment,s),o=!0)},o(s){b(e.$$.fragment,s),o=!1},d(s){C(e,s)}}}function ze(n){let e,i;return e=new je({props:{value:null,root:n[6],label:n[5],max_file_size:n[23].max_file_size,file_count:"multiple",file_types:n[10],i18n:n[23].i18n,upload:n[27],stream_handler:n[28],$$slots:{default:[Ce]},$$scope:{ctx:n}}}),e.$on("upload",n[29]),e.$on("error",n[30]),{c(){j(e.$$.fragment)},l(t){B(e.$$.fragment,t)},m(t,o){z(e,t,o),i=!0},p(t,o){const u={};o[0]&64&&(u.root=t[6]),o[0]&32&&(u.label=t[5]),o[0]&8388608&&(u.max_file_size=t[23].max_file_size),o[0]&1024&&(u.file_types=t[10]),o[0]&8388608&&(u.i18n=t[23].i18n),o[0]&8388608&&(u.upload=t[27]),o[0]&8388608&&(u.stream_handler=t[28]),o[0]&8388608|o[1]&4096&&(u.$$scope={dirty:o,ctx:t}),e.$set(u)},i(t){i||(w(e.$$.fragment,t),i=!0)},o(t){b(e.$$.fragment,t),i=!1},d(t){C(e,t)}}}function Ce(n){let e,i;return e=new ve({props:{i18n:n[23].i18n,type:"gallery"}}),{c(){j(e.$$.fragment)},l(t){B(e.$$.fragment,t)},m(t,o){z(e,t,o),i=!0},p(t,o){const u={};o[0]&8388608&&(u.i18n=t[23].i18n),e.$set(u)},i(t){i||(w(e.$$.fragment,t),i=!0)},o(t){b(e.$$.fragment,t),i=!1},d(t){C(e,t)}}}function Ue(n){let e,i,t,o,u,_;const c=[{autoscroll:n[23].autoscroll},{i18n:n[23].i18n},n[2]];let s={};for(let a=0;a<c.length;a+=1)s=ue(s,c[a]);e=new ge({props:s}),e.$on("clear_status",n[26]);const r=[ze,Be],f=[];function d(a,h){return a[21]&&a[25]?0:1}return t=d(n),o=f[t]=r[t](n),{c(){j(e.$$.fragment),i=re(),o.c(),u=W()},l(a){B(e.$$.fragment,a),i=ce(a),o.l(a),u=W()},m(a,h){z(e,a,h),H(a,i,h),f[t].m(a,h),H(a,u,h),_=!0},p(a,h){const U=h[0]&8388612?he(c,[h[0]&8388608&&{autoscroll:a[23].autoscroll},h[0]&8388608&&{i18n:a[23].i18n},h[0]&4&&me(a[2])]):{};e.$set(U);let g=t;t=d(a),t===g?f[t].p(a,h):(de(),b(f[g],1,1,()=>{f[g]=null}),we(),o=f[t],o?o.p(a,h):(o=f[t]=r[t](a),o.c()),w(o,1),o.m(u.parentNode,u))},i(a){_||(w(e.$$.fragment,a),w(o),_=!0)},o(a){b(e.$$.fragment,a),b(o),_=!1},d(a){a&&(J(i),J(u)),C(e,a),f[t].d(a)}}}function Se(n){let e,i,t;function o(_){n[41](_)}let u={visible:n[9],variant:"solid",padding:!1,elem_id:n[7],elem_classes:n[8],container:n[11],scale:n[12],min_width:n[13],allow_overflow:!1,height:typeof n[16]=="number"?n[16]:void 0,$$slots:{default:[Ue]},$$scope:{ctx:n}};return n[3]!==void 0&&(u.fullscreen=n[3]),e=new be({props:u}),S.push(()=>A(e,"fullscreen",o)),{c(){j(e.$$.fragment)},l(_){B(e.$$.fragment,_)},m(_,c){z(e,_,c),t=!0},p(_,c){const s={};c[0]&512&&(s.visible=_[9]),c[0]&128&&(s.elem_id=_[7]),c[0]&256&&(s.elem_classes=_[8]),c[0]&2048&&(s.container=_[11]),c[0]&4096&&(s.scale=_[12]),c[0]&8192&&(s.min_width=_[13]),c[0]&65536&&(s.height=typeof _[16]=="number"?_[16]:void 0),c[0]&67093631|c[1]&4096&&(s.$$scope={dirty:c,ctx:_}),!i&&c[0]&8&&(i=!0,s.fullscreen=_[3],G(()=>i=!1)),e.$set(s)},i(_){t||(w(e.$$.fragment,_),t=!0)},o(_){b(e.$$.fragment,_),t=!1},d(_){C(e,_)}}}async function Ae(n){return(await Promise.all(n.map(async i=>{var t;if((t=i.path)!=null&&t.toLowerCase().endsWith(".svg")&&i.url){const u=await(await fetch(i.url)).text();return{...i,url:`data:image/svg+xml,${encodeURIComponent(u)}`}}return i}))).map(i=>{var t;return(t=i.mime_type)!=null&&t.includes("video")?{video:i,caption:null}:{image:i,caption:null}})}function Ge(n,e,i){let t,{loading_status:o}=e,{show_label:u}=e,{label:_}=e,{root:c}=e,{elem_id:s=""}=e,{elem_classes:r=[]}=e,{visible:f=!0}=e,{value:d=null}=e,{file_types:a=["image","video"]}=e,{container:h=!0}=e,{scale:U=null}=e,{min_width:g=void 0}=e,{columns:I=[2]}=e,{rows:q=void 0}=e,{height:D="auto"}=e,{preview:E}=e,{allow_preview:F=!0}=e,{selected_index:v=null}=e,{object_fit:L="cover"}=e,{show_share_button:N=!1}=e,{interactive:P}=e,{show_download_button:R=!1}=e,{gradio:m}=e,{show_fullscreen_button:T=!0}=e,{fullscreen:k=!1}=e;const K=fe(),M=()=>m.dispatch("clear_status",o),O=(...l)=>m.client.upload(...l),Q=(...l)=>m.client.stream(...l),V=async l=>{const se=Array.isArray(l.detail)?l.detail:[l.detail];i(0,d=await Ae(se)),m.dispatch("upload",d),m.dispatch("change",d)},X=({detail:l})=>{i(2,o=o||{}),i(2,o.status="error",o),m.dispatch("error",l)},Y=(...l)=>m.client.fetch(...l);function Z(l){v=l,i(1,v)}function y(l){d=l,i(0,d)}const p=()=>m.dispatch("change",d),x=l=>m.dispatch("select",l.detail),$=l=>m.dispatch("share",l.detail),ee=l=>m.dispatch("error",l.detail),le=()=>m.dispatch("preview_open"),ne=()=>m.dispatch("preview_close"),ie=({detail:l})=>{i(3,k=l)};function te(l){k=l,i(3,k)}return n.$$set=l=>{"loading_status"in l&&i(2,o=l.loading_status),"show_label"in l&&i(4,u=l.show_label),"label"in l&&i(5,_=l.label),"root"in l&&i(6,c=l.root),"elem_id"in l&&i(7,s=l.elem_id),"elem_classes"in l&&i(8,r=l.elem_classes),"visible"in l&&i(9,f=l.visible),"value"in l&&i(0,d=l.value),"file_types"in l&&i(10,a=l.file_types),"container"in l&&i(11,h=l.container),"scale"in l&&i(12,U=l.scale),"min_width"in l&&i(13,g=l.min_width),"columns"in l&&i(14,I=l.columns),"rows"in l&&i(15,q=l.rows),"height"in l&&i(16,D=l.height),"preview"in l&&i(17,E=l.preview),"allow_preview"in l&&i(18,F=l.allow_preview),"selected_index"in l&&i(1,v=l.selected_index),"object_fit"in l&&i(19,L=l.object_fit),"show_share_button"in l&&i(20,N=l.show_share_button),"interactive"in l&&i(21,P=l.interactive),"show_download_button"in l&&i(22,R=l.show_download_button),"gradio"in l&&i(23,m=l.gradio),"show_fullscreen_button"in l&&i(24,T=l.show_fullscreen_button),"fullscreen"in l&&i(3,k=l.fullscreen)},n.$$.update=()=>{n.$$.dirty[0]&1&&i(25,t=d===null?!0:d.length===0),n.$$.dirty[0]&2&&K("prop_change",{selected_index:v})},[d,v,o,k,u,_,c,s,r,f,a,h,U,g,I,q,D,E,F,L,N,P,R,m,T,t,M,O,Q,V,X,Y,Z,y,p,x,$,ee,le,ne,ie,te]}class Pe extends oe{constructor(e){super(),ae(this,e,Ge,Se,_e,{loading_status:2,show_label:4,label:5,root:6,elem_id:7,elem_classes:8,visible:9,value:0,file_types:10,container:11,scale:12,min_width:13,columns:14,rows:15,height:16,preview:17,allow_preview:18,selected_index:1,object_fit:19,show_share_button:20,interactive:21,show_download_button:22,gradio:23,show_fullscreen_button:24,fullscreen:3},null,[-1,-1])}}export{ke as BaseGallery,Pe as default};
//# sourceMappingURL=Index.BJYxUfB0.js.map
