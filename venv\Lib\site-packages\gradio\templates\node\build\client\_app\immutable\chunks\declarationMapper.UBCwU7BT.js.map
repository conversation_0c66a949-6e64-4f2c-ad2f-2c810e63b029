{"version": 3, "file": "declarationMapper.UBCwU7BT.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/CustomTypes/flowGraphInteger.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/CustomTypes/flowGraphMatrix.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/flowGraphRichTypes.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_interactivity/declarationMapper.js"], "sourcesContent": ["import { RegisterClass } from \"../../Misc/typeStore.js\";\n/**\n * Class that represents an integer value.\n */\nexport class FlowGraphInteger {\n    constructor(value) {\n        this.value = this._toInt(value);\n    }\n    /**\n     * Converts a float to an integer.\n     * @param n the float to convert\n     * @returns the result of n | 0 - converting it to a int\n     */\n    _toInt(n) {\n        return n | 0;\n    }\n    /**\n     * Adds two integers together.\n     * @param other the other integer to add\n     * @returns a FlowGraphInteger with the result of the addition\n     */\n    add(other) {\n        return new FlowGraphInteger(this.value + other.value);\n    }\n    /**\n     * Subtracts two integers.\n     * @param other the other integer to subtract\n     * @returns a FlowGraphInteger with the result of the subtraction\n     */\n    subtract(other) {\n        return new FlowGraphInteger(this.value - other.value);\n    }\n    /**\n     * Multiplies two integers.\n     * @param other the other integer to multiply\n     * @returns a FlowGraphInteger with the result of the multiplication\n     */\n    multiply(other) {\n        return new FlowGraphInteger(Math.imul(this.value, other.value));\n    }\n    /**\n     * Divides two integers.\n     * @param other the other integer to divide\n     * @returns a FlowGraphInteger with the result of the division\n     */\n    divide(other) {\n        return new FlowGraphInteger(this.value / other.value);\n    }\n    /**\n     * The class name of this type.\n     * @returns\n     */\n    getClassName() {\n        return FlowGraphInteger.ClassName;\n    }\n    /**\n     * Compares two integers for equality.\n     * @param other the other integer to compare\n     * @returns\n     */\n    equals(other) {\n        return this.value === other.value;\n    }\n    /**\n     * Parses a FlowGraphInteger from a serialization object.\n     * @param value te number to parse\n     * @returns\n     */\n    static FromValue(value) {\n        return new FlowGraphInteger(value);\n    }\n    toString() {\n        return this.value.toString();\n    }\n}\nFlowGraphInteger.ClassName = \"FlowGraphInteger\";\nRegisterClass(\"FlowGraphInteger\", FlowGraphInteger);\n//# sourceMappingURL=flowGraphInteger.js.map", "import { Vector3, Vector2 } from \"../../Maths/math.vector.js\";\n// Note - the matrix classes are basically column-major, and work similarly to Babylon.js' Matrix class.\n/**\n * A 2x2 matrix.\n */\nexport class FlowGraphMatrix2D {\n    constructor(m = [1, 0, 0, 1]) {\n        this._m = m;\n    }\n    get m() {\n        return this._m;\n    }\n    transformVector(v) {\n        return this.transformVectorToRef(v, new Vector2());\n    }\n    transformVectorToRef(v, result) {\n        result.x = v.x * this._m[0] + v.y * this._m[1];\n        result.y = v.x * this._m[2] + v.y * this._m[3];\n        return result;\n    }\n    asArray() {\n        return this.toArray();\n    }\n    toArray(emptyArray = []) {\n        for (let i = 0; i < 4; i++) {\n            emptyArray[i] = this._m[i];\n        }\n        return emptyArray;\n    }\n    fromArray(array) {\n        for (let i = 0; i < 4; i++) {\n            this._m[i] = array[i];\n        }\n        return this;\n    }\n    multiplyToRef(other, result) {\n        const otherMatrix = other._m;\n        const thisMatrix = this._m;\n        const r = result._m;\n        // other * this\n        r[0] = otherMatrix[0] * thisMatrix[0] + otherMatrix[1] * thisMatrix[2];\n        r[1] = otherMatrix[0] * thisMatrix[1] + otherMatrix[1] * thisMatrix[3];\n        r[2] = otherMatrix[2] * thisMatrix[0] + otherMatrix[3] * thisMatrix[2];\n        r[3] = otherMatrix[2] * thisMatrix[1] + otherMatrix[3] * thisMatrix[3];\n        return result;\n    }\n    multiply(other) {\n        return this.multiplyToRef(other, new FlowGraphMatrix2D());\n    }\n    divideToRef(other, result) {\n        const m = this._m;\n        const o = other._m;\n        const r = result._m;\n        r[0] = m[0] / o[0];\n        r[1] = m[1] / o[1];\n        r[2] = m[2] / o[2];\n        r[3] = m[3] / o[3];\n        return result;\n    }\n    divide(other) {\n        return this.divideToRef(other, new FlowGraphMatrix2D());\n    }\n    addToRef(other, result) {\n        const m = this._m;\n        const o = other.m;\n        const r = result.m;\n        r[0] = m[0] + o[0];\n        r[1] = m[1] + o[1];\n        r[2] = m[2] + o[2];\n        r[3] = m[3] + o[3];\n        return result;\n    }\n    add(other) {\n        return this.addToRef(other, new FlowGraphMatrix2D());\n    }\n    subtractToRef(other, result) {\n        const m = this._m;\n        const o = other.m;\n        const r = result.m;\n        r[0] = m[0] - o[0];\n        r[1] = m[1] - o[1];\n        r[2] = m[2] - o[2];\n        r[3] = m[3] - o[3];\n        return result;\n    }\n    subtract(other) {\n        return this.subtractToRef(other, new FlowGraphMatrix2D());\n    }\n    transpose() {\n        const m = this._m;\n        return new FlowGraphMatrix2D([m[0], m[2], m[1], m[3]]);\n    }\n    determinant() {\n        const m = this._m;\n        return m[0] * m[3] - m[1] * m[2];\n    }\n    inverse() {\n        const det = this.determinant();\n        if (det === 0) {\n            throw new Error(\"Matrix is not invertible\");\n        }\n        const m = this._m;\n        const invDet = 1 / det;\n        return new FlowGraphMatrix2D([m[3] * invDet, -m[1] * invDet, -m[2] * invDet, m[0] * invDet]);\n    }\n    equals(other, epsilon = 0) {\n        const m = this._m;\n        const o = other.m;\n        if (epsilon === 0) {\n            return m[0] === o[0] && m[1] === o[1] && m[2] === o[2] && m[3] === o[3];\n        }\n        return Math.abs(m[0] - o[0]) < epsilon && Math.abs(m[1] - o[1]) < epsilon && Math.abs(m[2] - o[2]) < epsilon && Math.abs(m[3] - o[3]) < epsilon;\n    }\n    getClassName() {\n        return \"FlowGraphMatrix2D\";\n    }\n    toString() {\n        return `FlowGraphMatrix2D(${this._m.join(\", \")})`;\n    }\n}\n/**\n * A 3x3 matrix.\n */\nexport class FlowGraphMatrix3D {\n    constructor(array = [1, 0, 0, 0, 1, 0, 0, 0, 1]) {\n        this._m = array;\n    }\n    get m() {\n        return this._m;\n    }\n    transformVector(v) {\n        return this.transformVectorToRef(v, new Vector3());\n    }\n    transformVectorToRef(v, result) {\n        const m = this._m;\n        result.x = v.x * m[0] + v.y * m[1] + v.z * m[2];\n        result.y = v.x * m[3] + v.y * m[4] + v.z * m[5];\n        result.z = v.x * m[6] + v.y * m[7] + v.z * m[8];\n        return result;\n    }\n    multiplyToRef(other, result) {\n        const otherMatrix = other._m;\n        const thisMatrix = this._m;\n        const r = result.m;\n        r[0] = otherMatrix[0] * thisMatrix[0] + otherMatrix[1] * thisMatrix[3] + otherMatrix[2] * thisMatrix[6];\n        r[1] = otherMatrix[0] * thisMatrix[1] + otherMatrix[1] * thisMatrix[4] + otherMatrix[2] * thisMatrix[7];\n        r[2] = otherMatrix[0] * thisMatrix[2] + otherMatrix[1] * thisMatrix[5] + otherMatrix[2] * thisMatrix[8];\n        r[3] = otherMatrix[3] * thisMatrix[0] + otherMatrix[4] * thisMatrix[3] + otherMatrix[5] * thisMatrix[6];\n        r[4] = otherMatrix[3] * thisMatrix[1] + otherMatrix[4] * thisMatrix[4] + otherMatrix[5] * thisMatrix[7];\n        r[5] = otherMatrix[3] * thisMatrix[2] + otherMatrix[4] * thisMatrix[5] + otherMatrix[5] * thisMatrix[8];\n        r[6] = otherMatrix[6] * thisMatrix[0] + otherMatrix[7] * thisMatrix[3] + otherMatrix[8] * thisMatrix[6];\n        r[7] = otherMatrix[6] * thisMatrix[1] + otherMatrix[7] * thisMatrix[4] + otherMatrix[8] * thisMatrix[7];\n        r[8] = otherMatrix[6] * thisMatrix[2] + otherMatrix[7] * thisMatrix[5] + otherMatrix[8] * thisMatrix[8];\n        return result;\n    }\n    multiply(other) {\n        return this.multiplyToRef(other, new FlowGraphMatrix3D());\n    }\n    divideToRef(other, result) {\n        const m = this._m;\n        const o = other.m;\n        const r = result.m;\n        r[0] = m[0] / o[0];\n        r[1] = m[1] / o[1];\n        r[2] = m[2] / o[2];\n        r[3] = m[3] / o[3];\n        r[4] = m[4] / o[4];\n        r[5] = m[5] / o[5];\n        r[6] = m[6] / o[6];\n        r[7] = m[7] / o[7];\n        r[8] = m[8] / o[8];\n        return result;\n    }\n    divide(other) {\n        return this.divideToRef(other, new FlowGraphMatrix3D());\n    }\n    addToRef(other, result) {\n        const m = this._m;\n        const o = other.m;\n        const r = result.m;\n        r[0] = m[0] + o[0];\n        r[1] = m[1] + o[1];\n        r[2] = m[2] + o[2];\n        r[3] = m[3] + o[3];\n        r[4] = m[4] + o[4];\n        r[5] = m[5] + o[5];\n        r[6] = m[6] + o[6];\n        r[7] = m[7] + o[7];\n        r[8] = m[8] + o[8];\n        return result;\n    }\n    add(other) {\n        return this.addToRef(other, new FlowGraphMatrix3D());\n    }\n    subtractToRef(other, result) {\n        const m = this._m;\n        const o = other.m;\n        const r = result.m;\n        r[0] = m[0] - o[0];\n        r[1] = m[1] - o[1];\n        r[2] = m[2] - o[2];\n        r[3] = m[3] - o[3];\n        r[4] = m[4] - o[4];\n        r[5] = m[5] - o[5];\n        r[6] = m[6] - o[6];\n        r[7] = m[7] - o[7];\n        r[8] = m[8] - o[8];\n        return result;\n    }\n    subtract(other) {\n        return this.subtractToRef(other, new FlowGraphMatrix3D());\n    }\n    toArray(emptyArray = []) {\n        for (let i = 0; i < 9; i++) {\n            emptyArray[i] = this._m[i];\n        }\n        return emptyArray;\n    }\n    asArray() {\n        return this.toArray();\n    }\n    fromArray(array) {\n        for (let i = 0; i < 9; i++) {\n            this._m[i] = array[i];\n        }\n        return this;\n    }\n    transpose() {\n        const m = this._m;\n        return new FlowGraphMatrix3D([m[0], m[3], m[6], m[1], m[4], m[7], m[2], m[5], m[8]]);\n    }\n    determinant() {\n        const m = this._m;\n        return m[0] * (m[4] * m[8] - m[5] * m[7]) - m[1] * (m[3] * m[8] - m[5] * m[6]) + m[2] * (m[3] * m[7] - m[4] * m[6]);\n    }\n    inverse() {\n        const det = this.determinant();\n        if (det === 0) {\n            throw new Error(\"Matrix is not invertible\");\n        }\n        const m = this._m;\n        const invDet = 1 / det;\n        return new FlowGraphMatrix3D([\n            (m[4] * m[8] - m[5] * m[7]) * invDet,\n            (m[2] * m[7] - m[1] * m[8]) * invDet,\n            (m[1] * m[5] - m[2] * m[4]) * invDet,\n            (m[5] * m[6] - m[3] * m[8]) * invDet,\n            (m[0] * m[8] - m[2] * m[6]) * invDet,\n            (m[2] * m[3] - m[0] * m[5]) * invDet,\n            (m[3] * m[7] - m[4] * m[6]) * invDet,\n            (m[1] * m[6] - m[0] * m[7]) * invDet,\n            (m[0] * m[4] - m[1] * m[3]) * invDet,\n        ]);\n    }\n    equals(other, epsilon = 0) {\n        const m = this._m;\n        const o = other.m;\n        // performance shortcut\n        if (epsilon === 0) {\n            return m[0] === o[0] && m[1] === o[1] && m[2] === o[2] && m[3] === o[3] && m[4] === o[4] && m[5] === o[5] && m[6] === o[6] && m[7] === o[7] && m[8] === o[8];\n        }\n        return (Math.abs(m[0] - o[0]) < epsilon &&\n            Math.abs(m[1] - o[1]) < epsilon &&\n            Math.abs(m[2] - o[2]) < epsilon &&\n            Math.abs(m[3] - o[3]) < epsilon &&\n            Math.abs(m[4] - o[4]) < epsilon &&\n            Math.abs(m[5] - o[5]) < epsilon &&\n            Math.abs(m[6] - o[6]) < epsilon &&\n            Math.abs(m[7] - o[7]) < epsilon &&\n            Math.abs(m[8] - o[8]) < epsilon);\n    }\n    getClassName() {\n        return \"FlowGraphMatrix3D\";\n    }\n    toString() {\n        return `FlowGraphMatrix3D(${this._m.join(\", \")})`;\n    }\n}\n//# sourceMappingURL=flowGraphMatrix.js.map", "import { Vector2, Vector3, Vector4, <PERSON>, Quaternion } from \"../Maths/math.vector.js\";\nimport { Color3, Color4 } from \"../Maths/math.color.js\";\nimport { FlowGraphInteger } from \"./CustomTypes/flowGraphInteger.js\";\n\nimport { FlowGraphMatrix2D, FlowGraphMatrix3D } from \"./CustomTypes/flowGraphMatrix.js\";\n/**\n * The types supported by the flow graph.\n */\nexport var FlowGraphTypes;\n(function (FlowGraphTypes) {\n    FlowGraphTypes[\"Any\"] = \"any\";\n    FlowGraphTypes[\"String\"] = \"string\";\n    FlowGraphTypes[\"Number\"] = \"number\";\n    FlowGraphTypes[\"Boolean\"] = \"boolean\";\n    FlowGraphTypes[\"Object\"] = \"object\";\n    FlowGraphTypes[\"Integer\"] = \"FlowGraphInteger\";\n    FlowGraphTypes[\"Vector2\"] = \"Vector2\";\n    FlowGraphTypes[\"Vector3\"] = \"Vector3\";\n    FlowGraphTypes[\"Vector4\"] = \"Vector4\";\n    FlowGraphTypes[\"Quaternion\"] = \"Quaternion\";\n    FlowGraphTypes[\"Matrix\"] = \"Matrix\";\n    FlowGraphTypes[\"Matrix2D\"] = \"Matrix2D\";\n    FlowGraphTypes[\"Matrix3D\"] = \"Matrix3D\";\n    FlowGraphTypes[\"Color3\"] = \"Color3\";\n    FlowGraphTypes[\"Color4\"] = \"Color4\";\n})(FlowGraphTypes || (FlowGraphTypes = {}));\n/**\n * A rich type represents extra information about a type,\n * such as its name and a default value constructor.\n */\nexport class RichType {\n    constructor(\n    /**\n     * The name given to the type.\n     */\n    typeName, \n    /**\n     * The default value of the type.\n     */\n    defaultValue, \n    /**\n     * [-1] The ANIMATIONTYPE of the type, if available\n     */\n    animationType = -1) {\n        this.typeName = typeName;\n        this.defaultValue = defaultValue;\n        this.animationType = animationType;\n    }\n    /**\n     * Serializes this rich type into a serialization object.\n     * @param serializationObject the object to serialize to\n     */\n    serialize(serializationObject) {\n        serializationObject.typeName = this.typeName;\n        serializationObject.defaultValue = this.defaultValue;\n    }\n}\nexport const RichTypeAny = new RichType(\"any\" /* FlowGraphTypes.Any */, undefined);\nexport const RichTypeString = new RichType(\"string\" /* FlowGraphTypes.String */, \"\");\nexport const RichTypeNumber = new RichType(\"number\" /* FlowGraphTypes.Number */, 0, 0);\nexport const RichTypeBoolean = new RichType(\"boolean\" /* FlowGraphTypes.Boolean */, false);\nexport const RichTypeVector2 = new RichType(\"Vector2\" /* FlowGraphTypes.Vector2 */, Vector2.Zero(), 5);\nexport const RichTypeVector3 = new RichType(\"Vector3\" /* FlowGraphTypes.Vector3 */, Vector3.Zero(), 1);\nexport const RichTypeVector4 = new RichType(\"Vector4\" /* FlowGraphTypes.Vector4 */, Vector4.Zero());\nexport const RichTypeMatrix = new RichType(\"Matrix\" /* FlowGraphTypes.Matrix */, Matrix.Identity(), 3);\nexport const RichTypeMatrix2D = new RichType(\"Matrix2D\" /* FlowGraphTypes.Matrix2D */, new FlowGraphMatrix2D());\nexport const RichTypeMatrix3D = new RichType(\"Matrix3D\" /* FlowGraphTypes.Matrix3D */, new FlowGraphMatrix3D());\nexport const RichTypeColor3 = new RichType(\"Color3\" /* FlowGraphTypes.Color3 */, Color3.Black(), 4);\nexport const RichTypeColor4 = new RichType(\"Color4\" /* FlowGraphTypes.Color4 */, new Color4(0, 0, 0, 0), 7);\nexport const RichTypeQuaternion = new RichType(\"Quaternion\" /* FlowGraphTypes.Quaternion */, Quaternion.Identity(), 2);\nRichTypeQuaternion.typeTransformer = (value) => {\n    if (value.getClassName && value.getClassName() === \"Vector4\" /* FlowGraphTypes.Vector4 */) {\n        return Quaternion.FromArray(value.asArray());\n    }\n    else if (value.getClassName && value.getClassName() === \"Vector3\" /* FlowGraphTypes.Vector3 */) {\n        return Quaternion.FromEulerVector(value);\n    }\n    else if (value.getClassName && value.getClassName() === \"Matrix\" /* FlowGraphTypes.Matrix */) {\n        return Quaternion.FromRotationMatrix(value);\n    }\n    return value;\n};\nexport const RichTypeFlowGraphInteger = new RichType(\"FlowGraphInteger\" /* FlowGraphTypes.Integer */, new FlowGraphInteger(0), 0);\n/**\n * Given a value, try to deduce its rich type.\n * @param value the value to deduce the rich type from\n * @returns the value's rich type, or RichTypeAny if the type could not be deduced.\n */\nexport function getRichTypeFromValue(value) {\n    const anyValue = value;\n    switch (typeof value) {\n        case \"string\" /* FlowGraphTypes.String */:\n            return RichTypeString;\n        case \"number\" /* FlowGraphTypes.Number */:\n            return RichTypeNumber;\n        case \"boolean\" /* FlowGraphTypes.Boolean */:\n            return RichTypeBoolean;\n        case \"object\" /* FlowGraphTypes.Object */:\n            if (anyValue.getClassName) {\n                switch (anyValue.getClassName()) {\n                    case \"Vector2\" /* FlowGraphTypes.Vector2 */:\n                        return RichTypeVector2;\n                    case \"Vector3\" /* FlowGraphTypes.Vector3 */:\n                        return RichTypeVector3;\n                    case \"Vector4\" /* FlowGraphTypes.Vector4 */:\n                        return RichTypeVector4;\n                    case \"Matrix\" /* FlowGraphTypes.Matrix */:\n                        return RichTypeMatrix;\n                    case \"Color3\" /* FlowGraphTypes.Color3 */:\n                        return RichTypeColor3;\n                    case \"Color4\" /* FlowGraphTypes.Color4 */:\n                        return RichTypeColor4;\n                    case \"Quaternion\" /* FlowGraphTypes.Quaternion */:\n                        return RichTypeQuaternion;\n                    case \"FlowGraphInteger\" /* FlowGraphTypes.Integer */:\n                        return RichTypeFlowGraphInteger;\n                    case \"Matrix2D\" /* FlowGraphTypes.Matrix2D */:\n                        return RichTypeMatrix2D;\n                    case \"Matrix3D\" /* FlowGraphTypes.Matrix3D */:\n                        return RichTypeMatrix3D;\n                }\n            }\n            return RichTypeAny;\n        default:\n            return RichTypeAny;\n    }\n}\n/**\n * Given a flow graph type, return the rich type that corresponds to it.\n * @param flowGraphType the flow graph type\n * @returns the rich type that corresponds to the flow graph type\n */\nexport function getRichTypeByFlowGraphType(flowGraphType) {\n    switch (flowGraphType) {\n        case \"string\" /* FlowGraphTypes.String */:\n            return RichTypeString;\n        case \"number\" /* FlowGraphTypes.Number */:\n            return RichTypeNumber;\n        case \"boolean\" /* FlowGraphTypes.Boolean */:\n            return RichTypeBoolean;\n        case \"Vector2\" /* FlowGraphTypes.Vector2 */:\n            return RichTypeVector2;\n        case \"Vector3\" /* FlowGraphTypes.Vector3 */:\n            return RichTypeVector3;\n        case \"Vector4\" /* FlowGraphTypes.Vector4 */:\n            return RichTypeVector4;\n        case \"Matrix\" /* FlowGraphTypes.Matrix */:\n            return RichTypeMatrix;\n        case \"Color3\" /* FlowGraphTypes.Color3 */:\n            return RichTypeColor3;\n        case \"Color4\" /* FlowGraphTypes.Color4 */:\n            return RichTypeColor4;\n        case \"Quaternion\" /* FlowGraphTypes.Quaternion */:\n            return RichTypeQuaternion;\n        case \"FlowGraphInteger\" /* FlowGraphTypes.Integer */:\n            return RichTypeFlowGraphInteger;\n        case \"Matrix2D\" /* FlowGraphTypes.Matrix2D */:\n            return RichTypeMatrix2D;\n        case \"Matrix3D\" /* FlowGraphTypes.Matrix3D */:\n            return RichTypeMatrix3D;\n        default:\n            return RichTypeAny;\n    }\n}\n/**\n * get the animation type for a given flow graph type\n * @param flowGraphType the flow graph type\n * @returns the animation type for this flow graph type\n */\nexport function getAnimationTypeByFlowGraphType(flowGraphType) {\n    switch (flowGraphType) {\n        case \"number\" /* FlowGraphTypes.Number */:\n            return 0;\n        case \"Vector2\" /* FlowGraphTypes.Vector2 */:\n            return 5;\n        case \"Vector3\" /* FlowGraphTypes.Vector3 */:\n            return 1;\n        case \"Matrix\" /* FlowGraphTypes.Matrix */:\n            return 3;\n        case \"Color3\" /* FlowGraphTypes.Color3 */:\n            return 4;\n        case \"Color4\" /* FlowGraphTypes.Color4 */:\n            return 7;\n        case \"Quaternion\" /* FlowGraphTypes.Quaternion */:\n            return 2;\n        default:\n            return 0;\n    }\n}\n/**\n * Given an animation type, return the rich type that corresponds to it.\n * @param animationType the animation type\n * @returns the rich type that corresponds to the animation type\n */\nexport function getRichTypeByAnimationType(animationType) {\n    switch (animationType) {\n        case 0:\n            return RichTypeNumber;\n        case 5:\n            return RichTypeVector2;\n        case 1:\n            return RichTypeVector3;\n        case 3:\n            return RichTypeMatrix;\n        case 4:\n            return RichTypeColor3;\n        case 7:\n            return RichTypeColor4;\n        case 2:\n            return RichTypeQuaternion;\n        default:\n            return RichTypeAny;\n    }\n}\n//# sourceMappingURL=flowGraphRichTypes.js.map", "import { Logger } from \"@babylonjs/core/Misc/logger.js\";\nimport { getAnimationTypeByFlowGraphType } from \"@babylonjs/core/FlowGraph/flowGraphRichTypes.js\";\nexport function getMappingForFullOperationName(fullOperationName) {\n    const [op, extension] = fullOperationName.split(\":\");\n    return getMappingForDeclaration({ op, extension });\n}\nexport function getMappingForDeclaration(declaration, returnNoOpIfNotAvailable = true) {\n    const mapping = declaration.extension ? gltfExtensionsToFlowGraphMapping[declaration.extension]?.[declaration.op] : gltfToFlowGraphMapping[declaration.op];\n    if (!mapping) {\n        Logger.Warn(`No mapping found for operation ${declaration.op} and extension ${declaration.extension || \"KHR_interactivity\"}`);\n        if (returnNoOpIfNotAvailable) {\n            const inputs = {};\n            const outputs = {\n                flows: {},\n            };\n            if (declaration.inputValueSockets) {\n                inputs.values = {};\n                for (const key in declaration.inputValueSockets) {\n                    inputs.values[key] = {\n                        name: key,\n                    };\n                }\n            }\n            if (declaration.outputValueSockets) {\n                outputs.values = {};\n                Object.keys(declaration.outputValueSockets).forEach((key) => {\n                    outputs.values[key] = {\n                        name: key,\n                    };\n                });\n            }\n            return {\n                blocks: [], // no blocks, just mapping\n                inputs,\n                outputs,\n            };\n        }\n    }\n    return mapping;\n}\n/**\n * This function will add new mapping to glTF interactivity.\n * Other extensions can define new types of blocks, this is the way to let interactivity know how to parse them.\n * @param key the type of node, i.e. \"variable/get\"\n * @param extension the extension of the interactivity operation, i.e. \"KHR_selectability\"\n * @param mapping The mapping object. See documentation or examples below.\n */\nexport function addNewInteractivityFlowGraphMapping(key, extension, mapping) {\n    gltfExtensionsToFlowGraphMapping[extension] || (gltfExtensionsToFlowGraphMapping[extension] = {});\n    gltfExtensionsToFlowGraphMapping[extension][key] = mapping;\n}\nconst gltfExtensionsToFlowGraphMapping = {\n    /**\n     * This is the BABYLON extension for glTF interactivity.\n     * It defines babylon-specific blocks and operations.\n     */\n    BABYLON: {\n        /**\n         * flow/log is a flow node that logs input to the console.\n         * It has \"in\" and \"out\" flows, and takes a message as input.\n         * The message can be any type of value.\n         * The message is logged to the console when the \"in\" flow is triggered.\n         * The \"out\" flow is triggered when the message is logged.\n         */\n        \"flow/log\": {\n            blocks: [\"FlowGraphConsoleLogBlock\" /* FlowGraphBlockNames.ConsoleLog */],\n            inputs: {\n                values: {\n                    message: { name: \"message\" },\n                },\n            },\n        },\n    },\n};\n// this mapper is just a way to convert the glTF nodes to FlowGraph nodes in terms of input/output connection names and values.\nconst gltfToFlowGraphMapping = {\n    \"event/onStart\": {\n        blocks: [\"FlowGraphSceneReadyEventBlock\" /* FlowGraphBlockNames.SceneReadyEvent */],\n        outputs: {\n            flows: {\n                out: { name: \"done\" },\n            },\n        },\n    },\n    \"event/onTick\": {\n        blocks: [\"FlowGraphSceneTickEventBlock\" /* FlowGraphBlockNames.SceneTickEvent */],\n        inputs: {},\n        outputs: {\n            values: {\n                timeSinceLastTick: { name: \"deltaTime\", gltfType: \"number\" /*, dataTransformer: (time: number) => time / 1000*/ },\n            },\n            flows: {\n                out: { name: \"done\" },\n            },\n        },\n    },\n    \"event/send\": {\n        blocks: [\"FlowGraphSendCustomEventBlock\" /* FlowGraphBlockNames.SendCustomEvent */],\n        outputs: {\n            flows: {\n                out: { name: \"done\" },\n            },\n        },\n        extraProcessor(gltfBlock, declaration, _mapping, parser, serializedObjects) {\n            // set eventId and eventData. The configuration object of the glTF should have a single object.\n            // validate that we are running it on the right block.\n            if (declaration.op !== \"event/send\" || !gltfBlock.configuration || Object.keys(gltfBlock.configuration).length !== 1) {\n                throw new Error(\"Receive event should have a single configuration object, the event itself\");\n            }\n            const eventConfiguration = gltfBlock.configuration[\"event\"];\n            const eventId = eventConfiguration.value[0];\n            if (typeof eventId !== \"number\") {\n                throw new Error(\"Event id should be a number\");\n            }\n            const event = parser.arrays.events[eventId];\n            const serializedObject = serializedObjects[0];\n            serializedObject.config || (serializedObject.config = {});\n            serializedObject.config.eventId = event.eventId;\n            serializedObject.config.eventData = event.eventData;\n            return serializedObjects;\n        },\n    },\n    \"event/receive\": {\n        blocks: [\"FlowGraphReceiveCustomEventBlock\" /* FlowGraphBlockNames.ReceiveCustomEvent */],\n        outputs: {\n            flows: {\n                out: { name: \"done\" },\n            },\n        },\n        validation(gltfBlock, interactivityGraph) {\n            if (!gltfBlock.configuration) {\n                Logger.Error(\"Receive event should have a configuration object\");\n                return false;\n            }\n            const eventConfiguration = gltfBlock.configuration[\"event\"];\n            if (!eventConfiguration) {\n                Logger.Error(\"Receive event should have a single configuration object, the event itself\");\n                return false;\n            }\n            const eventId = eventConfiguration.value[0];\n            if (typeof eventId !== \"number\") {\n                Logger.Error(\"Event id should be a number\");\n                return false;\n            }\n            const event = interactivityGraph.events?.[eventId];\n            if (!event) {\n                Logger.Error(`Event with id ${eventId} not found`);\n                return false;\n            }\n            return true;\n        },\n        extraProcessor(gltfBlock, declaration, _mapping, parser, serializedObjects) {\n            // set eventId and eventData. The configuration object of the glTF should have a single object.\n            // validate that we are running it on the right block.\n            if (declaration.op !== \"event/receive\" || !gltfBlock.configuration || Object.keys(gltfBlock.configuration).length !== 1) {\n                throw new Error(\"Receive event should have a single configuration object, the event itself\");\n            }\n            const eventConfiguration = gltfBlock.configuration[\"event\"];\n            const eventId = eventConfiguration.value[0];\n            if (typeof eventId !== \"number\") {\n                throw new Error(\"Event id should be a number\");\n            }\n            const event = parser.arrays.events[eventId];\n            const serializedObject = serializedObjects[0];\n            serializedObject.config || (serializedObject.config = {});\n            serializedObject.config.eventId = event.eventId;\n            serializedObject.config.eventData = event.eventData;\n            return serializedObjects;\n        },\n    },\n    \"math/e\": getSimpleInputMapping(\"FlowGraphEBlock\" /* FlowGraphBlockNames.E */),\n    \"math/pi\": getSimpleInputMapping(\"FlowGraphPIBlock\" /* FlowGraphBlockNames.PI */),\n    \"math/inf\": getSimpleInputMapping(\"FlowGraphInfBlock\" /* FlowGraphBlockNames.Inf */),\n    \"math/nan\": getSimpleInputMapping(\"FlowGraphNaNBlock\" /* FlowGraphBlockNames.NaN */),\n    \"math/abs\": getSimpleInputMapping(\"FlowGraphAbsBlock\" /* FlowGraphBlockNames.Abs */),\n    \"math/sign\": getSimpleInputMapping(\"FlowGraphSignBlock\" /* FlowGraphBlockNames.Sign */),\n    \"math/trunc\": getSimpleInputMapping(\"FlowGraphTruncBlock\" /* FlowGraphBlockNames.Trunc */),\n    \"math/floor\": getSimpleInputMapping(\"FlowGraphFloorBlock\" /* FlowGraphBlockNames.Floor */),\n    \"math/ceil\": getSimpleInputMapping(\"FlowGraphCeilBlock\" /* FlowGraphBlockNames.Ceil */),\n    \"math/round\": {\n        blocks: [\"FlowGraphRoundBlock\" /* FlowGraphBlockNames.Round */],\n        configuration: {},\n        inputs: {\n            values: {\n                a: { name: \"a\" },\n            },\n        },\n        outputs: {\n            values: {\n                value: { name: \"value\" },\n            },\n        },\n        extraProcessor(gltfBlock, declaration, _mapping, parser, serializedObjects) {\n            // configure it to work the way glTF specifies\n            serializedObjects[0].config = serializedObjects[0].config || {};\n            serializedObjects[0].config.roundHalfAwayFromZero = true;\n            return serializedObjects;\n        },\n    },\n    \"math/fract\": getSimpleInputMapping(\"FlowGraphFractBlock\" /* FlowGraphBlockNames.Fraction */),\n    \"math/neg\": getSimpleInputMapping(\"FlowGraphNegationBlock\" /* FlowGraphBlockNames.Negation */),\n    \"math/add\": getSimpleInputMapping(\"FlowGraphAddBlock\" /* FlowGraphBlockNames.Add */, [\"a\", \"b\"], true),\n    \"math/sub\": getSimpleInputMapping(\"FlowGraphSubtractBlock\" /* FlowGraphBlockNames.Subtract */, [\"a\", \"b\"], true),\n    \"math/mul\": {\n        blocks: [\"FlowGraphMultiplyBlock\" /* FlowGraphBlockNames.Multiply */],\n        extraProcessor(_gltfBlock, _declaration, _mapping, _parser, serializedObjects) {\n            // configure it to work the way glTF specifies\n            serializedObjects[0].config = serializedObjects[0].config || {};\n            serializedObjects[0].config.useMatrixPerComponent = true;\n            // try to infer the type or fallback to Integer\n            // check the gltf block for the inputs, see if they have a type\n            let type = -1;\n            Object.keys(_gltfBlock.values || {}).find((value) => {\n                if (_gltfBlock.values?.[value].type !== undefined) {\n                    type = _gltfBlock.values[value].type;\n                    return true;\n                }\n                return false;\n            });\n            if (type !== -1) {\n                serializedObjects[0].config.type = _parser.arrays.types[type].flowGraphType;\n            }\n            return serializedObjects;\n        },\n    },\n    \"math/div\": getSimpleInputMapping(\"FlowGraphDivideBlock\" /* FlowGraphBlockNames.Divide */, [\"a\", \"b\"], true),\n    \"math/rem\": getSimpleInputMapping(\"FlowGraphModuloBlock\" /* FlowGraphBlockNames.Modulo */, [\"a\", \"b\"]),\n    \"math/min\": getSimpleInputMapping(\"FlowGraphMinBlock\" /* FlowGraphBlockNames.Min */, [\"a\", \"b\"]),\n    \"math/max\": getSimpleInputMapping(\"FlowGraphMaxBlock\" /* FlowGraphBlockNames.Max */, [\"a\", \"b\"]),\n    \"math/clamp\": getSimpleInputMapping(\"FlowGraphClampBlock\" /* FlowGraphBlockNames.Clamp */, [\"a\", \"b\", \"c\"]),\n    \"math/saturate\": getSimpleInputMapping(\"FlowGraphSaturateBlock\" /* FlowGraphBlockNames.Saturate */),\n    \"math/mix\": getSimpleInputMapping(\"FlowGraphMathInterpolationBlock\" /* FlowGraphBlockNames.MathInterpolation */, [\"a\", \"b\", \"c\"]),\n    \"math/eq\": getSimpleInputMapping(\"FlowGraphEqualityBlock\" /* FlowGraphBlockNames.Equality */, [\"a\", \"b\"]),\n    \"math/lt\": getSimpleInputMapping(\"FlowGraphLessThanBlock\" /* FlowGraphBlockNames.LessThan */, [\"a\", \"b\"]),\n    \"math/le\": getSimpleInputMapping(\"FlowGraphLessThanOrEqualBlock\" /* FlowGraphBlockNames.LessThanOrEqual */, [\"a\", \"b\"]),\n    \"math/gt\": getSimpleInputMapping(\"FlowGraphGreaterThanBlock\" /* FlowGraphBlockNames.GreaterThan */, [\"a\", \"b\"]),\n    \"math/ge\": getSimpleInputMapping(\"FlowGraphGreaterThanOrEqualBlock\" /* FlowGraphBlockNames.GreaterThanOrEqual */, [\"a\", \"b\"]),\n    \"math/isnan\": getSimpleInputMapping(\"FlowGraphIsNaNBlock\" /* FlowGraphBlockNames.IsNaN */),\n    \"math/isinf\": getSimpleInputMapping(\"FlowGraphIsInfBlock\" /* FlowGraphBlockNames.IsInfinity */),\n    \"math/select\": {\n        blocks: [\"FlowGraphConditionalBlock\" /* FlowGraphBlockNames.Conditional */],\n        inputs: {\n            values: {\n                condition: { name: \"condition\" },\n                // Should we validate those have the same type here, or assume it is already validated?\n                a: { name: \"onTrue\" },\n                b: { name: \"onFalse\" },\n            },\n        },\n        outputs: {\n            values: {\n                value: { name: \"output\" },\n            },\n        },\n    },\n    \"math/random\": {\n        blocks: [\"FlowGraphRandomBlock\" /* FlowGraphBlockNames.Random */],\n        outputs: {\n            values: {\n                value: { name: \"value\" },\n            },\n        },\n    },\n    \"math/sin\": getSimpleInputMapping(\"FlowGraphSinBlock\" /* FlowGraphBlockNames.Sin */),\n    \"math/cos\": getSimpleInputMapping(\"FlowGraphCosBlock\" /* FlowGraphBlockNames.Cos */),\n    \"math/tan\": getSimpleInputMapping(\"FlowGraphTanBlock\" /* FlowGraphBlockNames.Tan */),\n    \"math/asin\": getSimpleInputMapping(\"FlowGraphASinBlock\" /* FlowGraphBlockNames.Asin */),\n    \"math/acos\": getSimpleInputMapping(\"FlowGraphACosBlock\" /* FlowGraphBlockNames.Acos */),\n    \"math/atan\": getSimpleInputMapping(\"FlowGraphATanBlock\" /* FlowGraphBlockNames.Atan */),\n    \"math/atan2\": getSimpleInputMapping(\"FlowGraphATan2Block\" /* FlowGraphBlockNames.Atan2 */, [\"a\", \"b\"]),\n    \"math/sinh\": getSimpleInputMapping(\"FlowGraphSinhBlock\" /* FlowGraphBlockNames.Sinh */),\n    \"math/cosh\": getSimpleInputMapping(\"FlowGraphCoshBlock\" /* FlowGraphBlockNames.Cosh */),\n    \"math/tanh\": getSimpleInputMapping(\"FlowGraphTanhBlock\" /* FlowGraphBlockNames.Tanh */),\n    \"math/asinh\": getSimpleInputMapping(\"FlowGraphASinhBlock\" /* FlowGraphBlockNames.Asinh */),\n    \"math/acosh\": getSimpleInputMapping(\"FlowGraphACoshBlock\" /* FlowGraphBlockNames.Acosh */),\n    \"math/atanh\": getSimpleInputMapping(\"FlowGraphATanhBlock\" /* FlowGraphBlockNames.Atanh */),\n    \"math/exp\": getSimpleInputMapping(\"FlowGraphExponentialBlock\" /* FlowGraphBlockNames.Exponential */),\n    \"math/log\": getSimpleInputMapping(\"FlowGraphLogBlock\" /* FlowGraphBlockNames.Log */),\n    \"math/log2\": getSimpleInputMapping(\"FlowGraphLog2Block\" /* FlowGraphBlockNames.Log2 */),\n    \"math/log10\": getSimpleInputMapping(\"FlowGraphLog10Block\" /* FlowGraphBlockNames.Log10 */),\n    \"math/sqrt\": getSimpleInputMapping(\"FlowGraphSquareRootBlock\" /* FlowGraphBlockNames.SquareRoot */),\n    \"math/cbrt\": getSimpleInputMapping(\"FlowGraphCubeRootBlock\" /* FlowGraphBlockNames.CubeRoot */),\n    \"math/pow\": getSimpleInputMapping(\"FlowGraphPowerBlock\" /* FlowGraphBlockNames.Power */, [\"a\", \"b\"]),\n    \"math/length\": getSimpleInputMapping(\"FlowGraphLengthBlock\" /* FlowGraphBlockNames.Length */),\n    \"math/normalize\": getSimpleInputMapping(\"FlowGraphNormalizeBlock\" /* FlowGraphBlockNames.Normalize */),\n    \"math/dot\": getSimpleInputMapping(\"FlowGraphDotBlock\" /* FlowGraphBlockNames.Dot */, [\"a\", \"b\"]),\n    \"math/cross\": getSimpleInputMapping(\"FlowGraphCrossBlock\" /* FlowGraphBlockNames.Cross */, [\"a\", \"b\"]),\n    \"math/rotate2d\": getSimpleInputMapping(\"FlowGraphRotate2DBlock\" /* FlowGraphBlockNames.Rotate2D */, [\"a\", \"b\"]),\n    \"math/rotate3d\": getSimpleInputMapping(\"FlowGraphRotate3DBlock\" /* FlowGraphBlockNames.Rotate3D */, [\"a\", \"b\", \"c\"]),\n    \"math/transform\": {\n        // glTF transform is vectorN with matrixN\n        blocks: [\"FlowGraphTransformVectorBlock\" /* FlowGraphBlockNames.TransformVector */],\n        inputs: {\n            values: {\n                a: { name: \"a\" },\n                b: { name: \"b\" },\n            },\n        },\n        outputs: {\n            values: {\n                value: { name: \"value\" },\n            },\n        },\n    },\n    \"math/combine2\": {\n        blocks: [\"FlowGraphCombineVector2Block\" /* FlowGraphBlockNames.CombineVector2 */],\n        inputs: {\n            values: {\n                a: { name: \"input_0\", gltfType: \"number\" },\n                b: { name: \"input_1\", gltfType: \"number\" },\n            },\n        },\n        outputs: {\n            values: {\n                value: { name: \"value\" },\n            },\n        },\n    },\n    \"math/combine3\": {\n        blocks: [\"FlowGraphCombineVector3Block\" /* FlowGraphBlockNames.CombineVector3 */],\n        inputs: {\n            values: {\n                a: { name: \"input_0\", gltfType: \"number\" },\n                b: { name: \"input_1\", gltfType: \"number\" },\n                c: { name: \"input_2\", gltfType: \"number\" },\n            },\n        },\n        outputs: {\n            values: {\n                value: { name: \"value\" },\n            },\n        },\n    },\n    \"math/combine4\": {\n        blocks: [\"FlowGraphCombineVector4Block\" /* FlowGraphBlockNames.CombineVector4 */],\n        inputs: {\n            values: {\n                a: { name: \"input_0\", gltfType: \"number\" },\n                b: { name: \"input_1\", gltfType: \"number\" },\n                c: { name: \"input_2\", gltfType: \"number\" },\n                d: { name: \"input_3\", gltfType: \"number\" },\n            },\n        },\n        outputs: {\n            values: {\n                value: { name: \"value\" },\n            },\n        },\n    },\n    // one input, N outputs! outputs named using numbers.\n    \"math/extract2\": {\n        blocks: [\"FlowGraphExtractVector2Block\" /* FlowGraphBlockNames.ExtractVector2 */],\n        inputs: {\n            values: {\n                a: { name: \"input\", gltfType: \"number\" },\n            },\n        },\n        outputs: {\n            values: {\n                \"0\": { name: \"output_0\" },\n                \"1\": { name: \"output_1\" },\n            },\n        },\n    },\n    \"math/extract3\": {\n        blocks: [\"FlowGraphExtractVector3Block\" /* FlowGraphBlockNames.ExtractVector3 */],\n        inputs: {\n            values: {\n                a: { name: \"input\", gltfType: \"number\" },\n            },\n        },\n        outputs: {\n            values: {\n                \"0\": { name: \"output_0\" },\n                \"1\": { name: \"output_1\" },\n                \"2\": { name: \"output_2\" },\n            },\n        },\n    },\n    \"math/extract4\": {\n        blocks: [\"FlowGraphExtractVector4Block\" /* FlowGraphBlockNames.ExtractVector4 */],\n        inputs: {\n            values: {\n                a: { name: \"input\", gltfType: \"number\" },\n            },\n        },\n        outputs: {\n            values: {\n                \"0\": { name: \"output_0\" },\n                \"1\": { name: \"output_1\" },\n                \"2\": { name: \"output_2\" },\n                \"3\": { name: \"output_3\" },\n            },\n        },\n    },\n    \"math/transpose\": getSimpleInputMapping(\"FlowGraphTransposeBlock\" /* FlowGraphBlockNames.Transpose */),\n    \"math/determinant\": getSimpleInputMapping(\"FlowGraphDeterminantBlock\" /* FlowGraphBlockNames.Determinant */),\n    \"math/inverse\": getSimpleInputMapping(\"FlowGraphInvertMatrixBlock\" /* FlowGraphBlockNames.InvertMatrix */),\n    \"math/matmul\": getSimpleInputMapping(\"FlowGraphMatrixMultiplicationBlock\" /* FlowGraphBlockNames.MatrixMultiplication */, [\"a\", \"b\"]),\n    \"math/matCompose\": {\n        blocks: [\"FlowGraphMatrixCompose\" /* FlowGraphBlockNames.MatrixCompose */],\n        inputs: {\n            values: {\n                translation: { name: \"position\", gltfType: \"float3\" },\n                rotation: { name: \"rotationQuaternion\", gltfType: \"float4\" },\n                scale: { name: \"scaling\", gltfType: \"float3\" },\n            },\n        },\n        outputs: {\n            values: {\n                value: { name: \"value\" },\n            },\n        },\n        extraProcessor(_gltfBlock, _declaration, _mapping, _parser, serializedObjects, context) {\n            // configure it to work the way glTF specifies\n            const d = serializedObjects[0].dataInputs.find((input) => input.name === \"rotationQuaternion\");\n            if (!d) {\n                throw new Error(\"Rotation quaternion input not found\");\n            }\n            // if value is defined, set the type to quaternion\n            if (context._connectionValues[d.uniqueId]) {\n                context._connectionValues[d.uniqueId].type = \"Quaternion\" /* FlowGraphTypes.Quaternion */;\n            }\n            return serializedObjects;\n        },\n    },\n    \"math/matDecompose\": {\n        blocks: [\"FlowGraphMatrixDecompose\" /* FlowGraphBlockNames.MatrixDecompose */],\n        inputs: {\n            values: {\n                a: { name: \"input\", gltfType: \"number\" },\n            },\n        },\n        outputs: {\n            values: {\n                translation: { name: \"position\" },\n                rotation: { name: \"rotationQuaternion\" },\n                scale: { name: \"scaling\" },\n            },\n        },\n    },\n    \"math/combine2x2\": {\n        blocks: [\"FlowGraphCombineMatrix2DBlock\" /* FlowGraphBlockNames.CombineMatrix2D */],\n        inputs: {\n            values: {\n                a: { name: \"input_0\", gltfType: \"number\" },\n                b: { name: \"input_1\", gltfType: \"number\" },\n                c: { name: \"input_2\", gltfType: \"number\" },\n                d: { name: \"input_3\", gltfType: \"number\" },\n            },\n        },\n        outputs: {\n            values: {\n                value: { name: \"value\" },\n            },\n        },\n        extraProcessor(_gltfBlock, _declaration, _mapping, _parser, serializedObjects) {\n            // configure it to work the way glTF specifies\n            serializedObjects[0].config = serializedObjects[0].config || {};\n            serializedObjects[0].config.inputIsColumnMajor = true;\n            return serializedObjects;\n        },\n    },\n    \"math/extract2x2\": {\n        blocks: [\"FlowGraphExtractMatrix2DBlock\" /* FlowGraphBlockNames.ExtractMatrix2D */],\n        inputs: {\n            values: {\n                a: { name: \"input\", gltfType: \"float2x2\" },\n            },\n        },\n        outputs: {\n            values: {\n                \"0\": { name: \"output_0\" },\n                \"1\": { name: \"output_1\" },\n                \"2\": { name: \"output_2\" },\n                \"3\": { name: \"output_3\" },\n            },\n        },\n    },\n    \"math/combine3x3\": {\n        blocks: [\"FlowGraphCombineMatrix3DBlock\" /* FlowGraphBlockNames.CombineMatrix3D */],\n        inputs: {\n            values: {\n                a: { name: \"input_0\", gltfType: \"number\" },\n                b: { name: \"input_1\", gltfType: \"number\" },\n                c: { name: \"input_2\", gltfType: \"number\" },\n                d: { name: \"input_3\", gltfType: \"number\" },\n                e: { name: \"input_4\", gltfType: \"number\" },\n                f: { name: \"input_5\", gltfType: \"number\" },\n                g: { name: \"input_6\", gltfType: \"number\" },\n                h: { name: \"input_7\", gltfType: \"number\" },\n                i: { name: \"input_8\", gltfType: \"number\" },\n            },\n        },\n        outputs: {\n            values: {\n                value: { name: \"value\" },\n            },\n        },\n        extraProcessor(_gltfBlock, _declaration, _mapping, _parser, serializedObjects) {\n            // configure it to work the way glTF specifies\n            serializedObjects[0].config = serializedObjects[0].config || {};\n            serializedObjects[0].config.inputIsColumnMajor = true;\n            return serializedObjects;\n        },\n    },\n    \"math/extract3x3\": {\n        blocks: [\"FlowGraphExtractMatrix3DBlock\" /* FlowGraphBlockNames.ExtractMatrix3D */],\n        inputs: {\n            values: {\n                a: { name: \"input\", gltfType: \"float3x3\" },\n            },\n        },\n        outputs: {\n            values: {\n                \"0\": { name: \"output_0\" },\n                \"1\": { name: \"output_1\" },\n                \"2\": { name: \"output_2\" },\n                \"3\": { name: \"output_3\" },\n                \"4\": { name: \"output_4\" },\n                \"5\": { name: \"output_5\" },\n                \"6\": { name: \"output_6\" },\n                \"7\": { name: \"output_7\" },\n                \"8\": { name: \"output_8\" },\n            },\n        },\n    },\n    \"math/combine4x4\": {\n        blocks: [\"FlowGraphCombineMatrixBlock\" /* FlowGraphBlockNames.CombineMatrix */],\n        inputs: {\n            values: {\n                a: { name: \"input_0\", gltfType: \"number\" },\n                b: { name: \"input_1\", gltfType: \"number\" },\n                c: { name: \"input_2\", gltfType: \"number\" },\n                d: { name: \"input_3\", gltfType: \"number\" },\n                e: { name: \"input_4\", gltfType: \"number\" },\n                f: { name: \"input_5\", gltfType: \"number\" },\n                g: { name: \"input_6\", gltfType: \"number\" },\n                h: { name: \"input_7\", gltfType: \"number\" },\n                i: { name: \"input_8\", gltfType: \"number\" },\n                j: { name: \"input_9\", gltfType: \"number\" },\n                k: { name: \"input_10\", gltfType: \"number\" },\n                l: { name: \"input_11\", gltfType: \"number\" },\n                m: { name: \"input_12\", gltfType: \"number\" },\n                n: { name: \"input_13\", gltfType: \"number\" },\n                o: { name: \"input_14\", gltfType: \"number\" },\n                p: { name: \"input_15\", gltfType: \"number\" },\n            },\n        },\n        outputs: {\n            values: {\n                value: { name: \"value\" },\n            },\n        },\n        extraProcessor(_gltfBlock, _declaration, _mapping, _parser, serializedObjects) {\n            // configure it to work the way glTF specifies\n            serializedObjects[0].config = serializedObjects[0].config || {};\n            serializedObjects[0].config.inputIsColumnMajor = true;\n            return serializedObjects;\n        },\n    },\n    \"math/extract4x4\": {\n        blocks: [\"FlowGraphExtractMatrixBlock\" /* FlowGraphBlockNames.ExtractMatrix */],\n        configuration: {},\n        inputs: {\n            values: {\n                a: { name: \"input\", gltfType: \"number\" },\n            },\n        },\n        outputs: {\n            values: {\n                \"0\": { name: \"output_0\" },\n                \"1\": { name: \"output_1\" },\n                \"2\": { name: \"output_2\" },\n                \"3\": { name: \"output_3\" },\n                \"4\": { name: \"output_4\" },\n                \"5\": { name: \"output_5\" },\n                \"6\": { name: \"output_6\" },\n                \"7\": { name: \"output_7\" },\n                \"8\": { name: \"output_8\" },\n                \"9\": { name: \"output_9\" },\n                \"10\": { name: \"output_10\" },\n                \"11\": { name: \"output_11\" },\n                \"12\": { name: \"output_12\" },\n                \"13\": { name: \"output_13\" },\n                \"14\": { name: \"output_14\" },\n                \"15\": { name: \"output_15\" },\n            },\n        },\n    },\n    \"math/compose\": {\n        blocks: [\"FlowGraphMatrixCompose\" /* FlowGraphBlockNames.MatrixCompose */],\n        configuration: {},\n        inputs: {\n            values: {\n                translation: { name: \"position\", gltfType: \"float3\" },\n                rotation: { name: \"rotationQuaternion\", gltfType: \"float4\" },\n                scale: { name: \"scaling\", gltfType: \"float3\" },\n            },\n        },\n        outputs: {\n            values: {\n                value: { name: \"output\" },\n            },\n        },\n    },\n    \"math/decompose\": {\n        blocks: [\"FlowGraphMatrixDecompose\" /* FlowGraphBlockNames.MatrixDecompose */],\n        configuration: {},\n        inputs: {\n            values: {\n                a: { name: \"input\" },\n            },\n        },\n        outputs: {\n            values: {\n                translation: { name: \"position\" },\n                rotation: { name: \"rotationQuaternion\" },\n                scale: { name: \"scaling\" },\n            },\n        },\n    },\n    \"math/not\": {\n        blocks: [\"FlowGraphBitwiseNotBlock\" /* FlowGraphBlockNames.BitwiseNot */],\n        inputs: {\n            values: {\n                a: { name: \"a\" },\n            },\n        },\n        outputs: {\n            values: {\n                value: { name: \"value\" },\n            },\n        },\n        extraProcessor(_gltfBlock, _declaration, _mapping, _parser, serializedObjects, context) {\n            // configure it to work the way glTF specifies\n            serializedObjects[0].config = serializedObjects[0].config || {};\n            // try to infer the type or fallback to Integer\n            const socketIn = serializedObjects[0].dataInputs[0];\n            serializedObjects[0].config.valueType = context._connectionValues[socketIn.uniqueId]?.type ?? \"FlowGraphInteger\" /* FlowGraphTypes.Integer */;\n            return serializedObjects;\n        },\n    },\n    \"math/and\": {\n        blocks: [\"FlowGraphBitwiseAndBlock\" /* FlowGraphBlockNames.BitwiseAnd */],\n        inputs: {\n            values: {\n                a: { name: \"a\" },\n                b: { name: \"b\" },\n            },\n        },\n        outputs: {\n            values: {\n                value: { name: \"value\" },\n            },\n        },\n        extraProcessor(_gltfBlock, _declaration, _mapping, _parser, serializedObjects, context) {\n            // configure it to work the way glTF specifies\n            serializedObjects[0].config = serializedObjects[0].config || {};\n            // try to infer the type or fallback to Integer\n            const socketInA = serializedObjects[0].dataInputs[0];\n            const socketInB = serializedObjects[0].dataInputs[1];\n            serializedObjects[0].config.valueType =\n                context._connectionValues[socketInA.uniqueId]?.type ?? context._connectionValues[socketInB.uniqueId]?.type ?? \"FlowGraphInteger\" /* FlowGraphTypes.Integer */;\n            return serializedObjects;\n        },\n    },\n    \"math/or\": {\n        blocks: [\"FlowGraphBitwiseOrBlock\" /* FlowGraphBlockNames.BitwiseOr */],\n        inputs: {\n            values: {\n                a: { name: \"a\" },\n                b: { name: \"b\" },\n            },\n        },\n        outputs: {\n            values: {\n                value: { name: \"value\" },\n            },\n        },\n        extraProcessor(_gltfBlock, _declaration, _mapping, _parser, serializedObjects, context) {\n            // configure it to work the way glTF specifies\n            serializedObjects[0].config = serializedObjects[0].config || {};\n            // try to infer the type or fallback to Integer\n            const socketInA = serializedObjects[0].dataInputs[0];\n            const socketInB = serializedObjects[0].dataInputs[1];\n            serializedObjects[0].config.valueType =\n                context._connectionValues[socketInA.uniqueId]?.type ?? context._connectionValues[socketInB.uniqueId]?.type ?? \"FlowGraphInteger\" /* FlowGraphTypes.Integer */;\n            return serializedObjects;\n        },\n    },\n    \"math/xor\": {\n        blocks: [\"FlowGraphBitwiseXorBlock\" /* FlowGraphBlockNames.BitwiseXor */],\n        inputs: {\n            values: {\n                a: { name: \"a\" },\n                b: { name: \"b\" },\n            },\n        },\n        outputs: {\n            values: {\n                value: { name: \"value\" },\n            },\n        },\n        extraProcessor(_gltfBlock, _declaration, _mapping, _parser, serializedObjects, context) {\n            // configure it to work the way glTF specifies\n            serializedObjects[0].config = serializedObjects[0].config || {};\n            // try to infer the type or fallback to Integer\n            const socketInA = serializedObjects[0].dataInputs[0];\n            const socketInB = serializedObjects[0].dataInputs[1];\n            serializedObjects[0].config.valueType =\n                context._connectionValues[socketInA.uniqueId]?.type ?? context._connectionValues[socketInB.uniqueId]?.type ?? \"FlowGraphInteger\" /* FlowGraphTypes.Integer */;\n            return serializedObjects;\n        },\n    },\n    \"math/asr\": getSimpleInputMapping(\"FlowGraphBitwiseRightShiftBlock\" /* FlowGraphBlockNames.BitwiseRightShift */, [\"a\", \"b\"]),\n    \"math/lsl\": getSimpleInputMapping(\"FlowGraphBitwiseLeftShiftBlock\" /* FlowGraphBlockNames.BitwiseLeftShift */, [\"a\", \"b\"]),\n    \"math/clz\": getSimpleInputMapping(\"FlowGraphLeadingZerosBlock\" /* FlowGraphBlockNames.LeadingZeros */),\n    \"math/ctz\": getSimpleInputMapping(\"FlowGraphTrailingZerosBlock\" /* FlowGraphBlockNames.TrailingZeros */),\n    \"math/popcnt\": getSimpleInputMapping(\"FlowGraphOneBitsCounterBlock\" /* FlowGraphBlockNames.OneBitsCounter */),\n    \"math/rad\": getSimpleInputMapping(\"FlowGraphDegToRadBlock\" /* FlowGraphBlockNames.DegToRad */),\n    \"math/deg\": getSimpleInputMapping(\"FlowGraphRadToDegBlock\" /* FlowGraphBlockNames.RadToDeg */),\n    \"type/boolToInt\": getSimpleInputMapping(\"FlowGraphBooleanToInt\" /* FlowGraphBlockNames.BooleanToInt */),\n    \"type/boolToFloat\": getSimpleInputMapping(\"FlowGraphBooleanToFloat\" /* FlowGraphBlockNames.BooleanToFloat */),\n    \"type/intToBool\": getSimpleInputMapping(\"FlowGraphIntToBoolean\" /* FlowGraphBlockNames.IntToBoolean */),\n    \"type/intToFloat\": getSimpleInputMapping(\"FlowGraphIntToFloat\" /* FlowGraphBlockNames.IntToFloat */),\n    \"type/floatToInt\": getSimpleInputMapping(\"FlowGraphFloatToInt\" /* FlowGraphBlockNames.FloatToInt */),\n    \"type/floatToBool\": getSimpleInputMapping(\"FlowGraphFloatToBoolean\" /* FlowGraphBlockNames.FloatToBoolean */),\n    // flows\n    \"flow/sequence\": {\n        blocks: [\"FlowGraphSequenceBlock\" /* FlowGraphBlockNames.Sequence */],\n        extraProcessor(gltfBlock, _declaration, _mapping, _arrays, serializedObjects) {\n            const serializedObject = serializedObjects[0];\n            serializedObject.config || (serializedObject.config = {});\n            serializedObject.config.outputSignalCount = Object.keys(gltfBlock.flows || []).length;\n            serializedObject.signalOutputs.forEach((output, index) => {\n                output.name = \"out_\" + index;\n            });\n            return serializedObjects;\n        },\n    },\n    \"flow/branch\": {\n        blocks: [\"FlowGraphBranchBlock\" /* FlowGraphBlockNames.Branch */],\n        outputs: {\n            flows: {\n                true: { name: \"onTrue\" },\n                false: { name: \"onFalse\" },\n            },\n        },\n    },\n    \"flow/switch\": {\n        blocks: [\"FlowGraphSwitchBlock\" /* FlowGraphBlockNames.Switch */],\n        configuration: {\n            cases: { name: \"cases\", inOptions: true, defaultValue: [] },\n        },\n        inputs: {\n            values: {\n                selection: { name: \"case\" },\n            },\n        },\n        validation(gltfBlock) {\n            if (gltfBlock.configuration && gltfBlock.configuration.cases) {\n                const cases = gltfBlock.configuration.cases.value;\n                const onlyIntegers = cases.every((caseValue) => {\n                    // case value should be an integer. Since Number.isInteger(1.0) is true, we need to check if toString has only digits.\n                    return typeof caseValue === \"number\" && /^\\d+$/.test(caseValue.toString());\n                });\n                if (!onlyIntegers) {\n                    gltfBlock.configuration.cases.value = [];\n                    return true;\n                }\n                // check for duplicates\n                const uniqueCases = new Set(cases);\n                gltfBlock.configuration.cases.value = Array.from(uniqueCases);\n            }\n            return true;\n        },\n        extraProcessor(gltfBlock, declaration, _mapping, _arrays, serializedObjects) {\n            // convert all names of output flow to out_$1 apart from \"default\"\n            if (declaration.op !== \"flow/switch\" || !gltfBlock.flows || Object.keys(gltfBlock.flows).length === 0) {\n                throw new Error(\"Switch should have a single configuration object, the cases array\");\n            }\n            const serializedObject = serializedObjects[0];\n            serializedObject.signalOutputs.forEach((output) => {\n                if (output.name !== \"default\") {\n                    output.name = \"out_\" + output.name;\n                }\n            });\n            return serializedObjects;\n        },\n    },\n    \"flow/while\": {\n        blocks: [\"FlowGraphWhileLoopBlock\" /* FlowGraphBlockNames.WhileLoop */],\n        outputs: {\n            flows: {\n                loopBody: { name: \"executionFlow\" },\n            },\n        },\n    },\n    \"flow/for\": {\n        blocks: [\"FlowGraphForLoopBlock\" /* FlowGraphBlockNames.ForLoop */],\n        configuration: {\n            initialIndex: { name: \"initialIndex\", gltfType: \"number\", inOptions: true, defaultValue: 0 },\n        },\n        inputs: {\n            values: {\n                startIndex: { name: \"startIndex\", gltfType: \"number\" },\n                endIndex: { name: \"endIndex\", gltfType: \"number\" },\n            },\n        },\n        outputs: {\n            values: {\n                index: { name: \"index\" },\n            },\n            flows: {\n                loopBody: { name: \"executionFlow\" },\n            },\n        },\n    },\n    \"flow/doN\": {\n        blocks: [\"FlowGraphDoNBlock\" /* FlowGraphBlockNames.DoN */],\n        configuration: {},\n        inputs: {\n            values: {\n                n: { name: \"maxExecutions\", gltfType: \"number\" },\n            },\n        },\n        outputs: {\n            values: {\n                currentCount: { name: \"executionCount\" },\n            },\n        },\n    },\n    \"flow/multiGate\": {\n        blocks: [\"FlowGraphMultiGateBlock\" /* FlowGraphBlockNames.MultiGate */],\n        configuration: {\n            isRandom: { name: \"isRandom\", gltfType: \"boolean\", inOptions: true, defaultValue: false },\n            isLoop: { name: \"isLoop\", gltfType: \"boolean\", inOptions: true, defaultValue: false },\n        },\n        extraProcessor(gltfBlock, declaration, _mapping, _arrays, serializedObjects) {\n            if (declaration.op !== \"flow/multiGate\" || !gltfBlock.flows || Object.keys(gltfBlock.flows).length === 0) {\n                throw new Error(\"MultiGate should have a single configuration object, the number of output flows\");\n            }\n            const serializedObject = serializedObjects[0];\n            serializedObject.config || (serializedObject.config = {});\n            serializedObject.config.outputSignalCount = Object.keys(gltfBlock.flows).length;\n            serializedObject.signalOutputs.forEach((output, index) => {\n                output.name = \"out_\" + index;\n            });\n            return serializedObjects;\n        },\n    },\n    \"flow/waitAll\": {\n        blocks: [\"FlowGraphWaitAllBlock\" /* FlowGraphBlockNames.WaitAll */],\n        configuration: {\n            inputFlows: { name: \"inputSignalCount\", gltfType: \"number\", inOptions: true, defaultValue: 0 },\n        },\n        inputs: {\n            flows: {\n                \"[segment]\": { name: \"in_$1\" },\n            },\n        },\n        validation(gltfBlock) {\n            // check that the configuration value is an integer\n            if (typeof gltfBlock.configuration?.inputFlows?.value[0] !== \"number\") {\n                gltfBlock.configuration = gltfBlock.configuration || {\n                    inputFlows: { value: [0] },\n                };\n                gltfBlock.configuration.inputFlows.value = [0];\n            }\n            return true;\n        },\n    },\n    \"flow/throttle\": {\n        blocks: [\"FlowGraphThrottleBlock\" /* FlowGraphBlockNames.Throttle */],\n        outputs: {\n            flows: {\n                err: { name: \"error\" },\n            },\n        },\n    },\n    \"flow/setDelay\": {\n        blocks: [\"FlowGraphSetDelayBlock\" /* FlowGraphBlockNames.SetDelay */],\n        outputs: {\n            flows: {\n                err: { name: \"error\" },\n            },\n        },\n    },\n    \"flow/cancelDelay\": {\n        blocks: [\"FlowGraphCancelDelayBlock\" /* FlowGraphBlockNames.CancelDelay */],\n    },\n    \"variable/get\": {\n        blocks: [\"FlowGraphGetVariableBlock\" /* FlowGraphBlockNames.GetVariable */],\n        validation(gltfBlock) {\n            if (!gltfBlock.configuration?.variable?.value) {\n                Logger.Error(\"Variable get block should have a variable configuration\");\n                return false;\n            }\n            return true;\n        },\n        configuration: {\n            variable: {\n                name: \"variable\",\n                gltfType: \"number\",\n                flowGraphType: \"string\",\n                inOptions: true,\n                isVariable: true,\n                dataTransformer(index, parser) {\n                    return [parser.getVariableName(index[0])];\n                },\n            },\n        },\n    },\n    \"variable/set\": {\n        blocks: [\"FlowGraphSetVariableBlock\" /* FlowGraphBlockNames.SetVariable */],\n        configuration: {\n            variable: {\n                name: \"variable\",\n                gltfType: \"number\",\n                flowGraphType: \"string\",\n                inOptions: true,\n                isVariable: true,\n                dataTransformer(index, parser) {\n                    return [parser.getVariableName(index[0])];\n                },\n            },\n        },\n    },\n    \"variable/setMultiple\": {\n        blocks: [\"FlowGraphSetVariableBlock\" /* FlowGraphBlockNames.SetVariable */],\n        configuration: {\n            variables: {\n                name: \"variables\",\n                gltfType: \"number\",\n                flowGraphType: \"string\",\n                inOptions: true,\n                dataTransformer(index, parser) {\n                    return [index[0].map((i) => parser.getVariableName(i))];\n                },\n            },\n        },\n        extraProcessor(_gltfBlock, _declaration, _mapping, parser, serializedObjects) {\n            // variable/get configuration\n            const serializedGetVariable = serializedObjects[0];\n            serializedGetVariable.dataInputs.forEach((input) => {\n                input.name = parser.getVariableName(+input.name);\n            });\n            return serializedObjects;\n        },\n    },\n    \"variable/interpolate\": {\n        blocks: [\n            \"FlowGraphInterpolationBlock\" /* FlowGraphBlockNames.ValueInterpolation */,\n            \"FlowGraphContextBlock\" /* FlowGraphBlockNames.Context */,\n            \"FlowGraphPlayAnimationBlock\" /* FlowGraphBlockNames.PlayAnimation */,\n            \"FlowGraphBezierCurveEasing\" /* FlowGraphBlockNames.BezierCurveEasing */,\n            \"FlowGraphGetVariableBlock\" /* FlowGraphBlockNames.GetVariable */,\n        ],\n        configuration: {\n            variable: {\n                name: \"propertyName\",\n                inOptions: true,\n                isVariable: true,\n                dataTransformer(index, parser) {\n                    return [parser.getVariableName(index[0])];\n                },\n            },\n            useSlerp: {\n                name: \"animationType\",\n                inOptions: true,\n                defaultValue: false,\n                dataTransformer: (value) => {\n                    if (value[0] === true) {\n                        return [\"Quaternion\" /* FlowGraphTypes.Quaternion */];\n                    }\n                    else {\n                        return [undefined];\n                    }\n                },\n            },\n        },\n        inputs: {\n            values: {\n                value: { name: \"value_1\" },\n                duration: { name: \"duration_1\", gltfType: \"number\" },\n                p1: { name: \"controlPoint1\", toBlock: \"FlowGraphBezierCurveEasing\" /* FlowGraphBlockNames.BezierCurveEasing */ },\n                p2: { name: \"controlPoint2\", toBlock: \"FlowGraphBezierCurveEasing\" /* FlowGraphBlockNames.BezierCurveEasing */ },\n            },\n            flows: {\n                in: { name: \"in\", toBlock: \"FlowGraphPlayAnimationBlock\" /* FlowGraphBlockNames.PlayAnimation */ },\n            },\n        },\n        outputs: {\n            flows: {\n                err: { name: \"error\", toBlock: \"FlowGraphPlayAnimationBlock\" /* FlowGraphBlockNames.PlayAnimation */ },\n                out: { name: \"out\", toBlock: \"FlowGraphPlayAnimationBlock\" /* FlowGraphBlockNames.PlayAnimation */ },\n                done: { name: \"done\", toBlock: \"FlowGraphPlayAnimationBlock\" /* FlowGraphBlockNames.PlayAnimation */ },\n            },\n        },\n        interBlockConnectors: [\n            {\n                input: \"object\",\n                output: \"userVariables\",\n                inputBlockIndex: 2,\n                outputBlockIndex: 1,\n                isVariable: true,\n            },\n            {\n                input: \"animation\",\n                output: \"animation\",\n                inputBlockIndex: 2,\n                outputBlockIndex: 0,\n                isVariable: true,\n            },\n            {\n                input: \"easingFunction\",\n                output: \"easingFunction\",\n                inputBlockIndex: 0,\n                outputBlockIndex: 3,\n                isVariable: true,\n            },\n            {\n                input: \"value_0\",\n                output: \"value\",\n                inputBlockIndex: 0,\n                outputBlockIndex: 4,\n                isVariable: true,\n            },\n        ],\n        extraProcessor(gltfBlock, _declaration, _mapping, parser, serializedObjects) {\n            var _a, _b;\n            // is useSlerp is used, animationType should be set to be quaternion!\n            const serializedValueInterpolation = serializedObjects[0];\n            const propertyIndex = gltfBlock.configuration?.variable.value[0];\n            if (typeof propertyIndex !== \"number\") {\n                Logger.Error(\"Variable index is not defined for variable interpolation block\");\n                throw new Error(\"Variable index is not defined for variable interpolation block\");\n            }\n            const variable = parser.arrays.staticVariables[propertyIndex];\n            // if not set by useSlerp\n            if (typeof serializedValueInterpolation.config.animationType.value === \"undefined\") {\n                // get the value type\n                parser.arrays.staticVariables;\n                serializedValueInterpolation.config.animationType.value = getAnimationTypeByFlowGraphType(variable.type);\n            }\n            // variable/get configuration\n            const serializedGetVariable = serializedObjects[4];\n            serializedGetVariable.config || (serializedGetVariable.config = {});\n            (_a = serializedGetVariable.config).variable || (_a.variable = {});\n            serializedGetVariable.config.variable.value = parser.getVariableName(propertyIndex);\n            // get the control points from the easing block\n            (_b = serializedObjects[3]).config || (_b.config = {});\n            return serializedObjects;\n        },\n    },\n    \"pointer/get\": {\n        blocks: [\"FlowGraphGetPropertyBlock\" /* FlowGraphBlockNames.GetProperty */, \"FlowGraphJsonPointerParserBlock\" /* FlowGraphBlockNames.JsonPointerParser */],\n        configuration: {\n            pointer: { name: \"jsonPointer\", toBlock: \"FlowGraphJsonPointerParserBlock\" /* FlowGraphBlockNames.JsonPointerParser */ },\n        },\n        inputs: {\n            values: {\n                \"[segment]\": { name: \"$1\", toBlock: \"FlowGraphJsonPointerParserBlock\" /* FlowGraphBlockNames.JsonPointerParser */ },\n            },\n        },\n        interBlockConnectors: [\n            {\n                input: \"object\",\n                output: \"object\",\n                inputBlockIndex: 0,\n                outputBlockIndex: 1,\n                isVariable: true,\n            },\n            {\n                input: \"propertyName\",\n                output: \"propertyName\",\n                inputBlockIndex: 0,\n                outputBlockIndex: 1,\n                isVariable: true,\n            },\n            {\n                input: \"customGetFunction\",\n                output: \"getFunction\",\n                inputBlockIndex: 0,\n                outputBlockIndex: 1,\n                isVariable: true,\n            },\n        ],\n        extraProcessor(gltfBlock, _declaration, _mapping, parser, serializedObjects) {\n            serializedObjects.forEach((serializedObject) => {\n                // check if it is the json pointer block\n                if (serializedObject.className === \"FlowGraphJsonPointerParserBlock\" /* FlowGraphBlockNames.JsonPointerParser */) {\n                    serializedObject.config || (serializedObject.config = {});\n                    serializedObject.config.outputValue = true;\n                }\n            });\n            return serializedObjects;\n        },\n    },\n    \"pointer/set\": {\n        blocks: [\"FlowGraphSetPropertyBlock\" /* FlowGraphBlockNames.SetProperty */, \"FlowGraphJsonPointerParserBlock\" /* FlowGraphBlockNames.JsonPointerParser */],\n        configuration: {\n            pointer: { name: \"jsonPointer\", toBlock: \"FlowGraphJsonPointerParserBlock\" /* FlowGraphBlockNames.JsonPointerParser */ },\n        },\n        inputs: {\n            values: {\n                // must be defined due to the array taking over\n                value: { name: \"value\" },\n                \"[segment]\": { name: \"$1\", toBlock: \"FlowGraphJsonPointerParserBlock\" /* FlowGraphBlockNames.JsonPointerParser */ },\n            },\n        },\n        outputs: {\n            flows: {\n                err: { name: \"error\" },\n            },\n        },\n        interBlockConnectors: [\n            {\n                input: \"object\",\n                output: \"object\",\n                inputBlockIndex: 0,\n                outputBlockIndex: 1,\n                isVariable: true,\n            },\n            {\n                input: \"propertyName\",\n                output: \"propertyName\",\n                inputBlockIndex: 0,\n                outputBlockIndex: 1,\n                isVariable: true,\n            },\n            {\n                input: \"customSetFunction\",\n                output: \"setFunction\",\n                inputBlockIndex: 0,\n                outputBlockIndex: 1,\n                isVariable: true,\n            },\n        ],\n        extraProcessor(gltfBlock, _declaration, _mapping, parser, serializedObjects) {\n            serializedObjects.forEach((serializedObject) => {\n                // check if it is the json pointer block\n                if (serializedObject.className === \"FlowGraphJsonPointerParserBlock\" /* FlowGraphBlockNames.JsonPointerParser */) {\n                    serializedObject.config || (serializedObject.config = {});\n                    serializedObject.config.outputValue = true;\n                }\n            });\n            return serializedObjects;\n        },\n    },\n    \"pointer/interpolate\": {\n        // interpolate, parse the pointer and play the animation generated. 3 blocks!\n        blocks: [\"FlowGraphInterpolationBlock\" /* FlowGraphBlockNames.ValueInterpolation */, \"FlowGraphJsonPointerParserBlock\" /* FlowGraphBlockNames.JsonPointerParser */, \"FlowGraphPlayAnimationBlock\" /* FlowGraphBlockNames.PlayAnimation */, \"FlowGraphEasingBlock\" /* FlowGraphBlockNames.Easing */],\n        configuration: {\n            pointer: { name: \"jsonPointer\", toBlock: \"FlowGraphJsonPointerParserBlock\" /* FlowGraphBlockNames.JsonPointerParser */ },\n        },\n        inputs: {\n            values: {\n                value: { name: \"value_1\" },\n                \"[segment]\": { name: \"$1\", toBlock: \"FlowGraphJsonPointerParserBlock\" /* FlowGraphBlockNames.JsonPointerParser */ },\n                duration: { name: \"duration_1\", gltfType: \"number\" /*, inOptions: true */ },\n                p1: { name: \"controlPoint1\", toBlock: \"FlowGraphEasingBlock\" /* FlowGraphBlockNames.Easing */ },\n                p2: { name: \"controlPoint2\", toBlock: \"FlowGraphEasingBlock\" /* FlowGraphBlockNames.Easing */ },\n            },\n            flows: {\n                in: { name: \"in\", toBlock: \"FlowGraphPlayAnimationBlock\" /* FlowGraphBlockNames.PlayAnimation */ },\n            },\n        },\n        outputs: {\n            flows: {\n                err: { name: \"error\", toBlock: \"FlowGraphPlayAnimationBlock\" /* FlowGraphBlockNames.PlayAnimation */ },\n                out: { name: \"out\", toBlock: \"FlowGraphPlayAnimationBlock\" /* FlowGraphBlockNames.PlayAnimation */ },\n                done: { name: \"done\", toBlock: \"FlowGraphPlayAnimationBlock\" /* FlowGraphBlockNames.PlayAnimation */ },\n            },\n        },\n        interBlockConnectors: [\n            {\n                input: \"object\",\n                output: \"object\",\n                inputBlockIndex: 2,\n                outputBlockIndex: 1,\n                isVariable: true,\n            },\n            {\n                input: \"propertyName\",\n                output: \"propertyName\",\n                inputBlockIndex: 0,\n                outputBlockIndex: 1,\n                isVariable: true,\n            },\n            {\n                input: \"customBuildAnimation\",\n                output: \"generateAnimationsFunction\",\n                inputBlockIndex: 0,\n                outputBlockIndex: 1,\n                isVariable: true,\n            },\n            {\n                input: \"animation\",\n                output: \"animation\",\n                inputBlockIndex: 2,\n                outputBlockIndex: 0,\n                isVariable: true,\n            },\n            {\n                input: \"easingFunction\",\n                output: \"easingFunction\",\n                inputBlockIndex: 0,\n                outputBlockIndex: 3,\n                isVariable: true,\n            },\n            {\n                input: \"value_0\",\n                output: \"value\",\n                inputBlockIndex: 0,\n                outputBlockIndex: 1,\n                isVariable: true,\n            },\n        ],\n        extraProcessor(gltfBlock, _declaration, _mapping, parser, serializedObjects) {\n            serializedObjects.forEach((serializedObject) => {\n                // check if it is the json pointer block\n                if (serializedObject.className === \"FlowGraphJsonPointerParserBlock\" /* FlowGraphBlockNames.JsonPointerParser */) {\n                    serializedObject.config || (serializedObject.config = {});\n                    serializedObject.config.outputValue = true;\n                }\n                else if (serializedObject.className === \"FlowGraphInterpolationBlock\" /* FlowGraphBlockNames.ValueInterpolation */) {\n                    serializedObject.config || (serializedObject.config = {});\n                    Object.keys(gltfBlock.values || []).forEach((key) => {\n                        const value = gltfBlock.values?.[key];\n                        if (key === \"value\" && value) {\n                            // get the type of the value\n                            const type = value.type;\n                            if (type !== undefined) {\n                                serializedObject.config.animationType = parser.arrays.types[type].flowGraphType;\n                            }\n                        }\n                    });\n                }\n            });\n            return serializedObjects;\n        },\n    },\n    \"animation/start\": {\n        blocks: [\"FlowGraphPlayAnimationBlock\" /* FlowGraphBlockNames.PlayAnimation */, \"FlowGraphArrayIndexBlock\" /* FlowGraphBlockNames.ArrayIndex */, \"KHR_interactivity/FlowGraphGLTFDataProvider\"],\n        inputs: {\n            values: {\n                animation: { name: \"index\", gltfType: \"number\", toBlock: \"FlowGraphArrayIndexBlock\" /* FlowGraphBlockNames.ArrayIndex */ },\n                speed: { name: \"speed\", gltfType: \"number\" },\n                // 60 is a const from the glTF loader\n                startTime: { name: \"from\", gltfType: \"number\", dataTransformer: (time, parser) => [time[0] * parser._loader.parent.targetFps] },\n                endTime: { name: \"to\", gltfType: \"number\", dataTransformer: (time, parser) => [time[0] * parser._loader.parent.targetFps] },\n            },\n        },\n        outputs: {\n            flows: {\n                err: { name: \"error\" },\n            },\n        },\n        interBlockConnectors: [\n            {\n                input: \"animationGroup\",\n                output: \"value\",\n                inputBlockIndex: 0,\n                outputBlockIndex: 1,\n                isVariable: true,\n            },\n            {\n                input: \"array\",\n                output: \"animationGroups\",\n                inputBlockIndex: 1,\n                outputBlockIndex: 2,\n                isVariable: true,\n            },\n        ],\n        extraProcessor(_gltfBlock, _declaration, _mapping, _arrays, serializedObjects, _context, globalGLTF) {\n            // add the glTF to the configuration of the last serialized object\n            const serializedObject = serializedObjects[serializedObjects.length - 1];\n            serializedObject.config || (serializedObject.config = {});\n            serializedObject.config.glTF = globalGLTF;\n            return serializedObjects;\n        },\n    },\n    \"animation/stop\": {\n        blocks: [\"FlowGraphStopAnimationBlock\" /* FlowGraphBlockNames.StopAnimation */, \"FlowGraphArrayIndexBlock\" /* FlowGraphBlockNames.ArrayIndex */, \"KHR_interactivity/FlowGraphGLTFDataProvider\"],\n        inputs: {\n            values: {\n                animation: { name: \"index\", gltfType: \"number\", toBlock: \"FlowGraphArrayIndexBlock\" /* FlowGraphBlockNames.ArrayIndex */ },\n            },\n        },\n        outputs: {\n            flows: {\n                err: { name: \"error\" },\n            },\n        },\n        interBlockConnectors: [\n            {\n                input: \"animationGroup\",\n                output: \"value\",\n                inputBlockIndex: 0,\n                outputBlockIndex: 1,\n                isVariable: true,\n            },\n            {\n                input: \"array\",\n                output: \"animationGroups\",\n                inputBlockIndex: 1,\n                outputBlockIndex: 2,\n                isVariable: true,\n            },\n        ],\n        extraProcessor(_gltfBlock, _declaration, _mapping, _arrays, serializedObjects, _context, globalGLTF) {\n            // add the glTF to the configuration of the last serialized object\n            const serializedObject = serializedObjects[serializedObjects.length - 1];\n            serializedObject.config || (serializedObject.config = {});\n            serializedObject.config.glTF = globalGLTF;\n            return serializedObjects;\n        },\n    },\n    \"animation/stopAt\": {\n        blocks: [\"FlowGraphStopAnimationBlock\" /* FlowGraphBlockNames.StopAnimation */, \"FlowGraphArrayIndexBlock\" /* FlowGraphBlockNames.ArrayIndex */, \"KHR_interactivity/FlowGraphGLTFDataProvider\"],\n        configuration: {},\n        inputs: {\n            values: {\n                animation: { name: \"index\", gltfType: \"number\", toBlock: \"FlowGraphArrayIndexBlock\" /* FlowGraphBlockNames.ArrayIndex */ },\n                stopTime: { name: \"stopAtFrame\", gltfType: \"number\", dataTransformer: (time, parser) => [time[0] * parser._loader.parent.targetFps] },\n            },\n        },\n        outputs: {\n            flows: {\n                err: { name: \"error\" },\n            },\n        },\n        interBlockConnectors: [\n            {\n                input: \"animationGroup\",\n                output: \"value\",\n                inputBlockIndex: 0,\n                outputBlockIndex: 1,\n                isVariable: true,\n            },\n            {\n                input: \"array\",\n                output: \"animationGroups\",\n                inputBlockIndex: 1,\n                outputBlockIndex: 2,\n                isVariable: true,\n            },\n        ],\n        extraProcessor(_gltfBlock, _declaration, _mapping, _arrays, serializedObjects, _context, globalGLTF) {\n            // add the glTF to the configuration of the last serialized object\n            const serializedObject = serializedObjects[serializedObjects.length - 1];\n            serializedObject.config || (serializedObject.config = {});\n            serializedObject.config.glTF = globalGLTF;\n            return serializedObjects;\n        },\n    },\n    \"math/switch\": {\n        blocks: [\"FlowGraphDataSwitchBlock\" /* FlowGraphBlockNames.DataSwitch */],\n        configuration: {\n            cases: { name: \"cases\", inOptions: true, defaultValue: [] },\n        },\n        inputs: {\n            values: {\n                selection: { name: \"case\" },\n            },\n        },\n        validation(gltfBlock) {\n            if (gltfBlock.configuration && gltfBlock.configuration.cases) {\n                const cases = gltfBlock.configuration.cases.value;\n                const onlyIntegers = cases.every((caseValue) => {\n                    // case value should be an integer. Since Number.isInteger(1.0) is true, we need to check if toString has only digits.\n                    return typeof caseValue === \"number\" && /^\\d+$/.test(caseValue.toString());\n                });\n                if (!onlyIntegers) {\n                    gltfBlock.configuration.cases.value = [];\n                    return true;\n                }\n                // check for duplicates\n                const uniqueCases = new Set(cases);\n                gltfBlock.configuration.cases.value = Array.from(uniqueCases);\n            }\n            return true;\n        },\n        extraProcessor(_gltfBlock, _declaration, _mapping, _arrays, serializedObjects) {\n            const serializedObject = serializedObjects[0];\n            serializedObject.dataInputs.forEach((input) => {\n                if (input.name !== \"default\" && input.name !== \"case\") {\n                    input.name = \"in_\" + input.name;\n                }\n            });\n            return serializedObjects;\n        },\n    },\n    \"debug/log\": {\n        blocks: [\"FlowGraphConsoleLogBlock\" /* FlowGraphBlockNames.ConsoleLog */],\n        configuration: {\n            message: { name: \"messageTemplate\", inOptions: true },\n        },\n    },\n};\nfunction getSimpleInputMapping(type, inputs = [\"a\"], inferType) {\n    return {\n        blocks: [type],\n        inputs: {\n            values: inputs.reduce((acc, input) => {\n                acc[input] = { name: input };\n                return acc;\n            }, {}),\n        },\n        outputs: {\n            values: {\n                value: { name: \"value\" },\n            },\n        },\n        extraProcessor(_gltfBlock, _declaration, _mapping, _parser, serializedObjects) {\n            if (inferType) {\n                // configure it to work the way glTF specifies\n                serializedObjects[0].config = serializedObjects[0].config || {};\n                // try to infer the type or fallback to Integer\n                // check the gltf block for the inputs, see if they have a type\n                let type = -1;\n                Object.keys(_gltfBlock.values || {}).find((value) => {\n                    if (_gltfBlock.values?.[value].type !== undefined) {\n                        type = _gltfBlock.values[value].type;\n                        return true;\n                    }\n                    return false;\n                });\n                if (type !== -1) {\n                    serializedObjects[0].config.type = _parser.arrays.types[type].flowGraphType;\n                }\n            }\n            return serializedObjects;\n        },\n    };\n}\nexport function getAllSupportedNativeNodeTypes() {\n    return Object.keys(gltfToFlowGraphMapping);\n}\n/**\n *\n * These are the nodes from the specs:\n\n### Math Nodes\n1. **Constants**\n   - E (`math/e`) FlowGraphBlockNames.E\n   - Pi (`math/pi`) FlowGraphBlockNames.PI\n   - Infinity (`math/inf`) FlowGraphBlockNames.Inf\n   - Not a Number (`math/nan`) FlowGraphBlockNames.NaN\n2. **Arithmetic Nodes**\n   - Absolute Value (`math/abs`) FlowGraphBlockNames.Abs\n   - Sign (`math/sign`) FlowGraphBlockNames.Sign\n   - Truncate (`math/trunc`) FlowGraphBlockNames.Trunc\n   - Floor (`math/floor`) FlowGraphBlockNames.Floor\n   - Ceil (`math/ceil`) FlowGraphBlockNames.Ceil\n   - Round (`math/round`)  FlowGraphBlockNames.Round\n   - Fraction (`math/fract`) FlowGraphBlockNames.Fract\n   - Negation (`math/neg`) FlowGraphBlockNames.Negation\n   - Addition (`math/add`) FlowGraphBlockNames.Add\n   - Subtraction (`math/sub`) FlowGraphBlockNames.Subtract\n   - Multiplication (`math/mul`) FlowGraphBlockNames.Multiply\n   - Division (`math/div`) FlowGraphBlockNames.Divide\n   - Remainder (`math/rem`) FlowGraphBlockNames.Modulo\n   - Minimum (`math/min`) FlowGraphBlockNames.Min\n   - Maximum (`math/max`) FlowGraphBlockNames.Max\n   - Clamp (`math/clamp`) FlowGraphBlockNames.Clamp\n   - Saturate (`math/saturate`) FlowGraphBlockNames.Saturate\n   - Interpolate (`math/mix`) FlowGraphBlockNames.MathInterpolation\n3. **Comparison Nodes**\n   - Equality (`math/eq`) FlowGraphBlockNames.Equality\n   - Less Than (`math/lt`) FlowGraphBlockNames.LessThan\n   - Less Than Or Equal To (`math/le`) FlowGraphBlockNames.LessThanOrEqual\n   - Greater Than (`math/gt`) FlowGraphBlockNames.GreaterThan\n   - Greater Than Or Equal To (`math/ge`) FlowGraphBlockNames.GreaterThanOrEqual\n4. **Special Nodes**\n   - Is Not a Number (`math/isnan`) FlowGraphBlockNames.IsNaN\n   - Is Infinity (`math/isinf`) FlowGraphBlockNames.IsInfinity\n   - Select (`math/select`) FlowGraphBlockNames.Conditional\n   - Random (`math/random`) FlowGraphBlockNames.Random\n5. **Angle and Trigonometry Nodes**\n   - Degrees-To-Radians (`math/rad`) FlowGraphBlockNames.DegToRad\n   - Radians-To-Degrees (`math/deg`) FlowGraphBlockNames.RadToDeg\n   - Sine (`math/sin`)  FlowGraphBlockNames.Sin\n   - Cosine (`math/cos`) FlowGraphBlockNames.Cos\n   - Tangent (`math/tan`) FlowGraphBlockNames.Tan\n   - Arcsine (`math/asin`) FlowGraphBlockNames.Asin\n   - Arccosine (`math/acos`) FlowGraphBlockNames.Acos\n   - Arctangent (`math/atan`) FlowGraphBlockNames.Atan\n   - Arctangent 2 (`math/atan2`) FlowGraphBlockNames.Atan2\n6. **Hyperbolic Nodes**\n   - Hyperbolic Sine (`math/sinh`) FlowGraphBlockNames.Sinh\n   - Hyperbolic Cosine (`math/cosh`) FlowGraphBlockNames.Cosh\n   - Hyperbolic Tangent (`math/tanh`) FlowGraphBlockNames.Tanh\n   - Inverse Hyperbolic Sine (`math/asinh`) FlowGraphBlockNames.Asinh\n   - Inverse Hyperbolic Cosine (`math/acosh`) FlowGraphBlockNames.Acosh\n   - Inverse Hyperbolic Tangent (`math/atanh`) FlowGraphBlockNames.Atanh\n7. **Exponential Nodes**\n   - Exponent (`math/exp`) FlowGraphBlockNames.Exponential\n   - Natural Logarithm (`math/log`) FlowGraphBlockNames.Log\n   - Base-2 Logarithm (`math/log2`) FlowGraphBlockNames.Log2\n   - Base-10 Logarithm (`math/log10`) FlowGraphBlockNames.Log10\n   - Square Root (`math/sqrt`) FlowGraphBlockNames.SquareRoot\n   - Cube Root (`math/cbrt`) FlowGraphBlockNames.CubeRoot\n   - Power (`math/pow`) FlowGraphBlockNames.Power\n8. **Vector Nodes**\n   - Length (`math/length`) FlowGraphBlockNames.Length\n   - Normalize (`math/normalize`) FlowGraphBlockNames.Normalize\n   - Dot Product (`math/dot`) FlowGraphBlockNames.Dot\n   - Cross Product (`math/cross`) FlowGraphBlockNames.Cross\n   - Rotate 2D (`math/rotate2d`) FlowGraphBlockNames.Rotate2D\n   - Rotate 3D (`math/rotate3d`) FlowGraphBlockNames.Rotate3D\n   - Transform (`math/transform`) FlowGraphBlockNames.TransformVector\n9. **Matrix Nodes**\n   - Transpose (`math/transpose`) FlowGraphBlockNames.Transpose\n   - Determinant (`math/determinant`) FlowGraphBlockNames.Determinant\n   - Inverse (`math/inverse`) FlowGraphBlockNames.InvertMatrix\n   - Multiplication (`math/matmul`) FlowGraphBlockNames.MatrixMultiplication\n10. **Swizzle Nodes**\n    - Combine (`math/combine2`, `math/combine3`, `math/combine4`, `math/combine2x2`, `math/combine3x3`, `math/combine4x4`)\n        FlowGraphBlockNames.CombineVector2, FlowGraphBlockNames.CombineVector3, FlowGraphBlockNames.CombineVector4\n        FlowGraphBlockNames.CombineMatrix2D, FlowGraphBlockNames.CombineMatrix3D, FlowGraphBlockNames.CombineMatrix\n    - Extract (`math/extract2`, `math/extract3`, `math/extract4`, `math/extract2x2`, `math/extract3x3`, `math/extract4x4`)\n        FlowGraphBlockNames.ExtractVector2, FlowGraphBlockNames.ExtractVector3, FlowGraphBlockNames.ExtractVector4\n        FlowGraphBlockNames.ExtractMatrix2D, FlowGraphBlockNames.ExtractMatrix3D, FlowGraphBlockNames.ExtractMatrix\n11. **Integer Arithmetic Nodes**\n    - Absolute Value (`math/abs`) FlowGraphBlockNames.Abs\n    - Sign (`math/sign`) FlowGraphBlockNames.Sign\n    - Negation (`math/neg`) FlowGraphBlockNames.Negation\n    - Addition (`math/add`) FlowGraphBlockNames.Add\n    - Subtraction (`math/sub`) FlowGraphBlockNames.Subtract\n    - Multiplication (`math/mul`) FlowGraphBlockNames.Multiply\n    - Division (`math/div`) FlowGraphBlockNames.Divide\n    - Remainder (`math/rem`) FlowGraphBlockNames.Modulo\n    - Minimum (`math/min`) FlowGraphBlockNames.Min\n    - Maximum (`math/max`) FlowGraphBlockNames.Max\n    - Clamp (`math/clamp`) FlowGraphBlockNames.Clamp\n12. **Integer Comparison Nodes**\n    - Equality (`math/eq`) FlowGraphBlockNames.Equality\n    - Less Than (`math/lt`) FlowGraphBlockNames.LessThan\n    - Less Than Or Equal To (`math/le`) FlowGraphBlockNames.LessThanOrEqual\n    - Greater Than (`math/gt`) FlowGraphBlockNames.GreaterThan\n    - Greater Than Or Equal To (`math/ge`) FlowGraphBlockNames.GreaterThanOrEqual\n13. **Integer Bitwise Nodes**\n    - Bitwise NOT (`math/not`) FlowGraphBlockNames.BitwiseNot\n    - Bitwise AND (`math/and`) FlowGraphBlockNames.BitwiseAnd\n    - Bitwise OR (`math/or`) FlowGraphBlockNames.BitwiseOr\n    - Bitwise XOR (`math/xor`) FlowGraphBlockNames.BitwiseXor\n    - Right Shift (`math/asr`) FlowGraphBlockNames.BitwiseRightShift\n    - Left Shift (`math/lsl`) FlowGraphBlockNames.BitwiseLeftShift\n    - Count Leading Zeros (`math/clz`) FlowGraphBlockNames.LeadingZeros\n    - Count Trailing Zeros (`math/ctz`) FlowGraphBlockNames.TrailingZeros\n    - Count One Bits (`math/popcnt`) FlowGraphBlockNames.OneBitsCounter\n14. **Boolean Arithmetic Nodes**\n    - Equality (`math/eq`) FlowGraphBlockNames.Equality\n    - Boolean NOT (`math/not`) FlowGraphBlockNames.BitwiseNot\n    - Boolean AND (`math/and`) FlowGraphBlockNames.BitwiseAnd\n    - Boolean OR (`math/or`) FlowGraphBlockNames.BitwiseOr\n    - Boolean XOR (`math/xor`) FlowGraphBlockNames.BitwiseXor\n\n### Type Conversion Nodes\n1. **Boolean Conversion Nodes**\n   - Boolean to Integer (`type/boolToInt`) FlowGraphBlockNames.BooleanToInt\n   - Boolean to Float (`type/boolToFloat`) FlowGraphBlockNames.BooleanToFloat\n2. **Integer Conversion Nodes**\n   - Integer to Boolean (`type/intToBool`) FlowGraphBlockNames.IntToBoolean\n   - Integer to Float (`type/intToFloat`) FlowGraphBlockNames.IntToFloat\n3. **Float Conversion Nodes**\n   - Float to Boolean (`type/floatToBool`) FlowGraphBlockNames.FloatToBoolean\n   - Float to Integer (`type/floatToInt`) FlowGraphBlockNames.FloatToInt\n\n### Control Flow Nodes\n1. **Sync Nodes**\n   - Sequence (`flow/sequence`) FlowGraphBlockNames.Sequence\n   - Branch (`flow/branch`) FlowGraphBlockNames.Branch\n   - Switch (`flow/switch`) FlowGraphBlockNames.Switch\n   - While Loop (`flow/while`) FlowGraphBlockNames.WhileLoop\n   - For Loop (`flow/for`) FlowGraphBlockNames.ForLoop\n   - Do N (`flow/doN`) FlowGraphBlockNames.DoN\n   - Multi Gate (`flow/multiGate`) FlowGraphBlockNames.MultiGate\n   - Wait All (`flow/waitAll`) FlowGraphBlockNames.WaitAll\n   - Throttle (`flow/throttle`) FlowGraphBlockNames.Throttle\n2. **Delay Nodes**\n   - Set Delay (`flow/setDelay`) FlowGraphBlockNames.SetDelay\n   - Cancel Delay (`flow/cancelDelay`) FlowGraphBlockNames.CancelDelay\n\n### State Manipulation Nodes\n1. **Custom Variable Access**\n   - Variable Get (`variable/get`) FlowGraphBlockNames.GetVariable\n   - Variable Set (`variable/set`) FlowGraphBlockNames.SetVariable\n   - Variable Interpolate (`variable/interpolate`)\n2. **Object Model Access** // TODO fully test this!!!\n   - JSON Pointer Template Parsing (`pointer/get`) [FlowGraphBlockNames.GetProperty, FlowGraphBlockNames.JsonPointerParser]\n   - Effective JSON Pointer Generation (`pointer/set`) [FlowGraphBlockNames.SetProperty, FlowGraphBlockNames.JsonPointerParser]\n   - Pointer Get (`pointer/get`) [FlowGraphBlockNames.GetProperty, FlowGraphBlockNames.JsonPointerParser]\n   - Pointer Set (`pointer/set`) [FlowGraphBlockNames.SetProperty, FlowGraphBlockNames.JsonPointerParser]\n   - Pointer Interpolate (`pointer/interpolate`) [FlowGraphBlockNames.ValueInterpolation, FlowGraphBlockNames.JsonPointerParser, FlowGraphBlockNames.PlayAnimation, FlowGraphBlockNames.Easing]\n\n### Animation Control Nodes\n1. **Animation Play** (`animation/start`) FlowGraphBlockNames.PlayAnimation\n2. **Animation Stop** (`animation/stop`) FlowGraphBlockNames.StopAnimation\n3. **Animation Stop At** (`animation/stopAt`) FlowGraphBlockNames.StopAnimation\n\n### Event Nodes\n1. **Lifecycle Event Nodes**\n   - On Start (`event/onStart`) FlowGraphBlockNames.SceneReadyEvent\n   - On Tick (`event/onTick`) FlowGraphBlockNames.SceneTickEvent\n2. **Custom Event Nodes**\n   - Receive (`event/receive`) FlowGraphBlockNames.ReceiveCustomEvent\n   - Send (`event/send`) FlowGraphBlockNames.SendCustomEvent\n\n */\n//# sourceMappingURL=declarationMapper.js.map"], "names": ["FlowGraphInteger", "value", "n", "other", "RegisterClass", "FlowGraphMatrix2D", "m", "v", "Vector2", "result", "emptyArray", "i", "array", "otherMatrix", "thisMatrix", "o", "det", "invDet", "epsilon", "FlowGraphMatrix3D", "Vector3", "FlowGraphTypes", "RichType", "typeName", "defaultValue", "animationType", "serializationObject", "RichTypeAny", "RichTypeString", "RichTypeNumber", "RichTypeBoolean", "RichTypeVector2", "RichTypeVector3", "RichTypeVector4", "Vector4", "RichTypeMatrix", "Matrix", "RichTypeMatrix2D", "RichTypeMatrix3D", "RichTypeColor3", "Color3", "RichTypeColor4", "Color4", "RichTypeQuaternion", "Quaternion", "RichTypeFlowGraphInteger", "getRichTypeFromValue", "anyValue", "getRichTypeByFlowGraphType", "flowGraphType", "getAnimationTypeByFlowGraphType", "getRichTypeByAnimationType", "getMappingForFullOperationName", "fullOperationName", "op", "extension", "getMappingForDeclaration", "declaration", "returnNoOpIfNotAvailable", "mapping", "_a", "gltfExtensionsToFlowGraphMapping", "gltfToFlowGraphMapping", "<PERSON><PERSON>", "inputs", "outputs", "key", "addNewInteractivityFlowGraphMapping", "gltfBlock", "_mapping", "parser", "serializedObjects", "eventId", "event", "serializedObject", "interactivityGraph", "eventConfiguration", "getSimpleInputMapping", "_gltfBlock", "_declaration", "_parser", "type", "context", "d", "input", "socketIn", "socketInA", "socketInB", "_b", "_arrays", "output", "index", "cases", "caseValue", "uniqueCases", "serializedValueInterpolation", "propertyIndex", "variable", "serializedGetVariable", "time", "_context", "globalGLTF", "inferType", "acc"], "mappings": "mGAIO,MAAMA,CAAiB,CAC1B,YAAYC,EAAO,CACf,KAAK,MAAQ,KAAK,OAAOA,CAAK,CACjC,CAMD,OAAOC,EAAG,CACN,OAAOA,EAAI,CACd,CAMD,IAAIC,EAAO,CACP,OAAO,IAAIH,EAAiB,KAAK,MAAQG,EAAM,KAAK,CACvD,CAMD,SAASA,EAAO,CACZ,OAAO,IAAIH,EAAiB,KAAK,MAAQG,EAAM,KAAK,CACvD,CAMD,SAASA,EAAO,CACZ,OAAO,IAAIH,EAAiB,KAAK,KAAK,KAAK,MAAOG,EAAM,KAAK,CAAC,CACjE,CAMD,OAAOA,EAAO,CACV,OAAO,IAAIH,EAAiB,KAAK,MAAQG,EAAM,KAAK,CACvD,CAKD,cAAe,CACX,OAAOH,EAAiB,SAC3B,CAMD,OAAOG,EAAO,CACV,OAAO,KAAK,QAAUA,EAAM,KAC/B,CAMD,OAAO,UAAUF,EAAO,CACpB,OAAO,IAAID,EAAiBC,CAAK,CACpC,CACD,UAAW,CACP,OAAO,KAAK,MAAM,UACrB,CACL,CACAD,EAAiB,UAAY,mBAC7BI,EAAc,mBAAoBJ,CAAgB,ECvE3C,MAAMK,CAAkB,CAC3B,YAAYC,EAAI,CAAC,EAAG,EAAG,EAAG,CAAC,EAAG,CAC1B,KAAK,GAAKA,CACb,CACD,IAAI,GAAI,CACJ,OAAO,KAAK,EACf,CACD,gBAAgBC,EAAG,CACf,OAAO,KAAK,qBAAqBA,EAAG,IAAIC,CAAS,CACpD,CACD,qBAAqBD,EAAGE,EAAQ,CAC5B,OAAAA,EAAO,EAAIF,EAAE,EAAI,KAAK,GAAG,CAAC,EAAIA,EAAE,EAAI,KAAK,GAAG,CAAC,EAC7CE,EAAO,EAAIF,EAAE,EAAI,KAAK,GAAG,CAAC,EAAIA,EAAE,EAAI,KAAK,GAAG,CAAC,EACtCE,CACV,CACD,SAAU,CACN,OAAO,KAAK,SACf,CACD,QAAQC,EAAa,GAAI,CACrB,QAASC,EAAI,EAAGA,EAAI,EAAGA,IACnBD,EAAWC,CAAC,EAAI,KAAK,GAAGA,CAAC,EAE7B,OAAOD,CACV,CACD,UAAUE,EAAO,CACb,QAASD,EAAI,EAAGA,EAAI,EAAGA,IACnB,KAAK,GAAGA,CAAC,EAAIC,EAAMD,CAAC,EAExB,OAAO,IACV,CACD,cAAcR,EAAOM,EAAQ,CACzB,MAAMI,EAAcV,EAAM,GACpBW,EAAa,KAAK,GAClB,EAAIL,EAAO,GAEjB,SAAE,CAAC,EAAII,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EACrE,EAAE,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EACrE,EAAE,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EACrE,EAAE,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAC9DL,CACV,CACD,SAASN,EAAO,CACZ,OAAO,KAAK,cAAcA,EAAO,IAAIE,CAAmB,CAC3D,CACD,YAAYF,EAAOM,EAAQ,CACvB,MAAMH,EAAI,KAAK,GACTS,EAAIZ,EAAM,GACV,EAAIM,EAAO,GACjB,SAAE,CAAC,EAAIH,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACVN,CACV,CACD,OAAON,EAAO,CACV,OAAO,KAAK,YAAYA,EAAO,IAAIE,CAAmB,CACzD,CACD,SAASF,EAAOM,EAAQ,CACpB,MAAMH,EAAI,KAAK,GACTS,EAAIZ,EAAM,EACV,EAAIM,EAAO,EACjB,SAAE,CAAC,EAAIH,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACVN,CACV,CACD,IAAIN,EAAO,CACP,OAAO,KAAK,SAASA,EAAO,IAAIE,CAAmB,CACtD,CACD,cAAcF,EAAOM,EAAQ,CACzB,MAAMH,EAAI,KAAK,GACTS,EAAIZ,EAAM,EACV,EAAIM,EAAO,EACjB,SAAE,CAAC,EAAIH,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACVN,CACV,CACD,SAASN,EAAO,CACZ,OAAO,KAAK,cAAcA,EAAO,IAAIE,CAAmB,CAC3D,CACD,WAAY,CACR,MAAMC,EAAI,KAAK,GACf,OAAO,IAAID,EAAkB,CAACC,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,CAAC,CAAC,CACxD,CACD,aAAc,CACV,MAAMA,EAAI,KAAK,GACf,OAAOA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,CAClC,CACD,SAAU,CACN,MAAMU,EAAM,KAAK,cACjB,GAAIA,IAAQ,EACR,MAAM,IAAI,MAAM,0BAA0B,EAE9C,MAAMV,EAAI,KAAK,GACTW,EAAS,EAAID,EACnB,OAAO,IAAIX,EAAkB,CAACC,EAAE,CAAC,EAAIW,EAAQ,CAACX,EAAE,CAAC,EAAIW,EAAQ,CAACX,EAAE,CAAC,EAAIW,EAAQX,EAAE,CAAC,EAAIW,CAAM,CAAC,CAC9F,CACD,OAAOd,EAAOe,EAAU,EAAG,CACvB,MAAMZ,EAAI,KAAK,GACTS,EAAIZ,EAAM,EAChB,OAAIe,IAAY,EACLZ,EAAE,CAAC,IAAMS,EAAE,CAAC,GAAKT,EAAE,CAAC,IAAMS,EAAE,CAAC,GAAKT,EAAE,CAAC,IAAMS,EAAE,CAAC,GAAKT,EAAE,CAAC,IAAMS,EAAE,CAAC,EAEnE,KAAK,IAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,CAAC,EAAIG,GAAW,KAAK,IAAIZ,EAAE,CAAC,EAAIS,EAAE,CAAC,CAAC,EAAIG,GAAW,KAAK,IAAIZ,EAAE,CAAC,EAAIS,EAAE,CAAC,CAAC,EAAIG,GAAW,KAAK,IAAIZ,EAAE,CAAC,EAAIS,EAAE,CAAC,CAAC,EAAIG,CAC3I,CACD,cAAe,CACX,MAAO,mBACV,CACD,UAAW,CACP,MAAO,qBAAqB,KAAK,GAAG,KAAK,IAAI,CAAC,GACjD,CACL,CAIO,MAAMC,CAAkB,CAC3B,YAAYP,EAAQ,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,EAAG,CAC7C,KAAK,GAAKA,CACb,CACD,IAAI,GAAI,CACJ,OAAO,KAAK,EACf,CACD,gBAAgBL,EAAG,CACf,OAAO,KAAK,qBAAqBA,EAAG,IAAIa,CAAS,CACpD,CACD,qBAAqBb,EAAGE,EAAQ,CAC5B,MAAMH,EAAI,KAAK,GACf,OAAAG,EAAO,EAAIF,EAAE,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAID,EAAE,CAAC,EAC9CG,EAAO,EAAIF,EAAE,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAID,EAAE,CAAC,EAC9CG,EAAO,EAAIF,EAAE,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAID,EAAE,CAAC,EACvCG,CACV,CACD,cAAcN,EAAOM,EAAQ,CACzB,MAAMI,EAAcV,EAAM,GACpBW,EAAa,KAAK,GAClB,EAAIL,EAAO,EACjB,SAAE,CAAC,EAAII,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EACtG,EAAE,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EACtG,EAAE,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EACtG,EAAE,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EACtG,EAAE,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EACtG,EAAE,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EACtG,EAAE,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EACtG,EAAE,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EACtG,EAAE,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAAID,EAAY,CAAC,EAAIC,EAAW,CAAC,EAC/FL,CACV,CACD,SAASN,EAAO,CACZ,OAAO,KAAK,cAAcA,EAAO,IAAIgB,CAAmB,CAC3D,CACD,YAAYhB,EAAOM,EAAQ,CACvB,MAAMH,EAAI,KAAK,GACTS,EAAIZ,EAAM,EACV,EAAIM,EAAO,EACjB,SAAE,CAAC,EAAIH,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACVN,CACV,CACD,OAAON,EAAO,CACV,OAAO,KAAK,YAAYA,EAAO,IAAIgB,CAAmB,CACzD,CACD,SAAShB,EAAOM,EAAQ,CACpB,MAAMH,EAAI,KAAK,GACTS,EAAIZ,EAAM,EACV,EAAIM,EAAO,EACjB,SAAE,CAAC,EAAIH,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACVN,CACV,CACD,IAAIN,EAAO,CACP,OAAO,KAAK,SAASA,EAAO,IAAIgB,CAAmB,CACtD,CACD,cAAchB,EAAOM,EAAQ,CACzB,MAAMH,EAAI,KAAK,GACTS,EAAIZ,EAAM,EACV,EAAIM,EAAO,EACjB,SAAE,CAAC,EAAIH,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACjB,EAAE,CAAC,EAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,EACVN,CACV,CACD,SAASN,EAAO,CACZ,OAAO,KAAK,cAAcA,EAAO,IAAIgB,CAAmB,CAC3D,CACD,QAAQT,EAAa,GAAI,CACrB,QAASC,EAAI,EAAGA,EAAI,EAAGA,IACnBD,EAAWC,CAAC,EAAI,KAAK,GAAGA,CAAC,EAE7B,OAAOD,CACV,CACD,SAAU,CACN,OAAO,KAAK,SACf,CACD,UAAUE,EAAO,CACb,QAASD,EAAI,EAAGA,EAAI,EAAGA,IACnB,KAAK,GAAGA,CAAC,EAAIC,EAAMD,CAAC,EAExB,OAAO,IACV,CACD,WAAY,CACR,MAAML,EAAI,KAAK,GACf,OAAO,IAAIa,EAAkB,CAACb,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,CAAC,CAAC,CACtF,CACD,aAAc,CACV,MAAMA,EAAI,KAAK,GACf,OAAOA,EAAE,CAAC,GAAKA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKA,EAAE,CAAC,GAAKA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKA,EAAE,CAAC,GAAKA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EACpH,CACD,SAAU,CACN,MAAMU,EAAM,KAAK,cACjB,GAAIA,IAAQ,EACR,MAAM,IAAI,MAAM,0BAA0B,EAE9C,MAAMV,EAAI,KAAK,GACTW,EAAS,EAAID,EACnB,OAAO,IAAIG,EAAkB,EACxBb,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKW,GAC7BX,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKW,GAC7BX,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKW,GAC7BX,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKW,GAC7BX,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKW,GAC7BX,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKW,GAC7BX,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKW,GAC7BX,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKW,GAC7BX,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKW,CAC1C,CAAS,CACJ,CACD,OAAOd,EAAOe,EAAU,EAAG,CACvB,MAAMZ,EAAI,KAAK,GACTS,EAAIZ,EAAM,EAEhB,OAAIe,IAAY,EACLZ,EAAE,CAAC,IAAMS,EAAE,CAAC,GAAKT,EAAE,CAAC,IAAMS,EAAE,CAAC,GAAKT,EAAE,CAAC,IAAMS,EAAE,CAAC,GAAKT,EAAE,CAAC,IAAMS,EAAE,CAAC,GAAKT,EAAE,CAAC,IAAMS,EAAE,CAAC,GAAKT,EAAE,CAAC,IAAMS,EAAE,CAAC,GAAKT,EAAE,CAAC,IAAMS,EAAE,CAAC,GAAKT,EAAE,CAAC,IAAMS,EAAE,CAAC,GAAKT,EAAE,CAAC,IAAMS,EAAE,CAAC,EAEvJ,KAAK,IAAIT,EAAE,CAAC,EAAIS,EAAE,CAAC,CAAC,EAAIG,GAC5B,KAAK,IAAIZ,EAAE,CAAC,EAAIS,EAAE,CAAC,CAAC,EAAIG,GACxB,KAAK,IAAIZ,EAAE,CAAC,EAAIS,EAAE,CAAC,CAAC,EAAIG,GACxB,KAAK,IAAIZ,EAAE,CAAC,EAAIS,EAAE,CAAC,CAAC,EAAIG,GACxB,KAAK,IAAIZ,EAAE,CAAC,EAAIS,EAAE,CAAC,CAAC,EAAIG,GACxB,KAAK,IAAIZ,EAAE,CAAC,EAAIS,EAAE,CAAC,CAAC,EAAIG,GACxB,KAAK,IAAIZ,EAAE,CAAC,EAAIS,EAAE,CAAC,CAAC,EAAIG,GACxB,KAAK,IAAIZ,EAAE,CAAC,EAAIS,EAAE,CAAC,CAAC,EAAIG,GACxB,KAAK,IAAIZ,EAAE,CAAC,EAAIS,EAAE,CAAC,CAAC,EAAIG,CAC/B,CACD,cAAe,CACX,MAAO,mBACV,CACD,UAAW,CACP,MAAO,qBAAqB,KAAK,GAAG,KAAK,IAAI,CAAC,GACjD,CACL,CC7QO,IAAIG,GACV,SAAUA,EAAgB,CACvBA,EAAe,IAAS,MACxBA,EAAe,OAAY,SAC3BA,EAAe,OAAY,SAC3BA,EAAe,QAAa,UAC5BA,EAAe,OAAY,SAC3BA,EAAe,QAAa,mBAC5BA,EAAe,QAAa,UAC5BA,EAAe,QAAa,UAC5BA,EAAe,QAAa,UAC5BA,EAAe,WAAgB,aAC/BA,EAAe,OAAY,SAC3BA,EAAe,SAAc,WAC7BA,EAAe,SAAc,WAC7BA,EAAe,OAAY,SAC3BA,EAAe,OAAY,QAC/B,GAAGA,IAAmBA,EAAiB,CAAE,EAAC,EAKnC,MAAMC,CAAS,CAClB,YAIAC,EAIAC,EAIAC,EAAgB,GAAI,CAChB,KAAK,SAAWF,EAChB,KAAK,aAAeC,EACpB,KAAK,cAAgBC,CACxB,CAKD,UAAUC,EAAqB,CAC3BA,EAAoB,SAAW,KAAK,SACpCA,EAAoB,aAAe,KAAK,YAC3C,CACL,CACY,MAACC,EAAc,IAAIL,EAAS,MAAgC,MAAS,EACpEM,EAAiB,IAAIN,EAAS,SAAsC,EAAE,EACtEO,EAAiB,IAAIP,EAAS,SAAsC,EAAG,CAAC,EACxEQ,EAAkB,IAAIR,EAAS,UAAwC,EAAK,EAC5ES,EAAkB,IAAIT,EAAS,UAAwCd,EAAQ,KAAM,EAAE,CAAC,EACxFwB,EAAkB,IAAIV,EAAS,UAAwCF,EAAQ,KAAM,EAAE,CAAC,EACxFa,EAAkB,IAAIX,EAAS,UAAwCY,EAAQ,KAAI,CAAE,EACrFC,EAAiB,IAAIb,EAAS,SAAsCc,EAAO,SAAU,EAAE,CAAC,EACxFC,EAAmB,IAAIf,EAAS,WAA0C,IAAIjB,CAAmB,EACjGiC,EAAmB,IAAIhB,EAAS,WAA0C,IAAIH,CAAmB,EACjGoB,EAAiB,IAAIjB,EAAS,SAAsCkB,EAAO,MAAK,EAAI,CAAC,EACrFC,EAAiB,IAAInB,EAAS,SAAsC,IAAIoB,EAAO,EAAG,EAAG,EAAG,CAAC,EAAG,CAAC,EAC7FC,EAAqB,IAAIrB,EAAS,aAA8CsB,EAAW,SAAU,EAAE,CAAC,EACrHD,EAAmB,gBAAmB1C,GAC9BA,EAAM,cAAgBA,EAAM,aAAY,IAAO,UACxC2C,EAAW,UAAU3C,EAAM,QAAS,CAAA,EAEtCA,EAAM,cAAgBA,EAAM,aAAY,IAAO,UAC7C2C,EAAW,gBAAgB3C,CAAK,EAElCA,EAAM,cAAgBA,EAAM,aAAY,IAAO,SAC7C2C,EAAW,mBAAmB3C,CAAK,EAEvCA,EAEC,MAAC4C,EAA2B,IAAIvB,EAAS,mBAAiD,IAAItB,EAAiB,CAAC,EAAG,CAAC,EAMzH,SAAS8C,EAAqB7C,EAAO,CACxC,MAAM8C,EAAW9C,EACjB,OAAQ,OAAOA,EAAK,CAChB,IAAK,SACD,OAAO2B,EACX,IAAK,SACD,OAAOC,EACX,IAAK,UACD,OAAOC,EACX,IAAK,SACD,GAAIiB,EAAS,aACT,OAAQA,EAAS,aAAc,EAAA,CAC3B,IAAK,UACD,OAAOhB,EACX,IAAK,UACD,OAAOC,EACX,IAAK,UACD,OAAOC,EACX,IAAK,SACD,OAAOE,EACX,IAAK,SACD,OAAOI,EACX,IAAK,SACD,OAAOE,EACX,IAAK,aACD,OAAOE,EACX,IAAK,mBACD,OAAOE,EACX,IAAK,WACD,OAAOR,EACX,IAAK,WACD,OAAOC,CACd,CAEL,OAAOX,EACX,QACI,OAAOA,CACd,CACL,CAMO,SAASqB,EAA2BC,EAAe,CACtD,OAAQA,EAAa,CACjB,IAAK,SACD,OAAOrB,EACX,IAAK,SACD,OAAOC,EACX,IAAK,UACD,OAAOC,EACX,IAAK,UACD,OAAOC,EACX,IAAK,UACD,OAAOC,EACX,IAAK,UACD,OAAOC,EACX,IAAK,SACD,OAAOE,EACX,IAAK,SACD,OAAOI,EACX,IAAK,SACD,OAAOE,EACX,IAAK,aACD,OAAOE,EACX,IAAK,mBACD,OAAOE,EACX,IAAK,WACD,OAAOR,EACX,IAAK,WACD,OAAOC,EACX,QACI,OAAOX,CACd,CACL,CAMO,SAASuB,EAAgCD,EAAe,CAC3D,OAAQA,EAAa,CACjB,IAAK,SACD,MAAO,GACX,IAAK,UACD,MAAO,GACX,IAAK,UACD,MAAO,GACX,IAAK,SACD,MAAO,GACX,IAAK,SACD,MAAO,GACX,IAAK,SACD,MAAO,GACX,IAAK,aACD,MAAO,GACX,QACI,MAAO,EACd,CACL,CAMO,SAASE,EAA2B1B,EAAe,CACtD,OAAQA,EAAa,CACjB,IAAK,GACD,OAAOI,EACX,IAAK,GACD,OAAOE,EACX,IAAK,GACD,OAAOC,EACX,IAAK,GACD,OAAOG,EACX,IAAK,GACD,OAAOI,EACX,IAAK,GACD,OAAOE,EACX,IAAK,GACD,OAAOE,EACX,QACI,OAAOhB,CACd,CACL,CCnNO,SAASyB,EAA+BC,EAAmB,CAC9D,KAAM,CAACC,EAAIC,CAAS,EAAIF,EAAkB,MAAM,GAAG,EACnD,OAAOG,EAAyB,CAAE,GAAAF,EAAI,UAAAC,CAAW,CAAA,CACrD,CACO,SAASC,EAAyBC,EAAaC,EAA2B,GAAM,OACnF,MAAMC,EAAUF,EAAY,WAAYG,EAAAC,EAAiCJ,EAAY,SAAS,IAAtD,YAAAG,EAA0DH,EAAY,IAAMK,EAAuBL,EAAY,EAAE,EACzJ,GAAI,CAACE,IACDI,EAAO,KAAK,kCAAkCN,EAAY,EAAE,kBAAkBA,EAAY,WAAa,mBAAmB,EAAE,EACxHC,GAA0B,CAC1B,MAAMM,EAAS,CAAA,EACTC,EAAU,CACZ,MAAO,CAAE,CACzB,EACY,GAAIR,EAAY,kBAAmB,CAC/BO,EAAO,OAAS,GAChB,UAAWE,KAAOT,EAAY,kBAC1BO,EAAO,OAAOE,CAAG,EAAI,CACjB,KAAMA,CAC9B,CAEa,CACD,OAAIT,EAAY,qBACZQ,EAAQ,OAAS,GACjB,OAAO,KAAKR,EAAY,kBAAkB,EAAE,QAASS,GAAQ,CACzDD,EAAQ,OAAOC,CAAG,EAAI,CAClB,KAAMA,CAC9B,CACA,CAAiB,GAEE,CACH,OAAQ,CAAE,EACV,OAAAF,EACA,QAAAC,CAChB,CACS,CAEL,OAAON,CACX,CAQO,SAASQ,EAAoCD,EAAKX,EAAWI,EAAS,CACzEE,EAAiCN,CAAS,IAAMM,EAAiCN,CAAS,EAAI,CAAE,GAChGM,EAAiCN,CAAS,EAAEW,CAAG,EAAIP,CACvD,CACA,MAAME,EAAmC,CAKrC,QAAS,CAQL,WAAY,CACR,OAAQ,CAAC,0BAAgE,EACzE,OAAQ,CACJ,OAAQ,CACJ,QAAS,CAAE,KAAM,SAAW,CAC/B,CACJ,CACJ,CACJ,CACL,EAEMC,EAAyB,CAC3B,gBAAiB,CACb,OAAQ,CAAC,+BAA0E,EACnF,QAAS,CACL,MAAO,CACH,IAAK,CAAE,KAAM,MAAQ,CACxB,CACJ,CACJ,EACD,eAAgB,CACZ,OAAQ,CAAC,8BAAwE,EACjF,OAAQ,CAAE,EACV,QAAS,CACL,OAAQ,CACJ,kBAAmB,CAAE,KAAM,YAAa,SAAU,QAA+D,CACpH,EACD,MAAO,CACH,IAAK,CAAE,KAAM,MAAQ,CACxB,CACJ,CACJ,EACD,aAAc,CACV,OAAQ,CAAC,+BAA0E,EACnF,QAAS,CACL,MAAO,CACH,IAAK,CAAE,KAAM,MAAQ,CACxB,CACJ,EACD,eAAeM,EAAWX,EAAaY,EAAUC,EAAQC,EAAmB,CAGxE,GAAId,EAAY,KAAO,cAAgB,CAACW,EAAU,eAAiB,OAAO,KAAKA,EAAU,aAAa,EAAE,SAAW,EAC/G,MAAM,IAAI,MAAM,2EAA2E,EAG/F,MAAMI,EADqBJ,EAAU,cAAc,MAChB,MAAM,CAAC,EAC1C,GAAI,OAAOI,GAAY,SACnB,MAAM,IAAI,MAAM,6BAA6B,EAEjD,MAAMC,EAAQH,EAAO,OAAO,OAAOE,CAAO,EACpCE,EAAmBH,EAAkB,CAAC,EAC5C,OAAAG,EAAiB,SAAWA,EAAiB,OAAS,CAAE,GACxDA,EAAiB,OAAO,QAAUD,EAAM,QACxCC,EAAiB,OAAO,UAAYD,EAAM,UACnCF,CACV,CACJ,EACD,gBAAiB,CACb,OAAQ,CAAC,kCAAgF,EACzF,QAAS,CACL,MAAO,CACH,IAAK,CAAE,KAAM,MAAQ,CACxB,CACJ,EACD,WAAWH,EAAWO,EAAoB,OACtC,GAAI,CAACP,EAAU,cACX,OAAAL,EAAO,MAAM,kDAAkD,EACxD,GAEX,MAAMa,EAAqBR,EAAU,cAAc,MACnD,GAAI,CAACQ,EACD,OAAAb,EAAO,MAAM,2EAA2E,EACjF,GAEX,MAAMS,EAAUI,EAAmB,MAAM,CAAC,EAC1C,OAAI,OAAOJ,GAAY,UACnBT,EAAO,MAAM,6BAA6B,EACnC,MAEGH,EAAAe,EAAmB,SAAnB,YAAAf,EAA4BY,IAKnC,IAHHT,EAAO,MAAM,iBAAiBS,CAAO,YAAY,EAC1C,GAGd,EACD,eAAeJ,EAAWX,EAAaY,EAAUC,EAAQC,EAAmB,CAGxE,GAAId,EAAY,KAAO,iBAAmB,CAACW,EAAU,eAAiB,OAAO,KAAKA,EAAU,aAAa,EAAE,SAAW,EAClH,MAAM,IAAI,MAAM,2EAA2E,EAG/F,MAAMI,EADqBJ,EAAU,cAAc,MAChB,MAAM,CAAC,EAC1C,GAAI,OAAOI,GAAY,SACnB,MAAM,IAAI,MAAM,6BAA6B,EAEjD,MAAMC,EAAQH,EAAO,OAAO,OAAOE,CAAO,EACpCE,EAAmBH,EAAkB,CAAC,EAC5C,OAAAG,EAAiB,SAAWA,EAAiB,OAAS,CAAE,GACxDA,EAAiB,OAAO,QAAUD,EAAM,QACxCC,EAAiB,OAAO,UAAYD,EAAM,UACnCF,CACV,CACJ,EACD,SAAUM,EAAsB,iBAA8C,EAC9E,UAAWA,EAAsB,kBAAgD,EACjF,WAAYA,EAAsB,mBAAkD,EACpF,WAAYA,EAAsB,mBAAkD,EACpF,WAAYA,EAAsB,mBAAkD,EACpF,YAAaA,EAAsB,oBAAoD,EACvF,aAAcA,EAAsB,qBAAsD,EAC1F,aAAcA,EAAsB,qBAAsD,EAC1F,YAAaA,EAAsB,oBAAoD,EACvF,aAAc,CACV,OAAQ,CAAC,qBAAsD,EAC/D,cAAe,CAAE,EACjB,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,GAAK,CACnB,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,MAAO,CAAE,KAAM,OAAS,CAC3B,CACJ,EACD,eAAeT,EAAWX,EAAaY,EAAUC,EAAQC,EAAmB,CAExE,OAAAA,EAAkB,CAAC,EAAE,OAASA,EAAkB,CAAC,EAAE,QAAU,GAC7DA,EAAkB,CAAC,EAAE,OAAO,sBAAwB,GAC7CA,CACV,CACJ,EACD,aAAcM,EAAsB,qBAAyD,EAC7F,WAAYA,EAAsB,wBAA4D,EAC9F,WAAYA,EAAsB,oBAAmD,CAAC,IAAK,GAAG,EAAG,EAAI,EACrG,WAAYA,EAAsB,yBAA6D,CAAC,IAAK,GAAG,EAAG,EAAI,EAC/G,WAAY,CACR,OAAQ,CAAC,wBAA4D,EACrE,eAAeC,EAAYC,EAAcV,EAAUW,EAAST,EAAmB,CAE3EA,EAAkB,CAAC,EAAE,OAASA,EAAkB,CAAC,EAAE,QAAU,GAC7DA,EAAkB,CAAC,EAAE,OAAO,sBAAwB,GAGpD,IAAIU,EAAO,GACX,cAAO,KAAKH,EAAW,QAAU,CAAA,CAAE,EAAE,KAAM7E,GAAU,OACjD,QAAI2D,EAAAkB,EAAW,SAAX,YAAAlB,EAAoB3D,GAAO,QAAS,QACpCgF,EAAOH,EAAW,OAAO7E,CAAK,EAAE,KACzB,IAEJ,EACvB,CAAa,EACGgF,IAAS,KACTV,EAAkB,CAAC,EAAE,OAAO,KAAOS,EAAQ,OAAO,MAAMC,CAAI,EAAE,eAE3DV,CACV,CACJ,EACD,WAAYM,EAAsB,uBAAyD,CAAC,IAAK,GAAG,EAAG,EAAI,EAC3G,WAAYA,EAAsB,uBAAyD,CAAC,IAAK,GAAG,CAAC,EACrG,WAAYA,EAAsB,oBAAmD,CAAC,IAAK,GAAG,CAAC,EAC/F,WAAYA,EAAsB,oBAAmD,CAAC,IAAK,GAAG,CAAC,EAC/F,aAAcA,EAAsB,sBAAuD,CAAC,IAAK,IAAK,GAAG,CAAC,EAC1G,gBAAiBA,EAAsB,wBAA4D,EACnG,WAAYA,EAAsB,kCAA+E,CAAC,IAAK,IAAK,GAAG,CAAC,EAChI,UAAWA,EAAsB,yBAA6D,CAAC,IAAK,GAAG,CAAC,EACxG,UAAWA,EAAsB,yBAA6D,CAAC,IAAK,GAAG,CAAC,EACxG,UAAWA,EAAsB,gCAA2E,CAAC,IAAK,GAAG,CAAC,EACtH,UAAWA,EAAsB,4BAAmE,CAAC,IAAK,GAAG,CAAC,EAC9G,UAAWA,EAAsB,mCAAiF,CAAC,IAAK,GAAG,CAAC,EAC5H,aAAcA,EAAsB,qBAAsD,EAC1F,aAAcA,EAAsB,qBAA2D,EAC/F,cAAe,CACX,OAAQ,CAAC,2BAAkE,EAC3E,OAAQ,CACJ,OAAQ,CACJ,UAAW,CAAE,KAAM,WAAa,EAEhC,EAAG,CAAE,KAAM,QAAU,EACrB,EAAG,CAAE,KAAM,SAAW,CACzB,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,MAAO,CAAE,KAAM,QAAU,CAC5B,CACJ,CACJ,EACD,cAAe,CACX,OAAQ,CAAC,sBAAwD,EACjE,QAAS,CACL,OAAQ,CACJ,MAAO,CAAE,KAAM,OAAS,CAC3B,CACJ,CACJ,EACD,WAAYA,EAAsB,mBAAkD,EACpF,WAAYA,EAAsB,mBAAkD,EACpF,WAAYA,EAAsB,mBAAkD,EACpF,YAAaA,EAAsB,oBAAoD,EACvF,YAAaA,EAAsB,oBAAoD,EACvF,YAAaA,EAAsB,oBAAoD,EACvF,aAAcA,EAAsB,sBAAuD,CAAC,IAAK,GAAG,CAAC,EACrG,YAAaA,EAAsB,oBAAoD,EACvF,YAAaA,EAAsB,oBAAoD,EACvF,YAAaA,EAAsB,oBAAoD,EACvF,aAAcA,EAAsB,qBAAsD,EAC1F,aAAcA,EAAsB,qBAAsD,EAC1F,aAAcA,EAAsB,qBAAsD,EAC1F,WAAYA,EAAsB,2BAAkE,EACpG,WAAYA,EAAsB,mBAAkD,EACpF,YAAaA,EAAsB,oBAAoD,EACvF,aAAcA,EAAsB,qBAAsD,EAC1F,YAAaA,EAAsB,0BAAgE,EACnG,YAAaA,EAAsB,wBAA4D,EAC/F,WAAYA,EAAsB,sBAAuD,CAAC,IAAK,GAAG,CAAC,EACnG,cAAeA,EAAsB,sBAAwD,EAC7F,iBAAkBA,EAAsB,yBAA8D,EACtG,WAAYA,EAAsB,oBAAmD,CAAC,IAAK,GAAG,CAAC,EAC/F,aAAcA,EAAsB,sBAAuD,CAAC,IAAK,GAAG,CAAC,EACrG,gBAAiBA,EAAsB,yBAA6D,CAAC,IAAK,GAAG,CAAC,EAC9G,gBAAiBA,EAAsB,yBAA6D,CAAC,IAAK,IAAK,GAAG,CAAC,EACnH,iBAAkB,CAEd,OAAQ,CAAC,+BAA0E,EACnF,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,GAAK,EAChB,EAAG,CAAE,KAAM,GAAK,CACnB,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,MAAO,CAAE,KAAM,OAAS,CAC3B,CACJ,CACJ,EACD,gBAAiB,CACb,OAAQ,CAAC,8BAAwE,EACjF,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,CAC7C,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,MAAO,CAAE,KAAM,OAAS,CAC3B,CACJ,CACJ,EACD,gBAAiB,CACb,OAAQ,CAAC,8BAAwE,EACjF,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,CAC7C,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,MAAO,CAAE,KAAM,OAAS,CAC3B,CACJ,CACJ,EACD,gBAAiB,CACb,OAAQ,CAAC,8BAAwE,EACjF,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,CAC7C,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,MAAO,CAAE,KAAM,OAAS,CAC3B,CACJ,CACJ,EAED,gBAAiB,CACb,OAAQ,CAAC,8BAAwE,EACjF,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,QAAS,SAAU,QAAU,CAC3C,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,CAC5B,CACJ,CACJ,EACD,gBAAiB,CACb,OAAQ,CAAC,8BAAwE,EACjF,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,QAAS,SAAU,QAAU,CAC3C,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,CAC5B,CACJ,CACJ,EACD,gBAAiB,CACb,OAAQ,CAAC,8BAAwE,EACjF,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,QAAS,SAAU,QAAU,CAC3C,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,CAC5B,CACJ,CACJ,EACD,iBAAkBA,EAAsB,yBAA8D,EACtG,mBAAoBA,EAAsB,2BAAkE,EAC5G,eAAgBA,EAAsB,4BAAoE,EAC1G,cAAeA,EAAsB,qCAAqF,CAAC,IAAK,GAAG,CAAC,EACpI,kBAAmB,CACf,OAAQ,CAAC,wBAAiE,EAC1E,OAAQ,CACJ,OAAQ,CACJ,YAAa,CAAE,KAAM,WAAY,SAAU,QAAU,EACrD,SAAU,CAAE,KAAM,qBAAsB,SAAU,QAAU,EAC5D,MAAO,CAAE,KAAM,UAAW,SAAU,QAAU,CACjD,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,MAAO,CAAE,KAAM,OAAS,CAC3B,CACJ,EACD,eAAeC,EAAYC,EAAcV,EAAUW,EAAST,EAAmBW,EAAS,CAEpF,MAAMC,EAAIZ,EAAkB,CAAC,EAAE,WAAW,KAAMa,GAAUA,EAAM,OAAS,oBAAoB,EAC7F,GAAI,CAACD,EACD,MAAM,IAAI,MAAM,qCAAqC,EAGzD,OAAID,EAAQ,kBAAkBC,EAAE,QAAQ,IACpCD,EAAQ,kBAAkBC,EAAE,QAAQ,EAAE,KAAO,cAE1CZ,CACV,CACJ,EACD,oBAAqB,CACjB,OAAQ,CAAC,0BAAqE,EAC9E,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,QAAS,SAAU,QAAU,CAC3C,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,YAAa,CAAE,KAAM,UAAY,EACjC,SAAU,CAAE,KAAM,oBAAsB,EACxC,MAAO,CAAE,KAAM,SAAW,CAC7B,CACJ,CACJ,EACD,kBAAmB,CACf,OAAQ,CAAC,+BAA0E,EACnF,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,CAC7C,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,MAAO,CAAE,KAAM,OAAS,CAC3B,CACJ,EACD,eAAeO,EAAYC,EAAcV,EAAUW,EAAST,EAAmB,CAE3E,OAAAA,EAAkB,CAAC,EAAE,OAASA,EAAkB,CAAC,EAAE,QAAU,GAC7DA,EAAkB,CAAC,EAAE,OAAO,mBAAqB,GAC1CA,CACV,CACJ,EACD,kBAAmB,CACf,OAAQ,CAAC,+BAA0E,EACnF,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,QAAS,SAAU,UAAY,CAC7C,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,CAC5B,CACJ,CACJ,EACD,kBAAmB,CACf,OAAQ,CAAC,+BAA0E,EACnF,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,CAC7C,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,MAAO,CAAE,KAAM,OAAS,CAC3B,CACJ,EACD,eAAeO,EAAYC,EAAcV,EAAUW,EAAST,EAAmB,CAE3E,OAAAA,EAAkB,CAAC,EAAE,OAASA,EAAkB,CAAC,EAAE,QAAU,GAC7DA,EAAkB,CAAC,EAAE,OAAO,mBAAqB,GAC1CA,CACV,CACJ,EACD,kBAAmB,CACf,OAAQ,CAAC,+BAA0E,EACnF,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,QAAS,SAAU,UAAY,CAC7C,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,CAC5B,CACJ,CACJ,EACD,kBAAmB,CACf,OAAQ,CAAC,6BAAsE,EAC/E,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,UAAW,SAAU,QAAU,EAC1C,EAAG,CAAE,KAAM,WAAY,SAAU,QAAU,EAC3C,EAAG,CAAE,KAAM,WAAY,SAAU,QAAU,EAC3C,EAAG,CAAE,KAAM,WAAY,SAAU,QAAU,EAC3C,EAAG,CAAE,KAAM,WAAY,SAAU,QAAU,EAC3C,EAAG,CAAE,KAAM,WAAY,SAAU,QAAU,EAC3C,EAAG,CAAE,KAAM,WAAY,SAAU,QAAU,CAC9C,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,MAAO,CAAE,KAAM,OAAS,CAC3B,CACJ,EACD,eAAeO,EAAYC,EAAcV,EAAUW,EAAST,EAAmB,CAE3E,OAAAA,EAAkB,CAAC,EAAE,OAASA,EAAkB,CAAC,EAAE,QAAU,GAC7DA,EAAkB,CAAC,EAAE,OAAO,mBAAqB,GAC1CA,CACV,CACJ,EACD,kBAAmB,CACf,OAAQ,CAAC,6BAAsE,EAC/E,cAAe,CAAE,EACjB,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,QAAS,SAAU,QAAU,CAC3C,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,EAAK,CAAE,KAAM,UAAY,EACzB,GAAM,CAAE,KAAM,WAAa,EAC3B,GAAM,CAAE,KAAM,WAAa,EAC3B,GAAM,CAAE,KAAM,WAAa,EAC3B,GAAM,CAAE,KAAM,WAAa,EAC3B,GAAM,CAAE,KAAM,WAAa,EAC3B,GAAM,CAAE,KAAM,WAAa,CAC9B,CACJ,CACJ,EACD,eAAgB,CACZ,OAAQ,CAAC,wBAAiE,EAC1E,cAAe,CAAE,EACjB,OAAQ,CACJ,OAAQ,CACJ,YAAa,CAAE,KAAM,WAAY,SAAU,QAAU,EACrD,SAAU,CAAE,KAAM,qBAAsB,SAAU,QAAU,EAC5D,MAAO,CAAE,KAAM,UAAW,SAAU,QAAU,CACjD,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,MAAO,CAAE,KAAM,QAAU,CAC5B,CACJ,CACJ,EACD,iBAAkB,CACd,OAAQ,CAAC,0BAAqE,EAC9E,cAAe,CAAE,EACjB,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,OAAS,CACvB,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,YAAa,CAAE,KAAM,UAAY,EACjC,SAAU,CAAE,KAAM,oBAAsB,EACxC,MAAO,CAAE,KAAM,SAAW,CAC7B,CACJ,CACJ,EACD,WAAY,CACR,OAAQ,CAAC,0BAAgE,EACzE,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,GAAK,CACnB,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,MAAO,CAAE,KAAM,OAAS,CAC3B,CACJ,EACD,eAAeO,EAAYC,EAAcV,EAAUW,EAAST,EAAmBW,EAAS,OAEpFX,EAAkB,CAAC,EAAE,OAASA,EAAkB,CAAC,EAAE,QAAU,GAE7D,MAAMc,EAAWd,EAAkB,CAAC,EAAE,WAAW,CAAC,EAClD,OAAAA,EAAkB,CAAC,EAAE,OAAO,YAAYX,EAAAsB,EAAQ,kBAAkBG,EAAS,QAAQ,IAA3C,YAAAzB,EAA8C,OAAQ,mBACvFW,CACV,CACJ,EACD,WAAY,CACR,OAAQ,CAAC,0BAAgE,EACzE,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,GAAK,EAChB,EAAG,CAAE,KAAM,GAAK,CACnB,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,MAAO,CAAE,KAAM,OAAS,CAC3B,CACJ,EACD,eAAeO,EAAYC,EAAcV,EAAUW,EAAST,EAAmBW,EAAS,SAEpFX,EAAkB,CAAC,EAAE,OAASA,EAAkB,CAAC,EAAE,QAAU,GAE7D,MAAMe,EAAYf,EAAkB,CAAC,EAAE,WAAW,CAAC,EAC7CgB,EAAYhB,EAAkB,CAAC,EAAE,WAAW,CAAC,EACnD,OAAAA,EAAkB,CAAC,EAAE,OAAO,YACxBX,EAAAsB,EAAQ,kBAAkBI,EAAU,QAAQ,IAA5C,YAAA1B,EAA+C,SAAQ4B,EAAAN,EAAQ,kBAAkBK,EAAU,QAAQ,IAA5C,YAAAC,EAA+C,OAAQ,mBAC3GjB,CACV,CACJ,EACD,UAAW,CACP,OAAQ,CAAC,yBAA8D,EACvE,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,GAAK,EAChB,EAAG,CAAE,KAAM,GAAK,CACnB,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,MAAO,CAAE,KAAM,OAAS,CAC3B,CACJ,EACD,eAAeO,EAAYC,EAAcV,EAAUW,EAAST,EAAmBW,EAAS,SAEpFX,EAAkB,CAAC,EAAE,OAASA,EAAkB,CAAC,EAAE,QAAU,GAE7D,MAAMe,EAAYf,EAAkB,CAAC,EAAE,WAAW,CAAC,EAC7CgB,EAAYhB,EAAkB,CAAC,EAAE,WAAW,CAAC,EACnD,OAAAA,EAAkB,CAAC,EAAE,OAAO,YACxBX,EAAAsB,EAAQ,kBAAkBI,EAAU,QAAQ,IAA5C,YAAA1B,EAA+C,SAAQ4B,EAAAN,EAAQ,kBAAkBK,EAAU,QAAQ,IAA5C,YAAAC,EAA+C,OAAQ,mBAC3GjB,CACV,CACJ,EACD,WAAY,CACR,OAAQ,CAAC,0BAAgE,EACzE,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,GAAK,EAChB,EAAG,CAAE,KAAM,GAAK,CACnB,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,MAAO,CAAE,KAAM,OAAS,CAC3B,CACJ,EACD,eAAeO,EAAYC,EAAcV,EAAUW,EAAST,EAAmBW,EAAS,SAEpFX,EAAkB,CAAC,EAAE,OAASA,EAAkB,CAAC,EAAE,QAAU,GAE7D,MAAMe,EAAYf,EAAkB,CAAC,EAAE,WAAW,CAAC,EAC7CgB,EAAYhB,EAAkB,CAAC,EAAE,WAAW,CAAC,EACnD,OAAAA,EAAkB,CAAC,EAAE,OAAO,YACxBX,EAAAsB,EAAQ,kBAAkBI,EAAU,QAAQ,IAA5C,YAAA1B,EAA+C,SAAQ4B,EAAAN,EAAQ,kBAAkBK,EAAU,QAAQ,IAA5C,YAAAC,EAA+C,OAAQ,mBAC3GjB,CACV,CACJ,EACD,WAAYM,EAAsB,kCAA+E,CAAC,IAAK,GAAG,CAAC,EAC3H,WAAYA,EAAsB,iCAA6E,CAAC,IAAK,GAAG,CAAC,EACzH,WAAYA,EAAsB,4BAAoE,EACtG,WAAYA,EAAsB,6BAAsE,EACxG,cAAeA,EAAsB,8BAAwE,EAC7G,WAAYA,EAAsB,wBAA4D,EAC9F,WAAYA,EAAsB,wBAA4D,EAC9F,iBAAkBA,EAAsB,uBAA+D,EACvG,mBAAoBA,EAAsB,yBAAmE,EAC7G,iBAAkBA,EAAsB,uBAA+D,EACvG,kBAAmBA,EAAsB,qBAA2D,EACpG,kBAAmBA,EAAsB,qBAA2D,EACpG,mBAAoBA,EAAsB,yBAAmE,EAE7G,gBAAiB,CACb,OAAQ,CAAC,wBAA4D,EACrE,eAAeT,EAAWW,EAAcV,EAAUoB,EAASlB,EAAmB,CAC1E,MAAMG,EAAmBH,EAAkB,CAAC,EAC5C,OAAAG,EAAiB,SAAWA,EAAiB,OAAS,CAAE,GACxDA,EAAiB,OAAO,kBAAoB,OAAO,KAAKN,EAAU,OAAS,EAAE,EAAE,OAC/EM,EAAiB,cAAc,QAAQ,CAACgB,EAAQC,IAAU,CACtDD,EAAO,KAAO,OAASC,CACvC,CAAa,EACMpB,CACV,CACJ,EACD,cAAe,CACX,OAAQ,CAAC,sBAAwD,EACjE,QAAS,CACL,MAAO,CACH,KAAM,CAAE,KAAM,QAAU,EACxB,MAAO,CAAE,KAAM,SAAW,CAC7B,CACJ,CACJ,EACD,cAAe,CACX,OAAQ,CAAC,sBAAwD,EACjE,cAAe,CACX,MAAO,CAAE,KAAM,QAAS,UAAW,GAAM,aAAc,EAAI,CAC9D,EACD,OAAQ,CACJ,OAAQ,CACJ,UAAW,CAAE,KAAM,MAAQ,CAC9B,CACJ,EACD,WAAWH,EAAW,CAClB,GAAIA,EAAU,eAAiBA,EAAU,cAAc,MAAO,CAC1D,MAAMwB,EAAQxB,EAAU,cAAc,MAAM,MAK5C,GAAI,CAJiBwB,EAAM,MAAOC,GAEvB,OAAOA,GAAc,UAAY,QAAQ,KAAKA,EAAU,SAAQ,CAAE,CAC5E,EAEG,OAAAzB,EAAU,cAAc,MAAM,MAAQ,CAAA,EAC/B,GAGX,MAAM0B,EAAc,IAAI,IAAIF,CAAK,EACjCxB,EAAU,cAAc,MAAM,MAAQ,MAAM,KAAK0B,CAAW,CAC/D,CACD,MAAO,EACV,EACD,eAAe1B,EAAWX,EAAaY,EAAUoB,EAASlB,EAAmB,CAEzE,GAAId,EAAY,KAAO,eAAiB,CAACW,EAAU,OAAS,OAAO,KAAKA,EAAU,KAAK,EAAE,SAAW,EAChG,MAAM,IAAI,MAAM,mEAAmE,EAGvF,OADyBG,EAAkB,CAAC,EAC3B,cAAc,QAASmB,GAAW,CAC3CA,EAAO,OAAS,YAChBA,EAAO,KAAO,OAASA,EAAO,KAElD,CAAa,EACMnB,CACV,CACJ,EACD,aAAc,CACV,OAAQ,CAAC,yBAA8D,EACvE,QAAS,CACL,MAAO,CACH,SAAU,CAAE,KAAM,eAAiB,CACtC,CACJ,CACJ,EACD,WAAY,CACR,OAAQ,CAAC,uBAA0D,EACnE,cAAe,CACX,aAAc,CAAE,KAAM,eAAgB,SAAU,SAAU,UAAW,GAAM,aAAc,CAAG,CAC/F,EACD,OAAQ,CACJ,OAAQ,CACJ,WAAY,CAAE,KAAM,aAAc,SAAU,QAAU,EACtD,SAAU,CAAE,KAAM,WAAY,SAAU,QAAU,CACrD,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,MAAO,CAAE,KAAM,OAAS,CAC3B,EACD,MAAO,CACH,SAAU,CAAE,KAAM,eAAiB,CACtC,CACJ,CACJ,EACD,WAAY,CACR,OAAQ,CAAC,mBAAkD,EAC3D,cAAe,CAAE,EACjB,OAAQ,CACJ,OAAQ,CACJ,EAAG,CAAE,KAAM,gBAAiB,SAAU,QAAU,CACnD,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,aAAc,CAAE,KAAM,gBAAkB,CAC3C,CACJ,CACJ,EACD,iBAAkB,CACd,OAAQ,CAAC,yBAA8D,EACvE,cAAe,CACX,SAAU,CAAE,KAAM,WAAY,SAAU,UAAW,UAAW,GAAM,aAAc,EAAO,EACzF,OAAQ,CAAE,KAAM,SAAU,SAAU,UAAW,UAAW,GAAM,aAAc,EAAO,CACxF,EACD,eAAeH,EAAWX,EAAaY,EAAUoB,EAASlB,EAAmB,CACzE,GAAId,EAAY,KAAO,kBAAoB,CAACW,EAAU,OAAS,OAAO,KAAKA,EAAU,KAAK,EAAE,SAAW,EACnG,MAAM,IAAI,MAAM,iFAAiF,EAErG,MAAMM,EAAmBH,EAAkB,CAAC,EAC5C,OAAAG,EAAiB,SAAWA,EAAiB,OAAS,CAAE,GACxDA,EAAiB,OAAO,kBAAoB,OAAO,KAAKN,EAAU,KAAK,EAAE,OACzEM,EAAiB,cAAc,QAAQ,CAACgB,EAAQC,IAAU,CACtDD,EAAO,KAAO,OAASC,CACvC,CAAa,EACMpB,CACV,CACJ,EACD,eAAgB,CACZ,OAAQ,CAAC,uBAA0D,EACnE,cAAe,CACX,WAAY,CAAE,KAAM,mBAAoB,SAAU,SAAU,UAAW,GAAM,aAAc,CAAG,CACjG,EACD,OAAQ,CACJ,MAAO,CACH,YAAa,CAAE,KAAM,OAAS,CACjC,CACJ,EACD,WAAWH,EAAW,SAElB,OAAI,QAAOoB,GAAA5B,EAAAQ,EAAU,gBAAV,YAAAR,EAAyB,aAAzB,YAAA4B,EAAqC,MAAM,KAAO,WACzDpB,EAAU,cAAgBA,EAAU,eAAiB,CACjD,WAAY,CAAE,MAAO,CAAC,CAAC,CAAG,CAC9C,EACgBA,EAAU,cAAc,WAAW,MAAQ,CAAC,CAAC,GAE1C,EACV,CACJ,EACD,gBAAiB,CACb,OAAQ,CAAC,wBAA4D,EACrE,QAAS,CACL,MAAO,CACH,IAAK,CAAE,KAAM,OAAS,CACzB,CACJ,CACJ,EACD,gBAAiB,CACb,OAAQ,CAAC,wBAA4D,EACrE,QAAS,CACL,MAAO,CACH,IAAK,CAAE,KAAM,OAAS,CACzB,CACJ,CACJ,EACD,mBAAoB,CAChB,OAAQ,CAAC,2BAAkE,CAC9E,EACD,eAAgB,CACZ,OAAQ,CAAC,2BAAkE,EAC3E,WAAWA,EAAW,SAClB,OAAKoB,GAAA5B,EAAAQ,EAAU,gBAAV,YAAAR,EAAyB,WAAzB,MAAA4B,EAAmC,MAIjC,IAHHzB,EAAO,MAAM,yDAAyD,EAC/D,GAGd,EACD,cAAe,CACX,SAAU,CACN,KAAM,WACN,SAAU,SACV,cAAe,SACf,UAAW,GACX,WAAY,GACZ,gBAAgB4B,EAAOrB,EAAQ,CAC3B,MAAO,CAACA,EAAO,gBAAgBqB,EAAM,CAAC,CAAC,CAAC,CAC3C,CACJ,CACJ,CACJ,EACD,eAAgB,CACZ,OAAQ,CAAC,2BAAkE,EAC3E,cAAe,CACX,SAAU,CACN,KAAM,WACN,SAAU,SACV,cAAe,SACf,UAAW,GACX,WAAY,GACZ,gBAAgBA,EAAOrB,EAAQ,CAC3B,MAAO,CAACA,EAAO,gBAAgBqB,EAAM,CAAC,CAAC,CAAC,CAC3C,CACJ,CACJ,CACJ,EACD,uBAAwB,CACpB,OAAQ,CAAC,2BAAkE,EAC3E,cAAe,CACX,UAAW,CACP,KAAM,YACN,SAAU,SACV,cAAe,SACf,UAAW,GACX,gBAAgBA,EAAOrB,EAAQ,CAC3B,MAAO,CAACqB,EAAM,CAAC,EAAE,IAAKhF,GAAM2D,EAAO,gBAAgB3D,CAAC,CAAC,CAAC,CACzD,CACJ,CACJ,EACD,eAAemE,EAAYC,EAAcV,EAAUC,EAAQC,EAAmB,CAG1E,OAD8BA,EAAkB,CAAC,EAC3B,WAAW,QAASa,GAAU,CAChDA,EAAM,KAAOd,EAAO,gBAAgB,CAACc,EAAM,IAAI,CAC/D,CAAa,EACMb,CACV,CACJ,EACD,uBAAwB,CACpB,OAAQ,CACJ,8BACA,wBACA,8BACA,6BACA,2BACH,EACD,cAAe,CACX,SAAU,CACN,KAAM,eACN,UAAW,GACX,WAAY,GACZ,gBAAgBoB,EAAOrB,EAAQ,CAC3B,MAAO,CAACA,EAAO,gBAAgBqB,EAAM,CAAC,CAAC,CAAC,CAC3C,CACJ,EACD,SAAU,CACN,KAAM,gBACN,UAAW,GACX,aAAc,GACd,gBAAkB1F,GACVA,EAAM,CAAC,IAAM,GACN,CAAC,YAAY,EAGb,CAAC,MAAS,CAG5B,CACJ,EACD,OAAQ,CACJ,OAAQ,CACJ,MAAO,CAAE,KAAM,SAAW,EAC1B,SAAU,CAAE,KAAM,aAAc,SAAU,QAAU,EACpD,GAAI,CAAE,KAAM,gBAAiB,QAAS,4BAA0E,EAChH,GAAI,CAAE,KAAM,gBAAiB,QAAS,4BAA0E,CACnH,EACD,MAAO,CACH,GAAI,CAAE,KAAM,KAAM,QAAS,6BAAuE,CACrG,CACJ,EACD,QAAS,CACL,MAAO,CACH,IAAK,CAAE,KAAM,QAAS,QAAS,6BAAuE,EACtG,IAAK,CAAE,KAAM,MAAO,QAAS,6BAAuE,EACpG,KAAM,CAAE,KAAM,OAAQ,QAAS,6BAAuE,CACzG,CACJ,EACD,qBAAsB,CAClB,CACI,MAAO,SACP,OAAQ,gBACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,YACP,OAAQ,YACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,iBACP,OAAQ,iBACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,UACP,OAAQ,QACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,CACJ,EACD,eAAemE,EAAWW,EAAcV,EAAUC,EAAQC,EAAmB,OACzE,IAAIX,EAAI4B,EAER,MAAMO,EAA+BxB,EAAkB,CAAC,EAClDyB,GAAgBpC,EAAAQ,EAAU,gBAAV,YAAAR,EAAyB,SAAS,MAAM,GAC9D,GAAI,OAAOoC,GAAkB,SACzB,MAAAjC,EAAO,MAAM,gEAAgE,EACvE,IAAI,MAAM,gEAAgE,EAEpF,MAAMkC,EAAW3B,EAAO,OAAO,gBAAgB0B,CAAa,EAExD,OAAOD,EAA6B,OAAO,cAAc,MAAU,MAEnEzB,EAAO,OAAO,gBACdyB,EAA6B,OAAO,cAAc,MAAQ7C,EAAgC+C,EAAS,IAAI,GAG3G,MAAMC,EAAwB3B,EAAkB,CAAC,EACjD,OAAA2B,EAAsB,SAAWA,EAAsB,OAAS,CAAE,IACjEtC,EAAKsC,EAAsB,QAAQ,WAAatC,EAAG,SAAW,CAAA,GAC/DsC,EAAsB,OAAO,SAAS,MAAQ5B,EAAO,gBAAgB0B,CAAa,GAEjFR,EAAKjB,EAAkB,CAAC,GAAG,SAAWiB,EAAG,OAAS,CAAA,GAC5CjB,CACV,CACJ,EACD,cAAe,CACX,OAAQ,CAAC,4BAAmE,iCAA8E,EAC1J,cAAe,CACX,QAAS,CAAE,KAAM,cAAe,QAAS,iCAA+E,CAC3H,EACD,OAAQ,CACJ,OAAQ,CACJ,YAAa,CAAE,KAAM,KAAM,QAAS,iCAA+E,CACtH,CACJ,EACD,qBAAsB,CAClB,CACI,MAAO,SACP,OAAQ,SACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,eACP,OAAQ,eACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,oBACP,OAAQ,cACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,CACJ,EACD,eAAeH,EAAWW,EAAcV,EAAUC,EAAQC,EAAmB,CACzE,OAAAA,EAAkB,QAASG,GAAqB,CAExCA,EAAiB,YAAc,oCAC/BA,EAAiB,SAAWA,EAAiB,OAAS,CAAE,GACxDA,EAAiB,OAAO,YAAc,GAE1D,CAAa,EACMH,CACV,CACJ,EACD,cAAe,CACX,OAAQ,CAAC,4BAAmE,iCAA8E,EAC1J,cAAe,CACX,QAAS,CAAE,KAAM,cAAe,QAAS,iCAA+E,CAC3H,EACD,OAAQ,CACJ,OAAQ,CAEJ,MAAO,CAAE,KAAM,OAAS,EACxB,YAAa,CAAE,KAAM,KAAM,QAAS,iCAA+E,CACtH,CACJ,EACD,QAAS,CACL,MAAO,CACH,IAAK,CAAE,KAAM,OAAS,CACzB,CACJ,EACD,qBAAsB,CAClB,CACI,MAAO,SACP,OAAQ,SACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,eACP,OAAQ,eACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,oBACP,OAAQ,cACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,CACJ,EACD,eAAeH,EAAWW,EAAcV,EAAUC,EAAQC,EAAmB,CACzE,OAAAA,EAAkB,QAASG,GAAqB,CAExCA,EAAiB,YAAc,oCAC/BA,EAAiB,SAAWA,EAAiB,OAAS,CAAE,GACxDA,EAAiB,OAAO,YAAc,GAE1D,CAAa,EACMH,CACV,CACJ,EACD,sBAAuB,CAEnB,OAAQ,CAAC,8BAA4E,kCAA+E,8BAAuE,sBAAwD,EACnS,cAAe,CACX,QAAS,CAAE,KAAM,cAAe,QAAS,iCAA+E,CAC3H,EACD,OAAQ,CACJ,OAAQ,CACJ,MAAO,CAAE,KAAM,SAAW,EAC1B,YAAa,CAAE,KAAM,KAAM,QAAS,iCAA+E,EACnH,SAAU,CAAE,KAAM,aAAc,SAAU,QAAiC,EAC3E,GAAI,CAAE,KAAM,gBAAiB,QAAS,sBAAyD,EAC/F,GAAI,CAAE,KAAM,gBAAiB,QAAS,sBAAyD,CAClG,EACD,MAAO,CACH,GAAI,CAAE,KAAM,KAAM,QAAS,6BAAuE,CACrG,CACJ,EACD,QAAS,CACL,MAAO,CACH,IAAK,CAAE,KAAM,QAAS,QAAS,6BAAuE,EACtG,IAAK,CAAE,KAAM,MAAO,QAAS,6BAAuE,EACpG,KAAM,CAAE,KAAM,OAAQ,QAAS,6BAAuE,CACzG,CACJ,EACD,qBAAsB,CAClB,CACI,MAAO,SACP,OAAQ,SACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,eACP,OAAQ,eACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,uBACP,OAAQ,6BACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,YACP,OAAQ,YACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,iBACP,OAAQ,iBACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,UACP,OAAQ,QACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,CACJ,EACD,eAAeH,EAAWW,EAAcV,EAAUC,EAAQC,EAAmB,CACzE,OAAAA,EAAkB,QAASG,GAAqB,CAExCA,EAAiB,YAAc,mCAC/BA,EAAiB,SAAWA,EAAiB,OAAS,CAAE,GACxDA,EAAiB,OAAO,YAAc,IAEjCA,EAAiB,YAAc,gCACpCA,EAAiB,SAAWA,EAAiB,OAAS,CAAE,GACxD,OAAO,KAAKN,EAAU,QAAU,CAAA,CAAE,EAAE,QAASF,GAAQ,OACjD,MAAMjE,GAAQ2D,EAAAQ,EAAU,SAAV,YAAAR,EAAmBM,GACjC,GAAIA,IAAQ,SAAWjE,EAAO,CAE1B,MAAMgF,EAAOhF,EAAM,KACfgF,IAAS,SACTP,EAAiB,OAAO,cAAgBJ,EAAO,OAAO,MAAMW,CAAI,EAAE,cAEzE,CACzB,CAAqB,EAErB,CAAa,EACMV,CACV,CACJ,EACD,kBAAmB,CACf,OAAQ,CAAC,8BAAuE,2BAAiE,6CAA6C,EAC9L,OAAQ,CACJ,OAAQ,CACJ,UAAW,CAAE,KAAM,QAAS,SAAU,SAAU,QAAS,0BAAiE,EAC1H,MAAO,CAAE,KAAM,QAAS,SAAU,QAAU,EAE5C,UAAW,CAAE,KAAM,OAAQ,SAAU,SAAU,gBAAiB,CAAC4B,EAAM7B,IAAW,CAAC6B,EAAK,CAAC,EAAI7B,EAAO,QAAQ,OAAO,SAAS,CAAG,EAC/H,QAAS,CAAE,KAAM,KAAM,SAAU,SAAU,gBAAiB,CAAC6B,EAAM7B,IAAW,CAAC6B,EAAK,CAAC,EAAI7B,EAAO,QAAQ,OAAO,SAAS,CAAG,CAC9H,CACJ,EACD,QAAS,CACL,MAAO,CACH,IAAK,CAAE,KAAM,OAAS,CACzB,CACJ,EACD,qBAAsB,CAClB,CACI,MAAO,iBACP,OAAQ,QACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,QACP,OAAQ,kBACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,CACJ,EACD,eAAeQ,EAAYC,EAAcV,EAAUoB,EAASlB,EAAmB6B,EAAUC,EAAY,CAEjG,MAAM3B,EAAmBH,EAAkBA,EAAkB,OAAS,CAAC,EACvE,OAAAG,EAAiB,SAAWA,EAAiB,OAAS,CAAE,GACxDA,EAAiB,OAAO,KAAO2B,EACxB9B,CACV,CACJ,EACD,iBAAkB,CACd,OAAQ,CAAC,8BAAuE,2BAAiE,6CAA6C,EAC9L,OAAQ,CACJ,OAAQ,CACJ,UAAW,CAAE,KAAM,QAAS,SAAU,SAAU,QAAS,0BAAiE,CAC7H,CACJ,EACD,QAAS,CACL,MAAO,CACH,IAAK,CAAE,KAAM,OAAS,CACzB,CACJ,EACD,qBAAsB,CAClB,CACI,MAAO,iBACP,OAAQ,QACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,QACP,OAAQ,kBACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,CACJ,EACD,eAAeO,EAAYC,EAAcV,EAAUoB,EAASlB,EAAmB6B,EAAUC,EAAY,CAEjG,MAAM3B,EAAmBH,EAAkBA,EAAkB,OAAS,CAAC,EACvE,OAAAG,EAAiB,SAAWA,EAAiB,OAAS,CAAE,GACxDA,EAAiB,OAAO,KAAO2B,EACxB9B,CACV,CACJ,EACD,mBAAoB,CAChB,OAAQ,CAAC,8BAAuE,2BAAiE,6CAA6C,EAC9L,cAAe,CAAE,EACjB,OAAQ,CACJ,OAAQ,CACJ,UAAW,CAAE,KAAM,QAAS,SAAU,SAAU,QAAS,0BAAiE,EAC1H,SAAU,CAAE,KAAM,cAAe,SAAU,SAAU,gBAAiB,CAAC4B,EAAM7B,IAAW,CAAC6B,EAAK,CAAC,EAAI7B,EAAO,QAAQ,OAAO,SAAS,CAAG,CACxI,CACJ,EACD,QAAS,CACL,MAAO,CACH,IAAK,CAAE,KAAM,OAAS,CACzB,CACJ,EACD,qBAAsB,CAClB,CACI,MAAO,iBACP,OAAQ,QACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,QACP,OAAQ,kBACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,CACJ,EACD,eAAeQ,EAAYC,EAAcV,EAAUoB,EAASlB,EAAmB6B,EAAUC,EAAY,CAEjG,MAAM3B,EAAmBH,EAAkBA,EAAkB,OAAS,CAAC,EACvE,OAAAG,EAAiB,SAAWA,EAAiB,OAAS,CAAE,GACxDA,EAAiB,OAAO,KAAO2B,EACxB9B,CACV,CACJ,EACD,cAAe,CACX,OAAQ,CAAC,0BAAgE,EACzE,cAAe,CACX,MAAO,CAAE,KAAM,QAAS,UAAW,GAAM,aAAc,EAAI,CAC9D,EACD,OAAQ,CACJ,OAAQ,CACJ,UAAW,CAAE,KAAM,MAAQ,CAC9B,CACJ,EACD,WAAWH,EAAW,CAClB,GAAIA,EAAU,eAAiBA,EAAU,cAAc,MAAO,CAC1D,MAAMwB,EAAQxB,EAAU,cAAc,MAAM,MAK5C,GAAI,CAJiBwB,EAAM,MAAOC,GAEvB,OAAOA,GAAc,UAAY,QAAQ,KAAKA,EAAU,SAAQ,CAAE,CAC5E,EAEG,OAAAzB,EAAU,cAAc,MAAM,MAAQ,CAAA,EAC/B,GAGX,MAAM0B,EAAc,IAAI,IAAIF,CAAK,EACjCxB,EAAU,cAAc,MAAM,MAAQ,MAAM,KAAK0B,CAAW,CAC/D,CACD,MAAO,EACV,EACD,eAAehB,EAAYC,EAAcV,EAAUoB,EAASlB,EAAmB,CAE3E,OADyBA,EAAkB,CAAC,EAC3B,WAAW,QAASa,GAAU,CACvCA,EAAM,OAAS,WAAaA,EAAM,OAAS,SAC3CA,EAAM,KAAO,MAAQA,EAAM,KAE/C,CAAa,EACMb,CACV,CACJ,EACD,YAAa,CACT,OAAQ,CAAC,0BAAgE,EACzE,cAAe,CACX,QAAS,CAAE,KAAM,kBAAmB,UAAW,EAAM,CACxD,CACJ,CACL,EACA,SAASM,EAAsBI,EAAMjB,EAAS,CAAC,GAAG,EAAGsC,EAAW,CAC5D,MAAO,CACH,OAAQ,CAACrB,CAAI,EACb,OAAQ,CACJ,OAAQjB,EAAO,OAAO,CAACuC,EAAKnB,KACxBmB,EAAInB,CAAK,EAAI,CAAE,KAAMA,CAAK,EACnBmB,GACR,EAAE,CACR,EACD,QAAS,CACL,OAAQ,CACJ,MAAO,CAAE,KAAM,OAAS,CAC3B,CACJ,EACD,eAAezB,EAAYC,EAAcV,EAAUW,EAAST,EAAmB,CAC3E,GAAI+B,EAAW,CAEX/B,EAAkB,CAAC,EAAE,OAASA,EAAkB,CAAC,EAAE,QAAU,GAG7D,IAAIU,EAAO,GACX,OAAO,KAAKH,EAAW,QAAU,CAAA,CAAE,EAAE,KAAM7E,GAAU,OACjD,QAAI2D,EAAAkB,EAAW,SAAX,YAAAlB,EAAoB3D,GAAO,QAAS,QACpCgF,EAAOH,EAAW,OAAO7E,CAAK,EAAE,KACzB,IAEJ,EAC3B,CAAiB,EACGgF,IAAS,KACTV,EAAkB,CAAC,EAAE,OAAO,KAAOS,EAAQ,OAAO,MAAMC,CAAI,EAAE,cAErE,CACD,OAAOV,CACV,CACT,CACA", "x_google_ignoreList": [0, 1, 2, 3]}