{"version": 3, "mappings": ";m5BAOU,MAAAA,GAAA,kBAAAC,EAA+B,wIA6EnCC,EAAW,+DARTA,EAAU,qPAQZA,EAAW,0QARTA,EAAU,0VAQZA,EAAW,mhBAVZA,EAAO,IAAAC,GAAAD,CAAA,0EAAPA,EAAO,8VAjEA,MAAAE,CAAA,EAAAC,EACA,WAAAC,CAAA,EAAAD,EACA,QAAAE,CAAA,EAAAF,EACA,YAAAG,CAAA,EAAAH,EACA,UAAAI,CAAA,EAAAJ,EACA,OAAAK,CAAA,EAAAL,EAEA,SAAAM,CAAA,EAAAN,EACA,cAAAO,CAAA,EAAAP,EACA,KAAAQ,CAAA,EAAAR,EACA,SAAAS,CAAA,EAAAT,QAELU,EAAK,CAAAC,EAAYC,EAAWC,IAC7B,gBAAY,eAAiB,OAAU,IAAAF,EAAI,KAAMC,EAAG,MAAOC,CAAA,aAEvDC,EACRb,GAuBO,OArBD,IAAqB,MAAMA,GAChC,UAAUc,EAASC,GAAA,CAEZZ,aAAeW,KAAWC,EAAI,EAC9BC,GAAQ,OAAO,KAAKb,GAAS,GAAG,KAAK,WAElCc,GAAOD,GACE,gBAAAE,EAAA,CACX,IAAAjB,EAAA,aACCkB,EAAKV,EAAEF,EAAKS,EAAOE,CAAQ,EACjCjB,EAAO,cAAckB,CAAE,GAGzB,OAAAH,GAAM,QAASJ,GAAA,CACdjB,GAAkB,KAAW,IAAAD,GAAKS,GAAUS,EAAGK,GAAOL,CAAC,MAGjDT,MAON,IAAAiB,EAAaP,EAAKb,CAAS,EAEzB,MAAAqB,EAAA,CACL,cACA,OACA,QACA,cACA,QACA,kBAGQC,EAAeC,EAAA,WACZC,KAAOD,EACbF,EAAgB,SAASG,CAAa,IACzCD,EAAIC,CAAG,EAAIC,GAAoBF,EAAIC,CAAG,8CAY7BrB,EAAQuB,mfAPjBJ,EAAeK,CAAW,gBAC1BC,EAAA,EAAAxB,EAAQqB,GAAoBrB,CAAK,+7CCrE3B,CAAAyB,WAAAC,sCAAkD,iIAkFrD,KAAAlC,MAAM,EAAE,EACF,WAAAA,KAAK,SAAS,GAGf,oBAAaA,EAAK,UAASA,EAAK,SAAM,SAClC,aAAAA,KAAK,EAAE,kBACN,iBAAkBA,EAAK,UAASA,EAAI,GAAC,MAAM,cAAY,kBAGlEA,KAAK,qCAGO,eAAAA,EAAK,SAAM,SAAY,UACpCA,EAAI,GAAC,MAAM,QACX,2IAZY,OAAAA,KAAK,WAAQ,SAAbmC,EAAA,SAAAnC,KAAK,UACRA,EAAI,GAAC,MAAM,QAAK,iBAAhBA,EAAI,GAAC,MAAM,iKAHlBoC,EAAA,QAAApC,MAAM,EAAE,EACFoC,EAAA,cAAApC,KAAK,SAAS,QAGf,oBAAaA,EAAK,UAASA,EAAK,SAAM,SAClC,aAAAA,KAAK,EAAE,uBACN,iBAAkBA,EAAK,UAASA,EAAI,GAAC,MAAM,cAAY,uBAGlEoC,EAAA,GAAAC,GAAArC,KAAK,KAAK,gDAGE,eAAAA,EAAK,SAAM,SAAY,UACpCA,EAAI,GAAC,MAAM,QACX,8DAZYsC,EAAA,SAAAtC,KAAK,8CACRA,EAAI,GAAC,MAAM,uJAcfuC,EAAAC,GAAAxC,KAAK,QAAQ,EAAW,MAAAyC,EAAAzC,SAAM,mBAAnC,OAAI0C,GAAA,2LAACH,EAAAC,GAAAxC,KAAK,QAAQ,sFAAlB,OAAI0C,GAAA,gLAEE1C,EAAK,IACA,UAAAA,MAAM,sBAEb,GAAAA,MAAM,4OAHJA,EAAK,KACAoC,EAAA,IAAAO,EAAA,UAAA3C,MAAM,gCAEboC,EAAA,IAAAO,EAAA,GAAA3C,MAAM,gOANR4C,EAAA5C,KAAK,UAAYA,EAAK,YAAS,QAAM6C,GAAA7C,CAAA,wEAArCA,KAAK,UAAYA,EAAK,YAAS,4MAlBjC4C,EAAA5C,KAAK,WAASC,GAAAD,CAAA,0EAAdA,KAAK,sOA1EE,SAAAE,CAAA,EAAAC,EAEA,MAAA2C,CAAA,EAAA3C,GACA,OAAA4C,EAAwB,MAAA5C,EACxB,QAAAE,CAAA,EAAAF,EACA,YAAAG,CAAA,EAAAH,EACA,SAAA6C,CAAA,EAAA7C,EACA,YAAA8C,CAAA,EAAA9C,EACA,eAAA+C,CAAA,EAAA/C,EACA,QAAAgD,CAAA,EAAAhD,QAELiD,EAAWlB,KACb,IAAAmB,EAAA,GAEJpB,GAAA,KACCmB,EAAS,QAASN,EAAK,EAAE,YAEdQ,KAASD,EACnBD,EAAS,QAASE,EAAM,EAAE,aAI1BF,EAAS,UAAWN,EAAK,EAAE,YAEhBQ,KAASD,EACnBD,EAAS,QAASE,EAAM,EAAE,KAmB7BC,GAAW,YAAaR,CAAM,gFAkCdS,EAAA,aAAAV,EAAK,SAAQtC,CAAA,IAAbsC,EAAK,SAAQtC,uGAChBsC,EAAK,MAAM,MAAKtC,CAAA,IAAhBsC,EAAK,MAAM,MAAKtC,maAhDxBsC,OACHA,EAAK,SACJA,EAAK,UACLA,EAAK,SAAS,OAAQ9B,GAAA,CACf,MAAAyC,EAAaX,EAAK,OAAS,gBAC5B,OAAAW,GACJJ,EAAkB,KAAKrC,CAAC,EAElByC,qBAQNX,GAAQA,EAAK,OAAS,SAExBA,EAAK,UAAU,MACbY,GAAa,OAAAA,EAAE,MAAM,SAAY,YAAcA,EAAE,MAAM,SAGzD1B,EAAA,EAAAc,EAAK,MAAM,QAAU,GAAAA,CAAA,EAErBd,EAAA,EAAAc,EAAK,MAAM,QAAU,GAAAA,CAAA,mBAKxBd,EAAA,EAAGc,EAAK,MAAM,OAAa,IAAAa,GAC1Bb,EAAK,GACLzC,EACAC,EACA0C,EACA9C,EACA+C,EACAC,EACAU,EACAT,EACAU,EAAA,EAAAf,CAAA,opCC7EQ,SAAAb,GAAA,sBAAAC,IAAsC,sFAqBxClC,EAAQ,sLAARA,EAAQ,gSAFXA,EAAQ,IAAAC,GAAAD,CAAA,0EAARA,EAAQ,oMAfD,aAAA8D,CAAA,EAAA3D,EACA,MAAAD,CAAA,EAAAC,EACA,QAAAE,CAAA,EAAAF,EACA,YAAAG,CAAA,EAAAH,EACA,SAAA6C,CAAA,EAAA7C,EACA,YAAA8C,CAAA,EAAA9C,GACA,cAAA+C,EAA+B,MAAA/C,EAC/B,QAAAgD,CAAA,EAAAhD,QAELiD,EAAWlB,KACjBD,UAAA,KACCmB,EAAS,OAAO,qmCChBlB,MAAeW,GAAA,0yCCAAC,GAAA,80DCAAC,GAAA,qjCCAAC,GAAA,01CCEf,IAAIC,GAAc,GACdC,GAAsC,KACtCC,GAAyB,GACzBC,GAAqB,EAErBC,GAAkD,GAClDrE,GAEAsE,GAKAC,GAAkE,KAClEC,GAIE,GAEU,SAAAC,GACfC,EACAC,EAKAC,EACO,CACA5E,GAAA0E,EACgBJ,GAAAK,EACnBC,IACsBL,GAAAK,EAE3B,CAEA,eAAsBC,IAAgC,CACrD,GAAI,CAAAZ,GAIA,IACH,MAAMa,EAAgB,SAAS,MAC/B,SAAS,MAAQ,uBACjB,MAAMC,EAAS,MAAM,UAAU,aAAa,gBAAgB,CAC3D,MAAO,CACN,MAAO,CAAE,MAAO,IAAK,EACrB,OAAQ,CAAE,MAAO,IAAK,EACtB,UAAW,CAAE,MAAO,EAAG,CACxB,EACA,MAAO,GACP,mBAAoB,UACM,EAC3B,SAAS,MAAQD,EAEjB,MAAME,EAAU,CACf,mBAAoB,KAGLd,GAAA,IAAI,cAAca,EAAQC,CAAO,EAEjDb,GAAiB,GACjBE,GAAgB,GAEhBH,GAAc,gBAAkBe,GAChCf,GAAc,OAASgB,GAEvBhB,GAAc,MAAM,GAAI,EACVD,GAAA,GACVM,IACHA,GAAuB,EAAI,EAE5BH,GAAqB,KAAK,YAClBe,EAAY,CACpBb,GACC,kBACA,8BAAgCa,EAAM,QACtC,QAEF,CACD,CAEO,SAASC,IAAsB,CACjC,CAACnB,IAAe,CAACC,KAIrBA,GAAc,KAAK,EACLD,GAAA,GACVM,IACHA,GAAuB,EAAK,EAE9B,CAMO,SAASc,IAA+B,CAC9C,GAAI,CAACpB,GACJ,OAGD,MAAMqB,GAAe,KAAK,IAAI,EAAIlB,IAAsB,IACxDC,GAAc,MAAQiB,CACvB,CAEO,SAASC,IAA6B,CAC5C,GAAI,CAACtB,IAAeI,GAAc,QAAU,OAC3C,OAGD,MAAMiB,GAAe,KAAK,IAAI,EAAIlB,IAAsB,IACxDC,GAAc,IAAMiB,CACrB,CAMgB,SAAAE,GACfC,EACAC,EAOO,CACP,GAAI,CAACzB,GACJ,OAGD,MAAM0B,EAAM,GACNL,GAAe,KAAK,IAAI,EAAIlB,IAAsB,IAClDwB,EACH,KAAK,MADaH,GACNH,EAAc,GAAKK,EACpBL,EAAcK,CADS,EAGrC,GACCD,EAAO,aACPA,EAAO,YAAY,SACnBA,EAAO,YAAY,aACnBA,EAAO,YAAY,QAAQ,SAAW,GACtCA,EAAO,YAAY,YAAY,SAAW,EACzC,CACK,MAAAG,EAAoBH,EAAO,UAAY,EACvCI,EACLF,EAAe,KAAK,MAAMC,EAAoBF,CAAG,EAE/BnB,GAAY,KAAMuB,GAAmB,CACjD,MAAAC,EACLD,EAAe,YACf,KAAK,OAAOA,EAAe,UAAY,GAAOJ,CAAG,EAClD,OACEC,GAAgBG,EAAe,aAC/BH,GAAgBI,GAChBF,GAAqBC,EAAe,aACpCD,GAAqBE,GACrBJ,GAAgBG,EAAe,aAC/BD,GAAqBE,CAAA,CAEvB,GAGAxB,GAAY,KAAK,CAChB,YAAakB,EAAO,YACpB,YAAaE,EACb,SAAUC,CAAA,CACV,CAEH,CACD,CAEO,SAASI,GACfR,EACAS,EACAC,EAAW,EACJ,CACP,GAAKlC,GAID,IACH,WAAW,IAAM,CAChB,GAAI,CAACiC,GAAYA,EAAS,SAAW,EACpC,OAGD,IAAIE,EAAU,IACVC,EAAS,IACTC,EAAW,EACXC,EAAY,EACZC,EAAgB,GAEpB,UAAWC,KAAaP,EAAU,CAC3B,MAAAQ,EAAW,cAAcD,CAAS,GAClCE,EAAU,SAAS,cAAcD,CAAQ,EAE/C,GAAIC,EAAS,CACIH,EAAA,GACV,MAAAI,EAAOD,EAAQ,wBAErBP,EAAU,KAAK,IAAIA,EAASQ,EAAK,IAAI,EACrCP,EAAS,KAAK,IAAIA,EAAQO,EAAK,GAAG,EAClCN,EAAW,KAAK,IAAIA,EAAUM,EAAK,KAAK,EACxCL,EAAY,KAAK,IAAIA,EAAWK,EAAK,MAAM,CAC5C,CACD,CAEA,GAAI,CAACJ,EACJ,OAGD,MAAMK,EAAgB,OAAO,WACvBC,EAAiB,OAAO,YAExBC,EAAW,KAAK,IAAIT,EAAUO,CAAa,EAAI,KAAK,IAAI,EAAGT,CAAO,EAClEY,EACL,KAAK,IAAIT,EAAWO,CAAc,EAAI,KAAK,IAAI,EAAGT,CAAM,EAEnDY,EAAkBF,EAAWF,EAC7BK,EAAmBF,EAAYF,EAEjC,GAAAG,GAAmB,IAAOC,GAAoB,GACjD,OAGD,MAAMC,EAAW,iCAAiC,KACjD,UAAU,WAGX,IAAIC,EAA4B,CAC/B,KAAK,IAAI,EAAGhB,CAAO,EAAIS,EACvB,KAAK,IAAI,EAAGR,CAAM,EAAIS,CAAA,EAGnBO,EAAgC,CACnC,KAAK,IAAIf,EAAUO,CAAa,EAAIA,EACpC,KAAK,IAAIN,EAAWO,CAAc,EAAIA,CAAA,EAGvC,GAAIK,EAAU,CACLC,EAAA,CAAC,EAAI,KAAK,IAAI,EAAGA,EAAQ,CAAC,EAAI,EAAG,EAC7BC,EAAA,CAAC,EAAI,KAAK,IAAI,EAAGA,EAAY,CAAC,EAAI,EAAG,EACjD,MAAMC,EAAQD,EAAY,CAAC,EAAID,EAAQ,CAAC,EAElCG,GADUH,EAAQ,CAAC,EAAIC,EAAY,CAAC,GAAK,EACpB,GAC3BD,EAAQ,CAAC,EAAI,KAAK,IAAI,EAAGG,EAAYD,EAAQ,CAAC,EAC9CD,EAAY,CAAC,EAAI,KAAK,IAAI,EAAGE,EAAYD,EAAQ,CAAC,CACnD,CAEAF,EAAQ,CAAC,EAAI,KAAK,IAAI,EAAGA,EAAQ,CAAC,CAAC,EACnCA,EAAQ,CAAC,EAAI,KAAK,IAAI,EAAGA,EAAQ,CAAC,CAAC,EACnCC,EAAY,CAAC,EAAI,KAAK,IAAI,EAAGA,EAAY,CAAC,CAAC,EAC3CA,EAAY,CAAC,EAAI,KAAK,IAAI,EAAGA,EAAY,CAAC,CAAC,EAE3C7B,GAAcC,EAAU,CACvB,YAAa,CACZ,QAAA2B,EACA,YAAAC,CACD,EACA,SAAAlB,CAAA,CACA,GACC,GAAG,OACS,CAEhB,CACD,CAEA,SAASlB,GAAoBuC,EAAwB,CAChDA,EAAM,KAAK,KAAO,GACNrD,GAAA,KAAKqD,EAAM,IAAI,CAEhC,CAEA,SAAStC,IAAmB,CACbjB,GAAA,GACVM,IACHA,GAAuB,EAAK,EAGvB,MAAAkD,EAAO,IAAI,KAAKtD,GAAgB,CACrC,KAAM,YACN,EAEDuD,GAAwBD,CAAI,GAEPvD,IAAe,QAAQ,aAAe,IAC9C,QAASyD,GAAUA,EAAM,KAAM,EAM7C,CAEA,eAAeD,GAAwBE,EAAmC,CACrE,IACHtD,GACC,mBACA,iCACA,QAGK,MAAAuD,EAAW,IAAI,SACZA,EAAA,OAAO,QAASD,EAAc,eAAe,EAElDvD,GAAc,QAAU,QAAaA,GAAc,MAAQ,SAC9DwD,EAAS,OAAO,uBAAwBxD,GAAc,MAAM,UAAU,EACtEwD,EAAS,OAAO,qBAAsBxD,GAAc,IAAI,UAAU,GAG/DG,GAAY,OAAS,GACxBqD,EAAS,OAAO,eAAgB,KAAK,UAAUrD,EAAW,CAAC,EAG5D,MAAMsD,EAAW,MAAM,MAAM9H,GAAO,gCAAiC,CACpE,OAAQ,OACR,KAAM6H,CAAA,CACN,EAEG,IAACC,EAAS,GACb,MAAM,IAAI,MACT,mBAAmBA,EAAS,MAAM,KAAKA,EAAS,UAAU,IAItD,MAAAC,EAAgB,MAAMD,EAAS,OAC/BE,EAAkB,2BAA+B,WAAO,cAAc,QAAQ,KAAM,GAAG,EAAE,QAAQ,OAAQ,EAAE,CAAC,OAClHC,GAA0BF,EAAeC,CAAe,EACxDxD,GAAc,QACC,CACfF,GACC,mBACA,wDACA,WAGD,MAAM0D,EAAkB,2BAA+B,WAAO,cAAc,QAAQ,KAAM,GAAG,EAAE,QAAQ,OAAQ,EAAE,CAAC,OAClHC,GAA0BL,EAAcI,CAAe,CACxD,CACD,CAEA,SAASC,GAA0BR,EAAYS,EAA6B,CACrE,MAAAC,EAAM,IAAI,gBAAgBV,CAAI,EAC9BW,EAAI,SAAS,cAAc,GAAG,EACpCA,EAAE,MAAM,QAAU,OAClBA,EAAE,KAAOD,EACTC,EAAE,SAAWF,EAEJ,cAAK,YAAYE,CAAC,EAC3BA,EAAE,MAAM,EACR,WAAW,IAAM,CACP,cAAK,YAAYA,CAAC,EAC3B,IAAI,gBAAgBD,CAAG,GACrB,GAAG,CACP,8fCvWU,MAAAE,GAAA,QAAAtG,IAAqB,uEAy8BrBjC,EAAK,qDAGMwI,GAAWxI,EAAG,IAAEA,EAAO,uHAAvBwI,GAAWxI,EAAG,IAAEA,EAAO,iBAAAyI,EAAA,EAAAC,CAAA,kFAQ9B1I,EAAQ,mGAQVA,EAAG,qBAJDA,EAAY,8FAJZA,EAAQ,oMAQVA,EAAG,yHAiCV2I,EAAA3I,MAAG,0BAA0B,uBAW7B4I,EAAA5I,MAAG,uBAAuB,uBAa1B6I,GAAA7I,MAAG,iBAAiB,yBAlDjBA,EAAQ,IAAA8I,GAAA9I,CAAA,wPA2BF+D,EAAI,GAAAgF,EAAAC,EAAA,MAAAC,CAAA,EAAOF,EAAAC,EAAA,MAAAE,EAAAlJ,MAAG,aAAa,4MAEFA,EAAoB,gBAS7CkE,EAAW,GAAA6E,EAAAI,EAAA,MAAAC,CAAA,EAAOL,EAAAI,EAAA,MAAAE,EAAArJ,MAAG,uBAAuB,mFAPvCA,EAAoB,sDAoBzBiE,EAAa,GAAA8E,EAAAO,EAAA,MAAAC,EAAA,EAAOR,EAAAO,EAAA,MAAAE,GAAAxJ,MAAG,iBAAiB,2GApDpDyJ,EAsDQpJ,EAAAqJ,EAAAC,CAAA,wBAjCPC,EAQGF,EAAApB,CAAA,gBADFsB,EAAyCtB,EAAAU,CAAA,SAE1CY,EAAgEF,EAAAG,CAAA,SAChED,EASQF,EAAAI,CAAA,gBADPF,EAA0DE,EAAAX,CAAA,SAE3DS,EAA2BF,EAAAK,CAAA,SAC3BH,EAWQF,EAAAM,CAAA,gBADPJ,EAAsDI,EAAAV,CAAA,4FAnDlDtJ,EAAQ,0DA0BXoC,EAAA,cAAAuG,OAAA3I,MAAG,0BAA0B,OAAAiK,GAAAC,EAAAvB,CAAA,EACTvG,EAAA,cAAA8G,OAAAlJ,MAAG,aAAa,gDAEFA,EAAoB,KAQtDoC,EAAA,cAAAwG,OAAA5I,MAAG,uBAAuB,OAAAiK,GAAAE,EAAAvB,CAAA,EACCxG,EAAA,cAAAiH,OAAArJ,MAAG,uBAAuB,gDAPvCA,EAAoB,KAmBlCoC,EAAA,cAAAyG,SAAA7I,MAAG,iBAAiB,OAAAiK,GAAAG,EAAAvB,EAAA,EACSzG,EAAA,cAAAoH,SAAAxJ,MAAG,iBAAiB,oHAxC5CA,EAAG,IAAC,QAAQ,WAAUqK,sHAKjBrG,EAAQ,GAAA+E,EAAAuB,EAAA,MAAAC,CAAA,EAAOxB,EAAAuB,EAAA,MAAAE,EAAAxK,MAAG,aAAa,oIAf1CyJ,EAgBQpJ,EAAAoK,EAAAd,CAAA,qBADPC,EAA6Ca,EAAAH,CAAA,WAE9Cb,EAA4CpJ,EAAAqK,EAAAf,CAAA,kIAFlBvH,EAAA,cAAAoI,OAAAxK,MAAG,aAAa,6EAFvC,IAAA2K,EAAA3K,MAAG,oBAAoB,mDAAvBoC,EAAA,cAAAuI,OAAA3K,MAAG,oBAAoB,OAAAiK,GAAA,EAAAU,CAAA,iCAFvB,IAAAA,EAAA3K,MAAG,2BAA2B,mDAA9BoC,EAAA,cAAAuI,OAAA3K,MAAG,2BAA2B,OAAAiK,GAAA,EAAAU,CAAA,qDAwDX3K,EAAW,4MAPpCyJ,EAQKpJ,EAAAqK,EAAAf,CAAA,uFADoB3J,EAAW,iYAiB3BA,EAAO,4CACFA,EAAQ,wUAbtByJ,EA4BKpJ,EAAAuK,EAAAjB,CAAA,EAxBJC,EAKCgB,EAAAf,CAAA,SACDD,EAiBKgB,EAAAb,CAAA,uFAfG/J,EAAO,iNACFA,EAAQ,gYA+BbA,EAAQ,uCASDA,EAAG,IAAC,OAAO,sdArB3ByJ,EA0BKpJ,EAAAuK,EAAAjB,CAAA,EAtBJC,EAKCgB,EAAAf,CAAA,SACDD,EAeKgB,EAAAb,CAAA,wFAbG/J,EAAQ,2TASDA,EAAG,IAAC,OAAO,oXASCA,EAAkB,gPAlKzCA,EAAkB,IAAA6K,GAAA7K,CAAA,IAGlBA,EAAG,KAAA8K,GAAA9K,CAAA,IAOFA,EAAQ,KAAIA,EAAG,IAAC,QAAM+K,GAAA/K,CAAA,IAevBA,EAAW,IAAAgL,GAAAhL,CAAA,EA2DZiL,EAAAjL,OAAwBA,EAAW,KAAAkL,GAAAlL,CAAA,IAenCA,EAAgB,KAAIA,EAAQ,KAAIA,EAAO,KAAAmL,GAAAnL,CAAA,EAgCvCoL,EAAApL,OAAoBA,EAAQ,KAAIA,EAAI,YAAUA,EAAQ,KAAA6C,GAAA7C,CAAA,IA8BtDA,EAAQ,KAAAC,GAAAD,CAAA,0NAxJ0BA,EAAQ,GAAG,IAAM,MAAM,sDAD1BA,EAAQ,GAAG,OAAS,MAAM,+GAA9DyJ,EA0EKpJ,EAAA0J,EAAAJ,CAAA,EAzEJC,EAcKG,EAAAF,CAAA,iJAvBA7J,EAAkB,6DAGlBA,EAAG,sEAOFA,EAAQ,KAAIA,EAAG,IAAC,0IADgBA,EAAQ,GAAG,IAAM,MAAM,EAgBxDA,EAAW,yFAjBmBA,EAAQ,GAAG,OAAS,MAAM,EA4EzDA,OAAwBA,EAAW,sHAenCA,EAAgB,KAAIA,EAAQ,KAAIA,EAAO,sHAgCvCA,OAAoBA,EAAQ,KAAIA,EAAI,YAAUA,EAAQ,uHA8BtDA,EAAQ,kVAj1BNqL,GAAmB,aAInBC,GACL,gEACKC,GACL,sGACKC,GAAuB,6BACvBC,GACL,yIAEKC,GAAgC,GAChCC,GAAmC,YAunBhCC,GAAclE,EAAA,OACf,WAAYA,2DA33BT,SAAAxH,CAAA,EAAAC,EACA,YAAA0L,CAAA,EAAA1L,EACA,QAAA2L,CAAA,EAAA3L,EACA,cAAA4L,CAAA,EAAA5L,GACA,MAAA6L,EAAQ,UAAA7L,EACR,QAAAE,CAAA,EAAAF,EACA,YAAA8C,CAAA,EAAA9C,GACA,SAAA8L,EAAW,IAAA9L,GACX,YAAA+L,EAAc,IAAA/L,GACd,mBAAAgM,EAAqB,IAAAhM,EACrB,UAAAiM,CAAA,EAAAjM,EACA,YAAAG,CAAA,EAAAH,EACA,KAAAkM,CAAA,EAAAlM,EACA,UAAAmM,CAAA,EAAAnM,EACA,SAAA6C,CAAA,EAAA7C,EACA,IAAAoM,EAAA,EAAApM,GACA,YAAAqM,EAAc,IAAArM,EACd,OAAAsM,CAAA,EAAAtM,EACA,UAAAuM,CAAA,EAAAvM,GACA,WAAAwM,GAAa,IAAAxM,GACb,cAAA+C,GAAoC,QAAA/C,GACpC,eAAAyM,GAA4C,QAAAzM,GAC5C,IAAA0M,GAAiC,MAAA1M,EACxC2M,EAAoB,GAGvB,QAAQC,EACR,QAAAC,EACA,aAAAC,GACA,SAAAC,GACA,cAAAC,GACA,iBAAAC,GACA,eAAAC,GACA,eAAAC,GACA,kBAAAC,GACA,cAAAC,GACA,gBAAAC,GACA,aAAAC,EAAA,EACGC,GACH,gBAAAf,EAAA,6FASGgB,GAAmB7B,EAWR,eAAA8B,IAAA,OACRC,GAAUzB,EAAI,QAAQ,yBAA8B,OAE1D0B,GAAkB,IACZ,MAAAP,GAAA,CACL,WAAA3B,EACA,OAAAC,EACA,aAAAC,EACA,KAAM7L,EAAOyM,GACb,IAAAN,EACA,QACC,aAAAG,CAAA,SAGFuB,GAAkB,IAGR,kBAAAC,EAAA,EAAA7N,EACP8N,GAAmBD,GAAc,IAAI,MAAM,IAAM,OAAS/B,EAC1DiC,GAAmBF,GAAc,IAAI,MAAM,IAAM,WACjDG,GACHH,GAAc,IAAI,MAAM,IAAM,gBAAkB/B,EAC7CmC,GAAa,GACbC,GAAmB,GAGnBC,GAAyC,KACzCC,GAAiD,KACjDC,GAA2C,KAEhC,eAAAC,IAAA,KACTH,IAAY,CAAAC,GAAA,CACV,MAAAG,EAAA,MAAAC,GAAA,WAA+B,uBAA2B,0GAC1DC,EAAA,MAAAD,GAAA,WAAmC,2BAA+B,8EACnEL,IAAAtM,EAAA,GAASsM,GAAUI,EAAgB,SACnCH,IAAAvM,EAAA,GAAauM,GAAcK,EAAoB,UAIvC,eAAAC,IAAA,CACT,IAAAN,GAAA,CACE,MAAAK,EAAA,MAAAD,GAAA,WAAmC,2BAA+B,8EACxE3M,EAAA,GAAAuM,GAAcK,EAAoB,UAIrB,eAAAE,IAAA,CACT,IAAAN,GAAA,CACE,MAAAO,EAAA,MAAAJ,GAAA,WAA+B,wBAA4B,wGACjE3M,EAAA,GAAAwM,GAAWO,EAAgB,yBAIdC,GAAqBpO,EAAA,MACnCuN,GAAuB,IACnBvN,GACG,MAAA6N,GAAA,OAEPR,GAAmBrN,CAAA,MACfgF,EAAa,oBAAgB,OAAO,SAAS,MAAM,EACnDhF,EACHgF,EAAO,IAAI,OAAQ,KAAK,EAExBA,EAAO,OAAO,MAAM,EAErB,QAAQ,aAAa,KAAM,GAAI,IAAMA,EAAO,2BAG9BqJ,GAAqBrO,EAAA,CAC/BA,GACG,MAAAkO,GAAA,MAEHlJ,EAAa,oBAAgB,OAAO,SAAS,MAAM,EACnDhF,EACHgF,EAAO,IAAI,OAAQ,UAAU,EAE7BA,EAAO,OAAO,MAAM,EAErB,QAAQ,aAAa,KAAM,GAAI,IAAMA,EAAO,iBAC5CsI,GAAoB,CAAAA,EAAA,EAGjB,IAAAgB,GAAA,GAEAnB,GAAkB,IACX,gBAAAoB,GAAkB,IAAAhP,EAEd,eAAAiP,GAAcC,EAAWC,EAAA,CACjC,MAAAC,EAAMxD,EAAa,KAAMwD,GAAQA,EAAI,KAAOD,CAAQ,EACpDE,EAAa3D,EAAW,KAC5B4D,GAASA,EAAK,KAAOF,GAAK,OAAO,CAAC,CACjC,QAgBE,GAfDnB,IAAcmB,GAAOC,IAAe,YACnCD,GAAOA,EAAI,QAAUA,EAAI,OAAO,OAAS,GAAKG,GACjDC,GAAqB,GAAMJ,EAAI,OAAQ,CAAG,EAI1CA,GACAA,EAAI,SACJA,EAAI,QAAQ,OAAS,GACrBG,GAEAC,GAAqB,GAAOJ,EAAI,QAAS,CAAG,GAIzC,CAAAA,SAGC,MAAAK,EAAUL,EAAI,QACdM,EAAeR,GAAM,KAAK7O,EAAYkC,OAE1C,GAAIkN,EAAQlN,EAAC,EACb,KAAM,kBACN,MAAO,MAITuK,GAAa4C,CAAY,EAEnB,MAAAtH,GAAA,EAEA,MAAAuH,EAAA,GAENT,GAAM,SAAS7O,EAAYkC,KAAA,CAElB,UAAAlC,GAAU,UACjBA,IAAU,MACVA,EAAM,WAAa,SAEP,UAAAuP,GAAY9C,CAAY,IAAK,OAAO,QAAQzM,CAAK,EACxDuP,KAAe,YAGlBD,EAAQ,MACP,GAAIF,EAAQlN,EAAC,EACb,KAAMqN,GACN,MAAO9C,SAKV6C,EAAQ,MACP,GAAIF,EAAQlN,EAAC,EACb,KAAM,QACN,MAAAlC,CAAA,KAIHyM,GAAa6C,CAAO,EAEd,MAAAvH,GAAA,EAGH,IAAAyH,GAA6D,QAE7DC,EAAA,GACK,SAAAC,GACRlE,EACAmE,EACAb,EACAc,EACA/J,EAA0B,GAC1BzF,EAAU,WAGT,MAAAoL,EACA,QAAAmE,EACA,SAAAb,EACA,KAAAc,EACA,GAAM,EAAAC,GACN,SAAAhK,EACA,QAAAzF,GAIc,SAAAiE,GACfmH,EACAmE,EACAC,EAAA,CAEApO,EAAA,GAAAiO,EAAA,CAAYC,GAAYlE,EAAOmE,EAAA,GAAaC,CAAI,KAAMH,CAAQ,OAG3DI,GAAY,GAIV,MAAAC,GAAoBC,EAAG,4BAA4B,EACnDC,GAAuBD,EAAG,6BAA6B,EAQvDE,GAA6BF,EAAG,2BAA2B,MAG7DG,GAAmB,GACnBC,GAA2B,GAC3BC,GAAwB,GACxBC,GAAA,YAGKC,GACRC,EACAC,EAA4B,KAC5BC,EAAsB,MAElB,IAAAC,EAAA,OACK,SAAAC,GAAA,CACRD,IAEGE,EACHF,EAAS3D,GAAkB,UAAW8D,GAAA,CAChCA,GACJ9I,GAAO,YACN+I,GAAiBP,EAAWC,EAAYC,CAAU,EAClDE,QAKHG,GAAiBP,EAAWC,EAAYC,CAAU,EAIrC,eAAAM,GACdC,EACAR,EACAC,EAAA,QAGCO,IAAiBR,GACjBC,GACCA,EAAyB,gBAAkB,GAGrCA,EAAW,MAEZ/D,GAASsE,CAAY,iBAGdF,GACdP,EACAC,EAA4B,KAC5BC,EAAsB,MAEhB,MAAAQ,EAAO1F,EAAa,KAAMwD,GAAQA,EAAI,KAAOwB,CAAS,KACxDU,IAAS,oBAGPlC,EAAMkC,EACR,GAAAZ,GAAe,OAAS,aAChBa,KAASb,MACftB,EAAI,OAAO,SAASmC,CAAK,GAC5B7M,GAAgB,UAAW4L,GAA4B,SAAS,gBAK7DkB,EAAiBrE,GAAe,kBAAkByD,CAAS,EACjE/O,EAAA,GAAAiO,EAAWA,EAAS,SAAU,SAAAX,KAAeA,IAAayB,CAAS,IAC/DY,IAAmB,WAAaA,IAAmB,gBACtDpC,EAAI,gBAAkB,IAGnB,IAAAqC,EAAA,CACH,SAAUb,EACV,KAAY,cAAQ,IACnBxB,EAAI,OAAO,IAAKzO,GACfyQ,GAAkCzQ,EAAIkQ,EAAYC,CAAU,IAG9D,WAAY1B,EAAI,oBAAsB0B,EAAa,KACnD,WAAAD,GAGGzB,EAAI,oBAAsBA,EAAI,aAAgB,UACjDA,EACE,YACAqC,EAAQ,KAAK,aACN,QAAQ,IAAIrC,EAAI,QAAQ,IAAKzO,GAAOoM,GAASpM,CAAE,MAGtD,KAAME,GAAA,CACFuO,EAAI,YACPqC,EAAQ,KAAO5Q,EACf6Q,EAAmBtC,EAAKqC,CAAO,GAE/BxC,GAAcpO,EAAG+P,CAAS,IAGnBxB,EAAI,MAAM,QAAUA,EAAI,cAC5B,QAAQ,IACbA,EAAI,QAAQ,IAAW,MAAAD,GAAA,OAChBwC,EAAa9B,GAAW,IAAIV,CAAQ,EAC1C,OAAAwC,GAAY,SACLA,KAILvC,EAAI,aACHA,EAAI,mBACS,IAAAwC,GAAA,uBACQxC,EAAI,iBAAiB;AAAA,6DAGpC,GAAAqC,EAAQ,IAAI,EACnB,KAAMI,GAAA,CACN5C,GAAc4C,EAAWjB,CAAS,EAClCa,EAAQ,kBAAoB,KAE5B,MAAOvM,GAAA,CACP,QAAQ,MAAMA,CAAK,EACnBuM,EAAQ,kBAAoB,KAG/BC,EAAmBtC,EAAKqC,CAAO,GAIxB,SAAAC,EAAmBtC,EAAiBqC,GACxCrC,EAAI,eAAiB,OACnBA,EAAI,iBACR0C,GAAgBL,EAASrC,EAAI,YAAc,QAAQ,EAC1CA,EAAI,eAAiB,WAC/B0C,GAAgBL,EAASrC,EAAI,YAAc,QAAQ,EACzCA,EAAI,eAAiB,gBAC1BA,EAAI,gBAGRA,EAAI,YAAcqC,EAFlBK,GAAgBL,EAASrC,EAAI,YAAc,QAAQ,GAOvC,eAAA2C,IAAA,CACR,MAAAC,EAAA,MAA0B9F,EAAI,YAChC8F,IAAsB,SACzB,WAAWD,GAAW,GAAI,EAChBC,IAAsB,WAChCrF,EAAoB,GACpB9K,EAAA,GAAAiO,EAAA,CACCC,GACC,qBACA3E,GAAA,GAEA,OACA,EACA,OAEE0E,EAAS,IAAKmC,GAChBA,EAAE,UAAY9G,GAA+B,IAAA8G,EAAG,QAAS,IAAUA,CAAA,KAG3DD,IAAsB,cAChCrF,EAAoB,GACpB9K,EAAA,GAAAiO,EAAA,CACCC,GACC,cACA1E,GAAA,GAEA,UACA,KACA,OAEEyE,EAAS,IAAKmC,GAChBA,EAAE,UAAY9G,GAA+B,IAAA8G,EAAG,QAAS,IAAUA,CAAA,KAMxD,eAAAH,GACdL,EACAS,EAAY,IAERhE,IACHiE,KAEGnE,IACHnM,EAAA,GAAAkN,GAAA,IAAgBA,GAAW,KAAK,MAAM,KAAK,UAAU0C,CAAO,KAGzD,IAAAE,GAEA,GADJzF,EAAI,oBAAoBuF,CAAO,EAC3BS,EACE,IAAArC,GAAW,IAAIe,CAAS,EAC5BxB,EAAI,OAAO,QAASzO,GAAOqM,GAAcrM,EAAI,SAAS,WAEtDkP,GAAW,IAAIe,CAAS,GACxBxB,EAAI,OAAO,KAAMzO,GAAOsM,GAAiBtM,CAAE,IAAM,SAAS,YAI1DkP,GAAW,IAAIe,CAAS,GACxBxB,EAAI,OAAO,KAAMzO,GAAOsM,GAAiBtM,CAAE,IAAM,MAAM,SAEjDuL,EAAI,mBAENA,EAAI,OAAO,KAAOA,EAAI,OAAO,UAAU,WAAW2D,GAAW,IAAIe,CAAS,EAAE,cAC1Ea,KAAS,aAAcvF,EAAI,2BAMlCyF,GAAazF,EAAI,OAChBuF,EAAQ,SACRA,EAAQ,KACRA,EAAQ,WACRA,EAAQ,WAED,OAAAW,EAAA,IAEJlG,EAAI,cACRrK,EAAA,GAAAiO,EAAA,CACCC,GAAY,QAAS,OAAOqC,CAAC,EAAG,EAAU,OAAO,EAC9C,GAAAtC,CAAA,GAEJ3C,GAAe,QACd,OAAQ,QACR,WACA,IAAK,EACL,MAAO,GACP,eAAgB,OAEjBkF,GAAWC,CAAe,SAI3BzC,GAAW,IAAIe,EAAWe,EAAU,kBAEnB3B,KAAW2B,GAAA,IACvBF,EAAQ,yBAGRzB,EAAQ,OAAS,OACpBuC,GAAYvC,CAAO,EACTA,EAAQ,OAAS,SAC3BwC,GAAcxC,CAAO,EACXA,EAAQ,OAAS,SAC3ByC,GAAqBzC,CAAO,EAClBA,EAAQ,OAAS,OAC3B0C,GAAW1C,CAAO,WAIXuC,GAAYvC,EAAA,CACZ,WAAAd,EAAM,SAAAC,CAAa,EAAAa,EACvBZ,EAAI,iBAAmBA,EAAI,cAC9BA,EAAI,gBAAkB,GACtB0C,GAAgB1C,EAAI,YAAaA,EAAI,YAAc,QAAQ,GAE5DA,EAAI,gBAAkB,GACtBH,GAAcC,EAAMC,CAAQ,EAC5BkD,GAAWC,CAAe,WAGlBE,GAAcxC,EAAA,OACd,KAAAd,CAAS,EAAAc,EACb,IAAA2C,EAA+BzD,EAAK,WACpC0D,EAA4B1D,EAAK,OACjC2D,GAA8B3D,EAAK,aACnC4D,GAAY5D,EAAK,UAEjB6D,GAAA,GACJnH,EAAa,SAASoH,GAASzQ,KAAA,CAC1ByQ,GAAQ,cAAgB5D,EAAI,WAC/B2D,GAAe,KAAKxQ,EAAC,IAGvBwQ,GAAe,UAAU,QAASxQ,IAAA,CACjCqJ,EAAa,OAAOrJ,GAAG,CAAC,IAEzBsQ,GAAc,QAASzD,KACtBxD,EAAa,KAAKwD,EAAG,IAGtB9B,GAAA,CACC,WAAYqF,EACZ,OAAQC,EACR,KAAM7S,EAAOyM,GACb,aAAAZ,EACA,UAAAkH,KAEDD,GAAc,QAASzD,KAClBA,GAAI,QAAQ,KAAMA,IAAQA,GAAI,CAAC,IAAM,MAAM,GAC9CuB,GAA2BvB,GAAI,EAAE,aAK3BsD,GAAWO,EAAA,CACX,YAAApH,EAAO,IAAAqH,EAAK,SAAA/D,EAAU,MAAAgE,GAAO,SAAAjN,GAAU,QAAAzF,EAAY,EAAAwS,EAC3DpR,EAAA,GAAAiO,EAAA,CACCC,GAAYlE,EAAOqH,EAAK/D,EAAUgE,GAAOjN,GAAUzF,EAAO,EACvD,GAAAqP,IAII,SAAAsD,GACRC,EACA1S,EACAyO,GAGCiE,EAAO,eAAiB,kBACxBjE,EAAI,aAAe,UAEnBpC,GAAcrM,EAAI,MAAM,WAKjB8R,GAAqBzC,EAAA,CACzBA,EAAQ,QAAW,CAAArD,IACtB9K,EAAA,GAAAiO,EAAA,CACCC,GACC,oBACA5E,GAAA,GAEA,QACA,KACA,IAEE,GAAA2E,IAGJnD,EAAoB,GACpB,WAAWoF,GAAW,GAAI,GAEvB/B,EAAQ,mBACXnO,EAAA,GAAAiO,EAAA,CACCC,GACC,oBACAzE,GAAA,GAEA,QACA,KACA,IAEE,GAAAwE,IAGG,eAAAX,EAAA,GAAakE,CAAW,EAAArD,KAC5BqD,EAAO,QAAU,aAAeA,EAAO,YAC1CjE,EAAI,OAAO,QAASzO,GAAA,CACnBuM,GAAevM,EAAI0S,EAAO,UAAU,IAGtCjE,EAAI,OAAO,QAASzO,GAAA,CACnByS,GAAmBpD,EAASrP,EAAIyO,CAAG,IAGpCjC,GAAe,QACX,GAAAkG,EACH,WAAYA,EAAO,WACnB,OAAQA,EAAO,MACf,SAAUA,EAAO,cACjB,SAAAlE,IAEDkD,GAAWC,CAAe,EAExB,CAAA9B,IACDrE,IAAa,MACbkH,EAAO,mBACPA,EAAO,UAAY,GACnBA,EAAO,cACPA,EAAO,IAAM9H,KAEbiF,GAA2B,GAC3B3O,EAAA,GAAAiO,EAAA,CACCC,GAAY,UAAWI,GAAmBhB,EAAU,SAAS,EAC1D,GAAAW,KAIH,CAAAW,IACDF,IACA8C,EAAO,MAAQ,QACfA,EAAO,IAAM7H,KAEbiF,GAAwB,GACxB5O,EAAA,GAAAiO,EAAA,CACCC,GAAY,UAAWM,GAAsBlB,EAAU,SAAS,EAC7D,GAAAW,KAIDuD,EAAO,QAAU,YAAcA,EAAO,QAAU,oBAC7CC,EAA+C,QACrDD,EAAO,mBAAmB,QAAS1S,IAAA,CAClCiL,EACE,OAAQwD,IAAQA,GAAI,QAAQ,OAAO5O,GAAK+S,EAAC,IAAM/S,KAAQG,EAAE,GACzD,QAASyO,KACTkE,EAAwB,IAAIlE,EAAG,MAGlCkE,EAAwB,QAASlE,KAChCuB,GAA2BvB,GAAI,GAAIqC,EAAQ,UAAU,OAGnD4B,EAAO,QAAU,aACpBzH,EAAa,QAAewD,UACvBA,EAAI,gBAAkBD,GACzBwB,GAA2BvB,EAAI,GAAIqC,EAAQ,UAAU,IAGvDrC,EAAI,OAAO,QAASzO,GAAA,CACnBqM,GAAcrM,EAAI,QAAQ,IAE3BkP,GAAW,OAAOe,CAAS,GAG3ByC,EAAO,QAAU,SAChB,CAAA1G,GAAA,CACAqD,EAAQ,sBAELqD,EAAO,SACJ,MAAAG,EAAWH,EAAO,QAAQ,QAC/BnI,GAAA,CACCqI,GAAGE,KAAMA,EAAA,EAELC,GAASL,EAAO,OAAS,QAC/BxR,EAAA,GAAAiO,EAAA,CACCC,GACC2D,GACAF,EACArE,EACA,QACAkE,EAAO,SACPA,EAAO,SAEL,GAAAvD,IAGLlE,EAAa,IAAWwD,UAEtBA,EAAI,gBAAkBD,IACrBC,EAAI,yBAELuB,GAA2BvB,EAAI,GAAIqC,EAAQ,UAAU,KAKrDvD,IACHyF,MAMM,SAAAC,GAAc/H,EAA2BgI,EAAA,IAC7C1H,IAAa,YAGX,MAAA2H,EAAA,IAAqB,qCACO3H,CAAQ,oBAEtCN,IAAU,QAAaA,EAAM,OAAS,GACzCiI,EAAe,aAAa,IAAI,QAASjI,CAAK,EAE/CiI,EAAe,aAAa,IAAI,cAAeD,CAAW,EAC1D,OAAO,KAAKC,EAAe,WAAY,QAAQ,WAGvCC,GAAmB3B,EAAA,CACrB,MAAA5R,EAAM4R,EAAE,OACdvQ,EAAA,GAAAiO,EAAWA,EAAS,OAAQmC,GAAMA,EAAE,KAAOzR,CAAG,SAGzCwT,GAAmBC,GAAA,GACrBA,OAAY,IAAIA,EAAM,SAAS,IAAI,EAAE,SAAW,SAAS,QAE9C,eAAAC,IAAA,CACV9H,IAKG,MAJF,IAAyBwF,0BACLxF,EAAE;AAAA,4DAGpB,EAGD,MAAAhE,GAAA,UAEFD,EAAIjI,EAAO,qBAAqB,GAAG,EAE9BqC,EAAI,EAAGA,EAAI4F,EAAE,OAAQ5F,IAAA,CACvB,MAAAxB,EAAUoH,EAAE5F,CAAC,EAAE,aAAa,QAAQ,EACpC4R,EAAQhM,EAAE5F,CAAC,EAAE,aAAa,MAAM,EAGlCyR,GAAgBG,CAAK,GAAKpT,IAAY,UACzCoH,EAAE5F,CAAC,EAAE,aAAa,SAAU,QAAQ,EAEtC6R,QAEKlU,GAAU8O,MAEf9O,EAAO,iBAAiB,cAAgBkS,GAAA,KAClC3G,GAAc2G,CAAC,EAAa,gBAAM,oBAAoB,QACnD,GAAAzR,EAAI,KAAA0T,EAAM,MAAAhU,GAAU+R,EAAE,OAC9BtF,GAAA,EAAgB,GAAAnM,EAAI,KAAA0T,EAAM,MAAAhU,CAAA,IACtBgU,IAAS,eAAiBhU,IAAU,IACvCqQ,GAAe,KAAK/P,CAAE,EAEnB0T,IAAS,eAAiBhU,IAAU,KACvCqQ,GAAiBA,GAAe,OAAQ4D,GAASA,IAAS3T,CAAE,KAG9DT,EAAO,iBAAiB,SAAWkS,GAAA,KAC7B3G,GAAc2G,CAAC,EAAa,gBAAM,oBAAoB,QAEnD,GAAAzR,EAAI,MAAA4G,EAAO,KAAA2H,GAASkD,EAAE,UAE1B7K,IAAU,SACL,YAAAsE,EAAO,YAAAgI,CAAgB,EAAA3E,EAC/B0E,GAAc/H,EAAOgI,CAAW,OACtBtM,IAAU,QACpB1F,EAAA,GAAAiO,EAAA,CAAYC,GAAY,QAASb,EAAA,GAAU3H,CAAK,KAAMuI,CAAQ,GACpDvI,IAAU,UACpB1F,EAAA,GAAAiO,EAAA,CAAYC,GAAY,UAAWb,EAAA,GAAU3H,CAAK,KAAMuI,CAAQ,GACtDvI,IAAU,OACpB1F,EAAA,GAAAiO,EAAA,CAAYC,GAAY,OAAQb,EAAA,GAAU3H,CAAK,KAAMuI,CAAQ,GACnDvI,GAAS,eACnBgN,GAAc5T,EAAI,WAAYuO,CAAI,EACxB3H,GAAS,eACNiN,EAAS7T,CAAE,IAAIuO,CAAI,GAC1B,QAASuF,GAAA,CACV,GAAA5E,GAAW,IAAI4E,CAAM,SAElBvM,GAAS,GAAAgE,EAAI,OAAO,KAAOA,EAAI,OAAO,UAAU,WAAW2D,GAAW,IAAI4E,CAAM,EAAE,aACxFvI,EAAI,aAAahE,EAAG,aACpBgE,EAAI,SAAShE,EAAG,KAILsM,EAAS7T,CAAE,IAAI4G,CAAK,GAE3B,QAASkN,GAAA,CACd,2BACC9D,GAA2B8D,EAAQ9T,EAAIuO,CAAI,aAM/CF,GAAkB,KAGnBzB,GAAA,CAAc5M,EAAIN,IAAA,CACJmU,EAAS7T,CAAE,GAAI,QAEtB,QAAS8T,GAAA,CACd,2BACC9D,GAA2B8D,EAAQ9T,EAAIN,CAAK,QAKzC,MAAA+T,GAAA,KACLxI,EAAa,QAASwD,GAAA,CACjBA,EAAI,QAAQ,KAAMA,GAAQA,EAAI,CAAC,IAAM,MAAM,GAC9CuB,GAA2BvB,EAAI,EAAE,KAO3B,SAAAmF,GACR5T,EACA0S,EACAnE,EAAA,CAEAA,EAAK,OAASmE,EACdvG,GAEE,KAAAnM,EACA,KAAM,iBACN,MAAOuO,CAAA,aAKDmD,GAAWqC,EAAA,CACf,IAAA/E,EAAA,GAKJ,OAAO,QAAQ+E,CAAQ,EAAE,UAAU/T,EAAIwM,CAAc,QAChDjB,EAAI,QAAUiB,EAAe,SAAW,eAIxC,IAAAwH,EAAa/I,EAAa,KAC5BwD,GAAQA,EAAI,IAAMjC,EAAe,UAE/BwH,IAAe,SAGnBxH,EAAe,iBAAmBwH,EAAW,iBAC7CxH,EAAe,cAAgBwH,EAAW,cAC1ChF,EAAQ,MACP,GAAI,SAAShP,CAAE,EACf,KAAM,iBACN,MAAOwM,OAIH,MAAAyH,EAAmBzH,GAAe,uBAClC0H,EAAqB,MAAM,KAAKD,CAAgB,EAAE,MACrDjU,EAAImU,CAAc,MAElB,GAAAnU,EACA,KAAM,UACN,MAAOmU,IAAmB,aAK7BhI,GAAA,IAAiB6C,KAAYkF,CAAkB,GAO5C,IAAAE,GAAsBC,GAAS,EAAK,wBAExClT,GAAA,KACCyO,GACC,iEAAiE,KAChE,UAAU,WAGZ0E,GACClV,EACC8L,GAAOmE,EAASC,IAAA,CAChBvL,GAAgBmH,EAAOmE,EAASC,CAAI,GAEpCjM,GAAA,OACAuL,EAAuBvL,EAAAuL,CAAA,IAKrBzB,IACHQ,KAEGN,IACHU,KAEGX,IACHY,OAIO,SAAAuG,IAAA,CACJ3F,EACH4F,KAEAC,mBAoCGvG,IAAsBf,EAAgB,WAGtCQ,KACAI,cA0BDwG,cAUApG,IAAsBf,EAAgB,WAGtCY,cAkBFE,GAAqB,EAAI,EACzBhN,EAAA,GAAAmM,GAAuB,EAAK,WAe3Ba,GAAqB,EAAK,MAOftH,GAAK,CACfsH,GAAqB,EAAK,OAC1BE,GAAS,IACTlN,EAAA,GAAAmM,QAAuBA,GACtBzG,EAAM,QAAQ,oBAAoB,YAqBpCuH,GAAqB,EAAK,2EASzBA,GAAqB,EAAK,WAG1BoG,m+BAnhCiExH,GAAA,4BAGpEpB,EAAU,EAAA+I,CAAA,yCAKVzJ,IAAiB6B,IACjBuB,IACC,CAAApB,KAGDwG,UACA3G,GAAmB7B,CAAA,0BAywBjByG,GAAWC,CAAe", "names": ["bind", "binding_callbacks", "ctx", "create_if_block", "root", "$$props", "component", "target", "theme_mode", "instance", "value", "elem_id", "elem_classes", "_id", "visible", "s", "id", "p", "v", "wrap", "_target", "args", "props", "report", "propargs", "ev", "_component", "supported_props", "translate_prop", "obj", "key", "translate_if_needed", "$$value", "$$restProps", "$$invalidate", "onMount", "createEventDispatcher", "rendercomponent_props", "dirty", "get_spread_object", "rendercomponent_changes", "each_value", "ensure_array_like", "get_key", "i", "render_changes", "if_block", "create_if_block_1", "node", "parent", "version", "autoscroll", "max_file_size", "client", "dispatch", "filtered_children", "child", "setContext", "$$self", "valid_node", "c", "Gradio", "$reactive_formatter", "load_component", "rootNode", "logo", "api_logo", "settings_logo", "record_stop", "isRecording", "mediaRecorder", "recordedChunks", "recordingStartTime", "removeSegment", "add_message_callback", "onRecordingStateChange", "zoomEffects", "initialize", "rootPath", "add_new_message", "recordingStateCallback", "startRecording", "originalTitle", "stream", "options", "handleDataAvailable", "handleStop", "error", "stopRecording", "markRemoveSegmentStart", "currentTime", "markRemoveSegmentEnd", "addZoomEffect", "is_input", "params", "FPS", "currentFrame", "newEffectDuration", "newEffectEndFrame", "existingEffect", "existingEffectEndFrame", "zoom", "elements", "duration", "minLeft", "minTop", "maxRight", "maxBottom", "foundElements", "elementId", "selector", "element", "rect", "viewportWidth", "viewportHeight", "boxWidth", "boxHeight", "widthPercentage", "heightPercentage", "<PERSON><PERSON><PERSON><PERSON>", "topLeft", "bottomRight", "width", "newCenter", "event", "blob", "handleRecordingComplete", "track", "recordedBlob", "formData", "response", "processedBlob", "defaultFilename", "saveWithDownloadAttribute", "<PERSON><PERSON><PERSON>", "url", "a", "tick", "prefix_css", "html_tag", "raw_value", "t1_value", "t6_value", "t11_value", "create_if_block_5", "attr", "img0", "img0_src_value", "img0_alt_value", "img1", "img1_src_value", "img1_alt_value", "img2", "img2_src_value", "img2_alt_value", "insert", "footer", "anchor", "append", "div0", "button0", "div1", "button1", "set_data", "t1", "t6", "t11", "create_if_block_6", "img", "img_src_value", "img_alt_value", "button", "div", "t_value", "div2", "create_if_block_9", "create_if_block_8", "create_if_block_7", "create_if_block_4", "if_block4", "create_if_block_3", "create_if_block_2", "if_block6", "MESSAGE_QUOTE_RE", "LOST_CONNECTION_MESSAGE", "CHANGED_CONNECTION_MESSAGE", "RECONNECTION_MESSAGE", "SESSION_NOT_FOUND_MESSAGE", "SHOW_DUPLICATE_MESSAGE_ON_ETA", "SHOW_MOBILE_QUEUE_WARNING_ON_ETA", "isCustomEvent", "components", "layout", "dependencies", "title", "show_api", "show_footer", "control_page_title", "app_mode", "app", "space_id", "js", "fill_height", "ready", "username", "api_prefix", "initial_layout", "css", "broken_connection", "_layout", "targets", "update_value", "get_data", "modify_stream", "get_stream_state", "set_time_limit", "loading_status", "scheduled_updates", "create_layout", "rerender_layout", "value_change", "create_components", "old_dependencies", "run", "setupi18n", "layout_creating", "search_params", "api_docs_visible", "settings_visible", "api_recorder_visible", "allow_zoom", "allow_video_trim", "ApiDocs", "ApiRecorder", "Settings", "loadApiDocs", "api_docs_module", "__vitePreload", "api_recorder_module", "loadApiRecorder", "loadSettings", "settings_module", "set_api_docs_visible", "set_settings_visible", "api_calls", "render_complete", "handle_update", "data", "fn_index", "dep", "input_type", "comp", "$is_screen_recording", "screen_recorder.zoom", "outputs", "meta_updates", "updates", "update_key", "submit_map", "messages", "new_message", "message", "type", "_error_id", "DUPLICATE_MESSAGE", "$_", "MOBILE_QUEUE_WARNING", "WAITING_FOR_INPUTS_MESSAGE", "is_mobile_device", "showed_duplicate_message", "showed_mobile_warning", "inputs_waiting", "wait_then_trigger_api_call", "dep_index", "trigger_id", "event_data", "_unsub", "unsub", "$scheduled_updates", "updating", "trigger_api_call", "get_component_value_or_event_data", "component_id", "_dep", "input", "current_status", "payload", "trigger_prediction", "submission", "AsyncFunction", "js_result", "make_prediction", "reconnect", "connection_status", "m", "streaming", "screen_recorder.markRemoveSegmentStart", "e", "set_status", "$loading_status", "handle_data", "handle_render", "handle_status_update", "handle_log", "_components", "render_layout", "_dependencies", "render_id", "deps_to_remove", "old_dep", "msg", "log", "level", "open_stream_events", "status", "deps_triggered_by_state", "_", "_message", "b", "_title", "screen_recorder.markRemoveSegmentEnd", "trigger_share", "description", "discussion_url", "handle_error_close", "is_external_url", "link", "handle_mount", "_link", "handle_load_triggers", "prop", "item", "update_status", "$targets", "dep_id", "statuses", "dependency", "inputs_to_update", "additional_updates", "pending_status", "is_screen_recording", "writable", "screen_recorder.initialize", "screen_recording", "screen_recorder.stopRecording", "screen_recorder.startRecording", "$_layout"], "ignoreList": [], "sources": ["../../../../js/core/src/RenderComponent.svelte", "../../../../js/core/src/Render.svelte", "../../../../js/core/src/MountComponents.svelte", "../../../../js/core/src/images/logo.svg", "../../../../js/core/src/api_docs/img/api-logo.svg", "../../../../js/core/src/api_docs/img/settings-logo.svg", "../../../../js/core/src/api_docs/img/record-stop.svg", "../../../../js/core/src/screen_recorder.ts", "../../../../js/core/src/Blocks.svelte"], "sourcesContent": ["<svelte:options immutable={true} />\n\n<script lang=\"ts\">\n\timport type { ComponentMeta, ThemeMode } from \"./types\";\n\timport type { SvelteComponent, ComponentType } from \"svelte\";\n\timport { translate_if_needed } from \"./i18n\";\n\t// @ts-ignore\n\timport { bind, binding_callbacks } from \"svelte/internal\";\n\n\texport let root: string;\n\texport let component: ComponentMeta[\"component\"];\n\texport let target: HTMLElement;\n\texport let theme_mode: ThemeMode;\n\texport let instance: ComponentMeta[\"instance\"];\n\texport let value: any;\n\t// export let gradio: Gradio;\n\texport let elem_id: string;\n\texport let elem_classes: string[];\n\texport let _id: number;\n\texport let visible: boolean;\n\n\tconst s = (id: number, p: string, v: any): CustomEvent =>\n\t\tnew CustomEvent(\"prop_change\", { detail: { id, prop: p, value: v } });\n\n\tfunction wrap(\n\t\tcomponent: ComponentType<SvelteComponent>\n\t): ComponentType<SvelteComponent> {\n\t\tconst ProxiedMyClass = new Proxy(component, {\n\t\t\tconstruct(_target, args: Record<string, any>[]) {\n\t\t\t\t//@ts-ignore\n\t\t\t\tconst instance = new _target(...args);\n\t\t\t\tconst props = Object.keys(instance.$$.props);\n\n\t\t\t\tfunction report(props: string) {\n\t\t\t\t\treturn function (propargs: any) {\n\t\t\t\t\t\tif (!target) return;\n\t\t\t\t\t\tconst ev = s(_id, props, propargs);\n\t\t\t\t\t\ttarget.dispatchEvent(ev);\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tprops.forEach((v) => {\n\t\t\t\t\tbinding_callbacks.push(() => bind(instance, v, report(v)));\n\t\t\t\t});\n\n\t\t\t\treturn instance;\n\t\t\t}\n\t\t});\n\n\t\treturn ProxiedMyClass;\n\t}\n\n\tlet _component = wrap(component);\n\n\tconst supported_props = [\n\t\t\"description\",\n\t\t\"info\",\n\t\t\"title\",\n\t\t\"placeholder\",\n\t\t\"value\",\n\t\t\"label\"\n\t];\n\n\tfunction translate_prop(obj: SvelteRestProps): void {\n\t\tfor (const key in obj) {\n\t\t\tif (supported_props.includes(key as string)) {\n\t\t\t\tobj[key] = translate_if_needed(obj[key]);\n\t\t\t}\n\t\t}\n\t}\n\n\t$: translate_prop($$restProps);\n\t$: value = translate_if_needed(value);\n</script>\n\n{#if visible}\n\t<svelte:component\n\t\tthis={_component}\n\t\tbind:this={instance}\n\t\tbind:value\n\t\ton:prop_change\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\t{target}\n\t\t{visible}\n\t\t{...$$restProps}\n\t\t{theme_mode}\n\t\t{root}\n\t>\n\t\t<slot />\n\t</svelte:component>\n{/if}\n", "<script lang=\"ts\">\n\timport { Gradio, reactive_formatter } from \"./gradio_helper\";\n\timport { onMount, createEventDispatcher, setContext } from \"svelte\";\n\timport type { ComponentMeta, ThemeMode } from \"./types\";\n\timport type { Client } from \"@gradio/client\";\n\timport RenderComponent from \"./RenderComponent.svelte\";\n\timport { load_component } from \"virtual:component-loader\";\n\n\texport let root: string;\n\n\texport let node: ComponentMeta;\n\texport let parent: string | null = null;\n\texport let target: HTMLElement;\n\texport let theme_mode: ThemeMode;\n\texport let version: string;\n\texport let autoscroll: boolean;\n\texport let max_file_size: number | null;\n\texport let client: Client;\n\n\tconst dispatch = createEventDispatcher<{ mount: number; destroy: number }>();\n\tlet filtered_children: ComponentMeta[] = [];\n\n\tonMount(() => {\n\t\tdispatch(\"mount\", node.id);\n\n\t\tfor (const child of filtered_children) {\n\t\t\tdispatch(\"mount\", child.id);\n\t\t}\n\n\t\treturn () => {\n\t\t\tdispatch(\"destroy\", node.id);\n\n\t\t\tfor (const child of filtered_children) {\n\t\t\t\tdispatch(\"mount\", child.id);\n\t\t\t}\n\t\t};\n\t});\n\n\t$: {\n\t\tif (node) {\n\t\t\tnode.children =\n\t\t\t\tnode.children &&\n\t\t\t\tnode.children.filter((v) => {\n\t\t\t\t\tconst valid_node = node.type !== \"statustracker\";\n\t\t\t\t\tif (!valid_node) {\n\t\t\t\t\t\tfiltered_children.push(v);\n\t\t\t\t\t}\n\t\t\t\t\treturn valid_node;\n\t\t\t\t});\n\t\t}\n\t}\n\n\tsetContext(\"BLOCK_KEY\", parent);\n\n\t$: {\n\t\tif (node && node.type === \"form\") {\n\t\t\tif (\n\t\t\t\tnode.children?.every(\n\t\t\t\t\t(c) => typeof c.props.visible === \"boolean\" && !c.props.visible\n\t\t\t\t)\n\t\t\t) {\n\t\t\t\tnode.props.visible = false;\n\t\t\t} else {\n\t\t\t\tnode.props.visible = true;\n\t\t\t}\n\t\t}\n\t}\n\n\t$: node.props.gradio = new Gradio<Record<string, any>>(\n\t\tnode.id,\n\t\ttarget,\n\t\ttheme_mode,\n\t\tversion,\n\t\troot,\n\t\tautoscroll,\n\t\tmax_file_size,\n\t\t$reactive_formatter,\n\t\tclient,\n\t\tload_component\n\t);\n</script>\n\n{#if node.component}\n\t<RenderComponent\n\t\t_id={node?.id}\n\t\tcomponent={node.component}\n\t\tbind:instance={node.instance}\n\t\tbind:value={node.props.value}\n\t\telem_id={(\"elem_id\" in node.props && node.props.elem_id) ||\n\t\t\t`component-${node.id}`}\n\t\telem_classes={(\"elem_classes\" in node.props && node.props.elem_classes) ||\n\t\t\t[]}\n\t\t{target}\n\t\t{...node.props}\n\t\t{theme_mode}\n\t\t{root}\n\t\tvisible={typeof node.props.visible === \"boolean\"\n\t\t\t? node.props.visible\n\t\t\t: true}\n\t>\n\t\t{#if node.children && node.children.length}\n\t\t\t{#each node.children as _node (_node.id)}\n\t\t\t\t<svelte:self\n\t\t\t\t\tnode={_node}\n\t\t\t\t\tcomponent={_node.component}\n\t\t\t\t\t{target}\n\t\t\t\t\tid={_node.id}\n\t\t\t\t\t{root}\n\t\t\t\t\t{theme_mode}\n\t\t\t\t\ton:destroy\n\t\t\t\t\ton:mount\n\t\t\t\t\t{max_file_size}\n\t\t\t\t\t{client}\n\t\t\t\t/>\n\t\t\t{/each}\n\t\t{/if}\n\t</RenderComponent>\n{/if}\n", "<script lang=\"ts\">\n\timport { onMount, createEventDispatcher } from \"svelte\";\n\timport type { Client } from \"@gradio/client\";\n\timport Render from \"./Render.svelte\";\n\n\texport let rootNode: any;\n\texport let root: any;\n\texport let target: any;\n\texport let theme_mode: any;\n\texport let version: any;\n\texport let autoscroll: boolean;\n\texport let max_file_size: number | null = null;\n\texport let client: Client;\n\n\tconst dispatch = createEventDispatcher<{ mount?: never }>();\n\tonMount(() => {\n\t\tdispatch(\"mount\");\n\t});\n</script>\n\n{#if rootNode}\n\t<Render\n\t\tnode={rootNode}\n\t\t{root}\n\t\t{target}\n\t\t{theme_mode}\n\t\t{version}\n\t\t{autoscroll}\n\t\t{max_file_size}\n\t\t{client}\n\t/>\n{/if}\n", "export default \"data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='576'%20height='576'%20viewBox='0%200%20576%20576'%20fill='none'%3e%3cpath%20d='M287.5%20229L86%20344.5L287.5%20460L489%20344.5L287.5%20229Z'%20stroke='url(%23paint0_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='round'/%3e%3cpath%20d='M287.5%20116L86%20231.5L287.5%20347L489%20231.5L287.5%20116Z'%20stroke='url(%23paint1_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='round'/%3e%3cpath%20d='M86%20344L288%20229'%20stroke='url(%23paint2_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='bevel'/%3e%3cdefs%3e%3clinearGradient%20id='paint0_linear_102_7'%20x1='60'%20y1='341'%20x2='429.5'%20y2='344'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint1_linear_102_7'%20x1='513.5'%20y1='231'%20x2='143.5'%20y2='231'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint2_linear_102_7'%20x1='60'%20y1='344'%20x2='428.987'%20y2='341.811'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e\"", "export default \"data:image/svg+xml,%3csvg%20width='28'%20height='28'%20viewBox='0%200%2028%2028'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M26.9425%202.94265C27.4632%202.42195%2027.4632%201.57773%2026.9425%201.05703C26.4218%200.536329%2025.5776%200.536329%2025.0569%201.05703L22.5713%203.54256C21.1213%202.59333%2019.5367%202.43378%2018.1753%202.64006C16.5495%202.88638%2015.1127%203.66838%2014.3905%204.39053L12.3905%206.39053C12.1405%206.64058%2012%206.97972%2012%207.33334C12%207.68697%2012.1405%208.0261%2012.3905%208.27615L19.7239%2015.6095C20.2446%2016.1302%2021.0888%2016.1302%2021.6095%2015.6095L23.6095%2013.6095C24.3316%2012.8873%2025.1136%2011.4505%2025.36%209.82475C25.5663%208.46312%2025.4066%206.87827%2024.4571%205.42807L26.9425%202.94265Z'%20fill='%233c4555'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M12.276%2012.9426C12.7967%2012.4219%2012.7967%2011.5777%2012.276%2011.057C11.7553%2010.5363%2010.9111%2010.5363%2010.3904%2011.057L8.66651%2012.7809L8.27615%2012.3905C8.0261%2012.1405%207.68697%2012%207.33334%2012C6.97972%2012%206.64058%2012.1405%206.39053%2012.3905L4.39053%2014.3905C3.66838%2015.1127%202.88638%2016.5495%202.64006%2018.1753C2.43377%2019.5367%202.59333%2021.1214%203.54262%2022.5714L1.05703%2025.057C0.536329%2025.5777%200.536329%2026.4219%201.05703%2026.9426C1.57773%2027.4633%202.42195%2027.4633%202.94265%2026.9426L5.42817%2024.4571C6.87835%2025.4066%208.46315%2025.5663%209.82475%2025.36C11.4505%2025.1136%2012.8873%2024.3316%2013.6095%2023.6095L15.6095%2021.6095C16.1302%2021.0888%2016.1302%2020.2446%2015.6095%2019.7239L15.2188%2019.3332L16.9426%2017.6093C17.4633%2017.0886%2017.4633%2016.2444%2016.9426%2015.7237C16.4219%2015.203%2015.5777%2015.203%2015.057%2015.7237L13.3332%2017.4475L10.5521%2014.6665L12.276%2012.9426Z'%20fill='%23FF7C00'/%3e%3c/svg%3e\"", "export default \"data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20xmlns='http://www.w3.org/2000/svg'%3e%3c!--%20Outer%20gear%20teeth%20(gray)%20--%3e%3cpath%20d='M19.14%2012.94c.04-.3.06-.61.06-.94%200-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24%200-.43.17-.47.41l-.36%202.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47%200-.59.22L2.74%208.87c-.12.21-.08.47.12.61l2.03%201.58c-.05.3-.07.62-.07.94s.02.64.07.94l-2.03%201.58c-.18.14-.23.41-.12.61l1.92%203.32c.12.22.37.29.59.22l2.39-.96c.5.38%201.03.7%201.62.94l.36%202.54c.05.24.24.41.48.41h3.84c.24%200%20.44-.17.47-.41l.36-2.54c.59-.24%201.13-.56%201.62-.94l2.39.96c.22.08.47%200%20.59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12%2015.6c-1.98%200-3.6-1.62-3.6-3.6s1.62-3.6%203.6-3.6%203.6%201.62%203.6%203.6-1.62%203.6-3.6%203.6z'%20fill='%23808080'/%3e%3c!--%20Inner%20circle%20(now%20gray)%20--%3e%3ccircle%20cx='12'%20cy='12'%20r='2.5'%20fill='%23808080'/%3e%3c/svg%3e\"", "export default \"data:image/svg+xml,%3csvg%20viewBox='0%200%2020%2020'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='%23000000'%3e%3cg%20id='SVGRepo_bgCarrier'%20stroke-width='0'%3e%3c/g%3e%3cg%20id='SVGRepo_tracerCarrier'%20stroke-linecap='round'%20stroke-linejoin='round'%3e%3c/g%3e%3cg%20id='SVGRepo_iconCarrier'%3e%3ctitle%3erecord%20[%23982]%3c/title%3e%3cdesc%3eCreated%20with%20Sketch.%3c/desc%3e%3cdefs%3e%3c/defs%3e%3cg%20id='Page-1'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='Dribbble-Light-Preview'%20transform='translate(-380.000000,%20-3839.000000)'%20fill='%23FF0000'%3e%3cg%20id='icons'%20transform='translate(56.000000,%20160.000000)'%3e%3cpath%20d='M338,3689%20C338,3691.209%20336.209,3693%20334,3693%20C331.791,3693%20330,3691.209%20330,3689%20C330,3686.791%20331.791,3685%20334,3685%20C336.209,3685%20338,3686.791%20338,3689%20M334,3697%20C329.589,3697%20326,3693.411%20326,3689%20C326,3684.589%20329.589,3681%20334,3681%20C338.411,3681%20342,3684.589%20342,3689%20C342,3693.411%20338.411,3697%20334,3697%20M334,3679%20C328.477,3679%20324,3683.477%20324,3689%20C324,3694.523%20328.477,3699%20334,3699%20C339.523,3699%20344,3694.523%20344,3689%20C344,3683.477%20339.523,3679%20334,3679'%20id='record-[%23982]'%3e%3c/path%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e\"", "import type { ToastMessage } from \"@gradio/statustracker\";\n\nlet isRecording = false;\nlet mediaRecorder: MediaRecorder | null = null;\nlet recordedChunks: Blob[] = [];\nlet recordingStartTime = 0;\nlet animationFrameId: number | null = null;\nlet removeSegment: { start?: number; end?: number } = {};\nlet root: string;\n\nlet add_message_callback: (\n\ttitle: string,\n\tmessage: string,\n\ttype: ToastMessage[\"type\"]\n) => void;\nlet onRecordingStateChange: ((isRecording: boolean) => void) | null = null;\nlet zoomEffects: {\n\tboundingBox: { topLeft: [number, number]; bottomRight: [number, number] };\n\tstart_frame: number;\n\tduration?: number;\n}[] = [];\n\nexport function initialize(\n\trootPath: string,\n\tadd_new_message: (\n\t\ttitle: string,\n\t\tmessage: string,\n\t\ttype: ToastMessage[\"type\"]\n\t) => void,\n\trecordingStateCallback?: (isRecording: boolean) => void\n): void {\n\troot = rootPath;\n\tadd_message_callback = add_new_message;\n\tif (recordingStateCallback) {\n\t\tonRecordingStateChange = recordingStateCallback;\n\t}\n}\n\nexport async function startRecording(): Promise<void> {\n\tif (isRecording) {\n\t\treturn;\n\t}\n\n\ttry {\n\t\tconst originalTitle = document.title;\n\t\tdocument.title = \"[Sharing] Gradio Tab\";\n\t\tconst stream = await navigator.mediaDevices.getDisplayMedia({\n\t\t\tvideo: {\n\t\t\t\twidth: { ideal: 1920 },\n\t\t\t\theight: { ideal: 1080 },\n\t\t\t\tframeRate: { ideal: 30 }\n\t\t\t},\n\t\t\taudio: true,\n\t\t\tselfBrowserSurface: \"include\"\n\t\t} as MediaStreamConstraints);\n\t\tdocument.title = originalTitle;\n\n\t\tconst options = {\n\t\t\tvideoBitsPerSecond: 5000000\n\t\t};\n\n\t\tmediaRecorder = new MediaRecorder(stream, options);\n\n\t\trecordedChunks = [];\n\t\tremoveSegment = {};\n\n\t\tmediaRecorder.ondataavailable = handleDataAvailable;\n\t\tmediaRecorder.onstop = handleStop;\n\n\t\tmediaRecorder.start(1000);\n\t\tisRecording = true;\n\t\tif (onRecordingStateChange) {\n\t\t\tonRecordingStateChange(true);\n\t\t}\n\t\trecordingStartTime = Date.now();\n\t} catch (error: any) {\n\t\tadd_message_callback(\n\t\t\t\"Recording Error\",\n\t\t\t\"Failed to start recording: \" + error.message,\n\t\t\t\"error\"\n\t\t);\n\t}\n}\n\nexport function stopRecording(): void {\n\tif (!isRecording || !mediaRecorder) {\n\t\treturn;\n\t}\n\n\tmediaRecorder.stop();\n\tisRecording = false;\n\tif (onRecordingStateChange) {\n\t\tonRecordingStateChange(false);\n\t}\n}\n\nexport function isCurrentlyRecording(): boolean {\n\treturn isRecording;\n}\n\nexport function markRemoveSegmentStart(): void {\n\tif (!isRecording) {\n\t\treturn;\n\t}\n\n\tconst currentTime = (Date.now() - recordingStartTime) / 1000;\n\tremoveSegment.start = currentTime;\n}\n\nexport function markRemoveSegmentEnd(): void {\n\tif (!isRecording || removeSegment.start === undefined) {\n\t\treturn;\n\t}\n\n\tconst currentTime = (Date.now() - recordingStartTime) / 1000;\n\tremoveSegment.end = currentTime;\n}\n\nexport function clearRemoveSegment(): void {\n\tremoveSegment = {};\n}\n\nexport function addZoomEffect(\n\tis_input: boolean,\n\tparams: {\n\t\tboundingBox: {\n\t\t\ttopLeft: [number, number];\n\t\t\tbottomRight: [number, number];\n\t\t};\n\t\tduration?: number;\n\t}\n): void {\n\tif (!isRecording) {\n\t\treturn;\n\t}\n\n\tconst FPS = 30;\n\tconst currentTime = (Date.now() - recordingStartTime) / 1000;\n\tconst currentFrame = is_input\n\t\t? Math.floor((currentTime - 2) * FPS)\n\t\t: Math.floor(currentTime * FPS);\n\n\tif (\n\t\tparams.boundingBox &&\n\t\tparams.boundingBox.topLeft &&\n\t\tparams.boundingBox.bottomRight &&\n\t\tparams.boundingBox.topLeft.length === 2 &&\n\t\tparams.boundingBox.bottomRight.length === 2\n\t) {\n\t\tconst newEffectDuration = params.duration || 2.0;\n\t\tconst newEffectEndFrame =\n\t\t\tcurrentFrame + Math.floor(newEffectDuration * FPS);\n\n\t\tconst hasOverlap = zoomEffects.some((existingEffect) => {\n\t\t\tconst existingEffectEndFrame =\n\t\t\t\texistingEffect.start_frame +\n\t\t\t\tMath.floor((existingEffect.duration || 2.0) * FPS);\n\t\t\treturn (\n\t\t\t\t(currentFrame >= existingEffect.start_frame &&\n\t\t\t\t\tcurrentFrame <= existingEffectEndFrame) ||\n\t\t\t\t(newEffectEndFrame >= existingEffect.start_frame &&\n\t\t\t\t\tnewEffectEndFrame <= existingEffectEndFrame) ||\n\t\t\t\t(currentFrame <= existingEffect.start_frame &&\n\t\t\t\t\tnewEffectEndFrame >= existingEffectEndFrame)\n\t\t\t);\n\t\t});\n\n\t\tif (!hasOverlap) {\n\t\t\tzoomEffects.push({\n\t\t\t\tboundingBox: params.boundingBox,\n\t\t\t\tstart_frame: currentFrame,\n\t\t\t\tduration: newEffectDuration\n\t\t\t});\n\t\t}\n\t}\n}\n\nexport function zoom(\n\tis_input: boolean,\n\telements: number[],\n\tduration = 2.0\n): void {\n\tif (!isRecording) {\n\t\treturn;\n\t}\n\n\ttry {\n\t\tsetTimeout(() => {\n\t\t\tif (!elements || elements.length === 0) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tlet minLeft = Infinity;\n\t\t\tlet minTop = Infinity;\n\t\t\tlet maxRight = 0;\n\t\t\tlet maxBottom = 0;\n\t\t\tlet foundElements = false;\n\n\t\t\tfor (const elementId of elements) {\n\t\t\t\tconst selector = `#component-${elementId}`;\n\t\t\t\tconst element = document.querySelector(selector);\n\n\t\t\t\tif (element) {\n\t\t\t\t\tfoundElements = true;\n\t\t\t\t\tconst rect = element.getBoundingClientRect();\n\n\t\t\t\t\tminLeft = Math.min(minLeft, rect.left);\n\t\t\t\t\tminTop = Math.min(minTop, rect.top);\n\t\t\t\t\tmaxRight = Math.max(maxRight, rect.right);\n\t\t\t\t\tmaxBottom = Math.max(maxBottom, rect.bottom);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (!foundElements) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst viewportWidth = window.innerWidth;\n\t\t\tconst viewportHeight = window.innerHeight;\n\n\t\t\tconst boxWidth = Math.min(maxRight, viewportWidth) - Math.max(0, minLeft);\n\t\t\tconst boxHeight =\n\t\t\t\tMath.min(maxBottom, viewportHeight) - Math.max(0, minTop);\n\n\t\t\tconst widthPercentage = boxWidth / viewportWidth;\n\t\t\tconst heightPercentage = boxHeight / viewportHeight;\n\n\t\t\tif (widthPercentage >= 0.8 || heightPercentage >= 0.8) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst isSafari = /^((?!chrome|android).)*safari/i.test(\n\t\t\t\tnavigator.userAgent\n\t\t\t);\n\n\t\t\tlet topLeft: [number, number] = [\n\t\t\t\tMath.max(0, minLeft) / viewportWidth,\n\t\t\t\tMath.max(0, minTop) / viewportHeight\n\t\t\t];\n\n\t\t\tlet bottomRight: [number, number] = [\n\t\t\t\tMath.min(maxRight, viewportWidth) / viewportWidth,\n\t\t\t\tMath.min(maxBottom, viewportHeight) / viewportHeight\n\t\t\t];\n\n\t\t\tif (isSafari) {\n\t\t\t\ttopLeft[0] = Math.max(0, topLeft[0] * 0.9);\n\t\t\t\tbottomRight[0] = Math.min(1, bottomRight[0] * 0.9);\n\t\t\t\tconst width = bottomRight[0] - topLeft[0];\n\t\t\t\tconst center = (topLeft[0] + bottomRight[0]) / 2;\n\t\t\t\tconst newCenter = center * 0.9;\n\t\t\t\ttopLeft[0] = Math.max(0, newCenter - width / 2);\n\t\t\t\tbottomRight[0] = Math.min(1, newCenter + width / 2);\n\t\t\t}\n\n\t\t\ttopLeft[0] = Math.max(0, topLeft[0]);\n\t\t\ttopLeft[1] = Math.max(0, topLeft[1]);\n\t\t\tbottomRight[0] = Math.min(1, bottomRight[0]);\n\t\t\tbottomRight[1] = Math.min(1, bottomRight[1]);\n\n\t\t\taddZoomEffect(is_input, {\n\t\t\t\tboundingBox: {\n\t\t\t\t\ttopLeft,\n\t\t\t\t\tbottomRight\n\t\t\t\t},\n\t\t\t\tduration: duration\n\t\t\t});\n\t\t}, 300);\n\t} catch (error) {\n\t\t// pass\n\t}\n}\n\nfunction handleDataAvailable(event: BlobEvent): void {\n\tif (event.data.size > 0) {\n\t\trecordedChunks.push(event.data);\n\t}\n}\n\nfunction handleStop(): void {\n\tisRecording = false;\n\tif (onRecordingStateChange) {\n\t\tonRecordingStateChange(false);\n\t}\n\n\tconst blob = new Blob(recordedChunks, {\n\t\ttype: \"video/mp4\"\n\t});\n\n\thandleRecordingComplete(blob);\n\n\tconst screenStream = mediaRecorder?.stream?.getTracks() || [];\n\tscreenStream.forEach((track) => track.stop());\n\n\tif (animationFrameId !== null) {\n\t\tcancelAnimationFrame(animationFrameId);\n\t\tanimationFrameId = null;\n\t}\n}\n\nasync function handleRecordingComplete(recordedBlob: Blob): Promise<void> {\n\ttry {\n\t\tadd_message_callback(\n\t\t\t\"Processing video\",\n\t\t\t\"This may take a few seconds...\",\n\t\t\t\"info\"\n\t\t);\n\n\t\tconst formData = new FormData();\n\t\tformData.append(\"video\", recordedBlob, \"recording.mp4\");\n\n\t\tif (removeSegment.start !== undefined && removeSegment.end !== undefined) {\n\t\t\tformData.append(\"remove_segment_start\", removeSegment.start.toString());\n\t\t\tformData.append(\"remove_segment_end\", removeSegment.end.toString());\n\t\t}\n\n\t\tif (zoomEffects.length > 0) {\n\t\t\tformData.append(\"zoom_effects\", JSON.stringify(zoomEffects));\n\t\t}\n\n\t\tconst response = await fetch(root + \"/gradio_api/process_recording\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: formData\n\t\t});\n\n\t\tif (!response.ok) {\n\t\t\tthrow new Error(\n\t\t\t\t`Server returned ${response.status}: ${response.statusText}`\n\t\t\t);\n\t\t}\n\n\t\tconst processedBlob = await response.blob();\n\t\tconst defaultFilename = `gradio-screen-recording-${new Date().toISOString().replace(/:/g, \"-\").replace(/\\..+/, \"\")}.mp4`;\n\t\tsaveWithDownloadAttribute(processedBlob, defaultFilename);\n\t\tzoomEffects = [];\n\t} catch (error) {\n\t\tadd_message_callback(\n\t\t\t\"Processing Error\",\n\t\t\t\"Failed to process recording. Saving original version.\",\n\t\t\t\"warning\"\n\t\t);\n\n\t\tconst defaultFilename = `gradio-screen-recording-${new Date().toISOString().replace(/:/g, \"-\").replace(/\\..+/, \"\")}.mp4`;\n\t\tsaveWithDownloadAttribute(recordedBlob, defaultFilename);\n\t}\n}\n\nfunction saveWithDownloadAttribute(blob: Blob, suggestedName: string): void {\n\tconst url = URL.createObjectURL(blob);\n\tconst a = document.createElement(\"a\");\n\ta.style.display = \"none\";\n\ta.href = url;\n\ta.download = suggestedName;\n\n\tdocument.body.appendChild(a);\n\ta.click();\n\tsetTimeout(() => {\n\t\tdocument.body.removeChild(a);\n\t\tURL.revokeObjectURL(url);\n\t}, 100);\n}\n", "<script lang=\"ts\">\n\timport { tick, onMount } from \"svelte\";\n\timport { _ } from \"svelte-i18n\";\n\timport { Client } from \"@gradio/client\";\n\timport { writable } from \"svelte/store\";\n\n\timport type { LoadingStatus, LoadingStatusCollection } from \"./stores\";\n\n\timport type { ComponentMeta, Dependency, LayoutNode } from \"./types\";\n\timport type { UpdateTransaction } from \"./init\";\n\timport { setupi18n } from \"./i18n\";\n\timport type { ThemeMode, Payload } from \"./types\";\n\timport { Toast } from \"@gradio/statustracker\";\n\timport type { ToastMessage } from \"@gradio/statustracker\";\n\timport type { ShareData, ValueData } from \"@gradio/utils\";\n\timport MountComponents from \"./MountComponents.svelte\";\n\timport { prefix_css } from \"./css\";\n\n\timport type ApiDocs from \"./api_docs/ApiDocs.svelte\";\n\timport type ApiRecorder from \"./api_docs/ApiRecorder.svelte\";\n\timport type Settings from \"./api_docs/Settings.svelte\";\n\timport type { ComponentType } from \"svelte\";\n\n\timport logo from \"./images/logo.svg\";\n\timport api_logo from \"./api_docs/img/api-logo.svg\";\n\timport settings_logo from \"./api_docs/img/settings-logo.svg\";\n\timport record_stop from \"./api_docs/img/record-stop.svg\";\n\timport { create_components, AsyncFunction } from \"./init\";\n\timport type {\n\t\tLogMessage,\n\t\tRenderMessage,\n\t\tStatusMessage\n\t} from \"@gradio/client\";\n\timport * as screen_recorder from \"./screen_recorder\";\n\n\texport let root: string;\n\texport let components: ComponentMeta[];\n\texport let layout: LayoutNode;\n\texport let dependencies: Dependency[];\n\texport let title = \"Gradio\";\n\texport let target: HTMLElement;\n\texport let autoscroll: boolean;\n\texport let show_api = true;\n\texport let show_footer = true;\n\texport let control_page_title = false;\n\texport let app_mode: boolean;\n\texport let theme_mode: ThemeMode;\n\texport let app: Awaited<ReturnType<typeof Client.connect>>;\n\texport let space_id: string | null;\n\texport let version: string;\n\texport let js: string | null;\n\texport let fill_height = false;\n\texport let ready: boolean;\n\texport let username: string | null;\n\texport let api_prefix = \"\";\n\texport let max_file_size: number | undefined = undefined;\n\texport let initial_layout: ComponentMeta | undefined = undefined;\n\texport let css: string | null | undefined = null;\n\tlet broken_connection = false;\n\n\tlet {\n\t\tlayout: _layout,\n\t\ttargets,\n\t\tupdate_value,\n\t\tget_data,\n\t\tmodify_stream,\n\t\tget_stream_state,\n\t\tset_time_limit,\n\t\tloading_status,\n\t\tscheduled_updates,\n\t\tcreate_layout,\n\t\trerender_layout,\n\t\tvalue_change\n\t} = create_components({\n\t\tinitial_layout\n\t});\n\n\t$: components, layout, dependencies, root, app, fill_height, target, run();\n\n\t$: {\n\t\tready = !!$_layout;\n\t}\n\n\tlet old_dependencies = dependencies;\n\t$: if (\n\t\tdependencies !== old_dependencies &&\n\t\trender_complete &&\n\t\t!layout_creating\n\t) {\n\t\t// re-run load triggers in SSR mode when page changes\n\t\thandle_load_triggers();\n\t\told_dependencies = dependencies;\n\t}\n\n\tasync function run(): Promise<void> {\n\t\tawait setupi18n(app.config?.i18n_translations || undefined);\n\n\t\tlayout_creating = true;\n\t\tawait create_layout({\n\t\t\tcomponents,\n\t\t\tlayout,\n\t\t\tdependencies,\n\t\t\troot: root + api_prefix,\n\t\t\tapp,\n\t\t\toptions: {\n\t\t\t\tfill_height\n\t\t\t}\n\t\t});\n\t\tlayout_creating = false;\n\t}\n\n\texport let search_params: URLSearchParams;\n\tlet api_docs_visible = search_params.get(\"view\") === \"api\" && show_api;\n\tlet settings_visible = search_params.get(\"view\") === \"settings\";\n\tlet api_recorder_visible =\n\t\tsearch_params.get(\"view\") === \"api-recorder\" && show_api;\n\tlet allow_zoom = true;\n\tlet allow_video_trim = true;\n\n\t// Lazy component loading state\n\tlet ApiDocs: ComponentType<ApiDocs> | null = null;\n\tlet ApiRecorder: ComponentType<ApiRecorder> | null = null;\n\tlet Settings: ComponentType<Settings> | null = null;\n\n\tasync function loadApiDocs(): Promise<void> {\n\t\tif (!ApiDocs || !ApiRecorder) {\n\t\t\tconst api_docs_module = await import(\"./api_docs/ApiDocs.svelte\");\n\t\t\tconst api_recorder_module = await import(\"./api_docs/ApiRecorder.svelte\");\n\t\t\tif (!ApiDocs) ApiDocs = api_docs_module.default;\n\t\t\tif (!ApiRecorder) ApiRecorder = api_recorder_module.default;\n\t\t}\n\t}\n\n\tasync function loadApiRecorder(): Promise<void> {\n\t\tif (!ApiRecorder) {\n\t\t\tconst api_recorder_module = await import(\"./api_docs/ApiRecorder.svelte\");\n\t\t\tApiRecorder = api_recorder_module.default;\n\t\t}\n\t}\n\n\tasync function loadSettings(): Promise<void> {\n\t\tif (!Settings) {\n\t\t\tconst settings_module = await import(\"./api_docs/Settings.svelte\");\n\t\t\tSettings = settings_module.default;\n\t\t}\n\t}\n\n\tasync function set_api_docs_visible(visible: boolean): Promise<void> {\n\t\tapi_recorder_visible = false;\n\t\tif (visible) {\n\t\t\tawait loadApiDocs();\n\t\t}\n\t\tapi_docs_visible = visible;\n\t\tlet params = new URLSearchParams(window.location.search);\n\t\tif (visible) {\n\t\t\tparams.set(\"view\", \"api\");\n\t\t} else {\n\t\t\tparams.delete(\"view\");\n\t\t}\n\t\thistory.replaceState(null, \"\", \"?\" + params.toString());\n\t}\n\n\tasync function set_settings_visible(visible: boolean): Promise<void> {\n\t\tif (visible) {\n\t\t\tawait loadSettings();\n\t\t}\n\t\tlet params = new URLSearchParams(window.location.search);\n\t\tif (visible) {\n\t\t\tparams.set(\"view\", \"settings\");\n\t\t} else {\n\t\t\tparams.delete(\"view\");\n\t\t}\n\t\thistory.replaceState(null, \"\", \"?\" + params.toString());\n\t\tsettings_visible = !settings_visible;\n\t}\n\n\tlet api_calls: Payload[] = [];\n\n\tlet layout_creating = false;\n\texport let render_complete = false;\n\n\tasync function handle_update(data: any, fn_index: number): Promise<void> {\n\t\tconst dep = dependencies.find((dep) => dep.id === fn_index);\n\t\tconst input_type = components.find(\n\t\t\t(comp) => comp.id === dep?.inputs[0]\n\t\t)?.type;\n\t\tif (allow_zoom && dep && input_type !== \"dataset\") {\n\t\t\tif (dep && dep.inputs && dep.inputs.length > 0 && $is_screen_recording) {\n\t\t\t\tscreen_recorder.zoom(true, dep.inputs, 1.0);\n\t\t\t}\n\n\t\t\tif (\n\t\t\t\tdep &&\n\t\t\t\tdep.outputs &&\n\t\t\t\tdep.outputs.length > 0 &&\n\t\t\t\t$is_screen_recording\n\t\t\t) {\n\t\t\t\tscreen_recorder.zoom(false, dep.outputs, 2.0);\n\t\t\t}\n\t\t}\n\n\t\tif (!dep) {\n\t\t\treturn;\n\t\t}\n\t\tconst outputs = dep.outputs;\n\t\tconst meta_updates = data?.map((value: any, i: number) => {\n\t\t\treturn {\n\t\t\t\tid: outputs[i],\n\t\t\t\tprop: \"value_is_output\",\n\t\t\t\tvalue: true\n\t\t\t};\n\t\t});\n\n\t\tupdate_value(meta_updates);\n\n\t\tawait tick();\n\n\t\tconst updates: UpdateTransaction[] = [];\n\n\t\tdata?.forEach((value: any, i: number) => {\n\t\t\tif (\n\t\t\t\ttypeof value === \"object\" &&\n\t\t\t\tvalue !== null &&\n\t\t\t\tvalue.__type__ === \"update\"\n\t\t\t) {\n\t\t\t\tfor (const [update_key, update_value] of Object.entries(value)) {\n\t\t\t\t\tif (update_key === \"__type__\") {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tupdates.push({\n\t\t\t\t\t\t\tid: outputs[i],\n\t\t\t\t\t\t\tprop: update_key,\n\t\t\t\t\t\t\tvalue: update_value\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tupdates.push({\n\t\t\t\t\tid: outputs[i],\n\t\t\t\t\tprop: \"value\",\n\t\t\t\t\tvalue\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t\tupdate_value(updates);\n\n\t\tawait tick();\n\t}\n\n\tlet submit_map: Map<number, ReturnType<typeof app.submit>> = new Map();\n\n\tlet messages: (ToastMessage & { fn_index: number })[] = [];\n\tfunction new_message(\n\t\ttitle: string,\n\t\tmessage: string,\n\t\tfn_index: number,\n\t\ttype: ToastMessage[\"type\"],\n\t\tduration: number | null = 10,\n\t\tvisible = true\n\t): ToastMessage & { fn_index: number } {\n\t\treturn {\n\t\t\ttitle,\n\t\t\tmessage,\n\t\t\tfn_index,\n\t\t\ttype,\n\t\t\tid: ++_error_id,\n\t\t\tduration,\n\t\t\tvisible\n\t\t};\n\t}\n\n\texport function add_new_message(\n\t\ttitle: string,\n\t\tmessage: string,\n\t\ttype: ToastMessage[\"type\"]\n\t): void {\n\t\tmessages = [new_message(title, message, -1, type), ...messages];\n\t}\n\n\tlet _error_id = -1;\n\n\tconst MESSAGE_QUOTE_RE = /^'([^]+)'$/;\n\n\tconst DUPLICATE_MESSAGE = $_(\"blocks.long_requests_queue\");\n\tconst MOBILE_QUEUE_WARNING = $_(\"blocks.connection_can_break\");\n\tconst LOST_CONNECTION_MESSAGE =\n\t\t\"Connection to the server was lost. Attempting reconnection...\";\n\tconst CHANGED_CONNECTION_MESSAGE =\n\t\t\"Reconnected to server, but the server has changed. You may need to <a href=''>refresh the page</a>.\";\n\tconst RECONNECTION_MESSAGE = \"Connection re-established.\";\n\tconst SESSION_NOT_FOUND_MESSAGE =\n\t\t\"Session not found - this is likely because the machine you were connected to has changed. <a href=''>Refresh the page</a> to continue.\";\n\tconst WAITING_FOR_INPUTS_MESSAGE = $_(\"blocks.waiting_for_inputs\");\n\tconst SHOW_DUPLICATE_MESSAGE_ON_ETA = 15;\n\tconst SHOW_MOBILE_QUEUE_WARNING_ON_ETA = 10;\n\tlet is_mobile_device = false;\n\tlet showed_duplicate_message = false;\n\tlet showed_mobile_warning = false;\n\tlet inputs_waiting: number[] = [];\n\n\t// as state updates are not synchronous, we need to ensure updates are flushed before triggering any requests\n\tfunction wait_then_trigger_api_call(\n\t\tdep_index: number,\n\t\ttrigger_id: number | null = null,\n\t\tevent_data: unknown = null\n\t): void {\n\t\tlet _unsub = (): void => {};\n\t\tfunction unsub(): void {\n\t\t\t_unsub();\n\t\t}\n\t\tif ($scheduled_updates) {\n\t\t\t_unsub = scheduled_updates.subscribe((updating) => {\n\t\t\t\tif (!updating) {\n\t\t\t\t\ttick().then(() => {\n\t\t\t\t\t\ttrigger_api_call(dep_index, trigger_id, event_data);\n\t\t\t\t\t\tunsub();\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\ttrigger_api_call(dep_index, trigger_id, event_data);\n\t\t}\n\t}\n\n\tasync function get_component_value_or_event_data(\n\t\tcomponent_id: number,\n\t\ttrigger_id: number | null,\n\t\tevent_data: unknown\n\t): Promise<any> {\n\t\tif (\n\t\t\tcomponent_id === trigger_id &&\n\t\t\tevent_data &&\n\t\t\t(event_data as ValueData).is_value_data === true\n\t\t) {\n\t\t\t// @ts-ignore\n\t\t\treturn event_data.value;\n\t\t}\n\t\treturn get_data(component_id);\n\t}\n\n\tasync function trigger_api_call(\n\t\tdep_index: number,\n\t\ttrigger_id: number | null = null,\n\t\tevent_data: unknown = null\n\t): Promise<void> {\n\t\tconst _dep = dependencies.find((dep) => dep.id === dep_index);\n\t\tif (_dep === undefined) {\n\t\t\treturn;\n\t\t}\n\t\tconst dep = _dep;\n\t\tif (inputs_waiting.length > 0) {\n\t\t\tfor (const input of inputs_waiting) {\n\t\t\t\tif (dep.inputs.includes(input)) {\n\t\t\t\t\tadd_new_message(\"Warning\", WAITING_FOR_INPUTS_MESSAGE, \"warning\");\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tconst current_status = loading_status.get_status_for_fn(dep_index);\n\t\tmessages = messages.filter(({ fn_index }) => fn_index !== dep_index);\n\t\tif (current_status === \"pending\" || current_status === \"generating\") {\n\t\t\tdep.pending_request = true;\n\t\t}\n\n\t\tlet payload: Payload = {\n\t\t\tfn_index: dep_index,\n\t\t\tdata: await Promise.all(\n\t\t\t\tdep.inputs.map((id) =>\n\t\t\t\t\tget_component_value_or_event_data(id, trigger_id, event_data)\n\t\t\t\t)\n\t\t\t),\n\t\t\tevent_data: dep.collects_event_data ? event_data : null,\n\t\t\ttrigger_id: trigger_id\n\t\t};\n\n\t\tif (dep.frontend_fn && typeof dep.frontend_fn !== \"boolean\") {\n\t\t\tdep\n\t\t\t\t.frontend_fn(\n\t\t\t\t\tpayload.data.concat(\n\t\t\t\t\t\tawait Promise.all(dep.outputs.map((id) => get_data(id)))\n\t\t\t\t\t)\n\t\t\t\t)\n\t\t\t\t.then((v: unknown[]) => {\n\t\t\t\t\tif (dep.backend_fn) {\n\t\t\t\t\t\tpayload.data = v;\n\t\t\t\t\t\ttrigger_prediction(dep, payload);\n\t\t\t\t\t} else {\n\t\t\t\t\t\thandle_update(v, dep_index);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t} else if (dep.types.cancel && dep.cancels) {\n\t\t\tawait Promise.all(\n\t\t\t\tdep.cancels.map(async (fn_index) => {\n\t\t\t\t\tconst submission = submit_map.get(fn_index);\n\t\t\t\t\tsubmission?.cancel();\n\t\t\t\t\treturn submission;\n\t\t\t\t})\n\t\t\t);\n\t\t} else {\n\t\t\tif (dep.backend_fn) {\n\t\t\t\tif (dep.js_implementation) {\n\t\t\t\t\tlet js_fn = new AsyncFunction(\n\t\t\t\t\t\t`let result = await (${dep.js_implementation})(...arguments);\n\t\t\t\t\t\treturn (!Array.isArray(result)) ? [result] : result;`\n\t\t\t\t\t);\n\t\t\t\t\tjs_fn(...payload.data)\n\t\t\t\t\t\t.then((js_result) => {\n\t\t\t\t\t\t\thandle_update(js_result, dep_index);\n\t\t\t\t\t\t\tpayload.js_implementation = true;\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.catch((error) => {\n\t\t\t\t\t\t\tconsole.error(error);\n\t\t\t\t\t\t\tpayload.js_implementation = false;\n\t\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\ttrigger_prediction(dep, payload);\n\t\t\t}\n\t\t}\n\n\t\tfunction trigger_prediction(dep: Dependency, payload: Payload): void {\n\t\t\tif (dep.trigger_mode === \"once\") {\n\t\t\t\tif (!dep.pending_request)\n\t\t\t\t\tmake_prediction(payload, dep.connection == \"stream\");\n\t\t\t} else if (dep.trigger_mode === \"multiple\") {\n\t\t\t\tmake_prediction(payload, dep.connection == \"stream\");\n\t\t\t} else if (dep.trigger_mode === \"always_last\") {\n\t\t\t\tif (!dep.pending_request) {\n\t\t\t\t\tmake_prediction(payload, dep.connection == \"stream\");\n\t\t\t\t} else {\n\t\t\t\t\tdep.final_event = payload;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tasync function reconnect(): Promise<void> {\n\t\t\tconst connection_status = await app.reconnect();\n\t\t\tif (connection_status === \"broken\") {\n\t\t\t\tsetTimeout(reconnect, 1000);\n\t\t\t} else if (connection_status === \"changed\") {\n\t\t\t\tbroken_connection = false;\n\t\t\t\tmessages = [\n\t\t\t\t\tnew_message(\n\t\t\t\t\t\t\"Changed Connection\",\n\t\t\t\t\t\tCHANGED_CONNECTION_MESSAGE,\n\t\t\t\t\t\t-1,\n\t\t\t\t\t\t\"info\",\n\t\t\t\t\t\t3,\n\t\t\t\t\t\ttrue\n\t\t\t\t\t),\n\t\t\t\t\t...messages.map((m) =>\n\t\t\t\t\t\tm.message === LOST_CONNECTION_MESSAGE ? { ...m, visible: false } : m\n\t\t\t\t\t)\n\t\t\t\t];\n\t\t\t} else if (connection_status === \"connected\") {\n\t\t\t\tbroken_connection = false;\n\t\t\t\tmessages = [\n\t\t\t\t\tnew_message(\n\t\t\t\t\t\t\"Reconnected\",\n\t\t\t\t\t\tRECONNECTION_MESSAGE,\n\t\t\t\t\t\t-1,\n\t\t\t\t\t\t\"success\",\n\t\t\t\t\t\tnull,\n\t\t\t\t\t\ttrue\n\t\t\t\t\t),\n\t\t\t\t\t...messages.map((m) =>\n\t\t\t\t\t\tm.message === LOST_CONNECTION_MESSAGE ? { ...m, visible: false } : m\n\t\t\t\t\t)\n\t\t\t\t];\n\t\t\t}\n\t\t}\n\n\t\tasync function make_prediction(\n\t\t\tpayload: Payload,\n\t\t\tstreaming = false\n\t\t): Promise<void> {\n\t\t\tif (allow_video_trim) {\n\t\t\t\tscreen_recorder.markRemoveSegmentStart();\n\t\t\t}\n\t\t\tif (api_recorder_visible) {\n\t\t\t\tapi_calls = [...api_calls, JSON.parse(JSON.stringify(payload))];\n\t\t\t}\n\n\t\t\tlet submission: ReturnType<typeof app.submit>;\n\t\t\tapp.set_current_payload(payload);\n\t\t\tif (streaming) {\n\t\t\t\tif (!submit_map.has(dep_index)) {\n\t\t\t\t\tdep.inputs.forEach((id) => modify_stream(id, \"waiting\"));\n\t\t\t\t} else if (\n\t\t\t\t\tsubmit_map.has(dep_index) &&\n\t\t\t\t\tdep.inputs.some((id) => get_stream_state(id) === \"waiting\")\n\t\t\t\t) {\n\t\t\t\t\treturn;\n\t\t\t\t} else if (\n\t\t\t\t\tsubmit_map.has(dep_index) &&\n\t\t\t\t\tdep.inputs.some((id) => get_stream_state(id) === \"open\")\n\t\t\t\t) {\n\t\t\t\t\tawait app.send_ws_message(\n\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t`${app.config.root + app.config.api_prefix}/stream/${submit_map.get(dep_index).event_id()}`,\n\t\t\t\t\t\t{ ...payload, session_hash: app.session_hash }\n\t\t\t\t\t);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\ttry {\n\t\t\t\tsubmission = app.submit(\n\t\t\t\t\tpayload.fn_index,\n\t\t\t\t\tpayload.data as unknown[],\n\t\t\t\t\tpayload.event_data,\n\t\t\t\t\tpayload.trigger_id\n\t\t\t\t);\n\t\t\t} catch (e) {\n\t\t\t\tconst fn_index = 0; // Mock value for fn_index\n\t\t\t\tif (app.closed) return; // when a user navigates away in multipage app.\n\t\t\t\tmessages = [\n\t\t\t\t\tnew_message(\"Error\", String(e), fn_index, \"error\"),\n\t\t\t\t\t...messages\n\t\t\t\t];\n\t\t\t\tloading_status.update({\n\t\t\t\t\tstatus: \"error\",\n\t\t\t\t\tfn_index,\n\t\t\t\t\teta: 0,\n\t\t\t\t\tqueue: false,\n\t\t\t\t\tqueue_position: null\n\t\t\t\t});\n\t\t\t\tset_status($loading_status);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tsubmit_map.set(dep_index, submission);\n\n\t\t\tfor await (const message of submission) {\n\t\t\t\tif (payload.js_implementation) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (message.type === \"data\") {\n\t\t\t\t\thandle_data(message);\n\t\t\t\t} else if (message.type === \"render\") {\n\t\t\t\t\thandle_render(message);\n\t\t\t\t} else if (message.type === \"status\") {\n\t\t\t\t\thandle_status_update(message);\n\t\t\t\t} else if (message.type === \"log\") {\n\t\t\t\t\thandle_log(message);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfunction handle_data(message: Payload): void {\n\t\t\t\tconst { data, fn_index } = message;\n\t\t\t\tif (dep.pending_request && dep.final_event) {\n\t\t\t\t\tdep.pending_request = false;\n\t\t\t\t\tmake_prediction(dep.final_event, dep.connection == \"stream\");\n\t\t\t\t}\n\t\t\t\tdep.pending_request = false;\n\t\t\t\thandle_update(data, fn_index);\n\t\t\t\tset_status($loading_status);\n\t\t\t}\n\n\t\t\tfunction handle_render(message: RenderMessage): void {\n\t\t\t\tconst { data } = message;\n\t\t\t\tlet _components: ComponentMeta[] = data.components;\n\t\t\t\tlet render_layout: LayoutNode = data.layout;\n\t\t\t\tlet _dependencies: Dependency[] = data.dependencies;\n\t\t\t\tlet render_id = data.render_id;\n\n\t\t\t\tlet deps_to_remove: number[] = [];\n\t\t\t\tdependencies.forEach((old_dep, i) => {\n\t\t\t\t\tif (old_dep.rendered_in === dep.render_id) {\n\t\t\t\t\t\tdeps_to_remove.push(i);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tdeps_to_remove.reverse().forEach((i) => {\n\t\t\t\t\tdependencies.splice(i, 1);\n\t\t\t\t});\n\t\t\t\t_dependencies.forEach((dep) => {\n\t\t\t\t\tdependencies.push(dep);\n\t\t\t\t});\n\n\t\t\t\trerender_layout({\n\t\t\t\t\tcomponents: _components,\n\t\t\t\t\tlayout: render_layout,\n\t\t\t\t\troot: root + api_prefix,\n\t\t\t\t\tdependencies: dependencies,\n\t\t\t\t\trender_id: render_id\n\t\t\t\t});\n\t\t\t\t_dependencies.forEach((dep) => {\n\t\t\t\t\tif (dep.targets.some((dep) => dep[1] === \"load\")) {\n\t\t\t\t\t\twait_then_trigger_api_call(dep.id);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tfunction handle_log(msg: LogMessage): void {\n\t\t\t\tconst { title, log, fn_index, level, duration, visible } = msg;\n\t\t\t\tmessages = [\n\t\t\t\t\tnew_message(title, log, fn_index, level, duration, visible),\n\t\t\t\t\t...messages\n\t\t\t\t];\n\t\t\t}\n\n\t\t\tfunction open_stream_events(\n\t\t\t\tstatus: StatusMessage,\n\t\t\t\tid: number,\n\t\t\t\tdep: Dependency\n\t\t\t): void {\n\t\t\t\tif (\n\t\t\t\t\tstatus.original_msg === \"process_starts\" &&\n\t\t\t\t\tdep.connection === \"stream\"\n\t\t\t\t) {\n\t\t\t\t\tmodify_stream(id, \"open\");\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/* eslint-disable complexity */\n\t\t\tfunction handle_status_update(message: StatusMessage): void {\n\t\t\t\tif (message.broken && !broken_connection) {\n\t\t\t\t\tmessages = [\n\t\t\t\t\t\tnew_message(\n\t\t\t\t\t\t\t\"Broken Connection\",\n\t\t\t\t\t\t\tLOST_CONNECTION_MESSAGE,\n\t\t\t\t\t\t\t-1,\n\t\t\t\t\t\t\t\"error\",\n\t\t\t\t\t\t\tnull,\n\t\t\t\t\t\t\ttrue\n\t\t\t\t\t\t),\n\t\t\t\t\t\t...messages\n\t\t\t\t\t];\n\n\t\t\t\t\tbroken_connection = true;\n\t\t\t\t\tsetTimeout(reconnect, 1000);\n\t\t\t\t}\n\t\t\t\tif (message.session_not_found) {\n\t\t\t\t\tmessages = [\n\t\t\t\t\t\tnew_message(\n\t\t\t\t\t\t\t\"Session Not Found\",\n\t\t\t\t\t\t\tSESSION_NOT_FOUND_MESSAGE,\n\t\t\t\t\t\t\t-1,\n\t\t\t\t\t\t\t\"error\",\n\t\t\t\t\t\t\tnull,\n\t\t\t\t\t\t\ttrue\n\t\t\t\t\t\t),\n\t\t\t\t\t\t...messages\n\t\t\t\t\t];\n\t\t\t\t}\n\t\t\t\tconst { fn_index, ...status } = message;\n\t\t\t\tif (status.stage === \"streaming\" && status.time_limit) {\n\t\t\t\t\tdep.inputs.forEach((id) => {\n\t\t\t\t\t\tset_time_limit(id, status.time_limit);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tdep.inputs.forEach((id) => {\n\t\t\t\t\topen_stream_events(message, id, dep);\n\t\t\t\t});\n\t\t\t\t//@ts-ignore\n\t\t\t\tloading_status.update({\n\t\t\t\t\t...status,\n\t\t\t\t\ttime_limit: status.time_limit,\n\t\t\t\t\tstatus: status.stage,\n\t\t\t\t\tprogress: status.progress_data,\n\t\t\t\t\tfn_index\n\t\t\t\t});\n\t\t\t\tset_status($loading_status);\n\t\t\t\tif (\n\t\t\t\t\t!showed_duplicate_message &&\n\t\t\t\t\tspace_id !== null &&\n\t\t\t\t\tstatus.position !== undefined &&\n\t\t\t\t\tstatus.position >= 2 &&\n\t\t\t\t\tstatus.eta !== undefined &&\n\t\t\t\t\tstatus.eta > SHOW_DUPLICATE_MESSAGE_ON_ETA\n\t\t\t\t) {\n\t\t\t\t\tshowed_duplicate_message = true;\n\t\t\t\t\tmessages = [\n\t\t\t\t\t\tnew_message(\"Warning\", DUPLICATE_MESSAGE, fn_index, \"warning\"),\n\t\t\t\t\t\t...messages\n\t\t\t\t\t];\n\t\t\t\t}\n\t\t\t\tif (\n\t\t\t\t\t!showed_mobile_warning &&\n\t\t\t\t\tis_mobile_device &&\n\t\t\t\t\tstatus.eta !== undefined &&\n\t\t\t\t\tstatus.eta > SHOW_MOBILE_QUEUE_WARNING_ON_ETA\n\t\t\t\t) {\n\t\t\t\t\tshowed_mobile_warning = true;\n\t\t\t\t\tmessages = [\n\t\t\t\t\t\tnew_message(\"Warning\", MOBILE_QUEUE_WARNING, fn_index, \"warning\"),\n\t\t\t\t\t\t...messages\n\t\t\t\t\t];\n\t\t\t\t}\n\n\t\t\t\tif (status.stage === \"complete\" || status.stage === \"generating\") {\n\t\t\t\t\tconst deps_triggered_by_state: Set<Dependency> = new Set();\n\t\t\t\t\tstatus.changed_state_ids?.forEach((id) => {\n\t\t\t\t\t\tdependencies\n\t\t\t\t\t\t\t.filter((dep) => dep.targets.some(([_id, _]) => _id === id))\n\t\t\t\t\t\t\t.forEach((dep) => {\n\t\t\t\t\t\t\t\tdeps_triggered_by_state.add(dep);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t\tdeps_triggered_by_state.forEach((dep) => {\n\t\t\t\t\t\twait_then_trigger_api_call(dep.id, payload.trigger_id);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tif (status.stage === \"complete\") {\n\t\t\t\t\tdependencies.forEach(async (dep) => {\n\t\t\t\t\t\tif (dep.trigger_after === fn_index) {\n\t\t\t\t\t\t\twait_then_trigger_api_call(dep.id, payload.trigger_id);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\tdep.inputs.forEach((id) => {\n\t\t\t\t\t\tmodify_stream(id, \"closed\");\n\t\t\t\t\t});\n\t\t\t\t\tsubmit_map.delete(dep_index);\n\t\t\t\t}\n\t\t\t\tif (\n\t\t\t\t\tstatus.stage === \"error\" &&\n\t\t\t\t\t!broken_connection &&\n\t\t\t\t\t!message.session_not_found\n\t\t\t\t) {\n\t\t\t\t\tif (status.message) {\n\t\t\t\t\t\tconst _message = status.message.replace(\n\t\t\t\t\t\t\tMESSAGE_QUOTE_RE,\n\t\t\t\t\t\t\t(_, b) => b\n\t\t\t\t\t\t);\n\t\t\t\t\t\tconst _title = status.title ?? \"Error\";\n\t\t\t\t\t\tmessages = [\n\t\t\t\t\t\t\tnew_message(\n\t\t\t\t\t\t\t\t_title,\n\t\t\t\t\t\t\t\t_message,\n\t\t\t\t\t\t\t\tfn_index,\n\t\t\t\t\t\t\t\t\"error\",\n\t\t\t\t\t\t\t\tstatus.duration,\n\t\t\t\t\t\t\t\tstatus.visible\n\t\t\t\t\t\t\t),\n\t\t\t\t\t\t\t...messages\n\t\t\t\t\t\t];\n\t\t\t\t\t}\n\t\t\t\t\tdependencies.map(async (dep) => {\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\tdep.trigger_after === fn_index &&\n\t\t\t\t\t\t\t!dep.trigger_only_on_success\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\twait_then_trigger_api_call(dep.id, payload.trigger_id);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (allow_video_trim) {\n\t\t\t\tscreen_recorder.markRemoveSegmentEnd();\n\t\t\t}\n\t\t}\n\t}\n\t/* eslint-enable complexity */\n\n\tfunction trigger_share(title: string | undefined, description: string): void {\n\t\tif (space_id === null) {\n\t\t\treturn;\n\t\t}\n\t\tconst discussion_url = new URL(\n\t\t\t`https://huggingface.co/spaces/${space_id}/discussions/new`\n\t\t);\n\t\tif (title !== undefined && title.length > 0) {\n\t\t\tdiscussion_url.searchParams.set(\"title\", title);\n\t\t}\n\t\tdiscussion_url.searchParams.set(\"description\", description);\n\t\twindow.open(discussion_url.toString(), \"_blank\");\n\t}\n\n\tfunction handle_error_close(e: Event & { detail: number }): void {\n\t\tconst _id = e.detail;\n\t\tmessages = messages.filter((m) => m.id !== _id);\n\t}\n\n\tconst is_external_url = (link: string | null): boolean =>\n\t\t!!(link && new URL(link, location.href).origin !== location.origin);\n\n\tasync function handle_mount(): Promise<void> {\n\t\tif (js) {\n\t\t\tlet blocks_frontend_fn = new AsyncFunction(\n\t\t\t\t`let result = await (${js})();\n\t\t\t\t\treturn (!Array.isArray(result)) ? [result] : result;`\n\t\t\t);\n\t\t\tawait blocks_frontend_fn();\n\t\t}\n\n\t\tawait tick();\n\n\t\tvar a = target.getElementsByTagName(\"a\");\n\n\t\tfor (var i = 0; i < a.length; i++) {\n\t\t\tconst _target = a[i].getAttribute(\"target\");\n\t\t\tconst _link = a[i].getAttribute(\"href\");\n\n\t\t\t// only target anchor tags with external links\n\t\t\tif (is_external_url(_link) && _target !== \"_blank\")\n\t\t\t\ta[i].setAttribute(\"target\", \"_blank\");\n\t\t}\n\t\thandle_load_triggers();\n\n\t\tif (!target || render_complete) return;\n\n\t\ttarget.addEventListener(\"prop_change\", (e: Event) => {\n\t\t\tif (!isCustomEvent(e)) throw new Error(\"not a custom event\");\n\t\t\tconst { id, prop, value } = e.detail;\n\t\t\tupdate_value([{ id, prop, value }]);\n\t\t\tif (prop === \"input_ready\" && value === false) {\n\t\t\t\tinputs_waiting.push(id);\n\t\t\t}\n\t\t\tif (prop === \"input_ready\" && value === true) {\n\t\t\t\tinputs_waiting = inputs_waiting.filter((item) => item !== id);\n\t\t\t}\n\t\t});\n\t\ttarget.addEventListener(\"gradio\", (e: Event) => {\n\t\t\tif (!isCustomEvent(e)) throw new Error(\"not a custom event\");\n\n\t\t\tconst { id, event, data } = e.detail;\n\n\t\t\tif (event === \"share\") {\n\t\t\t\tconst { title, description } = data as ShareData;\n\t\t\t\ttrigger_share(title, description);\n\t\t\t} else if (event === \"error\") {\n\t\t\t\tmessages = [new_message(\"Error\", data, -1, event), ...messages];\n\t\t\t} else if (event === \"warning\") {\n\t\t\t\tmessages = [new_message(\"Warning\", data, -1, event), ...messages];\n\t\t\t} else if (event === \"info\") {\n\t\t\t\tmessages = [new_message(\"Info\", data, -1, event), ...messages];\n\t\t\t} else if (event == \"clear_status\") {\n\t\t\t\tupdate_status(id, \"complete\", data);\n\t\t\t} else if (event == \"close_stream\") {\n\t\t\t\tconst deps = $targets[id]?.[data];\n\t\t\t\tdeps?.forEach((dep_id) => {\n\t\t\t\t\tif (submit_map.has(dep_id)) {\n\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\tconst url = `${app.config.root + app.config.api_prefix}/stream/${submit_map.get(dep_id).event_id()}`;\n\t\t\t\t\t\tapp.post_data(`${url}/close`, {});\n\t\t\t\t\t\tapp.close_ws(url);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tconst deps = $targets[id]?.[event];\n\n\t\t\t\tdeps?.forEach((dep_id) => {\n\t\t\t\t\trequestAnimationFrame(() => {\n\t\t\t\t\t\twait_then_trigger_api_call(dep_id, id, data);\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\n\t\trender_complete = true;\n\t}\n\n\tvalue_change((id, value) => {\n\t\tconst deps = $targets[id]?.[\"change\"];\n\n\t\tdeps?.forEach((dep_id) => {\n\t\t\trequestAnimationFrame(() => {\n\t\t\t\twait_then_trigger_api_call(dep_id, id, value);\n\t\t\t});\n\t\t});\n\t});\n\n\tconst handle_load_triggers = (): void => {\n\t\tdependencies.forEach((dep) => {\n\t\t\tif (dep.targets.some((dep) => dep[1] === \"load\")) {\n\t\t\t\twait_then_trigger_api_call(dep.id);\n\t\t\t}\n\t\t});\n\t};\n\n\t$: set_status($loading_status);\n\n\tfunction update_status(\n\t\tid: number,\n\t\tstatus: \"error\" | \"complete\" | \"pending\",\n\t\tdata: LoadingStatus\n\t): void {\n\t\tdata.status = status;\n\t\tupdate_value([\n\t\t\t{\n\t\t\t\tid,\n\t\t\t\tprop: \"loading_status\",\n\t\t\t\tvalue: data\n\t\t\t}\n\t\t]);\n\t}\n\n\tfunction set_status(statuses: LoadingStatusCollection): void {\n\t\tlet updates: {\n\t\t\tid: number;\n\t\t\tprop: string;\n\t\t\tvalue: LoadingStatus;\n\t\t}[] = [];\n\t\tObject.entries(statuses).forEach(([id, loading_status]) => {\n\t\t\tif (app.closed && loading_status.status === \"error\") {\n\t\t\t\t// when a user navigates away in multipage app.\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tlet dependency = dependencies.find(\n\t\t\t\t(dep) => dep.id == loading_status.fn_index\n\t\t\t);\n\t\t\tif (dependency === undefined) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tloading_status.scroll_to_output = dependency.scroll_to_output;\n\t\t\tloading_status.show_progress = dependency.show_progress;\n\t\t\tupdates.push({\n\t\t\t\tid: parseInt(id),\n\t\t\t\tprop: \"loading_status\",\n\t\t\t\tvalue: loading_status\n\t\t\t});\n\t\t});\n\n\t\tconst inputs_to_update = loading_status.get_inputs_to_update();\n\t\tconst additional_updates = Array.from(inputs_to_update).map(\n\t\t\t([id, pending_status]) => {\n\t\t\t\treturn {\n\t\t\t\t\tid,\n\t\t\t\t\tprop: \"pending\",\n\t\t\t\t\tvalue: pending_status === \"pending\"\n\t\t\t\t};\n\t\t\t}\n\t\t);\n\n\t\tupdate_value([...updates, ...additional_updates]);\n\t}\n\n\tfunction isCustomEvent(event: Event): event is CustomEvent {\n\t\treturn \"detail\" in event;\n\t}\n\n\tlet is_screen_recording = writable(false);\n\n\tonMount(() => {\n\t\tis_mobile_device =\n\t\t\t/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(\n\t\t\t\tnavigator.userAgent\n\t\t\t);\n\n\t\tscreen_recorder.initialize(\n\t\t\troot,\n\t\t\t(title, message, type) => {\n\t\t\t\tadd_new_message(title, message, type);\n\t\t\t},\n\t\t\t(isRecording) => {\n\t\t\t\t$is_screen_recording = isRecording;\n\t\t\t}\n\t\t);\n\n\t\t// Load components if they should be visible on initial page load\n\t\tif (api_docs_visible) {\n\t\t\tloadApiDocs();\n\t\t}\n\t\tif (api_recorder_visible) {\n\t\t\tloadApiRecorder();\n\t\t}\n\t\tif (settings_visible) {\n\t\t\tloadSettings();\n\t\t}\n\t});\n\n\tfunction screen_recording(): void {\n\t\tif ($is_screen_recording) {\n\t\t\tscreen_recorder.stopRecording();\n\t\t} else {\n\t\t\tscreen_recorder.startRecording();\n\t\t}\n\t}\n</script>\n\n<svelte:head>\n\t{#if control_page_title}\n\t\t<title>{title}</title>\n\t{/if}\n\t{#if css}\n\t\t{@html `\\<style\\>${prefix_css(css, version)}</style>`}\n\t{/if}\n</svelte:head>\n\n<div class=\"wrap\" style:min-height={app_mode ? \"100%\" : \"auto\"}>\n\t<div class=\"contain\" style:flex-grow={app_mode ? \"1\" : \"auto\"}>\n\t\t{#if $_layout && app.config}\n\t\t\t<MountComponents\n\t\t\t\trootNode={$_layout}\n\t\t\t\t{root}\n\t\t\t\t{target}\n\t\t\t\t{theme_mode}\n\t\t\t\ton:mount={handle_mount}\n\t\t\t\t{version}\n\t\t\t\t{autoscroll}\n\t\t\t\t{max_file_size}\n\t\t\t\tclient={app}\n\t\t\t/>\n\t\t{/if}\n\t</div>\n\n\t{#if show_footer}\n\t\t<footer>\n\t\t\t{#if show_api}\n\t\t\t\t<button\n\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\tset_api_docs_visible(!api_docs_visible);\n\t\t\t\t\t}}\n\t\t\t\t\ton:mouseenter={() => {\n\t\t\t\t\t\tloadApiDocs();\n\t\t\t\t\t\tloadApiRecorder();\n\t\t\t\t\t}}\n\t\t\t\t\tclass=\"show-api\"\n\t\t\t\t>\n\t\t\t\t\t{#if app.config?.mcp_server}\n\t\t\t\t\t\t{$_(\"errors.use_via_api_or_mcp\")}\n\t\t\t\t\t{:else}\n\t\t\t\t\t\t{$_(\"errors.use_via_api\")}\n\t\t\t\t\t{/if}\n\t\t\t\t\t<img src={api_logo} alt={$_(\"common.logo\")} />\n\t\t\t\t</button>\n\t\t\t\t<div class=\"divider show-api-divider\">·</div>\n\t\t\t{/if}\n\t\t\t<a\n\t\t\t\thref=\"https://gradio.app\"\n\t\t\t\tclass=\"built-with\"\n\t\t\t\ttarget=\"_blank\"\n\t\t\t\trel=\"noreferrer\"\n\t\t\t>\n\t\t\t\t{$_(\"common.built_with_gradio\")}\n\t\t\t\t<img src={logo} alt={$_(\"common.logo\")} />\n\t\t\t</a>\n\t\t\t<div class=\"divider\" class:hidden={!$is_screen_recording}>·</div>\n\t\t\t<button\n\t\t\t\tclass:hidden={!$is_screen_recording}\n\t\t\t\ton:click={() => {\n\t\t\t\t\tscreen_recording();\n\t\t\t\t}}\n\t\t\t\tclass=\"record\"\n\t\t\t>\n\t\t\t\t{$_(\"common.stop_recording\")}\n\t\t\t\t<img src={record_stop} alt={$_(\"common.stop_recording\")} />\n\t\t\t</button>\n\t\t\t<div class=\"divider\">·</div>\n\t\t\t<button\n\t\t\t\ton:click={() => {\n\t\t\t\t\tset_settings_visible(!settings_visible);\n\t\t\t\t}}\n\t\t\t\ton:mouseenter={() => {\n\t\t\t\t\tloadSettings();\n\t\t\t\t}}\n\t\t\t\tclass=\"settings\"\n\t\t\t>\n\t\t\t\t{$_(\"common.settings\")}\n\t\t\t\t<img src={settings_logo} alt={$_(\"common.settings\")} />\n\t\t\t</button>\n\t\t</footer>\n\t{/if}\n</div>\n\n{#if api_recorder_visible && ApiRecorder}\n\t<!-- TODO: fix -->\n\t<!-- svelte-ignore a11y-click-events-have-key-events-->\n\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t<div\n\t\tid=\"api-recorder-container\"\n\t\ton:click={() => {\n\t\t\tset_api_docs_visible(true);\n\t\t\tapi_recorder_visible = false;\n\t\t}}\n\t>\n\t\t<svelte:component this={ApiRecorder} {api_calls} {dependencies} />\n\t</div>\n{/if}\n\n{#if api_docs_visible && $_layout && ApiDocs}\n\t<div class=\"api-docs\">\n\t\t<!-- TODO: fix -->\n\t\t<!-- svelte-ignore a11y-click-events-have-key-events-->\n\t\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t\t<div\n\t\t\tclass=\"backdrop\"\n\t\t\ton:click={() => {\n\t\t\t\tset_api_docs_visible(false);\n\t\t\t}}\n\t\t/>\n\t\t<div class=\"api-docs-wrap\">\n\t\t\t<svelte:component\n\t\t\t\tthis={ApiDocs}\n\t\t\t\troot_node={$_layout}\n\t\t\t\ton:close={(event) => {\n\t\t\t\t\tset_api_docs_visible(false);\n\t\t\t\t\tapi_calls = [];\n\t\t\t\t\tapi_recorder_visible = api_recorder_visible =\n\t\t\t\t\t\tevent.detail?.api_recorder_visible;\n\t\t\t\t}}\n\t\t\t\t{dependencies}\n\t\t\t\t{root}\n\t\t\t\t{app}\n\t\t\t\t{space_id}\n\t\t\t\t{api_calls}\n\t\t\t\t{username}\n\t\t\t/>\n\t\t</div>\n\t</div>\n{/if}\n\n{#if settings_visible && $_layout && app.config && Settings}\n\t<div class=\"api-docs\">\n\t\t<!-- TODO: fix -->\n\t\t<!-- svelte-ignore a11y-click-events-have-key-events-->\n\t\t<!-- svelte-ignore a11y-no-static-element-interactions-->\n\t\t<div\n\t\t\tclass=\"backdrop\"\n\t\t\ton:click={() => {\n\t\t\t\tset_settings_visible(false);\n\t\t\t}}\n\t\t/>\n\t\t<div class=\"api-docs-wrap\">\n\t\t\t<svelte:component\n\t\t\t\tthis={Settings}\n\t\t\t\tbind:allow_zoom\n\t\t\t\tbind:allow_video_trim\n\t\t\t\ton:close={() => {\n\t\t\t\t\tset_settings_visible(false);\n\t\t\t\t}}\n\t\t\t\ton:start_recording={() => {\n\t\t\t\t\tscreen_recording();\n\t\t\t\t}}\n\t\t\t\tpwa_enabled={app.config.pwa}\n\t\t\t\t{root}\n\t\t\t\t{space_id}\n\t\t\t/>\n\t\t</div>\n\t</div>\n{/if}\n\n{#if messages}\n\t<Toast {messages} on:close={handle_error_close} />\n{/if}\n\n<style>\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-grow: 1;\n\t\tflex-direction: column;\n\t\twidth: var(--size-full);\n\t\tfont-weight: var(--body-text-weight);\n\t\tfont-size: var(--body-text-size);\n\t}\n\n\t.contain {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\tfooter {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tmargin-top: var(--size-4);\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\t.divider {\n\t\tmargin-left: var(--size-1);\n\t\tmargin-right: var(--size-2);\n\t}\n\n\t.show-api,\n\t.settings,\n\t.record {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\t.show-api:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.show-api img {\n\t\tmargin-right: var(--size-1);\n\t\tmargin-left: var(--size-2);\n\t\twidth: var(--size-3);\n\t}\n\n\t.settings img {\n\t\tmargin-right: var(--size-1);\n\t\tmargin-left: var(--size-1);\n\t\twidth: var(--size-4);\n\t}\n\n\t.record img {\n\t\tmargin-right: var(--size-1);\n\t\tmargin-left: var(--size-1);\n\t\twidth: var(--size-3);\n\t}\n\n\t.built-with {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.built-with:hover,\n\t.settings:hover,\n\t.record:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.built-with img {\n\t\tmargin-right: var(--size-1);\n\t\tmargin-left: var(--size-1);\n\t\tmargin-bottom: 1px;\n\t\twidth: var(--size-4);\n\t}\n\n\t.api-docs {\n\t\tdisplay: flex;\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tz-index: var(--layer-top);\n\t\tbackground: rgba(0, 0, 0, 0.5);\n\t\twidth: var(--size-screen);\n\t\theight: var(--size-screen-h);\n\t}\n\n\t.backdrop {\n\t\tflex: 1 1 0%;\n\t\t-webkit-backdrop-filter: blur(4px);\n\t\tbackdrop-filter: blur(4px);\n\t}\n\n\t.api-docs-wrap {\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tbackground: var(--background-fill-primary);\n\t\toverflow-x: hidden;\n\t\toverflow-y: auto;\n\t}\n\n\t@media (--screen-md) {\n\t\t.api-docs-wrap {\n\t\t\tborder-top-left-radius: var(--radius-lg);\n\t\t\tborder-bottom-left-radius: var(--radius-lg);\n\t\t\twidth: 950px;\n\t\t}\n\t}\n\n\t@media (--screen-xxl) {\n\t\t.api-docs-wrap {\n\t\t\twidth: 1150px;\n\t\t}\n\t}\n\n\t#api-recorder-container {\n\t\tposition: fixed;\n\t\tleft: 10px;\n\t\tbottom: 10px;\n\t\tz-index: 1000;\n\t}\n\n\t.show-api {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t@media (max-width: 640px) {\n\t\t.show-api,\n\t\t.show-api-divider {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n\n\t.show-api:hover {\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.hidden {\n\t\tdisplay: none;\n\t}\n</style>\n"], "file": "assets/Blocks-DQBXfWaA.js"}