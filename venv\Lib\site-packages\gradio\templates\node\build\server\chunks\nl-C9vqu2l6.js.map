{"version": 3, "file": "nl-C9vqu2l6.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/nl.js"], "sourcesContent": ["const e=\"Nederlands\",o={built_with_gradio:\"Gemaakt met Gradio\",clear:\"Wissen\",or:\"of\",submit:\"Verzenden\",settings:\"Instellingen\",built_with:\"Gebouwd met\",download:\"Downloaden\",edit:\"Bewerken\",empty:\"Leeg\",error:\"Fout\",hosted_on:\"Gehost op\",loading:\"Laden\",logo:\"Logo\",remove:\"Verwijderen\",share:\"Delen\",undo:\"Ongedaan maken\",no_devices:\"Geen apparaten gevonden\",language:\"Taal\",display_theme:\"Weergavethema\",pwa:\"Progressieve Web App\"},n={click_to_upload:\"Klik om te uploaden\",drop_audio:\"Sleep een geluidsbestand hier\",drop_csv:\"Sleep een CSV-bestand hier\",drop_file:\"Sleep een bestand hier\",drop_image:\"Sleep een afbeelding hier\",drop_video:\"Sleep een video hier\",drop_gallery:\"Sleep media hier\",paste_clipboard:\"Plakken vanuit klembord\"},a={annotated_image:\"Geannoteerde afbeelding\"},t={allow_recording_access:\"Geef alstublieft toegang tot de microfoon voor opname.\",audio:\"Audio\",drop_to_upload:\"Sleep een audiobestand hier om te uploaden\",record_from_microphone:\"Opnemen via microfoon\",stop_recording:\"Opname stoppen\",no_device_support:\"Media-apparaten konden niet worden benaderd. Controleer of u werkt vanaf een beveiligde oorsprong (https) of localhost (of u een geldig SSL-certificaat heeft doorgegeven aan ssl_verify), en of u browsertoestemming heeft gegeven voor toegang tot uw apparaat.\",stop:\"Stoppen\",resume:\"Hervatten\",record:\"Opnemen\",no_microphone:\"Geen microfoon gevonden\",pause:\"Pauzeren\",play:\"Afspelen\",waiting:\"Wachten\"},r={connection_can_break:\"Op mobiel kan de verbinding verbroken worden als dit tabblad niet actief is of het apparaat in slaapstand gaat, waardoor je plaats in de wachtrij verloren gaat.\",long_requests_queue:\"Er is een lange wachtrij van aanvragen. Dupliceer deze Space om de wachtrij over te slaan.\",lost_connection:\"Verbinding verloren door het verlaten van de pagina. Opnieuw aansluiten in de wachtrij...\",waiting_for_inputs:\"Wachten tot het uploaden van bestand(en) is voltooid, probeer het opnieuw.\"},i={checkbox:\"Selectievakje\",checkbox_group:\"Selectievakjesgroep\"},d={code:\"Code\"},l={color_picker:\"Kleurkiezer\"},s={incorrect_format:\"Onjuist formaat, alleen CSV- en TSV-bestanden worden ondersteund\",new_column:\"Kolom toevoegen\",new_row:\"Nieuwe rij\",add_row_above:\"Rij boven toevoegen\",add_row_below:\"Rij onder toevoegen\",delete_row:\"Rij verwijderen\",delete_column:\"Kolom verwijderen\",add_column_left:\"Kolom links toevoegen\",add_column_right:\"Kolom rechts toevoegen\",sort_column:\"Kolom sorteren\",sort_ascending:\"Oplopend sorteren\",sort_descending:\"Aflopend sorteren\",drop_to_upload:\"Sleep CSV- of TSV-bestanden hier om gegevens in het dataframe te importeren\",clear_sort:\"Sortering wissen\"},c={dropdown:\"Vervolgkeuzelijst\"},p={build_error:\"Er is een bouwfout\",config_error:\"Er is een configuratiefout\",contact_page_author:\"Neem contact op met de auteur van de pagina om dit te melden.\",no_app_file:\"Er is geen app-bestand\",runtime_error:\"Er is een runtime-fout\",space_not_working:'\"Space werkt niet omdat\" {0}',space_paused:\"De Space is gepauzeerd\",use_via_api:\"Gebruik via API\",use_via_api_or_mcp:\"Gebruik via API of MCP\"},u={uploading:\"Uploaden...\"},g={highlighted_text:\"Gemarkeerde tekst\"},_={allow_webcam_access:\"Geef alstublieft toegang tot de webcam voor opname.\",brush_color:\"Penseelkleur\",brush_radius:\"Penseelgrootte\",image:\"Afbeelding\",remove_image:\"Afbeelding verwijderen\",select_brush_color:\"Penseelkleur kiezen\",start_drawing:\"Beginnen met tekenen\",use_brush:\"Penseel gebruiken\",drop_to_upload:\"Sleep een afbeelding hier om te uploaden\"},m={label:\"Label\"},b={enable_cookies:\"Als u een HuggingFace Space bezoekt in incognitomodus, moet u cookies van derden inschakelen.\",incorrect_credentials:\"Onjuiste inloggegevens\",username:\"Gebruikersnaam\",password:\"Wachtwoord\",login:\"Inloggen\"},h={number:\"Getal\"},v={plot:\"Grafiek\"},k={radio:\"Keuzerondje\"},w={slider:\"Schuifbalk\"},f={drop_to_upload:\"Sleep een videobestand hier om te uploaden\"},S={edit:\"Bewerken\",retry:\"Opnieuw proberen\",undo:\"Ongedaan maken\",submit:\"Verzenden\",cancel:\"Annuleren\",like:\"Vind ik leuk\",dislike:\"Vind ik niet leuk\",clear:\"Gesprek wissen\"},j={_name:e,common:o,upload_text:n,\"3D_model\":{\"3d_model\":\"3D-model\",drop_to_upload:\"Sleep een 3D-model (.obj, .glb, .stl, .gltf, .splat of .ply) bestand hier om te uploaden\"},annotated_image:a,audio:t,blocks:r,checkbox:i,code:d,color_picker:l,dataframe:s,dropdown:c,errors:p,file:u,highlighted_text:g,image:_,label:m,login:b,number:h,plot:v,radio:k,slider:w,video:f,chatbot:S};export{e as _name,a as annotated_image,t as audio,r as blocks,S as chatbot,i as checkbox,d as code,l as color_picker,o as common,s as dataframe,j as default,c as dropdown,p as errors,u as file,g as highlighted_text,_ as image,m as label,b as login,h as number,v as plot,k as radio,w as slider,n as upload_text,f as video};\n//# sourceMappingURL=nl.js.map\n"], "names": [], "mappings": "AAAK,MAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,yBAAyB,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,qBAAqB,CAAC,UAAU,CAAC,+BAA+B,CAAC,QAAQ,CAAC,4BAA4B,CAAC,SAAS,CAAC,wBAAwB,CAAC,UAAU,CAAC,2BAA2B,CAAC,UAAU,CAAC,sBAAsB,CAAC,YAAY,CAAC,kBAAkB,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,wDAAwD,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,4CAA4C,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,cAAc,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,mQAAmQ,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,yBAAyB,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,kKAAkK,CAAC,mBAAmB,CAAC,4FAA4F,CAAC,eAAe,CAAC,2FAA2F,CAAC,kBAAkB,CAAC,4EAA4E,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,kEAAkE,CAAC,UAAU,CAAC,iBAAiB,CAAC,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC,qBAAqB,CAAC,aAAa,CAAC,qBAAqB,CAAC,UAAU,CAAC,iBAAiB,CAAC,aAAa,CAAC,mBAAmB,CAAC,eAAe,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,WAAW,CAAC,gBAAgB,CAAC,cAAc,CAAC,mBAAmB,CAAC,eAAe,CAAC,mBAAmB,CAAC,cAAc,CAAC,6EAA6E,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,oBAAoB,CAAC,YAAY,CAAC,4BAA4B,CAAC,mBAAmB,CAAC,+DAA+D,CAAC,WAAW,CAAC,wBAAwB,CAAC,aAAa,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,8BAA8B,CAAC,YAAY,CAAC,wBAAwB,CAAC,WAAW,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,qDAAqD,CAAC,WAAW,CAAC,cAAc,CAAC,YAAY,CAAC,gBAAgB,CAAC,KAAK,CAAC,YAAY,CAAC,YAAY,CAAC,wBAAwB,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,aAAa,CAAC,sBAAsB,CAAC,SAAS,CAAC,mBAAmB,CAAC,cAAc,CAAC,0CAA0C,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,+FAA+F,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,4CAA4C,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,cAAc,CAAC,0FAA0F,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;;;;"}