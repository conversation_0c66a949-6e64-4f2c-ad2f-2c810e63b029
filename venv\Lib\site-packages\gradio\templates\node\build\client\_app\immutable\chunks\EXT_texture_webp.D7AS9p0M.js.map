{"version": 3, "file": "EXT_texture_webp.D7AS9p0M.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/EXT_texture_webp.js"], "sourcesContent": ["import { GLTFLoader, ArrayItem } from \"../glTFLoader.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"EXT_texture_webp\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Vendor/EXT_texture_webp/README.md)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class EXT_texture_webp {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /** The name of this extension. */\n        this.name = NAME;\n        this._loader = loader;\n        this.enabled = loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /**\n     * @internal\n     */\n    _loadTextureAsync(context, texture, assign) {\n        return GLTFLoader.LoadExtensionAsync(context, texture, this.name, (extensionContext, extension) => {\n            const sampler = texture.sampler == undefined ? GLTFLoader.DefaultSampler : ArrayItem.Get(`${context}/sampler`, this._loader.gltf.samplers, texture.sampler);\n            const image = ArrayItem.Get(`${extensionContext}/source`, this._loader.gltf.images, extension.source);\n            return this._loader._createTextureAsync(context, sampler, image, (babylonTexture) => {\n                assign(babylonTexture);\n            }, undefined, !texture._textureInfo.nonColorData);\n        });\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new EXT_texture_webp(loader));\n//# sourceMappingURL=EXT_texture_webp.js.map"], "names": ["NAME", "EXT_texture_webp", "loader", "context", "texture", "assign", "GLTFLoader", "extensionContext", "extension", "sampler", "ArrayItem", "image", "babylonTexture", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "sHAEA,MAAMA,EAAO,mBAKN,MAAMC,CAAiB,CAI1B,YAAYC,EAAQ,CAEhB,KAAK,KAAOF,EACZ,KAAK,QAAUE,EACf,KAAK,QAAUA,EAAO,gBAAgBF,CAAI,CAC7C,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAID,kBAAkBG,EAASC,EAASC,EAAQ,CACxC,OAAOC,EAAW,mBAAmBH,EAASC,EAAS,KAAK,KAAM,CAACG,EAAkBC,IAAc,CAC/F,MAAMC,EAAUL,EAAQ,SAAW,KAAYE,EAAW,eAAiBI,EAAU,IAAI,GAAGP,CAAO,WAAY,KAAK,QAAQ,KAAK,SAAUC,EAAQ,OAAO,EACpJO,EAAQD,EAAU,IAAI,GAAGH,CAAgB,UAAW,KAAK,QAAQ,KAAK,OAAQC,EAAU,MAAM,EACpG,OAAO,KAAK,QAAQ,oBAAoBL,EAASM,EAASE,EAAQC,GAAmB,CACjFP,EAAOO,CAAc,CACxB,EAAE,OAAW,CAACR,EAAQ,aAAa,YAAY,CAC5D,CAAS,CACJ,CACL,CACAS,EAAwBb,CAAI,EAC5Bc,EAAsBd,EAAM,GAAOE,GAAW,IAAID,EAAiBC,CAAM,CAAC", "x_google_ignoreList": [0]}