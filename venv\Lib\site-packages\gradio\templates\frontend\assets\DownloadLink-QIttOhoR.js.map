{"version": 3, "file": "DownloadLink-QIttOhoR.js", "sources": ["../../../../js/wasm/svelte/DownloadLink.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { HTMLAnchorAttributes } from \"svelte/elements\";\n\timport { createEventDispatcher, onMount } from \"svelte\";\n\n\tinterface DownloadLinkAttributes\n\t\textends Omit<HTMLAnchorAttributes, \"target\"> {\n\t\tdownload: NonNullable<HTMLAnchorAttributes[\"download\"]>;\n\t}\n\ttype $$Props = DownloadLinkAttributes;\n\n\timport { getWorkerProxyContext } from \"./context\";\n\timport { should_proxy_wasm_src } from \"./file-url\";\n\timport { getHeaderValue } from \"../src/http\";\n\n\texport let href: DownloadLinkAttributes[\"href\"] = undefined;\n\texport let download: DownloadLinkAttributes[\"download\"];\n\n\tconst dispatch = createEventDispatcher();\n\n\tlet is_downloading = false;\n\tconst worker_proxy = getWorkerProxyContext();\n\tasync function wasm_click_handler(): Promise<void> {\n\t\tif (is_downloading) {\n\t\t\treturn;\n\t\t}\n\n\t\tdispatch(\"click\");\n\n\t\tif (href == null) {\n\t\t\tthrow new Error(\"href is not defined.\");\n\t\t}\n\t\tif (worker_proxy == null) {\n\t\t\tthrow new Error(\"Wasm worker proxy is not available.\");\n\t\t}\n\n\t\tconst url = new URL(href, window.location.href);\n\t\tconst path = url.pathname;\n\n\t\tis_downloading = true;\n\t\tworker_proxy\n\t\t\t.httpRequest({\n\t\t\t\tmethod: \"GET\",\n\t\t\t\tpath,\n\t\t\t\theaders: {},\n\t\t\t\tquery_string: \"\"\n\t\t\t})\n\t\t\t.then((response) => {\n\t\t\t\tif (response.status !== 200) {\n\t\t\t\t\tthrow new Error(`Failed to get file ${path} from the Wasm worker.`);\n\t\t\t\t}\n\t\t\t\tconst blob = new Blob([response.body], {\n\t\t\t\t\ttype: getHeaderValue(response.headers, \"content-type\")\n\t\t\t\t});\n\t\t\t\tconst blobUrl = URL.createObjectURL(blob);\n\n\t\t\t\tconst link = document.createElement(\"a\");\n\t\t\t\tlink.href = blobUrl;\n\t\t\t\tlink.download = download;\n\t\t\t\tlink.click();\n\n\t\t\t\tURL.revokeObjectURL(blobUrl);\n\t\t\t})\n\t\t\t.finally(() => {\n\t\t\t\tis_downloading = false;\n\t\t\t});\n\t}\n</script>\n\n{#if worker_proxy && should_proxy_wasm_src(href)}\n\t{#if is_downloading}\n\t\t<slot />\n\t{:else}\n\t\t<a {...$$restProps} {href} on:click|preventDefault={wasm_click_handler}>\n\t\t\t<slot />\n\t\t</a>\n\t{/if}\n{:else}\n\t<a\n\t\tstyle:position=\"relative\"\n\t\tclass=\"download-link\"\n\t\t{href}\n\t\ttarget={typeof window !== \"undefined\" && window.__is_colab__\n\t\t\t? \"_blank\"\n\t\t\t: null}\n\t\trel=\"noopener noreferrer\"\n\t\t{download}\n\t\t{...$$restProps}\n\t\ton:click={dispatch.bind(null, \"click\")}\n\t>\n\t\t<slot />\n\t</a>\n{/if}\n\n<style>\n\t.unstyled-link {\n\t\tall: unset;\n\t\tcursor: pointer;\n\t}\n</style>\n"], "names": ["createEventDispatcher", "onMount", "a_target_value", "ctx", "insert", "target", "a", "anchor", "dispose", "listen", "should_proxy_wasm_src", "href", "$$props", "download", "dispatch", "is_downloading", "worker_proxy", "getWorkerProxyContext", "wasm_click_handler", "path", "response", "blob", "getHeaderValue", "blobUrl", "link"], "mappings": "8hBAEU,CAAA,sBAAAA,EAAA,QAAAC,IAAsC,OAAA,2IA+E/B,OAAAC,EAAA,OAAA,OAAW,KAAe,OAAO,aAC7C,SACA,kDAGCC,EAAW,CAAA,gJAThBC,EAaGC,EAAAC,EAAAC,CAAA,0BAHQC,EAAAC,EAAAH,EAAA,QAAAH,EAAS,CAAA,EAAA,KAAK,KAAM,OAAO,CAAA,qNADjCA,EAAW,CAAA,yMAjBXA,EAAc,CAAA,EAAA,+WAGXA,EAAW,CAAA,EAAA,CAAA,KAAAA,EAAA,CAAA,CAAA,CAAA,oHAAlBC,EAEGC,EAAAC,EAAAC,CAAA,0CAFiDJ,EAAkB,CAAA,CAAA,CAAA,oGAA/DA,EAAW,CAAA,mdAJfA,EAAY,CAAA,GAAIO,EAAsBP,EAAI,CAAA,CAAA,0YAtDnC,KAAAQ,EAAuC,MAAA,EAAAC,EACvC,CAAA,SAAAC,CAAA,EAAAD,QAELE,EAAWd,QAEbe,EAAiB,SACfC,EAAeC,IACN,eAAAC,GAAA,CACV,GAAAH,YAIJD,EAAS,OAAO,EAEZH,GAAQ,KACD,MAAA,IAAA,MAAM,sBAAsB,KAEnCK,GAAgB,KACT,MAAA,IAAA,MAAM,qCAAqC,EAIhD,MAAAG,EADA,IAAU,IAAIR,EAAM,OAAO,SAAS,IAAI,EAC7B,aAEjBI,EAAiB,EAAA,EACjBC,EACE,YAAA,CACA,OAAQ,MACR,KAAAG,EACA,QAAA,CAAA,EACA,aAAc,KAEd,KAAMC,GAAA,CACF,GAAAA,EAAS,SAAW,IACb,MAAA,IAAA,MAAA,sBAA4BD,CAAI,wBAAA,QAErCE,EAAW,IAAA,KAAA,CAAMD,EAAS,IAAI,GACnC,KAAME,EAAeF,EAAS,QAAS,cAAc,IAEhDG,EAAU,IAAI,gBAAgBF,CAAI,EAElCG,EAAO,SAAS,cAAc,GAAG,EACvCA,EAAK,KAAOD,EACZC,EAAK,SAAWX,EAChBW,EAAK,MAAA,EAEL,IAAI,gBAAgBD,CAAO,CAE3B,CAAA,EAAA,QAAA,IAAA,KACAR,EAAiB,EAAA"}