{"version": 3, "file": "flowGraphCodeExecutionBlock.BITVoFWe.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Utils/flowGraphCodeExecutionBlock.js"], "sourcesContent": ["import { FlowGraphBlock } from \"../../../flowGraphBlock.js\";\nimport { RichTypeAny } from \"../../../flowGraphRichTypes.js\";\n/**\n * This block takes in a function that is defined OUTSIDE of the flow graph and executes it.\n * The function can be a normal function or an async function.\n * The function's arguments will be the value of the input connection as the first variable, and the flow graph context as the second variable.\n */\nexport class FlowGraphCodeExecutionBlock extends FlowGraphBlock {\n    /**\n     * Construct a FlowGraphCodeExecutionBlock.\n     * @param config construction parameters\n     */\n    constructor(config) {\n        super(config);\n        this.config = config;\n        this.executionFunction = this.registerDataInput(\"function\", RichTypeAny);\n        this.value = this.registerDataInput(\"value\", RichTypeAny);\n        this.result = this.registerDataOutput(\"result\", RichTypeAny);\n    }\n    /**\n     * @internal\n     */\n    _updateOutputs(context) {\n        const func = this.executionFunction.getValue(context);\n        const value = this.value.getValue(context);\n        if (func) {\n            this.result.setValue(func(value, context), context);\n        }\n    }\n    getClassName() {\n        return \"FlowGraphCodeExecutionBlock\" /* FlowGraphBlockNames.CodeExecution */;\n    }\n}\n//# sourceMappingURL=flowGraphCodeExecutionBlock.js.map"], "names": ["FlowGraphCodeExecutionBlock", "FlowGraphBlock", "config", "RichTypeAny", "context", "func", "value"], "mappings": "wGAOO,MAAMA,UAAoCC,CAAe,CAK5D,YAAYC,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,OAASA,EACd,KAAK,kBAAoB,KAAK,kBAAkB,WAAYC,CAAW,EACvE,KAAK,MAAQ,KAAK,kBAAkB,QAASA,CAAW,EACxD,KAAK,OAAS,KAAK,mBAAmB,SAAUA,CAAW,CAC9D,CAID,eAAeC,EAAS,CACpB,MAAMC,EAAO,KAAK,kBAAkB,SAASD,CAAO,EAC9CE,EAAQ,KAAK,MAAM,SAASF,CAAO,EACrCC,GACA,KAAK,OAAO,SAASA,EAAKC,EAAOF,CAAO,EAAGA,CAAO,CAEzD,CACD,cAAe,CACX,MAAO,6BACV,CACL", "x_google_ignoreList": [0]}