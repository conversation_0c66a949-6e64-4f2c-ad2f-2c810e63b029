import{F as r}from"./KHR_interactivity-DTxiAnOo.js";import{q as u,R as o}from"./declarationMapper-BZjsjg7g.js";import{R as a}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class c extends r{constructor(t){super(t),this.functionName=this.registerDataInput("functionName",u),this.object=this.registerDataInput("object",o),this.context=this.registerDataInput("context",o,null),this.output=this.registerDataOutput("output",o)}_updateOutputs(t){const i=this.functionName.getValue(t),s=this.object.getValue(t),n=this.context.getValue(t);if(s&&i){const e=s[i];e&&typeof e=="function"&&this.output.setValue(e.bind(n),t)}}getClassName(){return"FlowGraphFunctionReference"}}a("FlowGraphFunctionReference",c);export{c as FlowGraphFunctionReferenceBlock};
//# sourceMappingURL=flowGraphFunctionReferenceBlock-CB8njG4c.js.map
