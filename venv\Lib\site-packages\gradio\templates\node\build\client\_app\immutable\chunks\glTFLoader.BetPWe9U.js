const __vite__fileDeps=["./animationGroup.CYmPDV4x.js","./index.BoI39RQH.js","./preload-helper.D6kgxu3v.js","./bone.F-7hGbXp.js","./glTFLoaderAnimation.CKms7EUf.js","./objectModelMapping.ha_8hIyl.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as le}from"./preload-helper.D6kgxu3v.js";import{_ as b,s as A,b8 as te,b as R,b9 as ve,V as v,O as P,ax as O,ba as xe,M as B,bb as Me,c as ge,R as Ce,bc as we,A as Te,ay as Pe,ak as ae,bd as ue,h as T,ap as ce,a as j,aO as Se,S as de,G as Oe,aa as g,T as C,q as Re,be as ke,bf as K,bg as q,bh as ye,bi as Ee,bj as Ie,af as Le,ad as De,ao as Ne,an as Be,t as N,bk as $e,ab as fe,aM as Ue,aN as _e,bl as Ve,aT as Fe,Q as z,aL as Ge,ac as We,ar as $,C as H,at as Xe,bm as Ye,bn as Ze,bo as Ke,bp as qe}from"./index.BoI39RQH.js";import{B as re}from"./bone.F-7hGbXp.js";import{R as pe}from"./rawTexture.CJKlnpAh.js";import{A as ze}from"./assetContainer.w1CRG79i.js";import{c as J}from"./objectModelMapping.ha_8hIyl.js";class I{constructor(){this.keysUp=[38],this.keysUpward=[33],this.keysDown=[40],this.keysDownward=[34],this.keysLeft=[37],this.keysRight=[39],this.rotationSpeed=.5,this.keysRotateLeft=[],this.keysRotateRight=[],this.keysRotateUp=[],this.keysRotateDown=[],this._keys=new Array}attachControl(e){e=R.BackCompatCameraNoPreventDefault(arguments),!this._onCanvasBlurObserver&&(this._scene=this.camera.getScene(),this._engine=this._scene.getEngine(),this._onCanvasBlurObserver=this._engine.onCanvasBlurObservable.add(()=>{this._keys.length=0}),this._onKeyboardObserver=this._scene.onKeyboardObservable.add(t=>{const s=t.event;if(!s.metaKey){if(t.type===ve.KEYDOWN)(this.keysUp.indexOf(s.keyCode)!==-1||this.keysDown.indexOf(s.keyCode)!==-1||this.keysLeft.indexOf(s.keyCode)!==-1||this.keysRight.indexOf(s.keyCode)!==-1||this.keysUpward.indexOf(s.keyCode)!==-1||this.keysDownward.indexOf(s.keyCode)!==-1||this.keysRotateLeft.indexOf(s.keyCode)!==-1||this.keysRotateRight.indexOf(s.keyCode)!==-1||this.keysRotateUp.indexOf(s.keyCode)!==-1||this.keysRotateDown.indexOf(s.keyCode)!==-1)&&(this._keys.indexOf(s.keyCode)===-1&&this._keys.push(s.keyCode),e||s.preventDefault());else if(this.keysUp.indexOf(s.keyCode)!==-1||this.keysDown.indexOf(s.keyCode)!==-1||this.keysLeft.indexOf(s.keyCode)!==-1||this.keysRight.indexOf(s.keyCode)!==-1||this.keysUpward.indexOf(s.keyCode)!==-1||this.keysDownward.indexOf(s.keyCode)!==-1||this.keysRotateLeft.indexOf(s.keyCode)!==-1||this.keysRotateRight.indexOf(s.keyCode)!==-1||this.keysRotateUp.indexOf(s.keyCode)!==-1||this.keysRotateDown.indexOf(s.keyCode)!==-1){const n=this._keys.indexOf(s.keyCode);n>=0&&this._keys.splice(n,1),e||s.preventDefault()}}}))}detachControl(){this._scene&&(this._onKeyboardObserver&&this._scene.onKeyboardObservable.remove(this._onKeyboardObserver),this._onCanvasBlurObserver&&this._engine.onCanvasBlurObservable.remove(this._onCanvasBlurObserver),this._onKeyboardObserver=null,this._onCanvasBlurObserver=null),this._keys.length=0}checkInputs(){if(this._onKeyboardObserver){const e=this.camera;for(let t=0;t<this._keys.length;t++){const s=this._keys[t],n=e._computeLocalCameraSpeed();this.keysLeft.indexOf(s)!==-1?e._localDirection.copyFromFloats(-n,0,0):this.keysUp.indexOf(s)!==-1?e._localDirection.copyFromFloats(0,0,n):this.keysRight.indexOf(s)!==-1?e._localDirection.copyFromFloats(n,0,0):this.keysDown.indexOf(s)!==-1?e._localDirection.copyFromFloats(0,0,-n):this.keysUpward.indexOf(s)!==-1?e._localDirection.copyFromFloats(0,n,0):this.keysDownward.indexOf(s)!==-1?e._localDirection.copyFromFloats(0,-n,0):this.keysRotateLeft.indexOf(s)!==-1?(e._localDirection.copyFromFloats(0,0,0),e.cameraRotation.y-=this._getLocalRotation()):this.keysRotateRight.indexOf(s)!==-1?(e._localDirection.copyFromFloats(0,0,0),e.cameraRotation.y+=this._getLocalRotation()):this.keysRotateUp.indexOf(s)!==-1?(e._localDirection.copyFromFloats(0,0,0),e.cameraRotation.x-=this._getLocalRotation()):this.keysRotateDown.indexOf(s)!==-1&&(e._localDirection.copyFromFloats(0,0,0),e.cameraRotation.x+=this._getLocalRotation()),e.getScene().useRightHandedSystem&&(e._localDirection.z*=-1),e.getViewMatrix().invertToRef(e._cameraTransformMatrix),v.TransformNormalToRef(e._localDirection,e._cameraTransformMatrix,e._transformedDirection),e.cameraDirection.addInPlace(e._transformedDirection)}}}getClassName(){return"FreeCameraKeyboardMoveInput"}_onLostFocus(){this._keys.length=0}getSimpleName(){return"keyboard"}_getLocalRotation(){const e=this.camera._calculateHandednessMultiplier();return this.rotationSpeed*this._engine.getDeltaTime()/1e3*e}}b([A()],I.prototype,"keysUp",void 0);b([A()],I.prototype,"keysUpward",void 0);b([A()],I.prototype,"keysDown",void 0);b([A()],I.prototype,"keysDownward",void 0);b([A()],I.prototype,"keysLeft",void 0);b([A()],I.prototype,"keysRight",void 0);b([A()],I.prototype,"rotationSpeed",void 0);b([A()],I.prototype,"keysRotateLeft",void 0);b([A()],I.prototype,"keysRotateRight",void 0);b([A()],I.prototype,"keysRotateUp",void 0);b([A()],I.prototype,"keysRotateDown",void 0);te.FreeCameraKeyboardMoveInput=I;class se{constructor(e=!0){this.touchEnabled=e,this.buttons=[0,1,2],this.angularSensibility=2e3,this._previousPosition=null,this.onPointerMovedObservable=new P,this._allowCameraRotation=!0,this._currentActiveButton=-1,this._activePointerId=-1}attachControl(e){e=R.BackCompatCameraNoPreventDefault(arguments);const t=this.camera.getEngine(),s=t.getInputElement();this._pointerInput||(this._pointerInput=n=>{const i=n.event,r=i.pointerType==="touch";if(!this.touchEnabled&&r||n.type!==O.POINTERMOVE&&this.buttons.indexOf(i.button)===-1)return;const o=i.target;if(n.type===O.POINTERDOWN){if(r&&this._activePointerId!==-1||!r&&this._currentActiveButton!==-1)return;this._activePointerId=i.pointerId;try{o==null||o.setPointerCapture(i.pointerId)}catch{}this._currentActiveButton===-1&&(this._currentActiveButton=i.button),this._previousPosition={x:i.clientX,y:i.clientY},e||(i.preventDefault(),s&&s.focus()),t.isPointerLock&&this._onMouseMove&&this._onMouseMove(n.event)}else if(n.type===O.POINTERUP){if(r&&this._activePointerId!==i.pointerId||!r&&this._currentActiveButton!==i.button)return;try{o==null||o.releasePointerCapture(i.pointerId)}catch{}this._currentActiveButton=-1,this._previousPosition=null,e||i.preventDefault(),this._activePointerId=-1}else if(n.type===O.POINTERMOVE&&(this._activePointerId===i.pointerId||!r)){if(t.isPointerLock&&this._onMouseMove)this._onMouseMove(n.event);else if(this._previousPosition){const a=this.camera._calculateHandednessMultiplier(),h=(i.clientX-this._previousPosition.x)*a,l=i.clientY-this._previousPosition.y;this._allowCameraRotation&&(this.camera.cameraRotation.y+=h/this.angularSensibility,this.camera.cameraRotation.x+=l/this.angularSensibility),this.onPointerMovedObservable.notifyObservers({offsetX:h,offsetY:l}),this._previousPosition={x:i.clientX,y:i.clientY},e||i.preventDefault()}}}),this._onMouseMove=n=>{if(!t.isPointerLock)return;const i=this.camera._calculateHandednessMultiplier(),r=n.movementX*i;this.camera.cameraRotation.y+=r/this.angularSensibility;const o=n.movementY;this.camera.cameraRotation.x+=o/this.angularSensibility,this._previousPosition=null,e||n.preventDefault()},this._observer=this.camera.getScene()._inputManager._addCameraPointerObserver(this._pointerInput,O.POINTERDOWN|O.POINTERUP|O.POINTERMOVE),s&&(this._contextMenuBind=n=>this.onContextMenu(n),s.addEventListener("contextmenu",this._contextMenuBind,!1))}onContextMenu(e){e.preventDefault()}detachControl(){if(this._observer){if(this.camera.getScene()._inputManager._removeCameraPointerObserver(this._observer),this._contextMenuBind){const t=this.camera.getEngine().getInputElement();t&&t.removeEventListener("contextmenu",this._contextMenuBind)}this.onPointerMovedObservable&&this.onPointerMovedObservable.clear(),this._observer=null,this._onMouseMove=null,this._previousPosition=null}this._activePointerId=-1,this._currentActiveButton=-1}getClassName(){return"FreeCameraMouseInput"}getSimpleName(){return"mouse"}}b([A()],se.prototype,"buttons",void 0);b([A()],se.prototype,"angularSensibility",void 0);te.FreeCameraMouseInput=se;class ne{constructor(){this.wheelPrecisionX=3,this.wheelPrecisionY=3,this.wheelPrecisionZ=3,this.onChangedObservable=new P,this._wheelDeltaX=0,this._wheelDeltaY=0,this._wheelDeltaZ=0,this._ffMultiplier=12,this._normalize=120}attachControl(e){e=R.BackCompatCameraNoPreventDefault(arguments),this._wheel=t=>{if(t.type!==O.POINTERWHEEL)return;const s=t.event,n=s.deltaMode===xe.DOM_DELTA_LINE?this._ffMultiplier:1;this._wheelDeltaX+=this.wheelPrecisionX*n*s.deltaX/this._normalize,this._wheelDeltaY-=this.wheelPrecisionY*n*s.deltaY/this._normalize,this._wheelDeltaZ+=this.wheelPrecisionZ*n*s.deltaZ/this._normalize,s.preventDefault&&(e||s.preventDefault())},this._observer=this.camera.getScene()._inputManager._addCameraPointerObserver(this._wheel,O.POINTERWHEEL)}detachControl(){this._observer&&(this.camera.getScene()._inputManager._removeCameraPointerObserver(this._observer),this._observer=null,this._wheel=null),this.onChangedObservable&&this.onChangedObservable.clear()}checkInputs(){this.onChangedObservable.notifyObservers({wheelDeltaX:this._wheelDeltaX,wheelDeltaY:this._wheelDeltaY,wheelDeltaZ:this._wheelDeltaZ}),this._wheelDeltaX=0,this._wheelDeltaY=0,this._wheelDeltaZ=0}getClassName(){return"BaseCameraMouseWheelInput"}getSimpleName(){return"mousewheel"}}b([A()],ne.prototype,"wheelPrecisionX",void 0);b([A()],ne.prototype,"wheelPrecisionY",void 0);b([A()],ne.prototype,"wheelPrecisionZ",void 0);var y;(function(d){d[d.MoveRelative=0]="MoveRelative",d[d.RotateRelative=1]="RotateRelative",d[d.MoveScene=2]="MoveScene"})(y||(y={}));class L extends ne{constructor(){super(...arguments),this._moveRelative=v.Zero(),this._rotateRelative=v.Zero(),this._moveScene=v.Zero(),this._wheelXAction=y.MoveRelative,this._wheelXActionCoordinate=0,this._wheelYAction=y.MoveRelative,this._wheelYActionCoordinate=2,this._wheelZAction=null,this._wheelZActionCoordinate=null}getClassName(){return"FreeCameraMouseWheelInput"}set wheelXMoveRelative(e){e===null&&this._wheelXAction!==y.MoveRelative||(this._wheelXAction=y.MoveRelative,this._wheelXActionCoordinate=e)}get wheelXMoveRelative(){return this._wheelXAction!==y.MoveRelative?null:this._wheelXActionCoordinate}set wheelYMoveRelative(e){e===null&&this._wheelYAction!==y.MoveRelative||(this._wheelYAction=y.MoveRelative,this._wheelYActionCoordinate=e)}get wheelYMoveRelative(){return this._wheelYAction!==y.MoveRelative?null:this._wheelYActionCoordinate}set wheelZMoveRelative(e){e===null&&this._wheelZAction!==y.MoveRelative||(this._wheelZAction=y.MoveRelative,this._wheelZActionCoordinate=e)}get wheelZMoveRelative(){return this._wheelZAction!==y.MoveRelative?null:this._wheelZActionCoordinate}set wheelXRotateRelative(e){e===null&&this._wheelXAction!==y.RotateRelative||(this._wheelXAction=y.RotateRelative,this._wheelXActionCoordinate=e)}get wheelXRotateRelative(){return this._wheelXAction!==y.RotateRelative?null:this._wheelXActionCoordinate}set wheelYRotateRelative(e){e===null&&this._wheelYAction!==y.RotateRelative||(this._wheelYAction=y.RotateRelative,this._wheelYActionCoordinate=e)}get wheelYRotateRelative(){return this._wheelYAction!==y.RotateRelative?null:this._wheelYActionCoordinate}set wheelZRotateRelative(e){e===null&&this._wheelZAction!==y.RotateRelative||(this._wheelZAction=y.RotateRelative,this._wheelZActionCoordinate=e)}get wheelZRotateRelative(){return this._wheelZAction!==y.RotateRelative?null:this._wheelZActionCoordinate}set wheelXMoveScene(e){e===null&&this._wheelXAction!==y.MoveScene||(this._wheelXAction=y.MoveScene,this._wheelXActionCoordinate=e)}get wheelXMoveScene(){return this._wheelXAction!==y.MoveScene?null:this._wheelXActionCoordinate}set wheelYMoveScene(e){e===null&&this._wheelYAction!==y.MoveScene||(this._wheelYAction=y.MoveScene,this._wheelYActionCoordinate=e)}get wheelYMoveScene(){return this._wheelYAction!==y.MoveScene?null:this._wheelYActionCoordinate}set wheelZMoveScene(e){e===null&&this._wheelZAction!==y.MoveScene||(this._wheelZAction=y.MoveScene,this._wheelZActionCoordinate=e)}get wheelZMoveScene(){return this._wheelZAction!==y.MoveScene?null:this._wheelZActionCoordinate}checkInputs(){if(this._wheelDeltaX===0&&this._wheelDeltaY===0&&this._wheelDeltaZ==0)return;this._moveRelative.setAll(0),this._rotateRelative.setAll(0),this._moveScene.setAll(0),this._updateCamera(),this.camera.getScene().useRightHandedSystem&&(this._moveRelative.z*=-1);const e=B.Zero();this.camera.getViewMatrix().invertToRef(e);const t=v.Zero();v.TransformNormalToRef(this._moveRelative,e,t),this.camera.cameraRotation.x+=this._rotateRelative.x/200,this.camera.cameraRotation.y+=this._rotateRelative.y/200,this.camera.cameraDirection.addInPlace(t),this.camera.cameraDirection.addInPlace(this._moveScene),super.checkInputs()}_updateCamera(){this._updateCameraProperty(this._wheelDeltaX,this._wheelXAction,this._wheelXActionCoordinate),this._updateCameraProperty(this._wheelDeltaY,this._wheelYAction,this._wheelYActionCoordinate),this._updateCameraProperty(this._wheelDeltaZ,this._wheelZAction,this._wheelZActionCoordinate)}_updateCameraProperty(e,t,s){if(e===0||t===null||s===null)return;let n=null;switch(t){case y.MoveRelative:n=this._moveRelative;break;case y.RotateRelative:n=this._rotateRelative;break;case y.MoveScene:n=this._moveScene;break}switch(s){case 0:n.set(e,0,0);break;case 1:n.set(0,e,0);break;case 2:n.set(0,0,e);break}}}b([A()],L.prototype,"wheelXMoveRelative",null);b([A()],L.prototype,"wheelYMoveRelative",null);b([A()],L.prototype,"wheelZMoveRelative",null);b([A()],L.prototype,"wheelXRotateRelative",null);b([A()],L.prototype,"wheelYRotateRelative",null);b([A()],L.prototype,"wheelZRotateRelative",null);b([A()],L.prototype,"wheelXMoveScene",null);b([A()],L.prototype,"wheelYMoveScene",null);b([A()],L.prototype,"wheelZMoveScene",null);te.FreeCameraMouseWheelInput=L;class ie{constructor(e=!1){this.allowMouse=e,this.touchAngularSensibility=2e5,this.touchMoveSensibility=250,this.singleFingerRotate=!1,this._offsetX=null,this._offsetY=null,this._pointerPressed=new Array,this._isSafari=R.IsSafari()}attachControl(e){e=R.BackCompatCameraNoPreventDefault(arguments);let t=null;if(this._pointerInput===void 0&&(this._onLostFocus=()=>{this._offsetX=null,this._offsetY=null},this._pointerInput=s=>{const n=s.event,i=n.pointerType==="mouse"||this._isSafari&&typeof n.pointerType>"u";if(!(!this.allowMouse&&i)){if(s.type===O.POINTERDOWN){if(e||n.preventDefault(),this._pointerPressed.push(n.pointerId),this._pointerPressed.length!==1)return;t={x:n.clientX,y:n.clientY}}else if(s.type===O.POINTERUP){e||n.preventDefault();const r=this._pointerPressed.indexOf(n.pointerId);if(r===-1||(this._pointerPressed.splice(r,1),r!=0))return;t=null,this._offsetX=null,this._offsetY=null}else if(s.type===O.POINTERMOVE){if(e||n.preventDefault(),!t||this._pointerPressed.indexOf(n.pointerId)!=0)return;this._offsetX=n.clientX-t.x,this._offsetY=-(n.clientY-t.y)}}}),this._observer=this.camera.getScene()._inputManager._addCameraPointerObserver(this._pointerInput,O.POINTERDOWN|O.POINTERUP|O.POINTERMOVE),this._onLostFocus){const n=this.camera.getEngine().getInputElement();n&&n.addEventListener("blur",this._onLostFocus)}}detachControl(){if(this._pointerInput){if(this._observer&&(this.camera.getScene()._inputManager._removeCameraPointerObserver(this._observer),this._observer=null),this._onLostFocus){const t=this.camera.getEngine().getInputElement();t&&t.removeEventListener("blur",this._onLostFocus),this._onLostFocus=null}this._pointerPressed.length=0,this._offsetX=null,this._offsetY=null}}checkInputs(){if(this._offsetX===null||this._offsetY===null||this._offsetX===0&&this._offsetY===0)return;const e=this.camera,t=e._calculateHandednessMultiplier();if(e.cameraRotation.y=t*this._offsetX/this.touchAngularSensibility,this.singleFingerRotate&&this._pointerPressed.length===1||!this.singleFingerRotate&&this._pointerPressed.length>1)e.cameraRotation.x=-this._offsetY/this.touchAngularSensibility;else{const n=e._computeLocalCameraSpeed(),i=new v(0,0,this.touchMoveSensibility!==0?n*this._offsetY/this.touchMoveSensibility:0);B.RotationYawPitchRollToRef(e.rotation.y,e.rotation.x,0,e._cameraRotationMatrix),e.cameraDirection.addInPlace(v.TransformCoordinates(i,e._cameraRotationMatrix))}}getClassName(){return"FreeCameraTouchInput"}getSimpleName(){return"touch"}}b([A()],ie.prototype,"touchAngularSensibility",void 0);b([A()],ie.prototype,"touchMoveSensibility",void 0);te.FreeCameraTouchInput=ie;class He extends Me{constructor(e){super(e),this._mouseInput=null,this._mouseWheelInput=null}addKeyboard(){return this.add(new I),this}addMouse(e=!0){return this._mouseInput||(this._mouseInput=new se(e),this.add(this._mouseInput)),this}removeMouse(){return this._mouseInput&&this.remove(this._mouseInput),this}addMouseWheel(){return this._mouseWheelInput||(this._mouseWheelInput=new L,this.add(this._mouseWheelInput)),this}removeMouseWheel(){return this._mouseWheelInput&&this.remove(this._mouseWheelInput),this}addTouch(){return this.add(new ie),this}clear(){super.clear(),this._mouseInput=null}}class X extends we{get angularSensibility(){const e=this.inputs.attached.mouse;return e?e.angularSensibility:0}set angularSensibility(e){const t=this.inputs.attached.mouse;t&&(t.angularSensibility=e)}get keysUp(){const e=this.inputs.attached.keyboard;return e?e.keysUp:[]}set keysUp(e){const t=this.inputs.attached.keyboard;t&&(t.keysUp=e)}get keysUpward(){const e=this.inputs.attached.keyboard;return e?e.keysUpward:[]}set keysUpward(e){const t=this.inputs.attached.keyboard;t&&(t.keysUpward=e)}get keysDown(){const e=this.inputs.attached.keyboard;return e?e.keysDown:[]}set keysDown(e){const t=this.inputs.attached.keyboard;t&&(t.keysDown=e)}get keysDownward(){const e=this.inputs.attached.keyboard;return e?e.keysDownward:[]}set keysDownward(e){const t=this.inputs.attached.keyboard;t&&(t.keysDownward=e)}get keysLeft(){const e=this.inputs.attached.keyboard;return e?e.keysLeft:[]}set keysLeft(e){const t=this.inputs.attached.keyboard;t&&(t.keysLeft=e)}get keysRight(){const e=this.inputs.attached.keyboard;return e?e.keysRight:[]}set keysRight(e){const t=this.inputs.attached.keyboard;t&&(t.keysRight=e)}get keysRotateLeft(){const e=this.inputs.attached.keyboard;return e?e.keysRotateLeft:[]}set keysRotateLeft(e){const t=this.inputs.attached.keyboard;t&&(t.keysRotateLeft=e)}get keysRotateRight(){const e=this.inputs.attached.keyboard;return e?e.keysRotateRight:[]}set keysRotateRight(e){const t=this.inputs.attached.keyboard;t&&(t.keysRotateRight=e)}get keysRotateUp(){const e=this.inputs.attached.keyboard;return e?e.keysRotateUp:[]}set keysRotateUp(e){const t=this.inputs.attached.keyboard;t&&(t.keysRotateUp=e)}get keysRotateDown(){const e=this.inputs.attached.keyboard;return e?e.keysRotateDown:[]}set keysRotateDown(e){const t=this.inputs.attached.keyboard;t&&(t.keysRotateDown=e)}constructor(e,t,s,n=!0){super(e,t,s,n),this.ellipsoid=new v(.5,1,.5),this.ellipsoidOffset=new v(0,0,0),this.checkCollisions=!1,this.applyGravity=!1,this._needMoveForGravity=!1,this._oldPosition=v.Zero(),this._diffPosition=v.Zero(),this._newPosition=v.Zero(),this._collisionMask=-1,this._onCollisionPositionChange=(i,r,o=null)=>{this._newPosition.copyFrom(r),this._newPosition.subtractToRef(this._oldPosition,this._diffPosition),this._diffPosition.length()>Te.CollisionsEpsilon&&(this.position.addToRef(this._diffPosition,this._deferredPositionUpdate),this._deferOnly?this._deferredUpdated=!0:this.position.copyFrom(this._deferredPositionUpdate),this.onCollide&&o&&this.onCollide(o))},this.inputs=new He(this),this.inputs.addKeyboard().addMouse()}attachControl(e,t){t=R.BackCompatCameraNoPreventDefault(arguments),this.inputs.attachElement(t)}detachControl(){this.inputs.detachElement(),this.cameraDirection=new v(0,0,0),this.cameraRotation=new Pe(0,0)}get collisionMask(){return this._collisionMask}set collisionMask(e){this._collisionMask=isNaN(e)?-1:e}_collideWithWorld(e){let t;this.parent?t=v.TransformCoordinates(this.position,this.parent.getWorldMatrix()):t=this.position,t.subtractFromFloatsToRef(0,this.ellipsoid.y,0,this._oldPosition),this._oldPosition.addInPlace(this.ellipsoidOffset);const s=this.getScene().collisionCoordinator;this._collider||(this._collider=s.createCollider()),this._collider._radius=this.ellipsoid,this._collider.collisionMask=this._collisionMask;let n=e;this.applyGravity&&(n=e.add(this.getScene().gravity)),s.getNewPosition(this._oldPosition,n,this._collider,3,null,this._onCollisionPositionChange,this.uniqueId)}_checkInputs(){this._localDirection||(this._localDirection=v.Zero(),this._transformedDirection=v.Zero()),this.inputs.checkInputs(),super._checkInputs()}set needMoveForGravity(e){this._needMoveForGravity=e}get needMoveForGravity(){return this._needMoveForGravity}_decideIfNeedsToMove(){return this._needMoveForGravity||Math.abs(this.cameraDirection.x)>0||Math.abs(this.cameraDirection.y)>0||Math.abs(this.cameraDirection.z)>0}_updatePosition(){this.checkCollisions&&this.getScene().collisionsEnabled?this._collideWithWorld(this.cameraDirection):super._updatePosition()}dispose(){this.inputs.clear(),super.dispose()}getClassName(){return"FreeCamera"}}b([ge()],X.prototype,"ellipsoid",void 0);b([ge()],X.prototype,"ellipsoidOffset",void 0);b([A()],X.prototype,"checkCollisions",void 0);b([A()],X.prototype,"applyGravity",void 0);Ce("BABYLON.FreeCamera",X);class ee{get useTextureToStoreBoneMatrices(){return this._useTextureToStoreBoneMatrices}set useTextureToStoreBoneMatrices(e){this._useTextureToStoreBoneMatrices=e,this._markAsDirty()}get animationPropertiesOverride(){return this._animationPropertiesOverride?this._animationPropertiesOverride:this._scene.animationPropertiesOverride}set animationPropertiesOverride(e){this._animationPropertiesOverride=e}get isUsingTextureForMatrices(){return this.useTextureToStoreBoneMatrices&&this._canUseTextureForBones}get uniqueId(){return this._uniqueId}constructor(e,t,s){this.name=e,this.id=t,this.bones=[],this.needInitialSkinMatrix=!1,this._isDirty=!0,this._meshesWithPoseMatrix=new Array,this._identity=B.Identity(),this._currentRenderId=-1,this._ranges={},this._absoluteTransformIsDirty=!0,this._canUseTextureForBones=!1,this._uniqueId=0,this._numBonesWithLinkedTransformNode=0,this._hasWaitingData=null,this._parentContainer=null,this.doNotSerialize=!1,this._useTextureToStoreBoneMatrices=!0,this._animationPropertiesOverride=null,this.onBeforeComputeObservable=new P,this.bones=[],this._scene=s||ae.LastCreatedScene,this._uniqueId=this._scene.getUniqueId(),this._scene.addSkeleton(this),this._isDirty=!0;const n=this._scene.getEngine().getCaps();this._canUseTextureForBones=n.textureFloat&&n.maxVertexTextureImageUnits>0}getClassName(){return"Skeleton"}getChildren(){return this.bones.filter(e=>!e.getParent())}getTransformMatrices(e){if(this.needInitialSkinMatrix){if(!e)throw new Error("getTransformMatrices: When using the needInitialSkinMatrix flag, a mesh must be provided");return e._bonesTransformMatrices||this.prepare(!0),e._bonesTransformMatrices}return(!this._transformMatrices||this._isDirty)&&this.prepare(!this._transformMatrices),this._transformMatrices}getTransformMatrixTexture(e){return this.needInitialSkinMatrix&&e._transformMatrixTexture?e._transformMatrixTexture:this._transformMatrixTexture}getScene(){return this._scene}toString(e){let t=`Name: ${this.name}, nBones: ${this.bones.length}`;if(t+=`, nAnimationRanges: ${this._ranges?Object.keys(this._ranges).length:"none"}`,e){t+=", Ranges: {";let s=!0;for(const n in this._ranges)s&&(t+=", ",s=!1),t+=n;t+="}"}return t}getBoneIndexByName(e){for(let t=0,s=this.bones.length;t<s;t++)if(this.bones[t].name===e)return t;return-1}createAnimationRange(e,t,s){if(!this._ranges[e]){this._ranges[e]=new ue(e,t,s);for(let n=0,i=this.bones.length;n<i;n++)this.bones[n].animations[0]&&this.bones[n].animations[0].createRange(e,t,s)}}deleteAnimationRange(e,t=!0){for(let s=0,n=this.bones.length;s<n;s++)this.bones[s].animations[0]&&this.bones[s].animations[0].deleteRange(e,t);this._ranges[e]=null}getAnimationRange(e){return this._ranges[e]||null}getAnimationRanges(){const e=[];let t;for(t in this._ranges)e.push(this._ranges[t]);return e}copyAnimationRange(e,t,s=!1){if(this._ranges[t]||!e.getAnimationRange(t))return!1;let n=!0;const i=this._getHighestAnimationFrame()+1,r={},o=e.bones;let a,h;for(h=0,a=o.length;h<a;h++)r[o[h].name]=o[h];this.bones.length!==o.length&&(T.Warn(`copyAnimationRange: this rig has ${this.bones.length} bones, while source as ${o.length}`),n=!1);const l=s&&this.dimensionsAtRest&&e.dimensionsAtRest?this.dimensionsAtRest.divide(e.dimensionsAtRest):null;for(h=0,a=this.bones.length;h<a;h++){const c=this.bones[h].name,p=r[c];p?n=n&&this.bones[h].copyAnimationRange(p,t,i,s,l):(T.Warn("copyAnimationRange: not same rig, missing source bone "+c),n=!1)}const u=e.getAnimationRange(t);return u&&(this._ranges[t]=new ue(t,u.from+i,u.to+i)),n}returnToRest(){for(const e of this.bones)e._index!==-1&&e.returnToRest()}_getHighestAnimationFrame(){let e=0;for(let t=0,s=this.bones.length;t<s;t++)if(this.bones[t].animations[0]){const n=this.bones[t].animations[0].getHighestFrame();e<n&&(e=n)}return e}beginAnimation(e,t,s,n){const i=this.getAnimationRange(e);return i?this._scene.beginAnimation(this,i.from,i.to,t,s,n):null}static MakeAnimationAdditive(e,t=0,s){const n=e.getAnimationRange(s);if(!n)return null;const i=e._scene.getAllAnimatablesByTarget(e);let r=null;for(let a=0;a<i.length;a++){const h=i[a];if(h.fromFrame===(n==null?void 0:n.from)&&h.toFrame===(n==null?void 0:n.to)){r=h;break}}const o=e.getAnimatables();for(let a=0;a<o.length;a++){const l=o[a].animations;if(l)for(let u=0;u<l.length;u++)ce.MakeAnimationAdditive(l[u],t,s)}return r&&(r.isAdditive=!0),e}_markAsDirty(){this._isDirty=!0,this._absoluteTransformIsDirty=!0}_registerMeshWithPoseMatrix(e){this._meshesWithPoseMatrix.push(e)}_unregisterMeshWithPoseMatrix(e){const t=this._meshesWithPoseMatrix.indexOf(e);t>-1&&this._meshesWithPoseMatrix.splice(t,1)}_computeTransformMatrices(e,t){this.onBeforeComputeObservable.notifyObservers(this);for(let s=0;s<this.bones.length;s++){const n=this.bones[s];n._childUpdateId++;const i=n.getParent();if(i?n.getLocalMatrix().multiplyToRef(i.getFinalMatrix(),n.getFinalMatrix()):t?n.getLocalMatrix().multiplyToRef(t,n.getFinalMatrix()):n.getFinalMatrix().copyFrom(n.getLocalMatrix()),n._index!==-1){const r=n._index===null?s:n._index;n.getAbsoluteInverseBindMatrix().multiplyToArray(n.getFinalMatrix(),e,r*16)}}this._identity.copyToArray(e,this.bones.length*16)}prepare(e=!1){if(!e){const t=this.getScene().getRenderId();if(this._currentRenderId===t)return;this._currentRenderId=t}if(this._numBonesWithLinkedTransformNode>0){for(const t of this.bones)if(t._linkedTransformNode){const s=t._linkedTransformNode;t.position=s.position,s.rotationQuaternion?t.rotationQuaternion=s.rotationQuaternion:t.rotation=s.rotation,t.scaling=s.scaling}}if(this.needInitialSkinMatrix)for(const t of this._meshesWithPoseMatrix){const s=t.getPoseMatrix();let n=this._isDirty;if((!t._bonesTransformMatrices||t._bonesTransformMatrices.length!==16*(this.bones.length+1))&&(t._bonesTransformMatrices=new Float32Array(16*(this.bones.length+1)),n=!0),!!n){if(this._synchronizedWithMesh!==t){this._synchronizedWithMesh=t;for(const i of this.bones)i.getParent()||(i.getBindMatrix().multiplyToRef(s,j.Matrix[1]),i._updateAbsoluteBindMatrices(j.Matrix[1]));if(this.isUsingTextureForMatrices){const i=(this.bones.length+1)*4;(!t._transformMatrixTexture||t._transformMatrixTexture.getSize().width!==i)&&(t._transformMatrixTexture&&t._transformMatrixTexture.dispose(),t._transformMatrixTexture=pe.CreateRGBATexture(t._bonesTransformMatrices,(this.bones.length+1)*4,1,this._scene,!1,!1,1,1))}}this._computeTransformMatrices(t._bonesTransformMatrices,s),this.isUsingTextureForMatrices&&t._transformMatrixTexture&&t._transformMatrixTexture.update(t._bonesTransformMatrices)}}else{if(!this._isDirty)return;(!this._transformMatrices||this._transformMatrices.length!==16*(this.bones.length+1))&&(this._transformMatrices=new Float32Array(16*(this.bones.length+1)),this.isUsingTextureForMatrices&&(this._transformMatrixTexture&&this._transformMatrixTexture.dispose(),this._transformMatrixTexture=pe.CreateRGBATexture(this._transformMatrices,(this.bones.length+1)*4,1,this._scene,!1,!1,1,1))),this._computeTransformMatrices(this._transformMatrices,null),this.isUsingTextureForMatrices&&this._transformMatrixTexture&&this._transformMatrixTexture.update(this._transformMatrices)}this._isDirty=!1}getAnimatables(){if(!this._animatables||this._animatables.length!==this.bones.length){this._animatables=[];for(let e=0;e<this.bones.length;e++)this._animatables.push(this.bones[e])}return this._animatables}clone(e,t){const s=new ee(e,t||e,this._scene);s.needInitialSkinMatrix=this.needInitialSkinMatrix;for(let n=0;n<this.bones.length;n++){const i=this.bones[n];let r=null;const o=i.getParent();if(o){const h=this.bones.indexOf(o);r=s.bones[h]}const a=new re(i.name,s,r,i.getBindMatrix().clone(),i.getRestMatrix().clone());a._index=i._index,i._linkedTransformNode&&a.linkTransformNode(i._linkedTransformNode),Se.DeepCopy(i.animations,a.animations)}if(this._ranges){s._ranges={};for(const n in this._ranges){const i=this._ranges[n];i&&(s._ranges[n]=i.clone())}}return this._isDirty=!0,s.prepare(!0),s}enableBlending(e=.01){this.bones.forEach(t=>{t.animations.forEach(s=>{s.enableBlending=!0,s.blendingSpeed=e})})}dispose(){if(this._meshesWithPoseMatrix.length=0,this.getScene().stopAnimation(this),this.getScene().removeSkeleton(this),this._parentContainer){const e=this._parentContainer.skeletons.indexOf(this);e>-1&&this._parentContainer.skeletons.splice(e,1),this._parentContainer=null}this._transformMatrixTexture&&(this._transformMatrixTexture.dispose(),this._transformMatrixTexture=null)}serialize(){var t;const e={};e.name=this.name,e.id=this.id,this.dimensionsAtRest&&(e.dimensionsAtRest=this.dimensionsAtRest.asArray()),e.bones=[],e.needInitialSkinMatrix=this.needInitialSkinMatrix;for(let s=0;s<this.bones.length;s++){const n=this.bones[s],i=n.getParent(),r={parentBoneIndex:i?this.bones.indexOf(i):-1,index:n.getIndex(),name:n.name,id:n.id,matrix:n.getBindMatrix().asArray(),rest:n.getRestMatrix().asArray(),linkedTransformNodeId:(t=n.getTransformNode())==null?void 0:t.id};e.bones.push(r),n.length&&(r.length=n.length),n.metadata&&(r.metadata=n.metadata),n.animations&&n.animations.length>0&&(r.animation=n.animations[0].serialize()),e.ranges=[];for(const o in this._ranges){const a=this._ranges[o];if(!a)continue;const h={};h.name=o,h.from=a.from,h.to=a.to,e.ranges.push(h)}}return e}static Parse(e,t){const s=new ee(e.name,e.id,t);e.dimensionsAtRest&&(s.dimensionsAtRest=v.FromArray(e.dimensionsAtRest)),s.needInitialSkinMatrix=e.needInitialSkinMatrix;let n;for(n=0;n<e.bones.length;n++){const i=e.bones[n],r=e.bones[n].index;let o=null;i.parentBoneIndex>-1&&(o=s.bones[i.parentBoneIndex]);const a=i.rest?B.FromArray(i.rest):null,h=new re(i.name,s,o,B.FromArray(i.matrix),a,null,r);i.id!==void 0&&i.id!==null&&(h.id=i.id),i.length&&(h.length=i.length),i.metadata&&(h.metadata=i.metadata),i.animation&&h.animations.push(ce.Parse(i.animation)),i.linkedTransformNodeId!==void 0&&i.linkedTransformNodeId!==null&&(s._hasWaitingData=!0,h._waitingTransformNodeId=i.linkedTransformNodeId)}if(e.ranges)for(n=0;n<e.ranges.length;n++){const i=e.ranges[n];s.createAnimationRange(i.name,i.from,i.to)}return s}computeAbsoluteMatrices(e=!1){(this._absoluteTransformIsDirty||e)&&(this.bones[0].computeAbsoluteMatrices(),this._absoluteTransformIsDirty=!1)}computeAbsoluteTransforms(e=!1){this.computeAbsoluteMatrices(e)}getPoseMatrix(){let e=null;return this._meshesWithPoseMatrix.length>0&&(e=this._meshesWithPoseMatrix[0].getPoseMatrix()),e}sortBones(){const e=[],t=new Array(this.bones.length);for(let s=0;s<this.bones.length;s++)this._sortBones(s,e,t);this.bones=e}_sortBones(e,t,s){if(s[e])return;s[e]=!0;const n=this.bones[e];if(!n)return;n._index===void 0&&(n._index=e);const i=n.getParent();i&&this._sortBones(this.bones.indexOf(i),t,s),t.push(n)}setCurrentPoseAsRest(){this.bones.forEach(e=>{e.setCurrentPoseAsRest()})}}class G{get influence(){return this._influence}set influence(e){if(this._influence===e)return;const t=this._influence;this._influence=e,this.onInfluenceChanged.hasObservers()&&this.onInfluenceChanged.notifyObservers(t===0||e===0)}get animationPropertiesOverride(){return!this._animationPropertiesOverride&&this._scene?this._scene.animationPropertiesOverride:this._animationPropertiesOverride}set animationPropertiesOverride(e){this._animationPropertiesOverride=e}constructor(e,t=0,s=null){this.name=e,this.animations=[],this._positions=null,this._normals=null,this._tangents=null,this._uvs=null,this._uv2s=null,this._colors=null,this._uniqueId=0,this.onInfluenceChanged=new P,this._onDataLayoutChanged=new P,this._animationPropertiesOverride=null,this.id=e,this._scene=s||ae.LastCreatedScene,this.influence=t,this._scene&&(this._uniqueId=this._scene.getUniqueId())}get uniqueId(){return this._uniqueId}get hasPositions(){return!!this._positions}get hasNormals(){return!!this._normals}get hasTangents(){return!!this._tangents}get hasUVs(){return!!this._uvs}get hasUV2s(){return!!this._uv2s}get hasColors(){return!!this._colors}get vertexCount(){return this._positions?this._positions.length/3:this._normals?this._normals.length/3:this._tangents?this._tangents.length/3:this._uvs?this._uvs.length/2:this._uv2s?this._uv2s.length/2:this._colors?this._colors.length/4:0}setPositions(e){const t=this.hasPositions;this._positions=e,t!==this.hasPositions&&this._onDataLayoutChanged.notifyObservers(void 0)}getPositions(){return this._positions}setNormals(e){const t=this.hasNormals;this._normals=e,t!==this.hasNormals&&this._onDataLayoutChanged.notifyObservers(void 0)}getNormals(){return this._normals}setTangents(e){const t=this.hasTangents;this._tangents=e,t!==this.hasTangents&&this._onDataLayoutChanged.notifyObservers(void 0)}getTangents(){return this._tangents}setUVs(e){const t=this.hasUVs;this._uvs=e,t!==this.hasUVs&&this._onDataLayoutChanged.notifyObservers(void 0)}getUVs(){return this._uvs}setUV2s(e){const t=this.hasUV2s;this._uv2s=e,t!==this.hasUV2s&&this._onDataLayoutChanged.notifyObservers(void 0)}getUV2s(){return this._uv2s}setColors(e){const t=this.hasColors;this._colors=e,t!==this.hasColors&&this._onDataLayoutChanged.notifyObservers(void 0)}getColors(){return this._colors}clone(){const e=de.Clone(()=>new G(this.name,this.influence,this._scene),this);return e._positions=this._positions,e._normals=this._normals,e._tangents=this._tangents,e._uvs=this._uvs,e._uv2s=this._uv2s,e._colors=this._colors,e}serialize(){const e={};return e.name=this.name,e.influence=this.influence,e.positions=Array.prototype.slice.call(this.getPositions()),this.id!=null&&(e.id=this.id),this.hasNormals&&(e.normals=Array.prototype.slice.call(this.getNormals())),this.hasTangents&&(e.tangents=Array.prototype.slice.call(this.getTangents())),this.hasUVs&&(e.uvs=Array.prototype.slice.call(this.getUVs())),this.hasUV2s&&(e.uv2s=Array.prototype.slice.call(this.getUV2s())),this.hasColors&&(e.colors=Array.prototype.slice.call(this.getColors())),de.AppendSerializedAnimations(this,e),e}getClassName(){return"MorphTarget"}static Parse(e,t){const s=new G(e.name,e.influence);if(s.setPositions(e.positions),e.id!=null&&(s.id=e.id),e.normals&&s.setNormals(e.normals),e.tangents&&s.setTangents(e.tangents),e.uvs&&s.setUVs(e.uvs),e.uv2s&&s.setUV2s(e.uv2s),e.colors&&s.setColors(e.colors),e.animations){for(let n=0;n<e.animations.length;n++){const i=e.animations[n],r=Oe("BABYLON.Animation");r&&s.animations.push(r.Parse(i))}e.autoAnimate&&t&&t.beginAnimation(s,e.autoAnimateFrom,e.autoAnimateTo,e.autoAnimateLoop,e.autoAnimateSpeed||1)}return s}static FromMesh(e,t,s){t||(t=e.name);const n=new G(t,s,e.getScene());return n.setPositions(e.getVerticesData(g.PositionKind)),e.isVerticesDataPresent(g.NormalKind)&&n.setNormals(e.getVerticesData(g.NormalKind)),e.isVerticesDataPresent(g.TangentKind)&&n.setTangents(e.getVerticesData(g.TangentKind)),e.isVerticesDataPresent(g.UVKind)&&n.setUVs(e.getVerticesData(g.UVKind)),e.isVerticesDataPresent(g.UV2Kind)&&n.setUV2s(e.getVerticesData(g.UV2Kind)),e.isVerticesDataPresent(g.ColorKind)&&n.setColors(e.getVerticesData(g.ColorKind)),n}}b([A()],G.prototype,"id",void 0);class he extends C{get depth(){return this._depth}constructor(e,t,s,n,i,r,o=!0,a=!1,h=C.TRILINEAR_SAMPLINGMODE,l=0,u){super(null,r,!o,a),this.format=i,this._texture=r.getEngine().createRawTexture2DArray(e,t,s,n,i,o,a,h,null,l,u),this._depth=n,this.is2DArray=!0}update(e){this._texture&&this._getEngine().updateRawTexture2DArray(this._texture,e,this._texture.format,this._texture.invertY,null,this._texture.type)}static CreateRGBATexture(e,t,s,n,i,r=!0,o=!1,a=3,h=0){return new he(e,t,s,n,5,i,r,o,a,h)}}class U{set areUpdatesFrozen(e){e?this._blockCounter++:(this._blockCounter--,this._blockCounter<=0&&(this._blockCounter=0,this._syncActiveTargets(this._forceUpdateWhenUnfrozen),this._forceUpdateWhenUnfrozen=!1))}get areUpdatesFrozen(){return this._blockCounter>0}constructor(e=null){if(this._targets=new Array,this._targetInfluenceChangedObservers=new Array,this._targetDataLayoutChangedObservers=new Array,this._activeTargets=new Re(16),this._supportsPositions=!1,this._supportsNormals=!1,this._supportsTangents=!1,this._supportsUVs=!1,this._supportsUV2s=!1,this._supportsColors=!1,this._vertexCount=0,this._uniqueId=0,this._tempInfluences=new Array,this._canUseTextureForTargets=!1,this._blockCounter=0,this._mustSynchronize=!0,this._forceUpdateWhenUnfrozen=!1,this._textureVertexStride=0,this._textureWidth=0,this._textureHeight=1,this._parentContainer=null,this.optimizeInfluencers=!0,this.enablePositionMorphing=!0,this.enableNormalMorphing=!0,this.enableTangentMorphing=!0,this.enableUVMorphing=!0,this.enableUV2Morphing=!0,this.enableColorMorphing=!0,this._numMaxInfluencers=0,this._useTextureToStoreTargets=!0,e||(e=ae.LastCreatedScene),this._scene=e,this._scene){this._scene.addMorphTargetManager(this),this._uniqueId=this._scene.getUniqueId();const t=this._scene.getEngine().getCaps();this._canUseTextureForTargets=t.canUseGLVertexID&&t.textureFloat&&t.maxVertexTextureImageUnits>0&&t.texture2DArrayMaxLayerCount>1}}get numMaxInfluencers(){return this._numMaxInfluencers}set numMaxInfluencers(e){this._numMaxInfluencers!==e&&(this._numMaxInfluencers=e,this._mustSynchronize=!0,this._syncActiveTargets())}get uniqueId(){return this._uniqueId}get vertexCount(){return this._vertexCount}get supportsPositions(){return this._supportsPositions&&this.enablePositionMorphing}get supportsNormals(){return this._supportsNormals&&this.enableNormalMorphing}get supportsTangents(){return this._supportsTangents&&this.enableTangentMorphing}get supportsUVs(){return this._supportsUVs&&this.enableUVMorphing}get supportsUV2s(){return this._supportsUV2s&&this.enableUV2Morphing}get supportsColors(){return this._supportsColors&&this.enableColorMorphing}get hasPositions(){return this._supportsPositions}get hasNormals(){return this._supportsNormals}get hasTangents(){return this._supportsTangents}get hasUVs(){return this._supportsUVs}get hasUV2s(){return this._supportsUV2s}get hasColors(){return this._supportsColors}get numTargets(){return this._targets.length}get numInfluencers(){return this._activeTargets.length}get influences(){return this._influences}get useTextureToStoreTargets(){return this._useTextureToStoreTargets}set useTextureToStoreTargets(e){this._useTextureToStoreTargets!==e&&(this._useTextureToStoreTargets=e,this._mustSynchronize=!0,this._syncActiveTargets())}get isUsingTextureForTargets(){var e;return U.EnableTextureStorage&&this.useTextureToStoreTargets&&this._canUseTextureForTargets&&!((e=this._scene)!=null&&e.getEngine().getCaps().disableMorphTargetTexture)}getActiveTarget(e){return this._activeTargets.data[e]}getTarget(e){return this._targets[e]}getTargetByName(e){for(const t of this._targets)if(t.name===e)return t;return null}addTarget(e){this._targets.push(e),this._targetInfluenceChangedObservers.push(e.onInfluenceChanged.add(t=>{this.areUpdatesFrozen&&t&&(this._forceUpdateWhenUnfrozen=!0),this._syncActiveTargets(t)})),this._targetDataLayoutChangedObservers.push(e._onDataLayoutChanged.add(()=>{this._mustSynchronize=!0,this._syncActiveTargets()})),this._mustSynchronize=!0,this._syncActiveTargets()}removeTarget(e){const t=this._targets.indexOf(e);t>=0&&(this._targets.splice(t,1),e.onInfluenceChanged.remove(this._targetInfluenceChangedObservers.splice(t,1)[0]),e._onDataLayoutChanged.remove(this._targetDataLayoutChangedObservers.splice(t,1)[0]),this._mustSynchronize=!0,this._syncActiveTargets()),this._scene&&this._scene.stopAnimation(e)}_bind(e){e.setFloat3("morphTargetTextureInfo",this._textureVertexStride,this._textureWidth,this._textureHeight),e.setFloatArray("morphTargetTextureIndices",this._morphTargetTextureIndices),e.setTexture("morphTargets",this._targetStoreTexture),e.setInt("morphTargetCount",this.numInfluencers)}clone(){const e=new U(this._scene);for(const t of this._targets)e.addTarget(t.clone());return e.enablePositionMorphing=this.enablePositionMorphing,e.enableNormalMorphing=this.enableNormalMorphing,e.enableTangentMorphing=this.enableTangentMorphing,e.enableUVMorphing=this.enableUVMorphing,e.enableUV2Morphing=this.enableUV2Morphing,e.enableColorMorphing=this.enableColorMorphing,e}serialize(){const e={};e.id=this.uniqueId,e.targets=[];for(const t of this._targets)e.targets.push(t.serialize());return e}_syncActiveTargets(e=!1){if(this.areUpdatesFrozen)return;const t=!!this._targetStoreTexture,s=this.isUsingTextureForTargets;(this._mustSynchronize||t!==s)&&(this._mustSynchronize=!1,this.synchronize());let n=0;this._activeTargets.reset(),(!this._morphTargetTextureIndices||this._morphTargetTextureIndices.length!==this._targets.length)&&(this._morphTargetTextureIndices=new Float32Array(this._targets.length));let i=-1;for(const r of this._targets)if(i++,!(r.influence===0&&this.optimizeInfluencers)){if(this._activeTargets.length>=U.MaxActiveMorphTargetsInVertexAttributeMode&&!this.isUsingTextureForTargets)break;this._activeTargets.push(r),this._morphTargetTextureIndices[n]=i,this._tempInfluences[n++]=r.influence}this._morphTargetTextureIndices.length!==n&&(this._morphTargetTextureIndices=this._morphTargetTextureIndices.slice(0,n)),(!this._influences||this._influences.length!==n)&&(this._influences=new Float32Array(n));for(let r=0;r<n;r++)this._influences[r]=this._tempInfluences[r];if(e&&this._scene)for(const r of this._scene.meshes)r.morphTargetManager===this&&(s?r._markSubMeshesAsAttributesDirty():r._syncGeometryWithMorphTargetManager())}synchronize(){var t;if(!this._scene||this.areUpdatesFrozen)return;const e=this._scene.getEngine();this._supportsPositions=!0,this._supportsNormals=!0,this._supportsTangents=!0,this._supportsUVs=!0,this._supportsUV2s=!0,this._supportsColors=!0,this._vertexCount=0,(t=this._targetStoreTexture)==null||t.dispose(),this._targetStoreTexture=null,this.isUsingTextureForTargets&&this._targets.length>e.getCaps().texture2DArrayMaxLayerCount&&(this.useTextureToStoreTargets=!1);for(const s of this._targets){this._supportsPositions=this._supportsPositions&&s.hasPositions,this._supportsNormals=this._supportsNormals&&s.hasNormals,this._supportsTangents=this._supportsTangents&&s.hasTangents,this._supportsUVs=this._supportsUVs&&s.hasUVs,this._supportsUV2s=this._supportsUV2s&&s.hasUV2s,this._supportsColors=this._supportsColors&&s.hasColors;const n=s.vertexCount;if(this._vertexCount===0)this._vertexCount=n;else if(this._vertexCount!==n){T.Error(`Incompatible target. Targets must all have the same vertices count. Current vertex count: ${this._vertexCount}, vertex count for target "${s.name}": ${n}`);return}}if(this.isUsingTextureForTargets){this._textureVertexStride=0,this._supportsPositions&&this._textureVertexStride++,this._supportsNormals&&this._textureVertexStride++,this._supportsTangents&&this._textureVertexStride++,this._supportsUVs&&this._textureVertexStride++,this._supportsUV2s&&this._textureVertexStride++,this._supportsColors&&this._textureVertexStride++,this._textureWidth=this._vertexCount*this._textureVertexStride||1,this._textureHeight=1;const s=e.getCaps().maxTextureSize;this._textureWidth>s&&(this._textureHeight=Math.ceil(this._textureWidth/s),this._textureWidth=s);const n=this._targets.length,i=new Float32Array(n*this._textureWidth*this._textureHeight*4);let r=0;for(let o=0;o<n;o++){const a=this._targets[o],h=a.getPositions(),l=a.getNormals(),u=a.getUVs(),c=a.getTangents(),p=a.getUV2s(),f=a.getColors();r=o*this._textureWidth*this._textureHeight*4;for(let m=0;m<this._vertexCount;m++)this._supportsPositions&&h&&(i[r]=h[m*3],i[r+1]=h[m*3+1],i[r+2]=h[m*3+2],r+=4),this._supportsNormals&&l&&(i[r]=l[m*3],i[r+1]=l[m*3+1],i[r+2]=l[m*3+2],r+=4),this._supportsUVs&&u&&(i[r]=u[m*2],i[r+1]=u[m*2+1],r+=4),this._supportsTangents&&c&&(i[r]=c[m*3],i[r+1]=c[m*3+1],i[r+2]=c[m*3+2],r+=4),this._supportsUV2s&&p&&(i[r]=p[m*2],i[r+1]=p[m*2+1],r+=4),this._supportsColors&&f&&(i[r]=f[m*4],i[r+1]=f[m*4+1],i[r+2]=f[m*4+2],i[r+3]=f[m*4+3],r+=4)}this._targetStoreTexture=he.CreateRGBATexture(i,this._textureWidth,this._textureHeight,n,this._scene,!1,!1,1,1),this._targetStoreTexture.name=`Morph texture_${this.uniqueId}`}for(const s of this._scene.meshes)s.morphTargetManager===this&&s._syncGeometryWithMorphTargetManager()}dispose(){if(this._targetStoreTexture&&this._targetStoreTexture.dispose(),this._targetStoreTexture=null,this._scene){if(this._scene.removeMorphTargetManager(this),this._parentContainer){const e=this._parentContainer.morphTargetManagers.indexOf(this);e>-1&&this._parentContainer.morphTargetManagers.splice(e,1),this._parentContainer=null}for(const e of this._targets)this._scene.stopAnimation(e)}}static Parse(e,t){const s=new U(t);for(const n of e.targets)s.addTarget(G.Parse(n,t));return s}}U.EnableTextureStorage=!0;U.MaxActiveMorphTargetsInVertexAttributeMode=8;class Q{constructor(e){this.byteOffset=0,this.buffer=e}loadAsync(e){return this.buffer.readAsync(this.byteOffset,e).then(t=>{this._dataView=new DataView(t.buffer,t.byteOffset,t.byteLength),this._dataByteOffset=0})}readUint32(){const e=this._dataView.getUint32(this._dataByteOffset,!0);return this._dataByteOffset+=4,this.byteOffset+=4,e}readUint8Array(e){const t=new Uint8Array(this._dataView.buffer,this._dataView.byteOffset+this._dataByteOffset,e);return this._dataByteOffset+=e,this.byteOffset+=e,t}readString(e){return ke(this.readUint8Array(e))}skipBytes(e){this._dataByteOffset+=e,this.byteOffset+=e}}function oe(d,e,t,s){const n={externalResourceFunction:s};return t&&(n.uri=e==="file:"?t:e+t),ArrayBuffer.isView(d)?GLTFValidator.validateBytes(d,n):GLTFValidator.validateString(d,n)}function Je(){const d=[];onmessage=e=>{const t=e.data;switch(t.id){case"init":{importScripts(t.url);break}case"validate":{oe(t.data,t.rootUrl,t.fileName,s=>new Promise((n,i)=>{const r=d.length;d.push({resolve:n,reject:i}),postMessage({id:"getExternalResource",index:r,uri:s})})).then(s=>{postMessage({id:"validate.resolve",value:s})},s=>{postMessage({id:"validate.reject",reason:s})});break}case"getExternalResource.resolve":{d[t.index].resolve(t.value);break}case"getExternalResource.reject":{d[t.index].reject(t.reason);break}}}}class be{static ValidateAsync(e,t,s,n){return typeof Worker=="function"?new Promise((i,r)=>{const o=`${oe}(${Je})()`,a=URL.createObjectURL(new Blob([o],{type:"application/javascript"})),h=new Worker(a),l=c=>{h.removeEventListener("error",l),h.removeEventListener("message",u),r(c)},u=c=>{const p=c.data;switch(p.id){case"getExternalResource":{n(p.uri).then(f=>{h.postMessage({id:"getExternalResource.resolve",index:p.index,value:f},[f.buffer])},f=>{h.postMessage({id:"getExternalResource.reject",index:p.index,reason:f})});break}case"validate.resolve":{h.removeEventListener("error",l),h.removeEventListener("message",u),i(p.value),h.terminate();break}case"validate.reject":h.removeEventListener("error",l),h.removeEventListener("message",u),r(p.reason),h.terminate()}};if(h.addEventListener("error",l),h.addEventListener("message",u),h.postMessage({id:"init",url:R.GetBabylonScriptURL(this.Configuration.url)}),ArrayBuffer.isView(e)){const c=e.slice();h.postMessage({id:"validate",data:c,rootUrl:t,fileName:s},[c.buffer])}else h.postMessage({id:"validate",data:e,rootUrl:t,fileName:s})}):(this._LoadScriptPromise||(this._LoadScriptPromise=R.LoadBabylonScriptAsync(this.Configuration.url)),this._LoadScriptPromise.then(()=>oe(e,t,s,n)))}}be.Configuration={url:`${R._DefaultCdnUrl}/gltf_validator.js`};function me(d,e,t){try{return Promise.resolve(new Uint8Array(d,e,t))}catch(s){return Promise.reject(s)}}function Qe(d,e,t){try{if(e<0||e>=d.byteLength)throw new RangeError("Offset is out of range.");if(e+t>d.byteLength)throw new RangeError("Length is out of range.");return Promise.resolve(new Uint8Array(d.buffer,d.byteOffset+e,t))}catch(s){return Promise.reject(s)}}var Y;(function(d){d[d.AUTO=0]="AUTO",d[d.FORCE_RIGHT_HANDED=1]="FORCE_RIGHT_HANDED"})(Y||(Y={}));var W;(function(d){d[d.NONE=0]="NONE",d[d.FIRST=1]="FIRST",d[d.ALL=2]="ALL"})(W||(W={}));var k;(function(d){d[d.LOADING=0]="LOADING",d[d.READY=1]="READY",d[d.COMPLETE=2]="COMPLETE"})(k||(k={}));class je{constructor(){this.coordinateSystemMode=Y.AUTO,this.animationStartMode=W.FIRST,this.loadNodeAnimations=!0,this.loadSkins=!0,this.loadMorphTargets=!0,this.compileMaterials=!1,this.useClipPlane=!1,this.compileShadowGenerators=!1,this.transparencyAsCoverage=!1,this.useRangeRequests=!1,this.createInstances=!0,this.alwaysComputeBoundingBox=!1,this.loadAllMaterials=!1,this.loadOnlyMaterials=!1,this.skipMaterials=!1,this.useSRGBBuffers=!0,this.targetFps=60,this.alwaysComputeSkeletonRootNode=!1,this.useGltfTextureNames=!1,this.preprocessUrlAsync=e=>Promise.resolve(e),this.extensionOptions={}}copyFrom(e){e&&(this.onParsed=e.onParsed,this.coordinateSystemMode=e.coordinateSystemMode??this.coordinateSystemMode,this.animationStartMode=e.animationStartMode??this.animationStartMode,this.loadNodeAnimations=e.loadNodeAnimations??this.loadNodeAnimations,this.loadSkins=e.loadSkins??this.loadSkins,this.loadMorphTargets=e.loadMorphTargets??this.loadMorphTargets,this.compileMaterials=e.compileMaterials??this.compileMaterials,this.useClipPlane=e.useClipPlane??this.useClipPlane,this.compileShadowGenerators=e.compileShadowGenerators??this.compileShadowGenerators,this.transparencyAsCoverage=e.transparencyAsCoverage??this.transparencyAsCoverage,this.useRangeRequests=e.useRangeRequests??this.useRangeRequests,this.createInstances=e.createInstances??this.createInstances,this.alwaysComputeBoundingBox=e.alwaysComputeBoundingBox??this.alwaysComputeBoundingBox,this.loadAllMaterials=e.loadAllMaterials??this.loadAllMaterials,this.loadOnlyMaterials=e.loadOnlyMaterials??this.loadOnlyMaterials,this.skipMaterials=e.skipMaterials??this.skipMaterials,this.useSRGBBuffers=e.useSRGBBuffers??this.useSRGBBuffers,this.targetFps=e.targetFps??this.targetFps,this.alwaysComputeSkeletonRootNode=e.alwaysComputeSkeletonRootNode??this.alwaysComputeSkeletonRootNode,this.useGltfTextureNames=e.useGltfTextureNames??this.useGltfTextureNames,this.preprocessUrlAsync=e.preprocessUrlAsync??this.preprocessUrlAsync,this.customRootNode=e.customRootNode,this.onMeshLoaded=e.onMeshLoaded,this.onSkinLoaded=e.onSkinLoaded,this.onTextureLoaded=e.onTextureLoaded,this.onMaterialLoaded=e.onMaterialLoaded,this.onCameraLoaded=e.onCameraLoaded,this.extensionOptions=e.extensionOptions??this.extensionOptions)}}class E extends je{constructor(e){super(),this.onParsedObservable=new P,this.onMeshLoadedObservable=new P,this.onSkinLoadedObservable=new P,this.onTextureLoadedObservable=new P,this.onMaterialLoadedObservable=new P,this.onCameraLoadedObservable=new P,this.onCompleteObservable=new P,this.onErrorObservable=new P,this.onDisposeObservable=new P,this.onExtensionLoadedObservable=new P,this.validate=!1,this.onValidatedObservable=new P,this._loader=null,this._state=null,this._requests=new Array,this.name=K.name,this.extensions=K.extensions,this.onLoaderStateChangedObservable=new P,this._logIndentLevel=0,this._loggingEnabled=!1,this._log=this._logDisabled,this._capturePerformanceCounters=!1,this._startPerformanceCounter=this._startPerformanceCounterDisabled,this._endPerformanceCounter=this._endPerformanceCounterDisabled,this.copyFrom(e)}set onParsed(e){this._onParsedObserver&&this.onParsedObservable.remove(this._onParsedObserver),e&&(this._onParsedObserver=this.onParsedObservable.add(e))}set onMeshLoaded(e){this._onMeshLoadedObserver&&this.onMeshLoadedObservable.remove(this._onMeshLoadedObserver),e&&(this._onMeshLoadedObserver=this.onMeshLoadedObservable.add(e))}set onSkinLoaded(e){this._onSkinLoadedObserver&&this.onSkinLoadedObservable.remove(this._onSkinLoadedObserver),e&&(this._onSkinLoadedObserver=this.onSkinLoadedObservable.add(t=>e(t.node,t.skinnedNode)))}set onTextureLoaded(e){this._onTextureLoadedObserver&&this.onTextureLoadedObservable.remove(this._onTextureLoadedObserver),e&&(this._onTextureLoadedObserver=this.onTextureLoadedObservable.add(e))}set onMaterialLoaded(e){this._onMaterialLoadedObserver&&this.onMaterialLoadedObservable.remove(this._onMaterialLoadedObserver),e&&(this._onMaterialLoadedObserver=this.onMaterialLoadedObservable.add(e))}set onCameraLoaded(e){this._onCameraLoadedObserver&&this.onCameraLoadedObservable.remove(this._onCameraLoadedObserver),e&&(this._onCameraLoadedObserver=this.onCameraLoadedObservable.add(e))}set onComplete(e){this._onCompleteObserver&&this.onCompleteObservable.remove(this._onCompleteObserver),this._onCompleteObserver=this.onCompleteObservable.add(e)}set onError(e){this._onErrorObserver&&this.onErrorObservable.remove(this._onErrorObserver),this._onErrorObserver=this.onErrorObservable.add(e)}set onDispose(e){this._onDisposeObserver&&this.onDisposeObservable.remove(this._onDisposeObserver),this._onDisposeObserver=this.onDisposeObservable.add(e)}set onExtensionLoaded(e){this._onExtensionLoadedObserver&&this.onExtensionLoadedObservable.remove(this._onExtensionLoadedObserver),this._onExtensionLoadedObserver=this.onExtensionLoadedObservable.add(e)}get loggingEnabled(){return this._loggingEnabled}set loggingEnabled(e){this._loggingEnabled!==e&&(this._loggingEnabled=e,this._loggingEnabled?this._log=this._logEnabled:this._log=this._logDisabled)}get capturePerformanceCounters(){return this._capturePerformanceCounters}set capturePerformanceCounters(e){this._capturePerformanceCounters!==e&&(this._capturePerformanceCounters=e,this._capturePerformanceCounters?(this._startPerformanceCounter=this._startPerformanceCounterEnabled,this._endPerformanceCounter=this._endPerformanceCounterEnabled):(this._startPerformanceCounter=this._startPerformanceCounterDisabled,this._endPerformanceCounter=this._endPerformanceCounterDisabled))}set onValidated(e){this._onValidatedObserver&&this.onValidatedObservable.remove(this._onValidatedObserver),this._onValidatedObserver=this.onValidatedObservable.add(e)}dispose(){this._loader&&(this._loader.dispose(),this._loader=null);for(const e of this._requests)e.abort();this._requests.length=0,delete this._progressCallback,this.preprocessUrlAsync=e=>Promise.resolve(e),this.onMeshLoadedObservable.clear(),this.onSkinLoadedObservable.clear(),this.onTextureLoadedObservable.clear(),this.onMaterialLoadedObservable.clear(),this.onCameraLoadedObservable.clear(),this.onCompleteObservable.clear(),this.onExtensionLoadedObservable.clear(),this.onDisposeObservable.notifyObservers(void 0),this.onDisposeObservable.clear()}loadFile(e,t,s,n,i,r,o,a){if(ArrayBuffer.isView(t))return this._loadBinary(e,t,s,n,o,a),null;this._progressCallback=i;const h=t.name||R.GetFilename(t);if(r){if(this.useRangeRequests){this.validate&&T.Warn("glTF validation is not supported when range requests are enabled");const l={abort:()=>{},onCompleteObservable:new P},u={readAsync:(c,p)=>new Promise((f,m)=>{this._loadFile(e,t,w=>{f(new Uint8Array(w))},!0,w=>{m(w)},w=>{w.setRequestHeader("Range",`bytes=${c}-${c+p-1}`)})}),byteLength:0};return this._unpackBinaryAsync(new Q(u)).then(c=>{l.onCompleteObservable.notifyObservers(l),n(c)},o?c=>o(void 0,c):void 0),l}return this._loadFile(e,t,l=>{this._validate(e,new Uint8Array(l,0,l.byteLength),s,h),this._unpackBinaryAsync(new Q({readAsync:(u,c)=>me(l,u,c),byteLength:l.byteLength})).then(u=>{n(u)},o?u=>o(void 0,u):void 0)},!0,o)}else return this._loadFile(e,t,l=>{try{this._validate(e,l,s,h),n({json:this._parseJson(l)})}catch{o&&o()}},!1,o)}_loadBinary(e,t,s,n,i,r){this._validate(e,new Uint8Array(t.buffer,t.byteOffset,t.byteLength),s,r),this._unpackBinaryAsync(new Q({readAsync:(o,a)=>Qe(t,o,a),byteLength:t.byteLength})).then(o=>{n(o)},i?o=>i(void 0,o):void 0)}importMeshAsync(e,t,s,n,i,r){return Promise.resolve().then(()=>(this.onParsedObservable.notifyObservers(s),this.onParsedObservable.clear(),this._log(`Loading ${r||""}`),this._loader=this._getLoader(s),this._loader.importMeshAsync(e,t,null,s,n,i,r)))}loadAsync(e,t,s,n,i){return Promise.resolve().then(()=>(this.onParsedObservable.notifyObservers(t),this.onParsedObservable.clear(),this._log(`Loading ${i||""}`),this._loader=this._getLoader(t),this._loader.loadAsync(e,t,s,n,i)))}loadAssetContainerAsync(e,t,s,n,i){return Promise.resolve().then(()=>{this.onParsedObservable.notifyObservers(t),this.onParsedObservable.clear(),this._log(`Loading ${i||""}`),this._loader=this._getLoader(t);const r=new ze(e),o=[];this.onMaterialLoadedObservable.add(u=>{o.push(u)});const a=[];this.onTextureLoadedObservable.add(u=>{a.push(u)});const h=[];this.onCameraLoadedObservable.add(u=>{h.push(u)});const l=[];return this.onMeshLoadedObservable.add(u=>{u.morphTargetManager&&l.push(u.morphTargetManager)}),this._loader.importMeshAsync(null,e,r,t,s,n,i).then(u=>(Array.prototype.push.apply(r.geometries,u.geometries),Array.prototype.push.apply(r.meshes,u.meshes),Array.prototype.push.apply(r.particleSystems,u.particleSystems),Array.prototype.push.apply(r.skeletons,u.skeletons),Array.prototype.push.apply(r.animationGroups,u.animationGroups),Array.prototype.push.apply(r.materials,o),Array.prototype.push.apply(r.textures,a),Array.prototype.push.apply(r.lights,u.lights),Array.prototype.push.apply(r.transformNodes,u.transformNodes),Array.prototype.push.apply(r.cameras,h),Array.prototype.push.apply(r.morphTargetManagers,l),r))})}canDirectLoad(e){return K.canDirectLoad(e)}directLoad(e,t){if(t.startsWith("base64,"+q)||t.startsWith(";base64,"+q)||t.startsWith("application/octet-stream;base64,"+q)||t.startsWith("model/gltf-binary;base64,"+q)){const s=ye(t);return this._validate(e,new Uint8Array(s,0,s.byteLength)),this._unpackBinaryAsync(new Q({readAsync:(n,i)=>me(s,n,i),byteLength:s.byteLength}))}return this._validate(e,t),Promise.resolve({json:this._parseJson(t)})}createPlugin(e){return new E(e[K.name])}get loaderState(){return this._state}whenCompleteAsync(){return new Promise((e,t)=>{this.onCompleteObservable.addOnce(()=>{e()}),this.onErrorObservable.addOnce(s=>{t(s)})})}_setState(e){this._state!==e&&(this._state=e,this.onLoaderStateChangedObservable.notifyObservers(this._state),this._log(k[this._state]))}_loadFile(e,t,s,n,i,r){const o=e._loadFile(t,s,a=>{this._onProgress(a,o)},!0,n,i,r);return o.onCompleteObservable.add(()=>{o._lengthComputable=!0,o._total=o._loaded}),this._requests.push(o),o}_onProgress(e,t){if(!this._progressCallback)return;t._lengthComputable=e.lengthComputable,t._loaded=e.loaded,t._total=e.total;let s=!0,n=0,i=0;for(const r of this._requests){if(r._lengthComputable===void 0||r._loaded===void 0||r._total===void 0)return;s=s&&r._lengthComputable,n+=r._loaded,i+=r._total}this._progressCallback({lengthComputable:s,loaded:n,total:s?i:0})}_validate(e,t,s="",n=""){this.validate&&(this._startPerformanceCounter("Validate JSON"),be.ValidateAsync(t,s,n,i=>this.preprocessUrlAsync(s+i).then(r=>e._loadFileAsync(r,void 0,!0,!0).then(o=>new Uint8Array(o,0,o.byteLength)))).then(i=>{this._endPerformanceCounter("Validate JSON"),this.onValidatedObservable.notifyObservers(i),this.onValidatedObservable.clear()},i=>{this._endPerformanceCounter("Validate JSON"),R.Warn(`Failed to validate: ${i.message}`),this.onValidatedObservable.clear()}))}_getLoader(e){const t=e.json.asset||{};this._log(`Asset version: ${t.version}`),t.minVersion&&this._log(`Asset minimum version: ${t.minVersion}`),t.generator&&this._log(`Asset generator: ${t.generator}`);const s=E._parseVersion(t.version);if(!s)throw new Error("Invalid version: "+t.version);if(t.minVersion!==void 0){const r=E._parseVersion(t.minVersion);if(!r)throw new Error("Invalid minimum version: "+t.minVersion);if(E._compareVersion(r,{major:2,minor:0})>0)throw new Error("Incompatible minimum version: "+t.minVersion)}const i={1:E._CreateGLTF1Loader,2:E._CreateGLTF2Loader}[s.major];if(!i)throw new Error("Unsupported version: "+t.version);return i(this)}_parseJson(e){this._startPerformanceCounter("Parse JSON"),this._log(`JSON length: ${e.length}`);const t=JSON.parse(e);return this._endPerformanceCounter("Parse JSON"),t}_unpackBinaryAsync(e){return this._startPerformanceCounter("Unpack Binary"),e.loadAsync(20).then(()=>{const t={Magic:1179937895},s=e.readUint32();if(s!==t.Magic)throw new Ee("Unexpected magic: "+s,Ie.GLTFLoaderUnexpectedMagicError);const n=e.readUint32();this.loggingEnabled&&this._log(`Binary version: ${n}`);const i=e.readUint32();!this.useRangeRequests&&i!==e.buffer.byteLength&&T.Warn(`Length in header does not match actual data length: ${i} != ${e.buffer.byteLength}`);let r;switch(n){case 1:{r=this._unpackBinaryV1Async(e,i);break}case 2:{r=this._unpackBinaryV2Async(e,i);break}default:throw new Error("Unsupported version: "+n)}return this._endPerformanceCounter("Unpack Binary"),r})}_unpackBinaryV1Async(e,t){const s={JSON:0},n=e.readUint32(),i=e.readUint32();if(i!==s.JSON)throw new Error(`Unexpected content format: ${i}`);const r=t-e.byteOffset,o={json:this._parseJson(e.readString(n)),bin:null};if(r!==0){const a=e.byteOffset;o.bin={readAsync:(h,l)=>e.buffer.readAsync(a+h,l),byteLength:r}}return Promise.resolve(o)}_unpackBinaryV2Async(e,t){const s={JSON:1313821514,BIN:5130562},n=e.readUint32();if(e.readUint32()!==s.JSON)throw new Error("First chunk format is not JSON");return e.byteOffset+n===t?e.loadAsync(n).then(()=>({json:this._parseJson(e.readString(n)),bin:null})):e.loadAsync(n+8).then(()=>{const r={json:this._parseJson(e.readString(n)),bin:null},o=()=>{const a=e.readUint32();switch(e.readUint32()){case s.JSON:throw new Error("Unexpected JSON chunk");case s.BIN:{const l=e.byteOffset;r.bin={readAsync:(u,c)=>e.buffer.readAsync(l+u,c),byteLength:a},e.skipBytes(a);break}default:{e.skipBytes(a);break}}return e.byteOffset!==t?e.loadAsync(8).then(o):Promise.resolve(r)};return o()})}static _parseVersion(e){if(e==="1.0"||e==="1.0.1")return{major:1,minor:0};const t=(e+"").match(/^(\d+)\.(\d+)/);return t?{major:parseInt(t[1]),minor:parseInt(t[2])}:null}static _compareVersion(e,t){return e.major>t.major?1:e.major<t.major?-1:e.minor>t.minor?1:e.minor<t.minor?-1:0}_logOpen(e){this._log(e),this._logIndentLevel++}_logClose(){--this._logIndentLevel}_logEnabled(e){const t=E._logSpaces.substring(0,this._logIndentLevel*2);T.Log(`${t}${e}`)}_logDisabled(e){}_startPerformanceCounterEnabled(e){R.StartPerformanceCounter(e)}_startPerformanceCounterDisabled(e){}_endPerformanceCounterEnabled(e){R.EndPerformanceCounter(e)}_endPerformanceCounterDisabled(e){}}E.IncrementalLoading=!0;E.HomogeneousCoordinates=!1;E._logSpaces="                                ";Le(new E);class _{static Get(e,t,s){if(!t||s==null||!t[s])throw new Error(`${e}: Failed to find index (${s})`);return t[s]}static TryGet(e,t){return!e||t==null||!e[t]?null:e[t]}static Assign(e){if(e)for(let t=0;t<e.length;t++)e[t].index=t}}function et(d){if(d.min&&d.max){const e=d.min,t=d.max,s=j.Vector3[0].copyFromFloats(e[0],e[1],e[2]),n=j.Vector3[1].copyFromFloats(t[0],t[1],t[2]);if(d.normalized&&d.componentType!==5126){let i=1;switch(d.componentType){case 5120:i=127;break;case 5121:i=255;break;case 5122:i=32767;break;case 5123:i=65535;break}const r=1/i;s.scaleInPlace(r),n.scaleInPlace(r)}return new De(s,n)}return null}class x{static RegisterExtension(e,t){Ne(e,!1,t)}static UnregisterExtension(e){return Be(e)}get gltf(){if(!this._gltf)throw new Error("glTF JSON is not available");return this._gltf}get bin(){return this._bin}get parent(){return this._parent}get babylonScene(){if(!this._babylonScene)throw new Error("Scene is not available");return this._babylonScene}get rootBabylonMesh(){return this._rootBabylonMesh}get rootUrl(){return this._rootUrl}constructor(e){this._completePromises=new Array,this._assetContainer=null,this._babylonLights=[],this._disableInstancedMesh=0,this._allMaterialsDirtyRequired=!1,this._skipStartAnimationStep=!1,this._extensions=new Array,this._disposed=!1,this._rootUrl=null,this._fileName=null,this._uniqueRootUrl=null,this._bin=null,this._rootBabylonMesh=null,this._defaultBabylonMaterialData={},this._postSceneLoadActions=new Array,this._parent=e}dispose(){this._disposed||(this._disposed=!0,this._completePromises.length=0,this._extensions.forEach(e=>e.dispose&&e.dispose()),this._extensions.length=0,this._gltf=null,this._bin=null,this._babylonScene=null,this._rootBabylonMesh=null,this._defaultBabylonMaterialData={},this._postSceneLoadActions.length=0,this._parent.dispose())}importMeshAsync(e,t,s,n,i,r,o=""){return Promise.resolve().then(()=>{this._babylonScene=t,this._assetContainer=s,this._loadData(n);let a=null;if(e){const h={};if(this._gltf.nodes)for(const u of this._gltf.nodes)u.name&&(h[u.name]=u.index);a=(e instanceof Array?e:[e]).map(u=>{const c=h[u];if(c===void 0)throw new Error(`Failed to find node '${u}'`);return c})}return this._loadAsync(i,o,a,()=>({meshes:this._getMeshes(),particleSystems:[],skeletons:this._getSkeletons(),animationGroups:this._getAnimationGroups(),lights:this._babylonLights,transformNodes:this._getTransformNodes(),geometries:this._getGeometries(),spriteManagers:[]}))})}loadAsync(e,t,s,n,i=""){return Promise.resolve().then(()=>(this._babylonScene=e,this._loadData(t),this._loadAsync(s,i,null,()=>{})))}_loadAsync(e,t,s,n){return Promise.resolve().then(async()=>{this._rootUrl=e,this._uniqueRootUrl=!e.startsWith("file:")&&t?e:`${e}${Date.now()}/`,this._fileName=t,this._allMaterialsDirtyRequired=!1,await this._loadExtensionsAsync();const i=`${k[k.LOADING]} => ${k[k.READY]}`,r=`${k[k.LOADING]} => ${k[k.COMPLETE]}`;this._parent._startPerformanceCounter(i),this._parent._startPerformanceCounter(r),this._parent._setState(k.LOADING),this._extensionsOnLoading();const o=new Array,a=this._babylonScene.blockMaterialDirtyMechanism;if(this._babylonScene.blockMaterialDirtyMechanism=!0,!this.parent.loadOnlyMaterials){if(s)o.push(this.loadSceneAsync("/nodes",{nodes:s,index:-1}));else if(this._gltf.scene!=null||this._gltf.scenes&&this._gltf.scenes[0]){const l=_.Get("/scene",this._gltf.scenes,this._gltf.scene||0);o.push(this.loadSceneAsync(`/scenes/${l.index}`,l))}}if(!this.parent.skipMaterials&&this.parent.loadAllMaterials&&this._gltf.materials)for(let l=0;l<this._gltf.materials.length;++l){const u=this._gltf.materials[l],c="/materials/"+l,p=N.TriangleFillMode;o.push(this._loadMaterialAsync(c,u,null,p,()=>{}))}return this._allMaterialsDirtyRequired?this._babylonScene.blockMaterialDirtyMechanism=a:this._babylonScene._forceBlockMaterialDirtyMechanism(a),this._parent.compileMaterials&&o.push(this._compileMaterialsAsync()),this._parent.compileShadowGenerators&&o.push(this._compileShadowGeneratorsAsync()),Promise.all(o).then(()=>{this._rootBabylonMesh&&this._rootBabylonMesh!==this._parent.customRootNode&&this._rootBabylonMesh.setEnabled(!0);for(const l of this._babylonScene.materials){const u=l;u.maxSimultaneousLights!==void 0&&(u.maxSimultaneousLights=Math.max(u.maxSimultaneousLights,this._babylonScene.lights.length))}return this._extensionsOnReady(),this._parent._setState(k.READY),this._skipStartAnimationStep||this._startAnimations(),n()}).then(l=>(this._parent._endPerformanceCounter(i),R.SetImmediate(()=>{this._disposed||Promise.all(this._completePromises).then(()=>{this._parent._endPerformanceCounter(r),this._parent._setState(k.COMPLETE),this._parent.onCompleteObservable.notifyObservers(void 0),this._parent.onCompleteObservable.clear(),this.dispose()},u=>{this._parent.onErrorObservable.notifyObservers(u),this._parent.onErrorObservable.clear(),this.dispose()})}),l))}).catch(i=>{throw this._disposed||(this._parent.onErrorObservable.notifyObservers(i),this._parent.onErrorObservable.clear(),this.dispose()),i})}_loadData(e){if(this._gltf=e.json,this._setupData(),e.bin){const t=this._gltf.buffers;if(t&&t[0]&&!t[0].uri){const s=t[0];(s.byteLength<e.bin.byteLength-3||s.byteLength>e.bin.byteLength)&&T.Warn(`Binary buffer length (${s.byteLength}) from JSON does not match chunk length (${e.bin.byteLength})`),this._bin=e.bin}else T.Warn("Unexpected BIN chunk")}}_setupData(){if(_.Assign(this._gltf.accessors),_.Assign(this._gltf.animations),_.Assign(this._gltf.buffers),_.Assign(this._gltf.bufferViews),_.Assign(this._gltf.cameras),_.Assign(this._gltf.images),_.Assign(this._gltf.materials),_.Assign(this._gltf.meshes),_.Assign(this._gltf.nodes),_.Assign(this._gltf.samplers),_.Assign(this._gltf.scenes),_.Assign(this._gltf.skins),_.Assign(this._gltf.textures),this._gltf.nodes){const e={};for(const s of this._gltf.nodes)if(s.children)for(const n of s.children)e[n]=s.index;const t=this._createRootNode();for(const s of this._gltf.nodes){const n=e[s.index];s.parent=n===void 0?t:this._gltf.nodes[n]}}}async _loadExtensionsAsync(){var t;const e=[];if($e.forEach((s,n)=>{var i;((i=this.parent.extensionOptions[n])==null?void 0:i.enabled)===!1?s.isGLTFExtension&&this.isExtensionUsed(n)&&T.Warn(`Extension ${n} is used but has been explicitly disabled.`):(!s.isGLTFExtension||this.isExtensionUsed(n))&&e.push((async()=>{const r=await s.factory(this);return r.name!==n&&T.Warn(`The name of the glTF loader extension instance does not match the registered name: ${r.name} !== ${n}`),this._parent.onExtensionLoadedObservable.notifyObservers(r),r})())}),this._extensions.push(...await Promise.all(e)),this._extensions.sort((s,n)=>(s.order||Number.MAX_VALUE)-(n.order||Number.MAX_VALUE)),this._parent.onExtensionLoadedObservable.clear(),this._gltf.extensionsRequired){for(const s of this._gltf.extensionsRequired)if(!this._extensions.some(i=>i.name===s&&i.enabled))throw((t=this.parent.extensionOptions[s])==null?void 0:t.enabled)===!1?new Error(`Required extension ${s} is disabled`):new Error(`Required extension ${s} is not available`)}}_createRootNode(){if(this._parent.customRootNode!==void 0)return this._rootBabylonMesh=this._parent.customRootNode,{_babylonTransformNode:this._rootBabylonMesh===null?void 0:this._rootBabylonMesh,index:-1};this._babylonScene._blockEntityCollection=!!this._assetContainer;const e=new fe("__root__",this._babylonScene);this._rootBabylonMesh=e,this._rootBabylonMesh._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,this._rootBabylonMesh.setEnabled(!1);const t={_babylonTransformNode:this._rootBabylonMesh,index:-1};switch(this._parent.coordinateSystemMode){case Y.AUTO:{this._babylonScene.useRightHandedSystem||(t.rotation=[0,1,0,0],t.scale=[1,1,-1],x._LoadTransform(t,this._rootBabylonMesh));break}case Y.FORCE_RIGHT_HANDED:{this._babylonScene.useRightHandedSystem=!0;break}default:throw new Error(`Invalid coordinate system mode (${this._parent.coordinateSystemMode})`)}return this._parent.onMeshLoadedObservable.notifyObservers(e),t}loadSceneAsync(e,t){const s=this._extensionsLoadSceneAsync(e,t);if(s)return s;const n=new Array;if(this.logOpen(`${e} ${t.name||""}`),t.nodes)for(const i of t.nodes){const r=_.Get(`${e}/nodes/${i}`,this._gltf.nodes,i);n.push(this.loadNodeAsync(`/nodes/${r.index}`,r,o=>{o.parent=this._rootBabylonMesh}))}for(const i of this._postSceneLoadActions)i();return n.push(this._loadAnimationsAsync()),this.logClose(),Promise.all(n).then(()=>{})}_forEachPrimitive(e,t){if(e._primitiveBabylonMeshes)for(const s of e._primitiveBabylonMeshes)t(s)}_getGeometries(){const e=[],t=this._gltf.nodes;if(t)for(const s of t)this._forEachPrimitive(s,n=>{const i=n.geometry;i&&e.indexOf(i)===-1&&e.push(i)});return e}_getMeshes(){const e=[];this._rootBabylonMesh instanceof Ue&&e.push(this._rootBabylonMesh);const t=this._gltf.nodes;if(t)for(const s of t)this._forEachPrimitive(s,n=>{e.push(n)});return e}_getTransformNodes(){const e=[],t=this._gltf.nodes;if(t)for(const s of t)s._babylonTransformNode&&s._babylonTransformNode.getClassName()==="TransformNode"&&e.push(s._babylonTransformNode),s._babylonTransformNodeForSkin&&e.push(s._babylonTransformNodeForSkin);return e}_getSkeletons(){const e=[],t=this._gltf.skins;if(t)for(const s of t)s._data&&e.push(s._data.babylonSkeleton);return e}_getAnimationGroups(){const e=[],t=this._gltf.animations;if(t)for(const s of t)s._babylonAnimationGroup&&e.push(s._babylonAnimationGroup);return e}_startAnimations(){switch(this._parent.animationStartMode){case W.NONE:break;case W.FIRST:{const e=this._getAnimationGroups();e.length!==0&&e[0].start(!0);break}case W.ALL:{const e=this._getAnimationGroups();for(const t of e)t.start(!0);break}default:{T.Error(`Invalid animation start mode (${this._parent.animationStartMode})`);return}}}loadNodeAsync(e,t,s=()=>{}){const n=this._extensionsLoadNodeAsync(e,t,s);if(n)return n;if(t._babylonTransformNode)throw new Error(`${e}: Invalid recursive node hierarchy`);const i=new Array;this.logOpen(`${e} ${t.name||""}`);const r=h=>{if(x.AddPointerMetadata(h,e),x._LoadTransform(t,h),t.camera!=null){const l=_.Get(`${e}/camera`,this._gltf.cameras,t.camera);i.push(this.loadCameraAsync(`/cameras/${l.index}`,l,u=>{u.parent=h}))}if(t.children)for(const l of t.children){const u=_.Get(`${e}/children/${l}`,this._gltf.nodes,l);i.push(this.loadNodeAsync(`/nodes/${u.index}`,u,c=>{c.parent=h}))}s(h)},o=t.mesh!=null,a=this._parent.loadSkins&&t.skin!=null;if(!o||a){const h=t.name||`node${t.index}`;this._babylonScene._blockEntityCollection=!!this._assetContainer;const l=new _e(h,this._babylonScene);l._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,t.mesh==null?t._babylonTransformNode=l:t._babylonTransformNodeForSkin=l,r(l)}if(o)if(a){const h=_.Get(`${e}/mesh`,this._gltf.meshes,t.mesh);i.push(this._loadMeshAsync(`/meshes/${h.index}`,t,h,l=>{const u=t._babylonTransformNodeForSkin;l.metadata=Ve(u.metadata,l.metadata||{});const c=_.Get(`${e}/skin`,this._gltf.skins,t.skin);i.push(this._loadSkinAsync(`/skins/${c.index}`,t,c,p=>{this._forEachPrimitive(t,f=>{f.skeleton=p}),this._postSceneLoadActions.push(()=>{if(c.skeleton!=null){const f=_.Get(`/skins/${c.index}/skeleton`,this._gltf.nodes,c.skeleton).parent;t.index===f.index?l.parent=u.parent:l.parent=f._babylonTransformNode}else l.parent=this._rootBabylonMesh;this._parent.onSkinLoadedObservable.notifyObservers({node:u,skinnedNode:l})})}))}))}else{const h=_.Get(`${e}/mesh`,this._gltf.meshes,t.mesh);i.push(this._loadMeshAsync(`/meshes/${h.index}`,t,h,r))}return this.logClose(),Promise.all(i).then(()=>(this._forEachPrimitive(t,h=>{const l=h;!l.isAnInstance&&l.geometry&&l.geometry.useBoundingInfoFromGeometry?h._updateBoundingInfo():h.refreshBoundingInfo(!0,!0)}),t._babylonTransformNode))}_loadMeshAsync(e,t,s,n){const i=s.primitives;if(!i||!i.length)throw new Error(`${e}: Primitives are missing`);i[0].index==null&&_.Assign(i);const r=new Array;this.logOpen(`${e} ${s.name||""}`);const o=t.name||`node${t.index}`;if(i.length===1){const a=s.primitives[0];r.push(this._loadMeshPrimitiveAsync(`${e}/primitives/${a.index}`,o,t,s,a,h=>{t._babylonTransformNode=h,t._primitiveBabylonMeshes=[h]}))}else{this._babylonScene._blockEntityCollection=!!this._assetContainer,t._babylonTransformNode=new _e(o,this._babylonScene),t._babylonTransformNode._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,t._primitiveBabylonMeshes=[];for(const a of i)r.push(this._loadMeshPrimitiveAsync(`${e}/primitives/${a.index}`,`${o}_primitive${a.index}`,t,s,a,h=>{h.parent=t._babylonTransformNode,t._primitiveBabylonMeshes.push(h)}))}return n(t._babylonTransformNode),this.logClose(),Promise.all(r).then(()=>t._babylonTransformNode)}_loadMeshPrimitiveAsync(e,t,s,n,i,r){const o=this._extensionsLoadMeshPrimitiveAsync(e,t,s,n,i,r);if(o)return o;this.logOpen(`${e}`);const a=this._disableInstancedMesh===0&&this._parent.createInstances&&s.skin==null&&!n.primitives[0].targets;let h,l;if(a&&i._instanceData)this._babylonScene._blockEntityCollection=!!this._assetContainer,h=i._instanceData.babylonSourceMesh.createInstance(t),h._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,l=i._instanceData.promise;else{const u=new Array;this._babylonScene._blockEntityCollection=!!this._assetContainer;const c=new fe(t,this._babylonScene);c._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,c.sideOrientation=this._babylonScene.useRightHandedSystem?N.CounterClockWiseSideOrientation:N.ClockWiseSideOrientation,this._createMorphTargets(e,s,n,i,c),u.push(this._loadVertexDataAsync(e,i,c).then(f=>this._loadMorphTargetsAsync(e,i,c,f).then(()=>{this._disposed||(this._babylonScene._blockEntityCollection=!!this._assetContainer,f.applyToMesh(c),f._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1)})));const p=x._GetDrawMode(e,i.mode);if(i.material==null){let f=this._defaultBabylonMaterialData[p];f||(f=this._createDefaultMaterial("__GLTFLoader._default",p),this._parent.onMaterialLoadedObservable.notifyObservers(f),this._defaultBabylonMaterialData[p]=f),c.material=f}else if(!this.parent.skipMaterials){const f=_.Get(`${e}/material`,this._gltf.materials,i.material);u.push(this._loadMaterialAsync(`/materials/${f.index}`,f,c,p,m=>{c.material=m}))}l=Promise.all(u),a&&(i._instanceData={babylonSourceMesh:c,promise:l}),h=c}return x.AddPointerMetadata(h,e),this._parent.onMeshLoadedObservable.notifyObservers(h),r(h),this.logClose(),l.then(()=>h)}_loadVertexDataAsync(e,t,s){const n=this._extensionsLoadVertexDataAsync(e,t,s);if(n)return n;const i=t.attributes;if(!i)throw new Error(`${e}: Attributes are missing`);const r=new Array,o=new Fe(s.name,this._babylonScene);if(t.indices==null)s.isUnIndexed=!0;else{const h=_.Get(`${e}/indices`,this._gltf.accessors,t.indices);r.push(this._loadIndicesAccessorAsync(`/accessors/${h.index}`,h).then(l=>{o.setIndices(l)}))}const a=(h,l,u)=>{if(i[h]==null)return;s._delayInfo=s._delayInfo||[],s._delayInfo.indexOf(l)===-1&&s._delayInfo.push(l);const c=_.Get(`${e}/attributes/${h}`,this._gltf.accessors,i[h]);r.push(this._loadVertexAccessorAsync(`/accessors/${c.index}`,c,l).then(p=>{if(p.getKind()===g.PositionKind&&!this.parent.alwaysComputeBoundingBox&&!s.skeleton){const f=et(c);f&&(o._boundingInfo=f,o.useBoundingInfoFromGeometry=!0)}o.setVerticesBuffer(p,c.count)})),l==g.MatricesIndicesExtraKind&&(s.numBoneInfluencers=8),u&&u(c)};return a("POSITION",g.PositionKind),a("NORMAL",g.NormalKind),a("TANGENT",g.TangentKind),a("TEXCOORD_0",g.UVKind),a("TEXCOORD_1",g.UV2Kind),a("TEXCOORD_2",g.UV3Kind),a("TEXCOORD_3",g.UV4Kind),a("TEXCOORD_4",g.UV5Kind),a("TEXCOORD_5",g.UV6Kind),a("JOINTS_0",g.MatricesIndicesKind),a("WEIGHTS_0",g.MatricesWeightsKind),a("JOINTS_1",g.MatricesIndicesExtraKind),a("WEIGHTS_1",g.MatricesWeightsExtraKind),a("COLOR_0",g.ColorKind,h=>{h.type==="VEC4"&&(s.hasVertexAlpha=!0)}),Promise.all(r).then(()=>o)}_createMorphTargets(e,t,s,n,i){if(!n.targets||!this._parent.loadMorphTargets)return;if(t._numMorphTargets==null)t._numMorphTargets=n.targets.length;else if(n.targets.length!==t._numMorphTargets)throw new Error(`${e}: Primitives do not have the same number of targets`);const r=s.extras?s.extras.targetNames:null;this._babylonScene._blockEntityCollection=!!this._assetContainer,i.morphTargetManager=new U(this._babylonScene),i.morphTargetManager._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,i.morphTargetManager.areUpdatesFrozen=!0;for(let o=0;o<n.targets.length;o++){const a=t.weights?t.weights[o]:s.weights?s.weights[o]:0,h=r?r[o]:`morphTarget${o}`;i.morphTargetManager.addTarget(new G(h,a,i.getScene()))}}_loadMorphTargetsAsync(e,t,s,n){if(!t.targets||!this._parent.loadMorphTargets)return Promise.resolve();const i=new Array,r=s.morphTargetManager;for(let o=0;o<r.numTargets;o++){const a=r.getTarget(o);i.push(this._loadMorphTargetVertexDataAsync(`${e}/targets/${o}`,n,t.targets[o],a))}return Promise.all(i).then(()=>{r.areUpdatesFrozen=!1})}_loadMorphTargetVertexDataAsync(e,t,s,n){const i=new Array,r=(o,a,h)=>{if(s[o]==null)return;const l=t.getVertexBuffer(a);if(!l)return;const u=_.Get(`${e}/${o}`,this._gltf.accessors,s[o]);i.push(this._loadFloatAccessorAsync(`/accessors/${u.index}`,u).then(c=>{h(l,c)}))};return r("POSITION",g.PositionKind,(o,a)=>{const h=new Float32Array(a.length);o.forEach(a.length,(l,u)=>{h[u]=a[u]+l}),n.setPositions(h)}),r("NORMAL",g.NormalKind,(o,a)=>{const h=new Float32Array(a.length);o.forEach(h.length,(l,u)=>{h[u]=a[u]+l}),n.setNormals(h)}),r("TANGENT",g.TangentKind,(o,a)=>{const h=new Float32Array(a.length/3*4);let l=0;o.forEach(a.length/3*4,(u,c)=>{(c+1)%4!==0&&(h[l]=a[l]+u,l++)}),n.setTangents(h)}),r("TEXCOORD_0",g.UVKind,(o,a)=>{const h=new Float32Array(a.length);o.forEach(a.length,(l,u)=>{h[u]=a[u]+l}),n.setUVs(h)}),r("TEXCOORD_1",g.UV2Kind,(o,a)=>{const h=new Float32Array(a.length);o.forEach(a.length,(l,u)=>{h[u]=a[u]+l}),n.setUV2s(h)}),r("COLOR_0",g.ColorKind,(o,a)=>{let h=null;const l=o.getSize();if(l===3){h=new Float32Array(a.length/3*4),o.forEach(a.length,(u,c)=>{const p=Math.floor(c/3),f=c%3;h[4*p+f]=a[3*p+f]+u});for(let u=0;u<a.length/3;++u)h[4*u+3]=1}else if(l===4)h=new Float32Array(a.length),o.forEach(a.length,(u,c)=>{h[c]=a[c]+u});else throw new Error(`${e}: Invalid number of components (${l}) for COLOR_0 attribute`);n.setColors(h)}),Promise.all(i).then(()=>{})}static _LoadTransform(e,t){if(e.skin!=null)return;let s=v.Zero(),n=z.Identity(),i=v.One();e.matrix?B.FromArray(e.matrix).decompose(i,n,s):(e.translation&&(s=v.FromArray(e.translation)),e.rotation&&(n=z.FromArray(e.rotation)),e.scale&&(i=v.FromArray(e.scale))),t.position=s,t.rotationQuaternion=n,t.scaling=i}_loadSkinAsync(e,t,s,n){if(!this._parent.loadSkins)return Promise.resolve();const i=this._extensionsLoadSkinAsync(e,t,s);if(i)return i;if(s._data)return n(s._data.babylonSkeleton),s._data.promise;const r=`skeleton${s.index}`;this._babylonScene._blockEntityCollection=!!this._assetContainer;const o=new ee(s.name||r,r,this._babylonScene);o._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,this._loadBones(e,s,o);const a=this._loadSkinInverseBindMatricesDataAsync(e,s).then(h=>{this._updateBoneMatrices(o,h)});return s._data={babylonSkeleton:o,promise:a},n(o),a}_loadBones(e,t,s){if(t.skeleton==null||this._parent.alwaysComputeSkeletonRootNode){const i=this._findSkeletonRootNode(`${e}/joints`,t.joints);if(i)if(t.skeleton===void 0)t.skeleton=i.index;else{const r=(a,h)=>{for(;h.parent;h=h.parent)if(h.parent===a)return!0;return!1},o=_.Get(`${e}/skeleton`,this._gltf.nodes,t.skeleton);o!==i&&!r(o,i)&&(T.Warn(`${e}/skeleton: Overriding with nearest common ancestor as skeleton node is not a common root`),t.skeleton=i.index)}else T.Warn(`${e}: Failed to find common root`)}const n={};for(const i of t.joints){const r=_.Get(`${e}/joints/${i}`,this._gltf.nodes,i);this._loadBone(r,t,s,n)}}_findSkeletonRootNode(e,t){if(t.length===0)return null;const s={};for(const i of t){const r=[];let o=_.Get(`${e}/${i}`,this._gltf.nodes,i);for(;o.index!==-1;)r.unshift(o),o=o.parent;s[i]=r}let n=null;for(let i=0;;++i){let r=s[t[0]];if(i>=r.length)return n;const o=r[i];for(let a=1;a<t.length;++a)if(r=s[t[a]],i>=r.length||o!==r[i])return n;n=o}}_loadBone(e,t,s,n){e._isJoint=!0;let i=n[e.index];if(i)return i;let r=null;e.index!==t.skeleton&&(e.parent&&e.parent.index!==-1?r=this._loadBone(e.parent,t,s,n):t.skeleton!==void 0&&T.Warn(`/skins/${t.index}/skeleton: Skeleton node is not a common root`));const o=t.joints.indexOf(e.index);return i=new re(e.name||`joint${e.index}`,s,r,this._getNodeMatrix(e),null,null,o),n[e.index]=i,this._postSceneLoadActions.push(()=>{i.linkTransformNode(e._babylonTransformNode)}),i}_loadSkinInverseBindMatricesDataAsync(e,t){if(t.inverseBindMatrices==null)return Promise.resolve(null);const s=_.Get(`${e}/inverseBindMatrices`,this._gltf.accessors,t.inverseBindMatrices);return this._loadFloatAccessorAsync(`/accessors/${s.index}`,s)}_updateBoneMatrices(e,t){for(const s of e.bones){const n=B.Identity(),i=s._index;t&&i!==-1&&(B.FromArrayToRef(t,i*16,n),n.invertToRef(n));const r=s.getParent();r&&n.multiplyToRef(r.getAbsoluteInverseBindMatrix(),n),s.updateMatrix(n,!1,!1),s._updateAbsoluteBindMatrices(void 0,!1)}}_getNodeMatrix(e){return e.matrix?B.FromArray(e.matrix):B.Compose(e.scale?v.FromArray(e.scale):v.One(),e.rotation?z.FromArray(e.rotation):z.Identity(),e.translation?v.FromArray(e.translation):v.Zero())}loadCameraAsync(e,t,s=()=>{}){const n=this._extensionsLoadCameraAsync(e,t,s);if(n)return n;const i=new Array;this.logOpen(`${e} ${t.name||""}`),this._babylonScene._blockEntityCollection=!!this._assetContainer;const r=new X(t.name||`camera${t.index}`,v.Zero(),this._babylonScene,!1);switch(r._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,r.ignoreParentScaling=!0,t._babylonCamera=r,r.rotation.set(0,Math.PI,0),t.type){case"perspective":{const o=t.perspective;if(!o)throw new Error(`${e}: Camera perspective properties are missing`);r.fov=o.yfov,r.minZ=o.znear,r.maxZ=o.zfar||0;break}case"orthographic":{if(!t.orthographic)throw new Error(`${e}: Camera orthographic properties are missing`);r.mode=Ge.ORTHOGRAPHIC_CAMERA,r.orthoLeft=-t.orthographic.xmag,r.orthoRight=t.orthographic.xmag,r.orthoBottom=-t.orthographic.ymag,r.orthoTop=t.orthographic.ymag,r.minZ=t.orthographic.znear,r.maxZ=t.orthographic.zfar;break}default:throw new Error(`${e}: Invalid camera type (${t.type})`)}return x.AddPointerMetadata(r,e),this._parent.onCameraLoadedObservable.notifyObservers(r),s(r),this.logClose(),Promise.all(i).then(()=>r)}_loadAnimationsAsync(){const e=this._gltf.animations;if(!e)return Promise.resolve();const t=new Array;for(let s=0;s<e.length;s++){const n=e[s];t.push(this.loadAnimationAsync(`/animations/${n.index}`,n).then(i=>{i.targetedAnimations.length===0&&i.dispose()}))}return Promise.all(t).then(()=>{})}loadAnimationAsync(e,t){const s=this._extensionsLoadAnimationAsync(e,t);return s||le(()=>import("./animationGroup.CYmPDV4x.js"),__vite__mapDeps([0,1,2,3]),import.meta.url).then(({AnimationGroup:n})=>{this._babylonScene._blockEntityCollection=!!this._assetContainer;const i=new n(t.name||`animation${t.index}`,this._babylonScene);i._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,t._babylonAnimationGroup=i;const r=new Array;_.Assign(t.channels),_.Assign(t.samplers);for(const o of t.channels)r.push(this._loadAnimationChannelAsync(`${e}/channels/${o.index}`,e,t,o,(a,h)=>{a.animations=a.animations||[],a.animations.push(h),i.addTargetedAnimation(h,a)}));return Promise.all(r).then(()=>(i.normalize(0),i))})}async _loadAnimationChannelAsync(e,t,s,n,i){var c,p,f,m;const r=this._extensionsLoadAnimationChannelAsync(e,t,s,n,i);if(r)return r;if(n.target.node==null)return Promise.resolve();const o=_.Get(`${e}/target/node`,this._gltf.nodes,n.target.node),a=n.target.path,h=a==="weights";if(h&&!o._numMorphTargets||!h&&!o._babylonTransformNode||!this._parent.loadNodeAnimations&&!h&&!o._isJoint)return Promise.resolve();await le(()=>import("./glTFLoaderAnimation.CKms7EUf.js"),__vite__mapDeps([4,1,2,5]),import.meta.url);let l;switch(a){case"translation":{l=(c=J("/nodes/{}/translation"))==null?void 0:c.interpolation;break}case"rotation":{l=(p=J("/nodes/{}/rotation"))==null?void 0:p.interpolation;break}case"scale":{l=(f=J("/nodes/{}/scale"))==null?void 0:f.interpolation;break}case"weights":{l=(m=J("/nodes/{}/weights"))==null?void 0:m.interpolation;break}default:throw new Error(`${e}/target/path: Invalid value (${n.target.path})`)}if(!l)throw new Error(`${e}/target/path: Could not find interpolation properties for target path (${n.target.path})`);const u={object:o,info:l};return this._loadAnimationChannelFromTargetInfoAsync(e,t,s,n,u,i)}_loadAnimationChannelFromTargetInfoAsync(e,t,s,n,i,r){const o=this.parent.targetFps,a=1/o,h=_.Get(`${e}/sampler`,s.samplers,n.sampler);return this._loadAnimationSamplerAsync(`${t}/samplers/${n.sampler}`,h).then(l=>{let u=0;const c=i.object,p=i.info;for(const f of p){const m=f.getStride(c),w=l.input,V=l.output,D=new Array(w.length);let S=0;switch(l.interpolation){case"STEP":{for(let M=0;M<w.length;M++){const F=f.getValue(c,V,S,1);S+=m,D[M]={frame:w[M]*o,value:F,interpolation:1}}break}case"CUBICSPLINE":{for(let M=0;M<w.length;M++){const F=f.getValue(c,V,S,a);S+=m;const Z=f.getValue(c,V,S,1);S+=m;const Ae=f.getValue(c,V,S,a);S+=m,D[M]={frame:w[M]*o,inTangent:F,value:Z,outTangent:Ae}}break}case"LINEAR":{for(let M=0;M<w.length;M++){const F=f.getValue(c,V,S,1);S+=m,D[M]={frame:w[M]*o,value:F}}break}}if(S>0){const M=`${s.name||`animation${s.index}`}_channel${n.index}_${u}`,F=f.buildAnimations(c,M,o,D);for(const Z of F)u++,r(Z.babylonAnimatable,Z.babylonAnimation)}}})}_loadAnimationSamplerAsync(e,t){if(t._data)return t._data;const s=t.interpolation||"LINEAR";switch(s){case"STEP":case"LINEAR":case"CUBICSPLINE":break;default:throw new Error(`${e}/interpolation: Invalid value (${t.interpolation})`)}const n=_.Get(`${e}/input`,this._gltf.accessors,t.input),i=_.Get(`${e}/output`,this._gltf.accessors,t.output);return t._data=Promise.all([this._loadFloatAccessorAsync(`/accessors/${n.index}`,n),this._loadFloatAccessorAsync(`/accessors/${i.index}`,i)]).then(([r,o])=>({input:r,interpolation:s,output:o})),t._data}loadBufferAsync(e,t,s,n){const i=this._extensionsLoadBufferAsync(e,t,s,n);if(i)return i;if(!t._data)if(t.uri)t._data=this.loadUriAsync(`${e}/uri`,t,t.uri);else{if(!this._bin)throw new Error(`${e}: Uri is missing or the binary glTF is missing its binary chunk`);t._data=this._bin.readAsync(0,t.byteLength)}return t._data.then(r=>{try{return new Uint8Array(r.buffer,r.byteOffset+s,n)}catch(o){throw new Error(`${e}: ${o.message}`)}})}loadBufferViewAsync(e,t){const s=this._extensionsLoadBufferViewAsync(e,t);if(s)return s;if(t._data)return t._data;const n=_.Get(`${e}/buffer`,this._gltf.buffers,t.buffer);return t._data=this.loadBufferAsync(`/buffers/${n.index}`,n,t.byteOffset||0,t.byteLength),t._data}_loadAccessorAsync(e,t,s){if(t._data)return t._data;const n=x._GetNumComponents(e,t.type),i=n*g.GetTypeByteLength(t.componentType),r=n*t.count;if(t.bufferView==null)t._data=Promise.resolve(new s(r));else{const o=_.Get(`${e}/bufferView`,this._gltf.bufferViews,t.bufferView);t._data=this.loadBufferViewAsync(`/bufferViews/${o.index}`,o).then(a=>{if(t.componentType===5126&&!t.normalized&&(!o.byteStride||o.byteStride===i))return x._GetTypedArray(e,t.componentType,a,t.byteOffset,r);{const h=new s(r);return g.ForEach(a,t.byteOffset||0,o.byteStride||i,n,t.componentType,h.length,t.normalized||!1,(l,u)=>{h[u]=l}),h}})}if(t.sparse){const o=t.sparse;t._data=t._data.then(a=>{const h=a,l=_.Get(`${e}/sparse/indices/bufferView`,this._gltf.bufferViews,o.indices.bufferView),u=_.Get(`${e}/sparse/values/bufferView`,this._gltf.bufferViews,o.values.bufferView);return Promise.all([this.loadBufferViewAsync(`/bufferViews/${l.index}`,l),this.loadBufferViewAsync(`/bufferViews/${u.index}`,u)]).then(([c,p])=>{const f=x._GetTypedArray(`${e}/sparse/indices`,o.indices.componentType,c,o.indices.byteOffset,o.count),m=n*o.count;let w;if(t.componentType===5126&&!t.normalized)w=x._GetTypedArray(`${e}/sparse/values`,t.componentType,p,o.values.byteOffset,m);else{const D=x._GetTypedArray(`${e}/sparse/values`,t.componentType,p,o.values.byteOffset,m);w=new s(m),g.ForEach(D,0,i,n,t.componentType,w.length,t.normalized||!1,(S,M)=>{w[M]=S})}let V=0;for(let D=0;D<f.length;D++){let S=f[D]*n;for(let M=0;M<n;M++)h[S++]=w[V++]}return h})})}return t._data}_loadFloatAccessorAsync(e,t){return this._loadAccessorAsync(e,t,Float32Array)}_loadIndicesAccessorAsync(e,t){if(t.type!=="SCALAR")throw new Error(`${e}/type: Invalid value ${t.type}`);if(t.componentType!==5121&&t.componentType!==5123&&t.componentType!==5125)throw new Error(`${e}/componentType: Invalid value ${t.componentType}`);if(t._data)return t._data;if(t.sparse){const s=x._GetTypedArrayConstructor(`${e}/componentType`,t.componentType);t._data=this._loadAccessorAsync(e,t,s)}else{const s=_.Get(`${e}/bufferView`,this._gltf.bufferViews,t.bufferView);t._data=this.loadBufferViewAsync(`/bufferViews/${s.index}`,s).then(n=>x._GetTypedArray(e,t.componentType,n,t.byteOffset,t.count))}return t._data}_loadVertexBufferViewAsync(e){if(e._babylonBuffer)return e._babylonBuffer;const t=this._babylonScene.getEngine();return e._babylonBuffer=this.loadBufferViewAsync(`/bufferViews/${e.index}`,e).then(s=>new We(t,s,!1)),e._babylonBuffer}_loadVertexAccessorAsync(e,t,s){var i;if((i=t._babylonVertexBuffer)!=null&&i[s])return t._babylonVertexBuffer[s];t._babylonVertexBuffer||(t._babylonVertexBuffer={});const n=this._babylonScene.getEngine();if(t.sparse||t.bufferView==null)t._babylonVertexBuffer[s]=this._loadFloatAccessorAsync(e,t).then(r=>new g(n,r,s,!1));else{const r=_.Get(`${e}/bufferView`,this._gltf.bufferViews,t.bufferView);t._babylonVertexBuffer[s]=this._loadVertexBufferViewAsync(r).then(o=>{const a=x._GetNumComponents(e,t.type);return new g(n,o,s,!1,void 0,r.byteStride,void 0,t.byteOffset,a,t.componentType,t.normalized,!0,void 0,!0)})}return t._babylonVertexBuffer[s]}_loadMaterialMetallicRoughnessPropertiesAsync(e,t,s){if(!(s instanceof $))throw new Error(`${e}: Material type not supported`);const n=new Array;return t&&(t.baseColorFactor?(s.albedoColor=H.FromArray(t.baseColorFactor),s.alpha=t.baseColorFactor[3]):s.albedoColor=H.White(),s.metallic=t.metallicFactor==null?1:t.metallicFactor,s.roughness=t.roughnessFactor==null?1:t.roughnessFactor,t.baseColorTexture&&n.push(this.loadTextureInfoAsync(`${e}/baseColorTexture`,t.baseColorTexture,i=>{i.name=`${s.name} (Base Color)`,s.albedoTexture=i})),t.metallicRoughnessTexture&&(t.metallicRoughnessTexture.nonColorData=!0,n.push(this.loadTextureInfoAsync(`${e}/metallicRoughnessTexture`,t.metallicRoughnessTexture,i=>{i.name=`${s.name} (Metallic Roughness)`,s.metallicTexture=i})),s.useMetallnessFromMetallicTextureBlue=!0,s.useRoughnessFromMetallicTextureGreen=!0,s.useRoughnessFromMetallicTextureAlpha=!1)),Promise.all(n).then(()=>{})}_loadMaterialAsync(e,t,s,n,i=()=>{}){const r=this._extensionsLoadMaterialAsync(e,t,s,n,i);if(r)return r;t._data=t._data||{};let o=t._data[n];if(!o){this.logOpen(`${e} ${t.name||""}`);const a=this.createMaterial(e,t,n);o={babylonMaterial:a,babylonMeshes:[],promise:this.loadMaterialPropertiesAsync(e,t,a)},t._data[n]=o,x.AddPointerMetadata(a,e),this._parent.onMaterialLoadedObservable.notifyObservers(a),this.logClose()}return s&&(o.babylonMeshes.push(s),s.onDisposeObservable.addOnce(()=>{const a=o.babylonMeshes.indexOf(s);a!==-1&&o.babylonMeshes.splice(a,1)})),i(o.babylonMaterial),o.promise.then(()=>o.babylonMaterial)}_createDefaultMaterial(e,t){this._babylonScene._blockEntityCollection=!!this._assetContainer;const s=new $(e,this._babylonScene);return s._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,s.fillMode=t,s.enableSpecularAntiAliasing=!0,s.useRadianceOverAlpha=!this._parent.transparencyAsCoverage,s.useSpecularOverAlpha=!this._parent.transparencyAsCoverage,s.transparencyMode=$.PBRMATERIAL_OPAQUE,s.metallic=1,s.roughness=1,s}createMaterial(e,t,s){const n=this._extensionsCreateMaterial(e,t,s);if(n)return n;const i=t.name||`material${t.index}`;return this._createDefaultMaterial(i,s)}loadMaterialPropertiesAsync(e,t,s){const n=this._extensionsLoadMaterialPropertiesAsync(e,t,s);if(n)return n;const i=new Array;return i.push(this.loadMaterialBasePropertiesAsync(e,t,s)),t.pbrMetallicRoughness&&i.push(this._loadMaterialMetallicRoughnessPropertiesAsync(`${e}/pbrMetallicRoughness`,t.pbrMetallicRoughness,s)),this.loadMaterialAlphaProperties(e,t,s),Promise.all(i).then(()=>{})}loadMaterialBasePropertiesAsync(e,t,s){if(!(s instanceof $))throw new Error(`${e}: Material type not supported`);const n=new Array;return s.emissiveColor=t.emissiveFactor?H.FromArray(t.emissiveFactor):new H(0,0,0),t.doubleSided&&(s.backFaceCulling=!1,s.twoSidedLighting=!0),t.normalTexture&&(t.normalTexture.nonColorData=!0,n.push(this.loadTextureInfoAsync(`${e}/normalTexture`,t.normalTexture,i=>{i.name=`${s.name} (Normal)`,s.bumpTexture=i})),s.invertNormalMapX=!this._babylonScene.useRightHandedSystem,s.invertNormalMapY=this._babylonScene.useRightHandedSystem,t.normalTexture.scale!=null&&s.bumpTexture&&(s.bumpTexture.level=t.normalTexture.scale),s.forceIrradianceInFragment=!0),t.occlusionTexture&&(t.occlusionTexture.nonColorData=!0,n.push(this.loadTextureInfoAsync(`${e}/occlusionTexture`,t.occlusionTexture,i=>{i.name=`${s.name} (Occlusion)`,s.ambientTexture=i})),s.useAmbientInGrayScale=!0,t.occlusionTexture.strength!=null&&(s.ambientTextureStrength=t.occlusionTexture.strength)),t.emissiveTexture&&n.push(this.loadTextureInfoAsync(`${e}/emissiveTexture`,t.emissiveTexture,i=>{i.name=`${s.name} (Emissive)`,s.emissiveTexture=i})),Promise.all(n).then(()=>{})}loadMaterialAlphaProperties(e,t,s){if(!(s instanceof $))throw new Error(`${e}: Material type not supported`);switch(t.alphaMode||"OPAQUE"){case"OPAQUE":{s.transparencyMode=$.PBRMATERIAL_OPAQUE,s.alpha=1;break}case"MASK":{s.transparencyMode=$.PBRMATERIAL_ALPHATEST,s.alphaCutOff=t.alphaCutoff==null?.5:t.alphaCutoff,s.albedoTexture&&(s.albedoTexture.hasAlpha=!0);break}case"BLEND":{s.transparencyMode=$.PBRMATERIAL_ALPHABLEND,s.albedoTexture&&(s.albedoTexture.hasAlpha=!0,s.useAlphaFromAlbedoTexture=!0);break}default:throw new Error(`${e}/alphaMode: Invalid value (${t.alphaMode})`)}}loadTextureInfoAsync(e,t,s=()=>{}){const n=this._extensionsLoadTextureInfoAsync(e,t,s);if(n)return n;if(this.logOpen(`${e}`),t.texCoord>=6)throw new Error(`${e}/texCoord: Invalid value (${t.texCoord})`);const i=_.Get(`${e}/index`,this._gltf.textures,t.index);i._textureInfo=t;const r=this._loadTextureAsync(`/textures/${t.index}`,i,o=>{o.coordinatesIndex=t.texCoord||0,x.AddPointerMetadata(o,e),this._parent.onTextureLoadedObservable.notifyObservers(o),s(o)});return this.logClose(),r}_loadTextureAsync(e,t,s=()=>{}){const n=this._extensionsLoadTextureAsync(e,t,s);if(n)return n;this.logOpen(`${e} ${t.name||""}`);const i=t.sampler==null?x.DefaultSampler:_.Get(`${e}/sampler`,this._gltf.samplers,t.sampler),r=_.Get(`${e}/source`,this._gltf.images,t.source),o=this._createTextureAsync(e,i,r,s,void 0,!t._textureInfo.nonColorData);return this.logClose(),o}_createTextureAsync(e,t,s,n=()=>{},i,r){const o=this._loadSampler(`/samplers/${t.index}`,t),a=new Array,h=new Xe;this._babylonScene._blockEntityCollection=!!this._assetContainer;const l={noMipmap:o.noMipMaps,invertY:!1,samplingMode:o.samplingMode,onLoad:()=>{this._disposed||h.resolve()},onError:(c,p)=>{this._disposed||h.reject(new Error(`${e}: ${p&&p.message?p.message:c||"Failed to load texture"}`))},mimeType:s.mimeType??Ye(s.uri??""),loaderOptions:i,useSRGBBuffer:!!r&&this._parent.useSRGBBuffers},u=new C(null,this._babylonScene,l);return u._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,a.push(h.promise),a.push(this.loadImageAsync(`/images/${s.index}`,s).then(c=>{const p=s.uri||`${this._fileName}#image${s.index}`,f=`data:${this._uniqueRootUrl}${p}`;u.updateURL(f,c);const m=u.getInternalTexture();m&&(m.label=s.name)})),u.wrapU=o.wrapU,u.wrapV=o.wrapV,n(u),this._parent.useGltfTextureNames&&(u.name=s.name||s.uri||`image${s.index}`),Promise.all(a).then(()=>u)}_loadSampler(e,t){return t._data||(t._data={noMipMaps:t.minFilter===9728||t.minFilter===9729,samplingMode:x._GetTextureSamplingMode(e,t),wrapU:x._GetTextureWrapMode(`${e}/wrapS`,t.wrapS),wrapV:x._GetTextureWrapMode(`${e}/wrapT`,t.wrapT)}),t._data}loadImageAsync(e,t){if(!t._data){if(this.logOpen(`${e} ${t.name||""}`),t.uri)t._data=this.loadUriAsync(`${e}/uri`,t,t.uri);else{const s=_.Get(`${e}/bufferView`,this._gltf.bufferViews,t.bufferView);t._data=this.loadBufferViewAsync(`/bufferViews/${s.index}`,s)}this.logClose()}return t._data}loadUriAsync(e,t,s){const n=this._extensionsLoadUriAsync(e,t,s);if(n)return n;if(!x._ValidateUri(s))throw new Error(`${e}: '${s}' is invalid`);if(Ze(s)){const i=new Uint8Array(ye(s));return this.log(`${e}: Decoded ${s.substring(0,64)}... (${i.length} bytes)`),Promise.resolve(i)}return this.log(`${e}: Loading ${s}`),this._parent.preprocessUrlAsync(this._rootUrl+s).then(i=>new Promise((r,o)=>{this._parent._loadFile(this._babylonScene,i,a=>{this._disposed||(this.log(`${e}: Loaded ${s} (${a.byteLength} bytes)`),r(new Uint8Array(a)))},!0,a=>{o(new Ke(`${e}: Failed to load '${s}'${a?": "+a.status+" "+a.statusText:""}`,a))})}))}static AddPointerMetadata(e,t){e.metadata=e.metadata||{};const s=e._internalMetadata=e._internalMetadata||{},n=s.gltf=s.gltf||{};(n.pointers=n.pointers||[]).push(t)}static _GetTextureWrapMode(e,t){switch(t=t??10497,t){case 33071:return C.CLAMP_ADDRESSMODE;case 33648:return C.MIRROR_ADDRESSMODE;case 10497:return C.WRAP_ADDRESSMODE;default:return T.Warn(`${e}: Invalid value (${t})`),C.WRAP_ADDRESSMODE}}static _GetTextureSamplingMode(e,t){const s=t.magFilter==null?9729:t.magFilter,n=t.minFilter==null?9987:t.minFilter;if(s===9729)switch(n){case 9728:return C.LINEAR_NEAREST;case 9729:return C.LINEAR_LINEAR;case 9984:return C.LINEAR_NEAREST_MIPNEAREST;case 9985:return C.LINEAR_LINEAR_MIPNEAREST;case 9986:return C.LINEAR_NEAREST_MIPLINEAR;case 9987:return C.LINEAR_LINEAR_MIPLINEAR;default:return T.Warn(`${e}/minFilter: Invalid value (${n})`),C.LINEAR_LINEAR_MIPLINEAR}else switch(s!==9728&&T.Warn(`${e}/magFilter: Invalid value (${s})`),n){case 9728:return C.NEAREST_NEAREST;case 9729:return C.NEAREST_LINEAR;case 9984:return C.NEAREST_NEAREST_MIPNEAREST;case 9985:return C.NEAREST_LINEAR_MIPNEAREST;case 9986:return C.NEAREST_NEAREST_MIPLINEAR;case 9987:return C.NEAREST_LINEAR_MIPLINEAR;default:return T.Warn(`${e}/minFilter: Invalid value (${n})`),C.NEAREST_NEAREST_MIPNEAREST}}static _GetTypedArrayConstructor(e,t){try{return qe(t)}catch(s){throw new Error(`${e}: ${s.message}`)}}static _GetTypedArray(e,t,s,n,i){const r=s.buffer;n=s.byteOffset+(n||0);const o=x._GetTypedArrayConstructor(`${e}/componentType`,t),a=g.GetTypeByteLength(t);return n%a!==0?(T.Warn(`${e}: Copying buffer as byte offset (${n}) is not a multiple of component type byte length (${a})`),new o(r.slice(n,n+i*a),0)):new o(r,n,i)}static _GetNumComponents(e,t){switch(t){case"SCALAR":return 1;case"VEC2":return 2;case"VEC3":return 3;case"VEC4":return 4;case"MAT2":return 4;case"MAT3":return 9;case"MAT4":return 16}throw new Error(`${e}: Invalid type (${t})`)}static _ValidateUri(e){return R.IsBase64(e)||e.indexOf("..")===-1}static _GetDrawMode(e,t){switch(t==null&&(t=4),t){case 0:return N.PointListDrawMode;case 1:return N.LineListDrawMode;case 2:return N.LineLoopDrawMode;case 3:return N.LineStripDrawMode;case 4:return N.TriangleFillMode;case 5:return N.TriangleStripDrawMode;case 6:return N.TriangleFanDrawMode}throw new Error(`${e}: Invalid mesh primitive mode (${t})`)}_compileMaterialsAsync(){this._parent._startPerformanceCounter("Compile materials");const e=new Array;if(this._gltf.materials){for(const t of this._gltf.materials)if(t._data)for(const s in t._data){const n=t._data[s];for(const i of n.babylonMeshes){i.computeWorldMatrix(!0);const r=n.babylonMaterial;e.push(r.forceCompilationAsync(i)),e.push(r.forceCompilationAsync(i,{useInstances:!0})),this._parent.useClipPlane&&(e.push(r.forceCompilationAsync(i,{clipPlane:!0})),e.push(r.forceCompilationAsync(i,{clipPlane:!0,useInstances:!0})))}}}return Promise.all(e).then(()=>{this._parent._endPerformanceCounter("Compile materials")})}_compileShadowGeneratorsAsync(){this._parent._startPerformanceCounter("Compile shadow generators");const e=new Array,t=this._babylonScene.lights;for(const s of t){const n=s.getShadowGenerator();n&&e.push(n.forceCompilationAsync())}return Promise.all(e).then(()=>{this._parent._endPerformanceCounter("Compile shadow generators")})}_forEachExtensions(e){for(const t of this._extensions)t.enabled&&e(t)}_applyExtensions(e,t,s){for(const n of this._extensions)if(n.enabled){const i=`${n.name}.${t}`,r=e;r._activeLoaderExtensionFunctions=r._activeLoaderExtensionFunctions||{};const o=r._activeLoaderExtensionFunctions;if(!o[i]){o[i]=!0;try{const a=s(n);if(a)return a}finally{delete o[i]}}}return null}_extensionsOnLoading(){this._forEachExtensions(e=>e.onLoading&&e.onLoading())}_extensionsOnReady(){this._forEachExtensions(e=>e.onReady&&e.onReady())}_extensionsLoadSceneAsync(e,t){return this._applyExtensions(t,"loadScene",s=>s.loadSceneAsync&&s.loadSceneAsync(e,t))}_extensionsLoadNodeAsync(e,t,s){return this._applyExtensions(t,"loadNode",n=>n.loadNodeAsync&&n.loadNodeAsync(e,t,s))}_extensionsLoadCameraAsync(e,t,s){return this._applyExtensions(t,"loadCamera",n=>n.loadCameraAsync&&n.loadCameraAsync(e,t,s))}_extensionsLoadVertexDataAsync(e,t,s){return this._applyExtensions(t,"loadVertexData",n=>n._loadVertexDataAsync&&n._loadVertexDataAsync(e,t,s))}_extensionsLoadMeshPrimitiveAsync(e,t,s,n,i,r){return this._applyExtensions(i,"loadMeshPrimitive",o=>o._loadMeshPrimitiveAsync&&o._loadMeshPrimitiveAsync(e,t,s,n,i,r))}_extensionsLoadMaterialAsync(e,t,s,n,i){return this._applyExtensions(t,"loadMaterial",r=>r._loadMaterialAsync&&r._loadMaterialAsync(e,t,s,n,i))}_extensionsCreateMaterial(e,t,s){return this._applyExtensions(t,"createMaterial",n=>n.createMaterial&&n.createMaterial(e,t,s))}_extensionsLoadMaterialPropertiesAsync(e,t,s){return this._applyExtensions(t,"loadMaterialProperties",n=>n.loadMaterialPropertiesAsync&&n.loadMaterialPropertiesAsync(e,t,s))}_extensionsLoadTextureInfoAsync(e,t,s){return this._applyExtensions(t,"loadTextureInfo",n=>n.loadTextureInfoAsync&&n.loadTextureInfoAsync(e,t,s))}_extensionsLoadTextureAsync(e,t,s){return this._applyExtensions(t,"loadTexture",n=>n._loadTextureAsync&&n._loadTextureAsync(e,t,s))}_extensionsLoadAnimationAsync(e,t){return this._applyExtensions(t,"loadAnimation",s=>s.loadAnimationAsync&&s.loadAnimationAsync(e,t))}_extensionsLoadAnimationChannelAsync(e,t,s,n,i){return this._applyExtensions(s,"loadAnimationChannel",r=>r._loadAnimationChannelAsync&&r._loadAnimationChannelAsync(e,t,s,n,i))}_extensionsLoadSkinAsync(e,t,s){return this._applyExtensions(s,"loadSkin",n=>n._loadSkinAsync&&n._loadSkinAsync(e,t,s))}_extensionsLoadUriAsync(e,t,s){return this._applyExtensions(t,"loadUri",n=>n._loadUriAsync&&n._loadUriAsync(e,t,s))}_extensionsLoadBufferViewAsync(e,t){return this._applyExtensions(t,"loadBufferView",s=>s.loadBufferViewAsync&&s.loadBufferViewAsync(e,t))}_extensionsLoadBufferAsync(e,t,s,n){return this._applyExtensions(t,"loadBuffer",i=>i.loadBufferAsync&&i.loadBufferAsync(e,t,s,n))}static LoadExtensionAsync(e,t,s,n){if(!t.extensions)return null;const r=t.extensions[s];return r?n(`${e}/extensions/${s}`,r):null}static LoadExtraAsync(e,t,s,n){if(!t.extras)return null;const r=t.extras[s];return r?n(`${e}/extras/${s}`,r):null}isExtensionUsed(e){return!!this._gltf.extensionsUsed&&this._gltf.extensionsUsed.indexOf(e)!==-1}logOpen(e){this._parent._logOpen(e)}logClose(){this._parent._logClose()}log(e){this._parent._log(e)}startPerformanceCounter(e){this._parent._startPerformanceCounter(e)}endPerformanceCounter(e){this._parent._endPerformanceCounter(e)}}x.DefaultSampler={index:-1};E._CreateGLTF2Loader=d=>new x(d);export{_ as ArrayItem,E as GLTFFileLoader,x as GLTFLoader,et as LoadBoundingInfoFromPositionAccessor};
//# sourceMappingURL=glTFLoader.BetPWe9U.js.map
