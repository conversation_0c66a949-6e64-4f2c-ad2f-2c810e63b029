{"version": 3, "file": "iblCdfy.fragment-D5tDJ8YV.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Shaders/iblCdfy.fragment.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nimport \"./ShadersInclude/helperFunctions.js\";\nconst name = \"iblCdfyPixelShader\";\nconst shader = `precision highp sampler2D;precision highp samplerCube;\n#include<helperFunctions>\n#define PI 3.1415927\nvarying vec2 vUV;\n#ifdef IBL_USE_CUBE_MAP\nuniform samplerCube iblSource;\n#else\nuniform sampler2D iblSource;\n#endif\nuniform int iblHeight;\n#ifdef IBL_USE_CUBE_MAP\nfloat fetchCube(vec2 uv) {vec3 direction=equirectangularToCubemapDirection(uv);return sin(PI*uv.y)*dot(textureCubeLodEXT(iblSource,direction,0.0).rgb,LuminanceEncodeApprox);}\n#else\nfloat fetchPanoramic(ivec2 Coords,float envmapHeight) {return sin(PI*(float(Coords.y)+0.5)/envmapHeight) *\ndot(texelFetch(iblSource,Coords,0).rgb,LuminanceEncodeApprox);}\n#endif\nvoid main(void) {ivec2 coords=ivec2(gl_FragCoord.x,gl_FragCoord.y);float cdfy=0.0;for (int y=1; y<=coords.y; y++) {\n#ifdef IBL_USE_CUBE_MAP\nvec2 uv=vec2(vUV.x,(float(y-1)+0.5)/float(iblHeight));cdfy+=fetchCube(uv);\n#else\ncdfy+=fetchPanoramic(ivec2(coords.x,y-1),float(iblHeight));\n#endif\n}\ngl_FragColor=vec4(cdfy,0.0,0.0,1.0);}`;\n// Sideeffect\nif (!ShaderStore.ShadersStore[name]) {\n    ShaderStore.ShadersStore[name] = shader;\n}\n/** @internal */\nexport const iblCdfyPixelShader = { name, shader };\n//# sourceMappingURL=iblCdfy.fragment.js.map"], "names": ["name", "shader", "ShaderStore", "iblCdfyPixelShader"], "mappings": "qIAGA,MAAMA,EAAO,qBACPC,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uCAyBVC,EAAY,aAAaF,CAAI,IAC9BE,EAAY,aAAaF,CAAI,EAAIC,GAGzB,MAACE,EAAqB,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}