import{GLTFLoader as S,ArrayItem as L}from"./glTFLoader.BetPWe9U.js";import{ab as k,an as C,ao as I}from"./index.BoI39RQH.js";const r="KHR_materials_variants";class s{constructor(t){this.name=r,this._loader=t,this.enabled=this._loader.isExtensionUsed(r)}dispose(){this._loader=null}static GetAvailableVariants(t){const a=this._GetExtensionMetadata(t);return a?Object.keys(a.variants):[]}getAvailableVariants(t){return s.GetAvailableVariants(t)}static SelectVariant(t,a){const e=this._GetExtensionMetadata(t);if(!e)throw new Error(`Cannot select variant on a glTF mesh that does not have the ${r} extension`);const l=h=>{const f=e.variants[h];if(f)for(const m of f)m.mesh.material=m.material};if(a instanceof Array)for(const h of a)l(h);else l(a);e.lastSelected=a}selectVariant(t,a){s.SelectVariant(t,a)}static Reset(t){const a=this._GetExtensionMetadata(t);if(!a)throw new Error(`Cannot reset on a glTF mesh that does not have the ${r} extension`);for(const e of a.original)e.mesh.material=e.material;a.lastSelected=null}reset(t){s.Reset(t)}static GetLastSelectedVariant(t){const a=this._GetExtensionMetadata(t);if(!a)throw new Error(`Cannot get the last selected variant on a glTF mesh that does not have the ${r} extension`);return a.lastSelected}getLastSelectedVariant(t){return s.GetLastSelectedVariant(t)}static _GetExtensionMetadata(t){var a,e;return((e=(a=t==null?void 0:t._internalMetadata)==null?void 0:a.gltf)==null?void 0:e[r])||null}onLoading(){const t=this._loader.gltf.extensions;if(t&&t[this.name]){const a=t[this.name];this._variants=a.variants}}onReady(){var a;const t=this._loader.rootBabylonMesh;if(t){const e=this._loader.parent.extensionOptions[r];e!=null&&e.defaultVariant&&s.SelectVariant(t,e.defaultVariant),(a=e==null?void 0:e.onLoaded)==null||a.call(e,{get variants(){return s.GetAvailableVariants(t)},get selectedVariant(){const l=s.GetLastSelectedVariant(t);return l?Array.isArray(l)?l[0]:l:s.GetAvailableVariants(t)[0]},set selectedVariant(l){s.SelectVariant(t,l)}})}}_loadMeshPrimitiveAsync(t,a,e,l,h,f){return S.LoadExtensionAsync(t,h,this.name,(m,V)=>{const p=new Array;return p.push(this._loader._loadMeshPrimitiveAsync(t,a,e,l,h,o=>{if(f(o),o instanceof k){const $=S._GetDrawMode(t,h.mode),c=this._loader.rootBabylonMesh,E=c?c._internalMetadata=c._internalMetadata||{}:{},A=E.gltf=E.gltf||{},g=A[r]=A[r]||{lastSelected:null,original:[],variants:{}};g.original.push({mesh:o,material:o.material});for(let u=0;u<V.mappings.length;++u){const M=V.mappings[u],F=L.Get(`${m}/mappings/${u}/material`,this._loader.gltf.materials,M.material);p.push(this._loader._loadMaterialAsync(`#/materials/${M.material}`,F,o,$,T=>{for(let v=0;v<M.variants.length;++v){const w=M.variants[v],x=L.Get(`/extensions/${r}/variants/${w}`,this._variants,w);g.variants[x.name]=g.variants[x.name]||[],g.variants[x.name].push({mesh:o,material:T}),o.onClonedObservable.add(O=>{const G=O;let d=null,i=G;do{if(i=i.parent,!i)return;d=s._GetExtensionMetadata(i)}while(d===null);if(c&&d===s._GetExtensionMetadata(c)){i._internalMetadata={};for(const n in c._internalMetadata)i._internalMetadata[n]=c._internalMetadata[n];i._internalMetadata.gltf=[];for(const n in c._internalMetadata.gltf)i._internalMetadata.gltf[n]=c._internalMetadata.gltf[n];i._internalMetadata.gltf[r]={lastSelected:null,original:[],variants:{}};for(const n of d.original)i._internalMetadata.gltf[r].original.push({mesh:n.mesh,material:n.material});for(const n in d.variants)if(Object.prototype.hasOwnProperty.call(d.variants,n)){i._internalMetadata.gltf[r].variants[n]=[];for(const y of d.variants[n])i._internalMetadata.gltf[r].variants[n].push({mesh:y.mesh,material:y.material})}d=i._internalMetadata.gltf[r]}for(const n of d.original)n.mesh===o&&(n.mesh=G);for(const n of d.variants[x.name])n.mesh===o&&(n.mesh=G)})}}))}}})),Promise.all(p).then(([o])=>o)})}}C(r);I(r,!0,_=>new s(_));export{s as KHR_materials_variants};
//# sourceMappingURL=KHR_materials_variants.Cryk6ix7.js.map
