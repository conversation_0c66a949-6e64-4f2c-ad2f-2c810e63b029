/* empty css                                                        */import{B as Ie}from"./BlockTitle-Ct-h8ev5.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import{C as Je}from"./Check-CEkiXcyC.js";import{C as Oe}from"./Copy-CxQ9EyK2.js";import{S as Pe}from"./Send-DyoOovnk.js";import{S as Qe}from"./Square-oAGqOwsh.js";import"./index-B7J2Z2jS.js";import{f as Re}from"./index-CEGzm7H5.js";/* empty css                                              */const{SvelteComponent:Ve,action_destroyer:We,add_render_callback:Xe,append:j,attr:i,binding_callbacks:R,bubble:D,check_outros:N,create_component:A,create_in_transition:Ze,destroy_component:G,detach:T,element:E,empty:be,flush:k,group_outros:U,init:xe,insert:C,is_function:$e,listen:g,mount_component:I,noop:Y,run_all:W,safe_not_equal:et,set_data:le,set_input_value:K,space:V,text:ne,toggle_class:q,transition_in:v,transition_out:H}=window.__gradio__svelte__internal,{beforeUpdate:tt,afterUpdate:lt,createEventDispatcher:nt,tick:fe}=window.__gradio__svelte__internal;function _e(t){let e,l,n,s;const a=[ot,it],c=[];function m(f,b){return f[19]?0:1}return e=m(t),l=c[e]=a[e](t),{c(){l.c(),n=be()},m(f,b){c[e].m(f,b),C(f,n,b),s=!0},p(f,b){let u=e;e=m(f),e===u?c[e].p(f,b):(U(),H(c[u],1,1,()=>{c[u]=null}),N(),l=c[e],l?l.p(f,b):(l=c[e]=a[e](f),l.c()),v(l,1),l.m(n.parentNode,n))},i(f){s||(v(l),s=!0)},o(f){H(l),s=!1},d(f){f&&T(n),c[e].d(f)}}}function it(t){let e,l,n,s,a;return l=new Oe({}),{c(){e=E("button"),A(l.$$.fragment),i(e,"class","copy-button svelte-173056l"),i(e,"aria-label","Copy"),i(e,"aria-roledescription","Copy text")},m(c,m){C(c,e,m),I(l,e,null),n=!0,s||(a=g(e,"click",t[21]),s=!0)},p:Y,i(c){n||(v(l.$$.fragment,c),n=!0)},o(c){H(l.$$.fragment,c),n=!1},d(c){c&&T(e),G(l),s=!1,a()}}}function ot(t){let e,l,n,s;return l=new Je({}),{c(){e=E("button"),A(l.$$.fragment),i(e,"class","copy-button svelte-173056l"),i(e,"aria-label","Copied"),i(e,"aria-roledescription","Text copied")},m(a,c){C(a,e,c),I(l,e,null),s=!0},p:Y,i(a){s||(v(l.$$.fragment,a),a&&(n||Xe(()=>{n=Ze(e,Re,{duration:300}),n.start()})),s=!0)},o(a){H(l.$$.fragment,a),s=!1},d(a){a&&T(e),G(l)}}}function ut(t){let e;return{c(){e=ne(t[3])},m(l,n){C(l,e,n)},p(l,n){n[0]&8&&le(e,l[3])},d(l){l&&T(e)}}}function at(t){let e,l,n,s,a,c,m,f,b,u,_,d,p;return{c(){e=E("textarea"),i(e,"data-testid","textbox"),i(e,"dir",l=t[12]?"rtl":"ltr"),i(e,"placeholder",t[2]),i(e,"rows",t[1]),e.disabled=t[5],e.autofocus=t[13],i(e,"maxlength",t[15]),i(e,"style",n=t[14]?"text-align: "+t[14]:""),i(e,"autocapitalize",s=t[16]?.autocapitalize),i(e,"autocorrect",a=t[16]?.autocorrect),i(e,"spellcheck",c=t[16]?.spellcheck),i(e,"autocomplete",m=t[16]?.autocomplete),i(e,"tabindex",f=t[16]?.tabindex),i(e,"enterkeyhint",b=t[16]?.enterkeyhint),i(e,"lang",u=t[16]?.lang),i(e,"class","svelte-173056l"),q(e,"no-label",!t[6]&&(t[10]||t[11]))},m(r,h){C(r,e,h),K(e,t[0]),t[46](e),t[13]&&e.focus(),d||(p=[We(_=t[27].call(null,e,t[0])),g(e,"input",t[45]),g(e,"keypress",t[23]),g(e,"blur",t[37]),g(e,"select",t[22]),g(e,"focus",t[38]),g(e,"scroll",t[24])],d=!0)},p(r,h){h[0]&4096&&l!==(l=r[12]?"rtl":"ltr")&&i(e,"dir",l),h[0]&4&&i(e,"placeholder",r[2]),h[0]&2&&i(e,"rows",r[1]),h[0]&32&&(e.disabled=r[5]),h[0]&8192&&(e.autofocus=r[13]),h[0]&32768&&i(e,"maxlength",r[15]),h[0]&16384&&n!==(n=r[14]?"text-align: "+r[14]:"")&&i(e,"style",n),h[0]&65536&&s!==(s=r[16]?.autocapitalize)&&i(e,"autocapitalize",s),h[0]&65536&&a!==(a=r[16]?.autocorrect)&&i(e,"autocorrect",a),h[0]&65536&&c!==(c=r[16]?.spellcheck)&&i(e,"spellcheck",c),h[0]&65536&&m!==(m=r[16]?.autocomplete)&&i(e,"autocomplete",m),h[0]&65536&&f!==(f=r[16]?.tabindex)&&i(e,"tabindex",f),h[0]&65536&&b!==(b=r[16]?.enterkeyhint)&&i(e,"enterkeyhint",b),h[0]&65536&&u!==(u=r[16]?.lang)&&i(e,"lang",u),_&&$e(_.update)&&h[0]&1&&_.update.call(null,r[0]),h[0]&1&&K(e,r[0]),h[0]&3136&&q(e,"no-label",!r[6]&&(r[10]||r[11]))},d(r){r&&T(e),t[46](null),d=!1,W(p)}}}function st(t){let e;function l(a,c){if(a[8]==="text")return _t;if(a[8]==="password")return ft;if(a[8]==="email")return rt}let n=l(t),s=n&&n(t);return{c(){s&&s.c(),e=be()},m(a,c){s&&s.m(a,c),C(a,e,c)},p(a,c){n===(n=l(a))&&s?s.p(a,c):(s&&s.d(1),s=n&&n(a),s&&(s.c(),s.m(e.parentNode,e)))},d(a){a&&T(e),s&&s.d(a)}}}function rt(t){let e,l,n,s,a,c,m,f,b;return{c(){e=E("input"),i(e,"data-testid","textbox"),i(e,"type","email"),i(e,"class","scroll-hide svelte-173056l"),i(e,"placeholder",t[2]),e.disabled=t[5],e.autofocus=t[13],i(e,"maxlength",t[15]),i(e,"autocomplete","email"),i(e,"autocapitalize",l=t[16]?.autocapitalize),i(e,"autocorrect",n=t[16]?.autocorrect),i(e,"spellcheck",s=t[16]?.spellcheck),i(e,"tabindex",a=t[16]?.tabindex),i(e,"enterkeyhint",c=t[16]?.enterkeyhint),i(e,"lang",m=t[16]?.lang)},m(u,_){C(u,e,_),K(e,t[0]),t[44](e),t[13]&&e.focus(),f||(b=[g(e,"input",t[43]),g(e,"keypress",t[23]),g(e,"blur",t[35]),g(e,"select",t[22]),g(e,"focus",t[36])],f=!0)},p(u,_){_[0]&4&&i(e,"placeholder",u[2]),_[0]&32&&(e.disabled=u[5]),_[0]&8192&&(e.autofocus=u[13]),_[0]&32768&&i(e,"maxlength",u[15]),_[0]&65536&&l!==(l=u[16]?.autocapitalize)&&i(e,"autocapitalize",l),_[0]&65536&&n!==(n=u[16]?.autocorrect)&&i(e,"autocorrect",n),_[0]&65536&&s!==(s=u[16]?.spellcheck)&&i(e,"spellcheck",s),_[0]&65536&&a!==(a=u[16]?.tabindex)&&i(e,"tabindex",a),_[0]&65536&&c!==(c=u[16]?.enterkeyhint)&&i(e,"enterkeyhint",c),_[0]&65536&&m!==(m=u[16]?.lang)&&i(e,"lang",m),_[0]&1&&e.value!==u[0]&&K(e,u[0])},d(u){u&&T(e),t[44](null),f=!1,W(b)}}}function ft(t){let e,l,n,s,a,c,m,f,b;return{c(){e=E("input"),i(e,"data-testid","password"),i(e,"type","password"),i(e,"class","scroll-hide svelte-173056l"),i(e,"placeholder",t[2]),e.disabled=t[5],e.autofocus=t[13],i(e,"maxlength",t[15]),i(e,"autocomplete",""),i(e,"autocapitalize",l=t[16]?.autocapitalize),i(e,"autocorrect",n=t[16]?.autocorrect),i(e,"spellcheck",s=t[16]?.spellcheck),i(e,"tabindex",a=t[16]?.tabindex),i(e,"enterkeyhint",c=t[16]?.enterkeyhint),i(e,"lang",m=t[16]?.lang)},m(u,_){C(u,e,_),K(e,t[0]),t[42](e),t[13]&&e.focus(),f||(b=[g(e,"input",t[41]),g(e,"keypress",t[23]),g(e,"blur",t[33]),g(e,"select",t[22]),g(e,"focus",t[34])],f=!0)},p(u,_){_[0]&4&&i(e,"placeholder",u[2]),_[0]&32&&(e.disabled=u[5]),_[0]&8192&&(e.autofocus=u[13]),_[0]&32768&&i(e,"maxlength",u[15]),_[0]&65536&&l!==(l=u[16]?.autocapitalize)&&i(e,"autocapitalize",l),_[0]&65536&&n!==(n=u[16]?.autocorrect)&&i(e,"autocorrect",n),_[0]&65536&&s!==(s=u[16]?.spellcheck)&&i(e,"spellcheck",s),_[0]&65536&&a!==(a=u[16]?.tabindex)&&i(e,"tabindex",a),_[0]&65536&&c!==(c=u[16]?.enterkeyhint)&&i(e,"enterkeyhint",c),_[0]&65536&&m!==(m=u[16]?.lang)&&i(e,"lang",m),_[0]&1&&e.value!==u[0]&&K(e,u[0])},d(u){u&&T(e),t[42](null),f=!1,W(b)}}}function _t(t){let e,l,n,s,a,c,m,f,b,u,_,d;return{c(){e=E("input"),i(e,"data-testid","textbox"),i(e,"type","text"),i(e,"class","scroll-hide svelte-173056l"),i(e,"dir",l=t[12]?"rtl":"ltr"),i(e,"placeholder",t[2]),e.disabled=t[5],e.autofocus=t[13],i(e,"maxlength",t[15]),i(e,"style",n=t[14]?"text-align: "+t[14]:""),i(e,"autocapitalize",s=t[16]?.autocapitalize),i(e,"autocorrect",a=t[16]?.autocorrect),i(e,"spellcheck",c=t[16]?.spellcheck),i(e,"autocomplete",m=t[16]?.autocomplete),i(e,"tabindex",f=t[16]?.tabindex),i(e,"enterkeyhint",b=t[16]?.enterkeyhint),i(e,"lang",u=t[16]?.lang)},m(p,r){C(p,e,r),K(e,t[0]),t[40](e),t[13]&&e.focus(),_||(d=[g(e,"input",t[39]),g(e,"keypress",t[23]),g(e,"blur",t[31]),g(e,"select",t[22]),g(e,"focus",t[32])],_=!0)},p(p,r){r[0]&4096&&l!==(l=p[12]?"rtl":"ltr")&&i(e,"dir",l),r[0]&4&&i(e,"placeholder",p[2]),r[0]&32&&(e.disabled=p[5]),r[0]&8192&&(e.autofocus=p[13]),r[0]&32768&&i(e,"maxlength",p[15]),r[0]&16384&&n!==(n=p[14]?"text-align: "+p[14]:"")&&i(e,"style",n),r[0]&65536&&s!==(s=p[16]?.autocapitalize)&&i(e,"autocapitalize",s),r[0]&65536&&a!==(a=p[16]?.autocorrect)&&i(e,"autocorrect",a),r[0]&65536&&c!==(c=p[16]?.spellcheck)&&i(e,"spellcheck",c),r[0]&65536&&m!==(m=p[16]?.autocomplete)&&i(e,"autocomplete",m),r[0]&65536&&f!==(f=p[16]?.tabindex)&&i(e,"tabindex",f),r[0]&65536&&b!==(b=p[16]?.enterkeyhint)&&i(e,"enterkeyhint",b),r[0]&65536&&u!==(u=p[16]?.lang)&&i(e,"lang",u),r[0]&1&&e.value!==p[0]&&K(e,p[0])},d(p){p&&T(e),t[40](null),_=!1,W(d)}}}function ce(t){let e,l,n,s,a,c;const m=[ht,ct],f=[];function b(u,_){return u[10]===!0?0:1}return l=b(t),n=f[l]=m[l](t),{c(){e=E("button"),n.c(),i(e,"class","submit-button svelte-173056l"),q(e,"padded-button",t[10]!==!0)},m(u,_){C(u,e,_),f[l].m(e,null),s=!0,a||(c=g(e,"click",t[26]),a=!0)},p(u,_){let d=l;l=b(u),l===d?f[l].p(u,_):(U(),H(f[d],1,1,()=>{f[d]=null}),N(),n=f[l],n?n.p(u,_):(n=f[l]=m[l](u),n.c()),v(n,1),n.m(e,null)),(!s||_[0]&1024)&&q(e,"padded-button",u[10]!==!0)},i(u){s||(v(n),s=!0)},o(u){H(n),s=!1},d(u){u&&T(e),f[l].d(),a=!1,c()}}}function ct(t){let e;return{c(){e=ne(t[10])},m(l,n){C(l,e,n)},p(l,n){n[0]&1024&&le(e,l[10])},i:Y,o:Y,d(l){l&&T(e)}}}function ht(t){let e,l;return e=new Pe({}),{c(){A(e.$$.fragment)},m(n,s){I(e,n,s),l=!0},p:Y,i(n){l||(v(e.$$.fragment,n),l=!0)},o(n){H(e.$$.fragment,n),l=!1},d(n){G(e,n)}}}function he(t){let e,l,n,s,a,c;const m=[pt,bt],f=[];function b(u,_){return u[11]===!0?0:1}return l=b(t),n=f[l]=m[l](t),{c(){e=E("button"),n.c(),i(e,"class","stop-button svelte-173056l"),q(e,"padded-button",t[11]!==!0)},m(u,_){C(u,e,_),f[l].m(e,null),s=!0,a||(c=g(e,"click",t[25]),a=!0)},p(u,_){let d=l;l=b(u),l===d?f[l].p(u,_):(U(),H(f[d],1,1,()=>{f[d]=null}),N(),n=f[l],n?n.p(u,_):(n=f[l]=m[l](u),n.c()),v(n,1),n.m(e,null)),(!s||_[0]&2048)&&q(e,"padded-button",u[11]!==!0)},i(u){s||(v(n),s=!0)},o(u){H(n),s=!1},d(u){u&&T(e),f[l].d(),a=!1,c()}}}function bt(t){let e;return{c(){e=ne(t[11])},m(l,n){C(l,e,n)},p(l,n){n[0]&2048&&le(e,l[11])},i:Y,o:Y,d(l){l&&T(e)}}}function pt(t){let e,l;return e=new Qe({props:{fill:"none",stroke_width:2.5}}),{c(){A(e.$$.fragment)},m(n,s){I(e,n,s),l=!0},p:Y,i(n){l||(v(e.$$.fragment,n),l=!0)},o(n){H(e.$$.fragment,n),l=!1},d(n){G(e,n)}}}function mt(t){let e,l,n,s,a,c,m,f,b=t[6]&&t[9]&&_e(t);n=new Ie({props:{show_label:t[6],info:t[4],$$slots:{default:[ut]},$$scope:{ctx:t}}});function u(h,w){return h[1]===1&&h[18]===1?st:at}let _=u(t),d=_(t),p=t[10]&&ce(t),r=t[11]&&he(t);return{c(){e=E("label"),b&&b.c(),l=V(),A(n.$$.fragment),s=V(),a=E("div"),d.c(),c=V(),p&&p.c(),m=V(),r&&r.c(),i(a,"class","input-container svelte-173056l"),i(e,"class","svelte-173056l"),q(e,"container",t[7]),q(e,"show_textbox_border",t[20])},m(h,w){C(h,e,w),b&&b.m(e,null),j(e,l),I(n,e,null),j(e,s),j(e,a),d.m(a,null),j(a,c),p&&p.m(a,null),j(a,m),r&&r.m(a,null),f=!0},p(h,w){h[6]&&h[9]?b?(b.p(h,w),w[0]&576&&v(b,1)):(b=_e(h),b.c(),v(b,1),b.m(e,l)):b&&(U(),H(b,1,1,()=>{b=null}),N());const L={};w[0]&64&&(L.show_label=h[6]),w[0]&16&&(L.info=h[4]),w[0]&8|w[1]&33554432&&(L.$$scope={dirty:w,ctx:h}),n.$set(L),_===(_=u(h))&&d?d.p(h,w):(d.d(1),d=_(h),d&&(d.c(),d.m(a,c))),h[10]?p?(p.p(h,w),w[0]&1024&&v(p,1)):(p=ce(h),p.c(),v(p,1),p.m(a,m)):p&&(U(),H(p,1,1,()=>{p=null}),N()),h[11]?r?(r.p(h,w),w[0]&2048&&v(r,1)):(r=he(h),r.c(),v(r,1),r.m(a,null)):r&&(U(),H(r,1,1,()=>{r=null}),N()),(!f||w[0]&128)&&q(e,"container",h[7])},i(h){f||(v(b),v(n.$$.fragment,h),v(p),v(r),f=!0)},o(h){H(b),H(n.$$.fragment,h),H(p),H(r),f=!1},d(h){h&&T(e),b&&b.d(),G(n),d.d(),p&&p.d(),r&&r.d()}}}function dt(t){const e=t.scrollHeight,l=t.clientHeight,n=parseFloat(window.getComputedStyle(t).lineHeight);e>l+n?t.style.overflowY="scroll":t.style.overflowY="hidden"}function gt(t,e,l){let{value:n=""}=e,{value_is_output:s=!1}=e,{lines:a=1}=e,{placeholder:c="Type here..."}=e,{label:m}=e,{info:f=void 0}=e,{disabled:b=!1}=e,{show_label:u=!0}=e,{container:_=!0}=e,{max_lines:d=void 0}=e,{type:p="text"}=e,{show_copy_button:r=!1}=e,{submit_btn:h=null}=e,{stop_btn:w=null}=e,{rtl:L=!1}=e,{autofocus:X=!1}=e,{text_align:ie=void 0}=e,{autoscroll:J=!0}=e,{max_length:oe=void 0}=e,{html_attributes:ue=null}=e,y,Z=!1,x,$,ae=0,O=!1,S;const pe=!h,B=nt();tt(()=>{!O&&y&&y.offsetHeight+y.scrollTop>y.scrollHeight-100&&($=!0)});const me=()=>{$&&J&&!O&&y.scrollTo(0,y.scrollHeight)};function de(){B("change",n),s||B("input")}lt(()=>{X&&y.focus(),$&&J&&me(),l(28,s=!1)});async function ge(){"clipboard"in navigator&&(await navigator.clipboard.writeText(n),B("copy",{value:n}),ke())}function ke(){l(19,Z=!0),x&&clearTimeout(x),x=setTimeout(()=>{l(19,Z=!1)},1e3)}function ve(o){const z=o.target,F=z.value,M=[z.selectionStart,z.selectionEnd];B("select",{value:F.substring(...M),index:M})}async function we(o){await fe(),(o.key==="Enter"&&o.shiftKey&&a>1||o.key==="Enter"&&!o.shiftKey&&a===1&&S>=1)&&(o.preventDefault(),B("submit"))}function ye(o){const z=o.target,F=z.scrollTop;F<ae&&(O=!0),ae=F;const M=z.scrollHeight-z.clientHeight;F>=M&&(O=!1)}function ze(){B("stop")}function He(){B("submit")}async function P(o){if(await fe(),a===S)return;const z=o.target,F=window.getComputedStyle(z),M=parseFloat(F.paddingTop),ee=parseFloat(F.paddingBottom),se=parseFloat(F.lineHeight);let te=S===void 0?!1:M+ee+se*S,re=M+ee+a*se;z.style.height="1px";let Q;te&&z.scrollHeight>te?Q=te:z.scrollHeight<re?Q=re:Q=z.scrollHeight,z.style.height=`${Q}px`,dt(z)}function Te(o,z){if(a!==S&&(o.style.overflowY="scroll",o.addEventListener("input",P),!!z.trim()))return P({target:o}),{destroy:()=>o.removeEventListener("input",P)}}function Ce(o){D.call(this,t,o)}function Se(o){D.call(this,t,o)}function Ee(o){D.call(this,t,o)}function Fe(o){D.call(this,t,o)}function qe(o){D.call(this,t,o)}function Be(o){D.call(this,t,o)}function De(o){D.call(this,t,o)}function Ye(o){D.call(this,t,o)}function Ke(){n=this.value,l(0,n)}function Le(o){R[o?"unshift":"push"](()=>{y=o,l(17,y)})}function Me(){n=this.value,l(0,n)}function Ne(o){R[o?"unshift":"push"](()=>{y=o,l(17,y)})}function Ue(){n=this.value,l(0,n)}function je(o){R[o?"unshift":"push"](()=>{y=o,l(17,y)})}function Ae(){n=this.value,l(0,n)}function Ge(o){R[o?"unshift":"push"](()=>{y=o,l(17,y)})}return t.$$set=o=>{"value"in o&&l(0,n=o.value),"value_is_output"in o&&l(28,s=o.value_is_output),"lines"in o&&l(1,a=o.lines),"placeholder"in o&&l(2,c=o.placeholder),"label"in o&&l(3,m=o.label),"info"in o&&l(4,f=o.info),"disabled"in o&&l(5,b=o.disabled),"show_label"in o&&l(6,u=o.show_label),"container"in o&&l(7,_=o.container),"max_lines"in o&&l(29,d=o.max_lines),"type"in o&&l(8,p=o.type),"show_copy_button"in o&&l(9,r=o.show_copy_button),"submit_btn"in o&&l(10,h=o.submit_btn),"stop_btn"in o&&l(11,w=o.stop_btn),"rtl"in o&&l(12,L=o.rtl),"autofocus"in o&&l(13,X=o.autofocus),"text_align"in o&&l(14,ie=o.text_align),"autoscroll"in o&&l(30,J=o.autoscroll),"max_length"in o&&l(15,oe=o.max_length),"html_attributes"in o&&l(16,ue=o.html_attributes)},t.$$.update=()=>{t.$$.dirty[0]&536871170&&(d==null?p==="text"?l(18,S=Math.max(a,20)):l(18,S=1):l(18,S=Math.max(d,a))),t.$$.dirty[0]&1&&n===null&&l(0,n=""),t.$$.dirty[0]&393219&&y&&a!==S&&P({target:y}),t.$$.dirty[0]&1&&de()},[n,a,c,m,f,b,u,_,p,r,h,w,L,X,ie,oe,ue,y,S,Z,pe,ge,ve,we,ye,ze,He,Te,s,d,J,Ce,Se,Ee,Fe,qe,Be,De,Ye,Ke,Le,Me,Ne,Ue,je,Ae,Ge]}class Ft extends Ve{constructor(e){super(),xe(this,e,gt,mt,et,{value:0,value_is_output:28,lines:1,placeholder:2,label:3,info:4,disabled:5,show_label:6,container:7,max_lines:29,type:8,show_copy_button:9,submit_btn:10,stop_btn:11,rtl:12,autofocus:13,text_align:14,autoscroll:30,max_length:15,html_attributes:16},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),k()}get value_is_output(){return this.$$.ctx[28]}set value_is_output(e){this.$$set({value_is_output:e}),k()}get lines(){return this.$$.ctx[1]}set lines(e){this.$$set({lines:e}),k()}get placeholder(){return this.$$.ctx[2]}set placeholder(e){this.$$set({placeholder:e}),k()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),k()}get info(){return this.$$.ctx[4]}set info(e){this.$$set({info:e}),k()}get disabled(){return this.$$.ctx[5]}set disabled(e){this.$$set({disabled:e}),k()}get show_label(){return this.$$.ctx[6]}set show_label(e){this.$$set({show_label:e}),k()}get container(){return this.$$.ctx[7]}set container(e){this.$$set({container:e}),k()}get max_lines(){return this.$$.ctx[29]}set max_lines(e){this.$$set({max_lines:e}),k()}get type(){return this.$$.ctx[8]}set type(e){this.$$set({type:e}),k()}get show_copy_button(){return this.$$.ctx[9]}set show_copy_button(e){this.$$set({show_copy_button:e}),k()}get submit_btn(){return this.$$.ctx[10]}set submit_btn(e){this.$$set({submit_btn:e}),k()}get stop_btn(){return this.$$.ctx[11]}set stop_btn(e){this.$$set({stop_btn:e}),k()}get rtl(){return this.$$.ctx[12]}set rtl(e){this.$$set({rtl:e}),k()}get autofocus(){return this.$$.ctx[13]}set autofocus(e){this.$$set({autofocus:e}),k()}get text_align(){return this.$$.ctx[14]}set text_align(e){this.$$set({text_align:e}),k()}get autoscroll(){return this.$$.ctx[30]}set autoscroll(e){this.$$set({autoscroll:e}),k()}get max_length(){return this.$$.ctx[15]}set max_length(e){this.$$set({max_length:e}),k()}get html_attributes(){return this.$$.ctx[16]}set html_attributes(e){this.$$set({html_attributes:e}),k()}}export{Ft as T};
//# sourceMappingURL=Textbox-kv26zexc.js.map
