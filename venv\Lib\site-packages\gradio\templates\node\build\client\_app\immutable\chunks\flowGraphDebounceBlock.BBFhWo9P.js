import{b as u}from"./declarationMapper.UBCwU7BT.js";import{b as s}from"./KHR_interactivity.DEAVS2UW.js";import{R as i}from"./index.BoI39RQH.js";class n extends s{constructor(t){super(t),this.count=this.registerDataInput("count",u),this.reset=this._registerSignalInput("reset"),this.currentCount=this.registerDataOutput("currentCount",u)}_execute(t,r){if(r===this.reset){t._setExecutionVariable(this,"debounceCount",0);return}const o=this.count.getValue(t),e=t._getExecutionVariable(this,"debounceCount",0)+1;this.currentCount.setValue(e,t),t._setExecutionVariable(this,"debounceCount",e),e>=o&&(this.out._activateSignal(t),t._setExecutionVariable(this,"debounceCount",0))}getClassName(){return"FlowGraphDebounceBlock"}}i("FlowGraphDebounceBlock",n);export{n as FlowGraphDebounceBlock};
//# sourceMappingURL=flowGraphDebounceBlock.BBFhWo9P.js.map
