import { c as create_ssr_component, v as validate_component, b as createEventDispatcher, e as escape, d as add_attribute, o as onDestroy } from './ssr-C3HYbsxA.js';
import { m as mt, z as zA, e as bt$1, O as jt$1, g as kt$1, q as j, G as Gt, C, t as Ut$1, j as Ke$1, P as f } from './2-DJbI4FWc.js';
import v$1 from './Example7-BgAxc6bX.js';
import './index-ClteBeTX.js';
import './Component-NmRBwSfF.js';
import 'path';
import 'url';
import 'fs';

let Vs=[],Sa=[];(()=>{let s="lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o".split(",").map(t=>t?parseInt(t,36):1);for(let t=0,e=0;t<s.length;t++)(t%2?Sa:Vs).push(e=e+s[t]);})();function Nf(s){if(s<768)return !1;for(let t=0,e=Vs.length;;){let i=t+e>>1;if(s<Vs[i])e=i;else if(s>=Sa[i])t=i+1;else return !0;if(t==e)return !1}}function xo(s){return s>=127462&&s<=127487}const vo=8205;function Ff(s,t,e=!0,i=!0){return (e?Ca:Hf)(s,t,i)}function Ca(s,t,e){if(t==s.length)return t;t&&Aa(s.charCodeAt(t))&&Ma(s.charCodeAt(t-1))&&t--;let i=hs(s,t);for(t+=ko(i);t<s.length;){let n=hs(s,t);if(i==vo||n==vo||e&&Nf(n))t+=ko(n),i=n;else if(xo(n)){let r=0,o=t-2;for(;o>=0&&xo(hs(s,o));)r++,o-=2;if(r%2==0)break;t+=2;}else break}return t}function Hf(s,t,e){for(;t>0;){let i=Ca(s,t-2,e);if(i<t)return i;t--;}return 0}function hs(s,t){let e=s.charCodeAt(t);if(!Ma(e)||t+1==s.length)return e;let i=s.charCodeAt(t+1);return Aa(i)?(e-55296<<10)+(i-56320)+65536:e}function Aa(s){return s>=56320&&s<57344}function Ma(s){return s>=55296&&s<56320}function ko(s){return s<65536?1:2}class H{lineAt(t){if(t<0||t>this.length)throw new RangeError(`Invalid position ${t} in document of length ${this.length}`);return this.lineInner(t,!1,1,0)}line(t){if(t<1||t>this.lines)throw new RangeError(`Invalid line number ${t} in ${this.lines}-line document`);return this.lineInner(t,!0,1,0)}replace(t,e,i){[t,e]=ti(this,t,e);let n=[];return this.decompose(0,t,n,2),i.length&&i.decompose(0,i.length,n,3),this.decompose(e,this.length,n,1),$t.from(n,this.length-(e-t)+i.length)}append(t){return this.replace(this.length,this.length,t)}slice(t,e=this.length){[t,e]=ti(this,t,e);let i=[];return this.decompose(t,e,i,0),$t.from(i,e-t)}eq(t){if(t==this)return !0;if(t.length!=this.length||t.lines!=this.lines)return !1;let e=this.scanIdentical(t,1),i=this.length-this.scanIdentical(t,-1),n=new Ci(this),r=new Ci(t);for(let o=e,l=e;;){if(n.next(o),r.next(o),o=0,n.lineBreak!=r.lineBreak||n.done!=r.done||n.value!=r.value)return !1;if(l+=n.value.length,n.done||l>=i)return !0}}iter(t=1){return new Ci(this,t)}iterRange(t,e=this.length){return new Da(this,t,e)}iterLines(t,e){let i;if(t==null)i=this.iter();else {e==null&&(e=this.lines+1);let n=this.line(t).from;i=this.iterRange(n,Math.max(n,e==this.lines+1?this.length:e<=1?0:this.line(e-1).to));}return new Ta(i)}toString(){return this.sliceString(0)}toJSON(){let t=[];return this.flatten(t),t}constructor(){}static of(t){if(t.length==0)throw new RangeError("A document must have at least one line");return t.length==1&&!t[0]?H.empty:t.length<=32?new Z(t):$t.from(Z.split(t,[]))}}class Z extends H{constructor(t,e=Vf(t)){super(),this.text=t,this.length=e;}get lines(){return this.text.length}get children(){return null}lineInner(t,e,i,n){for(let r=0;;r++){let o=this.text[r],l=n+o.length;if((e?i:l)>=t)return new Wf(n,l,i,o);n=l+1,i++;}}decompose(t,e,i,n){let r=t<=0&&e>=this.length?this:new Z(So(this.text,t,e),Math.min(e,this.length)-Math.max(0,t));if(n&1){let o=i.pop(),l=Mn(r.text,o.text.slice(),0,r.length);if(l.length<=32)i.push(new Z(l,o.length+r.length));else {let a=l.length>>1;i.push(new Z(l.slice(0,a)),new Z(l.slice(a)));}}else i.push(r);}replace(t,e,i){if(!(i instanceof Z))return super.replace(t,e,i);[t,e]=ti(this,t,e);let n=Mn(this.text,Mn(i.text,So(this.text,0,t)),e),r=this.length+i.length-(e-t);return n.length<=32?new Z(n,r):$t.from(Z.split(n,[]),r)}sliceString(t,e=this.length,i=`
`){[t,e]=ti(this,t,e);let n="";for(let r=0,o=0;r<=e&&o<this.text.length;o++){let l=this.text[o],a=r+l.length;r>t&&o&&(n+=i),t<a&&e>r&&(n+=l.slice(Math.max(0,t-r),e-r)),r=a+1;}return n}flatten(t){for(let e of this.text)t.push(e);}scanIdentical(){return 0}static split(t,e){let i=[],n=-1;for(let r of t)i.push(r),n+=r.length+1,i.length==32&&(e.push(new Z(i,n)),i=[],n=-1);return n>-1&&e.push(new Z(i,n)),e}}class $t extends H{constructor(t,e){super(),this.children=t,this.length=e,this.lines=0;for(let i of t)this.lines+=i.lines;}lineInner(t,e,i,n){for(let r=0;;r++){let o=this.children[r],l=n+o.length,a=i+o.lines-1;if((e?a:l)>=t)return o.lineInner(t,e,i,n);n=l+1,i=a+1;}}decompose(t,e,i,n){for(let r=0,o=0;o<=e&&r<this.children.length;r++){let l=this.children[r],a=o+l.length;if(t<=a&&e>=o){let h=n&((o<=t?1:0)|(a>=e?2:0));o>=t&&a<=e&&!h?i.push(l):l.decompose(t-o,e-o,i,h);}o=a+1;}}replace(t,e,i){if([t,e]=ti(this,t,e),i.lines<this.lines)for(let n=0,r=0;n<this.children.length;n++){let o=this.children[n],l=r+o.length;if(t>=r&&e<=l){let a=o.replace(t-r,e-r,i),h=this.lines-o.lines+a.lines;if(a.lines<h>>4&&a.lines>h>>6){let c=this.children.slice();return c[n]=a,new $t(c,this.length-(e-t)+i.length)}return super.replace(r,l,a)}r=l+1;}return super.replace(t,e,i)}sliceString(t,e=this.length,i=`
`){[t,e]=ti(this,t,e);let n="";for(let r=0,o=0;r<this.children.length&&o<=e;r++){let l=this.children[r],a=o+l.length;o>t&&r&&(n+=i),t<a&&e>o&&(n+=l.sliceString(t-o,e-o,i)),o=a+1;}return n}flatten(t){for(let e of this.children)e.flatten(t);}scanIdentical(t,e){if(!(t instanceof $t))return 0;let i=0,[n,r,o,l]=e>0?[0,0,this.children.length,t.children.length]:[this.children.length-1,t.children.length-1,-1,-1];for(;;n+=e,r+=e){if(n==o||r==l)return i;let a=this.children[n],h=t.children[r];if(a!=h)return i+a.scanIdentical(h,e);i+=a.length+1;}}static from(t,e=t.reduce((i,n)=>i+n.length+1,-1)){let i=0;for(let d of t)i+=d.lines;if(i<32){let d=[];for(let p of t)p.flatten(d);return new Z(d,e)}let n=Math.max(32,i>>5),r=n<<1,o=n>>1,l=[],a=0,h=-1,c=[];function f(d){let p;if(d.lines>r&&d instanceof $t)for(let y of d.children)f(y);else d.lines>o&&(a>o||!a)?(u(),l.push(d)):d instanceof Z&&a&&(p=c[c.length-1])instanceof Z&&d.lines+p.lines<=32?(a+=d.lines,h+=d.length+1,c[c.length-1]=new Z(p.text.concat(d.text),p.length+1+d.length)):(a+d.lines>n&&u(),a+=d.lines,h+=d.length+1,c.push(d));}function u(){a!=0&&(l.push(c.length==1?c[0]:$t.from(c,h)),h=-1,a=c.length=0);}for(let d of t)f(d);return u(),l.length==1?l[0]:new $t(l,e)}}H.empty=new Z([""],0);function Vf(s){let t=-1;for(let e of s)t+=e.length+1;return t}function Mn(s,t,e=0,i=1e9){for(let n=0,r=0,o=!0;r<s.length&&n<=i;r++){let l=s[r],a=n+l.length;a>=e&&(a>i&&(l=l.slice(0,i-n)),n<e&&(l=l.slice(e-n)),o?(t[t.length-1]+=l,o=!1):t.push(l)),n=a+1;}return t}function So(s,t,e){return Mn(s,[""],t,e)}class Ci{constructor(t,e=1){this.dir=e,this.done=!1,this.lineBreak=!1,this.value="",this.nodes=[t],this.offsets=[e>0?1:(t instanceof Z?t.text.length:t.children.length)<<1];}nextInner(t,e){for(this.done=this.lineBreak=!1;;){let i=this.nodes.length-1,n=this.nodes[i],r=this.offsets[i],o=r>>1,l=n instanceof Z?n.text.length:n.children.length;if(o==(e>0?l:0)){if(i==0)return this.done=!0,this.value="",this;e>0&&this.offsets[i-1]++,this.nodes.pop(),this.offsets.pop();}else if((r&1)==(e>0?0:1)){if(this.offsets[i]+=e,t==0)return this.lineBreak=!0,this.value=`
`,this;t--;}else if(n instanceof Z){let a=n.text[o+(e<0?-1:0)];if(this.offsets[i]+=e,a.length>Math.max(0,t))return this.value=t==0?a:e>0?a.slice(t):a.slice(0,a.length-t),this;t-=a.length;}else {let a=n.children[o+(e<0?-1:0)];t>a.length?(t-=a.length,this.offsets[i]+=e):(e<0&&this.offsets[i]--,this.nodes.push(a),this.offsets.push(e>0?1:(a instanceof Z?a.text.length:a.children.length)<<1));}}}next(t=0){return t<0&&(this.nextInner(-t,-this.dir),t=this.value.length),this.nextInner(t,this.dir)}}class Da{constructor(t,e,i){this.value="",this.done=!1,this.cursor=new Ci(t,e>i?-1:1),this.pos=e>i?t.length:0,this.from=Math.min(e,i),this.to=Math.max(e,i);}nextInner(t,e){if(e<0?this.pos<=this.from:this.pos>=this.to)return this.value="",this.done=!0,this;t+=Math.max(0,e<0?this.pos-this.to:this.from-this.pos);let i=e<0?this.pos-this.from:this.to-this.pos;t>i&&(t=i),i-=t;let{value:n}=this.cursor.next(t);return this.pos+=(n.length+t)*e,this.value=n.length<=i?n:e<0?n.slice(n.length-i):n.slice(0,i),this.done=!this.value,this}next(t=0){return t<0?t=Math.max(t,this.from-this.pos):t>0&&(t=Math.min(t,this.to-this.pos)),this.nextInner(t,this.cursor.dir)}get lineBreak(){return this.cursor.lineBreak&&this.value!=""}}class Ta{constructor(t){this.inner=t,this.afterBreak=!0,this.value="",this.done=!1;}next(t=0){let{done:e,lineBreak:i,value:n}=this.inner.next(t);return e&&this.afterBreak?(this.value="",this.afterBreak=!1):e?(this.done=!0,this.value=""):i?this.afterBreak?this.value="":(this.afterBreak=!0,this.next()):(this.value=n,this.afterBreak=!1),this}get lineBreak(){return !1}}typeof Symbol<"u"&&(H.prototype[Symbol.iterator]=function(){return this.iter()},Ci.prototype[Symbol.iterator]=Da.prototype[Symbol.iterator]=Ta.prototype[Symbol.iterator]=function(){return this});class Wf{constructor(t,e,i,n){this.from=t,this.to=e,this.number=i,this.text=n;}get length(){return this.to-this.from}}function ti(s,t,e){return t=Math.max(0,Math.min(s.length,t)),[t,Math.max(t,Math.min(s.length,e))]}function gt(s,t,e=!0,i=!0){return Ff(s,t,e,i)}function _f(s){return s>=56320&&s<57344}function zf(s){return s>=55296&&s<56320}function Tt(s,t){let e=s.charCodeAt(t);if(!zf(e)||t+1==s.length)return e;let i=s.charCodeAt(t+1);return _f(i)?(e-55296<<10)+(i-56320)+65536:e}function Oa(s){return s<=65535?String.fromCharCode(s):(s-=65536,String.fromCharCode((s>>10)+55296,(s&1023)+56320))}function ae(s){return s<65536?1:2}const Ws=/\r\n?|\n/;var at=function(s){return s[s.Simple=0]="Simple",s[s.TrackDel=1]="TrackDel",s[s.TrackBefore=2]="TrackBefore",s[s.TrackAfter=3]="TrackAfter",s}(at||(at={}));class se{constructor(t){this.sections=t;}get length(){let t=0;for(let e=0;e<this.sections.length;e+=2)t+=this.sections[e];return t}get newLength(){let t=0;for(let e=0;e<this.sections.length;e+=2){let i=this.sections[e+1];t+=i<0?this.sections[e]:i;}return t}get empty(){return this.sections.length==0||this.sections.length==2&&this.sections[1]<0}iterGaps(t){for(let e=0,i=0,n=0;e<this.sections.length;){let r=this.sections[e++],o=this.sections[e++];o<0?(t(i,n,r),n+=r):n+=o,i+=r;}}iterChangedRanges(t,e=!1){_s(this,t,e);}get invertedDesc(){let t=[];for(let e=0;e<this.sections.length;){let i=this.sections[e++],n=this.sections[e++];n<0?t.push(i,n):t.push(n,i);}return new se(t)}composeDesc(t){return this.empty?t:t.empty?this:Ba(this,t)}mapDesc(t,e=!1){return t.empty?this:zs(this,t,e)}mapPos(t,e=-1,i=at.Simple){let n=0,r=0;for(let o=0;o<this.sections.length;){let l=this.sections[o++],a=this.sections[o++],h=n+l;if(a<0){if(h>t)return r+(t-n);r+=l;}else {if(i!=at.Simple&&h>=t&&(i==at.TrackDel&&n<t&&h>t||i==at.TrackBefore&&n<t||i==at.TrackAfter&&h>t))return null;if(h>t||h==t&&e<0&&!l)return t==n||e<0?r:r+a;r+=a;}n=h;}if(t>n)throw new RangeError(`Position ${t} is out of range for changeset of length ${n}`);return r}touchesRange(t,e=t){for(let i=0,n=0;i<this.sections.length&&n<=e;){let r=this.sections[i++],o=this.sections[i++],l=n+r;if(o>=0&&n<=e&&l>=t)return n<t&&l>e?"cover":!0;n=l;}return !1}toString(){let t="";for(let e=0;e<this.sections.length;){let i=this.sections[e++],n=this.sections[e++];t+=(t?" ":"")+i+(n>=0?":"+n:"");}return t}toJSON(){return this.sections}static fromJSON(t){if(!Array.isArray(t)||t.length%2||t.some(e=>typeof e!="number"))throw new RangeError("Invalid JSON representation of ChangeDesc");return new se(t)}static create(t){return new se(t)}}class it extends se{constructor(t,e){super(t),this.inserted=e;}apply(t){if(this.length!=t.length)throw new RangeError("Applying change set to a document with the wrong length");return _s(this,(e,i,n,r,o)=>t=t.replace(n,n+(i-e),o),!1),t}mapDesc(t,e=!1){return zs(this,t,e,!0)}invert(t){let e=this.sections.slice(),i=[];for(let n=0,r=0;n<e.length;n+=2){let o=e[n],l=e[n+1];if(l>=0){e[n]=l,e[n+1]=o;let a=n>>1;for(;i.length<a;)i.push(H.empty);i.push(o?t.slice(r,r+o):H.empty);}r+=o;}return new it(e,i)}compose(t){return this.empty?t:t.empty?this:Ba(this,t,!0)}map(t,e=!1){return t.empty?this:zs(this,t,e,!0)}iterChanges(t,e=!1){_s(this,t,e);}get desc(){return se.create(this.sections)}filter(t){let e=[],i=[],n=[],r=new Bi(this);t:for(let o=0,l=0;;){let a=o==t.length?1e9:t[o++];for(;l<a||l==a&&r.len==0;){if(r.done)break t;let c=Math.min(r.len,a-l);ft(n,c,-1);let f=r.ins==-1?-1:r.off==0?r.ins:0;ft(e,c,f),f>0&&ye(i,e,r.text),r.forward(c),l+=c;}let h=t[o++];for(;l<h;){if(r.done)break t;let c=Math.min(r.len,h-l);ft(e,c,-1),ft(n,c,r.ins==-1?-1:r.off==0?r.ins:0),r.forward(c),l+=c;}}return {changes:new it(e,i),filtered:se.create(n)}}toJSON(){let t=[];for(let e=0;e<this.sections.length;e+=2){let i=this.sections[e],n=this.sections[e+1];n<0?t.push(i):n==0?t.push([i]):t.push([i].concat(this.inserted[e>>1].toJSON()));}return t}static of(t,e,i){let n=[],r=[],o=0,l=null;function a(c=!1){if(!c&&!n.length)return;o<e&&ft(n,e-o,-1);let f=new it(n,r);l=l?l.compose(f.map(l)):f,n=[],r=[],o=0;}function h(c){if(Array.isArray(c))for(let f of c)h(f);else if(c instanceof it){if(c.length!=e)throw new RangeError(`Mismatched change set length (got ${c.length}, expected ${e})`);a(),l=l?l.compose(c.map(l)):c;}else {let{from:f,to:u=f,insert:d}=c;if(f>u||f<0||u>e)throw new RangeError(`Invalid change range ${f} to ${u} (in doc of length ${e})`);let p=d?typeof d=="string"?H.of(d.split(i||Ws)):d:H.empty,y=p.length;if(f==u&&y==0)return;f<o&&a(),f>o&&ft(n,f-o,-1),ft(n,u-f,y),ye(r,n,p),o=u;}}return h(t),a(!l),l}static empty(t){return new it(t?[t,-1]:[],[])}static fromJSON(t){if(!Array.isArray(t))throw new RangeError("Invalid JSON representation of ChangeSet");let e=[],i=[];for(let n=0;n<t.length;n++){let r=t[n];if(typeof r=="number")e.push(r,-1);else {if(!Array.isArray(r)||typeof r[0]!="number"||r.some((o,l)=>l&&typeof o!="string"))throw new RangeError("Invalid JSON representation of ChangeSet");if(r.length==1)e.push(r[0],0);else {for(;i.length<n;)i.push(H.empty);i[n]=H.of(r.slice(1)),e.push(r[0],i[n].length);}}}return new it(e,i)}static createSet(t,e){return new it(t,e)}}function ft(s,t,e,i=!1){if(t==0&&e<=0)return;let n=s.length-2;n>=0&&e<=0&&e==s[n+1]?s[n]+=t:n>=0&&t==0&&s[n]==0?s[n+1]+=e:i?(s[n]+=t,s[n+1]+=e):s.push(t,e);}function ye(s,t,e){if(e.length==0)return;let i=t.length-2>>1;if(i<s.length)s[s.length-1]=s[s.length-1].append(e);else {for(;s.length<i;)s.push(H.empty);s.push(e);}}function _s(s,t,e){let i=s.inserted;for(let n=0,r=0,o=0;o<s.sections.length;){let l=s.sections[o++],a=s.sections[o++];if(a<0)n+=l,r+=l;else {let h=n,c=r,f=H.empty;for(;h+=l,c+=a,a&&i&&(f=f.append(i[o-2>>1])),!(e||o==s.sections.length||s.sections[o+1]<0);)l=s.sections[o++],a=s.sections[o++];t(n,h,r,c,f),n=h,r=c;}}}function zs(s,t,e,i=!1){let n=[],r=i?[]:null,o=new Bi(s),l=new Bi(t);for(let a=-1;;){if(o.done&&l.len||l.done&&o.len)throw new Error("Mismatched change set lengths");if(o.ins==-1&&l.ins==-1){let h=Math.min(o.len,l.len);ft(n,h,-1),o.forward(h),l.forward(h);}else if(l.ins>=0&&(o.ins<0||a==o.i||o.off==0&&(l.len<o.len||l.len==o.len&&!e))){let h=l.len;for(ft(n,l.ins,-1);h;){let c=Math.min(o.len,h);o.ins>=0&&a<o.i&&o.len<=c&&(ft(n,0,o.ins),r&&ye(r,n,o.text),a=o.i),o.forward(c),h-=c;}l.next();}else if(o.ins>=0){let h=0,c=o.len;for(;c;)if(l.ins==-1){let f=Math.min(c,l.len);h+=f,c-=f,l.forward(f);}else if(l.ins==0&&l.len<c)c-=l.len,l.next();else break;ft(n,h,a<o.i?o.ins:0),r&&a<o.i&&ye(r,n,o.text),a=o.i,o.forward(o.len-c);}else {if(o.done&&l.done)return r?it.createSet(n,r):se.create(n);throw new Error("Mismatched change set lengths")}}}function Ba(s,t,e=!1){let i=[],n=e?[]:null,r=new Bi(s),o=new Bi(t);for(let l=!1;;){if(r.done&&o.done)return n?it.createSet(i,n):se.create(i);if(r.ins==0)ft(i,r.len,0,l),r.next();else if(o.len==0&&!o.done)ft(i,0,o.ins,l),n&&ye(n,i,o.text),o.next();else {if(r.done||o.done)throw new Error("Mismatched change set lengths");{let a=Math.min(r.len2,o.len),h=i.length;if(r.ins==-1){let c=o.ins==-1?-1:o.off?0:o.ins;ft(i,a,c,l),n&&c&&ye(n,i,o.text);}else o.ins==-1?(ft(i,r.off?0:r.len,a,l),n&&ye(n,i,r.textBit(a))):(ft(i,r.off?0:r.len,o.off?0:o.ins,l),n&&!o.off&&ye(n,i,o.text));l=(r.ins>a||o.ins>=0&&o.len>a)&&(l||i.length>h),r.forward2(a),o.forward(a);}}}}class Bi{constructor(t){this.set=t,this.i=0,this.next();}next(){let{sections:t}=this.set;this.i<t.length?(this.len=t[this.i++],this.ins=t[this.i++]):(this.len=0,this.ins=-2),this.off=0;}get done(){return this.ins==-2}get len2(){return this.ins<0?this.len:this.ins}get text(){let{inserted:t}=this.set,e=this.i-2>>1;return e>=t.length?H.empty:t[e]}textBit(t){let{inserted:e}=this.set,i=this.i-2>>1;return i>=e.length&&!t?H.empty:e[i].slice(this.off,t==null?void 0:this.off+t)}forward(t){t==this.len?this.next():(this.len-=t,this.off+=t);}forward2(t){this.ins==-1?this.forward(t):t==this.ins?this.next():(this.ins-=t,this.off+=t);}}class Le{constructor(t,e,i){this.from=t,this.to=e,this.flags=i;}get anchor(){return this.flags&32?this.to:this.from}get head(){return this.flags&32?this.from:this.to}get empty(){return this.from==this.to}get assoc(){return this.flags&8?-1:this.flags&16?1:0}get bidiLevel(){let t=this.flags&7;return t==7?null:t}get goalColumn(){let t=this.flags>>6;return t==16777215?void 0:t}map(t,e=-1){let i,n;return this.empty?i=n=t.mapPos(this.from,e):(i=t.mapPos(this.from,1),n=t.mapPos(this.to,-1)),i==this.from&&n==this.to?this:new Le(i,n,this.flags)}extend(t,e=t){if(t<=this.anchor&&e>=this.anchor)return v.range(t,e);let i=Math.abs(t-this.anchor)>Math.abs(e-this.anchor)?t:e;return v.range(this.anchor,i)}eq(t,e=!1){return this.anchor==t.anchor&&this.head==t.head&&(!e||!this.empty||this.assoc==t.assoc)}toJSON(){return {anchor:this.anchor,head:this.head}}static fromJSON(t){if(!t||typeof t.anchor!="number"||typeof t.head!="number")throw new RangeError("Invalid JSON representation for SelectionRange");return v.range(t.anchor,t.head)}static create(t,e,i){return new Le(t,e,i)}}class v{constructor(t,e){this.ranges=t,this.mainIndex=e;}map(t,e=-1){return t.empty?this:v.create(this.ranges.map(i=>i.map(t,e)),this.mainIndex)}eq(t,e=!1){if(this.ranges.length!=t.ranges.length||this.mainIndex!=t.mainIndex)return !1;for(let i=0;i<this.ranges.length;i++)if(!this.ranges[i].eq(t.ranges[i],e))return !1;return !0}get main(){return this.ranges[this.mainIndex]}asSingle(){return this.ranges.length==1?this:new v([this.main],0)}addRange(t,e=!0){return v.create([t].concat(this.ranges),e?0:this.mainIndex+1)}replaceRange(t,e=this.mainIndex){let i=this.ranges.slice();return i[e]=t,v.create(i,this.mainIndex)}toJSON(){return {ranges:this.ranges.map(t=>t.toJSON()),main:this.mainIndex}}static fromJSON(t){if(!t||!Array.isArray(t.ranges)||typeof t.main!="number"||t.main>=t.ranges.length)throw new RangeError("Invalid JSON representation for EditorSelection");return new v(t.ranges.map(e=>Le.fromJSON(e)),t.main)}static single(t,e=t){return new v([v.range(t,e)],0)}static create(t,e=0){if(t.length==0)throw new RangeError("A selection needs at least one range");for(let i=0,n=0;n<t.length;n++){let r=t[n];if(r.empty?r.from<=i:r.from<i)return v.normalized(t.slice(),e);i=r.to;}return new v(t,e)}static cursor(t,e=0,i,n){return Le.create(t,t,(e==0?0:e<0?8:16)|(i==null?7:Math.min(6,i))|(n??16777215)<<6)}static range(t,e,i,n){let r=(i??16777215)<<6|(n==null?7:Math.min(6,n));return e<t?Le.create(e,t,48|r):Le.create(t,e,(e>t?8:0)|r)}static normalized(t,e=0){let i=t[e];t.sort((n,r)=>n.from-r.from),e=t.indexOf(i);for(let n=1;n<t.length;n++){let r=t[n],o=t[n-1];if(r.empty?r.from<=o.to:r.from<o.to){let l=o.from,a=Math.max(r.to,o.to);n<=e&&e--,t.splice(--n,2,r.anchor>r.head?v.range(a,l):v.range(l,a));}}return new v(t,e)}}function Ea(s,t){for(let e of s.ranges)if(e.to>t)throw new RangeError("Selection points outside of document")}let Ir=0;class T{constructor(t,e,i,n,r){this.combine=t,this.compareInput=e,this.compare=i,this.isStatic=n,this.id=Ir++,this.default=t([]),this.extensions=typeof r=="function"?r(this):r;}get reader(){return this}static define(t={}){return new T(t.combine||(e=>e),t.compareInput||((e,i)=>e===i),t.compare||(t.combine?(e,i)=>e===i:Nr),!!t.static,t.enables)}of(t){return new Dn([],this,0,t)}compute(t,e){if(this.isStatic)throw new Error("Can't compute a static facet");return new Dn(t,this,1,e)}computeN(t,e){if(this.isStatic)throw new Error("Can't compute a static facet");return new Dn(t,this,2,e)}from(t,e){return e||(e=i=>i),this.compute([t],i=>e(i.field(t)))}}function Nr(s,t){return s==t||s.length==t.length&&s.every((e,i)=>e===t[i])}class Dn{constructor(t,e,i,n){this.dependencies=t,this.facet=e,this.type=i,this.value=n,this.id=Ir++;}dynamicSlot(t){var e;let i=this.value,n=this.facet.compareInput,r=this.id,o=t[r]>>1,l=this.type==2,a=!1,h=!1,c=[];for(let f of this.dependencies)f=="doc"?a=!0:f=="selection"?h=!0:((e=t[f.id])!==null&&e!==void 0?e:1)&1||c.push(t[f.id]);return {create(f){return f.values[o]=i(f),1},update(f,u){if(a&&u.docChanged||h&&(u.docChanged||u.selection)||qs(f,c)){let d=i(f);if(l?!Co(d,f.values[o],n):!n(d,f.values[o]))return f.values[o]=d,1}return 0},reconfigure:(f,u)=>{let d,p=u.config.address[r];if(p!=null){let y=Nn(u,p);if(this.dependencies.every(g=>g instanceof T?u.facet(g)===f.facet(g):g instanceof At?u.field(g,!1)==f.field(g,!1):!0)||(l?Co(d=i(f),y,n):n(d=i(f),y)))return f.values[o]=y,0}else d=i(f);return f.values[o]=d,1}}}}function Co(s,t,e){if(s.length!=t.length)return !1;for(let i=0;i<s.length;i++)if(!e(s[i],t[i]))return !1;return !0}function qs(s,t){let e=!1;for(let i of t)Ai(s,i)&1&&(e=!0);return e}function qf(s,t,e){let i=e.map(a=>s[a.id]),n=e.map(a=>a.type),r=i.filter(a=>!(a&1)),o=s[t.id]>>1;function l(a){let h=[];for(let c=0;c<i.length;c++){let f=Nn(a,i[c]);if(n[c]==2)for(let u of f)h.push(u);else h.push(f);}return t.combine(h)}return {create(a){for(let h of i)Ai(a,h);return a.values[o]=l(a),1},update(a,h){if(!qs(a,r))return 0;let c=l(a);return t.compare(c,a.values[o])?0:(a.values[o]=c,1)},reconfigure(a,h){let c=qs(a,i),f=h.config.facets[t.id],u=h.facet(t);if(f&&!c&&Nr(e,f))return a.values[o]=u,0;let d=l(a);return t.compare(d,u)?(a.values[o]=u,0):(a.values[o]=d,1)}}}const en=T.define({static:!0});class At{constructor(t,e,i,n,r){this.id=t,this.createF=e,this.updateF=i,this.compareF=n,this.spec=r,this.provides=void 0;}static define(t){let e=new At(Ir++,t.create,t.update,t.compare||((i,n)=>i===n),t);return t.provide&&(e.provides=t.provide(e)),e}create(t){let e=t.facet(en).find(i=>i.field==this);return (e?.create||this.createF)(t)}slot(t){let e=t[this.id]>>1;return {create:i=>(i.values[e]=this.create(i),1),update:(i,n)=>{let r=i.values[e],o=this.updateF(r,n);return this.compareF(r,o)?0:(i.values[e]=o,1)},reconfigure:(i,n)=>{let r=i.facet(en),o=n.facet(en),l;return (l=r.find(a=>a.field==this))&&l!=o.find(a=>a.field==this)?(i.values[e]=l.create(i),1):n.config.address[this.id]!=null?(i.values[e]=n.field(this),0):(i.values[e]=this.create(i),1)}}}init(t){return [this,en.of({field:this,create:t})]}get extension(){return this}}const Ee={lowest:4,low:3,default:2,high:1,highest:0};function ui(s){return t=>new Pa(t,s)}const qe={highest:ui(Ee.highest),high:ui(Ee.high),default:ui(Ee.default),low:ui(Ee.low),lowest:ui(Ee.lowest)};class Pa{constructor(t,e){this.inner=t,this.prec=e;}}class Qn{of(t){return new js(this,t)}reconfigure(t){return Qn.reconfigure.of({compartment:this,extension:t})}get(t){return t.config.compartments.get(this)}}class js{constructor(t,e){this.compartment=t,this.inner=e;}}class In{constructor(t,e,i,n,r,o){for(this.base=t,this.compartments=e,this.dynamicSlots=i,this.address=n,this.staticValues=r,this.facets=o,this.statusTemplate=[];this.statusTemplate.length<i.length;)this.statusTemplate.push(0);}staticFacet(t){let e=this.address[t.id];return e==null?t.default:this.staticValues[e>>1]}static resolve(t,e,i){let n=[],r=Object.create(null),o=new Map;for(let u of jf(t,e,o))u instanceof At?n.push(u):(r[u.facet.id]||(r[u.facet.id]=[])).push(u);let l=Object.create(null),a=[],h=[];for(let u of n)l[u.id]=h.length<<1,h.push(d=>u.slot(d));let c=i?.config.facets;for(let u in r){let d=r[u],p=d[0].facet,y=c&&c[u]||[];if(d.every(g=>g.type==0))if(l[p.id]=a.length<<1|1,Nr(y,d))a.push(i.facet(p));else {let g=p.combine(d.map(b=>b.value));a.push(i&&p.compare(g,i.facet(p))?i.facet(p):g);}else {for(let g of d)g.type==0?(l[g.id]=a.length<<1|1,a.push(g.value)):(l[g.id]=h.length<<1,h.push(b=>g.dynamicSlot(b)));l[p.id]=h.length<<1,h.push(g=>qf(g,p,d));}}let f=h.map(u=>u(l));return new In(t,o,f,l,a,r)}}function jf(s,t,e){let i=[[],[],[],[],[]],n=new Map;function r(o,l){let a=n.get(o);if(a!=null){if(a<=l)return;let h=i[a].indexOf(o);h>-1&&i[a].splice(h,1),o instanceof js&&e.delete(o.compartment);}if(n.set(o,l),Array.isArray(o))for(let h of o)r(h,l);else if(o instanceof js){if(e.has(o.compartment))throw new RangeError("Duplicate use of compartment in extensions");let h=t.get(o.compartment)||o.inner;e.set(o.compartment,h),r(h,l);}else if(o instanceof Pa)r(o.inner,o.prec);else if(o instanceof At)i[l].push(o),o.provides&&r(o.provides,l);else if(o instanceof Dn)i[l].push(o),o.facet.extensions&&r(o.facet.extensions,Ee.default);else {let h=o.extension;if(!h)throw new Error(`Unrecognized extension value in extension set (${o}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);r(h,l);}}return r(s,Ee.default),i.reduce((o,l)=>o.concat(l))}function Ai(s,t){if(t&1)return 2;let e=t>>1,i=s.status[e];if(i==4)throw new Error("Cyclic dependency between fields and/or facets");if(i&2)return i;s.status[e]=4;let n=s.computeSlot(s,s.config.dynamicSlots[e]);return s.status[e]=2|n}function Nn(s,t){return t&1?s.config.staticValues[t>>1]:s.values[t>>1]}const La=T.define(),Ks=T.define({combine:s=>s.some(t=>t),static:!0}),Ra=T.define({combine:s=>s.length?s[0]:void 0,static:!0}),Ia=T.define(),Na=T.define(),Fa=T.define(),Ha=T.define({combine:s=>s.length?s[0]:!1});class de{constructor(t,e){this.type=t,this.value=e;}static define(){return new Kf}}class Kf{of(t){return new de(this,t)}}class Uf{constructor(t){this.map=t;}of(t){return new R(this,t)}}class R{constructor(t,e){this.type=t,this.value=e;}map(t){let e=this.type.map(this.value,t);return e===void 0?void 0:e==this.value?this:new R(this.type,e)}is(t){return this.type==t}static define(t={}){return new Uf(t.map||(e=>e))}static mapEffects(t,e){if(!t.length)return t;let i=[];for(let n of t){let r=n.map(e);r&&i.push(r);}return i}}R.reconfigure=R.define();R.appendConfig=R.define();class tt{constructor(t,e,i,n,r,o){this.startState=t,this.changes=e,this.selection=i,this.effects=n,this.annotations=r,this.scrollIntoView=o,this._doc=null,this._state=null,i&&Ea(i,e.newLength),r.some(l=>l.type==tt.time)||(this.annotations=r.concat(tt.time.of(Date.now())));}static create(t,e,i,n,r,o){return new tt(t,e,i,n,r,o)}get newDoc(){return this._doc||(this._doc=this.changes.apply(this.startState.doc))}get newSelection(){return this.selection||this.startState.selection.map(this.changes)}get state(){return this._state||this.startState.applyTransaction(this),this._state}annotation(t){for(let e of this.annotations)if(e.type==t)return e.value}get docChanged(){return !this.changes.empty}get reconfigured(){return this.startState.config!=this.state.config}isUserEvent(t){let e=this.annotation(tt.userEvent);return !!(e&&(e==t||e.length>t.length&&e.slice(0,t.length)==t&&e[t.length]=="."))}}tt.time=de.define();tt.userEvent=de.define();tt.addToHistory=de.define();tt.remote=de.define();function Gf(s,t){let e=[];for(let i=0,n=0;;){let r,o;if(i<s.length&&(n==t.length||t[n]>=s[i]))r=s[i++],o=s[i++];else if(n<t.length)r=t[n++],o=t[n++];else return e;!e.length||e[e.length-1]<r?e.push(r,o):e[e.length-1]<o&&(e[e.length-1]=o);}}function Va(s,t,e){var i;let n,r,o;return e?(n=t.changes,r=it.empty(t.changes.length),o=s.changes.compose(t.changes)):(n=t.changes.map(s.changes),r=s.changes.mapDesc(t.changes,!0),o=s.changes.compose(n)),{changes:o,selection:t.selection?t.selection.map(r):(i=s.selection)===null||i===void 0?void 0:i.map(n),effects:R.mapEffects(s.effects,n).concat(R.mapEffects(t.effects,r)),annotations:s.annotations.length?s.annotations.concat(t.annotations):t.annotations,scrollIntoView:s.scrollIntoView||t.scrollIntoView}}function Us(s,t,e){let i=t.selection,n=Xe(t.annotations);return t.userEvent&&(n=n.concat(tt.userEvent.of(t.userEvent))),{changes:t.changes instanceof it?t.changes:it.of(t.changes||[],e,s.facet(Ra)),selection:i&&(i instanceof v?i:v.single(i.anchor,i.head)),effects:Xe(t.effects),annotations:n,scrollIntoView:!!t.scrollIntoView}}function Wa(s,t,e){let i=Us(s,t.length?t[0]:{},s.doc.length);t.length&&t[0].filter===!1&&(e=!1);for(let r=1;r<t.length;r++){t[r].filter===!1&&(e=!1);let o=!!t[r].sequential;i=Va(i,Us(s,t[r],o?i.changes.newLength:s.doc.length),o);}let n=tt.create(s,i.changes,i.selection,i.effects,i.annotations,i.scrollIntoView);return Jf(e?Yf(n):n)}function Yf(s){let t=s.startState,e=!0;for(let n of t.facet(Ia)){let r=n(s);if(r===!1){e=!1;break}Array.isArray(r)&&(e=e===!0?r:Gf(e,r));}if(e!==!0){let n,r;if(e===!1)r=s.changes.invertedDesc,n=it.empty(t.doc.length);else {let o=s.changes.filter(e);n=o.changes,r=o.filtered.mapDesc(o.changes).invertedDesc;}s=tt.create(t,n,s.selection&&s.selection.map(r),R.mapEffects(s.effects,r),s.annotations,s.scrollIntoView);}let i=t.facet(Na);for(let n=i.length-1;n>=0;n--){let r=i[n](s);r instanceof tt?s=r:Array.isArray(r)&&r.length==1&&r[0]instanceof tt?s=r[0]:s=Wa(t,Xe(r),!1);}return s}function Jf(s){let t=s.startState,e=t.facet(Fa),i=s;for(let n=e.length-1;n>=0;n--){let r=e[n](s);r&&Object.keys(r).length&&(i=Va(i,Us(t,r,s.changes.newLength),!0));}return i==s?s:tt.create(t,s.changes,s.selection,i.effects,i.annotations,i.scrollIntoView)}const Xf=[];function Xe(s){return s==null?Xf:Array.isArray(s)?s:[s]}var Lt=function(s){return s[s.Word=0]="Word",s[s.Space=1]="Space",s[s.Other=2]="Other",s}(Lt||(Lt={}));const Qf=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;let Gs;try{Gs=new RegExp("[\\p{Alphabetic}\\p{Number}_]","u");}catch{}function Zf(s){if(Gs)return Gs.test(s);for(let t=0;t<s.length;t++){let e=s[t];if(/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||Qf.test(e)))return !0}return !1}function $f(s){return t=>{if(!/\S/.test(t))return Lt.Space;if(Zf(t))return Lt.Word;for(let e=0;e<s.length;e++)if(t.indexOf(s[e])>-1)return Lt.Word;return Lt.Other}}class W{constructor(t,e,i,n,r,o){this.config=t,this.doc=e,this.selection=i,this.values=n,this.status=t.statusTemplate.slice(),this.computeSlot=r,o&&(o._state=this);for(let l=0;l<this.config.dynamicSlots.length;l++)Ai(this,l<<1);this.computeSlot=null;}field(t,e=!0){let i=this.config.address[t.id];if(i==null){if(e)throw new RangeError("Field is not present in this state");return}return Ai(this,i),Nn(this,i)}update(...t){return Wa(this,t,!0)}applyTransaction(t){let e=this.config,{base:i,compartments:n}=e;for(let l of t.effects)l.is(Qn.reconfigure)?(e&&(n=new Map,e.compartments.forEach((a,h)=>n.set(h,a)),e=null),n.set(l.value.compartment,l.value.extension)):l.is(R.reconfigure)?(e=null,i=l.value):l.is(R.appendConfig)&&(e=null,i=Xe(i).concat(l.value));let r;e?r=t.startState.values.slice():(e=In.resolve(i,n,this),r=new W(e,this.doc,this.selection,e.dynamicSlots.map(()=>null),(a,h)=>h.reconfigure(a,this),null).values);let o=t.startState.facet(Ks)?t.newSelection:t.newSelection.asSingle();new W(e,t.newDoc,o,r,(l,a)=>a.update(l,t),t);}replaceSelection(t){return typeof t=="string"&&(t=this.toText(t)),this.changeByRange(e=>({changes:{from:e.from,to:e.to,insert:t},range:v.cursor(e.from+t.length)}))}changeByRange(t){let e=this.selection,i=t(e.ranges[0]),n=this.changes(i.changes),r=[i.range],o=Xe(i.effects);for(let l=1;l<e.ranges.length;l++){let a=t(e.ranges[l]),h=this.changes(a.changes),c=h.map(n);for(let u=0;u<l;u++)r[u]=r[u].map(c);let f=n.mapDesc(h,!0);r.push(a.range.map(f)),n=n.compose(c),o=R.mapEffects(o,c).concat(R.mapEffects(Xe(a.effects),f));}return {changes:n,selection:v.create(r,e.mainIndex),effects:o}}changes(t=[]){return t instanceof it?t:it.of(t,this.doc.length,this.facet(W.lineSeparator))}toText(t){return H.of(t.split(this.facet(W.lineSeparator)||Ws))}sliceDoc(t=0,e=this.doc.length){return this.doc.sliceString(t,e,this.lineBreak)}facet(t){let e=this.config.address[t.id];return e==null?t.default:(Ai(this,e),Nn(this,e))}toJSON(t){let e={doc:this.sliceDoc(),selection:this.selection.toJSON()};if(t)for(let i in t){let n=t[i];n instanceof At&&this.config.address[n.id]!=null&&(e[i]=n.spec.toJSON(this.field(t[i]),this));}return e}static fromJSON(t,e={},i){if(!t||typeof t.doc!="string")throw new RangeError("Invalid JSON representation for EditorState");let n=[];if(i){for(let r in i)if(Object.prototype.hasOwnProperty.call(t,r)){let o=i[r],l=t[r];n.push(o.init(a=>o.spec.fromJSON(l,a)));}}return W.create({doc:t.doc,selection:v.fromJSON(t.selection),extensions:e.extensions?n.concat([e.extensions]):n})}static create(t={}){let e=In.resolve(t.extensions||[],new Map),i=t.doc instanceof H?t.doc:H.of((t.doc||"").split(e.staticFacet(W.lineSeparator)||Ws)),n=t.selection?t.selection instanceof v?t.selection:v.single(t.selection.anchor,t.selection.head):v.single(0);return Ea(n,i.length),e.staticFacet(Ks)||(n=n.asSingle()),new W(e,i,n,e.dynamicSlots.map(()=>null),(r,o)=>o.create(r),null)}get tabSize(){return this.facet(W.tabSize)}get lineBreak(){return this.facet(W.lineSeparator)||`
`}get readOnly(){return this.facet(Ha)}phrase(t,...e){for(let i of this.facet(W.phrases))if(Object.prototype.hasOwnProperty.call(i,t)){t=i[t];break}return e.length&&(t=t.replace(/\$(\$|\d*)/g,(i,n)=>{if(n=="$")return "$";let r=+(n||1);return !r||r>e.length?i:e[r-1]})),t}languageDataAt(t,e,i=-1){let n=[];for(let r of this.facet(La))for(let o of r(this,e,i))Object.prototype.hasOwnProperty.call(o,t)&&n.push(o[t]);return n}charCategorizer(t){return $f(this.languageDataAt("wordChars",t).join(""))}wordAt(t){let{text:e,from:i,length:n}=this.doc.lineAt(t),r=this.charCategorizer(t),o=t-i,l=t-i;for(;o>0;){let a=gt(e,o,!1);if(r(e.slice(a,o))!=Lt.Word)break;o=a;}for(;l<n;){let a=gt(e,l);if(r(e.slice(l,a))!=Lt.Word)break;l=a;}return o==l?null:v.range(o+i,l+i)}}W.allowMultipleSelections=Ks;W.tabSize=T.define({combine:s=>s.length?s[0]:4});W.lineSeparator=Ra;W.readOnly=Ha;W.phrases=T.define({compare(s,t){let e=Object.keys(s),i=Object.keys(t);return e.length==i.length&&e.every(n=>s[n]==t[n])}});W.languageData=La;W.changeFilter=Ia;W.transactionFilter=Na;W.transactionExtender=Fa;Qn.reconfigure=R.define();function je(s,t,e={}){let i={};for(let n of s)for(let r of Object.keys(n)){let o=n[r],l=i[r];if(l===void 0)i[r]=o;else if(!(l===o||o===void 0))if(Object.hasOwnProperty.call(e,r))i[r]=e[r](l,o);else throw new Error("Config merge conflict for field "+r)}for(let n in t)i[n]===void 0&&(i[n]=t[n]);return i}class Fe{eq(t){return this==t}range(t,e=t){return Ys.create(t,e,this)}}Fe.prototype.startSide=Fe.prototype.endSide=0;Fe.prototype.point=!1;Fe.prototype.mapMode=at.TrackDel;let Ys=class _a{constructor(t,e,i){this.from=t,this.to=e,this.value=i;}static create(t,e,i){return new _a(t,e,i)}};function Js(s,t){return s.from-t.from||s.value.startSide-t.value.startSide}class Fr{constructor(t,e,i,n){this.from=t,this.to=e,this.value=i,this.maxPoint=n;}get length(){return this.to[this.to.length-1]}findIndex(t,e,i,n=0){let r=i?this.to:this.from;for(let o=n,l=r.length;;){if(o==l)return o;let a=o+l>>1,h=r[a]-t||(i?this.value[a].endSide:this.value[a].startSide)-e;if(a==o)return h>=0?o:l;h>=0?l=a:o=a+1;}}between(t,e,i,n){for(let r=this.findIndex(e,-1e9,!0),o=this.findIndex(i,1e9,!1,r);r<o;r++)if(n(this.from[r]+t,this.to[r]+t,this.value[r])===!1)return !1}map(t,e){let i=[],n=[],r=[],o=-1,l=-1;for(let a=0;a<this.value.length;a++){let h=this.value[a],c=this.from[a]+t,f=this.to[a]+t,u,d;if(c==f){let p=e.mapPos(c,h.startSide,h.mapMode);if(p==null||(u=d=p,h.startSide!=h.endSide&&(d=e.mapPos(c,h.endSide),d<u)))continue}else if(u=e.mapPos(c,h.startSide),d=e.mapPos(f,h.endSide),u>d||u==d&&h.startSide>0&&h.endSide<=0)continue;(d-u||h.endSide-h.startSide)<0||(o<0&&(o=u),h.point&&(l=Math.max(l,d-u)),i.push(h),n.push(u-o),r.push(d-o));}return {mapped:i.length?new Fr(n,r,i,l):null,pos:o}}}class z{constructor(t,e,i,n){this.chunkPos=t,this.chunk=e,this.nextLayer=i,this.maxPoint=n;}static create(t,e,i,n){return new z(t,e,i,n)}get length(){let t=this.chunk.length-1;return t<0?0:Math.max(this.chunkEnd(t),this.nextLayer.length)}get size(){if(this.isEmpty)return 0;let t=this.nextLayer.size;for(let e of this.chunk)t+=e.value.length;return t}chunkEnd(t){return this.chunkPos[t]+this.chunk[t].length}update(t){let{add:e=[],sort:i=!1,filterFrom:n=0,filterTo:r=this.length}=t,o=t.filter;if(e.length==0&&!o)return this;if(i&&(e=e.slice().sort(Js)),this.isEmpty)return e.length?z.of(e):this;let l=new za(this,null,-1).goto(0),a=0,h=[],c=new xe;for(;l.value||a<e.length;)if(a<e.length&&(l.from-e[a].from||l.startSide-e[a].value.startSide)>=0){let f=e[a++];c.addInner(f.from,f.to,f.value)||h.push(f);}else l.rangeIndex==1&&l.chunkIndex<this.chunk.length&&(a==e.length||this.chunkEnd(l.chunkIndex)<e[a].from)&&(!o||n>this.chunkEnd(l.chunkIndex)||r<this.chunkPos[l.chunkIndex])&&c.addChunk(this.chunkPos[l.chunkIndex],this.chunk[l.chunkIndex])?l.nextChunk():((!o||n>l.to||r<l.from||o(l.from,l.to,l.value))&&(c.addInner(l.from,l.to,l.value)||h.push(Ys.create(l.from,l.to,l.value))),l.next());return c.finishInner(this.nextLayer.isEmpty&&!h.length?z.empty:this.nextLayer.update({add:h,filter:o,filterFrom:n,filterTo:r}))}map(t){if(t.empty||this.isEmpty)return this;let e=[],i=[],n=-1;for(let o=0;o<this.chunk.length;o++){let l=this.chunkPos[o],a=this.chunk[o],h=t.touchesRange(l,l+a.length);if(h===!1)n=Math.max(n,a.maxPoint),e.push(a),i.push(t.mapPos(l));else if(h===!0){let{mapped:c,pos:f}=a.map(l,t);c&&(n=Math.max(n,c.maxPoint),e.push(c),i.push(f));}}let r=this.nextLayer.map(t);return e.length==0?r:new z(i,e,r||z.empty,n)}between(t,e,i){if(!this.isEmpty){for(let n=0;n<this.chunk.length;n++){let r=this.chunkPos[n],o=this.chunk[n];if(e>=r&&t<=r+o.length&&o.between(r,t-r,e-r,i)===!1)return}this.nextLayer.between(t,e,i);}}iter(t=0){return Ei.from([this]).goto(t)}get isEmpty(){return this.nextLayer==this}static iter(t,e=0){return Ei.from(t).goto(e)}static compare(t,e,i,n,r=-1){let o=t.filter(f=>f.maxPoint>0||!f.isEmpty&&f.maxPoint>=r),l=e.filter(f=>f.maxPoint>0||!f.isEmpty&&f.maxPoint>=r),a=Ao(o,l,i),h=new di(o,a,r),c=new di(l,a,r);i.iterGaps((f,u,d)=>Mo(h,f,c,u,d,n)),i.empty&&i.length==0&&Mo(h,0,c,0,0,n);}static eq(t,e,i=0,n){n==null&&(n=999999999);let r=t.filter(c=>!c.isEmpty&&e.indexOf(c)<0),o=e.filter(c=>!c.isEmpty&&t.indexOf(c)<0);if(r.length!=o.length)return !1;if(!r.length)return !0;let l=Ao(r,o),a=new di(r,l,0).goto(i),h=new di(o,l,0).goto(i);for(;;){if(a.to!=h.to||!Xs(a.active,h.active)||a.point&&(!h.point||!a.point.eq(h.point)))return !1;if(a.to>n)return !0;a.next(),h.next();}}static spans(t,e,i,n,r=-1){let o=new di(t,null,r).goto(e),l=e,a=o.openStart;for(;;){let h=Math.min(o.to,i);if(o.point){let c=o.activeForPoint(o.to),f=o.pointFrom<e?c.length+1:o.point.startSide<0?c.length:Math.min(c.length,a);n.point(l,h,o.point,c,f,o.pointRank),a=Math.min(o.openEnd(h),c.length);}else h>l&&(n.span(l,h,o.active,a),a=o.openEnd(h));if(o.to>i)return a+(o.point&&o.to>i?1:0);l=o.to,o.next();}}static of(t,e=!1){let i=new xe;for(let n of t instanceof Ys?[t]:e?tu(t):t)i.add(n.from,n.to,n.value);return i.finish()}static join(t){if(!t.length)return z.empty;let e=t[t.length-1];for(let i=t.length-2;i>=0;i--)for(let n=t[i];n!=z.empty;n=n.nextLayer)e=new z(n.chunkPos,n.chunk,e,Math.max(n.maxPoint,e.maxPoint));return e}}z.empty=new z([],[],null,-1);function tu(s){if(s.length>1)for(let t=s[0],e=1;e<s.length;e++){let i=s[e];if(Js(t,i)>0)return s.slice().sort(Js);t=i;}return s}z.empty.nextLayer=z.empty;class xe{finishChunk(t){this.chunks.push(new Fr(this.from,this.to,this.value,this.maxPoint)),this.chunkPos.push(this.chunkStart),this.chunkStart=-1,this.setMaxPoint=Math.max(this.setMaxPoint,this.maxPoint),this.maxPoint=-1,t&&(this.from=[],this.to=[],this.value=[]);}constructor(){this.chunks=[],this.chunkPos=[],this.chunkStart=-1,this.last=null,this.lastFrom=-1e9,this.lastTo=-1e9,this.from=[],this.to=[],this.value=[],this.maxPoint=-1,this.setMaxPoint=-1,this.nextLayer=null;}add(t,e,i){this.addInner(t,e,i)||(this.nextLayer||(this.nextLayer=new xe)).add(t,e,i);}addInner(t,e,i){let n=t-this.lastTo||i.startSide-this.last.endSide;if(n<=0&&(t-this.lastFrom||i.startSide-this.last.startSide)<0)throw new Error("Ranges must be added sorted by `from` position and `startSide`");return n<0?!1:(this.from.length==250&&this.finishChunk(!0),this.chunkStart<0&&(this.chunkStart=t),this.from.push(t-this.chunkStart),this.to.push(e-this.chunkStart),this.last=i,this.lastFrom=t,this.lastTo=e,this.value.push(i),i.point&&(this.maxPoint=Math.max(this.maxPoint,e-t)),!0)}addChunk(t,e){if((t-this.lastTo||e.value[0].startSide-this.last.endSide)<0)return !1;this.from.length&&this.finishChunk(!0),this.setMaxPoint=Math.max(this.setMaxPoint,e.maxPoint),this.chunks.push(e),this.chunkPos.push(t);let i=e.value.length-1;return this.last=e.value[i],this.lastFrom=e.from[i]+t,this.lastTo=e.to[i]+t,!0}finish(){return this.finishInner(z.empty)}finishInner(t){if(this.from.length&&this.finishChunk(!1),this.chunks.length==0)return t;let e=z.create(this.chunkPos,this.chunks,this.nextLayer?this.nextLayer.finishInner(t):t,this.setMaxPoint);return this.from=null,e}}function Ao(s,t,e){let i=new Map;for(let r of s)for(let o=0;o<r.chunk.length;o++)r.chunk[o].maxPoint<=0&&i.set(r.chunk[o],r.chunkPos[o]);let n=new Set;for(let r of t)for(let o=0;o<r.chunk.length;o++){let l=i.get(r.chunk[o]);l!=null&&(e?e.mapPos(l):l)==r.chunkPos[o]&&!e?.touchesRange(l,l+r.chunk[o].length)&&n.add(r.chunk[o]);}return n}class za{constructor(t,e,i,n=0){this.layer=t,this.skip=e,this.minPoint=i,this.rank=n;}get startSide(){return this.value?this.value.startSide:0}get endSide(){return this.value?this.value.endSide:0}goto(t,e=-1e9){return this.chunkIndex=this.rangeIndex=0,this.gotoInner(t,e,!1),this}gotoInner(t,e,i){for(;this.chunkIndex<this.layer.chunk.length;){let n=this.layer.chunk[this.chunkIndex];if(!(this.skip&&this.skip.has(n)||this.layer.chunkEnd(this.chunkIndex)<t||n.maxPoint<this.minPoint))break;this.chunkIndex++,i=!1;}if(this.chunkIndex<this.layer.chunk.length){let n=this.layer.chunk[this.chunkIndex].findIndex(t-this.layer.chunkPos[this.chunkIndex],e,!0);(!i||this.rangeIndex<n)&&this.setRangeIndex(n);}this.next();}forward(t,e){(this.to-t||this.endSide-e)<0&&this.gotoInner(t,e,!0);}next(){for(;;)if(this.chunkIndex==this.layer.chunk.length){this.from=this.to=1e9,this.value=null;break}else {let t=this.layer.chunkPos[this.chunkIndex],e=this.layer.chunk[this.chunkIndex],i=t+e.from[this.rangeIndex];if(this.from=i,this.to=t+e.to[this.rangeIndex],this.value=e.value[this.rangeIndex],this.setRangeIndex(this.rangeIndex+1),this.minPoint<0||this.value.point&&this.to-this.from>=this.minPoint)break}}setRangeIndex(t){if(t==this.layer.chunk[this.chunkIndex].value.length){if(this.chunkIndex++,this.skip)for(;this.chunkIndex<this.layer.chunk.length&&this.skip.has(this.layer.chunk[this.chunkIndex]);)this.chunkIndex++;this.rangeIndex=0;}else this.rangeIndex=t;}nextChunk(){this.chunkIndex++,this.rangeIndex=0,this.next();}compare(t){return this.from-t.from||this.startSide-t.startSide||this.rank-t.rank||this.to-t.to||this.endSide-t.endSide}}class Ei{constructor(t){this.heap=t;}static from(t,e=null,i=-1){let n=[];for(let r=0;r<t.length;r++)for(let o=t[r];!o.isEmpty;o=o.nextLayer)o.maxPoint>=i&&n.push(new za(o,e,i,r));return n.length==1?n[0]:new Ei(n)}get startSide(){return this.value?this.value.startSide:0}goto(t,e=-1e9){for(let i of this.heap)i.goto(t,e);for(let i=this.heap.length>>1;i>=0;i--)cs(this.heap,i);return this.next(),this}forward(t,e){for(let i of this.heap)i.forward(t,e);for(let i=this.heap.length>>1;i>=0;i--)cs(this.heap,i);(this.to-t||this.value.endSide-e)<0&&this.next();}next(){if(this.heap.length==0)this.from=this.to=1e9,this.value=null,this.rank=-1;else {let t=this.heap[0];this.from=t.from,this.to=t.to,this.value=t.value,this.rank=t.rank,t.value&&t.next(),cs(this.heap,0);}}}function cs(s,t){for(let e=s[t];;){let i=(t<<1)+1;if(i>=s.length)break;let n=s[i];if(i+1<s.length&&n.compare(s[i+1])>=0&&(n=s[i+1],i++),e.compare(n)<0)break;s[i]=e,s[t]=n,t=i;}}class di{constructor(t,e,i){this.minPoint=i,this.active=[],this.activeTo=[],this.activeRank=[],this.minActive=-1,this.point=null,this.pointFrom=0,this.pointRank=0,this.to=-1e9,this.endSide=0,this.openStart=-1,this.cursor=Ei.from(t,e,i);}goto(t,e=-1e9){return this.cursor.goto(t,e),this.active.length=this.activeTo.length=this.activeRank.length=0,this.minActive=-1,this.to=t,this.endSide=e,this.openStart=-1,this.next(),this}forward(t,e){for(;this.minActive>-1&&(this.activeTo[this.minActive]-t||this.active[this.minActive].endSide-e)<0;)this.removeActive(this.minActive);this.cursor.forward(t,e);}removeActive(t){nn(this.active,t),nn(this.activeTo,t),nn(this.activeRank,t),this.minActive=Do(this.active,this.activeTo);}addActive(t){let e=0,{value:i,to:n,rank:r}=this.cursor;for(;e<this.activeRank.length&&(r-this.activeRank[e]||n-this.activeTo[e])>0;)e++;sn(this.active,e,i),sn(this.activeTo,e,n),sn(this.activeRank,e,r),t&&sn(t,e,this.cursor.from),this.minActive=Do(this.active,this.activeTo);}next(){let t=this.to,e=this.point;this.point=null;let i=this.openStart<0?[]:null;for(;;){let n=this.minActive;if(n>-1&&(this.activeTo[n]-this.cursor.from||this.active[n].endSide-this.cursor.startSide)<0){if(this.activeTo[n]>t){this.to=this.activeTo[n],this.endSide=this.active[n].endSide;break}this.removeActive(n),i&&nn(i,n);}else if(this.cursor.value)if(this.cursor.from>t){this.to=this.cursor.from,this.endSide=this.cursor.startSide;break}else {let r=this.cursor.value;if(!r.point)this.addActive(i),this.cursor.next();else if(e&&this.cursor.to==this.to&&this.cursor.from<this.cursor.to)this.cursor.next();else {this.point=r,this.pointFrom=this.cursor.from,this.pointRank=this.cursor.rank,this.to=this.cursor.to,this.endSide=r.endSide,this.cursor.next(),this.forward(this.to,this.endSide);break}}else {this.to=this.endSide=1e9;break}}if(i){this.openStart=0;for(let n=i.length-1;n>=0&&i[n]<t;n--)this.openStart++;}}activeForPoint(t){if(!this.active.length)return this.active;let e=[];for(let i=this.active.length-1;i>=0&&!(this.activeRank[i]<this.pointRank);i--)(this.activeTo[i]>t||this.activeTo[i]==t&&this.active[i].endSide>=this.point.endSide)&&e.push(this.active[i]);return e.reverse()}openEnd(t){let e=0;for(let i=this.activeTo.length-1;i>=0&&this.activeTo[i]>t;i--)e++;return e}}function Mo(s,t,e,i,n,r){s.goto(t),e.goto(i);let o=i+n,l=i,a=i-t;for(;;){let h=s.to+a-e.to,c=h||s.endSide-e.endSide,f=c<0?s.to+a:e.to,u=Math.min(f,o);if(s.point||e.point?s.point&&e.point&&(s.point==e.point||s.point.eq(e.point))&&Xs(s.activeForPoint(s.to),e.activeForPoint(e.to))||r.comparePoint(l,u,s.point,e.point):u>l&&!Xs(s.active,e.active)&&r.compareRange(l,u,s.active,e.active),f>o)break;(h||s.openEnd!=e.openEnd)&&r.boundChange&&r.boundChange(f),l=f,c<=0&&s.next(),c>=0&&e.next();}}function Xs(s,t){if(s.length!=t.length)return !1;for(let e=0;e<s.length;e++)if(s[e]!=t[e]&&!s[e].eq(t[e]))return !1;return !0}function nn(s,t){for(let e=t,i=s.length-1;e<i;e++)s[e]=s[e+1];s.pop();}function sn(s,t,e){for(let i=s.length-1;i>=t;i--)s[i+1]=s[i];s[t]=e;}function Do(s,t){let e=-1,i=1e9;for(let n=0;n<t.length;n++)(t[n]-i||s[n].endSide-s[e].endSide)<0&&(e=n,i=t[n]);return e}function ai(s,t,e=s.length){let i=0;for(let n=0;n<e&&n<s.length;)s.charCodeAt(n)==9?(i+=t-i%t,n++):(i++,n=gt(s,n));return i}function Qs(s,t,e,i){for(let n=0,r=0;;){if(r>=t)return n;if(n==s.length)break;r+=s.charCodeAt(n)==9?e-r%e:1,n=gt(s,n);}return i===!0?-1:s.length}const Zs="ͼ",To=typeof Symbol>"u"?"__"+Zs:Symbol.for(Zs),$s=typeof Symbol>"u"?"__styleSet"+Math.floor(Math.random()*1e8):Symbol("styleSet"),Oo=typeof globalThis<"u"?globalThis:typeof window<"u"?window:{};class ve{constructor(t,e){this.rules=[];let{finish:i}=e||{};function n(o){return /^@/.test(o)?[o]:o.split(/,\s*/)}function r(o,l,a,h){let c=[],f=/^@(\w+)\b/.exec(o[0]),u=f&&f[1]=="keyframes";if(f&&l==null)return a.push(o[0]+";");for(let d in l){let p=l[d];if(/&/.test(d))r(d.split(/,\s*/).map(y=>o.map(g=>y.replace(/&/,g))).reduce((y,g)=>y.concat(g)),p,a);else if(p&&typeof p=="object"){if(!f)throw new RangeError("The value of a property ("+d+") should be a primitive value.");r(n(d),p,c,u);}else p!=null&&c.push(d.replace(/_.*/,"").replace(/[A-Z]/g,y=>"-"+y.toLowerCase())+": "+p+";");}(c.length||u)&&a.push((i&&!f&&!h?o.map(i):o).join(", ")+" {"+c.join(" ")+"}");}for(let o in t)r(n(o),t[o],this.rules);}getRules(){return this.rules.join(`
`)}static newName(){let t=Oo[To]||1;return Oo[To]=t+1,Zs+t.toString(36)}static mount(t,e,i){let n=t[$s],r=i&&i.nonce;n?r&&n.setNonce(r):n=new eu(t,r),n.mount(Array.isArray(e)?e:[e]);}}let Bo=new Map;class eu{constructor(t,e){let i=t.ownerDocument||t,n=i.defaultView;if(!t.head&&t.adoptedStyleSheets&&n.CSSStyleSheet){let r=Bo.get(i);if(r)return t.adoptedStyleSheets=[r.sheet,...t.adoptedStyleSheets],t[$s]=r;this.sheet=new n.CSSStyleSheet,t.adoptedStyleSheets=[this.sheet,...t.adoptedStyleSheets],Bo.set(i,this);}else {this.styleTag=i.createElement("style"),e&&this.styleTag.setAttribute("nonce",e);let r=t.head||t;r.insertBefore(this.styleTag,r.firstChild);}this.modules=[],t[$s]=this;}mount(t){let e=this.sheet,i=0,n=0;for(let r=0;r<t.length;r++){let o=t[r],l=this.modules.indexOf(o);if(l<n&&l>-1&&(this.modules.splice(l,1),n--,l=-1),l==-1){if(this.modules.splice(n++,0,o),e)for(let a=0;a<o.rules.length;a++)e.insertRule(o.rules[a],i++);}else {for(;n<l;)i+=this.modules[n++].rules.length;i+=o.rules.length,n++;}}if(!e){let r="";for(let o=0;o<this.modules.length;o++)r+=this.modules[o].getRules()+`
`;this.styleTag.textContent=r;}}setNonce(t){this.styleTag&&this.styleTag.getAttribute("nonce")!=t&&this.styleTag.setAttribute("nonce",t);}}var ke={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},Pi={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},iu=typeof navigator<"u"&&/Mac/.test(navigator.platform),nu=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent);for(var lt=0;lt<10;lt++)ke[48+lt]=ke[96+lt]=String(lt);for(var lt=1;lt<=24;lt++)ke[lt+111]="F"+lt;for(var lt=65;lt<=90;lt++)ke[lt]=String.fromCharCode(lt+32),Pi[lt]=String.fromCharCode(lt);for(var fs in ke)Pi.hasOwnProperty(fs)||(Pi[fs]=ke[fs]);function su(s){var t=iu&&s.metaKey&&s.shiftKey&&!s.ctrlKey&&!s.altKey||nu&&s.shiftKey&&s.key&&s.key.length==1||s.key=="Unidentified",e=!t&&s.key||(s.shiftKey?Pi:ke)[s.keyCode]||s.key||"Unidentified";return e=="Esc"&&(e="Escape"),e=="Del"&&(e="Delete"),e=="Left"&&(e="ArrowLeft"),e=="Up"&&(e="ArrowUp"),e=="Right"&&(e="ArrowRight"),e=="Down"&&(e="ArrowDown"),e}function Li(s){let t;return s.nodeType==11?t=s.getSelection?s:s.ownerDocument:t=s,t.getSelection()}function tr(s,t){return t?s==t||s.contains(t.nodeType!=1?t.parentNode:t):!1}function Tn(s,t){if(!t.anchorNode)return !1;try{return tr(s,t.anchorNode)}catch{return !1}}function ei(s){return s.nodeType==3?Ve(s,0,s.nodeValue.length).getClientRects():s.nodeType==1?s.getClientRects():[]}function Mi(s,t,e,i){return e?Eo(s,t,e,i,-1)||Eo(s,t,e,i,1):!1}function He(s){for(var t=0;;t++)if(s=s.previousSibling,!s)return t}function Fn(s){return s.nodeType==1&&/^(DIV|P|LI|UL|OL|BLOCKQUOTE|DD|DT|H\d|SECTION|PRE)$/.test(s.nodeName)}function Eo(s,t,e,i,n){for(;;){if(s==e&&t==i)return !0;if(t==(n<0?0:re(s))){if(s.nodeName=="DIV")return !1;let r=s.parentNode;if(!r||r.nodeType!=1)return !1;t=He(s)+(n<0?0:1),s=r;}else if(s.nodeType==1){if(s=s.childNodes[t+(n<0?-1:0)],s.nodeType==1&&s.contentEditable=="false")return !1;t=n<0?re(s):0;}else return !1}}function re(s){return s.nodeType==3?s.nodeValue.length:s.childNodes.length}function Ui(s,t){let e=t?s.left:s.right;return {left:e,right:e,top:s.top,bottom:s.bottom}}function ru(s){let t=s.visualViewport;return t?{left:0,right:t.width,top:0,bottom:t.height}:{left:0,right:s.innerWidth,top:0,bottom:s.innerHeight}}function qa(s,t){let e=t.width/s.offsetWidth,i=t.height/s.offsetHeight;return (e>.995&&e<1.005||!isFinite(e)||Math.abs(t.width-s.offsetWidth)<1)&&(e=1),(i>.995&&i<1.005||!isFinite(i)||Math.abs(t.height-s.offsetHeight)<1)&&(i=1),{scaleX:e,scaleY:i}}function ou(s,t,e,i,n,r,o,l){let a=s.ownerDocument,h=a.defaultView||window;for(let c=s,f=!1;c&&!f;)if(c.nodeType==1){let u,d=c==a.body,p=1,y=1;if(d)u=ru(h);else {if(/^(fixed|sticky)$/.test(getComputedStyle(c).position)&&(f=!0),c.scrollHeight<=c.clientHeight&&c.scrollWidth<=c.clientWidth){c=c.assignedSlot||c.parentNode;continue}let x=c.getBoundingClientRect();(({scaleX:p,scaleY:y}=qa(c,x))),u={left:x.left,right:x.left+c.clientWidth*p,top:x.top,bottom:x.top+c.clientHeight*y};}let g=0,b=0;if(n=="nearest")t.top<u.top?(b=t.top-(u.top+o),e>0&&t.bottom>u.bottom+b&&(b=t.bottom-u.bottom+o)):t.bottom>u.bottom&&(b=t.bottom-u.bottom+o,e<0&&t.top-b<u.top&&(b=t.top-(u.top+o)));else {let x=t.bottom-t.top,k=u.bottom-u.top;b=(n=="center"&&x<=k?t.top+x/2-k/2:n=="start"||n=="center"&&e<0?t.top-o:t.bottom-k+o)-u.top;}if(i=="nearest"?t.left<u.left?(g=t.left-(u.left+r),e>0&&t.right>u.right+g&&(g=t.right-u.right+r)):t.right>u.right&&(g=t.right-u.right+r,e<0&&t.left<u.left+g&&(g=t.left-(u.left+r))):g=(i=="center"?t.left+(t.right-t.left)/2-(u.right-u.left)/2:i=="start"==l?t.left-r:t.right-(u.right-u.left)+r)-u.left,g||b)if(d)h.scrollBy(g,b);else {let x=0,k=0;if(b){let S=c.scrollTop;c.scrollTop+=b/y,k=(c.scrollTop-S)*y;}if(g){let S=c.scrollLeft;c.scrollLeft+=g/p,x=(c.scrollLeft-S)*p;}t={left:t.left-x,top:t.top-k,right:t.right-x,bottom:t.bottom-k},x&&Math.abs(x-g)<1&&(i="nearest"),k&&Math.abs(k-b)<1&&(n="nearest");}if(d)break;(t.top<u.top||t.bottom>u.bottom||t.left<u.left||t.right>u.right)&&(t={left:Math.max(t.left,u.left),right:Math.min(t.right,u.right),top:Math.max(t.top,u.top),bottom:Math.min(t.bottom,u.bottom)}),c=c.assignedSlot||c.parentNode;}else if(c.nodeType==11)c=c.host;else break}function lu(s){let t=s.ownerDocument,e,i;for(let n=s.parentNode;n&&!(n==t.body||e&&i);)if(n.nodeType==1)!i&&n.scrollHeight>n.clientHeight&&(i=n),!e&&n.scrollWidth>n.clientWidth&&(e=n),n=n.assignedSlot||n.parentNode;else if(n.nodeType==11)n=n.host;else break;return {x:e,y:i}}class au{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0;}eq(t){return this.anchorNode==t.anchorNode&&this.anchorOffset==t.anchorOffset&&this.focusNode==t.focusNode&&this.focusOffset==t.focusOffset}setRange(t){let{anchorNode:e,focusNode:i}=t;this.set(e,Math.min(t.anchorOffset,e?re(e):0),i,Math.min(t.focusOffset,i?re(i):0));}set(t,e,i,n){this.anchorNode=t,this.anchorOffset=e,this.focusNode=i,this.focusOffset=n;}}let Ke=null;function ja(s){if(s.setActive)return s.setActive();if(Ke)return s.focus(Ke);let t=[];for(let e=s;e&&(t.push(e,e.scrollTop,e.scrollLeft),e!=e.ownerDocument);e=e.parentNode);if(s.focus(Ke==null?{get preventScroll(){return Ke={preventScroll:!0},!0}}:void 0),!Ke){Ke=!1;for(let e=0;e<t.length;){let i=t[e++],n=t[e++],r=t[e++];i.scrollTop!=n&&(i.scrollTop=n),i.scrollLeft!=r&&(i.scrollLeft=r);}}}let Po;function Ve(s,t,e=t){let i=Po||(Po=document.createRange());return i.setEnd(s,e),i.setStart(s,t),i}function Qe(s,t,e,i){let n={key:t,code:t,keyCode:e,which:e,cancelable:!0};i&&({altKey:n.altKey,ctrlKey:n.ctrlKey,shiftKey:n.shiftKey,metaKey:n.metaKey}=i);let r=new KeyboardEvent("keydown",n);r.synthetic=!0,s.dispatchEvent(r);let o=new KeyboardEvent("keyup",n);return o.synthetic=!0,s.dispatchEvent(o),r.defaultPrevented||o.defaultPrevented}function hu(s){for(;s;){if(s&&(s.nodeType==9||s.nodeType==11&&s.host))return s;s=s.assignedSlot||s.parentNode;}return null}function Ka(s){for(;s.attributes.length;)s.removeAttributeNode(s.attributes[0]);}function cu(s,t){let e=t.focusNode,i=t.focusOffset;if(!e||t.anchorNode!=e||t.anchorOffset!=i)return !1;for(i=Math.min(i,re(e));;)if(i){if(e.nodeType!=1)return !1;let n=e.childNodes[i-1];n.contentEditable=="false"?i--:(e=n,i=re(e));}else {if(e==s)return !0;i=He(e),e=e.parentNode;}}function Ua(s){return s.scrollTop>Math.max(1,s.scrollHeight-s.clientHeight-4)}function Ga(s,t){for(let e=s,i=t;;){if(e.nodeType==3&&i>0)return {node:e,offset:i};if(e.nodeType==1&&i>0){if(e.contentEditable=="false")return null;e=e.childNodes[i-1],i=re(e);}else if(e.parentNode&&!Fn(e))i=He(e),e=e.parentNode;else return null}}function Ya(s,t){for(let e=s,i=t;;){if(e.nodeType==3&&i<e.nodeValue.length)return {node:e,offset:i};if(e.nodeType==1&&i<e.childNodes.length){if(e.contentEditable=="false")return null;e=e.childNodes[i],i=0;}else if(e.parentNode&&!Fn(e))i=He(e)+1,e=e.parentNode;else return null}}class ut{constructor(t,e,i=!0){this.node=t,this.offset=e,this.precise=i;}static before(t,e){return new ut(t.parentNode,He(t),e)}static after(t,e){return new ut(t.parentNode,He(t)+1,e)}}const Hr=[];class U{constructor(){this.parent=null,this.dom=null,this.flags=2;}get overrideDOMText(){return null}get posAtStart(){return this.parent?this.parent.posBefore(this):0}get posAtEnd(){return this.posAtStart+this.length}posBefore(t){let e=this.posAtStart;for(let i of this.children){if(i==t)return e;e+=i.length+i.breakAfter;}throw new RangeError("Invalid child in posBefore")}posAfter(t){return this.posBefore(t)+t.length}sync(t,e){if(this.flags&2){let i=this.dom,n=null,r;for(let o of this.children){if(o.flags&7){if(!o.dom&&(r=n?n.nextSibling:i.firstChild)){let l=U.get(r);(!l||!l.parent&&l.canReuseDOM(o))&&o.reuseDOM(r);}o.sync(t,e),o.flags&=-8;}if(r=n?n.nextSibling:i.firstChild,e&&!e.written&&e.node==i&&r!=o.dom&&(e.written=!0),o.dom.parentNode==i)for(;r&&r!=o.dom;)r=Lo(r);else i.insertBefore(o.dom,r);n=o.dom;}for(r=n?n.nextSibling:i.firstChild,r&&e&&e.node==i&&(e.written=!0);r;)r=Lo(r);}else if(this.flags&1)for(let i of this.children)i.flags&7&&(i.sync(t,e),i.flags&=-8);}reuseDOM(t){}localPosFromDOM(t,e){let i;if(t==this.dom)i=this.dom.childNodes[e];else {let n=re(t)==0?0:e==0?-1:1;for(;;){let r=t.parentNode;if(r==this.dom)break;n==0&&r.firstChild!=r.lastChild&&(t==r.firstChild?n=-1:n=1),t=r;}n<0?i=t:i=t.nextSibling;}if(i==this.dom.firstChild)return 0;for(;i&&!U.get(i);)i=i.nextSibling;if(!i)return this.length;for(let n=0,r=0;;n++){let o=this.children[n];if(o.dom==i)return r;r+=o.length+o.breakAfter;}}domBoundsAround(t,e,i=0){let n=-1,r=-1,o=-1,l=-1;for(let a=0,h=i,c=i;a<this.children.length;a++){let f=this.children[a],u=h+f.length;if(h<t&&u>e)return f.domBoundsAround(t,e,h);if(u>=t&&n==-1&&(n=a,r=h),h>e&&f.dom.parentNode==this.dom){o=a,l=c;break}c=u,h=u+f.breakAfter;}return {from:r,to:l<0?i+this.length:l,startDOM:(n?this.children[n-1].dom.nextSibling:null)||this.dom.firstChild,endDOM:o<this.children.length&&o>=0?this.children[o].dom:null}}markDirty(t=!1){this.flags|=2,this.markParentsDirty(t);}markParentsDirty(t){for(let e=this.parent;e;e=e.parent){if(t&&(e.flags|=2),e.flags&1)return;e.flags|=1,t=!1;}}setParent(t){this.parent!=t&&(this.parent=t,this.flags&7&&this.markParentsDirty(!0));}setDOM(t){this.dom!=t&&(this.dom&&(this.dom.cmView=null),this.dom=t,t.cmView=this);}get rootView(){for(let t=this;;){let e=t.parent;if(!e)return t;t=e;}}replaceChildren(t,e,i=Hr){this.markDirty();for(let n=t;n<e;n++){let r=this.children[n];r.parent==this&&i.indexOf(r)<0&&r.destroy();}i.length<250?this.children.splice(t,e-t,...i):this.children=[].concat(this.children.slice(0,t),i,this.children.slice(e));for(let n=0;n<i.length;n++)i[n].setParent(this);}ignoreMutation(t){return !1}ignoreEvent(t){return !1}childCursor(t=this.length){return new Ja(this.children,t,this.children.length)}childPos(t,e=1){return this.childCursor().findPos(t,e)}toString(){let t=this.constructor.name.replace("View","");return t+(this.children.length?"("+this.children.join()+")":this.length?"["+(t=="Text"?this.text:this.length)+"]":"")+(this.breakAfter?"#":"")}static get(t){return t.cmView}get isEditable(){return !0}get isWidget(){return !1}get isHidden(){return !1}merge(t,e,i,n,r,o){return !1}become(t){return !1}canReuseDOM(t){return t.constructor==this.constructor&&!((this.flags|t.flags)&8)}getSide(){return 0}destroy(){for(let t of this.children)t.parent==this&&t.destroy();this.parent=null;}}U.prototype.breakAfter=0;function Lo(s){let t=s.nextSibling;return s.parentNode.removeChild(s),t}class Ja{constructor(t,e,i){this.children=t,this.pos=e,this.i=i,this.off=0;}findPos(t,e=1){for(;;){if(t>this.pos||t==this.pos&&(e>0||this.i==0||this.children[this.i-1].breakAfter))return this.off=t-this.pos,this;let i=this.children[--this.i];this.pos-=i.length+i.breakAfter;}}}function Xa(s,t,e,i,n,r,o,l,a){let{children:h}=s,c=h.length?h[t]:null,f=r.length?r[r.length-1]:null,u=f?f.breakAfter:o;if(!(t==i&&c&&!o&&!u&&r.length<2&&c.merge(e,n,r.length?f:null,e==0,l,a))){if(i<h.length){let d=h[i];d&&(n<d.length||d.breakAfter&&f?.breakAfter)?(t==i&&(d=d.split(n),n=0),!u&&f&&d.merge(0,n,f,!0,0,a)?r[r.length-1]=d:((n||d.children.length&&!d.children[0].length)&&d.merge(0,n,null,!1,0,a),r.push(d))):d?.breakAfter&&(f?f.breakAfter=1:o=1),i++;}for(c&&(c.breakAfter=o,e>0&&(!o&&r.length&&c.merge(e,c.length,r[0],!1,l,0)?c.breakAfter=r.shift().breakAfter:(e<c.length||c.children.length&&c.children[c.children.length-1].length==0)&&c.merge(e,c.length,null,!1,l,0),t++));t<i&&r.length;)if(h[i-1].become(r[r.length-1]))i--,r.pop(),a=r.length?0:l;else if(h[t].become(r[0]))t++,r.shift(),l=r.length?0:a;else break;!r.length&&t&&i<h.length&&!h[t-1].breakAfter&&h[i].merge(0,0,h[t-1],!1,l,a)&&t--,(t<i||r.length)&&s.replaceChildren(t,i,r);}}function Qa(s,t,e,i,n,r){let o=s.childCursor(),{i:l,off:a}=o.findPos(e,1),{i:h,off:c}=o.findPos(t,-1),f=t-e;for(let u of i)f+=u.length;s.length+=f,Xa(s,h,c,l,a,i,0,n,r);}let vt=typeof navigator<"u"?navigator:{userAgent:"",vendor:"",platform:""},er=typeof document<"u"?document:{documentElement:{style:{}}};const ir=/Edge\/(\d+)/.exec(vt.userAgent),Za=/MSIE \d/.test(vt.userAgent),nr=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(vt.userAgent),Zn=!!(Za||nr||ir),Ro=!Zn&&/gecko\/(\d+)/i.test(vt.userAgent),us=!Zn&&/Chrome\/(\d+)/.exec(vt.userAgent),Io="webkitFontSmoothing"in er.documentElement.style,$a=!Zn&&/Apple Computer/.test(vt.vendor),No=$a&&(/Mobile\/\w+/.test(vt.userAgent)||vt.maxTouchPoints>2);var D={mac:No||/Mac/.test(vt.platform),windows:/Win/.test(vt.platform),linux:/Linux|X11/.test(vt.platform),ie:Zn,ie_version:Za?er.documentMode||6:nr?+nr[1]:ir?+ir[1]:0,gecko:Ro,gecko_version:Ro?+(/Firefox\/(\d+)/.exec(vt.userAgent)||[0,0])[1]:0,chrome:!!us,chrome_version:us?+us[1]:0,ios:No,android:/Android\b/.test(vt.userAgent),webkit:Io,safari:$a,webkit_version:Io?+(/\bAppleWebKit\/(\d+)/.exec(vt.userAgent)||[0,0])[1]:0,tabSize:er.documentElement.style.tabSize!=null?"tab-size":"-moz-tab-size"};const fu=256;class jt extends U{constructor(t){super(),this.text=t;}get length(){return this.text.length}createDOM(t){this.setDOM(t||document.createTextNode(this.text));}sync(t,e){this.dom||this.createDOM(),this.dom.nodeValue!=this.text&&(e&&e.node==this.dom&&(e.written=!0),this.dom.nodeValue=this.text);}reuseDOM(t){t.nodeType==3&&this.createDOM(t);}merge(t,e,i){return this.flags&8||i&&(!(i instanceof jt)||this.length-(e-t)+i.length>fu||i.flags&8)?!1:(this.text=this.text.slice(0,t)+(i?i.text:"")+this.text.slice(e),this.markDirty(),!0)}split(t){let e=new jt(this.text.slice(t));return this.text=this.text.slice(0,t),this.markDirty(),e.flags|=this.flags&8,e}localPosFromDOM(t,e){return t==this.dom?e:e?this.text.length:0}domAtPos(t){return new ut(this.dom,t)}domBoundsAround(t,e,i){return {from:i,to:i+this.length,startDOM:this.dom,endDOM:this.dom.nextSibling}}coordsAt(t,e){return uu(this.dom,t,e)}}class ue extends U{constructor(t,e=[],i=0){super(),this.mark=t,this.children=e,this.length=i;for(let n of e)n.setParent(this);}setAttrs(t){if(Ka(t),this.mark.class&&(t.className=this.mark.class),this.mark.attrs)for(let e in this.mark.attrs)t.setAttribute(e,this.mark.attrs[e]);return t}canReuseDOM(t){return super.canReuseDOM(t)&&!((this.flags|t.flags)&8)}reuseDOM(t){t.nodeName==this.mark.tagName.toUpperCase()&&(this.setDOM(t),this.flags|=6);}sync(t,e){this.dom?this.flags&4&&this.setAttrs(this.dom):this.setDOM(this.setAttrs(document.createElement(this.mark.tagName))),super.sync(t,e);}merge(t,e,i,n,r,o){return i&&(!(i instanceof ue&&i.mark.eq(this.mark))||t&&r<=0||e<this.length&&o<=0)?!1:(Qa(this,t,e,i?i.children.slice():[],r-1,o-1),this.markDirty(),!0)}split(t){let e=[],i=0,n=-1,r=0;for(let l of this.children){let a=i+l.length;a>t&&e.push(i<t?l.split(t-i):l),n<0&&i>=t&&(n=r),i=a,r++;}let o=this.length-t;return this.length=t,n>-1&&(this.children.length=n,this.markDirty()),new ue(this.mark,e,o)}domAtPos(t){return th(this,t)}coordsAt(t,e){return ih(this,t,e)}}function uu(s,t,e){let i=s.nodeValue.length;t>i&&(t=i);let n=t,r=t,o=0;t==0&&e<0||t==i&&e>=0?D.chrome||D.gecko||(t?(n--,o=1):r<i&&(r++,o=-1)):e<0?n--:r<i&&r++;let l=Ve(s,n,r).getClientRects();if(!l.length)return null;let a=l[(o?o<0:e>=0)?0:l.length-1];return D.safari&&!o&&a.width==0&&(a=Array.prototype.find.call(l,h=>h.width)||a),o?Ui(a,o<0):a||null}class be extends U{static create(t,e,i){return new be(t,e,i)}constructor(t,e,i){super(),this.widget=t,this.length=e,this.side=i,this.prevWidget=null;}split(t){let e=be.create(this.widget,this.length-t,this.side);return this.length-=t,e}sync(t){(!this.dom||!this.widget.updateDOM(this.dom,t))&&(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"));}getSide(){return this.side}merge(t,e,i,n,r,o){return i&&(!(i instanceof be)||!this.widget.compare(i.widget)||t>0&&r<=0||e<this.length&&o<=0)?!1:(this.length=t+(i?i.length:0)+(this.length-e),!0)}become(t){return t instanceof be&&t.side==this.side&&this.widget.constructor==t.widget.constructor?(this.widget.compare(t.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,!0):!1}ignoreMutation(){return !0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get overrideDOMText(){if(this.length==0)return H.empty;let t=this;for(;t.parent;)t=t.parent;let{view:e}=t,i=e&&e.state.doc,n=this.posAtStart;return i?i.slice(n,n+this.length):H.empty}domAtPos(t){return (this.length?t==0:this.side>0)?ut.before(this.dom):ut.after(this.dom,t==this.length)}domBoundsAround(){return null}coordsAt(t,e){let i=this.widget.coordsAt(this.dom,t,e);if(i)return i;let n=this.dom.getClientRects(),r=null;if(!n.length)return null;let o=this.side?this.side<0:t>0;for(let l=o?n.length-1:0;r=n[l],!(t>0?l==0:l==n.length-1||r.top<r.bottom);l+=o?-1:1);return Ui(r,!o)}get isEditable(){return !1}get isWidget(){return !0}get isHidden(){return this.widget.isHidden}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom);}}class ii extends U{constructor(t){super(),this.side=t;}get length(){return 0}merge(){return !1}become(t){return t instanceof ii&&t.side==this.side}split(){return new ii(this.side)}sync(){if(!this.dom){let t=document.createElement("img");t.className="cm-widgetBuffer",t.setAttribute("aria-hidden","true"),this.setDOM(t);}}getSide(){return this.side}domAtPos(t){return this.side>0?ut.before(this.dom):ut.after(this.dom)}localPosFromDOM(){return 0}domBoundsAround(){return null}coordsAt(t){return this.dom.getBoundingClientRect()}get overrideDOMText(){return H.empty}get isHidden(){return !0}}jt.prototype.children=be.prototype.children=ii.prototype.children=Hr;function th(s,t){let e=s.dom,{children:i}=s,n=0;for(let r=0;n<i.length;n++){let o=i[n],l=r+o.length;if(!(l==r&&o.getSide()<=0)){if(t>r&&t<l&&o.dom.parentNode==e)return o.domAtPos(t-r);if(t<=r)break;r=l;}}for(let r=n;r>0;r--){let o=i[r-1];if(o.dom.parentNode==e)return o.domAtPos(o.length)}for(let r=n;r<i.length;r++){let o=i[r];if(o.dom.parentNode==e)return o.domAtPos(0)}return new ut(e,0)}function eh(s,t,e){let i,{children:n}=s;e>0&&t instanceof ue&&n.length&&(i=n[n.length-1])instanceof ue&&i.mark.eq(t.mark)?eh(i,t.children[0],e-1):(n.push(t),t.setParent(s)),s.length+=t.length;}function ih(s,t,e){let i=null,n=-1,r=null,o=-1;function l(h,c){for(let f=0,u=0;f<h.children.length&&u<=c;f++){let d=h.children[f],p=u+d.length;p>=c&&(d.children.length?l(d,c-u):(!r||r.isHidden&&e>0)&&(p>c||u==p&&d.getSide()>0)?(r=d,o=c-u):(u<c||u==p&&d.getSide()<0&&!d.isHidden)&&(i=d,n=c-u)),u=p;}}l(s,t);let a=(e<0?i:r)||i||r;return a?a.coordsAt(Math.max(0,a==i?n:o),e):du(s)}function du(s){let t=s.dom.lastChild;if(!t)return s.dom.getBoundingClientRect();let e=ei(t);return e[e.length-1]||null}function sr(s,t){for(let e in s)e=="class"&&t.class?t.class+=" "+s.class:e=="style"&&t.style?t.style+=";"+s.style:t[e]=s[e];return t}const Fo=Object.create(null);function Hn(s,t,e){if(s==t)return !0;s||(s=Fo),t||(t=Fo);let i=Object.keys(s),n=Object.keys(t);if(i.length-(e&&i.indexOf(e)>-1?1:0)!=n.length-(e&&n.indexOf(e)>-1?1:0))return !1;for(let r of i)if(r!=e&&(n.indexOf(r)==-1||s[r]!==t[r]))return !1;return !0}function rr(s,t,e){let i=!1;if(t)for(let n in t)e&&n in e||(i=!0,n=="style"?s.style.cssText="":s.removeAttribute(n));if(e)for(let n in e)t&&t[n]==e[n]||(i=!0,n=="style"?s.style.cssText=e[n]:s.setAttribute(n,e[n]));return i}function pu(s){let t=Object.create(null);for(let e=0;e<s.attributes.length;e++){let i=s.attributes[e];t[i.name]=i.value;}return t}class oe{eq(t){return !1}updateDOM(t,e){return !1}compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}get estimatedHeight(){return -1}get lineBreaks(){return 0}ignoreEvent(t){return !0}coordsAt(t,e,i){return null}get isHidden(){return !1}get editable(){return !1}destroy(t){}}var yt=function(s){return s[s.Text=0]="Text",s[s.WidgetBefore=1]="WidgetBefore",s[s.WidgetAfter=2]="WidgetAfter",s[s.WidgetRange=3]="WidgetRange",s}(yt||(yt={}));class I extends Fe{constructor(t,e,i,n){super(),this.startSide=t,this.endSide=e,this.widget=i,this.spec=n;}get heightRelevant(){return !1}static mark(t){return new Gi(t)}static widget(t){let e=Math.max(-1e4,Math.min(1e4,t.side||0)),i=!!t.block;return e+=i&&!t.inlineOrder?e>0?3e8:-4e8:e>0?1e8:-1e8,new Se(t,e,e,i,t.widget||null,!1)}static replace(t){let e=!!t.block,i,n;if(t.isBlockGap)i=-5e8,n=4e8;else {let{start:r,end:o}=nh(t,e);i=(r?e?-3e8:-1:5e8)-1,n=(o?e?2e8:1:-6e8)+1;}return new Se(t,i,n,e,t.widget||null,!0)}static line(t){return new Yi(t)}static set(t,e=!1){return z.of(t,e)}hasHeight(){return this.widget?this.widget.estimatedHeight>-1:!1}}I.none=z.empty;class Gi extends I{constructor(t){let{start:e,end:i}=nh(t);super(e?-1:5e8,i?1:-6e8,null,t),this.tagName=t.tagName||"span",this.class=t.class||"",this.attrs=t.attributes||null;}eq(t){var e,i;return this==t||t instanceof Gi&&this.tagName==t.tagName&&(this.class||((e=this.attrs)===null||e===void 0?void 0:e.class))==(t.class||((i=t.attrs)===null||i===void 0?void 0:i.class))&&Hn(this.attrs,t.attrs,"class")}range(t,e=t){if(t>=e)throw new RangeError("Mark decorations may not be empty");return super.range(t,e)}}Gi.prototype.point=!1;class Yi extends I{constructor(t){super(-2e8,-2e8,null,t);}eq(t){return t instanceof Yi&&this.spec.class==t.spec.class&&Hn(this.spec.attributes,t.spec.attributes)}range(t,e=t){if(e!=t)throw new RangeError("Line decoration ranges must be zero-length");return super.range(t,e)}}Yi.prototype.mapMode=at.TrackBefore;Yi.prototype.point=!0;class Se extends I{constructor(t,e,i,n,r,o){super(e,i,r,t),this.block=n,this.isReplace=o,this.mapMode=n?e<=0?at.TrackBefore:at.TrackAfter:at.TrackDel;}get type(){return this.startSide!=this.endSide?yt.WidgetRange:this.startSide<=0?yt.WidgetBefore:yt.WidgetAfter}get heightRelevant(){return this.block||!!this.widget&&(this.widget.estimatedHeight>=5||this.widget.lineBreaks>0)}eq(t){return t instanceof Se&&mu(this.widget,t.widget)&&this.block==t.block&&this.startSide==t.startSide&&this.endSide==t.endSide}range(t,e=t){if(this.isReplace&&(t>e||t==e&&this.startSide>0&&this.endSide<=0))throw new RangeError("Invalid range for replacement decoration");if(!this.isReplace&&e!=t)throw new RangeError("Widget decorations can only have zero-length ranges");return super.range(t,e)}}Se.prototype.point=!0;function nh(s,t=!1){let{inclusiveStart:e,inclusiveEnd:i}=s;return e==null&&(e=s.inclusive),i==null&&(i=s.inclusive),{start:e??t,end:i??t}}function mu(s,t){return s==t||!!(s&&t&&s.compare(t))}function On(s,t,e,i=0){let n=e.length-1;n>=0&&e[n]+i>=s?e[n]=Math.max(e[n],t):e.push(s,t);}class $ extends U{constructor(){super(...arguments),this.children=[],this.length=0,this.prevAttrs=void 0,this.attrs=null,this.breakAfter=0;}merge(t,e,i,n,r,o){if(i){if(!(i instanceof $))return !1;this.dom||i.transferDOM(this);}return n&&this.setDeco(i?i.attrs:null),Qa(this,t,e,i?i.children.slice():[],r,o),!0}split(t){let e=new $;if(e.breakAfter=this.breakAfter,this.length==0)return e;let{i,off:n}=this.childPos(t);n&&(e.append(this.children[i].split(n),0),this.children[i].merge(n,this.children[i].length,null,!1,0,0),i++);for(let r=i;r<this.children.length;r++)e.append(this.children[r],0);for(;i>0&&this.children[i-1].length==0;)this.children[--i].destroy();return this.children.length=i,this.markDirty(),this.length=t,e}transferDOM(t){this.dom&&(this.markDirty(),t.setDOM(this.dom),t.prevAttrs=this.prevAttrs===void 0?this.attrs:this.prevAttrs,this.prevAttrs=void 0,this.dom=null);}setDeco(t){Hn(this.attrs,t)||(this.dom&&(this.prevAttrs=this.attrs,this.markDirty()),this.attrs=t);}append(t,e){eh(this,t,e);}addLineDeco(t){let e=t.spec.attributes,i=t.spec.class;e&&(this.attrs=sr(e,this.attrs||{})),i&&(this.attrs=sr({class:i},this.attrs||{}));}domAtPos(t){return th(this,t)}reuseDOM(t){t.nodeName=="DIV"&&(this.setDOM(t),this.flags|=6);}sync(t,e){var i;this.dom?this.flags&4&&(Ka(this.dom),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0):(this.setDOM(document.createElement("div")),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0),this.prevAttrs!==void 0&&(rr(this.dom,this.prevAttrs,this.attrs),this.dom.classList.add("cm-line"),this.prevAttrs=void 0),super.sync(t,e);let n=this.dom.lastChild;for(;n&&U.get(n)instanceof ue;)n=n.lastChild;if(!n||!this.length||n.nodeName!="BR"&&((i=U.get(n))===null||i===void 0?void 0:i.isEditable)==!1&&(!D.ios||!this.children.some(r=>r instanceof jt))){let r=document.createElement("BR");r.cmIgnore=!0,this.dom.appendChild(r);}}measureTextSize(){if(this.children.length==0||this.length>20)return null;let t=0,e;for(let i of this.children){if(!(i instanceof jt)||/[^ -~]/.test(i.text))return null;let n=ei(i.dom);if(n.length!=1)return null;t+=n[0].width,e=n[0].height;}return t?{lineHeight:this.dom.getBoundingClientRect().height,charWidth:t/this.length,textHeight:e}:null}coordsAt(t,e){let i=ih(this,t,e);if(!this.children.length&&i&&this.parent){let{heightOracle:n}=this.parent.view.viewState,r=i.bottom-i.top;if(Math.abs(r-n.lineHeight)<2&&n.textHeight<r){let o=(r-n.textHeight)/2;return {top:i.top+o,bottom:i.bottom-o,left:i.left,right:i.left}}}return i}become(t){return t instanceof $&&this.children.length==0&&t.children.length==0&&Hn(this.attrs,t.attrs)&&this.breakAfter==t.breakAfter}covers(){return !0}static find(t,e){for(let i=0,n=0;i<t.children.length;i++){let r=t.children[i],o=n+r.length;if(o>=e){if(r instanceof $)return r;if(o>e)break}n=o+r.breakAfter;}return null}}class ce extends U{constructor(t,e,i){super(),this.widget=t,this.length=e,this.deco=i,this.breakAfter=0,this.prevWidget=null;}merge(t,e,i,n,r,o){return i&&(!(i instanceof ce)||!this.widget.compare(i.widget)||t>0&&r<=0||e<this.length&&o<=0)?!1:(this.length=t+(i?i.length:0)+(this.length-e),!0)}domAtPos(t){return t==0?ut.before(this.dom):ut.after(this.dom,t==this.length)}split(t){let e=this.length-t;this.length=t;let i=new ce(this.widget,e,this.deco);return i.breakAfter=this.breakAfter,i}get children(){return Hr}sync(t){(!this.dom||!this.widget.updateDOM(this.dom,t))&&(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"));}get overrideDOMText(){return this.parent?this.parent.view.state.doc.slice(this.posAtStart,this.posAtEnd):H.empty}domBoundsAround(){return null}become(t){return t instanceof ce&&t.widget.constructor==this.widget.constructor?(t.widget.compare(this.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,this.deco=t.deco,this.breakAfter=t.breakAfter,!0):!1}ignoreMutation(){return !0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get isEditable(){return !1}get isWidget(){return !0}coordsAt(t,e){let i=this.widget.coordsAt(this.dom,t,e);return i||(this.widget instanceof or?null:Ui(this.dom.getBoundingClientRect(),this.length?t==0:e<=0))}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom);}covers(t){let{startSide:e,endSide:i}=this.deco;return e==i?!1:t<0?e<0:i>0}}class or extends oe{constructor(t){super(),this.height=t;}toDOM(){let t=document.createElement("div");return t.className="cm-gap",this.updateDOM(t),t}eq(t){return t.height==this.height}updateDOM(t){return t.style.height=this.height+"px",!0}get editable(){return !0}get estimatedHeight(){return this.height}ignoreEvent(){return !1}}class Di{constructor(t,e,i,n){this.doc=t,this.pos=e,this.end=i,this.disallowBlockEffectsFor=n,this.content=[],this.curLine=null,this.breakAtStart=0,this.pendingBuffer=0,this.bufferMarks=[],this.atCursorPos=!0,this.openStart=-1,this.openEnd=-1,this.text="",this.textOff=0,this.cursor=t.iter(),this.skip=e;}posCovered(){if(this.content.length==0)return !this.breakAtStart&&this.doc.lineAt(this.pos).from!=this.pos;let t=this.content[this.content.length-1];return !(t.breakAfter||t instanceof ce&&t.deco.endSide<0)}getLine(){return this.curLine||(this.content.push(this.curLine=new $),this.atCursorPos=!0),this.curLine}flushBuffer(t=this.bufferMarks){this.pendingBuffer&&(this.curLine.append(rn(new ii(-1),t),t.length),this.pendingBuffer=0);}addBlockWidget(t){this.flushBuffer(),this.curLine=null,this.content.push(t);}finish(t){this.pendingBuffer&&t<=this.bufferMarks.length?this.flushBuffer():this.pendingBuffer=0,!this.posCovered()&&!(t&&this.content.length&&this.content[this.content.length-1]instanceof ce)&&this.getLine();}buildText(t,e,i){for(;t>0;){if(this.textOff==this.text.length){let{value:r,lineBreak:o,done:l}=this.cursor.next(this.skip);if(this.skip=0,l)throw new Error("Ran out of text content when drawing inline views");if(o){this.posCovered()||this.getLine(),this.content.length?this.content[this.content.length-1].breakAfter=1:this.breakAtStart=1,this.flushBuffer(),this.curLine=null,this.atCursorPos=!0,t--;continue}else this.text=r,this.textOff=0;}let n=Math.min(this.text.length-this.textOff,t,512);this.flushBuffer(e.slice(e.length-i)),this.getLine().append(rn(new jt(this.text.slice(this.textOff,this.textOff+n)),e),i),this.atCursorPos=!0,this.textOff+=n,t-=n,i=0;}}span(t,e,i,n){this.buildText(e-t,i,n),this.pos=e,this.openStart<0&&(this.openStart=n);}point(t,e,i,n,r,o){if(this.disallowBlockEffectsFor[o]&&i instanceof Se){if(i.block)throw new RangeError("Block decorations may not be specified via plugins");if(e>this.doc.lineAt(this.pos).to)throw new RangeError("Decorations that replace line breaks may not be specified via plugins")}let l=e-t;if(i instanceof Se)if(i.block)i.startSide>0&&!this.posCovered()&&this.getLine(),this.addBlockWidget(new ce(i.widget||ni.block,l,i));else {let a=be.create(i.widget||ni.inline,l,l?0:i.startSide),h=this.atCursorPos&&!a.isEditable&&r<=n.length&&(t<e||i.startSide>0),c=!a.isEditable&&(t<e||r>n.length||i.startSide<=0),f=this.getLine();this.pendingBuffer==2&&!h&&!a.isEditable&&(this.pendingBuffer=0),this.flushBuffer(n),h&&(f.append(rn(new ii(1),n),r),r=n.length+Math.max(0,r-n.length)),f.append(rn(a,n),r),this.atCursorPos=c,this.pendingBuffer=c?t<e||r>n.length?1:2:0,this.pendingBuffer&&(this.bufferMarks=n.slice());}else this.doc.lineAt(this.pos).from==this.pos&&this.getLine().addLineDeco(i);l&&(this.textOff+l<=this.text.length?this.textOff+=l:(this.skip+=l-(this.text.length-this.textOff),this.text="",this.textOff=0),this.pos=e),this.openStart<0&&(this.openStart=r);}static build(t,e,i,n,r){let o=new Di(t,e,i,r);return o.openEnd=z.spans(n,e,i,o),o.openStart<0&&(o.openStart=o.openEnd),o.finish(o.openEnd),o}}function rn(s,t){for(let e of t)s=new ue(e,[s],s.length);return s}class ni extends oe{constructor(t){super(),this.tag=t;}eq(t){return t.tag==this.tag}toDOM(){return document.createElement(this.tag)}updateDOM(t){return t.nodeName.toLowerCase()==this.tag}get isHidden(){return !0}}ni.inline=new ni("span");ni.block=new ni("div");var J=function(s){return s[s.LTR=0]="LTR",s[s.RTL=1]="RTL",s}(J||(J={}));const We=J.LTR,Vr=J.RTL;function sh(s){let t=[];for(let e=0;e<s.length;e++)t.push(1<<+s[e]);return t}const gu=sh("88888888888888888888888888888888888666888888787833333333337888888000000000000000000000000008888880000000000000000000000000088888888888888888888888888888888888887866668888088888663380888308888800000000000000000000000800000000000000000000000000000008"),yu=sh("4444448826627288999999999992222222222222222222222222222222222222222222222229999999999999999999994444444444644222822222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222999999949999999229989999223333333333"),lr=Object.create(null),Yt=[];for(let s of ["()","[]","{}"]){let t=s.charCodeAt(0),e=s.charCodeAt(1);lr[t]=e,lr[e]=-t;}function rh(s){return s<=247?gu[s]:1424<=s&&s<=1524?2:1536<=s&&s<=1785?yu[s-1536]:1774<=s&&s<=2220?4:8192<=s&&s<=8204?256:64336<=s&&s<=65023?4:1}const bu=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\ufb50-\ufdff]/;class we{get dir(){return this.level%2?Vr:We}constructor(t,e,i){this.from=t,this.to=e,this.level=i;}side(t,e){return this.dir==e==t?this.to:this.from}forward(t,e){return t==(this.dir==e)}static find(t,e,i,n){let r=-1;for(let o=0;o<t.length;o++){let l=t[o];if(l.from<=e&&l.to>=e){if(l.level==i)return o;(r<0||(n!=0?n<0?l.from<e:l.to>e:t[r].level>l.level))&&(r=o);}}if(r<0)throw new RangeError("Index out of range");return r}}function oh(s,t){if(s.length!=t.length)return !1;for(let e=0;e<s.length;e++){let i=s[e],n=t[e];if(i.from!=n.from||i.to!=n.to||i.direction!=n.direction||!oh(i.inner,n.inner))return !1}return !0}const K=[];function wu(s,t,e,i,n){for(let r=0;r<=i.length;r++){let o=r?i[r-1].to:t,l=r<i.length?i[r].from:e,a=r?256:n;for(let h=o,c=a,f=a;h<l;h++){let u=rh(s.charCodeAt(h));u==512?u=c:u==8&&f==4&&(u=16),K[h]=u==4?2:u,u&7&&(f=u),c=u;}for(let h=o,c=a,f=a;h<l;h++){let u=K[h];if(u==128)h<l-1&&c==K[h+1]&&c&24?u=K[h]=c:K[h]=256;else if(u==64){let d=h+1;for(;d<l&&K[d]==64;)d++;let p=h&&c==8||d<e&&K[d]==8?f==1?1:8:256;for(let y=h;y<d;y++)K[y]=p;h=d-1;}else u==8&&f==1&&(K[h]=1);c=u,u&7&&(f=u);}}}function xu(s,t,e,i,n){let r=n==1?2:1;for(let o=0,l=0,a=0;o<=i.length;o++){let h=o?i[o-1].to:t,c=o<i.length?i[o].from:e;for(let f=h,u,d,p;f<c;f++)if(d=lr[u=s.charCodeAt(f)])if(d<0){for(let y=l-3;y>=0;y-=3)if(Yt[y+1]==-d){let g=Yt[y+2],b=g&2?n:g&4?g&1?r:n:0;b&&(K[f]=K[Yt[y]]=b),l=y;break}}else {if(Yt.length==189)break;Yt[l++]=f,Yt[l++]=u,Yt[l++]=a;}else if((p=K[f])==2||p==1){let y=p==n;a=y?0:1;for(let g=l-3;g>=0;g-=3){let b=Yt[g+2];if(b&2)break;if(y)Yt[g+2]|=2;else {if(b&4)break;Yt[g+2]|=4;}}}}}function vu(s,t,e,i){for(let n=0,r=i;n<=e.length;n++){let o=n?e[n-1].to:s,l=n<e.length?e[n].from:t;for(let a=o;a<l;){let h=K[a];if(h==256){let c=a+1;for(;;)if(c==l){if(n==e.length)break;c=e[n++].to,l=n<e.length?e[n].from:t;}else if(K[c]==256)c++;else break;let f=r==1,u=(c<t?K[c]:i)==1,d=f==u?f?1:2:i;for(let p=c,y=n,g=y?e[y-1].to:s;p>a;)p==g&&(p=e[--y].from,g=y?e[y-1].to:s),K[--p]=d;a=c;}else r=h,a++;}}}function ar(s,t,e,i,n,r,o){let l=i%2?2:1;if(i%2==n%2)for(let a=t,h=0;a<e;){let c=!0,f=!1;if(h==r.length||a<r[h].from){let y=K[a];y!=l&&(c=!1,f=y==16);}let u=!c&&l==1?[]:null,d=c?i:i+1,p=a;t:for(;;)if(h<r.length&&p==r[h].from){if(f)break t;let y=r[h];if(!c)for(let g=y.to,b=h+1;;){if(g==e)break t;if(b<r.length&&r[b].from==g)g=r[b++].to;else {if(K[g]==l)break t;break}}if(h++,u)u.push(y);else {y.from>a&&o.push(new we(a,y.from,d));let g=y.direction==We!=!(d%2);hr(s,g?i+1:i,n,y.inner,y.from,y.to,o),a=y.to;}p=y.to;}else {if(p==e||(c?K[p]!=l:K[p]==l))break;p++;}u?ar(s,a,p,i+1,n,u,o):a<p&&o.push(new we(a,p,d)),a=p;}else for(let a=e,h=r.length;a>t;){let c=!0,f=!1;if(!h||a>r[h-1].to){let y=K[a-1];y!=l&&(c=!1,f=y==16);}let u=!c&&l==1?[]:null,d=c?i:i+1,p=a;t:for(;;)if(h&&p==r[h-1].to){if(f)break t;let y=r[--h];if(!c)for(let g=y.from,b=h;;){if(g==t)break t;if(b&&r[b-1].to==g)g=r[--b].from;else {if(K[g-1]==l)break t;break}}if(u)u.push(y);else {y.to<a&&o.push(new we(y.to,a,d));let g=y.direction==We!=!(d%2);hr(s,g?i+1:i,n,y.inner,y.from,y.to,o),a=y.from;}p=y.from;}else {if(p==t||(c?K[p-1]!=l:K[p-1]==l))break;p--;}u?ar(s,p,a,i+1,n,u,o):p<a&&o.push(new we(p,a,d)),a=p;}}function hr(s,t,e,i,n,r,o){let l=t%2?2:1;wu(s,n,r,i,l),xu(s,n,r,i,l),vu(n,r,i,l),ar(s,n,r,t,e,i,o);}function ku(s,t,e){if(!s)return [new we(0,0,t==Vr?1:0)];if(t==We&&!e.length&&!bu.test(s))return lh(s.length);if(e.length)for(;s.length>K.length;)K[K.length]=256;let i=[],n=t==We?0:1;return hr(s,n,n,e,0,s.length,i),i}function lh(s){return [new we(0,s,0)]}let ah="";function Su(s,t,e,i,n){var r;let o=i.head-s.from,l=we.find(t,o,(r=i.bidiLevel)!==null&&r!==void 0?r:-1,i.assoc),a=t[l],h=a.side(n,e);if(o==h){let u=l+=n?1:-1;if(u<0||u>=t.length)return null;a=t[l=u],o=a.side(!n,e),h=a.side(n,e);}let c=gt(s.text,o,a.forward(n,e));(c<a.from||c>a.to)&&(c=h),ah=s.text.slice(Math.min(o,c),Math.max(o,c));let f=l==(n?t.length-1:0)?null:t[l+(n?1:-1)];return f&&c==h&&f.level+(n?0:1)<a.level?v.cursor(f.side(!n,e)+s.from,f.forward(n,e)?1:-1,f.level):v.cursor(c+s.from,a.forward(n,e)?-1:1,a.level)}function Cu(s,t,e){for(let i=t;i<e;i++){let n=rh(s.charCodeAt(i));if(n==1)return We;if(n==2||n==4)return Vr}return We}const hh=T.define(),ch=T.define(),fh=T.define(),uh=T.define(),cr=T.define(),dh=T.define(),ph=T.define(),Wr=T.define(),_r=T.define(),mh=T.define({combine:s=>s.some(t=>t)}),gh=T.define({combine:s=>s.some(t=>t)}),yh=T.define();class Ze{constructor(t,e="nearest",i="nearest",n=5,r=5,o=!1){this.range=t,this.y=e,this.x=i,this.yMargin=n,this.xMargin=r,this.isSnapshot=o;}map(t){return t.empty?this:new Ze(this.range.map(t),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}clip(t){return this.range.to<=t.doc.length?this:new Ze(v.cursor(t.doc.length),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}}const on=R.define({map:(s,t)=>s.map(t)}),bh=R.define();function St(s,t,e){let i=s.facet(uh);i.length?i[0](t):window.onerror?window.onerror(String(t),e,void 0,void 0,t):e?console.error(e+":",t):console.error(t);}const he=T.define({combine:s=>s.length?s[0]:!0});let Au=0;const xi=T.define();class ht{constructor(t,e,i,n,r){this.id=t,this.create=e,this.domEventHandlers=i,this.domEventObservers=n,this.extension=r(this);}static define(t,e){const{eventHandlers:i,eventObservers:n,provide:r,decorations:o}=e||{};return new ht(Au++,t,i,n,l=>{let a=[xi.of(l)];return o&&a.push(Ri.of(h=>{let c=h.plugin(l);return c?o(c):I.none})),r&&a.push(r(l)),a})}static fromClass(t,e){return ht.define(i=>new t(i),e)}}class ds{constructor(t){this.spec=t,this.mustUpdate=null,this.value=null;}update(t){if(this.value){if(this.mustUpdate){let e=this.mustUpdate;if(this.mustUpdate=null,this.value.update)try{this.value.update(e);}catch(i){if(St(e.state,i,"CodeMirror plugin crashed"),this.value.destroy)try{this.value.destroy();}catch{}this.deactivate();}}}else if(this.spec)try{this.value=this.spec.create(t);}catch(e){St(t.state,e,"CodeMirror plugin crashed"),this.deactivate();}return this}destroy(t){var e;if(!((e=this.value)===null||e===void 0)&&e.destroy)try{this.value.destroy();}catch(i){St(t.state,i,"CodeMirror plugin crashed");}}deactivate(){this.spec=this.value=null;}}const wh=T.define(),zr=T.define(),Ri=T.define(),xh=T.define(),qr=T.define(),vh=T.define();function Ho(s,t){let e=s.state.facet(vh);if(!e.length)return e;let i=e.map(r=>r instanceof Function?r(s):r),n=[];return z.spans(i,t.from,t.to,{point(){},span(r,o,l,a){let h=r-t.from,c=o-t.from,f=n;for(let u=l.length-1;u>=0;u--,a--){let d=l[u].spec.bidiIsolate,p;if(d==null&&(d=Cu(t.text,h,c)),a>0&&f.length&&(p=f[f.length-1]).to==h&&p.direction==d)p.to=c,f=p.inner;else {let y={from:h,to:c,direction:d,inner:[]};f.push(y),f=y.inner;}}}}),n}const kh=T.define();function jr(s){let t=0,e=0,i=0,n=0;for(let r of s.state.facet(kh)){let o=r(s);o&&(o.left!=null&&(t=Math.max(t,o.left)),o.right!=null&&(e=Math.max(e,o.right)),o.top!=null&&(i=Math.max(i,o.top)),o.bottom!=null&&(n=Math.max(n,o.bottom)));}return {left:t,right:e,top:i,bottom:n}}const vi=T.define();class Ht{constructor(t,e,i,n){this.fromA=t,this.toA=e,this.fromB=i,this.toB=n;}join(t){return new Ht(Math.min(this.fromA,t.fromA),Math.max(this.toA,t.toA),Math.min(this.fromB,t.fromB),Math.max(this.toB,t.toB))}addToSet(t){let e=t.length,i=this;for(;e>0;e--){let n=t[e-1];if(!(n.fromA>i.toA)){if(n.toA<i.fromA)break;i=i.join(n),t.splice(e-1,1);}}return t.splice(e,0,i),t}static extendWithRanges(t,e){if(e.length==0)return t;let i=[];for(let n=0,r=0,o=0,l=0;;n++){let a=n==t.length?null:t[n],h=o-l,c=a?a.fromB:1e9;for(;r<e.length&&e[r]<c;){let f=e[r],u=e[r+1],d=Math.max(l,f),p=Math.min(c,u);if(d<=p&&new Ht(d+h,p+h,d,p).addToSet(i),u>c)break;r+=2;}if(!a)return i;new Ht(a.fromA,a.toA,a.fromB,a.toB).addToSet(i),o=a.toA,l=a.toB;}}}class Vn{constructor(t,e,i){this.view=t,this.state=e,this.transactions=i,this.flags=0,this.startState=t.state,this.changes=it.empty(this.startState.doc.length);for(let r of i)this.changes=this.changes.compose(r.changes);let n=[];this.changes.iterChangedRanges((r,o,l,a)=>n.push(new Ht(r,o,l,a))),this.changedRanges=n;}static create(t,e,i){return new Vn(t,e,i)}get viewportChanged(){return (this.flags&4)>0}get viewportMoved(){return (this.flags&8)>0}get heightChanged(){return (this.flags&2)>0}get geometryChanged(){return this.docChanged||(this.flags&18)>0}get focusChanged(){return (this.flags&1)>0}get docChanged(){return !this.changes.empty}get selectionSet(){return this.transactions.some(t=>t.selection)}get empty(){return this.flags==0&&this.transactions.length==0}}class Vo extends U{get length(){return this.view.state.doc.length}constructor(t){super(),this.view=t,this.decorations=[],this.dynamicDecorationMap=[!1],this.domChanged=null,this.hasComposition=null,this.markedForComposition=new Set,this.editContextFormatting=I.none,this.lastCompositionAfterCursor=!1,this.minWidth=0,this.minWidthFrom=0,this.minWidthTo=0,this.impreciseAnchor=null,this.impreciseHead=null,this.forceSelection=!1,this.lastUpdate=Date.now(),this.setDOM(t.contentDOM),this.children=[new $],this.children[0].setParent(this),this.updateDeco(),this.updateInner([new Ht(0,0,0,t.state.doc.length)],0,null);}update(t){var e;let i=t.changedRanges;this.minWidth>0&&i.length&&(i.every(({fromA:h,toA:c})=>c<this.minWidthFrom||h>this.minWidthTo)?(this.minWidthFrom=t.changes.mapPos(this.minWidthFrom,1),this.minWidthTo=t.changes.mapPos(this.minWidthTo,1)):this.minWidth=this.minWidthFrom=this.minWidthTo=0),this.updateEditContextFormatting(t);let n=-1;this.view.inputState.composing>=0&&!this.view.observer.editContext&&(!((e=this.domChanged)===null||e===void 0)&&e.newSel?n=this.domChanged.newSel.head:!Pu(t.changes,this.hasComposition)&&!t.selectionSet&&(n=t.state.selection.main.head));let r=n>-1?Du(this.view,t.changes,n):null;if(this.domChanged=null,this.hasComposition){this.markedForComposition.clear();let{from:h,to:c}=this.hasComposition;i=new Ht(h,c,t.changes.mapPos(h,-1),t.changes.mapPos(c,1)).addToSet(i.slice());}this.hasComposition=r?{from:r.range.fromB,to:r.range.toB}:null,(D.ie||D.chrome)&&!r&&t&&t.state.doc.lines!=t.startState.doc.lines&&(this.forceSelection=!0);let o=this.decorations,l=this.updateDeco(),a=Bu(o,l,t.changes);return i=Ht.extendWithRanges(i,a),!(this.flags&7)&&i.length==0?!1:(this.updateInner(i,t.startState.doc.length,r),t.transactions.length&&(this.lastUpdate=Date.now()),!0)}updateInner(t,e,i){this.view.viewState.mustMeasureContent=!0,this.updateChildren(t,e,i);let{observer:n}=this.view;n.ignore(()=>{this.dom.style.height=this.view.viewState.contentHeight/this.view.scaleY+"px",this.dom.style.flexBasis=this.minWidth?this.minWidth+"px":"";let o=D.chrome||D.ios?{node:n.selectionRange.focusNode,written:!1}:void 0;this.sync(this.view,o),this.flags&=-8,o&&(o.written||n.selectionRange.focusNode!=o.node)&&(this.forceSelection=!0),this.dom.style.height="";}),this.markedForComposition.forEach(o=>o.flags&=-9);let r=[];if(this.view.viewport.from||this.view.viewport.to<this.view.state.doc.length)for(let o of this.children)o instanceof ce&&o.widget instanceof or&&r.push(o.dom);n.updateGaps(r);}updateChildren(t,e,i){let n=i?i.range.addToSet(t.slice()):t,r=this.childCursor(e);for(let o=n.length-1;;o--){let l=o>=0?n[o]:null;if(!l)break;let{fromA:a,toA:h,fromB:c,toB:f}=l,u,d,p,y;if(i&&i.range.fromB<f&&i.range.toB>c){let S=Di.build(this.view.state.doc,c,i.range.fromB,this.decorations,this.dynamicDecorationMap),w=Di.build(this.view.state.doc,i.range.toB,f,this.decorations,this.dynamicDecorationMap);d=S.breakAtStart,p=S.openStart,y=w.openEnd;let C=this.compositionView(i);w.breakAtStart?C.breakAfter=1:w.content.length&&C.merge(C.length,C.length,w.content[0],!1,w.openStart,0)&&(C.breakAfter=w.content[0].breakAfter,w.content.shift()),S.content.length&&C.merge(0,0,S.content[S.content.length-1],!0,0,S.openEnd)&&S.content.pop(),u=S.content.concat(C).concat(w.content);}else ({content:u,breakAtStart:d,openStart:p,openEnd:y}=Di.build(this.view.state.doc,c,f,this.decorations,this.dynamicDecorationMap));let{i:g,off:b}=r.findPos(h,1),{i:x,off:k}=r.findPos(a,-1);Xa(this,x,k,g,b,u,d,p,y);}i&&this.fixCompositionDOM(i);}updateEditContextFormatting(t){this.editContextFormatting=this.editContextFormatting.map(t.changes);for(let e of t.transactions)for(let i of e.effects)i.is(bh)&&(this.editContextFormatting=i.value);}compositionView(t){let e=new jt(t.text.nodeValue);e.flags|=8;for(let{deco:n}of t.marks)e=new ue(n,[e],e.length);let i=new $;return i.append(e,0),i}fixCompositionDOM(t){let e=(r,o)=>{o.flags|=8|(o.children.some(a=>a.flags&7)?1:0),this.markedForComposition.add(o);let l=U.get(r);l&&l!=o&&(l.dom=null),o.setDOM(r);},i=this.childPos(t.range.fromB,1),n=this.children[i.i];e(t.line,n);for(let r=t.marks.length-1;r>=-1;r--)i=n.childPos(i.off,1),n=n.children[i.i],e(r>=0?t.marks[r].node:t.text,n);}updateSelection(t=!1,e=!1){(t||!this.view.observer.selectionRange.focusNode)&&this.view.observer.readSelectionRange();let i=this.view.root.activeElement,n=i==this.dom,r=!n&&!(this.view.state.facet(he)||this.dom.tabIndex>-1)&&Tn(this.dom,this.view.observer.selectionRange)&&!(i&&this.dom.contains(i));if(!(n||e||r))return;let o=this.forceSelection;this.forceSelection=!1;let l=this.view.state.selection.main,a=this.moveToLine(this.domAtPos(l.anchor)),h=l.empty?a:this.moveToLine(this.domAtPos(l.head));if(D.gecko&&l.empty&&!this.hasComposition&&Mu(a)){let f=document.createTextNode("");this.view.observer.ignore(()=>a.node.insertBefore(f,a.node.childNodes[a.offset]||null)),a=h=new ut(f,0),o=!0;}let c=this.view.observer.selectionRange;(o||!c.focusNode||(!Mi(a.node,a.offset,c.anchorNode,c.anchorOffset)||!Mi(h.node,h.offset,c.focusNode,c.focusOffset))&&!this.suppressWidgetCursorChange(c,l))&&(this.view.observer.ignore(()=>{D.android&&D.chrome&&this.dom.contains(c.focusNode)&&Eu(c.focusNode,this.dom)&&(this.dom.blur(),this.dom.focus({preventScroll:!0}));let f=Li(this.view.root);if(f)if(l.empty){if(D.gecko){let u=Tu(a.node,a.offset);if(u&&u!=3){let d=(u==1?Ga:Ya)(a.node,a.offset);d&&(a=new ut(d.node,d.offset));}}f.collapse(a.node,a.offset),l.bidiLevel!=null&&f.caretBidiLevel!==void 0&&(f.caretBidiLevel=l.bidiLevel);}else if(f.extend){f.collapse(a.node,a.offset);try{f.extend(h.node,h.offset);}catch{}}else {let u=document.createRange();l.anchor>l.head&&([a,h]=[h,a]),u.setEnd(h.node,h.offset),u.setStart(a.node,a.offset),f.removeAllRanges(),f.addRange(u);}r&&this.view.root.activeElement==this.dom&&(this.dom.blur(),i&&i.focus());}),this.view.observer.setSelectionRange(a,h)),this.impreciseAnchor=a.precise?null:new ut(c.anchorNode,c.anchorOffset),this.impreciseHead=h.precise?null:new ut(c.focusNode,c.focusOffset);}suppressWidgetCursorChange(t,e){return this.hasComposition&&e.empty&&Mi(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset)&&this.posFromDOM(t.focusNode,t.focusOffset)==e.head}enforceCursorAssoc(){if(this.hasComposition)return;let{view:t}=this,e=t.state.selection.main,i=Li(t.root),{anchorNode:n,anchorOffset:r}=t.observer.selectionRange;if(!i||!e.empty||!e.assoc||!i.modify)return;let o=$.find(this,e.head);if(!o)return;let l=o.posAtStart;if(e.head==l||e.head==l+o.length)return;let a=this.coordsAt(e.head,-1),h=this.coordsAt(e.head,1);if(!a||!h||a.bottom>h.top)return;let c=this.domAtPos(e.head+e.assoc);i.collapse(c.node,c.offset),i.modify("move",e.assoc<0?"forward":"backward","lineboundary"),t.observer.readSelectionRange();let f=t.observer.selectionRange;t.docView.posFromDOM(f.anchorNode,f.anchorOffset)!=e.from&&i.collapse(n,r);}moveToLine(t){let e=this.dom,i;if(t.node!=e)return t;for(let n=t.offset;!i&&n<e.childNodes.length;n++){let r=U.get(e.childNodes[n]);r instanceof $&&(i=r.domAtPos(0));}for(let n=t.offset-1;!i&&n>=0;n--){let r=U.get(e.childNodes[n]);r instanceof $&&(i=r.domAtPos(r.length));}return i?new ut(i.node,i.offset,!0):t}nearest(t){for(let e=t;e;){let i=U.get(e);if(i&&i.rootView==this)return i;e=e.parentNode;}return null}posFromDOM(t,e){let i=this.nearest(t);if(!i)throw new RangeError("Trying to find position for a DOM position outside of the document");return i.localPosFromDOM(t,e)+i.posAtStart}domAtPos(t){let{i:e,off:i}=this.childCursor().findPos(t,-1);for(;e<this.children.length-1;){let n=this.children[e];if(i<n.length||n instanceof $)break;e++,i=0;}return this.children[e].domAtPos(i)}coordsAt(t,e){let i=null,n=0;for(let r=this.length,o=this.children.length-1;o>=0;o--){let l=this.children[o],a=r-l.breakAfter,h=a-l.length;if(a<t)break;if(h<=t&&(h<t||l.covers(-1))&&(a>t||l.covers(1))&&(!i||l instanceof $&&!(i instanceof $&&e>=0)))i=l,n=h;else if(i&&h==t&&a==t&&l instanceof ce&&Math.abs(e)<2){if(l.deco.startSide<0)break;o&&(i=null);}r=h;}return i?i.coordsAt(t-n,e):null}coordsForChar(t){let{i:e,off:i}=this.childPos(t,1),n=this.children[e];if(!(n instanceof $))return null;for(;n.children.length;){let{i:l,off:a}=n.childPos(i,1);for(;;l++){if(l==n.children.length)return null;if((n=n.children[l]).length)break}i=a;}if(!(n instanceof jt))return null;let r=gt(n.text,i);if(r==i)return null;let o=Ve(n.dom,i,r).getClientRects();for(let l=0;l<o.length;l++){let a=o[l];if(l==o.length-1||a.top<a.bottom&&a.left<a.right)return a}return null}measureVisibleLineHeights(t){let e=[],{from:i,to:n}=t,r=this.view.contentDOM.clientWidth,o=r>Math.max(this.view.scrollDOM.clientWidth,this.minWidth)+1,l=-1,a=this.view.textDirection==J.LTR;for(let h=0,c=0;c<this.children.length;c++){let f=this.children[c],u=h+f.length;if(u>n)break;if(h>=i){let d=f.dom.getBoundingClientRect();if(e.push(d.height),o){let p=f.dom.lastChild,y=p?ei(p):[];if(y.length){let g=y[y.length-1],b=a?g.right-d.left:d.right-g.left;b>l&&(l=b,this.minWidth=r,this.minWidthFrom=h,this.minWidthTo=u);}}}h=u+f.breakAfter;}return e}textDirectionAt(t){let{i:e}=this.childPos(t,1);return getComputedStyle(this.children[e].dom).direction=="rtl"?J.RTL:J.LTR}measureTextSize(){for(let r of this.children)if(r instanceof $){let o=r.measureTextSize();if(o)return o}let t=document.createElement("div"),e,i,n;return t.className="cm-line",t.style.width="99999px",t.style.position="absolute",t.textContent="abc def ghi jkl mno pqr stu",this.view.observer.ignore(()=>{this.dom.appendChild(t);let r=ei(t.firstChild)[0];e=t.getBoundingClientRect().height,i=r?r.width/27:7,n=r?r.height:e,t.remove();}),{lineHeight:e,charWidth:i,textHeight:n}}childCursor(t=this.length){let e=this.children.length;return e&&(t-=this.children[--e].length),new Ja(this.children,t,e)}computeBlockGapDeco(){let t=[],e=this.view.viewState;for(let i=0,n=0;;n++){let r=n==e.viewports.length?null:e.viewports[n],o=r?r.from-1:this.length;if(o>i){let l=(e.lineBlockAt(o).bottom-e.lineBlockAt(i).top)/this.view.scaleY;t.push(I.replace({widget:new or(l),block:!0,inclusive:!0,isBlockGap:!0}).range(i,o));}if(!r)break;i=r.to+1;}return I.set(t)}updateDeco(){let t=1,e=this.view.state.facet(Ri).map(r=>(this.dynamicDecorationMap[t++]=typeof r=="function")?r(this.view):r),i=!1,n=this.view.state.facet(xh).map((r,o)=>{let l=typeof r=="function";return l&&(i=!0),l?r(this.view):r});for(n.length&&(this.dynamicDecorationMap[t++]=i,e.push(z.join(n))),this.decorations=[this.editContextFormatting,...e,this.computeBlockGapDeco(),this.view.viewState.lineGapDeco];t<this.decorations.length;)this.dynamicDecorationMap[t++]=!1;return this.decorations}scrollIntoView(t){if(t.isSnapshot){let h=this.view.viewState.lineBlockAt(t.range.head);this.view.scrollDOM.scrollTop=h.top-t.yMargin,this.view.scrollDOM.scrollLeft=t.xMargin;return}for(let h of this.view.state.facet(yh))try{if(h(this.view,t.range,t))return !0}catch(c){St(this.view.state,c,"scroll handler");}let{range:e}=t,i=this.coordsAt(e.head,e.empty?e.assoc:e.head>e.anchor?-1:1),n;if(!i)return;!e.empty&&(n=this.coordsAt(e.anchor,e.anchor>e.head?-1:1))&&(i={left:Math.min(i.left,n.left),top:Math.min(i.top,n.top),right:Math.max(i.right,n.right),bottom:Math.max(i.bottom,n.bottom)});let r=jr(this.view),o={left:i.left-r.left,top:i.top-r.top,right:i.right+r.right,bottom:i.bottom+r.bottom},{offsetWidth:l,offsetHeight:a}=this.view.scrollDOM;ou(this.view.scrollDOM,o,e.head<e.anchor?-1:1,t.x,t.y,Math.max(Math.min(t.xMargin,l),-l),Math.max(Math.min(t.yMargin,a),-a),this.view.textDirection==J.LTR);}}function Mu(s){return s.node.nodeType==1&&s.node.firstChild&&(s.offset==0||s.node.childNodes[s.offset-1].contentEditable=="false")&&(s.offset==s.node.childNodes.length||s.node.childNodes[s.offset].contentEditable=="false")}function Sh(s,t){let e=s.observer.selectionRange;if(!e.focusNode)return null;let i=Ga(e.focusNode,e.focusOffset),n=Ya(e.focusNode,e.focusOffset),r=i||n;if(n&&i&&n.node!=i.node){let l=U.get(n.node);if(!l||l instanceof jt&&l.text!=n.node.nodeValue)r=n;else if(s.docView.lastCompositionAfterCursor){let a=U.get(i.node);!a||a instanceof jt&&a.text!=i.node.nodeValue||(r=n);}}if(s.docView.lastCompositionAfterCursor=r!=i,!r)return null;let o=t-r.offset;return {from:o,to:o+r.node.nodeValue.length,node:r.node}}function Du(s,t,e){let i=Sh(s,e);if(!i)return null;let{node:n,from:r,to:o}=i,l=n.nodeValue;if(/[\n\r]/.test(l)||s.state.doc.sliceString(i.from,i.to)!=l)return null;let a=t.invertedDesc,h=new Ht(a.mapPos(r),a.mapPos(o),r,o),c=[];for(let f=n.parentNode;;f=f.parentNode){let u=U.get(f);if(u instanceof ue)c.push({node:f,deco:u.mark});else {if(u instanceof $||f.nodeName=="DIV"&&f.parentNode==s.contentDOM)return {range:h,text:n,marks:c,line:f};if(f!=s.contentDOM)c.push({node:f,deco:new Gi({inclusive:!0,attributes:pu(f),tagName:f.tagName.toLowerCase()})});else return null}}}function Tu(s,t){return s.nodeType!=1?0:(t&&s.childNodes[t-1].contentEditable=="false"?1:0)|(t<s.childNodes.length&&s.childNodes[t].contentEditable=="false"?2:0)}let Ou=class{constructor(){this.changes=[];}compareRange(t,e){On(t,e,this.changes);}comparePoint(t,e){On(t,e,this.changes);}boundChange(t){On(t,t,this.changes);}};function Bu(s,t,e){let i=new Ou;return z.compare(s,t,e,i),i.changes}function Eu(s,t){for(let e=s;e&&e!=t;e=e.assignedSlot||e.parentNode)if(e.nodeType==1&&e.contentEditable=="false")return !0;return !1}function Pu(s,t){let e=!1;return t&&s.iterChangedRanges((i,n)=>{i<t.to&&n>t.from&&(e=!0);}),e}function Lu(s,t,e=1){let i=s.charCategorizer(t),n=s.doc.lineAt(t),r=t-n.from;if(n.length==0)return v.cursor(t);r==0?e=1:r==n.length&&(e=-1);let o=r,l=r;e<0?o=gt(n.text,r,!1):l=gt(n.text,r);let a=i(n.text.slice(o,l));for(;o>0;){let h=gt(n.text,o,!1);if(i(n.text.slice(h,o))!=a)break;o=h;}for(;l<n.length;){let h=gt(n.text,l);if(i(n.text.slice(l,h))!=a)break;l=h;}return v.range(o+n.from,l+n.from)}function Ru(s,t){return t.left>s?t.left-s:Math.max(0,s-t.right)}function Iu(s,t){return t.top>s?t.top-s:Math.max(0,s-t.bottom)}function ps(s,t){return s.top<t.bottom-1&&s.bottom>t.top+1}function Wo(s,t){return t<s.top?{top:t,left:s.left,right:s.right,bottom:s.bottom}:s}function _o(s,t){return t>s.bottom?{top:s.top,left:s.left,right:s.right,bottom:t}:s}function fr(s,t,e){let i,n,r,o,l=!1,a,h,c,f;for(let p=s.firstChild;p;p=p.nextSibling){let y=ei(p);for(let g=0;g<y.length;g++){let b=y[g];n&&ps(n,b)&&(b=Wo(_o(b,n.bottom),n.top));let x=Ru(t,b),k=Iu(e,b);if(x==0&&k==0)return p.nodeType==3?zo(p,t,e):fr(p,t,e);if(!i||o>k||o==k&&r>x){i=p,n=b,r=x,o=k;let S=k?e<b.top?-1:1:x?t<b.left?-1:1:0;l=!S||(S>0?g<y.length-1:g>0);}x==0?e>b.bottom&&(!c||c.bottom<b.bottom)?(a=p,c=b):e<b.top&&(!f||f.top>b.top)&&(h=p,f=b):c&&ps(c,b)?c=_o(c,b.bottom):f&&ps(f,b)&&(f=Wo(f,b.top));}}if(c&&c.bottom>=e?(i=a,n=c):f&&f.top<=e&&(i=h,n=f),!i)return {node:s,offset:0};let u=Math.max(n.left,Math.min(n.right,t));if(i.nodeType==3)return zo(i,u,e);if(l&&i.contentEditable!="false")return fr(i,u,e);let d=Array.prototype.indexOf.call(s.childNodes,i)+(t>=(n.left+n.right)/2?1:0);return {node:s,offset:d}}function zo(s,t,e){let i=s.nodeValue.length,n=-1,r=1e9,o=0;for(let l=0;l<i;l++){let a=Ve(s,l,l+1).getClientRects();for(let h=0;h<a.length;h++){let c=a[h];if(c.top==c.bottom)continue;o||(o=t-c.left);let f=(c.top>e?c.top-e:e-c.bottom)-1;if(c.left-1<=t&&c.right+1>=t&&f<r){let u=t>=(c.left+c.right)/2,d=u;if((D.chrome||D.gecko)&&Ve(s,l).getBoundingClientRect().left==c.right&&(d=!u),f<=0)return {node:s,offset:l+(d?1:0)};n=l+(d?1:0),r=f;}}}return {node:s,offset:n>-1?n:o>0?s.nodeValue.length:0}}function Ch(s,t,e,i=-1){var n,r;let o=s.contentDOM.getBoundingClientRect(),l=o.top+s.viewState.paddingTop,a,{docHeight:h}=s.viewState,{x:c,y:f}=t,u=f-l;if(u<0)return 0;if(u>h)return s.state.doc.length;for(let S=s.viewState.heightOracle.textHeight/2,w=!1;a=s.elementAtHeight(u),a.type!=yt.Text;)for(;u=i>0?a.bottom+S:a.top-S,!(u>=0&&u<=h);){if(w)return e?null:0;w=!0,i=-i;}f=l+u;let d=a.from;if(d<s.viewport.from)return s.viewport.from==0?0:e?null:qo(s,o,a,c,f);if(d>s.viewport.to)return s.viewport.to==s.state.doc.length?s.state.doc.length:e?null:qo(s,o,a,c,f);let p=s.dom.ownerDocument,y=s.root.elementFromPoint?s.root:p,g=y.elementFromPoint(c,f);g&&!s.contentDOM.contains(g)&&(g=null),g||(c=Math.max(o.left+1,Math.min(o.right-1,c)),g=y.elementFromPoint(c,f),g&&!s.contentDOM.contains(g)&&(g=null));let b,x=-1;if(g&&((n=s.docView.nearest(g))===null||n===void 0?void 0:n.isEditable)!=!1){if(p.caretPositionFromPoint){let S=p.caretPositionFromPoint(c,f);S&&({offsetNode:b,offset:x}=S);}else if(p.caretRangeFromPoint){let S=p.caretRangeFromPoint(c,f);S&&({startContainer:b,startOffset:x}=S,(!s.contentDOM.contains(b)||D.safari&&Nu(b,x,c)||D.chrome&&Fu(b,x,c))&&(b=void 0));}b&&(x=Math.min(re(b),x));}if(!b||!s.docView.dom.contains(b)){let S=$.find(s.docView,d);if(!S)return u>a.top+a.height/2?a.to:a.from;({node:b,offset:x}=fr(S.dom,c,f));}let k=s.docView.nearest(b);if(!k)return null;if(k.isWidget&&((r=k.dom)===null||r===void 0?void 0:r.nodeType)==1){let S=k.dom.getBoundingClientRect();return t.y<S.top||t.y<=S.bottom&&t.x<=(S.left+S.right)/2?k.posAtStart:k.posAtEnd}else return k.localPosFromDOM(b,x)+k.posAtStart}function qo(s,t,e,i,n){let r=Math.round((i-t.left)*s.defaultCharacterWidth);if(s.lineWrapping&&e.height>s.defaultLineHeight*1.5){let l=s.viewState.heightOracle.textHeight,a=Math.floor((n-e.top-(s.defaultLineHeight-l)*.5)/l);r+=a*s.viewState.heightOracle.lineLength;}let o=s.state.sliceDoc(e.from,e.to);return e.from+Qs(o,r,s.state.tabSize)}function Nu(s,t,e){let i;if(s.nodeType!=3||t!=(i=s.nodeValue.length))return !1;for(let n=s.nextSibling;n;n=n.nextSibling)if(n.nodeType!=1||n.nodeName!="BR")return !1;return Ve(s,i-1,i).getBoundingClientRect().left>e}function Fu(s,t,e){if(t!=0)return !1;for(let n=s;;){let r=n.parentNode;if(!r||r.nodeType!=1||r.firstChild!=n)return !1;if(r.classList.contains("cm-line"))break;n=r;}let i=s.nodeType==1?s.getBoundingClientRect():Ve(s,0,Math.max(s.nodeValue.length,1)).getBoundingClientRect();return e-i.left>5}function ur(s,t){let e=s.lineBlockAt(t);if(Array.isArray(e.type)){for(let i of e.type)if(i.to>t||i.to==t&&(i.to==e.to||i.type==yt.Text))return i}return e}function Hu(s,t,e,i){let n=ur(s,t.head),r=!i||n.type!=yt.Text||!(s.lineWrapping||n.widgetLineBreaks)?null:s.coordsAtPos(t.assoc<0&&t.head>n.from?t.head-1:t.head);if(r){let o=s.dom.getBoundingClientRect(),l=s.textDirectionAt(n.from),a=s.posAtCoords({x:e==(l==J.LTR)?o.right-1:o.left+1,y:(r.top+r.bottom)/2});if(a!=null)return v.cursor(a,e?-1:1)}return v.cursor(e?n.to:n.from,e?-1:1)}function jo(s,t,e,i){let n=s.state.doc.lineAt(t.head),r=s.bidiSpans(n),o=s.textDirectionAt(n.from);for(let l=t,a=null;;){let h=Su(n,r,o,l,e),c=ah;if(!h){if(n.number==(e?s.state.doc.lines:1))return l;c=`
`,n=s.state.doc.line(n.number+(e?1:-1)),r=s.bidiSpans(n),h=s.visualLineSide(n,!e);}if(a){if(!a(c))return l}else {if(!i)return h;a=i(c);}l=h;}}function Vu(s,t,e){let i=s.state.charCategorizer(t),n=i(e);return r=>{let o=i(r);return n==Lt.Space&&(n=o),n==o}}function Wu(s,t,e,i){let n=t.head,r=e?1:-1;if(n==(e?s.state.doc.length:0))return v.cursor(n,t.assoc);let o=t.goalColumn,l,a=s.contentDOM.getBoundingClientRect(),h=s.coordsAtPos(n,t.assoc||-1),c=s.documentTop;if(h)o==null&&(o=h.left-a.left),l=r<0?h.top:h.bottom;else {let d=s.viewState.lineBlockAt(n);o==null&&(o=Math.min(a.right-a.left,s.defaultCharacterWidth*(n-d.from))),l=(r<0?d.top:d.bottom)+c;}let f=a.left+o,u=i??s.viewState.heightOracle.textHeight>>1;for(let d=0;;d+=10){let p=l+(u+d)*r,y=Ch(s,{x:f,y:p},!1,r);if(p<a.top||p>a.bottom||(r<0?y<n:y>n)){let g=s.docView.coordsForChar(y),b=!g||p<g.top?-1:1;return v.cursor(y,b,void 0,o)}}}function Bn(s,t,e){for(;;){let i=0;for(let n of s)n.between(t-1,t+1,(r,o,l)=>{if(t>r&&t<o){let a=i||e||(t-r<o-t?-1:1);t=a<0?r:o,i=a;}});if(!i)return t}}function ms(s,t,e){let i=Bn(s.state.facet(qr).map(n=>n(s)),e.from,t.head>e.from?-1:1);return i==e.from?e:v.cursor(i,i<e.from?1:-1)}const ki="￿";class _u{constructor(t,e){this.points=t,this.text="",this.lineSeparator=e.facet(W.lineSeparator);}append(t){this.text+=t;}lineBreak(){this.text+=ki;}readRange(t,e){if(!t)return this;let i=t.parentNode;for(let n=t;;){this.findPointBefore(i,n);let r=this.text.length;this.readNode(n);let o=n.nextSibling;if(o==e)break;let l=U.get(n),a=U.get(o);(l&&a?l.breakAfter:(l?l.breakAfter:Fn(n))||Fn(o)&&(n.nodeName!="BR"||n.cmIgnore)&&this.text.length>r)&&this.lineBreak(),n=o;}return this.findPointBefore(i,e),this}readTextNode(t){let e=t.nodeValue;for(let i of this.points)i.node==t&&(i.pos=this.text.length+Math.min(i.offset,e.length));for(let i=0,n=this.lineSeparator?null:/\r\n?|\n/g;;){let r=-1,o=1,l;if(this.lineSeparator?(r=e.indexOf(this.lineSeparator,i),o=this.lineSeparator.length):(l=n.exec(e))&&(r=l.index,o=l[0].length),this.append(e.slice(i,r<0?e.length:r)),r<0)break;if(this.lineBreak(),o>1)for(let a of this.points)a.node==t&&a.pos>this.text.length&&(a.pos-=o-1);i=r+o;}}readNode(t){if(t.cmIgnore)return;let e=U.get(t),i=e&&e.overrideDOMText;if(i!=null){this.findPointInside(t,i.length);for(let n=i.iter();!n.next().done;)n.lineBreak?this.lineBreak():this.append(n.value);}else t.nodeType==3?this.readTextNode(t):t.nodeName=="BR"?t.nextSibling&&this.lineBreak():t.nodeType==1&&this.readRange(t.firstChild,null);}findPointBefore(t,e){for(let i of this.points)i.node==t&&t.childNodes[i.offset]==e&&(i.pos=this.text.length);}findPointInside(t,e){for(let i of this.points)(t.nodeType==3?i.node==t:t.contains(i.node))&&(i.pos=this.text.length+(zu(t,i.node,i.offset)?e:0));}}function zu(s,t,e){for(;;){if(!t||e<re(t))return !1;if(t==s)return !0;e=He(t)+1,t=t.parentNode;}}class Ko{constructor(t,e){this.node=t,this.offset=e,this.pos=-1;}}class qu{constructor(t,e,i,n){this.typeOver=n,this.bounds=null,this.text="",this.domChanged=e>-1;let{impreciseHead:r,impreciseAnchor:o}=t.docView;if(t.state.readOnly&&e>-1)this.newSel=null;else if(e>-1&&(this.bounds=t.docView.domBoundsAround(e,i,0))){let l=r||o?[]:Uu(t),a=new _u(l,t.state);a.readRange(this.bounds.startDOM,this.bounds.endDOM),this.text=a.text,this.newSel=Gu(l,this.bounds.from);}else {let l=t.observer.selectionRange,a=r&&r.node==l.focusNode&&r.offset==l.focusOffset||!tr(t.contentDOM,l.focusNode)?t.state.selection.main.head:t.docView.posFromDOM(l.focusNode,l.focusOffset),h=o&&o.node==l.anchorNode&&o.offset==l.anchorOffset||!tr(t.contentDOM,l.anchorNode)?t.state.selection.main.anchor:t.docView.posFromDOM(l.anchorNode,l.anchorOffset),c=t.viewport;if((D.ios||D.chrome)&&t.state.selection.main.empty&&a!=h&&(c.from>0||c.to<t.state.doc.length)){let f=Math.min(a,h),u=Math.max(a,h),d=c.from-f,p=c.to-u;(d==0||d==1||f==0)&&(p==0||p==-1||u==t.state.doc.length)&&(a=0,h=t.state.doc.length);}this.newSel=v.single(h,a);}}}function Ah(s,t){let e,{newSel:i}=t,n=s.state.selection.main,r=s.inputState.lastKeyTime>Date.now()-100?s.inputState.lastKeyCode:-1;if(t.bounds){let{from:o,to:l}=t.bounds,a=n.from,h=null;(r===8||D.android&&t.text.length<l-o)&&(a=n.to,h="end");let c=Ku(s.state.doc.sliceString(o,l,ki),t.text,a-o,h);c&&(D.chrome&&r==13&&c.toB==c.from+2&&t.text.slice(c.from,c.toB)==ki+ki&&c.toB--,e={from:o+c.from,to:o+c.toA,insert:H.of(t.text.slice(c.from,c.toB).split(ki))});}else i&&(!s.hasFocus&&s.state.facet(he)||i.main.eq(n))&&(i=null);if(!e&&!i)return !1;if(!e&&t.typeOver&&!n.empty&&i&&i.main.empty?e={from:n.from,to:n.to,insert:s.state.doc.slice(n.from,n.to)}:(D.mac||D.android)&&e&&e.from==e.to&&e.from==n.head-1&&/^\. ?$/.test(e.insert.toString())&&s.contentDOM.getAttribute("autocorrect")=="off"?(i&&e.insert.length==2&&(i=v.single(i.main.anchor-1,i.main.head-1)),e={from:e.from,to:e.to,insert:H.of([e.insert.toString().replace("."," ")])}):e&&e.from>=n.from&&e.to<=n.to&&(e.from!=n.from||e.to!=n.to)&&n.to-n.from-(e.to-e.from)<=4?e={from:n.from,to:n.to,insert:s.state.doc.slice(n.from,e.from).append(e.insert).append(s.state.doc.slice(e.to,n.to))}:D.chrome&&e&&e.from==e.to&&e.from==n.head&&e.insert.toString()==`
 `&&s.lineWrapping&&(i&&(i=v.single(i.main.anchor-1,i.main.head-1)),e={from:n.from,to:n.to,insert:H.of([" "])}),e)return Kr(s,e,i,r);if(i&&!i.main.eq(n)){let o=!1,l="select";return s.inputState.lastSelectionTime>Date.now()-50&&(s.inputState.lastSelectionOrigin=="select"&&(o=!0),l=s.inputState.lastSelectionOrigin),s.dispatch({selection:i,scrollIntoView:o,userEvent:l}),!0}else return !1}function Kr(s,t,e,i=-1){if(D.ios&&s.inputState.flushIOSKey(t))return !0;let n=s.state.selection.main;if(D.android&&(t.to==n.to&&(t.from==n.from||t.from==n.from-1&&s.state.sliceDoc(t.from,n.from)==" ")&&t.insert.length==1&&t.insert.lines==2&&Qe(s.contentDOM,"Enter",13)||(t.from==n.from-1&&t.to==n.to&&t.insert.length==0||i==8&&t.insert.length<t.to-t.from&&t.to>n.head)&&Qe(s.contentDOM,"Backspace",8)||t.from==n.from&&t.to==n.to+1&&t.insert.length==0&&Qe(s.contentDOM,"Delete",46)))return !0;let r=t.insert.toString();s.inputState.composing>=0&&s.inputState.composing++;let o,l=()=>o||(o=ju(s,t,e));return s.state.facet(dh).some(a=>a(s,t.from,t.to,r,l))||s.dispatch(l()),!0}function ju(s,t,e){let i,n=s.state,r=n.selection.main;if(t.from>=r.from&&t.to<=r.to&&t.to-t.from>=(r.to-r.from)/3&&(!e||e.main.empty&&e.main.from==t.from+t.insert.length)&&s.inputState.composing<0){let l=r.from<t.from?n.sliceDoc(r.from,t.from):"",a=r.to>t.to?n.sliceDoc(t.to,r.to):"";i=n.replaceSelection(s.state.toText(l+t.insert.sliceString(0,void 0,s.state.lineBreak)+a));}else {let l=n.changes(t),a=e&&e.main.to<=l.newLength?e.main:void 0;if(n.selection.ranges.length>1&&s.inputState.composing>=0&&t.to<=r.to&&t.to>=r.to-10){let h=s.state.sliceDoc(t.from,t.to),c,f=e&&Sh(s,e.main.head);if(f){let p=t.insert.length-(t.to-t.from);c={from:f.from,to:f.to-p};}else c=s.state.doc.lineAt(r.head);let u=r.to-t.to,d=r.to-r.from;i=n.changeByRange(p=>{if(p.from==r.from&&p.to==r.to)return {changes:l,range:a||p.map(l)};let y=p.to-u,g=y-h.length;if(p.to-p.from!=d||s.state.sliceDoc(g,y)!=h||p.to>=c.from&&p.from<=c.to)return {range:p};let b=n.changes({from:g,to:y,insert:t.insert}),x=p.to-r.to;return {changes:b,range:a?v.range(Math.max(0,a.anchor+x),Math.max(0,a.head+x)):p.map(b)}});}else i={changes:l,selection:a&&n.selection.replaceRange(a)};}let o="input.type";return (s.composing||s.inputState.compositionPendingChange&&s.inputState.compositionEndedAt>Date.now()-50)&&(s.inputState.compositionPendingChange=!1,o+=".compose",s.inputState.compositionFirstChange&&(o+=".start",s.inputState.compositionFirstChange=!1)),n.update(i,{userEvent:o,scrollIntoView:!0})}function Ku(s,t,e,i){let n=Math.min(s.length,t.length),r=0;for(;r<n&&s.charCodeAt(r)==t.charCodeAt(r);)r++;if(r==n&&s.length==t.length)return null;let o=s.length,l=t.length;for(;o>0&&l>0&&s.charCodeAt(o-1)==t.charCodeAt(l-1);)o--,l--;if(i=="end"){let a=Math.max(0,r-Math.min(o,l));e-=o+a-r;}if(o<r&&s.length<t.length){let a=e<=r&&e>=o?r-e:0;r-=a,l=r+(l-o),o=r;}else if(l<r){let a=e<=r&&e>=l?r-e:0;r-=a,o=r+(o-l),l=r;}return {from:r,toA:o,toB:l}}function Uu(s){let t=[];if(s.root.activeElement!=s.contentDOM)return t;let{anchorNode:e,anchorOffset:i,focusNode:n,focusOffset:r}=s.observer.selectionRange;return e&&(t.push(new Ko(e,i)),(n!=e||r!=i)&&t.push(new Ko(n,r))),t}function Gu(s,t){if(s.length==0)return null;let e=s[0].pos,i=s.length==2?s[1].pos:e;return e>-1&&i>-1?v.single(e+t,i+t):null}class Yu{setSelectionOrigin(t){this.lastSelectionOrigin=t,this.lastSelectionTime=Date.now();}constructor(t){this.view=t,this.lastKeyCode=0,this.lastKeyTime=0,this.lastTouchTime=0,this.lastFocusTime=0,this.lastScrollTop=0,this.lastScrollLeft=0,this.pendingIOSKey=void 0,this.tabFocusMode=-1,this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastContextMenu=0,this.scrollHandlers=[],this.handlers=Object.create(null),this.composing=-1,this.compositionFirstChange=null,this.compositionEndedAt=0,this.compositionPendingKey=!1,this.compositionPendingChange=!1,this.mouseSelection=null,this.draggedContent=null,this.handleEvent=this.handleEvent.bind(this),this.notifiedFocused=t.hasFocus,D.safari&&t.contentDOM.addEventListener("input",()=>null),D.gecko&&cd(t.contentDOM.ownerDocument);}handleEvent(t){!id(this.view,t)||this.ignoreDuringComposition(t)||t.type=="keydown"&&this.keydown(t)||(this.view.updateState!=0?Promise.resolve().then(()=>this.runHandlers(t.type,t)):this.runHandlers(t.type,t));}runHandlers(t,e){let i=this.handlers[t];if(i){for(let n of i.observers)n(this.view,e);for(let n of i.handlers){if(e.defaultPrevented)break;if(n(this.view,e)){e.preventDefault();break}}}}ensureHandlers(t){let e=Ju(t),i=this.handlers,n=this.view.contentDOM;for(let r in e)if(r!="scroll"){let o=!e[r].handlers.length,l=i[r];l&&o!=!l.handlers.length&&(n.removeEventListener(r,this.handleEvent),l=null),l||n.addEventListener(r,this.handleEvent,{passive:o});}for(let r in i)r!="scroll"&&!e[r]&&n.removeEventListener(r,this.handleEvent);this.handlers=e;}keydown(t){if(this.lastKeyCode=t.keyCode,this.lastKeyTime=Date.now(),t.keyCode==9&&this.tabFocusMode>-1&&(!this.tabFocusMode||Date.now()<=this.tabFocusMode))return !0;if(this.tabFocusMode>0&&t.keyCode!=27&&Dh.indexOf(t.keyCode)<0&&(this.tabFocusMode=-1),D.android&&D.chrome&&!t.synthetic&&(t.keyCode==13||t.keyCode==8))return this.view.observer.delayAndroidKey(t.key,t.keyCode),!0;let e;return D.ios&&!t.synthetic&&!t.altKey&&!t.metaKey&&((e=Mh.find(i=>i.keyCode==t.keyCode))&&!t.ctrlKey||Xu.indexOf(t.key)>-1&&t.ctrlKey&&!t.shiftKey)?(this.pendingIOSKey=e||t,setTimeout(()=>this.flushIOSKey(),250),!0):(t.keyCode!=229&&this.view.observer.forceFlush(),!1)}flushIOSKey(t){let e=this.pendingIOSKey;return !e||e.key=="Enter"&&t&&t.from<t.to&&/^\S+$/.test(t.insert.toString())?!1:(this.pendingIOSKey=void 0,Qe(this.view.contentDOM,e.key,e.keyCode,e instanceof KeyboardEvent?e:void 0))}ignoreDuringComposition(t){return /^key/.test(t.type)?this.composing>0?!0:D.safari&&!D.ios&&this.compositionPendingKey&&Date.now()-this.compositionEndedAt<100?(this.compositionPendingKey=!1,!0):!1:!1}startMouseSelection(t){this.mouseSelection&&this.mouseSelection.destroy(),this.mouseSelection=t;}update(t){this.view.observer.update(t),this.mouseSelection&&this.mouseSelection.update(t),this.draggedContent&&t.docChanged&&(this.draggedContent=this.draggedContent.map(t.changes)),t.transactions.length&&(this.lastKeyCode=this.lastSelectionTime=0);}destroy(){this.mouseSelection&&this.mouseSelection.destroy();}}function Uo(s,t){return (e,i)=>{try{return t.call(s,i,e)}catch(n){St(e.state,n);}}}function Ju(s){let t=Object.create(null);function e(i){return t[i]||(t[i]={observers:[],handlers:[]})}for(let i of s){let n=i.spec;if(n&&n.domEventHandlers)for(let r in n.domEventHandlers){let o=n.domEventHandlers[r];o&&e(r).handlers.push(Uo(i.value,o));}if(n&&n.domEventObservers)for(let r in n.domEventObservers){let o=n.domEventObservers[r];o&&e(r).observers.push(Uo(i.value,o));}}for(let i in Kt)e(i).handlers.push(Kt[i]);for(let i in Vt)e(i).observers.push(Vt[i]);return t}const Mh=[{key:"Backspace",keyCode:8,inputType:"deleteContentBackward"},{key:"Enter",keyCode:13,inputType:"insertParagraph"},{key:"Enter",keyCode:13,inputType:"insertLineBreak"},{key:"Delete",keyCode:46,inputType:"deleteContentForward"}],Xu="dthko",Dh=[16,17,18,20,91,92,224,225],ln=6;function an(s){return Math.max(0,s)*.7+8}function Qu(s,t){return Math.max(Math.abs(s.clientX-t.clientX),Math.abs(s.clientY-t.clientY))}class Zu{constructor(t,e,i,n){this.view=t,this.startEvent=e,this.style=i,this.mustSelect=n,this.scrollSpeed={x:0,y:0},this.scrolling=-1,this.lastEvent=e,this.scrollParents=lu(t.contentDOM),this.atoms=t.state.facet(qr).map(o=>o(t));let r=t.contentDOM.ownerDocument;r.addEventListener("mousemove",this.move=this.move.bind(this)),r.addEventListener("mouseup",this.up=this.up.bind(this)),this.extend=e.shiftKey,this.multiple=t.state.facet(W.allowMultipleSelections)&&$u(t,e),this.dragging=ed(t,e)&&Bh(e)==1?null:!1;}start(t){this.dragging===!1&&this.select(t);}move(t){if(t.buttons==0)return this.destroy();if(this.dragging||this.dragging==null&&Qu(this.startEvent,t)<10)return;this.select(this.lastEvent=t);let e=0,i=0,n=0,r=0,o=this.view.win.innerWidth,l=this.view.win.innerHeight;this.scrollParents.x&&({left:n,right:o}=this.scrollParents.x.getBoundingClientRect()),this.scrollParents.y&&({top:r,bottom:l}=this.scrollParents.y.getBoundingClientRect());let a=jr(this.view);t.clientX-a.left<=n+ln?e=-an(n-t.clientX):t.clientX+a.right>=o-ln&&(e=an(t.clientX-o)),t.clientY-a.top<=r+ln?i=-an(r-t.clientY):t.clientY+a.bottom>=l-ln&&(i=an(t.clientY-l)),this.setScrollSpeed(e,i);}up(t){this.dragging==null&&this.select(this.lastEvent),this.dragging||t.preventDefault(),this.destroy();}destroy(){this.setScrollSpeed(0,0);let t=this.view.contentDOM.ownerDocument;t.removeEventListener("mousemove",this.move),t.removeEventListener("mouseup",this.up),this.view.inputState.mouseSelection=this.view.inputState.draggedContent=null;}setScrollSpeed(t,e){this.scrollSpeed={x:t,y:e},t||e?this.scrolling<0&&(this.scrolling=setInterval(()=>this.scroll(),50)):this.scrolling>-1&&(clearInterval(this.scrolling),this.scrolling=-1);}scroll(){let{x:t,y:e}=this.scrollSpeed;t&&this.scrollParents.x&&(this.scrollParents.x.scrollLeft+=t,t=0),e&&this.scrollParents.y&&(this.scrollParents.y.scrollTop+=e,e=0),(t||e)&&this.view.win.scrollBy(t,e),this.dragging===!1&&this.select(this.lastEvent);}skipAtoms(t){let e=null;for(let i=0;i<t.ranges.length;i++){let n=t.ranges[i],r=null;if(n.empty){let o=Bn(this.atoms,n.from,0);o!=n.from&&(r=v.cursor(o,-1));}else {let o=Bn(this.atoms,n.from,-1),l=Bn(this.atoms,n.to,1);(o!=n.from||l!=n.to)&&(r=v.range(n.from==n.anchor?o:l,n.from==n.head?o:l));}r&&(e||(e=t.ranges.slice()),e[i]=r);}return e?v.create(e,t.mainIndex):t}select(t){let{view:e}=this,i=this.skipAtoms(this.style.get(t,this.extend,this.multiple));(this.mustSelect||!i.eq(e.state.selection,this.dragging===!1))&&this.view.dispatch({selection:i,userEvent:"select.pointer"}),this.mustSelect=!1;}update(t){t.transactions.some(e=>e.isUserEvent("input.type"))?this.destroy():this.style.update(t)&&setTimeout(()=>this.select(this.lastEvent),20);}}function $u(s,t){let e=s.state.facet(hh);return e.length?e[0](t):D.mac?t.metaKey:t.ctrlKey}function td(s,t){let e=s.state.facet(ch);return e.length?e[0](t):D.mac?!t.altKey:!t.ctrlKey}function ed(s,t){let{main:e}=s.state.selection;if(e.empty)return !1;let i=Li(s.root);if(!i||i.rangeCount==0)return !0;let n=i.getRangeAt(0).getClientRects();for(let r=0;r<n.length;r++){let o=n[r];if(o.left<=t.clientX&&o.right>=t.clientX&&o.top<=t.clientY&&o.bottom>=t.clientY)return !0}return !1}function id(s,t){if(!t.bubbles)return !0;if(t.defaultPrevented)return !1;for(let e=t.target,i;e!=s.contentDOM;e=e.parentNode)if(!e||e.nodeType==11||(i=U.get(e))&&i.ignoreEvent(t))return !1;return !0}const Kt=Object.create(null),Vt=Object.create(null),Th=D.ie&&D.ie_version<15||D.ios&&D.webkit_version<604;function nd(s){let t=s.dom.parentNode;if(!t)return;let e=t.appendChild(document.createElement("textarea"));e.style.cssText="position: fixed; left: -10000px; top: 10px",e.focus(),setTimeout(()=>{s.focus(),e.remove(),Oh(s,e.value);},50);}function $n(s,t,e){for(let i of s.facet(t))e=i(e,s);return e}function Oh(s,t){t=$n(s.state,Wr,t);let{state:e}=s,i,n=1,r=e.toText(t),o=r.lines==e.selection.ranges.length;if(dr!=null&&e.selection.ranges.every(a=>a.empty)&&dr==r.toString()){let a=-1;i=e.changeByRange(h=>{let c=e.doc.lineAt(h.from);if(c.from==a)return {range:h};a=c.from;let f=e.toText((o?r.line(n++).text:t)+e.lineBreak);return {changes:{from:c.from,insert:f},range:v.cursor(h.from+f.length)}});}else o?i=e.changeByRange(a=>{let h=r.line(n++);return {changes:{from:a.from,to:a.to,insert:h.text},range:v.cursor(a.from+h.length)}}):i=e.replaceSelection(r);s.dispatch(i,{userEvent:"input.paste",scrollIntoView:!0});}Vt.scroll=s=>{s.inputState.lastScrollTop=s.scrollDOM.scrollTop,s.inputState.lastScrollLeft=s.scrollDOM.scrollLeft;};Kt.keydown=(s,t)=>(s.inputState.setSelectionOrigin("select"),t.keyCode==27&&s.inputState.tabFocusMode!=0&&(s.inputState.tabFocusMode=Date.now()+2e3),!1);Vt.touchstart=(s,t)=>{s.inputState.lastTouchTime=Date.now(),s.inputState.setSelectionOrigin("select.pointer");};Vt.touchmove=s=>{s.inputState.setSelectionOrigin("select.pointer");};Kt.mousedown=(s,t)=>{if(s.observer.flush(),s.inputState.lastTouchTime>Date.now()-2e3)return !1;let e=null;for(let i of s.state.facet(fh))if(e=i(s,t),e)break;if(!e&&t.button==0&&(e=od(s,t)),e){let i=!s.hasFocus;s.inputState.startMouseSelection(new Zu(s,t,e,i)),i&&s.observer.ignore(()=>{ja(s.contentDOM);let r=s.root.activeElement;r&&!r.contains(s.contentDOM)&&r.blur();});let n=s.inputState.mouseSelection;if(n)return n.start(t),n.dragging===!1}return !1};function Go(s,t,e,i){if(i==1)return v.cursor(t,e);if(i==2)return Lu(s.state,t,e);{let n=$.find(s.docView,t),r=s.state.doc.lineAt(n?n.posAtEnd:t),o=n?n.posAtStart:r.from,l=n?n.posAtEnd:r.to;return l<s.state.doc.length&&l==r.to&&l++,v.range(o,l)}}let Yo=(s,t,e)=>t>=e.top&&t<=e.bottom&&s>=e.left&&s<=e.right;function sd(s,t,e,i){let n=$.find(s.docView,t);if(!n)return 1;let r=t-n.posAtStart;if(r==0)return 1;if(r==n.length)return -1;let o=n.coordsAt(r,-1);if(o&&Yo(e,i,o))return -1;let l=n.coordsAt(r,1);return l&&Yo(e,i,l)?1:o&&o.bottom>=i?-1:1}function Jo(s,t){let e=s.posAtCoords({x:t.clientX,y:t.clientY},!1);return {pos:e,bias:sd(s,e,t.clientX,t.clientY)}}const rd=D.ie&&D.ie_version<=11;let Xo=null,Qo=0,Zo=0;function Bh(s){if(!rd)return s.detail;let t=Xo,e=Zo;return Xo=s,Zo=Date.now(),Qo=!t||e>Date.now()-400&&Math.abs(t.clientX-s.clientX)<2&&Math.abs(t.clientY-s.clientY)<2?(Qo+1)%3:1}function od(s,t){let e=Jo(s,t),i=Bh(t),n=s.state.selection;return {update(r){r.docChanged&&(e.pos=r.changes.mapPos(e.pos),n=n.map(r.changes));},get(r,o,l){let a=Jo(s,r),h,c=Go(s,a.pos,a.bias,i);if(e.pos!=a.pos&&!o){let f=Go(s,e.pos,e.bias,i),u=Math.min(f.from,c.from),d=Math.max(f.to,c.to);c=u<c.from?v.range(u,d):v.range(d,u);}return o?n.replaceRange(n.main.extend(c.from,c.to)):l&&i==1&&n.ranges.length>1&&(h=ld(n,a.pos))?h:l?n.addRange(c):v.create([c])}}}function ld(s,t){for(let e=0;e<s.ranges.length;e++){let{from:i,to:n}=s.ranges[e];if(i<=t&&n>=t)return v.create(s.ranges.slice(0,e).concat(s.ranges.slice(e+1)),s.mainIndex==e?0:s.mainIndex-(s.mainIndex>e?1:0))}return null}Kt.dragstart=(s,t)=>{let{selection:{main:e}}=s.state;if(t.target.draggable){let n=s.docView.nearest(t.target);if(n&&n.isWidget){let r=n.posAtStart,o=r+n.length;(r>=e.to||o<=e.from)&&(e=v.range(r,o));}}let{inputState:i}=s;return i.mouseSelection&&(i.mouseSelection.dragging=!0),i.draggedContent=e,t.dataTransfer&&(t.dataTransfer.setData("Text",$n(s.state,_r,s.state.sliceDoc(e.from,e.to))),t.dataTransfer.effectAllowed="copyMove"),!1};Kt.dragend=s=>(s.inputState.draggedContent=null,!1);function $o(s,t,e,i){if(e=$n(s.state,Wr,e),!e)return;let n=s.posAtCoords({x:t.clientX,y:t.clientY},!1),{draggedContent:r}=s.inputState,o=i&&r&&td(s,t)?{from:r.from,to:r.to}:null,l={from:n,insert:e},a=s.state.changes(o?[o,l]:l);s.focus(),s.dispatch({changes:a,selection:{anchor:a.mapPos(n,-1),head:a.mapPos(n,1)},userEvent:o?"move.drop":"input.drop"}),s.inputState.draggedContent=null;}Kt.drop=(s,t)=>{if(!t.dataTransfer)return !1;if(s.state.readOnly)return !0;let e=t.dataTransfer.files;if(e&&e.length){let i=Array(e.length),n=0,r=()=>{++n==e.length&&$o(s,t,i.filter(o=>o!=null).join(s.state.lineBreak),!1);};for(let o=0;o<e.length;o++){let l=new FileReader;l.onerror=r,l.onload=()=>{/[\x00-\x08\x0e-\x1f]{2}/.test(l.result)||(i[o]=l.result),r();},l.readAsText(e[o]);}return !0}else {let i=t.dataTransfer.getData("Text");if(i)return $o(s,t,i,!0),!0}return !1};Kt.paste=(s,t)=>{if(s.state.readOnly)return !0;s.observer.flush();let e=Th?null:t.clipboardData;return e?(Oh(s,e.getData("text/plain")||e.getData("text/uri-list")),!0):(nd(s),!1)};function ad(s,t){let e=s.dom.parentNode;if(!e)return;let i=e.appendChild(document.createElement("textarea"));i.style.cssText="position: fixed; left: -10000px; top: 10px",i.value=t,i.focus(),i.selectionEnd=t.length,i.selectionStart=0,setTimeout(()=>{i.remove(),s.focus();},50);}function hd(s){let t=[],e=[],i=!1;for(let n of s.selection.ranges)n.empty||(t.push(s.sliceDoc(n.from,n.to)),e.push(n));if(!t.length){let n=-1;for(let{from:r}of s.selection.ranges){let o=s.doc.lineAt(r);o.number>n&&(t.push(o.text),e.push({from:o.from,to:Math.min(s.doc.length,o.to+1)})),n=o.number;}i=!0;}return {text:$n(s,_r,t.join(s.lineBreak)),ranges:e,linewise:i}}let dr=null;Kt.copy=Kt.cut=(s,t)=>{let{text:e,ranges:i,linewise:n}=hd(s.state);if(!e&&!n)return !1;dr=n?e:null,t.type=="cut"&&!s.state.readOnly&&s.dispatch({changes:i,scrollIntoView:!0,userEvent:"delete.cut"});let r=Th?null:t.clipboardData;return r?(r.clearData(),r.setData("text/plain",e),!0):(ad(s,e),!1)};const Eh=de.define();function Ph(s,t){let e=[];for(let i of s.facet(ph)){let n=i(s,t);n&&e.push(n);}return e?s.update({effects:e,annotations:Eh.of(!0)}):null}function Lh(s){setTimeout(()=>{let t=s.hasFocus;if(t!=s.inputState.notifiedFocused){let e=Ph(s.state,t);e?s.dispatch(e):s.update([]);}},10);}Vt.focus=s=>{s.inputState.lastFocusTime=Date.now(),!s.scrollDOM.scrollTop&&(s.inputState.lastScrollTop||s.inputState.lastScrollLeft)&&(s.scrollDOM.scrollTop=s.inputState.lastScrollTop,s.scrollDOM.scrollLeft=s.inputState.lastScrollLeft),Lh(s);};Vt.blur=s=>{s.observer.clearSelectionRange(),Lh(s);};Vt.compositionstart=Vt.compositionupdate=s=>{s.observer.editContext||(s.inputState.compositionFirstChange==null&&(s.inputState.compositionFirstChange=!0),s.inputState.composing<0&&(s.inputState.composing=0));};Vt.compositionend=s=>{s.observer.editContext||(s.inputState.composing=-1,s.inputState.compositionEndedAt=Date.now(),s.inputState.compositionPendingKey=!0,s.inputState.compositionPendingChange=s.observer.pendingRecords().length>0,s.inputState.compositionFirstChange=null,D.chrome&&D.android?s.observer.flushSoon():s.inputState.compositionPendingChange?Promise.resolve().then(()=>s.observer.flush()):setTimeout(()=>{s.inputState.composing<0&&s.docView.hasComposition&&s.update([]);},50));};Vt.contextmenu=s=>{s.inputState.lastContextMenu=Date.now();};Kt.beforeinput=(s,t)=>{var e,i;if(t.inputType=="insertReplacementText"&&s.observer.editContext){let r=(e=t.dataTransfer)===null||e===void 0?void 0:e.getData("text/plain"),o=t.getTargetRanges();if(r&&o.length){let l=o[0],a=s.posAtDOM(l.startContainer,l.startOffset),h=s.posAtDOM(l.endContainer,l.endOffset);return Kr(s,{from:a,to:h,insert:s.state.toText(r)},null),!0}}let n;if(D.chrome&&D.android&&(n=Mh.find(r=>r.inputType==t.inputType))&&(s.observer.delayAndroidKey(n.key,n.keyCode),n.key=="Backspace"||n.key=="Delete")){let r=((i=window.visualViewport)===null||i===void 0?void 0:i.height)||0;setTimeout(()=>{var o;(((o=window.visualViewport)===null||o===void 0?void 0:o.height)||0)>r+10&&s.hasFocus&&(s.contentDOM.blur(),s.focus());},100);}return D.ios&&t.inputType=="deleteContentForward"&&s.observer.flushSoon(),D.safari&&t.inputType=="insertText"&&s.inputState.composing>=0&&setTimeout(()=>Vt.compositionend(s,t),20),!1};const tl=new Set;function cd(s){tl.has(s)||(tl.add(s),s.addEventListener("copy",()=>{}),s.addEventListener("cut",()=>{}));}const el=["pre-wrap","normal","pre-line","break-spaces"];let si=!1;function il(){si=!1;}class fd{constructor(t){this.lineWrapping=t,this.doc=H.empty,this.heightSamples={},this.lineHeight=14,this.charWidth=7,this.textHeight=14,this.lineLength=30;}heightForGap(t,e){let i=this.doc.lineAt(e).number-this.doc.lineAt(t).number+1;return this.lineWrapping&&(i+=Math.max(0,Math.ceil((e-t-i*this.lineLength*.5)/this.lineLength))),this.lineHeight*i}heightForLine(t){return this.lineWrapping?(1+Math.max(0,Math.ceil((t-this.lineLength)/(this.lineLength-5))))*this.lineHeight:this.lineHeight}setDoc(t){return this.doc=t,this}mustRefreshForWrapping(t){return el.indexOf(t)>-1!=this.lineWrapping}mustRefreshForHeights(t){let e=!1;for(let i=0;i<t.length;i++){let n=t[i];n<0?i++:this.heightSamples[Math.floor(n*10)]||(e=!0,this.heightSamples[Math.floor(n*10)]=!0);}return e}refresh(t,e,i,n,r,o){let l=el.indexOf(t)>-1,a=Math.round(e)!=Math.round(this.lineHeight)||this.lineWrapping!=l;if(this.lineWrapping=l,this.lineHeight=e,this.charWidth=i,this.textHeight=n,this.lineLength=r,a){this.heightSamples={};for(let h=0;h<o.length;h++){let c=o[h];c<0?h++:this.heightSamples[Math.floor(c*10)]=!0;}}return a}}class ud{constructor(t,e){this.from=t,this.heights=e,this.index=0;}get more(){return this.index<this.heights.length}}class te{constructor(t,e,i,n,r){this.from=t,this.length=e,this.top=i,this.height=n,this._content=r;}get type(){return typeof this._content=="number"?yt.Text:Array.isArray(this._content)?this._content:this._content.type}get to(){return this.from+this.length}get bottom(){return this.top+this.height}get widget(){return this._content instanceof Se?this._content.widget:null}get widgetLineBreaks(){return typeof this._content=="number"?this._content:0}join(t){let e=(Array.isArray(this._content)?this._content:[this]).concat(Array.isArray(t._content)?t._content:[t]);return new te(this.from,this.length+t.length,this.top,this.height+t.height,e)}}var Y=function(s){return s[s.ByPos=0]="ByPos",s[s.ByHeight=1]="ByHeight",s[s.ByPosNoHeight=2]="ByPosNoHeight",s}(Y||(Y={}));const En=.001;class bt{constructor(t,e,i=2){this.length=t,this.height=e,this.flags=i;}get outdated(){return (this.flags&2)>0}set outdated(t){this.flags=(t?2:0)|this.flags&-3;}setHeight(t){this.height!=t&&(Math.abs(this.height-t)>En&&(si=!0),this.height=t);}replace(t,e,i){return bt.of(i)}decomposeLeft(t,e){e.push(this);}decomposeRight(t,e){e.push(this);}applyChanges(t,e,i,n){let r=this,o=i.doc;for(let l=n.length-1;l>=0;l--){let{fromA:a,toA:h,fromB:c,toB:f}=n[l],u=r.lineAt(a,Y.ByPosNoHeight,i.setDoc(e),0,0),d=u.to>=h?u:r.lineAt(h,Y.ByPosNoHeight,i,0,0);for(f+=d.to-h,h=d.to;l>0&&u.from<=n[l-1].toA;)a=n[l-1].fromA,c=n[l-1].fromB,l--,a<u.from&&(u=r.lineAt(a,Y.ByPosNoHeight,i,0,0));c+=u.from-a,a=u.from;let p=Ur.build(i.setDoc(o),t,c,f);r=Wn(r,r.replace(a,h,p));}return r.updateHeight(i,0)}static empty(){return new Mt(0,0)}static of(t){if(t.length==1)return t[0];let e=0,i=t.length,n=0,r=0;for(;;)if(e==i)if(n>r*2){let l=t[e-1];l.break?t.splice(--e,1,l.left,null,l.right):t.splice(--e,1,l.left,l.right),i+=1+l.break,n-=l.size;}else if(r>n*2){let l=t[i];l.break?t.splice(i,1,l.left,null,l.right):t.splice(i,1,l.left,l.right),i+=2+l.break,r-=l.size;}else break;else if(n<r){let l=t[e++];l&&(n+=l.size);}else {let l=t[--i];l&&(r+=l.size);}let o=0;return t[e-1]==null?(o=1,e--):t[e]==null&&(o=1,i++),new dd(bt.of(t.slice(0,e)),o,bt.of(t.slice(i)))}}function Wn(s,t){return s==t?s:(s.constructor!=t.constructor&&(si=!0),t)}bt.prototype.size=1;class Rh extends bt{constructor(t,e,i){super(t,e),this.deco=i;}blockAt(t,e,i,n){return new te(n,this.length,i,this.height,this.deco||0)}lineAt(t,e,i,n,r){return this.blockAt(0,i,n,r)}forEachLine(t,e,i,n,r,o){t<=r+this.length&&e>=r&&o(this.blockAt(0,i,n,r));}updateHeight(t,e=0,i=!1,n){return n&&n.from<=e&&n.more&&this.setHeight(n.heights[n.index++]),this.outdated=!1,this}toString(){return `block(${this.length})`}}class Mt extends Rh{constructor(t,e){super(t,e,null),this.collapsed=0,this.widgetHeight=0,this.breaks=0;}blockAt(t,e,i,n){return new te(n,this.length,i,this.height,this.breaks)}replace(t,e,i){let n=i[0];return i.length==1&&(n instanceof Mt||n instanceof ot&&n.flags&4)&&Math.abs(this.length-n.length)<10?(n instanceof ot?n=new Mt(n.length,this.height):n.height=this.height,this.outdated||(n.outdated=!1),n):bt.of(i)}updateHeight(t,e=0,i=!1,n){return n&&n.from<=e&&n.more?this.setHeight(n.heights[n.index++]):(i||this.outdated)&&this.setHeight(Math.max(this.widgetHeight,t.heightForLine(this.length-this.collapsed))+this.breaks*t.lineHeight),this.outdated=!1,this}toString(){return `line(${this.length}${this.collapsed?-this.collapsed:""}${this.widgetHeight?":"+this.widgetHeight:""})`}}class ot extends bt{constructor(t){super(t,0);}heightMetrics(t,e){let i=t.doc.lineAt(e).number,n=t.doc.lineAt(e+this.length).number,r=n-i+1,o,l=0;if(t.lineWrapping){let a=Math.min(this.height,t.lineHeight*r);o=a/r,this.length>r+1&&(l=(this.height-a)/(this.length-r-1));}else o=this.height/r;return {firstLine:i,lastLine:n,perLine:o,perChar:l}}blockAt(t,e,i,n){let{firstLine:r,lastLine:o,perLine:l,perChar:a}=this.heightMetrics(e,n);if(e.lineWrapping){let h=n+(t<e.lineHeight?0:Math.round(Math.max(0,Math.min(1,(t-i)/this.height))*this.length)),c=e.doc.lineAt(h),f=l+c.length*a,u=Math.max(i,t-f/2);return new te(c.from,c.length,u,f,0)}else {let h=Math.max(0,Math.min(o-r,Math.floor((t-i)/l))),{from:c,length:f}=e.doc.line(r+h);return new te(c,f,i+l*h,l,0)}}lineAt(t,e,i,n,r){if(e==Y.ByHeight)return this.blockAt(t,i,n,r);if(e==Y.ByPosNoHeight){let{from:d,to:p}=i.doc.lineAt(t);return new te(d,p-d,0,0,0)}let{firstLine:o,perLine:l,perChar:a}=this.heightMetrics(i,r),h=i.doc.lineAt(t),c=l+h.length*a,f=h.number-o,u=n+l*f+a*(h.from-r-f);return new te(h.from,h.length,Math.max(n,Math.min(u,n+this.height-c)),c,0)}forEachLine(t,e,i,n,r,o){t=Math.max(t,r),e=Math.min(e,r+this.length);let{firstLine:l,perLine:a,perChar:h}=this.heightMetrics(i,r);for(let c=t,f=n;c<=e;){let u=i.doc.lineAt(c);if(c==t){let p=u.number-l;f+=a*p+h*(t-r-p);}let d=a+h*u.length;o(new te(u.from,u.length,f,d,0)),f+=d,c=u.to+1;}}replace(t,e,i){let n=this.length-e;if(n>0){let r=i[i.length-1];r instanceof ot?i[i.length-1]=new ot(r.length+n):i.push(null,new ot(n-1));}if(t>0){let r=i[0];r instanceof ot?i[0]=new ot(t+r.length):i.unshift(new ot(t-1),null);}return bt.of(i)}decomposeLeft(t,e){e.push(new ot(t-1),null);}decomposeRight(t,e){e.push(null,new ot(this.length-t-1));}updateHeight(t,e=0,i=!1,n){let r=e+this.length;if(n&&n.from<=e+this.length&&n.more){let o=[],l=Math.max(e,n.from),a=-1;for(n.from>e&&o.push(new ot(n.from-e-1).updateHeight(t,e));l<=r&&n.more;){let c=t.doc.lineAt(l).length;o.length&&o.push(null);let f=n.heights[n.index++];a==-1?a=f:Math.abs(f-a)>=En&&(a=-2);let u=new Mt(c,f);u.outdated=!1,o.push(u),l+=c+1;}l<=r&&o.push(null,new ot(r-l).updateHeight(t,l));let h=bt.of(o);return (a<0||Math.abs(h.height-this.height)>=En||Math.abs(a-this.heightMetrics(t,e).perLine)>=En)&&(si=!0),Wn(this,h)}else (i||this.outdated)&&(this.setHeight(t.heightForGap(e,e+this.length)),this.outdated=!1);return this}toString(){return `gap(${this.length})`}}class dd extends bt{constructor(t,e,i){super(t.length+e+i.length,t.height+i.height,e|(t.outdated||i.outdated?2:0)),this.left=t,this.right=i,this.size=t.size+i.size;}get break(){return this.flags&1}blockAt(t,e,i,n){let r=i+this.left.height;return t<r?this.left.blockAt(t,e,i,n):this.right.blockAt(t,e,r,n+this.left.length+this.break)}lineAt(t,e,i,n,r){let o=n+this.left.height,l=r+this.left.length+this.break,a=e==Y.ByHeight?t<o:t<l,h=a?this.left.lineAt(t,e,i,n,r):this.right.lineAt(t,e,i,o,l);if(this.break||(a?h.to<l:h.from>l))return h;let c=e==Y.ByPosNoHeight?Y.ByPosNoHeight:Y.ByPos;return a?h.join(this.right.lineAt(l,c,i,o,l)):this.left.lineAt(l,c,i,n,r).join(h)}forEachLine(t,e,i,n,r,o){let l=n+this.left.height,a=r+this.left.length+this.break;if(this.break)t<a&&this.left.forEachLine(t,e,i,n,r,o),e>=a&&this.right.forEachLine(t,e,i,l,a,o);else {let h=this.lineAt(a,Y.ByPos,i,n,r);t<h.from&&this.left.forEachLine(t,h.from-1,i,n,r,o),h.to>=t&&h.from<=e&&o(h),e>h.to&&this.right.forEachLine(h.to+1,e,i,l,a,o);}}replace(t,e,i){let n=this.left.length+this.break;if(e<n)return this.balanced(this.left.replace(t,e,i),this.right);if(t>this.left.length)return this.balanced(this.left,this.right.replace(t-n,e-n,i));let r=[];t>0&&this.decomposeLeft(t,r);let o=r.length;for(let l of i)r.push(l);if(t>0&&nl(r,o-1),e<this.length){let l=r.length;this.decomposeRight(e,r),nl(r,l);}return bt.of(r)}decomposeLeft(t,e){let i=this.left.length;if(t<=i)return this.left.decomposeLeft(t,e);e.push(this.left),this.break&&(i++,t>=i&&e.push(null)),t>i&&this.right.decomposeLeft(t-i,e);}decomposeRight(t,e){let i=this.left.length,n=i+this.break;if(t>=n)return this.right.decomposeRight(t-n,e);t<i&&this.left.decomposeRight(t,e),this.break&&t<n&&e.push(null),e.push(this.right);}balanced(t,e){return t.size>2*e.size||e.size>2*t.size?bt.of(this.break?[t,null,e]:[t,e]):(this.left=Wn(this.left,t),this.right=Wn(this.right,e),this.setHeight(t.height+e.height),this.outdated=t.outdated||e.outdated,this.size=t.size+e.size,this.length=t.length+this.break+e.length,this)}updateHeight(t,e=0,i=!1,n){let{left:r,right:o}=this,l=e+r.length+this.break,a=null;return n&&n.from<=e+r.length&&n.more?a=r=r.updateHeight(t,e,i,n):r.updateHeight(t,e,i),n&&n.from<=l+o.length&&n.more?a=o=o.updateHeight(t,l,i,n):o.updateHeight(t,l,i),a?this.balanced(r,o):(this.height=this.left.height+this.right.height,this.outdated=!1,this)}toString(){return this.left+(this.break?" ":"-")+this.right}}function nl(s,t){let e,i;s[t]==null&&(e=s[t-1])instanceof ot&&(i=s[t+1])instanceof ot&&s.splice(t-1,3,new ot(e.length+1+i.length));}const pd=5;class Ur{constructor(t,e){this.pos=t,this.oracle=e,this.nodes=[],this.lineStart=-1,this.lineEnd=-1,this.covering=null,this.writtenTo=t;}get isCovered(){return this.covering&&this.nodes[this.nodes.length-1]==this.covering}span(t,e){if(this.lineStart>-1){let i=Math.min(e,this.lineEnd),n=this.nodes[this.nodes.length-1];n instanceof Mt?n.length+=i-this.pos:(i>this.pos||!this.isCovered)&&this.nodes.push(new Mt(i-this.pos,-1)),this.writtenTo=i,e>i&&(this.nodes.push(null),this.writtenTo++,this.lineStart=-1);}this.pos=e;}point(t,e,i){if(t<e||i.heightRelevant){let n=i.widget?i.widget.estimatedHeight:0,r=i.widget?i.widget.lineBreaks:0;n<0&&(n=this.oracle.lineHeight);let o=e-t;i.block?this.addBlock(new Rh(o,n,i)):(o||r||n>=pd)&&this.addLineDeco(n,r,o);}else e>t&&this.span(t,e);this.lineEnd>-1&&this.lineEnd<this.pos&&(this.lineEnd=this.oracle.doc.lineAt(this.pos).to);}enterLine(){if(this.lineStart>-1)return;let{from:t,to:e}=this.oracle.doc.lineAt(this.pos);this.lineStart=t,this.lineEnd=e,this.writtenTo<t&&((this.writtenTo<t-1||this.nodes[this.nodes.length-1]==null)&&this.nodes.push(this.blankContent(this.writtenTo,t-1)),this.nodes.push(null)),this.pos>t&&this.nodes.push(new Mt(this.pos-t,-1)),this.writtenTo=this.pos;}blankContent(t,e){let i=new ot(e-t);return this.oracle.doc.lineAt(t).to==e&&(i.flags|=4),i}ensureLine(){this.enterLine();let t=this.nodes.length?this.nodes[this.nodes.length-1]:null;if(t instanceof Mt)return t;let e=new Mt(0,-1);return this.nodes.push(e),e}addBlock(t){this.enterLine();let e=t.deco;e&&e.startSide>0&&!this.isCovered&&this.ensureLine(),this.nodes.push(t),this.writtenTo=this.pos=this.pos+t.length,e&&e.endSide>0&&(this.covering=t);}addLineDeco(t,e,i){let n=this.ensureLine();n.length+=i,n.collapsed+=i,n.widgetHeight=Math.max(n.widgetHeight,t),n.breaks+=e,this.writtenTo=this.pos=this.pos+i;}finish(t){let e=this.nodes.length==0?null:this.nodes[this.nodes.length-1];this.lineStart>-1&&!(e instanceof Mt)&&!this.isCovered?this.nodes.push(new Mt(0,-1)):(this.writtenTo<this.pos||e==null)&&this.nodes.push(this.blankContent(this.writtenTo,this.pos));let i=t;for(let n of this.nodes)n instanceof Mt&&n.updateHeight(this.oracle,i),i+=n?n.length:1;return this.nodes}static build(t,e,i,n){let r=new Ur(i,t);return z.spans(e,i,n,r,0),r.finish(i)}}function md(s,t,e){let i=new gd;return z.compare(s,t,e,i,0),i.changes}class gd{constructor(){this.changes=[];}compareRange(){}comparePoint(t,e,i,n){(t<e||i&&i.heightRelevant||n&&n.heightRelevant)&&On(t,e,this.changes,5);}}function yd(s,t){let e=s.getBoundingClientRect(),i=s.ownerDocument,n=i.defaultView||window,r=Math.max(0,e.left),o=Math.min(n.innerWidth,e.right),l=Math.max(0,e.top),a=Math.min(n.innerHeight,e.bottom);for(let h=s.parentNode;h&&h!=i.body;)if(h.nodeType==1){let c=h,f=window.getComputedStyle(c);if((c.scrollHeight>c.clientHeight||c.scrollWidth>c.clientWidth)&&f.overflow!="visible"){let u=c.getBoundingClientRect();r=Math.max(r,u.left),o=Math.min(o,u.right),l=Math.max(l,u.top),a=Math.min(h==s.parentNode?n.innerHeight:a,u.bottom);}h=f.position=="absolute"||f.position=="fixed"?c.offsetParent:c.parentNode;}else if(h.nodeType==11)h=h.host;else break;return {left:r-e.left,right:Math.max(r,o)-e.left,top:l-(e.top+t),bottom:Math.max(l,a)-(e.top+t)}}function bd(s){let t=s.getBoundingClientRect(),e=s.ownerDocument.defaultView||window;return t.left<e.innerWidth&&t.right>0&&t.top<e.innerHeight&&t.bottom>0}function wd(s,t){let e=s.getBoundingClientRect();return {left:0,right:e.right-e.left,top:t,bottom:e.bottom-(e.top+t)}}class gs{constructor(t,e,i,n){this.from=t,this.to=e,this.size=i,this.displaySize=n;}static same(t,e){if(t.length!=e.length)return !1;for(let i=0;i<t.length;i++){let n=t[i],r=e[i];if(n.from!=r.from||n.to!=r.to||n.size!=r.size)return !1}return !0}draw(t,e){return I.replace({widget:new xd(this.displaySize*(e?t.scaleY:t.scaleX),e)}).range(this.from,this.to)}}class xd extends oe{constructor(t,e){super(),this.size=t,this.vertical=e;}eq(t){return t.size==this.size&&t.vertical==this.vertical}toDOM(){let t=document.createElement("div");return this.vertical?t.style.height=this.size+"px":(t.style.width=this.size+"px",t.style.height="2px",t.style.display="inline-block"),t}get estimatedHeight(){return this.vertical?this.size:-1}}class sl{constructor(t){this.state=t,this.pixelViewport={left:0,right:window.innerWidth,top:0,bottom:0},this.inView=!0,this.paddingTop=0,this.paddingBottom=0,this.contentDOMWidth=0,this.contentDOMHeight=0,this.editorHeight=0,this.editorWidth=0,this.scrollTop=0,this.scrolledToBottom=!1,this.scaleX=1,this.scaleY=1,this.scrollAnchorPos=0,this.scrollAnchorHeight=-1,this.scaler=rl,this.scrollTarget=null,this.printing=!1,this.mustMeasureContent=!0,this.defaultTextDirection=J.LTR,this.visibleRanges=[],this.mustEnforceCursorAssoc=!1;let e=t.facet(zr).some(i=>typeof i!="function"&&i.class=="cm-lineWrapping");this.heightOracle=new fd(e),this.stateDeco=t.facet(Ri).filter(i=>typeof i!="function"),this.heightMap=bt.empty().applyChanges(this.stateDeco,H.empty,this.heightOracle.setDoc(t.doc),[new Ht(0,0,0,t.doc.length)]);for(let i=0;i<2&&(this.viewport=this.getViewport(0,null),!!this.updateForViewport());i++);this.updateViewportLines(),this.lineGaps=this.ensureLineGaps([]),this.lineGapDeco=I.set(this.lineGaps.map(i=>i.draw(this,!1))),this.computeVisibleRanges();}updateForViewport(){let t=[this.viewport],{main:e}=this.state.selection;for(let i=0;i<=1;i++){let n=i?e.head:e.anchor;if(!t.some(({from:r,to:o})=>n>=r&&n<=o)){let{from:r,to:o}=this.lineBlockAt(n);t.push(new hn(r,o));}}return this.viewports=t.sort((i,n)=>i.from-n.from),this.updateScaler()}updateScaler(){let t=this.scaler;return this.scaler=this.heightMap.height<=7e6?rl:new Gr(this.heightOracle,this.heightMap,this.viewports),t.eq(this.scaler)?0:2}updateViewportLines(){this.viewportLines=[],this.heightMap.forEachLine(this.viewport.from,this.viewport.to,this.heightOracle.setDoc(this.state.doc),0,0,t=>{this.viewportLines.push(Si(t,this.scaler));});}update(t,e=null){this.state=t.state;let i=this.stateDeco;this.stateDeco=this.state.facet(Ri).filter(c=>typeof c!="function");let n=t.changedRanges,r=Ht.extendWithRanges(n,md(i,this.stateDeco,t?t.changes:it.empty(this.state.doc.length))),o=this.heightMap.height,l=this.scrolledToBottom?null:this.scrollAnchorAt(this.scrollTop);il(),this.heightMap=this.heightMap.applyChanges(this.stateDeco,t.startState.doc,this.heightOracle.setDoc(this.state.doc),r),(this.heightMap.height!=o||si)&&(t.flags|=2),l?(this.scrollAnchorPos=t.changes.mapPos(l.from,-1),this.scrollAnchorHeight=l.top):(this.scrollAnchorPos=-1,this.scrollAnchorHeight=this.heightMap.height);let a=r.length?this.mapViewport(this.viewport,t.changes):this.viewport;(e&&(e.range.head<a.from||e.range.head>a.to)||!this.viewportIsAppropriate(a))&&(a=this.getViewport(0,e));let h=a.from!=this.viewport.from||a.to!=this.viewport.to;this.viewport=a,t.flags|=this.updateForViewport(),(h||!t.changes.empty||t.flags&2)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(this.mapLineGaps(this.lineGaps,t.changes))),t.flags|=this.computeVisibleRanges(t.changes),e&&(this.scrollTarget=e),!this.mustEnforceCursorAssoc&&t.selectionSet&&t.view.lineWrapping&&t.state.selection.main.empty&&t.state.selection.main.assoc&&!t.state.facet(gh)&&(this.mustEnforceCursorAssoc=!0);}measure(t){let e=t.contentDOM,i=window.getComputedStyle(e),n=this.heightOracle,r=i.whiteSpace;this.defaultTextDirection=i.direction=="rtl"?J.RTL:J.LTR;let o=this.heightOracle.mustRefreshForWrapping(r),l=e.getBoundingClientRect(),a=o||this.mustMeasureContent||this.contentDOMHeight!=l.height;this.contentDOMHeight=l.height,this.mustMeasureContent=!1;let h=0,c=0;if(l.width&&l.height){let{scaleX:S,scaleY:w}=qa(e,l);(S>.005&&Math.abs(this.scaleX-S)>.005||w>.005&&Math.abs(this.scaleY-w)>.005)&&(this.scaleX=S,this.scaleY=w,h|=16,o=a=!0);}let f=(parseInt(i.paddingTop)||0)*this.scaleY,u=(parseInt(i.paddingBottom)||0)*this.scaleY;(this.paddingTop!=f||this.paddingBottom!=u)&&(this.paddingTop=f,this.paddingBottom=u,h|=18),this.editorWidth!=t.scrollDOM.clientWidth&&(n.lineWrapping&&(a=!0),this.editorWidth=t.scrollDOM.clientWidth,h|=16);let d=t.scrollDOM.scrollTop*this.scaleY;this.scrollTop!=d&&(this.scrollAnchorHeight=-1,this.scrollTop=d),this.scrolledToBottom=Ua(t.scrollDOM);let p=(this.printing?wd:yd)(e,this.paddingTop),y=p.top-this.pixelViewport.top,g=p.bottom-this.pixelViewport.bottom;this.pixelViewport=p;let b=this.pixelViewport.bottom>this.pixelViewport.top&&this.pixelViewport.right>this.pixelViewport.left;if(b!=this.inView&&(this.inView=b,b&&(a=!0)),!this.inView&&!this.scrollTarget&&!bd(t.dom))return 0;let x=l.width;if((this.contentDOMWidth!=x||this.editorHeight!=t.scrollDOM.clientHeight)&&(this.contentDOMWidth=l.width,this.editorHeight=t.scrollDOM.clientHeight,h|=16),a){let S=t.docView.measureVisibleLineHeights(this.viewport);if(n.mustRefreshForHeights(S)&&(o=!0),o||n.lineWrapping&&Math.abs(x-this.contentDOMWidth)>n.charWidth){let{lineHeight:w,charWidth:C,textHeight:A}=t.docView.measureTextSize();o=w>0&&n.refresh(r,w,C,A,x/C,S),o&&(t.docView.minWidth=0,h|=16);}y>0&&g>0?c=Math.max(y,g):y<0&&g<0&&(c=Math.min(y,g)),il();for(let w of this.viewports){let C=w.from==this.viewport.from?S:t.docView.measureVisibleLineHeights(w);this.heightMap=(o?bt.empty().applyChanges(this.stateDeco,H.empty,this.heightOracle,[new Ht(0,0,0,t.state.doc.length)]):this.heightMap).updateHeight(n,0,o,new ud(w.from,C));}si&&(h|=2);}let k=!this.viewportIsAppropriate(this.viewport,c)||this.scrollTarget&&(this.scrollTarget.range.head<this.viewport.from||this.scrollTarget.range.head>this.viewport.to);return k&&(h&2&&(h|=this.updateScaler()),this.viewport=this.getViewport(c,this.scrollTarget),h|=this.updateForViewport()),(h&2||k)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(o?[]:this.lineGaps,t)),h|=this.computeVisibleRanges(),this.mustEnforceCursorAssoc&&(this.mustEnforceCursorAssoc=!1,t.docView.enforceCursorAssoc()),h}get visibleTop(){return this.scaler.fromDOM(this.pixelViewport.top)}get visibleBottom(){return this.scaler.fromDOM(this.pixelViewport.bottom)}getViewport(t,e){let i=.5-Math.max(-.5,Math.min(.5,t/1e3/2)),n=this.heightMap,r=this.heightOracle,{visibleTop:o,visibleBottom:l}=this,a=new hn(n.lineAt(o-i*1e3,Y.ByHeight,r,0,0).from,n.lineAt(l+(1-i)*1e3,Y.ByHeight,r,0,0).to);if(e){let{head:h}=e.range;if(h<a.from||h>a.to){let c=Math.min(this.editorHeight,this.pixelViewport.bottom-this.pixelViewport.top),f=n.lineAt(h,Y.ByPos,r,0,0),u;e.y=="center"?u=(f.top+f.bottom)/2-c/2:e.y=="start"||e.y=="nearest"&&h<a.from?u=f.top:u=f.bottom-c,a=new hn(n.lineAt(u-1e3/2,Y.ByHeight,r,0,0).from,n.lineAt(u+c+1e3/2,Y.ByHeight,r,0,0).to);}}return a}mapViewport(t,e){let i=e.mapPos(t.from,-1),n=e.mapPos(t.to,1);return new hn(this.heightMap.lineAt(i,Y.ByPos,this.heightOracle,0,0).from,this.heightMap.lineAt(n,Y.ByPos,this.heightOracle,0,0).to)}viewportIsAppropriate({from:t,to:e},i=0){if(!this.inView)return !0;let{top:n}=this.heightMap.lineAt(t,Y.ByPos,this.heightOracle,0,0),{bottom:r}=this.heightMap.lineAt(e,Y.ByPos,this.heightOracle,0,0),{visibleTop:o,visibleBottom:l}=this;return (t==0||n<=o-Math.max(10,Math.min(-i,250)))&&(e==this.state.doc.length||r>=l+Math.max(10,Math.min(i,250)))&&n>o-2*1e3&&r<l+2*1e3}mapLineGaps(t,e){if(!t.length||e.empty)return t;let i=[];for(let n of t)e.touchesRange(n.from,n.to)||i.push(new gs(e.mapPos(n.from),e.mapPos(n.to),n.size,n.displaySize));return i}ensureLineGaps(t,e){let i=this.heightOracle.lineWrapping,n=i?1e4:2e3,r=n>>1,o=n<<1;if(this.defaultTextDirection!=J.LTR&&!i)return [];let l=[],a=(c,f,u,d)=>{if(f-c<r)return;let p=this.state.selection.main,y=[p.from];p.empty||y.push(p.to);for(let b of y)if(b>c&&b<f){a(c,b-10,u,d),a(b+10,f,u,d);return}let g=kd(t,b=>b.from>=u.from&&b.to<=u.to&&Math.abs(b.from-c)<r&&Math.abs(b.to-f)<r&&!y.some(x=>b.from<x&&b.to>x));if(!g){if(f<u.to&&e&&i&&e.visibleRanges.some(k=>k.from<=f&&k.to>=f)){let k=e.moveToLineBoundary(v.cursor(f),!1,!0).head;k>c&&(f=k);}let b=this.gapSize(u,c,f,d),x=i||b<2e6?b:2e6;g=new gs(c,f,b,x);}l.push(g);},h=c=>{if(c.length<o||c.type!=yt.Text)return;let f=vd(c.from,c.to,this.stateDeco);if(f.total<o)return;let u=this.scrollTarget?this.scrollTarget.range.head:null,d,p;if(i){let y=n/this.heightOracle.lineLength*this.heightOracle.lineHeight,g,b;if(u!=null){let x=fn(f,u),k=((this.visibleBottom-this.visibleTop)/2+y)/c.height;g=x-k,b=x+k;}else g=(this.visibleTop-c.top-y)/c.height,b=(this.visibleBottom-c.top+y)/c.height;d=cn(f,g),p=cn(f,b);}else {let y=f.total*this.heightOracle.charWidth,g=n*this.heightOracle.charWidth,b=0;if(y>2e6)for(let C of t)C.from>=c.from&&C.from<c.to&&C.size!=C.displaySize&&C.from*this.heightOracle.charWidth+b<this.pixelViewport.left&&(b=C.size-C.displaySize);let x=this.pixelViewport.left+b,k=this.pixelViewport.right+b,S,w;if(u!=null){let C=fn(f,u),A=((k-x)/2+g)/y;S=C-A,w=C+A;}else S=(x-g)/y,w=(k+g)/y;d=cn(f,S),p=cn(f,w);}d>c.from&&a(c.from,d,c,f),p<c.to&&a(p,c.to,c,f);};for(let c of this.viewportLines)Array.isArray(c.type)?c.type.forEach(h):h(c);return l}gapSize(t,e,i,n){let r=fn(n,i)-fn(n,e);return this.heightOracle.lineWrapping?t.height*r:n.total*this.heightOracle.charWidth*r}updateLineGaps(t){gs.same(t,this.lineGaps)||(this.lineGaps=t,this.lineGapDeco=I.set(t.map(e=>e.draw(this,this.heightOracle.lineWrapping))));}computeVisibleRanges(t){let e=this.stateDeco;this.lineGaps.length&&(e=e.concat(this.lineGapDeco));let i=[];z.spans(e,this.viewport.from,this.viewport.to,{span(r,o){i.push({from:r,to:o});},point(){}},20);let n=0;if(i.length!=this.visibleRanges.length)n=12;else for(let r=0;r<i.length&&!(n&8);r++){let o=this.visibleRanges[r],l=i[r];(o.from!=l.from||o.to!=l.to)&&(n|=4,t&&t.mapPos(o.from,-1)==l.from&&t.mapPos(o.to,1)==l.to||(n|=8));}return this.visibleRanges=i,n}lineBlockAt(t){return t>=this.viewport.from&&t<=this.viewport.to&&this.viewportLines.find(e=>e.from<=t&&e.to>=t)||Si(this.heightMap.lineAt(t,Y.ByPos,this.heightOracle,0,0),this.scaler)}lineBlockAtHeight(t){return t>=this.viewportLines[0].top&&t<=this.viewportLines[this.viewportLines.length-1].bottom&&this.viewportLines.find(e=>e.top<=t&&e.bottom>=t)||Si(this.heightMap.lineAt(this.scaler.fromDOM(t),Y.ByHeight,this.heightOracle,0,0),this.scaler)}scrollAnchorAt(t){let e=this.lineBlockAtHeight(t+8);return e.from>=this.viewport.from||this.viewportLines[0].top-t>200?e:this.viewportLines[0]}elementAtHeight(t){return Si(this.heightMap.blockAt(this.scaler.fromDOM(t),this.heightOracle,0,0),this.scaler)}get docHeight(){return this.scaler.toDOM(this.heightMap.height)}get contentHeight(){return this.docHeight+this.paddingTop+this.paddingBottom}}class hn{constructor(t,e){this.from=t,this.to=e;}}function vd(s,t,e){let i=[],n=s,r=0;return z.spans(e,s,t,{span(){},point(o,l){o>n&&(i.push({from:n,to:o}),r+=o-n),n=l;}},20),n<t&&(i.push({from:n,to:t}),r+=t-n),{total:r,ranges:i}}function cn({total:s,ranges:t},e){if(e<=0)return t[0].from;if(e>=1)return t[t.length-1].to;let i=Math.floor(s*e);for(let n=0;;n++){let{from:r,to:o}=t[n],l=o-r;if(i<=l)return r+i;i-=l;}}function fn(s,t){let e=0;for(let{from:i,to:n}of s.ranges){if(t<=n){e+=t-i;break}e+=n-i;}return e/s.total}function kd(s,t){for(let e of s)if(t(e))return e}const rl={toDOM(s){return s},fromDOM(s){return s},scale:1,eq(s){return s==this}};class Gr{constructor(t,e,i){let n=0,r=0,o=0;this.viewports=i.map(({from:l,to:a})=>{let h=e.lineAt(l,Y.ByPos,t,0,0).top,c=e.lineAt(a,Y.ByPos,t,0,0).bottom;return n+=c-h,{from:l,to:a,top:h,bottom:c,domTop:0,domBottom:0}}),this.scale=(7e6-n)/(e.height-n);for(let l of this.viewports)l.domTop=o+(l.top-r)*this.scale,o=l.domBottom=l.domTop+(l.bottom-l.top),r=l.bottom;}toDOM(t){for(let e=0,i=0,n=0;;e++){let r=e<this.viewports.length?this.viewports[e]:null;if(!r||t<r.top)return n+(t-i)*this.scale;if(t<=r.bottom)return r.domTop+(t-r.top);i=r.bottom,n=r.domBottom;}}fromDOM(t){for(let e=0,i=0,n=0;;e++){let r=e<this.viewports.length?this.viewports[e]:null;if(!r||t<r.domTop)return i+(t-n)/this.scale;if(t<=r.domBottom)return r.top+(t-r.domTop);i=r.bottom,n=r.domBottom;}}eq(t){return t instanceof Gr?this.scale==t.scale&&this.viewports.length==t.viewports.length&&this.viewports.every((e,i)=>e.from==t.viewports[i].from&&e.to==t.viewports[i].to):!1}}function Si(s,t){if(t.scale==1)return s;let e=t.toDOM(s.top),i=t.toDOM(s.bottom);return new te(s.from,s.length,e,i-e,Array.isArray(s._content)?s._content.map(n=>Si(n,t)):s._content)}const un=T.define({combine:s=>s.join(" ")}),pr=T.define({combine:s=>s.indexOf(!0)>-1}),mr=ve.newName(),Ih=ve.newName(),Nh=ve.newName(),Fh={"&light":"."+Ih,"&dark":"."+Nh};function gr(s,t,e){return new ve(t,{finish(i){return /&/.test(i)?i.replace(/&\w*/,n=>{if(n=="&")return s;if(!e||!e[n])throw new RangeError(`Unsupported selector: ${n}`);return e[n]}):s+" "+i}})}const Sd=gr("."+mr,{"&":{position:"relative !important",boxSizing:"border-box","&.cm-focused":{outline:"1px dotted #212121"},display:"flex !important",flexDirection:"column"},".cm-scroller":{display:"flex !important",alignItems:"flex-start !important",fontFamily:"monospace",lineHeight:1.4,height:"100%",overflowX:"auto",position:"relative",zIndex:0,overflowAnchor:"none"},".cm-content":{margin:0,flexGrow:2,flexShrink:0,display:"block",whiteSpace:"pre",wordWrap:"normal",boxSizing:"border-box",minHeight:"100%",padding:"4px 0",outline:"none","&[contenteditable=true]":{WebkitUserModify:"read-write-plaintext-only"}},".cm-lineWrapping":{whiteSpace_fallback:"pre-wrap",whiteSpace:"break-spaces",wordBreak:"break-word",overflowWrap:"anywhere",flexShrink:1},"&light .cm-content":{caretColor:"black"},"&dark .cm-content":{caretColor:"white"},".cm-line":{display:"block",padding:"0 2px 0 6px"},".cm-layer":{position:"absolute",left:0,top:0,contain:"size style","& > *":{position:"absolute"}},"&light .cm-selectionBackground":{background:"#d9d9d9"},"&dark .cm-selectionBackground":{background:"#222"},"&light.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#d7d4f0"},"&dark.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#233"},".cm-cursorLayer":{pointerEvents:"none"},"&.cm-focused > .cm-scroller > .cm-cursorLayer":{animation:"steps(1) cm-blink 1.2s infinite"},"@keyframes cm-blink":{"0%":{},"50%":{opacity:0},"100%":{}},"@keyframes cm-blink2":{"0%":{},"50%":{opacity:0},"100%":{}},".cm-cursor, .cm-dropCursor":{borderLeft:"1.2px solid black",marginLeft:"-0.6px",pointerEvents:"none"},".cm-cursor":{display:"none"},"&dark .cm-cursor":{borderLeftColor:"#ddd"},".cm-dropCursor":{position:"absolute"},"&.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor":{display:"block"},".cm-iso":{unicodeBidi:"isolate"},".cm-announced":{position:"fixed",top:"-10000px"},"@media print":{".cm-announced":{display:"none"}},"&light .cm-activeLine":{backgroundColor:"#cceeff44"},"&dark .cm-activeLine":{backgroundColor:"#99eeff33"},"&light .cm-specialChar":{color:"red"},"&dark .cm-specialChar":{color:"#f78"},".cm-gutters":{flexShrink:0,display:"flex",height:"100%",boxSizing:"border-box",insetInlineStart:0,zIndex:200},"&light .cm-gutters":{backgroundColor:"#f5f5f5",color:"#6c6c6c",borderRight:"1px solid #ddd"},"&dark .cm-gutters":{backgroundColor:"#333338",color:"#ccc"},".cm-gutter":{display:"flex !important",flexDirection:"column",flexShrink:0,boxSizing:"border-box",minHeight:"100%",overflow:"hidden"},".cm-gutterElement":{boxSizing:"border-box"},".cm-lineNumbers .cm-gutterElement":{padding:"0 3px 0 5px",minWidth:"20px",textAlign:"right",whiteSpace:"nowrap"},"&light .cm-activeLineGutter":{backgroundColor:"#e2f2ff"},"&dark .cm-activeLineGutter":{backgroundColor:"#222227"},".cm-panels":{boxSizing:"border-box",position:"sticky",left:0,right:0,zIndex:300},"&light .cm-panels":{backgroundColor:"#f5f5f5",color:"black"},"&light .cm-panels-top":{borderBottom:"1px solid #ddd"},"&light .cm-panels-bottom":{borderTop:"1px solid #ddd"},"&dark .cm-panels":{backgroundColor:"#333338",color:"white"},".cm-tab":{display:"inline-block",overflow:"hidden",verticalAlign:"bottom"},".cm-widgetBuffer":{verticalAlign:"text-top",height:"1em",width:0,display:"inline"},".cm-placeholder":{color:"#888",display:"inline-block",verticalAlign:"top"},".cm-highlightSpace":{backgroundImage:"radial-gradient(circle at 50% 55%, #aaa 20%, transparent 5%)",backgroundPosition:"center"},".cm-highlightTab":{backgroundImage:`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>')`,backgroundSize:"auto 100%",backgroundPosition:"right 90%",backgroundRepeat:"no-repeat"},".cm-trailingSpace":{backgroundColor:"#ff332255"},".cm-button":{verticalAlign:"middle",color:"inherit",fontSize:"70%",padding:".2em 1em",borderRadius:"1px"},"&light .cm-button":{backgroundImage:"linear-gradient(#eff1f5, #d9d9df)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#b4b4b4, #d0d3d6)"}},"&dark .cm-button":{backgroundImage:"linear-gradient(#393939, #111)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#111, #333)"}},".cm-textfield":{verticalAlign:"middle",color:"inherit",fontSize:"70%",border:"1px solid silver",padding:".2em .5em"},"&light .cm-textfield":{backgroundColor:"white"},"&dark .cm-textfield":{border:"1px solid #555",backgroundColor:"inherit"}},Fh),Cd={childList:!0,characterData:!0,subtree:!0,attributes:!0,characterDataOldValue:!0},ys=D.ie&&D.ie_version<=11;class Ad{constructor(t){this.view=t,this.active=!1,this.editContext=null,this.selectionRange=new au,this.selectionChanged=!1,this.delayedFlush=-1,this.resizeTimeout=-1,this.queue=[],this.delayedAndroidKey=null,this.flushingAndroidKey=-1,this.lastChange=0,this.scrollTargets=[],this.intersection=null,this.resizeScroll=null,this.intersecting=!1,this.gapIntersection=null,this.gaps=[],this.printQuery=null,this.parentCheck=-1,this.dom=t.contentDOM,this.observer=new MutationObserver(e=>{for(let i of e)this.queue.push(i);(D.ie&&D.ie_version<=11||D.ios&&t.composing)&&e.some(i=>i.type=="childList"&&i.removedNodes.length||i.type=="characterData"&&i.oldValue.length>i.target.nodeValue.length)?this.flushSoon():this.flush();}),window.EditContext&&t.constructor.EDIT_CONTEXT!==!1&&!(D.chrome&&D.chrome_version<126)&&(this.editContext=new Dd(t),t.state.facet(he)&&(t.contentDOM.editContext=this.editContext.editContext)),ys&&(this.onCharData=e=>{this.queue.push({target:e.target,type:"characterData",oldValue:e.prevValue}),this.flushSoon();}),this.onSelectionChange=this.onSelectionChange.bind(this),this.onResize=this.onResize.bind(this),this.onPrint=this.onPrint.bind(this),this.onScroll=this.onScroll.bind(this),window.matchMedia&&(this.printQuery=window.matchMedia("print")),typeof ResizeObserver=="function"&&(this.resizeScroll=new ResizeObserver(()=>{var e;((e=this.view.docView)===null||e===void 0?void 0:e.lastUpdate)<Date.now()-75&&this.onResize();}),this.resizeScroll.observe(t.scrollDOM)),this.addWindowListeners(this.win=t.win),this.start(),typeof IntersectionObserver=="function"&&(this.intersection=new IntersectionObserver(e=>{this.parentCheck<0&&(this.parentCheck=setTimeout(this.listenForScroll.bind(this),1e3)),e.length>0&&e[e.length-1].intersectionRatio>0!=this.intersecting&&(this.intersecting=!this.intersecting,this.intersecting!=this.view.inView&&this.onScrollChanged(document.createEvent("Event")));},{threshold:[0,.001]}),this.intersection.observe(this.dom),this.gapIntersection=new IntersectionObserver(e=>{e.length>0&&e[e.length-1].intersectionRatio>0&&this.onScrollChanged(document.createEvent("Event"));},{})),this.listenForScroll(),this.readSelectionRange();}onScrollChanged(t){this.view.inputState.runHandlers("scroll",t),this.intersecting&&this.view.measure();}onScroll(t){this.intersecting&&this.flush(!1),this.editContext&&this.view.requestMeasure(this.editContext.measureReq),this.onScrollChanged(t);}onResize(){this.resizeTimeout<0&&(this.resizeTimeout=setTimeout(()=>{this.resizeTimeout=-1,this.view.requestMeasure();},50));}onPrint(t){(t.type=="change"||!t.type)&&!t.matches||(this.view.viewState.printing=!0,this.view.measure(),setTimeout(()=>{this.view.viewState.printing=!1,this.view.requestMeasure();},500));}updateGaps(t){if(this.gapIntersection&&(t.length!=this.gaps.length||this.gaps.some((e,i)=>e!=t[i]))){this.gapIntersection.disconnect();for(let e of t)this.gapIntersection.observe(e);this.gaps=t;}}onSelectionChange(t){let e=this.selectionChanged;if(!this.readSelectionRange()||this.delayedAndroidKey)return;let{view:i}=this,n=this.selectionRange;if(i.state.facet(he)?i.root.activeElement!=this.dom:!Tn(this.dom,n))return;let r=n.anchorNode&&i.docView.nearest(n.anchorNode);if(r&&r.ignoreEvent(t)){e||(this.selectionChanged=!1);return}(D.ie&&D.ie_version<=11||D.android&&D.chrome)&&!i.state.selection.main.empty&&n.focusNode&&Mi(n.focusNode,n.focusOffset,n.anchorNode,n.anchorOffset)?this.flushSoon():this.flush(!1);}readSelectionRange(){let{view:t}=this,e=Li(t.root);if(!e)return !1;let i=D.safari&&t.root.nodeType==11&&t.root.activeElement==this.dom&&Md(this.view,e)||e;if(!i||this.selectionRange.eq(i))return !1;let n=Tn(this.dom,i);return n&&!this.selectionChanged&&t.inputState.lastFocusTime>Date.now()-200&&t.inputState.lastTouchTime<Date.now()-300&&cu(this.dom,i)?(this.view.inputState.lastFocusTime=0,t.docView.updateSelection(),!1):(this.selectionRange.setRange(i),n&&(this.selectionChanged=!0),!0)}setSelectionRange(t,e){this.selectionRange.set(t.node,t.offset,e.node,e.offset),this.selectionChanged=!1;}clearSelectionRange(){this.selectionRange.set(null,0,null,0);}listenForScroll(){this.parentCheck=-1;let t=0,e=null;for(let i=this.dom;i;)if(i.nodeType==1)!e&&t<this.scrollTargets.length&&this.scrollTargets[t]==i?t++:e||(e=this.scrollTargets.slice(0,t)),e&&e.push(i),i=i.assignedSlot||i.parentNode;else if(i.nodeType==11)i=i.host;else break;if(t<this.scrollTargets.length&&!e&&(e=this.scrollTargets.slice(0,t)),e){for(let i of this.scrollTargets)i.removeEventListener("scroll",this.onScroll);for(let i of this.scrollTargets=e)i.addEventListener("scroll",this.onScroll);}}ignore(t){if(!this.active)return t();try{return this.stop(),t()}finally{this.start(),this.clear();}}start(){this.active||(this.observer.observe(this.dom,Cd),ys&&this.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.active=!0);}stop(){this.active&&(this.active=!1,this.observer.disconnect(),ys&&this.dom.removeEventListener("DOMCharacterDataModified",this.onCharData));}clear(){this.processRecords(),this.queue.length=0,this.selectionChanged=!1;}delayAndroidKey(t,e){var i;if(!this.delayedAndroidKey){let n=()=>{let r=this.delayedAndroidKey;r&&(this.clearDelayedAndroidKey(),this.view.inputState.lastKeyCode=r.keyCode,this.view.inputState.lastKeyTime=Date.now(),!this.flush()&&r.force&&Qe(this.dom,r.key,r.keyCode));};this.flushingAndroidKey=this.view.win.requestAnimationFrame(n);}(!this.delayedAndroidKey||t=="Enter")&&(this.delayedAndroidKey={key:t,keyCode:e,force:this.lastChange<Date.now()-50||!!(!((i=this.delayedAndroidKey)===null||i===void 0)&&i.force)});}clearDelayedAndroidKey(){this.win.cancelAnimationFrame(this.flushingAndroidKey),this.delayedAndroidKey=null,this.flushingAndroidKey=-1;}flushSoon(){this.delayedFlush<0&&(this.delayedFlush=this.view.win.requestAnimationFrame(()=>{this.delayedFlush=-1,this.flush();}));}forceFlush(){this.delayedFlush>=0&&(this.view.win.cancelAnimationFrame(this.delayedFlush),this.delayedFlush=-1),this.flush();}pendingRecords(){for(let t of this.observer.takeRecords())this.queue.push(t);return this.queue}processRecords(){let t=this.pendingRecords();t.length&&(this.queue=[]);let e=-1,i=-1,n=!1;for(let r of t){let o=this.readMutation(r);o&&(o.typeOver&&(n=!0),e==-1?{from:e,to:i}=o:(e=Math.min(o.from,e),i=Math.max(o.to,i)));}return {from:e,to:i,typeOver:n}}readChange(){let{from:t,to:e,typeOver:i}=this.processRecords(),n=this.selectionChanged&&Tn(this.dom,this.selectionRange);if(t<0&&!n)return null;t>-1&&(this.lastChange=Date.now()),this.view.inputState.lastFocusTime=0,this.selectionChanged=!1;let r=new qu(this.view,t,e,i);return this.view.docView.domChanged={newSel:r.newSel?r.newSel.main:null},r}flush(t=!0){if(this.delayedFlush>=0||this.delayedAndroidKey)return !1;t&&this.readSelectionRange();let e=this.readChange();if(!e)return this.view.requestMeasure(),!1;let i=this.view.state,n=Ah(this.view,e);return this.view.state==i&&(e.domChanged||e.newSel&&!e.newSel.main.eq(this.view.state.selection.main))&&this.view.update([]),n}readMutation(t){let e=this.view.docView.nearest(t.target);if(!e||e.ignoreMutation(t))return null;if(e.markDirty(t.type=="attributes"),t.type=="attributes"&&(e.flags|=4),t.type=="childList"){let i=ol(e,t.previousSibling||t.target.previousSibling,-1),n=ol(e,t.nextSibling||t.target.nextSibling,1);return {from:i?e.posAfter(i):e.posAtStart,to:n?e.posBefore(n):e.posAtEnd,typeOver:!1}}else return t.type=="characterData"?{from:e.posAtStart,to:e.posAtEnd,typeOver:t.target.nodeValue==t.oldValue}:null}setWindow(t){t!=this.win&&(this.removeWindowListeners(this.win),this.win=t,this.addWindowListeners(this.win));}addWindowListeners(t){t.addEventListener("resize",this.onResize),this.printQuery?this.printQuery.addEventListener?this.printQuery.addEventListener("change",this.onPrint):this.printQuery.addListener(this.onPrint):t.addEventListener("beforeprint",this.onPrint),t.addEventListener("scroll",this.onScroll),t.document.addEventListener("selectionchange",this.onSelectionChange);}removeWindowListeners(t){t.removeEventListener("scroll",this.onScroll),t.removeEventListener("resize",this.onResize),this.printQuery?this.printQuery.removeEventListener?this.printQuery.removeEventListener("change",this.onPrint):this.printQuery.removeListener(this.onPrint):t.removeEventListener("beforeprint",this.onPrint),t.document.removeEventListener("selectionchange",this.onSelectionChange);}update(t){this.editContext&&(this.editContext.update(t),t.startState.facet(he)!=t.state.facet(he)&&(t.view.contentDOM.editContext=t.state.facet(he)?this.editContext.editContext:null));}destroy(){var t,e,i;this.stop(),(t=this.intersection)===null||t===void 0||t.disconnect(),(e=this.gapIntersection)===null||e===void 0||e.disconnect(),(i=this.resizeScroll)===null||i===void 0||i.disconnect();for(let n of this.scrollTargets)n.removeEventListener("scroll",this.onScroll);this.removeWindowListeners(this.win),clearTimeout(this.parentCheck),clearTimeout(this.resizeTimeout),this.win.cancelAnimationFrame(this.delayedFlush),this.win.cancelAnimationFrame(this.flushingAndroidKey),this.editContext&&(this.view.contentDOM.editContext=null,this.editContext.destroy());}}function ol(s,t,e){for(;t;){let i=U.get(t);if(i&&i.parent==s)return i;let n=t.parentNode;t=n!=s.dom?n:e>0?t.nextSibling:t.previousSibling;}return null}function ll(s,t){let e=t.startContainer,i=t.startOffset,n=t.endContainer,r=t.endOffset,o=s.docView.domAtPos(s.state.selection.main.anchor);return Mi(o.node,o.offset,n,r)&&([e,i,n,r]=[n,r,e,i]),{anchorNode:e,anchorOffset:i,focusNode:n,focusOffset:r}}function Md(s,t){if(t.getComposedRanges){let n=t.getComposedRanges(s.root)[0];if(n)return ll(s,n)}let e=null;function i(n){n.preventDefault(),n.stopImmediatePropagation(),e=n.getTargetRanges()[0];}return s.contentDOM.addEventListener("beforeinput",i,!0),s.dom.ownerDocument.execCommand("indent"),s.contentDOM.removeEventListener("beforeinput",i,!0),e?ll(s,e):null}class Dd{constructor(t){this.from=0,this.to=0,this.pendingContextChange=null,this.handlers=Object.create(null),this.composing=null,this.resetRange(t.state);let e=this.editContext=new window.EditContext({text:t.state.doc.sliceString(this.from,this.to),selectionStart:this.toContextPos(Math.max(this.from,Math.min(this.to,t.state.selection.main.anchor))),selectionEnd:this.toContextPos(t.state.selection.main.head)});this.handlers.textupdate=i=>{let n=t.state.selection.main,{anchor:r,head:o}=n,l=this.toEditorPos(i.updateRangeStart),a=this.toEditorPos(i.updateRangeEnd);t.inputState.composing>=0&&!this.composing&&(this.composing={contextBase:i.updateRangeStart,editorBase:l,drifted:!1});let h={from:l,to:a,insert:H.of(i.text.split(`
`))};if(h.from==this.from&&r<this.from?h.from=r:h.to==this.to&&r>this.to&&(h.to=r),h.from==h.to&&!h.insert.length){let c=v.single(this.toEditorPos(i.selectionStart),this.toEditorPos(i.selectionEnd));c.main.eq(n)||t.dispatch({selection:c,userEvent:"select"});return}if((D.mac||D.android)&&h.from==o-1&&/^\. ?$/.test(i.text)&&t.contentDOM.getAttribute("autocorrect")=="off"&&(h={from:l,to:a,insert:H.of([i.text.replace("."," ")])}),this.pendingContextChange=h,!t.state.readOnly){let c=this.to-this.from+(h.to-h.from+h.insert.length);Kr(t,h,v.single(this.toEditorPos(i.selectionStart,c),this.toEditorPos(i.selectionEnd,c)));}this.pendingContextChange&&(this.revertPending(t.state),this.setSelection(t.state));},this.handlers.characterboundsupdate=i=>{let n=[],r=null;for(let o=this.toEditorPos(i.rangeStart),l=this.toEditorPos(i.rangeEnd);o<l;o++){let a=t.coordsForChar(o);r=a&&new DOMRect(a.left,a.top,a.right-a.left,a.bottom-a.top)||r||new DOMRect,n.push(r);}e.updateCharacterBounds(i.rangeStart,n);},this.handlers.textformatupdate=i=>{let n=[];for(let r of i.getTextFormats()){let o=r.underlineStyle,l=r.underlineThickness;if(o!="None"&&l!="None"){let a=this.toEditorPos(r.rangeStart),h=this.toEditorPos(r.rangeEnd);if(a<h){let c=`text-decoration: underline ${o=="Dashed"?"dashed ":o=="Squiggle"?"wavy ":""}${l=="Thin"?1:2}px`;n.push(I.mark({attributes:{style:c}}).range(a,h));}}}t.dispatch({effects:bh.of(I.set(n))});},this.handlers.compositionstart=()=>{t.inputState.composing<0&&(t.inputState.composing=0,t.inputState.compositionFirstChange=!0);},this.handlers.compositionend=()=>{if(t.inputState.composing=-1,t.inputState.compositionFirstChange=null,this.composing){let{drifted:i}=this.composing;this.composing=null,i&&this.reset(t.state);}};for(let i in this.handlers)e.addEventListener(i,this.handlers[i]);this.measureReq={read:i=>{this.editContext.updateControlBounds(i.contentDOM.getBoundingClientRect());let n=Li(i.root);n&&n.rangeCount&&this.editContext.updateSelectionBounds(n.getRangeAt(0).getBoundingClientRect());}};}applyEdits(t){let e=0,i=!1,n=this.pendingContextChange;return t.changes.iterChanges((r,o,l,a,h)=>{if(i)return;let c=h.length-(o-r);if(n&&o>=n.to)if(n.from==r&&n.to==o&&n.insert.eq(h)){n=this.pendingContextChange=null,e+=c,this.to+=c;return}else n=null,this.revertPending(t.state);if(r+=e,o+=e,o<=this.from)this.from+=c,this.to+=c;else if(r<this.to){if(r<this.from||o>this.to||this.to-this.from+h.length>3e4){i=!0;return}this.editContext.updateText(this.toContextPos(r),this.toContextPos(o),h.toString()),this.to+=c;}e+=c;}),n&&!i&&this.revertPending(t.state),!i}update(t){let e=this.pendingContextChange,i=t.startState.selection.main;this.composing&&(this.composing.drifted||!t.changes.touchesRange(i.from,i.to)&&t.transactions.some(n=>!n.isUserEvent("input.type")&&n.changes.touchesRange(this.from,this.to)))?(this.composing.drifted=!0,this.composing.editorBase=t.changes.mapPos(this.composing.editorBase)):!this.applyEdits(t)||!this.rangeIsValid(t.state)?(this.pendingContextChange=null,this.reset(t.state)):(t.docChanged||t.selectionSet||e)&&this.setSelection(t.state),(t.geometryChanged||t.docChanged||t.selectionSet)&&t.view.requestMeasure(this.measureReq);}resetRange(t){let{head:e}=t.selection.main;this.from=Math.max(0,e-1e4),this.to=Math.min(t.doc.length,e+1e4);}reset(t){this.resetRange(t),this.editContext.updateText(0,this.editContext.text.length,t.doc.sliceString(this.from,this.to)),this.setSelection(t);}revertPending(t){let e=this.pendingContextChange;this.pendingContextChange=null,this.editContext.updateText(this.toContextPos(e.from),this.toContextPos(e.from+e.insert.length),t.doc.sliceString(e.from,e.to));}setSelection(t){let{main:e}=t.selection,i=this.toContextPos(Math.max(this.from,Math.min(this.to,e.anchor))),n=this.toContextPos(e.head);(this.editContext.selectionStart!=i||this.editContext.selectionEnd!=n)&&this.editContext.updateSelection(i,n);}rangeIsValid(t){let{head:e}=t.selection.main;return !(this.from>0&&e-this.from<500||this.to<t.doc.length&&this.to-e<500||this.to-this.from>1e4*3)}toEditorPos(t,e=this.to-this.from){t=Math.min(t,e);let i=this.composing;return i&&i.drifted?i.editorBase+(t-i.contextBase):t+this.from}toContextPos(t){let e=this.composing;return e&&e.drifted?e.contextBase+(t-e.editorBase):t-this.from}destroy(){for(let t in this.handlers)this.editContext.removeEventListener(t,this.handlers[t]);}}class O{get state(){return this.viewState.state}get viewport(){return this.viewState.viewport}get visibleRanges(){return this.viewState.visibleRanges}get inView(){return this.viewState.inView}get composing(){return this.inputState.composing>0}get compositionStarted(){return this.inputState.composing>=0}get root(){return this._root}get win(){return this.dom.ownerDocument.defaultView||window}constructor(t={}){var e;this.plugins=[],this.pluginMap=new Map,this.editorAttrs={},this.contentAttrs={},this.bidiCache=[],this.destroyed=!1,this.updateState=2,this.measureScheduled=-1,this.measureRequests=[],this.contentDOM=document.createElement("div"),this.scrollDOM=document.createElement("div"),this.scrollDOM.tabIndex=-1,this.scrollDOM.className="cm-scroller",this.scrollDOM.appendChild(this.contentDOM),this.announceDOM=document.createElement("div"),this.announceDOM.className="cm-announced",this.announceDOM.setAttribute("aria-live","polite"),this.dom=document.createElement("div"),this.dom.appendChild(this.announceDOM),this.dom.appendChild(this.scrollDOM),t.parent&&t.parent.appendChild(this.dom);let{dispatch:i}=t;this.dispatchTransactions=t.dispatchTransactions||i&&(n=>n.forEach(r=>i(r,this)))||(n=>this.update(n)),this.dispatch=this.dispatch.bind(this),this._root=t.root||hu(t.parent)||document,this.viewState=new sl(t.state||W.create(t)),t.scrollTo&&t.scrollTo.is(on)&&(this.viewState.scrollTarget=t.scrollTo.value.clip(this.viewState.state)),this.plugins=this.state.facet(xi).map(n=>new ds(n));for(let n of this.plugins)n.update(this);this.observer=new Ad(this),this.inputState=new Yu(this),this.inputState.ensureHandlers(this.plugins),this.docView=new Vo(this),this.mountStyles(),this.updateAttrs(),this.updateState=0,this.requestMeasure(),!((e=document.fonts)===null||e===void 0)&&e.ready&&document.fonts.ready.then(()=>this.requestMeasure());}dispatch(...t){let e=t.length==1&&t[0]instanceof tt?t:t.length==1&&Array.isArray(t[0])?t[0]:[this.state.update(...t)];this.dispatchTransactions(e,this);}update(t){if(this.updateState!=0)throw new Error("Calls to EditorView.update are not allowed while an update is in progress");let e=!1,i=!1,n,r=this.state;for(let u of t){if(u.startState!=r)throw new RangeError("Trying to update state with a transaction that doesn't start from the previous state.");r=u.state;}if(this.destroyed){this.viewState.state=r;return}let o=this.hasFocus,l=0,a=null;t.some(u=>u.annotation(Eh))?(this.inputState.notifiedFocused=o,l=1):o!=this.inputState.notifiedFocused&&(this.inputState.notifiedFocused=o,a=Ph(r,o),a||(l=1));let h=this.observer.delayedAndroidKey,c=null;if(h?(this.observer.clearDelayedAndroidKey(),c=this.observer.readChange(),(c&&!this.state.doc.eq(r.doc)||!this.state.selection.eq(r.selection))&&(c=null)):this.observer.clear(),r.facet(W.phrases)!=this.state.facet(W.phrases))return this.setState(r);n=Vn.create(this,r,t),n.flags|=l;let f=this.viewState.scrollTarget;try{this.updateState=2;for(let u of t){if(f&&(f=f.map(u.changes)),u.scrollIntoView){let{main:d}=u.state.selection;f=new Ze(d.empty?d:v.cursor(d.head,d.head>d.anchor?-1:1));}for(let d of u.effects)d.is(on)&&(f=d.value.clip(this.state));}this.viewState.update(n,f),this.bidiCache=_n.update(this.bidiCache,n.changes),n.empty||(this.updatePlugins(n),this.inputState.update(n)),e=this.docView.update(n),this.state.facet(vi)!=this.styleModules&&this.mountStyles(),i=this.updateAttrs(),this.showAnnouncements(t),this.docView.updateSelection(e,t.some(u=>u.isUserEvent("select.pointer")));}finally{this.updateState=0;}if(n.startState.facet(un)!=n.state.facet(un)&&(this.viewState.mustMeasureContent=!0),(e||i||f||this.viewState.mustEnforceCursorAssoc||this.viewState.mustMeasureContent)&&this.requestMeasure(),e&&this.docViewUpdate(),!n.empty)for(let u of this.state.facet(cr))try{u(n);}catch(d){St(this.state,d,"update listener");}(a||c)&&Promise.resolve().then(()=>{a&&this.state==a.startState&&this.dispatch(a),c&&!Ah(this,c)&&h.force&&Qe(this.contentDOM,h.key,h.keyCode);});}setState(t){if(this.updateState!=0)throw new Error("Calls to EditorView.setState are not allowed while an update is in progress");if(this.destroyed){this.viewState.state=t;return}this.updateState=2;let e=this.hasFocus;try{for(let i of this.plugins)i.destroy(this);this.viewState=new sl(t),this.plugins=t.facet(xi).map(i=>new ds(i)),this.pluginMap.clear();for(let i of this.plugins)i.update(this);this.docView.destroy(),this.docView=new Vo(this),this.inputState.ensureHandlers(this.plugins),this.mountStyles(),this.updateAttrs(),this.bidiCache=[];}finally{this.updateState=0;}e&&this.focus(),this.requestMeasure();}updatePlugins(t){let e=t.startState.facet(xi),i=t.state.facet(xi);if(e!=i){let n=[];for(let r of i){let o=e.indexOf(r);if(o<0)n.push(new ds(r));else {let l=this.plugins[o];l.mustUpdate=t,n.push(l);}}for(let r of this.plugins)r.mustUpdate!=t&&r.destroy(this);this.plugins=n,this.pluginMap.clear();}else for(let n of this.plugins)n.mustUpdate=t;for(let n=0;n<this.plugins.length;n++)this.plugins[n].update(this);e!=i&&this.inputState.ensureHandlers(this.plugins);}docViewUpdate(){for(let t of this.plugins){let e=t.value;if(e&&e.docViewUpdate)try{e.docViewUpdate(this);}catch(i){St(this.state,i,"doc view update listener");}}}measure(t=!0){if(this.destroyed)return;if(this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.observer.delayedAndroidKey){this.measureScheduled=-1,this.requestMeasure();return}this.measureScheduled=0,t&&this.observer.forceFlush();let e=null,i=this.scrollDOM,n=i.scrollTop*this.scaleY,{scrollAnchorPos:r,scrollAnchorHeight:o}=this.viewState;Math.abs(n-this.viewState.scrollTop)>1&&(o=-1),this.viewState.scrollAnchorHeight=-1;try{for(let l=0;;l++){if(o<0)if(Ua(i))r=-1,o=this.viewState.heightMap.height;else {let d=this.viewState.scrollAnchorAt(n);r=d.from,o=d.top;}this.updateState=1;let a=this.viewState.measure(this);if(!a&&!this.measureRequests.length&&this.viewState.scrollTarget==null)break;if(l>5){console.warn(this.measureRequests.length?"Measure loop restarted more than 5 times":"Viewport failed to stabilize");break}let h=[];a&4||([this.measureRequests,h]=[h,this.measureRequests]);let c=h.map(d=>{try{return d.read(this)}catch(p){return St(this.state,p),al}}),f=Vn.create(this,this.state,[]),u=!1;f.flags|=a,e?e.flags|=a:e=f,this.updateState=2,f.empty||(this.updatePlugins(f),this.inputState.update(f),this.updateAttrs(),u=this.docView.update(f),u&&this.docViewUpdate());for(let d=0;d<h.length;d++)if(c[d]!=al)try{let p=h[d];p.write&&p.write(c[d],this);}catch(p){St(this.state,p);}if(u&&this.docView.updateSelection(!0),!f.viewportChanged&&this.measureRequests.length==0){if(this.viewState.editorHeight)if(this.viewState.scrollTarget){this.docView.scrollIntoView(this.viewState.scrollTarget),this.viewState.scrollTarget=null,o=-1;continue}else {let p=(r<0?this.viewState.heightMap.height:this.viewState.lineBlockAt(r).top)-o;if(p>1||p<-1){n=n+p,i.scrollTop=n/this.scaleY,o=-1;continue}}break}}}finally{this.updateState=0,this.measureScheduled=-1;}if(e&&!e.empty)for(let l of this.state.facet(cr))l(e);}get themeClasses(){return mr+" "+(this.state.facet(pr)?Nh:Ih)+" "+this.state.facet(un)}updateAttrs(){let t=hl(this,wh,{class:"cm-editor"+(this.hasFocus?" cm-focused ":" ")+this.themeClasses}),e={spellcheck:"false",autocorrect:"off",autocapitalize:"off",writingsuggestions:"false",translate:"no",contenteditable:this.state.facet(he)?"true":"false",class:"cm-content",style:`${D.tabSize}: ${this.state.tabSize}`,role:"textbox","aria-multiline":"true"};this.state.readOnly&&(e["aria-readonly"]="true"),hl(this,zr,e);let i=this.observer.ignore(()=>{let n=rr(this.contentDOM,this.contentAttrs,e),r=rr(this.dom,this.editorAttrs,t);return n||r});return this.editorAttrs=t,this.contentAttrs=e,i}showAnnouncements(t){let e=!0;for(let i of t)for(let n of i.effects)if(n.is(O.announce)){e&&(this.announceDOM.textContent=""),e=!1;let r=this.announceDOM.appendChild(document.createElement("div"));r.textContent=n.value;}}mountStyles(){this.styleModules=this.state.facet(vi);let t=this.state.facet(O.cspNonce);ve.mount(this.root,this.styleModules.concat(Sd).reverse(),t?{nonce:t}:void 0);}readMeasured(){if(this.updateState==2)throw new Error("Reading the editor layout isn't allowed during an update");this.updateState==0&&this.measureScheduled>-1&&this.measure(!1);}requestMeasure(t){if(this.measureScheduled<0&&(this.measureScheduled=this.win.requestAnimationFrame(()=>this.measure())),t){if(this.measureRequests.indexOf(t)>-1)return;if(t.key!=null){for(let e=0;e<this.measureRequests.length;e++)if(this.measureRequests[e].key===t.key){this.measureRequests[e]=t;return}}this.measureRequests.push(t);}}plugin(t){let e=this.pluginMap.get(t);return (e===void 0||e&&e.spec!=t)&&this.pluginMap.set(t,e=this.plugins.find(i=>i.spec==t)||null),e&&e.update(this).value}get documentTop(){return this.contentDOM.getBoundingClientRect().top+this.viewState.paddingTop}get documentPadding(){return {top:this.viewState.paddingTop,bottom:this.viewState.paddingBottom}}get scaleX(){return this.viewState.scaleX}get scaleY(){return this.viewState.scaleY}elementAtHeight(t){return this.readMeasured(),this.viewState.elementAtHeight(t)}lineBlockAtHeight(t){return this.readMeasured(),this.viewState.lineBlockAtHeight(t)}get viewportLineBlocks(){return this.viewState.viewportLines}lineBlockAt(t){return this.viewState.lineBlockAt(t)}get contentHeight(){return this.viewState.contentHeight}moveByChar(t,e,i){return ms(this,t,jo(this,t,e,i))}moveByGroup(t,e){return ms(this,t,jo(this,t,e,i=>Vu(this,t.head,i)))}visualLineSide(t,e){let i=this.bidiSpans(t),n=this.textDirectionAt(t.from),r=i[e?i.length-1:0];return v.cursor(r.side(e,n)+t.from,r.forward(!e,n)?1:-1)}moveToLineBoundary(t,e,i=!0){return Hu(this,t,e,i)}moveVertically(t,e,i){return ms(this,t,Wu(this,t,e,i))}domAtPos(t){return this.docView.domAtPos(t)}posAtDOM(t,e=0){return this.docView.posFromDOM(t,e)}posAtCoords(t,e=!0){return this.readMeasured(),Ch(this,t,e)}coordsAtPos(t,e=1){this.readMeasured();let i=this.docView.coordsAt(t,e);if(!i||i.left==i.right)return i;let n=this.state.doc.lineAt(t),r=this.bidiSpans(n),o=r[we.find(r,t-n.from,-1,e)];return Ui(i,o.dir==J.LTR==e>0)}coordsForChar(t){return this.readMeasured(),this.docView.coordsForChar(t)}get defaultCharacterWidth(){return this.viewState.heightOracle.charWidth}get defaultLineHeight(){return this.viewState.heightOracle.lineHeight}get textDirection(){return this.viewState.defaultTextDirection}textDirectionAt(t){return !this.state.facet(mh)||t<this.viewport.from||t>this.viewport.to?this.textDirection:(this.readMeasured(),this.docView.textDirectionAt(t))}get lineWrapping(){return this.viewState.heightOracle.lineWrapping}bidiSpans(t){if(t.length>Td)return lh(t.length);let e=this.textDirectionAt(t.from),i;for(let r of this.bidiCache)if(r.from==t.from&&r.dir==e&&(r.fresh||oh(r.isolates,i=Ho(this,t))))return r.order;i||(i=Ho(this,t));let n=ku(t.text,e,i);return this.bidiCache.push(new _n(t.from,t.to,e,i,!0,n)),n}get hasFocus(){var t;return (this.dom.ownerDocument.hasFocus()||D.safari&&((t=this.inputState)===null||t===void 0?void 0:t.lastContextMenu)>Date.now()-3e4)&&this.root.activeElement==this.contentDOM}focus(){this.observer.ignore(()=>{ja(this.contentDOM),this.docView.updateSelection();});}setRoot(t){this._root!=t&&(this._root=t,this.observer.setWindow((t.nodeType==9?t:t.ownerDocument).defaultView||window),this.mountStyles());}destroy(){this.root.activeElement==this.contentDOM&&this.contentDOM.blur();for(let t of this.plugins)t.destroy(this);this.plugins=[],this.inputState.destroy(),this.docView.destroy(),this.dom.remove(),this.observer.destroy(),this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.destroyed=!0;}static scrollIntoView(t,e={}){return on.of(new Ze(typeof t=="number"?v.cursor(t):t,e.y,e.x,e.yMargin,e.xMargin))}scrollSnapshot(){let{scrollTop:t,scrollLeft:e}=this.scrollDOM,i=this.viewState.scrollAnchorAt(t);return on.of(new Ze(v.cursor(i.from),"start","start",i.top-t,e,!0))}setTabFocusMode(t){t==null?this.inputState.tabFocusMode=this.inputState.tabFocusMode<0?0:-1:typeof t=="boolean"?this.inputState.tabFocusMode=t?0:-1:this.inputState.tabFocusMode!=0&&(this.inputState.tabFocusMode=Date.now()+t);}static domEventHandlers(t){return ht.define(()=>({}),{eventHandlers:t})}static domEventObservers(t){return ht.define(()=>({}),{eventObservers:t})}static theme(t,e){let i=ve.newName(),n=[un.of(i),vi.of(gr(`.${i}`,t))];return e&&e.dark&&n.push(pr.of(!0)),n}static baseTheme(t){return qe.lowest(vi.of(gr("."+mr,t,Fh)))}static findFromDOM(t){var e;let i=t.querySelector(".cm-content"),n=i&&U.get(i)||U.get(t);return ((e=n?.rootView)===null||e===void 0?void 0:e.view)||null}}O.styleModule=vi;O.inputHandler=dh;O.clipboardInputFilter=Wr;O.clipboardOutputFilter=_r;O.scrollHandler=yh;O.focusChangeEffect=ph;O.perLineTextDirection=mh;O.exceptionSink=uh;O.updateListener=cr;O.editable=he;O.mouseSelectionStyle=fh;O.dragMovesSelection=ch;O.clickAddsSelectionRange=hh;O.decorations=Ri;O.outerDecorations=xh;O.atomicRanges=qr;O.bidiIsolatedRanges=vh;O.scrollMargins=kh;O.darkTheme=pr;O.cspNonce=T.define({combine:s=>s.length?s[0]:""});O.contentAttributes=zr;O.editorAttributes=wh;O.lineWrapping=O.contentAttributes.of({class:"cm-lineWrapping"});O.announce=R.define();const Td=4096,al={};class _n{constructor(t,e,i,n,r,o){this.from=t,this.to=e,this.dir=i,this.isolates=n,this.fresh=r,this.order=o;}static update(t,e){if(e.empty&&!t.some(r=>r.fresh))return t;let i=[],n=t.length?t[t.length-1].dir:J.LTR;for(let r=Math.max(0,t.length-10);r<t.length;r++){let o=t[r];o.dir==n&&!e.touchesRange(o.from,o.to)&&i.push(new _n(e.mapPos(o.from,1),e.mapPos(o.to,-1),o.dir,o.isolates,!1,o.order));}return i}}function hl(s,t,e){for(let i=s.state.facet(t),n=i.length-1;n>=0;n--){let r=i[n],o=typeof r=="function"?r(s):r;o&&sr(o,e);}return e}const Od=D.mac?"mac":D.windows?"win":D.linux?"linux":"key";function Bd(s,t){const e=s.split(/-(?!$)/);let i=e[e.length-1];i=="Space"&&(i=" ");let n,r,o,l;for(let a=0;a<e.length-1;++a){const h=e[a];if(/^(cmd|meta|m)$/i.test(h))l=!0;else if(/^a(lt)?$/i.test(h))n=!0;else if(/^(c|ctrl|control)$/i.test(h))r=!0;else if(/^s(hift)?$/i.test(h))o=!0;else if(/^mod$/i.test(h))t=="mac"?l=!0:r=!0;else throw new Error("Unrecognized modifier name: "+h)}return n&&(i="Alt-"+i),r&&(i="Ctrl-"+i),l&&(i="Meta-"+i),o&&(i="Shift-"+i),i}function dn(s,t,e){return t.altKey&&(s="Alt-"+s),t.ctrlKey&&(s="Ctrl-"+s),t.metaKey&&(s="Meta-"+s),e!==!1&&t.shiftKey&&(s="Shift-"+s),s}const Ed=qe.default(O.domEventHandlers({keydown(s,t){return Id(Pd(t.state),s,t,"editor")}})),Ji=T.define({enables:Ed}),cl=new WeakMap;function Pd(s){let t=s.facet(Ji),e=cl.get(t);return e||cl.set(t,e=Rd(t.reduce((i,n)=>i.concat(n),[]))),e}let ge=null;const Ld=4e3;function Rd(s,t=Od){let e=Object.create(null),i=Object.create(null),n=(o,l)=>{let a=i[o];if(a==null)i[o]=l;else if(a!=l)throw new Error("Key binding "+o+" is used both as a regular binding and as a multi-stroke prefix")},r=(o,l,a,h,c)=>{var f,u;let d=e[o]||(e[o]=Object.create(null)),p=l.split(/ (?!$)/).map(b=>Bd(b,t));for(let b=1;b<p.length;b++){let x=p.slice(0,b).join(" ");n(x,!0),d[x]||(d[x]={preventDefault:!0,stopPropagation:!1,run:[k=>{let S=ge={view:k,prefix:x,scope:o};return setTimeout(()=>{ge==S&&(ge=null);},Ld),!0}]});}let y=p.join(" ");n(y,!1);let g=d[y]||(d[y]={preventDefault:!1,stopPropagation:!1,run:((u=(f=d._any)===null||f===void 0?void 0:f.run)===null||u===void 0?void 0:u.slice())||[]});a&&g.run.push(a),h&&(g.preventDefault=!0),c&&(g.stopPropagation=!0);};for(let o of s){let l=o.scope?o.scope.split(" "):["editor"];if(o.any)for(let h of l){let c=e[h]||(e[h]=Object.create(null));c._any||(c._any={preventDefault:!1,stopPropagation:!1,run:[]});let{any:f}=o;for(let u in c)c[u].run.push(d=>f(d,yr));}let a=o[t]||o.key;if(a)for(let h of l)r(h,a,o.run,o.preventDefault,o.stopPropagation),o.shift&&r(h,"Shift-"+a,o.shift,o.preventDefault,o.stopPropagation);}return e}let yr=null;function Id(s,t,e,i){yr=t;let n=su(t),r=Tt(n,0),o=ae(r)==n.length&&n!=" ",l="",a=!1,h=!1,c=!1;ge&&ge.view==e&&ge.scope==i&&(l=ge.prefix+" ",Dh.indexOf(t.keyCode)<0&&(h=!0,ge=null));let f=new Set,u=g=>{if(g){for(let b of g.run)if(!f.has(b)&&(f.add(b),b(e)))return g.stopPropagation&&(c=!0),!0;g.preventDefault&&(g.stopPropagation&&(c=!0),h=!0);}return !1},d=s[i],p,y;return d&&(u(d[l+dn(n,t,!o)])?a=!0:o&&(t.altKey||t.metaKey||t.ctrlKey)&&!(D.windows&&t.ctrlKey&&t.altKey)&&(p=ke[t.keyCode])&&p!=n?(u(d[l+dn(p,t,!0)])||t.shiftKey&&(y=Pi[t.keyCode])!=n&&y!=p&&u(d[l+dn(y,t,!1)]))&&(a=!0):o&&t.shiftKey&&u(d[l+dn(n,t,!0)])&&(a=!0),!a&&u(d._any)&&(a=!0)),h&&(a=!0),a&&c&&t.stopPropagation(),yr=null,a}class Xi{constructor(t,e,i,n,r){this.className=t,this.left=e,this.top=i,this.width=n,this.height=r;}draw(){let t=document.createElement("div");return t.className=this.className,this.adjust(t),t}update(t,e){return e.className!=this.className?!1:(this.adjust(t),!0)}adjust(t){t.style.left=this.left+"px",t.style.top=this.top+"px",this.width!=null&&(t.style.width=this.width+"px"),t.style.height=this.height+"px";}eq(t){return this.left==t.left&&this.top==t.top&&this.width==t.width&&this.height==t.height&&this.className==t.className}static forRange(t,e,i){if(i.empty){let n=t.coordsAtPos(i.head,i.assoc||1);if(!n)return [];let r=Hh(t);return [new Xi(e,n.left-r.left,n.top-r.top,null,n.bottom-n.top)]}else return Nd(t,e,i)}}function Hh(s){let t=s.scrollDOM.getBoundingClientRect();return {left:(s.textDirection==J.LTR?t.left:t.right-s.scrollDOM.clientWidth*s.scaleX)-s.scrollDOM.scrollLeft*s.scaleX,top:t.top-s.scrollDOM.scrollTop*s.scaleY}}function fl(s,t,e,i){let n=s.coordsAtPos(t,e*2);if(!n)return i;let r=s.dom.getBoundingClientRect(),o=(n.top+n.bottom)/2,l=s.posAtCoords({x:r.left+1,y:o}),a=s.posAtCoords({x:r.right-1,y:o});return l==null||a==null?i:{from:Math.max(i.from,Math.min(l,a)),to:Math.min(i.to,Math.max(l,a))}}function Nd(s,t,e){if(e.to<=s.viewport.from||e.from>=s.viewport.to)return [];let i=Math.max(e.from,s.viewport.from),n=Math.min(e.to,s.viewport.to),r=s.textDirection==J.LTR,o=s.contentDOM,l=o.getBoundingClientRect(),a=Hh(s),h=o.querySelector(".cm-line"),c=h&&window.getComputedStyle(h),f=l.left+(c?parseInt(c.paddingLeft)+Math.min(0,parseInt(c.textIndent)):0),u=l.right-(c?parseInt(c.paddingRight):0),d=ur(s,i),p=ur(s,n),y=d.type==yt.Text?d:null,g=p.type==yt.Text?p:null;if(y&&(s.lineWrapping||d.widgetLineBreaks)&&(y=fl(s,i,1,y)),g&&(s.lineWrapping||p.widgetLineBreaks)&&(g=fl(s,n,-1,g)),y&&g&&y.from==g.from&&y.to==g.to)return x(k(e.from,e.to,y));{let w=y?k(e.from,null,y):S(d,!1),C=g?k(null,e.to,g):S(p,!0),A=[];return (y||d).to<(g||p).from-(y&&g?1:0)||d.widgetLineBreaks>1&&w.bottom+s.defaultLineHeight/2<C.top?A.push(b(f,w.bottom,u,C.top)):w.bottom<C.top&&s.elementAtHeight((w.bottom+C.top)/2).type==yt.Text&&(w.bottom=C.top=(w.bottom+C.top)/2),x(w).concat(A).concat(x(C))}function b(w,C,A,E){return new Xi(t,w-a.left,C-a.top,A-w,E-C)}function x({top:w,bottom:C,horizontal:A}){let E=[];for(let L=0;L<A.length;L+=2)E.push(b(A[L],w,A[L+1],C));return E}function k(w,C,A){let E=1e9,L=-1e9,q=[];function P(V,j,st,mt,Bt){let et=s.coordsAtPos(V,V==A.to?-2:2),xt=s.coordsAtPos(st,st==A.from?2:-2);!et||!xt||(E=Math.min(et.top,xt.top,E),L=Math.max(et.bottom,xt.bottom,L),Bt==J.LTR?q.push(r&&j?f:et.left,r&&mt?u:xt.right):q.push(!r&&mt?f:xt.left,!r&&j?u:et.right));}let B=w??A.from,_=C??A.to;for(let V of s.visibleRanges)if(V.to>B&&V.from<_)for(let j=Math.max(V.from,B),st=Math.min(V.to,_);;){let mt=s.state.doc.lineAt(j);for(let Bt of s.bidiSpans(mt)){let et=Bt.from+mt.from,xt=Bt.to+mt.from;if(et>=st)break;xt>j&&P(Math.max(et,j),w==null&&et<=B,Math.min(xt,st),C==null&&xt>=_,Bt.dir);}if(j=mt.to+1,j>=st)break}return q.length==0&&P(B,w==null,_,C==null,s.textDirection),{top:E,bottom:L,horizontal:q}}function S(w,C){let A=l.top+(C?w.top:w.bottom);return {top:A,bottom:A,horizontal:[]}}}function Fd(s,t){return s.constructor==t.constructor&&s.eq(t)}class Hd{constructor(t,e){this.view=t,this.layer=e,this.drawn=[],this.scaleX=1,this.scaleY=1,this.measureReq={read:this.measure.bind(this),write:this.draw.bind(this)},this.dom=t.scrollDOM.appendChild(document.createElement("div")),this.dom.classList.add("cm-layer"),e.above&&this.dom.classList.add("cm-layer-above"),e.class&&this.dom.classList.add(e.class),this.scale(),this.dom.setAttribute("aria-hidden","true"),this.setOrder(t.state),t.requestMeasure(this.measureReq),e.mount&&e.mount(this.dom,t);}update(t){t.startState.facet(Pn)!=t.state.facet(Pn)&&this.setOrder(t.state),(this.layer.update(t,this.dom)||t.geometryChanged)&&(this.scale(),t.view.requestMeasure(this.measureReq));}docViewUpdate(t){this.layer.updateOnDocViewUpdate!==!1&&t.requestMeasure(this.measureReq);}setOrder(t){let e=0,i=t.facet(Pn);for(;e<i.length&&i[e]!=this.layer;)e++;this.dom.style.zIndex=String((this.layer.above?150:-1)-e);}measure(){return this.layer.markers(this.view)}scale(){let{scaleX:t,scaleY:e}=this.view;(t!=this.scaleX||e!=this.scaleY)&&(this.scaleX=t,this.scaleY=e,this.dom.style.transform=`scale(${1/t}, ${1/e})`);}draw(t){if(t.length!=this.drawn.length||t.some((e,i)=>!Fd(e,this.drawn[i]))){let e=this.dom.firstChild,i=0;for(let n of t)n.update&&e&&n.constructor&&this.drawn[i].constructor&&n.update(e,this.drawn[i])?(e=e.nextSibling,i++):this.dom.insertBefore(n.draw(),e);for(;e;){let n=e.nextSibling;e.remove(),e=n;}this.drawn=t;}}destroy(){this.layer.destroy&&this.layer.destroy(this.dom,this.view),this.dom.remove();}}const Pn=T.define();function Vh(s){return [ht.define(t=>new Hd(t,s)),Pn.of(s)]}const Wh=!(D.ios&&D.webkit&&D.webkit_version<534),Ii=T.define({combine(s){return je(s,{cursorBlinkRate:1200,drawRangeCursor:!0},{cursorBlinkRate:(t,e)=>Math.min(t,e),drawRangeCursor:(t,e)=>t||e})}});function Vd(s={}){return [Ii.of(s),Wd,_d,zd,gh.of(!0)]}function _h(s){return s.startState.facet(Ii)!=s.state.facet(Ii)}const Wd=Vh({above:!0,markers(s){let{state:t}=s,e=t.facet(Ii),i=[];for(let n of t.selection.ranges){let r=n==t.selection.main;if(n.empty?!r||Wh:e.drawRangeCursor){let o=r?"cm-cursor cm-cursor-primary":"cm-cursor cm-cursor-secondary",l=n.empty?n:v.cursor(n.head,n.head>n.anchor?-1:1);for(let a of Xi.forRange(s,o,l))i.push(a);}}return i},update(s,t){s.transactions.some(i=>i.selection)&&(t.style.animationName=t.style.animationName=="cm-blink"?"cm-blink2":"cm-blink");let e=_h(s);return e&&ul(s.state,t),s.docChanged||s.selectionSet||e},mount(s,t){ul(t.state,s);},class:"cm-cursorLayer"});function ul(s,t){t.style.animationDuration=s.facet(Ii).cursorBlinkRate+"ms";}const _d=Vh({above:!1,markers(s){return s.state.selection.ranges.map(t=>t.empty?[]:Xi.forRange(s,"cm-selectionBackground",t)).reduce((t,e)=>t.concat(e))},update(s,t){return s.docChanged||s.selectionSet||s.viewportChanged||_h(s)},class:"cm-selectionLayer"}),br={".cm-line":{"& ::selection, &::selection":{backgroundColor:"transparent !important"}},".cm-content":{"& :focus":{caretColor:"initial !important","&::selection, & ::selection":{backgroundColor:"Highlight !important"}}}};Wh&&(br[".cm-line"].caretColor=br[".cm-content"].caretColor="transparent !important");const zd=qe.highest(O.theme(br));function dl(s,t,e,i,n){t.lastIndex=0;for(let r=s.iterRange(e,i),o=e,l;!r.next().done;o+=r.value.length)if(!r.lineBreak)for(;l=t.exec(r.value);)n(o+l.index,l);}function qd(s,t){let e=s.visibleRanges;if(e.length==1&&e[0].from==s.viewport.from&&e[0].to==s.viewport.to)return e;let i=[];for(let{from:n,to:r}of e)n=Math.max(s.state.doc.lineAt(n).from,n-t),r=Math.min(s.state.doc.lineAt(r).to,r+t),i.length&&i[i.length-1].to>=n?i[i.length-1].to=r:i.push({from:n,to:r});return i}class jd{constructor(t){const{regexp:e,decoration:i,decorate:n,boundary:r,maxLength:o=1e3}=t;if(!e.global)throw new RangeError("The regular expression given to MatchDecorator should have its 'g' flag set");if(this.regexp=e,n)this.addMatch=(l,a,h,c)=>n(c,h,h+l[0].length,l,a);else if(typeof i=="function")this.addMatch=(l,a,h,c)=>{let f=i(l,a,h);f&&c(h,h+l[0].length,f);};else if(i)this.addMatch=(l,a,h,c)=>c(h,h+l[0].length,i);else throw new RangeError("Either 'decorate' or 'decoration' should be provided to MatchDecorator");this.boundary=r,this.maxLength=o;}createDeco(t){let e=new xe,i=e.add.bind(e);for(let{from:n,to:r}of qd(t,this.maxLength))dl(t.state.doc,this.regexp,n,r,(o,l)=>this.addMatch(l,t,o,i));return e.finish()}updateDeco(t,e){let i=1e9,n=-1;return t.docChanged&&t.changes.iterChanges((r,o,l,a)=>{a>=t.view.viewport.from&&l<=t.view.viewport.to&&(i=Math.min(l,i),n=Math.max(a,n));}),t.viewportMoved||n-i>1e3?this.createDeco(t.view):n>-1?this.updateRange(t.view,e.map(t.changes),i,n):e}updateRange(t,e,i,n){for(let r of t.visibleRanges){let o=Math.max(r.from,i),l=Math.min(r.to,n);if(l>o){let a=t.state.doc.lineAt(o),h=a.to<l?t.state.doc.lineAt(l):a,c=Math.max(r.from,a.from),f=Math.min(r.to,h.to);if(this.boundary){for(;o>a.from;o--)if(this.boundary.test(a.text[o-1-a.from])){c=o;break}for(;l<h.to;l++)if(this.boundary.test(h.text[l-h.from])){f=l;break}}let u=[],d,p=(y,g,b)=>u.push(b.range(y,g));if(a==h)for(this.regexp.lastIndex=c-a.from;(d=this.regexp.exec(a.text))&&d.index<f-a.from;)this.addMatch(d,t,d.index+a.from,p);else dl(t.state.doc,this.regexp,c,f,(y,g)=>this.addMatch(g,t,y,p));e=e.update({filterFrom:c,filterTo:f,filter:(y,g)=>y<c||g>f,add:u});}}return e}}const wr=/x/.unicode!=null?"gu":"g",Kd=new RegExp(`[\0-\b
--­؜​‎‏\u2028\u2029‭‮⁦⁧⁩\uFEFF￹-￼]`,wr),Ud={0:"null",7:"bell",8:"backspace",10:"newline",11:"vertical tab",13:"carriage return",27:"escape",8203:"zero width space",8204:"zero width non-joiner",8205:"zero width joiner",8206:"left-to-right mark",8207:"right-to-left mark",8232:"line separator",8237:"left-to-right override",8238:"right-to-left override",8294:"left-to-right isolate",8295:"right-to-left isolate",8297:"pop directional isolate",8233:"paragraph separator",65279:"zero width no-break space",65532:"object replacement"};let bs=null;function Gd(){var s;if(bs==null&&typeof document<"u"&&document.body){let t=document.body.style;bs=((s=t.tabSize)!==null&&s!==void 0?s:t.MozTabSize)!=null;}return bs||!1}const Ln=T.define({combine(s){let t=je(s,{render:null,specialChars:Kd,addSpecialChars:null});return (t.replaceTabs=!Gd())&&(t.specialChars=new RegExp("	|"+t.specialChars.source,wr)),t.addSpecialChars&&(t.specialChars=new RegExp(t.specialChars.source+"|"+t.addSpecialChars.source,wr)),t}});function Yd(s={}){return [Ln.of(s),Jd()]}let pl=null;function Jd(){return pl||(pl=ht.fromClass(class{constructor(s){this.view=s,this.decorations=I.none,this.decorationCache=Object.create(null),this.decorator=this.makeDecorator(s.state.facet(Ln)),this.decorations=this.decorator.createDeco(s);}makeDecorator(s){return new jd({regexp:s.specialChars,decoration:(t,e,i)=>{let{doc:n}=e.state,r=Tt(t[0],0);if(r==9){let o=n.lineAt(i),l=e.state.tabSize,a=ai(o.text,l,i-o.from);return I.replace({widget:new $d((l-a%l)*this.view.defaultCharacterWidth/this.view.scaleX)})}return this.decorationCache[r]||(this.decorationCache[r]=I.replace({widget:new Zd(s,r)}))},boundary:s.replaceTabs?void 0:/[^]/})}update(s){let t=s.state.facet(Ln);s.startState.facet(Ln)!=t?(this.decorator=this.makeDecorator(t),this.decorations=this.decorator.createDeco(s.view)):this.decorations=this.decorator.updateDeco(s,this.decorations);}},{decorations:s=>s.decorations}))}const Xd="•";function Qd(s){return s>=32?Xd:s==10?"␤":String.fromCharCode(9216+s)}class Zd extends oe{constructor(t,e){super(),this.options=t,this.code=e;}eq(t){return t.code==this.code}toDOM(t){let e=Qd(this.code),i=t.state.phrase("Control character")+" "+(Ud[this.code]||"0x"+this.code.toString(16)),n=this.options.render&&this.options.render(this.code,i,e);if(n)return n;let r=document.createElement("span");return r.textContent=e,r.title=i,r.setAttribute("aria-label",i),r.className="cm-specialChar",r}ignoreEvent(){return !1}}class $d extends oe{constructor(t){super(),this.width=t;}eq(t){return t.width==this.width}toDOM(){let t=document.createElement("span");return t.textContent="	",t.className="cm-tab",t.style.width=this.width+"px",t}ignoreEvent(){return !1}}const xr=2e3;function ip(s,t,e){let i=Math.min(t.line,e.line),n=Math.max(t.line,e.line),r=[];if(t.off>xr||e.off>xr||t.col<0||e.col<0){let o=Math.min(t.off,e.off),l=Math.max(t.off,e.off);for(let a=i;a<=n;a++){let h=s.doc.line(a);h.length<=l&&r.push(v.range(h.from+o,h.to+l));}}else {let o=Math.min(t.col,e.col),l=Math.max(t.col,e.col);for(let a=i;a<=n;a++){let h=s.doc.line(a),c=Qs(h.text,o,s.tabSize,!0);if(c<0)r.push(v.cursor(h.to));else {let f=Qs(h.text,l,s.tabSize);r.push(v.range(h.from+c,h.from+f));}}}return r}function np(s,t){let e=s.coordsAtPos(s.viewport.from);return e?Math.round(Math.abs((e.left-t)/s.defaultCharacterWidth)):-1}function ml(s,t){let e=s.posAtCoords({x:t.clientX,y:t.clientY},!1),i=s.state.doc.lineAt(e),n=e-i.from,r=n>xr?-1:n==i.length?np(s,t.clientX):ai(i.text,s.state.tabSize,e-i.from);return {line:i.number,col:r,off:n}}function sp(s,t){let e=ml(s,t),i=s.state.selection;return e?{update(n){if(n.docChanged){let r=n.changes.mapPos(n.startState.doc.line(e.line).from),o=n.state.doc.lineAt(r);e={line:o.number,col:e.col,off:Math.min(e.off,o.length)},i=i.map(n.changes);}},get(n,r,o){let l=ml(s,n);if(!l)return i;let a=ip(s.state,e,l);return a.length?o?v.create(a.concat(i.ranges)):v.create(a):i}}:null}function rp(s){let t=e=>e.altKey&&e.button==0;return O.mouseSelectionStyle.of((e,i)=>t(i)?sp(e,i):null)}const op={Alt:[18,s=>!!s.altKey],Control:[17,s=>!!s.ctrlKey],Shift:[16,s=>!!s.shiftKey],Meta:[91,s=>!!s.metaKey]},lp={style:"cursor: crosshair"};function ap(s={}){let[t,e]=op[s.key||"Alt"],i=ht.fromClass(class{constructor(n){this.view=n,this.isDown=!1;}set(n){this.isDown!=n&&(this.isDown=n,this.view.update([]));}},{eventObservers:{keydown(n){this.set(n.keyCode==t||e(n));},keyup(n){(n.keyCode==t||!e(n))&&this.set(!1);},mousemove(n){this.set(e(n));}}});return [i,O.contentAttributes.of(n=>{var r;return !((r=n.plugin(i))===null||r===void 0)&&r.isDown?lp:null})]}const pi="-10000px";class zh{constructor(t,e,i,n){this.facet=e,this.createTooltipView=i,this.removeTooltipView=n,this.input=t.state.facet(e),this.tooltips=this.input.filter(o=>o);let r=null;this.tooltipViews=this.tooltips.map(o=>r=i(o,r));}update(t,e){var i;let n=t.state.facet(this.facet),r=n.filter(a=>a);if(n===this.input){for(let a of this.tooltipViews)a.update&&a.update(t);return !1}let o=[],l=e?[]:null;for(let a=0;a<r.length;a++){let h=r[a],c=-1;if(h){for(let f=0;f<this.tooltips.length;f++){let u=this.tooltips[f];u&&u.create==h.create&&(c=f);}if(c<0)o[a]=this.createTooltipView(h,a?o[a-1]:null),l&&(l[a]=!!h.above);else {let f=o[a]=this.tooltipViews[c];l&&(l[a]=e[c]),f.update&&f.update(t);}}}for(let a of this.tooltipViews)o.indexOf(a)<0&&(this.removeTooltipView(a),(i=a.destroy)===null||i===void 0||i.call(a));return e&&(l.forEach((a,h)=>e[h]=a),e.length=l.length),this.input=n,this.tooltips=r,this.tooltipViews=o,!0}}function hp(s){let t=s.dom.ownerDocument.documentElement;return {top:0,left:0,bottom:t.clientHeight,right:t.clientWidth}}const ws=T.define({combine:s=>{var t,e,i;return {position:D.ios?"absolute":((t=s.find(n=>n.position))===null||t===void 0?void 0:t.position)||"fixed",parent:((e=s.find(n=>n.parent))===null||e===void 0?void 0:e.parent)||null,tooltipSpace:((i=s.find(n=>n.tooltipSpace))===null||i===void 0?void 0:i.tooltipSpace)||hp}}}),gl=new WeakMap,Yr=ht.fromClass(class{constructor(s){this.view=s,this.above=[],this.inView=!0,this.madeAbsolute=!1,this.lastTransaction=0,this.measureTimeout=-1;let t=s.state.facet(ws);this.position=t.position,this.parent=t.parent,this.classes=s.themeClasses,this.createContainer(),this.measureReq={read:this.readMeasure.bind(this),write:this.writeMeasure.bind(this),key:this},this.resizeObserver=typeof ResizeObserver=="function"?new ResizeObserver(()=>this.measureSoon()):null,this.manager=new zh(s,Jr,(e,i)=>this.createTooltip(e,i),e=>{this.resizeObserver&&this.resizeObserver.unobserve(e.dom),e.dom.remove();}),this.above=this.manager.tooltips.map(e=>!!e.above),this.intersectionObserver=typeof IntersectionObserver=="function"?new IntersectionObserver(e=>{Date.now()>this.lastTransaction-50&&e.length>0&&e[e.length-1].intersectionRatio<1&&this.measureSoon();},{threshold:[1]}):null,this.observeIntersection(),s.win.addEventListener("resize",this.measureSoon=this.measureSoon.bind(this)),this.maybeMeasure();}createContainer(){this.parent?(this.container=document.createElement("div"),this.container.style.position="relative",this.container.className=this.view.themeClasses,this.parent.appendChild(this.container)):this.container=this.view.dom;}observeIntersection(){if(this.intersectionObserver){this.intersectionObserver.disconnect();for(let s of this.manager.tooltipViews)this.intersectionObserver.observe(s.dom);}}measureSoon(){this.measureTimeout<0&&(this.measureTimeout=setTimeout(()=>{this.measureTimeout=-1,this.maybeMeasure();},50));}update(s){s.transactions.length&&(this.lastTransaction=Date.now());let t=this.manager.update(s,this.above);t&&this.observeIntersection();let e=t||s.geometryChanged,i=s.state.facet(ws);if(i.position!=this.position&&!this.madeAbsolute){this.position=i.position;for(let n of this.manager.tooltipViews)n.dom.style.position=this.position;e=!0;}if(i.parent!=this.parent){this.parent&&this.container.remove(),this.parent=i.parent,this.createContainer();for(let n of this.manager.tooltipViews)this.container.appendChild(n.dom);e=!0;}else this.parent&&this.view.themeClasses!=this.classes&&(this.classes=this.container.className=this.view.themeClasses);e&&this.maybeMeasure();}createTooltip(s,t){let e=s.create(this.view),i=t?t.dom:null;if(e.dom.classList.add("cm-tooltip"),s.arrow&&!e.dom.querySelector(".cm-tooltip > .cm-tooltip-arrow")){let n=document.createElement("div");n.className="cm-tooltip-arrow",e.dom.appendChild(n);}return e.dom.style.position=this.position,e.dom.style.top=pi,e.dom.style.left="0px",this.container.insertBefore(e.dom,i),e.mount&&e.mount(this.view),this.resizeObserver&&this.resizeObserver.observe(e.dom),e}destroy(){var s,t,e;this.view.win.removeEventListener("resize",this.measureSoon);for(let i of this.manager.tooltipViews)i.dom.remove(),(s=i.destroy)===null||s===void 0||s.call(i);this.parent&&this.container.remove(),(t=this.resizeObserver)===null||t===void 0||t.disconnect(),(e=this.intersectionObserver)===null||e===void 0||e.disconnect(),clearTimeout(this.measureTimeout);}readMeasure(){let s=1,t=1,e=!1;if(this.position=="fixed"&&this.manager.tooltipViews.length){let{dom:r}=this.manager.tooltipViews[0];if(D.gecko)e=r.offsetParent!=this.container.ownerDocument.body;else if(r.style.top==pi&&r.style.left=="0px"){let o=r.getBoundingClientRect();e=Math.abs(o.top+1e4)>1||Math.abs(o.left)>1;}}if(e||this.position=="absolute")if(this.parent){let r=this.parent.getBoundingClientRect();r.width&&r.height&&(s=r.width/this.parent.offsetWidth,t=r.height/this.parent.offsetHeight);}else ({scaleX:s,scaleY:t}=this.view.viewState);let i=this.view.scrollDOM.getBoundingClientRect(),n=jr(this.view);return {visible:{left:i.left+n.left,top:i.top+n.top,right:i.right-n.right,bottom:i.bottom-n.bottom},parent:this.parent?this.container.getBoundingClientRect():this.view.dom.getBoundingClientRect(),pos:this.manager.tooltips.map((r,o)=>{let l=this.manager.tooltipViews[o];return l.getCoords?l.getCoords(r.pos):this.view.coordsAtPos(r.pos)}),size:this.manager.tooltipViews.map(({dom:r})=>r.getBoundingClientRect()),space:this.view.state.facet(ws).tooltipSpace(this.view),scaleX:s,scaleY:t,makeAbsolute:e}}writeMeasure(s){var t;if(s.makeAbsolute){this.madeAbsolute=!0,this.position="absolute";for(let l of this.manager.tooltipViews)l.dom.style.position="absolute";}let{visible:e,space:i,scaleX:n,scaleY:r}=s,o=[];for(let l=0;l<this.manager.tooltips.length;l++){let a=this.manager.tooltips[l],h=this.manager.tooltipViews[l],{dom:c}=h,f=s.pos[l],u=s.size[l];if(!f||a.clip!==!1&&(f.bottom<=Math.max(e.top,i.top)||f.top>=Math.min(e.bottom,i.bottom)||f.right<Math.max(e.left,i.left)-.1||f.left>Math.min(e.right,i.right)+.1)){c.style.top=pi;continue}let d=a.arrow?h.dom.querySelector(".cm-tooltip-arrow"):null,p=d?7:0,y=u.right-u.left,g=(t=gl.get(h))!==null&&t!==void 0?t:u.bottom-u.top,b=h.offset||fp,x=this.view.textDirection==J.LTR,k=u.width>i.right-i.left?x?i.left:i.right-u.width:x?Math.max(i.left,Math.min(f.left-(d?14:0)+b.x,i.right-y)):Math.min(Math.max(i.left,f.left-y+(d?14:0)-b.x),i.right-y),S=this.above[l];!a.strictSide&&(S?f.top-g-p-b.y<i.top:f.bottom+g+p+b.y>i.bottom)&&S==i.bottom-f.bottom>f.top-i.top&&(S=this.above[l]=!S);let w=(S?f.top-i.top:i.bottom-f.bottom)-p;if(w<g&&h.resize!==!1){if(w<this.view.defaultLineHeight){c.style.top=pi;continue}gl.set(h,g),c.style.height=(g=w)/r+"px";}else c.style.height&&(c.style.height="");let C=S?f.top-g-p-b.y:f.bottom+p+b.y,A=k+y;if(h.overlap!==!0)for(let E of o)E.left<A&&E.right>k&&E.top<C+g&&E.bottom>C&&(C=S?E.top-g-2-p:E.bottom+p+2);if(this.position=="absolute"?(c.style.top=(C-s.parent.top)/r+"px",yl(c,(k-s.parent.left)/n)):(c.style.top=C/r+"px",yl(c,k/n)),d){let E=f.left+(x?b.x:-b.x)-(k+14-7);d.style.left=E/n+"px";}h.overlap!==!0&&o.push({left:k,top:C,right:A,bottom:C+g}),c.classList.toggle("cm-tooltip-above",S),c.classList.toggle("cm-tooltip-below",!S),h.positioned&&h.positioned(s.space);}}maybeMeasure(){if(this.manager.tooltips.length&&(this.view.inView&&this.view.requestMeasure(this.measureReq),this.inView!=this.view.inView&&(this.inView=this.view.inView,!this.inView)))for(let s of this.manager.tooltipViews)s.dom.style.top=pi;}},{eventObservers:{scroll(){this.maybeMeasure();}}});function yl(s,t){let e=parseInt(s.style.left,10);(isNaN(e)||Math.abs(t-e)>1)&&(s.style.left=t+"px");}const cp=O.baseTheme({".cm-tooltip":{zIndex:500,boxSizing:"border-box"},"&light .cm-tooltip":{border:"1px solid #bbb",backgroundColor:"#f5f5f5"},"&light .cm-tooltip-section:not(:first-child)":{borderTop:"1px solid #bbb"},"&dark .cm-tooltip":{backgroundColor:"#333338",color:"white"},".cm-tooltip-arrow":{height:"7px",width:`${7*2}px`,position:"absolute",zIndex:-1,overflow:"hidden","&:before, &:after":{content:"''",position:"absolute",width:0,height:0,borderLeft:"7px solid transparent",borderRight:"7px solid transparent"},".cm-tooltip-above &":{bottom:"-7px","&:before":{borderTop:"7px solid #bbb"},"&:after":{borderTop:"7px solid #f5f5f5",bottom:"1px"}},".cm-tooltip-below &":{top:"-7px","&:before":{borderBottom:"7px solid #bbb"},"&:after":{borderBottom:"7px solid #f5f5f5",top:"1px"}}},"&dark .cm-tooltip .cm-tooltip-arrow":{"&:before":{borderTopColor:"#333338",borderBottomColor:"#333338"},"&:after":{borderTopColor:"transparent",borderBottomColor:"transparent"}}}),fp={x:0,y:0},Jr=T.define({enables:[Yr,cp]}),zn=T.define({combine:s=>s.reduce((t,e)=>t.concat(e),[])});class ts{static create(t){return new ts(t)}constructor(t){this.view=t,this.mounted=!1,this.dom=document.createElement("div"),this.dom.classList.add("cm-tooltip-hover"),this.manager=new zh(t,zn,(e,i)=>this.createHostedView(e,i),e=>e.dom.remove());}createHostedView(t,e){let i=t.create(this.view);return i.dom.classList.add("cm-tooltip-section"),this.dom.insertBefore(i.dom,e?e.dom.nextSibling:this.dom.firstChild),this.mounted&&i.mount&&i.mount(this.view),i}mount(t){for(let e of this.manager.tooltipViews)e.mount&&e.mount(t);this.mounted=!0;}positioned(t){for(let e of this.manager.tooltipViews)e.positioned&&e.positioned(t);}update(t){this.manager.update(t);}destroy(){var t;for(let e of this.manager.tooltipViews)(t=e.destroy)===null||t===void 0||t.call(e);}passProp(t){let e;for(let i of this.manager.tooltipViews){let n=i[t];if(n!==void 0){if(e===void 0)e=n;else if(e!==n)return}}return e}get offset(){return this.passProp("offset")}get getCoords(){return this.passProp("getCoords")}get overlap(){return this.passProp("overlap")}get resize(){return this.passProp("resize")}}const up=Jr.compute([zn],s=>{let t=s.facet(zn);return t.length===0?null:{pos:Math.min(...t.map(e=>e.pos)),end:Math.max(...t.map(e=>{var i;return (i=e.end)!==null&&i!==void 0?i:e.pos})),create:ts.create,above:t[0].above,arrow:t.some(e=>e.arrow)}});class dp{constructor(t,e,i,n,r){this.view=t,this.source=e,this.field=i,this.setHover=n,this.hoverTime=r,this.hoverTimeout=-1,this.restartTimeout=-1,this.pending=null,this.lastMove={x:0,y:0,target:t.dom,time:0},this.checkHover=this.checkHover.bind(this),t.dom.addEventListener("mouseleave",this.mouseleave=this.mouseleave.bind(this)),t.dom.addEventListener("mousemove",this.mousemove=this.mousemove.bind(this));}update(){this.pending&&(this.pending=null,clearTimeout(this.restartTimeout),this.restartTimeout=setTimeout(()=>this.startHover(),20));}get active(){return this.view.state.field(this.field)}checkHover(){if(this.hoverTimeout=-1,this.active.length)return;let t=Date.now()-this.lastMove.time;t<this.hoverTime?this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime-t):this.startHover();}startHover(){clearTimeout(this.restartTimeout);let{view:t,lastMove:e}=this,i=t.docView.nearest(e.target);if(!i)return;let n,r=1;if(i instanceof be)n=i.posAtStart;else {if(n=t.posAtCoords(e),n==null)return;let l=t.coordsAtPos(n);if(!l||e.y<l.top||e.y>l.bottom||e.x<l.left-t.defaultCharacterWidth||e.x>l.right+t.defaultCharacterWidth)return;let a=t.bidiSpans(t.state.doc.lineAt(n)).find(c=>c.from<=n&&c.to>=n),h=a&&a.dir==J.RTL?-1:1;r=e.x<l.left?-h:h;}let o=this.source(t,n,r);if(o?.then){let l=this.pending={pos:n};o.then(a=>{this.pending==l&&(this.pending=null,a&&!(Array.isArray(a)&&!a.length)&&t.dispatch({effects:this.setHover.of(Array.isArray(a)?a:[a])}));},a=>St(t.state,a,"hover tooltip"));}else o&&!(Array.isArray(o)&&!o.length)&&t.dispatch({effects:this.setHover.of(Array.isArray(o)?o:[o])});}get tooltip(){let t=this.view.plugin(Yr),e=t?t.manager.tooltips.findIndex(i=>i.create==ts.create):-1;return e>-1?t.manager.tooltipViews[e]:null}mousemove(t){var e,i;this.lastMove={x:t.clientX,y:t.clientY,target:t.target,time:Date.now()},this.hoverTimeout<0&&(this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime));let{active:n,tooltip:r}=this;if(n.length&&r&&!pp(r.dom,t)||this.pending){let{pos:o}=n[0]||this.pending,l=(i=(e=n[0])===null||e===void 0?void 0:e.end)!==null&&i!==void 0?i:o;(o==l?this.view.posAtCoords(this.lastMove)!=o:!mp(this.view,o,l,t.clientX,t.clientY))&&(this.view.dispatch({effects:this.setHover.of([])}),this.pending=null);}}mouseleave(t){clearTimeout(this.hoverTimeout),this.hoverTimeout=-1;let{active:e}=this;if(e.length){let{tooltip:i}=this;i&&i.dom.contains(t.relatedTarget)?this.watchTooltipLeave(i.dom):this.view.dispatch({effects:this.setHover.of([])});}}watchTooltipLeave(t){let e=i=>{t.removeEventListener("mouseleave",e),this.active.length&&!this.view.dom.contains(i.relatedTarget)&&this.view.dispatch({effects:this.setHover.of([])});};t.addEventListener("mouseleave",e);}destroy(){clearTimeout(this.hoverTimeout),this.view.dom.removeEventListener("mouseleave",this.mouseleave),this.view.dom.removeEventListener("mousemove",this.mousemove);}}const pn=4;function pp(s,t){let{left:e,right:i,top:n,bottom:r}=s.getBoundingClientRect(),o;if(o=s.querySelector(".cm-tooltip-arrow")){let l=o.getBoundingClientRect();n=Math.min(l.top,n),r=Math.max(l.bottom,r);}return t.clientX>=e-pn&&t.clientX<=i+pn&&t.clientY>=n-pn&&t.clientY<=r+pn}function mp(s,t,e,i,n,r){let o=s.scrollDOM.getBoundingClientRect(),l=s.documentTop+s.documentPadding.top+s.contentHeight;if(o.left>i||o.right<i||o.top>n||Math.min(o.bottom,l)<n)return !1;let a=s.posAtCoords({x:i,y:n},!1);return a>=t&&a<=e}function gp(s,t={}){let e=R.define(),i=At.define({create(){return []},update(n,r){if(n.length&&(t.hideOnChange&&(r.docChanged||r.selection)?n=[]:t.hideOn&&(n=n.filter(o=>!t.hideOn(r,o))),r.docChanged)){let o=[];for(let l of n){let a=r.changes.mapPos(l.pos,-1,at.TrackDel);if(a!=null){let h=Object.assign(Object.create(null),l);h.pos=a,h.end!=null&&(h.end=r.changes.mapPos(h.end)),o.push(h);}}n=o;}for(let o of r.effects)o.is(e)&&(n=o.value),o.is(yp)&&(n=[]);return n},provide:n=>zn.from(n)});return {active:i,extension:[i,ht.define(n=>new dp(n,s,i,e,t.hoverTime||300)),up]}}function qh(s,t){let e=s.plugin(Yr);if(!e)return null;let i=e.manager.tooltips.indexOf(t);return i<0?null:e.manager.tooltipViews[i]}const yp=R.define(),bl=T.define({combine(s){let t,e;for(let i of s)t=t||i.topContainer,e=e||i.bottomContainer;return {topContainer:t,bottomContainer:e}}});function bp(s,t){let e=s.plugin(jh),i=e?e.specs.indexOf(t):-1;return i>-1?e.panels[i]:null}const jh=ht.fromClass(class{constructor(s){this.input=s.state.facet(vr),this.specs=this.input.filter(e=>e),this.panels=this.specs.map(e=>e(s));let t=s.state.facet(bl);this.top=new mn(s,!0,t.topContainer),this.bottom=new mn(s,!1,t.bottomContainer),this.top.sync(this.panels.filter(e=>e.top)),this.bottom.sync(this.panels.filter(e=>!e.top));for(let e of this.panels)e.dom.classList.add("cm-panel"),e.mount&&e.mount();}update(s){let t=s.state.facet(bl);this.top.container!=t.topContainer&&(this.top.sync([]),this.top=new mn(s.view,!0,t.topContainer)),this.bottom.container!=t.bottomContainer&&(this.bottom.sync([]),this.bottom=new mn(s.view,!1,t.bottomContainer)),this.top.syncClasses(),this.bottom.syncClasses();let e=s.state.facet(vr);if(e!=this.input){let i=e.filter(a=>a),n=[],r=[],o=[],l=[];for(let a of i){let h=this.specs.indexOf(a),c;h<0?(c=a(s.view),l.push(c)):(c=this.panels[h],c.update&&c.update(s)),n.push(c),(c.top?r:o).push(c);}this.specs=i,this.panels=n,this.top.sync(r),this.bottom.sync(o);for(let a of l)a.dom.classList.add("cm-panel"),a.mount&&a.mount();}else for(let i of this.panels)i.update&&i.update(s);}destroy(){this.top.sync([]),this.bottom.sync([]);}},{provide:s=>O.scrollMargins.of(t=>{let e=t.plugin(s);return e&&{top:e.top.scrollMargin(),bottom:e.bottom.scrollMargin()}})});class mn{constructor(t,e,i){this.view=t,this.top=e,this.container=i,this.dom=void 0,this.classes="",this.panels=[],this.syncClasses();}sync(t){for(let e of this.panels)e.destroy&&t.indexOf(e)<0&&e.destroy();this.panels=t,this.syncDOM();}syncDOM(){if(this.panels.length==0){this.dom&&(this.dom.remove(),this.dom=void 0);return}if(!this.dom){this.dom=document.createElement("div"),this.dom.className=this.top?"cm-panels cm-panels-top":"cm-panels cm-panels-bottom",this.dom.style[this.top?"top":"bottom"]="0";let e=this.container||this.view.dom;e.insertBefore(this.dom,this.top?e.firstChild:null);}let t=this.dom.firstChild;for(let e of this.panels)if(e.dom.parentNode==this.dom){for(;t!=e.dom;)t=wl(t);t=t.nextSibling;}else this.dom.insertBefore(e.dom,t);for(;t;)t=wl(t);}scrollMargin(){return !this.dom||this.container?0:Math.max(0,this.top?this.dom.getBoundingClientRect().bottom-Math.max(0,this.view.scrollDOM.getBoundingClientRect().top):Math.min(innerHeight,this.view.scrollDOM.getBoundingClientRect().bottom)-this.dom.getBoundingClientRect().top)}syncClasses(){if(!(!this.container||this.classes==this.view.themeClasses)){for(let t of this.classes.split(" "))t&&this.container.classList.remove(t);for(let t of (this.classes=this.view.themeClasses).split(" "))t&&this.container.classList.add(t);}}}function wl(s){let t=s.nextSibling;return s.remove(),t}const vr=T.define({enables:jh});class Ce extends Fe{compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}eq(t){return !1}destroy(t){}}Ce.prototype.elementClass="";Ce.prototype.toDOM=void 0;Ce.prototype.mapMode=at.TrackBefore;Ce.prototype.startSide=Ce.prototype.endSide=-1;Ce.prototype.point=!0;const xs=T.define(),wp=T.define(),xp={class:"",renderEmptyElements:!1,elementStyle:"",markers:()=>z.empty,lineMarker:()=>null,widgetMarker:()=>null,lineMarkerChange:null,initialSpacer:null,updateSpacer:null,domEventHandlers:{}},Ti=T.define();function vp(s){return [Kh(),Ti.of(Object.assign(Object.assign({},xp),s))]}const xl=T.define({combine:s=>s.some(t=>t)});function Kh(s){return [kp]}const kp=ht.fromClass(class{constructor(s){this.view=s,this.prevViewport=s.viewport,this.dom=document.createElement("div"),this.dom.className="cm-gutters",this.dom.setAttribute("aria-hidden","true"),this.dom.style.minHeight=this.view.contentHeight/this.view.scaleY+"px",this.gutters=s.state.facet(Ti).map(t=>new kl(s,t));for(let t of this.gutters)this.dom.appendChild(t.dom);this.fixed=!s.state.facet(xl),this.fixed&&(this.dom.style.position="sticky"),this.syncGutters(!1),s.scrollDOM.insertBefore(this.dom,s.contentDOM);}update(s){if(this.updateGutters(s)){let t=this.prevViewport,e=s.view.viewport,i=Math.min(t.to,e.to)-Math.max(t.from,e.from);this.syncGutters(i<(e.to-e.from)*.8);}s.geometryChanged&&(this.dom.style.minHeight=this.view.contentHeight/this.view.scaleY+"px"),this.view.state.facet(xl)!=!this.fixed&&(this.fixed=!this.fixed,this.dom.style.position=this.fixed?"sticky":""),this.prevViewport=s.view.viewport;}syncGutters(s){let t=this.dom.nextSibling;s&&this.dom.remove();let e=z.iter(this.view.state.facet(xs),this.view.viewport.from),i=[],n=this.gutters.map(r=>new Sp(r,this.view.viewport,-this.view.documentPadding.top));for(let r of this.view.viewportLineBlocks)if(i.length&&(i=[]),Array.isArray(r.type)){let o=!0;for(let l of r.type)if(l.type==yt.Text&&o){kr(e,i,l.from);for(let a of n)a.line(this.view,l,i);o=!1;}else if(l.widget)for(let a of n)a.widget(this.view,l);}else if(r.type==yt.Text){kr(e,i,r.from);for(let o of n)o.line(this.view,r,i);}else if(r.widget)for(let o of n)o.widget(this.view,r);for(let r of n)r.finish();s&&this.view.scrollDOM.insertBefore(this.dom,t);}updateGutters(s){let t=s.startState.facet(Ti),e=s.state.facet(Ti),i=s.docChanged||s.heightChanged||s.viewportChanged||!z.eq(s.startState.facet(xs),s.state.facet(xs),s.view.viewport.from,s.view.viewport.to);if(t==e)for(let n of this.gutters)n.update(s)&&(i=!0);else {i=!0;let n=[];for(let r of e){let o=t.indexOf(r);o<0?n.push(new kl(this.view,r)):(this.gutters[o].update(s),n.push(this.gutters[o]));}for(let r of this.gutters)r.dom.remove(),n.indexOf(r)<0&&r.destroy();for(let r of n)this.dom.appendChild(r.dom);this.gutters=n;}return i}destroy(){for(let s of this.gutters)s.destroy();this.dom.remove();}},{provide:s=>O.scrollMargins.of(t=>{let e=t.plugin(s);return !e||e.gutters.length==0||!e.fixed?null:t.textDirection==J.LTR?{left:e.dom.offsetWidth*t.scaleX}:{right:e.dom.offsetWidth*t.scaleX}})});function vl(s){return Array.isArray(s)?s:[s]}function kr(s,t,e){for(;s.value&&s.from<=e;)s.from==e&&t.push(s.value),s.next();}class Sp{constructor(t,e,i){this.gutter=t,this.height=i,this.i=0,this.cursor=z.iter(t.markers,e.from);}addElement(t,e,i){let{gutter:n}=this,r=(e.top-this.height)/t.scaleY,o=e.height/t.scaleY;if(this.i==n.elements.length){let l=new Uh(t,o,r,i);n.elements.push(l),n.dom.appendChild(l.dom);}else n.elements[this.i].update(t,o,r,i);this.height=e.bottom,this.i++;}line(t,e,i){let n=[];kr(this.cursor,n,e.from),i.length&&(n=n.concat(i));let r=this.gutter.config.lineMarker(t,e,n);r&&n.unshift(r);let o=this.gutter;n.length==0&&!o.config.renderEmptyElements||this.addElement(t,e,n);}widget(t,e){let i=this.gutter.config.widgetMarker(t,e.widget,e),n=i?[i]:null;for(let r of t.state.facet(wp)){let o=r(t,e.widget,e);o&&(n||(n=[])).push(o);}n&&this.addElement(t,e,n);}finish(){let t=this.gutter;for(;t.elements.length>this.i;){let e=t.elements.pop();t.dom.removeChild(e.dom),e.destroy();}}}class kl{constructor(t,e){this.view=t,this.config=e,this.elements=[],this.spacer=null,this.dom=document.createElement("div"),this.dom.className="cm-gutter"+(this.config.class?" "+this.config.class:"");for(let i in e.domEventHandlers)this.dom.addEventListener(i,n=>{let r=n.target,o;if(r!=this.dom&&this.dom.contains(r)){for(;r.parentNode!=this.dom;)r=r.parentNode;let a=r.getBoundingClientRect();o=(a.top+a.bottom)/2;}else o=n.clientY;let l=t.lineBlockAtHeight(o-t.documentTop);e.domEventHandlers[i](t,l,n)&&n.preventDefault();});this.markers=vl(e.markers(t)),e.initialSpacer&&(this.spacer=new Uh(t,0,0,[e.initialSpacer(t)]),this.dom.appendChild(this.spacer.dom),this.spacer.dom.style.cssText+="visibility: hidden; pointer-events: none");}update(t){let e=this.markers;if(this.markers=vl(this.config.markers(t.view)),this.spacer&&this.config.updateSpacer){let n=this.config.updateSpacer(this.spacer.markers[0],t);n!=this.spacer.markers[0]&&this.spacer.update(t.view,0,0,[n]);}let i=t.view.viewport;return !z.eq(this.markers,e,i.from,i.to)||(this.config.lineMarkerChange?this.config.lineMarkerChange(t):!1)}destroy(){for(let t of this.elements)t.destroy();}}class Uh{constructor(t,e,i,n){this.height=-1,this.above=0,this.markers=[],this.dom=document.createElement("div"),this.dom.className="cm-gutterElement",this.update(t,e,i,n);}update(t,e,i,n){this.height!=e&&(this.height=e,this.dom.style.height=e+"px"),this.above!=i&&(this.dom.style.marginTop=(this.above=i)?i+"px":""),Cp(this.markers,n)||this.setMarkers(t,n);}setMarkers(t,e){let i="cm-gutterElement",n=this.dom.firstChild;for(let r=0,o=0;;){let l=o,a=r<e.length?e[r++]:null,h=!1;if(a){let c=a.elementClass;c&&(i+=" "+c);for(let f=o;f<this.markers.length;f++)if(this.markers[f].compare(a)){l=f,h=!0;break}}else l=this.markers.length;for(;o<l;){let c=this.markers[o++];if(c.toDOM){c.destroy(n);let f=n.nextSibling;n.remove(),n=f;}}if(!a)break;a.toDOM&&(h?n=n.nextSibling:this.dom.insertBefore(a.toDOM(t),n)),h&&o++;}this.dom.className=i,this.markers=e;}destroy(){this.setMarkers(null,[]);}}function Cp(s,t){if(s.length!=t.length)return !1;for(let e=0;e<s.length;e++)if(!s[e].compare(t[e]))return !1;return !0}const Ap=T.define(),Mp=T.define(),Ge=T.define({combine(s){return je(s,{formatNumber:String,domEventHandlers:{}},{domEventHandlers(t,e){let i=Object.assign({},t);for(let n in e){let r=i[n],o=e[n];i[n]=r?(l,a,h)=>r(l,a,h)||o(l,a,h):o;}return i}})}});class vs extends Ce{constructor(t){super(),this.number=t;}eq(t){return this.number==t.number}toDOM(){return document.createTextNode(this.number)}}function ks(s,t){return s.state.facet(Ge).formatNumber(t,s.state)}Ti.compute([Ge],s=>({class:"cm-lineNumbers",renderEmptyElements:!1,markers(t){return t.state.facet(Ap)},lineMarker(t,e,i){return i.some(n=>n.toDOM)?null:new vs(ks(t,t.state.doc.lineAt(e.from).number))},widgetMarker:(t,e,i)=>{for(let n of t.state.facet(Mp)){let r=n(t,e,i);if(r)return r}return null},lineMarkerChange:t=>t.startState.facet(Ge)!=t.state.facet(Ge),initialSpacer(t){return new vs(ks(t,Sl(t.state.doc.lines)))},updateSpacer(t,e){let i=ks(e.view,Sl(e.view.state.doc.lines));return i==t.number?t:new vs(i)},domEventHandlers:s.facet(Ge).domEventHandlers}));function Sl(s){let t=9;for(;t<s;)t=t*10+9;return t}const Op=1024;let Bp=0;class Rt{constructor(t,e){this.from=t,this.to=e;}}class N{constructor(t={}){this.id=Bp++,this.perNode=!!t.perNode,this.deserialize=t.deserialize||(()=>{throw new Error("This node type doesn't define a deserialize function")});}add(t){if(this.perNode)throw new RangeError("Can't add per-node props to node types");return typeof t!="function"&&(t=wt.match(t)),e=>{let i=t(e);return i===void 0?null:[this,i]}}}N.closedBy=new N({deserialize:s=>s.split(" ")});N.openedBy=new N({deserialize:s=>s.split(" ")});N.group=new N({deserialize:s=>s.split(" ")});N.isolate=new N({deserialize:s=>{if(s&&s!="rtl"&&s!="ltr"&&s!="auto")throw new RangeError("Invalid value for isolate: "+s);return s||"auto"}});N.contextHash=new N({perNode:!0});N.lookAhead=new N({perNode:!0});N.mounted=new N({perNode:!0});class Ni{constructor(t,e,i){this.tree=t,this.overlay=e,this.parser=i;}static get(t){return t&&t.props&&t.props[N.mounted.id]}}const Ep=Object.create(null);class wt{constructor(t,e,i,n=0){this.name=t,this.props=e,this.id=i,this.flags=n;}static define(t){let e=t.props&&t.props.length?Object.create(null):Ep,i=(t.top?1:0)|(t.skipped?2:0)|(t.error?4:0)|(t.name==null?8:0),n=new wt(t.name||"",e,t.id,i);if(t.props){for(let r of t.props)if(Array.isArray(r)||(r=r(n)),r){if(r[0].perNode)throw new RangeError("Can't store a per-node prop on a node type");e[r[0].id]=r[1];}}return n}prop(t){return this.props[t.id]}get isTop(){return (this.flags&1)>0}get isSkipped(){return (this.flags&2)>0}get isError(){return (this.flags&4)>0}get isAnonymous(){return (this.flags&8)>0}is(t){if(typeof t=="string"){if(this.name==t)return !0;let e=this.prop(N.group);return e?e.indexOf(t)>-1:!1}return this.id==t}static match(t){let e=Object.create(null);for(let i in t)for(let n of i.split(" "))e[n]=t[i];return i=>{for(let n=i.prop(N.group),r=-1;r<(n?n.length:0);r++){let o=e[r<0?i.name:n[r]];if(o)return o}}}}wt.none=new wt("",Object.create(null),0,8);class Xr{constructor(t){this.types=t;for(let e=0;e<t.length;e++)if(t[e].id!=e)throw new RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...t){let e=[];for(let i of this.types){let n=null;for(let r of t){let o=r(i);o&&(n||(n=Object.assign({},i.props)),n[o[0].id]=o[1]);}e.push(n?new wt(i.name,n,i.id,i.flags):i);}return new Xr(e)}}const gn=new WeakMap,Cl=new WeakMap;var X;(function(s){s[s.ExcludeBuffers=1]="ExcludeBuffers",s[s.IncludeAnonymous=2]="IncludeAnonymous",s[s.IgnoreMounts=4]="IgnoreMounts",s[s.IgnoreOverlays=8]="IgnoreOverlays";})(X||(X={}));class G{constructor(t,e,i,n,r){if(this.type=t,this.children=e,this.positions=i,this.length=n,this.props=null,r&&r.length){this.props=Object.create(null);for(let[o,l]of r)this.props[typeof o=="number"?o:o.id]=l;}}toString(){let t=Ni.get(this);if(t&&!t.overlay)return t.tree.toString();let e="";for(let i of this.children){let n=i.toString();n&&(e&&(e+=","),e+=n);}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(e.length?"("+e+")":""):e}cursor(t=0){return new qn(this.topNode,t)}cursorAt(t,e=0,i=0){let n=gn.get(this)||this.topNode,r=new qn(n);return r.moveTo(t,e),gn.set(this,r._tree),r}get topNode(){return new dt(this,0,0,null)}resolve(t,e=0){let i=Fi(gn.get(this)||this.topNode,t,e,!1);return gn.set(this,i),i}resolveInner(t,e=0){let i=Fi(Cl.get(this)||this.topNode,t,e,!0);return Cl.set(this,i),i}resolveStack(t,e=0){return Rp(this,t,e)}iterate(t){let{enter:e,leave:i,from:n=0,to:r=this.length}=t,o=t.mode||0,l=(o&X.IncludeAnonymous)>0;for(let a=this.cursor(o|X.IncludeAnonymous);;){let h=!1;if(a.from<=r&&a.to>=n&&(!l&&a.type.isAnonymous||e(a)!==!1)){if(a.firstChild())continue;h=!0;}for(;h&&i&&(l||!a.type.isAnonymous)&&i(a),!a.nextSibling();){if(!a.parent())return;h=!0;}}}prop(t){return t.perNode?this.props?this.props[t.id]:void 0:this.type.prop(t)}get propValues(){let t=[];if(this.props)for(let e in this.props)t.push([+e,this.props[e]]);return t}balance(t={}){return this.children.length<=8?this:$r(wt.none,this.children,this.positions,0,this.children.length,0,this.length,(e,i,n)=>new G(this.type,e,i,n,this.propValues),t.makeTree||((e,i,n)=>new G(wt.none,e,i,n)))}static build(t){return Ip(t)}}G.empty=new G(wt.none,[],[],0);class Qr{constructor(t,e){this.buffer=t,this.index=e;}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4;}fork(){return new Qr(this.buffer,this.index)}}class Ae{constructor(t,e,i){this.buffer=t,this.length=e,this.set=i;}get type(){return wt.none}toString(){let t=[];for(let e=0;e<this.buffer.length;)t.push(this.childString(e)),e=this.buffer[e+3];return t.join(",")}childString(t){let e=this.buffer[t],i=this.buffer[t+3],n=this.set.types[e],r=n.name;if(/\W/.test(r)&&!n.isError&&(r=JSON.stringify(r)),t+=4,i==t)return r;let o=[];for(;t<i;)o.push(this.childString(t)),t=this.buffer[t+3];return r+"("+o.join(",")+")"}findChild(t,e,i,n,r){let{buffer:o}=this,l=-1;for(let a=t;a!=e&&!(Gh(r,n,o[a+1],o[a+2])&&(l=a,i>0));a=o[a+3]);return l}slice(t,e,i){let n=this.buffer,r=new Uint16Array(e-t),o=0;for(let l=t,a=0;l<e;){r[a++]=n[l++],r[a++]=n[l++]-i;let h=r[a++]=n[l++]-i;r[a++]=n[l++]-t,o=Math.max(o,h);}return new Ae(r,o,this.set)}}function Gh(s,t,e,i){switch(s){case-2:return e<t;case-1:return i>=t&&e<t;case 0:return e<t&&i>t;case 1:return e<=t&&i>t;case 2:return i>t;case 4:return !0}}function Fi(s,t,e,i){for(var n;s.from==s.to||(e<1?s.from>=t:s.from>t)||(e>-1?s.to<=t:s.to<t);){let o=!i&&s instanceof dt&&s.index<0?null:s.parent;if(!o)return s;s=o;}let r=i?0:X.IgnoreOverlays;if(i)for(let o=s,l=o.parent;l;o=l,l=o.parent)o instanceof dt&&o.index<0&&((n=l.enter(t,e,r))===null||n===void 0?void 0:n.from)!=o.from&&(s=l);for(;;){let o=s.enter(t,e,r);if(!o)return s;s=o;}}class Yh{cursor(t=0){return new qn(this,t)}getChild(t,e=null,i=null){let n=Al(this,t,e,i);return n.length?n[0]:null}getChildren(t,e=null,i=null){return Al(this,t,e,i)}resolve(t,e=0){return Fi(this,t,e,!1)}resolveInner(t,e=0){return Fi(this,t,e,!0)}matchContext(t){return Sr(this.parent,t)}enterUnfinishedNodesBefore(t){let e=this.childBefore(t),i=this;for(;e;){let n=e.lastChild;if(!n||n.to!=e.to)break;n.type.isError&&n.from==n.to?(i=e,e=n.prevSibling):e=n;}return i}get node(){return this}get next(){return this.parent}}class dt extends Yh{constructor(t,e,i,n){super(),this._tree=t,this.from=e,this.index=i,this._parent=n;}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(t,e,i,n,r=0){for(let o=this;;){for(let{children:l,positions:a}=o._tree,h=e>0?l.length:-1;t!=h;t+=e){let c=l[t],f=a[t]+o.from;if(Gh(n,i,f,f+c.length)){if(c instanceof Ae){if(r&X.ExcludeBuffers)continue;let u=c.findChild(0,c.buffer.length,e,i-f,n);if(u>-1)return new ie(new Pp(o,c,t,f),null,u)}else if(r&X.IncludeAnonymous||!c.type.isAnonymous||Zr(c)){let u;if(!(r&X.IgnoreMounts)&&(u=Ni.get(c))&&!u.overlay)return new dt(u.tree,f,t,o);let d=new dt(c,f,t,o);return r&X.IncludeAnonymous||!d.type.isAnonymous?d:d.nextChild(e<0?c.children.length-1:0,e,i,n)}}}if(r&X.IncludeAnonymous||!o.type.isAnonymous||(o.index>=0?t=o.index+e:t=e<0?-1:o._parent._tree.children.length,o=o._parent,!o))return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(t){return this.nextChild(0,1,t,2)}childBefore(t){return this.nextChild(this._tree.children.length-1,-1,t,-2)}enter(t,e,i=0){let n;if(!(i&X.IgnoreOverlays)&&(n=Ni.get(this._tree))&&n.overlay){let r=t-this.from;for(let{from:o,to:l}of n.overlay)if((e>0?o<=r:o<r)&&(e<0?l>=r:l>r))return new dt(n.tree,n.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,t,e,i)}nextSignificantParent(){let t=this;for(;t.type.isAnonymous&&t._parent;)t=t._parent;return t}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}get tree(){return this._tree}toTree(){return this._tree}toString(){return this._tree.toString()}}function Al(s,t,e,i){let n=s.cursor(),r=[];if(!n.firstChild())return r;if(e!=null){for(let o=!1;!o;)if(o=n.type.is(e),!n.nextSibling())return r}for(;;){if(i!=null&&n.type.is(i))return r;if(n.type.is(t)&&r.push(n.node),!n.nextSibling())return i==null?r:[]}}function Sr(s,t,e=t.length-1){for(let i=s;e>=0;i=i.parent){if(!i)return !1;if(!i.type.isAnonymous){if(t[e]&&t[e]!=i.name)return !1;e--;}}return !0}class Pp{constructor(t,e,i,n){this.parent=t,this.buffer=e,this.index=i,this.start=n;}}class ie extends Yh{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(t,e,i){super(),this.context=t,this._parent=e,this.index=i,this.type=t.buffer.set.types[t.buffer.buffer[i]];}child(t,e,i){let{buffer:n}=this.context,r=n.findChild(this.index+4,n.buffer[this.index+3],t,e-this.context.start,i);return r<0?null:new ie(this.context,this,r)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(t){return this.child(1,t,2)}childBefore(t){return this.child(-1,t,-2)}enter(t,e,i=0){if(i&X.ExcludeBuffers)return null;let{buffer:n}=this.context,r=n.findChild(this.index+4,n.buffer[this.index+3],e>0?1:-1,t-this.context.start,e);return r<0?null:new ie(this.context,this,r)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(t){return this._parent?null:this.context.parent.nextChild(this.context.index+t,t,0,4)}get nextSibling(){let{buffer:t}=this.context,e=t.buffer[this.index+3];return e<(this._parent?t.buffer[this._parent.index+3]:t.buffer.length)?new ie(this.context,this._parent,e):this.externalSibling(1)}get prevSibling(){let{buffer:t}=this.context,e=this._parent?this._parent.index+4:0;return this.index==e?this.externalSibling(-1):new ie(this.context,this._parent,t.findChild(e,this.index,-1,0,4))}get tree(){return null}toTree(){let t=[],e=[],{buffer:i}=this.context,n=this.index+4,r=i.buffer[this.index+3];if(r>n){let o=i.buffer[this.index+1];t.push(i.slice(n,r,o)),e.push(0);}return new G(this.type,t,e,this.to-this.from)}toString(){return this.context.buffer.childString(this.index)}}function Jh(s){if(!s.length)return null;let t=0,e=s[0];for(let r=1;r<s.length;r++){let o=s[r];(o.from>e.from||o.to<e.to)&&(e=o,t=r);}let i=e instanceof dt&&e.index<0?null:e.parent,n=s.slice();return i?n[t]=i:n.splice(t,1),new Lp(n,e)}class Lp{constructor(t,e){this.heads=t,this.node=e;}get next(){return Jh(this.heads)}}function Rp(s,t,e){let i=s.resolveInner(t,e),n=null;for(let r=i instanceof dt?i:i.context.parent;r;r=r.parent)if(r.index<0){let o=r.parent;(n||(n=[i])).push(o.resolve(t,e)),r=o;}else {let o=Ni.get(r.tree);if(o&&o.overlay&&o.overlay[0].from<=t&&o.overlay[o.overlay.length-1].to>=t){let l=new dt(o.tree,o.overlay[0].from+r.from,-1,r);(n||(n=[i])).push(Fi(l,t,e,!1));}}return n?Jh(n):i}class qn{get name(){return this.type.name}constructor(t,e=0){if(this.mode=e,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,t instanceof dt)this.yieldNode(t);else {this._tree=t.context.parent,this.buffer=t.context;for(let i=t._parent;i;i=i._parent)this.stack.unshift(i.index);this.bufferNode=t,this.yieldBuf(t.index);}}yieldNode(t){return t?(this._tree=t,this.type=t.type,this.from=t.from,this.to=t.to,!0):!1}yieldBuf(t,e){this.index=t;let{start:i,buffer:n}=this.buffer;return this.type=e||n.set.types[n.buffer[t]],this.from=i+n.buffer[t+1],this.to=i+n.buffer[t+2],!0}yield(t){return t?t instanceof dt?(this.buffer=null,this.yieldNode(t)):(this.buffer=t.context,this.yieldBuf(t.index,t.type)):!1}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(t,e,i){if(!this.buffer)return this.yield(this._tree.nextChild(t<0?this._tree._tree.children.length-1:0,t,e,i,this.mode));let{buffer:n}=this.buffer,r=n.findChild(this.index+4,n.buffer[this.index+3],t,e-this.buffer.start,i);return r<0?!1:(this.stack.push(this.index),this.yieldBuf(r))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(t){return this.enterChild(1,t,2)}childBefore(t){return this.enterChild(-1,t,-2)}enter(t,e,i=this.mode){return this.buffer?i&X.ExcludeBuffers?!1:this.enterChild(1,t,e):this.yield(this._tree.enter(t,e,i))}parent(){if(!this.buffer)return this.yieldNode(this.mode&X.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let t=this.mode&X.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(t)}sibling(t){if(!this.buffer)return this._tree._parent?this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+t,t,0,4,this.mode)):!1;let{buffer:e}=this.buffer,i=this.stack.length-1;if(t<0){let n=i<0?0:this.stack[i]+4;if(this.index!=n)return this.yieldBuf(e.findChild(n,this.index,-1,0,4))}else {let n=e.buffer[this.index+3];if(n<(i<0?e.buffer.length:e.buffer[this.stack[i]+3]))return this.yieldBuf(n)}return i<0?this.yield(this.buffer.parent.nextChild(this.buffer.index+t,t,0,4,this.mode)):!1}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(t){let e,i,{buffer:n}=this;if(n){if(t>0){if(this.index<n.buffer.buffer.length)return !1}else for(let r=0;r<this.index;r++)if(n.buffer.buffer[r+3]<this.index)return !1;({index:e,parent:i}=n);}else ({index:e,_parent:i}=this._tree);for(;i;{index:e,_parent:i}=i)if(e>-1)for(let r=e+t,o=t<0?-1:i._tree.children.length;r!=o;r+=t){let l=i._tree.children[r];if(this.mode&X.IncludeAnonymous||l instanceof Ae||!l.type.isAnonymous||Zr(l))return !1}return !0}move(t,e){if(e&&this.enterChild(t,0,4))return !0;for(;;){if(this.sibling(t))return !0;if(this.atLastNode(t)||!this.parent())return !1}}next(t=!0){return this.move(1,t)}prev(t=!0){return this.move(-1,t)}moveTo(t,e=0){for(;(this.from==this.to||(e<1?this.from>=t:this.from>t)||(e>-1?this.to<=t:this.to<t))&&this.parent(););for(;this.enterChild(1,t,e););return this}get node(){if(!this.buffer)return this._tree;let t=this.bufferNode,e=null,i=0;if(t&&t.context==this.buffer)t:for(let n=this.index,r=this.stack.length;r>=0;){for(let o=t;o;o=o._parent)if(o.index==n){if(n==this.index)return o;e=o,i=r+1;break t}n=this.stack[--r];}for(let n=i;n<this.stack.length;n++)e=new ie(this.buffer,e,this.stack[n]);return this.bufferNode=new ie(this.buffer,e,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(t,e){for(let i=0;;){let n=!1;if(this.type.isAnonymous||t(this)!==!1){if(this.firstChild()){i++;continue}this.type.isAnonymous||(n=!0);}for(;;){if(n&&e&&e(this),n=this.type.isAnonymous,!i)return;if(this.nextSibling())break;this.parent(),i--,n=!0;}}}matchContext(t){if(!this.buffer)return Sr(this.node.parent,t);let{buffer:e}=this.buffer,{types:i}=e.set;for(let n=t.length-1,r=this.stack.length-1;n>=0;r--){if(r<0)return Sr(this._tree,t,n);let o=i[e.buffer[this.stack[r]]];if(!o.isAnonymous){if(t[n]&&t[n]!=o.name)return !1;n--;}}return !0}}function Zr(s){return s.children.some(t=>t instanceof Ae||!t.type.isAnonymous||Zr(t))}function Ip(s){var t;let{buffer:e,nodeSet:i,maxBufferLength:n=Op,reused:r=[],minRepeatType:o=i.types.length}=s,l=Array.isArray(e)?new Qr(e,e.length):e,a=i.types,h=0,c=0;function f(w,C,A,E,L,q){let{id:P,start:B,end:_,size:V}=l,j=c,st=h;for(;V<0;)if(l.next(),V==-1){let _t=r[P];A.push(_t),E.push(B-w);return}else if(V==-3){h=P;return}else if(V==-4){c=P;return}else throw new RangeError(`Unrecognized record size: ${V}`);let mt=a[P],Bt,et,xt=B-w;if(_-B<=n&&(et=g(l.pos-C,L))){let _t=new Uint16Array(et.size-et.skip),F=l.pos-et.size,Q=_t.length;for(;l.pos>F;)Q=b(et.start,_t,Q);Bt=new Ae(_t,_-et.start,i),xt=et.start-w;}else {let _t=l.pos-V;l.next();let F=[],Q=[],rt=P>=o?P:-1,zt=0,Te=_;for(;l.pos>_t;)rt>=0&&l.id==rt&&l.size>=0?(l.end<=Te-n&&(p(F,Q,B,zt,l.end,Te,rt,j,st),zt=F.length,Te=l.end),l.next()):q>2500?u(B,_t,F,Q):f(B,_t,F,Q,rt,q+1);if(rt>=0&&zt>0&&zt<F.length&&p(F,Q,B,zt,B,Te,rt,j,st),F.reverse(),Q.reverse(),rt>-1&&zt>0){let tn=d(mt,st);Bt=$r(mt,F,Q,0,F.length,0,_-B,tn,tn);}else Bt=y(mt,F,Q,_-B,j-_,st);}A.push(Bt),E.push(xt);}function u(w,C,A,E){let L=[],q=0,P=-1;for(;l.pos>C;){let{id:B,start:_,end:V,size:j}=l;if(j>4)l.next();else {if(P>-1&&_<P)break;P<0&&(P=V-n),L.push(B,_,V),q++,l.next();}}if(q){let B=new Uint16Array(q*4),_=L[L.length-2];for(let V=L.length-3,j=0;V>=0;V-=3)B[j++]=L[V],B[j++]=L[V+1]-_,B[j++]=L[V+2]-_,B[j++]=j;A.push(new Ae(B,L[2]-_,i)),E.push(_-w);}}function d(w,C){return (A,E,L)=>{let q=0,P=A.length-1,B,_;if(P>=0&&(B=A[P])instanceof G){if(!P&&B.type==w&&B.length==L)return B;(_=B.prop(N.lookAhead))&&(q=E[P]+B.length+_);}return y(w,A,E,L,q,C)}}function p(w,C,A,E,L,q,P,B,_){let V=[],j=[];for(;w.length>E;)V.push(w.pop()),j.push(C.pop()+A-L);w.push(y(i.types[P],V,j,q-L,B-q,_)),C.push(L-A);}function y(w,C,A,E,L,q,P){if(q){let B=[N.contextHash,q];P=P?[B].concat(P):[B];}if(L>25){let B=[N.lookAhead,L];P=P?[B].concat(P):[B];}return new G(w,C,A,E,P)}function g(w,C){let A=l.fork(),E=0,L=0,q=0,P=A.end-n,B={size:0,start:0,skip:0};t:for(let _=A.pos-w;A.pos>_;){let V=A.size;if(A.id==C&&V>=0){B.size=E,B.start=L,B.skip=q,q+=4,E+=4,A.next();continue}let j=A.pos-V;if(V<0||j<_||A.start<P)break;let st=A.id>=o?4:0,mt=A.start;for(A.next();A.pos>j;){if(A.size<0)if(A.size==-3)st+=4;else break t;else A.id>=o&&(st+=4);A.next();}L=mt,E+=V,q+=st;}return (C<0||E==w)&&(B.size=E,B.start=L,B.skip=q),B.size>4?B:void 0}function b(w,C,A){let{id:E,start:L,end:q,size:P}=l;if(l.next(),P>=0&&E<o){let B=A;if(P>4){let _=l.pos-(P-4);for(;l.pos>_;)A=b(w,C,A);}C[--A]=B,C[--A]=q-w,C[--A]=L-w,C[--A]=E;}else P==-3?h=E:P==-4&&(c=E);return A}let x=[],k=[];for(;l.pos>0;)f(s.start||0,s.bufferStart||0,x,k,-1,0);let S=(t=s.length)!==null&&t!==void 0?t:x.length?k[0]+x[0].length:0;return new G(a[s.topID],x.reverse(),k.reverse(),S)}const Ml=new WeakMap;function Rn(s,t){if(!s.isAnonymous||t instanceof Ae||t.type!=s)return 1;let e=Ml.get(t);if(e==null){e=1;for(let i of t.children){if(i.type!=s||!(i instanceof G)){e=1;break}e+=Rn(s,i);}Ml.set(t,e);}return e}function $r(s,t,e,i,n,r,o,l,a){let h=0;for(let p=i;p<n;p++)h+=Rn(s,t[p]);let c=Math.ceil(h*1.5/8),f=[],u=[];function d(p,y,g,b,x){for(let k=g;k<b;){let S=k,w=y[k],C=Rn(s,p[k]);for(k++;k<b;k++){let A=Rn(s,p[k]);if(C+A>=c)break;C+=A;}if(k==S+1){if(C>c){let A=p[S];d(A.children,A.positions,0,A.children.length,y[S]+x);continue}f.push(p[S]);}else {let A=y[k-1]+p[k-1].length-w;f.push($r(s,p,y,S,k,w,A,null,a));}u.push(w+x-r);}}return d(t,e,i,n,0),(l||a)(f,u,o)}class Uy{constructor(){this.map=new WeakMap;}setBuffer(t,e,i){let n=this.map.get(t);n||this.map.set(t,n=new Map),n.set(e,i);}getBuffer(t,e){let i=this.map.get(t);return i&&i.get(e)}set(t,e){t instanceof ie?this.setBuffer(t.context.buffer,t.index,e):t instanceof dt&&this.map.set(t.tree,e);}get(t){return t instanceof ie?this.getBuffer(t.context.buffer,t.index):t instanceof dt?this.map.get(t.tree):void 0}cursorSet(t,e){t.buffer?this.setBuffer(t.buffer.buffer,t.index,e):this.map.set(t.tree,e);}cursorGet(t){return t.buffer?this.getBuffer(t.buffer.buffer,t.index):this.map.get(t.tree)}}class fe{constructor(t,e,i,n,r=!1,o=!1){this.from=t,this.to=e,this.tree=i,this.offset=n,this.open=(r?1:0)|(o?2:0);}get openStart(){return (this.open&1)>0}get openEnd(){return (this.open&2)>0}static addTree(t,e=[],i=!1){let n=[new fe(0,t.length,t,0,!1,i)];for(let r of e)r.to>t.length&&n.push(r);return n}static applyChanges(t,e,i=128){if(!e.length)return t;let n=[],r=1,o=t.length?t[0]:null;for(let l=0,a=0,h=0;;l++){let c=l<e.length?e[l]:null,f=c?c.fromA:1e9;if(f-a>=i)for(;o&&o.from<f;){let u=o;if(a>=u.from||f<=u.to||h){let d=Math.max(u.from,a)-h,p=Math.min(u.to,f)-h;u=d>=p?null:new fe(d,p,u.tree,u.offset+h,l>0,!!c);}if(u&&n.push(u),o.to>f)break;o=r<t.length?t[r++]:null;}if(!c)break;a=c.toA,h=c.toA-c.toB;}return n}}class Xh{startParse(t,e,i){return typeof t=="string"&&(t=new Np(t)),i=i?i.length?i.map(n=>new Rt(n.from,n.to)):[new Rt(0,0)]:[new Rt(0,t.length)],this.createParse(t,e||[],i)}parse(t,e,i){let n=this.startParse(t,e,i);for(;;){let r=n.advance();if(r)return r}}}class Np{constructor(t){this.string=t;}get length(){return this.string.length}chunk(t){return this.string.slice(t)}get lineChunks(){return !1}read(t,e){return this.string.slice(t,e)}}function Gy(s){return (t,e,i,n)=>new Hp(t,s,e,i,n)}class Dl{constructor(t,e,i,n,r){this.parser=t,this.parse=e,this.overlay=i,this.target=n,this.from=r;}}function Tl(s){if(!s.length||s.some(t=>t.from>=t.to))throw new RangeError("Invalid inner parse ranges given: "+JSON.stringify(s))}class Fp{constructor(t,e,i,n,r,o,l){this.parser=t,this.predicate=e,this.mounts=i,this.index=n,this.start=r,this.target=o,this.prev=l,this.depth=0,this.ranges=[];}}const Cr=new N({perNode:!0});class Hp{constructor(t,e,i,n,r){this.nest=e,this.input=i,this.fragments=n,this.ranges=r,this.inner=[],this.innerDone=0,this.baseTree=null,this.stoppedAt=null,this.baseParse=t;}advance(){if(this.baseParse){let i=this.baseParse.advance();if(!i)return null;if(this.baseParse=null,this.baseTree=i,this.startInner(),this.stoppedAt!=null)for(let n of this.inner)n.parse.stopAt(this.stoppedAt);}if(this.innerDone==this.inner.length){let i=this.baseTree;return this.stoppedAt!=null&&(i=new G(i.type,i.children,i.positions,i.length,i.propValues.concat([[Cr,this.stoppedAt]]))),i}let t=this.inner[this.innerDone],e=t.parse.advance();if(e){this.innerDone++;let i=Object.assign(Object.create(null),t.target.props);i[N.mounted.id]=new Ni(e,t.overlay,t.parser),t.target.props=i;}return null}get parsedPos(){if(this.baseParse)return 0;let t=this.input.length;for(let e=this.innerDone;e<this.inner.length;e++)this.inner[e].from<t&&(t=Math.min(t,this.inner[e].parse.parsedPos));return t}stopAt(t){if(this.stoppedAt=t,this.baseParse)this.baseParse.stopAt(t);else for(let e=this.innerDone;e<this.inner.length;e++)this.inner[e].parse.stopAt(t);}startInner(){let t=new _p(this.fragments),e=null,i=null,n=new qn(new dt(this.baseTree,this.ranges[0].from,0,null),X.IncludeAnonymous|X.IgnoreMounts);t:for(let r,o;;){let l=!0,a;if(this.stoppedAt!=null&&n.from>=this.stoppedAt)l=!1;else if(t.hasNode(n)){if(e){let h=e.mounts.find(c=>c.frag.from<=n.from&&c.frag.to>=n.to&&c.mount.overlay);if(h)for(let c of h.mount.overlay){let f=c.from+h.pos,u=c.to+h.pos;f>=n.from&&u<=n.to&&!e.ranges.some(d=>d.from<u&&d.to>f)&&e.ranges.push({from:f,to:u});}}l=!1;}else if(i&&(o=Vp(i.ranges,n.from,n.to)))l=o!=2;else if(!n.type.isAnonymous&&(r=this.nest(n,this.input))&&(n.from<n.to||!r.overlay)){n.tree||Wp(n);let h=t.findMounts(n.from,r.parser);if(typeof r.overlay=="function")e=new Fp(r.parser,r.overlay,h,this.inner.length,n.from,n.tree,e);else {let c=El(this.ranges,r.overlay||(n.from<n.to?[new Rt(n.from,n.to)]:[]));c.length&&Tl(c),(c.length||!r.overlay)&&this.inner.push(new Dl(r.parser,c.length?r.parser.startParse(this.input,Pl(h,c),c):r.parser.startParse(""),r.overlay?r.overlay.map(f=>new Rt(f.from-n.from,f.to-n.from)):null,n.tree,c.length?c[0].from:n.from)),r.overlay?c.length&&(i={ranges:c,depth:0,prev:i}):l=!1;}}else if(e&&(a=e.predicate(n))&&(a===!0&&(a=new Rt(n.from,n.to)),a.from<a.to)){let h=e.ranges.length-1;h>=0&&e.ranges[h].to==a.from?e.ranges[h]={from:e.ranges[h].from,to:a.to}:e.ranges.push(a);}if(l&&n.firstChild())e&&e.depth++,i&&i.depth++;else for(;!n.nextSibling();){if(!n.parent())break t;if(e&&!--e.depth){let h=El(this.ranges,e.ranges);h.length&&(Tl(h),this.inner.splice(e.index,0,new Dl(e.parser,e.parser.startParse(this.input,Pl(e.mounts,h),h),e.ranges.map(c=>new Rt(c.from-e.start,c.to-e.start)),e.target,h[0].from))),e=e.prev;}i&&!--i.depth&&(i=i.prev);}}}}function Vp(s,t,e){for(let i of s){if(i.from>=e)break;if(i.to>t)return i.from<=t&&i.to>=e?2:1}return 0}function Ol(s,t,e,i,n,r){if(t<e){let o=s.buffer[t+1];i.push(s.slice(t,e,o)),n.push(o-r);}}function Wp(s){let{node:t}=s,e=[],i=t.context.buffer;do e.push(s.index),s.parent();while(!s.tree);let n=s.tree,r=n.children.indexOf(i),o=n.children[r],l=o.buffer,a=[r];function h(c,f,u,d,p,y){let g=e[y],b=[],x=[];Ol(o,c,g,b,x,d);let k=l[g+1],S=l[g+2];a.push(b.length);let w=y?h(g+4,l[g+3],o.set.types[l[g]],k,S-k,y-1):t.toTree();return b.push(w),x.push(k-d),Ol(o,l[g+3],f,b,x,d),new G(u,b,x,p)}n.children[r]=h(0,l.length,wt.none,0,o.length,e.length-1);for(let c of a){let f=s.tree.children[c],u=s.tree.positions[c];s.yield(new dt(f,u+s.from,c,s._tree));}}class Bl{constructor(t,e){this.offset=e,this.done=!1,this.cursor=t.cursor(X.IncludeAnonymous|X.IgnoreMounts);}moveTo(t){let{cursor:e}=this,i=t-this.offset;for(;!this.done&&e.from<i;)e.to>=t&&e.enter(i,1,X.IgnoreOverlays|X.ExcludeBuffers)||e.next(!1)||(this.done=!0);}hasNode(t){if(this.moveTo(t.from),!this.done&&this.cursor.from+this.offset==t.from&&this.cursor.tree)for(let e=this.cursor.tree;;){if(e==t.tree)return !0;if(e.children.length&&e.positions[0]==0&&e.children[0]instanceof G)e=e.children[0];else break}return !1}}class _p{constructor(t){var e;if(this.fragments=t,this.curTo=0,this.fragI=0,t.length){let i=this.curFrag=t[0];this.curTo=(e=i.tree.prop(Cr))!==null&&e!==void 0?e:i.to,this.inner=new Bl(i.tree,-i.offset);}else this.curFrag=this.inner=null;}hasNode(t){for(;this.curFrag&&t.from>=this.curTo;)this.nextFrag();return this.curFrag&&this.curFrag.from<=t.from&&this.curTo>=t.to&&this.inner.hasNode(t)}nextFrag(){var t;if(this.fragI++,this.fragI==this.fragments.length)this.curFrag=this.inner=null;else {let e=this.curFrag=this.fragments[this.fragI];this.curTo=(t=e.tree.prop(Cr))!==null&&t!==void 0?t:e.to,this.inner=new Bl(e.tree,-e.offset);}}findMounts(t,e){var i;let n=[];if(this.inner){this.inner.cursor.moveTo(t,1);for(let r=this.inner.cursor.node;r;r=r.parent){let o=(i=r.tree)===null||i===void 0?void 0:i.prop(N.mounted);if(o&&o.parser==e)for(let l=this.fragI;l<this.fragments.length;l++){let a=this.fragments[l];if(a.from>=r.to)break;a.tree==this.curFrag.tree&&n.push({frag:a,pos:r.from-a.offset,mount:o});}}}return n}}function El(s,t){let e=null,i=t;for(let n=1,r=0;n<s.length;n++){let o=s[n-1].to,l=s[n].from;for(;r<i.length;r++){let a=i[r];if(a.from>=l)break;a.to<=o||(e||(i=e=t.slice()),a.from<o?(e[r]=new Rt(a.from,o),a.to>l&&e.splice(r+1,0,new Rt(l,a.to))):a.to>l?e[r--]=new Rt(l,a.to):e.splice(r--,1));}}return i}function zp(s,t,e,i){let n=0,r=0,o=!1,l=!1,a=-1e9,h=[];for(;;){let c=n==s.length?1e9:o?s[n].to:s[n].from,f=r==t.length?1e9:l?t[r].to:t[r].from;if(o!=l){let u=Math.max(a,e),d=Math.min(c,f,i);u<d&&h.push(new Rt(u,d));}if(a=Math.min(c,f),a==1e9)break;c==a&&(o?(o=!1,n++):o=!0),f==a&&(l?(l=!1,r++):l=!0);}return h}function Pl(s,t){let e=[];for(let{pos:i,mount:n,frag:r}of s){let o=i+(n.overlay?n.overlay[0].from:0),l=o+n.tree.length,a=Math.max(r.from,o),h=Math.min(r.to,l);if(n.overlay){let c=n.overlay.map(u=>new Rt(u.from+i,u.to+i)),f=zp(t,c,a,h);for(let u=0,d=a;;u++){let p=u==f.length,y=p?h:f[u].from;if(y>d&&e.push(new fe(d,y,n.tree,-o,r.from>=d||r.openStart,r.to<=y||r.openEnd)),p)break;d=f[u].to;}}else e.push(new fe(a,h,n.tree,-o,r.from>=o||r.openStart,r.to<=l||r.openEnd));}return e}let qp=0;class Pt{constructor(t,e,i,n){this.name=t,this.set=e,this.base=i,this.modified=n,this.id=qp++;}toString(){let{name:t}=this;for(let e of this.modified)e.name&&(t=`${e.name}(${t})`);return t}static define(t,e){let i=typeof t=="string"?t:"?";if(t instanceof Pt&&(e=t),e?.base)throw new Error("Can not derive from a modified tag");let n=new Pt(i,[],null,[]);if(n.set.push(n),e)for(let r of e.set)n.set.push(r);return n}static defineModifier(t){let e=new jn(t);return i=>i.modified.indexOf(e)>-1?i:jn.get(i.base||i,i.modified.concat(e).sort((n,r)=>n.id-r.id))}}let jp=0;class jn{constructor(t){this.name=t,this.instances=[],this.id=jp++;}static get(t,e){if(!e.length)return t;let i=e[0].instances.find(l=>l.base==t&&Kp(e,l.modified));if(i)return i;let n=[],r=new Pt(t.name,n,t,e);for(let l of e)l.instances.push(r);let o=Up(e);for(let l of t.set)if(!l.modified.length)for(let a of o)n.push(jn.get(l,a));return r}}function Kp(s,t){return s.length==t.length&&s.every((e,i)=>e==t[i])}function Up(s){let t=[[]];for(let e=0;e<s.length;e++)for(let i=0,n=t.length;i<n;i++)t.push(t[i].concat(s[e]));return t.sort((e,i)=>i.length-e.length)}function Gp(s){let t=Object.create(null);for(let e in s){let i=s[e];Array.isArray(i)||(i=[i]);for(let n of e.split(" "))if(n){let r=[],o=2,l=n;for(let f=0;;){if(l=="..."&&f>0&&f+3==n.length){o=1;break}let u=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(l);if(!u)throw new RangeError("Invalid path: "+n);if(r.push(u[0]=="*"?"":u[0][0]=='"'?JSON.parse(u[0]):u[0]),f+=u[0].length,f==n.length)break;let d=n[f++];if(f==n.length&&d=="!"){o=0;break}if(d!="/")throw new RangeError("Invalid path: "+n);l=n.slice(f);}let a=r.length-1,h=r[a];if(!h)throw new RangeError("Invalid path: "+n);let c=new Kn(i,o,a>0?r.slice(0,a):null);t[h]=c.sort(t[h]);}}return Qh.add(t)}const Qh=new N;class Kn{constructor(t,e,i,n){this.tags=t,this.mode=e,this.context=i,this.next=n;}get opaque(){return this.mode==0}get inherit(){return this.mode==1}sort(t){return !t||t.depth<this.depth?(this.next=t,this):(t.next=this.sort(t.next),t)}get depth(){return this.context?this.context.length:0}}Kn.empty=new Kn([],2,null);function Zh(s,t){let e=Object.create(null);for(let r of s)if(!Array.isArray(r.tag))e[r.tag.id]=r.class;else for(let o of r.tag)e[o.id]=r.class;let{scope:i,all:n=null}=t||{};return {style:r=>{let o=n;for(let l of r)for(let a of l.set){let h=e[a.id];if(h){o=o?o+" "+h:h;break}}return o},scope:i}}function Yp(s,t){let e=null;for(let i of s){let n=i.style(t);n&&(e=e?e+" "+n:n);}return e}function Jp(s,t,e,i=0,n=s.length){let r=new Xp(i,Array.isArray(t)?t:[t],e);r.highlightRange(s.cursor(),i,n,"",r.highlighters),r.flush(n);}class Xp{constructor(t,e,i){this.at=t,this.highlighters=e,this.span=i,this.class="";}startSpan(t,e){e!=this.class&&(this.flush(t),t>this.at&&(this.at=t),this.class=e);}flush(t){t>this.at&&this.class&&this.span(this.at,t,this.class);}highlightRange(t,e,i,n,r){let{type:o,from:l,to:a}=t;if(l>=i||a<=e)return;o.isTop&&(r=this.highlighters.filter(d=>!d.scope||d.scope(o)));let h=n,c=Qp(t)||Kn.empty,f=Yp(r,c.tags);if(f&&(h&&(h+=" "),h+=f,c.mode==1&&(n+=(n?" ":"")+f)),this.startSpan(Math.max(e,l),h),c.opaque)return;let u=t.tree&&t.tree.prop(N.mounted);if(u&&u.overlay){let d=t.node.enter(u.overlay[0].from+l,1),p=this.highlighters.filter(g=>!g.scope||g.scope(u.tree.type)),y=t.firstChild();for(let g=0,b=l;;g++){let x=g<u.overlay.length?u.overlay[g]:null,k=x?x.from+l:a,S=Math.max(e,b),w=Math.min(i,k);if(S<w&&y)for(;t.from<w&&(this.highlightRange(t,S,w,n,r),this.startSpan(Math.min(w,t.to),h),!(t.to>=k||!t.nextSibling())););if(!x||k>i)break;b=x.to+l,b>e&&(this.highlightRange(d.cursor(),Math.max(e,x.from+l),Math.min(i,b),"",p),this.startSpan(Math.min(i,b),h));}y&&t.parent();}else if(t.firstChild()){u&&(n="");do if(!(t.to<=e)){if(t.from>=i)break;this.highlightRange(t,e,i,n,r),this.startSpan(Math.min(i,t.to),h);}while(t.nextSibling());t.parent();}}}function Qp(s){let t=s.type.prop(Qh);for(;t&&t.context&&!s.matchContext(t.context);)t=t.next;return t||null}const M=Pt.define,yn=M(),pe=M(),Ll=M(pe),Rl=M(pe),me=M(),bn=M(me),Ss=M(me),Qt=M(),Oe=M(Qt),Jt=M(),Xt=M(),Ar=M(),mi=M(Ar),wn=M(),m={comment:yn,lineComment:M(yn),blockComment:M(yn),docComment:M(yn),name:pe,variableName:M(pe),typeName:Ll,tagName:M(Ll),propertyName:Rl,attributeName:M(Rl),className:M(pe),labelName:M(pe),namespace:M(pe),macroName:M(pe),literal:me,string:bn,docString:M(bn),character:M(bn),attributeValue:M(bn),number:Ss,integer:M(Ss),float:M(Ss),bool:M(me),regexp:M(me),escape:M(me),color:M(me),url:M(me),keyword:Jt,self:M(Jt),null:M(Jt),atom:M(Jt),unit:M(Jt),modifier:M(Jt),operatorKeyword:M(Jt),controlKeyword:M(Jt),definitionKeyword:M(Jt),moduleKeyword:M(Jt),operator:Xt,derefOperator:M(Xt),arithmeticOperator:M(Xt),logicOperator:M(Xt),bitwiseOperator:M(Xt),compareOperator:M(Xt),updateOperator:M(Xt),definitionOperator:M(Xt),typeOperator:M(Xt),controlOperator:M(Xt),punctuation:Ar,separator:M(Ar),bracket:mi,angleBracket:M(mi),squareBracket:M(mi),paren:M(mi),brace:M(mi),content:Qt,heading:Oe,heading1:M(Oe),heading2:M(Oe),heading3:M(Oe),heading4:M(Oe),heading5:M(Oe),heading6:M(Oe),contentSeparator:M(Qt),list:M(Qt),quote:M(Qt),emphasis:M(Qt),strong:M(Qt),link:M(Qt),monospace:M(Qt),strikethrough:M(Qt),inserted:M(),deleted:M(),changed:M(),invalid:M(),meta:wn,documentMeta:M(wn),annotation:M(wn),processingInstruction:M(wn),definition:Pt.defineModifier("definition"),constant:Pt.defineModifier("constant"),function:Pt.defineModifier("function"),standard:Pt.defineModifier("standard"),local:Pt.defineModifier("local"),special:Pt.defineModifier("special")};for(let s in m){let t=m[s];t instanceof Pt&&(t.name=s);}Zh([{tag:m.link,class:"tok-link"},{tag:m.heading,class:"tok-heading"},{tag:m.emphasis,class:"tok-emphasis"},{tag:m.strong,class:"tok-strong"},{tag:m.keyword,class:"tok-keyword"},{tag:m.atom,class:"tok-atom"},{tag:m.bool,class:"tok-bool"},{tag:m.url,class:"tok-url"},{tag:m.labelName,class:"tok-labelName"},{tag:m.inserted,class:"tok-inserted"},{tag:m.deleted,class:"tok-deleted"},{tag:m.literal,class:"tok-literal"},{tag:m.string,class:"tok-string"},{tag:m.number,class:"tok-number"},{tag:[m.regexp,m.escape,m.special(m.string)],class:"tok-string2"},{tag:m.variableName,class:"tok-variableName"},{tag:m.local(m.variableName),class:"tok-variableName tok-local"},{tag:m.definition(m.variableName),class:"tok-variableName tok-definition"},{tag:m.special(m.variableName),class:"tok-variableName2"},{tag:m.definition(m.propertyName),class:"tok-propertyName tok-definition"},{tag:m.typeName,class:"tok-typeName"},{tag:m.namespace,class:"tok-namespace"},{tag:m.className,class:"tok-className"},{tag:m.macroName,class:"tok-macroName"},{tag:m.propertyName,class:"tok-propertyName"},{tag:m.operator,class:"tok-operator"},{tag:m.comment,class:"tok-comment"},{tag:m.meta,class:"tok-meta"},{tag:m.invalid,class:"tok-invalid"},{tag:m.punctuation,class:"tok-punctuation"}]);var Cs;const Re=new N;function $h(s){return T.define({combine:s?t=>t.concat(s):void 0})}const Zp=new N;class It{constructor(t,e,i=[],n=""){this.data=t,this.name=n,W.prototype.hasOwnProperty("tree")||Object.defineProperty(W.prototype,"tree",{get(){return ct(this)}}),this.parser=e,this.extension=[Me.of(this),W.languageData.of((r,o,l)=>{let a=Il(r,o,l),h=a.type.prop(Re);if(!h)return [];let c=r.facet(h),f=a.type.prop(Zp);if(f){let u=a.resolve(o-a.from,l);for(let d of f)if(d.test(u,r)){let p=r.facet(d.facet);return d.type=="replace"?p:p.concat(c)}}return c})].concat(i);}isActiveAt(t,e,i=-1){return Il(t,e,i).type.prop(Re)==this.data}findRegions(t){let e=t.facet(Me);if(e?.data==this.data)return [{from:0,to:t.doc.length}];if(!e||!e.allowsNesting)return [];let i=[],n=(r,o)=>{if(r.prop(Re)==this.data){i.push({from:o,to:o+r.length});return}let l=r.prop(N.mounted);if(l){if(l.tree.prop(Re)==this.data){if(l.overlay)for(let a of l.overlay)i.push({from:a.from+o,to:a.to+o});else i.push({from:o,to:o+r.length});return}else if(l.overlay){let a=i.length;if(n(l.tree,l.overlay[0].from+o),i.length>a)return}}for(let a=0;a<r.children.length;a++){let h=r.children[a];h instanceof G&&n(h,r.positions[a]+o);}};return n(ct(t),0),i}get allowsNesting(){return !0}}It.setState=R.define();function Il(s,t,e){let i=s.facet(Me),n=ct(s).topNode;if(!i||i.allowsNesting)for(let r=n;r;r=r.enter(t,e,X.ExcludeBuffers))r.type.isTop&&(n=r);return n}class Mr extends It{constructor(t,e,i){super(t,e,[],i),this.parser=e;}static define(t){let e=$h(t.languageData);return new Mr(e,t.parser.configure({props:[Re.add(i=>i.isTop?e:void 0)]}),t.name)}configure(t,e){return new Mr(this.data,this.parser.configure(t),e||this.name)}get allowsNesting(){return this.parser.hasWrappers()}}function ct(s){let t=s.field(It.state,!1);return t?t.tree:G.empty}class $p{constructor(t){this.doc=t,this.cursorPos=0,this.string="",this.cursor=t.iter();}get length(){return this.doc.length}syncTo(t){return this.string=this.cursor.next(t-this.cursorPos).value,this.cursorPos=t+this.string.length,this.cursorPos-this.string.length}chunk(t){return this.syncTo(t),this.string}get lineChunks(){return !0}read(t,e){let i=this.cursorPos-this.string.length;return t<i||e>=this.cursorPos?this.doc.sliceString(t,e):this.string.slice(t-i,e-i)}}let gi=null;class ri{constructor(t,e,i=[],n,r,o,l,a){this.parser=t,this.state=e,this.fragments=i,this.tree=n,this.treeLen=r,this.viewport=o,this.skipped=l,this.scheduleOn=a,this.parse=null,this.tempSkipped=[];}static create(t,e,i){return new ri(t,e,[],G.empty,0,i,[],null)}startParse(){return this.parser.startParse(new $p(this.state.doc),this.fragments)}work(t,e){return e!=null&&e>=this.state.doc.length&&(e=void 0),this.tree!=G.empty&&this.isDone(e??this.state.doc.length)?(this.takeTree(),!0):this.withContext(()=>{var i;if(typeof t=="number"){let n=Date.now()+t;t=()=>Date.now()>n;}for(this.parse||(this.parse=this.startParse()),e!=null&&(this.parse.stoppedAt==null||this.parse.stoppedAt>e)&&e<this.state.doc.length&&this.parse.stopAt(e);;){let n=this.parse.advance();if(n)if(this.fragments=this.withoutTempSkipped(fe.addTree(n,this.fragments,this.parse.stoppedAt!=null)),this.treeLen=(i=this.parse.stoppedAt)!==null&&i!==void 0?i:this.state.doc.length,this.tree=n,this.parse=null,this.treeLen<(e??this.state.doc.length))this.parse=this.startParse();else return !0;if(t())return !1}})}takeTree(){let t,e;this.parse&&(t=this.parse.parsedPos)>=this.treeLen&&((this.parse.stoppedAt==null||this.parse.stoppedAt>t)&&this.parse.stopAt(t),this.withContext(()=>{for(;!(e=this.parse.advance()););}),this.treeLen=t,this.tree=e,this.fragments=this.withoutTempSkipped(fe.addTree(this.tree,this.fragments,!0)),this.parse=null);}withContext(t){let e=gi;gi=this;try{return t()}finally{gi=e;}}withoutTempSkipped(t){for(let e;e=this.tempSkipped.pop();)t=Nl(t,e.from,e.to);return t}changes(t,e){let{fragments:i,tree:n,treeLen:r,viewport:o,skipped:l}=this;if(this.takeTree(),!t.empty){let a=[];if(t.iterChangedRanges((h,c,f,u)=>a.push({fromA:h,toA:c,fromB:f,toB:u})),i=fe.applyChanges(i,a),n=G.empty,r=0,o={from:t.mapPos(o.from,-1),to:t.mapPos(o.to,1)},this.skipped.length){l=[];for(let h of this.skipped){let c=t.mapPos(h.from,1),f=t.mapPos(h.to,-1);c<f&&l.push({from:c,to:f});}}}return new ri(this.parser,e,i,n,r,o,l,this.scheduleOn)}updateViewport(t){if(this.viewport.from==t.from&&this.viewport.to==t.to)return !1;this.viewport=t;let e=this.skipped.length;for(let i=0;i<this.skipped.length;i++){let{from:n,to:r}=this.skipped[i];n<t.to&&r>t.from&&(this.fragments=Nl(this.fragments,n,r),this.skipped.splice(i--,1));}return this.skipped.length>=e?!1:(this.reset(),!0)}reset(){this.parse&&(this.takeTree(),this.parse=null);}skipUntilInView(t,e){this.skipped.push({from:t,to:e});}static getSkippingParser(t){return new class extends Xh{createParse(e,i,n){let r=n[0].from,o=n[n.length-1].to;return {parsedPos:r,advance(){let a=gi;if(a){for(let h of n)a.tempSkipped.push(h);t&&(a.scheduleOn=a.scheduleOn?Promise.all([a.scheduleOn,t]):t);}return this.parsedPos=o,new G(wt.none,[],[],o-r)},stoppedAt:null,stopAt(){}}}}}isDone(t){t=Math.min(t,this.state.doc.length);let e=this.fragments;return this.treeLen>=t&&e.length&&e[0].from==0&&e[0].to>=t}static get(){return gi}}function Nl(s,t,e){return fe.applyChanges(s,[{fromA:t,toA:e,fromB:t,toB:e}])}class oi{constructor(t){this.context=t,this.tree=t.tree;}apply(t){if(!t.docChanged&&this.tree==this.context.tree)return this;let e=this.context.changes(t.changes,t.state),i=this.context.treeLen==t.startState.doc.length?void 0:Math.max(t.changes.mapPos(this.context.treeLen),e.viewport.to);return e.work(20,i)||e.takeTree(),new oi(e)}static init(t){let e=Math.min(3e3,t.doc.length),i=ri.create(t.facet(Me).parser,t,{from:0,to:e});return i.work(20,e)||i.takeTree(),new oi(i)}}It.state=At.define({create:oi.init,update(s,t){for(let e of t.effects)if(e.is(It.setState))return e.value;return t.startState.facet(Me)!=t.state.facet(Me)?oi.init(t.state):s.apply(t)}});let tc=s=>{let t=setTimeout(()=>s(),500);return ()=>clearTimeout(t)};typeof requestIdleCallback<"u"&&(tc=s=>{let t=-1,e=setTimeout(()=>{t=requestIdleCallback(s,{timeout:400});},100);return ()=>t<0?clearTimeout(e):cancelIdleCallback(t)});const As=typeof navigator<"u"&&(!((Cs=navigator.scheduling)===null||Cs===void 0)&&Cs.isInputPending)?()=>navigator.scheduling.isInputPending():null,tm=ht.fromClass(class{constructor(t){this.view=t,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork();}update(t){let e=this.view.state.field(It.state).context;(e.updateViewport(t.view.viewport)||this.view.viewport.to>e.treeLen)&&this.scheduleWork(),(t.docChanged||t.selectionSet)&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(e);}scheduleWork(){if(this.working)return;let{state:t}=this.view,e=t.field(It.state);(e.tree!=e.context.tree||!e.context.isDone(t.doc.length))&&(this.working=tc(this.work));}work(t){this.working=null;let e=Date.now();if(this.chunkEnd<e&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=e+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:i,viewport:{to:n}}=this.view,r=i.field(It.state);if(r.tree==r.context.tree&&r.context.isDone(n+1e5))return;let o=Date.now()+Math.min(this.chunkBudget,100,t&&!As?Math.max(25,t.timeRemaining()-5):1e9),l=r.context.treeLen<n&&i.doc.length>n+1e3,a=r.context.work(()=>As&&As()||Date.now()>o,n+(l?0:1e5));this.chunkBudget-=Date.now()-e,(a||this.chunkBudget<=0)&&(r.context.takeTree(),this.view.dispatch({effects:It.setState.of(new oi(r.context))})),this.chunkBudget>0&&!(a&&!l)&&this.scheduleWork(),this.checkAsyncSchedule(r.context);}checkAsyncSchedule(t){t.scheduleOn&&(this.workScheduled++,t.scheduleOn.then(()=>this.scheduleWork()).catch(e=>St(this.view.state,e)).then(()=>this.workScheduled--),t.scheduleOn=null);}destroy(){this.working&&this.working();}isWorking(){return !!(this.working||this.workScheduled>0)}},{eventHandlers:{focus(){this.scheduleWork();}}}),Me=T.define({combine(s){return s.length?s[0]:null},enables:s=>[It.state,tm,O.contentAttributes.compute([s],t=>{let e=t.facet(s);return e&&e.name?{"data-language":e.name}:{}})]});class em{constructor(t,e=[]){this.language=t,this.support=e,this.extension=[t,e];}}class ec{constructor(t,e,i,n,r,o=void 0){this.name=t,this.alias=e,this.extensions=i,this.filename=n,this.loadFunc=r,this.support=o,this.loading=null;}load(){return this.loading||(this.loading=this.loadFunc().then(t=>this.support=t,t=>{throw this.loading=null,t}))}static of(t){let{load:e,support:i}=t;if(!e){if(!i)throw new RangeError("Must pass either 'load' or 'support' to LanguageDescription.of");e=()=>Promise.resolve(i);}return new ec(t.name,(t.alias||[]).concat(t.name).map(n=>n.toLowerCase()),t.extensions||[],t.filename,e,i)}static matchFilename(t,e){for(let n of t)if(n.filename&&n.filename.test(e))return n;let i=/\.([^.]+)$/.exec(e);if(i){for(let n of t)if(n.extensions.indexOf(i[1])>-1)return n}return null}static matchLanguageName(t,e,i=!0){e=e.toLowerCase();for(let n of t)if(n.alias.some(r=>r==e))return n;if(i)for(let n of t)for(let r of n.alias){let o=e.indexOf(r);if(o>-1&&(r.length>2||!/\w/.test(e[o-1])&&!/\w/.test(e[o+r.length])))return n}return null}}const im=T.define(),es=T.define({combine:s=>{if(!s.length)return "  ";let t=s[0];if(!t||/\S/.test(t)||Array.from(t).some(e=>e!=t[0]))throw new Error("Invalid indent unit: "+JSON.stringify(s[0]));return t}});function _e(s){let t=s.facet(es);return t.charCodeAt(0)==9?s.tabSize*t.length:t.length}function Hi(s,t){let e="",i=s.tabSize,n=s.facet(es)[0];if(n=="	"){for(;t>=i;)e+="	",t-=i;n=" ";}for(let r=0;r<t;r++)e+=n;return e}function to(s,t){s instanceof W&&(s=new is(s));for(let i of s.state.facet(im)){let n=i(s,t);if(n!==void 0)return n}let e=ct(s.state);return e.length>=t?nm(s,e,t):null}class is{constructor(t,e={}){this.state=t,this.options=e,this.unit=_e(t);}lineAt(t,e=1){let i=this.state.doc.lineAt(t),{simulateBreak:n,simulateDoubleBreak:r}=this.options;return n!=null&&n>=i.from&&n<=i.to?r&&n==t?{text:"",from:t}:(e<0?n<t:n<=t)?{text:i.text.slice(n-i.from),from:n}:{text:i.text.slice(0,n-i.from),from:i.from}:i}textAfterPos(t,e=1){if(this.options.simulateDoubleBreak&&t==this.options.simulateBreak)return "";let{text:i,from:n}=this.lineAt(t,e);return i.slice(t-n,Math.min(i.length,t+100-n))}column(t,e=1){let{text:i,from:n}=this.lineAt(t,e),r=this.countColumn(i,t-n),o=this.options.overrideIndentation?this.options.overrideIndentation(n):-1;return o>-1&&(r+=o-this.countColumn(i,i.search(/\S|$/))),r}countColumn(t,e=t.length){return ai(t,this.state.tabSize,e)}lineIndent(t,e=1){let{text:i,from:n}=this.lineAt(t,e),r=this.options.overrideIndentation;if(r){let o=r(n);if(o>-1)return o}return this.countColumn(i,i.search(/\S|$/))}get simulatedBreak(){return this.options.simulateBreak||null}}const ic=new N;function nm(s,t,e){let i=t.resolveStack(e),n=t.resolveInner(e,-1).resolve(e,0).enterUnfinishedNodesBefore(e);if(n!=i.node){let r=[];for(let o=n;o&&!(o.from==i.node.from&&o.type==i.node.type);o=o.parent)r.push(o);for(let o=r.length-1;o>=0;o--)i={node:r[o],next:i};}return nc(i,s,e)}function nc(s,t,e){for(let i=s;i;i=i.next){let n=rm(i.node);if(n)return n(eo.create(t,e,i))}return 0}function sm(s){return s.pos==s.options.simulateBreak&&s.options.simulateDoubleBreak}function rm(s){let t=s.type.prop(ic);if(t)return t;let e=s.firstChild,i;if(e&&(i=e.type.prop(N.closedBy))){let n=s.lastChild,r=n&&i.indexOf(n.name)>-1;return o=>sc(o,!0,1,void 0,r&&!sm(o)?n.from:void 0)}return s.parent==null?om:null}function om(){return 0}class eo extends is{constructor(t,e,i){super(t.state,t.options),this.base=t,this.pos=e,this.context=i;}get node(){return this.context.node}static create(t,e,i){return new eo(t,e,i)}get textAfter(){return this.textAfterPos(this.pos)}get baseIndent(){return this.baseIndentFor(this.node)}baseIndentFor(t){let e=this.state.doc.lineAt(t.from);for(;;){let i=t.resolve(e.from);for(;i.parent&&i.parent.from==i.from;)i=i.parent;if(lm(i,t))break;e=this.state.doc.lineAt(i.from);}return this.lineIndent(e.from)}continue(){return nc(this.context.next,this.base,this.pos)}}function lm(s,t){for(let e=t;e;e=e.parent)if(s==e)return !0;return !1}function am(s){let t=s.node,e=t.childAfter(t.from),i=t.lastChild;if(!e)return null;let n=s.options.simulateBreak,r=s.state.doc.lineAt(e.from),o=n==null||n<=r.from?r.to:Math.min(r.to,n);for(let l=e.to;;){let a=t.childAfter(l);if(!a||a==i)return null;if(!a.type.isSkipped){if(a.from>=o)return null;let h=/^ */.exec(r.text.slice(e.to-r.from))[0].length;return {from:e.from,to:e.to+h}}l=a.to;}}function Jy({closing:s,align:t=!0,units:e=1}){return i=>sc(i,t,e,s)}function sc(s,t,e,i,n){let r=s.textAfter,o=r.match(/^\s*/)[0].length,l=i&&r.slice(o,o+i.length)==i||n==s.pos+o,a=t?am(s):null;return a?l?s.column(a.from):s.column(a.to):s.baseIndent+(l?0:s.unit*e)}const Xy=s=>s.baseIndent;function Qy({except:s,units:t=1}={}){return e=>{let i=s&&s.test(e.textAfter);return e.baseIndent+(i?0:t*e.unit)}}const hm=200;function cm(){return W.transactionFilter.of(s=>{if(!s.docChanged||!s.isUserEvent("input.type")&&!s.isUserEvent("input.complete"))return s;let t=s.startState.languageDataAt("indentOnInput",s.startState.selection.main.head);if(!t.length)return s;let e=s.newDoc,{head:i}=s.newSelection.main,n=e.lineAt(i);if(i>n.from+hm)return s;let r=e.sliceString(n.from,i);if(!t.some(h=>h.test(r)))return s;let{state:o}=s,l=-1,a=[];for(let{head:h}of o.selection.ranges){let c=o.doc.lineAt(h);if(c.from==l)continue;l=c.from;let f=to(o,c.from);if(f==null)continue;let u=/^\s*/.exec(c.text)[0],d=Hi(o,f);u!=d&&a.push({from:c.from,to:c.from+u.length,insert:d});}return a.length?[s,{changes:a,sequential:!0}]:s})}const fm=T.define(),um=new N;function Zy(s){let t=s.firstChild,e=s.lastChild;return t&&t.to<e.from?{from:t.to,to:e.type.isError?s.to:e.from}:null}function dm(s,t,e){let i=ct(s);if(i.length<e)return null;let n=i.resolveStack(e,1),r=null;for(let o=n;o;o=o.next){let l=o.node;if(l.to<=e||l.from>e)continue;if(r&&l.from<t)break;let a=l.type.prop(um);if(a&&(l.to<i.length-50||i.length==s.doc.length||!pm(l))){let h=a(l,s);h&&h.from<=e&&h.from>=t&&h.to>e&&(r=h);}}return r}function pm(s){let t=s.lastChild;return t&&t.to==s.to&&t.type.isError}function Un(s,t,e){for(let i of s.facet(fm)){let n=i(s,t,e);if(n)return n}return dm(s,t,e)}function rc(s,t){let e=t.mapPos(s.from,1),i=t.mapPos(s.to,-1);return e>=i?void 0:{from:e,to:i}}const ns=R.define({map:rc}),Qi=R.define({map:rc});function oc(s){let t=[];for(let{head:e}of s.state.selection.ranges)t.some(i=>i.from<=e&&i.to>=e)||t.push(s.lineBlockAt(e));return t}const ze=At.define({create(){return I.none},update(s,t){s=s.map(t.changes);for(let e of t.effects)if(e.is(ns)&&!mm(s,e.value.from,e.value.to)){let{preparePlaceholder:i}=t.state.facet(hc),n=i?I.replace({widget:new km(i(t.state,e.value))}):Fl;s=s.update({add:[n.range(e.value.from,e.value.to)]});}else e.is(Qi)&&(s=s.update({filter:(i,n)=>e.value.from!=i||e.value.to!=n,filterFrom:e.value.from,filterTo:e.value.to}));if(t.selection){let e=!1,{head:i}=t.selection.main;s.between(i,i,(n,r)=>{n<i&&r>i&&(e=!0);}),e&&(s=s.update({filterFrom:i,filterTo:i,filter:(n,r)=>r<=i||n>=i}));}return s},provide:s=>O.decorations.from(s),toJSON(s,t){let e=[];return s.between(0,t.doc.length,(i,n)=>{e.push(i,n);}),e},fromJSON(s){if(!Array.isArray(s)||s.length%2)throw new RangeError("Invalid JSON for fold state");let t=[];for(let e=0;e<s.length;){let i=s[e++],n=s[e++];if(typeof i!="number"||typeof n!="number")throw new RangeError("Invalid JSON for fold state");t.push(Fl.range(i,n));}return I.set(t,!0)}});function Gn(s,t,e){var i;let n=null;return (i=s.field(ze,!1))===null||i===void 0||i.between(t,e,(r,o)=>{(!n||n.from>r)&&(n={from:r,to:o});}),n}function mm(s,t,e){let i=!1;return s.between(t,t,(n,r)=>{n==t&&r==e&&(i=!0);}),i}function lc(s,t){return s.field(ze,!1)?t:t.concat(R.appendConfig.of(cc()))}const gm=s=>{for(let t of oc(s)){let e=Un(s.state,t.from,t.to);if(e)return s.dispatch({effects:lc(s.state,[ns.of(e),ac(s,e)])}),!0}return !1},ym=s=>{if(!s.state.field(ze,!1))return !1;let t=[];for(let e of oc(s)){let i=Gn(s.state,e.from,e.to);i&&t.push(Qi.of(i),ac(s,i,!1));}return t.length&&s.dispatch({effects:t}),t.length>0};function ac(s,t,e=!0){let i=s.state.doc.lineAt(t.from).number,n=s.state.doc.lineAt(t.to).number;return O.announce.of(`${s.state.phrase(e?"Folded lines":"Unfolded lines")} ${i} ${s.state.phrase("to")} ${n}.`)}const bm=s=>{let{state:t}=s,e=[];for(let i=0;i<t.doc.length;){let n=s.lineBlockAt(i),r=Un(t,n.from,n.to);r&&e.push(ns.of(r)),i=(r?s.lineBlockAt(r.to):n).to+1;}return e.length&&s.dispatch({effects:lc(s.state,e)}),!!e.length},wm=s=>{let t=s.state.field(ze,!1);if(!t||!t.size)return !1;let e=[];return t.between(0,s.state.doc.length,(i,n)=>{e.push(Qi.of({from:i,to:n}));}),s.dispatch({effects:e}),!0},xm=[{key:"Ctrl-Shift-[",mac:"Cmd-Alt-[",run:gm},{key:"Ctrl-Shift-]",mac:"Cmd-Alt-]",run:ym},{key:"Ctrl-Alt-[",run:bm},{key:"Ctrl-Alt-]",run:wm}],vm={placeholderDOM:null,preparePlaceholder:null,placeholderText:"…"},hc=T.define({combine(s){return je(s,vm)}});function cc(s){return [ze,Am]}function fc(s,t){let{state:e}=s,i=e.facet(hc),n=o=>{let l=s.lineBlockAt(s.posAtDOM(o.target)),a=Gn(s.state,l.from,l.to);a&&s.dispatch({effects:Qi.of(a)}),o.preventDefault();};if(i.placeholderDOM)return i.placeholderDOM(s,n,t);let r=document.createElement("span");return r.textContent=i.placeholderText,r.setAttribute("aria-label",e.phrase("folded code")),r.title=e.phrase("unfold"),r.className="cm-foldPlaceholder",r.onclick=n,r}const Fl=I.replace({widget:new class extends oe{toDOM(s){return fc(s,null)}}});class km extends oe{constructor(t){super(),this.value=t;}eq(t){return this.value==t.value}toDOM(t){return fc(t,this.value)}}const Sm={openText:"⌄",closedText:"›",markerDOM:null,domEventHandlers:{},foldingChanged:()=>!1};class Ms extends Ce{constructor(t,e){super(),this.config=t,this.open=e;}eq(t){return this.config==t.config&&this.open==t.open}toDOM(t){if(this.config.markerDOM)return this.config.markerDOM(this.open);let e=document.createElement("span");return e.textContent=this.open?this.config.openText:this.config.closedText,e.title=t.state.phrase(this.open?"Fold line":"Unfold line"),e}}function Cm(s={}){let t=Object.assign(Object.assign({},Sm),s),e=new Ms(t,!0),i=new Ms(t,!1),n=ht.fromClass(class{constructor(o){this.from=o.viewport.from,this.markers=this.buildMarkers(o);}update(o){(o.docChanged||o.viewportChanged||o.startState.facet(Me)!=o.state.facet(Me)||o.startState.field(ze,!1)!=o.state.field(ze,!1)||ct(o.startState)!=ct(o.state)||t.foldingChanged(o))&&(this.markers=this.buildMarkers(o.view));}buildMarkers(o){let l=new xe;for(let a of o.viewportLineBlocks){let h=Gn(o.state,a.from,a.to)?i:Un(o.state,a.from,a.to)?e:null;h&&l.add(a.from,a.from,h);}return l.finish()}}),{domEventHandlers:r}=t;return [n,vp({class:"cm-foldGutter",markers(o){var l;return ((l=o.plugin(n))===null||l===void 0?void 0:l.markers)||z.empty},initialSpacer(){return new Ms(t,!1)},domEventHandlers:Object.assign(Object.assign({},r),{click:(o,l,a)=>{if(r.click&&r.click(o,l,a))return !0;let h=Gn(o.state,l.from,l.to);if(h)return o.dispatch({effects:Qi.of(h)}),!0;let c=Un(o.state,l.from,l.to);return c?(o.dispatch({effects:ns.of(c)}),!0):!1}})}),cc()]}const Am=O.baseTheme({".cm-foldPlaceholder":{backgroundColor:"#eee",border:"1px solid #ddd",color:"#888",borderRadius:".2em",margin:"0 1px",padding:"0 1px",cursor:"pointer"},".cm-foldGutter span":{padding:"0 1px",cursor:"pointer"}});class hi{constructor(t,e){this.specs=t;let i;function n(l){let a=ve.newName();return (i||(i=Object.create(null)))["."+a]=l,a}const r=typeof e.all=="string"?e.all:e.all?n(e.all):void 0,o=e.scope;this.scope=o instanceof It?l=>l.prop(Re)==o.data:o?l=>l==o:void 0,this.style=Zh(t.map(l=>({tag:l.tag,class:l.class||n(Object.assign({},l,{tag:null}))})),{all:r}).style,this.module=i?new ve(i):null,this.themeType=e.themeType;}static define(t,e){return new hi(t,e||{})}}const Dr=T.define(),uc=T.define({combine(s){return s.length?[s[0]]:null}});function Ds(s){let t=s.facet(Dr);return t.length?t:s.facet(uc)}function io(s,t){let e=[Dm],i;return s instanceof hi&&(s.module&&e.push(O.styleModule.of(s.module)),i=s.themeType),t?.fallback?e.push(uc.of(s)):i?e.push(Dr.computeN([O.darkTheme],n=>n.facet(O.darkTheme)==(i=="dark")?[s]:[])):e.push(Dr.of(s)),e}class Mm{constructor(t){this.markCache=Object.create(null),this.tree=ct(t.state),this.decorations=this.buildDeco(t,Ds(t.state)),this.decoratedTo=t.viewport.to;}update(t){let e=ct(t.state),i=Ds(t.state),n=i!=Ds(t.startState),{viewport:r}=t.view,o=t.changes.mapPos(this.decoratedTo,1);e.length<r.to&&!n&&e.type==this.tree.type&&o>=r.to?(this.decorations=this.decorations.map(t.changes),this.decoratedTo=o):(e!=this.tree||t.viewportChanged||n)&&(this.tree=e,this.decorations=this.buildDeco(t.view,i),this.decoratedTo=r.to);}buildDeco(t,e){if(!e||!this.tree.length)return I.none;let i=new xe;for(let{from:n,to:r}of t.visibleRanges)Jp(this.tree,e,(o,l,a)=>{i.add(o,l,this.markCache[a]||(this.markCache[a]=I.mark({class:a})));},n,r);return i.finish()}}const Dm=qe.high(ht.fromClass(Mm,{decorations:s=>s.decorations})),Tm=hi.define([{tag:m.meta,color:"#404740"},{tag:m.link,textDecoration:"underline"},{tag:m.heading,textDecoration:"underline",fontWeight:"bold"},{tag:m.emphasis,fontStyle:"italic"},{tag:m.strong,fontWeight:"bold"},{tag:m.strikethrough,textDecoration:"line-through"},{tag:m.keyword,color:"#708"},{tag:[m.atom,m.bool,m.url,m.contentSeparator,m.labelName],color:"#219"},{tag:[m.literal,m.inserted],color:"#164"},{tag:[m.string,m.deleted],color:"#a11"},{tag:[m.regexp,m.escape,m.special(m.string)],color:"#e40"},{tag:m.definition(m.variableName),color:"#00f"},{tag:m.local(m.variableName),color:"#30a"},{tag:[m.typeName,m.namespace],color:"#085"},{tag:m.className,color:"#167"},{tag:[m.special(m.variableName),m.macroName],color:"#256"},{tag:m.definition(m.propertyName),color:"#00c"},{tag:m.comment,color:"#940"},{tag:m.invalid,color:"#f00"}]),Om=1e4,Bm="()[]{}",Em=new N;function Tr(s,t,e){let i=s.prop(t<0?N.openedBy:N.closedBy);if(i)return i;if(s.name.length==1){let n=e.indexOf(s.name);if(n>-1&&n%2==(t<0?1:0))return [e[n+t]]}return null}function Or(s){let t=s.type.prop(Em);return t?t(s.node):s}function Ye(s,t,e,i={}){let n=i.maxScanDistance||Om,r=i.brackets||Bm,o=ct(s),l=o.resolveInner(t,e);for(let a=l;a;a=a.parent){let h=Tr(a.type,e,r);if(h&&a.from<a.to){let c=Or(a);if(c&&(e>0?t>=c.from&&t<c.to:t>c.from&&t<=c.to))return Pm(s,t,e,a,c,h,r)}}return Lm(s,t,e,o,l.type,n,r)}function Pm(s,t,e,i,n,r,o){let l=i.parent,a={from:n.from,to:n.to},h=0,c=l?.cursor();if(c&&(e<0?c.childBefore(i.from):c.childAfter(i.to)))do if(e<0?c.to<=i.from:c.from>=i.to){if(h==0&&r.indexOf(c.type.name)>-1&&c.from<c.to){let f=Or(c);return {start:a,end:f?{from:f.from,to:f.to}:void 0,matched:!0}}else if(Tr(c.type,e,o))h++;else if(Tr(c.type,-e,o)){if(h==0){let f=Or(c);return {start:a,end:f&&f.from<f.to?{from:f.from,to:f.to}:void 0,matched:!1}}h--;}}while(e<0?c.prevSibling():c.nextSibling());return {start:a,matched:!1}}function Lm(s,t,e,i,n,r,o){let l=e<0?s.sliceDoc(t-1,t):s.sliceDoc(t,t+1),a=o.indexOf(l);if(a<0||a%2==0!=e>0)return null;let h={from:e<0?t-1:t,to:e>0?t+1:t},c=s.doc.iterRange(t,e>0?s.doc.length:0),f=0;for(let u=0;!c.next().done&&u<=r;){let d=c.value;e<0&&(u+=d.length);let p=t+u*e;for(let y=e>0?0:d.length-1,g=e>0?d.length:-1;y!=g;y+=e){let b=o.indexOf(d[y]);if(!(b<0||i.resolveInner(p+y,1).type!=n))if(b%2==0==e>0)f++;else {if(f==1)return {start:h,end:{from:p+y,to:p+y+1},matched:b>>1==a>>1};f--;}}e>0&&(u+=d.length);}return c.done?{start:h,matched:!1}:null}function Hl(s,t,e,i=0,n=0){t==null&&(t=s.search(/[^\s\u00a0]/),t==-1&&(t=s.length));let r=n;for(let o=i;o<t;o++)s.charCodeAt(o)==9?r+=e-r%e:r++;return r}class dc{constructor(t,e,i,n){this.string=t,this.tabSize=e,this.indentUnit=i,this.overrideIndent=n,this.pos=0,this.start=0,this.lastColumnPos=0,this.lastColumnValue=0;}eol(){return this.pos>=this.string.length}sol(){return this.pos==0}peek(){return this.string.charAt(this.pos)||void 0}next(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)}eat(t){let e=this.string.charAt(this.pos),i;if(typeof t=="string"?i=e==t:i=e&&(t instanceof RegExp?t.test(e):t(e)),i)return ++this.pos,e}eatWhile(t){let e=this.pos;for(;this.eat(t););return this.pos>e}eatSpace(){let t=this.pos;for(;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>t}skipToEnd(){this.pos=this.string.length;}skipTo(t){let e=this.string.indexOf(t,this.pos);if(e>-1)return this.pos=e,!0}backUp(t){this.pos-=t;}column(){return this.lastColumnPos<this.start&&(this.lastColumnValue=Hl(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue}indentation(){var t;return (t=this.overrideIndent)!==null&&t!==void 0?t:Hl(this.string,null,this.tabSize)}match(t,e,i){if(typeof t=="string"){let n=o=>i?o.toLowerCase():o,r=this.string.substr(this.pos,t.length);return n(r)==n(t)?(e!==!1&&(this.pos+=t.length),!0):null}else {let n=this.string.slice(this.pos).match(t);return n&&n.index>0?null:(n&&e!==!1&&(this.pos+=n[0].length),n)}}current(){return this.string.slice(this.start,this.pos)}}function Rm(s){return {name:s.name||"",token:s.token,blankLine:s.blankLine||(()=>{}),startState:s.startState||(()=>!0),copyState:s.copyState||Im,indent:s.indent||(()=>null),languageData:s.languageData||{},tokenTable:s.tokenTable||so}}function Im(s){if(typeof s!="object")return s;let t={};for(let e in s){let i=s[e];t[e]=i instanceof Array?i.slice():i;}return t}const Vl=new WeakMap;class Et extends It{constructor(t){let e=$h(t.languageData),i=Rm(t),n,r=new class extends Xh{createParse(o,l,a){return new Fm(n,o,l,a)}};super(e,r,[],t.name),this.topNode=Wm(e,this),n=this,this.streamParser=i,this.stateAfter=new N({perNode:!0}),this.tokenTable=t.tokenTable?new yc(i.tokenTable):Vm;}static define(t){return new Et(t)}getIndent(t){let e,{overrideIndentation:i}=t.options;i&&(e=Vl.get(t.state),e!=null&&e<t.pos-1e4&&(e=void 0));let n=no(this,t.node.tree,t.node.from,t.node.from,e??t.pos),r,o;if(n?(o=n.state,r=n.pos+1):(o=this.streamParser.startState(t.unit),r=t.node.from),t.pos-r>1e4)return null;for(;r<t.pos;){let a=t.state.doc.lineAt(r),h=Math.min(t.pos,a.to);if(a.length){let c=i?i(a.from):-1,f=new dc(a.text,t.state.tabSize,t.unit,c<0?void 0:c);for(;f.pos<h-a.from;)mc(this.streamParser.token,f,o);}else this.streamParser.blankLine(o,t.unit);if(h==t.pos)break;r=a.to+1;}let l=t.lineAt(t.pos);return i&&e==null&&Vl.set(t.state,l.from),this.streamParser.indent(o,/^\s*(.*)/.exec(l.text)[1],t)}get allowsNesting(){return !1}}function no(s,t,e,i,n){let r=e>=i&&e+t.length<=n&&t.prop(s.stateAfter);if(r)return {state:s.streamParser.copyState(r),pos:e+t.length};for(let o=t.children.length-1;o>=0;o--){let l=t.children[o],a=e+t.positions[o],h=l instanceof G&&a<n&&no(s,l,a,i,n);if(h)return h}return null}function pc(s,t,e,i,n){if(n&&e<=0&&i>=t.length)return t;!n&&e==0&&t.type==s.topNode&&(n=!0);for(let r=t.children.length-1;r>=0;r--){let o=t.positions[r],l=t.children[r],a;if(o<i&&l instanceof G){if(!(a=pc(s,l,e-o,i-o,n)))break;return n?new G(t.type,t.children.slice(0,r).concat(a),t.positions.slice(0,r+1),o+a.length):a}}return null}function Nm(s,t,e,i,n){for(let r of t){let o=r.from+(r.openStart?25:0),l=r.to-(r.openEnd?25:0),a=o<=e&&l>e&&no(s,r.tree,0-r.offset,e,l),h;if(a&&a.pos<=i&&(h=pc(s,r.tree,e+r.offset,a.pos+r.offset,!1)))return {state:a.state,tree:h}}return {state:s.streamParser.startState(n?_e(n):4),tree:G.empty}}class Fm{constructor(t,e,i,n){this.lang=t,this.input=e,this.fragments=i,this.ranges=n,this.stoppedAt=null,this.chunks=[],this.chunkPos=[],this.chunk=[],this.chunkReused=void 0,this.rangeIndex=0,this.to=n[n.length-1].to;let r=ri.get(),o=n[0].from,{state:l,tree:a}=Nm(t,i,o,this.to,r?.state);this.state=l,this.parsedPos=this.chunkStart=o+a.length;for(let h=0;h<a.children.length;h++)this.chunks.push(a.children[h]),this.chunkPos.push(a.positions[h]);r&&this.parsedPos<r.viewport.from-1e5&&n.some(h=>h.from<=r.viewport.from&&h.to>=r.viewport.from)&&(this.state=this.lang.streamParser.startState(_e(r.state)),r.skipUntilInView(this.parsedPos,r.viewport.from),this.parsedPos=r.viewport.from),this.moveRangeIndex();}advance(){let t=ri.get(),e=this.stoppedAt==null?this.to:Math.min(this.to,this.stoppedAt),i=Math.min(e,this.chunkStart+2048);for(t&&(i=Math.min(i,t.viewport.to));this.parsedPos<i;)this.parseLine(t);return this.chunkStart<this.parsedPos&&this.finishChunk(),this.parsedPos>=e?this.finish():t&&this.parsedPos>=t.viewport.to?(t.skipUntilInView(this.parsedPos,e),this.finish()):null}stopAt(t){this.stoppedAt=t;}lineAfter(t){let e=this.input.chunk(t);if(this.input.lineChunks)e==`
`&&(e="");else {let i=e.indexOf(`
`);i>-1&&(e=e.slice(0,i));}return t+e.length<=this.to?e:e.slice(0,this.to-t)}nextLine(){let t=this.parsedPos,e=this.lineAfter(t),i=t+e.length;for(let n=this.rangeIndex;;){let r=this.ranges[n].to;if(r>=i||(e=e.slice(0,r-(i-e.length)),n++,n==this.ranges.length))break;let o=this.ranges[n].from,l=this.lineAfter(o);e+=l,i=o+l.length;}return {line:e,end:i}}skipGapsTo(t,e,i){for(;;){let n=this.ranges[this.rangeIndex].to,r=t+e;if(i>0?n>r:n>=r)break;let o=this.ranges[++this.rangeIndex].from;e+=o-n;}return e}moveRangeIndex(){for(;this.ranges[this.rangeIndex].to<this.parsedPos;)this.rangeIndex++;}emitToken(t,e,i,n){let r=4;if(this.ranges.length>1){n=this.skipGapsTo(e,n,1),e+=n;let l=this.chunk.length;n=this.skipGapsTo(i,n,-1),i+=n,r+=this.chunk.length-l;}let o=this.chunk.length-4;return r==4&&o>=0&&this.chunk[o]==t&&this.chunk[o+2]==e?this.chunk[o+2]=i:this.chunk.push(t,e,i,r),n}parseLine(t){let{line:e,end:i}=this.nextLine(),n=0,{streamParser:r}=this.lang,o=new dc(e,t?t.state.tabSize:4,t?_e(t.state):2);if(o.eol())r.blankLine(this.state,o.indentUnit);else for(;!o.eol();){let l=mc(r.token,o,this.state);if(l&&(n=this.emitToken(this.lang.tokenTable.resolve(l),this.parsedPos+o.start,this.parsedPos+o.pos,n)),o.start>1e4)break}this.parsedPos=i,this.moveRangeIndex(),this.parsedPos<this.to&&this.parsedPos++;}finishChunk(){let t=G.build({buffer:this.chunk,start:this.chunkStart,length:this.parsedPos-this.chunkStart,nodeSet:Hm,topID:0,maxBufferLength:2048,reused:this.chunkReused});t=new G(t.type,t.children,t.positions,t.length,[[this.lang.stateAfter,this.lang.streamParser.copyState(this.state)]]),this.chunks.push(t),this.chunkPos.push(this.chunkStart-this.ranges[0].from),this.chunk=[],this.chunkReused=void 0,this.chunkStart=this.parsedPos;}finish(){return new G(this.lang.topNode,this.chunks,this.chunkPos,this.parsedPos-this.ranges[0].from).balance()}}function mc(s,t,e){t.start=t.pos;for(let i=0;i<10;i++){let n=s(t,e);if(t.pos>t.start)return n}throw new Error("Stream parser failed to advance stream.")}const so=Object.create(null),Vi=[wt.none],Hm=new Xr(Vi),Wl=[],_l=Object.create(null),gc=Object.create(null);for(let[s,t]of [["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","tagName"],["attribute","attributeName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])gc[s]=bc(so,t);class yc{constructor(t){this.extra=t,this.table=Object.assign(Object.create(null),gc);}resolve(t){return t?this.table[t]||(this.table[t]=bc(this.extra,t)):0}}const Vm=new yc(so);function Ts(s,t){Wl.indexOf(s)>-1||(Wl.push(s),console.warn(t));}function bc(s,t){let e=[];for(let l of t.split(" ")){let a=[];for(let h of l.split(".")){let c=s[h]||m[h];c?typeof c=="function"?a.length?a=a.map(c):Ts(h,`Modifier ${h} used at start of tag`):a.length?Ts(h,`Tag ${h} used as modifier`):a=Array.isArray(c)?c:[c]:Ts(h,`Unknown highlighting tag ${h}`);}for(let h of a)e.push(h);}if(!e.length)return 0;let i=t.replace(/ /g,"_"),n=i+" "+e.map(l=>l.id),r=_l[n];if(r)return r.id;let o=_l[n]=wt.define({id:Vi.length,name:i,props:[Gp({[i]:e})]});return Vi.push(o),o.id}function Wm(s,t){let e=wt.define({id:Vi.length,name:"Document",props:[Re.add(()=>s),ic.add(()=>i=>t.getIndent(i))],top:!0});return Vi.push(e),e}J.RTL,J.LTR;const _m=s=>{let{state:t}=s,e=t.doc.lineAt(t.selection.main.from),i=oo(s.state,e.from);return i.line?zm(s):i.block?jm(s):!1};function ro(s,t){return ({state:e,dispatch:i})=>{if(e.readOnly)return !1;let n=s(t,e);return n?(i(e.update(n)),!0):!1}}const zm=ro(Gm,0),qm=ro(wc,0),jm=ro((s,t)=>wc(s,t,Um(t)),0);function oo(s,t){let e=s.languageDataAt("commentTokens",t);return e.length?e[0]:{}}const yi=50;function Km(s,{open:t,close:e},i,n){let r=s.sliceDoc(i-yi,i),o=s.sliceDoc(n,n+yi),l=/\s*$/.exec(r)[0].length,a=/^\s*/.exec(o)[0].length,h=r.length-l;if(r.slice(h-t.length,h)==t&&o.slice(a,a+e.length)==e)return {open:{pos:i-l,margin:l&&1},close:{pos:n+a,margin:a&&1}};let c,f;n-i<=2*yi?c=f=s.sliceDoc(i,n):(c=s.sliceDoc(i,i+yi),f=s.sliceDoc(n-yi,n));let u=/^\s*/.exec(c)[0].length,d=/\s*$/.exec(f)[0].length,p=f.length-d-e.length;return c.slice(u,u+t.length)==t&&f.slice(p,p+e.length)==e?{open:{pos:i+u+t.length,margin:/\s/.test(c.charAt(u+t.length))?1:0},close:{pos:n-d-e.length,margin:/\s/.test(f.charAt(p-1))?1:0}}:null}function Um(s){let t=[];for(let e of s.selection.ranges){let i=s.doc.lineAt(e.from),n=e.to<=i.to?i:s.doc.lineAt(e.to);n.from>i.from&&n.from==e.to&&(n=e.to==i.to+1?i:s.doc.lineAt(e.to-1));let r=t.length-1;r>=0&&t[r].to>i.from?t[r].to=n.to:t.push({from:i.from+/^\s*/.exec(i.text)[0].length,to:n.to});}return t}function wc(s,t,e=t.selection.ranges){let i=e.map(r=>oo(t,r.from).block);if(!i.every(r=>r))return null;let n=e.map((r,o)=>Km(t,i[o],r.from,r.to));if(s!=2&&!n.every(r=>r))return {changes:t.changes(e.map((r,o)=>n[o]?[]:[{from:r.from,insert:i[o].open+" "},{from:r.to,insert:" "+i[o].close}]))};if(s!=1&&n.some(r=>r)){let r=[];for(let o=0,l;o<n.length;o++)if(l=n[o]){let a=i[o],{open:h,close:c}=l;r.push({from:h.pos-a.open.length,to:h.pos+h.margin},{from:c.pos-c.margin,to:c.pos+a.close.length});}return {changes:r}}return null}function Gm(s,t,e=t.selection.ranges){let i=[],n=-1;for(let{from:r,to:o}of e){let l=i.length,a=1e9,h=oo(t,r).line;if(h){for(let c=r;c<=o;){let f=t.doc.lineAt(c);if(f.from>n&&(r==o||o>f.from)){n=f.from;let u=/^\s*/.exec(f.text)[0].length,d=u==f.length,p=f.text.slice(u,u+h.length)==h?u:-1;u<f.text.length&&u<a&&(a=u),i.push({line:f,comment:p,token:h,indent:u,empty:d,single:!1});}c=f.to+1;}if(a<1e9)for(let c=l;c<i.length;c++)i[c].indent<i[c].line.text.length&&(i[c].indent=a);i.length==l+1&&(i[l].single=!0);}}if(s!=2&&i.some(r=>r.comment<0&&(!r.empty||r.single))){let r=[];for(let{line:l,token:a,indent:h,empty:c,single:f}of i)(f||!c)&&r.push({from:l.from+h,insert:a+" "});let o=t.changes(r);return {changes:o,selection:t.selection.map(o,1)}}else if(s!=1&&i.some(r=>r.comment>=0)){let r=[];for(let{line:o,comment:l,token:a}of i)if(l>=0){let h=o.from+l,c=h+a.length;o.text[c-o.from]==" "&&c++,r.push({from:h,to:c});}return {changes:r}}return null}const Br=de.define(),Ym=de.define(),Jm=T.define(),xc=T.define({combine(s){return je(s,{minDepth:100,newGroupDelay:500,joinToEvent:(t,e)=>e},{minDepth:Math.max,newGroupDelay:Math.min,joinToEvent:(t,e)=>(i,n)=>t(i,n)||e(i,n)})}}),vc=At.define({create(){return ne.empty},update(s,t){let e=t.state.facet(xc),i=t.annotation(Br);if(i){let a=Ct.fromTransaction(t,i.selection),h=i.side,c=h==0?s.undone:s.done;return a?c=Yn(c,c.length,e.minDepth,a):c=Cc(c,t.startState.selection),new ne(h==0?i.rest:c,h==0?c:i.rest)}let n=t.annotation(Ym);if((n=="full"||n=="before")&&(s=s.isolate()),t.annotation(tt.addToHistory)===!1)return t.changes.empty?s:s.addMapping(t.changes.desc);let r=Ct.fromTransaction(t),o=t.annotation(tt.time),l=t.annotation(tt.userEvent);return r?s=s.addChanges(r,o,l,e,t):t.selection&&(s=s.addSelection(t.startState.selection,o,l,e.newGroupDelay)),(n=="full"||n=="after")&&(s=s.isolate()),s},toJSON(s){return {done:s.done.map(t=>t.toJSON()),undone:s.undone.map(t=>t.toJSON())}},fromJSON(s){return new ne(s.done.map(Ct.fromJSON),s.undone.map(Ct.fromJSON))}});function Xm(s={}){return [vc,xc.of(s),O.domEventHandlers({beforeinput(t,e){let i=t.inputType=="historyUndo"?kc:t.inputType=="historyRedo"?Er:null;return i?(t.preventDefault(),i(e)):!1}})]}function ss(s,t){return function({state:e,dispatch:i}){if(!t&&e.readOnly)return !1;let n=e.field(vc,!1);if(!n)return !1;let r=n.pop(s,e,t);return r?(i(r),!0):!1}}const kc=ss(0,!1),Er=ss(1,!1),Qm=ss(0,!0),Zm=ss(1,!0);class Ct{constructor(t,e,i,n,r){this.changes=t,this.effects=e,this.mapped=i,this.startSelection=n,this.selectionsAfter=r;}setSelAfter(t){return new Ct(this.changes,this.effects,this.mapped,this.startSelection,t)}toJSON(){var t,e,i;return {changes:(t=this.changes)===null||t===void 0?void 0:t.toJSON(),mapped:(e=this.mapped)===null||e===void 0?void 0:e.toJSON(),startSelection:(i=this.startSelection)===null||i===void 0?void 0:i.toJSON(),selectionsAfter:this.selectionsAfter.map(n=>n.toJSON())}}static fromJSON(t){return new Ct(t.changes&&it.fromJSON(t.changes),[],t.mapped&&se.fromJSON(t.mapped),t.startSelection&&v.fromJSON(t.startSelection),t.selectionsAfter.map(v.fromJSON))}static fromTransaction(t,e){let i=Nt;for(let n of t.startState.facet(Jm)){let r=n(t);r.length&&(i=i.concat(r));}return !i.length&&t.changes.empty?null:new Ct(t.changes.invert(t.startState.doc),i,void 0,e||t.startState.selection,Nt)}static selection(t){return new Ct(void 0,Nt,void 0,void 0,t)}}function Yn(s,t,e,i){let n=t+1>e+20?t-e-1:0,r=s.slice(n,t);return r.push(i),r}function $m(s,t){let e=[],i=!1;return s.iterChangedRanges((n,r)=>e.push(n,r)),t.iterChangedRanges((n,r,o,l)=>{for(let a=0;a<e.length;){let h=e[a++],c=e[a++];l>=h&&o<=c&&(i=!0);}}),i}function tg(s,t){return s.ranges.length==t.ranges.length&&s.ranges.filter((e,i)=>e.empty!=t.ranges[i].empty).length===0}function Sc(s,t){return s.length?t.length?s.concat(t):s:t}const Nt=[],eg=200;function Cc(s,t){if(s.length){let e=s[s.length-1],i=e.selectionsAfter.slice(Math.max(0,e.selectionsAfter.length-eg));return i.length&&i[i.length-1].eq(t)?s:(i.push(t),Yn(s,s.length-1,1e9,e.setSelAfter(i)))}else return [Ct.selection([t])]}function ig(s){let t=s[s.length-1],e=s.slice();return e[s.length-1]=t.setSelAfter(t.selectionsAfter.slice(0,t.selectionsAfter.length-1)),e}function Os(s,t){if(!s.length)return s;let e=s.length,i=Nt;for(;e;){let n=ng(s[e-1],t,i);if(n.changes&&!n.changes.empty||n.effects.length){let r=s.slice(0,e);return r[e-1]=n,r}else t=n.mapped,e--,i=n.selectionsAfter;}return i.length?[Ct.selection(i)]:Nt}function ng(s,t,e){let i=Sc(s.selectionsAfter.length?s.selectionsAfter.map(l=>l.map(t)):Nt,e);if(!s.changes)return Ct.selection(i);let n=s.changes.map(t),r=t.mapDesc(s.changes,!0),o=s.mapped?s.mapped.composeDesc(r):r;return new Ct(n,R.mapEffects(s.effects,t),o,s.startSelection.map(r),i)}const sg=/^(input\.type|delete)($|\.)/;class ne{constructor(t,e,i=0,n=void 0){this.done=t,this.undone=e,this.prevTime=i,this.prevUserEvent=n;}isolate(){return this.prevTime?new ne(this.done,this.undone):this}addChanges(t,e,i,n,r){let o=this.done,l=o[o.length-1];return l&&l.changes&&!l.changes.empty&&t.changes&&(!i||sg.test(i))&&(!l.selectionsAfter.length&&e-this.prevTime<n.newGroupDelay&&n.joinToEvent(r,$m(l.changes,t.changes))||i=="input.type.compose")?o=Yn(o,o.length-1,n.minDepth,new Ct(t.changes.compose(l.changes),Sc(R.mapEffects(t.effects,l.changes),l.effects),l.mapped,l.startSelection,Nt)):o=Yn(o,o.length,n.minDepth,t),new ne(o,Nt,e,i)}addSelection(t,e,i,n){let r=this.done.length?this.done[this.done.length-1].selectionsAfter:Nt;return r.length>0&&e-this.prevTime<n&&i==this.prevUserEvent&&i&&/^select($|\.)/.test(i)&&tg(r[r.length-1],t)?this:new ne(Cc(this.done,t),this.undone,e,i)}addMapping(t){return new ne(Os(this.done,t),Os(this.undone,t),this.prevTime,this.prevUserEvent)}pop(t,e,i){let n=t==0?this.done:this.undone;if(n.length==0)return null;let r=n[n.length-1],o=r.selectionsAfter[0]||e.selection;if(i&&r.selectionsAfter.length)return e.update({selection:r.selectionsAfter[r.selectionsAfter.length-1],annotations:Br.of({side:t,rest:ig(n),selection:o}),userEvent:t==0?"select.undo":"select.redo",scrollIntoView:!0});if(r.changes){let l=n.length==1?Nt:n.slice(0,n.length-1);return r.mapped&&(l=Os(l,r.mapped)),e.update({changes:r.changes,selection:r.startSelection,effects:r.effects,annotations:Br.of({side:t,rest:l,selection:o}),filter:!1,userEvent:t==0?"undo":"redo",scrollIntoView:!0})}else return null}}ne.empty=new ne(Nt,Nt);const rg=[{key:"Mod-z",run:kc,preventDefault:!0},{key:"Mod-y",mac:"Mod-Shift-z",run:Er,preventDefault:!0},{linux:"Ctrl-Shift-z",run:Er,preventDefault:!0},{key:"Mod-u",run:Qm,preventDefault:!0},{key:"Alt-u",mac:"Mod-Shift-u",run:Zm,preventDefault:!0}];function ci(s,t){return v.create(s.ranges.map(t),s.mainIndex)}function le(s,t){return s.update({selection:t,scrollIntoView:!0,userEvent:"select"})}function Ut({state:s,dispatch:t},e){let i=ci(s.selection,e);return i.eq(s.selection,!0)?!1:(t(le(s,i)),!0)}function rs(s,t){return v.cursor(t?s.to:s.from)}function Ac(s,t){return Ut(s,e=>e.empty?s.moveByChar(e,t):rs(e,t))}function pt(s){return s.textDirectionAt(s.state.selection.main.head)==J.LTR}const Mc=s=>Ac(s,!pt(s)),Dc=s=>Ac(s,pt(s));function Tc(s,t){return Ut(s,e=>e.empty?s.moveByGroup(e,t):rs(e,t))}const og=s=>Tc(s,!pt(s)),lg=s=>Tc(s,pt(s));function ag(s,t,e){if(t.type.prop(e))return !0;let i=t.to-t.from;return i&&(i>2||/[^\s,.;:]/.test(s.sliceDoc(t.from,t.to)))||t.firstChild}function os(s,t,e){let i=ct(s).resolveInner(t.head),n=e?N.closedBy:N.openedBy;for(let a=t.head;;){let h=e?i.childAfter(a):i.childBefore(a);if(!h)break;ag(s,h,n)?i=h:a=e?h.to:h.from;}let r=i.type.prop(n),o,l;return r&&(o=e?Ye(s,i.from,1):Ye(s,i.to,-1))&&o.matched?l=e?o.end.to:o.end.from:l=e?i.to:i.from,v.cursor(l,e?-1:1)}const hg=s=>Ut(s,t=>os(s.state,t,!pt(s))),cg=s=>Ut(s,t=>os(s.state,t,pt(s)));function Oc(s,t){return Ut(s,e=>{if(!e.empty)return rs(e,t);let i=s.moveVertically(e,t);return i.head!=e.head?i:s.moveToLineBoundary(e,t)})}const Bc=s=>Oc(s,!1),Ec=s=>Oc(s,!0);function Pc(s){let t=s.scrollDOM.clientHeight<s.scrollDOM.scrollHeight-2,e=0,i=0,n;if(t){for(let r of s.state.facet(O.scrollMargins)){let o=r(s);o?.top&&(e=Math.max(o?.top,e)),o?.bottom&&(i=Math.max(o?.bottom,i));}n=s.scrollDOM.clientHeight-e-i;}else n=(s.dom.ownerDocument.defaultView||window).innerHeight;return {marginTop:e,marginBottom:i,selfScroll:t,height:Math.max(s.defaultLineHeight,n-5)}}function Lc(s,t){let e=Pc(s),{state:i}=s,n=ci(i.selection,o=>o.empty?s.moveVertically(o,t,e.height):rs(o,t));if(n.eq(i.selection))return !1;let r;if(e.selfScroll){let o=s.coordsAtPos(i.selection.main.head),l=s.scrollDOM.getBoundingClientRect(),a=l.top+e.marginTop,h=l.bottom-e.marginBottom;o&&o.top>a&&o.bottom<h&&(r=O.scrollIntoView(n.main.head,{y:"start",yMargin:o.top-a}));}return s.dispatch(le(i,n),{effects:r}),!0}const zl=s=>Lc(s,!1),Pr=s=>Lc(s,!0);function De(s,t,e){let i=s.lineBlockAt(t.head),n=s.moveToLineBoundary(t,e);if(n.head==t.head&&n.head!=(e?i.to:i.from)&&(n=s.moveToLineBoundary(t,e,!1)),!e&&n.head==i.from&&i.length){let r=/^\s*/.exec(s.state.sliceDoc(i.from,Math.min(i.from+100,i.to)))[0].length;r&&t.head!=i.from+r&&(n=v.cursor(i.from+r));}return n}const fg=s=>Ut(s,t=>De(s,t,!0)),ug=s=>Ut(s,t=>De(s,t,!1)),dg=s=>Ut(s,t=>De(s,t,!pt(s))),pg=s=>Ut(s,t=>De(s,t,pt(s))),mg=s=>Ut(s,t=>v.cursor(s.lineBlockAt(t.head).from,1)),gg=s=>Ut(s,t=>v.cursor(s.lineBlockAt(t.head).to,-1));function yg(s,t,e){let i=!1,n=ci(s.selection,r=>{let o=Ye(s,r.head,-1)||Ye(s,r.head,1)||r.head>0&&Ye(s,r.head-1,1)||r.head<s.doc.length&&Ye(s,r.head+1,-1);if(!o||!o.end)return r;i=!0;let l=o.start.from==r.head?o.end.to:o.end.from;return v.cursor(l)});return i?(t(le(s,n)),!0):!1}const bg=({state:s,dispatch:t})=>yg(s,t);function Wt(s,t){let e=ci(s.state.selection,i=>{let n=t(i);return v.range(i.anchor,n.head,n.goalColumn,n.bidiLevel||void 0)});return e.eq(s.state.selection)?!1:(s.dispatch(le(s.state,e)),!0)}function Rc(s,t){return Wt(s,e=>s.moveByChar(e,t))}const Ic=s=>Rc(s,!pt(s)),Nc=s=>Rc(s,pt(s));function Fc(s,t){return Wt(s,e=>s.moveByGroup(e,t))}const wg=s=>Fc(s,!pt(s)),xg=s=>Fc(s,pt(s)),vg=s=>Wt(s,t=>os(s.state,t,!pt(s))),kg=s=>Wt(s,t=>os(s.state,t,pt(s)));function Hc(s,t){return Wt(s,e=>s.moveVertically(e,t))}const Vc=s=>Hc(s,!1),Wc=s=>Hc(s,!0);function _c(s,t){return Wt(s,e=>s.moveVertically(e,t,Pc(s).height))}const ql=s=>_c(s,!1),jl=s=>_c(s,!0),Sg=s=>Wt(s,t=>De(s,t,!0)),Cg=s=>Wt(s,t=>De(s,t,!1)),Ag=s=>Wt(s,t=>De(s,t,!pt(s))),Mg=s=>Wt(s,t=>De(s,t,pt(s))),Dg=s=>Wt(s,t=>v.cursor(s.lineBlockAt(t.head).from)),Tg=s=>Wt(s,t=>v.cursor(s.lineBlockAt(t.head).to)),Kl=({state:s,dispatch:t})=>(t(le(s,{anchor:0})),!0),Ul=({state:s,dispatch:t})=>(t(le(s,{anchor:s.doc.length})),!0),Gl=({state:s,dispatch:t})=>(t(le(s,{anchor:s.selection.main.anchor,head:0})),!0),Yl=({state:s,dispatch:t})=>(t(le(s,{anchor:s.selection.main.anchor,head:s.doc.length})),!0),Og=({state:s,dispatch:t})=>(t(s.update({selection:{anchor:0,head:s.doc.length},userEvent:"select"})),!0),Bg=({state:s,dispatch:t})=>{let e=ls(s).map(({from:i,to:n})=>v.range(i,Math.min(n+1,s.doc.length)));return t(s.update({selection:v.create(e),userEvent:"select"})),!0},Eg=({state:s,dispatch:t})=>{let e=ci(s.selection,i=>{let n=ct(s),r=n.resolveStack(i.from,1);if(i.empty){let o=n.resolveStack(i.from,-1);o.node.from>=r.node.from&&o.node.to<=r.node.to&&(r=o);}for(let o=r;o;o=o.next){let{node:l}=o;if((l.from<i.from&&l.to>=i.to||l.to>i.to&&l.from<=i.from)&&o.next)return v.range(l.to,l.from)}return i});return e.eq(s.selection)?!1:(t(le(s,e)),!0)},Pg=({state:s,dispatch:t})=>{let e=s.selection,i=null;return e.ranges.length>1?i=v.create([e.main]):e.main.empty||(i=v.create([v.cursor(e.main.head)])),i?(t(le(s,i)),!0):!1};function Zi(s,t){if(s.state.readOnly)return !1;let e="delete.selection",{state:i}=s,n=i.changeByRange(r=>{let{from:o,to:l}=r;if(o==l){let a=t(r);a<o?(e="delete.backward",a=xn(s,a,!1)):a>o&&(e="delete.forward",a=xn(s,a,!0)),o=Math.min(o,a),l=Math.max(l,a);}else o=xn(s,o,!1),l=xn(s,l,!0);return o==l?{range:r}:{changes:{from:o,to:l},range:v.cursor(o,o<r.head?-1:1)}});return n.changes.empty?!1:(s.dispatch(i.update(n,{scrollIntoView:!0,userEvent:e,effects:e=="delete.selection"?O.announce.of(i.phrase("Selection deleted")):void 0})),!0)}function xn(s,t,e){if(s instanceof O)for(let i of s.state.facet(O.atomicRanges).map(n=>n(s)))i.between(t,t,(n,r)=>{n<t&&r>t&&(t=e?r:n);});return t}const zc=(s,t,e)=>Zi(s,i=>{let n=i.from,{state:r}=s,o=r.doc.lineAt(n),l,a;if(e&&!t&&n>o.from&&n<o.from+200&&!/[^ \t]/.test(l=o.text.slice(0,n-o.from))){if(l[l.length-1]=="	")return n-1;let h=ai(l,r.tabSize),c=h%_e(r)||_e(r);for(let f=0;f<c&&l[l.length-1-f]==" ";f++)n--;a=n;}else a=gt(o.text,n-o.from,t,t)+o.from,a==n&&o.number!=(t?r.doc.lines:1)?a+=t?1:-1:!t&&/[\ufe00-\ufe0f]/.test(o.text.slice(a-o.from,n-o.from))&&(a=gt(o.text,a-o.from,!1,!1)+o.from);return a}),Lr=s=>zc(s,!1,!0),qc=s=>zc(s,!0,!1),jc=(s,t)=>Zi(s,e=>{let i=e.head,{state:n}=s,r=n.doc.lineAt(i),o=n.charCategorizer(i);for(let l=null;;){if(i==(t?r.to:r.from)){i==e.head&&r.number!=(t?n.doc.lines:1)&&(i+=t?1:-1);break}let a=gt(r.text,i-r.from,t)+r.from,h=r.text.slice(Math.min(i,a)-r.from,Math.max(i,a)-r.from),c=o(h);if(l!=null&&c!=l)break;(h!=" "||i!=e.head)&&(l=c),i=a;}return i}),Kc=s=>jc(s,!1),Lg=s=>jc(s,!0),Rg=s=>Zi(s,t=>{let e=s.lineBlockAt(t.head).to;return t.head<e?e:Math.min(s.state.doc.length,t.head+1)}),Ig=s=>Zi(s,t=>{let e=s.moveToLineBoundary(t,!1).head;return t.head>e?e:Math.max(0,t.head-1)}),Ng=s=>Zi(s,t=>{let e=s.moveToLineBoundary(t,!0).head;return t.head<e?e:Math.min(s.state.doc.length,t.head+1)}),Fg=({state:s,dispatch:t})=>{if(s.readOnly)return !1;let e=s.changeByRange(i=>({changes:{from:i.from,to:i.to,insert:H.of(["",""])},range:v.cursor(i.from)}));return t(s.update(e,{scrollIntoView:!0,userEvent:"input"})),!0},Hg=({state:s,dispatch:t})=>{if(s.readOnly)return !1;let e=s.changeByRange(i=>{if(!i.empty||i.from==0||i.from==s.doc.length)return {range:i};let n=i.from,r=s.doc.lineAt(n),o=n==r.from?n-1:gt(r.text,n-r.from,!1)+r.from,l=n==r.to?n+1:gt(r.text,n-r.from,!0)+r.from;return {changes:{from:o,to:l,insert:s.doc.slice(n,l).append(s.doc.slice(o,n))},range:v.cursor(l)}});return e.changes.empty?!1:(t(s.update(e,{scrollIntoView:!0,userEvent:"move.character"})),!0)};function ls(s){let t=[],e=-1;for(let i of s.selection.ranges){let n=s.doc.lineAt(i.from),r=s.doc.lineAt(i.to);if(!i.empty&&i.to==r.from&&(r=s.doc.lineAt(i.to-1)),e>=n.number){let o=t[t.length-1];o.to=r.to,o.ranges.push(i);}else t.push({from:n.from,to:r.to,ranges:[i]});e=r.number+1;}return t}function Uc(s,t,e){if(s.readOnly)return !1;let i=[],n=[];for(let r of ls(s)){if(e?r.to==s.doc.length:r.from==0)continue;let o=s.doc.lineAt(e?r.to+1:r.from-1),l=o.length+1;if(e){i.push({from:r.to,to:o.to},{from:r.from,insert:o.text+s.lineBreak});for(let a of r.ranges)n.push(v.range(Math.min(s.doc.length,a.anchor+l),Math.min(s.doc.length,a.head+l)));}else {i.push({from:o.from,to:r.from},{from:r.to,insert:s.lineBreak+o.text});for(let a of r.ranges)n.push(v.range(a.anchor-l,a.head-l));}}return i.length?(t(s.update({changes:i,scrollIntoView:!0,selection:v.create(n,s.selection.mainIndex),userEvent:"move.line"})),!0):!1}const Vg=({state:s,dispatch:t})=>Uc(s,t,!1),Wg=({state:s,dispatch:t})=>Uc(s,t,!0);function Gc(s,t,e){if(s.readOnly)return !1;let i=[];for(let n of ls(s))e?i.push({from:n.from,insert:s.doc.slice(n.from,n.to)+s.lineBreak}):i.push({from:n.to,insert:s.lineBreak+s.doc.slice(n.from,n.to)});return t(s.update({changes:i,scrollIntoView:!0,userEvent:"input.copyline"})),!0}const _g=({state:s,dispatch:t})=>Gc(s,t,!1),zg=({state:s,dispatch:t})=>Gc(s,t,!0),qg=s=>{if(s.state.readOnly)return !1;let{state:t}=s,e=t.changes(ls(t).map(({from:n,to:r})=>(n>0?n--:r<t.doc.length&&r++,{from:n,to:r}))),i=ci(t.selection,n=>{let r;if(s.lineWrapping){let o=s.lineBlockAt(n.head),l=s.coordsAtPos(n.head,n.assoc||1);l&&(r=o.bottom+s.documentTop-l.bottom+s.defaultLineHeight/2);}return s.moveVertically(n,!0,r)}).map(e);return s.dispatch({changes:e,selection:i,scrollIntoView:!0,userEvent:"delete.line"}),!0};function jg(s,t){if(/\(\)|\[\]|\{\}/.test(s.sliceDoc(t-1,t+1)))return {from:t,to:t};let e=ct(s).resolveInner(t),i=e.childBefore(t),n=e.childAfter(t),r;return i&&n&&i.to<=t&&n.from>=t&&(r=i.type.prop(N.closedBy))&&r.indexOf(n.name)>-1&&s.doc.lineAt(i.to).from==s.doc.lineAt(n.from).from&&!/\S/.test(s.sliceDoc(i.to,n.from))?{from:i.to,to:n.from}:null}const Jl=Yc(!1),Kg=Yc(!0);function Yc(s){return ({state:t,dispatch:e})=>{if(t.readOnly)return !1;let i=t.changeByRange(n=>{let{from:r,to:o}=n,l=t.doc.lineAt(r),a=!s&&r==o&&jg(t,r);s&&(r=o=(o<=l.to?l:t.doc.lineAt(o)).to);let h=new is(t,{simulateBreak:r,simulateDoubleBreak:!!a}),c=to(h,r);for(c==null&&(c=ai(/^\s*/.exec(t.doc.lineAt(r).text)[0],t.tabSize));o<l.to&&/\s/.test(l.text[o-l.from]);)o++;a?{from:r,to:o}=a:r>l.from&&r<l.from+100&&!/\S/.test(l.text.slice(0,r))&&(r=l.from);let f=["",Hi(t,c)];return a&&f.push(Hi(t,h.lineIndent(l.from,-1))),{changes:{from:r,to:o,insert:H.of(f)},range:v.cursor(r+1+f[1].length)}});return e(t.update(i,{scrollIntoView:!0,userEvent:"input"})),!0}}function lo(s,t){let e=-1;return s.changeByRange(i=>{let n=[];for(let o=i.from;o<=i.to;){let l=s.doc.lineAt(o);l.number>e&&(i.empty||i.to>l.from)&&(t(l,n,i),e=l.number),o=l.to+1;}let r=s.changes(n);return {changes:n,range:v.range(r.mapPos(i.anchor,1),r.mapPos(i.head,1))}})}const Ug=({state:s,dispatch:t})=>{if(s.readOnly)return !1;let e=Object.create(null),i=new is(s,{overrideIndentation:r=>{let o=e[r];return o??-1}}),n=lo(s,(r,o,l)=>{let a=to(i,r.from);if(a==null)return;/\S/.test(r.text)||(a=0);let h=/^\s*/.exec(r.text)[0],c=Hi(s,a);(h!=c||l.from<r.from+h.length)&&(e[r.from]=a,o.push({from:r.from,to:r.from+h.length,insert:c}));});return n.changes.empty||t(s.update(n,{userEvent:"indent"})),!0},Jc=({state:s,dispatch:t})=>s.readOnly?!1:(t(s.update(lo(s,(e,i)=>{i.push({from:e.from,insert:s.facet(es)});}),{userEvent:"input.indent"})),!0),Xc=({state:s,dispatch:t})=>s.readOnly?!1:(t(s.update(lo(s,(e,i)=>{let n=/^\s*/.exec(e.text)[0];if(!n)return;let r=ai(n,s.tabSize),o=0,l=Hi(s,Math.max(0,r-_e(s)));for(;o<n.length&&o<l.length&&n.charCodeAt(o)==l.charCodeAt(o);)o++;i.push({from:e.from+o,to:e.from+n.length,insert:l.slice(o)});}),{userEvent:"delete.dedent"})),!0),Gg=s=>(s.setTabFocusMode(),!0),Yg=[{key:"Ctrl-b",run:Mc,shift:Ic,preventDefault:!0},{key:"Ctrl-f",run:Dc,shift:Nc},{key:"Ctrl-p",run:Bc,shift:Vc},{key:"Ctrl-n",run:Ec,shift:Wc},{key:"Ctrl-a",run:mg,shift:Dg},{key:"Ctrl-e",run:gg,shift:Tg},{key:"Ctrl-d",run:qc},{key:"Ctrl-h",run:Lr},{key:"Ctrl-k",run:Rg},{key:"Ctrl-Alt-h",run:Kc},{key:"Ctrl-o",run:Fg},{key:"Ctrl-t",run:Hg},{key:"Ctrl-v",run:Pr}],Jg=[{key:"ArrowLeft",run:Mc,shift:Ic,preventDefault:!0},{key:"Mod-ArrowLeft",mac:"Alt-ArrowLeft",run:og,shift:wg,preventDefault:!0},{mac:"Cmd-ArrowLeft",run:dg,shift:Ag,preventDefault:!0},{key:"ArrowRight",run:Dc,shift:Nc,preventDefault:!0},{key:"Mod-ArrowRight",mac:"Alt-ArrowRight",run:lg,shift:xg,preventDefault:!0},{mac:"Cmd-ArrowRight",run:pg,shift:Mg,preventDefault:!0},{key:"ArrowUp",run:Bc,shift:Vc,preventDefault:!0},{mac:"Cmd-ArrowUp",run:Kl,shift:Gl},{mac:"Ctrl-ArrowUp",run:zl,shift:ql},{key:"ArrowDown",run:Ec,shift:Wc,preventDefault:!0},{mac:"Cmd-ArrowDown",run:Ul,shift:Yl},{mac:"Ctrl-ArrowDown",run:Pr,shift:jl},{key:"PageUp",run:zl,shift:ql},{key:"PageDown",run:Pr,shift:jl},{key:"Home",run:ug,shift:Cg,preventDefault:!0},{key:"Mod-Home",run:Kl,shift:Gl},{key:"End",run:fg,shift:Sg,preventDefault:!0},{key:"Mod-End",run:Ul,shift:Yl},{key:"Enter",run:Jl,shift:Jl},{key:"Mod-a",run:Og},{key:"Backspace",run:Lr,shift:Lr},{key:"Delete",run:qc},{key:"Mod-Backspace",mac:"Alt-Backspace",run:Kc},{key:"Mod-Delete",mac:"Alt-Delete",run:Lg},{mac:"Mod-Backspace",run:Ig},{mac:"Mod-Delete",run:Ng}].concat(Yg.map(s=>({mac:s.key,run:s.run,shift:s.shift}))),Xg=[{key:"Alt-ArrowLeft",mac:"Ctrl-ArrowLeft",run:hg,shift:vg},{key:"Alt-ArrowRight",mac:"Ctrl-ArrowRight",run:cg,shift:kg},{key:"Alt-ArrowUp",run:Vg},{key:"Shift-Alt-ArrowUp",run:_g},{key:"Alt-ArrowDown",run:Wg},{key:"Shift-Alt-ArrowDown",run:zg},{key:"Escape",run:Pg},{key:"Mod-Enter",run:Kg},{key:"Alt-l",mac:"Ctrl-l",run:Bg},{key:"Mod-i",run:Eg,preventDefault:!0},{key:"Mod-[",run:Xc},{key:"Mod-]",run:Jc},{key:"Mod-Alt-\\",run:Ug},{key:"Shift-Mod-k",run:qg},{key:"Shift-Mod-\\",run:bg},{key:"Mod-/",run:_m},{key:"Alt-A",run:qm},{key:"Ctrl-m",mac:"Shift-Alt-m",run:Gg}].concat(Jg);class Qc{constructor(t,e,i,n){this.state=t,this.pos=e,this.explicit=i,this.view=n,this.abortListeners=[],this.abortOnDocChange=!1;}tokenBefore(t){let e=ct(this.state).resolveInner(this.pos,-1);for(;e&&t.indexOf(e.name)<0;)e=e.parent;return e?{from:e.from,to:this.pos,text:this.state.sliceDoc(e.from,this.pos),type:e.type}:null}matchBefore(t){let e=this.state.doc.lineAt(this.pos),i=Math.max(e.from,this.pos-250),n=e.text.slice(i-e.from,this.pos-e.from),r=n.search(Zc(t,!1));return r<0?null:{from:i+r,to:this.pos,text:n.slice(r)}}get aborted(){return this.abortListeners==null}addEventListener(t,e,i){t=="abort"&&this.abortListeners&&(this.abortListeners.push(e),i&&i.onDocChange&&(this.abortOnDocChange=!0));}}function Xl(s){let t=Object.keys(s).join(""),e=/\w/.test(t);return e&&(t=t.replace(/\w/g,"")),`[${e?"\\w":""}${t.replace(/[^\w\s]/g,"\\$&")}]`}function Zg(s){let t=Object.create(null),e=Object.create(null);for(let{label:n}of s){t[n[0]]=!0;for(let r=1;r<n.length;r++)e[n[r]]=!0;}let i=Xl(t)+Xl(e)+"*$";return [new RegExp("^"+i),new RegExp(i)]}function $g(s){let t=s.map(n=>typeof n=="string"?{label:n}:n),[e,i]=t.every(n=>/^\w+$/.test(n.label))?[/\w*$/,/\w+$/]:Zg(t);return n=>{let r=n.matchBefore(i);return r||n.explicit?{from:r?r.from:n.pos,options:t,validFor:e}:null}}function $y(s,t){return e=>{for(let i=ct(e.state).resolveInner(e.pos,-1);i;i=i.parent){if(s.indexOf(i.name)>-1)return null;if(i.type.isTop)break}return t(e)}}class Ql{constructor(t,e,i,n){this.completion=t,this.source=e,this.match=i,this.score=n;}}function Ne(s){return s.selection.main.from}function Zc(s,t){var e;let{source:i}=s,n=t&&i[0]!="^",r=i[i.length-1]!="$";return !n&&!r?s:new RegExp(`${n?"^":""}(?:${i})${r?"$":""}`,(e=s.flags)!==null&&e!==void 0?e:s.ignoreCase?"i":"")}const ao=de.define();function t0(s,t,e,i){let{main:n}=s.selection,r=e-n.from,o=i-n.from;return Object.assign(Object.assign({},s.changeByRange(l=>{if(l!=n&&e!=i&&s.sliceDoc(l.from+r,l.from+o)!=s.sliceDoc(e,i))return {range:l};let a=s.toText(t);return {changes:{from:l.from+r,to:i==n.from?l.to:l.from+o,insert:a},range:v.cursor(l.from+r+a.length)}})),{scrollIntoView:!0,userEvent:"input.complete"})}const Zl=new WeakMap;function e0(s){if(!Array.isArray(s))return s;let t=Zl.get(s);return t||Zl.set(s,t=$g(s)),t}const Jn=R.define(),Wi=R.define();class i0{constructor(t){this.pattern=t,this.chars=[],this.folded=[],this.any=[],this.precise=[],this.byWord=[],this.score=0,this.matched=[];for(let e=0;e<t.length;){let i=Tt(t,e),n=ae(i);this.chars.push(i);let r=t.slice(e,e+n),o=r.toUpperCase();this.folded.push(Tt(o==r?r.toLowerCase():o,0)),e+=n;}this.astral=t.length!=this.chars.length;}ret(t,e){return this.score=t,this.matched=e,this}match(t){if(this.pattern.length==0)return this.ret(-100,[]);if(t.length<this.pattern.length)return null;let{chars:e,folded:i,any:n,precise:r,byWord:o}=this;if(e.length==1){let x=Tt(t,0),k=ae(x),S=k==t.length?0:-100;if(x!=e[0])if(x==i[0])S+=-200;else return null;return this.ret(S,[0,k])}let l=t.indexOf(this.pattern);if(l==0)return this.ret(t.length==this.pattern.length?0:-100,[0,this.pattern.length]);let a=e.length,h=0;if(l<0){for(let x=0,k=Math.min(t.length,200);x<k&&h<a;){let S=Tt(t,x);(S==e[h]||S==i[h])&&(n[h++]=x),x+=ae(S);}if(h<a)return null}let c=0,f=0,u=!1,d=0,p=-1,y=-1,g=/[a-z]/.test(t),b=!0;for(let x=0,k=Math.min(t.length,200),S=0;x<k&&f<a;){let w=Tt(t,x);l<0&&(c<a&&w==e[c]&&(r[c++]=x),d<a&&(w==e[d]||w==i[d]?(d==0&&(p=x),y=x+1,d++):d=0));let C,A=w<255?w>=48&&w<=57||w>=97&&w<=122?2:w>=65&&w<=90?1:0:(C=Oa(w))!=C.toLowerCase()?1:C!=C.toUpperCase()?2:0;(!x||A==1&&g||S==0&&A!=0)&&(e[f]==w||i[f]==w&&(u=!0)?o[f++]=x:o.length&&(b=!1)),S=A,x+=ae(w);}return f==a&&o[0]==0&&b?this.result(-100+(u?-200:0),o,t):d==a&&p==0?this.ret(-200-t.length+(y==t.length?0:-100),[0,y]):l>-1?this.ret(-700-t.length,[l,l+this.pattern.length]):d==a?this.ret(-900-t.length,[p,y]):f==a?this.result(-100+(u?-200:0)+-700+(b?0:-1100),o,t):e.length==2?null:this.result((n[0]?-700:0)+-200+-1100,n,t)}result(t,e,i){let n=[],r=0;for(let o of e){let l=o+(this.astral?ae(Tt(i,o)):1);r&&n[r-1]==o?n[r-1]=l:(n[r++]=o,n[r++]=l);}return this.ret(t-i.length,n)}}class n0{constructor(t){this.pattern=t,this.matched=[],this.score=0,this.folded=t.toLowerCase();}match(t){if(t.length<this.pattern.length)return null;let e=t.slice(0,this.pattern.length),i=e==this.pattern?0:e.toLowerCase()==this.folded?-200:null;return i==null?null:(this.matched=[0,e.length],this.score=i+(t.length==this.pattern.length?0:-100),this)}}const nt=T.define({combine(s){return je(s,{activateOnTyping:!0,activateOnCompletion:()=>!1,activateOnTypingDelay:100,selectOnOpen:!0,override:null,closeOnBlur:!0,maxRenderedOptions:100,defaultKeymap:!0,tooltipClass:()=>"",optionClass:()=>"",aboveCursor:!1,icons:!0,addToOptions:[],positionInfo:s0,filterStrict:!1,compareCompletions:(t,e)=>t.label.localeCompare(e.label),interactionDelay:75,updateSyncTime:100},{defaultKeymap:(t,e)=>t&&e,closeOnBlur:(t,e)=>t&&e,icons:(t,e)=>t&&e,tooltipClass:(t,e)=>i=>$l(t(i),e(i)),optionClass:(t,e)=>i=>$l(t(i),e(i)),addToOptions:(t,e)=>t.concat(e),filterStrict:(t,e)=>t||e})}});function $l(s,t){return s?t?s+" "+t:s:t}function s0(s,t,e,i,n,r){let o=s.textDirection==J.RTL,l=o,a=!1,h="top",c,f,u=t.left-n.left,d=n.right-t.right,p=i.right-i.left,y=i.bottom-i.top;if(l&&u<Math.min(p,d)?l=!1:!l&&d<Math.min(p,u)&&(l=!0),p<=(l?u:d))c=Math.max(n.top,Math.min(e.top,n.bottom-y))-t.top,f=Math.min(400,l?u:d);else {a=!0,f=Math.min(400,(o?t.right:n.right-t.left)-30);let x=n.bottom-t.bottom;x>=y||x>t.top?c=e.bottom-t.top:(h="bottom",c=t.bottom-e.top);}let g=(t.bottom-t.top)/r.offsetHeight,b=(t.right-t.left)/r.offsetWidth;return {style:`${h}: ${c/g}px; max-width: ${f/b}px`,class:"cm-completionInfo-"+(a?o?"left-narrow":"right-narrow":l?"left":"right")}}function r0(s){let t=s.addToOptions.slice();return s.icons&&t.push({render(e){let i=document.createElement("div");return i.classList.add("cm-completionIcon"),e.type&&i.classList.add(...e.type.split(/\s+/g).map(n=>"cm-completionIcon-"+n)),i.setAttribute("aria-hidden","true"),i},position:20}),t.push({render(e,i,n,r){let o=document.createElement("span");o.className="cm-completionLabel";let l=e.displayLabel||e.label,a=0;for(let h=0;h<r.length;){let c=r[h++],f=r[h++];c>a&&o.appendChild(document.createTextNode(l.slice(a,c)));let u=o.appendChild(document.createElement("span"));u.appendChild(document.createTextNode(l.slice(c,f))),u.className="cm-completionMatchedText",a=f;}return a<l.length&&o.appendChild(document.createTextNode(l.slice(a))),o},position:50},{render(e){if(!e.detail)return null;let i=document.createElement("span");return i.className="cm-completionDetail",i.textContent=e.detail,i},position:80}),t.sort((e,i)=>e.position-i.position).map(e=>e.render)}function Bs(s,t,e){if(s<=e)return {from:0,to:s};if(t<0&&(t=0),t<=s>>1){let n=Math.floor(t/e);return {from:n*e,to:(n+1)*e}}let i=Math.floor((s-t)/e);return {from:s-(i+1)*e,to:s-i*e}}class o0{constructor(t,e,i){this.view=t,this.stateField=e,this.applyCompletion=i,this.info=null,this.infoDestroy=null,this.placeInfoReq={read:()=>this.measureInfo(),write:a=>this.placeInfo(a),key:this},this.space=null,this.currentClass="";let n=t.state.field(e),{options:r,selected:o}=n.open,l=t.state.facet(nt);this.optionContent=r0(l),this.optionClass=l.optionClass,this.tooltipClass=l.tooltipClass,this.range=Bs(r.length,o,l.maxRenderedOptions),this.dom=document.createElement("div"),this.dom.className="cm-tooltip-autocomplete",this.updateTooltipClass(t.state),this.dom.addEventListener("mousedown",a=>{let{options:h}=t.state.field(e).open;for(let c=a.target,f;c&&c!=this.dom;c=c.parentNode)if(c.nodeName=="LI"&&(f=/-(\d+)$/.exec(c.id))&&+f[1]<h.length){this.applyCompletion(t,h[+f[1]]),a.preventDefault();return}}),this.dom.addEventListener("focusout",a=>{let h=t.state.field(this.stateField,!1);h&&h.tooltip&&t.state.facet(nt).closeOnBlur&&a.relatedTarget!=t.contentDOM&&t.dispatch({effects:Wi.of(null)});}),this.showOptions(r,n.id);}mount(){this.updateSel();}showOptions(t,e){this.list&&this.list.remove(),this.list=this.dom.appendChild(this.createListBox(t,e,this.range)),this.list.addEventListener("scroll",()=>{this.info&&this.view.requestMeasure(this.placeInfoReq);});}update(t){var e;let i=t.state.field(this.stateField),n=t.startState.field(this.stateField);if(this.updateTooltipClass(t.state),i!=n){let{options:r,selected:o,disabled:l}=i.open;(!n.open||n.open.options!=r)&&(this.range=Bs(r.length,o,t.state.facet(nt).maxRenderedOptions),this.showOptions(r,i.id)),this.updateSel(),l!=((e=n.open)===null||e===void 0?void 0:e.disabled)&&this.dom.classList.toggle("cm-tooltip-autocomplete-disabled",!!l);}}updateTooltipClass(t){let e=this.tooltipClass(t);if(e!=this.currentClass){for(let i of this.currentClass.split(" "))i&&this.dom.classList.remove(i);for(let i of e.split(" "))i&&this.dom.classList.add(i);this.currentClass=e;}}positioned(t){this.space=t,this.info&&this.view.requestMeasure(this.placeInfoReq);}updateSel(){let t=this.view.state.field(this.stateField),e=t.open;if((e.selected>-1&&e.selected<this.range.from||e.selected>=this.range.to)&&(this.range=Bs(e.options.length,e.selected,this.view.state.facet(nt).maxRenderedOptions),this.showOptions(e.options,t.id)),this.updateSelectedOption(e.selected)){this.destroyInfo();let{completion:i}=e.options[e.selected],{info:n}=i;if(!n)return;let r=typeof n=="string"?document.createTextNode(n):n(i);if(!r)return;"then"in r?r.then(o=>{o&&this.view.state.field(this.stateField,!1)==t&&this.addInfoPane(o,i);}).catch(o=>St(this.view.state,o,"completion info")):this.addInfoPane(r,i);}}addInfoPane(t,e){this.destroyInfo();let i=this.info=document.createElement("div");if(i.className="cm-tooltip cm-completionInfo",t.nodeType!=null)i.appendChild(t),this.infoDestroy=null;else {let{dom:n,destroy:r}=t;i.appendChild(n),this.infoDestroy=r||null;}this.dom.appendChild(i),this.view.requestMeasure(this.placeInfoReq);}updateSelectedOption(t){let e=null;for(let i=this.list.firstChild,n=this.range.from;i;i=i.nextSibling,n++)i.nodeName!="LI"||!i.id?n--:n==t?i.hasAttribute("aria-selected")||(i.setAttribute("aria-selected","true"),e=i):i.hasAttribute("aria-selected")&&i.removeAttribute("aria-selected");return e&&a0(this.list,e),e}measureInfo(){let t=this.dom.querySelector("[aria-selected]");if(!t||!this.info)return null;let e=this.dom.getBoundingClientRect(),i=this.info.getBoundingClientRect(),n=t.getBoundingClientRect(),r=this.space;if(!r){let o=this.dom.ownerDocument.documentElement;r={left:0,top:0,right:o.clientWidth,bottom:o.clientHeight};}return n.top>Math.min(r.bottom,e.bottom)-10||n.bottom<Math.max(r.top,e.top)+10?null:this.view.state.facet(nt).positionInfo(this.view,e,n,i,r,this.dom)}placeInfo(t){this.info&&(t?(t.style&&(this.info.style.cssText=t.style),this.info.className="cm-tooltip cm-completionInfo "+(t.class||"")):this.info.style.cssText="top: -1e6px");}createListBox(t,e,i){const n=document.createElement("ul");n.id=e,n.setAttribute("role","listbox"),n.setAttribute("aria-expanded","true"),n.setAttribute("aria-label",this.view.state.phrase("Completions")),n.addEventListener("mousedown",o=>{o.target==n&&o.preventDefault();});let r=null;for(let o=i.from;o<i.to;o++){let{completion:l,match:a}=t[o],{section:h}=l;if(h){let u=typeof h=="string"?h:h.name;if(u!=r&&(o>i.from||i.from==0))if(r=u,typeof h!="string"&&h.header)n.appendChild(h.header(h));else {let d=n.appendChild(document.createElement("completion-section"));d.textContent=u;}}const c=n.appendChild(document.createElement("li"));c.id=e+"-"+o,c.setAttribute("role","option");let f=this.optionClass(l);f&&(c.className=f);for(let u of this.optionContent){let d=u(l,this.view.state,this.view,a);d&&c.appendChild(d);}}return i.from&&n.classList.add("cm-completionListIncompleteTop"),i.to<t.length&&n.classList.add("cm-completionListIncompleteBottom"),n}destroyInfo(){this.info&&(this.infoDestroy&&this.infoDestroy(),this.info.remove(),this.info=null);}destroy(){this.destroyInfo();}}function l0(s,t){return e=>new o0(e,s,t)}function a0(s,t){let e=s.getBoundingClientRect(),i=t.getBoundingClientRect(),n=e.height/s.offsetHeight;i.top<e.top?s.scrollTop-=(e.top-i.top)/n:i.bottom>e.bottom&&(s.scrollTop+=(i.bottom-e.bottom)/n);}function ta(s){return (s.boost||0)*100+(s.apply?10:0)+(s.info?5:0)+(s.type?1:0)}function h0(s,t){let e=[],i=null,n=h=>{e.push(h);let{section:c}=h.completion;if(c){i||(i=[]);let f=typeof c=="string"?c:c.name;i.some(u=>u.name==f)||i.push(typeof c=="string"?{name:f}:c);}},r=t.facet(nt);for(let h of s)if(h.hasResult()){let c=h.result.getMatch;if(h.result.filter===!1)for(let f of h.result.options)n(new Ql(f,h.source,c?c(f):[],1e9-e.length));else {let f=t.sliceDoc(h.from,h.to),u,d=r.filterStrict?new n0(f):new i0(f);for(let p of h.result.options)if(u=d.match(p.label)){let y=p.displayLabel?c?c(p,u.matched):[]:u.matched;n(new Ql(p,h.source,y,u.score+(p.boost||0)));}}}if(i){let h=Object.create(null),c=0,f=(u,d)=>{var p,y;return ((p=u.rank)!==null&&p!==void 0?p:1e9)-((y=d.rank)!==null&&y!==void 0?y:1e9)||(u.name<d.name?-1:1)};for(let u of i.sort(f))c-=1e5,h[u.name]=c;for(let u of e){let{section:d}=u.completion;d&&(u.score+=h[typeof d=="string"?d:d.name]);}}let o=[],l=null,a=r.compareCompletions;for(let h of e.sort((c,f)=>f.score-c.score||a(c.completion,f.completion))){let c=h.completion;!l||l.label!=c.label||l.detail!=c.detail||l.type!=null&&c.type!=null&&l.type!=c.type||l.apply!=c.apply||l.boost!=c.boost?o.push(h):ta(h.completion)>ta(l)&&(o[o.length-1]=h),l=h.completion;}return o}class Je{constructor(t,e,i,n,r,o){this.options=t,this.attrs=e,this.tooltip=i,this.timestamp=n,this.selected=r,this.disabled=o;}setSelected(t,e){return t==this.selected||t>=this.options.length?this:new Je(this.options,ea(e,t),this.tooltip,this.timestamp,t,this.disabled)}static build(t,e,i,n,r,o){if(n&&!o&&t.some(h=>h.isPending))return n.setDisabled();let l=h0(t,e);if(!l.length)return n&&t.some(h=>h.isPending)?n.setDisabled():null;let a=e.facet(nt).selectOnOpen?0:-1;if(n&&n.selected!=a&&n.selected!=-1){let h=n.options[n.selected].completion;for(let c=0;c<l.length;c++)if(l[c].completion==h){a=c;break}}return new Je(l,ea(i,a),{pos:t.reduce((h,c)=>c.hasResult()?Math.min(h,c.from):h,1e8),create:m0,above:r.aboveCursor},n?n.timestamp:Date.now(),a,!1)}map(t){return new Je(this.options,this.attrs,Object.assign(Object.assign({},this.tooltip),{pos:t.mapPos(this.tooltip.pos)}),this.timestamp,this.selected,this.disabled)}setDisabled(){return new Je(this.options,this.attrs,this.tooltip,this.timestamp,this.selected,!0)}}class Xn{constructor(t,e,i){this.active=t,this.id=e,this.open=i;}static start(){return new Xn(d0,"cm-ac-"+Math.floor(Math.random()*2e6).toString(36),null)}update(t){let{state:e}=t,i=e.facet(nt),r=(i.override||e.languageDataAt("autocomplete",Ne(e)).map(e0)).map(a=>(this.active.find(c=>c.source==a)||new Ft(a,this.active.some(c=>c.state!=0)?1:0)).update(t,i));r.length==this.active.length&&r.every((a,h)=>a==this.active[h])&&(r=this.active);let o=this.open,l=t.effects.some(a=>a.is(ho));o&&t.docChanged&&(o=o.map(t.changes)),t.selection||r.some(a=>a.hasResult()&&t.changes.touchesRange(a.from,a.to))||!c0(r,this.active)||l?o=Je.build(r,e,this.id,o,i,l):o&&o.disabled&&!r.some(a=>a.isPending)&&(o=null),!o&&r.every(a=>!a.isPending)&&r.some(a=>a.hasResult())&&(r=r.map(a=>a.hasResult()?new Ft(a.source,0):a));for(let a of t.effects)a.is(tf)&&(o=o&&o.setSelected(a.value,this.id));return r==this.active&&o==this.open?this:new Xn(r,this.id,o)}get tooltip(){return this.open?this.open.tooltip:null}get attrs(){return this.open?this.open.attrs:this.active.length?f0:u0}}function c0(s,t){if(s==t)return !0;for(let e=0,i=0;;){for(;e<s.length&&!s[e].hasResult();)e++;for(;i<t.length&&!t[i].hasResult();)i++;let n=e==s.length,r=i==t.length;if(n||r)return n==r;if(s[e++].result!=t[i++].result)return !1}}const f0={"aria-autocomplete":"list"},u0={};function ea(s,t){let e={"aria-autocomplete":"list","aria-haspopup":"listbox","aria-controls":s};return t>-1&&(e["aria-activedescendant"]=s+"-"+t),e}const d0=[];function $c(s,t){if(s.isUserEvent("input.complete")){let i=s.annotation(ao);if(i&&t.activateOnCompletion(i))return 12}let e=s.isUserEvent("input.type");return e&&t.activateOnTyping?5:e?1:s.isUserEvent("delete.backward")?2:s.selection?8:s.docChanged?16:0}class Ft{constructor(t,e,i=!1){this.source=t,this.state=e,this.explicit=i;}hasResult(){return !1}get isPending(){return this.state==1}update(t,e){let i=$c(t,e),n=this;(i&8||i&16&&this.touches(t))&&(n=new Ft(n.source,0)),i&4&&n.state==0&&(n=new Ft(this.source,1)),n=n.updateFor(t,i);for(let r of t.effects)if(r.is(Jn))n=new Ft(n.source,1,r.value);else if(r.is(Wi))n=new Ft(n.source,0);else if(r.is(ho))for(let o of r.value)o.source==n.source&&(n=o);return n}updateFor(t,e){return this.map(t.changes)}map(t){return this}touches(t){return t.changes.touchesRange(Ne(t.state))}}class $e extends Ft{constructor(t,e,i,n,r,o){super(t,3,e),this.limit=i,this.result=n,this.from=r,this.to=o;}hasResult(){return !0}updateFor(t,e){var i;if(!(e&3))return this.map(t.changes);let n=this.result;n.map&&!t.changes.empty&&(n=n.map(n,t.changes));let r=t.changes.mapPos(this.from),o=t.changes.mapPos(this.to,1),l=Ne(t.state);if(l>o||!n||e&2&&(Ne(t.startState)==this.from||l<this.limit))return new Ft(this.source,e&4?1:0);let a=t.changes.mapPos(this.limit);return p0(n.validFor,t.state,r,o)?new $e(this.source,this.explicit,a,n,r,o):n.update&&(n=n.update(n,r,o,new Qc(t.state,l,!1)))?new $e(this.source,this.explicit,a,n,n.from,(i=n.to)!==null&&i!==void 0?i:Ne(t.state)):new Ft(this.source,1,this.explicit)}map(t){return t.empty?this:(this.result.map?this.result.map(this.result,t):this.result)?new $e(this.source,this.explicit,t.mapPos(this.limit),this.result,t.mapPos(this.from),t.mapPos(this.to,1)):new Ft(this.source,0)}touches(t){return t.changes.touchesRange(this.from,this.to)}}function p0(s,t,e,i){if(!s)return !1;let n=t.sliceDoc(e,i);return typeof s=="function"?s(n,e,i,t):Zc(s,!0).test(n)}const ho=R.define({map(s,t){return s.map(e=>e.map(t))}}),tf=R.define(),kt=At.define({create(){return Xn.start()},update(s,t){return s.update(t)},provide:s=>[Jr.from(s,t=>t.tooltip),O.contentAttributes.from(s,t=>t.attrs)]});function co(s,t){const e=t.completion.apply||t.completion.label;let i=s.state.field(kt).active.find(n=>n.source==t.source);return i instanceof $e?(typeof e=="string"?s.dispatch(Object.assign(Object.assign({},t0(s.state,e,i.from,i.to)),{annotations:ao.of(t.completion)})):e(s,t.completion,i.from,i.to),!0):!1}const m0=l0(kt,co);function vn(s,t="option"){return e=>{let i=e.state.field(kt,!1);if(!i||!i.open||i.open.disabled||Date.now()-i.open.timestamp<e.state.facet(nt).interactionDelay)return !1;let n=1,r;t=="page"&&(r=qh(e,i.open.tooltip))&&(n=Math.max(2,Math.floor(r.dom.offsetHeight/r.dom.querySelector("li").offsetHeight)-1));let{length:o}=i.open.options,l=i.open.selected>-1?i.open.selected+n*(s?1:-1):s?0:o-1;return l<0?l=t=="page"?0:o-1:l>=o&&(l=t=="page"?o-1:0),e.dispatch({effects:tf.of(l)}),!0}}const ef=s=>{let t=s.state.field(kt,!1);return s.state.readOnly||!t||!t.open||t.open.selected<0||t.open.disabled||Date.now()-t.open.timestamp<s.state.facet(nt).interactionDelay?!1:co(s,t.open.options[t.open.selected])},ia=s=>s.state.field(kt,!1)?(s.dispatch({effects:Jn.of(!0)}),!0):!1,g0=s=>{let t=s.state.field(kt,!1);return !t||!t.active.some(e=>e.state!=0)?!1:(s.dispatch({effects:Wi.of(null)}),!0)};class y0{constructor(t,e){this.active=t,this.context=e,this.time=Date.now(),this.updates=[],this.done=void 0;}}const b0=50,w0=1e3;ht.fromClass(class{constructor(s){this.view=s,this.debounceUpdate=-1,this.running=[],this.debounceAccept=-1,this.pendingStart=!1,this.composing=0;for(let t of s.state.field(kt).active)t.isPending&&this.startQuery(t);}update(s){let t=s.state.field(kt),e=s.state.facet(nt);if(!s.selectionSet&&!s.docChanged&&s.startState.field(kt)==t)return;let i=s.transactions.some(r=>{let o=$c(r,e);return o&8||(r.selection||r.docChanged)&&!(o&3)});for(let r=0;r<this.running.length;r++){let o=this.running[r];if(i||o.context.abortOnDocChange&&s.docChanged||o.updates.length+s.transactions.length>b0&&Date.now()-o.time>w0){for(let l of o.context.abortListeners)try{l();}catch(a){St(this.view.state,a);}o.context.abortListeners=null,this.running.splice(r--,1);}else o.updates.push(...s.transactions);}this.debounceUpdate>-1&&clearTimeout(this.debounceUpdate),s.transactions.some(r=>r.effects.some(o=>o.is(Jn)))&&(this.pendingStart=!0);let n=this.pendingStart?50:e.activateOnTypingDelay;if(this.debounceUpdate=t.active.some(r=>r.isPending&&!this.running.some(o=>o.active.source==r.source))?setTimeout(()=>this.startUpdate(),n):-1,this.composing!=0)for(let r of s.transactions)r.isUserEvent("input.type")?this.composing=2:this.composing==2&&r.selection&&(this.composing=3);}startUpdate(){this.debounceUpdate=-1,this.pendingStart=!1;let{state:s}=this.view,t=s.field(kt);for(let e of t.active)e.isPending&&!this.running.some(i=>i.active.source==e.source)&&this.startQuery(e);this.running.length&&t.open&&t.open.disabled&&(this.debounceAccept=setTimeout(()=>this.accept(),this.view.state.facet(nt).updateSyncTime));}startQuery(s){let{state:t}=this.view,e=Ne(t),i=new Qc(t,e,s.explicit,this.view),n=new y0(s,i);this.running.push(n),Promise.resolve(s.source(i)).then(r=>{n.context.aborted||(n.done=r||null,this.scheduleAccept());},r=>{this.view.dispatch({effects:Wi.of(null)}),St(this.view.state,r);});}scheduleAccept(){this.running.every(s=>s.done!==void 0)?this.accept():this.debounceAccept<0&&(this.debounceAccept=setTimeout(()=>this.accept(),this.view.state.facet(nt).updateSyncTime));}accept(){var s;this.debounceAccept>-1&&clearTimeout(this.debounceAccept),this.debounceAccept=-1;let t=[],e=this.view.state.facet(nt),i=this.view.state.field(kt);for(let n=0;n<this.running.length;n++){let r=this.running[n];if(r.done===void 0)continue;if(this.running.splice(n--,1),r.done){let l=Ne(r.updates.length?r.updates[0].startState:this.view.state),a=Math.min(l,r.done.from+(r.active.explicit?0:1)),h=new $e(r.active.source,r.active.explicit,a,r.done,r.done.from,(s=r.done.to)!==null&&s!==void 0?s:l);for(let c of r.updates)h=h.update(c,e);if(h.hasResult()){t.push(h);continue}}let o=i.active.find(l=>l.source==r.active.source);if(o&&o.isPending)if(r.done==null){let l=new Ft(r.active.source,0);for(let a of r.updates)l=l.update(a,e);l.isPending||t.push(l);}else this.startQuery(o);}(t.length||i.open&&i.open.disabled)&&this.view.dispatch({effects:ho.of(t)});}},{eventHandlers:{blur(s){let t=this.view.state.field(kt,!1);if(t&&t.tooltip&&this.view.state.facet(nt).closeOnBlur){let e=t.open&&qh(this.view,t.open.tooltip);(!e||!e.dom.contains(s.relatedTarget))&&setTimeout(()=>this.view.dispatch({effects:Wi.of(null)}),10);}},compositionstart(){this.composing=1;},compositionend(){this.composing==3&&setTimeout(()=>this.view.dispatch({effects:Jn.of(!1)}),20),this.composing=0;}}});const v0=typeof navigator=="object"&&/Win/.test(navigator.platform);qe.highest(O.domEventHandlers({keydown(s,t){let e=t.state.field(kt,!1);if(!e||!e.open||e.open.disabled||e.open.selected<0||s.key.length>1||s.ctrlKey&&!(v0&&s.altKey)||s.metaKey)return !1;let i=e.open.options[e.open.selected],n=e.active.find(o=>o.source==i.source),r=i.completion.commitCharacters||n.result.commitCharacters;return r&&r.indexOf(s.key)>-1&&co(t,i),!1}}));const nf=O.baseTheme({".cm-tooltip.cm-tooltip-autocomplete":{"& > ul":{fontFamily:"monospace",whiteSpace:"nowrap",overflow:"hidden auto",maxWidth_fallback:"700px",maxWidth:"min(700px, 95vw)",minWidth:"250px",maxHeight:"10em",height:"100%",listStyle:"none",margin:0,padding:0,"& > li, & > completion-section":{padding:"1px 3px",lineHeight:1.2},"& > li":{overflowX:"hidden",textOverflow:"ellipsis",cursor:"pointer"},"& > completion-section":{display:"list-item",borderBottom:"1px solid silver",paddingLeft:"0.5em",opacity:.7}}},"&light .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#17c",color:"white"},"&light .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#777"},"&dark .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#347",color:"white"},"&dark .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#444"},".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after":{content:'"···"',opacity:.5,display:"block",textAlign:"center"},".cm-tooltip.cm-completionInfo":{position:"absolute",padding:"3px 9px",width:"max-content",maxWidth:"400px",boxSizing:"border-box",whiteSpace:"pre-line"},".cm-completionInfo.cm-completionInfo-left":{right:"100%"},".cm-completionInfo.cm-completionInfo-right":{left:"100%"},".cm-completionInfo.cm-completionInfo-left-narrow":{right:"30px"},".cm-completionInfo.cm-completionInfo-right-narrow":{left:"30px"},"&light .cm-snippetField":{backgroundColor:"#00000022"},"&dark .cm-snippetField":{backgroundColor:"#ffffff22"},".cm-snippetFieldPosition":{verticalAlign:"text-top",width:0,height:"1.15em",display:"inline-block",margin:"0 -0.7px -.7em",borderLeft:"1.4px dotted #888"},".cm-completionMatchedText":{textDecoration:"underline"},".cm-completionDetail":{marginLeft:"0.5em",fontStyle:"italic"},".cm-completionIcon":{fontSize:"90%",width:".8em",display:"inline-block",textAlign:"center",paddingRight:".6em",opacity:"0.6",boxSizing:"content-box"},".cm-completionIcon-function, .cm-completionIcon-method":{"&:after":{content:"'ƒ'"}},".cm-completionIcon-class":{"&:after":{content:"'○'"}},".cm-completionIcon-interface":{"&:after":{content:"'◌'"}},".cm-completionIcon-variable":{"&:after":{content:"'𝑥'"}},".cm-completionIcon-constant":{"&:after":{content:"'𝐶'"}},".cm-completionIcon-type":{"&:after":{content:"'𝑡'"}},".cm-completionIcon-enum":{"&:after":{content:"'∪'"}},".cm-completionIcon-property":{"&:after":{content:"'□'"}},".cm-completionIcon-keyword":{"&:after":{content:"'🔑︎'"}},".cm-completionIcon-namespace":{"&:after":{content:"'▢'"}},".cm-completionIcon-text":{"&:after":{content:"'abc'",fontSize:"50%",verticalAlign:"middle"}}});class S0{constructor(t,e,i,n){this.field=t,this.line=e,this.from=i,this.to=n;}}class fo{constructor(t,e,i){this.field=t,this.from=e,this.to=i;}map(t){let e=t.mapPos(this.from,-1,at.TrackDel),i=t.mapPos(this.to,1,at.TrackDel);return e==null||i==null?null:new fo(this.field,e,i)}}class uo{constructor(t,e){this.lines=t,this.fieldPositions=e;}instantiate(t,e){let i=[],n=[e],r=t.doc.lineAt(e),o=/^\s*/.exec(r.text)[0];for(let a of this.lines){if(i.length){let h=o,c=/^\t*/.exec(a)[0].length;for(let f=0;f<c;f++)h+=t.facet(es);n.push(e+h.length-c),a=h+a.slice(c);}i.push(a),e+=a.length+1;}let l=this.fieldPositions.map(a=>new fo(a.field,n[a.line]+a.from,n[a.line]+a.to));return {text:i,ranges:l}}static parse(t){let e=[],i=[],n=[],r;for(let o of t.split(/\r\n?|\n/)){for(;r=/[#$]\{(?:(\d+)(?::([^}]*))?|((?:\\[{}]|[^}])*))\}/.exec(o);){let l=r[1]?+r[1]:null,a=r[2]||r[3]||"",h=-1,c=a.replace(/\\[{}]/g,f=>f[1]);for(let f=0;f<e.length;f++)(l!=null?e[f].seq==l:c&&e[f].name==c)&&(h=f);if(h<0){let f=0;for(;f<e.length&&(l==null||e[f].seq!=null&&e[f].seq<l);)f++;e.splice(f,0,{seq:l,name:c}),h=f;for(let u of n)u.field>=h&&u.field++;}n.push(new S0(h,i.length,r.index,r.index+c.length)),o=o.slice(0,r.index)+a+o.slice(r.index+r[0].length);}o=o.replace(/\\([{}])/g,(l,a,h)=>{for(let c of n)c.line==i.length&&c.from>h&&(c.from--,c.to--);return a}),i.push(o);}return new uo(i,n)}}let C0=I.widget({widget:new class extends oe{toDOM(){let s=document.createElement("span");return s.className="cm-snippetFieldPosition",s}ignoreEvent(){return !1}}}),A0=I.mark({class:"cm-snippetField"});class fi{constructor(t,e){this.ranges=t,this.active=e,this.deco=I.set(t.map(i=>(i.from==i.to?C0:A0).range(i.from,i.to)));}map(t){let e=[];for(let i of this.ranges){let n=i.map(t);if(!n)return null;e.push(n);}return new fi(e,this.active)}selectionInsideField(t){return t.ranges.every(e=>this.ranges.some(i=>i.field==this.active&&i.from<=e.from&&i.to>=e.to))}}const $i=R.define({map(s,t){return s&&s.map(t)}}),M0=R.define(),_i=At.define({create(){return null},update(s,t){for(let e of t.effects){if(e.is($i))return e.value;if(e.is(M0)&&s)return new fi(s.ranges,e.value)}return s&&t.docChanged&&(s=s.map(t.changes)),s&&t.selection&&!s.selectionInsideField(t.selection)&&(s=null),s},provide:s=>O.decorations.from(s,t=>t?t.deco:I.none)});function po(s,t){return v.create(s.filter(e=>e.field==t).map(e=>v.range(e.from,e.to)))}function D0(s){let t=uo.parse(s);return (e,i,n,r)=>{let{text:o,ranges:l}=t.instantiate(e.state,n),{main:a}=e.state.selection,h={changes:{from:n,to:r==a.from?a.to:r,insert:H.of(o)},scrollIntoView:!0,annotations:i?[ao.of(i),tt.userEvent.of("input.complete")]:void 0};if(l.length&&(h.selection=po(l,0)),l.some(c=>c.field>0)){let c=new fi(l,0),f=h.effects=[$i.of(c)];e.state.field(_i,!1)===void 0&&f.push(R.appendConfig.of([_i,P0,L0,nf]));}e.dispatch(e.state.update(h));}}function sf(s){return ({state:t,dispatch:e})=>{let i=t.field(_i,!1);if(!i||s<0&&i.active==0)return !1;let n=i.active+s,r=s>0&&!i.ranges.some(o=>o.field==n+s);return e(t.update({selection:po(i.ranges,n),effects:$i.of(r?null:new fi(i.ranges,n)),scrollIntoView:!0})),!0}}const T0=({state:s,dispatch:t})=>s.field(_i,!1)?(t(s.update({effects:$i.of(null)})),!0):!1,O0=sf(1),B0=sf(-1),E0=[{key:"Tab",run:O0,shift:B0},{key:"Escape",run:T0}],na=T.define({combine(s){return s.length?s[0]:E0}}),P0=qe.highest(Ji.compute([na],s=>s.facet(na)));function tb(s,t){return Object.assign(Object.assign({},t),{apply:D0(s)})}const L0=O.domEventHandlers({mousedown(s,t){let e=t.state.field(_i,!1),i;if(!e||(i=t.posAtCoords({x:s.clientX,y:s.clientY}))==null)return !1;let n=e.ranges.find(r=>r.from<=i&&r.to>=i);return !n||n.field==e.active?!1:(t.dispatch({selection:po(e.ranges,n.field),effects:$i.of(e.ranges.some(r=>r.field>n.field)?new fi(e.ranges,n.field):null),scrollIntoView:!0}),!0)}}),zi={brackets:["(","[","{","'",'"'],before:")]}:;>",stringPrefixes:[]},Ie=R.define({map(s,t){let e=t.mapPos(s,-1,at.TrackAfter);return e??void 0}}),mo=new class extends Fe{};mo.startSide=1;mo.endSide=-1;const rf=At.define({create(){return z.empty},update(s,t){if(s=s.map(t.changes),t.selection){let e=t.state.doc.lineAt(t.selection.main.head);s=s.update({filter:i=>i>=e.from&&i<=e.to});}for(let e of t.effects)e.is(Ie)&&(s=s.update({add:[mo.range(e.value,e.value+1)]}));return s}});function R0(){return [N0,rf]}const Es="()[]{}<>«»»«［］｛｝";function of(s){for(let t=0;t<Es.length;t+=2)if(Es.charCodeAt(t)==s)return Es.charAt(t+1);return Oa(s<128?s:s+1)}function lf(s,t){return s.languageDataAt("closeBrackets",t)[0]||zi}const I0=typeof navigator=="object"&&/Android\b/.test(navigator.userAgent),N0=O.inputHandler.of((s,t,e,i)=>{if((I0?s.composing:s.compositionStarted)||s.state.readOnly)return !1;let n=s.state.selection.main;if(i.length>2||i.length==2&&ae(Tt(i,0))==1||t!=n.from||e!=n.to)return !1;let r=V0(s.state,i);return r?(s.dispatch(r),!0):!1}),F0=({state:s,dispatch:t})=>{if(s.readOnly)return !1;let i=lf(s,s.selection.main.head).brackets||zi.brackets,n=null,r=s.changeByRange(o=>{if(o.empty){let l=W0(s.doc,o.head);for(let a of i)if(a==l&&as(s.doc,o.head)==of(Tt(a,0)))return {changes:{from:o.head-a.length,to:o.head+a.length},range:v.cursor(o.head-a.length)}}return {range:n=o}});return n||t(s.update(r,{scrollIntoView:!0,userEvent:"delete.backward"})),!n},H0=[{key:"Backspace",run:F0}];function V0(s,t){let e=lf(s,s.selection.main.head),i=e.brackets||zi.brackets;for(let n of i){let r=of(Tt(n,0));if(t==n)return r==n?q0(s,n,i.indexOf(n+n+n)>-1,e):_0(s,n,r,e.before||zi.before);if(t==r&&af(s,s.selection.main.from))return z0(s,n,r)}return null}function af(s,t){let e=!1;return s.field(rf).between(0,s.doc.length,i=>{i==t&&(e=!0);}),e}function as(s,t){let e=s.sliceString(t,t+2);return e.slice(0,ae(Tt(e,0)))}function W0(s,t){let e=s.sliceString(t-2,t);return ae(Tt(e,0))==e.length?e:e.slice(1)}function _0(s,t,e,i){let n=null,r=s.changeByRange(o=>{if(!o.empty)return {changes:[{insert:t,from:o.from},{insert:e,from:o.to}],effects:Ie.of(o.to+t.length),range:v.range(o.anchor+t.length,o.head+t.length)};let l=as(s.doc,o.head);return !l||/\s/.test(l)||i.indexOf(l)>-1?{changes:{insert:t+e,from:o.head},effects:Ie.of(o.head+t.length),range:v.cursor(o.head+t.length)}:{range:n=o}});return n?null:s.update(r,{scrollIntoView:!0,userEvent:"input.type"})}function z0(s,t,e){let i=null,n=s.changeByRange(r=>r.empty&&as(s.doc,r.head)==e?{changes:{from:r.head,to:r.head+e.length,insert:e},range:v.cursor(r.head+e.length)}:i={range:r});return i?null:s.update(n,{scrollIntoView:!0,userEvent:"input.type"})}function q0(s,t,e,i){let n=i.stringPrefixes||zi.stringPrefixes,r=null,o=s.changeByRange(l=>{if(!l.empty)return {changes:[{insert:t,from:l.from},{insert:t,from:l.to}],effects:Ie.of(l.to+t.length),range:v.range(l.anchor+t.length,l.head+t.length)};let a=l.head,h=as(s.doc,a),c;if(h==t){if(sa(s,a))return {changes:{insert:t+t,from:a},effects:Ie.of(a+t.length),range:v.cursor(a+t.length)};if(af(s,a)){let u=e&&s.sliceDoc(a,a+t.length*3)==t+t+t?t+t+t:t;return {changes:{from:a,to:a+u.length,insert:u},range:v.cursor(a+u.length)}}}else {if(e&&s.sliceDoc(a-2*t.length,a)==t+t&&(c=ra(s,a-2*t.length,n))>-1&&sa(s,c))return {changes:{insert:t+t+t+t,from:a},effects:Ie.of(a+t.length),range:v.cursor(a+t.length)};if(s.charCategorizer(a)(h)!=Lt.Word&&ra(s,a,n)>-1&&!j0(s,a,t,n))return {changes:{insert:t+t,from:a},effects:Ie.of(a+t.length),range:v.cursor(a+t.length)}}return {range:r=l}});return r?null:s.update(o,{scrollIntoView:!0,userEvent:"input.type"})}function sa(s,t){let e=ct(s).resolveInner(t+1);return e.parent&&e.from==t}function j0(s,t,e,i){let n=ct(s).resolveInner(t,-1),r=i.reduce((o,l)=>Math.max(o,l.length),0);for(let o=0;o<5;o++){let l=s.sliceDoc(n.from,Math.min(n.to,n.from+e.length+r)),a=l.indexOf(e);if(!a||a>-1&&i.indexOf(l.slice(0,a))>-1){let c=n.firstChild;for(;c&&c.from==n.from&&c.to-c.from>e.length+a;){if(s.sliceDoc(c.to-e.length,c.to)==e)return !1;c=c.firstChild;}return !0}let h=n.to==t&&n.parent;if(!h)break;n=h;}return !1}function ra(s,t,e){let i=s.charCategorizer(t);if(i(s.sliceDoc(t-1,t))!=Lt.Word)return t;for(let n of e){let r=t-n.length;if(s.sliceDoc(r,t)==n&&i(s.sliceDoc(r-1,r))!=Lt.Word)return r}return -1}const hf=[{key:"Ctrl-Space",run:ia},{mac:"Alt-`",run:ia},{key:"Escape",run:g0},{key:"ArrowDown",run:vn(!0)},{key:"ArrowUp",run:vn(!1)},{key:"PageDown",run:vn(!0,"page")},{key:"PageUp",run:vn(!1,"page")},{key:"Enter",run:ef}];qe.highest(Ji.computeN([nt],s=>s.facet(nt).defaultKeymap?[hf]:[]));const G0="#2E3235",Zt="#DDDDDD",Oi="#B9D2FF",kn="#b0b0b0",Y0="#e0e0e0",cf="#808080",Ps="#000000",J0="#A54543",ff="#fc6d24",Be="#fda331",Ls="#8abeb7",oa="#b5bd68",bi="#6fb3d2",wi="#cc99cc",X0="#6987AF",la=ff,aa="#292d30",Sn=Oi+"30",Q0=G0,Rs=Zt,Z0="#202325",ha=Zt,$0=O.theme({"&":{color:Zt,backgroundColor:Q0},".cm-content":{caretColor:ha},".cm-cursor, .cm-dropCursor":{borderLeftColor:ha},"&.cm-focused .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection":{backgroundColor:Z0},".cm-panels":{backgroundColor:aa,color:kn},".cm-panels.cm-panels-top":{borderBottom:"2px solid black"},".cm-panels.cm-panels-bottom":{borderTop:"2px solid black"},".cm-searchMatch":{backgroundColor:Oi,outline:`1px solid ${kn}`,color:Ps},".cm-searchMatch.cm-searchMatch-selected":{backgroundColor:Y0,color:Ps},".cm-activeLine":{backgroundColor:Sn},".cm-selectionMatch":{backgroundColor:Sn},"&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket":{outline:`1px solid ${kn}`},"&.cm-focused .cm-matchingBracket":{backgroundColor:Oi,color:Ps},".cm-gutters":{borderRight:"1px solid #ffffff10",color:cf,backgroundColor:aa},".cm-activeLineGutter":{backgroundColor:Sn},".cm-foldPlaceholder":{backgroundColor:"transparent",border:"none",color:Oi},".cm-tooltip":{border:"none",backgroundColor:Rs},".cm-tooltip .cm-tooltip-arrow:before":{borderTopColor:"transparent",borderBottomColor:"transparent"},".cm-tooltip .cm-tooltip-arrow:after":{borderTopColor:Rs,borderBottomColor:Rs},".cm-tooltip-autocomplete":{"& > ul > li[aria-selected]":{backgroundColor:Sn,color:kn}}},{dark:!0}),ty=hi.define([{tag:m.keyword,color:Be},{tag:[m.name,m.deleted,m.character,m.propertyName,m.macroName],color:oa},{tag:[m.variableName],color:bi},{tag:[m.function(m.variableName)],color:Be},{tag:[m.labelName],color:ff},{tag:[m.color,m.constant(m.name),m.standard(m.name)],color:Be},{tag:[m.definition(m.name),m.separator],color:wi},{tag:[m.brace],color:wi},{tag:[m.annotation],color:la},{tag:[m.number,m.changed,m.annotation,m.modifier,m.self,m.namespace],color:Be},{tag:[m.typeName,m.className],color:bi},{tag:[m.operator,m.operatorKeyword],color:wi},{tag:[m.tagName],color:Be},{tag:[m.squareBracket],color:wi},{tag:[m.angleBracket],color:wi},{tag:[m.attributeName],color:bi},{tag:[m.regexp],color:Be},{tag:[m.quote],color:Zt},{tag:[m.string],color:oa},{tag:m.link,color:X0,textDecoration:"underline",textUnderlinePosition:"under"},{tag:[m.url,m.escape,m.special(m.string)],color:Ls},{tag:[m.meta],color:J0},{tag:[m.comment],color:cf,fontStyle:"italic"},{tag:m.monospace,color:Zt},{tag:m.strong,fontWeight:"bold",color:Be},{tag:m.emphasis,fontStyle:"italic",color:bi},{tag:m.strikethrough,textDecoration:"line-through"},{tag:m.heading,fontWeight:"bold",color:Zt},{tag:m.special(m.heading1),fontWeight:"bold",color:Zt},{tag:m.heading1,fontWeight:"bold",color:Zt},{tag:[m.heading2,m.heading3,m.heading4],fontWeight:"bold",color:Zt},{tag:[m.heading5,m.heading6],color:Zt},{tag:[m.atom,m.bool,m.special(m.variableName)],color:Ls},{tag:[m.processingInstruction,m.inserted],color:Ls},{tag:[m.contentSeparator],color:bi},{tag:m.invalid,color:Oi,borderBottom:`1px dotted ${la}`}]);[$0,io(ty)];const ca="#2e3440",go="#3b4252",fa="#434c5e",Cn="#4c566a",ua="#e5e9f0",Rr="#eceff4",Is="#8fbcbb",da="#88c0d0",iy="#81a1c1",qt="#5e81ac",ny="#bf616a",Ue="#d08770",Ns="#ebcb8b",pa="#a3be8c",sy="#b48ead",ma="#d30102",yo=Rr,Fs=yo,ry="#ffffff",Hs=go,oy=yo,ga=go,ly=O.theme({"&":{color:ca,backgroundColor:ry},".cm-content":{caretColor:ga},".cm-cursor, .cm-dropCursor":{borderLeftColor:ga},"&.cm-focused .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection":{backgroundColor:oy},".cm-panels":{backgroundColor:yo,color:Cn},".cm-panels.cm-panels-top":{borderBottom:"2px solid black"},".cm-panels.cm-panels-bottom":{borderTop:"2px solid black"},".cm-searchMatch":{backgroundColor:"#72a1ff59",outline:`1px solid ${Cn}`},".cm-searchMatch.cm-searchMatch-selected":{backgroundColor:ua},".cm-activeLine":{backgroundColor:Fs},".cm-selectionMatch":{backgroundColor:ua},"&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket":{outline:`1px solid ${Cn}`},"&.cm-focused .cm-matchingBracket":{backgroundColor:Rr},".cm-gutters":{backgroundColor:Rr,color:ca,border:"none"},".cm-activeLineGutter":{backgroundColor:Fs},".cm-foldPlaceholder":{backgroundColor:"transparent",border:"none",color:"#ddd"},".cm-tooltip":{border:"none",backgroundColor:Hs},".cm-tooltip .cm-tooltip-arrow:before":{borderTopColor:"transparent",borderBottomColor:"transparent"},".cm-tooltip .cm-tooltip-arrow:after":{borderTopColor:Hs,borderBottomColor:Hs},".cm-tooltip-autocomplete":{"& > ul > li[aria-selected]":{backgroundColor:Fs,color:Cn}}},{dark:!1}),ay=hi.define([{tag:m.keyword,color:qt},{tag:[m.name,m.deleted,m.character,m.propertyName,m.macroName],color:Ue},{tag:[m.variableName],color:Ue},{tag:[m.function(m.variableName)],color:qt},{tag:[m.labelName],color:iy},{tag:[m.color,m.constant(m.name),m.standard(m.name)],color:qt},{tag:[m.definition(m.name),m.separator],color:pa},{tag:[m.brace],color:Is},{tag:[m.annotation],color:ma},{tag:[m.number,m.changed,m.annotation,m.modifier,m.self,m.namespace],color:da},{tag:[m.typeName,m.className],color:Ns},{tag:[m.operator,m.operatorKeyword],color:pa},{tag:[m.tagName],color:sy},{tag:[m.squareBracket],color:ny},{tag:[m.angleBracket],color:Ue},{tag:[m.attributeName],color:Ns},{tag:[m.regexp],color:qt},{tag:[m.quote],color:go},{tag:[m.string],color:Ue},{tag:m.link,color:Is,textDecoration:"underline",textUnderlinePosition:"under"},{tag:[m.url,m.escape,m.special(m.string)],color:Ue},{tag:[m.meta],color:da},{tag:[m.comment],color:fa,fontStyle:"italic"},{tag:m.strong,fontWeight:"bold",color:qt},{tag:m.emphasis,fontStyle:"italic",color:qt},{tag:m.strikethrough,textDecoration:"line-through"},{tag:m.heading,fontWeight:"bold",color:qt},{tag:m.special(m.heading1),fontWeight:"bold",color:qt},{tag:m.heading1,fontWeight:"bold",color:qt},{tag:[m.heading2,m.heading3,m.heading4],fontWeight:"bold",color:qt},{tag:[m.heading5,m.heading6],color:qt},{tag:[m.atom,m.bool,m.special(m.variableName)],color:Ue},{tag:[m.processingInstruction,m.inserted],color:Is},{tag:[m.contentSeparator],color:Ns},{tag:m.invalid,color:fa,borderBottom:`1px dotted ${ma}`}]);[ly,io(ay)];function ee(){var s=arguments[0];typeof s=="string"&&(s=document.createElement(s));var t=1,e=arguments[1];if(e&&typeof e=="object"&&e.nodeType==null&&!Array.isArray(e)){for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var n=e[i];typeof n=="string"?s.setAttribute(i,n):n!=null&&(s[i]=n);}t++;}for(;t<arguments.length;t++)uf(s,arguments[t]);return s}function uf(s,t){if(typeof t=="string")s.appendChild(document.createTextNode(t));else if(t!=null)if(t.nodeType!=null)s.appendChild(t);else if(Array.isArray(t))for(var e=0;e<t.length;e++)uf(s,t[e]);else throw new RangeError("Unsupported child node: "+t)}class ya{constructor(t,e,i){this.from=t,this.to=e,this.diagnostic=i;}}class Pe{constructor(t,e,i){this.diagnostics=t,this.panel=e,this.selected=i;}static init(t,e,i){let n=t,r=i.facet(qi).markerFilter;r&&(n=r(n,i));let o=t.slice().sort((f,u)=>f.from-u.from||f.to-u.to),l=new xe,a=[],h=0;for(let f=0;;){let u=f==o.length?null:o[f];if(!u&&!a.length)break;let d,p;for(a.length?(d=h,p=a.reduce((g,b)=>Math.min(g,b.to),u&&u.from>d?u.from:1e8)):(d=u.from,p=u.to,a.push(u),f++);f<o.length;){let g=o[f];if(g.from==d&&(g.to>g.from||g.to==d))a.push(g),f++,p=Math.min(g.to,p);else {p=Math.min(g.from,p);break}}let y=ky(a);if(a.some(g=>g.from==g.to||g.from==g.to-1&&i.doc.lineAt(g.from).to==g.from))l.add(d,d,I.widget({widget:new by(y),diagnostics:a.slice()}));else {let g=a.reduce((b,x)=>x.markClass?b+" "+x.markClass:b,"");l.add(d,p,I.mark({class:"cm-lintRange cm-lintRange-"+y+g,diagnostics:a.slice(),inclusiveEnd:a.some(b=>b.to>p)}));}h=p;for(let g=0;g<a.length;g++)a[g].to<=h&&a.splice(g--,1);}let c=l.finish();return new Pe(c,e,li(c))}}function li(s,t=null,e=0){let i=null;return s.between(e,1e9,(n,r,{spec:o})=>{if(!(t&&o.diagnostics.indexOf(t)<0))if(!i)i=new ya(n,r,t||o.diagnostics[0]);else {if(o.diagnostics.indexOf(i.diagnostic)<0)return !1;i=new ya(i.from,r,i.diagnostic);}}),i}function cy(s,t){let e=t.pos,i=t.end||e,n=s.state.facet(qi).hideOn(s,e,i);if(n!=null)return n;let r=s.startState.doc.lineAt(t.pos);return !!(s.effects.some(o=>o.is(df))||s.changes.touchesRange(r.from,Math.max(r.to,i)))}function fy(s,t){return s.field(Ot,!1)?t:t.concat(R.appendConfig.of(Sy))}const df=R.define(),bo=R.define(),pf=R.define(),Ot=At.define({create(){return new Pe(I.none,null,null)},update(s,t){if(t.docChanged&&s.diagnostics.size){let e=s.diagnostics.map(t.changes),i=null,n=s.panel;if(s.selected){let r=t.changes.mapPos(s.selected.from,1);i=li(e,s.selected.diagnostic,r)||li(e,null,r);}!e.size&&n&&t.state.facet(qi).autoPanel&&(n=null),s=new Pe(e,n,i);}for(let e of t.effects)if(e.is(df)){let i=t.state.facet(qi).autoPanel?e.value.length?ji.open:null:s.panel;s=Pe.init(e.value,i,t.state);}else e.is(bo)?s=new Pe(s.diagnostics,e.value?ji.open:null,s.selected):e.is(pf)&&(s=new Pe(s.diagnostics,s.panel,e.value));return s},provide:s=>[vr.from(s,t=>t.panel),O.decorations.from(s,t=>t.diagnostics)]}),uy=I.mark({class:"cm-lintRange cm-lintRange-active"});function dy(s,t,e){let{diagnostics:i}=s.state.field(Ot),n,r=-1,o=-1;i.between(t-(e<0?1:0),t+(e>0?1:0),(a,h,{spec:c})=>{if(t>=a&&t<=h&&(a==h||(t>a||e>0)&&(t<h||e<0)))return n=c.diagnostics,r=a,o=h,!1});let l=s.state.facet(qi).tooltipFilter;return n&&l&&(n=l(n,s.state)),n?{pos:r,end:o,above:s.state.doc.lineAt(r).to<o,create(){return {dom:py(s,n)}}}:null}function py(s,t){return ee("ul",{class:"cm-tooltip-lint"},t.map(e=>gf(s,e,!1)))}const my=s=>{let t=s.state.field(Ot,!1);(!t||!t.panel)&&s.dispatch({effects:fy(s.state,[bo.of(!0)])});let e=bp(s,ji.open);return e&&e.dom.querySelector(".cm-panel-lint ul").focus(),!0},ba=s=>{let t=s.state.field(Ot,!1);return !t||!t.panel?!1:(s.dispatch({effects:bo.of(!1)}),!0)},gy=s=>{let t=s.state.field(Ot,!1);if(!t)return !1;let e=s.state.selection.main,i=t.diagnostics.iter(e.to+1);return !i.value&&(i=t.diagnostics.iter(0),!i.value||i.from==e.from&&i.to==e.to)?!1:(s.dispatch({selection:{anchor:i.from,head:i.to},scrollIntoView:!0}),!0)},yy=[{key:"Mod-Shift-m",run:my,preventDefault:!0},{key:"F8",run:gy}],qi=T.define({combine(s){return Object.assign({sources:s.map(t=>t.source).filter(t=>t!=null)},je(s.map(t=>t.config),{delay:750,markerFilter:null,tooltipFilter:null,needsRefresh:null,hideOn:()=>null},{needsRefresh:(t,e)=>t?e?i=>t(i)||e(i):t:e}))}});function mf(s){let t=[];if(s)t:for(let{name:e}of s){for(let i=0;i<e.length;i++){let n=e[i];if(/[a-zA-Z]/.test(n)&&!t.some(r=>r.toLowerCase()==n.toLowerCase())){t.push(n);continue t}}t.push("");}return t}function gf(s,t,e){var i;let n=e?mf(t.actions):[];return ee("li",{class:"cm-diagnostic cm-diagnostic-"+t.severity},ee("span",{class:"cm-diagnosticText"},t.renderMessage?t.renderMessage(s):t.message),(i=t.actions)===null||i===void 0?void 0:i.map((r,o)=>{let l=!1,a=u=>{if(u.preventDefault(),l)return;l=!0;let d=li(s.state.field(Ot).diagnostics,t);d&&r.apply(s,d.from,d.to);},{name:h}=r,c=n[o]?h.indexOf(n[o]):-1,f=c<0?h:[h.slice(0,c),ee("u",h.slice(c,c+1)),h.slice(c+1)];return ee("button",{type:"button",class:"cm-diagnosticAction",onclick:a,onmousedown:a,"aria-label":` Action: ${h}${c<0?"":` (access key "${n[o]})"`}.`},f)}),t.source&&ee("div",{class:"cm-diagnosticSource"},t.source))}class by extends oe{constructor(t){super(),this.sev=t;}eq(t){return t.sev==this.sev}toDOM(){return ee("span",{class:"cm-lintPoint cm-lintPoint-"+this.sev})}}class wa{constructor(t,e){this.diagnostic=e,this.id="item_"+Math.floor(Math.random()*4294967295).toString(16),this.dom=gf(t,e,!0),this.dom.id=this.id,this.dom.setAttribute("role","option");}}class ji{constructor(t){this.view=t,this.items=[];let e=n=>{if(n.keyCode==27)ba(this.view),this.view.focus();else if(n.keyCode==38||n.keyCode==33)this.moveSelection((this.selectedIndex-1+this.items.length)%this.items.length);else if(n.keyCode==40||n.keyCode==34)this.moveSelection((this.selectedIndex+1)%this.items.length);else if(n.keyCode==36)this.moveSelection(0);else if(n.keyCode==35)this.moveSelection(this.items.length-1);else if(n.keyCode==13)this.view.focus();else if(n.keyCode>=65&&n.keyCode<=90&&this.selectedIndex>=0){let{diagnostic:r}=this.items[this.selectedIndex],o=mf(r.actions);for(let l=0;l<o.length;l++)if(o[l].toUpperCase().charCodeAt(0)==n.keyCode){let a=li(this.view.state.field(Ot).diagnostics,r);a&&r.actions[l].apply(t,a.from,a.to);}}else return;n.preventDefault();},i=n=>{for(let r=0;r<this.items.length;r++)this.items[r].dom.contains(n.target)&&this.moveSelection(r);};this.list=ee("ul",{tabIndex:0,role:"listbox","aria-label":this.view.state.phrase("Diagnostics"),onkeydown:e,onclick:i}),this.dom=ee("div",{class:"cm-panel-lint"},this.list,ee("button",{type:"button",name:"close","aria-label":this.view.state.phrase("close"),onclick:()=>ba(this.view)},"×")),this.update();}get selectedIndex(){let t=this.view.state.field(Ot).selected;if(!t)return -1;for(let e=0;e<this.items.length;e++)if(this.items[e].diagnostic==t.diagnostic)return e;return -1}update(){let{diagnostics:t,selected:e}=this.view.state.field(Ot),i=0,n=!1,r=null,o=new Set;for(t.between(0,this.view.state.doc.length,(l,a,{spec:h})=>{for(let c of h.diagnostics){if(o.has(c))continue;o.add(c);let f=-1,u;for(let d=i;d<this.items.length;d++)if(this.items[d].diagnostic==c){f=d;break}f<0?(u=new wa(this.view,c),this.items.splice(i,0,u),n=!0):(u=this.items[f],f>i&&(this.items.splice(i,f-i),n=!0)),e&&u.diagnostic==e.diagnostic?u.dom.hasAttribute("aria-selected")||(u.dom.setAttribute("aria-selected","true"),r=u):u.dom.hasAttribute("aria-selected")&&u.dom.removeAttribute("aria-selected"),i++;}});i<this.items.length&&!(this.items.length==1&&this.items[0].diagnostic.from<0);)n=!0,this.items.pop();this.items.length==0&&(this.items.push(new wa(this.view,{from:-1,to:-1,severity:"info",message:this.view.state.phrase("No diagnostics")})),n=!0),r?(this.list.setAttribute("aria-activedescendant",r.id),this.view.requestMeasure({key:this,read:()=>({sel:r.dom.getBoundingClientRect(),panel:this.list.getBoundingClientRect()}),write:({sel:l,panel:a})=>{let h=a.height/this.list.offsetHeight;l.top<a.top?this.list.scrollTop-=(a.top-l.top)/h:l.bottom>a.bottom&&(this.list.scrollTop+=(l.bottom-a.bottom)/h);}})):this.selectedIndex<0&&this.list.removeAttribute("aria-activedescendant"),n&&this.sync();}sync(){let t=this.list.firstChild;function e(){let i=t;t=i.nextSibling,i.remove();}for(let i of this.items)if(i.dom.parentNode==this.list){for(;t!=i.dom;)e();t=i.dom.nextSibling;}else this.list.insertBefore(i.dom,t);for(;t;)e();}moveSelection(t){if(this.selectedIndex<0)return;let e=this.view.state.field(Ot),i=li(e.diagnostics,this.items[t].diagnostic);i&&this.view.dispatch({selection:{anchor:i.from,head:i.to},scrollIntoView:!0,effects:pf.of(i)});}static open(t){return new ji(t)}}function wy(s,t='viewBox="0 0 40 40"'){return `url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" ${t}>${encodeURIComponent(s)}</svg>')`}function An(s){return wy(`<path d="m0 2.5 l2 -1.5 l1 0 l2 1.5 l1 0" stroke="${s}" fill="none" stroke-width=".7"/>`,'width="6" height="3"')}const xy=O.baseTheme({".cm-diagnostic":{padding:"3px 6px 3px 8px",marginLeft:"-1px",display:"block",whiteSpace:"pre-wrap"},".cm-diagnostic-error":{borderLeft:"5px solid #d11"},".cm-diagnostic-warning":{borderLeft:"5px solid orange"},".cm-diagnostic-info":{borderLeft:"5px solid #999"},".cm-diagnostic-hint":{borderLeft:"5px solid #66d"},".cm-diagnosticAction":{font:"inherit",border:"none",padding:"2px 4px",backgroundColor:"#444",color:"white",borderRadius:"3px",marginLeft:"8px",cursor:"pointer"},".cm-diagnosticSource":{fontSize:"70%",opacity:.7},".cm-lintRange":{backgroundPosition:"left bottom",backgroundRepeat:"repeat-x",paddingBottom:"0.7px"},".cm-lintRange-error":{backgroundImage:An("#d11")},".cm-lintRange-warning":{backgroundImage:An("orange")},".cm-lintRange-info":{backgroundImage:An("#999")},".cm-lintRange-hint":{backgroundImage:An("#66d")},".cm-lintRange-active":{backgroundColor:"#ffdd9980"},".cm-tooltip-lint":{padding:0,margin:0},".cm-lintPoint":{position:"relative","&:after":{content:'""',position:"absolute",bottom:0,left:"-2px",borderLeft:"3px solid transparent",borderRight:"3px solid transparent",borderBottom:"4px solid #d11"}},".cm-lintPoint-warning":{"&:after":{borderBottomColor:"orange"}},".cm-lintPoint-info":{"&:after":{borderBottomColor:"#999"}},".cm-lintPoint-hint":{"&:after":{borderBottomColor:"#66d"}},".cm-panel.cm-panel-lint":{position:"relative","& ul":{maxHeight:"100px",overflowY:"auto","& [aria-selected]":{backgroundColor:"#ddd","& u":{textDecoration:"underline"}},"&:focus [aria-selected]":{background_fallback:"#bdf",backgroundColor:"Highlight",color_fallback:"white",color:"HighlightText"},"& u":{textDecoration:"none"},padding:0,margin:0},"& [name=close]":{position:"absolute",top:"0",right:"2px",background:"inherit",border:"none",font:"inherit",padding:0,margin:0}}});function vy(s){return s=="error"?4:s=="warning"?3:s=="info"?2:1}function ky(s){let t="hint",e=1;for(let i of s){let n=vy(i.severity);n>e&&(e=n,t=i.severity);}return t}const Sy=[Ot,O.decorations.compute([Ot],s=>{let{selected:t,panel:e}=s.field(Ot);return !t||!e||t.from==t.to?I.none:I.set([uy.range(t.from,t.to)])}),gp(dy,{hideOn:cy}),xy];[Yd(),Xm(),Cm(),Vd(),W.allowMultipleSelections.of(!0),cm(),io(Tm,{fallback:!0}),R0(),rp(),ap(),Ji.of([...H0,...Xg,...rg,...xm,...hf,...yy])];const Ay=["standardSQL","msSQL","mySQL","mariaDB","sqlite","cassandra","plSQL","hive","pgSQL","gql","gpSQL","sparkSQL","esper"],xa={python:()=>import('./index33-mRs5qzQc.js').then(s=>s.python()),c:()=>import('./clike-BmyQvQnm.js').then(s=>Et.define(s.c)),cpp:()=>import('./clike-BmyQvQnm.js').then(s=>Et.define(s.cpp)),markdown:async()=>{const[s,t]=await Promise.all([import('./index34-BpCe0EBP.js'),import('./frontmatter-CX9oWgfC.js')]);return s.markdown({extensions:[t.frontmatter]})},latex:()=>import('./stex-DE2_jxrP.js').then(s=>Et.define(s.stex)),json:()=>import('./index38-B6oGoNTC.js').then(s=>s.json()),html:()=>import('./index35-B_ezRcLy.js').then(s=>s.html()),css:()=>import('./index36-D9QOX6gF.js').then(s=>s.css()),javascript:()=>import('./index37-G-4dJWCi.js').then(s=>s.javascript()),jinja2:()=>import('./jinja2-BOu_N3FT.js').then(s=>Et.define(s.jinja2)),typescript:()=>import('./index37-G-4dJWCi.js').then(s=>s.javascript({typescript:!0})),yaml:()=>import('./yaml-C6fZICTl.js').then(s=>Et.define(s.yaml)),dockerfile:()=>import('./dockerfile-BYGRaj2s.js').then(s=>Et.define(s.dockerFile)),shell:()=>import('./shell-fOTMLIXC.js').then(s=>Et.define(s.shell)),r:()=>import('./r-CxMAnsBX.js').then(s=>Et.define(s.r)),sql:()=>import('./sql-CrwZCygc.js').then(s=>Et.define(s.standardSQL)),...Object.fromEntries(Ay.map(s=>["sql-"+s,()=>import('./sql-CrwZCygc.js').then(t=>Et.define(t[s]))]))},My={py:"python",md:"markdown",js:"javascript",ts:"typescript",sh:"shell"};async function Dy(s){const t=xa[s]||xa[My[s]]||void 0;if(t)return t()}const Ty={module:"namespace"};function Oy(){let s;try{s=f();}catch{return console.debug("Not in the Wasm env. Context-aware autocomplete disabled."),null}if(!s)return null;const t=s;return async function(i){try{const n=await t.getCodeCompletions({code:i.state.doc.toString(),line:i.state.doc.lineAt(i.state.selection.main.head).number,column:i.state.selection.main.head-i.state.doc.lineAt(i.state.selection.main.head).from});return n.length===0?null:{from:i.state.selection.main.head-n[0].completion_prefix_length,options:n.map(r=>({label:r.label,type:Ty[r.type]??r.type,documentation:r.docstring,boost:r.label.startsWith("_")?-1:0}))}}catch(n){return console.error("Error getting completions",n),null}}}const By={code:".wrap.svelte-scxcch{display:flex;flex-direction:column;flex-grow:1;margin:0;padding:0;height:100%}.codemirror-wrapper.svelte-scxcch{flex-grow:1;overflow:auto}.cm-editor{height:100%}.cm-selectionBackground{background-color:#b9d2ff30 !important}.cm-focused{outline:none !important}",map:'{"version":3,"file":"Code.svelte","sources":["Code.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { createEventDispatcher, onMount } from \\"svelte\\";\\nimport { EditorView, ViewUpdate, keymap, placeholder as placeholderExt, lineNumbers } from \\"@codemirror/view\\";\\nimport { StateEffect, EditorState } from \\"@codemirror/state\\";\\nimport { indentWithTab } from \\"@codemirror/commands\\";\\nimport { autocompletion, acceptCompletion } from \\"@codemirror/autocomplete\\";\\nimport { LanguageSupport } from \\"@codemirror/language\\";\\nimport { basicDark } from \\"cm6-theme-basic-dark\\";\\nimport { basicLight } from \\"cm6-theme-basic-light\\";\\nimport { basicSetup } from \\"./extensions\\";\\nimport { getLanguageExtension } from \\"./language\\";\\nimport { create_pyodide_autocomplete } from \\"./autocomplete\\";\\nexport let class_names = \\"\\";\\nexport let value = \\"\\";\\nexport let dark_mode;\\nexport let basic = true;\\nexport let language;\\nexport let lines = 5;\\nexport let max_lines = null;\\nexport let extensions = [];\\nexport let use_tab = true;\\nexport let readonly = false;\\nexport let placeholder = void 0;\\nexport let wrap_lines = false;\\nexport let show_line_numbers = true;\\nexport let autocomplete = false;\\nconst dispatch = createEventDispatcher();\\nlet lang_extension;\\nlet element;\\nlet view;\\n$: get_lang(language);\\nconst pyodide_autocomplete = create_pyodide_autocomplete();\\nasync function get_lang(val) {\\n    const ext = await getLanguageExtension(val);\\n    if (pyodide_autocomplete && val === \\"python\\" && ext instanceof LanguageSupport) {\\n        ext.support.push(ext.language.data.of({ autocomplete: pyodide_autocomplete }));\\n    }\\n    lang_extension = ext;\\n}\\n$: reconfigure(), lang_extension, readonly;\\n$: set_doc(value);\\n$: update_lines();\\nfunction set_doc(new_doc) {\\n    if (view && new_doc !== view.state.doc.toString()) {\\n        view.dispatch({\\n            changes: {\\n                from: 0,\\n                to: view.state.doc.length,\\n                insert: new_doc\\n            }\\n        });\\n    }\\n}\\nfunction update_lines() {\\n    if (view) {\\n        view.requestMeasure({ read: resize });\\n    }\\n}\\nfunction create_editor_view() {\\n    const editorView = new EditorView({\\n        parent: element,\\n        state: create_editor_state(value)\\n    });\\n    editorView.dom.addEventListener(\\"focus\\", handle_focus, true);\\n    editorView.dom.addEventListener(\\"blur\\", handle_blur, true);\\n    return editorView;\\n}\\nfunction handle_focus() {\\n    dispatch(\\"focus\\");\\n}\\nfunction handle_blur() {\\n    dispatch(\\"blur\\");\\n}\\nfunction getGutterLineHeight(_view) {\\n    let elements = _view.dom.querySelectorAll(\\".cm-gutterElement\\");\\n    if (elements.length === 0) {\\n        return null;\\n    }\\n    for (var i = 0; i < elements.length; i++) {\\n        let node = elements[i];\\n        let height = getComputedStyle(node)?.height ?? \\"0px\\";\\n        if (height != \\"0px\\") {\\n            return height;\\n        }\\n    }\\n    return null;\\n}\\nfunction resize(_view) {\\n    let scroller = _view.dom.querySelector(\\".cm-scroller\\");\\n    if (!scroller) {\\n        return null;\\n    }\\n    const lineHeight = getGutterLineHeight(_view);\\n    if (!lineHeight) {\\n        return null;\\n    }\\n    const minLines = lines == 1 ? 1 : lines + 1;\\n    scroller.style.minHeight = `calc(${lineHeight} * ${minLines})`;\\n    if (max_lines)\\n        scroller.style.maxHeight = `calc(${lineHeight} * ${max_lines + 1})`;\\n}\\nfunction handle_change(vu) {\\n    if (vu.docChanged) {\\n        const doc = vu.state.doc;\\n        const text = doc.toString();\\n        value = text;\\n        dispatch(\\"change\\", text);\\n    }\\n    view.requestMeasure({ read: resize });\\n}\\nfunction get_extensions() {\\n    const stateExtensions = [\\n        ...get_base_extensions(basic, use_tab, placeholder, readonly, lang_extension, show_line_numbers),\\n        FontTheme,\\n        ...get_theme(),\\n        ...extensions\\n    ];\\n    return stateExtensions;\\n}\\nconst FontTheme = EditorView.theme({\\n    \\"&\\": {\\n        fontSize: \\"var(--text-sm)\\",\\n        backgroundColor: \\"var(--border-color-secondary)\\"\\n    },\\n    \\".cm-content\\": {\\n        paddingTop: \\"5px\\",\\n        paddingBottom: \\"5px\\",\\n        color: \\"var(--body-text-color)\\",\\n        fontFamily: \\"var(--font-mono)\\",\\n        minHeight: \\"100%\\"\\n    },\\n    \\".cm-gutterElement\\": {\\n        marginRight: \\"var(--spacing-xs)\\"\\n    },\\n    \\".cm-gutters\\": {\\n        marginRight: \\"1px\\",\\n        borderRight: \\"1px solid var(--border-color-primary)\\",\\n        backgroundColor: \\"var(--block-background-fill);\\",\\n        color: \\"var(--body-text-color-subdued)\\"\\n    },\\n    \\".cm-focused\\": {\\n        outline: \\"none\\"\\n    },\\n    \\".cm-scroller\\": {\\n        height: \\"auto\\"\\n    },\\n    \\".cm-cursor\\": {\\n        borderLeftColor: \\"var(--body-text-color)\\"\\n    }\\n});\\nconst AutocompleteTheme = EditorView.theme({\\n    \\".cm-tooltip-autocomplete\\": {\\n        \\"& > ul\\": {\\n            backgroundColor: \\"var(--background-fill-primary)\\",\\n            color: \\"var(--body-text-color)\\"\\n        },\\n        \\"& > ul > li[aria-selected]\\": {\\n            backgroundColor: \\"var(--color-accent-soft)\\",\\n            color: \\"var(--body-text-color)\\"\\n        }\\n    }\\n});\\nfunction create_editor_state(_value) {\\n    return EditorState.create({\\n        doc: _value ?? void 0,\\n        extensions: get_extensions()\\n    });\\n}\\nfunction get_base_extensions(basic2, use_tab2, placeholder2, readonly2, lang, show_line_numbers2) {\\n    const extensions2 = [\\n        EditorView.editable.of(!readonly2),\\n        EditorState.readOnly.of(readonly2),\\n        EditorView.contentAttributes.of({ \\"aria-label\\": \\"Code input container\\" })\\n    ];\\n    if (basic2) {\\n        extensions2.push(basicSetup);\\n    }\\n    if (use_tab2) {\\n        extensions2.push(keymap.of([{ key: \\"Tab\\", run: acceptCompletion }, indentWithTab]));\\n    }\\n    if (placeholder2) {\\n        extensions2.push(placeholderExt(placeholder2));\\n    }\\n    if (lang) {\\n        extensions2.push(lang);\\n    }\\n    if (show_line_numbers2) {\\n        extensions2.push(lineNumbers());\\n    }\\n    if (autocomplete) {\\n        extensions2.push(autocompletion());\\n        extensions2.push(AutocompleteTheme);\\n    }\\n    extensions2.push(EditorView.updateListener.of(handle_change));\\n    if (wrap_lines) {\\n        extensions2.push(EditorView.lineWrapping);\\n    }\\n    return extensions2;\\n}\\nfunction get_theme() {\\n    const extensions2 = [];\\n    if (dark_mode) {\\n        extensions2.push(basicDark);\\n    }\\n    else {\\n        extensions2.push(basicLight);\\n    }\\n    return extensions2;\\n}\\nfunction reconfigure() {\\n    view?.dispatch({\\n        effects: StateEffect.reconfigure.of(get_extensions())\\n    });\\n}\\nonMount(() => {\\n    view = create_editor_view();\\n    return () => view?.destroy();\\n});\\n<\/script>\\n\\n<div class=\\"wrap\\">\\n\\t<div class=\\"codemirror-wrapper {class_names}\\" bind:this={element} />\\n</div>\\n\\n<style>\\n\\t.wrap {\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: column;\\n\\t\\tflex-grow: 1;\\n\\t\\tmargin: 0;\\n\\t\\tpadding: 0;\\n\\t\\theight: 100%;\\n\\t}\\n\\t.codemirror-wrapper {\\n\\t\\tflex-grow: 1;\\n\\t\\toverflow: auto;\\n\\t}\\n\\n\\t:global(.cm-editor) {\\n\\t\\theight: 100%;\\n\\t}\\n\\n\\t/* Dunno why this doesn\'t work through the theme API -- don\'t remove*/\\n\\t:global(.cm-selectionBackground) {\\n\\t\\tbackground-color: #b9d2ff30 !important;\\n\\t}\\n\\n\\t:global(.cm-focused) {\\n\\t\\toutline: none !important;\\n\\t}</style>\\n"],"names":[],"mappings":"AAgOC,mBAAM,CACL,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,SAAS,CAAE,CAAC,CACZ,MAAM,CAAE,CAAC,CACT,OAAO,CAAE,CAAC,CACV,MAAM,CAAE,IACT,CACA,iCAAoB,CACnB,SAAS,CAAE,CAAC,CACZ,QAAQ,CAAE,IACX,CAEQ,UAAY,CACnB,MAAM,CAAE,IACT,CAGQ,uBAAyB,CAChC,gBAAgB,CAAE,SAAS,CAAC,UAC7B,CAEQ,WAAa,CACpB,OAAO,CAAE,IAAI,CAAC,UACf"}'};const Py=create_ssr_component((s,t,e,i)=>{let{class_names:n=""}=t,{value:r=""}=t,{dark_mode:o}=t,{basic:l=!0}=t,{language:a}=t,{lines:h=5}=t,{max_lines:c=null}=t,{extensions:f=[]}=t,{use_tab:u=!0}=t,{readonly:d=!1}=t,{placeholder:p=void 0}=t,{wrap_lines:y=!1}=t,{show_line_numbers:g=!0}=t,{autocomplete:b=!1}=t;createEventDispatcher();let S;const C=Oy();async function A(F){const Q=await Dy(F);C&&F==="python"&&Q instanceof em&&Q.support.push(Q.language.data.of({autocomplete:C}));}O.theme({"&":{fontSize:"var(--text-sm)",backgroundColor:"var(--border-color-secondary)"},".cm-content":{paddingTop:"5px",paddingBottom:"5px",color:"var(--body-text-color)",fontFamily:"var(--font-mono)",minHeight:"100%"},".cm-gutterElement":{marginRight:"var(--spacing-xs)"},".cm-gutters":{marginRight:"1px",borderRight:"1px solid var(--border-color-primary)",backgroundColor:"var(--block-background-fill);",color:"var(--body-text-color-subdued)"},".cm-focused":{outline:"none"},".cm-scroller":{height:"auto"},".cm-cursor":{borderLeftColor:"var(--body-text-color)"}});O.theme({".cm-tooltip-autocomplete":{"& > ul":{backgroundColor:"var(--background-fill-primary)",color:"var(--body-text-color)"},"& > ul > li[aria-selected]":{backgroundColor:"var(--color-accent-soft)",color:"var(--body-text-color)"}}});return t.class_names===void 0&&e.class_names&&n!==void 0&&e.class_names(n),t.value===void 0&&e.value&&r!==void 0&&e.value(r),t.dark_mode===void 0&&e.dark_mode&&o!==void 0&&e.dark_mode(o),t.basic===void 0&&e.basic&&l!==void 0&&e.basic(l),t.language===void 0&&e.language&&a!==void 0&&e.language(a),t.lines===void 0&&e.lines&&h!==void 0&&e.lines(h),t.max_lines===void 0&&e.max_lines&&c!==void 0&&e.max_lines(c),t.extensions===void 0&&e.extensions&&f!==void 0&&e.extensions(f),t.use_tab===void 0&&e.use_tab&&u!==void 0&&e.use_tab(u),t.readonly===void 0&&e.readonly&&d!==void 0&&e.readonly(d),t.placeholder===void 0&&e.placeholder&&p!==void 0&&e.placeholder(p),t.wrap_lines===void 0&&e.wrap_lines&&y!==void 0&&e.wrap_lines(y),t.show_line_numbers===void 0&&e.show_line_numbers&&g!==void 0&&e.show_line_numbers(g),t.autocomplete===void 0&&e.autocomplete&&b!==void 0&&e.autocomplete(b),s.css.add(By),A(a),`<div class="wrap svelte-scxcch"><div class="${"codemirror-wrapper "+escape(n,!0)+" svelte-scxcch"}"${add_attribute("this",S,0)}></div> </div>`}),yf=Py,Ly=create_ssr_component((s,t,e,i)=>{let{value:n}=t;return onDestroy(()=>{}),t.value===void 0&&e.value&&n!==void 0&&e.value(n),`${validate_component(j,"IconButton").$$render(s,{Icon:Gt},{},{})}`}),bf=Ly;function Ry(s){return {py:"py",python:"py",md:"md",markdown:"md",json:"json",html:"html",css:"css",js:"js",javascript:"js",ts:"ts",typescript:"ts",yaml:"yaml",yml:"yml",dockerfile:"dockerfile",sh:"sh",shell:"sh",r:"r",c:"c",cpp:"cpp",latex:"tex"}[s]||"txt"}const Iy=create_ssr_component((s,t,e,i)=>{let n,r,{value:o}=t,{language:l}=t;return onDestroy(()=>{}),t.value===void 0&&e.value&&o!==void 0&&e.value(o),t.language===void 0&&e.language&&l!==void 0&&e.language(l),n=Ry(l),r=URL.createObjectURL(new Blob([o])),`${validate_component(C,"DownloadLink").$$render(s,{download:"file."+n,href:r},{},{default:()=>`${validate_component(j,"IconButton").$$render(s,{Icon:Ut$1},{},{})}`})}`}),wf=Iy,Ny=create_ssr_component((s,t,e,i)=>{let{value:n}=t,{language:r}=t;return t.value===void 0&&e.value&&n!==void 0&&e.value(n),t.language===void 0&&e.language&&r!==void 0&&e.language(r),`${validate_component(Ke$1,"IconButtonWrapper").$$render(s,{},{},{default:()=>`${validate_component(wf,"Download").$$render(s,{value:n,language:r},{},{})} ${validate_component(bf,"Copy").$$render(s,{value:n},{},{})}`})}`}),xf=Ny,Fy=create_ssr_component((s,t,e,i)=>{let{gradio:n}=t,{value:r=""}=t,{value_is_output:o=!1}=t,{language:l=""}=t,{lines:a=5}=t,{max_lines:h=void 0}=t,{elem_id:c=""}=t,{elem_classes:f=[]}=t,{visible:u=!0}=t,{label:d=n.i18n("code.code")}=t,{show_label:p=!0}=t,{loading_status:y}=t,{scale:g=null}=t,{min_width:b=void 0}=t,{wrap_lines:x=!1}=t,{show_line_numbers:k=!0}=t,{autocomplete:S=!1}=t,{interactive:w}=t,C=n.theme==="dark";function A(){n.dispatch("change",r),o||n.dispatch("input");}t.gradio===void 0&&e.gradio&&n!==void 0&&e.gradio(n),t.value===void 0&&e.value&&r!==void 0&&e.value(r),t.value_is_output===void 0&&e.value_is_output&&o!==void 0&&e.value_is_output(o),t.language===void 0&&e.language&&l!==void 0&&e.language(l),t.lines===void 0&&e.lines&&a!==void 0&&e.lines(a),t.max_lines===void 0&&e.max_lines&&h!==void 0&&e.max_lines(h),t.elem_id===void 0&&e.elem_id&&c!==void 0&&e.elem_id(c),t.elem_classes===void 0&&e.elem_classes&&f!==void 0&&e.elem_classes(f),t.visible===void 0&&e.visible&&u!==void 0&&e.visible(u),t.label===void 0&&e.label&&d!==void 0&&e.label(d),t.show_label===void 0&&e.show_label&&p!==void 0&&e.show_label(p),t.loading_status===void 0&&e.loading_status&&y!==void 0&&e.loading_status(y),t.scale===void 0&&e.scale&&g!==void 0&&e.scale(g),t.min_width===void 0&&e.min_width&&b!==void 0&&e.min_width(b),t.wrap_lines===void 0&&e.wrap_lines&&x!==void 0&&e.wrap_lines(x),t.show_line_numbers===void 0&&e.show_line_numbers&&k!==void 0&&e.show_line_numbers(k),t.autocomplete===void 0&&e.autocomplete&&S!==void 0&&e.autocomplete(S),t.interactive===void 0&&e.interactive&&w!==void 0&&e.interactive(w);let E,L,q=s.head;do E=!0,s.head=q,A(),L=`${validate_component(mt,"Block").$$render(s,{height:h&&"fit-content",variant:"solid",padding:!1,elem_id:c,elem_classes:f,visible:u,scale:g,min_width:b},{},{default:()=>`${validate_component(zA,"StatusTracker").$$render(s,Object.assign({},{autoscroll:n.autoscroll},{i18n:n.i18n},y),{},{})} ${p?`${validate_component(bt$1,"BlockLabel").$$render(s,{Icon:jt$1,show_label:p,label:d,float:!1},{},{})}`:""} ${!r&&!w?`${validate_component(kt$1,"Empty").$$render(s,{unpadded_box:!0,size:"large"},{},{default:()=>`${validate_component(jt$1,"CodeIcon").$$render(s,{},{},{})}`})}`:`${validate_component(xf,"Widget").$$render(s,{language:l,value:r},{},{})} ${validate_component(yf,"Code").$$render(s,{language:l,lines:a,max_lines:h,dark_mode:C,wrap_lines:x,show_line_numbers:k,autocomplete:S,readonly:!w,value:r},{value:P=>{r=P,E=!1;}},{})}`}`})}`;while(!E);return L}),eb=Object.freeze(Object.defineProperty({__proto__:null,BaseCode:yf,BaseCopy:bf,BaseDownload:wf,BaseExample:v$1,BaseWidget:xf,default:Fy},Symbol.toStringTag,{value:"Module"}));

export { ri as A, Em as B, Qc as C, Op as D, v as E, O as F, Qy as G, Xy as H, X as I, Zp as J, eb as K, Mr as L, Xr as N, Xh as P, Et as S, G as T, wt as a, N as b, Zy as c, ct as d, ic as e, um as f, Jy as g, em as h, $y as i, $g as j, Uy as k, tb as l, Pt as m, ai as n, qe as o, Gy as p, Ji as q, W as r, Gp as s, m as t, It as u, es as v, $h as w, Re as x, fm as y, ec as z };
//# sourceMappingURL=Index16-BEzKq74l.js.map
