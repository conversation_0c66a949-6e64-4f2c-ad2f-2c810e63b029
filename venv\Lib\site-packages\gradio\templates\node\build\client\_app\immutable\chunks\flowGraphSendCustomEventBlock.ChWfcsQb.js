import{b as n}from"./KHR_interactivity.DEAVS2UW.js";import{R as s}from"./index.BoI39RQH.js";class i extends n{constructor(t){super(t),this.config=t;for(const e in this.config.eventData)this.registerDataInput(e,this.config.eventData[e].type,this.config.eventData[e].value)}_execute(t){const e=this.config.eventId,o={};this.dataInputs.forEach(a=>{o[a.name]=a.getValue(t)}),t.configuration.coordinator.notifyCustomEvent(e,o),this.out._activateSignal(t)}getClassName(){return"FlowGraphReceiveCustomEventBlock"}}s("FlowGraphReceiveCustomEventBlock",i);export{i as FlowGraphSendCustomEventBlock};
//# sourceMappingURL=flowGraphSendCustomEventBlock.ChWfcsQb.js.map
