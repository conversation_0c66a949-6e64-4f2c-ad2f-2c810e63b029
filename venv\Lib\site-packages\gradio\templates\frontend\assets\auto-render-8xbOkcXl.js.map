{"version": 3, "file": "auto-render-8xbOkcXl.js", "sources": ["../../../../node_modules/.pnpm/katex@0.16.10/node_modules/katex/dist/contrib/auto-render.mjs"], "sourcesContent": ["import katex from '../katex.mjs';\n\n/* eslint no-constant-condition:0 */\nvar findEndOfMath = function findEndOfMath(delimiter, text, startIndex) {\n  // Adapted from\n  // https://github.com/Khan/perseus/blob/master/src/perseus-markdown.jsx\n  var index = startIndex;\n  var braceLevel = 0;\n  var delimLength = delimiter.length;\n\n  while (index < text.length) {\n    var character = text[index];\n\n    if (braceLevel <= 0 && text.slice(index, index + delimLength) === delimiter) {\n      return index;\n    } else if (character === \"\\\\\") {\n      index++;\n    } else if (character === \"{\") {\n      braceLevel++;\n    } else if (character === \"}\") {\n      braceLevel--;\n    }\n\n    index++;\n  }\n\n  return -1;\n};\n\nvar escapeRegex = function escapeRegex(string) {\n  return string.replace(/[-/\\\\^$*+?.()|[\\]{}]/g, \"\\\\$&\");\n};\n\nvar amsRegex = /^\\\\begin{/;\n\nvar splitAtDelimiters = function splitAtDelimiters(text, delimiters) {\n  var index;\n  var data = [];\n  var regexLeft = new RegExp(\"(\" + delimiters.map(x => escapeRegex(x.left)).join(\"|\") + \")\");\n\n  while (true) {\n    index = text.search(regexLeft);\n\n    if (index === -1) {\n      break;\n    }\n\n    if (index > 0) {\n      data.push({\n        type: \"text\",\n        data: text.slice(0, index)\n      });\n      text = text.slice(index); // now text starts with delimiter\n    } // ... so this always succeeds:\n\n\n    var i = delimiters.findIndex(delim => text.startsWith(delim.left));\n    index = findEndOfMath(delimiters[i].right, text, delimiters[i].left.length);\n\n    if (index === -1) {\n      break;\n    }\n\n    var rawData = text.slice(0, index + delimiters[i].right.length);\n    var math = amsRegex.test(rawData) ? rawData : text.slice(delimiters[i].left.length, index);\n    data.push({\n      type: \"math\",\n      data: math,\n      rawData,\n      display: delimiters[i].display\n    });\n    text = text.slice(index + delimiters[i].right.length);\n  }\n\n  if (text !== \"\") {\n    data.push({\n      type: \"text\",\n      data: text\n    });\n  }\n\n  return data;\n};\n\n/* eslint no-console:0 */\n/* Note: optionsCopy is mutated by this method. If it is ever exposed in the\n * API, we should copy it before mutating.\n */\n\nvar renderMathInText = function renderMathInText(text, optionsCopy) {\n  var data = splitAtDelimiters(text, optionsCopy.delimiters);\n\n  if (data.length === 1 && data[0].type === 'text') {\n    // There is no formula in the text.\n    // Let's return null which means there is no need to replace\n    // the current text node with a new one.\n    return null;\n  }\n\n  var fragment = document.createDocumentFragment();\n\n  for (var i = 0; i < data.length; i++) {\n    if (data[i].type === \"text\") {\n      fragment.appendChild(document.createTextNode(data[i].data));\n    } else {\n      var span = document.createElement(\"span\");\n      var math = data[i].data; // Override any display mode defined in the settings with that\n      // defined by the text itself\n\n      optionsCopy.displayMode = data[i].display;\n\n      try {\n        if (optionsCopy.preProcess) {\n          math = optionsCopy.preProcess(math);\n        }\n\n        katex.render(math, span, optionsCopy);\n      } catch (e) {\n        if (!(e instanceof katex.ParseError)) {\n          throw e;\n        }\n\n        optionsCopy.errorCallback(\"KaTeX auto-render: Failed to parse `\" + data[i].data + \"` with \", e);\n        fragment.appendChild(document.createTextNode(data[i].rawData));\n        continue;\n      }\n\n      fragment.appendChild(span);\n    }\n  }\n\n  return fragment;\n};\n\nvar renderElem = function renderElem(elem, optionsCopy) {\n  for (var i = 0; i < elem.childNodes.length; i++) {\n    var childNode = elem.childNodes[i];\n\n    if (childNode.nodeType === 3) {\n      // Text node\n      // Concatenate all sibling text nodes.\n      // Webkit browsers split very large text nodes into smaller ones,\n      // so the delimiters may be split across different nodes.\n      var textContentConcat = childNode.textContent;\n      var sibling = childNode.nextSibling;\n      var nSiblings = 0;\n\n      while (sibling && sibling.nodeType === Node.TEXT_NODE) {\n        textContentConcat += sibling.textContent;\n        sibling = sibling.nextSibling;\n        nSiblings++;\n      }\n\n      var frag = renderMathInText(textContentConcat, optionsCopy);\n\n      if (frag) {\n        // Remove extra text nodes\n        for (var j = 0; j < nSiblings; j++) {\n          childNode.nextSibling.remove();\n        }\n\n        i += frag.childNodes.length - 1;\n        elem.replaceChild(frag, childNode);\n      } else {\n        // If the concatenated text does not contain math\n        // the siblings will not either\n        i += nSiblings;\n      }\n    } else if (childNode.nodeType === 1) {\n      (function () {\n        // Element node\n        var className = ' ' + childNode.className + ' ';\n        var shouldRender = optionsCopy.ignoredTags.indexOf(childNode.nodeName.toLowerCase()) === -1 && optionsCopy.ignoredClasses.every(x => className.indexOf(' ' + x + ' ') === -1);\n\n        if (shouldRender) {\n          renderElem(childNode, optionsCopy);\n        }\n      })();\n    } // Otherwise, it's something else, and ignore it.\n\n  }\n};\n\nvar renderMathInElement = function renderMathInElement(elem, options) {\n  if (!elem) {\n    throw new Error(\"No element provided to render\");\n  }\n\n  var optionsCopy = {}; // Object.assign(optionsCopy, option)\n\n  for (var option in options) {\n    if (options.hasOwnProperty(option)) {\n      optionsCopy[option] = options[option];\n    }\n  } // default options\n\n\n  optionsCopy.delimiters = optionsCopy.delimiters || [{\n    left: \"$$\",\n    right: \"$$\",\n    display: true\n  }, {\n    left: \"\\\\(\",\n    right: \"\\\\)\",\n    display: false\n  }, // LaTeX uses $…$, but it ruins the display of normal `$` in text:\n  // {left: \"$\", right: \"$\", display: false},\n  // $ must come after $$\n  // Render AMS environments even if outside $$…$$ delimiters.\n  {\n    left: \"\\\\begin{equation}\",\n    right: \"\\\\end{equation}\",\n    display: true\n  }, {\n    left: \"\\\\begin{align}\",\n    right: \"\\\\end{align}\",\n    display: true\n  }, {\n    left: \"\\\\begin{alignat}\",\n    right: \"\\\\end{alignat}\",\n    display: true\n  }, {\n    left: \"\\\\begin{gather}\",\n    right: \"\\\\end{gather}\",\n    display: true\n  }, {\n    left: \"\\\\begin{CD}\",\n    right: \"\\\\end{CD}\",\n    display: true\n  }, {\n    left: \"\\\\[\",\n    right: \"\\\\]\",\n    display: true\n  }];\n  optionsCopy.ignoredTags = optionsCopy.ignoredTags || [\"script\", \"noscript\", \"style\", \"textarea\", \"pre\", \"code\", \"option\"];\n  optionsCopy.ignoredClasses = optionsCopy.ignoredClasses || [];\n  optionsCopy.errorCallback = optionsCopy.errorCallback || console.error; // Enable sharing of global macros defined via `\\gdef` between different\n  // math elements within a single call to `renderMathInElement`.\n\n  optionsCopy.macros = optionsCopy.macros || {};\n  renderElem(elem, optionsCopy);\n};\n\nexport { renderMathInElement as default };\n"], "names": ["findEndOfMath", "delimiter", "text", "startIndex", "index", "braceLevel", "delimLength", "character", "escapeRegex", "string", "amsRegex", "splitAtDelimiters", "delimiters", "data", "regexLeft", "x", "i", "delim", "rawData", "math", "renderMathInText", "optionsCopy", "fragment", "span", "katex", "e", "renderElem", "elem", "childNode", "textContentConcat", "sibling", "nSiblings", "frag", "j", "className", "shouldRender", "renderMathInElement", "options", "option"], "mappings": "mCAGA,IAAIA,EAAgB,SAAuBC,EAAWC,EAAMC,EAAY,CAOtE,QAJIC,EAAQD,EACRE,EAAa,EACbC,EAAcL,EAAU,OAErBG,EAAQF,EAAK,QAAQ,CAC1B,IAAIK,EAAYL,EAAKE,CAAK,EAE1B,GAAIC,GAAc,GAAKH,EAAK,MAAME,EAAOA,EAAQE,CAAW,IAAML,EAChE,OAAOG,EACEG,IAAc,KACvBH,IACSG,IAAc,IACvBF,IACSE,IAAc,KACvBF,IAGFD,GACD,CAED,MAAO,EACT,EAEII,EAAc,SAAqBC,EAAQ,CAC7C,OAAOA,EAAO,QAAQ,wBAAyB,MAAM,CACvD,EAEIC,EAAW,YAEXC,EAAoB,SAA2BT,EAAMU,EAAY,CAKnE,QAJIR,EACAS,EAAO,CAAA,EACPC,EAAY,IAAI,OAAO,IAAMF,EAAW,IAAIG,GAAKP,EAAYO,EAAE,IAAI,CAAC,EAAE,KAAK,GAAG,EAAI,GAAG,EAGvFX,EAAQF,EAAK,OAAOY,CAAS,EAEzBV,IAAU,IAHH,CAOPA,EAAQ,IACVS,EAAK,KAAK,CACR,KAAM,OACN,KAAMX,EAAK,MAAM,EAAGE,CAAK,CACjC,CAAO,EACDF,EAAOA,EAAK,MAAME,CAAK,GAIzB,IAAIY,EAAIJ,EAAW,UAAUK,GAASf,EAAK,WAAWe,EAAM,IAAI,CAAC,EAGjE,GAFAb,EAAQJ,EAAcY,EAAWI,CAAC,EAAE,MAAOd,EAAMU,EAAWI,CAAC,EAAE,KAAK,MAAM,EAEtEZ,IAAU,GACZ,MAGF,IAAIc,EAAUhB,EAAK,MAAM,EAAGE,EAAQQ,EAAWI,CAAC,EAAE,MAAM,MAAM,EAC1DG,EAAOT,EAAS,KAAKQ,CAAO,EAAIA,EAAUhB,EAAK,MAAMU,EAAWI,CAAC,EAAE,KAAK,OAAQZ,CAAK,EACzFS,EAAK,KAAK,CACR,KAAM,OACN,KAAMM,EACN,QAAAD,EACA,QAASN,EAAWI,CAAC,EAAE,OAC7B,CAAK,EACDd,EAAOA,EAAK,MAAME,EAAQQ,EAAWI,CAAC,EAAE,MAAM,MAAM,CACrD,CAED,OAAId,IAAS,IACXW,EAAK,KAAK,CACR,KAAM,OACN,KAAMX,CACZ,CAAK,EAGIW,CACT,EAOIO,EAAmB,SAA0BlB,EAAMmB,EAAa,CAClE,IAAIR,EAAOF,EAAkBT,EAAMmB,EAAY,UAAU,EAEzD,GAAIR,EAAK,SAAW,GAAKA,EAAK,CAAC,EAAE,OAAS,OAIxC,OAAO,KAKT,QAFIS,EAAW,SAAS,yBAEfN,EAAI,EAAGA,EAAIH,EAAK,OAAQG,IAC/B,GAAIH,EAAKG,CAAC,EAAE,OAAS,OACnBM,EAAS,YAAY,SAAS,eAAeT,EAAKG,CAAC,EAAE,IAAI,CAAC,MACrD,CACL,IAAIO,EAAO,SAAS,cAAc,MAAM,EACpCJ,EAAON,EAAKG,CAAC,EAAE,KAGnBK,EAAY,YAAcR,EAAKG,CAAC,EAAE,QAElC,GAAI,CACEK,EAAY,aACdF,EAAOE,EAAY,WAAWF,CAAI,GAGpCK,EAAM,OAAOL,EAAMI,EAAMF,CAAW,CACrC,OAAQI,EAAG,CACV,GAAI,EAAEA,aAAaD,EAAM,YACvB,MAAMC,EAGRJ,EAAY,cAAc,uCAAyCR,EAAKG,CAAC,EAAE,KAAO,UAAWS,CAAC,EAC9FH,EAAS,YAAY,SAAS,eAAeT,EAAKG,CAAC,EAAE,OAAO,CAAC,EAC7D,QACD,CAEDM,EAAS,YAAYC,CAAI,CAC1B,CAGH,OAAOD,CACT,EAEII,EAAa,SAASA,EAAWC,EAAMN,EAAa,CACtD,QAASL,EAAI,EAAGA,EAAIW,EAAK,WAAW,OAAQX,IAAK,CAC/C,IAAIY,EAAYD,EAAK,WAAWX,CAAC,EAEjC,GAAIY,EAAU,WAAa,EAAG,CAS5B,QAJIC,EAAoBD,EAAU,YAC9BE,EAAUF,EAAU,YACpBG,EAAY,EAETD,GAAWA,EAAQ,WAAa,KAAK,WAC1CD,GAAqBC,EAAQ,YAC7BA,EAAUA,EAAQ,YAClBC,IAGF,IAAIC,EAAOZ,EAAiBS,EAAmBR,CAAW,EAE1D,GAAIW,EAAM,CAER,QAASC,EAAI,EAAGA,EAAIF,EAAWE,IAC7BL,EAAU,YAAY,SAGxBZ,GAAKgB,EAAK,WAAW,OAAS,EAC9BL,EAAK,aAAaK,EAAMJ,CAAS,CACzC,MAGQZ,GAAKe,CAEb,MAAeH,EAAU,WAAa,GAC/B,UAAY,CAEX,IAAIM,EAAY,IAAMN,EAAU,UAAY,IACxCO,EAAed,EAAY,YAAY,QAAQO,EAAU,SAAS,YAAW,CAAE,IAAM,IAAMP,EAAY,eAAe,MAAMN,GAAKmB,EAAU,QAAQ,IAAMnB,EAAI,GAAG,IAAM,EAAE,EAExKoB,GACFT,EAAWE,EAAWP,CAAW,CAE3C,GAGG,CACH,EAEIe,EAAsB,SAA6BT,EAAMU,EAAS,CACpE,GAAI,CAACV,EACH,MAAM,IAAI,MAAM,+BAA+B,EAGjD,IAAIN,EAAc,CAAA,EAElB,QAASiB,KAAUD,EACbA,EAAQ,eAAeC,CAAM,IAC/BjB,EAAYiB,CAAM,EAAID,EAAQC,CAAM,GAKxCjB,EAAY,WAAaA,EAAY,YAAc,CAAC,CAClD,KAAM,KACN,MAAO,KACP,QAAS,EACb,EAAK,CACD,KAAM,MACN,MAAO,MACP,QAAS,EACV,EAID,CACE,KAAM,oBACN,MAAO,kBACP,QAAS,EACb,EAAK,CACD,KAAM,iBACN,MAAO,eACP,QAAS,EACb,EAAK,CACD,KAAM,mBACN,MAAO,iBACP,QAAS,EACb,EAAK,CACD,KAAM,kBACN,MAAO,gBACP,QAAS,EACb,EAAK,CACD,KAAM,cACN,MAAO,YACP,QAAS,EACb,EAAK,CACD,KAAM,MACN,MAAO,MACP,QAAS,EACb,CAAG,EACDA,EAAY,YAAcA,EAAY,aAAe,CAAC,SAAU,WAAY,QAAS,WAAY,MAAO,OAAQ,QAAQ,EACxHA,EAAY,eAAiBA,EAAY,gBAAkB,CAAA,EAC3DA,EAAY,cAAgBA,EAAY,eAAiB,QAAQ,MAGjEA,EAAY,OAASA,EAAY,QAAU,CAAA,EAC3CK,EAAWC,EAAMN,CAAW,CAC9B", "x_google_ignoreList": [0]}