{"version": 3, "file": "fr-Doonyoep.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/fr.js"], "sourcesContent": ["const e=\"Français\",o={annotated_image:\"Image annotée\"},r={allow_recording_access:\"Veuillez autoriser l'accès au microphone pour l'enregistrement.\",audio:\"Audio\",record_from_microphone:\"Enregistrer depuis le microphone\",stop_recording:\"Arrêter l'enregistrement\",no_device_support:\"Impossible d'accéder aux périphériques multimédias. Assurez-vous que vous êtes sur une source sécurisée (https) ou localhost (ou avez passé un certificat SSL valide à ssl_verify), et que vous avez autorisé l'accès du navigateur à votre appareil.\",stop:\"Arrêter\",resume:\"Reprendre\",record:\"Enregistrer\",no_microphone:\"Aucun microphone trouvé\",pause:\"Pause\",play:\"Lecture\",waiting:\"En attente\",drop_to_upload:\"Déposez un fichier audio ici pour le télécharger\"},i={connection_can_break:\"Sur mobile, la connexion peut être interrompue si cet onglet perd le focus ou si l'appareil se met en veille, perdant votre position dans la file d'attente.\",long_requests_queue:\"Il y a une longue file d'attente de requêtes en attente. Dupliquez ce Space pour la contourner.\",lost_connection:\"Connexion perdue en quittant la page. Retour à la file d'attente...\",waiting_for_inputs:\"En attente de la fin du téléchargement des fichier(s), veuillez réessayer.\"},a={checkbox:\"Case à cocher\",checkbox_group:\"Groupe de cases à cocher\"},n={code:\"Code\"},t={color_picker:\"Sélecteur de couleur\"},c={built_with:\"créé avec\",built_with_gradio:\"Créé avec Gradio\",clear:\"Effacer\",download:\"Télécharger\",edit:\"Modifier\",empty:\"Vide\",flag:\"Marquer\",error:\"Erreur\",hosted_on:\"Hébergé sur\",loading:\"Chargement\",logo:\"logo\",or:\"ou\",remove:\"Supprimer\",settings:\"Paramètres\",share:\"Partager\",submit:\"Envoyer\",undo:\"Annuler\",no_devices:\"Aucun périphérique trouvé\",language:\"Langue\",display_theme:\"Thème d'affichage\",pwa:\"Application web progressive\",run:\"Exécuter\"},s={incorrect_format:\"Format incorrect, seuls les fichiers CSV et TSV sont pris en charge\",new_column:\"Ajouter une colonne\",new_row:\"Nouvelle ligne\",add_row_above:\"Ajouter une ligne au-dessus\",add_row_below:\"Ajouter une ligne en dessous\",add_column_left:\"Ajouter une colonne à gauche\",add_column_right:\"Ajouter une colonne à droite\",delete_row:\"Supprimer la ligne\",delete_column:\"Supprimer la colonne\",sort_column:\"Trier la colonne\",sort_ascending:\"Trier par ordre croissant\",sort_descending:\"Trier par ordre décroissant\",drop_to_upload:\"Déposez ici les fichiers CSV ou TSV pour importer les données dans le dataframe.\",clear_sort:\"Effacer le tri\"},l={dropdown:\"Menu déroulant\"},u={build_error:\"il y a une erreur de construction\",config_error:\"il y a une erreur de configuration\",contact_page_author:\"Veuillez contacter l'auteur de la page pour l'informer.\",no_app_file:\"il n'y a pas de fichier d'application\",runtime_error:\"il y a une erreur d'exécution\",space_not_working:'\"Le Space ne fonctionne pas car\" {0}',space_paused:\"le Space est en pause\",use_via_api:\"Utiliser via API\",use_via_api_or_mcp:\"Utiliser via API ou MCP\"},d={uploading:\"Téléchargement...\"},p={highlighted_text:\"Texte surligné\"},_={allow_webcam_access:\"Veuillez autoriser l'accès à la webcam pour l'enregistrement.\",brush_color:\"Couleur du pinceau\",brush_radius:\"Taille du pinceau\",image:\"Image\",remove_image:\"Supprimer l'image\",select_brush_color:\"Sélectionner la couleur du pinceau\",start_drawing:\"Commencer à dessiner\",use_brush:\"Utiliser le pinceau\",drop_to_upload:\"Déposez un fichier image ici pour télécharger\"},g={label:\"Étiquette\"},m={enable_cookies:\"Si vous visitez un Space HuggingFace en mode Incognito, vous devez activer les cookies tiers.\",incorrect_credentials:\"Identifiants incorrects\",username:\"nom d'utilisateur\",password:\"mot de passe\",login:\"Connexion\"},h={number:\"Nombre\"},f={plot:\"Graphique\"},v={radio:\"Bouton radio\"},b={slider:\"Curseur\"},w={click_to_upload:\"Cliquez pour télécharger un fichier\",drop_audio:\"Déposez l'audio ici\",drop_csv:\"Déposez le fichier CSV ici\",drop_file:\"Déposez le fichier ici\",drop_image:\"Déposez l'image ici\",drop_video:\"Déposez la vidéo ici\",drop_gallery:\"Déposez les médias ici\",paste_clipboard:\"Coller depuis le presse-papiers\"},z={drop_to_upload:\"Déposez un fichier vidéo ici pour le télécharger\"},S={edit:\"Modifier\",retry:\"Réessayer\",undo:\"Annuler\",submit:\"Envoyer\",cancel:\"Annuler\",like:\"J'aime\",dislike:\"Je n'aime pas\",clear:\"Effacer la conversation\"},x={_name:e,\"3D_model\":{\"3d_model\":\"Modèle 3D\",drop_to_upload:\"Déposez un fichier image ici pour le télécharger\"},annotated_image:o,audio:r,blocks:i,checkbox:a,code:n,color_picker:t,common:c,dataframe:s,dropdown:l,errors:u,file:d,highlighted_text:p,image:_,label:g,login:m,number:h,plot:f,radio:v,slider:b,upload_text:w,video:z,chatbot:S};export{e as _name,o as annotated_image,r as audio,i as blocks,S as chatbot,a as checkbox,n as code,t as color_picker,c as common,s as dataframe,x as default,l as dropdown,u as errors,d as file,p as highlighted_text,_ as image,g as label,m as login,h as number,f as plot,v as radio,b as slider,w as upload_text,z as video};\n//# sourceMappingURL=fr.js.map\n"], "names": [], "mappings": "AAAK,MAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,iEAAiE,CAAC,KAAK,CAAC,OAAO,CAAC,sBAAsB,CAAC,kCAAkC,CAAC,cAAc,CAAC,0BAA0B,CAAC,iBAAiB,CAAC,uPAAuP,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,yBAAyB,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,kDAAkD,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,8JAA8J,CAAC,mBAAmB,CAAC,iGAAiG,CAAC,eAAe,CAAC,qEAAqE,CAAC,kBAAkB,CAAC,4EAA4E,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,2BAA2B,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,mBAAmB,CAAC,GAAG,CAAC,6BAA6B,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,qEAAqE,CAAC,UAAU,CAAC,qBAAqB,CAAC,OAAO,CAAC,gBAAgB,CAAC,aAAa,CAAC,6BAA6B,CAAC,aAAa,CAAC,8BAA8B,CAAC,eAAe,CAAC,8BAA8B,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,UAAU,CAAC,oBAAoB,CAAC,aAAa,CAAC,sBAAsB,CAAC,WAAW,CAAC,kBAAkB,CAAC,cAAc,CAAC,2BAA2B,CAAC,eAAe,CAAC,6BAA6B,CAAC,cAAc,CAAC,kFAAkF,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,mCAAmC,CAAC,YAAY,CAAC,oCAAoC,CAAC,mBAAmB,CAAC,yDAAyD,CAAC,WAAW,CAAC,uCAAuC,CAAC,aAAa,CAAC,+BAA+B,CAAC,iBAAiB,CAAC,sCAAsC,CAAC,YAAY,CAAC,uBAAuB,CAAC,WAAW,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,+DAA+D,CAAC,WAAW,CAAC,oBAAoB,CAAC,YAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,oCAAoC,CAAC,aAAa,CAAC,sBAAsB,CAAC,SAAS,CAAC,qBAAqB,CAAC,cAAc,CAAC,+CAA+C,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,+FAA+F,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,QAAQ,CAAC,mBAAmB,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,qCAAqC,CAAC,UAAU,CAAC,qBAAqB,CAAC,QAAQ,CAAC,4BAA4B,CAAC,SAAS,CAAC,wBAAwB,CAAC,UAAU,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,YAAY,CAAC,wBAAwB,CAAC,eAAe,CAAC,iCAAiC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,kDAAkD,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,cAAc,CAAC,kDAAkD,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;;;;"}