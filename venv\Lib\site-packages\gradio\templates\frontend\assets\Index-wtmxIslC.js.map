{"version": 3, "file": "Index-wtmxIslC.js", "sources": ["../../../../js/annotatedimage/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\n\timport { onMount } from \"svelte\";\n\timport {\n\t\tBlock,\n\t\tBlockLabel,\n\t\tEmpty,\n\t\tIconButtonWrapper,\n\t\tFullscreenButton\n\t} from \"@gradio/atoms\";\n\timport { Image, Maximize, Minimize } from \"@gradio/icons\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { type FileData } from \"@gradio/client\";\n\timport { resolve_wasm_src } from \"@gradio/wasm/svelte\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: {\n\t\timage: FileData;\n\t\tannotations: { image: FileData; label: string }[] | [];\n\t} | null = null;\n\tlet old_value: {\n\t\timage: FileData;\n\t\tannotations: { image: FileData; label: string }[] | [];\n\t} | null = null;\n\tlet _value: {\n\t\timage: FileData;\n\t\tannotations: { image: FileData; label: string }[];\n\t} | null = null;\n\texport let gradio: Gradio<{\n\t\tchange: undefined;\n\t\tselect: SelectData;\n\t}>;\n\texport let label = gradio.i18n(\"annotated_image.annotated_image\");\n\texport let show_label = true;\n\texport let show_legend = true;\n\texport let height: number | undefined;\n\texport let width: number | undefined;\n\texport let color_map: Record<string, string>;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\tlet active: string | null = null;\n\texport let loading_status: LoadingStatus;\n\texport let show_fullscreen_button = true;\n\n\tlet image_container: HTMLElement;\n\tlet fullscreen = false;\n\n\t// `value` can be updated before the Promises from `resolve_wasm_src` are resolved.\n\t// In such a case, the resolved values for the old `value` have to be discarded,\n\t// This variable `latest_promise` is used to pick up only the values resolved for the latest `value`.\n\tlet latest_promise: Promise<unknown> | null = null;\n\t$: {\n\t\tif (value !== old_value) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}\n\t\tif (value) {\n\t\t\tconst normalized_value = {\n\t\t\t\timage: value.image as FileData,\n\t\t\t\tannotations: value.annotations.map((ann) => ({\n\t\t\t\t\timage: ann.image as FileData,\n\t\t\t\t\tlabel: ann.label\n\t\t\t\t}))\n\t\t\t};\n\t\t\t_value = normalized_value;\n\n\t\t\t// In normal (non-Wasm) Gradio, the `<img>` element should be rendered with the passed values immediately\n\t\t\t// without waiting for `resolve_wasm_src()` to resolve.\n\t\t\t// If it waits, a blank image is displayed until the async task finishes\n\t\t\t// and it leads to undesirable flickering.\n\t\t\t// So set `_value` immediately above, and update it with the resolved values below later.\n\t\t\tconst image_url_promise = resolve_wasm_src(normalized_value.image.url);\n\t\t\tconst annotation_urls_promise = Promise.all(\n\t\t\t\tnormalized_value.annotations.map((ann) =>\n\t\t\t\t\tresolve_wasm_src(ann.image.url)\n\t\t\t\t)\n\t\t\t);\n\t\t\tconst current_promise = Promise.all([\n\t\t\t\timage_url_promise,\n\t\t\t\tannotation_urls_promise\n\t\t\t]);\n\t\t\tlatest_promise = current_promise;\n\t\t\tcurrent_promise.then(([image_url, annotation_urls]) => {\n\t\t\t\tif (latest_promise !== current_promise) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tconst async_resolved_value: typeof _value = {\n\t\t\t\t\timage: {\n\t\t\t\t\t\t...normalized_value.image,\n\t\t\t\t\t\turl: image_url ?? undefined\n\t\t\t\t\t},\n\t\t\t\t\tannotations: normalized_value.annotations.map((ann, i) => ({\n\t\t\t\t\t\t...ann,\n\t\t\t\t\t\timage: {\n\t\t\t\t\t\t\t...ann.image,\n\t\t\t\t\t\t\turl: annotation_urls[i] ?? undefined\n\t\t\t\t\t\t}\n\t\t\t\t\t}))\n\t\t\t\t};\n\t\t\t\t_value = async_resolved_value;\n\t\t\t});\n\t\t} else {\n\t\t\t_value = null;\n\t\t}\n\t}\n\tfunction handle_mouseover(_label: string): void {\n\t\tactive = _label;\n\t}\n\tfunction handle_mouseout(): void {\n\t\tactive = null;\n\t}\n\n\tfunction handle_click(i: number, value: string): void {\n\t\tgradio.dispatch(\"select\", {\n\t\t\tvalue: label,\n\t\t\tindex: i\n\t\t});\n\t}\n</script>\n\n<Block\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\tpadding={false}\n\t{height}\n\t{width}\n\tallow_overflow={false}\n\t{container}\n\t{scale}\n\t{min_width}\n\tbind:fullscreen\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t/>\n\t<BlockLabel\n\t\t{show_label}\n\t\tIcon={Image}\n\t\tlabel={label || gradio.i18n(\"image.image\")}\n\t/>\n\n\t<div class=\"container\">\n\t\t{#if _value == null}\n\t\t\t<Empty size=\"large\" unpadded_box={true}><Image /></Empty>\n\t\t{:else}\n\t\t\t<div class=\"image-container\" bind:this={image_container}>\n\t\t\t\t<IconButtonWrapper>\n\t\t\t\t\t{#if show_fullscreen_button}\n\t\t\t\t\t\t<FullscreenButton\n\t\t\t\t\t\t\t{fullscreen}\n\t\t\t\t\t\t\ton:fullscreen={({ detail }) => {\n\t\t\t\t\t\t\t\tfullscreen = detail;\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/if}\n\t\t\t\t</IconButtonWrapper>\n\n\t\t\t\t<img\n\t\t\t\t\tclass=\"base-image\"\n\t\t\t\t\tclass:fit-height={height && !fullscreen}\n\t\t\t\t\tsrc={_value ? _value.image.url : null}\n\t\t\t\t\talt=\"the base file that is annotated\"\n\t\t\t\t/>\n\t\t\t\t{#each _value ? _value?.annotations : [] as ann, i}\n\t\t\t\t\t<img\n\t\t\t\t\t\talt=\"segmentation mask identifying {label} within the uploaded file\"\n\t\t\t\t\t\tclass=\"mask fit-height\"\n\t\t\t\t\t\tclass:fit-height={!fullscreen}\n\t\t\t\t\t\tclass:active={active == ann.label}\n\t\t\t\t\t\tclass:inactive={active != ann.label && active != null}\n\t\t\t\t\t\tsrc={ann.image.url}\n\t\t\t\t\t\tstyle={color_map && ann.label in color_map\n\t\t\t\t\t\t\t? null\n\t\t\t\t\t\t\t: `filter: hue-rotate(${Math.round(\n\t\t\t\t\t\t\t\t\t(i * 360) / _value?.annotations.length\n\t\t\t\t\t\t\t\t)}deg);`}\n\t\t\t\t\t/>\n\t\t\t\t{/each}\n\t\t\t</div>\n\t\t\t{#if show_legend && _value}\n\t\t\t\t<div class=\"legend\">\n\t\t\t\t\t{#each _value.annotations as ann, i}\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"legend-item\"\n\t\t\t\t\t\t\tstyle=\"background-color: {color_map && ann.label in color_map\n\t\t\t\t\t\t\t\t? color_map[ann.label] + '88'\n\t\t\t\t\t\t\t\t: `hsla(${Math.round(\n\t\t\t\t\t\t\t\t\t\t(i * 360) / _value.annotations.length\n\t\t\t\t\t\t\t\t\t)}, 100%, 50%, 0.3)`}\"\n\t\t\t\t\t\t\ton:mouseover={() => handle_mouseover(ann.label)}\n\t\t\t\t\t\t\ton:focus={() => handle_mouseover(ann.label)}\n\t\t\t\t\t\t\ton:mouseout={() => handle_mouseout()}\n\t\t\t\t\t\t\ton:blur={() => handle_mouseout()}\n\t\t\t\t\t\t\ton:click={() => handle_click(i, ann.label)}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{ann.label}\n\t\t\t\t\t\t</button>\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t{/if}\n\t</div>\n</Block>\n\n<style>\n\t.base-image {\n\t\tdisplay: block;\n\t\twidth: 100%;\n\t\theight: auto;\n\t}\n\t.container {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\t.image-container {\n\t\tposition: relative;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tflex-grow: 1;\n\t\twidth: 100%;\n\t\toverflow: hidden;\n\t}\n\t.fit-height {\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: contain;\n\t}\n\t.mask {\n\t\topacity: 0.85;\n\t\ttransition: all 0.2s ease-in-out;\n\t\tposition: absolute;\n\t}\n\t.image-container:hover .mask {\n\t\topacity: 0.3;\n\t}\n\t.mask.active {\n\t\topacity: 1;\n\t}\n\t.mask.inactive {\n\t\topacity: 0;\n\t}\n\t.legend {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tflex-wrap: wrap;\n\t\talign-content: center;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tgap: var(--spacing-sm);\n\t\tpadding: var(--spacing-sm);\n\t}\n\t.legend-item {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tcursor: pointer;\n\t\tborder-radius: var(--radius-sm);\n\t\tpadding: var(--spacing-sm);\n\t}\n</style>\n"], "names": ["ctx", "i", "if_block", "create_if_block_1", "src_url_equal", "img", "img_src_value", "attr", "toggle_class", "insert", "target", "div", "anchor", "append", "current", "dirty", "create_if_block_2", "img_style_value", "each_value", "ensure_array_like", "t0_value", "set_style", "button", "set_data", "t0", "Image", "blocklabel_changes", "elem_id", "$$props", "elem_classes", "visible", "value", "old_value", "_value", "gradio", "label", "show_label", "show_legend", "height", "width", "color_map", "container", "scale", "min_width", "active", "loading_status", "show_fullscreen_button", "image_container", "fullscreen", "latest_promise", "handle_mouseover", "_label", "handle_mouseout", "handle_click", "detail", "$$invalidate", "$$value", "ann", "click_handler", "normalized_value", "image_url_promise", "resolve_wasm_src", "annotation_urls_promise", "current_promise", "image_url", "annotation_urls", "async_resolved_value"], "mappings": "i+CA2KWA,EAAM,EAAA,EAAGA,OAAQ,qCAAtB,OAAIC,GAAA,oBAgBF,IAAAC,EAAAF,MAAeA,EAAM,EAAA,GAAAG,GAAAH,CAAA,mKAnBnBI,EAAAC,EAAA,IAAAC,EAAAN,MAASA,EAAM,EAAA,EAAC,MAAM,IAAM,IAAI,GAAAO,EAAAF,EAAA,MAAAC,CAAA,+CADnBE,EAAAH,EAAA,aAAAL,OAAWA,EAAU,EAAA,CAAA,uDAdzCS,EAiCKC,EAAAC,EAAAC,CAAA,qBArBJC,EAKCF,EAAAN,CAAA,wLAFK,CAAAS,GAAAC,EAAA,CAAA,EAAA,OAAA,CAAAX,EAAAC,EAAA,IAAAC,EAAAN,MAASA,EAAM,EAAA,EAAC,MAAM,IAAM,IAAI,oCADnBQ,EAAAH,EAAA,aAAAL,OAAWA,EAAU,EAAA,CAAA,mBAIjCA,EAAM,EAAA,EAAGA,OAAQ,kCAAtB,OAAIC,GAAA,EAAA,kHAAJ,OAgBED,MAAeA,EAAM,EAAA,6QApCQ,2hBAI3BA,EAAsB,EAAA,GAAAgB,EAAAhB,CAAA,uEAAtBA,EAAsB,EAAA,8QAkBUA,EAAK,CAAA,EAAA,2BAAA,yDAKpCA,EAAG,EAAA,EAAC,MAAM,GAAG,GAAAO,EAAAF,EAAA,MAAAC,CAAA,EACXC,EAAAF,EAAA,QAAAY,EAAAjB,EAAa,CAAA,GAAAA,EAAI,EAAA,EAAA,SAASA,EAAA,CAAA,EAC9B,2BACsB,KAAK,MAC1BA,EAAC,EAAA,EAAG,IAAOA,EAAQ,EAAA,GAAA,YAAY,MAAA,CAAA,OAAA,oBAPhBA,EAAU,EAAA,CAAA,eACfA,EAAM,EAAA,GAAIA,EAAG,EAAA,EAAC,KAAK,EACjBQ,EAAAH,EAAA,WAAAL,OAAUA,EAAG,EAAA,EAAC,OAASA,OAAU,IAAI,UALtDS,EAYCC,EAAAL,EAAAO,CAAA,2DAXoCZ,EAAK,CAAA,EAAA,kEAKpCA,EAAG,EAAA,EAAC,MAAM,GAAG,gBACXe,EAAA,CAAA,EAAA,OAAAE,KAAAA,EAAAjB,EAAa,CAAA,GAAAA,EAAI,EAAA,EAAA,SAASA,EAAA,CAAA,EAC9B,2BACsB,KAAK,MAC1BA,EAAC,EAAA,EAAG,IAAOA,EAAQ,EAAA,GAAA,YAAY,MAAA,CAAA,wDAPhBA,EAAU,EAAA,CAAA,2BACfA,EAAM,EAAA,GAAIA,EAAG,EAAA,EAAC,KAAK,cACjBQ,EAAAH,EAAA,WAAAL,OAAUA,EAAG,EAAA,EAAC,OAASA,OAAU,IAAI,uCAY/CkB,EAAAC,EAAAnB,MAAO,WAAW,uBAAvB,OAAIC,GAAA,2HADPQ,EAkBKC,EAAAC,EAAAC,CAAA,4EAjBGM,EAAAC,EAAAnB,MAAO,WAAW,oBAAvB,OAAI,GAAA,EAAA,kHAAJ,qDAcCoB,EAAApB,MAAI,MAAK,oMAXgBqB,EAAAC,EAAA,mBAAAtB,EAAa,CAAA,GAAAA,EAAI,EAAA,EAAA,SAASA,EAAA,CAAA,EACjDA,EAAU,CAAA,EAAAA,EAAI,EAAA,EAAA,KAAK,EAAI,aACf,KAAK,MACZA,EAAC,EAAA,EAAG,IAAOA,EAAO,EAAA,EAAA,YAAY,MAAA,CAAA,mBAAA,UALnCS,EAcQC,EAAAY,EAAAV,CAAA,mIADNG,EAAA,CAAA,EAAA,OAAAK,KAAAA,EAAApB,MAAI,MAAK,KAAAuB,GAAAC,EAAAJ,CAAA,cAXgBC,EAAAC,EAAA,mBAAAtB,EAAa,CAAA,GAAAA,EAAI,EAAA,EAAA,SAASA,EAAA,CAAA,EACjDA,EAAU,CAAA,EAAAA,EAAI,EAAA,EAAA,KAAK,EAAI,aACf,KAAK,MACZA,EAAC,EAAA,EAAG,IAAOA,EAAO,EAAA,EAAA,YAAY,MAAA,CAAA,mBAAA,gPAxD3B,CAAA,WAAAA,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,EAAA,+GAIZyB,GACC,MAAAzB,EAAS,CAAA,GAAAA,EAAO,CAAA,EAAA,KAAK,aAAa,0CAIpC,OAAAA,OAAU,KAAI,wLADpBS,EA4DKC,EAAAC,EAAAC,CAAA,sDAtEQG,EAAA,CAAA,EAAA,GAAA,CAAA,WAAAf,KAAO,UAAU,EACvBe,EAAA,CAAA,EAAA,GAAA,CAAA,KAAAf,KAAO,IAAI,gBACbA,EAAc,EAAA,CAAA,yDAKXe,EAAA,CAAA,EAAA,KAAAW,EAAA,MAAA1B,EAAS,CAAA,GAAAA,EAAO,CAAA,EAAA,KAAK,aAAa,iaAjBjC,yCAGO,8qBAnHL,QAAA2B,EAAU,EAAA,EAAAC,EACV,CAAA,aAAAC,EAAA,EAAA,EAAAD,GACA,QAAAE,EAAU,EAAA,EAAAF,GACV,MAAAG,EAGA,IAAA,EAAAH,EACPI,EAGO,KACPC,EAGO,KACA,CAAA,OAAAC,CAAA,EAAAN,GAIA,MAAAO,EAAQD,EAAO,KAAK,iCAAiC,CAAA,EAAAN,GACrD,WAAAQ,EAAa,EAAA,EAAAR,GACb,YAAAS,EAAc,EAAA,EAAAT,EACd,CAAA,OAAAU,CAAA,EAAAV,EACA,CAAA,MAAAW,CAAA,EAAAX,EACA,CAAA,UAAAY,CAAA,EAAAZ,GACA,UAAAa,EAAY,EAAA,EAAAb,GACZ,MAAAc,EAAuB,IAAA,EAAAd,GACvB,UAAAe,EAAgC,MAAA,EAAAf,EACvCgB,EAAwB,KACjB,CAAA,eAAAC,CAAA,EAAAjB,GACA,uBAAAkB,EAAyB,EAAA,EAAAlB,EAEhCmB,EACAC,EAAa,GAKbC,EAA0C,cAuDrCC,EAAiBC,EAAA,MACzBP,EAASO,CAAA,EAED,SAAAC,GAAA,MACRR,EAAS,IAAA,EAGD,SAAAS,EAAapD,EAAW8B,EAAAA,CAChCG,EAAO,SAAS,SAAA,CACf,MAAOC,EACP,MAAOlC,CAAA,CAAA,aAsCe,OAAAqD,KAAM,CACvBC,EAAA,GAAAP,EAAaM,CAAM,8CANgBP,EAAeS,wBA4C/BN,EAAiBO,EAAI,KAAK,QAC9BP,EAAiBO,EAAI,KAAK,SACvBL,WACJA,IACCM,GAAA,CAAAzD,EAAAwD,IAAAJ,EAAapD,EAAGwD,EAAI,KAAK,8pBA5I1C,GAJA1B,IAAUC,SACbA,EAAYD,CAAA,EACZG,EAAO,SAAS,QAAQ,GAErBH,EAAA,CACG,MAAA4B,EAAA,CACL,MAAO5B,EAAM,MACb,YAAaA,EAAM,YAAY,IAAK0B,IAAA,CACnC,MAAOA,EAAI,MACX,MAAOA,EAAI,KAAA,EAAA,QAGbxB,EAAS0B,CAAA,QAOHC,EAAoBC,EAAiBF,EAAiB,MAAM,GAAG,EAC/DG,GAA0B,QAAQ,IACvCH,EAAiB,YAAY,IAAKF,GACjCI,EAAiBJ,EAAI,MAAM,GAAG,CAAA,CAAA,EAG1BM,EAAkB,QAAQ,KAC/BH,EACAE,EAAA,CAAA,OAEDb,EAAiBc,CAAA,EACjBA,EAAgB,KAAA,CAAA,CAAOC,EAAWC,EAAe,IAAA,IAC5ChB,IAAmBc,SAGjB,MAAAG,GAAA,CACL,MAAA,IACIP,EAAiB,MACpB,IAAKK,GAAa,QAEnB,YAAaL,EAAiB,YAAY,IAAA,CAAKF,EAAKxD,MAAA,CAChD,GAAAwD,EACH,MAAA,IACIA,EAAI,MACP,IAAKQ,GAAgBhE,EAAC,GAAK,iBAI9BgC,EAASiC,EAAA,cAGVjC,EAAS,IAAA"}