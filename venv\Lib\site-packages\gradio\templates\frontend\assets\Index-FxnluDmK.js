import"./index-B7J2Z2jS.js";import{B as b}from"./Button-B3gqVEq2.js";import"./svelte/svelte.js";import"./Image-CnqB5dbD.js";import"./file-url-DoxvUUVV.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import"./prism-python-MMh3z1bK.js";/* empty css                                                   */const{SvelteComponent:k,create_component:w,destroy_component:z,detach:B,flush:c,init:q,insert:C,mount_component:I,safe_not_equal:S,set_data:j,text:A,transition_in:D,transition_out:E}=window.__gradio__svelte__internal;function F(l){let e=(l[3]??"")+"",n;return{c(){n=A(e)},m(t,s){C(t,n,s)},p(t,s){s&8&&e!==(e=(t[3]??"")+"")&&j(n,e)},d(t){t&&B(n)}}}function G(l){let e,n;return e=new b({props:{value:l[3],variant:l[4],elem_id:l[0],elem_classes:l[1],size:l[6],scale:l[7],link:l[9],icon:l[8],min_width:l[10],visible:l[2],disabled:!l[5],$$slots:{default:[F]},$$scope:{ctx:l}}}),e.$on("click",l[12]),{c(){w(e.$$.fragment)},m(t,s){I(e,t,s),n=!0},p(t,[s]){const a={};s&8&&(a.value=t[3]),s&16&&(a.variant=t[4]),s&1&&(a.elem_id=t[0]),s&2&&(a.elem_classes=t[1]),s&64&&(a.size=t[6]),s&128&&(a.scale=t[7]),s&512&&(a.link=t[9]),s&256&&(a.icon=t[8]),s&1024&&(a.min_width=t[10]),s&4&&(a.visible=t[2]),s&32&&(a.disabled=!t[5]),s&8200&&(a.$$scope={dirty:s,ctx:t}),e.$set(a)},i(t){n||(D(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){z(e,t)}}}function H(l,e,n){let{elem_id:t=""}=e,{elem_classes:s=[]}=e,{visible:a=!0}=e,{value:m}=e,{variant:_="secondary"}=e,{interactive:r}=e,{size:f="lg"}=e,{scale:h=null}=e,{icon:o=null}=e,{link:g=null}=e,{min_width:v=void 0}=e,{gradio:u}=e;const d=()=>u.dispatch("click");return l.$$set=i=>{"elem_id"in i&&n(0,t=i.elem_id),"elem_classes"in i&&n(1,s=i.elem_classes),"visible"in i&&n(2,a=i.visible),"value"in i&&n(3,m=i.value),"variant"in i&&n(4,_=i.variant),"interactive"in i&&n(5,r=i.interactive),"size"in i&&n(6,f=i.size),"scale"in i&&n(7,h=i.scale),"icon"in i&&n(8,o=i.icon),"link"in i&&n(9,g=i.link),"min_width"in i&&n(10,v=i.min_width),"gradio"in i&&n(11,u=i.gradio)},[t,s,a,m,_,r,f,h,o,g,v,u,d]}class U extends k{constructor(e){super(),q(this,e,H,G,S,{elem_id:0,elem_classes:1,visible:2,value:3,variant:4,interactive:5,size:6,scale:7,icon:8,link:9,min_width:10,gradio:11})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),c()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),c()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),c()}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),c()}get variant(){return this.$$.ctx[4]}set variant(e){this.$$set({variant:e}),c()}get interactive(){return this.$$.ctx[5]}set interactive(e){this.$$set({interactive:e}),c()}get size(){return this.$$.ctx[6]}set size(e){this.$$set({size:e}),c()}get scale(){return this.$$.ctx[7]}set scale(e){this.$$set({scale:e}),c()}get icon(){return this.$$.ctx[8]}set icon(e){this.$$set({icon:e}),c()}get link(){return this.$$.ctx[9]}set link(e){this.$$set({link:e}),c()}get min_width(){return this.$$.ctx[10]}set min_width(e){this.$$set({min_width:e}),c()}get gradio(){return this.$$.ctx[11]}set gradio(e){this.$$set({gradio:e}),c()}}export{b as BaseButton,U as default};
//# sourceMappingURL=Index-FxnluDmK.js.map
