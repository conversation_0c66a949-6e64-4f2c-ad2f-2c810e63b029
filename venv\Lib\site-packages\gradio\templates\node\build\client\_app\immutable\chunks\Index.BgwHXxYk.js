import{SvelteComponent as v,init as b,safe_not_equal as p,create_slot as w,element as c,claim_element as m,children as h,detach as _,attr as d,set_style as o,toggle_class as g,insert_hydration as I,append_hydration as j,update_slot_base as k,get_all_dirty_from_scope as y,get_slot_changes as D,transition_in as E,transition_out as V}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function q(a){let l,s,u,n;const r=a[4].default,i=w(r,a,a[3],null);return{c(){l=c("div"),s=c("div"),i&&i.c(),this.h()},l(e){l=m(e,"DIV",{id:!0,class:!0});var t=h(l);s=m(t,"DIV",{class:!0});var f=h(s);i&&i.l(f),f.for<PERSON>ach(_),t.forEach(_),this.h()},h(){d(s,"class","styler svelte-1nguped"),o(s,"--block-radius","0px"),o(s,"--block-border-width","0px"),o(s,"--layout-gap","1px"),o(s,"--form-gap-width","1px"),o(s,"--button-border-width","0px"),o(s,"--button-large-radius","0px"),o(s,"--button-small-radius","0px"),d(l,"id",a[0]),d(l,"class",u="gr-group "+a[1].join(" ")+" svelte-1nguped"),g(l,"hide",!a[2])},m(e,t){I(e,l,t),j(l,s),i&&i.m(s,null),n=!0},p(e,[t]){i&&i.p&&(!n||t&8)&&k(i,r,e,e[3],n?D(r,e[3],t,null):y(e[3]),null),(!n||t&1)&&d(l,"id",e[0]),(!n||t&2&&u!==(u="gr-group "+e[1].join(" ")+" svelte-1nguped"))&&d(l,"class",u),(!n||t&6)&&g(l,"hide",!e[2])},i(e){n||(E(i,e),n=!0)},o(e){V(i,e),n=!1},d(e){e&&_(l),i&&i.d(e)}}}function C(a,l,s){let{$$slots:u={},$$scope:n}=l,{elem_id:r=""}=l,{elem_classes:i=[]}=l,{visible:e=!0}=l;return a.$$set=t=>{"elem_id"in t&&s(0,r=t.elem_id),"elem_classes"in t&&s(1,i=t.elem_classes),"visible"in t&&s(2,e=t.visible),"$$scope"in t&&s(3,n=t.$$scope)},[r,i,e,n,u]}class A extends v{constructor(l){super(),b(this,l,C,q,p,{elem_id:0,elem_classes:1,visible:2})}}export{A as default};
//# sourceMappingURL=Index.BgwHXxYk.js.map
