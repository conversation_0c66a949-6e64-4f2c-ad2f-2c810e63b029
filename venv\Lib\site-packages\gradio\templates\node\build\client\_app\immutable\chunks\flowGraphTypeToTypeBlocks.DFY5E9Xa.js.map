{"version": 3, "file": "flowGraphTypeToTypeBlocks.DFY5E9Xa.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Transformers/flowGraphTypeToTypeBlocks.js"], "sourcesContent": ["import { FlowGraphUnaryOperationBlock } from \"../flowGraphUnaryOperationBlock.js\";\nimport { RichTypeBoolean, RichTypeFlowGraphInteger, RichTypeNumber } from \"../../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\nimport { FlowGraphInteger } from \"../../../CustomTypes/flowGraphInteger.js\";\n/**\n * A block that converts a boolean to a float.\n */\nexport class FlowGraphBooleanToFloat extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeBoolean, RichTypeNumber, (a) => +a, \"FlowGraphBooleanToFloat\" /* FlowGraphBlockNames.BooleanToFloat */, config);\n    }\n}\nRegisterClass(\"FlowGraphBooleanToFloat\" /* FlowGraphBlockNames.BooleanToFloat */, FlowGraphBooleanToFloat);\n/**\n * A block that converts a boolean to an integer\n */\nexport class FlowGraphBooleanToInt extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeBoolean, RichTypeFlowGraphInteger, (a) => FlowGraphInteger.FromValue(+a), \"FlowGraphBooleanToInt\" /* FlowGraphBlockNames.BooleanToInt */, config);\n    }\n}\nRegisterClass(\"FlowGraphBooleanToInt\" /* FlowGraphBlockNames.BooleanToInt */, FlowGraphBooleanToInt);\n/**\n * A block that converts a float to a boolean.\n */\nexport class FlowGraphFloatToBoolean extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeNumber, RichTypeBoolean, (a) => !!a, \"FlowGraphFloatToBoolean\" /* FlowGraphBlockNames.FloatToBoolean */, config);\n    }\n}\nRegisterClass(\"FlowGraphFloatToBoolean\" /* FlowGraphBlockNames.FloatToBoolean */, FlowGraphFloatToBoolean);\n/**\n * A block that converts an integer to a boolean.\n */\nexport class FlowGraphIntToBoolean extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeFlowGraphInteger, RichTypeBoolean, (a) => !!a.value, \"FlowGraphIntToBoolean\" /* FlowGraphBlockNames.IntToBoolean */, config);\n    }\n}\nRegisterClass(\"FlowGraphIntToBoolean\" /* FlowGraphBlockNames.IntToBoolean */, FlowGraphIntToBoolean);\n/**\n * A block that converts an integer to a float.\n */\nexport class FlowGraphIntToFloat extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeFlowGraphInteger, RichTypeNumber, (a) => a.value, \"FlowGraphIntToFloat\" /* FlowGraphBlockNames.IntToFloat */, config);\n    }\n}\nRegisterClass(\"FlowGraphIntToFloat\" /* FlowGraphBlockNames.IntToFloat */, FlowGraphIntToFloat);\n/**\n * A block that converts a float to an integer.\n */\nexport class FlowGraphFloatToInt extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeNumber, RichTypeFlowGraphInteger, (a) => {\n            const roundingMode = config?.roundingMode;\n            switch (roundingMode) {\n                case \"floor\":\n                    return FlowGraphInteger.FromValue(Math.floor(a));\n                case \"ceil\":\n                    return FlowGraphInteger.FromValue(Math.ceil(a));\n                case \"round\":\n                    return FlowGraphInteger.FromValue(Math.round(a));\n                default:\n                    return FlowGraphInteger.FromValue(a);\n            }\n        }, \"FlowGraphFloatToInt\" /* FlowGraphBlockNames.FloatToInt */, config);\n    }\n}\nRegisterClass(\"FlowGraphFloatToInt\" /* FlowGraphBlockNames.FloatToInt */, FlowGraphFloatToInt);\n//# sourceMappingURL=flowGraphTypeToTypeBlocks.js.map"], "names": ["FlowGraphBooleanToFloat", "FlowGraphUnaryOperationBlock", "config", "RichTypeBoolean", "RichTypeNumber", "RegisterClass", "FlowGraphBooleanToInt", "RichTypeFlowGraphInteger", "FlowGraphInteger", "FlowGraphFloatToBoolean", "FlowGraphIntToBoolean", "FlowGraphIntToFloat", "FlowGraphFloatToInt"], "mappings": "gLAOO,MAAMA,UAAgCC,CAA6B,CACtE,YAAYC,EAAQ,CAChB,MAAMC,EAAiBC,EAAiB,GAAM,CAAC,EAAG,0BAAoEF,CAAM,CAC/H,CACL,CACAG,EAAc,0BAAoEL,CAAuB,EAIlG,MAAMM,UAA8BL,CAA6B,CACpE,YAAYC,EAAQ,CAChB,MAAMC,EAAiBI,EAA2B,GAAMC,EAAiB,UAAU,CAAC,CAAC,EAAG,wBAAgEN,CAAM,CACjK,CACL,CACAG,EAAc,wBAAgEC,CAAqB,EAI5F,MAAMG,UAAgCR,CAA6B,CACtE,YAAYC,EAAQ,CAChB,MAAME,EAAgBD,EAAkB,GAAM,CAAC,CAAC,EAAG,0BAAoED,CAAM,CAChI,CACL,CACAG,EAAc,0BAAoEI,CAAuB,EAIlG,MAAMC,UAA8BT,CAA6B,CACpE,YAAYC,EAAQ,CAChB,MAAMK,EAA0BJ,EAAkB,GAAM,CAAC,CAAC,EAAE,MAAO,wBAAgED,CAAM,CAC5I,CACL,CACAG,EAAc,wBAAgEK,CAAqB,EAI5F,MAAMC,UAA4BV,CAA6B,CAClE,YAAYC,EAAQ,CAChB,MAAMK,EAA0BH,EAAiB,GAAM,EAAE,MAAO,sBAA4DF,CAAM,CACrI,CACL,CACAG,EAAc,sBAA4DM,CAAmB,EAItF,MAAMC,UAA4BX,CAA6B,CAClE,YAAYC,EAAQ,CAChB,MAAME,EAAgBG,EAA2B,GAAM,CAEnD,OADqBL,GAAA,YAAAA,EAAQ,aACT,CAChB,IAAK,QACD,OAAOM,EAAiB,UAAU,KAAK,MAAM,CAAC,CAAC,EACnD,IAAK,OACD,OAAOA,EAAiB,UAAU,KAAK,KAAK,CAAC,CAAC,EAClD,IAAK,QACD,OAAOA,EAAiB,UAAU,KAAK,MAAM,CAAC,CAAC,EACnD,QACI,OAAOA,EAAiB,UAAU,CAAC,CAC1C,CACb,EAAW,sBAA4DN,CAAM,CACxE,CACL,CACAG,EAAc,sBAA4DO,CAAmB", "x_google_ignoreList": [0]}