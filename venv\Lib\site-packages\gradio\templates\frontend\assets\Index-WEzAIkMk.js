const{SvelteComponent:m,append:g,attr:d,create_slot:b,detach:h,element:f,flush:r,get_all_dirty_from_scope:v,get_slot_changes:p,init:w,insert:j,safe_not_equal:k,set_style:_,toggle_class:c,transition_in:x,transition_out:q,update_slot_base:C}=window.__gradio__svelte__internal;function I(u){let e,s,a,n;const o=u[4].default,i=b(o,u,u[3],null);return{c(){e=f("div"),s=f("div"),i&&i.c(),d(s,"class","styler svelte-1nguped"),_(s,"--block-radius","0px"),_(s,"--block-border-width","0px"),_(s,"--layout-gap","1px"),_(s,"--form-gap-width","1px"),_(s,"--button-border-width","0px"),_(s,"--button-large-radius","0px"),_(s,"--button-small-radius","0px"),d(e,"id",u[0]),d(e,"class",a="gr-group "+u[1].join(" ")+" svelte-1nguped"),c(e,"hide",!u[2])},m(t,l){j(t,e,l),g(e,s),i&&i.m(s,null),n=!0},p(t,[l]){i&&i.p&&(!n||l&8)&&C(i,o,t,t[3],n?p(o,t[3],l,null):v(t[3]),null),(!n||l&1)&&d(e,"id",t[0]),(!n||l&2&&a!==(a="gr-group "+t[1].join(" ")+" svelte-1nguped"))&&d(e,"class",a),(!n||l&6)&&c(e,"hide",!t[2])},i(t){n||(x(i,t),n=!0)},o(t){q(i,t),n=!1},d(t){t&&h(e),i&&i.d(t)}}}function S(u,e,s){let{$$slots:a={},$$scope:n}=e,{elem_id:o=""}=e,{elem_classes:i=[]}=e,{visible:t=!0}=e;return u.$$set=l=>{"elem_id"in l&&s(0,o=l.elem_id),"elem_classes"in l&&s(1,i=l.elem_classes),"visible"in l&&s(2,t=l.visible),"$$scope"in l&&s(3,n=l.$$scope)},[o,i,t,n,a]}class y extends m{constructor(e){super(),w(this,e,S,I,k,{elem_id:0,elem_classes:1,visible:2})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),r()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),r()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),r()}}export{y as default};
//# sourceMappingURL=Index-WEzAIkMk.js.map
