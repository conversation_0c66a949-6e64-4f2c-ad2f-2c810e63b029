{"version": 3, "file": "flowGraphVectorMathBlocks-Cuy1i23S.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Math/flowGraphVectorMathBlocks.js"], "sourcesContent": ["import { RichTypeVector3, <PERSON><PERSON><PERSON><PERSON><PERSON>ber, RichTypeAny, RichTypeVector2, RichTypeMatrix, getRichTypeByFlowGraphType } from \"../../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\nimport { FlowGraphBinaryOperationBlock } from \"../flowGraphBinaryOperationBlock.js\";\nimport { FlowGraphUnaryOperationBlock } from \"../flowGraphUnaryOperationBlock.js\";\nimport { Matrix, Vector2, Vector3, Vector4 } from \"../../../../Maths/math.vector.js\";\nimport { FlowGraphTernaryOperationBlock } from \"../flowGraphTernaryOperationBlock.js\";\nimport { _getClassNameOf } from \"../../../utils.js\";\n/**\n * Vector length block.\n */\nexport class FlowGraphLengthBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(<PERSON>T<PERSON><PERSON>ny, RichTypeNumber, (a) => this._polymorphicLength(a), \"FlowGraphLengthBlock\" /* FlowGraphBlockNames.Length */, config);\n    }\n    _polymorphicLength(a) {\n        const aClassName = _getClassNameOf(a);\n        switch (aClassName) {\n            case \"Vector2\" /* FlowGraphTypes.Vector2 */:\n            case \"Vector3\" /* FlowGraphTypes.Vector3 */:\n            case \"Vector4\" /* FlowGraphTypes.Vector4 */:\n            case \"Quaternion\" /* FlowGraphTypes.Quaternion */:\n                return a.length();\n            default:\n                throw new Error(`Cannot compute length of value ${a}`);\n        }\n    }\n}\nRegisterClass(\"FlowGraphLengthBlock\" /* FlowGraphBlockNames.Length */, FlowGraphLengthBlock);\n/**\n * Vector normalize block.\n */\nexport class FlowGraphNormalizeBlock extends FlowGraphUnaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, (a) => this._polymorphicNormalize(a), \"FlowGraphNormalizeBlock\" /* FlowGraphBlockNames.Normalize */, config);\n    }\n    _polymorphicNormalize(a) {\n        const aClassName = _getClassNameOf(a);\n        let normalized;\n        switch (aClassName) {\n            case \"Vector2\" /* FlowGraphTypes.Vector2 */:\n            case \"Vector3\" /* FlowGraphTypes.Vector3 */:\n            case \"Vector4\" /* FlowGraphTypes.Vector4 */:\n            case \"Quaternion\" /* FlowGraphTypes.Quaternion */:\n                normalized = a.normalizeToNew();\n                if (this.config?.nanOnZeroLength) {\n                    const length = a.length();\n                    if (length === 0) {\n                        normalized.setAll(NaN);\n                    }\n                }\n                return normalized;\n            default:\n                throw new Error(`Cannot normalize value ${a}`);\n        }\n    }\n}\nRegisterClass(\"FlowGraphNormalizeBlock\" /* FlowGraphBlockNames.Normalize */, FlowGraphNormalizeBlock);\n/**\n * Dot product block.\n */\nexport class FlowGraphDotBlock extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        super(RichTypeAny, RichTypeAny, RichTypeNumber, (a, b) => this._polymorphicDot(a, b), \"FlowGraphDotBlock\" /* FlowGraphBlockNames.Dot */, config);\n    }\n    _polymorphicDot(a, b) {\n        const className = _getClassNameOf(a);\n        switch (className) {\n            case \"Vector2\" /* FlowGraphTypes.Vector2 */:\n            case \"Vector3\" /* FlowGraphTypes.Vector3 */:\n            case \"Vector4\" /* FlowGraphTypes.Vector4 */:\n            case \"Quaternion\" /* FlowGraphTypes.Quaternion */:\n                // casting is needed because dot requires both to be the same type\n                return a.dot(b);\n            default:\n                throw new Error(`Cannot get dot product of ${a} and ${b}`);\n        }\n    }\n}\nRegisterClass(\"FlowGraphDotBlock\" /* FlowGraphBlockNames.Dot */, FlowGraphDotBlock);\n/**\n * Cross product block.\n */\nexport class FlowGraphCrossBlock extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        super(RichTypeVector3, RichTypeVector3, RichTypeVector3, (a, b) => Vector3.Cross(a, b), \"FlowGraphCrossBlock\" /* FlowGraphBlockNames.Cross */, config);\n    }\n}\nRegisterClass(\"FlowGraphCrossBlock\" /* FlowGraphBlockNames.Cross */, FlowGraphCrossBlock);\n/**\n * 2D rotation block.\n */\nexport class FlowGraphRotate2DBlock extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        super(RichTypeVector2, RichTypeNumber, RichTypeVector2, (a, b) => Vector2.Transform(a, Matrix.RotationZ(b)), \"FlowGraphRotate2DBlock\" /* FlowGraphBlockNames.Rotate2D */, config);\n    }\n}\nRegisterClass(\"FlowGraphRotate2DBlock\" /* FlowGraphBlockNames.Rotate2D */, FlowGraphRotate2DBlock);\n/**\n * 3D rotation block.\n */\nexport class FlowGraphRotate3DBlock extends FlowGraphTernaryOperationBlock {\n    constructor(config) {\n        super(RichTypeVector3, RichTypeVector3, RichTypeNumber, RichTypeVector3, (a, b, c) => Vector3.TransformCoordinates(a, Matrix.RotationAxis(b, c)), \"FlowGraphRotate3DBlock\" /* FlowGraphBlockNames.Rotate3D */, config);\n    }\n}\nRegisterClass(\"FlowGraphRotate3DBlock\" /* FlowGraphBlockNames.Rotate3D */, FlowGraphRotate3DBlock);\nfunction _transformVector(a, b) {\n    const className = _getClassNameOf(a);\n    switch (className) {\n        case \"Vector2\" /* FlowGraphTypes.Vector2 */:\n            return b.transformVector(a);\n        case \"Vector3\" /* FlowGraphTypes.Vector3 */:\n            return b.transformVector(a);\n        case \"Vector4\" /* FlowGraphTypes.Vector4 */:\n            a = a;\n            // transform the vector 4 with the matrix here. Vector4.TransformCoordinates transforms a 3D coordinate, not Vector4\n            return new Vector4(a.x * b.m[0] + a.y * b.m[1] + a.z * b.m[2] + a.w * b.m[3], a.x * b.m[4] + a.y * b.m[5] + a.z * b.m[6] + a.w * b.m[7], a.x * b.m[8] + a.y * b.m[9] + a.z * b.m[10] + a.w * b.m[11], a.x * b.m[12] + a.y * b.m[13] + a.z * b.m[14] + a.w * b.m[15]);\n        default:\n            throw new Error(`Cannot transform value ${a}`);\n    }\n}\n/**\n * Transform a vector3 by a matrix.\n */\nexport class FlowGraphTransformBlock extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        const vectorType = config?.vectorType || \"Vector3\" /* FlowGraphTypes.Vector3 */;\n        const matrixType = vectorType === \"Vector2\" /* FlowGraphTypes.Vector2 */ ? \"Matrix2D\" /* FlowGraphTypes.Matrix2D */ : vectorType === \"Vector3\" /* FlowGraphTypes.Vector3 */ ? \"Matrix3D\" /* FlowGraphTypes.Matrix3D */ : \"Matrix\" /* FlowGraphTypes.Matrix */;\n        super(getRichTypeByFlowGraphType(vectorType), getRichTypeByFlowGraphType(matrixType), getRichTypeByFlowGraphType(vectorType), _transformVector, \"FlowGraphTransformVectorBlock\" /* FlowGraphBlockNames.TransformVector */, config);\n    }\n}\nRegisterClass(\"FlowGraphTransformVectorBlock\" /* FlowGraphBlockNames.TransformVector */, FlowGraphTransformBlock);\n/**\n * Transform a vector3 by a matrix.\n */\nexport class FlowGraphTransformCoordinatesBlock extends FlowGraphBinaryOperationBlock {\n    constructor(config) {\n        super(RichTypeVector3, RichTypeMatrix, RichTypeVector3, (a, b) => Vector3.TransformCoordinates(a, b), \"FlowGraphTransformCoordinatesBlock\" /* FlowGraphBlockNames.TransformCoordinates */, config);\n    }\n}\nRegisterClass(\"FlowGraphTransformCoordinatesBlock\" /* FlowGraphBlockNames.TransformCoordinates */, FlowGraphTransformCoordinatesBlock);\n//# sourceMappingURL=flowGraphVectorMathBlocks.js.map"], "names": ["FlowGraphLengthBlock", "FlowGraphUnaryOperationBlock", "config", "RichTypeAny", "RichTypeNumber", "a", "_getClassNameOf", "RegisterClass", "FlowGraphNormalizeBlock", "aClassName", "normalized", "FlowGraphDotBlock", "FlowGraphBinaryOperationBlock", "b", "FlowGraphCrossBlock", "RichTypeVector3", "Vector3", "FlowGraphRotate2DBlock", "RichTypeVector2", "Vector2", "Matrix", "FlowGraphRotate3DBlock", "FlowGraphTernaryOperationBlock", "c", "_transformVector", "Vector4", "FlowGraphTransformBlock", "vectorType", "matrixType", "getRichTypeByFlowGraphType", "FlowGraphTransformCoordinatesBlock", "RichTypeMatrix"], "mappings": "giBAUO,MAAMA,UAA6BC,CAA6B,CACnE,YAAYC,EAAQ,CAChB,MAAMC,EAAaC,EAAiBC,GAAM,KAAK,mBAAmBA,CAAC,EAAG,uBAAyDH,CAAM,CACxI,CACD,mBAAmBG,EAAG,CAElB,OADmBC,EAAgBD,CAAC,EAClB,CACd,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,aACD,OAAOA,EAAE,SACb,QACI,MAAM,IAAI,MAAM,kCAAkCA,CAAC,EAAE,CAC5D,CACJ,CACL,CACAE,EAAc,uBAAyDP,CAAoB,EAIpF,MAAMQ,UAAgCP,CAA6B,CACtE,YAAYC,EAAQ,CAChB,MAAMC,EAAaA,EAAcE,GAAM,KAAK,sBAAsBA,CAAC,EAAG,0BAA+DH,CAAM,CAC9I,CACD,sBAAsBG,EAAG,CACrB,MAAMI,EAAaH,EAAgBD,CAAC,EACpC,IAAIK,EACJ,OAAQD,EAAU,CACd,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,aACD,OAAAC,EAAaL,EAAE,iBACX,KAAK,QAAQ,iBACEA,EAAE,WACF,GACXK,EAAW,OAAO,GAAG,EAGtBA,EACX,QACI,MAAM,IAAI,MAAM,0BAA0BL,CAAC,EAAE,CACpD,CACJ,CACL,CACAE,EAAc,0BAA+DC,CAAuB,EAI7F,MAAMG,UAA0BC,CAA8B,CACjE,YAAYV,EAAQ,CAChB,MAAMC,EAAaA,EAAaC,EAAgB,CAACC,EAAGQ,IAAM,KAAK,gBAAgBR,EAAGQ,CAAC,EAAG,oBAAmDX,CAAM,CAClJ,CACD,gBAAgBG,EAAGQ,EAAG,CAElB,OADkBP,EAAgBD,CAAC,EAClB,CACb,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,aAED,OAAOA,EAAE,IAAIQ,CAAC,EAClB,QACI,MAAM,IAAI,MAAM,6BAA6BR,CAAC,QAAQQ,CAAC,EAAE,CAChE,CACJ,CACL,CACAN,EAAc,oBAAmDI,CAAiB,EAI3E,MAAMG,UAA4BF,CAA8B,CACnE,YAAYV,EAAQ,CAChB,MAAMa,EAAiBA,EAAiBA,EAAiB,CAACV,EAAGQ,IAAMG,EAAQ,MAAMX,EAAGQ,CAAC,EAAG,sBAAuDX,CAAM,CACxJ,CACL,CACAK,EAAc,sBAAuDO,CAAmB,EAIjF,MAAMG,UAA+BL,CAA8B,CACtE,YAAYV,EAAQ,CAChB,MAAMgB,EAAiBd,EAAgBc,EAAiB,CAACb,EAAGQ,IAAMM,EAAQ,UAAUd,EAAGe,EAAO,UAAUP,CAAC,CAAC,EAAG,yBAA6DX,CAAM,CACnL,CACL,CACAK,EAAc,yBAA6DU,CAAsB,EAI1F,MAAMI,UAA+BC,CAA+B,CACvE,YAAYpB,EAAQ,CAChB,MAAMa,EAAiBA,EAAiBX,EAAgBW,EAAiB,CAACV,EAAGQ,EAAGU,IAAMP,EAAQ,qBAAqBX,EAAGe,EAAO,aAAaP,EAAGU,CAAC,CAAC,EAAG,yBAA6DrB,CAAM,CACxN,CACL,CACAK,EAAc,yBAA6Dc,CAAsB,EACjG,SAASG,EAAiBnB,EAAGQ,EAAG,CAE5B,OADkBP,EAAgBD,CAAC,EAClB,CACb,IAAK,UACD,OAAOQ,EAAE,gBAAgBR,CAAC,EAC9B,IAAK,UACD,OAAOQ,EAAE,gBAAgBR,CAAC,EAC9B,IAAK,UACD,OAAAA,EAAIA,EAEG,IAAIoB,EAAQpB,EAAE,EAAIQ,EAAE,EAAE,CAAC,EAAIR,EAAE,EAAIQ,EAAE,EAAE,CAAC,EAAIR,EAAE,EAAIQ,EAAE,EAAE,CAAC,EAAIR,EAAE,EAAIQ,EAAE,EAAE,CAAC,EAAGR,EAAE,EAAIQ,EAAE,EAAE,CAAC,EAAIR,EAAE,EAAIQ,EAAE,EAAE,CAAC,EAAIR,EAAE,EAAIQ,EAAE,EAAE,CAAC,EAAIR,EAAE,EAAIQ,EAAE,EAAE,CAAC,EAAGR,EAAE,EAAIQ,EAAE,EAAE,CAAC,EAAIR,EAAE,EAAIQ,EAAE,EAAE,CAAC,EAAIR,EAAE,EAAIQ,EAAE,EAAE,EAAE,EAAIR,EAAE,EAAIQ,EAAE,EAAE,EAAE,EAAGR,EAAE,EAAIQ,EAAE,EAAE,EAAE,EAAIR,EAAE,EAAIQ,EAAE,EAAE,EAAE,EAAIR,EAAE,EAAIQ,EAAE,EAAE,EAAE,EAAIR,EAAE,EAAIQ,EAAE,EAAE,EAAE,CAAC,EACvQ,QACI,MAAM,IAAI,MAAM,0BAA0BR,CAAC,EAAE,CACpD,CACL,CAIO,MAAMqB,UAAgCd,CAA8B,CACvE,YAAYV,EAAQ,CAChB,MAAMyB,EAAazB,GAAQ,YAAc,UACnC0B,EAAaD,IAAe,UAAyC,WAA2CA,IAAe,UAAyC,WAA2C,SACzN,MAAME,EAA2BF,CAAU,EAAGE,EAA2BD,CAAU,EAAGC,EAA2BF,CAAU,EAAGH,EAAkB,gCAA2EtB,CAAM,CACpO,CACL,CACAK,EAAc,gCAA2EmB,CAAuB,EAIzG,MAAMI,UAA2ClB,CAA8B,CAClF,YAAYV,EAAQ,CAChB,MAAMa,EAAiBgB,EAAgBhB,EAAiB,CAACV,EAAGQ,IAAMG,EAAQ,qBAAqBX,EAAGQ,CAAC,EAAG,qCAAqFX,CAAM,CACpM,CACL,CACAK,EAAc,qCAAqFuB,CAAkC", "x_google_ignoreList": [0]}