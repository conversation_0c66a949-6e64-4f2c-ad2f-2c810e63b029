{"version": 3, "file": "infoDiagram-SDLB2J7W.3PWTbl2N.js", "sources": ["../../../../../../../../node_modules/.pnpm/mermaid@11.5.0/node_modules/mermaid/dist/chunks/mermaid.core/infoDiagram-SDLB2J7W.mjs"], "sourcesContent": ["import {\n  package_default\n} from \"./chunk-WAVVWEMB.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-XYJ2X5CJ.mjs\";\nimport {\n  __name,\n  configureSvgSize,\n  log\n} from \"./chunk-O7R7247Q.mjs\";\n\n// src/diagrams/info/infoParser.ts\nimport { parse } from \"@mermaid-js/parser\";\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"info\", input);\n    log.debug(ast);\n  }, \"parse\")\n};\n\n// src/diagrams/info/infoDb.ts\nvar DEFAULT_INFO_DB = { version: package_default.version };\nvar getVersion = /* @__PURE__ */ __name(() => DEFAULT_INFO_DB.version, \"getVersion\");\nvar db = {\n  getVersion\n};\n\n// src/diagrams/info/infoRenderer.ts\nvar draw = /* @__PURE__ */ __name((text, id, version) => {\n  log.debug(\"rendering info diagram\\n\" + text);\n  const svg = selectSvgElement(id);\n  configureSvgSize(svg, 100, 400, true);\n  const group = svg.append(\"g\");\n  group.append(\"text\").attr(\"x\", 100).attr(\"y\", 40).attr(\"class\", \"version\").attr(\"font-size\", 32).style(\"text-anchor\", \"middle\").text(`v${version}`);\n}, \"draw\");\nvar renderer = { draw };\n\n// src/diagrams/info/infoDiagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer\n};\nexport {\n  diagram\n};\n"], "names": ["parser", "__name", "input", "ast", "parse", "log", "DEFAULT_INFO_DB", "package_default", "getVersion", "db", "draw", "text", "id", "version", "svg", "selectSvgElement", "configureSvgSize", "renderer", "diagram"], "mappings": "4HAcA,IAAIA,EAAS,CACX,MAAuBC,EAAO,MAAOC,GAAU,CAC7C,MAAMC,EAAM,MAAMC,EAAM,OAAQF,CAAK,EACrCG,EAAI,MAAMF,CAAG,CACd,EAAE,OAAO,CACZ,EAGIG,EAAkB,CAAE,QAASC,EAAgB,OAAO,EACpDC,EAA6BP,EAAO,IAAMK,EAAgB,QAAS,YAAY,EAC/EG,EAAK,CACP,WAAAD,CACF,EAGIE,EAAuBT,EAAO,CAACU,EAAMC,EAAIC,IAAY,CACvDR,EAAI,MAAM;AAAA,EAA6BM,CAAI,EAC3C,MAAMG,EAAMC,EAAiBH,CAAE,EAC/BI,EAAiBF,EAAK,IAAK,IAAK,EAAI,EACtBA,EAAI,OAAO,GAAG,EACtB,OAAO,MAAM,EAAE,KAAK,IAAK,GAAG,EAAE,KAAK,IAAK,EAAE,EAAE,KAAK,QAAS,SAAS,EAAE,KAAK,YAAa,EAAE,EAAE,MAAM,cAAe,QAAQ,EAAE,KAAK,IAAID,CAAO,EAAE,CACpJ,EAAG,MAAM,EACLI,EAAW,CAAE,KAAAP,GAGbQ,EAAU,CACZ,OAAAlB,EACA,GAAAS,EACA,SAAAQ,CACF", "x_google_ignoreList": [0]}