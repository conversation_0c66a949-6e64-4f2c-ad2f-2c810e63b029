"""
人设选择器组件
"""

from typing import Dict, Any

from PyQt6.QtWidgets import (QWidget, QHBoxLayout, QComboBox, QLabel, 
                            QPushButton, QFrame, QVBoxLayout)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap

from core.utils.logger import get_logger
from personas.persona_manager import persona_manager

logger = get_logger(__name__)

class PersonaSelector(QFrame):
    """人设选择器"""
    
    persona_changed = pyqtSignal(str)  # persona_id
    agents_hub_requested = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.current_persona_id = None
        
        self.init_ui()
        self.load_personas()
    
    def init_ui(self):
        """初始化UI"""
        self.setObjectName("persona_selector")
        self.setMinimumHeight(70)

        # 设置现代化样式
        self.setStyleSheet("""
            QWidget#persona_selector {
                background: rgba(45, 53, 72, 0.8);
                border: 1px solid rgba(102, 126, 234, 0.3);
                border-radius: 16px;
                margin: 5px;
            }
        """)

        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(15)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 人设图标和标签 - 现代化设计
        persona_info_layout = QHBoxLayout()
        persona_info_layout.setSpacing(12)

        persona_icon = QLabel("🎭")
        persona_icon.setFixedSize(28, 28)
        persona_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        persona_icon.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #8b5cf6, stop:1 #06b6d4);
                border-radius: 14px;
                font-size: 16px;
                color: white;
                border: 1px solid rgba(139, 92, 246, 0.3);
            }
        """)
        persona_info_layout.addWidget(persona_icon)

        # 人设信息容器
        persona_text_layout = QVBoxLayout()
        persona_text_layout.setSpacing(2)

        # 当前人设名称
        self.current_persona_name = QLabel("AI助手")
        self.current_persona_name.setObjectName("current_persona_name")
        self.current_persona_name.setStyleSheet("""
            QLabel#current_persona_name {
                background: transparent;
                color: #f1f5f9;
                font-size: 14px;
                font-weight: 700;
                border: none;
            }
        """)
        persona_text_layout.addWidget(self.current_persona_name)

        persona_info_layout.addLayout(persona_text_layout)

        layout.addLayout(persona_info_layout)

        # 现代化人设下拉框
        self.persona_combo = QComboBox()
        self.persona_combo.setObjectName("persona_combo")
        self.persona_combo.setMinimumWidth(200)
        self.persona_combo.setFixedHeight(38)
        self.persona_combo.setStyleSheet("""
            QComboBox#persona_combo {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(26, 31, 46, 0.9), stop:1 rgba(45, 53, 72, 0.9));
                border: 2px solid rgba(139, 92, 246, 0.3);
                border-radius: 19px;
                padding: 8px 16px;
                font-size: 13px;
                font-weight: 600;
                color: #f1f5f9;
            }
            QComboBox#persona_combo:hover {
                border: 2px solid rgba(139, 92, 246, 0.6);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(45, 53, 72, 0.9), stop:1 rgba(55, 65, 81, 0.9));
            }
            QComboBox#persona_combo:focus {
                border: 2px solid #8b5cf6;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(45, 53, 72, 0.9), stop:1 rgba(55, 65, 81, 0.9));
            }
            QComboBox#persona_combo::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox#persona_combo::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 7px solid #a78bfa;
                margin-right: 10px;
            }
            QComboBox#persona_combo QAbstractItemView {
                background: rgba(31, 41, 55, 0.95);
                border: 2px solid rgba(139, 92, 246, 0.4);
                border-radius: 12px;
                selection-background-color: rgba(102, 126, 234, 0.3);
                selection-color: white;
                color: #e2e8f0;
                padding: 5px;
            }
        """)
        self.persona_combo.currentTextChanged.connect(self.on_persona_changed)
        layout.addWidget(self.persona_combo)

        # Agents Hub按钮
        hub_button = QPushButton("🌟 Agents Hub")
        hub_button.setFixedHeight(35)
        hub_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border: none;
                border-radius: 17px;
                padding: 8px 16px;
                font-size: 13px;
                font-weight: 600;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #5a67d8, stop:1 #6b46c1);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4c51bf, stop:1 #553c9a);
            }
        """)
        hub_button.clicked.connect(self.agents_hub_requested.emit)
        layout.addWidget(hub_button)

        # 人设描述
        self.description_label = QLabel()
        self.description_label.setObjectName("persona_description")
        self.description_label.setWordWrap(True)
        self.description_label.setStyleSheet("""
            QLabel#persona_description {
                background: transparent;
                color: #94a3b8;
                font-size: 12px;
                font-style: italic;
                border: none;
            }
        """)
        layout.addWidget(self.description_label)
        layout.addStretch()

    
    def load_personas(self):
        """加载人设列表"""
        self.persona_combo.clear()
        
        personas = persona_manager.get_available_personas()
        
        for persona_id, persona_info in personas.items():
            display_name = f"{persona_info['name']}"
            if persona_info.get('nsfw_enabled', False):
                display_name += " 🔞"
            
            self.persona_combo.addItem(display_name, persona_id)
        
        # 设置当前人设
        current_persona = persona_manager.get_current_persona()
        if current_persona:
            self.set_current_persona(current_persona.id)
    
    def set_current_persona(self, persona_id: str):
        """设置当前人设"""
        for i in range(self.persona_combo.count()):
            if self.persona_combo.itemData(i) == persona_id:
                self.persona_combo.setCurrentIndex(i)
                self.current_persona_id = persona_id
                self.update_persona_info(persona_id)
                break
    
    def on_persona_changed(self):
        """人设改变事件"""
        current_index = self.persona_combo.currentIndex()
        if current_index >= 0:
            persona_id = self.persona_combo.itemData(current_index)
            if persona_id != self.current_persona_id:
                self.current_persona_id = persona_id
                self.update_persona_info(persona_id)
                self.persona_changed.emit(persona_id)
    
    def update_persona_info(self, persona_id: str):
        """更新人设信息显示"""
        persona_info = persona_manager.get_persona_info(persona_id)
        
        if persona_info:
            # 更新描述
            self.description_label.setText(persona_info['description'])
        else:
            self.description_label.setText("未知人设")
    
    def refresh_personas(self):
        """刷新人设列表"""
        persona_manager.reload_personas()
        self.load_personas()
        logger.info("人设列表已刷新")
    
    def apply_theme(self, theme):
        """应用主题"""
        self.setStyleSheet(theme.get_persona_selector_stylesheet())

    def set_current_persona(self, persona_id: str):
        """设置当前人设"""
        try:
            personas = persona_manager.get_available_personas()
            if persona_id in personas:
                persona_info = personas[persona_id]
                persona_name = persona_info.get('name', persona_id)

                # 更新当前人设名称显示
                self.current_persona_name.setText(persona_name)

                # 在下拉框中查找并选择
                for i in range(self.persona_combo.count()):
                    if self.persona_combo.itemText(i) == persona_name:
                        self.persona_combo.setCurrentIndex(i)
                        break

                logger.info(f"设置当前人设: {persona_id}")

        except Exception as e:
            logger.error(f"设置当前人设时出错: {e}")

    def update_current_persona_display(self, persona_name: str):
        """更新当前人设显示"""
        self.current_persona_name.setText(persona_name)
