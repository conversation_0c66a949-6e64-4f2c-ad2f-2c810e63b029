const __vite__fileDeps=["./module.BkjqLaM6.js","./module.C-VadMaF.js","./2.B2AoQPnG.js","./preload-helper.D6kgxu3v.js","./stores.z8sZTwoA.js","./client.Cd1aarwx.js","../assets/2.BTQDGmJF.css","./module.niXGEr8P.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as Ue}from"./preload-helper.D6kgxu3v.js";import{SvelteComponent as Ee,init as De,safe_not_equal as Re,element as I,claim_element as M,children as C,detach as p,attr as R,insert_hydration as V,noop as be,createEventDispatcher as Le,ensure_array_like as Oe,empty as pe,destroy_each as bt,text as ee,claim_text as te,set_input_value as Ae,append_hydration as k,set_data as ie,binding_callbacks as j,bind as ne,space as N,create_component as J,claim_space as z,claim_component as K,mount_component as Q,listen as fe,add_flush_callback as oe,transition_in as A,transition_out as S,destroy_component as X,run_all as vt,group_outros as we,check_outros as ke,onMount as Ze,get_svelte_dataset as Ie,set_style as Ne,null_to_empty as ze,onDestroy as pt,bubble as ge,create_slot as wt,update_slot_base as kt,get_all_dirty_from_scope as yt,get_slot_changes as Et}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{a as Dt}from"./Upload.yOHVlgUe.js";import{M as We}from"./ModifyUpload.uW4g0eE0.js";import{U as ce,A as Rt}from"./2.B2AoQPnG.js";import{B as Tt}from"./BlockLabel.BTSz9r5s.js";import{M as It}from"./Music.BVFRDHso.js";import{a as Mt,S as At}from"./SelectSource.CTC8Kkgx.js";import{S as Pt}from"./StreamingBar.BOfzkLQo.js";import{s as Ve,W as Lt,p as je,a as Pe,A as St}from"./AudioPlayer.CnAk5fND.js";import{P as Bt}from"./Trim.CWFkmJwA.js";function Me(n,e,t,i){return new(t||(t=Promise))(function(o,s){function l(f){try{a(i.next(f))}catch(r){s(r)}}function u(f){try{a(i.throw(f))}catch(r){s(r)}}function a(f){var r;f.done?o(f.value):(r=f.value,r instanceof t?r:new t(function(c){c(r)})).then(l,u)}a((i=i.apply(n,[])).next())})}class Ct{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,i){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),i==null?void 0:i.once){const o=()=>{this.removeEventListener(e,o),this.removeEventListener(e,t)};return this.addEventListener(e,o),o}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var i;(i=this.listeners[e])===null||i===void 0||i.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(i=>i(...t))}}class Ut extends Ct{constructor(e){super(),this.subscriptions=[],this.options=e}onInit(){}init(e){this.wavesurfer=e,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(e=>e())}}const Ot=["audio/webm","audio/wav","audio/mpeg","audio/mp4","audio/mp3"];class Te extends Ut{constructor(e){var t;super(Object.assign(Object.assign({},e),{audioBitsPerSecond:(t=e.audioBitsPerSecond)!==null&&t!==void 0?t:128e3})),this.stream=null,this.mediaRecorder=null}static create(e){return new Te(e||{})}renderMicStream(e){const t=new AudioContext,i=t.createMediaStreamSource(e),o=t.createAnalyser();i.connect(o);const s=o.frequencyBinCount,l=new Float32Array(s),u=s/t.sampleRate;let a;const f=()=>{o.getFloatTimeDomainData(l),this.wavesurfer&&(this.wavesurfer.options.cursorWidth=0,this.wavesurfer.options.interact=!1,this.wavesurfer.load("",[l],u)),a=requestAnimationFrame(f)};return f(),()=>{cancelAnimationFrame(a),i==null||i.disconnect(),t==null||t.close()}}startMic(e){return Me(this,void 0,void 0,function*(){let t;try{t=yield navigator.mediaDevices.getUserMedia({audio:!(e!=null&&e.deviceId)||{deviceId:e.deviceId}})}catch(o){throw new Error("Error accessing the microphone: "+o.message)}const i=this.renderMicStream(t);return this.subscriptions.push(this.once("destroy",i)),this.stream=t,t})}stopMic(){this.stream&&(this.stream.getTracks().forEach(e=>e.stop()),this.stream=null,this.mediaRecorder=null)}startRecording(e){return Me(this,void 0,void 0,function*(){const t=this.stream||(yield this.startMic(e)),i=this.mediaRecorder||new MediaRecorder(t,{mimeType:this.options.mimeType||Ot.find(s=>MediaRecorder.isTypeSupported(s)),audioBitsPerSecond:this.options.audioBitsPerSecond});this.mediaRecorder=i,this.stopRecording();const o=[];i.ondataavailable=s=>{s.data.size>0&&o.push(s.data)},i.onstop=()=>{var s;const l=new Blob(o,{type:i.mimeType});this.emit("record-end",l),this.options.renderRecordedAudio!==!1&&((s=this.wavesurfer)===null||s===void 0||s.load(URL.createObjectURL(l)))},i.start(),this.emit("record-start")})}isRecording(){var e;return((e=this.mediaRecorder)===null||e===void 0?void 0:e.state)==="recording"}isPaused(){var e;return((e=this.mediaRecorder)===null||e===void 0?void 0:e.state)==="paused"}stopRecording(){var e;this.isRecording()&&((e=this.mediaRecorder)===null||e===void 0||e.stop())}pauseRecording(){var e;this.isRecording()&&((e=this.mediaRecorder)===null||e===void 0||e.pause(),this.emit("record-pause"))}resumeRecording(){var e;this.isPaused()&&((e=this.mediaRecorder)===null||e===void 0||e.resume(),this.emit("record-resume"))}static getAvailableAudioDevices(){return Me(this,void 0,void 0,function*(){return navigator.mediaDevices.enumerateDevices().then(e=>e.filter(t=>t.kind==="audioinput"))})}destroy(){super.destroy(),this.stopRecording(),this.stopMic()}}function He(n,e,t){const i=n.slice();return i[3]=e[t],i}function Nt(n){let e,t=Oe(n[0]),i=[];for(let o=0;o<t.length;o+=1)i[o]=qe(He(n,t,o));return{c(){for(let o=0;o<i.length;o+=1)i[o].c();e=pe()},l(o){for(let s=0;s<i.length;s+=1)i[s].l(o);e=pe()},m(o,s){for(let l=0;l<i.length;l+=1)i[l]&&i[l].m(o,s);V(o,e,s)},p(o,s){if(s&1){t=Oe(o[0]);let l;for(l=0;l<t.length;l+=1){const u=He(o,t,l);i[l]?i[l].p(u,s):(i[l]=qe(u),i[l].c(),i[l].m(e.parentNode,e))}for(;l<i.length;l+=1)i[l].d(1);i.length=t.length}},d(o){o&&p(e),bt(i,o)}}}function zt(n){let e,t=n[1]("audio.no_microphone")+"",i;return{c(){e=I("option"),i=ee(t),this.h()},l(o){e=M(o,"OPTION",{});var s=C(e);i=te(s,t),s.forEach(p),this.h()},h(){e.__value="",Ae(e,e.__value)},m(o,s){V(o,e,s),k(e,i)},p(o,s){s&2&&t!==(t=o[1]("audio.no_microphone")+"")&&ie(i,t)},d(o){o&&p(e)}}}function qe(n){let e,t=n[3].label+"",i,o;return{c(){e=I("option"),i=ee(t),this.h()},l(s){e=M(s,"OPTION",{});var l=C(e);i=te(l,t),l.forEach(p),this.h()},h(){e.__value=o=n[3].deviceId,Ae(e,e.__value)},m(s,l){V(s,e,l),k(e,i)},p(s,l){l&1&&t!==(t=s[3].label+"")&&ie(i,t),l&1&&o!==(o=s[3].deviceId)&&(e.__value=o,Ae(e,e.__value))},d(s){s&&p(e)}}}function Vt(n){let e,t;function i(l,u){return l[0].length===0?zt:Nt}let o=i(n),s=o(n);return{c(){e=I("select"),s.c(),this.h()},l(l){e=M(l,"SELECT",{class:!0,"aria-label":!0});var u=C(e);s.l(u),u.forEach(p),this.h()},h(){R(e,"class","mic-select svelte-1ya9x7a"),R(e,"aria-label","Select input device"),e.disabled=t=n[0].length===0},m(l,u){V(l,e,u),s.m(e,null)},p(l,[u]){o===(o=i(l))&&s?s.p(l,u):(s.d(1),s=o(l),s&&(s.c(),s.m(e,null))),u&1&&t!==(t=l[0].length===0)&&(e.disabled=t)},i:be,o:be,d(l){l&&p(e),s.d()}}}function jt(n,e,t){let{i18n:i}=e,{micDevices:o=[]}=e;const s=Le();return n.$$set=l=>{"i18n"in l&&t(1,i=l.i18n),"micDevices"in l&&t(0,o=l.micDevices)},n.$$.update=()=>{if(n.$$.dirty&2&&typeof window<"u")try{let l=[];Te.getAvailableAudioDevices().then(u=>{t(0,o=u),u.forEach(a=>{a.deviceId&&l.push(a)}),t(0,o=l)})}catch(l){throw l instanceof DOMException&&l.name=="NotAllowedError"&&s("error",i("audio.allow_recording_access")),l}},[o,i]}class xe extends Ee{constructor(e){super(),De(this,e,jt,Vt,Re,{i18n:1,micDevices:0})}}function Fe(n){let e,t;return{c(){e=I("time"),t=ee(n[2]),this.h()},l(i){e=M(i,"TIME",{class:!0});var o=C(e);t=te(o,n[2]),o.forEach(p),this.h()},h(){R(e,"class","duration-button duration svelte-1oiuk2f")},m(i,o){V(i,e,o),k(e,t)},p(i,o){o&4&&ie(t,i[2])},d(i){i&&p(e)}}}function Ht(n){let e,t,i,o=n[1]("audio.record")+"",s,l,u,a=n[1]("audio.stop")+"",f,r,c,d,g=n[1]("audio.stop")+"",D,L,h,E,m,v,H=n[1]("audio.resume")+"",y,F,G,Y,W,U,w,de;E=new Bt({});let O=n[4]&&!n[3]&&Fe(n);function re(P){n[23](P)}let ve={i18n:n[1]};return n[5]!==void 0&&(ve.micDevices=n[5]),Y=new xe({props:ve}),j.push(()=>ne(Y,"micDevices",re)),{c(){e=I("div"),t=I("div"),i=I("button"),s=ee(o),l=N(),u=I("button"),f=ee(a),c=N(),d=I("button"),D=ee(g),L=N(),h=I("button"),J(E.$$.fragment),m=N(),v=I("button"),y=ee(H),F=N(),O&&O.c(),G=N(),J(Y.$$.fragment),this.h()},l(P){e=M(P,"DIV",{class:!0});var T=C(e);t=M(T,"DIV",{class:!0});var B=C(t);i=M(B,"BUTTON",{class:!0});var x=C(i);s=te(x,o),x.forEach(p),l=z(B),u=M(B,"BUTTON",{class:!0});var $=C(u);f=te($,a),$.forEach(p),c=z(B),d=M(B,"BUTTON",{id:!0,class:!0});var ae=C(d);D=te(ae,g),ae.forEach(p),L=z(B),h=M(B,"BUTTON",{"aria-label":!0,class:!0});var _e=C(h);K(E.$$.fragment,_e),_e.forEach(p),m=z(B),v=M(B,"BUTTON",{class:!0});var se=C(v);y=te(se,H),se.forEach(p),F=z(B),O&&O.l(B),B.forEach(p),G=z(T),K(Y.$$.fragment,T),T.forEach(p),this.h()},h(){R(i,"class","record record-button svelte-1oiuk2f"),R(u,"class",r="stop-button "+(n[0].isPaused()?"stop-button-paused":"")+" svelte-1oiuk2f"),R(d,"id","stop-paused"),R(d,"class","stop-button-paused svelte-1oiuk2f"),R(h,"aria-label","pause"),R(h,"class","pause-button svelte-1oiuk2f"),R(v,"class","resume-button svelte-1oiuk2f"),R(t,"class","wrapper svelte-1oiuk2f"),R(e,"class","controls svelte-1oiuk2f")},m(P,T){V(P,e,T),k(e,t),k(t,i),k(i,s),n[13](i),k(t,l),k(t,u),k(u,f),n[15](u),k(t,c),k(t,d),k(d,D),n[17](d),k(t,L),k(t,h),Q(E,h,null),n[19](h),k(t,m),k(t,v),k(v,y),n[21](v),k(t,F),O&&O.m(t,null),k(e,G),Q(Y,e,null),U=!0,w||(de=[fe(i,"click",n[14]),fe(u,"click",n[16]),fe(d,"click",n[18]),fe(h,"click",n[20]),fe(v,"click",n[22])],w=!0)},p(P,[T]){(!U||T&2)&&o!==(o=P[1]("audio.record")+"")&&ie(s,o),(!U||T&2)&&a!==(a=P[1]("audio.stop")+"")&&ie(f,a),(!U||T&1&&r!==(r="stop-button "+(P[0].isPaused()?"stop-button-paused":"")+" svelte-1oiuk2f"))&&R(u,"class",r),(!U||T&2)&&g!==(g=P[1]("audio.stop")+"")&&ie(D,g),(!U||T&2)&&H!==(H=P[1]("audio.resume")+"")&&ie(y,H),P[4]&&!P[3]?O?O.p(P,T):(O=Fe(P),O.c(),O.m(t,null)):O&&(O.d(1),O=null);const B={};T&2&&(B.i18n=P[1]),!W&&T&32&&(W=!0,B.micDevices=P[5],oe(()=>W=!1)),Y.$set(B)},i(P){U||(A(E.$$.fragment,P),A(Y.$$.fragment,P),U=!0)},o(P){S(E.$$.fragment,P),S(Y.$$.fragment,P),U=!1},d(P){P&&p(e),n[13](null),n[15](null),n[17](null),X(E),n[19](null),n[21](null),O&&O.d(),X(Y),w=!1,vt(de)}}}function qt(n,e,t){let{record:i}=e,{i18n:o}=e,{recording:s=!1}=e,l=[],u,a,f,r,c,d=!1,{record_time:g}=e,{show_recording_waveform:D}=e,{timing:L=!1}=e;function h(w){j[w?"unshift":"push"](()=>{u=w,t(6,u),t(0,i)})}const E=()=>i.startRecording();function m(w){j[w?"unshift":"push"](()=>{r=w,t(9,r),t(0,i)})}const v=()=>{i.isPaused()&&(i.resumeRecording(),i.stopRecording()),i.stopRecording()};function H(w){j[w?"unshift":"push"](()=>{c=w,t(10,c),t(0,i)})}const y=()=>{i.isPaused()&&(i.resumeRecording(),i.stopRecording()),i.stopRecording()};function F(w){j[w?"unshift":"push"](()=>{a=w,t(7,a),t(0,i)})}const G=()=>i.pauseRecording();function Y(w){j[w?"unshift":"push"](()=>{f=w,t(8,f),t(0,i)})}const W=()=>i.resumeRecording();function U(w){l=w,t(5,l)}return n.$$set=w=>{"record"in w&&t(0,i=w.record),"i18n"in w&&t(1,o=w.i18n),"recording"in w&&t(11,s=w.recording),"record_time"in w&&t(2,g=w.record_time),"show_recording_waveform"in w&&t(3,D=w.show_recording_waveform),"timing"in w&&t(4,L=w.timing)},n.$$.update=()=>{n.$$.dirty&1&&i.on("record-start",()=>{i.startMic(),t(6,u.style.display="none",u),t(9,r.style.display="flex",r),t(7,a.style.display="block",a)}),n.$$.dirty&1&&i.on("record-end",()=>{i.isPaused()&&(i.resumeRecording(),i.stopRecording()),i.stopMic(),t(6,u.style.display="flex",u),t(9,r.style.display="none",r),t(7,a.style.display="none",a),t(6,u.disabled=!1,u)}),n.$$.dirty&1&&i.on("record-pause",()=>{t(7,a.style.display="none",a),t(8,f.style.display="block",f),t(9,r.style.display="none",r),t(10,c.style.display="flex",c)}),n.$$.dirty&1&&i.on("record-resume",()=>{t(7,a.style.display="block",a),t(8,f.style.display="none",f),t(6,u.style.display="none",u),t(9,r.style.display="flex",r),t(10,c.style.display="none",c)}),n.$$.dirty&6145&&(s&&!d?(i.startRecording(),t(12,d=!0)):(i.stopRecording(),t(12,d=!1)))},[i,o,g,D,L,l,u,a,f,r,c,s,d,h,E,m,v,H,y,F,G,Y,W,U]}class Ft extends Ee{constructor(e){super(),De(this,e,qt,Ht,Re,{record:0,i18n:1,recording:11,record_time:2,show_recording_waveform:3,timing:4})}}function Ye(n){let e,t,i="0:00",o,s,l,u=n[0]==="edit"&&n[17]>0&&Ge(n);function a(c,d){return c[16]?Gt:Yt}let f=a(n),r=f(n);return{c(){e=I("div"),t=I("time"),t.textContent=i,o=N(),s=I("div"),u&&u.c(),l=N(),r.c(),this.h()},l(c){e=M(c,"DIV",{class:!0});var d=C(e);t=M(d,"TIME",{class:!0,"data-svelte-h":!0}),Ie(t)!=="svelte-1rda9am"&&(t.textContent=i),o=z(d),s=M(d,"DIV",{});var g=C(s);u&&u.l(g),l=z(g),r.l(g),g.forEach(p),d.forEach(p),this.h()},h(){R(t,"class","time svelte-9n45fh"),R(e,"class","timestamps svelte-9n45fh")},m(c,d){V(c,e,d),k(e,t),n[23](t),k(e,o),k(e,s),u&&u.m(s,null),k(s,l),r.m(s,null)},p(c,d){c[0]==="edit"&&c[17]>0?u?u.p(c,d):(u=Ge(c),u.c(),u.m(s,l)):u&&(u.d(1),u=null),f===(f=a(c))&&r?r.p(c,d):(r.d(1),r=f(c),r&&(r.c(),r.m(s,null)))},d(c){c&&p(e),n[23](null),u&&u.d(),r.d()}}}function Ge(n){let e,t=ce(n[17])+"",i;return{c(){e=I("time"),i=ee(t),this.h()},l(o){e=M(o,"TIME",{class:!0});var s=C(e);i=te(s,t),s.forEach(p),this.h()},h(){R(e,"class","trim-duration svelte-9n45fh")},m(o,s){V(o,e,s),k(e,i)},p(o,s){s[0]&131072&&t!==(t=ce(o[17])+"")&&ie(i,t)},d(o){o&&p(e)}}}function Yt(n){let e,t="0:00";return{c(){e=I("time"),e.textContent=t,this.h()},l(i){e=M(i,"TIME",{class:!0,"data-svelte-h":!0}),Ie(e)!=="svelte-1llsiqq"&&(e.textContent=t),this.h()},h(){R(e,"class","duration svelte-9n45fh")},m(i,o){V(i,e,o),n[24](e)},p:be,d(i){i&&p(e),n[24](null)}}}function Gt(n){let e,t=ce(n[15])+"",i;return{c(){e=I("time"),i=ee(t),this.h()},l(o){e=M(o,"TIME",{class:!0});var s=C(e);i=te(s,t),s.forEach(p),this.h()},h(){R(e,"class","duration svelte-9n45fh")},m(o,s){V(o,e,s),k(e,i)},p(o,s){s[0]&32768&&t!==(t=ce(o[15])+"")&&ie(i,t)},d(o){o&&p(e)}}}function Je(n){let e,t,i;function o(l){n[25](l)}let s={i18n:n[1],timing:n[16],recording:n[5],show_recording_waveform:n[2].show_recording_waveform,record_time:ce(n[15])};return n[7]!==void 0&&(s.record=n[7]),e=new Ft({props:s}),j.push(()=>ne(e,"record",o)),{c(){J(e.$$.fragment)},l(l){K(e.$$.fragment,l)},m(l,u){Q(e,l,u),i=!0},p(l,u){const a={};u[0]&2&&(a.i18n=l[1]),u[0]&65536&&(a.timing=l[16]),u[0]&32&&(a.recording=l[5]),u[0]&4&&(a.show_recording_waveform=l[2].show_recording_waveform),u[0]&32768&&(a.record_time=ce(l[15])),!t&&u[0]&128&&(t=!0,a.record=l[7],oe(()=>t=!1)),e.$set(a)},i(l){i||(A(e.$$.fragment,l),i=!0)},o(l){S(e.$$.fragment,l),i=!1},d(l){X(e,l)}}}function Ke(n){let e,t,i,o,s;function l(r){n[26](r)}function u(r){n[27](r)}function a(r){n[28](r)}let f={container:n[11],playing:n[10],audio_duration:n[14],i18n:n[1],editable:n[4],interactive:!0,handle_trim_audio:n[18],show_redo:!0,handle_reset_value:n[3],waveform_options:n[2]};return n[6]!==void 0&&(f.waveform=n[6]),n[17]!==void 0&&(f.trimDuration=n[17]),n[0]!==void 0&&(f.mode=n[0]),e=new Lt({props:f}),j.push(()=>ne(e,"waveform",l)),j.push(()=>ne(e,"trimDuration",u)),j.push(()=>ne(e,"mode",a)),{c(){J(e.$$.fragment)},l(r){K(e.$$.fragment,r)},m(r,c){Q(e,r,c),s=!0},p(r,c){const d={};c[0]&2048&&(d.container=r[11]),c[0]&1024&&(d.playing=r[10]),c[0]&16384&&(d.audio_duration=r[14]),c[0]&2&&(d.i18n=r[1]),c[0]&16&&(d.editable=r[4]),c[0]&8&&(d.handle_reset_value=r[3]),c[0]&4&&(d.waveform_options=r[2]),!t&&c[0]&64&&(t=!0,d.waveform=r[6],oe(()=>t=!1)),!i&&c[0]&131072&&(i=!0,d.trimDuration=r[17],oe(()=>i=!1)),!o&&c[0]&1&&(o=!0,d.mode=r[0],oe(()=>o=!1)),e.$set(d)},i(r){s||(A(e.$$.fragment,r),s=!0)},o(r){S(e.$$.fragment,r),s=!1},d(r){X(e,r)}}}function Jt(n){let e,t,i,o,s,l,u,a,f=(n[16]||n[13])&&n[2].show_recording_waveform&&Ye(n),r=n[12]&&!n[13]&&Je(n),c=n[6]&&n[13]&&Ke(n);return{c(){e=I("div"),t=I("div"),i=N(),o=I("div"),s=N(),f&&f.c(),l=N(),r&&r.c(),u=N(),c&&c.c(),this.h()},l(d){e=M(d,"DIV",{class:!0});var g=C(e);t=M(g,"DIV",{class:!0,"data-testid":!0}),C(t).forEach(p),i=z(g),o=M(g,"DIV",{"data-testid":!0}),C(o).forEach(p),s=z(g),f&&f.l(g),l=z(g),r&&r.l(g),u=z(g),c&&c.l(g),g.forEach(p),this.h()},h(){R(t,"class","microphone svelte-9n45fh"),R(t,"data-testid","microphone-waveform"),R(o,"data-testid","recording-waveform"),R(e,"class","component-wrapper svelte-9n45fh")},m(d,g){V(d,e,g),k(e,t),n[21](t),k(e,i),k(e,o),n[22](o),k(e,s),f&&f.m(e,null),k(e,l),r&&r.m(e,null),k(e,u),c&&c.m(e,null),a=!0},p(d,g){(d[16]||d[13])&&d[2].show_recording_waveform?f?f.p(d,g):(f=Ye(d),f.c(),f.m(e,l)):f&&(f.d(1),f=null),d[12]&&!d[13]?r?(r.p(d,g),g[0]&12288&&A(r,1)):(r=Je(d),r.c(),A(r,1),r.m(e,u)):r&&(we(),S(r,1,1,()=>{r=null}),ke()),d[6]&&d[13]?c?(c.p(d,g),g[0]&8256&&A(c,1)):(c=Ke(d),c.c(),A(c,1),c.m(e,null)):c&&(we(),S(c,1,1,()=>{c=null}),ke())},i(d){a||(A(r),A(c),a=!0)},o(d){S(r),S(c),a=!1},d(d){d&&p(e),n[21](null),n[22](null),f&&f.d(),r&&r.d(),c&&c.d()}}}function Kt(n,e,t){let{mode:i}=e,{i18n:o}=e,{dispatch_blob:s}=e,{waveform_settings:l}=e,{waveform_options:u={show_recording_waveform:!0}}=e,{handle_reset_value:a}=e,{editable:f=!0}=e,{recording:r=!1}=e,c,d,g=!1,D,L,h,E=null,m,v,H,y=0,F,G=!1,Y=0;const W=()=>{clearInterval(F),F=setInterval(()=>{t(15,y++,y)},1e3)},U=Le();function w(){if(W(),t(16,G=!0),U("start_recording"),u.show_recording_waveform){let b=L;b&&(b.style.display="block")}}async function de(b){t(15,y=0),t(16,G=!1),clearInterval(F);try{const le=await b.arrayBuffer(),q=await new AudioContext({sampleRate:l.sampleRate}).decodeAudioData(le);q&&await je(q).then(async ue=>{await s([ue],"change"),await s([ue],"stop_recording")})}catch(le){console.error(le)}}const O=()=>{L&&t(12,L.innerHTML="",L),c!==void 0&&c.destroy(),L&&(c=Pe.create({...l,normalize:!1,container:L}),t(7,h=c.registerPlugin(Te.create())),h==null||h.on("record-end",de),h==null||h.on("record-start",w),h==null||h.on("record-pause",()=>{U("pause_recording"),clearInterval(F)}),h==null||h.on("record-end",b=>{t(13,E=URL.createObjectURL(b));const le=L,me=D;le&&(le.style.display="none"),me&&E&&(me.innerHTML="",re())}))},re=()=>{let b=D;!E||!b||t(6,d=Pe.create({container:b,url:E,...l}))},ve=async(b,le)=>{t(0,i="edit");const me=d.getDecodedData();me&&await je(me,b,le).then(async q=>{await s([q],"change"),await s([q],"stop_recording"),d.destroy(),re()}),U("edit")};Ze(()=>{O(),window.addEventListener("keydown",b=>{b.key==="ArrowRight"?Ve(d,.1):b.key==="ArrowLeft"&&Ve(d,-.1)})});function P(b){j[b?"unshift":"push"](()=>{L=b,t(12,L)})}function T(b){j[b?"unshift":"push"](()=>{D=b,t(11,D)})}function B(b){j[b?"unshift":"push"](()=>{m=b,t(8,m),t(6,d)})}function x(b){j[b?"unshift":"push"](()=>{v=b,t(9,v),t(6,d)})}function $(b){h=b,t(7,h)}function ae(b){d=b,t(6,d)}function _e(b){Y=b,t(17,Y)}function se(b){i=b,t(0,i)}return n.$$set=b=>{"mode"in b&&t(0,i=b.mode),"i18n"in b&&t(1,o=b.i18n),"dispatch_blob"in b&&t(19,s=b.dispatch_blob),"waveform_settings"in b&&t(20,l=b.waveform_settings),"waveform_options"in b&&t(2,u=b.waveform_options),"handle_reset_value"in b&&t(3,a=b.handle_reset_value),"editable"in b&&t(4,f=b.editable),"recording"in b&&t(5,r=b.recording)},n.$$.update=()=>{n.$$.dirty[0]&128&&(h==null||h.on("record-resume",()=>{W()})),n.$$.dirty[0]&576&&(d==null||d.on("decode",b=>{t(14,H=b),v&&t(9,v.textContent=ce(b),v)})),n.$$.dirty[0]&320&&(d==null||d.on("timeupdate",b=>m&&t(8,m.textContent=ce(b),m))),n.$$.dirty[0]&64&&(d==null||d.on("pause",()=>{U("pause"),t(10,g=!1)})),n.$$.dirty[0]&64&&(d==null||d.on("play",()=>{U("play"),t(10,g=!0)})),n.$$.dirty[0]&64&&(d==null||d.on("finish",()=>{U("stop"),t(10,g=!1)}))},[i,o,u,a,f,r,d,h,m,v,g,D,L,E,H,y,G,Y,ve,s,l,P,T,B,x,$,ae,_e,se]}class Qt extends Ee{constructor(e){super(),De(this,e,Kt,Jt,Re,{mode:0,i18n:1,dispatch_blob:19,waveform_settings:20,waveform_options:2,handle_reset_value:3,editable:4,recording:5},null,[-1,-1])}}function Qe(n){let e;return{c(){e=I("div"),this.h()},l(t){e=M(t,"DIV",{}),C(e).forEach(p),this.h()},h(){Ne(e,"display",n[0]?"block":"none")},m(t,i){V(t,e,i),n[11](e)},p(t,i){i&1&&Ne(e,"display",t[0]?"block":"none")},d(t){t&&p(e),n[11](null)}}}function Xt(n){let e,t,i='<span class="dot"></span>',o,s=n[4]("audio.record")+"",l,u,a;return{c(){e=I("button"),t=I("span"),t.innerHTML=i,o=N(),l=ee(s),this.h()},l(f){e=M(f,"BUTTON",{class:!0});var r=C(e);t=M(r,"SPAN",{class:!0,"data-svelte-h":!0}),Ie(t)!=="svelte-1dwz2xe"&&(t.innerHTML=i),o=z(r),l=te(r,s),r.forEach(p),this.h()},h(){R(t,"class","record-icon"),R(e,"class","record-button svelte-1fz19cj")},m(f,r){V(f,e,r),k(e,t),k(e,o),k(e,l),u||(a=fe(e,"click",n[14]),u=!0)},p(f,r){r&16&&s!==(s=f[4]("audio.record")+"")&&ie(l,s)},i:be,o:be,d(f){f&&p(e),u=!1,a()}}}function Zt(n){let e,t,i,o,s=n[4]("audio.waiting")+"",l,u,a,f;return i=new Mt({}),{c(){e=I("button"),t=I("div"),J(i.$$.fragment),o=N(),l=ee(s),this.h()},l(r){e=M(r,"BUTTON",{class:!0});var c=C(e);t=M(c,"DIV",{class:!0});var d=C(t);K(i.$$.fragment,d),d.forEach(p),o=z(c),l=te(c,s),c.forEach(p),this.h()},h(){R(t,"class","icon svelte-1fz19cj"),R(e,"class","spinner-button svelte-1fz19cj")},m(r,c){V(r,e,c),k(e,t),Q(i,t,null),k(e,o),k(e,l),u=!0,a||(f=fe(e,"click",n[13]),a=!0)},p(r,c){(!u||c&16)&&s!==(s=r[4]("audio.waiting")+"")&&ie(l,s)},i(r){u||(A(i.$$.fragment,r),u=!0)},o(r){S(i.$$.fragment,r),u=!1},d(r){r&&p(e),X(i),a=!1,f()}}}function Wt(n){let e,t,i='<span class="pinger"></span> <span class="dot"></span>',o,s=(n[1]?n[4]("audio.pause"):n[4]("audio.stop"))+"",l,u,a,f;return{c(){e=I("button"),t=I("span"),t.innerHTML=i,o=N(),l=ee(s),this.h()},l(r){e=M(r,"BUTTON",{class:!0});var c=C(e);t=M(c,"SPAN",{class:!0,"data-svelte-h":!0}),Ie(t)!=="svelte-bla7qm"&&(t.innerHTML=i),o=z(c),l=te(c,s),c.forEach(p),this.h()},h(){R(t,"class","record-icon"),R(e,"class",u=ze(n[1]?"stop-button-paused":"stop-button")+" svelte-1fz19cj")},m(r,c){V(r,e,c),k(e,t),k(e,o),k(e,l),a||(f=fe(e,"click",n[12]),a=!0)},p(r,c){c&18&&s!==(s=(r[1]?r[4]("audio.pause"):r[4]("audio.stop"))+"")&&ie(l,s),c&2&&u!==(u=ze(r[1]?"stop-button-paused":"stop-button")+" svelte-1fz19cj")&&R(e,"class",u)},i:be,o:be,d(r){r&&p(e),a=!1,f()}}}function xt(n){let e,t,i,o,s,l,u,a,f,r=n[5].show_recording_waveform&&Qe(n);const c=[Wt,Zt,Xt],d=[];function g(h,E){return h[0]&&!h[6]?0:h[0]&&h[6]?1:2}o=g(n),s=d[o]=c[o](n);function D(h){n[15](h)}let L={i18n:n[4]};return n[9]!==void 0&&(L.micDevices=n[9]),u=new xe({props:L}),j.push(()=>ne(u,"micDevices",D)),{c(){e=I("div"),r&&r.c(),t=N(),i=I("div"),s.c(),l=N(),J(u.$$.fragment),this.h()},l(h){e=M(h,"DIV",{class:!0});var E=C(e);r&&r.l(E),t=z(E),i=M(E,"DIV",{class:!0});var m=C(i);s.l(m),l=z(m),K(u.$$.fragment,m),m.forEach(p),E.forEach(p),this.h()},h(){R(i,"class","controls svelte-1fz19cj"),R(e,"class","mic-wrap svelte-1fz19cj")},m(h,E){V(h,e,E),r&&r.m(e,null),k(e,t),k(e,i),d[o].m(i,null),k(i,l),Q(u,i,null),f=!0},p(h,[E]){h[5].show_recording_waveform?r?r.p(h,E):(r=Qe(h),r.c(),r.m(e,t)):r&&(r.d(1),r=null);let m=o;o=g(h),o===m?d[o].p(h,E):(we(),S(d[m],1,1,()=>{d[m]=null}),ke(),s=d[o],s?s.p(h,E):(s=d[o]=c[o](h),s.c()),A(s,1),s.m(i,l));const v={};E&16&&(v.i18n=h[4]),!a&&E&512&&(a=!0,v.micDevices=h[9],oe(()=>a=!1)),u.$set(v)},i(h){f||(A(s),A(u.$$.fragment,h),f=!0)},o(h){S(s),S(u.$$.fragment,h),f=!1},d(h){h&&p(e),r&&r.d(),d[o].d(),X(u)}}}function $t(n,e,t){let{recording:i=!1}=e,{paused_recording:o=!1}=e,{stop:s}=e,{record:l}=e,{i18n:u}=e,{waveform_settings:a}=e,{waveform_options:f={show_recording_waveform:!0}}=e,{waiting:r=!1}=e,c,d,g,D=[];Ze(()=>{L()});const L=()=>{c!==void 0&&c.destroy(),g&&(c=Pe.create({...a,height:100,container:g}),t(7,d=c.registerPlugin(Te.create())))};function h(y){j[y?"unshift":"push"](()=>{g=y,t(8,g)})}const E=()=>{d==null||d.stopMic(),s()},m=()=>{s()},v=()=>{d==null||d.startMic(),l()};function H(y){D=y,t(9,D)}return n.$$set=y=>{"recording"in y&&t(0,i=y.recording),"paused_recording"in y&&t(1,o=y.paused_recording),"stop"in y&&t(2,s=y.stop),"record"in y&&t(3,l=y.record),"i18n"in y&&t(4,u=y.i18n),"waveform_settings"in y&&t(10,a=y.waveform_settings),"waveform_options"in y&&t(5,f=y.waveform_options),"waiting"in y&&t(6,r=y.waiting)},[i,o,s,l,u,f,r,d,g,D,a,h,E,m,v,H]}class ei extends Ee{constructor(e){super(),De(this,e,$t,xt,Re,{recording:0,paused_recording:1,stop:2,record:3,i18n:4,waveform_settings:10,waveform_options:5,waiting:6})}}function ti(n){let e,t,i,o,s;e=new We({props:{i18n:n[12],download:n[9]?n[2].url:null}}),e.$on("clear",n[28]),e.$on("edit",n[47]);function l(a){n[48](a)}let u={value:n[2],label:n[5],i18n:n[12],dispatch_blob:n[26],waveform_settings:n[13],waveform_options:n[15],trim_region_settings:n[14],handle_reset_value:n[16],editable:n[17],loop:n[7],interactive:!0};return n[24]!==void 0&&(u.mode=n[24]),i=new St({props:u}),j.push(()=>ne(i,"mode",l)),i.$on("stop",n[49]),i.$on("play",n[50]),i.$on("pause",n[51]),i.$on("edit",n[52]),{c(){J(e.$$.fragment),t=N(),J(i.$$.fragment)},l(a){K(e.$$.fragment,a),t=z(a),K(i.$$.fragment,a)},m(a,f){Q(e,a,f),V(a,t,f),Q(i,a,f),s=!0},p(a,f){const r={};f[0]&4096&&(r.i18n=a[12]),f[0]&516&&(r.download=a[9]?a[2].url:null),e.$set(r);const c={};f[0]&4&&(c.value=a[2]),f[0]&32&&(c.label=a[5]),f[0]&4096&&(c.i18n=a[12]),f[0]&8192&&(c.waveform_settings=a[13]),f[0]&32768&&(c.waveform_options=a[15]),f[0]&16384&&(c.trim_region_settings=a[14]),f[0]&65536&&(c.handle_reset_value=a[16]),f[0]&131072&&(c.editable=a[17]),f[0]&128&&(c.loop=a[7]),!o&&f[0]&16777216&&(o=!0,c.mode=a[24],oe(()=>o=!1)),i.$set(c)},i(a){s||(A(e.$$.fragment,a),A(i.$$.fragment,a),s=!0)},o(a){S(e.$$.fragment,a),S(i.$$.fragment,a),s=!1},d(a){a&&p(t),X(e,a),X(i,a)}}}function ii(n){let e,t,i,o;const s=[oi,ni],l=[];function u(a,f){return a[3]==="microphone"?0:a[3]==="upload"?1:-1}return~(e=u(n))&&(t=l[e]=s[e](n)),{c(){t&&t.c(),i=pe()},l(a){t&&t.l(a),i=pe()},m(a,f){~e&&l[e].m(a,f),V(a,i,f),o=!0},p(a,f){let r=e;e=u(a),e===r?~e&&l[e].p(a,f):(t&&(we(),S(l[r],1,1,()=>{l[r]=null}),ke()),~e?(t=l[e],t?t.p(a,f):(t=l[e]=s[e](a),t.c()),A(t,1),t.m(i.parentNode,i)):t=null)},i(a){o||(A(t),o=!0)},o(a){S(t),o=!1},d(a){a&&p(i),~e&&l[e].d(a)}}}function ni(n){let e,t,i,o;function s(a){n[44](a)}function l(a){n[45](a)}let u={filetype:"audio/aac,audio/midi,audio/mpeg,audio/ogg,audio/wav,audio/x-wav,audio/opus,audio/webm,audio/flac,audio/vnd.rn-realaudio,audio/x-ms-wma,audio/x-aiff,audio/amr,audio/*",root:n[6],max_file_size:n[18],upload:n[19],stream_handler:n[20],aria_label:n[12]("audio.drop_to_upload"),$$slots:{default:[ri]},$$scope:{ctx:n}};return n[0]!==void 0&&(u.dragging=n[0]),n[4]!==void 0&&(u.uploading=n[4]),e=new Dt({props:u}),j.push(()=>ne(e,"dragging",s)),j.push(()=>ne(e,"uploading",l)),e.$on("load",n[29]),e.$on("error",n[46]),{c(){J(e.$$.fragment)},l(a){K(e.$$.fragment,a)},m(a,f){Q(e,a,f),o=!0},p(a,f){const r={};f[0]&64&&(r.root=a[6]),f[0]&262144&&(r.max_file_size=a[18]),f[0]&524288&&(r.upload=a[19]),f[0]&1048576&&(r.stream_handler=a[20]),f[0]&4096&&(r.aria_label=a[12]("audio.drop_to_upload")),f[1]&8388608&&(r.$$scope={dirty:f,ctx:a}),!t&&f[0]&1&&(t=!0,r.dragging=a[0],oe(()=>t=!1)),!i&&f[0]&16&&(i=!0,r.uploading=a[4],oe(()=>i=!1)),e.$set(r)},i(a){o||(A(e.$$.fragment,a),o=!0)},o(a){S(e.$$.fragment,a),o=!1},d(a){X(e,a)}}}function oi(n){let e,t,i,o,s,l;e=new We({props:{i18n:n[12]}}),e.$on("clear",n[28]);const u=[li,si],a=[];function f(r,c){return r[11]?0:1}return i=f(n),o=a[i]=u[i](n),{c(){J(e.$$.fragment),t=N(),o.c(),s=pe()},l(r){K(e.$$.fragment,r),t=z(r),o.l(r),s=pe()},m(r,c){Q(e,r,c),V(r,t,c),a[i].m(r,c),V(r,s,c),l=!0},p(r,c){const d={};c[0]&4096&&(d.i18n=r[12]),e.$set(d);let g=i;i=f(r),i===g?a[i].p(r,c):(we(),S(a[g],1,1,()=>{a[g]=null}),ke(),o=a[i],o?o.p(r,c):(o=a[i]=u[i](r),o.c()),A(o,1),o.m(s.parentNode,s))},i(r){l||(A(e.$$.fragment,r),A(o),l=!0)},o(r){S(e.$$.fragment,r),S(o),l=!1},d(r){r&&(p(t),p(s)),X(e,r),a[i].d(r)}}}function ri(n){let e;const t=n[39].default,i=wt(t,n,n[54],null);return{c(){i&&i.c()},l(o){i&&i.l(o)},m(o,s){i&&i.m(o,s),e=!0},p(o,s){i&&i.p&&(!e||s[1]&8388608)&&kt(i,t,o,o[54],e?Et(t,o[54],s,null):yt(o[54]),null)},i(o){e||(A(i,o),e=!0)},o(o){S(i,o),e=!1},d(o){i&&i.d(o)}}}function si(n){let e,t,i;function o(l){n[40](l)}let s={i18n:n[12],editable:n[17],recording:n[1],dispatch_blob:n[26],waveform_settings:n[13],waveform_options:n[15],handle_reset_value:n[16]};return n[24]!==void 0&&(s.mode=n[24]),e=new Qt({props:s}),j.push(()=>ne(e,"mode",o)),e.$on("start_recording",n[41]),e.$on("pause_recording",n[42]),e.$on("stop_recording",n[43]),{c(){J(e.$$.fragment)},l(l){K(e.$$.fragment,l)},m(l,u){Q(e,l,u),i=!0},p(l,u){const a={};u[0]&4096&&(a.i18n=l[12]),u[0]&131072&&(a.editable=l[17]),u[0]&2&&(a.recording=l[1]),u[0]&8192&&(a.waveform_settings=l[13]),u[0]&32768&&(a.waveform_options=l[15]),u[0]&65536&&(a.handle_reset_value=l[16]),!t&&u[0]&16777216&&(t=!0,a.mode=l[24],oe(()=>t=!1)),e.$set(a)},i(l){i||(A(e.$$.fragment,l),i=!0)},o(l){S(e.$$.fragment,l),i=!1},d(l){X(e,l)}}}function li(n){let e,t;return e=new ei({props:{record:n[27],recording:n[1],stop:n[30],i18n:n[12],waveform_settings:n[13],waveform_options:n[15],waiting:n[23]==="waiting"}}),{c(){J(e.$$.fragment)},l(i){K(e.$$.fragment,i)},m(i,o){Q(e,i,o),t=!0},p(i,o){const s={};o[0]&2&&(s.recording=i[1]),o[0]&4096&&(s.i18n=i[12]),o[0]&8192&&(s.waveform_settings=i[13]),o[0]&32768&&(s.waveform_options=i[15]),o[0]&8388608&&(s.waiting=i[23]==="waiting"),e.$set(s)},i(i){t||(A(e.$$.fragment,i),t=!0)},o(i){S(e.$$.fragment,i),t=!1},d(i){X(e,i)}}}function ai(n){let e,t,i,o,s,l,u,a,f,r,c,d;e=new Tt({props:{show_label:n[8],Icon:It,float:n[3]==="upload"&&n[2]===null,label:n[5]||n[12]("audio.audio")}}),o=new Pt({props:{time_limit:n[22]}});const g=[ii,ti],D=[];function L(m,v){return m[2]===null||m[11]?0:1}l=L(n),u=D[l]=g[l](n);function h(m){n[53](m)}let E={sources:n[10],handle_clear:n[28]};return n[3]!==void 0&&(E.active_source=n[3]),f=new At({props:E}),j.push(()=>ne(f,"active_source",h)),{c(){J(e.$$.fragment),t=N(),i=I("div"),J(o.$$.fragment),s=N(),u.c(),a=N(),J(f.$$.fragment),this.h()},l(m){K(e.$$.fragment,m),t=z(m),i=M(m,"DIV",{class:!0});var v=C(i);K(o.$$.fragment,v),s=z(v),u.l(v),a=z(v),K(f.$$.fragment,v),v.forEach(p),this.h()},h(){R(i,"class",c="audio-container "+n[21]+" svelte-1ud6e7m")},m(m,v){Q(e,m,v),V(m,t,v),V(m,i,v),Q(o,i,null),k(i,s),D[l].m(i,null),k(i,a),Q(f,i,null),d=!0},p(m,v){const H={};v[0]&256&&(H.show_label=m[8]),v[0]&12&&(H.float=m[3]==="upload"&&m[2]===null),v[0]&4128&&(H.label=m[5]||m[12]("audio.audio")),e.$set(H);const y={};v[0]&4194304&&(y.time_limit=m[22]),o.$set(y);let F=l;l=L(m),l===F?D[l].p(m,v):(we(),S(D[F],1,1,()=>{D[F]=null}),ke(),u=D[l],u?u.p(m,v):(u=D[l]=g[l](m),u.c()),A(u,1),u.m(i,a));const G={};v[0]&1024&&(G.sources=m[10]),!r&&v[0]&8&&(r=!0,G.active_source=m[3],oe(()=>r=!1)),f.$set(G),(!d||v[0]&2097152&&c!==(c="audio-container "+m[21]+" svelte-1ud6e7m"))&&R(i,"class",c)},i(m){d||(A(e.$$.fragment,m),A(o.$$.fragment,m),A(u),A(f.$$.fragment,m),d=!0)},o(m){S(e.$$.fragment,m),S(o.$$.fragment,m),S(u),S(f.$$.fragment,m),d=!1},d(m){m&&(p(t),p(i)),X(e,m),X(o),D[l].d(),X(f)}}}const Xe=44;function ui(n,e,t){let{$$slots:i={},$$scope:o}=e,{value:s=null}=e,{label:l}=e,{root:u}=e,{loop:a}=e,{show_label:f=!0}=e,{show_download_button:r=!1}=e,{sources:c=["microphone","upload"]}=e,{pending:d=!1}=e,{streaming:g=!1}=e,{i18n:D}=e,{waveform_settings:L}=e,{trim_region_settings:h={}}=e,{waveform_options:E={}}=e,{dragging:m}=e,{active_source:v}=e,{handle_reset_value:H=()=>{}}=e,{editable:y=!0}=e,{max_file_size:F=null}=e,{upload:G}=e,{stream_handler:Y}=e,{stream_every:W}=e,{uploading:U=!1}=e,{recording:w=!1}=e,{class_name:de=""}=e,O=null,re="closed";const ve=_=>{_==="closed"?(t(22,O=null),t(23,re="closed")):_==="waiting"?t(23,re="waiting"):t(23,re="open")},P=_=>{w&&t(22,O=_)};let T,B="",x,$=[],ae=!1,_e=!1,se=[],b;function le(){b=[Ue(()=>import("./module.BkjqLaM6.js"),__vite__mapDeps([0,1,2,3,4,5,6]),import.meta.url),Ue(()=>import("./module.niXGEr8P.js"),__vite__mapDeps([7,1]),import.meta.url)]}typeof window<"u"&&g&&le();const q=Le(),ue=async(_,Z)=>{var Ce;let he=new File(_,"audio.wav");const ye=await Rt([he],Z==="stream");t(2,s=(Ce=await G(ye,u,void 0,F||void 0))==null?void 0:Ce.filter(Boolean)[0]),q(Z,s)};pt(()=>{g&&T&&T.state!=="inactive"&&T.stop()});async function $e(){let _;try{_=await navigator.mediaDevices.getUserMedia({audio:!0})}catch(Z){if(!navigator.mediaDevices){q("error",D("audio.no_device_support"));return}if(Z instanceof DOMException&&Z.name=="NotAllowedError"){q("error",D("audio.allow_recording_access"));return}throw Z}if(_!=null){if(g){const[{MediaRecorder:Z,register:he},{connect:ye}]=await Promise.all(b);await he(await ye()),t(35,T=new Z(_,{mimeType:"audio/wav"})),T.addEventListener("dataavailable",et)}else t(35,T=new MediaRecorder(_)),T.addEventListener("dataavailable",Z=>{se.push(Z.data)});T.addEventListener("stop",async()=>{t(1,w=!1),await ue(se,"change"),await ue(se,"stop_recording"),se=[]}),_e=!0}}async function et(_){let Z=await _.data.arrayBuffer(),he=new Uint8Array(Z);if(x||(t(36,x=new Uint8Array(Z.slice(0,Xe))),he=new Uint8Array(Z.slice(Xe))),d)$.push(he);else{let ye=[x].concat($,[he]);if(!w||re==="waiting")return;ue(ye,"stream"),t(37,$=[])}}async function Se(){t(1,w=!0),q("start_recording"),_e||await $e(),t(36,x=void 0),g&&T.state!="recording"&&T.start(W*1e3)}function tt(){q("change",null),q("clear"),t(24,B=""),t(2,s=null)}function it({detail:_}){t(2,s=_),q("change",_),q("upload",_)}async function Be(){t(1,w=!1),g&&(q("close_stream"),q("stop_recording"),T.stop(),d&&t(38,ae=!0),ue(se,"stop_recording"),q("clear"),t(24,B=""))}function nt(_){B=_,t(24,B)}function ot(_){ge.call(this,n,_)}function rt(_){ge.call(this,n,_)}function st(_){ge.call(this,n,_)}function lt(_){m=_,t(0,m)}function at(_){U=_,t(4,U)}const ut=({detail:_})=>q("error",_),ft=()=>t(24,B="edit");function ct(_){B=_,t(24,B)}function dt(_){ge.call(this,n,_)}function _t(_){ge.call(this,n,_)}function mt(_){ge.call(this,n,_)}function ht(_){ge.call(this,n,_)}function gt(_){v=_,t(3,v)}return n.$$set=_=>{"value"in _&&t(2,s=_.value),"label"in _&&t(5,l=_.label),"root"in _&&t(6,u=_.root),"loop"in _&&t(7,a=_.loop),"show_label"in _&&t(8,f=_.show_label),"show_download_button"in _&&t(9,r=_.show_download_button),"sources"in _&&t(10,c=_.sources),"pending"in _&&t(31,d=_.pending),"streaming"in _&&t(11,g=_.streaming),"i18n"in _&&t(12,D=_.i18n),"waveform_settings"in _&&t(13,L=_.waveform_settings),"trim_region_settings"in _&&t(14,h=_.trim_region_settings),"waveform_options"in _&&t(15,E=_.waveform_options),"dragging"in _&&t(0,m=_.dragging),"active_source"in _&&t(3,v=_.active_source),"handle_reset_value"in _&&t(16,H=_.handle_reset_value),"editable"in _&&t(17,y=_.editable),"max_file_size"in _&&t(18,F=_.max_file_size),"upload"in _&&t(19,G=_.upload),"stream_handler"in _&&t(20,Y=_.stream_handler),"stream_every"in _&&t(32,W=_.stream_every),"uploading"in _&&t(4,U=_.uploading),"recording"in _&&t(1,w=_.recording),"class_name"in _&&t(21,de=_.class_name),"$$scope"in _&&t(54,o=_.$$scope)},n.$$.update=()=>{if(n.$$.dirty[0]&1&&q("drag",m),n.$$.dirty[1]&225&&ae&&d===!1&&(t(38,ae=!1),x&&$)){let _=[x].concat($);t(37,$=[]),ue(_,"stream")}n.$$.dirty[0]&2|n.$$.dirty[1]&16&&!w&&T&&Be(),n.$$.dirty[0]&2|n.$$.dirty[1]&16&&w&&T&&Se()},[m,w,s,v,U,l,u,a,f,r,c,g,D,L,h,E,H,y,F,G,Y,de,O,re,B,q,ue,Se,tt,it,Be,d,W,ve,P,T,x,$,ae,i,nt,ot,rt,st,lt,at,ut,ft,ct,dt,_t,mt,ht,gt,o]}class fi extends Ee{constructor(e){super(),De(this,e,ui,ai,Re,{value:2,label:5,root:6,loop:7,show_label:8,show_download_button:9,sources:10,pending:31,streaming:11,i18n:12,waveform_settings:13,trim_region_settings:14,waveform_options:15,dragging:0,active_source:3,handle_reset_value:16,editable:17,max_file_size:18,upload:19,stream_handler:20,stream_every:32,uploading:4,recording:1,class_name:21,modify_stream:33,set_time_limit:34},null,[-1,-1])}get modify_stream(){return this.$$.ctx[33]}get set_time_limit(){return this.$$.ctx[34]}}const Ei=fi;export{Ei as I};
//# sourceMappingURL=InteractiveAudio.BOik-Knm.js.map
