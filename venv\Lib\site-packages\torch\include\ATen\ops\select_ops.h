#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API select_Dimname {
  using schema = at::Tensor (const at::Tensor &, at::Dimname, int64_t);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::select";
  static constexpr const char* overload_name = "Dimname";
  static constexpr const char* schema_str = "select.Dimname(Tensor(a) self, Dimname dim, int index) -> Tensor(a)";
  static at::Tensor call(const at::Tensor & self, at::Dimname dim, int64_t index);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, at::Dimname dim, int64_t index);
};

struct TORCH_API select_int {
  using schema = at::Tensor (const at::Tensor &, int64_t, c10::SymInt);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::select";
  static constexpr const char* overload_name = "int";
  static constexpr const char* schema_str = "select.int(Tensor(a) self, int dim, SymInt index) -> Tensor(a)";
  static at::Tensor call(const at::Tensor & self, int64_t dim, c10::SymInt index);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, int64_t dim, c10::SymInt index);
};

}} // namespace at::_ops
