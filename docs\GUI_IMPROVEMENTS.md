# GUI改进文档

## 概述

本文档记录了Reverie Agents应用程序的GUI改进和新功能。这些改进旨在提供更现代、更直观的用户体验。

## 主要改进

### 1. 聊天界面布局优化

#### 问题描述
- 用户头像位置不正确，显示在中间而不是右侧
- 缺乏ChatGPT风格的现代化布局

#### 解决方案
- **文件**: `ui/app/components/chat_widget.py`
- **改进内容**:
  - AI消息左对齐，头像在左侧
  - 用户消息右对齐，头像在右侧
  - 移除气泡设计，采用更简洁的ChatGPT风格
  - 优化消息间距和布局

#### 技术实现
```python
# AI消息布局 - 左对齐
avatar_container = QWidget()
avatar_container.setFixedSize(32, 32)
# 消息内容左对齐显示

# 用户消息布局 - 右对齐  
main_layout.addStretch(2)  # 左侧留白
message_container = QWidget()
# 头像固定在右侧
```

### 2. 滚动功能增强

#### 问题描述
- 对话历史缺乏滚动功能
- 滚动条样式不够现代化

#### 解决方案
- **文件**: `ui/app/components/chat_widget.py`
- **改进内容**:
  - 添加自定义滚动条样式
  - 优化滚动区域布局
  - 支持鼠标滚轮操作

#### 技术实现
```python
# 自定义滚动条样式
QScrollBar:vertical {
    background: rgba(45, 53, 72, 0.5);
    width: 12px;
    border-radius: 6px;
}
QScrollBar::handle:vertical {
    background: rgba(102, 126, 234, 0.6);
    border-radius: 6px;
}
```

### 3. 底部文字遮挡修复

#### 问题描述
- 界面底部文字被遮挡
- 状态栏高度不足

#### 解决方案
- **文件**: `ui/app/components/status_bar.py`, `ui/app/main.py`
- **改进内容**:
  - 增加状态栏高度从30px到40px
  - 优化内边距和外边距
  - 改进主窗口底部边距

### 4. 头像自定义功能

#### 新功能描述
- 支持AI和用户头像自定义
- 照片选择、裁剪、缩放功能
- 圆形头像预览

#### 技术实现
- **文件**: `ui/app/components/avatar_editor.py`
- **主要组件**:
  - `AvatarCropDialog`: 头像裁剪对话框
  - `AvatarEditor`: 头像编辑器主组件

#### 功能特性
```python
class AvatarEditor(QWidget):
    """头像编辑器"""
    avatar_changed = pyqtSignal(str, str)  # avatar_type, avatar_path
    
    def select_avatar(self, avatar_type: str):
        """选择头像"""
        # 文件选择对话框
        # 裁剪对话框
        # 保存头像文件
```

#### 集成位置
- **设置页面**: `ui/app/components/settings_dialog.py`
- 在"界面设置"标签页中添加头像设置组

### 5. Agents Hub页面

#### 新功能描述
- 替换下拉菜单选择方式
- 卡片式Agent展示
- Agent详情查看功能

#### 技术实现
- **文件**: `ui/app/components/agents_hub.py`
- **主要组件**:
  - `AgentCard`: Agent卡片组件
  - `AgentDetailDialog`: Agent详情对话框
  - `AgentsHub`: 主页面组件

#### 功能特性
```python
class AgentsHub(QWidget):
    """Agents Hub主页面"""
    agent_selected = pyqtSignal(str)  # persona_id
    back_requested = pyqtSignal()
    
    def load_agents(self):
        """加载所有Agent"""
        # 网格布局显示Agent卡片
        # 每行最多3个卡片
```

#### 页面切换
- **文件**: `ui/app/main.py`
- **实现方式**:
```python
def show_agents_hub(self):
    """显示Agents Hub页面"""
    self.persona_selector.hide()
    self.chat_widget.hide()
    self.agents_hub.show()

def show_chat_page(self):
    """显示聊天页面"""
    self.agents_hub.hide()
    self.persona_selector.show()
    self.chat_widget.show()
```

### 6. Agent详情界面

#### 功能描述
- 详细的Agent信息展示
- 头像、名称、描述、标签
- 完整的prompt内容查看
- Agent选择功能

#### 技术实现
```python
class AgentDetailDialog(QDialog):
    """Agent详情对话框"""
    agent_selected = pyqtSignal(str)
    
    def __init__(self, persona_id: str, persona_info: dict):
        # 模态对话框
        # 详细信息展示
        # 选择按钮
```

#### 界面元素
- 80x80像素头像显示
- Agent名称和类型标签
- NSFW标签（如适用）
- 可滚动的详细介绍文本
- 选择和取消按钮

## 配置文件更新

### 头像配置
```yaml
ui:
  avatars:
    ai: "assets/avatars/ai_avatar.png"
    user: "assets/avatars/user_avatar.png"
```

### 人设配置扩展
```yaml
personas:
  example_agent:
    name: "示例Agent"
    description: "简短描述"
    avatar_path: "path/to/avatar.png"
    type: "assistant"
    nsfw_enabled: false
    prompt: "详细的prompt内容..."
```

## 样式主题

### 现代化设计元素
- 渐变背景和边框
- 圆角设计
- 半透明效果
- 悬停动画
- 统一的色彩方案

### 主要颜色
- 主色调: `#667eea` (蓝紫色)
- 辅助色: `#764ba2` (紫色)
- 背景色: `#1a1f2e` (深蓝灰)
- 文字色: `#e2e8f0` (浅灰)
- 次要文字: `#94a3b8` (中灰)

## 使用说明

### 头像自定义
1. 打开设置页面
2. 选择"界面设置"标签
3. 在"头像设置"区域点击"选择图片"
4. 选择图片文件后进行裁剪调整
5. 点击确定保存

### Agents Hub使用
1. 在主界面点击"🌟 Agents Hub"按钮
2. 浏览可用的Agent卡片
3. 点击卡片查看详情
4. 在详情页面点击"选择此Agent"
5. 自动返回聊天页面并切换Agent

### 聊天界面
- AI消息显示在左侧，带有AI头像
- 用户消息显示在右侧，带有用户头像
- 支持鼠标滚轮滚动查看历史消息
- 底部输入框支持多行文本输入

## 技术细节

### 依赖项
- PyQt6: GUI框架
- PIL/Pillow: 图像处理（头像裁剪）
- pathlib: 文件路径处理

### 文件结构
```
ui/app/components/
├── avatar_editor.py      # 头像编辑器
├── agents_hub.py         # Agents Hub页面
├── chat_widget.py        # 聊天界面（已优化）
├── persona_selector.py   # 人设选择器（已扩展）
├── status_bar.py         # 状态栏（已优化）
└── settings_dialog.py    # 设置对话框（已扩展）

docs/
└── GUI_IMPROVEMENTS.md   # 本文档
```

### 信号连接
```python
# 主要信号流
persona_selector.agents_hub_requested -> main.show_agents_hub()
agents_hub.agent_selected -> main.select_agent_from_hub()
agents_hub.back_requested -> main.show_chat_page()
avatar_editor.avatar_changed -> settings_dialog.on_avatar_changed()
```

## 未来改进建议

1. **动画效果**: 添加页面切换动画
2. **主题系统**: 支持多种颜色主题
3. **Agent分类**: 按类型分组显示Agent
4. **搜索功能**: 在Agents Hub中添加搜索
5. **拖拽排序**: 支持Agent卡片拖拽排序
6. **导入导出**: 支持Agent配置的导入导出

## 测试建议

1. 测试不同分辨率下的界面显示
2. 验证头像裁剪功能的图片格式兼容性
3. 测试大量Agent时的性能表现
4. 验证页面切换的流畅性
5. 测试长对话的滚动性能
