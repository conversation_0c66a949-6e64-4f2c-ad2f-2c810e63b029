import{SvelteComponent as y,init as p,not_equal as x,create_component as q,claim_component as C,mount_component as D,transition_in as U,transition_out as V,destroy_component as z,assign as $,ensure_array_like as G,space as L,element as A,claim_space as O,claim_element as J,children as F,detach as S,attr as v,insert_hydration as I,get_spread_update as ee,get_spread_object as le,destroy_each as te,text as M,claim_text as Q,set_data as R,toggle_class as T,append_hydration as N,listen as j,run_all as ne}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{B as ie,S as se,y as ae}from"./2.B2AoQPnG.js";function H(t,e,l){const i=t.slice();return i[21]=e[l][0],i[22]=e[l][1],i[24]=l,i}function ce(t){let e;return{c(){e=M(t[9])},l(l){e=Q(l,t[9])},m(l,i){I(l,e,i)},p(l,i){i&512&&R(e,l[9])},d(l){l&&S(e)}}}function K(t){let e,l,i,a,c,b,g,o=t[21]+"",m,f,n,u;function d(){return t[18](t[22])}function w(...r){return t[19](t[24],t[22],...r)}function h(...r){return t[20](t[22],t[24],...r)}return{c(){e=A("label"),l=A("input"),b=L(),g=A("span"),m=M(o),f=L(),this.h()},l(r){e=J(r,"LABEL",{class:!0});var _=F(e);l=J(_,"INPUT",{type:!0,name:!0,title:!0,class:!0}),b=O(_),g=J(_,"SPAN",{class:!0});var k=F(g);m=Q(k,o),k.forEach(S),f=O(_),_.forEach(S),this.h()},h(){var r,_;l.disabled=t[13],l.checked=i=t[0].includes(t[22]),v(l,"type","checkbox"),v(l,"name",a=(r=t[22])==null?void 0:r.toString()),v(l,"title",c=(_=t[22])==null?void 0:_.toString()),v(l,"class","svelte-1e02hys"),v(g,"class","ml-2 svelte-1e02hys"),v(e,"class","svelte-1e02hys"),T(e,"disabled",t[13]),T(e,"selected",t[0].includes(t[22]))},m(r,_){I(r,e,_),N(e,l),N(e,b),N(e,g),N(g,m),N(e,f),n||(u=[j(l,"change",d),j(l,"input",w),j(l,"keydown",h)],n=!0)},p(r,_){var k,E;t=r,_&8192&&(l.disabled=t[13]),_&33&&i!==(i=t[0].includes(t[22]))&&(l.checked=i),_&32&&a!==(a=(k=t[22])==null?void 0:k.toString())&&v(l,"name",a),_&32&&c!==(c=(E=t[22])==null?void 0:E.toString())&&v(l,"title",c),_&32&&o!==(o=t[21]+"")&&R(m,o),_&8192&&T(e,"disabled",t[13]),_&33&&T(e,"selected",t[0].includes(t[22]))},d(r){r&&S(e),n=!1,ne(u)}}}function ue(t){let e,l,i,a,c,b;const g=[{autoscroll:t[1].autoscroll},{i18n:t[1].i18n},t[12]];let o={};for(let n=0;n<g.length;n+=1)o=$(o,g[n]);e=new se({props:o}),e.$on("clear_status",t[17]),i=new ae({props:{show_label:t[11],info:t[10],$$slots:{default:[ce]},$$scope:{ctx:t}}});let m=G(t[5]),f=[];for(let n=0;n<m.length;n+=1)f[n]=K(H(t,m,n));return{c(){q(e.$$.fragment),l=L(),q(i.$$.fragment),a=L(),c=A("div");for(let n=0;n<f.length;n+=1)f[n].c();this.h()},l(n){C(e.$$.fragment,n),l=O(n),C(i.$$.fragment,n),a=O(n),c=J(n,"DIV",{class:!0,"data-testid":!0});var u=F(c);for(let d=0;d<f.length;d+=1)f[d].l(u);u.forEach(S),this.h()},h(){v(c,"class","wrap svelte-1e02hys"),v(c,"data-testid","checkbox-group")},m(n,u){D(e,n,u),I(n,l,u),D(i,n,u),I(n,a,u),I(n,c,u);for(let d=0;d<f.length;d+=1)f[d]&&f[d].m(c,null);b=!0},p(n,u){const d=u&4098?ee(g,[u&2&&{autoscroll:n[1].autoscroll},u&2&&{i18n:n[1].i18n},u&4096&&le(n[12])]):{};e.$set(d);const w={};if(u&2048&&(w.show_label=n[11]),u&1024&&(w.info=n[10]),u&33554944&&(w.$$scope={dirty:u,ctx:n}),i.$set(w),u&24611){m=G(n[5]);let h;for(h=0;h<m.length;h+=1){const r=H(n,m,h);f[h]?f[h].p(r,u):(f[h]=K(r),f[h].c(),f[h].m(c,null))}for(;h<f.length;h+=1)f[h].d(1);f.length=m.length}},i(n){b||(U(e.$$.fragment,n),U(i.$$.fragment,n),b=!0)},o(n){V(e.$$.fragment,n),V(i.$$.fragment,n),b=!1},d(n){n&&(S(l),S(a),S(c)),z(e,n),z(i,n),te(f,n)}}}function fe(t){let e,l;return e=new ie({props:{visible:t[4],elem_id:t[2],elem_classes:t[3],type:"fieldset",container:t[6],scale:t[7],min_width:t[8],$$slots:{default:[ue]},$$scope:{ctx:t}}}),{c(){q(e.$$.fragment)},l(i){C(e.$$.fragment,i)},m(i,a){D(e,i,a),l=!0},p(i,[a]){const c={};a&16&&(c.visible=i[4]),a&4&&(c.elem_id=i[2]),a&8&&(c.elem_classes=i[3]),a&64&&(c.container=i[6]),a&128&&(c.scale=i[7]),a&256&&(c.min_width=i[8]),a&33570339&&(c.$$scope={dirty:a,ctx:i}),e.$set(c)},i(i){l||(U(e.$$.fragment,i),l=!0)},o(i){V(e.$$.fragment,i),l=!1},d(i){z(e,i)}}}function _e(t,e,l){let i,{gradio:a}=e,{elem_id:c=""}=e,{elem_classes:b=[]}=e,{visible:g=!0}=e,{value:o=[]}=e,{choices:m}=e,{container:f=!0}=e,{scale:n=null}=e,{min_width:u=void 0}=e,{label:d=a.i18n("checkbox.checkbox_group")}=e,{info:w=void 0}=e,{show_label:h=!0}=e,{loading_status:r}=e,{interactive:_=!0}=e,{old_value:k=o.slice()}=e;function E(s){o.includes(s)?l(0,o=o.filter(B=>B!==s)):l(0,o=[...o,s]),a.dispatch("input")}const W=()=>a.dispatch("clear_status",r),X=s=>E(s),Y=(s,B,P)=>a.dispatch("select",{index:s,value:B,selected:P.currentTarget.checked}),Z=(s,B,P)=>{P.key==="Enter"&&(E(s),a.dispatch("select",{index:B,value:s,selected:!o.includes(s)}))};return t.$$set=s=>{"gradio"in s&&l(1,a=s.gradio),"elem_id"in s&&l(2,c=s.elem_id),"elem_classes"in s&&l(3,b=s.elem_classes),"visible"in s&&l(4,g=s.visible),"value"in s&&l(0,o=s.value),"choices"in s&&l(5,m=s.choices),"container"in s&&l(6,f=s.container),"scale"in s&&l(7,n=s.scale),"min_width"in s&&l(8,u=s.min_width),"label"in s&&l(9,d=s.label),"info"in s&&l(10,w=s.info),"show_label"in s&&l(11,h=s.show_label),"loading_status"in s&&l(12,r=s.loading_status),"interactive"in s&&l(16,_=s.interactive),"old_value"in s&&l(15,k=s.old_value)},t.$$.update=()=>{t.$$.dirty&65536&&l(13,i=!_),t.$$.dirty&32771&&JSON.stringify(k)!==JSON.stringify(o)&&(l(15,k=o),a.dispatch("change"))},[o,a,c,b,g,m,f,n,u,d,w,h,r,i,E,k,_,W,X,Y,Z]}class de extends y{constructor(e){super(),p(this,e,_e,fe,x,{gradio:1,elem_id:2,elem_classes:3,visible:4,value:0,choices:5,container:6,scale:7,min_width:8,label:9,info:10,show_label:11,loading_status:12,interactive:16,old_value:15})}}export{de as default};
//# sourceMappingURL=Index.DbcE8rmG.js.map
