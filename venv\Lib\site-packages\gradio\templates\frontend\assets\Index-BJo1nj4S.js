const __vite__fileDeps=["./Blocks-DQBXfWaA.js","./index-B7J2Z2jS.js","./index-CJsBH6a-.css","./StreamingBar.svelte_svelte_type_style_lang-DyfAPhwM.js","./StreamingBar-pGVW9eBG.css","./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js","./prism-python-MMh3z1bK.js","./MarkdownCode-GC7vPJ5f.css","./Toast-BMPuxKCO.js","./index-CEGzm7H5.js","./IconButtonWrapper-BqcF4N5S.css","./utils-BsGrhMNe.js","./Blocks-B6OQoqZ7.css","./Login-DEchR4-d.js","./Index-DE1Sah7F.js","./Index-12OnbRhk.css","./Textbox-kv26zexc.js","./BlockTitle-Ct-h8ev5.js","./Info-IGMCDo7y.js","./MarkdownCode-CkSMBRHJ.js","./Check-CEkiXcyC.js","./Copy-CxQ9EyK2.js","./Send-DyoOovnk.js","./Square-oAGqOwsh.js","./Textbox-jWD3sCxr.css","./Block-CJdXVpa7.js","./Button-B3gqVEq2.js","./Image-CnqB5dbD.js","./file-url-DoxvUUVV.js","./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js","./DownloadLink-CqD3Uu0l.css","./Image-B8dFOee4.css","./Button-DTh9AgeE.css","./ImagePreview-C_qhEOxI.css","./Index-C7inCcrM.js","./index-B1FJGuzG.js","./IconButton-C_HS7fTi.js","./Clear-By3xiIwg.js","./Index-D-5U5m91.css","./Login-BCwzjozv.css","./Example-ClKJOMGh.css"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{$ as We,s as Ze,w as Ye,m as Je,p as ye,_ as ve}from"./index-B7J2Z2jS.js";import{E as Ke}from"./Embed-HnJWX_lT.js";import{S as Qe}from"./index-B1FJGuzG.js";/* empty css                                                        */import"./StreamingBar.svelte_svelte_type_style_lang-DyfAPhwM.js";import{s as Xe}from"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import"./svelte/svelte.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import"./prism-python-MMh3z1bK.js";import"./IconButton-C_HS7fTi.js";import"./Clear-By3xiIwg.js";var xe=()=>{const t=document.createElement("link");t.href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700;1,900&display=swap",t.rel="stylesheet";const e=document.createElement("link");e.href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@400;600;700&display=swap",e.rel="stylesheet",document.head.appendChild(t),document.head.appendChild(e)},$e=()=>{const t=document.createElement("div");return t.style.backgroundImage="linear-gradient(to top, #f9fafb, white)",t.style.border="1px solid #e5e7eb",t.style.borderRadius="0.75rem",t.style.boxShadow="0 0 10px rgba(0, 0, 0, 0.1)",t.style.color="#374151",t.style.display="flex",t.style.flexDirection="row",t.style.alignItems="center",t.style.height="40px",t.style.justifyContent="space-between",t.style.overflow="hidden",t.style.position="fixed",t.style.right=".75rem",t.style.top=".75rem",t.style.width="auto",t.style.zIndex="20",t.style.paddingLeft="1rem",t.setAttribute("id","huggingface-space-header"),window.matchMedia("(max-width: 768px)").addEventListener("change",e=>{e.matches?t.style.display="none":t.style.display="flex"}),t},et=()=>{const t=document.createElementNS("http://www.w3.org/2000/svg","svg");t.setAttribute("xmlns","http://www.w3.org/2000/svg"),t.setAttribute("xmlns:link","http://www.w3.org/1999/xlink"),t.setAttribute("aria-hidden","true"),t.setAttribute("focusable","false"),t.setAttribute("role","img"),t.setAttribute("width","1em"),t.setAttribute("height","1em"),t.setAttribute("preserveAspectRatio","xMidYMid meet"),t.setAttribute("viewBox","0 0 12 12"),t.setAttribute("fill","currentColor");const e=document.createElementNS("http://www.w3.org/2000/svg","path");return e.setAttribute("d","M0.375001 10.3828L0.375 1.61719C0.375 1.104 0.816001 0.687501 1.35938 0.687501L10.6406 0.6875C10.9017 0.6875 11.1521 0.785449 11.3367 0.959797C11.5213 1.13415 11.625 1.37062 11.625 1.61719V10.3828C11.625 10.6294 11.5213 10.8659 11.3367 11.0402C11.1521 11.2145 10.9017 11.3125 10.6406 11.3125H1.35938C0.816001 11.3125 0.375001 10.896 0.375001 10.3828ZM1.35938 10.5156H10.6406C10.7183 10.5156 10.7813 10.4561 10.7813 10.3828V4.40625H1.21875V10.3828C1.21875 10.418 1.23356 10.4518 1.25994 10.4767C1.28631 10.5017 1.32208 10.5156 1.35938 10.5156ZM4.61052 6.38251L5.9999 7.69472L7.38927 6.38251C7.44083 6.33007 7.50645 6.29173 7.57913 6.27153C7.6518 6.25134 7.72898 6.25003 7.8024 6.26776C7.87583 6.28549 7.9428 6.3216 7.99628 6.37227C8.04983 6.42295 8.08785 6.48631 8.10645 6.5557C8.12528 6.62497 8.12393 6.69773 8.10263 6.76635C8.0814 6.83497 8.0409 6.8969 7.98555 6.94564L6.29802 8.53936C6.21892 8.61399 6.11169 8.65592 5.9999 8.65592C5.8881 8.65592 5.78087 8.61399 5.70177 8.53936L4.01427 6.94564C3.95874 6.89694 3.91814 6.835 3.89676 6.76633C3.87538 6.69766 3.874 6.62483 3.89277 6.55549C3.91154 6.48615 3.94977 6.42287 4.00343 6.37233C4.05708 6.32179 4.12418 6.28585 4.19765 6.2683C4.27098 6.25054 4.34803 6.25178 4.42068 6.27188C4.49334 6.29198 4.55891 6.3302 4.61052 6.38251Z"),t.appendChild(e),t},tt=(t,e)=>{const r=document.createElement("div");return r.setAttribute("id","space-header__collapse"),r.style.display="flex",r.style.flexDirection="row",r.style.alignItems="center",r.style.justifyContent="center",r.style.fontSize="16px",r.style.paddingLeft="10px",r.style.paddingRight="10px",r.style.height="40px",r.style.cursor="pointer",r.style.color="#40546e",r.style.transitionDuration="0.1s",r.style.transitionProperty="all",r.style.transitionTimingFunction="ease-in-out",r.appendChild(et()),r.addEventListener("click",n=>{n.preventDefault(),n.stopPropagation(),e()}),r.addEventListener("mouseenter",()=>{r.style.color="#213551"}),r.addEventListener("mouseleave",()=>{r.style.color="#40546e"}),r},rt=t=>{const e=document.createElement("p");return e.style.margin="0",e.style.padding="0",e.style.color="#9ca3af",e.style.fontSize="14px",e.style.fontFamily="Source Sans Pro, sans-serif",e.style.padding="0px 6px",e.style.borderLeft="1px solid #e5e7eb",e.style.marginLeft="4px",e.textContent=(t??0).toString(),e},nt=()=>{const t=document.createElementNS("http://www.w3.org/2000/svg","svg");t.setAttribute("xmlns","http://www.w3.org/2000/svg"),t.setAttribute("xmlns:link","http://www.w3.org/1999/xlink"),t.setAttribute("aria-hidden","true"),t.setAttribute("focusable","false"),t.setAttribute("role","img"),t.setAttribute("width","1em"),t.setAttribute("height","1em"),t.setAttribute("preserveAspectRatio","xMidYMid meet"),t.setAttribute("viewBox","0 0 32 32"),t.setAttribute("fill","#6b7280");const e=document.createElementNS("http://www.w3.org/2000/svg","path");return e.setAttribute("d","M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z"),t.appendChild(e),t},st=t=>{const e=document.createElement("a");return e.setAttribute("href",`https://huggingface.co/spaces/${t.id}`),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e.style.border="1px solid #e5e7eb",e.style.borderRadius="6px",e.style.display="flex",e.style.flexDirection="row",e.style.alignItems="center",e.style.margin="0 0 0 12px",e.style.fontSize="14px",e.style.paddingLeft="4px",e.style.textDecoration="none",e.appendChild(nt()),e.appendChild(rt(t.likes)),e},ot=(t,e="user")=>{const r=e==="user"?"users":"organizations",n=document.createElement("img");return n.src=`https://huggingface.co/api/${r}/${t}/avatar`,n.style.width="0.875rem",n.style.height="0.875rem",n.style.borderRadius="50%",n.style.flex="none",n.style.marginRight="0.375rem",n},it=t=>{const[e,r]=t.split("/"),n=document.createElement("a");return n.setAttribute("href",`https://huggingface.co/spaces/${t}`),n.setAttribute("rel","noopener noreferrer"),n.setAttribute("target","_blank"),n.style.color="#1f2937",n.style.textDecoration="none",n.style.fontWeight="600",n.style.fontSize="15px",n.style.lineHeight="24px",n.style.flex="none",n.style.fontFamily="IBM Plex Mono, sans-serif",n.addEventListener("mouseover",()=>{n.style.color="#2563eb"}),n.addEventListener("mouseout",()=>{n.style.color="#1f2937"}),n.textContent=r,n},at=()=>{const t=document.createElement("div");return t.style.marginLeft=".125rem",t.style.marginRight=".125rem",t.style.color="#d1d5db",t.textContent="/",t},lt=t=>{const e=document.createElement("a");return e.setAttribute("href",`https://huggingface.co/${t}`),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e.style.color="rgb(107, 114, 128)",e.style.textDecoration="none",e.style.fontWeight="400",e.style.fontSize="16px",e.style.lineHeight="24px",e.style.flex="none",e.style.fontFamily="Source Sans Pro, sans-serif",e.addEventListener("mouseover",()=>{e.style.color="#2563eb"}),e.addEventListener("mouseout",()=>{e.style.color="rgb(107, 114, 128)"}),e.textContent=t,e},ct=t=>{const e=document.createElement("div");return e.style.display="flex",e.style.flexDirection="row",e.style.alignItems="center",e.style.justifyContent="center",e.style.borderRight="1px solid #e5e7eb",e.style.paddingRight="12px",e.style.height="40px",t.type!=="unknown"&&e.appendChild(ot(t.author,t.type)),e.appendChild(lt(t.author)),e.appendChild(at()),e.appendChild(it(t.id)),e.appendChild(st(t)),e},ut=t=>{const e=$e(),r=()=>e.style.display="none";return e.appendChild(ct(t)),e.appendChild(tt(t,r)),e},ke=async(t,e="user")=>{const r=e==="user"?"users":"organizations";try{return(await fetch(`https://huggingface.co/api/${r}/${t}/avatar`)).ok}catch{return!1}},dt=async t=>{try{return await(await fetch(`https://huggingface.co/api/spaces/${t}`)).json()}catch{return null}},_t=(t,e)=>{if(document.body===null)return console.error("document.body is null");document.body.appendChild(t)};async function ft(t,e){var r,n;if(window===void 0)return console.error("Please run this script in a browser environment");if(Object.values((n=(r=window.location)==null?void 0:r.ancestorOrigins)!=null?n:{0:window.document.referrer}).some(_=>{var c;return((c=new URL(_))==null?void 0:c.origin)==="https://huggingface.co"}))return;xe();let o;if(typeof t=="string"){if(o=await dt(t),o===null)return console.error("Space not found")}else o=t;const[a,d]=await Promise.all([ke(o.author,"user"),ke(o.author,"org")]);o.type=a?"user":d?"org":"unknown";const u=ut(o);return _t(u),{element:u}}var pt=(t,e)=>ft(t);const{SvelteComponent:mt,add_flush_callback:H,append:C,assign:ht,attr:v,bind:V,binding_callbacks:G,check_outros:te,component_subscribe:Ce,create_component:W,destroy_component:Z,detach:A,element:E,empty:Ie,flush:g,get_spread_object:gt,get_spread_update:wt,group_outros:re,init:bt,insert:L,mount_component:Y,noop:yt,safe_not_equal:vt,set_data:Pe,space:Me,text:O,transition_in:y,transition_out:k}=window.__gradio__svelte__internal,{onMount:Ee,createEventDispatcher:kt,onDestroy:Ct}=window.__gradio__svelte__internal;function Ae(t){let e,r,n,s,o,a=(t[21]==="pending"||t[21]==="error")&&!(t[13]&&t[13]?.auth_required)&&Le(t);const d=[Pt,It],u=[];function _(c,l){return c[13]?.auth_required&&c[26]?0:c[13]&&c[25]&&c[24]?1:-1}return~(r=_(t))&&(n=u[r]=d[r](t)),{c(){a&&a.c(),e=Me(),n&&n.c(),s=Ie()},m(c,l){a&&a.m(c,l),L(c,e,l),~r&&u[r].m(c,l),L(c,s,l),o=!0},p(c,l){(c[21]==="pending"||c[21]==="error")&&!(c[13]&&c[13]?.auth_required)?a?(a.p(c,l),l[0]&2105344&&y(a,1)):(a=Le(c),a.c(),y(a,1),a.m(e.parentNode,e)):a&&(re(),k(a,1,1,()=>{a=null}),te());let f=r;r=_(c),r===f?~r&&u[r].p(c,l):(n&&(re(),k(u[f],1,1,()=>{u[f]=null}),te()),~r?(n=u[r],n?n.p(c,l):(n=u[r]=d[r](c),n.c()),y(n,1),n.m(s.parentNode,s)):n=null)},i(c){o||(y(a),y(n),o=!0)},o(c){k(a),k(n),o=!1},d(c){c&&(A(e),A(s)),a&&a.d(c),~r&&u[r].d(c)}}}function Le(t){let e,r;return e=new Qe({props:{absolute:!t[4],status:t[21],timer:!1,queue_position:null,queue_size:null,translucent:!0,loading_text:t[22],i18n:t[17],autoscroll:t[0],$$slots:{error:[St],"additional-loading-text":[At]},$$scope:{ctx:t}}}),{c(){W(e.$$.fragment)},m(n,s){Y(e,n,s),r=!0},p(n,s){const o={};s[0]&16&&(o.absolute=!n[4]),s[0]&2097152&&(o.status=n[21]),s[0]&4194304&&(o.loading_text=n[22]),s[0]&131072&&(o.i18n=n[17]),s[0]&1&&(o.autoscroll=n[0]),s[0]&134365952|s[1]&134217728&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){r||(y(e.$$.fragment,n),r=!0)},o(n){k(e.$$.fragment,n),r=!1},d(n){Z(e,n)}}}function Et(t){let e;return{c(){e=E("p"),e.innerHTML='If your custom component never loads, consult the troubleshooting <a style="color: blue;" href="https://www.gradio.app/guides/frequently-asked-questions#the-development-server-didnt-work-for-me" class="svelte-y6l4b">guide</a>.'},m(r,n){L(r,e,n)},d(r){r&&A(e)}}}function At(t){let e,r=t[29]==="dev"&&Et();return{c(){e=E("div"),r&&r.c(),v(e,"class","load-text"),v(e,"slot","additional-loading-text")},m(n,s){L(n,e,s),r&&r.m(e,null)},p:yt,d(n){n&&A(e),r&&r.d()}}}function Lt(t){let e,r=t[17]("errors.contact_page_author")+"",n;return{c(){e=E("p"),n=O(r),v(e,"class","svelte-y6l4b")},m(s,o){L(s,e,o),C(e,n)},p(s,o){o[0]&131072&&r!==(r=s[17]("errors.contact_page_author")+"")&&Pe(n,r)},d(s){s&&A(e)}}}function Rt(t){let e,r,n,s,o,a;return{c(){e=E("p"),r=O("Please "),n=E("a"),s=O("contact the author of the space"),a=O(" to let them know."),v(n,"href",o="https://huggingface.co/spaces/"+t[8]+"/discussions/new?title="+t[27].title(t[14]?.detail)+"&description="+t[27].description(t[14]?.detail,location.origin)),v(n,"class","svelte-y6l4b"),v(e,"class","svelte-y6l4b")},m(d,u){L(d,e,u),C(e,r),C(e,n),C(n,s),C(e,a)},p(d,u){u[0]&134234368&&o!==(o="https://huggingface.co/spaces/"+d[8]+"/discussions/new?title="+d[27].title(d[14]?.detail)+"&description="+d[27].description(d[14]?.detail,location.origin))&&v(n,"href",o)},d(d){d&&A(e)}}}function St(t){let e,r,n,s=(t[14]?.message||"")+"",o,a;function d(c,l){if((c[14].status==="space_error"||c[14].status==="paused")&&c[14].discussions_enabled&&c[27])return Rt;if(c[9])return Lt}let u=d(t),_=u&&u(t);return{c(){e=E("div"),r=E("p"),n=E("strong"),o=O(s),a=Me(),_&&_.c(),v(r,"class","svelte-y6l4b"),v(e,"class","error svelte-y6l4b"),v(e,"slot","error")},m(c,l){L(c,e,l),C(e,r),C(r,n),C(n,o),C(e,a),_&&_.m(e,null)},p(c,l){l[0]&16384&&s!==(s=(c[14]?.message||"")+"")&&Pe(o,s),u===(u=d(c))&&_?_.p(c,l):(_&&_.d(1),_=u&&u(c),_&&(_.c(),_.m(e,null)))},d(c){c&&A(e),_&&_.d()}}}function It(t){let e,r,n,s,o;const a=[{app:t[15]},t[13],{fill_height:!t[4]&&t[13].fill_height},{theme_mode:t[23]},{control_page_title:t[5]},{target:t[10]},{autoscroll:t[0]},{show_footer:!t[4]},{app_mode:t[3]},{version:t[1]},{api_prefix:t[13].api_prefix||""},{max_file_size:t[13].max_file_size},{initial_layout:void 0},{search_params:new URLSearchParams(window.location.search)}];function d(l){t[38](l)}function u(l){t[39](l)}function _(l){t[40](l)}let c={};for(let l=0;l<a.length;l+=1)c=ht(c,a[l]);return t[11]!==void 0&&(c.ready=t[11]),t[12]!==void 0&&(c.render_complete=t[12]),t[16]!==void 0&&(c.add_new_message=t[16]),e=new t[25]({props:c}),G.push(()=>V(e,"ready",d)),G.push(()=>V(e,"render_complete",u)),G.push(()=>V(e,"add_new_message",_)),{c(){W(e.$$.fragment)},m(l,f){Y(e,l,f),o=!0},p(l,f){const R=f[0]&8430651?wt(a,[f[0]&32768&&{app:l[15]},f[0]&8192&&gt(l[13]),f[0]&8208&&{fill_height:!l[4]&&l[13].fill_height},f[0]&8388608&&{theme_mode:l[23]},f[0]&32&&{control_page_title:l[5]},f[0]&1024&&{target:l[10]},f[0]&1&&{autoscroll:l[0]},f[0]&16&&{show_footer:!l[4]},f[0]&8&&{app_mode:l[3]},f[0]&2&&{version:l[1]},f[0]&8192&&{api_prefix:l[13].api_prefix||""},f[0]&8192&&{max_file_size:l[13].max_file_size},f&0&&{initial_layout:void 0},f&0&&{search_params:new URLSearchParams(window.location.search)}]):{};!r&&f[0]&2048&&(r=!0,R.ready=l[11],H(()=>r=!1)),!n&&f[0]&4096&&(n=!0,R.render_complete=l[12],H(()=>n=!1)),!s&&f[0]&65536&&(s=!0,R.add_new_message=l[16],H(()=>s=!1)),e.$set(R)},i(l){o||(y(e.$$.fragment,l),o=!0)},o(l){k(e.$$.fragment,l),o=!1},d(l){Z(e,l)}}}function Pt(t){let e,r;return e=new t[26]({props:{auth_message:t[13].auth_message,root:t[13].root,space_id:t[8],app_mode:t[3]}}),{c(){W(e.$$.fragment)},m(n,s){Y(e,n,s),r=!0},p(n,s){const o={};s[0]&8192&&(o.auth_message=n[13].auth_message),s[0]&8192&&(o.root=n[13].root),s[0]&256&&(o.space_id=n[8]),s[0]&8&&(o.app_mode=n[3]),e.$set(o)},i(n){r||(y(e.$$.fragment,n),r=!0)},o(n){k(e.$$.fragment,n),r=!1},d(n){Z(e,n)}}}function Mt(t){let e,r,n=t[9]&&Ae(t);return{c(){n&&n.c(),e=Ie()},m(s,o){n&&n.m(s,o),L(s,e,o),r=!0},p(s,o){s[9]?n?(n.p(s,o),o[0]&512&&y(n,1)):(n=Ae(s),n.c(),y(n,1),n.m(e.parentNode,e)):n&&(re(),k(n,1,1,()=>{n=null}),te())},i(s){r||(y(n),r=!0)},o(s){k(n),r=!1},d(s){s&&A(e),n&&n.d(s)}}}function Nt(t){let e,r,n;function s(a){t[41](a)}let o={display:t[6]&&t[4],is_embed:t[4],info:!!t[8]&&t[7],version:t[1],initial_height:t[2],space:t[8],loaded:t[21]==="complete",fill_width:t[13]?.fill_width||!1,pages:t[18],current_page:t[19],root:t[20],is_lite:t[28],$$slots:{default:[Mt]},$$scope:{ctx:t}};return t[10]!==void 0&&(o.wrapper=t[10]),e=new Ke({props:o}),G.push(()=>V(e,"wrapper",s)),{c(){W(e.$$.fragment)},m(a,d){Y(e,a,d),n=!0},p(a,d){const u={};d[0]&80&&(u.display=a[6]&&a[4]),d[0]&16&&(u.is_embed=a[4]),d[0]&384&&(u.info=!!a[8]&&a[7]),d[0]&2&&(u.version=a[1]),d[0]&4&&(u.initial_height=a[2]),d[0]&256&&(u.space=a[8]),d[0]&2097152&&(u.loaded=a[21]==="complete"),d[0]&8192&&(u.fill_width=a[13]?.fill_width||!1),d[0]&262144&&(u.pages=a[18]),d[0]&524288&&(u.current_page=a[19]),d[0]&1048576&&(u.root=a[20]),d[0]&266600251|d[1]&134217728&&(u.$$scope={dirty:d,ctx:a}),!r&&d[0]&1024&&(r=!0,u.wrapper=a[10],H(()=>r=!1)),e.$set(u)},i(a){n||(y(e.$$.fragment,a),n=!0)},o(a){k(e.$$.fragment,a),n=!1},d(a){Z(e,a)}}}let Dt=-1;function zt(){const t=Ye({}),e=new Map,r=new IntersectionObserver(s=>{s.forEach(o=>{if(o.isIntersecting){let a=e.get(o.target);a!==void 0&&t.update(d=>({...d,[a]:!0}))}})});function n(s,o){e.set(o,s),r.observe(o)}return{register:n,subscribe:t.subscribe}}const Re=zt();async function Se(t){if(t){const e=new DOMParser,r=Array.from(e.parseFromString(t,"text/html").head.children);if(r)for(let n of r){let s=document.createElement(n.tagName);if(Array.from(n.attributes).forEach(o=>{s.setAttribute(o.name,o.value)}),s.textContent=n.textContent,s.tagName=="META"){const o=s.getAttribute("property"),a=s.getAttribute("name");if(o||a){const u=Array.from(document.head.getElementsByTagName("meta")??[]).find(_=>o&&_.getAttribute("property")===o||a&&_.getAttribute("name")===a?!_.isEqualNode(s):!1);if(u){document.head.replaceChild(s,u);continue}}}document.head.appendChild(s)}}}function Ot(t,e,r){let n,s;Ce(t,We,i=>r(17,n=i)),Ce(t,Re,i=>r(37,s=i));let o=!1;Ze().then(()=>{r(9,o=!0)});const a=kt();let{autoscroll:d}=e,{version:u}=e,{initial_height:_}=e,{app_mode:c}=e,{is_embed:l}=e,{theme_mode:f="system"}=e,{control_page_title:R}=e,{container:ne}=e,{info:se}=e,{eager:J}=e,K,oe=[],ie,ae,{mount_css:U=Je}=e,{Client:q}=e,{worker_proxy:S=void 0}=e;S&&(Xe(S),S.addEventListener("progress-update",i=>{r(22,x=i.detail+"...")}));let Ne=S!==void 0,{space:Q}=e,{src:X}=e,le=Dt++,ce="pending",I,N=!1,T=!1,p,x="Loading...",ue,B,D=null;async function de(i){i&&(D||(D=document.createElement("style"),document.head.appendChild(D)),D.textContent=ye(i,u,D)),await U(p.root+"/theme.css?v="+p.theme_hash,document.head),p.stylesheets&&await Promise.all(p.stylesheets.map(h=>h.startsWith("http:")||h.startsWith("https:")?U(h,document.head):fetch(p.root+"/"+h).then(b=>b.text()).then(b=>{ye(b,u)})))}function De(i){const h=window.__gradio_mode__==="website";let m;if(h)m="light";else{const F=new URL(window.location.toString()).searchParams.get("__theme");m=f||F||"system"}return m==="dark"||m==="light"?_e(i,m):m=ze(i),m}function ze(i){const h=m();window?.matchMedia("(prefers-color-scheme: dark)")?.addEventListener("change",m);function m(){let b=window?.matchMedia?.("(prefers-color-scheme: dark)").matches?"dark":"light";return _e(i,b),b}return h}function _e(i,h){const m=l?i.parentElement:document.body,b=l?i:i.parentElement;b.style.background="var(--body-background-fill)",h==="dark"?m.classList.add("dark"):m.classList.remove("dark")}let P={message:"",load_status:"pending",status:"sleeping",detail:"SLEEPING"},w,$=!1;function fe(i){r(14,P=i)}const pe=window.__GRADIO_DEV__;let j=!1,M;Ee(async()=>{r(23,ue=De(I));const i=window.__GRADIO__SERVER_PORT__;B=pe==="dev"?`http://localhost:${typeof i=="number"?i:7860}`:Q||X||new URL(location.pathname,location.origin).href.replace(/\/$/,"");const h=new URLSearchParams(window.location.search).get("deep_link"),m={};if(h&&(m.deep_link=h),r(15,w=await q.connect(B,{status_callback:fe,with_null_state:!0,events:["data","log","status","render"],query_params:m})),window.addEventListener("beforeunload",()=>{w.close()}),!w.config)throw new Error("Could not resolve app config");r(13,p=w.get_url_config()),window.__gradio_space__=p.space_id,window.__gradio_session_hash__=w.session_hash,r(14,P={message:"",load_status:"complete",status:"running",detail:"RUNNING"}),await de(p.css),await Se(p.head),r(24,$=!0),window.__is_colab__=p.is_colab;const b="supports-zerogpu-headers";window.addEventListener("message",ee=>{ee.data===b&&(window.supports_zerogpu_headers=!0)});const F=window.location.hostname,Ve=F.includes(".dev.")?`https://moon-${F.split(".")[1]}.dev.spaces.huggingface.tech`:"https://huggingface.co";window.parent.postMessage(b,Ve),a("loaded"),r(18,oe=p.pages),r(19,ie=p.current_page),r(20,ae=p.root),p.deep_link_state==="invalid"&&r(36,j=!0),p.dev_mode&&setTimeout(()=>{const{host:ee}=new URL(B);let Ge=new URL(`${window.location.protocol}//${ee}${w.api_prefix}/dev/reload`);K=new EventSource(Ge),K.addEventListener("error",async we=>{let be=we.data;be&&(M("Error","Error reloading app","error"),console.error(JSON.parse(be)))}),K.addEventListener("reload",async we=>{if(w.close(),r(15,w=await q.connect(B,{status_callback:fe,with_null_state:!0,events:["data","log","status","render"],session_hash:w.session_hash})),!w.config)throw new Error("Could not resolve app config");r(13,p=w.get_url_config()),window.__gradio_space__=p.space_id,await de(p.css),await Se(p.head),r(24,$=!0),window.__is_colab__=p.is_colab,a("loaded")})},200)});let me,he;async function Oe(){r(25,me=(await ve(()=>import("./Blocks-DQBXfWaA.js").then(i=>i.B),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12]),import.meta.url)).default)}async function Ue(){r(26,he=(await ve(()=>import("./Login-DEchR4-d.js"),__vite__mapDeps([13,14,15,16,17,18,19,1,2,5,6,7,10,20,21,22,23,9,24,25,3,4,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40]),import.meta.url)).default)}function qe(){p.auth_required?Ue():Oe()}let ge;Ee(async()=>{Re.register(le,I)});let z;async function Te(i,h){if(i&&!h&&window.self===window.top){z&&(z.remove(),z=void 0);const m=await pt(i);m&&(z=m.element)}}Ct(()=>{z?.remove()});function Be(i){N=i,r(11,N)}function je(i){T=i,r(12,T)}function Fe(i){M=i,r(16,M)}function He(i){I=i,r(10,I)}return t.$$set=i=>{"autoscroll"in i&&r(0,d=i.autoscroll),"version"in i&&r(1,u=i.version),"initial_height"in i&&r(2,_=i.initial_height),"app_mode"in i&&r(3,c=i.app_mode),"is_embed"in i&&r(4,l=i.is_embed),"theme_mode"in i&&r(30,f=i.theme_mode),"control_page_title"in i&&r(5,R=i.control_page_title),"container"in i&&r(6,ne=i.container),"info"in i&&r(7,se=i.info),"eager"in i&&r(31,J=i.eager),"mount_css"in i&&r(32,U=i.mount_css),"Client"in i&&r(33,q=i.Client),"worker_proxy"in i&&r(34,S=i.worker_proxy),"space"in i&&r(8,Q=i.space),"src"in i&&r(35,X=i.src)},t.$$.update=()=>{t.$$.dirty[0]&8192&&p?.app_id&&p.app_id,t.$$.dirty[0]&65536|t.$$.dirty[1]&32&&M&&j&&(M("Error","Deep link was not valid","error"),r(36,j=!1)),t.$$.dirty[0]&18432&&r(21,ce=!N&&P.load_status!=="error"?"pending":!N&&P.load_status==="error"?"error":P.load_status),t.$$.dirty[0]&8192|t.$$.dirty[1]&65&&p&&(J||s[le])&&qe(),t.$$.dirty[0]&131584&&o&&(r(22,x=n("common.loading")+"..."),r(27,ge={readable_error:{NO_APP_FILE:n("errors.no_app_file"),CONFIG_ERROR:n("errors.config_error"),BUILD_ERROR:n("errors.build_error"),RUNTIME_ERROR:n("errors.runtime_error"),PAUSED:n("errors.space_paused")},title(i){return encodeURIComponent(n("errors.space_not_working"))},description(i,h){return encodeURIComponent(`Hello,

Firstly, thanks for creating this space!

I noticed that the space isn't working correctly because there is ${this.readable_error[i]||"an error"}.

It would be great if you could take a look at this because this space is being embedded on ${h}.

Thanks!`)}})),t.$$.dirty[0]&5120&&T&&I.dispatchEvent(new CustomEvent("render",{bubbles:!0,cancelable:!1,composed:!0})),t.$$.dirty[0]&32784&&w?.config&&Te(w?.config?.space_id,l)},[d,u,_,c,l,R,ne,se,Q,o,I,N,T,p,P,w,M,n,oe,ie,ae,ce,x,ue,$,me,he,ge,Ne,pe,f,J,U,q,S,X,j,s,Be,je,Fe,He]}class Yt extends mt{constructor(e){super(),bt(this,e,Ot,Nt,vt,{autoscroll:0,version:1,initial_height:2,app_mode:3,is_embed:4,theme_mode:30,control_page_title:5,container:6,info:7,eager:31,mount_css:32,Client:33,worker_proxy:34,space:8,src:35},null,[-1,-1])}get autoscroll(){return this.$$.ctx[0]}set autoscroll(e){this.$$set({autoscroll:e}),g()}get version(){return this.$$.ctx[1]}set version(e){this.$$set({version:e}),g()}get initial_height(){return this.$$.ctx[2]}set initial_height(e){this.$$set({initial_height:e}),g()}get app_mode(){return this.$$.ctx[3]}set app_mode(e){this.$$set({app_mode:e}),g()}get is_embed(){return this.$$.ctx[4]}set is_embed(e){this.$$set({is_embed:e}),g()}get theme_mode(){return this.$$.ctx[30]}set theme_mode(e){this.$$set({theme_mode:e}),g()}get control_page_title(){return this.$$.ctx[5]}set control_page_title(e){this.$$set({control_page_title:e}),g()}get container(){return this.$$.ctx[6]}set container(e){this.$$set({container:e}),g()}get info(){return this.$$.ctx[7]}set info(e){this.$$set({info:e}),g()}get eager(){return this.$$.ctx[31]}set eager(e){this.$$set({eager:e}),g()}get mount_css(){return this.$$.ctx[32]}set mount_css(e){this.$$set({mount_css:e}),g()}get Client(){return this.$$.ctx[33]}set Client(e){this.$$set({Client:e}),g()}get worker_proxy(){return this.$$.ctx[34]}set worker_proxy(e){this.$$set({worker_proxy:e}),g()}get space(){return this.$$.ctx[8]}set space(e){this.$$set({space:e}),g()}get src(){return this.$$.ctx[35]}set src(e){this.$$set({src:e}),g()}}export{Yt as default};
//# sourceMappingURL=Index-BJo1nj4S.js.map
