{"version": 3, "file": "client.Cd1aarwx.js", "sources": ["../../../../../../../../node_modules/.pnpm/@sveltejs+kit@2.5.20_@sveltejs+vite-plugin-svelte@3.1.0_svelte@4.2.15_vite@5.2.11_@types+node_5qlfuvulcykb7n4xkfzb6ec34e/node_modules/@sveltejs/kit/src/utils/url.js", "../../../../../../../../node_modules/.pnpm/@sveltejs+kit@2.5.20_@sveltejs+vite-plugin-svelte@3.1.0_svelte@4.2.15_vite@5.2.11_@types+node_5qlfuvulcykb7n4xkfzb6ec34e/node_modules/@sveltejs/kit/src/runtime/hash.js", "../../../../../../../../node_modules/.pnpm/@sveltejs+kit@2.5.20_@sveltejs+vite-plugin-svelte@3.1.0_svelte@4.2.15_vite@5.2.11_@types+node_5qlfuvulcykb7n4xkfzb6ec34e/node_modules/@sveltejs/kit/src/runtime/utils.js", "../../../../../../../../node_modules/.pnpm/@sveltejs+kit@2.5.20_@sveltejs+vite-plugin-svelte@3.1.0_svelte@4.2.15_vite@5.2.11_@types+node_5qlfuvulcykb7n4xkfzb6ec34e/node_modules/@sveltejs/kit/src/runtime/client/fetcher.js", "../../../../../../../../node_modules/.pnpm/@sveltejs+kit@2.5.20_@sveltejs+vite-plugin-svelte@3.1.0_svelte@4.2.15_vite@5.2.11_@types+node_5qlfuvulcykb7n4xkfzb6ec34e/node_modules/@sveltejs/kit/src/utils/routing.js", "../../../../../../../../node_modules/.pnpm/@sveltejs+kit@2.5.20_@sveltejs+vite-plugin-svelte@3.1.0_svelte@4.2.15_vite@5.2.11_@types+node_5qlfuvulcykb7n4xkfzb6ec34e/node_modules/@sveltejs/kit/src/runtime/client/parse.js", "../../../../../../../../node_modules/.pnpm/@sveltejs+kit@2.5.20_@sveltejs+vite-plugin-svelte@3.1.0_svelte@4.2.15_vite@5.2.11_@types+node_5qlfuvulcykb7n4xkfzb6ec34e/node_modules/@sveltejs/kit/src/runtime/client/session-storage.js", "../../../../../../../../node_modules/.pnpm/@sveltejs+kit@2.5.20_@sveltejs+vite-plugin-svelte@3.1.0_svelte@4.2.15_vite@5.2.11_@types+node_5qlfuvulcykb7n4xkfzb6ec34e/node_modules/@sveltejs/kit/src/runtime/client/constants.js", "../../../../../../../../node_modules/.pnpm/@sveltejs+kit@2.5.20_@sveltejs+vite-plugin-svelte@3.1.0_svelte@4.2.15_vite@5.2.11_@types+node_5qlfuvulcykb7n4xkfzb6ec34e/node_modules/@sveltejs/kit/src/runtime/client/utils.js", "../../../../../../../../node_modules/.pnpm/devalue@5.0.0/node_modules/devalue/src/constants.js", "../../../../../../../../node_modules/.pnpm/devalue@5.0.0/node_modules/devalue/src/parse.js", "../../../../../../../../node_modules/.pnpm/@sveltejs+kit@2.5.20_@sveltejs+vite-plugin-svelte@3.1.0_svelte@4.2.15_vite@5.2.11_@types+node_5qlfuvulcykb7n4xkfzb6ec34e/node_modules/@sveltejs/kit/src/utils/exports.js", "../../../../../../../../node_modules/.pnpm/@sveltejs+kit@2.5.20_@sveltejs+vite-plugin-svelte@3.1.0_svelte@4.2.15_vite@5.2.11_@types+node_5qlfuvulcykb7n4xkfzb6ec34e/node_modules/@sveltejs/kit/src/utils/array.js", "../../../../../../../../node_modules/.pnpm/@sveltejs+kit@2.5.20_@sveltejs+vite-plugin-svelte@3.1.0_svelte@4.2.15_vite@5.2.11_@types+node_5qlfuvulcykb7n4xkfzb6ec34e/node_modules/@sveltejs/kit/src/runtime/control.js", "../../../../../../../../node_modules/.pnpm/@sveltejs+kit@2.5.20_@sveltejs+vite-plugin-svelte@3.1.0_svelte@4.2.15_vite@5.2.11_@types+node_5qlfuvulcykb7n4xkfzb6ec34e/node_modules/@sveltejs/kit/src/runtime/shared.js", "../../../../../../../../node_modules/.pnpm/@sveltejs+kit@2.5.20_@sveltejs+vite-plugin-svelte@3.1.0_svelte@4.2.15_vite@5.2.11_@types+node_5qlfuvulcykb7n4xkfzb6ec34e/node_modules/@sveltejs/kit/src/utils/error.js", "../../../../../../../../node_modules/.pnpm/@sveltejs+kit@2.5.20_@sveltejs+vite-plugin-svelte@3.1.0_svelte@4.2.15_vite@5.2.11_@types+node_5qlfuvulcykb7n4xkfzb6ec34e/node_modules/@sveltejs/kit/src/runtime/client/client.js"], "sourcesContent": ["import { BROWSER, DEV } from 'esm-env';\n\n/**\n * Matches a URI scheme. See https://www.rfc-editor.org/rfc/rfc3986#section-3.1\n * @type {RegExp}\n */\nexport const SCHEME = /^[a-z][a-z\\d+\\-.]+:/i;\n\nconst internal = new URL('sveltekit-internal://');\n\n/**\n * @param {string} base\n * @param {string} path\n */\nexport function resolve(base, path) {\n\t// special case\n\tif (path[0] === '/' && path[1] === '/') return path;\n\n\tlet url = new URL(base, internal);\n\turl = new URL(path, url);\n\n\treturn url.protocol === internal.protocol ? url.pathname + url.search + url.hash : url.href;\n}\n\n/** @param {string} path */\nexport function is_root_relative(path) {\n\treturn path[0] === '/' && path[1] !== '/';\n}\n\n/**\n * @param {string} path\n * @param {import('types').TrailingSlash} trailing_slash\n */\nexport function normalize_path(path, trailing_slash) {\n\tif (path === '/' || trailing_slash === 'ignore') return path;\n\n\tif (trailing_slash === 'never') {\n\t\treturn path.endsWith('/') ? path.slice(0, -1) : path;\n\t} else if (trailing_slash === 'always' && !path.endsWith('/')) {\n\t\treturn path + '/';\n\t}\n\n\treturn path;\n}\n\n/**\n * Decode pathname excluding %25 to prevent further double decoding of params\n * @param {string} pathname\n */\nexport function decode_pathname(pathname) {\n\treturn pathname.split('%25').map(decodeURI).join('%25');\n}\n\n/** @param {Record<string, string>} params */\nexport function decode_params(params) {\n\tfor (const key in params) {\n\t\t// input has already been decoded by decodeURI\n\t\t// now handle the rest\n\t\tparams[key] = decodeURIComponent(params[key]);\n\t}\n\n\treturn params;\n}\n\n/**\n * The error when a URL is malformed is not very helpful, so we augment it with the URI\n * @param {string} uri\n */\nexport function decode_uri(uri) {\n\ttry {\n\t\treturn decodeURI(uri);\n\t} catch (e) {\n\t\tif (e instanceof Error) {\n\t\t\te.message = `Failed to decode URI: ${uri}\\n` + e.message;\n\t\t}\n\t\tthrow e;\n\t}\n}\n\n/**\n * Returns everything up to the first `#` in a URL\n * @param {{href: string}} url_like\n */\nexport function strip_hash({ href }) {\n\treturn href.split('#')[0];\n}\n\n/**\n * URL properties that could change during the lifetime of the page,\n * which excludes things like `origin`\n */\nconst tracked_url_properties = /** @type {const} */ ([\n\t'href',\n\t'pathname',\n\t'search',\n\t'toString',\n\t'toJSON'\n]);\n\n/**\n * @param {URL} url\n * @param {() => void} callback\n * @param {(search_param: string) => void} search_params_callback\n */\nexport function make_trackable(url, callback, search_params_callback) {\n\tconst tracked = new URL(url);\n\n\tObject.defineProperty(tracked, 'searchParams', {\n\t\tvalue: new Proxy(tracked.searchParams, {\n\t\t\tget(obj, key) {\n\t\t\t\tif (key === 'get' || key === 'getAll' || key === 'has') {\n\t\t\t\t\treturn (/**@type {string}*/ param) => {\n\t\t\t\t\t\tsearch_params_callback(param);\n\t\t\t\t\t\treturn obj[key](param);\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\t// if they try to access something different from what is in `tracked_search_params_properties`\n\t\t\t\t// we track the whole url (entries, values, keys etc)\n\t\t\t\tcallback();\n\n\t\t\t\tconst value = Reflect.get(obj, key);\n\t\t\t\treturn typeof value === 'function' ? value.bind(obj) : value;\n\t\t\t}\n\t\t}),\n\t\tenumerable: true,\n\t\tconfigurable: true\n\t});\n\n\tfor (const property of tracked_url_properties) {\n\t\tObject.defineProperty(tracked, property, {\n\t\t\tget() {\n\t\t\t\tcallback();\n\t\t\t\treturn url[property];\n\t\t\t},\n\n\t\t\tenumerable: true,\n\t\t\tconfigurable: true\n\t\t});\n\t}\n\n\tif (!BROWSER) {\n\t\t// @ts-ignore\n\t\ttracked[Symbol.for('nodejs.util.inspect.custom')] = (depth, opts, inspect) => {\n\t\t\treturn inspect(url, opts);\n\t\t};\n\t}\n\n\tif (DEV || !BROWSER) {\n\t\tdisable_hash(tracked);\n\t}\n\n\treturn tracked;\n}\n\n/**\n * Disallow access to `url.hash` on the server and in `load`\n * @param {URL} url\n */\nfunction disable_hash(url) {\n\tallow_nodejs_console_log(url);\n\n\tObject.defineProperty(url, 'hash', {\n\t\tget() {\n\t\t\tthrow new Error(\n\t\t\t\t'Cannot access event.url.hash. Consider using `$page.url.hash` inside a component instead'\n\t\t\t);\n\t\t}\n\t});\n}\n\n/**\n * Disallow access to `url.search` and `url.searchParams` during prerendering\n * @param {URL} url\n */\nexport function disable_search(url) {\n\tallow_nodejs_console_log(url);\n\n\tfor (const property of ['search', 'searchParams']) {\n\t\tObject.defineProperty(url, property, {\n\t\t\tget() {\n\t\t\t\tthrow new Error(`Cannot access url.${property} on a page with prerendering enabled`);\n\t\t\t}\n\t\t});\n\t}\n}\n\n/**\n * Allow URL to be console logged, bypassing disabled properties.\n * @param {URL} url\n */\nfunction allow_nodejs_console_log(url) {\n\tif (!BROWSER) {\n\t\t// @ts-ignore\n\t\turl[Symbol.for('nodejs.util.inspect.custom')] = (depth, opts, inspect) => {\n\t\t\treturn inspect(new URL(url), opts);\n\t\t};\n\t}\n}\n\nconst DATA_SUFFIX = '/__data.json';\nconst HTML_DATA_SUFFIX = '.html__data.json';\n\n/** @param {string} pathname */\nexport function has_data_suffix(pathname) {\n\treturn pathname.endsWith(DATA_SUFFIX) || pathname.endsWith(HTML_DATA_SUFFIX);\n}\n\n/** @param {string} pathname */\nexport function add_data_suffix(pathname) {\n\tif (pathname.endsWith('.html')) return pathname.replace(/\\.html$/, HTML_DATA_SUFFIX);\n\treturn pathname.replace(/\\/$/, '') + DATA_SUFFIX;\n}\n\n/** @param {string} pathname */\nexport function strip_data_suffix(pathname) {\n\tif (pathname.endsWith(HTML_DATA_SUFFIX)) {\n\t\treturn pathname.slice(0, -HTML_DATA_SUFFIX.length) + '.html';\n\t}\n\n\treturn pathname.slice(0, -DATA_SUFFIX.length);\n}\n", "/**\n * Hash using djb2\n * @param {import('types').StrictBody[]} values\n */\nexport function hash(...values) {\n\tlet hash = 5381;\n\n\tfor (const value of values) {\n\t\tif (typeof value === 'string') {\n\t\t\tlet i = value.length;\n\t\t\twhile (i) hash = (hash * 33) ^ value.charCodeAt(--i);\n\t\t} else if (ArrayBuffer.isView(value)) {\n\t\t\tconst buffer = new Uint8Array(value.buffer, value.byteOffset, value.byteLength);\n\t\t\tlet i = buffer.length;\n\t\t\twhile (i) hash = (hash * 33) ^ buffer[--i];\n\t\t} else {\n\t\t\tthrow new TypeError('value must be a string or TypedArray');\n\t\t}\n\t}\n\n\treturn (hash >>> 0).toString(36);\n}\n", "/**\n * @param {string} text\n * @returns {ArrayBufferLike}\n */\nexport function b64_decode(text) {\n\tconst d = atob(text);\n\n\tconst u8 = new Uint8Array(d.length);\n\n\tfor (let i = 0; i < d.length; i++) {\n\t\tu8[i] = d.charCodeAt(i);\n\t}\n\n\treturn u8.buffer;\n}\n\n/**\n * @param {ArrayBuffer} buffer\n * @returns {string}\n */\nexport function b64_encode(buffer) {\n\tif (globalThis.Buffer) {\n\t\treturn Buffer.from(buffer).toString('base64');\n\t}\n\n\tconst little_endian = new Uint8Array(new Uint16Array([1]).buffer)[0] > 0;\n\n\t// The Uint16Array(Uint8Array(...)) ensures the code points are padded with 0's\n\treturn btoa(\n\t\tnew TextDecoder(little_endian ? 'utf-16le' : 'utf-16be').decode(\n\t\t\tnew Uint16Array(new Uint8Array(buffer))\n\t\t)\n\t);\n}\n", "import { BROWSER, DEV } from 'esm-env';\nimport { hash } from '../hash.js';\nimport { b64_decode } from '../utils.js';\n\nlet loading = 0;\n\n/** @type {typeof fetch} */\nexport const native_fetch = BROWSER ? window.fetch : /** @type {any} */ (() => {});\n\nexport function lock_fetch() {\n\tloading += 1;\n}\n\nexport function unlock_fetch() {\n\tloading -= 1;\n}\n\nif (DEV && BROWSER) {\n\tlet can_inspect_stack_trace = false;\n\n\t// detect whether async stack traces work\n\t// eslint-disable-next-line @typescript-eslint/require-await\n\tconst check_stack_trace = async () => {\n\t\tconst stack = /** @type {string} */ (new Error().stack);\n\t\tcan_inspect_stack_trace = stack.includes('check_stack_trace');\n\t};\n\n\tcheck_stack_trace();\n\n\t/**\n\t * @param {RequestInfo | URL} input\n\t * @param {RequestInit & Record<string, any> | undefined} init\n\t */\n\twindow.fetch = (input, init) => {\n\t\t// Check if fetch was called via load_node. the lock method only checks if it was called at the\n\t\t// same time, but not necessarily if it was called from `load`.\n\t\t// We use just the filename as the method name sometimes does not appear on the CI.\n\t\tconst url = input instanceof Request ? input.url : input.toString();\n\t\tconst stack_array = /** @type {string} */ (new Error().stack).split('\\n');\n\t\t// We need to do a cutoff because Safari and Firefox maintain the stack\n\t\t// across events and for example traces a `fetch` call triggered from a button\n\t\t// back to the creation of the event listener and the element creation itself,\n\t\t// where at some point client.js will show up, leading to false positives.\n\t\tconst cutoff = stack_array.findIndex((a) => a.includes('load@') || a.includes('at load'));\n\t\tconst stack = stack_array.slice(0, cutoff + 2).join('\\n');\n\n\t\tconst in_load_heuristic = can_inspect_stack_trace\n\t\t\t? stack.includes('src/runtime/client/client.js')\n\t\t\t: loading;\n\n\t\t// This flag is set in initial_fetch and subsequent_fetch\n\t\tconst used_kit_fetch = init?.__sveltekit_fetch__;\n\n\t\tif (in_load_heuristic && !used_kit_fetch) {\n\t\t\tconsole.warn(\n\t\t\t\t`Loading ${url} using \\`window.fetch\\`. For best results, use the \\`fetch\\` that is passed to your \\`load\\` function: https://kit.svelte.dev/docs/load#making-fetch-requests`\n\t\t\t);\n\t\t}\n\n\t\tconst method = input instanceof Request ? input.method : init?.method || 'GET';\n\n\t\tif (method !== 'GET') {\n\t\t\tcache.delete(build_selector(input));\n\t\t}\n\n\t\treturn native_fetch(input, init);\n\t};\n} else if (BROWSER) {\n\twindow.fetch = (input, init) => {\n\t\tconst method = input instanceof Request ? input.method : init?.method || 'GET';\n\n\t\tif (method !== 'GET') {\n\t\t\tcache.delete(build_selector(input));\n\t\t}\n\n\t\treturn native_fetch(input, init);\n\t};\n}\n\nconst cache = new Map();\n\n/**\n * Should be called on the initial run of load functions that hydrate the page.\n * Saves any requests with cache-control max-age to the cache.\n * @param {URL | string} resource\n * @param {RequestInit} [opts]\n */\nexport function initial_fetch(resource, opts) {\n\tconst selector = build_selector(resource, opts);\n\n\tconst script = document.querySelector(selector);\n\tif (script?.textContent) {\n\t\tlet { body, ...init } = JSON.parse(script.textContent);\n\n\t\tconst ttl = script.getAttribute('data-ttl');\n\t\tif (ttl) cache.set(selector, { body, init, ttl: 1000 * Number(ttl) });\n\t\tconst b64 = script.getAttribute('data-b64');\n\t\tif (b64 !== null) {\n\t\t\t// Can't use native_fetch('data:...;base64,${body}')\n\t\t\t// csp can block the request\n\t\t\tbody = b64_decode(body);\n\t\t}\n\n\t\treturn Promise.resolve(new Response(body, init));\n\t}\n\n\treturn DEV ? dev_fetch(resource, opts) : window.fetch(resource, opts);\n}\n\n/**\n * Tries to get the response from the cache, if max-age allows it, else does a fetch.\n * @param {URL | string} resource\n * @param {string} resolved\n * @param {RequestInit} [opts]\n */\nexport function subsequent_fetch(resource, resolved, opts) {\n\tif (cache.size > 0) {\n\t\tconst selector = build_selector(resource, opts);\n\t\tconst cached = cache.get(selector);\n\t\tif (cached) {\n\t\t\t// https://developer.mozilla.org/en-US/docs/Web/API/Request/cache#value\n\t\t\tif (\n\t\t\t\tperformance.now() < cached.ttl &&\n\t\t\t\t['default', 'force-cache', 'only-if-cached', undefined].includes(opts?.cache)\n\t\t\t) {\n\t\t\t\treturn new Response(cached.body, cached.init);\n\t\t\t}\n\n\t\t\tcache.delete(selector);\n\t\t}\n\t}\n\n\treturn DEV ? dev_fetch(resolved, opts) : window.fetch(resolved, opts);\n}\n\n/**\n * @param {RequestInfo | URL} resource\n * @param {RequestInit & Record<string, any> | undefined} opts\n */\nfunction dev_fetch(resource, opts) {\n\tconst patched_opts = { ...opts };\n\t// This assigns the __sveltekit_fetch__ flag and makes it non-enumerable\n\tObject.defineProperty(patched_opts, '__sveltekit_fetch__', {\n\t\tvalue: true,\n\t\twritable: true,\n\t\tconfigurable: true\n\t});\n\treturn window.fetch(resource, patched_opts);\n}\n\n/**\n * Build the cache key for a given request\n * @param {URL | RequestInfo} resource\n * @param {RequestInit} [opts]\n */\nfunction build_selector(resource, opts) {\n\tconst url = JSON.stringify(resource instanceof Request ? resource.url : resource);\n\n\tlet selector = `script[data-sveltekit-fetched][data-url=${url}]`;\n\n\tif (opts?.headers || opts?.body) {\n\t\t/** @type {import('types').StrictBody[]} */\n\t\tconst values = [];\n\n\t\tif (opts.headers) {\n\t\t\tvalues.push([...new Headers(opts.headers)].join(','));\n\t\t}\n\n\t\tif (opts.body && (typeof opts.body === 'string' || ArrayBuffer.isView(opts.body))) {\n\t\t\tvalues.push(opts.body);\n\t\t}\n\n\t\tselector += `[data-hash=\"${hash(...values)}\"]`;\n\t}\n\n\treturn selector;\n}\n", "import { BROWSER } from 'esm-env';\n\nconst param_pattern = /^(\\[)?(\\.\\.\\.)?(\\w+)(?:=(\\w+))?(\\])?$/;\n\n/**\n * Creates the regex pattern, extracts parameter names, and generates types for a route\n * @param {string} id\n */\nexport function parse_route_id(id) {\n\t/** @type {import('types').RouteParam[]} */\n\tconst params = [];\n\n\tconst pattern =\n\t\tid === '/'\n\t\t\t? /^\\/$/\n\t\t\t: new RegExp(\n\t\t\t\t\t`^${get_route_segments(id)\n\t\t\t\t\t\t.map((segment) => {\n\t\t\t\t\t\t\t// special case — /[...rest]/ could contain zero segments\n\t\t\t\t\t\t\tconst rest_match = /^\\[\\.\\.\\.(\\w+)(?:=(\\w+))?\\]$/.exec(segment);\n\t\t\t\t\t\t\tif (rest_match) {\n\t\t\t\t\t\t\t\tparams.push({\n\t\t\t\t\t\t\t\t\tname: rest_match[1],\n\t\t\t\t\t\t\t\t\tmatcher: rest_match[2],\n\t\t\t\t\t\t\t\t\toptional: false,\n\t\t\t\t\t\t\t\t\trest: true,\n\t\t\t\t\t\t\t\t\tchained: true\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\treturn '(?:/(.*))?';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// special case — /[[optional]]/ could contain zero segments\n\t\t\t\t\t\t\tconst optional_match = /^\\[\\[(\\w+)(?:=(\\w+))?\\]\\]$/.exec(segment);\n\t\t\t\t\t\t\tif (optional_match) {\n\t\t\t\t\t\t\t\tparams.push({\n\t\t\t\t\t\t\t\t\tname: optional_match[1],\n\t\t\t\t\t\t\t\t\tmatcher: optional_match[2],\n\t\t\t\t\t\t\t\t\toptional: true,\n\t\t\t\t\t\t\t\t\trest: false,\n\t\t\t\t\t\t\t\t\tchained: true\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\treturn '(?:/([^/]+))?';\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (!segment) {\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tconst parts = segment.split(/\\[(.+?)\\](?!\\])/);\n\t\t\t\t\t\t\tconst result = parts\n\t\t\t\t\t\t\t\t.map((content, i) => {\n\t\t\t\t\t\t\t\t\tif (i % 2) {\n\t\t\t\t\t\t\t\t\t\tif (content.startsWith('x+')) {\n\t\t\t\t\t\t\t\t\t\t\treturn escape(String.fromCharCode(parseInt(content.slice(2), 16)));\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\tif (content.startsWith('u+')) {\n\t\t\t\t\t\t\t\t\t\t\treturn escape(\n\t\t\t\t\t\t\t\t\t\t\t\tString.fromCharCode(\n\t\t\t\t\t\t\t\t\t\t\t\t\t...content\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t.slice(2)\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t.split('-')\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t.map((code) => parseInt(code, 16))\n\t\t\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t// We know the match cannot be null in the browser because manifest generation\n\t\t\t\t\t\t\t\t\t\t// would have invoked this during build and failed if we hit an invalid\n\t\t\t\t\t\t\t\t\t\t// param/matcher name with non-alphanumeric character.\n\t\t\t\t\t\t\t\t\t\tconst match = /** @type {RegExpExecArray} */ (param_pattern.exec(content));\n\t\t\t\t\t\t\t\t\t\tif (!BROWSER && !match) {\n\t\t\t\t\t\t\t\t\t\t\tthrow new Error(\n\t\t\t\t\t\t\t\t\t\t\t\t`Invalid param: ${content}. Params and matcher names can only have underscores and alphanumeric characters.`\n\t\t\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\tconst [, is_optional, is_rest, name, matcher] = match;\n\t\t\t\t\t\t\t\t\t\t// It's assumed that the following invalid route id cases are already checked\n\t\t\t\t\t\t\t\t\t\t// - unbalanced brackets\n\t\t\t\t\t\t\t\t\t\t// - optional param following rest param\n\n\t\t\t\t\t\t\t\t\t\tparams.push({\n\t\t\t\t\t\t\t\t\t\t\tname,\n\t\t\t\t\t\t\t\t\t\t\tmatcher,\n\t\t\t\t\t\t\t\t\t\t\toptional: !!is_optional,\n\t\t\t\t\t\t\t\t\t\t\trest: !!is_rest,\n\t\t\t\t\t\t\t\t\t\t\tchained: is_rest ? i === 1 && parts[0] === '' : false\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\treturn is_rest ? '(.*?)' : is_optional ? '([^/]*)?' : '([^/]+?)';\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\treturn escape(content);\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.join('');\n\n\t\t\t\t\t\t\treturn '/' + result;\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.join('')}/?$`\n\t\t\t\t);\n\n\treturn { pattern, params };\n}\n\nconst optional_param_regex = /\\/\\[\\[\\w+?(?:=\\w+)?\\]\\]/;\n\n/**\n * Removes optional params from a route ID.\n * @param {string} id\n * @returns The route id with optional params removed\n */\nexport function remove_optional_params(id) {\n\treturn id.replace(optional_param_regex, '');\n}\n\n/**\n * Returns `false` for `(group)` segments\n * @param {string} segment\n */\nfunction affects_path(segment) {\n\treturn !/^\\([^)]+\\)$/.test(segment);\n}\n\n/**\n * Splits a route id into its segments, removing segments that\n * don't affect the path (i.e. groups). The root route is represented by `/`\n * and will be returned as `['']`.\n * @param {string} route\n * @returns string[]\n */\nexport function get_route_segments(route) {\n\treturn route.slice(1).split('/').filter(affects_path);\n}\n\n/**\n * @param {RegExpMatchArray} match\n * @param {import('types').RouteParam[]} params\n * @param {Record<string, import('@sveltejs/kit').ParamMatcher>} matchers\n */\nexport function exec(match, params, matchers) {\n\t/** @type {Record<string, string>} */\n\tconst result = {};\n\n\tconst values = match.slice(1);\n\tconst values_needing_match = values.filter((value) => value !== undefined);\n\n\tlet buffered = 0;\n\n\tfor (let i = 0; i < params.length; i += 1) {\n\t\tconst param = params[i];\n\t\tlet value = values[i - buffered];\n\n\t\t// in the `[[a=b]]/.../[...rest]` case, if one or more optional parameters\n\t\t// weren't matched, roll the skipped values into the rest\n\t\tif (param.chained && param.rest && buffered) {\n\t\t\tvalue = values\n\t\t\t\t.slice(i - buffered, i + 1)\n\t\t\t\t.filter((s) => s)\n\t\t\t\t.join('/');\n\n\t\t\tbuffered = 0;\n\t\t}\n\n\t\t// if `value` is undefined, it means this is an optional or rest parameter\n\t\tif (value === undefined) {\n\t\t\tif (param.rest) result[param.name] = '';\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (!param.matcher || matchers[param.matcher](value)) {\n\t\t\tresult[param.name] = value;\n\n\t\t\t// Now that the params match, reset the buffer if the next param isn't the [...rest]\n\t\t\t// and the next value is defined, otherwise the buffer will cause us to skip values\n\t\t\tconst next_param = params[i + 1];\n\t\t\tconst next_value = values[i + 1];\n\t\t\tif (next_param && !next_param.rest && next_param.optional && next_value && param.chained) {\n\t\t\t\tbuffered = 0;\n\t\t\t}\n\n\t\t\t// There are no more params and no more values, but all non-empty values have been matched\n\t\t\tif (\n\t\t\t\t!next_param &&\n\t\t\t\t!next_value &&\n\t\t\t\tObject.keys(result).length === values_needing_match.length\n\t\t\t) {\n\t\t\t\tbuffered = 0;\n\t\t\t}\n\t\t\tcontinue;\n\t\t}\n\n\t\t// in the `/[[a=b]]/...` case, if the value didn't satisfy the matcher,\n\t\t// keep track of the number of skipped optional parameters and continue\n\t\tif (param.optional && param.chained) {\n\t\t\tbuffered++;\n\t\t\tcontinue;\n\t\t}\n\n\t\t// otherwise, if the matcher returns `false`, the route did not match\n\t\treturn;\n\t}\n\n\tif (buffered) return;\n\treturn result;\n}\n\n/** @param {string} str */\nfunction escape(str) {\n\treturn (\n\t\tstr\n\t\t\t.normalize()\n\t\t\t// escape [ and ] before escaping other characters, since they are used in the replacements\n\t\t\t.replace(/[[\\]]/g, '\\\\$&')\n\t\t\t// replace %, /, ? and # with their encoded versions because decode_pathname leaves them untouched\n\t\t\t.replace(/%/g, '%25')\n\t\t\t.replace(/\\//g, '%2[Ff]')\n\t\t\t.replace(/\\?/g, '%3[Ff]')\n\t\t\t.replace(/#/g, '%23')\n\t\t\t// escape characters that have special meaning in regex\n\t\t\t.replace(/[.*+?^${}()|\\\\]/g, '\\\\$&')\n\t);\n}\n\nconst basic_param_pattern = /\\[(\\[)?(\\.\\.\\.)?(\\w+?)(?:=(\\w+))?\\]\\]?/g;\n\n/**\n * Populate a route ID with params to resolve a pathname.\n * @example\n * ```js\n * resolveRoute(\n *   `/blog/[slug]/[...somethingElse]`,\n *   {\n *     slug: 'hello-world',\n *     somethingElse: 'something/else'\n *   }\n * ); // `/blog/hello-world/something/else`\n * ```\n * @param {string} id\n * @param {Record<string, string | undefined>} params\n * @returns {string}\n */\nexport function resolve_route(id, params) {\n\tconst segments = get_route_segments(id);\n\treturn (\n\t\t'/' +\n\t\tsegments\n\t\t\t.map((segment) =>\n\t\t\t\tsegment.replace(basic_param_pattern, (_, optional, rest, name) => {\n\t\t\t\t\tconst param_value = params[name];\n\n\t\t\t\t\t// This is nested so TS correctly narrows the type\n\t\t\t\t\tif (!param_value) {\n\t\t\t\t\t\tif (optional) return '';\n\t\t\t\t\t\tif (rest && param_value !== undefined) return '';\n\t\t\t\t\t\tthrow new Error(`Missing parameter '${name}' in route ${id}`);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (param_value.startsWith('/') || param_value.endsWith('/'))\n\t\t\t\t\t\tthrow new Error(\n\t\t\t\t\t\t\t`Parameter '${name}' in route ${id} cannot start or end with a slash -- this would cause an invalid route like foo//bar`\n\t\t\t\t\t\t);\n\t\t\t\t\treturn param_value;\n\t\t\t\t})\n\t\t\t)\n\t\t\t.filter(Boolean)\n\t\t\t.join('/')\n\t);\n}\n", "import { exec, parse_route_id } from '../../utils/routing.js';\n\n/**\n * @param {import('./types.js').SvelteKitApp} app\n * @returns {import('types').CSRRoute[]}\n */\nexport function parse({ nodes, server_loads, dictionary, matchers }) {\n\tconst layouts_with_server_load = new Set(server_loads);\n\n\treturn Object.entries(dictionary).map(([id, [leaf, layouts, errors]]) => {\n\t\tconst { pattern, params } = parse_route_id(id);\n\n\t\tconst route = {\n\t\t\tid,\n\t\t\t/** @param {string} path */\n\t\t\texec: (path) => {\n\t\t\t\tconst match = pattern.exec(path);\n\t\t\t\tif (match) return exec(match, params, matchers);\n\t\t\t},\n\t\t\terrors: [1, ...(errors || [])].map((n) => nodes[n]),\n\t\t\tlayouts: [0, ...(layouts || [])].map(create_layout_loader),\n\t\t\tleaf: create_leaf_loader(leaf)\n\t\t};\n\n\t\t// bit of a hack, but ensures that layout/error node lists are the same\n\t\t// length, without which the wrong data will be applied if the route\n\t\t// manifest looks like `[[a, b], [c,], d]`\n\t\troute.errors.length = route.layouts.length = Math.max(\n\t\t\troute.errors.length,\n\t\t\troute.layouts.length\n\t\t);\n\n\t\treturn route;\n\t});\n\n\t/**\n\t * @param {number} id\n\t * @returns {[boolean, import('types').CSRPageNodeLoader]}\n\t */\n\tfunction create_leaf_loader(id) {\n\t\t// whether or not the route uses the server data is\n\t\t// encoded using the ones' complement, to save space\n\t\tconst uses_server_data = id < 0;\n\t\tif (uses_server_data) id = ~id;\n\t\treturn [uses_server_data, nodes[id]];\n\t}\n\n\t/**\n\t * @param {number | undefined} id\n\t * @returns {[boolean, import('types').CSRPageNodeLoader] | undefined}\n\t */\n\tfunction create_layout_loader(id) {\n\t\t// whether or not the layout uses the server data is\n\t\t// encoded in the layouts array, to save space\n\t\treturn id === undefined ? id : [layouts_with_server_load.has(id), nodes[id]];\n\t}\n}\n", "/**\n * Read a value from `sessionStorage`\n * @param {string} key\n * @param {(value: string) => any} parse\n */\nexport function get(key, parse = JSON.parse) {\n\ttry {\n\t\treturn parse(sessionStorage[key]);\n\t} catch {\n\t\t// do nothing\n\t}\n}\n\n/**\n * Write a value to `sessionStorage`\n * @param {string} key\n * @param {any} value\n * @param {(value: any) => string} stringify\n */\nexport function set(key, value, stringify = JSON.stringify) {\n\tconst data = stringify(value);\n\ttry {\n\t\tsessionStorage[key] = data;\n\t} catch {\n\t\t// do nothing\n\t}\n}\n", "export const SNAPSHOT_KEY = 'sveltekit:snapshot';\nexport const SCROLL_KEY = 'sveltekit:scroll';\nexport const STATES_KEY = 'sveltekit:states';\nexport const PAGE_URL_KEY = 'sveltekit:pageurl';\n\nexport const HISTORY_INDEX = 'sveltekit:history';\nexport const NAVIGATION_INDEX = 'sveltekit:navigation';\n\nexport const PRELOAD_PRIORITIES = /** @type {const} */ ({\n\ttap: 1,\n\thover: 2,\n\tviewport: 3,\n\teager: 4,\n\toff: -1,\n\tfalse: -1\n});\n", "import { BROWSER, DEV } from 'esm-env';\nimport { writable } from 'svelte/store';\nimport { assets } from '__sveltekit/paths';\nimport { version } from '__sveltekit/environment';\nimport { PRELOAD_PRIORITIES } from './constants.js';\n\n/* global __SVELTEKIT_APP_VERSION_FILE__, __SVELTEKIT_APP_VERSION_POLL_INTERVAL__ */\n\nexport const origin = BROWSER ? location.origin : '';\n\n/** @param {string | URL} url */\nexport function resolve_url(url) {\n\tif (url instanceof URL) return url;\n\n\tlet baseURI = document.baseURI;\n\n\tif (!baseURI) {\n\t\tconst baseTags = document.getElementsByTagName('base');\n\t\tbaseURI = baseTags.length ? baseTags[0].href : document.URL;\n\t}\n\n\treturn new URL(url, baseURI);\n}\n\nexport function scroll_state() {\n\treturn {\n\t\tx: pageXOffset,\n\t\ty: pageYOffset\n\t};\n}\n\nconst warned = new WeakSet();\n\n/** @typedef {keyof typeof valid_link_options} LinkOptionName */\n\nconst valid_link_options = /** @type {const} */ ({\n\t'preload-code': ['', 'off', 'false', 'tap', 'hover', 'viewport', 'eager'],\n\t'preload-data': ['', 'off', 'false', 'tap', 'hover'],\n\tkeepfocus: ['', 'true', 'off', 'false'],\n\tnoscroll: ['', 'true', 'off', 'false'],\n\treload: ['', 'true', 'off', 'false'],\n\treplacestate: ['', 'true', 'off', 'false']\n});\n\n/**\n * @template {LinkOptionName} T\n * @typedef {typeof valid_link_options[T][number]} ValidLinkOptions\n */\n\n/**\n * @template {LinkOptionName} T\n * @param {Element} element\n * @param {T} name\n */\nfunction link_option(element, name) {\n\tconst value = /** @type {ValidLinkOptions<T> | null} */ (\n\t\telement.getAttribute(`data-sveltekit-${name}`)\n\t);\n\n\tif (DEV) {\n\t\tvalidate_link_option(element, name, value);\n\t}\n\n\treturn value;\n}\n\n/**\n * @template {LinkOptionName} T\n * @template {ValidLinkOptions<T> | null} U\n * @param {Element} element\n * @param {T} name\n * @param {U} value\n */\nfunction validate_link_option(element, name, value) {\n\tif (value === null) return;\n\n\t// @ts-expect-error - includes is dumb\n\tif (!warned.has(element) && !valid_link_options[name].includes(value)) {\n\t\tconsole.error(\n\t\t\t`Unexpected value for ${name} — should be one of ${valid_link_options[name]\n\t\t\t\t.map((option) => JSON.stringify(option))\n\t\t\t\t.join(', ')}`,\n\t\t\telement\n\t\t);\n\n\t\twarned.add(element);\n\t}\n}\n\nconst levels = {\n\t...PRELOAD_PRIORITIES,\n\t'': PRELOAD_PRIORITIES.hover\n};\n\n/**\n * @param {Element} element\n * @returns {Element | null}\n */\nfunction parent_element(element) {\n\tlet parent = element.assignedSlot ?? element.parentNode;\n\n\t// @ts-expect-error handle shadow roots\n\tif (parent?.nodeType === 11) parent = parent.host;\n\n\treturn /** @type {Element} */ (parent);\n}\n\n/**\n * @param {Element} element\n * @param {Element} target\n */\nexport function find_anchor(element, target) {\n\twhile (element && element !== target) {\n\t\tif (element.nodeName.toUpperCase() === 'A' && element.hasAttribute('href')) {\n\t\t\treturn /** @type {HTMLAnchorElement | SVGAElement} */ (element);\n\t\t}\n\n\t\telement = /** @type {Element} */ (parent_element(element));\n\t}\n}\n\n/**\n * @param {HTMLAnchorElement | SVGAElement} a\n * @param {string} base\n */\nexport function get_link_info(a, base) {\n\t/** @type {URL | undefined} */\n\tlet url;\n\n\ttry {\n\t\turl = new URL(a instanceof SVGAElement ? a.href.baseVal : a.href, document.baseURI);\n\t} catch {}\n\n\tconst target = a instanceof SVGAElement ? a.target.baseVal : a.target;\n\n\tconst external =\n\t\t!url ||\n\t\t!!target ||\n\t\tis_external_url(url, base) ||\n\t\t(a.getAttribute('rel') || '').split(/\\s+/).includes('external');\n\n\tconst download = url?.origin === origin && a.hasAttribute('download');\n\n\treturn { url, external, target, download };\n}\n\n/**\n * @param {HTMLFormElement | HTMLAnchorElement | SVGAElement} element\n */\nexport function get_router_options(element) {\n\t/** @type {ValidLinkOptions<'keepfocus'> | null} */\n\tlet keepfocus = null;\n\n\t/** @type {ValidLinkOptions<'noscroll'> | null} */\n\tlet noscroll = null;\n\n\t/** @type {ValidLinkOptions<'preload-code'> | null} */\n\tlet preload_code = null;\n\n\t/** @type {ValidLinkOptions<'preload-data'> | null} */\n\tlet preload_data = null;\n\n\t/** @type {ValidLinkOptions<'reload'> | null} */\n\tlet reload = null;\n\n\t/** @type {ValidLinkOptions<'replacestate'> | null} */\n\tlet replace_state = null;\n\n\t/** @type {Element} */\n\tlet el = element;\n\n\twhile (el && el !== document.documentElement) {\n\t\tif (preload_code === null) preload_code = link_option(el, 'preload-code');\n\t\tif (preload_data === null) preload_data = link_option(el, 'preload-data');\n\t\tif (keepfocus === null) keepfocus = link_option(el, 'keepfocus');\n\t\tif (noscroll === null) noscroll = link_option(el, 'noscroll');\n\t\tif (reload === null) reload = link_option(el, 'reload');\n\t\tif (replace_state === null) replace_state = link_option(el, 'replacestate');\n\n\t\tel = /** @type {Element} */ (parent_element(el));\n\t}\n\n\t/** @param {string | null} value */\n\tfunction get_option_state(value) {\n\t\tswitch (value) {\n\t\t\tcase '':\n\t\t\tcase 'true':\n\t\t\t\treturn true;\n\t\t\tcase 'off':\n\t\t\tcase 'false':\n\t\t\t\treturn false;\n\t\t\tdefault:\n\t\t\t\treturn undefined;\n\t\t}\n\t}\n\n\treturn {\n\t\tpreload_code: levels[preload_code ?? 'off'],\n\t\tpreload_data: levels[preload_data ?? 'off'],\n\t\tkeepfocus: get_option_state(keepfocus),\n\t\tnoscroll: get_option_state(noscroll),\n\t\treload: get_option_state(reload),\n\t\treplace_state: get_option_state(replace_state)\n\t};\n}\n\n/** @param {any} value */\nexport function notifiable_store(value) {\n\tconst store = writable(value);\n\tlet ready = true;\n\n\tfunction notify() {\n\t\tready = true;\n\t\tstore.update((val) => val);\n\t}\n\n\t/** @param {any} new_value */\n\tfunction set(new_value) {\n\t\tready = false;\n\t\tstore.set(new_value);\n\t}\n\n\t/** @param {(value: any) => void} run */\n\tfunction subscribe(run) {\n\t\t/** @type {any} */\n\t\tlet old_value;\n\t\treturn store.subscribe((new_value) => {\n\t\t\tif (old_value === undefined || (ready && new_value !== old_value)) {\n\t\t\t\trun((old_value = new_value));\n\t\t\t}\n\t\t});\n\t}\n\n\treturn { notify, set, subscribe };\n}\n\nexport function create_updated_store() {\n\tconst { set, subscribe } = writable(false);\n\n\tif (DEV || !BROWSER) {\n\t\treturn {\n\t\t\tsubscribe,\n\t\t\t// eslint-disable-next-line @typescript-eslint/require-await\n\t\t\tcheck: async () => false\n\t\t};\n\t}\n\n\tconst interval = __SVELTEKIT_APP_VERSION_POLL_INTERVAL__;\n\n\t/** @type {NodeJS.Timeout} */\n\tlet timeout;\n\n\t/** @type {() => Promise<boolean>} */\n\tasync function check() {\n\t\tclearTimeout(timeout);\n\n\t\tif (interval) timeout = setTimeout(check, interval);\n\n\t\ttry {\n\t\t\tconst res = await fetch(`${assets}/${__SVELTEKIT_APP_VERSION_FILE__}`, {\n\t\t\t\theaders: {\n\t\t\t\t\tpragma: 'no-cache',\n\t\t\t\t\t'cache-control': 'no-cache'\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tif (!res.ok) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\tconst data = await res.json();\n\t\t\tconst updated = data.version !== version;\n\n\t\t\tif (updated) {\n\t\t\t\tset(true);\n\t\t\t\tclearTimeout(timeout);\n\t\t\t}\n\n\t\t\treturn updated;\n\t\t} catch {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\tif (interval) timeout = setTimeout(check, interval);\n\n\treturn {\n\t\tsubscribe,\n\t\tcheck\n\t};\n}\n\n/**\n * @param {URL} url\n * @param {string} base\n */\nexport function is_external_url(url, base) {\n\treturn url.origin !== origin || !url.pathname.startsWith(base);\n}\n", "export const UNDEFINED = -1;\nexport const HOLE = -2;\nexport const NAN = -3;\nexport const POSITIVE_INFINITY = -4;\nexport const NEGATIVE_INFINITY = -5;\nexport const NEGATIVE_ZERO = -6;\n", "import {\n\tHOLE,\n\tNAN,\n\tNEGATIVE_INFINITY,\n\tNEGATIVE_ZERO,\n\tPOSITIVE_INFINITY,\n\tUNDEFINED\n} from './constants.js';\n\n/**\n * Revive a value serialized with `devalue.stringify`\n * @param {string} serialized\n * @param {Record<string, (value: any) => any>} [revivers]\n */\nexport function parse(serialized, revivers) {\n\treturn unflatten(JSON.parse(serialized), revivers);\n}\n\n/**\n * Revive a value flattened with `devalue.stringify`\n * @param {number | any[]} parsed\n * @param {Record<string, (value: any) => any>} [revivers]\n */\nexport function unflatten(parsed, revivers) {\n\tif (typeof parsed === 'number') return hydrate(parsed, true);\n\n\tif (!Array.isArray(parsed) || parsed.length === 0) {\n\t\tthrow new Error('Invalid input');\n\t}\n\n\tconst values = /** @type {any[]} */ (parsed);\n\n\tconst hydrated = Array(values.length);\n\n\t/**\n\t * @param {number} index\n\t * @returns {any}\n\t */\n\tfunction hydrate(index, standalone = false) {\n\t\tif (index === UNDEFINED) return undefined;\n\t\tif (index === NAN) return NaN;\n\t\tif (index === POSITIVE_INFINITY) return Infinity;\n\t\tif (index === NEGATIVE_INFINITY) return -Infinity;\n\t\tif (index === NEGATIVE_ZERO) return -0;\n\n\t\tif (standalone) throw new Error(`Invalid input`);\n\n\t\tif (index in hydrated) return hydrated[index];\n\n\t\tconst value = values[index];\n\n\t\tif (!value || typeof value !== 'object') {\n\t\t\thydrated[index] = value;\n\t\t} else if (Array.isArray(value)) {\n\t\t\tif (typeof value[0] === 'string') {\n\t\t\t\tconst type = value[0];\n\n\t\t\t\tconst reviver = revivers?.[type];\n\t\t\t\tif (reviver) {\n\t\t\t\t\treturn (hydrated[index] = reviver(hydrate(value[1])));\n\t\t\t\t}\n\n\t\t\t\tswitch (type) {\n\t\t\t\t\tcase 'Date':\n\t\t\t\t\t\thydrated[index] = new Date(value[1]);\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'Set':\n\t\t\t\t\t\tconst set = new Set();\n\t\t\t\t\t\thydrated[index] = set;\n\t\t\t\t\t\tfor (let i = 1; i < value.length; i += 1) {\n\t\t\t\t\t\t\tset.add(hydrate(value[i]));\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'Map':\n\t\t\t\t\t\tconst map = new Map();\n\t\t\t\t\t\thydrated[index] = map;\n\t\t\t\t\t\tfor (let i = 1; i < value.length; i += 2) {\n\t\t\t\t\t\t\tmap.set(hydrate(value[i]), hydrate(value[i + 1]));\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'RegExp':\n\t\t\t\t\t\thydrated[index] = new RegExp(value[1], value[2]);\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'Object':\n\t\t\t\t\t\thydrated[index] = Object(value[1]);\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'BigInt':\n\t\t\t\t\t\thydrated[index] = BigInt(value[1]);\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'null':\n\t\t\t\t\t\tconst obj = Object.create(null);\n\t\t\t\t\t\thydrated[index] = obj;\n\t\t\t\t\t\tfor (let i = 1; i < value.length; i += 2) {\n\t\t\t\t\t\t\tobj[value[i]] = hydrate(value[i + 1]);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tthrow new Error(`Unknown type ${type}`);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tconst array = new Array(value.length);\n\t\t\t\thydrated[index] = array;\n\n\t\t\t\tfor (let i = 0; i < value.length; i += 1) {\n\t\t\t\t\tconst n = value[i];\n\t\t\t\t\tif (n === HOLE) continue;\n\n\t\t\t\t\tarray[i] = hydrate(n);\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\t/** @type {Record<string, any>} */\n\t\t\tconst object = {};\n\t\t\thydrated[index] = object;\n\n\t\t\tfor (const key in value) {\n\t\t\t\tconst n = value[key];\n\t\t\t\tobject[key] = hydrate(n);\n\t\t\t}\n\t\t}\n\n\t\treturn hydrated[index];\n\t}\n\n\treturn hydrate(0);\n}\n", "/**\n * @param {Set<string>} expected\n */\nfunction validator(expected) {\n\t/**\n\t * @param {any} module\n\t * @param {string} [file]\n\t */\n\tfunction validate(module, file) {\n\t\tif (!module) return;\n\n\t\tfor (const key in module) {\n\t\t\tif (key[0] === '_' || expected.has(key)) continue; // key is valid in this module\n\n\t\t\tconst values = [...expected.values()];\n\n\t\t\tconst hint =\n\t\t\t\thint_for_supported_files(key, file?.slice(file.lastIndexOf('.'))) ??\n\t\t\t\t`valid exports are ${values.join(', ')}, or anything with a '_' prefix`;\n\n\t\t\tthrow new Error(`Invalid export '${key}'${file ? ` in ${file}` : ''} (${hint})`);\n\t\t}\n\t}\n\n\treturn validate;\n}\n\n/**\n * @param {string} key\n * @param {string} ext\n * @returns {string | void}\n */\nfunction hint_for_supported_files(key, ext = '.js') {\n\tconst supported_files = [];\n\n\tif (valid_layout_exports.has(key)) {\n\t\tsupported_files.push(`+layout${ext}`);\n\t}\n\n\tif (valid_page_exports.has(key)) {\n\t\tsupported_files.push(`+page${ext}`);\n\t}\n\n\tif (valid_layout_server_exports.has(key)) {\n\t\tsupported_files.push(`+layout.server${ext}`);\n\t}\n\n\tif (valid_page_server_exports.has(key)) {\n\t\tsupported_files.push(`+page.server${ext}`);\n\t}\n\n\tif (valid_server_exports.has(key)) {\n\t\tsupported_files.push(`+server${ext}`);\n\t}\n\n\tif (supported_files.length > 0) {\n\t\treturn `'${key}' is a valid export in ${supported_files.slice(0, -1).join(', ')}${\n\t\t\tsupported_files.length > 1 ? ' or ' : ''\n\t\t}${supported_files.at(-1)}`;\n\t}\n}\n\nconst valid_layout_exports = new Set([\n\t'load',\n\t'prerender',\n\t'csr',\n\t'ssr',\n\t'trailingSlash',\n\t'config'\n]);\nconst valid_page_exports = new Set([...valid_layout_exports, 'entries']);\nconst valid_layout_server_exports = new Set([...valid_layout_exports]);\nconst valid_page_server_exports = new Set([...valid_layout_server_exports, 'actions', 'entries']);\nconst valid_server_exports = new Set([\n\t'GET',\n\t'POST',\n\t'PATCH',\n\t'PUT',\n\t'DELETE',\n\t'OPTIONS',\n\t'HEAD',\n\t'fallback',\n\t'prerender',\n\t'trailingSlash',\n\t'config',\n\t'entries'\n]);\n\nexport const validate_layout_exports = validator(valid_layout_exports);\nexport const validate_page_exports = validator(valid_page_exports);\nexport const validate_layout_server_exports = validator(valid_layout_server_exports);\nexport const validate_page_server_exports = validator(valid_page_server_exports);\nexport const validate_server_exports = validator(valid_server_exports);\n", "/**\n * Removes nullish values from an array.\n *\n * @template T\n * @param {Array<T>} arr\n */\nexport function compact(arr) {\n\treturn arr.filter(/** @returns {val is NonNullable<T>} */ (val) => val != null);\n}\n", "export class HttpError {\n\t/**\n\t * @param {number} status\n\t * @param {{message: string} extends App.Error ? (App.Error | string | undefined) : App.Error} body\n\t */\n\tconstructor(status, body) {\n\t\tthis.status = status;\n\t\tif (typeof body === 'string') {\n\t\t\tthis.body = { message: body };\n\t\t} else if (body) {\n\t\t\tthis.body = body;\n\t\t} else {\n\t\t\tthis.body = { message: `Error: ${status}` };\n\t\t}\n\t}\n\n\ttoString() {\n\t\treturn JSON.stringify(this.body);\n\t}\n}\n\nexport class Redirect {\n\t/**\n\t * @param {300 | 301 | 302 | 303 | 304 | 305 | 306 | 307 | 308} status\n\t * @param {string} location\n\t */\n\tconstructor(status, location) {\n\t\tthis.status = status;\n\t\tthis.location = location;\n\t}\n}\n\n/**\n * An error that was thrown from within the SvelteKit runtime that is not fatal and doesn't result in a 500, such as a 404.\n * `SvelteKitError` goes through `handleError`.\n * @extends Error\n */\nexport class SvelteKitError extends Error {\n\t/**\n\t * @param {number} status\n\t * @param {string} text\n\t * @param {string} message\n\t */\n\tconstructor(status, text, message) {\n\t\tsuper(message);\n\t\tthis.status = status;\n\t\tthis.text = text;\n\t}\n}\n\n/**\n * @template {Record<string, unknown> | undefined} [T=undefined]\n */\nexport class ActionFailure {\n\t/**\n\t * @param {number} status\n\t * @param {T} data\n\t */\n\tconstructor(status, data) {\n\t\tthis.status = status;\n\t\tthis.data = data;\n\t}\n}\n\n/**\n * This is a grotesque hack that, in dev, allows us to replace the implementations\n * of these classes that you'd get by importing them from `@sveltejs/kit` with the\n * ones that are imported via Vite and loaded internally, so that instanceof\n * checks work even though SvelteKit imports this module via Vite and consumers\n * import it via Node\n * @param {{\n *   ActionFailure: typeof ActionFailure;\n *   HttpError: typeof HttpError;\n *   Redirect: typeof Redirect;\n *   SvelteKitError: typeof SvelteKitError;\n * }} implementations\n */\nexport function replace_implementations(implementations) {\n\t// @ts-expect-error\n\tActionFailure = implementations.ActionFailure; // eslint-disable-line no-class-assign\n\t// @ts-expect-error\n\tHttpError = implementations.HttpError; // eslint-disable-line no-class-assign\n\t// @ts-expect-error\n\tRedirect = implementations.Redirect; // eslint-disable-line no-class-assign\n\t// @ts-expect-error\n\tSvelteKitError = implementations.SvelteKitError; // eslint-disable-line no-class-assign\n}\n", "/**\n * @param {string} route_id\n * @param {string} dep\n */\nexport function validate_depends(route_id, dep) {\n\tconst match = /^(moz-icon|view-source|jar):/.exec(dep);\n\tif (match) {\n\t\tconsole.warn(\n\t\t\t`${route_id}: Calling \\`depends('${dep}')\\` will throw an error in Firefox because \\`${match[1]}\\` is a special URI scheme`\n\t\t);\n\t}\n}\n\nexport const INVALIDATED_PARAM = 'x-sveltekit-invalidated';\n\nexport const TRAILING_SLASH_PARAM = 'x-sveltekit-trailing-slash';\n", "import { HttpError, SvelteKitError } from '../runtime/control.js';\n\n/**\n * @param {unknown} err\n * @return {Error}\n */\nexport function coalesce_to_error(err) {\n\treturn err instanceof Error ||\n\t\t(err && /** @type {any} */ (err).name && /** @type {any} */ (err).message)\n\t\t? /** @type {Error} */ (err)\n\t\t: new Error(JSON.stringify(err));\n}\n\n/**\n * This is an identity function that exists to make TypeScript less\n * paranoid about people throwing things that aren't errors, which\n * frankly is not something we should care about\n * @param {unknown} error\n */\nexport function normalize_error(error) {\n\treturn /** @type {import('../runtime/control.js').Redirect | HttpError | SvelteKitError | Error} */ (\n\t\terror\n\t);\n}\n\n/**\n * @param {unknown} error\n */\nexport function get_status(error) {\n\treturn error instanceof HttpError || error instanceof SvelteKitError ? error.status : 500;\n}\n\n/**\n * @param {unknown} error\n */\nexport function get_message(error) {\n\treturn error instanceof SvelteKitError ? error.text : 'Internal Error';\n}\n", "import { BROWSER, DEV } from 'esm-env';\nimport { onMount, tick } from 'svelte';\nimport {\n\tadd_data_suffix,\n\tdecode_params,\n\tdecode_pathname,\n\tstrip_hash,\n\tmake_trackable,\n\tnormalize_path\n} from '../../utils/url.js';\nimport {\n\tinitial_fetch,\n\tlock_fetch,\n\tnative_fetch,\n\tsubsequent_fetch,\n\tunlock_fetch\n} from './fetcher.js';\nimport { parse } from './parse.js';\nimport * as storage from './session-storage.js';\nimport {\n\tfind_anchor,\n\tresolve_url,\n\tget_link_info,\n\tget_router_options,\n\tis_external_url,\n\torigin,\n\tscroll_state,\n\tnotifiable_store,\n\tcreate_updated_store\n} from './utils.js';\nimport { base } from '__sveltekit/paths';\nimport * as devalue from 'devalue';\nimport {\n\tHISTORY_INDEX,\n\tNAVIGATION_INDEX,\n\tPRELOAD_PRIORITIES,\n\tSCROLL_KEY,\n\tSTATES_KEY,\n\tSNAPSHOT_KEY,\n\tPAGE_URL_KEY\n} from './constants.js';\nimport { validate_page_exports } from '../../utils/exports.js';\nimport { compact } from '../../utils/array.js';\nimport { HttpError, Redirect, SvelteKitError } from '../control.js';\nimport { INVALIDATED_PARAM, TRAILING_SLASH_PARAM, validate_depends } from '../shared.js';\nimport { get_message, get_status } from '../../utils/error.js';\nimport { writable } from 'svelte/store';\n\nlet errored = false;\n\n// We track the scroll position associated with each history entry in sessionStorage,\n// rather than on history.state itself, because when navigation is driven by\n// popstate it's too late to update the scroll position associated with the\n// state we're navigating from\n/**\n * history index -> { x, y }\n * @type {Record<number, { x: number; y: number }>}\n */\nconst scroll_positions = storage.get(SCROLL_KEY) ?? {};\n\n/**\n * navigation index -> any\n * @type {Record<string, any[]>}\n */\nconst snapshots = storage.get(SNAPSHOT_KEY) ?? {};\n\nif (DEV && BROWSER) {\n\tlet warned = false;\n\n\tconst current_module_url = import.meta.url.split('?')[0]; // remove query params that vite adds to the URL when it is loaded from node_modules\n\n\tconst warn = () => {\n\t\tif (warned) return;\n\n\t\t// Rather than saving a pointer to the original history methods, which would prevent monkeypatching by other libs,\n\t\t// inspect the stack trace to see if we're being called from within SvelteKit.\n\t\tlet stack = new Error().stack?.split('\\n');\n\t\tif (!stack) return;\n\t\tif (!stack[0].includes('https:') && !stack[0].includes('http:')) stack = stack.slice(1); // Chrome includes the error message in the stack\n\t\tstack = stack.slice(2); // remove `warn` and the place where `warn` was called\n\t\t// Can be falsy if was called directly from an anonymous function\n\t\tif (stack[0]?.includes(current_module_url)) return;\n\n\t\twarned = true;\n\n\t\tconsole.warn(\n\t\t\t\"Avoid using `history.pushState(...)` and `history.replaceState(...)` as these will conflict with SvelteKit's router. Use the `pushState` and `replaceState` imports from `$app/navigation` instead.\"\n\t\t);\n\t};\n\n\tconst push_state = history.pushState;\n\thistory.pushState = (...args) => {\n\t\twarn();\n\t\treturn push_state.apply(history, args);\n\t};\n\n\tconst replace_state = history.replaceState;\n\thistory.replaceState = (...args) => {\n\t\twarn();\n\t\treturn replace_state.apply(history, args);\n\t};\n}\n\nexport const stores = {\n\turl: /* @__PURE__ */ notifiable_store({}),\n\tpage: /* @__PURE__ */ notifiable_store({}),\n\tnavigating: /* @__PURE__ */ writable(\n\t\t/** @type {import('@sveltejs/kit').Navigation | null} */ (null)\n\t),\n\tupdated: /* @__PURE__ */ create_updated_store()\n};\n\n/** @param {number} index */\nfunction update_scroll_positions(index) {\n\tscroll_positions[index] = scroll_state();\n}\n\n/**\n * @param {number} current_history_index\n * @param {number} current_navigation_index\n */\nfunction clear_onward_history(current_history_index, current_navigation_index) {\n\t// if we navigated back, then pushed a new state, we can\n\t// release memory by pruning the scroll/snapshot lookup\n\tlet i = current_history_index + 1;\n\twhile (scroll_positions[i]) {\n\t\tdelete scroll_positions[i];\n\t\ti += 1;\n\t}\n\n\ti = current_navigation_index + 1;\n\twhile (snapshots[i]) {\n\t\tdelete snapshots[i];\n\t\ti += 1;\n\t}\n}\n\n/**\n * Loads `href` the old-fashioned way, with a full page reload.\n * Returns a `Promise` that never resolves (to prevent any\n * subsequent work, e.g. history manipulation, from happening)\n * @param {URL} url\n */\nfunction native_navigation(url) {\n\tlocation.href = url.href;\n\treturn new Promise(() => {});\n}\n\nfunction noop() {}\n\n/** @type {import('types').CSRRoute[]} */\nlet routes;\n/** @type {import('types').CSRPageNodeLoader} */\nlet default_layout_loader;\n/** @type {import('types').CSRPageNodeLoader} */\nlet default_error_loader;\n/** @type {HTMLElement} */\nlet container;\n/** @type {HTMLElement} */\nlet target;\n/** @type {import('./types.js').SvelteKitApp} */\nlet app;\n\n/** @type {Array<((url: URL) => boolean)>} */\nconst invalidated = [];\n\n/**\n * An array of the `+layout.svelte` and `+page.svelte` component instances\n * that currently live on the page — used for capturing and restoring snapshots.\n * It's updated/manipulated through `bind:this` in `Root.svelte`.\n * @type {import('svelte').SvelteComponent[]}\n */\nconst components = [];\n\n/** @type {{id: string, token: {}, promise: Promise<import('./types.js').NavigationResult>} | null} */\nlet load_cache = null;\n\n/** @type {Array<(navigation: import('@sveltejs/kit').BeforeNavigate) => void>} */\nconst before_navigate_callbacks = [];\n\n/** @type {Array<(navigation: import('@sveltejs/kit').OnNavigate) => import('types').MaybePromise<(() => void) | void>>} */\nconst on_navigate_callbacks = [];\n\n/** @type {Array<(navigation: import('@sveltejs/kit').AfterNavigate) => void>} */\nlet after_navigate_callbacks = [];\n\n/** @type {import('./types.js').NavigationState} */\nlet current = {\n\tbranch: [],\n\terror: null,\n\t// @ts-ignore - we need the initial value to be null\n\turl: null\n};\n\n/** this being true means we SSR'd */\nlet hydrated = false;\nlet started = false;\nlet autoscroll = true;\nlet updating = false;\nlet navigating = false;\nlet hash_navigating = false;\n/** True as soon as there happened one client-side navigation (excluding the SvelteKit-initialized initial one when in SPA mode) */\nlet has_navigated = false;\n\nlet force_invalidation = false;\n\n/** @type {import('svelte').SvelteComponent} */\nlet root;\n\n/** @type {number} keeping track of the history index in order to prevent popstate navigation events if needed */\nlet current_history_index;\n\n/** @type {number} */\nlet current_navigation_index;\n\n/** @type {import('@sveltejs/kit').Page} */\nlet page;\n\n/** @type {{}} */\nlet token;\n\n/**\n * A set of tokens which are associated to current preloads.\n * If a preload becomes a real navigation, it's removed from the set.\n * If a preload token is in the set and the preload errors, the error\n * handling logic (for example reloading) is skipped.\n */\nconst preload_tokens = new Set();\n\n/** @type {Promise<void> | null} */\nlet pending_invalidate;\n\n/**\n * @param {import('./types.js').SvelteKitApp} _app\n * @param {HTMLElement} _target\n * @param {Parameters<typeof _hydrate>[1]} [hydrate]\n */\nexport async function start(_app, _target, hydrate) {\n\tif (DEV && _target === document.body) {\n\t\tconsole.warn(\n\t\t\t'Placing %sveltekit.body% directly inside <body> is not recommended, as your app may break for users who have certain browser extensions installed.\\n\\nConsider wrapping it in an element:\\n\\n<div style=\"display: contents\">\\n  %sveltekit.body%\\n</div>'\n\t\t);\n\t}\n\n\t// detect basic auth credentials in the current URL\n\t// https://github.com/sveltejs/kit/pull/11179\n\t// if so, refresh the page without credentials\n\tif (document.URL !== location.href) {\n\t\t// eslint-disable-next-line no-self-assign\n\t\tlocation.href = location.href;\n\t}\n\n\tapp = _app;\n\troutes = parse(_app);\n\tcontainer = __SVELTEKIT_EMBEDDED__ ? _target : document.documentElement;\n\ttarget = _target;\n\n\t// we import the root layout/error nodes eagerly, so that\n\t// connectivity errors after initialisation don't nuke the app\n\tdefault_layout_loader = _app.nodes[0];\n\tdefault_error_loader = _app.nodes[1];\n\tdefault_layout_loader();\n\tdefault_error_loader();\n\n\tcurrent_history_index = history.state?.[HISTORY_INDEX];\n\tcurrent_navigation_index = history.state?.[NAVIGATION_INDEX];\n\n\tif (!current_history_index) {\n\t\t// we use Date.now() as an offset so that cross-document navigations\n\t\t// within the app don't result in data loss\n\t\tcurrent_history_index = current_navigation_index = Date.now();\n\n\t\t// create initial history entry, so we can return here\n\t\thistory.replaceState(\n\t\t\t{\n\t\t\t\t...history.state,\n\t\t\t\t[HISTORY_INDEX]: current_history_index,\n\t\t\t\t[NAVIGATION_INDEX]: current_navigation_index\n\t\t\t},\n\t\t\t''\n\t\t);\n\t}\n\n\t// if we reload the page, or Cmd-Shift-T back to it,\n\t// recover scroll position\n\tconst scroll = scroll_positions[current_history_index];\n\tif (scroll) {\n\t\thistory.scrollRestoration = 'manual';\n\t\tscrollTo(scroll.x, scroll.y);\n\t}\n\n\tif (hydrate) {\n\t\tawait _hydrate(target, hydrate);\n\t} else {\n\t\tgoto(location.href, { replaceState: true });\n\t}\n\n\t_start_router();\n}\n\nasync function _invalidate() {\n\t// Accept all invalidations as they come, don't swallow any while another invalidation\n\t// is running because subsequent invalidations may make earlier ones outdated,\n\t// but batch multiple synchronous invalidations.\n\tawait (pending_invalidate ||= Promise.resolve());\n\tif (!pending_invalidate) return;\n\tpending_invalidate = null;\n\n\tconst intent = get_navigation_intent(current.url, true);\n\n\t// Clear preload, it might be affected by the invalidation.\n\t// Also solves an edge case where a preload is triggered, the navigation for it\n\t// was then triggered and is still running while the invalidation kicks in,\n\t// at which point the invalidation should take over and \"win\".\n\tload_cache = null;\n\n\tconst nav_token = (token = {});\n\tconst navigation_result = intent && (await load_route(intent));\n\tif (!navigation_result || nav_token !== token) return;\n\n\tif (navigation_result.type === 'redirect') {\n\t\treturn _goto(new URL(navigation_result.location, current.url).href, {}, 1, nav_token);\n\t}\n\n\tif (navigation_result.props.page) {\n\t\tpage = navigation_result.props.page;\n\t}\n\tcurrent = navigation_result.state;\n\treset_invalidation();\n\troot.$set(navigation_result.props);\n}\n\nfunction reset_invalidation() {\n\tinvalidated.length = 0;\n\tforce_invalidation = false;\n}\n\n/** @param {number} index */\nfunction capture_snapshot(index) {\n\tif (components.some((c) => c?.snapshot)) {\n\t\tsnapshots[index] = components.map((c) => c?.snapshot?.capture());\n\t}\n}\n\n/** @param {number} index */\nfunction restore_snapshot(index) {\n\tsnapshots[index]?.forEach((value, i) => {\n\t\tcomponents[i]?.snapshot?.restore(value);\n\t});\n}\n\nfunction persist_state() {\n\tupdate_scroll_positions(current_history_index);\n\tstorage.set(SCROLL_KEY, scroll_positions);\n\n\tcapture_snapshot(current_navigation_index);\n\tstorage.set(SNAPSHOT_KEY, snapshots);\n}\n\n/**\n * @param {string | URL} url\n * @param {{ replaceState?: boolean; noScroll?: boolean; keepFocus?: boolean; invalidateAll?: boolean; state?: Record<string, any> }} options\n * @param {number} redirect_count\n * @param {{}} [nav_token]\n */\nasync function _goto(url, options, redirect_count, nav_token) {\n\treturn navigate({\n\t\ttype: 'goto',\n\t\turl: resolve_url(url),\n\t\tkeepfocus: options.keepFocus,\n\t\tnoscroll: options.noScroll,\n\t\treplace_state: options.replaceState,\n\t\tstate: options.state,\n\t\tredirect_count,\n\t\tnav_token,\n\t\taccept: () => {\n\t\t\tif (options.invalidateAll) {\n\t\t\t\tforce_invalidation = true;\n\t\t\t}\n\t\t}\n\t});\n}\n\n/** @param {import('./types.js').NavigationIntent} intent */\nasync function _preload_data(intent) {\n\t// Reuse the existing pending preload if it's for the same navigation.\n\t// Prevents an edge case where same preload is triggered multiple times,\n\t// then a later one is becoming the real navigation and the preload tokens\n\t// get out of sync.\n\tif (intent.id !== load_cache?.id) {\n\t\tconst preload = {};\n\t\tpreload_tokens.add(preload);\n\t\tload_cache = {\n\t\t\tid: intent.id,\n\t\t\ttoken: preload,\n\t\t\tpromise: load_route({ ...intent, preload }).then((result) => {\n\t\t\t\tpreload_tokens.delete(preload);\n\t\t\t\tif (result.type === 'loaded' && result.state.error) {\n\t\t\t\t\t// Don't cache errors, because they might be transient\n\t\t\t\t\tload_cache = null;\n\t\t\t\t}\n\t\t\t\treturn result;\n\t\t\t})\n\t\t};\n\t}\n\n\treturn load_cache.promise;\n}\n\n/** @param {string} pathname */\nasync function _preload_code(pathname) {\n\tconst route = routes.find((route) => route.exec(get_url_path(pathname)));\n\n\tif (route) {\n\t\tawait Promise.all([...route.layouts, route.leaf].map((load) => load?.[1]()));\n\t}\n}\n\n/**\n * @param {import('./types.js').NavigationFinished} result\n * @param {HTMLElement} target\n * @param {boolean} hydrate\n */\nfunction initialize(result, target, hydrate) {\n\tif (DEV && result.state.error && document.querySelector('vite-error-overlay')) return;\n\n\tcurrent = result.state;\n\n\tconst style = document.querySelector('style[data-sveltekit]');\n\tif (style) style.remove();\n\n\tpage = /** @type {import('@sveltejs/kit').Page} */ (result.props.page);\n\n\troot = new app.root({\n\t\ttarget,\n\t\tprops: { ...result.props, stores, components },\n\t\thydrate\n\t});\n\n\trestore_snapshot(current_navigation_index);\n\n\t/** @type {import('@sveltejs/kit').AfterNavigate} */\n\tconst navigation = {\n\t\tfrom: null,\n\t\tto: {\n\t\t\tparams: current.params,\n\t\t\troute: { id: current.route?.id ?? null },\n\t\t\turl: new URL(location.href)\n\t\t},\n\t\twillUnload: false,\n\t\ttype: 'enter',\n\t\tcomplete: Promise.resolve()\n\t};\n\n\tafter_navigate_callbacks.forEach((fn) => fn(navigation));\n\n\tstarted = true;\n}\n\n/**\n *\n * @param {{\n *   url: URL;\n *   params: Record<string, string>;\n *   branch: Array<import('./types.js').BranchNode | undefined>;\n *   status: number;\n *   error: App.Error | null;\n *   route: import('types').CSRRoute | null;\n *   form?: Record<string, any> | null;\n * }} opts\n */\nfunction get_navigation_result_from_branch({ url, params, branch, status, error, route, form }) {\n\t/** @type {import('types').TrailingSlash} */\n\tlet slash = 'never';\n\n\t// if `paths.base === '/a/b/c`, then the root route is always `/a/b/c/`, regardless of\n\t// the `trailingSlash` route option, so that relative paths to JS and CSS work\n\tif (base && (url.pathname === base || url.pathname === base + '/')) {\n\t\tslash = 'always';\n\t} else {\n\t\tfor (const node of branch) {\n\t\t\tif (node?.slash !== undefined) slash = node.slash;\n\t\t}\n\t}\n\n\turl.pathname = normalize_path(url.pathname, slash);\n\n\t// eslint-disable-next-line\n\turl.search = url.search; // turn `/?` into `/`\n\n\t/** @type {import('./types.js').NavigationFinished} */\n\tconst result = {\n\t\ttype: 'loaded',\n\t\tstate: {\n\t\t\turl,\n\t\t\tparams,\n\t\t\tbranch,\n\t\t\terror,\n\t\t\troute\n\t\t},\n\t\tprops: {\n\t\t\t// @ts-ignore Somehow it's getting SvelteComponent and SvelteComponentDev mixed up\n\t\t\tconstructors: compact(branch).map((branch_node) => branch_node.node.component),\n\t\t\tpage\n\t\t}\n\t};\n\n\tif (form !== undefined) {\n\t\tresult.props.form = form;\n\t}\n\n\tlet data = {};\n\tlet data_changed = !page;\n\n\tlet p = 0;\n\n\tfor (let i = 0; i < Math.max(branch.length, current.branch.length); i += 1) {\n\t\tconst node = branch[i];\n\t\tconst prev = current.branch[i];\n\n\t\tif (node?.data !== prev?.data) data_changed = true;\n\t\tif (!node) continue;\n\n\t\tdata = { ...data, ...node.data };\n\n\t\t// Only set props if the node actually updated. This prevents needless rerenders.\n\t\tif (data_changed) {\n\t\t\tresult.props[`data_${p}`] = data;\n\t\t}\n\n\t\tp += 1;\n\t}\n\n\tconst page_changed =\n\t\t!current.url ||\n\t\turl.href !== current.url.href ||\n\t\tcurrent.error !== error ||\n\t\t(form !== undefined && form !== page.form) ||\n\t\tdata_changed;\n\n\tif (page_changed) {\n\t\tresult.props.page = {\n\t\t\terror,\n\t\t\tparams,\n\t\t\troute: {\n\t\t\t\tid: route?.id ?? null\n\t\t\t},\n\t\t\tstate: {},\n\t\t\tstatus,\n\t\t\turl: new URL(url),\n\t\t\tform: form ?? null,\n\t\t\t// The whole page store is updated, but this way the object reference stays the same\n\t\t\tdata: data_changed ? data : page.data\n\t\t};\n\t}\n\n\treturn result;\n}\n\n/**\n * Call the load function of the given node, if it exists.\n * If `server_data` is passed, this is treated as the initial run and the page endpoint is not requested.\n *\n * @param {{\n *   loader: import('types').CSRPageNodeLoader;\n * \t parent: () => Promise<Record<string, any>>;\n *   url: URL;\n *   params: Record<string, string>;\n *   route: { id: string | null };\n * \t server_data_node: import('./types.js').DataNode | null;\n * }} options\n * @returns {Promise<import('./types.js').BranchNode>}\n */\nasync function load_node({ loader, parent, url, params, route, server_data_node }) {\n\t/** @type {Record<string, any> | null} */\n\tlet data = null;\n\n\tlet is_tracking = true;\n\n\t/** @type {import('types').Uses} */\n\tconst uses = {\n\t\tdependencies: new Set(),\n\t\tparams: new Set(),\n\t\tparent: false,\n\t\troute: false,\n\t\turl: false,\n\t\tsearch_params: new Set()\n\t};\n\n\tconst node = await loader();\n\n\tif (DEV) {\n\t\tvalidate_page_exports(node.universal);\n\t}\n\n\tif (node.universal?.load) {\n\t\t/** @param {string[]} deps */\n\t\tfunction depends(...deps) {\n\t\t\tfor (const dep of deps) {\n\t\t\t\tif (DEV) validate_depends(/** @type {string} */ (route.id), dep);\n\n\t\t\t\tconst { href } = new URL(dep, url);\n\t\t\t\tuses.dependencies.add(href);\n\t\t\t}\n\t\t}\n\n\t\t/** @type {import('@sveltejs/kit').LoadEvent} */\n\t\tconst load_input = {\n\t\t\troute: new Proxy(route, {\n\t\t\t\tget: (target, key) => {\n\t\t\t\t\tif (is_tracking) {\n\t\t\t\t\t\tuses.route = true;\n\t\t\t\t\t}\n\t\t\t\t\treturn target[/** @type {'id'} */ (key)];\n\t\t\t\t}\n\t\t\t}),\n\t\t\tparams: new Proxy(params, {\n\t\t\t\tget: (target, key) => {\n\t\t\t\t\tif (is_tracking) {\n\t\t\t\t\t\tuses.params.add(/** @type {string} */ (key));\n\t\t\t\t\t}\n\t\t\t\t\treturn target[/** @type {string} */ (key)];\n\t\t\t\t}\n\t\t\t}),\n\t\t\tdata: server_data_node?.data ?? null,\n\t\t\turl: make_trackable(\n\t\t\t\turl,\n\t\t\t\t() => {\n\t\t\t\t\tif (is_tracking) {\n\t\t\t\t\t\tuses.url = true;\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t(param) => {\n\t\t\t\t\tif (is_tracking) {\n\t\t\t\t\t\tuses.search_params.add(param);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t),\n\t\t\tasync fetch(resource, init) {\n\t\t\t\t/** @type {URL | string} */\n\t\t\t\tlet requested;\n\n\t\t\t\tif (resource instanceof Request) {\n\t\t\t\t\trequested = resource.url;\n\n\t\t\t\t\t// we're not allowed to modify the received `Request` object, so in order\n\t\t\t\t\t// to fixup relative urls we create a new equivalent `init` object instead\n\t\t\t\t\tinit = {\n\t\t\t\t\t\t// the request body must be consumed in memory until browsers\n\t\t\t\t\t\t// implement streaming request bodies and/or the body getter\n\t\t\t\t\t\tbody:\n\t\t\t\t\t\t\tresource.method === 'GET' || resource.method === 'HEAD'\n\t\t\t\t\t\t\t\t? undefined\n\t\t\t\t\t\t\t\t: await resource.blob(),\n\t\t\t\t\t\tcache: resource.cache,\n\t\t\t\t\t\tcredentials: resource.credentials,\n\t\t\t\t\t\theaders: resource.headers,\n\t\t\t\t\t\tintegrity: resource.integrity,\n\t\t\t\t\t\tkeepalive: resource.keepalive,\n\t\t\t\t\t\tmethod: resource.method,\n\t\t\t\t\t\tmode: resource.mode,\n\t\t\t\t\t\tredirect: resource.redirect,\n\t\t\t\t\t\treferrer: resource.referrer,\n\t\t\t\t\t\treferrerPolicy: resource.referrerPolicy,\n\t\t\t\t\t\tsignal: resource.signal,\n\t\t\t\t\t\t...init\n\t\t\t\t\t};\n\t\t\t\t} else {\n\t\t\t\t\trequested = resource;\n\t\t\t\t}\n\n\t\t\t\t// we must fixup relative urls so they are resolved from the target page\n\t\t\t\tconst resolved = new URL(requested, url);\n\t\t\t\tif (is_tracking) {\n\t\t\t\t\tdepends(resolved.href);\n\t\t\t\t}\n\n\t\t\t\t// match ssr serialized data url, which is important to find cached responses\n\t\t\t\tif (resolved.origin === url.origin) {\n\t\t\t\t\trequested = resolved.href.slice(url.origin.length);\n\t\t\t\t}\n\n\t\t\t\t// prerendered pages may be served from any origin, so `initial_fetch` urls shouldn't be resolved\n\t\t\t\treturn started\n\t\t\t\t\t? subsequent_fetch(requested, resolved.href, init)\n\t\t\t\t\t: initial_fetch(requested, init);\n\t\t\t},\n\t\t\tsetHeaders: () => {}, // noop\n\t\t\tdepends,\n\t\t\tparent() {\n\t\t\t\tif (is_tracking) {\n\t\t\t\t\tuses.parent = true;\n\t\t\t\t}\n\t\t\t\treturn parent();\n\t\t\t},\n\t\t\tuntrack(fn) {\n\t\t\t\tis_tracking = false;\n\t\t\t\ttry {\n\t\t\t\t\treturn fn();\n\t\t\t\t} finally {\n\t\t\t\t\tis_tracking = true;\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\n\t\tif (DEV) {\n\t\t\ttry {\n\t\t\t\tlock_fetch();\n\t\t\t\tdata = (await node.universal.load.call(null, load_input)) ?? null;\n\t\t\t\tif (data != null && Object.getPrototypeOf(data) !== Object.prototype) {\n\t\t\t\t\tthrow new Error(\n\t\t\t\t\t\t`a load function related to route '${route.id}' returned ${\n\t\t\t\t\t\t\ttypeof data !== 'object'\n\t\t\t\t\t\t\t\t? `a ${typeof data}`\n\t\t\t\t\t\t\t\t: data instanceof Response\n\t\t\t\t\t\t\t\t\t? 'a Response object'\n\t\t\t\t\t\t\t\t\t: Array.isArray(data)\n\t\t\t\t\t\t\t\t\t\t? 'an array'\n\t\t\t\t\t\t\t\t\t\t: 'a non-plain object'\n\t\t\t\t\t\t}, but must return a plain object at the top level (i.e. \\`return {...}\\`)`\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t} finally {\n\t\t\t\tunlock_fetch();\n\t\t\t}\n\t\t} else {\n\t\t\tdata = (await node.universal.load.call(null, load_input)) ?? null;\n\t\t}\n\t}\n\n\treturn {\n\t\tnode,\n\t\tloader,\n\t\tserver: server_data_node,\n\t\tuniversal: node.universal?.load ? { type: 'data', data, uses } : null,\n\t\tdata: data ?? server_data_node?.data ?? null,\n\t\tslash: node.universal?.trailingSlash ?? server_data_node?.slash\n\t};\n}\n\n/**\n * @param {boolean} parent_changed\n * @param {boolean} route_changed\n * @param {boolean} url_changed\n * @param {Set<string>} search_params_changed\n * @param {import('types').Uses | undefined} uses\n * @param {Record<string, string>} params\n */\nfunction has_changed(\n\tparent_changed,\n\troute_changed,\n\turl_changed,\n\tsearch_params_changed,\n\tuses,\n\tparams\n) {\n\tif (force_invalidation) return true;\n\n\tif (!uses) return false;\n\n\tif (uses.parent && parent_changed) return true;\n\tif (uses.route && route_changed) return true;\n\tif (uses.url && url_changed) return true;\n\n\tfor (const tracked_params of uses.search_params) {\n\t\tif (search_params_changed.has(tracked_params)) return true;\n\t}\n\n\tfor (const param of uses.params) {\n\t\tif (params[param] !== current.params[param]) return true;\n\t}\n\n\tfor (const href of uses.dependencies) {\n\t\tif (invalidated.some((fn) => fn(new URL(href)))) return true;\n\t}\n\n\treturn false;\n}\n\n/**\n * @param {import('types').ServerDataNode | import('types').ServerDataSkippedNode | null} node\n * @param {import('./types.js').DataNode | null} [previous]\n * @returns {import('./types.js').DataNode | null}\n */\nfunction create_data_node(node, previous) {\n\tif (node?.type === 'data') return node;\n\tif (node?.type === 'skip') return previous ?? null;\n\treturn null;\n}\n\n/**\n *\n * @param {URL | null} old_url\n * @param {URL} new_url\n */\nfunction diff_search_params(old_url, new_url) {\n\tif (!old_url) return new Set(new_url.searchParams.keys());\n\n\tconst changed = new Set([...old_url.searchParams.keys(), ...new_url.searchParams.keys()]);\n\n\tfor (const key of changed) {\n\t\tconst old_values = old_url.searchParams.getAll(key);\n\t\tconst new_values = new_url.searchParams.getAll(key);\n\n\t\tif (\n\t\t\told_values.every((value) => new_values.includes(value)) &&\n\t\t\tnew_values.every((value) => old_values.includes(value))\n\t\t) {\n\t\t\tchanged.delete(key);\n\t\t}\n\t}\n\n\treturn changed;\n}\n\n/**\n * @param {Omit<import('./types.js').NavigationFinished['state'], 'branch'> & { error: App.Error }} opts\n * @returns {import('./types.js').NavigationFinished}\n */\nfunction preload_error({ error, url, route, params }) {\n\treturn {\n\t\ttype: 'loaded',\n\t\tstate: {\n\t\t\terror,\n\t\t\turl,\n\t\t\troute,\n\t\t\tparams,\n\t\t\tbranch: []\n\t\t},\n\t\tprops: { page, constructors: [] }\n\t};\n}\n\n/**\n * @param {import('./types.js').NavigationIntent & { preload?: {} }} intent\n * @returns {Promise<import('./types.js').NavigationResult>}\n */\nasync function load_route({ id, invalidating, url, params, route, preload }) {\n\tif (load_cache?.id === id) {\n\t\t// the preload becomes the real navigation\n\t\tpreload_tokens.delete(load_cache.token);\n\t\treturn load_cache.promise;\n\t}\n\n\tconst { errors, layouts, leaf } = route;\n\n\tconst loaders = [...layouts, leaf];\n\n\t// preload modules to avoid waterfall, but handle rejections\n\t// so they don't get reported to Sentry et al (we don't need\n\t// to act on the failures at this point)\n\terrors.forEach((loader) => loader?.().catch(() => {}));\n\tloaders.forEach((loader) => loader?.[1]().catch(() => {}));\n\n\t/** @type {import('types').ServerNodesResponse | import('types').ServerRedirectNode | null} */\n\tlet server_data = null;\n\tconst url_changed = current.url ? id !== current.url.pathname + current.url.search : false;\n\tconst route_changed = current.route ? route.id !== current.route.id : false;\n\tconst search_params_changed = diff_search_params(current.url, url);\n\n\tlet parent_invalid = false;\n\tconst invalid_server_nodes = loaders.map((loader, i) => {\n\t\tconst previous = current.branch[i];\n\n\t\tconst invalid =\n\t\t\t!!loader?.[0] &&\n\t\t\t(previous?.loader !== loader[1] ||\n\t\t\t\thas_changed(\n\t\t\t\t\tparent_invalid,\n\t\t\t\t\troute_changed,\n\t\t\t\t\turl_changed,\n\t\t\t\t\tsearch_params_changed,\n\t\t\t\t\tprevious.server?.uses,\n\t\t\t\t\tparams\n\t\t\t\t));\n\n\t\tif (invalid) {\n\t\t\t// For the next one\n\t\t\tparent_invalid = true;\n\t\t}\n\n\t\treturn invalid;\n\t});\n\n\tif (invalid_server_nodes.some(Boolean)) {\n\t\ttry {\n\t\t\tserver_data = await load_data(url, invalid_server_nodes);\n\t\t} catch (error) {\n\t\t\tconst handled_error = await handle_error(error, { url, params, route: { id } });\n\n\t\t\tif (preload_tokens.has(preload)) {\n\t\t\t\treturn preload_error({ error: handled_error, url, params, route });\n\t\t\t}\n\n\t\t\treturn load_root_error_page({\n\t\t\t\tstatus: get_status(error),\n\t\t\t\terror: handled_error,\n\t\t\t\turl,\n\t\t\t\troute\n\t\t\t});\n\t\t}\n\n\t\tif (server_data.type === 'redirect') {\n\t\t\treturn server_data;\n\t\t}\n\t}\n\n\tconst server_data_nodes = server_data?.nodes;\n\n\tlet parent_changed = false;\n\n\tconst branch_promises = loaders.map(async (loader, i) => {\n\t\tif (!loader) return;\n\n\t\t/** @type {import('./types.js').BranchNode | undefined} */\n\t\tconst previous = current.branch[i];\n\n\t\tconst server_data_node = server_data_nodes?.[i];\n\n\t\t// re-use data from previous load if it's still valid\n\t\tconst valid =\n\t\t\t(!server_data_node || server_data_node.type === 'skip') &&\n\t\t\tloader[1] === previous?.loader &&\n\t\t\t!has_changed(\n\t\t\t\tparent_changed,\n\t\t\t\troute_changed,\n\t\t\t\turl_changed,\n\t\t\t\tsearch_params_changed,\n\t\t\t\tprevious.universal?.uses,\n\t\t\t\tparams\n\t\t\t);\n\t\tif (valid) return previous;\n\n\t\tparent_changed = true;\n\n\t\tif (server_data_node?.type === 'error') {\n\t\t\t// rethrow and catch below\n\t\t\tthrow server_data_node;\n\t\t}\n\n\t\treturn load_node({\n\t\t\tloader: loader[1],\n\t\t\turl,\n\t\t\tparams,\n\t\t\troute,\n\t\t\tparent: async () => {\n\t\t\t\tconst data = {};\n\t\t\t\tfor (let j = 0; j < i; j += 1) {\n\t\t\t\t\tObject.assign(data, (await branch_promises[j])?.data);\n\t\t\t\t}\n\t\t\t\treturn data;\n\t\t\t},\n\t\t\tserver_data_node: create_data_node(\n\t\t\t\t// server_data_node is undefined if it wasn't reloaded from the server;\n\t\t\t\t// and if current loader uses server data, we want to reuse previous data.\n\t\t\t\tserver_data_node === undefined && loader[0] ? { type: 'skip' } : server_data_node ?? null,\n\t\t\t\tloader[0] ? previous?.server : undefined\n\t\t\t)\n\t\t});\n\t});\n\n\t// if we don't do this, rejections will be unhandled\n\tfor (const p of branch_promises) p.catch(() => {});\n\n\t/** @type {Array<import('./types.js').BranchNode | undefined>} */\n\tconst branch = [];\n\n\tfor (let i = 0; i < loaders.length; i += 1) {\n\t\tif (loaders[i]) {\n\t\t\ttry {\n\t\t\t\tbranch.push(await branch_promises[i]);\n\t\t\t} catch (err) {\n\t\t\t\tif (err instanceof Redirect) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\ttype: 'redirect',\n\t\t\t\t\t\tlocation: err.location\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\tif (preload_tokens.has(preload)) {\n\t\t\t\t\treturn preload_error({\n\t\t\t\t\t\terror: await handle_error(err, { params, url, route: { id: route.id } }),\n\t\t\t\t\t\turl,\n\t\t\t\t\t\tparams,\n\t\t\t\t\t\troute\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\tlet status = get_status(err);\n\t\t\t\t/** @type {App.Error} */\n\t\t\t\tlet error;\n\n\t\t\t\tif (server_data_nodes?.includes(/** @type {import('types').ServerErrorNode} */ (err))) {\n\t\t\t\t\t// this is the server error rethrown above, reconstruct but don't invoke\n\t\t\t\t\t// the client error handler; it should've already been handled on the server\n\t\t\t\t\tstatus = /** @type {import('types').ServerErrorNode} */ (err).status ?? status;\n\t\t\t\t\terror = /** @type {import('types').ServerErrorNode} */ (err).error;\n\t\t\t\t} else if (err instanceof HttpError) {\n\t\t\t\t\terror = err.body;\n\t\t\t\t} else {\n\t\t\t\t\t// Referenced node could have been removed due to redeploy, check\n\t\t\t\t\tconst updated = await stores.updated.check();\n\t\t\t\t\tif (updated) {\n\t\t\t\t\t\treturn await native_navigation(url);\n\t\t\t\t\t}\n\n\t\t\t\t\terror = await handle_error(err, { params, url, route: { id: route.id } });\n\t\t\t\t}\n\n\t\t\t\tconst error_load = await load_nearest_error_page(i, branch, errors);\n\t\t\t\tif (error_load) {\n\t\t\t\t\treturn get_navigation_result_from_branch({\n\t\t\t\t\t\turl,\n\t\t\t\t\t\tparams,\n\t\t\t\t\t\tbranch: branch.slice(0, error_load.idx).concat(error_load.node),\n\t\t\t\t\t\tstatus,\n\t\t\t\t\t\terror,\n\t\t\t\t\t\troute\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\treturn await server_fallback(url, { id: route.id }, error, status);\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\t// push an empty slot so we can rewind past gaps to the\n\t\t\t// layout that corresponds with an +error.svelte page\n\t\t\tbranch.push(undefined);\n\t\t}\n\t}\n\n\treturn get_navigation_result_from_branch({\n\t\turl,\n\t\tparams,\n\t\tbranch,\n\t\tstatus: 200,\n\t\terror: null,\n\t\troute,\n\t\t// Reset `form` on navigation, but not invalidation\n\t\tform: invalidating ? undefined : null\n\t});\n}\n\n/**\n * @param {number} i Start index to backtrack from\n * @param {Array<import('./types.js').BranchNode | undefined>} branch Branch to backtrack\n * @param {Array<import('types').CSRPageNodeLoader | undefined>} errors All error pages for this branch\n * @returns {Promise<{idx: number; node: import('./types.js').BranchNode} | undefined>}\n */\nasync function load_nearest_error_page(i, branch, errors) {\n\twhile (i--) {\n\t\tif (errors[i]) {\n\t\t\tlet j = i;\n\t\t\twhile (!branch[j]) j -= 1;\n\t\t\ttry {\n\t\t\t\treturn {\n\t\t\t\t\tidx: j + 1,\n\t\t\t\t\tnode: {\n\t\t\t\t\t\tnode: await /** @type {import('types').CSRPageNodeLoader } */ (errors[i])(),\n\t\t\t\t\t\tloader: /** @type {import('types').CSRPageNodeLoader } */ (errors[i]),\n\t\t\t\t\t\tdata: {},\n\t\t\t\t\t\tserver: null,\n\t\t\t\t\t\tuniversal: null\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t} catch {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * @param {{\n *   status: number;\n *   error: App.Error;\n *   url: URL;\n *   route: { id: string | null }\n * }} opts\n * @returns {Promise<import('./types.js').NavigationFinished>}\n */\nasync function load_root_error_page({ status, error, url, route }) {\n\t/** @type {Record<string, string>} */\n\tconst params = {}; // error page does not have params\n\n\t/** @type {import('types').ServerDataNode | null} */\n\tlet server_data_node = null;\n\n\tconst default_layout_has_server_load = app.server_loads[0] === 0;\n\n\tif (default_layout_has_server_load) {\n\t\t// TODO post-https://github.com/sveltejs/kit/discussions/6124 we can use\n\t\t// existing root layout data\n\t\ttry {\n\t\t\tconst server_data = await load_data(url, [true]);\n\n\t\t\tif (\n\t\t\t\tserver_data.type !== 'data' ||\n\t\t\t\t(server_data.nodes[0] && server_data.nodes[0].type !== 'data')\n\t\t\t) {\n\t\t\t\tthrow 0;\n\t\t\t}\n\n\t\t\tserver_data_node = server_data.nodes[0] ?? null;\n\t\t} catch {\n\t\t\t// at this point we have no choice but to fall back to the server, if it wouldn't\n\t\t\t// bring us right back here, turning this into an endless loop\n\t\t\tif (url.origin !== origin || url.pathname !== location.pathname || hydrated) {\n\t\t\t\tawait native_navigation(url);\n\t\t\t}\n\t\t}\n\t}\n\n\tconst root_layout = await load_node({\n\t\tloader: default_layout_loader,\n\t\turl,\n\t\tparams,\n\t\troute,\n\t\tparent: () => Promise.resolve({}),\n\t\tserver_data_node: create_data_node(server_data_node)\n\t});\n\n\t/** @type {import('./types.js').BranchNode} */\n\tconst root_error = {\n\t\tnode: await default_error_loader(),\n\t\tloader: default_error_loader,\n\t\tuniversal: null,\n\t\tserver: null,\n\t\tdata: null\n\t};\n\n\treturn get_navigation_result_from_branch({\n\t\turl,\n\t\tparams,\n\t\tbranch: [root_layout, root_error],\n\t\tstatus,\n\t\terror,\n\t\troute: null\n\t});\n}\n\n/**\n * Resolve the full info (which route, params, etc.) for a client-side navigation from the URL,\n * taking the reroute hook into account. If this isn't a client-side-navigation (or the URL is undefined),\n * returns undefined.\n * @param {URL | undefined} url\n * @param {boolean} invalidating\n */\nfunction get_navigation_intent(url, invalidating) {\n\tif (!url) return undefined;\n\tif (is_external_url(url, base)) return;\n\n\t// reroute could alter the given URL, so we pass a copy\n\tlet rerouted;\n\ttry {\n\t\trerouted = app.hooks.reroute({ url: new URL(url) }) ?? url.pathname;\n\t} catch (e) {\n\t\tif (DEV) {\n\t\t\t// in development, print the error...\n\t\t\tconsole.error(e);\n\n\t\t\t// ...and pause execution, since otherwise we will immediately reload the page\n\t\t\tdebugger; // eslint-disable-line\n\t\t}\n\n\t\t// fall back to native navigation\n\t\treturn undefined;\n\t}\n\n\tconst path = get_url_path(rerouted);\n\n\tfor (const route of routes) {\n\t\tconst params = route.exec(path);\n\n\t\tif (params) {\n\t\t\tconst id = url.pathname + url.search;\n\t\t\t/** @type {import('./types.js').NavigationIntent} */\n\t\t\tconst intent = {\n\t\t\t\tid,\n\t\t\t\tinvalidating,\n\t\t\t\troute,\n\t\t\t\tparams: decode_params(params),\n\t\t\t\turl\n\t\t\t};\n\t\t\treturn intent;\n\t\t}\n\t}\n}\n\n/** @param {string} pathname */\nfunction get_url_path(pathname) {\n\treturn decode_pathname(pathname.slice(base.length) || '/');\n}\n\n/**\n * @param {{\n *   url: URL;\n *   type: import('@sveltejs/kit').Navigation[\"type\"];\n *   intent?: import('./types.js').NavigationIntent;\n *   delta?: number;\n * }} opts\n */\nfunction _before_navigate({ url, type, intent, delta }) {\n\tlet should_block = false;\n\n\tconst nav = create_navigation(current, intent, url, type);\n\n\tif (delta !== undefined) {\n\t\tnav.navigation.delta = delta;\n\t}\n\n\tconst cancellable = {\n\t\t...nav.navigation,\n\t\tcancel: () => {\n\t\t\tshould_block = true;\n\t\t\tnav.reject(new Error('navigation cancelled'));\n\t\t}\n\t};\n\n\tif (!navigating) {\n\t\t// Don't run the event during redirects\n\t\tbefore_navigate_callbacks.forEach((fn) => fn(cancellable));\n\t}\n\n\treturn should_block ? null : nav;\n}\n\n/**\n * @param {{\n *   type: import('@sveltejs/kit').Navigation[\"type\"];\n *   url: URL;\n *   popped?: {\n *     state: Record<string, any>;\n *     scroll: { x: number, y: number };\n *     delta: number;\n *   };\n *   keepfocus?: boolean;\n *   noscroll?: boolean;\n *   replace_state?: boolean;\n *   state?: Record<string, any>;\n *   redirect_count?: number;\n *   nav_token?: {};\n *   accept?: () => void;\n *   block?: () => void;\n * }} opts\n */\nasync function navigate({\n\ttype,\n\turl,\n\tpopped,\n\tkeepfocus,\n\tnoscroll,\n\treplace_state,\n\tstate = {},\n\tredirect_count = 0,\n\tnav_token = {},\n\taccept = noop,\n\tblock = noop\n}) {\n\tconst intent = get_navigation_intent(url, false);\n\tconst nav = _before_navigate({ url, type, delta: popped?.delta, intent });\n\n\tif (!nav) {\n\t\tblock();\n\t\treturn;\n\t}\n\n\t// store this before calling `accept()`, which may change the index\n\tconst previous_history_index = current_history_index;\n\tconst previous_navigation_index = current_navigation_index;\n\n\taccept();\n\n\tnavigating = true;\n\n\tif (started) {\n\t\tstores.navigating.set(nav.navigation);\n\t}\n\n\ttoken = nav_token;\n\tlet navigation_result = intent && (await load_route(intent));\n\n\tif (!navigation_result) {\n\t\tif (is_external_url(url, base)) {\n\t\t\treturn await native_navigation(url);\n\t\t}\n\t\tnavigation_result = await server_fallback(\n\t\t\turl,\n\t\t\t{ id: null },\n\t\t\tawait handle_error(new SvelteKitError(404, 'Not Found', `Not found: ${url.pathname}`), {\n\t\t\t\turl,\n\t\t\t\tparams: {},\n\t\t\t\troute: { id: null }\n\t\t\t}),\n\t\t\t404\n\t\t);\n\t}\n\n\t// if this is an internal navigation intent, use the normalized\n\t// URL for the rest of the function\n\turl = intent?.url || url;\n\n\t// abort if user navigated during update\n\tif (token !== nav_token) {\n\t\tnav.reject(new Error('navigation aborted'));\n\t\treturn false;\n\t}\n\n\tif (navigation_result.type === 'redirect') {\n\t\t// whatwg fetch spec https://fetch.spec.whatwg.org/#http-redirect-fetch says to error after 20 redirects\n\t\tif (redirect_count >= 20) {\n\t\t\tnavigation_result = await load_root_error_page({\n\t\t\t\tstatus: 500,\n\t\t\t\terror: await handle_error(new Error('Redirect loop'), {\n\t\t\t\t\turl,\n\t\t\t\t\tparams: {},\n\t\t\t\t\troute: { id: null }\n\t\t\t\t}),\n\t\t\t\turl,\n\t\t\t\troute: { id: null }\n\t\t\t});\n\t\t} else {\n\t\t\t_goto(new URL(navigation_result.location, url).href, {}, redirect_count + 1, nav_token);\n\t\t\treturn false;\n\t\t}\n\t} else if (/** @type {number} */ (navigation_result.props.page.status) >= 400) {\n\t\tconst updated = await stores.updated.check();\n\t\tif (updated) {\n\t\t\tawait native_navigation(url);\n\t\t}\n\t}\n\n\t// reset invalidation only after a finished navigation. If there are redirects or\n\t// additional invalidations, they should get the same invalidation treatment\n\treset_invalidation();\n\n\tupdating = true;\n\n\tupdate_scroll_positions(previous_history_index);\n\tcapture_snapshot(previous_navigation_index);\n\n\t// ensure the url pathname matches the page's trailing slash option\n\tif (navigation_result.props.page.url.pathname !== url.pathname) {\n\t\turl.pathname = navigation_result.props.page.url.pathname;\n\t}\n\n\tstate = popped ? popped.state : state;\n\n\tif (!popped) {\n\t\t// this is a new navigation, rather than a popstate\n\t\tconst change = replace_state ? 0 : 1;\n\n\t\tconst entry = {\n\t\t\t[HISTORY_INDEX]: (current_history_index += change),\n\t\t\t[NAVIGATION_INDEX]: (current_navigation_index += change),\n\t\t\t[STATES_KEY]: state\n\t\t};\n\n\t\tconst fn = replace_state ? history.replaceState : history.pushState;\n\t\tfn.call(history, entry, '', url);\n\n\t\tif (!replace_state) {\n\t\t\tclear_onward_history(current_history_index, current_navigation_index);\n\t\t}\n\t}\n\n\t// reset preload synchronously after the history state has been set to avoid race conditions\n\tload_cache = null;\n\n\tnavigation_result.props.page.state = state;\n\n\tif (started) {\n\t\tcurrent = navigation_result.state;\n\n\t\t// reset url before updating page store\n\t\tif (navigation_result.props.page) {\n\t\t\tnavigation_result.props.page.url = url;\n\t\t}\n\n\t\tconst after_navigate = (\n\t\t\tawait Promise.all(\n\t\t\t\ton_navigate_callbacks.map((fn) =>\n\t\t\t\t\tfn(/** @type {import('@sveltejs/kit').OnNavigate} */ (nav.navigation))\n\t\t\t\t)\n\t\t\t)\n\t\t).filter(/** @returns {value is () => void} */ (value) => typeof value === 'function');\n\n\t\tif (after_navigate.length > 0) {\n\t\t\tfunction cleanup() {\n\t\t\t\tafter_navigate_callbacks = after_navigate_callbacks.filter(\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t(fn) => !after_navigate.includes(fn)\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tafter_navigate.push(cleanup);\n\t\t\tafter_navigate_callbacks.push(...after_navigate);\n\t\t}\n\n\t\troot.$set(navigation_result.props);\n\t\thas_navigated = true;\n\t} else {\n\t\tinitialize(navigation_result, target, false);\n\t}\n\n\tconst { activeElement } = document;\n\n\t// need to render the DOM before we can scroll to the rendered elements and do focus management\n\tawait tick();\n\n\t// we reset scroll before dealing with focus, to avoid a flash of unscrolled content\n\tconst scroll = popped ? popped.scroll : noscroll ? scroll_state() : null;\n\n\tif (autoscroll) {\n\t\tconst deep_linked = url.hash && document.getElementById(decodeURIComponent(url.hash.slice(1)));\n\t\tif (scroll) {\n\t\t\tscrollTo(scroll.x, scroll.y);\n\t\t} else if (deep_linked) {\n\t\t\t// Here we use `scrollIntoView` on the element instead of `scrollTo`\n\t\t\t// because it natively supports the `scroll-margin` and `scroll-behavior`\n\t\t\t// CSS properties.\n\t\t\tdeep_linked.scrollIntoView();\n\t\t} else {\n\t\t\tscrollTo(0, 0);\n\t\t}\n\t}\n\n\tconst changed_focus =\n\t\t// reset focus only if any manual focus management didn't override it\n\t\tdocument.activeElement !== activeElement &&\n\t\t// also refocus when activeElement is body already because the\n\t\t// focus event might not have been fired on it yet\n\t\tdocument.activeElement !== document.body;\n\n\tif (!keepfocus && !changed_focus) {\n\t\treset_focus();\n\t}\n\n\tautoscroll = true;\n\n\tif (navigation_result.props.page) {\n\t\tpage = navigation_result.props.page;\n\t}\n\n\tnavigating = false;\n\n\tif (type === 'popstate') {\n\t\trestore_snapshot(current_navigation_index);\n\t}\n\n\tnav.fulfil(undefined);\n\n\tafter_navigate_callbacks.forEach((fn) =>\n\t\tfn(/** @type {import('@sveltejs/kit').AfterNavigate} */ (nav.navigation))\n\t);\n\n\tstores.navigating.set(null);\n\n\tupdating = false;\n}\n\n/**\n * Does a full page reload if it wouldn't result in an endless loop in the SPA case\n * @param {URL} url\n * @param {{ id: string | null }} route\n * @param {App.Error} error\n * @param {number} status\n * @returns {Promise<import('./types.js').NavigationFinished>}\n */\nasync function server_fallback(url, route, error, status) {\n\tif (url.origin === origin && url.pathname === location.pathname && !hydrated) {\n\t\t// We would reload the same page we're currently on, which isn't hydrated,\n\t\t// which means no SSR, which means we would end up in an endless loop\n\t\treturn await load_root_error_page({\n\t\t\tstatus,\n\t\t\terror,\n\t\t\turl,\n\t\t\troute\n\t\t});\n\t}\n\n\tif (DEV && status !== 404) {\n\t\tconsole.error(\n\t\t\t'An error occurred while loading the page. This will cause a full page reload. (This message will only appear during development.)'\n\t\t);\n\n\t\tdebugger; // eslint-disable-line\n\t}\n\n\treturn await native_navigation(url);\n}\n\nif (import.meta.hot) {\n\timport.meta.hot.on('vite:beforeUpdate', () => {\n\t\tif (current.error) location.reload();\n\t});\n}\n\nfunction setup_preload() {\n\t/** @type {NodeJS.Timeout} */\n\tlet mousemove_timeout;\n\n\tcontainer.addEventListener('mousemove', (event) => {\n\t\tconst target = /** @type {Element} */ (event.target);\n\n\t\tclearTimeout(mousemove_timeout);\n\t\tmousemove_timeout = setTimeout(() => {\n\t\t\tpreload(target, 2);\n\t\t}, 20);\n\t});\n\n\t/** @param {Event} event */\n\tfunction tap(event) {\n\t\tpreload(/** @type {Element} */ (event.composedPath()[0]), 1);\n\t}\n\n\tcontainer.addEventListener('mousedown', tap);\n\tcontainer.addEventListener('touchstart', tap, { passive: true });\n\n\tconst observer = new IntersectionObserver(\n\t\t(entries) => {\n\t\t\tfor (const entry of entries) {\n\t\t\t\tif (entry.isIntersecting) {\n\t\t\t\t\t_preload_code(/** @type {HTMLAnchorElement} */ (entry.target).href);\n\t\t\t\t\tobserver.unobserve(entry.target);\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t{ threshold: 0 }\n\t);\n\n\t/**\n\t * @param {Element} element\n\t * @param {number} priority\n\t */\n\tfunction preload(element, priority) {\n\t\tconst a = find_anchor(element, container);\n\t\tif (!a) return;\n\n\t\tconst { url, external, download } = get_link_info(a, base);\n\t\tif (external || download) return;\n\n\t\tconst options = get_router_options(a);\n\n\t\tif (!options.reload) {\n\t\t\tif (priority <= options.preload_data) {\n\t\t\t\tconst intent = get_navigation_intent(url, false);\n\t\t\t\tif (intent) {\n\t\t\t\t\tif (DEV) {\n\t\t\t\t\t\t_preload_data(intent).then((result) => {\n\t\t\t\t\t\t\tif (result.type === 'loaded' && result.state.error) {\n\t\t\t\t\t\t\t\tconsole.warn(\n\t\t\t\t\t\t\t\t\t`Preloading data for ${intent.url.pathname} failed with the following error: ${result.state.error.message}\\n` +\n\t\t\t\t\t\t\t\t\t\t'If this error is transient, you can ignore it. Otherwise, consider disabling preloading for this route. ' +\n\t\t\t\t\t\t\t\t\t\t'This route was preloaded due to a data-sveltekit-preload-data attribute. ' +\n\t\t\t\t\t\t\t\t\t\t'See https://kit.svelte.dev/docs/link-options for more info'\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\t_preload_data(intent);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else if (priority <= options.preload_code) {\n\t\t\t\t_preload_code(/** @type {URL} */ (url).pathname);\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction after_navigate() {\n\t\tobserver.disconnect();\n\n\t\tfor (const a of container.querySelectorAll('a')) {\n\t\t\tconst { url, external, download } = get_link_info(a, base);\n\t\t\tif (external || download) continue;\n\n\t\t\tconst options = get_router_options(a);\n\t\t\tif (options.reload) continue;\n\n\t\t\tif (options.preload_code === PRELOAD_PRIORITIES.viewport) {\n\t\t\t\tobserver.observe(a);\n\t\t\t}\n\n\t\t\tif (options.preload_code === PRELOAD_PRIORITIES.eager) {\n\t\t\t\t_preload_code(/** @type {URL} */ (url).pathname);\n\t\t\t}\n\t\t}\n\t}\n\n\tafter_navigate_callbacks.push(after_navigate);\n\tafter_navigate();\n}\n\n/**\n * @param {unknown} error\n * @param {import('@sveltejs/kit').NavigationEvent} event\n * @returns {import('types').MaybePromise<App.Error>}\n */\nfunction handle_error(error, event) {\n\tif (error instanceof HttpError) {\n\t\treturn error.body;\n\t}\n\n\tif (DEV) {\n\t\terrored = true;\n\t\tconsole.warn('The next HMR update will cause the page to reload');\n\t}\n\n\tconst status = get_status(error);\n\tconst message = get_message(error);\n\n\treturn (\n\t\tapp.hooks.handleError({ error, event, status, message }) ?? /** @type {any} */ ({ message })\n\t);\n}\n\n/**\n * @template {Function} T\n * @param {T[]} callbacks\n * @param {T} callback\n */\nfunction add_navigation_callback(callbacks, callback) {\n\tonMount(() => {\n\t\tcallbacks.push(callback);\n\n\t\treturn () => {\n\t\t\tconst i = callbacks.indexOf(callback);\n\t\t\tcallbacks.splice(i, 1);\n\t\t};\n\t});\n}\n\n/**\n * A lifecycle function that runs the supplied `callback` when the current component mounts, and also whenever we navigate to a new URL.\n *\n * `afterNavigate` must be called during a component initialization. It remains active as long as the component is mounted.\n * @param {(navigation: import('@sveltejs/kit').AfterNavigate) => void} callback\n * @returns {void}\n */\nexport function afterNavigate(callback) {\n\tadd_navigation_callback(after_navigate_callbacks, callback);\n}\n\n/**\n * A navigation interceptor that triggers before we navigate to a new URL, whether by clicking a link, calling `goto(...)`, or using the browser back/forward controls.\n *\n * Calling `cancel()` will prevent the navigation from completing. If `navigation.type === 'leave'` — meaning the user is navigating away from the app (or closing the tab) — calling `cancel` will trigger the native browser unload confirmation dialog. In this case, the navigation may or may not be cancelled depending on the user's response.\n *\n * When a navigation isn't to a SvelteKit-owned route (and therefore controlled by SvelteKit's client-side router), `navigation.to.route.id` will be `null`.\n *\n * If the navigation will (if not cancelled) cause the document to unload — in other words `'leave'` navigations and `'link'` navigations where `navigation.to.route === null` — `navigation.willUnload` is `true`.\n *\n * `beforeNavigate` must be called during a component initialization. It remains active as long as the component is mounted.\n * @param {(navigation: import('@sveltejs/kit').BeforeNavigate) => void} callback\n * @returns {void}\n */\nexport function beforeNavigate(callback) {\n\tadd_navigation_callback(before_navigate_callbacks, callback);\n}\n\n/**\n * A lifecycle function that runs the supplied `callback` immediately before we navigate to a new URL except during full-page navigations.\n *\n * If you return a `Promise`, SvelteKit will wait for it to resolve before completing the navigation. This allows you to — for example — use `document.startViewTransition`. Avoid promises that are slow to resolve, since navigation will appear stalled to the user.\n *\n * If a function (or a `Promise` that resolves to a function) is returned from the callback, it will be called once the DOM has updated.\n *\n * `onNavigate` must be called during a component initialization. It remains active as long as the component is mounted.\n * @param {(navigation: import('@sveltejs/kit').OnNavigate) => import('types').MaybePromise<(() => void) | void>} callback\n * @returns {void}\n */\nexport function onNavigate(callback) {\n\tadd_navigation_callback(on_navigate_callbacks, callback);\n}\n\n/**\n * If called when the page is being updated following a navigation (in `onMount` or `afterNavigate` or an action, for example), this disables SvelteKit's built-in scroll handling.\n * This is generally discouraged, since it breaks user expectations.\n * @returns {void}\n */\nexport function disableScrollHandling() {\n\tif (!BROWSER) {\n\t\tthrow new Error('Cannot call disableScrollHandling() on the server');\n\t}\n\n\tif (DEV && started && !updating) {\n\t\tthrow new Error('Can only disable scroll handling during navigation');\n\t}\n\n\tif (updating || !started) {\n\t\tautoscroll = false;\n\t}\n}\n\n/**\n * Returns a Promise that resolves when SvelteKit navigates (or fails to navigate, in which case the promise rejects) to the specified `url`.\n * For external URLs, use `window.location = url` instead of calling `goto(url)`.\n *\n * @param {string | URL} url Where to navigate to. Note that if you've set [`config.kit.paths.base`](https://kit.svelte.dev/docs/configuration#paths) and the URL is root-relative, you need to prepend the base path if you want to navigate within the app.\n * @param {Object} [opts] Options related to the navigation\n * @param {boolean} [opts.replaceState] If `true`, will replace the current `history` entry rather than creating a new one with `pushState`\n * @param {boolean} [opts.noScroll] If `true`, the browser will maintain its scroll position rather than scrolling to the top of the page after navigation\n * @param {boolean} [opts.keepFocus] If `true`, the currently focused element will retain focus after navigation. Otherwise, focus will be reset to the body\n * @param {boolean} [opts.invalidateAll] If `true`, all `load` functions of the page will be rerun. See https://kit.svelte.dev/docs/load#rerunning-load-functions for more info on invalidation.\n * @param {App.PageState} [opts.state] An optional object that will be available on the `$page.state` store\n * @returns {Promise<void>}\n */\nexport function goto(url, opts = {}) {\n\tif (!BROWSER) {\n\t\tthrow new Error('Cannot call goto(...) on the server');\n\t}\n\n\turl = resolve_url(url);\n\n\tif (url.origin !== origin) {\n\t\treturn Promise.reject(\n\t\t\tnew Error(\n\t\t\t\tDEV\n\t\t\t\t\t? `Cannot use \\`goto\\` with an external URL. Use \\`window.location = \"${url}\"\\` instead`\n\t\t\t\t\t: 'goto: invalid URL'\n\t\t\t)\n\t\t);\n\t}\n\n\treturn _goto(url, opts, 0);\n}\n\n/**\n * Causes any `load` functions belonging to the currently active page to re-run if they depend on the `url` in question, via `fetch` or `depends`. Returns a `Promise` that resolves when the page is subsequently updated.\n *\n * If the argument is given as a `string` or `URL`, it must resolve to the same URL that was passed to `fetch` or `depends` (including query parameters).\n * To create a custom identifier, use a string beginning with `[a-z]+:` (e.g. `custom:state`) — this is a valid URL.\n *\n * The `function` argument can be used define a custom predicate. It receives the full `URL` and causes `load` to rerun if `true` is returned.\n * This can be useful if you want to invalidate based on a pattern instead of a exact match.\n *\n * ```ts\n * // Example: Match '/path' regardless of the query parameters\n * import { invalidate } from '$app/navigation';\n *\n * invalidate((url) => url.pathname === '/path');\n * ```\n * @param {string | URL | ((url: URL) => boolean)} resource The invalidated URL\n * @returns {Promise<void>}\n */\nexport function invalidate(resource) {\n\tif (!BROWSER) {\n\t\tthrow new Error('Cannot call invalidate(...) on the server');\n\t}\n\n\tif (typeof resource === 'function') {\n\t\tinvalidated.push(resource);\n\t} else {\n\t\tconst { href } = new URL(resource, location.href);\n\t\tinvalidated.push((url) => url.href === href);\n\t}\n\n\treturn _invalidate();\n}\n\n/**\n * Causes all `load` functions belonging to the currently active page to re-run. Returns a `Promise` that resolves when the page is subsequently updated.\n * @returns {Promise<void>}\n */\nexport function invalidateAll() {\n\tif (!BROWSER) {\n\t\tthrow new Error('Cannot call invalidateAll() on the server');\n\t}\n\n\tforce_invalidation = true;\n\treturn _invalidate();\n}\n\n/**\n * Programmatically preloads the given page, which means\n *  1. ensuring that the code for the page is loaded, and\n *  2. calling the page's load function with the appropriate options.\n *\n * This is the same behaviour that SvelteKit triggers when the user taps or mouses over an `<a>` element with `data-sveltekit-preload-data`.\n * If the next navigation is to `href`, the values returned from load will be used, making navigation instantaneous.\n * Returns a Promise that resolves with the result of running the new route's `load` functions once the preload is complete.\n *\n * @param {string} href Page to preload\n * @returns {Promise<{ type: 'loaded'; status: number; data: Record<string, any> } | { type: 'redirect'; location: string }>}\n */\nexport async function preloadData(href) {\n\tif (!BROWSER) {\n\t\tthrow new Error('Cannot call preloadData(...) on the server');\n\t}\n\n\tconst url = resolve_url(href);\n\tconst intent = get_navigation_intent(url, false);\n\n\tif (!intent) {\n\t\tthrow new Error(`Attempted to preload a URL that does not belong to this app: ${url}`);\n\t}\n\n\tconst result = await _preload_data(intent);\n\tif (result.type === 'redirect') {\n\t\treturn {\n\t\t\ttype: result.type,\n\t\t\tlocation: result.location\n\t\t};\n\t}\n\n\tconst { status, data } = result.props.page ?? page;\n\treturn { type: result.type, status, data };\n}\n\n/**\n * Programmatically imports the code for routes that haven't yet been fetched.\n * Typically, you might call this to speed up subsequent navigation.\n *\n * You can specify routes by any matching pathname such as `/about` (to match `src/routes/about/+page.svelte`) or `/blog/*` (to match `src/routes/blog/[slug]/+page.svelte`).\n *\n * Unlike `preloadData`, this won't call `load` functions.\n * Returns a Promise that resolves when the modules have been imported.\n *\n * @param {string} pathname\n * @returns {Promise<void>}\n */\nexport function preloadCode(pathname) {\n\tif (!BROWSER) {\n\t\tthrow new Error('Cannot call preloadCode(...) on the server');\n\t}\n\n\tif (DEV) {\n\t\tif (!pathname.startsWith(base)) {\n\t\t\tthrow new Error(\n\t\t\t\t`pathnames passed to preloadCode must start with \\`paths.base\\` (i.e. \"${base}${pathname}\" rather than \"${pathname}\")`\n\t\t\t);\n\t\t}\n\n\t\tif (!routes.find((route) => route.exec(get_url_path(pathname)))) {\n\t\t\tthrow new Error(`'${pathname}' did not match any routes`);\n\t\t}\n\t}\n\n\treturn _preload_code(pathname);\n}\n\n/**\n * Programmatically create a new history entry with the given `$page.state`. To use the current URL, you can pass `''` as the first argument. Used for [shallow routing](https://kit.svelte.dev/docs/shallow-routing).\n *\n * @param {string | URL} url\n * @param {App.PageState} state\n * @returns {void}\n */\nexport function pushState(url, state) {\n\tif (!BROWSER) {\n\t\tthrow new Error('Cannot call pushState(...) on the server');\n\t}\n\n\tif (DEV) {\n\t\ttry {\n\t\t\t// use `devalue.stringify` as a convenient way to ensure we exclude values that can't be properly rehydrated, such as custom class instances\n\t\t\tdevalue.stringify(state);\n\t\t} catch (error) {\n\t\t\t// @ts-expect-error\n\t\t\tthrow new Error(`Could not serialize state${error.path}`);\n\t\t}\n\t}\n\n\tupdate_scroll_positions(current_history_index);\n\n\tconst opts = {\n\t\t[HISTORY_INDEX]: (current_history_index += 1),\n\t\t[NAVIGATION_INDEX]: current_navigation_index,\n\t\t[PAGE_URL_KEY]: page.url.href,\n\t\t[STATES_KEY]: state\n\t};\n\n\thistory.pushState(opts, '', resolve_url(url));\n\thas_navigated = true;\n\n\tpage = { ...page, state };\n\troot.$set({ page });\n\n\tclear_onward_history(current_history_index, current_navigation_index);\n}\n\n/**\n * Programmatically replace the current history entry with the given `$page.state`. To use the current URL, you can pass `''` as the first argument. Used for [shallow routing](https://kit.svelte.dev/docs/shallow-routing).\n *\n * @param {string | URL} url\n * @param {App.PageState} state\n * @returns {void}\n */\nexport function replaceState(url, state) {\n\tif (!BROWSER) {\n\t\tthrow new Error('Cannot call replaceState(...) on the server');\n\t}\n\n\tif (DEV) {\n\t\ttry {\n\t\t\t// use `devalue.stringify` as a convenient way to ensure we exclude values that can't be properly rehydrated, such as custom class instances\n\t\t\tdevalue.stringify(state);\n\t\t} catch (error) {\n\t\t\t// @ts-expect-error\n\t\t\tthrow new Error(`Could not serialize state${error.path}`);\n\t\t}\n\t}\n\n\tconst opts = {\n\t\t[HISTORY_INDEX]: current_history_index,\n\t\t[NAVIGATION_INDEX]: current_navigation_index,\n\t\t[PAGE_URL_KEY]: page.url.href,\n\t\t[STATES_KEY]: state\n\t};\n\n\thistory.replaceState(opts, '', resolve_url(url));\n\n\tpage = { ...page, state };\n\troot.$set({ page });\n}\n\n/**\n * This action updates the `form` property of the current page with the given data and updates `$page.status`.\n * In case of an error, it redirects to the nearest error page.\n * @template {Record<string, unknown> | undefined} Success\n * @template {Record<string, unknown> | undefined} Failure\n * @param {import('@sveltejs/kit').ActionResult<Success, Failure>} result\n * @returns {Promise<void>}\n */\nexport async function applyAction(result) {\n\tif (!BROWSER) {\n\t\tthrow new Error('Cannot call applyAction(...) on the server');\n\t}\n\n\tif (result.type === 'error') {\n\t\tconst url = new URL(location.href);\n\n\t\tconst { branch, route } = current;\n\t\tif (!route) return;\n\n\t\tconst error_load = await load_nearest_error_page(current.branch.length, branch, route.errors);\n\t\tif (error_load) {\n\t\t\tconst navigation_result = get_navigation_result_from_branch({\n\t\t\t\turl,\n\t\t\t\tparams: current.params,\n\t\t\t\tbranch: branch.slice(0, error_load.idx).concat(error_load.node),\n\t\t\t\tstatus: result.status ?? 500,\n\t\t\t\terror: result.error,\n\t\t\t\troute\n\t\t\t});\n\n\t\t\tcurrent = navigation_result.state;\n\n\t\t\troot.$set(navigation_result.props);\n\n\t\t\ttick().then(reset_focus);\n\t\t}\n\t} else if (result.type === 'redirect') {\n\t\t_goto(result.location, { invalidateAll: true }, 0);\n\t} else {\n\t\t/** @type {Record<string, any>} */\n\t\troot.$set({\n\t\t\t// this brings Svelte's view of the world in line with SvelteKit's\n\t\t\t// after use:enhance reset the form....\n\t\t\tform: null,\n\t\t\tpage: { ...page, form: result.data, status: result.status }\n\t\t});\n\n\t\t// ...so that setting the `form` prop takes effect and isn't ignored\n\t\tawait tick();\n\t\troot.$set({ form: result.data });\n\n\t\tif (result.type === 'success') {\n\t\t\treset_focus();\n\t\t}\n\t}\n}\n\nfunction _start_router() {\n\thistory.scrollRestoration = 'manual';\n\n\t// Adopted from Nuxt.js\n\t// Reset scrollRestoration to auto when leaving page, allowing page reload\n\t// and back-navigation from other pages to use the browser to restore the\n\t// scrolling position.\n\taddEventListener('beforeunload', (e) => {\n\t\tlet should_block = false;\n\n\t\tpersist_state();\n\n\t\tif (!navigating) {\n\t\t\tconst nav = create_navigation(current, undefined, null, 'leave');\n\n\t\t\t// If we're navigating, beforeNavigate was already called. If we end up in here during navigation,\n\t\t\t// it's due to an external or full-page-reload link, for which we don't want to call the hook again.\n\t\t\t/** @type {import('@sveltejs/kit').BeforeNavigate} */\n\t\t\tconst navigation = {\n\t\t\t\t...nav.navigation,\n\t\t\t\tcancel: () => {\n\t\t\t\t\tshould_block = true;\n\t\t\t\t\tnav.reject(new Error('navigation cancelled'));\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tbefore_navigate_callbacks.forEach((fn) => fn(navigation));\n\t\t}\n\n\t\tif (should_block) {\n\t\t\te.preventDefault();\n\t\t\te.returnValue = '';\n\t\t} else {\n\t\t\thistory.scrollRestoration = 'auto';\n\t\t}\n\t});\n\n\taddEventListener('visibilitychange', () => {\n\t\tif (document.visibilityState === 'hidden') {\n\t\t\tpersist_state();\n\t\t}\n\t});\n\n\t// @ts-expect-error this isn't supported everywhere yet\n\tif (!navigator.connection?.saveData) {\n\t\tsetup_preload();\n\t}\n\n\t/** @param {MouseEvent} event */\n\tcontainer.addEventListener('click', async (event) => {\n\t\t// Adapted from https://github.com/visionmedia/page.js\n\t\t// MIT license https://github.com/visionmedia/page.js#license\n\t\tif (event.button || event.which !== 1) return;\n\t\tif (event.metaKey || event.ctrlKey || event.shiftKey || event.altKey) return;\n\t\tif (event.defaultPrevented) return;\n\n\t\tconst a = find_anchor(/** @type {Element} */ (event.composedPath()[0]), container);\n\t\tif (!a) return;\n\n\t\tconst { url, external, target, download } = get_link_info(a, base);\n\t\tif (!url) return;\n\n\t\t// bail out before `beforeNavigate` if link opens in a different tab\n\t\tif (target === '_parent' || target === '_top') {\n\t\t\tif (window.parent !== window) return;\n\t\t} else if (target && target !== '_self') {\n\t\t\treturn;\n\t\t}\n\n\t\tconst options = get_router_options(a);\n\t\tconst is_svg_a_element = a instanceof SVGAElement;\n\n\t\t// Ignore URL protocols that differ to the current one and are not http(s) (e.g. `mailto:`, `tel:`, `myapp:`, etc.)\n\t\t// This may be wrong when the protocol is x: and the link goes to y:.. which should be treated as an external\n\t\t// navigation, but it's not clear how to handle that case and it's not likely to come up in practice.\n\t\t// MEMO: Without this condition, firefox will open mailer twice.\n\t\t// See:\n\t\t// - https://github.com/sveltejs/kit/issues/4045\n\t\t// - https://github.com/sveltejs/kit/issues/5725\n\t\t// - https://github.com/sveltejs/kit/issues/6496\n\t\tif (\n\t\t\t!is_svg_a_element &&\n\t\t\turl.protocol !== location.protocol &&\n\t\t\t!(url.protocol === 'https:' || url.protocol === 'http:')\n\t\t)\n\t\t\treturn;\n\n\t\tif (download) return;\n\n\t\t// Ignore the following but fire beforeNavigate\n\t\tif (external || options.reload) {\n\t\t\tif (_before_navigate({ url, type: 'link' })) {\n\t\t\t\t// set `navigating` to `true` to prevent `beforeNavigate` callbacks\n\t\t\t\t// being called when the page unloads\n\t\t\t\tnavigating = true;\n\t\t\t} else {\n\t\t\t\tevent.preventDefault();\n\t\t\t}\n\n\t\t\treturn;\n\t\t}\n\n\t\t// Check if new url only differs by hash and use the browser default behavior in that case\n\t\t// This will ensure the `hashchange` event is fired\n\t\t// Removing the hash does a full page navigation in the browser, so make sure a hash is present\n\t\tconst [nonhash, hash] = url.href.split('#');\n\t\tif (hash !== undefined && nonhash === strip_hash(location)) {\n\t\t\t// If we are trying to navigate to the same hash, we should only\n\t\t\t// attempt to scroll to that element and avoid any history changes.\n\t\t\t// Otherwise, this can cause Firefox to incorrectly assign a null\n\t\t\t// history state value without any signal that we can detect.\n\t\t\tconst [, current_hash] = current.url.href.split('#');\n\t\t\tif (current_hash === hash) {\n\t\t\t\tevent.preventDefault();\n\n\t\t\t\t// We're already on /# and click on a link that goes to /#, or we're on\n\t\t\t\t// /#top and click on a link that goes to /#top. In those cases just go to\n\t\t\t\t// the top of the page, and avoid a history change.\n\t\t\t\tif (hash === '' || (hash === 'top' && a.ownerDocument.getElementById('top') === null)) {\n\t\t\t\t\twindow.scrollTo({ top: 0 });\n\t\t\t\t} else {\n\t\t\t\t\ta.ownerDocument.getElementById(hash)?.scrollIntoView();\n\t\t\t\t}\n\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t// set this flag to distinguish between navigations triggered by\n\t\t\t// clicking a hash link and those triggered by popstate\n\t\t\thash_navigating = true;\n\n\t\t\tupdate_scroll_positions(current_history_index);\n\n\t\t\tupdate_url(url);\n\n\t\t\tif (!options.replace_state) return;\n\n\t\t\t// hashchange event shouldn't occur if the router is replacing state.\n\t\t\thash_navigating = false;\n\t\t}\n\n\t\tevent.preventDefault();\n\n\t\t// allow the browser to repaint before navigating —\n\t\t// this prevents INP scores being penalised\n\t\tawait new Promise((fulfil) => {\n\t\t\trequestAnimationFrame(() => {\n\t\t\t\tsetTimeout(fulfil, 0);\n\t\t\t});\n\n\t\t\tsetTimeout(fulfil, 100); // fallback for edge case where rAF doesn't fire because e.g. tab was backgrounded\n\t\t});\n\n\t\tnavigate({\n\t\t\ttype: 'link',\n\t\t\turl,\n\t\t\tkeepfocus: options.keepfocus,\n\t\t\tnoscroll: options.noscroll,\n\t\t\treplace_state: options.replace_state ?? url.href === location.href\n\t\t});\n\t});\n\n\tcontainer.addEventListener('submit', (event) => {\n\t\tif (event.defaultPrevented) return;\n\n\t\tconst form = /** @type {HTMLFormElement} */ (\n\t\t\tHTMLFormElement.prototype.cloneNode.call(event.target)\n\t\t);\n\n\t\tconst submitter = /** @type {HTMLButtonElement | HTMLInputElement | null} */ (event.submitter);\n\n\t\tconst method = submitter?.formMethod || form.method;\n\n\t\tif (method !== 'get') return;\n\n\t\tconst url = new URL(\n\t\t\t(submitter?.hasAttribute('formaction') && submitter?.formAction) || form.action\n\t\t);\n\n\t\tif (is_external_url(url, base)) return;\n\n\t\tconst event_form = /** @type {HTMLFormElement} */ (event.target);\n\n\t\tconst options = get_router_options(event_form);\n\t\tif (options.reload) return;\n\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\n\t\tconst data = new FormData(event_form);\n\n\t\tconst submitter_name = submitter?.getAttribute('name');\n\t\tif (submitter_name) {\n\t\t\tdata.append(submitter_name, submitter?.getAttribute('value') ?? '');\n\t\t}\n\n\t\t// @ts-expect-error `URLSearchParams(fd)` is kosher, but typescript doesn't know that\n\t\turl.search = new URLSearchParams(data).toString();\n\n\t\tnavigate({\n\t\t\ttype: 'form',\n\t\t\turl,\n\t\t\tkeepfocus: options.keepfocus,\n\t\t\tnoscroll: options.noscroll,\n\t\t\treplace_state: options.replace_state ?? url.href === location.href\n\t\t});\n\t});\n\n\taddEventListener('popstate', async (event) => {\n\t\tif (event.state?.[HISTORY_INDEX]) {\n\t\t\tconst history_index = event.state[HISTORY_INDEX];\n\t\t\ttoken = {};\n\n\t\t\t// if a popstate-driven navigation is cancelled, we need to counteract it\n\t\t\t// with history.go, which means we end up back here, hence this check\n\t\t\tif (history_index === current_history_index) return;\n\n\t\t\tconst scroll = scroll_positions[history_index];\n\t\t\tconst state = event.state[STATES_KEY] ?? {};\n\t\t\tconst url = new URL(event.state[PAGE_URL_KEY] ?? location.href);\n\t\t\tconst navigation_index = event.state[NAVIGATION_INDEX];\n\t\t\tconst is_hash_change = strip_hash(location) === strip_hash(current.url);\n\t\t\tconst shallow =\n\t\t\t\tnavigation_index === current_navigation_index && (has_navigated || is_hash_change);\n\n\t\t\tif (shallow) {\n\t\t\t\t// We don't need to navigate, we just need to update scroll and/or state.\n\t\t\t\t// This happens with hash links and `pushState`/`replaceState`. The\n\t\t\t\t// exception is if we haven't navigated yet, since we could have\n\t\t\t\t// got here after a modal navigation then a reload\n\t\t\t\tupdate_url(url);\n\n\t\t\t\tscroll_positions[current_history_index] = scroll_state();\n\t\t\t\tif (scroll) scrollTo(scroll.x, scroll.y);\n\n\t\t\t\tif (state !== page.state) {\n\t\t\t\t\tpage = { ...page, state };\n\t\t\t\t\troot.$set({ page });\n\t\t\t\t}\n\n\t\t\t\tcurrent_history_index = history_index;\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst delta = history_index - current_history_index;\n\n\t\t\tawait navigate({\n\t\t\t\ttype: 'popstate',\n\t\t\t\turl,\n\t\t\t\tpopped: {\n\t\t\t\t\tstate,\n\t\t\t\t\tscroll,\n\t\t\t\t\tdelta\n\t\t\t\t},\n\t\t\t\taccept: () => {\n\t\t\t\t\tcurrent_history_index = history_index;\n\t\t\t\t\tcurrent_navigation_index = navigation_index;\n\t\t\t\t},\n\t\t\t\tblock: () => {\n\t\t\t\t\thistory.go(-delta);\n\t\t\t\t},\n\t\t\t\tnav_token: token\n\t\t\t});\n\t\t} else {\n\t\t\t// since popstate event is also emitted when an anchor referencing the same\n\t\t\t// document is clicked, we have to check that the router isn't already handling\n\t\t\t// the navigation. otherwise we would be updating the page store twice.\n\t\t\tif (!hash_navigating) {\n\t\t\t\tconst url = new URL(location.href);\n\t\t\t\tupdate_url(url);\n\t\t\t}\n\t\t}\n\t});\n\n\taddEventListener('hashchange', () => {\n\t\t// if the hashchange happened as a result of clicking on a link,\n\t\t// we need to update history, otherwise we have to leave it alone\n\t\tif (hash_navigating) {\n\t\t\thash_navigating = false;\n\t\t\thistory.replaceState(\n\t\t\t\t{\n\t\t\t\t\t...history.state,\n\t\t\t\t\t[HISTORY_INDEX]: ++current_history_index,\n\t\t\t\t\t[NAVIGATION_INDEX]: current_navigation_index\n\t\t\t\t},\n\t\t\t\t'',\n\t\t\t\tlocation.href\n\t\t\t);\n\t\t}\n\t});\n\n\t// fix link[rel=icon], because browsers will occasionally try to load relative\n\t// URLs after a pushState/replaceState, resulting in a 404 — see\n\t// https://github.com/sveltejs/kit/issues/3748#issuecomment-1125980897\n\tfor (const link of document.querySelectorAll('link')) {\n\t\tif (link.rel === 'icon') link.href = link.href; // eslint-disable-line\n\t}\n\n\taddEventListener('pageshow', (event) => {\n\t\t// If the user navigates to another site and then uses the back button and\n\t\t// bfcache hits, we need to set navigating to null, the site doesn't know\n\t\t// the navigation away from it was successful.\n\t\t// Info about bfcache here: https://web.dev/bfcache\n\t\tif (event.persisted) {\n\t\t\tstores.navigating.set(null);\n\t\t}\n\t});\n\n\t/**\n\t * @param {URL} url\n\t */\n\tfunction update_url(url) {\n\t\tcurrent.url = url;\n\t\tstores.page.set({ ...page, url });\n\t\tstores.page.notify();\n\t}\n}\n\n/**\n * @param {HTMLElement} target\n * @param {{\n *   status: number;\n *   error: App.Error | null;\n *   node_ids: number[];\n *   params: Record<string, string>;\n *   route: { id: string | null };\n *   data: Array<import('types').ServerDataNode | null>;\n *   form: Record<string, any> | null;\n * }} opts\n */\nasync function _hydrate(\n\ttarget,\n\t{ status = 200, error, node_ids, params, route, data: server_data_nodes, form }\n) {\n\thydrated = true;\n\n\tconst url = new URL(location.href);\n\n\tif (!__SVELTEKIT_EMBEDDED__) {\n\t\t// See https://github.com/sveltejs/kit/pull/4935#issuecomment-1328093358 for one motivation\n\t\t// of determining the params on the client side.\n\t\t({ params = {}, route = { id: null } } = get_navigation_intent(url, false) || {});\n\t}\n\n\t/** @type {import('./types.js').NavigationFinished | undefined} */\n\tlet result;\n\n\ttry {\n\t\tconst branch_promises = node_ids.map(async (n, i) => {\n\t\t\tconst server_data_node = server_data_nodes[i];\n\t\t\t// Type isn't completely accurate, we still need to deserialize uses\n\t\t\tif (server_data_node?.uses) {\n\t\t\t\tserver_data_node.uses = deserialize_uses(server_data_node.uses);\n\t\t\t}\n\n\t\t\treturn load_node({\n\t\t\t\tloader: app.nodes[n],\n\t\t\t\turl,\n\t\t\t\tparams,\n\t\t\t\troute,\n\t\t\t\tparent: async () => {\n\t\t\t\t\tconst data = {};\n\t\t\t\t\tfor (let j = 0; j < i; j += 1) {\n\t\t\t\t\t\tObject.assign(data, (await branch_promises[j]).data);\n\t\t\t\t\t}\n\t\t\t\t\treturn data;\n\t\t\t\t},\n\t\t\t\tserver_data_node: create_data_node(server_data_node)\n\t\t\t});\n\t\t});\n\n\t\t/** @type {Array<import('./types.js').BranchNode | undefined>} */\n\t\tconst branch = await Promise.all(branch_promises);\n\n\t\tconst parsed_route = routes.find(({ id }) => id === route.id);\n\n\t\t// server-side will have compacted the branch, reinstate empty slots\n\t\t// so that error boundaries can be lined up correctly\n\t\tif (parsed_route) {\n\t\t\tconst layouts = parsed_route.layouts;\n\t\t\tfor (let i = 0; i < layouts.length; i++) {\n\t\t\t\tif (!layouts[i]) {\n\t\t\t\t\tbranch.splice(i, 0, undefined);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tresult = get_navigation_result_from_branch({\n\t\t\turl,\n\t\t\tparams,\n\t\t\tbranch,\n\t\t\tstatus,\n\t\t\terror,\n\t\t\tform,\n\t\t\troute: parsed_route ?? null\n\t\t});\n\t} catch (error) {\n\t\tif (error instanceof Redirect) {\n\t\t\t// this is a real edge case — `load` would need to return\n\t\t\t// a redirect but only in the browser\n\t\t\tawait native_navigation(new URL(error.location, location.href));\n\t\t\treturn;\n\t\t}\n\n\t\tresult = await load_root_error_page({\n\t\t\tstatus: get_status(error),\n\t\t\terror: await handle_error(error, { url, params, route }),\n\t\t\turl,\n\t\t\troute\n\t\t});\n\t}\n\n\tif (result.props.page) {\n\t\tresult.props.page.state = {};\n\t}\n\n\tinitialize(result, target, true);\n}\n\n/**\n * @param {URL} url\n * @param {boolean[]} invalid\n * @returns {Promise<import('types').ServerNodesResponse | import('types').ServerRedirectNode>}\n */\nasync function load_data(url, invalid) {\n\tconst data_url = new URL(url);\n\tdata_url.pathname = add_data_suffix(url.pathname);\n\tif (url.pathname.endsWith('/')) {\n\t\tdata_url.searchParams.append(TRAILING_SLASH_PARAM, '1');\n\t}\n\tif (DEV && url.searchParams.has(INVALIDATED_PARAM)) {\n\t\tthrow new Error(`Cannot used reserved query parameter \"${INVALIDATED_PARAM}\"`);\n\t}\n\tdata_url.searchParams.append(INVALIDATED_PARAM, invalid.map((i) => (i ? '1' : '0')).join(''));\n\n\tconst res = await native_fetch(data_url.href);\n\n\tif (!res.ok) {\n\t\t// error message is a JSON-stringified string which devalue can't handle at the top level\n\t\t// turn it into a HttpError to not call handleError on the client again (was already handled on the server)\n\t\t// if `__data.json` doesn't exist or the server has an internal error,\n\t\t// avoid parsing the HTML error page as a JSON\n\t\t/** @type {string | undefined} */\n\t\tlet message;\n\t\tif (res.headers.get('content-type')?.includes('application/json')) {\n\t\t\tmessage = await res.json();\n\t\t} else if (res.status === 404) {\n\t\t\tmessage = 'Not Found';\n\t\t} else if (res.status === 500) {\n\t\t\tmessage = 'Internal Error';\n\t\t}\n\t\tthrow new HttpError(res.status, message);\n\t}\n\n\t// TODO: fix eslint error / figure out if it actually applies to our situation\n\t// eslint-disable-next-line\n\treturn new Promise(async (resolve) => {\n\t\t/**\n\t\t * Map of deferred promises that will be resolved by a subsequent chunk of data\n\t\t * @type {Map<string, import('types').Deferred>}\n\t\t */\n\t\tconst deferreds = new Map();\n\t\tconst reader = /** @type {ReadableStream<Uint8Array>} */ (res.body).getReader();\n\t\tconst decoder = new TextDecoder();\n\n\t\t/**\n\t\t * @param {any} data\n\t\t */\n\t\tfunction deserialize(data) {\n\t\t\treturn devalue.unflatten(data, {\n\t\t\t\tPromise: (id) => {\n\t\t\t\t\treturn new Promise((fulfil, reject) => {\n\t\t\t\t\t\tdeferreds.set(id, { fulfil, reject });\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\tlet text = '';\n\n\t\twhile (true) {\n\t\t\t// Format follows ndjson (each line is a JSON object) or regular JSON spec\n\t\t\tconst { done, value } = await reader.read();\n\t\t\tif (done && !text) break;\n\n\t\t\ttext += !value && text ? '\\n' : decoder.decode(value, { stream: true }); // no value -> final chunk -> add a new line to trigger the last parse\n\n\t\t\twhile (true) {\n\t\t\t\tconst split = text.indexOf('\\n');\n\t\t\t\tif (split === -1) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tconst node = JSON.parse(text.slice(0, split));\n\t\t\t\ttext = text.slice(split + 1);\n\n\t\t\t\tif (node.type === 'redirect') {\n\t\t\t\t\treturn resolve(node);\n\t\t\t\t}\n\n\t\t\t\tif (node.type === 'data') {\n\t\t\t\t\t// This is the first (and possibly only, if no pending promises) chunk\n\t\t\t\t\tnode.nodes?.forEach((/** @type {any} */ node) => {\n\t\t\t\t\t\tif (node?.type === 'data') {\n\t\t\t\t\t\t\tnode.uses = deserialize_uses(node.uses);\n\t\t\t\t\t\t\tnode.data = deserialize(node.data);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tresolve(node);\n\t\t\t\t} else if (node.type === 'chunk') {\n\t\t\t\t\t// This is a subsequent chunk containing deferred data\n\t\t\t\t\tconst { id, data, error } = node;\n\t\t\t\t\tconst deferred = /** @type {import('types').Deferred} */ (deferreds.get(id));\n\t\t\t\t\tdeferreds.delete(id);\n\n\t\t\t\t\tif (error) {\n\t\t\t\t\t\tdeferred.reject(deserialize(error));\n\t\t\t\t\t} else {\n\t\t\t\t\t\tdeferred.fulfil(deserialize(data));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t});\n\n\t// TODO edge case handling necessary? stream() read fails?\n}\n\n/**\n * @param {any} uses\n * @return {import('types').Uses}\n */\nfunction deserialize_uses(uses) {\n\treturn {\n\t\tdependencies: new Set(uses?.dependencies ?? []),\n\t\tparams: new Set(uses?.params ?? []),\n\t\tparent: !!uses?.parent,\n\t\troute: !!uses?.route,\n\t\turl: !!uses?.url,\n\t\tsearch_params: new Set(uses?.search_params ?? [])\n\t};\n}\n\nfunction reset_focus() {\n\tconst autofocus = document.querySelector('[autofocus]');\n\tif (autofocus) {\n\t\t// @ts-ignore\n\t\tautofocus.focus();\n\t} else {\n\t\t// Reset page selection and focus\n\t\t// We try to mimic browsers' behaviour as closely as possible by targeting the\n\t\t// first scrollable region, but unfortunately it's not a perfect match — e.g.\n\t\t// shift-tabbing won't immediately cycle up from the end of the page on Chromium\n\t\t// See https://html.spec.whatwg.org/multipage/interaction.html#get-the-focusable-area\n\t\tconst root = document.body;\n\t\tconst tabindex = root.getAttribute('tabindex');\n\n\t\troot.tabIndex = -1;\n\t\t// @ts-expect-error\n\t\troot.focus({ preventScroll: true, focusVisible: false });\n\n\t\t// restore `tabindex` as to prevent `root` from stealing input from elements\n\t\tif (tabindex !== null) {\n\t\t\troot.setAttribute('tabindex', tabindex);\n\t\t} else {\n\t\t\troot.removeAttribute('tabindex');\n\t\t}\n\n\t\t// capture current selection, so we can compare the state after\n\t\t// snapshot restoration and afterNavigate callbacks have run\n\t\tconst selection = getSelection();\n\n\t\tif (selection && selection.type !== 'None') {\n\t\t\t/** @type {Range[]} */\n\t\t\tconst ranges = [];\n\n\t\t\tfor (let i = 0; i < selection.rangeCount; i += 1) {\n\t\t\t\tranges.push(selection.getRangeAt(i));\n\t\t\t}\n\n\t\t\tsetTimeout(() => {\n\t\t\t\tif (selection.rangeCount !== ranges.length) return;\n\n\t\t\t\tfor (let i = 0; i < selection.rangeCount; i += 1) {\n\t\t\t\t\tconst a = ranges[i];\n\t\t\t\t\tconst b = selection.getRangeAt(i);\n\n\t\t\t\t\t// we need to do a deep comparison rather than just `a !== b` because\n\t\t\t\t\t// Safari behaves differently to other browsers\n\t\t\t\t\tif (\n\t\t\t\t\t\ta.commonAncestorContainer !== b.commonAncestorContainer ||\n\t\t\t\t\t\ta.startContainer !== b.startContainer ||\n\t\t\t\t\t\ta.endContainer !== b.endContainer ||\n\t\t\t\t\t\ta.startOffset !== b.startOffset ||\n\t\t\t\t\t\ta.endOffset !== b.endOffset\n\t\t\t\t\t) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// if the selection hasn't changed (as a result of an element being (auto)focused,\n\t\t\t\t// or a programmatic selection, we reset everything as part of the navigation)\n\t\t\t\t// fixes https://github.com/sveltejs/kit/issues/8439\n\t\t\t\tselection.removeAllRanges();\n\t\t\t});\n\t\t}\n\t}\n}\n\n/**\n * @param {import('./types.js').NavigationState} current\n * @param {import('./types.js').NavigationIntent | undefined} intent\n * @param {URL | null} url\n * @param {Exclude<import('@sveltejs/kit').NavigationType, 'enter'>} type\n */\nfunction create_navigation(current, intent, url, type) {\n\t/** @type {(value: any) => void} */\n\tlet fulfil;\n\n\t/** @type {(error: any) => void} */\n\tlet reject;\n\n\tconst complete = new Promise((f, r) => {\n\t\tfulfil = f;\n\t\treject = r;\n\t});\n\n\t// Handle any errors off-chain so that it doesn't show up as an unhandled rejection\n\tcomplete.catch(() => {});\n\n\t/** @type {import('@sveltejs/kit').Navigation} */\n\tconst navigation = {\n\t\tfrom: {\n\t\t\tparams: current.params,\n\t\t\troute: { id: current.route?.id ?? null },\n\t\t\turl: current.url\n\t\t},\n\t\tto: url && {\n\t\t\tparams: intent?.params ?? null,\n\t\t\troute: { id: intent?.route?.id ?? null },\n\t\t\turl\n\t\t},\n\t\twillUnload: !intent,\n\t\ttype,\n\t\tcomplete\n\t};\n\n\treturn {\n\t\tnavigation,\n\t\t// @ts-expect-error\n\t\tfulfil,\n\t\t// @ts-expect-error\n\t\treject\n\t};\n}\n\nif (DEV) {\n\t// Nasty hack to silence harmless warnings the user can do nothing about\n\tconst console_warn = console.warn;\n\tconsole.warn = function warn(...args) {\n\t\tif (\n\t\t\targs.length === 1 &&\n\t\t\t/<(Layout|Page|Error)(_[\\w$]+)?> was created (with unknown|without expected) prop '(data|form)'/.test(\n\t\t\t\targs[0]\n\t\t\t)\n\t\t) {\n\t\t\treturn;\n\t\t}\n\t\tconsole_warn(...args);\n\t};\n\n\tif (import.meta.hot) {\n\t\timport.meta.hot.on('vite:beforeUpdate', () => {\n\t\t\tif (errored) {\n\t\t\t\tlocation.reload();\n\t\t\t}\n\t\t});\n\t}\n}\n"], "names": ["normalize_path", "path", "trailing_slash", "decode_pathname", "pathname", "decode_params", "params", "key", "strip_hash", "href", "tracked_url_properties", "make_trackable", "url", "callback", "search_params_callback", "tracked", "obj", "param", "value", "property", "DATA_SUFFIX", "HTML_DATA_SUFFIX", "add_data_suffix", "hash", "values", "i", "buffer", "b64_decode", "text", "d", "u8", "native_fetch", "input", "init", "cache", "build_selector", "initial_fetch", "resource", "opts", "selector", "script", "body", "ttl", "subsequent_fetch", "resolved", "cached", "param_pattern", "parse_route_id", "id", "get_route_segments", "segment", "rest_match", "optional_match", "parts", "content", "escape", "code", "match", "is_optional", "is_rest", "name", "matcher", "affects_path", "route", "exec", "matchers", "result", "values_needing_match", "buffered", "s", "next_param", "next_value", "str", "parse", "nodes", "server_loads", "dictionary", "layouts_with_server_load", "leaf", "layouts", "errors", "pattern", "n", "create_layout_loader", "create_leaf_loader", "uses_server_data", "get", "set", "stringify", "data", "SNAPSHOT_KEY", "SCROLL_KEY", "STATES_KEY", "PAGE_URL_KEY", "HISTORY_INDEX", "NAVIGATION_INDEX", "PRELOAD_PRIORITIES", "origin", "resolve_url", "baseURI", "baseTags", "scroll_state", "link_option", "element", "levels", "parent_element", "parent", "find_anchor", "target", "get_link_info", "a", "base", "external", "is_external_url", "download", "get_router_options", "keepfocus", "noscroll", "preload_code", "preload_data", "reload", "replace_state", "el", "get_option_state", "notifiable_store", "store", "writable", "ready", "notify", "val", "new_value", "subscribe", "run", "old_value", "create_updated_store", "timeout", "check", "res", "assets", "updated", "version", "UNDEFINED", "HOLE", "NAN", "POSITIVE_INFINITY", "NEGATIVE_INFINITY", "NEGATIVE_ZERO", "unflatten", "parsed", "revivers", "hydrate", "hydrated", "index", "standalone", "type", "reviver", "map", "array", "object", "valid_layout_exports", "valid_layout_server_exports", "compact", "arr", "HttpError", "status", "Redirect", "location", "SvelteKitError", "message", "INVALIDATED_PARAM", "TRAILING_SLASH_PARAM", "get_status", "error", "get_message", "scroll_positions", "storage.get", "snapshots", "stores", "update_scroll_positions", "clear_onward_history", "current_history_index", "current_navigation_index", "native_navigation", "noop", "routes", "default_layout_loader", "default_error_loader", "container", "app", "invalidated", "components", "load_cache", "before_navigate_callbacks", "on_navigate_callbacks", "after_navigate_callbacks", "current", "started", "autoscroll", "navigating", "hash_navigating", "has_navigated", "force_invalidation", "root", "page", "token", "preload_tokens", "start", "_app", "_target", "_a", "_b", "scroll", "_hydrate", "goto", "_start_router", "reset_invalidation", "capture_snapshot", "c", "restore_snapshot", "persist_state", "storage.set", "_goto", "options", "redirect_count", "nav_token", "navigate", "_preload_data", "intent", "preload", "load_route", "_preload_code", "get_url_path", "load", "initialize", "style", "navigation", "fn", "get_navigation_result_from_branch", "branch", "form", "slash", "node", "branch_node", "data_changed", "p", "prev", "load_node", "loader", "server_data_node", "is_tracking", "uses", "depends", "deps", "dep", "load_input", "requested", "_c", "has_changed", "parent_changed", "route_changed", "url_changed", "search_params_changed", "tracked_params", "create_data_node", "previous", "diff_search_params", "old_url", "new_url", "changed", "old_values", "new_values", "preload_error", "invalidating", "loaders", "server_data", "parent_invalid", "invalid_server_nodes", "invalid", "load_data", "handled_error", "handle_error", "load_root_error_page", "server_data_nodes", "branch_promises", "j", "err", "error_load", "load_nearest_error_page", "server_fallback", "root_layout", "root_error", "get_navigation_intent", "rerouted", "_before_navigate", "delta", "should_block", "nav", "create_navigation", "cancellable", "popped", "state", "accept", "block", "previous_history_index", "previous_navigation_index", "navigation_result", "change", "entry", "after_navigate", "cleanup", "activeElement", "tick", "deep_linked", "changed_focus", "reset_focus", "setup_preload", "mousemove_timeout", "event", "tap", "observer", "entries", "priority", "e", "<PERSON><PERSON>h", "current_hash", "update_url", "fulfil", "submitter", "event_form", "submitter_name", "history_index", "navigation_index", "is_hash_change", "link", "node_ids", "deserialize_uses", "parsed_route", "data_url", "resolve", "deferreds", "reader", "decoder", "deserialize", "devalue.unflatten", "reject", "done", "split", "deferred", "autofocus", "tabindex", "selection", "ranges", "b", "complete", "f", "r"], "mappings": "mHAQiB,IAAI,IAAI,uBAAuB,EAyBzC,SAASA,GAAeC,EAAMC,EAAgB,CACpD,OAAID,IAAS,KAAOC,IAAmB,SAAiBD,EAEpDC,IAAmB,QACfD,EAAK,SAAS,GAAG,EAAIA,EAAK,MAAM,EAAG,EAAE,EAAIA,EACtCC,IAAmB,UAAY,CAACD,EAAK,SAAS,GAAG,EACpDA,EAAO,IAGRA,CACR,CAMO,SAASE,GAAgBC,EAAU,CACzC,OAAOA,EAAS,MAAM,KAAK,EAAE,IAAI,SAAS,EAAE,KAAK,KAAK,CACvD,CAGO,SAASC,GAAcC,EAAQ,CACrC,UAAWC,KAAOD,EAGjBA,EAAOC,CAAG,EAAI,mBAAmBD,EAAOC,CAAG,CAAC,EAG7C,OAAOD,CACR,CAqBO,SAASE,GAAW,CAAE,KAAAC,GAAQ,CACpC,OAAOA,EAAK,MAAM,GAAG,EAAE,CAAC,CACzB,CAMA,MAAMC,GAA+C,CACpD,OACA,WACA,SACA,WACA,QACD,EAOO,SAASC,GAAeC,EAAKC,EAAUC,EAAwB,CACrE,MAAMC,EAAU,IAAI,IAAIH,CAAG,EAE3B,OAAO,eAAeG,EAAS,eAAgB,CAC9C,MAAO,IAAI,MAAMA,EAAQ,aAAc,CACtC,IAAIC,EAAKT,EAAK,CACb,GAAIA,IAAQ,OAASA,IAAQ,UAAYA,IAAQ,MAChD,OAA4BU,IAC3BH,EAAuBG,CAAK,EACrBD,EAAIT,CAAG,EAAEU,CAAK,GAMvBJ,IAEA,MAAMK,EAAQ,QAAQ,IAAIF,EAAKT,CAAG,EAClC,OAAO,OAAOW,GAAU,WAAaA,EAAM,KAAKF,CAAG,EAAIE,CACvD,CACJ,CAAG,EACD,WAAY,GACZ,aAAc,EAChB,CAAE,EAED,UAAWC,KAAYT,GACtB,OAAO,eAAeK,EAASI,EAAU,CACxC,KAAM,CACL,OAAAN,IACOD,EAAIO,CAAQ,CACnB,EAED,WAAY,GACZ,aAAc,EACjB,CAAG,EAcF,OAAOJ,CACR,CA+CA,MAAMK,GAAc,eACdC,GAAmB,mBAQlB,SAASC,GAAgBlB,EAAU,CACzC,OAAIA,EAAS,SAAS,OAAO,EAAUA,EAAS,QAAQ,UAAWiB,EAAgB,EAC5EjB,EAAS,QAAQ,MAAO,EAAE,EAAIgB,EACtC,CChNO,SAASG,MAAQC,EAAQ,CAC/B,IAAID,EAAO,KAEX,UAAWL,KAASM,EACnB,GAAI,OAAON,GAAU,SAAU,CAC9B,IAAIO,EAAIP,EAAM,OACd,KAAOO,GAAGF,EAAQA,EAAO,GAAML,EAAM,WAAW,EAAEO,CAAC,CACnD,SAAU,YAAY,OAAOP,CAAK,EAAG,CACrC,MAAMQ,EAAS,IAAI,WAAWR,EAAM,OAAQA,EAAM,WAAYA,EAAM,UAAU,EAC9E,IAAIO,EAAIC,EAAO,OACf,KAAOD,GAAGF,EAAQA,EAAO,GAAMG,EAAO,EAAED,CAAC,CAC5C,KACG,OAAM,IAAI,UAAU,sCAAsC,EAI5D,OAAQF,IAAS,GAAG,SAAS,EAAE,CAChC,CCjBO,SAASI,GAAWC,EAAM,CAChC,MAAMC,EAAI,KAAKD,CAAI,EAEbE,EAAK,IAAI,WAAWD,EAAE,MAAM,EAElC,QAASJ,EAAI,EAAGA,EAAII,EAAE,OAAQJ,IAC7BK,EAAGL,CAAC,EAAII,EAAE,WAAWJ,CAAC,EAGvB,OAAOK,EAAG,MACX,CCPO,MAAMC,GAAyB,OAAO,MA6D5C,OAAO,MAAQ,CAACC,EAAOC,MACPD,aAAiB,QAAUA,EAAM,QAASC,GAAA,YAAAA,EAAM,SAAU,SAE1D,OACdC,EAAM,OAAOC,GAAeH,CAAK,CAAC,EAG5BD,GAAaC,EAAOC,CAAI,GAIjC,MAAMC,EAAQ,IAAI,IAQX,SAASE,GAAcC,EAAUC,EAAM,CAC7C,MAAMC,EAAWJ,GAAeE,EAAUC,CAAI,EAExCE,EAAS,SAAS,cAAcD,CAAQ,EAC9C,GAAIC,GAAA,MAAAA,EAAQ,YAAa,CACxB,GAAI,CAAE,KAAAC,EAAM,GAAGR,CAAM,EAAG,KAAK,MAAMO,EAAO,WAAW,EAErD,MAAME,EAAMF,EAAO,aAAa,UAAU,EAC1C,OAAIE,GAAKR,EAAM,IAAIK,EAAU,CAAE,KAAAE,EAAM,KAAAR,EAAM,IAAK,IAAO,OAAOS,CAAG,CAAG,CAAA,EACxDF,EAAO,aAAa,UAAU,IAC9B,OAGXC,EAAOd,GAAWc,CAAI,GAGhB,QAAQ,QAAQ,IAAI,SAASA,EAAMR,CAAI,CAAC,CAC/C,CAED,OAAyC,OAAO,MAAMI,EAAUC,CAAI,CACrE,CAQO,SAASK,GAAiBN,EAAUO,EAAUN,EAAM,CAC1D,GAAIJ,EAAM,KAAO,EAAG,CACnB,MAAMK,EAAWJ,GAAeE,EAAUC,CAAI,EACxCO,EAASX,EAAM,IAAIK,CAAQ,EACjC,GAAIM,EAAQ,CAEX,GACC,YAAY,MAAQA,EAAO,KAC3B,CAAC,UAAW,cAAe,iBAAkB,MAAS,EAAE,SAASP,GAAA,YAAAA,EAAM,KAAK,EAE5E,OAAO,IAAI,SAASO,EAAO,KAAMA,EAAO,IAAI,EAG7CX,EAAM,OAAOK,CAAQ,CACrB,CACD,CAED,OAAyC,OAAO,MAAMK,EAAUN,CAAI,CACrE,CAsBA,SAASH,GAAeE,EAAUC,EAAM,CAGvC,IAAIC,EAAW,2CAFH,KAAK,UAAUF,aAAoB,QAAUA,EAAS,IAAMA,CAAQ,CAEnB,IAE7D,GAAIC,GAAA,MAAAA,EAAM,SAAWA,GAAA,MAAAA,EAAM,KAAM,CAEhC,MAAMd,EAAS,CAAA,EAEXc,EAAK,SACRd,EAAO,KAAK,CAAC,GAAG,IAAI,QAAQc,EAAK,OAAO,CAAC,EAAE,KAAK,GAAG,CAAC,EAGjDA,EAAK,OAAS,OAAOA,EAAK,MAAS,UAAY,YAAY,OAAOA,EAAK,IAAI,IAC9Ed,EAAO,KAAKc,EAAK,IAAI,EAGtBC,GAAY,eAAehB,GAAK,GAAGC,CAAM,CAAC,IAC1C,CAED,OAAOe,CACR,CC9KA,MAAMO,GAAgB,wCAMf,SAASC,GAAeC,EAAI,CAElC,MAAM1C,EAAS,CAAA,EA0Ff,MAAO,CAAE,QAvFR0C,IAAO,IACJ,OACA,IAAI,OACJ,IAAIC,GAAmBD,CAAE,EACvB,IAAKE,GAAY,CAEjB,MAAMC,EAAa,+BAA+B,KAAKD,CAAO,EAC9D,GAAIC,EACH,OAAA7C,EAAO,KAAK,CACX,KAAM6C,EAAW,CAAC,EAClB,QAASA,EAAW,CAAC,EACrB,SAAU,GACV,KAAM,GACN,QAAS,EAClB,CAAS,EACM,aAGR,MAAMC,EAAiB,6BAA6B,KAAKF,CAAO,EAChE,GAAIE,EACH,OAAA9C,EAAO,KAAK,CACX,KAAM8C,EAAe,CAAC,EACtB,QAASA,EAAe,CAAC,EACzB,SAAU,GACV,KAAM,GACN,QAAS,EAClB,CAAS,EACM,gBAGR,GAAI,CAACF,EACJ,OAGD,MAAMG,EAAQH,EAAQ,MAAM,iBAAiB,EAgD7C,MAAO,IA/CQG,EACb,IAAI,CAACC,EAAS7B,IAAM,CACpB,GAAIA,EAAI,EAAG,CACV,GAAI6B,EAAQ,WAAW,IAAI,EAC1B,OAAOC,GAAO,OAAO,aAAa,SAASD,EAAQ,MAAM,CAAC,EAAG,EAAE,CAAC,CAAC,EAGlE,GAAIA,EAAQ,WAAW,IAAI,EAC1B,OAAOC,GACN,OAAO,aACN,GAAGD,EACD,MAAM,CAAC,EACP,MAAM,GAAG,EACT,IAAKE,GAAS,SAASA,EAAM,EAAE,CAAC,CAClC,CACb,EAMU,MAAMC,EAAwCX,GAAc,KAAKQ,CAAO,EAOlE,CAAA,CAAGI,EAAaC,EAASC,EAAMC,CAAO,EAAIJ,EAKhD,OAAAnD,EAAO,KAAK,CACX,KAAAsD,EACA,QAAAC,EACA,SAAU,CAAC,CAACH,EACZ,KAAM,CAAC,CAACC,EACR,QAASA,EAAUlC,IAAM,GAAK4B,EAAM,CAAC,IAAM,GAAK,EAC3D,CAAW,EACMM,EAAU,QAAUD,EAAc,WAAa,UACtD,CAED,OAAOH,GAAOD,CAAO,CAC9B,CAAS,EACA,KAAK,EAAE,CAGhB,CAAO,EACA,KAAK,EAAE,CAAC,KACf,EAEmB,OAAAhD,EACnB,CAiBA,SAASwD,GAAaZ,EAAS,CAC9B,MAAO,CAAC,cAAc,KAAKA,CAAO,CACnC,CASO,SAASD,GAAmBc,EAAO,CACzC,OAAOA,EAAM,MAAM,CAAC,EAAE,MAAM,GAAG,EAAE,OAAOD,EAAY,CACrD,CAOO,SAASE,GAAKP,EAAOnD,EAAQ2D,EAAU,CAE7C,MAAMC,EAAS,CAAA,EAET1C,EAASiC,EAAM,MAAM,CAAC,EACtBU,EAAuB3C,EAAO,OAAQN,GAAUA,IAAU,MAAS,EAEzE,IAAIkD,EAAW,EAEf,QAAS3C,EAAI,EAAGA,EAAInB,EAAO,OAAQmB,GAAK,EAAG,CAC1C,MAAMR,EAAQX,EAAOmB,CAAC,EACtB,IAAIP,EAAQM,EAAOC,EAAI2C,CAAQ,EAc/B,GAVInD,EAAM,SAAWA,EAAM,MAAQmD,IAClClD,EAAQM,EACN,MAAMC,EAAI2C,EAAU3C,EAAI,CAAC,EACzB,OAAQ4C,GAAMA,CAAC,EACf,KAAK,GAAG,EAEVD,EAAW,GAIRlD,IAAU,OAAW,CACpBD,EAAM,OAAMiD,EAAOjD,EAAM,IAAI,EAAI,IACrC,QACA,CAED,GAAI,CAACA,EAAM,SAAWgD,EAAShD,EAAM,OAAO,EAAEC,CAAK,EAAG,CACrDgD,EAAOjD,EAAM,IAAI,EAAIC,EAIrB,MAAMoD,EAAahE,EAAOmB,EAAI,CAAC,EACzB8C,EAAa/C,EAAOC,EAAI,CAAC,EAC3B6C,GAAc,CAACA,EAAW,MAAQA,EAAW,UAAYC,GAActD,EAAM,UAChFmD,EAAW,GAKX,CAACE,GACD,CAACC,GACD,OAAO,KAAKL,CAAM,EAAE,SAAWC,EAAqB,SAEpDC,EAAW,GAEZ,QACA,CAID,GAAInD,EAAM,UAAYA,EAAM,QAAS,CACpCmD,IACA,QACA,CAGD,MACA,CAED,GAAI,CAAAA,EACJ,OAAOF,CACR,CAGA,SAASX,GAAOiB,EAAK,CACpB,OACCA,EACE,UAAW,EAEX,QAAQ,SAAU,MAAM,EAExB,QAAQ,KAAM,KAAK,EACnB,QAAQ,MAAO,QAAQ,EACvB,QAAQ,MAAO,QAAQ,EACvB,QAAQ,KAAM,KAAK,EAEnB,QAAQ,mBAAoB,MAAM,CAEtC,CCtNO,SAASC,GAAM,CAAE,MAAAC,EAAO,aAAAC,EAAc,WAAAC,EAAY,SAAAX,CAAQ,EAAI,CACpE,MAAMY,EAA2B,IAAI,IAAIF,CAAY,EAErD,OAAO,OAAO,QAAQC,CAAU,EAAE,IAAI,CAAC,CAAC5B,EAAI,CAAC8B,EAAMC,EAASC,CAAM,CAAC,IAAM,CACxE,KAAM,CAAE,QAAAC,EAAS,OAAA3E,CAAQ,EAAGyC,GAAeC,CAAE,EAEvCe,EAAQ,CACb,GAAAf,EAEA,KAAO/C,GAAS,CACf,MAAMwD,EAAQwB,EAAQ,KAAKhF,CAAI,EAC/B,GAAIwD,EAAO,OAAOO,GAAKP,EAAOnD,EAAQ2D,CAAQ,CAC9C,EACD,OAAQ,CAAC,EAAG,GAAIe,GAAU,CAAE,CAAA,EAAG,IAAKE,GAAMR,EAAMQ,CAAC,CAAC,EAClD,QAAS,CAAC,EAAG,GAAIH,GAAW,CAAA,CAAI,EAAC,IAAII,CAAoB,EACzD,KAAMC,EAAmBN,CAAI,CAChC,EAKE,OAAAf,EAAM,OAAO,OAASA,EAAM,QAAQ,OAAS,KAAK,IACjDA,EAAM,OAAO,OACbA,EAAM,QAAQ,MACjB,EAESA,CACT,CAAE,EAMD,SAASqB,EAAmBpC,EAAI,CAG/B,MAAMqC,EAAmBrC,EAAK,EAC9B,OAAIqC,IAAkBrC,EAAK,CAACA,GACrB,CAACqC,EAAkBX,EAAM1B,CAAE,CAAC,CACnC,CAMD,SAASmC,EAAqBnC,EAAI,CAGjC,OAAOA,IAAO,OAAYA,EAAK,CAAC6B,EAAyB,IAAI7B,CAAE,EAAG0B,EAAM1B,CAAE,CAAC,CAC3E,CACF,CCnDO,SAASsC,GAAI/E,EAAKkE,EAAQ,KAAK,MAAO,CAC5C,GAAI,CACH,OAAOA,EAAM,eAAelE,CAAG,CAAC,CAClC,MAAS,CAEP,CACF,CAQO,SAASgF,GAAIhF,EAAKW,EAAOsE,EAAY,KAAK,UAAW,CAC3D,MAAMC,EAAOD,EAAUtE,CAAK,EAC5B,GAAI,CACH,eAAeX,CAAG,EAAIkF,CACxB,MAAS,CAEP,CACF,gLC1BaC,GAAe,qBACfC,GAAa,mBACbC,GAAa,mBACbC,GAAe,oBAEfC,EAAgB,oBAChBC,EAAmB,uBAEnBC,EAA2C,CACvD,IAAK,EACL,MAAO,EACP,SAAU,EACV,MAAO,EACP,IAAK,GACL,MAAO,EACR,ECPaC,EAAmB,SAAS,OAGlC,SAASC,GAAYtF,EAAK,CAChC,GAAIA,aAAe,IAAY,OAAAA,EAE/B,IAAIuF,EAAU,SAAS,QAEvB,GAAI,CAACA,EAAS,CACP,MAAAC,EAAW,SAAS,qBAAqB,MAAM,EACrDD,EAAUC,EAAS,OAASA,EAAS,CAAC,EAAE,KAAO,SAAS,GACzD,CAEO,OAAA,IAAI,IAAIxF,EAAKuF,CAAO,CAC5B,CAEO,SAASE,IAAe,CACvB,MAAA,CACN,EAAG,YACH,EAAG,WAAA,CAEL,CAyBA,SAASC,EAAYC,EAAS3C,EAAM,CAS5B,OAPN2C,EAAQ,aAAa,kBAAkB3C,CAAI,EAAE,CAQ/C,CAyBA,MAAM4C,GAAS,CACd,GAAGR,EACH,GAAIA,EAAmB,KACxB,EAMA,SAASS,GAAeF,EAAS,CAC5B,IAAAG,EAASH,EAAQ,cAAgBA,EAAQ,WAG7C,OAAIG,GAAA,YAAAA,EAAQ,YAAa,KAAIA,EAASA,EAAO,MAEdA,CAChC,CAMgB,SAAAC,GAAYJ,EAASK,EAAQ,CACrC,KAAAL,GAAWA,IAAYK,GAAQ,CACjC,GAAAL,EAAQ,SAAS,YAAY,IAAM,KAAOA,EAAQ,aAAa,MAAM,EACxE,OAAuDA,EAGxDA,EAAkCE,GAAeF,CAAO,CACzD,CACD,CAMgB,SAAAM,GAAcC,EAAGC,EAAM,CAElC,IAAAnG,EAEA,GAAA,CACGA,EAAA,IAAI,IAAIkG,aAAa,YAAcA,EAAE,KAAK,QAAUA,EAAE,KAAM,SAAS,OAAO,CAAA,MAC3E,CAAC,CAET,MAAMF,EAASE,aAAa,YAAcA,EAAE,OAAO,QAAUA,EAAE,OAEzDE,EACL,CAACpG,GACD,CAAC,CAACgG,GACFK,GAAgBrG,EAAKmG,CAAI,IACxBD,EAAE,aAAa,KAAK,GAAK,IAAI,MAAM,KAAK,EAAE,SAAS,UAAU,EAEzDI,GAAWtG,GAAA,YAAAA,EAAK,UAAWqF,GAAUa,EAAE,aAAa,UAAU,EAEpE,MAAO,CAAE,IAAAlG,EAAK,SAAAoG,EAAU,OAAAJ,EAAQ,SAAAM,CAAS,CAC1C,CAKO,SAASC,EAAmBZ,EAAS,CAE3C,IAAIa,EAAY,KAGZC,EAAW,KAGXC,EAAe,KAGfC,EAAe,KAGfC,EAAS,KAGTC,EAAgB,KAGhBC,EAAKnB,EAEF,KAAAmB,GAAMA,IAAO,SAAS,iBACxBJ,IAAiB,OAAqBA,EAAAhB,EAAYoB,EAAI,cAAc,GACpEH,IAAiB,OAAqBA,EAAAjB,EAAYoB,EAAI,cAAc,GACpEN,IAAc,OAAkBA,EAAAd,EAAYoB,EAAI,WAAW,GAC3DL,IAAa,OAAiBA,EAAAf,EAAYoB,EAAI,UAAU,GACxDF,IAAW,OAAeA,EAAAlB,EAAYoB,EAAI,QAAQ,GAClDD,IAAkB,OAAsBA,EAAAnB,EAAYoB,EAAI,cAAc,GAE1EA,EAA6BjB,GAAeiB,CAAE,EAI/C,SAASC,EAAiBzG,EAAO,CAChC,OAAQA,EAAO,CACd,IAAK,GACL,IAAK,OACG,MAAA,GACR,IAAK,MACL,IAAK,QACG,MAAA,GACR,QACQ,MACT,CACD,CAEO,MAAA,CACN,aAAcsF,GAAOc,GAAgB,KAAK,EAC1C,aAAcd,GAAOe,GAAgB,KAAK,EAC1C,UAAWI,EAAiBP,CAAS,EACrC,SAAUO,EAAiBN,CAAQ,EACnC,OAAQM,EAAiBH,CAAM,EAC/B,cAAeG,EAAiBF,CAAa,CAAA,CAE/C,CAGO,SAASG,GAAiB1G,EAAO,CACjC,MAAA2G,EAAQC,GAAS5G,CAAK,EAC5B,IAAI6G,EAAQ,GAEZ,SAASC,GAAS,CACTD,EAAA,GACFF,EAAA,OAAQI,GAAQA,CAAG,CAC1B,CAGA,SAAS1C,EAAI2C,EAAW,CACfH,EAAA,GACRF,EAAM,IAAIK,CAAS,CACpB,CAGA,SAASC,EAAUC,EAAK,CAEnB,IAAAC,EACG,OAAAR,EAAM,UAAWK,GAAc,EACjCG,IAAc,QAAcN,GAASG,IAAcG,IACtDD,EAAKC,EAAYH,CAAU,CAC5B,CACA,CACF,CAEO,MAAA,CAAE,OAAAF,EAAQ,IAAAzC,EAAK,UAAA4C,EACvB,CAEO,SAASG,IAAuB,CACtC,KAAM,CAAE,IAAA/C,EAAK,UAAA4C,CAAU,EAAIL,GAAS,EAAK,EAarC,IAAAS,EAGJ,eAAeC,GAAQ,CACtB,aAAaD,CAAO,EAIhB,GAAA,CACH,MAAME,EAAM,MAAM,MAAM,GAAGC,EAAM,qBAAsC,CACtE,QAAS,CACR,OAAQ,WACR,gBAAiB,UAClB,CAAA,CACA,EAEG,GAAA,CAACD,EAAI,GACD,MAAA,GAIF,MAAAE,GADO,MAAMF,EAAI,QACF,UAAYG,GAEjC,OAAID,IACHpD,EAAI,EAAI,EACR,aAAagD,CAAO,GAGdI,CAAA,MACA,CACA,MAAA,EACR,CACD,CAIO,MAAA,CACN,UAAAR,EACA,MAAAK,CAAA,CAEF,CAMgB,SAAAvB,GAAgBrG,EAAKmG,EAAM,CAC1C,OAAOnG,EAAI,SAAWqF,GAAU,CAACrF,EAAI,SAAS,WAAWmG,CAAI,CAC9D,CC1SO,MAAM8B,GAAY,GACZC,GAAO,GACPC,GAAM,GACNC,GAAoB,GACpBC,GAAoB,GACpBC,GAAgB,GCkBtB,SAASC,GAAUC,EAAQC,EAAU,CAC3C,GAAI,OAAOD,GAAW,SAAU,OAAOE,EAAQF,EAAQ,EAAI,EAE3D,GAAI,CAAC,MAAM,QAAQA,CAAM,GAAKA,EAAO,SAAW,EAC/C,MAAM,IAAI,MAAM,eAAe,EAGhC,MAAM5H,EAA+B4H,EAE/BG,EAAW,MAAM/H,EAAO,MAAM,EAMpC,SAAS8H,EAAQE,EAAOC,EAAa,GAAO,CAC3C,GAAID,IAAUX,GAAW,OACzB,GAAIW,IAAUT,GAAK,MAAO,KAC1B,GAAIS,IAAUR,GAAmB,MAAO,KACxC,GAAIQ,IAAUP,GAAmB,MAAO,KACxC,GAAIO,IAAUN,GAAe,MAAO,GAEpC,GAAIO,EAAY,MAAM,IAAI,MAAM,eAAe,EAE/C,GAAID,KAASD,EAAU,OAAOA,EAASC,CAAK,EAE5C,MAAMtI,EAAQM,EAAOgI,CAAK,EAE1B,GAAI,CAACtI,GAAS,OAAOA,GAAU,SAC9BqI,EAASC,CAAK,EAAItI,UACR,MAAM,QAAQA,CAAK,EAC7B,GAAI,OAAOA,EAAM,CAAC,GAAM,SAAU,CACjC,MAAMwI,EAAOxI,EAAM,CAAC,EAEdyI,EAAUN,GAAA,YAAAA,EAAWK,GAC3B,GAAIC,EACH,OAAQJ,EAASC,CAAK,EAAIG,EAAQL,EAAQpI,EAAM,CAAC,CAAC,CAAC,EAGpD,OAAQwI,EAAI,CACX,IAAK,OACJH,EAASC,CAAK,EAAI,IAAI,KAAKtI,EAAM,CAAC,CAAC,EACnC,MAED,IAAK,MACJ,MAAMqE,EAAM,IAAI,IAChBgE,EAASC,CAAK,EAAIjE,EAClB,QAAS9D,EAAI,EAAGA,EAAIP,EAAM,OAAQO,GAAK,EACtC8D,EAAI,IAAI+D,EAAQpI,EAAMO,CAAC,CAAC,CAAC,EAE1B,MAED,IAAK,MACJ,MAAMmI,EAAM,IAAI,IAChBL,EAASC,CAAK,EAAII,EAClB,QAASnI,EAAI,EAAGA,EAAIP,EAAM,OAAQO,GAAK,EACtCmI,EAAI,IAAIN,EAAQpI,EAAMO,CAAC,CAAC,EAAG6H,EAAQpI,EAAMO,EAAI,CAAC,CAAC,CAAC,EAEjD,MAED,IAAK,SACJ8H,EAASC,CAAK,EAAI,IAAI,OAAOtI,EAAM,CAAC,EAAGA,EAAM,CAAC,CAAC,EAC/C,MAED,IAAK,SACJqI,EAASC,CAAK,EAAI,OAAOtI,EAAM,CAAC,CAAC,EACjC,MAED,IAAK,SACJqI,EAASC,CAAK,EAAI,OAAOtI,EAAM,CAAC,CAAC,EACjC,MAED,IAAK,OACJ,MAAMF,EAAM,OAAO,OAAO,IAAI,EAC9BuI,EAASC,CAAK,EAAIxI,EAClB,QAASS,EAAI,EAAGA,EAAIP,EAAM,OAAQO,GAAK,EACtCT,EAAIE,EAAMO,CAAC,CAAC,EAAI6H,EAAQpI,EAAMO,EAAI,CAAC,CAAC,EAErC,MAED,QACC,MAAM,IAAI,MAAM,gBAAgBiI,CAAI,EAAE,CACvC,CACL,KAAU,CACN,MAAMG,EAAQ,IAAI,MAAM3I,EAAM,MAAM,EACpCqI,EAASC,CAAK,EAAIK,EAElB,QAASpI,EAAI,EAAGA,EAAIP,EAAM,OAAQO,GAAK,EAAG,CACzC,MAAMyD,EAAIhE,EAAMO,CAAC,EACbyD,IAAM4D,KAEVe,EAAMpI,CAAC,EAAI6H,EAAQpE,CAAC,EACpB,CACD,KACK,CAEN,MAAM4E,EAAS,CAAA,EACfP,EAASC,CAAK,EAAIM,EAElB,UAAWvJ,KAAOW,EAAO,CACxB,MAAMgE,EAAIhE,EAAMX,CAAG,EACnBuJ,EAAOvJ,CAAG,EAAI+I,EAAQpE,CAAC,CACvB,CACD,CAED,OAAOqE,EAASC,CAAK,CACrB,CAED,OAAOF,EAAQ,CAAC,CACjB,CCtEA,MAAMS,GAAuB,IAAI,IAAI,CACpC,OACA,YACA,MACA,MACA,gBACA,QACD,CAAC,EACkC,CAAC,GAAGA,EAA+B,EACtE,MAAMC,GAA8B,IAAI,IAAI,CAAC,GAAGD,EAAoB,CAAC,EAC3B,CAAC,GAAGC,EAAiD,EClExF,SAASC,GAAQC,EAAK,CAC5B,OAAOA,EAAI,OAAgDjC,GAAQA,GAAO,IAAI,CAC/E,CCRO,MAAMkC,EAAU,CAKtB,YAAYC,EAAQ3H,EAAM,CACzB,KAAK,OAAS2H,EACV,OAAO3H,GAAS,SACnB,KAAK,KAAO,CAAE,QAASA,CAAI,EACjBA,EACV,KAAK,KAAOA,EAEZ,KAAK,KAAO,CAAE,QAAS,UAAU2H,CAAM,GAExC,CAED,UAAW,CACV,OAAO,KAAK,UAAU,KAAK,IAAI,CAC/B,CACF,CAEO,MAAMC,EAAS,CAKrB,YAAYD,EAAQE,EAAU,CAC7B,KAAK,OAASF,EACd,KAAK,SAAWE,CAChB,CACF,CAOO,MAAMC,WAAuB,KAAM,CAMzC,YAAYH,EAAQxI,EAAM4I,EAAS,CAClC,MAAMA,CAAO,EACb,KAAK,OAASJ,EACd,KAAK,KAAOxI,CACZ,CACF,CCnCO,MAAM6I,GAAoB,0BAEpBC,GAAuB,6BCa7B,SAASC,EAAWC,EAAO,CACjC,OAAOA,aAAiBT,IAAaS,aAAiBL,GAAiBK,EAAM,OAAS,GACvF,CAKO,SAASC,GAAYD,EAAO,CAClC,OAAOA,aAAiBL,GAAiBK,EAAM,KAAO,gBACvD,CCqBA,MAAME,EAAmBC,GAAYpF,EAAU,GAAK,GAM9CqF,EAAYD,GAAYrF,EAAY,GAAK,GAuClCuF,EAAS,CACrB,IAAsCrD,GAAA,EAAE,EACxC,KAAuCA,GAAA,EAAE,EACzC,WAA4BE,GAC+B,IAC3D,EACA,QAA8CQ,GAAA,CAC/C,EAGA,SAAS4C,GAAwB1B,EAAO,CACtBsB,EAAAtB,CAAK,EAAInD,IAC3B,CAMA,SAAS8E,GAAqBC,EAAuBC,EAA0B,CAG9E,IAAI5J,EAAI2J,EAAwB,EACzB,KAAAN,EAAiBrJ,CAAC,GACxB,OAAOqJ,EAAiBrJ,CAAC,EACpBA,GAAA,EAIC,IADPA,EAAI4J,EAA2B,EACxBL,EAAUvJ,CAAC,GACjB,OAAOuJ,EAAUvJ,CAAC,EACbA,GAAA,CAEP,CAQA,SAAS6J,EAAkB1K,EAAK,CAC/B,gBAAS,KAAOA,EAAI,KACb,IAAI,QAAQ,IAAM,CAAA,CAAE,CAC5B,CAEA,SAAS2K,IAAO,CAAC,CAGjB,IAAIC,GAEAC,GAEAC,EAEAC,EAEA/E,GAEAgF,EAGJ,MAAMC,GAAc,CAAA,EAQdC,EAAa,CAAA,EAGnB,IAAIC,EAAa,KAGjB,MAAMC,GAA4B,CAAA,EAG5BC,GAAwB,CAAA,EAG9B,IAAIC,EAA2B,CAAA,EAG3BC,EAAU,CACb,OAAQ,CAAC,EACT,MAAO,KAEP,IAAK,IACN,EAGI5C,GAAW,GACX6C,EAAU,GACVC,GAAa,GAEbC,EAAa,GACbC,EAAkB,GAElBC,GAAgB,GAEhBC,GAAqB,GAGrBC,GAGAtB,EAGAC,EAGAsB,EAGAC,GAQJ,MAAMC,MAAqB,IAUL,eAAAC,GAAMC,EAAMC,EAAS1D,EAAS,SAU/C,SAAS,MAAQ,SAAS,OAE7B,SAAS,KAAO,SAAS,MAGpBsC,EAAAmB,EACNvB,GAAS/G,GAAMsI,CAAI,EACPpB,EAAmC,SAAS,gBAC/C/E,GAAAoG,EAIevB,GAAAsB,EAAK,MAAM,CAAC,EACbrB,EAAAqB,EAAK,MAAM,CAAC,EACbtB,KACDC,IAEGN,GAAA6B,EAAA,QAAQ,QAAR,YAAAA,EAAgBnH,GACbuF,GAAA6B,EAAA,QAAQ,QAAR,YAAAA,EAAgBnH,GAEtCqF,IAGoBA,EAAAC,EAA2B,KAAK,MAGhD,QAAA,aACP,CACC,GAAG,QAAQ,MACX,CAACvF,CAAa,EAAGsF,EACjB,CAACrF,CAAgB,EAAGsF,CACrB,EACA,EAAA,GAMI,MAAA8B,EAASrC,EAAiBM,CAAqB,EACjD+B,IACH,QAAQ,kBAAoB,SACnB,SAAAA,EAAO,EAAGA,EAAO,CAAC,GAGxB7D,EACG,MAAA8D,GAASxG,GAAQ0C,CAAO,EAE9B+D,GAAK,SAAS,KAAM,CAAE,aAAc,EAAM,CAAA,EAG7BC,IACf,CAkCA,SAASC,IAAqB,CAC7B1B,GAAY,OAAS,EACAY,GAAA,EACtB,CAGA,SAASe,GAAiBhE,EAAO,CAC5BsC,EAAW,KAAM2B,GAAMA,GAAA,YAAAA,EAAG,QAAQ,IAC3BzC,EAAAxB,CAAK,EAAIsC,EAAW,IAAK2B,GAAM,OAAA,OAAAR,EAAAQ,GAAA,YAAAA,EAAG,WAAH,YAAAR,EAAa,UAAS,EAEjE,CAGA,SAASS,GAAiBlE,EAAO,QAChCyD,EAAAjC,EAAUxB,CAAK,IAAf,MAAAyD,EAAkB,QAAQ,CAAC/L,EAAOO,IAAM,UACvCyL,GAAAD,EAAAnB,EAAWrK,CAAC,IAAZ,YAAAwL,EAAe,WAAf,MAAAC,EAAyB,QAAQhM,EAAK,EAExC,CAEA,SAASyM,IAAgB,CACxBzC,GAAwBE,CAAqB,EACrCwC,GAAIjI,GAAYmF,CAAgB,EAExC0C,GAAiBnC,CAAwB,EACjCuC,GAAIlI,GAAcsF,CAAS,CACpC,CAQA,eAAe6C,GAAMjN,EAAKkN,EAASC,EAAgBC,EAAW,CAC7D,OAAOC,EAAS,CACf,KAAM,OACN,IAAK/H,GAAYtF,CAAG,EACpB,UAAWkN,EAAQ,UACnB,SAAUA,EAAQ,SAClB,cAAeA,EAAQ,aACvB,MAAOA,EAAQ,MACf,eAAAC,EACA,UAAAC,EACA,OAAQ,IAAM,CACTF,EAAQ,gBACUrB,GAAA,GAEvB,CAAA,CACA,CACF,CAGA,eAAeyB,GAAcC,EAAQ,CAKhC,GAAAA,EAAO,MAAOpC,GAAA,YAAAA,EAAY,IAAI,CACjC,MAAMqC,EAAU,CAAA,EAChBvB,EAAe,IAAIuB,CAAO,EACbrC,EAAA,CACZ,GAAIoC,EAAO,GACX,MAAOC,EACP,QAASC,GAAW,CAAE,GAAGF,EAAQ,QAAAC,EAAS,EAAE,KAAMlK,IACjD2I,EAAe,OAAOuB,CAAO,EACzBlK,EAAO,OAAS,UAAYA,EAAO,MAAM,QAE/B6H,EAAA,MAEP7H,EACP,CAAA,CAEH,CAEA,OAAO6H,EAAW,OACnB,CAGA,eAAeuC,GAAclO,EAAU,CAChC,MAAA2D,EAAQyH,GAAO,KAAMzH,GAAUA,EAAM,KAAKwK,GAAanO,CAAQ,CAAC,CAAC,EAEnE2D,GACH,MAAM,QAAQ,IAAI,CAAC,GAAGA,EAAM,QAASA,EAAM,IAAI,EAAE,IAAKyK,GAASA,GAAA,YAAAA,EAAO,IAAI,CAAC,CAE7E,CAOA,SAASC,GAAWvK,EAAQ0C,EAAQ0C,EAAS,OAG5C6C,EAAUjI,EAAO,MAEX,MAAAwK,EAAQ,SAAS,cAAc,uBAAuB,EACxDA,GAAOA,EAAM,OAAO,EAExB/B,EAAoDzI,EAAO,MAAM,KAE1DwI,GAAA,IAAId,EAAI,KAAK,CACnB,OAAAhF,EACA,MAAO,CAAE,GAAG1C,EAAO,MAAO,OAAA+G,EAAQ,WAAAa,CAAW,EAC7C,QAAAxC,CAAA,CACA,EAEDoE,GAAiBrC,CAAwB,EAGzC,MAAMsD,EAAa,CAClB,KAAM,KACN,GAAI,CACH,OAAQxC,EAAQ,OAChB,MAAO,CAAE,KAAIc,EAAAd,EAAQ,QAAR,YAAAc,EAAe,KAAM,IAAK,EACvC,IAAK,IAAI,IAAI,SAAS,IAAI,CAC3B,EACA,WAAY,GACZ,KAAM,QACN,SAAU,QAAQ,QAAQ,CAAA,EAG3Bf,EAAyB,QAAS0C,GAAOA,EAAGD,CAAU,CAAC,EAE7CvC,EAAA,EACX,CAcA,SAASyC,GAAkC,CAAE,IAAAjO,EAAK,OAAAN,EAAQ,OAAAwO,EAAQ,OAAA1E,EAAQ,MAAAQ,EAAO,MAAA7G,EAAO,KAAAgL,GAAQ,CAE/F,IAAIC,EAAQ,QAIZ,GAAIjI,IAASnG,EAAI,WAAamG,GAAQnG,EAAI,WAAamG,EAAO,KACrDiI,EAAA,aAER,WAAWC,KAAQH,GACdG,GAAA,YAAAA,EAAM,SAAU,SAAWD,EAAQC,EAAK,OAI9CrO,EAAI,SAAWZ,GAAeY,EAAI,SAAUoO,CAAK,EAGjDpO,EAAI,OAASA,EAAI,OAGjB,MAAMsD,EAAS,CACd,KAAM,SACN,MAAO,CACN,IAAAtD,EACA,OAAAN,EACA,OAAAwO,EACA,MAAAlE,EACA,MAAA7G,CACD,EACA,MAAO,CAEN,aAAckG,GAAQ6E,CAAM,EAAE,IAAKI,GAAgBA,EAAY,KAAK,SAAS,EAC7E,KAAAvC,CACD,CAAA,EAGGoC,IAAS,SACZ7K,EAAO,MAAM,KAAO6K,GAGrB,IAAItJ,EAAO,CAAA,EACP0J,EAAe,CAACxC,EAEhByC,EAAI,EAER,QAAS3N,EAAI,EAAGA,EAAI,KAAK,IAAIqN,EAAO,OAAQ3C,EAAQ,OAAO,MAAM,EAAG1K,GAAK,EAAG,CACrE,MAAAwN,EAAOH,EAAOrN,CAAC,EACf4N,EAAOlD,EAAQ,OAAO1K,CAAC,GAEzBwN,GAAA,YAAAA,EAAM,SAASI,GAAA,YAAAA,EAAM,QAAqBF,EAAA,IACzCF,IAELxJ,EAAO,CAAE,GAAGA,EAAM,GAAGwJ,EAAK,IAAK,EAG3BE,IACHjL,EAAO,MAAM,QAAQkL,CAAC,EAAE,EAAI3J,GAGxB2J,GAAA,EACN,CASA,OANC,CAACjD,EAAQ,KACTvL,EAAI,OAASuL,EAAQ,IAAI,MACzBA,EAAQ,QAAUvB,GACjBmE,IAAS,QAAaA,IAASpC,EAAK,MACrCwC,KAGAjL,EAAO,MAAM,KAAO,CACnB,MAAA0G,EACA,OAAAtK,EACA,MAAO,CACN,IAAIyD,GAAA,YAAAA,EAAO,KAAM,IAClB,EACA,MAAO,CAAC,EACR,OAAAqG,EACA,IAAK,IAAI,IAAIxJ,CAAG,EAChB,KAAMmO,GAAQ,KAEd,KAAMI,EAAe1J,EAAOkH,EAAK,IAAA,GAI5BzI,CACR,CAgBA,eAAeoL,GAAU,CAAE,OAAAC,EAAQ,OAAA7I,EAAQ,IAAA9F,EAAK,OAAAN,EAAQ,MAAAyD,EAAO,iBAAAyL,GAAoB,WAElF,IAAI/J,EAAO,KAEPgK,EAAc,GAGlB,MAAMC,EAAO,CACZ,iBAAkB,IAClB,WAAY,IACZ,OAAQ,GACR,MAAO,GACP,IAAK,GACL,kBAAmB,GAAI,EAGlBT,EAAO,MAAMM,IAMf,IAAAtC,EAAAgC,EAAK,YAAL,MAAAhC,EAAgB,KAAM,CAEhB,IAAA0C,EAAT,YAAoBC,EAAM,CACzB,UAAWC,KAAOD,EAAM,CAGvB,KAAM,CAAE,KAAAnP,CAAK,EAAI,IAAI,IAAIoP,EAAKjP,CAAG,EAC5B8O,EAAA,aAAa,IAAIjP,CAAI,CAC3B,CAAA,EAID,MAAMqP,EAAa,CAClB,MAAO,IAAI,MAAM/L,EAAO,CACvB,IAAK,CAAC6C,EAAQrG,KACTkP,IACHC,EAAK,MAAQ,IAEP9I,EAA4BrG,CAAA,EACpC,CACA,EACD,OAAQ,IAAI,MAAMD,EAAQ,CACzB,IAAK,CAACsG,EAAQrG,KACTkP,GACHC,EAAK,OAAO,IAA2BnP,CAAA,EAEjCqG,EAA8BrG,CAAA,EACtC,CACA,EACD,MAAMiP,GAAA,YAAAA,EAAkB,OAAQ,KAChC,IAAK7O,GACJC,EACA,IAAM,CACD6O,IACHC,EAAK,IAAM,GAEb,EACCzO,GAAU,CACNwO,GACEC,EAAA,cAAc,IAAIzO,CAAK,CAE9B,CACD,EACA,MAAM,MAAMoB,EAAUJ,EAAM,CAEvB,IAAA8N,EAEA1N,aAAoB,SACvB0N,EAAY1N,EAAS,IAIdJ,EAAA,CAGN,KACCI,EAAS,SAAW,OAASA,EAAS,SAAW,OAC9C,OACA,MAAMA,EAAS,KAAK,EACxB,MAAOA,EAAS,MAChB,YAAaA,EAAS,YACtB,QAASA,EAAS,QAClB,UAAWA,EAAS,UACpB,UAAWA,EAAS,UACpB,OAAQA,EAAS,OACjB,KAAMA,EAAS,KACf,SAAUA,EAAS,SACnB,SAAUA,EAAS,SACnB,eAAgBA,EAAS,eACzB,OAAQA,EAAS,OACjB,GAAGJ,CAAA,GAGQ8N,EAAA1N,EAIb,MAAMO,EAAW,IAAI,IAAImN,EAAWnP,CAAG,EACvC,OAAI6O,GACHE,EAAQ/M,EAAS,IAAI,EAIlBA,EAAS,SAAWhC,EAAI,SAC3BmP,EAAYnN,EAAS,KAAK,MAAMhC,EAAI,OAAO,MAAM,GAI3CwL,EACJzJ,GAAiBoN,EAAWnN,EAAS,KAAMX,CAAI,EAC/CG,GAAc2N,EAAW9N,CAAI,CACjC,EACA,WAAY,IAAM,CAAC,EACnB,QAAA0N,EACA,QAAS,CACR,OAAIF,IACHC,EAAK,OAAS,IAERhJ,EAAO,CACf,EACA,QAAQkI,EAAI,CACGa,EAAA,GACV,GAAA,CACH,OAAOb,EAAG,CAAA,QACT,CACaa,EAAA,EACf,CACD,CAAA,EAwBAhK,EAAQ,MAAMwJ,EAAK,UAAU,KAAK,KAAK,KAAMa,CAAU,GAAM,IAE/D,CAEO,MAAA,CACN,KAAAb,EACA,OAAAM,EACA,OAAQC,EACR,WAAWtC,EAAA+B,EAAK,YAAL,MAAA/B,EAAgB,KAAO,CAAE,KAAM,OAAQ,KAAAzH,EAAM,KAAAiK,CAAA,EAAS,KACjE,KAAMjK,IAAQ+J,GAAA,YAAAA,EAAkB,OAAQ,KACxC,QAAOQ,EAAAf,EAAK,YAAL,YAAAe,EAAgB,iBAAiBR,GAAA,YAAAA,EAAkB,MAAA,CAE5D,CAUA,SAASS,GACRC,EACAC,EACAC,EACAC,EACAX,EACApP,EACC,CACG,GAAAmM,GAA2B,MAAA,GAE/B,GAAI,CAACiD,EAAa,MAAA,GAIlB,GAFIA,EAAK,QAAUQ,GACfR,EAAK,OAASS,GACdT,EAAK,KAAOU,EAAoB,MAAA,GAEzB,UAAAE,KAAkBZ,EAAK,cAC7B,GAAAW,EAAsB,IAAIC,CAAc,EAAU,MAAA,GAG5C,UAAArP,KAASyO,EAAK,OACxB,GAAIpP,EAAOW,CAAK,IAAMkL,EAAQ,OAAOlL,CAAK,EAAU,MAAA,GAG1C,UAAAR,KAAQiP,EAAK,aACnB,GAAA7D,GAAY,KAAM+C,GAAOA,EAAG,IAAI,IAAInO,CAAI,CAAC,CAAC,EAAU,MAAA,GAGlD,MAAA,EACR,CAOA,SAAS8P,GAAiBtB,EAAMuB,EAAU,CACzC,OAAIvB,GAAA,YAAAA,EAAM,QAAS,OAAeA,GAC9BA,GAAA,YAAAA,EAAM,QAAS,OAAeuB,GAAY,KACvC,IACR,CAOA,SAASC,GAAmBC,EAASC,EAAS,CAC7C,GAAI,CAACD,EAAS,OAAO,IAAI,IAAIC,EAAQ,aAAa,KAAM,CAAA,EAExD,MAAMC,EAAU,IAAI,IAAI,CAAC,GAAGF,EAAQ,aAAa,KAAK,EAAG,GAAGC,EAAQ,aAAa,KAAA,CAAM,CAAC,EAExF,UAAWpQ,KAAOqQ,EAAS,CAC1B,MAAMC,EAAaH,EAAQ,aAAa,OAAOnQ,CAAG,EAC5CuQ,EAAaH,EAAQ,aAAa,OAAOpQ,CAAG,EAGjDsQ,EAAW,MAAO3P,GAAU4P,EAAW,SAAS5P,CAAK,CAAC,GACtD4P,EAAW,MAAO5P,GAAU2P,EAAW,SAAS3P,CAAK,CAAC,GAEtD0P,EAAQ,OAAOrQ,CAAG,CAEpB,CAEO,OAAAqQ,CACR,CAMA,SAASG,GAAc,CAAE,MAAAnG,EAAO,IAAAhK,EAAK,MAAAmD,EAAO,OAAAzD,GAAU,CAC9C,MAAA,CACN,KAAM,SACN,MAAO,CACN,MAAAsK,EACA,IAAAhK,EACA,MAAAmD,EACA,OAAAzD,EACA,OAAQ,CAAC,CACV,EACA,MAAO,CAAE,KAAAqM,EAAM,aAAc,EAAG,CAAA,CAElC,CAMA,eAAe0B,GAAW,CAAE,GAAArL,EAAI,aAAAgO,EAAc,IAAApQ,EAAK,OAAAN,EAAQ,MAAAyD,EAAO,QAAAqK,GAAW,CACxE,IAAArC,GAAA,YAAAA,EAAY,MAAO/I,EAEP,OAAA6J,EAAA,OAAOd,EAAW,KAAK,EAC/BA,EAAW,QAGnB,KAAM,CAAE,OAAA/G,EAAQ,QAAAD,EAAS,KAAAD,CAAA,EAASf,EAE5BkN,EAAU,CAAC,GAAGlM,EAASD,CAAI,EAKjCE,EAAO,QAASuK,GAAWA,GAAA,YAAAA,IAAW,MAAM,IAAM,CAAE,EAAC,EAC7C0B,EAAA,QAAS1B,GAAWA,GAAA,YAAAA,EAAS,KAAK,MAAM,IAAM,CAAE,EAAC,EAGzD,IAAI2B,EAAc,KACZ,MAAAd,EAAcjE,EAAQ,IAAMnJ,IAAOmJ,EAAQ,IAAI,SAAWA,EAAQ,IAAI,OAAS,GAC/EgE,EAAgBhE,EAAQ,MAAQpI,EAAM,KAAOoI,EAAQ,MAAM,GAAK,GAChEkE,EAAwBI,GAAmBtE,EAAQ,IAAKvL,CAAG,EAEjE,IAAIuQ,EAAiB,GACrB,MAAMC,EAAuBH,EAAQ,IAAI,CAAC1B,EAAQ9N,IAAM,OACjD,MAAA+O,EAAWrE,EAAQ,OAAO1K,CAAC,EAE3B4P,EACL,CAAC,EAAC9B,GAAA,MAAAA,EAAS,OACViB,GAAA,YAAAA,EAAU,UAAWjB,EAAO,CAAC,GAC7BU,GACCkB,EACAhB,EACAC,EACAC,GACApD,EAAAuD,EAAS,SAAT,YAAAvD,EAAiB,KACjB3M,CAAA,GAGH,OAAI+Q,IAEcF,EAAA,IAGXE,CAAA,CACP,EAEG,GAAAD,EAAqB,KAAK,OAAO,EAAG,CACnC,GAAA,CACWF,EAAA,MAAMI,GAAU1Q,EAAKwQ,CAAoB,QAC/CxG,EAAO,CACT,MAAA2G,EAAgB,MAAMC,EAAa5G,EAAO,CAAE,IAAAhK,EAAK,OAAAN,EAAQ,MAAO,CAAE,GAAA0C,CAAG,CAAA,CAAG,EAE1E,OAAA6J,EAAe,IAAIuB,CAAO,EACtB2C,GAAc,CAAE,MAAOQ,EAAe,IAAA3Q,EAAK,OAAAN,EAAQ,MAAAyD,EAAO,EAG3D0N,GAAqB,CAC3B,OAAQ9G,EAAWC,CAAK,EACxB,MAAO2G,EACP,IAAA3Q,EACA,MAAAmD,CAAA,CACA,CACF,CAEI,GAAAmN,EAAY,OAAS,WACjB,OAAAA,CAET,CAEA,MAAMQ,EAAoBR,GAAA,YAAAA,EAAa,MAEvC,IAAIhB,EAAiB,GAErB,MAAMyB,EAAkBV,EAAQ,IAAI,MAAO1B,EAAQ9N,IAAM,QACxD,GAAI,CAAC8N,EAAQ,OAGP,MAAAiB,EAAWrE,EAAQ,OAAO1K,CAAC,EAE3B+N,EAAmBkC,GAAA,YAAAA,EAAoBjQ,GAczC,IAVF,CAAC+N,GAAoBA,EAAiB,OAAS,SAChDD,EAAO,CAAC,KAAMiB,GAAA,YAAAA,EAAU,SACxB,CAACP,GACAC,EACAC,EACAC,EACAC,GACApD,GAAAuD,EAAS,YAAT,YAAAvD,GAAoB,KACpB3M,CAAA,EAEgB,OAAAkQ,EAId,GAFaN,EAAA,IAEbV,GAAA,YAAAA,EAAkB,QAAS,QAExB,MAAAA,EAGP,OAAOF,GAAU,CAChB,OAAQC,EAAO,CAAC,EAChB,IAAA3O,EACA,OAAAN,EACA,MAAAyD,EACA,OAAQ,SAAY,QACnB,MAAM0B,GAAO,CAAA,EACb,QAASmM,GAAI,EAAGA,GAAInQ,EAAGmQ,IAAK,EAC3B,OAAO,OAAOnM,IAAOwH,GAAA,MAAM0E,EAAgBC,EAAC,IAAvB,YAAA3E,GAA2B,IAAI,EAE9C,OAAAxH,EACR,EACA,iBAAkB8K,GAGjBf,IAAqB,QAAaD,EAAO,CAAC,EAAI,CAAE,KAAM,QAAWC,GAAoB,KACrFD,EAAO,CAAC,EAAIiB,GAAA,YAAAA,EAAU,OAAS,MAChC,CAAA,CACA,CAAA,CACD,EAGD,UAAW,KAAKmB,EAAiB,EAAE,MAAM,IAAM,CAAA,CAAE,EAGjD,MAAM7C,EAAS,CAAA,EAEf,QAASrN,EAAI,EAAGA,EAAIwP,EAAQ,OAAQxP,GAAK,EACpC,GAAAwP,EAAQxP,CAAC,EACR,GAAA,CACHqN,EAAO,KAAK,MAAM6C,EAAgBlQ,CAAC,CAAC,QAC5BoQ,EAAK,CACb,GAAIA,aAAexH,GACX,MAAA,CACN,KAAM,WACN,SAAUwH,EAAI,QAAA,EAIZ,GAAAhF,EAAe,IAAIuB,CAAO,EAC7B,OAAO2C,GAAc,CACpB,MAAO,MAAMS,EAAaK,EAAK,CAAE,OAAAvR,EAAQ,IAAAM,EAAK,MAAO,CAAE,GAAImD,EAAM,IAAM,EACvE,IAAAnD,EACA,OAAAN,EACA,MAAAyD,CAAA,CACA,EAGE,IAAAqG,EAASO,EAAWkH,CAAG,EAEvBjH,EAEJ,GAAI8G,GAAA,MAAAA,EAAmB,SAAyDG,GAG/EzH,EAAyDyH,EAAK,QAAUzH,EACxEQ,EAAwDiH,EAAK,cACnDA,aAAe1H,GACzBS,EAAQiH,EAAI,SACN,CAGN,GADgB,MAAM5G,EAAO,QAAQ,MAAM,EAEnC,OAAA,MAAMK,EAAkB1K,CAAG,EAGnCgK,EAAQ,MAAM4G,EAAaK,EAAK,CAAE,OAAAvR,EAAQ,IAAAM,EAAK,MAAO,CAAE,GAAImD,EAAM,EAAG,CAAG,CAAA,CACzE,CAEA,MAAM+N,EAAa,MAAMC,GAAwBtQ,EAAGqN,EAAQ9J,CAAM,EAClE,OAAI8M,EACIjD,GAAkC,CACxC,IAAAjO,EACA,OAAAN,EACA,OAAQwO,EAAO,MAAM,EAAGgD,EAAW,GAAG,EAAE,OAAOA,EAAW,IAAI,EAC9D,OAAA1H,EACA,MAAAQ,EACA,MAAA7G,CAAA,CACA,EAEM,MAAMiO,GAAgBpR,EAAK,CAAE,GAAImD,EAAM,EAAM,EAAA6G,EAAOR,CAAM,CAEnE,MAIA0E,EAAO,KAAK,MAAS,EAIvB,OAAOD,GAAkC,CACxC,IAAAjO,EACA,OAAAN,EACA,OAAAwO,EACA,OAAQ,IACR,MAAO,KACP,MAAA/K,EAEA,KAAMiN,EAAe,OAAY,IAAA,CACjC,CACF,CAQA,eAAee,GAAwBtQ,EAAGqN,EAAQ9J,EAAQ,CACzD,KAAOvD,KACF,GAAAuD,EAAOvD,CAAC,EAAG,CACd,IAAImQ,EAAInQ,EACD,KAAA,CAACqN,EAAO8C,CAAC,GAAQA,GAAA,EACpB,GAAA,CACI,MAAA,CACN,IAAKA,EAAI,EACT,KAAM,CACL,KAAM,MAAyD5M,EAAOvD,CAAC,EAAG,EAC1E,OAA2DuD,EAAOvD,CAAC,EACnE,KAAM,CAAC,EACP,OAAQ,KACR,UAAW,IACZ,CAAA,CACD,MACO,CACP,QACD,CACD,CAEF,CAWA,eAAegQ,GAAqB,CAAE,OAAArH,EAAQ,MAAAQ,EAAO,IAAAhK,EAAK,MAAAmD,GAAS,CAElE,MAAMzD,EAAS,CAAA,EAGf,IAAIkP,EAAmB,KAIvB,GAFuC5D,EAAI,aAAa,CAAC,IAAM,EAK1D,GAAA,CACH,MAAMsF,EAAc,MAAMI,GAAU1Q,EAAK,CAAC,EAAI,CAAC,EAE/C,GACCsQ,EAAY,OAAS,QACpBA,EAAY,MAAM,CAAC,GAAKA,EAAY,MAAM,CAAC,EAAE,OAAS,OAEjD,KAAA,GAGY1B,EAAA0B,EAAY,MAAM,CAAC,GAAK,IAAA,MACpC,EAGHtQ,EAAI,SAAWqF,GAAUrF,EAAI,WAAa,SAAS,UAAY2I,KAClE,MAAM+B,EAAkB1K,CAAG,CAE7B,CAGK,MAAAqR,EAAc,MAAM3C,GAAU,CACnC,OAAQ7D,GACR,IAAA7K,EACA,OAAAN,EACA,MAAAyD,EACA,OAAQ,IAAM,QAAQ,QAAQ,EAAE,EAChC,iBAAkBwM,GAAiBf,CAAgB,CAAA,CACnD,EAGK0C,EAAa,CAClB,KAAM,MAAMxG,EAAqB,EACjC,OAAQA,EACR,UAAW,KACX,OAAQ,KACR,KAAM,IAAA,EAGP,OAAOmD,GAAkC,CACxC,IAAAjO,EACA,OAAAN,EACA,OAAQ,CAAC2R,EAAaC,CAAU,EAChC,OAAA9H,EACA,MAAAQ,EACA,MAAO,IAAA,CACP,CACF,CASA,SAASuH,GAAsBvR,EAAKoQ,EAAc,CAE7C,GADA,CAACpQ,GACDqG,GAAgBrG,EAAKmG,CAAI,EAAG,OAG5B,IAAAqL,EACA,GAAA,CACQA,EAAAxG,EAAI,MAAM,QAAQ,CAAE,IAAK,IAAI,IAAIhL,CAAG,CAAA,CAAG,GAAKA,EAAI,cAChD,CAUJ,MACR,CAEM,MAAAX,EAAOsO,GAAa6D,CAAQ,EAElC,UAAWrO,KAASyH,GAAQ,CACrB,MAAAlL,EAASyD,EAAM,KAAK9D,CAAI,EAE9B,GAAIK,EAUI,MAPQ,CACd,GAHUM,EAAI,SAAWA,EAAI,OAI7B,aAAAoQ,EACA,MAAAjN,EACA,OAAQ1D,GAAcC,CAAM,EAC5B,IAAAM,CAAA,CAIH,CACD,CAGA,SAAS2N,GAAanO,EAAU,CAC/B,OAAOD,GAAgBC,EAAS,MAAM2G,EAAK,MAAM,GAAK,GAAG,CAC1D,CAUA,SAASsL,GAAiB,CAAE,IAAAzR,EAAK,KAAA8I,EAAM,OAAAyE,EAAQ,MAAAmE,GAAS,CACvD,IAAIC,EAAe,GAEnB,MAAMC,EAAMC,GAAkBtG,EAASgC,EAAQvN,EAAK8I,CAAI,EAEpD4I,IAAU,SACbE,EAAI,WAAW,MAAQF,GAGxB,MAAMI,EAAc,CACnB,GAAGF,EAAI,WACP,OAAQ,IAAM,CACED,EAAA,GACfC,EAAI,OAAO,IAAI,MAAM,sBAAsB,CAAC,CAC7C,CAAA,EAGD,OAAKlG,GAEJN,GAA0B,QAAS4C,GAAOA,EAAG8D,CAAW,CAAC,EAGnDH,EAAe,KAAOC,CAC9B,CAqBA,eAAevE,EAAS,CACvB,KAAAvE,EACA,IAAA9I,EACA,OAAA+R,EACA,UAAAvL,EACA,SAAAC,EACA,cAAAI,EACA,MAAAmL,EAAQ,CAAC,EACT,eAAA7E,EAAiB,EACjB,UAAAC,EAAY,CAAC,EACb,OAAA6E,EAAStH,GACT,MAAAuH,EAAQvH,EACT,EAAG,CACI,MAAA4C,EAASgE,GAAsBvR,EAAK,EAAK,EACzC4R,EAAMH,GAAiB,CAAE,IAAAzR,EAAK,KAAA8I,EAAM,MAAOiJ,GAAA,YAAAA,EAAQ,MAAO,OAAAxE,CAAA,CAAQ,EAExE,GAAI,CAACqE,EAAK,CACHM,IACN,MACD,CAGA,MAAMC,EAAyB3H,EACzB4H,EAA4B3H,EAE3BwH,IAEMvG,EAAA,GAETF,GACInB,EAAA,WAAW,IAAIuH,EAAI,UAAU,EAG7B5F,GAAAoB,EACR,IAAIiF,EAAoB9E,GAAW,MAAME,GAAWF,CAAM,EAE1D,GAAI,CAAC8E,EAAmB,CACnB,GAAAhM,GAAgBrG,EAAKmG,CAAI,EACrB,OAAA,MAAMuE,EAAkB1K,CAAG,EAEnCqS,EAAoB,MAAMjB,GACzBpR,EACA,CAAE,GAAI,IAAK,EACX,MAAM4Q,EAAa,IAAIjH,GAAe,IAAK,YAAa,cAAc3J,EAAI,QAAQ,EAAE,EAAG,CACtF,IAAAA,EACA,OAAQ,CAAC,EACT,MAAO,CAAE,GAAI,IAAK,CAAA,CAClB,EACD,GAAA,CAEF,CAOA,GAHAA,GAAMuN,GAAA,YAAAA,EAAQ,MAAOvN,EAGjBgM,KAAUoB,EACb,OAAAwE,EAAI,OAAO,IAAI,MAAM,oBAAoB,CAAC,EACnC,GAGJ,GAAAS,EAAkB,OAAS,WAE9B,GAAIlF,GAAkB,GACrBkF,EAAoB,MAAMxB,GAAqB,CAC9C,OAAQ,IACR,MAAO,MAAMD,EAAa,IAAI,MAAM,eAAe,EAAG,CACrD,IAAA5Q,EACA,OAAQ,CAAC,EACT,MAAO,CAAE,GAAI,IAAK,CAAA,CAClB,EACD,IAAAA,EACA,MAAO,CAAE,GAAI,IAAK,CAAA,CAClB,MAEK,QAAAiN,GAAA,IAAI,IAAIoF,EAAkB,SAAUrS,CAAG,EAAE,KAAM,GAAImN,EAAiB,EAAGC,CAAS,EAC/E,QAEyBiF,EAAkB,MAAM,KAAK,QAAW,KACzD,MAAMhI,EAAO,QAAQ,MAAM,GAE1C,MAAMK,EAAkB1K,CAAG,EAoB7B,GAdmB2M,KAInBrC,GAAwB6H,CAAsB,EAC9CvF,GAAiBwF,CAAyB,EAGtCC,EAAkB,MAAM,KAAK,IAAI,WAAarS,EAAI,WACrDA,EAAI,SAAWqS,EAAkB,MAAM,KAAK,IAAI,UAGzCL,EAAAD,EAASA,EAAO,MAAQC,EAE5B,CAACD,EAAQ,CAEN,MAAAO,EAASzL,EAAgB,EAAI,EAE7B0L,EAAQ,CACb,CAACrN,CAAa,EAAIsF,GAAyB8H,EAC3C,CAACnN,CAAgB,EAAIsF,GAA4B6H,EACjD,CAACtN,EAAU,EAAGgN,CAAA,GAGJnL,EAAgB,QAAQ,aAAe,QAAQ,WACvD,KAAK,QAAS0L,EAAO,GAAIvS,CAAG,EAE1B6G,GACJ0D,GAAqBC,EAAuBC,CAAwB,CAEtE,CAOA,GAJaU,EAAA,KAEKkH,EAAA,MAAM,KAAK,MAAQL,EAEjCxG,EAAS,CACZD,EAAU8G,EAAkB,MAGxBA,EAAkB,MAAM,OACTA,EAAA,MAAM,KAAK,IAAMrS,GAG9B,MAAAwS,GACL,MAAM,QAAQ,IACbnH,GAAsB,IAAK2C,GAC1BA,EAAsD4D,EAAI,UAAW,CACtE,CAAA,GAEA,OAA8CtR,GAAU,OAAOA,GAAU,UAAA,EAEvE,GAAAkS,EAAe,OAAS,EAAG,CAC9B,IAASC,EAAT,UAAmB,CAClBnH,EAA2BA,EAAyB,OAElD0C,GAAO,CAACwE,EAAe,SAASxE,CAAE,CAAA,CACpC,EAGDwE,EAAe,KAAKC,CAAO,EACFnH,EAAA,KAAK,GAAGkH,CAAc,CAChD,CAEK1G,GAAA,KAAKuG,EAAkB,KAAK,EACjBzG,GAAA,EAAA,MAELiC,GAAAwE,EAAmBrM,GAAQ,EAAK,EAGtC,KAAA,CAAE,cAAA0M,CAAkB,EAAA,SAG1B,MAAMC,GAAK,EAGX,MAAMpG,EAASwF,EAASA,EAAO,OAAStL,EAAWhB,GAAiB,EAAA,KAEpE,GAAIgG,GAAY,CACT,MAAAmH,EAAc5S,EAAI,MAAQ,SAAS,eAAe,mBAAmBA,EAAI,KAAK,MAAM,CAAC,CAAC,CAAC,EACzFuM,EACM,SAAAA,EAAO,EAAGA,EAAO,CAAC,EACjBqG,EAIVA,EAAY,eAAe,EAE3B,SAAS,EAAG,CAAC,CAEf,CAEM,MAAAC,EAEL,SAAS,gBAAkBH,GAG3B,SAAS,gBAAkB,SAAS,KAEjC,CAAClM,GAAa,CAACqM,GACNC,KAGArH,GAAA,GAET4G,EAAkB,MAAM,OAC3BtG,EAAOsG,EAAkB,MAAM,MAGnB3G,EAAA,GAET5C,IAAS,YACZgE,GAAiBrC,CAAwB,EAG1CmH,EAAI,OAAO,MAAS,EAEKtG,EAAA,QAAS0C,GACjCA,EAAyD4D,EAAI,UAAW,CAAA,EAGlEvH,EAAA,WAAW,IAAI,IAAI,CAG3B,CAUA,eAAe+G,GAAgBpR,EAAKmD,EAAO6G,EAAOR,EAAQ,CACrD,OAAAxJ,EAAI,SAAWqF,GAAUrF,EAAI,WAAa,SAAS,UAAY,CAAC2I,GAG5D,MAAMkI,GAAqB,CACjC,OAAArH,EACA,MAAAQ,EACA,IAAAhK,EACA,MAAAmD,CAAA,CACA,EAWK,MAAMuH,EAAkB1K,CAAG,CACnC,CAQA,SAAS+S,IAAgB,CAEpB,IAAAC,EAEMjI,EAAA,iBAAiB,YAAckI,GAAU,CAC5CjN,MAAAA,EAAiCiN,EAAM,OAE7C,aAAaD,CAAiB,EAC9BA,EAAoB,WAAW,IAAM,CACpCxF,EAAQxH,EAAQ,CAAC,GACf,EAAE,CAAA,CACL,EAGD,SAASkN,EAAID,EAAO,CACnBzF,EAAgCyF,EAAM,aAAa,EAAE,CAAC,EAAI,CAAA,CAC3D,CAEUlI,EAAA,iBAAiB,YAAamI,CAAG,EAC3CnI,EAAU,iBAAiB,aAAcmI,EAAK,CAAE,QAAS,GAAM,EAE/D,MAAMC,EAAW,IAAI,qBACnBC,GAAY,CACZ,UAAWb,KAASa,EACfb,EAAM,iBACT7E,GAAgD6E,EAAM,OAAQ,IAAA,EACrDY,EAAA,UAAUZ,EAAM,MAAM,EAGlC,EACA,CAAE,UAAW,CAAE,CAAA,EAOP,SAAA/E,EAAQ7H,EAAS0N,EAAU,CAC7B,MAAAnN,EAAIH,GAAYJ,EAASoF,CAAS,EACxC,GAAI,CAAC7E,EAAG,OAER,KAAM,CAAE,IAAAlG,EAAK,SAAAoG,EAAU,SAAAE,CAAa,EAAAL,GAAcC,EAAGC,CAAI,EACzD,GAAIC,GAAYE,EAAU,OAEpB,MAAA4G,EAAU3G,EAAmBL,CAAC,EAEhC,GAAA,CAACgH,EAAQ,OACR,GAAAmG,GAAYnG,EAAQ,aAAc,CAC/B,MAAAK,EAASgE,GAAsBvR,EAAK,EAAK,EAC3CuN,GAaFD,GAAcC,CAAM,CAEtB,MACU8F,GAAYnG,EAAQ,cAC9BQ,GAAkC1N,EAAK,QAAA,CAG1C,CAEA,SAASwS,GAAiB,CACzBW,EAAS,WAAW,EAEpB,UAAWjN,KAAK6E,EAAU,iBAAiB,GAAG,EAAG,CAChD,KAAM,CAAE,IAAA/K,EAAK,SAAAoG,EAAU,SAAAE,CAAa,EAAAL,GAAcC,EAAGC,CAAI,EACzD,GAAIC,GAAYE,EAAU,SAEpB,MAAA4G,EAAU3G,EAAmBL,CAAC,EAChCgH,EAAQ,SAERA,EAAQ,eAAiB9H,EAAmB,UAC/C+N,EAAS,QAAQjN,CAAC,EAGfgH,EAAQ,eAAiB9H,EAAmB,OAC/CsI,GAAkC1N,EAAK,QAAA,EAEzC,CACD,CAEAsL,EAAyB,KAAKkH,CAAc,EAC7BA,GAChB,CAOA,SAAS5B,EAAa5G,EAAOiJ,EAAO,CACnC,GAAIjJ,aAAiBT,GACpB,OAAOS,EAAM,KAQR,MAAAR,EAASO,EAAWC,CAAK,EACzBJ,EAAUK,GAAYD,CAAK,EAGhC,OAAAgB,EAAI,MAAM,YAAY,CAAE,MAAAhB,EAAO,MAAAiJ,EAAO,OAAAzJ,EAAQ,QAAAI,EAAS,GAAyB,CAAE,QAAAA,CAAQ,CAE5F,CA6FO,SAAS6C,GAAKzM,EAAK0B,EAAO,GAAI,CAOhC,OAFJ1B,EAAMsF,GAAYtF,CAAG,EAEjBA,EAAI,SAAWqF,EACX,QAAQ,OACd,IAAI,MAGA,mBACJ,CAAA,EAIK4H,GAAMjN,EAAK0B,EAAM,CAAC,CAC1B,CAwPA,SAASgL,IAAgB,OACxB,QAAQ,kBAAoB,SAMX,iBAAA,eAAiB4G,GAAM,CACvC,IAAI3B,EAAe,GAInB,GAFc5E,KAEV,CAACrB,EAAY,CAChB,MAAMkG,EAAMC,GAAkBtG,EAAS,OAAW,KAAM,OAAO,EAKzDwC,EAAa,CAClB,GAAG6D,EAAI,WACP,OAAQ,IAAM,CACED,EAAA,GACfC,EAAI,OAAO,IAAI,MAAM,sBAAsB,CAAC,CAC7C,CAAA,EAGDxG,GAA0B,QAAS4C,GAAOA,EAAGD,CAAU,CAAC,CACzD,CAEI4D,GACH2B,EAAE,eAAe,EACjBA,EAAE,YAAc,IAEhB,QAAQ,kBAAoB,MAC7B,CACA,EAED,iBAAiB,mBAAoB,IAAM,CACtC,SAAS,kBAAoB,UAClBvG,IACf,CACA,GAGIV,EAAA,UAAU,aAAV,MAAAA,EAAsB,UACZ0G,KAILhI,EAAA,iBAAiB,QAAS,MAAOkI,GAAU,OAKpD,GAFIA,EAAM,QAAUA,EAAM,QAAU,GAChCA,EAAM,SAAWA,EAAM,SAAWA,EAAM,UAAYA,EAAM,QAC1DA,EAAM,iBAAkB,OAE5B,MAAM,EAAIlN,GAAoCkN,EAAM,aAAa,EAAE,CAAC,EAAIlI,CAAA,EACxE,GAAI,CAAC,EAAG,OAEF,KAAA,CAAE,IAAA/K,EAAK,SAAAoG,EAAU,OAAAJ,EAAQ,SAAAM,CAAS,EAAIL,GAAc,EAAGE,CAAI,EACjE,GAAI,CAACnG,EAAK,OAGNgG,GAAAA,IAAW,WAAaA,IAAW,QACtC,GAAI,OAAO,SAAW,OAAQ,eACpBA,GAAUA,IAAW,QAC/B,OAGK,MAAAkH,EAAU3G,EAAmB,CAAC,EAkBhC,GANH,EAXwB,aAAa,cAYrCvG,EAAI,WAAa,SAAS,UAC1B,EAAEA,EAAI,WAAa,UAAYA,EAAI,WAAa,UAI7CsG,EAAU,OAGV,GAAAF,GAAY8G,EAAQ,OAAQ,CAC3BuE,GAAiB,CAAE,IAAAzR,EAAK,KAAM,MAAQ,CAAA,EAG5B0L,EAAA,GAEbuH,EAAM,eAAe,EAGtB,MACD,CAKA,KAAM,CAACM,EAAS5S,CAAI,EAAIX,EAAI,KAAK,MAAM,GAAG,EAC1C,GAAIW,IAAS,QAAa4S,IAAY3T,GAAW,QAAQ,EAAG,CAKrD,KAAA,CAAA,CAAG4T,CAAY,EAAIjI,EAAQ,IAAI,KAAK,MAAM,GAAG,EACnD,GAAIiI,IAAiB7S,EAAM,CAC1BsS,EAAM,eAAe,EAKjBtS,IAAS,IAAOA,IAAS,OAAS,EAAE,cAAc,eAAe,KAAK,IAAM,KAC/E,OAAO,SAAS,CAAE,IAAK,CAAG,CAAA,GAE1B0L,EAAA,EAAE,cAAc,eAAe1L,CAAI,IAAnC,MAAA0L,EAAsC,iBAGvC,MACD,CASA,GANkBV,EAAA,GAElBrB,GAAwBE,CAAqB,EAE7CiJ,EAAWzT,CAAG,EAEV,CAACkN,EAAQ,cAAe,OAGVvB,EAAA,EACnB,CAEAsH,EAAM,eAAe,EAIf,MAAA,IAAI,QAASS,GAAW,CAC7B,sBAAsB,IAAM,CAC3B,WAAWA,EAAQ,CAAC,CAAA,CACpB,EAED,WAAWA,EAAQ,GAAG,CAAA,CACtB,EAEQrG,EAAA,CACR,KAAM,OACN,IAAArN,EACA,UAAWkN,EAAQ,UACnB,SAAUA,EAAQ,SAClB,cAAeA,EAAQ,eAAiBlN,EAAI,OAAS,SAAS,IAAA,CAC9D,CAAA,CACD,EAES+K,EAAA,iBAAiB,SAAWkI,GAAU,CAC/C,GAAIA,EAAM,iBAAkB,OAEtB,MAAA9E,EACL,gBAAgB,UAAU,UAAU,KAAK8E,EAAM,MAAM,EAGhDU,EAAwEV,EAAM,UAIpF,KAFeU,GAAA,YAAAA,EAAW,aAAcxF,EAAK,UAE9B,MAAO,OAEtB,MAAMnO,EAAM,IAAI,KACd2T,GAAA,YAAAA,EAAW,aAAa,iBAAiBA,GAAA,YAAAA,EAAW,aAAexF,EAAK,MAAA,EAGtE,GAAA9H,GAAgBrG,EAAKmG,CAAI,EAAG,OAE1B,MAAAyN,EAA6CX,EAAM,OAEnD/F,EAAU3G,EAAmBqN,CAAU,EAC7C,GAAI1G,EAAQ,OAAQ,OAEpB+F,EAAM,eAAe,EACrBA,EAAM,gBAAgB,EAEhB,MAAApO,EAAO,IAAI,SAAS+O,CAAU,EAE9BC,EAAiBF,GAAA,YAAAA,EAAW,aAAa,QAC3CE,GACHhP,EAAK,OAAOgP,GAAgBF,GAAA,YAAAA,EAAW,aAAa,WAAY,EAAE,EAInE3T,EAAI,OAAS,IAAI,gBAAgB6E,CAAI,EAAE,SAAS,EAEvCwI,EAAA,CACR,KAAM,OACN,IAAArN,EACA,UAAWkN,EAAQ,UACnB,SAAUA,EAAQ,SAClB,cAAeA,EAAQ,eAAiBlN,EAAI,OAAS,SAAS,IAAA,CAC9D,CAAA,CACD,EAEgB,iBAAA,WAAY,MAAOiT,GAAU,OACzC,IAAA5G,EAAA4G,EAAM,QAAN,MAAA5G,EAAcnH,GAAgB,CAC3B,MAAA4O,EAAgBb,EAAM,MAAM/N,CAAa,EAK/C,GAJA8G,GAAQ,CAAA,EAIJ8H,IAAkBtJ,EAAuB,OAEvC,MAAA+B,EAASrC,EAAiB4J,CAAa,EACvC9B,EAAQiB,EAAM,MAAMjO,EAAU,GAAK,CAAA,EACnChF,EAAM,IAAI,IAAIiT,EAAM,MAAMhO,EAAY,GAAK,SAAS,IAAI,EACxD8O,EAAmBd,EAAM,MAAM9N,CAAgB,EAC/C6O,EAAiBpU,GAAW,QAAQ,IAAMA,GAAW2L,EAAQ,GAAG,EAItE,GAFCwI,IAAqBtJ,IAA6BmB,IAAiBoI,GAEvD,CAKZP,EAAWzT,CAAG,EAEGkK,EAAAM,CAAqB,EAAI/E,KACtC8G,GAAiB,SAAAA,EAAO,EAAGA,EAAO,CAAC,EAEnCyF,IAAUjG,EAAK,QACXA,EAAA,CAAE,GAAGA,EAAM,MAAAiG,GACblG,GAAA,KAAK,CAAE,KAAAC,CAAA,CAAM,GAGKvB,EAAAsJ,EACxB,MACD,CAEA,MAAMpC,EAAQoC,EAAgBtJ,EAE9B,MAAM6C,EAAS,CACd,KAAM,WACN,IAAArN,EACA,OAAQ,CACP,MAAAgS,EACA,OAAAzF,EACA,MAAAmF,CACD,EACA,OAAQ,IAAM,CACWlH,EAAAsJ,EACGrJ,EAAAsJ,CAC5B,EACA,MAAO,IAAM,CACJ,QAAA,GAAG,CAACrC,CAAK,CAClB,EACA,UAAW1F,EAAA,CACX,CAAA,SAKG,CAACL,EAAiB,CACrB,MAAM3L,EAAM,IAAI,IAAI,SAAS,IAAI,EACjCyT,EAAWzT,CAAG,CACf,CACD,CACA,EAED,iBAAiB,aAAc,IAAM,CAGhC2L,IACeA,EAAA,GACV,QAAA,aACP,CACC,GAAG,QAAQ,MACX,CAACzG,CAAa,EAAG,EAAEsF,EACnB,CAACrF,CAAgB,EAAGsF,CACrB,EACA,GACA,SAAS,IAAA,EAEX,CACA,EAKD,UAAWwJ,KAAQ,SAAS,iBAAiB,MAAM,EAC9CA,EAAK,MAAQ,SAAQA,EAAK,KAAOA,EAAK,MAG1B,iBAAA,WAAahB,GAAU,CAKnCA,EAAM,WACF5I,EAAA,WAAW,IAAI,IAAI,CAC3B,CACA,EAKD,SAASoJ,EAAWzT,EAAK,CACxBuL,EAAQ,IAAMvL,EACdqK,EAAO,KAAK,IAAI,CAAE,GAAG0B,EAAM,IAAA/L,EAAK,EAChCqK,EAAO,KAAK,QACb,CACD,CAcA,eAAemC,GACdxG,EACA,CAAE,OAAAwD,EAAS,IAAK,MAAAQ,EAAO,SAAAkK,EAAU,OAAAxU,EAAQ,MAAAyD,EAAO,KAAM2N,EAAmB,KAAA3C,CAAA,EACxE,CACUxF,GAAA,GAEX,MAAM3I,EAAM,IAAI,IAAI,SAAS,IAAI,GAK/B,CAAE,OAAAN,EAAS,GAAI,MAAAyD,EAAQ,CAAE,GAAI,IAAK,CAAA,EAAMoO,GAAsBvR,EAAK,EAAK,GAAK,CAAA,GAI3E,IAAAsD,EAEA,GAAA,CACH,MAAMyN,EAAkBmD,EAAS,IAAI,MAAO5P,EAAGzD,IAAM,CAC9C,MAAA+N,EAAmBkC,EAAkBjQ,CAAC,EAE5C,OAAI+N,GAAA,MAAAA,EAAkB,OACJA,EAAA,KAAOuF,GAAiBvF,EAAiB,IAAI,GAGxDF,GAAU,CAChB,OAAQ1D,EAAI,MAAM1G,CAAC,EACnB,IAAAtE,EACA,OAAAN,EACA,MAAAyD,EACA,OAAQ,SAAY,CACnB,MAAM0B,EAAO,CAAA,EACb,QAASmM,EAAI,EAAGA,EAAInQ,EAAGmQ,GAAK,EAC3B,OAAO,OAAOnM,GAAO,MAAMkM,EAAgBC,CAAC,GAAG,IAAI,EAE7C,OAAAnM,CACR,EACA,iBAAkB8K,GAAiBf,CAAgB,CAAA,CACnD,CAAA,CACD,EAGKV,EAAS,MAAM,QAAQ,IAAI6C,CAAe,EAE1CqD,EAAexJ,GAAO,KAAK,CAAC,CAAE,GAAAxI,CAAG,IAAMA,IAAOe,EAAM,EAAE,EAI5D,GAAIiR,EAAc,CACjB,MAAMjQ,EAAUiQ,EAAa,QAC7B,QAASvT,EAAI,EAAGA,EAAIsD,EAAQ,OAAQtD,IAC9BsD,EAAQtD,CAAC,GACNqN,EAAA,OAAOrN,EAAG,EAAG,MAAS,CAGhC,CAEAyC,EAAS2K,GAAkC,CAC1C,IAAAjO,EACA,OAAAN,EACA,OAAAwO,EACA,OAAA1E,EACA,MAAAQ,EACA,KAAAmE,EACA,MAAOiG,GAAgB,IAAA,CACvB,QACOpK,EAAO,CACf,GAAIA,aAAiBP,GAAU,CAG9B,MAAMiB,EAAkB,IAAI,IAAIV,EAAM,SAAU,SAAS,IAAI,CAAC,EAC9D,MACD,CAEA1G,EAAS,MAAMuN,GAAqB,CACnC,OAAQ9G,EAAWC,CAAK,EACxB,MAAO,MAAM4G,EAAa5G,EAAO,CAAE,IAAAhK,EAAK,OAAAN,EAAQ,MAAAyD,EAAO,EACvD,IAAAnD,EACA,MAAAmD,CAAA,CACA,CACF,CAEIG,EAAO,MAAM,OACTA,EAAA,MAAM,KAAK,MAAQ,CAAA,GAGhBuK,GAAAvK,EAAQ0C,EAAQ,EAAI,CAChC,CAOA,eAAe0K,GAAU1Q,EAAKyQ,EAAS,OAChC,MAAA4D,EAAW,IAAI,IAAIrU,CAAG,EACnBqU,EAAA,SAAW3T,GAAgBV,EAAI,QAAQ,EAC5CA,EAAI,SAAS,SAAS,GAAG,GACnBqU,EAAA,aAAa,OAAOvK,GAAsB,GAAG,EAKvDuK,EAAS,aAAa,OAAOxK,GAAmB4G,EAAQ,IAAK5P,GAAOA,EAAI,IAAM,GAAI,EAAE,KAAK,EAAE,CAAC,EAE5F,MAAMgH,EAAM,MAAM1G,GAAakT,EAAS,IAAI,EAExC,GAAA,CAACxM,EAAI,GAAI,CAMR,IAAA+B,EACJ,MAAIyC,EAAAxE,EAAI,QAAQ,IAAI,cAAc,IAA9B,MAAAwE,EAAiC,SAAS,oBACnCzC,EAAA,MAAM/B,EAAI,OACVA,EAAI,SAAW,IACf+B,EAAA,YACA/B,EAAI,SAAW,MACf+B,EAAA,kBAEL,IAAIL,GAAU1B,EAAI,OAAQ+B,CAAO,CACxC,CAIO,OAAA,IAAI,QAAQ,MAAO0K,GAAY,OAK/B,MAAAC,MAAgB,IAChBC,EAAoD3M,EAAI,KAAM,UAAU,EACxE4M,EAAU,IAAI,YAKpB,SAASC,EAAY7P,EAAM,CACnB,OAAA8P,GAAkB9P,EAAM,CAC9B,QAAUzC,GACF,IAAI,QAAQ,CAACsR,EAAQkB,IAAW,CACtCL,EAAU,IAAInS,EAAI,CAAE,OAAAsR,EAAQ,OAAAkB,CAAQ,CAAA,CAAA,CACpC,CACF,CACA,CACF,CAEA,IAAI5T,EAAO,GAEX,OAAa,CAEZ,KAAM,CAAE,KAAA6T,EAAM,MAAAvU,CAAA,EAAU,MAAMkU,EAAO,KAAK,EAC1C,GAAIK,GAAQ,CAAC7T,EAAM,MAInB,IAFQA,GAAA,CAACV,GAASU,EAAO;AAAA,EAAOyT,EAAQ,OAAOnU,EAAO,CAAE,OAAQ,EAAM,CAAA,IAEzD,CACN,MAAAwU,EAAQ9T,EAAK,QAAQ;AAAA,CAAI,EAC/B,GAAI8T,IAAU,GACb,MAGD,MAAMzG,EAAO,KAAK,MAAMrN,EAAK,MAAM,EAAG8T,CAAK,CAAC,EAGxC,GAFG9T,EAAAA,EAAK,MAAM8T,EAAQ,CAAC,EAEvBzG,EAAK,OAAS,WACjB,OAAOiG,EAAQjG,CAAI,EAGhB,GAAAA,EAAK,OAAS,QAEZhC,EAAAgC,EAAA,QAAA,MAAAhC,EAAO,QAA4BgC,GAAS,EAC5CA,GAAAA,YAAAA,EAAM,QAAS,SAClBA,EAAK,KAAO8F,GAAiB9F,EAAK,IAAI,EACtCA,EAAK,KAAOqG,EAAYrG,EAAK,IAAI,EAClC,GAGDiG,EAAQjG,CAAI,UACFA,EAAK,OAAS,QAAS,CAEjC,KAAM,CAAE,GAAAjM,EAAI,KAAAyC,EAAM,MAAAmF,CAAA,EAAUqE,EACtB0G,EAAoDR,EAAU,IAAInS,CAAE,EAC1EmS,EAAU,OAAOnS,CAAE,EAEf4H,EACM+K,EAAA,OAAOL,EAAY1K,CAAK,CAAC,EAEzB+K,EAAA,OAAOL,EAAY7P,CAAI,CAAC,CAEnC,CACD,CACD,CAAA,CACA,CAGF,CAMA,SAASsP,GAAiBrF,EAAM,CACxB,MAAA,CACN,aAAc,IAAI,KAAIA,GAAA,YAAAA,EAAM,eAAgB,CAAA,CAAE,EAC9C,OAAQ,IAAI,KAAIA,GAAA,YAAAA,EAAM,SAAU,CAAA,CAAE,EAClC,OAAQ,CAAC,EAACA,GAAA,MAAAA,EAAM,QAChB,MAAO,CAAC,EAACA,GAAA,MAAAA,EAAM,OACf,IAAK,CAAC,EAACA,GAAA,MAAAA,EAAM,KACb,cAAe,IAAI,KAAIA,GAAA,YAAAA,EAAM,gBAAiB,CAAA,CAAE,CAAA,CAElD,CAEA,SAASgE,IAAc,CAChB,MAAAkC,EAAY,SAAS,cAAc,aAAa,EACtD,GAAIA,EAEHA,EAAU,MAAM,MACV,CAMN,MAAMlJ,EAAO,SAAS,KAChBmJ,EAAWnJ,EAAK,aAAa,UAAU,EAE7CA,EAAK,SAAW,GAEhBA,EAAK,MAAM,CAAE,cAAe,GAAM,aAAc,GAAO,EAGnDmJ,IAAa,KAChBnJ,EAAK,aAAa,WAAYmJ,CAAQ,EAEtCnJ,EAAK,gBAAgB,UAAU,EAKhC,MAAMoJ,EAAY,eAEd,GAAAA,GAAaA,EAAU,OAAS,OAAQ,CAE3C,MAAMC,EAAS,CAAA,EAEf,QAAStU,EAAI,EAAGA,EAAIqU,EAAU,WAAYrU,GAAK,EAC9CsU,EAAO,KAAKD,EAAU,WAAWrU,CAAC,CAAC,EAGpC,WAAW,IAAM,CACZ,GAAAqU,EAAU,aAAeC,EAAO,OAEpC,SAAStU,EAAI,EAAGA,EAAIqU,EAAU,WAAYrU,GAAK,EAAG,CAC3C,MAAAqF,EAAIiP,EAAOtU,CAAC,EACZuU,EAAIF,EAAU,WAAWrU,CAAC,EAIhC,GACCqF,EAAE,0BAA4BkP,EAAE,yBAChClP,EAAE,iBAAmBkP,EAAE,gBACvBlP,EAAE,eAAiBkP,EAAE,cACrBlP,EAAE,cAAgBkP,EAAE,aACpBlP,EAAE,YAAckP,EAAE,UAElB,MAEF,CAKAF,EAAU,gBAAgB,EAAA,CAC1B,CACF,CACD,CACD,CAQA,SAASrD,GAAkBtG,EAASgC,EAAQvN,EAAK8I,EAAM,SAElD,IAAA4K,EAGAkB,EAEJ,MAAMS,EAAW,IAAI,QAAQ,CAACC,EAAGC,IAAM,CAC7B7B,EAAA4B,EACAV,EAAAW,CAAA,CACT,EAGD,OAAAF,EAAS,MAAM,IAAM,CAAA,CAAE,EAmBhB,CACN,WAjBkB,CAClB,KAAM,CACL,OAAQ9J,EAAQ,OAChB,MAAO,CAAE,KAAIA,EAAAA,EAAQ,QAARA,YAAAA,EAAe,KAAM,IAAK,EACvC,IAAKA,EAAQ,GACd,EACA,GAAIvL,GAAO,CACV,QAAQuN,GAAA,YAAAA,EAAQ,SAAU,KAC1B,MAAO,CAAE,KAAIjB,EAAAiB,GAAA,YAAAA,EAAQ,QAAR,YAAAjB,EAAe,KAAM,IAAK,EACvC,IAAAtM,CACD,EACA,WAAY,CAACuN,EACb,KAAAzE,EACA,SAAAuM,CAAA,EAMA,OAAA3B,EAEA,OAAAkB,CAAA,CAEF", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]}