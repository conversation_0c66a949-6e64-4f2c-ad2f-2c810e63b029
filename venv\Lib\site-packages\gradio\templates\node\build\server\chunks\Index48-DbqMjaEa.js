import { c as create_ssr_component, v as validate_component, e as escape, f as each } from './ssr-C3HYbsxA.js';
import { m as mt, z as zA } from './2-DJbI4FWc.js';
import './index-ClteBeTX.js';
import './Component-NmRBwSfF.js';
import 'path';
import 'url';
import 'fs';

const B={code:".svelte-ei2xnu:where(._jsonList){list-style:none;margin:0;padding:0;padding-left:var(--jsonPaddingLeft, 1rem);border-left:var(--jsonBorderLeft, 1px dotted)}.svelte-ei2xnu:where(._jsonBkt){color:var(--jsonBracketColor, currentcolor)}.svelte-ei2xnu:where(._jsonBkt):not(.empty):hover{cursor:pointer;background:var(--jsonBracketHoverBackground, #e5e7eb)}.svelte-ei2xnu:where(._jsonSep){color:var(--jsonSeparatorColor, currentcolor)}.svelte-ei2xnu:where(._jsonKey){color:var(--jsonKeyColor, currentcolor)}.svelte-ei2xnu:where(._jsonVal){color:var(--jsonValColor, #9ca3af)}:where(._jsonVal).string.svelte-ei2xnu{color:var(--jsonValStringColor, #059669)}:where(._jsonVal).number.svelte-ei2xnu{color:var(--jsonValNumberColor, #d97706)}:where(._jsonVal).boolean.svelte-ei2xnu{color:var(--jsonValBooleanColor, #2563eb)}",map:`{"version":3,"file":"JsonView.svelte","sources":["JsonView.svelte"],"sourcesContent":["<script>\\n/** @type {*} - object or array to display */\\nexport let json\\n/** @type {number} - initial expansion depth */\\nexport let depth = Infinity\\nexport let _cur = 0\\nexport let _last = true\\n\\n/** @type {*[]} */\\nlet items\\nlet isArray = false\\nlet brackets = ['', '']\\nlet collapsed = false\\n\\n/**\\n * @param {*} i\\n * @returns {string}\\n */\\nfunction getType(i) {\\n  if (i === null) return 'null'\\n  return typeof i\\n}\\n\\n/**\\n * @param {*} i\\n * @returns {string}\\n */\\nfunction format(i) {\\n  const t = getType(i)\\n  if (t === 'string') return \`\\"\${i}\\"\`\\n  if (t === 'function') return 'f () {...}'\\n  if (t === 'symbol') return i.toString()\\n  return i\\n}\\n\\nfunction clicked() {\\n  collapsed = !collapsed\\n}\\n\\n/**\\n * @param {Event} e\\n */\\nfunction pressed(e) {\\n  if (e instanceof KeyboardEvent && ['Enter', ' '].includes(e.key)) clicked()\\n}\\n\\n$: {\\n  items = getType(json) === 'object' ? Object.keys(json) : []\\n  isArray = Array.isArray(json)\\n  brackets = isArray ? ['[', ']'] : ['{', '}']\\n}\\n\\n$: collapsed = depth < _cur\\n<\/script>\\n\\n{#if !items.length}\\n  <span class=\\"_jsonBkt empty\\" class:isArray>{brackets[0]}{brackets[1]}</span>{#if !_last}<span\\n      class=\\"_jsonSep\\">,</span\\n    >{/if}\\n{:else if collapsed}\\n  <span\\n    class=\\"_jsonBkt\\"\\n    class:isArray\\n    role=\\"button\\"\\n    tabindex=\\"0\\"\\n    on:click={clicked}\\n    on:keydown={pressed}>{brackets[0]}...{brackets[1]}</span\\n  >{#if !_last && collapsed}<span class=\\"_jsonSep\\">,</span>{/if}\\n{:else}\\n  <span\\n    class=\\"_jsonBkt\\"\\n    class:isArray\\n    role=\\"button\\"\\n    tabindex=\\"0\\"\\n    on:click={clicked}\\n    on:keydown={pressed}>{brackets[0]}</span\\n  >\\n  <ul class=\\"_jsonList\\">\\n    {#each items as i, idx}\\n      <li>\\n        {#if !isArray}\\n          <span class=\\"_jsonKey\\">\\"{i}\\"</span><span class=\\"_jsonSep\\">:</span>\\n        {/if}\\n        {#if getType(json[i]) === 'object'}\\n          <svelte:self json={json[i]} {depth} _cur={_cur + 1} _last={idx === items.length - 1} />\\n        {:else}\\n          <span class=\\"_jsonVal {getType(json[i])}\\">{format(json[i])}</span\\n          >{#if idx < items.length - 1}<span class=\\"_jsonSep\\">,</span>{/if}\\n        {/if}\\n      </li>\\n    {/each}\\n  </ul>\\n  <span\\n    class=\\"_jsonBkt\\"\\n    class:isArray\\n    role=\\"button\\"\\n    tabindex=\\"0\\"\\n    on:click={clicked}\\n    on:keydown={pressed}>{brackets[1]}</span\\n  >{#if !_last}<span class=\\"_jsonSep\\">,</span>{/if}\\n{/if}\\n\\n<style>\\n:where(._jsonList) {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n  padding-left: var(--jsonPaddingLeft, 1rem);\\n  border-left: var(--jsonBorderLeft, 1px dotted);\\n}\\n:where(._jsonBkt) {\\n  color: var(--jsonBracketColor, currentcolor);\\n}\\n:where(._jsonBkt):not(.empty):hover {\\n  cursor: pointer;\\n  background: var(--jsonBracketHoverBackground, #e5e7eb);\\n}\\n:where(._jsonSep) {\\n  color: var(--jsonSeparatorColor, currentcolor);\\n}\\n:where(._jsonKey) {\\n  color: var(--jsonKeyColor, currentcolor);\\n}\\n:where(._jsonVal) {\\n  color: var(--jsonValColor, #9ca3af);\\n}\\n:where(._jsonVal).string {\\n  color: var(--jsonValStringColor, #059669);\\n}\\n:where(._jsonVal).number {\\n  color: var(--jsonValNumberColor, #d97706);\\n}\\n:where(._jsonVal).boolean {\\n  color: var(--jsonValBooleanColor, #2563eb);\\n}</style>\\n"],"names":[],"mappings":"cAuGA,OAAO,UAAU,CAAE,CACjB,UAAU,CAAE,IAAI,CAChB,MAAM,CAAE,CAAC,CACT,OAAO,CAAE,CAAC,CACV,YAAY,CAAE,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAC1C,WAAW,CAAE,IAAI,gBAAgB,CAAC,WAAW,CAC/C,eACA,OAAO,SAAS,CAAE,CAChB,KAAK,CAAE,IAAI,kBAAkB,CAAC,aAAa,CAC7C,eACA,OAAO,SAAS,CAAC,KAAK,MAAM,CAAC,MAAO,CAClC,MAAM,CAAE,OAAO,CACf,UAAU,CAAE,IAAI,4BAA4B,CAAC,QAAQ,CACvD,eACA,OAAO,SAAS,CAAE,CAChB,KAAK,CAAE,IAAI,oBAAoB,CAAC,aAAa,CAC/C,eACA,OAAO,SAAS,CAAE,CAChB,KAAK,CAAE,IAAI,cAAc,CAAC,aAAa,CACzC,eACA,OAAO,SAAS,CAAE,CAChB,KAAK,CAAE,IAAI,cAAc,CAAC,QAAQ,CACpC,CACA,OAAO,SAAS,CAAC,qBAAQ,CACvB,KAAK,CAAE,IAAI,oBAAoB,CAAC,QAAQ,CAC1C,CACA,OAAO,SAAS,CAAC,qBAAQ,CACvB,KAAK,CAAE,IAAI,oBAAoB,CAAC,QAAQ,CAC1C,CACA,OAAO,SAAS,CAAC,sBAAS,CACxB,KAAK,CAAE,IAAI,qBAAqB,CAAC,QAAQ,CAC3C"}`};function d(s){return s===null?"null":typeof s}function k(s){const e=d(s);return e==="string"?`"${s}"`:e==="function"?"f () {...}":e==="symbol"?s.toString():s}const _=create_ssr_component((s,e,n,p)=>{let{json:t}=e,{depth:c=1/0}=e,{_cur:C=0}=e,{_last:A=!0}=e,r,o=!1,a=["",""],u=!1;return e.json===void 0&&n.json&&t!==void 0&&n.json(t),e.depth===void 0&&n.depth&&c!==void 0&&n.depth(c),e._cur===void 0&&n._cur&&C!==void 0&&n._cur(C),e._last===void 0&&n._last&&A!==void 0&&n._last(A),s.css.add(B),r=d(t)==="object"?Object.keys(t):[],o=Array.isArray(t),a=o?["[","]"]:["{","}"],u=c<C,`${r.length?`${u?`<span class="${["_jsonBkt svelte-ei2xnu",o?"isArray":""].join(" ").trim()}" role="button" tabindex="0">${escape(a[0])}...${escape(a[1])}</span>${!A&&u?'<span class="_jsonSep svelte-ei2xnu" data-svelte-h="svelte-1inngla">,</span>':""}`:`<span class="${["_jsonBkt svelte-ei2xnu",o?"isArray":""].join(" ").trim()}" role="button" tabindex="0">${escape(a[0])}</span> <ul class="_jsonList svelte-ei2xnu">${each(r,(l,j)=>`<li class="svelte-ei2xnu">${o?"":`<span class="_jsonKey svelte-ei2xnu">&quot;${escape(l)}&quot;</span><span class="_jsonSep svelte-ei2xnu" data-svelte-h="svelte-168684w">:</span>`} ${d(t[l])==="object"?`${validate_component(_,"svelte:self").$$render(s,{json:t[l],depth:c,_cur:C+1,_last:j===r.length-1},{},{})}`:`<span class="${"_jsonVal "+escape(d(t[l]),!0)+" svelte-ei2xnu"}">${escape(k(t[l]))}</span>${j<r.length-1?'<span class="_jsonSep svelte-ei2xnu" data-svelte-h="svelte-1inngla">,</span>':""}`} </li>`)}</ul> <span class="${["_jsonBkt svelte-ei2xnu",o?"isArray":""].join(" ").trim()}" role="button" tabindex="0">${escape(a[1])}</span>${A?"":'<span class="_jsonSep svelte-ei2xnu" data-svelte-h="svelte-1inngla">,</span>'}`}`:`<span class="${["_jsonBkt empty svelte-ei2xnu",o?"isArray":""].join(" ").trim()}">${escape(a[0])}${escape(a[1])}</span>${A?"":'<span class="_jsonSep svelte-ei2xnu" data-svelte-h="svelte-1f29ohw">,</span>'}`}`}),b=create_ssr_component((s,e,n,p)=>{let{elem_id:t=""}=e,{elem_classes:c=[]}=e,{visible:C=!0}=e,{value:A=!1}=e,{container:r=!0}=e,{scale:o=null}=e,{min_width:a=void 0}=e,{loading_status:u}=e,{gradio:l}=e;return e.elem_id===void 0&&n.elem_id&&t!==void 0&&n.elem_id(t),e.elem_classes===void 0&&n.elem_classes&&c!==void 0&&n.elem_classes(c),e.visible===void 0&&n.visible&&C!==void 0&&n.visible(C),e.value===void 0&&n.value&&A!==void 0&&n.value(A),e.container===void 0&&n.container&&r!==void 0&&n.container(r),e.scale===void 0&&n.scale&&o!==void 0&&n.scale(o),e.min_width===void 0&&n.min_width&&a!==void 0&&n.min_width(a),e.loading_status===void 0&&n.loading_status&&u!==void 0&&n.loading_status(u),e.gradio===void 0&&n.gradio&&l!==void 0&&n.gradio(l),`${validate_component(mt,"Block").$$render(s,{visible:C,elem_id:t,elem_classes:c,container:r,scale:o,min_width:a},{},{default:()=>`${u?`${validate_component(zA,"StatusTracker").$$render(s,Object.assign({},{autoscroll:l.autoscroll},{i18n:l.i18n},u),{},{})}`:""} ${validate_component(_,"JsonView").$$render(s,{json:A},{},{})}`})}`});

export { b as default };
//# sourceMappingURL=Index48-DbqMjaEa.js.map
