# Llama.cpp Setup Guide

This guide will walk you through the process of downloading, installing, and configuring llama.cpp to work with Reverie Agents.

## 1. Download Llama.cpp

You can download the pre-compiled binaries for your operating system from the official llama.cpp repository.

- **Windows:** [https://github.com/ggerganov/llama.cpp/releases](https://github.com/ggerganov/llama.cpp/releases)

Download the latest release that is compatible with your system (e.g., `llama-bXXXX-bin-win-avx2-x64.zip`).

## 2. Installation

1.  Create a new folder named `llama.cpp` in a convenient location on your system (e.g., `C:\Tools\llama.cpp`).
2.  Extract the contents of the downloaded zip file into this new folder.

## 3. Add to Environment Variables

To make the `llama-server` command accessible from anywhere in the terminal, you need to add the `llama.cpp` directory to your system's PATH environment variable.

1.  Press the **Windows Key**, type `env`, and select **Edit the system environment variables**.
2.  In the System Properties window, click the **Environment Variables...** button.
3.  In the Environment Variables window, under the **System variables** section, find and select the `Path` variable, then click **Edit...**.
4.  Click **New** and add the full path to your `llama.cpp` directory (e.g., `C:\Tools\llama.cpp`).
5.  Click **OK** on all windows to save the changes.

## 4. Verify Installation

Open a new terminal or command prompt and run the following command:

```bash
llama-server --version
```

If the installation was successful, you should see the version number of llama.cpp printed in the console.

## 5. Running the Llama.cpp Server

To use the Reverie Agents application, you'll need to have the llama.cpp server running in the background. Open a terminal and run the following command, replacing `your_model.gguf` with the actual name of your model file:

```bash
llama-server -m models/llm/your_model.gguf --port 8080
```

This will start the server and load your model. You can now run the Reverie Agents application, which will connect to this server to perform language model inference.
