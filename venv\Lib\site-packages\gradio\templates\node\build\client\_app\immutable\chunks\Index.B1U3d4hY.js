import{SvelteComponent as X,init as Y,safe_not_equal as Z,create_component as S,claim_component as I,mount_component as L,transition_in as T,transition_out as U,destroy_component as j,afterUpdate as y,assign as p,space as D,element as P,claim_space as z,claim_element as F,children as x,detach as v,attr as o,toggle_class as G,insert_hydration as q,append_hydration as H,set_input_value as J,listen as k,get_spread_update as $,get_spread_object as ee,to_number as K,run_all as le,tick as te,text as ie,claim_text as ae,set_data as ne}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{B as se,S as ue,y as fe}from"./2.B2AoQPnG.js";function me(a){let e;return{c(){e=ie(a[2])},l(l){e=ae(l,a[2])},m(l,n){q(l,e,n)},p(l,n){n&4&&ne(e,l[2])},d(l){l&&v(e)}}}function _e(a){let e,l,n,u,m,f,_,r,d;const h=[{autoscroll:a[1].autoscroll},{i18n:a[1].i18n},a[13]];let b={};for(let t=0;t<h.length;t+=1)b=p(b,h[t]);return e=new ue({props:b}),e.$on("clear_status",a[20]),u=new fe({props:{show_label:a[10],info:a[3],$$slots:{default:[me]},$$scope:{ctx:a}}}),{c(){S(e.$$.fragment),l=D(),n=P("label"),S(u.$$.fragment),m=D(),f=P("input"),this.h()},l(t){I(e.$$.fragment,t),l=z(t),n=F(t,"LABEL",{class:!0});var s=x(n);I(u.$$.fragment,s),m=z(s),f=F(s,"INPUT",{"aria-label":!0,type:!0,min:!0,max:!0,step:!0,placeholder:!0,class:!0}),s.forEach(v),this.h()},h(){o(f,"aria-label",a[2]),o(f,"type","number"),o(f,"min",a[11]),o(f,"max",a[12]),o(f,"step",a[14]),o(f,"placeholder",a[15]),f.disabled=a[16],o(f,"class","svelte-7ha85a"),o(n,"class","block svelte-7ha85a"),G(n,"container",a[7])},m(t,s){L(e,t,s),q(t,l,s),q(t,n,s),L(u,n,null),H(n,m),H(n,f),J(f,a[0]),_=!0,r||(d=[k(f,"input",a[21]),k(f,"keypress",a[17]),k(f,"blur",a[22]),k(f,"focus",a[23])],r=!0)},p(t,s){const g=s&8194?$(h,[s&2&&{autoscroll:t[1].autoscroll},s&2&&{i18n:t[1].i18n},s&8192&&ee(t[13])]):{};e.$set(g);const c={};s&1024&&(c.show_label=t[10]),s&8&&(c.info=t[3]),s&33554436&&(c.$$scope={dirty:s,ctx:t}),u.$set(c),(!_||s&4)&&o(f,"aria-label",t[2]),(!_||s&2048)&&o(f,"min",t[11]),(!_||s&4096)&&o(f,"max",t[12]),(!_||s&16384)&&o(f,"step",t[14]),(!_||s&32768)&&o(f,"placeholder",t[15]),(!_||s&65536)&&(f.disabled=t[16]),s&1&&K(f.value)!==t[0]&&J(f,t[0]),(!_||s&128)&&G(n,"container",t[7])},i(t){_||(T(e.$$.fragment,t),T(u.$$.fragment,t),_=!0)},o(t){U(e.$$.fragment,t),U(u.$$.fragment,t),_=!1},d(t){t&&(v(l),v(n)),j(e,t),j(u),r=!1,le(d)}}}function oe(a){let e,l;return e=new se({props:{visible:a[6],elem_id:a[4],elem_classes:a[5],padding:a[7],allow_overflow:!1,scale:a[8],min_width:a[9],$$slots:{default:[_e]},$$scope:{ctx:a}}}),{c(){S(e.$$.fragment)},l(n){I(e.$$.fragment,n)},m(n,u){L(e,n,u),l=!0},p(n,[u]){const m={};u&64&&(m.visible=n[6]),u&16&&(m.elem_id=n[4]),u&32&&(m.elem_classes=n[5]),u&128&&(m.padding=n[7]),u&256&&(m.scale=n[8]),u&512&&(m.min_width=n[9]),u&33684623&&(m.$$scope={dirty:u,ctx:n}),e.$set(m)},i(n){l||(T(e.$$.fragment,n),l=!0)},o(n){U(e.$$.fragment,n),l=!1},d(n){j(e,n)}}}function ce(a,e,l){let n,{gradio:u}=e,{label:m=u.i18n("number.number")}=e,{info:f=void 0}=e,{elem_id:_=""}=e,{elem_classes:r=[]}=e,{visible:d=!0}=e,{container:h=!0}=e,{scale:b=null}=e,{min_width:t=void 0}=e,{value:s=null}=e,{show_label:g}=e,{minimum:c=void 0}=e,{maximum:A=void 0}=e,{loading_status:B}=e,{value_is_output:w=!1}=e,{step:C=null}=e,{interactive:E}=e,{placeholder:N=""}=e;s===null&&N===""&&(s=0);function M(){s!==null&&!isNaN(s)&&(u.dispatch("change"),w||u.dispatch("input"))}y(()=>{l(18,w=!1)});async function O(i){await te(),i.key==="Enter"&&(i.preventDefault(),u.dispatch("submit"))}const Q=()=>u.dispatch("clear_status",B);function R(){s=K(this.value),l(0,s)}const V=()=>u.dispatch("blur"),W=()=>u.dispatch("focus");return a.$$set=i=>{"gradio"in i&&l(1,u=i.gradio),"label"in i&&l(2,m=i.label),"info"in i&&l(3,f=i.info),"elem_id"in i&&l(4,_=i.elem_id),"elem_classes"in i&&l(5,r=i.elem_classes),"visible"in i&&l(6,d=i.visible),"container"in i&&l(7,h=i.container),"scale"in i&&l(8,b=i.scale),"min_width"in i&&l(9,t=i.min_width),"value"in i&&l(0,s=i.value),"show_label"in i&&l(10,g=i.show_label),"minimum"in i&&l(11,c=i.minimum),"maximum"in i&&l(12,A=i.maximum),"loading_status"in i&&l(13,B=i.loading_status),"value_is_output"in i&&l(18,w=i.value_is_output),"step"in i&&l(14,C=i.step),"interactive"in i&&l(19,E=i.interactive),"placeholder"in i&&l(15,N=i.placeholder)},a.$$.update=()=>{a.$$.dirty&1&&M(),a.$$.dirty&524288&&l(16,n=!E)},[s,u,m,f,_,r,d,h,b,t,g,c,A,B,C,N,n,O,w,E,Q,R,V,W]}class de extends X{constructor(e){super(),Y(this,e,ce,oe,Z,{gradio:1,label:2,info:3,elem_id:4,elem_classes:5,visible:6,container:7,scale:8,min_width:9,value:0,show_label:10,minimum:11,maximum:12,loading_status:13,value_is_output:18,step:14,interactive:19,placeholder:15})}}export{de as default};
//# sourceMappingURL=Index.B1U3d4hY.js.map
