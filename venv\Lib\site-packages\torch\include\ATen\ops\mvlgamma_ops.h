#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API mvlgamma_out {
  using schema = at::Tensor & (const at::Tensor &, int64_t, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::mvlgamma";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "mvlgamma.out(Tensor self, int p, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, int64_t p, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, int64_t p, at::Tensor & out);
};

struct TORCH_API mvlgamma {
  using schema = at::Tensor (const at::Tensor &, int64_t);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::mvlgamma";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "mvlgamma(Tensor self, int p) -> Tensor";
  static at::Tensor call(const at::Tensor & self, int64_t p);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, int64_t p);
};

struct TORCH_API mvlgamma_ {
  using schema = at::Tensor & (at::Tensor &, int64_t);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::mvlgamma_";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "mvlgamma_(Tensor(a!) self, int p) -> Tensor(a!)";
  static at::Tensor & call(at::Tensor & self, int64_t p);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, at::Tensor & self, int64_t p);
};

}} // namespace at::_ops
