import{GLTFLoader as o,ArrayItem as a}from"./glTFLoader-9Z3KGax5.js";import{an as d,ao as _}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./bone-kZWM5-u7.js";import"./rawTexture-DmvUfjqF.js";import"./assetContainer-BRzQBugc.js";import"./objectModelMapping-BR4RdEzn.js";const s="KHR_texture_basisu";class c{constructor(e){this.name=s,this._loader=e,this.enabled=e.isExtensionUsed(s)}dispose(){this._loader=null}_loadTextureAsync(e,r,n){return o.LoadExtensionAsync(e,r,this.name,(i,l)=>{const m=r.sampler==null?o.DefaultSampler:a.Get(`${e}/sampler`,this._loader.gltf.samplers,r.sampler),u=a.Get(`${i}/source`,this._loader.gltf.images,l.source);return this._loader._createTextureAsync(e,m,u,p=>{n(p)},r._textureInfo.nonColorData?{useRGBAIfASTCBC7NotAvailableWhenUASTC:!0}:void 0,!r._textureInfo.nonColorData)})}}d(s);_(s,!0,t=>new c(t));export{c as KHR_texture_basisu};
//# sourceMappingURL=KHR_texture_basisu-BE88bGIm.js.map
