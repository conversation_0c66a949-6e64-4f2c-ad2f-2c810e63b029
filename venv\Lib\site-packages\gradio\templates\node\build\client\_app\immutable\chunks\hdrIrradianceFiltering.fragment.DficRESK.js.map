{"version": 3, "file": "hdrIrradianceFiltering.fragment.DficRESK.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/hdrIrradianceFiltering.fragment.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nimport \"./ShadersInclude/helperFunctions.js\";\nimport \"./ShadersInclude/importanceSampling.js\";\nimport \"./ShadersInclude/pbrBRDFFunctions.js\";\nimport \"./ShadersInclude/hdrFilteringFunctions.js\";\nconst name = \"hdrIrradianceFilteringPixelShader\";\nconst shader = `#include<helperFunctions>\n#include<importanceSampling>\n#include<pbrBRDFFunctions>\n#include<hdrFilteringFunctions>\nvar inputTextureSampler: sampler;var inputTexture: texture_cube<f32>;\n#ifdef IBL_CDF_FILTERING\nvar icdfTextureSampler: sampler;var icdfTexture: texture_2d<f32>;\n#endif\nuniform vFilteringInfo: vec2f;uniform hdrScale: f32;varying direction: vec3f;@fragment\nfn main(input: FragmentInputs)->FragmentOutputs {var color: vec3f=irradiance(inputTexture,inputTextureSampler,input.direction,uniforms.vFilteringInfo\n#ifdef IBL_CDF_FILTERING\n,icdfTexture,icdfTextureSampler\n#endif\n);fragmentOutputs.color= vec4f(color*uniforms.hdrScale,1.0);}`;\n// Sideeffect\nif (!ShaderStore.ShadersStoreWGSL[name]) {\n    ShaderStore.ShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const hdrIrradianceFilteringPixelShaderWGSL = { name, shader };\n//# sourceMappingURL=hdrIrradianceFiltering.fragment.js.map"], "names": ["name", "shader", "ShaderStore", "hdrIrradianceFilteringPixelShaderWGSL"], "mappings": "0HAMA,MAAMA,EAAO,oCACPC,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DAeVC,EAAY,iBAAiBF,CAAI,IAClCE,EAAY,iBAAiBF,CAAI,EAAIC,GAG7B,MAACE,EAAwC,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}