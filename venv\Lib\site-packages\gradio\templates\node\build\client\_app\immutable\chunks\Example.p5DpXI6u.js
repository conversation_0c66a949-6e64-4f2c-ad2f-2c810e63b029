import{SvelteComponent as o,init as r,safe_not_equal as u,text as s,claim_text as c,insert_hydration as f,set_data as m,noop as l,detach as _}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function d(i){let e=(i[0]||"")+"",a;return{c(){a=s(e)},l(t){a=c(t,e)},m(t,n){f(t,a,n)},p(t,[n]){n&1&&e!==(e=(t[0]||"")+"")&&m(a,e)},i:l,o:l,d(t){t&&_(a)}}}function p(i,e,a){let{value:t}=e;return i.$$set=n=>{"value"in n&&a(0,t=n.value)},[t]}class h extends o{constructor(e){super(),r(this,e,p,d,u,{value:0})}}export{h as default};
//# sourceMappingURL=Example.p5DpXI6u.js.map
