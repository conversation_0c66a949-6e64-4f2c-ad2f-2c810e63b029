#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _foreach_copy_ {
  using schema = void (at::TensorList, at::TensorList, bool);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_copy_";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "_foreach_copy_(Tensor(a!)[] self, Tensor[] src, bool non_blocking=False) -> ()";
  static void call(at::TensorList self, at::TensorList src, bool non_blocking);
  static void redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, at::TensorList src, bool non_blocking);
};

struct TORCH_API _foreach_copy {
  using schema = ::std::vector<at::Tensor> (at::TensorList, at::TensorList, bool);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_copy";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "_foreach_copy(Tensor[] self, Tensor[] src, bool non_blocking=False) -> Tensor[] self_out";
  static ::std::vector<at::Tensor> call(at::TensorList self, at::TensorList src, bool non_blocking);
  static ::std::vector<at::Tensor> redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, at::TensorList src, bool non_blocking);
};

struct TORCH_API _foreach_copy_out {
  using schema = void (at::TensorList, at::TensorList, bool, at::TensorList);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_foreach_copy";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "_foreach_copy.out(Tensor[] self, Tensor[] src, bool non_blocking=False, *, Tensor(a!)[] out) -> ()";
  static void call(at::TensorList self, at::TensorList src, bool non_blocking, at::TensorList out);
  static void redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList self, at::TensorList src, bool non_blocking, at::TensorList out);
};

}} // namespace at::_ops
