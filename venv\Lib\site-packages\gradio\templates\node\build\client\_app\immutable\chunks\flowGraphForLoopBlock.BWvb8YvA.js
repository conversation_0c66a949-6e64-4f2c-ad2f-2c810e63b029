import{b as p,g as s}from"./KHR_interactivity.DEAVS2UW.js";import{R as r,b as u,j as h,F as n}from"./declarationMapper.UBCwU7BT.js";import{R as d}from"./index.BoI39RQH.js";class i extends p{constructor(e){super(e),this.startIndex=this.registerDataInput("startIndex",r,0),this.endIndex=this.registerDataInput("endIndex",r),this.step=this.registerDataInput("step",u,1),this.index=this.registerDataOutput("index",h,new n(s((e==null?void 0:e.initialIndex)??0))),this.executionFlow=this._registerSignalOutput("executionFlow"),this.completed=this._registerSignalOutput("completed"),this._unregisterSignalOutput("out")}_execute(e){const l=s(this.startIndex.getValue(e)),o=this.step.getValue(e);let a=s(this.endIndex.getValue(e));for(let t=l;t<a&&(this.index.setValue(new n(t),e),this.executionFlow._activateSignal(e),a=s(this.endIndex.getValue(e)),!(t>i.MaxLoopIterations));t+=o);this.completed._activateSignal(e)}getClassName(){return"FlowGraphForLoopBlock"}}i.MaxLoopIterations=1e3;d("FlowGraphForLoopBlock",i);export{i as FlowGraphForLoopBlock};
//# sourceMappingURL=flowGraphForLoopBlock.BWvb8YvA.js.map
