import { c as create_ssr_component, v as validate_component, h as add_styles, b as createEventDispatcher, e as escape } from './ssr-C3HYbsxA.js';
import { m as mt, e as bt, O as jt, z as zA, ao as y } from './2-DJbI4FWc.js';
import './index-ClteBeTX.js';
import './Component-NmRBwSfF.js';
import 'path';
import 'url';
import 'fs';

const L={code:".hide.svelte-ydeks8{display:none}",map:`{"version":3,"file":"HTML.svelte","sources":["HTML.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { createEventDispatcher } from \\"svelte\\";\\nexport let elem_classes = [];\\nexport let value;\\nexport let visible = true;\\nconst dispatch = createEventDispatcher();\\n$: value, dispatch(\\"change\\");\\n<\/script>\\n\\n<!-- svelte-ignore a11y-click-events-have-key-events a11y-no-static-element-interactions -->\\n<div\\n\\tclass=\\"prose {elem_classes.join(' ')}\\"\\n\\tclass:hide={!visible}\\n\\ton:click={() => dispatch(\\"click\\")}\\n>\\n\\t{@html value}\\n</div>\\n\\n<style>\\n\\t.hide {\\n\\t\\tdisplay: none;\\n\\t}</style>\\n"],"names":[],"mappings":"AAkBC,mBAAM,CACL,OAAO,CAAE,IACV"}`},H=create_ssr_component((l,e,t,g)=>{let{elem_classes:i=[]}=e,{value:n}=e,{visible:a=!0}=e;const s=createEventDispatcher();return e.elem_classes===void 0&&t.elem_classes&&i!==void 0&&t.elem_classes(i),e.value===void 0&&t.value&&n!==void 0&&t.value(n),e.visible===void 0&&t.visible&&a!==void 0&&t.visible(a),l.css.add(L),s("change"),` <div class="${["prose "+escape(i.join(" "),!0)+" svelte-ydeks8",a?"":"hide"].join(" ").trim()}"><!-- HTML_TAG_START -->${n}<!-- HTML_TAG_END --> </div>`}),M={code:".padding.svelte-phx28p{padding:var(--block-padding)}div.svelte-phx28p{transition:150ms}.pending.svelte-phx28p{opacity:0.2}",map:'{"version":3,"file":"Index.svelte","sources":["Index.svelte"],"sourcesContent":["<script lang=\\"ts\\">import HTML from \\"./shared/HTML.svelte\\";\\nimport { StatusTracker } from \\"@gradio/statustracker\\";\\nimport { Block, BlockLabel } from \\"@gradio/atoms\\";\\nimport { Code as CodeIcon } from \\"@gradio/icons\\";\\nimport { css_units } from \\"@gradio/utils\\";\\nexport let label = \\"HTML\\";\\nexport let elem_id = \\"\\";\\nexport let elem_classes = [];\\nexport let visible = true;\\nexport let value = \\"\\";\\nexport let loading_status;\\nexport let gradio;\\nexport let show_label = false;\\nexport let min_height = void 0;\\nexport let max_height = void 0;\\nexport let container = false;\\nexport let padding = true;\\n<\/script>\\n\\n<Block {visible} {elem_id} {elem_classes} {container} padding={false}>\\n\\t{#if show_label}\\n\\t\\t<BlockLabel Icon={CodeIcon} {show_label} {label} float={false} />\\n\\t{/if}\\n\\n\\t<StatusTracker\\n\\t\\tautoscroll={gradio.autoscroll}\\n\\t\\ti18n={gradio.i18n}\\n\\t\\t{...loading_status}\\n\\t\\tvariant=\\"center\\"\\n\\t\\ton:clear_status={() => gradio.dispatch(\\"clear_status\\", loading_status)}\\n\\t/>\\n\\t<div\\n\\t\\tclass=\\"html-container\\"\\n\\t\\tclass:padding\\n\\t\\tclass:pending={loading_status?.status === \\"pending\\"}\\n\\t\\tstyle:min-height={min_height && loading_status?.status !== \\"pending\\"\\n\\t\\t\\t? css_units(min_height)\\n\\t\\t\\t: undefined}\\n\\t\\tstyle:max-height={max_height ? css_units(max_height) : undefined}\\n\\t>\\n\\t\\t<HTML\\n\\t\\t\\t{value}\\n\\t\\t\\t{elem_classes}\\n\\t\\t\\t{visible}\\n\\t\\t\\ton:change={() => gradio.dispatch(\\"change\\")}\\n\\t\\t\\ton:click={() => gradio.dispatch(\\"click\\")}\\n\\t\\t/>\\n\\t</div>\\n</Block>\\n\\n<style>\\n\\t.padding {\\n\\t\\tpadding: var(--block-padding);\\n\\t}\\n\\n\\tdiv {\\n\\t\\ttransition: 150ms;\\n\\t}\\n\\n\\t.pending {\\n\\t\\topacity: 0.2;\\n\\t}</style>\\n"],"names":[],"mappings":"AAmDC,sBAAS,CACR,OAAO,CAAE,IAAI,eAAe,CAC7B,CAEA,iBAAI,CACH,UAAU,CAAE,KACb,CAEA,sBAAS,CACR,OAAO,CAAE,GACV"}'},j=create_ssr_component((l,e,t,g)=>{let{label:i="HTML"}=e,{elem_id:n=""}=e,{elem_classes:a=[]}=e,{visible:s=!0}=e,{value:h=""}=e,{loading_status:o}=e,{gradio:d}=e,{show_label:c=!1}=e,{min_height:r=void 0}=e,{max_height:v=void 0}=e,{container:_=!1}=e,{padding:u=!0}=e;return e.label===void 0&&t.label&&i!==void 0&&t.label(i),e.elem_id===void 0&&t.elem_id&&n!==void 0&&t.elem_id(n),e.elem_classes===void 0&&t.elem_classes&&a!==void 0&&t.elem_classes(a),e.visible===void 0&&t.visible&&s!==void 0&&t.visible(s),e.value===void 0&&t.value&&h!==void 0&&t.value(h),e.loading_status===void 0&&t.loading_status&&o!==void 0&&t.loading_status(o),e.gradio===void 0&&t.gradio&&d!==void 0&&t.gradio(d),e.show_label===void 0&&t.show_label&&c!==void 0&&t.show_label(c),e.min_height===void 0&&t.min_height&&r!==void 0&&t.min_height(r),e.max_height===void 0&&t.max_height&&v!==void 0&&t.max_height(v),e.container===void 0&&t.container&&_!==void 0&&t.container(_),e.padding===void 0&&t.padding&&u!==void 0&&t.padding(u),l.css.add(M),`${validate_component(mt,"Block").$$render(l,{visible:s,elem_id:n,elem_classes:a,container:_,padding:!1},{},{default:()=>`${c?`${validate_component(bt,"BlockLabel").$$render(l,{Icon:jt,show_label:c,label:i,float:!1},{},{})}`:""} ${validate_component(zA,"StatusTracker").$$render(l,Object.assign({},{autoscroll:d.autoscroll},{i18n:d.i18n},o,{variant:"center"}),{},{})} <div class="${["html-container svelte-phx28p",(u?"padding":"")+" "+(o?.status==="pending"?"pending":"")].join(" ").trim()}"${add_styles({"min-height":r&&o?.status!=="pending"?y(r):void 0,"max-height":v?y(v):void 0})}>${validate_component(H,"HTML").$$render(l,{value:h,elem_classes:a,visible:s},{},{})}</div>`})}`});

export { j as default };
//# sourceMappingURL=Index50-DBS9dC6R.js.map
