import{F as i}from"./KHR_interactivity.DEAVS2UW.js";import{R as r,j as o,F as a}from"./declarationMapper.UBCwU7BT.js";import{R as p}from"./index.BoI39RQH.js";class h extends i{constructor(e){super(e),this.config=e,this.object=this.registerDataInput("object",r),this.array=this.registerDataInput("array",r),this.index=this.registerDataOutput("index",o,new a(-1))}_updateOutputs(e){const s=this.object.getValue(e),t=this.array.getValue(e);t&&this.index.setValue(new a(t.indexOf(s)),e)}serialize(e){super.serialize(e)}getClassName(){return"FlowGraphIndexOfBlock"}}p("FlowGraphIndexOfBlock",h);export{h as FlowGraphIndexOfBlock};
//# sourceMappingURL=flowGraphIndexOfBlock.cBkAYfIL.js.map
