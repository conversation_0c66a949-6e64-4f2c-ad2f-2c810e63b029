import { c as create_ssr_component, b as create<PERSON>ventDispatcher, o as on<PERSON><PERSON>roy, d as add_attribute } from './ssr-C3HYbsxA.js';

const C={code:".gradio-bokeh.svelte-1rhu6ax{display:flex;justify-content:center}",map:'{"version":3,"file":"BokehPlot.svelte","sources":["BokehPlot.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { onDestroy, createEventDispatcher } from \\"svelte\\";\\nexport let value;\\nexport let bokeh_version;\\nconst div_id = `bokehDiv-${Math.random().toString(5).substring(2)}`;\\nconst dispatch = createEventDispatcher();\\n$: plot = value?.plot;\\nasync function embed_bokeh(_plot) {\\n    if (document) {\\n        if (document.getElementById(div_id)) {\\n            document.getElementById(div_id).innerHTML = \\"\\";\\n        }\\n    }\\n    if (window.Bokeh) {\\n        load_bokeh();\\n        let plotObj = JSON.parse(_plot);\\n        const y = await window.Bokeh.embed.embed_item(plotObj, div_id);\\n        y._roots.forEach(async (p) => {\\n            await p.ready;\\n            dispatch(\\"load\\");\\n        });\\n    }\\n}\\n$: loaded && embed_bokeh(plot);\\nconst main_src = `https://cdn.bokeh.org/bokeh/release/bokeh-${bokeh_version}.min.js`;\\nconst plugins_src = [\\n    `https://cdn.pydata.org/bokeh/release/bokeh-widgets-${bokeh_version}.min.js`,\\n    `https://cdn.pydata.org/bokeh/release/bokeh-tables-${bokeh_version}.min.js`,\\n    `https://cdn.pydata.org/bokeh/release/bokeh-gl-${bokeh_version}.min.js`,\\n    `https://cdn.pydata.org/bokeh/release/bokeh-api-${bokeh_version}.min.js`\\n];\\nlet loaded = false;\\nasync function load_plugins() {\\n    await Promise.all(plugins_src.map((src, i) => {\\n        return new Promise((resolve) => {\\n            const script = document.createElement(\\"script\\");\\n            script.onload = resolve;\\n            script.src = src;\\n            document.head.appendChild(script);\\n            return script;\\n        });\\n    }));\\n    loaded = true;\\n}\\nlet plugin_scripts = [];\\nfunction handle_bokeh_loaded() {\\n    plugin_scripts = load_plugins();\\n}\\nfunction load_bokeh() {\\n    const script = document.createElement(\\"script\\");\\n    script.onload = handle_bokeh_loaded;\\n    script.src = main_src;\\n    const is_bokeh_script_present = document.head.querySelector(`script[src=\\"${main_src}\\"]`);\\n    if (!is_bokeh_script_present) {\\n        document.head.appendChild(script);\\n    }\\n    else {\\n        handle_bokeh_loaded();\\n    }\\n    return script;\\n}\\nconst main_script = bokeh_version ? load_bokeh() : null;\\nonDestroy(() => {\\n    if (main_script in document.children) {\\n        document.removeChild(main_script);\\n        plugin_scripts.forEach((child) => document.removeChild(child));\\n    }\\n});\\n<\/script>\\n\\n<div data-testid={\\"bokeh\\"} id={div_id} class=\\"gradio-bokeh\\" />\\n\\n<style>\\n\\t.gradio-bokeh {\\n\\t\\tdisplay: flex;\\n\\t\\tjustify-content: center;\\n\\t}</style>\\n"],"names":[],"mappings":"AAwEC,4BAAc,CACb,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAClB"}'},x=create_ssr_component((k,o,s,j)=>{let a,{value:r}=o,{bokeh_version:n}=o;const i=`bokehDiv-${Math.random().toString(5).substring(2)}`,v=createEventDispatcher();async function f(e){if(document&&document.getElementById(i)&&(document.getElementById(i).innerHTML=""),window.Bokeh){m();let d=JSON.parse(e);(await window.Bokeh.embed.embed_item(d,i))._roots.forEach(async t=>{await t.ready,v("load");});}}const c=`https://cdn.bokeh.org/bokeh/release/bokeh-${n}.min.js`,g=[`https://cdn.pydata.org/bokeh/release/bokeh-widgets-${n}.min.js`,`https://cdn.pydata.org/bokeh/release/bokeh-tables-${n}.min.js`,`https://cdn.pydata.org/bokeh/release/bokeh-gl-${n}.min.js`,`https://cdn.pydata.org/bokeh/release/bokeh-api-${n}.min.js`];let l=!1;async function y(){await Promise.all(g.map((e,d)=>new Promise(b=>{const t=document.createElement("script");return t.onload=b,t.src=e,document.head.appendChild(t),t}))),l=!0;}let h=[];function p(){h=y();}function m(){const e=document.createElement("script");return e.onload=p,e.src=c,document.head.querySelector(`script[src="${c}"]`)?p():document.head.appendChild(e),e}const _=n?m():null;return onDestroy(()=>{_ in document.children&&(document.removeChild(_),h.forEach(e=>document.removeChild(e)));}),o.value===void 0&&s.value&&r!==void 0&&s.value(r),o.bokeh_version===void 0&&s.bokeh_version&&n!==void 0&&s.bokeh_version(n),k.css.add(C),a=r?.plot,l&&f(a),`<div${add_attribute("data-testid","bokeh",0)}${add_attribute("id",i,0)} class="gradio-bokeh svelte-1rhu6ax"></div>`});

export { x as default };
//# sourceMappingURL=BokehPlot-DrxknxTG.js.map
