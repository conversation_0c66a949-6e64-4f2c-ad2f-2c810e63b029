{"version": 3, "file": "arc.CL2LCPr_.js", "sources": ["../../../../../../../../node_modules/.pnpm/d3-shape@3.2.0/node_modules/d3-shape/src/arc.js"], "sourcesContent": ["import constant from \"./constant.js\";\nimport {abs, acos, asin, atan2, cos, epsilon, halfPi, max, min, pi, sin, sqrt, tau} from \"./math.js\";\nimport {withPath} from \"./path.js\";\n\nfunction arcInnerRadius(d) {\n  return d.innerRadius;\n}\n\nfunction arcOuterRadius(d) {\n  return d.outerRadius;\n}\n\nfunction arcStartAngle(d) {\n  return d.startAngle;\n}\n\nfunction arcEndAngle(d) {\n  return d.endAngle;\n}\n\nfunction arcPadAngle(d) {\n  return d && d.padAngle; // Note: optional!\n}\n\nfunction intersect(x0, y0, x1, y1, x2, y2, x3, y3) {\n  var x10 = x1 - x0, y10 = y1 - y0,\n      x32 = x3 - x2, y32 = y3 - y2,\n      t = y32 * x10 - x32 * y10;\n  if (t * t < epsilon) return;\n  t = (x32 * (y0 - y2) - y32 * (x0 - x2)) / t;\n  return [x0 + t * x10, y0 + t * y10];\n}\n\n// Compute perpendicular offset line of length rc.\n// http://mathworld.wolfram.com/Circle-LineIntersection.html\nfunction cornerTangents(x0, y0, x1, y1, r1, rc, cw) {\n  var x01 = x0 - x1,\n      y01 = y0 - y1,\n      lo = (cw ? rc : -rc) / sqrt(x01 * x01 + y01 * y01),\n      ox = lo * y01,\n      oy = -lo * x01,\n      x11 = x0 + ox,\n      y11 = y0 + oy,\n      x10 = x1 + ox,\n      y10 = y1 + oy,\n      x00 = (x11 + x10) / 2,\n      y00 = (y11 + y10) / 2,\n      dx = x10 - x11,\n      dy = y10 - y11,\n      d2 = dx * dx + dy * dy,\n      r = r1 - rc,\n      D = x11 * y10 - x10 * y11,\n      d = (dy < 0 ? -1 : 1) * sqrt(max(0, r * r * d2 - D * D)),\n      cx0 = (D * dy - dx * d) / d2,\n      cy0 = (-D * dx - dy * d) / d2,\n      cx1 = (D * dy + dx * d) / d2,\n      cy1 = (-D * dx + dy * d) / d2,\n      dx0 = cx0 - x00,\n      dy0 = cy0 - y00,\n      dx1 = cx1 - x00,\n      dy1 = cy1 - y00;\n\n  // Pick the closer of the two intersection points.\n  // TODO Is there a faster way to determine which intersection to use?\n  if (dx0 * dx0 + dy0 * dy0 > dx1 * dx1 + dy1 * dy1) cx0 = cx1, cy0 = cy1;\n\n  return {\n    cx: cx0,\n    cy: cy0,\n    x01: -ox,\n    y01: -oy,\n    x11: cx0 * (r1 / r - 1),\n    y11: cy0 * (r1 / r - 1)\n  };\n}\n\nexport default function() {\n  var innerRadius = arcInnerRadius,\n      outerRadius = arcOuterRadius,\n      cornerRadius = constant(0),\n      padRadius = null,\n      startAngle = arcStartAngle,\n      endAngle = arcEndAngle,\n      padAngle = arcPadAngle,\n      context = null,\n      path = withPath(arc);\n\n  function arc() {\n    var buffer,\n        r,\n        r0 = +innerRadius.apply(this, arguments),\n        r1 = +outerRadius.apply(this, arguments),\n        a0 = startAngle.apply(this, arguments) - halfPi,\n        a1 = endAngle.apply(this, arguments) - halfPi,\n        da = abs(a1 - a0),\n        cw = a1 > a0;\n\n    if (!context) context = buffer = path();\n\n    // Ensure that the outer radius is always larger than the inner radius.\n    if (r1 < r0) r = r1, r1 = r0, r0 = r;\n\n    // Is it a point?\n    if (!(r1 > epsilon)) context.moveTo(0, 0);\n\n    // Or is it a circle or annulus?\n    else if (da > tau - epsilon) {\n      context.moveTo(r1 * cos(a0), r1 * sin(a0));\n      context.arc(0, 0, r1, a0, a1, !cw);\n      if (r0 > epsilon) {\n        context.moveTo(r0 * cos(a1), r0 * sin(a1));\n        context.arc(0, 0, r0, a1, a0, cw);\n      }\n    }\n\n    // Or is it a circular or annular sector?\n    else {\n      var a01 = a0,\n          a11 = a1,\n          a00 = a0,\n          a10 = a1,\n          da0 = da,\n          da1 = da,\n          ap = padAngle.apply(this, arguments) / 2,\n          rp = (ap > epsilon) && (padRadius ? +padRadius.apply(this, arguments) : sqrt(r0 * r0 + r1 * r1)),\n          rc = min(abs(r1 - r0) / 2, +cornerRadius.apply(this, arguments)),\n          rc0 = rc,\n          rc1 = rc,\n          t0,\n          t1;\n\n      // Apply padding? Note that since r1 ≥ r0, da1 ≥ da0.\n      if (rp > epsilon) {\n        var p0 = asin(rp / r0 * sin(ap)),\n            p1 = asin(rp / r1 * sin(ap));\n        if ((da0 -= p0 * 2) > epsilon) p0 *= (cw ? 1 : -1), a00 += p0, a10 -= p0;\n        else da0 = 0, a00 = a10 = (a0 + a1) / 2;\n        if ((da1 -= p1 * 2) > epsilon) p1 *= (cw ? 1 : -1), a01 += p1, a11 -= p1;\n        else da1 = 0, a01 = a11 = (a0 + a1) / 2;\n      }\n\n      var x01 = r1 * cos(a01),\n          y01 = r1 * sin(a01),\n          x10 = r0 * cos(a10),\n          y10 = r0 * sin(a10);\n\n      // Apply rounded corners?\n      if (rc > epsilon) {\n        var x11 = r1 * cos(a11),\n            y11 = r1 * sin(a11),\n            x00 = r0 * cos(a00),\n            y00 = r0 * sin(a00),\n            oc;\n\n        // Restrict the corner radius according to the sector angle. If this\n        // intersection fails, it’s probably because the arc is too small, so\n        // disable the corner radius entirely.\n        if (da < pi) {\n          if (oc = intersect(x01, y01, x00, y00, x11, y11, x10, y10)) {\n            var ax = x01 - oc[0],\n                ay = y01 - oc[1],\n                bx = x11 - oc[0],\n                by = y11 - oc[1],\n                kc = 1 / sin(acos((ax * bx + ay * by) / (sqrt(ax * ax + ay * ay) * sqrt(bx * bx + by * by))) / 2),\n                lc = sqrt(oc[0] * oc[0] + oc[1] * oc[1]);\n            rc0 = min(rc, (r0 - lc) / (kc - 1));\n            rc1 = min(rc, (r1 - lc) / (kc + 1));\n          } else {\n            rc0 = rc1 = 0;\n          }\n        }\n      }\n\n      // Is the sector collapsed to a line?\n      if (!(da1 > epsilon)) context.moveTo(x01, y01);\n\n      // Does the sector’s outer ring have rounded corners?\n      else if (rc1 > epsilon) {\n        t0 = cornerTangents(x00, y00, x01, y01, r1, rc1, cw);\n        t1 = cornerTangents(x11, y11, x10, y10, r1, rc1, cw);\n\n        context.moveTo(t0.cx + t0.x01, t0.cy + t0.y01);\n\n        // Have the corners merged?\n        if (rc1 < rc) context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);\n\n        // Otherwise, draw the two corners and the ring.\n        else {\n          context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);\n          context.arc(0, 0, r1, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), !cw);\n          context.arc(t1.cx, t1.cy, rc1, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);\n        }\n      }\n\n      // Or is the outer ring just a circular arc?\n      else context.moveTo(x01, y01), context.arc(0, 0, r1, a01, a11, !cw);\n\n      // Is there no inner ring, and it’s a circular sector?\n      // Or perhaps it’s an annular sector collapsed due to padding?\n      if (!(r0 > epsilon) || !(da0 > epsilon)) context.lineTo(x10, y10);\n\n      // Does the sector’s inner ring (or point) have rounded corners?\n      else if (rc0 > epsilon) {\n        t0 = cornerTangents(x10, y10, x11, y11, r0, -rc0, cw);\n        t1 = cornerTangents(x01, y01, x00, y00, r0, -rc0, cw);\n\n        context.lineTo(t0.cx + t0.x01, t0.cy + t0.y01);\n\n        // Have the corners merged?\n        if (rc0 < rc) context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);\n\n        // Otherwise, draw the two corners and the ring.\n        else {\n          context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);\n          context.arc(0, 0, r0, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), cw);\n          context.arc(t1.cx, t1.cy, rc0, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);\n        }\n      }\n\n      // Or is the inner ring just a circular arc?\n      else context.arc(0, 0, r0, a10, a00, cw);\n    }\n\n    context.closePath();\n\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  arc.centroid = function() {\n    var r = (+innerRadius.apply(this, arguments) + +outerRadius.apply(this, arguments)) / 2,\n        a = (+startAngle.apply(this, arguments) + +endAngle.apply(this, arguments)) / 2 - pi / 2;\n    return [cos(a) * r, sin(a) * r];\n  };\n\n  arc.innerRadius = function(_) {\n    return arguments.length ? (innerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : innerRadius;\n  };\n\n  arc.outerRadius = function(_) {\n    return arguments.length ? (outerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : outerRadius;\n  };\n\n  arc.cornerRadius = function(_) {\n    return arguments.length ? (cornerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : cornerRadius;\n  };\n\n  arc.padRadius = function(_) {\n    return arguments.length ? (padRadius = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), arc) : padRadius;\n  };\n\n  arc.startAngle = function(_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : startAngle;\n  };\n\n  arc.endAngle = function(_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : endAngle;\n  };\n\n  arc.padAngle = function(_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : padAngle;\n  };\n\n  arc.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), arc) : context;\n  };\n\n  return arc;\n}\n"], "names": ["arcInnerRadius", "d", "arcOuterRadius", "arcStartAngle", "arcEndAngle", "arcPadAngle", "intersect", "x0", "y0", "x1", "y1", "x2", "y2", "x3", "y3", "x10", "y10", "x32", "y32", "t", "epsilon", "cornerTangents", "r1", "rc", "cw", "x01", "y01", "lo", "sqrt", "ox", "oy", "x11", "y11", "x00", "y00", "dx", "dy", "d2", "r", "D", "max", "cx0", "cy0", "cx1", "cy1", "dx0", "dy0", "dx1", "dy1", "d3arc", "innerRadius", "outerRadius", "cornerRadius", "constant", "padRadius", "startAngle", "endAngle", "padAngle", "context", "path", "with<PERSON><PERSON>", "arc", "buffer", "r0", "a0", "halfPi", "a1", "da", "abs", "tau", "cos", "sin", "a01", "a11", "a00", "a10", "da0", "da1", "ap", "rp", "min", "rc0", "rc1", "t0", "t1", "p0", "asin", "p1", "oc", "pi", "ax", "ay", "bx", "by", "kc", "acos", "lc", "atan2", "a", "_"], "mappings": "iJAIA,SAASA,GAAeC,EAAG,CACzB,OAAOA,EAAE,WACX,CAEA,SAASC,GAAeD,EAAG,CACzB,OAAOA,EAAE,WACX,CAEA,SAASE,GAAcF,EAAG,CACxB,OAAOA,EAAE,UACX,CAEA,SAASG,GAAYH,EAAG,CACtB,OAAOA,EAAE,QACX,CAEA,SAASI,GAAYJ,EAAG,CACtB,OAAOA,GAAKA,EAAE,QAChB,CAEA,SAASK,GAAUC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAI,CACjD,IAAIC,EAAMN,EAAKF,EAAIS,EAAMN,EAAKF,EAC1BS,EAAMJ,EAAKF,EAAIO,EAAMJ,EAAKF,EAC1BO,EAAID,EAAMH,EAAME,EAAMD,EAC1B,GAAI,EAAAG,EAAIA,EAAIC,GACZ,OAAAD,GAAKF,GAAOT,EAAKI,GAAMM,GAAOX,EAAKI,IAAOQ,EACnC,CAACZ,EAAKY,EAAIJ,EAAKP,EAAKW,EAAIH,CAAG,CACpC,CAIA,SAASK,EAAed,EAAIC,EAAIC,EAAIC,EAAIY,EAAIC,EAAIC,EAAI,CAClD,IAAIC,EAAMlB,EAAKE,EACXiB,EAAMlB,EAAKE,EACXiB,GAAMH,EAAKD,EAAK,CAACA,GAAMK,EAAKH,EAAMA,EAAMC,EAAMA,CAAG,EACjDG,EAAKF,EAAKD,EACVI,EAAK,CAACH,EAAKF,EACXM,EAAMxB,EAAKsB,EACXG,EAAMxB,EAAKsB,EACXf,EAAMN,EAAKoB,EACXb,EAAMN,EAAKoB,EACXG,GAAOF,EAAMhB,GAAO,EACpBmB,GAAOF,EAAMhB,GAAO,EACpBmB,EAAKpB,EAAMgB,EACXK,EAAKpB,EAAMgB,EACXK,EAAKF,EAAKA,EAAKC,EAAKA,EACpBE,EAAIhB,EAAKC,EACTgB,EAAIR,EAAMf,EAAMD,EAAMiB,EACtB/B,GAAKmC,EAAK,EAAI,GAAK,GAAKR,EAAKY,GAAI,EAAGF,EAAIA,EAAID,EAAKE,EAAIA,CAAC,CAAC,EACvDE,GAAOF,EAAIH,EAAKD,EAAKlC,GAAKoC,EAC1BK,GAAO,CAACH,EAAIJ,EAAKC,EAAKnC,GAAKoC,EAC3BM,GAAOJ,EAAIH,EAAKD,EAAKlC,GAAKoC,EAC1BO,GAAO,CAACL,EAAIJ,EAAKC,EAAKnC,GAAKoC,EAC3BQ,EAAMJ,EAAMR,EACZa,EAAMJ,EAAMR,EACZa,EAAMJ,EAAMV,EACZe,EAAMJ,EAAMV,EAIhB,OAAIW,EAAMA,EAAMC,EAAMA,EAAMC,EAAMA,EAAMC,EAAMA,IAAKP,EAAME,EAAKD,EAAME,GAE7D,CACL,GAAIH,EACJ,GAAIC,EACJ,IAAK,CAACb,EACN,IAAK,CAACC,EACN,IAAKW,GAAOnB,EAAKgB,EAAI,GACrB,IAAKI,GAAOpB,EAAKgB,EAAI,EACzB,CACA,CAEe,SAAAW,IAAW,CACxB,IAAIC,EAAclD,GACdmD,EAAcjD,GACdkD,EAAeC,EAAS,CAAC,EACzBC,EAAY,KACZC,EAAapD,GACbqD,EAAWpD,GACXqD,EAAWpD,GACXqD,EAAU,KACVC,EAAOC,GAASC,CAAG,EAEvB,SAASA,GAAM,CACb,IAAIC,EACAxB,EACAyB,EAAK,CAACb,EAAY,MAAM,KAAM,SAAS,EACvC5B,EAAK,CAAC6B,EAAY,MAAM,KAAM,SAAS,EACvCa,EAAKT,EAAW,MAAM,KAAM,SAAS,EAAIU,GACzCC,EAAKV,EAAS,MAAM,KAAM,SAAS,EAAIS,GACvCE,EAAKC,GAAIF,EAAKF,CAAE,EAChBxC,EAAK0C,EAAKF,EAQd,GANKN,IAASA,EAAUI,EAASH,EAAI,GAGjCrC,EAAKyC,IAAIzB,EAAIhB,EAAIA,EAAKyC,EAAIA,EAAKzB,GAG/B,EAAEhB,EAAKF,GAAUsC,EAAQ,OAAO,EAAG,CAAC,UAG/BS,EAAKE,GAAMjD,EAClBsC,EAAQ,OAAOpC,EAAKgD,EAAIN,CAAE,EAAG1C,EAAKiD,EAAIP,CAAE,CAAC,EACzCN,EAAQ,IAAI,EAAG,EAAGpC,EAAI0C,EAAIE,EAAI,CAAC1C,CAAE,EAC7BuC,EAAK3C,IACPsC,EAAQ,OAAOK,EAAKO,EAAIJ,CAAE,EAAGH,EAAKQ,EAAIL,CAAE,CAAC,EACzCR,EAAQ,IAAI,EAAG,EAAGK,EAAIG,EAAIF,EAAIxC,CAAE,OAK/B,CACH,IAAIgD,EAAMR,EACNS,EAAMP,EACNQ,EAAMV,EACNW,EAAMT,EACNU,EAAMT,EACNU,EAAMV,EACNW,EAAKrB,EAAS,MAAM,KAAM,SAAS,EAAI,EACvCsB,EAAMD,EAAK1D,IAAakC,EAAY,CAACA,EAAU,MAAM,KAAM,SAAS,EAAI1B,EAAKmC,EAAKA,EAAKzC,EAAKA,CAAE,GAC9FC,EAAKyD,EAAIZ,GAAI9C,EAAKyC,CAAE,EAAI,EAAG,CAACX,EAAa,MAAM,KAAM,SAAS,CAAC,EAC/D6B,EAAM1D,EACN2D,EAAM3D,EACN4D,EACAC,EAGJ,GAAIL,EAAK3D,EAAS,CAChB,IAAIiE,EAAKC,GAAKP,EAAKhB,EAAKQ,EAAIO,CAAE,CAAC,EAC3BS,EAAKD,GAAKP,EAAKzD,EAAKiD,EAAIO,CAAE,CAAC,GAC1BF,GAAOS,EAAK,GAAKjE,GAASiE,GAAO7D,EAAK,EAAI,GAAKkD,GAAOW,EAAIV,GAAOU,IACjET,EAAM,EAAGF,EAAMC,GAAOX,EAAKE,GAAM,IACjCW,GAAOU,EAAK,GAAKnE,GAASmE,GAAO/D,EAAK,EAAI,GAAKgD,GAAOe,EAAId,GAAOc,IACjEV,EAAM,EAAGL,EAAMC,GAAOT,EAAKE,GAAM,EACvC,CAED,IAAIzC,EAAMH,EAAKgD,EAAIE,CAAG,EAClB9C,EAAMJ,EAAKiD,EAAIC,CAAG,EAClBzD,EAAMgD,EAAKO,EAAIK,CAAG,EAClB3D,EAAM+C,EAAKQ,EAAII,CAAG,EAGtB,GAAIpD,EAAKH,EAAS,CAChB,IAAIW,EAAMT,EAAKgD,EAAIG,CAAG,EAClBzC,EAAMV,EAAKiD,EAAIE,CAAG,EAClBxC,EAAM8B,EAAKO,EAAII,CAAG,EAClBxC,EAAM6B,EAAKQ,EAAIG,CAAG,EAClBc,EAKJ,GAAIrB,EAAKsB,GACP,GAAID,EAAKlF,GAAUmB,EAAKC,EAAKO,EAAKC,EAAKH,EAAKC,EAAKjB,EAAKC,CAAG,EAAG,CAC1D,IAAI0E,EAAKjE,EAAM+D,EAAG,CAAC,EACfG,EAAKjE,EAAM8D,EAAG,CAAC,EACfI,EAAK7D,EAAMyD,EAAG,CAAC,EACfK,EAAK7D,EAAMwD,EAAG,CAAC,EACfM,GAAK,EAAIvB,EAAIwB,IAAML,EAAKE,EAAKD,EAAKE,IAAOjE,EAAK8D,EAAKA,EAAKC,EAAKA,CAAE,EAAI/D,EAAKgE,EAAKA,EAAKC,EAAKA,CAAE,EAAE,EAAI,CAAC,EAChGG,GAAKpE,EAAK4D,EAAG,CAAC,EAAIA,EAAG,CAAC,EAAIA,EAAG,CAAC,EAAIA,EAAG,CAAC,CAAC,EAC3CP,EAAMD,EAAIzD,GAAKwC,EAAKiC,KAAOF,GAAK,EAAE,EAClCZ,EAAMF,EAAIzD,GAAKD,EAAK0E,KAAOF,GAAK,EAAE,CAC9C,MACYb,EAAMC,EAAM,CAGjB,CAGKL,EAAMzD,EAGH8D,EAAM9D,GACb+D,EAAK9D,EAAeY,EAAKC,EAAKT,EAAKC,EAAKJ,EAAI4D,EAAK1D,CAAE,EACnD4D,EAAK/D,EAAeU,EAAKC,EAAKjB,EAAKC,EAAKM,EAAI4D,EAAK1D,CAAE,EAEnDkC,EAAQ,OAAOyB,EAAG,GAAKA,EAAG,IAAKA,EAAG,GAAKA,EAAG,GAAG,EAGzCD,EAAM3D,EAAImC,EAAQ,IAAIyB,EAAG,GAAIA,EAAG,GAAID,EAAKe,EAAMd,EAAG,IAAKA,EAAG,GAAG,EAAGc,EAAMb,EAAG,IAAKA,EAAG,GAAG,EAAG,CAAC5D,CAAE,GAI5FkC,EAAQ,IAAIyB,EAAG,GAAIA,EAAG,GAAID,EAAKe,EAAMd,EAAG,IAAKA,EAAG,GAAG,EAAGc,EAAMd,EAAG,IAAKA,EAAG,GAAG,EAAG,CAAC3D,CAAE,EAChFkC,EAAQ,IAAI,EAAG,EAAGpC,EAAI2E,EAAMd,EAAG,GAAKA,EAAG,IAAKA,EAAG,GAAKA,EAAG,GAAG,EAAGc,EAAMb,EAAG,GAAKA,EAAG,IAAKA,EAAG,GAAKA,EAAG,GAAG,EAAG,CAAC5D,CAAE,EACvGkC,EAAQ,IAAI0B,EAAG,GAAIA,EAAG,GAAIF,EAAKe,EAAMb,EAAG,IAAKA,EAAG,GAAG,EAAGa,EAAMb,EAAG,IAAKA,EAAG,GAAG,EAAG,CAAC5D,CAAE,KAK/EkC,EAAQ,OAAOjC,EAAKC,CAAG,EAAGgC,EAAQ,IAAI,EAAG,EAAGpC,EAAIkD,EAAKC,EAAK,CAACjD,CAAE,GArB5CkC,EAAQ,OAAOjC,EAAKC,CAAG,EAyBzC,EAAEqC,EAAK3C,IAAY,EAAEwD,EAAMxD,GAAUsC,EAAQ,OAAO3C,EAAKC,CAAG,EAGvDiE,EAAM7D,GACb+D,EAAK9D,EAAeN,EAAKC,EAAKe,EAAKC,EAAK+B,EAAI,CAACkB,EAAKzD,CAAE,EACpD4D,EAAK/D,EAAeI,EAAKC,EAAKO,EAAKC,EAAK6B,EAAI,CAACkB,EAAKzD,CAAE,EAEpDkC,EAAQ,OAAOyB,EAAG,GAAKA,EAAG,IAAKA,EAAG,GAAKA,EAAG,GAAG,EAGzCF,EAAM1D,EAAImC,EAAQ,IAAIyB,EAAG,GAAIA,EAAG,GAAIF,EAAKgB,EAAMd,EAAG,IAAKA,EAAG,GAAG,EAAGc,EAAMb,EAAG,IAAKA,EAAG,GAAG,EAAG,CAAC5D,CAAE,GAI5FkC,EAAQ,IAAIyB,EAAG,GAAIA,EAAG,GAAIF,EAAKgB,EAAMd,EAAG,IAAKA,EAAG,GAAG,EAAGc,EAAMd,EAAG,IAAKA,EAAG,GAAG,EAAG,CAAC3D,CAAE,EAChFkC,EAAQ,IAAI,EAAG,EAAGK,EAAIkC,EAAMd,EAAG,GAAKA,EAAG,IAAKA,EAAG,GAAKA,EAAG,GAAG,EAAGc,EAAMb,EAAG,GAAKA,EAAG,IAAKA,EAAG,GAAKA,EAAG,GAAG,EAAG5D,CAAE,EACtGkC,EAAQ,IAAI0B,EAAG,GAAIA,EAAG,GAAIH,EAAKgB,EAAMb,EAAG,IAAKA,EAAG,GAAG,EAAGa,EAAMb,EAAG,IAAKA,EAAG,GAAG,EAAG,CAAC5D,CAAE,IAK/EkC,EAAQ,IAAI,EAAG,EAAGK,EAAIY,EAAKD,EAAKlD,CAAE,CACxC,CAID,GAFAkC,EAAQ,UAAS,EAEbI,EAAQ,OAAOJ,EAAU,KAAMI,EAAS,IAAM,IACnD,CAED,OAAAD,EAAI,SAAW,UAAW,CACxB,IAAIvB,GAAK,CAACY,EAAY,MAAM,KAAM,SAAS,GAAI,CAACC,EAAY,MAAM,KAAM,SAAS,GAAK,EAClF+C,GAAK,CAAC3C,EAAW,MAAM,KAAM,SAAS,GAAI,CAACC,EAAS,MAAM,KAAM,SAAS,GAAK,EAAIiC,GAAK,EAC3F,MAAO,CAACnB,EAAI4B,CAAC,EAAI5D,EAAGiC,EAAI2B,CAAC,EAAI5D,CAAC,CAClC,EAEEuB,EAAI,YAAc,SAASsC,EAAG,CAC5B,OAAO,UAAU,QAAUjD,EAAc,OAAOiD,GAAM,WAAaA,EAAI9C,EAAS,CAAC8C,CAAC,EAAGtC,GAAOX,CAChG,EAEEW,EAAI,YAAc,SAASsC,EAAG,CAC5B,OAAO,UAAU,QAAUhD,EAAc,OAAOgD,GAAM,WAAaA,EAAI9C,EAAS,CAAC8C,CAAC,EAAGtC,GAAOV,CAChG,EAEEU,EAAI,aAAe,SAASsC,EAAG,CAC7B,OAAO,UAAU,QAAU/C,EAAe,OAAO+C,GAAM,WAAaA,EAAI9C,EAAS,CAAC8C,CAAC,EAAGtC,GAAOT,CACjG,EAEES,EAAI,UAAY,SAASsC,EAAG,CAC1B,OAAO,UAAU,QAAU7C,EAAY6C,GAAK,KAAO,KAAO,OAAOA,GAAM,WAAaA,EAAI9C,EAAS,CAAC8C,CAAC,EAAGtC,GAAOP,CACjH,EAEEO,EAAI,WAAa,SAASsC,EAAG,CAC3B,OAAO,UAAU,QAAU5C,EAAa,OAAO4C,GAAM,WAAaA,EAAI9C,EAAS,CAAC8C,CAAC,EAAGtC,GAAON,CAC/F,EAEEM,EAAI,SAAW,SAASsC,EAAG,CACzB,OAAO,UAAU,QAAU3C,EAAW,OAAO2C,GAAM,WAAaA,EAAI9C,EAAS,CAAC8C,CAAC,EAAGtC,GAAOL,CAC7F,EAEEK,EAAI,SAAW,SAASsC,EAAG,CACzB,OAAO,UAAU,QAAU1C,EAAW,OAAO0C,GAAM,WAAaA,EAAI9C,EAAS,CAAC8C,CAAC,EAAGtC,GAAOJ,CAC7F,EAEEI,EAAI,QAAU,SAASsC,EAAG,CACxB,OAAO,UAAU,QAAWzC,EAAUyC,GAAY,KAAWtC,GAAOH,CACxE,EAESG,CACT", "x_google_ignoreList": [0]}