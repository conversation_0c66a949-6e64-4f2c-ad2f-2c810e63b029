{"version": 3, "file": "index.CMpgkpBY.js", "sources": ["../../../../../../../video/shared/InteractiveVideo.svelte", "../../../../../../../video/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { Upload, ModifyUpload } from \"@gradio/upload\";\n\timport type { FileData, Client } from \"@gradio/client\";\n\timport { BlockLabel } from \"@gradio/atoms\";\n\timport { Webcam } from \"@gradio/image\";\n\timport { Video } from \"@gradio/icons\";\n\timport type { WebcamOptions } from \"./utils\";\n\timport { prettyBytes, playable } from \"./utils\";\n\timport Player from \"./Player.svelte\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { SelectSource } from \"@gradio/atoms\";\n\n\texport let value: FileData | null = null;\n\texport let subtitle: FileData | null = null;\n\texport let sources:\n\t\t| [\"webcam\"]\n\t\t| [\"upload\"]\n\t\t| [\"webcam\", \"upload\"]\n\t\t| [\"upload\", \"webcam\"] = [\"webcam\", \"upload\"];\n\texport let label: string | undefined = undefined;\n\texport let show_download_button = false;\n\texport let show_label = true;\n\texport let webcam_options: WebcamOptions;\n\texport let include_audio: boolean;\n\texport let autoplay: boolean;\n\texport let root: string;\n\texport let i18n: I18nFormatter;\n\texport let active_source: \"webcam\" | \"upload\" = \"webcam\";\n\texport let handle_reset_value: () => void = () => {};\n\texport let max_file_size: number | null = null;\n\texport let upload: Client[\"upload\"];\n\texport let stream_handler: Client[\"stream\"];\n\texport let loop: boolean;\n\texport let uploading = false;\n\n\tlet has_change_history = false;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: FileData | null;\n\t\tclear?: never;\n\t\tplay?: never;\n\t\tpause?: never;\n\t\tend?: never;\n\t\tdrag: boolean;\n\t\terror: string;\n\t\tupload: FileData;\n\t\tstart_recording?: never;\n\t\tstop_recording?: never;\n\t}>();\n\n\tfunction handle_load({ detail }: CustomEvent<FileData | null>): void {\n\t\tvalue = detail;\n\t\tdispatch(\"change\", detail);\n\t\tdispatch(\"upload\", detail!);\n\t}\n\n\tfunction handle_clear(): void {\n\t\tvalue = null;\n\t\tdispatch(\"change\", null);\n\t\tdispatch(\"clear\");\n\t}\n\n\tfunction handle_change(video: FileData): void {\n\t\thas_change_history = true;\n\t\tdispatch(\"change\", video);\n\t}\n\n\tfunction handle_capture({\n\t\tdetail\n\t}: CustomEvent<FileData | any | null>): void {\n\t\tdispatch(\"change\", detail);\n\t}\n\n\tlet dragging = false;\n\t$: dispatch(\"drag\", dragging);\n</script>\n\n<BlockLabel {show_label} Icon={Video} label={label || \"Video\"} />\n<div data-testid=\"video\" class=\"video-container\">\n\t{#if value === null || value.url === undefined}\n\t\t<div class=\"upload-container\">\n\t\t\t{#if active_source === \"upload\"}\n\t\t\t\t<Upload\n\t\t\t\t\tbind:dragging\n\t\t\t\t\tbind:uploading\n\t\t\t\t\tfiletype=\"video/x-m4v,video/*\"\n\t\t\t\t\ton:load={handle_load}\n\t\t\t\t\t{max_file_size}\n\t\t\t\t\ton:error={({ detail }) => dispatch(\"error\", detail)}\n\t\t\t\t\t{root}\n\t\t\t\t\t{upload}\n\t\t\t\t\t{stream_handler}\n\t\t\t\t\taria_label={i18n(\"video.drop_to_upload\")}\n\t\t\t\t>\n\t\t\t\t\t<slot />\n\t\t\t\t</Upload>\n\t\t\t{:else if active_source === \"webcam\"}\n\t\t\t\t<Webcam\n\t\t\t\t\t{root}\n\t\t\t\t\tmirror_webcam={webcam_options.mirror}\n\t\t\t\t\twebcam_constraints={webcam_options.constraints}\n\t\t\t\t\t{include_audio}\n\t\t\t\t\tmode=\"video\"\n\t\t\t\t\ton:error\n\t\t\t\t\ton:capture={handle_capture}\n\t\t\t\t\ton:start_recording\n\t\t\t\t\ton:stop_recording\n\t\t\t\t\t{i18n}\n\t\t\t\t\t{upload}\n\t\t\t\t\tstream_every={1}\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t</div>\n\t{:else if value?.url}\n\t\t{#key value?.url}\n\t\t\t<Player\n\t\t\t\t{upload}\n\t\t\t\t{root}\n\t\t\t\tinteractive\n\t\t\t\t{autoplay}\n\t\t\t\tsrc={value.url}\n\t\t\t\tsubtitle={subtitle?.url}\n\t\t\t\tis_stream={false}\n\t\t\t\ton:play\n\t\t\t\ton:pause\n\t\t\t\ton:stop\n\t\t\t\ton:end\n\t\t\t\ton:error\n\t\t\t\tmirror={webcam_options.mirror && active_source === \"webcam\"}\n\t\t\t\t{label}\n\t\t\t\t{handle_change}\n\t\t\t\t{handle_reset_value}\n\t\t\t\t{loop}\n\t\t\t\t{value}\n\t\t\t\t{i18n}\n\t\t\t\t{show_download_button}\n\t\t\t\t{handle_clear}\n\t\t\t\t{has_change_history}\n\t\t\t/>\n\t\t{/key}\n\t{:else if value.size}\n\t\t<div class=\"file-name\">{value.orig_name || value.url}</div>\n\t\t<div class=\"file-size\">\n\t\t\t{prettyBytes(value.size)}\n\t\t</div>\n\t{/if}\n\n\t<SelectSource {sources} bind:active_source {handle_clear} />\n</div>\n\n<style>\n\t.file-name {\n\t\tpadding: var(--size-6);\n\t\tfont-size: var(--text-xxl);\n\t\tword-break: break-all;\n\t}\n\n\t.file-size {\n\t\tpadding: var(--size-2);\n\t\tfont-size: var(--text-xl);\n\t}\n\n\t.upload-container {\n\t\theight: 100%;\n\t\twidth: 100%;\n\t}\n\n\t.video-container {\n\t\tdisplay: flex;\n\t\theight: 100%;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n</style>\n", "<svelte:options accessors={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio, ShareData } from \"@gradio/utils\";\n\n\timport type { FileData } from \"@gradio/client\";\n\timport { Block, UploadText } from \"@gradio/atoms\";\n\timport StaticVideo from \"./shared/VideoPreview.svelte\";\n\timport Video from \"./shared/InteractiveVideo.svelte\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport type { WebcamOptions } from \"./shared/utils\";\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: { video: FileData; subtitles: FileData | null } | null =\n\t\tnull;\n\tlet old_value: { video: FileData; subtitles: FileData | null } | null = null;\n\n\texport let label: string;\n\texport let sources:\n\t\t| [\"webcam\"]\n\t\t| [\"upload\"]\n\t\t| [\"webcam\", \"upload\"]\n\t\t| [\"upload\", \"webcam\"];\n\texport let root: string;\n\texport let show_label: boolean;\n\texport let loading_status: LoadingStatus;\n\texport let height: number | undefined;\n\texport let width: number | undefined;\n\n\texport let container = false;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let autoplay = false;\n\texport let show_share_button = true;\n\texport let show_download_button: boolean;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tclear: never;\n\t\tplay: never;\n\t\tpause: never;\n\t\tupload: never;\n\t\tstop: never;\n\t\tend: never;\n\t\tstart_recording: never;\n\t\tstop_recording: never;\n\t\tshare: ShareData;\n\t\terror: string;\n\t\twarning: string;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let interactive: boolean;\n\texport let webcam_options: WebcamOptions;\n\texport let include_audio: boolean;\n\texport let loop = false;\n\texport let input_ready: boolean;\n\tlet uploading = false;\n\t$: input_ready = !uploading;\n\n\tlet _video: FileData | null = null;\n\tlet _subtitle: FileData | null = null;\n\n\tlet active_source: \"webcam\" | \"upload\";\n\n\tlet initial_value: { video: FileData; subtitles: FileData | null } | null =\n\t\tvalue;\n\n\t$: if (value && initial_value === null) {\n\t\tinitial_value = value;\n\t}\n\n\tconst handle_reset_value = (): void => {\n\t\tif (initial_value === null || value === initial_value) {\n\t\t\treturn;\n\t\t}\n\n\t\tvalue = initial_value;\n\t};\n\n\t$: if (sources && !active_source) {\n\t\tactive_source = sources[0];\n\t}\n\n\t$: {\n\t\tif (value != null) {\n\t\t\t_video = value.video;\n\t\t\t_subtitle = value.subtitles;\n\t\t} else {\n\t\t\t_video = null;\n\t\t\t_subtitle = null;\n\t\t}\n\t}\n\n\tlet dragging = false;\n\n\t$: {\n\t\tif (JSON.stringify(value) !== JSON.stringify(old_value)) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}\n\t}\n\n\tfunction handle_change({ detail }: CustomEvent<FileData | null>): void {\n\t\tif (detail != null) {\n\t\t\tvalue = { video: detail, subtitles: null } as {\n\t\t\t\tvideo: FileData;\n\t\t\t\tsubtitles: FileData | null;\n\t\t\t} | null;\n\t\t} else {\n\t\t\tvalue = null;\n\t\t}\n\t}\n\n\tfunction handle_error({ detail }: CustomEvent<string>): void {\n\t\tconst [level, status] = detail.includes(\"Invalid file type\")\n\t\t\t? [\"warning\", \"complete\"]\n\t\t\t: [\"error\", \"error\"];\n\t\tloading_status = loading_status || {};\n\t\tloading_status.status = status as LoadingStatus[\"status\"];\n\t\tloading_status.message = detail;\n\t\tgradio.dispatch(level as \"error\" | \"warning\", detail);\n\t}\n</script>\n\n{#if !interactive}\n\t<Block\n\t\t{visible}\n\t\tvariant={value === null && active_source === \"upload\" ? \"dashed\" : \"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\t{height}\n\t\t{width}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t\tallow_overflow={false}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\n\t\t<StaticVideo\n\t\t\tvalue={_video}\n\t\t\tsubtitle={_subtitle}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{autoplay}\n\t\t\t{loop}\n\t\t\t{show_share_button}\n\t\t\t{show_download_button}\n\t\t\ton:play={() => gradio.dispatch(\"play\")}\n\t\t\ton:pause={() => gradio.dispatch(\"pause\")}\n\t\t\ton:stop={() => gradio.dispatch(\"stop\")}\n\t\t\ton:end={() => gradio.dispatch(\"end\")}\n\t\t\ton:share={({ detail }) => gradio.dispatch(\"share\", detail)}\n\t\t\ton:error={({ detail }) => gradio.dispatch(\"error\", detail)}\n\t\t\ti18n={gradio.i18n}\n\t\t\tupload={(...args) => gradio.client.upload(...args)}\n\t\t/>\n\t</Block>\n{:else}\n\t<Block\n\t\t{visible}\n\t\tvariant={value === null && active_source === \"upload\" ? \"dashed\" : \"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\t{height}\n\t\t{width}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t\tallow_overflow={false}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\n\t\t<Video\n\t\t\tvalue={_video}\n\t\t\tsubtitle={_subtitle}\n\t\t\ton:change={handle_change}\n\t\t\ton:drag={({ detail }) => (dragging = detail)}\n\t\t\ton:error={handle_error}\n\t\t\tbind:uploading\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{show_download_button}\n\t\t\t{sources}\n\t\t\t{active_source}\n\t\t\t{webcam_options}\n\t\t\t{include_audio}\n\t\t\t{autoplay}\n\t\t\t{root}\n\t\t\t{loop}\n\t\t\t{handle_reset_value}\n\t\t\ton:clear={() => gradio.dispatch(\"clear\")}\n\t\t\ton:play={() => gradio.dispatch(\"play\")}\n\t\t\ton:pause={() => gradio.dispatch(\"pause\")}\n\t\t\ton:upload={() => gradio.dispatch(\"upload\")}\n\t\t\ton:stop={() => gradio.dispatch(\"stop\")}\n\t\t\ton:end={() => gradio.dispatch(\"end\")}\n\t\t\ton:start_recording={() => gradio.dispatch(\"start_recording\")}\n\t\t\ton:stop_recording={() => gradio.dispatch(\"stop_recording\")}\n\t\t\ti18n={gradio.i18n}\n\t\t\tmax_file_size={gradio.max_file_size}\n\t\t\tupload={(...args) => gradio.client.upload(...args)}\n\t\t\tstream_handler={(...args) => gradio.client.stream(...args)}\n\t\t>\n\t\t\t<UploadText i18n={gradio.i18n} type=\"video\" />\n\t\t</Video>\n\t</Block>\n{/if}\n"], "names": ["t0_value", "ctx", "prettyBytes", "insert_hydration", "target", "div0", "anchor", "div1", "dirty", "set_data", "t0", "t2", "t2_value", "previous_key", "_a", "safe_not_equal", "div", "player_changes", "webcam_changes", "upload_1_changes", "Video", "blocklabel_changes", "value", "$$props", "subtitle", "sources", "label", "show_download_button", "show_label", "webcam_options", "include_audio", "autoplay", "root", "i18n", "active_source", "handle_reset_value", "max_file_size", "upload", "stream_handler", "loop", "uploading", "has_change_history", "dispatch", "createEventDispatcher", "handle_load", "detail", "handle_clear", "handle_change", "video", "handle_capture", "dragging", "error_handler_2", "block_changes", "uploadtext_changes", "video_changes", "staticvideo_changes", "elem_id", "elem_classes", "visible", "old_value", "loading_status", "height", "width", "container", "scale", "min_width", "show_share_button", "gradio", "interactive", "input_ready", "_video", "_subtitle", "initial_value", "handle_error", "level", "status", "$$invalidate", "clear_status_handler", "func", "args", "share_handler", "error_handler", "clear_status_handler_1", "func_1", "func_2"], "mappings": "s1CA8I0BA,GAAAC,EAAM,CAAA,EAAA,WAAaA,KAAM,KAAG,WAElDC,GAAYD,EAAK,CAAA,EAAC,IAAI,EAAA,4SAFxBE,EAA0DC,EAAAC,EAAAC,CAAA,mBAC1DH,EAEKC,EAAAG,EAAAD,CAAA,kBAHmBE,EAAA,CAAA,EAAA,GAAAR,KAAAA,GAAAC,EAAM,CAAA,EAAA,WAAaA,KAAM,KAAG,KAAAQ,GAAAC,EAAAV,CAAA,iBAElDE,GAAYD,EAAK,CAAA,EAAC,IAAI,EAAA,KAAAQ,GAAAE,EAAAC,CAAA,6DA7BlB,IAAAC,GAAAC,EAAAb,OAAA,YAAAa,EAAO,yGAAPN,EAAA,CAAA,EAAA,GAAAO,GAAAF,EAAAA,GAAAC,EAAAb,OAAA,YAAAa,EAAO,GAAG,wMAjCV,OAAAb,OAAkB,SAAQ,EAerBA,OAAkB,SAAQ,8MAhBrCE,EAgCKC,EAAAY,EAAAV,CAAA,yWAQE,IAAAL,KAAM,IACD,UAAAa,EAAAb,OAAA,YAAAa,EAAU,cACT,GAMH,OAAAb,EAAe,CAAA,EAAA,QAAUA,OAAkB,mcAR9CO,EAAA,CAAA,EAAA,IAAAS,EAAA,IAAAhB,KAAM,KACDO,EAAA,CAAA,EAAA,IAAAS,EAAA,UAAAH,EAAAb,OAAA,YAAAa,EAAU,KAOZN,EAAA,CAAA,EAAA,MAAAS,EAAA,OAAAhB,EAAe,CAAA,EAAA,QAAUA,OAAkB,4XA7BnC,cAAAA,KAAe,OACV,mBAAAA,KAAe,iFASrB,0CALFA,EAAc,EAAA,CAAA,iLALXO,EAAA,CAAA,EAAA,MAAAU,EAAA,cAAAjB,KAAe,QACVO,EAAA,CAAA,EAAA,MAAAU,EAAA,mBAAAjB,KAAe,+WARvB,WAAAA,MAAK,sBAAsB,oNAN9BA,EAAW,EAAA,CAAA,+OAMRO,EAAA,CAAA,EAAA,OAAAW,EAAA,WAAAlB,MAAK,sBAAsB,+jBAfbmB,GAAc,MAAAnB,MAAS,yDAEhD,OAAAA,OAAU,MAAQA,EAAM,CAAA,EAAA,MAAQ,OAAS,GAkCpCa,EAAAb,OAAA,MAAAa,EAAO,IAAG,EA2BVb,KAAM,KAAI,0gBA9DrBE,EAsEKC,EAAAY,EAAAV,CAAA,+FAvEwCE,EAAA,CAAA,EAAA,KAAAa,EAAA,MAAApB,MAAS,geAjE1C,MAAAqB,EAAyB,IAAA,EAAAC,GACzB,SAAAC,EAA4B,IAAA,EAAAD,EAC5B,CAAA,QAAAE,EAAA,CAIgB,SAAU,QAAQ,CAAA,EAAAF,GAClC,MAAAG,EAA4B,MAAA,EAAAH,GAC5B,qBAAAI,EAAuB,EAAA,EAAAJ,GACvB,WAAAK,EAAa,EAAA,EAAAL,EACb,CAAA,eAAAM,CAAA,EAAAN,EACA,CAAA,cAAAO,CAAA,EAAAP,EACA,CAAA,SAAAQ,CAAA,EAAAR,EACA,CAAA,KAAAS,CAAA,EAAAT,EACA,CAAA,KAAAU,CAAA,EAAAV,GACA,cAAAW,EAAqC,QAAA,EAAAX,EACrC,CAAA,mBAAAY,EAAA,IAAA,OACA,cAAAC,EAA+B,IAAA,EAAAb,EAC/B,CAAA,OAAAc,CAAA,EAAAd,EACA,CAAA,eAAAe,CAAA,EAAAf,EACA,CAAA,KAAAgB,CAAA,EAAAhB,GACA,UAAAiB,EAAY,EAAA,EAAAjB,EAEnBkB,EAAqB,SAEnBC,EAAWC,cAaRC,EAAc,CAAA,OAAAC,GAAA,KACtBvB,EAAQuB,CAAA,EACRH,EAAS,SAAUG,CAAM,EACzBH,EAAS,SAAUG,CAAO,EAGlB,SAAAC,GAAA,KACRxB,EAAQ,IAAA,EACRoB,EAAS,SAAU,IAAI,EACvBA,EAAS,OAAO,WAGRK,EAAcC,EAAA,MACtBP,EAAqB,EAAA,EACrBC,EAAS,SAAUM,CAAK,WAGhBC,EACR,CAAA,OAAAJ,GAAA,CAEAH,EAAS,SAAUG,CAAM,MAGtBK,EAAW,sDAeE,MAAAC,GAAA,CAAA,CAAA,OAAAN,CAAM,IAAOH,EAAS,QAASG,CAAM,iiCAdnDH,EAAS,OAAQQ,CAAQ,geC8FlB,QAAAjD,EAAU,CAAA,IAAA,MAAQA,QAAkB,SAAW,SAAW,oBACtDA,EAAQ,EAAA,EAAG,QAAU,eACzB,qHAQO,+JAVPO,EAAA,CAAA,EAAA,UAAA4C,EAAA,QAAAnD,EAAU,CAAA,IAAA,MAAQA,QAAkB,SAAW,SAAW,uCACtDA,EAAQ,EAAA,EAAG,QAAU,4ZA1CzB,QAAAA,EAAU,CAAA,IAAA,MAAQA,QAAkB,SAAW,SAAW,oBACtDA,EAAQ,EAAA,EAAG,QAAU,eACzB,qHAQO,+JAVPO,EAAA,CAAA,EAAA,UAAA4C,EAAA,QAAAnD,EAAU,CAAA,IAAA,MAAQA,QAAkB,SAAW,SAAW,uCACtDA,EAAQ,EAAA,EAAG,QAAU,8YA0Ff,KAAAA,MAAO,6GAAPO,EAAA,CAAA,EAAA,SAAA6C,EAAA,KAAApD,MAAO,uIArCb,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,yIAKXA,EAAM,EAAA,WACHA,EAAS,EAAA,+LAwBb,KAAAA,MAAO,KACE,cAAAA,MAAO,qMAxBXA,EAAa,EAAA,CAAA,oCAEdA,EAAY,EAAA,CAAA,kXAXV,WAAAA,MAAO,YACbO,EAAA,CAAA,EAAA,QAAA,CAAA,KAAAP,MAAO,IAAI,aACbA,EAAc,CAAA,CAAA,oDAKXA,EAAM,EAAA,8BACHA,EAAS,EAAA,oUAwBbO,EAAA,CAAA,EAAA,SAAA8C,EAAA,KAAArD,MAAO,MACEO,EAAA,CAAA,EAAA,SAAA8C,EAAA,cAAArD,MAAO,oXA1EV,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,mIAKXA,EAAM,EAAA,WACHA,EAAS,EAAA,0GAab,KAAAA,MAAO,uUArBD,WAAAA,MAAO,YACbO,EAAA,CAAA,EAAA,QAAA,CAAA,KAAAP,MAAO,IAAI,aACbA,EAAc,CAAA,CAAA,oDAKXA,EAAM,EAAA,8BACHA,EAAS,EAAA,wMAabO,EAAA,CAAA,EAAA,SAAA+C,EAAA,KAAAtD,MAAO,0PArCVA,EAAW,EAAA,IAAA,kVAjHL,QAAAuD,EAAU,EAAA,EAAAjC,EACV,CAAA,aAAAkC,EAAA,EAAA,EAAAlC,GACA,QAAAmC,EAAU,EAAA,EAAAnC,GACV,MAAAD,EACV,IAAA,EAAAC,EACGoC,EAAoE,KAE7D,CAAA,MAAAjC,CAAA,EAAAH,EACA,CAAA,QAAAE,CAAA,EAAAF,EAKA,CAAA,KAAAS,CAAA,EAAAT,EACA,CAAA,WAAAK,CAAA,EAAAL,EACA,CAAA,eAAAqC,CAAA,EAAArC,EACA,CAAA,OAAAsC,CAAA,EAAAtC,EACA,CAAA,MAAAuC,CAAA,EAAAvC,GAEA,UAAAwC,EAAY,EAAA,EAAAxC,GACZ,MAAAyC,EAAuB,IAAA,EAAAzC,GACvB,UAAA0C,EAAgC,MAAA,EAAA1C,GAChC,SAAAQ,EAAW,EAAA,EAAAR,GACX,kBAAA2C,EAAoB,EAAA,EAAA3C,EACpB,CAAA,qBAAAI,CAAA,EAAAJ,EACA,CAAA,OAAA4C,CAAA,EAAA5C,EAeA,CAAA,YAAA6C,CAAA,EAAA7C,EACA,CAAA,eAAAM,CAAA,EAAAN,EACA,CAAA,cAAAO,CAAA,EAAAP,GACA,KAAAgB,EAAO,EAAA,EAAAhB,EACP,CAAA,YAAA8C,CAAA,EAAA9C,EACPiB,EAAY,GAGZ8B,EAA0B,KAC1BC,EAA6B,KAE7BrC,EAEAsC,EACHlD,EAMK,MAAAa,GAAA,IAAA,CACDqC,IAAkB,MAAQlD,IAAUkD,OAIxClD,EAAQkD,CAAA,OAiBLtB,EAAW,YASNH,GAAgB,CAAA,OAAAF,GAAA,CACpBA,GAAU,SACbvB,EAAU,CAAA,MAAOuB,EAAQ,UAAW,IAAA,CAAA,MAKpCvB,EAAQ,IAAA,WAIDmD,GAAe,CAAA,OAAA5B,GAAA,CAChB,KAAA,CAAA6B,GAAOC,EAAM,EAAI9B,EAAO,SAAS,mBAAmB,EACvD,CAAA,UAAW,UAAU,EACrB,CAAA,QAAS,OAAO,MACpBe,EAAiBA,GAAA,CAAA,CAAA,EACjBgB,EAAA,EAAAhB,EAAe,OAASe,GAAAf,CAAA,EACxBgB,EAAA,EAAAhB,EAAe,QAAUf,EAAAe,CAAA,EACzBO,EAAO,SAASO,GAA8B7B,CAAM,EAuB5B,MAAAgC,GAAA,IAAAV,EAAO,SAAS,eAAgBP,CAAc,EAmBzDkB,GAAA,IAAAC,IAASZ,EAAO,OAAO,UAAUY,CAAI,SAPlCZ,EAAO,SAAS,MAAM,SACrBA,EAAO,SAAS,OAAO,SACxBA,EAAO,SAAS,MAAM,SACvBA,EAAO,SAAS,KAAK,EACtBa,EAAA,CAAA,CAAA,OAAAnC,KAAasB,EAAO,SAAS,QAAStB,CAAM,EAC5CoC,GAAA,CAAA,CAAA,OAAApC,KAAasB,EAAO,SAAS,QAAStB,CAAM,EAwBlCqC,GAAA,IAAAf,EAAO,SAAS,eAAgBP,CAAc,EA+BzDuB,GAAA,IAAAJ,IAASZ,EAAO,OAAO,UAAUY,CAAI,EAC7BK,GAAA,IAAAL,IAASZ,EAAO,OAAO,UAAUY,CAAI,wCAzB7C,OAAAlC,CAAM,IAAA+B,EAAA,GAAQ1B,EAAWL,CAAM,SAc3BsB,EAAO,SAAS,OAAO,SACxBA,EAAO,SAAS,MAAM,SACrBA,EAAO,SAAS,OAAO,SACtBA,EAAO,SAAS,QAAQ,SAC1BA,EAAO,SAAS,MAAM,SACvBA,EAAO,SAAS,KAAK,SACTA,EAAO,SAAS,iBAAiB,SAClCA,EAAO,SAAS,gBAAgB,24BA3J3DS,EAAA,GAAGP,EAAe,CAAA7B,CAAA,mCAUXlB,GAASkD,IAAkB,WACjCA,EAAgBlD,CAAA,yBAWVG,GAAY,CAAAS,GAClB0C,EAAA,GAAA1C,EAAgBT,EAAQ,CAAC,CAAA,oBAIrBH,GAAS,MACZsD,EAAA,GAAAN,EAAShD,EAAM,KAAA,EACfsD,EAAA,GAAAL,EAAYjD,EAAM,SAAA,SAElBgD,EAAS,IAAA,OACTC,EAAY,IAAA,0CAOT,KAAK,UAAUjD,CAAK,IAAM,KAAK,UAAUqC,CAAS,SACrDA,EAAYrC,CAAA,EACZ6C,EAAO,SAAS,QAAQ"}