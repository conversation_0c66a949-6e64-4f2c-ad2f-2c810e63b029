{"version": 3, "file": "KHR_materials_emissive_strength-h355fKj6.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_materials_emissive_strength.js"], "sourcesContent": ["import { PBRMaterial } from \"@babylonjs/core/Materials/PBR/pbrMaterial.js\";\nimport { GLTFLoader } from \"../glTFLoader.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"KHR_materials_emissive_strength\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_emissive_strength/README.md)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_materials_emissive_strength {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        /**\n         * Defines a number that determines the order the extensions are applied.\n         */\n        this.order = 170;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /**\n     * @internal\n     */\n    loadMaterialPropertiesAsync(context, material, babylonMaterial) {\n        return GLTFLoader.LoadExtensionAsync(context, material, this.name, (extensionContext, extension) => {\n            return this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial).then(() => {\n                this._loadEmissiveProperties(extensionContext, extension, babylonMaterial);\n            });\n        });\n    }\n    _loadEmissiveProperties(context, properties, babylonMaterial) {\n        if (!(babylonMaterial instanceof PBRMaterial)) {\n            throw new Error(`${context}: Material type not supported`);\n        }\n        if (properties.emissiveStrength !== undefined) {\n            babylonMaterial.emissiveIntensity = properties.emissiveStrength;\n        }\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_materials_emissive_strength(loader));\n//# sourceMappingURL=KHR_materials_emissive_strength.js.map"], "names": ["NAME", "KHR_materials_emissive_strength", "loader", "context", "material", "babylonMaterial", "GLTFLoader", "extensionContext", "extension", "properties", "PBRMaterial", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "gTAGA,MAAMA,EAAO,kCAKN,MAAMC,CAAgC,CAIzC,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EAIZ,KAAK,MAAQ,IACb,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,CACnD,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAID,4BAA4BG,EAASC,EAAUC,EAAiB,CAC5D,OAAOC,EAAW,mBAAmBH,EAASC,EAAU,KAAK,KAAM,CAACG,EAAkBC,IAC3E,KAAK,QAAQ,4BAA4BL,EAASC,EAAUC,CAAe,EAAE,KAAK,IAAM,CAC3F,KAAK,wBAAwBE,EAAkBC,EAAWH,CAAe,CACzF,CAAa,CACJ,CACJ,CACD,wBAAwBF,EAASM,EAAYJ,EAAiB,CAC1D,GAAI,EAAEA,aAA2BK,GAC7B,MAAM,IAAI,MAAM,GAAGP,CAAO,+BAA+B,EAEzDM,EAAW,mBAAqB,SAChCJ,EAAgB,kBAAoBI,EAAW,iBAEtD,CACL,CACAE,EAAwBX,CAAI,EAC5BY,EAAsBZ,EAAM,GAAOE,GAAW,IAAID,EAAgCC,CAAM,CAAC", "x_google_ignoreList": [0]}