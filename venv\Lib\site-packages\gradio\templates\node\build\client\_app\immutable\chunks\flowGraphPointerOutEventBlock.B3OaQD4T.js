import{c as n,_ as i}from"./KHR_interactivity.DEAVS2UW.js";import{b as o,R as h}from"./declarationMapper.UBCwU7BT.js";import{R as u}from"./index.BoI39RQH.js";class a extends n{constructor(t){super(t),this.type="PointerOut",this.pointerId=this.registerDataOutput("pointerId",o),this.targetMesh=this.registerDataInput("targetMesh",h,t==null?void 0:t.targetMesh),this.meshOutOfPointer=this.registerDataOutput("meshOutOfPointer",h)}_executeEvent(t,e){var r;const s=this.targetMesh.getValue(t);return this.meshOutOfPointer.setValue(e.mesh,t),this.pointerId.setValue(e.pointerId,t),!(e.over&&i(e.mesh,s))&&(e.mesh===s||i(e.mesh,s))?(this._execute(t),!((r=this.config)!=null&&r.stopPropagation)):!0}_preparePendingTasks(t){}_cancelPendingTasks(t){}getClassName(){return"FlowGraphPointerOutEventBlock"}}u("FlowGraphPointerOutEventBlock",a);export{a as FlowGraphPointerOutEventBlock};
//# sourceMappingURL=flowGraphPointerOutEventBlock.B3OaQD4T.js.map
