{"version": 3, "file": "Index.3nCFs0Nn.js", "sources": ["../../../../../../../markdown/shared/Markdown.svelte", "../../../../../../../markdown/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { copy, css_units } from \"@gradio/utils\";\n\timport type { CopyData } from \"@gradio/utils\";\n\timport { Copy, Check } from \"@gradio/icons\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { IconButton, IconButtonWrapper } from \"@gradio/atoms\";\n\timport type { ThemeMode } from \"@gradio/core\";\n\n\timport { MarkdownCode } from \"@gradio/markdown-code\";\n\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: string;\n\texport let min_height: number | string | undefined = undefined;\n\texport let rtl = false;\n\texport let sanitize_html = true;\n\texport let line_breaks = false;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let header_links = false;\n\texport let height: number | string | undefined = undefined;\n\texport let show_copy_button = false;\n\texport let loading_status: LoadingStatus | undefined = undefined;\n\texport let theme_mode: ThemeMode;\n\tlet copied = false;\n\tlet timer: NodeJS.Timeout;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tcopy: CopyData;\n\t}>();\n\n\t$: value, dispatch(\"change\");\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tawait navigator.clipboard.writeText(value);\n\t\t\tdispatch(\"copy\", { value: value });\n\t\t\tcopy_feedback();\n\t\t}\n\t}\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 1000);\n\t}\n</script>\n\n<div\n\tclass=\"prose {elem_classes?.join(' ') || ''}\"\n\tclass:hide={!visible}\n\tdata-testid=\"markdown\"\n\tdir={rtl ? \"rtl\" : \"ltr\"}\n\tuse:copy\n\tstyle={height ? `max-height: ${css_units(height)}; overflow-y: auto;` : \"\"}\n\tstyle:min-height={min_height && loading_status?.status !== \"pending\"\n\t\t? css_units(min_height)\n\t\t: undefined}\n>\n\t{#if show_copy_button}\n\t\t<IconButtonWrapper>\n\t\t\t<IconButton\n\t\t\t\tIcon={copied ? Check : Copy}\n\t\t\t\ton:click={handle_copy}\n\t\t\t\tlabel={copied ? \"Copied conversation\" : \"Copy conversation\"}\n\t\t\t></IconButton>\n\t\t</IconButtonWrapper>\n\t{/if}\n\t<MarkdownCode\n\t\tmessage={value}\n\t\t{latex_delimiters}\n\t\t{sanitize_html}\n\t\t{line_breaks}\n\t\tchatbot={false}\n\t\t{header_links}\n\t\t{theme_mode}\n\t/>\n</div>\n\n<style>\n\tdiv :global(.math.inline) {\n\t\tfill: var(--body-text-color);\n\t\tdisplay: inline-block;\n\t\tvertical-align: middle;\n\t\tpadding: var(--size-1-5) -var(--size-1);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\tdiv :global(.math.inline svg) {\n\t\tdisplay: inline;\n\t\tmargin-bottom: 0.22em;\n\t}\n\n\tdiv {\n\t\tmax-width: 100%;\n\t}\n\n\t.hide {\n\t\tdisplay: none;\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseMarkdown } from \"./shared/Markdown.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, CopyData } from \"@gradio/utils\";\n\timport Markdown from \"./shared/Markdown.svelte\";\n\timport type { ThemeMode } from \"@gradio/core\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { Block } from \"@gradio/atoms\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value = \"\";\n\texport let loading_status: LoadingStatus;\n\texport let rtl = false;\n\texport let sanitize_html = true;\n\texport let line_breaks = false;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tcopy: CopyData;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let header_links = false;\n\texport let height: number | string | undefined;\n\texport let min_height: number | string | undefined;\n\texport let max_height: number | string | undefined;\n\texport let show_copy_button = false;\n\texport let container = false;\n\texport let theme_mode: ThemeMode;\n\texport let padding = false;\n</script>\n\n<Block\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\tallow_overflow={true}\n\toverflow_behavior=\"auto\"\n\t{height}\n\t{min_height}\n\t{max_height}\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\tvariant=\"center\"\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t<div class:padding class:pending={loading_status?.status === \"pending\"}>\n\t\t<Markdown\n\t\t\t{value}\n\t\t\t{elem_classes}\n\t\t\t{visible}\n\t\t\t{rtl}\n\t\t\ton:change={() => gradio.dispatch(\"change\")}\n\t\t\ton:copy={(e) => gradio.dispatch(\"copy\", e.detail)}\n\t\t\t{latex_delimiters}\n\t\t\t{sanitize_html}\n\t\t\t{line_breaks}\n\t\t\t{header_links}\n\t\t\t{show_copy_button}\n\t\t\t{loading_status}\n\t\t\t{theme_mode}\n\t\t/>\n\t</div>\n</Block>\n\n<style>\n\tdiv {\n\t\ttransition: 150ms;\n\t}\n\n\t.pending {\n\t\topacity: 0.2;\n\t}\n\n\t.padding {\n\t\tpadding: var(--block-padding);\n\t}\n</style>\n"], "names": ["ctx", "Check", "Copy", "create_if_block", "attr", "div", "div_class_value", "_a", "css_units", "set_style", "_b", "insert_hydration", "target", "anchor", "current", "dirty", "elem_classes", "$$props", "visible", "value", "min_height", "rtl", "sanitize_html", "line_breaks", "latex_delimiters", "header_links", "height", "show_copy_button", "loading_status", "theme_mode", "copied", "timer", "dispatch", "createEventDispatcher", "handle_copy", "copy_feedback", "elem_id", "gradio", "max_height", "container", "padding", "clear_status_handler", "e"], "mappings": "6lCAqEUA,EAAM,EAAA,EAAGC,EAAQC,QAEhBF,EAAM,EAAA,EAAG,sBAAwB,qCAD9BA,EAAW,EAAA,CAAA,0GADfA,EAAM,EAAA,EAAGC,EAAQC,oBAEhBF,EAAM,EAAA,EAAG,sBAAwB,sJALtCA,EAAgB,EAAA,GAAAG,EAAAH,CAAA,kCAUXA,EAAK,CAAA,oEAIL,mPAxBII,EAAAC,EAAA,QAAAC,EAAA,YAAAC,EAAAP,EAAc,CAAA,IAAd,YAAAO,EAAc,KAAK,OAAQ,IAAE,gBAAA,4CAGtCP,EAAG,CAAA,EAAG,MAAQ,KAAK,gBAEjBA,EAAM,CAAA,EAAkB,eAAAQ,EAAUR,EAAM,CAAA,CAAA,CAAA,sBAAyB,EAAE,cAJ7DA,EAAO,CAAA,CAAA,EAKFS,EAAAJ,EAAA,aAAAL,EAAc,CAAA,KAAAU,EAAAV,EAAgB,EAAA,IAAhB,YAAAU,EAAgB,UAAW,UACxDF,EAAUR,EAAU,CAAA,CAAA,EACpB,MAAS,UATbW,EA6BKC,EAAAP,EAAAQ,CAAA,0FAlBCb,EAAgB,EAAA,2HAUXA,EAAK,CAAA,iKApBD,CAAAc,GAAAC,EAAA,GAAAT,KAAAA,EAAA,YAAAC,EAAAP,EAAc,CAAA,IAAd,YAAAO,EAAc,KAAK,OAAQ,IAAE,qDAGtCP,EAAG,CAAA,EAAG,MAAQ,yCAEZA,EAAM,CAAA,EAAkB,eAAAQ,EAAUR,EAAM,CAAA,CAAA,CAAA,sBAAyB,4CAJ3DA,EAAO,CAAA,CAAA,6BAKFS,EAAAJ,EAAA,aAAAL,EAAc,CAAA,KAAAU,EAAAV,EAAgB,EAAA,IAAhB,YAAAU,EAAgB,UAAW,UACxDF,EAAUR,EAAU,CAAA,CAAA,EACpB,MAAS,uIArDD,GAAA,CAAA,aAAAgB,EAAA,EAAA,EAAAC,GACA,QAAAC,EAAU,EAAA,EAAAD,EACV,CAAA,MAAAE,CAAA,EAAAF,GACA,WAAAG,EAA0C,MAAA,EAAAH,GAC1C,IAAAI,EAAM,EAAA,EAAAJ,GACN,cAAAK,EAAgB,EAAA,EAAAL,GAChB,YAAAM,EAAc,EAAA,EAAAN,EACd,CAAA,iBAAAO,CAAA,EAAAP,GAKA,aAAAQ,EAAe,EAAA,EAAAR,GACf,OAAAS,EAAsC,MAAA,EAAAT,GACtC,iBAAAU,EAAmB,EAAA,EAAAV,GACnB,eAAAW,EAA4C,MAAA,EAAAX,EAC5C,CAAA,WAAAY,CAAA,EAAAZ,EACPa,EAAS,GACTC,QAEEC,EAAWC,IAOF,eAAAC,GAAA,CACV,cAAe,kBACZ,UAAU,UAAU,UAAUf,CAAK,EACzCa,EAAS,OAAU,CAAA,MAAAb,CAAA,CAAA,EACnBgB,KAIO,SAAAA,GAAA,MACRL,EAAS,EAAA,EACLC,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,qBACPD,EAAS,EAAA,GACP,mjBAfME,EAAS,QAAQ,qUCkBd,CAAA,WAAAhC,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,CAAA,knBAIeO,EAAAP,EAAc,CAAA,IAAd,YAAAO,EAAgB,UAAW,SAAS,4BAAtEI,EAgBKC,EAAAP,EAAAQ,CAAA,qDAtBQE,EAAA,KAAA,CAAA,WAAAf,KAAO,UAAU,EACvBe,EAAA,KAAA,CAAA,KAAAf,KAAO,IAAI,WACbA,EAAc,CAAA,CAAA,6ZAIeO,EAAAP,EAAc,CAAA,IAAd,YAAAO,EAAgB,UAAW,SAAS,sQAbtD,8hBAjCL,QAAA6B,EAAU,EAAA,EAAAnB,EACV,CAAA,aAAAD,EAAA,EAAA,EAAAC,GACA,QAAAC,EAAU,EAAA,EAAAD,GACV,MAAAE,EAAQ,EAAA,EAAAF,EACR,CAAA,eAAAW,CAAA,EAAAX,GACA,IAAAI,EAAM,EAAA,EAAAJ,GACN,cAAAK,EAAgB,EAAA,EAAAL,GAChB,YAAAM,EAAc,EAAA,EAAAN,EACd,CAAA,OAAAoB,CAAA,EAAApB,EAKA,CAAA,iBAAAO,CAAA,EAAAP,GAKA,aAAAQ,EAAe,EAAA,EAAAR,EACf,CAAA,OAAAS,CAAA,EAAAT,EACA,CAAA,WAAAG,CAAA,EAAAH,EACA,CAAA,WAAAqB,CAAA,EAAArB,GACA,iBAAAU,EAAmB,EAAA,EAAAV,GACnB,UAAAsB,EAAY,EAAA,EAAAtB,EACZ,CAAA,WAAAY,CAAA,EAAAZ,GACA,QAAAuB,EAAU,EAAA,EAAAvB,EAmBG,MAAAwB,EAAA,IAAAJ,EAAO,SAAS,eAAgBT,CAAc,QAQnDS,EAAO,SAAS,QAAQ,IAC/BK,GAAML,EAAO,SAAS,OAAQK,EAAE,MAAM"}