import { c as create_ssr_component, b as createEventDispatcher, v as validate_component, e as escape } from './ssr-C3HYbsxA.js';
import { g as ge } from './ModifyUpload-wYzp15o5.js';
import { e as bt, at as ue, a9 as ze, m as mt, z as zA, _ as _e } from './2-DJbI4FWc.js';
import { Webcam as zt } from './Index26-R8oUQBBK.js';
import { P as bt$1, p as zt$1, V as yt } from './VideoPreview-gwu2teMC.js';
export { l as loaded, a as playable } from './VideoPreview-gwu2teMC.js';
export { default as BaseExample } from './Example25-Cah6Caoc.js';
import './Component-NmRBwSfF.js';
import './index-ClteBeTX.js';
import 'path';
import 'url';
import 'fs';
import './ImagePreview-dCWEyjhO.js';
import './Example10-DN5tOkvr.js';
import './Video-CUcSyd4W.js';
import './hls-DpKhbIaL.js';

const $={code:".file-name.svelte-14jis2k{padding:var(--size-6);font-size:var(--text-xxl);word-break:break-all}.file-size.svelte-14jis2k{padding:var(--size-2);font-size:var(--text-xl)}.upload-container.svelte-14jis2k{height:100%;width:100%}.video-container.svelte-14jis2k{display:flex;height:100%;flex-direction:column;justify-content:center;align-items:center}",map:'{"version":3,"file":"InteractiveVideo.svelte","sources":["InteractiveVideo.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { createEventDispatcher } from \\"svelte\\";\\nimport { Upload, ModifyUpload } from \\"@gradio/upload\\";\\nimport { BlockLabel } from \\"@gradio/atoms\\";\\nimport { Webcam } from \\"@gradio/image\\";\\nimport { Video } from \\"@gradio/icons\\";\\nimport { prettyBytes, playable } from \\"./utils\\";\\nimport Player from \\"./Player.svelte\\";\\nimport { SelectSource } from \\"@gradio/atoms\\";\\nexport let value = null;\\nexport let subtitle = null;\\nexport let sources = [\\"webcam\\", \\"upload\\"];\\nexport let label = void 0;\\nexport let show_download_button = false;\\nexport let show_label = true;\\nexport let webcam_options;\\nexport let include_audio;\\nexport let autoplay;\\nexport let root;\\nexport let i18n;\\nexport let active_source = \\"webcam\\";\\nexport let handle_reset_value = () => {\\n};\\nexport let max_file_size = null;\\nexport let upload;\\nexport let stream_handler;\\nexport let loop;\\nexport let uploading = false;\\nlet has_change_history = false;\\nconst dispatch = createEventDispatcher();\\nfunction handle_load({ detail }) {\\n    value = detail;\\n    dispatch(\\"change\\", detail);\\n    dispatch(\\"upload\\", detail);\\n}\\nfunction handle_clear() {\\n    value = null;\\n    dispatch(\\"change\\", null);\\n    dispatch(\\"clear\\");\\n}\\nfunction handle_change(video) {\\n    has_change_history = true;\\n    dispatch(\\"change\\", video);\\n}\\nfunction handle_capture({ detail }) {\\n    dispatch(\\"change\\", detail);\\n}\\nlet dragging = false;\\n$: dispatch(\\"drag\\", dragging);\\n<\/script>\\n\\n<BlockLabel {show_label} Icon={Video} label={label || \\"Video\\"} />\\n<div data-testid=\\"video\\" class=\\"video-container\\">\\n\\t{#if value === null || value.url === undefined}\\n\\t\\t<div class=\\"upload-container\\">\\n\\t\\t\\t{#if active_source === \\"upload\\"}\\n\\t\\t\\t\\t<Upload\\n\\t\\t\\t\\t\\tbind:dragging\\n\\t\\t\\t\\t\\tbind:uploading\\n\\t\\t\\t\\t\\tfiletype=\\"video/x-m4v,video/*\\"\\n\\t\\t\\t\\t\\ton:load={handle_load}\\n\\t\\t\\t\\t\\t{max_file_size}\\n\\t\\t\\t\\t\\ton:error={({ detail }) => dispatch(\\"error\\", detail)}\\n\\t\\t\\t\\t\\t{root}\\n\\t\\t\\t\\t\\t{upload}\\n\\t\\t\\t\\t\\t{stream_handler}\\n\\t\\t\\t\\t\\taria_label={i18n(\\"video.drop_to_upload\\")}\\n\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t<slot />\\n\\t\\t\\t\\t</Upload>\\n\\t\\t\\t{:else if active_source === \\"webcam\\"}\\n\\t\\t\\t\\t<Webcam\\n\\t\\t\\t\\t\\t{root}\\n\\t\\t\\t\\t\\tmirror_webcam={webcam_options.mirror}\\n\\t\\t\\t\\t\\twebcam_constraints={webcam_options.constraints}\\n\\t\\t\\t\\t\\t{include_audio}\\n\\t\\t\\t\\t\\tmode=\\"video\\"\\n\\t\\t\\t\\t\\ton:error\\n\\t\\t\\t\\t\\ton:capture={handle_capture}\\n\\t\\t\\t\\t\\ton:start_recording\\n\\t\\t\\t\\t\\ton:stop_recording\\n\\t\\t\\t\\t\\t{i18n}\\n\\t\\t\\t\\t\\t{upload}\\n\\t\\t\\t\\t\\tstream_every={1}\\n\\t\\t\\t\\t/>\\n\\t\\t\\t{/if}\\n\\t\\t</div>\\n\\t{:else if value?.url}\\n\\t\\t{#key value?.url}\\n\\t\\t\\t<Player\\n\\t\\t\\t\\t{upload}\\n\\t\\t\\t\\t{root}\\n\\t\\t\\t\\tinteractive\\n\\t\\t\\t\\t{autoplay}\\n\\t\\t\\t\\tsrc={value.url}\\n\\t\\t\\t\\tsubtitle={subtitle?.url}\\n\\t\\t\\t\\tis_stream={false}\\n\\t\\t\\t\\ton:play\\n\\t\\t\\t\\ton:pause\\n\\t\\t\\t\\ton:stop\\n\\t\\t\\t\\ton:end\\n\\t\\t\\t\\ton:error\\n\\t\\t\\t\\tmirror={webcam_options.mirror && active_source === \\"webcam\\"}\\n\\t\\t\\t\\t{label}\\n\\t\\t\\t\\t{handle_change}\\n\\t\\t\\t\\t{handle_reset_value}\\n\\t\\t\\t\\t{loop}\\n\\t\\t\\t\\t{value}\\n\\t\\t\\t\\t{i18n}\\n\\t\\t\\t\\t{show_download_button}\\n\\t\\t\\t\\t{handle_clear}\\n\\t\\t\\t\\t{has_change_history}\\n\\t\\t\\t/>\\n\\t\\t{/key}\\n\\t{:else if value.size}\\n\\t\\t<div class=\\"file-name\\">{value.orig_name || value.url}</div>\\n\\t\\t<div class=\\"file-size\\">\\n\\t\\t\\t{prettyBytes(value.size)}\\n\\t\\t</div>\\n\\t{/if}\\n\\n\\t<SelectSource {sources} bind:active_source {handle_clear} />\\n</div>\\n\\n<style>\\n\\t.file-name {\\n\\t\\tpadding: var(--size-6);\\n\\t\\tfont-size: var(--text-xxl);\\n\\t\\tword-break: break-all;\\n\\t}\\n\\n\\t.file-size {\\n\\t\\tpadding: var(--size-2);\\n\\t\\tfont-size: var(--text-xl);\\n\\t}\\n\\n\\t.upload-container {\\n\\t\\theight: 100%;\\n\\t\\twidth: 100%;\\n\\t}\\n\\n\\t.video-container {\\n\\t\\tdisplay: flex;\\n\\t\\theight: 100%;\\n\\t\\tflex-direction: column;\\n\\t\\tjustify-content: center;\\n\\t\\talign-items: center;\\n\\t}</style>\\n"],"names":[],"mappings":"AA4HC,yBAAW,CACV,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,SAAS,CAAE,IAAI,UAAU,CAAC,CAC1B,UAAU,CAAE,SACb,CAEA,yBAAW,CACV,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,SAAS,CAAE,IAAI,SAAS,CACzB,CAEA,gCAAkB,CACjB,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IACR,CAEA,+BAAiB,CAChB,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,CACZ,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MACd"}'},tt=create_ssr_component((a,t,e,P)=>{let{value:o=null}=t,{subtitle:h=null}=t,{sources:m=["webcam","upload"]}=t,{label:l=void 0}=t,{show_download_button:S=!1}=t,{show_label:A=!0}=t,{webcam_options:u}=t,{include_audio:z}=t,{autoplay:w}=t,{root:c}=t,{i18n:v}=t,{active_source:d="webcam"}=t,{handle_reset_value:C=()=>{}}=t,{max_file_size:y=null}=t,{upload:_}=t,{stream_handler:x}=t,{loop:k}=t,{uploading:s=!1}=t,i=!1;const B=createEventDispatcher();function E(){o=null,B("change",null),B("clear");}function j(n){i=!0,B("change",n);}let b=!1;t.value===void 0&&e.value&&o!==void 0&&e.value(o),t.subtitle===void 0&&e.subtitle&&h!==void 0&&e.subtitle(h),t.sources===void 0&&e.sources&&m!==void 0&&e.sources(m),t.label===void 0&&e.label&&l!==void 0&&e.label(l),t.show_download_button===void 0&&e.show_download_button&&S!==void 0&&e.show_download_button(S),t.show_label===void 0&&e.show_label&&A!==void 0&&e.show_label(A),t.webcam_options===void 0&&e.webcam_options&&u!==void 0&&e.webcam_options(u),t.include_audio===void 0&&e.include_audio&&z!==void 0&&e.include_audio(z),t.autoplay===void 0&&e.autoplay&&w!==void 0&&e.autoplay(w),t.root===void 0&&e.root&&c!==void 0&&e.root(c),t.i18n===void 0&&e.i18n&&v!==void 0&&e.i18n(v),t.active_source===void 0&&e.active_source&&d!==void 0&&e.active_source(d),t.handle_reset_value===void 0&&e.handle_reset_value&&C!==void 0&&e.handle_reset_value(C),t.max_file_size===void 0&&e.max_file_size&&y!==void 0&&e.max_file_size(y),t.upload===void 0&&e.upload&&_!==void 0&&e.upload(_),t.stream_handler===void 0&&e.stream_handler&&x!==void 0&&e.stream_handler(x),t.loop===void 0&&e.loop&&k!==void 0&&e.loop(k),t.uploading===void 0&&e.uploading&&s!==void 0&&e.uploading(s),a.css.add($);let f,V,U=a.head;do f=!0,a.head=U,B("drag",b),V=`${validate_component(bt,"BlockLabel").$$render(a,{show_label:A,Icon:ue,label:l||"Video"},{},{})} <div data-testid="video" class="video-container svelte-14jis2k">${o===null||o.url===void 0?`<div class="upload-container svelte-14jis2k">${d==="upload"?`${validate_component(ge,"Upload").$$render(a,{filetype:"video/x-m4v,video/*",max_file_size:y,root:c,upload:_,stream_handler:x,aria_label:v("video.drop_to_upload"),dragging:b,uploading:s},{dragging:n=>{b=n,f=!1;},uploading:n=>{s=n,f=!1;}},{default:()=>`${P.default?P.default({}):""}`})}`:`${d==="webcam"?`${validate_component(zt,"Webcam").$$render(a,{root:c,mirror_webcam:u.mirror,webcam_constraints:u.constraints,include_audio:z,mode:"video",i18n:v,upload:_,stream_every:1},{},{})}`:""}`}</div>`:`${o?.url?`${validate_component(bt$1,"Player").$$render(a,{upload:_,root:c,interactive:!0,autoplay:w,src:o.url,subtitle:h?.url,is_stream:!1,mirror:u.mirror&&d==="webcam",label:l,handle_change:j,handle_reset_value:C,loop:k,value:o,i18n:v,show_download_button:S,handle_clear:E,has_change_history:i},{},{})}`:`${o.size?`<div class="file-name svelte-14jis2k">${escape(o.orig_name||o.url)}</div> <div class="file-size svelte-14jis2k">${escape(zt$1(o.size))}</div>`:""}`}`} ${validate_component(ze,"SelectSource").$$render(a,{sources:m,handle_clear:E,active_source:d},{active_source:n=>{d=n,f=!1;}},{})} </div>`;while(!f);return V}),et=tt,lt=create_ssr_component((a,t,e,P)=>{let{elem_id:o=""}=t,{elem_classes:h=[]}=t,{visible:m=!0}=t,{value:l=null}=t,S=null,{label:A}=t,{sources:u}=t,{root:z}=t,{show_label:w}=t,{loading_status:c}=t,{height:v}=t,{width:d}=t,{container:C=!1}=t,{scale:y=null}=t,{min_width:_=void 0}=t,{autoplay:x=!1}=t,{show_share_button:k=!0}=t,{show_download_button:s}=t,{gradio:i}=t,{interactive:B}=t,{webcam_options:E}=t,{include_audio:j}=t,{loop:b=!1}=t,{input_ready:f}=t,V=!1,U=null,n=null,M,O=l;const J=()=>{O===null||l===O||(l=O);};t.elem_id===void 0&&e.elem_id&&o!==void 0&&e.elem_id(o),t.elem_classes===void 0&&e.elem_classes&&h!==void 0&&e.elem_classes(h),t.visible===void 0&&e.visible&&m!==void 0&&e.visible(m),t.value===void 0&&e.value&&l!==void 0&&e.value(l),t.label===void 0&&e.label&&A!==void 0&&e.label(A),t.sources===void 0&&e.sources&&u!==void 0&&e.sources(u),t.root===void 0&&e.root&&z!==void 0&&e.root(z),t.show_label===void 0&&e.show_label&&w!==void 0&&e.show_label(w),t.loading_status===void 0&&e.loading_status&&c!==void 0&&e.loading_status(c),t.height===void 0&&e.height&&v!==void 0&&e.height(v),t.width===void 0&&e.width&&d!==void 0&&e.width(d),t.container===void 0&&e.container&&C!==void 0&&e.container(C),t.scale===void 0&&e.scale&&y!==void 0&&e.scale(y),t.min_width===void 0&&e.min_width&&_!==void 0&&e.min_width(_),t.autoplay===void 0&&e.autoplay&&x!==void 0&&e.autoplay(x),t.show_share_button===void 0&&e.show_share_button&&k!==void 0&&e.show_share_button(k),t.show_download_button===void 0&&e.show_download_button&&s!==void 0&&e.show_download_button(s),t.gradio===void 0&&e.gradio&&i!==void 0&&e.gradio(i),t.interactive===void 0&&e.interactive&&B!==void 0&&e.interactive(B),t.webcam_options===void 0&&e.webcam_options&&E!==void 0&&e.webcam_options(E),t.include_audio===void 0&&e.include_audio&&j!==void 0&&e.include_audio(j),t.loop===void 0&&e.loop&&b!==void 0&&e.loop(b),t.input_ready===void 0&&e.input_ready&&f!==void 0&&e.input_ready(f);let W,p,K=a.head;do W=!0,a.head=K,f=!V,l&&O===null&&(O=l),u&&!M&&(M=u[0]),l!=null?(U=l.video,n=l.subtitles):(U=null,n=null),JSON.stringify(l)!==JSON.stringify(S)&&(S=l,i.dispatch("change")),p=`  ${B?`${validate_component(mt,"Block").$$render(a,{visible:m,variant:l===null&&M==="upload"?"dashed":"solid",border_mode:"base",padding:!1,elem_id:o,elem_classes:h,height:v,width:d,container:C,scale:y,min_width:_,allow_overflow:!1},{},{default:()=>`${validate_component(zA,"StatusTracker").$$render(a,Object.assign({},{autoscroll:i.autoscroll},{i18n:i.i18n},c),{},{})} ${validate_component(et,"Video").$$render(a,{value:U,subtitle:n,label:A,show_label:w,show_download_button:s,sources:u,active_source:M,webcam_options:E,include_audio:j,autoplay:x,root:z,loop:b,handle_reset_value:J,i18n:i.i18n,max_file_size:i.max_file_size,upload:(...I)=>i.client.upload(...I),stream_handler:(...I)=>i.client.stream(...I),uploading:V},{uploading:I=>{V=I,W=!1;}},{default:()=>`${validate_component(_e,"UploadText").$$render(a,{i18n:i.i18n,type:"video"},{},{})}`})}`})}`:`${validate_component(mt,"Block").$$render(a,{visible:m,variant:l===null&&M==="upload"?"dashed":"solid",border_mode:"base",padding:!1,elem_id:o,elem_classes:h,height:v,width:d,container:C,scale:y,min_width:_,allow_overflow:!1},{},{default:()=>`${validate_component(zA,"StatusTracker").$$render(a,Object.assign({},{autoscroll:i.autoscroll},{i18n:i.i18n},c),{},{})} ${validate_component(yt,"StaticVideo").$$render(a,{value:U,subtitle:n,label:A,show_label:w,autoplay:x,loop:b,show_share_button:k,show_download_button:s,i18n:i.i18n,upload:(...I)=>i.client.upload(...I)},{},{})}`})}`}`;while(!W);return p}),ct=lt;

export { et as BaseInteractiveVideo, bt$1 as BasePlayer, yt as BaseStaticVideo, ct as default, zt$1 as prettyBytes };
//# sourceMappingURL=index53-Ij-wVD2m.js.map
