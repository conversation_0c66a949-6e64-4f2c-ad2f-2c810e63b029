#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_nested_get_min_seqlen_ops.h>

namespace at {


// aten::_nested_get_min_seqlen(Tensor self) -> Tensor
inline at::Tensor _nested_get_min_seqlen(const at::Tensor & self) {
    return at::_ops::_nested_get_min_seqlen::call(self);
}

}
