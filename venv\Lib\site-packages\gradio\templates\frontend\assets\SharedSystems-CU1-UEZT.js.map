{"version": 3, "file": "SharedSystems-CU1-UEZT.js", "sources": ["../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/filters/mask/mask.frag.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/filters/mask/mask.vert.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/filters/mask/mask.wgsl.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/filters/mask/MaskFilter.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/batcher/shared/BatcherPipe.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/high-shader/shader-bits/textureBit.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/mask/alpha/AlphaMaskPipe.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/mask/color/ColorMaskPipe.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/mask/stencil/StencilMaskPipe.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gl/shader/program/ensureAttributes.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/state/GpuStencilModesToPixi.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/shared/shader/UboSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/shared/shader/utils/uniformParsers.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/shared/shader/utils/createUboSyncFunction.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/shared/shader/utils/uboSyncFunctions.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/renderTarget/calculateProjection.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/shared/texture/utils/getCanvasTexture.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/shared/renderTarget/isRenderingToScreen.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/shared/renderTarget/RenderTarget.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/shared/renderTarget/RenderTargetSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/shared/buffer/BufferResource.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/container/CustomRenderPipe.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/container/utils/executeInstructions.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/container/RenderGroupPipe.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/container/utils/clearList.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/container/utils/updateRenderGroupTransforms.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/container/utils/validateRenderables.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/container/RenderGroupSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/sprite/SpritePipe.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/shared/background/BackgroundSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/shared/blendModes/BlendModePipe.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/shared/extract/ExtractSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/shared/extract/GenerateTextureSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/shared/renderTarget/GlobalUniformSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/shared/SchedulerSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/utils/sayHello.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/shared/startup/HelloSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/utils/data/clean.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/shared/texture/RenderableGCSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/shared/texture/TextureGCSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/shared/view/ViewSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/shared/system/SharedSystems.mjs"], "sourcesContent": ["var fragment = \"in vec2 vMaskCoord;\\nin vec2 vTextureCoord;\\n\\nuniform sampler2D uTexture;\\nuniform sampler2D uMaskTexture;\\n\\nuniform float uAlpha;\\nuniform vec4 uMaskClamp;\\nuniform float uInverse;\\n\\nout vec4 finalColor;\\n\\nvoid main(void)\\n{\\n    float clip = step(3.5,\\n        step(uMaskClamp.x, vMaskCoord.x) +\\n        step(uMaskClamp.y, vMaskCoord.y) +\\n        step(vMaskCoord.x, uMaskClamp.z) +\\n        step(vMaskCoord.y, uMaskClamp.w));\\n\\n    // TODO look into why this is needed\\n    float npmAlpha = uAlpha;\\n    vec4 original = texture(uTexture, vTextureCoord);\\n    vec4 masky = texture(uMaskTexture, vMaskCoord);\\n    float alphaMul = 1.0 - npmAlpha * (1.0 - masky.a);\\n\\n    float a = alphaMul * masky.r * npmAlpha * clip;\\n\\n    if (uInverse == 1.0) {\\n        a = 1.0 - a;\\n    }\\n\\n    finalColor = original * a;\\n}\\n\";\n\nexport { fragment as default };\n//# sourceMappingURL=mask.frag.mjs.map\n", "var vertex = \"in vec2 aPosition;\\n\\nout vec2 vTextureCoord;\\nout vec2 vMaskCoord;\\n\\n\\nuniform vec4 uInputSize;\\nuniform vec4 uOutputFrame;\\nuniform vec4 uOutputTexture;\\nuniform mat3 uFilterMatrix;\\n\\nvec4 filterVertexPosition(  vec2 aPosition )\\n{\\n    vec2 position = aPosition * uOutputFrame.zw + uOutputFrame.xy;\\n       \\n    position.x = position.x * (2.0 / uOutputTexture.x) - 1.0;\\n    position.y = position.y * (2.0*uOutputTexture.z / uOutputTexture.y) - uOutputTexture.z;\\n\\n    return vec4(position, 0.0, 1.0);\\n}\\n\\nvec2 filterTextureCoord(  vec2 aPosition )\\n{\\n    return aPosition * (uOutputFrame.zw * uInputSize.zw);\\n}\\n\\nvec2 getFilterCoord( vec2 aPosition )\\n{\\n    return  ( uFilterMatrix * vec3( filterTextureCoord(aPosition), 1.0)  ).xy;\\n}   \\n\\nvoid main(void)\\n{\\n    gl_Position = filterVertexPosition(aPosition);\\n    vTextureCoord = filterTextureCoord(aPosition);\\n    vMaskCoord = getFilterCoord(aPosition);\\n}\\n\";\n\nexport { vertex as default };\n//# sourceMappingURL=mask.vert.mjs.map\n", "var source = \"struct GlobalFilterUniforms {\\n  uInputSize:vec4<f32>,\\n  uInputPixel:vec4<f32>,\\n  uInputClamp:vec4<f32>,\\n  uOutputFrame:vec4<f32>,\\n  uGlobalFrame:vec4<f32>,\\n  uOutputTexture:vec4<f32>,\\n};\\n\\nstruct MaskUniforms {\\n  uFilterMatrix:mat3x3<f32>,\\n  uMaskClamp:vec4<f32>,\\n  uAlpha:f32,\\n  uInverse:f32,\\n};\\n\\n@group(0) @binding(0) var<uniform> gfu: GlobalFilterUniforms;\\n@group(0) @binding(1) var uTexture: texture_2d<f32>;\\n@group(0) @binding(2) var uSampler : sampler;\\n\\n@group(1) @binding(0) var<uniform> filterUniforms : MaskUniforms;\\n@group(1) @binding(1) var uMaskTexture: texture_2d<f32>;\\n\\nstruct VSOutput {\\n    @builtin(position) position: vec4<f32>,\\n    @location(0) uv : vec2<f32>,\\n    @location(1) filterUv : vec2<f32>,\\n};\\n\\nfn filterVertexPosition(aPosition:vec2<f32>) -> vec4<f32>\\n{\\n    var position = aPosition * gfu.uOutputFrame.zw + gfu.uOutputFrame.xy;\\n\\n    position.x = position.x * (2.0 / gfu.uOutputTexture.x) - 1.0;\\n    position.y = position.y * (2.0*gfu.uOutputTexture.z / gfu.uOutputTexture.y) - gfu.uOutputTexture.z;\\n\\n    return vec4(position, 0.0, 1.0);\\n}\\n\\nfn filterTextureCoord( aPosition:vec2<f32> ) -> vec2<f32>\\n{\\n    return aPosition * (gfu.uOutputFrame.zw * gfu.uInputSize.zw);\\n}\\n\\nfn globalTextureCoord( aPosition:vec2<f32> ) -> vec2<f32>\\n{\\n  return  (aPosition.xy / gfu.uGlobalFrame.zw) + (gfu.uGlobalFrame.xy / gfu.uGlobalFrame.zw);\\n}\\n\\nfn getFilterCoord(aPosition:vec2<f32> ) -> vec2<f32>\\n{\\n  return ( filterUniforms.uFilterMatrix * vec3( filterTextureCoord(aPosition), 1.0)  ).xy;\\n}\\n\\nfn getSize() -> vec2<f32>\\n{\\n  return gfu.uGlobalFrame.zw;\\n}\\n\\n@vertex\\nfn mainVertex(\\n  @location(0) aPosition : vec2<f32>,\\n) -> VSOutput {\\n  return VSOutput(\\n   filterVertexPosition(aPosition),\\n   filterTextureCoord(aPosition),\\n   getFilterCoord(aPosition)\\n  );\\n}\\n\\n@fragment\\nfn mainFragment(\\n  @location(0) uv: vec2<f32>,\\n  @location(1) filterUv: vec2<f32>,\\n  @builtin(position) position: vec4<f32>\\n) -> @location(0) vec4<f32> {\\n\\n    var maskClamp = filterUniforms.uMaskClamp;\\n    var uAlpha = filterUniforms.uAlpha;\\n\\n    var clip = step(3.5,\\n      step(maskClamp.x, filterUv.x) +\\n      step(maskClamp.y, filterUv.y) +\\n      step(filterUv.x, maskClamp.z) +\\n      step(filterUv.y, maskClamp.w));\\n\\n    var mask = textureSample(uMaskTexture, uSampler, filterUv);\\n    var source = textureSample(uTexture, uSampler, uv);\\n    var alphaMul = 1.0 - uAlpha * (1.0 - mask.a);\\n\\n    var a: f32 = alphaMul * mask.r * uAlpha * clip;\\n\\n    if (filterUniforms.uInverse == 1.0) {\\n        a = 1.0 - a;\\n    }\\n\\n    return source * a;\\n}\\n\";\n\nexport { source as default };\n//# sourceMappingURL=mask.wgsl.mjs.map\n", "import { Matrix } from '../../maths/matrix/Matrix.mjs';\nimport { GlProgram } from '../../rendering/renderers/gl/shader/GlProgram.mjs';\nimport { GpuProgram } from '../../rendering/renderers/gpu/shader/GpuProgram.mjs';\nimport { UniformGroup } from '../../rendering/renderers/shared/shader/UniformGroup.mjs';\nimport { TextureMatrix } from '../../rendering/renderers/shared/texture/TextureMatrix.mjs';\nimport { Filter } from '../Filter.mjs';\nimport fragment from './mask.frag.mjs';\nimport vertex from './mask.vert.mjs';\nimport source from './mask.wgsl.mjs';\n\n\"use strict\";\nclass MaskFilter extends Filter {\n  constructor(options) {\n    const { sprite, ...rest } = options;\n    const textureMatrix = new TextureMatrix(sprite.texture);\n    const filterUniforms = new UniformGroup({\n      uFilterMatrix: { value: new Matrix(), type: \"mat3x3<f32>\" },\n      uMaskClamp: { value: textureMatrix.uClampFrame, type: \"vec4<f32>\" },\n      uAlpha: { value: 1, type: \"f32\" },\n      uInverse: { value: options.inverse ? 1 : 0, type: \"f32\" }\n    });\n    const gpuProgram = GpuProgram.from({\n      vertex: {\n        source,\n        entryPoint: \"mainVertex\"\n      },\n      fragment: {\n        source,\n        entryPoint: \"mainFragment\"\n      }\n    });\n    const glProgram = GlProgram.from({\n      vertex,\n      fragment,\n      name: \"mask-filter\"\n    });\n    super({\n      ...rest,\n      gpuProgram,\n      glProgram,\n      resources: {\n        filterUniforms,\n        uMaskTexture: sprite.texture.source\n      }\n    });\n    this.sprite = sprite;\n    this._textureMatrix = textureMatrix;\n  }\n  set inverse(value) {\n    this.resources.filterUniforms.uniforms.uInverse = value ? 1 : 0;\n  }\n  get inverse() {\n    return this.resources.filterUniforms.uniforms.uInverse === 1;\n  }\n  apply(filterManager, input, output, clearMode) {\n    this._textureMatrix.texture = this.sprite.texture;\n    filterManager.calculateSpriteMatrix(\n      this.resources.filterUniforms.uniforms.uFilterMatrix,\n      this.sprite\n    ).prepend(this._textureMatrix.mapCoord);\n    this.resources.uMaskTexture = this.sprite.texture.source;\n    filterManager.applyFilter(this, input, output, clearMode);\n  }\n}\n\nexport { MaskFilter };\n//# sourceMappingURL=MaskFilter.mjs.map\n", "import { ExtensionType, extensions } from '../../../extensions/Extensions.mjs';\nimport { State } from '../../renderers/shared/state/State.mjs';\nimport { DefaultBatcher } from './DefaultBatcher.mjs';\n\n\"use strict\";\nconst _BatcherPipe = class _BatcherPipe {\n  constructor(renderer, adaptor) {\n    this.state = State.for2d();\n    this._batchersByInstructionSet = /* @__PURE__ */ Object.create(null);\n    /** A record of all active batchers, keyed by their names */\n    this._activeBatches = /* @__PURE__ */ Object.create(null);\n    this.renderer = renderer;\n    this._adaptor = adaptor;\n    this._adaptor.init?.(this);\n  }\n  static getBatcher(name) {\n    return new this._availableBatchers[name]();\n  }\n  buildStart(instructionSet) {\n    let batchers = this._batchersByInstructionSet[instructionSet.uid];\n    if (!batchers) {\n      batchers = this._batchersByInstructionSet[instructionSet.uid] = /* @__PURE__ */ Object.create(null);\n      batchers.default || (batchers.default = new DefaultBatcher());\n    }\n    this._activeBatches = batchers;\n    this._activeBatch = this._activeBatches.default;\n    for (const i in this._activeBatches) {\n      this._activeBatches[i].begin();\n    }\n  }\n  addToBatch(batchableObject, instructionSet) {\n    if (this._activeBatch.name !== batchableObject.batcherName) {\n      this._activeBatch.break(instructionSet);\n      let batch = this._activeBatches[batchableObject.batcherName];\n      if (!batch) {\n        batch = this._activeBatches[batchableObject.batcherName] = _BatcherPipe.getBatcher(batchableObject.batcherName);\n        batch.begin();\n      }\n      this._activeBatch = batch;\n    }\n    this._activeBatch.add(batchableObject);\n  }\n  break(instructionSet) {\n    this._activeBatch.break(instructionSet);\n  }\n  buildEnd(instructionSet) {\n    this._activeBatch.break(instructionSet);\n    const batches = this._activeBatches;\n    for (const i in batches) {\n      const batch = batches[i];\n      const geometry = batch.geometry;\n      geometry.indexBuffer.setDataWithSize(batch.indexBuffer, batch.indexSize, true);\n      geometry.buffers[0].setDataWithSize(batch.attributeBuffer.float32View, batch.attributeSize, false);\n    }\n  }\n  upload(instructionSet) {\n    const batchers = this._batchersByInstructionSet[instructionSet.uid];\n    for (const i in batchers) {\n      const batcher = batchers[i];\n      const geometry = batcher.geometry;\n      if (batcher.dirty) {\n        batcher.dirty = false;\n        geometry.buffers[0].update(batcher.attributeSize * 4);\n      }\n    }\n  }\n  execute(batch) {\n    if (batch.action === \"startBatch\") {\n      const batcher = batch.batcher;\n      const geometry = batcher.geometry;\n      const shader = batcher.shader;\n      this._adaptor.start(this, geometry, shader);\n    }\n    this._adaptor.execute(this, batch);\n  }\n  destroy() {\n    this.state = null;\n    this.renderer = null;\n    this._adaptor = null;\n    for (const i in this._activeBatches) {\n      this._activeBatches[i].destroy();\n    }\n    this._activeBatches = null;\n  }\n};\n/** @ignore */\n_BatcherPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"batch\"\n};\n_BatcherPipe._availableBatchers = /* @__PURE__ */ Object.create(null);\nlet BatcherPipe = _BatcherPipe;\nextensions.handleByMap(ExtensionType.Batcher, BatcherPipe._availableBatchers);\nextensions.add(DefaultBatcher);\n\nexport { BatcherPipe };\n//# sourceMappingURL=BatcherPipe.mjs.map\n", "\"use strict\";\nconst textureBit = {\n  name: \"texture-bit\",\n  vertex: {\n    header: (\n      /* wgsl */\n      `\n\n        struct TextureUniforms {\n            uTextureMatrix:mat3x3<f32>,\n        }\n\n        @group(2) @binding(2) var<uniform> textureUniforms : TextureUniforms;\n        `\n    ),\n    main: (\n      /* wgsl */\n      `\n            uv = (textureUniforms.uTextureMatrix * vec3(uv, 1.0)).xy;\n        `\n    )\n  },\n  fragment: {\n    header: (\n      /* wgsl */\n      `\n            @group(2) @binding(0) var uTexture: texture_2d<f32>;\n            @group(2) @binding(1) var uSampler: sampler;\n\n         \n        `\n    ),\n    main: (\n      /* wgsl */\n      `\n            outColor = textureSample(uTexture, uSampler, vUV);\n        `\n    )\n  }\n};\nconst textureBitGl = {\n  name: \"texture-bit\",\n  vertex: {\n    header: (\n      /* glsl */\n      `\n            uniform mat3 uTextureMatrix;\n        `\n    ),\n    main: (\n      /* glsl */\n      `\n            uv = (uTextureMatrix * vec3(uv, 1.0)).xy;\n        `\n    )\n  },\n  fragment: {\n    header: (\n      /* glsl */\n      `\n        uniform sampler2D uTexture;\n\n         \n        `\n    ),\n    main: (\n      /* glsl */\n      `\n            outColor = texture(uTexture, vUV);\n        `\n    )\n  }\n};\n\nexport { textureBit, textureBitGl };\n//# sourceMappingURL=textureBit.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { FilterEffect } from '../../../filters/FilterEffect.mjs';\nimport { MaskFilter } from '../../../filters/mask/MaskFilter.mjs';\nimport { Bounds } from '../../../scene/container/bounds/Bounds.mjs';\nimport { getGlobalBounds } from '../../../scene/container/bounds/getGlobalBounds.mjs';\nimport { Sprite } from '../../../scene/sprite/Sprite.mjs';\nimport { BigPool } from '../../../utils/pool/PoolGroup.mjs';\nimport { Texture } from '../../renderers/shared/texture/Texture.mjs';\nimport { TexturePool } from '../../renderers/shared/texture/TexturePool.mjs';\nimport { RendererType } from '../../renderers/types.mjs';\n\n\"use strict\";\nconst tempBounds = new Bounds();\nclass AlphaMaskEffect extends FilterEffect {\n  constructor() {\n    super();\n    this.filters = [new MaskFilter({\n      sprite: new Sprite(Texture.EMPTY),\n      inverse: false,\n      resolution: \"inherit\",\n      antialias: \"inherit\"\n    })];\n  }\n  get sprite() {\n    return this.filters[0].sprite;\n  }\n  set sprite(value) {\n    this.filters[0].sprite = value;\n  }\n  get inverse() {\n    return this.filters[0].inverse;\n  }\n  set inverse(value) {\n    this.filters[0].inverse = value;\n  }\n}\nclass AlphaMaskPipe {\n  constructor(renderer) {\n    this._activeMaskStage = [];\n    this._renderer = renderer;\n  }\n  push(mask, maskedContainer, instructionSet) {\n    const renderer = this._renderer;\n    renderer.renderPipes.batch.break(instructionSet);\n    instructionSet.add({\n      renderPipeId: \"alphaMask\",\n      action: \"pushMaskBegin\",\n      mask,\n      inverse: maskedContainer._maskOptions.inverse,\n      canBundle: false,\n      maskedContainer\n    });\n    mask.inverse = maskedContainer._maskOptions.inverse;\n    if (mask.renderMaskToTexture) {\n      const maskContainer = mask.mask;\n      maskContainer.includeInBuild = true;\n      maskContainer.collectRenderables(\n        instructionSet,\n        renderer,\n        null\n      );\n      maskContainer.includeInBuild = false;\n    }\n    renderer.renderPipes.batch.break(instructionSet);\n    instructionSet.add({\n      renderPipeId: \"alphaMask\",\n      action: \"pushMaskEnd\",\n      mask,\n      maskedContainer,\n      inverse: maskedContainer._maskOptions.inverse,\n      canBundle: false\n    });\n  }\n  pop(mask, _maskedContainer, instructionSet) {\n    const renderer = this._renderer;\n    renderer.renderPipes.batch.break(instructionSet);\n    instructionSet.add({\n      renderPipeId: \"alphaMask\",\n      action: \"popMaskEnd\",\n      mask,\n      inverse: _maskedContainer._maskOptions.inverse,\n      canBundle: false\n    });\n  }\n  execute(instruction) {\n    const renderer = this._renderer;\n    const renderMask = instruction.mask.renderMaskToTexture;\n    if (instruction.action === \"pushMaskBegin\") {\n      const filterEffect = BigPool.get(AlphaMaskEffect);\n      filterEffect.inverse = instruction.inverse;\n      if (renderMask) {\n        instruction.mask.mask.measurable = true;\n        const bounds = getGlobalBounds(instruction.mask.mask, true, tempBounds);\n        instruction.mask.mask.measurable = false;\n        bounds.ceil();\n        const colorTextureSource = renderer.renderTarget.renderTarget.colorTexture.source;\n        const filterTexture = TexturePool.getOptimalTexture(\n          bounds.width,\n          bounds.height,\n          colorTextureSource._resolution,\n          colorTextureSource.antialias\n        );\n        renderer.renderTarget.push(filterTexture, true);\n        renderer.globalUniforms.push({\n          offset: bounds,\n          worldColor: 4294967295\n        });\n        const sprite = filterEffect.sprite;\n        sprite.texture = filterTexture;\n        sprite.worldTransform.tx = bounds.minX;\n        sprite.worldTransform.ty = bounds.minY;\n        this._activeMaskStage.push({\n          filterEffect,\n          maskedContainer: instruction.maskedContainer,\n          filterTexture\n        });\n      } else {\n        filterEffect.sprite = instruction.mask.mask;\n        this._activeMaskStage.push({\n          filterEffect,\n          maskedContainer: instruction.maskedContainer\n        });\n      }\n    } else if (instruction.action === \"pushMaskEnd\") {\n      const maskData = this._activeMaskStage[this._activeMaskStage.length - 1];\n      if (renderMask) {\n        if (renderer.type === RendererType.WEBGL) {\n          renderer.renderTarget.finishRenderPass();\n        }\n        renderer.renderTarget.pop();\n        renderer.globalUniforms.pop();\n      }\n      renderer.filter.push({\n        renderPipeId: \"filter\",\n        action: \"pushFilter\",\n        container: maskData.maskedContainer,\n        filterEffect: maskData.filterEffect,\n        canBundle: false\n      });\n    } else if (instruction.action === \"popMaskEnd\") {\n      renderer.filter.pop();\n      const maskData = this._activeMaskStage.pop();\n      if (renderMask) {\n        TexturePool.returnTexture(maskData.filterTexture);\n      }\n      BigPool.return(maskData.filterEffect);\n    }\n  }\n  destroy() {\n    this._renderer = null;\n    this._activeMaskStage = null;\n  }\n}\n/** @ignore */\nAlphaMaskPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"alphaMask\"\n};\n\nexport { AlphaMaskPipe };\n//# sourceMappingURL=AlphaMaskPipe.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\n\n\"use strict\";\nclass ColorMaskPipe {\n  constructor(renderer) {\n    this._colorStack = [];\n    this._colorStackIndex = 0;\n    this._currentColor = 0;\n    this._renderer = renderer;\n  }\n  buildStart() {\n    this._colorStack[0] = 15;\n    this._colorStackIndex = 1;\n    this._currentColor = 15;\n  }\n  push(mask, _container, instructionSet) {\n    const renderer = this._renderer;\n    renderer.renderPipes.batch.break(instructionSet);\n    const colorStack = this._colorStack;\n    colorStack[this._colorStackIndex] = colorStack[this._colorStackIndex - 1] & mask.mask;\n    const currentColor = this._colorStack[this._colorStackIndex];\n    if (currentColor !== this._currentColor) {\n      this._currentColor = currentColor;\n      instructionSet.add({\n        renderPipeId: \"colorMask\",\n        colorMask: currentColor,\n        canBundle: false\n      });\n    }\n    this._colorStackIndex++;\n  }\n  pop(_mask, _container, instructionSet) {\n    const renderer = this._renderer;\n    renderer.renderPipes.batch.break(instructionSet);\n    const colorStack = this._colorStack;\n    this._colorStackIndex--;\n    const currentColor = colorStack[this._colorStackIndex - 1];\n    if (currentColor !== this._currentColor) {\n      this._currentColor = currentColor;\n      instructionSet.add({\n        renderPipeId: \"colorMask\",\n        colorMask: currentColor,\n        canBundle: false\n      });\n    }\n  }\n  execute(instruction) {\n    const renderer = this._renderer;\n    renderer.colorMask.setMask(instruction.colorMask);\n  }\n  destroy() {\n    this._colorStack = null;\n  }\n}\n/** @ignore */\nColorMaskPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"colorMask\"\n};\n\nexport { ColorMaskPipe };\n//# sourceMappingURL=ColorMaskPipe.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { CLEAR } from '../../renderers/gl/const.mjs';\nimport { STENCIL_MODES } from '../../renderers/shared/state/const.mjs';\n\n\"use strict\";\nclass StencilMaskPipe {\n  constructor(renderer) {\n    // used when building and also when executing..\n    this._maskStackHash = {};\n    this._maskHash = /* @__PURE__ */ new WeakMap();\n    this._renderer = renderer;\n  }\n  push(mask, _container, instructionSet) {\n    var _a;\n    const effect = mask;\n    const renderer = this._renderer;\n    renderer.renderPipes.batch.break(instructionSet);\n    renderer.renderPipes.blendMode.setBlendMode(effect.mask, \"none\", instructionSet);\n    instructionSet.add({\n      renderPipeId: \"stencilMask\",\n      action: \"pushMaskBegin\",\n      mask,\n      inverse: _container._maskOptions.inverse,\n      canBundle: false\n    });\n    const maskContainer = effect.mask;\n    maskContainer.includeInBuild = true;\n    if (!this._maskHash.has(effect)) {\n      this._maskHash.set(effect, {\n        instructionsStart: 0,\n        instructionsLength: 0\n      });\n    }\n    const maskData = this._maskHash.get(effect);\n    maskData.instructionsStart = instructionSet.instructionSize;\n    maskContainer.collectRenderables(\n      instructionSet,\n      renderer,\n      null\n    );\n    maskContainer.includeInBuild = false;\n    renderer.renderPipes.batch.break(instructionSet);\n    instructionSet.add({\n      renderPipeId: \"stencilMask\",\n      action: \"pushMaskEnd\",\n      mask,\n      inverse: _container._maskOptions.inverse,\n      canBundle: false\n    });\n    const instructionsLength = instructionSet.instructionSize - maskData.instructionsStart - 1;\n    maskData.instructionsLength = instructionsLength;\n    const renderTargetUid = renderer.renderTarget.renderTarget.uid;\n    (_a = this._maskStackHash)[renderTargetUid] ?? (_a[renderTargetUid] = 0);\n  }\n  pop(mask, _container, instructionSet) {\n    const effect = mask;\n    const renderer = this._renderer;\n    renderer.renderPipes.batch.break(instructionSet);\n    renderer.renderPipes.blendMode.setBlendMode(effect.mask, \"none\", instructionSet);\n    instructionSet.add({\n      renderPipeId: \"stencilMask\",\n      action: \"popMaskBegin\",\n      inverse: _container._maskOptions.inverse,\n      canBundle: false\n    });\n    const maskData = this._maskHash.get(mask);\n    for (let i = 0; i < maskData.instructionsLength; i++) {\n      instructionSet.instructions[instructionSet.instructionSize++] = instructionSet.instructions[maskData.instructionsStart++];\n    }\n    instructionSet.add({\n      renderPipeId: \"stencilMask\",\n      action: \"popMaskEnd\",\n      canBundle: false\n    });\n  }\n  execute(instruction) {\n    var _a;\n    const renderer = this._renderer;\n    const renderTargetUid = renderer.renderTarget.renderTarget.uid;\n    let maskStackIndex = (_a = this._maskStackHash)[renderTargetUid] ?? (_a[renderTargetUid] = 0);\n    if (instruction.action === \"pushMaskBegin\") {\n      renderer.renderTarget.ensureDepthStencil();\n      renderer.stencil.setStencilMode(STENCIL_MODES.RENDERING_MASK_ADD, maskStackIndex);\n      maskStackIndex++;\n      renderer.colorMask.setMask(0);\n    } else if (instruction.action === \"pushMaskEnd\") {\n      if (instruction.inverse) {\n        renderer.stencil.setStencilMode(STENCIL_MODES.INVERSE_MASK_ACTIVE, maskStackIndex);\n      } else {\n        renderer.stencil.setStencilMode(STENCIL_MODES.MASK_ACTIVE, maskStackIndex);\n      }\n      renderer.colorMask.setMask(15);\n    } else if (instruction.action === \"popMaskBegin\") {\n      renderer.colorMask.setMask(0);\n      if (maskStackIndex !== 0) {\n        renderer.stencil.setStencilMode(STENCIL_MODES.RENDERING_MASK_REMOVE, maskStackIndex);\n      } else {\n        renderer.renderTarget.clear(null, CLEAR.STENCIL);\n        renderer.stencil.setStencilMode(STENCIL_MODES.DISABLED, maskStackIndex);\n      }\n      maskStackIndex--;\n    } else if (instruction.action === \"popMaskEnd\") {\n      if (instruction.inverse) {\n        renderer.stencil.setStencilMode(STENCIL_MODES.INVERSE_MASK_ACTIVE, maskStackIndex);\n      } else {\n        renderer.stencil.setStencilMode(STENCIL_MODES.MASK_ACTIVE, maskStackIndex);\n      }\n      renderer.colorMask.setMask(15);\n    }\n    this._maskStackHash[renderTargetUid] = maskStackIndex;\n  }\n  destroy() {\n    this._renderer = null;\n    this._maskStackHash = null;\n    this._maskHash = null;\n  }\n}\nStencilMaskPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"stencilMask\"\n};\n\nexport { StencilMaskPipe };\n//# sourceMappingURL=StencilMaskPipe.mjs.map\n", "import { warn } from '../../../../../utils/logging/warn.mjs';\nimport { getAttributeInfoFromFormat } from '../../../shared/geometry/utils/getAttributeInfoFromFormat.mjs';\n\n\"use strict\";\nfunction ensureAttributes(geometry, extractedData) {\n  for (const i in geometry.attributes) {\n    const attribute = geometry.attributes[i];\n    const attributeData = extractedData[i];\n    if (attributeData) {\n      attribute.format ?? (attribute.format = attributeData.format);\n      attribute.offset ?? (attribute.offset = attributeData.offset);\n      attribute.instance ?? (attribute.instance = attributeData.instance);\n    } else {\n      warn(`Attribute ${i} is not present in the shader, but is present in the geometry. Unable to infer attribute details.`);\n    }\n  }\n  ensureStartAndStride(geometry);\n}\nfunction ensureStartAndStride(geometry) {\n  const { buffers, attributes } = geometry;\n  const tempStride = {};\n  const tempStart = {};\n  for (const j in buffers) {\n    const buffer = buffers[j];\n    tempStride[buffer.uid] = 0;\n    tempStart[buffer.uid] = 0;\n  }\n  for (const j in attributes) {\n    const attribute = attributes[j];\n    tempStride[attribute.buffer.uid] += getAttributeInfoFromFormat(attribute.format).stride;\n  }\n  for (const j in attributes) {\n    const attribute = attributes[j];\n    attribute.stride ?? (attribute.stride = tempStride[attribute.buffer.uid]);\n    attribute.start ?? (attribute.start = tempStart[attribute.buffer.uid]);\n    tempStart[attribute.buffer.uid] += getAttributeInfoFromFormat(attribute.format).stride;\n  }\n}\n\nexport { ensureAttributes };\n//# sourceMappingURL=ensureAttributes.mjs.map\n", "import { STENCIL_MODES } from '../../shared/state/const.mjs';\n\n\"use strict\";\nconst GpuStencilModesToPixi = [];\nGpuStencilModesToPixi[STENCIL_MODES.NONE] = void 0;\nGpuStencilModesToPixi[STENCIL_MODES.DISABLED] = {\n  stencilWriteMask: 0,\n  stencilReadMask: 0\n};\nGpuStencilModesToPixi[STENCIL_MODES.RENDERING_MASK_ADD] = {\n  stencilFront: {\n    compare: \"equal\",\n    passOp: \"increment-clamp\"\n  },\n  stencilBack: {\n    compare: \"equal\",\n    passOp: \"increment-clamp\"\n  }\n};\nGpuStencilModesToPixi[STENCIL_MODES.RENDERING_MASK_REMOVE] = {\n  stencilFront: {\n    compare: \"equal\",\n    passOp: \"decrement-clamp\"\n  },\n  stencilBack: {\n    compare: \"equal\",\n    passOp: \"decrement-clamp\"\n  }\n};\nGpuStencilModesToPixi[STENCIL_MODES.MASK_ACTIVE] = {\n  stencilWriteMask: 0,\n  stencilFront: {\n    compare: \"equal\",\n    passOp: \"keep\"\n  },\n  stencilBack: {\n    compare: \"equal\",\n    passOp: \"keep\"\n  }\n};\nGpuStencilModesToPixi[STENCIL_MODES.INVERSE_MASK_ACTIVE] = {\n  stencilWriteMask: 0,\n  stencilFront: {\n    compare: \"not-equal\",\n    passOp: \"replace\"\n  },\n  stencilBack: {\n    compare: \"not-equal\",\n    passOp: \"replace\"\n  }\n};\n\nexport { GpuStencilModesToPixi };\n//# sourceMappingURL=GpuStencilModesToPixi.mjs.map\n", "import { unsafeEvalSupported } from '../../../../utils/browser/unsafeEvalSupported.mjs';\nimport { Buffer } from '../buffer/Buffer.mjs';\nimport { BufferUsage } from '../buffer/const.mjs';\n\n\"use strict\";\nclass UboSystem {\n  constructor(adaptor) {\n    /** Cache of uniform buffer layouts and sync functions, so we don't have to re-create them */\n    this._syncFunctionHash = /* @__PURE__ */ Object.create(null);\n    this._adaptor = adaptor;\n    this._systemCheck();\n  }\n  /**\n   * Overridable function by `pixi.js/unsafe-eval` to silence\n   * throwing an error if platform doesn't support unsafe-evals.\n   * @private\n   */\n  _systemCheck() {\n    if (!unsafeEvalSupported()) {\n      throw new Error(\"Current environment does not allow unsafe-eval, please use pixi.js/unsafe-eval module to enable support.\");\n    }\n  }\n  ensureUniformGroup(uniformGroup) {\n    const uniformData = this.getUniformGroupData(uniformGroup);\n    uniformGroup.buffer || (uniformGroup.buffer = new Buffer({\n      data: new Float32Array(uniformData.layout.size / 4),\n      usage: BufferUsage.UNIFORM | BufferUsage.COPY_DST\n    }));\n  }\n  getUniformGroupData(uniformGroup) {\n    return this._syncFunctionHash[uniformGroup._signature] || this._initUniformGroup(uniformGroup);\n  }\n  _initUniformGroup(uniformGroup) {\n    const uniformGroupSignature = uniformGroup._signature;\n    let uniformData = this._syncFunctionHash[uniformGroupSignature];\n    if (!uniformData) {\n      const elements = Object.keys(uniformGroup.uniformStructures).map((i) => uniformGroup.uniformStructures[i]);\n      const layout = this._adaptor.createUboElements(elements);\n      const syncFunction = this._generateUboSync(layout.uboElements);\n      uniformData = this._syncFunctionHash[uniformGroupSignature] = {\n        layout,\n        syncFunction\n      };\n    }\n    return this._syncFunctionHash[uniformGroupSignature];\n  }\n  _generateUboSync(uboElements) {\n    return this._adaptor.generateUboSync(uboElements);\n  }\n  syncUniformGroup(uniformGroup, data, offset) {\n    const uniformGroupData = this.getUniformGroupData(uniformGroup);\n    uniformGroup.buffer || (uniformGroup.buffer = new Buffer({\n      data: new Float32Array(uniformGroupData.layout.size / 4),\n      usage: BufferUsage.UNIFORM | BufferUsage.COPY_DST\n    }));\n    let dataInt32 = null;\n    if (!data) {\n      data = uniformGroup.buffer.data;\n      dataInt32 = uniformGroup.buffer.dataInt32;\n    }\n    offset || (offset = 0);\n    uniformGroupData.syncFunction(uniformGroup.uniforms, data, dataInt32, offset);\n    return true;\n  }\n  updateUniformGroup(uniformGroup) {\n    if (uniformGroup.isStatic && !uniformGroup._dirtyId)\n      return false;\n    uniformGroup._dirtyId = 0;\n    const synced = this.syncUniformGroup(uniformGroup);\n    uniformGroup.buffer.update();\n    return synced;\n  }\n  destroy() {\n    this._syncFunctionHash = null;\n  }\n}\n\nexport { UboSystem };\n//# sourceMappingURL=UboSystem.mjs.map\n", "\"use strict\";\nconst uniformParsers = [\n  // uploading pixi matrix object to mat3\n  {\n    type: \"mat3x3<f32>\",\n    test: (data) => {\n      const value = data.value;\n      return value.a !== void 0;\n    },\n    ubo: `\n            var matrix = uv[name].toArray(true);\n            data[offset] = matrix[0];\n            data[offset + 1] = matrix[1];\n            data[offset + 2] = matrix[2];\n            data[offset + 4] = matrix[3];\n            data[offset + 5] = matrix[4];\n            data[offset + 6] = matrix[5];\n            data[offset + 8] = matrix[6];\n            data[offset + 9] = matrix[7];\n            data[offset + 10] = matrix[8];\n        `,\n    uniform: `\n            gl.uniformMatrix3fv(ud[name].location, false, uv[name].toArray(true));\n        `\n  },\n  // uploading a pixi rectangle as a vec4\n  {\n    type: \"vec4<f32>\",\n    test: (data) => data.type === \"vec4<f32>\" && data.size === 1 && data.value.width !== void 0,\n    ubo: `\n            v = uv[name];\n            data[offset] = v.x;\n            data[offset + 1] = v.y;\n            data[offset + 2] = v.width;\n            data[offset + 3] = v.height;\n        `,\n    uniform: `\n            cv = ud[name].value;\n            v = uv[name];\n            if (cv[0] !== v.x || cv[1] !== v.y || cv[2] !== v.width || cv[3] !== v.height) {\n                cv[0] = v.x;\n                cv[1] = v.y;\n                cv[2] = v.width;\n                cv[3] = v.height;\n                gl.uniform4f(ud[name].location, v.x, v.y, v.width, v.height);\n            }\n        `\n  },\n  // uploading a pixi point as a vec2\n  {\n    type: \"vec2<f32>\",\n    test: (data) => data.type === \"vec2<f32>\" && data.size === 1 && data.value.x !== void 0,\n    ubo: `\n            v = uv[name];\n            data[offset] = v.x;\n            data[offset + 1] = v.y;\n        `,\n    uniform: `\n            cv = ud[name].value;\n            v = uv[name];\n            if (cv[0] !== v.x || cv[1] !== v.y) {\n                cv[0] = v.x;\n                cv[1] = v.y;\n                gl.uniform2f(ud[name].location, v.x, v.y);\n            }\n        `\n  },\n  // uploading a pixi color as a vec4\n  {\n    type: \"vec4<f32>\",\n    test: (data) => data.type === \"vec4<f32>\" && data.size === 1 && data.value.red !== void 0,\n    ubo: `\n            v = uv[name];\n            data[offset] = v.red;\n            data[offset + 1] = v.green;\n            data[offset + 2] = v.blue;\n            data[offset + 3] = v.alpha;\n        `,\n    uniform: `\n            cv = ud[name].value;\n            v = uv[name];\n            if (cv[0] !== v.red || cv[1] !== v.green || cv[2] !== v.blue || cv[3] !== v.alpha) {\n                cv[0] = v.red;\n                cv[1] = v.green;\n                cv[2] = v.blue;\n                cv[3] = v.alpha;\n                gl.uniform4f(ud[name].location, v.red, v.green, v.blue, v.alpha);\n            }\n        `\n  },\n  // uploading a pixi color as a vec3\n  {\n    type: \"vec3<f32>\",\n    test: (data) => data.type === \"vec3<f32>\" && data.size === 1 && data.value.red !== void 0,\n    ubo: `\n            v = uv[name];\n            data[offset] = v.red;\n            data[offset + 1] = v.green;\n            data[offset + 2] = v.blue;\n        `,\n    uniform: `\n            cv = ud[name].value;\n            v = uv[name];\n            if (cv[0] !== v.red || cv[1] !== v.green || cv[2] !== v.blue) {\n                cv[0] = v.red;\n                cv[1] = v.green;\n                cv[2] = v.blue;\n                gl.uniform3f(ud[name].location, v.red, v.green, v.blue);\n            }\n        `\n  }\n];\n\nexport { uniformParsers };\n//# sourceMappingURL=uniformParsers.mjs.map\n", "import { uniformParsers } from './uniformParsers.mjs';\n\n\"use strict\";\nfunction createUboSyncFunction(uboElements, parserCode, arrayGenerationFunction, singleSettersMap) {\n  const funcFragments = [`\n        var v = null;\n        var v2 = null;\n        var t = 0;\n        var index = 0;\n        var name = null;\n        var arrayOffset = null;\n    `];\n  let prev = 0;\n  for (let i = 0; i < uboElements.length; i++) {\n    const uboElement = uboElements[i];\n    const name = uboElement.data.name;\n    let parsed = false;\n    let offset = 0;\n    for (let j = 0; j < uniformParsers.length; j++) {\n      const uniformParser = uniformParsers[j];\n      if (uniformParser.test(uboElement.data)) {\n        offset = uboElement.offset / 4;\n        funcFragments.push(\n          `name = \"${name}\";`,\n          `offset += ${offset - prev};`,\n          uniformParsers[j][parserCode] || uniformParsers[j].ubo\n        );\n        parsed = true;\n        break;\n      }\n    }\n    if (!parsed) {\n      if (uboElement.data.size > 1) {\n        offset = uboElement.offset / 4;\n        funcFragments.push(arrayGenerationFunction(uboElement, offset - prev));\n      } else {\n        const template = singleSettersMap[uboElement.data.type];\n        offset = uboElement.offset / 4;\n        funcFragments.push(\n          /* wgsl */\n          `\n                    v = uv.${name};\n                    offset += ${offset - prev};\n                    ${template};\n                `\n        );\n      }\n    }\n    prev = offset;\n  }\n  const fragmentSrc = funcFragments.join(\"\\n\");\n  return new Function(\n    \"uv\",\n    \"data\",\n    \"dataInt32\",\n    \"offset\",\n    fragmentSrc\n  );\n}\n\nexport { createUboSyncFunction };\n//# sourceMappingURL=createUboSyncFunction.mjs.map\n", "\"use strict\";\nfunction loopMatrix(col, row) {\n  const total = col * row;\n  return `\n        for (let i = 0; i < ${total}; i++) {\n            data[offset + (((i / ${col})|0) * 4) + (i % ${col})] = v[i];\n        }\n    `;\n}\nconst uboSyncFunctionsSTD40 = {\n  f32: `\n        data[offset] = v;`,\n  i32: `\n        dataInt32[offset] = v;`,\n  \"vec2<f32>\": `\n        data[offset] = v[0];\n        data[offset + 1] = v[1];`,\n  \"vec3<f32>\": `\n        data[offset] = v[0];\n        data[offset + 1] = v[1];\n        data[offset + 2] = v[2];`,\n  \"vec4<f32>\": `\n        data[offset] = v[0];\n        data[offset + 1] = v[1];\n        data[offset + 2] = v[2];\n        data[offset + 3] = v[3];`,\n  \"vec2<i32>\": `\n        dataInt32[offset] = v[0];\n        dataInt32[offset + 1] = v[1];`,\n  \"vec3<i32>\": `\n        dataInt32[offset] = v[0];\n        dataInt32[offset + 1] = v[1];\n        dataInt32[offset + 2] = v[2];`,\n  \"vec4<i32>\": `\n        dataInt32[offset] = v[0];\n        dataInt32[offset + 1] = v[1];\n        dataInt32[offset + 2] = v[2];\n        dataInt32[offset + 3] = v[3];`,\n  \"mat2x2<f32>\": `\n        data[offset] = v[0];\n        data[offset + 1] = v[1];\n        data[offset + 4] = v[2];\n        data[offset + 5] = v[3];`,\n  \"mat3x3<f32>\": `\n        data[offset] = v[0];\n        data[offset + 1] = v[1];\n        data[offset + 2] = v[2];\n        data[offset + 4] = v[3];\n        data[offset + 5] = v[4];\n        data[offset + 6] = v[5];\n        data[offset + 8] = v[6];\n        data[offset + 9] = v[7];\n        data[offset + 10] = v[8];`,\n  \"mat4x4<f32>\": `\n        for (let i = 0; i < 16; i++) {\n            data[offset + i] = v[i];\n        }`,\n  \"mat3x2<f32>\": loopMatrix(3, 2),\n  \"mat4x2<f32>\": loopMatrix(4, 2),\n  \"mat2x3<f32>\": loopMatrix(2, 3),\n  \"mat4x3<f32>\": loopMatrix(4, 3),\n  \"mat2x4<f32>\": loopMatrix(2, 4),\n  \"mat3x4<f32>\": loopMatrix(3, 4)\n};\nconst uboSyncFunctionsWGSL = {\n  ...uboSyncFunctionsSTD40,\n  \"mat2x2<f32>\": `\n        data[offset] = v[0];\n        data[offset + 1] = v[1];\n        data[offset + 2] = v[2];\n        data[offset + 3] = v[3];\n    `\n};\n\nexport { uboSyncFunctionsSTD40, uboSyncFunctionsWGSL };\n//# sourceMappingURL=uboSyncFunctions.mjs.map\n", "\"use strict\";\nfunction calculateProjection(pm, x, y, width, height, flipY) {\n  const sign = flipY ? 1 : -1;\n  pm.identity();\n  pm.a = 1 / width * 2;\n  pm.d = sign * (1 / height * 2);\n  pm.tx = -1 - x * pm.a;\n  pm.ty = -sign - y * pm.d;\n  return pm;\n}\n\nexport { calculateProjection };\n//# sourceMappingURL=calculateProjection.mjs.map\n", "import { CanvasSource } from '../sources/CanvasSource.mjs';\nimport { Texture } from '../Texture.mjs';\n\n\"use strict\";\nconst canvasCache = /* @__PURE__ */ new Map();\nfunction getCanvasTexture(canvas, options) {\n  if (!canvasCache.has(canvas)) {\n    const texture = new Texture({\n      source: new CanvasSource({\n        resource: canvas,\n        ...options\n      })\n    });\n    const onDestroy = () => {\n      if (canvasCache.get(canvas) === texture) {\n        canvasCache.delete(canvas);\n      }\n    };\n    texture.once(\"destroy\", onDestroy);\n    texture.source.once(\"destroy\", onDestroy);\n    canvasCache.set(canvas, texture);\n  }\n  return canvasCache.get(canvas);\n}\nfunction hasCachedCanvasTexture(canvas) {\n  return canvasCache.has(canvas);\n}\n\nexport { getCanvasTexture, hasCachedCanvasTexture };\n//# sourceMappingURL=getCanvasTexture.mjs.map\n", "\"use strict\";\nfunction isRenderingToScreen(renderTarget) {\n  const resource = renderTarget.colorTexture.source.resource;\n  return globalThis.HTMLCanvasElement && resource instanceof HTMLCanvasElement && document.body.contains(resource);\n}\n\nexport { isRenderingToScreen };\n//# sourceMappingURL=isRenderingToScreen.mjs.map\n", "import { uid } from '../../../../utils/data/uid.mjs';\nimport { TextureSource } from '../texture/sources/TextureSource.mjs';\nimport { Texture } from '../texture/Texture.mjs';\n\n\"use strict\";\nconst _RenderTarget = class _RenderTarget {\n  /**\n   * @param [descriptor] - Options for creating a render target.\n   */\n  constructor(descriptor = {}) {\n    /** unique id for this render target */\n    this.uid = uid(\"renderTarget\");\n    /**\n     * An array of textures that can be written to by the GPU - mostly this has one texture in Pixi, but you could\n     * write to multiple if required! (eg deferred lighting)\n     */\n    this.colorTextures = [];\n    this.dirtyId = 0;\n    this.isRoot = false;\n    this._size = new Float32Array(2);\n    /** if true, then when the render target is destroyed, it will destroy all the textures that were created for it. */\n    this._managedColorTextures = false;\n    descriptor = { ..._RenderTarget.defaultOptions, ...descriptor };\n    this.stencil = descriptor.stencil;\n    this.depth = descriptor.depth;\n    this.isRoot = descriptor.isRoot;\n    if (typeof descriptor.colorTextures === \"number\") {\n      this._managedColorTextures = true;\n      for (let i = 0; i < descriptor.colorTextures; i++) {\n        this.colorTextures.push(\n          new TextureSource({\n            width: descriptor.width,\n            height: descriptor.height,\n            resolution: descriptor.resolution,\n            antialias: descriptor.antialias\n          })\n        );\n      }\n    } else {\n      this.colorTextures = [...descriptor.colorTextures.map((texture) => texture.source)];\n      const colorSource = this.colorTexture.source;\n      this.resize(colorSource.width, colorSource.height, colorSource._resolution);\n    }\n    this.colorTexture.source.on(\"resize\", this.onSourceResize, this);\n    if (descriptor.depthStencilTexture || this.stencil) {\n      if (descriptor.depthStencilTexture instanceof Texture || descriptor.depthStencilTexture instanceof TextureSource) {\n        this.depthStencilTexture = descriptor.depthStencilTexture.source;\n      } else {\n        this.ensureDepthStencilTexture();\n      }\n    }\n  }\n  get size() {\n    const _size = this._size;\n    _size[0] = this.pixelWidth;\n    _size[1] = this.pixelHeight;\n    return _size;\n  }\n  get width() {\n    return this.colorTexture.source.width;\n  }\n  get height() {\n    return this.colorTexture.source.height;\n  }\n  get pixelWidth() {\n    return this.colorTexture.source.pixelWidth;\n  }\n  get pixelHeight() {\n    return this.colorTexture.source.pixelHeight;\n  }\n  get resolution() {\n    return this.colorTexture.source._resolution;\n  }\n  get colorTexture() {\n    return this.colorTextures[0];\n  }\n  onSourceResize(source) {\n    this.resize(source.width, source.height, source._resolution, true);\n  }\n  /**\n   * This will ensure a depthStencil texture is created for this render target.\n   * Most likely called by the mask system to make sure we have stencil buffer added.\n   * @internal\n   * @ignore\n   */\n  ensureDepthStencilTexture() {\n    if (!this.depthStencilTexture) {\n      this.depthStencilTexture = new TextureSource({\n        width: this.width,\n        height: this.height,\n        resolution: this.resolution,\n        format: \"depth24plus-stencil8\",\n        autoGenerateMipmaps: false,\n        antialias: false,\n        mipLevelCount: 1\n        // sampleCount: handled by the render target system..\n      });\n    }\n  }\n  resize(width, height, resolution = this.resolution, skipColorTexture = false) {\n    this.dirtyId++;\n    this.colorTextures.forEach((colorTexture, i) => {\n      if (skipColorTexture && i === 0)\n        return;\n      colorTexture.source.resize(width, height, resolution);\n    });\n    if (this.depthStencilTexture) {\n      this.depthStencilTexture.source.resize(width, height, resolution);\n    }\n  }\n  destroy() {\n    this.colorTexture.source.off(\"resize\", this.onSourceResize, this);\n    if (this._managedColorTextures) {\n      this.colorTextures.forEach((texture) => {\n        texture.destroy();\n      });\n    }\n    if (this.depthStencilTexture) {\n      this.depthStencilTexture.destroy();\n      delete this.depthStencilTexture;\n    }\n  }\n};\n/** The default options for a render target */\n_RenderTarget.defaultOptions = {\n  /** the width of the RenderTarget */\n  width: 0,\n  /** the height of the RenderTarget */\n  height: 0,\n  /** the resolution of the RenderTarget */\n  resolution: 1,\n  /** an array of textures, or a number indicating how many color textures there should be */\n  colorTextures: 1,\n  /** should this render target have a stencil buffer? */\n  stencil: false,\n  /** should this render target have a depth buffer? */\n  depth: false,\n  /** should this render target be antialiased? */\n  antialias: false,\n  // save on perf by default!\n  /** is this a root element, true if this is gl context owners render target */\n  isRoot: false\n};\nlet RenderTarget = _RenderTarget;\n\nexport { RenderTarget };\n//# sourceMappingURL=RenderTarget.mjs.map\n", "import { Matrix } from '../../../../maths/matrix/Matrix.mjs';\nimport { Rectangle } from '../../../../maths/shapes/Rectangle.mjs';\nimport { CLEAR } from '../../gl/const.mjs';\nimport { calculateProjection } from '../../gpu/renderTarget/calculateProjection.mjs';\nimport { SystemRunner } from '../system/SystemRunner.mjs';\nimport { CanvasSource } from '../texture/sources/CanvasSource.mjs';\nimport { TextureSource } from '../texture/sources/TextureSource.mjs';\nimport { Texture } from '../texture/Texture.mjs';\nimport { getCanvasTexture } from '../texture/utils/getCanvasTexture.mjs';\nimport { isRenderingToScreen } from './isRenderingToScreen.mjs';\nimport { RenderTarget } from './RenderTarget.mjs';\n\n\"use strict\";\nclass RenderTargetSystem {\n  constructor(renderer) {\n    /** This is the root viewport for the render pass*/\n    this.rootViewPort = new Rectangle();\n    /** the current viewport that the gpu is using */\n    this.viewport = new Rectangle();\n    /**\n     * a runner that lets systems know if the active render target has changed.\n     * Eg the Stencil System needs to know so it can manage the stencil buffer\n     */\n    this.onRenderTargetChange = new SystemRunner(\"onRenderTargetChange\");\n    /** the projection matrix that is used by the shaders based on the active render target and the viewport */\n    this.projectionMatrix = new Matrix();\n    /** the default clear color for render targets */\n    this.defaultClearColor = [0, 0, 0, 0];\n    /**\n     * a hash that stores the render target for a given render surface. When you pass in a texture source,\n     * a render target is created for it. This map stores and makes it easy to retrieve the render target\n     */\n    this._renderSurfaceToRenderTargetHash = /* @__PURE__ */ new Map();\n    /** A hash that stores a gpu render target for a given render target. */\n    this._gpuRenderTargetHash = /* @__PURE__ */ Object.create(null);\n    /**\n     * A stack that stores the render target and frame that is currently being rendered to.\n     * When push is called, the current render target is stored in this stack.\n     * When pop is called, the previous render target is restored.\n     */\n    this._renderTargetStack = [];\n    this._renderer = renderer;\n    renderer.renderableGC.addManagedHash(this, \"_gpuRenderTargetHash\");\n  }\n  /** called when dev wants to finish a render pass */\n  finishRenderPass() {\n    this.adaptor.finishRenderPass(this.renderTarget);\n  }\n  /**\n   * called when the renderer starts to render a scene.\n   * @param options\n   * @param options.target - the render target to render to\n   * @param options.clear - the clear mode to use. Can be true or a CLEAR number 'COLOR | DEPTH | STENCIL' 0b111\n   * @param options.clearColor - the color to clear to\n   * @param options.frame - the frame to render to\n   */\n  renderStart({\n    target,\n    clear,\n    clearColor,\n    frame\n  }) {\n    this._renderTargetStack.length = 0;\n    this.push(\n      target,\n      clear,\n      clearColor,\n      frame\n    );\n    this.rootViewPort.copyFrom(this.viewport);\n    this.rootRenderTarget = this.renderTarget;\n    this.renderingToScreen = isRenderingToScreen(this.rootRenderTarget);\n    this.adaptor.prerender?.(this.rootRenderTarget);\n  }\n  postrender() {\n    this.adaptor.postrender?.(this.rootRenderTarget);\n  }\n  /**\n   * Binding a render surface! This is the main function of the render target system.\n   * It will take the RenderSurface (which can be a texture, canvas, or render target) and bind it to the renderer.\n   * Once bound all draw calls will be rendered to the render surface.\n   *\n   * If a frame is not provide and the render surface is a texture, the frame of the texture will be used.\n   * @param renderSurface - the render surface to bind\n   * @param clear - the clear mode to use. Can be true or a CLEAR number 'COLOR | DEPTH | STENCIL' 0b111\n   * @param clearColor - the color to clear to\n   * @param frame - the frame to render to\n   * @returns the render target that was bound\n   */\n  bind(renderSurface, clear = true, clearColor, frame) {\n    const renderTarget = this.getRenderTarget(renderSurface);\n    const didChange = this.renderTarget !== renderTarget;\n    this.renderTarget = renderTarget;\n    this.renderSurface = renderSurface;\n    const gpuRenderTarget = this.getGpuRenderTarget(renderTarget);\n    if (renderTarget.pixelWidth !== gpuRenderTarget.width || renderTarget.pixelHeight !== gpuRenderTarget.height) {\n      this.adaptor.resizeGpuRenderTarget(renderTarget);\n      gpuRenderTarget.width = renderTarget.pixelWidth;\n      gpuRenderTarget.height = renderTarget.pixelHeight;\n    }\n    const source = renderTarget.colorTexture;\n    const viewport = this.viewport;\n    const pixelWidth = source.pixelWidth;\n    const pixelHeight = source.pixelHeight;\n    if (!frame && renderSurface instanceof Texture) {\n      frame = renderSurface.frame;\n    }\n    if (frame) {\n      const resolution = source._resolution;\n      viewport.x = frame.x * resolution + 0.5 | 0;\n      viewport.y = frame.y * resolution + 0.5 | 0;\n      viewport.width = frame.width * resolution + 0.5 | 0;\n      viewport.height = frame.height * resolution + 0.5 | 0;\n    } else {\n      viewport.x = 0;\n      viewport.y = 0;\n      viewport.width = pixelWidth;\n      viewport.height = pixelHeight;\n    }\n    calculateProjection(\n      this.projectionMatrix,\n      0,\n      0,\n      viewport.width / source.resolution,\n      viewport.height / source.resolution,\n      !renderTarget.isRoot\n    );\n    this.adaptor.startRenderPass(renderTarget, clear, clearColor, viewport);\n    if (didChange) {\n      this.onRenderTargetChange.emit(renderTarget);\n    }\n    return renderTarget;\n  }\n  clear(target, clear = CLEAR.ALL, clearColor) {\n    if (!clear)\n      return;\n    if (target) {\n      target = this.getRenderTarget(target);\n    }\n    this.adaptor.clear(\n      target || this.renderTarget,\n      clear,\n      clearColor,\n      this.viewport\n    );\n  }\n  contextChange() {\n    this._gpuRenderTargetHash = /* @__PURE__ */ Object.create(null);\n  }\n  /**\n   * Push a render surface to the renderer. This will bind the render surface to the renderer,\n   * @param renderSurface - the render surface to push\n   * @param clear - the clear mode to use. Can be true or a CLEAR number 'COLOR | DEPTH | STENCIL' 0b111\n   * @param clearColor - the color to clear to\n   * @param frame - the frame to use when rendering to the render surface\n   */\n  push(renderSurface, clear = CLEAR.ALL, clearColor, frame) {\n    const renderTarget = this.bind(renderSurface, clear, clearColor, frame);\n    this._renderTargetStack.push({\n      renderTarget,\n      frame\n    });\n    return renderTarget;\n  }\n  /** Pops the current render target from the renderer and restores the previous render target. */\n  pop() {\n    this._renderTargetStack.pop();\n    const currentRenderTargetData = this._renderTargetStack[this._renderTargetStack.length - 1];\n    this.bind(currentRenderTargetData.renderTarget, false, null, currentRenderTargetData.frame);\n  }\n  /**\n   * Gets the render target from the provide render surface. Eg if its a texture,\n   * it will return the render target for the texture.\n   * If its a render target, it will return the same render target.\n   * @param renderSurface - the render surface to get the render target for\n   * @returns the render target for the render surface\n   */\n  getRenderTarget(renderSurface) {\n    if (renderSurface.isTexture) {\n      renderSurface = renderSurface.source;\n    }\n    return this._renderSurfaceToRenderTargetHash.get(renderSurface) ?? this._initRenderTarget(renderSurface);\n  }\n  /**\n   * Copies a render surface to another texture\n   * @param sourceRenderSurfaceTexture - the render surface to copy from\n   * @param destinationTexture - the texture to copy to\n   * @param originSrc - the origin of the copy\n   * @param originSrc.x - the x origin of the copy\n   * @param originSrc.y - the y origin of the copy\n   * @param size - the size of the copy\n   * @param size.width - the width of the copy\n   * @param size.height - the height of the copy\n   * @param originDest - the destination origin (top left to paste from!)\n   * @param originDest.x - the x origin of the paste\n   * @param originDest.y - the y origin of the paste\n   */\n  copyToTexture(sourceRenderSurfaceTexture, destinationTexture, originSrc, size, originDest) {\n    if (originSrc.x < 0) {\n      size.width += originSrc.x;\n      originDest.x -= originSrc.x;\n      originSrc.x = 0;\n    }\n    if (originSrc.y < 0) {\n      size.height += originSrc.y;\n      originDest.y -= originSrc.y;\n      originSrc.y = 0;\n    }\n    const { pixelWidth, pixelHeight } = sourceRenderSurfaceTexture;\n    size.width = Math.min(size.width, pixelWidth - originSrc.x);\n    size.height = Math.min(size.height, pixelHeight - originSrc.y);\n    return this.adaptor.copyToTexture(\n      sourceRenderSurfaceTexture,\n      destinationTexture,\n      originSrc,\n      size,\n      originDest\n    );\n  }\n  /**\n   * ensures that we have a depth stencil buffer available to render to\n   * This is used by the mask system to make sure we have a stencil buffer.\n   */\n  ensureDepthStencil() {\n    if (!this.renderTarget.stencil) {\n      this.renderTarget.stencil = true;\n      this.adaptor.startRenderPass(this.renderTarget, false, null, this.viewport);\n    }\n  }\n  /** nukes the render target system */\n  destroy() {\n    this._renderer = null;\n    this._renderSurfaceToRenderTargetHash.forEach((renderTarget, key) => {\n      if (renderTarget !== key) {\n        renderTarget.destroy();\n      }\n    });\n    this._renderSurfaceToRenderTargetHash.clear();\n    this._gpuRenderTargetHash = /* @__PURE__ */ Object.create(null);\n  }\n  _initRenderTarget(renderSurface) {\n    let renderTarget = null;\n    if (CanvasSource.test(renderSurface)) {\n      renderSurface = getCanvasTexture(renderSurface).source;\n    }\n    if (renderSurface instanceof RenderTarget) {\n      renderTarget = renderSurface;\n    } else if (renderSurface instanceof TextureSource) {\n      renderTarget = new RenderTarget({\n        colorTextures: [renderSurface]\n      });\n      if (CanvasSource.test(renderSurface.source.resource)) {\n        renderTarget.isRoot = true;\n      }\n      renderSurface.once(\"destroy\", () => {\n        renderTarget.destroy();\n        this._renderSurfaceToRenderTargetHash.delete(renderSurface);\n        const gpuRenderTarget = this._gpuRenderTargetHash[renderTarget.uid];\n        if (gpuRenderTarget) {\n          this._gpuRenderTargetHash[renderTarget.uid] = null;\n          this.adaptor.destroyGpuRenderTarget(gpuRenderTarget);\n        }\n      });\n    }\n    this._renderSurfaceToRenderTargetHash.set(renderSurface, renderTarget);\n    return renderTarget;\n  }\n  getGpuRenderTarget(renderTarget) {\n    return this._gpuRenderTargetHash[renderTarget.uid] || (this._gpuRenderTargetHash[renderTarget.uid] = this.adaptor.initGpuRenderTarget(renderTarget));\n  }\n  resetState() {\n    this.renderTarget = null;\n    this.renderSurface = null;\n  }\n}\n\nexport { RenderTargetSystem };\n//# sourceMappingURL=RenderTargetSystem.mjs.map\n", "import EventEmitter from 'eventemitter3';\nimport { uid } from '../../../../utils/data/uid.mjs';\n\n\"use strict\";\nclass BufferResource extends EventEmitter {\n  /**\n   * Create a new Buffer Resource.\n   * @param options - The options for the buffer resource\n   * @param options.buffer - The underlying buffer that this resource is using\n   * @param options.offset - The offset of the buffer this resource is using.\n   * If not provided, then it will use the offset of the buffer.\n   * @param options.size - The size of the buffer this resource is using.\n   * If not provided, then it will use the size of the buffer.\n   */\n  constructor({ buffer, offset, size }) {\n    super();\n    /**\n     * emits when the underlying buffer has changed shape (i.e. resized)\n     * letting the renderer know that it needs to discard the old buffer on the GPU and create a new one\n     * @event change\n     */\n    /** a unique id for this uniform group used through the renderer */\n    this.uid = uid(\"buffer\");\n    /**\n     * a resource type, used to identify how to handle it when its in a bind group / shader resource\n     * @internal\n     * @ignore\n     */\n    this._resourceType = \"bufferResource\";\n    /**\n     * used internally to know if a uniform group was used in the last render pass\n     * @internal\n     * @ignore\n     */\n    this._touched = 0;\n    /**\n     * the resource id used internally by the renderer to build bind group keys\n     * @internal\n     * @ignore\n     */\n    this._resourceId = uid(\"resource\");\n    /**\n     * A cheeky hint to the GL renderer to let it know this is a BufferResource\n     * @internal\n     * @ignore\n     */\n    this._bufferResource = true;\n    /**\n     * Has the Buffer resource been destroyed?\n     * @readonly\n     */\n    this.destroyed = false;\n    this.buffer = buffer;\n    this.offset = offset | 0;\n    this.size = size;\n    this.buffer.on(\"change\", this.onBufferChange, this);\n  }\n  onBufferChange() {\n    this._resourceId = uid(\"resource\");\n    this.emit(\"change\", this);\n  }\n  /**\n   * Destroys this resource. Make sure the underlying buffer is not used anywhere else\n   * if you want to destroy it as well, or code will explode\n   * @param destroyBuffer - Should the underlying buffer be destroyed as well?\n   */\n  destroy(destroyBuffer = false) {\n    this.destroyed = true;\n    if (destroyBuffer) {\n      this.buffer.destroy();\n    }\n    this.emit(\"change\", this);\n    this.buffer = null;\n  }\n}\n\nexport { BufferResource };\n//# sourceMappingURL=BufferResource.mjs.map\n", "import { ExtensionType } from '../../extensions/Extensions.mjs';\n\n\"use strict\";\nclass CustomRenderPipe {\n  constructor(renderer) {\n    this._renderer = renderer;\n  }\n  updateRenderable() {\n  }\n  destroyRenderable() {\n  }\n  validateRenderable() {\n    return false;\n  }\n  addRenderable(container, instructionSet) {\n    this._renderer.renderPipes.batch.break(instructionSet);\n    instructionSet.add(container);\n  }\n  execute(container) {\n    if (!container.isRenderable)\n      return;\n    container.render(this._renderer);\n  }\n  destroy() {\n    this._renderer = null;\n  }\n}\nCustomRenderPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"customRender\"\n};\n\nexport { CustomRenderPipe };\n//# sourceMappingURL=CustomRenderPipe.mjs.map\n", "\"use strict\";\nfunction executeInstructions(renderGroup, renderer) {\n  const instructionSet = renderGroup.instructionSet;\n  const instructions = instructionSet.instructions;\n  for (let i = 0; i < instructionSet.instructionSize; i++) {\n    const instruction = instructions[i];\n    renderer[instruction.renderPipeId].execute(instruction);\n  }\n}\n\nexport { executeInstructions };\n//# sourceMappingURL=executeInstructions.mjs.map\n", "import { ExtensionType } from '../../extensions/Extensions.mjs';\nimport { Matrix } from '../../maths/matrix/Matrix.mjs';\nimport { BigPool } from '../../utils/pool/PoolGroup.mjs';\nimport { BatchableSprite } from '../sprite/BatchableSprite.mjs';\nimport { executeInstructions } from './utils/executeInstructions.mjs';\n\n\"use strict\";\nconst tempMatrix = new Matrix();\nclass RenderGroupPipe {\n  constructor(renderer) {\n    this._renderer = renderer;\n  }\n  addRenderGroup(renderGroup, instructionSet) {\n    if (renderGroup.isCachedAsTexture) {\n      this._addRenderableCacheAsTexture(renderGroup, instructionSet);\n    } else {\n      this._addRenderableDirect(renderGroup, instructionSet);\n    }\n  }\n  execute(renderGroup) {\n    if (!renderGroup.isRenderable)\n      return;\n    if (renderGroup.isCachedAsTexture) {\n      this._executeCacheAsTexture(renderGroup);\n    } else {\n      this._executeDirect(renderGroup);\n    }\n  }\n  destroy() {\n    this._renderer = null;\n  }\n  _addRenderableDirect(renderGroup, instructionSet) {\n    this._renderer.renderPipes.batch.break(instructionSet);\n    if (renderGroup._batchableRenderGroup) {\n      BigPool.return(renderGroup._batchableRenderGroup);\n      renderGroup._batchableRenderGroup = null;\n    }\n    instructionSet.add(renderGroup);\n  }\n  _addRenderableCacheAsTexture(renderGroup, instructionSet) {\n    const batchableRenderGroup = renderGroup._batchableRenderGroup ?? (renderGroup._batchableRenderGroup = BigPool.get(BatchableSprite));\n    batchableRenderGroup.renderable = renderGroup.root;\n    batchableRenderGroup.transform = renderGroup.root.relativeGroupTransform;\n    batchableRenderGroup.texture = renderGroup.texture;\n    batchableRenderGroup.bounds = renderGroup._textureBounds;\n    instructionSet.add(renderGroup);\n    this._renderer.renderPipes.batch.addToBatch(batchableRenderGroup, instructionSet);\n  }\n  _executeCacheAsTexture(renderGroup) {\n    if (renderGroup.textureNeedsUpdate) {\n      renderGroup.textureNeedsUpdate = false;\n      const worldTransformMatrix = tempMatrix.identity().translate(\n        -renderGroup._textureBounds.x,\n        -renderGroup._textureBounds.y\n      );\n      this._renderer.renderTarget.push(renderGroup.texture, true, null, renderGroup.texture.frame);\n      this._renderer.globalUniforms.push({\n        worldTransformMatrix,\n        worldColor: 4294967295\n      });\n      executeInstructions(renderGroup, this._renderer.renderPipes);\n      this._renderer.renderTarget.finishRenderPass();\n      this._renderer.renderTarget.pop();\n      this._renderer.globalUniforms.pop();\n    }\n    renderGroup._batchableRenderGroup._batcher.updateElement(renderGroup._batchableRenderGroup);\n    renderGroup._batchableRenderGroup._batcher.geometry.buffers[0].update();\n  }\n  _executeDirect(renderGroup) {\n    this._renderer.globalUniforms.push({\n      worldTransformMatrix: renderGroup.inverseParentTextureTransform,\n      worldColor: renderGroup.worldColorAlpha\n    });\n    executeInstructions(renderGroup, this._renderer.renderPipes);\n    this._renderer.globalUniforms.pop();\n  }\n}\nRenderGroupPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"renderGroup\"\n};\n\nexport { RenderGroupPipe };\n//# sourceMappingURL=RenderGroupPipe.mjs.map\n", "\"use strict\";\nfunction clearList(list, index) {\n  index || (index = 0);\n  for (let j = index; j < list.length; j++) {\n    if (list[j]) {\n      list[j] = null;\n    } else {\n      break;\n    }\n  }\n}\n\nexport { clearList };\n//# sourceMappingURL=clearList.mjs.map\n", "import { Container, UPDATE_VISIBLE, UPDATE_COLOR, UPDATE_BLEND } from '../Container.mjs';\nimport { clearList } from './clearList.mjs';\nimport { multiplyColors } from './multiplyColors.mjs';\n\n\"use strict\";\nconst tempContainer = new Container();\nconst UPDATE_BLEND_COLOR_VISIBLE = UPDATE_VISIBLE | UPDATE_COLOR | UPDATE_BLEND;\nfunction updateRenderGroupTransforms(renderGroup, updateChildRenderGroups = false) {\n  updateRenderGroupTransform(renderGroup);\n  const childrenToUpdate = renderGroup.childrenToUpdate;\n  const updateTick = renderGroup.updateTick++;\n  for (const j in childrenToUpdate) {\n    const renderGroupDepth = Number(j);\n    const childrenAtDepth = childrenToUpdate[j];\n    const list = childrenAtDepth.list;\n    const index = childrenAtDepth.index;\n    for (let i = 0; i < index; i++) {\n      const child = list[i];\n      if (child.parentRenderGroup === renderGroup && child.relativeRenderGroupDepth === renderGroupDepth) {\n        updateTransformAndChildren(child, updateTick, 0);\n      }\n    }\n    clearList(list, index);\n    childrenAtDepth.index = 0;\n  }\n  if (updateChildRenderGroups) {\n    for (let i = 0; i < renderGroup.renderGroupChildren.length; i++) {\n      updateRenderGroupTransforms(renderGroup.renderGroupChildren[i], updateChildRenderGroups);\n    }\n  }\n}\nfunction updateRenderGroupTransform(renderGroup) {\n  const root = renderGroup.root;\n  let worldAlpha;\n  if (renderGroup.renderGroupParent) {\n    const renderGroupParent = renderGroup.renderGroupParent;\n    renderGroup.worldTransform.appendFrom(\n      root.relativeGroupTransform,\n      renderGroupParent.worldTransform\n    );\n    renderGroup.worldColor = multiplyColors(\n      root.groupColor,\n      renderGroupParent.worldColor\n    );\n    worldAlpha = root.groupAlpha * renderGroupParent.worldAlpha;\n  } else {\n    renderGroup.worldTransform.copyFrom(root.localTransform);\n    renderGroup.worldColor = root.localColor;\n    worldAlpha = root.localAlpha;\n  }\n  worldAlpha = worldAlpha < 0 ? 0 : worldAlpha > 1 ? 1 : worldAlpha;\n  renderGroup.worldAlpha = worldAlpha;\n  renderGroup.worldColorAlpha = renderGroup.worldColor + ((worldAlpha * 255 | 0) << 24);\n}\nfunction updateTransformAndChildren(container, updateTick, updateFlags) {\n  if (updateTick === container.updateTick)\n    return;\n  container.updateTick = updateTick;\n  container.didChange = false;\n  const localTransform = container.localTransform;\n  container.updateLocalTransform();\n  const parent = container.parent;\n  if (parent && !parent.renderGroup) {\n    updateFlags |= container._updateFlags;\n    container.relativeGroupTransform.appendFrom(\n      localTransform,\n      parent.relativeGroupTransform\n    );\n    if (updateFlags & UPDATE_BLEND_COLOR_VISIBLE) {\n      updateColorBlendVisibility(container, parent, updateFlags);\n    }\n  } else {\n    updateFlags = container._updateFlags;\n    container.relativeGroupTransform.copyFrom(localTransform);\n    if (updateFlags & UPDATE_BLEND_COLOR_VISIBLE) {\n      updateColorBlendVisibility(container, tempContainer, updateFlags);\n    }\n  }\n  if (!container.renderGroup) {\n    const children = container.children;\n    const length = children.length;\n    for (let i = 0; i < length; i++) {\n      updateTransformAndChildren(children[i], updateTick, updateFlags);\n    }\n    const renderGroup = container.parentRenderGroup;\n    const renderable = container;\n    if (renderable.renderPipeId && !renderGroup.structureDidChange) {\n      renderGroup.updateRenderable(renderable);\n    }\n  }\n}\nfunction updateColorBlendVisibility(container, parent, updateFlags) {\n  if (updateFlags & UPDATE_COLOR) {\n    container.groupColor = multiplyColors(\n      container.localColor,\n      parent.groupColor\n    );\n    let groupAlpha = container.localAlpha * parent.groupAlpha;\n    groupAlpha = groupAlpha < 0 ? 0 : groupAlpha > 1 ? 1 : groupAlpha;\n    container.groupAlpha = groupAlpha;\n    container.groupColorAlpha = container.groupColor + ((groupAlpha * 255 | 0) << 24);\n  }\n  if (updateFlags & UPDATE_BLEND) {\n    container.groupBlendMode = container.localBlendMode === \"inherit\" ? parent.groupBlendMode : container.localBlendMode;\n  }\n  if (updateFlags & UPDATE_VISIBLE) {\n    container.globalDisplayStatus = container.localDisplayStatus & parent.globalDisplayStatus;\n  }\n  container._updateFlags = 0;\n}\n\nexport { updateRenderGroupTransform, updateRenderGroupTransforms, updateTransformAndChildren };\n//# sourceMappingURL=updateRenderGroupTransforms.mjs.map\n", "\"use strict\";\nfunction validateRenderables(renderGroup, renderPipes) {\n  const { list, index } = renderGroup.childrenRenderablesToUpdate;\n  let rebuildRequired = false;\n  for (let i = 0; i < index; i++) {\n    const container = list[i];\n    const renderable = container;\n    const pipe = renderPipes[renderable.renderPipeId];\n    rebuildRequired = pipe.validateRenderable(container);\n    if (rebuildRequired) {\n      break;\n    }\n  }\n  renderGroup.structureDidChange = rebuildRequired;\n  return rebuildRequired;\n}\n\nexport { validateRenderables };\n//# sourceMappingURL=validateRenderables.mjs.map\n", "import { ExtensionType } from '../../extensions/Extensions.mjs';\nimport { Matrix } from '../../maths/matrix/Matrix.mjs';\nimport { TexturePool } from '../../rendering/renderers/shared/texture/TexturePool.mjs';\nimport { Bounds } from './bounds/Bounds.mjs';\nimport { clearList } from './utils/clearList.mjs';\nimport { executeInstructions } from './utils/executeInstructions.mjs';\nimport { updateRenderGroupTransforms } from './utils/updateRenderGroupTransforms.mjs';\nimport { validateRenderables } from './utils/validateRenderables.mjs';\n\n\"use strict\";\nconst tempMatrix = new Matrix();\nclass RenderGroupSystem {\n  constructor(renderer) {\n    this._renderer = renderer;\n  }\n  render({ container, transform }) {\n    const parent = container.parent;\n    const renderGroupParent = container.renderGroup.renderGroupParent;\n    container.parent = null;\n    container.renderGroup.renderGroupParent = null;\n    const renderer = this._renderer;\n    let originalLocalTransform = tempMatrix;\n    if (transform) {\n      originalLocalTransform = originalLocalTransform.copyFrom(container.renderGroup.localTransform);\n      container.renderGroup.localTransform.copyFrom(transform);\n    }\n    const renderPipes = renderer.renderPipes;\n    this._updateCachedRenderGroups(container.renderGroup, null);\n    this._updateRenderGroups(container.renderGroup);\n    renderer.globalUniforms.start({\n      worldTransformMatrix: transform ? container.renderGroup.localTransform : container.renderGroup.worldTransform,\n      worldColor: container.renderGroup.worldColorAlpha\n    });\n    executeInstructions(container.renderGroup, renderPipes);\n    if (renderPipes.uniformBatch) {\n      renderPipes.uniformBatch.renderEnd();\n    }\n    if (transform) {\n      container.renderGroup.localTransform.copyFrom(originalLocalTransform);\n    }\n    container.parent = parent;\n    container.renderGroup.renderGroupParent = renderGroupParent;\n  }\n  destroy() {\n    this._renderer = null;\n  }\n  _updateCachedRenderGroups(renderGroup, closestCacheAsTexture) {\n    if (renderGroup.isCachedAsTexture) {\n      if (!renderGroup.updateCacheTexture)\n        return;\n      closestCacheAsTexture = renderGroup;\n    }\n    renderGroup._parentCacheAsTextureRenderGroup = closestCacheAsTexture;\n    for (let i = renderGroup.renderGroupChildren.length - 1; i >= 0; i--) {\n      this._updateCachedRenderGroups(renderGroup.renderGroupChildren[i], closestCacheAsTexture);\n    }\n    renderGroup.invalidateMatrices();\n    if (renderGroup.isCachedAsTexture) {\n      if (renderGroup.textureNeedsUpdate) {\n        const bounds = renderGroup.root.getLocalBounds();\n        bounds.ceil();\n        const lastTexture = renderGroup.texture;\n        if (renderGroup.texture) {\n          TexturePool.returnTexture(renderGroup.texture);\n        }\n        const renderer = this._renderer;\n        const resolution = renderGroup.textureOptions.resolution || renderer.view.resolution;\n        const antialias = renderGroup.textureOptions.antialias ?? renderer.view.antialias;\n        renderGroup.texture = TexturePool.getOptimalTexture(\n          bounds.width,\n          bounds.height,\n          resolution,\n          antialias\n        );\n        renderGroup._textureBounds || (renderGroup._textureBounds = new Bounds());\n        renderGroup._textureBounds.copyFrom(bounds);\n        if (lastTexture !== renderGroup.texture) {\n          if (renderGroup.renderGroupParent) {\n            renderGroup.renderGroupParent.structureDidChange = true;\n          }\n        }\n      }\n    } else if (renderGroup.texture) {\n      TexturePool.returnTexture(renderGroup.texture);\n      renderGroup.texture = null;\n    }\n  }\n  _updateRenderGroups(renderGroup) {\n    const renderer = this._renderer;\n    const renderPipes = renderer.renderPipes;\n    renderGroup.runOnRender(renderer);\n    renderGroup.instructionSet.renderPipes = renderPipes;\n    if (!renderGroup.structureDidChange) {\n      validateRenderables(renderGroup, renderPipes);\n    } else {\n      clearList(renderGroup.childrenRenderablesToUpdate.list, 0);\n    }\n    updateRenderGroupTransforms(renderGroup);\n    if (renderGroup.structureDidChange) {\n      renderGroup.structureDidChange = false;\n      this._buildInstructions(renderGroup, renderer);\n    } else {\n      this._updateRenderables(renderGroup);\n    }\n    renderGroup.childrenRenderablesToUpdate.index = 0;\n    renderer.renderPipes.batch.upload(renderGroup.instructionSet);\n    if (renderGroup.isCachedAsTexture && !renderGroup.textureNeedsUpdate)\n      return;\n    for (let i = 0; i < renderGroup.renderGroupChildren.length; i++) {\n      this._updateRenderGroups(renderGroup.renderGroupChildren[i]);\n    }\n  }\n  _updateRenderables(renderGroup) {\n    const { list, index } = renderGroup.childrenRenderablesToUpdate;\n    for (let i = 0; i < index; i++) {\n      const container = list[i];\n      if (container.didViewUpdate) {\n        renderGroup.updateRenderable(container);\n      }\n    }\n    clearList(list, index);\n  }\n  _buildInstructions(renderGroup, rendererOrPipes) {\n    const root = renderGroup.root;\n    const instructionSet = renderGroup.instructionSet;\n    instructionSet.reset();\n    const renderer = rendererOrPipes.renderPipes ? rendererOrPipes : rendererOrPipes.batch.renderer;\n    const renderPipes = renderer.renderPipes;\n    renderPipes.batch.buildStart(instructionSet);\n    renderPipes.blendMode.buildStart();\n    renderPipes.colorMask.buildStart();\n    if (root.sortableChildren) {\n      root.sortChildren();\n    }\n    root.collectRenderablesWithEffects(instructionSet, renderer, null);\n    renderPipes.batch.buildEnd(instructionSet);\n    renderPipes.blendMode.buildEnd(instructionSet);\n  }\n}\n/** @ignore */\nRenderGroupSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem,\n    ExtensionType.CanvasSystem\n  ],\n  name: \"renderGroup\"\n};\n\nexport { RenderGroupSystem };\n//# sourceMappingURL=RenderGroupSystem.mjs.map\n", "import { ExtensionType } from '../../extensions/Extensions.mjs';\nimport { BigPool } from '../../utils/pool/PoolGroup.mjs';\nimport { BatchableSprite } from './BatchableSprite.mjs';\n\n\"use strict\";\nclass SpritePipe {\n  constructor(renderer) {\n    this._gpuSpriteHash = /* @__PURE__ */ Object.create(null);\n    this._destroyRenderableBound = this.destroyRenderable.bind(this);\n    this._renderer = renderer;\n    this._renderer.renderableGC.addManagedHash(this, \"_gpuSpriteHash\");\n  }\n  addRenderable(sprite, instructionSet) {\n    const gpuSprite = this._getGpuSprite(sprite);\n    if (sprite.didViewUpdate)\n      this._updateBatchableSprite(sprite, gpuSprite);\n    this._renderer.renderPipes.batch.addToBatch(gpuSprite, instructionSet);\n  }\n  updateRenderable(sprite) {\n    const gpuSprite = this._gpuSpriteHash[sprite.uid];\n    if (sprite.didViewUpdate)\n      this._updateBatchableSprite(sprite, gpuSprite);\n    gpuSprite._batcher.updateElement(gpuSprite);\n  }\n  validateRenderable(sprite) {\n    const gpuSprite = this._getGpuSprite(sprite);\n    return !gpuSprite._batcher.checkAndUpdateTexture(\n      gpuSprite,\n      sprite._texture\n    );\n  }\n  destroyRenderable(sprite) {\n    const batchableSprite = this._gpuSpriteHash[sprite.uid];\n    BigPool.return(batchableSprite);\n    this._gpuSpriteHash[sprite.uid] = null;\n    sprite.off(\"destroyed\", this._destroyRenderableBound);\n  }\n  _updateBatchableSprite(sprite, batchableSprite) {\n    batchableSprite.bounds = sprite.visualBounds;\n    batchableSprite.texture = sprite._texture;\n  }\n  _getGpuSprite(sprite) {\n    return this._gpuSpriteHash[sprite.uid] || this._initGPUSprite(sprite);\n  }\n  _initGPUSprite(sprite) {\n    const batchableSprite = BigPool.get(BatchableSprite);\n    batchableSprite.renderable = sprite;\n    batchableSprite.transform = sprite.groupTransform;\n    batchableSprite.texture = sprite._texture;\n    batchableSprite.bounds = sprite.visualBounds;\n    batchableSprite.roundPixels = this._renderer._roundPixels | sprite._roundPixels;\n    this._gpuSpriteHash[sprite.uid] = batchableSprite;\n    sprite.on(\"destroyed\", this._destroyRenderableBound);\n    return batchableSprite;\n  }\n  destroy() {\n    for (const i in this._gpuSpriteHash) {\n      BigPool.return(this._gpuSpriteHash[i]);\n    }\n    this._gpuSpriteHash = null;\n    this._renderer = null;\n  }\n}\n/** @ignore */\nSpritePipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"sprite\"\n};\n\nexport { SpritePipe };\n//# sourceMappingURL=SpritePipe.mjs.map\n", "import { Color } from '../../../../color/Color.mjs';\nimport { ExtensionType } from '../../../../extensions/Extensions.mjs';\n\n\"use strict\";\nconst _BackgroundSystem = class _BackgroundSystem {\n  constructor() {\n    this.clearBeforeRender = true;\n    this._backgroundColor = new Color(0);\n    this.color = this._backgroundColor;\n    this.alpha = 1;\n  }\n  /**\n   * initiates the background system\n   * @param options - the options for the background colors\n   */\n  init(options) {\n    options = { ..._BackgroundSystem.defaultOptions, ...options };\n    this.clearBeforeRender = options.clearBeforeRender;\n    this.color = options.background || options.backgroundColor || this._backgroundColor;\n    this.alpha = options.backgroundAlpha;\n    this._backgroundColor.setAlpha(options.backgroundAlpha);\n  }\n  /** The background color to fill if not transparent */\n  get color() {\n    return this._backgroundColor;\n  }\n  set color(value) {\n    this._backgroundColor.setValue(value);\n  }\n  /** The background color alpha. Setting this to 0 will make the canvas transparent. */\n  get alpha() {\n    return this._backgroundColor.alpha;\n  }\n  set alpha(value) {\n    this._backgroundColor.setAlpha(value);\n  }\n  /** The background color as an [R, G, B, A] array. */\n  get colorRgba() {\n    return this._backgroundColor.toArray();\n  }\n  /**\n   * destroys the background system\n   * @internal\n   * @ignore\n   */\n  destroy() {\n  }\n};\n/** @ignore */\n_BackgroundSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem,\n    ExtensionType.CanvasSystem\n  ],\n  name: \"background\",\n  priority: 0\n};\n/** default options used by the system */\n_BackgroundSystem.defaultOptions = {\n  /**\n   * {@link WebGLOptions.backgroundAlpha}\n   * @default 1\n   */\n  backgroundAlpha: 1,\n  /**\n   * {@link WebGLOptions.backgroundColor}\n   * @default 0x000000\n   */\n  backgroundColor: 0,\n  /**\n   * {@link WebGLOptions.clearBeforeRender}\n   * @default true\n   */\n  clearBeforeRender: true\n};\nlet BackgroundSystem = _BackgroundSystem;\n\nexport { BackgroundSystem };\n//# sourceMappingURL=BackgroundSystem.mjs.map\n", "import { extensions, ExtensionType } from '../../../../extensions/Extensions.mjs';\nimport { FilterEffect } from '../../../../filters/FilterEffect.mjs';\nimport { warn } from '../../../../utils/logging/warn.mjs';\n\n\"use strict\";\nconst BLEND_MODE_FILTERS = {};\nextensions.handle(ExtensionType.BlendMode, (value) => {\n  if (!value.name) {\n    throw new Error(\"BlendMode extension must have a name property\");\n  }\n  BLEND_MODE_FILTERS[value.name] = value.ref;\n}, (value) => {\n  delete BLEND_MODE_FILTERS[value.name];\n});\nclass BlendModePipe {\n  constructor(renderer) {\n    this._isAdvanced = false;\n    this._filterHash = /* @__PURE__ */ Object.create(null);\n    this._renderer = renderer;\n    this._renderer.runners.prerender.add(this);\n  }\n  prerender() {\n    this._activeBlendMode = \"normal\";\n    this._isAdvanced = false;\n  }\n  /**\n   * This ensures that a blendMode switch is added to the instruction set if the blend mode has changed.\n   * @param renderable - The renderable we are adding to the instruction set\n   * @param blendMode - The blend mode of the renderable\n   * @param instructionSet - The instruction set we are adding to\n   */\n  setBlendMode(renderable, blendMode, instructionSet) {\n    if (this._activeBlendMode === blendMode) {\n      if (this._isAdvanced)\n        this._renderableList.push(renderable);\n      return;\n    }\n    this._activeBlendMode = blendMode;\n    if (this._isAdvanced) {\n      this._endAdvancedBlendMode(instructionSet);\n    }\n    this._isAdvanced = !!BLEND_MODE_FILTERS[blendMode];\n    if (this._isAdvanced) {\n      this._beginAdvancedBlendMode(instructionSet);\n      this._renderableList.push(renderable);\n    }\n  }\n  _beginAdvancedBlendMode(instructionSet) {\n    this._renderer.renderPipes.batch.break(instructionSet);\n    const blendMode = this._activeBlendMode;\n    if (!BLEND_MODE_FILTERS[blendMode]) {\n      warn(`Unable to assign BlendMode: '${blendMode}'. You may want to include: import 'pixi.js/advanced-blend-modes'`);\n      return;\n    }\n    let filterEffect = this._filterHash[blendMode];\n    if (!filterEffect) {\n      filterEffect = this._filterHash[blendMode] = new FilterEffect();\n      filterEffect.filters = [new BLEND_MODE_FILTERS[blendMode]()];\n    }\n    const instruction = {\n      renderPipeId: \"filter\",\n      action: \"pushFilter\",\n      renderables: [],\n      filterEffect,\n      canBundle: false\n    };\n    this._renderableList = instruction.renderables;\n    instructionSet.add(instruction);\n  }\n  _endAdvancedBlendMode(instructionSet) {\n    this._renderableList = null;\n    this._renderer.renderPipes.batch.break(instructionSet);\n    instructionSet.add({\n      renderPipeId: \"filter\",\n      action: \"popFilter\",\n      canBundle: false\n    });\n  }\n  /**\n   * called when the instruction build process is starting this will reset internally to the default blend mode\n   * @internal\n   * @ignore\n   */\n  buildStart() {\n    this._isAdvanced = false;\n  }\n  /**\n   * called when the instruction build process is finished, ensuring that if there is an advanced blend mode\n   * active, we add the final render instructions added to the instruction set\n   * @param instructionSet - The instruction set we are adding to\n   * @internal\n   * @ignore\n   */\n  buildEnd(instructionSet) {\n    if (this._isAdvanced) {\n      this._endAdvancedBlendMode(instructionSet);\n    }\n  }\n  /**\n   * @internal\n   * @ignore\n   */\n  destroy() {\n    this._renderer = null;\n    this._renderableList = null;\n    for (const i in this._filterHash) {\n      this._filterHash[i].destroy();\n    }\n    this._filterHash = null;\n  }\n}\n/** @ignore */\nBlendModePipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"blendMode\"\n};\n\nexport { BlendModePipe };\n//# sourceMappingURL=BlendModePipe.mjs.map\n", "import { ExtensionType } from '../../../../extensions/Extensions.mjs';\nimport { Container } from '../../../../scene/container/Container.mjs';\nimport { Texture } from '../texture/Texture.mjs';\n\n\"use strict\";\nconst imageTypes = {\n  png: \"image/png\",\n  jpg: \"image/jpeg\",\n  webp: \"image/webp\"\n};\nconst _ExtractSystem = class _ExtractSystem {\n  /** @param renderer - The renderer this System works for. */\n  constructor(renderer) {\n    this._renderer = renderer;\n  }\n  _normalizeOptions(options, defaults = {}) {\n    if (options instanceof Container || options instanceof Texture) {\n      return {\n        target: options,\n        ...defaults\n      };\n    }\n    return {\n      ...defaults,\n      ...options\n    };\n  }\n  /**\n   * Will return a HTML Image of the target\n   * @param options - The options for creating the image, or the target to extract\n   * @returns - HTML Image of the target\n   */\n  async image(options) {\n    const image = new Image();\n    image.src = await this.base64(options);\n    return image;\n  }\n  /**\n   * Will return a base64 encoded string of this target. It works by calling\n   * `Extract.canvas` and then running toDataURL on that.\n   * @param options - The options for creating the image, or the target to extract\n   */\n  async base64(options) {\n    options = this._normalizeOptions(\n      options,\n      _ExtractSystem.defaultImageOptions\n    );\n    const { format, quality } = options;\n    const canvas = this.canvas(options);\n    if (canvas.toBlob !== void 0) {\n      return new Promise((resolve, reject) => {\n        canvas.toBlob((blob) => {\n          if (!blob) {\n            reject(new Error(\"ICanvas.toBlob failed!\"));\n            return;\n          }\n          const reader = new FileReader();\n          reader.onload = () => resolve(reader.result);\n          reader.onerror = reject;\n          reader.readAsDataURL(blob);\n        }, imageTypes[format], quality);\n      });\n    }\n    if (canvas.toDataURL !== void 0) {\n      return canvas.toDataURL(imageTypes[format], quality);\n    }\n    if (canvas.convertToBlob !== void 0) {\n      const blob = await canvas.convertToBlob({ type: imageTypes[format], quality });\n      return new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onload = () => resolve(reader.result);\n        reader.onerror = reject;\n        reader.readAsDataURL(blob);\n      });\n    }\n    throw new Error(\"Extract.base64() requires ICanvas.toDataURL, ICanvas.toBlob, or ICanvas.convertToBlob to be implemented\");\n  }\n  /**\n   * Creates a Canvas element, renders this target to it and then returns it.\n   * @param options - The options for creating the canvas, or the target to extract\n   * @returns - A Canvas element with the texture rendered on.\n   */\n  canvas(options) {\n    options = this._normalizeOptions(options);\n    const target = options.target;\n    const renderer = this._renderer;\n    if (target instanceof Texture) {\n      return renderer.texture.generateCanvas(target);\n    }\n    const texture = renderer.textureGenerator.generateTexture(options);\n    const canvas = renderer.texture.generateCanvas(texture);\n    texture.destroy(true);\n    return canvas;\n  }\n  /**\n   * Will return a one-dimensional array containing the pixel data of the entire texture in RGBA\n   * order, with integer values between 0 and 255 (included).\n   * @param options - The options for extracting the image, or the target to extract\n   * @returns - One-dimensional array containing the pixel data of the entire texture\n   */\n  pixels(options) {\n    options = this._normalizeOptions(options);\n    const target = options.target;\n    const renderer = this._renderer;\n    const texture = target instanceof Texture ? target : renderer.textureGenerator.generateTexture(options);\n    const pixelInfo = renderer.texture.getPixels(texture);\n    if (target instanceof Container) {\n      texture.destroy(true);\n    }\n    return pixelInfo;\n  }\n  /**\n   * Will return a texture of the target\n   * @param options - The options for creating the texture, or the target to extract\n   * @returns - A texture of the target\n   */\n  texture(options) {\n    options = this._normalizeOptions(options);\n    if (options.target instanceof Texture)\n      return options.target;\n    return this._renderer.textureGenerator.generateTexture(options);\n  }\n  /**\n   * Will extract a HTMLImage of the target and download it\n   * @param options - The options for downloading and extracting the image, or the target to extract\n   */\n  download(options) {\n    options = this._normalizeOptions(options);\n    const canvas = this.canvas(options);\n    const link = document.createElement(\"a\");\n    link.download = options.filename ?? \"image.png\";\n    link.href = canvas.toDataURL(\"image/png\");\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n  /**\n   * Logs the target to the console as an image. This is a useful way to debug what's happening in the renderer.\n   * @param options - The options for logging the image, or the target to log\n   */\n  log(options) {\n    const width = options.width ?? 200;\n    options = this._normalizeOptions(options);\n    const canvas = this.canvas(options);\n    const base64 = canvas.toDataURL();\n    console.log(`[Pixi Texture] ${canvas.width}px ${canvas.height}px`);\n    const style = [\n      \"font-size: 1px;\",\n      `padding: ${width}px ${300}px;`,\n      `background: url(${base64}) no-repeat;`,\n      \"background-size: contain;\"\n    ].join(\" \");\n    console.log(\"%c \", style);\n  }\n  destroy() {\n    this._renderer = null;\n  }\n};\n/** @ignore */\n_ExtractSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"extract\"\n};\n/** Default options for creating an image. */\n_ExtractSystem.defaultImageOptions = {\n  /** The format of the image. */\n  format: \"png\",\n  /** The quality of the image. */\n  quality: 1\n};\nlet ExtractSystem = _ExtractSystem;\n\nexport { ExtractSystem };\n//# sourceMappingURL=ExtractSystem.mjs.map\n", "import { Color } from '../../../../color/Color.mjs';\nimport { ExtensionType } from '../../../../extensions/Extensions.mjs';\nimport { Matrix } from '../../../../maths/matrix/Matrix.mjs';\nimport { Rectangle } from '../../../../maths/shapes/Rectangle.mjs';\nimport { Bounds } from '../../../../scene/container/bounds/Bounds.mjs';\nimport { getLocalBounds } from '../../../../scene/container/bounds/getLocalBounds.mjs';\nimport { Container } from '../../../../scene/container/Container.mjs';\nimport { RenderTexture } from '../texture/RenderTexture.mjs';\n\n\"use strict\";\nconst tempRect = new Rectangle();\nconst tempBounds = new Bounds();\nconst noColor = [0, 0, 0, 0];\nclass GenerateTextureSystem {\n  constructor(renderer) {\n    this._renderer = renderer;\n  }\n  /**\n   * A Useful function that returns a texture of the display object that can then be used to create sprites\n   * This can be quite useful if your container is complicated and needs to be reused multiple times.\n   * @param {GenerateTextureOptions | Container} options - Generate texture options.\n   * @param {Container} [options.container] - If not given, the renderer's resolution is used.\n   * @param {Rectangle} options.region - The region of the container, that shall be rendered,\n   * @param {number} [options.resolution] - The resolution of the texture being generated.\n   *        if no region is specified, defaults to the local bounds of the container.\n   * @param {GenerateTextureSourceOptions} [options.textureSourceOptions] - Texture options for GPU.\n   * @returns a shiny new texture of the container passed in\n   */\n  generateTexture(options) {\n    if (options instanceof Container) {\n      options = {\n        target: options,\n        frame: void 0,\n        textureSourceOptions: {},\n        resolution: void 0\n      };\n    }\n    const resolution = options.resolution || this._renderer.resolution;\n    const antialias = options.antialias || this._renderer.view.antialias;\n    const container = options.target;\n    let clearColor = options.clearColor;\n    if (clearColor) {\n      const isRGBAArray = Array.isArray(clearColor) && clearColor.length === 4;\n      clearColor = isRGBAArray ? clearColor : Color.shared.setValue(clearColor).toArray();\n    } else {\n      clearColor = noColor;\n    }\n    const region = options.frame?.copyTo(tempRect) || getLocalBounds(container, tempBounds).rectangle;\n    region.width = Math.max(region.width, 1 / resolution) | 0;\n    region.height = Math.max(region.height, 1 / resolution) | 0;\n    const target = RenderTexture.create({\n      ...options.textureSourceOptions,\n      width: region.width,\n      height: region.height,\n      resolution,\n      antialias\n    });\n    const transform = Matrix.shared.translate(-region.x, -region.y);\n    this._renderer.render({\n      container,\n      transform,\n      target,\n      clearColor\n    });\n    target.source.updateMipmaps();\n    return target;\n  }\n  destroy() {\n    this._renderer = null;\n  }\n}\n/** @ignore */\nGenerateTextureSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"textureGenerator\"\n};\n\nexport { GenerateTextureSystem };\n//# sourceMappingURL=GenerateTextureSystem.mjs.map\n", "import { ExtensionType } from '../../../../extensions/Extensions.mjs';\nimport { Matrix } from '../../../../maths/matrix/Matrix.mjs';\nimport { Point } from '../../../../maths/point/Point.mjs';\nimport { color32BitToUniform } from '../../../../scene/graphics/gpu/colorToUniform.mjs';\nimport { BindGroup } from '../../gpu/shader/BindGroup.mjs';\nimport { RendererType } from '../../types.mjs';\nimport { UniformGroup } from '../shader/UniformGroup.mjs';\n\n\"use strict\";\nclass GlobalUniformSystem {\n  constructor(renderer) {\n    this._stackIndex = 0;\n    this._globalUniformDataStack = [];\n    this._uniformsPool = [];\n    this._activeUniforms = [];\n    this._bindGroupPool = [];\n    this._activeBindGroups = [];\n    this._renderer = renderer;\n  }\n  reset() {\n    this._stackIndex = 0;\n    for (let i = 0; i < this._activeUniforms.length; i++) {\n      this._uniformsPool.push(this._activeUniforms[i]);\n    }\n    for (let i = 0; i < this._activeBindGroups.length; i++) {\n      this._bindGroupPool.push(this._activeBindGroups[i]);\n    }\n    this._activeUniforms.length = 0;\n    this._activeBindGroups.length = 0;\n  }\n  start(options) {\n    this.reset();\n    this.push(options);\n  }\n  bind({\n    size,\n    projectionMatrix,\n    worldTransformMatrix,\n    worldColor,\n    offset\n  }) {\n    const renderTarget = this._renderer.renderTarget.renderTarget;\n    const currentGlobalUniformData = this._stackIndex ? this._globalUniformDataStack[this._stackIndex - 1] : {\n      projectionData: renderTarget,\n      worldTransformMatrix: new Matrix(),\n      worldColor: 4294967295,\n      offset: new Point()\n    };\n    const globalUniformData = {\n      projectionMatrix: projectionMatrix || this._renderer.renderTarget.projectionMatrix,\n      resolution: size || renderTarget.size,\n      worldTransformMatrix: worldTransformMatrix || currentGlobalUniformData.worldTransformMatrix,\n      worldColor: worldColor || currentGlobalUniformData.worldColor,\n      offset: offset || currentGlobalUniformData.offset,\n      bindGroup: null\n    };\n    const uniformGroup = this._uniformsPool.pop() || this._createUniforms();\n    this._activeUniforms.push(uniformGroup);\n    const uniforms = uniformGroup.uniforms;\n    uniforms.uProjectionMatrix = globalUniformData.projectionMatrix;\n    uniforms.uResolution = globalUniformData.resolution;\n    uniforms.uWorldTransformMatrix.copyFrom(globalUniformData.worldTransformMatrix);\n    uniforms.uWorldTransformMatrix.tx -= globalUniformData.offset.x;\n    uniforms.uWorldTransformMatrix.ty -= globalUniformData.offset.y;\n    color32BitToUniform(\n      globalUniformData.worldColor,\n      uniforms.uWorldColorAlpha,\n      0\n    );\n    uniformGroup.update();\n    let bindGroup;\n    if (this._renderer.renderPipes.uniformBatch) {\n      bindGroup = this._renderer.renderPipes.uniformBatch.getUniformBindGroup(uniformGroup, false);\n    } else {\n      bindGroup = this._bindGroupPool.pop() || new BindGroup();\n      this._activeBindGroups.push(bindGroup);\n      bindGroup.setResource(uniformGroup, 0);\n    }\n    globalUniformData.bindGroup = bindGroup;\n    this._currentGlobalUniformData = globalUniformData;\n  }\n  push(options) {\n    this.bind(options);\n    this._globalUniformDataStack[this._stackIndex++] = this._currentGlobalUniformData;\n  }\n  pop() {\n    this._currentGlobalUniformData = this._globalUniformDataStack[--this._stackIndex - 1];\n    if (this._renderer.type === RendererType.WEBGL) {\n      this._currentGlobalUniformData.bindGroup.resources[0].update();\n    }\n  }\n  get bindGroup() {\n    return this._currentGlobalUniformData.bindGroup;\n  }\n  get globalUniformData() {\n    return this._currentGlobalUniformData;\n  }\n  get uniformGroup() {\n    return this._currentGlobalUniformData.bindGroup.resources[0];\n  }\n  _createUniforms() {\n    const globalUniforms = new UniformGroup({\n      uProjectionMatrix: { value: new Matrix(), type: \"mat3x3<f32>\" },\n      uWorldTransformMatrix: { value: new Matrix(), type: \"mat3x3<f32>\" },\n      // TODO - someone smart - set this to be a unorm8x4 rather than a vec4<f32>\n      uWorldColorAlpha: { value: new Float32Array(4), type: \"vec4<f32>\" },\n      uResolution: { value: [0, 0], type: \"vec2<f32>\" }\n    }, {\n      isStatic: true\n    });\n    return globalUniforms;\n  }\n  destroy() {\n    this._renderer = null;\n  }\n}\n/** @ignore */\nGlobalUniformSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem,\n    ExtensionType.CanvasSystem\n  ],\n  name: \"globalUniforms\"\n};\n\nexport { GlobalUniformSystem };\n//# sourceMappingURL=GlobalUniformSystem.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { Ticker } from '../../../ticker/Ticker.mjs';\n\n\"use strict\";\nlet uid = 1;\nclass SchedulerSystem {\n  constructor() {\n    this._tasks = [];\n    /** a small off set to apply to the repeat schedules. This is just to make sure they run at slightly different times */\n    this._offset = 0;\n  }\n  /** Initializes the scheduler system and starts the ticker. */\n  init() {\n    Ticker.system.add(this._update, this);\n  }\n  /**\n   * Schedules a repeating task.\n   * @param func - The function to execute.\n   * @param duration - The interval duration in milliseconds.\n   * @param useOffset - this will spread out tasks so that they do not all run at the same time\n   * @returns The unique identifier for the scheduled task.\n   */\n  repeat(func, duration, useOffset = true) {\n    const id = uid++;\n    let offset = 0;\n    if (useOffset) {\n      this._offset += 1e3;\n      offset = this._offset;\n    }\n    this._tasks.push({\n      func,\n      duration,\n      start: performance.now(),\n      offset,\n      last: performance.now(),\n      repeat: true,\n      id\n    });\n    return id;\n  }\n  /**\n   * Cancels a scheduled task.\n   * @param id - The unique identifier of the task to cancel.\n   */\n  cancel(id) {\n    for (let i = 0; i < this._tasks.length; i++) {\n      if (this._tasks[i].id === id) {\n        this._tasks.splice(i, 1);\n        return;\n      }\n    }\n  }\n  /**\n   * Updates and executes the scheduled tasks.\n   * @private\n   */\n  _update() {\n    const now = performance.now();\n    for (let i = 0; i < this._tasks.length; i++) {\n      const task = this._tasks[i];\n      if (now - task.offset - task.last >= task.duration) {\n        const elapsed = now - task.start;\n        task.func(elapsed);\n        task.last = now;\n      }\n    }\n  }\n  /**\n   * Destroys the scheduler system and removes all tasks.\n   * @internal\n   * @ignore\n   */\n  destroy() {\n    Ticker.system.remove(this._update, this);\n    this._tasks.length = 0;\n  }\n}\n/** @ignore */\nSchedulerSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem,\n    ExtensionType.CanvasSystem\n  ],\n  name: \"scheduler\",\n  priority: 0\n};\n\nexport { SchedulerSystem };\n//# sourceMappingURL=SchedulerSystem.mjs.map\n", "import { DOMAdapter } from '../environment/adapter.mjs';\nimport { VERSION } from './const.mjs';\n\n\"use strict\";\nlet saidHello = false;\nfunction sayHello(type) {\n  if (saidHello) {\n    return;\n  }\n  if (DOMAdapter.get().getNavigator().userAgent.toLowerCase().indexOf(\"chrome\") > -1) {\n    const args = [\n      `%c  %c  %c  %c  %c PixiJS %c v${VERSION} (${type}) http://www.pixijs.com/\n\n`,\n      \"background: #E72264; padding:5px 0;\",\n      \"background: #6CA2EA; padding:5px 0;\",\n      \"background: #B5D33D; padding:5px 0;\",\n      \"background: #FED23F; padding:5px 0;\",\n      \"color: #FFFFFF; background: #E72264; padding:5px 0;\",\n      \"color: #E72264; background: #FFFFFF; padding:5px 0;\"\n    ];\n    globalThis.console.log(...args);\n  } else if (globalThis.console) {\n    globalThis.console.log(`PixiJS ${VERSION} - ${type} - http://www.pixijs.com/`);\n  }\n  saidHello = true;\n}\n\nexport { sayHello };\n//# sourceMappingURL=sayHello.mjs.map\n", "import { ExtensionType } from '../../../../extensions/Extensions.mjs';\nimport { sayHello } from '../../../../utils/sayHello.mjs';\nimport { RendererType } from '../../types.mjs';\n\n\"use strict\";\nclass HelloSystem {\n  constructor(renderer) {\n    this._renderer = renderer;\n  }\n  /**\n   * It all starts here! This initiates every system, passing in the options for any system by name.\n   * @param options - the config for the renderer and all its systems\n   */\n  init(options) {\n    if (options.hello) {\n      let name = this._renderer.name;\n      if (this._renderer.type === RendererType.WEBGL) {\n        name += ` ${this._renderer.context.webGLVersion}`;\n      }\n      sayHello(name);\n    }\n  }\n}\n/** @ignore */\nHelloSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem,\n    ExtensionType.CanvasSystem\n  ],\n  name: \"hello\",\n  priority: -2\n};\n/** The default options for the system. */\nHelloSystem.defaultOptions = {\n  /** {@link WebGLOptions.hello} */\n  hello: false\n};\n\nexport { HelloSystem };\n//# sourceMappingURL=HelloSystem.mjs.map\n", "\"use strict\";\nfunction cleanHash(hash) {\n  let clean = false;\n  for (const i in hash) {\n    if (hash[i] == void 0) {\n      clean = true;\n      break;\n    }\n  }\n  if (!clean)\n    return hash;\n  const cleanHash2 = /* @__PURE__ */ Object.create(null);\n  for (const i in hash) {\n    const value = hash[i];\n    if (value) {\n      cleanHash2[i] = value;\n    }\n  }\n  return cleanHash2;\n}\nfunction cleanArray(arr) {\n  let offset = 0;\n  for (let i = 0; i < arr.length; i++) {\n    if (arr[i] == void 0) {\n      offset++;\n    } else {\n      arr[i - offset] = arr[i];\n    }\n  }\n  arr.length -= offset;\n  return arr;\n}\n\nexport { cleanArray, cleanHash };\n//# sourceMappingURL=clean.mjs.map\n", "import { ExtensionType } from '../../../../extensions/Extensions.mjs';\nimport { cleanHash, cleanArray } from '../../../../utils/data/clean.mjs';\n\n\"use strict\";\nlet renderableGCTick = 0;\nconst _RenderableGCSystem = class _RenderableGCSystem {\n  /**\n   * Creates a new RenderableGCSystem instance.\n   * @param renderer - The renderer this garbage collection system works for\n   */\n  constructor(renderer) {\n    /** Array of renderables being tracked for garbage collection */\n    this._managedRenderables = [];\n    /** Array of hash objects being tracked for cleanup */\n    this._managedHashes = [];\n    /** Array of arrays being tracked for cleanup */\n    this._managedArrays = [];\n    this._renderer = renderer;\n  }\n  /**\n   * Initializes the garbage collection system with the provided options.\n   * @param options - Configuration options for the renderer\n   */\n  init(options) {\n    options = { ..._RenderableGCSystem.defaultOptions, ...options };\n    this.maxUnusedTime = options.renderableGCMaxUnusedTime;\n    this._frequency = options.renderableGCFrequency;\n    this.enabled = options.renderableGCActive;\n  }\n  /**\n   * Gets whether the garbage collection system is currently enabled.\n   * @returns True if GC is enabled, false otherwise\n   */\n  get enabled() {\n    return !!this._handler;\n  }\n  /**\n   * Enables or disables the garbage collection system.\n   * When enabled, schedules periodic cleanup of resources.\n   * When disabled, cancels all scheduled cleanups.\n   */\n  set enabled(value) {\n    if (this.enabled === value)\n      return;\n    if (value) {\n      this._handler = this._renderer.scheduler.repeat(\n        () => this.run(),\n        this._frequency,\n        false\n      );\n      this._hashHandler = this._renderer.scheduler.repeat(\n        () => {\n          for (const hash of this._managedHashes) {\n            hash.context[hash.hash] = cleanHash(hash.context[hash.hash]);\n          }\n        },\n        this._frequency\n      );\n      this._arrayHandler = this._renderer.scheduler.repeat(\n        () => {\n          for (const array of this._managedArrays) {\n            cleanArray(array.context[array.hash]);\n          }\n        },\n        this._frequency\n      );\n    } else {\n      this._renderer.scheduler.cancel(this._handler);\n      this._renderer.scheduler.cancel(this._hashHandler);\n      this._renderer.scheduler.cancel(this._arrayHandler);\n    }\n  }\n  /**\n   * Adds a hash table to be managed by the garbage collector.\n   * @param context - The object containing the hash table\n   * @param hash - The property name of the hash table\n   */\n  addManagedHash(context, hash) {\n    this._managedHashes.push({ context, hash });\n  }\n  /**\n   * Adds an array to be managed by the garbage collector.\n   * @param context - The object containing the array\n   * @param hash - The property name of the array\n   */\n  addManagedArray(context, hash) {\n    this._managedArrays.push({ context, hash });\n  }\n  /**\n   * Updates the GC timestamp and tracking before rendering.\n   * @param options - The render options\n   * @param options.container - The container to render\n   */\n  prerender({\n    container\n  }) {\n    this._now = performance.now();\n    container.renderGroup.gcTick = renderableGCTick++;\n    this._updateInstructionGCTick(container.renderGroup, container.renderGroup.gcTick);\n  }\n  /**\n   * Starts tracking a renderable for garbage collection.\n   * @param renderable - The renderable to track\n   */\n  addRenderable(renderable) {\n    if (!this.enabled)\n      return;\n    if (renderable._lastUsed === -1) {\n      this._managedRenderables.push(renderable);\n      renderable.once(\"destroyed\", this._removeRenderable, this);\n    }\n    renderable._lastUsed = this._now;\n  }\n  /**\n   * Performs garbage collection by cleaning up unused renderables.\n   * Removes renderables that haven't been used for longer than maxUnusedTime.\n   */\n  run() {\n    const now = this._now;\n    const managedRenderables = this._managedRenderables;\n    const renderPipes = this._renderer.renderPipes;\n    let offset = 0;\n    for (let i = 0; i < managedRenderables.length; i++) {\n      const renderable = managedRenderables[i];\n      if (renderable === null) {\n        offset++;\n        continue;\n      }\n      const renderGroup = renderable.renderGroup ?? renderable.parentRenderGroup;\n      const currentTick = renderGroup?.instructionSet?.gcTick ?? -1;\n      if ((renderGroup?.gcTick ?? 0) === currentTick) {\n        renderable._lastUsed = now;\n      }\n      if (now - renderable._lastUsed > this.maxUnusedTime) {\n        if (!renderable.destroyed) {\n          const rp = renderPipes;\n          if (renderGroup)\n            renderGroup.structureDidChange = true;\n          rp[renderable.renderPipeId].destroyRenderable(renderable);\n        }\n        renderable._lastUsed = -1;\n        offset++;\n        renderable.off(\"destroyed\", this._removeRenderable, this);\n      } else {\n        managedRenderables[i - offset] = renderable;\n      }\n    }\n    managedRenderables.length -= offset;\n  }\n  /** Cleans up the garbage collection system. Disables GC and removes all tracked resources. */\n  destroy() {\n    this.enabled = false;\n    this._renderer = null;\n    this._managedRenderables.length = 0;\n    this._managedHashes.length = 0;\n    this._managedArrays.length = 0;\n  }\n  /**\n   * Removes a renderable from being tracked when it's destroyed.\n   * @param renderable - The renderable to stop tracking\n   */\n  _removeRenderable(renderable) {\n    const index = this._managedRenderables.indexOf(renderable);\n    if (index >= 0) {\n      renderable.off(\"destroyed\", this._removeRenderable, this);\n      this._managedRenderables[index] = null;\n    }\n  }\n  /**\n   * Updates the GC tick counter for a render group and its children.\n   * @param renderGroup - The render group to update\n   * @param gcTick - The new tick value\n   */\n  _updateInstructionGCTick(renderGroup, gcTick) {\n    renderGroup.instructionSet.gcTick = gcTick;\n    for (const child of renderGroup.renderGroupChildren) {\n      this._updateInstructionGCTick(child, gcTick);\n    }\n  }\n};\n/**\n * Extension metadata for registering this system with the renderer.\n * @ignore\n */\n_RenderableGCSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"renderableGC\",\n  priority: 0\n};\n/**\n * Default configuration options for the garbage collection system.\n * These can be overridden when initializing the renderer.\n */\n_RenderableGCSystem.defaultOptions = {\n  /** Enable/disable the garbage collector */\n  renderableGCActive: true,\n  /** Time in ms before an unused resource is collected (default 1 minute) */\n  renderableGCMaxUnusedTime: 6e4,\n  /** How often to run garbage collection in ms (default 30 seconds) */\n  renderableGCFrequency: 3e4\n};\nlet RenderableGCSystem = _RenderableGCSystem;\n\nexport { RenderableGCSystem };\n//# sourceMappingURL=RenderableGCSystem.mjs.map\n", "import { ExtensionType } from '../../../../extensions/Extensions.mjs';\n\n\"use strict\";\nconst _TextureGCSystem = class _TextureGCSystem {\n  /** @param renderer - The renderer this System works for. */\n  constructor(renderer) {\n    this._renderer = renderer;\n    this.count = 0;\n    this.checkCount = 0;\n  }\n  init(options) {\n    options = { ..._TextureGCSystem.defaultOptions, ...options };\n    this.checkCountMax = options.textureGCCheckCountMax;\n    this.maxIdle = options.textureGCAMaxIdle ?? options.textureGCMaxIdle;\n    this.active = options.textureGCActive;\n  }\n  /**\n   * Checks to see when the last time a texture was used.\n   * If the texture has not been used for a specified amount of time, it will be removed from the GPU.\n   */\n  postrender() {\n    if (!this._renderer.renderingToScreen) {\n      return;\n    }\n    this.count++;\n    if (!this.active)\n      return;\n    this.checkCount++;\n    if (this.checkCount > this.checkCountMax) {\n      this.checkCount = 0;\n      this.run();\n    }\n  }\n  /**\n   * Checks to see when the last time a texture was used.\n   * If the texture has not been used for a specified amount of time, it will be removed from the GPU.\n   */\n  run() {\n    const managedTextures = this._renderer.texture.managedTextures;\n    for (let i = 0; i < managedTextures.length; i++) {\n      const texture = managedTextures[i];\n      if (texture.autoGarbageCollect && texture.resource && texture._touched > -1 && this.count - texture._touched > this.maxIdle) {\n        texture._touched = -1;\n        texture.unload();\n      }\n    }\n  }\n  destroy() {\n    this._renderer = null;\n  }\n};\n/** @ignore */\n_TextureGCSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"textureGC\"\n};\n/** default options for the TextureGCSystem */\n_TextureGCSystem.defaultOptions = {\n  /**\n   * If set to true, this will enable the garbage collector on the GPU.\n   * @default true\n   */\n  textureGCActive: true,\n  /**\n   * @deprecated since 8.3.0\n   * @see {@link TextureGCSystem.textureGCMaxIdle}\n   */\n  textureGCAMaxIdle: null,\n  /**\n   * The maximum idle frames before a texture is destroyed by garbage collection.\n   * @default 60 * 60\n   */\n  textureGCMaxIdle: 60 * 60,\n  /**\n   * Frames between two garbage collections.\n   * @default 600\n   */\n  textureGCCheckCountMax: 600\n};\nlet TextureGCSystem = _TextureGCSystem;\n\nexport { TextureGCSystem };\n//# sourceMappingURL=TextureGCSystem.mjs.map\n", "import { DOMAdapter } from '../../../../environment/adapter.mjs';\nimport { ExtensionType } from '../../../../extensions/Extensions.mjs';\nimport { Rectangle } from '../../../../maths/shapes/Rectangle.mjs';\nimport { deprecation, v8_0_0 } from '../../../../utils/logging/deprecation.mjs';\nimport { RenderTarget } from '../renderTarget/RenderTarget.mjs';\nimport { getCanvasTexture } from '../texture/utils/getCanvasTexture.mjs';\n\n\"use strict\";\nconst _ViewSystem = class _ViewSystem {\n  /**\n   * Whether CSS dimensions of canvas view should be resized to screen dimensions automatically.\n   * @member {boolean}\n   */\n  get autoDensity() {\n    return this.texture.source.autoDensity;\n  }\n  set autoDensity(value) {\n    this.texture.source.autoDensity = value;\n  }\n  /** The resolution / device pixel ratio of the renderer. */\n  get resolution() {\n    return this.texture.source._resolution;\n  }\n  set resolution(value) {\n    this.texture.source.resize(\n      this.texture.source.width,\n      this.texture.source.height,\n      value\n    );\n  }\n  /**\n   * initiates the view system\n   * @param options - the options for the view\n   */\n  init(options) {\n    options = {\n      ..._ViewSystem.defaultOptions,\n      ...options\n    };\n    if (options.view) {\n      deprecation(v8_0_0, \"ViewSystem.view has been renamed to ViewSystem.canvas\");\n      options.canvas = options.view;\n    }\n    this.screen = new Rectangle(0, 0, options.width, options.height);\n    this.canvas = options.canvas || DOMAdapter.get().createCanvas();\n    this.antialias = !!options.antialias;\n    this.texture = getCanvasTexture(this.canvas, options);\n    this.renderTarget = new RenderTarget({\n      colorTextures: [this.texture],\n      depth: !!options.depth,\n      isRoot: true\n    });\n    this.texture.source.transparent = options.backgroundAlpha < 1;\n    this.resolution = options.resolution;\n  }\n  /**\n   * Resizes the screen and canvas to the specified dimensions.\n   * @param desiredScreenWidth - The new width of the screen.\n   * @param desiredScreenHeight - The new height of the screen.\n   * @param resolution\n   */\n  resize(desiredScreenWidth, desiredScreenHeight, resolution) {\n    this.texture.source.resize(desiredScreenWidth, desiredScreenHeight, resolution);\n    this.screen.width = this.texture.frame.width;\n    this.screen.height = this.texture.frame.height;\n  }\n  /**\n   * Destroys this System and optionally removes the canvas from the dom.\n   * @param {options | false} options - The options for destroying the view, or \"false\".\n   * @param options.removeView - Whether to remove the view element from the DOM. Defaults to `false`.\n   */\n  destroy(options = false) {\n    const removeView = typeof options === \"boolean\" ? options : !!options?.removeView;\n    if (removeView && this.canvas.parentNode) {\n      this.canvas.parentNode.removeChild(this.canvas);\n    }\n  }\n};\n/** @ignore */\n_ViewSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem,\n    ExtensionType.CanvasSystem\n  ],\n  name: \"view\",\n  priority: 0\n};\n/** The default options for the view system. */\n_ViewSystem.defaultOptions = {\n  /**\n   * {@link WebGLOptions.width}\n   * @default 800\n   */\n  width: 800,\n  /**\n   * {@link WebGLOptions.height}\n   * @default 600\n   */\n  height: 600,\n  /**\n   * {@link WebGLOptions.autoDensity}\n   * @default false\n   */\n  autoDensity: false,\n  /**\n   * {@link WebGLOptions.antialias}\n   * @default false\n   */\n  antialias: false\n};\nlet ViewSystem = _ViewSystem;\n\nexport { ViewSystem };\n//# sourceMappingURL=ViewSystem.mjs.map\n", "import { CustomRenderPipe } from '../../../../scene/container/CustomRenderPipe.mjs';\nimport { RenderGroupPipe } from '../../../../scene/container/RenderGroupPipe.mjs';\nimport { RenderGroupSystem } from '../../../../scene/container/RenderGroupSystem.mjs';\nimport { SpritePipe } from '../../../../scene/sprite/SpritePipe.mjs';\nimport { RendererInitHook } from '../../../../utils/global/globalHooks.mjs';\nimport { BatcherPipe } from '../../../batcher/shared/BatcherPipe.mjs';\nimport { AlphaMaskPipe } from '../../../mask/alpha/AlphaMaskPipe.mjs';\nimport { ColorMaskPipe } from '../../../mask/color/ColorMaskPipe.mjs';\nimport { StencilMaskPipe } from '../../../mask/stencil/StencilMaskPipe.mjs';\nimport { BackgroundSystem } from '../background/BackgroundSystem.mjs';\nimport { BlendModePipe } from '../blendModes/BlendModePipe.mjs';\nimport { ExtractSystem } from '../extract/ExtractSystem.mjs';\nimport { GenerateTextureSystem } from '../extract/GenerateTextureSystem.mjs';\nimport { GlobalUniformSystem } from '../renderTarget/GlobalUniformSystem.mjs';\nimport { SchedulerSystem } from '../SchedulerSystem.mjs';\nimport { HelloSystem } from '../startup/HelloSystem.mjs';\nimport { RenderableGCSystem } from '../texture/RenderableGCSystem.mjs';\nimport { TextureGCSystem } from '../texture/TextureGCSystem.mjs';\nimport { ViewSystem } from '../view/ViewSystem.mjs';\n\n\"use strict\";\nconst SharedSystems = [\n  BackgroundSystem,\n  GlobalUniformSystem,\n  HelloSystem,\n  ViewSystem,\n  RenderGroupSystem,\n  TextureGCSystem,\n  GenerateTextureSystem,\n  ExtractSystem,\n  RendererInitHook,\n  RenderableGCSystem,\n  SchedulerSystem\n];\nconst SharedRenderPipes = [\n  BlendModePipe,\n  BatcherPipe,\n  SpritePipe,\n  RenderGroupPipe,\n  AlphaMaskPipe,\n  StencilMaskPipe,\n  ColorMaskPipe,\n  CustomRenderPipe\n];\n\nexport { SharedRenderPipes, SharedSystems };\n//# sourceMappingURL=SharedSystems.mjs.map\n"], "names": ["fragment", "vertex", "source", "<PERSON><PERSON><PERSON><PERSON>", "Filter", "options", "sprite", "rest", "textureMatrix", "TextureMatrix", "filterUniforms", "UniformGroup", "Matrix", "gpuProgram", "GpuProgram", "glProgram", "GlProgram", "value", "filterManager", "input", "output", "clearMode", "_Batcher<PERSON>ipe", "renderer", "adaptor", "State", "name", "instructionSet", "batchers", "De<PERSON>ultBatcher", "i", "batchableObject", "batch", "batches", "geometry", "batcher", "shader", "ExtensionType", "BatcherPipe", "extensions", "textureBit", "textureBitGl", "tempBounds", "Bounds", "AlphaMaskEffect", "FilterEffect", "Sprite", "Texture", "AlphaMaskPipe", "mask", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON><PERSON><PERSON>", "instruction", "renderMask", "filterEffect", "BigPool", "bounds", "getGlobalBounds", "colorTextureSource", "filterTexture", "TexturePool", "maskData", "RendererType", "ColorMaskPipe", "_container", "colorStack", "currentColor", "_mask", "StencilMaskPipe", "_a", "effect", "<PERSON><PERSON><PERSON><PERSON>", "renderTargetUid", "maskStackIndex", "STENCIL_MODES", "CLEAR", "ensureAttributes", "extractedData", "attribute", "attributeData", "warn", "ensureStartAndStride", "buffers", "attributes", "tempStride", "tempStart", "j", "buffer", "getAttributeInfoFromFormat", "GpuStencilModesToPixi", "UboSystem", "unsafeEvalSupported", "uniformGroup", "uniformData", "<PERSON><PERSON><PERSON>", "BufferUsage", "uniformGroupSignature", "elements", "layout", "syncFunction", "uboElements", "data", "offset", "uniformGroupData", "dataInt32", "synced", "uniformParsers", "createUboSyncFunction", "parserCode", "arrayGenerationFunction", "singleSettersMap", "funcFragments", "prev", "uboElement", "parsed", "template", "fragmentSrc", "loopMatrix", "col", "row", "uboSyncFunctionsSTD40", "uboSyncFunctionsWGSL", "calculateProjection", "pm", "x", "y", "width", "height", "flipY", "sign", "canvasCache", "getCanvasTexture", "canvas", "texture", "CanvasSource", "onDestroy", "isRenderingToScreen", "renderTarget", "resource", "_RenderTarget", "descriptor", "uid", "TextureSource", "colorSource", "_size", "resolution", "skipColorTexture", "colorTexture", "RenderTarget", "RenderTargetSystem", "Rectangle", "System<PERSON>unner", "target", "clear", "clearColor", "frame", "renderSurface", "<PERSON><PERSON><PERSON><PERSON>", "gpuRenderTarget", "viewport", "pixelWidth", "pixelHeight", "currentRenderTargetData", "sourceRenderSurfaceTexture", "destinationTexture", "originSrc", "size", "originDest", "key", "BufferResource", "EventEmitter", "destroy<PERSON>uffer", "CustomRenderPipe", "container", "executeInstructions", "renderGroup", "instructions", "tempMatrix", "RenderGroupPipe", "batchableRenderGroup", "BatchableSprite", "worldTransformMatrix", "clearList", "list", "index", "tempContainer", "Container", "UPDATE_BLEND_COLOR_VISIBLE", "UPDATE_VISIBLE", "UPDATE_COLOR", "UPDATE_BLEND", "updateRenderGroupTransforms", "updateChildRenderGroups", "updateRenderGroupTransform", "childrenToUpdate", "updateTick", "renderGroupDepth", "childrenAtDepth", "child", "updateTransformAndChildren", "root", "worldAlpha", "renderGroupParent", "multiplyColors", "updateFlags", "localTransform", "parent", "updateColorBlendVisibility", "children", "length", "renderable", "groupAlpha", "validateRenderables", "renderPipes", "rebuildRequired", "RenderGroupSystem", "transform", "originalLocalTransform", "closestCacheAsTexture", "lastTexture", "antialias", "rendererOrPipes", "SpritePipe", "gpuSprite", "batchableSprite", "_BackgroundSystem", "Color", "BackgroundSystem", "BLEND_MODE_FILTERS", "BlendModePipe", "blendMode", "imageTypes", "_ExtractSystem", "defaults", "image", "format", "quality", "resolve", "reject", "blob", "reader", "pixelInfo", "link", "base64", "style", "ExtractSystem", "tempRect", "noColor", "GenerateTextureSystem", "region", "getLocalBounds", "RenderTexture", "GlobalUniformSystem", "projectionMatrix", "worldColor", "currentGlobalUniformData", "Point", "globalUniformData", "uniforms", "color32BitToUniform", "bindGroup", "BindGroup", "SchedulerSystem", "Ticker", "func", "duration", "useOffset", "id", "now", "task", "elapsed", "<PERSON><PERSON><PERSON>", "sayHello", "type", "DOMAdapter", "args", "VERSION", "HelloSystem", "cleanHash", "hash", "clean", "cleanHash2", "cleanArray", "arr", "renderableGCTick", "_RenderableGCSystem", "array", "context", "managedRenderables", "currentTick", "rp", "gcTick", "RenderableGCSystem", "_TextureGCSystem", "managedTextures", "TextureGCSystem", "_ViewSystem", "deprecation", "v8_0_0", "desiredScreenWidth", "desiredScreenHeight", "ViewSystem", "SharedSystems", "RendererInitHook", "SharedRenderPipes"], "mappings": "ocAAA,IAAIA,GAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,ECAXC,GAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,ECATC,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,ECWb,MAAMC,WAAmBC,EAAO,CAC9B,YAAYC,EAAS,CACnB,KAAM,CAAE,OAAAC,EAAQ,GAAGC,CAAI,EAAKF,EACtBG,EAAgB,IAAIC,GAAcH,EAAO,OAAO,EAChDI,EAAiB,IAAIC,EAAa,CACtC,cAAe,CAAE,MAAO,IAAIC,EAAU,KAAM,aAAe,EAC3D,WAAY,CAAE,MAAOJ,EAAc,YAAa,KAAM,WAAa,EACnE,OAAQ,CAAE,MAAO,EAAG,KAAM,KAAO,EACjC,SAAU,CAAE,MAAOH,EAAQ,QAAU,EAAI,EAAG,KAAM,KAAO,CAC/D,CAAK,EACKQ,EAAaC,GAAW,KAAK,CACjC,OAAQ,CACN,OAAAZ,EACA,WAAY,YACb,EACD,SAAU,CACR,OAAAA,EACA,WAAY,cACb,CACP,CAAK,EACKa,EAAYC,GAAU,KAAK,CAC/B,OAAAf,GACA,SAAAD,GACA,KAAM,aACZ,CAAK,EACD,MAAM,CACJ,GAAGO,EACH,WAAAM,EACA,UAAAE,EACA,UAAW,CACT,eAAAL,EACA,aAAcJ,EAAO,QAAQ,MAC9B,CACP,CAAK,EACD,KAAK,OAASA,EACd,KAAK,eAAiBE,CACvB,CACD,IAAI,QAAQS,EAAO,CACjB,KAAK,UAAU,eAAe,SAAS,SAAWA,EAAQ,EAAI,CAC/D,CACD,IAAI,SAAU,CACZ,OAAO,KAAK,UAAU,eAAe,SAAS,WAAa,CAC5D,CACD,MAAMC,EAAeC,EAAOC,EAAQC,EAAW,CAC7C,KAAK,eAAe,QAAU,KAAK,OAAO,QAC1CH,EAAc,sBACZ,KAAK,UAAU,eAAe,SAAS,cACvC,KAAK,MACN,EAAC,QAAQ,KAAK,eAAe,QAAQ,EACtC,KAAK,UAAU,aAAe,KAAK,OAAO,QAAQ,OAClDA,EAAc,YAAY,KAAMC,EAAOC,EAAQC,CAAS,CACzD,CACH,CC1DA,MAAMC,EAAe,MAAMA,EAAa,CACtC,YAAYC,EAAUC,EAAS,CAC7B,KAAK,MAAQC,GAAM,QACnB,KAAK,0BAA4C,OAAO,OAAO,IAAI,EAEnE,KAAK,eAAiC,OAAO,OAAO,IAAI,EACxD,KAAK,SAAWF,EAChB,KAAK,SAAWC,EAChB,KAAK,SAAS,OAAO,IAAI,CAC1B,CACD,OAAO,WAAWE,EAAM,CACtB,OAAO,IAAI,KAAK,mBAAmBA,CAAI,CACxC,CACD,WAAWC,EAAgB,CACzB,IAAIC,EAAW,KAAK,0BAA0BD,EAAe,GAAG,EAC3DC,IACHA,EAAW,KAAK,0BAA0BD,EAAe,GAAG,EAAoB,OAAO,OAAO,IAAI,EAClGC,EAAS,UAAYA,EAAS,QAAU,IAAIC,KAE9C,KAAK,eAAiBD,EACtB,KAAK,aAAe,KAAK,eAAe,QACxC,UAAWE,KAAK,KAAK,eACnB,KAAK,eAAeA,CAAC,EAAE,MAAK,CAE/B,CACD,WAAWC,EAAiBJ,EAAgB,CAC1C,GAAI,KAAK,aAAa,OAASI,EAAgB,YAAa,CAC1D,KAAK,aAAa,MAAMJ,CAAc,EACtC,IAAIK,EAAQ,KAAK,eAAeD,EAAgB,WAAW,EACtDC,IACHA,EAAQ,KAAK,eAAeD,EAAgB,WAAW,EAAIT,GAAa,WAAWS,EAAgB,WAAW,EAC9GC,EAAM,MAAK,GAEb,KAAK,aAAeA,CACrB,CACD,KAAK,aAAa,IAAID,CAAe,CACtC,CACD,MAAMJ,EAAgB,CACpB,KAAK,aAAa,MAAMA,CAAc,CACvC,CACD,SAASA,EAAgB,CACvB,KAAK,aAAa,MAAMA,CAAc,EACtC,MAAMM,EAAU,KAAK,eACrB,UAAWH,KAAKG,EAAS,CACvB,MAAMD,EAAQC,EAAQH,CAAC,EACjBI,EAAWF,EAAM,SACvBE,EAAS,YAAY,gBAAgBF,EAAM,YAAaA,EAAM,UAAW,EAAI,EAC7EE,EAAS,QAAQ,CAAC,EAAE,gBAAgBF,EAAM,gBAAgB,YAAaA,EAAM,cAAe,EAAK,CAClG,CACF,CACD,OAAOL,EAAgB,CACrB,MAAMC,EAAW,KAAK,0BAA0BD,EAAe,GAAG,EAClE,UAAWG,KAAKF,EAAU,CACxB,MAAMO,EAAUP,EAASE,CAAC,EACpBI,EAAWC,EAAQ,SACrBA,EAAQ,QACVA,EAAQ,MAAQ,GAChBD,EAAS,QAAQ,CAAC,EAAE,OAAOC,EAAQ,cAAgB,CAAC,EAEvD,CACF,CACD,QAAQH,EAAO,CACb,GAAIA,EAAM,SAAW,aAAc,CACjC,MAAMG,EAAUH,EAAM,QAChBE,EAAWC,EAAQ,SACnBC,EAASD,EAAQ,OACvB,KAAK,SAAS,MAAM,KAAMD,EAAUE,CAAM,CAC3C,CACD,KAAK,SAAS,QAAQ,KAAMJ,CAAK,CAClC,CACD,SAAU,CACR,KAAK,MAAQ,KACb,KAAK,SAAW,KAChB,KAAK,SAAW,KAChB,UAAWF,KAAK,KAAK,eACnB,KAAK,eAAeA,CAAC,EAAE,QAAO,EAEhC,KAAK,eAAiB,IACvB,CACH,EAEAR,EAAa,UAAY,CACvB,KAAM,CACJe,EAAc,WACdA,EAAc,YACdA,EAAc,WACf,EACD,KAAM,OACR,EACAf,EAAa,mBAAqC,OAAO,OAAO,IAAI,EACpE,IAAIgB,GAAchB,EAClBiB,EAAW,YAAYF,EAAc,QAASC,GAAY,kBAAkB,EAC5EC,EAAW,IAAIV,EAAc,EChGxB,MAACW,GAAa,CACjB,KAAM,cACN,OAAQ,CACN,OAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UASF,KAEE;AAAA;AAAA,SAIH,EACD,SAAU,CACR,OAEE;AAAA;AAAA;AAAA;AAAA;AAAA,UAOF,KAEE;AAAA;AAAA,SAIH,CACH,EACMC,GAAe,CACnB,KAAM,cACN,OAAQ,CACN,OAEE;AAAA;AAAA,UAIF,KAEE;AAAA;AAAA,SAIH,EACD,SAAU,CACR,OAEE;AAAA;AAAA;AAAA;AAAA,UAMF,KAEE;AAAA;AAAA,SAIH,CACH,EC5DMC,GAAa,IAAIC,EACvB,MAAMC,WAAwBC,EAAa,CACzC,aAAc,CACZ,QACA,KAAK,QAAU,CAAC,IAAI1C,GAAW,CAC7B,OAAQ,IAAI2C,GAAOC,EAAQ,KAAK,EAChC,QAAS,GACT,WAAY,UACZ,UAAW,SACZ,CAAA,CAAC,CACH,CACD,IAAI,QAAS,CACX,OAAO,KAAK,QAAQ,CAAC,EAAE,MACxB,CACD,IAAI,OAAO9B,EAAO,CAChB,KAAK,QAAQ,CAAC,EAAE,OAASA,CAC1B,CACD,IAAI,SAAU,CACZ,OAAO,KAAK,QAAQ,CAAC,EAAE,OACxB,CACD,IAAI,QAAQA,EAAO,CACjB,KAAK,QAAQ,CAAC,EAAE,QAAUA,CAC3B,CACH,CACA,MAAM+B,EAAc,CAClB,YAAYzB,EAAU,CACpB,KAAK,iBAAmB,GACxB,KAAK,UAAYA,CAClB,CACD,KAAK0B,EAAMC,EAAiBvB,EAAgB,CAC1C,MAAMJ,EAAW,KAAK,UAWtB,GAVAA,EAAS,YAAY,MAAM,MAAMI,CAAc,EAC/CA,EAAe,IAAI,CACjB,aAAc,YACd,OAAQ,gBACR,KAAAsB,EACA,QAASC,EAAgB,aAAa,QACtC,UAAW,GACX,gBAAAA,CACN,CAAK,EACDD,EAAK,QAAUC,EAAgB,aAAa,QACxCD,EAAK,oBAAqB,CAC5B,MAAME,EAAgBF,EAAK,KAC3BE,EAAc,eAAiB,GAC/BA,EAAc,mBACZxB,EACAJ,EACA,IACR,EACM4B,EAAc,eAAiB,EAChC,CACD5B,EAAS,YAAY,MAAM,MAAMI,CAAc,EAC/CA,EAAe,IAAI,CACjB,aAAc,YACd,OAAQ,cACR,KAAAsB,EACA,gBAAAC,EACA,QAASA,EAAgB,aAAa,QACtC,UAAW,EACjB,CAAK,CACF,CACD,IAAID,EAAMG,EAAkBzB,EAAgB,CACzB,KAAK,UACb,YAAY,MAAM,MAAMA,CAAc,EAC/CA,EAAe,IAAI,CACjB,aAAc,YACd,OAAQ,aACR,KAAAsB,EACA,QAASG,EAAiB,aAAa,QACvC,UAAW,EACjB,CAAK,CACF,CACD,QAAQC,EAAa,CACnB,MAAM9B,EAAW,KAAK,UAChB+B,EAAaD,EAAY,KAAK,oBACpC,GAAIA,EAAY,SAAW,gBAAiB,CAC1C,MAAME,EAAeC,EAAQ,IAAIZ,EAAe,EAEhD,GADAW,EAAa,QAAUF,EAAY,QAC/BC,EAAY,CACdD,EAAY,KAAK,KAAK,WAAa,GACnC,MAAMI,EAASC,GAAgBL,EAAY,KAAK,KAAM,GAAMX,EAAU,EACtEW,EAAY,KAAK,KAAK,WAAa,GACnCI,EAAO,KAAI,EACX,MAAME,EAAqBpC,EAAS,aAAa,aAAa,aAAa,OACrEqC,EAAgBC,EAAY,kBAChCJ,EAAO,MACPA,EAAO,OACPE,EAAmB,YACnBA,EAAmB,SAC7B,EACQpC,EAAS,aAAa,KAAKqC,EAAe,EAAI,EAC9CrC,EAAS,eAAe,KAAK,CAC3B,OAAQkC,EACR,WAAY,UACtB,CAAS,EACD,MAAMnD,EAASiD,EAAa,OAC5BjD,EAAO,QAAUsD,EACjBtD,EAAO,eAAe,GAAKmD,EAAO,KAClCnD,EAAO,eAAe,GAAKmD,EAAO,KAClC,KAAK,iBAAiB,KAAK,CACzB,aAAAF,EACA,gBAAiBF,EAAY,gBAC7B,cAAAO,CACV,CAAS,CACT,MACQL,EAAa,OAASF,EAAY,KAAK,KACvC,KAAK,iBAAiB,KAAK,CACzB,aAAAE,EACA,gBAAiBF,EAAY,eACvC,CAAS,CAET,SAAeA,EAAY,SAAW,cAAe,CAC/C,MAAMS,EAAW,KAAK,iBAAiB,KAAK,iBAAiB,OAAS,CAAC,EACnER,IACE/B,EAAS,OAASwC,EAAa,OACjCxC,EAAS,aAAa,mBAExBA,EAAS,aAAa,MACtBA,EAAS,eAAe,OAE1BA,EAAS,OAAO,KAAK,CACnB,aAAc,SACd,OAAQ,aACR,UAAWuC,EAAS,gBACpB,aAAcA,EAAS,aACvB,UAAW,EACnB,CAAO,CACP,SAAeT,EAAY,SAAW,aAAc,CAC9C9B,EAAS,OAAO,MAChB,MAAMuC,EAAW,KAAK,iBAAiB,IAAG,EACtCR,GACFO,EAAY,cAAcC,EAAS,aAAa,EAElDN,EAAQ,OAAOM,EAAS,YAAY,CACrC,CACF,CACD,SAAU,CACR,KAAK,UAAY,KACjB,KAAK,iBAAmB,IACzB,CACH,CAEAd,GAAc,UAAY,CACxB,KAAM,CACJX,EAAc,WACdA,EAAc,YACdA,EAAc,WACf,EACD,KAAM,WACR,EC9JA,MAAM2B,EAAc,CAClB,YAAYzC,EAAU,CACpB,KAAK,YAAc,GACnB,KAAK,iBAAmB,EACxB,KAAK,cAAgB,EACrB,KAAK,UAAYA,CAClB,CACD,YAAa,CACX,KAAK,YAAY,CAAC,EAAI,GACtB,KAAK,iBAAmB,EACxB,KAAK,cAAgB,EACtB,CACD,KAAK0B,EAAMgB,EAAYtC,EAAgB,CACpB,KAAK,UACb,YAAY,MAAM,MAAMA,CAAc,EAC/C,MAAMuC,EAAa,KAAK,YACxBA,EAAW,KAAK,gBAAgB,EAAIA,EAAW,KAAK,iBAAmB,CAAC,EAAIjB,EAAK,KACjF,MAAMkB,EAAe,KAAK,YAAY,KAAK,gBAAgB,EACvDA,IAAiB,KAAK,gBACxB,KAAK,cAAgBA,EACrBxC,EAAe,IAAI,CACjB,aAAc,YACd,UAAWwC,EACX,UAAW,EACnB,CAAO,GAEH,KAAK,kBACN,CACD,IAAIC,EAAOH,EAAYtC,EAAgB,CACpB,KAAK,UACb,YAAY,MAAM,MAAMA,CAAc,EAC/C,MAAMuC,EAAa,KAAK,YACxB,KAAK,mBACL,MAAMC,EAAeD,EAAW,KAAK,iBAAmB,CAAC,EACrDC,IAAiB,KAAK,gBACxB,KAAK,cAAgBA,EACrBxC,EAAe,IAAI,CACjB,aAAc,YACd,UAAWwC,EACX,UAAW,EACnB,CAAO,EAEJ,CACD,QAAQd,EAAa,CACF,KAAK,UACb,UAAU,QAAQA,EAAY,SAAS,CACjD,CACD,SAAU,CACR,KAAK,YAAc,IACpB,CACH,CAEAW,GAAc,UAAY,CACxB,KAAM,CACJ3B,EAAc,WACdA,EAAc,YACdA,EAAc,WACf,EACD,KAAM,WACR,ECzDA,MAAMgC,EAAgB,CACpB,YAAY9C,EAAU,CAEpB,KAAK,eAAiB,GACtB,KAAK,UAA4B,IAAI,QACrC,KAAK,UAAYA,CAClB,CACD,KAAK0B,EAAMgB,EAAYtC,EAAgB,CACrC,IAAI2C,EACJ,MAAMC,EAAStB,EACT1B,EAAW,KAAK,UACtBA,EAAS,YAAY,MAAM,MAAMI,CAAc,EAC/CJ,EAAS,YAAY,UAAU,aAAagD,EAAO,KAAM,OAAQ5C,CAAc,EAC/EA,EAAe,IAAI,CACjB,aAAc,cACd,OAAQ,gBACR,KAAAsB,EACA,QAASgB,EAAW,aAAa,QACjC,UAAW,EACjB,CAAK,EACD,MAAMd,EAAgBoB,EAAO,KAC7BpB,EAAc,eAAiB,GAC1B,KAAK,UAAU,IAAIoB,CAAM,GAC5B,KAAK,UAAU,IAAIA,EAAQ,CACzB,kBAAmB,EACnB,mBAAoB,CAC5B,CAAO,EAEH,MAAMT,EAAW,KAAK,UAAU,IAAIS,CAAM,EAC1CT,EAAS,kBAAoBnC,EAAe,gBAC5CwB,EAAc,mBACZxB,EACAJ,EACA,IACN,EACI4B,EAAc,eAAiB,GAC/B5B,EAAS,YAAY,MAAM,MAAMI,CAAc,EAC/CA,EAAe,IAAI,CACjB,aAAc,cACd,OAAQ,cACR,KAAAsB,EACA,QAASgB,EAAW,aAAa,QACjC,UAAW,EACjB,CAAK,EACD,MAAMO,EAAqB7C,EAAe,gBAAkBmC,EAAS,kBAAoB,EACzFA,EAAS,mBAAqBU,EAC9B,MAAMC,EAAkBlD,EAAS,aAAa,aAAa,KAC1D+C,EAAK,KAAK,gBAAgBG,CAAe,IAAMH,EAAGG,CAAe,EAAI,EACvE,CACD,IAAIxB,EAAMgB,EAAYtC,EAAgB,CACpC,MAAM4C,EAAStB,EACT1B,EAAW,KAAK,UACtBA,EAAS,YAAY,MAAM,MAAMI,CAAc,EAC/CJ,EAAS,YAAY,UAAU,aAAagD,EAAO,KAAM,OAAQ5C,CAAc,EAC/EA,EAAe,IAAI,CACjB,aAAc,cACd,OAAQ,eACR,QAASsC,EAAW,aAAa,QACjC,UAAW,EACjB,CAAK,EACD,MAAMH,EAAW,KAAK,UAAU,IAAIb,CAAI,EACxC,QAASnB,EAAI,EAAGA,EAAIgC,EAAS,mBAAoBhC,IAC/CH,EAAe,aAAaA,EAAe,iBAAiB,EAAIA,EAAe,aAAamC,EAAS,mBAAmB,EAE1HnC,EAAe,IAAI,CACjB,aAAc,cACd,OAAQ,aACR,UAAW,EACjB,CAAK,CACF,CACD,QAAQ0B,EAAa,CACnB,IAAIiB,EACJ,MAAM/C,EAAW,KAAK,UAChBkD,EAAkBlD,EAAS,aAAa,aAAa,IAC3D,IAAImD,GAAkBJ,EAAK,KAAK,gBAAgBG,CAAe,IAAMH,EAAGG,CAAe,EAAI,GACvFpB,EAAY,SAAW,iBACzB9B,EAAS,aAAa,qBACtBA,EAAS,QAAQ,eAAeoD,EAAc,mBAAoBD,CAAc,EAChFA,IACAnD,EAAS,UAAU,QAAQ,CAAC,GACnB8B,EAAY,SAAW,eAC5BA,EAAY,QACd9B,EAAS,QAAQ,eAAeoD,EAAc,oBAAqBD,CAAc,EAEjFnD,EAAS,QAAQ,eAAeoD,EAAc,YAAaD,CAAc,EAE3EnD,EAAS,UAAU,QAAQ,EAAE,GACpB8B,EAAY,SAAW,gBAChC9B,EAAS,UAAU,QAAQ,CAAC,EACxBmD,IAAmB,EACrBnD,EAAS,QAAQ,eAAeoD,EAAc,sBAAuBD,CAAc,GAEnFnD,EAAS,aAAa,MAAM,KAAMqD,EAAM,OAAO,EAC/CrD,EAAS,QAAQ,eAAeoD,EAAc,SAAUD,CAAc,GAExEA,KACSrB,EAAY,SAAW,eAC5BA,EAAY,QACd9B,EAAS,QAAQ,eAAeoD,EAAc,oBAAqBD,CAAc,EAEjFnD,EAAS,QAAQ,eAAeoD,EAAc,YAAaD,CAAc,EAE3EnD,EAAS,UAAU,QAAQ,EAAE,GAE/B,KAAK,eAAekD,CAAe,EAAIC,CACxC,CACD,SAAU,CACR,KAAK,UAAY,KACjB,KAAK,eAAiB,KACtB,KAAK,UAAY,IAClB,CACH,CACAL,GAAgB,UAAY,CAC1B,KAAM,CACJhC,EAAc,WACdA,EAAc,YACdA,EAAc,WACf,EACD,KAAM,aACR,ECxHA,SAASwC,GAAiB3C,EAAU4C,EAAe,CACjD,UAAWhD,KAAKI,EAAS,WAAY,CACnC,MAAM6C,EAAY7C,EAAS,WAAWJ,CAAC,EACjCkD,EAAgBF,EAAchD,CAAC,EACjCkD,GACFD,EAAU,SAAWA,EAAU,OAASC,EAAc,QACtDD,EAAU,SAAWA,EAAU,OAASC,EAAc,QACtDD,EAAU,WAAaA,EAAU,SAAWC,EAAc,WAE1DC,GAAK,aAAanD,CAAC,mGAAmG,CAEzH,CACDoD,GAAqBhD,CAAQ,CAC/B,CACA,SAASgD,GAAqBhD,EAAU,CACtC,KAAM,CAAE,QAAAiD,EAAS,WAAAC,CAAY,EAAGlD,EAC1BmD,EAAa,CAAA,EACbC,EAAY,CAAA,EAClB,UAAWC,KAAKJ,EAAS,CACvB,MAAMK,EAASL,EAAQI,CAAC,EACxBF,EAAWG,EAAO,GAAG,EAAI,EACzBF,EAAUE,EAAO,GAAG,EAAI,CACzB,CACD,UAAWD,KAAKH,EAAY,CAC1B,MAAML,EAAYK,EAAWG,CAAC,EAC9BF,EAAWN,EAAU,OAAO,GAAG,GAAKU,EAA2BV,EAAU,MAAM,EAAE,MAClF,CACD,UAAWQ,KAAKH,EAAY,CAC1B,MAAML,EAAYK,EAAWG,CAAC,EAC9BR,EAAU,SAAWA,EAAU,OAASM,EAAWN,EAAU,OAAO,GAAG,GACvEA,EAAU,QAAUA,EAAU,MAAQO,EAAUP,EAAU,OAAO,GAAG,GACpEO,EAAUP,EAAU,OAAO,GAAG,GAAKU,EAA2BV,EAAU,MAAM,EAAE,MACjF,CACH,CClCK,MAACW,EAAwB,CAAG,EACjCA,EAAsBf,EAAc,IAAI,EAAI,OAC5Ce,EAAsBf,EAAc,QAAQ,EAAI,CAC9C,iBAAkB,EAClB,gBAAiB,CACnB,EACAe,EAAsBf,EAAc,kBAAkB,EAAI,CACxD,aAAc,CACZ,QAAS,QACT,OAAQ,iBACT,EACD,YAAa,CACX,QAAS,QACT,OAAQ,iBACT,CACH,EACAe,EAAsBf,EAAc,qBAAqB,EAAI,CAC3D,aAAc,CACZ,QAAS,QACT,OAAQ,iBACT,EACD,YAAa,CACX,QAAS,QACT,OAAQ,iBACT,CACH,EACAe,EAAsBf,EAAc,WAAW,EAAI,CACjD,iBAAkB,EAClB,aAAc,CACZ,QAAS,QACT,OAAQ,MACT,EACD,YAAa,CACX,QAAS,QACT,OAAQ,MACT,CACH,EACAe,EAAsBf,EAAc,mBAAmB,EAAI,CACzD,iBAAkB,EAClB,aAAc,CACZ,QAAS,YACT,OAAQ,SACT,EACD,YAAa,CACX,QAAS,YACT,OAAQ,SACT,CACH,EC7CA,MAAMgB,EAAU,CACd,YAAYnE,EAAS,CAEnB,KAAK,kBAAoC,OAAO,OAAO,IAAI,EAC3D,KAAK,SAAWA,EAChB,KAAK,aAAY,CAClB,CAMD,cAAe,CACb,GAAI,CAACoE,GAAmB,EACtB,MAAM,IAAI,MAAM,0GAA0G,CAE7H,CACD,mBAAmBC,EAAc,CAC/B,MAAMC,EAAc,KAAK,oBAAoBD,CAAY,EACzDA,EAAa,SAAWA,EAAa,OAAS,IAAIE,EAAO,CACvD,KAAM,IAAI,aAAaD,EAAY,OAAO,KAAO,CAAC,EAClD,MAAOE,EAAY,QAAUA,EAAY,QAC1C,CAAA,EACF,CACD,oBAAoBH,EAAc,CAChC,OAAO,KAAK,kBAAkBA,EAAa,UAAU,GAAK,KAAK,kBAAkBA,CAAY,CAC9F,CACD,kBAAkBA,EAAc,CAC9B,MAAMI,EAAwBJ,EAAa,WAC3C,IAAIC,EAAc,KAAK,kBAAkBG,CAAqB,EAC9D,GAAI,CAACH,EAAa,CAChB,MAAMI,EAAW,OAAO,KAAKL,EAAa,iBAAiB,EAAE,IAAK/D,GAAM+D,EAAa,kBAAkB/D,CAAC,CAAC,EACnGqE,EAAS,KAAK,SAAS,kBAAkBD,CAAQ,EACjDE,EAAe,KAAK,iBAAiBD,EAAO,WAAW,EAC7DL,EAAc,KAAK,kBAAkBG,CAAqB,EAAI,CAC5D,OAAAE,EACA,aAAAC,CACR,CACK,CACD,OAAO,KAAK,kBAAkBH,CAAqB,CACpD,CACD,iBAAiBI,EAAa,CAC5B,OAAO,KAAK,SAAS,gBAAgBA,CAAW,CACjD,CACD,iBAAiBR,EAAcS,EAAMC,EAAQ,CAC3C,MAAMC,EAAmB,KAAK,oBAAoBX,CAAY,EAC9DA,EAAa,SAAWA,EAAa,OAAS,IAAIE,EAAO,CACvD,KAAM,IAAI,aAAaS,EAAiB,OAAO,KAAO,CAAC,EACvD,MAAOR,EAAY,QAAUA,EAAY,QAC1C,CAAA,GACD,IAAIS,EAAY,KAChB,OAAKH,IACHA,EAAOT,EAAa,OAAO,KAC3BY,EAAYZ,EAAa,OAAO,WAElCU,IAAWA,EAAS,GACpBC,EAAiB,aAAaX,EAAa,SAAUS,EAAMG,EAAWF,CAAM,EACrE,EACR,CACD,mBAAmBV,EAAc,CAC/B,GAAIA,EAAa,UAAY,CAACA,EAAa,SACzC,MAAO,GACTA,EAAa,SAAW,EACxB,MAAMa,EAAS,KAAK,iBAAiBb,CAAY,EACjD,OAAAA,EAAa,OAAO,SACba,CACR,CACD,SAAU,CACR,KAAK,kBAAoB,IAC1B,CACH,CC1EK,MAACC,EAAiB,CAErB,CACE,KAAM,cACN,KAAOL,GACSA,EAAK,MACN,IAAM,OAErB,IAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAYL,QAAS;AAAA;AAAA,SAGV,EAED,CACE,KAAM,YACN,KAAOA,GAASA,EAAK,OAAS,aAAeA,EAAK,OAAS,GAAKA,EAAK,MAAM,QAAU,OACrF,IAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOL,QAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAWV,EAED,CACE,KAAM,YACN,KAAOA,GAASA,EAAK,OAAS,aAAeA,EAAK,OAAS,GAAKA,EAAK,MAAM,IAAM,OACjF,IAAK;AAAA;AAAA;AAAA;AAAA,UAKL,QAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SASV,EAED,CACE,KAAM,YACN,KAAOA,GAASA,EAAK,OAAS,aAAeA,EAAK,OAAS,GAAKA,EAAK,MAAM,MAAQ,OACnF,IAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOL,QAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAWV,EAED,CACE,KAAM,YACN,KAAOA,GAASA,EAAK,OAAS,aAAeA,EAAK,OAAS,GAAKA,EAAK,MAAM,MAAQ,OACnF,IAAK;AAAA;AAAA;AAAA;AAAA;AAAA,UAML,QAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAUV,CACH,EC5GA,SAASM,GAAsBP,EAAaQ,EAAYC,EAAyBC,EAAkB,CACjG,MAAMC,EAAgB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAOpB,EACH,IAAIC,EAAO,EACX,QAASnF,EAAI,EAAGA,EAAIuE,EAAY,OAAQvE,IAAK,CAC3C,MAAMoF,EAAab,EAAYvE,CAAC,EAC1BJ,EAAOwF,EAAW,KAAK,KAC7B,IAAIC,EAAS,GACTZ,EAAS,EACb,QAAShB,EAAI,EAAGA,EAAIoB,EAAe,OAAQpB,IAEzC,GADsBoB,EAAepB,CAAC,EACpB,KAAK2B,EAAW,IAAI,EAAG,CACvCX,EAASW,EAAW,OAAS,EAC7BF,EAAc,KACZ,WAAWtF,CAAI,KACf,aAAa6E,EAASU,CAAI,IAC1BN,EAAepB,CAAC,EAAEsB,CAAU,GAAKF,EAAepB,CAAC,EAAE,GAC7D,EACQ4B,EAAS,GACT,KACD,CAEH,GAAI,CAACA,EACH,GAAID,EAAW,KAAK,KAAO,EACzBX,EAASW,EAAW,OAAS,EAC7BF,EAAc,KAAKF,EAAwBI,EAAYX,EAASU,CAAI,CAAC,MAChE,CACL,MAAMG,EAAWL,EAAiBG,EAAW,KAAK,IAAI,EACtDX,EAASW,EAAW,OAAS,EAC7BF,EAAc,KAEZ;AAAA,6BACmBtF,CAAI;AAAA,gCACD6E,EAASU,CAAI;AAAA,sBACvBG,CAAQ;AAAA,iBAE9B,CACO,CAEHH,EAAOV,CACR,CACD,MAAMc,EAAcL,EAAc,KAAK;AAAA,CAAI,EAC3C,OAAO,IAAI,SACT,KACA,OACA,YACA,SACAK,CACJ,CACA,CCzDA,SAASC,EAAWC,EAAKC,EAAK,CAE5B,MAAO;AAAA,8BADOD,EAAMC,CAEa;AAAA,mCACAD,CAAG,oBAAoBA,CAAG;AAAA;AAAA,KAG7D,CACK,MAACE,GAAwB,CAC5B,IAAK;AAAA,2BAEL,IAAK;AAAA,gCAEL,YAAa;AAAA;AAAA,kCAGb,YAAa;AAAA;AAAA;AAAA,kCAIb,YAAa;AAAA;AAAA;AAAA;AAAA,kCAKb,YAAa;AAAA;AAAA,uCAGb,YAAa;AAAA;AAAA;AAAA,uCAIb,YAAa;AAAA;AAAA;AAAA;AAAA,uCAKb,cAAe;AAAA;AAAA;AAAA;AAAA,kCAKf,cAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mCAUf,cAAe;AAAA;AAAA;AAAA,WAIf,cAAeH,EAAW,EAAG,CAAC,EAC9B,cAAeA,EAAW,EAAG,CAAC,EAC9B,cAAeA,EAAW,EAAG,CAAC,EAC9B,cAAeA,EAAW,EAAG,CAAC,EAC9B,cAAeA,EAAW,EAAG,CAAC,EAC9B,cAAeA,EAAW,EAAG,CAAC,CAChC,EACMI,GAAuB,CAC3B,GAAGD,GACH,cAAe;AAAA;AAAA;AAAA;AAAA;AAAA,KAMjB,ECvEA,SAASE,GAAoBC,EAAIC,EAAGC,EAAGC,EAAOC,EAAQC,EAAO,CAC3D,MAAMC,EAAOD,EAAQ,EAAI,GACzB,OAAAL,EAAG,SAAQ,EACXA,EAAG,EAAI,EAAIG,EAAQ,EACnBH,EAAG,EAAIM,GAAQ,EAAIF,EAAS,GAC5BJ,EAAG,GAAK,GAAKC,EAAID,EAAG,EACpBA,EAAG,GAAK,CAACM,EAAOJ,EAAIF,EAAG,EAChBA,CACT,CCLA,MAAMO,EAA8B,IAAI,IACxC,SAASC,GAAiBC,EAAQhI,EAAS,CACzC,GAAI,CAAC8H,EAAY,IAAIE,CAAM,EAAG,CAC5B,MAAMC,EAAU,IAAIvF,EAAQ,CAC1B,OAAQ,IAAIwF,EAAa,CACvB,SAAUF,EACV,GAAGhI,CACX,CAAO,CACP,CAAK,EACKmI,EAAY,IAAM,CAClBL,EAAY,IAAIE,CAAM,IAAMC,GAC9BH,EAAY,OAAOE,CAAM,CAEjC,EACIC,EAAQ,KAAK,UAAWE,CAAS,EACjCF,EAAQ,OAAO,KAAK,UAAWE,CAAS,EACxCL,EAAY,IAAIE,EAAQC,CAAO,CAChC,CACD,OAAOH,EAAY,IAAIE,CAAM,CAC/B,CCtBA,SAASI,GAAoBC,EAAc,CACzC,MAAMC,EAAWD,EAAa,aAAa,OAAO,SAClD,OAAO,WAAW,mBAAqBC,aAAoB,mBAAqB,SAAS,KAAK,SAASA,CAAQ,CACjH,CCCA,MAAMC,GAAgB,MAAMA,EAAc,CAIxC,YAAYC,EAAa,GAAI,CAiB3B,GAfA,KAAK,IAAMC,EAAI,cAAc,EAK7B,KAAK,cAAgB,GACrB,KAAK,QAAU,EACf,KAAK,OAAS,GACd,KAAK,MAAQ,IAAI,aAAa,CAAC,EAE/B,KAAK,sBAAwB,GAC7BD,EAAa,CAAE,GAAGD,GAAc,eAAgB,GAAGC,CAAU,EAC7D,KAAK,QAAUA,EAAW,QAC1B,KAAK,MAAQA,EAAW,MACxB,KAAK,OAASA,EAAW,OACrB,OAAOA,EAAW,eAAkB,SAAU,CAChD,KAAK,sBAAwB,GAC7B,QAAS/G,EAAI,EAAGA,EAAI+G,EAAW,cAAe/G,IAC5C,KAAK,cAAc,KACjB,IAAIiH,EAAc,CAChB,MAAOF,EAAW,MAClB,OAAQA,EAAW,OACnB,WAAYA,EAAW,WACvB,UAAWA,EAAW,SAClC,CAAW,CACX,CAEA,KAAW,CACL,KAAK,cAAgB,CAAC,GAAGA,EAAW,cAAc,IAAKP,GAAYA,EAAQ,MAAM,CAAC,EAClF,MAAMU,EAAc,KAAK,aAAa,OACtC,KAAK,OAAOA,EAAY,MAAOA,EAAY,OAAQA,EAAY,WAAW,CAC3E,CACD,KAAK,aAAa,OAAO,GAAG,SAAU,KAAK,eAAgB,IAAI,GAC3DH,EAAW,qBAAuB,KAAK,WACrCA,EAAW,+BAA+B9F,GAAW8F,EAAW,+BAA+BE,EACjG,KAAK,oBAAsBF,EAAW,oBAAoB,OAE1D,KAAK,0BAAyB,EAGnC,CACD,IAAI,MAAO,CACT,MAAMI,EAAQ,KAAK,MACnB,OAAAA,EAAM,CAAC,EAAI,KAAK,WAChBA,EAAM,CAAC,EAAI,KAAK,YACTA,CACR,CACD,IAAI,OAAQ,CACV,OAAO,KAAK,aAAa,OAAO,KACjC,CACD,IAAI,QAAS,CACX,OAAO,KAAK,aAAa,OAAO,MACjC,CACD,IAAI,YAAa,CACf,OAAO,KAAK,aAAa,OAAO,UACjC,CACD,IAAI,aAAc,CAChB,OAAO,KAAK,aAAa,OAAO,WACjC,CACD,IAAI,YAAa,CACf,OAAO,KAAK,aAAa,OAAO,WACjC,CACD,IAAI,cAAe,CACjB,OAAO,KAAK,cAAc,CAAC,CAC5B,CACD,eAAe/I,EAAQ,CACrB,KAAK,OAAOA,EAAO,MAAOA,EAAO,OAAQA,EAAO,YAAa,EAAI,CAClE,CAOD,2BAA4B,CACrB,KAAK,sBACR,KAAK,oBAAsB,IAAI6I,EAAc,CAC3C,MAAO,KAAK,MACZ,OAAQ,KAAK,OACb,WAAY,KAAK,WACjB,OAAQ,uBACR,oBAAqB,GACrB,UAAW,GACX,cAAe,CAEvB,CAAO,EAEJ,CACD,OAAOhB,EAAOC,EAAQkB,EAAa,KAAK,WAAYC,EAAmB,GAAO,CAC5E,KAAK,UACL,KAAK,cAAc,QAAQ,CAACC,EAAc,IAAM,CAC1CD,GAAoB,IAAM,GAE9BC,EAAa,OAAO,OAAOrB,EAAOC,EAAQkB,CAAU,CAC1D,CAAK,EACG,KAAK,qBACP,KAAK,oBAAoB,OAAO,OAAOnB,EAAOC,EAAQkB,CAAU,CAEnE,CACD,SAAU,CACR,KAAK,aAAa,OAAO,IAAI,SAAU,KAAK,eAAgB,IAAI,EAC5D,KAAK,uBACP,KAAK,cAAc,QAASZ,GAAY,CACtCA,EAAQ,QAAO,CACvB,CAAO,EAEC,KAAK,sBACP,KAAK,oBAAoB,UACzB,OAAO,KAAK,oBAEf,CACH,EAEAM,GAAc,eAAiB,CAE7B,MAAO,EAEP,OAAQ,EAER,WAAY,EAEZ,cAAe,EAEf,QAAS,GAET,MAAO,GAEP,UAAW,GAGX,OAAQ,EACV,EACA,IAAIS,EAAeT,GClInB,MAAMU,EAAmB,CACvB,YAAY/H,EAAU,CAEpB,KAAK,aAAe,IAAIgI,EAExB,KAAK,SAAW,IAAIA,EAKpB,KAAK,qBAAuB,IAAIC,GAAa,sBAAsB,EAEnE,KAAK,iBAAmB,IAAI5I,EAE5B,KAAK,kBAAoB,CAAC,EAAG,EAAG,EAAG,CAAC,EAKpC,KAAK,iCAAmD,IAAI,IAE5D,KAAK,qBAAuC,OAAO,OAAO,IAAI,EAM9D,KAAK,mBAAqB,GAC1B,KAAK,UAAYW,EACjBA,EAAS,aAAa,eAAe,KAAM,sBAAsB,CAClE,CAED,kBAAmB,CACjB,KAAK,QAAQ,iBAAiB,KAAK,YAAY,CAChD,CASD,YAAY,CACV,OAAAkI,EACA,MAAAC,EACA,WAAAC,EACA,MAAAC,CACJ,EAAK,CACD,KAAK,mBAAmB,OAAS,EACjC,KAAK,KACHH,EACAC,EACAC,EACAC,CACN,EACI,KAAK,aAAa,SAAS,KAAK,QAAQ,EACxC,KAAK,iBAAmB,KAAK,aAC7B,KAAK,kBAAoBnB,GAAoB,KAAK,gBAAgB,EAClE,KAAK,QAAQ,YAAY,KAAK,gBAAgB,CAC/C,CACD,YAAa,CACX,KAAK,QAAQ,aAAa,KAAK,gBAAgB,CAChD,CAaD,KAAKoB,EAAeH,EAAQ,GAAMC,EAAYC,EAAO,CACnD,MAAMlB,EAAe,KAAK,gBAAgBmB,CAAa,EACjDC,EAAY,KAAK,eAAiBpB,EACxC,KAAK,aAAeA,EACpB,KAAK,cAAgBmB,EACrB,MAAME,EAAkB,KAAK,mBAAmBrB,CAAY,GACxDA,EAAa,aAAeqB,EAAgB,OAASrB,EAAa,cAAgBqB,EAAgB,UACpG,KAAK,QAAQ,sBAAsBrB,CAAY,EAC/CqB,EAAgB,MAAQrB,EAAa,WACrCqB,EAAgB,OAASrB,EAAa,aAExC,MAAMxI,EAASwI,EAAa,aACtBsB,EAAW,KAAK,SAChBC,EAAa/J,EAAO,WACpBgK,EAAchK,EAAO,YAI3B,GAHI,CAAC0J,GAASC,aAAyB9G,IACrC6G,EAAQC,EAAc,OAEpBD,EAAO,CACT,MAAMV,EAAahJ,EAAO,YAC1B8J,EAAS,EAAIJ,EAAM,EAAIV,EAAa,GAAM,EAC1Cc,EAAS,EAAIJ,EAAM,EAAIV,EAAa,GAAM,EAC1Cc,EAAS,MAAQJ,EAAM,MAAQV,EAAa,GAAM,EAClDc,EAAS,OAASJ,EAAM,OAASV,EAAa,GAAM,CAC1D,MACMc,EAAS,EAAI,EACbA,EAAS,EAAI,EACbA,EAAS,MAAQC,EACjBD,EAAS,OAASE,EAEpB,OAAAvC,GACE,KAAK,iBACL,EACA,EACAqC,EAAS,MAAQ9J,EAAO,WACxB8J,EAAS,OAAS9J,EAAO,WACzB,CAACwI,EAAa,MACpB,EACI,KAAK,QAAQ,gBAAgBA,EAAcgB,EAAOC,EAAYK,CAAQ,EAClEF,GACF,KAAK,qBAAqB,KAAKpB,CAAY,EAEtCA,CACR,CACD,MAAMe,EAAQC,EAAQ9E,EAAM,IAAK+E,EAAY,CACtCD,IAEDD,IACFA,EAAS,KAAK,gBAAgBA,CAAM,GAEtC,KAAK,QAAQ,MACXA,GAAU,KAAK,aACfC,EACAC,EACA,KAAK,QACX,EACG,CACD,eAAgB,CACd,KAAK,qBAAuC,OAAO,OAAO,IAAI,CAC/D,CAQD,KAAKE,EAAeH,EAAQ9E,EAAM,IAAK+E,EAAYC,EAAO,CACxD,MAAMlB,EAAe,KAAK,KAAKmB,EAAeH,EAAOC,EAAYC,CAAK,EACtE,YAAK,mBAAmB,KAAK,CAC3B,aAAAlB,EACA,MAAAkB,CACN,CAAK,EACMlB,CACR,CAED,KAAM,CACJ,KAAK,mBAAmB,MACxB,MAAMyB,EAA0B,KAAK,mBAAmB,KAAK,mBAAmB,OAAS,CAAC,EAC1F,KAAK,KAAKA,EAAwB,aAAc,GAAO,KAAMA,EAAwB,KAAK,CAC3F,CAQD,gBAAgBN,EAAe,CAC7B,OAAIA,EAAc,YAChBA,EAAgBA,EAAc,QAEzB,KAAK,iCAAiC,IAAIA,CAAa,GAAK,KAAK,kBAAkBA,CAAa,CACxG,CAeD,cAAcO,EAA4BC,EAAoBC,EAAWC,EAAMC,EAAY,CACrFF,EAAU,EAAI,IAChBC,EAAK,OAASD,EAAU,EACxBE,EAAW,GAAKF,EAAU,EAC1BA,EAAU,EAAI,GAEZA,EAAU,EAAI,IAChBC,EAAK,QAAUD,EAAU,EACzBE,EAAW,GAAKF,EAAU,EAC1BA,EAAU,EAAI,GAEhB,KAAM,CAAE,WAAAL,EAAY,YAAAC,CAAa,EAAGE,EACpC,OAAAG,EAAK,MAAQ,KAAK,IAAIA,EAAK,MAAON,EAAaK,EAAU,CAAC,EAC1DC,EAAK,OAAS,KAAK,IAAIA,EAAK,OAAQL,EAAcI,EAAU,CAAC,EACtD,KAAK,QAAQ,cAClBF,EACAC,EACAC,EACAC,EACAC,CACN,CACG,CAKD,oBAAqB,CACd,KAAK,aAAa,UACrB,KAAK,aAAa,QAAU,GAC5B,KAAK,QAAQ,gBAAgB,KAAK,aAAc,GAAO,KAAM,KAAK,QAAQ,EAE7E,CAED,SAAU,CACR,KAAK,UAAY,KACjB,KAAK,iCAAiC,QAAQ,CAAC9B,EAAc+B,IAAQ,CAC/D/B,IAAiB+B,GACnB/B,EAAa,QAAO,CAE5B,CAAK,EACD,KAAK,iCAAiC,QACtC,KAAK,qBAAuC,OAAO,OAAO,IAAI,CAC/D,CACD,kBAAkBmB,EAAe,CAC/B,IAAInB,EAAe,KACnB,OAAIH,EAAa,KAAKsB,CAAa,IACjCA,EAAgBzB,GAAiByB,CAAa,EAAE,QAE9CA,aAAyBR,EAC3BX,EAAemB,EACNA,aAAyBd,IAClCL,EAAe,IAAIW,EAAa,CAC9B,cAAe,CAACQ,CAAa,CACrC,CAAO,EACGtB,EAAa,KAAKsB,EAAc,OAAO,QAAQ,IACjDnB,EAAa,OAAS,IAExBmB,EAAc,KAAK,UAAW,IAAM,CAClCnB,EAAa,QAAO,EACpB,KAAK,iCAAiC,OAAOmB,CAAa,EAC1D,MAAME,EAAkB,KAAK,qBAAqBrB,EAAa,GAAG,EAC9DqB,IACF,KAAK,qBAAqBrB,EAAa,GAAG,EAAI,KAC9C,KAAK,QAAQ,uBAAuBqB,CAAe,EAE7D,CAAO,GAEH,KAAK,iCAAiC,IAAIF,EAAenB,CAAY,EAC9DA,CACR,CACD,mBAAmBA,EAAc,CAC/B,OAAO,KAAK,qBAAqBA,EAAa,GAAG,IAAM,KAAK,qBAAqBA,EAAa,GAAG,EAAI,KAAK,QAAQ,oBAAoBA,CAAY,EACnJ,CACD,YAAa,CACX,KAAK,aAAe,KACpB,KAAK,cAAgB,IACtB,CACH,CC9QA,MAAMgC,WAAuBC,EAAa,CAUxC,YAAY,CAAE,OAAAnF,EAAQ,OAAAe,EAAQ,KAAAgE,CAAI,EAAI,CACpC,QAOA,KAAK,IAAMzB,EAAI,QAAQ,EAMvB,KAAK,cAAgB,iBAMrB,KAAK,SAAW,EAMhB,KAAK,YAAcA,EAAI,UAAU,EAMjC,KAAK,gBAAkB,GAKvB,KAAK,UAAY,GACjB,KAAK,OAAStD,EACd,KAAK,OAASe,EAAS,EACvB,KAAK,KAAOgE,EACZ,KAAK,OAAO,GAAG,SAAU,KAAK,eAAgB,IAAI,CACnD,CACD,gBAAiB,CACf,KAAK,YAAczB,EAAI,UAAU,EACjC,KAAK,KAAK,SAAU,IAAI,CACzB,CAMD,QAAQ8B,EAAgB,GAAO,CAC7B,KAAK,UAAY,GACbA,GACF,KAAK,OAAO,UAEd,KAAK,KAAK,SAAU,IAAI,EACxB,KAAK,OAAS,IACf,CACH,CCvEA,MAAMC,EAAiB,CACrB,YAAYtJ,EAAU,CACpB,KAAK,UAAYA,CAClB,CACD,kBAAmB,CAClB,CACD,mBAAoB,CACnB,CACD,oBAAqB,CACnB,MAAO,EACR,CACD,cAAcuJ,EAAWnJ,EAAgB,CACvC,KAAK,UAAU,YAAY,MAAM,MAAMA,CAAc,EACrDA,EAAe,IAAImJ,CAAS,CAC7B,CACD,QAAQA,EAAW,CACZA,EAAU,cAEfA,EAAU,OAAO,KAAK,SAAS,CAChC,CACD,SAAU,CACR,KAAK,UAAY,IAClB,CACH,CACAD,GAAiB,UAAY,CAC3B,KAAM,CACJxI,EAAc,WACdA,EAAc,YACdA,EAAc,WACf,EACD,KAAM,cACR,ECjCA,SAAS0I,EAAoBC,EAAazJ,EAAU,CAClD,MAAMI,EAAiBqJ,EAAY,eAC7BC,EAAetJ,EAAe,aACpC,QAASG,EAAI,EAAGA,EAAIH,EAAe,gBAAiBG,IAAK,CACvD,MAAMuB,EAAc4H,EAAanJ,CAAC,EAClCP,EAAS8B,EAAY,YAAY,EAAE,QAAQA,CAAW,CACvD,CACH,CCDA,MAAM6H,GAAa,IAAItK,EACvB,MAAMuK,EAAgB,CACpB,YAAY5J,EAAU,CACpB,KAAK,UAAYA,CAClB,CACD,eAAeyJ,EAAarJ,EAAgB,CACtCqJ,EAAY,kBACd,KAAK,6BAA6BA,EAAarJ,CAAc,EAE7D,KAAK,qBAAqBqJ,EAAarJ,CAAc,CAExD,CACD,QAAQqJ,EAAa,CACdA,EAAY,eAEbA,EAAY,kBACd,KAAK,uBAAuBA,CAAW,EAEvC,KAAK,eAAeA,CAAW,EAElC,CACD,SAAU,CACR,KAAK,UAAY,IAClB,CACD,qBAAqBA,EAAarJ,EAAgB,CAChD,KAAK,UAAU,YAAY,MAAM,MAAMA,CAAc,EACjDqJ,EAAY,wBACdxH,EAAQ,OAAOwH,EAAY,qBAAqB,EAChDA,EAAY,sBAAwB,MAEtCrJ,EAAe,IAAIqJ,CAAW,CAC/B,CACD,6BAA6BA,EAAarJ,EAAgB,CACxD,MAAMyJ,EAAuBJ,EAAY,wBAA0BA,EAAY,sBAAwBxH,EAAQ,IAAI6H,EAAe,GAClID,EAAqB,WAAaJ,EAAY,KAC9CI,EAAqB,UAAYJ,EAAY,KAAK,uBAClDI,EAAqB,QAAUJ,EAAY,QAC3CI,EAAqB,OAASJ,EAAY,eAC1CrJ,EAAe,IAAIqJ,CAAW,EAC9B,KAAK,UAAU,YAAY,MAAM,WAAWI,EAAsBzJ,CAAc,CACjF,CACD,uBAAuBqJ,EAAa,CAClC,GAAIA,EAAY,mBAAoB,CAClCA,EAAY,mBAAqB,GACjC,MAAMM,EAAuBJ,GAAW,SAAQ,EAAG,UACjD,CAACF,EAAY,eAAe,EAC5B,CAACA,EAAY,eAAe,CACpC,EACM,KAAK,UAAU,aAAa,KAAKA,EAAY,QAAS,GAAM,KAAMA,EAAY,QAAQ,KAAK,EAC3F,KAAK,UAAU,eAAe,KAAK,CACjC,qBAAAM,EACA,WAAY,UACpB,CAAO,EACDP,EAAoBC,EAAa,KAAK,UAAU,WAAW,EAC3D,KAAK,UAAU,aAAa,mBAC5B,KAAK,UAAU,aAAa,MAC5B,KAAK,UAAU,eAAe,KAC/B,CACDA,EAAY,sBAAsB,SAAS,cAAcA,EAAY,qBAAqB,EAC1FA,EAAY,sBAAsB,SAAS,SAAS,QAAQ,CAAC,EAAE,QAChE,CACD,eAAeA,EAAa,CAC1B,KAAK,UAAU,eAAe,KAAK,CACjC,qBAAsBA,EAAY,8BAClC,WAAYA,EAAY,eAC9B,CAAK,EACDD,EAAoBC,EAAa,KAAK,UAAU,WAAW,EAC3D,KAAK,UAAU,eAAe,KAC/B,CACH,CACAG,GAAgB,UAAY,CAC1B,KAAM,CACJ9I,EAAc,WACdA,EAAc,YACdA,EAAc,WACf,EACD,KAAM,aACR,ECnFA,SAASkJ,EAAUC,EAAMC,EAAO,CAC9BA,IAAUA,EAAQ,GAClB,QAASlG,EAAIkG,EAAOlG,EAAIiG,EAAK,QACvBA,EAAKjG,CAAC,EADyBA,IAEjCiG,EAAKjG,CAAC,EAAI,IAKhB,CCLA,MAAMmG,GAAgB,IAAIC,EACpBC,EAA6BC,GAAiBC,GAAeC,GACnE,SAASC,GAA4BhB,EAAaiB,EAA0B,GAAO,CACjFC,GAA2BlB,CAAW,EACtC,MAAMmB,EAAmBnB,EAAY,iBAC/BoB,EAAapB,EAAY,aAC/B,UAAWzF,KAAK4G,EAAkB,CAChC,MAAME,EAAmB,OAAO9G,CAAC,EAC3B+G,EAAkBH,EAAiB5G,CAAC,EACpCiG,EAAOc,EAAgB,KACvBb,EAAQa,EAAgB,MAC9B,QAASxK,EAAI,EAAGA,EAAI2J,EAAO3J,IAAK,CAC9B,MAAMyK,EAAQf,EAAK1J,CAAC,EAChByK,EAAM,oBAAsBvB,GAAeuB,EAAM,2BAA6BF,GAChFG,GAA2BD,EAAOH,EAAY,CAAC,CAElD,CACDb,EAAUC,EAAMC,CAAK,EACrBa,EAAgB,MAAQ,CACzB,CACD,GAAIL,EACF,QAASnK,EAAI,EAAGA,EAAIkJ,EAAY,oBAAoB,OAAQlJ,IAC1DkK,GAA4BhB,EAAY,oBAAoBlJ,CAAC,EAAGmK,CAAuB,CAG7F,CACA,SAASC,GAA2BlB,EAAa,CAC/C,MAAMyB,EAAOzB,EAAY,KACzB,IAAI0B,EACJ,GAAI1B,EAAY,kBAAmB,CACjC,MAAM2B,EAAoB3B,EAAY,kBACtCA,EAAY,eAAe,WACzByB,EAAK,uBACLE,EAAkB,cACxB,EACI3B,EAAY,WAAa4B,GACvBH,EAAK,WACLE,EAAkB,UACxB,EACID,EAAaD,EAAK,WAAaE,EAAkB,UACrD,MACI3B,EAAY,eAAe,SAASyB,EAAK,cAAc,EACvDzB,EAAY,WAAayB,EAAK,WAC9BC,EAAaD,EAAK,WAEpBC,EAAaA,EAAa,EAAI,EAAIA,EAAa,EAAI,EAAIA,EACvD1B,EAAY,WAAa0B,EACzB1B,EAAY,gBAAkBA,EAAY,aAAe0B,EAAa,IAAM,IAAM,GACpF,CACA,SAASF,GAA2B1B,EAAWsB,EAAYS,EAAa,CACtE,GAAIT,IAAetB,EAAU,WAC3B,OACFA,EAAU,WAAasB,EACvBtB,EAAU,UAAY,GACtB,MAAMgC,EAAiBhC,EAAU,eACjCA,EAAU,qBAAoB,EAC9B,MAAMiC,EAASjC,EAAU,OAiBzB,GAhBIiC,GAAU,CAACA,EAAO,aACpBF,GAAe/B,EAAU,aACzBA,EAAU,uBAAuB,WAC/BgC,EACAC,EAAO,sBACb,EACQF,EAAcjB,GAChBoB,EAA2BlC,EAAWiC,EAAQF,CAAW,IAG3DA,EAAc/B,EAAU,aACxBA,EAAU,uBAAuB,SAASgC,CAAc,EACpDD,EAAcjB,GAChBoB,EAA2BlC,EAAWY,GAAemB,CAAW,GAGhE,CAAC/B,EAAU,YAAa,CAC1B,MAAMmC,EAAWnC,EAAU,SACrBoC,EAASD,EAAS,OACxB,QAASnL,EAAI,EAAGA,EAAIoL,EAAQpL,IAC1B0K,GAA2BS,EAASnL,CAAC,EAAGsK,EAAYS,CAAW,EAEjE,MAAM7B,EAAcF,EAAU,kBACxBqC,EAAarC,EACfqC,EAAW,cAAgB,CAACnC,EAAY,oBAC1CA,EAAY,iBAAiBmC,CAAU,CAE1C,CACH,CACA,SAASH,EAA2BlC,EAAWiC,EAAQF,EAAa,CAClE,GAAIA,EAAcf,GAAc,CAC9BhB,EAAU,WAAa8B,GACrB9B,EAAU,WACViC,EAAO,UACb,EACI,IAAIK,EAAatC,EAAU,WAAaiC,EAAO,WAC/CK,EAAaA,EAAa,EAAI,EAAIA,EAAa,EAAI,EAAIA,EACvDtC,EAAU,WAAasC,EACvBtC,EAAU,gBAAkBA,EAAU,aAAesC,EAAa,IAAM,IAAM,GAC/E,CACGP,EAAcd,KAChBjB,EAAU,eAAiBA,EAAU,iBAAmB,UAAYiC,EAAO,eAAiBjC,EAAU,gBAEpG+B,EAAchB,KAChBf,EAAU,oBAAsBA,EAAU,mBAAqBiC,EAAO,qBAExEjC,EAAU,aAAe,CAC3B,CC5GA,SAASuC,GAAoBrC,EAAasC,EAAa,CACrD,KAAM,CAAE,KAAA9B,EAAM,MAAAC,GAAUT,EAAY,4BACpC,IAAIuC,EAAkB,GACtB,QAASzL,EAAI,EAAGA,EAAI2J,EAAO3J,IAAK,CAC9B,MAAMgJ,EAAYU,EAAK1J,CAAC,EAIxB,GADAyL,EADaD,EADMxC,EACiB,YAAY,EACzB,mBAAmBA,CAAS,EAC/CyC,EACF,KAEH,CACD,OAAAvC,EAAY,mBAAqBuC,EAC1BA,CACT,CCLA,MAAMrC,GAAa,IAAItK,EACvB,MAAM4M,EAAkB,CACtB,YAAYjM,EAAU,CACpB,KAAK,UAAYA,CAClB,CACD,OAAO,CAAE,UAAAuJ,EAAW,UAAA2C,GAAa,CAC/B,MAAMV,EAASjC,EAAU,OACnB6B,EAAoB7B,EAAU,YAAY,kBAChDA,EAAU,OAAS,KACnBA,EAAU,YAAY,kBAAoB,KAC1C,MAAMvJ,EAAW,KAAK,UACtB,IAAImM,EAAyBxC,GACzBuC,IACFC,EAAyBA,EAAuB,SAAS5C,EAAU,YAAY,cAAc,EAC7FA,EAAU,YAAY,eAAe,SAAS2C,CAAS,GAEzD,MAAMH,EAAc/L,EAAS,YAC7B,KAAK,0BAA0BuJ,EAAU,YAAa,IAAI,EAC1D,KAAK,oBAAoBA,EAAU,WAAW,EAC9CvJ,EAAS,eAAe,MAAM,CAC5B,qBAAsBkM,EAAY3C,EAAU,YAAY,eAAiBA,EAAU,YAAY,eAC/F,WAAYA,EAAU,YAAY,eACxC,CAAK,EACDC,EAAoBD,EAAU,YAAawC,CAAW,EAClDA,EAAY,cACdA,EAAY,aAAa,YAEvBG,GACF3C,EAAU,YAAY,eAAe,SAAS4C,CAAsB,EAEtE5C,EAAU,OAASiC,EACnBjC,EAAU,YAAY,kBAAoB6B,CAC3C,CACD,SAAU,CACR,KAAK,UAAY,IAClB,CACD,0BAA0B3B,EAAa2C,EAAuB,CAC5D,GAAI3C,EAAY,kBAAmB,CACjC,GAAI,CAACA,EAAY,mBACf,OACF2C,EAAwB3C,CACzB,CACDA,EAAY,iCAAmC2C,EAC/C,QAAS7L,EAAIkJ,EAAY,oBAAoB,OAAS,EAAGlJ,GAAK,EAAGA,IAC/D,KAAK,0BAA0BkJ,EAAY,oBAAoBlJ,CAAC,EAAG6L,CAAqB,EAG1F,GADA3C,EAAY,mBAAkB,EAC1BA,EAAY,mBACd,GAAIA,EAAY,mBAAoB,CAClC,MAAMvH,EAASuH,EAAY,KAAK,eAAc,EAC9CvH,EAAO,KAAI,EACX,MAAMmK,EAAc5C,EAAY,QAC5BA,EAAY,SACdnH,EAAY,cAAcmH,EAAY,OAAO,EAE/C,MAAMzJ,EAAW,KAAK,UAChB2H,EAAa8B,EAAY,eAAe,YAAczJ,EAAS,KAAK,WACpEsM,EAAY7C,EAAY,eAAe,WAAazJ,EAAS,KAAK,UACxEyJ,EAAY,QAAUnH,EAAY,kBAChCJ,EAAO,MACPA,EAAO,OACPyF,EACA2E,CACV,EACQ7C,EAAY,iBAAmBA,EAAY,eAAiB,IAAIrI,GAChEqI,EAAY,eAAe,SAASvH,CAAM,EACtCmK,IAAgB5C,EAAY,SAC1BA,EAAY,oBACdA,EAAY,kBAAkB,mBAAqB,GAGxD,OACQA,EAAY,UACrBnH,EAAY,cAAcmH,EAAY,OAAO,EAC7CA,EAAY,QAAU,KAEzB,CACD,oBAAoBA,EAAa,CAC/B,MAAMzJ,EAAW,KAAK,UAChB+L,EAAc/L,EAAS,YAiB7B,GAhBAyJ,EAAY,YAAYzJ,CAAQ,EAChCyJ,EAAY,eAAe,YAAcsC,EACpCtC,EAAY,mBAGfO,EAAUP,EAAY,4BAA4B,KAAM,CAAC,EAFzDqC,GAAoBrC,EAAasC,CAAW,EAI9CtB,GAA4BhB,CAAW,EACnCA,EAAY,oBACdA,EAAY,mBAAqB,GACjC,KAAK,mBAAmBA,EAAazJ,CAAQ,GAE7C,KAAK,mBAAmByJ,CAAW,EAErCA,EAAY,4BAA4B,MAAQ,EAChDzJ,EAAS,YAAY,MAAM,OAAOyJ,EAAY,cAAc,EACxD,EAAAA,EAAY,mBAAqB,CAACA,EAAY,oBAElD,QAASlJ,EAAI,EAAGA,EAAIkJ,EAAY,oBAAoB,OAAQlJ,IAC1D,KAAK,oBAAoBkJ,EAAY,oBAAoBlJ,CAAC,CAAC,CAE9D,CACD,mBAAmBkJ,EAAa,CAC9B,KAAM,CAAE,KAAAQ,EAAM,MAAAC,GAAUT,EAAY,4BACpC,QAASlJ,EAAI,EAAGA,EAAI2J,EAAO3J,IAAK,CAC9B,MAAMgJ,EAAYU,EAAK1J,CAAC,EACpBgJ,EAAU,eACZE,EAAY,iBAAiBF,CAAS,CAEzC,CACDS,EAAUC,EAAMC,CAAK,CACtB,CACD,mBAAmBT,EAAa8C,EAAiB,CAC/C,MAAMrB,EAAOzB,EAAY,KACnBrJ,EAAiBqJ,EAAY,eACnCrJ,EAAe,MAAK,EACpB,MAAMJ,EAAWuM,EAAgB,YAAcA,EAAkBA,EAAgB,MAAM,SACjFR,EAAc/L,EAAS,YAC7B+L,EAAY,MAAM,WAAW3L,CAAc,EAC3C2L,EAAY,UAAU,aACtBA,EAAY,UAAU,aAClBb,EAAK,kBACPA,EAAK,aAAY,EAEnBA,EAAK,8BAA8B9K,EAAgBJ,EAAU,IAAI,EACjE+L,EAAY,MAAM,SAAS3L,CAAc,EACzC2L,EAAY,UAAU,SAAS3L,CAAc,CAC9C,CACH,CAEA6L,GAAkB,UAAY,CAC5B,KAAM,CACJnL,EAAc,YACdA,EAAc,aACdA,EAAc,YACf,EACD,KAAM,aACR,EC9IA,MAAM0L,EAAW,CACf,YAAYxM,EAAU,CACpB,KAAK,eAAiC,OAAO,OAAO,IAAI,EACxD,KAAK,wBAA0B,KAAK,kBAAkB,KAAK,IAAI,EAC/D,KAAK,UAAYA,EACjB,KAAK,UAAU,aAAa,eAAe,KAAM,gBAAgB,CAClE,CACD,cAAcjB,EAAQqB,EAAgB,CACpC,MAAMqM,EAAY,KAAK,cAAc1N,CAAM,EACvCA,EAAO,eACT,KAAK,uBAAuBA,EAAQ0N,CAAS,EAC/C,KAAK,UAAU,YAAY,MAAM,WAAWA,EAAWrM,CAAc,CACtE,CACD,iBAAiBrB,EAAQ,CACvB,MAAM0N,EAAY,KAAK,eAAe1N,EAAO,GAAG,EAC5CA,EAAO,eACT,KAAK,uBAAuBA,EAAQ0N,CAAS,EAC/CA,EAAU,SAAS,cAAcA,CAAS,CAC3C,CACD,mBAAmB1N,EAAQ,CACzB,MAAM0N,EAAY,KAAK,cAAc1N,CAAM,EAC3C,MAAO,CAAC0N,EAAU,SAAS,sBACzBA,EACA1N,EAAO,QACb,CACG,CACD,kBAAkBA,EAAQ,CACxB,MAAM2N,EAAkB,KAAK,eAAe3N,EAAO,GAAG,EACtDkD,EAAQ,OAAOyK,CAAe,EAC9B,KAAK,eAAe3N,EAAO,GAAG,EAAI,KAClCA,EAAO,IAAI,YAAa,KAAK,uBAAuB,CACrD,CACD,uBAAuBA,EAAQ2N,EAAiB,CAC9CA,EAAgB,OAAS3N,EAAO,aAChC2N,EAAgB,QAAU3N,EAAO,QAClC,CACD,cAAcA,EAAQ,CACpB,OAAO,KAAK,eAAeA,EAAO,GAAG,GAAK,KAAK,eAAeA,CAAM,CACrE,CACD,eAAeA,EAAQ,CACrB,MAAM2N,EAAkBzK,EAAQ,IAAI6H,EAAe,EACnD,OAAA4C,EAAgB,WAAa3N,EAC7B2N,EAAgB,UAAY3N,EAAO,eACnC2N,EAAgB,QAAU3N,EAAO,SACjC2N,EAAgB,OAAS3N,EAAO,aAChC2N,EAAgB,YAAc,KAAK,UAAU,aAAe3N,EAAO,aACnE,KAAK,eAAeA,EAAO,GAAG,EAAI2N,EAClC3N,EAAO,GAAG,YAAa,KAAK,uBAAuB,EAC5C2N,CACR,CACD,SAAU,CACR,UAAWnM,KAAK,KAAK,eACnB0B,EAAQ,OAAO,KAAK,eAAe1B,CAAC,CAAC,EAEvC,KAAK,eAAiB,KACtB,KAAK,UAAY,IAClB,CACH,CAEAiM,GAAW,UAAY,CACrB,KAAM,CACJ1L,EAAc,WACdA,EAAc,YACdA,EAAc,WACf,EACD,KAAM,QACR,ECnEA,MAAM6L,EAAoB,MAAMA,EAAkB,CAChD,aAAc,CACZ,KAAK,kBAAoB,GACzB,KAAK,iBAAmB,IAAIC,GAAM,CAAC,EACnC,KAAK,MAAQ,KAAK,iBAClB,KAAK,MAAQ,CACd,CAKD,KAAK9N,EAAS,CACZA,EAAU,CAAE,GAAG6N,GAAkB,eAAgB,GAAG7N,CAAO,EAC3D,KAAK,kBAAoBA,EAAQ,kBACjC,KAAK,MAAQA,EAAQ,YAAcA,EAAQ,iBAAmB,KAAK,iBACnE,KAAK,MAAQA,EAAQ,gBACrB,KAAK,iBAAiB,SAASA,EAAQ,eAAe,CACvD,CAED,IAAI,OAAQ,CACV,OAAO,KAAK,gBACb,CACD,IAAI,MAAMY,EAAO,CACf,KAAK,iBAAiB,SAASA,CAAK,CACrC,CAED,IAAI,OAAQ,CACV,OAAO,KAAK,iBAAiB,KAC9B,CACD,IAAI,MAAMA,EAAO,CACf,KAAK,iBAAiB,SAASA,CAAK,CACrC,CAED,IAAI,WAAY,CACd,OAAO,KAAK,iBAAiB,SAC9B,CAMD,SAAU,CACT,CACH,EAEAiN,EAAkB,UAAY,CAC5B,KAAM,CACJ7L,EAAc,YACdA,EAAc,aACdA,EAAc,YACf,EACD,KAAM,aACN,SAAU,CACZ,EAEA6L,EAAkB,eAAiB,CAKjC,gBAAiB,EAKjB,gBAAiB,EAKjB,kBAAmB,EACrB,EACA,IAAIE,GAAmBF,ECvEvB,MAAMG,EAAqB,CAAA,EAC3B9L,EAAW,OAAOF,EAAc,UAAYpB,GAAU,CACpD,GAAI,CAACA,EAAM,KACT,MAAM,IAAI,MAAM,+CAA+C,EAEjEoN,EAAmBpN,EAAM,IAAI,EAAIA,EAAM,GACzC,EAAIA,GAAU,CACZ,OAAOoN,EAAmBpN,EAAM,IAAI,CACtC,CAAC,EACD,MAAMqN,EAAc,CAClB,YAAY/M,EAAU,CACpB,KAAK,YAAc,GACnB,KAAK,YAA8B,OAAO,OAAO,IAAI,EACrD,KAAK,UAAYA,EACjB,KAAK,UAAU,QAAQ,UAAU,IAAI,IAAI,CAC1C,CACD,WAAY,CACV,KAAK,iBAAmB,SACxB,KAAK,YAAc,EACpB,CAOD,aAAa4L,EAAYoB,EAAW5M,EAAgB,CAClD,GAAI,KAAK,mBAAqB4M,EAAW,CACnC,KAAK,aACP,KAAK,gBAAgB,KAAKpB,CAAU,EACtC,MACD,CACD,KAAK,iBAAmBoB,EACpB,KAAK,aACP,KAAK,sBAAsB5M,CAAc,EAE3C,KAAK,YAAc,CAAC,CAAC0M,EAAmBE,CAAS,EAC7C,KAAK,cACP,KAAK,wBAAwB5M,CAAc,EAC3C,KAAK,gBAAgB,KAAKwL,CAAU,EAEvC,CACD,wBAAwBxL,EAAgB,CACtC,KAAK,UAAU,YAAY,MAAM,MAAMA,CAAc,EACrD,MAAM4M,EAAY,KAAK,iBACvB,GAAI,CAACF,EAAmBE,CAAS,EAAG,CAClCtJ,GAAK,gCAAgCsJ,CAAS,mEAAmE,EACjH,MACD,CACD,IAAIhL,EAAe,KAAK,YAAYgL,CAAS,EACxChL,IACHA,EAAe,KAAK,YAAYgL,CAAS,EAAI,IAAI1L,GACjDU,EAAa,QAAU,CAAC,IAAI8K,EAAmBE,CAAS,CAAG,GAE7D,MAAMlL,EAAc,CAClB,aAAc,SACd,OAAQ,aACR,YAAa,CAAE,EACf,aAAAE,EACA,UAAW,EACjB,EACI,KAAK,gBAAkBF,EAAY,YACnC1B,EAAe,IAAI0B,CAAW,CAC/B,CACD,sBAAsB1B,EAAgB,CACpC,KAAK,gBAAkB,KACvB,KAAK,UAAU,YAAY,MAAM,MAAMA,CAAc,EACrDA,EAAe,IAAI,CACjB,aAAc,SACd,OAAQ,YACR,UAAW,EACjB,CAAK,CACF,CAMD,YAAa,CACX,KAAK,YAAc,EACpB,CAQD,SAASA,EAAgB,CACnB,KAAK,aACP,KAAK,sBAAsBA,CAAc,CAE5C,CAKD,SAAU,CACR,KAAK,UAAY,KACjB,KAAK,gBAAkB,KACvB,UAAWG,KAAK,KAAK,YACnB,KAAK,YAAYA,CAAC,EAAE,QAAO,EAE7B,KAAK,YAAc,IACpB,CACH,CAEAwM,GAAc,UAAY,CACxB,KAAM,CACJjM,EAAc,WACdA,EAAc,YACdA,EAAc,WACf,EACD,KAAM,WACR,EClHA,MAAMmM,EAAa,CACjB,IAAK,YACL,IAAK,aACL,KAAM,YACR,EACMC,EAAiB,MAAMA,EAAe,CAE1C,YAAYlN,EAAU,CACpB,KAAK,UAAYA,CAClB,CACD,kBAAkBlB,EAASqO,EAAW,GAAI,CACxC,OAAIrO,aAAmBsL,GAAatL,aAAmB0C,EAC9C,CACL,OAAQ1C,EACR,GAAGqO,CACX,EAEW,CACL,GAAGA,EACH,GAAGrO,CACT,CACG,CAMD,MAAM,MAAMA,EAAS,CACnB,MAAMsO,EAAQ,IAAI,MAClB,OAAAA,EAAM,IAAM,MAAM,KAAK,OAAOtO,CAAO,EAC9BsO,CACR,CAMD,MAAM,OAAOtO,EAAS,CACpBA,EAAU,KAAK,kBACbA,EACAoO,GAAe,mBACrB,EACI,KAAM,CAAE,OAAAG,EAAQ,QAAAC,CAAS,EAAGxO,EACtBgI,EAAS,KAAK,OAAOhI,CAAO,EAClC,GAAIgI,EAAO,SAAW,OACpB,OAAO,IAAI,QAAQ,CAACyG,EAASC,IAAW,CACtC1G,EAAO,OAAQ2G,GAAS,CACtB,GAAI,CAACA,EAAM,CACTD,EAAO,IAAI,MAAM,wBAAwB,CAAC,EAC1C,MACD,CACD,MAAME,EAAS,IAAI,WACnBA,EAAO,OAAS,IAAMH,EAAQG,EAAO,MAAM,EAC3CA,EAAO,QAAUF,EACjBE,EAAO,cAAcD,CAAI,CAC1B,EAAER,EAAWI,CAAM,EAAGC,CAAO,CACtC,CAAO,EAEH,GAAIxG,EAAO,YAAc,OACvB,OAAOA,EAAO,UAAUmG,EAAWI,CAAM,EAAGC,CAAO,EAErD,GAAIxG,EAAO,gBAAkB,OAAQ,CACnC,MAAM2G,EAAO,MAAM3G,EAAO,cAAc,CAAE,KAAMmG,EAAWI,CAAM,EAAG,QAAAC,CAAO,CAAE,EAC7E,OAAO,IAAI,QAAQ,CAACC,EAASC,IAAW,CACtC,MAAME,EAAS,IAAI,WACnBA,EAAO,OAAS,IAAMH,EAAQG,EAAO,MAAM,EAC3CA,EAAO,QAAUF,EACjBE,EAAO,cAAcD,CAAI,CACjC,CAAO,CACF,CACD,MAAM,IAAI,MAAM,yGAAyG,CAC1H,CAMD,OAAO3O,EAAS,CACdA,EAAU,KAAK,kBAAkBA,CAAO,EACxC,MAAMoJ,EAASpJ,EAAQ,OACjBkB,EAAW,KAAK,UACtB,GAAIkI,aAAkB1G,EACpB,OAAOxB,EAAS,QAAQ,eAAekI,CAAM,EAE/C,MAAMnB,EAAU/G,EAAS,iBAAiB,gBAAgBlB,CAAO,EAC3DgI,EAAS9G,EAAS,QAAQ,eAAe+G,CAAO,EACtD,OAAAA,EAAQ,QAAQ,EAAI,EACbD,CACR,CAOD,OAAOhI,EAAS,CACdA,EAAU,KAAK,kBAAkBA,CAAO,EACxC,MAAMoJ,EAASpJ,EAAQ,OACjBkB,EAAW,KAAK,UAChB+G,EAAUmB,aAAkB1G,EAAU0G,EAASlI,EAAS,iBAAiB,gBAAgBlB,CAAO,EAChG6O,EAAY3N,EAAS,QAAQ,UAAU+G,CAAO,EACpD,OAAImB,aAAkBkC,GACpBrD,EAAQ,QAAQ,EAAI,EAEf4G,CACR,CAMD,QAAQ7O,EAAS,CAEf,OADAA,EAAU,KAAK,kBAAkBA,CAAO,EACpCA,EAAQ,kBAAkB0C,EACrB1C,EAAQ,OACV,KAAK,UAAU,iBAAiB,gBAAgBA,CAAO,CAC/D,CAKD,SAASA,EAAS,CAChBA,EAAU,KAAK,kBAAkBA,CAAO,EACxC,MAAMgI,EAAS,KAAK,OAAOhI,CAAO,EAC5B8O,EAAO,SAAS,cAAc,GAAG,EACvCA,EAAK,SAAW9O,EAAQ,UAAY,YACpC8O,EAAK,KAAO9G,EAAO,UAAU,WAAW,EACxC,SAAS,KAAK,YAAY8G,CAAI,EAC9BA,EAAK,MAAK,EACV,SAAS,KAAK,YAAYA,CAAI,CAC/B,CAKD,IAAI9O,EAAS,CACX,MAAM0H,EAAQ1H,EAAQ,OAAS,IAC/BA,EAAU,KAAK,kBAAkBA,CAAO,EACxC,MAAMgI,EAAS,KAAK,OAAOhI,CAAO,EAC5B+O,EAAS/G,EAAO,YACtB,QAAQ,IAAI,kBAAkBA,EAAO,KAAK,MAAMA,EAAO,MAAM,IAAI,EACjE,MAAMgH,EAAQ,CACZ,kBACA,YAAYtH,CAAK,YACjB,mBAAmBqH,CAAM,eACzB,2BACN,EAAM,KAAK,GAAG,EACV,QAAQ,IAAI,MAAOC,CAAK,CACzB,CACD,SAAU,CACR,KAAK,UAAY,IAClB,CACH,EAEAZ,EAAe,UAAY,CACzB,KAAM,CACJpM,EAAc,YACdA,EAAc,YACf,EACD,KAAM,SACR,EAEAoM,EAAe,oBAAsB,CAEnC,OAAQ,MAER,QAAS,CACX,EACA,IAAIa,GAAgBb,ECnKpB,MAAMc,GAAW,IAAIhG,EACf7G,GAAa,IAAIC,EACjB6M,GAAU,CAAC,EAAG,EAAG,EAAG,CAAC,EAC3B,MAAMC,EAAsB,CAC1B,YAAYlO,EAAU,CACpB,KAAK,UAAYA,CAClB,CAYD,gBAAgBlB,EAAS,CACnBA,aAAmBsL,IACrBtL,EAAU,CACR,OAAQA,EACR,MAAO,OACP,qBAAsB,CAAE,EACxB,WAAY,MACpB,GAEI,MAAM6I,EAAa7I,EAAQ,YAAc,KAAK,UAAU,WAClDwN,EAAYxN,EAAQ,WAAa,KAAK,UAAU,KAAK,UACrDyK,EAAYzK,EAAQ,OAC1B,IAAIsJ,EAAatJ,EAAQ,WACrBsJ,EAEFA,EADoB,MAAM,QAAQA,CAAU,GAAKA,EAAW,SAAW,EAC5CA,EAAawE,GAAM,OAAO,SAASxE,CAAU,EAAE,UAE1EA,EAAa6F,GAEf,MAAME,EAASrP,EAAQ,OAAO,OAAOkP,EAAQ,GAAKI,GAAe7E,EAAWpI,EAAU,EAAE,UACxFgN,EAAO,MAAQ,KAAK,IAAIA,EAAO,MAAO,EAAIxG,CAAU,EAAI,EACxDwG,EAAO,OAAS,KAAK,IAAIA,EAAO,OAAQ,EAAIxG,CAAU,EAAI,EAC1D,MAAMO,EAASmG,GAAc,OAAO,CAClC,GAAGvP,EAAQ,qBACX,MAAOqP,EAAO,MACd,OAAQA,EAAO,OACf,WAAAxG,EACA,UAAA2E,CACN,CAAK,EACKJ,EAAY7M,EAAO,OAAO,UAAU,CAAC8O,EAAO,EAAG,CAACA,EAAO,CAAC,EAC9D,YAAK,UAAU,OAAO,CACpB,UAAA5E,EACA,UAAA2C,EACA,OAAAhE,EACA,WAAAE,CACN,CAAK,EACDF,EAAO,OAAO,gBACPA,CACR,CACD,SAAU,CACR,KAAK,UAAY,IAClB,CACH,CAEAgG,GAAsB,UAAY,CAChC,KAAM,CACJpN,EAAc,YACdA,EAAc,YACf,EACD,KAAM,kBACR,ECrEA,MAAMwN,EAAoB,CACxB,YAAYtO,EAAU,CACpB,KAAK,YAAc,EACnB,KAAK,wBAA0B,GAC/B,KAAK,cAAgB,GACrB,KAAK,gBAAkB,GACvB,KAAK,eAAiB,GACtB,KAAK,kBAAoB,GACzB,KAAK,UAAYA,CAClB,CACD,OAAQ,CACN,KAAK,YAAc,EACnB,QAASO,EAAI,EAAGA,EAAI,KAAK,gBAAgB,OAAQA,IAC/C,KAAK,cAAc,KAAK,KAAK,gBAAgBA,CAAC,CAAC,EAEjD,QAASA,EAAI,EAAGA,EAAI,KAAK,kBAAkB,OAAQA,IACjD,KAAK,eAAe,KAAK,KAAK,kBAAkBA,CAAC,CAAC,EAEpD,KAAK,gBAAgB,OAAS,EAC9B,KAAK,kBAAkB,OAAS,CACjC,CACD,MAAMzB,EAAS,CACb,KAAK,MAAK,EACV,KAAK,KAAKA,CAAO,CAClB,CACD,KAAK,CACH,KAAAkK,EACA,iBAAAuF,EACA,qBAAAxE,EACA,WAAAyE,EACA,OAAAxJ,CACJ,EAAK,CACD,MAAMmC,EAAe,KAAK,UAAU,aAAa,aAC3CsH,EAA2B,KAAK,YAAc,KAAK,wBAAwB,KAAK,YAAc,CAAC,EAAI,CACvG,eAAgBtH,EAChB,qBAAsB,IAAI9H,EAC1B,WAAY,WACZ,OAAQ,IAAIqP,EAClB,EACUC,EAAoB,CACxB,iBAAkBJ,GAAoB,KAAK,UAAU,aAAa,iBAClE,WAAYvF,GAAQ7B,EAAa,KACjC,qBAAsB4C,GAAwB0E,EAAyB,qBACvE,WAAYD,GAAcC,EAAyB,WACnD,OAAQzJ,GAAUyJ,EAAyB,OAC3C,UAAW,IACjB,EACUnK,EAAe,KAAK,cAAc,IAAG,GAAM,KAAK,kBACtD,KAAK,gBAAgB,KAAKA,CAAY,EACtC,MAAMsK,EAAWtK,EAAa,SAC9BsK,EAAS,kBAAoBD,EAAkB,iBAC/CC,EAAS,YAAcD,EAAkB,WACzCC,EAAS,sBAAsB,SAASD,EAAkB,oBAAoB,EAC9EC,EAAS,sBAAsB,IAAMD,EAAkB,OAAO,EAC9DC,EAAS,sBAAsB,IAAMD,EAAkB,OAAO,EAC9DE,GACEF,EAAkB,WAClBC,EAAS,iBACT,CACN,EACItK,EAAa,OAAM,EACnB,IAAIwK,EACA,KAAK,UAAU,YAAY,aAC7BA,EAAY,KAAK,UAAU,YAAY,aAAa,oBAAoBxK,EAAc,EAAK,GAE3FwK,EAAY,KAAK,eAAe,IAAG,GAAM,IAAIC,GAC7C,KAAK,kBAAkB,KAAKD,CAAS,EACrCA,EAAU,YAAYxK,EAAc,CAAC,GAEvCqK,EAAkB,UAAYG,EAC9B,KAAK,0BAA4BH,CAClC,CACD,KAAK7P,EAAS,CACZ,KAAK,KAAKA,CAAO,EACjB,KAAK,wBAAwB,KAAK,aAAa,EAAI,KAAK,yBACzD,CACD,KAAM,CACJ,KAAK,0BAA4B,KAAK,wBAAwB,EAAE,KAAK,YAAc,CAAC,EAChF,KAAK,UAAU,OAAS0D,EAAa,OACvC,KAAK,0BAA0B,UAAU,UAAU,CAAC,EAAE,QAEzD,CACD,IAAI,WAAY,CACd,OAAO,KAAK,0BAA0B,SACvC,CACD,IAAI,mBAAoB,CACtB,OAAO,KAAK,yBACb,CACD,IAAI,cAAe,CACjB,OAAO,KAAK,0BAA0B,UAAU,UAAU,CAAC,CAC5D,CACD,iBAAkB,CAUhB,OATuB,IAAIpD,EAAa,CACtC,kBAAmB,CAAE,MAAO,IAAIC,EAAU,KAAM,aAAe,EAC/D,sBAAuB,CAAE,MAAO,IAAIA,EAAU,KAAM,aAAe,EAEnE,iBAAkB,CAAE,MAAO,IAAI,aAAa,CAAC,EAAG,KAAM,WAAa,EACnE,YAAa,CAAE,MAAO,CAAC,EAAG,CAAC,EAAG,KAAM,WAAa,CACvD,EAAO,CACD,SAAU,EAChB,CAAK,CAEF,CACD,SAAU,CACR,KAAK,UAAY,IAClB,CACH,CAEAiP,GAAoB,UAAY,CAC9B,KAAM,CACJxN,EAAc,YACdA,EAAc,aACdA,EAAc,YACf,EACD,KAAM,gBACR,ECxHA,IAAIyG,GAAM,EACV,MAAMyH,EAAgB,CACpB,aAAc,CACZ,KAAK,OAAS,GAEd,KAAK,QAAU,CAChB,CAED,MAAO,CACLC,EAAO,OAAO,IAAI,KAAK,QAAS,IAAI,CACrC,CAQD,OAAOC,EAAMC,EAAUC,EAAY,GAAM,CACvC,MAAMC,EAAK9H,KACX,IAAIvC,EAAS,EACb,OAAIoK,IACF,KAAK,SAAW,IAChBpK,EAAS,KAAK,SAEhB,KAAK,OAAO,KAAK,CACf,KAAAkK,EACA,SAAAC,EACA,MAAO,YAAY,IAAK,EACxB,OAAAnK,EACA,KAAM,YAAY,IAAK,EACvB,OAAQ,GACR,GAAAqK,CACN,CAAK,EACMA,CACR,CAKD,OAAOA,EAAI,CACT,QAAS9O,EAAI,EAAGA,EAAI,KAAK,OAAO,OAAQA,IACtC,GAAI,KAAK,OAAOA,CAAC,EAAE,KAAO8O,EAAI,CAC5B,KAAK,OAAO,OAAO9O,EAAG,CAAC,EACvB,MACD,CAEJ,CAKD,SAAU,CACR,MAAM+O,EAAM,YAAY,MACxB,QAAS/O,EAAI,EAAGA,EAAI,KAAK,OAAO,OAAQA,IAAK,CAC3C,MAAMgP,EAAO,KAAK,OAAOhP,CAAC,EAC1B,GAAI+O,EAAMC,EAAK,OAASA,EAAK,MAAQA,EAAK,SAAU,CAClD,MAAMC,EAAUF,EAAMC,EAAK,MAC3BA,EAAK,KAAKC,CAAO,EACjBD,EAAK,KAAOD,CACb,CACF,CACF,CAMD,SAAU,CACRL,EAAO,OAAO,OAAO,KAAK,QAAS,IAAI,EACvC,KAAK,OAAO,OAAS,CACtB,CACH,CAEAD,GAAgB,UAAY,CAC1B,KAAM,CACJlO,EAAc,YACdA,EAAc,aACdA,EAAc,YACf,EACD,KAAM,YACN,SAAU,CACZ,EClFA,IAAI2O,EAAY,GAChB,SAASC,GAASC,EAAM,CACtB,GAAI,CAAAF,EAGJ,IAAIG,GAAW,MAAM,eAAe,UAAU,YAAW,EAAG,QAAQ,QAAQ,EAAI,GAAI,CAClF,MAAMC,EAAO,CACX,iCAAiCC,CAAO,KAAKH,CAAI;AAAA;AAAA,EAGjD,sCACA,sCACA,sCACA,sCACA,sDACA,qDACN,EACI,WAAW,QAAQ,IAAI,GAAGE,CAAI,CAClC,MAAa,WAAW,SACpB,WAAW,QAAQ,IAAI,UAAUC,CAAO,MAAMH,CAAI,2BAA2B,EAE/EF,EAAY,GACd,CCrBA,MAAMM,CAAY,CAChB,YAAY/P,EAAU,CACpB,KAAK,UAAYA,CAClB,CAKD,KAAKlB,EAAS,CACZ,GAAIA,EAAQ,MAAO,CACjB,IAAIqB,EAAO,KAAK,UAAU,KACtB,KAAK,UAAU,OAASqC,EAAa,QACvCrC,GAAQ,IAAI,KAAK,UAAU,QAAQ,YAAY,IAEjDuP,GAASvP,CAAI,CACd,CACF,CACH,CAEA4P,EAAY,UAAY,CACtB,KAAM,CACJjP,EAAc,YACdA,EAAc,aACdA,EAAc,YACf,EACD,KAAM,QACN,SAAU,EACZ,EAEAiP,EAAY,eAAiB,CAE3B,MAAO,EACT,ECpCA,SAASC,GAAUC,EAAM,CACvB,IAAIC,EAAQ,GACZ,UAAW3P,KAAK0P,EACd,GAAIA,EAAK1P,CAAC,GAAK,KAAQ,CACrB2P,EAAQ,GACR,KACD,CAEH,GAAI,CAACA,EACH,OAAOD,EACT,MAAME,EAA6B,OAAO,OAAO,IAAI,EACrD,UAAW5P,KAAK0P,EAAM,CACpB,MAAMvQ,EAAQuQ,EAAK1P,CAAC,EAChBb,IACFyQ,EAAW5P,CAAC,EAAIb,EAEnB,CACD,OAAOyQ,CACT,CACA,SAASC,GAAWC,EAAK,CACvB,IAAIrL,EAAS,EACb,QAASzE,EAAI,EAAGA,EAAI8P,EAAI,OAAQ9P,IAC1B8P,EAAI9P,CAAC,GAAK,KACZyE,IAEAqL,EAAI9P,EAAIyE,CAAM,EAAIqL,EAAI9P,CAAC,EAG3B,OAAA8P,EAAI,QAAUrL,EACPqL,CACT,CC3BA,IAAIC,GAAmB,EACvB,MAAMC,EAAsB,MAAMA,EAAoB,CAKpD,YAAYvQ,EAAU,CAEpB,KAAK,oBAAsB,GAE3B,KAAK,eAAiB,GAEtB,KAAK,eAAiB,GACtB,KAAK,UAAYA,CAClB,CAKD,KAAKlB,EAAS,CACZA,EAAU,CAAE,GAAGyR,GAAoB,eAAgB,GAAGzR,CAAO,EAC7D,KAAK,cAAgBA,EAAQ,0BAC7B,KAAK,WAAaA,EAAQ,sBAC1B,KAAK,QAAUA,EAAQ,kBACxB,CAKD,IAAI,SAAU,CACZ,MAAO,CAAC,CAAC,KAAK,QACf,CAMD,IAAI,QAAQY,EAAO,CACb,KAAK,UAAYA,IAEjBA,GACF,KAAK,SAAW,KAAK,UAAU,UAAU,OACvC,IAAM,KAAK,IAAK,EAChB,KAAK,WACL,EACR,EACM,KAAK,aAAe,KAAK,UAAU,UAAU,OAC3C,IAAM,CACJ,UAAWuQ,KAAQ,KAAK,eACtBA,EAAK,QAAQA,EAAK,IAAI,EAAID,GAAUC,EAAK,QAAQA,EAAK,IAAI,CAAC,CAE9D,EACD,KAAK,UACb,EACM,KAAK,cAAgB,KAAK,UAAU,UAAU,OAC5C,IAAM,CACJ,UAAWO,KAAS,KAAK,eACvBJ,GAAWI,EAAM,QAAQA,EAAM,IAAI,CAAC,CAEvC,EACD,KAAK,UACb,IAEM,KAAK,UAAU,UAAU,OAAO,KAAK,QAAQ,EAC7C,KAAK,UAAU,UAAU,OAAO,KAAK,YAAY,EACjD,KAAK,UAAU,UAAU,OAAO,KAAK,aAAa,GAErD,CAMD,eAAeC,EAASR,EAAM,CAC5B,KAAK,eAAe,KAAK,CAAE,QAAAQ,EAAS,KAAAR,CAAM,CAAA,CAC3C,CAMD,gBAAgBQ,EAASR,EAAM,CAC7B,KAAK,eAAe,KAAK,CAAE,QAAAQ,EAAS,KAAAR,CAAM,CAAA,CAC3C,CAMD,UAAU,CACR,UAAA1G,CACJ,EAAK,CACD,KAAK,KAAO,YAAY,MACxBA,EAAU,YAAY,OAAS+G,KAC/B,KAAK,yBAAyB/G,EAAU,YAAaA,EAAU,YAAY,MAAM,CAClF,CAKD,cAAcqC,EAAY,CACnB,KAAK,UAENA,EAAW,YAAc,KAC3B,KAAK,oBAAoB,KAAKA,CAAU,EACxCA,EAAW,KAAK,YAAa,KAAK,kBAAmB,IAAI,GAE3DA,EAAW,UAAY,KAAK,KAC7B,CAKD,KAAM,CACJ,MAAM0D,EAAM,KAAK,KACXoB,EAAqB,KAAK,oBAC1B3E,EAAc,KAAK,UAAU,YACnC,IAAI/G,EAAS,EACb,QAASzE,EAAI,EAAGA,EAAImQ,EAAmB,OAAQnQ,IAAK,CAClD,MAAMqL,EAAa8E,EAAmBnQ,CAAC,EACvC,GAAIqL,IAAe,KAAM,CACvB5G,IACA,QACD,CACD,MAAMyE,EAAcmC,EAAW,aAAeA,EAAW,kBACnD+E,EAAclH,GAAa,gBAAgB,QAAU,GAI3D,IAHKA,GAAa,QAAU,KAAOkH,IACjC/E,EAAW,UAAY0D,GAErBA,EAAM1D,EAAW,UAAY,KAAK,cAAe,CACnD,GAAI,CAACA,EAAW,UAAW,CACzB,MAAMgF,EAAK7E,EACPtC,IACFA,EAAY,mBAAqB,IACnCmH,EAAGhF,EAAW,YAAY,EAAE,kBAAkBA,CAAU,CACzD,CACDA,EAAW,UAAY,GACvB5G,IACA4G,EAAW,IAAI,YAAa,KAAK,kBAAmB,IAAI,CAChE,MACQ8E,EAAmBnQ,EAAIyE,CAAM,EAAI4G,CAEpC,CACD8E,EAAmB,QAAU1L,CAC9B,CAED,SAAU,CACR,KAAK,QAAU,GACf,KAAK,UAAY,KACjB,KAAK,oBAAoB,OAAS,EAClC,KAAK,eAAe,OAAS,EAC7B,KAAK,eAAe,OAAS,CAC9B,CAKD,kBAAkB4G,EAAY,CAC5B,MAAM1B,EAAQ,KAAK,oBAAoB,QAAQ0B,CAAU,EACrD1B,GAAS,IACX0B,EAAW,IAAI,YAAa,KAAK,kBAAmB,IAAI,EACxD,KAAK,oBAAoB1B,CAAK,EAAI,KAErC,CAMD,yBAAyBT,EAAaoH,EAAQ,CAC5CpH,EAAY,eAAe,OAASoH,EACpC,UAAW7F,KAASvB,EAAY,oBAC9B,KAAK,yBAAyBuB,EAAO6F,CAAM,CAE9C,CACH,EAKAN,EAAoB,UAAY,CAC9B,KAAM,CACJzP,EAAc,YACdA,EAAc,YACf,EACD,KAAM,eACN,SAAU,CACZ,EAKAyP,EAAoB,eAAiB,CAEnC,mBAAoB,GAEpB,0BAA2B,IAE3B,sBAAuB,GACzB,EACA,IAAIO,GAAqBP,ECzMzB,MAAMQ,EAAmB,MAAMA,EAAiB,CAE9C,YAAY/Q,EAAU,CACpB,KAAK,UAAYA,EACjB,KAAK,MAAQ,EACb,KAAK,WAAa,CACnB,CACD,KAAKlB,EAAS,CACZA,EAAU,CAAE,GAAGiS,GAAiB,eAAgB,GAAGjS,CAAO,EAC1D,KAAK,cAAgBA,EAAQ,uBAC7B,KAAK,QAAUA,EAAQ,mBAAqBA,EAAQ,iBACpD,KAAK,OAASA,EAAQ,eACvB,CAKD,YAAa,CACN,KAAK,UAAU,oBAGpB,KAAK,QACA,KAAK,SAEV,KAAK,aACD,KAAK,WAAa,KAAK,gBACzB,KAAK,WAAa,EAClB,KAAK,IAAG,IAEX,CAKD,KAAM,CACJ,MAAMkS,EAAkB,KAAK,UAAU,QAAQ,gBAC/C,QAASzQ,EAAI,EAAGA,EAAIyQ,EAAgB,OAAQzQ,IAAK,CAC/C,MAAMwG,EAAUiK,EAAgBzQ,CAAC,EAC7BwG,EAAQ,oBAAsBA,EAAQ,UAAYA,EAAQ,SAAW,IAAM,KAAK,MAAQA,EAAQ,SAAW,KAAK,UAClHA,EAAQ,SAAW,GACnBA,EAAQ,OAAM,EAEjB,CACF,CACD,SAAU,CACR,KAAK,UAAY,IAClB,CACH,EAEAgK,EAAiB,UAAY,CAC3B,KAAM,CACJjQ,EAAc,YACdA,EAAc,YACf,EACD,KAAM,WACR,EAEAiQ,EAAiB,eAAiB,CAKhC,gBAAiB,GAKjB,kBAAmB,KAKnB,iBAAkB,GAAK,GAKvB,uBAAwB,GAC1B,EACA,IAAIE,GAAkBF,EC1EtB,MAAMG,EAAc,MAAMA,EAAY,CAKpC,IAAI,aAAc,CAChB,OAAO,KAAK,QAAQ,OAAO,WAC5B,CACD,IAAI,YAAYxR,EAAO,CACrB,KAAK,QAAQ,OAAO,YAAcA,CACnC,CAED,IAAI,YAAa,CACf,OAAO,KAAK,QAAQ,OAAO,WAC5B,CACD,IAAI,WAAWA,EAAO,CACpB,KAAK,QAAQ,OAAO,OAClB,KAAK,QAAQ,OAAO,MACpB,KAAK,QAAQ,OAAO,OACpBA,CACN,CACG,CAKD,KAAKZ,EAAS,CACZA,EAAU,CACR,GAAGoS,GAAY,eACf,GAAGpS,CACT,EACQA,EAAQ,OACVqS,GAAYC,GAAQ,uDAAuD,EAC3EtS,EAAQ,OAASA,EAAQ,MAE3B,KAAK,OAAS,IAAIkJ,EAAU,EAAG,EAAGlJ,EAAQ,MAAOA,EAAQ,MAAM,EAC/D,KAAK,OAASA,EAAQ,QAAU8Q,GAAW,IAAG,EAAG,eACjD,KAAK,UAAY,CAAC,CAAC9Q,EAAQ,UAC3B,KAAK,QAAU+H,GAAiB,KAAK,OAAQ/H,CAAO,EACpD,KAAK,aAAe,IAAIgJ,EAAa,CACnC,cAAe,CAAC,KAAK,OAAO,EAC5B,MAAO,CAAC,CAAChJ,EAAQ,MACjB,OAAQ,EACd,CAAK,EACD,KAAK,QAAQ,OAAO,YAAcA,EAAQ,gBAAkB,EAC5D,KAAK,WAAaA,EAAQ,UAC3B,CAOD,OAAOuS,EAAoBC,EAAqB3J,EAAY,CAC1D,KAAK,QAAQ,OAAO,OAAO0J,EAAoBC,EAAqB3J,CAAU,EAC9E,KAAK,OAAO,MAAQ,KAAK,QAAQ,MAAM,MACvC,KAAK,OAAO,OAAS,KAAK,QAAQ,MAAM,MACzC,CAMD,QAAQ7I,EAAU,GAAO,EACJ,OAAOA,GAAY,UAAYA,EAAU,CAAC,CAACA,GAAS,aACrD,KAAK,OAAO,YAC5B,KAAK,OAAO,WAAW,YAAY,KAAK,MAAM,CAEjD,CACH,EAEAoS,EAAY,UAAY,CACtB,KAAM,CACJpQ,EAAc,YACdA,EAAc,aACdA,EAAc,YACf,EACD,KAAM,OACN,SAAU,CACZ,EAEAoQ,EAAY,eAAiB,CAK3B,MAAO,IAKP,OAAQ,IAKR,YAAa,GAKb,UAAW,EACb,EACA,IAAIK,GAAaL,EC1FZ,MAACM,GAAgB,CACpB3E,GACAyB,GACAyB,EACAwB,GACAtF,GACAgF,GACA/C,GACAH,GACA0D,GACAX,GACA9B,EACF,EACM0C,GAAoB,CACxB3E,GACAhM,GACAyL,GACA5C,GACAnI,GACAqB,GACAL,GACA6G,EACF", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41]}