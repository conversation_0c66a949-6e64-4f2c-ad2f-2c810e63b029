{"version": 3, "file": "stores.z8sZTwoA.js", "sources": ["../../../../../../../../node_modules/.pnpm/@sveltejs+kit@2.5.20_@sveltejs+vite-plugin-svelte@3.1.0_svelte@4.2.15_vite@5.2.11_@types+node_5qlfuvulcykb7n4xkfzb6ec34e/node_modules/@sveltejs/kit/src/runtime/app/stores.js"], "sourcesContent": ["import { getContext } from 'svelte';\nimport { BROWSER } from 'esm-env';\nimport { stores as browser_stores } from '../client/client.js';\n\n/**\n * A function that returns all of the contextual stores. On the server, this must be called during component initialization.\n * Only use this if you need to defer store subscription until after the component has mounted, for some reason.\n */\nexport const getStores = () => {\n\tconst stores = BROWSER ? browser_stores : getContext('__svelte__');\n\n\treturn {\n\t\t/** @type {typeof page} */\n\t\tpage: {\n\t\t\tsubscribe: stores.page.subscribe\n\t\t},\n\t\t/** @type {typeof navigating} */\n\t\tnavigating: {\n\t\t\tsubscribe: stores.navigating.subscribe\n\t\t},\n\t\t/** @type {typeof updated} */\n\t\tupdated: stores.updated\n\t};\n};\n\n/**\n * A readable store whose value contains page data.\n *\n * On the server, this store can only be subscribed to during component initialization. In the browser, it can be subscribed to at any time.\n *\n * @type {import('svelte/store').Readable<import('@sveltejs/kit').Page>}\n */\nexport const page = {\n\tsubscribe(fn) {\n\t\tconst store = __SVELTEKIT_DEV__ ? get_store('page') : getStores().page;\n\t\treturn store.subscribe(fn);\n\t}\n};\n\n/**\n * A readable store.\n * When navigating starts, its value is a `Navigation` object with `from`, `to`, `type` and (if `type === 'popstate'`) `delta` properties.\n * When navigating finishes, its value reverts to `null`.\n *\n * On the server, this store can only be subscribed to during component initialization. In the browser, it can be subscribed to at any time.\n * @type {import('svelte/store').Readable<import('@sveltejs/kit').Navigation | null>}\n */\nexport const navigating = {\n\tsubscribe(fn) {\n\t\tconst store = __SVELTEKIT_DEV__ ? get_store('navigating') : getStores().navigating;\n\t\treturn store.subscribe(fn);\n\t}\n};\n\n/**\n * A readable store whose initial value is `false`. If [`version.pollInterval`](https://kit.svelte.dev/docs/configuration#version) is a non-zero value, SvelteKit will poll for new versions of the app and update the store value to `true` when it detects one. `updated.check()` will force an immediate check, regardless of polling.\n *\n * On the server, this store can only be subscribed to during component initialization. In the browser, it can be subscribed to at any time.\n * @type {import('svelte/store').Readable<boolean> & { check(): Promise<boolean> }}\n */\nexport const updated = {\n\tsubscribe(fn) {\n\t\tconst store = __SVELTEKIT_DEV__ ? get_store('updated') : getStores().updated;\n\n\t\tif (BROWSER) {\n\t\t\tupdated.check = store.check;\n\t\t}\n\n\t\treturn store.subscribe(fn);\n\t},\n\tcheck: () => {\n\t\tthrow new Error(\n\t\t\tBROWSER\n\t\t\t\t? 'Cannot check updated store before subscribing'\n\t\t\t\t: 'Can only check updated store in browser'\n\t\t);\n\t}\n};\n\n/**\n * @template {keyof ReturnType<typeof getStores>} Name\n * @param {Name} name\n * @returns {ReturnType<typeof getStores>[Name]}\n */\nfunction get_store(name) {\n\ttry {\n\t\treturn getStores()[name];\n\t} catch {\n\t\tthrow new Error(\n\t\t\t`Cannot subscribe to '${name}' store on the server outside of a Svelte component, as it is bound to the current request via component context. This prevents state from leaking between users.` +\n\t\t\t\t'For more information, see https://kit.svelte.dev/docs/state-management#avoid-shared-state-on-the-server'\n\t\t);\n\t}\n}\n"], "names": ["getStores", "stores", "browser_stores", "page", "fn"], "mappings": "2EAQO,MAAMA,EAAY,IAAM,CAC9B,MAAMC,EAAmBC,EAElB,MAAA,CAEN,KAAM,CACL,UAAWD,EAAO,KAAK,SACxB,EAEA,WAAY,CACX,UAAWA,EAAO,WAAW,SAC9B,EAEA,QAASA,EAAO,OAAA,CAElB,EASaE,EAAO,CACnB,UAAUC,EAAI,CAEN,OAD+CJ,EAAY,EAAA,KACrD,UAAUI,CAAE,CAC1B,CACD", "x_google_ignoreList": [0]}