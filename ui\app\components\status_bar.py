"""
状态栏组件
显示当前状态、模型信息、token使用情况等
"""

from PyQt6.QtWidgets import QWidget, QHBoxLayout, QLabel, QFrame
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont

from core.utils.logger import get_logger

logger = get_logger(__name__)

class StatusBar(QFrame):
    """状态栏"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.setObjectName("status_bar")
        self.setMinimumHeight(45)

        # 设置状态栏样式
        self.setStyleSheet("""
            QFrame#status_bar {
                background: rgba(26, 31, 46, 0.9);
                border: 1px solid rgba(102, 126, 234, 0.2);
                border-radius: 8px;
                margin: 5px;
            }
            QLabel {
                background: transparent;
                color: #94a3b8;
                font-size: 12px;
                border: none;
                padding: 2px 4px;
            }
            QLabel#separator {
                color: rgba(148, 163, 184, 0.5);
            }
        """)

        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 8, 15, 8)  # 增加内边距
        layout.setSpacing(12)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 状态信息
        self.status_label = QLabel("就绪")
        self.status_label.setObjectName("status_label")
        layout.addWidget(self.status_label)
        
        # 分隔符
        separator1 = QLabel("|")
        separator1.setObjectName("separator")
        layout.addWidget(separator1)
        
        # 当前人设
        self.persona_label = QLabel("人设: 未选择")
        self.persona_label.setObjectName("persona_label")
        layout.addWidget(self.persona_label)
        
        # 分隔符
        separator2 = QLabel("|")
        separator2.setObjectName("separator")
        layout.addWidget(separator2)
        
        # 当前模型
        self.model_label = QLabel("模型: 未加载")
        self.model_label.setObjectName("model_label")
        layout.addWidget(self.model_label)
        
        # 弹簧，推到右边
        layout.addStretch()
        
        # Token使用情况
        self.token_label = QLabel("Tokens: 0")
        self.token_label.setObjectName("token_label")
        layout.addWidget(self.token_label)
        
        # 分隔符
        separator3 = QLabel("|")
        separator3.setObjectName("separator")
        layout.addWidget(separator3)
        
        # Token输出速度
        self.token_speed_label = QLabel("速度: 0.0 t/s")
        self.token_speed_label.setObjectName("token_speed_label")
        layout.addWidget(self.token_speed_label)
        
        # 分隔符
        separator4 = QLabel("|")
        separator4.setObjectName("separator")
        layout.addWidget(separator4)
        
        # 时间显示
        self.time_label = QLabel()
        self.time_label.setObjectName("time_label")
        layout.addWidget(self.time_label)
        
        # 设置时间更新定时器
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)  # 每秒更新
        self.update_time()
    
    def set_status(self, status: str):
        """设置状态"""
        self.status_label.setText(status)
    
    def set_persona(self, persona_name: str):
        """设置当前人设"""
        self.persona_label.setText(f"人设: {persona_name}")
    
    def set_model(self, model_name: str):
        """设置当前模型"""
        self.model_label.setText(f"模型: {model_name}")
    
    def set_tokens(self, token_count: int):
        """设置Token数量"""
        self.token_label.setText(f"Tokens: {token_count:,}")

    def set_token_speed(self, speed: float):
        """设置Token输出速度"""
        self.token_speed_label.setText(f"速度: {speed:.1f} t/s")
    
    def update_time(self):
        """更新时间显示"""
        from datetime import datetime
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(current_time)
    
    def apply_theme(self, theme):
        """应用主题"""
        self.setStyleSheet(theme.get_status_bar_stylesheet())
