import{F as s}from"./KHR_interactivity.DEAVS2UW.js";import{c as a,R as o}from"./declarationMapper.UBCwU7BT.js";import{R as e}from"./index.BoI39RQH.js";class r extends s{constructor(t){super(t),this.condition=this.registerDataInput("condition",a),this.onTrue=this.registerDataInput("onTrue",o),this.onFalse=this.registerDataInput("onFalse",o),this.output=this.registerDataOutput("output",o)}_updateOutputs(t){const i=this.condition.getValue(t);this.output.setValue(i?this.onTrue.getValue(t):this.onFalse.getValue(t),t)}getClassName(){return"FlowGraphConditionalBlock"}}e("FlowGraphConditionalBlock",r);export{r as FlowGraphConditionalDataBlock};
//# sourceMappingURL=flowGraphConditionalDataBlock.BpscsBV2.js.map
