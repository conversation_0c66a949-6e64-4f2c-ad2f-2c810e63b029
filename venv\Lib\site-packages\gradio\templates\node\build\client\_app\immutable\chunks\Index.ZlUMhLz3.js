import{SvelteComponent as Ee,init as Me,safe_not_equal as De,binding_callbacks as oe,bind as Ne,create_component as D,claim_component as N,mount_component as S,add_flush_callback as Se,transition_in as v,transition_out as I,destroy_component as V,assign as Ve,space as F,element as P,claim_space as G,claim_element as T,children as H,detach as w,attr as k,insert_hydration as B,get_spread_update as qe,get_spread_object as ze,group_outros as ue,check_outros as re,ensure_array_like as U,empty as W,src_url_equal as A,toggle_class as M,append_hydration as C,destroy_each as fe,text as Fe,claim_text as Ge,set_style as x,listen as j,set_data as Pe,run_all as Te}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{B as je,x as ee,S as Ce}from"./2.B2AoQPnG.js";import{B as Le}from"./BlockLabel.BTSz9r5s.js";import{E as Oe}from"./Empty.DwQ6nkN6.js";import{I as _e}from"./Image.CTVzPhL7.js";import{I as Ue}from"./IconButtonWrapper.D5aGR59h.js";import{F as We}from"./FullscreenButton.g_8wwg6y.js";function le(n,e,t){const l=n.slice();return l[33]=e[t],l[35]=t,l}function ne(n,e,t){const l=n.slice();return l[33]=e[t],l[35]=t,l}function Ae(n){var E;let e,t,l,s,o,i,r,m,b;t=new Ue({props:{$$slots:{default:[Je]},$$scope:{ctx:n}}});let h=U(n[15]?(E=n[15])==null?void 0:E.annotations:[]),_=[];for(let a=0;a<h.length;a+=1)_[a]=ie(ne(n,h,a));let c=n[6]&&n[15]&&ae(n);return{c(){e=P("div"),D(t.$$.fragment),l=F(),s=P("img"),i=F();for(let a=0;a<_.length;a+=1)_[a].c();r=F(),c&&c.c(),m=W(),this.h()},l(a){e=T(a,"DIV",{class:!0});var f=H(e);N(t.$$.fragment,f),l=G(f),s=T(f,"IMG",{class:!0,src:!0,alt:!0}),i=G(f);for(let d=0;d<_.length;d+=1)_[d].l(f);f.forEach(w),r=G(a),c&&c.l(a),m=W(),this.h()},h(){k(s,"class","base-image svelte-303fln"),A(s.src,o=n[15]?n[15].image.url:null)||k(s,"src",o),k(s,"alt","the base file that is annotated"),M(s,"fit-height",n[7]&&!n[18]),k(e,"class","image-container svelte-303fln")},m(a,f){B(a,e,f),S(t,e,null),C(e,l),C(e,s),C(e,i);for(let d=0;d<_.length;d+=1)_[d]&&_[d].m(e,null);n[26](e),B(a,r,f),c&&c.m(a,f),B(a,m,f),b=!0},p(a,f){var p;const d={};if(f[0]&278528|f[1]&64&&(d.$$scope={dirty:f,ctx:a}),t.$set(d),(!b||f[0]&32768&&!A(s.src,o=a[15]?a[15].image.url:null))&&k(s,"src",o),(!b||f[0]&262272)&&M(s,"fit-height",a[7]&&!a[18]),f[0]&360976){h=U(a[15]?(p=a[15])==null?void 0:p.annotations:[]);let g;for(g=0;g<h.length;g+=1){const q=ne(a,h,g);_[g]?_[g].p(q,f):(_[g]=ie(q),_[g].c(),_[g].m(e,null))}for(;g<_.length;g+=1)_[g].d(1);_.length=h.length}a[6]&&a[15]?c?c.p(a,f):(c=ae(a),c.c(),c.m(m.parentNode,m)):c&&(c.d(1),c=null)},i(a){b||(v(t.$$.fragment,a),b=!0)},o(a){I(t.$$.fragment,a),b=!1},d(a){a&&(w(e),w(r),w(m)),V(t),fe(_,a),n[26](null),c&&c.d(a)}}}function He(n){let e,t;return e=new Oe({props:{size:"large",unpadded_box:!0,$$slots:{default:[Ke]},$$scope:{ctx:n}}}),{c(){D(e.$$.fragment)},l(l){N(e.$$.fragment,l)},m(l,s){S(e,l,s),t=!0},p(l,s){const o={};s[1]&64&&(o.$$scope={dirty:s,ctx:l}),e.$set(o)},i(l){t||(v(e.$$.fragment,l),t=!0)},o(l){I(e.$$.fragment,l),t=!1},d(l){V(e,l)}}}function te(n){let e,t;return e=new We({props:{fullscreen:n[18]}}),e.$on("fullscreen",n[25]),{c(){D(e.$$.fragment)},l(l){N(e.$$.fragment,l)},m(l,s){S(e,l,s),t=!0},p(l,s){const o={};s[0]&262144&&(o.fullscreen=l[18]),e.$set(o)},i(l){t||(v(e.$$.fragment,l),t=!0)},o(l){I(e.$$.fragment,l),t=!1},d(l){V(e,l)}}}function Je(n){let e,t,l=n[14]&&te(n);return{c(){l&&l.c(),e=W()},l(s){l&&l.l(s),e=W()},m(s,o){l&&l.m(s,o),B(s,e,o),t=!0},p(s,o){s[14]?l?(l.p(s,o),o[0]&16384&&v(l,1)):(l=te(s),l.c(),v(l,1),l.m(e.parentNode,e)):l&&(ue(),I(l,1,1,()=>{l=null}),re())},i(s){t||(v(l),t=!0)},o(s){I(l),t=!1},d(s){s&&w(e),l&&l.d(s)}}}function ie(n){let e,t,l,s;return{c(){e=P("img"),this.h()},l(o){e=T(o,"IMG",{alt:!0,class:!0,src:!0,style:!0}),this.h()},h(){var o;k(e,"alt",t="segmentation mask identifying "+n[4]+" within the uploaded file"),k(e,"class","mask fit-height svelte-303fln"),A(e.src,l=n[33].image.url)||k(e,"src",l),k(e,"style",s=n[9]&&n[33].label in n[9]?null:`filter: hue-rotate(${Math.round(n[35]*360/((o=n[15])==null?void 0:o.annotations.length))}deg);`),M(e,"fit-height",!n[18]),M(e,"active",n[16]==n[33].label),M(e,"inactive",n[16]!=n[33].label&&n[16]!=null)},m(o,i){B(o,e,i)},p(o,i){var r;i[0]&16&&t!==(t="segmentation mask identifying "+o[4]+" within the uploaded file")&&k(e,"alt",t),i[0]&32768&&!A(e.src,l=o[33].image.url)&&k(e,"src",l),i[0]&33280&&s!==(s=o[9]&&o[33].label in o[9]?null:`filter: hue-rotate(${Math.round(o[35]*360/((r=o[15])==null?void 0:r.annotations.length))}deg);`)&&k(e,"style",s),i[0]&262144&&M(e,"fit-height",!o[18]),i[0]&98304&&M(e,"active",o[16]==o[33].label),i[0]&98304&&M(e,"inactive",o[16]!=o[33].label&&o[16]!=null)},d(o){o&&w(e)}}}function ae(n){let e,t=U(n[15].annotations),l=[];for(let s=0;s<t.length;s+=1)l[s]=se(le(n,t,s));return{c(){e=P("div");for(let s=0;s<l.length;s+=1)l[s].c();this.h()},l(s){e=T(s,"DIV",{class:!0});var o=H(e);for(let i=0;i<l.length;i+=1)l[i].l(o);o.forEach(w),this.h()},h(){k(e,"class","legend svelte-303fln")},m(s,o){B(s,e,o);for(let i=0;i<l.length;i+=1)l[i]&&l[i].m(e,null)},p(s,o){if(o[0]&3703296){t=U(s[15].annotations);let i;for(i=0;i<t.length;i+=1){const r=le(s,t,i);l[i]?l[i].p(r,o):(l[i]=se(r),l[i].c(),l[i].m(e,null))}for(;i<l.length;i+=1)l[i].d(1);l.length=t.length}},d(s){s&&w(e),fe(l,s)}}}function se(n){let e,t=n[33].label+"",l,s,o,i;function r(){return n[27](n[33])}function m(){return n[28](n[33])}function b(){return n[31](n[35],n[33])}return{c(){e=P("button"),l=Fe(t),s=F(),this.h()},l(h){e=T(h,"BUTTON",{class:!0,style:!0});var _=H(e);l=Ge(_,t),s=G(_),_.forEach(w),this.h()},h(){k(e,"class","legend-item svelte-303fln"),x(e,"background-color",n[9]&&n[33].label in n[9]?n[9][n[33].label]+"88":`hsla(${Math.round(n[35]*360/n[15].annotations.length)}, 100%, 50%, 0.3)`)},m(h,_){B(h,e,_),C(e,l),C(e,s),o||(i=[j(e,"mouseover",r),j(e,"focus",m),j(e,"mouseout",n[29]),j(e,"blur",n[30]),j(e,"click",b)],o=!0)},p(h,_){n=h,_[0]&32768&&t!==(t=n[33].label+"")&&Pe(l,t),_[0]&33280&&x(e,"background-color",n[9]&&n[33].label in n[9]?n[9][n[33].label]+"88":`hsla(${Math.round(n[35]*360/n[15].annotations.length)}, 100%, 50%, 0.3)`)},d(h){h&&w(e),o=!1,Te(i)}}}function Ke(n){let e,t;return e=new _e({}),{c(){D(e.$$.fragment)},l(l){N(e.$$.fragment,l)},m(l,s){S(e,l,s),t=!0},i(l){t||(v(e.$$.fragment,l),t=!0)},o(l){I(e.$$.fragment,l),t=!1},d(l){V(e,l)}}}function Qe(n){let e,t,l,s,o,i,r,m;const b=[{autoscroll:n[3].autoscroll},{i18n:n[3].i18n},n[13]];let h={};for(let a=0;a<b.length;a+=1)h=Ve(h,b[a]);e=new Ce({props:h}),l=new Le({props:{show_label:n[5],Icon:_e,label:n[4]||n[3].i18n("image.image")}});const _=[He,Ae],c=[];function E(a,f){return a[15]==null?0:1}return i=E(n),r=c[i]=_[i](n),{c(){D(e.$$.fragment),t=F(),D(l.$$.fragment),s=F(),o=P("div"),r.c(),this.h()},l(a){N(e.$$.fragment,a),t=G(a),N(l.$$.fragment,a),s=G(a),o=T(a,"DIV",{class:!0});var f=H(o);r.l(f),f.forEach(w),this.h()},h(){k(o,"class","container svelte-303fln")},m(a,f){S(e,a,f),B(a,t,f),S(l,a,f),B(a,s,f),B(a,o,f),c[i].m(o,null),m=!0},p(a,f){const d=f[0]&8200?qe(b,[f[0]&8&&{autoscroll:a[3].autoscroll},f[0]&8&&{i18n:a[3].i18n},f[0]&8192&&ze(a[13])]):{};e.$set(d);const p={};f[0]&32&&(p.show_label=a[5]),f[0]&24&&(p.label=a[4]||a[3].i18n("image.image")),l.$set(p);let g=i;i=E(a),i===g?c[i].p(a,f):(ue(),I(c[g],1,1,()=>{c[g]=null}),re(),r=c[i],r?r.p(a,f):(r=c[i]=_[i](a),r.c()),v(r,1),r.m(o,null))},i(a){m||(v(e.$$.fragment,a),v(l.$$.fragment,a),v(r),m=!0)},o(a){I(e.$$.fragment,a),I(l.$$.fragment,a),I(r),m=!1},d(a){a&&(w(t),w(s),w(o)),V(e,a),V(l,a),c[i].d()}}}function Re(n){let e,t,l;function s(i){n[32](i)}let o={visible:n[2],elem_id:n[0],elem_classes:n[1],padding:!1,height:n[7],width:n[8],allow_overflow:!1,container:n[10],scale:n[11],min_width:n[12],$$slots:{default:[Qe]},$$scope:{ctx:n}};return n[18]!==void 0&&(o.fullscreen=n[18]),e=new je({props:o}),oe.push(()=>Ne(e,"fullscreen",s)),{c(){D(e.$$.fragment)},l(i){N(e.$$.fragment,i)},m(i,r){S(e,i,r),l=!0},p(i,r){const m={};r[0]&4&&(m.visible=i[2]),r[0]&1&&(m.elem_id=i[0]),r[0]&2&&(m.elem_classes=i[1]),r[0]&128&&(m.height=i[7]),r[0]&256&&(m.width=i[8]),r[0]&1024&&(m.container=i[10]),r[0]&2048&&(m.scale=i[11]),r[0]&4096&&(m.min_width=i[12]),r[0]&516856|r[1]&64&&(m.$$scope={dirty:r,ctx:i}),!t&&r[0]&262144&&(t=!0,m.fullscreen=i[18],Se(()=>t=!1)),e.$set(m)},i(i){l||(v(e.$$.fragment,i),l=!0)},o(i){I(e.$$.fragment,i),l=!1},d(i){V(e,i)}}}function Xe(n,e,t){let{elem_id:l=""}=e,{elem_classes:s=[]}=e,{visible:o=!0}=e,{value:i=null}=e,r=null,m=null,{gradio:b}=e,{label:h=b.i18n("annotated_image.annotated_image")}=e,{show_label:_=!0}=e,{show_legend:c=!0}=e,{height:E}=e,{width:a}=e,{color_map:f}=e,{container:d=!0}=e,{scale:p=null}=e,{min_width:g=void 0}=e,q=null,{loading_status:Y}=e,{show_fullscreen_button:Z=!0}=e,J,L=!1,K=null;function Q(u){t(16,q=u)}function R(){t(16,q=null)}function y(u,O){b.dispatch("select",{value:h,index:u})}const me=({detail:u})=>{t(18,L=u)};function ce(u){oe[u?"unshift":"push"](()=>{J=u,t(17,J)})}const he=u=>Q(u.label),ge=u=>Q(u.label),be=()=>R(),de=()=>R(),ke=(u,O)=>y(u,O.label);function we(u){L=u,t(18,L)}return n.$$set=u=>{"elem_id"in u&&t(0,l=u.elem_id),"elem_classes"in u&&t(1,s=u.elem_classes),"visible"in u&&t(2,o=u.visible),"value"in u&&t(22,i=u.value),"gradio"in u&&t(3,b=u.gradio),"label"in u&&t(4,h=u.label),"show_label"in u&&t(5,_=u.show_label),"show_legend"in u&&t(6,c=u.show_legend),"height"in u&&t(7,E=u.height),"width"in u&&t(8,a=u.width),"color_map"in u&&t(9,f=u.color_map),"container"in u&&t(10,d=u.container),"scale"in u&&t(11,p=u.scale),"min_width"in u&&t(12,g=u.min_width),"loading_status"in u&&t(13,Y=u.loading_status),"show_fullscreen_button"in u&&t(14,Z=u.show_fullscreen_button)},n.$$.update=()=>{if(n.$$.dirty[0]&29360136)if(i!==r&&(t(23,r=i),b.dispatch("change")),i){const u={image:i.image,annotations:i.annotations.map(z=>({image:z.image,label:z.label}))};t(15,m=u);const O=ee(u.image.url),ve=Promise.all(u.annotations.map(z=>ee(z.image.url))),X=Promise.all([O,ve]);t(24,K=X),X.then(([z,Ie])=>{if(K!==X)return;const Be={image:{...u.image,url:z??void 0},annotations:u.annotations.map(($,pe)=>({...$,image:{...$.image,url:Ie[pe]??void 0}}))};t(15,m=Be)})}else t(15,m=null)},[l,s,o,b,h,_,c,E,a,f,d,p,g,Y,Z,m,q,J,L,Q,R,y,i,r,K,me,ce,he,ge,be,de,ke,we]}class tl extends Ee{constructor(e){super(),Me(this,e,Xe,Re,De,{elem_id:0,elem_classes:1,visible:2,value:22,gradio:3,label:4,show_label:5,show_legend:6,height:7,width:8,color_map:9,container:10,scale:11,min_width:12,loading_status:13,show_fullscreen_button:14},null,[-1,-1])}}export{tl as default};
//# sourceMappingURL=Index.ZlUMhLz3.js.map
