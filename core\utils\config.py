"""
配置管理器
统一管理应用配置
"""

import os
import yaml
from typing import Any, Dict, Optional
from pathlib import Path
import threading

from .logger import get_logger

logger = get_logger(__name__)

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "config/settings/app_config.yaml"):
        self.config_path = config_path
        self.config: Dict[str, Any] = {}
        self.lock = threading.Lock()
        self.load_config()
    
    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            with self.lock:
                if not os.path.exists(self.config_path):
                    logger.warning(f"配置文件不存在: {self.config_path}")
                    self._create_default_config()
                
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = yaml.safe_load(f) or {}
                
                logger.info("配置文件加载成功")
                return True
                
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            self._create_default_config()
            return False
    
    def _create_default_config(self):
        """创建默认配置"""
        default_config = {
            'app': {
                'name': 'Reverie Agents',
                'version': '1.0.0',
                'debug': False,
                'log_level': 'INFO'
            },
            'models': {
                'llm': {
                    'default_model': 'lucy-128k',
                    'model_path': 'models/llm',
                    'max_context_length': 128000,
                    'temperature': 0.7,
                    'top_p': 0.9,
                    'top_k': 40,
                    'repetition_penalty': 1.1,
                    'max_new_tokens': 2048
                },
                't2i': {
                    'default_model': 'flux-dev',
                    'model_path': 'models/t2i',
                    'default_steps': 20,
                    'default_guidance': 7.5,
                    'default_width': 1024,
                    'default_height': 1024
                }
            },
            'ui': {
                'theme': 'dark',
                'font_family': 'Microsoft YaHei UI',
                'font_size': 12,
                'window_width': 1200,
                'window_height': 800
            },
            'memory': {
                'storage_path': 'memory/storage',
                'max_memory_size': 10000,
                'context_window': 8000,
                'enable_long_term_memory': True,
                'memory_compression': True
            },
            'search': {
                'engine': 'duckduckgo',
                'max_results': 5,
                'auto_search': True,
                'search_timeout': 30
            },
            'personas': {
                'config_path': 'personas/configs',
                'default_persona': 'assistant',
                'enable_nsfw': True
            },
            'logging': {
                'log_path': 'logs',
                'max_log_size': '10MB',
                'backup_count': 5
            },
            'network': {
                'proxy': None,
                'timeout': 30,
                'user_agent': 'Reverie-Agents/1.0.0'
            },
            'mirrors': {
                'huggingface': 'https://hf-mirror.com'
            },
            'security': {
                'enable_content_filter': False,
                'max_image_size': '10MB'
            }
        }
        
        self.config = default_config
        self.save_config()
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值（支持点号分隔的嵌套键）"""
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> bool:
        """设置配置值（支持点号分隔的嵌套键）"""
        keys = key.split('.')
        config = self.config
        
        try:
            with self.lock:
                # 导航到目标位置
                for k in keys[:-1]:
                    if k not in config:
                        config[k] = {}
                    config = config[k]
                
                # 设置值
                config[keys[-1]] = value
                return True
                
        except Exception as e:
            logger.error(f"设置配置失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """保存配置到文件"""
        try:
            with self.lock:
                # 确保目录存在
                os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
                
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    yaml.dump(self.config, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
                
                logger.info("配置文件保存成功")
                return True
                
        except Exception as e:
            logger.error(f"配置文件保存失败: {e}")
            return False
    
    def reload_config(self) -> bool:
        """重新加载配置"""
        return self.load_config()
    
    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        with self.lock:
            return self.config.copy()
    
    def update_config(self, new_config: Dict[str, Any]) -> bool:
        """更新配置"""
        try:
            with self.lock:
                self._deep_update(self.config, new_config)
                return self.save_config()
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return False
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value

# 全局配置管理器实例
config_manager = ConfigManager()
