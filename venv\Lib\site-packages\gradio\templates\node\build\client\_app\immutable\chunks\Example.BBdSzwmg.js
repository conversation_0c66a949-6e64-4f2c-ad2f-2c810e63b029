import{SvelteComponent as m,init as u,safe_not_equal as d,element as o,HtmlTagHydration as h,claim_element as g,children as _,claim_html_tag as v,detach as c,attr as y,toggle_class as s,insert_hydration as b,noop as r}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function E(n){let e,a;return{c(){e=o("div"),a=new h(!1),this.h()},l(l){e=g(l,"DIV",{class:!0});var t=_(e);a=v(t,!1),t.forEach(c),this.h()},h(){a.a=null,y(e,"class","prose svelte-zvfedn"),s(e,"table",n[1]==="table"),s(e,"gallery",n[1]==="gallery"),s(e,"selected",n[2])},m(l,t){b(l,e,t),a.m(n[0],e)},p(l,[t]){t&1&&a.p(l[0]),t&2&&s(e,"table",l[1]==="table"),t&2&&s(e,"gallery",l[1]==="gallery"),t&4&&s(e,"selected",l[2])},i:r,o:r,d(l){l&&c(e)}}}function H(n,e,a){let{value:l}=e,{type:t}=e,{selected:f=!1}=e;return n.$$set=i=>{"value"in i&&a(0,l=i.value),"type"in i&&a(1,t=i.type),"selected"in i&&a(2,f=i.selected)},[l,t,f]}class z extends m{constructor(e){super(),u(this,e,H,E,d,{value:0,type:1,selected:2})}}export{z as default};
//# sourceMappingURL=Example.BBdSzwmg.js.map
