import{B as lo}from"./Block-CJdXVpa7.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";/* empty css                                                        */import{C as Le,w as io}from"./index-B7J2Z2jS.js";import{d as el}from"./index-tFQomdd2.js";import{d as so}from"./dsv-DB8NKgIY.js";import{d as ln}from"./index-CnqicUFC.js";import{a as ro}from"./Upload-D4uXt6Nz.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import{M as oo}from"./MarkdownCode-CkSMBRHJ.js";import"./StreamingBar.svelte_svelte_type_style_lang-DyfAPhwM.js";import{C as ao}from"./Checkbox-CjOIpf6b.js";import{C as ql}from"./Check-CEkiXcyC.js";import{D as _o}from"./DropdownArrow-DYWFcSFn.js";import{C as uo}from"./Copy-CxQ9EyK2.js";import{F as fo}from"./FullscreenButton-jgOGhOHz.js";import{S as co}from"./index-B1FJGuzG.js";import ho from"./Index-BLHCXS_b.js";import{default as _h}from"./Example-CqPGqNav.js";import"./prism-python-MMh3z1bK.js";import"./svelte/svelte.js";/* empty css                                             */import"./IconButton-C_HS7fTi.js";import"./Clear-By3xiIwg.js";import"./ImagePreview-lAInKGA8.js";import"./utils-BsGrhMNe.js";import"./BlockLabel-3KxTaaiM.js";import"./Empty-ZqppqzTN.js";import"./ShareButton-BuIiIMKb.js";import"./Community-Dw1micSV.js";import"./Download-DVtk-Jv3.js";import"./Image-Bsh8Umrh.js";import"./IconButtonWrapper--EIOWuEM.js";import"./utils-Gtzs_Zla.js";import"./Image-CnqB5dbD.js";import"./file-url-DoxvUUVV.js";import"./DownloadLink-QIttOhoR.js";/* empty css                                                   */import"./ImageUploader-CGsNpf2-.js";import"./SelectSource-CVJruJ8L.js";import"./Square-oAGqOwsh.js";import"./index-CEGzm7H5.js";import"./StreamingBar-JqJtcvLZ.js";import"./UploadText-DtO3Yo94.js";import"./Example-CC8yxxGn.js";/* empty css                                              */function mn(n,e,t){if(!e.length)return"none";const l=e.find(i=>{const r=i.col;return r<0||r>=t.length?!1:t[r]===n});return l?l.direction:"none"}function mo(n,e){if(!n||!n.length||!n[0])return[];if(e.length>0){const t=[...Array(n.length)].map((l,i)=>i);return t.sort((l,i)=>{const r=n[l],s=n[i];for(const{col:o,direction:_}of e){if(!r||!s||o<0||o>=r.length||o>=s.length||!r[o]||!s[o])continue;const f=r[o].value,a=s[o].value,u=f<a?-1:f>a?1:0;if(u!==0)return _==="asc"?u:-u}return 0}),t}return[...Array(n.length)].map((t,l)=>l)}function go(n,e,t,l,i,r){let s=null;i&&i[0]in n&&i[1]in n[i[0]]&&(s=n[i[0]][i[1]].id),Js(n,e,t,l);let o=i;if(s){const[_,f]=r(s,n);o=[_,f]}return{data:n,selected:o}}function bo(n,e){if(!n||!n.length||!n[0])return[];let t=[...Array(n.length)].map((l,i)=>i);return e.length>0?(e.forEach(l=>{if(l.datatype==="string")switch(l.filter){case"Contains":t=t.filter(i=>n[i][l.col]?.value.toString().includes(l.value));break;case"Does not contain":t=t.filter(i=>!n[i][l.col]?.value.toString().includes(l.value));break;case"Starts with":t=t.filter(i=>n[i][l.col]?.value.toString().startsWith(l.value));break;case"Ends with":t=t.filter(i=>n[i][l.col]?.value.toString().endsWith(l.value));break;case"Is":t=t.filter(i=>n[i][l.col]?.value.toString()===l.value);break;case"Is not":t=t.filter(i=>n[i][l.col]?.value.toString()!==l.value);break;case"Is empty":t=t.filter(i=>n[i][l.col]?.value.toString()==="");break;case"Is not empty":t=t.filter(i=>n[i][l.col]?.value.toString()!=="");break}else if(l.datatype==="number")switch(l.filter){case"=":t=t.filter(i=>!isNaN(Number(n[i][l.col]?.value))&&!isNaN(Number(l.value))?Number(n[i][l.col]?.value)===Number(l.value):!1);break;case"≠":t=t.filter(i=>!isNaN(Number(n[i][l.col]?.value))&&!isNaN(Number(l.value))?Number(n[i][l.col]?.value)!==Number(l.value):!1);break;case">":t=t.filter(i=>!isNaN(Number(n[i][l.col]?.value))&&!isNaN(Number(l.value))?Number(n[i][l.col]?.value)>Number(l.value):!1);break;case"<":t=t.filter(i=>!isNaN(Number(n[i][l.col]?.value))&&!isNaN(Number(l.value))?Number(n[i][l.col]?.value)<Number(l.value):!1);break;case"≥":t=t.filter(i=>!isNaN(Number(n[i][l.col]?.value))&&!isNaN(Number(l.value))?Number(n[i][l.col]?.value)>=Number(l.value):!1);break;case"≤":t=t.filter(i=>!isNaN(Number(n[i][l.col]?.value))&&!isNaN(Number(l.value))?Number(n[i][l.col]?.value)<=Number(l.value):!1);break;case"Is empty":t=t.filter(i=>n[i][l.col]?.value.toString()==="");break;case"Is not empty":t=t.filter(i=>isNaN(Number(n[i][l.col]?.value))?!1:n[i][l.col]?.value.toString()!=="");break}}),t):[...Array(n.length)].map((l,i)=>i)}function wo(n,e,t,l,i,r,s,o,_){let f=null;i&&i[0]in n&&i[1]in n[i[0]]&&(f=n[i[0]][i[1]].id),po(n,e,t,l,s,o,_);let a=i;if(f){const[u,c]=r(f,n);a=[u,c]}return{data:n,selected:a}}function ko(n){if(!n||!n.length)return[];let e=n[0].slice();for(let t=0;t<n.length;t++)for(let l=0;l<n[t].length;l++)`${e[l].value}`.length<`${n[t][l].value}`.length&&(e[l]=n[t][l]);return e}function Js(n,e,t,l){if(!l.length||!n||!n.length)return;const i=mo(n,l),r=i.map(s=>n[s]);if(n.splice(0,n.length,...r),e){const s=i.map(o=>e[o]);e.splice(0,e.length,...s)}if(t){const s=i.map(o=>t[o]);t.splice(0,t.length,...s)}}function po(n,e,t,l,i,r,s){const o=i??n,_=r??e,f=s??t;if(!l.length){n.splice(0,n.length,...o.map(c=>[...c])),e&&_&&e.splice(0,e.length,..._.map(c=>[...c])),t&&f&&t.splice(0,t.length,...f.map(c=>[...c]));return}if(!n||!n.length)return;const a=bo(o,l),u=a.map(c=>o[c]);if(n.splice(0,n.length,...u),e&&_){const c=a.map(g=>_[g]);e.splice(0,e.length,...c)}if(t&&f){const c=a.map(g=>f[g]);t.splice(0,t.length,...c)}}async function Fs(n,e){if(!n||!n.length)return;const l=(e||n.flatMap((o,_)=>o.map((f,a)=>[_,a]))).reduce((o,[_,f])=>{o[_]=o[_]||{};const a=String(n[_][f].value);return o[_][f]=a.includes(",")||a.includes('"')||a.includes(`
`)?`"${a.replace(/"/g,'""')}"`:a,o},{}),i=Object.keys(l).sort((o,_)=>+o-+_);if(!i.length)return;const r=Object.keys(l[i[0]]).sort((o,_)=>+o-+_),s=i.map(o=>r.map(_=>l[o][_]||"").join(",")).join(`
`);try{await navigator.clipboard.writeText(s)}catch(o){throw new Error("Failed to copy to clipboard: "+o.message)}}function vo(n,e){return e.filter(t);function t(l){var i=-1;return n.split(`
`).every(r);function r(s){if(!s)return!0;var o=s.split(l).length;return i<0&&(i=o),i===o&&o>1}}}function yo(n){const e=atob(n.split(",")[1]),t=n.split(",")[0].split(":")[1].split(";")[0],l=new ArrayBuffer(e.length),i=new Uint8Array(l);for(let r=0;r<e.length;r++)i[r]=e.charCodeAt(r);return new Blob([l],{type:t})}function Co(n,e,t){const l=yo(n),i=new FileReader;i.addEventListener("loadend",r=>{if(!r?.target?.result||typeof r.target.result!="string")return;const[s]=vo(r.target.result,[",","	"]),[o,..._]=so(s).parseRows(r.target.result);e(o),t(_)}),i.readAsText(l)}function So(n,e){const[t,l]=n;return e.some(([i,r])=>i===t&&r===l)}function No(n,e){const[t,l]=n;if(!e.some(([_,f])=>_===t&&f===l))return"";const i=e.some(([_,f])=>_===t-1&&f===l),r=e.some(([_,f])=>_===t+1&&f===l),s=e.some(([_,f])=>_===t&&f===l-1),o=e.some(([_,f])=>_===t&&f===l+1);return`cell-selected${i?" no-top":""}${r?" no-bottom":""}${s?" no-left":""}${o?" no-right":""}`}function Ml(n,e){const[t,l]=n,[i,r]=e,s=Math.min(t,i),o=Math.max(t,i),_=Math.min(l,r),f=Math.max(l,r),a=[];a.push(n);for(let u=s;u<=o;u++)for(let c=_;c<=f;c++)u===t&&c===l||a.push([u,c]);return a}function qo(n,e,t){if(t.shiftKey&&e.length>0)return Ml(e[e.length-1],n);if(t.metaKey||t.ctrlKey){const l=([r,s])=>r===n[0]&&s===n[1],i=e.findIndex(l);return i===-1?[...e,n]:e.filter((r,s)=>s!==i)}return[n]}function Mo(n,e){const t=n.map(l=>[...l]);return e.forEach(([l,i])=>{t[l]&&t[l][i]&&(t[l][i]={...t[l][i],value:""})}),t}function Ao(n,e,t){const[l,i]=n;return t&&e.length===1&&e[0][0]===l&&e[0][1]===i}function Eo(n,e,t){const[l,i]=n,r=t?-1:1;if(e[l]?.[i+r])return[l,i+r];const s=l+(r>0?1:0),o=l+(r<0?-1:0);return r>0&&e[s]?.[0]?[s,0]:r<0&&e[o]?.[e[0].length-1]?[o,e[0].length-1]:!1}function Lo(n,e,t){const l=n.key,i={ArrowRight:[0,1],ArrowLeft:[0,-1],ArrowDown:[1,0],ArrowUp:[-1,0]}[l];let r,s;if(n.metaKey||n.ctrlKey)if(l==="ArrowRight")r=e[0],s=t[0].length-1;else if(l==="ArrowLeft")r=e[0],s=0;else if(l==="ArrowDown")r=t.length-1,s=e[1];else if(l==="ArrowUp")r=0,s=e[1];else return!1;else r=e[0]+i[0],s=e[1]+i[1];return r<0&&s<=0?!1:t[r]?.[s]?[r,s]:!1}function ti(n,e){return e.reduce((t,l,i)=>{const r=l.reduce((s,o,_)=>n===o.id?_:s,-1);return r===-1?t:[i,r]},[-1,-1])}function jo(n,e){const[t]=n.composedPath();return!e.contains(t)}function Bo(n,e,t,l,i){const[r,s]=n;if(!e[r]?.[s])return{col_pos:"0px",row_pos:void 0};const o=e[r][s].id,_=t[o]?.cell;if(!_)return{col_pos:"0px",row_pos:void 0};const f=_.getBoundingClientRect(),a=i.getBoundingClientRect(),u=`${f.left-a.left+f.width/2}px`,c=`${f.top-a.top+f.height/2}px`;return{col_pos:u,row_pos:c}}const{getContext:sh,setContext:ni}=window.__gradio__svelte__internal,{tick:li}=window.__gradio__svelte__internal,Do=Symbol("dataframe");function zo(n,e){const t=s=>n.update(o=>({...o,...s(o)})),l=(s,o,_)=>{const f=s[0]?.length?Array(s[0].length).fill(null).map(()=>({value:"",id:o()})):[{value:"",id:o()}],a=[...s];return _!==void 0?a.splice(_,0,f):a.push(f),a},i=(s,o,_,f)=>{const a=e.headers?[...o.map(c=>e.headers[o.indexOf(c)].value)]:[...o,`Header ${o.length+1}`],u=s.map(c=>[...c,{value:"",id:_()}]);return f!==void 0&&(a.splice(f,0,a.pop()),u.forEach(c=>c.splice(f,0,c.pop()))),{data:u,headers:a}},r=(s,o)=>{s&&o&&o.splice(0,o.length,...JSON.parse(JSON.stringify(s)))};return{handle_search:s=>t(o=>({current_search_query:s})),handle_sort:(s,o)=>t(_=>{const f=_.sort_state.sort_columns.filter(u=>u.col!==s);_.sort_state.sort_columns.some(u=>u.col===s&&u.direction===o)||f.push({col:s,direction:o});const a=_.sort_state.initial_data||(e.data&&f.length>0?{data:JSON.parse(JSON.stringify(e.data)),display_value:e.display_value?JSON.parse(JSON.stringify(e.display_value)):null,styling:e.styling?JSON.parse(JSON.stringify(e.styling)):null}:null);return{sort_state:{..._.sort_state,sort_columns:f.slice(-3),initial_data:a}}}),handle_filter:(s,o,_,f)=>t(a=>{const u=a.filter_state.filter_columns.some(g=>g.col===s)?a.filter_state.filter_columns.filter(g=>g.col!==s):[...a.filter_state.filter_columns,{col:s,datatype:o,filter:_,value:f}],c=a.filter_state.initial_data||(e.data&&u.length>0?{data:JSON.parse(JSON.stringify(e.data)),display_value:e.display_value?JSON.parse(JSON.stringify(e.display_value)):null,styling:e.styling?JSON.parse(JSON.stringify(e.styling)):null}:null);return{filter_state:{...a.filter_state,filter_columns:u,initial_data:c}}}),get_sort_status:(s,o)=>{const f=Le(n).sort_state.sort_columns.find(a=>o[a.col]===s);return f?f.direction:"none"},sort_data:(s,o,_)=>{const{sort_state:{sort_columns:f}}=Le(n);f.length&&Js(s,o,_,f)},update_row_order:s=>t(o=>({sort_state:{...o.sort_state,row_order:o.sort_state.sort_columns.length&&s[0]?[...Array(s.length)].map((_,f)=>f).sort((_,f)=>{for(const{col:a,direction:u}of o.sort_state.sort_columns){const c=(s[_]?.[a]?.value??"")<(s[f]?.[a]?.value??"")?-1:1;if(c)return u==="asc"?c:-c}return 0}):[...Array(s.length)].map((_,f)=>f)}})),filter_data:s=>{const o=Le(n).current_search_query?.toLowerCase();return o?s.filter(_=>_.some(f=>String(f?.value).toLowerCase().includes(o))):s},add_row:l,add_col:i,add_row_at:(s,o,_,f)=>l(s,f,_==="above"?o:o+1),add_col_at:(s,o,_,f,a)=>i(s,o,a,f==="left"?_:_+1),delete_row:(s,o)=>s.length>1?s.filter((_,f)=>f!==o):s,delete_col:(s,o,_)=>o.length>1?{data:s.map(f=>f.filter((a,u)=>u!==_)),headers:o.filter((f,a)=>a!==_)}:{data:s,headers:o},delete_row_at:(s,o)=>s.length>1?[...s.slice(0,o),...s.slice(o+1)]:s,delete_col_at:(s,o,_)=>o.length>1?{data:s.map(f=>[...f.slice(0,_),...f.slice(_+1)]),headers:[...o.slice(0,_),...o.slice(_+1)]}:{data:s,headers:o},trigger_change:async(s,o,_,f,a,u)=>{if(Le(n).current_search_query)return;const g=o.map(v=>v.value),L=s.map(v=>v.map(q=>String(q.value)));(!el(L,_)||!el(g,f))&&(el(g,f)||t(v=>({sort_state:{sort_columns:[],row_order:[],initial_data:null},filter_state:{filter_columns:[],initial_data:null}})),u("change",{data:s.map(v=>v.map(q=>q.value)),headers:g,metadata:null}),a||u("input"))},reset_sort_state:()=>t(s=>{if(s.sort_state.initial_data&&e.data){const o=s.sort_state.initial_data;r(o.data,e.data),r(o.display_value,e.display_value),r(o.styling,e.styling)}return{sort_state:{sort_columns:[],row_order:[],initial_data:null}}}),reset_filter_state:()=>t(s=>{if(s.filter_state.initial_data&&e.data){const o=s.filter_state.initial_data;r(o.data,e.data),r(o.display_value,e.display_value),r(o.styling,e.styling)}return{filter_state:{filter_columns:[],initial_data:null}}}),set_active_cell_menu:s=>t(o=>({ui_state:{...o.ui_state,active_cell_menu:s}})),set_active_header_menu:s=>t(o=>({ui_state:{...o.ui_state,active_header_menu:s}})),set_selected_cells:s=>t(o=>({ui_state:{...o.ui_state,selected_cells:s}})),set_selected:s=>t(o=>({ui_state:{...o.ui_state,selected:s}})),set_editing:s=>t(o=>({ui_state:{...o.ui_state,editing:s}})),clear_ui_state:()=>t(s=>({ui_state:{active_cell_menu:null,active_header_menu:null,selected_cells:[],selected:!1,editing:!1,header_edit:!1,selected_header:!1,active_button:null,copy_flash:!1}})),set_header_edit:s=>t(o=>({ui_state:{...o.ui_state,selected_cells:[],selected_header:s,header_edit:s}})),set_selected_header:s=>t(o=>({ui_state:{...o.ui_state,selected_header:s,selected:!1,selected_cells:[]}})),handle_header_click:(s,o)=>t(_=>({ui_state:{..._.ui_state,active_cell_menu:null,active_header_menu:null,selected:!1,selected_cells:[],selected_header:s,header_edit:o?s:!1}})),end_header_edit:s=>{["Escape","Enter","Tab"].includes(s)&&t(o=>({ui_state:{...o.ui_state,selected:!1,header_edit:!1}}))},get_selected_cells:()=>Le(n).ui_state.selected_cells,get_active_cell_menu:()=>Le(n).ui_state.active_cell_menu,get_active_button:()=>Le(n).ui_state.active_button,set_active_button:s=>t(o=>({ui_state:{...o.ui_state,active_button:s}})),set_copy_flash:s=>t(o=>({ui_state:{...o.ui_state,copy_flash:s}})),handle_cell_click:(s,o,_)=>{s.preventDefault(),s.stopPropagation();const f=Le(n);if(f.config.show_row_numbers&&_===-1)return;let a=o;if(f.current_search_query&&e.data){const c=[];e.data.forEach((g,L)=>{g.some(v=>String(v?.value).toLowerCase().includes(f.current_search_query?.toLowerCase()||""))&&c.push(L)}),a=c[o]??o}const u=qo([a,_],f.ui_state.selected_cells,s);t(c=>({ui_state:{...c.ui_state,active_cell_menu:null,active_header_menu:null,selected_header:!1,header_edit:!1,selected_cells:u,selected:u[0]}})),f.config.editable&&u.length===1?(t(c=>({ui_state:{...c.ui_state,editing:[a,_]}})),li().then(()=>e.els[e.data[a][_].id]?.input?.focus())):li().then(()=>{e.parent_element&&e.parent_element.focus()}),e.dispatch?.("select",{index:[a,_],col_value:e.get_column(_),row_value:e.get_row(a),value:e.get_data_at(a,_)})},toggle_cell_menu:(s,o,_)=>{s.stopPropagation();const f=Le(n).ui_state.active_cell_menu;if(f?.row===o&&f.col===_)t(a=>({ui_state:{...a.ui_state,active_cell_menu:null}}));else{const a=s.target.closest("td");if(a){const u=a.getBoundingClientRect();t(c=>({ui_state:{...c.ui_state,active_cell_menu:{row:o,col:_,x:u.right,y:u.bottom}}}))}}},toggle_cell_button:(s,o)=>{const _=Le(n).ui_state.active_button,f=_?.type==="cell"&&_.row===s&&_.col===o?null:{type:"cell",row:s,col:o};t(a=>({ui_state:{...a.ui_state,active_button:f}}))},handle_select_column:s=>{if(!e.data)return;const o=e.data.map((_,f)=>[f,s]);t(_=>({ui_state:{..._.ui_state,selected_cells:o,selected:o[0],editing:!1}})),setTimeout(()=>e.parent_element?.focus(),0)},handle_select_row:s=>{if(!e.data||!e.data[0])return;const o=e.data[0].map((_,f)=>[s,f]);t(_=>({ui_state:{..._.ui_state,selected_cells:o,selected:o[0],editing:!1}})),setTimeout(()=>e.parent_element?.focus(),0)},get_next_cell_coordinates:Eo,get_range_selection:Ml,move_cursor:Lo}}function To(n){const e=io({config:n,current_search_query:null,sort_state:{sort_columns:[],row_order:[],initial_data:null},filter_state:{filter_columns:[],initial_data:null},ui_state:{active_cell_menu:null,active_header_menu:null,selected_cells:[],selected:!1,editing:!1,header_edit:!1,selected_header:!1,active_button:null,copy_flash:!1}}),t={state:e,actions:null};t.actions=zo(e,t);const l=Symbol(`dataframe_${Math.random().toString(36).substring(2)}`);return ni(l,t),ni(Do,{instance_id:l,context:t}),t}const{SvelteComponent:Ho,append:tl,attr:et,detach:Oo,element:ii,flush:nl,init:Io,insert:Ro,listen:Po,noop:si,null_to_empty:ri,safe_not_equal:Jo,stop_propagation:Fo,svg_element:oi}=window.__gradio__svelte__internal;function Ko(n){let e,t,l,i,r,s,o,_,f;return{c(){e=ii("button"),t=ii("span"),l=oi("svg"),i=oi("path"),et(i,"d","m16.707 13.293-4-4a1 1 0 0 0-1.414 0l-4 4A1 1 0 0 0 8 15h8a1 1 0 0 0 .707-1.707z"),et(i,"data-name",n[3]),et(l,"xmlns","http://www.w3.org/2000/svg"),et(l,"viewBox","0 0 24 24"),et(l,"class","svelte-1mp8yw1"),et(t,"class",r=ri(n[3])+" svelte-1mp8yw1"),et(e,"class",s="selection-button selection-button-"+n[0]+" "+(n[2]?`move-${n[3]}`:"")+" svelte-1mp8yw1"),et(e,"aria-label",o=`Select ${n[0]}`)},m(a,u){Ro(a,e,u),tl(e,t),tl(t,l),tl(l,i),_||(f=Po(e,"click",Fo(n[5])),_=!0)},p(a,[u]){u&8&&et(i,"data-name",a[3]),u&8&&r!==(r=ri(a[3])+" svelte-1mp8yw1")&&et(t,"class",r),u&13&&s!==(s="selection-button selection-button-"+a[0]+" "+(a[2]?`move-${a[3]}`:"")+" svelte-1mp8yw1")&&et(e,"class",s),u&1&&o!==(o=`Select ${a[0]}`)&&et(e,"aria-label",o)},i:si,o:si,d(a){a&&Oo(e),_=!1,f()}}}function Wo(n,e,t){let l,i,{position:r}=e,{coords:s}=e,{on_click:o=null}=e;const _=()=>o&&o();return n.$$set=f=>{"position"in f&&t(0,r=f.position),"coords"in f&&t(4,s=f.coords),"on_click"in f&&t(1,o=f.on_click)},n.$$.update=()=>{n.$$.dirty&17&&t(2,l=r==="column"?s[0]===0:s[1]===0),n.$$.dirty&5&&t(3,i=r==="column"?l?"down":"up":l?"right":"left")},[r,o,l,i,s,_]}class ai extends Ho{constructor(e){super(),Io(this,e,Wo,Ko,Jo,{position:0,coords:4,on_click:1})}get position(){return this.$$.ctx[0]}set position(e){this.$$set({position:e}),nl()}get coords(){return this.$$.ctx[4]}set coords(e){this.$$set({coords:e}),nl()}get on_click(){return this.$$.ctx[1]}set on_click(e){this.$$set({on_click:e}),nl()}}const{SvelteComponent:Uo,add_flush_callback:Vo,attr:ll,bind:Yo,binding_callbacks:Xo,create_component:Go,destroy_component:Qo,detach:Zo,element:$o,flush:il,init:xo,insert:ea,mount_component:ta,safe_not_equal:na,transition_in:la,transition_out:ia}=window.__gradio__svelte__internal;function sa(n){let e,t,l,i;function r(o){n[5](o)}let s={label:"",interactive:n[0]};return n[1]!==void 0&&(s.value=n[1]),t=new ao({props:s}),Xo.push(()=>Yo(t,"value",r)),t.$on("change",n[2]),{c(){e=$o("div"),Go(t.$$.fragment),ll(e,"class","bool-cell svelte-1svo4lb"),ll(e,"role","button"),ll(e,"tabindex","-1")},m(o,_){ea(o,e,_),ta(t,e,null),i=!0},p(o,[_]){const f={};_&1&&(f.interactive=o[0]),!l&&_&2&&(l=!0,f.value=o[1],Vo(()=>l=!1)),t.$set(f)},i(o){i||(la(t.$$.fragment,o),i=!0)},o(o){ia(t.$$.fragment,o),i=!1},d(o){o&&Zo(e),Qo(t)}}}function ra(n,e,t){let l,{value:i=!1}=e,{editable:r=!0}=e,{on_change:s}=e;function o(f){r&&s(f.detail)}function _(f){l=f,t(1,l),t(3,i)}return n.$$set=f=>{"value"in f&&t(3,i=f.value),"editable"in f&&t(0,r=f.editable),"on_change"in f&&t(4,s=f.on_change)},n.$$.update=()=>{n.$$.dirty&8&&t(1,l=typeof i=="string"?i.toLowerCase()==="true":!!i)},[r,l,o,i,s,_]}class oa extends Uo{constructor(e){super(),xo(this,e,ra,sa,na,{value:3,editable:0,on_change:4})}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),il()}get editable(){return this.$$.ctx[0]}set editable(e){this.$$set({editable:e}),il()}get on_change(){return this.$$.ctx[4]}set on_change(e){this.$$set({on_change:e}),il()}}const{HtmlTag:aa,SvelteComponent:_a,action_destroyer:ua,attr:Me,binding_callbacks:fa,bubble:sl,check_outros:yn,construct_svelte_component:_i,create_component:Jt,destroy_component:Ft,detach:pt,element:Ks,empty:Al,flush:ke,group_outros:Cn,init:ca,insert:vt,listen:Lt,mount_component:Kt,noop:Sn,prevent_default:ha,run_all:Ws,safe_not_equal:da,set_data:ma,set_input_value:ui,space:fl,stop_propagation:fi,text:ga,toggle_class:Je,transition_in:Ye,transition_out:at}=window.__gradio__svelte__internal,{createEventDispatcher:ba}=window.__gradio__svelte__internal;function ci(n){let e,t,l,i;return{c(){e=Ks("textarea"),e.readOnly=n[9],Me(e,"aria-readonly",n[9]),Me(e,"aria-label",t=n[9]?"Cell is read-only":"Edit cell"),Me(e,"tabindex","-1"),Me(e,"class","svelte-fvkwu"),Je(e,"header",n[4])},m(r,s){vt(r,e,s),n[29](e),ui(e,n[0]),l||(i=[Lt(e,"input",n[30]),Lt(e,"blur",n[21]),Lt(e,"mousedown",fi(n[27])),Lt(e,"click",fi(n[28])),ua(qa.call(null,e)),Lt(e,"keydown",n[22])],l=!0)},p(r,s){s[0]&512&&(e.readOnly=r[9]),s[0]&512&&Me(e,"aria-readonly",r[9]),s[0]&512&&t!==(t=r[9]?"Cell is read-only":"Edit cell")&&Me(e,"aria-label",t),s[0]&1&&ui(e,r[0]),s[0]&16&&Je(e,"header",r[4])},d(r){r&&pt(e),n[29](null),l=!1,Ws(i)}}}function wa(n){let e,t,l,i,r,s;const o=[Ca,ya,va,pa],_=[];function f(a,u){return a[5]==="image"&&a[11].image?0:a[5]==="html"?1:a[5]==="markdown"?2:3}return t=f(n),l=_[t]=o[t](n),{c(){e=Ks("span"),l.c(),Me(e,"tabindex","0"),Me(e,"role","button"),Me(e,"style",n[3]),Me(e,"data-editable",n[8]),Me(e,"data-max-chars",n[10]),Me(e,"data-expanded",n[2]),Me(e,"placeholder"," "),Me(e,"class","svelte-fvkwu"),Je(e,"dragging",n[13]),Je(e,"edit",n[2]),Je(e,"expanded",n[2]),Je(e,"multiline",n[4]),Je(e,"text",n[5]==="str"),Je(e,"wrap",n[14])},m(a,u){vt(a,e,u),_[t].m(e,null),i=!0,r||(s=[Lt(e,"keydown",n[22]),Lt(e,"focus",ha(n[26]))],r=!0)},p(a,u){let c=t;t=f(a),t===c?_[t].p(a,u):(Cn(),at(_[c],1,1,()=>{_[c]=null}),yn(),l=_[t],l?l.p(a,u):(l=_[t]=o[t](a),l.c()),Ye(l,1),l.m(e,null)),(!i||u[0]&8)&&Me(e,"style",a[3]),(!i||u[0]&256)&&Me(e,"data-editable",a[8]),(!i||u[0]&1024)&&Me(e,"data-max-chars",a[10]),(!i||u[0]&4)&&Me(e,"data-expanded",a[2]),(!i||u[0]&8192)&&Je(e,"dragging",a[13]),(!i||u[0]&4)&&Je(e,"edit",a[2]),(!i||u[0]&4)&&Je(e,"expanded",a[2]),(!i||u[0]&16)&&Je(e,"multiline",a[4]),(!i||u[0]&32)&&Je(e,"text",a[5]==="str"),(!i||u[0]&16384)&&Je(e,"wrap",a[14])},i(a){i||(Ye(l),i=!0)},o(a){at(l),i=!1},d(a){a&&pt(e),_[t].d(),r=!1,Ws(s)}}}function ka(n){let e,t;return e=new oa({props:{value:String(n[19]),editable:n[8],on_change:n[23]}}),{c(){Jt(e.$$.fragment)},m(l,i){Kt(e,l,i),t=!0},p(l,i){const r={};i[0]&524288&&(r.value=String(l[19])),i[0]&256&&(r.editable=l[8]),e.$set(r)},i(l){t||(Ye(e.$$.fragment,l),t=!0)},o(l){at(e.$$.fragment,l),t=!1},d(l){Ft(e,l)}}}function pa(n){let e;return{c(){e=ga(n[20])},m(t,l){vt(t,e,l)},p(t,l){l[0]&1048576&&ma(e,t[20])},i:Sn,o:Sn,d(t){t&&pt(e)}}}function va(n){let e,t;return e=new oo({props:{message:n[20].toLocaleString(),latex_delimiters:n[6],line_breaks:n[7],chatbot:!1}}),{c(){Jt(e.$$.fragment)},m(l,i){Kt(e,l,i),t=!0},p(l,i){const r={};i[0]&1048576&&(r.message=l[20].toLocaleString()),i[0]&64&&(r.latex_delimiters=l[6]),i[0]&128&&(r.line_breaks=l[7]),e.$set(r)},i(l){t||(Ye(e.$$.fragment,l),t=!0)},o(l){at(e.$$.fragment,l),t=!1},d(l){Ft(e,l)}}}function ya(n){let e,t;return{c(){e=new aa(!1),t=Al(),e.a=t},m(l,i){e.m(n[20],l,i),vt(l,t,i)},p(l,i){i[0]&1048576&&e.p(l[20])},i:Sn,o:Sn,d(l){l&&(pt(t),e.d())}}}function Ca(n){let e,t,l;var i=n[11].image;function r(s,o){return{props:{value:{url:s[20]},show_label:!1,label:"cell-image",show_download_button:!1,i18n:s[12],gradio:{dispatch:Ma}}}}return i&&(e=_i(i,r(n))),{c(){e&&Jt(e.$$.fragment),t=Al()},m(s,o){e&&Kt(e,s,o),vt(s,t,o),l=!0},p(s,o){if(o[0]&2048&&i!==(i=s[11].image)){if(e){Cn();const _=e;at(_.$$.fragment,1,0,()=>{Ft(_,1)}),yn()}i?(e=_i(i,r(s)),Jt(e.$$.fragment),Ye(e.$$.fragment,1),Kt(e,t.parentNode,t)):e=null}else if(i){const _={};o[0]&1048576&&(_.value={url:s[20]}),o[0]&4096&&(_.i18n=s[12]),e.$set(_)}},i(s){l||(e&&Ye(e.$$.fragment,s),l=!0)},o(s){e&&at(e.$$.fragment,s),l=!1},d(s){s&&pt(t),e&&Ft(e,s)}}}function hi(n){let e,t,l,i;return e=new ai({props:{position:"column",coords:n[16],on_click:n[31]}}),l=new ai({props:{position:"row",coords:n[16],on_click:n[32]}}),{c(){Jt(e.$$.fragment),t=fl(),Jt(l.$$.fragment)},m(r,s){Kt(e,r,s),vt(r,t,s),Kt(l,r,s),i=!0},p(r,s){const o={};s[0]&65536&&(o.coords=r[16]),s[0]&196608&&(o.on_click=r[31]),e.$set(o);const _={};s[0]&65536&&(_.coords=r[16]),s[0]&327680&&(_.on_click=r[32]),l.$set(_)},i(r){i||(Ye(e.$$.fragment,r),Ye(l.$$.fragment,r),i=!0)},o(r){at(e.$$.fragment,r),at(l.$$.fragment,r),i=!1},d(r){r&&pt(t),Ft(e,r),Ft(l,r)}}}function Sa(n){let e,t,l,i,r,s,o=n[2]&&n[5]!=="bool"&&ci(n);const _=[ka,wa],f=[];function a(c,g){return c[5]==="bool"?0:1}t=a(n),l=f[t]=_[t](n);let u=n[15]&&n[16]&&n[17]&&n[18]&&hi(n);return{c(){o&&o.c(),e=fl(),l.c(),i=fl(),u&&u.c(),r=Al()},m(c,g){o&&o.m(c,g),vt(c,e,g),f[t].m(c,g),vt(c,i,g),u&&u.m(c,g),vt(c,r,g),s=!0},p(c,g){c[2]&&c[5]!=="bool"?o?o.p(c,g):(o=ci(c),o.c(),o.m(e.parentNode,e)):o&&(o.d(1),o=null);let L=t;t=a(c),t===L?f[t].p(c,g):(Cn(),at(f[L],1,1,()=>{f[L]=null}),yn(),l=f[t],l?l.p(c,g):(l=f[t]=_[t](c),l.c()),Ye(l,1),l.m(i.parentNode,i)),c[15]&&c[16]&&c[17]&&c[18]?u?(u.p(c,g),g[0]&491520&&Ye(u,1)):(u=hi(c),u.c(),Ye(u,1),u.m(r.parentNode,r)):u&&(Cn(),at(u,1,1,()=>{u=null}),yn())},i(c){s||(Ye(l),Ye(u),s=!0)},o(c){at(l),at(u),s=!1},d(c){c&&(pt(e),pt(i),pt(r)),o&&o.d(c),f[t].d(c),u&&u.d(c)}}}function Na(n,e=null,t=!1){if(t)return String(n);const l=String(n);return!e||e<=0||l.length<=e?l:l.slice(0,e)+"..."}function qa(n){return requestAnimationFrame(()=>{n.focus()}),{}}const Ma=()=>{};function Aa(n,e,t){let l,i,r,{edit:s}=e,{value:o=""}=e,{display_value:_=null}=e,{styling:f=""}=e,{header:a=!1}=e,{datatype:u="str"}=e,{latex_delimiters:c}=e,{line_breaks:g=!0}=e,{editable:L=!0}=e,{is_static:v=!1}=e,{max_chars:q=null}=e,{components:w={}}=e,{i18n:p}=e,{is_dragging:k=!1}=e,{wrap_text:y=!1}=e,{show_selection_buttons:C=!1}=e,{coords:j}=e,{on_select_column:S=null}=e,{on_select_row:D=null}=e,{el:O}=e;const G=ba();function I(N){G("blur",{blur_event:N,coords:j})}function z(N){G("keydown",N)}function T(N){t(0,o=N.toString()),G("blur",{blur_event:{target:{type:"checkbox",checked:N,value:N.toString()}},coords:j})}function Y(N){sl.call(this,n,N)}function le(N){sl.call(this,n,N)}function K(N){sl.call(this,n,N)}function W(N){fa[N?"unshift":"push"](()=>{O=N,t(1,O)})}function ce(){o=this.value,t(0,o)}const B=()=>S(j[1]),Z=()=>D(j[0]);return n.$$set=N=>{"edit"in N&&t(2,s=N.edit),"value"in N&&t(0,o=N.value),"display_value"in N&&t(24,_=N.display_value),"styling"in N&&t(3,f=N.styling),"header"in N&&t(4,a=N.header),"datatype"in N&&t(5,u=N.datatype),"latex_delimiters"in N&&t(6,c=N.latex_delimiters),"line_breaks"in N&&t(7,g=N.line_breaks),"editable"in N&&t(8,L=N.editable),"is_static"in N&&t(9,v=N.is_static),"max_chars"in N&&t(10,q=N.max_chars),"components"in N&&t(11,w=N.components),"i18n"in N&&t(12,p=N.i18n),"is_dragging"in N&&t(13,k=N.is_dragging),"wrap_text"in N&&t(14,y=N.wrap_text),"show_selection_buttons"in N&&t(15,C=N.show_selection_buttons),"coords"in N&&t(16,j=N.coords),"on_select_column"in N&&t(17,S=N.on_select_column),"on_select_row"in N&&t(18,D=N.on_select_row),"el"in N&&t(1,O=N.el)},n.$$.update=()=>{n.$$.dirty[0]&1028&&t(25,l=!s&&q!==null&&q>0),n.$$.dirty[0]&16777473&&t(19,i=L?o:_!==null?_:o),n.$$.dirty[0]&34079776&&t(20,r=l?Na(i,q,u==="image"):i)},[o,O,s,f,a,u,c,g,L,v,q,w,p,k,y,C,j,S,D,i,r,I,z,T,_,l,Y,le,K,W,ce,B,Z]}class El extends _a{constructor(e){super(),ca(this,e,Aa,Sa,da,{edit:2,value:0,display_value:24,styling:3,header:4,datatype:5,latex_delimiters:6,line_breaks:7,editable:8,is_static:9,max_chars:10,components:11,i18n:12,is_dragging:13,wrap_text:14,show_selection_buttons:15,coords:16,on_select_column:17,on_select_row:18,el:1},null,[-1,-1])}get edit(){return this.$$.ctx[2]}set edit(e){this.$$set({edit:e}),ke()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),ke()}get display_value(){return this.$$.ctx[24]}set display_value(e){this.$$set({display_value:e}),ke()}get styling(){return this.$$.ctx[3]}set styling(e){this.$$set({styling:e}),ke()}get header(){return this.$$.ctx[4]}set header(e){this.$$set({header:e}),ke()}get datatype(){return this.$$.ctx[5]}set datatype(e){this.$$set({datatype:e}),ke()}get latex_delimiters(){return this.$$.ctx[6]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),ke()}get line_breaks(){return this.$$.ctx[7]}set line_breaks(e){this.$$set({line_breaks:e}),ke()}get editable(){return this.$$.ctx[8]}set editable(e){this.$$set({editable:e}),ke()}get is_static(){return this.$$.ctx[9]}set is_static(e){this.$$set({is_static:e}),ke()}get max_chars(){return this.$$.ctx[10]}set max_chars(e){this.$$set({max_chars:e}),ke()}get components(){return this.$$.ctx[11]}set components(e){this.$$set({components:e}),ke()}get i18n(){return this.$$.ctx[12]}set i18n(e){this.$$set({i18n:e}),ke()}get is_dragging(){return this.$$.ctx[13]}set is_dragging(e){this.$$set({is_dragging:e}),ke()}get wrap_text(){return this.$$.ctx[14]}set wrap_text(e){this.$$set({wrap_text:e}),ke()}get show_selection_buttons(){return this.$$.ctx[15]}set show_selection_buttons(e){this.$$set({show_selection_buttons:e}),ke()}get coords(){return this.$$.ctx[16]}set coords(e){this.$$set({coords:e}),ke()}get on_select_column(){return this.$$.ctx[17]}set on_select_column(e){this.$$set({on_select_column:e}),ke()}get on_select_row(){return this.$$.ctx[18]}set on_select_row(e){this.$$set({on_select_row:e}),ke()}get el(){return this.$$.ctx[1]}set el(e){this.$$set({el:e}),ke()}}const{SvelteComponent:Ea,append:La,attr:jt,detach:Ll,element:Us,empty:ja,flush:di,init:Ba,insert:jl,noop:cl,safe_not_equal:Da,set_data:za,text:Ta}=window.__gradio__svelte__internal;function Ha(n){let e,t=(n[0]!==null?n[0]+1:"")+"",l;return{c(){e=Us("td"),l=Ta(t),jt(e,"class","row-number svelte-ux4in1"),jt(e,"tabindex","-1"),jt(e,"data-row",n[0]),jt(e,"data-col","row-number")},m(i,r){jl(i,e,r),La(e,l)},p(i,r){r&1&&t!==(t=(i[0]!==null?i[0]+1:"")+"")&&za(l,t),r&1&&jt(e,"data-row",i[0])},d(i){i&&Ll(e)}}}function Oa(n){let e;return{c(){e=Us("th"),e.innerHTML='<div class="cell-wrap"><div class="header-content"><div class="header-text"></div></div></div>',jt(e,"tabindex","-1"),jt(e,"class","row-number svelte-ux4in1")},m(t,l){jl(t,e,l)},p:cl,d(t){t&&Ll(e)}}}function Ia(n){let e;function t(r,s){return r[1]?Oa:Ha}let l=t(n),i=l(n);return{c(){i.c(),e=ja()},m(r,s){i.m(r,s),jl(r,e,s)},p(r,[s]){l===(l=t(r))&&i?i.p(r,s):(i.d(1),i=l(r),i&&(i.c(),i.m(e.parentNode,e)))},i:cl,o:cl,d(r){r&&Ll(e),i.d(r)}}}function Ra(n,e,t){let{index:l=null}=e,{is_header:i=!1}=e;return n.$$set=r=>{"index"in r&&t(0,l=r.index),"is_header"in r&&t(1,i=r.is_header)},[l,i]}class En extends Ea{constructor(e){super(),Ba(this,e,Ra,Ia,Da,{index:0,is_header:1})}get index(){return this.$$.ctx[0]}set index(e){this.$$set({index:e}),di()}get is_header(){return this.$$.ctx[1]}set is_header(e){this.$$set({is_header:e}),di()}}const{SvelteComponent:Pa,attr:rl,detach:Ja,element:Fa,flush:Ka,init:Wa,insert:Ua,is_function:Va,listen:mi,noop:gi,run_all:Ya,safe_not_equal:Xa}=window.__gradio__svelte__internal;function Ga(n){let e,t,l;return{c(){e=Fa("button"),e.textContent="⋮",rl(e,"aria-label","Open cell menu"),rl(e,"class","cell-menu-button svelte-vt38nd"),rl(e,"aria-haspopup","menu")},m(i,r){Ua(i,e,r),t||(l=[mi(e,"click",function(){Va(n[0])&&n[0].apply(this,arguments)}),mi(e,"touchstart",n[1])],t=!0)},p(i,[r]){n=i},i:gi,o:gi,d(i){i&&Ja(e),t=!1,Ya(l)}}}function Qa(n,e,t){let{on_click:l}=e;const i=r=>{r.preventDefault();const s=r.touches[0],o=new MouseEvent("click",{clientX:s.clientX,clientY:s.clientY,bubbles:!0,cancelable:!0,view:window});l(o)};return n.$$set=r=>{"on_click"in r&&t(0,l=r.on_click)},[l,i]}class Vs extends Pa{constructor(e){super(),Wa(this,e,Qa,Ga,Xa,{on_click:0})}get on_click(){return this.$$.ctx[0]}set on_click(e){this.$$set({on_click:e}),Ka()}}const{SvelteComponent:Za,attr:bi,detach:$a,element:xa,init:e_,insert:t_,noop:ol,safe_not_equal:n_}=window.__gradio__svelte__internal;function l_(n){let e;return{c(){e=xa("div"),e.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg>',bi(e,"class","wrapper svelte-1skchaw"),bi(e,"aria-label","Static column")},m(t,l){t_(t,e,l)},p:ol,i:ol,o:ol,d(t){t&&$a(e)}}}class i_ extends Za{constructor(e){super(),e_(this,e,null,l_,n_,{})}}const{SvelteComponent:s_,append:wi,attr:De,detach:r_,flush:o_,init:a_,insert:__,noop:ki,safe_not_equal:u_,svg_element:al}=window.__gradio__svelte__internal;function f_(n){let e,t,l;return{c(){e=al("svg"),t=al("path"),l=al("path"),De(t,"d","M4 8L8 4L12 8"),De(t,"stroke","currentColor"),De(t,"stroke-width","1.5"),De(t,"stroke-linecap","round"),De(t,"stroke-linejoin","round"),De(l,"d","M8 4V12"),De(l,"stroke","currentColor"),De(l,"stroke-width","1.5"),De(l,"stroke-linecap","round"),De(e,"width",n[0]),De(e,"height",n[0]),De(e,"viewBox","0 0 16 16"),De(e,"fill","none"),De(e,"xmlns","http://www.w3.org/2000/svg")},m(i,r){__(i,e,r),wi(e,t),wi(e,l)},p(i,[r]){r&1&&De(e,"width",i[0]),r&1&&De(e,"height",i[0])},i:ki,o:ki,d(i){i&&r_(e)}}}function c_(n,e,t){let{size:l=16}=e;return n.$$set=i=>{"size"in i&&t(0,l=i.size)},[l]}class h_ extends s_{constructor(e){super(),a_(this,e,c_,f_,u_,{size:0})}get size(){return this.$$.ctx[0]}set size(e){this.$$set({size:e}),o_()}}const{SvelteComponent:d_,append:pi,attr:ze,detach:m_,flush:g_,init:b_,insert:w_,noop:vi,safe_not_equal:k_,svg_element:_l}=window.__gradio__svelte__internal;function p_(n){let e,t,l;return{c(){e=_l("svg"),t=_l("path"),l=_l("path"),ze(t,"d","M4 8L8 12L12 8"),ze(t,"stroke","currentColor"),ze(t,"stroke-width","1.5"),ze(t,"stroke-linecap","round"),ze(t,"stroke-linejoin","round"),ze(l,"d","M8 12V4"),ze(l,"stroke","currentColor"),ze(l,"stroke-width","1.5"),ze(l,"stroke-linecap","round"),ze(e,"width",n[0]),ze(e,"height",n[0]),ze(e,"viewBox","0 0 16 16"),ze(e,"fill","none"),ze(e,"xmlns","http://www.w3.org/2000/svg")},m(i,r){w_(i,e,r),pi(e,t),pi(e,l)},p(i,[r]){r&1&&ze(e,"width",i[0]),r&1&&ze(e,"height",i[0])},i:vi,o:vi,d(i){i&&m_(e)}}}function v_(n,e,t){let{size:l=16}=e;return n.$$set=i=>{"size"in i&&t(0,l=i.size)},[l]}class y_ extends d_{constructor(e){super(),b_(this,e,v_,p_,k_,{size:0})}get size(){return this.$$.ctx[0]}set size(e){this.$$set({size:e}),g_()}}const{SvelteComponent:C_,append:ne,attr:d,detach:_t,empty:S_,flush:N_,init:q_,insert:ut,noop:yi,safe_not_equal:M_,svg_element:U}=window.__gradio__svelte__internal;function A_(n){let e,t,l,i,r;return{c(){e=U("svg"),t=U("path"),l=U("path"),i=U("path"),r=U("path"),d(t,"d","M5 5H19"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(t,"stroke-linecap","round"),d(l,"d","M8 9H16"),d(l,"stroke","currentColor"),d(l,"stroke-width","2"),d(l,"stroke-linecap","round"),d(i,"d","M11 13H13"),d(i,"stroke","currentColor"),d(i,"stroke-width","2"),d(i,"stroke-linecap","round"),d(r,"d","M17 17L21 21M21 17L17 21"),d(r,"stroke","currentColor"),d(r,"stroke-width","2"),d(r,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(s,o){ut(s,e,o),ne(e,t),ne(e,l),ne(e,i),ne(e,r)},d(s){s&&_t(e)}}}function E_(n){let e,t,l,i;return{c(){e=U("svg"),t=U("path"),l=U("path"),i=U("path"),d(t,"d","M5 5H19"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(t,"stroke-linecap","round"),d(l,"d","M8 9H16"),d(l,"stroke","currentColor"),d(l,"stroke-width","2"),d(l,"stroke-linecap","round"),d(i,"d","M11 13H13"),d(i,"stroke","currentColor"),d(i,"stroke-width","2"),d(i,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(r,s){ut(r,e,s),ne(e,t),ne(e,l),ne(e,i)},d(r){r&&_t(e)}}}function L_(n){let e,t,l,i,r,s;return{c(){e=U("svg"),t=U("path"),l=U("path"),i=U("path"),r=U("path"),s=U("path"),d(t,"d","M5 5H19"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(t,"stroke-linecap","round"),d(l,"d","M5 9H15"),d(l,"stroke","currentColor"),d(l,"stroke-width","2"),d(l,"stroke-linecap","round"),d(i,"d","M5 13H11"),d(i,"stroke","currentColor"),d(i,"stroke-width","2"),d(i,"stroke-linecap","round"),d(r,"d","M5 17H7"),d(r,"stroke","currentColor"),d(r,"stroke-width","2"),d(r,"stroke-linecap","round"),d(s,"d","M17 17L21 21M21 17L17 21"),d(s,"stroke","currentColor"),d(s,"stroke-width","2"),d(s,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(o,_){ut(o,e,_),ne(e,t),ne(e,l),ne(e,i),ne(e,r),ne(e,s)},d(o){o&&_t(e)}}}function j_(n){let e,t,l,i;return{c(){e=U("svg"),t=U("path"),l=U("path"),i=U("path"),d(t,"d","M8 12L12 16L16 12"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(t,"fill","none"),d(t,"stroke-linecap","round"),d(t,"stroke-linejoin","round"),d(l,"d","M12 16V9"),d(l,"stroke","currentColor"),d(l,"stroke-width","2"),d(l,"stroke-linecap","round"),d(i,"d","M5 5H19"),d(i,"stroke","currentColor"),d(i,"stroke-width","2"),d(i,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(r,s){ut(r,e,s),ne(e,t),ne(e,l),ne(e,i)},d(r){r&&_t(e)}}}function B_(n){let e,t,l,i;return{c(){e=U("svg"),t=U("path"),l=U("path"),i=U("path"),d(t,"d","M8 16L12 12L16 16"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(t,"fill","none"),d(t,"stroke-linecap","round"),d(t,"stroke-linejoin","round"),d(l,"d","M12 12V19"),d(l,"stroke","currentColor"),d(l,"stroke-width","2"),d(l,"stroke-linecap","round"),d(i,"d","M5 7H19"),d(i,"stroke","currentColor"),d(i,"stroke-width","2"),d(i,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(r,s){ut(r,e,s),ne(e,t),ne(e,l),ne(e,i)},d(r){r&&_t(e)}}}function D_(n){let e,t,l;return{c(){e=U("svg"),t=U("rect"),l=U("path"),d(t,"x","10"),d(t,"y","5"),d(t,"width","4"),d(t,"height","14"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(l,"d","M7 8L17 16M17 8L7 16"),d(l,"stroke","currentColor"),d(l,"stroke-width","2"),d(l,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(i,r){ut(i,e,r),ne(e,t),ne(e,l)},d(i){i&&_t(e)}}}function z_(n){let e,t,l;return{c(){e=U("svg"),t=U("rect"),l=U("path"),d(t,"x","5"),d(t,"y","10"),d(t,"width","14"),d(t,"height","4"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(l,"d","M8 7L16 17M16 7L8 17"),d(l,"stroke","currentColor"),d(l,"stroke-width","2"),d(l,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(i,r){ut(i,e,r),ne(e,t),ne(e,l)},d(i){i&&_t(e)}}}function T_(n){let e,t,l;return{c(){e=U("svg"),t=U("rect"),l=U("path"),d(t,"x","6"),d(t,"y","4"),d(t,"width","12"),d(t,"height","4"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(l,"d","M12 12V19M8 16L12 19L16 16"),d(l,"stroke","currentColor"),d(l,"stroke-width","2"),d(l,"fill","none"),d(l,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(i,r){ut(i,e,r),ne(e,t),ne(e,l)},d(i){i&&_t(e)}}}function H_(n){let e,t,l;return{c(){e=U("svg"),t=U("rect"),l=U("path"),d(t,"x","6"),d(t,"y","16"),d(t,"width","12"),d(t,"height","4"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(l,"d","M12 12V5M8 8L12 5L16 8"),d(l,"stroke","currentColor"),d(l,"stroke-width","2"),d(l,"fill","none"),d(l,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(i,r){ut(i,e,r),ne(e,t),ne(e,l)},d(i){i&&_t(e)}}}function O_(n){let e,t,l;return{c(){e=U("svg"),t=U("rect"),l=U("path"),d(t,"x","16"),d(t,"y","6"),d(t,"width","4"),d(t,"height","12"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(t,"fill","none"),d(l,"d","M12 12H5M8 8L5 12L8 16"),d(l,"stroke","currentColor"),d(l,"stroke-width","2"),d(l,"fill","none"),d(l,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(i,r){ut(i,e,r),ne(e,t),ne(e,l)},d(i){i&&_t(e)}}}function I_(n){let e,t,l;return{c(){e=U("svg"),t=U("rect"),l=U("path"),d(t,"x","4"),d(t,"y","6"),d(t,"width","4"),d(t,"height","12"),d(t,"stroke","currentColor"),d(t,"stroke-width","2"),d(t,"fill","none"),d(l,"d","M12 12H19M16 8L19 12L16 16"),d(l,"stroke","currentColor"),d(l,"stroke-width","2"),d(l,"fill","none"),d(l,"stroke-linecap","round"),d(e,"viewBox","0 0 24 24"),d(e,"width","16"),d(e,"height","16")},m(i,r){ut(i,e,r),ne(e,t),ne(e,l)},d(i){i&&_t(e)}}}function R_(n){let e;function t(r,s){if(r[0]=="add-column-right")return I_;if(r[0]=="add-column-left")return O_;if(r[0]=="add-row-above")return H_;if(r[0]=="add-row-below")return T_;if(r[0]=="delete-row")return z_;if(r[0]=="delete-column")return D_;if(r[0]=="sort-asc")return B_;if(r[0]=="sort-desc")return j_;if(r[0]=="clear-sort")return L_;if(r[0]=="filter")return E_;if(r[0]=="clear-filter")return A_}let l=t(n),i=l&&l(n);return{c(){i&&i.c(),e=S_()},m(r,s){i&&i.m(r,s),ut(r,e,s)},p(r,[s]){l!==(l=t(r))&&(i&&i.d(1),i=l&&l(r),i&&(i.c(),i.m(e.parentNode,e)))},i:yi,o:yi,d(r){r&&_t(e),i&&i.d(r)}}}function P_(n,e,t){let{icon:l}=e;return n.$$set=i=>{"icon"in i&&t(0,l=i.icon)},[l]}class nt extends C_{constructor(e){super(),q_(this,e,P_,R_,M_,{icon:0})}get icon(){return this.$$.ctx[0]}set icon(e){this.$$set({icon:e}),N_()}}const{SvelteComponent:J_,add_flush_callback:Ci,append:ft,attr:He,bind:Si,binding_callbacks:Ni,check_outros:xt,create_component:Ut,destroy_component:Vt,detach:Ln,element:kt,flush:pe,group_outros:en,init:F_,insert:jn,listen:gn,mount_component:Yt,run_all:K_,safe_not_equal:W_,set_data:U_,set_style:bn,space:tn,text:V_,toggle_class:bt,transition_in:ye,transition_out:Te}=window.__gradio__svelte__internal;function qi(n){let e,t,l,i,r,s;const o=[X_,Y_],_=[];function f(u,c){return u[19]==="asc"?0:1}l=f(n),i=_[l]=o[l](n);let a=n[11].length>1&&Mi(n);return{c(){e=kt("div"),t=kt("span"),i.c(),r=tn(),a&&a.c(),He(t,"class","sort-arrow svelte-41hbvn"),He(e,"class","sort-indicators svelte-41hbvn")},m(u,c){jn(u,e,c),ft(e,t),_[l].m(t,null),ft(e,r),a&&a.m(e,null),s=!0},p(u,c){let g=l;l=f(u),l!==g&&(en(),Te(_[g],1,1,()=>{_[g]=null}),xt(),i=_[l],i||(i=_[l]=o[l](u),i.c()),ye(i,1),i.m(t,null)),u[11].length>1?a?a.p(u,c):(a=Mi(u),a.c(),a.m(e,null)):a&&(a.d(1),a=null)},i(u){s||(ye(i),s=!0)},o(u){Te(i),s=!1},d(u){u&&Ln(e),_[l].d(),a&&a.d()}}}function Y_(n){let e,t;return e=new y_({props:{size:12}}),{c(){Ut(e.$$.fragment)},m(l,i){Yt(e,l,i),t=!0},i(l){t||(ye(e.$$.fragment,l),t=!0)},o(l){Te(e.$$.fragment,l),t=!1},d(l){Vt(e,l)}}}function X_(n){let e,t;return e=new h_({props:{size:12}}),{c(){Ut(e.$$.fragment)},m(l,i){Yt(e,l,i),t=!0},i(l){t||(ye(e.$$.fragment,l),t=!0)},o(l){Te(e.$$.fragment,l),t=!1},d(l){Vt(e,l)}}}function Mi(n){let e,t;return{c(){e=kt("span"),t=V_(n[20]),He(e,"class","sort-priority svelte-41hbvn")},m(l,i){jn(l,e,i),ft(e,t)},p(l,i){i[0]&1048576&&U_(t,l[20])},d(l){l&&Ln(e)}}}function Ai(n){let e,t,l,i;return l=new nt({props:{icon:"filter"}}),{c(){e=kt("div"),t=kt("span"),Ut(l.$$.fragment),He(t,"class","filter-icon svelte-41hbvn"),He(e,"class","filter-indicators svelte-41hbvn")},m(r,s){jn(r,e,s),ft(e,t),Yt(l,t,null),i=!0},i(r){i||(ye(l.$$.fragment,r),i=!0)},o(r){Te(l.$$.fragment,r),i=!1},d(r){r&&Ln(e),Vt(l)}}}function Ei(n){let e,t;return e=new i_({}),{c(){Ut(e.$$.fragment)},m(l,i){Yt(e,l,i),t=!0},i(l){t||(ye(e.$$.fragment,l),t=!0)},o(l){Te(e.$$.fragment,l),t=!1},d(l){Vt(e,l)}}}function Li(n){let e,t;return e=new Vs({props:{on_click:n[30]}}),{c(){Ut(e.$$.fragment)},m(l,i){Yt(e,l,i),t=!0},p(l,i){const r={};i[0]&516&&(r.on_click=l[30]),e.$set(r)},i(l){t||(ye(e.$$.fragment,l),t=!0)},o(l){Te(e.$$.fragment,l),t=!1},d(l){Vt(e,l)}}}function G_(n){let e,t,l,i,r,s,o,_,f,a,u,c,g,L,v;function q(S){n[26](S)}function w(S){n[27](S)}let p={max_chars:n[14],latex_delimiters:n[12],line_breaks:n[13],edit:n[4]===n[2],header:!0,editable:n[15],is_static:n[17],i18n:n[16],coords:[n[2],0]};n[0]!==void 0&&(p.value=n[0]),n[1]!==void 0&&(p.el=n[1]),r=new El({props:p}),Ni.push(()=>Si(r,"value",q)),Ni.push(()=>Si(r,"el",w)),r.$on("keydown",n[28]);let k=n[18]!==-1&&qi(n),y=n[21]!==-1&&Ai(),C=n[17]&&Ei(),j=n[22]&&Li(n);return{c(){e=kt("th"),t=kt("div"),l=kt("div"),i=kt("button"),Ut(r.$$.fragment),_=tn(),k&&k.c(),f=tn(),y&&y.c(),a=tn(),C&&C.c(),u=tn(),j&&j.c(),He(i,"class","header-button svelte-41hbvn"),He(i,"title",n[0]),He(l,"class","header-content svelte-41hbvn"),He(t,"class","cell-wrap svelte-41hbvn"),He(e,"aria-sort",c=mn(n[0],n[11],n[6])==="none"?"none":mn(n[0],n[11],n[6])==="asc"?"ascending":"descending"),bn(e,"width",n[7](n[2])),bn(e,"left",n[23](n[2])),He(e,"title",n[0]),He(e,"class","svelte-41hbvn"),bt(e,"pinned-column",n[2]<n[3]),bt(e,"last-pinned",n[2]===n[3]-1),bt(e,"focus",n[4]===n[2]||n[5]===n[2]),bt(e,"sorted",n[18]!==-1),bt(e,"filtered",n[21]!==-1)},m(S,D){jn(S,e,D),ft(e,t),ft(t,l),ft(l,i),Yt(r,i,null),ft(i,_),k&&k.m(i,null),ft(i,f),y&&y.m(i,null),ft(l,a),C&&C.m(l,null),ft(t,u),j&&j.m(t,null),g=!0,L||(v=[gn(i,"click",n[29]),gn(i,"mousedown",Q_),gn(e,"click",n[31]),gn(e,"mousedown",Z_)],L=!0)},p(S,D){const O={};D[0]&16384&&(O.max_chars=S[14]),D[0]&4096&&(O.latex_delimiters=S[12]),D[0]&8192&&(O.line_breaks=S[13]),D[0]&20&&(O.edit=S[4]===S[2]),D[0]&32768&&(O.editable=S[15]),D[0]&131072&&(O.is_static=S[17]),D[0]&65536&&(O.i18n=S[16]),D[0]&4&&(O.coords=[S[2],0]),!s&&D[0]&1&&(s=!0,O.value=S[0],Ci(()=>s=!1)),!o&&D[0]&2&&(o=!0,O.el=S[1],Ci(()=>o=!1)),r.$set(O),S[18]!==-1?k?(k.p(S,D),D[0]&262144&&ye(k,1)):(k=qi(S),k.c(),ye(k,1),k.m(i,f)):k&&(en(),Te(k,1,1,()=>{k=null}),xt()),S[21]!==-1?y?D[0]&2097152&&ye(y,1):(y=Ai(),y.c(),ye(y,1),y.m(i,null)):y&&(en(),Te(y,1,1,()=>{y=null}),xt()),(!g||D[0]&1)&&He(i,"title",S[0]),S[17]?C?D[0]&131072&&ye(C,1):(C=Ei(),C.c(),ye(C,1),C.m(l,null)):C&&(en(),Te(C,1,1,()=>{C=null}),xt()),S[22]?j?(j.p(S,D),D[0]&4194304&&ye(j,1)):(j=Li(S),j.c(),ye(j,1),j.m(t,null)):j&&(en(),Te(j,1,1,()=>{j=null}),xt()),(!g||D[0]&2113&&c!==(c=mn(S[0],S[11],S[6])==="none"?"none":mn(S[0],S[11],S[6])==="asc"?"ascending":"descending"))&&He(e,"aria-sort",c),(!g||D[0]&132)&&bn(e,"width",S[7](S[2])),(!g||D[0]&4)&&bn(e,"left",S[23](S[2])),(!g||D[0]&1)&&He(e,"title",S[0]),(!g||D[0]&12)&&bt(e,"pinned-column",S[2]<S[3]),(!g||D[0]&12)&&bt(e,"last-pinned",S[2]===S[3]-1),(!g||D[0]&52)&&bt(e,"focus",S[4]===S[2]||S[5]===S[2]),(!g||D[0]&262144)&&bt(e,"sorted",S[18]!==-1),(!g||D[0]&2097152)&&bt(e,"filtered",S[21]!==-1)},i(S){g||(ye(r.$$.fragment,S),ye(k),ye(y),ye(C),ye(j),g=!0)},o(S){Te(r.$$.fragment,S),Te(k),Te(y),Te(C),Te(j),g=!1},d(S){S&&Ln(e),Vt(r),k&&k.d(),y&&y.d(),C&&C.d(),j&&j.d(),L=!1,K_(v)}}}const Q_=n=>{n.preventDefault(),n.stopPropagation()},Z_=n=>{n.preventDefault(),n.stopPropagation()};function $_(n,e,t){let l,i,r,s,o,{value:_}=e,{i:f}=e,{actual_pinned_columns:a}=e,{header_edit:u}=e,{selected_header:c}=e,{headers:g}=e,{get_cell_width:L}=e,{handle_header_click:v}=e,{toggle_header_menu:q}=e,{end_header_edit:w}=e,{sort_columns:p=[]}=e,{filter_columns:k=[]}=e,{latex_delimiters:y}=e,{line_breaks:C}=e,{max_chars:j}=e,{editable:S}=e,{i18n:D}=e,{el:O}=e,{is_static:G}=e,{col_count:I}=e;function z(B){return B>=a?"auto":B===0?"0":`calc(${Array(B).fill(0).map((N,J)=>L(J)).join(" + ")})`}function T(B){_=B,t(0,_)}function Y(B){O=B,t(1,O)}const le=B=>{(B.detail.key==="Enter"||B.detail.key==="Escape"||B.detail.key==="Tab")&&w(B)},K=B=>v(B,f),W=B=>q(B,f),ce=B=>v(B,f);return n.$$set=B=>{"value"in B&&t(0,_=B.value),"i"in B&&t(2,f=B.i),"actual_pinned_columns"in B&&t(3,a=B.actual_pinned_columns),"header_edit"in B&&t(4,u=B.header_edit),"selected_header"in B&&t(5,c=B.selected_header),"headers"in B&&t(6,g=B.headers),"get_cell_width"in B&&t(7,L=B.get_cell_width),"handle_header_click"in B&&t(8,v=B.handle_header_click),"toggle_header_menu"in B&&t(9,q=B.toggle_header_menu),"end_header_edit"in B&&t(10,w=B.end_header_edit),"sort_columns"in B&&t(11,p=B.sort_columns),"filter_columns"in B&&t(24,k=B.filter_columns),"latex_delimiters"in B&&t(12,y=B.latex_delimiters),"line_breaks"in B&&t(13,C=B.line_breaks),"max_chars"in B&&t(14,j=B.max_chars),"editable"in B&&t(15,S=B.editable),"i18n"in B&&t(16,D=B.i18n),"el"in B&&t(1,O=B.el),"is_static"in B&&t(17,G=B.is_static),"col_count"in B&&t(25,I=B.col_count)},n.$$.update=()=>{n.$$.dirty[0]&33554432&&t(22,l=I&&I[1]==="dynamic"),n.$$.dirty[0]&2052&&t(18,i=p.findIndex(B=>B.col===f)),n.$$.dirty[0]&16777220&&t(21,r=k.findIndex(B=>B.col===f)),n.$$.dirty[0]&262144&&t(20,s=i!==-1?i+1:null),n.$$.dirty[0]&264192&&t(19,o=i!==-1?p[i].direction:null)},[_,O,f,a,u,c,g,L,v,q,w,p,y,C,j,S,D,G,i,o,s,r,l,z,k,I,T,Y,le,K,W,ce]}class Ys extends J_{constructor(e){super(),F_(this,e,$_,G_,W_,{value:0,i:2,actual_pinned_columns:3,header_edit:4,selected_header:5,headers:6,get_cell_width:7,handle_header_click:8,toggle_header_menu:9,end_header_edit:10,sort_columns:11,filter_columns:24,latex_delimiters:12,line_breaks:13,max_chars:14,editable:15,i18n:16,el:1,is_static:17,col_count:25},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),pe()}get i(){return this.$$.ctx[2]}set i(e){this.$$set({i:e}),pe()}get actual_pinned_columns(){return this.$$.ctx[3]}set actual_pinned_columns(e){this.$$set({actual_pinned_columns:e}),pe()}get header_edit(){return this.$$.ctx[4]}set header_edit(e){this.$$set({header_edit:e}),pe()}get selected_header(){return this.$$.ctx[5]}set selected_header(e){this.$$set({selected_header:e}),pe()}get headers(){return this.$$.ctx[6]}set headers(e){this.$$set({headers:e}),pe()}get get_cell_width(){return this.$$.ctx[7]}set get_cell_width(e){this.$$set({get_cell_width:e}),pe()}get handle_header_click(){return this.$$.ctx[8]}set handle_header_click(e){this.$$set({handle_header_click:e}),pe()}get toggle_header_menu(){return this.$$.ctx[9]}set toggle_header_menu(e){this.$$set({toggle_header_menu:e}),pe()}get end_header_edit(){return this.$$.ctx[10]}set end_header_edit(e){this.$$set({end_header_edit:e}),pe()}get sort_columns(){return this.$$.ctx[11]}set sort_columns(e){this.$$set({sort_columns:e}),pe()}get filter_columns(){return this.$$.ctx[24]}set filter_columns(e){this.$$set({filter_columns:e}),pe()}get latex_delimiters(){return this.$$.ctx[12]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),pe()}get line_breaks(){return this.$$.ctx[13]}set line_breaks(e){this.$$set({line_breaks:e}),pe()}get max_chars(){return this.$$.ctx[14]}set max_chars(e){this.$$set({max_chars:e}),pe()}get editable(){return this.$$.ctx[15]}set editable(e){this.$$set({editable:e}),pe()}get i18n(){return this.$$.ctx[16]}set i18n(e){this.$$set({i18n:e}),pe()}get el(){return this.$$.ctx[1]}set el(e){this.$$set({el:e}),pe()}get is_static(){return this.$$.ctx[17]}set is_static(e){this.$$set({is_static:e}),pe()}get col_count(){return this.$$.ctx[25]}set col_count(e){this.$$set({col_count:e}),pe()}}const{SvelteComponent:x_,add_flush_callback:ji,append:Bi,attr:tt,bind:Di,binding_callbacks:hl,check_outros:eu,create_component:Xs,destroy_component:Gs,detach:tu,element:zi,flush:ie,group_outros:nu,init:lu,insert:iu,is_function:su,listen:Ti,mount_component:Qs,prevent_default:ru,run_all:ou,safe_not_equal:au,space:_u,toggle_class:ve,transition_in:nn,transition_out:pn}=window.__gradio__svelte__internal;function Hi(n){let e,t;return e=new Vs({props:{on_click:n[39]}}),{c(){Xs(e.$$.fragment)},m(l,i){Qs(e,l,i),t=!0},p(l,i){const r={};i[0]&536&&(r.on_click=l[39]),e.$set(r)},i(l){t||(nn(e.$$.fragment,l),t=!0)},o(l){pn(e.$$.fragment,l),t=!1},d(l){Gs(e,l)}}}function uu(n){let e,t,l,i,r,s,o=n[19]&&n[10]([n[3],n[4]],n[0],n[19]),_,f,a,u,c,g;function L(p){n[36](p)}function v(p){n[37](p)}let q={display_value:n[26]!==void 0?n[26]:String(n[1]),latex_delimiters:n[14],line_breaks:n[15],editable:n[19],is_static:n[20],edit:n[17]&&n[17][0]===n[3]&&n[17][1]===n[4],datatype:n[16],max_chars:n[18],i18n:n[21],components:n[22],show_selection_buttons:n[0].length===1&&n[0][0][0]===n[3]&&n[0][0][1]===n[4],coords:[n[3],n[4]],on_select_column:n[23],on_select_row:n[24],is_dragging:n[25],wrap_text:n[27]};n[1]!==void 0&&(q.value=n[1]),n[2].input!==void 0&&(q.el=n[2].input),l=new El({props:q}),hl.push(()=>Di(l,"value",L)),hl.push(()=>Di(l,"el",v)),l.$on("focus",n[38]),l.$on("blur",function(){su(n[8])&&n[8].apply(this,arguments)});let w=o&&Hi(n);return{c(){e=zi("td"),t=zi("div"),Xs(l.$$.fragment),s=_u(),w&&w.c(),tt(t,"class","cell-wrap svelte-v1pjjd"),tt(e,"tabindex",_=n[4]<n[5]?-1:0),tt(e,"data-row",n[3]),tt(e,"data-col",n[4]),tt(e,"data-testid",f=`cell-${n[3]}-${n[4]}`),tt(e,"style",a="width: "+n[6](n[4])+"; left: "+n[33](n[4])+"; "+(n[13]||"")),tt(e,"class","svelte-v1pjjd"),ve(e,"pinned-column",n[4]<n[5]),ve(e,"last-pinned",n[4]===n[5]-1),ve(e,"flash",n[11]&&n[32]),ve(e,"cell-selected",n[32]),ve(e,"no-top",n[31]),ve(e,"no-bottom",n[30]),ve(e,"no-left",n[29]),ve(e,"no-right",n[28]),ve(e,"menu-active",n[12]&&n[12].row===n[3]&&n[12].col===n[4]),ve(e,"dragging",n[25])},m(p,k){iu(p,e,k),Bi(e,t),Qs(l,t,null),Bi(t,s),w&&w.m(t,null),n[40](e),u=!0,c||(g=[Ti(e,"mousedown",n[41]),Ti(e,"contextmenu",ru(n[42]))],c=!0)},p(p,k){n=p;const y={};k[0]&67108866&&(y.display_value=n[26]!==void 0?n[26]:String(n[1])),k[0]&16384&&(y.latex_delimiters=n[14]),k[0]&32768&&(y.line_breaks=n[15]),k[0]&524288&&(y.editable=n[19]),k[0]&1048576&&(y.is_static=n[20]),k[0]&131096&&(y.edit=n[17]&&n[17][0]===n[3]&&n[17][1]===n[4]),k[0]&65536&&(y.datatype=n[16]),k[0]&262144&&(y.max_chars=n[18]),k[0]&2097152&&(y.i18n=n[21]),k[0]&4194304&&(y.components=n[22]),k[0]&25&&(y.show_selection_buttons=n[0].length===1&&n[0][0][0]===n[3]&&n[0][0][1]===n[4]),k[0]&24&&(y.coords=[n[3],n[4]]),k[0]&8388608&&(y.on_select_column=n[23]),k[0]&16777216&&(y.on_select_row=n[24]),k[0]&33554432&&(y.is_dragging=n[25]),k[0]&134217728&&(y.wrap_text=n[27]),!i&&k[0]&2&&(i=!0,y.value=n[1],ji(()=>i=!1)),!r&&k[0]&4&&(r=!0,y.el=n[2].input,ji(()=>r=!1)),l.$set(y),k[0]&525337&&(o=n[19]&&n[10]([n[3],n[4]],n[0],n[19])),o?w?(w.p(n,k),k[0]&525337&&nn(w,1)):(w=Hi(n),w.c(),nn(w,1),w.m(t,null)):w&&(nu(),pn(w,1,1,()=>{w=null}),eu()),(!u||k[0]&48&&_!==(_=n[4]<n[5]?-1:0))&&tt(e,"tabindex",_),(!u||k[0]&8)&&tt(e,"data-row",n[3]),(!u||k[0]&16)&&tt(e,"data-col",n[4]),(!u||k[0]&24&&f!==(f=`cell-${n[3]}-${n[4]}`))&&tt(e,"data-testid",f),(!u||k[0]&8272&&a!==(a="width: "+n[6](n[4])+"; left: "+n[33](n[4])+"; "+(n[13]||"")))&&tt(e,"style",a),(!u||k[0]&48)&&ve(e,"pinned-column",n[4]<n[5]),(!u||k[0]&48)&&ve(e,"last-pinned",n[4]===n[5]-1),(!u||k[0]&2048|k[1]&2)&&ve(e,"flash",n[11]&&n[32]),(!u||k[1]&2)&&ve(e,"cell-selected",n[32]),(!u||k[1]&1)&&ve(e,"no-top",n[31]),(!u||k[0]&1073741824)&&ve(e,"no-bottom",n[30]),(!u||k[0]&536870912)&&ve(e,"no-left",n[29]),(!u||k[0]&268435456)&&ve(e,"no-right",n[28]),(!u||k[0]&4120)&&ve(e,"menu-active",n[12]&&n[12].row===n[3]&&n[12].col===n[4]),(!u||k[0]&33554432)&&ve(e,"dragging",n[25])},i(p){u||(nn(l.$$.fragment,p),nn(w),u=!0)},o(p){pn(l.$$.fragment,p),pn(w),u=!1},d(p){p&&tu(e),Gs(l),w&&w.d(),n[40](null),c=!1,ou(g)}}}function fu(n,e,t){let l,i,r,s,o,_,{value:f}=e,{index:a}=e,{j:u}=e,{actual_pinned_columns:c}=e,{get_cell_width:g}=e,{handle_cell_click:L}=e,{handle_blur:v}=e,{toggle_cell_menu:q}=e,{is_cell_selected:w}=e,{should_show_cell_menu:p}=e,{selected_cells:k}=e,{copy_flash:y}=e,{active_cell_menu:C}=e,{styling:j}=e,{latex_delimiters:S}=e,{line_breaks:D}=e,{datatype:O}=e,{editing:G}=e,{max_chars:I}=e,{editable:z}=e,{is_static:T=!1}=e,{i18n:Y}=e,{components:le={}}=e,{el:K}=e,{handle_select_column:W}=e,{handle_select_row:ce}=e,{is_dragging:B}=e,{display_value:Z}=e,{wrap:N=!1}=e;function J(A){return A>=c?"auto":A===0?"0":`calc(${Array(A).fill(0).map((Nt,x)=>g(x)).join(" + ")})`}function b(A){f=A,t(1,f)}function M(A){n.$$.not_equal(K.input,A)&&(K.input=A,t(2,K))}const m=()=>{const A=a,P=u;k.some(([Nt,x])=>Nt===A&&x===P)||t(0,k=[[A,P]])},F=A=>q(A,a,u);function Se(A){hl[A?"unshift":"push"](()=>{K.cell=A,t(2,K)})}const mt=A=>L(A,a,u),$=A=>q(A,a,u);return n.$$set=A=>{"value"in A&&t(1,f=A.value),"index"in A&&t(3,a=A.index),"j"in A&&t(4,u=A.j),"actual_pinned_columns"in A&&t(5,c=A.actual_pinned_columns),"get_cell_width"in A&&t(6,g=A.get_cell_width),"handle_cell_click"in A&&t(7,L=A.handle_cell_click),"handle_blur"in A&&t(8,v=A.handle_blur),"toggle_cell_menu"in A&&t(9,q=A.toggle_cell_menu),"is_cell_selected"in A&&t(34,w=A.is_cell_selected),"should_show_cell_menu"in A&&t(10,p=A.should_show_cell_menu),"selected_cells"in A&&t(0,k=A.selected_cells),"copy_flash"in A&&t(11,y=A.copy_flash),"active_cell_menu"in A&&t(12,C=A.active_cell_menu),"styling"in A&&t(13,j=A.styling),"latex_delimiters"in A&&t(14,S=A.latex_delimiters),"line_breaks"in A&&t(15,D=A.line_breaks),"datatype"in A&&t(16,O=A.datatype),"editing"in A&&t(17,G=A.editing),"max_chars"in A&&t(18,I=A.max_chars),"editable"in A&&t(19,z=A.editable),"is_static"in A&&t(20,T=A.is_static),"i18n"in A&&t(21,Y=A.i18n),"components"in A&&t(22,le=A.components),"el"in A&&t(2,K=A.el),"handle_select_column"in A&&t(23,W=A.handle_select_column),"handle_select_row"in A&&t(24,ce=A.handle_select_row),"is_dragging"in A&&t(25,B=A.is_dragging),"display_value"in A&&t(26,Z=A.display_value),"wrap"in A&&t(27,N=A.wrap)},n.$$.update=()=>{n.$$.dirty[0]&25|n.$$.dirty[1]&8&&t(35,l=w([a,u],k||[])),n.$$.dirty[0]&25&&t(32,i=So([a,u],k)),n.$$.dirty[1]&16&&t(31,r=l.includes("no-top")),n.$$.dirty[1]&16&&t(30,s=l.includes("no-bottom")),n.$$.dirty[1]&16&&t(29,o=l.includes("no-left")),n.$$.dirty[1]&16&&t(28,_=l.includes("no-right"))},[k,f,K,a,u,c,g,L,v,q,p,y,C,j,S,D,O,G,I,z,T,Y,le,W,ce,B,Z,N,_,o,s,r,i,J,w,l,b,M,m,F,Se,mt,$]}class cu extends x_{constructor(e){super(),lu(this,e,fu,uu,au,{value:1,index:3,j:4,actual_pinned_columns:5,get_cell_width:6,handle_cell_click:7,handle_blur:8,toggle_cell_menu:9,is_cell_selected:34,should_show_cell_menu:10,selected_cells:0,copy_flash:11,active_cell_menu:12,styling:13,latex_delimiters:14,line_breaks:15,datatype:16,editing:17,max_chars:18,editable:19,is_static:20,i18n:21,components:22,el:2,handle_select_column:23,handle_select_row:24,is_dragging:25,display_value:26,wrap:27},null,[-1,-1])}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),ie()}get index(){return this.$$.ctx[3]}set index(e){this.$$set({index:e}),ie()}get j(){return this.$$.ctx[4]}set j(e){this.$$set({j:e}),ie()}get actual_pinned_columns(){return this.$$.ctx[5]}set actual_pinned_columns(e){this.$$set({actual_pinned_columns:e}),ie()}get get_cell_width(){return this.$$.ctx[6]}set get_cell_width(e){this.$$set({get_cell_width:e}),ie()}get handle_cell_click(){return this.$$.ctx[7]}set handle_cell_click(e){this.$$set({handle_cell_click:e}),ie()}get handle_blur(){return this.$$.ctx[8]}set handle_blur(e){this.$$set({handle_blur:e}),ie()}get toggle_cell_menu(){return this.$$.ctx[9]}set toggle_cell_menu(e){this.$$set({toggle_cell_menu:e}),ie()}get is_cell_selected(){return this.$$.ctx[34]}set is_cell_selected(e){this.$$set({is_cell_selected:e}),ie()}get should_show_cell_menu(){return this.$$.ctx[10]}set should_show_cell_menu(e){this.$$set({should_show_cell_menu:e}),ie()}get selected_cells(){return this.$$.ctx[0]}set selected_cells(e){this.$$set({selected_cells:e}),ie()}get copy_flash(){return this.$$.ctx[11]}set copy_flash(e){this.$$set({copy_flash:e}),ie()}get active_cell_menu(){return this.$$.ctx[12]}set active_cell_menu(e){this.$$set({active_cell_menu:e}),ie()}get styling(){return this.$$.ctx[13]}set styling(e){this.$$set({styling:e}),ie()}get latex_delimiters(){return this.$$.ctx[14]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),ie()}get line_breaks(){return this.$$.ctx[15]}set line_breaks(e){this.$$set({line_breaks:e}),ie()}get datatype(){return this.$$.ctx[16]}set datatype(e){this.$$set({datatype:e}),ie()}get editing(){return this.$$.ctx[17]}set editing(e){this.$$set({editing:e}),ie()}get max_chars(){return this.$$.ctx[18]}set max_chars(e){this.$$set({max_chars:e}),ie()}get editable(){return this.$$.ctx[19]}set editable(e){this.$$set({editable:e}),ie()}get is_static(){return this.$$.ctx[20]}set is_static(e){this.$$set({is_static:e}),ie()}get i18n(){return this.$$.ctx[21]}set i18n(e){this.$$set({i18n:e}),ie()}get components(){return this.$$.ctx[22]}set components(e){this.$$set({components:e}),ie()}get el(){return this.$$.ctx[2]}set el(e){this.$$set({el:e}),ie()}get handle_select_column(){return this.$$.ctx[23]}set handle_select_column(e){this.$$set({handle_select_column:e}),ie()}get handle_select_row(){return this.$$.ctx[24]}set handle_select_row(e){this.$$set({handle_select_row:e}),ie()}get is_dragging(){return this.$$.ctx[25]}set is_dragging(e){this.$$set({is_dragging:e}),ie()}get display_value(){return this.$$.ctx[26]}set display_value(e){this.$$set({display_value:e}),ie()}get wrap(){return this.$$.ctx[27]}set wrap(e){this.$$set({wrap:e}),ie()}}const{SvelteComponent:hu,attr:Oi,detach:du,element:mu,flush:gu,init:bu,insert:wu,is_function:ku,listen:pu,noop:Ii,safe_not_equal:vu}=window.__gradio__svelte__internal;function yu(n){let e,t,l;return{c(){e=mu("button"),e.textContent="+",Oi(e,"class","add-row-button svelte-jkwuz7"),Oi(e,"aria-label","Add row")},m(i,r){wu(i,e,r),t||(l=pu(e,"click",function(){ku(n[0])&&n[0].apply(this,arguments)}),t=!0)},p(i,[r]){n=i},i:Ii,o:Ii,d(i){i&&du(e),t=!1,l()}}}function Cu(n,e,t){let{on_click:l}=e;return n.$$set=i=>{"on_click"in i&&t(0,l=i.on_click)},[l]}class Su extends hu{constructor(e){super(),bu(this,e,Cu,yu,vu,{on_click:0})}get on_click(){return this.$$.ctx[0]}set on_click(e){this.$$set({on_click:e}),gu()}}const{ResizeObserverSingleton:Nu,SvelteComponent:qu,add_iframe_resize_listener:Ri,add_render_callback:Pi,append:Mt,attr:wn,binding_callbacks:Ji,check_outros:Zs,create_slot:dl,detach:Bn,element:zt,empty:$s,ensure_array_like:Fi,flush:wt,get_all_dirty_from_scope:ml,get_slot_changes:gl,group_outros:xs,init:Mu,insert:Dn,listen:Au,outro_and_destroy_block:Eu,resize_observer_content_box:Lu,safe_not_equal:ju,set_style:We,space:Ki,text:Bu,toggle_class:Wi,transition_in:Bt,transition_out:It,update_keyed_each:Du,update_slot_base:bl}=window.__gradio__svelte__internal,{onMount:zu,tick:Tu,createEventDispatcher:Hu}=window.__gradio__svelte__internal,Ou=n=>({}),Ui=n=>({});function Vi(n,e,t){const l=n.slice();return l[37]=e[t],l}const Iu=n=>({item:n[0]&1024,index:n[0]&1024}),Yi=n=>({item:n[37].data,index:n[37].index}),Ru=n=>({}),Xi=n=>({});function Gi(n){let e=[],t=new Map,l,i,r=Fi(n[10]);const s=o=>o[37].data[0].id;for(let o=0;o<r.length;o+=1){let _=Vi(n,r,o),f=s(_);t.set(f,e[o]=Qi(f,_))}return{c(){for(let o=0;o<e.length;o+=1)e[o].c();l=$s()},m(o,_){for(let f=0;f<e.length;f+=1)e[f]&&e[f].m(o,_);Dn(o,l,_),i=!0},p(o,_){_[0]&8389632&&(r=Fi(o[10]),xs(),e=Du(e,_,s,1,o,r,t,l.parentNode,Eu,Qi,l,Vi),Zs())},i(o){if(!i){for(let _=0;_<r.length;_+=1)Bt(e[_]);i=!0}},o(o){for(let _=0;_<e.length;_+=1)It(e[_]);i=!1},d(o){o&&Bn(l);for(let _=0;_<e.length;_+=1)e[_].d(o)}}}function Pu(n){let e;return{c(){e=Bu(`Missing Table Row
						`)},m(t,l){Dn(t,e,l)},d(t){t&&Bn(e)}}}function Qi(n,e){let t,l;const i=e[24].tbody,r=dl(i,e,e[23],Yi),s=r||Pu();return{key:n,first:null,c(){t=$s(),s&&s.c(),this.first=t},m(o,_){Dn(o,t,_),s&&s.m(o,_),l=!0},p(o,_){e=o,r&&r.p&&(!l||_[0]&8389632)&&bl(r,i,e,e[23],l?gl(i,e[23],_,Iu):ml(e[23]),Yi)},i(o){l||(Bt(s,o),l=!0)},o(o){It(s,o),l=!1},d(o){o&&Bn(t),s&&s.d(o)}}}function Ju(n){let e,t,l,i,r,s,o,_,f,a,u,c,g,L;const v=n[24].thead,q=dl(v,n,n[23],Xi);let w=n[10].length&&n[10][0].data.length&&Gi(n);const p=n[24].tfoot,k=dl(p,n,n[23],Ui);return{c(){e=zt("svelte-virtual-table-viewport"),t=zt("div"),l=zt("table"),i=zt("thead"),q&&q.c(),s=Ki(),o=zt("tbody"),w&&w.c(),_=Ki(),f=zt("tfoot"),k&&k.c(),wn(i,"class","thead svelte-zsmsrz"),Pi(()=>n[25].call(i)),wn(o,"class","tbody svelte-zsmsrz"),wn(f,"class","tfoot svelte-zsmsrz"),Pi(()=>n[27].call(f)),wn(l,"class","table svelte-zsmsrz"),We(l,"height",Fu),We(l,"--bw-svt-p-top",n[9]+"px"),We(l,"--bw-svt-p-bottom",n[5]+"px"),We(l,"--bw-svt-head-height",n[7]+"px"),We(l,"--bw-svt-foot-height",n[8]+"px"),We(l,"--bw-svt-avg-row-height",n[3]+"px"),We(l,"--max-height",n[1]+"px"),Wi(l,"disable-scroll",n[2])},m(y,C){Dn(y,e,C),Mt(e,t),Mt(t,l),Mt(l,i),q&&q.m(i,null),r=Ri(i,n[25].bind(i)),Mt(l,s),Mt(l,o),w&&w.m(o,null),n[26](o),Mt(l,_),Mt(l,f),k&&k.m(f,null),a=Ri(f,n[27].bind(f)),n[28](l),u=Lu.observe(l,n[29].bind(l)),c=!0,g||(L=Au(l,"scroll",n[11]),g=!0)},p(y,C){q&&q.p&&(!c||C[0]&8388608)&&bl(q,v,y,y[23],c?gl(v,y[23],C,Ru):ml(y[23]),Xi),y[10].length&&y[10][0].data.length?w?(w.p(y,C),C[0]&1024&&Bt(w,1)):(w=Gi(y),w.c(),Bt(w,1),w.m(o,null)):w&&(xs(),It(w,1,1,()=>{w=null}),Zs()),k&&k.p&&(!c||C[0]&8388608)&&bl(k,p,y,y[23],c?gl(p,y[23],C,Ou):ml(y[23]),Ui),(!c||C[0]&512)&&We(l,"--bw-svt-p-top",y[9]+"px"),(!c||C[0]&32)&&We(l,"--bw-svt-p-bottom",y[5]+"px"),(!c||C[0]&128)&&We(l,"--bw-svt-head-height",y[7]+"px"),(!c||C[0]&256)&&We(l,"--bw-svt-foot-height",y[8]+"px"),(!c||C[0]&8)&&We(l,"--bw-svt-avg-row-height",y[3]+"px"),(!c||C[0]&2)&&We(l,"--max-height",y[1]+"px"),(!c||C[0]&4)&&Wi(l,"disable-scroll",y[2])},i(y){c||(Bt(q,y),Bt(w),Bt(k,y),c=!0)},o(y){It(q,y),It(w),It(k,y),c=!1},d(y){y&&Bn(e),q&&q.d(y),r(),w&&w.d(),n[26](null),k&&k.d(y),a(),n[28](null),u(),g=!1,L()}}}let Fu="100%";function Ku(n,e,t){let l,{$$slots:i={},$$scope:r}=e,{items:s=[]}=e,{max_height:o}=e,{actual_height:_}=e,{table_scrollbar_width:f}=e,{start:a=0}=e,{end:u=20}=e,{selected:c}=e,{disable_scroll:g=!1}=e,{show_scroll_button:L=!1}=e,{viewport:v}=e;Hu();let q=30,w=0,p,k=0,y=0,C=[],j,S,D=0,O=200,G=[],I;const z=typeof window<"u",T=z?window.requestAnimationFrame:b=>b();async function Y(){l.length<a&&await W(l.length-1,{behavior:"auto"});const b=Math.max(0,v.scrollTop);t(16,L=b>100),t(15,f=v.offsetWidth-v.clientWidth);for(let A=0;A<S.length;A+=1)C[a+A]=S[A].getBoundingClientRect().height;let M=0,m=k;for(;M<l.length;){const A=C[M]||q;if(m+A>b-o){t(12,a=M),t(9,D=m-k);break}m+=A,M+=1}let F=k;for(;M<l.length;){const A=C[M]||q;if(F+=A,M+=1,F-k>3*o)break}t(13,u=M);const Se=l.length-u,mt=v.offsetHeight-v.clientHeight;mt>0&&(F+=mt);let $=C.filter(A=>typeof A=="number");for(t(3,q=$.reduce((A,P)=>A+P,0)/$.length||30),t(5,w=Se*q),isFinite(w)||t(5,w=2e5),C.length=l.length;M<l.length;)M+=1,C[M]=q;o&&F>o?t(14,_=o):t(14,_=F)}async function le(b){T(async()=>{if(typeof b!="number")return;const M=typeof b!="number"?!1:K(b);M!==!0&&(M==="back"&&await W(b,{behavior:"instant"}),M==="forwards"&&await W(b,{behavior:"instant"},!0))})}function K(b){const M=S&&S[b-a];if(!M&&b<a)return"back";if(!M&&b>=u-1)return"forwards";const{top:m}=v.getBoundingClientRect(),{top:F,bottom:Se}=M.getBoundingClientRect();return F-m<37?"back":Se-m>O?"forwards":!0}async function W(b,M,m=!1){await Tu();const F=q;let Se=b*F;m&&(Se=Se-O+F+k);const mt=v.offsetHeight-v.clientHeight;mt>0&&(Se+=mt);const $={top:Se,behavior:"smooth",...M};v.scrollTo($)}zu(()=>{S=p.children,t(20,j=!0)});function ce(){k=this.offsetHeight,t(7,k)}function B(b){Ji[b?"unshift":"push"](()=>{p=b,t(6,p)})}function Z(){y=this.offsetHeight,t(8,y)}function N(b){Ji[b?"unshift":"push"](()=>{v=b,t(0,v)})}function J(){I=Nu.entries.get(this)?.contentRect,t(4,I)}return n.$$set=b=>{"items"in b&&t(17,s=b.items),"max_height"in b&&t(1,o=b.max_height),"actual_height"in b&&t(14,_=b.actual_height),"table_scrollbar_width"in b&&t(15,f=b.table_scrollbar_width),"start"in b&&t(12,a=b.start),"end"in b&&t(13,u=b.end),"selected"in b&&t(18,c=b.selected),"disable_scroll"in b&&t(2,g=b.disable_scroll),"show_scroll_button"in b&&t(16,L=b.show_scroll_button),"viewport"in b&&t(0,v=b.viewport),"$$scope"in b&&t(23,r=b.$$scope)},n.$$.update=()=>{n.$$.dirty[0]&16&&t(21,O=I?.height||200),n.$$.dirty[0]&131072&&t(22,l=s),n.$$.dirty[0]&7340033&&j&&O&&v.offsetParent&&T(Y),n.$$.dirty[0]&262144&&le(c),n.$$.dirty[0]&4206602&&t(10,G=z?l.slice(a,u).map((b,M)=>({index:M+a,data:b})):l.slice(0,o/l.length*q+1).map((b,M)=>({index:M+a,data:b})))},[v,o,g,q,I,w,p,k,y,D,G,Y,a,u,_,f,L,s,c,W,j,O,l,r,i,ce,B,Z,N,J]}class Wu extends qu{constructor(e){super(),Mu(this,e,Ku,Ju,ju,{items:17,max_height:1,actual_height:14,table_scrollbar_width:15,start:12,end:13,selected:18,disable_scroll:2,show_scroll_button:16,viewport:0,scroll_to_index:19},null,[-1,-1])}get items(){return this.$$.ctx[17]}set items(e){this.$$set({items:e}),wt()}get max_height(){return this.$$.ctx[1]}set max_height(e){this.$$set({max_height:e}),wt()}get actual_height(){return this.$$.ctx[14]}set actual_height(e){this.$$set({actual_height:e}),wt()}get table_scrollbar_width(){return this.$$.ctx[15]}set table_scrollbar_width(e){this.$$set({table_scrollbar_width:e}),wt()}get start(){return this.$$.ctx[12]}set start(e){this.$$set({start:e}),wt()}get end(){return this.$$.ctx[13]}set end(e){this.$$set({end:e}),wt()}get selected(){return this.$$.ctx[18]}set selected(e){this.$$set({selected:e}),wt()}get disable_scroll(){return this.$$.ctx[2]}set disable_scroll(e){this.$$set({disable_scroll:e}),wt()}get show_scroll_button(){return this.$$.ctx[16]}set show_scroll_button(e){this.$$set({show_scroll_button:e}),wt()}get viewport(){return this.$$.ctx[0]}set viewport(e){this.$$set({viewport:e}),wt()}get scroll_to_index(){return this.$$.ctx[19]}}const{SvelteComponent:Uu,append:we,attr:qe,binding_callbacks:Vu,bubble:Yu,create_component:Zi,destroy_component:$i,destroy_each:Xu,detach:Bl,element:Ue,ensure_array_like:xi,flush:Gu,init:Qu,insert:Dl,listen:Ht,mount_component:es,run_all:Zu,safe_not_equal:$u,set_data:wl,space:yt,stop_propagation:vn,text:kl,transition_in:ts,transition_out:ns}=window.__gradio__svelte__internal,{onMount:xu}=window.__gradio__svelte__internal;function ls(n,e,t){const l=n.slice();return l[15]=e[t],l}function is(n){let e,t=xi(n[6][n[2]]),l=[];for(let i=0;i<t.length;i+=1)l[i]=ss(ls(n,t,i));return{c(){e=Ue("div");for(let i=0;i<l.length;i+=1)l[i].c();qe(e,"class","dropdown-filter-options svelte-1nf5kyf")},m(i,r){Dl(i,e,r);for(let s=0;s<l.length;s+=1)l[s]&&l[s].m(e,null)},p(i,r){if(r&92){t=xi(i[6][i[2]]);let s;for(s=0;s<t.length;s+=1){const o=ls(i,t,s);l[s]?l[s].p(o,r):(l[s]=ss(o),l[s].c(),l[s].m(e,null))}for(;s<l.length;s+=1)l[s].d(1);l.length=t.length}},d(i){i&&Bl(e),Xu(l,i)}}}function ss(n){let e,t=n[15]+"",l,i,r,s;function o(){return n[11](n[15])}return{c(){e=Ue("button"),l=kl(t),i=yt(),qe(e,"class","filter-option svelte-1nf5kyf")},m(_,f){Dl(_,e,f),we(e,l),we(e,i),r||(s=Ht(e,"click",vn(o)),r=!0)},p(_,f){n=_,f&4&&t!==(t=n[15]+"")&&wl(l,t)},d(_){_&&Bl(e),r=!1,s()}}}function ef(n){let e,t,l,i,r,s,o,_,f,a,u,c,g,L,v,q,w,p,k,y,C,j,S,D,O,G,I;w=new _o({});let z=n[4]&&is(n);return D=new ql({}),{c(){e=Ue("div"),t=Ue("div"),l=yt(),i=Ue("div"),r=Ue("div"),s=Ue("span"),s.textContent="Filter as",o=yt(),_=Ue("button"),f=kl(n[2]),u=yt(),c=Ue("div"),g=Ue("div"),L=Ue("button"),v=kl(n[3]),q=yt(),Zi(w.$$.fragment),k=yt(),z&&z.c(),y=yt(),C=Ue("input"),j=yt(),S=Ue("button"),Zi(D.$$.fragment),qe(t,"class","background svelte-1nf5kyf"),qe(s,"class","svelte-1nf5kyf"),qe(_,"aria-label",a=`Change filter type. Filtering ${n[2]}s`),qe(_,"class","svelte-1nf5kyf"),qe(r,"class","filter-datatype-container svelte-1nf5kyf"),qe(L,"aria-label",p=`Change filter. Using '${n[3]}'`),qe(L,"class","svelte-1nf5kyf"),qe(g,"class","filter-dropdown"),qe(C,"type","text"),C.value=n[5],qe(C,"placeholder","Type a value"),qe(C,"class","filter-input svelte-1nf5kyf"),qe(c,"class","input-container svelte-1nf5kyf"),qe(S,"class","check-button svelte-1nf5kyf"),qe(i,"class","filter-menu svelte-1nf5kyf")},m(T,Y){Dl(T,e,Y),we(e,t),we(e,l),we(e,i),we(i,r),we(r,s),we(r,o),we(r,_),we(_,f),we(i,u),we(i,c),we(c,g),we(g,L),we(L,v),we(L,q),es(w,L,null),we(g,k),z&&z.m(g,null),we(c,y),we(c,C),we(i,j),we(i,S),es(D,S,null),n[13](i),O=!0,G||(I=[Ht(_,"click",vn(n[9])),Ht(L,"click",vn(n[10])),Ht(C,"click",vn(n[8])),Ht(C,"input",n[7]),Ht(S,"click",n[12])],G=!0)},p(T,[Y]){(!O||Y&4)&&wl(f,T[2]),(!O||Y&4&&a!==(a=`Change filter type. Filtering ${T[2]}s`))&&qe(_,"aria-label",a),(!O||Y&8)&&wl(v,T[3]),(!O||Y&8&&p!==(p=`Change filter. Using '${T[3]}'`))&&qe(L,"aria-label",p),T[4]?z?z.p(T,Y):(z=is(T),z.c(),z.m(g,null)):z&&(z.d(1),z=null),(!O||Y&32&&C.value!==T[5])&&(C.value=T[5])},i(T){O||(ts(w.$$.fragment,T),ts(D.$$.fragment,T),O=!0)},o(T){ns(w.$$.fragment,T),ns(D.$$.fragment,T),O=!1},d(T){T&&Bl(e),$i(w),z&&z.d(),$i(D),n[13](null),G=!1,Zu(I)}}}function tf(n,e,t){let{on_filter:l=()=>{}}=e,i,r="string",s="Contains",o=!1,_="";const f={string:["Contains","Does not contain","Starts with","Ends with","Is","Is not","Is empty","Is not empty"],number:["=","≠",">","<","≥","≤","Is empty","Is not empty"]};xu(()=>{a()});function a(){if(!i)return;const p=window.innerWidth,k=window.innerHeight,y=i.getBoundingClientRect(),C=(p-y.width)/2,j=(k-y.height)/2;t(1,i.style.left=`${C}px`,i),t(1,i.style.top=`${j}px`,i)}function u(p){const k=p.target;t(5,_=k.value)}function c(p){Yu.call(this,n,p)}const g=()=>{t(2,r=r==="string"?"number":"string"),t(3,s=f[r][0])},L=()=>t(4,o=!o),v=p=>{t(3,s=p),t(4,o=!o)},q=()=>l(r,s,_);function w(p){Vu[p?"unshift":"push"](()=>{i=p,t(1,i)})}return n.$$set=p=>{"on_filter"in p&&t(0,l=p.on_filter)},[l,i,r,s,o,_,f,u,c,g,L,v,q,w]}class nf extends Uu{constructor(e){super(),Qu(this,e,tf,ef,$u,{on_filter:0})}get on_filter(){return this.$$.ctx[0]}set on_filter(e){this.$$set({on_filter:e}),Gu()}}const{SvelteComponent:lf,append:oe,attr:Q,binding_callbacks:sf,check_outros:Rt,create_component:lt,destroy_component:it,detach:_e,element:Oe,empty:zl,flush:ge,group_outros:Pt,init:rf,insert:ue,is_function:Nn,listen:dt,mount_component:st,run_all:Tl,safe_not_equal:of,set_data:Xe,space:he,stop_propagation:af,text:Ge,toggle_class:Tt,transition_in:te,transition_out:me}=window.__gradio__svelte__internal,{onMount:_f}=window.__gradio__svelte__internal;function rs(n){let e,t,l,i=n[15]("dataframe.sort_ascending")+"",r,s,o,_,f,a,u=n[15]("dataframe.sort_descending")+"",c,g,L,v,q,w,p=n[15]("dataframe.clear_sort")+"",k,y,C,j,S,D=n[15]("dataframe.filter")+"",O,G,I,z,T,Y,le=n[15]("dataframe.clear_filter")+"",K,W,ce,B;t=new nt({props:{icon:"sort-asc"}});let Z=n[10]==="asc"&&n[11]!==null&&os(n);f=new nt({props:{icon:"sort-desc"}});let N=n[10]==="desc"&&n[11]!==null&&as(n);q=new nt({props:{icon:"clear-sort"}}),j=new nt({props:{icon:"filter"}});let J=n[14]&&_s();return T=new nt({props:{icon:"clear-filter"}}),{c(){e=Oe("button"),lt(t.$$.fragment),l=he(),r=Ge(i),s=he(),Z&&Z.c(),o=he(),_=Oe("button"),lt(f.$$.fragment),a=he(),c=Ge(u),g=he(),N&&N.c(),L=he(),v=Oe("button"),lt(q.$$.fragment),w=he(),k=Ge(p),y=he(),C=Oe("button"),lt(j.$$.fragment),S=he(),O=Ge(D),G=he(),J&&J.c(),I=he(),z=Oe("button"),lt(T.$$.fragment),Y=he(),K=Ge(le),Q(e,"role","menuitem"),Q(e,"class","svelte-42thj4"),Tt(e,"active",n[10]==="asc"),Q(_,"role","menuitem"),Q(_,"class","svelte-42thj4"),Tt(_,"active",n[10]==="desc"),Q(v,"role","menuitem"),Q(v,"class","svelte-42thj4"),Q(C,"role","menuitem"),Q(C,"class","svelte-42thj4"),Tt(C,"active",n[14]||n[17]),Q(z,"role","menuitem"),Q(z,"class","svelte-42thj4")},m(b,M){ue(b,e,M),st(t,e,null),oe(e,l),oe(e,r),oe(e,s),Z&&Z.m(e,null),ue(b,o,M),ue(b,_,M),st(f,_,null),oe(_,a),oe(_,c),oe(_,g),N&&N.m(_,null),ue(b,L,M),ue(b,v,M),st(q,v,null),oe(v,w),oe(v,k),ue(b,y,M),ue(b,C,M),st(j,C,null),oe(C,S),oe(C,O),oe(C,G),J&&J.m(C,null),ue(b,I,M),ue(b,z,M),st(T,z,null),oe(z,Y),oe(z,K),W=!0,ce||(B=[dt(e,"click",n[28]),dt(_,"click",n[29]),dt(v,"click",function(){Nn(n[9])&&n[9].apply(this,arguments)}),dt(C,"click",af(n[21])),dt(z,"click",function(){Nn(n[13])&&n[13].apply(this,arguments)})],ce=!0)},p(b,M){n=b,(!W||M[0]&32768)&&i!==(i=n[15]("dataframe.sort_ascending")+"")&&Xe(r,i),n[10]==="asc"&&n[11]!==null?Z?Z.p(n,M):(Z=os(n),Z.c(),Z.m(e,null)):Z&&(Z.d(1),Z=null),(!W||M[0]&1024)&&Tt(e,"active",n[10]==="asc"),(!W||M[0]&32768)&&u!==(u=n[15]("dataframe.sort_descending")+"")&&Xe(c,u),n[10]==="desc"&&n[11]!==null?N?N.p(n,M):(N=as(n),N.c(),N.m(_,null)):N&&(N.d(1),N=null),(!W||M[0]&1024)&&Tt(_,"active",n[10]==="desc"),(!W||M[0]&32768)&&p!==(p=n[15]("dataframe.clear_sort")+"")&&Xe(k,p),(!W||M[0]&32768)&&D!==(D=n[15]("dataframe.filter")+"")&&Xe(O,D),n[14]?J||(J=_s(),J.c(),J.m(C,null)):J&&(J.d(1),J=null),(!W||M[0]&147456)&&Tt(C,"active",n[14]||n[17]),(!W||M[0]&32768)&&le!==(le=n[15]("dataframe.clear_filter")+"")&&Xe(K,le)},i(b){W||(te(t.$$.fragment,b),te(f.$$.fragment,b),te(q.$$.fragment,b),te(j.$$.fragment,b),te(T.$$.fragment,b),W=!0)},o(b){me(t.$$.fragment,b),me(f.$$.fragment,b),me(q.$$.fragment,b),me(j.$$.fragment,b),me(T.$$.fragment,b),W=!1},d(b){b&&(_e(e),_e(o),_e(_),_e(L),_e(v),_e(y),_e(C),_e(I),_e(z)),it(t),Z&&Z.d(),it(f),N&&N.d(),it(q),it(j),J&&J.d(),it(T),ce=!1,Tl(B)}}}function os(n){let e,t;return{c(){e=Oe("span"),t=Ge(n[11]),Q(e,"class","priority svelte-42thj4")},m(l,i){ue(l,e,i),oe(e,t)},p(l,i){i[0]&2048&&Xe(t,l[11])},d(l){l&&_e(e)}}}function as(n){let e,t;return{c(){e=Oe("span"),t=Ge(n[11]),Q(e,"class","priority svelte-42thj4")},m(l,i){ue(l,e,i),oe(e,t)},p(l,i){i[0]&2048&&Xe(t,l[11])},d(l){l&&_e(e)}}}function _s(n){let e;return{c(){e=Oe("span"),e.textContent="1",Q(e,"class","priority svelte-42thj4")},m(t,l){ue(t,e,l)},d(t){t&&_e(e)}}}function us(n){let e,t,l,i=n[15]("dataframe.add_row_above")+"",r,s,o,_,f,a=n[15]("dataframe.add_row_below")+"",u,c,g,L,v,q;t=new nt({props:{icon:"add-row-above"}}),_=new nt({props:{icon:"add-row-below"}});let w=n[6]&&fs(n);return{c(){e=Oe("button"),lt(t.$$.fragment),l=he(),r=Ge(i),s=he(),o=Oe("button"),lt(_.$$.fragment),f=he(),u=Ge(a),c=he(),w&&w.c(),g=zl(),Q(e,"role","menuitem"),Q(e,"aria-label","Add row above"),Q(e,"class","svelte-42thj4"),Q(o,"role","menuitem"),Q(o,"aria-label","Add row below"),Q(o,"class","svelte-42thj4")},m(p,k){ue(p,e,k),st(t,e,null),oe(e,l),oe(e,r),ue(p,s,k),ue(p,o,k),st(_,o,null),oe(o,f),oe(o,u),ue(p,c,k),w&&w.m(p,k),ue(p,g,k),L=!0,v||(q=[dt(e,"click",n[30]),dt(o,"click",n[31])],v=!0)},p(p,k){(!L||k[0]&32768)&&i!==(i=p[15]("dataframe.add_row_above")+"")&&Xe(r,i),(!L||k[0]&32768)&&a!==(a=p[15]("dataframe.add_row_below")+"")&&Xe(u,a),p[6]?w?(w.p(p,k),k[0]&64&&te(w,1)):(w=fs(p),w.c(),te(w,1),w.m(g.parentNode,g)):w&&(Pt(),me(w,1,1,()=>{w=null}),Rt())},i(p){L||(te(t.$$.fragment,p),te(_.$$.fragment,p),te(w),L=!0)},o(p){me(t.$$.fragment,p),me(_.$$.fragment,p),me(w),L=!1},d(p){p&&(_e(e),_e(s),_e(o),_e(c),_e(g)),it(t),it(_),w&&w.d(p),v=!1,Tl(q)}}}function fs(n){let e,t,l,i=n[15]("dataframe.delete_row")+"",r,s,o,_;return t=new nt({props:{icon:"delete-row"}}),{c(){e=Oe("button"),lt(t.$$.fragment),l=he(),r=Ge(i),Q(e,"role","menuitem"),Q(e,"class","delete svelte-42thj4"),Q(e,"aria-label","Delete row")},m(f,a){ue(f,e,a),st(t,e,null),oe(e,l),oe(e,r),s=!0,o||(_=dt(e,"click",function(){Nn(n[4])&&n[4].apply(this,arguments)}),o=!0)},p(f,a){n=f,(!s||a[0]&32768)&&i!==(i=n[15]("dataframe.delete_row")+"")&&Xe(r,i)},i(f){s||(te(t.$$.fragment,f),s=!0)},o(f){me(t.$$.fragment,f),s=!1},d(f){f&&_e(e),it(t),o=!1,_()}}}function cs(n){let e,t,l,i=n[15]("dataframe.add_column_left")+"",r,s,o,_,f,a=n[15]("dataframe.add_column_right")+"",u,c,g,L,v,q;t=new nt({props:{icon:"add-column-left"}}),_=new nt({props:{icon:"add-column-right"}});let w=n[7]&&hs(n);return{c(){e=Oe("button"),lt(t.$$.fragment),l=he(),r=Ge(i),s=he(),o=Oe("button"),lt(_.$$.fragment),f=he(),u=Ge(a),c=he(),w&&w.c(),g=zl(),Q(e,"role","menuitem"),Q(e,"aria-label","Add column to the left"),Q(e,"class","svelte-42thj4"),Q(o,"role","menuitem"),Q(o,"aria-label","Add column to the right"),Q(o,"class","svelte-42thj4")},m(p,k){ue(p,e,k),st(t,e,null),oe(e,l),oe(e,r),ue(p,s,k),ue(p,o,k),st(_,o,null),oe(o,f),oe(o,u),ue(p,c,k),w&&w.m(p,k),ue(p,g,k),L=!0,v||(q=[dt(e,"click",n[32]),dt(o,"click",n[33])],v=!0)},p(p,k){(!L||k[0]&32768)&&i!==(i=p[15]("dataframe.add_column_left")+"")&&Xe(r,i),(!L||k[0]&32768)&&a!==(a=p[15]("dataframe.add_column_right")+"")&&Xe(u,a),p[7]?w?(w.p(p,k),k[0]&128&&te(w,1)):(w=hs(p),w.c(),te(w,1),w.m(g.parentNode,g)):w&&(Pt(),me(w,1,1,()=>{w=null}),Rt())},i(p){L||(te(t.$$.fragment,p),te(_.$$.fragment,p),te(w),L=!0)},o(p){me(t.$$.fragment,p),me(_.$$.fragment,p),me(w),L=!1},d(p){p&&(_e(e),_e(s),_e(o),_e(c),_e(g)),it(t),it(_),w&&w.d(p),v=!1,Tl(q)}}}function hs(n){let e,t,l,i=n[15]("dataframe.delete_column")+"",r,s,o,_;return t=new nt({props:{icon:"delete-column"}}),{c(){e=Oe("button"),lt(t.$$.fragment),l=he(),r=Ge(i),Q(e,"role","menuitem"),Q(e,"class","delete svelte-42thj4"),Q(e,"aria-label","Delete column")},m(f,a){ue(f,e,a),st(t,e,null),oe(e,l),oe(e,r),s=!0,o||(_=dt(e,"click",function(){Nn(n[5])&&n[5].apply(this,arguments)}),o=!0)},p(f,a){n=f,(!s||a[0]&32768)&&i!==(i=n[15]("dataframe.delete_column")+"")&&Xe(r,i)},i(f){s||(te(t.$$.fragment,f),s=!0)},o(f){me(t.$$.fragment,f),s=!1},d(f){f&&_e(e),it(t),o=!1,_()}}}function ds(n){let e,t;return e=new nf({props:{on_filter:n[12]}}),{c(){lt(e.$$.fragment)},m(l,i){st(e,l,i),t=!0},p(l,i){const r={};i[0]&4096&&(r.on_filter=l[12]),e.$set(r)},i(l){t||(te(e.$$.fragment,l),t=!0)},o(l){me(e.$$.fragment,l),t=!1},d(l){it(e,l)}}}function uf(n){let e,t,l,i,r,s,o=n[20]&&rs(n),_=!n[20]&&n[19]&&us(n),f=n[18]&&cs(n),a=n[17]&&ds(n);return{c(){e=Oe("div"),o&&o.c(),t=he(),_&&_.c(),l=he(),f&&f.c(),i=he(),a&&a.c(),r=zl(),Q(e,"class","cell-menu svelte-42thj4"),Q(e,"role","menu")},m(u,c){ue(u,e,c),o&&o.m(e,null),oe(e,t),_&&_.m(e,null),oe(e,l),f&&f.m(e,null),n[34](e),ue(u,i,c),a&&a.m(u,c),ue(u,r,c),s=!0},p(u,c){u[20]?o?(o.p(u,c),c[0]&1048576&&te(o,1)):(o=rs(u),o.c(),te(o,1),o.m(e,t)):o&&(Pt(),me(o,1,1,()=>{o=null}),Rt()),!u[20]&&u[19]?_?(_.p(u,c),c[0]&1572864&&te(_,1)):(_=us(u),_.c(),te(_,1),_.m(e,l)):_&&(Pt(),me(_,1,1,()=>{_=null}),Rt()),u[18]?f?(f.p(u,c),c[0]&262144&&te(f,1)):(f=cs(u),f.c(),te(f,1),f.m(e,null)):f&&(Pt(),me(f,1,1,()=>{f=null}),Rt()),u[17]?a?(a.p(u,c),c[0]&131072&&te(a,1)):(a=ds(u),a.c(),te(a,1),a.m(r.parentNode,r)):a&&(Pt(),me(a,1,1,()=>{a=null}),Rt())},i(u){s||(te(o),te(_),te(f),te(a),s=!0)},o(u){me(o),me(_),me(f),me(a),s=!1},d(u){u&&(_e(e),_e(i),_e(r)),o&&o.d(),_&&_.d(),f&&f.d(),n[34](null),a&&a.d(u)}}}function ff(n,e,t){let l,i,r,{x:s}=e,{y:o}=e,{on_add_row_above:_}=e,{on_add_row_below:f}=e,{on_add_column_left:a}=e,{on_add_column_right:u}=e,{row:c}=e,{col_count:g}=e,{row_count:L}=e,{on_delete_row:v}=e,{on_delete_col:q}=e,{can_delete_rows:w}=e,{can_delete_cols:p}=e,{on_sort:k=()=>{}}=e,{on_clear_sort:y=()=>{}}=e,{sort_direction:C=null}=e,{sort_priority:j=null}=e,{on_filter:S=()=>{}}=e,{on_clear_filter:D=()=>{}}=e,{filter_active:O=null}=e,{editable:G=!0}=e,{i18n:I}=e,z,T=null;_f(()=>{Y()});function Y(){if(!z)return;const b=window.innerWidth,M=window.innerHeight,m=z.getBoundingClientRect();let F=s-30,Se=o-20;F+m.width>b&&(F=s-m.width+10),Se+m.height>M&&(Se=o-m.height+10),t(16,z.style.left=`${F}px`,z),t(16,z.style.top=`${Se}px`,z)}function le(){if(O){S("string","","");return}const b=z.getBoundingClientRect();t(17,T={x:b.right,y:b.top+b.height/2})}const K=()=>k("asc"),W=()=>k("desc"),ce=()=>_(),B=()=>f(),Z=()=>a(),N=()=>u();function J(b){sf[b?"unshift":"push"](()=>{z=b,t(16,z)})}return n.$$set=b=>{"x"in b&&t(22,s=b.x),"y"in b&&t(23,o=b.y),"on_add_row_above"in b&&t(0,_=b.on_add_row_above),"on_add_row_below"in b&&t(1,f=b.on_add_row_below),"on_add_column_left"in b&&t(2,a=b.on_add_column_left),"on_add_column_right"in b&&t(3,u=b.on_add_column_right),"row"in b&&t(24,c=b.row),"col_count"in b&&t(25,g=b.col_count),"row_count"in b&&t(26,L=b.row_count),"on_delete_row"in b&&t(4,v=b.on_delete_row),"on_delete_col"in b&&t(5,q=b.on_delete_col),"can_delete_rows"in b&&t(6,w=b.can_delete_rows),"can_delete_cols"in b&&t(7,p=b.can_delete_cols),"on_sort"in b&&t(8,k=b.on_sort),"on_clear_sort"in b&&t(9,y=b.on_clear_sort),"sort_direction"in b&&t(10,C=b.sort_direction),"sort_priority"in b&&t(11,j=b.sort_priority),"on_filter"in b&&t(12,S=b.on_filter),"on_clear_filter"in b&&t(13,D=b.on_clear_filter),"filter_active"in b&&t(14,O=b.filter_active),"editable"in b&&t(27,G=b.editable),"i18n"in b&&t(15,I=b.i18n)},n.$$.update=()=>{n.$$.dirty[0]&16777216&&t(20,l=c===-1),n.$$.dirty[0]&201326592&&t(19,i=G&&L[1]==="dynamic"),n.$$.dirty[0]&167772160&&t(18,r=G&&g[1]==="dynamic")},[_,f,a,u,v,q,w,p,k,y,C,j,S,D,O,I,z,T,r,i,l,le,s,o,c,g,L,G,K,W,ce,B,Z,N,J]}class cf extends lf{constructor(e){super(),rf(this,e,ff,uf,of,{x:22,y:23,on_add_row_above:0,on_add_row_below:1,on_add_column_left:2,on_add_column_right:3,row:24,col_count:25,row_count:26,on_delete_row:4,on_delete_col:5,can_delete_rows:6,can_delete_cols:7,on_sort:8,on_clear_sort:9,sort_direction:10,sort_priority:11,on_filter:12,on_clear_filter:13,filter_active:14,editable:27,i18n:15},null,[-1,-1])}get x(){return this.$$.ctx[22]}set x(e){this.$$set({x:e}),ge()}get y(){return this.$$.ctx[23]}set y(e){this.$$set({y:e}),ge()}get on_add_row_above(){return this.$$.ctx[0]}set on_add_row_above(e){this.$$set({on_add_row_above:e}),ge()}get on_add_row_below(){return this.$$.ctx[1]}set on_add_row_below(e){this.$$set({on_add_row_below:e}),ge()}get on_add_column_left(){return this.$$.ctx[2]}set on_add_column_left(e){this.$$set({on_add_column_left:e}),ge()}get on_add_column_right(){return this.$$.ctx[3]}set on_add_column_right(e){this.$$set({on_add_column_right:e}),ge()}get row(){return this.$$.ctx[24]}set row(e){this.$$set({row:e}),ge()}get col_count(){return this.$$.ctx[25]}set col_count(e){this.$$set({col_count:e}),ge()}get row_count(){return this.$$.ctx[26]}set row_count(e){this.$$set({row_count:e}),ge()}get on_delete_row(){return this.$$.ctx[4]}set on_delete_row(e){this.$$set({on_delete_row:e}),ge()}get on_delete_col(){return this.$$.ctx[5]}set on_delete_col(e){this.$$set({on_delete_col:e}),ge()}get can_delete_rows(){return this.$$.ctx[6]}set can_delete_rows(e){this.$$set({can_delete_rows:e}),ge()}get can_delete_cols(){return this.$$.ctx[7]}set can_delete_cols(e){this.$$set({can_delete_cols:e}),ge()}get on_sort(){return this.$$.ctx[8]}set on_sort(e){this.$$set({on_sort:e}),ge()}get on_clear_sort(){return this.$$.ctx[9]}set on_clear_sort(e){this.$$set({on_clear_sort:e}),ge()}get sort_direction(){return this.$$.ctx[10]}set sort_direction(e){this.$$set({sort_direction:e}),ge()}get sort_priority(){return this.$$.ctx[11]}set sort_priority(e){this.$$set({sort_priority:e}),ge()}get on_filter(){return this.$$.ctx[12]}set on_filter(e){this.$$set({on_filter:e}),ge()}get on_clear_filter(){return this.$$.ctx[13]}set on_clear_filter(e){this.$$set({on_clear_filter:e}),ge()}get filter_active(){return this.$$.ctx[14]}set filter_active(e){this.$$set({filter_active:e}),ge()}get editable(){return this.$$.ctx[27]}set editable(e){this.$$set({editable:e}),ge()}get i18n(){return this.$$.ctx[15]}set i18n(e){this.$$set({i18n:e}),ge()}}const{SvelteComponent:hf,append:sn,attr:Ce,bubble:df,check_outros:rn,create_component:zn,destroy_component:Tn,detach:Hn,element:Wt,flush:At,group_outros:on,init:mf,insert:On,is_function:gf,listen:Hl,mount_component:In,safe_not_equal:bf,space:pl,toggle_class:ms,transition_in:Ae,transition_out:Fe}=window.__gradio__svelte__internal,{onDestroy:wf}=window.__gradio__svelte__internal,{createEventDispatcher:kf}=window.__gradio__svelte__internal;function gs(n){let e,t,l,i,r,s,o,_,f,a=n[0]&&n[3]==="filter"&&bs(n);return{c(){e=Wt("div"),t=Wt("input"),s=pl(),a&&a.c(),Ce(t,"type","text"),t.value=l=n[0]||"",Ce(t,"placeholder",i=n[3]==="filter"?"Filter...":"Search..."),Ce(t,"class","search-input svelte-b1nr0g"),Ce(t,"title",r=`Enter text to ${n[3]} the table`),ms(t,"filter-mode",n[3]==="filter"),Ce(e,"class","search-container svelte-b1nr0g")},m(u,c){On(u,e,c),sn(e,t),sn(e,s),a&&a.m(e,null),o=!0,_||(f=Hl(t,"input",n[7]),_=!0)},p(u,c){(!o||c&1&&l!==(l=u[0]||"")&&t.value!==l)&&(t.value=l),(!o||c&8&&i!==(i=u[3]==="filter"?"Filter...":"Search..."))&&Ce(t,"placeholder",i),(!o||c&8&&r!==(r=`Enter text to ${u[3]} the table`))&&Ce(t,"title",r),(!o||c&8)&&ms(t,"filter-mode",u[3]==="filter"),u[0]&&u[3]==="filter"?a?(a.p(u,c),c&9&&Ae(a,1)):(a=bs(u),a.c(),Ae(a,1),a.m(e,null)):a&&(on(),Fe(a,1,1,()=>{a=null}),rn())},i(u){o||(Ae(a),o=!0)},o(u){Fe(a),o=!1},d(u){u&&Hn(e),a&&a.d(),_=!1,f()}}}function bs(n){let e,t,l,i,r;return t=new ql({}),{c(){e=Wt("button"),zn(t.$$.fragment),Ce(e,"class","toolbar-button check-button svelte-b1nr0g"),Ce(e,"aria-label","Apply filter and update dataframe values"),Ce(e,"title","Apply filter and update dataframe values")},m(s,o){On(s,e,o),In(t,e,null),l=!0,i||(r=Hl(e,"click",function(){gf(n[5])&&n[5].apply(this,arguments)}),i=!0)},p(s,o){n=s},i(s){l||(Ae(t.$$.fragment,s),l=!0)},o(s){Fe(t.$$.fragment,s),l=!1},d(s){s&&Hn(e),Tn(t),i=!1,r()}}}function ws(n){let e,t,l,i,r,s,o,_;const f=[vf,pf],a=[];function u(c,g){return c[6]?0:1}return t=u(n),l=a[t]=f[t](n),{c(){e=Wt("button"),l.c(),Ce(e,"class","toolbar-button svelte-b1nr0g"),Ce(e,"aria-label",i=n[6]?"Copied to clipboard":"Copy table data"),Ce(e,"title",r=n[6]?"Copied to clipboard":"Copy table data")},m(c,g){On(c,e,g),a[t].m(e,null),s=!0,o||(_=Hl(e,"click",n[8]),o=!0)},p(c,g){let L=t;t=u(c),t!==L&&(on(),Fe(a[L],1,1,()=>{a[L]=null}),rn(),l=a[t],l||(l=a[t]=f[t](c),l.c()),Ae(l,1),l.m(e,null)),(!s||g&64&&i!==(i=c[6]?"Copied to clipboard":"Copy table data"))&&Ce(e,"aria-label",i),(!s||g&64&&r!==(r=c[6]?"Copied to clipboard":"Copy table data"))&&Ce(e,"title",r)},i(c){s||(Ae(l),s=!0)},o(c){Fe(l),s=!1},d(c){c&&Hn(e),a[t].d(),o=!1,_()}}}function pf(n){let e,t;return e=new uo({}),{c(){zn(e.$$.fragment)},m(l,i){In(e,l,i),t=!0},i(l){t||(Ae(e.$$.fragment,l),t=!0)},o(l){Fe(e.$$.fragment,l),t=!1},d(l){Tn(e,l)}}}function vf(n){let e,t;return e=new ql({}),{c(){zn(e.$$.fragment)},m(l,i){In(e,l,i),t=!0},i(l){t||(Ae(e.$$.fragment,l),t=!0)},o(l){Fe(e.$$.fragment,l),t=!1},d(l){Tn(e,l)}}}function ks(n){let e,t;return e=new fo({props:{fullscreen:n[4]}}),e.$on("fullscreen",n[10]),{c(){zn(e.$$.fragment)},m(l,i){In(e,l,i),t=!0},p(l,i){const r={};i&16&&(r.fullscreen=l[4]),e.$set(r)},i(l){t||(Ae(e.$$.fragment,l),t=!0)},o(l){Fe(e.$$.fragment,l),t=!1},d(l){Tn(e,l)}}}function yf(n){let e,t,l,i,r,s=n[3]!=="none"&&gs(n),o=n[2]&&ws(n),_=n[1]&&ks(n);return{c(){e=Wt("div"),t=Wt("div"),s&&s.c(),l=pl(),o&&o.c(),i=pl(),_&&_.c(),Ce(t,"class","toolbar-buttons svelte-b1nr0g"),Ce(e,"class","toolbar svelte-b1nr0g"),Ce(e,"role","toolbar"),Ce(e,"aria-label","Table actions")},m(f,a){On(f,e,a),sn(e,t),s&&s.m(t,null),sn(t,l),o&&o.m(t,null),sn(t,i),_&&_.m(t,null),r=!0},p(f,[a]){f[3]!=="none"?s?(s.p(f,a),a&8&&Ae(s,1)):(s=gs(f),s.c(),Ae(s,1),s.m(t,l)):s&&(on(),Fe(s,1,1,()=>{s=null}),rn()),f[2]?o?(o.p(f,a),a&4&&Ae(o,1)):(o=ws(f),o.c(),Ae(o,1),o.m(t,i)):o&&(on(),Fe(o,1,1,()=>{o=null}),rn()),f[1]?_?(_.p(f,a),a&2&&Ae(_,1)):(_=ks(f),_.c(),Ae(_,1),_.m(t,null)):_&&(on(),Fe(_,1,1,()=>{_=null}),rn())},i(f){r||(Ae(s),Ae(o),Ae(_),r=!0)},o(f){Fe(s),Fe(o),Fe(_),r=!1},d(f){f&&Hn(e),s&&s.d(),o&&o.d(),_&&_.d()}}}function Cf(n,e,t){let{show_fullscreen_button:l=!1}=e,{show_copy_button:i=!1}=e,{show_search:r="none"}=e,{fullscreen:s=!1}=e,{on_copy:o}=e,{on_commit_filter:_}=e;const f=kf();let a=!1,u,{current_search_query:c=null}=e,g="";function L(p){g=p.target.value;const y=g||null;c!==y&&(t(0,c=y),f("search",c))}function v(){t(6,a=!0),u&&clearTimeout(u),u=setTimeout(()=>{t(6,a=!1)},2e3)}async function q(){await o(),v()}wf(()=>{u&&clearTimeout(u)});function w(p){df.call(this,n,p)}return n.$$set=p=>{"show_fullscreen_button"in p&&t(1,l=p.show_fullscreen_button),"show_copy_button"in p&&t(2,i=p.show_copy_button),"show_search"in p&&t(3,r=p.show_search),"fullscreen"in p&&t(4,s=p.fullscreen),"on_copy"in p&&t(9,o=p.on_copy),"on_commit_filter"in p&&t(5,_=p.on_commit_filter),"current_search_query"in p&&t(0,c=p.current_search_query)},[c,l,i,r,s,_,a,L,q,o,w]}class Sf extends hf{constructor(e){super(),mf(this,e,Cf,yf,bf,{show_fullscreen_button:1,show_copy_button:2,show_search:3,fullscreen:4,on_copy:9,on_commit_filter:5,current_search_query:0})}get show_fullscreen_button(){return this.$$.ctx[1]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),At()}get show_copy_button(){return this.$$.ctx[2]}set show_copy_button(e){this.$$set({show_copy_button:e}),At()}get show_search(){return this.$$.ctx[3]}set show_search(e){this.$$set({show_search:e}),At()}get fullscreen(){return this.$$.ctx[4]}set fullscreen(e){this.$$set({fullscreen:e}),At()}get on_copy(){return this.$$.ctx[9]}set on_copy(e){this.$$set({on_copy:e}),At()}get on_commit_filter(){return this.$$.ctx[5]}set on_commit_filter(e){this.$$set({on_commit_filter:e}),At()}get current_search_query(){return this.$$.ctx[0]}set current_search_query(e){this.$$set({current_search_query:e}),At()}}function kn(n,e,t,l){let i=n||[];if(e[1]==="fixed"&&i.length<e[0]){const r=Array(e[0]-i.length).fill("").map((s,o)=>`${o+i.length}`);i=i.concat(r)}return!i||i.length===0?Array(e[0]).fill(0).map((r,s)=>{const o=l();return t[o]={cell:null,input:null},{id:o,value:JSON.stringify(s+1)}}):i.map((r,s)=>{const o=l();return t[o]={cell:null,input:null},{id:o,value:r??""}})}function Nf(n,e,t,l,i=null){return!n||n.length===0?[]:n.map((s,o)=>s.map((_,f)=>{const a=l();e[a]={cell:null,input:null},t[a]=_;let u=i?.[o]?.[f];return u===void 0&&(u=String(_)),{id:a,value:_,display_value:u}}))}function qf(n,e){if(e==="number"){const t=Number(n);return isNaN(t)?n:t}if(e==="bool"){if(typeof n=="boolean")return n;if(typeof n=="number")return n!==0;const t=String(n).toLowerCase();return t==="true"||t==="1"?!0:t==="false"||t==="0"?!1:n}if(e==="date"){const t=new Date(n);return isNaN(t.getTime())?n:t.toISOString()}return n}const{tick:er}=window.__gradio__svelte__internal;async function tr(n,e,t,l){if(!e.data||!e.data[t]||!e.data[t][l])return;const i=e.data[t][l].value;e.data[t][l].value=n,i!==n&&e.dispatch&&e.dispatch("change",{data:e.data.map(r=>r.map(s=>s.value)),headers:e.headers?.map(r=>r.value)||[],metadata:null}),e.actions.set_selected([t,l])}async function Mf(n,e,t){if(!e.data||!e.headers||!e.els)return;const l=n.target;!l||l.value===void 0||await tr(l.type==="checkbox"?String(l.checked):l.value,e,t[0],t[1])}function Af(n,e){const t=Le(e.state),l=t.ui_state.selected_header,i=t.ui_state.header_edit,r=e.headers||[];if(l===!1||i!==!1)return!1;switch(n.key){case"ArrowDown":return e.actions.set_selected_header(!1),e.actions.set_selected([0,l]),e.actions.set_selected_cells([[0,l]]),!0;case"ArrowLeft":return e.actions.set_selected_header(l>0?l-1:l),!0;case"ArrowRight":return e.actions.set_selected_header(l<r.length-1?l+1:l),!0;case"Escape":return n.preventDefault(),e.actions.set_selected_header(!1),!0;case"Enter":return n.preventDefault(),t.config.editable&&e.actions.set_header_edit(l),!0}return!1}function nr(n,e){if(!e.data||!e.headers||!e.els||!e.dispatch)return!1;const t=Le(e.state);if(!t.config.editable||n.key!=="Delete"&&n.key!=="Backspace")return!1;const l=t.ui_state.editing,i=t.ui_state.selected_cells,r=t.config.static_columns||[];if(i.some(([s,o])=>r.includes(o)))return!1;if(l){const[s,o]=l,_=e.els[e.data[s][o].id]?.input;if(_&&_.selectionStart!==_.selectionEnd||n.key==="Delete"&&_?.selectionStart!==_?.value.length||n.key==="Backspace"&&_?.selectionStart!==0)return!1}if(n.preventDefault(),i.length>0){const s=Mo(e.data,i);e.dispatch("change",{data:s.map(o=>o.map(_=>_.value)),headers:e.headers.map(o=>o.value),metadata:null})}return!0}function Ef(n,e,t,l){const i=Le(e.state),r=i.ui_state.editing,s=i.ui_state.selected_cells;if(r||!e.data)return!1;n.preventDefault();const o=e.actions.move_cursor(n,[t,l],e.data);return o?(n.shiftKey?(e.actions.set_selected_cells(e.actions.get_range_selection(s.length>0?s[0]:[t,l],o)),e.actions.set_editing(!1)):(e.actions.set_selected_cells([o]),e.actions.set_editing(!1)),e.actions.set_selected(o)):o===!1&&n.key==="ArrowUp"&&t===0&&(e.actions.set_selected_header(l),e.actions.set_selected(!1),e.actions.set_selected_cells([]),e.actions.set_editing(!1)),!0}async function Lf(n,e,t,l){if(!e.data||!e.els)return!1;const i=Le(e.state);if(!i.config.editable)return!1;const r=i.ui_state.editing;if(r&&n.shiftKey)return!1;if(n.preventDefault(),r&&ln(r,[t,l])){const s=e.data[t][l].id,o=e.els[s]?.input;o&&await tr(o.value,e,t,l),e.actions.set_editing(!1),await er(),e.parent_element?.focus()}else e.actions.set_editing([t,l]);return!0}function jf(n,e,t,l){if(!e.data)return!1;n.preventDefault(),e.actions.set_editing(!1);const i=e.actions.get_next_cell_coordinates([t,l],e.data,n.shiftKey);return i&&(e.actions.set_selected_cells([i]),e.actions.set_selected(i),Le(e.state).config.editable&&e.actions.set_editing(i)),!0}function Bf(n,e,t,l){const i=Le(e.state);if(!i.config.editable)return!1;const r=i.ui_state.editing;return(!r||r&&ln(r,[t,l]))&&n.key.length===1?(e.actions.set_editing([t,l]),!0):!1}async function Df(n,e){if(!e.data)return!1;const t=Le(e.state),l=t.ui_state.selected,i=t.ui_state.selected_cells;if(!l)return!1;if(n.key==="c"&&(n.metaKey||n.ctrlKey))return n.preventDefault(),i.length>0&&await Fs(e.data,i),e.actions.set_copy_flash(!0),!0;const[r,s]=l;switch(n.key){case"ArrowRight":case"ArrowLeft":case"ArrowDown":case"ArrowUp":return Ef(n,e,r,s);case"Escape":return t.config.editable?(n.preventDefault(),e.actions.set_editing(!1),er().then(()=>{e.parent_element&&e.parent_element.focus()}),!0):!1;case"Enter":return await Lf(n,e,r,s);case"Tab":return jf(n,e,r,s);case"Delete":case"Backspace":return nr(n,e);default:return Bf(n,e,r,s)}}async function zf(n,e){Af(n,e)||nr(n,e)||await Df(n,e)}function Tf(n,e,t,l,i,r,s){const o=(a,u,c)=>{const g=a.target,L=g.type==="checkbox"||g.closest('input[type="checkbox"]')||g.closest(".bool-cell");a.target instanceof HTMLAnchorElement||r&&c===-1||L||(a.preventDefault(),a.stopPropagation(),n.mouse_down_pos={x:a.clientX,y:a.clientY},n.drag_start=[u,c],!a.shiftKey&&!a.metaKey&&!a.ctrlKey&&(t([[u,c]]),l([u,c]),i(a,u,c)))},_=a=>{const u=a.target.closest("td");if(!u)return;const c=parseInt(u.getAttribute("data-row")||"0"),g=parseInt(u.getAttribute("data-col")||"0");if(isNaN(c)||isNaN(g))return;const L=Ml(n.drag_start,[c,g]);t(L),l([c,g])},f=a=>{!n.is_dragging&&n.drag_start?i(a,n.drag_start[0],n.drag_start[1]):n.is_dragging&&s&&s.focus(),n.is_dragging=!1,e(!1),n.drag_start=null,n.mouse_down_pos=null};return{handle_mouse_down:o,handle_mouse_move(a){if(!n.drag_start||!n.mouse_down_pos)return;if(!(a.buttons&1)){f(a);return}const u=Math.abs(a.clientX-n.mouse_down_pos.x),c=Math.abs(a.clientY-n.mouse_down_pos.y);!n.is_dragging&&(u>3||c>3)&&(n.is_dragging=!0,e(!0)),n.is_dragging&&_(a)},handle_mouse_up:f}}const{SvelteComponent:Hf,add_flush_callback:rt,append:be,attr:de,bind:ot,binding_callbacks:Ie,bubble:Of,check_outros:ct,component_subscribe:If,create_component:Qe,destroy_component:Ze,detach:je,element:Ee,empty:an,ensure_array_like:St,flush:se,globals:Rf,group_outros:ht,init:Pf,insert:Be,is_function:ul,listen:Ot,mount_component:$e,noop:lr,outro_and_destroy_block:qn,run_all:Jf,safe_not_equal:Ff,set_data:Ol,set_style:ps,space:Ve,text:Il,toggle_class:Ct,transition_in:V,transition_out:ee,update_keyed_each:Mn}=window.__gradio__svelte__internal,{Map:An,window:Kf}=Rf,{afterUpdate:Wf,createEventDispatcher:Uf,tick:Vf,onMount:vs}=window.__gradio__svelte__internal;function ys(n,e,t){const l=n.slice();return l[146]=e[t].value,l[147]=e[t].id,l[150]=e,l[151]=t,l}function Cs(n,e,t){const l=n.slice();return l[146]=e[t].value,l[147]=e[t].id,l[148]=e,l[149]=t,l}function Ss(n,e,t){const l=n.slice();return l[146]=e[t].value,l[147]=e[t].id,l[152]=e,l[149]=t,l}function Ns(n,e,t){const l=n.slice();return l[146]=e[t].value,l[147]=e[t].id,l[153]=e,l[151]=t,l}function qs(n){let e,t,l,i,r=n[3]&&n[3].length!==0&&n[4]&&Ms(n);return l=new Sf({props:{show_fullscreen_button:n[18],fullscreen:n[23],on_copy:n[89],show_copy_button:n[19],show_search:n[21],on_commit_filter:n[68],current_search_query:n[33].current_search_query}}),l.$on("search",n[90]),l.$on("fullscreen",n[91]),{c(){e=Ee("div"),r&&r.c(),t=Ve(),Qe(l.$$.fragment),de(e,"class","header-row svelte-1vwr9xf")},m(s,o){Be(s,e,o),r&&r.m(e,null),be(e,t),$e(l,e,null),i=!0},p(s,o){s[3]&&s[3].length!==0&&s[4]?r?r.p(s,o):(r=Ms(s),r.c(),r.m(e,t)):r&&(r.d(1),r=null);const _={};o[0]&262144&&(_.show_fullscreen_button=s[18]),o[0]&8388608&&(_.fullscreen=s[23]),o[0]&67108864&&(_.on_copy=s[89]),o[0]&524288&&(_.show_copy_button=s[19]),o[0]&2097152&&(_.show_search=s[21]),o[1]&4&&(_.current_search_query=s[33].current_search_query),l.$set(_)},i(s){i||(V(l.$$.fragment,s),i=!0)},o(s){ee(l.$$.fragment,s),i=!1},d(s){s&&je(e),r&&r.d(),Ze(l)}}}function Ms(n){let e,t,l;return{c(){e=Ee("div"),t=Ee("p"),l=Il(n[3]),de(t,"class","svelte-1vwr9xf"),de(e,"class","label svelte-1vwr9xf")},m(i,r){Be(i,e,r),be(e,t),be(t,l)},p(i,r){r[0]&8&&Ol(l,i[3])},d(i){i&&je(e)}}}function As(n){let e,t;return{c(){e=Ee("caption"),t=Il(n[3]),de(e,"class","sr-only svelte-1vwr9xf")},m(l,i){Be(l,e,i),be(e,t)},p(l,i){i[0]&8&&Ol(t,l[3])},d(l){l&&je(e)}}}function Es(n){let e,t;return e=new En({props:{is_header:!0}}),{c(){Qe(e.$$.fragment)},m(l,i){$e(e,l,i),t=!0},i(l){t||(V(e.$$.fragment,l),t=!0)},o(l){ee(e.$$.fragment,l),t=!1},d(l){Ze(e,l)}}}function Ls(n,e){let t,l,i,r,s;function o(a){e[92](a,e[151])}function _(a){e[93](a,e[147])}let f={i:e[151],actual_pinned_columns:e[53],header_edit:e[52],selected_header:e[51],headers:e[0],get_cell_width:Rl,handle_header_click:e[60],toggle_header_menu:e[65],end_header_edit:e[61],sort_columns:e[33].sort_state.sort_columns,filter_columns:e[33].filter_state.filter_columns,latex_delimiters:e[7],line_breaks:e[14],max_chars:e[20],editable:e[9],is_static:e[22].includes(e[151]),i18n:e[12],col_count:e[5]};return e[25][e[151]].value!==void 0&&(f.value=e[25][e[151]].value),e[24][e[147]].input!==void 0&&(f.el=e[24][e[147]].input),l=new Ys({props:f}),Ie.push(()=>ot(l,"value",o)),Ie.push(()=>ot(l,"el",_)),{key:n,first:null,c(){t=an(),Qe(l.$$.fragment),this.first=t},m(a,u){Be(a,t,u),$e(l,a,u),s=!0},p(a,u){e=a;const c={};u[0]&33554432&&(c.i=e[151]),u[1]&4194304&&(c.actual_pinned_columns=e[53]),u[1]&2097152&&(c.header_edit=e[52]),u[1]&1048576&&(c.selected_header=e[51]),u[0]&1&&(c.headers=e[0]),u[1]&4&&(c.sort_columns=e[33].sort_state.sort_columns),u[1]&4&&(c.filter_columns=e[33].filter_state.filter_columns),u[0]&128&&(c.latex_delimiters=e[7]),u[0]&16384&&(c.line_breaks=e[14]),u[0]&1048576&&(c.max_chars=e[20]),u[0]&512&&(c.editable=e[9]),u[0]&37748736&&(c.is_static=e[22].includes(e[151])),u[0]&4096&&(c.i18n=e[12]),u[0]&32&&(c.col_count=e[5]),!i&&u[0]&33554432&&(i=!0,c.value=e[25][e[151]].value,rt(()=>i=!1)),!r&&u[0]&50331648&&(r=!0,c.el=e[24][e[147]].input,rt(()=>r=!1)),l.$set(c)},i(a){s||(V(l.$$.fragment,a),s=!0)},o(a){ee(l.$$.fragment,a),s=!1},d(a){a&&je(t),Ze(l,a)}}}function js(n){let e,t;return e=new En({props:{index:0}}),{c(){Qe(e.$$.fragment)},m(l,i){$e(e,l,i),t=!0},i(l){t||(V(e.$$.fragment,l),t=!0)},o(l){ee(e.$$.fragment,l),t=!1},d(l){Ze(e,l)}}}function Bs(n,e){let t,l,i,r,s=e[149],o;i=new El({props:{value:e[146],latex_delimiters:e[7],line_breaks:e[14],datatype:Array.isArray(e[2])?e[2][e[149]]:e[2],edit:!1,el:null,editable:e[9],i18n:e[12],show_selection_buttons:e[31].length===1&&e[31][0][0]===0&&e[31][0][1]===e[149],coords:e[39],on_select_column:e[55].handle_select_column,on_select_row:e[55].handle_select_row,is_dragging:e[42]}}),i.$on("blur",e[64]);const _=()=>e[94](t,s),f=()=>e[94](null,s);return{key:n,first:null,c(){t=Ee("td"),l=Ee("div"),Qe(i.$$.fragment),r=Ve(),de(l,"class","cell-wrap svelte-1vwr9xf"),de(t,"tabindex","-1"),de(t,"class","svelte-1vwr9xf"),this.first=t},m(a,u){Be(a,t,u),be(t,l),$e(i,l,null),be(t,r),_(),o=!0},p(a,u){e=a;const c={};u[1]&524288&&(c.value=e[146]),u[0]&128&&(c.latex_delimiters=e[7]),u[0]&16384&&(c.line_breaks=e[14]),u[0]&4|u[1]&524288&&(c.datatype=Array.isArray(e[2])?e[2][e[149]]:e[2]),u[0]&512&&(c.editable=e[9]),u[0]&4096&&(c.i18n=e[12]),u[1]&524289&&(c.show_selection_buttons=e[31].length===1&&e[31][0][0]===0&&e[31][0][1]===e[149]),u[1]&256&&(c.coords=e[39]),u[1]&2048&&(c.is_dragging=e[42]),i.$set(c),s!==e[149]&&(f(),s=e[149],_())},i(a){o||(V(i.$$.fragment,a),o=!0)},o(a){ee(i.$$.fragment,a),o=!1},d(a){a&&je(t),Ze(i),f()}}}function Ds(n){let e,t;return{c(){e=Ee("caption"),t=Il(n[3]),de(e,"class","sr-only svelte-1vwr9xf")},m(l,i){Be(l,e,i),be(e,t)},p(l,i){i[0]&8&&Ol(t,l[3])},d(l){l&&je(e)}}}function Yf(n){let e,t=n[3]&&n[3].length!==0&&Ds(n);return{c(){t&&t.c(),e=an()},m(l,i){t&&t.m(l,i),Be(l,e,i)},p(l,i){l[3]&&l[3].length!==0?t?t.p(l,i):(t=Ds(l),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null)},d(l){l&&je(e),t&&t.d(l)}}}function zs(n){let e,t;return e=new En({props:{is_header:!0}}),{c(){Qe(e.$$.fragment)},m(l,i){$e(e,l,i),t=!0},i(l){t||(V(e.$$.fragment,l),t=!0)},o(l){ee(e.$$.fragment,l),t=!1},d(l){Ze(e,l)}}}function Ts(n,e){let t,l,i,r,s;function o(a){e[99](a,e[151])}function _(a){e[100](a,e[147])}let f={i:e[151],actual_pinned_columns:e[53],header_edit:e[52],selected_header:e[51],headers:e[0],get_cell_width:Rl,handle_header_click:e[60],toggle_header_menu:e[65],end_header_edit:e[61],sort_columns:e[33].sort_state.sort_columns,filter_columns:e[33].filter_state.filter_columns,latex_delimiters:e[7],line_breaks:e[14],max_chars:e[20],editable:e[9],is_static:e[22].includes(e[151]),i18n:e[12],col_count:e[5]};return e[25][e[151]].value!==void 0&&(f.value=e[25][e[151]].value),e[24][e[147]].input!==void 0&&(f.el=e[24][e[147]].input),l=new Ys({props:f}),Ie.push(()=>ot(l,"value",o)),Ie.push(()=>ot(l,"el",_)),{key:n,first:null,c(){t=an(),Qe(l.$$.fragment),this.first=t},m(a,u){Be(a,t,u),$e(l,a,u),s=!0},p(a,u){e=a;const c={};u[0]&33554432&&(c.i=e[151]),u[1]&4194304&&(c.actual_pinned_columns=e[53]),u[1]&2097152&&(c.header_edit=e[52]),u[1]&1048576&&(c.selected_header=e[51]),u[0]&1&&(c.headers=e[0]),u[1]&4&&(c.sort_columns=e[33].sort_state.sort_columns),u[1]&4&&(c.filter_columns=e[33].filter_state.filter_columns),u[0]&128&&(c.latex_delimiters=e[7]),u[0]&16384&&(c.line_breaks=e[14]),u[0]&1048576&&(c.max_chars=e[20]),u[0]&512&&(c.editable=e[9]),u[0]&37748736&&(c.is_static=e[22].includes(e[151])),u[0]&4096&&(c.i18n=e[12]),u[0]&32&&(c.col_count=e[5]),!i&&u[0]&33554432&&(i=!0,c.value=e[25][e[151]].value,rt(()=>i=!1)),!r&&u[0]&50331648&&(r=!0,c.el=e[24][e[147]].input,rt(()=>r=!1)),l.$set(c)},i(a){s||(V(l.$$.fragment,a),s=!0)},o(a){ee(l.$$.fragment,a),s=!1},d(a){a&&je(t),Ze(l,a)}}}function Xf(n){let e,t,l=[],i=new An,r,s=n[15]&&zs(),o=St(n[25]);const _=f=>f[147];for(let f=0;f<o.length;f+=1){let a=ys(n,o,f),u=_(a);i.set(u,l[f]=Ts(u,a))}return{c(){e=Ee("tr"),s&&s.c(),t=Ve();for(let f=0;f<l.length;f+=1)l[f].c();de(e,"slot","thead"),de(e,"class","svelte-1vwr9xf")},m(f,a){Be(f,e,a),s&&s.m(e,null),be(e,t);for(let u=0;u<l.length;u+=1)l[u]&&l[u].m(e,null);r=!0},p(f,a){f[15]?s?a[0]&32768&&V(s,1):(s=zs(),s.c(),V(s,1),s.m(e,t)):s&&(ht(),ee(s,1,1,()=>{s=null}),ct()),a[0]&55595681|a[1]&1617952772|a[2]&8&&(o=St(f[25]),ht(),l=Mn(l,a,_,1,f,o,i,e,qn,Ts,null,ys),ct())},i(f){if(!r){V(s);for(let a=0;a<o.length;a+=1)V(l[a]);r=!0}},o(f){ee(s);for(let a=0;a<l.length;a+=1)ee(l[a]);r=!1},d(f){f&&je(e),s&&s.d();for(let a=0;a<l.length;a+=1)l[a].d()}}}function Hs(n){let e,t;return e=new En({props:{index:n[144]}}),{c(){Qe(e.$$.fragment)},m(l,i){$e(e,l,i),t=!0},p(l,i){const r={};i[4]&1048576&&(r.index=l[144]),e.$set(r)},i(l){t||(V(e.$$.fragment,l),t=!0)},o(l){ee(e.$$.fragment,l),t=!1},d(l){Ze(e,l)}}}function Os(n,e){let t,l,i,r,s;function o(a){e[97](a,e[144],e[149])}function _(a){e[98](a,e[147])}let f={display_value:e[72](e[144],e[149]),index:e[33].current_search_query!==void 0&&e[27][e[144]]!==void 0?e[27][e[144]]:e[144],j:e[149],actual_pinned_columns:e[53],get_cell_width:Rl,handle_cell_click:e[96],handle_blur:e[64],toggle_cell_menu:e[55].toggle_cell_menu,is_cell_selected:No,should_show_cell_menu:Ao,selected_cells:e[31],copy_flash:e[32],active_cell_menu:e[47],styling:e[35][e[144]][e[149]].styling,latex_delimiters:e[7],line_breaks:e[14],datatype:Array.isArray(e[2])?e[2][e[149]]:e[2],editing:e[48],max_chars:e[20],editable:e[9],is_static:e[22].includes(e[149]),i18n:e[12],components:e[8],handle_select_column:e[55].handle_select_column,handle_select_row:e[55].handle_select_row,is_dragging:e[42],wrap:e[10]};return e[35][e[144]][e[149]].value!==void 0&&(f.value=e[35][e[144]][e[149]].value),e[24][e[147]]!==void 0&&(f.el=e[24][e[147]]),l=new cu({props:f}),Ie.push(()=>ot(l,"value",o)),Ie.push(()=>ot(l,"el",_)),{key:n,first:null,c(){t=an(),Qe(l.$$.fragment),this.first=t},m(a,u){Be(a,t,u),$e(l,a,u),s=!0},p(a,u){e=a;const c={};u[4]&3145728&&(c.display_value=e[72](e[144],e[149])),u[0]&134217728|u[1]&4|u[4]&1048576&&(c.index=e[33].current_search_query!==void 0&&e[27][e[144]]!==void 0?e[27][e[144]]:e[144]),u[4]&2097152&&(c.j=e[149]),u[1]&4194304&&(c.actual_pinned_columns=e[53]),u[1]&16384&&(c.handle_cell_click=e[96]),u[1]&1&&(c.selected_cells=e[31]),u[1]&2&&(c.copy_flash=e[32]),u[1]&65536&&(c.active_cell_menu=e[47]),u[1]&16|u[4]&3145728&&(c.styling=e[35][e[144]][e[149]].styling),u[0]&128&&(c.latex_delimiters=e[7]),u[0]&16384&&(c.line_breaks=e[14]),u[0]&4|u[4]&2097152&&(c.datatype=Array.isArray(e[2])?e[2][e[149]]:e[2]),u[1]&131072&&(c.editing=e[48]),u[0]&1048576&&(c.max_chars=e[20]),u[0]&512&&(c.editable=e[9]),u[0]&4194304|u[4]&2097152&&(c.is_static=e[22].includes(e[149])),u[0]&4096&&(c.i18n=e[12]),u[0]&256&&(c.components=e[8]),u[1]&2048&&(c.is_dragging=e[42]),u[0]&1024&&(c.wrap=e[10]),!i&&u[1]&16|u[4]&3145728&&(i=!0,c.value=e[35][e[144]][e[149]].value,rt(()=>i=!1)),!r&&u[0]&16777216|u[4]&2097152&&(r=!0,c.el=e[24][e[147]],rt(()=>r=!1)),l.$set(c)},i(a){s||(V(l.$$.fragment,a),s=!0)},o(a){ee(l.$$.fragment,a),s=!1},d(a){a&&je(t),Ze(l,a)}}}function Gf(n){let e,t,l=[],i=new An,r,s=n[15]&&Hs(n),o=St(n[145]);const _=f=>f[147];for(let f=0;f<o.length;f+=1){let a=Cs(n,o,f),u=_(a);i.set(u,l[f]=Os(u,a))}return{c(){e=Ee("tr"),s&&s.c(),t=Ve();for(let f=0;f<l.length;f+=1)l[f].c();de(e,"slot","tbody"),de(e,"class","svelte-1vwr9xf"),Ct(e,"row-odd",n[144]%2===0)},m(f,a){Be(f,e,a),s&&s.m(e,null),be(e,t);for(let u=0;u<l.length;u+=1)l[u]&&l[u].m(e,null);r=!0},p(f,a){f[15]?s?(s.p(f,a),a[0]&32768&&V(s,1)):(s=Hs(f),s.c(),V(s,1),s.m(e,t)):s&&(ht(),ee(s,1,1,()=>{s=null}),ct()),a[0]&156260228|a[1]&21186583|a[2]&1028|a[4]&3145728&&(o=St(f[145]),ht(),l=Mn(l,a,_,1,f,o,i,e,qn,Os,null,Cs),ct()),(!r||a[4]&1048576)&&Ct(e,"row-odd",f[144]%2===0)},i(f){if(!r){V(s);for(let a=0;a<o.length;a+=1)V(l[a]);r=!0}},o(f){ee(s);for(let a=0;a<l.length;a+=1)ee(l[a]);r=!1},d(f){f&&je(e),s&&s.d();for(let a=0;a<l.length;a+=1)l[a].d()}}}function Qf(n){let e,t,l,i,r,s,o,_;function f(v){n[101](v)}function a(v){n[102](v)}function u(v){n[103](v)}function c(v){n[104](v)}function g(v){n[105](v)}let L={max_height:n[13],selected:n[49],disable_scroll:n[47]!==null||n[46]!==null,$$slots:{tbody:[Gf,({index:v,item:q})=>({144:v,145:q}),({index:v,item:q})=>[0,0,0,0,(v?1048576:0)|(q?2097152:0)]],thead:[Xf],default:[Yf]},$$scope:{ctx:n}};return n[35]!==void 0&&(L.items=n[35]),n[37]!==void 0&&(L.actual_height=n[37]),n[38]!==void 0&&(L.table_scrollbar_width=n[38]),n[40]!==void 0&&(L.viewport=n[40]),n[41]!==void 0&&(L.show_scroll_button=n[41]),t=new Wu({props:L}),Ie.push(()=>ot(t,"items",f)),Ie.push(()=>ot(t,"actual_height",a)),Ie.push(()=>ot(t,"table_scrollbar_width",u)),Ie.push(()=>ot(t,"viewport",c)),Ie.push(()=>ot(t,"show_scroll_button",g)),t.$on("scroll_top",$f),{c(){e=Ee("div"),Qe(t.$$.fragment),de(e,"class","table-wrap svelte-1vwr9xf")},m(v,q){Be(v,e,q),$e(t,e,null),_=!0},p(v,q){const w={};q[0]&8192&&(w.max_height=v[13]),q[1]&262144&&(w.selected=v[49]),q[1]&98304&&(w.disable_scroll=v[47]!==null||v[46]!==null),q[0]&189847469|q[1]&7555095|q[4]&1076887552&&(w.$$scope={dirty:q,ctx:v}),!l&&q[1]&16&&(l=!0,w.items=v[35],rt(()=>l=!1)),!i&&q[1]&64&&(i=!0,w.actual_height=v[37],rt(()=>i=!1)),!r&&q[1]&128&&(r=!0,w.table_scrollbar_width=v[38],rt(()=>r=!1)),!s&&q[1]&512&&(s=!0,w.viewport=v[40],rt(()=>s=!1)),!o&&q[1]&1024&&(o=!0,w.show_scroll_button=v[41],rt(()=>o=!1)),t.$set(w)},i(v){_||(V(t.$$.fragment,v),_=!0)},o(v){ee(t.$$.fragment,v),_=!1},d(v){v&&je(e),Ze(t)}}}function Is(n){let e,t,l;return{c(){e=Ee("button"),e.textContent="↑",de(e,"class","scroll-top-button svelte-1vwr9xf")},m(i,r){Be(i,e,r),t||(l=Ot(e,"click",n[69]),t=!0)},p:lr,d(i){i&&je(e),t=!1,l()}}}function Rs(n){let e,t;return e=new Su({props:{on_click:n[110]}}),{c(){Qe(e.$$.fragment)},m(l,i){$e(e,l,i),t=!0},p:lr,i(l){t||(V(e.$$.fragment,l),t=!0)},o(l){ee(e.$$.fragment,l),t=!1},d(l){Ze(e,l)}}}function Ps(n){let e,t;return e=new cf({props:{x:n[47]?.x??n[46]?.x??0,y:n[47]?.y??n[46]?.y??0,row:n[46]?-1:n[47]?.row??0,col_count:n[5],row_count:n[6],on_add_row_above:n[111],on_add_row_below:n[112],on_add_column_left:n[113],on_add_column_right:n[114],on_delete_row:n[115],on_delete_col:n[116],editable:n[9],can_delete_rows:!n[46]&&n[26].length>1&&n[9],can_delete_cols:n[26].length>0&&n[26][0]?.length>1&&n[9],i18n:n[12],on_sort:n[46]?n[117]:void 0,on_clear_sort:n[46]?n[118]:void 0,sort_direction:n[46]?n[33].sort_state.sort_columns.find(n[119])?.direction??null:null,sort_priority:n[46]&&n[33].sort_state.sort_columns.findIndex(n[120])+1||null,on_filter:n[46]?n[121]:void 0,on_clear_filter:n[46]?n[122]:void 0,filter_active:n[46]?n[33].filter_state.filter_columns.some(n[123]):null}}),{c(){Qe(e.$$.fragment)},m(l,i){$e(e,l,i),t=!0},p(l,i){const r={};i[1]&98304&&(r.x=l[47]?.x??l[46]?.x??0),i[1]&98304&&(r.y=l[47]?.y??l[46]?.y??0),i[1]&98304&&(r.row=l[46]?-1:l[47]?.row??0),i[0]&32&&(r.col_count=l[5]),i[0]&64&&(r.row_count=l[6]),i[1]&65536&&(r.on_add_row_above=l[111]),i[1]&65536&&(r.on_add_row_below=l[112]),i[1]&98304&&(r.on_add_column_left=l[113]),i[1]&98304&&(r.on_add_column_right=l[114]),i[1]&65536&&(r.on_delete_row=l[115]),i[1]&98304&&(r.on_delete_col=l[116]),i[0]&512&&(r.editable=l[9]),i[0]&67109376|i[1]&32768&&(r.can_delete_rows=!l[46]&&l[26].length>1&&l[9]),i[0]&67109376&&(r.can_delete_cols=l[26].length>0&&l[26][0]?.length>1&&l[9]),i[0]&4096&&(r.i18n=l[12]),i[1]&32768&&(r.on_sort=l[46]?l[117]:void 0),i[1]&32768&&(r.on_clear_sort=l[46]?l[118]:void 0),i[1]&32772&&(r.sort_direction=l[46]?l[33].sort_state.sort_columns.find(l[119])?.direction??null:null),i[1]&32772&&(r.sort_priority=l[46]&&l[33].sort_state.sort_columns.findIndex(l[120])+1||null),i[1]&32768&&(r.on_filter=l[46]?l[121]:void 0),i[1]&32768&&(r.on_clear_filter=l[46]?l[122]:void 0),i[1]&32772&&(r.filter_active=l[46]?l[33].filter_state.filter_columns.some(l[123]):null),e.$set(r)},i(l){t||(V(e.$$.fragment,l),t=!0)},o(l){ee(e.$$.fragment,l),t=!1},d(l){Ze(e,l)}}}function Zf(n){let e,t,l,i,r,s,o,_,f=[],a=new An,u,c,g,L,v=[],q=new An,w,p,k,y,C,j,S,D,O,G,I=(n[3]&&n[3].length!==0&&n[4]||n[18]||n[19]||n[21]!=="none")&&qs(n),z=n[3]&&n[3].length!==0&&As(n),T=n[15]&&Es(),Y=St(n[25]);const le=M=>M[147];for(let M=0;M<Y.length;M+=1){let m=Ns(n,Y,M),F=le(m);a.set(F,f[M]=Ls(F,m))}let K=n[15]&&js(),W=St(n[50]);const ce=M=>M[147];for(let M=0;M<W.length;M+=1){let m=Ss(n,W,M),F=ce(m);q.set(F,v[M]=Bs(F,m))}function B(M){n[106](M)}let Z={upload:n[16],stream_handler:n[17],flex:!1,center:!1,boundedheight:!1,disable_click:!0,root:n[11],aria_label:n[12]("dataframe.drop_to_upload"),$$slots:{default:[Qf]},$$scope:{ctx:n}};n[36]!==void 0&&(Z.dragging=n[36]),p=new ro({props:Z}),Ie.push(()=>ot(p,"dragging",B)),p.$on("load",n[107]);let N=n[41]&&Is(n),J=n[26].length===0&&n[9]&&n[6][1]==="dynamic"&&Rs(n),b=(n[47]||n[46])&&Ps(n);return{c(){e=Ee("div"),I&&I.c(),t=Ve(),l=Ee("div"),i=Ee("table"),z&&z.c(),r=Ve(),s=Ee("thead"),o=Ee("tr"),T&&T.c(),_=Ve();for(let M=0;M<f.length;M+=1)f[M].c();u=Ve(),c=Ee("tbody"),g=Ee("tr"),K&&K.c(),L=Ve();for(let M=0;M<v.length;M+=1)v[M].c();w=Ve(),Qe(p.$$.fragment),y=Ve(),N&&N.c(),C=Ve(),J&&J.c(),j=Ve(),b&&b.c(),S=an(),de(o,"class","svelte-1vwr9xf"),de(s,"class","svelte-1vwr9xf"),de(g,"class","svelte-1vwr9xf"),de(c,"class","svelte-1vwr9xf"),de(i,"aria-hidden","true"),de(i,"class","svelte-1vwr9xf"),de(l,"class","table-wrap svelte-1vwr9xf"),ps(l,"height",n[37]+"px"),de(l,"role","grid"),de(l,"tabindex","0"),Ct(l,"dragging",n[42]),Ct(l,"no-wrap",!n[10]),Ct(l,"menu-open",n[47]||n[46]),de(e,"class","table-container svelte-1vwr9xf")},m(M,m){Be(M,e,m),I&&I.m(e,null),be(e,t),be(e,l),be(l,i),z&&z.m(i,null),be(i,r),be(i,s),be(s,o),T&&T.m(o,null),be(o,_);for(let F=0;F<f.length;F+=1)f[F]&&f[F].m(o,null);be(i,u),be(i,c),be(c,g),K&&K.m(g,null),be(g,L);for(let F=0;F<v.length;F+=1)v[F]&&v[F].m(g,null);n[95](i),be(l,w),$e(p,l,null),be(l,y),N&&N.m(l,null),n[108](l),Be(M,C,m),J&&J.m(M,m),Be(M,j,m),b&&b.m(M,m),Be(M,S,m),D=!0,O||(G=[Ot(Kf,"resize",n[88]),Ot(l,"keydown",n[109]),Ot(l,"mousemove",function(){ul(n[44])&&n[44].apply(this,arguments)}),Ot(l,"mouseup",function(){ul(n[43])&&n[43].apply(this,arguments)}),Ot(l,"mouseleave",function(){ul(n[43])&&n[43].apply(this,arguments)})],O=!0)},p(M,m){n=M,n[3]&&n[3].length!==0&&n[4]||n[18]||n[19]||n[21]!=="none"?I?(I.p(n,m),m[0]&2883608&&V(I,1)):(I=qs(n),I.c(),V(I,1),I.m(e,t)):I&&(ht(),ee(I,1,1,()=>{I=null}),ct()),n[3]&&n[3].length!==0?z?z.p(n,m):(z=As(n),z.c(),z.m(i,r)):z&&(z.d(1),z=null),n[15]?T?m[0]&32768&&V(T,1):(T=Es(),T.c(),V(T,1),T.m(o,_)):T&&(ht(),ee(T,1,1,()=>{T=null}),ct()),m[0]&55595681|m[1]&1617952772|m[2]&8&&(Y=St(n[25]),ht(),f=Mn(f,m,le,1,n,Y,a,o,qn,Ls,null,Ns),ct()),n[15]?K?m[0]&32768&&V(K,1):(K=js(),K.c(),V(K,1),K.m(g,L)):K&&(ht(),ee(K,1,1,()=>{K=null}),ct()),m[0]&268456580|m[1]&17303809|m[2]&4&&(W=St(n[50]),ht(),v=Mn(v,m,ce,1,n,W,q,g,qn,Bs,null,Ss),ct());const F={};m[0]&65536&&(F.upload=n[16]),m[0]&131072&&(F.stream_handler=n[17]),m[0]&2048&&(F.root=n[11]),m[0]&4096&&(F.aria_label=n[12]("dataframe.drop_to_upload")),m[0]&189855661|m[1]&7851735|m[4]&1073741824&&(F.$$scope={dirty:m,ctx:n}),!k&&m[1]&32&&(k=!0,F.dragging=n[36],rt(()=>k=!1)),p.$set(F),n[41]?N?N.p(n,m):(N=Is(n),N.c(),N.m(l,null)):N&&(N.d(1),N=null),(!D||m[1]&64)&&ps(l,"height",n[37]+"px"),(!D||m[1]&2048)&&Ct(l,"dragging",n[42]),(!D||m[0]&1024)&&Ct(l,"no-wrap",!n[10]),(!D||m[1]&98304)&&Ct(l,"menu-open",n[47]||n[46]),n[26].length===0&&n[9]&&n[6][1]==="dynamic"?J?(J.p(n,m),m[0]&67109440&&V(J,1)):(J=Rs(n),J.c(),V(J,1),J.m(j.parentNode,j)):J&&(ht(),ee(J,1,1,()=>{J=null}),ct()),n[47]||n[46]?b?(b.p(n,m),m[1]&98304&&V(b,1)):(b=Ps(n),b.c(),V(b,1),b.m(S.parentNode,S)):b&&(ht(),ee(b,1,1,()=>{b=null}),ct())},i(M){if(!D){V(I),V(T);for(let m=0;m<Y.length;m+=1)V(f[m]);V(K);for(let m=0;m<W.length;m+=1)V(v[m]);V(p.$$.fragment,M),V(J),V(b),D=!0}},o(M){ee(I),ee(T);for(let m=0;m<f.length;m+=1)ee(f[m]);ee(K);for(let m=0;m<v.length;m+=1)ee(v[m]);ee(p.$$.fragment,M),ee(J),ee(b),D=!1},d(M){M&&(je(e),je(C),je(j),je(S)),I&&I.d(),z&&z.d(),T&&T.d();for(let m=0;m<f.length;m+=1)f[m].d();K&&K.d();for(let m=0;m<v.length;m+=1)v[m].d();n[95](null),Ze(p),N&&N.d(),n[108](null),J&&J.d(M),b&&b.d(M),O=!1,Jf(G)}}}function Et(){return Math.random().toString(36).substring(2,15)}function Rl(n){return`var(--cell-width-${n})`}const $f=n=>{};function xf(n,e,t){let l,i,r,s,o,_,f,a,u,c,g,L,v,q,w,{datatype:p}=e,{label:k=null}=e,{show_label:y=!0}=e,{headers:C=[]}=e,{values:j=[]}=e,{col_count:S}=e,{row_count:D}=e,{latex_delimiters:O}=e,{components:G={}}=e,{editable:I=!0}=e,{wrap:z=!1}=e,{root:T}=e,{i18n:Y}=e,{max_height:le=500}=e,{line_breaks:K=!0}=e,{column_widths:W=[]}=e,{show_row_numbers:ce=!1}=e,{upload:B}=e,{stream_handler:Z}=e,{show_fullscreen_button:N=!1}=e,{show_copy_button:J=!1}=e,{value_is_output:b=!1}=e,{max_chars:M=void 0}=e,{show_search:m="none"}=e,{pinned_columns:F=0}=e,{static_columns:Se=[]}=e,{fullscreen:mt=!1}=e;const $=To({show_fullscreen_button:N,show_copy_button:J,show_search:m,show_row_numbers:ce,editable:I,pinned_columns:F,show_label:y,line_breaks:K,wrap:z,max_height:le,column_widths:W,max_chars:M,static_columns:Se}),{state:A,actions:P}=$;If(n,A,h=>t(33,w=h)),vs(()=>{t(34,$.parent_element=fe,$),t(34,$.get_data_at=or,$),t(34,$.get_column=ar,$),t(34,$.get_row=_r,$),t(34,$.dispatch=Nt,$),$l();const h=new IntersectionObserver(H=>{H.forEach(X=>{X.isIntersecting&&!Vl&&t(84,qt=!1),Vl=X.isIntersecting})});h.observe(fe),document.addEventListener("click",Ul),window.addEventListener("resize",Ql);const E=H=>{(Qt||xn)&&q(H)};return document.addEventListener("mouseup",E),()=>{h.disconnect(),document.removeEventListener("click",Ul),window.removeEventListener("resize",Ql),document.removeEventListener("mouseup",E)}});const Nt=Uf();let x={},Rn={},Ne=kn(C,S,x,Et),Xt=C,R=[[]],Ke,xe=[[]],Pn=!1,Pl,Dt=[];vs(()=>{Pl=getComputedStyle(document.documentElement).getPropertyValue("--color-accent").trim()+"40",document.documentElement.style.setProperty("--color-accent-copied",Pl)});const or=(h,E)=>R?.[h]?.[E]?.value,ar=h=>R?.map(E=>E[h]?.value)??[],_r=h=>R?.[h]?.map(E=>E.value)??[];let{display_value:Re=null}=e,{styling:Pe=null}=e,Jn=Ne.map(h=>h.value),Fn=R.map(h=>h.map(E=>String(E.value)));function Jl(h,E){P.handle_sort(h,E),un(R,Re,Pe)}function Fl(){P.reset_sort_state(),un(R,Re,Pe)}function Kl(h,E,H,X){P.handle_filter(h,E,H,X),fn(R,Re,Pe)}function Wl(){P.reset_filter_state(),fn(R,Re,Pe)}async function ur(h,E=!1){!I||s===h||S[1]!=="dynamic"||P.set_header_edit(h)}function fr(h,E){h.target instanceof HTMLAnchorElement||(h.preventDefault(),h.stopPropagation(),I&&(P.set_editing(!1),P.handle_header_click(E,I),fe.focus()))}function cr(h){I&&(P.end_header_edit(h.detail.key),fe.focus())}async function Kn(h){if(fe.focus(),D[1]!=="dynamic")return;const E=Array(R[0]?.length||C.length).fill(0).map((H,X)=>{const ae=Et();return t(24,x[ae]={cell:null,input:null},x),{id:ae,value:""}});R.length===0?t(26,R=[E]):h!==void 0&&h>=0&&h<=R.length?R.splice(h,0,E):R.push(E),t(87,i=[h!==void 0?h:R.length-1,0])}async function hr(h){if(fe.focus(),S[1]!=="dynamic")return;const E=P.add_col(R,C,Et,h);E.data.forEach(H=>{H.forEach(X=>{x[X.id]||t(24,x[X.id]={cell:null,input:null},x)})}),t(26,R=E.data),t(0,C=E.headers),await Vf(),requestAnimationFrame(()=>{ur(h!==void 0?h:R[0].length-1,!0);const H=fe.querySelectorAll("tbody")[1].offsetWidth;fe.querySelectorAll("table")[1].scrollTo({left:H})})}function Ul(h){jo(h,fe)&&(P.clear_ui_state(),t(52,s=!1),t(51,o=!1))}let Wn,qt=!1,gt=[],fe,_n,Un=0,Vn=0;function Gt(){const h=R[0]?.length||0;if(w.filter_state.filter_columns.length>0||Un===R.length&&Vn===h&&w.sort_state.sort_columns.length>0)return;Un=R.length,Vn=h;const E=gt.map(H=>H?.clientWidth||0);if(E.length!==0){ce&&fe.style.setProperty("--cell-width-row-number",`${E[0]}px`);for(let H=0;H<50;H++)if(!W[H])fe.style.removeProperty(`--cell-width-${H}`);else if(W[H].endsWith("%")){const X=parseFloat(W[H]),ae=Math.floor(X/100*fe.clientWidth);fe.style.setProperty(`--cell-width-${H}`,`${ae}px`)}else fe.style.setProperty(`--cell-width-${H}`,W[H]);E.forEach((H,X)=>{if(!W[X]){const ae=`${Math.max(H,45)}px`;fe.style.setProperty(`--cell-width-${X}`,ae)}})}}let Yn=j.slice(0,le/j.length*37).length*37+37,Xn=0;function un(h,E,H){const X=go(h,E,H,w.sort_state.sort_columns,i,ti);t(26,R=X.data),t(87,i=X.selected)}function fn(h,E,H){const X=wo(h,E,H,w.filter_state.filter_columns,i,ti,w.filter_state.initial_data?.data,w.filter_state.initial_data?.display_value,w.filter_state.initial_data?.styling);t(26,R=X.data),t(87,i=X.selected)}let Vl=!1;const dr=h=>{P.set_copy_flash(h)};let Gn=[];function mr(h){const{blur_event:E,coords:H}=h.detail;Mf(E,$,H)}function gr(h,E){if(h.stopPropagation(),f&&f.col===E)P.set_active_header_menu(null);else{const H=h.target.closest("th");if(H){const X=H.getBoundingClientRect();P.set_active_header_menu({col:E,x:X.right,y:X.bottom})}}}Wf(()=>{t(73,b=!1)});function Yl(h){if(S[1]!=="dynamic"||R[0].length<=1)return;const E=P.delete_col_at(R,C,h);t(26,R=E.data),t(0,C=E.headers),t(25,Ne=kn(C,S,x,Et)),P.set_active_cell_menu(null),P.set_active_header_menu(null),P.set_selected(!1),P.set_selected_cells([]),P.set_editing(!1)}function Xl(h){t(26,R=P.delete_row_at(R,h)),P.set_active_cell_menu(null),P.set_active_header_menu(null)}let Gl;function br(){if(w.current_search_query&&m==="filter"){const h=[],E=[],H=[];xe.forEach(ae=>{const dn=[],xl=[],ei=[];ae.forEach($t=>{dn.push($t.value),xl.push($t.display_value!==void 0?$t.display_value:String($t.value)),ei.push($t.styling||"")}),h.push(dn),E.push(xl),H.push(ei)});const X={data:h,headers:Ne.map(ae=>ae.value),metadata:{display_value:E,styling:H}};Nt("change",X),b||Nt("input"),P.handle_search(null)}}let cn,Qn=!1;function wr(){cn.scrollTo({top:0})}function Ql(){P.set_active_cell_menu(null),P.set_active_header_menu(null),t(31,l=[]),t(87,i=!1),t(48,r=!1),t(84,qt=!1),Gt()}function Zn(h,E){const H=E==="above"?h:h+1;Kn(H),t(47,_=null),t(46,f=null)}function $n(h,E){const H=E==="left"?h:h+1;hr(H),t(47,_=null),t(46,f=null)}function kr(){P.reset_sort_state()}let Qt=!1,xn=null,Zl=null;const hn={is_dragging:Qt,drag_start:xn,mouse_down_pos:Zl};let Zt;function $l(){t(86,Zt=Tf(hn,h=>t(42,Qt=h),h=>P.set_selected_cells(h),h=>P.set_selected(h),(h,E,H)=>P.handle_cell_click(h,E,H),ce,fe))}function pr(h,E){return w.current_search_query!==void 0&&xe?.[h]?.[E]?xe[h][E].display_value!==void 0?xe[h][E].display_value:String(xe[h][E].value):R?.[h]?.[E]?R[h][E].display_value!==void 0?R[h][E].display_value:String(R[h][E].value):""}const vr=()=>Gt(),yr=async()=>await Fs(R,null),Cr=h=>P.handle_search(h.detail);function Sr(h){Of.call(this,n,h)}function Nr(h,E){n.$$.not_equal(Ne[E].value,h)&&(Ne[E].value=h,t(25,Ne),t(0,C),t(79,Xt),t(5,S),t(24,x))}function qr(h,E){n.$$.not_equal(x[E].input,h)&&(x[E].input=h,t(24,x))}function Mr(h,E){Ie[h?"unshift":"push"](()=>{gt[E]=h,t(28,gt)})}function Ar(h){Ie[h?"unshift":"push"](()=>{_n=h,t(30,_n)})}const Er=(h,E,H)=>L(h,E,H);function Lr(h,E,H){n.$$.not_equal(xe[E][H].value,h)&&(xe[E][H].value=h,t(35,xe),t(33,w),t(26,R),t(27,Dt),t(77,Pe),t(1,j),t(80,Ke),t(29,fe),t(24,x),t(131,Rn),t(76,Re),t(28,gt))}function jr(h,E){n.$$.not_equal(x[E],h)&&(x[E]=h,t(24,x))}function Br(h,E){n.$$.not_equal(Ne[E].value,h)&&(Ne[E].value=h,t(25,Ne),t(0,C),t(79,Xt),t(5,S),t(24,x))}function Dr(h,E){n.$$.not_equal(x[E].input,h)&&(x[E].input=h,t(24,x))}function zr(h){xe=h,t(35,xe),t(33,w),t(26,R),t(27,Dt),t(77,Pe),t(1,j),t(80,Ke),t(29,fe),t(24,x),t(131,Rn),t(76,Re),t(28,gt)}function Tr(h){Yn=h,t(37,Yn)}function Hr(h){Xn=h,t(38,Xn)}function Or(h){cn=h,t(40,cn)}function Ir(h){Qn=h,t(41,Qn)}function Rr(h){Pn=h,t(36,Pn)}const Pr=({detail:h})=>Co(h.data,E=>(t(25,Ne=kn(E.map(H=>H??""),S,x,Et)),Ne),E=>{t(1,j=E)});function Jr(h){Ie[h?"unshift":"push"](()=>{fe=h,t(29,fe)})}const Fr=h=>zf(h,$),Kr=()=>Kn(),Wr=()=>Zn(_?.row??-1,"above"),Ur=()=>Zn(_?.row??-1,"below"),Vr=()=>$n(_?.col??f?.col??-1,"left"),Yr=()=>$n(_?.col??f?.col??-1,"right"),Xr=()=>Xl(_?.row??-1),Gr=()=>Yl(_?.col??f?.col??-1),Qr=h=>{f&&(Jl(f.col,h),P.set_active_header_menu(null))},Zr=()=>{Fl(),P.set_active_header_menu(null)},$r=h=>h.col===(f?.col??-1),xr=h=>h.col===(f?.col??-1),eo=(h,E,H)=>{f&&(Kl(f.col,h,E,H),P.set_active_header_menu(null))},to=()=>{Wl(),P.set_active_header_menu(null)},no=h=>h.col===(f?.col??-1);return n.$$set=h=>{"datatype"in h&&t(2,p=h.datatype),"label"in h&&t(3,k=h.label),"show_label"in h&&t(4,y=h.show_label),"headers"in h&&t(0,C=h.headers),"values"in h&&t(1,j=h.values),"col_count"in h&&t(5,S=h.col_count),"row_count"in h&&t(6,D=h.row_count),"latex_delimiters"in h&&t(7,O=h.latex_delimiters),"components"in h&&t(8,G=h.components),"editable"in h&&t(9,I=h.editable),"wrap"in h&&t(10,z=h.wrap),"root"in h&&t(11,T=h.root),"i18n"in h&&t(12,Y=h.i18n),"max_height"in h&&t(13,le=h.max_height),"line_breaks"in h&&t(14,K=h.line_breaks),"column_widths"in h&&t(74,W=h.column_widths),"show_row_numbers"in h&&t(15,ce=h.show_row_numbers),"upload"in h&&t(16,B=h.upload),"stream_handler"in h&&t(17,Z=h.stream_handler),"show_fullscreen_button"in h&&t(18,N=h.show_fullscreen_button),"show_copy_button"in h&&t(19,J=h.show_copy_button),"value_is_output"in h&&t(73,b=h.value_is_output),"max_chars"in h&&t(20,M=h.max_chars),"show_search"in h&&t(21,m=h.show_search),"pinned_columns"in h&&t(75,F=h.pinned_columns),"static_columns"in h&&t(22,Se=h.static_columns),"fullscreen"in h&&t(23,mt=h.fullscreen),"display_value"in h&&t(76,Re=h.display_value),"styling"in h&&t(77,Pe=h.styling)},n.$$.update=()=>{if(n.$$.dirty[1]&4&&t(31,l=w.ui_state.selected_cells),n.$$.dirty[1]&4&&t(87,i=w.ui_state.selected),n.$$.dirty[1]&4&&t(48,r=w.ui_state.editing),n.$$.dirty[1]&4&&t(52,s=w.ui_state.header_edit),n.$$.dirty[1]&4&&t(51,o=w.ui_state.selected_header),n.$$.dirty[1]&4&&t(47,_=w.ui_state.active_cell_menu),n.$$.dirty[1]&4&&t(46,f=w.ui_state.active_header_menu),n.$$.dirty[1]&4&&t(32,a=w.ui_state.copy_flash),n.$$.dirty[0]&889192450|n.$$.dirty[1]&4|n.$$.dirty[2]&311296&&!ln(j,Ke)){if(fe){const H=j.length===0||j.length===1&&j[0].length===0,X=Ke!==void 0&&(j.length!==Ke.length||j[0]&&Ke[0]&&j[0].length!==Ke[0].length);if(H||X){for(let ae=0;ae<50;ae++)fe.style.removeProperty(`--cell-width-${ae}`);Un=0,Vn=0,t(84,qt=!1)}}const h=j.length===0||j.length===1&&j[0].length===0,E=Ke!==void 0&&(j.length!==Ke.length||j[0]&&Ke[0]&&j[0].length!==Ke[0].length);t(26,R=Nf(j,x,Rn,Et,Re)),t(80,Ke=JSON.parse(JSON.stringify(j))),h||E?P.reset_sort_state():w.sort_state.sort_columns.length>0?un(R,Re,Pe):(P.handle_sort(-1,"asc"),P.reset_sort_state()),w.filter_state.filter_columns.length>0?fn(R,Re,Pe):P.reset_filter_state(),w.current_search_query&&P.handle_search(null),fe&&gt.length>0&&(h||E)&&t(84,qt=!1)}if(n.$$.dirty[0]&67108864|n.$$.dirty[2]&8192&&t(53,u=F&&R?.[0]?.length?Math.min(F,R[0].length):0),n.$$.dirty[0]&16777249|n.$$.dirty[2]&131072&&(ln(C,Xt)||(t(25,Ne=kn(C,S,x,Et)),t(79,Xt=JSON.parse(JSON.stringify(C))))),n.$$.dirty[0]&117440512|n.$$.dirty[2]&49152&&(R||Ne||x)&&(t(34,$.data=R,$),t(34,$.headers=Ne,$),t(34,$.els=x,$),t(34,$.display_value=Re,$),t(34,$.styling=Pe,$)),n.$$.dirty[0]&201326592|n.$$.dirty[1]&4|n.$$.dirty[2]&32768)if(w.current_search_query!==void 0){const h=new Map;t(27,Dt=[]),R.forEach((H,X)=>{H.some(ae=>String(ae?.value).toLowerCase().includes(w.current_search_query?.toLowerCase()||""))&&Dt.push(X),H.forEach((ae,dn)=>{h.set(ae.id,{value:ae.value,display_value:ae.display_value!==void 0?ae.display_value:String(ae.value),styling:Pe?.[X]?.[dn]||""})})});const E=P.filter_data(R);t(35,xe=E.map(H=>H.map(X=>{const ae=h.get(X.id);return{...X,display_value:ae?.display_value!==void 0?ae.display_value:String(X.value),styling:ae?.styling||""}})))}else t(27,Dt=[]);if(n.$$.dirty[0]&100663300|n.$$.dirty[2]&1574912&&(R||Ne)&&(P.trigger_change(R.map((h,E)=>h.map((H,X)=>{const ae=Array.isArray(p)?p[X]:p;return{...H,value:qf(H.value,ae)}})),Ne,Fn,Jn,b,Nt),t(82,Fn=R.map(h=>h.map(E=>String(E.value)))),t(81,Jn=Ne.map(h=>h.value))),n.$$.dirty[0]&67108864|n.$$.dirty[1]&4|n.$$.dirty[2]&49152&&(w.filter_state.filter_columns.length>0&&fn(R,Re,Pe),w.sort_state.sort_columns.length>0&&(un(R,Re,Pe),P.update_row_order(R))),n.$$.dirty[0]&67108864&&t(50,c=ko(R)),n.$$.dirty[0]&268435456|n.$$.dirty[2]&2097152&&gt[0]&&gt[0]?.clientWidth&&(clearTimeout(Wn),t(83,Wn=setTimeout(()=>Gt(),100))),n.$$.dirty[0]&268435456|n.$$.dirty[2]&4194304&&gt[0]&&!qt&&(Gt(),t(84,qt=!0)),n.$$.dirty[2]&33554432&&t(49,g=!!i&&i[0]),n.$$.dirty[1]&3|n.$$.dirty[2]&8388608&&(a&&!ln(l,Gn)&&dr(!1),t(85,Gn=l)),n.$$.dirty[2]&33554432&&i!==!1&&t(39,Gl=i),n.$$.dirty[0]&1694498816|n.$$.dirty[2]&33554432&&i!==!1){const h=Bo(i,R,x,fe,_n);document.documentElement.style.setProperty("--selected-col-pos",h.col_pos),document.documentElement.style.setProperty("--selected-row-pos",h.row_pos||"0px")}n.$$.dirty[0]&536870912&&fe&&$l(),n.$$.dirty[2]&16777216&&t(45,L=Zt?.handle_mouse_down||(()=>{})),n.$$.dirty[2]&16777216&&t(44,v=Zt?.handle_mouse_move||(()=>{})),n.$$.dirty[2]&16777216&&t(43,q=Zt?.handle_mouse_up||(()=>{}))},t(42,Qt=hn.is_dragging),xn=hn.drag_start,Zl=hn.mouse_down_pos,[C,j,p,k,y,S,D,O,G,I,z,T,Y,le,K,ce,B,Z,N,J,M,m,Se,mt,x,Ne,R,Dt,gt,fe,_n,l,a,w,$,xe,Pn,Yn,Xn,Gl,cn,Qn,Qt,q,v,L,f,_,r,g,c,o,s,u,A,P,Jl,Fl,Kl,Wl,fr,cr,Kn,Gt,mr,gr,Yl,Xl,br,wr,Zn,$n,pr,b,W,F,Re,Pe,kr,Xt,Ke,Jn,Fn,Wn,qt,Gn,Zt,i,vr,yr,Cr,Sr,Nr,qr,Mr,Ar,Er,Lr,jr,Br,Dr,zr,Tr,Hr,Or,Ir,Rr,Pr,Jr,Fr,Kr,Wr,Ur,Vr,Yr,Xr,Gr,Qr,Zr,$r,xr,eo,to,no]}class ec extends Hf{constructor(e){super(),Pf(this,e,xf,Zf,Ff,{datatype:2,label:3,show_label:4,headers:0,values:1,col_count:5,row_count:6,latex_delimiters:7,components:8,editable:9,wrap:10,root:11,i18n:12,max_height:13,line_breaks:14,column_widths:74,show_row_numbers:15,upload:16,stream_handler:17,show_fullscreen_button:18,show_copy_button:19,value_is_output:73,max_chars:20,show_search:21,pinned_columns:75,static_columns:22,fullscreen:23,display_value:76,styling:77,reset_sort_state:78},null,[-1,-1,-1,-1,-1])}get datatype(){return this.$$.ctx[2]}set datatype(e){this.$$set({datatype:e}),se()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),se()}get show_label(){return this.$$.ctx[4]}set show_label(e){this.$$set({show_label:e}),se()}get headers(){return this.$$.ctx[0]}set headers(e){this.$$set({headers:e}),se()}get values(){return this.$$.ctx[1]}set values(e){this.$$set({values:e}),se()}get col_count(){return this.$$.ctx[5]}set col_count(e){this.$$set({col_count:e}),se()}get row_count(){return this.$$.ctx[6]}set row_count(e){this.$$set({row_count:e}),se()}get latex_delimiters(){return this.$$.ctx[7]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),se()}get components(){return this.$$.ctx[8]}set components(e){this.$$set({components:e}),se()}get editable(){return this.$$.ctx[9]}set editable(e){this.$$set({editable:e}),se()}get wrap(){return this.$$.ctx[10]}set wrap(e){this.$$set({wrap:e}),se()}get root(){return this.$$.ctx[11]}set root(e){this.$$set({root:e}),se()}get i18n(){return this.$$.ctx[12]}set i18n(e){this.$$set({i18n:e}),se()}get max_height(){return this.$$.ctx[13]}set max_height(e){this.$$set({max_height:e}),se()}get line_breaks(){return this.$$.ctx[14]}set line_breaks(e){this.$$set({line_breaks:e}),se()}get column_widths(){return this.$$.ctx[74]}set column_widths(e){this.$$set({column_widths:e}),se()}get show_row_numbers(){return this.$$.ctx[15]}set show_row_numbers(e){this.$$set({show_row_numbers:e}),se()}get upload(){return this.$$.ctx[16]}set upload(e){this.$$set({upload:e}),se()}get stream_handler(){return this.$$.ctx[17]}set stream_handler(e){this.$$set({stream_handler:e}),se()}get show_fullscreen_button(){return this.$$.ctx[18]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),se()}get show_copy_button(){return this.$$.ctx[19]}set show_copy_button(e){this.$$set({show_copy_button:e}),se()}get value_is_output(){return this.$$.ctx[73]}set value_is_output(e){this.$$set({value_is_output:e}),se()}get max_chars(){return this.$$.ctx[20]}set max_chars(e){this.$$set({max_chars:e}),se()}get show_search(){return this.$$.ctx[21]}set show_search(e){this.$$set({show_search:e}),se()}get pinned_columns(){return this.$$.ctx[75]}set pinned_columns(e){this.$$set({pinned_columns:e}),se()}get static_columns(){return this.$$.ctx[22]}set static_columns(e){this.$$set({static_columns:e}),se()}get fullscreen(){return this.$$.ctx[23]}set fullscreen(e){this.$$set({fullscreen:e}),se()}get display_value(){return this.$$.ctx[76]}set display_value(e){this.$$set({display_value:e}),se()}get styling(){return this.$$.ctx[77]}set styling(e){this.$$set({styling:e}),se()}get reset_sort_state(){return this.$$.ctx[78]}}const tc=ec,{SvelteComponent:nc,add_flush_callback:ir,assign:lc,bind:sr,binding_callbacks:rr,create_component:vl,destroy_component:yl,detach:ic,flush:re,get_spread_object:sc,get_spread_update:rc,init:oc,insert:ac,mount_component:Cl,safe_not_equal:_c,space:uc,transition_in:Sl,transition_out:Nl}=window.__gradio__svelte__internal;function fc(n){let e,t,l,i,r;const s=[{autoscroll:n[17].autoscroll},{i18n:n[17].i18n},n[20]];let o={};for(let a=0;a<s.length;a+=1)o=lc(o,s[a]);e=new co({props:o}),e.$on("clear_status",n[29]);function _(a){n[32](a)}let f={root:n[14],label:n[8],show_label:n[9],row_count:n[7],col_count:n[6],values:n[0].data,display_value:n[0].metadata?.display_value,styling:n[0].metadata?.styling,headers:n[0].headers,fullscreen:n[2],wrap:n[10],datatype:n[11],latex_delimiters:n[18],editable:n[21],max_height:n[19],i18n:n[17].i18n,line_breaks:n[15],column_widths:n[16],upload:n[30],stream_handler:n[31],show_fullscreen_button:n[22],max_chars:n[23],show_copy_button:n[24],show_row_numbers:n[25],show_search:n[26],pinned_columns:n[27],components:{image:ho},static_columns:n[28]};return n[1]!==void 0&&(f.value_is_output=n[1]),l=new tc({props:f}),rr.push(()=>sr(l,"value_is_output",_)),l.$on("change",n[33]),l.$on("input",n[34]),l.$on("select",n[35]),l.$on("fullscreen",n[36]),{c(){vl(e.$$.fragment),t=uc(),vl(l.$$.fragment)},m(a,u){Cl(e,a,u),ac(a,t,u),Cl(l,a,u),r=!0},p(a,u){const c=u[0]&1179648?rc(s,[u[0]&131072&&{autoscroll:a[17].autoscroll},u[0]&131072&&{i18n:a[17].i18n},u[0]&1048576&&sc(a[20])]):{};e.$set(c);const g={};u[0]&16384&&(g.root=a[14]),u[0]&256&&(g.label=a[8]),u[0]&512&&(g.show_label=a[9]),u[0]&128&&(g.row_count=a[7]),u[0]&64&&(g.col_count=a[6]),u[0]&1&&(g.values=a[0].data),u[0]&1&&(g.display_value=a[0].metadata?.display_value),u[0]&1&&(g.styling=a[0].metadata?.styling),u[0]&1&&(g.headers=a[0].headers),u[0]&4&&(g.fullscreen=a[2]),u[0]&1024&&(g.wrap=a[10]),u[0]&2048&&(g.datatype=a[11]),u[0]&262144&&(g.latex_delimiters=a[18]),u[0]&2097152&&(g.editable=a[21]),u[0]&524288&&(g.max_height=a[19]),u[0]&131072&&(g.i18n=a[17].i18n),u[0]&32768&&(g.line_breaks=a[15]),u[0]&65536&&(g.column_widths=a[16]),u[0]&131072&&(g.upload=a[30]),u[0]&131072&&(g.stream_handler=a[31]),u[0]&4194304&&(g.show_fullscreen_button=a[22]),u[0]&8388608&&(g.max_chars=a[23]),u[0]&16777216&&(g.show_copy_button=a[24]),u[0]&33554432&&(g.show_row_numbers=a[25]),u[0]&67108864&&(g.show_search=a[26]),u[0]&134217728&&(g.pinned_columns=a[27]),u[0]&268435456&&(g.static_columns=a[28]),!i&&u[0]&2&&(i=!0,g.value_is_output=a[1],ir(()=>i=!1)),l.$set(g)},i(a){r||(Sl(e.$$.fragment,a),Sl(l.$$.fragment,a),r=!0)},o(a){Nl(e.$$.fragment,a),Nl(l.$$.fragment,a),r=!1},d(a){a&&ic(t),yl(e,a),yl(l,a)}}}function cc(n){let e,t,l;function i(s){n[37](s)}let r={visible:n[5],padding:!1,elem_id:n[3],elem_classes:n[4],container:!1,scale:n[12],min_width:n[13],overflow_behavior:"visible",$$slots:{default:[fc]},$$scope:{ctx:n}};return n[2]!==void 0&&(r.fullscreen=n[2]),e=new lo({props:r}),rr.push(()=>sr(e,"fullscreen",i)),{c(){vl(e.$$.fragment)},m(s,o){Cl(e,s,o),l=!0},p(s,o){const _={};o[0]&32&&(_.visible=s[5]),o[0]&8&&(_.elem_id=s[3]),o[0]&16&&(_.elem_classes=s[4]),o[0]&4096&&(_.scale=s[12]),o[0]&8192&&(_.min_width=s[13]),o[0]&536858567|o[1]&128&&(_.$$scope={dirty:o,ctx:s}),!t&&o[0]&4&&(t=!0,_.fullscreen=s[2],ir(()=>t=!1)),e.$set(_)},i(s){l||(Sl(e.$$.fragment,s),l=!0)},o(s){Nl(e.$$.fragment,s),l=!1},d(s){yl(e,s)}}}function hc(n,e,t){let{elem_id:l=""}=e,{elem_classes:i=[]}=e,{visible:r=!0}=e,{value:s={data:[["","",""]],headers:["1","2","3"],metadata:null}}=e,{value_is_output:o=!1}=e,{col_count:_}=e,{row_count:f}=e,{label:a=null}=e,{show_label:u=!0}=e,{wrap:c}=e,{datatype:g}=e,{scale:L=null}=e,{min_width:v=void 0}=e,{root:q}=e,{line_breaks:w=!0}=e,{column_widths:p=[]}=e,{gradio:k}=e,{latex_delimiters:y}=e,{max_height:C=void 0}=e,{loading_status:j}=e,{interactive:S}=e,{show_fullscreen_button:D=!1}=e,{max_chars:O=void 0}=e,{show_copy_button:G=!1}=e,{show_row_numbers:I=!1}=e,{show_search:z="none"}=e,{pinned_columns:T=0}=e,{static_columns:Y=[]}=e,{fullscreen:le=!1}=e;const K=()=>k.dispatch("clear_status",j),W=(...m)=>k.client.upload(...m),ce=(...m)=>k.client.stream(...m);function B(m){o=m,t(1,o)}const Z=m=>{t(0,s.data=m.detail.data,s),t(0,s.headers=m.detail.headers,s),k.dispatch("change")},N=m=>k.dispatch("input"),J=m=>k.dispatch("select",m.detail),b=({detail:m})=>{t(2,le=m)};function M(m){le=m,t(2,le)}return n.$$set=m=>{"elem_id"in m&&t(3,l=m.elem_id),"elem_classes"in m&&t(4,i=m.elem_classes),"visible"in m&&t(5,r=m.visible),"value"in m&&t(0,s=m.value),"value_is_output"in m&&t(1,o=m.value_is_output),"col_count"in m&&t(6,_=m.col_count),"row_count"in m&&t(7,f=m.row_count),"label"in m&&t(8,a=m.label),"show_label"in m&&t(9,u=m.show_label),"wrap"in m&&t(10,c=m.wrap),"datatype"in m&&t(11,g=m.datatype),"scale"in m&&t(12,L=m.scale),"min_width"in m&&t(13,v=m.min_width),"root"in m&&t(14,q=m.root),"line_breaks"in m&&t(15,w=m.line_breaks),"column_widths"in m&&t(16,p=m.column_widths),"gradio"in m&&t(17,k=m.gradio),"latex_delimiters"in m&&t(18,y=m.latex_delimiters),"max_height"in m&&t(19,C=m.max_height),"loading_status"in m&&t(20,j=m.loading_status),"interactive"in m&&t(21,S=m.interactive),"show_fullscreen_button"in m&&t(22,D=m.show_fullscreen_button),"max_chars"in m&&t(23,O=m.max_chars),"show_copy_button"in m&&t(24,G=m.show_copy_button),"show_row_numbers"in m&&t(25,I=m.show_row_numbers),"show_search"in m&&t(26,z=m.show_search),"pinned_columns"in m&&t(27,T=m.pinned_columns),"static_columns"in m&&t(28,Y=m.static_columns),"fullscreen"in m&&t(2,le=m.fullscreen)},[s,o,le,l,i,r,_,f,a,u,c,g,L,v,q,w,p,k,y,C,j,S,D,O,G,I,z,T,Y,K,W,ce,B,Z,N,J,b,M]}class rh extends nc{constructor(e){super(),oc(this,e,hc,cc,_c,{elem_id:3,elem_classes:4,visible:5,value:0,value_is_output:1,col_count:6,row_count:7,label:8,show_label:9,wrap:10,datatype:11,scale:12,min_width:13,root:14,line_breaks:15,column_widths:16,gradio:17,latex_delimiters:18,max_height:19,loading_status:20,interactive:21,show_fullscreen_button:22,max_chars:23,show_copy_button:24,show_row_numbers:25,show_search:26,pinned_columns:27,static_columns:28,fullscreen:2},null,[-1,-1])}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),re()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),re()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),re()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),re()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),re()}get col_count(){return this.$$.ctx[6]}set col_count(e){this.$$set({col_count:e}),re()}get row_count(){return this.$$.ctx[7]}set row_count(e){this.$$set({row_count:e}),re()}get label(){return this.$$.ctx[8]}set label(e){this.$$set({label:e}),re()}get show_label(){return this.$$.ctx[9]}set show_label(e){this.$$set({show_label:e}),re()}get wrap(){return this.$$.ctx[10]}set wrap(e){this.$$set({wrap:e}),re()}get datatype(){return this.$$.ctx[11]}set datatype(e){this.$$set({datatype:e}),re()}get scale(){return this.$$.ctx[12]}set scale(e){this.$$set({scale:e}),re()}get min_width(){return this.$$.ctx[13]}set min_width(e){this.$$set({min_width:e}),re()}get root(){return this.$$.ctx[14]}set root(e){this.$$set({root:e}),re()}get line_breaks(){return this.$$.ctx[15]}set line_breaks(e){this.$$set({line_breaks:e}),re()}get column_widths(){return this.$$.ctx[16]}set column_widths(e){this.$$set({column_widths:e}),re()}get gradio(){return this.$$.ctx[17]}set gradio(e){this.$$set({gradio:e}),re()}get latex_delimiters(){return this.$$.ctx[18]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),re()}get max_height(){return this.$$.ctx[19]}set max_height(e){this.$$set({max_height:e}),re()}get loading_status(){return this.$$.ctx[20]}set loading_status(e){this.$$set({loading_status:e}),re()}get interactive(){return this.$$.ctx[21]}set interactive(e){this.$$set({interactive:e}),re()}get show_fullscreen_button(){return this.$$.ctx[22]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),re()}get max_chars(){return this.$$.ctx[23]}set max_chars(e){this.$$set({max_chars:e}),re()}get show_copy_button(){return this.$$.ctx[24]}set show_copy_button(e){this.$$set({show_copy_button:e}),re()}get show_row_numbers(){return this.$$.ctx[25]}set show_row_numbers(e){this.$$set({show_row_numbers:e}),re()}get show_search(){return this.$$.ctx[26]}set show_search(e){this.$$set({show_search:e}),re()}get pinned_columns(){return this.$$.ctx[27]}set pinned_columns(e){this.$$set({pinned_columns:e}),re()}get static_columns(){return this.$$.ctx[28]}set static_columns(e){this.$$set({static_columns:e}),re()}get fullscreen(){return this.$$.ctx[2]}set fullscreen(e){this.$$set({fullscreen:e}),re()}}export{tc as BaseDataFrame,_h as BaseExample,rh as default};
//# sourceMappingURL=Index-B0grnMRw.js.map
