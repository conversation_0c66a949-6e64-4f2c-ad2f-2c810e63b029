"""
美化的加载动画组件
提供各种加载指示器
"""

import math
from PyQt6.QtWidgets import QWidget, QLabel, QVBoxLayout, QHBoxLayout
from PyQt6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal
from PyQt6.QtGui import QPainter, QPen, QColor, QFont

class LoadingSpinner(QWidget):
    """旋转加载指示器"""
    
    def __init__(self, size=32, color="#58a6ff", parent=None):
        super().__init__(parent)
        self.size = size
        self.color = QColor(color)
        self.angle = 0
        
        self.setFixedSize(size, size)
        
        # 动画定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self.rotate)
        
    def start(self):
        """开始动画"""
        self.timer.start(50)  # 每50ms更新一次
        
    def stop(self):
        """停止动画"""
        self.timer.stop()
        
    def rotate(self):
        """旋转动画"""
        self.angle = (self.angle + 10) % 360
        self.update()
        
    def paintEvent(self, event):
        """绘制旋转指示器"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 设置画笔
        pen = QPen(self.color, 3)
        pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        painter.setPen(pen)
        
        # 绘制圆弧
        rect = self.rect().adjusted(5, 5, -5, -5)
        painter.drawArc(rect, self.angle * 16, 120 * 16)

class LoadingDots(QWidget):
    """点状加载指示器"""
    
    def __init__(self, dot_count=3, color="#58a6ff", parent=None):
        super().__init__(parent)
        self.dot_count = dot_count
        self.color = QColor(color)
        self.current_dot = 0
        
        self.setFixedSize(dot_count * 20, 20)
        
        # 动画定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self.next_dot)
        
    def start(self):
        """开始动画"""
        self.timer.start(300)  # 每300ms切换一个点
        
    def stop(self):
        """停止动画"""
        self.timer.stop()
        
    def next_dot(self):
        """切换到下一个点"""
        self.current_dot = (self.current_dot + 1) % self.dot_count
        self.update()
        
    def paintEvent(self, event):
        """绘制点状指示器"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        dot_size = 6
        spacing = 20
        
        for i in range(self.dot_count):
            x = i * spacing + 10
            y = 10
            
            # 当前点使用高亮颜色，其他点使用暗色
            if i == self.current_dot:
                painter.setBrush(self.color)
            else:
                dark_color = QColor(self.color)
                dark_color.setAlpha(100)
                painter.setBrush(dark_color)
                
            painter.setPen(Qt.PenStyle.NoPen)
            painter.drawEllipse(x - dot_size//2, y - dot_size//2, dot_size, dot_size)

class LoadingPulse(QWidget):
    """脉冲加载指示器"""
    
    def __init__(self, size=32, color="#58a6ff", parent=None):
        super().__init__(parent)
        self.size = size
        self.color = QColor(color)
        self.scale = 1.0
        self.growing = True
        
        self.setFixedSize(size, size)
        
        # 动画定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self.pulse)
        
    def start(self):
        """开始动画"""
        self.timer.start(50)
        
    def stop(self):
        """停止动画"""
        self.timer.stop()
        
    def pulse(self):
        """脉冲动画"""
        if self.growing:
            self.scale += 0.05
            if self.scale >= 1.3:
                self.growing = False
        else:
            self.scale -= 0.05
            if self.scale <= 0.7:
                self.growing = True
                
        self.update()
        
    def paintEvent(self, event):
        """绘制脉冲指示器"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 计算缩放后的大小
        scaled_size = int(self.size * self.scale * 0.6)
        x = (self.size - scaled_size) // 2
        y = (self.size - scaled_size) // 2
        
        # 设置颜色和透明度
        color = QColor(self.color)
        color.setAlpha(int(255 * (2.0 - self.scale)))
        painter.setBrush(color)
        painter.setPen(Qt.PenStyle.NoPen)
        
        # 绘制圆形
        painter.drawEllipse(x, y, scaled_size, scaled_size)

class LoadingWidget(QWidget):
    """完整的加载组件"""
    
    def __init__(self, text="加载中...", loading_type="spinner", parent=None):
        super().__init__(parent)
        self.loading_type = loading_type
        self.init_ui(text)
        
    def init_ui(self, text):
        """初始化UI"""
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.setSpacing(15)
        
        # 创建加载指示器
        if self.loading_type == "spinner":
            self.indicator = LoadingSpinner(48, "#58a6ff")
        elif self.loading_type == "dots":
            self.indicator = LoadingDots(3, "#58a6ff")
        elif self.loading_type == "pulse":
            self.indicator = LoadingPulse(48, "#58a6ff")
        else:
            self.indicator = LoadingSpinner(48, "#58a6ff")
            
        # 居中指示器
        indicator_layout = QHBoxLayout()
        indicator_layout.addStretch()
        indicator_layout.addWidget(self.indicator)
        indicator_layout.addStretch()
        
        # 加载文本
        self.text_label = QLabel(text)
        self.text_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.text_label.setFont(QFont("Microsoft YaHei UI", 12))
        self.text_label.setStyleSheet("""
            QLabel {
                color: #f0f6fc;
                background: transparent;
                padding: 5px;
            }
        """)
        
        layout.addLayout(indicator_layout)
        layout.addWidget(self.text_label)
        
        self.setLayout(layout)
        
    def start_loading(self):
        """开始加载动画"""
        self.indicator.start()
        self.show()
        
    def stop_loading(self):
        """停止加载动画"""
        self.indicator.stop()
        self.hide()
        
    def set_text(self, text):
        """设置加载文本"""
        self.text_label.setText(text)

class TypingIndicator(QWidget):
    """打字指示器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout()
        layout.setContentsMargins(12, 8, 12, 8)
        
        # AI头像
        avatar_label = QLabel("🤖")
        avatar_label.setFont(QFont("Segoe UI Emoji", 16))
        
        # 打字动画
        self.dots_indicator = LoadingDots(3, "#8b949e")
        
        # 文本
        text_label = QLabel("正在输入")
        text_label.setFont(QFont("Microsoft YaHei UI", 11))
        text_label.setStyleSheet("color: #8b949e;")
        
        layout.addWidget(avatar_label)
        layout.addWidget(text_label)
        layout.addWidget(self.dots_indicator)
        layout.addStretch()
        
        self.setLayout(layout)
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #161b22, stop:1 #21262d);
                border-radius: 12px;
                border: 1px solid #30363d;
                margin: 5px;
            }
        """)
        
    def start_typing(self):
        """开始打字动画"""
        self.dots_indicator.start()
        self.show()
        
    def stop_typing(self):
        """停止打字动画"""
        self.dots_indicator.stop()
        self.hide()
