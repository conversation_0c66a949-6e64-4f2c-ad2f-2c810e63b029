{"version": 3, "mappings": ";mWAOO,MAAMA,CAAa,CAOtB,YAAYC,EAAQC,EAAU,GAAI,CAC9B,KAAK,qBAAuB,EAC5B,KAAK,oBAAsB,GAK3B,KAAK,QAAU,KAIf,KAAK,SAAW,EAEhB,KAAK,QAAUD,EACf,KAAK,SAAWC,EAAQ,UAAY,KAAK,SACzC,KAAK,QAAUA,EAAQ,SAAW,KAAK,OAC1C,CACD,oBAAoBC,EAAM,CACtB,IAAIC,EAAc,EACd,KAAK,QAAQ,QAAO,EAAG,uBACvBA,EAAc,EAET,KAAK,QAAQ,QAAO,EAAG,qBAC5BA,EAAc,GAElB,MAAMC,EAAY,KAAK,QAAQ,8BAA8BF,EAAM,CAC/D,OAAQ,EACR,KAAMC,EACN,cAAe,GACf,gBAAiB,GACjB,oBAAqB,GACrB,sBAAuB,GACvB,aAAc,EACd,MAAO,+BACnB,CAAS,EACD,YAAK,QAAQ,0BAA0BC,EAAU,QAAS,EAAG,EAAG,CAAC,EACjE,KAAK,QAAQ,0BAA0B,EAAGA,EAAU,QAAS,EAAI,EAC1DA,CACV,CACD,mBAAmBC,EAAS,CACxB,MAAMC,EAAQD,EAAQ,QAAO,EAAG,MAC1BE,EAAeC,EAAMF,CAAK,EAAI,EAC9BG,EAAS,KAAK,eAAe,OAC7BC,EAAgB,KAAK,oBAAoBJ,CAAK,EACpD,KAAK,gBAAgB,aACrB,KAAK,gBAAgB,cACrB,MAAMK,EAAaN,EAAQ,qBACvBM,GAEA,KAAK,QAAQ,0BAA0B,EAAGA,EAAY,EAAI,EAE9D,KAAK,gBAAgB,mBAAmB,KAAK,cAAc,EAC3D,MAAMC,EAAa,CACf,CAAC,IAAIC,EAAQ,EAAG,EAAG,EAAE,EAAG,IAAIA,EAAQ,EAAG,GAAI,CAAC,EAAG,IAAIA,EAAQ,EAAG,EAAG,CAAC,CAAC,EACnE,CAAC,IAAIA,EAAQ,EAAG,EAAG,CAAC,EAAG,IAAIA,EAAQ,EAAG,GAAI,CAAC,EAAG,IAAIA,EAAQ,GAAI,EAAG,CAAC,CAAC,EACnE,CAAC,IAAIA,EAAQ,EAAG,EAAG,CAAC,EAAG,IAAIA,EAAQ,EAAG,EAAG,CAAC,EAAG,IAAIA,EAAQ,EAAG,EAAG,CAAC,CAAC,EACjE,CAAC,IAAIA,EAAQ,EAAG,EAAG,CAAC,EAAG,IAAIA,EAAQ,EAAG,EAAG,EAAE,EAAG,IAAIA,EAAQ,EAAG,GAAI,CAAC,CAAC,EACnE,CAAC,IAAIA,EAAQ,EAAG,EAAG,CAAC,EAAG,IAAIA,EAAQ,EAAG,GAAI,CAAC,EAAG,IAAIA,EAAQ,EAAG,EAAG,CAAC,CAAC,EAClE,CAAC,IAAIA,EAAQ,GAAI,EAAG,CAAC,EAAG,IAAIA,EAAQ,EAAG,GAAI,CAAC,EAAG,IAAIA,EAAQ,EAAG,EAAG,EAAE,CAAC,CAChF,EACQJ,EAAO,SAAS,WAAY,KAAK,QAAQ,EACzCA,EAAO,UAAU,iBAAkBJ,EAAQ,UAAU,MAAOE,CAAY,EACxEE,EAAO,WAAW,eAAgBJ,CAAO,EACzC,QAASS,EAAO,EAAGA,EAAO,EAAGA,IAAQ,CACjCL,EAAO,WAAW,KAAMG,EAAWE,CAAI,EAAE,CAAC,CAAC,EAC3CL,EAAO,WAAW,QAASG,EAAWE,CAAI,EAAE,CAAC,CAAC,EAC9CL,EAAO,WAAW,QAASG,EAAWE,CAAI,EAAE,CAAC,CAAC,EAC9C,QAASC,EAAM,EAAGA,EAAMR,EAAcQ,IAAO,CACzC,KAAK,QAAQ,gBAAgBL,EAAeI,EAAM,OAAW,OAAW,GAAMC,CAAG,EACjF,KAAK,gBAAgB,mBAAmB,KAAK,cAAc,EAC3D,IAAIC,EAAQ,KAAK,IAAI,GAAID,EAAM,KAAK,sBAAwB,KAAK,mBAAmB,EAAIT,EACpFS,IAAQ,IACRC,EAAQ,GAEZP,EAAO,SAAS,SAAUO,CAAK,EAC/B,KAAK,gBAAgB,MACxB,CACJ,CAED,KAAK,gBAAgB,gBACrB,KAAK,QAAQ,4BACb,KAAK,QAAQ,gBAAgBX,EAAQ,QAAQ,EAE7C,MAAMY,EAAOP,EAAc,QAAQ,KAC7BQ,EAASR,EAAc,QAAQ,OACrC,OAAAA,EAAc,YAAYL,EAAQ,QAAQ,EAC1CA,EAAQ,SAAS,KAAOY,EACxBZ,EAAQ,SAAS,OAASa,EAE1Bb,EAAQ,WAAa,GACrBA,EAAQ,oBAAsB,KAAK,qBACnCA,EAAQ,mBAAqB,KAAK,oBAClCA,EAAQ,aAAe,GAChBA,CACV,CACD,cAAcA,EAASc,EAAY,CAC/B,MAAMC,EAAU,GACZf,EAAQ,YACRe,EAAQ,KAAK,qBAAqB,EAEtCA,EAAQ,KAAK,uBAAyB,KAAK,QAAU,GAAG,EACxD,MAAMC,EAAW,KAAK,QAAQ,SAqB9B,OApBsB,IAAIC,EAAc,CACpC,OAAQ,KAAK,QACb,KAAM,eACN,aAAc,eACd,eAAgB,eAChB,aAAc,CAAC,cAAc,EAC7B,aAAc,CAAC,oBAAqB,WAAY,KAAM,QAAS,QAAS,iBAAkB,WAAY,QAAQ,EAC9G,eAAgB,GAChB,QAAAF,EACA,WAAYD,EACZ,eAAgBE,EAAW,EAA8B,EACzD,0BAA2B,SAAY,CAC/BA,EACA,MAAM,QAAQ,IAAI,CAAAE,EAAA,IAAC,OAAO,mCAA6C,4CAAGA,EAAA,WAAO,qCAA+C,EAAC,8CAAC,EAGlI,MAAM,QAAQ,IAAI,CAAAA,EAAA,IAAC,OAAO,mCAAyC,4CAAGA,EAAA,WAAO,qCAA2C,EAAC,8CAAC,CAEjI,CACb,CAAS,CAEJ,CAMD,QAAQlB,EAAS,CACb,OAAOA,EAAQ,WAAa,KAAK,eAAe,OAAO,SAC1D,CASD,MAAM,UAAUA,EAAS,CACrB,GAAI,CAAC,KAAK,QAAQ,UAAU,yBACxB,MAAM,IAAI,MAAM,yFAAyF,EAE7G,KAAK,gBAAkB,IAAImB,EAAe,KAAK,OAAO,EACtD,KAAK,eAAiB,KAAK,cAAcnB,CAAO,EAChD,MAAM,KAAK,eAAe,OAAO,kBAAiB,EAClD,KAAK,mBAAmBA,CAAO,EAC/B,KAAK,gBAAgB,UACrB,KAAK,eAAe,SACvB,CACL,CC/JO,MAAMoB,CAAgC,CAKzC,YAAYC,EAAO,CAIf,KAAK,KAAOC,EAAwB,uBACpC,KAAK,MAAQD,CAChB,CAID,UAAW,CACP,KAAK,MAAM,kBAAkB,aAAaC,EAAwB,mCAAoC,KAAM,KAAK,YAAY,CAChI,CAKD,SAAU,CAET,CAID,SAAU,CAET,CACD,cAAe,CACX,GAAI,KAAK,MAAM,0BAA2B,CACtCC,EAAM,wBAAwB,sBAAuB,KAAK,MAAM,mBAAmB,OAAS,CAAC,EAC7F,QAASC,EAAkB,EAAGA,EAAkB,KAAK,MAAM,mBAAmB,OAAQA,IAAmB,CACrG,MAAMC,EAAoB,KAAK,MAAM,mBAAmBD,CAAe,EACnEC,EAAkB,iBAClBA,EAAkB,OAAM,CAE/B,CACDF,EAAM,sBAAsB,sBAAuB,KAAK,MAAM,mBAAmB,OAAS,CAAC,CAC9F,CACJ,CACL,CC/BO,MAAMG,UAA0BC,CAAQ,CAI3C,IAAI,gBAAiB,CACjB,OAAO,KAAK,eACf,CAkBD,YAAYC,EAAM/B,EAAMgC,EAAUR,EAAOS,EAAkB,KAAMC,EAAkB,GAAMC,EAAS,GAAOlC,EAAc,EAAG,CACtH,MAAM,KAAMuB,EAAO,CAACU,CAAe,EAInC,KAAK,UAAY,GAIjB,KAAK,UAAY,GAIjB,KAAK,sBAAwB,IAAIE,EAIjC,KAAK,6BAA+B,IAAIA,EAIxC,KAAK,mBAAqB,KAI1B,KAAK,QAAU,GAEf,KAAK,UAAY,GACjB,KAAK,kBAAoB,GACzB,KAAK,SAAW,GAChB,KAAK,aAAe,EACpB,KAAK,eAAiB,GACtB,KAAK,UAAY,IAAI,MACrB,KAAK,UAAY,IAAI,MACrB,KAAK,QAAU,GACf,KAAK,MAAQ,GACb,KAAK,cAAgB,GACrB,KAAK,SAAW,GAChB,KAAK,SAAW,GAChB,KAAK,UAAY,GACjB,KAAK,UAAY,GACjB,KAAK,UAAY,GACjB,KAAK,UAAY,GACjB,KAAK,qBAAuB,GAC5B,KAAK,eAAiB,KACtB,KAAK,iBAAmB,GACxB,KAAK,WAAa,KACdH,IAAoB,MAAQ,EAAEA,aAA2BH,IACzD,KAAK,SAAWG,EAChB,KAAK,iBAAmBA,EAAgB,iBAAmB,OAG3D,KAAK,SAAW,GAChB,KAAK,iBAAmBA,GAE5B,KAAK,gBAAkB,KAAK,SAAS,gBAAkB,EACvDT,EAAQ,KAAK,YAAca,EAAY,iBACvC,IAAIC,EAAYd,EAAM,cAAcC,EAAwB,sBAAsB,EAC7Ea,IACDA,EAAY,IAAIf,EAAgCC,CAAK,EACrDA,EAAM,cAAcc,CAAS,GAEjCd,EAAM,mBAAmB,KAAK,IAAI,EAClC,KAAK,YAAcA,EAAM,YACzB,KAAK,KAAOO,EACZ,KAAK,eAAiB,GACtB,KAAK,MAAQ/B,EACb,KAAK,aAAeC,EACpB,KAAK,iBAAmBiC,EACxB,KAAK,aAAe,IAAIK,EAAY,KAAK,WAAW,EACpD,KAAK,YAAYP,CAAQ,EACzB,MAAM9B,EAAY,KAAK,iBAAiBiC,EAAQnC,EAAMkC,EAAiBjC,CAAW,EAClF,KAAK,SAAWC,EAAU,QAE1B,MAAMsC,EAAW,GACjBA,EAAS,KAAK,EAAG,CAAC,EAClBA,EAAS,KAAK,GAAI,CAAC,EACnBA,EAAS,KAAK,GAAI,EAAE,EACpBA,EAAS,KAAK,EAAG,EAAE,EACnB,KAAK,eAAeC,EAAa,YAAY,EAAI,IAAIA,EAAa,KAAK,YAAaD,EAAUC,EAAa,aAAc,GAAO,GAAO,CAAC,EACxI,KAAK,mBAAkB,CAC1B,CACD,iBAAiBN,EAAQnC,EAAMkC,EAAiBjC,EAAa,CACzD,OAAIkC,GACA,KAAK,WAAa,KAAK,YAAY,8BAA8BnC,EAAM,CACnE,gBAAiBkC,EACjB,oBAAqB,GACrB,sBAAuB,GACvB,KAAMjC,EACN,GAAG,KAAK,QACxB,CAAa,EACD,KAAK,SAAS,OAAQ,CAAC,IAGvB,KAAK,WAAa,KAAK,YAAY,0BAA0BD,EAAM,CAC/D,gBAAiBkC,EACjB,oBAAqB,GACrB,sBAAuB,GACvB,KAAMjC,EACN,GAAG,KAAK,QACxB,CAAa,EACG,KAAK,WAAW,OAChB,KAAK,SAAS,QAAS,CAAC,EACxB,KAAK,OAAO,WAAY,CAAC,IAG1B,KAAK,UACf,CAKD,WAAY,CACR,OAAO,KAAK,aAAa,MAC5B,CAID,WAAWM,EAAQ,CACf,KAAK,aAAa,OAASA,CAC9B,CAKD,YAAa,CACT,OAAI,KAAK,cAAgB,KAAK,WAAa,KAAK,iBACrC,KAAK,cAEZ,KAAK,aACL,KAAK,aAAa,KAAMmC,GAAW,CAC/B,KAAK,aAAe,KAAK,WAAW,EAAG,EAAGA,CAAM,EAChD,KAAK,iBAAmB,KAAK,QAC7C,CAAa,GAGD,KAAK,aAAe,KAAK,WAAW,EAAG,CAAC,EACxC,KAAK,iBAAmB,KAAK,UAE1B,KAAK,aACf,CACD,oBAAqB,CACjB,MAAM5C,EAAS,KAAK,YAEd6C,EAAU,GAChBA,EAAQ,KAAK,CAAC,EACdA,EAAQ,KAAK,CAAC,EACdA,EAAQ,KAAK,CAAC,EACdA,EAAQ,KAAK,CAAC,EACdA,EAAQ,KAAK,CAAC,EACdA,EAAQ,KAAK,CAAC,EACd,KAAK,aAAe7C,EAAO,kBAAkB6C,CAAO,CACvD,CAED,UAAW,CACP,MAAMC,EAAK,KAAK,eAAeH,EAAa,YAAY,EACpDG,GACAA,EAAG,SAAQ,EAEf,KAAK,mBAAkB,EACnB,KAAK,cAAgBC,EAAoB,0BACzC,KAAK,YAAcA,EAAoB,wBAE9C,CAKD,OAAQ,QACJC,EAAA,KAAK,aAAa,SAAlB,MAAAA,EAA0B,UAC1B,KAAK,aAAa,OAAS,KAC3B,KAAK,eAAiB,IACzB,CACD,aAAc,CACV,OAAO,KAAK,OACf,CAKD,iBAAiBC,EAAM,CACnB,GAAI,KAAK,UAAW,CAChBA,EAAK,IAAI,EACT,MACH,CACD,MAAMxC,EAAS,KAAK,YAChBA,GACAA,EAAO,oBAAoB,IAAM,CAC7BwC,EAAK,IAAI,CACzB,CAAa,CAER,CAKD,SAAU,CACN,MAAMjD,EAAS,KAAK,YACpB,GAAI,KAAK,mBACL,OAAO,KAAK,aAAa,OAAO,QAAO,EAE3C,GAAI,CAAC,KAAK,UACN,MAAO,GAEX,GAAI,KAAK,qBACL,MAAO,GAEX,GAAI,CAAC,KAAK,SACN,MAAO,GAEX,MAAMoB,EAAU,KAAK,cACrB,GAAI,KAAK,aAAa,QAAUA,IAAY,KAAK,gBAAkB,KAAK,aAAa,OAAO,QAAO,EAC/F,MAAO,GAEX,MAAM8B,EAAU,CACZ,OAAQ,aACR,gBAAiB,KAAK,UAAU,gBAChC,eAAgB,KAAK,UAAU,eAC/B,SAAU,OAAO,KAAK,WAAc,SAAW,KAAK,UAAY,MAC5E,EACQ,OAAI,KAAK,iBAAmB9B,IACxB,KAAK,eAAiBA,EACtB,KAAK,aAAa,OAASpB,EAAO,aAAakD,EAAS,CAACP,EAAa,YAAY,EAAG,KAAK,UAAW,KAAK,UAAWvB,EAAS,OAAW,OAAW,IAAM,QACtJ4B,EAAA,KAAK,aAAL,MAAAA,EAAiB,UACjB,KAAK,WAAa,KAAK,SAAW,KAC9B,KAAK,mBACL,KAAK,SAAW,KAAK,iBAAiB,SAClC,KAAK,UACL,KAAK,SAAS,uBAGtB,KAAK,qBAAuB,EAC5C,EAAe,OAAW,KAAK,gBAAiB,SAAY,CACxC,KAAK,SAAS,0BACV,KAAK,iBAAmB,EACxB,MAAM,QAAQ,IAAI,CAACzB,EAAA,WAAO,iCAA2C,6CAAG,KAAK,SAAS,0BAA2B,EAAC,EAGlH,MAAM,QAAQ,IAAI,CAACA,EAAA,WAAO,iCAAuC,6CAAG,KAAK,SAAS,0BAA2B,EAAC,EAI9G,KAAK,iBAAmB,EACxB,MAAMA,EAAA,WAAO,iCAA2C,6CAGxD,MAAMA,EAAA,WAAO,iCAAuC,4CAG5E,CAAa,GAEE,KAAK,aAAa,OAAO,QAAO,CAC1C,CAKD,qBAAsB,CAClB,KAAK,kBAAoB,EAC5B,CAKD,YAAYW,EAAU,CAClB,KAAK,UAAYA,CACpB,CAKD,IAAI,aAAc,CACd,OAAO,KAAK,YACf,CACD,IAAI,YAAYiB,EAAO,CACnB,KAAK,aAAeA,EACpB,KAAK,oBAAmB,CAC3B,CAED,eAAgB,CACZ,MAAI,CAAC,KAAK,WAAa,CAAC,KAAK,WAAa,CAAC,KAAK,UACxC,KAAK,WACL,KAAK,SAAS,QAAU,IAErB,IAEP,KAAK,qBACE,GAEP,KAAK,oBAAsB,IAE3B,KAAK,kBAAoB,EACzB,KAAK,WACE,IAEP,KAAK,cAAgB,KAAK,mBAC1B,KAAK,kBAAoB,EACzB,KAAK,WACE,KAEX,KAAK,oBACE,GACV,CAKD,eAAgB,CACZ,OAAO,KAAK,KACf,CAMD,OAAOjD,EAAMkC,EAAiB,CAC1B,GAAI,KAAK,sBAAwB,CAAC,KAAK,YAAc,CAAC,KAAK,SACvD,OAEJ,MAAMC,EAAS,KAAK,SAAS,OAC7B,KAAK,WAAW,UAChB,MAAMjC,EAAY,KAAK,iBAAiBiC,EAAQnC,EAAMkC,EAAiB,KAAK,YAAY,EACxF,KAAK,SAAWhC,EAAU,QAE1B,KAAK,MAAQF,EACb,KAAK,iBAAmBkC,CAC3B,CACD,cAAcgB,EAAa,CACnB,KAAK,UAAU,QAAQA,CAAW,IAAM,IACxC,KAAK,UAAU,KAAKA,CAAW,CAEtC,CAOD,WAAWnB,EAAM5B,EAAS,CACtB,OAAI,KAAK,UAAU,QAAQ4B,CAAI,IAAM,IACjC,KAAK,UAAU,KAAKA,CAAI,EAE5B,KAAK,UAAUA,CAAI,EAAI5B,EAChB,IACV,CAOD,SAAS4B,EAAMkB,EAAO,CAClB,YAAK,cAAclB,CAAI,EACvB,KAAK,QAAQA,CAAI,EAAIkB,EACd,IACV,CAOD,OAAOlB,EAAMkB,EAAO,CAChB,YAAK,cAAclB,CAAI,EACvB,KAAK,MAAMA,CAAI,EAAIkB,EACZ,IACV,CAOD,UAAUlB,EAAMkB,EAAO,CACnB,YAAK,cAAclB,CAAI,EACvB,KAAK,cAAcA,CAAI,EAAIkB,EACpB,IACV,CAOD,UAAUlB,EAAMkB,EAAO,CACnB,YAAK,cAAclB,CAAI,EACvB,KAAK,SAASA,CAAI,EAAIkB,EACf,IACV,CAOD,UAAUlB,EAAMkB,EAAO,CACnB,YAAK,cAAclB,CAAI,EACvB,KAAK,SAASA,CAAI,EAAIkB,EACf,IACV,CAOD,WAAWlB,EAAMkB,EAAO,CACpB,YAAK,cAAclB,CAAI,EACvB,KAAK,UAAUA,CAAI,EAAIkB,EAChB,IACV,CAOD,WAAWlB,EAAMkB,EAAO,CACpB,YAAK,cAAclB,CAAI,EACvB,KAAK,UAAUA,CAAI,EAAIkB,EAChB,IACV,CAOD,WAAWlB,EAAMkB,EAAO,CACpB,YAAK,cAAclB,CAAI,EACvB,KAAK,UAAUA,CAAI,EAAIkB,EAChB,IACV,CAOD,UAAUlB,EAAMkB,EAAO,CACnB,YAAK,cAAclB,CAAI,EACvB,KAAK,UAAUA,CAAI,EAAIkB,EAChB,IACV,CAMD,OAAOE,EAAsB,aACzB,MAAM3B,EAAQ,KAAK,WACnB,GAAI,CAACA,EACD,OAEJ,MAAM1B,EAAS,KAAK,YAKpB,GAHAA,EAAO,aAAa,KAAK,YAAY,EACrC,KAAK,6BAA6B,gBAAgB,IAAI,EACtDA,EAAO,SAAS,EAAK,EACjB,CAAC,KAAK,mBAAoB,CAE1B,UAAWiC,KAAQ,KAAK,UACpB,KAAK,aAAa,OAAO,WAAWA,EAAM,KAAK,UAAUA,CAAI,CAAC,EAGlE,UAAWA,KAAQ,KAAK,MACpB,KAAK,aAAa,OAAO,OAAOA,EAAM,KAAK,MAAMA,CAAI,CAAC,EAG1D,UAAWA,KAAQ,KAAK,QACpB,KAAK,aAAa,OAAO,SAASA,EAAM,KAAK,QAAQA,CAAI,CAAC,EAG9D,UAAWA,KAAQ,KAAK,cACpB,KAAK,aAAa,OAAO,SAASA,EAAM,KAAK,cAAcA,CAAI,CAAC,EAGpE,UAAWA,KAAQ,KAAK,SACpB,KAAK,aAAa,OAAO,UAAUA,EAAM,KAAK,SAASA,CAAI,CAAC,EAGhE,UAAWA,KAAQ,KAAK,SAAU,CAC9B,MAAMqB,EAAQ,KAAK,SAASrB,CAAI,EAChC,KAAK,aAAa,OAAO,UAAUA,EAAMqB,EAAM,EAAGA,EAAM,EAAGA,EAAM,EAAGA,EAAM,CAAC,CAC9E,CAED,UAAWrB,KAAQ,KAAK,UACpB,KAAK,aAAa,OAAO,WAAWA,EAAM,KAAK,UAAUA,CAAI,CAAC,EAGlE,UAAWA,KAAQ,KAAK,UACpB,KAAK,aAAa,OAAO,WAAWA,EAAM,KAAK,UAAUA,CAAI,CAAC,EAGlE,UAAWA,KAAQ,KAAK,UACpB,KAAK,aAAa,OAAO,WAAWA,EAAM,KAAK,UAAUA,CAAI,CAAC,EAGlE,UAAWA,KAAQ,KAAK,UACpB,KAAK,aAAa,OAAO,UAAUA,EAAM,KAAK,UAAUA,CAAI,CAAC,CAEpE,CACD,GAAI,CAAC,KAAK,UAAY,CAAC,KAAK,WACxB,QAEJe,EAAAhD,EAAO,kBAAP,MAAAgD,EAAA,KAAAhD,EAAyB,qCAAqC,KAAK,IAAI,GAAI,GAC3E,MAAMuD,EAAWvD,EAAO,gBACxB,GAAI,KAAK,OACL,QAASc,EAAO,EAAGA,EAAO,EAAGA,IACzBd,EAAO,gBAAgB,KAAK,WAAYc,EAAM,OAAW,OAAW,EAAI,EAExEd,EAAO,YAAY,KAAK,eAAgB,KAAK,aAAc,KAAK,aAAa,MAAM,EACnF,KAAK,aAAa,OAAO,SAAS,OAAQc,CAAI,EAE1C,KAAK,WACLd,EAAO,MAAM0B,EAAM,WAAY,GAAM,GAAO,EAAK,EAGrD1B,EAAO,iBAAiBwD,EAAS,iBAAkB,EAAG,CAAC,EAEvDxD,EAAO,kBAAkB,KAAK,WAAY,EAAI,MAGjD,CACD,IAAIyD,EAAY,EACZ,KAAK,WAAW,KAChBA,EAAY,KAAK,WAAW,MAEvB,KAAK,WAAW,YACrBA,EAAY,KAAK,WAAW,QAEhC,QAASC,EAAQ,EAAGA,EAAQD,EAAWC,IAAS,CAI5C,GAHA1D,EAAO,gBAAgB,KAAK,WAAY,EAAG,OAAW,OAAW,GAAM,EAAG0D,CAAK,EAE/E1D,EAAO,YAAY,KAAK,eAAgB,KAAK,aAAc,KAAK,aAAa,MAAM,EAC/E,KAAK,WAAW,MAAQ,KAAK,WAAW,UAAW,EACnD2D,EAAA,KAAK,aAAa,SAAlB,MAAAA,EAA0B,SAAS,QAASF,IAAc,EAAIC,GAASD,EAAY,GAAK,IACxFG,EAAA,KAAK,aAAa,SAAlB,MAAAA,EAA0B,OAAO,WAAYF,GAC7C,UAAWzB,KAAQ,KAAK,UACpB,KAAK,aAAa,OAAO,WAAWA,EAAM,KAAK,UAAUA,CAAI,CAAC,CAErE,CAEG,KAAK,WACLjC,EAAO,MAAM0B,EAAM,WAAY,GAAM,GAAO,EAAK,EAGrD1B,EAAO,iBAAiBwD,EAAS,iBAAkB,EAAG,CAAC,EAEvDxD,EAAO,kBAAkB,KAAK,WAAY,CAAC,KAAK,gBAAgB,CACnE,CACJ,CACGuD,GACAvD,EAAO,YAAYuD,CAAQ,EAG3B,KAAK,QACLvD,EAAO,0BAA0B,KAAK,SAAU,EAAI,GAExD6D,EAAA7D,EAAO,iBAAP,MAAA6D,EAAA,KAAA7D,EAAwB,GACpB,KAAK,aACL,KAAK,YAAW,EAEpB,KAAK,sBAAsB,gBAAgB,IAAI,CAClD,CAKD,OAAQ,CACJ,MAAM8D,EAAc,KAAK,UACnBC,EAAa,IAAIhC,EAAkB,KAAK,KAAM+B,EAAY,MAAO,KAAK,UAAW,KAAK,WAAY,KAAK,iBAAkB,KAAK,gBAAgB,EAEpJ,OAAAC,EAAW,SAAW,KAAK,SAC3BA,EAAW,MAAQ,KAAK,MAExBA,EAAW,gBAAkB,KAAK,gBAC3BA,CACV,CAID,SAAU,CACN,MAAMrC,EAAQ,KAAK,WACnB,GAAI,CAACA,EACD,OAEJ,MAAMsC,EAAQtC,EAAM,mBAAmB,QAAQ,IAAI,EAC/CsC,GAAS,GACTtC,EAAM,mBAAmB,OAAOsC,EAAO,CAAC,EAE5C,MAAMC,EAAe,KAAK,eAAetB,EAAa,YAAY,EAC9DsB,IACAA,EAAa,QAAO,EACpB,KAAK,eAAetB,EAAa,YAAY,EAAI,MAEjD,KAAK,cAAgB,KAAK,YAAY,eAAe,KAAK,YAAY,IACtE,KAAK,aAAe,MAExB,KAAK,sBAAsB,QAC3B,KAAK,6BAA6B,QAClC,MAAM,QAAO,CAChB,CACL,CACAuB,EAAW,CACPC,EAAW,CACf,EAAGpC,EAAkB,UAAW,YAAa,MAAM,EACnDmC,EAAW,CACPC,EAAW,CACf,EAAGpC,EAAkB,UAAW,YAAa,MAAM,EACnDmC,EAAW,CACPC,EAAW,CACf,EAAGpC,EAAkB,UAAW,mBAAoB,MAAM,EAC1DmC,EAAW,CACPC,EAAW,CACf,EAAGpC,EAAkB,UAAW,QAAS,MAAM,EAC/CmC,EAAW,CACPC,EAAW,CACf,EAAGpC,EAAkB,UAAW,cAAe,IAAI,EACnDqC,EAAc,4BAA6BrC,CAAiB,ECxoBrD,MAAMsC,CAAgB,CAIzB,IAAI,WAAY,CACZ,OAAO,KAAK,UACf,CAKD,IAAI,UAAUC,EAAQ,CACd,KAAK,aAAeA,IAGxB,KAAK,iBAAgB,EACrB,KAAK,WAAaA,EACbA,IAGDA,EAAO,OACHA,EAAO,uBACP,KAAK,0BAAyB,EAG9BA,EAAO,iBAAiB,QAAQ,KAAK,0BAA0B,KAAK,KAAMA,CAAM,CAAC,EAIjFA,EAAO,uBACP,KAAK,0BAAyB,EAG9BA,EAAO,iBAAiB,QAAQ,KAAK,0BAA0B,KAAK,KAAMA,CAAM,CAAC,GAG5F,CACD,2BAA4B,CACpB,KAAK,YACL,KAAK,WAAW,UAEpB,KAAK,gBAAe,EAChB,KAAK,YAEL,KAAK,iBAAgB,CAE5B,CAKD,gBAAiB,CACb,OAAO,KAAK,QAAU,KAAK,QAAU,KAAK,aAC7C,CAQD,sBAAsBC,EAAGC,EAAGC,EAAYC,EAAa,CACjD,KAAK,iBAAiB,IAAIH,EAAGC,EAAGC,EAAYC,CAAW,CAC1D,CAID,IAAI,eAAgB,CAChB,OAAO,KAAK,cACf,CAKD,gBAAiB,CACb,OAAK,KAAK,YACN,KAAK,iBAAgB,EAElB,KAAK,UACf,CAMD,YAAYC,EAAe,CAEvB,KAAK,aAAe,GACpB,KAAK,iBAAmB,IAAIC,EAAQ,EAAK,EAAK,EAAK,CAAG,EACtD,KAAK,eAAiB,YAItB,KAAK,sBAAwB,IAAItC,EAC7BqC,EACIN,EAAgB,SAASM,CAAa,EACtC,KAAK,OAASA,EAGd,KAAK,QAAUA,EAInB,KAAK,OAASpC,EAAY,iBAE1B,KAAK,SACL,KAAK,QAAU,KAAK,OAAO,UAAS,GAExC,MAAMsC,EAAc,IAAI,YAAY,CAAC,EAAG,EAAG,EAAG,GAAG,CAAC,EAClD,KAAK,cAAgB,IAAIC,EAAWD,EAAa,EAAG,EAAGE,EAAO,mBAAoBJ,EAAe,GAAO,GAAO,OAAW,CAAC,EACvH,KAAK,QACLN,EAAgB,8BAA8B,KAAK,MAAM,CAEhE,CACD,iBAAkB,CACd,MAAMnE,EAAO,KAAK,WAAa,CAAE,MAAO,KAAK,WAAW,QAAS,EAAC,MAAO,OAAQ,KAAK,WAAW,QAAS,EAAC,MAAQ,EAAG,CAAE,MAAO,EAAG,OAAQ,GACrI,KAAK,aACN,KAAK,WAAa4E,EAAW,eAAe,IAAI,WAAW,CAAC,GAAG,CAAC,EAAG,EAAG,EAAG,KAAK,QAAS,GAAO,GAAO,EAAG,CAAC,EACzG,KAAK,WAAW,KAAO,0BAEvB,KAAK,WAAW,SAChB5E,EAAK,OAAS,EACdA,EAAK,QAAU,EAIfA,EAAK,MAAQ,GAAK,KAAK,MAAM,KAAK,KAAKA,EAAK,KAAK,CAAC,EAClDA,EAAK,OAAS,GAAK,KAAK,MAAM,KAAK,KAAKA,EAAK,MAAM,CAAC,GAExD,MAAMmB,EAAW,KAAK,QAAQ,SAExB2D,EAAa,CACf,oBAAqB,GACrB,gBAAiB,GACjB,OAAQ,EACR,KAAM,EACN,aAAc,EACd,eAAgB3D,EAAW,EAA8B,EACzD,WAAY,GACZ,0BAA2B,SAAY,CAC/BA,EACA,MAAM,QAAQ,IAAI,OAAC,OAAO,gCAAoC,6CAAEE,EAAA,IAAC,OAAO,gCAAoC,6CAAEA,EAAA,IAAC,OAAO,2CAA+C,EAAC,6CAAC,EAGvK,MAAM,QAAQ,IAAI,OAAC,OAAO,gCAAgC,6CAAEA,EAAA,IAAC,OAAO,gCAAgC,+CAAEA,EAAA,IAAC,OAAO,2CAA2C,EAAC,6CAAC,CAElK,CACb,EACc0D,EAAc,CAChB,oBAAqB,GACrB,gBAAiB,GACjB,OAAQ,EACR,KAAM,EACN,aAAc,EACd,eAAgB5D,EAAW,EAA8B,EACzD,WAAY,GACZ,0BAA2B,SAAY,CAC/BA,EACA,MAAM,QAAQ,IAAI,CAACE,EAAA,WAAO,gCAAoC,EAAC,6CAAC,EAGhE,MAAM,QAAQ,IAAI,CAACA,EAAA,WAAO,gCAAgC,EAAC,6CAAC,CAEnE,CACb,EACQ,KAAK,QAAU,IAAIQ,EAAkB,cAAe,CAAE,MAAO7B,EAAK,MAAO,OAAQA,EAAK,OAAS,CAAG,EAAE,UAAW,KAAK,OAAQ8E,EAAY,GAAO,EAAK,EACpJ,KAAK,QAAQ,UAAY,GACzB,KAAK,QAAQ,WAAW,YAAa,KAAK,UAAU,EACpD,KAAK,QAAQ,OAAO,YAAa9E,EAAK,MAAM,EAC5C,KAAK,QAAQ,MAAQ,EACrB,KAAK,QAAQ,YAAc,EACvB,KAAK,WAAW,SAChB,KAAK,QAAQ,QAAU;AAAA,GAE3B,KAAK,QAAU,IAAI6B,EAAkB,cAAe,CAAE,MAAO7B,EAAK,MAAQ,EAAG,OAAQ,CAAC,EAAI,UAAW,KAAK,OAAQ8E,EAAY,GAAO,EAAK,EAC1I,KAAK,QAAQ,UAAY,GACzB,KAAK,QAAQ,WAAW,OAAQ,KAAK,OAAO,EAC5C,KAAK,QAAQ,YAAc,EAC3B,KAAK,QAAQ,MAAQ,EACrB,KAAK,mBAAqB,IAAIjD,EAAkB,qBAAsB,CAAE,MAAO7B,EAAK,MAAO,OAAQA,EAAK,MAAQ,EAAE,qBAAsB,KAAK,OAAQ,CAAE,GAAG8E,EAAY,aAAc,EAAG,gBAAiB,EAAI,EAAI,GAAM,EAAK,EAC3N,KAAK,mBAAmB,UAAY,GACpC,KAAK,mBAAmB,WAAW,YAAa,KAAK,UAAU,EAC/D,KAAK,mBAAmB,OAAO,YAAa9E,EAAK,MAAM,EACvD,KAAK,mBAAmB,OAAO,WAAYA,EAAK,KAAK,EACrD,KAAK,mBAAmB,YAAc,EAClC,KAAK,WAAW,SAChB,KAAK,mBAAmB,QAAU;AAAA,GAEtC,KAAK,QAAU,IAAI6B,EAAkB,cAAe,CAAE,MAAO7B,EAAK,MAAO,OAAQA,EAAK,MAAM,EAAI,UAAW,KAAK,OAAQ+E,EAAa,GAAO,EAAK,EACjJ,KAAK,QAAQ,UAAY,GACzB,KAAK,QAAQ,WAAW,OAAQ,KAAK,OAAO,EAC5C,KAAK,QAAQ,WAAW,OAAQ,KAAK,OAAO,EAC5C,KAAK,QAAQ,WAAW,YAAa,KAAK,UAAU,EACpD,KAAK,QAAQ,WAAW,yBAA0B,KAAK,kBAAkB,EACzE,KAAK,QAAQ,YAAc,EAC3B,KAAK,QAAQ,MAAQ,EACrB,KAAK,QAAQ,MAAQ,EACjB,KAAK,WAAW,SAChB,KAAK,QAAQ,QAAU;AAAA,GAG3B,KAAK,QAAQ,sBAAsB,QAAQ,IAAM,CAC7C,KAAK,sBAAsB,iBACvC,CAAS,CACJ,CACD,kBAAmB,cACfjC,EAAA,KAAK,UAAL,MAAAA,EAAc,WACdW,EAAA,KAAK,UAAL,MAAAA,EAAc,WACdC,EAAA,KAAK,UAAL,MAAAA,EAAc,WACdC,EAAA,KAAK,qBAAL,MAAAA,EAAyB,SAC5B,CACD,kBAAmB,WACX,KAAK,YACL,KAAK,WAAW,UAEpB,MAAMxC,EAAW,KAAK,QAAQ,SACxB6D,EAAe,CACjB,MAAO,KAAK,QAAQ,eAAgB,EACpC,OAAQ,KAAK,QAAQ,gBAAiB,EACtC,aAAclD,EAAQ,sBACtB,OAAQ,KAAK,QACb,YAAa,EACb,SAAU,CAAC,YAAY,EACvB,SAAU,CAAC,OAAQ,OAAQ,OAAQ,WAAW,EAC9C,SAASgB,EAAA,KAAK,aAAL,MAAAA,EAAiB,OAAS;AAAA,EAA+B,GAClE,eAAgB3B,EAAW,EAA8B,EACzD,qBAAsB,CAAC8D,EAAWC,IAAS,CACnCD,EACAC,EAAK,KAAK7D,EAAA,WAAO,oCAAwC,4CAAC,EAG1D6D,EAAK,KAAK7D,EAAA,WAAO,oCAAoC,4CAAC,CAE7D,CACb,EACQ,KAAK,WAAa,IAAI8D,EAAY,KAAK,eAAgB,cAAeH,CAAY,EAClF,MAAMI,EAAc,KAAK,WAAW,UAAS,EACzCA,IACAA,EAAY,SAAU3B,EAAA,KAAK,aAAL,MAAAA,EAAiB,OAAS;AAAA,EAA+B,KAE/EC,EAAA,KAAK,aAAL,MAAAA,EAAiB,QACjB,KAAK,WAAW,aAAa;AAAA,CAA4B,EAE7D,KAAK,WAAW,kBAAkB,IAAKnD,GAAW,CAC9CA,EAAO,WAAW,OAAQ,KAAK,OAAO,EACtCA,EAAO,WAAW,OAAQ,KAAK,OAAO,EACtCA,EAAO,WAAW,OAAQ,KAAK,OAAO,EACtCA,EAAO,WAAW,YAAa,KAAK,UAAU,EAC9CA,EAAO,UAAU,aAAc,KAAK,iBAAiB,EAAG,KAAK,iBAAiB,EAAG,KAAK,iBAAiB,EAAG,KAAK,iBAAiB,CAAC,CAC7I,CAAS,CACJ,CAKD,SAAU,CACN,OAAQ,KAAK,YACT,KAAK,WAAW,OAAS,0BACzB,KAAK,WAAW,QAAS,GACzB,KAAK,SACL,KAAK,QAAQ,QAAS,GACtB,KAAK,SACL,KAAK,QAAQ,QAAS,GACtB,KAAK,SACL,KAAK,QAAQ,QAAS,GACtB,KAAK,oBACL,KAAK,mBAAmB,SAC/B,CAKD,iBAAkB,CAEd,KAAK,QAAQ,sBAAsB,QAAQ,IAAM,CAC7C,KAAK,sBAAsB,iBACvC,CAAS,EACD,MAAM8E,EAAW,GACXC,EAAgB,CAAC,KAAK,QAAS,KAAK,QAAS,KAAK,mBAAoB,KAAK,OAAO,EACxF,OAAAA,EAAc,QAASC,GAAW,CAC9BF,EAAS,KAAK,IAAI,QAASG,GAAY,CAC/BD,EAAO,UACPC,IAGAD,EAAO,YAAY,oBAAoB,IAAM,CACzCC,GACxB,CAAqB,CAER,EAAC,CACd,CAAS,EACM,QAAQ,IAAIH,CAAQ,EAAE,KAAK,IAAM,CACpCC,EAAc,QAASC,GAAW,CAC9BA,EAAO,OAAM,CAC7B,CAAa,CACb,CAAS,CACJ,CAID,SAAU,CACN,KAAK,iBAAgB,EACrB,KAAK,cAAc,UACf,KAAK,YACL,KAAK,WAAW,UAEpB,KAAK,sBAAsB,OAC9B,CACD,OAAO,SAASd,EAAe,CAC3B,OAAOA,EAAc,aAAc,IAAK,OAC3C,CACL,CAIAN,EAAgB,8BAAiCsB,GAAM,CACnD,MAAMC,EAAY,6CAA6C,CACnE,ECjUO,MAAMC,CAAuB,CAOhC,YAAY7F,EAAQC,EAAU,GAAI,CAK9B,KAAK,QAAU,KAIf,KAAK,SAAW,EAIhB,KAAK,OAAS,GAEd,KAAK,QAAUD,EACf,KAAK,SAAWC,EAAQ,UAAY,KAAK,SACzC,KAAK,QAAUA,EAAQ,SAAW,KAAK,QACvC,KAAK,OAASA,EAAQ,QAAU,KAAK,MACxC,CACD,oBAAoBC,EAAM,CACtB,IAAIC,EAAc,EACd,KAAK,QAAQ,QAAO,EAAG,uBACvBA,EAAc,EAET,KAAK,QAAQ,QAAO,EAAG,qBAC5BA,EAAc,GAElB,MAAMC,EAAY,KAAK,QAAQ,8BAA8BF,EAAM,CAC/D,OAAQ,EACR,KAAMC,EACN,cAAe,GACf,gBAAiB,GACjB,oBAAqB,GACrB,sBAAuB,GACvB,aAAc,EACd,MAAO,iCACnB,CAAS,EACD,YAAK,QAAQ,0BAA0BC,EAAU,QAAS,EAAG,EAAG,CAAC,EAC1DA,CACV,CACD,mBAAmBC,EAAS,CACxB,MAAMC,EAAQD,EAAQ,QAAO,EAAG,MAC1BE,EAAeC,EAAMF,CAAK,EAC1BG,EAAS,KAAK,eAAe,OAG7BqF,EAAiB,KAAK,IAAI,GAAI,GAAKtF,EAAMF,GAAS,CAAC,CAAC,EACpDI,EAAgB,KAAK,oBAAoBoF,CAAc,EAC7D,KAAK,gBAAgB,aACrB,KAAK,gBAAgB,cACrB,KAAK,gBAAgB,mBAAmB,KAAK,cAAc,EAC3D,MAAMlF,EAAa,CACf,CAAC,IAAIC,EAAQ,EAAG,EAAG,EAAE,EAAG,IAAIA,EAAQ,EAAG,GAAI,CAAC,EAAG,IAAIA,EAAQ,EAAG,EAAG,CAAC,CAAC,EACnE,CAAC,IAAIA,EAAQ,EAAG,EAAG,CAAC,EAAG,IAAIA,EAAQ,EAAG,GAAI,CAAC,EAAG,IAAIA,EAAQ,GAAI,EAAG,CAAC,CAAC,EACnE,CAAC,IAAIA,EAAQ,EAAG,EAAG,CAAC,EAAG,IAAIA,EAAQ,EAAG,EAAG,CAAC,EAAG,IAAIA,EAAQ,EAAG,EAAG,CAAC,CAAC,EACjE,CAAC,IAAIA,EAAQ,EAAG,EAAG,CAAC,EAAG,IAAIA,EAAQ,EAAG,EAAG,EAAE,EAAG,IAAIA,EAAQ,EAAG,GAAI,CAAC,CAAC,EACnE,CAAC,IAAIA,EAAQ,EAAG,EAAG,CAAC,EAAG,IAAIA,EAAQ,EAAG,GAAI,CAAC,EAAG,IAAIA,EAAQ,EAAG,EAAG,CAAC,CAAC,EAClE,CAAC,IAAIA,EAAQ,GAAI,EAAG,CAAC,EAAG,IAAIA,EAAQ,EAAG,GAAI,CAAC,EAAG,IAAIA,EAAQ,EAAG,EAAG,EAAE,CAAC,CAChF,EACQJ,EAAO,SAAS,WAAY,KAAK,QAAQ,EACzCA,EAAO,UAAU,iBAAkBJ,EAAQ,UAAU,MAAOE,CAAY,EACxEE,EAAO,WAAW,eAAgBJ,CAAO,EACrC,KAAK,eACLI,EAAO,WAAW,cAAe,KAAK,cAAc,eAAc,CAAE,EAExE,QAASK,EAAO,EAAGA,EAAO,EAAGA,IACzBL,EAAO,WAAW,KAAMG,EAAWE,CAAI,EAAE,CAAC,CAAC,EAC3CL,EAAO,WAAW,QAASG,EAAWE,CAAI,EAAE,CAAC,CAAC,EAC9CL,EAAO,WAAW,QAASG,EAAWE,CAAI,EAAE,CAAC,CAAC,EAC9C,KAAK,QAAQ,gBAAgBJ,EAAeI,EAAM,OAAW,OAAW,EAAI,EAC5E,KAAK,gBAAgB,mBAAmB,KAAK,cAAc,EAC3D,KAAK,gBAAgB,OAGzB,KAAK,gBAAgB,gBACrB,KAAK,QAAQ,4BACbL,EAAO,WAAW,eAAgB,IAAI,EACtCA,EAAO,WAAW,cAAe,IAAI,EACrC,MAAMsF,EAAoB,IAAIC,EAAY3F,EAAQ,WAAYK,EAAc,OAAO,EACnF,OAAAqF,EAAkB,KAAO1F,EAAQ,KAAO,cACxC0F,EAAkB,YAAc1F,EAAQ,KAAO,cAC/C0F,EAAkB,WAAa,GACxBA,CACV,CACD,cAAc1F,EAASc,EAAY,CAC/B,MAAMC,EAAU,GACZf,EAAQ,YACRe,EAAQ,KAAK,qBAAqB,EAEtCA,EAAQ,KAAK,uBAAyB,KAAK,QAAU,GAAG,EACxD,MAAMC,EAAW,KAAK,QAAQ,SACxB4E,EAAW,CAAC,cAAc,EAChC,OAAI,KAAK,gBACLA,EAAS,KAAK,aAAa,EAC3B7E,EAAQ,KAAK,2BAA2B,GAEtB,IAAIE,EAAc,CACpC,OAAQ,KAAK,QACb,KAAM,yBACN,aAAc,yBACd,eAAgB,yBAChB,aAAc2E,EACd,aAAc,CAAC,oBAAqB,WAAY,KAAM,QAAS,QAAS,iBAAkB,UAAU,EACpG,eAAgB,GAChB,QAAA7E,EACA,WAAYD,EACZ,eAAgBE,EAAW,EAA8B,EACzD,0BAA2B,SAAY,CAC/BA,EACA,MAAM,QAAQ,IAAI,CAAAE,EAAA,IAAC,OAAO,6CAAuD,6CAAGA,EAAA,WAAO,+CAAyD,EAAC,+CAAC,EAGtJ,MAAM,QAAQ,IAAI,CAAAA,EAAA,IAAC,OAAO,6CAAmD,6CAAGA,EAAA,WAAO,+CAAqD,EAAC,+CAAC,CAErJ,CACb,CAAS,CAEJ,CAMD,QAAQlB,EAAS,CACb,OAAOA,EAAQ,WAAa,KAAK,eAAe,OAAO,SAC1D,CASD,MAAM,UAAUA,EAAS,OACrB,GAAI,CAAC,KAAK,QAAQ,UAAU,yBACxB,MAAM,IAAI,MAAM,yFAAyF,EAEzG,KAAK,SACL,KAAK,cAAgB,IAAIgE,EAAgB,KAAK,OAAO,EACrD,KAAK,cAAc,UAAYhE,EAC/B,MAAM,KAAK,cAAc,mBAE7B,KAAK,gBAAkB,IAAImB,EAAe,KAAK,OAAO,EACtD,KAAK,eAAiB,KAAK,cAAcnB,CAAO,EAChD,MAAM,KAAK,eAAe,OAAO,kBAAiB,EAClD,MAAM0F,EAAoB,KAAK,mBAAmB1F,CAAO,EACzD,YAAK,gBAAgB,UACrB,KAAK,eAAe,WACpB2C,EAAA,KAAK,gBAAL,MAAAA,EAAoB,UACb+C,CACV,CACL,CCrJO,MAAMG,UAAuBF,CAAY,CAI5C,IAAI,WAAW7C,EAAO,CAClB,KAAK,YAAcA,CACtB,CAID,IAAI,YAAa,CACb,OAAO,KAAK,WACf,CAID,IAAI,UAAUA,EAAO,CACjB,KAAK,WAAaA,EAClB,KAAK,2BAA2BgD,EAAO,UAAU,KAAK,UAAU,CAAC,CACpE,CAID,IAAI,WAAY,CACZ,OAAO,KAAK,UACf,CAOD,IAAI,gBAAgBhD,EAAO,CACvB,GAAI,KAAK,kBAAoB,KAAK,iBAAiB,OAAOA,CAAK,EAC3D,OAEJ,KAAK,iBAAmBA,EACxB,MAAMzB,EAAQ,KAAK,WACfA,GACAA,EAAM,wBAAwB,CAAC,CAEtC,CACD,IAAI,iBAAkB,CAClB,OAAO,KAAK,gBACf,CAiBD,YAAY0E,EAAKzB,EAAezE,EAAMmG,EAAW,GAAOC,EAAoB,GAAMC,EAAa,GAAOC,EAAkB,GAAOC,EAAS,KAAMC,EAAU,KAAMC,EAAc,GAAOC,EAA4B,GAAOC,EAAoB,GAAO,OAC7O,MAAMlC,CAAa,EACnB,KAAK,mBAAqB,GAC1B,KAAK,SAAW,KAChB,KAAK,YAAc,GACnB,KAAK,WAAa,EAKlB,KAAK,oBAAsB9D,EAAQ,OAInC,KAAK,iBAAmB,IAAIyB,EACvB8D,IAGL,KAAK,iBAAmBpE,EAAQ,WAChC,KAAK,KAAOoE,EACZ,KAAK,IAAMA,EACX,KAAK,SAAW,GAChB,KAAK,OAAS,GACd,KAAK,eAAiBD,EAAO,WAC7B,KAAK,iBAAmBK,EACxB,KAAK,2BAA6BI,EAClC,KAAK,mBAAqBC,EAC1B,KAAK,QAAU,IAAM,CACjB,KAAK,iBAAiB,gBAAgB,IAAI,EACtCJ,GACAA,GAEhB,EACQ,KAAK,SAAWC,EAChB,KAAK,WAAaH,EAClB,KAAK,UAAYF,EACjB,KAAK,MAAQnG,EAIb,KAAK,aAAeyG,GAAeE,EACnC,KAAK,mBAAqBP,EAC1B,KAAK,SAAW,KAAK,cAAcF,EAAK,KAAK,UAAW,OAAW,OAAW,OAAW,KAAK,MAAM,EAC/F,KAAK,SASF,KAAK,SAAS,QACdxE,EAAM,aAAa,IAAM,KAAK,QAAS,GAGvC,KAAK,SAAS,mBAAmB,IAAI,KAAK,OAAO,GAZhDoB,EAAA,KAAK,SAAU,IAAf,MAAAA,EAAiB,yBAIlB,KAAK,eAAiB,EAHtB,KAAK,aAAY,EAc5B,CAKD,cAAe,CACX,MAAO,gBACV,CAID,cAAe,CACX,MAAMhD,EAAS,KAAK,aACd8G,EAAO9G,EAAO,UACpB,IAAIG,EAAc,EACd2G,EAAK,cAAgBA,EAAK,4BAC1B3G,EAAc,EAET2G,EAAK,kBAAoBA,EAAK,kCACnC3G,EAAc,GAElB,MAAM4G,EAAYnE,GAAW,CACzB,KAAK,oBAAsB,EAC3B,KAAK,mBAAqB,GAE1B,MAAMoE,EAAOC,EAAsBrE,EAAQ,KAAK,MAAO,KAAK,YAAY,EAExE,GAAI,KAAK,mBAAoB,CACzB,MAAMsE,EAAsBC,EAAkC,oCAAoCH,CAAI,EACtG,KAAK,oBAAsBE,CAC9B,CACD,MAAME,EAAU,GAChB,IAAIC,EAAY,KACZC,EAAa,KAEjB,QAASC,EAAI,EAAGA,EAAI,EAAGA,IAAK,CAEpBpH,IAAgB,EAChBmH,EAAa,IAAI,YAAY,KAAK,MAAQ,KAAK,MAAQ,CAAC,EAEnDnH,IAAgB,IAErBkH,EAAY,IAAI,WAAW,KAAK,MAAQ,KAAK,MAAQ,CAAC,GAE1D,MAAMG,EAAWR,EAAKd,EAAe,cAAcqB,CAAC,CAAC,EAErD,GAAI,KAAK,YAAcD,GAAcD,GACjC,QAASI,EAAI,EAAGA,EAAI,KAAK,MAAQ,KAAK,MAAOA,IAczC,GAZI,KAAK,aACLD,EAASC,EAAI,EAAI,CAAC,EAAI,KAAK,IAAID,EAASC,EAAI,EAAI,CAAC,EAAGC,CAAY,EAChEF,EAASC,EAAI,EAAI,CAAC,EAAI,KAAK,IAAID,EAASC,EAAI,EAAI,CAAC,EAAGC,CAAY,EAChEF,EAASC,EAAI,EAAI,CAAC,EAAI,KAAK,IAAID,EAASC,EAAI,EAAI,CAAC,EAAGC,CAAY,GAGhEJ,IACAA,EAAWG,EAAI,EAAI,CAAC,EAAIE,EAAYH,EAASC,EAAI,EAAI,CAAC,CAAC,EACvDH,EAAWG,EAAI,EAAI,CAAC,EAAIE,EAAYH,EAASC,EAAI,EAAI,CAAC,CAAC,EACvDH,EAAWG,EAAI,EAAI,CAAC,EAAIE,EAAYH,EAASC,EAAI,EAAI,CAAC,CAAC,GAGvDJ,EAAW,CACX,IAAIO,EAAI,KAAK,IAAIJ,EAASC,EAAI,EAAI,CAAC,EAAI,IAAK,CAAC,EACzCI,EAAI,KAAK,IAAIL,EAASC,EAAI,EAAI,CAAC,EAAI,IAAK,CAAC,EACzCK,EAAI,KAAK,IAAIN,EAASC,EAAI,EAAI,CAAC,EAAI,IAAK,CAAC,EAE7C,MAAMM,EAAM,KAAK,IAAI,KAAK,IAAIH,EAAGC,CAAC,EAAGC,CAAC,EACtC,GAAIC,EAAM,IAAK,CACX,MAAMC,EAAQ,IAAMD,EACpBH,GAAKI,EACLH,GAAKG,EACLF,GAAKE,CACR,CACDX,EAAUI,EAAI,EAAI,CAAC,EAAIG,EACvBP,EAAUI,EAAI,EAAI,CAAC,EAAII,EACvBR,EAAUI,EAAI,EAAI,CAAC,EAAIK,CAC1B,EAGLR,EACAF,EAAQ,KAAKE,CAAU,EAElBD,EACLD,EAAQ,KAAKC,CAAS,EAGtBD,EAAQ,KAAKI,CAAQ,CAE5B,CACD,OAAOJ,CACnB,EACQ,GAAIpH,EAAO,UAAU,2BAA6B,KAAK,kBAAoB,KAAK,4BAA6B,CACzG,MAAMiI,EAAiB,KAAK,QACtBC,EAAe,IAAInI,EAAaC,CAAM,EAC5C,KAAK,QAAU,IAAM,CACjB,IAAImI,EAAoB,QAAQ,QAAQ,IAAI,EACxCC,EAAkB,QAAQ,UAC1B,KAAK,6BAELD,EAD+B,IAAItC,EAAuB7F,EAAQ,CAAE,OAAQ,KAAK,kBAAkB,CAAE,EAC1D,UAAU,IAAI,GAEzD,KAAK,mBACLoI,EAAkBF,EAAa,UAAU,IAAI,GAEjD,QAAQ,IAAI,CAACC,EAAmBC,CAAe,CAAC,EAAE,KAAMhB,GAAY,CAChE,MAAMrB,EAAoBqB,EAAQ,CAAC,EACnC,GAAI,KAAK,4BAA8BrB,EAAmB,CACtD,KAAK,kBAAoBA,EACzB,MAAMrE,EAAQ,KAAK,WACfA,GACAA,EAAM,wBAAwB,CAAC,CAEtC,CACGuG,GACAA,GAExB,CAAiB,CACjB,CACS,CACD,KAAK,SAAWjI,EAAO,4BAA4B,KAAK,IAAK,KAAK,SAAU,EAAE,KAAK,MAAO,EAAGG,EAAa,KAAK,UAAW4G,EAAU,KAAM,KAAK,QAAS,KAAK,QAAQ,CACxK,CACD,OAAQ,CACJ,MAAMhD,EAAa,IAAImC,EAAe,KAAK,IAAK,KAAK,SAAU,GAAI,KAAK,WAAY,EAAE,KAAK,MAAO,KAAK,UAAW,KAAK,mBAAoB,KAAK,UAAU,EAE1J,OAAAnC,EAAW,MAAQ,KAAK,MACxBA,EAAW,MAAQ,KAAK,MACxBA,EAAW,MAAQ,KAAK,MACxBA,EAAW,iBAAmB,KAAK,iBACnCA,EAAW,gBAAkB,KAAK,gBAC3BA,CACV,CAED,WAAY,CACJ,KAAK,iBAAmB,IAG5B,KAAK,eAAiB,EACtB,KAAK,SAAW,KAAK,cAAc,KAAK,IAAK,KAAK,SAAS,EACtD,KAAK,UACN,KAAK,aAAY,EAExB,CAKD,4BAA6B,CACzB,OAAO,KAAK,cACf,CAKD,2BAA2BZ,EAAO,OAC9B,KAAK,eAAiBA,EAClBA,EAAM,aAAe,KAAK,eAAe,YAGzCA,EAAM,WAAY,IAAK,KAAK,eAAe,WAAU,KACrDH,EAAA,KAAK,SAAU,IAAf,MAAAA,EAAiB,wBAAwB,EAAIqF,GAAQA,EAAI,kBAAmB,EAAC,QAAQ,IAAI,IAAM,IAEtG,CAID,SAAU,CACN,KAAK,iBAAiB,QACtB,MAAM,QAAO,CAChB,CAQD,OAAO,MAAMC,EAAe5G,EAAO6G,EAAS,CACxC,IAAIlI,EAAU,KACd,OAAIiI,EAAc,MAAQ,CAACA,EAAc,iBACrCjI,EAAU,IAAI6F,EAAeqC,EAAUD,EAAc,KAAM5G,EAAO4G,EAAc,KAAMA,EAAc,SAAUA,EAAc,kBAAmBA,EAAc,eAAe,EAC5KjI,EAAQ,KAAOiI,EAAc,KAC7BjI,EAAQ,SAAWiI,EAAc,SACjCjI,EAAQ,MAAQiI,EAAc,MAC9BjI,EAAQ,gBAAkBiI,EAAc,gBACxCjI,EAAQ,WAAaiI,EAAc,YAEnCjI,IACIiI,EAAc,sBACdjI,EAAQ,oBAAsBQ,EAAQ,UAAUyH,EAAc,mBAAmB,GAEjFA,EAAc,kBACdjI,EAAQ,gBAAkBQ,EAAQ,UAAUyH,EAAc,eAAe,GAEzEA,EAAc,YACdjI,EAAQ,UAAYiI,EAAc,YAGnCjI,CACV,CACD,WAAY,CACR,GAAI,CAAC,KAAK,KACN,OAAO,KAEX,MAAMmI,EAAsB,GAC5B,OAAAA,EAAoB,KAAO,KAAK,KAChCA,EAAoB,SAAW,KAAK,SACpCA,EAAoB,OAAS,GAC7BA,EAAoB,MAAQ,KAAK,MACjCA,EAAoB,KAAO,KAAK,MAChCA,EAAoB,gBAAkB,KAAK,gBAC3CA,EAAoB,gBAAkB,KAAK,WAC3CA,EAAoB,kBAAoB,KAAK,mBAC7CA,EAAoB,WAAa,yBACjCA,EAAoB,SAAW,KAAK,UACpCA,EAAoB,WAAa,KAAK,YACtCA,EAAoB,UAAY,KAAK,WAC9BA,CACV,CACL,CACAtC,EAAe,cAAgB,CAAC,QAAS,OAAQ,KAAM,OAAQ,QAAS,MAAM,EAC9E9B,EAAc,yBAA0B8B,CAAc", "names": ["HDRFiltering", "engine", "options", "size", "textureType", "rtWrapper", "texture", "width", "mipmapsCount", "ILog2", "effect", "outputTexture", "intTexture", "directions", "Vector3", "face", "lod", "alpha", "type", "format", "onCompiled", "defines", "isWebGPU", "EffectWrapper", "__vitePreload", "<PERSON><PERSON><PERSON><PERSON>", "ProceduralTextureSceneComponent", "scene", "SceneComponentConstants", "Tools", "proceduralIndex", "proceduralTexture", "ProceduralTexture", "Texture", "name", "fragment", "fallbackTexture", "generateMipMaps", "isCube", "Observable", "EngineStore", "component", "DrawWrapper", "vertices", "VertexBuffer", "buffer", "indices", "vb", "RenderTargetTexture", "_a", "func", "shaders", "value", "uniformName", "useCameraPostProcess", "color", "viewPort", "Material", "numLayers", "layer", "_b", "_c", "_d", "textureSize", "newTexture", "index", "vertexBuffer", "__decorate", "serialize", "RegisterClass", "IblCdfGenerator", "source", "x", "y", "widthScale", "heightScale", "scene<PERSON><PERSON><PERSON><PERSON><PERSON>", "Vector4", "blackPixels", "RawTexture", "Engine", "cdfOptions", "icdfOptions", "debugOptions", "useWebGPU", "list", "PostProcess", "debugEffect", "promises", "renderTargets", "target", "resolve", "_", "_WarnImport", "HDRIrradianceFiltering", "irradianceSize", "irradianceTexture", "BaseTexture", "samplers", "HDRCubeTexture", "Matrix", "url", "noMipmap", "generateHarmonics", "gammaSpace", "prefilterOnLoad", "onLoad", "onError", "supersample", "prefilterIrradianceOnLoad", "prefilterUsingCdf", "caps", "callback", "data", "GetCubeMapTextureData", "sphericalPolynomial", "CubeMapToSphericalPolynomialTools", "results", "byteArray", "shortArray", "j", "dataFace", "i", "ToGammaSpace", "ToHalfFloat", "r", "g", "b", "max", "scale", "previousOnLoad", "hdrFiltering", "irradiancePromise", "radiancePromise", "mat", "parsedTexture", "rootUrl", "serializationObject"], "ignoreList": [0, 1, 2, 3, 4, 5], "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/Filtering/hdrFiltering.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/Procedurals/proceduralTextureSceneComponent.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/Procedurals/proceduralTexture.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Rendering/iblCdfGenerator.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/Filtering/hdrIrradianceFiltering.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/hdrCubeTexture.js"], "sourcesContent": ["import { Vector3 } from \"../../../Maths/math.js\";\nimport { ILog2 } from \"../../../Maths/math.scalar.functions.js\";\n\nimport { EffectWrapper, EffectRenderer } from \"../../../Materials/effectRenderer.js\";\n/**\n * Filters HDR maps to get correct renderings of PBR reflections\n */\nexport class HDRFiltering {\n    /**\n     * Instantiates HDR filter for reflection maps\n     *\n     * @param engine Thin engine\n     * @param options Options\n     */\n    constructor(engine, options = {}) {\n        this._lodGenerationOffset = 0;\n        this._lodGenerationScale = 0.8;\n        /**\n         * Quality switch for prefiltering. Should be set to `4096` unless\n         * you care about baking speed.\n         */\n        this.quality = 4096;\n        /**\n         * Scales pixel intensity for the input HDR map.\n         */\n        this.hdrScale = 1;\n        // pass\n        this._engine = engine;\n        this.hdrScale = options.hdrScale || this.hdrScale;\n        this.quality = options.quality || this.quality;\n    }\n    _createRenderTarget(size) {\n        let textureType = 0;\n        if (this._engine.getCaps().textureHalfFloatRender) {\n            textureType = 2;\n        }\n        else if (this._engine.getCaps().textureFloatRender) {\n            textureType = 1;\n        }\n        const rtWrapper = this._engine.createRenderTargetCubeTexture(size, {\n            format: 5,\n            type: textureType,\n            createMipMaps: true,\n            generateMipMaps: false,\n            generateDepthBuffer: false,\n            generateStencilBuffer: false,\n            samplingMode: 1,\n            label: \"HDR_Radiance_Filtering_Target\",\n        });\n        this._engine.updateTextureWrappingMode(rtWrapper.texture, 0, 0, 0);\n        this._engine.updateTextureSamplingMode(3, rtWrapper.texture, true);\n        return rtWrapper;\n    }\n    _prefilterInternal(texture) {\n        const width = texture.getSize().width;\n        const mipmapsCount = ILog2(width) + 1;\n        const effect = this._effectWrapper.effect;\n        const outputTexture = this._createRenderTarget(width);\n        this._effectRenderer.saveStates();\n        this._effectRenderer.setViewport();\n        const intTexture = texture.getInternalTexture();\n        if (intTexture) {\n            // Just in case generate fresh clean mips.\n            this._engine.updateTextureSamplingMode(3, intTexture, true);\n        }\n        this._effectRenderer.applyEffectWrapper(this._effectWrapper);\n        const directions = [\n            [new Vector3(0, 0, -1), new Vector3(0, -1, 0), new Vector3(1, 0, 0)], // PositiveX\n            [new Vector3(0, 0, 1), new Vector3(0, -1, 0), new Vector3(-1, 0, 0)], // NegativeX\n            [new Vector3(1, 0, 0), new Vector3(0, 0, 1), new Vector3(0, 1, 0)], // PositiveY\n            [new Vector3(1, 0, 0), new Vector3(0, 0, -1), new Vector3(0, -1, 0)], // NegativeY\n            [new Vector3(1, 0, 0), new Vector3(0, -1, 0), new Vector3(0, 0, 1)], // PositiveZ\n            [new Vector3(-1, 0, 0), new Vector3(0, -1, 0), new Vector3(0, 0, -1)], // NegativeZ\n        ];\n        effect.setFloat(\"hdrScale\", this.hdrScale);\n        effect.setFloat2(\"vFilteringInfo\", texture.getSize().width, mipmapsCount);\n        effect.setTexture(\"inputTexture\", texture);\n        for (let face = 0; face < 6; face++) {\n            effect.setVector3(\"up\", directions[face][0]);\n            effect.setVector3(\"right\", directions[face][1]);\n            effect.setVector3(\"front\", directions[face][2]);\n            for (let lod = 0; lod < mipmapsCount; lod++) {\n                this._engine.bindFramebuffer(outputTexture, face, undefined, undefined, true, lod);\n                this._effectRenderer.applyEffectWrapper(this._effectWrapper);\n                let alpha = Math.pow(2, (lod - this._lodGenerationOffset) / this._lodGenerationScale) / width;\n                if (lod === 0) {\n                    alpha = 0;\n                }\n                effect.setFloat(\"alphaG\", alpha);\n                this._effectRenderer.draw();\n            }\n        }\n        // Cleanup\n        this._effectRenderer.restoreStates();\n        this._engine.restoreDefaultFramebuffer();\n        this._engine._releaseTexture(texture._texture);\n        // Internal Swap\n        const type = outputTexture.texture.type;\n        const format = outputTexture.texture.format;\n        outputTexture._swapAndDie(texture._texture);\n        texture._texture.type = type;\n        texture._texture.format = format;\n        // New settings\n        texture.gammaSpace = false;\n        texture.lodGenerationOffset = this._lodGenerationOffset;\n        texture.lodGenerationScale = this._lodGenerationScale;\n        texture._prefiltered = true;\n        return texture;\n    }\n    _createEffect(texture, onCompiled) {\n        const defines = [];\n        if (texture.gammaSpace) {\n            defines.push(\"#define GAMMA_INPUT\");\n        }\n        defines.push(\"#define NUM_SAMPLES \" + this.quality + \"u\"); // unsigned int\n        const isWebGPU = this._engine.isWebGPU;\n        const effectWrapper = new EffectWrapper({\n            engine: this._engine,\n            name: \"hdrFiltering\",\n            vertexShader: \"hdrFiltering\",\n            fragmentShader: \"hdrFiltering\",\n            samplerNames: [\"inputTexture\"],\n            uniformNames: [\"vSampleDirections\", \"vWeights\", \"up\", \"right\", \"front\", \"vFilteringInfo\", \"hdrScale\", \"alphaG\"],\n            useShaderStore: true,\n            defines,\n            onCompiled: onCompiled,\n            shaderLanguage: isWebGPU ? 1 /* ShaderLanguage.WGSL */ : 0 /* ShaderLanguage.GLSL */,\n            extraInitializationsAsync: async () => {\n                if (isWebGPU) {\n                    await Promise.all([import(\"../../../ShadersWGSL/hdrFiltering.vertex.js\"), import(\"../../../ShadersWGSL/hdrFiltering.fragment.js\")]);\n                }\n                else {\n                    await Promise.all([import(\"../../../Shaders/hdrFiltering.vertex.js\"), import(\"../../../Shaders/hdrFiltering.fragment.js\")]);\n                }\n            },\n        });\n        return effectWrapper;\n    }\n    /**\n     * Get a value indicating if the filter is ready to be used\n     * @param texture Texture to filter\n     * @returns true if the filter is ready\n     */\n    isReady(texture) {\n        return texture.isReady() && this._effectWrapper.effect.isReady();\n    }\n    /**\n     * Prefilters a cube texture to have mipmap levels representing roughness values.\n     * Prefiltering will be invoked at the end of next rendering pass.\n     * This has to be done once the map is loaded, and has not been prefiltered by a third party software.\n     * See http://blog.selfshadow.com/publications/s2013-shading-course/karis/s2013_pbs_epic_notes_v2.pdf for more information\n     * @param texture Texture to filter\n     * @returns Promise called when prefiltering is done\n     */\n    async prefilter(texture) {\n        if (!this._engine._features.allowTexturePrefiltering) {\n            throw new Error(\"HDR prefiltering is not available in WebGL 1., you can use real time filtering instead.\");\n        }\n        this._effectRenderer = new EffectRenderer(this._engine);\n        this._effectWrapper = this._createEffect(texture);\n        await this._effectWrapper.effect.whenCompiledAsync();\n        this._prefilterInternal(texture);\n        this._effectRenderer.dispose();\n        this._effectWrapper.dispose();\n    }\n}\n//# sourceMappingURL=hdrFiltering.js.map", "import { Tools } from \"../../../Misc/tools.js\";\nimport { SceneComponentConstants } from \"../../../sceneComponent.js\";\n/**\n * Defines the Procedural Texture scene component responsible to manage any Procedural Texture\n * in a given scene.\n */\nexport class ProceduralTextureSceneComponent {\n    /**\n     * Creates a new instance of the component for the given scene\n     * @param scene Defines the scene to register the component in\n     */\n    constructor(scene) {\n        /**\n         * The component name helpful to identify the component in the list of scene components.\n         */\n        this.name = SceneComponentConstants.NAME_PROCEDURALTEXTURE;\n        this.scene = scene;\n    }\n    /**\n     * Registers the component in a given scene\n     */\n    register() {\n        this.scene._beforeClearStage.registerStep(SceneComponentConstants.STEP_BEFORECLEAR_PROCEDURALTEXTURE, this, this._beforeClear);\n    }\n    /**\n     * Rebuilds the elements related to this component in case of\n     * context lost for instance.\n     */\n    rebuild() {\n        // Nothing to do here.\n    }\n    /**\n     * Disposes the component and the associated resources.\n     */\n    dispose() {\n        // Nothing to do here.\n    }\n    _beforeClear() {\n        if (this.scene.proceduralTexturesEnabled) {\n            Tools.StartPerformanceCounter(\"Procedural textures\", this.scene.proceduralTextures.length > 0);\n            for (let proceduralIndex = 0; proceduralIndex < this.scene.proceduralTextures.length; proceduralIndex++) {\n                const proceduralTexture = this.scene.proceduralTextures[proceduralIndex];\n                if (proceduralTexture._shouldRender()) {\n                    proceduralTexture.render();\n                }\n            }\n            Tools.EndPerformanceCounter(\"Procedural textures\", this.scene.proceduralTextures.length > 0);\n        }\n    }\n}\n//# sourceMappingURL=proceduralTextureSceneComponent.js.map", "import { __decorate } from \"../../../tslib.es6.js\";\nimport { serialize } from \"../../../Misc/decorators.js\";\nimport { Observable } from \"../../../Misc/observable.js\";\nimport { VertexBuffer } from \"../../../Buffers/buffer.js\";\nimport { SceneComponentConstants } from \"../../../sceneComponent.js\";\nimport { Material } from \"../../../Materials/material.js\";\nimport { Texture } from \"../../../Materials/Textures/texture.js\";\nimport { RenderTargetTexture } from \"../../../Materials/Textures/renderTargetTexture.js\";\nimport { ProceduralTextureSceneComponent } from \"./proceduralTextureSceneComponent.js\";\nimport { RegisterClass } from \"../../../Misc/typeStore.js\";\nimport { EngineStore } from \"../../../Engines/engineStore.js\";\n\nimport { DrawWrapper } from \"../../drawWrapper.js\";\n/**\n * Procedural texturing is a way to programmatically create a texture. There are 2 types of procedural textures: code-only, and code that references some classic 2D images, sometimes calmpler' images.\n * This is the base class of any Procedural texture and contains most of the shareable code.\n * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/proceduralTextures\n */\nexport class ProceduralTexture extends Texture {\n    /**\n     * Gets the shader language type used to generate vertex and fragment source code.\n     */\n    get shaderLanguage() {\n        return this._shaderLanguage;\n    }\n    /**\n     * Instantiates a new procedural texture.\n     * Procedural texturing is a way to programmatically create a texture. There are 2 types of procedural textures: code-only, and code that references some classic 2D images, sometimes called 'refMaps' or 'sampler' images.\n     * This is the base class of any Procedural texture and contains most of the shareable code.\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/proceduralTextures\n     * @param name  Define the name of the texture\n     * @param size Define the size of the texture to create\n     * @param fragment Define the fragment shader to use to generate the texture or null if it is defined later:\n     *  * object: \\{ fragmentElement: \"fragmentShaderCode\" \\}, used with shader code in script tags\n     *  * object: \\{ fragmentSource: \"fragment shader code string\" \\}, the string contains the shader code\n     *  * string: the string contains a name \"XXX\" to lookup in Effect.ShadersStore[\"XXXFragmentShader\"]\n     * @param scene Define the scene the texture belongs to\n     * @param fallbackTexture Define a fallback texture in case there were issues to create the custom texture\n     * @param generateMipMaps Define if the texture should creates mip maps or not\n     * @param isCube Define if the texture is a cube texture or not (this will render each faces of the cube)\n     * @param textureType The FBO internal texture type\n     */\n    constructor(name, size, fragment, scene, fallbackTexture = null, generateMipMaps = true, isCube = false, textureType = 0) {\n        super(null, scene, !generateMipMaps);\n        /**\n         * Define if the texture is enabled or not (disabled texture will not render)\n         */\n        this.isEnabled = true;\n        /**\n         * Define if the texture must be cleared before rendering (default is true)\n         */\n        this.autoClear = true;\n        /**\n         * Event raised when the texture is generated\n         */\n        this.onGeneratedObservable = new Observable();\n        /**\n         * Event raised before the texture is generated\n         */\n        this.onBeforeGenerationObservable = new Observable();\n        /**\n         * Gets or sets the node material used to create this texture (null if the texture was manually created)\n         */\n        this.nodeMaterialSource = null;\n        /**\n         * Define the list of custom preprocessor defines used in the shader\n         */\n        this.defines = \"\";\n        /** @internal */\n        this._textures = {};\n        this._currentRefreshId = -1;\n        this._frameId = -1;\n        this._refreshRate = 1;\n        this._vertexBuffers = {};\n        this._uniforms = new Array();\n        this._samplers = new Array();\n        this._floats = {};\n        this._ints = {};\n        this._floatsArrays = {};\n        this._colors3 = {};\n        this._colors4 = {};\n        this._vectors2 = {};\n        this._vectors3 = {};\n        this._vectors4 = {};\n        this._matrices = {};\n        this._fallbackTextureUsed = false;\n        this._cachedDefines = null;\n        this._contentUpdateId = -1;\n        this._rtWrapper = null;\n        if (fallbackTexture !== null && !(fallbackTexture instanceof Texture)) {\n            this._options = fallbackTexture;\n            this._fallbackTexture = fallbackTexture.fallbackTexture ?? null;\n        }\n        else {\n            this._options = {};\n            this._fallbackTexture = fallbackTexture;\n        }\n        this._shaderLanguage = this._options.shaderLanguage ?? 0 /* ShaderLanguage.GLSL */;\n        scene = this.getScene() || EngineStore.LastCreatedScene;\n        let component = scene._getComponent(SceneComponentConstants.NAME_PROCEDURALTEXTURE);\n        if (!component) {\n            component = new ProceduralTextureSceneComponent(scene);\n            scene._addComponent(component);\n        }\n        scene.proceduralTextures.push(this);\n        this._fullEngine = scene.getEngine();\n        this.name = name;\n        this.isRenderTarget = true;\n        this._size = size;\n        this._textureType = textureType;\n        this._generateMipMaps = generateMipMaps;\n        this._drawWrapper = new DrawWrapper(this._fullEngine);\n        this.setFragment(fragment);\n        const rtWrapper = this._createRtWrapper(isCube, size, generateMipMaps, textureType);\n        this._texture = rtWrapper.texture;\n        // VBO\n        const vertices = [];\n        vertices.push(1, 1);\n        vertices.push(-1, 1);\n        vertices.push(-1, -1);\n        vertices.push(1, -1);\n        this._vertexBuffers[VertexBuffer.PositionKind] = new VertexBuffer(this._fullEngine, vertices, VertexBuffer.PositionKind, false, false, 2);\n        this._createIndexBuffer();\n    }\n    _createRtWrapper(isCube, size, generateMipMaps, textureType) {\n        if (isCube) {\n            this._rtWrapper = this._fullEngine.createRenderTargetCubeTexture(size, {\n                generateMipMaps: generateMipMaps,\n                generateDepthBuffer: false,\n                generateStencilBuffer: false,\n                type: textureType,\n                ...this._options,\n            });\n            this.setFloat(\"face\", 0);\n        }\n        else {\n            this._rtWrapper = this._fullEngine.createRenderTargetTexture(size, {\n                generateMipMaps: generateMipMaps,\n                generateDepthBuffer: false,\n                generateStencilBuffer: false,\n                type: textureType,\n                ...this._options,\n            });\n            if (this._rtWrapper.is3D) {\n                this.setFloat(\"layer\", 0);\n                this.setInt(\"layerNum\", 0);\n            }\n        }\n        return this._rtWrapper;\n    }\n    /**\n     * The effect that is created when initializing the post process.\n     * @returns The created effect corresponding the postprocess.\n     */\n    getEffect() {\n        return this._drawWrapper.effect;\n    }\n    /**\n     * @internal\n     */\n    _setEffect(effect) {\n        this._drawWrapper.effect = effect;\n    }\n    /**\n     * Gets texture content (Use this function wisely as reading from a texture can be slow)\n     * @returns an ArrayBufferView promise (Uint8Array or Float32Array)\n     */\n    getContent() {\n        if (this._contentData && this._frameId === this._contentUpdateId) {\n            return this._contentData;\n        }\n        if (this._contentData) {\n            this._contentData.then((buffer) => {\n                this._contentData = this.readPixels(0, 0, buffer);\n                this._contentUpdateId = this._frameId;\n            });\n        }\n        else {\n            this._contentData = this.readPixels(0, 0);\n            this._contentUpdateId = this._frameId;\n        }\n        return this._contentData;\n    }\n    _createIndexBuffer() {\n        const engine = this._fullEngine;\n        // Indices\n        const indices = [];\n        indices.push(0);\n        indices.push(1);\n        indices.push(2);\n        indices.push(0);\n        indices.push(2);\n        indices.push(3);\n        this._indexBuffer = engine.createIndexBuffer(indices);\n    }\n    /** @internal */\n    _rebuild() {\n        const vb = this._vertexBuffers[VertexBuffer.PositionKind];\n        if (vb) {\n            vb._rebuild();\n        }\n        this._createIndexBuffer();\n        if (this.refreshRate === RenderTargetTexture.REFRESHRATE_RENDER_ONCE) {\n            this.refreshRate = RenderTargetTexture.REFRESHRATE_RENDER_ONCE;\n        }\n    }\n    /**\n     * Resets the texture in order to recreate its associated resources.\n     * This can be called in case of context loss or if you change the shader code and need to regenerate the texture with the new code\n     */\n    reset() {\n        this._drawWrapper.effect?.dispose();\n        this._drawWrapper.effect = null;\n        this._cachedDefines = null;\n    }\n    _getDefines() {\n        return this.defines;\n    }\n    /**\n     * Executes a function when the texture will be ready to be drawn.\n     * @param func The callback to be used.\n     */\n    executeWhenReady(func) {\n        if (this.isReady()) {\n            func(this);\n            return;\n        }\n        const effect = this.getEffect();\n        if (effect) {\n            effect.executeWhenCompiled(() => {\n                func(this);\n            });\n        }\n    }\n    /**\n     * Is the texture ready to be used ? (rendered at least once)\n     * @returns true if ready, otherwise, false.\n     */\n    isReady() {\n        const engine = this._fullEngine;\n        if (this.nodeMaterialSource) {\n            return this._drawWrapper.effect.isReady();\n        }\n        if (!this._fragment) {\n            return false;\n        }\n        if (this._fallbackTextureUsed) {\n            return true;\n        }\n        if (!this._texture) {\n            return false;\n        }\n        const defines = this._getDefines();\n        if (this._drawWrapper.effect && defines === this._cachedDefines && this._drawWrapper.effect.isReady()) {\n            return true;\n        }\n        const shaders = {\n            vertex: \"procedural\",\n            fragmentElement: this._fragment.fragmentElement,\n            fragmentSource: this._fragment.fragmentSource,\n            fragment: typeof this._fragment === \"string\" ? this._fragment : undefined,\n        };\n        if (this._cachedDefines !== defines) {\n            this._cachedDefines = defines;\n            this._drawWrapper.effect = engine.createEffect(shaders, [VertexBuffer.PositionKind], this._uniforms, this._samplers, defines, undefined, undefined, () => {\n                this._rtWrapper?.dispose();\n                this._rtWrapper = this._texture = null;\n                if (this._fallbackTexture) {\n                    this._texture = this._fallbackTexture._texture;\n                    if (this._texture) {\n                        this._texture.incrementReferences();\n                    }\n                }\n                this._fallbackTextureUsed = true;\n            }, undefined, this._shaderLanguage, async () => {\n                if (this._options.extraInitializationsAsync) {\n                    if (this.shaderLanguage === 1 /* ShaderLanguage.WGSL */) {\n                        await Promise.all([import(\"../../../ShadersWGSL/procedural.vertex.js\"), this._options.extraInitializationsAsync()]);\n                    }\n                    else {\n                        await Promise.all([import(\"../../../Shaders/procedural.vertex.js\"), this._options.extraInitializationsAsync()]);\n                    }\n                }\n                else {\n                    if (this.shaderLanguage === 1 /* ShaderLanguage.WGSL */) {\n                        await import(\"../../../ShadersWGSL/procedural.vertex.js\");\n                    }\n                    else {\n                        await import(\"../../../Shaders/procedural.vertex.js\");\n                    }\n                }\n            });\n        }\n        return this._drawWrapper.effect.isReady();\n    }\n    /**\n     * Resets the refresh counter of the texture and start bak from scratch.\n     * Could be useful to regenerate the texture if it is setup to render only once.\n     */\n    resetRefreshCounter() {\n        this._currentRefreshId = -1;\n    }\n    /**\n     * Set the fragment shader to use in order to render the texture.\n     * @param fragment This can be set to a path (into the shader store) or to a json object containing a fragmentElement property.\n     */\n    setFragment(fragment) {\n        this._fragment = fragment;\n    }\n    /**\n     * Define the refresh rate of the texture or the rendering frequency.\n     * Use 0 to render just once, 1 to render on every frame, 2 to render every two frames and so on...\n     */\n    get refreshRate() {\n        return this._refreshRate;\n    }\n    set refreshRate(value) {\n        this._refreshRate = value;\n        this.resetRefreshCounter();\n    }\n    /** @internal */\n    _shouldRender() {\n        if (!this.isEnabled || !this.isReady() || !this._texture) {\n            if (this._texture) {\n                this._texture.isReady = false;\n            }\n            return false;\n        }\n        if (this._fallbackTextureUsed) {\n            return false;\n        }\n        if (this._currentRefreshId === -1) {\n            // At least render once\n            this._currentRefreshId = 1;\n            this._frameId++;\n            return true;\n        }\n        if (this.refreshRate === this._currentRefreshId) {\n            this._currentRefreshId = 1;\n            this._frameId++;\n            return true;\n        }\n        this._currentRefreshId++;\n        return false;\n    }\n    /**\n     * Get the size the texture is rendering at.\n     * @returns the size (on cube texture it is always squared)\n     */\n    getRenderSize() {\n        return this._size;\n    }\n    /**\n     * Resize the texture to new value.\n     * @param size Define the new size the texture should have\n     * @param generateMipMaps Define whether the new texture should create mip maps\n     */\n    resize(size, generateMipMaps) {\n        if (this._fallbackTextureUsed || !this._rtWrapper || !this._texture) {\n            return;\n        }\n        const isCube = this._texture.isCube;\n        this._rtWrapper.dispose();\n        const rtWrapper = this._createRtWrapper(isCube, size, generateMipMaps, this._textureType);\n        this._texture = rtWrapper.texture;\n        // Update properties\n        this._size = size;\n        this._generateMipMaps = generateMipMaps;\n    }\n    _checkUniform(uniformName) {\n        if (this._uniforms.indexOf(uniformName) === -1) {\n            this._uniforms.push(uniformName);\n        }\n    }\n    /**\n     * Set a texture in the shader program used to render.\n     * @param name Define the name of the uniform samplers as defined in the shader\n     * @param texture Define the texture to bind to this sampler\n     * @returns the texture itself allowing \"fluent\" like uniform updates\n     */\n    setTexture(name, texture) {\n        if (this._samplers.indexOf(name) === -1) {\n            this._samplers.push(name);\n        }\n        this._textures[name] = texture;\n        return this;\n    }\n    /**\n     * Set a float in the shader.\n     * @param name Define the name of the uniform as defined in the shader\n     * @param value Define the value to give to the uniform\n     * @returns the texture itself allowing \"fluent\" like uniform updates\n     */\n    setFloat(name, value) {\n        this._checkUniform(name);\n        this._floats[name] = value;\n        return this;\n    }\n    /**\n     * Set a int in the shader.\n     * @param name Define the name of the uniform as defined in the shader\n     * @param value Define the value to give to the uniform\n     * @returns the texture itself allowing \"fluent\" like uniform updates\n     */\n    setInt(name, value) {\n        this._checkUniform(name);\n        this._ints[name] = value;\n        return this;\n    }\n    /**\n     * Set an array of floats in the shader.\n     * @param name Define the name of the uniform as defined in the shader\n     * @param value Define the value to give to the uniform\n     * @returns the texture itself allowing \"fluent\" like uniform updates\n     */\n    setFloats(name, value) {\n        this._checkUniform(name);\n        this._floatsArrays[name] = value;\n        return this;\n    }\n    /**\n     * Set a vec3 in the shader from a Color3.\n     * @param name Define the name of the uniform as defined in the shader\n     * @param value Define the value to give to the uniform\n     * @returns the texture itself allowing \"fluent\" like uniform updates\n     */\n    setColor3(name, value) {\n        this._checkUniform(name);\n        this._colors3[name] = value;\n        return this;\n    }\n    /**\n     * Set a vec4 in the shader from a Color4.\n     * @param name Define the name of the uniform as defined in the shader\n     * @param value Define the value to give to the uniform\n     * @returns the texture itself allowing \"fluent\" like uniform updates\n     */\n    setColor4(name, value) {\n        this._checkUniform(name);\n        this._colors4[name] = value;\n        return this;\n    }\n    /**\n     * Set a vec2 in the shader from a Vector2.\n     * @param name Define the name of the uniform as defined in the shader\n     * @param value Define the value to give to the uniform\n     * @returns the texture itself allowing \"fluent\" like uniform updates\n     */\n    setVector2(name, value) {\n        this._checkUniform(name);\n        this._vectors2[name] = value;\n        return this;\n    }\n    /**\n     * Set a vec3 in the shader from a Vector3.\n     * @param name Define the name of the uniform as defined in the shader\n     * @param value Define the value to give to the uniform\n     * @returns the texture itself allowing \"fluent\" like uniform updates\n     */\n    setVector3(name, value) {\n        this._checkUniform(name);\n        this._vectors3[name] = value;\n        return this;\n    }\n    /**\n     * Set a vec4 in the shader from a Vector4.\n     * @param name Define the name of the uniform as defined in the shader\n     * @param value Define the value to give to the uniform\n     * @returns the texture itself allowing \"fluent\" like uniform updates\n     */\n    setVector4(name, value) {\n        this._checkUniform(name);\n        this._vectors4[name] = value;\n        return this;\n    }\n    /**\n     * Set a mat4 in the shader from a MAtrix.\n     * @param name Define the name of the uniform as defined in the shader\n     * @param value Define the value to give to the uniform\n     * @returns the texture itself allowing \"fluent\" like uniform updates\n     */\n    setMatrix(name, value) {\n        this._checkUniform(name);\n        this._matrices[name] = value;\n        return this;\n    }\n    /**\n     * Render the texture to its associated render target.\n     * @param useCameraPostProcess Define if camera post process should be applied to the texture\n     */\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    render(useCameraPostProcess) {\n        const scene = this.getScene();\n        if (!scene) {\n            return;\n        }\n        const engine = this._fullEngine;\n        // Render\n        engine.enableEffect(this._drawWrapper);\n        this.onBeforeGenerationObservable.notifyObservers(this);\n        engine.setState(false);\n        if (!this.nodeMaterialSource) {\n            // Texture\n            for (const name in this._textures) {\n                this._drawWrapper.effect.setTexture(name, this._textures[name]);\n            }\n            // Float\n            for (const name in this._ints) {\n                this._drawWrapper.effect.setInt(name, this._ints[name]);\n            }\n            // Float\n            for (const name in this._floats) {\n                this._drawWrapper.effect.setFloat(name, this._floats[name]);\n            }\n            // Floats\n            for (const name in this._floatsArrays) {\n                this._drawWrapper.effect.setArray(name, this._floatsArrays[name]);\n            }\n            // Color3\n            for (const name in this._colors3) {\n                this._drawWrapper.effect.setColor3(name, this._colors3[name]);\n            }\n            // Color4\n            for (const name in this._colors4) {\n                const color = this._colors4[name];\n                this._drawWrapper.effect.setFloat4(name, color.r, color.g, color.b, color.a);\n            }\n            // Vector2\n            for (const name in this._vectors2) {\n                this._drawWrapper.effect.setVector2(name, this._vectors2[name]);\n            }\n            // Vector3\n            for (const name in this._vectors3) {\n                this._drawWrapper.effect.setVector3(name, this._vectors3[name]);\n            }\n            // Vector4\n            for (const name in this._vectors4) {\n                this._drawWrapper.effect.setVector4(name, this._vectors4[name]);\n            }\n            // Matrix\n            for (const name in this._matrices) {\n                this._drawWrapper.effect.setMatrix(name, this._matrices[name]);\n            }\n        }\n        if (!this._texture || !this._rtWrapper) {\n            return;\n        }\n        engine._debugPushGroup?.(`procedural texture generation for ${this.name}`, 1);\n        const viewPort = engine.currentViewport;\n        if (this.isCube) {\n            for (let face = 0; face < 6; face++) {\n                engine.bindFramebuffer(this._rtWrapper, face, undefined, undefined, true);\n                // VBOs\n                engine.bindBuffers(this._vertexBuffers, this._indexBuffer, this._drawWrapper.effect);\n                this._drawWrapper.effect.setFloat(\"face\", face);\n                // Clear\n                if (this.autoClear) {\n                    engine.clear(scene.clearColor, true, false, false);\n                }\n                // Draw order\n                engine.drawElementsType(Material.TriangleFillMode, 0, 6);\n                // Unbind and restore viewport\n                engine.unBindFramebuffer(this._rtWrapper, true);\n            }\n        }\n        else {\n            let numLayers = 1;\n            if (this._rtWrapper.is3D) {\n                numLayers = this._rtWrapper.depth;\n            }\n            else if (this._rtWrapper.is2DArray) {\n                numLayers = this._rtWrapper.layers;\n            }\n            for (let layer = 0; layer < numLayers; layer++) {\n                engine.bindFramebuffer(this._rtWrapper, 0, undefined, undefined, true, 0, layer);\n                // VBOs\n                engine.bindBuffers(this._vertexBuffers, this._indexBuffer, this._drawWrapper.effect);\n                if (this._rtWrapper.is3D || this._rtWrapper.is2DArray) {\n                    this._drawWrapper.effect?.setFloat(\"layer\", numLayers !== 1 ? layer / (numLayers - 1) : 0);\n                    this._drawWrapper.effect?.setInt(\"layerNum\", layer);\n                    for (const name in this._textures) {\n                        this._drawWrapper.effect.setTexture(name, this._textures[name]);\n                    }\n                }\n                // Clear\n                if (this.autoClear) {\n                    engine.clear(scene.clearColor, true, false, false);\n                }\n                // Draw order\n                engine.drawElementsType(Material.TriangleFillMode, 0, 6);\n                // Unbind and restore viewport\n                engine.unBindFramebuffer(this._rtWrapper, !this._generateMipMaps);\n            }\n        }\n        if (viewPort) {\n            engine.setViewport(viewPort);\n        }\n        // Mipmaps\n        if (this.isCube) {\n            engine.generateMipMapsForCubemap(this._texture, true);\n        }\n        engine._debugPopGroup?.(1);\n        if (this.onGenerated) {\n            this.onGenerated();\n        }\n        this.onGeneratedObservable.notifyObservers(this);\n    }\n    /**\n     * Clone the texture.\n     * @returns the cloned texture\n     */\n    clone() {\n        const textureSize = this.getSize();\n        const newTexture = new ProceduralTexture(this.name, textureSize.width, this._fragment, this.getScene(), this._fallbackTexture, this._generateMipMaps);\n        // Base texture\n        newTexture.hasAlpha = this.hasAlpha;\n        newTexture.level = this.level;\n        // RenderTarget Texture\n        newTexture.coordinatesMode = this.coordinatesMode;\n        return newTexture;\n    }\n    /**\n     * Dispose the texture and release its associated resources.\n     */\n    dispose() {\n        const scene = this.getScene();\n        if (!scene) {\n            return;\n        }\n        const index = scene.proceduralTextures.indexOf(this);\n        if (index >= 0) {\n            scene.proceduralTextures.splice(index, 1);\n        }\n        const vertexBuffer = this._vertexBuffers[VertexBuffer.PositionKind];\n        if (vertexBuffer) {\n            vertexBuffer.dispose();\n            this._vertexBuffers[VertexBuffer.PositionKind] = null;\n        }\n        if (this._indexBuffer && this._fullEngine._releaseBuffer(this._indexBuffer)) {\n            this._indexBuffer = null;\n        }\n        this.onGeneratedObservable.clear();\n        this.onBeforeGenerationObservable.clear();\n        super.dispose();\n    }\n}\n__decorate([\n    serialize()\n], ProceduralTexture.prototype, \"isEnabled\", void 0);\n__decorate([\n    serialize()\n], ProceduralTexture.prototype, \"autoClear\", void 0);\n__decorate([\n    serialize()\n], ProceduralTexture.prototype, \"_generateMipMaps\", void 0);\n__decorate([\n    serialize()\n], ProceduralTexture.prototype, \"_size\", void 0);\n__decorate([\n    serialize()\n], ProceduralTexture.prototype, \"refreshRate\", null);\nRegisterClass(\"BABYLON.ProceduralTexture\", ProceduralTexture);\n//# sourceMappingURL=proceduralTexture.js.map", "\nimport { Texture } from \"../Materials/Textures/texture.js\";\nimport { ProceduralTexture } from \"../Materials/Textures/Procedurals/proceduralTexture.js\";\nimport { PostProcess } from \"../PostProcesses/postProcess.js\";\nimport { Vector4 } from \"../Maths/math.vector.js\";\nimport { RawTexture } from \"../Materials/Textures/rawTexture.js\";\nimport { Observable } from \"../Misc/observable.js\";\nimport { Engine } from \"../Engines/engine.js\";\nimport { _WarnImport } from \"../Misc/devTools.js\";\nimport { EngineStore } from \"../Engines/engineStore.js\";\n/**\n * Build cdf maps to be used for IBL importance sampling.\n */\nexport class IblCdfGenerator {\n    /**\n     * Gets the IBL source texture being used by the CDF renderer\n     */\n    get iblSource() {\n        return this._iblSource;\n    }\n    /**\n     * Sets the IBL source texture to be used by the CDF renderer.\n     * This will trigger recreation of the CDF assets.\n     */\n    set iblSource(source) {\n        if (this._iblSource === source) {\n            return;\n        }\n        this._disposeTextures();\n        this._iblSource = source;\n        if (!source) {\n            return;\n        }\n        if (source.isCube) {\n            if (source.isReadyOrNotBlocking()) {\n                this._recreateAssetsFromNewIbl();\n            }\n            else {\n                source.onLoadObservable.addOnce(this._recreateAssetsFromNewIbl.bind(this, source));\n            }\n        }\n        else {\n            if (source.isReadyOrNotBlocking()) {\n                this._recreateAssetsFromNewIbl();\n            }\n            else {\n                source.onLoadObservable.addOnce(this._recreateAssetsFromNewIbl.bind(this, source));\n            }\n        }\n    }\n    _recreateAssetsFromNewIbl() {\n        if (this._debugPass) {\n            this._debugPass.dispose();\n        }\n        this._createTextures();\n        if (this._debugPass) {\n            // Recreate the debug pass because of the new textures\n            this._createDebugPass();\n        }\n    }\n    /**\n     * Return the cumulative distribution function (CDF) texture\n     * @returns Return the cumulative distribution function (CDF) texture\n     */\n    getIcdfTexture() {\n        return this._icdfPT ? this._icdfPT : this._dummyTexture;\n    }\n    /**\n     * Sets params that control the position and scaling of the debug display on the screen.\n     * @param x Screen X offset of the debug display (0-1)\n     * @param y Screen Y offset of the debug display (0-1)\n     * @param widthScale X scale of the debug display (0-1)\n     * @param heightScale Y scale of the debug display (0-1)\n     */\n    setDebugDisplayParams(x, y, widthScale, heightScale) {\n        this._debugSizeParams.set(x, y, widthScale, heightScale);\n    }\n    /**\n     * The name of the debug pass post process\n     */\n    get debugPassName() {\n        return this._debugPassName;\n    }\n    /**\n     * Gets the debug pass post process\n     * @returns The post process\n     */\n    getDebugPassPP() {\n        if (!this._debugPass) {\n            this._createDebugPass();\n        }\n        return this._debugPass;\n    }\n    /**\n     * Instanciates the CDF renderer\n     * @param sceneOrEngine Scene to attach to\n     * @returns The CDF renderer\n     */\n    constructor(sceneOrEngine) {\n        /** Enable the debug view for this pass */\n        this.debugEnabled = false;\n        this._debugSizeParams = new Vector4(0.0, 0.0, 1.0, 1.0);\n        this._debugPassName = \"CDF Debug\";\n        /**\n         * Observable that triggers when the CDF renderer is ready\n         */\n        this.onGeneratedObservable = new Observable();\n        if (sceneOrEngine) {\n            if (IblCdfGenerator._IsScene(sceneOrEngine)) {\n                this._scene = sceneOrEngine;\n            }\n            else {\n                this._engine = sceneOrEngine;\n            }\n        }\n        else {\n            this._scene = EngineStore.LastCreatedScene;\n        }\n        if (this._scene) {\n            this._engine = this._scene.getEngine();\n        }\n        const blackPixels = new Uint16Array([0, 0, 0, 255]);\n        this._dummyTexture = new RawTexture(blackPixels, 1, 1, Engine.TEXTUREFORMAT_RGBA, sceneOrEngine, false, false, undefined, 2);\n        if (this._scene) {\n            IblCdfGenerator._SceneComponentInitialization(this._scene);\n        }\n    }\n    _createTextures() {\n        const size = this._iblSource ? { width: this._iblSource.getSize().width, height: this._iblSource.getSize().height } : { width: 1, height: 1 };\n        if (!this._iblSource) {\n            this._iblSource = RawTexture.CreateRTexture(new Uint8Array([255]), 1, 1, this._engine, false, false, 1, 0);\n            this._iblSource.name = \"Placeholder IBL Source\";\n        }\n        if (this._iblSource.isCube) {\n            size.width *= 4;\n            size.height *= 2;\n            // Force the resolution to be a power of 2 because we rely on the\n            // auto-mipmap generation for the scaled luminance texture to produce\n            // a 1x1 mip that represents the true average pixel intensity of the IBL.\n            size.width = 1 << Math.floor(Math.log2(size.width));\n            size.height = 1 << Math.floor(Math.log2(size.height));\n        }\n        const isWebGPU = this._engine.isWebGPU;\n        // Create CDF maps (Cumulative Distribution Function) to assist in importance sampling\n        const cdfOptions = {\n            generateDepthBuffer: false,\n            generateMipMaps: false,\n            format: 6,\n            type: 1,\n            samplingMode: 1,\n            shaderLanguage: isWebGPU ? 1 /* ShaderLanguage.WGSL */ : 0 /* ShaderLanguage.GLSL */,\n            gammaSpace: false,\n            extraInitializationsAsync: async () => {\n                if (isWebGPU) {\n                    await Promise.all([import(\"../ShadersWGSL/iblCdfx.fragment.js\"), import(\"../ShadersWGSL/iblCdfy.fragment.js\"), import(\"../ShadersWGSL/iblScaledLuminance.fragment.js\")]);\n                }\n                else {\n                    await Promise.all([import(\"../Shaders/iblCdfx.fragment.js\"), import(\"../Shaders/iblCdfy.fragment.js\"), import(\"../Shaders/iblScaledLuminance.fragment.js\")]);\n                }\n            },\n        };\n        const icdfOptions = {\n            generateDepthBuffer: false,\n            generateMipMaps: false,\n            format: 5,\n            type: 2,\n            samplingMode: 1,\n            shaderLanguage: isWebGPU ? 1 /* ShaderLanguage.WGSL */ : 0 /* ShaderLanguage.GLSL */,\n            gammaSpace: false,\n            extraInitializationsAsync: async () => {\n                if (isWebGPU) {\n                    await Promise.all([import(\"../ShadersWGSL/iblIcdf.fragment.js\")]);\n                }\n                else {\n                    await Promise.all([import(\"../Shaders/iblIcdf.fragment.js\")]);\n                }\n            },\n        };\n        this._cdfyPT = new ProceduralTexture(\"cdfyTexture\", { width: size.width, height: size.height + 1 }, \"iblCdfy\", this._scene, cdfOptions, false, false);\n        this._cdfyPT.autoClear = false;\n        this._cdfyPT.setTexture(\"iblSource\", this._iblSource);\n        this._cdfyPT.setInt(\"iblHeight\", size.height);\n        this._cdfyPT.wrapV = 0;\n        this._cdfyPT.refreshRate = 0;\n        if (this._iblSource.isCube) {\n            this._cdfyPT.defines = \"#define IBL_USE_CUBE_MAP\\n\";\n        }\n        this._cdfxPT = new ProceduralTexture(\"cdfxTexture\", { width: size.width + 1, height: 1 }, \"iblCdfx\", this._scene, cdfOptions, false, false);\n        this._cdfxPT.autoClear = false;\n        this._cdfxPT.setTexture(\"cdfy\", this._cdfyPT);\n        this._cdfxPT.refreshRate = 0;\n        this._cdfxPT.wrapU = 0;\n        this._scaledLuminancePT = new ProceduralTexture(\"iblScaledLuminance\", { width: size.width, height: size.height }, \"iblScaledLuminance\", this._scene, { ...cdfOptions, samplingMode: 3, generateMipMaps: true }, true, false);\n        this._scaledLuminancePT.autoClear = false;\n        this._scaledLuminancePT.setTexture(\"iblSource\", this._iblSource);\n        this._scaledLuminancePT.setInt(\"iblHeight\", size.height);\n        this._scaledLuminancePT.setInt(\"iblWidth\", size.width);\n        this._scaledLuminancePT.refreshRate = 0;\n        if (this._iblSource.isCube) {\n            this._scaledLuminancePT.defines = \"#define IBL_USE_CUBE_MAP\\n\";\n        }\n        this._icdfPT = new ProceduralTexture(\"icdfTexture\", { width: size.width, height: size.height }, \"iblIcdf\", this._scene, icdfOptions, false, false);\n        this._icdfPT.autoClear = false;\n        this._icdfPT.setTexture(\"cdfy\", this._cdfyPT);\n        this._icdfPT.setTexture(\"cdfx\", this._cdfxPT);\n        this._icdfPT.setTexture(\"iblSource\", this._iblSource);\n        this._icdfPT.setTexture(\"scaledLuminanceSampler\", this._scaledLuminancePT);\n        this._icdfPT.refreshRate = 0;\n        this._icdfPT.wrapV = 0;\n        this._icdfPT.wrapU = 0;\n        if (this._iblSource.isCube) {\n            this._icdfPT.defines = \"#define IBL_USE_CUBE_MAP\\n\";\n        }\n        // Once the textures are generated, notify that they are ready to use.\n        this._icdfPT.onGeneratedObservable.addOnce(() => {\n            this.onGeneratedObservable.notifyObservers();\n        });\n    }\n    _disposeTextures() {\n        this._cdfyPT?.dispose();\n        this._cdfxPT?.dispose();\n        this._icdfPT?.dispose();\n        this._scaledLuminancePT?.dispose();\n    }\n    _createDebugPass() {\n        if (this._debugPass) {\n            this._debugPass.dispose();\n        }\n        const isWebGPU = this._engine.isWebGPU;\n        const debugOptions = {\n            width: this._engine.getRenderWidth(),\n            height: this._engine.getRenderHeight(),\n            samplingMode: Texture.BILINEAR_SAMPLINGMODE,\n            engine: this._engine,\n            textureType: 0,\n            uniforms: [\"sizeParams\"],\n            samplers: [\"cdfy\", \"icdf\", \"cdfx\", \"iblSource\"],\n            defines: this._iblSource?.isCube ? \"#define IBL_USE_CUBE_MAP\\n\" : \"\",\n            shaderLanguage: isWebGPU ? 1 /* ShaderLanguage.WGSL */ : 0 /* ShaderLanguage.GLSL */,\n            extraInitializations: (useWebGPU, list) => {\n                if (useWebGPU) {\n                    list.push(import(\"../ShadersWGSL/iblCdfDebug.fragment.js\"));\n                }\n                else {\n                    list.push(import(\"../Shaders/iblCdfDebug.fragment.js\"));\n                }\n            },\n        };\n        this._debugPass = new PostProcess(this._debugPassName, \"iblCdfDebug\", debugOptions);\n        const debugEffect = this._debugPass.getEffect();\n        if (debugEffect) {\n            debugEffect.defines = this._iblSource?.isCube ? \"#define IBL_USE_CUBE_MAP\\n\" : \"\";\n        }\n        if (this._iblSource?.isCube) {\n            this._debugPass.updateEffect(\"#define IBL_USE_CUBE_MAP\\n\");\n        }\n        this._debugPass.onApplyObservable.add((effect) => {\n            effect.setTexture(\"cdfy\", this._cdfyPT);\n            effect.setTexture(\"icdf\", this._icdfPT);\n            effect.setTexture(\"cdfx\", this._cdfxPT);\n            effect.setTexture(\"iblSource\", this._iblSource);\n            effect.setFloat4(\"sizeParams\", this._debugSizeParams.x, this._debugSizeParams.y, this._debugSizeParams.z, this._debugSizeParams.w);\n        });\n    }\n    /**\n     * Checks if the CDF renderer is ready\n     * @returns true if the CDF renderer is ready\n     */\n    isReady() {\n        return (this._iblSource &&\n            this._iblSource.name !== \"Placeholder IBL Source\" &&\n            this._iblSource.isReady() &&\n            this._cdfyPT &&\n            this._cdfyPT.isReady() &&\n            this._icdfPT &&\n            this._icdfPT.isReady() &&\n            this._cdfxPT &&\n            this._cdfxPT.isReady() &&\n            this._scaledLuminancePT &&\n            this._scaledLuminancePT.isReady());\n    }\n    /**\n     * Explicitly trigger generation of CDF maps when they are ready to render.\n     * @returns Promise that resolves when the CDF maps are rendered.\n     */\n    renderWhenReady() {\n        // Once the textures are generated, notify that they are ready to use.\n        this._icdfPT.onGeneratedObservable.addOnce(() => {\n            this.onGeneratedObservable.notifyObservers();\n        });\n        const promises = [];\n        const renderTargets = [this._cdfyPT, this._cdfxPT, this._scaledLuminancePT, this._icdfPT];\n        renderTargets.forEach((target) => {\n            promises.push(new Promise((resolve) => {\n                if (target.isReady()) {\n                    resolve();\n                }\n                else {\n                    target.getEffect().executeWhenCompiled(() => {\n                        resolve();\n                    });\n                }\n            }));\n        });\n        return Promise.all(promises).then(() => {\n            renderTargets.forEach((target) => {\n                target.render();\n            });\n        });\n    }\n    /**\n     * Disposes the CDF renderer and associated resources\n     */\n    dispose() {\n        this._disposeTextures();\n        this._dummyTexture.dispose();\n        if (this._debugPass) {\n            this._debugPass.dispose();\n        }\n        this.onGeneratedObservable.clear();\n    }\n    static _IsScene(sceneOrEngine) {\n        return sceneOrEngine.getClassName() === \"Scene\";\n    }\n}\n/**\n * @internal\n */\nIblCdfGenerator._SceneComponentInitialization = (_) => {\n    throw _WarnImport(\"IblCdfGeneratorSceneComponentSceneComponent\");\n};\n//# sourceMappingURL=iblCdfGenerator.js.map", "import { Vector3 } from \"../../../Maths/math.js\";\nimport { ILog2 } from \"../../../Maths/math.scalar.functions.js\";\nimport { BaseTexture } from \"../baseTexture.js\";\n\nimport { EffectWrapper, EffectRenderer } from \"../../../Materials/effectRenderer.js\";\nimport { IblCdfGenerator } from \"../../../Rendering/iblCdfGenerator.js\";\n/**\n * Filters HDR maps to get correct renderings of PBR reflections\n */\nexport class HDRIrradianceFiltering {\n    /**\n     * Instantiates HDR filter for irradiance map\n     *\n     * @param engine Thin engine\n     * @param options Options\n     */\n    constructor(engine, options = {}) {\n        /**\n         * Quality switch for prefiltering. Should be set to `4096` unless\n         * you care about baking speed.\n         */\n        this.quality = 4096;\n        /**\n         * Scales pixel intensity for the input HDR map.\n         */\n        this.hdrScale = 1;\n        /**\n         * Use the Cumulative Distribution Function (CDF) for filtering\n         */\n        this.useCdf = false;\n        // pass\n        this._engine = engine;\n        this.hdrScale = options.hdrScale || this.hdrScale;\n        this.quality = options.quality || this.quality;\n        this.useCdf = options.useCdf || this.useCdf;\n    }\n    _createRenderTarget(size) {\n        let textureType = 0;\n        if (this._engine.getCaps().textureHalfFloatRender) {\n            textureType = 2;\n        }\n        else if (this._engine.getCaps().textureFloatRender) {\n            textureType = 1;\n        }\n        const rtWrapper = this._engine.createRenderTargetCubeTexture(size, {\n            format: 5,\n            type: textureType,\n            createMipMaps: false,\n            generateMipMaps: false,\n            generateDepthBuffer: false,\n            generateStencilBuffer: false,\n            samplingMode: 2,\n            label: \"HDR_Irradiance_Filtering_Target\",\n        });\n        this._engine.updateTextureWrappingMode(rtWrapper.texture, 0, 0, 0);\n        return rtWrapper;\n    }\n    _prefilterInternal(texture) {\n        const width = texture.getSize().width;\n        const mipmapsCount = ILog2(width);\n        const effect = this._effectWrapper.effect;\n        // Choose a power of 2 size for the irradiance map.\n        // It can be much smaller than the original texture.\n        const irradianceSize = Math.max(32, 1 << ILog2(width >> 3));\n        const outputTexture = this._createRenderTarget(irradianceSize);\n        this._effectRenderer.saveStates();\n        this._effectRenderer.setViewport();\n        this._effectRenderer.applyEffectWrapper(this._effectWrapper);\n        const directions = [\n            [new Vector3(0, 0, -1), new Vector3(0, -1, 0), new Vector3(1, 0, 0)], // PositiveX\n            [new Vector3(0, 0, 1), new Vector3(0, -1, 0), new Vector3(-1, 0, 0)], // NegativeX\n            [new Vector3(1, 0, 0), new Vector3(0, 0, 1), new Vector3(0, 1, 0)], // PositiveY\n            [new Vector3(1, 0, 0), new Vector3(0, 0, -1), new Vector3(0, -1, 0)], // NegativeY\n            [new Vector3(1, 0, 0), new Vector3(0, -1, 0), new Vector3(0, 0, 1)], // PositiveZ\n            [new Vector3(-1, 0, 0), new Vector3(0, -1, 0), new Vector3(0, 0, -1)], // NegativeZ\n        ];\n        effect.setFloat(\"hdrScale\", this.hdrScale);\n        effect.setFloat2(\"vFilteringInfo\", texture.getSize().width, mipmapsCount);\n        effect.setTexture(\"inputTexture\", texture);\n        if (this._cdfGenerator) {\n            effect.setTexture(\"icdfTexture\", this._cdfGenerator.getIcdfTexture());\n        }\n        for (let face = 0; face < 6; face++) {\n            effect.setVector3(\"up\", directions[face][0]);\n            effect.setVector3(\"right\", directions[face][1]);\n            effect.setVector3(\"front\", directions[face][2]);\n            this._engine.bindFramebuffer(outputTexture, face, undefined, undefined, true);\n            this._effectRenderer.applyEffectWrapper(this._effectWrapper);\n            this._effectRenderer.draw();\n        }\n        // Cleanup\n        this._effectRenderer.restoreStates();\n        this._engine.restoreDefaultFramebuffer();\n        effect.setTexture(\"inputTexture\", null);\n        effect.setTexture(\"icdfTexture\", null);\n        const irradianceTexture = new BaseTexture(texture.getScene(), outputTexture.texture);\n        irradianceTexture.name = texture.name + \"_irradiance\";\n        irradianceTexture.displayName = texture.name + \"_irradiance\";\n        irradianceTexture.gammaSpace = false;\n        return irradianceTexture;\n    }\n    _createEffect(texture, onCompiled) {\n        const defines = [];\n        if (texture.gammaSpace) {\n            defines.push(\"#define GAMMA_INPUT\");\n        }\n        defines.push(\"#define NUM_SAMPLES \" + this.quality + \"u\"); // unsigned int\n        const isWebGPU = this._engine.isWebGPU;\n        const samplers = [\"inputTexture\"];\n        if (this._cdfGenerator) {\n            samplers.push(\"icdfTexture\");\n            defines.push(\"#define IBL_CDF_FILTERING\");\n        }\n        const effectWrapper = new EffectWrapper({\n            engine: this._engine,\n            name: \"HDRIrradianceFiltering\",\n            vertexShader: \"hdrIrradianceFiltering\",\n            fragmentShader: \"hdrIrradianceFiltering\",\n            samplerNames: samplers,\n            uniformNames: [\"vSampleDirections\", \"vWeights\", \"up\", \"right\", \"front\", \"vFilteringInfo\", \"hdrScale\"],\n            useShaderStore: true,\n            defines,\n            onCompiled: onCompiled,\n            shaderLanguage: isWebGPU ? 1 /* ShaderLanguage.WGSL */ : 0 /* ShaderLanguage.GLSL */,\n            extraInitializationsAsync: async () => {\n                if (isWebGPU) {\n                    await Promise.all([import(\"../../../ShadersWGSL/hdrIrradianceFiltering.vertex.js\"), import(\"../../../ShadersWGSL/hdrIrradianceFiltering.fragment.js\")]);\n                }\n                else {\n                    await Promise.all([import(\"../../../Shaders/hdrIrradianceFiltering.vertex.js\"), import(\"../../../Shaders/hdrIrradianceFiltering.fragment.js\")]);\n                }\n            },\n        });\n        return effectWrapper;\n    }\n    /**\n     * Get a value indicating if the filter is ready to be used\n     * @param texture Texture to filter\n     * @returns true if the filter is ready\n     */\n    isReady(texture) {\n        return texture.isReady() && this._effectWrapper.effect.isReady();\n    }\n    /**\n     * Prefilters a cube texture to contain IBL irradiance.\n     * Prefiltering will be invoked at the end of next rendering pass.\n     * This has to be done once the map is loaded, and has not been prefiltered by a third party software.\n     * See http://blog.selfshadow.com/publications/s2013-shading-course/karis/s2013_pbs_epic_notes_v2.pdf for more information\n     * @param texture Texture to filter\n     * @returns Promise called when prefiltering is done\n     */\n    async prefilter(texture) {\n        if (!this._engine._features.allowTexturePrefiltering) {\n            throw new Error(\"HDR prefiltering is not available in WebGL 1., you can use real time filtering instead.\");\n        }\n        if (this.useCdf) {\n            this._cdfGenerator = new IblCdfGenerator(this._engine);\n            this._cdfGenerator.iblSource = texture;\n            await this._cdfGenerator.renderWhenReady();\n        }\n        this._effectRenderer = new EffectRenderer(this._engine);\n        this._effectWrapper = this._createEffect(texture);\n        await this._effectWrapper.effect.whenCompiledAsync();\n        const irradianceTexture = this._prefilterInternal(texture);\n        this._effectRenderer.dispose();\n        this._effectWrapper.dispose();\n        this._cdfGenerator?.dispose();\n        return irradianceTexture;\n    }\n}\n//# sourceMappingURL=hdrIrradianceFiltering.js.map", "import { Matrix, Vector3 } from \"../../Maths/math.vector.js\";\nimport { BaseTexture } from \"../../Materials/Textures/baseTexture.js\";\nimport { Texture } from \"../../Materials/Textures/texture.js\";\n\nimport { GetCubeMapTextureData } from \"../../Misc/HighDynamicRange/hdr.js\";\nimport { CubeMapToSphericalPolynomialTools } from \"../../Misc/HighDynamicRange/cubemapToSphericalPolynomial.js\";\nimport { RegisterClass } from \"../../Misc/typeStore.js\";\nimport { Observable } from \"../../Misc/observable.js\";\nimport { Tools } from \"../../Misc/tools.js\";\nimport { ToGammaSpace } from \"../../Maths/math.constants.js\";\nimport { HDRFiltering } from \"../../Materials/Textures/Filtering/hdrFiltering.js\";\nimport { HDRIrradianceFiltering } from \"../../Materials/Textures/Filtering/hdrIrradianceFiltering.js\";\nimport { ToHalfFloat } from \"../../Misc/textureTools.js\";\nimport \"../../Materials/Textures/baseTexture.polynomial.js\";\n/**\n * This represents a texture coming from an HDR input.\n *\n * The only supported format is currently panorama picture stored in RGBE format.\n * Example of such files can be found on Poly Haven: https://polyhaven.com/hdris\n */\nexport class HDRCubeTexture extends BaseTexture {\n    /**\n     * Sets whether or not the texture is blocking during loading.\n     */\n    set isBlocking(value) {\n        this._isBlocking = value;\n    }\n    /**\n     * Gets whether or not the texture is blocking during loading.\n     */\n    get isBlocking() {\n        return this._isBlocking;\n    }\n    /**\n     * Sets texture matrix rotation angle around Y axis in radians.\n     */\n    set rotationY(value) {\n        this._rotationY = value;\n        this.setReflectionTextureMatrix(Matrix.RotationY(this._rotationY));\n    }\n    /**\n     * Gets texture matrix rotation angle around Y axis radians.\n     */\n    get rotationY() {\n        return this._rotationY;\n    }\n    /**\n     * Gets or sets the size of the bounding box associated with the cube texture\n     * When defined, the cubemap will switch to local mode\n     * @see https://community.arm.com/graphics/b/blog/posts/reflections-based-on-local-cubemaps-in-unity\n     * @example https://www.babylonjs-playground.com/#RNASML\n     */\n    set boundingBoxSize(value) {\n        if (this._boundingBoxSize && this._boundingBoxSize.equals(value)) {\n            return;\n        }\n        this._boundingBoxSize = value;\n        const scene = this.getScene();\n        if (scene) {\n            scene.markAllMaterialsAsDirty(1);\n        }\n    }\n    get boundingBoxSize() {\n        return this._boundingBoxSize;\n    }\n    /**\n     * Instantiates an HDRTexture from the following parameters.\n     *\n     * @param url The location of the HDR raw data (Panorama stored in RGBE format)\n     * @param sceneOrEngine The scene or engine the texture will be used in\n     * @param size The cubemap desired size (the more it increases the longer the generation will be)\n     * @param noMipmap Forces to not generate the mipmap if true\n     * @param generateHarmonics Specifies whether you want to extract the polynomial harmonics during the generation process\n     * @param gammaSpace Specifies if the texture will be use in gamma or linear space (the PBR material requires those texture in linear space, but the standard material would require them in Gamma space)\n     * @param prefilterOnLoad Prefilters HDR texture to allow use of this texture as a PBR reflection texture.\n     * @param onLoad on success callback function\n     * @param onError on error callback function\n     * @param supersample Defines if texture must be supersampled (default: false)\n     * @param prefilterIrradianceOnLoad Prefilters HDR texture to allow use of this texture for irradiance lighting.\n     * @param prefilterUsingCdf Defines if the prefiltering should be done using a CDF instead of the default approach.\n     */\n    constructor(url, sceneOrEngine, size, noMipmap = false, generateHarmonics = true, gammaSpace = false, prefilterOnLoad = false, onLoad = null, onError = null, supersample = false, prefilterIrradianceOnLoad = false, prefilterUsingCdf = false) {\n        super(sceneOrEngine);\n        this._generateHarmonics = true;\n        this._onError = null;\n        this._isBlocking = true;\n        this._rotationY = 0;\n        /**\n         * Gets or sets the center of the bounding box associated with the cube texture\n         * It must define where the camera used to render the texture was set\n         */\n        this.boundingBoxPosition = Vector3.Zero();\n        /**\n         * Observable triggered once the texture has been loaded.\n         */\n        this.onLoadObservable = new Observable();\n        if (!url) {\n            return;\n        }\n        this._coordinatesMode = Texture.CUBIC_MODE;\n        this.name = url;\n        this.url = url;\n        this.hasAlpha = false;\n        this.isCube = true;\n        this._textureMatrix = Matrix.Identity();\n        this._prefilterOnLoad = prefilterOnLoad;\n        this._prefilterIrradianceOnLoad = prefilterIrradianceOnLoad;\n        this._prefilterUsingCdf = prefilterUsingCdf;\n        this._onLoad = () => {\n            this.onLoadObservable.notifyObservers(this);\n            if (onLoad) {\n                onLoad();\n            }\n        };\n        this._onError = onError;\n        this.gammaSpace = gammaSpace;\n        this._noMipmap = noMipmap;\n        this._size = size;\n        // CDF is very sensitive to lost precision due to downsampling. This can result in\n        // noticeable brightness differences with different resolutions. Enabling supersampling\n        // mitigates this.\n        this._supersample = supersample || prefilterUsingCdf;\n        this._generateHarmonics = generateHarmonics;\n        this._texture = this._getFromCache(url, this._noMipmap, undefined, undefined, undefined, this.isCube);\n        if (!this._texture) {\n            if (!this.getScene()?.useDelayedTextureLoading) {\n                this._loadTexture();\n            }\n            else {\n                this.delayLoadState = 4;\n            }\n        }\n        else {\n            if (this._texture.isReady) {\n                Tools.SetImmediate(() => this._onLoad());\n            }\n            else {\n                this._texture.onLoadedObservable.add(this._onLoad);\n            }\n        }\n    }\n    /**\n     * Get the current class name of the texture useful for serialization or dynamic coding.\n     * @returns \"HDRCubeTexture\"\n     */\n    getClassName() {\n        return \"HDRCubeTexture\";\n    }\n    /**\n     * Occurs when the file is raw .hdr file.\n     */\n    _loadTexture() {\n        const engine = this._getEngine();\n        const caps = engine.getCaps();\n        let textureType = 0;\n        if (caps.textureFloat && caps.textureFloatLinearFiltering) {\n            textureType = 1;\n        }\n        else if (caps.textureHalfFloat && caps.textureHalfFloatLinearFiltering) {\n            textureType = 2;\n        }\n        const callback = (buffer) => {\n            this.lodGenerationOffset = 0.0;\n            this.lodGenerationScale = 0.8;\n            // Extract the raw linear data.\n            const data = GetCubeMapTextureData(buffer, this._size, this._supersample);\n            // Generate harmonics if needed.\n            if (this._generateHarmonics) {\n                const sphericalPolynomial = CubeMapToSphericalPolynomialTools.ConvertCubeMapToSphericalPolynomial(data);\n                this.sphericalPolynomial = sphericalPolynomial;\n            }\n            const results = [];\n            let byteArray = null;\n            let shortArray = null;\n            // Push each faces.\n            for (let j = 0; j < 6; j++) {\n                // Create fallback array\n                if (textureType === 2) {\n                    shortArray = new Uint16Array(this._size * this._size * 3);\n                }\n                else if (textureType === 0) {\n                    // 3 channels of 1 bytes per pixel in bytes.\n                    byteArray = new Uint8Array(this._size * this._size * 3);\n                }\n                const dataFace = data[HDRCubeTexture._FacesMapping[j]];\n                // If special cases.\n                if (this.gammaSpace || shortArray || byteArray) {\n                    for (let i = 0; i < this._size * this._size; i++) {\n                        // Put in gamma space if requested.\n                        if (this.gammaSpace) {\n                            dataFace[i * 3 + 0] = Math.pow(dataFace[i * 3 + 0], ToGammaSpace);\n                            dataFace[i * 3 + 1] = Math.pow(dataFace[i * 3 + 1], ToGammaSpace);\n                            dataFace[i * 3 + 2] = Math.pow(dataFace[i * 3 + 2], ToGammaSpace);\n                        }\n                        // Convert to half float texture for fallback.\n                        if (shortArray) {\n                            shortArray[i * 3 + 0] = ToHalfFloat(dataFace[i * 3 + 0]);\n                            shortArray[i * 3 + 1] = ToHalfFloat(dataFace[i * 3 + 1]);\n                            shortArray[i * 3 + 2] = ToHalfFloat(dataFace[i * 3 + 2]);\n                        }\n                        // Convert to int texture for fallback.\n                        if (byteArray) {\n                            let r = Math.max(dataFace[i * 3 + 0] * 255, 0);\n                            let g = Math.max(dataFace[i * 3 + 1] * 255, 0);\n                            let b = Math.max(dataFace[i * 3 + 2] * 255, 0);\n                            // May use luminance instead if the result is not accurate.\n                            const max = Math.max(Math.max(r, g), b);\n                            if (max > 255) {\n                                const scale = 255 / max;\n                                r *= scale;\n                                g *= scale;\n                                b *= scale;\n                            }\n                            byteArray[i * 3 + 0] = r;\n                            byteArray[i * 3 + 1] = g;\n                            byteArray[i * 3 + 2] = b;\n                        }\n                    }\n                }\n                if (shortArray) {\n                    results.push(shortArray);\n                }\n                else if (byteArray) {\n                    results.push(byteArray);\n                }\n                else {\n                    results.push(dataFace);\n                }\n            }\n            return results;\n        };\n        if (engine._features.allowTexturePrefiltering && (this._prefilterOnLoad || this._prefilterIrradianceOnLoad)) {\n            const previousOnLoad = this._onLoad;\n            const hdrFiltering = new HDRFiltering(engine);\n            this._onLoad = () => {\n                let irradiancePromise = Promise.resolve(null);\n                let radiancePromise = Promise.resolve();\n                if (this._prefilterIrradianceOnLoad) {\n                    const hdrIrradianceFiltering = new HDRIrradianceFiltering(engine, { useCdf: this._prefilterUsingCdf });\n                    irradiancePromise = hdrIrradianceFiltering.prefilter(this);\n                }\n                if (this._prefilterOnLoad) {\n                    radiancePromise = hdrFiltering.prefilter(this);\n                }\n                Promise.all([irradiancePromise, radiancePromise]).then((results) => {\n                    const irradianceTexture = results[0];\n                    if (this._prefilterIrradianceOnLoad && irradianceTexture) {\n                        this.irradianceTexture = irradianceTexture;\n                        const scene = this.getScene();\n                        if (scene) {\n                            scene.markAllMaterialsAsDirty(1);\n                        }\n                    }\n                    if (previousOnLoad) {\n                        previousOnLoad();\n                    }\n                });\n            };\n        }\n        this._texture = engine.createRawCubeTextureFromUrl(this.url, this.getScene(), this._size, 4, textureType, this._noMipmap, callback, null, this._onLoad, this._onError);\n    }\n    clone() {\n        const newTexture = new HDRCubeTexture(this.url, this.getScene() || this._getEngine(), this._size, this._noMipmap, this._generateHarmonics, this.gammaSpace);\n        // Base texture\n        newTexture.level = this.level;\n        newTexture.wrapU = this.wrapU;\n        newTexture.wrapV = this.wrapV;\n        newTexture.coordinatesIndex = this.coordinatesIndex;\n        newTexture.coordinatesMode = this.coordinatesMode;\n        return newTexture;\n    }\n    // Methods\n    delayLoad() {\n        if (this.delayLoadState !== 4) {\n            return;\n        }\n        this.delayLoadState = 1;\n        this._texture = this._getFromCache(this.url, this._noMipmap);\n        if (!this._texture) {\n            this._loadTexture();\n        }\n    }\n    /**\n     * Get the texture reflection matrix used to rotate/transform the reflection.\n     * @returns the reflection matrix\n     */\n    getReflectionTextureMatrix() {\n        return this._textureMatrix;\n    }\n    /**\n     * Set the texture reflection matrix used to rotate/transform the reflection.\n     * @param value Define the reflection matrix to set\n     */\n    setReflectionTextureMatrix(value) {\n        this._textureMatrix = value;\n        if (value.updateFlag === this._textureMatrix.updateFlag) {\n            return;\n        }\n        if (value.isIdentity() !== this._textureMatrix.isIdentity()) {\n            this.getScene()?.markAllMaterialsAsDirty(1, (mat) => mat.getActiveTextures().indexOf(this) !== -1);\n        }\n    }\n    /**\n     * Dispose the texture and release its associated resources.\n     */\n    dispose() {\n        this.onLoadObservable.clear();\n        super.dispose();\n    }\n    /**\n     * Parses a JSON representation of an HDR Texture in order to create the texture\n     * @param parsedTexture Define the JSON representation\n     * @param scene Define the scene the texture should be created in\n     * @param rootUrl Define the root url in case we need to load relative dependencies\n     * @returns the newly created texture after parsing\n     */\n    static Parse(parsedTexture, scene, rootUrl) {\n        let texture = null;\n        if (parsedTexture.name && !parsedTexture.isRenderTarget) {\n            texture = new HDRCubeTexture(rootUrl + parsedTexture.name, scene, parsedTexture.size, parsedTexture.noMipmap, parsedTexture.generateHarmonics, parsedTexture.useInGammaSpace);\n            texture.name = parsedTexture.name;\n            texture.hasAlpha = parsedTexture.hasAlpha;\n            texture.level = parsedTexture.level;\n            texture.coordinatesMode = parsedTexture.coordinatesMode;\n            texture.isBlocking = parsedTexture.isBlocking;\n        }\n        if (texture) {\n            if (parsedTexture.boundingBoxPosition) {\n                texture.boundingBoxPosition = Vector3.FromArray(parsedTexture.boundingBoxPosition);\n            }\n            if (parsedTexture.boundingBoxSize) {\n                texture.boundingBoxSize = Vector3.FromArray(parsedTexture.boundingBoxSize);\n            }\n            if (parsedTexture.rotationY) {\n                texture.rotationY = parsedTexture.rotationY;\n            }\n        }\n        return texture;\n    }\n    serialize() {\n        if (!this.name) {\n            return null;\n        }\n        const serializationObject = {};\n        serializationObject.name = this.name;\n        serializationObject.hasAlpha = this.hasAlpha;\n        serializationObject.isCube = true;\n        serializationObject.level = this.level;\n        serializationObject.size = this._size;\n        serializationObject.coordinatesMode = this.coordinatesMode;\n        serializationObject.useInGammaSpace = this.gammaSpace;\n        serializationObject.generateHarmonics = this._generateHarmonics;\n        serializationObject.customType = \"BABYLON.HDRCubeTexture\";\n        serializationObject.noMipmap = this._noMipmap;\n        serializationObject.isBlocking = this._isBlocking;\n        serializationObject.rotationY = this._rotationY;\n        return serializationObject;\n    }\n}\nHDRCubeTexture._FacesMapping = [\"right\", \"left\", \"up\", \"down\", \"front\", \"back\"];\nRegisterClass(\"BABYLON.HDRCubeTexture\", HDRCubeTexture);\n//# sourceMappingURL=hdrCubeTexture.js.map"], "file": "_app/immutable/chunks/hdrCubeTexture.DjwBvdn7.js"}