import{T as d,an as c,ao as n}from"./index.BoI39RQH.js";import{GLTFLoader as h}from"./glTFLoader.BetPWe9U.js";const o="KHR_texture_transform";class m{constructor(s){this.name=o,this._loader=s,this.enabled=this._loader.isExtensionUsed(o)}dispose(){this._loader=null}loadTextureInfoAsync(s,a,e){return h.LoadExtensionAsync(s,a,this.name,(i,r)=>this._loader.loadTextureInfoAsync(s,a,t=>{if(!(t instanceof d))throw new Error(`${i}: Texture type not supported`);r.offset&&(t.uOffset=r.offset[0],t.vOffset=r.offset[1]),t.uRotationCenter=0,t.vRotationCenter=0,r.rotation&&(t.wAng=-r.rotation),r.scale&&(t.uScale=r.scale[0],t.vScale=r.scale[1]),r.texCoord!=null&&(t.coordinatesIndex=r.texCoord),e(t)}))}}c(o);n(o,!0,f=>new m(f));export{m as KHR_texture_transform};
//# sourceMappingURL=KHR_texture_transform.DzMk5bDN.js.map
