#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API clamp_max {
  using schema = at::Tensor (const at::Tensor &, const at::Scalar &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::clamp_max";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "clamp_max(Tensor self, Scalar max) -> Tensor";
  static at::Tensor call(const at::Tensor & self, const at::Scalar & max);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Scalar & max);
};

struct TORCH_API clamp_max_Tensor {
  using schema = at::Tensor (const at::Tensor &, const at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::clamp_max";
  static constexpr const char* overload_name = "Tensor";
  static constexpr const char* schema_str = "clamp_max.Tensor(Tensor self, Tensor max) -> Tensor";
  static at::Tensor call(const at::Tensor & self, const at::Tensor & max);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & max);
};

struct TORCH_API clamp_max_ {
  using schema = at::Tensor & (at::Tensor &, const at::Scalar &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::clamp_max_";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "clamp_max_(Tensor(a!) self, Scalar max) -> Tensor(a!)";
  static at::Tensor & call(at::Tensor & self, const at::Scalar & max);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, at::Tensor & self, const at::Scalar & max);
};

struct TORCH_API clamp_max__Tensor {
  using schema = at::Tensor & (at::Tensor &, const at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::clamp_max_";
  static constexpr const char* overload_name = "Tensor";
  static constexpr const char* schema_str = "clamp_max_.Tensor(Tensor(a!) self, Tensor max) -> Tensor(a!)";
  static at::Tensor & call(at::Tensor & self, const at::Tensor & max);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, at::Tensor & self, const at::Tensor & max);
};

struct TORCH_API clamp_max_out {
  using schema = at::Tensor & (const at::Tensor &, const at::Scalar &, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::clamp_max";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "clamp_max.out(Tensor self, Scalar max, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, const at::Scalar & max, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Scalar & max, at::Tensor & out);
};

struct TORCH_API clamp_max_Tensor_out {
  using schema = at::Tensor & (const at::Tensor &, const at::Tensor &, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::clamp_max";
  static constexpr const char* overload_name = "Tensor_out";
  static constexpr const char* schema_str = "clamp_max.Tensor_out(Tensor self, Tensor max, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, const at::Tensor & max, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & max, at::Tensor & out);
};

}} // namespace at::_ops
