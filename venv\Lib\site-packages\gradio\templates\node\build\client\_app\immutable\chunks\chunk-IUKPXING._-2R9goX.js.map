{"version": 3, "file": "chunk-IUKPXING._-2R9goX.js", "sources": ["../../../../../../../../node_modules/.pnpm/mermaid@11.5.0/node_modules/mermaid/dist/chunks/mermaid.core/chunk-IUKPXING.mjs"], "sourcesContent": ["import {\n  __name\n} from \"./chunk-O7R7247Q.mjs\";\n\n// src/diagrams/common/populateCommonDb.ts\nfunction populateCommonDb(ast, db) {\n  if (ast.accDescr) {\n    db.setAccDescription?.(ast.accDescr);\n  }\n  if (ast.accTitle) {\n    db.setAccTitle?.(ast.accTitle);\n  }\n  if (ast.title) {\n    db.setDiagramTitle?.(ast.title);\n  }\n}\n__name(populateCommonDb, \"populateCommonDb\");\n\nexport {\n  populateCommonDb\n};\n"], "names": ["populateCommonDb", "ast", "db", "_a", "_b", "_c", "__name"], "mappings": "+CAKA,SAASA,EAAiBC,EAAKC,EAAI,WAC7BD,EAAI,YACNE,EAAAD,EAAG,oBAAH,MAAAC,EAAA,KAAAD,EAAuBD,EAAI,WAEzBA,EAAI,YACNG,EAAAF,EAAG,cAAH,MAAAE,EAAA,KAAAF,EAAiBD,EAAI,WAEnBA,EAAI,SACNI,EAAAH,EAAG,kBAAH,MAAAG,EAAA,KAAAH,EAAqBD,EAAI,OAE7B,CACAK,EAAON,EAAkB,kBAAkB", "x_google_ignoreList": [0]}