import{h as Gn,i as re,a as I,w as vo,s as fi,$ as B,b as X,c as di,d as k,e as ce,l as $p,W as wp,f as Cp,m as Ir,g as pi,j as Np,p as Fp,v as kp,k as Gl,n as Tp,o as Ap,H as Op,q as Fs}from"./vega-tooltip.module.sYf3DH7j.js";const _p="vega-lite",Rp='<PERSON><PERSON><PERSON>, <PERSON><PERSON> "<PERSON>" <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>',Ip="5.17.0",Lp=["<PERSON><PERSON><PERSON> (http://kanitw.yellowpigz.com)","<PERSON><PERSON><PERSON> (https://www.domoritz.de)","<PERSON><PERSON><PERSON> (https://arvindsatya.com)","<PERSON> (https://jheer.org)"],Pp="https://vega.github.io/vega-lite/",zp="Vega-Lite is a concise high-level language for interactive visualization.",Dp=["vega","chart","visualization"],jp="build/vega-lite.js",Mp="build/vega-lite.min.js",Up="build/vega-lite.min.js",Bp="build/src/index",Wp="build/src/index.d.ts",Gp={vl2pdf:"./bin/vl2pdf",vl2png:"./bin/vl2png",vl2svg:"./bin/vl2svg",vl2vg:"./bin/vl2vg"},Hp=["bin","build","src","vega-lite*","tsconfig.json"],qp={changelog:"conventional-changelog -p angular -r 2",prebuild:"yarn clean:build",build:"yarn build:only","build:only":"tsc -p tsconfig.build.json && rollup -c","prebuild:examples":"yarn build:only","build:examples":"yarn data && TZ=America/Los_Angeles scripts/build-examples.sh","prebuild:examples-full":"yarn build:only","build:examples-full":"TZ=America/Los_Angeles scripts/build-examples.sh 1","build:example":"TZ=America/Los_Angeles scripts/build-example.sh","build:toc":"yarn build:jekyll && scripts/generate-toc","build:site":"rollup -c site/rollup.config.mjs","build:jekyll":"pushd site && bundle exec jekyll build -q && popd","build:versions":"scripts/update-version.sh",clean:"yarn clean:build && del-cli 'site/data/*' 'examples/compiled/*.png' && find site/examples ! -name 'index.md' ! -name 'data' -type f -delete","clean:build":"del-cli 'build/*' !build/vega-lite-schema.json",data:"rsync -r node_modules/vega-datasets/data/* site/data",schema:"mkdir -p build && ts-json-schema-generator -f tsconfig.json -p src/index.ts -t TopLevelSpec --no-type-check --no-ref-encode > build/vega-lite-schema.json && yarn renameschema && cp build/vega-lite-schema.json site/_data/",renameschema:"scripts/rename-schema.sh",presite:"yarn data && yarn schema && yarn build:site && yarn build:versions && scripts/create-example-pages.sh",site:"yarn site:only","site:only":"pushd site && bundle exec jekyll serve -I -l && popd",prettierbase:"prettier '**/*.{md,css,yml}'",format:"eslint . --fix && yarn prettierbase --write",lint:"eslint . && yarn prettierbase --check",test:"yarn jest test/ && yarn lint && yarn schema && yarn jest examples/ && yarn test:runtime","test:cover":"yarn jest --collectCoverage test/","test:inspect":"node --inspect-brk ./node_modules/.bin/jest --runInBand test","test:runtime":"TZ=America/Los_Angeles npx jest test-runtime/ --config test-runtime/jest-config.json","test:runtime:generate":"yarn build:only && del-cli test-runtime/resources && VL_GENERATE_TESTS=true yarn test:runtime",watch:"tsc -p tsconfig.build.json -w","watch:site":"yarn build:site -w","watch:test":"yarn jest --watch test/","watch:test:runtime":"TZ=America/Los_Angeles npx jest --watch test-runtime/ --config test-runtime/jest-config.json",release:"release-it"},Vp={type:"git",url:"https://github.com/vega/vega-lite.git"},Xp="BSD-3-Clause",Yp={url:"https://github.com/vega/vega-lite/issues"},Kp={"@babel/core":"^7.23.9","@babel/preset-env":"^7.23.9","@babel/preset-typescript":"^7.23.3","@release-it/conventional-changelog":"^8.0.1","@rollup/plugin-alias":"^5.1.0","@rollup/plugin-babel":"^6.0.4","@rollup/plugin-commonjs":"^25.0.7","@rollup/plugin-json":"^6.1.0","@rollup/plugin-node-resolve":"^15.2.3","@rollup/plugin-terser":"^0.4.4","@types/d3":"^7.4.3","@types/jest":"^29.5.11","@types/pako":"^2.0.3","@typescript-eslint/eslint-plugin":"^6.20.0","@typescript-eslint/parser":"^6.20.0",ajv:"^8.12.0","ajv-formats":"^2.1.1",cheerio:"^1.0.0-rc.12","conventional-changelog-cli":"^4.1.0",d3:"^7.8.5","del-cli":"^5.1.0",eslint:"^8.56.0","eslint-config-prettier":"^9.1.0","eslint-plugin-jest":"^27.6.3","eslint-plugin-prettier":"^5.1.3","fast-json-stable-stringify":"~2.1.0","highlight.js":"^11.9.0",jest:"^29.7.0","jest-dev-server":"^9.0.2",mkdirp:"^3.0.1",pako:"^2.1.0",prettier:"^3.2.4",puppeteer:"^15.0.0","release-it":"^17.0.3",rollup:"^4.9.6","rollup-plugin-bundle-size":"^1.0.3",serve:"^14.2.1",terser:"^5.27.0","ts-jest":"^29.1.2","ts-json-schema-generator":"^1.5.0",typescript:"~5.3.3","vega-cli":"^5.27.0","vega-datasets":"^2.8.0","vega-embed":"^6.24.0","vega-tooltip":"^0.34.0","yaml-front-matter":"^4.1.1"},Qp={"json-stringify-pretty-compact":"~3.0.0",tslib:"~2.6.2","vega-event-selector":"~3.0.1","vega-expression":"~5.1.0","vega-util":"~1.17.2",yargs:"~17.7.2"},Jp={vega:"^5.24.0"},Zp={node:">=18"},eg={name:_p,author:Rp,version:Ip,collaborators:Lp,homepage:Pp,description:zp,keywords:Dp,main:jp,unpkg:Mp,jsdelivr:Up,module:Bp,types:Wp,bin:Gp,files:Hp,scripts:qp,repository:Vp,license:Xp,bugs:Yp,devDependencies:Kp,dependencies:Qp,peerDependencies:Jp,engines:Zp};function So(e){return!!e.or}function Eo(e){return!!e.and}function $o(e){return!!e.not}function hr(e,t){if($o(e))hr(e.not,t);else if(Eo(e))for(const n of e.and)hr(n,t);else if(So(e))for(const n of e.or)hr(n,t);else t(e)}function Hn(e,t){return $o(e)?{not:Hn(e.not,t)}:Eo(e)?{and:e.and.map(n=>Hn(n,t))}:So(e)?{or:e.or.map(n=>Hn(n,t))}:t(e)}const P=structuredClone;function Hl(e){throw new Error(e)}function Qn(e,t){const n={};for(const i of t)Gn(e,i)&&(n[i]=e[i]);return n}function Te(e,t){const n={...e};for(const i of t)delete n[i];return n}Set.prototype.toJSON=function(){return`Set(${[...this].map(e=>J(e)).join(",")})`};function W(e){if(re(e))return e;const t=I(e)?e:J(e);if(t.length<250)return t;let n=0;for(let i=0;i<t.length;i++){const r=t.charCodeAt(i);n=(n<<5)-n+r,n=n&n}return n}function Ys(e){return e===!1||e===null}function G(e,t){return e.includes(t)}function vn(e,t){let n=0;for(const[i,r]of e.entries())if(t(r,i,n++))return!0;return!1}function wo(e,t){let n=0;for(const[i,r]of e.entries())if(!t(r,i,n++))return!1;return!0}function ql(e,...t){for(const n of t)tg(e,n??{});return e}function tg(e,t){for(const n of x(t))vo(e,n,t[n],!0)}function ut(e,t){const n=[],i={};let r;for(const s of e)r=t(s),!(r in i)&&(i[r]=1,n.push(s));return n}function ng(e,t){const n=x(e),i=x(t);if(n.length!==i.length)return!1;for(const r of n)if(e[r]!==t[r])return!1;return!0}function Vl(e,t){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}function Co(e,t){for(const n of e)if(t.has(n))return!0;return!1}function Ks(e){const t=new Set;for(const n of e){const r=fi(n).map((o,a)=>a===0?o:`[${o}]`),s=r.map((o,a)=>r.slice(0,a+1).join(""));for(const o of s)t.add(o)}return t}function No(e,t){return e===void 0||t===void 0?!0:Co(Ks(e),Ks(t))}function Q(e){return x(e).length===0}const x=Object.keys,ve=Object.values,Gt=Object.entries;function Li(e){return e===!0||e===!1}function se(e){const t=e.replace(/\W/g,"_");return(e.match(/^\d+/)?"_":"")+t}function Oi(e,t){return $o(e)?`!(${Oi(e.not,t)})`:Eo(e)?`(${e.and.map(n=>Oi(n,t)).join(") && (")})`:So(e)?`(${e.or.map(n=>Oi(n,t)).join(") || (")})`:t(e)}function mr(e,t){if(t.length===0)return!0;const n=t.shift();return n in e&&mr(e[n],t)&&delete e[n],Q(e)}function Wi(e){return e.charAt(0).toUpperCase()+e.substr(1)}function Fo(e,t="datum"){const n=fi(e),i=[];for(let r=1;r<=n.length;r++){const s=`[${n.slice(0,r).map(B).join("][")}]`;i.push(`${t}${s}`)}return i.join(" && ")}function Xl(e,t="datum"){return`${t}[${B(fi(e).join("."))}]`}function ig(e){return e.replace(/(\[|\]|\.|'|")/g,"\\$1")}function Ue(e){return`${fi(e).map(ig).join("\\.")}`}function Sn(e,t,n){return e.replace(new RegExp(t.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&"),"g"),n)}function ko(e){return`${fi(e).join(".")}`}function Jn(e){return e?fi(e).length:0}function le(...e){for(const t of e)if(t!==void 0)return t}let Yl=42;function Kl(e){const t=++Yl;return e?String(e)+t:t}function rg(){Yl=42}function Ql(e){return Jl(e)?e:`__${e}`}function Jl(e){return e.startsWith("__")}function Pi(e){if(e!==void 0)return(e%360+360)%360}function Lr(e){return re(e)?!0:!isNaN(e)&&!isNaN(parseFloat(e))}const nc=Object.getPrototypeOf(structuredClone({}));function Pe(e,t){if(e===t)return!0;if(e&&t&&typeof e=="object"&&typeof t=="object"){if(e.constructor.name!==t.constructor.name)return!1;let n,i;if(Array.isArray(e)){if(n=e.length,n!=t.length)return!1;for(i=n;i--!==0;)if(!Pe(e[i],t[i]))return!1;return!0}if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(i of e.entries())if(!t.has(i[0]))return!1;for(i of e.entries())if(!Pe(i[1],t.get(i[0])))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(i of e.entries())if(!t.has(i[0]))return!1;return!0}if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(t)){if(n=e.length,n!=t.length)return!1;for(i=n;i--!==0;)if(e[i]!==t[i])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf&&e.valueOf!==nc.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString&&e.toString!==nc.toString)return e.toString()===t.toString();const r=Object.keys(e);if(n=r.length,n!==Object.keys(t).length)return!1;for(i=n;i--!==0;)if(!Object.prototype.hasOwnProperty.call(t,r[i]))return!1;for(i=n;i--!==0;){const s=r[i];if(!Pe(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function J(e){const t=[];return function n(i){if(i&&i.toJSON&&typeof i.toJSON=="function"&&(i=i.toJSON()),i===void 0)return;if(typeof i=="number")return isFinite(i)?""+i:"null";if(typeof i!="object")return JSON.stringify(i);let r,s;if(Array.isArray(i)){for(s="[",r=0;r<i.length;r++)r&&(s+=","),s+=n(i[r])||"null";return s+"]"}if(i===null)return"null";if(t.includes(i))throw new TypeError("Converting circular structure to JSON");const o=t.push(i)-1,a=Object.keys(i).sort();for(s="",r=0;r<a.length;r++){const c=a[r],l=n(i[c]);l&&(s&&(s+=","),s+=JSON.stringify(c)+":"+l)}return t.splice(o,1),`{${s}}`}(e)}const Ft="row",kt="column",Pr="facet",oe="x",be="y",et="x2",yt="y2",Qt="xOffset",gi="yOffset",tt="radius",Rt="radius2",We="theta",It="theta2",nt="latitude",it="longitude",rt="latitude2",Be="longitude2",Oe="color",bt="fill",xt="stroke",_e="shape",Lt="size",On="angle",Pt="opacity",Jt="fillOpacity",Zt="strokeOpacity",en="strokeWidth",tn="strokeDash",Gi="text",Zn="order",Hi="detail",zr="key",En="tooltip",Dr="href",jr="url",Mr="description",sg={x:1,y:1,x2:1,y2:1},Zl={theta:1,theta2:1,radius:1,radius2:1};function eu(e){return e in Zl}const To={longitude:1,longitude2:1,latitude:1,latitude2:1};function tu(e){switch(e){case nt:return"y";case rt:return"y2";case it:return"x";case Be:return"x2"}}function nu(e){return e in To}const og=x(To),Ao={...sg,...Zl,...To,xOffset:1,yOffset:1,color:1,fill:1,stroke:1,opacity:1,fillOpacity:1,strokeOpacity:1,strokeWidth:1,strokeDash:1,size:1,angle:1,shape:1,order:1,text:1,detail:1,key:1,tooltip:1,href:1,url:1,description:1};function qn(e){return e===Oe||e===bt||e===xt}const iu={row:1,column:1,facet:1},Me=x(iu),Oo={...Ao,...iu},ag=x(Oo),{order:lw,detail:uw,tooltip:fw,...cg}=Oo,{row:dw,column:pw,facet:gw,...lg}=cg;function ug(e){return!!lg[e]}function ru(e){return!!Oo[e]}const fg=[et,yt,rt,Be,It,Rt];function su(e){return _n(e)!==e}function _n(e){switch(e){case et:return oe;case yt:return be;case rt:return nt;case Be:return it;case It:return We;case Rt:return tt}return e}function Ht(e){if(eu(e))switch(e){case We:return"startAngle";case It:return"endAngle";case tt:return"outerRadius";case Rt:return"innerRadius"}return e}function vt(e){switch(e){case oe:return et;case be:return yt;case nt:return rt;case it:return Be;case We:return It;case tt:return Rt}}function Re(e){switch(e){case oe:case et:return"width";case be:case yt:return"height"}}function ou(e){switch(e){case oe:return"xOffset";case be:return"yOffset";case et:return"x2Offset";case yt:return"y2Offset";case We:return"thetaOffset";case tt:return"radiusOffset";case It:return"theta2Offset";case Rt:return"radius2Offset"}}function _o(e){switch(e){case oe:return"xOffset";case be:return"yOffset"}}function dg(e){switch(e){case"xOffset":return"x";case"yOffset":return"y"}}const pg=x(Ao),{x:hw,y:mw,x2:yw,y2:bw,xOffset:xw,yOffset:vw,latitude:Sw,longitude:Ew,latitude2:$w,longitude2:ww,theta:Cw,theta2:Nw,radius:Fw,radius2:kw,...Ro}=Ao,gg=x(Ro),Io={x:1,y:1},St=x(Io);function ue(e){return e in Io}const Lo={theta:1,radius:1},hg=x(Lo);function Ur(e){return e==="width"?oe:be}const au={xOffset:1,yOffset:1};function qi(e){return e in au}const{text:Tw,tooltip:Aw,href:Ow,url:_w,description:Rw,detail:Iw,key:Lw,order:Pw,...cu}=Ro,mg=x(cu);function yg(e){return!!Ro[e]}function bg(e){switch(e){case Oe:case bt:case xt:case Lt:case _e:case Pt:case en:case tn:return!0;case Jt:case Zt:case On:return!1}}const lu={...Io,...Lo,...au,...cu},Br=x(lu);function zt(e){return!!lu[e]}function xg(e,t){return Sg(e)[t]}const uu={arc:"always",area:"always",bar:"always",circle:"always",geoshape:"always",image:"always",line:"always",rule:"always",point:"always",rect:"always",square:"always",trail:"always",text:"always",tick:"always"},{geoshape:zw,...vg}=uu;function Sg(e){switch(e){case Oe:case bt:case xt:case Mr:case Hi:case zr:case En:case Dr:case Zn:case Pt:case Jt:case Zt:case en:case Pr:case Ft:case kt:return uu;case oe:case be:case Qt:case gi:case nt:case it:return vg;case et:case yt:case rt:case Be:return{area:"always",bar:"always",image:"always",rect:"always",rule:"always",circle:"binned",point:"binned",square:"binned",tick:"binned",line:"binned",trail:"binned"};case Lt:return{point:"always",tick:"always",rule:"always",circle:"always",square:"always",bar:"always",text:"always",line:"always",trail:"always"};case tn:return{line:"always",point:"always",tick:"always",rule:"always",circle:"always",square:"always",bar:"always",geoshape:"always"};case _e:return{point:"always",geoshape:"always"};case Gi:return{text:"always"};case On:return{point:"always",square:"always",text:"always"};case jr:return{image:"always"};case We:return{text:"always",arc:"always"};case tt:return{text:"always",arc:"always"};case It:case Rt:return{arc:"always"}}}function ks(e){switch(e){case oe:case be:case We:case tt:case Qt:case gi:case Lt:case On:case en:case Pt:case Jt:case Zt:case et:case yt:case It:case Rt:return;case Pr:case Ft:case kt:case _e:case tn:case Gi:case En:case Dr:case jr:case Mr:return"discrete";case Oe:case bt:case xt:return"flexible";case nt:case it:case rt:case Be:case Hi:case zr:case Zn:return}}const Eg={argmax:1,argmin:1,average:1,count:1,distinct:1,exponential:1,exponentialb:1,product:1,max:1,mean:1,median:1,min:1,missing:1,q1:1,q3:1,ci0:1,ci1:1,stderr:1,stdev:1,stdevp:1,sum:1,valid:1,values:1,variance:1,variancep:1},$g={count:1,min:1,max:1};function Ot(e){return!!e&&!!e.argmin}function nn(e){return!!e&&!!e.argmax}function Po(e){return I(e)&&!!Eg[e]}const wg=new Set(["count","valid","missing","distinct"]);function fu(e){return I(e)&&wg.has(e)}function Cg(e){return I(e)&&G(["min","max"],e)}const Ng=new Set(["count","sum","distinct","valid","missing"]),Fg=new Set(["mean","average","median","q1","q3","min","max"]);function du(e){return di(e)&&(e=es(e,void 0)),"bin"+x(e).map(t=>Wr(e[t])?se(`_${t}_${Gt(e[t])}`):se(`_${t}_${e[t]}`)).join("")}function te(e){return e===!0||Rn(e)&&!e.binned}function xe(e){return e==="binned"||Rn(e)&&e.binned===!0}function Rn(e){return X(e)}function Wr(e){return e==null?void 0:e.param}function ic(e){switch(e){case Ft:case kt:case Lt:case Oe:case bt:case xt:case en:case Pt:case Jt:case Zt:case _e:return 6;case tn:return 4;default:return 10}}function Vi(e){return!!(e!=null&&e.expr)}function ke(e){const t=x(e||{}),n={};for(const i of t)n[i]=Le(e[i]);return n}function pu(e){const{anchor:t,frame:n,offset:i,orient:r,angle:s,limit:o,color:a,subtitleColor:c,subtitleFont:l,subtitleFontSize:u,subtitleFontStyle:f,subtitleFontWeight:d,subtitleLineHeight:g,subtitlePadding:p,...h}=e,m={...h,...a?{fill:a}:{}},y={...t?{anchor:t}:{},...n?{frame:n}:{},...i?{offset:i}:{},...r?{orient:r}:{},...s!==void 0?{angle:s}:{},...o!==void 0?{limit:o}:{}},b={...c?{subtitleColor:c}:{},...l?{subtitleFont:l}:{},...u?{subtitleFontSize:u}:{},...f?{subtitleFontStyle:f}:{},...d?{subtitleFontWeight:d}:{},...g?{subtitleLineHeight:g}:{},...p?{subtitlePadding:p}:{}},C=Qn(e,["align","baseline","dx","dy","limit"]);return{titleMarkConfig:m,subtitleMarkConfig:C,nonMarkTitleProperties:y,subtitle:b}}function Ut(e){return I(e)||k(e)&&I(e[0])}function T(e){return!!(e!=null&&e.signal)}function rn(e){return!!e.step}function kg(e){return k(e)?!1:"fields"in e&&!("data"in e)}function Tg(e){return k(e)?!1:"fields"in e&&"data"in e}function Nt(e){return k(e)?!1:"field"in e&&"data"in e}const Ag={aria:1,description:1,ariaRole:1,ariaRoleDescription:1,blend:1,opacity:1,fill:1,fillOpacity:1,stroke:1,strokeCap:1,strokeWidth:1,strokeOpacity:1,strokeDash:1,strokeDashOffset:1,strokeJoin:1,strokeOffset:1,strokeMiterLimit:1,startAngle:1,endAngle:1,padAngle:1,innerRadius:1,outerRadius:1,size:1,shape:1,interpolate:1,tension:1,orient:1,align:1,baseline:1,text:1,dir:1,dx:1,dy:1,ellipsis:1,limit:1,radius:1,theta:1,angle:1,font:1,fontSize:1,fontWeight:1,fontStyle:1,lineBreak:1,lineHeight:1,cursor:1,href:1,tooltip:1,cornerRadius:1,cornerRadiusTopLeft:1,cornerRadiusTopRight:1,cornerRadiusBottomLeft:1,cornerRadiusBottomRight:1,aspect:1,width:1,height:1,url:1,smooth:1},Og=x(Ag),_g={arc:1,area:1,group:1,image:1,line:1,path:1,rect:1,rule:1,shape:1,symbol:1,text:1,trail:1},Qs=["cornerRadius","cornerRadiusTopLeft","cornerRadiusTopRight","cornerRadiusBottomLeft","cornerRadiusBottomRight"];function gu(e){const t=k(e.condition)?e.condition.map(rc):rc(e.condition);return{...Le(e),condition:t}}function Le(e){if(Vi(e)){const{expr:t,...n}=e;return{signal:t,...n}}return e}function rc(e){if(Vi(e)){const{expr:t,...n}=e;return{signal:t,...n}}return e}function ie(e){if(Vi(e)){const{expr:t,...n}=e;return{signal:t,...n}}return T(e)?e:e!==void 0?{value:e}:void 0}function Rg(e){return T(e)?e.signal:B(e)}function sc(e){return T(e)?e.signal:B(e.value)}function Xe(e){return T(e)?e.signal:e==null?null:B(e)}function Ig(e,t,n){for(const i of n){const r=_t(i,t.markDef,t.config);r!==void 0&&(e[i]=ie(r))}return e}function hu(e){return[].concat(e.type,e.style??[])}function V(e,t,n,i={}){const{vgChannel:r,ignoreVgConfig:s}=i;return r&&t[r]!==void 0?t[r]:t[e]!==void 0?t[e]:s&&(!r||r===e)?void 0:_t(e,t,n,i)}function _t(e,t,n,{vgChannel:i}={}){return le(i?yr(e,t,n.style):void 0,yr(e,t,n.style),i?n[t.type][i]:void 0,n[t.type][e],i?n.mark[i]:n.mark[e])}function yr(e,t,n){return mu(e,hu(t),n)}function mu(e,t,n){t=ce(t);let i;for(const r of t){const s=n[r];s&&s[e]!==void 0&&(i=s[e])}return i}function yu(e,t){return ce(e).reduce((n,i)=>(n.field.push(E(i,t)),n.order.push(i.sort??"ascending"),n),{field:[],order:[]})}function bu(e,t){const n=[...e];return t.forEach(i=>{for(const r of n)if(Pe(r,i))return;n.push(i)}),n}function xu(e,t){return Pe(e,t)||!t?e:e?[...ce(e),...ce(t)].join(", "):t}function vu(e,t){const n=e.value,i=t.value;if(n==null||i===null)return{explicit:e.explicit,value:null};if((Ut(n)||T(n))&&(Ut(i)||T(i)))return{explicit:e.explicit,value:xu(n,i)};if(Ut(n)||T(n))return{explicit:e.explicit,value:n};if(Ut(i)||T(i))return{explicit:e.explicit,value:i};if(!Ut(n)&&!T(n)&&!Ut(i)&&!T(i))return{explicit:e.explicit,value:bu(n,i)};throw new Error("It should never reach here")}function zo(e){return`Invalid specification ${J(e)}. Make sure the specification includes at least one of the following properties: "mark", "layer", "facet", "hconcat", "vconcat", "concat", or "repeat".`}const Lg='Autosize "fit" only works for single views and layered views.';function oc(e){return`${e=="width"?"Width":"Height"} "container" only works for single views and layered views.`}function ac(e){const t=e=="width"?"Width":"Height",n=e=="width"?"x":"y";return`${t} "container" only works well with autosize "fit" or "fit-${n}".`}function cc(e){return e?`Dropping "fit-${e}" because spec has discrete ${Re(e)}.`:'Dropping "fit" because spec has discrete size.'}function Do(e){return`Unknown field for ${e}. Cannot calculate view size.`}function lc(e){return`Cannot project a selection on encoding channel "${e}", which has no field.`}function Pg(e,t){return`Cannot project a selection on encoding channel "${e}" as it uses an aggregate function ("${t}").`}function zg(e){return`The "nearest" transform is not supported for ${e} marks.`}function Su(e){return`Selection not supported for ${e} yet.`}function Dg(e){return`Cannot find a selection named "${e}".`}const jg="Scale bindings are currently only supported for scales with unbinned, continuous domains.",Mg="Sequntial scales are deprecated. The available quantitative scale type values are linear, log, pow, sqrt, symlog, time and utc",Ug="Legend bindings are only supported for selections over an individual field or encoding channel.";function Bg(e){return`Lookups can only be performed on selection parameters. "${e}" is a variable parameter.`}function Wg(e){return`Cannot define and lookup the "${e}" selection in the same view. Try moving the lookup into a second, layered view?`}const Gg="The same selection must be used to override scale domains in a layered view.",Hg='Interval selections should be initialized using "x", "y", "longitude", or "latitude" keys.';function qg(e){return`Unknown repeated value "${e}".`}function uc(e){return`The "columns" property cannot be used when "${e}" has nested row/column.`}const Vg="Axes cannot be shared in concatenated or repeated views yet (https://github.com/vega/vega-lite/issues/2415).";function Xg(e){return`Unrecognized parse "${e}".`}function fc(e,t,n){return`An ancestor parsed field "${e}" as ${n} but a child wants to parse the field as ${t}.`}const Yg="Attempt to add the same child twice.";function Kg(e){return`Ignoring an invalid transform: ${J(e)}.`}const Qg='If "from.fields" is not specified, "as" has to be a string that specifies the key to be used for the data from the secondary source.';function dc(e){return`Config.customFormatTypes is not true, thus custom format type and format for channel ${e} are dropped.`}function Jg(e){const{parentProjection:t,projection:n}=e;return`Layer's shared projection ${J(t)} is overridden by a child projection ${J(n)}.`}const Zg="Arc marks uses theta channel rather than angle, replacing angle with theta.";function eh(e){return`${e}Offset dropped because ${e} is continuous`}function th(e,t,n){return`Channel ${e} is a ${t}. Converted to {value: ${J(n)}}.`}function Eu(e){return`Invalid field type "${e}".`}function nh(e,t){return`Invalid field type "${e}" for aggregate: "${t}", using "quantitative" instead.`}function ih(e){return`Invalid aggregation operator "${e}".`}function $u(e,t){const{fill:n,stroke:i}=t;return`Dropping color ${e} as the plot also has ${n&&i?"fill and stroke":n?"fill":"stroke"}.`}function rh(e){return`Position range does not support relative band size for ${e}.`}function Js(e,t){return`Dropping ${J(e)} from channel "${t}" since it does not contain any data field, datum, value, or signal.`}const sh="Line marks cannot encode size with a non-groupby field. You may want to use trail marks instead.";function Gr(e,t,n){return`${e} dropped as it is incompatible with "${t}".`}function oh(e){return`${e}-encoding is dropped as ${e} is not a valid encoding channel.`}function ah(e){return`${e} encoding should be discrete (ordinal / nominal / binned).`}function ch(e){return`${e} encoding should be discrete (ordinal / nominal / binned) or use a discretizing scale (e.g. threshold).`}function lh(e){return`Facet encoding dropped as ${e.join(" and ")} ${e.length>1?"are":"is"} also specified.`}function Ts(e,t){return`Using discrete channel "${e}" to encode "${t}" field can be misleading as it does not encode ${t==="ordinal"?"order":"magnitude"}.`}function uh(e){return`The ${e} for range marks cannot be an expression`}function fh(e,t){return`Line mark is for continuous lines and thus cannot be used with ${e&&t?"x2 and y2":e?"x2":"y2"}. We will use the rule mark (line segments) instead.`}function dh(e,t){return`Specified orient "${e}" overridden with "${t}".`}function ph(e){return`Cannot use the scale property "${e}" with non-color channel.`}function gh(e){return`Cannot use the relative band size with ${e} scale.`}function hh(e){return`Using unaggregated domain with raw field has no effect (${J(e)}).`}function mh(e){return`Unaggregated domain not applicable for "${e}" since it produces values outside the origin domain of the source data.`}function yh(e){return`Unaggregated domain is currently unsupported for log scale (${J(e)}).`}function bh(e){return`Cannot apply size to non-oriented mark "${e}".`}function xh(e,t,n){return`Channel "${e}" does not work with "${t}" scale. We are using "${n}" scale instead.`}function vh(e,t){return`FieldDef does not work with "${e}" scale. We are using "${t}" scale instead.`}function wu(e,t,n){return`${n}-scale's "${t}" is dropped as it does not work with ${e} scale.`}function Cu(e){return`The step for "${e}" is dropped because the ${e==="width"?"x":"y"} is continuous.`}function Sh(e,t,n,i){return`Conflicting ${t.toString()} property "${e.toString()}" (${J(n)} and ${J(i)}). Using ${J(n)}.`}function Eh(e,t,n,i){return`Conflicting ${t.toString()} property "${e.toString()}" (${J(n)} and ${J(i)}). Using the union of the two domains.`}function $h(e){return`Setting the scale to be independent for "${e}" means we also have to set the guide (axis or legend) to be independent.`}function wh(e){return`Dropping sort property ${J(e)} as unioned domains only support boolean or op "count", "min", and "max".`}const pc="Domains that should be unioned has conflicting sort properties. Sort will be set to true.",Ch="Detected faceted independent scales that union domain of multiple fields from different data sources. We will use the first field. The result view size may be incorrect.",Nh="Detected faceted independent scales that union domain of the same fields from different source. We will assume that this is the same field from a different fork of the same data source. However, if this is not the case, the result view size may be incorrect.",Fh="Detected faceted independent scales that union domain of multiple fields from the same data source. We will use the first field. The result view size may be incorrect.";function kh(e){return`Cannot stack "${e}" if there is already "${e}2".`}function Th(e){return`Cannot stack non-linear scale (${e}).`}function Ah(e){return`Stacking is applied even though the aggregate function is non-summative ("${e}").`}function br(e,t){return`Invalid ${e}: ${J(t)}.`}function Oh(e){return`Dropping day from datetime ${J(e)} as day cannot be combined with other units.`}function _h(e,t){return`${t?"extent ":""}${t&&e?"and ":""}${e?"center ":""}${t&&e?"are ":"is "}not needed when data are aggregated.`}function Rh(e,t,n){return`${e} is not usually used with ${t} for ${n}.`}function Ih(e,t){return`Continuous axis should not have customized aggregation function ${e}; ${t} already agregates the axis.`}function gc(e){return`1D error band does not support ${e}.`}function Nu(e){return`Channel ${e} is required for "binned" bin.`}function Lh(e){return`Channel ${e} should not be used with "binned" bin.`}function Ph(e){return`Domain for ${e} is required for threshold scale.`}const Fu=$p(wp);let ei=Fu;function zh(e){return ei=e,ei}function Dh(){return ei=Fu,ei}function v(...e){ei.warn(...e)}function jh(...e){ei.debug(...e)}function In(e){if(e&&X(e)){for(const t of Mo)if(t in e)return!0}return!1}const ku=["january","february","march","april","may","june","july","august","september","october","november","december"],Mh=ku.map(e=>e.substr(0,3)),Tu=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"],Uh=Tu.map(e=>e.substr(0,3));function Bh(e){if(Lr(e)&&(e=+e),re(e))return e>4&&v(br("quarter",e)),e-1;throw new Error(br("quarter",e))}function Wh(e){if(Lr(e)&&(e=+e),re(e))return e-1;{const t=e.toLowerCase(),n=ku.indexOf(t);if(n!==-1)return n;const i=t.substr(0,3),r=Mh.indexOf(i);if(r!==-1)return r;throw new Error(br("month",e))}}function Gh(e){if(Lr(e)&&(e=+e),re(e))return e%7;{const t=e.toLowerCase(),n=Tu.indexOf(t);if(n!==-1)return n;const i=t.substr(0,3),r=Uh.indexOf(i);if(r!==-1)return r;throw new Error(br("day",e))}}function jo(e,t){const n=[];if(t&&e.day!==void 0&&x(e).length>1&&(v(Oh(e)),e=P(e),delete e.day),e.year!==void 0?n.push(e.year):n.push(2012),e.month!==void 0){const i=t?Wh(e.month):e.month;n.push(i)}else if(e.quarter!==void 0){const i=t?Bh(e.quarter):e.quarter;n.push(re(i)?i*3:`${i}*3`)}else n.push(0);if(e.date!==void 0)n.push(e.date);else if(e.day!==void 0){const i=t?Gh(e.day):e.day;n.push(re(i)?i+1:`${i}+1`)}else n.push(1);for(const i of["hours","minutes","seconds","milliseconds"]){const r=e[i];n.push(typeof r>"u"?0:r)}return n}function $n(e){const n=jo(e,!0).join(", ");return e.utc?`utc(${n})`:`datetime(${n})`}function Hh(e){const n=jo(e,!1).join(", ");return e.utc?`utc(${n})`:`datetime(${n})`}function qh(e){const t=jo(e,!0);return e.utc?+new Date(Date.UTC(...t)):+new Date(...t)}const Au={year:1,quarter:1,month:1,week:1,day:1,dayofyear:1,date:1,hours:1,minutes:1,seconds:1,milliseconds:1},Mo=x(Au);function Vh(e){return!!Au[e]}function Ln(e){return X(e)?e.binned:Ou(e)}function Ou(e){return e&&e.startsWith("binned")}function Uo(e){return e.startsWith("utc")}function Xh(e){return e.substring(3)}const Yh={"year-month":"%b %Y ","year-month-date":"%b %d, %Y "};function Hr(e){return Mo.filter(t=>Ru(e,t))}function _u(e){const t=Hr(e);return t[t.length-1]}function Ru(e,t){const n=e.indexOf(t);return!(n<0||n>0&&t==="seconds"&&e.charAt(n-1)==="i"||e.length>n+3&&t==="day"&&e.charAt(n+3)==="o"||n>0&&t==="year"&&e.charAt(n-1)==="f")}function Kh(e,t,{end:n}={end:!1}){const i=Fo(t),r=Uo(e)?"utc":"";function s(c){return c==="quarter"?`(${r}quarter(${i})-1)`:`${r}${c}(${i})`}let o;const a={};for(const c of Mo)Ru(e,c)&&(a[c]=s(c),o=c);return n&&(a[o]+="+1"),Hh(a)}function Iu(e){if(!e)return;const t=Hr(e);return`timeUnitSpecifier(${J(t)}, ${J(Yh)})`}function Qh(e,t,n){if(!e)return;const i=Iu(e);return`${n||Uo(e)?"utc":"time"}Format(${t}, ${i})`}function me(e){if(!e)return;let t;return I(e)?Ou(e)?t={unit:e.substring(6),binned:!0}:t={unit:e}:X(e)&&(t={...e,...e.unit?{unit:e.unit}:{}}),Uo(t.unit)&&(t.utc=!0,t.unit=Xh(t.unit)),t}function Jh(e){const{utc:t,...n}=me(e);return n.unit?(t?"utc":"")+x(n).map(i=>se(`${i==="unit"?"":`_${i}_`}${n[i]}`)).join(""):(t?"utc":"")+"timeunit"+x(n).map(i=>se(`_${i}_${n[i]}`)).join("")}function Lu(e,t=n=>n){const n=me(e),i=_u(n.unit);if(i&&i!=="day"){const r={year:2001,month:1,date:1,hours:0,minutes:0,seconds:0,milliseconds:0},{step:s,part:o}=Pu(i,n.step),a={...r,[o]:+r[o]+s};return`${t($n(a))} - ${t($n(r))}`}}const Zh={year:1,month:1,date:1,hours:1,minutes:1,seconds:1,milliseconds:1};function em(e){return!!Zh[e]}function Pu(e,t=1){if(em(e))return{part:e,step:t};switch(e){case"day":case"dayofyear":return{part:"date",step:t};case"quarter":return{part:"month",step:t*3};case"week":return{part:"date",step:t*7}}}function tm(e){return e==null?void 0:e.param}function Bo(e){return!!(e!=null&&e.field)&&e.equal!==void 0}function Wo(e){return!!(e!=null&&e.field)&&e.lt!==void 0}function Go(e){return!!(e!=null&&e.field)&&e.lte!==void 0}function Ho(e){return!!(e!=null&&e.field)&&e.gt!==void 0}function qo(e){return!!(e!=null&&e.field)&&e.gte!==void 0}function Vo(e){if(e!=null&&e.field){if(k(e.range)&&e.range.length===2)return!0;if(T(e.range))return!0}return!1}function Xo(e){return!!(e!=null&&e.field)&&(k(e.oneOf)||k(e.in))}function nm(e){return!!(e!=null&&e.field)&&e.valid!==void 0}function zu(e){return Xo(e)||Bo(e)||Vo(e)||Wo(e)||Ho(e)||Go(e)||qo(e)}function ot(e,t){return ts(e,{timeUnit:t,wrapTime:!0})}function im(e,t){return e.map(n=>ot(n,t))}function Du(e,t=!0){const{field:n}=e,i=me(e.timeUnit),{unit:r,binned:s}=i||{},o=E(e,{expr:"datum"}),a=r?`time(${s?o:Kh(r,n)})`:o;if(Bo(e))return`${a}===${ot(e.equal,r)}`;if(Wo(e)){const c=e.lt;return`${a}<${ot(c,r)}`}else if(Ho(e)){const c=e.gt;return`${a}>${ot(c,r)}`}else if(Go(e)){const c=e.lte;return`${a}<=${ot(c,r)}`}else if(qo(e)){const c=e.gte;return`${a}>=${ot(c,r)}`}else{if(Xo(e))return`indexof([${im(e.oneOf,r).join(",")}], ${a}) !== -1`;if(nm(e))return Yo(a,e.valid);if(Vo(e)){const{range:c}=e,l=T(c)?{signal:`${c.signal}[0]`}:c[0],u=T(c)?{signal:`${c.signal}[1]`}:c[1];if(l!==null&&u!==null&&t)return"inrange("+a+", ["+ot(l,r)+", "+ot(u,r)+"])";const f=[];return l!==null&&f.push(`${a} >= ${ot(l,r)}`),u!==null&&f.push(`${a} <= ${ot(u,r)}`),f.length>0?f.join(" && "):"true"}}throw new Error(`Invalid field predicate: ${J(e)}`)}function Yo(e,t=!0){return t?`isValid(${e}) && isFinite(+${e})`:`!isValid(${e}) || !isFinite(+${e})`}function rm(e){return zu(e)&&e.timeUnit?{...e,timeUnit:me(e.timeUnit)}:e}const Xi={quantitative:"quantitative",ordinal:"ordinal",temporal:"temporal",nominal:"nominal",geojson:"geojson"};function sm(e){return e==="quantitative"||e==="temporal"}function ju(e){return e==="ordinal"||e==="nominal"}const wn=Xi.quantitative,Ko=Xi.ordinal,ti=Xi.temporal,Qo=Xi.nominal,hi=Xi.geojson;function om(e){if(e)switch(e=e.toLowerCase(),e){case"q":case wn:return"quantitative";case"t":case ti:return"temporal";case"o":case Ko:return"ordinal";case"n":case Qo:return"nominal";case hi:return"geojson"}}const Ae={LINEAR:"linear",LOG:"log",POW:"pow",SQRT:"sqrt",SYMLOG:"symlog",IDENTITY:"identity",SEQUENTIAL:"sequential",TIME:"time",UTC:"utc",QUANTILE:"quantile",QUANTIZE:"quantize",THRESHOLD:"threshold",BIN_ORDINAL:"bin-ordinal",ORDINAL:"ordinal",POINT:"point",BAND:"band"},Zs={linear:"numeric",log:"numeric",pow:"numeric",sqrt:"numeric",symlog:"numeric",identity:"numeric",sequential:"numeric",time:"time",utc:"time",ordinal:"ordinal","bin-ordinal":"bin-ordinal",point:"ordinal-position",band:"ordinal-position",quantile:"discretizing",quantize:"discretizing",threshold:"discretizing"};function am(e,t){const n=Zs[e],i=Zs[t];return n===i||n==="ordinal-position"&&i==="time"||i==="ordinal-position"&&n==="time"}const cm={linear:0,log:1,pow:1,sqrt:1,symlog:1,identity:1,sequential:1,time:0,utc:0,point:10,band:11,ordinal:0,"bin-ordinal":0,quantile:0,quantize:0,threshold:0};function hc(e){return cm[e]}const Mu=new Set(["linear","log","pow","sqrt","symlog"]),Uu=new Set([...Mu,"time","utc"]);function Bu(e){return Mu.has(e)}const Wu=new Set(["quantile","quantize","threshold"]),lm=new Set([...Uu,...Wu,"sequential","identity"]),um=new Set(["ordinal","bin-ordinal","point","band"]);function ye(e){return um.has(e)}function De(e){return lm.has(e)}function Ye(e){return Uu.has(e)}function ni(e){return Wu.has(e)}const fm={pointPadding:.5,barBandPaddingInner:.1,rectBandPaddingInner:0,bandWithNestedOffsetPaddingInner:.2,bandWithNestedOffsetPaddingOuter:.2,minBandSize:2,minFontSize:8,maxFontSize:40,minOpacity:.3,maxOpacity:.8,minSize:9,minStrokeWidth:1,maxStrokeWidth:4,quantileCount:4,quantizeCount:4,zero:!0};function dm(e){return!I(e)&&!!e.name}function Gu(e){return e==null?void 0:e.param}function pm(e){return e==null?void 0:e.unionWith}function gm(e){return X(e)&&"field"in e}const hm={type:1,domain:1,domainMax:1,domainMin:1,domainMid:1,domainRaw:1,align:1,range:1,rangeMax:1,rangeMin:1,scheme:1,bins:1,reverse:1,round:1,clamp:1,nice:1,base:1,exponent:1,constant:1,interpolate:1,zero:1,padding:1,paddingInner:1,paddingOuter:1},{type:Dw,domain:jw,range:Mw,rangeMax:Uw,rangeMin:Bw,scheme:Ww,...mm}=hm,ym=x(mm);function eo(e,t){switch(t){case"type":case"domain":case"reverse":case"range":return!0;case"scheme":case"interpolate":return!["point","band","identity"].includes(e);case"bins":return!["point","band","identity","ordinal"].includes(e);case"round":return Ye(e)||e==="band"||e==="point";case"padding":case"rangeMin":case"rangeMax":return Ye(e)||["point","band"].includes(e);case"paddingOuter":case"align":return["point","band"].includes(e);case"paddingInner":return e==="band";case"domainMax":case"domainMid":case"domainMin":case"domainRaw":case"clamp":return Ye(e);case"nice":return Ye(e)||e==="quantize"||e==="threshold";case"exponent":return e==="pow";case"base":return e==="log";case"constant":return e==="symlog";case"zero":return De(e)&&!G(["log","time","utc","threshold","quantile"],e)}}function Hu(e,t){switch(t){case"interpolate":case"scheme":case"domainMid":return qn(e)?void 0:ph(t);case"align":case"type":case"bins":case"domain":case"domainMax":case"domainMin":case"domainRaw":case"range":case"base":case"exponent":case"constant":case"nice":case"padding":case"paddingInner":case"paddingOuter":case"rangeMax":case"rangeMin":case"reverse":case"round":case"clamp":case"zero":return}}function bm(e,t){return G([Ko,Qo],t)?e===void 0||ye(e):t===ti?G([Ae.TIME,Ae.UTC,void 0],e):t===wn?Bu(e)||ni(e)||e===void 0:!0}function xm(e,t,n=!1){if(!zt(e))return!1;switch(e){case oe:case be:case Qt:case gi:case We:case tt:return Ye(t)||t==="band"?!0:t==="point"?!n:!1;case Lt:case en:case Pt:case Jt:case Zt:case On:return Ye(t)||ni(t)||G(["band","point","ordinal"],t);case Oe:case bt:case xt:return t!=="band";case tn:case _e:return t==="ordinal"||ni(t)}}const Fe={arc:"arc",area:"area",bar:"bar",image:"image",line:"line",point:"point",rect:"rect",rule:"rule",text:"text",tick:"tick",trail:"trail",circle:"circle",square:"square",geoshape:"geoshape"},qu=Fe.arc,qr=Fe.area,Vr=Fe.bar,vm=Fe.image,Xr=Fe.line,Yr=Fe.point,Sm=Fe.rect,xr=Fe.rule,Vu=Fe.text,Jo=Fe.tick,Em=Fe.trail,Zo=Fe.circle,ea=Fe.square,Xu=Fe.geoshape;function sn(e){return["line","area","trail"].includes(e)}function zi(e){return["rect","bar","image","arc"].includes(e)}const $m=new Set(x(Fe));function gt(e){return e.type}const wm=["stroke","strokeWidth","strokeDash","strokeDashOffset","strokeOpacity","strokeJoin","strokeMiterLimit"],Cm=["fill","fillOpacity"],Nm=[...wm,...Cm],Fm={color:1,filled:1,invalid:1,order:1,radius2:1,theta2:1,timeUnitBandSize:1,timeUnitBandPosition:1},mc=x(Fm),km={area:["line","point"],bar:["binSpacing","continuousBandSize","discreteBandSize","minBandSize"],rect:["binSpacing","continuousBandSize","discreteBandSize","minBandSize"],line:["point"],tick:["bandSize","thickness"]},Tm={color:"#4c78a8",invalid:"filter",timeUnitBandSize:1},Am={mark:1,arc:1,area:1,bar:1,circle:1,image:1,line:1,point:1,rect:1,rule:1,square:1,text:1,tick:1,trail:1,geoshape:1},Yu=x(Am);function Cn(e){return e&&e.band!=null}const Om={horizontal:["cornerRadiusTopRight","cornerRadiusBottomRight"],vertical:["cornerRadiusTopLeft","cornerRadiusTopRight"]},Ku=5,_m={binSpacing:1,continuousBandSize:Ku,minBandSize:.25,timeUnitBandPosition:.5},Rm={binSpacing:0,continuousBandSize:Ku,minBandSize:.25,timeUnitBandPosition:.5},Im={thickness:1};function Lm(e){return gt(e)?e.type:e}function ta(e){const{channel:t,channelDef:n,markDef:i,scale:r,config:s}=e,o=ia(e);return S(n)&&!fu(n.aggregate)&&r&&Ye(r.get("type"))?Pm({fieldDef:n,channel:t,markDef:i,ref:o,config:s}):o}function Pm({fieldDef:e,channel:t,markDef:n,ref:i,config:r}){return sn(n.type)?i:V("invalid",n,r)===null?[zm(e,t),i]:i}function zm(e,t){const n=na(e,!0),r=_n(t)==="y"?{field:{group:"height"}}:{value:0};return{test:n,...r}}function na(e,t=!0){return Yo(I(e)?e:E(e,{expr:"datum"}),!t)}function Dm(e){const{datum:t}=e;return In(t)?$n(t):`${J(t)}`}function mn(e,t,n,i){const r={};if(t&&(r.scale=t),Et(e)){const{datum:s}=e;In(s)?r.signal=$n(s):T(s)?r.signal=s.signal:Vi(s)?r.signal=s.expr:r.value=s}else r.field=E(e,n);if(i){const{offset:s,band:o}=i;s&&(r.offset=s),o&&(r.band=o)}return r}function vr({scaleName:e,fieldOrDatumDef:t,fieldOrDatumDef2:n,offset:i,startSuffix:r,endSuffix:s="end",bandPosition:o=.5}){const a=!T(o)&&0<o&&o<1?"datum":void 0,c=E(t,{expr:a,suffix:r}),l=n!==void 0?E(n,{expr:a}):E(t,{suffix:s,expr:a}),u={};if(o===0||o===1){u.scale=e;const f=o===0?c:l;u.field=f}else{const f=T(o)?`(1-${o.signal}) * ${c} + ${o.signal} * ${l}`:`${1-o} * ${c} + ${o} * ${l}`;u.signal=`scale("${e}", ${f})`}return i&&(u.offset=i),u}function jm({scaleName:e,fieldDef:t}){const n=E(t,{expr:"datum"}),i=E(t,{expr:"datum",suffix:"end"});return`abs(scale("${e}", ${i}) - scale("${e}", ${n}))`}function ia({channel:e,channelDef:t,channel2Def:n,markDef:i,config:r,scaleName:s,scale:o,stack:a,offset:c,defaultRef:l,bandPosition:u}){if(t){if(z(t)){const f=o==null?void 0:o.get("type");if(Ne(t)){u??(u=qt({fieldDef:t,fieldDef2:n,markDef:i,config:r}));const{bin:d,timeUnit:g,type:p}=t;if(te(d)||u&&g&&p===ti)return a!=null&&a.impute?mn(t,s,{binSuffix:"mid"},{offset:c}):u&&!ye(f)?vr({scaleName:s,fieldOrDatumDef:t,bandPosition:u,offset:c}):mn(t,s,Ji(t,e)?{binSuffix:"range"}:{},{offset:c});if(xe(d)){if(S(n))return vr({scaleName:s,fieldOrDatumDef:t,fieldOrDatumDef2:n,bandPosition:u,offset:c});v(Nu(e===oe?et:yt))}}return mn(t,s,ye(f)?{binSuffix:"range"}:{},{offset:c,band:f==="band"?u??t.bandPosition??.5:void 0})}else if(Je(t)){const f=t.value,d=c?{offset:c}:{};return{..._i(e,f),...d}}}return Cp(l)&&(l=l()),l&&{...l,...c?{offset:c}:{}}}function _i(e,t){return G(["x","x2"],e)&&t==="width"?{field:{group:"width"}}:G(["y","y2"],e)&&t==="height"?{field:{group:"height"}}:ie(t)}function Nn(e){return e&&e!=="number"&&e!=="time"}function Qu(e,t,n){return`${e}(${t}${n?`, ${J(n)}`:""})`}const Mm=" – ";function ra({fieldOrDatumDef:e,format:t,formatType:n,expr:i,normalizeStack:r,config:s}){var c,l;if(Nn(n))return Ke({fieldOrDatumDef:e,format:t,formatType:n,expr:i,config:s});const o=Ju(e,i,r),a=ii(e);if(t===void 0&&n===void 0&&s.customFormatTypes){if(a==="quantitative"){if(r&&s.normalizedNumberFormatType)return Ke({fieldOrDatumDef:e,format:s.normalizedNumberFormat,formatType:s.normalizedNumberFormatType,expr:i,config:s});if(s.numberFormatType)return Ke({fieldOrDatumDef:e,format:s.numberFormat,formatType:s.numberFormatType,expr:i,config:s})}if(a==="temporal"&&s.timeFormatType&&S(e)&&e.timeUnit===void 0)return Ke({fieldOrDatumDef:e,format:s.timeFormat,formatType:s.timeFormatType,expr:i,config:s})}if(si(e)){const u=Bm({field:o,timeUnit:S(e)?(c=me(e.timeUnit))==null?void 0:c.unit:void 0,format:t,formatType:s.timeFormatType,rawTimeFormat:s.timeFormat,isUTCScale:Pn(e)&&((l=e.scale)==null?void 0:l.type)===Ae.UTC});return u?{signal:u}:void 0}if(t=to({type:a,specifiedFormat:t,config:s,normalizeStack:r}),S(e)&&te(e.bin)){const u=E(e,{expr:i,binSuffix:"end"});return{signal:Yi(o,u,t,n,s)}}else return t||ii(e)==="quantitative"?{signal:`${tf(o,t)}`}:{signal:`isValid(${o}) ? ${o} : ""+${o}`}}function Ju(e,t,n){return S(e)?n?`${E(e,{expr:t,suffix:"end"})}-${E(e,{expr:t,suffix:"start"})}`:E(e,{expr:t}):Dm(e)}function Ke({fieldOrDatumDef:e,format:t,formatType:n,expr:i,normalizeStack:r,config:s,field:o}){if(o??(o=Ju(e,i,r)),o!=="datum.value"&&S(e)&&te(e.bin)){const a=E(e,{expr:i,binSuffix:"end"});return{signal:Yi(o,a,t,n,s)}}return{signal:Qu(n,o,t)}}function Zu(e,t,n,i,r,s){var o;if(!(I(i)&&Nn(i))&&!(n===void 0&&i===void 0&&r.customFormatTypes&&ii(e)==="quantitative"&&(r.normalizedNumberFormatType&&ri(e)&&e.stack==="normalize"||r.numberFormatType))){if(ri(e)&&e.stack==="normalize"&&r.normalizedNumberFormat)return to({type:"quantitative",config:r,normalizeStack:!0});if(si(e)){const a=S(e)?(o=me(e.timeUnit))==null?void 0:o.unit:void 0;return a===void 0&&r.customFormatTypes&&r.timeFormatType?void 0:Um({specifiedFormat:n,timeUnit:a,config:r,omitTimeFormatConfig:s})}return to({type:t,specifiedFormat:n,config:r})}}function ef(e,t,n){var i;if(e&&(T(e)||e==="number"||e==="time"))return e;if(si(t)&&n!=="time"&&n!=="utc")return S(t)&&((i=me(t==null?void 0:t.timeUnit))!=null&&i.utc)?"utc":"time"}function to({type:e,specifiedFormat:t,config:n,normalizeStack:i}){if(I(t))return t;if(e===wn)return i?n.normalizedNumberFormat:n.numberFormat}function Um({specifiedFormat:e,timeUnit:t,config:n,omitTimeFormatConfig:i}){return e||(t?{signal:Iu(t)}:i?void 0:n.timeFormat)}function tf(e,t){return`format(${e}, "${t||""}")`}function yc(e,t,n,i){return Nn(n)?Qu(n,e,t):tf(e,(I(t)?t:void 0)??i.numberFormat)}function Yi(e,t,n,i,r){if(n===void 0&&i===void 0&&r.customFormatTypes&&r.numberFormatType)return Yi(e,t,r.numberFormat,r.numberFormatType,r);const s=yc(e,n,i,r),o=yc(t,n,i,r);return`${Yo(e,!1)} ? "null" : ${s} + "${Mm}" + ${o}`}function Bm({field:e,timeUnit:t,format:n,formatType:i,rawTimeFormat:r,isUTCScale:s}){return!t||n?!t&&i?`${i}(${e}, '${n}')`:(n=I(n)?n:r,`${s?"utc":"time"}Format(${e}, '${n}')`):Qh(t,e,s)}const Kr="min",Wm={x:1,y:1,color:1,fill:1,stroke:1,strokeWidth:1,size:1,shape:1,fillOpacity:1,strokeOpacity:1,opacity:1,text:1};function bc(e){return e in Wm}function nf(e){return!!(e!=null&&e.encoding)}function ft(e){return e&&(e.op==="count"||!!e.field)}function rf(e){return e&&k(e)}function Ki(e){return"row"in e||"column"in e}function sa(e){return!!e&&"header"in e}function Qr(e){return"facet"in e}function Gm(e){return e.param}function Hm(e){return e&&!I(e)&&"repeat"in e}function xc(e){const{field:t,timeUnit:n,bin:i,aggregate:r}=e;return{...n?{timeUnit:n}:{},...i?{bin:i}:{},...r?{aggregate:r}:{},field:t}}function oa(e){return"sort"in e}function qt({fieldDef:e,fieldDef2:t,markDef:n,config:i}){if(z(e)&&e.bandPosition!==void 0)return e.bandPosition;if(S(e)){const{timeUnit:r,bin:s}=e;if(r&&!t)return _t("timeUnitBandPosition",n,i);if(te(s))return .5}}function sf({channel:e,fieldDef:t,fieldDef2:n,markDef:i,config:r,scaleType:s,useVlSizeChannel:o}){var l,u,f;const a=Re(e),c=V(o?"size":a,i,r,{vgChannel:a});if(c!==void 0)return c;if(S(t)){const{timeUnit:d,bin:g}=t;if(d&&!n)return{band:_t("timeUnitBandSize",i,r)};if(te(g)&&!ye(s))return{band:1}}if(zi(i.type))return s?ye(s)?((l=r[i.type])==null?void 0:l.discreteBandSize)||{band:1}:(u=r[i.type])==null?void 0:u.continuousBandSize:(f=r[i.type])==null?void 0:f.discreteBandSize}function of(e,t,n,i){return te(e.bin)||e.timeUnit&&Ne(e)&&e.type==="temporal"?qt({fieldDef:e,fieldDef2:t,markDef:n,config:i})!==void 0:!1}function af(e){return e&&!!e.sort&&!e.field}function Jr(e){return e&&"condition"in e}function Zr(e){const t=e==null?void 0:e.condition;return!!t&&!k(t)&&S(t)}function Qi(e){const t=e==null?void 0:e.condition;return!!t&&!k(t)&&z(t)}function qm(e){const t=e==null?void 0:e.condition;return!!t&&(k(t)||Je(t))}function S(e){return e&&(!!e.field||e.aggregate==="count")}function ii(e){return e==null?void 0:e.type}function Et(e){return e&&"datum"in e}function Bt(e){return Ne(e)&&!Er(e)||Sr(e)}function vc(e){return Ne(e)&&e.type==="quantitative"&&!e.bin||Sr(e)}function Sr(e){return Et(e)&&re(e.datum)}function z(e){return S(e)||Et(e)}function Ne(e){return e&&("field"in e||e.aggregate==="count")&&"type"in e}function Je(e){return e&&"value"in e&&"value"in e}function Pn(e){return e&&("scale"in e||"sort"in e)}function ri(e){return e&&("axis"in e||"stack"in e||"impute"in e)}function cf(e){return e&&"legend"in e}function lf(e){return e&&("format"in e||"formatType"in e)}function Vm(e){return Te(e,["legend","axis","header","scale"])}function Xm(e){return"op"in e}function E(e,t={}){let n=e.field;const i=t.prefix;let r=t.suffix,s="";if(Km(e))n=Ql("count");else{let o;if(!t.nofn)if(Xm(e))o=e.op;else{const{bin:a,aggregate:c,timeUnit:l}=e;te(a)?(o=du(a),r=(t.binSuffix??"")+(t.suffix??"")):c?nn(c)?(s=`["${n}"]`,n=`argmax_${c.argmax}`):Ot(c)?(s=`["${n}"]`,n=`argmin_${c.argmin}`):o=String(c):l&&!Ln(l)&&(o=Jh(l),r=(!["range","mid"].includes(t.binSuffix)&&t.binSuffix||"")+(t.suffix??""))}o&&(n=n?`${o}_${n}`:o)}return r&&(n=`${n}_${r}`),i&&(n=`${i}_${n}`),t.forAs?ko(n):t.expr?Xl(n,t.expr)+s:Ue(n)+s}function Er(e){switch(e.type){case"nominal":case"ordinal":case"geojson":return!0;case"quantitative":return S(e)&&!!e.bin;case"temporal":return!1}throw new Error(Eu(e.type))}function Ym(e){var t;return Pn(e)&&ni((t=e.scale)==null?void 0:t.type)}function Km(e){return e.aggregate==="count"}function Qm(e,t){var o;const{field:n,bin:i,timeUnit:r,aggregate:s}=e;if(s==="count")return t.countTitle;if(te(i))return`${n} (binned)`;if(r&&!Ln(r)){const a=(o=me(r))==null?void 0:o.unit;if(a)return`${n} (${Hr(a).join("-")})`}else if(s)return nn(s)?`${n} for max ${s.argmax}`:Ot(s)?`${n} for min ${s.argmin}`:`${Wi(s)} of ${n}`;return n}function Jm(e){const{aggregate:t,bin:n,timeUnit:i,field:r}=e;if(nn(t))return`${r} for argmax(${t.argmax})`;if(Ot(t))return`${r} for argmin(${t.argmin})`;const s=i&&!Ln(i)?me(i):void 0,o=t||(s==null?void 0:s.unit)||(s==null?void 0:s.maxbins)&&"timeunit"||te(n)&&"bin";return o?`${o.toUpperCase()}(${r})`:r}const uf=(e,t)=>{switch(t.fieldTitle){case"plain":return e.field;case"functional":return Jm(e);default:return Qm(e,t)}};let ff=uf;function df(e){ff=e}function Zm(){df(uf)}function Vn(e,t,{allowDisabling:n,includeDefault:i=!0}){var a;const r=(a=aa(e))==null?void 0:a.title;if(!S(e))return r??e.title;const s=e,o=i?ca(s,t):void 0;return n?le(r,s.title,o):r??s.title??o}function aa(e){if(ri(e)&&e.axis)return e.axis;if(cf(e)&&e.legend)return e.legend;if(sa(e)&&e.header)return e.header}function ca(e,t){return ff(e,t)}function $r(e){if(lf(e)){const{format:t,formatType:n}=e;return{format:t,formatType:n}}else{const t=aa(e)??{},{format:n,formatType:i}=t;return{format:n,formatType:i}}}function ey(e,t){var s;switch(t){case"latitude":case"longitude":return"quantitative";case"row":case"column":case"facet":case"shape":case"strokeDash":return"nominal";case"order":return"ordinal"}if(oa(e)&&k(e.sort))return"ordinal";const{aggregate:n,bin:i,timeUnit:r}=e;if(r)return"temporal";if(i||n&&!nn(n)&&!Ot(n))return"quantitative";if(Pn(e)&&((s=e.scale)!=null&&s.type))switch(Zs[e.scale.type]){case"numeric":case"discretizing":return"quantitative";case"time":return"temporal"}return"nominal"}function ht(e){if(S(e))return e;if(Zr(e))return e.condition}function fe(e){if(z(e))return e;if(Qi(e))return e.condition}function pf(e,t,n,i={}){if(I(e)||re(e)||di(e)){const r=I(e)?"string":re(e)?"number":"boolean";return v(th(t,r,e)),{value:e}}return z(e)?wr(e,t,n,i):Qi(e)?{...e,condition:wr(e.condition,t,n,i)}:e}function wr(e,t,n,i){if(lf(e)){const{format:r,formatType:s,...o}=e;if(Nn(s)&&!n.customFormatTypes)return v(dc(t)),wr(o,t,n,i)}else{const r=ri(e)?"axis":cf(e)?"legend":sa(e)?"header":null;if(r&&e[r]){const{format:s,formatType:o,...a}=e[r];if(Nn(o)&&!n.customFormatTypes)return v(dc(t)),wr({...e,[r]:a},t,n,i)}}return S(e)?la(e,t,i):ty(e)}function ty(e){let t=e.type;if(t)return e;const{datum:n}=e;return t=re(n)?"quantitative":I(n)?"nominal":In(n)?"temporal":void 0,{...e,type:t}}function la(e,t,{compositeMark:n=!1}={}){const{aggregate:i,timeUnit:r,bin:s,field:o}=e,a={...e};if(!n&&i&&!Po(i)&&!nn(i)&&!Ot(i)&&(v(ih(i)),delete a.aggregate),r&&(a.timeUnit=me(r)),o&&(a.field=`${o}`),te(s)&&(a.bin=es(s,t)),xe(s)&&!ue(t)&&v(Lh(t)),Ne(a)){const{type:c}=a,l=om(c);c!==l&&(a.type=l),c!=="quantitative"&&fu(i)&&(v(nh(c,i)),a.type="quantitative")}else if(!su(t)){const c=ey(a,t);a.type=c}if(Ne(a)){const{compatible:c,warning:l}=ny(a,t)||{};c===!1&&v(l)}if(oa(a)&&I(a.sort)){const{sort:c}=a;if(bc(c))return{...a,sort:{encoding:c}};const l=c.substr(1);if(c.charAt(0)==="-"&&bc(l))return{...a,sort:{encoding:l,order:"descending"}}}if(sa(a)){const{header:c}=a;if(c){const{orient:l,...u}=c;if(l)return{...a,header:{...u,labelOrient:c.labelOrient||l,titleOrient:c.titleOrient||l}}}}return a}function es(e,t){return di(e)?{maxbins:ic(t)}:e==="binned"?{binned:!0}:!e.maxbins&&!e.step?{...e,maxbins:ic(t)}:e}const jn={compatible:!0};function ny(e,t){const n=e.type;if(n==="geojson"&&t!=="shape")return{compatible:!1,warning:`Channel ${t} should not be used with a geojson data.`};switch(t){case Ft:case kt:case Pr:return Er(e)?jn:{compatible:!1,warning:ah(t)};case oe:case be:case Qt:case gi:case Oe:case bt:case xt:case Gi:case Hi:case zr:case En:case Dr:case jr:case On:case We:case tt:case Mr:return jn;case it:case Be:case nt:case rt:return n!==wn?{compatible:!1,warning:`Channel ${t} should be used with a quantitative field only, not ${e.type} field.`}:jn;case Pt:case Jt:case Zt:case en:case Lt:case It:case Rt:case et:case yt:return n==="nominal"&&!e.sort?{compatible:!1,warning:`Channel ${t} should not be used with an unsorted discrete field.`}:jn;case _e:case tn:return!Er(e)&&!Ym(e)?{compatible:!1,warning:ch(t)}:jn;case Zn:return e.type==="nominal"&&!("sort"in e)?{compatible:!1,warning:"Channel order is inappropriate for nominal field, which has no inherent order."}:jn}}function si(e){const{formatType:t}=$r(e);return t==="time"||!t&&iy(e)}function iy(e){return e&&(e.type==="temporal"||S(e)&&!!e.timeUnit)}function ts(e,{timeUnit:t,type:n,wrapTime:i,undefinedIfExprNotRequired:r}){var c;const s=t&&((c=me(t))==null?void 0:c.unit);let o=s||n==="temporal",a;return Vi(e)?a=e.expr:T(e)?a=e.signal:In(e)?(o=!0,a=$n(e)):(I(e)||re(e))&&o&&(a=`datetime(${J(e)})`,Vh(s)&&(re(e)&&e<1e4||I(e)&&isNaN(Date.parse(e)))&&(a=$n({[s]:e}))),a?i&&o?`time(${a})`:a:r?void 0:J(e)}function gf(e,t){const{type:n}=e;return t.map(i=>{const r=S(e)&&!Ln(e.timeUnit)?e.timeUnit:void 0,s=ts(i,{timeUnit:r,type:n,undefinedIfExprNotRequired:!0});return s!==void 0?{signal:s}:i})}function Ji(e,t){return te(e.bin)?zt(t)&&["ordinal","nominal"].includes(e.type):(console.warn("Only call this method for binned field defs."),!1)}const Sc={labelAlign:{part:"labels",vgProp:"align"},labelBaseline:{part:"labels",vgProp:"baseline"},labelColor:{part:"labels",vgProp:"fill"},labelFont:{part:"labels",vgProp:"font"},labelFontSize:{part:"labels",vgProp:"fontSize"},labelFontStyle:{part:"labels",vgProp:"fontStyle"},labelFontWeight:{part:"labels",vgProp:"fontWeight"},labelOpacity:{part:"labels",vgProp:"opacity"},labelOffset:null,labelPadding:null,gridColor:{part:"grid",vgProp:"stroke"},gridDash:{part:"grid",vgProp:"strokeDash"},gridDashOffset:{part:"grid",vgProp:"strokeDashOffset"},gridOpacity:{part:"grid",vgProp:"opacity"},gridWidth:{part:"grid",vgProp:"strokeWidth"},tickColor:{part:"ticks",vgProp:"stroke"},tickDash:{part:"ticks",vgProp:"strokeDash"},tickDashOffset:{part:"ticks",vgProp:"strokeDashOffset"},tickOpacity:{part:"ticks",vgProp:"opacity"},tickSize:null,tickWidth:{part:"ticks",vgProp:"strokeWidth"}};function Zi(e){return e==null?void 0:e.condition}const hf=["domain","grid","labels","ticks","title"],ry={grid:"grid",gridCap:"grid",gridColor:"grid",gridDash:"grid",gridDashOffset:"grid",gridOpacity:"grid",gridScale:"grid",gridWidth:"grid",orient:"main",bandPosition:"both",aria:"main",description:"main",domain:"main",domainCap:"main",domainColor:"main",domainDash:"main",domainDashOffset:"main",domainOpacity:"main",domainWidth:"main",format:"main",formatType:"main",labelAlign:"main",labelAngle:"main",labelBaseline:"main",labelBound:"main",labelColor:"main",labelFlush:"main",labelFlushOffset:"main",labelFont:"main",labelFontSize:"main",labelFontStyle:"main",labelFontWeight:"main",labelLimit:"main",labelLineHeight:"main",labelOffset:"main",labelOpacity:"main",labelOverlap:"main",labelPadding:"main",labels:"main",labelSeparation:"main",maxExtent:"main",minExtent:"main",offset:"both",position:"main",tickCap:"main",tickColor:"main",tickDash:"main",tickDashOffset:"main",tickMinStep:"both",tickOffset:"both",tickOpacity:"main",tickRound:"both",ticks:"main",tickSize:"main",tickWidth:"both",title:"main",titleAlign:"main",titleAnchor:"main",titleAngle:"main",titleBaseline:"main",titleColor:"main",titleFont:"main",titleFontSize:"main",titleFontStyle:"main",titleFontWeight:"main",titleLimit:"main",titleLineHeight:"main",titleOpacity:"main",titlePadding:"main",titleX:"main",titleY:"main",encode:"both",scale:"both",tickBand:"both",tickCount:"both",tickExtra:"both",translate:"both",values:"both",zindex:"both"},mf={orient:1,aria:1,bandPosition:1,description:1,domain:1,domainCap:1,domainColor:1,domainDash:1,domainDashOffset:1,domainOpacity:1,domainWidth:1,format:1,formatType:1,grid:1,gridCap:1,gridColor:1,gridDash:1,gridDashOffset:1,gridOpacity:1,gridWidth:1,labelAlign:1,labelAngle:1,labelBaseline:1,labelBound:1,labelColor:1,labelFlush:1,labelFlushOffset:1,labelFont:1,labelFontSize:1,labelFontStyle:1,labelFontWeight:1,labelLimit:1,labelLineHeight:1,labelOffset:1,labelOpacity:1,labelOverlap:1,labelPadding:1,labels:1,labelSeparation:1,maxExtent:1,minExtent:1,offset:1,position:1,tickBand:1,tickCap:1,tickColor:1,tickCount:1,tickDash:1,tickDashOffset:1,tickExtra:1,tickMinStep:1,tickOffset:1,tickOpacity:1,tickRound:1,ticks:1,tickSize:1,tickWidth:1,title:1,titleAlign:1,titleAnchor:1,titleAngle:1,titleBaseline:1,titleColor:1,titleFont:1,titleFontSize:1,titleFontStyle:1,titleFontWeight:1,titleLimit:1,titleLineHeight:1,titleOpacity:1,titlePadding:1,titleX:1,titleY:1,translate:1,values:1,zindex:1},sy={...mf,style:1,labelExpr:1,encoding:1};function Ec(e){return!!sy[e]}const oy={axis:1,axisBand:1,axisBottom:1,axisDiscrete:1,axisLeft:1,axisPoint:1,axisQuantitative:1,axisRight:1,axisTemporal:1,axisTop:1,axisX:1,axisXBand:1,axisXDiscrete:1,axisXPoint:1,axisXQuantitative:1,axisXTemporal:1,axisY:1,axisYBand:1,axisYDiscrete:1,axisYPoint:1,axisYQuantitative:1,axisYTemporal:1},yf=x(oy);function Dt(e){return"mark"in e}class ns{constructor(t,n){this.name=t,this.run=n}hasMatchingType(t){return Dt(t)?Lm(t.mark)===this.name:!1}}function yn(e,t){const n=e&&e[t];return n?k(n)?vn(n,i=>!!i.field):S(n)||Zr(n):!1}function bf(e,t){const n=e&&e[t];return n?k(n)?vn(n,i=>!!i.field):S(n)||Et(n)||Qi(n):!1}function xf(e,t){if(ue(t)){const n=e[t];if((S(n)||Et(n))&&(ju(n.type)||S(n)&&n.timeUnit)){const i=_o(t);return bf(e,i)}}return!1}function ua(e){return vn(ag,t=>{if(yn(e,t)){const n=e[t];if(k(n))return vn(n,i=>!!i.aggregate);{const i=ht(n);return i&&!!i.aggregate}}return!1})}function vf(e,t){const n=[],i=[],r=[],s=[],o={};return fa(e,(a,c)=>{if(S(a)){const{field:l,aggregate:u,bin:f,timeUnit:d,...g}=a;if(u||d||f){const p=aa(a),h=p==null?void 0:p.title;let m=E(a,{forAs:!0});const y={...h?[]:{title:Vn(a,t,{allowDisabling:!0})},...g,field:m};if(u){let b;if(nn(u)?(b="argmax",m=E({op:"argmax",field:u.argmax},{forAs:!0}),y.field=`${m}.${l}`):Ot(u)?(b="argmin",m=E({op:"argmin",field:u.argmin},{forAs:!0}),y.field=`${m}.${l}`):u!=="boxplot"&&u!=="errorbar"&&u!=="errorband"&&(b=u),b){const C={op:b,as:m};l&&(C.field=l),s.push(C)}}else if(n.push(m),Ne(a)&&te(f)){if(i.push({bin:f,field:l,as:m}),n.push(E(a,{binSuffix:"end"})),Ji(a,c)&&n.push(E(a,{binSuffix:"range"})),ue(c)){const b={field:`${m}_end`};o[`${c}2`]=b}y.bin="binned",su(c)||(y.type=wn)}else if(d&&!Ln(d)){r.push({timeUnit:d,field:l,as:m});const b=Ne(a)&&a.type!==ti&&"time";b&&(c===Gi||c===En?y.formatType=b:yg(c)?y.legend={formatType:b,...y.legend}:ue(c)&&(y.axis={formatType:b,...y.axis}))}o[c]=y}else n.push(l),o[c]=e[c]}else o[c]=e[c]}),{bins:i,timeUnits:r,aggregate:s,groupby:n,encoding:o}}function ay(e,t,n){const i=xg(t,n);if(i){if(i==="binned"){const r=e[t===et?oe:be];return!!(S(r)&&S(e[t])&&xe(r.bin))}}else return!1;return!0}function cy(e,t,n,i){const r={};for(const s of x(e))ru(s)||v(oh(s));for(let s of pg){if(!e[s])continue;const o=e[s];if(qi(s)){const a=dg(s),c=r[a];if(S(c)&&sm(c.type)&&S(o)&&!c.timeUnit){v(eh(a));continue}}if(s==="angle"&&t==="arc"&&!e.theta&&(v(Zg),s=We),!ay(e,s,t)){v(Gr(s,t));continue}if(s===Lt&&t==="line"){const a=ht(e[s]);if(a!=null&&a.aggregate){v(sh);continue}}if(s===Oe&&(n?"fill"in e:"stroke"in e)){v($u("encoding",{fill:"fill"in e,stroke:"stroke"in e}));continue}if(s===Hi||s===Zn&&!k(o)&&!Je(o)||s===En&&k(o)){if(o){if(s===Zn){const a=e[s];if(af(a)){r[s]=a;continue}}r[s]=ce(o).reduce((a,c)=>(S(c)?a.push(la(c,s)):v(Js(c,s)),a),[])}}else{if(s===En&&o===null)r[s]=null;else if(!S(o)&&!Et(o)&&!Je(o)&&!Jr(o)&&!T(o)){v(Js(o,s));continue}r[s]=pf(o,s,i)}}return r}function is(e,t){const n={};for(const i of x(e)){const r=pf(e[i],i,t,{compositeMark:!0});n[i]=r}return n}function ly(e){const t=[];for(const n of x(e))if(yn(e,n)){const i=e[n],r=ce(i);for(const s of r)S(s)?t.push(s):Zr(s)&&t.push(s.condition)}return t}function fa(e,t,n){if(e)for(const i of x(e)){const r=e[i];if(k(r))for(const s of r)t.call(n,s,i);else t.call(n,r,i)}}function uy(e,t,n,i){return e?x(e).reduce((r,s)=>{const o=e[s];return k(o)?o.reduce((a,c)=>t.call(i,a,c,s),r):t.call(i,r,o,s)},n):n}function Sf(e,t){return x(t).reduce((n,i)=>{switch(i){case oe:case be:case Dr:case Mr:case jr:case et:case yt:case Qt:case gi:case We:case It:case tt:case Rt:case nt:case it:case rt:case Be:case Gi:case _e:case On:case En:return n;case Zn:if(e==="line"||e==="trail")return n;case Hi:case zr:{const r=t[i];if(k(r)||S(r))for(const s of ce(r))s.aggregate||n.push(E(s,{}));return n}case Lt:if(e==="trail")return n;case Oe:case bt:case xt:case Pt:case Jt:case Zt:case tn:case en:{const r=ht(t[i]);return r&&!r.aggregate&&n.push(E(r,{})),n}}},[])}function fy(e){const{tooltip:t,...n}=e;if(!t)return{filteredEncoding:n};let i,r;if(k(t)){for(const s of t)s.aggregate?(i||(i=[]),i.push(s)):(r||(r=[]),r.push(s));i&&(n.tooltip=i)}else t.aggregate?n.tooltip=t:r=t;return k(r)&&r.length===1&&(r=r[0]),{customTooltipWithoutAggregatedField:r,filteredEncoding:n}}function no(e,t,n,i=!0){if("tooltip"in n)return{tooltip:n.tooltip};const r=e.map(({fieldPrefix:o,titlePrefix:a})=>{const c=i?` of ${da(t)}`:"";return{field:o+t.field,type:t.type,title:T(a)?{signal:`${a}"${escape(c)}"`}:a+c}}),s=ly(n).map(Vm);return{tooltip:[...r,...ut(s,W)]}}function da(e){const{title:t,field:n}=e;return le(t,n)}function pa(e,t,n,i,r){const{scale:s,axis:o}=n;return({partName:a,mark:c,positionPrefix:l,endPositionPrefix:u=void 0,extraEncoding:f={}})=>{const d=da(n);return Ef(e,a,r,{mark:c,encoding:{[t]:{field:`${l}_${n.field}`,type:n.type,...d!==void 0?{title:d}:{},...s!==void 0?{scale:s}:{},...o!==void 0?{axis:o}:{}},...I(u)?{[`${t}2`]:{field:`${u}_${n.field}`}}:{},...i,...f}})}}function Ef(e,t,n,i){const{clip:r,color:s,opacity:o}=e,a=e.type;return e[t]||e[t]===void 0&&n[t]?[{...i,mark:{...n[t],...r?{clip:r}:{},...s?{color:s}:{},...o?{opacity:o}:{},...gt(i.mark)?i.mark:{type:i.mark},style:`${a}-${String(t)}`,...di(e[t])?{}:e[t]}}]:[]}function $f(e,t,n){const{encoding:i}=e,r=t==="vertical"?"y":"x",s=i[r],o=i[`${r}2`],a=i[`${r}Error`],c=i[`${r}Error2`];return{continuousAxisChannelDef:rr(s,n),continuousAxisChannelDef2:rr(o,n),continuousAxisChannelDefError:rr(a,n),continuousAxisChannelDefError2:rr(c,n),continuousAxis:r}}function rr(e,t){if(e!=null&&e.aggregate){const{aggregate:n,...i}=e;return n!==t&&v(Ih(n,t)),i}else return e}function wf(e,t){const{mark:n,encoding:i}=e,{x:r,y:s}=i;if(gt(n)&&n.orient)return n.orient;if(Bt(r)){if(Bt(s)){const o=S(r)&&r.aggregate,a=S(s)&&s.aggregate;if(!o&&a===t)return"vertical";if(!a&&o===t)return"horizontal";if(o===t&&a===t)throw new Error("Both x and y cannot have aggregate");return si(s)&&!si(r)?"horizontal":"vertical"}return"horizontal"}else{if(Bt(s))return"vertical";throw new Error(`Need a valid continuous axis for ${t}s`)}}const Cr="boxplot",dy=["box","median","outliers","rule","ticks"],py=new ns(Cr,Nf);function Cf(e){return re(e)?"tukey":e}function Nf(e,{config:t}){e={...e,encoding:is(e.encoding,t)};const{mark:n,encoding:i,params:r,projection:s,...o}=e,a=gt(n)?n:{type:n};r&&v(Su("boxplot"));const c=a.extent??t.boxplot.extent,l=V("size",a,t),u=a.invalid,f=Cf(c),{bins:d,timeUnits:g,transform:p,continuousAxisChannelDef:h,continuousAxis:m,groupby:y,aggregate:b,encodingWithoutContinuousAxis:C,ticksOrient:D,boxOrient:w,customTooltipWithoutAggregatedField:F}=gy(e,c,t),{color:A,size:M,...K}=C,we=Ep=>pa(a,m,h,Ep,t.boxplot),de=we(K),st=we(C),N=we({...K,...M?{size:M}:{}}),$=no([{fieldPrefix:f==="min-max"?"upper_whisker_":"max_",titlePrefix:"Max"},{fieldPrefix:"upper_box_",titlePrefix:"Q3"},{fieldPrefix:"mid_box_",titlePrefix:"Median"},{fieldPrefix:"lower_box_",titlePrefix:"Q1"},{fieldPrefix:f==="min-max"?"lower_whisker_":"min_",titlePrefix:"Min"}],h,C),R={type:"tick",color:"black",opacity:1,orient:D,invalid:u,aria:!1},_=f==="min-max"?$:no([{fieldPrefix:"upper_whisker_",titlePrefix:"Upper Whisker"},{fieldPrefix:"lower_whisker_",titlePrefix:"Lower Whisker"}],h,C),L=[...de({partName:"rule",mark:{type:"rule",invalid:u,aria:!1},positionPrefix:"lower_whisker",endPositionPrefix:"lower_box",extraEncoding:_}),...de({partName:"rule",mark:{type:"rule",invalid:u,aria:!1},positionPrefix:"upper_box",endPositionPrefix:"upper_whisker",extraEncoding:_}),...de({partName:"ticks",mark:R,positionPrefix:"lower_whisker",extraEncoding:_}),...de({partName:"ticks",mark:R,positionPrefix:"upper_whisker",extraEncoding:_})],O=[...f!=="tukey"?L:[],...st({partName:"box",mark:{type:"bar",...l?{size:l}:{},orient:w,invalid:u,ariaRoleDescription:"box"},positionPrefix:"lower_box",endPositionPrefix:"upper_box",extraEncoding:$}),...N({partName:"median",mark:{type:"tick",invalid:u,...X(t.boxplot.median)&&t.boxplot.median.color?{color:t.boxplot.median.color}:{},...l?{size:l}:{},orient:D,aria:!1},positionPrefix:"mid_box",extraEncoding:$})];if(f==="min-max")return{...o,transform:(o.transform??[]).concat(p),layer:O};const j=`datum["lower_box_${h.field}"]`,q=`datum["upper_box_${h.field}"]`,U=`(${q} - ${j})`,Z=`${j} - ${c} * ${U}`,Se=`${q} + ${c} * ${U}`,ne=`datum["${h.field}"]`,on={joinaggregate:Ff(h.field),groupby:y},Ns={transform:[{filter:`(${Z} <= ${ne}) && (${ne} <= ${Se})`},{aggregate:[{op:"min",field:h.field,as:`lower_whisker_${h.field}`},{op:"max",field:h.field,as:`upper_whisker_${h.field}`},{op:"min",field:`lower_box_${h.field}`,as:`lower_box_${h.field}`},{op:"max",field:`upper_box_${h.field}`,as:`upper_box_${h.field}`},...b],groupby:y}],layer:L},{tooltip:aw,...vp}=K,{scale:Qa,axis:Sp}=h,Ja=da(h),Za=Te(Sp,["title"]),ec=Ef(a,"outliers",t.boxplot,{transform:[{filter:`(${ne} < ${Z}) || (${ne} > ${Se})`}],mark:"point",encoding:{[m]:{field:h.field,type:h.type,...Ja!==void 0?{title:Ja}:{},...Qa!==void 0?{scale:Qa}:{},...Q(Za)?{}:{axis:Za}},...vp,...A?{color:A}:{},...F?{tooltip:F}:{}}})[0];let ir;const tc=[...d,...g,on];return ec?ir={transform:tc,layer:[ec,Ns]}:(ir=Ns,ir.transform.unshift(...tc)),{...o,layer:[ir,{transform:p,layer:O}]}}function Ff(e){return[{op:"q1",field:e,as:`lower_box_${e}`},{op:"q3",field:e,as:`upper_box_${e}`}]}function gy(e,t,n){const i=wf(e,Cr),{continuousAxisChannelDef:r,continuousAxis:s}=$f(e,i,Cr),o=r.field,a=Cf(t),c=[...Ff(o),{op:"median",field:o,as:`mid_box_${o}`},{op:"min",field:o,as:(a==="min-max"?"lower_whisker_":"min_")+o},{op:"max",field:o,as:(a==="min-max"?"upper_whisker_":"max_")+o}],l=a==="min-max"||a==="tukey"?[]:[{calculate:`datum["upper_box_${o}"] - datum["lower_box_${o}"]`,as:`iqr_${o}`},{calculate:`min(datum["upper_box_${o}"] + datum["iqr_${o}"] * ${t}, datum["max_${o}"])`,as:`upper_whisker_${o}`},{calculate:`max(datum["lower_box_${o}"] - datum["iqr_${o}"] * ${t}, datum["min_${o}"])`,as:`lower_whisker_${o}`}],{[s]:u,...f}=e.encoding,{customTooltipWithoutAggregatedField:d,filteredEncoding:g}=fy(f),{bins:p,timeUnits:h,aggregate:m,groupby:y,encoding:b}=vf(g,n),C=i==="vertical"?"horizontal":"vertical",D=i,w=[...p,...h,{aggregate:[...m,...c],groupby:y},...l];return{bins:p,timeUnits:h,transform:w,groupby:y,aggregate:m,continuousAxisChannelDef:r,continuousAxis:s,encodingWithoutContinuousAxis:b,ticksOrient:C,boxOrient:D,customTooltipWithoutAggregatedField:d}}const ga="errorbar",hy=["ticks","rule"],my=new ns(ga,kf);function kf(e,{config:t}){e={...e,encoding:is(e.encoding,t)};const{transform:n,continuousAxisChannelDef:i,continuousAxis:r,encodingWithoutContinuousAxis:s,ticksOrient:o,markDef:a,outerSpec:c,tooltipEncoding:l}=Tf(e,ga,t);delete s.size;const u=pa(a,r,i,s,t.errorbar),f=a.thickness,d=a.size,g={type:"tick",orient:o,aria:!1,...f!==void 0?{thickness:f}:{},...d!==void 0?{size:d}:{}},p=[...u({partName:"ticks",mark:g,positionPrefix:"lower",extraEncoding:l}),...u({partName:"ticks",mark:g,positionPrefix:"upper",extraEncoding:l}),...u({partName:"rule",mark:{type:"rule",ariaRoleDescription:"errorbar",...f!==void 0?{size:f}:{}},positionPrefix:"lower",endPositionPrefix:"upper",extraEncoding:l})];return{...c,transform:n,...p.length>1?{layer:p}:{...p[0]}}}function yy(e,t){const{encoding:n}=e;if(by(n))return{orient:wf(e,t),inputType:"raw"};const i=xy(n),r=vy(n),s=n.x,o=n.y;if(i){if(r)throw new Error(`${t} cannot be both type aggregated-upper-lower and aggregated-error`);const a=n.x2,c=n.y2;if(z(a)&&z(c))throw new Error(`${t} cannot have both x2 and y2`);if(z(a)){if(Bt(s))return{orient:"horizontal",inputType:"aggregated-upper-lower"};throw new Error(`Both x and x2 have to be quantitative in ${t}`)}else if(z(c)){if(Bt(o))return{orient:"vertical",inputType:"aggregated-upper-lower"};throw new Error(`Both y and y2 have to be quantitative in ${t}`)}throw new Error("No ranged axis")}else{const a=n.xError,c=n.xError2,l=n.yError,u=n.yError2;if(z(c)&&!z(a))throw new Error(`${t} cannot have xError2 without xError`);if(z(u)&&!z(l))throw new Error(`${t} cannot have yError2 without yError`);if(z(a)&&z(l))throw new Error(`${t} cannot have both xError and yError with both are quantiative`);if(z(a)){if(Bt(s))return{orient:"horizontal",inputType:"aggregated-error"};throw new Error("All x, xError, and xError2 (if exist) have to be quantitative")}else if(z(l)){if(Bt(o))return{orient:"vertical",inputType:"aggregated-error"};throw new Error("All y, yError, and yError2 (if exist) have to be quantitative")}throw new Error("No ranged axis")}}function by(e){return(z(e.x)||z(e.y))&&!z(e.x2)&&!z(e.y2)&&!z(e.xError)&&!z(e.xError2)&&!z(e.yError)&&!z(e.yError2)}function xy(e){return z(e.x2)||z(e.y2)}function vy(e){return z(e.xError)||z(e.xError2)||z(e.yError)||z(e.yError2)}function Tf(e,t,n){const{mark:i,encoding:r,params:s,projection:o,...a}=e,c=gt(i)?i:{type:i};s&&v(Su(t));const{orient:l,inputType:u}=yy(e,t),{continuousAxisChannelDef:f,continuousAxisChannelDef2:d,continuousAxisChannelDefError:g,continuousAxisChannelDefError2:p,continuousAxis:h}=$f(e,l,t),{errorBarSpecificAggregate:m,postAggregateCalculates:y,tooltipSummary:b,tooltipTitleWithFieldName:C}=Sy(c,f,d,g,p,u,t,n),{[h]:D,[h==="x"?"x2":"y2"]:w,[h==="x"?"xError":"yError"]:F,[h==="x"?"xError2":"yError2"]:A,...M}=r,{bins:K,timeUnits:we,aggregate:de,groupby:st,encoding:N}=vf(M,n),$=[...de,...m],R=u!=="raw"?[]:st,_=no(b,f,N,C);return{transform:[...a.transform??[],...K,...we,...$.length===0?[]:[{aggregate:$,groupby:R}],...y],groupby:R,continuousAxisChannelDef:f,continuousAxis:h,encodingWithoutContinuousAxis:N,ticksOrient:l==="vertical"?"horizontal":"vertical",markDef:c,outerSpec:a,tooltipEncoding:_}}function Sy(e,t,n,i,r,s,o,a){let c=[],l=[];const u=t.field;let f,d=!1;if(s==="raw"){const g=e.center?e.center:e.extent?e.extent==="iqr"?"median":"mean":a.errorbar.center,p=e.extent?e.extent:g==="mean"?"stderr":"iqr";if(g==="median"!=(p==="iqr")&&v(Rh(g,p,o)),p==="stderr"||p==="stdev")c=[{op:p,field:u,as:`extent_${u}`},{op:g,field:u,as:`center_${u}`}],l=[{calculate:`datum["center_${u}"] + datum["extent_${u}"]`,as:`upper_${u}`},{calculate:`datum["center_${u}"] - datum["extent_${u}"]`,as:`lower_${u}`}],f=[{fieldPrefix:"center_",titlePrefix:Wi(g)},{fieldPrefix:"upper_",titlePrefix:$c(g,p,"+")},{fieldPrefix:"lower_",titlePrefix:$c(g,p,"-")}],d=!0;else{let h,m,y;p==="ci"?(h="mean",m="ci0",y="ci1"):(h="median",m="q1",y="q3"),c=[{op:m,field:u,as:`lower_${u}`},{op:y,field:u,as:`upper_${u}`},{op:h,field:u,as:`center_${u}`}],f=[{fieldPrefix:"upper_",titlePrefix:Vn({field:u,aggregate:y,type:"quantitative"},a,{allowDisabling:!1})},{fieldPrefix:"lower_",titlePrefix:Vn({field:u,aggregate:m,type:"quantitative"},a,{allowDisabling:!1})},{fieldPrefix:"center_",titlePrefix:Vn({field:u,aggregate:h,type:"quantitative"},a,{allowDisabling:!1})}]}}else{(e.center||e.extent)&&v(_h(e.center,e.extent)),s==="aggregated-upper-lower"?(f=[],l=[{calculate:`datum["${n.field}"]`,as:`upper_${u}`},{calculate:`datum["${u}"]`,as:`lower_${u}`}]):s==="aggregated-error"&&(f=[{fieldPrefix:"",titlePrefix:u}],l=[{calculate:`datum["${u}"] + datum["${i.field}"]`,as:`upper_${u}`}],r?l.push({calculate:`datum["${u}"] + datum["${r.field}"]`,as:`lower_${u}`}):l.push({calculate:`datum["${u}"] - datum["${i.field}"]`,as:`lower_${u}`}));for(const g of l)f.push({fieldPrefix:g.as.substring(0,6),titlePrefix:Sn(Sn(g.calculate,'datum["',""),'"]',"")})}return{postAggregateCalculates:l,errorBarSpecificAggregate:c,tooltipSummary:f,tooltipTitleWithFieldName:d}}function $c(e,t,n){return`${Wi(e)} ${n} ${t}`}const ha="errorband",Ey=["band","borders"],$y=new ns(ha,Af);function Af(e,{config:t}){e={...e,encoding:is(e.encoding,t)};const{transform:n,continuousAxisChannelDef:i,continuousAxis:r,encodingWithoutContinuousAxis:s,markDef:o,outerSpec:a,tooltipEncoding:c}=Tf(e,ha,t),l=o,u=pa(l,r,i,s,t.errorband),f=e.encoding.x!==void 0&&e.encoding.y!==void 0;let d={type:f?"area":"rect"},g={type:f?"line":"rule"};const p={...l.interpolate?{interpolate:l.interpolate}:{},...l.tension&&l.interpolate?{tension:l.tension}:{}};return f?(d={...d,...p,ariaRoleDescription:"errorband"},g={...g,...p,aria:!1}):l.interpolate?v(gc("interpolate")):l.tension&&v(gc("tension")),{...a,transform:n,layer:[...u({partName:"band",mark:d,positionPrefix:"lower",endPositionPrefix:"upper",extraEncoding:c}),...u({partName:"borders",mark:g,positionPrefix:"lower",extraEncoding:c}),...u({partName:"borders",mark:g,positionPrefix:"upper",extraEncoding:c})]}}const Of={};function ma(e,t,n){const i=new ns(e,t);Of[e]={normalizer:i,parts:n}}function wy(){return x(Of)}ma(Cr,Nf,dy);ma(ga,kf,hy);ma(ha,Af,Ey);const Cy=["gradientHorizontalMaxLength","gradientHorizontalMinLength","gradientVerticalMaxLength","gradientVerticalMinLength","unselectedOpacity"],_f={titleAlign:"align",titleAnchor:"anchor",titleAngle:"angle",titleBaseline:"baseline",titleColor:"color",titleFont:"font",titleFontSize:"fontSize",titleFontStyle:"fontStyle",titleFontWeight:"fontWeight",titleLimit:"limit",titleLineHeight:"lineHeight",titleOrient:"orient",titlePadding:"offset"},Rf={labelAlign:"align",labelAnchor:"anchor",labelAngle:"angle",labelBaseline:"baseline",labelColor:"color",labelFont:"font",labelFontSize:"fontSize",labelFontStyle:"fontStyle",labelFontWeight:"fontWeight",labelLimit:"limit",labelLineHeight:"lineHeight",labelOrient:"orient",labelPadding:"offset"},Ny=x(_f),Fy=x(Rf),ky={header:1,headerRow:1,headerColumn:1,headerFacet:1},If=x(ky),Lf=["size","shape","fill","stroke","strokeDash","strokeWidth","opacity"],Ty={gradientHorizontalMaxLength:200,gradientHorizontalMinLength:100,gradientVerticalMaxLength:200,gradientVerticalMinLength:64,unselectedOpacity:.35},Ay={aria:1,clipHeight:1,columnPadding:1,columns:1,cornerRadius:1,description:1,direction:1,fillColor:1,format:1,formatType:1,gradientLength:1,gradientOpacity:1,gradientStrokeColor:1,gradientStrokeWidth:1,gradientThickness:1,gridAlign:1,labelAlign:1,labelBaseline:1,labelColor:1,labelFont:1,labelFontSize:1,labelFontStyle:1,labelFontWeight:1,labelLimit:1,labelOffset:1,labelOpacity:1,labelOverlap:1,labelPadding:1,labelSeparation:1,legendX:1,legendY:1,offset:1,orient:1,padding:1,rowPadding:1,strokeColor:1,symbolDash:1,symbolDashOffset:1,symbolFillColor:1,symbolLimit:1,symbolOffset:1,symbolOpacity:1,symbolSize:1,symbolStrokeColor:1,symbolStrokeWidth:1,symbolType:1,tickCount:1,tickMinStep:1,title:1,titleAlign:1,titleAnchor:1,titleBaseline:1,titleColor:1,titleFont:1,titleFontSize:1,titleFontStyle:1,titleFontWeight:1,titleLimit:1,titleLineHeight:1,titleOpacity:1,titleOrient:1,titlePadding:1,type:1,values:1,zindex:1},Ze="_vgsid_",Oy={point:{on:"click",fields:[Ze],toggle:"event.shiftKey",resolve:"global",clear:"dblclick"},interval:{on:"[pointerdown, window:pointerup] > window:pointermove!",encodings:["x","y"],translate:"[pointerdown, window:pointerup] > window:pointermove!",zoom:"wheel!",mark:{fill:"#333",fillOpacity:.125,stroke:"white"},resolve:"global",clear:"dblclick"}};function ya(e){return e==="legend"||!!(e!=null&&e.legend)}function As(e){return ya(e)&&X(e)}function ba(e){return!!(e!=null&&e.select)}function Pf(e){const t=[];for(const n of e||[]){if(ba(n))continue;const{expr:i,bind:r,...s}=n;if(r&&i){const o={...s,bind:r,init:i};t.push(o)}else{const o={...s,...i?{update:i}:{},...r?{bind:r}:{}};t.push(o)}}return t}function _y(e){return rs(e)||va(e)||xa(e)}function xa(e){return"concat"in e}function rs(e){return"vconcat"in e}function va(e){return"hconcat"in e}function zf({step:e,offsetIsDiscrete:t}){return t?e.for??"offset":"position"}function mt(e){return X(e)&&e.step!==void 0}function wc(e){return e.view||e.width||e.height}const Cc=20,Ry={align:1,bounds:1,center:1,columns:1,spacing:1},Iy=x(Ry);function Ly(e,t,n){const i=n[t],r={},{spacing:s,columns:o}=i;s!==void 0&&(r.spacing=s),o!==void 0&&(Qr(e)&&!Ki(e.facet)||xa(e))&&(r.columns=o),rs(e)&&(r.columns=1);for(const a of Iy)if(e[a]!==void 0)if(a==="spacing"){const c=e[a];r[a]=re(c)?c:{row:c.row??s,column:c.column??s}}else r[a]=e[a];return r}function io(e,t){return e[t]??e[t==="width"?"continuousWidth":"continuousHeight"]}function Nr(e,t){const n=Fr(e,t);return mt(n)?n.step:Df}function Fr(e,t){const n=e[t]??e[t==="width"?"discreteWidth":"discreteHeight"];return le(n,{step:e.step})}const Df=20,Py={continuousWidth:200,continuousHeight:200,step:Df},zy={background:"white",padding:5,timeFormat:"%b %d, %Y",countTitle:"Count of Records",view:Py,mark:Tm,arc:{},area:{},bar:_m,circle:{},geoshape:{},image:{},line:{},point:{},rect:Rm,rule:{color:"black"},square:{},text:{color:"black"},tick:Im,trail:{},boxplot:{size:14,extent:1.5,box:{},median:{color:"white"},outliers:{},rule:{},ticks:null},errorbar:{center:"mean",rule:!0,ticks:!1},errorband:{band:{opacity:.3},borders:!1},scale:fm,projection:{},legend:Ty,header:{titlePadding:10,labelPadding:10},headerColumn:{},headerRow:{},headerFacet:{},selection:Oy,style:{},title:{},facet:{spacing:Cc},concat:{spacing:Cc},normalizedNumberFormat:".0%"},$t=["#4c78a8","#f58518","#e45756","#72b7b2","#54a24b","#eeca3b","#b279a2","#ff9da6","#9d755d","#bab0ac"],Nc={text:11,guideLabel:10,guideTitle:11,groupTitle:13,groupSubtitle:12},Fc={blue:$t[0],orange:$t[1],red:$t[2],teal:$t[3],green:$t[4],yellow:$t[5],purple:$t[6],pink:$t[7],brown:$t[8],gray0:"#000",gray1:"#111",gray2:"#222",gray3:"#333",gray4:"#444",gray5:"#555",gray6:"#666",gray7:"#777",gray8:"#888",gray9:"#999",gray10:"#aaa",gray11:"#bbb",gray12:"#ccc",gray13:"#ddd",gray14:"#eee",gray15:"#fff"};function Dy(e={}){return{signals:[{name:"color",value:X(e)?{...Fc,...e}:Fc}],mark:{color:{signal:"color.blue"}},rule:{color:{signal:"color.gray0"}},text:{color:{signal:"color.gray0"}},style:{"guide-label":{fill:{signal:"color.gray0"}},"guide-title":{fill:{signal:"color.gray0"}},"group-title":{fill:{signal:"color.gray0"}},"group-subtitle":{fill:{signal:"color.gray0"}},cell:{stroke:{signal:"color.gray8"}}},axis:{domainColor:{signal:"color.gray13"},gridColor:{signal:"color.gray8"},tickColor:{signal:"color.gray13"}},range:{category:[{signal:"color.blue"},{signal:"color.orange"},{signal:"color.red"},{signal:"color.teal"},{signal:"color.green"},{signal:"color.yellow"},{signal:"color.purple"},{signal:"color.pink"},{signal:"color.brown"},{signal:"color.grey8"}]}}}function jy(e){return{signals:[{name:"fontSize",value:X(e)?{...Nc,...e}:Nc}],text:{fontSize:{signal:"fontSize.text"}},style:{"guide-label":{fontSize:{signal:"fontSize.guideLabel"}},"guide-title":{fontSize:{signal:"fontSize.guideTitle"}},"group-title":{fontSize:{signal:"fontSize.groupTitle"}},"group-subtitle":{fontSize:{signal:"fontSize.groupSubtitle"}}}}}function My(e){return{text:{font:e},style:{"guide-label":{font:e},"guide-title":{font:e},"group-title":{font:e},"group-subtitle":{font:e}}}}function jf(e){const t=x(e||{}),n={};for(const i of t){const r=e[i];n[i]=Zi(r)?gu(r):Le(r)}return n}function Uy(e){const t=x(e),n={};for(const i of t)n[i]=jf(e[i]);return n}const By=[...Yu,...yf,...If,"background","padding","legend","lineBreak","scale","style","title","view"];function Mf(e={}){const{color:t,font:n,fontSize:i,selection:r,...s}=e,o=Ir({},P(zy),n?My(n):{},t?Dy(t):{},i?jy(i):{},s||{});r&&vo(o,"selection",r,!0);const a=Te(o,By);for(const c of["background","lineBreak","padding"])o[c]&&(a[c]=Le(o[c]));for(const c of Yu)o[c]&&(a[c]=ke(o[c]));for(const c of yf)o[c]&&(a[c]=jf(o[c]));for(const c of If)o[c]&&(a[c]=ke(o[c]));return o.legend&&(a.legend=ke(o.legend)),o.scale&&(a.scale=ke(o.scale)),o.style&&(a.style=Uy(o.style)),o.title&&(a.title=ke(o.title)),o.view&&(a.view=ke(o.view)),a}const Wy=new Set(["view",...$m]),Gy=["color","fontSize","background","padding","facet","concat","numberFormat","numberFormatType","normalizedNumberFormat","normalizedNumberFormatType","timeFormat","countTitle","header","axisQuantitative","axisTemporal","axisDiscrete","axisPoint","axisXBand","axisXPoint","axisXDiscrete","axisXQuantitative","axisXTemporal","axisYBand","axisYPoint","axisYDiscrete","axisYQuantitative","axisYTemporal","scale","selection","overlay"],Hy={view:["continuousWidth","continuousHeight","discreteWidth","discreteHeight","step"],...km};function qy(e){e=P(e);for(const t of Gy)delete e[t];if(e.axis)for(const t in e.axis)Zi(e.axis[t])&&delete e.axis[t];if(e.legend)for(const t of Cy)delete e.legend[t];if(e.mark){for(const t of mc)delete e.mark[t];e.mark.tooltip&&X(e.mark.tooltip)&&delete e.mark.tooltip}e.params&&(e.signals=(e.signals||[]).concat(Pf(e.params)),delete e.params);for(const t of Wy){for(const i of mc)delete e[t][i];const n=Hy[t];if(n)for(const i of n)delete e[t][i];Xy(e,t)}for(const t of wy())delete e[t];Vy(e);for(const t in e)X(e[t])&&Q(e[t])&&delete e[t];return Q(e)?void 0:e}function Vy(e){const{titleMarkConfig:t,subtitleMarkConfig:n,subtitle:i}=pu(e.title);Q(t)||(e.style["group-title"]={...e.style["group-title"],...t}),Q(n)||(e.style["group-subtitle"]={...e.style["group-subtitle"],...n}),Q(i)?delete e.title:e.title=i}function Xy(e,t,n,i){const r=e[t];t==="view"&&(n="cell");const s={...r,...e.style[n??t]};Q(s)||(e.style[n??t]=s),delete e[t]}function ss(e){return"layer"in e}function Yy(e){return"repeat"in e}function Ky(e){return!k(e.repeat)&&e.repeat.layer}class Sa{map(t,n){return Qr(t)?this.mapFacet(t,n):Yy(t)?this.mapRepeat(t,n):va(t)?this.mapHConcat(t,n):rs(t)?this.mapVConcat(t,n):xa(t)?this.mapConcat(t,n):this.mapLayerOrUnit(t,n)}mapLayerOrUnit(t,n){if(ss(t))return this.mapLayer(t,n);if(Dt(t))return this.mapUnit(t,n);throw new Error(zo(t))}mapLayer(t,n){return{...t,layer:t.layer.map(i=>this.mapLayerOrUnit(i,n))}}mapHConcat(t,n){return{...t,hconcat:t.hconcat.map(i=>this.map(i,n))}}mapVConcat(t,n){return{...t,vconcat:t.vconcat.map(i=>this.map(i,n))}}mapConcat(t,n){const{concat:i,...r}=t;return{...r,concat:i.map(s=>this.map(s,n))}}mapFacet(t,n){return{...t,spec:this.map(t.spec,n)}}mapRepeat(t,n){return{...t,spec:this.map(t.spec,n)}}}const Qy={zero:1,center:1,normalize:1};function Jy(e){return e in Qy}const Zy=new Set([qu,Vr,qr,xr,Yr,Zo,ea,Xr,Vu,Jo]),eb=new Set([Vr,qr,qu]);function Mn(e){return S(e)&&ii(e)==="quantitative"&&!e.bin}function kc(e,t,{orient:n,type:i}){const r=t==="x"?"y":"radius",s=t==="x"&&["bar","area"].includes(i),o=e[t],a=e[r];if(S(o)&&S(a))if(Mn(o)&&Mn(a)){if(o.stack)return t;if(a.stack)return r;const c=S(o)&&!!o.aggregate,l=S(a)&&!!a.aggregate;if(c!==l)return c?t:r;if(s){if(n==="vertical")return r;if(n==="horizontal")return t}}else{if(Mn(o))return t;if(Mn(a))return r}else{if(Mn(o))return s&&n==="vertical"?void 0:t;if(Mn(a))return s&&n==="horizontal"?void 0:r}}function tb(e){switch(e){case"x":return"y";case"y":return"x";case"theta":return"radius";case"radius":return"theta"}}function Uf(e,t){var h,m;const n=gt(e)?e:{type:e},i=n.type;if(!Zy.has(i))return null;const r=kc(t,"x",n)||kc(t,"theta",n);if(!r)return null;const s=t[r],o=S(s)?E(s,{}):void 0,a=tb(r),c=[],l=new Set;if(t[a]){const y=t[a],b=S(y)?E(y,{}):void 0;b&&b!==o&&(c.push(a),l.add(b))}const u=a==="x"?"xOffset":"yOffset",f=t[u],d=S(f)?E(f,{}):void 0;d&&d!==o&&(c.push(u),l.add(d));const g=gg.reduce((y,b)=>{if(b!=="tooltip"&&yn(t,b)){const C=t[b];for(const D of ce(C)){const w=ht(D);if(w.aggregate)continue;const F=E(w,{});(!F||!l.has(F))&&y.push({channel:b,fieldDef:w})}}return y},[]);let p;return s.stack!==void 0?di(s.stack)?p=s.stack?"zero":null:p=s.stack:eb.has(i)&&(p="zero"),!p||!Jy(p)||ua(t)&&g.length===0?null:(h=s==null?void 0:s.scale)!=null&&h.type&&((m=s==null?void 0:s.scale)==null?void 0:m.type)!==Ae.LINEAR?(s!=null&&s.stack&&v(Th(s.scale.type)),null):z(t[vt(r)])?(s.stack!==void 0&&v(kh(r)),null):(S(s)&&s.aggregate&&!Ng.has(s.aggregate)&&v(Ah(s.aggregate)),{groupbyChannels:c,groupbyFields:l,fieldChannel:r,impute:s.impute===null?!1:sn(i),stackBy:g,offset:p})}function Bf(e,t,n){const i=ke(e),r=V("orient",i,n);if(i.orient=sb(i.type,t,r),r!==void 0&&r!==i.orient&&v(dh(i.orient,r)),i.type==="bar"&&i.orient){const c=V("cornerRadiusEnd",i,n);if(c!==void 0){const l=i.orient==="horizontal"&&t.x2||i.orient==="vertical"&&t.y2?["cornerRadius"]:Om[i.orient];for(const u of l)i[u]=c;i.cornerRadiusEnd!==void 0&&delete i.cornerRadiusEnd}}const s=V("opacity",i,n),o=V("fillOpacity",i,n);return s===void 0&&o===void 0&&(i.opacity=ib(i.type,t)),V("cursor",i,n)===void 0&&(i.cursor=nb(i,t,n)),i}function nb(e,t,n){return t.href||e.href||V("href",e,n)?"pointer":e.cursor}function ib(e,t){if(G([Yr,Jo,Zo,ea],e)&&!ua(t))return .7}function rb(e,t,{graticule:n}){if(n)return!1;const i=_t("filled",e,t),r=e.type;return le(i,r!==Yr&&r!==Xr&&r!==xr)}function sb(e,t,n){switch(e){case Yr:case Zo:case ea:case Vu:case Sm:case vm:return}const{x:i,y:r,x2:s,y2:o}=t;switch(e){case Vr:if(S(i)&&(xe(i.bin)||S(r)&&r.aggregate&&!i.aggregate))return"vertical";if(S(r)&&(xe(r.bin)||S(i)&&i.aggregate&&!r.aggregate))return"horizontal";if(o||s){if(n)return n;if(!s)return(S(i)&&i.type===wn&&!te(i.bin)||Sr(i))&&S(r)&&xe(r.bin)?"horizontal":"vertical";if(!o)return(S(r)&&r.type===wn&&!te(r.bin)||Sr(r))&&S(i)&&xe(i.bin)?"vertical":"horizontal"}case xr:if(s&&!(S(i)&&xe(i.bin))&&o&&!(S(r)&&xe(r.bin)))return;case qr:if(o)return S(r)&&xe(r.bin)?"horizontal":"vertical";if(s)return S(i)&&xe(i.bin)?"vertical":"horizontal";if(e===xr){if(i&&!r)return"vertical";if(r&&!i)return"horizontal"}case Xr:case Jo:{const a=vc(i),c=vc(r);if(n)return n;if(a&&!c)return e!=="tick"?"horizontal":"vertical";if(!a&&c)return e!=="tick"?"vertical":"horizontal";if(a&&c)return"vertical";{const l=Ne(i)&&i.type===ti,u=Ne(r)&&r.type===ti;if(l&&!u)return"vertical";if(!l&&u)return"horizontal"}return}}return"vertical"}function ob(e){const{point:t,line:n,...i}=e;return x(i).length>1?i:i.type}function ab(e){for(const t of["line","area","rule","trail"])e[t]&&(e={...e,[t]:Te(e[t],["point","line"])});return e}function Os(e,t={},n){return e.point==="transparent"?{opacity:0}:e.point?X(e.point)?e.point:{}:e.point!==void 0?null:t.point||n.shape?X(t.point)?t.point:{}:void 0}function Tc(e,t={}){return e.line?e.line===!0?{}:e.line:e.line!==void 0?null:t.line?t.line===!0?{}:t.line:void 0}class cb{constructor(){this.name="path-overlay"}hasMatchingType(t,n){if(Dt(t)){const{mark:i,encoding:r}=t,s=gt(i)?i:{type:i};switch(s.type){case"line":case"rule":case"trail":return!!Os(s,n[s.type],r);case"area":return!!Os(s,n[s.type],r)||!!Tc(s,n[s.type])}}return!1}run(t,n,i){const{config:r}=n,{params:s,projection:o,mark:a,name:c,encoding:l,...u}=t,f=is(l,r),d=gt(a)?a:{type:a},g=Os(d,r[d.type],f),p=d.type==="area"&&Tc(d,r[d.type]),h=[{name:c,...s?{params:s}:{},mark:ob({...d.type==="area"&&d.opacity===void 0&&d.fillOpacity===void 0?{opacity:.7}:{},...d}),encoding:Te(f,["shape"])}],m=Uf(Bf(d,f,r),f);let y=f;if(m){const{fieldChannel:b,offset:C}=m;y={...f,[b]:{...f[b],...C?{stack:C}:{}}}}return y=Te(y,["y2","x2"]),p&&h.push({...o?{projection:o}:{},mark:{type:"line",...Qn(d,["clip","interpolate","tension","tooltip"]),...p},encoding:y}),g&&h.push({...o?{projection:o}:{},mark:{type:"point",opacity:1,filled:!0,...Qn(d,["clip","tooltip"]),...g},encoding:y}),i({...u,layer:h},{...n,config:ab(r)})}}function lb(e,t){return t?Ki(e)?Gf(e,t):Wf(e,t):e}function _s(e,t){return t?Gf(e,t):e}function ro(e,t,n){const i=t[e];if(Hm(i)){if(i.repeat in n)return{...t,[e]:n[i.repeat]};v(qg(i.repeat));return}return t}function Wf(e,t){if(e=ro("field",e,t),e!==void 0){if(e===null)return null;if(oa(e)&&ft(e.sort)){const n=ro("field",e.sort,t);e={...e,...n?{sort:n}:{}}}return e}}function Ac(e,t){if(S(e))return Wf(e,t);{const n=ro("datum",e,t);return n!==e&&!n.type&&(n.type="nominal"),n}}function Oc(e,t){if(z(e)){const n=Ac(e,t);if(n)return n;if(Jr(e))return{condition:e.condition}}else{if(Qi(e)){const n=Ac(e.condition,t);if(n)return{...e,condition:n};{const{condition:i,...r}=e;return r}}return e}}function Gf(e,t){const n={};for(const i in e)if(Gn(e,i)){const r=e[i];if(k(r))n[i]=r.map(s=>Oc(s,t)).filter(s=>s);else{const s=Oc(r,t);s!==void 0&&(n[i]=s)}}return n}class ub{constructor(){this.name="RuleForRangedLine"}hasMatchingType(t){if(Dt(t)){const{encoding:n,mark:i}=t;if(i==="line"||gt(i)&&i.type==="line")for(const r of fg){const s=_n(r),o=n[s];if(n[r]&&(S(o)&&!xe(o.bin)||Et(o)))return!0}}return!1}run(t,n,i){const{encoding:r,mark:s}=t;return v(fh(!!r.x2,!!r.y2)),i({...t,mark:X(s)?{...s,type:"rule"}:"rule"},n)}}class fb extends Sa{constructor(){super(...arguments),this.nonFacetUnitNormalizers=[py,my,$y,new cb,new ub]}map(t,n){if(Dt(t)){const i=yn(t.encoding,Ft),r=yn(t.encoding,kt),s=yn(t.encoding,Pr);if(i||r||s)return this.mapFacetedUnit(t,n)}return super.map(t,n)}mapUnit(t,n){const{parentEncoding:i,parentProjection:r}=n,s=_s(t.encoding,n.repeater),o={...t,...t.name?{name:[n.repeaterPrefix,t.name].filter(c=>c).join("_")}:{},...s?{encoding:s}:{}};if(i||r)return this.mapUnitWithParentEncodingOrProjection(o,n);const a=this.mapLayerOrUnit.bind(this);for(const c of this.nonFacetUnitNormalizers)if(c.hasMatchingType(o,n.config))return c.run(o,n,a);return o}mapRepeat(t,n){return Ky(t)?this.mapLayerRepeat(t,n):this.mapNonLayerRepeat(t,n)}mapLayerRepeat(t,n){const{repeat:i,spec:r,...s}=t,{row:o,column:a,layer:c}=i,{repeater:l={},repeaterPrefix:u=""}=n;return o||a?this.mapRepeat({...t,repeat:{...o?{row:o}:{},...a?{column:a}:{}},spec:{repeat:{layer:c},spec:r}},n):{...s,layer:c.map(f=>{const d={...l,layer:f},g=`${(r.name?`${r.name}_`:"")+u}child__layer_${se(f)}`,p=this.mapLayerOrUnit(r,{...n,repeater:d,repeaterPrefix:g});return p.name=g,p})}}mapNonLayerRepeat(t,n){const{repeat:i,spec:r,data:s,...o}=t;!k(i)&&t.columns&&(t=Te(t,["columns"]),v(uc("repeat")));const a=[],{repeater:c={},repeaterPrefix:l=""}=n,u=!k(i)&&i.row||[c?c.row:null],f=!k(i)&&i.column||[c?c.column:null],d=k(i)&&i||[c?c.repeat:null];for(const p of d)for(const h of u)for(const m of f){const y={repeat:p,row:h,column:m,layer:c.layer},b=(r.name?`${r.name}_`:"")+l+"child__"+(k(i)?`${se(p)}`:(i.row?`row_${se(h)}`:"")+(i.column?`column_${se(m)}`:"")),C=this.map(r,{...n,repeater:y,repeaterPrefix:b});C.name=b,a.push(Te(C,["data"]))}const g=k(i)?t.columns:i.column?i.column.length:1;return{data:r.data??s,align:"all",...o,columns:g,concat:a}}mapFacet(t,n){const{facet:i}=t;return Ki(i)&&t.columns&&(t=Te(t,["columns"]),v(uc("facet"))),super.mapFacet(t,n)}mapUnitWithParentEncodingOrProjection(t,n){const{encoding:i,projection:r}=t,{parentEncoding:s,parentProjection:o,config:a}=n,c=Rc({parentProjection:o,projection:r}),l=_c({parentEncoding:s,encoding:_s(i,n.repeater)});return this.mapUnit({...t,...c?{projection:c}:{},...l?{encoding:l}:{}},{config:a})}mapFacetedUnit(t,n){const{row:i,column:r,facet:s,...o}=t.encoding,{mark:a,width:c,projection:l,height:u,view:f,params:d,encoding:g,...p}=t,{facetMapping:h,layout:m}=this.getFacetMappingAndLayout({row:i,column:r,facet:s},n),y=_s(o,n.repeater);return this.mapFacet({...p,...m,facet:h,spec:{...c?{width:c}:{},...u?{height:u}:{},...f?{view:f}:{},...l?{projection:l}:{},mark:a,encoding:y,...d?{params:d}:{}}},n)}getFacetMappingAndLayout(t,n){const{row:i,column:r,facet:s}=t;if(i||r){s&&v(lh([...i?[Ft]:[],...r?[kt]:[]]));const o={},a={};for(const c of[Ft,kt]){const l=t[c];if(l){const{align:u,center:f,spacing:d,columns:g,...p}=l;o[c]=p;for(const h of["align","center","spacing"])l[h]!==void 0&&(a[h]??(a[h]={}),a[h][c]=l[h])}}return{facetMapping:o,layout:a}}else{const{align:o,center:a,spacing:c,columns:l,...u}=s;return{facetMapping:lb(u,n.repeater),layout:{...o?{align:o}:{},...a?{center:a}:{},...c?{spacing:c}:{},...l?{columns:l}:{}}}}}mapLayer(t,{parentEncoding:n,parentProjection:i,...r}){const{encoding:s,projection:o,...a}=t,c={...r,parentEncoding:_c({parentEncoding:n,encoding:s,layer:!0}),parentProjection:Rc({parentProjection:i,projection:o})};return super.mapLayer({...a,...t.name?{name:[c.repeaterPrefix,t.name].filter(l=>l).join("_")}:{}},c)}}function _c({parentEncoding:e,encoding:t={},layer:n}){let i={};if(e){const r=new Set([...x(e),...x(t)]);for(const s of r){const o=t[s],a=e[s];if(z(o)){const c={...a,...o};i[s]=c}else Qi(o)?i[s]={...o,condition:{...a,...o.condition}}:o||o===null?i[s]=o:(n||Je(a)||T(a)||z(a)||k(a))&&(i[s]=a)}}else i=t;return!i||Q(i)?void 0:i}function Rc(e){const{parentProjection:t,projection:n}=e;return t&&n&&v(Jg({parentProjection:t,projection:n})),n??t}function Ea(e){return"filter"in e}function db(e){return(e==null?void 0:e.stop)!==void 0}function Hf(e){return"lookup"in e}function pb(e){return"data"in e}function gb(e){return"param"in e}function hb(e){return"pivot"in e}function mb(e){return"density"in e}function yb(e){return"quantile"in e}function bb(e){return"regression"in e}function xb(e){return"loess"in e}function vb(e){return"sample"in e}function Sb(e){return"window"in e}function Eb(e){return"joinaggregate"in e}function $b(e){return"flatten"in e}function wb(e){return"calculate"in e}function qf(e){return"bin"in e}function Cb(e){return"impute"in e}function Nb(e){return"timeUnit"in e}function Fb(e){return"aggregate"in e}function kb(e){return"stack"in e}function Tb(e){return"fold"in e}function Ab(e){return"extent"in e&&!("density"in e)}function Ob(e){return e.map(t=>Ea(t)?{filter:Hn(t.filter,rm)}:t)}class _b extends Sa{map(t,n){return n.emptySelections??(n.emptySelections={}),n.selectionPredicates??(n.selectionPredicates={}),t=Ic(t,n),super.map(t,n)}mapLayerOrUnit(t,n){if(t=Ic(t,n),t.encoding){const i={};for(const[r,s]of Gt(t.encoding))i[r]=Vf(s,n);t={...t,encoding:i}}return super.mapLayerOrUnit(t,n)}mapUnit(t,n){const{selection:i,...r}=t;return i?{...r,params:Gt(i).map(([s,o])=>{const{init:a,bind:c,empty:l,...u}=o;u.type==="single"?(u.type="point",u.toggle=!1):u.type==="multi"&&(u.type="point"),n.emptySelections[s]=l!=="none";for(const f of ve(n.selectionPredicates[s]??{}))f.empty=l!=="none";return{name:s,value:a,select:u,bind:c}})}:t}}function Ic(e,t){const{transform:n,...i}=e;if(n){const r=n.map(s=>{if(Ea(s))return{filter:so(s,t)};if(qf(s)&&Rn(s.bin))return{...s,bin:Xf(s.bin)};if(Hf(s)){const{selection:o,...a}=s.from;return o?{...s,from:{param:o,...a}}:s}return s});return{...i,transform:r}}return e}function Vf(e,t){var i,r;const n=P(e);if(S(n)&&Rn(n.bin)&&(n.bin=Xf(n.bin)),Pn(n)&&((r=(i=n.scale)==null?void 0:i.domain)!=null&&r.selection)){const{selection:s,...o}=n.scale.domain;n.scale.domain={...o,...s?{param:s}:{}}}if(Jr(n))if(k(n.condition))n.condition=n.condition.map(s=>{const{selection:o,param:a,test:c,...l}=s;return a?s:{...l,test:so(s,t)}});else{const{selection:s,param:o,test:a,...c}=Vf(n.condition,t);n.condition=o?n.condition:{...c,test:so(n.condition,t)}}return n}function Xf(e){const t=e.extent;if(t!=null&&t.selection){const{selection:n,...i}=t;return{...e,extent:{...i,param:n}}}return e}function so(e,t){const n=i=>Hn(i,r=>{var s;const o=t.emptySelections[r]??!0,a={param:r,empty:o};return(s=t.selectionPredicates)[r]??(s[r]=[]),t.selectionPredicates[r].push(a),a});return e.selection?n(e.selection):Hn(e.test||e.filter,i=>i.selection?n(i.selection):i)}class oo extends Sa{map(t,n){const i=n.selections??[];if(t.params&&!Dt(t)){const r=[];for(const s of t.params)ba(s)?i.push(s):r.push(s);t.params=r}return n.selections=i,super.map(t,n)}mapUnit(t,n){const i=n.selections;if(!i||!i.length)return t;const r=(n.path??[]).concat(t.name),s=[];for(const o of i)if(!o.views||!o.views.length)s.push(o);else for(const a of o.views)(I(a)&&(a===t.name||r.includes(a))||k(a)&&a.map(c=>r.indexOf(c)).every((c,l,u)=>c!==-1&&(l===0||c>u[l-1])))&&s.push(o);return s.length&&(t.params=s),t}}for(const e of["mapFacet","mapRepeat","mapHConcat","mapVConcat","mapLayer"]){const t=oo.prototype[e];oo.prototype[e]=function(n,i){return t.call(this,n,Rb(n,i))}}function Rb(e,t){return e.name?{...t,path:(t.path??[]).concat(e.name)}:t}function Yf(e,t){t===void 0&&(t=Mf(e.config));const n=zb(e,t),{width:i,height:r}=e,s=Db(n,{width:i,height:r,autosize:e.autosize},t);return{...n,...s?{autosize:s}:{}}}const Ib=new fb,Lb=new _b,Pb=new oo;function zb(e,t={}){const n={config:t};return Pb.map(Ib.map(Lb.map(e,n),n),n)}function Lc(e){return I(e)?{type:e}:e??{}}function Db(e,t,n){let{width:i,height:r}=t;const s=Dt(e)||ss(e),o={};s?i=="container"&&r=="container"?(o.type="fit",o.contains="padding"):i=="container"?(o.type="fit-x",o.contains="padding"):r=="container"&&(o.type="fit-y",o.contains="padding"):(i=="container"&&(v(oc("width")),i=void 0),r=="container"&&(v(oc("height")),r=void 0));const a={type:"pad",...o,...n?Lc(n.autosize):{},...Lc(e.autosize)};if(a.type==="fit"&&!s&&(v(Lg),a.type="pad"),i=="container"&&!(a.type=="fit"||a.type=="fit-x")&&v(ac("width")),r=="container"&&!(a.type=="fit"||a.type=="fit-y")&&v(ac("height")),!Pe(a,{type:"pad"}))return a}function jb(e){return e==="fit"||e==="fit-x"||e==="fit-y"}function Mb(e){return e?`fit-${Ur(e)}`:"fit"}const Ub=["background","padding"];function Pc(e,t){const n={};for(const i of Ub)e&&e[i]!==void 0&&(n[i]=Le(e[i]));return t&&(n.params=e.params),n}class jt{constructor(t={},n={}){this.explicit=t,this.implicit=n}clone(){return new jt(P(this.explicit),P(this.implicit))}combine(){return{...this.explicit,...this.implicit}}get(t){return le(this.explicit[t],this.implicit[t])}getWithExplicit(t){return this.explicit[t]!==void 0?{explicit:!0,value:this.explicit[t]}:this.implicit[t]!==void 0?{explicit:!1,value:this.implicit[t]}:{explicit:!1,value:void 0}}setWithExplicit(t,{value:n,explicit:i}){n!==void 0&&this.set(t,n,i)}set(t,n,i){return delete this[i?"implicit":"explicit"][t],this[i?"explicit":"implicit"][t]=n,this}copyKeyFromSplit(t,{explicit:n,implicit:i}){n[t]!==void 0?this.set(t,n[t],!0):i[t]!==void 0&&this.set(t,i[t],!1)}copyKeyFromObject(t,n){n[t]!==void 0&&this.set(t,n[t],!0)}copyAll(t){for(const n of x(t.combine())){const i=t.getWithExplicit(n);this.setWithExplicit(n,i)}}}function lt(e){return{explicit:!0,value:e}}function Ie(e){return{explicit:!1,value:e}}function Kf(e){return(t,n,i,r)=>{const s=e(t.value,n.value);return s>0?t:s<0?n:os(t,n,i,r)}}function os(e,t,n,i){return e.explicit&&t.explicit&&v(Sh(n,i,e.value,t.value)),e}function Vt(e,t,n,i,r=os){return e===void 0||e.value===void 0?t:e.explicit&&!t.explicit?e:t.explicit&&!e.explicit?t:Pe(e.value,t.value)?e:r(e,t,n,i)}class Bb extends jt{constructor(t={},n={},i=!1){super(t,n),this.explicit=t,this.implicit=n,this.parseNothing=i}clone(){const t=super.clone();return t.parseNothing=this.parseNothing,t}}function oi(e){return"url"in e}function Di(e){return"values"in e}function Qf(e){return"name"in e&&!oi(e)&&!Di(e)&&!Wt(e)}function Wt(e){return e&&(Jf(e)||Zf(e)||$a(e))}function Jf(e){return"sequence"in e}function Zf(e){return"sphere"in e}function $a(e){return"graticule"in e}var ee;(function(e){e[e.Raw=0]="Raw",e[e.Main=1]="Main",e[e.Row=2]="Row",e[e.Column=3]="Column",e[e.Lookup=4]="Lookup"})(ee||(ee={}));function ed(e){const{signals:t,hasLegend:n,index:i,...r}=e;return r.field=Ue(r.field),r}function Fn(e,t=!0,n=Np){if(k(e)){const i=e.map(r=>Fn(r,t,n));return t?`[${i.join(", ")}]`:i}else if(In(e))return n(t?$n(e):qh(e));return t?n(J(e)):e}function Wb(e,t){for(const n of ve(e.component.selection??{})){const i=n.name;let r=`${i}${Yt}, ${n.resolve==="global"?"true":`{unit: ${bn(e)}}`}`;for(const s of us)s.defined(n)&&(s.signals&&(t=s.signals(e,n,t)),s.modifyExpr&&(r=s.modifyExpr(e,n,r)));t.push({name:i+Sx,on:[{events:{signal:n.name+Yt},update:`modify(${B(n.name+kn)}, ${r})`}]})}return wa(t)}function Gb(e,t){if(e.component.selection&&x(e.component.selection).length){const n=B(e.getName("cell"));t.unshift({name:"facet",value:{},on:[{events:pi("pointermove","scope"),update:`isTuple(facet) ? facet : group(${n}).datum`}]})}return wa(t)}function Hb(e,t){let n=!1;for(const i of ve(e.component.selection??{})){const r=i.name,s=B(r+kn);if(t.filter(a=>a.name===r).length===0){const a=i.resolve==="global"?"union":i.resolve,c=i.type==="point"?", true, true)":")";t.push({name:i.name,update:`${bd}(${s}, ${B(a)}${c}`})}n=!0;for(const a of us)a.defined(i)&&a.topLevelSignals&&(t=a.topLevelSignals(e,i,t))}return n&&t.filter(r=>r.name==="unit").length===0&&t.unshift({name:"unit",value:{},on:[{events:"pointermove",update:"isTuple(group()) ? group() : unit"}]}),wa(t)}function qb(e,t){const n=[...t],i=bn(e,{escape:!1});for(const r of ve(e.component.selection??{})){const s={name:r.name+kn};if(r.project.hasSelectionId&&(s.transform=[{type:"collect",sort:{field:Ze}}]),r.init){const a=r.project.items.map(ed);s.values=r.project.hasSelectionId?r.init.map(c=>({unit:i,[Ze]:Fn(c,!1)[0]})):r.init.map(c=>({unit:i,fields:a,values:Fn(c,!1)}))}n.filter(a=>a.name===r.name+kn).length||n.push(s)}return n}function td(e,t){for(const n of ve(e.component.selection??{}))for(const i of us)i.defined(n)&&i.marks&&(t=i.marks(e,n,t));return t}function Vb(e,t){for(const n of e.children)ae(n)&&(t=td(n,t));return t}function Xb(e,t,n,i){const r=$d(e,t.param,t);return{signal:De(n.get("type"))&&k(i)&&i[0]>i[1]?`isValid(${r}) && reverse(${r})`:r}}function wa(e){return e.map(t=>(t.on&&!t.on.length&&delete t.on,t))}class Y{constructor(t,n){this.debugName=n,this._children=[],this._parent=null,t&&(this.parent=t)}clone(){throw new Error("Cannot clone node")}get parent(){return this._parent}set parent(t){this._parent=t,t&&t.addChild(this)}get children(){return this._children}numChildren(){return this._children.length}addChild(t,n){if(this._children.includes(t)){v(Yg);return}n!==void 0?this._children.splice(n,0,t):this._children.push(t)}removeChild(t){const n=this._children.indexOf(t);return this._children.splice(n,1),n}remove(){let t=this._parent.removeChild(this);for(const n of this._children)n._parent=this._parent,this._parent.addChild(n,t++)}insertAsParentOf(t){const n=t.parent;n.removeChild(this),this.parent=n,t.parent=this}swapWithParent(){const t=this._parent,n=t.parent;for(const r of this._children)r.parent=t;this._children=[],t.removeChild(this);const i=t.parent.removeChild(t);this._parent=n,n.addChild(this,i),t.parent=this}}class Ce extends Y{clone(){const t=new this.constructor;return t.debugName=`clone_${this.debugName}`,t._source=this._source,t._name=`clone_${this._name}`,t.type=this.type,t.refCounts=this.refCounts,t.refCounts[t._name]=0,t}constructor(t,n,i,r){super(t,n),this.type=i,this.refCounts=r,this._source=this._name=n,this.refCounts&&!(this._name in this.refCounts)&&(this.refCounts[this._name]=0)}dependentFields(){return new Set}producedFields(){return new Set}hash(){return this._hash===void 0&&(this._hash=`Output ${Kl()}`),this._hash}getSource(){return this.refCounts[this._name]++,this._source}isRequired(){return!!this.refCounts[this._name]}setSource(t){this._source=t}}function Rs(e){return e.as!==void 0}function zc(e){return`${e}_end`}class dt extends Y{clone(){return new dt(null,P(this.timeUnits))}constructor(t,n){super(t),this.timeUnits=n}static makeFromEncoding(t,n){const i=n.reduceFieldDef((r,s,o)=>{const{field:a,timeUnit:c}=s;if(c){let l;if(Ln(c)){if(ae(n)){const{mark:u,markDef:f,config:d}=n,g=qt({fieldDef:s,markDef:f,config:d});(zi(u)||g)&&(l={timeUnit:me(c),field:a})}}else l={as:E(s,{forAs:!0}),field:a,timeUnit:c};if(ae(n)){const{mark:u,markDef:f,config:d}=n,g=qt({fieldDef:s,markDef:f,config:d});zi(u)&&ue(o)&&g!==.5&&(l.rectBandPosition=g)}l&&(r[W(l)]=l)}return r},{});return Q(i)?null:new dt(t,i)}static makeFromTransform(t,n){const{timeUnit:i,...r}={...n},s=me(i),o={...r,timeUnit:s};return new dt(t,{[W(o)]:o})}merge(t){this.timeUnits={...this.timeUnits};for(const n in t.timeUnits)this.timeUnits[n]||(this.timeUnits[n]=t.timeUnits[n]);for(const n of t.children)t.removeChild(n),n.parent=this;t.remove()}removeFormulas(t){const n={};for(const[i,r]of Gt(this.timeUnits)){const s=Rs(r)?r.as:`${r.field}_end`;t.has(s)||(n[i]=r)}this.timeUnits=n}producedFields(){return new Set(ve(this.timeUnits).map(t=>Rs(t)?t.as:zc(t.field)))}dependentFields(){return new Set(ve(this.timeUnits).map(t=>t.field))}hash(){return`TimeUnit ${W(this.timeUnits)}`}assemble(){const t=[];for(const n of ve(this.timeUnits)){const{rectBandPosition:i}=n,r=me(n.timeUnit);if(Rs(n)){const{field:s,as:o}=n,{unit:a,utc:c,...l}=r,u=[o,`${o}_end`];t.push({field:Ue(s),type:"timeunit",...a?{units:Hr(a)}:{},...c?{timezone:"utc"}:{},...l,as:u}),t.push(...Dc(u,i,r))}else if(n){const{field:s}=n,o=s.replaceAll("\\.","."),a=nd({timeUnit:r,field:o}),c=zc(o);t.push({type:"formula",expr:a,as:c}),t.push(...Dc([o,c],i,r))}}return t}}const as="offsetted_rect_start",cs="offsetted_rect_end";function nd({timeUnit:e,field:t,reverse:n}){const{unit:i,utc:r}=e,s=_u(i),{part:o,step:a}=Pu(s,e.step);return`${r?"utcOffset":"timeOffset"}('${o}', datum['${t}'], ${n?-a:a})`}function Dc([e,t],n,i){if(n!==void 0&&n!==.5){const r=`datum['${e}']`,s=`datum['${t}']`;return[{type:"formula",expr:jc([nd({timeUnit:i,field:e,reverse:!0}),r],n+.5),as:`${e}_${as}`},{type:"formula",expr:jc([r,s],n+.5),as:`${e}_${cs}`}]}return[]}function jc([e,t],n){return`${1-n} * ${e} + ${n} * ${t}`}const er="_tuple_fields";class Yb{constructor(...t){this.items=t,this.hasChannel={},this.hasField={},this.hasSelectionId=!1}}const Kb={defined:()=>!0,parse:(e,t,n)=>{const i=t.name,r=t.project??(t.project=new Yb),s={},o={},a=new Set,c=(p,h)=>{const m=h==="visual"?p.channel:p.field;let y=se(`${i}_${m}`);for(let b=1;a.has(y);b++)y=se(`${i}_${m}_${b}`);return a.add(y),{[h]:y}},l=t.type,u=e.config.selection[l],f=n.value!==void 0?ce(n.value):null;let{fields:d,encodings:g}=X(n.select)?n.select:{};if(!d&&!g&&f){for(const p of f)if(X(p))for(const h of x(p))ug(h)?(g||(g=[])).push(h):l==="interval"?(v(Hg),g=u.encodings):(d??(d=[])).push(h)}!d&&!g&&(g=u.encodings,"fields"in u&&(d=u.fields));for(const p of g??[]){const h=e.fieldDef(p);if(h){let m=h.field;if(h.aggregate){v(Pg(p,h.aggregate));continue}else if(!m){v(lc(p));continue}if(h.timeUnit&&!Ln(h.timeUnit)){m=e.vgField(p);const y={timeUnit:h.timeUnit,as:m,field:h.field};o[W(y)]=y}if(!s[m]){const y=l==="interval"&&zt(p)&&De(e.getScaleComponent(p).get("type"))?"R":h.bin?"R-RE":"E",b={field:m,channel:p,type:y,index:r.items.length};b.signals={...c(b,"data"),...c(b,"visual")},r.items.push(s[m]=b),r.hasField[m]=s[m],r.hasSelectionId=r.hasSelectionId||m===Ze,nu(p)?(b.geoChannel=p,b.channel=tu(p),r.hasChannel[b.channel]=s[m]):r.hasChannel[p]=s[m]}}else v(lc(p))}for(const p of d??[]){if(r.hasField[p])continue;const h={type:"E",field:p,index:r.items.length};h.signals={...c(h,"data")},r.items.push(h),r.hasField[p]=h,r.hasSelectionId=r.hasSelectionId||p===Ze}f&&(t.init=f.map(p=>r.items.map(h=>X(p)?p[h.geoChannel||h.channel]!==void 0?p[h.geoChannel||h.channel]:p[h.field]:p))),Q(o)||(r.timeUnit=new dt(null,o))},signals:(e,t,n)=>{const i=t.name+er;return n.filter(s=>s.name===i).length>0||t.project.hasSelectionId?n:n.concat({name:i,value:t.project.items.map(ed)})}},Tt={defined:e=>e.type==="interval"&&e.resolve==="global"&&e.bind&&e.bind==="scales",parse:(e,t)=>{const n=t.scales=[];for(const i of t.project.items){const r=i.channel;if(!zt(r))continue;const s=e.getScaleComponent(r),o=s?s.get("type"):void 0;if(o=="sequential"&&v(Mg),!s||!De(o)){v(jg);continue}s.set("selectionExtent",{param:t.name,field:i.field},!0),n.push(i)}},topLevelSignals:(e,t,n)=>{const i=t.scales.filter(o=>n.filter(a=>a.name===o.signals.data).length===0);if(!e.parent||Mc(e)||i.length===0)return n;const r=n.filter(o=>o.name===t.name)[0];let s=r.update;if(s.indexOf(bd)>=0)r.update=`{${i.map(o=>`${B(Ue(o.field))}: ${o.signals.data}`).join(", ")}}`;else{for(const o of i){const a=`${B(Ue(o.field))}: ${o.signals.data}`;s.includes(a)||(s=`${s.substring(0,s.length-1)}, ${a}}`)}r.update=s}return n.concat(i.map(o=>({name:o.signals.data})))},signals:(e,t,n)=>{if(e.parent&&!Mc(e))for(const i of t.scales){const r=n.find(s=>s.name===i.signals.data);r.push="outer",delete r.value,delete r.update}return n}};function ao(e,t){return`domain(${B(e.scaleName(t))})`}function Mc(e){return e.parent&&vi(e.parent)&&!e.parent.parent}const Xn="_brush",id="_scale_trigger",Ei="geo_interval_init_tick",rd="_init",Qb="_center",Jb={defined:e=>e.type==="interval",parse:(e,t,n)=>{var i;if(e.hasProjection){const r={...X(n.select)?n.select:{}};r.fields=[Ze],r.encodings||(r.encodings=n.value?x(n.value):[it,nt]),n.select={type:"interval",...r}}if(t.translate&&!Tt.defined(t)){const r=`!event.item || event.item.mark.name !== ${B(t.name+Xn)}`;for(const s of t.events){if(!s.between){v(`${s} is not an ordered event stream for interval selections.`);continue}const o=ce((i=s.between[0]).filter??(i.filter=[]));o.indexOf(r)<0&&o.push(r)}}},signals:(e,t,n)=>{const i=t.name,r=i+Yt,s=ve(t.project.hasChannel).filter(a=>a.channel===oe||a.channel===be),o=t.init?t.init[0]:null;if(n.push(...s.reduce((a,c)=>a.concat(Zb(e,t,c,o&&o[c.index])),[])),e.hasProjection){const a=B(e.projectionName()),c=e.projectionName()+Qb,{x:l,y:u}=t.project.hasChannel,f=l&&l.signals.visual,d=u&&u.signals.visual,g=l?o&&o[l.index]:`${c}[0]`,p=u?o&&o[u.index]:`${c}[1]`,h=w=>e.getSizeSignalRef(w).signal,m=`[[${f?f+"[0]":"0"}, ${d?d+"[0]":"0"}],[${f?f+"[1]":h("width")}, ${d?d+"[1]":h("height")}]]`;o&&(n.unshift({name:i+rd,init:`[scale(${a}, [${l?g[0]:g}, ${u?p[0]:p}]), scale(${a}, [${l?g[1]:g}, ${u?p[1]:p}])]`}),(!l||!u)&&(n.find(F=>F.name===c)||n.unshift({name:c,update:`invert(${a}, [${h("width")}/2, ${h("height")}/2])`})));const y=`intersect(${m}, {markname: ${B(e.getName("marks"))}}, unit.mark)`,b=`{unit: ${bn(e)}}`,C=`vlSelectionTuples(${y}, ${b})`,D=s.map(w=>w.signals.visual);return n.concat({name:r,on:[{events:[...D.length?[{signal:D.join(" || ")}]:[],...o?[{signal:Ei}]:[]],update:C}]})}else{if(!Tt.defined(t)){const l=i+id,u=s.map(f=>{const d=f.channel,{data:g,visual:p}=f.signals,h=B(e.scaleName(d)),m=e.getScaleComponent(d).get("type"),y=De(m)?"+":"";return`(!isArray(${g}) || (${y}invert(${h}, ${p})[0] === ${y}${g}[0] && ${y}invert(${h}, ${p})[1] === ${y}${g}[1]))`});u.length&&n.push({name:l,value:{},on:[{events:s.map(f=>({scale:e.scaleName(f.channel)})),update:u.join(" && ")+` ? ${l} : {}`}]})}const a=s.map(l=>l.signals.data),c=`unit: ${bn(e)}, fields: ${i+er}, values`;return n.concat({name:r,...o?{init:`{${c}: ${Fn(o)}}`}:{},...a.length?{on:[{events:[{signal:a.join(" || ")}],update:`${a.join(" && ")} ? {${c}: [${a}]} : null`}]}:{}})}},topLevelSignals:(e,t,n)=>(ae(e)&&e.hasProjection&&t.init&&(n.filter(r=>r.name===Ei).length||n.unshift({name:Ei,value:null,on:[{events:"timer{1}",update:`${Ei} === null ? {} : ${Ei}`}]})),n),marks:(e,t,n)=>{const i=t.name,{x:r,y:s}=t.project.hasChannel,o=r==null?void 0:r.signals.visual,a=s==null?void 0:s.signals.visual,c=`data(${B(t.name+kn)})`;if(Tt.defined(t)||!r&&!s)return n;const l={x:r!==void 0?{signal:`${o}[0]`}:{value:0},y:s!==void 0?{signal:`${a}[0]`}:{value:0},x2:r!==void 0?{signal:`${o}[1]`}:{field:{group:"width"}},y2:s!==void 0?{signal:`${a}[1]`}:{field:{group:"height"}}};if(t.resolve==="global")for(const m of x(l))l[m]=[{test:`${c}.length && ${c}[0].unit === ${bn(e)}`,...l[m]},{value:0}];const{fill:u,fillOpacity:f,cursor:d,...g}=t.mark,p=x(g).reduce((m,y)=>(m[y]=[{test:[r!==void 0&&`${o}[0] !== ${o}[1]`,s!==void 0&&`${a}[0] !== ${a}[1]`].filter(b=>b).join(" && "),value:g[y]},{value:null}],m),{}),h=d??(t.translate?"move":null);return[{name:`${i+Xn}_bg`,type:"rect",clip:!0,encode:{enter:{fill:{value:u},fillOpacity:{value:f}},update:l}},...n,{name:i+Xn,type:"rect",clip:!0,encode:{enter:{...h?{cursor:{value:h}}:{},fill:{value:"transparent"}},update:{...l,...p}}}]}};function Zb(e,t,n,i){const r=!e.hasProjection,s=n.channel,o=n.signals.visual,a=B(r?e.scaleName(s):e.projectionName()),c=d=>`scale(${a}, ${d})`,l=e.getSizeSignalRef(s===oe?"width":"height").signal,u=`${s}(unit)`,f=t.events.reduce((d,g)=>[...d,{events:g.between[0],update:`[${u}, ${u}]`},{events:g,update:`[${o}[0], clamp(${u}, 0, ${l})]`}],[]);if(r){const d=n.signals.data,g=Tt.defined(t),p=e.getScaleComponent(s),h=p?p.get("type"):void 0,m=i?{init:Fn(i,!0,c)}:{value:[]};return f.push({events:{signal:t.name+id},update:De(h)?`[${c(`${d}[0]`)}, ${c(`${d}[1]`)}]`:"[0, 0]"}),g?[{name:d,on:[]}]:[{name:o,...m,on:f},{name:d,...i?{init:Fn(i)}:{},on:[{events:{signal:o},update:`${o}[0] === ${o}[1] ? null : invert(${a}, ${o})`}]}]}else{const d=s===oe?0:1,g=t.name+rd,p=i?{init:`[${g}[0][${d}], ${g}[1][${d}]]`}:{value:[]};return[{name:o,...p,on:f}]}}const ex={defined:e=>e.type==="point",signals:(e,t,n)=>{const i=t.name,r=i+er,s=t.project,o="(item().isVoronoi ? datum.datum : datum)",a=ve(e.component.selection??{}).reduce((f,d)=>d.type==="interval"?f.concat(d.name+Xn):f,[]).map(f=>`indexof(item().mark.name, '${f}') < 0`).join(" && "),c=`datum && item().mark.marktype !== 'group' && indexof(item().mark.role, 'legend') < 0${a?` && ${a}`:""}`;let l=`unit: ${bn(e)}, `;if(t.project.hasSelectionId)l+=`${Ze}: ${o}[${B(Ze)}]`;else{const f=s.items.map(d=>{const g=e.fieldDef(d.channel);return g!=null&&g.bin?`[${o}[${B(e.vgField(d.channel,{}))}], ${o}[${B(e.vgField(d.channel,{binSuffix:"end"}))}]]`:`${o}[${B(d.field)}]`}).join(", ");l+=`fields: ${r}, values: [${f}]`}const u=t.events;return n.concat([{name:i+Yt,on:u?[{events:u,update:`${c} ? {${l}} : null`,force:!0}]:[]}])}};function mi(e,t,n,i){const r=Jr(t)&&t.condition,s=i(t);if(r){const a=ce(r).map(c=>{const l=i(c);if(Gm(c)){const{param:u,empty:f}=c;return{test:Ed(e,{param:u,empty:f}),...l}}else return{test:Ar(e,c.test),...l}});return{[n]:[...a,...s!==void 0?[s]:[]]}}else return s!==void 0?{[n]:s}:{}}function Ca(e,t="text"){const n=e.encoding[t];return mi(e,n,t,i=>ls(i,e.config))}function ls(e,t,n="datum"){if(e){if(Je(e))return ie(e.value);if(z(e)){const{format:i,formatType:r}=$r(e);return ra({fieldOrDatumDef:e,format:i,formatType:r,expr:n,config:t})}}}function sd(e,t={}){const{encoding:n,markDef:i,config:r,stack:s}=e,o=n.tooltip;if(k(o))return{tooltip:Uc({tooltip:o},s,r,t)};{const a=t.reactiveGeom?"datum.datum":"datum";return mi(e,o,"tooltip",c=>{const l=ls(c,r,a);if(l)return l;if(c===null)return;let u=V("tooltip",i,r);if(u===!0&&(u={content:"encoding"}),I(u))return{value:u};if(X(u))return T(u)?u:u.content==="encoding"?Uc(n,s,r,t):{signal:a}})}}function od(e,t,n,{reactiveGeom:i}={}){const r={...n,...n.tooltipFormat},s={},o=i?"datum.datum":"datum",a=[];function c(u,f){const d=_n(f),g=Ne(u)?u:{...u,type:e[d].type},p=g.title||ca(g,r),h=ce(p).join(", ").replaceAll(/"/g,'\\"');let m;if(ue(f)){const y=f==="x"?"x2":"y2",b=ht(e[y]);if(xe(g.bin)&&b){const C=E(g,{expr:o}),D=E(b,{expr:o}),{format:w,formatType:F}=$r(g);m=Yi(C,D,w,F,r),s[y]=!0}}if((ue(f)||f===We||f===tt)&&t&&t.fieldChannel===f&&t.offset==="normalize"){const{format:y,formatType:b}=$r(g);m=ra({fieldOrDatumDef:g,format:y,formatType:b,expr:o,config:r,normalizeStack:!0}).signal}m??(m=ls(g,r,o).signal),a.push({channel:f,key:h,value:m})}fa(e,(u,f)=>{S(u)?c(u,f):Zr(u)&&c(u.condition,f)});const l={};for(const{channel:u,key:f,value:d}of a)!s[u]&&!l[f]&&(l[f]=d);return l}function Uc(e,t,n,{reactiveGeom:i}={}){const r=od(e,t,n,{reactiveGeom:i}),s=Gt(r).map(([o,a])=>`"${o}": ${a}`);return s.length>0?{signal:`{${s.join(", ")}}`}:void 0}function tx(e){const{markDef:t,config:n}=e,i=V("aria",t,n);return i===!1?{}:{...i?{aria:i}:{},...nx(e),...ix(e)}}function nx(e){const{mark:t,markDef:n,config:i}=e;if(i.aria===!1)return{};const r=V("ariaRoleDescription",n,i);return r!=null?{ariaRoleDescription:{value:r}}:t in _g?{}:{ariaRoleDescription:{value:t}}}function ix(e){const{encoding:t,markDef:n,config:i,stack:r}=e,s=t.description;if(s)return mi(e,s,"description",c=>ls(c,e.config));const o=V("description",n,i);if(o!=null)return{description:ie(o)};if(i.aria===!1)return{};const a=od(t,r,i);if(!Q(a))return{description:{signal:Gt(a).map(([c,l],u)=>`"${u>0?"; ":""}${c}: " + (${l})`).join(" + ")}}}function he(e,t,n={}){const{markDef:i,encoding:r,config:s}=t,{vgChannel:o}=n;let{defaultRef:a,defaultValue:c}=n;a===void 0&&(c??(c=V(e,i,s,{vgChannel:o,ignoreVgConfig:!0})),c!==void 0&&(a=ie(c)));const l=r[e];return mi(t,l,o??e,u=>ia({channel:e,channelDef:u,markDef:i,config:s,scaleName:t.scaleName(e),scale:t.getScaleComponent(e),stack:null,defaultRef:a}))}function ad(e,t={filled:void 0}){const{markDef:n,encoding:i,config:r}=e,{type:s}=n,o=t.filled??V("filled",n,r),a=G(["bar","point","circle","square","geoshape"],s)?"transparent":void 0,c=V(o===!0?"color":void 0,n,r,{vgChannel:"fill"})??r.mark[o===!0&&"color"]??a,l=V(o===!1?"color":void 0,n,r,{vgChannel:"stroke"})??r.mark[o===!1&&"color"],u=o?"fill":"stroke",f={...c?{fill:ie(c)}:{},...l?{stroke:ie(l)}:{}};return n.color&&(o?n.fill:n.stroke)&&v($u("property",{fill:"fill"in n,stroke:"stroke"in n})),{...f,...he("color",e,{vgChannel:u,defaultValue:o?c:l}),...he("fill",e,{defaultValue:i.fill?c:void 0}),...he("stroke",e,{defaultValue:i.stroke?l:void 0})}}function rx(e){const{encoding:t,mark:n}=e,i=t.order;return!sn(n)&&Je(i)?mi(e,i,"zindex",r=>ie(r.value)):{}}function ai({channel:e,markDef:t,encoding:n={},model:i,bandPosition:r}){const s=`${e}Offset`,o=t[s],a=n[s];if((s==="xOffset"||s==="yOffset")&&a)return{offsetType:"encoding",offset:ia({channel:s,channelDef:a,markDef:t,config:i==null?void 0:i.config,scaleName:i.scaleName(s),scale:i.getScaleComponent(s),stack:null,defaultRef:ie(o),bandPosition:r})};const c=t[s];return c?{offsetType:"visual",offset:c}:{}}function Ee(e,t,{defaultPos:n,vgChannel:i}){const{encoding:r,markDef:s,config:o,stack:a}=t,c=r[e],l=r[vt(e)],u=t.scaleName(e),f=t.getScaleComponent(e),{offset:d,offsetType:g}=ai({channel:e,markDef:s,encoding:r,model:t,bandPosition:.5}),p=Na({model:t,defaultPos:n,channel:e,scaleName:u,scale:f}),h=!c&&ue(e)&&(r.latitude||r.longitude)?{field:t.getName(e)}:sx({channel:e,channelDef:c,channel2Def:l,markDef:s,config:o,scaleName:u,scale:f,stack:a,offset:d,defaultRef:p,bandPosition:g==="encoding"?0:void 0});return h?{[i||e]:h}:void 0}function sx(e){const{channel:t,channelDef:n,scaleName:i,stack:r,offset:s,markDef:o}=e;if(z(n)&&r&&t===r.fieldChannel){if(S(n)){let a=n.bandPosition;if(a===void 0&&o.type==="text"&&(t==="radius"||t==="theta")&&(a=.5),a!==void 0)return vr({scaleName:i,fieldOrDatumDef:n,startSuffix:"start",bandPosition:a,offset:s})}return mn(n,i,{suffix:"end"},{offset:s})}return ta(e)}function Na({model:e,defaultPos:t,channel:n,scaleName:i,scale:r}){const{markDef:s,config:o}=e;return()=>{const a=_n(n),c=Ht(n),l=V(n,s,o,{vgChannel:c});if(l!==void 0)return _i(n,l);switch(t){case"zeroOrMin":case"zeroOrMax":if(i){const u=r.get("type");if(!G([Ae.LOG,Ae.TIME,Ae.UTC],u)){if(r.domainDefinitelyIncludesZero())return{scale:i,value:0}}}if(t==="zeroOrMin")return a==="y"?{field:{group:"height"}}:{value:0};switch(a){case"radius":return{signal:`min(${e.width.signal},${e.height.signal})/2`};case"theta":return{signal:"2*PI"};case"x":return{field:{group:"width"}};case"y":return{value:0}}break;case"mid":return{...e[Re(n)],mult:.5}}}}const ox={left:"x",center:"xc",right:"x2"},ax={top:"y",middle:"yc",bottom:"y2"};function cd(e,t,n,i="middle"){if(e==="radius"||e==="theta")return Ht(e);const r=e==="x"?"align":"baseline",s=V(r,t,n);let o;return T(s)?(v(uh(r)),o=void 0):o=s,e==="x"?ox[o||(i==="top"?"left":"center")]:ax[o||i]}function kr(e,t,{defaultPos:n,defaultPos2:i,range:r}){return r?ld(e,t,{defaultPos:n,defaultPos2:i}):Ee(e,t,{defaultPos:n})}function ld(e,t,{defaultPos:n,defaultPos2:i}){const{markDef:r,config:s}=t,o=vt(e),a=Re(e),c=cx(t,i,o),l=c[a]?cd(e,r,s):Ht(e);return{...Ee(e,t,{defaultPos:n,vgChannel:l}),...c}}function cx(e,t,n){const{encoding:i,mark:r,markDef:s,stack:o,config:a}=e,c=_n(n),l=Re(n),u=Ht(n),f=i[c],d=e.scaleName(c),g=e.getScaleComponent(c),{offset:p}=n in i||n in s?ai({channel:n,markDef:s,encoding:i,model:e}):ai({channel:c,markDef:s,encoding:i,model:e});if(!f&&(n==="x2"||n==="y2")&&(i.latitude||i.longitude)){const m=Re(n),y=e.markDef[m];return y!=null?{[m]:{value:y}}:{[u]:{field:e.getName(n)}}}const h=lx({channel:n,channelDef:f,channel2Def:i[n],markDef:s,config:a,scaleName:d,scale:g,stack:o,offset:p,defaultRef:void 0});return h!==void 0?{[u]:h}:sr(n,s)||sr(n,{[n]:yr(n,s,a.style),[l]:yr(l,s,a.style)})||sr(n,a[r])||sr(n,a.mark)||{[u]:Na({model:e,defaultPos:t,channel:n,scaleName:d,scale:g})()}}function lx({channel:e,channelDef:t,channel2Def:n,markDef:i,config:r,scaleName:s,scale:o,stack:a,offset:c,defaultRef:l}){return z(t)&&a&&e.charAt(0)===a.fieldChannel.charAt(0)?mn(t,s,{suffix:"start"},{offset:c}):ta({channel:e,channelDef:n,scaleName:s,scale:o,stack:a,markDef:i,config:r,offset:c,defaultRef:l})}function sr(e,t){const n=Re(e),i=Ht(e);if(t[i]!==void 0)return{[i]:_i(e,t[i])};if(t[e]!==void 0)return{[i]:_i(e,t[e])};if(t[n]){const r=t[n];if(Cn(r))v(rh(n));else return{[n]:_i(e,r)}}}function Xt(e,t){const{config:n,encoding:i,markDef:r}=e,s=r.type,o=vt(t),a=Re(t),c=i[t],l=i[o],u=e.getScaleComponent(t),f=u?u.get("type"):void 0,d=r.orient,g=i[a]??i.size??V("size",r,n,{vgChannel:a}),p=ou(t),h=s==="bar"&&(t==="x"?d==="vertical":d==="horizontal");return S(c)&&(te(c.bin)||xe(c.bin)||c.timeUnit&&!l)&&!(g&&!Cn(g))&&!i[p]&&!ye(f)?dx({fieldDef:c,fieldDef2:l,channel:t,model:e}):(z(c)&&ye(f)||h)&&!l?fx(c,t,e):ld(t,e,{defaultPos:"zeroOrMax",defaultPos2:"zeroOrMin"})}function ux(e,t,n,i,r,s,o){if(Cn(r))if(n){const c=n.get("type");if(c==="band"){let l=`bandwidth('${t}')`;r.band!==1&&(l=`${r.band} * ${l}`);const u=_t("minBandSize",{type:o},i);return{signal:u?`max(${Xe(u)}, ${l})`:l}}else r.band!==1&&(v(gh(c)),r=void 0)}else return{mult:r.band,field:{group:e}};else{if(T(r))return r;if(r)return{value:r}}if(n){const c=n.get("range");if(rn(c)&&re(c.step))return{value:c.step-2}}if(!s){const{bandPaddingInner:c,barBandPaddingInner:l,rectBandPaddingInner:u}=i.scale,f=le(c,o==="bar"?l:u);if(T(f))return{signal:`(1 - (${f.signal})) * ${e}`};if(re(f))return{signal:`${1-f} * ${e}`}}return{value:Nr(i.view,e)-2}}function fx(e,t,n){var K,we;const{markDef:i,encoding:r,config:s,stack:o}=n,a=i.orient,c=n.scaleName(t),l=n.getScaleComponent(t),u=Re(t),f=vt(t),d=ou(t),g=n.scaleName(d),p=n.getScaleComponent(_o(t)),h=a==="horizontal"&&t==="y"||a==="vertical"&&t==="x";let m;(r.size||i.size)&&(h?m=he("size",n,{vgChannel:u,defaultRef:ie(i.size)}):v(bh(i.type)));const y=!!m,b=sf({channel:t,fieldDef:e,markDef:i,config:s,scaleType:(K=l||p)==null?void 0:K.get("type"),useVlSizeChannel:h});m=m||{[u]:ux(u,g||c,p||l,s,b,!!e,i.type)};const C=((we=l||p)==null?void 0:we.get("type"))==="band"&&Cn(b)&&!y?"top":"middle",D=cd(t,i,s,C),w=D==="xc"||D==="yc",{offset:F,offsetType:A}=ai({channel:t,markDef:i,encoding:r,model:n,bandPosition:w?.5:0}),M=ta({channel:t,channelDef:e,markDef:i,config:s,scaleName:c,scale:l,stack:o,offset:F,defaultRef:Na({model:n,defaultPos:"mid",channel:t,scaleName:c,scale:l}),bandPosition:w?A==="encoding"?0:.5:T(b)?{signal:`(1-${b})/2`}:Cn(b)?(1-b.band)/2:0});if(u)return{[D]:M,...m};{const de=Ht(f),st=m[u],N=F?{...st,offset:F}:st;return{[D]:M,[de]:k(M)?[M[0],{...M[1],offset:N}]:{...M,offset:N}}}}function Bc(e,t,n,i,r,s,o){if(eu(e))return 0;const a=e==="x"||e==="y2",c=a?-t/2:t/2;if(T(n)||T(r)||T(i)||s){const l=Xe(n),u=Xe(r),f=Xe(i),d=Xe(s),p=s?`(${o} < ${d} ? ${a?"":"-"}0.5 * (${d} - (${o})) : ${c})`:c,h=f?`${f} + `:"",m=l?`(${l} ? -1 : 1) * `:"",y=u?`(${u} + ${p})`:p;return{signal:h+m+y}}else return r=r||0,i+(n?-r-c:+r+c)}function dx({fieldDef:e,fieldDef2:t,channel:n,model:i}){var we;const{config:r,markDef:s,encoding:o}=i,a=i.getScaleComponent(n),c=i.scaleName(n),l=a?a.get("type"):void 0,u=a.get("reverse"),f=sf({channel:n,fieldDef:e,markDef:s,config:r,scaleType:l}),d=(we=i.component.axes[n])==null?void 0:we[0],g=(d==null?void 0:d.get("translate"))??.5,p=ue(n)?V("binSpacing",s,r)??0:0,h=vt(n),m=Ht(n),y=Ht(h),b=_t("minBandSize",s,r),{offset:C}=ai({channel:n,markDef:s,encoding:o,model:i,bandPosition:0}),{offset:D}=ai({channel:h,markDef:s,encoding:o,model:i,bandPosition:0}),w=jm({fieldDef:e,scaleName:c}),F=Bc(n,p,u,g,C,b,w),A=Bc(h,p,u,g,D??C,b,w),M=T(f)?{signal:`(1-${f.signal})/2`}:Cn(f)?(1-f.band)/2:.5,K=qt({fieldDef:e,fieldDef2:t,markDef:s,config:r});if(te(e.bin)||e.timeUnit){const de=e.timeUnit&&K!==.5;return{[y]:Wc({fieldDef:e,scaleName:c,bandPosition:M,offset:A,useRectOffsetField:de}),[m]:Wc({fieldDef:e,scaleName:c,bandPosition:T(M)?{signal:`1-${M.signal}`}:1-M,offset:F,useRectOffsetField:de})}}else if(xe(e.bin)){const de=mn(e,c,{},{offset:A});if(S(t))return{[y]:de,[m]:mn(t,c,{},{offset:F})};if(Rn(e.bin)&&e.bin.step)return{[y]:de,[m]:{signal:`scale("${c}", ${E(e,{expr:"datum"})} + ${e.bin.step})`,offset:F}}}v(Nu(h))}function Wc({fieldDef:e,scaleName:t,bandPosition:n,offset:i,useRectOffsetField:r}){return vr({scaleName:t,fieldOrDatumDef:e,bandPosition:n,offset:i,...r?{startSuffix:as,endSuffix:cs}:{}})}const px=new Set(["aria","width","height"]);function Ge(e,t){const{fill:n=void 0,stroke:i=void 0}=t.color==="include"?ad(e):{};return{...gx(e.markDef,t),...Gc(e,"fill",n),...Gc(e,"stroke",i),...he("opacity",e),...he("fillOpacity",e),...he("strokeOpacity",e),...he("strokeWidth",e),...he("strokeDash",e),...rx(e),...sd(e),...Ca(e,"href"),...tx(e)}}function Gc(e,t,n){const{config:i,mark:r,markDef:s}=e;if(V("invalid",s,i)==="hide"&&n&&!sn(r)){const a=hx(e,{invalid:!0,channels:Br});if(a)return{[t]:[{test:a,value:null},...ce(n)]}}return n?{[t]:n}:{}}function gx(e,t){return Og.reduce((n,i)=>(!px.has(i)&&e[i]!==void 0&&t[i]!=="ignore"&&(n[i]=ie(e[i])),n),{})}function hx(e,{invalid:t=!1,channels:n}){const i=n.reduce((s,o)=>{const a=e.getScaleComponent(o);if(a){const c=a.get("type"),l=e.vgField(o,{expr:"datum"});l&&De(c)&&(s[l]=!0)}return s},{}),r=x(i);if(r.length>0){const s=t?"||":"&&";return r.map(o=>na(o,t)).join(` ${s} `)}}function Fa(e){const{config:t,markDef:n}=e;if(V("invalid",n,t)){const r=mx(e,{channels:St});if(r)return{defined:{signal:r}}}return{}}function mx(e,{invalid:t=!1,channels:n}){const i=n.reduce((s,o)=>{var c;const a=e.getScaleComponent(o);if(a){const l=a.get("type"),u=e.vgField(o,{expr:"datum",binSuffix:(c=e.stack)!=null&&c.impute?"mid":void 0});u&&De(l)&&(s[u]=!0)}return s},{}),r=x(i);if(r.length>0){const s=t?"||":"&&";return r.map(o=>na(o,t)).join(` ${s} `)}}function Hc(e,t){if(t!==void 0)return{[e]:ie(t)}}const Is="voronoi",ud={defined:e=>e.type==="point"&&e.nearest,parse:(e,t)=>{if(t.events)for(const n of t.events)n.markname=e.getName(Is)},marks:(e,t,n)=>{const{x:i,y:r}=t.project.hasChannel,s=e.mark;if(sn(s))return v(zg(s)),n;const o={name:e.getName(Is),type:"path",interactive:!0,from:{data:e.getName("marks")},encode:{update:{fill:{value:"transparent"},strokeWidth:{value:.35},stroke:{value:"transparent"},isVoronoi:{value:!0},...sd(e,{reactiveGeom:!0})}},transform:[{type:"voronoi",x:{expr:i||!r?"datum.datum.x || 0":"0"},y:{expr:r||!i?"datum.datum.y || 0":"0"},size:[e.getSizeSignalRef("width"),e.getSizeSignalRef("height")]}]};let a=0,c=!1;return n.forEach((l,u)=>{const f=l.name??"";f===e.component.mark[0].name?a=u:f.indexOf(Is)>=0&&(c=!0)}),c||n.splice(a+1,0,o),n}},fd={defined:e=>e.type==="point"&&e.resolve==="global"&&e.bind&&e.bind!=="scales"&&!ya(e.bind),parse:(e,t,n)=>xd(t,n),topLevelSignals:(e,t,n)=>{const i=t.name,r=t.project,s=t.bind,o=t.init&&t.init[0],a=ud.defined(t)?"(item().isVoronoi ? datum.datum : datum)":"datum";return r.items.forEach((c,l)=>{const u=se(`${i}_${c.field}`);n.filter(d=>d.name===u).length||n.unshift({name:u,...o?{init:Fn(o[l])}:{value:null},on:t.events?[{events:t.events,update:`datum && item().mark.marktype !== 'group' ? ${a}[${B(c.field)}] : null`}]:[],bind:s[c.field]??s[c.channel]??s})}),n},signals:(e,t,n)=>{const i=t.name,r=t.project,s=n.filter(l=>l.name===i+Yt)[0],o=i+er,a=r.items.map(l=>se(`${i}_${l.field}`)),c=a.map(l=>`${l} !== null`).join(" && ");return a.length&&(s.update=`${c} ? {fields: ${o}, values: [${a.join(", ")}]} : null`),delete s.value,delete s.on,n}},Tr="_toggle",dd={defined:e=>e.type==="point"&&!!e.toggle,signals:(e,t,n)=>n.concat({name:t.name+Tr,value:!1,on:[{events:t.events,update:t.toggle}]}),modifyExpr:(e,t)=>{const n=t.name+Yt,i=t.name+Tr;return`${i} ? null : ${n}, `+(t.resolve==="global"?`${i} ? null : true, `:`${i} ? null : {unit: ${bn(e)}}, `)+`${i} ? ${n} : null`}},yx={defined:e=>e.clear!==void 0&&e.clear!==!1,parse:(e,t)=>{t.clear&&(t.clear=I(t.clear)?pi(t.clear,"view"):t.clear)},topLevelSignals:(e,t,n)=>{if(fd.defined(t))for(const i of t.project.items){const r=n.findIndex(s=>s.name===se(`${t.name}_${i.field}`));r!==-1&&n[r].on.push({events:t.clear,update:"null"})}return n},signals:(e,t,n)=>{function i(r,s){r!==-1&&n[r].on&&n[r].on.push({events:t.clear,update:s})}if(t.type==="interval")for(const r of t.project.items){const s=n.findIndex(o=>o.name===r.signals.visual);if(i(s,"[0, 0]"),s===-1){const o=n.findIndex(a=>a.name===r.signals.data);i(o,"null")}}else{let r=n.findIndex(s=>s.name===t.name+Yt);i(r,"null"),dd.defined(t)&&(r=n.findIndex(s=>s.name===t.name+Tr),i(r,"false"))}return n}},pd={defined:e=>{const t=e.resolve==="global"&&e.bind&&ya(e.bind),n=e.project.items.length===1&&e.project.items[0].field!==Ze;return t&&!n&&v(Ug),t&&n},parse:(e,t,n)=>{const i=P(n);if(i.select=I(i.select)?{type:i.select,toggle:t.toggle}:{...i.select,toggle:t.toggle},xd(t,i),X(n.select)&&(n.select.on||n.select.clear)){const o='event.item && indexof(event.item.mark.role, "legend") < 0';for(const a of t.events)a.filter=ce(a.filter??[]),a.filter.includes(o)||a.filter.push(o)}const r=As(t.bind)?t.bind.legend:"click",s=I(r)?pi(r,"view"):ce(r);t.bind={legend:{merge:s}}},topLevelSignals:(e,t,n)=>{const i=t.name,r=As(t.bind)&&t.bind.legend,s=o=>a=>{const c=P(a);return c.markname=o,c};for(const o of t.project.items){if(!o.hasLegend)continue;const a=`${se(o.field)}_legend`,c=`${i}_${a}`;if(n.filter(u=>u.name===c).length===0){const u=r.merge.map(s(`${a}_symbols`)).concat(r.merge.map(s(`${a}_labels`))).concat(r.merge.map(s(`${a}_entries`)));n.unshift({name:c,...t.init?{}:{value:null},on:[{events:u,update:"isDefined(datum.value) ? datum.value : item().items[0].items[0].datum.value",force:!0},{events:r.merge,update:`!event.item || !datum ? null : ${c}`,force:!0}]})}}return n},signals:(e,t,n)=>{const i=t.name,r=t.project,s=n.find(d=>d.name===i+Yt),o=i+er,a=r.items.filter(d=>d.hasLegend).map(d=>se(`${i}_${se(d.field)}_legend`)),l=`${a.map(d=>`${d} !== null`).join(" && ")} ? {fields: ${o}, values: [${a.join(", ")}]} : null`;t.events&&a.length>0?s.on.push({events:a.map(d=>({signal:d})),update:l}):a.length>0&&(s.update=l,delete s.value,delete s.on);const u=n.find(d=>d.name===i+Tr),f=As(t.bind)&&t.bind.legend;return u&&(t.events?u.on.push({...u.on[0],events:f}):u.on[0].events=f),n}};function bx(e,t,n){var r;const i=(r=e.fieldDef(t))==null?void 0:r.field;for(const s of ve(e.component.selection??{})){const o=s.project.hasField[i]??s.project.hasChannel[t];if(o&&pd.defined(s)){const a=n.get("selections")??[];a.push(s.name),n.set("selections",a,!1),o.hasLegend=!0}}}const gd="_translate_anchor",hd="_translate_delta",xx={defined:e=>e.type==="interval"&&e.translate,signals:(e,t,n)=>{const i=t.name,r=Tt.defined(t),s=i+gd,{x:o,y:a}=t.project.hasChannel;let c=pi(t.translate,"scope");return r||(c=c.map(l=>(l.between[0].markname=i+Xn,l))),n.push({name:s,value:{},on:[{events:c.map(l=>l.between[0]),update:"{x: x(unit), y: y(unit)"+(o!==void 0?`, extent_x: ${r?ao(e,oe):`slice(${o.signals.visual})`}`:"")+(a!==void 0?`, extent_y: ${r?ao(e,be):`slice(${a.signals.visual})`}`:"")+"}"}]},{name:i+hd,value:{},on:[{events:c,update:`{x: ${s}.x - x(unit), y: ${s}.y - y(unit)}`}]}),o!==void 0&&qc(e,t,o,"width",n),a!==void 0&&qc(e,t,a,"height",n),n}};function qc(e,t,n,i,r){const s=t.name,o=s+gd,a=s+hd,c=n.channel,l=Tt.defined(t),u=r.filter(w=>w.name===n.signals[l?"data":"visual"])[0],f=e.getSizeSignalRef(i).signal,d=e.getScaleComponent(c),g=d&&d.get("type"),p=d&&d.get("reverse"),h=l?c===oe?p?"":"-":p?"-":"":"",m=`${o}.extent_${c}`,y=`${h}${a}.${c} / ${l?`${f}`:`span(${m})`}`,b=!l||!d?"panLinear":g==="log"?"panLog":g==="symlog"?"panSymlog":g==="pow"?"panPow":"panLinear",C=l?g==="pow"?`, ${d.get("exponent")??1}`:g==="symlog"?`, ${d.get("constant")??1}`:"":"",D=`${b}(${m}, ${y}${C})`;u.on.push({events:{signal:a},update:l?D:`clampRange(${D}, 0, ${f})`})}const md="_zoom_anchor",yd="_zoom_delta",vx={defined:e=>e.type==="interval"&&e.zoom,signals:(e,t,n)=>{const i=t.name,r=Tt.defined(t),s=i+yd,{x:o,y:a}=t.project.hasChannel,c=B(e.scaleName(oe)),l=B(e.scaleName(be));let u=pi(t.zoom,"scope");return r||(u=u.map(f=>(f.markname=i+Xn,f))),n.push({name:i+md,on:[{events:u,update:r?"{"+[c?`x: invert(${c}, x(unit))`:"",l?`y: invert(${l}, y(unit))`:""].filter(f=>f).join(", ")+"}":"{x: x(unit), y: y(unit)}"}]},{name:s,on:[{events:u,force:!0,update:"pow(1.001, event.deltaY * pow(16, event.deltaMode))"}]}),o!==void 0&&Vc(e,t,o,"width",n),a!==void 0&&Vc(e,t,a,"height",n),n}};function Vc(e,t,n,i,r){const s=t.name,o=n.channel,a=Tt.defined(t),c=r.filter(b=>b.name===n.signals[a?"data":"visual"])[0],l=e.getSizeSignalRef(i).signal,u=e.getScaleComponent(o),f=u&&u.get("type"),d=a?ao(e,o):c.name,g=s+yd,p=`${s}${md}.${o}`,h=!a||!u?"zoomLinear":f==="log"?"zoomLog":f==="symlog"?"zoomSymlog":f==="pow"?"zoomPow":"zoomLinear",m=a?f==="pow"?`, ${u.get("exponent")??1}`:f==="symlog"?`, ${u.get("constant")??1}`:"":"",y=`${h}(${d}, ${p}, ${g}${m})`;c.on.push({events:{signal:g},update:a?y:`clampRange(${y}, 0, ${l})`})}const kn="_store",Yt="_tuple",Sx="_modify",bd="vlSelectionResolve",us=[ex,Jb,Kb,dd,fd,Tt,pd,yx,xx,vx,ud];function Ex(e){let t=e.parent;for(;t&&!ze(t);)t=t.parent;return t}function bn(e,{escape:t}={escape:!0}){let n=t?B(e.name):e.name;const i=Ex(e);if(i){const{facet:r}=i;for(const s of Me)r[s]&&(n+=` + '__facet_${s}_' + (facet[${B(i.vgField(s))}])`)}return n}function ka(e){return ve(e.component.selection??{}).reduce((t,n)=>t||n.project.hasSelectionId,!1)}function xd(e,t){(I(t.select)||!t.select.on)&&delete e.events,(I(t.select)||!t.select.clear)&&delete e.clear,(I(t.select)||!t.select.toggle)&&delete e.toggle}function co(e){const t=[];return e.type==="Identifier"?[e.name]:e.type==="Literal"?[e.value]:(e.type==="MemberExpression"&&(t.push(...co(e.object)),t.push(...co(e.property))),t)}function vd(e){return e.object.type==="MemberExpression"?vd(e.object):e.object.name==="datum"}function Sd(e){const t=Fp(e),n=new Set;return t.visit(i=>{i.type==="MemberExpression"&&vd(i)&&n.add(co(i).slice(1).join("."))}),n}class yi extends Y{clone(){return new yi(null,this.model,P(this.filter))}constructor(t,n,i){super(t),this.model=n,this.filter=i,this.expr=Ar(this.model,this.filter,this),this._dependentFields=Sd(this.expr)}dependentFields(){return this._dependentFields}producedFields(){return new Set}assemble(){return{type:"filter",expr:this.expr}}hash(){return`Filter ${this.expr}`}}function $x(e,t){const n={},i=e.config.selection;if(!t||!t.length)return n;for(const r of t){const s=se(r.name),o=r.select,a=I(o)?o:o.type,c=X(o)?P(o):{type:a},l=i[a];for(const d in l)d==="fields"||d==="encodings"||(d==="mark"&&(c[d]={...l[d],...c[d]}),(c[d]===void 0||c[d]===!0)&&(c[d]=P(l[d]??c[d])));const u=n[s]={...c,name:s,type:a,init:r.value,bind:r.bind,events:I(c.on)?pi(c.on,"scope"):ce(P(c.on))},f=P(r);for(const d of us)d.defined(u)&&d.parse&&d.parse(e,u,f)}return n}function Ed(e,t,n,i="datum"){const r=I(t)?t:t.param,s=se(r),o=B(s+kn);let a;try{a=e.getSelectionComponent(s,r)}catch{return`!!${s}`}if(a.project.timeUnit){const d=n??e.component.data.raw,g=a.project.timeUnit.clone();d.parent?g.insertAsParentOf(d):d.parent=g}const c=a.project.hasSelectionId?"vlSelectionIdTest(":"vlSelectionTest(",l=a.resolve==="global"?")":`, ${B(a.resolve)})`,u=`${c}${o}, ${i}${l}`,f=`length(data(${o}))`;return t.empty===!1?`${f} && ${u}`:`!${f} || ${u}`}function $d(e,t,n){const i=se(t),r=n.encoding;let s=n.field,o;try{o=e.getSelectionComponent(i,t)}catch{return i}if(!r&&!s)s=o.project.items[0].field,o.project.items.length>1&&v(`A "field" or "encoding" must be specified when using a selection as a scale domain. Using "field": ${B(s)}.`);else if(r&&!s){const a=o.project.items.filter(c=>c.channel===r);!a.length||a.length>1?(s=o.project.items[0].field,v((a.length?"Multiple ":"No ")+`matching ${B(r)} encoding found for selection ${B(n.param)}. Using "field": ${B(s)}.`)):s=a[0].field}return`${o.name}[${B(Ue(s))}]`}function wx(e,t){for(const[n,i]of Gt(e.component.selection??{})){const r=e.getName(`lookup_${n}`);e.component.data.outputNodes[r]=i.materialized=new Ce(new yi(t,e,{param:n}),r,ee.Lookup,e.component.data.outputNodeRefCounts)}}function Ar(e,t,n){return Oi(t,i=>I(i)?i:tm(i)?Ed(e,i,n):Du(i))}function Cx(e,t){if(e)return k(e)&&!Ut(e)?e.map(n=>ca(n,t)).join(", "):e}function Ls(e,t,n,i){var r,s;e.encode??(e.encode={}),(r=e.encode)[t]??(r[t]={}),(s=e.encode[t]).update??(s.update={}),e.encode[t].update[n]=i}function ki(e,t,n,i={header:!1}){var f,d;const{disable:r,orient:s,scale:o,labelExpr:a,title:c,zindex:l,...u}=e.combine();if(!r){for(const g in u){const p=ry[g],h=u[g];if(p&&p!==t&&p!=="both")delete u[g];else if(Zi(h)){const{condition:m,...y}=h,b=ce(m),C=Sc[g];if(C){const{vgProp:D,part:w}=C,F=[...b.map(A=>{const{test:M,...K}=A;return{test:Ar(null,M),...K}}),y];Ls(u,w,D,F),delete u[g]}else if(C===null){const D={signal:b.map(w=>{const{test:F,...A}=w;return`${Ar(null,F)} ? ${sc(A)} : `}).join("")+sc(y)};u[g]=D}}else if(T(h)){const m=Sc[g];if(m){const{vgProp:y,part:b}=m;Ls(u,b,y,h),delete u[g]}}G(["labelAlign","labelBaseline"],g)&&u[g]===null&&delete u[g]}if(t==="grid"){if(!u.grid)return;if(u.encode){const{grid:g}=u.encode;u.encode={...g?{grid:g}:{}},Q(u.encode)&&delete u.encode}return{scale:o,orient:s,...u,domain:!1,labels:!1,aria:!1,maxExtent:0,minExtent:0,ticks:!1,zindex:le(l,0)}}else{if(!i.header&&e.mainExtracted)return;if(a!==void 0){let p=a;(d=(f=u.encode)==null?void 0:f.labels)!=null&&d.update&&T(u.encode.labels.update.text)&&(p=Sn(a,"datum.label",u.encode.labels.update.text.signal)),Ls(u,"labels","text",{signal:p})}if(u.labelAlign===null&&delete u.labelAlign,u.encode){for(const p of hf)e.hasAxisPart(p)||delete u.encode[p];Q(u.encode)&&delete u.encode}const g=Cx(c,n);return{scale:o,orient:s,grid:!1,...g?{title:g}:{},...u,...n.aria===!1?{aria:!1}:{},zindex:le(l,0)}}}}function wd(e){const{axes:t}=e.component,n=[];for(const i of St)if(t[i]){for(const r of t[i])if(!r.get("disable")&&!r.get("gridScale")){const s=i==="x"?"height":"width",o=e.getSizeSignalRef(s).signal;s!==o&&n.push({name:s,update:o})}}return n}function Nx(e,t){const{x:n=[],y:i=[]}=e;return[...n.map(r=>ki(r,"grid",t)),...i.map(r=>ki(r,"grid",t)),...n.map(r=>ki(r,"main",t)),...i.map(r=>ki(r,"main",t))].filter(r=>r)}function Xc(e,t,n,i){return Object.assign.apply(null,[{},...e.map(r=>{if(r==="axisOrient"){const s=n==="x"?"bottom":"left",o=t[n==="x"?"axisBottom":"axisLeft"]||{},a=t[n==="x"?"axisTop":"axisRight"]||{},c=new Set([...x(o),...x(a)]),l={};for(const u of c.values())l[u]={signal:`${i.signal} === "${s}" ? ${Xe(o[u])} : ${Xe(a[u])}`};return l}return t[r]})])}function Fx(e,t,n,i){const r=t==="band"?["axisDiscrete","axisBand"]:t==="point"?["axisDiscrete","axisPoint"]:Bu(t)?["axisQuantitative"]:t==="time"||t==="utc"?["axisTemporal"]:[],s=e==="x"?"axisX":"axisY",o=T(n)?"axisOrient":`axis${Wi(n)}`,a=[...r,...r.map(l=>s+l.substr(4))],c=["axis",o,s];return{vlOnlyAxisConfig:Xc(a,i,e,n),vgAxisConfig:Xc(c,i,e,n),axisConfigStyle:kx([...c,...a],i)}}function kx(e,t){var i;const n=[{}];for(const r of e){let s=(i=t[r])==null?void 0:i.style;if(s){s=ce(s);for(const o of s)n.push(t.style[o])}}return Object.assign.apply(null,n)}function lo(e,t,n,i={}){var s;const r=mu(e,n,t);if(r!==void 0)return{configFrom:"style",configValue:r};for(const o of["vlOnlyAxisConfig","vgAxisConfig","axisConfigStyle"])if(((s=i[o])==null?void 0:s[e])!==void 0)return{configFrom:o,configValue:i[o][e]};return{}}const Yc={scale:({model:e,channel:t})=>e.scaleName(t),format:({format:e})=>e,formatType:({formatType:e})=>e,grid:({fieldOrDatumDef:e,axis:t,scaleType:n})=>t.grid??Tx(n,e),gridScale:({model:e,channel:t})=>Ax(e,t),labelAlign:({axis:e,labelAngle:t,orient:n,channel:i})=>e.labelAlign||Nd(t,n,i),labelAngle:({labelAngle:e})=>e,labelBaseline:({axis:e,labelAngle:t,orient:n,channel:i})=>e.labelBaseline||Cd(t,n,i),labelFlush:({axis:e,fieldOrDatumDef:t,channel:n})=>e.labelFlush??_x(t.type,n),labelOverlap:({axis:e,fieldOrDatumDef:t,scaleType:n})=>e.labelOverlap??Rx(t.type,n,S(t)&&!!t.timeUnit,S(t)?t.sort:void 0),orient:({orient:e})=>e,tickCount:({channel:e,model:t,axis:n,fieldOrDatumDef:i,scaleType:r})=>{const s=e==="x"?"width":e==="y"?"height":void 0,o=s?t.getSizeSignalRef(s):void 0;return n.tickCount??Lx({fieldOrDatumDef:i,scaleType:r,size:o,values:n.values})},tickMinStep:Px,title:({axis:e,model:t,channel:n})=>{if(e.title!==void 0)return e.title;const i=Fd(t,n);if(i!==void 0)return i;const r=t.typedFieldDef(n),s=n==="x"?"x2":"y2",o=t.fieldDef(s);return bu(r?[xc(r)]:[],S(o)?[xc(o)]:[])},values:({axis:e,fieldOrDatumDef:t})=>zx(e,t),zindex:({axis:e,fieldOrDatumDef:t,mark:n})=>e.zindex??Dx(n,t)};function Tx(e,t){return!ye(e)&&S(t)&&!te(t==null?void 0:t.bin)&&!xe(t==null?void 0:t.bin)}function Ax(e,t){const n=t==="x"?"y":"x";if(e.getScaleComponent(n))return e.scaleName(n)}function Ox(e,t,n,i,r){const s=t==null?void 0:t.labelAngle;if(s!==void 0)return T(s)?s:Pi(s);{const{configValue:o}=lo("labelAngle",i,t==null?void 0:t.style,r);return o!==void 0?Pi(o):n===oe&&G([Qo,Ko],e.type)&&!(S(e)&&e.timeUnit)?270:void 0}}function uo(e){return`(((${e.signal} % 360) + 360) % 360)`}function Cd(e,t,n,i){if(e!==void 0)if(n==="x"){if(T(e)){const r=uo(e),s=T(t)?`(${t.signal} === "top")`:t==="top";return{signal:`(45 < ${r} && ${r} < 135) || (225 < ${r} && ${r} < 315) ? "middle" :(${r} <= 45 || 315 <= ${r}) === ${s} ? "bottom" : "top"`}}if(45<e&&e<135||225<e&&e<315)return"middle";if(T(t)){const r=e<=45||315<=e?"===":"!==";return{signal:`${t.signal} ${r} "top" ? "bottom" : "top"`}}return(e<=45||315<=e)==(t==="top")?"bottom":"top"}else{if(T(e)){const r=uo(e),s=T(t)?`(${t.signal} === "left")`:t==="left";return{signal:`${r} <= 45 || 315 <= ${r} || (135 <= ${r} && ${r} <= 225) ? ${i?'"middle"':"null"} : (45 <= ${r} && ${r} <= 135) === ${s} ? "top" : "bottom"`}}if(e<=45||315<=e||135<=e&&e<=225)return i?"middle":null;if(T(t)){const r=45<=e&&e<=135?"===":"!==";return{signal:`${t.signal} ${r} "left" ? "top" : "bottom"`}}return(45<=e&&e<=135)==(t==="left")?"top":"bottom"}}function Nd(e,t,n){if(e===void 0)return;const i=n==="x",r=i?0:90,s=i?"bottom":"left";if(T(e)){const o=uo(e),a=T(t)?`(${t.signal} === "${s}")`:t===s;return{signal:`(${r?`(${o} + 90)`:o} % 180 === 0) ? ${i?null:'"center"'} :(${r} < ${o} && ${o} < ${180+r}) === ${a} ? "left" : "right"`}}if((e+r)%180===0)return i?null:"center";if(T(t)){const o=r<e&&e<180+r?"===":"!==";return{signal:`${`${t.signal} ${o} "${s}"`} ? "left" : "right"`}}return(r<e&&e<180+r)==(t===s)?"left":"right"}function _x(e,t){if(t==="x"&&G(["quantitative","temporal"],e))return!0}function Rx(e,t,n,i){if(n&&!X(i)||e!=="nominal"&&e!=="ordinal")return t==="log"||t==="symlog"?"greedy":!0}function Ix(e){return e==="x"?"bottom":"left"}function Lx({fieldOrDatumDef:e,scaleType:t,size:n,values:i}){var r;if(!i&&!ye(t)&&t!=="log"){if(S(e)){if(te(e.bin))return{signal:`ceil(${n.signal}/10)`};if(e.timeUnit&&G(["month","hours","day","quarter"],(r=me(e.timeUnit))==null?void 0:r.unit))return}return{signal:`ceil(${n.signal}/40)`}}}function Px({format:e,fieldOrDatumDef:t}){if(e==="d")return 1;if(S(t)){const{timeUnit:n}=t;if(n){const i=Lu(n);if(i)return{signal:i}}}}function Fd(e,t){const n=t==="x"?"x2":"y2",i=e.fieldDef(t),r=e.fieldDef(n),s=i?i.title:void 0,o=r?r.title:void 0;if(s&&o)return xu(s,o);if(s)return s;if(o)return o;if(s!==void 0)return s;if(o!==void 0)return o}function zx(e,t){const n=e.values;if(k(n))return gf(t,n);if(T(n))return n}function Dx(e,t){return e==="rect"&&Er(t)?1:0}class ci extends Y{clone(){return new ci(null,P(this.transform))}constructor(t,n){super(t),this.transform=n,this._dependentFields=Sd(this.transform.calculate)}static parseAllForSortIndex(t,n){return n.forEachFieldDef((i,r)=>{if(Pn(i)&&rf(i.sort)){const{field:s,timeUnit:o}=i,a=i.sort,c=a.map((l,u)=>`${Du({field:s,timeUnit:o,equal:l})} ? ${u} : `).join("")+a.length;t=new ci(t,{calculate:c,as:li(i,r,{forAs:!0})})}}),t}producedFields(){return new Set([this.transform.as])}dependentFields(){return this._dependentFields}assemble(){return{type:"formula",expr:this.transform.calculate,as:this.transform.as}}hash(){return`Calculate ${W(this.transform)}`}}function li(e,t,n){return E(e,{prefix:t,suffix:"sort_index",...n})}function fs(e,t){return G(["top","bottom"],t)?"column":G(["left","right"],t)||e==="row"?"row":"column"}function ui(e,t,n,i){const r=i==="row"?n.headerRow:i==="column"?n.headerColumn:n.headerFacet;return le((t||{})[e],r[e],n.header[e])}function ds(e,t,n,i){const r={};for(const s of e){const o=ui(s,t||{},n,i);o!==void 0&&(r[s]=o)}return r}const Ta=["row","column"],Aa=["header","footer"];function jx(e,t){const n=e.component.layoutHeaders[t].title,i=e.config?e.config:void 0,r=e.component.layoutHeaders[t].facetFieldDef?e.component.layoutHeaders[t].facetFieldDef:void 0,{titleAnchor:s,titleAngle:o,titleOrient:a}=ds(["titleAnchor","titleAngle","titleOrient"],r.header,i,t),c=fs(t,a),l=Pi(o);return{name:`${t}-title`,type:"group",role:`${c}-title`,title:{text:n,...t==="row"?{orient:"left"}:{},style:"guide-title",...Td(l,c),...kd(c,l,s),...Ad(i,r,t,Ny,_f)}}}function kd(e,t,n="middle"){switch(n){case"start":return{align:"left"};case"end":return{align:"right"}}const i=Nd(t,e==="row"?"left":"top",e==="row"?"y":"x");return i?{align:i}:{}}function Td(e,t){const n=Cd(e,t==="row"?"left":"top",t==="row"?"y":"x",!0);return n?{baseline:n}:{}}function Mx(e,t){const n=e.component.layoutHeaders[t],i=[];for(const r of Aa)if(n[r])for(const s of n[r]){const o=Bx(e,t,r,n,s);o!=null&&i.push(o)}return i}function Ux(e,t){const{sort:n}=e;return ft(n)?{field:E(n,{expr:"datum"}),order:n.order??"ascending"}:k(n)?{field:li(e,t,{expr:"datum"}),order:"ascending"}:{field:E(e,{expr:"datum"}),order:n??"ascending"}}function fo(e,t,n){const{format:i,formatType:r,labelAngle:s,labelAnchor:o,labelOrient:a,labelExpr:c}=ds(["format","formatType","labelAngle","labelAnchor","labelOrient","labelExpr"],e.header,n,t),l=ra({fieldOrDatumDef:e,format:i,formatType:r,expr:"parent",config:n}).signal,u=fs(t,a);return{text:{signal:c?Sn(Sn(c,"datum.label",l),"datum.value",E(e,{expr:"parent"})):l},...t==="row"?{orient:"left"}:{},style:"guide-label",frame:"group",...Td(s,u),...kd(u,s,o),...Ad(n,e,t,Fy,Rf)}}function Bx(e,t,n,i,r){if(r){let s=null;const{facetFieldDef:o}=i,a=e.config?e.config:void 0;if(o&&r.labels){const{labelOrient:f}=ds(["labelOrient"],o.header,a,t);(t==="row"&&!G(["top","bottom"],f)||t==="column"&&!G(["left","right"],f))&&(s=fo(o,t,a))}const c=ze(e)&&!Ki(e.facet),l=r.axes,u=(l==null?void 0:l.length)>0;if(s||u){const f=t==="row"?"height":"width";return{name:e.getName(`${t}_${n}`),type:"group",role:`${t}-${n}`,...i.facetFieldDef?{from:{data:e.getName(`${t}_domain`)},sort:Ux(o,t)}:{},...u&&c?{from:{data:e.getName(`facet_domain_${t}`)}}:{},...s?{title:s}:{},...r.sizeSignal?{encode:{update:{[f]:r.sizeSignal}}}:{},...u?{axes:l}:{}}}}return null}const Wx={column:{start:0,end:1},row:{start:1,end:0}};function Gx(e,t){return Wx[t][e]}function Hx(e,t){const n={};for(const i of Me){const r=e[i];if(r!=null&&r.facetFieldDef){const{titleAnchor:s,titleOrient:o}=ds(["titleAnchor","titleOrient"],r.facetFieldDef.header,t,i),a=fs(i,o),c=Gx(s,a);c!==void 0&&(n[a]=c)}}return Q(n)?void 0:n}function Ad(e,t,n,i,r){const s={};for(const o of i){if(!r[o])continue;const a=ui(o,t==null?void 0:t.header,e,n);a!==void 0&&(s[r[o]]=a)}return s}function Oa(e){return[...or(e,"width"),...or(e,"height"),...or(e,"childWidth"),...or(e,"childHeight")]}function or(e,t){const n=t==="width"?"x":"y",i=e.component.layoutSize.get(t);if(!i||i==="merged")return[];const r=e.getSizeSignalRef(t).signal;if(i==="step"){const s=e.getScaleComponent(n);if(s){const o=s.get("type"),a=s.get("range");if(ye(o)&&rn(a)){const c=e.scaleName(n);return ze(e.parent)&&e.parent.component.resolve.scale[n]==="independent"?[Kc(c,a)]:[Kc(c,a),{name:r,update:Od(c,s,`domain('${c}').length`)}]}}throw new Error("layout size is step although width/height is not step.")}else if(i=="container"){const s=r.endsWith("width"),o=s?"containerSize()[0]":"containerSize()[1]",a=io(e.config.view,s?"width":"height"),c=`isFinite(${o}) ? ${o} : ${a}`;return[{name:r,init:c,on:[{update:c,events:"window:resize"}]}]}else return[{name:r,value:i}]}function Kc(e,t){const n=`${e}_step`;return T(t.step)?{name:n,update:t.step.signal}:{name:n,value:t.step}}function Od(e,t,n){const i=t.get("type"),r=t.get("padding"),s=le(t.get("paddingOuter"),r);let o=t.get("paddingInner");return o=i==="band"?o!==void 0?o:r:1,`bandspace(${n}, ${Xe(o)}, ${Xe(s)}) * ${e}_step`}function _d(e){return e==="childWidth"?"width":e==="childHeight"?"height":e}function Rd(e,t){return x(e).reduce((n,i)=>{const r=e[i];return{...n,...mi(t,r,i,s=>ie(s.value))}},{})}function Id(e,t){if(ze(t))return e==="theta"?"independent":"shared";if(vi(t))return"shared";if(Da(t))return ue(e)||e==="theta"||e==="radius"?"independent":"shared";throw new Error("invalid model type for resolve")}function _a(e,t){const n=e.scale[t],i=ue(t)?"axis":"legend";return n==="independent"?(e[i][t]==="shared"&&v($h(t)),"independent"):e[i][t]||"shared"}const qx={...Ay,disable:1,labelExpr:1,selections:1,opacity:1,shape:1,stroke:1,fill:1,size:1,strokeWidth:1,strokeDash:1,encode:1},Ld=x(qx);class Vx extends jt{}const Qc={symbols:Xx,gradient:Yx,labels:Kx,entries:Qx};function Xx(e,{fieldOrDatumDef:t,model:n,channel:i,legendCmpt:r,legendType:s}){if(s!=="symbol")return;const{markDef:o,encoding:a,config:c,mark:l}=n,u=o.filled&&l!=="trail";let f={...Ig({},n,Nm),...ad(n,{filled:u})};const d=r.get("symbolOpacity")??c.legend.symbolOpacity,g=r.get("symbolFillColor")??c.legend.symbolFillColor,p=r.get("symbolStrokeColor")??c.legend.symbolStrokeColor,h=d===void 0?Pd(a.opacity)??o.opacity:void 0;if(f.fill){if(i==="fill"||u&&i===Oe)delete f.fill;else if(f.fill.field)g?delete f.fill:(f.fill=ie(c.legend.symbolBaseFillColor??"black"),f.fillOpacity=ie(h??1));else if(k(f.fill)){const m=po(a.fill??a.color)??o.fill??(u&&o.color);m&&(f.fill=ie(m))}}if(f.stroke){if(i==="stroke"||!u&&i===Oe)delete f.stroke;else if(f.stroke.field||p)delete f.stroke;else if(k(f.stroke)){const m=le(po(a.stroke||a.color),o.stroke,u?o.color:void 0);m&&(f.stroke={value:m})}}if(i!==Pt){const m=S(t)&&Dd(n,r,t);m?f.opacity=[{test:m,...ie(h??1)},ie(c.legend.unselectedOpacity)]:h&&(f.opacity=ie(h))}return f={...f,...e},Q(f)?void 0:f}function Yx(e,{model:t,legendType:n,legendCmpt:i}){if(n!=="gradient")return;const{config:r,markDef:s,encoding:o}=t;let a={};const l=(i.get("gradientOpacity")??r.legend.gradientOpacity)===void 0?Pd(o.opacity)||s.opacity:void 0;return l&&(a.opacity=ie(l)),a={...a,...e},Q(a)?void 0:a}function Kx(e,{fieldOrDatumDef:t,model:n,channel:i,legendCmpt:r}){const s=n.legend(i)||{},o=n.config,a=S(t)?Dd(n,r,t):void 0,c=a?[{test:a,value:1},{value:o.legend.unselectedOpacity}]:void 0,{format:l,formatType:u}=s;let f;Nn(u)?f=Ke({fieldOrDatumDef:t,field:"datum.value",format:l,formatType:u,config:o}):l===void 0&&u===void 0&&o.customFormatTypes&&(t.type==="quantitative"&&o.numberFormatType?f=Ke({fieldOrDatumDef:t,field:"datum.value",format:o.numberFormat,formatType:o.numberFormatType,config:o}):t.type==="temporal"&&o.timeFormatType&&S(t)&&t.timeUnit===void 0&&(f=Ke({fieldOrDatumDef:t,field:"datum.value",format:o.timeFormat,formatType:o.timeFormatType,config:o})));const d={...c?{opacity:c}:{},...f?{text:f}:{},...e};return Q(d)?void 0:d}function Qx(e,{legendCmpt:t}){const n=t.get("selections");return n!=null&&n.length?{...e,fill:{value:"transparent"}}:e}function Pd(e){return zd(e,(t,n)=>Math.max(t,n.value))}function po(e){return zd(e,(t,n)=>le(t,n.value))}function zd(e,t){if(qm(e))return ce(e.condition).reduce(t,e.value);if(Je(e))return e.value}function Dd(e,t,n){const i=t.get("selections");if(!(i!=null&&i.length))return;const r=B(n.field);return i.map(s=>`(!length(data(${B(se(s)+kn)})) || (${s}[${r}] && indexof(${s}[${r}], datum.value) >= 0))`).join(" || ")}const Jc={direction:({direction:e})=>e,format:({fieldOrDatumDef:e,legend:t,config:n})=>{const{format:i,formatType:r}=t;return Zu(e,e.type,i,r,n,!1)},formatType:({legend:e,fieldOrDatumDef:t,scaleType:n})=>{const{formatType:i}=e;return ef(i,t,n)},gradientLength:e=>{const{legend:t,legendConfig:n}=e;return t.gradientLength??n.gradientLength??rv(e)},labelOverlap:({legend:e,legendConfig:t,scaleType:n})=>e.labelOverlap??t.labelOverlap??sv(n),symbolType:({legend:e,markDef:t,channel:n,encoding:i})=>e.symbolType??Zx(t.type,n,i.shape,t.shape),title:({fieldOrDatumDef:e,config:t})=>Vn(e,t,{allowDisabling:!0}),type:({legendType:e,scaleType:t,channel:n})=>{if(qn(n)&&Ye(t)){if(e==="gradient")return}else if(e==="symbol")return;return e},values:({fieldOrDatumDef:e,legend:t})=>Jx(t,e)};function Jx(e,t){const n=e.values;if(k(n))return gf(t,n);if(T(n))return n}function Zx(e,t,n,i){if(t!=="shape"){const r=po(n)??i;if(r)return r}switch(e){case"bar":case"rect":case"image":case"square":return"square";case"line":case"trail":case"rule":return"stroke";case"arc":case"point":case"circle":case"tick":case"geoshape":case"area":case"text":return"circle"}}function ev(e){const{legend:t}=e;return le(t.type,tv(e))}function tv({channel:e,timeUnit:t,scaleType:n}){if(qn(e)){if(G(["quarter","month","day"],t))return"symbol";if(Ye(n))return"gradient"}return"symbol"}function nv({legendConfig:e,legendType:t,orient:n,legend:i}){return i.direction??e[t?"gradientDirection":"symbolDirection"]??iv(n,t)}function iv(e,t){switch(e){case"top":case"bottom":return"horizontal";case"left":case"right":case"none":case void 0:return;default:return t==="gradient"?"horizontal":void 0}}function rv({legendConfig:e,model:t,direction:n,orient:i,scaleType:r}){const{gradientHorizontalMaxLength:s,gradientHorizontalMinLength:o,gradientVerticalMaxLength:a,gradientVerticalMinLength:c}=e;if(Ye(r))return n==="horizontal"?i==="top"||i==="bottom"?Zc(t,"width",o,s):o:Zc(t,"height",c,a)}function Zc(e,t,n,i){return{signal:`clamp(${e.getSizeSignalRef(t).signal}, ${n}, ${i})`}}function sv(e){if(G(["quantile","threshold","log","symlog"],e))return"greedy"}function jd(e){const t=ae(e)?ov(e):uv(e);return e.component.legends=t,t}function ov(e){const{encoding:t}=e,n={};for(const i of[Oe,...Lf]){const r=fe(t[i]);!r||!e.getScaleComponent(i)||i===_e&&S(r)&&r.type===hi||(n[i]=lv(e,i))}return n}function av(e,t){const n=e.scaleName(t);if(e.mark==="trail"){if(t==="color")return{stroke:n};if(t==="size")return{strokeWidth:n}}return t==="color"?e.markDef.filled?{fill:n}:{stroke:n}:{[t]:n}}function cv(e,t,n,i){switch(t){case"disable":return n!==void 0;case"values":return!!(n!=null&&n.values);case"title":if(t==="title"&&e===(i==null?void 0:i.title))return!0}return e===(n||{})[t]}function lv(e,t){var D;let n=e.legend(t);const{markDef:i,encoding:r,config:s}=e,o=s.legend,a=new Vx({},av(e,t));bx(e,t,a);const c=n!==void 0?!n:o.disable;if(a.set("disable",c,n!==void 0),c)return a;n=n||{};const l=e.getScaleComponent(t).get("type"),u=fe(r[t]),f=S(u)?(D=me(u.timeUnit))==null?void 0:D.unit:void 0,d=n.orient||s.legend.orient||"right",g=ev({legend:n,channel:t,timeUnit:f,scaleType:l}),p=nv({legend:n,legendType:g,orient:d,legendConfig:o}),h={legend:n,channel:t,model:e,markDef:i,encoding:r,fieldOrDatumDef:u,legendConfig:o,config:s,scaleType:l,orient:d,legendType:g,direction:p};for(const w of Ld){if(g==="gradient"&&w.startsWith("symbol")||g==="symbol"&&w.startsWith("gradient"))continue;const F=w in Jc?Jc[w](h):n[w];if(F!==void 0){const A=cv(F,w,n,e.fieldDef(t));(A||s.legend[w]===void 0)&&a.set(w,F,A)}}const m=(n==null?void 0:n.encoding)??{},y=a.get("selections"),b={},C={fieldOrDatumDef:u,model:e,channel:t,legendCmpt:a,legendType:g};for(const w of["labels","legend","title","symbols","gradient","entries"]){const F=Rd(m[w]??{},e),A=w in Qc?Qc[w](F,C):F;A!==void 0&&!Q(A)&&(b[w]={...y!=null&&y.length&&S(u)?{name:`${se(u.field)}_legend_${w}`}:{},...y!=null&&y.length?{interactive:!!y}:{},update:A})}return Q(b)||a.set("encode",b,!!(n!=null&&n.encoding)),a}function uv(e){const{legends:t,resolve:n}=e.component;for(const i of e.children){jd(i);for(const r of x(i.component.legends))n.legend[r]=_a(e.component.resolve,r),n.legend[r]==="shared"&&(t[r]=Md(t[r],i.component.legends[r]),t[r]||(n.legend[r]="independent",delete t[r]))}for(const i of x(t))for(const r of e.children)r.component.legends[i]&&n.legend[i]==="shared"&&delete r.component.legends[i];return t}function Md(e,t){var s,o,a,c;if(!e)return t.clone();const n=e.getWithExplicit("orient"),i=t.getWithExplicit("orient");if(n.explicit&&i.explicit&&n.value!==i.value)return;let r=!1;for(const l of Ld){const u=Vt(e.getWithExplicit(l),t.getWithExplicit(l),l,"legend",(f,d)=>{switch(l){case"symbolType":return fv(f,d);case"title":return vu(f,d);case"type":return r=!0,Ie("symbol")}return os(f,d,l,"legend")});e.setWithExplicit(l,u)}return r&&((o=(s=e.implicit)==null?void 0:s.encode)!=null&&o.gradient&&mr(e.implicit,["encode","gradient"]),(c=(a=e.explicit)==null?void 0:a.encode)!=null&&c.gradient&&mr(e.explicit,["encode","gradient"])),e}function fv(e,t){return t.value==="circle"?t:e}function dv(e,t,n,i){var r,s;e.encode??(e.encode={}),(r=e.encode)[t]??(r[t]={}),(s=e.encode[t]).update??(s.update={}),e.encode[t].update[n]=i}function Ud(e){const t=e.component.legends,n={};for(const r of x(t)){const s=e.getScaleComponent(r),o=J(s.get("domains"));if(n[o])for(const a of n[o])Md(a,t[r])||n[o].push(t[r]);else n[o]=[t[r].clone()]}return ve(n).flat().map(r=>pv(r,e.config)).filter(r=>r!==void 0)}function pv(e,t){var o,a,c;const{disable:n,labelExpr:i,selections:r,...s}=e.combine();if(!n){if(t.aria===!1&&s.aria==null&&(s.aria=!1),(o=s.encode)!=null&&o.symbols){const l=s.encode.symbols.update;l.fill&&l.fill.value!=="transparent"&&!l.stroke&&!s.stroke&&(l.stroke={value:"transparent"});for(const u of Lf)s[u]&&delete l[u]}if(s.title||delete s.title,i!==void 0){let l=i;(c=(a=s.encode)==null?void 0:a.labels)!=null&&c.update&&T(s.encode.labels.update.text)&&(l=Sn(i,"datum.label",s.encode.labels.update.text.signal)),dv(s,"labels","text",{signal:l})}return s}}function gv(e){return vi(e)||Da(e)?hv(e):Bd(e)}function hv(e){return e.children.reduce((t,n)=>t.concat(n.assembleProjections()),Bd(e))}function Bd(e){const t=e.component.projection;if(!t||t.merged)return[];const n=t.combine(),{name:i}=n;if(t.data){const r={signal:`[${t.size.map(o=>o.signal).join(", ")}]`},s=t.data.reduce((o,a)=>{const c=T(a)?a.signal:`data('${e.lookupDataSource(a)}')`;return G(o,c)||o.push(c),o},[]);if(s.length<=0)throw new Error("Projection's fit didn't find any data sources");return[{name:i,size:r,fit:{signal:s.length>1?`[${s.join(", ")}]`:s[0]},...n}]}else return[{name:i,translate:{signal:"[width / 2, height / 2]"},...n}]}const mv=["type","clipAngle","clipExtent","center","rotate","precision","reflectX","reflectY","coefficient","distance","fraction","lobes","parallel","radius","ratio","spacing","tilt"];class Wd extends jt{constructor(t,n,i,r){super({...n},{name:t}),this.specifiedProjection=n,this.size=i,this.data=r,this.merged=!1}get isFit(){return!!this.data}}function Gd(e){e.component.projection=ae(e)?yv(e):vv(e)}function yv(e){if(e.hasProjection){const t=ke(e.specifiedProjection),n=!(t&&(t.scale!=null||t.translate!=null)),i=n?[e.getSizeSignalRef("width"),e.getSizeSignalRef("height")]:void 0,r=n?bv(e):void 0,s=new Wd(e.projectionName(!0),{...ke(e.config.projection),...t},i,r);return s.get("type")||s.set("type","equalEarth",!1),s}}function bv(e){const t=[],{encoding:n}=e;for(const i of[[it,nt],[Be,rt]])(fe(n[i[0]])||fe(n[i[1]]))&&t.push({signal:e.getName(`geojson_${t.length}`)});return e.channelHasField(_e)&&e.typedFieldDef(_e).type===hi&&t.push({signal:e.getName(`geojson_${t.length}`)}),t.length===0&&t.push(e.requestDataName(ee.Main)),t}function xv(e,t){const n=wo(mv,r=>!!(!Gn(e.explicit,r)&&!Gn(t.explicit,r)||Gn(e.explicit,r)&&Gn(t.explicit,r)&&Pe(e.get(r),t.get(r))));if(Pe(e.size,t.size)){if(n)return e;if(Pe(e.explicit,{}))return t;if(Pe(t.explicit,{}))return e}return null}function vv(e){if(e.children.length===0)return;let t;for(const i of e.children)Gd(i);const n=wo(e.children,i=>{const r=i.component.projection;if(r)if(t){const s=xv(t,r);return s&&(t=s),!!s}else return t=r,!0;else return!0});if(t&&n){const i=e.projectionName(!0),r=new Wd(i,t.specifiedProjection,t.size,P(t.data));for(const s of e.children){const o=s.component.projection;o&&(o.isFit&&r.data.push(...s.component.projection.data),s.renameProjection(o.get("name"),i),o.merged=!0)}return r}}function Sv(e,t,n,i){if(Ji(t,n)){const r=ae(e)?e.axis(n)??e.legend(n)??{}:{},s=E(t,{expr:"datum"}),o=E(t,{expr:"datum",binSuffix:"end"});return{formulaAs:E(t,{binSuffix:"range",forAs:!0}),formula:Yi(s,o,r.format,r.formatType,i)}}return{}}function Hd(e,t){return`${du(e)}_${t}`}function Ev(e,t){return{signal:e.getName(`${t}_bins`),extentSignal:e.getName(`${t}_extent`)}}function Ra(e,t,n){const i=es(n,void 0)??{},r=Hd(i,t);return e.getName(`${r}_bins`)}function $v(e){return"as"in e}function el(e,t,n){let i,r;$v(e)?i=I(e.as)?[e.as,`${e.as}_end`]:[e.as[0],e.as[1]]:i=[E(e,{forAs:!0}),E(e,{binSuffix:"end",forAs:!0})];const s={...es(t,void 0)},o=Hd(s,e.field),{signal:a,extentSignal:c}=Ev(n,o);if(Wr(s.extent)){const u=s.extent;r=$d(n,u.param,u),delete s.extent}const l={bin:s,field:e.field,as:[i],...a?{signal:a}:{},...c?{extentSignal:c}:{},...r?{span:r}:{}};return{key:o,binComponent:l}}class pt extends Y{clone(){return new pt(null,P(this.bins))}constructor(t,n){super(t),this.bins=n}static makeFromEncoding(t,n){const i=n.reduceFieldDef((r,s,o)=>{if(Ne(s)&&te(s.bin)){const{key:a,binComponent:c}=el(s,s.bin,n);r[a]={...c,...r[a],...Sv(n,s,o,n.config)}}return r},{});return Q(i)?null:new pt(t,i)}static makeFromTransform(t,n,i){const{key:r,binComponent:s}=el(n,n.bin,i);return new pt(t,{[r]:s})}merge(t,n){for(const i of x(t.bins))i in this.bins?(n(t.bins[i].signal,this.bins[i].signal),this.bins[i].as=ut([...this.bins[i].as,...t.bins[i].as],W)):this.bins[i]=t.bins[i];for(const i of t.children)t.removeChild(i),i.parent=this;t.remove()}producedFields(){return new Set(ve(this.bins).map(t=>t.as).flat(2))}dependentFields(){return new Set(ve(this.bins).map(t=>t.field))}hash(){return`Bin ${W(this.bins)}`}assemble(){return ve(this.bins).flatMap(t=>{const n=[],[i,...r]=t.as,{extent:s,...o}=t.bin,a={type:"bin",field:Ue(t.field),as:i,signal:t.signal,...Wr(s)?{extent:null}:{extent:s},...t.span?{span:{signal:`span(${t.span})`}}:{},...o};!s&&t.extentSignal&&(n.push({type:"extent",field:Ue(t.field),signal:t.extentSignal}),a.extent={signal:t.extentSignal}),n.push(a);for(const c of r)for(let l=0;l<2;l++)n.push({type:"formula",expr:E({field:i[l]},{expr:"datum"}),as:c[l]});return t.formula&&n.push({type:"formula",expr:t.formula,as:t.formulaAs}),n})}}function wv(e,t,n,i){var s;const r=ae(i)?i.encoding[vt(t)]:void 0;if(Ne(n)&&ae(i)&&of(n,r,i.markDef,i.config)){e.add(E(n,{})),e.add(E(n,{suffix:"end"}));const{mark:o,markDef:a,config:c}=i,l=qt({fieldDef:n,markDef:a,config:c});zi(o)&&l!==.5&&ue(t)&&(e.add(E(n,{suffix:as})),e.add(E(n,{suffix:cs}))),n.bin&&Ji(n,t)&&e.add(E(n,{binSuffix:"range"}))}else if(nu(t)){const o=tu(t);e.add(i.getName(o))}else e.add(E(n));return Pn(n)&&gm((s=n.scale)==null?void 0:s.range)&&e.add(n.scale.range.field),e}function Cv(e,t){for(const n of x(t)){const i=t[n];for(const r of x(i))n in e?e[n][r]=new Set([...e[n][r]??[],...i[r]]):e[n]={[r]:i[r]}}}class Qe extends Y{clone(){return new Qe(null,new Set(this.dimensions),P(this.measures))}constructor(t,n,i){super(t),this.dimensions=n,this.measures=i}get groupBy(){return this.dimensions}static makeFromEncoding(t,n){let i=!1;n.forEachFieldDef(o=>{o.aggregate&&(i=!0)});const r={},s=new Set;return!i||(n.forEachFieldDef((o,a)=>{const{aggregate:c,field:l}=o;if(c)if(c==="count")r["*"]??(r["*"]={}),r["*"].count=new Set([E(o,{forAs:!0})]);else{if(Ot(c)||nn(c)){const u=Ot(c)?"argmin":"argmax",f=c[u];r[f]??(r[f]={}),r[f][u]=new Set([E({op:u,field:f},{forAs:!0})])}else r[l]??(r[l]={}),r[l][c]=new Set([E(o,{forAs:!0})]);zt(a)&&n.scaleDomain(a)==="unaggregated"&&(r[l]??(r[l]={}),r[l].min=new Set([E({field:l,aggregate:"min"},{forAs:!0})]),r[l].max=new Set([E({field:l,aggregate:"max"},{forAs:!0})]))}else wv(s,a,o,n)}),s.size+x(r).length===0)?null:new Qe(t,s,r)}static makeFromTransform(t,n){const i=new Set,r={};for(const s of n.aggregate){const{op:o,field:a,as:c}=s;o&&(o==="count"?(r["*"]??(r["*"]={}),r["*"].count=new Set([c||E(s,{forAs:!0})])):(r[a]??(r[a]={}),r[a][o]=new Set([c||E(s,{forAs:!0})])))}for(const s of n.groupby??[])i.add(s);return i.size+x(r).length===0?null:new Qe(t,i,r)}merge(t){return Vl(this.dimensions,t.dimensions)?(Cv(this.measures,t.measures),!0):(jh("different dimensions, cannot merge"),!1)}addDimensions(t){t.forEach(this.dimensions.add,this.dimensions)}dependentFields(){return new Set([...this.dimensions,...x(this.measures)])}producedFields(){const t=new Set;for(const n of x(this.measures))for(const i of x(this.measures[n])){const r=this.measures[n][i];r.size===0?t.add(`${i}_${n}`):r.forEach(t.add,t)}return t}hash(){return`Aggregate ${W({dimensions:this.dimensions,measures:this.measures})}`}assemble(){const t=[],n=[],i=[];for(const s of x(this.measures))for(const o of x(this.measures[s]))for(const a of this.measures[s][o])i.push(a),t.push(o),n.push(s==="*"?null:Ue(s));return{type:"aggregate",groupby:[...this.dimensions].map(Ue),ops:t,fields:n,as:i}}}class bi extends Y{constructor(t,n,i,r){super(t),this.model=n,this.name=i,this.data=r;for(const s of Me){const o=n.facet[s];if(o){const{bin:a,sort:c}=o;this[s]={name:n.getName(`${s}_domain`),fields:[E(o),...te(a)?[E(o,{binSuffix:"end"})]:[]],...ft(c)?{sortField:c}:k(c)?{sortIndexField:li(o,s)}:{}}}}this.childModel=n.child}hash(){let t="Facet";for(const n of Me)this[n]&&(t+=` ${n.charAt(0)}:${W(this[n])}`);return t}get fields(){var n;const t=[];for(const i of Me)(n=this[i])!=null&&n.fields&&t.push(...this[i].fields);return t}dependentFields(){const t=new Set(this.fields);for(const n of Me)this[n]&&(this[n].sortField&&t.add(this[n].sortField.field),this[n].sortIndexField&&t.add(this[n].sortIndexField));return t}producedFields(){return new Set}getSource(){return this.name}getChildIndependentFieldsWithStep(){const t={};for(const n of St){const i=this.childModel.component.scales[n];if(i&&!i.merged){const r=i.get("type"),s=i.get("range");if(ye(r)&&rn(s)){const o=ps(this.childModel,n),a=za(o);a?t[n]=a:v(Do(n))}}}return t}assembleRowColumnHeaderData(t,n,i){const r={row:"y",column:"x",facet:void 0}[t],s=[],o=[],a=[];r&&i&&i[r]&&(n?(s.push(`distinct_${i[r]}`),o.push("max")):(s.push(i[r]),o.push("distinct")),a.push(`distinct_${i[r]}`));const{sortField:c,sortIndexField:l}=this[t];if(c){const{op:u=Kr,field:f}=c;s.push(f),o.push(u),a.push(E(c,{forAs:!0}))}else l&&(s.push(l),o.push("max"),a.push(l));return{name:this[t].name,source:n??this.data,transform:[{type:"aggregate",groupby:this[t].fields,...s.length?{fields:s,ops:o,as:a}:{}}]}}assembleFacetHeaderData(t){var c;const{columns:n}=this.model.layout,{layoutHeaders:i}=this.model.component,r=[],s={};for(const l of Ta){for(const u of Aa){const f=(i[l]&&i[l][u])??[];for(const d of f)if(((c=d.axes)==null?void 0:c.length)>0){s[l]=!0;break}}if(s[l]){const u=`length(data("${this.facet.name}"))`,f=l==="row"?n?{signal:`ceil(${u} / ${n})`}:1:n?{signal:`min(${u}, ${n})`}:{signal:u};r.push({name:`${this.facet.name}_${l}`,transform:[{type:"sequence",start:0,stop:f}]})}}const{row:o,column:a}=s;return(o||a)&&r.unshift(this.assembleRowColumnHeaderData("facet",null,t)),r}assemble(){const t=[];let n=null;const i=this.getChildIndependentFieldsWithStep(),{column:r,row:s,facet:o}=this;if(r&&s&&(i.x||i.y)){n=`cross_${this.column.name}_${this.row.name}`;const a=[].concat(i.x??[],i.y??[]),c=a.map(()=>"distinct");t.push({name:n,source:this.data,transform:[{type:"aggregate",groupby:this.fields,fields:a,ops:c}]})}for(const a of[kt,Ft])this[a]&&t.push(this.assembleRowColumnHeaderData(a,n,i));if(o){const a=this.assembleFacetHeaderData(i);a&&t.push(...a)}return t}}function tl(e){return e.startsWith("'")&&e.endsWith("'")||e.startsWith('"')&&e.endsWith('"')?e.slice(1,-1):e}function Nv(e,t){const n=Fo(e);if(t==="number")return`toNumber(${n})`;if(t==="boolean")return`toBoolean(${n})`;if(t==="string")return`toString(${n})`;if(t==="date")return`toDate(${n})`;if(t==="flatten")return n;if(t.startsWith("date:")){const i=tl(t.slice(5,t.length));return`timeParse(${n},'${i}')`}else if(t.startsWith("utc:")){const i=tl(t.slice(4,t.length));return`utcParse(${n},'${i}')`}else return v(Xg(t)),null}function Fv(e){const t={};return hr(e.filter,n=>{if(zu(n)){let i=null;Bo(n)?i=Le(n.equal):Go(n)?i=Le(n.lte):Wo(n)?i=Le(n.lt):Ho(n)?i=Le(n.gt):qo(n)?i=Le(n.gte):Vo(n)?i=n.range[0]:Xo(n)&&(i=(n.oneOf??n.in)[0]),i&&(In(i)?t[n.field]="date":re(i)?t[n.field]="number":I(i)&&(t[n.field]="string")),n.timeUnit&&(t[n.field]="date")}}),t}function kv(e){const t={};function n(i){si(i)?t[i.field]="date":i.type==="quantitative"&&Cg(i.aggregate)?t[i.field]="number":Jn(i.field)>1?i.field in t||(t[i.field]="flatten"):Pn(i)&&ft(i.sort)&&Jn(i.sort.field)>1&&(i.sort.field in t||(t[i.sort.field]="flatten"))}if((ae(e)||ze(e))&&e.forEachFieldDef((i,r)=>{if(Ne(i))n(i);else{const s=_n(r),o=e.fieldDef(s);n({...i,type:o.type})}}),ae(e)){const{mark:i,markDef:r,encoding:s}=e;if(sn(i)&&!e.encoding.order){const o=r.orient==="horizontal"?"y":"x",a=s[o];S(a)&&a.type==="quantitative"&&!(a.field in t)&&(t[a.field]="number")}}return t}function Tv(e){const t={};if(ae(e)&&e.component.selection)for(const n of x(e.component.selection)){const i=e.component.selection[n];for(const r of i.project.items)!r.channel&&Jn(r.field)>1&&(t[r.field]="flatten")}return t}class $e extends Y{clone(){return new $e(null,P(this._parse))}constructor(t,n){super(t),this._parse=n}hash(){return`Parse ${W(this._parse)}`}static makeExplicit(t,n,i){var o;let r={};const s=n.data;return!Wt(s)&&((o=s==null?void 0:s.format)!=null&&o.parse)&&(r=s.format.parse),this.makeWithAncestors(t,r,{},i)}static makeWithAncestors(t,n,i,r){for(const a of x(i)){const c=r.getWithExplicit(a);c.value!==void 0&&(c.explicit||c.value===i[a]||c.value==="derived"||i[a]==="flatten"?delete i[a]:v(fc(a,i[a],c.value)))}for(const a of x(n)){const c=r.get(a);c!==void 0&&(c===n[a]?delete n[a]:v(fc(a,n[a],c)))}const s=new jt(n,i);r.copyAll(s);const o={};for(const a of x(s.combine())){const c=s.get(a);c!==null&&(o[a]=c)}return x(o).length===0||r.parseNothing?null:new $e(t,o)}get parse(){return this._parse}merge(t){this._parse={...this._parse,...t.parse},t.remove()}assembleFormatParse(){const t={};for(const n of x(this._parse)){const i=this._parse[n];Jn(n)===1&&(t[n]=i)}return t}producedFields(){return new Set(x(this._parse))}dependentFields(){return new Set(x(this._parse))}assembleTransforms(t=!1){return x(this._parse).filter(n=>t?Jn(n)>1:!0).map(n=>{const i=Nv(n,this._parse[n]);return i?{type:"formula",expr:i,as:ko(n)}:null}).filter(n=>n!==null)}}class Kt extends Y{clone(){return new Kt(null)}constructor(t){super(t)}dependentFields(){return new Set}producedFields(){return new Set([Ze])}hash(){return"Identifier"}assemble(){return{type:"identifier",as:Ze}}}class tr extends Y{clone(){return new tr(null,this.params)}constructor(t,n){super(t),this.params=n}dependentFields(){return new Set}producedFields(){}hash(){return`Graticule ${W(this.params)}`}assemble(){return{type:"graticule",...this.params===!0?{}:this.params}}}class nr extends Y{clone(){return new nr(null,this.params)}constructor(t,n){super(t),this.params=n}dependentFields(){return new Set}producedFields(){return new Set([this.params.as??"data"])}hash(){return`Hash ${W(this.params)}`}assemble(){return{type:"sequence",...this.params}}}class Tn extends Y{constructor(t){super(null),t??(t={name:"source"});let n;if(Wt(t)||(n=t.format?{...Te(t.format,["parse"])}:{}),Di(t))this._data={values:t.values};else if(oi(t)){if(this._data={url:t.url},!n.type){let i=/(?:\.([^.]+))?$/.exec(t.url)[1];G(["json","csv","tsv","dsv","topojson"],i)||(i="json"),n.type=i}}else Zf(t)?this._data={values:[{type:"Sphere"}]}:(Qf(t)||Wt(t))&&(this._data={});this._generator=Wt(t),t.name&&(this._name=t.name),n&&!Q(n)&&(this._data.format=n)}dependentFields(){return new Set}producedFields(){}get data(){return this._data}hasName(){return!!this._name}get isGenerator(){return this._generator}get dataName(){return this._name}set dataName(t){this._name=t}set parent(t){throw new Error("Source nodes have to be roots.")}remove(){throw new Error("Source nodes are roots and cannot be removed.")}hash(){throw new Error("Cannot hash sources")}assemble(){return{name:this._name,...this._data,transform:[]}}}var nl=function(e,t,n,i,r){if(i==="m")throw new TypeError("Private method is not writable");if(i==="a"&&!r)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return i==="a"?r.call(e,n):r?r.value=n:t.set(e,n),n},Av=function(e,t,n,i){if(n==="a"&&!i)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!i:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?i:n==="a"?i.call(e):i?i.value:t.get(e)},Ti;function Ia(e){return e instanceof Tn||e instanceof tr||e instanceof nr}class La{constructor(){Ti.set(this,void 0),nl(this,Ti,!1,"f")}setModified(){nl(this,Ti,!0,"f")}get modifiedFlag(){return Av(this,Ti,"f")}}Ti=new WeakMap;class zn extends La{getNodeDepths(t,n,i){i.set(t,n);for(const r of t.children)this.getNodeDepths(r,n+1,i);return i}optimize(t){const i=[...this.getNodeDepths(t,0,new Map).entries()].sort((r,s)=>s[1]-r[1]);for(const r of i)this.run(r[0]);return this.modifiedFlag}}class Pa extends La{optimize(t){this.run(t);for(const n of t.children)this.optimize(n);return this.modifiedFlag}}class Ov extends Pa{mergeNodes(t,n){const i=n.shift();for(const r of n)t.removeChild(r),r.parent=i,r.remove()}run(t){const n=t.children.map(r=>r.hash()),i={};for(let r=0;r<n.length;r++)i[n[r]]===void 0?i[n[r]]=[t.children[r]]:i[n[r]].push(t.children[r]);for(const r of x(i))i[r].length>1&&(this.setModified(),this.mergeNodes(t,i[r]))}}class _v extends Pa{constructor(t){super(),this.requiresSelectionId=t&&ka(t)}run(t){t instanceof Kt&&(this.requiresSelectionId&&(Ia(t.parent)||t.parent instanceof Qe||t.parent instanceof $e)||(this.setModified(),t.remove()))}}class Rv extends La{optimize(t){return this.run(t,new Set),this.modifiedFlag}run(t,n){let i=new Set;t instanceof dt&&(i=t.producedFields(),Co(i,n)&&(this.setModified(),t.removeFormulas(n),t.producedFields.length===0&&t.remove()));for(const r of t.children)this.run(r,new Set([...n,...i]))}}class Iv extends Pa{constructor(){super()}run(t){t instanceof Ce&&!t.isRequired()&&(this.setModified(),t.remove())}}class Lv extends zn{run(t){if(!Ia(t)&&!(t.numChildren()>1)){for(const n of t.children)if(n instanceof $e)if(t instanceof $e)this.setModified(),t.merge(n);else{if(No(t.producedFields(),n.dependentFields()))continue;this.setModified(),n.swapWithParent()}}}}class Pv extends zn{run(t){const n=[...t.children],i=t.children.filter(r=>r instanceof $e);if(t.numChildren()>1&&i.length>=1){const r={},s=new Set;for(const o of i){const a=o.parse;for(const c of x(a))c in r?r[c]!==a[c]&&s.add(c):r[c]=a[c]}for(const o of s)delete r[o];if(!Q(r)){this.setModified();const o=new $e(t,r);for(const a of n){if(a instanceof $e)for(const c of x(r))delete a.parse[c];t.removeChild(a),a.parent=o,a instanceof $e&&x(a.parse).length===0&&a.remove()}}}}}class zv extends zn{run(t){t instanceof Ce||t.numChildren()>0||t instanceof bi||t instanceof Tn||(this.setModified(),t.remove())}}class Dv extends zn{run(t){const n=t.children.filter(r=>r instanceof dt),i=n.pop();for(const r of n)this.setModified(),i.merge(r)}}class jv extends zn{run(t){const n=t.children.filter(r=>r instanceof Qe),i={};for(const r of n){const s=W(r.groupBy);s in i||(i[s]=[]),i[s].push(r)}for(const r of x(i)){const s=i[r];if(s.length>1){const o=s.pop();for(const a of s)o.merge(a)&&(t.removeChild(a),a.parent=o,a.remove(),this.setModified())}}}}class Mv extends zn{constructor(t){super(),this.model=t}run(t){const n=!(Ia(t)||t instanceof yi||t instanceof $e||t instanceof Kt),i=[],r=[];for(const s of t.children)s instanceof pt&&(n&&!No(t.producedFields(),s.dependentFields())?i.push(s):r.push(s));if(i.length>0){const s=i.pop();for(const o of i)s.merge(o,this.model.renameSignal.bind(this.model));this.setModified(),t instanceof pt?t.merge(s,this.model.renameSignal.bind(this.model)):s.swapWithParent()}if(r.length>1){const s=r.pop();for(const o of r)s.merge(o,this.model.renameSignal.bind(this.model));this.setModified()}}}class Uv extends zn{run(t){const n=[...t.children];if(!vn(n,o=>o instanceof Ce)||t.numChildren()<=1)return;const r=[];let s;for(const o of n)if(o instanceof Ce){let a=o;for(;a.numChildren()===1;){const[c]=a.children;if(c instanceof Ce)a=c;else break}r.push(...a.children),s?(t.removeChild(o),o.parent=s.parent,s.parent.removeChild(s),s.parent=a,this.setModified()):s=a}else r.push(o);if(r.length){this.setModified();for(const o of r)o.parent.removeChild(o),o.parent=s}}}class Dn extends Y{clone(){return new Dn(null,P(this.transform))}constructor(t,n){super(t),this.transform=n}addDimensions(t){this.transform.groupby=ut(this.transform.groupby.concat(t),n=>n)}dependentFields(){const t=new Set;return this.transform.groupby&&this.transform.groupby.forEach(t.add,t),this.transform.joinaggregate.map(n=>n.field).filter(n=>n!==void 0).forEach(t.add,t),t}producedFields(){return new Set(this.transform.joinaggregate.map(this.getDefaultName))}getDefaultName(t){return t.as??E(t)}hash(){return`JoinAggregateTransform ${W(this.transform)}`}assemble(){const t=[],n=[],i=[];for(const s of this.transform.joinaggregate)n.push(s.op),i.push(this.getDefaultName(s)),t.push(s.field===void 0?null:s.field);const r=this.transform.groupby;return{type:"joinaggregate",as:i,ops:n,fields:t,...r!==void 0?{groupby:r}:{}}}}function Bv(e){return e.stack.stackBy.reduce((t,n)=>{const i=n.fieldDef,r=E(i);return r&&t.push(r),t},[])}function Wv(e){return k(e)&&e.every(t=>I(t))&&e.length>1}class At extends Y{clone(){return new At(null,P(this._stack))}constructor(t,n){super(t),this._stack=n}static makeFromTransform(t,n){const{stack:i,groupby:r,as:s,offset:o="zero"}=n,a=[],c=[];if(n.sort!==void 0)for(const f of n.sort)a.push(f.field),c.push(le(f.order,"ascending"));const l={field:a,order:c};let u;return Wv(s)?u=s:I(s)?u=[s,`${s}_end`]:u=[`${n.stack}_start`,`${n.stack}_end`],new At(t,{dimensionFieldDefs:[],stackField:i,groupby:r,offset:o,sort:l,facetby:[],as:u})}static makeFromEncoding(t,n){const i=n.stack,{encoding:r}=n;if(!i)return null;const{groupbyChannels:s,fieldChannel:o,offset:a,impute:c}=i,l=s.map(g=>{const p=r[g];return ht(p)}).filter(g=>!!g),u=Bv(n),f=n.encoding.order;let d;if(k(f)||S(f))d=yu(f);else{const g=af(f)?f.sort:o==="y"?"descending":"ascending";d=u.reduce((p,h)=>(p.field.includes(h)||(p.field.push(h),p.order.push(g)),p),{field:[],order:[]})}return new At(t,{dimensionFieldDefs:l,stackField:n.vgField(o),facetby:[],stackby:u,sort:d,offset:a,impute:c,as:[n.vgField(o,{suffix:"start",forAs:!0}),n.vgField(o,{suffix:"end",forAs:!0})]})}get stack(){return this._stack}addDimensions(t){this._stack.facetby.push(...t)}dependentFields(){const t=new Set;return t.add(this._stack.stackField),this.getGroupbyFields().forEach(t.add,t),this._stack.facetby.forEach(t.add,t),this._stack.sort.field.forEach(t.add,t),t}producedFields(){return new Set(this._stack.as)}hash(){return`Stack ${W(this._stack)}`}getGroupbyFields(){const{dimensionFieldDefs:t,impute:n,groupby:i}=this._stack;return t.length>0?t.map(r=>r.bin?n?[E(r,{binSuffix:"mid"})]:[E(r,{}),E(r,{binSuffix:"end"})]:[E(r)]).flat():i??[]}assemble(){const t=[],{facetby:n,dimensionFieldDefs:i,stackField:r,stackby:s,sort:o,offset:a,impute:c,as:l}=this._stack;if(c)for(const u of i){const{bandPosition:f=.5,bin:d}=u;if(d){const g=E(u,{expr:"datum"}),p=E(u,{expr:"datum",binSuffix:"end"});t.push({type:"formula",expr:`${f}*${g}+${1-f}*${p}`,as:E(u,{binSuffix:"mid",forAs:!0})})}t.push({type:"impute",field:r,groupby:[...s,...n],key:E(u,{binSuffix:"mid"}),method:"value",value:0})}return t.push({type:"stack",groupby:[...this.getGroupbyFields(),...n],field:r,sort:o,as:l,offset:a}),t}}class xi extends Y{clone(){return new xi(null,P(this.transform))}constructor(t,n){super(t),this.transform=n}addDimensions(t){this.transform.groupby=ut(this.transform.groupby.concat(t),n=>n)}dependentFields(){const t=new Set;return(this.transform.groupby??[]).forEach(t.add,t),(this.transform.sort??[]).forEach(n=>t.add(n.field)),this.transform.window.map(n=>n.field).filter(n=>n!==void 0).forEach(t.add,t),t}producedFields(){return new Set(this.transform.window.map(this.getDefaultName))}getDefaultName(t){return t.as??E(t)}hash(){return`WindowTransform ${W(this.transform)}`}assemble(){const t=[],n=[],i=[],r=[];for(const f of this.transform.window)n.push(f.op),i.push(this.getDefaultName(f)),r.push(f.param===void 0?null:f.param),t.push(f.field===void 0?null:f.field);const s=this.transform.frame,o=this.transform.groupby;if(s&&s[0]===null&&s[1]===null&&n.every(f=>Po(f)))return{type:"joinaggregate",as:i,ops:n,fields:t,...o!==void 0?{groupby:o}:{}};const a=[],c=[];if(this.transform.sort!==void 0)for(const f of this.transform.sort)a.push(f.field),c.push(f.order??"ascending");const l={field:a,order:c},u=this.transform.ignorePeers;return{type:"window",params:r,as:i,ops:n,fields:t,sort:l,...u!==void 0?{ignorePeers:u}:{},...o!==void 0?{groupby:o}:{},...s!==void 0?{frame:s}:{}}}}function Gv(e){function t(n){if(!(n instanceof bi)){const i=n.clone();if(i instanceof Ce){const r=ho+i.getSource();i.setSource(r),e.model.component.data.outputNodes[r]=i}else(i instanceof Qe||i instanceof At||i instanceof xi||i instanceof Dn)&&i.addDimensions(e.fields);for(const r of n.children.flatMap(t))r.parent=i;return[i]}return n.children.flatMap(t)}return t}function go(e){if(e instanceof bi)if(e.numChildren()===1&&!(e.children[0]instanceof Ce)){const t=e.children[0];(t instanceof Qe||t instanceof At||t instanceof xi||t instanceof Dn)&&t.addDimensions(e.fields),t.swapWithParent(),go(e)}else{const t=e.model.component.data.main;qd(t);const n=Gv(e),i=e.children.map(n).flat();for(const r of i)r.parent=t}else e.children.map(go)}function qd(e){if(e instanceof Ce&&e.type===ee.Main&&e.numChildren()===1){const t=e.children[0];t instanceof bi||(t.swapWithParent(),qd(e))}}const ho="scale_",ar=5;function mo(e){for(const t of e){for(const n of t.children)if(n.parent!==t)return!1;if(!mo(t.children))return!1}return!0}function He(e,t){let n=!1;for(const i of t)n=e.optimize(i)||n;return n}function il(e,t,n){let i=e.sources,r=!1;return r=He(new Iv,i)||r,r=He(new _v(t),i)||r,i=i.filter(s=>s.numChildren()>0),r=He(new zv,i)||r,i=i.filter(s=>s.numChildren()>0),n||(r=He(new Lv,i)||r,r=He(new Mv(t),i)||r,r=He(new Rv,i)||r,r=He(new Pv,i)||r,r=He(new jv,i)||r,r=He(new Dv,i)||r,r=He(new Ov,i)||r,r=He(new Uv,i)||r),e.sources=i,r}function Hv(e,t){mo(e.sources);let n=0,i=0;for(let r=0;r<ar&&il(e,t,!0);r++)n++;e.sources.map(go);for(let r=0;r<ar&&il(e,t,!1);r++)i++;mo(e.sources),Math.max(n,i)===ar&&v(`Maximum optimization runs(${ar}) reached.`)}class ge{constructor(t){Object.defineProperty(this,"signal",{enumerable:!0,get:t})}static fromName(t,n){return new ge(()=>t(n))}}function Vd(e){ae(e)?qv(e):Vv(e)}function qv(e){const t=e.component.scales;for(const n of x(t)){const i=Yv(e,n);if(t[n].setWithExplicit("domains",i),Qv(e,n),e.component.data.isFaceted){let s=e;for(;!ze(s)&&s.parent;)s=s.parent;if(s.component.resolve.scale[n]==="shared")for(const a of i.value)Nt(a)&&(a.data=ho+a.data.replace(ho,""))}}}function Vv(e){for(const n of e.children)Vd(n);const t=e.component.scales;for(const n of x(t)){let i,r=null;for(const s of e.children){const o=s.component.scales[n];if(o){i===void 0?i=o.getWithExplicit("domains"):i=Vt(i,o.getWithExplicit("domains"),"domains","scale",yo);const a=o.get("selectionExtent");r&&a&&r.param!==a.param&&v(Gg),r=a}}t[n].setWithExplicit("domains",i),r&&t[n].set("selectionExtent",r,!0)}}function Xv(e,t,n,i){if(e==="unaggregated"){const{valid:r,reason:s}=rl(t,n);if(!r){v(s);return}}else if(e===void 0&&i.useUnaggregatedDomain){const{valid:r}=rl(t,n);if(r)return"unaggregated"}return e}function Yv(e,t){const n=e.getScaleComponent(t).get("type"),{encoding:i}=e,r=Xv(e.scaleDomain(t),e.typedFieldDef(t),n,e.config.scale);return r!==e.scaleDomain(t)&&(e.specifiedScales[t]={...e.specifiedScales[t],domain:r}),t==="x"&&fe(i.x2)?fe(i.x)?Vt(Mt(n,r,e,"x"),Mt(n,r,e,"x2"),"domain","scale",yo):Mt(n,r,e,"x2"):t==="y"&&fe(i.y2)?fe(i.y)?Vt(Mt(n,r,e,"y"),Mt(n,r,e,"y2"),"domain","scale",yo):Mt(n,r,e,"y2"):Mt(n,r,e,t)}function Kv(e,t,n){return e.map(i=>({signal:`{data: ${ts(i,{timeUnit:n,type:t})}}`}))}function Ps(e,t,n){var r;const i=(r=me(n))==null?void 0:r.unit;return t==="temporal"||i?Kv(e,t,i):[e]}function Mt(e,t,n,i){const{encoding:r,markDef:s,mark:o,config:a,stack:c}=n,l=fe(r[i]),{type:u}=l,f=l.timeUnit;if(pm(t)){const p=Mt(e,void 0,n,i),h=Ps(t.unionWith,u,f);return lt([...h,...p.value])}else{if(T(t))return lt([t]);if(t&&t!=="unaggregated"&&!Gu(t))return lt(Ps(t,u,f))}if(c&&i===c.fieldChannel){if(c.offset==="normalize")return Ie([[0,1]]);const p=n.requestDataName(ee.Main);return Ie([{data:p,field:n.vgField(i,{suffix:"start"})},{data:p,field:n.vgField(i,{suffix:"end"})}])}const d=zt(i)&&S(l)?Jv(n,i,e):void 0;if(Et(l)){const p=Ps([l.datum],u,f);return Ie(p)}const g=l;if(t==="unaggregated"){const p=n.requestDataName(ee.Main),{field:h}=l;return Ie([{data:p,field:E({field:h,aggregate:"min"})},{data:p,field:E({field:h,aggregate:"max"})}])}else if(te(g.bin)){if(ye(e))return Ie(e==="bin-ordinal"?[]:[{data:Li(d)?n.requestDataName(ee.Main):n.requestDataName(ee.Raw),field:n.vgField(i,Ji(g,i)?{binSuffix:"range"}:{}),sort:d===!0||!X(d)?{field:n.vgField(i,{}),op:"min"}:d}]);{const{bin:p}=g;if(te(p)){const h=Ra(n,g.field,p);return Ie([new ge(()=>{const m=n.getSignalName(h);return`[${m}.start, ${m}.stop]`})])}else return Ie([{data:n.requestDataName(ee.Main),field:n.vgField(i,{})}])}}else if(g.timeUnit&&G(["time","utc"],e)){const p=r[vt(i)];if(of(g,p,s,a)){const h=n.requestDataName(ee.Main),m=qt({fieldDef:g,fieldDef2:p,markDef:s,config:a}),y=zi(o)&&m!==.5&&ue(i);return Ie([{data:h,field:n.vgField(i,y?{suffix:as}:{})},{data:h,field:n.vgField(i,{suffix:y?cs:"end"})}])}}return Ie(d?[{data:Li(d)?n.requestDataName(ee.Main):n.requestDataName(ee.Raw),field:n.vgField(i),sort:d}]:[{data:n.requestDataName(ee.Main),field:n.vgField(i)}])}function zs(e,t){const{op:n,field:i,order:r}=e;return{op:n??(t?"sum":Kr),...i?{field:Ue(i)}:{},...r?{order:r}:{}}}function Qv(e,t){var a;const n=e.component.scales[t],i=e.specifiedScales[t].domain,r=(a=e.fieldDef(t))==null?void 0:a.bin,s=Gu(i)&&i,o=Rn(r)&&Wr(r.extent)&&r.extent;(s||o)&&n.set("selectionExtent",s??o,!0)}function Jv(e,t,n){if(!ye(n))return;const i=e.fieldDef(t),r=i.sort;if(rf(r))return{op:"min",field:li(i,t),order:"ascending"};const{stack:s}=e,o=s?new Set([...s.groupbyFields,...s.stackBy.map(a=>a.fieldDef.field)]):void 0;if(ft(r)){const a=s&&!o.has(r.field);return zs(r,a)}else if(nf(r)){const{encoding:a,order:c}=r,l=e.fieldDef(a),{aggregate:u,field:f}=l,d=s&&!o.has(f);if(Ot(u)||nn(u))return zs({field:E(l),order:c},d);if(Po(u)||!u)return zs({op:u,field:f,order:c},d)}else{if(r==="descending")return{op:"min",field:e.vgField(t),order:"descending"};if(G(["ascending",void 0],r))return!0}}function rl(e,t){const{aggregate:n,type:i}=e;return n?I(n)&&!Fg.has(n)?{valid:!1,reason:mh(n)}:i==="quantitative"&&t==="log"?{valid:!1,reason:yh(e)}:{valid:!0}:{valid:!1,reason:hh(e)}}function yo(e,t,n,i){return e.explicit&&t.explicit&&v(Eh(n,i,e.value,t.value)),{explicit:e.explicit,value:[...e.value,...t.value]}}function Zv(e){const t=ut(e.map(o=>{if(Nt(o)){const{sort:a,...c}=o;return c}return o}),W),n=ut(e.map(o=>{if(Nt(o)){const a=o.sort;return a!==void 0&&!Li(a)&&("op"in a&&a.op==="count"&&delete a.field,a.order==="ascending"&&delete a.order),a}}).filter(o=>o!==void 0),W);if(t.length===0)return;if(t.length===1){const o=e[0];if(Nt(o)&&n.length>0){let a=n[0];if(n.length>1){v(pc);const c=n.filter(l=>X(l)&&"op"in l&&l.op!=="min");n.every(l=>X(l)&&"op"in l)&&c.length===1?a=c[0]:a=!0}else if(X(a)&&"field"in a){const c=a.field;o.field===c&&(a=a.order?{order:a.order}:!0)}return{...o,sort:a}}return o}const i=ut(n.map(o=>Li(o)||!("op"in o)||I(o.op)&&o.op in $g?o:(v(wh(o)),!0)),W);let r;i.length===1?r=i[0]:i.length>1&&(v(pc),r=!0);const s=ut(e.map(o=>Nt(o)?o.data:null),o=>o);return s.length===1&&s[0]!==null?{data:s[0],fields:t.map(a=>a.field),...r?{sort:r}:{}}:{fields:t,...r?{sort:r}:{}}}function za(e){if(Nt(e)&&I(e.field))return e.field;if(kg(e)){let t;for(const n of e.fields)if(Nt(n)&&I(n.field)){if(!t)t=n.field;else if(t!==n.field)return v(Ch),t}return v(Nh),t}else if(Tg(e)){v(Fh);const t=e.fields[0];return I(t)?t:void 0}}function ps(e,t){const i=e.component.scales[t].get("domains").map(r=>(Nt(r)&&(r.data=e.lookupDataSource(r.data)),r));return Zv(i)}function Xd(e){return vi(e)||Da(e)?e.children.reduce((t,n)=>t.concat(Xd(n)),sl(e)):sl(e)}function sl(e){return x(e.component.scales).reduce((t,n)=>{const i=e.component.scales[n];if(i.merged)return t;const r=i.combine(),{name:s,type:o,selectionExtent:a,domains:c,range:l,reverse:u,...f}=r,d=eS(r.range,s,n,e),g=ps(e,n),p=a?Xb(e,a,i,g):null;return t.push({name:s,type:o,...g?{domain:g}:{},...p?{domainRaw:p}:{},range:d,...u!==void 0?{reverse:u}:{},...f}),t},[])}function eS(e,t,n,i){if(ue(n)){if(rn(e))return{step:{signal:`${t}_step`}}}else if(X(e)&&Nt(e))return{...e,data:i.lookupDataSource(e.data)};return e}class Yd extends jt{constructor(t,n){super({},{name:t}),this.merged=!1,this.setWithExplicit("type",n)}domainDefinitelyIncludesZero(){return this.get("zero")!==!1?!0:vn(this.get("domains"),t=>k(t)&&t.length===2&&re(t[0])&&t[0]<=0&&re(t[1])&&t[1]>=0)}}const tS=["range","scheme"];function nS(e){const t=e.component.scales;for(const n of Br){const i=t[n];if(!i)continue;const r=iS(n,e);i.setWithExplicit("range",r)}}function ol(e,t){const n=e.fieldDef(t);if(n!=null&&n.bin){const{bin:i,field:r}=n,s=Re(t),o=e.getName(s);if(X(i)&&i.binned&&i.step!==void 0)return new ge(()=>{const a=e.scaleName(t),c=`(domain("${a}")[1] - domain("${a}")[0]) / ${i.step}`;return`${e.getSignalName(o)} / (${c})`});if(te(i)){const a=Ra(e,r,i);return new ge(()=>{const c=e.getSignalName(a),l=`(${c}.stop - ${c}.start) / ${c}.step`;return`${e.getSignalName(o)} / (${l})`})}}}function iS(e,t){const n=t.specifiedScales[e],{size:i}=t,s=t.getScaleComponent(e).get("type");for(const f of tS)if(n[f]!==void 0){const d=eo(s,f),g=Hu(e,f);if(!d)v(wu(s,f,e));else if(g)v(g);else switch(f){case"range":{const p=n.range;if(k(p)){if(ue(e))return lt(p.map(h=>{if(h==="width"||h==="height"){const m=t.getName(h),y=t.getSignalName.bind(t);return ge.fromName(y,m)}return h}))}else if(X(p))return lt({data:t.requestDataName(ee.Main),field:p.field,sort:{op:"min",field:t.vgField(e)}});return lt(p)}case"scheme":return lt(rS(n[f]))}}const o=e===oe||e==="xOffset"?"width":"height",a=i[o];if(mt(a)){if(ue(e))if(ye(s)){const f=Qd(a,t,e);if(f)return lt({step:f})}else v(Cu(o));else if(qi(e)){const f=e===Qt?"x":"y";if(t.getScaleComponent(f).get("type")==="band"){const p=Jd(a,s);if(p)return lt(p)}}}const{rangeMin:c,rangeMax:l}=n,u=sS(e,t);return(c!==void 0||l!==void 0)&&eo(s,"rangeMin")&&k(u)&&u.length===2?lt([c??u[0],l??u[1]]):Ie(u)}function rS(e){return dm(e)?{scheme:e.name,...Te(e,["name"])}:{scheme:e}}function Kd(e,t,n,{center:i}={}){const r=Re(e),s=t.getName(r),o=t.getSignalName.bind(t);return e===be&&De(n)?i?[ge.fromName(a=>`${o(a)}/2`,s),ge.fromName(a=>`-${o(a)}/2`,s)]:[ge.fromName(o,s),0]:i?[ge.fromName(a=>`-${o(a)}/2`,s),ge.fromName(a=>`${o(a)}/2`,s)]:[0,ge.fromName(o,s)]}function sS(e,t){const{size:n,config:i,mark:r,encoding:s}=t,{type:o}=fe(s[e]),c=t.getScaleComponent(e).get("type"),{domain:l,domainMid:u}=t.specifiedScales[e];switch(e){case oe:case be:{if(G(["point","band"],c)){const f=Zd(e,n,i.view);if(mt(f))return{step:Qd(f,t,e)}}return Kd(e,t,c)}case Qt:case gi:return oS(e,t,c);case Lt:{const f=t.component.scales[e].get("zero"),d=ep(r,f,i),g=lS(r,n,t,i);return ni(c)?cS(d,g,aS(c,i,l,e)):[d,g]}case We:return[0,Math.PI*2];case On:return[0,360];case tt:return[0,new ge(()=>{const f=t.getSignalName(ze(t.parent)?"child_width":"width"),d=t.getSignalName(ze(t.parent)?"child_height":"height");return`min(${f},${d})/2`})];case en:return[i.scale.minStrokeWidth,i.scale.maxStrokeWidth];case tn:return[[1,0],[4,2],[2,1],[1,1],[1,2,4,2]];case _e:return"symbol";case Oe:case bt:case xt:return c==="ordinal"?o==="nominal"?"category":"ordinal":u!==void 0?"diverging":r==="rect"||r==="geoshape"?"heatmap":"ramp";case Pt:case Jt:case Zt:return[i.scale.minOpacity,i.scale.maxOpacity]}}function Qd(e,t,n){const{encoding:i}=t,r=t.getScaleComponent(n),s=_o(n),o=i[s];if(zf({step:e,offsetIsDiscrete:z(o)&&ju(o.type)})==="offset"&&bf(i,s)){const c=t.getScaleComponent(s);let u=`domain('${t.scaleName(s)}').length`;if(c.get("type")==="band"){const d=c.get("paddingInner")??c.get("padding")??0,g=c.get("paddingOuter")??c.get("padding")??0;u=`bandspace(${u}, ${d}, ${g})`}const f=r.get("paddingInner")??r.get("padding");return{signal:`${e.step} * ${u} / (1-${Rg(f)})`}}else return e.step}function Jd(e,t){if(zf({step:e,offsetIsDiscrete:ye(t)})==="offset")return{step:e.step}}function oS(e,t,n){const i=e===Qt?"x":"y",r=t.getScaleComponent(i);if(!r)return Kd(i,t,n,{center:!0});const s=r.get("type"),o=t.scaleName(i),{markDef:a,config:c}=t;if(s==="band"){const l=Zd(i,t.size,t.config.view);if(mt(l)){const u=Jd(l,n);if(u)return u}return[0,{signal:`bandwidth('${o}')`}]}else{const l=t.encoding[i];if(S(l)&&l.timeUnit){const u=Lu(l.timeUnit,p=>`scale('${o}', ${p})`),f=t.config.scale.bandWithNestedOffsetPaddingInner,d=qt({fieldDef:l,markDef:a,config:c})-.5,g=d!==0?` + ${d}`:"";if(f){const p=T(f)?`${f.signal}/2`+g:`${f/2+d}`,h=T(f)?`(1 - ${f.signal}/2)`+g:`${1-f/2+d}`;return[{signal:`${p} * (${u})`},{signal:`${h} * (${u})`}]}return[0,{signal:u}]}return Hl(`Cannot use ${e} scale if ${i} scale is not discrete.`)}}function Zd(e,t,n){const i=e===oe?"width":"height",r=t[i];return r||Fr(n,i)}function aS(e,t,n,i){switch(e){case"quantile":return t.scale.quantileCount;case"quantize":return t.scale.quantizeCount;case"threshold":return n!==void 0&&k(n)?n.length+1:(v(Ph(i)),3)}}function cS(e,t,n){const i=()=>{const r=Xe(t),s=Xe(e),o=`(${r} - ${s}) / (${n} - 1)`;return`sequence(${s}, ${r} + ${o}, ${o})`};return T(t)?new ge(i):{signal:i()}}function ep(e,t,n){if(t)return T(t)?{signal:`${t.signal} ? 0 : ${ep(e,!1,n)}`}:0;switch(e){case"bar":case"tick":return n.scale.minBandSize;case"line":case"trail":case"rule":return n.scale.minStrokeWidth;case"text":return n.scale.minFontSize;case"point":case"square":case"circle":return n.scale.minSize}throw new Error(Gr("size",e))}const al=.95;function lS(e,t,n,i){const r={x:ol(n,"x"),y:ol(n,"y")};switch(e){case"bar":case"tick":{if(i.scale.maxBandSize!==void 0)return i.scale.maxBandSize;const s=cl(t,r,i.view);return re(s)?s-1:new ge(()=>`${s.signal} - 1`)}case"line":case"trail":case"rule":return i.scale.maxStrokeWidth;case"text":return i.scale.maxFontSize;case"point":case"square":case"circle":{if(i.scale.maxSize)return i.scale.maxSize;const s=cl(t,r,i.view);return re(s)?Math.pow(al*s,2):new ge(()=>`pow(${al} * ${s.signal}, 2)`)}}throw new Error(Gr("size",e))}function cl(e,t,n){const i=mt(e.width)?e.width.step:Nr(n,"width"),r=mt(e.height)?e.height.step:Nr(n,"height");return t.x||t.y?new ge(()=>`min(${[t.x?t.x.signal:i,t.y?t.y.signal:r].join(", ")})`):Math.min(i,r)}function tp(e,t){ae(e)?uS(e,t):ip(e,t)}function uS(e,t){const n=e.component.scales,{config:i,encoding:r,markDef:s,specifiedScales:o}=e;for(const a of x(n)){const c=o[a],l=n[a],u=e.getScaleComponent(a),f=fe(r[a]),d=c[t],g=u.get("type"),p=u.get("padding"),h=u.get("paddingInner"),m=eo(g,t),y=Hu(a,t);if(d!==void 0&&(m?y&&v(y):v(wu(g,t,a))),m&&y===void 0)if(d!==void 0){const b=f.timeUnit,C=f.type;switch(t){case"domainMax":case"domainMin":In(c[t])||C==="temporal"||b?l.set(t,{signal:ts(c[t],{type:C,timeUnit:b})},!0):l.set(t,c[t],!0);break;default:l.copyKeyFromObject(t,c)}}else{const b=t in ll?ll[t]({model:e,channel:a,fieldOrDatumDef:f,scaleType:g,scalePadding:p,scalePaddingInner:h,domain:c.domain,domainMin:c.domainMin,domainMax:c.domainMax,markDef:s,config:i,hasNestedOffsetScale:xf(r,a),hasSecondaryRangeChannel:!!r[vt(a)]}):i.scale[t];b!==void 0&&l.set(t,b,!1)}}}const ll={bins:({model:e,fieldOrDatumDef:t})=>S(t)?fS(e,t):void 0,interpolate:({channel:e,fieldOrDatumDef:t})=>dS(e,t.type),nice:({scaleType:e,channel:t,domain:n,domainMin:i,domainMax:r,fieldOrDatumDef:s})=>pS(e,t,n,i,r,s),padding:({channel:e,scaleType:t,fieldOrDatumDef:n,markDef:i,config:r})=>gS(e,t,r.scale,n,i,r.bar),paddingInner:({scalePadding:e,channel:t,markDef:n,scaleType:i,config:r,hasNestedOffsetScale:s})=>hS(e,t,n.type,i,r.scale,s),paddingOuter:({scalePadding:e,channel:t,scaleType:n,scalePaddingInner:i,config:r,hasNestedOffsetScale:s})=>mS(e,t,n,i,r.scale,s),reverse:({fieldOrDatumDef:e,scaleType:t,channel:n,config:i})=>{const r=S(e)?e.sort:void 0;return yS(t,r,n,i.scale)},zero:({channel:e,fieldOrDatumDef:t,domain:n,markDef:i,scaleType:r,config:s,hasSecondaryRangeChannel:o})=>bS(e,t,n,i,r,s.scale,o)};function np(e){ae(e)?nS(e):ip(e,"range")}function ip(e,t){const n=e.component.scales;for(const i of e.children)t==="range"?np(i):tp(i,t);for(const i of x(n)){let r;for(const s of e.children){const o=s.component.scales[i];if(o){const a=o.getWithExplicit(t);r=Vt(r,a,t,"scale",Kf((c,l)=>{switch(t){case"range":return c.step&&l.step?c.step-l.step:0}return 0}))}}n[i].setWithExplicit(t,r)}}function fS(e,t){const n=t.bin;if(te(n)){const i=Ra(e,t.field,n);return new ge(()=>e.getSignalName(i))}else if(xe(n)&&Rn(n)&&n.step!==void 0)return{step:n.step}}function dS(e,t){if(G([Oe,bt,xt],e)&&t!=="nominal")return"hcl"}function pS(e,t,n,i,r,s){var o;if(!((o=ht(s))!=null&&o.bin||k(n)||r!=null||i!=null||G([Ae.TIME,Ae.UTC],e)))return ue(t)?!0:void 0}function gS(e,t,n,i,r,s){if(ue(e)){if(Ye(t)){if(n.continuousPadding!==void 0)return n.continuousPadding;const{type:o,orient:a}=r;if(o==="bar"&&!(S(i)&&(i.bin||i.timeUnit))&&(a==="vertical"&&e==="x"||a==="horizontal"&&e==="y"))return s.continuousBandSize}if(t===Ae.POINT)return n.pointPadding}}function hS(e,t,n,i,r,s=!1){if(e===void 0){if(ue(t)){const{bandPaddingInner:o,barBandPaddingInner:a,rectBandPaddingInner:c,bandWithNestedOffsetPaddingInner:l}=r;return s?l:le(o,n==="bar"?a:c)}else if(qi(t)&&i===Ae.BAND)return r.offsetBandPaddingInner}}function mS(e,t,n,i,r,s=!1){if(e===void 0){if(ue(t)){const{bandPaddingOuter:o,bandWithNestedOffsetPaddingOuter:a}=r;if(s)return a;if(n===Ae.BAND)return le(o,T(i)?{signal:`${i.signal}/2`}:i/2)}else if(qi(t)){if(n===Ae.POINT)return .5;if(n===Ae.BAND)return r.offsetBandPaddingOuter}}}function yS(e,t,n,i){if(n==="x"&&i.xReverse!==void 0)return De(e)&&t==="descending"?T(i.xReverse)?{signal:`!${i.xReverse.signal}`}:!i.xReverse:i.xReverse;if(De(e)&&t==="descending")return!0}function bS(e,t,n,i,r,s,o){if(!!n&&n!=="unaggregated"&&De(r)){if(k(n)){const c=n[0],l=n[n.length-1];if(re(c)&&c<=0&&re(l)&&l>=0)return!0}return!1}if(e==="size"&&t.type==="quantitative"&&!ni(r))return!0;if(!(S(t)&&t.bin)&&G([...St,...hg],e)){const{orient:c,type:l}=i;return G(["bar","area","line","trail"],l)&&(c==="horizontal"&&e==="y"||c==="vertical"&&e==="x")?!1:G(["bar","area"],l)&&!o?!0:s==null?void 0:s.zero}return!1}function xS(e,t,n,i,r=!1){const s=vS(t,n,i,r),{type:o}=e;return zt(t)?o!==void 0?xm(t,o)?S(n)&&!bm(o,n.type)?(v(vh(o,s)),s):o:(v(xh(t,o,s)),s):s:null}function vS(e,t,n,i){var r;switch(t.type){case"nominal":case"ordinal":{if(qn(e)||ks(e)==="discrete")return e==="shape"&&t.type==="ordinal"&&v(Ts(e,"ordinal")),"ordinal";if(ue(e)||qi(e)){if(G(["rect","bar","image","rule"],n.type)||i)return"band"}else if(n.type==="arc"&&e in Lo)return"band";const s=n[Re(e)];return Cn(s)||ri(t)&&((r=t.axis)!=null&&r.tickBand)?"band":"point"}case"temporal":return qn(e)?"time":ks(e)==="discrete"?(v(Ts(e,"temporal")),"ordinal"):S(t)&&t.timeUnit&&me(t.timeUnit).utc?"utc":"time";case"quantitative":return qn(e)?S(t)&&te(t.bin)?"bin-ordinal":"linear":ks(e)==="discrete"?(v(Ts(e,"quantitative")),"ordinal"):"linear";case"geojson":return}throw new Error(Eu(t.type))}function SS(e,{ignoreRange:t}={}){rp(e),Vd(e);for(const n of ym)tp(e,n);t||np(e)}function rp(e){ae(e)?e.component.scales=ES(e):e.component.scales=wS(e)}function ES(e){const{encoding:t,mark:n,markDef:i}=e,r={};for(const s of Br){const o=fe(t[s]);if(o&&n===Xu&&s===_e&&o.type===hi)continue;let a=o&&o.scale;if(o&&a!==null&&a!==!1){a??(a={});const c=xf(t,s),l=xS(a,s,o,i,c);r[s]=new Yd(e.scaleName(`${s}`,!0),{value:l,explicit:a.type===l})}}return r}const $S=Kf((e,t)=>hc(e)-hc(t));function wS(e){var t;const n=e.component.scales={},i={},r=e.component.resolve;for(const s of e.children){rp(s);for(const o of x(s.component.scales))if((t=r.scale)[o]??(t[o]=Id(o,e)),r.scale[o]==="shared"){const a=i[o],c=s.component.scales[o].getWithExplicit("type");a?am(a.value,c.value)?i[o]=Vt(a,c,"type","scale",$S):(r.scale[o]="independent",delete i[o]):i[o]=c}}for(const s of x(i)){const o=e.scaleName(s,!0),a=i[s];n[s]=new Yd(o,a);for(const c of e.children){const l=c.component.scales[s];l&&(c.renameScale(l.get("name"),o),l.merged=!0)}}return n}class Ds{constructor(){this.nameMap={}}rename(t,n){this.nameMap[t]=n}has(t){return this.nameMap[t]!==void 0}get(t){for(;this.nameMap[t]&&t!==this.nameMap[t];)t=this.nameMap[t];return t}}function ae(e){return(e==null?void 0:e.type)==="unit"}function ze(e){return(e==null?void 0:e.type)==="facet"}function Da(e){return(e==null?void 0:e.type)==="concat"}function vi(e){return(e==null?void 0:e.type)==="layer"}class ja{constructor(t,n,i,r,s,o,a){this.type=n,this.parent=i,this.config=s,this.correctDataNames=c=>{var l,u,f;return(l=c.from)!=null&&l.data&&(c.from.data=this.lookupDataSource(c.from.data)),(f=(u=c.from)==null?void 0:u.facet)!=null&&f.data&&(c.from.facet.data=this.lookupDataSource(c.from.facet.data)),c},this.parent=i,this.config=s,this.view=ke(a),this.name=t.name??r,this.title=Ut(t.title)?{text:t.title}:t.title?ke(t.title):void 0,this.scaleNameMap=i?i.scaleNameMap:new Ds,this.projectionNameMap=i?i.projectionNameMap:new Ds,this.signalNameMap=i?i.signalNameMap:new Ds,this.data=t.data,this.description=t.description,this.transforms=Ob(t.transform??[]),this.layout=n==="layer"||n==="unit"?{}:Ly(t,n,s),this.component={data:{sources:i?i.component.data.sources:[],outputNodes:i?i.component.data.outputNodes:{},outputNodeRefCounts:i?i.component.data.outputNodeRefCounts:{},isFaceted:Qr(t)||(i==null?void 0:i.component.data.isFaceted)&&t.data===void 0},layoutSize:new jt,layoutHeaders:{row:{},column:{},facet:{}},mark:null,resolve:{scale:{},axis:{},legend:{},...o?P(o):{}},selection:null,scales:null,projection:null,axes:{},legends:{}}}get width(){return this.getSizeSignalRef("width")}get height(){return this.getSizeSignalRef("height")}parse(){this.parseScale(),this.parseLayoutSize(),this.renameTopLevelLayoutSizeSignal(),this.parseSelections(),this.parseProjection(),this.parseData(),this.parseAxesAndHeaders(),this.parseLegends(),this.parseMarkGroup()}parseScale(){SS(this)}parseProjection(){Gd(this)}renameTopLevelLayoutSizeSignal(){this.getName("width")!=="width"&&this.renameSignal(this.getName("width"),"width"),this.getName("height")!=="height"&&this.renameSignal(this.getName("height"),"height")}parseLegends(){jd(this)}assembleEncodeFromView(t){const{style:n,...i}=t,r={};for(const s of x(i)){const o=i[s];o!==void 0&&(r[s]=ie(o))}return r}assembleGroupEncodeEntry(t){let n={};return this.view&&(n=this.assembleEncodeFromView(this.view)),!t&&(this.description&&(n.description=ie(this.description)),this.type==="unit"||this.type==="layer")?{width:this.getSizeSignalRef("width"),height:this.getSizeSignalRef("height"),...n}:Q(n)?void 0:n}assembleLayout(){if(!this.layout)return;const{spacing:t,...n}=this.layout,{component:i,config:r}=this,s=Hx(i.layoutHeaders,r);return{padding:t,...this.assembleDefaultLayout(),...n,...s?{titleBand:s}:{}}}assembleDefaultLayout(){return{}}assembleHeaderMarks(){const{layoutHeaders:t}=this.component;let n=[];for(const i of Me)t[i].title&&n.push(jx(this,i));for(const i of Ta)n=n.concat(Mx(this,i));return n}assembleAxes(){return Nx(this.component.axes,this.config)}assembleLegends(){return Ud(this)}assembleProjections(){return gv(this)}assembleTitle(){const{encoding:t,...n}=this.title??{},i={...pu(this.config.title).nonMarkTitleProperties,...n,...t?{encode:{update:t}}:{}};if(i.text)return G(["unit","layer"],this.type)?G(["middle",void 0],i.anchor)&&(i.frame??(i.frame="group")):i.anchor??(i.anchor="start"),Q(i)?void 0:i}assembleGroup(t=[]){const n={};t=t.concat(this.assembleSignals()),t.length>0&&(n.signals=t);const i=this.assembleLayout();i&&(n.layout=i),n.marks=[].concat(this.assembleHeaderMarks(),this.assembleMarks());const r=!this.parent||ze(this.parent)?Xd(this):[];r.length>0&&(n.scales=r);const s=this.assembleAxes();s.length>0&&(n.axes=s);const o=this.assembleLegends();return o.length>0&&(n.legends=o),n}getName(t){return se((this.name?`${this.name}_`:"")+t)}getDataName(t){return this.getName(ee[t].toLowerCase())}requestDataName(t){const n=this.getDataName(t),i=this.component.data.outputNodeRefCounts;return i[n]=(i[n]||0)+1,n}getSizeSignalRef(t){if(ze(this.parent)){const n=_d(t),i=Ur(n),r=this.component.scales[i];if(r&&!r.merged){const s=r.get("type"),o=r.get("range");if(ye(s)&&rn(o)){const a=r.get("name"),c=ps(this,i),l=za(c);if(l){const u=E({aggregate:"distinct",field:l},{expr:"datum"});return{signal:Od(a,r,u)}}else return v(Do(i)),null}}}return{signal:this.signalNameMap.get(this.getName(t))}}lookupDataSource(t){const n=this.component.data.outputNodes[t];return n?n.getSource():t}getSignalName(t){return this.signalNameMap.get(t)}renameSignal(t,n){this.signalNameMap.rename(t,n)}renameScale(t,n){this.scaleNameMap.rename(t,n)}renameProjection(t,n){this.projectionNameMap.rename(t,n)}scaleName(t,n){if(n)return this.getName(t);if(ru(t)&&zt(t)&&this.component.scales[t]||this.scaleNameMap.has(this.getName(t)))return this.scaleNameMap.get(this.getName(t))}projectionName(t){if(t)return this.getName("projection");if(this.component.projection&&!this.component.projection.merged||this.projectionNameMap.has(this.getName("projection")))return this.projectionNameMap.get(this.getName("projection"))}getScaleComponent(t){if(!this.component.scales)throw new Error("getScaleComponent cannot be called before parseScale(). Make sure you have called parseScale or use parseUnitModelWithScale().");const n=this.component.scales[t];return n&&!n.merged?n:this.parent?this.parent.getScaleComponent(t):void 0}getSelectionComponent(t,n){let i=this.component.selection[t];if(!i&&this.parent&&(i=this.parent.getSelectionComponent(t,n)),!i)throw new Error(Dg(n));return i}hasAxisOrientSignalRef(){var t,n;return((t=this.component.axes.x)==null?void 0:t.some(i=>i.hasOrientSignalRef()))||((n=this.component.axes.y)==null?void 0:n.some(i=>i.hasOrientSignalRef()))}}class sp extends ja{vgField(t,n={}){const i=this.fieldDef(t);if(i)return E(i,n)}reduceFieldDef(t,n){return uy(this.getMapping(),(i,r,s)=>{const o=ht(r);return o?t(i,o,s):i},n)}forEachFieldDef(t,n){fa(this.getMapping(),(i,r)=>{const s=ht(i);s&&t(s,r)},n)}}class gs extends Y{clone(){return new gs(null,P(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=P(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??"value",i[1]??"density"]}dependentFields(){return new Set([this.transform.density,...this.transform.groupby??[]])}producedFields(){return new Set(this.transform.as)}hash(){return`DensityTransform ${W(this.transform)}`}assemble(){const{density:t,...n}=this.transform,i={type:"kde",field:t,...n};return this.transform.groupby&&(i.resolve="shared"),i}}class hs extends Y{clone(){return new hs(null,P(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=P(n)}dependentFields(){return new Set([this.transform.extent])}producedFields(){return new Set([])}hash(){return`ExtentTransform ${W(this.transform)}`}assemble(){const{extent:t,param:n}=this.transform;return{type:"extent",field:t,signal:n}}}class ji extends Y{clone(){return new ji(null,{...this.filter})}constructor(t,n){super(t),this.filter=n}static make(t,n){const{config:i,mark:r,markDef:s}=n;if(V("invalid",s,i)!=="filter")return null;const a=n.reduceFieldDef((c,l,u)=>{const f=zt(u)&&n.getScaleComponent(u);if(f){const d=f.get("type");De(d)&&l.aggregate!=="count"&&!sn(r)&&(c[l.field]=l)}return c},{});return x(a).length?new ji(t,a):null}dependentFields(){return new Set(x(this.filter))}producedFields(){return new Set}hash(){return`FilterInvalid ${W(this.filter)}`}assemble(){const t=x(this.filter).reduce((n,i)=>{const r=this.filter[i],s=E(r,{expr:"datum"});return r!==null&&(r.type==="temporal"?n.push(`(isDate(${s}) || (isValid(${s}) && isFinite(+${s})))`):r.type==="quantitative"&&(n.push(`isValid(${s})`),n.push(`isFinite(+${s})`))),n},[]);return t.length>0?{type:"filter",expr:t.join(" && ")}:null}}class ms extends Y{clone(){return new ms(this.parent,P(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=P(n);const{flatten:i,as:r=[]}=this.transform;this.transform.as=i.map((s,o)=>r[o]??s)}dependentFields(){return new Set(this.transform.flatten)}producedFields(){return new Set(this.transform.as)}hash(){return`FlattenTransform ${W(this.transform)}`}assemble(){const{flatten:t,as:n}=this.transform;return{type:"flatten",fields:t,as:n}}}class ys extends Y{clone(){return new ys(null,P(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=P(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??"key",i[1]??"value"]}dependentFields(){return new Set(this.transform.fold)}producedFields(){return new Set(this.transform.as)}hash(){return`FoldTransform ${W(this.transform)}`}assemble(){const{fold:t,as:n}=this.transform;return{type:"fold",fields:t,as:n}}}class Yn extends Y{clone(){return new Yn(null,P(this.fields),this.geojson,this.signal)}static parseAll(t,n){if(n.component.projection&&!n.component.projection.isFit)return t;let i=0;for(const r of[[it,nt],[Be,rt]]){const s=r.map(o=>{const a=fe(n.encoding[o]);return S(a)?a.field:Et(a)?{expr:`${a.datum}`}:Je(a)?{expr:`${a.value}`}:void 0});(s[0]||s[1])&&(t=new Yn(t,s,null,n.getName(`geojson_${i++}`)))}if(n.channelHasField(_e)){const r=n.typedFieldDef(_e);r.type===hi&&(t=new Yn(t,null,r.field,n.getName(`geojson_${i++}`)))}return t}constructor(t,n,i,r){super(t),this.fields=n,this.geojson=i,this.signal=r}dependentFields(){const t=(this.fields??[]).filter(I);return new Set([...this.geojson?[this.geojson]:[],...t])}producedFields(){return new Set}hash(){return`GeoJSON ${this.geojson} ${this.signal} ${W(this.fields)}`}assemble(){return[...this.geojson?[{type:"filter",expr:`isValid(datum["${this.geojson}"])`}]:[],{type:"geojson",...this.fields?{fields:this.fields}:{},...this.geojson?{geojson:this.geojson}:{},signal:this.signal}]}}class Mi extends Y{clone(){return new Mi(null,this.projection,P(this.fields),P(this.as))}constructor(t,n,i,r){super(t),this.projection=n,this.fields=i,this.as=r}static parseAll(t,n){if(!n.projectionName())return t;for(const i of[[it,nt],[Be,rt]]){const r=i.map(o=>{const a=fe(n.encoding[o]);return S(a)?a.field:Et(a)?{expr:`${a.datum}`}:Je(a)?{expr:`${a.value}`}:void 0}),s=i[0]===Be?"2":"";(r[0]||r[1])&&(t=new Mi(t,n.projectionName(),r,[n.getName(`x${s}`),n.getName(`y${s}`)]))}return t}dependentFields(){return new Set(this.fields.filter(I))}producedFields(){return new Set(this.as)}hash(){return`Geopoint ${this.projection} ${W(this.fields)} ${W(this.as)}`}assemble(){return{type:"geopoint",projection:this.projection,fields:this.fields,as:this.as}}}class xn extends Y{clone(){return new xn(null,P(this.transform))}constructor(t,n){super(t),this.transform=n}dependentFields(){return new Set([this.transform.impute,this.transform.key,...this.transform.groupby??[]])}producedFields(){return new Set([this.transform.impute])}processSequence(t){const{start:n=0,stop:i,step:r}=t;return{signal:`sequence(${[n,i,...r?[r]:[]].join(",")})`}}static makeFromTransform(t,n){return new xn(t,n)}static makeFromEncoding(t,n){const i=n.encoding,r=i.x,s=i.y;if(S(r)&&S(s)){const o=r.impute?r:s.impute?s:void 0;if(o===void 0)return;const a=r.impute?s:s.impute?r:void 0,{method:c,value:l,frame:u,keyvals:f}=o.impute,d=Sf(n.mark,i);return new xn(t,{impute:o.field,key:a.field,...c?{method:c}:{},...l!==void 0?{value:l}:{},...u?{frame:u}:{},...f!==void 0?{keyvals:f}:{},...d.length?{groupby:d}:{}})}return null}hash(){return`Impute ${W(this.transform)}`}assemble(){const{impute:t,key:n,keyvals:i,method:r,groupby:s,value:o,frame:a=[null,null]}=this.transform,c={type:"impute",field:t,key:n,...i?{keyvals:db(i)?this.processSequence(i):i}:{},method:"value",...s?{groupby:s}:{},value:!r||r==="value"?o:null};if(r&&r!=="value"){const l={type:"window",as:[`imputed_${t}_value`],ops:[r],fields:[t],frame:a,ignorePeers:!1,...s?{groupby:s}:{}},u={type:"formula",expr:`datum.${t} === null ? datum.imputed_${t}_value : datum.${t}`,as:t};return[c,l,u]}else return[c]}}class bs extends Y{clone(){return new bs(null,P(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=P(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??n.on,i[1]??n.loess]}dependentFields(){return new Set([this.transform.loess,this.transform.on,...this.transform.groupby??[]])}producedFields(){return new Set(this.transform.as)}hash(){return`LoessTransform ${W(this.transform)}`}assemble(){const{loess:t,on:n,...i}=this.transform;return{type:"loess",x:n,y:t,...i}}}class Ui extends Y{clone(){return new Ui(null,P(this.transform),this.secondary)}constructor(t,n,i){super(t),this.transform=n,this.secondary=i}static make(t,n,i,r){const s=n.component.data.sources,{from:o}=i;let a=null;if(pb(o)){let c=cp(o.data,s);c||(c=new Tn(o.data),s.push(c));const l=n.getName(`lookup_${r}`);a=new Ce(c,l,ee.Lookup,n.component.data.outputNodeRefCounts),n.component.data.outputNodes[l]=a}else if(gb(o)){const c=o.param;i={as:c,...i};let l;try{l=n.getSelectionComponent(se(c),c)}catch{throw new Error(Bg(c))}if(a=l.materialized,!a)throw new Error(Wg(c))}return new Ui(t,i,a.getSource())}dependentFields(){return new Set([this.transform.lookup])}producedFields(){return new Set(this.transform.as?ce(this.transform.as):this.transform.from.fields)}hash(){return`Lookup ${W({transform:this.transform,secondary:this.secondary})}`}assemble(){let t;if(this.transform.from.fields)t={values:this.transform.from.fields,...this.transform.as?{as:ce(this.transform.as)}:{}};else{let n=this.transform.as;I(n)||(v(Qg),n="_lookup"),t={as:[n]}}return{type:"lookup",from:this.secondary,key:this.transform.from.key,fields:[this.transform.lookup],...t,...this.transform.default?{default:this.transform.default}:{}}}}class xs extends Y{clone(){return new xs(null,P(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=P(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??"prob",i[1]??"value"]}dependentFields(){return new Set([this.transform.quantile,...this.transform.groupby??[]])}producedFields(){return new Set(this.transform.as)}hash(){return`QuantileTransform ${W(this.transform)}`}assemble(){const{quantile:t,...n}=this.transform;return{type:"quantile",field:t,...n}}}class vs extends Y{clone(){return new vs(null,P(this.transform))}constructor(t,n){super(t),this.transform=n,this.transform=P(n);const i=this.transform.as??[void 0,void 0];this.transform.as=[i[0]??n.on,i[1]??n.regression]}dependentFields(){return new Set([this.transform.regression,this.transform.on,...this.transform.groupby??[]])}producedFields(){return new Set(this.transform.as)}hash(){return`RegressionTransform ${W(this.transform)}`}assemble(){const{regression:t,on:n,...i}=this.transform;return{type:"regression",x:n,y:t,...i}}}class Ss extends Y{clone(){return new Ss(null,P(this.transform))}constructor(t,n){super(t),this.transform=n}addDimensions(t){this.transform.groupby=ut((this.transform.groupby??[]).concat(t),n=>n)}producedFields(){}dependentFields(){return new Set([this.transform.pivot,this.transform.value,...this.transform.groupby??[]])}hash(){return`PivotTransform ${W(this.transform)}`}assemble(){const{pivot:t,value:n,groupby:i,limit:r,op:s}=this.transform;return{type:"pivot",field:t,value:n,...r!==void 0?{limit:r}:{},...s!==void 0?{op:s}:{},...i!==void 0?{groupby:i}:{}}}}class Es extends Y{clone(){return new Es(null,P(this.transform))}constructor(t,n){super(t),this.transform=n}dependentFields(){return new Set}producedFields(){return new Set}hash(){return`SampleTransform ${W(this.transform)}`}assemble(){return{type:"sample",size:this.transform.sample}}}function op(e){let t=0;function n(i,r){if(i instanceof Tn&&!i.isGenerator&&!oi(i.data)&&(e.push(r),r={name:null,source:r.name,transform:[]}),i instanceof $e&&(i.parent instanceof Tn&&!r.source?(r.format={...r.format,parse:i.assembleFormatParse()},r.transform.push(...i.assembleTransforms(!0))):r.transform.push(...i.assembleTransforms())),i instanceof bi){r.name||(r.name=`data_${t++}`),!r.source||r.transform.length>0?(e.push(r),i.data=r.name):i.data=r.source,e.push(...i.assemble());return}switch((i instanceof tr||i instanceof nr||i instanceof ji||i instanceof yi||i instanceof ci||i instanceof Mi||i instanceof Qe||i instanceof Ui||i instanceof xi||i instanceof Dn||i instanceof ys||i instanceof ms||i instanceof gs||i instanceof bs||i instanceof xs||i instanceof vs||i instanceof Kt||i instanceof Es||i instanceof Ss||i instanceof hs)&&r.transform.push(i.assemble()),(i instanceof pt||i instanceof dt||i instanceof xn||i instanceof At||i instanceof Yn)&&r.transform.push(...i.assemble()),i instanceof Ce&&(r.source&&r.transform.length===0?i.setSource(r.source):i.parent instanceof Ce?i.setSource(r.name):(r.name||(r.name=`data_${t++}`),i.setSource(r.name),i.numChildren()===1&&(e.push(r),r={name:null,source:r.name,transform:[]}))),i.numChildren()){case 0:i instanceof Ce&&(!r.source||r.transform.length>0)&&e.push(r);break;case 1:n(i.children[0],r);break;default:{r.name||(r.name=`data_${t++}`);let s=r.name;!r.source||r.transform.length>0?e.push(r):s=r.source;for(const o of i.children)n(o,{name:null,source:s,transform:[]});break}}}return n}function CS(e){const t=[],n=op(t);for(const i of e.children)n(i,{source:e.name,name:null,transform:[]});return t}function NS(e,t){const n=[],i=op(n);let r=0;for(const o of e.sources){o.hasName()||(o.dataName=`source_${r++}`);const a=o.assemble();i(o,a)}for(const o of n)o.transform.length===0&&delete o.transform;let s=0;for(const[o,a]of n.entries())(a.transform??[]).length===0&&!a.source&&n.splice(s++,0,n.splice(o,1)[0]);for(const o of n)for(const a of o.transform??[])a.type==="lookup"&&(a.from=e.outputNodes[a.from].getSource());for(const o of n)o.name in t&&(o.values=t[o.name]);return n}function FS(e){return e==="top"||e==="left"||T(e)?"header":"footer"}function kS(e){for(const t of Me)TS(e,t);ul(e,"x"),ul(e,"y")}function TS(e,t){var o;const{facet:n,config:i,child:r,component:s}=e;if(e.channelHasField(t)){const a=n[t],c=ui("title",null,i,t);let l=Vn(a,i,{allowDisabling:!0,includeDefault:c===void 0||!!c});r.component.layoutHeaders[t].title&&(l=k(l)?l.join(", "):l,l+=` / ${r.component.layoutHeaders[t].title}`,r.component.layoutHeaders[t].title=null);const u=ui("labelOrient",a.header,i,t),f=a.header!==null?le((o=a.header)==null?void 0:o.labels,i.header.labels,!0):!1,d=G(["bottom","right"],u)?"footer":"header";s.layoutHeaders[t]={title:a.header!==null?l:null,facetFieldDef:a,[d]:t==="facet"?[]:[ap(e,t,f)]}}}function ap(e,t,n){const i=t==="row"?"height":"width";return{labels:n,sizeSignal:e.child.component.layoutSize.get(i)?e.child.getSizeSignalRef(i):void 0,axes:[]}}function ul(e,t){const{child:n}=e;if(n.component.axes[t]){const{layoutHeaders:i,resolve:r}=e.component;if(r.axis[t]=_a(r,t),r.axis[t]==="shared"){const s=t==="x"?"column":"row",o=i[s];for(const a of n.component.axes[t]){const c=FS(a.get("orient"));o[c]??(o[c]=[ap(e,s,!1)]);const l=ki(a,"main",e.config,{header:!0});l&&o[c][0].axes.push(l),a.mainExtracted=!0}}}}function AS(e){Ma(e),Or(e,"width"),Or(e,"height")}function OS(e){Ma(e);const t=e.layout.columns===1?"width":"childWidth",n=e.layout.columns===void 0?"height":"childHeight";Or(e,t),Or(e,n)}function Ma(e){for(const t of e.children)t.parseLayoutSize()}function Or(e,t){const n=_d(t),i=Ur(n),r=e.component.resolve,s=e.component.layoutSize;let o;for(const a of e.children){const c=a.component.layoutSize.getWithExplicit(n),l=r.scale[i]??Id(i,e);if(l==="independent"&&c.value==="step"){o=void 0;break}if(o){if(l==="independent"&&o.value!==c.value){o=void 0;break}o=Vt(o,c,n,"")}else o=c}if(o){for(const a of e.children)e.renameSignal(a.getName(n),e.getName(t)),a.component.layoutSize.set(n,"merged",!1);s.setWithExplicit(t,o)}else s.setWithExplicit(t,{explicit:!1,value:void 0})}function _S(e){const{size:t,component:n}=e;for(const i of St){const r=Re(i);if(t[r]){const s=t[r];n.layoutSize.set(r,mt(s)?"step":s,!0)}else{const s=RS(e,r);n.layoutSize.set(r,s,!1)}}}function RS(e,t){const n=t==="width"?"x":"y",i=e.config,r=e.getScaleComponent(n);if(r){const s=r.get("type"),o=r.get("range");if(ye(s)){const a=Fr(i.view,t);return rn(o)||mt(a)?"step":a}else return io(i.view,t)}else{if(e.hasProjection||e.mark==="arc")return io(i.view,t);{const s=Fr(i.view,t);return mt(s)?s.step:s}}}function bo(e,t,n){return E(t,{suffix:`by_${E(e)}`,...n})}class Ri extends sp{constructor(t,n,i,r){super(t,"facet",n,i,r,t.resolve),this.child=Ha(t.spec,this,this.getName("child"),void 0,r),this.children=[this.child],this.facet=this.initFacet(t.facet)}initFacet(t){if(!Ki(t))return{facet:this.initFacetFieldDef(t,"facet")};const n=x(t),i={};for(const r of n){if(![Ft,kt].includes(r)){v(Gr(r,"facet"));break}const s=t[r];if(s.field===void 0){v(Js(s,r));break}i[r]=this.initFacetFieldDef(s,r)}return i}initFacetFieldDef(t,n){const i=la(t,n);return i.header?i.header=ke(i.header):i.header===null&&(i.header=null),i}channelHasField(t){return!!this.facet[t]}fieldDef(t){return this.facet[t]}parseData(){this.component.data=$s(this),this.child.parseData()}parseLayoutSize(){Ma(this)}parseSelections(){this.child.parseSelections(),this.component.selection=this.child.component.selection}parseMarkGroup(){this.child.parseMarkGroup()}parseAxesAndHeaders(){this.child.parseAxesAndHeaders(),kS(this)}assembleSelectionTopLevelSignals(t){return this.child.assembleSelectionTopLevelSignals(t)}assembleSignals(){return this.child.assembleSignals(),[]}assembleSelectionData(t){return this.child.assembleSelectionData(t)}getHeaderLayoutMixins(){const t={};for(const n of Me)for(const i of Aa){const r=this.component.layoutHeaders[n],s=r[i],{facetFieldDef:o}=r;if(o){const a=ui("titleOrient",o.header,this.config,n);if(["right","bottom"].includes(a)){const c=fs(n,a);t.titleAnchor??(t.titleAnchor={}),t.titleAnchor[c]="end"}}if(s!=null&&s[0]){const a=n==="row"?"height":"width",c=i==="header"?"headerBand":"footerBand";n!=="facet"&&!this.child.component.layoutSize.get(a)&&(t[c]??(t[c]={}),t[c][n]=.5),r.title&&(t.offset??(t.offset={}),t.offset[n==="row"?"rowTitle":"columnTitle"]=10)}}return t}assembleDefaultLayout(){const{column:t,row:n}=this.facet,i=t?this.columnDistinctSignal():n?1:void 0;let r="all";return(!n&&this.component.resolve.scale.x==="independent"||!t&&this.component.resolve.scale.y==="independent")&&(r="none"),{...this.getHeaderLayoutMixins(),...i?{columns:i}:{},bounds:"full",align:r}}assembleLayoutSignals(){return this.child.assembleLayoutSignals()}columnDistinctSignal(){if(!(this.parent&&this.parent instanceof Ri))return{signal:`length(data('${this.getName("column_domain")}'))`}}assembleGroupStyle(){}assembleGroup(t){return this.parent&&this.parent instanceof Ri?{...this.channelHasField("column")?{encode:{update:{columns:{field:E(this.facet.column,{prefix:"distinct"})}}}}:{},...super.assembleGroup(t)}:super.assembleGroup(t)}getCardinalityAggregateForChild(){const t=[],n=[],i=[];if(this.child instanceof Ri){if(this.child.channelHasField("column")){const r=E(this.child.facet.column);t.push(r),n.push("distinct"),i.push(`distinct_${r}`)}}else for(const r of St){const s=this.child.component.scales[r];if(s&&!s.merged){const o=s.get("type"),a=s.get("range");if(ye(o)&&rn(a)){const c=ps(this.child,r),l=za(c);l?(t.push(l),n.push("distinct"),i.push(`distinct_${l}`)):v(Do(r))}}}return{fields:t,ops:n,as:i}}assembleFacet(){const{name:t,data:n}=this.component.data.facetRoot,{row:i,column:r}=this.facet,{fields:s,ops:o,as:a}=this.getCardinalityAggregateForChild(),c=[];for(const u of Me){const f=this.facet[u];if(f){c.push(E(f));const{bin:d,sort:g}=f;if(te(d)&&c.push(E(f,{binSuffix:"end"})),ft(g)){const{field:p,op:h=Kr}=g,m=bo(f,g);i&&r?(s.push(m),o.push("max"),a.push(m)):(s.push(p),o.push(h),a.push(m))}else if(k(g)){const p=li(f,u);s.push(p),o.push("max"),a.push(p)}}}const l=!!i&&!!r;return{name:t,data:n,groupby:c,...l||s.length>0?{aggregate:{...l?{cross:l}:{},...s.length?{fields:s,ops:o,as:a}:{}}}:{}}}facetSortFields(t){const{facet:n}=this,i=n[t];return i?ft(i.sort)?[bo(i,i.sort,{expr:"datum"})]:k(i.sort)?[li(i,t,{expr:"datum"})]:[E(i,{expr:"datum"})]:[]}facetSortOrder(t){const{facet:n}=this,i=n[t];if(i){const{sort:r}=i;return[(ft(r)?r.order:!k(r)&&r)||"ascending"]}return[]}assembleLabelTitle(){var r;const{facet:t,config:n}=this;if(t.facet)return fo(t.facet,"facet",n);const i={row:["top","bottom"],column:["left","right"]};for(const s of Ta)if(t[s]){const o=ui("labelOrient",(r=t[s])==null?void 0:r.header,n,s);if(i[s].includes(o))return fo(t[s],s,n)}}assembleMarks(){const{child:t}=this,n=this.component.data.facetRoot,i=CS(n),r=t.assembleGroupEncodeEntry(!1),s=this.assembleLabelTitle()||t.assembleTitle(),o=t.assembleGroupStyle();return[{name:this.getName("cell"),type:"group",...s?{title:s}:{},...o?{style:o}:{},from:{facet:this.assembleFacet()},sort:{field:Me.map(c=>this.facetSortFields(c)).flat(),order:Me.map(c=>this.facetSortOrder(c)).flat()},...i.length>0?{data:i}:{},...r?{encode:{update:r}}:{},...t.assembleGroup(Gb(this,[]))}]}getMapping(){return this.facet}}function IS(e,t){const{row:n,column:i}=t;if(n&&i){let r=null;for(const s of[n,i])if(ft(s.sort)){const{field:o,op:a=Kr}=s.sort;e=r=new Dn(e,{joinaggregate:[{op:a,field:o,as:bo(s,s.sort,{forAs:!0})}],groupby:[E(s)]})}return r}return null}function cp(e,t){var n,i,r,s;for(const o of t){const a=o.data;if(e.name&&o.hasName()&&e.name!==o.dataName)continue;const c=(n=e.format)==null?void 0:n.mesh,l=(i=a.format)==null?void 0:i.feature;if(c&&l)continue;const u=(r=e.format)==null?void 0:r.feature;if((u||l)&&u!==l)continue;const f=(s=a.format)==null?void 0:s.mesh;if(!((c||f)&&c!==f)){if(Di(e)&&Di(a)){if(Pe(e.values,a.values))return o}else if(oi(e)&&oi(a)){if(e.url===a.url)return o}else if(Qf(e)&&e.name===o.dataName)return o}}return null}function LS(e,t){if(e.data||!e.parent){if(e.data===null){const i=new Tn({values:[]});return t.push(i),i}const n=cp(e.data,t);if(n)return Wt(e.data)||(n.data.format=ql({},e.data.format,n.data.format)),!n.hasName()&&e.data.name&&(n.dataName=e.data.name),n;{const i=new Tn(e.data);return t.push(i),i}}else return e.parent.component.data.facetRoot?e.parent.component.data.facetRoot:e.parent.component.data.main}function PS(e,t,n){let i=0;for(const r of t.transforms){let s,o;if(wb(r))o=e=new ci(e,r),s="derived";else if(Ea(r)){const a=Fv(r);o=e=$e.makeWithAncestors(e,{},a,n)??e,e=new yi(e,t,r.filter)}else if(qf(r))o=e=pt.makeFromTransform(e,r,t),s="number";else if(Nb(r))s="date",n.getWithExplicit(r.field).value===void 0&&(e=new $e(e,{[r.field]:s}),n.set(r.field,s,!1)),o=e=dt.makeFromTransform(e,r);else if(Fb(r))o=e=Qe.makeFromTransform(e,r),s="number",ka(t)&&(e=new Kt(e));else if(Hf(r))o=e=Ui.make(e,t,r,i++),s="derived";else if(Sb(r))o=e=new xi(e,r),s="number";else if(Eb(r))o=e=new Dn(e,r),s="number";else if(kb(r))o=e=At.makeFromTransform(e,r),s="derived";else if(Tb(r))o=e=new ys(e,r),s="derived";else if(Ab(r))o=e=new hs(e,r),s="derived";else if($b(r))o=e=new ms(e,r),s="derived";else if(hb(r))o=e=new Ss(e,r),s="derived";else if(vb(r))e=new Es(e,r);else if(Cb(r))o=e=xn.makeFromTransform(e,r),s="derived";else if(mb(r))o=e=new gs(e,r),s="derived";else if(yb(r))o=e=new xs(e,r),s="derived";else if(bb(r))o=e=new vs(e,r),s="derived";else if(xb(r))o=e=new bs(e,r),s="derived";else{v(Kg(r));continue}if(o&&s!==void 0)for(const a of o.producedFields()??[])n.set(a,s,!1)}return e}function $s(e){var h;let t=LS(e,e.component.data.sources);const{outputNodes:n,outputNodeRefCounts:i}=e.component.data,r=e.data,o=!(r&&(Wt(r)||oi(r)||Di(r)))&&e.parent?e.parent.component.data.ancestorParse.clone():new Bb;Wt(r)?(Jf(r)?t=new nr(t,r.sequence):$a(r)&&(t=new tr(t,r.graticule)),o.parseNothing=!0):((h=r==null?void 0:r.format)==null?void 0:h.parse)===null&&(o.parseNothing=!0),t=$e.makeExplicit(t,e,o)??t,t=new Kt(t);const a=e.parent&&vi(e.parent);(ae(e)||ze(e))&&a&&(t=pt.makeFromEncoding(t,e)??t),e.transforms.length>0&&(t=PS(t,e,o));const c=Tv(e),l=kv(e);t=$e.makeWithAncestors(t,{},{...c,...l},o)??t,ae(e)&&(t=Yn.parseAll(t,e),t=Mi.parseAll(t,e)),(ae(e)||ze(e))&&(a||(t=pt.makeFromEncoding(t,e)??t),t=dt.makeFromEncoding(t,e)??t,t=ci.parseAllForSortIndex(t,e));const u=e.getDataName(ee.Raw),f=new Ce(t,u,ee.Raw,i);if(n[u]=f,t=f,ae(e)){const m=Qe.makeFromEncoding(t,e);m&&(t=m,ka(e)&&(t=new Kt(t))),t=xn.makeFromEncoding(t,e)??t,t=At.makeFromEncoding(t,e)??t}ae(e)&&(t=ji.make(t,e)??t);const d=e.getDataName(ee.Main),g=new Ce(t,d,ee.Main,i);n[d]=g,t=g,ae(e)&&wx(e,g);let p=null;if(ze(e)){const m=e.getName("facet");t=IS(t,e.facet)??t,p=new bi(t,e,m,g.getSource()),n[m]=p}return{...e.component.data,outputNodes:n,outputNodeRefCounts:i,raw:f,main:g,facetRoot:p,ancestorParse:o}}class zS extends ja{constructor(t,n,i,r){var s,o,a,c;super(t,"concat",n,i,r,t.resolve),(((o=(s=t.resolve)==null?void 0:s.axis)==null?void 0:o.x)==="shared"||((c=(a=t.resolve)==null?void 0:a.axis)==null?void 0:c.y)==="shared")&&v(Vg),this.children=this.getChildren(t).map((l,u)=>Ha(l,this,this.getName(`concat_${u}`),void 0,r))}parseData(){this.component.data=$s(this);for(const t of this.children)t.parseData()}parseSelections(){this.component.selection={};for(const t of this.children){t.parseSelections();for(const n of x(t.component.selection))this.component.selection[n]=t.component.selection[n]}}parseMarkGroup(){for(const t of this.children)t.parseMarkGroup()}parseAxesAndHeaders(){for(const t of this.children)t.parseAxesAndHeaders()}getChildren(t){return rs(t)?t.vconcat:va(t)?t.hconcat:t.concat}parseLayoutSize(){OS(this)}parseAxisGroup(){return null}assembleSelectionTopLevelSignals(t){return this.children.reduce((n,i)=>i.assembleSelectionTopLevelSignals(n),t)}assembleSignals(){return this.children.forEach(t=>t.assembleSignals()),[]}assembleLayoutSignals(){const t=Oa(this);for(const n of this.children)t.push(...n.assembleLayoutSignals());return t}assembleSelectionData(t){return this.children.reduce((n,i)=>i.assembleSelectionData(n),t)}assembleMarks(){return this.children.map(t=>{const n=t.assembleTitle(),i=t.assembleGroupStyle(),r=t.assembleGroupEncodeEntry(!1);return{type:"group",name:t.getName("group"),...n?{title:n}:{},...i?{style:i}:{},...r?{encode:{update:r}}:{},...t.assembleGroup()}})}assembleGroupStyle(){}assembleDefaultLayout(){const t=this.layout.columns;return{...t!=null?{columns:t}:{},bounds:"full",align:"each"}}}function DS(e){return e===!1||e===null}const jS={disable:1,gridScale:1,scale:1,...mf,labelExpr:1,encode:1},lp=x(jS);class Ua extends jt{constructor(t={},n={},i=!1){super(),this.explicit=t,this.implicit=n,this.mainExtracted=i}clone(){return new Ua(P(this.explicit),P(this.implicit),this.mainExtracted)}hasAxisPart(t){return t==="axis"?!0:t==="grid"||t==="title"?!!this.get(t):!DS(this.get(t))}hasOrientSignalRef(){return T(this.explicit.orient)}}function MS(e,t,n){const{encoding:i,config:r}=e,s=fe(i[t])??fe(i[vt(t)]),o=e.axis(t)||{},{format:a,formatType:c}=o;if(Nn(c))return{text:Ke({fieldOrDatumDef:s,field:"datum.value",format:a,formatType:c,config:r}),...n};if(a===void 0&&c===void 0&&r.customFormatTypes){if(ii(s)==="quantitative"){if(ri(s)&&s.stack==="normalize"&&r.normalizedNumberFormatType)return{text:Ke({fieldOrDatumDef:s,field:"datum.value",format:r.normalizedNumberFormat,formatType:r.normalizedNumberFormatType,config:r}),...n};if(r.numberFormatType)return{text:Ke({fieldOrDatumDef:s,field:"datum.value",format:r.numberFormat,formatType:r.numberFormatType,config:r}),...n}}if(ii(s)==="temporal"&&r.timeFormatType&&S(s)&&!s.timeUnit)return{text:Ke({fieldOrDatumDef:s,field:"datum.value",format:r.timeFormat,formatType:r.timeFormatType,config:r}),...n}}return n}function US(e){return St.reduce((t,n)=>(e.component.scales[n]&&(t[n]=[XS(n,e)]),t),{})}const BS={bottom:"top",top:"bottom",left:"right",right:"left"};function WS(e){const{axes:t,resolve:n}=e.component,i={top:0,bottom:0,right:0,left:0};for(const r of e.children){r.parseAxesAndHeaders();for(const s of x(r.component.axes))n.axis[s]=_a(e.component.resolve,s),n.axis[s]==="shared"&&(t[s]=GS(t[s],r.component.axes[s]),t[s]||(n.axis[s]="independent",delete t[s]))}for(const r of St){for(const s of e.children)if(s.component.axes[r]){if(n.axis[r]==="independent"){t[r]=(t[r]??[]).concat(s.component.axes[r]);for(const o of s.component.axes[r]){const{value:a,explicit:c}=o.getWithExplicit("orient");if(!T(a)){if(i[a]>0&&!c){const l=BS[a];i[a]>i[l]&&o.set("orient",l,!1)}i[a]++}}}delete s.component.axes[r]}if(n.axis[r]==="independent"&&t[r]&&t[r].length>1)for(const[s,o]of(t[r]||[]).entries())s>0&&o.get("grid")&&!o.explicit.grid&&(o.implicit.grid=!1)}}function GS(e,t){if(e){if(e.length!==t.length)return;const n=e.length;for(let i=0;i<n;i++){const r=e[i],s=t[i];if(!!r!=!!s)return;if(r&&s){const o=r.getWithExplicit("orient"),a=s.getWithExplicit("orient");if(o.explicit&&a.explicit&&o.value!==a.value)return;e[i]=HS(r,s)}}}else return t.map(n=>n.clone());return e}function HS(e,t){for(const n of lp){const i=Vt(e.getWithExplicit(n),t.getWithExplicit(n),n,"axis",(r,s)=>{switch(n){case"title":return vu(r,s);case"gridScale":return{explicit:r.explicit,value:le(r.value,s.value)}}return os(r,s,n,"axis")});e.setWithExplicit(n,i)}return e}function qS(e,t,n,i,r){if(t==="disable")return n!==void 0;switch(n=n||{},t){case"titleAngle":case"labelAngle":return e===(T(n.labelAngle)?n.labelAngle:Pi(n.labelAngle));case"values":return!!n.values;case"encode":return!!n.encoding||!!n.labelAngle;case"title":if(e===Fd(i,r))return!0}return e===n[t]}const VS=new Set(["grid","translate","format","formatType","orient","labelExpr","tickCount","position","tickMinStep"]);function XS(e,t){var y,b;let n=t.axis(e);const i=new Ua,r=fe(t.encoding[e]),{mark:s,config:o}=t,a=(n==null?void 0:n.orient)||((y=o[e==="x"?"axisX":"axisY"])==null?void 0:y.orient)||((b=o.axis)==null?void 0:b.orient)||Ix(e),c=t.getScaleComponent(e).get("type"),l=Fx(e,c,a,t.config),u=n!==void 0?!n:lo("disable",o.style,n==null?void 0:n.style,l).configValue;if(i.set("disable",u,n!==void 0),u)return i;n=n||{};const f=Ox(r,n,e,o.style,l),d=ef(n.formatType,r,c),g=Zu(r,r.type,n.format,n.formatType,o,!0),p={fieldOrDatumDef:r,axis:n,channel:e,model:t,scaleType:c,orient:a,labelAngle:f,format:g,formatType:d,mark:s,config:o};for(const C of lp){const D=C in Yc?Yc[C](p):Ec(C)?n[C]:void 0,w=D!==void 0,F=qS(D,C,n,t,e);if(w&&F)i.set(C,D,F);else{const{configValue:A=void 0,configFrom:M=void 0}=Ec(C)&&C!=="values"?lo(C,o.style,n.style,l):{},K=A!==void 0;w&&!K?i.set(C,D,F):(M!=="vgAxisConfig"||VS.has(C)&&K||Zi(A)||T(A))&&i.set(C,A,!1)}}const h=n.encoding??{},m=hf.reduce((C,D)=>{if(!i.hasAxisPart(D))return C;const w=Rd(h[D]??{},t),F=D==="labels"?MS(t,e,w):w;return F!==void 0&&!Q(F)&&(C[D]={update:F}),C},{});return Q(m)||i.set("encode",m,!!n.encoding||n.labelAngle!==void 0),i}function YS({encoding:e,size:t}){for(const n of St){const i=Re(n);mt(t[i])&&Bt(e[n])&&(delete t[i],v(Cu(i)))}return t}const KS={vgMark:"arc",encodeEntry:e=>({...Ge(e,{align:"ignore",baseline:"ignore",color:"include",size:"ignore",orient:"ignore",theta:"ignore"}),...Ee("x",e,{defaultPos:"mid"}),...Ee("y",e,{defaultPos:"mid"}),...Xt(e,"radius"),...Xt(e,"theta")})},QS={vgMark:"area",encodeEntry:e=>({...Ge(e,{align:"ignore",baseline:"ignore",color:"include",orient:"include",size:"ignore",theta:"ignore"}),...kr("x",e,{defaultPos:"zeroOrMin",defaultPos2:"zeroOrMin",range:e.markDef.orient==="horizontal"}),...kr("y",e,{defaultPos:"zeroOrMin",defaultPos2:"zeroOrMin",range:e.markDef.orient==="vertical"}),...Fa(e)})},JS={vgMark:"rect",encodeEntry:e=>({...Ge(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"}),...Xt(e,"x"),...Xt(e,"y")})},ZS={vgMark:"shape",encodeEntry:e=>({...Ge(e,{align:"ignore",baseline:"ignore",color:"include",size:"ignore",orient:"ignore",theta:"ignore"})}),postEncodingTransform:e=>{const{encoding:t}=e,n=t.shape;return[{type:"geoshape",projection:e.projectionName(),...n&&S(n)&&n.type===hi?{field:E(n,{expr:"datum"})}:{}}]}},eE={vgMark:"image",encodeEntry:e=>({...Ge(e,{align:"ignore",baseline:"ignore",color:"ignore",orient:"ignore",size:"ignore",theta:"ignore"}),...Xt(e,"x"),...Xt(e,"y"),...Ca(e,"url")})},tE={vgMark:"line",encodeEntry:e=>({...Ge(e,{align:"ignore",baseline:"ignore",color:"include",size:"ignore",orient:"ignore",theta:"ignore"}),...Ee("x",e,{defaultPos:"mid"}),...Ee("y",e,{defaultPos:"mid"}),...he("size",e,{vgChannel:"strokeWidth"}),...Fa(e)})},nE={vgMark:"trail",encodeEntry:e=>({...Ge(e,{align:"ignore",baseline:"ignore",color:"include",size:"include",orient:"ignore",theta:"ignore"}),...Ee("x",e,{defaultPos:"mid"}),...Ee("y",e,{defaultPos:"mid"}),...he("size",e),...Fa(e)})};function Ba(e,t){const{config:n}=e;return{...Ge(e,{align:"ignore",baseline:"ignore",color:"include",size:"include",orient:"ignore",theta:"ignore"}),...Ee("x",e,{defaultPos:"mid"}),...Ee("y",e,{defaultPos:"mid"}),...he("size",e),...he("angle",e),...iE(e,n,t)}}function iE(e,t,n){return n?{shape:{value:n}}:he("shape",e)}const rE={vgMark:"symbol",encodeEntry:e=>Ba(e)},sE={vgMark:"symbol",encodeEntry:e=>Ba(e,"circle")},oE={vgMark:"symbol",encodeEntry:e=>Ba(e,"square")},aE={vgMark:"rect",encodeEntry:e=>({...Ge(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"}),...Xt(e,"x"),...Xt(e,"y")})},cE={vgMark:"rule",encodeEntry:e=>{const{markDef:t}=e,n=t.orient;return!e.encoding.x&&!e.encoding.y&&!e.encoding.latitude&&!e.encoding.longitude?{}:{...Ge(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"}),...kr("x",e,{defaultPos:n==="horizontal"?"zeroOrMax":"mid",defaultPos2:"zeroOrMin",range:n!=="vertical"}),...kr("y",e,{defaultPos:n==="vertical"?"zeroOrMax":"mid",defaultPos2:"zeroOrMin",range:n!=="horizontal"}),...he("size",e,{vgChannel:"strokeWidth"})}}},lE={vgMark:"text",encodeEntry:e=>{const{config:t,encoding:n}=e;return{...Ge(e,{align:"include",baseline:"include",color:"include",size:"ignore",orient:"ignore",theta:"include"}),...Ee("x",e,{defaultPos:"mid"}),...Ee("y",e,{defaultPos:"mid"}),...Ca(e),...he("size",e,{vgChannel:"fontSize"}),...he("angle",e),...Hc("align",uE(e.markDef,n,t)),...Hc("baseline",fE(e.markDef,n,t)),...Ee("radius",e,{defaultPos:null}),...Ee("theta",e,{defaultPos:null})}}};function uE(e,t,n){if(V("align",e,n)===void 0)return"center"}function fE(e,t,n){if(V("baseline",e,n)===void 0)return"middle"}const dE={vgMark:"rect",encodeEntry:e=>{const{config:t,markDef:n}=e,i=n.orient,r=i==="horizontal"?"width":"height",s=i==="horizontal"?"height":"width";return{...Ge(e,{align:"ignore",baseline:"ignore",color:"include",orient:"ignore",size:"ignore",theta:"ignore"}),...Ee("x",e,{defaultPos:"mid",vgChannel:"xc"}),...Ee("y",e,{defaultPos:"mid",vgChannel:"yc"}),...he("size",e,{defaultValue:pE(e),vgChannel:r}),[s]:ie(V("thickness",n,t))}}};function pE(e){const{config:t,markDef:n}=e,{orient:i}=n,r=i==="horizontal"?"width":"height",s=e.getScaleComponent(i==="horizontal"?"x":"y"),o=V("size",n,t,{vgChannel:r})??t.tick.bandSize;if(o!==void 0)return o;{const a=s?s.get("range"):void 0;return a&&rn(a)&&re(a.step)?a.step*3/4:Nr(t.view,r)*3/4}}const cr={arc:KS,area:QS,bar:JS,circle:sE,geoshape:ZS,image:eE,line:tE,point:rE,rect:aE,rule:cE,square:oE,text:lE,tick:dE,trail:nE};function gE(e){if(G([Xr,qr,Em],e.mark)){const t=Sf(e.mark,e.encoding);if(t.length>0)return hE(e,t)}else if(e.mark===Vr){const t=Qs.some(n=>V(n,e.markDef,e.config));if(e.stack&&!e.fieldDef("size")&&t)return mE(e)}return Wa(e)}const fl="faceted_path_";function hE(e,t){return[{name:e.getName("pathgroup"),type:"group",from:{facet:{name:fl+e.requestDataName(ee.Main),data:e.requestDataName(ee.Main),groupby:t}},encode:{update:{width:{field:{group:"width"}},height:{field:{group:"height"}}}},marks:Wa(e,{fromPrefix:fl})}]}const dl="stack_group_";function mE(e){var l;const[t]=Wa(e,{fromPrefix:dl}),n=e.scaleName(e.stack.fieldChannel),i=(u={})=>e.vgField(e.stack.fieldChannel,u),r=(u,f)=>{const d=[i({prefix:"min",suffix:"start",expr:f}),i({prefix:"max",suffix:"start",expr:f}),i({prefix:"min",suffix:"end",expr:f}),i({prefix:"max",suffix:"end",expr:f})];return`${u}(${d.map(g=>`scale('${n}',${g})`).join(",")})`};let s,o;e.stack.fieldChannel==="x"?(s={...Qn(t.encode.update,["y","yc","y2","height",...Qs]),x:{signal:r("min","datum")},x2:{signal:r("max","datum")},clip:{value:!0}},o={x:{field:{group:"x"},mult:-1},height:{field:{group:"height"}}},t.encode.update={...Te(t.encode.update,["y","yc","y2"]),height:{field:{group:"height"}}}):(s={...Qn(t.encode.update,["x","xc","x2","width"]),y:{signal:r("min","datum")},y2:{signal:r("max","datum")},clip:{value:!0}},o={y:{field:{group:"y"},mult:-1},width:{field:{group:"width"}}},t.encode.update={...Te(t.encode.update,["x","xc","x2"]),width:{field:{group:"width"}}});for(const u of Qs){const f=_t(u,e.markDef,e.config);t.encode.update[u]?(s[u]=t.encode.update[u],delete t.encode.update[u]):f&&(s[u]=ie(f)),f&&(t.encode.update[u]={value:0})}const a=[];if(((l=e.stack.groupbyChannels)==null?void 0:l.length)>0)for(const u of e.stack.groupbyChannels){const f=e.fieldDef(u),d=E(f);d&&a.push(d),(f!=null&&f.bin||f!=null&&f.timeUnit)&&a.push(E(f,{binSuffix:"end"}))}return s=["stroke","strokeWidth","strokeJoin","strokeCap","strokeDash","strokeDashOffset","strokeMiterLimit","strokeOpacity"].reduce((u,f)=>{if(t.encode.update[f])return{...u,[f]:t.encode.update[f]};{const d=_t(f,e.markDef,e.config);return d!==void 0?{...u,[f]:ie(d)}:u}},s),s.stroke&&(s.strokeForeground={value:!0},s.strokeOffset={value:0}),[{type:"group",from:{facet:{data:e.requestDataName(ee.Main),name:dl+e.requestDataName(ee.Main),groupby:a,aggregate:{fields:[i({suffix:"start"}),i({suffix:"start"}),i({suffix:"end"}),i({suffix:"end"})],ops:["min","max","min","max"]}}},encode:{update:s},marks:[{type:"group",encode:{update:o},marks:[t]}]}]}function yE(e){var a;const{encoding:t,stack:n,mark:i,markDef:r,config:s}=e,o=t.order;if(!(!k(o)&&Je(o)&&Ys(o.value)||!o&&Ys(V("order",r,s)))){if((k(o)||S(o))&&!n)return yu(o,{expr:"datum"});if(sn(i)){const c=r.orient==="horizontal"?"y":"x",l=t[c];if(S(l)){const u=l.sort;if(k(u))return{field:E(l,{prefix:c,suffix:"sort_index",expr:"datum"})};if(ft(u))return{field:E({aggregate:ua(e.encoding)?u.op:void 0,field:u.field},{expr:"datum"})};if(nf(u)){const f=e.fieldDef(u.encoding);return{field:E(f,{expr:"datum"}),order:u.order}}else return u===null?void 0:{field:E(l,{binSuffix:(a=e.stack)!=null&&a.impute?"mid":void 0,expr:"datum"})}}return}}}function Wa(e,t={fromPrefix:""}){const{mark:n,markDef:i,encoding:r,config:s}=e,o=le(i.clip,bE(e),xE(e)),a=hu(i),c=r.key,l=yE(e),u=vE(e),f=V("aria",i,s),d=cr[n].postEncodingTransform?cr[n].postEncodingTransform(e):null;return[{name:e.getName("marks"),type:cr[n].vgMark,...o?{clip:o}:{},...a?{style:a}:{},...c?{key:c.field}:{},...l?{sort:l}:{},...u||{},...f===!1?{aria:f}:{},from:{data:t.fromPrefix+e.requestDataName(ee.Main)},encode:{update:cr[n].encodeEntry(e)},...d?{transform:d}:{}}]}function bE(e){const t=e.getScaleComponent("x"),n=e.getScaleComponent("y");return t!=null&&t.get("selectionExtent")||n!=null&&n.get("selectionExtent")?!0:void 0}function xE(e){const t=e.component.projection;return t&&!t.isFit?!0:void 0}function vE(e){if(!e.component.selection)return null;const t=x(e.component.selection).length;let n=t,i=e.parent;for(;i&&n===0;)n=x(i.component.selection).length,i=i.parent;return n?{interactive:t>0||e.mark==="geoshape"||!!e.encoding.tooltip||!!e.markDef.tooltip}:null}class up extends sp{constructor(t,n,i,r={},s){super(t,"unit",n,i,s,void 0,wc(t)?t.view:void 0),this.specifiedScales={},this.specifiedAxes={},this.specifiedLegends={},this.specifiedProjection={},this.selection=[],this.children=[];const o=gt(t.mark)?{...t.mark}:{type:t.mark},a=o.type;o.filled===void 0&&(o.filled=rb(o,s,{graticule:t.data&&$a(t.data)}));const c=this.encoding=cy(t.encoding||{},a,o.filled,s);this.markDef=Bf(o,c,s),this.size=YS({encoding:c,size:wc(t)?{...r,...t.width?{width:t.width}:{},...t.height?{height:t.height}:{}}:r}),this.stack=Uf(this.markDef,c),this.specifiedScales=this.initScales(a,c),this.specifiedAxes=this.initAxes(c),this.specifiedLegends=this.initLegends(c),this.specifiedProjection=t.projection,this.selection=(t.params??[]).filter(l=>ba(l))}get hasProjection(){const{encoding:t}=this,n=this.mark===Xu,i=t&&og.some(r=>z(t[r]));return n||i}scaleDomain(t){const n=this.specifiedScales[t];return n?n.domain:void 0}axis(t){return this.specifiedAxes[t]}legend(t){return this.specifiedLegends[t]}initScales(t,n){return Br.reduce((i,r)=>{const s=fe(n[r]);return s&&(i[r]=this.initScale(s.scale??{})),i},{})}initScale(t){const{domain:n,range:i}=t,r=ke(t);return k(n)&&(r.domain=n.map(Le)),k(i)&&(r.range=i.map(Le)),r}initAxes(t){return St.reduce((n,i)=>{const r=t[i];if(z(r)||i===oe&&z(t.x2)||i===be&&z(t.y2)){const s=z(r)?r.axis:void 0;n[i]=s&&this.initAxis({...s})}return n},{})}initAxis(t){const n=x(t),i={};for(const r of n){const s=t[r];i[r]=Zi(s)?gu(s):Le(s)}return i}initLegends(t){return mg.reduce((n,i)=>{const r=fe(t[i]);if(r&&bg(i)){const s=r.legend;n[i]=s&&ke(s)}return n},{})}parseData(){this.component.data=$s(this)}parseLayoutSize(){_S(this)}parseSelections(){this.component.selection=$x(this,this.selection)}parseMarkGroup(){this.component.mark=gE(this)}parseAxesAndHeaders(){this.component.axes=US(this)}assembleSelectionTopLevelSignals(t){return Hb(this,t)}assembleSignals(){return[...wd(this),...Wb(this,[])]}assembleSelectionData(t){return qb(this,t)}assembleLayout(){return null}assembleLayoutSignals(){return Oa(this)}assembleMarks(){let t=this.component.mark??[];return(!this.parent||!vi(this.parent))&&(t=td(this,t)),t.map(this.correctDataNames)}assembleGroupStyle(){const{style:t}=this.view||{};return t!==void 0?t:this.encoding.x||this.encoding.y?"cell":"view"}getMapping(){return this.encoding}get mark(){return this.markDef.type}channelHasField(t){return yn(this.encoding,t)}fieldDef(t){const n=this.encoding[t];return ht(n)}typedFieldDef(t){const n=this.fieldDef(t);return Ne(n)?n:null}}class Ga extends ja{constructor(t,n,i,r,s){super(t,"layer",n,i,s,t.resolve,t.view);const o={...r,...t.width?{width:t.width}:{},...t.height?{height:t.height}:{}};this.children=t.layer.map((a,c)=>{if(ss(a))return new Ga(a,this,this.getName(`layer_${c}`),o,s);if(Dt(a))return new up(a,this,this.getName(`layer_${c}`),o,s);throw new Error(zo(a))})}parseData(){this.component.data=$s(this);for(const t of this.children)t.parseData()}parseLayoutSize(){AS(this)}parseSelections(){this.component.selection={};for(const t of this.children){t.parseSelections();for(const n of x(t.component.selection))this.component.selection[n]=t.component.selection[n]}}parseMarkGroup(){for(const t of this.children)t.parseMarkGroup()}parseAxesAndHeaders(){WS(this)}assembleSelectionTopLevelSignals(t){return this.children.reduce((n,i)=>i.assembleSelectionTopLevelSignals(n),t)}assembleSignals(){return this.children.reduce((t,n)=>t.concat(n.assembleSignals()),wd(this))}assembleLayoutSignals(){return this.children.reduce((t,n)=>t.concat(n.assembleLayoutSignals()),Oa(this))}assembleSelectionData(t){return this.children.reduce((n,i)=>i.assembleSelectionData(n),t)}assembleGroupStyle(){const t=new Set;for(const i of this.children)for(const r of ce(i.assembleGroupStyle()))t.add(r);const n=Array.from(t);return n.length>1?n:n.length===1?n[0]:void 0}assembleTitle(){let t=super.assembleTitle();if(t)return t;for(const n of this.children)if(t=n.assembleTitle(),t)return t}assembleLayout(){return null}assembleMarks(){return Vb(this,this.children.flatMap(t=>t.assembleMarks()))}assembleLegends(){return this.children.reduce((t,n)=>t.concat(n.assembleLegends()),Ud(this))}}function Ha(e,t,n,i,r){if(Qr(e))return new Ri(e,t,n,r);if(ss(e))return new Ga(e,t,n,i,r);if(Dt(e))return new up(e,t,n,i,r);if(_y(e))return new zS(e,t,n,r);throw new Error(zo(e))}function SE(e,t={}){t.logger&&zh(t.logger),t.fieldTitle&&df(t.fieldTitle);try{const n=Mf(Ir(t.config,e.config)),i=Yf(e,n),r=Ha(i,null,"",void 0,n);return r.parse(),Hv(r.component.data,r),{spec:$E(r,EE(e,i.autosize,n,r),e.datasets,e.usermeta),normalized:i}}finally{t.logger&&Dh(),t.fieldTitle&&Zm()}}function EE(e,t,n,i){const r=i.component.layoutSize.get("width"),s=i.component.layoutSize.get("height");if(t===void 0?(t={type:"pad"},i.hasAxisOrientSignalRef()&&(t.resize=!0)):I(t)&&(t={type:t}),r&&s&&jb(t.type)){if(r==="step"&&s==="step")v(cc()),t.type="pad";else if(r==="step"||s==="step"){const o=r==="step"?"width":"height";v(cc(Ur(o)));const a=o==="width"?"height":"width";t.type=Mb(a)}}return{...x(t).length===1&&t.type?t.type==="pad"?{}:{autosize:t.type}:{autosize:t},...Pc(n,!1),...Pc(e,!0)}}function $E(e,t,n={},i){const r=e.config?qy(e.config):void 0,s=[].concat(e.assembleSelectionData([]),NS(e.component.data,n)),o=e.assembleProjections(),a=e.assembleTitle(),c=e.assembleGroupStyle(),l=e.assembleGroupEncodeEntry(!0);let u=e.assembleLayoutSignals();u=u.filter(g=>(g.name==="width"||g.name==="height")&&g.value!==void 0?(t[g.name]=+g.value,!1):!0);const{params:f,...d}=t;return{$schema:"https://vega.github.io/schema/vega/v5.json",...e.description?{description:e.description}:{},...d,...a?{title:a}:{},...c?{style:c}:{},...l?{encode:{update:l}}:{},data:s,...o.length>0?{projections:o}:{},...e.assembleGroup([...u,...e.assembleSelectionTopLevelSignals([]),...Pf(f)]),...r?{config:r}:{},...i?{usermeta:i}:{}}}const wE=eg.version,CE=Object.freeze(Object.defineProperty({__proto__:null,accessPathDepth:Jn,accessPathWithDatum:Fo,compile:SE,contains:G,deepEqual:Pe,deleteNestedProperty:mr,duplicate:P,entries:Gt,every:wo,fieldIntersection:No,flatAccessWithDatum:Xl,getFirstDefined:le,hasIntersection:Co,hash:W,internalField:Ql,isBoolean:Li,isEmpty:Q,isEqual:ng,isInternalField:Jl,isNullOrFalse:Ys,isNumeric:Lr,keys:x,logicalExpr:Oi,mergeDeep:ql,never:Hl,normalize:Yf,normalizeAngle:Pi,omit:Te,pick:Qn,prefixGenerator:Ks,removePathFromField:ko,replaceAll:Sn,replacePathInField:Ue,resetIdCounter:rg,setEqual:Vl,some:vn,stringify:J,titleCase:Wi,unique:ut,uniqueId:Kl,vals:ve,varName:se,version:wE},Symbol.toStringTag,{value:"Module"}));var NE="vega-themes",FE="2.14.0",kE="Themes for stylized Vega and Vega-Lite visualizations.",TE=["vega","vega-lite","themes","style"],AE="BSD-3-Clause",OE={name:"UW Interactive Data Lab",url:"https://idl.cs.washington.edu"},_E=[{name:"Emily Gu",url:"https://github.com/emilygu"},{name:"Arvind Satyanarayan",url:"http://arvindsatya.com"},{name:"Jeffrey Heer",url:"https://idl.cs.washington.edu"},{name:"Dominik Moritz",url:"https://www.domoritz.de"}],RE="build/vega-themes.js",IE="build/vega-themes.module.js",LE="build/vega-themes.min.js",PE="build/vega-themes.min.js",zE="build/vega-themes.module.d.ts",DE={type:"git",url:"https://github.com/vega/vega-themes.git"},jE=["src","build"],ME={prebuild:"yarn clean",build:"rollup -c",clean:"rimraf build && rimraf examples/build","copy:data":"rsync -r node_modules/vega-datasets/data/* examples/data","copy:build":"rsync -r build/* examples/build","deploy:gh":"yarn build && mkdir -p examples/build && rsync -r build/* examples/build && gh-pages -d examples",preversion:"yarn lint",serve:"browser-sync start -s -f build examples --serveStatic examples",start:"yarn build && concurrently --kill-others -n Server,Rollup 'yarn serve' 'rollup -c -w'",format:"eslint . --fix",lint:"eslint .",release:"release-it"},UE={"@babel/core":"^7.22.9","@babel/plugin-proposal-async-generator-functions":"^7.20.7","@babel/plugin-proposal-json-strings":"^7.18.6","@babel/plugin-proposal-object-rest-spread":"^7.20.7","@babel/plugin-proposal-optional-catch-binding":"^7.18.6","@babel/plugin-transform-runtime":"^7.22.9","@babel/preset-env":"^7.22.9","@babel/preset-typescript":"^7.22.5","@release-it/conventional-changelog":"^7.0.0","@rollup/plugin-json":"^6.0.0","@rollup/plugin-node-resolve":"^15.1.0","@rollup/plugin-terser":"^0.4.3","@typescript-eslint/eslint-plugin":"^6.0.0","@typescript-eslint/parser":"^6.0.0","browser-sync":"^2.29.3",concurrently:"^8.2.0",eslint:"^8.45.0","eslint-config-prettier":"^8.8.0","eslint-plugin-prettier":"^5.0.0","gh-pages":"^5.0.0",prettier:"^3.0.0","release-it":"^16.1.0",rollup:"^3.26.2","rollup-plugin-bundle-size":"^1.0.3","rollup-plugin-ts":"^3.2.0",typescript:"^5.1.6",vega:"^5.25.0","vega-lite":"^5.9.3"},BE={vega:"*","vega-lite":"*"},WE={},GE={name:NE,version:FE,description:kE,keywords:TE,license:AE,author:OE,contributors:_E,main:RE,module:IE,unpkg:LE,jsdelivr:PE,types:zE,repository:DE,files:jE,scripts:ME,devDependencies:UE,peerDependencies:BE,dependencies:WE};const Un="#fff",pl="#888",HE={background:"#333",view:{stroke:pl},title:{color:Un,subtitleColor:Un},style:{"guide-label":{fill:Un},"guide-title":{fill:Un}},axis:{domainColor:Un,gridColor:pl,tickColor:Un}},an="#4572a7",qE={background:"#fff",arc:{fill:an},area:{fill:an},line:{stroke:an,strokeWidth:2},path:{stroke:an},rect:{fill:an},shape:{stroke:an},symbol:{fill:an,strokeWidth:1.5,size:50},axis:{bandPosition:.5,grid:!0,gridColor:"#000000",gridOpacity:1,gridWidth:.5,labelPadding:10,tickSize:5,tickWidth:.5},axisBand:{grid:!1,tickExtra:!0},legend:{labelBaseline:"middle",labelFontSize:11,symbolSize:50,symbolType:"square"},range:{category:["#4572a7","#aa4643","#8aa453","#71598e","#4598ae","#d98445","#94aace","#d09393","#b9cc98","#a99cbc"]}},cn="#30a2da",js="#cbcbcb",VE="#999",XE="#333",gl="#f0f0f0",hl="#333",YE={arc:{fill:cn},area:{fill:cn},axis:{domainColor:js,grid:!0,gridColor:js,gridWidth:1,labelColor:VE,labelFontSize:10,titleColor:XE,tickColor:js,tickSize:10,titleFontSize:14,titlePadding:10,labelPadding:4},axisBand:{grid:!1},background:gl,group:{fill:gl},legend:{labelColor:hl,labelFontSize:11,padding:1,symbolSize:30,symbolType:"square",titleColor:hl,titleFontSize:14,titlePadding:10},line:{stroke:cn,strokeWidth:2},path:{stroke:cn,strokeWidth:.5},rect:{fill:cn},range:{category:["#30a2da","#fc4f30","#e5ae38","#6d904f","#8b8b8b","#b96db8","#ff9e27","#56cc60","#52d2ca","#52689e","#545454","#9fe4f8"],diverging:["#cc0020","#e77866","#f6e7e1","#d6e8ed","#91bfd9","#1d78b5"],heatmap:["#d6e8ed","#cee0e5","#91bfd9","#549cc6","#1d78b5"]},point:{filled:!0,shape:"circle"},shape:{stroke:cn},bar:{binSpacing:2,fill:cn,stroke:null},title:{anchor:"start",fontSize:24,fontWeight:600,offset:20}},ln="#000",KE={group:{fill:"#e5e5e5"},arc:{fill:ln},area:{fill:ln},line:{stroke:ln},path:{stroke:ln},rect:{fill:ln},shape:{stroke:ln},symbol:{fill:ln,size:40},axis:{domain:!1,grid:!0,gridColor:"#FFFFFF",gridOpacity:1,labelColor:"#7F7F7F",labelPadding:4,tickColor:"#7F7F7F",tickSize:5.67,titleFontSize:16,titleFontWeight:"normal"},legend:{labelBaseline:"middle",labelFontSize:11,symbolSize:40},range:{category:["#000000","#7F7F7F","#1A1A1A","#999999","#333333","#B0B0B0","#4D4D4D","#C9C9C9","#666666","#DCDCDC"]}},QE=22,JE="normal",ml="Benton Gothic, sans-serif",yl=11.5,ZE="normal",un="#82c6df",Ms="Benton Gothic Bold, sans-serif",bl="normal",xl=13,$i={"category-6":["#ec8431","#829eb1","#c89d29","#3580b1","#adc839","#ab7fb4"],"fire-7":["#fbf2c7","#f9e39c","#f8d36e","#f4bb6a","#e68a4f","#d15a40","#ab4232"],"fireandice-6":["#e68a4f","#f4bb6a","#f9e39c","#dadfe2","#a6b7c6","#849eae"],"ice-7":["#edefee","#dadfe2","#c4ccd2","#a6b7c6","#849eae","#607785","#47525d"]},e$={background:"#ffffff",title:{anchor:"start",color:"#000000",font:Ms,fontSize:QE,fontWeight:JE},arc:{fill:un},area:{fill:un},line:{stroke:un,strokeWidth:2},path:{stroke:un},rect:{fill:un},shape:{stroke:un},symbol:{fill:un,size:30},axis:{labelFont:ml,labelFontSize:yl,labelFontWeight:ZE,titleFont:Ms,titleFontSize:xl,titleFontWeight:bl},axisX:{labelAngle:0,labelPadding:4,tickSize:3},axisY:{labelBaseline:"middle",maxExtent:45,minExtent:45,tickSize:2,titleAlign:"left",titleAngle:0,titleX:-45,titleY:-11},legend:{labelFont:ml,labelFontSize:yl,symbolType:"square",titleFont:Ms,titleFontSize:xl,titleFontWeight:bl},range:{category:$i["category-6"],diverging:$i["fireandice-6"],heatmap:$i["fire-7"],ordinal:$i["fire-7"],ramp:$i["fire-7"]}},fn="#ab5787",lr="#979797",t$={background:"#f9f9f9",arc:{fill:fn},area:{fill:fn},line:{stroke:fn},path:{stroke:fn},rect:{fill:fn},shape:{stroke:fn},symbol:{fill:fn,size:30},axis:{domainColor:lr,domainWidth:.5,gridWidth:.2,labelColor:lr,tickColor:lr,tickWidth:.2,titleColor:lr},axisBand:{grid:!1},axisX:{grid:!0,tickSize:10},axisY:{domain:!1,grid:!0,tickSize:0},legend:{labelFontSize:11,padding:1,symbolSize:30,symbolType:"square"},range:{category:["#ab5787","#51b2e5","#703c5c","#168dd9","#d190b6","#00609f","#d365ba","#154866","#666666","#c4c4c4"]}},dn="#3e5c69",n$={background:"#fff",arc:{fill:dn},area:{fill:dn},line:{stroke:dn},path:{stroke:dn},rect:{fill:dn},shape:{stroke:dn},symbol:{fill:dn},axis:{domainWidth:.5,grid:!0,labelPadding:2,tickSize:5,tickWidth:.5,titleFontWeight:"normal"},axisBand:{grid:!1},axisX:{gridWidth:.2},axisY:{gridDash:[3],gridWidth:.4},legend:{labelFontSize:11,padding:1,symbolType:"square"},range:{category:["#3e5c69","#6793a6","#182429","#0570b0","#3690c0","#74a9cf","#a6bddb","#e2ddf2"]}},je="#1696d2",vl="#000000",i$="#FFFFFF",ur="Lato",Us="Lato",r$="Lato",s$="#DEDDDD",o$=18,wi={"main-colors":["#1696d2","#d2d2d2","#000000","#fdbf11","#ec008b","#55b748","#5c5859","#db2b27"],"shades-blue":["#CFE8F3","#A2D4EC","#73BFE2","#46ABDB","#1696D2","#12719E","#0A4C6A","#062635"],"shades-gray":["#F5F5F5","#ECECEC","#E3E3E3","#DCDBDB","#D2D2D2","#9D9D9D","#696969","#353535"],"shades-yellow":["#FFF2CF","#FCE39E","#FDD870","#FCCB41","#FDBF11","#E88E2D","#CA5800","#843215"],"shades-magenta":["#F5CBDF","#EB99C2","#E46AA7","#E54096","#EC008B","#AF1F6B","#761548","#351123"],"shades-green":["#DCEDD9","#BCDEB4","#98CF90","#78C26D","#55B748","#408941","#2C5C2D","#1A2E19"],"shades-black":["#D5D5D4","#ADABAC","#848081","#5C5859","#332D2F","#262223","#1A1717","#0E0C0D"],"shades-red":["#F8D5D4","#F1AAA9","#E9807D","#E25552","#DB2B27","#A4201D","#6E1614","#370B0A"],"one-group":["#1696d2","#000000"],"two-groups-cat-1":["#1696d2","#000000"],"two-groups-cat-2":["#1696d2","#fdbf11"],"two-groups-cat-3":["#1696d2","#db2b27"],"two-groups-seq":["#a2d4ec","#1696d2"],"three-groups-cat":["#1696d2","#fdbf11","#000000"],"three-groups-seq":["#a2d4ec","#1696d2","#0a4c6a"],"four-groups-cat-1":["#000000","#d2d2d2","#fdbf11","#1696d2"],"four-groups-cat-2":["#1696d2","#ec0008b","#fdbf11","#5c5859"],"four-groups-seq":["#cfe8f3","#73bf42","#1696d2","#0a4c6a"],"five-groups-cat-1":["#1696d2","#fdbf11","#d2d2d2","#ec008b","#000000"],"five-groups-cat-2":["#1696d2","#0a4c6a","#d2d2d2","#fdbf11","#332d2f"],"five-groups-seq":["#cfe8f3","#73bf42","#1696d2","#0a4c6a","#000000"],"six-groups-cat-1":["#1696d2","#ec008b","#fdbf11","#000000","#d2d2d2","#55b748"],"six-groups-cat-2":["#1696d2","#d2d2d2","#ec008b","#fdbf11","#332d2f","#0a4c6a"],"six-groups-seq":["#cfe8f3","#a2d4ec","#73bfe2","#46abdb","#1696d2","#12719e"],"diverging-colors":["#ca5800","#fdbf11","#fdd870","#fff2cf","#cfe8f3","#73bfe2","#1696d2","#0a4c6a"]},a$={background:i$,title:{anchor:"start",fontSize:o$,font:ur},axisX:{domain:!0,domainColor:vl,domainWidth:1,grid:!1,labelFontSize:12,labelFont:Us,labelAngle:0,tickColor:vl,tickSize:5,titleFontSize:12,titlePadding:10,titleFont:ur},axisY:{domain:!1,domainWidth:1,grid:!0,gridColor:s$,gridWidth:1,labelFontSize:12,labelFont:Us,labelPadding:8,ticks:!1,titleFontSize:12,titlePadding:10,titleFont:ur,titleAngle:0,titleY:-10,titleX:18},legend:{labelFontSize:12,labelFont:Us,symbolSize:100,titleFontSize:12,titlePadding:10,titleFont:ur,orient:"right",offset:10},view:{stroke:"transparent"},range:{category:wi["six-groups-cat-1"],diverging:wi["diverging-colors"],heatmap:wi["diverging-colors"],ordinal:wi["six-groups-seq"],ramp:wi["shades-blue"]},area:{fill:je},rect:{fill:je},line:{color:je,stroke:je,strokeWidth:5},trail:{color:je,stroke:je,strokeWidth:0,size:1},path:{stroke:je,strokeWidth:.5},point:{filled:!0},text:{font:r$,color:je,fontSize:11,align:"center",fontWeight:400,size:11},style:{bar:{fill:je,stroke:null}},arc:{fill:je},shape:{stroke:je},symbol:{fill:je,size:30}},pn="#3366CC",Sl="#ccc",fr="Arial, sans-serif",c$={arc:{fill:pn},area:{fill:pn},path:{stroke:pn},rect:{fill:pn},shape:{stroke:pn},symbol:{stroke:pn},circle:{fill:pn},background:"#fff",padding:{top:10,right:10,bottom:10,left:10},style:{"guide-label":{font:fr,fontSize:12},"guide-title":{font:fr,fontSize:12},"group-title":{font:fr,fontSize:12}},title:{font:fr,fontSize:14,fontWeight:"bold",dy:-3,anchor:"start"},axis:{gridColor:Sl,tickColor:Sl,domain:!1,grid:!0},range:{category:["#4285F4","#DB4437","#F4B400","#0F9D58","#AB47BC","#00ACC1","#FF7043","#9E9D24","#5C6BC0","#F06292","#00796B","#C2185B"],heatmap:["#c6dafc","#5e97f6","#2a56c6"]}},qa=e=>e*(1/3+1),El=qa(9),$l=qa(10),wl=qa(12),Ci="Segoe UI",Cl="wf_standard-font, helvetica, arial, sans-serif",Nl="#252423",Ni="#605E5C",Fl="transparent",l$="#C8C6C4",qe="#118DFF",u$="#12239E",f$="#E66C37",d$="#6B007B",p$="#E044A7",g$="#744EC2",h$="#D9B300",m$="#D64550",fp=qe,dp="#DEEFFF",kl=[dp,fp],y$=[dp,"#c7e4ff","#b0d9ff","#9aceff","#83c3ff","#6cb9ff","#55aeff","#3fa3ff","#2898ff",fp],b$={view:{stroke:Fl},background:Fl,font:Ci,header:{titleFont:Cl,titleFontSize:wl,titleColor:Nl,labelFont:Ci,labelFontSize:$l,labelColor:Ni},axis:{ticks:!1,grid:!1,domain:!1,labelColor:Ni,labelFontSize:El,titleFont:Cl,titleColor:Nl,titleFontSize:wl,titleFontWeight:"normal"},axisQuantitative:{tickCount:3,grid:!0,gridColor:l$,gridDash:[1,5],labelFlush:!1},axisBand:{tickExtra:!0},axisX:{labelPadding:5},axisY:{labelPadding:10},bar:{fill:qe},line:{stroke:qe,strokeWidth:3,strokeCap:"round",strokeJoin:"round"},text:{font:Ci,fontSize:El,fill:Ni},arc:{fill:qe},area:{fill:qe,line:!0,opacity:.6},path:{stroke:qe},rect:{fill:qe},point:{fill:qe,filled:!0,size:75},shape:{stroke:qe},symbol:{fill:qe,strokeWidth:1.5,size:50},legend:{titleFont:Ci,titleFontWeight:"bold",titleColor:Ni,labelFont:Ci,labelFontSize:$l,labelColor:Ni,symbolType:"circle",symbolSize:75},range:{category:[qe,u$,f$,d$,p$,g$,h$,m$],diverging:kl,heatmap:kl,ordinal:y$}},Bs='IBM Plex Sans,system-ui,-apple-system,BlinkMacSystemFont,".sfnstext-regular",sans-serif',Tl=400,x$=["#8a3ffc","#33b1ff","#007d79","#ff7eb6","#fa4d56","#fff1f1","#6fdc8c","#4589ff","#d12771","#d2a106","#08bdba","#bae6ff","#ba4e00","#d4bbff"],v$=["#6929c4","#1192e8","#005d5d","#9f1853","#fa4d56","#570408","#198038","#002d9c","#ee538b","#b28600","#009d9a","#012749","#8a3800","#a56eff"];function ws({type:e,background:t}){const n=e==="dark"?"#161616":"#ffffff",i=e==="dark"?"#f4f4f4":"#161616",r=e==="dark"?x$:v$,s=e==="dark"?"#d4bbff":"#6929c4";return{background:t,arc:{fill:s},area:{fill:s},path:{stroke:s},rect:{fill:s},shape:{stroke:s},symbol:{stroke:s},circle:{fill:s},view:{fill:n,stroke:n},group:{fill:n},title:{color:i,anchor:"start",dy:-15,fontSize:16,font:Bs,fontWeight:600},axis:{labelColor:i,labelFontSize:12,grid:!0,gridColor:"#525252",titleColor:i,labelAngle:0},style:{"guide-label":{font:Bs,fill:i,fontWeight:Tl},"guide-title":{font:Bs,fill:i,fontWeight:Tl}},range:{category:r,diverging:["#750e13","#a2191f","#da1e28","#fa4d56","#ff8389","#ffb3b8","#ffd7d9","#fff1f1","#e5f6ff","#bae6ff","#82cfff","#33b1ff","#1192e8","#0072c3","#00539a","#003a6d"],heatmap:["#f6f2ff","#e8daff","#d4bbff","#be95ff","#a56eff","#8a3ffc","#6929c4","#491d8b","#31135e","#1c0f30"]}}}const S$=ws({type:"light",background:"#ffffff"}),E$=ws({type:"light",background:"#f4f4f4"}),$$=ws({type:"dark",background:"#262626"}),w$=ws({type:"dark",background:"#161616"}),C$=GE.version,N$=Object.freeze(Object.defineProperty({__proto__:null,carbong10:E$,carbong100:w$,carbong90:$$,carbonwhite:S$,dark:HE,excel:qE,fivethirtyeight:YE,ggplot2:KE,googlecharts:c$,latimes:e$,powerbi:b$,quartz:t$,urbaninstitute:a$,version:C$,vox:n$},Symbol.toStringTag,{value:"Module"}));var Ws={};function F$(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Gs,Al;function k$(){return Al||(Al=1,Gs=function(e){e.prototype[Symbol.iterator]=function*(){for(let t=this.head;t;t=t.next)yield t.value}}),Gs}var T$=H;H.Node=An;H.create=H;function H(e){var t=this;if(t instanceof H||(t=new H),t.tail=null,t.head=null,t.length=0,e&&typeof e.forEach=="function")e.forEach(function(r){t.push(r)});else if(arguments.length>0)for(var n=0,i=arguments.length;n<i;n++)t.push(arguments[n]);return t}H.prototype.removeNode=function(e){if(e.list!==this)throw new Error("removing node which does not belong to this list");var t=e.next,n=e.prev;return t&&(t.prev=n),n&&(n.next=t),e===this.head&&(this.head=t),e===this.tail&&(this.tail=n),e.list.length--,e.next=null,e.prev=null,e.list=null,t};H.prototype.unshiftNode=function(e){if(e!==this.head){e.list&&e.list.removeNode(e);var t=this.head;e.list=this,e.next=t,t&&(t.prev=e),this.head=e,this.tail||(this.tail=e),this.length++}};H.prototype.pushNode=function(e){if(e!==this.tail){e.list&&e.list.removeNode(e);var t=this.tail;e.list=this,e.prev=t,t&&(t.next=e),this.tail=e,this.head||(this.head=e),this.length++}};H.prototype.push=function(){for(var e=0,t=arguments.length;e<t;e++)O$(this,arguments[e]);return this.length};H.prototype.unshift=function(){for(var e=0,t=arguments.length;e<t;e++)_$(this,arguments[e]);return this.length};H.prototype.pop=function(){if(this.tail){var e=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,e}};H.prototype.shift=function(){if(this.head){var e=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,e}};H.prototype.forEach=function(e,t){t=t||this;for(var n=this.head,i=0;n!==null;i++)e.call(t,n.value,i,this),n=n.next};H.prototype.forEachReverse=function(e,t){t=t||this;for(var n=this.tail,i=this.length-1;n!==null;i--)e.call(t,n.value,i,this),n=n.prev};H.prototype.get=function(e){for(var t=0,n=this.head;n!==null&&t<e;t++)n=n.next;if(t===e&&n!==null)return n.value};H.prototype.getReverse=function(e){for(var t=0,n=this.tail;n!==null&&t<e;t++)n=n.prev;if(t===e&&n!==null)return n.value};H.prototype.map=function(e,t){t=t||this;for(var n=new H,i=this.head;i!==null;)n.push(e.call(t,i.value,this)),i=i.next;return n};H.prototype.mapReverse=function(e,t){t=t||this;for(var n=new H,i=this.tail;i!==null;)n.push(e.call(t,i.value,this)),i=i.prev;return n};H.prototype.reduce=function(e,t){var n,i=this.head;if(arguments.length>1)n=t;else if(this.head)i=this.head.next,n=this.head.value;else throw new TypeError("Reduce of empty list with no initial value");for(var r=0;i!==null;r++)n=e(n,i.value,r),i=i.next;return n};H.prototype.reduceReverse=function(e,t){var n,i=this.tail;if(arguments.length>1)n=t;else if(this.tail)i=this.tail.prev,n=this.tail.value;else throw new TypeError("Reduce of empty list with no initial value");for(var r=this.length-1;i!==null;r--)n=e(n,i.value,r),i=i.prev;return n};H.prototype.toArray=function(){for(var e=new Array(this.length),t=0,n=this.head;n!==null;t++)e[t]=n.value,n=n.next;return e};H.prototype.toArrayReverse=function(){for(var e=new Array(this.length),t=0,n=this.tail;n!==null;t++)e[t]=n.value,n=n.prev;return e};H.prototype.slice=function(e,t){t=t||this.length,t<0&&(t+=this.length),e=e||0,e<0&&(e+=this.length);var n=new H;if(t<e||t<0)return n;e<0&&(e=0),t>this.length&&(t=this.length);for(var i=0,r=this.head;r!==null&&i<e;i++)r=r.next;for(;r!==null&&i<t;i++,r=r.next)n.push(r.value);return n};H.prototype.sliceReverse=function(e,t){t=t||this.length,t<0&&(t+=this.length),e=e||0,e<0&&(e+=this.length);var n=new H;if(t<e||t<0)return n;e<0&&(e=0),t>this.length&&(t=this.length);for(var i=this.length,r=this.tail;r!==null&&i>t;i--)r=r.prev;for(;r!==null&&i>e;i--,r=r.prev)n.push(r.value);return n};H.prototype.splice=function(e,t,...n){e>this.length&&(e=this.length-1),e<0&&(e=this.length+e);for(var i=0,r=this.head;r!==null&&i<e;i++)r=r.next;for(var s=[],i=0;r&&i<t;i++)s.push(r.value),r=this.removeNode(r);r===null&&(r=this.tail),r!==this.head&&r!==this.tail&&(r=r.prev);for(var i=0;i<n.length;i++)r=A$(this,r,n[i]);return s};H.prototype.reverse=function(){for(var e=this.head,t=this.tail,n=e;n!==null;n=n.prev){var i=n.prev;n.prev=n.next,n.next=i}return this.head=t,this.tail=e,this};function A$(e,t,n){var i=t===e.head?new An(n,null,t,e):new An(n,t,t.next,e);return i.next===null&&(e.tail=i),i.prev===null&&(e.head=i),e.length++,i}function O$(e,t){e.tail=new An(t,e.tail,null,e),e.head||(e.head=e.tail),e.length++}function _$(e,t){e.head=new An(t,null,e.head,e),e.tail||(e.tail=e.head),e.length++}function An(e,t,n,i){if(!(this instanceof An))return new An(e,t,n,i);this.list=i,this.value=e,t?(t.next=this,this.prev=t):this.prev=null,n?(n.prev=this,this.next=n):this.next=null}try{k$()(H)}catch{}const R$=T$,gn=Symbol("max"),Ct=Symbol("length"),Bn=Symbol("lengthCalculator"),Ii=Symbol("allowStale"),hn=Symbol("maxAge"),wt=Symbol("dispose"),Ol=Symbol("noDisposeOnSet"),pe=Symbol("lruList"),Ve=Symbol("cache"),pp=Symbol("updateAgeOnGet"),Hs=()=>1;class I${constructor(t){if(typeof t=="number"&&(t={max:t}),t||(t={}),t.max&&(typeof t.max!="number"||t.max<0))throw new TypeError("max must be a non-negative number");this[gn]=t.max||1/0;const n=t.length||Hs;if(this[Bn]=typeof n!="function"?Hs:n,this[Ii]=t.stale||!1,t.maxAge&&typeof t.maxAge!="number")throw new TypeError("maxAge must be a number");this[hn]=t.maxAge||0,this[wt]=t.dispose,this[Ol]=t.noDisposeOnSet||!1,this[pp]=t.updateAgeOnGet||!1,this.reset()}set max(t){if(typeof t!="number"||t<0)throw new TypeError("max must be a non-negative number");this[gn]=t||1/0,Fi(this)}get max(){return this[gn]}set allowStale(t){this[Ii]=!!t}get allowStale(){return this[Ii]}set maxAge(t){if(typeof t!="number")throw new TypeError("maxAge must be a non-negative number");this[hn]=t,Fi(this)}get maxAge(){return this[hn]}set lengthCalculator(t){typeof t!="function"&&(t=Hs),t!==this[Bn]&&(this[Bn]=t,this[Ct]=0,this[pe].forEach(n=>{n.length=this[Bn](n.value,n.key),this[Ct]+=n.length})),Fi(this)}get lengthCalculator(){return this[Bn]}get length(){return this[Ct]}get itemCount(){return this[pe].length}rforEach(t,n){n=n||this;for(let i=this[pe].tail;i!==null;){const r=i.prev;_l(this,t,i,n),i=r}}forEach(t,n){n=n||this;for(let i=this[pe].head;i!==null;){const r=i.next;_l(this,t,i,n),i=r}}keys(){return this[pe].toArray().map(t=>t.key)}values(){return this[pe].toArray().map(t=>t.value)}reset(){this[wt]&&this[pe]&&this[pe].length&&this[pe].forEach(t=>this[wt](t.key,t.value)),this[Ve]=new Map,this[pe]=new R$,this[Ct]=0}dump(){return this[pe].map(t=>_r(this,t)?!1:{k:t.key,v:t.value,e:t.now+(t.maxAge||0)}).toArray().filter(t=>t)}dumpLru(){return this[pe]}set(t,n,i){if(i=i||this[hn],i&&typeof i!="number")throw new TypeError("maxAge must be a number");const r=i?Date.now():0,s=this[Bn](n,t);if(this[Ve].has(t)){if(s>this[gn])return Kn(this,this[Ve].get(t)),!1;const c=this[Ve].get(t).value;return this[wt]&&(this[Ol]||this[wt](t,c.value)),c.now=r,c.maxAge=i,c.value=n,this[Ct]+=s-c.length,c.length=s,this.get(t),Fi(this),!0}const o=new L$(t,n,s,r,i);return o.length>this[gn]?(this[wt]&&this[wt](t,n),!1):(this[Ct]+=o.length,this[pe].unshift(o),this[Ve].set(t,this[pe].head),Fi(this),!0)}has(t){if(!this[Ve].has(t))return!1;const n=this[Ve].get(t).value;return!_r(this,n)}get(t){return qs(this,t,!0)}peek(t){return qs(this,t,!1)}pop(){const t=this[pe].tail;return t?(Kn(this,t),t.value):null}del(t){Kn(this,this[Ve].get(t))}load(t){this.reset();const n=Date.now();for(let i=t.length-1;i>=0;i--){const r=t[i],s=r.e||0;if(s===0)this.set(r.k,r.v);else{const o=s-n;o>0&&this.set(r.k,r.v,o)}}}prune(){this[Ve].forEach((t,n)=>qs(this,n,!1))}}const qs=(e,t,n)=>{const i=e[Ve].get(t);if(i){const r=i.value;if(_r(e,r)){if(Kn(e,i),!e[Ii])return}else n&&(e[pp]&&(i.value.now=Date.now()),e[pe].unshiftNode(i));return r.value}},_r=(e,t)=>{if(!t||!t.maxAge&&!e[hn])return!1;const n=Date.now()-t.now;return t.maxAge?n>t.maxAge:e[hn]&&n>e[hn]},Fi=e=>{if(e[Ct]>e[gn])for(let t=e[pe].tail;e[Ct]>e[gn]&&t!==null;){const n=t.prev;Kn(e,t),t=n}},Kn=(e,t)=>{if(t){const n=t.value;e[wt]&&e[wt](n.key,n.value),e[Ct]-=n.length,e[Ve].delete(n.key),e[pe].removeNode(t)}};class L${constructor(t,n,i,r,s){this.key=t,this.value=n,this.length=i,this.now=r,this.maxAge=s||0}}const _l=(e,t,n,i)=>{let r=n.value;_r(e,r)&&(Kn(e,n),e[Ii]||(r=void 0)),r&&t.call(i,r.value,r.key,e)};var P$=I$;const z$=Object.freeze({loose:!0}),D$=Object.freeze({}),j$=e=>e?typeof e!="object"?z$:e:D$;var Va=j$,xo={exports:{}};const M$="2.0.0",gp=256,U$=Number.MAX_SAFE_INTEGER||9007199254740991,B$=16,W$=gp-6,G$=["major","premajor","minor","preminor","patch","prepatch","prerelease"];var Xa={MAX_LENGTH:gp,MAX_SAFE_COMPONENT_LENGTH:B$,MAX_SAFE_BUILD_LENGTH:W$,MAX_SAFE_INTEGER:U$,RELEASE_TYPES:G$,SEMVER_SPEC_VERSION:M$,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2};const H$=typeof process=="object"&&Ws&&Ws.NODE_DEBUG&&/\bsemver\b/i.test(Ws.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};var Cs=H$;(function(e,t){const{MAX_SAFE_COMPONENT_LENGTH:n,MAX_SAFE_BUILD_LENGTH:i,MAX_LENGTH:r}=Xa,s=Cs;t=e.exports={};const o=t.re=[],a=t.safeRe=[],c=t.src=[],l=t.t={};let u=0;const f="[a-zA-Z0-9-]",d=[["\\s",1],["\\d",r],[f,i]],g=h=>{for(const[m,y]of d)h=h.split(`${m}*`).join(`${m}{0,${y}}`).split(`${m}+`).join(`${m}{1,${y}}`);return h},p=(h,m,y)=>{const b=g(m),C=u++;s(h,C,m),l[h]=C,c[C]=m,o[C]=new RegExp(m,y?"g":void 0),a[C]=new RegExp(b,y?"g":void 0)};p("NUMERICIDENTIFIER","0|[1-9]\\d*"),p("NUMERICIDENTIFIERLOOSE","\\d+"),p("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${f}*`),p("MAINVERSION",`(${c[l.NUMERICIDENTIFIER]})\\.(${c[l.NUMERICIDENTIFIER]})\\.(${c[l.NUMERICIDENTIFIER]})`),p("MAINVERSIONLOOSE",`(${c[l.NUMERICIDENTIFIERLOOSE]})\\.(${c[l.NUMERICIDENTIFIERLOOSE]})\\.(${c[l.NUMERICIDENTIFIERLOOSE]})`),p("PRERELEASEIDENTIFIER",`(?:${c[l.NUMERICIDENTIFIER]}|${c[l.NONNUMERICIDENTIFIER]})`),p("PRERELEASEIDENTIFIERLOOSE",`(?:${c[l.NUMERICIDENTIFIERLOOSE]}|${c[l.NONNUMERICIDENTIFIER]})`),p("PRERELEASE",`(?:-(${c[l.PRERELEASEIDENTIFIER]}(?:\\.${c[l.PRERELEASEIDENTIFIER]})*))`),p("PRERELEASELOOSE",`(?:-?(${c[l.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${c[l.PRERELEASEIDENTIFIERLOOSE]})*))`),p("BUILDIDENTIFIER",`${f}+`),p("BUILD",`(?:\\+(${c[l.BUILDIDENTIFIER]}(?:\\.${c[l.BUILDIDENTIFIER]})*))`),p("FULLPLAIN",`v?${c[l.MAINVERSION]}${c[l.PRERELEASE]}?${c[l.BUILD]}?`),p("FULL",`^${c[l.FULLPLAIN]}$`),p("LOOSEPLAIN",`[v=\\s]*${c[l.MAINVERSIONLOOSE]}${c[l.PRERELEASELOOSE]}?${c[l.BUILD]}?`),p("LOOSE",`^${c[l.LOOSEPLAIN]}$`),p("GTLT","((?:<|>)?=?)"),p("XRANGEIDENTIFIERLOOSE",`${c[l.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),p("XRANGEIDENTIFIER",`${c[l.NUMERICIDENTIFIER]}|x|X|\\*`),p("XRANGEPLAIN",`[v=\\s]*(${c[l.XRANGEIDENTIFIER]})(?:\\.(${c[l.XRANGEIDENTIFIER]})(?:\\.(${c[l.XRANGEIDENTIFIER]})(?:${c[l.PRERELEASE]})?${c[l.BUILD]}?)?)?`),p("XRANGEPLAINLOOSE",`[v=\\s]*(${c[l.XRANGEIDENTIFIERLOOSE]})(?:\\.(${c[l.XRANGEIDENTIFIERLOOSE]})(?:\\.(${c[l.XRANGEIDENTIFIERLOOSE]})(?:${c[l.PRERELEASELOOSE]})?${c[l.BUILD]}?)?)?`),p("XRANGE",`^${c[l.GTLT]}\\s*${c[l.XRANGEPLAIN]}$`),p("XRANGELOOSE",`^${c[l.GTLT]}\\s*${c[l.XRANGEPLAINLOOSE]}$`),p("COERCEPLAIN",`(^|[^\\d])(\\d{1,${n}})(?:\\.(\\d{1,${n}}))?(?:\\.(\\d{1,${n}}))?`),p("COERCE",`${c[l.COERCEPLAIN]}(?:$|[^\\d])`),p("COERCEFULL",c[l.COERCEPLAIN]+`(?:${c[l.PRERELEASE]})?(?:${c[l.BUILD]})?(?:$|[^\\d])`),p("COERCERTL",c[l.COERCE],!0),p("COERCERTLFULL",c[l.COERCEFULL],!0),p("LONETILDE","(?:~>?)"),p("TILDETRIM",`(\\s*)${c[l.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",p("TILDE",`^${c[l.LONETILDE]}${c[l.XRANGEPLAIN]}$`),p("TILDELOOSE",`^${c[l.LONETILDE]}${c[l.XRANGEPLAINLOOSE]}$`),p("LONECARET","(?:\\^)"),p("CARETTRIM",`(\\s*)${c[l.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",p("CARET",`^${c[l.LONECARET]}${c[l.XRANGEPLAIN]}$`),p("CARETLOOSE",`^${c[l.LONECARET]}${c[l.XRANGEPLAINLOOSE]}$`),p("COMPARATORLOOSE",`^${c[l.GTLT]}\\s*(${c[l.LOOSEPLAIN]})$|^$`),p("COMPARATOR",`^${c[l.GTLT]}\\s*(${c[l.FULLPLAIN]})$|^$`),p("COMPARATORTRIM",`(\\s*)${c[l.GTLT]}\\s*(${c[l.LOOSEPLAIN]}|${c[l.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",p("HYPHENRANGE",`^\\s*(${c[l.XRANGEPLAIN]})\\s+-\\s+(${c[l.XRANGEPLAIN]})\\s*$`),p("HYPHENRANGELOOSE",`^\\s*(${c[l.XRANGEPLAINLOOSE]})\\s+-\\s+(${c[l.XRANGEPLAINLOOSE]})\\s*$`),p("STAR","(<|>)?=?\\s*\\*"),p("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),p("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")})(xo,xo.exports);var Ya=xo.exports;const Rl=/^[0-9]+$/,hp=(e,t)=>{const n=Rl.test(e),i=Rl.test(t);return n&&i&&(e=+e,t=+t),e===t?0:n&&!i?-1:i&&!n?1:e<t?-1:1},q$=(e,t)=>hp(t,e);var V$={compareIdentifiers:hp,rcompareIdentifiers:q$};const dr=Cs,{MAX_LENGTH:Il,MAX_SAFE_INTEGER:pr}=Xa,{safeRe:Ll,t:Pl}=Ya,X$=Va,{compareIdentifiers:Wn}=V$;let Y$=class at{constructor(t,n){if(n=X$(n),t instanceof at){if(t.loose===!!n.loose&&t.includePrerelease===!!n.includePrerelease)return t;t=t.version}else if(typeof t!="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof t}".`);if(t.length>Il)throw new TypeError(`version is longer than ${Il} characters`);dr("SemVer",t,n),this.options=n,this.loose=!!n.loose,this.includePrerelease=!!n.includePrerelease;const i=t.trim().match(n.loose?Ll[Pl.LOOSE]:Ll[Pl.FULL]);if(!i)throw new TypeError(`Invalid Version: ${t}`);if(this.raw=t,this.major=+i[1],this.minor=+i[2],this.patch=+i[3],this.major>pr||this.major<0)throw new TypeError("Invalid major version");if(this.minor>pr||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>pr||this.patch<0)throw new TypeError("Invalid patch version");i[4]?this.prerelease=i[4].split(".").map(r=>{if(/^[0-9]+$/.test(r)){const s=+r;if(s>=0&&s<pr)return s}return r}):this.prerelease=[],this.build=i[5]?i[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(t){if(dr("SemVer.compare",this.version,this.options,t),!(t instanceof at)){if(typeof t=="string"&&t===this.version)return 0;t=new at(t,this.options)}return t.version===this.version?0:this.compareMain(t)||this.comparePre(t)}compareMain(t){return t instanceof at||(t=new at(t,this.options)),Wn(this.major,t.major)||Wn(this.minor,t.minor)||Wn(this.patch,t.patch)}comparePre(t){if(t instanceof at||(t=new at(t,this.options)),this.prerelease.length&&!t.prerelease.length)return-1;if(!this.prerelease.length&&t.prerelease.length)return 1;if(!this.prerelease.length&&!t.prerelease.length)return 0;let n=0;do{const i=this.prerelease[n],r=t.prerelease[n];if(dr("prerelease compare",n,i,r),i===void 0&&r===void 0)return 0;if(r===void 0)return 1;if(i===void 0)return-1;if(i===r)continue;return Wn(i,r)}while(++n)}compareBuild(t){t instanceof at||(t=new at(t,this.options));let n=0;do{const i=this.build[n],r=t.build[n];if(dr("prerelease compare",n,i,r),i===void 0&&r===void 0)return 0;if(r===void 0)return 1;if(i===void 0)return-1;if(i===r)continue;return Wn(i,r)}while(++n)}inc(t,n,i){switch(t){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",n,i);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",n,i);break;case"prepatch":this.prerelease.length=0,this.inc("patch",n,i),this.inc("pre",n,i);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",n,i),this.inc("pre",n,i);break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":{const r=Number(i)?1:0;if(!n&&i===!1)throw new Error("invalid increment argument: identifier is empty");if(this.prerelease.length===0)this.prerelease=[r];else{let s=this.prerelease.length;for(;--s>=0;)typeof this.prerelease[s]=="number"&&(this.prerelease[s]++,s=-2);if(s===-1){if(n===this.prerelease.join(".")&&i===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(r)}}if(n){let s=[n,r];i===!1&&(s=[n]),Wn(this.prerelease[0],n)===0?isNaN(this.prerelease[1])&&(this.prerelease=s):this.prerelease=s}break}default:throw new Error(`invalid increment argument: ${t}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}};var Ka=Y$;const zl=Ka,K$=(e,t,n)=>new zl(e,n).compare(new zl(t,n));var Si=K$;const Q$=Si,J$=(e,t,n)=>Q$(e,t,n)===0;var Z$=J$;const e1=Si,t1=(e,t,n)=>e1(e,t,n)!==0;var n1=t1;const i1=Si,r1=(e,t,n)=>i1(e,t,n)>0;var s1=r1;const o1=Si,a1=(e,t,n)=>o1(e,t,n)>=0;var c1=a1;const l1=Si,u1=(e,t,n)=>l1(e,t,n)<0;var f1=u1;const d1=Si,p1=(e,t,n)=>d1(e,t,n)<=0;var g1=p1;const h1=Z$,m1=n1,y1=s1,b1=c1,x1=f1,v1=g1,S1=(e,t,n,i)=>{switch(t){case"===":return typeof e=="object"&&(e=e.version),typeof n=="object"&&(n=n.version),e===n;case"!==":return typeof e=="object"&&(e=e.version),typeof n=="object"&&(n=n.version),e!==n;case"":case"=":case"==":return h1(e,n,i);case"!=":return m1(e,n,i);case">":return y1(e,n,i);case">=":return b1(e,n,i);case"<":return x1(e,n,i);case"<=":return v1(e,n,i);default:throw new TypeError(`Invalid operator: ${t}`)}};var E1=S1,Vs,Dl;function $1(){if(Dl)return Vs;Dl=1;const e=Symbol("SemVer ANY");class t{static get ANY(){return e}constructor(u,f){if(f=n(f),u instanceof t){if(u.loose===!!f.loose)return u;u=u.value}u=u.trim().split(/\s+/).join(" "),o("comparator",u,f),this.options=f,this.loose=!!f.loose,this.parse(u),this.semver===e?this.value="":this.value=this.operator+this.semver.version,o("comp",this)}parse(u){const f=this.options.loose?i[r.COMPARATORLOOSE]:i[r.COMPARATOR],d=u.match(f);if(!d)throw new TypeError(`Invalid comparator: ${u}`);this.operator=d[1]!==void 0?d[1]:"",this.operator==="="&&(this.operator=""),d[2]?this.semver=new a(d[2],this.options.loose):this.semver=e}toString(){return this.value}test(u){if(o("Comparator.test",u,this.options.loose),this.semver===e||u===e)return!0;if(typeof u=="string")try{u=new a(u,this.options)}catch{return!1}return s(u,this.operator,this.semver,this.options)}intersects(u,f){if(!(u instanceof t))throw new TypeError("a Comparator is required");return this.operator===""?this.value===""?!0:new c(u.value,f).test(this.value):u.operator===""?u.value===""?!0:new c(this.value,f).test(u.semver):(f=n(f),f.includePrerelease&&(this.value==="<0.0.0-0"||u.value==="<0.0.0-0")||!f.includePrerelease&&(this.value.startsWith("<0.0.0")||u.value.startsWith("<0.0.0"))?!1:!!(this.operator.startsWith(">")&&u.operator.startsWith(">")||this.operator.startsWith("<")&&u.operator.startsWith("<")||this.semver.version===u.semver.version&&this.operator.includes("=")&&u.operator.includes("=")||s(this.semver,"<",u.semver,f)&&this.operator.startsWith(">")&&u.operator.startsWith("<")||s(this.semver,">",u.semver,f)&&this.operator.startsWith("<")&&u.operator.startsWith(">")))}}Vs=t;const n=Va,{safeRe:i,t:r}=Ya,s=E1,o=Cs,a=Ka,c=mp();return Vs}var Xs,jl;function mp(){if(jl)return Xs;jl=1;class e{constructor($,R){if(R=i(R),$ instanceof e)return $.loose===!!R.loose&&$.includePrerelease===!!R.includePrerelease?$:new e($.raw,R);if($ instanceof r)return this.raw=$.value,this.set=[[$]],this.format(),this;if(this.options=R,this.loose=!!R.loose,this.includePrerelease=!!R.includePrerelease,this.raw=$.trim().split(/\s+/).join(" "),this.set=this.raw.split("||").map(_=>this.parseRange(_.trim())).filter(_=>_.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const _=this.set[0];if(this.set=this.set.filter(L=>!p(L[0])),this.set.length===0)this.set=[_];else if(this.set.length>1){for(const L of this.set)if(L.length===1&&h(L[0])){this.set=[L];break}}}this.format()}format(){return this.range=this.set.map($=>$.join(" ").trim()).join("||").trim(),this.range}toString(){return this.range}parseRange($){const _=((this.options.includePrerelease&&d)|(this.options.loose&&g))+":"+$,L=n.get(_);if(L)return L;const O=this.options.loose,j=O?a[c.HYPHENRANGELOOSE]:a[c.HYPHENRANGE];$=$.replace(j,de(this.options.includePrerelease)),s("hyphen replace",$),$=$.replace(a[c.COMPARATORTRIM],l),s("comparator trim",$),$=$.replace(a[c.TILDETRIM],u),s("tilde trim",$),$=$.replace(a[c.CARETTRIM],f),s("caret trim",$);let q=$.split(" ").map(ne=>y(ne,this.options)).join(" ").split(/\s+/).map(ne=>we(ne,this.options));O&&(q=q.filter(ne=>(s("loose invalid filter",ne,this.options),!!ne.match(a[c.COMPARATORLOOSE])))),s("range list",q);const U=new Map,Z=q.map(ne=>new r(ne,this.options));for(const ne of Z){if(p(ne))return[ne];U.set(ne.value,ne)}U.size>1&&U.has("")&&U.delete("");const Se=[...U.values()];return n.set(_,Se),Se}intersects($,R){if(!($ instanceof e))throw new TypeError("a Range is required");return this.set.some(_=>m(_,R)&&$.set.some(L=>m(L,R)&&_.every(O=>L.every(j=>O.intersects(j,R)))))}test($){if(!$)return!1;if(typeof $=="string")try{$=new o($,this.options)}catch{return!1}for(let R=0;R<this.set.length;R++)if(st(this.set[R],$,this.options))return!0;return!1}}Xs=e;const t=P$,n=new t({max:1e3}),i=Va,r=$1(),s=Cs,o=Ka,{safeRe:a,t:c,comparatorTrimReplace:l,tildeTrimReplace:u,caretTrimReplace:f}=Ya,{FLAG_INCLUDE_PRERELEASE:d,FLAG_LOOSE:g}=Xa,p=N=>N.value==="<0.0.0-0",h=N=>N.value==="",m=(N,$)=>{let R=!0;const _=N.slice();let L=_.pop();for(;R&&_.length;)R=_.every(O=>L.intersects(O,$)),L=_.pop();return R},y=(N,$)=>(s("comp",N,$),N=w(N,$),s("caret",N),N=C(N,$),s("tildes",N),N=A(N,$),s("xrange",N),N=K(N,$),s("stars",N),N),b=N=>!N||N.toLowerCase()==="x"||N==="*",C=(N,$)=>N.trim().split(/\s+/).map(R=>D(R,$)).join(" "),D=(N,$)=>{const R=$.loose?a[c.TILDELOOSE]:a[c.TILDE];return N.replace(R,(_,L,O,j,q)=>{s("tilde",N,_,L,O,j,q);let U;return b(L)?U="":b(O)?U=`>=${L}.0.0 <${+L+1}.0.0-0`:b(j)?U=`>=${L}.${O}.0 <${L}.${+O+1}.0-0`:q?(s("replaceTilde pr",q),U=`>=${L}.${O}.${j}-${q} <${L}.${+O+1}.0-0`):U=`>=${L}.${O}.${j} <${L}.${+O+1}.0-0`,s("tilde return",U),U})},w=(N,$)=>N.trim().split(/\s+/).map(R=>F(R,$)).join(" "),F=(N,$)=>{s("caret",N,$);const R=$.loose?a[c.CARETLOOSE]:a[c.CARET],_=$.includePrerelease?"-0":"";return N.replace(R,(L,O,j,q,U)=>{s("caret",N,L,O,j,q,U);let Z;return b(O)?Z="":b(j)?Z=`>=${O}.0.0${_} <${+O+1}.0.0-0`:b(q)?O==="0"?Z=`>=${O}.${j}.0${_} <${O}.${+j+1}.0-0`:Z=`>=${O}.${j}.0${_} <${+O+1}.0.0-0`:U?(s("replaceCaret pr",U),O==="0"?j==="0"?Z=`>=${O}.${j}.${q}-${U} <${O}.${j}.${+q+1}-0`:Z=`>=${O}.${j}.${q}-${U} <${O}.${+j+1}.0-0`:Z=`>=${O}.${j}.${q}-${U} <${+O+1}.0.0-0`):(s("no pr"),O==="0"?j==="0"?Z=`>=${O}.${j}.${q}${_} <${O}.${j}.${+q+1}-0`:Z=`>=${O}.${j}.${q}${_} <${O}.${+j+1}.0-0`:Z=`>=${O}.${j}.${q} <${+O+1}.0.0-0`),s("caret return",Z),Z})},A=(N,$)=>(s("replaceXRanges",N,$),N.split(/\s+/).map(R=>M(R,$)).join(" ")),M=(N,$)=>{N=N.trim();const R=$.loose?a[c.XRANGELOOSE]:a[c.XRANGE];return N.replace(R,(_,L,O,j,q,U)=>{s("xRange",N,_,L,O,j,q,U);const Z=b(O),Se=Z||b(j),ne=Se||b(q),on=ne;return L==="="&&on&&(L=""),U=$.includePrerelease?"-0":"",Z?L===">"||L==="<"?_="<0.0.0-0":_="*":L&&on?(Se&&(j=0),q=0,L===">"?(L=">=",Se?(O=+O+1,j=0,q=0):(j=+j+1,q=0)):L==="<="&&(L="<",Se?O=+O+1:j=+j+1),L==="<"&&(U="-0"),_=`${L+O}.${j}.${q}${U}`):Se?_=`>=${O}.0.0${U} <${+O+1}.0.0-0`:ne&&(_=`>=${O}.${j}.0${U} <${O}.${+j+1}.0-0`),s("xRange return",_),_})},K=(N,$)=>(s("replaceStars",N,$),N.trim().replace(a[c.STAR],"")),we=(N,$)=>(s("replaceGTE0",N,$),N.trim().replace(a[$.includePrerelease?c.GTE0PRE:c.GTE0],"")),de=N=>($,R,_,L,O,j,q,U,Z,Se,ne,on,Ns)=>(b(_)?R="":b(L)?R=`>=${_}.0.0${N?"-0":""}`:b(O)?R=`>=${_}.${L}.0${N?"-0":""}`:j?R=`>=${R}`:R=`>=${R}${N?"-0":""}`,b(Z)?U="":b(Se)?U=`<${+Z+1}.0.0-0`:b(ne)?U=`<${Z}.${+Se+1}.0-0`:on?U=`<=${Z}.${Se}.${ne}-${on}`:N?U=`<${Z}.${Se}.${+ne+1}-0`:U=`<=${U}`,`${R} ${U}`.trim()),st=(N,$,R)=>{for(let _=0;_<N.length;_++)if(!N[_].test($))return!1;if($.prerelease.length&&!R.includePrerelease){for(let _=0;_<N.length;_++)if(s(N[_].semver),N[_].semver!==r.ANY&&N[_].semver.prerelease.length>0){const L=N[_].semver;if(L.major===$.major&&L.minor===$.minor&&L.patch===$.patch)return!0}return!1}return!0};return Xs}const w1=mp(),C1=(e,t,n)=>{try{t=new w1(t,n)}catch{return!1}return t.test(e)};var N1=C1,yp=F$(N1);function F1(e,t,n){const i=e.open(t),r=1e4,s=250,{origin:o}=new URL(t);let a=~~(r/s);function c(u){u.source===i&&(a=0,e.removeEventListener("message",c,!1))}e.addEventListener("message",c,!1);function l(){a<=0||(i.postMessage(n,o),setTimeout(l,s),a-=1)}setTimeout(l,s)}var k1=`.vega-embed {
  position: relative;
  display: inline-block;
  box-sizing: border-box;
}
.vega-embed.has-actions {
  padding-right: 38px;
}
.vega-embed details:not([open]) > :not(summary) {
  display: none !important;
}
.vega-embed summary {
  list-style: none;
  position: absolute;
  top: 0;
  right: 0;
  padding: 6px;
  z-index: 1000;
  background: white;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
  color: #1b1e23;
  border: 1px solid #aaa;
  border-radius: 999px;
  opacity: 0.2;
  transition: opacity 0.4s ease-in;
  cursor: pointer;
  line-height: 0px;
}
.vega-embed summary::-webkit-details-marker {
  display: none;
}
.vega-embed summary:active {
  box-shadow: #aaa 0px 0px 0px 1px inset;
}
.vega-embed summary svg {
  width: 14px;
  height: 14px;
}
.vega-embed details[open] summary {
  opacity: 0.7;
}
.vega-embed:hover summary, .vega-embed:focus-within summary {
  opacity: 1 !important;
  transition: opacity 0.2s ease;
}
.vega-embed .vega-actions {
  position: absolute;
  z-index: 1001;
  top: 35px;
  right: -9px;
  display: flex;
  flex-direction: column;
  padding-bottom: 8px;
  padding-top: 8px;
  border-radius: 4px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.2);
  border: 1px solid #d9d9d9;
  background: white;
  animation-duration: 0.15s;
  animation-name: scale-in;
  animation-timing-function: cubic-bezier(0.2, 0, 0.13, 1.5);
  text-align: left;
}
.vega-embed .vega-actions a {
  padding: 8px 16px;
  font-family: sans-serif;
  font-size: 14px;
  font-weight: 600;
  white-space: nowrap;
  color: #434a56;
  text-decoration: none;
}
.vega-embed .vega-actions a:hover, .vega-embed .vega-actions a:focus {
  background-color: #f7f7f9;
  color: black;
}
.vega-embed .vega-actions::before, .vega-embed .vega-actions::after {
  content: "";
  display: inline-block;
  position: absolute;
}
.vega-embed .vega-actions::before {
  left: auto;
  right: 14px;
  top: -16px;
  border: 8px solid rgba(0, 0, 0, 0);
  border-bottom-color: #d9d9d9;
}
.vega-embed .vega-actions::after {
  left: auto;
  right: 15px;
  top: -14px;
  border: 7px solid rgba(0, 0, 0, 0);
  border-bottom-color: #fff;
}
.vega-embed .chart-wrapper.fit-x {
  width: 100%;
}
.vega-embed .chart-wrapper.fit-y {
  height: 100%;
}

.vega-embed-wrapper {
  max-width: 100%;
  overflow: auto;
  padding-right: 14px;
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.6);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
`;function bp(e,...t){for(const n of t)T1(e,n);return e}function T1(e,t){for(const n of Object.keys(t))vo(e,n,t[n],!0)}var A1="vega-embed",O1="6.25.0",_1="Publish Vega visualizations as embedded web components.",R1=["vega","data","visualization","component","embed"],I1={type:"git",url:"http://github.com/vega/vega-embed.git"},L1={name:"UW Interactive Data Lab",url:"http://idl.cs.washington.edu"},P1=[{name:"Dominik Moritz",url:"https://www.domoritz.de"}],z1={url:"https://github.com/vega/vega-embed/issues"},D1="https://github.com/vega/vega-embed#readme",j1="BSD-3-Clause",M1="build/vega-embed.js",U1="build/vega-embed.module.js",B1="build/vega-embed.min.js",W1="build/vega-embed.min.js",G1="build/vega-embed.module.d.ts",H1=["src","build"],q1={"@babel/core":"^7.24.4","@babel/plugin-transform-runtime":"^7.24.3","@babel/preset-env":"^7.24.4","@babel/preset-typescript":"^7.24.1","@release-it/conventional-changelog":"^8.0.1","@rollup/plugin-commonjs":"25.0.7","@rollup/plugin-json":"^6.1.0","@rollup/plugin-node-resolve":"^15.2.3","@rollup/plugin-terser":"^0.4.4","@types/jest":"^29.5.12","@types/semver":"^7.5.8","@typescript-eslint/eslint-plugin":"^7.6.0","@typescript-eslint/parser":"^7.6.0","browser-sync":"^3.0.2",concurrently:"^8.2.2","del-cli":"^5.1.0",eslint:"^8.56.0","eslint-config-prettier":"^9.1.0","eslint-plugin-jest":"^28.2.0","eslint-plugin-prettier":"^5.1.3",jest:"^29.7.0","jest-canvas-mock":"^2.5.2","jest-environment-jsdom":"^29.7.0","postinstall-postinstall":"^2.1.0",prettier:"^3.2.5","release-it":"^17.1.1",rollup:"4.14.1","rollup-plugin-bundle-size":"^1.0.3","rollup-plugin-ts":"^3.4.5",sass:"^1.74.1",typescript:"^5.4.5",vega:"^5.22.1","vega-lite":"^5.2.0"},V1={vega:"^5.21.0","vega-lite":"*"},X1={"fast-json-patch":"^3.1.1","json-stringify-pretty-compact":"^3.0.0",semver:"^7.6.0",tslib:"^2.6.2","vega-interpreter":"^1.0.5","vega-schema-url-parser":"^2.2.0","vega-themes":"^2.14.0","vega-tooltip":"^0.34.0"},Y1={prebuild:"yarn clean && yarn build:style",build:"rollup -c","build:style":"./build-style.sh",clean:"del-cli build src/style.ts",prepublishOnly:"yarn clean && yarn build",preversion:"yarn lint && yarn test",serve:"browser-sync start --directory -s -f build *.html",start:"yarn build && concurrently --kill-others -n Server,Rollup 'yarn serve' 'rollup -c -w'",pretest:"yarn build:style",test:"jest","test:inspect":"node --inspect-brk ./node_modules/.bin/jest --runInBand",prettierbase:"prettier '*.{css,scss,html}'",format:"eslint . --fix && yarn prettierbase --write",lint:"eslint . && yarn prettierbase --check",release:"release-it"},K1={name:A1,version:O1,description:_1,keywords:R1,repository:I1,author:L1,contributors:P1,bugs:z1,homepage:D1,license:j1,main:M1,module:U1,unpkg:B1,jsdelivr:W1,types:G1,files:H1,devDependencies:q1,peerDependencies:V1,dependencies:X1,scripts:Y1};const Gw=K1.version,ct=kp;let Bi=CE;const gr=typeof window<"u"?window:void 0;var Wl;Bi===void 0&&((Wl=gr==null?void 0:gr.vl)!=null&&Wl.compile)&&(Bi=gr.vl);const Q1={export:{svg:!0,png:!0},source:!0,compiled:!0,editor:!0},J1={CLICK_TO_VIEW_ACTIONS:"Click to view actions",COMPILED_ACTION:"View Compiled Vega",EDITOR_ACTION:"Open in Vega Editor",PNG_ACTION:"Save as PNG",SOURCE_ACTION:"View Source",SVG_ACTION:"Save as SVG"},Ai={vega:"Vega","vega-lite":"Vega-Lite"},Rr={vega:ct.version,"vega-lite":Bi?Bi.version:"not available"},Z1={vega:e=>e,"vega-lite":(e,t)=>Bi.compile(e,{config:t}).spec},ew=`
<svg viewBox="0 0 16 16" fill="currentColor" stroke="none" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
  <circle r="2" cy="8" cx="2"></circle>
  <circle r="2" cy="8" cx="8"></circle>
  <circle r="2" cy="8" cx="14"></circle>
</svg>`,tw="chart-wrapper";function nw(e){return typeof e=="function"}function Ml(e,t,n,i){const r=`<html><head>${t}</head><body><pre><code class="json">`,s=`</code></pre>${n}</body></html>`,o=window.open("");o.document.write(r+e+s),o.document.title=`${Ai[i]} JSON Source`}function iw(e,t){if(e.$schema){const n=Gl(e.$schema);t&&t!==n.library&&console.warn(`The given visualization spec is written in ${Ai[n.library]}, but mode argument sets ${Ai[t]??t}.`);const i=n.library;return yp(Rr[i],`^${n.version.slice(1)}`)||console.warn(`The input spec uses ${Ai[i]} ${n.version}, but the current version of ${Ai[i]} is v${Rr[i]}.`),i}return"mark"in e||"encoding"in e||"layer"in e||"hconcat"in e||"vconcat"in e||"facet"in e||"repeat"in e?"vega-lite":"marks"in e||"signals"in e||"scales"in e||"axes"in e?"vega":t??"vega"}function xp(e){return!!(e&&"load"in e)}function Ul(e){return xp(e)?e:ct.loader(e)}function rw(e){var n;const t=((n=e.usermeta)==null?void 0:n.embedOptions)??{};return I(t.defaultStyle)&&(t.defaultStyle=!1),t}async function Hw(e,t,n={}){let i,r;I(t)?(r=Ul(n.loader),i=JSON.parse(await r.load(t))):i=t;const s=rw(i),o=s.loader;(!r||o)&&(r=Ul(n.loader??o));const a=await Bl(s,r),c=await Bl(n,r),l={...bp(c,a),config:Ir(c.config??{},a.config??{})};return await ow(e,i,l,r)}async function Bl(e,t){const n=I(e.config)?JSON.parse(await t.load(e.config)):e.config??{},i=I(e.patch)?JSON.parse(await t.load(e.patch)):e.patch;return{...e,...i?{patch:i}:{},...n?{config:n}:{}}}function sw(e){const t=e.getRootNode?e.getRootNode():document;return t instanceof ShadowRoot?{root:t,rootContainer:t}:{root:document,rootContainer:document.head??document.body}}async function ow(e,t,n={},i){const r=n.theme?Ir(N$[n.theme],n.config??{}):n.config,s=di(n.actions)?n.actions:bp({},Q1,n.actions??{}),o={...J1,...n.i18n},a=n.renderer??"canvas",c=n.logLevel??ct.Warn,l=n.downloadFileName??"visualization",u=typeof e=="string"?document.querySelector(e):e;if(!u)throw new Error(`${e} does not exist`);if(n.defaultStyle!==!1){const w="vega-embed-style",{root:F,rootContainer:A}=sw(u);if(!F.getElementById(w)){const M=document.createElement("style");M.id=w,M.innerHTML=n.defaultStyle===void 0||n.defaultStyle===!0?k1.toString():n.defaultStyle,A.appendChild(M)}}const f=iw(t,n.mode);let d=Z1[f](t,r);if(f==="vega-lite"&&d.$schema){const w=Gl(d.$schema);yp(Rr.vega,`^${w.version.slice(1)}`)||console.warn(`The compiled spec uses Vega ${w.version}, but current version is v${Rr.vega}.`)}u.classList.add("vega-embed"),s&&u.classList.add("has-actions"),u.innerHTML="";let g=u;if(s){const w=document.createElement("div");w.classList.add(tw),u.appendChild(w),g=w}const p=n.patch;if(p&&(d=p instanceof Function?p(d):Tp(d,p,!0,!1).newDocument),n.formatLocale&&ct.formatLocale(n.formatLocale),n.timeFormatLocale&&ct.timeFormatLocale(n.timeFormatLocale),n.expressionFunctions)for(const w in n.expressionFunctions){const F=n.expressionFunctions[w];"fn"in F?ct.expressionFunction(w,F.fn,F.visitor):F instanceof Function&&ct.expressionFunction(w,F)}const{ast:h}=n,m=ct.parse(d,f==="vega-lite"?{}:r,{ast:h}),y=new(n.viewClass||ct.View)(m,{loader:i,logLevel:c,renderer:a,...h?{expr:ct.expressionInterpreter??n.expr??Ap}:{}});if(y.addSignalListener("autosize",(w,F)=>{const{type:A}=F;A=="fit-x"?(g.classList.add("fit-x"),g.classList.remove("fit-y")):A=="fit-y"?(g.classList.remove("fit-x"),g.classList.add("fit-y")):A=="fit"?g.classList.add("fit-x","fit-y"):g.classList.remove("fit-x","fit-y")}),n.tooltip!==!1){const{loader:w,tooltip:F}=n,A=w&&!xp(w)?w==null?void 0:w.baseURL:void 0,M=nw(F)?F:new Op({baseURL:A,...F===!0?{}:F}).call;y.tooltip(M)}let{hover:b}=n;if(b===void 0&&(b=f==="vega"),b){const{hoverSet:w,updateSet:F}=typeof b=="boolean"?{}:b;y.hover(w,F)}n&&(n.width!=null&&y.width(n.width),n.height!=null&&y.height(n.height),n.padding!=null&&y.padding(n.padding)),await y.initialize(g,n.bind).runAsync();let C;if(s!==!1){let w=u;if(n.defaultStyle!==!1||n.forceActionsMenu){const A=document.createElement("details");A.title=o.CLICK_TO_VIEW_ACTIONS,u.append(A),w=A;const M=document.createElement("summary");M.innerHTML=ew,A.append(M),C=K=>{A.contains(K.target)||A.removeAttribute("open")},document.addEventListener("click",C)}const F=document.createElement("div");if(w.append(F),F.classList.add("vega-actions"),s===!0||s.export!==!1){for(const A of["svg","png"])if(s===!0||s.export===!0||s.export[A]){const M=o[`${A.toUpperCase()}_ACTION`],K=document.createElement("a"),we=X(n.scaleFactor)?n.scaleFactor[A]:n.scaleFactor;K.text=M,K.href="#",K.target="_blank",K.download=`${l}.${A}`,K.addEventListener("mousedown",async function(de){de.preventDefault();const st=await y.toImageURL(A,we);this.href=st}),F.append(K)}}if(s===!0||s.source!==!1){const A=document.createElement("a");A.text=o.SOURCE_ACTION,A.href="#",A.addEventListener("click",function(M){Ml(Fs(t),n.sourceHeader??"",n.sourceFooter??"",f),M.preventDefault()}),F.append(A)}if(f==="vega-lite"&&(s===!0||s.compiled!==!1)){const A=document.createElement("a");A.text=o.COMPILED_ACTION,A.href="#",A.addEventListener("click",function(M){Ml(Fs(d),n.sourceHeader??"",n.sourceFooter??"","vega"),M.preventDefault()}),F.append(A)}if(s===!0||s.editor!==!1){const A=n.editorUrl??"https://vega.github.io/editor/",M=document.createElement("a");M.text=o.EDITOR_ACTION,M.href="#",M.addEventListener("click",function(K){F1(window,A,{config:r,mode:f,renderer:a,spec:Fs(t)}),K.preventDefault()}),F.append(M)}}function D(){C&&document.removeEventListener("click",C),y.finalize()}return{view:y,spec:t,vgSpec:d,finalize:D,embedOptions:n}}export{Q1 as DEFAULT_ACTIONS,Hw as default,iw as guessMode,ct as vega,Bi as vegaLite,Gw as version};
//# sourceMappingURL=vega-embed.module.BU0P0uZZ.js.map
