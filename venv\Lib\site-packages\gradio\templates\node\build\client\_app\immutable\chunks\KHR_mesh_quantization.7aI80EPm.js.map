{"version": 3, "file": "KHR_mesh_quantization.7aI80EPm.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_mesh_quantization.js"], "sourcesContent": ["import { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"KHR_mesh_quantization\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_mesh_quantization/README.md)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_mesh_quantization {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        this.enabled = loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() { }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_mesh_quantization(loader));\n//# sourceMappingURL=KHR_mesh_quantization.js.map"], "names": ["NAME", "KHR_mesh_quantization", "loader", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "iDACA,MAAMA,EAAO,wBAKN,MAAMC,CAAsB,CAI/B,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EACZ,KAAK,QAAUE,EAAO,gBAAgBF,CAAI,CAC7C,CAED,SAAU,CAAG,CACjB,CACAG,EAAwBH,CAAI,EAC5BI,EAAsBJ,EAAM,GAAOE,GAAW,IAAID,EAAsBC,CAAM,CAAC", "x_google_ignoreList": [0]}