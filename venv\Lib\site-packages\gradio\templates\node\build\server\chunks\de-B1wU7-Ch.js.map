{"version": 3, "file": "de-B1wU7-Ch.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/de.js"], "sourcesContent": ["const e=\"Deutsch\",n={annotated_image:\"Annotiertes Bild\"},i={allow_recording_access:\"Bitte erlauben Sie den Zugriff auf das Mikrofon für die Aufnahme.\",audio:\"Audio\",record_from_microphone:\"Vom Mikrofon aufnehmen\",stop_recording:\"Aufnahme stoppen\",no_device_support:\"Auf Mediengeräte konnte nicht zugegriffen werden. <PERSON><PERSON><PERSON>, dass Sie sich auf einer sicheren Quelle (https) oder localhost befinden (oder ein gültiges SSL-Zertifikat an ssl_verify übergeben haben) und Sie dem Browser den Zugriff auf Ihr Gerät erlaubt haben.\",stop:\"Stopp\",resume:\"Fortsetzen\",record:\"Aufnehmen\",no_microphone:\"Kein Mikrofon gefunden\",pause:\"Pause\",play:\"Abspielen\",waiting:\"Warten\",drop_to_upload:\"Laden Sie eine Audiodatei hier ab, um sie hochzuladen.\"},r={connection_can_break:\"Auf Mobilgeräten kann die Verbindung unterbrochen werden, wenn dieser Tab den Fokus verliert oder das Gerät in den Ruhezustand geht, wodurch Ihre Position in der Warteschlange verloren geht.\",long_requests_queue:\"Es gibt eine lange Warteschlange ausstehender Anfragen. Duplizieren Sie diesen Space zum Überspringen.\",lost_connection:\"Verbindung durch Verlassen der Seite verloren. Kehre zur Warteschlange zurück...\",waiting_for_inputs:\"Warten auf das Hochladen der Datei(en), bitte versuchen Sie es erneut.\"},o={checkbox:\"Kontrollkästchen\",checkbox_group:\"Kontrollkästchengruppe\"},t={code:\"Code\"},a={color_picker:\"Farbwähler\"},d={built_with:\"erstellt mit\",built_with_gradio:\"Mit Gradio erstellt\",clear:\"Löschen\",download:\"Herunterladen\",edit:\"Bearbeiten\",empty:\"Leer\",error:\"Fehler\",hosted_on:\"Gehostet auf\",loading:\"Laden\",logo:\"Logo\",or:\"oder\",remove:\"Entfernen\",settings:\"Einstellungen\",share:\"Teilen\",submit:\"Absenden\",undo:\"Rückgängig\",no_devices:\"Keine Geräte gefunden\",language:\"Sprache\",display_theme:\"Anzeigedesign\",pwa:\"Progressive Web App\"},l={incorrect_format:\"Falsches Format, nur CSV- und TSV-Dateien werden unterstützt\",new_column:\"Spalte hinzufügen\",new_row:\"Neue Zeile\",add_row_above:\"Zeile oben hinzufügen\",add_row_below:\"Zeile unten hinzufügen\",add_column_left:\"Spalte links hinzufügen\",add_column_right:\"Spalte rechts hinzufügen\",delete_row:\"Zeile löschen\",delete_column:\"Spalte löschen\",sort_column:\"Spalte sortieren\",sort_ascending:\"Aufsteigend sortieren\",sort_descending:\"Absteigend sortieren\",drop_to_upload:\"CSV- oder TSV-Dateien hier ablegen, um Daten in den DataFrame zu importieren.\",clear_sort:\"Sortierung aufheben\"},s={dropdown:\"Dropdown-Menü\"},c={build_error:\"Es gibt einen Build-Fehler\",config_error:\"Es gibt einen Konfigurationsfehler\",contact_page_author:\"Bitte kontaktieren Sie den Autor der Seite.\",no_app_file:\"Es gibt keine App-Datei\",runtime_error:\"Es gibt einen Laufzeitfehler\",space_not_working:'\"Space funktioniert nicht, weil\" {0}',space_paused:\"Der Space ist pausiert\",use_via_api:\"Über API verwenden\"},u={uploading:\"Hochladen...\"},h={highlighted_text:\"Hervorgehobener Text\"},g={allow_webcam_access:\"Bitte erlauben Sie den Zugriff auf die Webcam für die Aufnahme.\",brush_color:\"Pinselfarbe\",brush_radius:\"Pinselgröße\",image:\"Bild\",remove_image:\"Bild entfernen\",select_brush_color:\"Pinselfarbe auswählen\",start_drawing:\"Zeichnen beginnen\",use_brush:\"Pinsel verwenden\",drop_to_upload:\"Laden Sie ein Bild hier ab, um es hochzuladen.\"},_={label:\"Beschriftung\"},b={enable_cookies:\"Wenn Sie einen HuggingFace Space im Inkognito-Modus besuchen, müssen Sie Cookies von Drittanbietern aktivieren.\",incorrect_credentials:\"Falsche Anmeldedaten\",username:\"Benutzername\",password:\"Passwort\",login:\"Anmelden\"},p={number:\"Zahl\"},m={plot:\"Diagramm\"},f={radio:\"Optionsfeld\"},S={slider:\"Schieberegler\"},w={click_to_upload:\"Hochladen\",drop_audio:\"Audio hier ablegen\",drop_csv:\"CSV Datei hier ablegen\",drop_file:\"Datei hier ablegen\",drop_image:\"Bild hier ablegen\",drop_video:\"Video hier ablegen\",drop_gallery:\"Medien hier ablegen\",paste_clipboard:\"Aus Zwischenablage einfügen\"},k={drop_to_upload:\"Laden Sie eine Video-Datei hier ab, um sie hochzuladen.\"},A={edit:\"Bearbeiten\",retry:\"Wiederholen\",undo:\"Rückgängig\",submit:\"Senden\",cancel:\"Abbrechen\",like:\"Gefällt mir\",dislike:\"Gefällt mir nicht\",clear:\"Chat leeren\"},v={_name:e,\"3D_model\":{\"3d_model\":\"3D-Modell\",drop_to_upload:\"Laden Sie eine 3D-Modell-Datei (.obj, .glb, .stl, .gltf, .splat oder .ply) hier ab, um sie hochzuladen.\"},annotated_image:n,audio:i,blocks:r,checkbox:o,code:t,color_picker:a,common:d,dataframe:l,dropdown:s,errors:c,file:u,highlighted_text:h,image:g,label:_,login:b,number:p,plot:m,radio:f,slider:S,upload_text:w,video:k,chatbot:A};export{e as _name,n as annotated_image,i as audio,r as blocks,A as chatbot,o as checkbox,t as code,a as color_picker,d as common,l as dataframe,v as default,s as dropdown,c as errors,u as file,h as highlighted_text,g as image,_ as label,b as login,p as number,m as plot,f as radio,S as slider,w as upload_text,k as video};\n//# sourceMappingURL=de.js.map\n"], "names": [], "mappings": "AAAK,MAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,mEAAmE,CAAC,KAAK,CAAC,OAAO,CAAC,sBAAsB,CAAC,wBAAwB,CAAC,cAAc,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,6QAA6Q,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,wBAAwB,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,wDAAwD,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,gMAAgM,CAAC,mBAAmB,CAAC,wGAAwG,CAAC,eAAe,CAAC,kFAAkF,CAAC,kBAAkB,CAAC,wEAAwE,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC,cAAc,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,cAAc,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,uBAAuB,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,eAAe,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,8DAA8D,CAAC,UAAU,CAAC,mBAAmB,CAAC,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC,uBAAuB,CAAC,aAAa,CAAC,wBAAwB,CAAC,eAAe,CAAC,yBAAyB,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,UAAU,CAAC,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC,WAAW,CAAC,kBAAkB,CAAC,cAAc,CAAC,uBAAuB,CAAC,eAAe,CAAC,sBAAsB,CAAC,cAAc,CAAC,+EAA+E,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,4BAA4B,CAAC,YAAY,CAAC,oCAAoC,CAAC,mBAAmB,CAAC,6CAA6C,CAAC,WAAW,CAAC,yBAAyB,CAAC,aAAa,CAAC,8BAA8B,CAAC,iBAAiB,CAAC,sCAAsC,CAAC,YAAY,CAAC,wBAAwB,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,iEAAiE,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,uBAAuB,CAAC,aAAa,CAAC,mBAAmB,CAAC,SAAS,CAAC,kBAAkB,CAAC,cAAc,CAAC,gDAAgD,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,iHAAiH,CAAC,qBAAqB,CAAC,sBAAsB,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,UAAU,CAAC,oBAAoB,CAAC,QAAQ,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,UAAU,CAAC,mBAAmB,CAAC,UAAU,CAAC,oBAAoB,CAAC,YAAY,CAAC,qBAAqB,CAAC,eAAe,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,yDAAyD,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,cAAc,CAAC,yGAAyG,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;;;;"}