import{SvelteComponent as O,init as Q,safe_not_equal as U,create_slot as W,element as M,space as A,claim_element as S,children as N,get_svelte_dataset as y,claim_space as F,detach as E,attr as v,toggle_class as w,insert_hydration as C,append_hydration as T,listen as $,update_slot_base as G,get_all_dirty_from_scope as J,get_slot_changes as K,transition_in as g,transition_out as b,createEventDispatcher as x,onMount as ee,binding_callbacks as I,assign as te,create_component as B,empty as P,claim_component as D,mount_component as R,get_spread_update as ne,get_spread_object as ie,group_outros as se,check_outros as oe,destroy_component as H,bind as V,add_flush_callback as j}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{S as le,z as ae}from"./2.B2AoQPnG.js";function re(s){let e,n,o='<div class="chevron svelte-1hez9vf"><span class="chevron-left svelte-1hez9vf"></span></div>',l,a,_,i,t,r;const d=s[10].default,f=W(d,s,s[9],null);return{c(){e=M("div"),n=M("button"),n.innerHTML=o,l=A(),a=M("div"),f&&f.c(),this.h()},l(u){e=S(u,"DIV",{class:!0,style:!0});var p=N(e);n=S(p,"BUTTON",{class:!0,"aria-label":!0,"data-svelte-h":!0}),y(n)!=="svelte-12xdlyi"&&(n.innerHTML=o),l=F(p),a=S(p,"DIV",{class:!0});var h=N(a);f&&f.l(h),h.forEach(E),p.forEach(E),this.h()},h(){v(n,"class","toggle-button svelte-1hez9vf"),v(n,"aria-label","Toggle Sidebar"),v(a,"class","sidebar-content svelte-1hez9vf"),v(e,"class","sidebar svelte-1hez9vf"),v(e,"style",_="width: "+s[6]+"; "+s[1]+": calc("+s[6]+" * -1)"),w(e,"open",s[2]),w(e,"right",s[1]==="right"),w(e,"reduce-motion",s[4])},m(u,p){C(u,e,p),T(e,n),T(e,l),T(e,a),f&&f.m(a,null),s[12](e),i=!0,t||(r=$(n,"click",s[11]),t=!0)},p(u,[p]){f&&f.p&&(!i||p&512)&&G(f,d,u,u[9],i?K(d,u[9],p,null):J(u[9]),null),(!i||p&2&&_!==(_="width: "+u[6]+"; "+u[1]+": calc("+u[6]+" * -1)"))&&v(e,"style",_),(!i||p&4)&&w(e,"open",u[2]),(!i||p&2)&&w(e,"right",u[1]==="right"),(!i||p&16)&&w(e,"reduce-motion",u[4])},i(u){i||(g(f,u),i=!0)},o(u){b(f,u),i=!1},d(u){u&&E(e),f&&f.d(u),s[12](null),t=!1,r()}}}function ue(s,e,n){let{$$slots:o={},$$scope:l}=e;const a=x();let{open:_=!0}=e,{width:i}=e,{position:t="left"}=e,r=!1,d=!1,f,u=0,p=typeof i=="number"?`${i}px`:i,h;function c(){var z;if(!f.closest(".wrap"))return;const m=(z=f.closest(".wrap"))==null?void 0:z.getBoundingClientRect();if(!m)return;const k=f.getBoundingClientRect(),L=t==="left"?m.left:window.innerWidth-m.right;u=Math.max(0,k.width-L+30)}ee(()=>{var z;(z=f.closest(".wrap"))==null||z.classList.add("sidebar-parent"),c(),window.addEventListener("resize",c),(()=>{document.documentElement.style.setProperty("--overlap-amount",`${u}px`)})(),n(8,r=!0);const k=window.matchMedia("(prefers-reduced-motion: reduce)");n(4,h=k.matches);const L=Z=>{n(4,h=Z.matches)};return k.addEventListener("change",L),()=>{window.removeEventListener("resize",c),k.removeEventListener("change",L)}});const X=()=>{n(2,d=!d),n(0,_=d),a(d?"expand":"collapse")};function Y(m){I[m?"unshift":"push"](()=>{f=m,n(3,f)})}return s.$$set=m=>{"open"in m&&n(0,_=m.open),"width"in m&&n(7,i=m.width),"position"in m&&n(1,t=m.position),"$$scope"in m&&n(9,l=m.$$scope)},s.$$.update=()=>{s.$$.dirty&257&&r&&n(2,d=_)},[_,t,d,f,h,a,p,i,r,l,o,X,Y]}class fe extends O{constructor(e){super(),Q(this,e,ue,re,U,{open:0,width:7,position:1})}}function q(s){let e,n,o,l;function a(t){s[7](t)}function _(t){s[8](t)}let i={width:s[4],$$slots:{default:[de]},$$scope:{ctx:s}};return s[0]!==void 0&&(i.open=s[0]),s[1]!==void 0&&(i.position=s[1]),e=new fe({props:i}),I.push(()=>V(e,"open",a)),I.push(()=>V(e,"position",_)),e.$on("expand",s[9]),e.$on("collapse",s[10]),{c(){B(e.$$.fragment)},l(t){D(e.$$.fragment,t)},m(t,r){R(e,t,r),l=!0},p(t,r){const d={};r&16&&(d.width=t[4]),r&2048&&(d.$$scope={dirty:r,ctx:t}),!n&&r&1&&(n=!0,d.open=t[0],j(()=>n=!1)),!o&&r&2&&(o=!0,d.position=t[1],j(()=>o=!1)),e.$set(d)},i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){H(e,t)}}}function ce(s){let e;const n=s[6].default,o=W(n,s,s[11],null);return{c(){o&&o.c()},l(l){o&&o.l(l)},m(l,a){o&&o.m(l,a),e=!0},p(l,a){o&&o.p&&(!e||a&2048)&&G(o,n,l,l[11],e?K(n,l[11],a,null):J(l[11]),null)},i(l){e||(g(o,l),e=!0)},o(l){b(o,l),e=!1},d(l){o&&o.d(l)}}}function de(s){let e,n;return e=new ae({props:{$$slots:{default:[ce]},$$scope:{ctx:s}}}),{c(){B(e.$$.fragment)},l(o){D(e.$$.fragment,o)},m(o,l){R(e,o,l),n=!0},p(o,l){const a={};l&2048&&(a.$$scope={dirty:l,ctx:o}),e.$set(a)},i(o){n||(g(e.$$.fragment,o),n=!0)},o(o){b(e.$$.fragment,o),n=!1},d(o){H(e,o)}}}function _e(s){let e,n,o,l;const a=[{autoscroll:s[3].autoscroll},{i18n:s[3].i18n},s[2]];let _={};for(let t=0;t<a.length;t+=1)_=te(_,a[t]);e=new le({props:_});let i=s[5]&&q(s);return{c(){B(e.$$.fragment),n=A(),i&&i.c(),o=P()},l(t){D(e.$$.fragment,t),n=F(t),i&&i.l(t),o=P()},m(t,r){R(e,t,r),C(t,n,r),i&&i.m(t,r),C(t,o,r),l=!0},p(t,[r]){const d=r&12?ne(a,[r&8&&{autoscroll:t[3].autoscroll},r&8&&{i18n:t[3].i18n},r&4&&ie(t[2])]):{};e.$set(d),t[5]?i?(i.p(t,r),r&32&&g(i,1)):(i=q(t),i.c(),g(i,1),i.m(o.parentNode,o)):i&&(se(),b(i,1,1,()=>{i=null}),oe())},i(t){l||(g(e.$$.fragment,t),g(i),l=!0)},o(t){b(e.$$.fragment,t),b(i),l=!1},d(t){t&&(E(n),E(o)),H(e,t),i&&i.d(t)}}}function pe(s,e,n){let{$$slots:o={},$$scope:l}=e,{open:a=!0}=e,{position:_="left"}=e,{loading_status:i}=e,{gradio:t}=e,{width:r}=e,{visible:d=!0}=e;function f(c){a=c,n(0,a)}function u(c){_=c,n(1,_)}const p=()=>t.dispatch("expand"),h=()=>t.dispatch("collapse");return s.$$set=c=>{"open"in c&&n(0,a=c.open),"position"in c&&n(1,_=c.position),"loading_status"in c&&n(2,i=c.loading_status),"gradio"in c&&n(3,t=c.gradio),"width"in c&&n(4,r=c.width),"visible"in c&&n(5,d=c.visible),"$$scope"in c&&n(11,l=c.$$scope)},[a,_,i,t,r,d,o,f,u,p,h,l]}class be extends O{constructor(e){super(),Q(this,e,pe,_e,U,{open:0,position:1,loading_status:2,gradio:3,width:4,visible:5})}}export{be as default};
//# sourceMappingURL=Index.B_t3vyeP.js.map
