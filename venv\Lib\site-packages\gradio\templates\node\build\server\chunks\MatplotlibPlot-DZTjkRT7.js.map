{"version": 3, "file": "MatplotlibPlot-DZTjkRT7.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/MatplotlibPlot.js"], "sourcesContent": ["import{create_ssr_component as a,add_attribute as l}from\"svelte/internal\";const n={code:\".layout.svelte-j1jcu3.svelte-j1jcu3{display:flex;flex-direction:column;justify-content:center;align-items:center;width:var(--size-full);height:var(--size-full);color:var(--body-text-color)}.matplotlib.svelte-j1jcu3 img.svelte-j1jcu3{object-fit:contain}\",map:'{\"version\":3,\"file\":\"MatplotlibPlot.svelte\",\"sources\":[\"MatplotlibPlot.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">export let value;\\\\n$: plot = value?.plot;\\\\n<\\/script>\\\\n\\\\n<div data-testid={\\\\\"matplotlib\\\\\"} class=\\\\\"matplotlib layout\\\\\">\\\\n\\\\t<img\\\\n\\\\t\\\\tsrc={plot}\\\\n\\\\t\\\\talt={`${value.chart} plot visualising provided data`}\\\\n\\\\t\\\\ton:load\\\\n\\\\t/>\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.layout {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t}\\\\n\\\\t.matplotlib img {\\\\n\\\\t\\\\tobject-fit: contain;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAaC,mCAAQ,CACP,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CACA,yBAAW,CAAC,iBAAI,CACf,UAAU,CAAE,OACb\"}'},C=a((o,A,e,s)=>{let i,{value:t}=A;return A.value===void 0&&e.value&&t!==void 0&&e.value(t),o.css.add(n),i=t?.plot,`<div${l(\"data-testid\",\"matplotlib\",0)} class=\"matplotlib layout svelte-j1jcu3\"><img${l(\"src\",i,0)}${l(\"alt\",`${t.chart} plot visualising provided data`,0)} class=\"svelte-j1jcu3\"> </div>`});export{C as default};\n//# sourceMappingURL=MatplotlibPlot.js.map\n"], "names": ["a", "l"], "mappings": ";;AAA+E,MAAC,CAAC,CAAC,CAAC,IAAI,CAAC,8PAA8P,CAAC,GAAG,CAAC,g8BAAg8B,CAAC,CAAC,CAAC,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,IAAI,EAAEC,aAAC,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,6CAA6C,EAAEA,aAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,aAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC;;;;"}