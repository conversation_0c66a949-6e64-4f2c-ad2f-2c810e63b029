import{c as i}from"./declarationMapper-BZjsjg7g.js";import{f as o}from"./KHR_interactivity-DTxiAnOo.js";import{R as r}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class e extends o{constructor(t){super(t),this.condition=this.registerDataInput("condition",i),this.onTrue=this._registerSignalOutput("onTrue"),this.onFalse=this._registerSignalOutput("onFalse")}_execute(t){this.condition.getValue(t)?this.onTrue._activateSignal(t):this.onFalse._activateSignal(t)}getClassName(){return"FlowGraphBranchBlock"}}r("FlowGraphBranchBlock",e);export{e as FlowGraphBranchBlock};
//# sourceMappingURL=flowGraphBranchBlock-_Nco8L2d.js.map
