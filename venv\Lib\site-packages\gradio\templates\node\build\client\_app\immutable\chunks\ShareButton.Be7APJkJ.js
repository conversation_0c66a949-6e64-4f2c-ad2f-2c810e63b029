import{SvelteComponent as l,init as u,safe_not_equal as h,create_component as g,claim_component as _,mount_component as d,transition_in as p,transition_out as S,destroy_component as b,createEventDispatcher as y}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{I as C,h as I}from"./2.B2AoQPnG.js";import{C as k}from"./Community.i_uzCNAp.js";function v(i){let t,r;return t=new C({props:{Icon:k,label:i[2]("common.share"),pending:i[3]}}),t.$on("click",i[5]),{c(){g(t.$$.fragment)},l(e){_(t.$$.fragment,e)},m(e,o){d(t,e,o),r=!0},p(e,[o]){const a={};o&4&&(a.label=e[2]("common.share")),o&8&&(a.pending=e[3]),t.$set(a)},i(e){r||(p(t.$$.fragment,e),r=!0)},o(e){S(t.$$.fragment,e),r=!1},d(e){b(t,e)}}}function w(i,t,r){const e=y();let{formatter:o}=t,{value:a}=t,{i18n:m}=t,c=!1;const s=async()=>{try{r(3,c=!0);const n=await o(a);e("share",{description:n})}catch(n){console.error(n);let f=n instanceof I?n.message:"Share failed.";e("error",f)}finally{r(3,c=!1)}};return i.$$set=n=>{"formatter"in n&&r(0,o=n.formatter),"value"in n&&r(1,a=n.value),"i18n"in n&&r(2,m=n.i18n)},[o,a,m,c,e,s]}class j extends l{constructor(t){super(),u(this,t,w,v,h,{formatter:0,value:1,i18n:2})}}export{j as S};
//# sourceMappingURL=ShareButton.Be7APJkJ.js.map
