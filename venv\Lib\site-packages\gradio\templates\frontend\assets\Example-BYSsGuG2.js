import"./index-B7J2Z2jS.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import{I as a}from"./Image-CnqB5dbD.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";/* empty css                                                   */import"./ImageUploader-CGsNpf2-.js";import"./StreamingBar.svelte_svelte_type_style_lang-DyfAPhwM.js";/* empty css                                              */import"./svelte/svelte.js";import"./prism-python-MMh3z1bK.js";import"./file-url-DoxvUUVV.js";import"./BlockLabel-3KxTaaiM.js";import"./IconButton-C_HS7fTi.js";import"./Clear-By3xiIwg.js";import"./Image-Bsh8Umrh.js";import"./SelectSource-CVJruJ8L.js";import"./Upload-D4uXt6Nz.js";/* empty css                                             */import"./IconButtonWrapper--EIOWuEM.js";import"./FullscreenButton-jgOGhOHz.js";import"./utils-Gtzs_Zla.js";import"./DropdownArrow-DYWFcSFn.js";import"./Square-oAGqOwsh.js";import"./index-CEGzm7H5.js";import"./StreamingBar-JqJtcvLZ.js";const{SvelteComponent:u,attr:p,create_component:c,destroy_component:f,detach:g,element:_,flush:m,init:d,insert:h,mount_component:v,safe_not_equal:y,toggle_class:s,transition_in:b,transition_out:w}=window.__gradio__svelte__internal;function $(n){let e,r,i;return r=new a({props:{src:n[0].composite?.url||n[0].background?.url,alt:""}}),{c(){e=_("div"),c(r.$$.fragment),p(e,"class","container svelte-jhlhb0"),s(e,"table",n[1]==="table"),s(e,"gallery",n[1]==="gallery"),s(e,"selected",n[2])},m(t,l){h(t,e,l),v(r,e,null),i=!0},p(t,[l]){const o={};l&1&&(o.src=t[0].composite?.url||t[0].background?.url),r.$set(o),(!i||l&2)&&s(e,"table",t[1]==="table"),(!i||l&2)&&s(e,"gallery",t[1]==="gallery"),(!i||l&4)&&s(e,"selected",t[2])},i(t){i||(b(r.$$.fragment,t),i=!0)},o(t){w(r.$$.fragment,t),i=!1},d(t){t&&g(e),f(r)}}}function k(n,e,r){let{value:i}=e,{type:t}=e,{selected:l=!1}=e;return n.$$set=o=>{"value"in o&&r(0,i=o.value),"type"in o&&r(1,t=o.type),"selected"in o&&r(2,l=o.selected)},[i,t,l]}class X extends u{constructor(e){super(),d(this,e,k,$,y,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),m()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),m()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),m()}}export{X as default};
//# sourceMappingURL=Example-BYSsGuG2.js.map
