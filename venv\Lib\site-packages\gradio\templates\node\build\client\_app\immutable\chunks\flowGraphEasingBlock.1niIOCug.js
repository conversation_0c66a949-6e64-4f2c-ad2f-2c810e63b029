import{R as h,aA as o,aB as E,aC as w,aD as d,aE as m,aF as B,aG as n}from"./index.BoI39RQH.js";import{F as C}from"./KHR_interactivity.DEAVS2UW.js";import{R as i,b as f}from"./declarationMapper.UBCwU7BT.js";var l;(function(e){e[e.CircleEase=0]="CircleEase",e[e.BackEase=1]="BackEase",e[e.BounceEase=2]="BounceEase",e[e.CubicEase=3]="CubicEase",e[e.ElasticEase=4]="ElasticEase",e[e.ExponentialEase=5]="ExponentialEase",e[e.PowerEase=6]="PowerEase",e[e.QuadraticEase=7]="QuadraticEase",e[e.QuarticEase=8]="QuarticEase",e[e.QuinticEase=9]="QuinticEase",e[e.SineEase=10]="SineEase",e[e.BezierCurveEase=11]="BezierCurveEase"})(l||(l={}));function k(e,...s){switch(e){case 11:return new n(...s);case 0:return new B;case 1:return new m(...s);case 2:return new d(...s);case 3:return new w;case 4:return new E(...s);case 5:return new o(...s);default:throw new Error("Easing type not yet implemented")}}class Q extends C{constructor(s){super(s),this.config=s,this._easingFunctions={},this.type=this.registerDataInput("type",i,11),this.mode=this.registerDataInput("mode",f,0),this.parameters=this.registerDataInput("parameters",i,[1,0,0,1]),this.easingFunction=this.registerDataOutput("easingFunction",i)}_updateOutputs(s){const a=this.type.getValue(s),t=this.mode.getValue(s),u=this.parameters.getValue(s);if(a===void 0||t===void 0)return;const r=`${a}-${t}-${u.join("-")}`;if(!this._easingFunctions[r]){const c=k(a,...u);c.setEasingMode(t),this._easingFunctions[r]=c}this.easingFunction.setValue(this._easingFunctions[r],s)}getClassName(){return"FlowGraphEasingBlock"}}h("FlowGraphEasingBlock",Q);export{l as EasingFunctionType,Q as FlowGraphEasingBlock};
//# sourceMappingURL=flowGraphEasingBlock.1niIOCug.js.map
