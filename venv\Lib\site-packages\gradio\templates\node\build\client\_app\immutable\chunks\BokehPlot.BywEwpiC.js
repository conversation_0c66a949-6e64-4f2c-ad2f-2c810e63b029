import{SvelteComponent as j,init as B,safe_not_equal as C,element as D,claim_element as S,children as x,detach as y,attr as l,insert_hydration as I,noop as m,create<PERSON><PERSON><PERSON><PERSON>patcher as <PERSON>,onD<PERSON><PERSON> as q}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function M(i){let t;return{c(){t=D("div"),this.h()},l(o){t=S(o,"DIV",{"data-testid":!0,id:!0,class:!0}),x(t).forEach(y),this.h()},h(){l(t,"data-testid","bokeh"),l(t,"id",i[0]),l(t,"class","gradio-bokeh svelte-1rhu6ax")},m(o,s){I(o,t,s)},p:m,i:m,o:m,d(o){o&&y(t)}}}function O(i,t,o){let s,{value:a}=t,{bokeh_version:n}=t;const c=`bokehDiv-${Math.random().toString(5).substring(2)}`,g=P();async function v(e){if(document&&document.getElementById(c)&&(document.getElementById(c).innerHTML=""),window.Bokeh){_();let h=JSON.parse(e);(await window.Bokeh.embed.embed_item(h,c))._roots.forEach(async r=>{await r.ready,g("load")})}}const u=`https://cdn.bokeh.org/bokeh/release/bokeh-${n}.min.js`,w=[`https://cdn.pydata.org/bokeh/release/bokeh-widgets-${n}.min.js`,`https://cdn.pydata.org/bokeh/release/bokeh-tables-${n}.min.js`,`https://cdn.pydata.org/bokeh/release/bokeh-gl-${n}.min.js`,`https://cdn.pydata.org/bokeh/release/bokeh-api-${n}.min.js`];let d=!1;async function E(){await Promise.all(w.map((e,h)=>new Promise(f=>{const r=document.createElement("script");return r.onload=f,r.src=e,document.head.appendChild(r),r}))),o(3,d=!0)}let p=[];function b(){p=E()}function _(){const e=document.createElement("script");return e.onload=b,e.src=u,document.head.querySelector(`script[src="${u}"]`)?b():document.head.appendChild(e),e}const k=n?_():null;return q(()=>{k in document.children&&(document.removeChild(k),p.forEach(e=>document.removeChild(e)))}),i.$$set=e=>{"value"in e&&o(1,a=e.value),"bokeh_version"in e&&o(2,n=e.bokeh_version)},i.$$.update=()=>{i.$$.dirty&2&&o(4,s=a==null?void 0:a.plot),i.$$.dirty&24&&d&&v(s)},[c,a,n,d,s]}class L extends j{constructor(t){super(),B(this,t,O,M,C,{value:1,bokeh_version:2})}}export{L as default};
//# sourceMappingURL=BokehPlot.BywEwpiC.js.map
