import { c as create_ssr_component, b as create<PERSON>ventDispatcher, o as on<PERSON><PERSON><PERSON>, v as validate_component, e as escape, d as add_attribute, h as add_styles, j as null_to_empty, f as each } from './ssr-C3HYbsxA.js';
import { h as he, g as ge } from './ModifyUpload-wYzp15o5.js';
import { e as bt, p as ee, c as KA, a9 as ze, aa as ke, ab as la } from './2-DJbI4FWc.js';
import { Y as Yt } from './AudioPlayer-CFZK-O44.js';

function N(A,t,e,o){return new(e||(e=Promise))(function(n,r){function l(a){try{i(o.next(a));}catch(c){r(c);}}function s(a){try{i(o.throw(a));}catch(c){r(c);}}function i(a){var c;a.done?n(a.value):(c=a.value,c instanceof e?c:new e(function(C){C(c);})).then(l,s);}i((o=o.apply(A,[])).next());})}class yt{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener;}addEventListener(t,e,o){if(this.listeners[t]||(this.listeners[t]=new Set),this.listeners[t].add(e),o?.once){const n=()=>{this.removeEventListener(t,n),this.removeEventListener(t,e);};return this.addEventListener(t,n),n}return ()=>this.removeEventListener(t,e)}removeEventListener(t,e){var o;(o=this.listeners[t])===null||o===void 0||o.delete(e);}once(t,e){return this.on(t,e,{once:!0})}unAll(){this.listeners={};}emit(t,...e){this.listeners[t]&&this.listeners[t].forEach(o=>o(...e));}}class xt extends yt{constructor(t){super(),this.subscriptions=[],this.options=t;}onInit(){}init(t){this.wavesurfer=t,this.onInit();}destroy(){this.emit("destroy"),this.subscriptions.forEach(t=>t());}}const Mt=["audio/webm","audio/wav","audio/mpeg","audio/mp4","audio/mp3"];class Z extends xt{constructor(t){var e;super(Object.assign(Object.assign({},t),{audioBitsPerSecond:(e=t.audioBitsPerSecond)!==null&&e!==void 0?e:128e3})),this.stream=null,this.mediaRecorder=null;}static create(t){return new Z(t||{})}renderMicStream(t){const e=new AudioContext,o=e.createMediaStreamSource(t),n=e.createAnalyser();o.connect(n);const r=n.frequencyBinCount,l=new Float32Array(r),s=r/e.sampleRate;let i;const a=()=>{n.getFloatTimeDomainData(l),this.wavesurfer&&(this.wavesurfer.options.cursorWidth=0,this.wavesurfer.options.interact=!1,this.wavesurfer.load("",[l],s)),i=requestAnimationFrame(a);};return a(),()=>{cancelAnimationFrame(i),o?.disconnect(),e?.close();}}startMic(t){return N(this,void 0,void 0,function*(){let e;try{e=yield navigator.mediaDevices.getUserMedia({audio:!t?.deviceId||{deviceId:t.deviceId}});}catch(n){throw new Error("Error accessing the microphone: "+n.message)}const o=this.renderMicStream(e);return this.subscriptions.push(this.once("destroy",o)),this.stream=e,e})}stopMic(){this.stream&&(this.stream.getTracks().forEach(t=>t.stop()),this.stream=null,this.mediaRecorder=null);}startRecording(t){return N(this,void 0,void 0,function*(){const e=this.stream||(yield this.startMic(t)),o=this.mediaRecorder||new MediaRecorder(e,{mimeType:this.options.mimeType||Mt.find(r=>MediaRecorder.isTypeSupported(r)),audioBitsPerSecond:this.options.audioBitsPerSecond});this.mediaRecorder=o,this.stopRecording();const n=[];o.ondataavailable=r=>{r.data.size>0&&n.push(r.data);},o.onstop=()=>{var r;const l=new Blob(n,{type:o.mimeType});this.emit("record-end",l),this.options.renderRecordedAudio!==!1&&((r=this.wavesurfer)===null||r===void 0||r.load(URL.createObjectURL(l)));},o.start(),this.emit("record-start");})}isRecording(){var t;return ((t=this.mediaRecorder)===null||t===void 0?void 0:t.state)==="recording"}isPaused(){var t;return ((t=this.mediaRecorder)===null||t===void 0?void 0:t.state)==="paused"}stopRecording(){var t;this.isRecording()&&((t=this.mediaRecorder)===null||t===void 0||t.stop());}pauseRecording(){var t;this.isRecording()&&((t=this.mediaRecorder)===null||t===void 0||t.pause(),this.emit("record-pause"));}resumeRecording(){var t;this.isPaused()&&((t=this.mediaRecorder)===null||t===void 0||t.resume(),this.emit("record-resume"));}static getAvailableAudioDevices(){return N(this,void 0,void 0,function*(){return navigator.mediaDevices.enumerateDevices().then(t=>t.filter(e=>e.kind==="audioinput"))})}destroy(){super.destroy(),this.stopRecording(),this.stopMic();}}const kt={code:".mic-select.svelte-1ya9x7a{height:var(--size-8);background:var(--block-background-fill);padding:0px var(--spacing-xxl);border-radius:var(--button-large-radius);font-size:var(--text-md);border:1px solid var(--block-border-color);gap:var(--size-1)}select.svelte-1ya9x7a{text-overflow:ellipsis;max-width:var(--size-40)}@media(max-width: 375px){select.svelte-1ya9x7a{width:100%}}",map:'{"version":3,"file":"DeviceSelect.svelte","sources":["DeviceSelect.svelte"],"sourcesContent":["<script lang=\\"ts\\">import RecordPlugin from \\"wavesurfer.js/dist/plugins/record.js\\";\\nimport { createEventDispatcher } from \\"svelte\\";\\nexport let i18n;\\nexport let micDevices = [];\\nconst dispatch = createEventDispatcher();\\n$: if (typeof window !== \\"undefined\\") {\\n    try {\\n        let tempDevices = [];\\n        RecordPlugin.getAvailableAudioDevices().then((devices) => {\\n            micDevices = devices;\\n            devices.forEach((device) => {\\n                if (device.deviceId) {\\n                    tempDevices.push(device);\\n                }\\n            });\\n            micDevices = tempDevices;\\n        });\\n    }\\n    catch (err) {\\n        if (err instanceof DOMException && err.name == \\"NotAllowedError\\") {\\n            dispatch(\\"error\\", i18n(\\"audio.allow_recording_access\\"));\\n        }\\n        throw err;\\n    }\\n}\\n<\/script>\\n\\n<select\\n\\tclass=\\"mic-select\\"\\n\\taria-label=\\"Select input device\\"\\n\\tdisabled={micDevices.length === 0}\\n>\\n\\t{#if micDevices.length === 0}\\n\\t\\t<option value=\\"\\">{i18n(\\"audio.no_microphone\\")}</option>\\n\\t{:else}\\n\\t\\t{#each micDevices as micDevice}\\n\\t\\t\\t<option value={micDevice.deviceId}>{micDevice.label}</option>\\n\\t\\t{/each}\\n\\t{/if}\\n</select>\\n\\n<style>\\n\\t.mic-select {\\n\\t\\theight: var(--size-8);\\n\\t\\tbackground: var(--block-background-fill);\\n\\t\\tpadding: 0px var(--spacing-xxl);\\n\\t\\tborder-radius: var(--button-large-radius);\\n\\t\\tfont-size: var(--text-md);\\n\\t\\tborder: 1px solid var(--block-border-color);\\n\\t\\tgap: var(--size-1);\\n\\t}\\n\\n\\tselect {\\n\\t\\ttext-overflow: ellipsis;\\n\\t\\tmax-width: var(--size-40);\\n\\t}\\n\\n\\t@media (max-width: 375px) {\\n\\t\\tselect {\\n\\t\\t\\twidth: 100%;\\n\\t\\t}\\n\\t}</style>\\n"],"names":[],"mappings":"AA0CC,0BAAY,CACX,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,UAAU,CAAE,IAAI,uBAAuB,CAAC,CACxC,OAAO,CAAE,GAAG,CAAC,IAAI,aAAa,CAAC,CAC/B,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CAC3C,GAAG,CAAE,IAAI,QAAQ,CAClB,CAEA,qBAAO,CACN,aAAa,CAAE,QAAQ,CACvB,SAAS,CAAE,IAAI,SAAS,CACzB,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,qBAAO,CACN,KAAK,CAAE,IACR,CACD"}'},at=create_ssr_component((A,t,e,o)=>{let{i18n:n}=t,{micDevices:r=[]}=t;const l=createEventDispatcher();if(t.i18n===void 0&&e.i18n&&n!==void 0&&e.i18n(n),t.micDevices===void 0&&e.micDevices&&r!==void 0&&e.micDevices(r),A.css.add(kt),typeof window<"u")try{let s=[];Z.getAvailableAudioDevices().then(i=>{r=i,i.forEach(a=>{a.deviceId&&s.push(a);}),r=s;});}catch(s){throw s instanceof DOMException&&s.name=="NotAllowedError"&&l("error",n("audio.allow_recording_access")),s}return `<select class="mic-select svelte-1ya9x7a" aria-label="Select input device" ${r.length===0?"disabled":""}>${r.length===0?`<option value="">${escape(n("audio.no_microphone"))}</option>`:`${each(r,s=>`<option${add_attribute("value",s.deviceId,0)}>${escape(s.label)}</option>`)}`}</select>`});const St={code:".microphone.svelte-9n45fh{width:100%;display:none}.component-wrapper.svelte-9n45fh{padding:var(--size-3);width:100%}.timestamps.svelte-9n45fh{display:flex;justify-content:space-between;align-items:center;width:100%;padding:var(--size-1) 0;margin:var(--spacing-md) 0}.time.svelte-9n45fh{color:var(--neutral-400)}.duration.svelte-9n45fh{color:var(--neutral-400)}.trim-duration.svelte-9n45fh{color:var(--color-accent);margin-right:var(--spacing-sm)}",map:'{"version":3,"file":"AudioRecorder.svelte","sources":["AudioRecorder.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { onMount } from \\"svelte\\";\\nimport { createEventDispatcher } from \\"svelte\\";\\nimport WaveSurfer from \\"wavesurfer.js\\";\\nimport { skip_audio, process_audio } from \\"../shared/utils\\";\\nimport WSRecord from \\"wavesurfer.js/dist/plugins/record.js\\";\\nimport WaveformControls from \\"../shared/WaveformControls.svelte\\";\\nimport WaveformRecordControls from \\"../shared/WaveformRecordControls.svelte\\";\\nimport RecordPlugin from \\"wavesurfer.js/dist/plugins/record.js\\";\\nimport { format_time } from \\"@gradio/utils\\";\\nexport let mode;\\nexport let i18n;\\nexport let dispatch_blob;\\nexport let waveform_settings;\\nexport let waveform_options = {\\n    show_recording_waveform: true\\n};\\nexport let handle_reset_value;\\nexport let editable = true;\\nexport let recording = false;\\nlet micWaveform;\\nlet recordingWaveform;\\nlet playing = false;\\nlet recordingContainer;\\nlet microphoneContainer;\\nlet record;\\nlet recordedAudio = null;\\nlet timeRef;\\nlet durationRef;\\nlet audio_duration;\\nlet seconds = 0;\\nlet interval;\\nlet timing = false;\\nlet trimDuration = 0;\\nconst start_interval = () => {\\n    clearInterval(interval);\\n    interval = setInterval(() => {\\n        seconds++;\\n    }, 1e3);\\n};\\nconst dispatch = createEventDispatcher();\\nfunction record_start_callback() {\\n    start_interval();\\n    timing = true;\\n    dispatch(\\"start_recording\\");\\n    if (waveform_options.show_recording_waveform) {\\n        let waveformCanvas = microphoneContainer;\\n        if (waveformCanvas)\\n            waveformCanvas.style.display = \\"block\\";\\n    }\\n}\\nasync function record_end_callback(blob) {\\n    seconds = 0;\\n    timing = false;\\n    clearInterval(interval);\\n    try {\\n        const array_buffer = await blob.arrayBuffer();\\n        const context = new AudioContext({\\n            sampleRate: waveform_settings.sampleRate\\n        });\\n        const audio_buffer = await context.decodeAudioData(array_buffer);\\n        if (audio_buffer)\\n            await process_audio(audio_buffer).then(async (audio) => {\\n                await dispatch_blob([audio], \\"change\\");\\n                await dispatch_blob([audio], \\"stop_recording\\");\\n            });\\n    }\\n    catch (e) {\\n        console.error(e);\\n    }\\n}\\n$: record?.on(\\"record-resume\\", () => {\\n    start_interval();\\n});\\n$: recordingWaveform?.on(\\"decode\\", (duration) => {\\n    audio_duration = duration;\\n    durationRef && (durationRef.textContent = format_time(duration));\\n});\\n$: recordingWaveform?.on(\\"timeupdate\\", (currentTime) => timeRef && (timeRef.textContent = format_time(currentTime)));\\n$: recordingWaveform?.on(\\"pause\\", () => {\\n    dispatch(\\"pause\\");\\n    playing = false;\\n});\\n$: recordingWaveform?.on(\\"play\\", () => {\\n    dispatch(\\"play\\");\\n    playing = true;\\n});\\n$: recordingWaveform?.on(\\"finish\\", () => {\\n    dispatch(\\"stop\\");\\n    playing = false;\\n});\\nconst create_mic_waveform = () => {\\n    if (microphoneContainer)\\n        microphoneContainer.innerHTML = \\"\\";\\n    if (micWaveform !== void 0)\\n        micWaveform.destroy();\\n    if (!microphoneContainer)\\n        return;\\n    micWaveform = WaveSurfer.create({\\n        ...waveform_settings,\\n        normalize: false,\\n        container: microphoneContainer\\n    });\\n    record = micWaveform.registerPlugin(RecordPlugin.create());\\n    record?.on(\\"record-end\\", record_end_callback);\\n    record?.on(\\"record-start\\", record_start_callback);\\n    record?.on(\\"record-pause\\", () => {\\n        dispatch(\\"pause_recording\\");\\n        clearInterval(interval);\\n    });\\n    record?.on(\\"record-end\\", (blob) => {\\n        recordedAudio = URL.createObjectURL(blob);\\n        const microphone = microphoneContainer;\\n        const recording2 = recordingContainer;\\n        if (microphone)\\n            microphone.style.display = \\"none\\";\\n        if (recording2 && recordedAudio) {\\n            recording2.innerHTML = \\"\\";\\n            create_recording_waveform();\\n        }\\n    });\\n};\\nconst create_recording_waveform = () => {\\n    let recording2 = recordingContainer;\\n    if (!recordedAudio || !recording2)\\n        return;\\n    recordingWaveform = WaveSurfer.create({\\n        container: recording2,\\n        url: recordedAudio,\\n        ...waveform_settings\\n    });\\n};\\nconst handle_trim_audio = async (start, end) => {\\n    mode = \\"edit\\";\\n    const decodedData = recordingWaveform.getDecodedData();\\n    if (decodedData)\\n        await process_audio(decodedData, start, end).then(async (trimmedAudio) => {\\n            await dispatch_blob([trimmedAudio], \\"change\\");\\n            await dispatch_blob([trimmedAudio], \\"stop_recording\\");\\n            recordingWaveform.destroy();\\n            create_recording_waveform();\\n        });\\n    dispatch(\\"edit\\");\\n};\\nonMount(() => {\\n    create_mic_waveform();\\n    window.addEventListener(\\"keydown\\", (e) => {\\n        if (e.key === \\"ArrowRight\\") {\\n            skip_audio(recordingWaveform, 0.1);\\n        }\\n        else if (e.key === \\"ArrowLeft\\") {\\n            skip_audio(recordingWaveform, -0.1);\\n        }\\n    });\\n});\\n<\/script>\\n\\n<div class=\\"component-wrapper\\">\\n\\t<div\\n\\t\\tclass=\\"microphone\\"\\n\\t\\tbind:this={microphoneContainer}\\n\\t\\tdata-testid=\\"microphone-waveform\\"\\n\\t/>\\n\\t<div bind:this={recordingContainer} data-testid=\\"recording-waveform\\" />\\n\\n\\t{#if (timing || recordedAudio) && waveform_options.show_recording_waveform}\\n\\t\\t<div class=\\"timestamps\\">\\n\\t\\t\\t<time bind:this={timeRef} class=\\"time\\">0:00</time>\\n\\t\\t\\t<div>\\n\\t\\t\\t\\t{#if mode === \\"edit\\" && trimDuration > 0}\\n\\t\\t\\t\\t\\t<time class=\\"trim-duration\\">{format_time(trimDuration)}</time>\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t{#if timing}\\n\\t\\t\\t\\t\\t<time class=\\"duration\\">{format_time(seconds)}</time>\\n\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t<time bind:this={durationRef} class=\\"duration\\">0:00</time>\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t</div>\\n\\t\\t</div>\\n\\t{/if}\\n\\n\\t{#if microphoneContainer && !recordedAudio}\\n\\t\\t<WaveformRecordControls\\n\\t\\t\\tbind:record\\n\\t\\t\\t{i18n}\\n\\t\\t\\t{timing}\\n\\t\\t\\t{recording}\\n\\t\\t\\tshow_recording_waveform={waveform_options.show_recording_waveform}\\n\\t\\t\\trecord_time={format_time(seconds)}\\n\\t\\t/>\\n\\t{/if}\\n\\n\\t{#if recordingWaveform && recordedAudio}\\n\\t\\t<WaveformControls\\n\\t\\t\\tbind:waveform={recordingWaveform}\\n\\t\\t\\tcontainer={recordingContainer}\\n\\t\\t\\t{playing}\\n\\t\\t\\t{audio_duration}\\n\\t\\t\\t{i18n}\\n\\t\\t\\t{editable}\\n\\t\\t\\tinteractive={true}\\n\\t\\t\\t{handle_trim_audio}\\n\\t\\t\\tbind:trimDuration\\n\\t\\t\\tbind:mode\\n\\t\\t\\tshow_redo\\n\\t\\t\\t{handle_reset_value}\\n\\t\\t\\t{waveform_options}\\n\\t\\t/>\\n\\t{/if}\\n</div>\\n\\n<style>\\n\\t.microphone {\\n\\t\\twidth: 100%;\\n\\t\\tdisplay: none;\\n\\t}\\n\\n\\t.component-wrapper {\\n\\t\\tpadding: var(--size-3);\\n\\t\\twidth: 100%;\\n\\t}\\n\\n\\t.timestamps {\\n\\t\\tdisplay: flex;\\n\\t\\tjustify-content: space-between;\\n\\t\\talign-items: center;\\n\\t\\twidth: 100%;\\n\\t\\tpadding: var(--size-1) 0;\\n\\t\\tmargin: var(--spacing-md) 0;\\n\\t}\\n\\n\\t.time {\\n\\t\\tcolor: var(--neutral-400);\\n\\t}\\n\\n\\t.duration {\\n\\t\\tcolor: var(--neutral-400);\\n\\t}\\n\\n\\t.trim-duration {\\n\\t\\tcolor: var(--color-accent);\\n\\t\\tmargin-right: var(--spacing-sm);\\n\\t}</style>\\n"],"names":[],"mappings":"AAmNC,yBAAY,CACX,KAAK,CAAE,IAAI,CACX,OAAO,CAAE,IACV,CAEA,gCAAmB,CAClB,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,KAAK,CAAE,IACR,CAEA,yBAAY,CACX,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,aAAa,CAC9B,WAAW,CAAE,MAAM,CACnB,KAAK,CAAE,IAAI,CACX,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,CAAC,CACxB,MAAM,CAAE,IAAI,YAAY,CAAC,CAAC,CAC3B,CAEA,mBAAM,CACL,KAAK,CAAE,IAAI,aAAa,CACzB,CAEA,uBAAU,CACT,KAAK,CAAE,IAAI,aAAa,CACzB,CAEA,4BAAe,CACd,KAAK,CAAE,IAAI,cAAc,CAAC,CAC1B,YAAY,CAAE,IAAI,YAAY,CAC/B"}'},Qt=create_ssr_component((A,t,e,o)=>{let {mode:n}=t,{i18n:r}=t,{dispatch_blob:l}=t,{waveform_settings:s}=t,{waveform_options:i={show_recording_waveform:!0}}=t,{handle_reset_value:a}=t,{editable:c=!0}=t,{recording:C=!1}=t,u,h;createEventDispatcher(),t.mode===void 0&&e.mode&&n!==void 0&&e.mode(n),t.i18n===void 0&&e.i18n&&r!==void 0&&e.i18n(r),t.dispatch_blob===void 0&&e.dispatch_blob&&l!==void 0&&e.dispatch_blob(l),t.waveform_settings===void 0&&e.waveform_settings&&s!==void 0&&e.waveform_settings(s),t.waveform_options===void 0&&e.waveform_options&&i!==void 0&&e.waveform_options(i),t.handle_reset_value===void 0&&e.handle_reset_value&&a!==void 0&&e.handle_reset_value(a),t.editable===void 0&&e.editable&&c!==void 0&&e.editable(c),t.recording===void 0&&e.recording&&C!==void 0&&e.recording(C),A.css.add(St);let f,m,b=A.head;do f=!0,A.head=b,m=`<div class="component-wrapper svelte-9n45fh"><div class="microphone svelte-9n45fh" data-testid="microphone-waveform"${add_attribute("this",h,0)}></div> <div data-testid="recording-waveform"${add_attribute("this",u,0)}></div>    </div>`;while(!f);return m}),Kt={code:'.controls.svelte-1fz19cj{display:flex;align-items:center;justify-content:space-between;flex-wrap:wrap}.mic-wrap.svelte-1fz19cj{display:block;align-items:center;margin:var(--spacing-xl)}.icon.svelte-1fz19cj{width:var(--size-4);height:var(--size-4);fill:var(--primary-600);stroke:var(--primary-600)}.stop-button-paused.svelte-1fz19cj{display:none;height:var(--size-8);width:var(--size-20);background-color:var(--block-background-fill);border-radius:var(--button-large-radius);align-items:center;border:1px solid var(--block-border-color);margin-right:5px}.stop-button-paused.svelte-1fz19cj::before{content:"";height:var(--size-4);width:var(--size-4);border-radius:var(--radius-full);background:var(--primary-600);margin:0 var(--spacing-xl)}.stop-button.svelte-1fz19cj::before{content:"";height:var(--size-4);width:var(--size-4);border-radius:var(--radius-full);background:var(--primary-600);margin:0 var(--spacing-xl);animation:svelte-1fz19cj-scaling 1800ms infinite}.stop-button.svelte-1fz19cj{height:var(--size-8);width:var(--size-20);background-color:var(--block-background-fill);border-radius:var(--button-large-radius);align-items:center;border:1px solid var(--primary-600);margin-right:5px;display:flex}.spinner-button.svelte-1fz19cj{height:var(--size-8);width:var(--size-24);background-color:var(--block-background-fill);border-radius:var(--radius-3xl);align-items:center;border:1px solid var(--primary-600);margin:0 var(--spacing-xl);display:flex;justify-content:space-evenly}.record-button.svelte-1fz19cj::before{content:"";height:var(--size-4);width:var(--size-4);border-radius:var(--radius-full);background:var(--primary-600);margin:0 var(--spacing-xl)}.record-button.svelte-1fz19cj{height:var(--size-8);width:var(--size-24);background-color:var(--block-background-fill);border-radius:var(--button-large-radius);display:flex;align-items:center;border:1px solid var(--block-border-color)}@keyframes svelte-1fz19cj-scaling{0%{background-color:var(--primary-600);scale:1}50%{background-color:var(--primary-600);scale:1.2}100%{background-color:var(--primary-600);scale:1}}',map:'{"version":3,"file":"StreamAudio.svelte","sources":["StreamAudio.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { onMount } from \\"svelte\\";\\nimport { Spinner } from \\"@gradio/icons\\";\\nimport WaveSurfer from \\"wavesurfer.js\\";\\nimport RecordPlugin from \\"wavesurfer.js/dist/plugins/record.js\\";\\nimport DeviceSelect from \\"../shared/DeviceSelect.svelte\\";\\nexport let recording = false;\\nexport let paused_recording = false;\\nexport let stop;\\nexport let record;\\nexport let i18n;\\nexport let waveform_settings;\\nexport let waveform_options = {\\n    show_recording_waveform: true\\n};\\nexport let waiting = false;\\nlet micWaveform;\\nlet waveformRecord;\\nlet microphoneContainer;\\nlet micDevices = [];\\nonMount(() => {\\n    create_mic_waveform();\\n});\\nconst create_mic_waveform = () => {\\n    if (micWaveform !== void 0)\\n        micWaveform.destroy();\\n    if (!microphoneContainer)\\n        return;\\n    micWaveform = WaveSurfer.create({\\n        ...waveform_settings,\\n        height: 100,\\n        container: microphoneContainer\\n    });\\n    waveformRecord = micWaveform.registerPlugin(RecordPlugin.create());\\n};\\n<\/script>\\n\\n<div class=\\"mic-wrap\\">\\n\\t{#if waveform_options.show_recording_waveform}\\n\\t\\t<div\\n\\t\\t\\tbind:this={microphoneContainer}\\n\\t\\t\\tstyle:display={recording ? \\"block\\" : \\"none\\"}\\n\\t\\t/>\\n\\t{/if}\\n\\t<div class=\\"controls\\">\\n\\t\\t{#if recording && !waiting}\\n\\t\\t\\t<button\\n\\t\\t\\t\\tclass={paused_recording ? \\"stop-button-paused\\" : \\"stop-button\\"}\\n\\t\\t\\t\\ton:click={() => {\\n\\t\\t\\t\\t\\twaveformRecord?.stopMic();\\n\\t\\t\\t\\t\\tstop();\\n\\t\\t\\t\\t}}\\n\\t\\t\\t>\\n\\t\\t\\t\\t<span class=\\"record-icon\\">\\n\\t\\t\\t\\t\\t<span class=\\"pinger\\" />\\n\\t\\t\\t\\t\\t<span class=\\"dot\\" />\\n\\t\\t\\t\\t</span>\\n\\t\\t\\t\\t{paused_recording ? i18n(\\"audio.pause\\") : i18n(\\"audio.stop\\")}\\n\\t\\t\\t</button>\\n\\t\\t{:else if recording && waiting}\\n\\t\\t\\t<button\\n\\t\\t\\t\\tclass=\\"spinner-button\\"\\n\\t\\t\\t\\ton:click={() => {\\n\\t\\t\\t\\t\\tstop();\\n\\t\\t\\t\\t}}\\n\\t\\t\\t>\\n\\t\\t\\t\\t<div class=\\"icon\\">\\n\\t\\t\\t\\t\\t<Spinner />\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t{i18n(\\"audio.waiting\\")}\\n\\t\\t\\t</button>\\n\\t\\t{:else}\\n\\t\\t\\t<button\\n\\t\\t\\t\\tclass=\\"record-button\\"\\n\\t\\t\\t\\ton:click={() => {\\n\\t\\t\\t\\t\\twaveformRecord?.startMic();\\n\\t\\t\\t\\t\\trecord();\\n\\t\\t\\t\\t}}\\n\\t\\t\\t>\\n\\t\\t\\t\\t<span class=\\"record-icon\\">\\n\\t\\t\\t\\t\\t<span class=\\"dot\\" />\\n\\t\\t\\t\\t</span>\\n\\t\\t\\t\\t{i18n(\\"audio.record\\")}\\n\\t\\t\\t</button>\\n\\t\\t{/if}\\n\\n\\t\\t<DeviceSelect bind:micDevices {i18n} />\\n\\t</div>\\n</div>\\n\\n<style>\\n\\t.controls {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tjustify-content: space-between;\\n\\t\\tflex-wrap: wrap;\\n\\t}\\n\\n\\t.mic-wrap {\\n\\t\\tdisplay: block;\\n\\t\\talign-items: center;\\n\\t\\tmargin: var(--spacing-xl);\\n\\t}\\n\\n\\t.icon {\\n\\t\\twidth: var(--size-4);\\n\\t\\theight: var(--size-4);\\n\\t\\tfill: var(--primary-600);\\n\\t\\tstroke: var(--primary-600);\\n\\t}\\n\\n\\t.stop-button-paused {\\n\\t\\tdisplay: none;\\n\\t\\theight: var(--size-8);\\n\\t\\twidth: var(--size-20);\\n\\t\\tbackground-color: var(--block-background-fill);\\n\\t\\tborder-radius: var(--button-large-radius);\\n\\t\\talign-items: center;\\n\\t\\tborder: 1px solid var(--block-border-color);\\n\\t\\tmargin-right: 5px;\\n\\t}\\n\\n\\t.stop-button-paused::before {\\n\\t\\tcontent: \\"\\";\\n\\t\\theight: var(--size-4);\\n\\t\\twidth: var(--size-4);\\n\\t\\tborder-radius: var(--radius-full);\\n\\t\\tbackground: var(--primary-600);\\n\\t\\tmargin: 0 var(--spacing-xl);\\n\\t}\\n\\n\\t.stop-button::before {\\n\\t\\tcontent: \\"\\";\\n\\t\\theight: var(--size-4);\\n\\t\\twidth: var(--size-4);\\n\\t\\tborder-radius: var(--radius-full);\\n\\t\\tbackground: var(--primary-600);\\n\\t\\tmargin: 0 var(--spacing-xl);\\n\\t\\tanimation: scaling 1800ms infinite;\\n\\t}\\n\\n\\t.stop-button {\\n\\t\\theight: var(--size-8);\\n\\t\\twidth: var(--size-20);\\n\\t\\tbackground-color: var(--block-background-fill);\\n\\t\\tborder-radius: var(--button-large-radius);\\n\\t\\talign-items: center;\\n\\t\\tborder: 1px solid var(--primary-600);\\n\\t\\tmargin-right: 5px;\\n\\t\\tdisplay: flex;\\n\\t}\\n\\n\\t.spinner-button {\\n\\t\\theight: var(--size-8);\\n\\t\\twidth: var(--size-24);\\n\\t\\tbackground-color: var(--block-background-fill);\\n\\t\\tborder-radius: var(--radius-3xl);\\n\\t\\talign-items: center;\\n\\t\\tborder: 1px solid var(--primary-600);\\n\\t\\tmargin: 0 var(--spacing-xl);\\n\\t\\tdisplay: flex;\\n\\t\\tjustify-content: space-evenly;\\n\\t}\\n\\n\\t.record-button::before {\\n\\t\\tcontent: \\"\\";\\n\\t\\theight: var(--size-4);\\n\\t\\twidth: var(--size-4);\\n\\t\\tborder-radius: var(--radius-full);\\n\\t\\tbackground: var(--primary-600);\\n\\t\\tmargin: 0 var(--spacing-xl);\\n\\t}\\n\\n\\t.record-button {\\n\\t\\theight: var(--size-8);\\n\\t\\twidth: var(--size-24);\\n\\t\\tbackground-color: var(--block-background-fill);\\n\\t\\tborder-radius: var(--button-large-radius);\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tborder: 1px solid var(--block-border-color);\\n\\t}\\n\\n\\t@keyframes scaling {\\n\\t\\t0% {\\n\\t\\t\\tbackground-color: var(--primary-600);\\n\\t\\t\\tscale: 1;\\n\\t\\t}\\n\\t\\t50% {\\n\\t\\t\\tbackground-color: var(--primary-600);\\n\\t\\t\\tscale: 1.2;\\n\\t\\t}\\n\\t\\t100% {\\n\\t\\t\\tbackground-color: var(--primary-600);\\n\\t\\t\\tscale: 1;\\n\\t\\t}\\n\\t}</style>\\n"],"names":[],"mappings":"AA0FC,wBAAU,CACT,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,aAAa,CAC9B,SAAS,CAAE,IACZ,CAEA,wBAAU,CACT,OAAO,CAAE,KAAK,CACd,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,IAAI,YAAY,CACzB,CAEA,oBAAM,CACL,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,IAAI,CAAE,IAAI,aAAa,CAAC,CACxB,MAAM,CAAE,IAAI,aAAa,CAC1B,CAEA,kCAAoB,CACnB,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CAC3C,YAAY,CAAE,GACf,CAEA,kCAAmB,QAAS,CAC3B,OAAO,CAAE,EAAE,CACX,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,aAAa,CAAE,IAAI,aAAa,CAAC,CACjC,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,MAAM,CAAE,CAAC,CAAC,IAAI,YAAY,CAC3B,CAEA,2BAAY,QAAS,CACpB,OAAO,CAAE,EAAE,CACX,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,aAAa,CAAE,IAAI,aAAa,CAAC,CACjC,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,MAAM,CAAE,CAAC,CAAC,IAAI,YAAY,CAAC,CAC3B,SAAS,CAAE,sBAAO,CAAC,MAAM,CAAC,QAC3B,CAEA,2BAAa,CACZ,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,CACpC,YAAY,CAAE,GAAG,CACjB,OAAO,CAAE,IACV,CAEA,8BAAgB,CACf,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,aAAa,CAAE,IAAI,YAAY,CAAC,CAChC,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,CACpC,MAAM,CAAE,CAAC,CAAC,IAAI,YAAY,CAAC,CAC3B,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,YAClB,CAEA,6BAAc,QAAS,CACtB,OAAO,CAAE,EAAE,CACX,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,aAAa,CAAE,IAAI,aAAa,CAAC,CACjC,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,MAAM,CAAE,CAAC,CAAC,IAAI,YAAY,CAC3B,CAEA,6BAAe,CACd,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAC3C,CAEA,WAAW,sBAAQ,CAClB,EAAG,CACF,gBAAgB,CAAE,IAAI,aAAa,CAAC,CACpC,KAAK,CAAE,CACR,CACA,GAAI,CACH,gBAAgB,CAAE,IAAI,aAAa,CAAC,CACpC,KAAK,CAAE,GACR,CACA,IAAK,CACJ,gBAAgB,CAAE,IAAI,aAAa,CAAC,CACpC,KAAK,CAAE,CACR,CACD"}'},Rt=create_ssr_component((A,t,e,o)=>{let{recording:n=!1}=t,{paused_recording:r=!1}=t,{stop:l}=t,{record:s}=t,{i18n:i}=t,{waveform_settings:a}=t,{waveform_options:c={show_recording_waveform:!0}}=t,{waiting:C=!1}=t,v,u=[];t.recording===void 0&&e.recording&&n!==void 0&&e.recording(n),t.paused_recording===void 0&&e.paused_recording&&r!==void 0&&e.paused_recording(r),t.stop===void 0&&e.stop&&l!==void 0&&e.stop(l),t.record===void 0&&e.record&&s!==void 0&&e.record(s),t.i18n===void 0&&e.i18n&&i!==void 0&&e.i18n(i),t.waveform_settings===void 0&&e.waveform_settings&&a!==void 0&&e.waveform_settings(a),t.waveform_options===void 0&&e.waveform_options&&c!==void 0&&e.waveform_options(c),t.waiting===void 0&&e.waiting&&C!==void 0&&e.waiting(C),A.css.add(Kt);let f,m,b=A.head;do f=!0,A.head=b,m=`<div class="mic-wrap svelte-1fz19cj">${c.show_recording_waveform?`<div${add_styles({display:n?"block":"none"})}${add_attribute("this",v,0)}></div>`:""} <div class="controls svelte-1fz19cj">${n&&!C?`<button class="${escape(null_to_empty(r?"stop-button-paused":"stop-button"),!0)+" svelte-1fz19cj"}"><span class="record-icon" data-svelte-h="svelte-bla7qm"><span class="pinger"></span> <span class="dot"></span></span> ${escape(i(r?"audio.pause":"audio.stop"))}</button>`:`${n&&C?`<button class="spinner-button svelte-1fz19cj"><div class="icon svelte-1fz19cj">${validate_component(ke,"Spinner").$$render(A,{},{},{})}</div> ${escape(i("audio.waiting"))}</button>`:`<button class="record-button svelte-1fz19cj"><span class="record-icon" data-svelte-h="svelte-1dwz2xe"><span class="dot"></span></span> ${escape(i("audio.record"))}</button>`}`} ${validate_component(at,"DeviceSelect").$$render(A,{i18n:i,micDevices:u},{micDevices:p=>{u=p,f=!1;}},{})}</div> </div>`;while(!f);return m}),Ot={code:".audio-container.svelte-1ud6e7m{height:calc(var(--size-full) - var(--size-6));display:flex;flex-direction:column;justify-content:space-between}.audio-container.compact-audio.svelte-1ud6e7m{margin-top:calc(var(--size-8) * -1);height:auto;padding:0px;gap:var(--size-2);min-height:var(--size-5)}.compact-audio.svelte-1ud6e7m .audio-player{padding:0px}.compact-audio.svelte-1ud6e7m .controls{gap:0px;padding:0px}.compact-audio.svelte-1ud6e7m .waveform-container{height:var(--size-12) !important}.compact-audio.svelte-1ud6e7m .player-container{min-height:unset;height:auto}",map:'{"version":3,"file":"InteractiveAudio.svelte","sources":["InteractiveAudio.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { onDestroy, createEventDispatcher, tick } from \\"svelte\\";\\nimport { Upload, ModifyUpload } from \\"@gradio/upload\\";\\nimport { prepare_files } from \\"@gradio/client\\";\\nimport { BlockLabel } from \\"@gradio/atoms\\";\\nimport { Music } from \\"@gradio/icons\\";\\nimport { StreamingBar } from \\"@gradio/statustracker\\";\\nimport AudioPlayer from \\"../player/AudioPlayer.svelte\\";\\nimport AudioRecorder from \\"../recorder/AudioRecorder.svelte\\";\\nimport StreamAudio from \\"../streaming/StreamAudio.svelte\\";\\nimport { SelectSource } from \\"@gradio/atoms\\";\\nexport let value = null;\\nexport let label;\\nexport let root;\\nexport let loop;\\nexport let show_label = true;\\nexport let show_download_button = false;\\nexport let sources = [\\"microphone\\", \\"upload\\"];\\nexport let pending = false;\\nexport let streaming = false;\\nexport let i18n;\\nexport let waveform_settings;\\nexport let trim_region_settings = {};\\nexport let waveform_options = {};\\nexport let dragging;\\nexport let active_source;\\nexport let handle_reset_value = () => {\\n};\\nexport let editable = true;\\nexport let max_file_size = null;\\nexport let upload;\\nexport let stream_handler;\\nexport let stream_every;\\nexport let uploading = false;\\nexport let recording = false;\\nexport let class_name = \\"\\";\\nlet time_limit = null;\\nlet stream_state = \\"closed\\";\\nexport const modify_stream = (state) => {\\n    if (state === \\"closed\\") {\\n        time_limit = null;\\n        stream_state = \\"closed\\";\\n    }\\n    else if (state === \\"waiting\\") {\\n        stream_state = \\"waiting\\";\\n    }\\n    else {\\n        stream_state = \\"open\\";\\n    }\\n};\\nexport const set_time_limit = (time) => {\\n    if (recording)\\n        time_limit = time;\\n};\\n$: dispatch(\\"drag\\", dragging);\\nlet recorder;\\nlet mode = \\"\\";\\nlet header = void 0;\\nlet pending_stream = [];\\nlet submit_pending_stream_on_pending_end = false;\\nlet inited = false;\\nconst NUM_HEADER_BYTES = 44;\\nlet audio_chunks = [];\\nlet module_promises;\\nfunction get_modules() {\\n    module_promises = [\\n        import(\\"extendable-media-recorder\\"),\\n        import(\\"extendable-media-recorder-wav-encoder\\")\\n    ];\\n}\\nconst is_browser = typeof window !== \\"undefined\\";\\nif (is_browser && streaming) {\\n    get_modules();\\n}\\nconst dispatch = createEventDispatcher();\\nconst dispatch_blob = async (blobs, event) => {\\n    let _audio_blob = new File(blobs, \\"audio.wav\\");\\n    const val = await prepare_files([_audio_blob], event === \\"stream\\");\\n    value = ((await upload(val, root, void 0, max_file_size || void 0))?.filter(Boolean))[0];\\n    dispatch(event, value);\\n};\\nonDestroy(() => {\\n    if (streaming && recorder && recorder.state !== \\"inactive\\") {\\n        recorder.stop();\\n    }\\n});\\nasync function prepare_audio() {\\n    let stream;\\n    try {\\n        stream = await navigator.mediaDevices.getUserMedia({ audio: true });\\n    }\\n    catch (err) {\\n        if (!navigator.mediaDevices) {\\n            dispatch(\\"error\\", i18n(\\"audio.no_device_support\\"));\\n            return;\\n        }\\n        if (err instanceof DOMException && err.name == \\"NotAllowedError\\") {\\n            dispatch(\\"error\\", i18n(\\"audio.allow_recording_access\\"));\\n            return;\\n        }\\n        throw err;\\n    }\\n    if (stream == null)\\n        return;\\n    if (streaming) {\\n        const [{ MediaRecorder: MediaRecorder2, register }, { connect }] = await Promise.all(module_promises);\\n        await register(await connect());\\n        recorder = new MediaRecorder2(stream, { mimeType: \\"audio/wav\\" });\\n        recorder.addEventListener(\\"dataavailable\\", handle_chunk);\\n    }\\n    else {\\n        recorder = new MediaRecorder(stream);\\n        recorder.addEventListener(\\"dataavailable\\", (event) => {\\n            audio_chunks.push(event.data);\\n        });\\n    }\\n    recorder.addEventListener(\\"stop\\", async () => {\\n        recording = false;\\n        await dispatch_blob(audio_chunks, \\"change\\");\\n        await dispatch_blob(audio_chunks, \\"stop_recording\\");\\n        audio_chunks = [];\\n    });\\n    inited = true;\\n}\\nasync function handle_chunk(event) {\\n    let buffer = await event.data.arrayBuffer();\\n    let payload = new Uint8Array(buffer);\\n    if (!header) {\\n        header = new Uint8Array(buffer.slice(0, NUM_HEADER_BYTES));\\n        payload = new Uint8Array(buffer.slice(NUM_HEADER_BYTES));\\n    }\\n    if (pending) {\\n        pending_stream.push(payload);\\n    }\\n    else {\\n        let blobParts = [header].concat(pending_stream, [payload]);\\n        if (!recording || stream_state === \\"waiting\\")\\n            return;\\n        dispatch_blob(blobParts, \\"stream\\");\\n        pending_stream = [];\\n    }\\n}\\n$: if (submit_pending_stream_on_pending_end && pending === false) {\\n    submit_pending_stream_on_pending_end = false;\\n    if (header && pending_stream) {\\n        let blobParts = [header].concat(pending_stream);\\n        pending_stream = [];\\n        dispatch_blob(blobParts, \\"stream\\");\\n    }\\n}\\nasync function record() {\\n    recording = true;\\n    dispatch(\\"start_recording\\");\\n    if (!inited)\\n        await prepare_audio();\\n    header = void 0;\\n    if (streaming && recorder.state != \\"recording\\") {\\n        recorder.start(stream_every * 1e3);\\n    }\\n}\\nfunction clear() {\\n    dispatch(\\"change\\", null);\\n    dispatch(\\"clear\\");\\n    mode = \\"\\";\\n    value = null;\\n}\\nfunction handle_load({ detail }) {\\n    value = detail;\\n    dispatch(\\"change\\", detail);\\n    dispatch(\\"upload\\", detail);\\n}\\nasync function stop() {\\n    recording = false;\\n    if (streaming) {\\n        dispatch(\\"close_stream\\");\\n        dispatch(\\"stop_recording\\");\\n        recorder.stop();\\n        if (pending) {\\n            submit_pending_stream_on_pending_end = true;\\n        }\\n        dispatch_blob(audio_chunks, \\"stop_recording\\");\\n        dispatch(\\"clear\\");\\n        mode = \\"\\";\\n    }\\n}\\n$: if (!recording && recorder)\\n    stop();\\n$: if (recording && recorder)\\n    record();\\n<\/script>\\n\\n<BlockLabel\\n\\t{show_label}\\n\\tIcon={Music}\\n\\tfloat={active_source === \\"upload\\" && value === null}\\n\\tlabel={label || i18n(\\"audio.audio\\")}\\n/>\\n<div class=\\"audio-container {class_name}\\">\\n\\t<StreamingBar {time_limit} />\\n\\t{#if value === null || streaming}\\n\\t\\t{#if active_source === \\"microphone\\"}\\n\\t\\t\\t<ModifyUpload {i18n} on:clear={clear} />\\n\\t\\t\\t{#if streaming}\\n\\t\\t\\t\\t<StreamAudio\\n\\t\\t\\t\\t\\t{record}\\n\\t\\t\\t\\t\\t{recording}\\n\\t\\t\\t\\t\\t{stop}\\n\\t\\t\\t\\t\\t{i18n}\\n\\t\\t\\t\\t\\t{waveform_settings}\\n\\t\\t\\t\\t\\t{waveform_options}\\n\\t\\t\\t\\t\\twaiting={stream_state === \\"waiting\\"}\\n\\t\\t\\t\\t/>\\n\\t\\t\\t{:else}\\n\\t\\t\\t\\t<AudioRecorder\\n\\t\\t\\t\\t\\tbind:mode\\n\\t\\t\\t\\t\\t{i18n}\\n\\t\\t\\t\\t\\t{editable}\\n\\t\\t\\t\\t\\t{recording}\\n\\t\\t\\t\\t\\t{dispatch_blob}\\n\\t\\t\\t\\t\\t{waveform_settings}\\n\\t\\t\\t\\t\\t{waveform_options}\\n\\t\\t\\t\\t\\t{handle_reset_value}\\n\\t\\t\\t\\t\\ton:start_recording\\n\\t\\t\\t\\t\\ton:pause_recording\\n\\t\\t\\t\\t\\ton:stop_recording\\n\\t\\t\\t\\t/>\\n\\t\\t\\t{/if}\\n\\t\\t{:else if active_source === \\"upload\\"}\\n\\t\\t\\t<!-- explicitly listed out audio mimetypes due to iOS bug not recognizing audio/* -->\\n\\t\\t\\t<Upload\\n\\t\\t\\t\\tfiletype=\\"audio/aac,audio/midi,audio/mpeg,audio/ogg,audio/wav,audio/x-wav,audio/opus,audio/webm,audio/flac,audio/vnd.rn-realaudio,audio/x-ms-wma,audio/x-aiff,audio/amr,audio/*\\"\\n\\t\\t\\t\\ton:load={handle_load}\\n\\t\\t\\t\\tbind:dragging\\n\\t\\t\\t\\tbind:uploading\\n\\t\\t\\t\\ton:error={({ detail }) => dispatch(\\"error\\", detail)}\\n\\t\\t\\t\\t{root}\\n\\t\\t\\t\\t{max_file_size}\\n\\t\\t\\t\\t{upload}\\n\\t\\t\\t\\t{stream_handler}\\n\\t\\t\\t\\taria_label={i18n(\\"audio.drop_to_upload\\")}\\n\\t\\t\\t>\\n\\t\\t\\t\\t<slot />\\n\\t\\t\\t</Upload>\\n\\t\\t{/if}\\n\\t{:else}\\n\\t\\t<ModifyUpload\\n\\t\\t\\t{i18n}\\n\\t\\t\\ton:clear={clear}\\n\\t\\t\\ton:edit={() => (mode = \\"edit\\")}\\n\\t\\t\\tdownload={show_download_button ? value.url : null}\\n\\t\\t/>\\n\\n\\t\\t<AudioPlayer\\n\\t\\t\\tbind:mode\\n\\t\\t\\t{value}\\n\\t\\t\\t{label}\\n\\t\\t\\t{i18n}\\n\\t\\t\\t{dispatch_blob}\\n\\t\\t\\t{waveform_settings}\\n\\t\\t\\t{waveform_options}\\n\\t\\t\\t{trim_region_settings}\\n\\t\\t\\t{handle_reset_value}\\n\\t\\t\\t{editable}\\n\\t\\t\\t{loop}\\n\\t\\t\\tinteractive\\n\\t\\t\\ton:stop\\n\\t\\t\\ton:play\\n\\t\\t\\ton:pause\\n\\t\\t\\ton:edit\\n\\t\\t/>\\n\\t{/if}\\n\\t<SelectSource {sources} bind:active_source handle_clear={clear} />\\n</div>\\n\\n<style>\\n\\t.audio-container {\\n\\t\\theight: calc(var(--size-full) - var(--size-6));\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: column;\\n\\t\\tjustify-content: space-between;\\n\\t}\\n\\n\\t.audio-container.compact-audio {\\n\\t\\tmargin-top: calc(var(--size-8) * -1);\\n\\t\\theight: auto;\\n\\t\\tpadding: 0px;\\n\\t\\tgap: var(--size-2);\\n\\t\\tmin-height: var(--size-5);\\n\\t}\\n\\n\\t.compact-audio :global(.audio-player) {\\n\\t\\tpadding: 0px;\\n\\t}\\n\\n\\t.compact-audio :global(.controls) {\\n\\t\\tgap: 0px;\\n\\t\\tpadding: 0px;\\n\\t}\\n\\n\\t.compact-audio :global(.waveform-container) {\\n\\t\\theight: var(--size-12) !important;\\n\\t}\\n\\n\\t.compact-audio :global(.player-container) {\\n\\t\\tmin-height: unset;\\n\\t\\theight: auto;\\n\\t}</style>\\n"],"names":[],"mappings":"AAkRC,+BAAiB,CAChB,MAAM,CAAE,KAAK,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAC9C,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,aAClB,CAEA,gBAAgB,6BAAe,CAC9B,UAAU,CAAE,KAAK,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CACpC,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,GAAG,CACZ,GAAG,CAAE,IAAI,QAAQ,CAAC,CAClB,UAAU,CAAE,IAAI,QAAQ,CACzB,CAEA,6BAAc,CAAS,aAAe,CACrC,OAAO,CAAE,GACV,CAEA,6BAAc,CAAS,SAAW,CACjC,GAAG,CAAE,GAAG,CACR,OAAO,CAAE,GACV,CAEA,6BAAc,CAAS,mBAAqB,CAC3C,MAAM,CAAE,IAAI,SAAS,CAAC,CAAC,UACxB,CAEA,6BAAc,CAAS,iBAAmB,CACzC,UAAU,CAAE,KAAK,CACjB,MAAM,CAAE,IACT"}'},ot=44,Wt=create_ssr_component((A,t,e,o)=>{let{value:n=null}=t,{label:r}=t,{root:l}=t,{loop:s}=t,{show_label:i=!0}=t,{show_download_button:a=!1}=t,{sources:c=["microphone","upload"]}=t,{pending:C=!1}=t,{streaming:v=!1}=t,{i18n:u}=t,{waveform_settings:h}=t,{trim_region_settings:f={}}=t,{waveform_options:m={}}=t,{dragging:b}=t,{active_source:p}=t,{handle_reset_value:z=()=>{}}=t,{editable:S=!0}=t,{max_file_size:G=null}=t,{upload:j}=t,{stream_handler:P}=t,{stream_every:L}=t,{uploading:Y=!1}=t,{recording:B=!1}=t,{class_name:q=""}=t,T=null,O="closed";const H=d=>{d==="closed"?(T=null,O="closed"):d==="waiting"?O="waiting":O="open";},V=d=>{B&&(T=d);};let _,Q="",K,R=[],X=!1,J=!1,W=[],$;function st(){$=[import('./module3-D79csqxs.js'),import('./module-D-2RN6lZ.js')];}typeof window<"u"&&v&&st();const E=createEventDispatcher(),x=async(d,g)=>{let k=new File(d,"audio.wav");const D=await la([k],g==="stream");n=(await j(D,l,void 0,G||void 0))?.filter(Boolean)[0],E(g,n);};onDestroy(()=>{v&&_&&_.state!=="inactive"&&_.stop();});async function dt(){let d;try{d=await navigator.mediaDevices.getUserMedia({audio:!0});}catch(g){if(!navigator.mediaDevices){E("error",u("audio.no_device_support"));return}if(g instanceof DOMException&&g.name=="NotAllowedError"){E("error",u("audio.allow_recording_access"));return}throw g}if(d!=null){if(v){const[{MediaRecorder:g,register:k},{connect:D}]=await Promise.all($);await k(await D()),_=new g(d,{mimeType:"audio/wav"}),_.addEventListener("dataavailable",lt);}else _=new MediaRecorder(d),_.addEventListener("dataavailable",g=>{W.push(g.data);});_.addEventListener("stop",async()=>{B=!1,await x(W,"change"),await x(W,"stop_recording"),W=[];}),J=!0;}}async function lt(d){let g=await d.data.arrayBuffer(),k=new Uint8Array(g);if(K||(K=new Uint8Array(g.slice(0,ot)),k=new Uint8Array(g.slice(ot))),C)R.push(k);else {let D=[K].concat(R,[k]);if(!B||O==="waiting")return;x(D,"stream"),R=[];}}async function tt(){B=!0,E("start_recording"),J||await dt(),K=void 0,v&&_.state!="recording"&&_.start(L*1e3);}function ct(){E("change",null),E("clear"),Q="",n=null;}async function et(){B=!1,v&&(E("close_stream"),E("stop_recording"),_.stop(),C&&(X=!0),x(W,"stop_recording"),E("clear"),Q="");}t.value===void 0&&e.value&&n!==void 0&&e.value(n),t.label===void 0&&e.label&&r!==void 0&&e.label(r),t.root===void 0&&e.root&&l!==void 0&&e.root(l),t.loop===void 0&&e.loop&&s!==void 0&&e.loop(s),t.show_label===void 0&&e.show_label&&i!==void 0&&e.show_label(i),t.show_download_button===void 0&&e.show_download_button&&a!==void 0&&e.show_download_button(a),t.sources===void 0&&e.sources&&c!==void 0&&e.sources(c),t.pending===void 0&&e.pending&&C!==void 0&&e.pending(C),t.streaming===void 0&&e.streaming&&v!==void 0&&e.streaming(v),t.i18n===void 0&&e.i18n&&u!==void 0&&e.i18n(u),t.waveform_settings===void 0&&e.waveform_settings&&h!==void 0&&e.waveform_settings(h),t.trim_region_settings===void 0&&e.trim_region_settings&&f!==void 0&&e.trim_region_settings(f),t.waveform_options===void 0&&e.waveform_options&&m!==void 0&&e.waveform_options(m),t.dragging===void 0&&e.dragging&&b!==void 0&&e.dragging(b),t.active_source===void 0&&e.active_source&&p!==void 0&&e.active_source(p),t.handle_reset_value===void 0&&e.handle_reset_value&&z!==void 0&&e.handle_reset_value(z),t.editable===void 0&&e.editable&&S!==void 0&&e.editable(S),t.max_file_size===void 0&&e.max_file_size&&G!==void 0&&e.max_file_size(G),t.upload===void 0&&e.upload&&j!==void 0&&e.upload(j),t.stream_handler===void 0&&e.stream_handler&&P!==void 0&&e.stream_handler(P),t.stream_every===void 0&&e.stream_every&&L!==void 0&&e.stream_every(L),t.uploading===void 0&&e.uploading&&Y!==void 0&&e.uploading(Y),t.recording===void 0&&e.recording&&B!==void 0&&e.recording(B),t.class_name===void 0&&e.class_name&&q!==void 0&&e.class_name(q),t.modify_stream===void 0&&e.modify_stream&&H!==void 0&&e.modify_stream(H),t.set_time_limit===void 0&&e.set_time_limit&&V!==void 0&&e.set_time_limit(V),A.css.add(Ot);let M,nt,Ct=A.head;do{if(M=!0,A.head=Ct,E("drag",b),X&&C===!1&&(X=!1,K&&R)){let d=[K].concat(R);R=[],x(d,"stream");}!B&&_&&et(),B&&_&&tt(),nt=`${validate_component(bt,"BlockLabel").$$render(A,{show_label:i,Icon:ee,float:p==="upload"&&n===null,label:r||u("audio.audio")},{},{})} <div class="${"audio-container "+escape(q,!0)+" svelte-1ud6e7m"}">${validate_component(KA,"StreamingBar").$$render(A,{time_limit:T},{},{})} ${n===null||v?`${p==="microphone"?`${validate_component(he,"ModifyUpload").$$render(A,{i18n:u},{},{})} ${v?`${validate_component(Rt,"StreamAudio").$$render(A,{record:tt,recording:B,stop:et,i18n:u,waveform_settings:h,waveform_options:m,waiting:O==="waiting"},{},{})}`:`${validate_component(Qt,"AudioRecorder").$$render(A,{i18n:u,editable:S,recording:B,dispatch_blob:x,waveform_settings:h,waveform_options:m,handle_reset_value:z,mode:Q},{mode:d=>{Q=d,M=!1;}},{})}`}`:`${p==="upload"?` ${validate_component(ge,"Upload").$$render(A,{filetype:"audio/aac,audio/midi,audio/mpeg,audio/ogg,audio/wav,audio/x-wav,audio/opus,audio/webm,audio/flac,audio/vnd.rn-realaudio,audio/x-ms-wma,audio/x-aiff,audio/amr,audio/*",root:l,max_file_size:G,upload:j,stream_handler:P,aria_label:u("audio.drop_to_upload"),dragging:b,uploading:Y},{dragging:d=>{b=d,M=!1;},uploading:d=>{Y=d,M=!1;}},{default:()=>`${o.default?o.default({}):""}`})}`:""}`}`:`${validate_component(he,"ModifyUpload").$$render(A,{i18n:u,download:a?n.url:null},{},{})} ${validate_component(Yt,"AudioPlayer").$$render(A,{value:n,label:r,i18n:u,dispatch_blob:x,waveform_settings:h,waveform_options:m,trim_region_settings:f,handle_reset_value:z,editable:S,loop:s,interactive:!0,mode:Q},{mode:d=>{Q=d,M=!1;}},{})}`} ${validate_component(ze,"SelectSource").$$render(A,{sources:c,handle_clear:ct,active_source:p},{active_source:d=>{p=d,M=!1;}},{})} </div>`;}while(!M);return nt}),Tt=Wt;

export { Tt as T };
//# sourceMappingURL=InteractiveAudio-DKNRgR2D.js.map
