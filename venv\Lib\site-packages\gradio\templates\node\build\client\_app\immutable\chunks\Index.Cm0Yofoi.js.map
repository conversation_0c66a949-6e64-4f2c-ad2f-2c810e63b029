{"version": 3, "file": "Index.Cm0Yofoi.js", "sources": ["../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/sourceEvent.js", "../../../../../../../../node_modules/.pnpm/d3-selection@3.0.0/node_modules/d3-selection/src/pointer.js", "../../../../../../../../node_modules/.pnpm/d3-drag@3.0.0/node_modules/d3-drag/src/noevent.js", "../../../../../../../../node_modules/.pnpm/d3-drag@3.0.0/node_modules/d3-drag/src/nodrag.js", "../../../../../../../../node_modules/.pnpm/d3-drag@3.0.0/node_modules/d3-drag/src/constant.js", "../../../../../../../../node_modules/.pnpm/d3-drag@3.0.0/node_modules/d3-drag/src/event.js", "../../../../../../../../node_modules/.pnpm/d3-drag@3.0.0/node_modules/d3-drag/src/drag.js", "../../../../../../../imageslider/shared/Slider.svelte", "../../../../../../../imageslider/shared/ImageEl.svelte", "../../../../../../../imageslider/shared/zoom.ts", "../../../../../../../imageslider/shared/SliderPreview.svelte", "../../../../../../../imageslider/shared/ClearImage.svelte", "../../../../../../../imageslider/shared/Image.svelte", "../../../../../../../imageslider/shared/SliderUpload.svelte", "../../../../../../../imageslider/Index.svelte"], "sourcesContent": ["export default function(event) {\n  let sourceEvent;\n  while (sourceEvent = event.sourceEvent) event = sourceEvent;\n  return event;\n}\n", "import sourceEvent from \"./sourceEvent.js\";\n\nexport default function(event, node) {\n  event = sourceEvent(event);\n  if (node === undefined) node = event.currentTarget;\n  if (node) {\n    var svg = node.ownerSVGElement || node;\n    if (svg.createSVGPoint) {\n      var point = svg.createSVGPoint();\n      point.x = event.clientX, point.y = event.clientY;\n      point = point.matrixTransform(node.getScreenCTM().inverse());\n      return [point.x, point.y];\n    }\n    if (node.getBoundingClientRect) {\n      var rect = node.getBoundingClientRect();\n      return [event.clientX - rect.left - node.clientLeft, event.clientY - rect.top - node.clientTop];\n    }\n  }\n  return [event.pageX, event.pageY];\n}\n", "// These are typically used in conjunction with noevent to ensure that we can\n// preventDefault on the event.\nexport const nonpassive = {passive: false};\nexport const nonpassivecapture = {capture: true, passive: false};\n\nexport function nopropagation(event) {\n  event.stopImmediatePropagation();\n}\n\nexport default function(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\n", "import {select} from \"d3-selection\";\nimport noevent, {nonpassivecapture} from \"./noevent.js\";\n\nexport default function(view) {\n  var root = view.document.documentElement,\n      selection = select(view).on(\"dragstart.drag\", noevent, nonpassivecapture);\n  if (\"onselectstart\" in root) {\n    selection.on(\"selectstart.drag\", noevent, nonpassivecapture);\n  } else {\n    root.__noselect = root.style.MozUserSelect;\n    root.style.MozUserSelect = \"none\";\n  }\n}\n\nexport function yesdrag(view, noclick) {\n  var root = view.document.documentElement,\n      selection = select(view).on(\"dragstart.drag\", null);\n  if (noclick) {\n    selection.on(\"click.drag\", noevent, nonpassivecapture);\n    setTimeout(function() { selection.on(\"click.drag\", null); }, 0);\n  }\n  if (\"onselectstart\" in root) {\n    selection.on(\"selectstart.drag\", null);\n  } else {\n    root.style.MozUserSelect = root.__noselect;\n    delete root.__noselect;\n  }\n}\n", "export default x => () => x;\n", "export default function DragEvent(type, {\n  sourceEvent,\n  subject,\n  target,\n  identifier,\n  active,\n  x, y, dx, dy,\n  dispatch\n}) {\n  Object.defineProperties(this, {\n    type: {value: type, enumerable: true, configurable: true},\n    sourceEvent: {value: sourceEvent, enumerable: true, configurable: true},\n    subject: {value: subject, enumerable: true, configurable: true},\n    target: {value: target, enumerable: true, configurable: true},\n    identifier: {value: identifier, enumerable: true, configurable: true},\n    active: {value: active, enumerable: true, configurable: true},\n    x: {value: x, enumerable: true, configurable: true},\n    y: {value: y, enumerable: true, configurable: true},\n    dx: {value: dx, enumerable: true, configurable: true},\n    dy: {value: dy, enumerable: true, configurable: true},\n    _: {value: dispatch}\n  });\n}\n\nDragEvent.prototype.on = function() {\n  var value = this._.on.apply(this._, arguments);\n  return value === this._ ? this : value;\n};\n", "import {dispatch} from \"d3-dispatch\";\nimport {select, pointer} from \"d3-selection\";\nimport nodrag, {yesdrag} from \"./nodrag.js\";\nimport noevent, {nonpassive, nonpassivecapture, nopropagation} from \"./noevent.js\";\nimport constant from \"./constant.js\";\nimport DragEvent from \"./event.js\";\n\n// Ignore right-click, since that should open the context menu.\nfunction defaultFilter(event) {\n  return !event.ctrlKey && !event.button;\n}\n\nfunction defaultContainer() {\n  return this.parentNode;\n}\n\nfunction defaultSubject(event, d) {\n  return d == null ? {x: event.x, y: event.y} : d;\n}\n\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || (\"ontouchstart\" in this);\n}\n\nexport default function() {\n  var filter = defaultFilter,\n      container = defaultContainer,\n      subject = defaultSubject,\n      touchable = defaultTouchable,\n      gestures = {},\n      listeners = dispatch(\"start\", \"drag\", \"end\"),\n      active = 0,\n      mousedownx,\n      mousedowny,\n      mousemoving,\n      touchending,\n      clickDistance2 = 0;\n\n  function drag(selection) {\n    selection\n        .on(\"mousedown.drag\", mousedowned)\n      .filter(touchable)\n        .on(\"touchstart.drag\", touchstarted)\n        .on(\"touchmove.drag\", touchmoved, nonpassive)\n        .on(\"touchend.drag touchcancel.drag\", touchended)\n        .style(\"touch-action\", \"none\")\n        .style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n\n  function mousedowned(event, d) {\n    if (touchending || !filter.call(this, event, d)) return;\n    var gesture = beforestart(this, container.call(this, event, d), event, d, \"mouse\");\n    if (!gesture) return;\n    select(event.view)\n      .on(\"mousemove.drag\", mousemoved, nonpassivecapture)\n      .on(\"mouseup.drag\", mouseupped, nonpassivecapture);\n    nodrag(event.view);\n    nopropagation(event);\n    mousemoving = false;\n    mousedownx = event.clientX;\n    mousedowny = event.clientY;\n    gesture(\"start\", event);\n  }\n\n  function mousemoved(event) {\n    noevent(event);\n    if (!mousemoving) {\n      var dx = event.clientX - mousedownx, dy = event.clientY - mousedowny;\n      mousemoving = dx * dx + dy * dy > clickDistance2;\n    }\n    gestures.mouse(\"drag\", event);\n  }\n\n  function mouseupped(event) {\n    select(event.view).on(\"mousemove.drag mouseup.drag\", null);\n    yesdrag(event.view, mousemoving);\n    noevent(event);\n    gestures.mouse(\"end\", event);\n  }\n\n  function touchstarted(event, d) {\n    if (!filter.call(this, event, d)) return;\n    var touches = event.changedTouches,\n        c = container.call(this, event, d),\n        n = touches.length, i, gesture;\n\n    for (i = 0; i < n; ++i) {\n      if (gesture = beforestart(this, c, event, d, touches[i].identifier, touches[i])) {\n        nopropagation(event);\n        gesture(\"start\", event, touches[i]);\n      }\n    }\n  }\n\n  function touchmoved(event) {\n    var touches = event.changedTouches,\n        n = touches.length, i, gesture;\n\n    for (i = 0; i < n; ++i) {\n      if (gesture = gestures[touches[i].identifier]) {\n        noevent(event);\n        gesture(\"drag\", event, touches[i]);\n      }\n    }\n  }\n\n  function touchended(event) {\n    var touches = event.changedTouches,\n        n = touches.length, i, gesture;\n\n    if (touchending) clearTimeout(touchending);\n    touchending = setTimeout(function() { touchending = null; }, 500); // Ghost clicks are delayed!\n    for (i = 0; i < n; ++i) {\n      if (gesture = gestures[touches[i].identifier]) {\n        nopropagation(event);\n        gesture(\"end\", event, touches[i]);\n      }\n    }\n  }\n\n  function beforestart(that, container, event, d, identifier, touch) {\n    var dispatch = listeners.copy(),\n        p = pointer(touch || event, container), dx, dy,\n        s;\n\n    if ((s = subject.call(that, new DragEvent(\"beforestart\", {\n        sourceEvent: event,\n        target: drag,\n        identifier,\n        active,\n        x: p[0],\n        y: p[1],\n        dx: 0,\n        dy: 0,\n        dispatch\n      }), d)) == null) return;\n\n    dx = s.x - p[0] || 0;\n    dy = s.y - p[1] || 0;\n\n    return function gesture(type, event, touch) {\n      var p0 = p, n;\n      switch (type) {\n        case \"start\": gestures[identifier] = gesture, n = active++; break;\n        case \"end\": delete gestures[identifier], --active; // falls through\n        case \"drag\": p = pointer(touch || event, container), n = active; break;\n      }\n      dispatch.call(\n        type,\n        that,\n        new DragEvent(type, {\n          sourceEvent: event,\n          subject: s,\n          target: drag,\n          identifier,\n          active: n,\n          x: p[0] + dx,\n          y: p[1] + dy,\n          dx: p[0] - p0[0],\n          dy: p[1] - p0[1],\n          dispatch\n        }),\n        d\n      );\n    };\n  }\n\n  drag.filter = function(_) {\n    return arguments.length ? (filter = typeof _ === \"function\" ? _ : constant(!!_), drag) : filter;\n  };\n\n  drag.container = function(_) {\n    return arguments.length ? (container = typeof _ === \"function\" ? _ : constant(_), drag) : container;\n  };\n\n  drag.subject = function(_) {\n    return arguments.length ? (subject = typeof _ === \"function\" ? _ : constant(_), drag) : subject;\n  };\n\n  drag.touchable = function(_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), drag) : touchable;\n  };\n\n  drag.on = function() {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? drag : value;\n  };\n\n  drag.clickDistance = function(_) {\n    return arguments.length ? (clickDistance2 = (_ = +_) * _, drag) : Math.sqrt(clickDistance2);\n  };\n\n  return drag;\n}\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport { drag } from \"d3-drag\";\n\timport { select } from \"d3-selection\";\n\n\tfunction clamp(value: number, min: number, max: number): number {\n\t\treturn Math.min(Math.max(value, min), max);\n\t}\n\n\texport let position = 0.5;\n\texport let disabled = false;\n\n\texport let slider_color = \"var(--border-color-primary)\";\n\texport let image_size: {\n\t\ttop: number;\n\t\tleft: number;\n\t\twidth: number;\n\t\theight: number;\n\t} = { top: 0, left: 0, width: 0, height: 0 };\n\texport let el: HTMLDivElement | undefined = undefined;\n\texport let parent_el: HTMLDivElement | undefined = undefined;\n\tlet inner: Element;\n\tlet px = 0;\n\tlet active = false;\n\tlet container_width = 0;\n\n\tfunction set_position(width: number): void {\n\t\tcontainer_width = parent_el?.getBoundingClientRect().width || 0;\n\t\tif (width === 0) {\n\t\t\timage_size.width = el?.getBoundingClientRect().width || 0;\n\t\t}\n\n\t\tpx = clamp(\n\t\t\timage_size.width * position + image_size.left,\n\t\t\t0,\n\t\t\tcontainer_width\n\t\t);\n\t}\n\n\tfunction round(n: number, points: number): number {\n\t\tconst mod = Math.pow(10, points);\n\t\treturn Math.round((n + Number.EPSILON) * mod) / mod;\n\t}\n\n\tfunction update_position(x: number): void {\n\t\tpx = clamp(x, 0, container_width);\n\t\tposition = round((x - image_size.left) / image_size.width, 5);\n\t}\n\n\tfunction drag_start(event: any): void {\n\t\tif (disabled) return;\n\t\tactive = true;\n\t\tupdate_position(event.x);\n\t}\n\n\tfunction drag_move(event: any): void {\n\t\tif (disabled) return;\n\t\tupdate_position(event.x);\n\t}\n\n\tfunction drag_end(): void {\n\t\tif (disabled) return;\n\t\tactive = false;\n\t}\n\n\tfunction update_position_from_pc(pc: number): void {\n\t\tpx = clamp(image_size.width * pc + image_size.left, 0, container_width);\n\t}\n\n\t$: set_position(image_size.width);\n\t$: update_position_from_pc(position);\n\n\tonMount(() => {\n\t\tset_position(image_size.width);\n\t\tconst drag_handler = drag()\n\t\t\t.on(\"start\", drag_start)\n\t\t\t.on(\"drag\", drag_move)\n\t\t\t.on(\"end\", drag_end);\n\t\tselect(inner).call(drag_handler);\n\t});\n</script>\n\n<svelte:window on:resize={() => set_position(image_size.width)} />\n\n<div class=\"wrap\" role=\"none\" bind:this={parent_el}>\n\t<div class=\"content\" bind:this={el}>\n\t\t<slot />\n\t</div>\n\t<div\n\t\tclass=\"outer\"\n\t\tclass:disabled\n\t\tbind:this={inner}\n\t\trole=\"none\"\n\t\tstyle=\"transform: translateX({px}px)\"\n\t\tclass:grab={active}\n\t>\n\t\t<span class=\"icon-wrap\" class:active class:disabled\n\t\t\t><span class=\"icon left\">◢</span><span\n\t\t\t\tclass=\"icon center\"\n\t\t\t\tstyle:--color={slider_color}\n\t\t\t></span><span class=\"icon right\">◢</span></span\n\t\t>\n\t\t<div class=\"inner\" style:--color={slider_color}></div>\n\t</div>\n</div>\n\n<style>\n\t.wrap {\n\t\tposition: relative;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tz-index: var(--layer-1);\n\t\toverflow: hidden;\n\t}\n\n\t.icon-wrap {\n\t\tdisplay: block;\n\t\tposition: absolute;\n\t\ttop: 50%;\n\t\ttransform: translate(-20.5px, -50%);\n\t\tleft: 10px;\n\t\twidth: 40px;\n\t\ttransition: 0.2s;\n\t\tcolor: var(--body-text-color);\n\t\theight: 30px;\n\t\tborder-radius: 5px;\n\t\tbackground-color: var(--color-accent);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tz-index: var(--layer-3);\n\t\tbox-shadow: 0px 0px 5px 2px rgba(0, 0, 0, 0.3);\n\t\tfont-size: 12px;\n\t}\n\n\t.icon.left {\n\t\ttransform: rotate(135deg);\n\t\ttext-shadow: -1px -1px 1px rgba(0, 0, 0, 0.1);\n\t}\n\n\t.icon.right {\n\t\ttransform: rotate(-45deg);\n\t\ttext-shadow: -1px -1px 1px rgba(0, 0, 0, 0.1);\n\t}\n\n\t.icon.center {\n\t\tdisplay: block;\n\t\twidth: 1px;\n\t\theight: 100%;\n\t\tbackground-color: var(--color);\n\t\topacity: 0.1;\n\t}\n\n\t.icon-wrap.active {\n\t\topacity: 0;\n\t}\n\n\t.icon-wrap.disabled {\n\t\topacity: 0;\n\t}\n\n\t.outer {\n\t\twidth: 20px;\n\t\theight: 100%;\n\t\tposition: absolute;\n\t\tcursor: grab;\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: -10px;\n\t\tpointer-events: auto;\n\t\tz-index: var(--layer-2);\n\t}\n\t.grab {\n\t\tcursor: grabbing;\n\t}\n\n\t.inner {\n\t\twidth: 1px;\n\t\theight: 100%;\n\t\tbackground: var(--color);\n\t\tposition: absolute;\n\t\tleft: calc((100% - 2px) / 2);\n\t}\n\n\t.disabled {\n\t\tcursor: auto;\n\t}\n\n\t.disabled .inner {\n\t\tbox-shadow: none;\n\t}\n\n\t.content {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport type { HTMLImgAttributes } from \"svelte/elements\";\n\timport { createEventDispatcher, onMount, tick } from \"svelte\";\n\tinterface Props extends HTMLImgAttributes {\n\t\t\"data-testid\"?: string;\n\t\tfixed?: boolean;\n\t\ttransform?: string;\n\t\timg_el?: HTMLImageElement;\n\t\thidden?: boolean;\n\t\tvariant?: \"preview\" | \"upload\";\n\t\tmax_height?: number;\n\t\tfullscreen?: boolean;\n\t}\n\ttype $$Props = Props;\n\n\timport { resolve_wasm_src } from \"@gradio/wasm/svelte\";\n\n\texport let src: HTMLImgAttributes[\"src\"] = undefined;\n\texport let fullscreen = false;\n\n\tlet resolved_src: typeof src;\n\n\texport let fixed = false;\n\texport let transform = \"translate(0px, 0px) scale(1)\";\n\texport let img_el: HTMLImageElement | null = null;\n\texport let hidden = false;\n\texport let variant = \"upload\";\n\texport let max_height = 500;\n\t// The `src` prop can be updated before the Promise from `resolve_wasm_src` is resolved.\n\t// In such a case, the resolved value for the old `src` has to be discarded,\n\t// This variable `latest_src` is used to pick up only the value resolved for the latest `src` prop.\n\tlet latest_src: typeof src;\n\t$: {\n\t\t// In normal (non-Wasm) Gradio, the `<img>` element should be rendered with the passed `src` props immediately\n\t\t// without waiting for `resolve_wasm_src()` to resolve.\n\t\t// If it waits, a blank image is displayed until the async task finishes\n\t\t// and it leads to undesirable flickering.\n\t\t// So set `src` to `resolved_src` here.\n\t\tresolved_src = src;\n\n\t\tlatest_src = src;\n\t\tconst resolving_src = src;\n\t\tresolve_wasm_src(resolving_src).then((s) => {\n\t\t\tif (latest_src === resolving_src) {\n\t\t\t\tresolved_src = s;\n\t\t\t}\n\t\t});\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tload: {\n\t\t\ttop: number;\n\t\t\tleft: number;\n\t\t\twidth: number;\n\t\t\theight: number;\n\t\t};\n\t}>();\n\n\tfunction get_image_size(img: HTMLImageElement | null): {\n\t\ttop: number;\n\t\tleft: number;\n\t\twidth: number;\n\t\theight: number;\n\t} {\n\t\tif (!img) return { top: 0, left: 0, width: 0, height: 0 };\n\t\tconst container = img.parentElement?.getBoundingClientRect();\n\n\t\tif (!container) return { top: 0, left: 0, width: 0, height: 0 };\n\n\t\tconst naturalAspect = img.naturalWidth / img.naturalHeight;\n\t\tconst containerAspect = container.width / container.height;\n\t\tlet displayedWidth, displayedHeight;\n\n\t\tif (naturalAspect > containerAspect) {\n\t\t\tdisplayedWidth = container.width;\n\t\t\tdisplayedHeight = container.width / naturalAspect;\n\t\t} else {\n\t\t\tdisplayedHeight = container.height;\n\t\t\tdisplayedWidth = container.height * naturalAspect;\n\t\t}\n\n\t\tconst offsetX = (container.width - displayedWidth) / 2;\n\t\tconst offsetY = (container.height - displayedHeight) / 2;\n\n\t\treturn {\n\t\t\ttop: offsetY,\n\t\t\tleft: offsetX,\n\t\t\twidth: displayedWidth,\n\t\t\theight: displayedHeight\n\t\t};\n\t}\n\n\tonMount(() => {\n\t\tconst resizer = new ResizeObserver(async (entries) => {\n\t\t\tfor (const entry of entries) {\n\t\t\t\tawait tick();\n\t\t\t\tdispatch(\"load\", get_image_size(img_el));\n\t\t\t}\n\t\t});\n\n\t\tresizer.observe(img_el!);\n\n\t\treturn () => {\n\t\t\tresizer.disconnect();\n\t\t};\n\t});\n</script>\n\n<!-- svelte-ignore a11y-missing-attribute -->\n<img\n\tsrc={resolved_src}\n\t{...$$restProps}\n\tclass:fixed\n\tstyle:transform\n\tbind:this={img_el}\n\tclass:hidden\n\tclass:preview={variant === \"preview\"}\n\tclass:slider={variant === \"upload\"}\n\tstyle:max-height={max_height && !fullscreen ? `${max_height}px` : null}\n\tclass:fullscreen\n\tclass:small={!fullscreen}\n\ton:load={() => dispatch(\"load\", get_image_size(img_el))}\n/>\n\n<style>\n\t.preview {\n\t\tobject-fit: contain;\n\t\twidth: 100%;\n\t\ttransform-origin: top left;\n\t\tmargin: auto;\n\t}\n\n\t.small {\n\t\tmax-height: 500px;\n\t}\n\n\t.upload {\n\t\tobject-fit: contain;\n\t\tmax-height: 500px;\n\t}\n\n\t.fixed {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t}\n\n\t.fullscreen {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t:global(.image-container:fullscreen) img {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tmax-height: none;\n\t\tmax-width: none;\n\t}\n\n\t.hidden {\n\t\topacity: 0;\n\t}\n</style>\n", "export class ZoomableImage {\n\tcontainer: HTMLDivElement;\n\timage: HTMLImageElement;\n\tscale: number;\n\toffsetX: number;\n\toffsetY: number;\n\tisDragging: boolean;\n\tlastX: number;\n\tlastY: number;\n\tinitial_left_padding: number;\n\tinitial_top_padding: number;\n\tinitial_width: number;\n\tinitial_height: number;\n\tsubscribers: (({\n\t\tx,\n\t\ty,\n\t\tscale\n\t}: {\n\t\tx: number;\n\t\ty: number;\n\t\tscale: number;\n\t}) => void)[];\n\thandleImageLoad: () => void;\n\treal_image_size: {\n\t\ttop: number;\n\t\tleft: number;\n\t\twidth: number;\n\t\theight: number;\n\t} = { top: 0, left: 0, width: 0, height: 0 };\n\n\tlast_touch_distance: number;\n\n\tconstructor(container: HTMLDivElement, image: HTMLImageElement) {\n\t\tthis.container = container;\n\t\tthis.image = image;\n\n\t\tthis.scale = 1;\n\t\tthis.offsetX = 0;\n\t\tthis.offsetY = 0;\n\t\tthis.isDragging = false;\n\t\tthis.lastX = 0;\n\t\tthis.lastY = 0;\n\t\tthis.initial_left_padding = 0;\n\t\tthis.initial_top_padding = 0;\n\t\tthis.initial_width = 0;\n\t\tthis.initial_height = 0;\n\t\tthis.subscribers = [];\n\t\tthis.last_touch_distance = 0;\n\n\t\tthis.handleWheel = this.handleWheel.bind(this);\n\t\tthis.handleMouseDown = this.handleMouseDown.bind(this);\n\t\tthis.handleMouseMove = this.handleMouseMove.bind(this);\n\t\tthis.handleMouseUp = this.handleMouseUp.bind(this);\n\t\tthis.handleImageLoad = this.init.bind(this);\n\t\tthis.handleTouchStart = this.handleTouchStart.bind(this);\n\t\tthis.handleTouchMove = this.handleTouchMove.bind(this);\n\t\tthis.handleTouchEnd = this.handleTouchEnd.bind(this);\n\n\t\tthis.image.addEventListener(\"load\", this.handleImageLoad);\n\n\t\tthis.container.addEventListener(\"wheel\", this.handleWheel);\n\t\tthis.container.addEventListener(\"mousedown\", this.handleMouseDown);\n\t\tdocument.addEventListener(\"mousemove\", this.handleMouseMove);\n\t\tdocument.addEventListener(\"mouseup\", this.handleMouseUp);\n\n\t\tthis.container.addEventListener(\"touchstart\", this.handleTouchStart);\n\t\tdocument.addEventListener(\"touchmove\", this.handleTouchMove);\n\t\tdocument.addEventListener(\"touchend\", this.handleTouchEnd);\n\n\t\tconst observer = new ResizeObserver((entries) => {\n\t\t\tfor (const entry of entries) {\n\t\t\t\tif (entry.target === this.container) {\n\t\t\t\t\tthis.handleResize();\n\t\t\t\t\tthis.get_image_size(this.image);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t\tobserver.observe(this.container);\n\t}\n\n\thandleResize(): void {\n\t\tthis.init();\n\t}\n\n\tinit(): void {\n\t\tconst containerRect = this.container.getBoundingClientRect();\n\n\t\tconst imageRect = this.image.getBoundingClientRect();\n\t\tthis.initial_left_padding = imageRect.left - containerRect.left;\n\t\tthis.initial_top_padding = imageRect.top - containerRect.top;\n\t\tthis.initial_width = imageRect.width;\n\t\tthis.initial_height = imageRect.height;\n\n\t\tthis.reset_zoom();\n\n\t\tthis.updateTransform();\n\t}\n\n\treset_zoom(): void {\n\t\tthis.scale = 1;\n\t\tthis.offsetX = 0;\n\t\tthis.offsetY = 0;\n\t\tthis.updateTransform();\n\t}\n\n\thandleMouseDown(e: MouseEvent): void {\n\t\tconst imageRect = this.image.getBoundingClientRect();\n\n\t\tif (\n\t\t\te.clientX >= imageRect.left &&\n\t\t\te.clientX <= imageRect.right &&\n\t\t\te.clientY >= imageRect.top &&\n\t\t\te.clientY <= imageRect.bottom\n\t\t) {\n\t\t\te.preventDefault();\n\t\t\tif (this.scale === 1) return;\n\t\t\tthis.isDragging = true;\n\t\t\tthis.lastX = e.clientX;\n\t\t\tthis.lastY = e.clientY;\n\t\t\tthis.image.style.cursor = \"grabbing\";\n\t\t}\n\t}\n\n\thandleMouseMove(e: MouseEvent): void {\n\t\tif (!this.isDragging) return;\n\n\t\tconst deltaX = e.clientX - this.lastX;\n\t\tconst deltaY = e.clientY - this.lastY;\n\n\t\tthis.offsetX += deltaX;\n\t\tthis.offsetY += deltaY;\n\n\t\tthis.lastX = e.clientX;\n\t\tthis.lastY = e.clientY;\n\n\t\tthis.updateTransform();\n\n\t\tthis.updateTransform();\n\t}\n\n\thandleMouseUp(): void {\n\t\tif (this.isDragging) {\n\t\t\tthis.constrain_to_bounds(true);\n\t\t\tthis.updateTransform();\n\t\t\tthis.isDragging = false;\n\t\t\tthis.image.style.cursor = this.scale > 1 ? \"grab\" : \"zoom-in\";\n\t\t}\n\t}\n\n\tasync handleWheel(e: WheelEvent): Promise<void> {\n\t\te.preventDefault();\n\n\t\tconst containerRect = this.container.getBoundingClientRect();\n\t\tconst imageRect = this.image.getBoundingClientRect();\n\n\t\tif (\n\t\t\te.clientX < imageRect.left ||\n\t\t\te.clientX > imageRect.right ||\n\t\t\te.clientY < imageRect.top ||\n\t\t\te.clientY > imageRect.bottom\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst zoomFactor = 1.05;\n\t\tconst oldScale = this.scale;\n\t\tconst newScale =\n\t\t\t-Math.sign(e.deltaY) > 0\n\t\t\t\t? Math.min(15, oldScale * zoomFactor) // in\n\t\t\t\t: Math.max(1, oldScale / zoomFactor); // out\n\n\t\tif (newScale === oldScale) return;\n\n\t\tconst cursorX = e.clientX - containerRect.left - this.initial_left_padding;\n\t\tconst cursorY = e.clientY - containerRect.top - this.initial_top_padding;\n\n\t\tthis.scale = newScale;\n\t\tthis.offsetX = this.compute_new_offset({\n\t\t\tcursor_position: cursorX,\n\t\t\tcurrent_offset: this.offsetX,\n\t\t\tnew_scale: newScale,\n\t\t\told_scale: oldScale\n\t\t});\n\t\tthis.offsetY = this.compute_new_offset({\n\t\t\tcursor_position: cursorY,\n\t\t\tcurrent_offset: this.offsetY,\n\t\t\tnew_scale: newScale,\n\t\t\told_scale: oldScale\n\t\t});\n\n\t\tthis.updateTransform(); // apply before constraints\n\n\t\tthis.constrain_to_bounds();\n\t\tthis.updateTransform(); // apply again after constraints\n\n\t\tthis.image.style.cursor = this.scale > 1 ? \"grab\" : \"zoom-in\";\n\t}\n\n\t// compute_offset_for_positions({ position: number, scale: number }) {\n\t// \treturn position - (scale / this.scale) * (position - this.offset);\n\t// }\n\n\tcompute_new_position({\n\t\tposition,\n\t\tscale,\n\t\tanchor_position\n\t}: {\n\t\tposition: number;\n\t\tscale: number;\n\t\tanchor_position: number;\n\t}): number {\n\t\treturn position - (position - anchor_position) * (scale / this.scale);\n\t}\n\n\tcompute_new_offset({\n\t\tcursor_position,\n\t\tcurrent_offset,\n\t\tnew_scale,\n\t\told_scale\n\t}: {\n\t\tcursor_position: number;\n\t\tcurrent_offset: number;\n\t\tnew_scale: number;\n\t\told_scale: number;\n\t}): number {\n\t\treturn (\n\t\t\tcursor_position -\n\t\t\t(new_scale / old_scale) * (cursor_position - current_offset)\n\t\t);\n\t}\n\n\tconstrain_to_bounds(pan = false): void {\n\t\tif (this.scale === 1) {\n\t\t\tthis.offsetX = 0;\n\t\t\tthis.offsetY = 0;\n\t\t\treturn;\n\t\t}\n\t\tconst onscreen = {\n\t\t\ttop: this.real_image_size.top * this.scale + this.offsetY,\n\t\t\tleft: this.real_image_size.left * this.scale + this.offsetX,\n\t\t\twidth: this.real_image_size.width * this.scale,\n\t\t\theight: this.real_image_size.height * this.scale,\n\n\t\t\tbottom:\n\t\t\t\tthis.real_image_size.top * this.scale +\n\t\t\t\tthis.offsetY +\n\t\t\t\tthis.real_image_size.height * this.scale,\n\t\t\tright:\n\t\t\t\tthis.real_image_size.left * this.scale +\n\t\t\t\tthis.offsetX +\n\t\t\t\tthis.real_image_size.width * this.scale\n\t\t};\n\n\t\tconst real_image_size_right =\n\t\t\tthis.real_image_size.left + this.real_image_size.width;\n\t\tconst real_image_size_bottom =\n\t\t\tthis.real_image_size.top + this.real_image_size.height;\n\n\t\tif (pan) {\n\t\t\tif (onscreen.top > this.real_image_size.top) {\n\t\t\t\tthis.offsetY = this.calculate_position(\n\t\t\t\t\tthis.real_image_size.top,\n\t\t\t\t\t0,\n\t\t\t\t\t\"y\"\n\t\t\t\t);\n\t\t\t} else if (onscreen.bottom < real_image_size_bottom) {\n\t\t\t\tthis.offsetY = this.calculate_position(real_image_size_bottom, 1, \"y\");\n\t\t\t}\n\n\t\t\tif (onscreen.left > this.real_image_size.left) {\n\t\t\t\tthis.offsetX = this.calculate_position(\n\t\t\t\t\tthis.real_image_size.left,\n\t\t\t\t\t0,\n\t\t\t\t\t\"x\"\n\t\t\t\t);\n\t\t\t} else if (onscreen.right < real_image_size_right) {\n\t\t\t\tthis.offsetX = this.calculate_position(real_image_size_right, 1, \"x\");\n\t\t\t}\n\t\t}\n\t}\n\n\tupdateTransform(): void {\n\t\tthis.notify({ x: this.offsetX, y: this.offsetY, scale: this.scale });\n\t}\n\n\tdestroy(): void {\n\t\tthis.container.removeEventListener(\"wheel\", this.handleWheel);\n\t\tthis.container.removeEventListener(\"mousedown\", this.handleMouseDown);\n\t\tdocument.removeEventListener(\"mousemove\", this.handleMouseMove);\n\t\tdocument.removeEventListener(\"mouseup\", this.handleMouseUp);\n\t\tthis.container.removeEventListener(\"touchstart\", this.handleTouchStart);\n\t\tdocument.removeEventListener(\"touchmove\", this.handleTouchMove);\n\t\tdocument.removeEventListener(\"touchend\", this.handleTouchEnd);\n\t\tthis.image.removeEventListener(\"load\", this.handleImageLoad);\n\t}\n\n\tsubscribe(\n\t\tcb: ({ x, y, scale }: { x: number; y: number; scale: number }) => void\n\t): void {\n\t\tthis.subscribers.push(cb);\n\t}\n\n\tunsubscribe(\n\t\tcb: ({ x, y, scale }: { x: number; y: number; scale: number }) => void\n\t): void {\n\t\tthis.subscribers = this.subscribers.filter(\n\t\t\t(subscriber) => subscriber !== cb\n\t\t);\n\t}\n\n\tnotify({ x, y, scale }: { x: number; y: number; scale: number }): void {\n\t\tthis.subscribers.forEach((subscriber) => subscriber({ x, y, scale }));\n\t}\n\n\thandleTouchStart(e: TouchEvent): void {\n\t\te.preventDefault();\n\t\tconst imageRect = this.image.getBoundingClientRect();\n\t\tconst touch = e.touches[0];\n\n\t\tif (\n\t\t\ttouch.clientX >= imageRect.left &&\n\t\t\ttouch.clientX <= imageRect.right &&\n\t\t\ttouch.clientY >= imageRect.top &&\n\t\t\ttouch.clientY <= imageRect.bottom\n\t\t) {\n\t\t\tif (e.touches.length === 1 && this.scale > 1) {\n\t\t\t\t// one finger == prepare pan\n\t\t\t\tthis.isDragging = true;\n\t\t\t\tthis.lastX = touch.clientX;\n\t\t\t\tthis.lastY = touch.clientY;\n\t\t\t} else if (e.touches.length === 2) {\n\t\t\t\t// two fingers == prepare pinch zoom\n\t\t\t\tconst touch1 = e.touches[0];\n\t\t\t\tconst touch2 = e.touches[1];\n\t\t\t\tthis.last_touch_distance = Math.hypot(\n\t\t\t\t\ttouch2.clientX - touch1.clientX,\n\t\t\t\t\ttouch2.clientY - touch1.clientY\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t}\n\n\tget_image_size(img: HTMLImageElement | null): void {\n\t\tif (!img) return;\n\t\tconst container = img.parentElement?.getBoundingClientRect();\n\n\t\tif (!container) return;\n\n\t\tconst naturalAspect = img.naturalWidth / img.naturalHeight;\n\t\tconst containerAspect = container.width / container.height;\n\t\tlet displayedWidth, displayedHeight;\n\n\t\tif (naturalAspect > containerAspect) {\n\t\t\tdisplayedWidth = container.width;\n\t\t\tdisplayedHeight = container.width / naturalAspect;\n\t\t} else {\n\t\t\tdisplayedHeight = container.height;\n\t\t\tdisplayedWidth = container.height * naturalAspect;\n\t\t}\n\n\t\tconst offsetX = (container.width - displayedWidth) / 2;\n\t\tconst offsetY = (container.height - displayedHeight) / 2;\n\n\t\tthis.real_image_size = {\n\t\t\ttop: offsetY,\n\t\t\tleft: offsetX,\n\t\t\twidth: displayedWidth,\n\t\t\theight: displayedHeight\n\t\t};\n\t}\n\n\thandleTouchMove(e: TouchEvent): void {\n\t\tif (e.touches.length === 1 && this.isDragging) {\n\t\t\t// one finger == pan\n\t\t\te.preventDefault();\n\t\t\tconst touch = e.touches[0];\n\n\t\t\tconst deltaX = touch.clientX - this.lastX;\n\t\t\tconst deltaY = touch.clientY - this.lastY;\n\n\t\t\tthis.offsetX += deltaX;\n\t\t\tthis.offsetY += deltaY;\n\n\t\t\tthis.lastX = touch.clientX;\n\t\t\tthis.lastY = touch.clientY;\n\n\t\t\tthis.updateTransform();\n\t\t} else if (e.touches.length === 2) {\n\t\t\t// two fingers == pinch zoom\n\t\t\te.preventDefault();\n\n\t\t\tconst touch1 = e.touches[0];\n\t\t\tconst touch2 = e.touches[1];\n\n\t\t\tconst current_distance = Math.hypot(\n\t\t\t\ttouch2.clientX - touch1.clientX,\n\t\t\t\ttouch2.clientY - touch1.clientY\n\t\t\t);\n\n\t\t\tif (this.last_touch_distance === 0) {\n\t\t\t\tthis.last_touch_distance = current_distance;\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst zoomFactor = current_distance / this.last_touch_distance;\n\n\t\t\tconst oldScale = this.scale;\n\t\t\tconst newScale = Math.min(15, Math.max(1, oldScale * zoomFactor));\n\n\t\t\tif (newScale === oldScale) {\n\t\t\t\tthis.last_touch_distance = current_distance;\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// midpoint of touches relative to image\n\t\t\tconst containerRect = this.container.getBoundingClientRect();\n\t\t\tconst midX =\n\t\t\t\t(touch1.clientX + touch2.clientX) / 2 -\n\t\t\t\tcontainerRect.left -\n\t\t\t\tthis.initial_left_padding;\n\t\t\tconst midY =\n\t\t\t\t(touch1.clientY + touch2.clientY) / 2 -\n\t\t\t\tcontainerRect.top -\n\t\t\t\tthis.initial_top_padding;\n\n\t\t\tthis.scale = newScale;\n\t\t\tthis.offsetX = this.compute_new_offset({\n\t\t\t\tcursor_position: midX,\n\t\t\t\tcurrent_offset: this.offsetX,\n\t\t\t\tnew_scale: newScale,\n\t\t\t\told_scale: oldScale\n\t\t\t});\n\t\t\tthis.offsetY = this.compute_new_offset({\n\t\t\t\tcursor_position: midY,\n\t\t\t\tcurrent_offset: this.offsetY,\n\t\t\t\tnew_scale: newScale,\n\t\t\t\told_scale: oldScale\n\t\t\t});\n\n\t\t\tthis.updateTransform();\n\t\t\tthis.constrain_to_bounds();\n\t\t\tthis.updateTransform();\n\n\t\t\tthis.last_touch_distance = current_distance;\n\n\t\t\tthis.image.style.cursor = this.scale > 1 ? \"grab\" : \"zoom-in\";\n\t\t}\n\t}\n\n\thandleTouchEnd(e: TouchEvent): void {\n\t\tif (this.isDragging) {\n\t\t\tthis.constrain_to_bounds(true);\n\t\t\tthis.updateTransform();\n\t\t\tthis.isDragging = false;\n\t\t}\n\n\t\tif (e.touches.length === 0) {\n\t\t\tthis.last_touch_distance = 0;\n\t\t}\n\t}\n\n\tcalculate_position(\n\t\tscreen_coord: number,\n\t\timage_anchor: number,\n\t\taxis: \"x\" | \"y\"\n\t): number {\n\t\tconst containerRect = this.container.getBoundingClientRect();\n\n\t\t// Calculate X offset if requested\n\t\tif (axis === \"x\") {\n\t\t\tconst relative_screen_x = screen_coord;\n\t\t\tconst anchor_x =\n\t\t\t\tthis.real_image_size.left + image_anchor * this.real_image_size.width;\n\t\t\treturn relative_screen_x - anchor_x * this.scale;\n\t\t}\n\n\t\t// Calculate Y offset if requested\n\t\tif (axis === \"y\") {\n\t\t\tconst relative_screen_y = screen_coord;\n\t\t\tconst anchor_y =\n\t\t\t\tthis.real_image_size.top + image_anchor * this.real_image_size.height;\n\t\t\treturn relative_screen_y - anchor_y * this.scale;\n\t\t}\n\n\t\treturn 0;\n\t}\n}\n", "<script lang=\"ts\">\n\timport Slider from \"./Slider.svelte\";\n\timport ImageEl from \"./ImageEl.svelte\";\n\timport {\n\t\tBlockLabel,\n\t\tEmpty,\n\t\tIconButton,\n\t\tIconButtonWrapper,\n\t\tFullscreenButton\n\t} from \"@gradio/atoms\";\n\timport { Image, Download, Undo, Clear } from \"@gradio/icons\";\n\timport { type FileData } from \"@gradio/client\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { DownloadLink } from \"@gradio/wasm/svelte\";\n\timport { ZoomableImage } from \"./zoom\";\n\timport { onMount } from \"svelte\";\n\timport { tweened, type Tweened } from \"svelte/motion\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\texport let value: [null | FileData, null | FileData] = [null, null];\n\texport let label: string | undefined = undefined;\n\texport let show_download_button = true;\n\texport let show_label: boolean;\n\texport let i18n: I18nFormatter;\n\texport let position: number;\n\texport let layer_images = true;\n\texport let show_single = false;\n\texport let slider_color: string;\n\texport let show_fullscreen_button = true;\n\texport let fullscreen = false;\n\texport let el_width = 0;\n\texport let max_height: number;\n\texport let interactive = true;\n\tconst dispatch = createEventDispatcher<{ clear: void }>();\n\n\tlet img: HTMLImageElement;\n\tlet slider_wrap: HTMLDivElement;\n\tlet image_container: HTMLDivElement;\n\n\tlet transform: Tweened<{ x: number; y: number; z: number }> = tweened(\n\t\t{ x: 0, y: 0, z: 1 },\n\t\t{\n\t\t\tduration: 75\n\t\t}\n\t);\n\tlet parent_el: HTMLDivElement;\n\n\t$: coords_at_viewport = get_coords_at_viewport(\n\t\tposition,\n\t\tviewport_width,\n\t\timage_size.width,\n\t\timage_size.left,\n\t\t$transform.x,\n\t\t$transform.z\n\t);\n\t$: style = layer_images\n\t\t? `clip-path: inset(0 0 0 ${coords_at_viewport * 100}%)`\n\t\t: \"\";\n\n\tfunction get_coords_at_viewport(\n\t\tviewport_percent_x: number, // 0-1\n\t\tviewportWidth: number,\n\t\timage_width: number,\n\t\timg_offset_x: number,\n\t\ttx: number, // image translation x (in pixels)\n\t\tscale: number // image scale (uniform)\n\t): number {\n\t\tconst px_relative_to_image = viewport_percent_x * image_width;\n\t\tconst pixel_position = px_relative_to_image + img_offset_x;\n\n\t\tconst normalised_position = (pixel_position - tx) / scale;\n\t\tconst percent_position = normalised_position / viewportWidth;\n\n\t\treturn percent_position;\n\t}\n\n\tlet img_width = 0;\n\tlet viewport_width = 0;\n\n\tlet zoomable_image: ZoomableImage | null = null;\n\tlet observer: ResizeObserver | null = null;\n\n\tfunction init_image(\n\t\timg: HTMLImageElement,\n\t\tslider_wrap: HTMLDivElement\n\t): void {\n\t\tif (!img || !slider_wrap) return;\n\t\tzoomable_image?.destroy();\n\t\tobserver?.disconnect();\n\t\timg_width = img?.getBoundingClientRect().width || 0;\n\t\tviewport_width = slider_wrap?.getBoundingClientRect().width || 0;\n\t\tzoomable_image = new ZoomableImage(slider_wrap, img);\n\t\tzoomable_image.subscribe(({ x, y, scale }) => {\n\t\t\ttransform.set({ x, y, z: scale });\n\t\t});\n\n\t\tobserver = new ResizeObserver((entries) => {\n\t\t\tfor (const entry of entries) {\n\t\t\t\tif (entry.target === slider_wrap) {\n\t\t\t\t\tviewport_width = entry.contentRect.width;\n\t\t\t\t}\n\n\t\t\t\tif (entry.target === img) {\n\t\t\t\t\timg_width = entry.contentRect.width;\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t\tobserver.observe(slider_wrap);\n\t\tobserver.observe(img);\n\t}\n\n\t$: init_image(img, slider_wrap);\n\n\tonMount(() => {\n\t\treturn () => {\n\t\t\tzoomable_image?.destroy();\n\t\t\tobserver?.disconnect();\n\t\t};\n\t});\n\n\tlet slider_wrap_parent: HTMLDivElement;\n\n\tlet image_size: { top: number; left: number; width: number; height: number } =\n\t\t{ top: 0, left: 0, width: 0, height: 0 };\n\n\tfunction handle_image_load(event: CustomEvent): void {\n\t\timage_size = event.detail;\n\t}\n</script>\n\n<BlockLabel {show_label} Icon={Image} label={label || i18n(\"image.image\")} />\n{#if (value === null || value[0] === null || value[1] === null) && !show_single}\n\t<Empty unpadded_box={true} size=\"large\"><Image /></Empty>\n{:else}\n\t<div class=\"image-container\" bind:this={image_container}>\n\t\t<IconButtonWrapper>\n\t\t\t<IconButton\n\t\t\t\tIcon={Undo}\n\t\t\t\tlabel={i18n(\"common.undo\")}\n\t\t\t\tdisabled={$transform.z === 1}\n\t\t\t\ton:click={() => zoomable_image?.reset_zoom()}\n\t\t\t/>\n\t\t\t{#if show_fullscreen_button}\n\t\t\t\t<FullscreenButton {fullscreen} on:fullscreen />\n\t\t\t{/if}\n\n\t\t\t{#if show_download_button}\n\t\t\t\t<DownloadLink\n\t\t\t\t\thref={value[1]?.url}\n\t\t\t\t\tdownload={value[1]?.orig_name || \"image\"}\n\t\t\t\t>\n\t\t\t\t\t<IconButton Icon={Download} label={i18n(\"common.download\")} />\n\t\t\t\t</DownloadLink>\n\t\t\t{/if}\n\t\t\t{#if interactive}\n\t\t\t\t<IconButton\n\t\t\t\t\tIcon={Clear}\n\t\t\t\t\tlabel=\"Remove Image\"\n\t\t\t\t\ton:click={(event) => {\n\t\t\t\t\t\tvalue = [null, null];\n\t\t\t\t\t\tdispatch(\"clear\");\n\t\t\t\t\t\tevent.stopPropagation();\n\t\t\t\t\t}}\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t</IconButtonWrapper>\n\t\t<div\n\t\t\tclass=\"slider-wrap\"\n\t\t\tbind:this={slider_wrap_parent}\n\t\t\tbind:clientWidth={el_width}\n\t\t\tclass:limit_height={!fullscreen}\n\t\t>\n\t\t\t<Slider\n\t\t\t\tbind:position\n\t\t\t\t{slider_color}\n\t\t\t\tbind:el={slider_wrap}\n\t\t\t\tbind:parent_el\n\t\t\t\t{image_size}\n\t\t\t>\n\t\t\t\t<ImageEl\n\t\t\t\t\tsrc={value?.[0]?.url}\n\t\t\t\t\talt=\"\"\n\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\tbind:img_el={img}\n\t\t\t\t\tvariant=\"preview\"\n\t\t\t\t\ttransform=\"translate({$transform.x}px, {$transform.y}px) scale({$transform.z})\"\n\t\t\t\t\t{fullscreen}\n\t\t\t\t\t{max_height}\n\t\t\t\t\ton:load={handle_image_load}\n\t\t\t\t/>\n\t\t\t\t<ImageEl\n\t\t\t\t\tvariant=\"preview\"\n\t\t\t\t\tfixed={layer_images}\n\t\t\t\t\thidden={!value?.[1]?.url}\n\t\t\t\t\tsrc={value?.[1]?.url}\n\t\t\t\t\talt=\"\"\n\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\tstyle=\"{style}; background: var(--block-background-fill);\"\n\t\t\t\t\ttransform=\"translate({$transform.x}px, {$transform.y}px) scale({$transform.z})\"\n\t\t\t\t\t{fullscreen}\n\t\t\t\t\t{max_height}\n\t\t\t\t\ton:load={handle_image_load}\n\t\t\t\t/>\n\t\t\t</Slider>\n\t\t</div>\n\t</div>\n{/if}\n\n<style>\n\t.slider-wrap {\n\t\tuser-select: none;\n\t\theight: 100%;\n\t\twidth: 100%;\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.limit_height :global(img) {\n\t\tmax-height: 500px;\n\t}\n\n\t.image-container {\n\t\theight: 100%;\n\t\tposition: relative;\n\t\tmin-width: var(--size-20);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { IconButton } from \"@gradio/atoms\";\n\timport { Clear } from \"@gradio/icons\";\n\n\tconst dispatch = createEventDispatcher();\n</script>\n\n<div>\n\t<IconButton\n\t\tIcon={Clear}\n\t\tlabel=\"Remove Image\"\n\t\ton:click={(event) => {\n\t\t\tdispatch(\"remove_image\");\n\t\t\tevent.stopPropagation();\n\t\t}}\n\t/>\n</div>\n\n<style>\n\tdiv {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\ttop: var(--size-2);\n\t\tright: var(--size-2);\n\t\tjustify-content: flex-end;\n\t\tgap: var(--spacing-sm);\n\t\tz-index: var(--layer-5);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport Slider from \"./Slider.svelte\";\n\timport { createEventDispatcher, tick } from \"svelte\";\n\timport { BlockLabel, Empty, IconButton } from \"@gradio/atoms\";\n\timport { Download } from \"@gradio/icons\";\n\timport { Image } from \"@gradio/icons\";\n\timport { type SelectData, type I18nFormatter } from \"@gradio/utils\";\n\timport ClearImage from \"./ClearImage.svelte\";\n\timport ImageEl from \"./ImageEl.svelte\";\n\n\timport { Upload } from \"@gradio/upload\";\n\timport { DownloadLink } from \"@gradio/wasm/svelte\";\n\n\timport { type FileData, type Client } from \"@gradio/client\";\n\n\texport let value: [FileData | null, FileData | null];\n\n\texport let label: string | undefined = undefined;\n\texport let show_label: boolean;\n\texport let root: string;\n\texport let position: number;\n\texport let upload_count = 2;\n\n\texport let show_download_button = true;\n\texport let slider_color: string;\n\texport let upload: Client[\"upload\"];\n\texport let stream_handler: Client[\"stream\"];\n\texport let max_file_size: number | null = null;\n\texport let i18n: I18nFormatter;\n\texport let max_height: number;\n\n\tlet value_: [FileData | null, FileData | null] = value || [null, null];\n\n\tlet img: HTMLImageElement;\n\tlet el_width: number;\n\tlet el_height: number;\n\n\tasync function handle_upload(\n\t\t{ detail }: CustomEvent<FileData[]>,\n\t\tn: number\n\t): Promise<void> {\n\t\tconst new_value = [value[0], value[1]] as [\n\t\t\tFileData | null,\n\t\t\tFileData | null\n\t\t];\n\t\tif (detail.length > 1) {\n\t\t\tnew_value[n] = detail[0];\n\t\t} else {\n\t\t\tnew_value[n] = detail[n];\n\t\t}\n\t\tvalue = new_value;\n\t\tawait tick();\n\n\t\tdispatch(\"upload\", new_value);\n\t}\n\n\tlet old_value = \"\";\n\n\t$: if (JSON.stringify(value) !== old_value) {\n\t\told_value = JSON.stringify(value);\n\t\tvalue_ = value;\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: string | null;\n\t\tstream: string | null;\n\t\tedit: undefined;\n\t\tclear: undefined;\n\t\tdrag: boolean;\n\t\tupload: [FileData | null, FileData | null];\n\t\tselect: SelectData;\n\t}>();\n\n\texport let dragging = false;\n\n\t$: dispatch(\"drag\", dragging);\n</script>\n\n<BlockLabel {show_label} Icon={Image} label={label || i18n(\"image.image\")} />\n\n<div\n\tdata-testid=\"image\"\n\tclass=\"image-container\"\n\tbind:clientWidth={el_width}\n\tbind:clientHeight={el_height}\n>\n\t{#if value?.[0]?.url || value?.[1]?.url}\n\t\t<ClearImage\n\t\t\ton:remove_image={() => {\n\t\t\t\tposition = 0.5;\n\t\t\t\tvalue = [null, null];\n\t\t\t\tdispatch(\"clear\");\n\t\t\t}}\n\t\t/>\n\t{/if}\n\t{#if value?.[1]?.url}\n\t\t<div class=\"icon-buttons\">\n\t\t\t{#if show_download_button}\n\t\t\t\t<DownloadLink\n\t\t\t\t\thref={value[1].url}\n\t\t\t\t\tdownload={value[1].orig_name || \"image\"}\n\t\t\t\t>\n\t\t\t\t\t<IconButton Icon={Download} />\n\t\t\t\t</DownloadLink>\n\t\t\t{/if}\n\t\t</div>\n\t{/if}\n\t<Slider\n\t\tbind:position\n\t\tdisabled={upload_count == 2 || !value?.[0]}\n\t\t{slider_color}\n\t>\n\t\t<div\n\t\t\tclass=\"upload-wrap\"\n\t\t\tstyle:display={upload_count === 2 ? \"flex\" : \"block\"}\n\t\t\tclass:side-by-side={upload_count === 2}\n\t\t>\n\t\t\t{#if !value_?.[0]}\n\t\t\t\t<div class=\"wrap\" class:half-wrap={upload_count === 1}>\n\t\t\t\t\t<Upload\n\t\t\t\t\t\tbind:dragging\n\t\t\t\t\t\tfiletype=\"image/*\"\n\t\t\t\t\t\ton:load={(e) => handle_upload(e, 0)}\n\t\t\t\t\t\tdisable_click={!!value?.[0]}\n\t\t\t\t\t\t{root}\n\t\t\t\t\t\tfile_count=\"multiple\"\n\t\t\t\t\t\t{upload}\n\t\t\t\t\t\t{stream_handler}\n\t\t\t\t\t\t{max_file_size}\n\t\t\t\t\t>\n\t\t\t\t\t\t<slot />\n\t\t\t\t\t</Upload>\n\t\t\t\t</div>\n\t\t\t{:else}\n\t\t\t\t<ImageEl\n\t\t\t\t\tvariant=\"upload\"\n\t\t\t\t\tsrc={value_[0]?.url}\n\t\t\t\t\talt=\"\"\n\t\t\t\t\tbind:img_el={img}\n\t\t\t\t\t{max_height}\n\t\t\t\t/>\n\t\t\t{/if}\n\n\t\t\t{#if !value_?.[1] && upload_count === 2}\n\t\t\t\t<Upload\n\t\t\t\t\tbind:dragging\n\t\t\t\t\tfiletype=\"image/*\"\n\t\t\t\t\ton:load={(e) => handle_upload(e, 1)}\n\t\t\t\t\tdisable_click={!!value?.[1]}\n\t\t\t\t\t{root}\n\t\t\t\t\tfile_count=\"multiple\"\n\t\t\t\t\t{upload}\n\t\t\t\t\t{stream_handler}\n\t\t\t\t\t{max_file_size}\n\t\t\t\t>\n\t\t\t\t\t<slot />\n\t\t\t\t</Upload>\n\t\t\t{:else if !value_?.[1] && upload_count === 1}\n\t\t\t\t<div\n\t\t\t\t\tclass=\"empty-wrap fixed\"\n\t\t\t\t\tstyle:width=\"{el_width * (1 - position)}px\"\n\t\t\t\t\tstyle:transform=\"translateX({el_width * position}px)\"\n\t\t\t\t\tclass:white-icon={!value?.[0]?.url}\n\t\t\t\t>\n\t\t\t\t\t<Empty unpadded_box={true} size=\"large\"><Image /></Empty>\n\t\t\t\t</div>\n\t\t\t{:else if value_?.[1]}\n\t\t\t\t<ImageEl\n\t\t\t\t\tvariant=\"upload\"\n\t\t\t\t\tsrc={value_[1].url}\n\t\t\t\t\talt=\"\"\n\t\t\t\t\tfixed={upload_count === 1}\n\t\t\t\t\ttransform=\"translate(0px, 0px) scale(1)\"\n\t\t\t\t\t{max_height}\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t</div>\n\t</Slider>\n</div>\n\n<style>\n\t.upload-wrap {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\theight: 100%;\n\t\twidth: 100%;\n\t}\n\n\t.wrap {\n\t\twidth: 100%;\n\t}\n\n\t.half-wrap {\n\t\twidth: 50%;\n\t}\n\t.image-container,\n\t.empty-wrap {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.fixed {\n\t\t--anim-block-background-fill: 255, 255, 255;\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tbackground-color: rgba(var(--anim-block-background-fill), 0.8);\n\t\tz-index: 0;\n\t}\n\n\t@media (prefers-color-scheme: dark) {\n\t\t.fixed {\n\t\t\t--anim-block-background-fill: 31, 41, 55;\n\t\t}\n\t}\n\n\t.side-by-side :global(img) {\n\t\t/* width: 100%; */\n\t\twidth: 50%;\n\t\tobject-fit: contain;\n\t}\n\n\t.empty-wrap {\n\t\tpointer-events: none;\n\t}\n\n\t.icon-buttons {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tright: 8px;\n\t\tz-index: var(--layer-top);\n\t\ttop: 8px;\n\t}\n</style>\n", "<svelte:options accessors={true} />\n\n<script lang=\"ts\">\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport Image from \"./Image.svelte\";\n\timport { type Client } from \"@gradio/client\";\n\n\timport type { FileData } from \"@gradio/client\";\n\n\texport let value: [FileData | null, FileData | null] = [null, null];\n\texport let upload: Client[\"upload\"];\n\texport let stream_handler: Client[\"stream\"];\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let i18n: I18nFormatter;\n\texport let root: string;\n\texport let upload_count = 1;\n\texport let dragging: boolean;\n\texport let max_height: number;\n\texport let max_file_size: number | null = null;\n</script>\n\n<Image\n\tslider_color=\"var(--border-color-primary)\"\n\tposition={0.5}\n\tbind:value\n\tbind:dragging\n\t{root}\n\ton:edit\n\ton:clear\n\ton:stream\n\ton:drag={({ detail }) => (dragging = detail)}\n\ton:upload\n\ton:select\n\ton:share\n\t{label}\n\t{show_label}\n\t{upload_count}\n\t{stream_handler}\n\t{upload}\n\t{max_file_size}\n\t{max_height}\n\t{i18n}\n>\n\t<slot />\n</Image>\n", "<svelte:options accessors={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData, ValueData } from \"@gradio/utils\";\n\timport StaticImage from \"./shared/SliderPreview.svelte\";\n\timport ImageUploader from \"./shared/SliderUpload.svelte\";\n\timport { afterUpdate } from \"svelte\";\n\n\timport { Block, Empty, UploadText } from \"@gradio/atoms\";\n\timport { Image } from \"@gradio/icons\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport { type FileData } from \"@gradio/client\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\ttype sources = \"upload\" | \"webcam\" | \"clipboard\" | null;\n\n\texport let value_is_output = false;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: [FileData | null, FileData | null] = [null, null];\n\tlet old_value: [FileData | null, FileData | null] = [null, null];\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let show_download_button: boolean;\n\texport let root: string;\n\texport let height: number | undefined;\n\texport let width: number | undefined;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let interactive: boolean;\n\texport let placeholder: string | undefined = undefined;\n\texport let show_fullscreen_button: boolean;\n\tlet fullscreen = false;\n\texport let input_ready: boolean;\n\texport let slider_position: number;\n\texport let upload_count = 1;\n\texport let slider_color = \"var(--border-color-primary)\";\n\texport let max_height: number;\n\tlet uploading = false;\n\n\t$: normalised_slider_position =\n\t\tMath.max(0, Math.min(100, slider_position)) / 100;\n\n\t$: input_ready = !uploading;\n\n\texport let gradio: Gradio<{\n\t\tinput: never;\n\t\tchange: never;\n\t\terror: string;\n\t\tedit: never;\n\t\tstream: ValueData;\n\t\tdrag: never;\n\t\tupload: never;\n\t\tclear: never;\n\t\tselect: SelectData;\n\t\tshare: ShareData;\n\t\tclear_status: LoadingStatus;\n\t\tclose_stream: string;\n\t}>;\n\n\t$: {\n\t\tif (JSON.stringify(value) !== JSON.stringify(old_value)) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t\tif (!value_is_output) {\n\t\t\t\tgradio.dispatch(\"input\");\n\t\t\t}\n\t\t}\n\t}\n\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\n\tlet dragging: boolean;\n\tlet active_source: sources = null;\n\tlet upload_component: ImageUploader;\n\n\tconst handle_drag_event = (event: Event): void => {\n\t\tconst drag_event = event as DragEvent;\n\t\tdrag_event.preventDefault();\n\t\tdrag_event.stopPropagation();\n\t\tif (drag_event.type === \"dragenter\" || drag_event.type === \"dragover\") {\n\t\t\tdragging = true;\n\t\t} else if (drag_event.type === \"dragleave\") {\n\t\t\tdragging = false;\n\t\t}\n\t};\n\n\tconst handle_drop = (event: Event): void => {\n\t\tif (interactive) {\n\t\t\tconst drop_event = event as DragEvent;\n\t\t\tdrop_event.preventDefault();\n\t\t\tdrop_event.stopPropagation();\n\t\t\tdragging = false;\n\n\t\t\tif (upload_component) {\n\t\t\t\tupload_component.loadFilesFromDrop(drop_event);\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n{#if !interactive || (value?.[1] && value?.[0])}\n\t<Block\n\t\t{visible}\n\t\tvariant={\"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\theight={height || undefined}\n\t\t{width}\n\t\tallow_overflow={false}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t\tbind:fullscreen\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t/>\n\t\t<StaticImage\n\t\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\t\ton:share={({ detail }) => gradio.dispatch(\"share\", detail)}\n\t\t\ton:error={({ detail }) => gradio.dispatch(\"error\", detail)}\n\t\t\ton:clear={() => gradio.dispatch(\"clear\")}\n\t\t\ton:fullscreen={({ detail }) => {\n\t\t\t\tfullscreen = detail;\n\t\t\t}}\n\t\t\t{fullscreen}\n\t\t\t{interactive}\n\t\t\tbind:value\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{show_download_button}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{show_fullscreen_button}\n\t\t\tposition={normalised_slider_position}\n\t\t\t{slider_color}\n\t\t\t{max_height}\n\t\t/>\n\t</Block>\n{:else}\n\t<Block\n\t\t{visible}\n\t\tvariant={value === null ? \"dashed\" : \"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\theight={height || undefined}\n\t\t{width}\n\t\tallow_overflow={false}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t\ton:dragenter={handle_drag_event}\n\t\ton:dragleave={handle_drag_event}\n\t\ton:dragover={handle_drag_event}\n\t\ton:drop={handle_drop}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\n\t\t<ImageUploader\n\t\t\tbind:this={upload_component}\n\t\t\tbind:value\n\t\t\tbind:dragging\n\t\t\t{root}\n\t\t\ton:edit={() => gradio.dispatch(\"edit\")}\n\t\t\ton:clear={() => {\n\t\t\t\tgradio.dispatch(\"clear\");\n\t\t\t}}\n\t\t\ton:drag={({ detail }) => (dragging = detail)}\n\t\t\ton:upload={() => gradio.dispatch(\"upload\")}\n\t\t\ton:error={({ detail }) => {\n\t\t\t\tloading_status = loading_status || {};\n\t\t\t\tloading_status.status = \"error\";\n\t\t\t\tgradio.dispatch(\"error\", detail);\n\t\t\t}}\n\t\t\ton:close_stream={() => {\n\t\t\t\tgradio.dispatch(\"close_stream\", \"stream\");\n\t\t\t}}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{upload_count}\n\t\t\tmax_file_size={gradio.max_file_size}\n\t\t\ti18n={gradio.i18n}\n\t\t\tupload={(...args) => gradio.client.upload(...args)}\n\t\t\tstream_handler={gradio.client?.stream}\n\t\t\t{max_height}\n\t\t>\n\t\t\t{#if active_source === \"upload\" || !active_source}\n\t\t\t\t<UploadText i18n={gradio.i18n} type=\"image\" {placeholder} />\n\t\t\t{:else if active_source === \"clipboard\"}\n\t\t\t\t<UploadText i18n={gradio.i18n} type=\"clipboard\" mode=\"short\" />\n\t\t\t{:else}\n\t\t\t\t<Empty unpadded_box={true} size=\"large\"><Image /></Empty>\n\t\t\t{/if}\n\t\t</ImageUploader>\n\t</Block>\n{/if}\n"], "names": ["sourceEvent", "event", "pointer", "node", "svg", "point", "rect", "nonpassive", "nonpassivecapture", "nopropagation", "noevent", "dragDisable", "view", "root", "selection", "select", "yesdrag", "noclick", "constant", "x", "DragEvent", "type", "subject", "target", "identifier", "active", "y", "dx", "dy", "dispatch", "value", "defaultFilter", "defaultContainer", "defaultSubject", "d", "defaultTouchable", "drag", "filter", "container", "touchable", "gestures", "listeners", "mousedownx", "mousedowny", "mousemoving", "touchending", "clickDistance2", "mousedowned", "touchstarted", "touchmoved", "touchended", "gesture", "beforestart", "mousemoved", "mouseupped", "nodrag", "touches", "c", "n", "i", "that", "touch", "p", "s", "p0", "_", "ctx", "insert_hydration", "div3", "anchor", "append_hydration", "div0", "div2", "span3", "span0", "span1", "span2", "div1", "clamp", "min", "max", "round", "points", "mod", "position", "$$props", "disabled", "slider_color", "image_size", "el", "parent_el", "inner", "px", "container_width", "set_position", "width", "$$invalidate", "update_position", "drag_start", "drag_move", "drag_end", "update_position_from_pc", "pc", "onMount", "drag_handler", "$$value", "toggle_class", "img", "set_style", "img_src_value", "get_image_size", "_a", "naturalAspect", "containerAspect", "displayedWidth", "displayedHeight", "offsetX", "src", "fullscreen", "resolved_src", "fixed", "transform", "img_el", "hidden", "variant", "max_height", "latest_src", "createEventDispatcher", "resizer", "entries", "entry", "tick", "load_handler", "resolving_src", "resolve_wasm_src", "ZoomableImage", "image", "__publicField", "containerRect", "imageRect", "deltaX", "deltaY", "zoomFactor", "oldScale", "newScale", "cursorX", "cursorY", "scale", "anchor_position", "cursor_position", "current_offset", "new_scale", "old_scale", "pan", "onscreen", "real_image_size_right", "real_image_size_bottom", "cb", "subscriber", "touch1", "touch2", "offsetY", "current_distance", "midX", "midY", "screen_coord", "image_anchor", "axis", "relative_screen_x", "anchor_x", "relative_screen_y", "anchor_y", "_b", "dirty", "downloadlink_changes", "Download", "iconbutton_changes", "Clear", "Undo", "create_if_block_3", "create_if_block_2", "create_if_block_1", "_d", "_c", "_f", "_e", "Image", "get_coords_at_viewport", "viewport_percent_x", "viewportWidth", "image_width", "img_offset_x", "tx", "label", "show_download_button", "show_label", "i18n", "layer_images", "show_single", "show_fullscreen_button", "el_width", "interactive", "slider_wrap", "image_container", "tweened", "viewport_width", "zoomable_image", "observer", "init_image", "slider_wrap_parent", "handle_image_load", "click_handler", "coords_at_viewport", "$transform", "style", "div", "create_if_block_5", "upload_1_changes", "imageel_changes", "style_transform", "create_if_block_6", "create_if_block_4", "_g", "slider_changes", "upload_count", "upload", "stream_handler", "max_file_size", "value_", "el_height", "handle_upload", "detail", "new_value", "old_value", "dragging", "e", "load_handler_1", "block_changes", "uploadtext_changes", "imageuploader_changes", "staticimage_changes", "uploading", "value_is_output", "elem_id", "elem_classes", "visible", "height", "min_width", "loading_status", "placeholder", "input_ready", "slider_position", "gradio", "afterUpdate", "upload_component", "handle_drag_event", "drag_event", "handle_drop", "drop_event", "select_handler", "share_handler", "error_handler", "clear_status_handler", "func", "args", "normalised_slider_position"], "mappings": "kyDAAe,SAAQA,GAACC,EAAO,CAC7B,IAAID,EACJ,KAAOA,EAAcC,EAAM,aAAaA,EAAQD,EAChD,OAAOC,CACT,CCFe,SAAAC,GAASD,EAAOE,EAAM,CAGnC,GAFAF,EAAQD,GAAYC,CAAK,EACrBE,IAAS,SAAWA,EAAOF,EAAM,eACjCE,EAAM,CACR,IAAIC,EAAMD,EAAK,iBAAmBA,EAClC,GAAIC,EAAI,eAAgB,CACtB,IAAIC,EAAQD,EAAI,iBAChB,OAAAC,EAAM,EAAIJ,EAAM,QAASI,EAAM,EAAIJ,EAAM,QACzCI,EAAQA,EAAM,gBAAgBF,EAAK,aAAc,EAAC,QAAO,CAAE,EACpD,CAACE,EAAM,EAAGA,EAAM,CAAC,CACzB,CACD,GAAIF,EAAK,sBAAuB,CAC9B,IAAIG,EAAOH,EAAK,wBAChB,MAAO,CAACF,EAAM,QAAUK,EAAK,KAAOH,EAAK,WAAYF,EAAM,QAAUK,EAAK,IAAMH,EAAK,SAAS,CAC/F,CACF,CACD,MAAO,CAACF,EAAM,MAAOA,EAAM,KAAK,CAClC,CCjBO,MAAMM,GAAa,CAAC,QAAS,EAAK,EAC5BC,GAAoB,CAAC,QAAS,GAAM,QAAS,EAAK,EAExD,SAASC,GAAcR,EAAO,CACnCA,EAAM,yBAAwB,CAChC,CAEe,SAAQS,GAACT,EAAO,CAC7BA,EAAM,eAAc,EACpBA,EAAM,yBAAwB,CAChC,CCTe,SAAQU,GAACC,EAAM,CAC5B,IAAIC,EAAOD,EAAK,SAAS,gBACrBE,EAAYC,GAAOH,CAAI,EAAE,GAAG,iBAAkBF,GAASF,EAAiB,EACxE,kBAAmBK,EACrBC,EAAU,GAAG,mBAAoBJ,GAASF,EAAiB,GAE3DK,EAAK,WAAaA,EAAK,MAAM,cAC7BA,EAAK,MAAM,cAAgB,OAE/B,CAEO,SAASG,GAAQJ,EAAMK,EAAS,CACrC,IAAIJ,EAAOD,EAAK,SAAS,gBACrBE,EAAYC,GAAOH,CAAI,EAAE,GAAG,iBAAkB,IAAI,EAClDK,IACFH,EAAU,GAAG,aAAcJ,GAASF,EAAiB,EACrD,WAAW,UAAW,CAAEM,EAAU,GAAG,aAAc,IAAI,GAAM,CAAC,GAE5D,kBAAmBD,EACrBC,EAAU,GAAG,mBAAoB,IAAI,GAErCD,EAAK,MAAM,cAAgBA,EAAK,WAChC,OAAOA,EAAK,WAEhB,CC3BA,MAAeK,GAAAC,GAAK,IAAMA,ECAX,SAASC,GAAUC,EAAM,CACtC,YAAArB,EACA,QAAAsB,EACA,OAAAC,EACA,WAAAC,EACA,OAAAC,EACA,EAAAN,EAAG,EAAAO,EAAG,GAAAC,EAAI,GAAAC,EACV,SAAAC,CACF,EAAG,CACD,OAAO,iBAAiB,KAAM,CAC5B,KAAM,CAAC,MAAOR,EAAM,WAAY,GAAM,aAAc,EAAI,EACxD,YAAa,CAAC,MAAOrB,EAAa,WAAY,GAAM,aAAc,EAAI,EACtE,QAAS,CAAC,MAAOsB,EAAS,WAAY,GAAM,aAAc,EAAI,EAC9D,OAAQ,CAAC,MAAOC,EAAQ,WAAY,GAAM,aAAc,EAAI,EAC5D,WAAY,CAAC,MAAOC,EAAY,WAAY,GAAM,aAAc,EAAI,EACpE,OAAQ,CAAC,MAAOC,EAAQ,WAAY,GAAM,aAAc,EAAI,EAC5D,EAAG,CAAC,MAAON,EAAG,WAAY,GAAM,aAAc,EAAI,EAClD,EAAG,CAAC,MAAOO,EAAG,WAAY,GAAM,aAAc,EAAI,EAClD,GAAI,CAAC,MAAOC,EAAI,WAAY,GAAM,aAAc,EAAI,EACpD,GAAI,CAAC,MAAOC,EAAI,WAAY,GAAM,aAAc,EAAI,EACpD,EAAG,CAAC,MAAOC,CAAQ,CACvB,CAAG,CACH,CAEAT,GAAU,UAAU,GAAK,UAAW,CAClC,IAAIU,EAAQ,KAAK,EAAE,GAAG,MAAM,KAAK,EAAG,SAAS,EAC7C,OAAOA,IAAU,KAAK,EAAI,KAAOA,CACnC,ECnBA,SAASC,GAAc9B,EAAO,CAC5B,MAAO,CAACA,EAAM,SAAW,CAACA,EAAM,MAClC,CAEA,SAAS+B,IAAmB,CAC1B,OAAO,KAAK,UACd,CAEA,SAASC,GAAehC,EAAOiC,EAAG,CAChC,OAAOA,GAAY,CAAC,EAAGjC,EAAM,EAAG,EAAGA,EAAM,CAAC,CAC5C,CAEA,SAASkC,IAAmB,CAC1B,OAAO,UAAU,gBAAmB,iBAAkB,IACxD,CAEe,SAAAC,IAAW,CACxB,IAAIC,EAASN,GACTO,EAAYN,GACZV,EAAUW,GACVM,EAAYJ,GACZK,EAAW,CAAE,EACbC,EAAYZ,GAAS,QAAS,OAAQ,KAAK,EAC3CJ,EAAS,EACTiB,EACAC,EACAC,EACAC,EACAC,EAAiB,EAErB,SAASV,EAAKtB,EAAW,CACvBA,EACK,GAAG,iBAAkBiC,CAAW,EAClC,OAAOR,CAAS,EACd,GAAG,kBAAmBS,CAAY,EAClC,GAAG,iBAAkBC,EAAY1C,EAAU,EAC3C,GAAG,iCAAkC2C,CAAU,EAC/C,MAAM,eAAgB,MAAM,EAC5B,MAAM,8BAA+B,eAAe,CAC1D,CAED,SAASH,EAAY9C,EAAOiC,EAAG,CAC7B,GAAI,EAAAW,GAAe,CAACR,EAAO,KAAK,KAAMpC,EAAOiC,CAAC,GAC9C,KAAIiB,EAAUC,EAAY,KAAMd,EAAU,KAAK,KAAMrC,EAAOiC,CAAC,EAAGjC,EAAOiC,EAAG,OAAO,EAC5EiB,IACLpC,GAAOd,EAAM,IAAI,EACd,GAAG,iBAAkBoD,EAAY7C,EAAiB,EAClD,GAAG,eAAgB8C,EAAY9C,EAAiB,EACnD+C,GAAOtD,EAAM,IAAI,EACjBQ,GAAcR,CAAK,EACnB2C,EAAc,GACdF,EAAazC,EAAM,QACnB0C,EAAa1C,EAAM,QACnBkD,EAAQ,QAASlD,CAAK,GACvB,CAED,SAASoD,EAAWpD,EAAO,CAEzB,GADAS,GAAQT,CAAK,EACT,CAAC2C,EAAa,CAChB,IAAIjB,EAAK1B,EAAM,QAAUyC,EAAYd,EAAK3B,EAAM,QAAU0C,EAC1DC,EAAcjB,EAAKA,EAAKC,EAAKA,EAAKkB,CACnC,CACDN,EAAS,MAAM,OAAQvC,CAAK,CAC7B,CAED,SAASqD,EAAWrD,EAAO,CACzBc,GAAOd,EAAM,IAAI,EAAE,GAAG,8BAA+B,IAAI,EACzDe,GAAQf,EAAM,KAAM2C,CAAW,EAC/BlC,GAAQT,CAAK,EACbuC,EAAS,MAAM,MAAOvC,CAAK,CAC5B,CAED,SAAS+C,EAAa/C,EAAOiC,EAAG,CAC9B,GAAKG,EAAO,KAAK,KAAMpC,EAAOiC,CAAC,EAC/B,KAAIsB,EAAUvD,EAAM,eAChBwD,EAAInB,EAAU,KAAK,KAAMrC,EAAOiC,CAAC,EACjCwB,EAAIF,EAAQ,OAAQG,EAAGR,EAE3B,IAAKQ,EAAI,EAAGA,EAAID,EAAG,EAAEC,GACfR,EAAUC,EAAY,KAAMK,EAAGxD,EAAOiC,EAAGsB,EAAQG,CAAC,EAAE,WAAYH,EAAQG,CAAC,CAAC,KAC5ElD,GAAcR,CAAK,EACnBkD,EAAQ,QAASlD,EAAOuD,EAAQG,CAAC,CAAC,GAGvC,CAED,SAASV,EAAWhD,EAAO,CACzB,IAAIuD,EAAUvD,EAAM,eAChByD,EAAIF,EAAQ,OAAQG,EAAGR,EAE3B,IAAKQ,EAAI,EAAGA,EAAID,EAAG,EAAEC,GACfR,EAAUX,EAASgB,EAAQG,CAAC,EAAE,UAAU,KAC1CjD,GAAQT,CAAK,EACbkD,EAAQ,OAAQlD,EAAOuD,EAAQG,CAAC,CAAC,EAGtC,CAED,SAAST,EAAWjD,EAAO,CACzB,IAAIuD,EAAUvD,EAAM,eAChByD,EAAIF,EAAQ,OAAQG,EAAGR,EAI3B,IAFIN,GAAa,aAAaA,CAAW,EACzCA,EAAc,WAAW,UAAW,CAAEA,EAAc,IAAK,EAAI,GAAG,EAC3Dc,EAAI,EAAGA,EAAID,EAAG,EAAEC,GACfR,EAAUX,EAASgB,EAAQG,CAAC,EAAE,UAAU,KAC1ClD,GAAcR,CAAK,EACnBkD,EAAQ,MAAOlD,EAAOuD,EAAQG,CAAC,CAAC,EAGrC,CAED,SAASP,EAAYQ,EAAMtB,EAAWrC,EAAOiC,EAAGV,EAAYqC,EAAO,CACjE,IAAIhC,EAAWY,EAAU,KAAM,EAC3BqB,EAAI5D,GAAQ2D,GAAS5D,EAAOqC,CAAS,EAAGX,EAAIC,EAC5CmC,EAEJ,IAAKA,EAAIzC,EAAQ,KAAKsC,EAAM,IAAIxC,GAAU,cAAe,CACrD,YAAanB,EACb,OAAQmC,EACR,WAAAZ,EACA,OAAAC,EACA,EAAGqC,EAAE,CAAC,EACN,EAAGA,EAAE,CAAC,EACN,GAAI,EACJ,GAAI,EACJ,SAAAjC,CACR,CAAO,EAAGK,CAAC,IAAM,KAEb,OAAAP,EAAKoC,EAAE,EAAID,EAAE,CAAC,GAAK,EACnBlC,EAAKmC,EAAE,EAAID,EAAE,CAAC,GAAK,EAEZ,SAASX,EAAQ9B,GAAMpB,GAAO4D,EAAO,CAC1C,IAAIG,GAAKF,EAAGJ,GACZ,OAAQrC,GAAI,CACV,IAAK,QAASmB,EAAShB,CAAU,EAAI2B,EAASO,GAAIjC,IAAU,MAC5D,IAAK,MAAO,OAAOe,EAAShB,CAAU,EAAG,EAAEC,EAC3C,IAAK,OAAQqC,EAAI5D,GAAQ2D,GAAS5D,GAAOqC,CAAS,EAAGoB,GAAIjC,EAAQ,KAClE,CACDI,EAAS,KACPR,GACAuC,EACA,IAAIxC,GAAUC,GAAM,CAClB,YAAapB,GACb,QAAS8D,EACT,OAAQ3B,EACR,WAAAZ,EACA,OAAQkC,GACR,EAAGI,EAAE,CAAC,EAAInC,EACV,EAAGmC,EAAE,CAAC,EAAIlC,EACV,GAAIkC,EAAE,CAAC,EAAIE,GAAG,CAAC,EACf,GAAIF,EAAE,CAAC,EAAIE,GAAG,CAAC,EACf,SAAAnC,CACV,CAAS,EACDK,CACR,CACA,CACG,CAED,OAAAE,EAAK,OAAS,SAAS6B,EAAG,CACxB,OAAO,UAAU,QAAU5B,EAAS,OAAO4B,GAAM,WAAaA,EAAI/C,GAAS,CAAC,CAAC+C,CAAC,EAAG7B,GAAQC,CAC7F,EAEED,EAAK,UAAY,SAAS6B,EAAG,CAC3B,OAAO,UAAU,QAAU3B,EAAY,OAAO2B,GAAM,WAAaA,EAAI/C,GAAS+C,CAAC,EAAG7B,GAAQE,CAC9F,EAEEF,EAAK,QAAU,SAAS6B,EAAG,CACzB,OAAO,UAAU,QAAU3C,EAAU,OAAO2C,GAAM,WAAaA,EAAI/C,GAAS+C,CAAC,EAAG7B,GAAQd,CAC5F,EAEEc,EAAK,UAAY,SAAS6B,EAAG,CAC3B,OAAO,UAAU,QAAU1B,EAAY,OAAO0B,GAAM,WAAaA,EAAI/C,GAAS,CAAC,CAAC+C,CAAC,EAAG7B,GAAQG,CAChG,EAEEH,EAAK,GAAK,UAAW,CACnB,IAAIN,EAAQW,EAAU,GAAG,MAAMA,EAAW,SAAS,EACnD,OAAOX,IAAUW,EAAYL,EAAON,CACxC,EAEEM,EAAK,cAAgB,SAAS6B,EAAG,CAC/B,OAAO,UAAU,QAAUnB,GAAkBmB,EAAI,CAACA,GAAKA,EAAG7B,GAAQ,KAAK,KAAKU,CAAc,CAC9F,EAESV,CACT,q5BC9FmB8B,EAAY,CAAA,CAAA,0KAGKA,EAAY,CAAA,CAAA,sFAThBA,EAAE,CAAA,EAAA,KAAA,kCACpBA,EAAM,CAAA,CAAA,+DAVpBC,EAoBK5C,EAAA6C,EAAAC,CAAA,EAnBJC,EAEKF,EAAAG,CAAA,iCACLD,EAeKF,EAAAI,CAAA,EAPJF,EAKAE,EAAAC,CAAA,EAJEH,EAAgCG,EAAAC,CAAA,EAAAJ,EAGzBG,EAAAE,CAAA,EAAAL,EAAiCG,EAAAG,CAAA,SAE1CN,EAAqDE,EAAAK,CAAA,uKAHpCX,EAAY,CAAA,CAAA,sFAGKA,EAAY,CAAA,CAAA,4CAThBA,EAAE,CAAA,EAAA,KAAA,0DACpBA,EAAM,CAAA,CAAA,kHAzFV,SAAAY,GAAMhD,EAAeiD,EAAaC,EAAA,CACnC,OAAA,KAAK,IAAI,KAAK,IAAIlD,EAAOiD,CAAG,EAAGC,CAAG,EAiCjC,SAAAC,GAAMvB,EAAWwB,EAAA,OACnBC,EAAM,KAAK,IAAI,GAAID,CAAM,EACxB,OAAA,KAAK,OAAOxB,EAAI,OAAO,SAAWyB,CAAG,EAAIA,oDAhCtC,SAAAC,EAAW,EAAA,EAAAC,GACX,SAAAC,EAAW,EAAA,EAAAD,GAEX,aAAAE,EAAe,6BAAA,EAAAF,EACf,CAAA,WAAAG,EAAA,CAKL,IAAK,EAAG,KAAM,EAAG,MAAO,EAAG,OAAQ,CAAA,CAAA,EAAAH,GAC9B,GAAAI,EAAiC,MAAA,EAAAJ,GACjC,UAAAK,EAAwC,MAAA,EAAAL,EAC/CM,EACAC,EAAK,EACLnE,EAAS,GACToE,EAAkB,WAEbC,EAAaC,EAAA,CACrBF,GAAkBH,GAAA,YAAAA,EAAW,wBAAwB,QAAS,EAC1DK,IAAU,OACbP,EAAW,OAAQC,GAAA,YAAAA,EAAI,wBAAwB,QAAS,EAAAD,CAAA,EAGzDQ,EAAA,EAAAJ,EAAKd,GACJU,EAAW,MAAQJ,EAAWI,EAAW,KACzC,EACAK,CAAA,CAAA,WASOI,EAAgB9E,EAAA,KACxByE,EAAKd,GAAM3D,EAAG,EAAG0E,CAAe,CAAA,EAChCG,EAAA,EAAAZ,EAAWH,IAAO9D,EAAIqE,EAAW,MAAQA,EAAW,MAAO,CAAC,CAAA,WAGpDU,EAAWjG,EAAA,CACfqF,QACJ7D,EAAS,EAAA,EACTwE,EAAgBhG,EAAM,CAAC,YAGfkG,EAAUlG,EAAA,CACdqF,GACJW,EAAgBhG,EAAM,CAAC,EAGf,SAAAmG,GAAA,CACJd,OACJ7D,EAAS,EAAA,WAGD4E,EAAwBC,EAAA,CAChCN,EAAA,EAAAJ,EAAKd,GAAMU,EAAW,MAAQc,EAAKd,EAAW,KAAM,EAAGK,CAAe,CAAA,EAMvEU,GAAA,IAAA,CACCT,EAAaN,EAAW,KAAK,QACvBgB,EAAepE,GAAA,EACnB,GAAG,QAAS8D,CAAU,EACtB,GAAG,OAAQC,CAAS,EACpB,GAAG,MAAOC,CAAQ,EACpBrF,GAAO4E,CAAK,EAAE,KAAKa,CAAY,gBAIDV,EAAaN,EAAW,KAAK,2CAG5BC,EAAEgB,oDAMtBd,EAAKc,oDAPuBf,EAASe,6SAf9CX,EAAaN,EAAW,KAAK,kBAC7Ba,EAAwBjB,CAAQ,+MCwC9BlB,EAAY,CAAA,GACbA,EAAW,CAAA,uKAKAwC,EAAAC,EAAA,UAAAzC,OAAY,SAAS,EACtBwC,EAAAC,EAAA,SAAAzC,OAAY,QAAQ,sCAGpBA,EAAU,CAAA,CAAA,wBAFN0C,EAAAD,EAAA,aAAAzC,OAAeA,EAAU,CAAA,KAAMA,EAAU,CAAA,CAAA,KAAO,IAAI,kCATvEC,EAaC5C,EAAAoF,EAAAtC,CAAA,oFAZKH,EAAY,CAAA,CAAA,GAAA,CAAA,IAAA2C,CAAA,SACb3C,EAAW,CAAA,0CAKAwC,EAAAC,EAAA,UAAAzC,OAAY,SAAS,EACtBwC,EAAAC,EAAA,SAAAzC,OAAY,QAAQ,sCAGpBA,EAAU,CAAA,CAAA,wBAFN0C,EAAAD,EAAA,aAAAzC,OAAeA,EAAU,CAAA,KAAMA,EAAU,CAAA,CAAA,KAAO,IAAI,kFA5D7D4C,GAAeH,EAAA,OAMlB,GAAA,CAAAA,EAAA,MAAA,CAAc,IAAK,EAAG,KAAM,EAAG,MAAO,EAAG,OAAQ,CAAA,EAChD,MAAArE,GAAYyE,EAAAJ,EAAI,gBAAJ,YAAAI,EAAmB,wBAEhC,GAAA,CAAAzE,EAAA,MAAA,CAAoB,IAAK,EAAG,KAAM,EAAG,MAAO,EAAG,OAAQ,CAAA,QAEtD0E,EAAgBL,EAAI,aAAeA,EAAI,cACvCM,EAAkB3E,EAAU,MAAQA,EAAU,WAChD4E,EAAgBC,EAEhBH,EAAgBC,GACnBC,EAAiB5E,EAAU,MAC3B6E,EAAkB7E,EAAU,MAAQ0E,IAEpCG,EAAkB7E,EAAU,OAC5B4E,EAAiB5E,EAAU,OAAS0E,SAG/BI,GAAW9E,EAAU,MAAQ4E,GAAkB,SAIpD,KAHgB5E,EAAU,OAAS6E,GAAmB,EAItD,KAAMC,EACN,MAAOF,EACP,OAAQC,+HAvEC,IAAAE,EAAgC,MAAA,EAAAhC,GAChC,WAAAiC,EAAa,EAAA,EAAAjC,EAEpBkC,GAEO,MAAAC,EAAQ,EAAA,EAAAnC,GACR,UAAAoC,EAAY,8BAAA,EAAApC,GACZ,OAAAqC,EAAkC,IAAA,EAAArC,GAClC,OAAAsC,EAAS,EAAA,EAAAtC,GACT,QAAAuC,EAAU,QAAA,EAAAvC,GACV,WAAAwC,EAAa,GAAA,EAAAxC,EAIpByC,QAkBEjG,EAAWkG,KA2CjBxB,GAAA,IAAA,CACO,MAAAyB,EAAA,IAAc,eAAsB,MAAAC,GAAA,WAC9BC,KAASD,EACb,MAAAE,GAAA,EACNtG,EAAS,OAAQiF,GAAeY,CAAM,CAAA,IAIxC,OAAAM,EAAQ,QAAQN,CAAO,OAGtBM,EAAQ,WAAA,8CAWCN,EAAMjB,WAOF,MAAA2B,EAAA,IAAAvG,EAAS,OAAQiF,GAAeY,CAAM,CAAA,+VAzFrD,KAMCH,EAAeF,CAAA,OAEfS,EAAaT,CAAA,QACPgB,EAAgBhB,EACtBiB,GAAiBD,CAAa,EAAE,KAAMtE,GAAA,CACjC+D,IAAeO,OAClBd,EAAexD,CAAA,uLC5CZ,MAAMwE,EAAc,CAgC1B,YAAYjG,EAA2BkG,EAAyB,CA/BhEC,EAAA,kBACAA,EAAA,cACAA,EAAA,cACAA,EAAA,gBACAA,EAAA,gBACAA,EAAA,mBACAA,EAAA,cACAA,EAAA,cACAA,EAAA,6BACAA,EAAA,4BACAA,EAAA,sBACAA,EAAA,uBACAA,EAAA,oBASAA,EAAA,wBACAA,EAAA,uBAKI,CAAE,IAAK,EAAG,KAAM,EAAG,MAAO,EAAG,OAAQ,IAEzCA,EAAA,4BAGC,KAAK,UAAYnG,EACjB,KAAK,MAAQkG,EAEb,KAAK,MAAQ,EACb,KAAK,QAAU,EACf,KAAK,QAAU,EACf,KAAK,WAAa,GAClB,KAAK,MAAQ,EACb,KAAK,MAAQ,EACb,KAAK,qBAAuB,EAC5B,KAAK,oBAAsB,EAC3B,KAAK,cAAgB,EACrB,KAAK,eAAiB,EACtB,KAAK,YAAc,GACnB,KAAK,oBAAsB,EAE3B,KAAK,YAAc,KAAK,YAAY,KAAK,IAAI,EAC7C,KAAK,gBAAkB,KAAK,gBAAgB,KAAK,IAAI,EACrD,KAAK,gBAAkB,KAAK,gBAAgB,KAAK,IAAI,EACrD,KAAK,cAAgB,KAAK,cAAc,KAAK,IAAI,EACjD,KAAK,gBAAkB,KAAK,KAAK,KAAK,IAAI,EAC1C,KAAK,iBAAmB,KAAK,iBAAiB,KAAK,IAAI,EACvD,KAAK,gBAAkB,KAAK,gBAAgB,KAAK,IAAI,EACrD,KAAK,eAAiB,KAAK,eAAe,KAAK,IAAI,EAEnD,KAAK,MAAM,iBAAiB,OAAQ,KAAK,eAAe,EAExD,KAAK,UAAU,iBAAiB,QAAS,KAAK,WAAW,EACzD,KAAK,UAAU,iBAAiB,YAAa,KAAK,eAAe,EACxD,SAAA,iBAAiB,YAAa,KAAK,eAAe,EAClD,SAAA,iBAAiB,UAAW,KAAK,aAAa,EAEvD,KAAK,UAAU,iBAAiB,aAAc,KAAK,gBAAgB,EAC1D,SAAA,iBAAiB,YAAa,KAAK,eAAe,EAClD,SAAA,iBAAiB,WAAY,KAAK,cAAc,EAExC,IAAI,eAAgBP,GAAY,CAChD,UAAWC,KAASD,EACfC,EAAM,SAAW,KAAK,YACzB,KAAK,aAAa,EACb,KAAA,eAAe,KAAK,KAAK,EAEhC,CACA,EACQ,QAAQ,KAAK,SAAS,CAChC,CAEA,cAAqB,CACpB,KAAK,KAAK,CACX,CAEA,MAAa,CACN,MAAAQ,EAAgB,KAAK,UAAU,sBAAsB,EAErDC,EAAY,KAAK,MAAM,sBAAsB,EAC9C,KAAA,qBAAuBA,EAAU,KAAOD,EAAc,KACtD,KAAA,oBAAsBC,EAAU,IAAMD,EAAc,IACzD,KAAK,cAAgBC,EAAU,MAC/B,KAAK,eAAiBA,EAAU,OAEhC,KAAK,WAAW,EAEhB,KAAK,gBAAgB,CACtB,CAEA,YAAmB,CAClB,KAAK,MAAQ,EACb,KAAK,QAAU,EACf,KAAK,QAAU,EACf,KAAK,gBAAgB,CACtB,CAEA,gBAAgB,EAAqB,CAC9B,MAAAA,EAAY,KAAK,MAAM,sBAAsB,EAEnD,GACC,EAAE,SAAWA,EAAU,MACvB,EAAE,SAAWA,EAAU,OACvB,EAAE,SAAWA,EAAU,KACvB,EAAE,SAAWA,EAAU,OACtB,CAED,GADA,EAAE,eAAe,EACb,KAAK,QAAU,EAAG,OACtB,KAAK,WAAa,GAClB,KAAK,MAAQ,EAAE,QACf,KAAK,MAAQ,EAAE,QACV,KAAA,MAAM,MAAM,OAAS,UAC3B,CACD,CAEA,gBAAgB,EAAqB,CACpC,GAAI,CAAC,KAAK,WAAY,OAEhB,MAAAC,EAAS,EAAE,QAAU,KAAK,MAC1BC,EAAS,EAAE,QAAU,KAAK,MAEhC,KAAK,SAAWD,EAChB,KAAK,SAAWC,EAEhB,KAAK,MAAQ,EAAE,QACf,KAAK,MAAQ,EAAE,QAEf,KAAK,gBAAgB,EAErB,KAAK,gBAAgB,CACtB,CAEA,eAAsB,CACjB,KAAK,aACR,KAAK,oBAAoB,EAAI,EAC7B,KAAK,gBAAgB,EACrB,KAAK,WAAa,GAClB,KAAK,MAAM,MAAM,OAAS,KAAK,MAAQ,EAAI,OAAS,UAEtD,CAEA,MAAM,YAAY,EAA8B,CAC/C,EAAE,eAAe,EAEX,MAAAH,EAAgB,KAAK,UAAU,sBAAsB,EACrDC,EAAY,KAAK,MAAM,sBAAsB,EAEnD,GACC,EAAE,QAAUA,EAAU,MACtB,EAAE,QAAUA,EAAU,OACtB,EAAE,QAAUA,EAAU,KACtB,EAAE,QAAUA,EAAU,OAEtB,OAGD,MAAMG,EAAa,KACbC,EAAW,KAAK,MAChBC,EACL,CAAC,KAAK,KAAK,EAAE,MAAM,EAAI,EACpB,KAAK,IAAI,GAAID,EAAWD,CAAU,EAClC,KAAK,IAAI,EAAGC,EAAWD,CAAU,EAErC,GAAIE,IAAaD,EAAU,OAE3B,MAAME,EAAU,EAAE,QAAUP,EAAc,KAAO,KAAK,qBAChDQ,EAAU,EAAE,QAAUR,EAAc,IAAM,KAAK,oBAErD,KAAK,MAAQM,EACR,KAAA,QAAU,KAAK,mBAAmB,CACtC,gBAAiBC,EACjB,eAAgB,KAAK,QACrB,UAAWD,EACX,UAAWD,CAAA,CACX,EACI,KAAA,QAAU,KAAK,mBAAmB,CACtC,gBAAiBG,EACjB,eAAgB,KAAK,QACrB,UAAWF,EACX,UAAWD,CAAA,CACX,EAED,KAAK,gBAAgB,EAErB,KAAK,oBAAoB,EACzB,KAAK,gBAAgB,EAErB,KAAK,MAAM,MAAM,OAAS,KAAK,MAAQ,EAAI,OAAS,SACrD,CAMA,qBAAqB,CACpB,SAAA3D,EACA,MAAA+D,EACA,gBAAAC,CAAA,EAKU,CACV,OAAOhE,GAAYA,EAAWgE,IAAoBD,EAAQ,KAAK,MAChE,CAEA,mBAAmB,CAClB,gBAAAE,EACA,eAAAC,EACA,UAAAC,EACA,UAAAC,CAAA,EAMU,CAET,OAAAH,EACCE,EAAYC,GAAcH,EAAkBC,EAE/C,CAEA,oBAAoBG,EAAM,GAAa,CAClC,GAAA,KAAK,QAAU,EAAG,CACrB,KAAK,QAAU,EACf,KAAK,QAAU,EACf,MACD,CACA,MAAMC,EAAW,CAChB,IAAK,KAAK,gBAAgB,IAAM,KAAK,MAAQ,KAAK,QAClD,KAAM,KAAK,gBAAgB,KAAO,KAAK,MAAQ,KAAK,QACpD,MAAO,KAAK,gBAAgB,MAAQ,KAAK,MACzC,OAAQ,KAAK,gBAAgB,OAAS,KAAK,MAE3C,OACC,KAAK,gBAAgB,IAAM,KAAK,MAChC,KAAK,QACL,KAAK,gBAAgB,OAAS,KAAK,MACpC,MACC,KAAK,gBAAgB,KAAO,KAAK,MACjC,KAAK,QACL,KAAK,gBAAgB,MAAQ,KAAK,KAAA,EAG9BC,EACL,KAAK,gBAAgB,KAAO,KAAK,gBAAgB,MAC5CC,EACL,KAAK,gBAAgB,IAAM,KAAK,gBAAgB,OAE7CH,IACCC,EAAS,IAAM,KAAK,gBAAgB,IACvC,KAAK,QAAU,KAAK,mBACnB,KAAK,gBAAgB,IACrB,EACA,GAAA,EAESA,EAAS,OAASE,IAC5B,KAAK,QAAU,KAAK,mBAAmBA,EAAwB,EAAG,GAAG,GAGlEF,EAAS,KAAO,KAAK,gBAAgB,KACxC,KAAK,QAAU,KAAK,mBACnB,KAAK,gBAAgB,KACrB,EACA,GAAA,EAESA,EAAS,MAAQC,IAC3B,KAAK,QAAU,KAAK,mBAAmBA,EAAuB,EAAG,GAAG,GAGvE,CAEA,iBAAwB,CAClB,KAAA,OAAO,CAAE,EAAG,KAAK,QAAS,EAAG,KAAK,QAAS,MAAO,KAAK,KAAO,CAAA,CACpE,CAEA,SAAgB,CACf,KAAK,UAAU,oBAAoB,QAAS,KAAK,WAAW,EAC5D,KAAK,UAAU,oBAAoB,YAAa,KAAK,eAAe,EAC3D,SAAA,oBAAoB,YAAa,KAAK,eAAe,EACrD,SAAA,oBAAoB,UAAW,KAAK,aAAa,EAC1D,KAAK,UAAU,oBAAoB,aAAc,KAAK,gBAAgB,EAC7D,SAAA,oBAAoB,YAAa,KAAK,eAAe,EACrD,SAAA,oBAAoB,WAAY,KAAK,cAAc,EAC5D,KAAK,MAAM,oBAAoB,OAAQ,KAAK,eAAe,CAC5D,CAEA,UACCE,EACO,CACF,KAAA,YAAY,KAAKA,CAAE,CACzB,CAEA,YACCA,EACO,CACF,KAAA,YAAc,KAAK,YAAY,OAClCC,GAAeA,IAAeD,CAAA,CAEjC,CAEA,OAAO,CAAE,EAAA1I,EAAG,EAAAO,EAAG,MAAAyH,GAAwD,CACjE,KAAA,YAAY,QAASW,GAAeA,EAAW,CAAE,EAAA3I,EAAG,EAAAO,EAAG,MAAAyH,CAAO,CAAA,CAAC,CACrE,CAEA,iBAAiB,EAAqB,CACrC,EAAE,eAAe,EACX,MAAAR,EAAY,KAAK,MAAM,sBAAsB,EAC7C9E,EAAQ,EAAE,QAAQ,CAAC,EAEzB,GACCA,EAAM,SAAW8E,EAAU,MAC3B9E,EAAM,SAAW8E,EAAU,OAC3B9E,EAAM,SAAW8E,EAAU,KAC3B9E,EAAM,SAAW8E,EAAU,QAE3B,GAAI,EAAE,QAAQ,SAAW,GAAK,KAAK,MAAQ,EAE1C,KAAK,WAAa,GAClB,KAAK,MAAQ9E,EAAM,QACnB,KAAK,MAAQA,EAAM,gBACT,EAAE,QAAQ,SAAW,EAAG,CAE5B,MAAAkG,EAAS,EAAE,QAAQ,CAAC,EACpBC,EAAS,EAAE,QAAQ,CAAC,EAC1B,KAAK,oBAAsB,KAAK,MAC/BA,EAAO,QAAUD,EAAO,QACxBC,EAAO,QAAUD,EAAO,OAAA,CAE1B,EAEF,CAEA,eAAepD,EAAoC,OAClD,GAAI,CAACA,EAAK,OACJ,MAAArE,GAAYyE,EAAAJ,EAAI,gBAAJ,YAAAI,EAAmB,wBAErC,GAAI,CAACzE,EAAW,OAEV,MAAA0E,EAAgBL,EAAI,aAAeA,EAAI,cACvCM,EAAkB3E,EAAU,MAAQA,EAAU,OACpD,IAAI4E,EAAgBC,EAEhBH,EAAgBC,GACnBC,EAAiB5E,EAAU,MAC3B6E,EAAkB7E,EAAU,MAAQ0E,IAEpCG,EAAkB7E,EAAU,OAC5B4E,EAAiB5E,EAAU,OAAS0E,GAG/B,MAAAI,GAAW9E,EAAU,MAAQ4E,GAAkB,EAC/C+C,GAAW3H,EAAU,OAAS6E,GAAmB,EAEvD,KAAK,gBAAkB,CACtB,IAAK8C,EACL,KAAM7C,EACN,MAAOF,EACP,OAAQC,CAAA,CAEV,CAEA,gBAAgB,EAAqB,CACpC,GAAI,EAAE,QAAQ,SAAW,GAAK,KAAK,WAAY,CAE9C,EAAE,eAAe,EACX,MAAAtD,EAAQ,EAAE,QAAQ,CAAC,EAEnB+E,EAAS/E,EAAM,QAAU,KAAK,MAC9BgF,EAAShF,EAAM,QAAU,KAAK,MAEpC,KAAK,SAAW+E,EAChB,KAAK,SAAWC,EAEhB,KAAK,MAAQhF,EAAM,QACnB,KAAK,MAAQA,EAAM,QAEnB,KAAK,gBAAgB,CACX,SAAA,EAAE,QAAQ,SAAW,EAAG,CAElC,EAAE,eAAe,EAEX,MAAAkG,EAAS,EAAE,QAAQ,CAAC,EACpBC,EAAS,EAAE,QAAQ,CAAC,EAEpBE,EAAmB,KAAK,MAC7BF,EAAO,QAAUD,EAAO,QACxBC,EAAO,QAAUD,EAAO,OAAA,EAGrB,GAAA,KAAK,sBAAwB,EAAG,CACnC,KAAK,oBAAsBG,EAC3B,MACD,CAEM,MAAApB,EAAaoB,EAAmB,KAAK,oBAErCnB,EAAW,KAAK,MAChBC,EAAW,KAAK,IAAI,GAAI,KAAK,IAAI,EAAGD,EAAWD,CAAU,CAAC,EAEhE,GAAIE,IAAaD,EAAU,CAC1B,KAAK,oBAAsBmB,EAC3B,MACD,CAGM,MAAAxB,EAAgB,KAAK,UAAU,sBAAsB,EACrDyB,GACJJ,EAAO,QAAUC,EAAO,SAAW,EACpCtB,EAAc,KACd,KAAK,qBACA0B,GACJL,EAAO,QAAUC,EAAO,SAAW,EACpCtB,EAAc,IACd,KAAK,oBAEN,KAAK,MAAQM,EACR,KAAA,QAAU,KAAK,mBAAmB,CACtC,gBAAiBmB,EACjB,eAAgB,KAAK,QACrB,UAAWnB,EACX,UAAWD,CAAA,CACX,EACI,KAAA,QAAU,KAAK,mBAAmB,CACtC,gBAAiBqB,EACjB,eAAgB,KAAK,QACrB,UAAWpB,EACX,UAAWD,CAAA,CACX,EAED,KAAK,gBAAgB,EACrB,KAAK,oBAAoB,EACzB,KAAK,gBAAgB,EAErB,KAAK,oBAAsBmB,EAE3B,KAAK,MAAM,MAAM,OAAS,KAAK,MAAQ,EAAI,OAAS,SACrD,CACD,CAEA,eAAe,EAAqB,CAC/B,KAAK,aACR,KAAK,oBAAoB,EAAI,EAC7B,KAAK,gBAAgB,EACrB,KAAK,WAAa,IAGf,EAAE,QAAQ,SAAW,IACxB,KAAK,oBAAsB,EAE7B,CAEA,mBACCG,EACAC,EACAC,EACS,CAIT,GAHsB,KAAK,UAAU,sBAAsB,EAGvDA,IAAS,IAAK,CACjB,MAAMC,EAAoBH,EACpBI,EACL,KAAK,gBAAgB,KAAOH,EAAe,KAAK,gBAAgB,MAC1D,OAAAE,EAAoBC,EAAW,KAAK,KAC5C,CAGA,GAAIF,IAAS,IAAK,CACjB,MAAMG,EAAoBL,EACpBM,EACL,KAAK,gBAAgB,IAAML,EAAe,KAAK,gBAAgB,OACzD,OAAAI,EAAoBC,EAAW,KAAK,KAC5C,CAEO,MAAA,EACR,CACD,kSCvTazG,EAAW,EAAA,IAAA,cAAXA,EAAW,EAAA,odALAA,EAAU,EAAA,CAAA,uDApCjCC,EAuEK5C,EAAAsD,EAAAR,CAAA,qBAvCJC,EAsCKO,EAAAN,CAAA,uVA7BML,EAAW,EAAA,sHALAA,EAAU,EAAA,CAAA,2NAtCZ,+nBAgBX6C,EAAA7C,EAAK,CAAA,EAAC,CAAC,IAAP,YAAA6C,EAAU,IACN,WAAA6D,EAAA1G,EAAM,CAAA,EAAA,CAAC,IAAP,YAAA0G,EAAU,YAAa,mKAD3B7D,EAAA7C,EAAK,CAAA,EAAC,CAAC,IAAP,YAAA6C,EAAU,KACN8D,EAAA,CAAA,EAAA,IAAAC,EAAA,WAAAF,EAAA1G,EAAM,CAAA,EAAA,CAAC,IAAP,YAAA0G,EAAU,YAAa,sMAEfG,GAAiB,MAAA7G,KAAK,iBAAiB,6FAAtB2G,EAAA,CAAA,EAAA,KAAAG,EAAA,MAAA9G,KAAK,iBAAiB,iJAKnD+G,GAAK,MAAA,cAAA,wOAnBNC,GACC,MAAAhH,KAAK,aAAa,WACfA,EAAU,EAAA,EAAC,IAAM,gCAGvBA,EAAsB,EAAA,GAAAiH,GAAAjH,CAAA,IAItBA,EAAoB,CAAA,GAAAkH,GAAAlH,CAAA,IAQpBA,EAAW,EAAA,GAAAmH,GAAAnH,CAAA,yRAhBR2G,EAAA,CAAA,EAAA,KAAAG,EAAA,MAAA9G,KAAK,aAAa,4BACfA,EAAU,EAAA,EAAC,IAAM,aAGvBA,EAAsB,EAAA,iHAItBA,EAAoB,CAAA,+GAQpBA,EAAW,EAAA,sWA0BT0G,GAAA7D,EAAA7C,EAAK,CAAA,IAAL,YAAA6C,EAAQ,KAAR,YAAA6D,EAAY,mEAKK1G,EAAU,EAAA,EAAC,EAAO,OAAAA,MAAW,EAAC,aAAYA,EAAU,EAAA,EAAC,EAAC,8CAF/DA,EAAG,EAAA,IAAA,kBAAHA,EAAG,EAAA,iEAKPA,EAAiB,EAAA,CAAA,2CAInBA,EAAY,CAAA,YACVoH,GAAAC,EAAArH,EAAK,CAAA,IAAL,YAAAqH,EAAQ,KAAR,MAAAD,EAAY,UAChBE,GAAAC,EAAAvH,EAAK,CAAA,IAAL,YAAAuH,EAAQ,KAAR,YAAAD,EAAY,gCAGTtH,EAAK,EAAA,EAAA,qEACSA,EAAU,EAAA,EAAC,EAAO,OAAAA,MAAW,EAAC,aAAYA,EAAU,EAAA,EAAC,EAAC,sDAGnEA,EAAiB,EAAA,CAAA,8LArBrB0G,GAAA7D,EAAA7C,EAAK,CAAA,IAAL,YAAA6C,EAAQ,KAAR,YAAA6D,EAAY,4CAKK1G,EAAU,EAAA,EAAC,EAAO,OAAAA,MAAW,EAAC,aAAYA,EAAU,EAAA,EAAC,EAAC,oGAF/DA,EAAG,EAAA,wDASTA,EAAY,CAAA,wBACVoH,GAAAC,EAAArH,EAAK,CAAA,IAAL,YAAAqH,EAAQ,KAAR,MAAAD,EAAY,sBAChBE,GAAAC,EAAAvH,EAAK,CAAA,IAAL,YAAAuH,EAAQ,KAAR,YAAAD,EAAY,4BAGTtH,EAAK,EAAA,EAAA,sFACSA,EAAU,EAAA,EAAC,EAAO,OAAAA,MAAW,EAAC,aAAYA,EAAU,EAAA,EAAC,EAAC,ydApElDwH,SAAcxH,EAAK,CAAA,GAAIA,EAAI,CAAA,EAAC,aAAa,0CAClE,OAAAA,EAAU,CAAA,IAAA,MAAQA,KAAM,CAAC,IAAM,MAAQA,EAAM,CAAA,EAAA,CAAC,IAAM,QAAUA,EAAW,CAAA,EAAA,+OADlCA,EAAK,CAAA,GAAIA,EAAI,CAAA,EAAC,aAAa,6QAvE9D,SAAAyH,GACRC,EACAC,EACAC,EACAC,EACAC,EACA7C,EAAA,CAQO,OANsByC,EAAqBE,EACJC,EAEAC,GAAM7C,EACL0C,+BApDrC,CAAA,MAAA/J,EAAA,CAA6C,KAAM,IAAI,CAAA,EAAAuD,GACvD,MAAA4G,EAA4B,MAAA,EAAA5G,GAC5B,qBAAA6G,EAAuB,EAAA,EAAA7G,EACvB,CAAA,WAAA8G,CAAA,EAAA9G,EACA,CAAA,KAAA+G,CAAA,EAAA/G,EACA,CAAA,SAAAD,CAAA,EAAAC,GACA,aAAAgH,EAAe,EAAA,EAAAhH,GACf,YAAAiH,EAAc,EAAA,EAAAjH,EACd,CAAA,aAAAE,CAAA,EAAAF,GACA,uBAAAkH,EAAyB,EAAA,EAAAlH,GACzB,WAAAiC,EAAa,EAAA,EAAAjC,GACb,SAAAmH,EAAW,CAAA,EAAAnH,EACX,CAAA,WAAAwC,CAAA,EAAAxC,GACA,YAAAoH,EAAc,EAAA,EAAApH,QACnBxD,EAAWkG,KAEb,IAAApB,EACA+F,EACAC,EAEAlF,EAA0DmF,IAC3D,EAAG,EAAG,EAAG,EAAG,EAAG,IAEhB,SAAU,EAAA,CAAA,uBAGR,IAAAlH,EAgCAmH,EAAiB,EAEjBC,EAAuC,KACvCC,EAAkC,KAE7B,SAAAC,EACRrG,EACA+F,GAAAA,EAEK/F,GAAQ+F,CAAAA,KACbI,GAAA,MAAAA,EAAgB,UAChBC,GAAA,MAAAA,EAAU,aACEpG,GAAAA,MAAAA,EAAK,wBAAwB,WACzCkG,GAAiBH,IAAAA,YAAAA,GAAa,wBAAwB,QAAS,CAAA,OAC/DI,EAAqB,IAAAvE,GAAcmE,GAAa/F,CAAG,CAAA,EACnDmG,EAAe,UAAA,CAAA,CAAa,EAAA3L,GAAG,EAAAO,GAAG,MAAAyH,EAAA,IAAA,CACjC1B,EAAU,IAAA,CAAM,EAAAtG,GAAG,EAAAO,GAAG,EAAGyH,EAAA,CAAA,IAG1B4D,EAAA,IAAe,eAAgB9E,IAAA,WACnBC,MAASD,GACfC,GAAM,SAAWwE,IACpB1G,EAAA,GAAA6G,EAAiB3E,GAAM,YAAY,KAAA,EAGhCA,GAAM,SAAWvB,GACRuB,GAAM,YAAY,QAIjC6E,EAAS,QAAQL,EAAW,EAC5BK,EAAS,QAAQpG,CAAG,GAKrBJ,GAAA,SAEEuG,GAAA,MAAAA,EAAgB,UAChBC,GAAA,MAAAA,EAAU,eAIR,IAAAE,EAEAzH,EAAA,CACD,IAAK,EAAG,KAAM,EAAG,MAAO,EAAG,OAAQ,YAE7B0H,GAAkBjN,EAAA,CAC1B+F,EAAA,GAAAR,EAAavF,EAAM,MAAA,EAcD,MAAAkN,GAAA,IAAAL,GAAA,YAAAA,EAAgB,sDAkBpB7M,GAAK,KACf6B,EAAK,CAAI,KAAM,IAAI,CAAA,EACnBD,EAAS,OAAO,EAChB5B,EAAM,gBAAe,kBAsBT0G,EAAG7E,mDARR4K,EAAW5K,+EAPVmL,EAAkBxG,0BACX+F,EAAQ,KAAA,6DAnCYG,EAAelG,inBAvFpDT,EAAA,GAAAoH,EAAqBzB,GACvBvG,EACAyH,EACArH,EAAW,MACXA,EAAW,KACX6H,EAAW,EACXA,EAAW,CAAA,CAAA,2BAEZrH,EAAA,GAAGsH,EAAQjB,EACkB,0BAAAe,EAAqB,GAAG,KAClD,EAAA,uBAsDAJ,EAAWrG,EAAK+F,CAAW,kaCrGvBzB,GAAK,MAAA,cAAA,8LAFb9G,EASK5C,EAAAgM,EAAAlJ,CAAA,mIAZExC,EAAWkG,cAOL9H,GAAK,CACf4B,EAAS,cAAc,EACvB5B,EAAM,gBAAe,iUCmFhBiE,EAAoB,CAAA,GAAAsJ,GAAAtJ,CAAA,2KAD1BC,EASK5C,EAAAgM,EAAAlJ,CAAA,8BARCH,EAAoB,CAAA,uNAEjBA,EAAK,CAAA,EAAC,CAAC,EAAE,IACL,SAAAA,EAAM,CAAA,EAAA,CAAC,EAAE,WAAa,uJAD1BA,EAAK,CAAA,EAAC,CAAC,EAAE,KACL2G,EAAA,IAAAC,EAAA,SAAA5G,EAAM,CAAA,EAAA,CAAC,EAAE,WAAa,iMAEd6G,EAAQ,CAAA,CAAA,+OAkCrBhE,EAAA7C,EAAM,EAAA,EAAC,CAAC,IAAR,YAAA6C,EAAW,oCAEH7C,EAAG,EAAA,IAAA,kBAAHA,EAAG,EAAA,mKAFX6C,EAAA7C,EAAM,EAAA,EAAC,CAAC,IAAR,YAAA6C,EAAW,8DAEH7C,EAAG,EAAA,4LAfE,cAAA,CAAA,GAAA6C,EAAA7C,OAAA,MAAA6C,EAAQ,oZALQL,EAAA6G,EAAA,YAAArJ,OAAiB,CAAC,UAArDC,EAcK5C,EAAAgM,EAAAlJ,CAAA,4CATcwG,EAAA,IAAA4C,EAAA,cAAA,CAAA,GAAA1G,EAAA7C,OAAA,MAAA6C,EAAQ,8NALQL,EAAA6G,EAAA,YAAArJ,OAAiB,CAAC,4aAmD/CA,EAAM,EAAA,EAAC,CAAC,EAAE,WAER,MAAAA,OAAiB,uKAFnBA,EAAM,EAAA,EAAC,CAAC,EAAE,KAER2G,EAAA,KAAA6C,EAAA,MAAAxJ,OAAiB,sJAXVA,EAAQ,EAAA,GAAI,EAAIA,EAAQ,CAAA,EAAA,KACTyJ,EAAA,cAAAzJ,MAAWA,EAAQ,CAAA,CAAA,6CAG3B,sQAFF0G,GAAA7D,EAAA7C,EAAK,CAAA,IAAL,YAAA6C,EAAQ,KAAR,MAAA6D,EAAY,IAAG,4CAJnCzG,EAOK5C,EAAAgM,EAAAlJ,CAAA,iIAHeuG,GAAA7D,EAAA7C,EAAK,CAAA,IAAL,YAAA6C,EAAQ,KAAR,MAAA6D,EAAY,IAAG,qBAFpB1G,EAAQ,EAAA,GAAI,EAAIA,EAAQ,CAAA,EAAA,sBACT2G,EAAA,OAAA8C,KAAAA,EAAA,cAAAzJ,MAAWA,EAAQ,CAAA,CAAA,iMAb/B,cAAA,CAAA,GAAA6C,EAAA7C,OAAA,MAAA6C,EAAQ,8UAAR8D,EAAA,IAAA4C,EAAA,cAAA,CAAA,GAAA1G,EAAA7C,OAAA,MAAA6C,EAAQ,8zBA/BrB,OAAAA,EAAA7C,QAAA,MAAA6C,EAAS,KAAC,0EA0BV,MAAA,GAAAA,EAAA7C,EAAS,EAAA,IAAT,MAAA6C,EAAS,KAAM7C,OAAiB,EAAC,EAc5B,GAAA0G,EAAA1G,EAAS,EAAA,IAAT,MAAA0G,EAAS,KAAM1G,OAAiB,EAAC,GASlCqH,EAAArH,QAAA,MAAAqH,EAAS,GAAC,4NAnDA7E,EAAA6G,EAAA,eAAArJ,OAAiB,CAAC,EADvB0C,EAAA2G,EAAA,UAAArJ,EAAiB,CAAA,IAAA,EAAI,OAAS,OAAO,UAFrDC,EAgEK5C,EAAAgM,EAAAlJ,CAAA,gWA7DgBqC,EAAA6G,EAAA,eAAArJ,OAAiB,CAAC,QADvB0C,EAAA2G,EAAA,UAAArJ,EAAiB,CAAA,IAAA,EAAI,OAAS,OAAO,oLApCxBwH,SAAcxH,EAAK,CAAA,GAAIA,EAAI,EAAA,EAAC,aAAa,cAQlE0G,GAAA7D,EAAA7C,EAAK,CAAA,IAAL,YAAA6C,EAAQ,KAAR,YAAA6D,EAAY,QAAOU,GAAAC,EAAArH,EAAK,CAAA,IAAL,YAAAqH,EAAQ,KAAR,YAAAD,EAAY,OAAGsC,GAAA1J,CAAA,MASlCsH,GAAAC,EAAAvH,EAAK,CAAA,IAAL,YAAAuH,EAAQ,KAAR,YAAAD,EAAY,MAAGqC,GAAA3J,CAAA,gCAcT,SAAAA,EAAgB,CAAA,GAAA,GAAM,GAAA4J,EAAA5J,OAAA,MAAA4J,EAAQ,whBA7B1C3J,EAkGK5C,EAAAgM,EAAAlJ,CAAA,uKApGwCH,EAAK,CAAA,GAAIA,EAAI,EAAA,EAAC,aAAa,cAQlE0G,GAAA7D,EAAA7C,EAAK,CAAA,IAAL,YAAA6C,EAAQ,KAAR,MAAA6D,EAAY,MAAOU,GAAAC,EAAArH,EAAK,CAAA,IAAL,YAAAqH,EAAQ,KAAR,MAAAD,EAAY,mGAS/BE,GAAAC,EAAAvH,EAAK,CAAA,IAAL,YAAAuH,EAAQ,KAAR,MAAAD,EAAY,6GAcNX,EAAA,KAAAkD,EAAA,SAAA7J,EAAgB,CAAA,GAAA,GAAM,GAAA4J,EAAA5J,OAAA,MAAA4J,EAAQ,wWA9F9B,CAAA,MAAAhM,CAAA,EAAAuD,GAEA,MAAA4G,EAA4B,MAAA,EAAA5G,EAC5B,CAAA,WAAA8G,CAAA,EAAA9G,EACA,CAAA,KAAAxE,CAAA,EAAAwE,EACA,CAAA,SAAAD,CAAA,EAAAC,GACA,aAAA2I,EAAe,CAAA,EAAA3I,GAEf,qBAAA6G,EAAuB,EAAA,EAAA7G,EACvB,CAAA,aAAAE,CAAA,EAAAF,EACA,CAAA,OAAA4I,CAAA,EAAA5I,EACA,CAAA,eAAA6I,CAAA,EAAA7I,GACA,cAAA8I,EAA+B,IAAA,EAAA9I,EAC/B,CAAA,KAAA+G,CAAA,EAAA/G,EACA,CAAA,WAAAwC,CAAA,EAAAxC,EAEP+I,EAA6CtM,GAAA,CAAU,KAAM,IAAI,EAEjE6E,EACA6F,EACA6B,EAEW,eAAAC,EAAA,CACZ,OAAAC,CACF,EAAA7K,GAAA,CAEM,MAAA8K,GAAA,CAAa1M,EAAM,CAAC,EAAGA,EAAM,CAAC,CAAA,EAIhCyM,EAAO,OAAS,EACnBC,GAAU9K,EAAC,EAAI6K,EAAO,CAAC,EAEvBC,GAAU9K,EAAC,EAAI6K,EAAO7K,EAAC,MAExB5B,EAAQ0M,EAAA,EACF,MAAArG,GAAA,EAENtG,EAAS,SAAU2M,EAAS,MAGzBC,EAAY,SAOV5M,EAAWkG,SAUN,SAAA2G,EAAW,EAAA,EAAArJ,eAgBnBW,EAAA,EAAAZ,EAAW,EAAG,MACdtD,EAAK,CAAI,KAAM,IAAI,CAAA,EACnBD,EAAS,OAAO,4BA+BJ,MAAAuG,EAAAuG,GAAML,EAAcK,EAAG,CAAC,gBAgBtBhI,EAAG7E,mCASN,MAAA8M,EAAAD,GAAML,EAAcK,EAAG,CAAC,0CAhEpBnC,EAAQ,KAAA,YACP6B,EAAS,KAAA,6nBA1BrB,KAAK,UAAUvM,CAAK,IAAM2M,SAChCA,EAAY,KAAK,UAAU3M,CAAK,CAAA,OAChCsM,EAAStM,CAAA,iBAePD,EAAS,OAAQ6M,CAAQ,ysBCnDlB,8hCAfC,CAAA,MAAA5M,EAAA,CAA6C,KAAM,IAAI,CAAA,EAAAuD,EACvD,CAAA,OAAA4I,CAAA,EAAA5I,EACA,CAAA,eAAA6I,CAAA,EAAA7I,EACA,CAAA,MAAA4G,CAAA,EAAA5G,EACA,CAAA,WAAA8G,CAAA,EAAA9G,EACA,CAAA,KAAA+G,CAAA,EAAA/G,EACA,CAAA,KAAAxE,CAAA,EAAAwE,GACA,aAAA2I,EAAe,CAAA,EAAA3I,EACf,CAAA,SAAAqJ,CAAA,EAAArJ,EACA,CAAA,WAAAwC,CAAA,EAAAxC,GACA,cAAA8I,EAA+B,IAAA,EAAA9I,8JAY9B,OAAAkJ,CAAM,IAAAvI,EAAA,EAAQ0I,EAAWH,CAAM,6sDCwHjC,QAAArK,EAAU,CAAA,IAAA,KAAO,SAAW,oBACxBA,EAAQ,EAAA,EAAG,QAAU,eACzB,kCAGD,OAAAA,MAAU,kCAEF,2GAIFA,EAAiB,EAAA,CAAA,oBACjBA,EAAiB,EAAA,CAAA,mBAClBA,EAAiB,EAAA,CAAA,eACrBA,EAAW,EAAA,CAAA,oHAdX2G,EAAA,CAAA,EAAA,IAAAgE,EAAA,QAAA3K,EAAU,CAAA,IAAA,KAAO,SAAW,sCACxBA,EAAQ,EAAA,EAAG,QAAU,+DAI1B2G,EAAA,CAAA,EAAA,MAAAgE,EAAA,OAAA3K,MAAU,yVA/CT,oBACIA,EAAQ,EAAA,EAAG,QAAU,eACzB,kCAGD,OAAAA,MAAU,kCAEF,uUANHA,EAAQ,EAAA,EAAG,QAAU,+DAI1B2G,EAAA,CAAA,EAAA,MAAAgE,EAAA,OAAA3K,MAAU,kYA6FK,oUAFH,KAAAA,MAAO,8HAAP2G,EAAA,CAAA,EAAA,UAAAiE,EAAA,KAAA5K,MAAO,+IAFP,KAAAA,MAAO,+HAAP2G,EAAA,CAAA,EAAA,UAAAiE,EAAA,KAAA5K,MAAO,qYADuB,MAAA,sOAlCrC,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,kNA0BH,cAAAA,MAAO,cAChB,KAAAA,MAAO,kCAEG6C,EAAA7C,EAAM,EAAA,EAAC,SAAP,YAAA6C,EAAe,qiBA/BnB,WAAA7C,MAAO,YACb2G,EAAA,CAAA,EAAA,SAAA,CAAA,KAAA3G,MAAO,IAAI,aACbA,EAAc,CAAA,CAAA,6IA0BH2G,EAAA,CAAA,EAAA,UAAAkE,EAAA,cAAA7K,MAAO,eAChB2G,EAAA,CAAA,EAAA,UAAAkE,EAAA,KAAA7K,MAAO,sEAEG6C,EAAA7C,EAAM,EAAA,EAAC,SAAP,YAAA6C,EAAe,sYA5EnB,WAAA7C,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,+LAgBZ,KAAAA,MAAO,2CAEHA,EAA0B,EAAA,saApBxB,WAAAA,MAAO,YACb2G,EAAA,CAAA,EAAA,SAAA,CAAA,KAAA3G,MAAO,IAAI,aACbA,EAAc,CAAA,CAAA,8LAgBZ2G,EAAA,CAAA,EAAA,UAAAmE,EAAA,KAAA9K,MAAO,8EAEHA,EAA0B,EAAA,6UArCjC,MAAA,CAAAA,QAAgB6C,EAAA7C,EAAK,CAAA,IAAL,MAAA6C,EAAQ,MAAM6D,EAAA1G,OAAA,MAAA0G,EAAQ,IAAC,iUAjExCqE,GAAY,6BAzBL,gBAAAC,EAAkB,EAAA,EAAA7J,GAClB,QAAA8J,EAAU,EAAA,EAAA9J,EACV,CAAA,aAAA+J,EAAA,EAAA,EAAA/J,GACA,QAAAgK,EAAU,EAAA,EAAAhK,EACV,CAAA,MAAAvD,EAAA,CAA6C,KAAM,IAAI,CAAA,EAAAuD,EAC9DoJ,EAAA,CAAiD,KAAM,IAAI,EACpD,CAAA,MAAAxC,CAAA,EAAA5G,EACA,CAAA,WAAA8G,CAAA,EAAA9G,EACA,CAAA,qBAAA6G,CAAA,EAAA7G,EACA,CAAA,KAAAxE,CAAA,EAAAwE,EACA,CAAA,OAAAiK,CAAA,EAAAjK,EACA,CAAA,MAAAU,CAAA,EAAAV,GACA,UAAA/C,EAAY,EAAA,EAAA+C,GACZ,MAAA8D,EAAuB,IAAA,EAAA9D,GACvB,UAAAkK,EAAgC,MAAA,EAAAlK,EAChC,CAAA,eAAAmK,CAAA,EAAAnK,EACA,CAAA,YAAAoH,CAAA,EAAApH,GACA,YAAAoK,EAAkC,MAAA,EAAApK,EAClC,CAAA,uBAAAkH,CAAA,EAAAlH,EACPiC,EAAa,GACN,CAAA,YAAAoI,CAAA,EAAArK,EACA,CAAA,gBAAAsK,CAAA,EAAAtK,GACA,aAAA2I,EAAe,CAAA,EAAA3I,GACf,aAAAE,EAAe,6BAAA,EAAAF,EACf,CAAA,WAAAwC,CAAA,EAAAxC,EAQA,CAAA,OAAAuK,CAAA,EAAAvK,EAyBXwK,GAAA,IAAA,MACCX,EAAkB,EAAA,IAGf,IAAAR,EAEAoB,QAEEC,GAAqB9P,GAAA,OACpB+P,GAAa/P,EACnB+P,GAAW,eAAA,EACXA,GAAW,gBAAA,EACPA,GAAW,OAAS,aAAeA,GAAW,OAAS,gBAC1DtB,EAAW,EAAA,EACDsB,GAAW,OAAS,kBAC9BtB,EAAW,EAAA,GAIPuB,GAAehQ,GAAA,CAChB,GAAAwM,EAAA,OACGyD,GAAajQ,EACnBiQ,GAAW,eAAA,EACXA,GAAW,gBAAA,OACXxB,EAAW,EAAA,EAEPoB,GACHA,EAAiB,kBAAkBI,EAAU,6BA4BhC,MAAAC,GAAA,CAAA,CAAA,OAAA5B,KAAaqB,EAAO,SAAS,SAAUrB,CAAM,EAC9C6B,GAAA,CAAA,CAAA,OAAA7B,KAAaqB,EAAO,SAAS,QAASrB,CAAM,EAC5C8B,GAAA,CAAA,CAAA,OAAA9B,KAAaqB,EAAO,SAAS,QAASrB,CAAM,SACzCqB,EAAO,SAAS,OAAO,OACrB,OAAArB,KAAM,CACvBvI,EAAA,GAAAsB,EAAaiH,CAAM,8BAsCG,MAAA+B,GAAA,IAAAV,EAAO,SAAS,eAAgBJ,CAAc,EA2BzDe,GAAA,IAAAC,IAASZ,EAAO,OAAO,UAAUY,CAAI,2CAvBtCV,EAAgBrJ,8EAIZmJ,EAAO,SAAS,MAAM,UAEpCA,EAAO,SAAS,OAAO,QAEZ,OAAArB,CAAM,IAAAvI,EAAA,GAAQ0I,EAAWH,CAAM,SAC1BqB,EAAO,SAAS,QAAQ,OAC5B,OAAArB,KAAM,CAClBvI,EAAA,EAAAwJ,EAAiBA,GAAc,CAAA,CAAA,MAC/BA,EAAe,OAAS,QAAOA,CAAA,EAC/BI,EAAO,SAAS,QAASrB,CAAM,WAG/BqB,EAAO,SAAS,eAAgB,QAAQ,q+BApJxC5J,EAAA,GAAAyK,EACF,KAAK,IAAI,EAAG,KAAK,IAAI,IAAKd,CAAe,CAAK,EAAA,GAAA,4BAoB1C,KAAK,UAAU7N,CAAK,IAAM,KAAK,UAAU2M,CAAS,SACrDA,EAAY3M,CAAA,EACZ8N,EAAO,SAAS,QAAQ,EACnBV,GACJU,EAAO,SAAS,OAAO,IAtB1B5J,EAAA,GAAG0J,EAAe,CAAAT,EAAA", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6]}