import{SvelteComponent as d,init as f,safe_not_equal as g,svg_element as s,claim_svg_element as a,children as h,detach as l,attr as t,insert_hydration as m,append_hydration as c,noop as u}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function v(p){let e,r,o;return{c(){e=s("svg"),r=s("polyline"),o=s("path"),this.h()},l(n){e=a(n,"svg",{"aria-label":!0,xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0,class:!0});var i=h(e);r=a(i,"polyline",{points:!0}),h(r).forEach(l),o=a(i,"path",{d:!0}),h(o).forEach(l),i.forEach(l),this.h()},h(){t(r,"points","1 4 1 10 7 10"),t(o,"d","M3.51 15a9 9 0 1 0 2.13-9.36L1 10"),t(e,"aria-label","undo"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"width","100%"),t(e,"height","100%"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","1.5"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-rotate-ccw")},m(n,i){m(n,e,i),c(e,r),c(e,o)},p:u,i:u,o:u,d(n){n&&l(e)}}}class k extends d{constructor(e){super(),f(this,e,null,v,g,{})}}export{k as U};
//# sourceMappingURL=Undo.LhwFM5M8.js.map
