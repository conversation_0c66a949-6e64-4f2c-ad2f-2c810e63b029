{"version": 3, "file": "flowGraphPauseAnimationBlock.tGoUJuwx.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/Animation/flowGraphPauseAnimationBlock.js"], "sourcesContent": ["import { FlowGraphExecutionBlockWithOutSignal } from \"../../../flowGraphExecutionBlockWithOutSignal.js\";\nimport { RichTypeAny } from \"../../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\n/**\n * @experimental\n * Block that pauses a running animation\n */\nexport class FlowGraphPauseAnimationBlock extends FlowGraphExecutionBlockWithOutSignal {\n    constructor(config) {\n        super(config);\n        this.animationToPause = this.registerDataInput(\"animationToPause\", RichTypeAny);\n    }\n    _execute(context) {\n        const animationToPauseValue = this.animationToPause.getValue(context);\n        animationToPauseValue.pause();\n        this.out._activateSignal(context);\n    }\n    /**\n     * @returns class name of the block.\n     */\n    getClassName() {\n        return \"FlowGraphPauseAnimationBlock\" /* FlowGraphBlockNames.PauseAnimation */;\n    }\n}\nRegisterClass(\"FlowGraphPauseAnimationBlock\" /* FlowGraphBlockNames.PauseAnimation */, FlowGraphPauseAnimationBlock);\n//# sourceMappingURL=flowGraphPauseAnimationBlock.js.map"], "names": ["FlowGraphPauseAnimationBlock", "FlowGraphExecutionBlockWithOutSignal", "config", "RichTypeAny", "context", "RegisterClass"], "mappings": "gJAOO,MAAMA,UAAqCC,CAAqC,CACnF,YAAYC,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,iBAAmB,KAAK,kBAAkB,mBAAoBC,CAAW,CACjF,CACD,SAASC,EAAS,CACgB,KAAK,iBAAiB,SAASA,CAAO,EAC9C,MAAK,EAC3B,KAAK,IAAI,gBAAgBA,CAAO,CACnC,CAID,cAAe,CACX,MAAO,8BACV,CACL,CACAC,EAAc,+BAAyEL,CAA4B", "x_google_ignoreList": [0]}