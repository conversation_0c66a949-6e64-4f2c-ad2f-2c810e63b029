"""
2D Engine API
提供简单的一行代码调用接口
"""

import os
import uuid
from typing import Optional, List, Dict, Any, Callable
from PIL import Image
from datetime import datetime

from engine_2d.image_generator import image_generator, GenerationParams, GenerationResult
from core.utils.logger import get_logger
from core.utils.config import config_manager

logger = get_logger(__name__)

class Engine2D:
    """2D Engine API类"""
    
    def __init__(self):
        self.generator = image_generator
        self.output_dir = "outputs/images"
        os.makedirs(self.output_dir, exist_ok=True)
    
    def generate(self, 
                prompt: str,
                negative_prompt: str = "",
                width: int = 1024,
                height: int = 1024,
                steps: int = 20,
                guidance: float = 7.5,
                seed: Optional[int] = None,
                model: Optional[str] = None,
                save: bool = True,
                progress_callback: Optional[Callable[[int, int, str], None]] = None) -> Optional[List[str]]:
        """
        生成图像 - 一行代码调用
        
        Args:
            prompt: 提示词
            negative_prompt: 负面提示词
            width: 图像宽度
            height: 图像高度
            steps: 推理步数
            guidance: 引导强度
            seed: 随机种子
            model: 模型名称（可选）
            save: 是否保存图像
            progress_callback: 进度回调
        
        Returns:
            生成的图像文件路径列表
        """
        try:
            # 检查是否需要加载模型
            if model and self.generator.get_current_model() != model:
                if not self.generator.load_model(model):
                    logger.error(f"模型加载失败: {model}")
                    return None
            elif not self.generator.get_current_model():
                # 加载默认模型
                available_models = self.generator.get_available_models()
                if not available_models:
                    logger.error("没有可用的模型")
                    return None
                
                default_model = list(available_models.keys())[0]
                if not self.generator.load_model(default_model):
                    logger.error("默认模型加载失败")
                    return None
            
            # 创建生成参数
            params = GenerationParams(
                prompt=prompt,
                negative_prompt=negative_prompt,
                width=width,
                height=height,
                steps=steps,
                guidance_scale=guidance,
                seed=seed
            )
            
            # 生成图像
            result = self.generator.generate_image(params, progress_callback)
            
            if not result:
                return None
            
            # 保存图像
            if save:
                return self._save_images(result)
            else:
                return result.images
                
        except Exception as e:
            logger.error(f"图像生成失败: {e}")
            return None
    
    def _save_images(self, result: GenerationResult) -> List[str]:
        """保存图像并返回路径"""
        saved_paths = []
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for i, image in enumerate(result.images):
            # 生成文件名
            filename = f"{timestamp}_{result.seed_used}_{i+1}.png"
            filepath = os.path.join(self.output_dir, filename)
            
            # 保存图像
            image.save(filepath)
            saved_paths.append(filepath)
            
            logger.info(f"图像已保存: {filepath}")
        
        return saved_paths
    
    def list_models(self) -> Dict[str, Dict[str, Any]]:
        """列出可用模型"""
        return self.generator.get_available_models()
    
    def load_model(self, model_name: str) -> bool:
        """加载指定模型"""
        return self.generator.load_model(model_name)
    
    def get_current_model(self) -> Optional[str]:
        """获取当前模型"""
        return self.generator.get_current_model()
    
    def create_prompt(self, 
                     subject: str,
                     style: str = "anime",
                     quality: str = "masterpiece",
                     lighting: str = "soft lighting",
                     composition: str = "centered",
                     additional: str = "") -> str:
        """
        创建优化的提示词
        
        Args:
            subject: 主体描述
            style: 风格
            quality: 质量词
            lighting: 光照
            composition: 构图
            additional: 额外描述
        
        Returns:
            优化后的提示词
        """
        prompt_parts = []
        
        # 质量词
        if quality:
            prompt_parts.append(quality)
        
        # 主体
        prompt_parts.append(subject)
        
        # 风格
        if style:
            prompt_parts.append(f"{style} style")
        
        # 光照
        if lighting:
            prompt_parts.append(lighting)
        
        # 构图
        if composition:
            prompt_parts.append(composition)
        
        # 额外描述
        if additional:
            prompt_parts.append(additional)
        
        # 通用质量提升词
        quality_boost = [
            "highly detailed",
            "8k resolution",
            "professional artwork"
        ]
        prompt_parts.extend(quality_boost)
        
        return ", ".join(prompt_parts)
    
    def create_negative_prompt(self, style: str = "anime") -> str:
        """创建负面提示词"""
        base_negative = [
            "low quality",
            "blurry",
            "bad anatomy",
            "bad hands",
            "missing fingers",
            "extra fingers",
            "cropped",
            "worst quality",
            "jpeg artifacts"
        ]
        
        if style == "anime":
            anime_negative = [
                "realistic",
                "3d",
                "photorealistic",
                "western cartoon"
            ]
            base_negative.extend(anime_negative)
        elif style == "realistic":
            realistic_negative = [
                "anime",
                "cartoon",
                "illustration",
                "painting"
            ]
            base_negative.extend(realistic_negative)
        
        return ", ".join(base_negative)

# 全局2D Engine实例
engine_2d = Engine2D()

# 便捷函数
def generate_image(prompt: str, **kwargs) -> Optional[List[str]]:
    """便捷的图像生成函数"""
    return engine_2d.generate(prompt, **kwargs)

def create_prompt(subject: str, **kwargs) -> str:
    """便捷的提示词创建函数"""
    return engine_2d.create_prompt(subject, **kwargs)

def list_models() -> Dict[str, Dict[str, Any]]:
    """便捷的模型列表函数"""
    return engine_2d.list_models()

# 示例代码生成器
def generate_example_code(prompt: str, **params) -> str:
    """生成示例代码"""
    code_lines = [
        "# 2D Engine 图像生成示例",
        "from engine_2d.api import generate_image, create_prompt",
        "",
        "# 创建优化的提示词",
        f'prompt = create_prompt("{prompt}")',
        "",
        "# 生成图像",
        "images = generate_image("
    ]
    
    # 添加参数
    param_lines = [f'    prompt=prompt']
    
    for key, value in params.items():
        if isinstance(value, str):
            param_lines.append(f'    {key}="{value}"')
        else:
            param_lines.append(f'    {key}={value}')
    
    code_lines.append(",\n".join(param_lines))
    code_lines.append(")")
    code_lines.append("")
    code_lines.append("# 查看生成的图像路径")
    code_lines.append("if images:")
    code_lines.append("    for img_path in images:")
    code_lines.append("        print(f'生成的图像: {img_path}')")
    
    return "\n".join(code_lines)
