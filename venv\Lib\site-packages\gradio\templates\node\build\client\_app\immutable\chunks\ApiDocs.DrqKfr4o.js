import{SvelteComponent as ke,init as we,safe_not_equal as ye,element as k,space as W,text as h,create_component as re,claim_element as w,children as O,get_svelte_dataset as te,claim_space as Y,claim_text as m,detach as u,claim_component as ie,attr as $,insert_hydration as v,append_hydration as p,mount_component as ae,listen as Ee,set_data as X,transition_in as G,transition_out as Q,destroy_component as oe,createEventDispatcher as Me,empty as ue,src_url_equal as Bt,group_outros as pe,check_outros as de,noop as _e,ensure_array_like as fe,toggle_class as Ne,destroy_each as ve,set_style as he,binding_callbacks as $e,onMount as Jt,tick as Zt,run_all as Qt,bubble as Fe,bind as Xt,add_flush_callback as Kt}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{C as Gt}from"./clear.D_TEXRKD.js";import{W as xt,o as He,L as Wt,B as Ce}from"./2.B2AoQPnG.js";function el(n){let e,t,l="API Docs",s,i,r,f,o,a,c,_=`To expose an API endpoint of your app in this page, set the <code>api_name</code>
		parameter of the event listener.
		<br/>
		For more information, visit the
		<a href="https://gradio.app/sharing_your_app/#api-page" target="_blank">API Page guide</a>
		. To hide the API documentation button and this page, set
		<code>show_api=False</code>
		in the
		<code>Blocks.launch()</code>
		method.`,d,g,b,N,E,I;return b=new Gt({}),{c(){e=k("div"),t=k("h1"),t.textContent=l,s=W(),i=k("p"),r=h(`No API Routes found for
		`),f=k("code"),o=h(n[0]),a=W(),c=k("p"),c.innerHTML=_,d=W(),g=k("button"),re(b.$$.fragment),this.h()},l(y){e=w(y,"DIV",{class:!0});var D=O(e);t=w(D,"H1",{"data-svelte-h":!0}),te(t)!=="svelte-1nemy2n"&&(t.textContent=l),s=Y(D),i=w(D,"P",{class:!0});var S=O(i);r=m(S,`No API Routes found for
		`),f=w(S,"CODE",{class:!0});var C=O(f);o=m(C,n[0]),C.forEach(u),S.forEach(u),a=Y(D),c=w(D,"P",{"data-svelte-h":!0}),te(c)!=="svelte-2ediv8"&&(c.innerHTML=_),D.forEach(u),d=Y(y),g=w(y,"BUTTON",{class:!0});var P=O(g);ie(b.$$.fragment,P),P.forEach(u),this.h()},h(){$(f,"class","svelte-e1ha0f"),$(i,"class","attention svelte-e1ha0f"),$(e,"class","wrap prose svelte-e1ha0f"),$(g,"class","svelte-e1ha0f")},m(y,D){v(y,e,D),p(e,t),p(e,s),p(e,i),p(i,r),p(i,f),p(f,o),p(e,a),p(e,c),v(y,d,D),v(y,g,D),ae(b,g,null),N=!0,E||(I=Ee(g,"click",n[2]),E=!0)},p(y,[D]){(!N||D&1)&&X(o,y[0])},i(y){N||(G(b.$$.fragment,y),N=!0)},o(y){Q(b.$$.fragment,y),N=!1},d(y){y&&(u(e),u(d),u(g)),oe(b),E=!1,I()}}}function tl(n,e,t){const l=Me();let{root:s}=e;const i=()=>l("close");return n.$$set=r=>{"root"in r&&t(0,s=r.root)},[s,l,i]}class ll extends ke{constructor(e){super(),we(this,e,tl,el,ye,{root:0})}}function nl(n){let e;return{c(){e=h("API")},l(t){e=m(t,"API")},m(t,l){v(t,e,l)},d(t){t&&u(e)}}}function sl(n){let e;return{c(){e=h("MCP")},l(t){e=m(t,"MCP")},m(t,l){v(t,e,l)},d(t){t&&u(e)}}}function Be(n){let e,t;return e=new He({props:{size:"sm",variant:"secondary",elem_id:"start-api-recorder",$$slots:{default:[rl]},$$scope:{ctx:n}}}),e.$on("click",n[4]),{c(){re(e.$$.fragment)},l(l){ie(e.$$.fragment,l)},m(l,s){ae(e,l,s),t=!0},p(l,s){const i={};s&64&&(i.$$scope={dirty:s,ctx:l}),e.$set(i)},i(l){t||(G(e.$$.fragment,l),t=!0)},o(l){Q(e.$$.fragment,l),t=!1},d(l){oe(e,l)}}}function rl(n){let e,t,l,s="API Recorder";return{c(){e=k("div"),t=W(),l=k("p"),l.textContent=s,this.h()},l(i){e=w(i,"DIV",{class:!0}),O(e).forEach(u),t=Y(i),l=w(i,"P",{class:!0,"data-svelte-h":!0}),te(l)!=="svelte-1ywx371"&&(l.textContent=s),this.h()},h(){$(e,"class","loading-dot self-baseline svelte-1i1gjw2"),$(l,"class","self-baseline btn-text svelte-1i1gjw2")},m(i,r){v(i,e,r),v(i,t,r),v(i,l,r)},p:_e,d(i){i&&(u(e),u(t),u(l))}}}function il(n){let e;return{c(){e=h("MCP Tool")},l(t){e=m(t,"MCP Tool")},m(t,l){v(t,e,l)},d(t){t&&u(e)}}}function al(n){let e;return{c(){e=h("API endpoint")},l(t){e=m(t,"API endpoint")},m(t,l){v(t,e,l)},d(t){t&&u(e)}}}function Je(n){let e;return{c(){e=h("s")},l(t){e=m(t,"s")},m(t,l){v(t,e,l)},d(t){t&&u(e)}}}function ol(n){let e,t,l,s,i,r,f,o,a,c,_,d,g,b,N,E,I,y,D,S,C,P,T;function V(Z,R){return Z[2]==="mcp"?sl:nl}let j=V(n),F=j(n),z=n[2]!=="mcp"&&Be(n);function B(Z,R){return Z[2]!=="mcp"?al:il}let q=B(n),H=q(n),L=n[1]>1&&Je();return S=new Gt({}),{c(){e=k("h2"),t=k("img"),s=W(),i=k("div"),F.c(),r=h(` documentation
		`),f=k("div"),o=h(n[0]),a=W(),c=k("span"),z&&z.c(),_=W(),d=k("p"),g=k("span"),b=h(n[1]),N=W(),H.c(),E=ue(),L&&L.c(),I=k("br"),y=W(),D=k("button"),re(S.$$.fragment),this.h()},l(Z){e=w(Z,"H2",{class:!0});var R=O(e);t=w(R,"IMG",{src:!0,alt:!0,class:!0}),s=Y(R),i=w(R,"DIV",{class:!0});var M=O(i);F.l(M),r=m(M,` documentation
		`),f=w(M,"DIV",{class:!0});var J=O(f);o=m(J,n[0]),J.forEach(u),M.forEach(u),a=Y(R),c=w(R,"SPAN",{class:!0});var le=O(c);z&&z.l(le),_=Y(le),d=w(le,"P",{});var ee=O(d);g=w(ee,"SPAN",{class:!0});var ce=O(g);b=m(ce,n[1]),ce.forEach(u),N=Y(ee),H.l(ee),E=ue(),L&&L.l(ee),I=w(ee,"BR",{}),ee.forEach(u),le.forEach(u),R.forEach(u),y=Y(Z),D=w(Z,"BUTTON",{class:!0});var U=O(D);ie(S.$$.fragment,U),U.forEach(u),this.h()},h(){Bt(t.src,l=xt)||$(t,"src",l),$(t,"alt",""),$(t,"class","svelte-1i1gjw2"),$(f,"class","url svelte-1i1gjw2"),$(i,"class","title svelte-1i1gjw2"),$(g,"class","url svelte-1i1gjw2"),$(c,"class","counts svelte-1i1gjw2"),$(e,"class","svelte-1i1gjw2"),$(D,"class","svelte-1i1gjw2")},m(Z,R){v(Z,e,R),p(e,t),p(e,s),p(e,i),F.m(i,null),p(i,r),p(i,f),p(f,o),p(e,a),p(e,c),z&&z.m(c,null),p(c,_),p(c,d),p(d,g),p(g,b),p(d,N),H.m(d,null),p(d,E),L&&L.m(d,null),p(d,I),v(Z,y,R),v(Z,D,R),ae(S,D,null),C=!0,P||(T=Ee(D,"click",n[5]),P=!0)},p(Z,[R]){j!==(j=V(Z))&&(F.d(1),F=j(Z),F&&(F.c(),F.m(i,r))),(!C||R&1)&&X(o,Z[0]),Z[2]!=="mcp"?z?(z.p(Z,R),R&4&&G(z,1)):(z=Be(Z),z.c(),G(z,1),z.m(c,_)):z&&(pe(),Q(z,1,1,()=>{z=null}),de()),(!C||R&2)&&X(b,Z[1]),q!==(q=B(Z))&&(H.d(1),H=q(Z),H&&(H.c(),H.m(d,E))),Z[1]>1?L||(L=Je(),L.c(),L.m(d,I)):L&&(L.d(1),L=null)},i(Z){C||(G(z),G(S.$$.fragment,Z),C=!0)},o(Z){Q(z),Q(S.$$.fragment,Z),C=!1},d(Z){Z&&(u(e),u(y),u(D)),F.d(),z&&z.d(),H.d(),L&&L.d(),oe(S),P=!1,T()}}}function cl(n,e,t){let{root:l}=e,{api_count:s}=e,{current_language:i="python"}=e;const r=Me(),f=()=>r("close",{api_recorder_visible:!0}),o=()=>r("close");return n.$$set=a=>{"root"in a&&t(0,l=a.root),"api_count"in a&&t(1,s=a.api_count),"current_language"in a&&t(2,i=a.current_language)},[l,s,i,r,f,o]}class fl extends ke{constructor(e){super(),we(this,e,cl,ol,ye,{root:0,api_count:1,current_language:2})}}function be(n,e,t=null){return e===void 0?t==="py"?"None":null:n===null&&t==="py"?"None":e==="string"||e==="str"?t===null?n:'"'+n+'"':e==="number"?t===null?parseFloat(n):n:e==="boolean"||e=="bool"?t==="py"?(n=String(n),n==="true"?"True":"False"):t==="js"||t==="bash"?n:n==="true":e==="List[str]"?(n=JSON.stringify(n),n):e.startsWith("Literal['")?'"'+n+'"':t===null?n===""?null:JSON.parse(n):typeof n=="string"?n===""?t==="py"?"None":"null":n:(t==="bash"&&(n=ze(n)),t==="py"&&(n=qe(n)),ul(n))}function Yt(n){if(typeof n=="object"&&n!==null&&n.hasOwnProperty("url")&&n.hasOwnProperty("meta")&&typeof n.meta=="object"&&n.meta!==null&&n.meta._type==="gradio.FileData")return!0;if(typeof n=="object"&&n!==null){for(let e in n)if(typeof n[e]=="object"&&Yt(n[e]))return!0}return!1}function ze(n){var e;return typeof n=="object"&&n!==null&&!Array.isArray(n)&&"url"in n&&n.url&&"meta"in n&&((e=n.meta)==null?void 0:e._type)==="gradio.FileData"?{path:n.url,meta:{_type:"gradio.FileData"}}:(Array.isArray(n)?n.forEach((t,l)=>{typeof t=="object"&&t!==null&&(n[l]=ze(t))}):typeof n=="object"&&n!==null&&Object.keys(n).forEach(t=>{n[t]=ze(n[t])}),n)}function qe(n){var e;return typeof n=="object"&&n!==null&&!Array.isArray(n)&&"url"in n&&n.url&&"meta"in n&&((e=n.meta)==null?void 0:e._type)==="gradio.FileData"?`handle_file('${n.url}')`:(Array.isArray(n)?n.forEach((t,l)=>{typeof t=="object"&&t!==null&&(n[l]=qe(t))}):typeof n=="object"&&n!==null&&Object.keys(n).forEach(t=>{n[t]=qe(n[t])}),n)}function ul(n){let e=JSON.stringify(n,(s,i)=>i===null?"UNQUOTEDNone":typeof i=="string"&&i.startsWith("handle_file(")&&i.endsWith(")")?`UNQUOTED${i}`:i);const t=/"UNQUOTEDhandle_file\(([^)]*)\)"/g;e=e.replace(t,(s,i)=>`handle_file(${i})`);const l=/"UNQUOTEDNone"/g;return e.replace(l,"None")}function Ge(n,e,t){const l=n.slice();return l[4]=e[t].label,l[5]=e[t].python_type,l[6]=e[t].component,l[7]=e[t].parameter_name,l[8]=e[t].parameter_has_default,l[9]=e[t].parameter_default,l[11]=t,l}function We(n){let e;return{c(){e=h("s")},l(t){e=m(t,"s")},m(t,l){v(t,e,l)},d(t){t&&u(e)}}}function _l(n){let e=(n[2][n[11]].type||"any")+"",t;return{c(){t=h(e)},l(l){t=m(l,e)},m(l,s){v(l,t,s)},p(l,s){s&4&&e!==(e=(l[2][l[11]].type||"any")+"")&&X(t,e)},d(l){l&&u(t)}}}function pl(n){let e=n[5].type+"",t,l,s=n[8]&&n[9]===null&&Ye();return{c(){t=h(e),s&&s.c(),l=ue()},l(i){t=m(i,e),s&&s.l(i),l=ue()},m(i,r){v(i,t,r),s&&s.m(i,r),v(i,l,r)},p(i,r){r&2&&e!==(e=i[5].type+"")&&X(t,e),i[8]&&i[9]===null?s||(s=Ye(),s.c(),s.m(l.parentNode,l)):s&&(s.d(1),s=null)},d(i){i&&(u(t),u(l)),s&&s.d(i)}}}function Ye(n){let e;return{c(){e=h(` |
							None`)},l(t){e=m(t,` |
							None`)},m(t,l){v(t,e,l)},d(t){t&&u(e)}}}function dl(n){let e,t="Default: ",l,s=be(n[9],n[5].type,"py")+"",i;return{c(){e=k("span"),e.textContent=t,l=k("span"),i=h(s),this.h()},l(r){e=w(r,"SPAN",{"data-svelte-h":!0}),te(e)!=="svelte-y3zgxh"&&(e.textContent=t),l=w(r,"SPAN",{class:!0,style:!0});var f=O(l);i=m(f,s),f.forEach(u),this.h()},h(){$(l,"class","code svelte-1yt946s"),he(l,"font-size","var(--text-sm)")},m(r,f){v(r,e,f),v(r,l,f),p(l,i)},p(r,f){f&2&&s!==(s=be(r[9],r[5].type,"py")+"")&&X(i,s)},d(r){r&&(u(e),u(l))}}}function hl(n){let e,t="Required";return{c(){e=k("span"),e.textContent=t,this.h()},l(l){e=w(l,"SPAN",{style:!0,"data-svelte-h":!0}),te(e)!=="svelte-1y2sdp"&&(e.textContent=t),this.h()},h(){he(e,"font-weight","bold")},m(l,s){v(l,e,s)},p:_e,d(l){l&&u(e)}}}function Ze(n){let e,t,l,s,i,r=(n[3]!=="bash"&&n[7]?n[7]:"["+n[11]+"]")+"",f,o,a,c,_,d,g,b=n[4]+"",N,E,I=n[6]+"",y,D,S=n[5].description+"",C,P;function T(q,H){return q[3]==="python"?pl:_l}let V=T(n),j=V(n);function F(q,H){return!q[8]||q[3]=="bash"?hl:dl}let z=F(n),B=z(n);return{c(){e=k("hr"),t=W(),l=k("div"),s=k("p"),i=k("span"),f=h(r),o=W(),a=k("span"),j.c(),c=W(),B.c(),_=W(),d=k("p"),g=h('The input value that is provided in the "'),N=h(b),E=h('" '),y=h(I),D=h(`
				component. `),C=h(S),P=W(),this.h()},l(q){e=w(q,"HR",{class:!0}),t=Y(q),l=w(q,"DIV",{style:!0});var H=O(l);s=w(H,"P",{style:!0});var L=O(s);i=w(L,"SPAN",{class:!0,style:!0});var Z=O(i);f=m(Z,r),Z.forEach(u),o=Y(L),a=w(L,"SPAN",{class:!0,style:!0});var R=O(a);j.l(R),R.forEach(u),c=Y(L),B.l(L),L.forEach(u),_=Y(H),d=w(H,"P",{class:!0});var M=O(d);g=m(M,'The input value that is provided in the "'),N=m(M,b),E=m(M,'" '),y=m(M,I),D=m(M,`
				component. `),C=m(M,S),M.forEach(u),P=Y(H),H.forEach(u),this.h()},h(){$(e,"class","hr svelte-1yt946s"),$(i,"class","code svelte-1yt946s"),he(i,"margin-right","10px"),$(a,"class","code highlight svelte-1yt946s"),he(a,"margin-right","10px"),he(s,"white-space","nowrap"),he(s,"overflow-x","auto"),$(d,"class","desc svelte-1yt946s"),he(l,"margin","10px")},m(q,H){v(q,e,H),v(q,t,H),v(q,l,H),p(l,s),p(s,i),p(i,f),p(s,o),p(s,a),j.m(a,null),p(s,c),B.m(s,null),p(l,_),p(l,d),p(d,g),p(d,N),p(d,E),p(d,y),p(d,D),p(d,C),p(l,P)},p(q,H){H&10&&r!==(r=(q[3]!=="bash"&&q[7]?q[7]:"["+q[11]+"]")+"")&&X(f,r),V===(V=T(q))&&j?j.p(q,H):(j.d(1),j=V(q),j&&(j.c(),j.m(a,null))),z===(z=F(q))&&B?B.p(q,H):(B.d(1),B=z(q),B&&(B.c(),B.m(s,null))),H&2&&b!==(b=q[4]+"")&&X(N,b),H&2&&I!==(I=q[6]+"")&&X(y,I),H&2&&S!==(S=q[5].description+"")&&X(C,S)},d(q){q&&(u(e),u(t),u(l)),j.d(),B.d()}}}function Qe(n){let e,t,l;return t=new Wt({props:{margin:!1}}),{c(){e=k("div"),re(t.$$.fragment),this.h()},l(s){e=w(s,"DIV",{class:!0});var i=O(e);ie(t.$$.fragment,i),i.forEach(u),this.h()},h(){$(e,"class","load-wrap")},m(s,i){v(s,e,i),ae(t,e,null),l=!0},i(s){l||(G(t.$$.fragment,s),l=!0)},o(s){Q(t.$$.fragment,s),l=!1},d(s){s&&u(e),oe(t)}}}function ml(n){let e,t,l='<div class="toggle-dot svelte-1yt946s"></div>',s,i=n[1].length+"",r,f,o,a,c,_,d,g,b=n[1].length!=1&&We(),N=fe(n[1]),E=[];for(let y=0;y<N.length;y+=1)E[y]=Ze(Ge(n,N,y));let I=n[0]&&Qe();return{c(){e=k("h4"),t=k("div"),t.innerHTML=l,s=h(`
	Accepts `),r=h(i),f=h(" parameter"),b&&b.c(),o=h(":"),a=W(),c=k("div");for(let y=0;y<E.length;y+=1)E[y].c();_=W(),I&&I.c(),d=ue(),this.h()},l(y){e=w(y,"H4",{class:!0});var D=O(e);t=w(D,"DIV",{class:!0,"data-svelte-h":!0}),te(t)!=="svelte-1pmwe5h"&&(t.innerHTML=l),s=m(D,`
	Accepts `),r=m(D,i),f=m(D," parameter"),b&&b.l(D),o=m(D,":"),D.forEach(u),a=Y(y),c=w(y,"DIV",{});var S=O(c);for(let C=0;C<E.length;C+=1)E[C].l(S);S.forEach(u),_=Y(y),I&&I.l(y),d=ue(),this.h()},h(){$(t,"class","toggle-icon svelte-1yt946s"),$(e,"class","svelte-1yt946s"),Ne(c,"hide",n[0])},m(y,D){v(y,e,D),p(e,t),p(e,s),p(e,r),p(e,f),b&&b.m(e,null),p(e,o),v(y,a,D),v(y,c,D);for(let S=0;S<E.length;S+=1)E[S]&&E[S].m(c,null);v(y,_,D),I&&I.m(y,D),v(y,d,D),g=!0},p(y,[D]){if((!g||D&2)&&i!==(i=y[1].length+"")&&X(r,i),y[1].length!=1?b||(b=We(),b.c(),b.m(e,o)):b&&(b.d(1),b=null),D&14){N=fe(y[1]);let S;for(S=0;S<N.length;S+=1){const C=Ge(y,N,S);E[S]?E[S].p(C,D):(E[S]=Ze(C),E[S].c(),E[S].m(c,null))}for(;S<E.length;S+=1)E[S].d(1);E.length=N.length}(!g||D&1)&&Ne(c,"hide",y[0]),y[0]?I?D&1&&G(I,1):(I=Qe(),I.c(),G(I,1),I.m(d.parentNode,d)):I&&(pe(),Q(I,1,1,()=>{I=null}),de())},i(y){g||(G(I),g=!0)},o(y){Q(I),g=!1},d(y){y&&(u(e),u(a),u(c),u(_),u(d)),b&&b.d(),ve(E,y),I&&I.d(y)}}}function vl(n,e,t){let{is_running:l}=e,{endpoint_returns:s}=e,{js_returns:i}=e,{current_language:r}=e;return n.$$set=f=>{"is_running"in f&&t(0,l=f.is_running),"endpoint_returns"in f&&t(1,s=f.endpoint_returns),"js_returns"in f&&t(2,i=f.js_returns),"current_language"in f&&t(3,r=f.current_language)},[l,s,i,r]}class gl extends ke{constructor(e){super(),we(this,e,vl,ml,ye,{is_running:0,endpoint_returns:1,js_returns:2,current_language:3})}}function bl(n){let e;return{c(){e=h(n[0])},l(t){e=m(t,n[0])},m(t,l){v(t,e,l)},p(t,l){l&1&&X(e,t[0])},d(t){t&&u(e)}}}function kl(n){let e,t;return e=new He({props:{size:"sm",$$slots:{default:[bl]},$$scope:{ctx:n}}}),e.$on("click",n[1]),{c(){re(e.$$.fragment)},l(l){ie(e.$$.fragment,l)},m(l,s){ae(e,l,s),t=!0},p(l,[s]){const i={};s&9&&(i.$$scope={dirty:s,ctx:l}),e.$set(i)},i(l){t||(G(e.$$.fragment,l),t=!0)},o(l){Q(e.$$.fragment,l),t=!1},d(l){oe(e,l)}}}function wl(n,e,t){let{code:l}=e,s="copy";function i(){navigator.clipboard.writeText(l),t(0,s="copied!"),setTimeout(()=>{t(0,s="copy")},1500)}return n.$$set=r=>{"code"in r&&t(2,l=r.code)},[s,i,l]}class ge extends ke{constructor(e){super(),we(this,e,wl,kl,ye,{code:2})}}function yl(n){let e,t,l,s,i,r,f,o;return t=new ge({props:{code:Oe}}),{c(){e=k("div"),re(t.$$.fragment),l=W(),s=k("div"),i=k("pre"),r=h("$ "),f=h(Oe),this.h()},l(a){e=w(a,"DIV",{class:!0});var c=O(e);ie(t.$$.fragment,c),c.forEach(u),l=Y(a),s=w(a,"DIV",{});var _=O(s);i=w(_,"PRE",{class:!0});var d=O(i);r=m(d,"$ "),f=m(d,Oe),d.forEach(u),_.forEach(u),this.h()},h(){$(e,"class","copy svelte-hq8ezf"),$(i,"class","svelte-hq8ezf")},m(a,c){v(a,e,c),ae(t,e,null),v(a,l,c),v(a,s,c),p(s,i),p(i,r),p(i,f),o=!0},p:_e,i(a){o||(G(t.$$.fragment,a),o=!0)},o(a){Q(t.$$.fragment,a),o=!1},d(a){a&&(u(e),u(l),u(s)),oe(t)}}}function El(n){let e,t,l,s,i,r,f,o;return t=new ge({props:{code:je}}),{c(){e=k("div"),re(t.$$.fragment),l=W(),s=k("div"),i=k("pre"),r=h("$ "),f=h(je),this.h()},l(a){e=w(a,"DIV",{class:!0});var c=O(e);ie(t.$$.fragment,c),c.forEach(u),l=Y(a),s=w(a,"DIV",{});var _=O(s);i=w(_,"PRE",{class:!0});var d=O(i);r=m(d,"$ "),f=m(d,je),d.forEach(u),_.forEach(u),this.h()},h(){$(e,"class","copy svelte-hq8ezf"),$(i,"class","svelte-hq8ezf")},m(a,c){v(a,e,c),ae(t,e,null),v(a,l,c),v(a,s,c),p(s,i),p(i,r),p(i,f),o=!0},p:_e,i(a){o||(G(t.$$.fragment,a),o=!0)},o(a){Q(t.$$.fragment,a),o=!1},d(a){a&&(u(e),u(l),u(s)),oe(t)}}}function Cl(n){let e,t,l,s,i,r,f,o;return t=new ge({props:{code:Ae}}),{c(){e=k("div"),re(t.$$.fragment),l=W(),s=k("div"),i=k("pre"),r=h("$ "),f=h(Ae),this.h()},l(a){e=w(a,"DIV",{class:!0});var c=O(e);ie(t.$$.fragment,c),c.forEach(u),l=Y(a),s=w(a,"DIV",{});var _=O(s);i=w(_,"PRE",{class:!0});var d=O(i);r=m(d,"$ "),f=m(d,Ae),d.forEach(u),_.forEach(u),this.h()},h(){$(e,"class","copy svelte-hq8ezf"),$(i,"class","svelte-hq8ezf")},m(a,c){v(a,e,c),ae(t,e,null),v(a,l,c),v(a,s,c),p(s,i),p(i,r),p(i,f),o=!0},p:_e,i(a){o||(G(t.$$.fragment,a),o=!0)},o(a){Q(t.$$.fragment,a),o=!1},d(a){a&&(u(e),u(l),u(s)),oe(t)}}}function Pl(n){let e,t,l,s;const i=[Cl,El,yl],r=[];function f(o,a){return o[0]==="python"?0:o[0]==="javascript"?1:o[0]==="bash"?2:-1}return~(t=f(n))&&(l=r[t]=i[t](n)),{c(){e=k("code"),l&&l.c(),this.h()},l(o){e=w(o,"CODE",{class:!0});var a=O(e);l&&l.l(a),a.forEach(u),this.h()},h(){$(e,"class","svelte-hq8ezf")},m(o,a){v(o,e,a),~t&&r[t].m(e,null),s=!0},p(o,a){let c=t;t=f(o),t===c?~t&&r[t].p(o,a):(l&&(pe(),Q(r[c],1,1,()=>{r[c]=null}),de()),~t?(l=r[t],l?l.p(o,a):(l=r[t]=i[t](o),l.c()),G(l,1),l.m(e,null)):l=null)},i(o){s||(G(l),s=!0)},o(o){Q(l),s=!1},d(o){o&&u(e),~t&&r[t].d()}}}function $l(n){let e,t;return e=new Ce({props:{$$slots:{default:[Pl]},$$scope:{ctx:n}}}),{c(){re(e.$$.fragment)},l(l){ie(e.$$.fragment,l)},m(l,s){ae(e,l,s),t=!0},p(l,[s]){const i={};s&3&&(i.$$scope={dirty:s,ctx:l}),e.$set(i)},i(l){t||(G(e.$$.fragment,l),t=!0)},o(l){Q(e.$$.fragment,l),t=!1},d(l){oe(e,l)}}}let Ae="pip install gradio_client",je="npm i -D @gradio/client",Oe="curl --version";function Sl(n,e,t){let{current_language:l}=e;return n.$$set=s=>{"current_language"in s&&t(0,l=s.current_language)},[l]}class Tl extends ke{constructor(e){super(),we(this,e,Sl,$l,ye,{current_language:0})}}function Nl(n){let e,t,l,s="/"+n[0],i,r,f,o;return{c(){e=k("h3"),t=h(`API name:
	`),l=k("span"),i=h(s),r=W(),f=k("span"),o=h(n[1]),this.h()},l(a){e=w(a,"H3",{class:!0});var c=O(e);t=m(c,`API name:
	`),l=w(c,"SPAN",{class:!0});var _=O(l);i=m(_,s),_.forEach(u),r=Y(c),f=w(c,"SPAN",{class:!0});var d=O(f);o=m(d,n[1]),d.forEach(u),c.forEach(u),this.h()},h(){$(l,"class","post svelte-1y4an3z"),$(f,"class","desc svelte-1y4an3z"),$(e,"class","svelte-1y4an3z")},m(a,c){v(a,e,c),p(e,t),p(e,l),p(l,i),p(e,r),p(e,f),p(f,o)},p(a,[c]){c&1&&s!==(s="/"+a[0])&&X(i,s),c&2&&X(o,a[1])},i:_e,o:_e,d(a){a&&u(e)}}}function Il(n,e,t){let{api_name:l=null}=e,{description:s=null}=e;return n.$$set=i=>{"api_name"in i&&t(0,l=i.api_name),"description"in i&&t(1,s=i.description)},[l,s]}class Dl extends ke{constructor(e){super(),we(this,e,Il,Nl,ye,{api_name:0,description:1})}}function Xe(n,e,t){const l=n.slice();return l[27]=e[t].label,l[22]=e[t].parameter_name,l[28]=e[t].type,l[20]=e[t].python_type,l[29]=e[t].component,l[21]=e[t].example_input,l[30]=e[t].serializer,l[26]=t,l}function Ke(n,e,t){const l=n.slice();return l[27]=e[t].label,l[22]=e[t].parameter_name,l[28]=e[t].type,l[20]=e[t].python_type,l[29]=e[t].component,l[21]=e[t].example_input,l[30]=e[t].serializer,l[26]=t,l}function xe(n,e,t){const l=n.slice();return l[29]=e[t].component,l[21]=e[t].example_input,l[26]=t,l}function et(n,e,t){const l=n.slice();return l[20]=e[t].python_type,l[21]=e[t].example_input,l[22]=e[t].parameter_name,l[23]=e[t].parameter_has_default,l[24]=e[t].parameter_default,l[26]=t,l}function Al(n){let e,t;return e=new Ce({props:{$$slots:{default:[Vl]},$$scope:{ctx:n}}}),{c(){re(e.$$.fragment)},l(l){ie(e.$$.fragment,l)},m(l,s){ae(e,l,s),t=!0},p(l,s){const i={};s[0]&3593|s[1]&8&&(i.$$scope={dirty:s,ctx:l}),e.$set(i)},i(l){t||(G(e.$$.fragment,l),t=!0)},o(l){Q(e.$$.fragment,l),t=!1},d(l){oe(e,l)}}}function jl(n){let e,t;return e=new Ce({props:{$$slots:{default:[Rl]},$$scope:{ctx:n}}}),{c(){re(e.$$.fragment)},l(l){ie(e.$$.fragment,l)},m(l,s){ae(e,l,s),t=!0},p(l,s){const i={};s[0]&287|s[1]&8&&(i.$$scope={dirty:s,ctx:l}),e.$set(i)},i(l){t||(G(e.$$.fragment,l),t=!0)},o(l){Q(e.$$.fragment,l),t=!1},d(l){oe(e,l)}}}function Ol(n){let e,t;return e=new Ce({props:{$$slots:{default:[Hl]},$$scope:{ctx:n}}}),{c(){re(e.$$.fragment)},l(l){ie(e.$$.fragment,l)},m(l,s){ae(e,l,s),t=!0},p(l,s){const i={};s[0]&159|s[1]&8&&(i.$$scope={dirty:s,ctx:l}),e.$set(i)},i(l){t||(G(e.$$.fragment,l),t=!0)},o(l){Q(e.$$.fragment,l),t=!1},d(l){oe(e,l)}}}function tt(n){let e;return{c(){e=h(",")},l(t){e=m(t,",")},m(t,l){v(t,e,l)},d(t){t&&u(e)}}}function lt(n){let e,t=be(n[21],n[20].type,"bash")+"",l,s,i=n[26]<n[3].length-1&&tt();return{c(){e=h(`
							`),l=h(t),i&&i.c(),s=ue()},l(r){e=m(r,`
							`),l=m(r,t),i&&i.l(r),s=ue()},m(r,f){v(r,e,f),v(r,l,f),i&&i.m(r,f),v(r,s,f)},p(r,f){f[0]&8&&t!==(t=be(r[21],r[20].type,"bash")+"")&&X(l,t),r[26]<r[3].length-1?i||(i=tt(),i.c(),i.m(s.parentNode,s)):i&&(i.d(1),i=null)},d(r){r&&(u(e),u(l),u(s)),i&&i.d(r)}}}function Vl(n){var le;let e,t,l,s,i,r,f,o,a,c,_=n[0].api_name+"",d,g,b="{",N,E,I,y="}",D,S,C="{",P,T,V="}",j,F,z,B,q,H=n[0].api_name+"",L,Z,R;l=new ge({props:{code:(le=n[9])==null?void 0:le.innerText}});let M=fe(n[3]),J=[];for(let ee=0;ee<M.length;ee+=1)J[ee]=lt(Xe(n,M,ee));return{c(){e=k("code"),t=k("div"),re(l.$$.fragment),s=W(),i=k("div"),r=k("pre"),f=h("curl -X POST "),o=h(n[10]),a=h(n[11]),c=h("/call/"),d=h(_),g=h(` -s -H "Content-Type: application/json" -d '`),N=h(b),E=h(`
  "data": [`);for(let ee=0;ee<J.length;ee+=1)J[ee].c();I=h(`
]`),D=h(y),S=h(`' \\
  | awk -F'"' '`),P=h(C),T=h(" print $4"),j=h(V),F=h(`'  \\
  | read EVENT_ID; curl -N `),z=h(n[10]),B=h(n[11]),q=h("/call/"),L=h(H),Z=h("/$EVENT_ID"),this.h()},l(ee){e=w(ee,"CODE",{class:!0});var ce=O(e);t=w(ce,"DIV",{class:!0});var U=O(t);ie(l.$$.fragment,U),U.forEach(u),s=Y(ce),i=w(ce,"DIV",{});var ne=O(i);r=w(ne,"PRE",{class:!0});var A=O(r);f=m(A,"curl -X POST "),o=m(A,n[10]),a=m(A,n[11]),c=m(A,"/call/"),d=m(A,_),g=m(A,` -s -H "Content-Type: application/json" -d '`),N=m(A,b),E=m(A,`
  "data": [`);for(let K=0;K<J.length;K+=1)J[K].l(A);I=m(A,`
]`),D=m(A,y),S=m(A,`' \\
  | awk -F'"' '`),P=m(A,C),T=m(A," print $4"),j=m(A,V),F=m(A,`'  \\
  | read EVENT_ID; curl -N `),z=m(A,n[10]),B=m(A,n[11]),q=m(A,"/call/"),L=m(A,H),Z=m(A,"/$EVENT_ID"),A.forEach(u),ne.forEach(u),ce.forEach(u),this.h()},h(){$(t,"class","copy svelte-114qcyq"),$(r,"class","svelte-114qcyq"),$(e,"class","svelte-114qcyq")},m(ee,ce){v(ee,e,ce),p(e,t),ae(l,t,null),p(e,s),p(e,i),p(i,r),p(r,f),p(r,o),p(r,a),p(r,c),p(r,d),p(r,g),p(r,N),p(r,E);for(let U=0;U<J.length;U+=1)J[U]&&J[U].m(r,null);p(r,I),p(r,D),p(r,S),p(r,P),p(r,T),p(r,j),p(r,F),p(r,z),p(r,B),p(r,q),p(r,L),p(r,Z),n[18](i),R=!0},p(ee,ce){var ne;const U={};if(ce[0]&512&&(U.code=(ne=ee[9])==null?void 0:ne.innerText),l.$set(U),(!R||ce[0]&1024)&&X(o,ee[10]),(!R||ce[0]&2048)&&X(a,ee[11]),(!R||ce[0]&1)&&_!==(_=ee[0].api_name+"")&&X(d,_),ce[0]&8){M=fe(ee[3]);let A;for(A=0;A<M.length;A+=1){const K=Xe(ee,M,A);J[A]?J[A].p(K,ce):(J[A]=lt(K),J[A].c(),J[A].m(r,I))}for(;A<J.length;A+=1)J[A].d(1);J.length=M.length}(!R||ce[0]&1024)&&X(z,ee[10]),(!R||ce[0]&2048)&&X(B,ee[11]),(!R||ce[0]&1)&&H!==(H=ee[0].api_name+"")&&X(L,H)},i(ee){R||(G(l.$$.fragment,ee),R=!0)},o(ee){Q(l.$$.fragment,ee),R=!1},d(ee){ee&&u(e),oe(l),ve(J,ee),n[18](null)}}}function nt(n){let e,t,l,s=n[21].url+"",i,r,f=n[29]+"",o,a,c,_;return{c(){e=h(`
const response_`),t=h(n[26]),l=h(' = await fetch("'),i=h(s),r=h(`");
const example`),o=h(f),a=h(" = await response_"),c=h(n[26]),_=h(`.blob();
						`)},l(d){e=m(d,`
const response_`),t=m(d,n[26]),l=m(d,' = await fetch("'),i=m(d,s),r=m(d,`");
const example`),o=m(d,f),a=m(d," = await response_"),c=m(d,n[26]),_=m(d,`.blob();
						`)},m(d,g){v(d,e,g),v(d,t,g),v(d,l,g),v(d,i,g),v(d,r,g),v(d,o,g),v(d,a,g),v(d,c,g),v(d,_,g)},p:_e,d(d){d&&(u(e),u(t),u(l),u(i),u(r),u(o),u(a),u(c),u(_))}}}function st(n){let e,t,l;return{c(){e=h(', {auth: ["'),t=h(n[4]),l=h('", **password**]}')},l(s){e=m(s,', {auth: ["'),t=m(s,n[4]),l=m(s,'", **password**]}')},m(s,i){v(s,e,i),v(s,t,i),v(s,l,i)},p(s,i){i[0]&16&&X(t,s[4])},d(s){s&&(u(e),u(t),u(l))}}}function zl(n){let e,t,l=n[22]+"",s,i,r=be(n[21],n[20].type,"js")+"",f,o;return{c(){e=h(`		
		`),t=k("span"),s=h(l),i=h(": "),f=h(r),o=h(", "),this.h()},l(a){e=m(a,`		
		`),t=w(a,"SPAN",{class:!0});var c=O(t);s=m(c,l),i=m(c,": "),f=m(c,r),c.forEach(u),o=m(a,", "),this.h()},h(){$(t,"class","example-inputs")},m(a,c){v(a,e,c),v(a,t,c),p(t,s),p(t,i),p(t,f),v(a,o,c)},p(a,c){c[0]&8&&l!==(l=a[22]+"")&&X(s,l),c[0]&8&&r!==(r=be(a[21],a[20].type,"js")+"")&&X(f,r)},d(a){a&&(u(e),u(t),u(o))}}}function ql(n){let e,t,l=n[22]+"",s,i,r=n[29]+"",f,o,a,c="";return{c(){e=h(`
				`),t=k("span"),s=h(l),i=h(": example"),f=h(r),o=h(", "),a=k("span"),a.innerHTML=c,this.h()},l(_){e=m(_,`
				`),t=w(_,"SPAN",{class:!0});var d=O(t);s=m(d,l),i=m(d,": example"),f=m(d,r),d.forEach(u),o=m(_,", "),a=w(_,"SPAN",{class:!0,"data-svelte-h":!0}),te(a)!=="svelte-1hu8bzt"&&(a.innerHTML=c),this.h()},h(){$(t,"class","example-inputs"),$(a,"class","desc svelte-114qcyq")},m(_,d){v(_,e,d),v(_,t,d),p(t,s),p(t,i),p(t,f),v(_,o,d),v(_,a,d)},p(_,d){d[0]&8&&l!==(l=_[22]+"")&&X(s,l),d[0]&8&&r!==(r=_[29]+"")&&X(f,r)},d(_){_&&(u(e),u(t),u(o),u(a))}}}function rt(n){let e,t;function l(r,f){return f[0]&8&&(e=null),e==null&&(e=!!r[13].includes(r[29])),e?ql:zl}let s=l(n,[-1,-1]),i=s(n);return{c(){i.c(),t=ue()},l(r){i.l(r),t=ue()},m(r,f){i.m(r,f),v(r,t,f)},p(r,f){s===(s=l(r,f))&&i?i.p(r,f):(i.d(1),i=s(r),i&&(i.c(),i.m(t.parentNode,t)))},d(r){r&&u(t),i.d(r)}}}function Rl(n){var B;let e,t,l,s,i,r,f,o,a,c,_=(n[2]||n[1])+"",d,g,b,N,E,I=n[0].api_name+"",y,D,S,C,P;l=new ge({props:{code:(B=n[8])==null?void 0:B.innerText}});let T=fe(n[14]),V=[];for(let q=0;q<T.length;q+=1)V[q]=nt(xe(n,T,q));let j=n[4]!==null&&st(n),F=fe(n[3]),z=[];for(let q=0;q<F.length;q+=1)z[q]=rt(Ke(n,F,q));return{c(){e=k("code"),t=k("div"),re(l.$$.fragment),s=W(),i=k("div"),r=k("pre"),f=h(`import { Client } from "@gradio/client";
`);for(let q=0;q<V.length;q+=1)V[q].c();o=h(`
const client = await Client.connect(`),a=k("span"),c=h('"'),d=h(_),g=h('"'),j&&j.c(),b=h(`);
const result = await client.predict(`),N=k("span"),E=h('"/'),y=h(I),D=h('"'),S=h(", { ");for(let q=0;q<z.length;q+=1)z[q].c();C=h(`
});

console.log(result.data);
`),this.h()},l(q){e=w(q,"CODE",{class:!0});var H=O(e);t=w(H,"DIV",{class:!0});var L=O(t);ie(l.$$.fragment,L),L.forEach(u),s=Y(H),i=w(H,"DIV",{});var Z=O(i);r=w(Z,"PRE",{class:!0});var R=O(r);f=m(R,`import { Client } from "@gradio/client";
`);for(let le=0;le<V.length;le+=1)V[le].l(R);o=m(R,`
const client = await Client.connect(`),a=w(R,"SPAN",{class:!0});var M=O(a);c=m(M,'"'),d=m(M,_),g=m(M,'"'),M.forEach(u),j&&j.l(R),b=m(R,`);
const result = await client.predict(`),N=w(R,"SPAN",{class:!0});var J=O(N);E=m(J,'"/'),y=m(J,I),D=m(J,'"'),J.forEach(u),S=m(R,", { ");for(let le=0;le<z.length;le+=1)z[le].l(R);C=m(R,`
});

console.log(result.data);
`),R.forEach(u),Z.forEach(u),H.forEach(u),this.h()},h(){$(t,"class","copy svelte-114qcyq"),$(a,"class","token string svelte-114qcyq"),$(N,"class","api-name svelte-114qcyq"),$(r,"class","svelte-114qcyq"),$(e,"class","svelte-114qcyq")},m(q,H){v(q,e,H),p(e,t),ae(l,t,null),p(e,s),p(e,i),p(i,r),p(r,f);for(let L=0;L<V.length;L+=1)V[L]&&V[L].m(r,null);p(r,o),p(r,a),p(a,c),p(a,d),p(a,g),j&&j.m(r,null),p(r,b),p(r,N),p(N,E),p(N,y),p(N,D),p(r,S);for(let L=0;L<z.length;L+=1)z[L]&&z[L].m(r,null);p(r,C),n[17](i),P=!0},p(q,H){var Z;const L={};if(H[0]&256&&(L.code=(Z=q[8])==null?void 0:Z.innerText),l.$set(L),H[0]&16384){T=fe(q[14]);let R;for(R=0;R<T.length;R+=1){const M=xe(q,T,R);V[R]?V[R].p(M,H):(V[R]=nt(M),V[R].c(),V[R].m(r,o))}for(;R<V.length;R+=1)V[R].d(1);V.length=T.length}if((!P||H[0]&6)&&_!==(_=(q[2]||q[1])+"")&&X(d,_),q[4]!==null?j?j.p(q,H):(j=st(q),j.c(),j.m(r,b)):j&&(j.d(1),j=null),(!P||H[0]&1)&&I!==(I=q[0].api_name+"")&&X(y,I),H[0]&8200){F=fe(q[3]);let R;for(R=0;R<F.length;R+=1){const M=Ke(q,F,R);z[R]?z[R].p(M,H):(z[R]=rt(M),z[R].c(),z[R].m(r,C))}for(;R<z.length;R+=1)z[R].d(1);z.length=F.length}},i(q){P||(G(l.$$.fragment,q),P=!0)},o(q){Q(l.$$.fragment,q),P=!1},d(q){q&&u(e),oe(l),ve(V,q),j&&j.d(),ve(z,q),n[17](null)}}}function Ml(n){let e;return{c(){e=h(", handle_file")},l(t){e=m(t,", handle_file")},m(t,l){v(t,e,l)},d(t){t&&u(e)}}}function it(n){let e,t,l;return{c(){e=h(', auth=("'),t=h(n[4]),l=h('", **password**)')},l(s){e=m(s,', auth=("'),t=m(s,n[4]),l=m(s,'", **password**)')},m(s,i){v(s,e,i),v(s,t,i),v(s,l,i)},p(s,i){i[0]&16&&X(t,s[4])},d(s){s&&(u(e),u(t),u(l))}}}function at(n){let e,t=n[22]?n[22]+"=":"",l,s,i=be(n[23]?n[24]:n[21],n[20].type,"py")+"",r,f;return{c(){e=h(`
		`),l=h(t),s=k("span"),r=h(i),f=h(",")},l(o){e=m(o,`
		`),l=m(o,t),s=w(o,"SPAN",{});var a=O(s);r=m(a,i),a.forEach(u),f=m(o,",")},m(o,a){v(o,e,a),v(o,l,a),v(o,s,a),p(s,r),v(o,f,a)},p(o,a){a[0]&8&&t!==(t=o[22]?o[22]+"=":"")&&X(l,t),a[0]&8&&i!==(i=be(o[23]?o[24]:o[21],o[20].type,"py")+"")&&X(r,i)},d(o){o&&(u(e),u(l),u(s),u(f))}}}function Hl(n){var ce;let e,t,l,s,i,r,f,o="from",a,c,_="import",d,g,b,N,E=(n[2]||n[1])+"",I,y,D,S,C="predict",P,T,V,j,F=n[0].api_name+"",z,B,q,H,L="print",Z,R;l=new ge({props:{code:(ce=n[7])==null?void 0:ce.innerText}});let M=n[12]&&Ml(),J=n[4]!==null&&it(n),le=fe(n[3]),ee=[];for(let U=0;U<le.length;U+=1)ee[U]=at(et(n,le,U));return{c(){e=k("code"),t=k("div"),re(l.$$.fragment),s=W(),i=k("div"),r=k("pre"),f=k("span"),f.textContent=o,a=h(" gradio_client "),c=k("span"),c.textContent=_,d=h(" Client"),M&&M.c(),g=h(`

client = Client(`),b=k("span"),N=h('"'),I=h(E),y=h('"'),J&&J.c(),D=h(`)
result = client.`),S=k("span"),S.textContent=C,P=h("(");for(let U=0;U<ee.length;U+=1)ee[U].c();T=h(`
		api_name=`),V=k("span"),j=h('"/'),z=h(F),B=h('"'),q=h(`
)
`),H=k("span"),H.textContent=L,Z=h("(result)"),this.h()},l(U){e=w(U,"CODE",{class:!0});var ne=O(e);t=w(ne,"DIV",{class:!0});var A=O(t);ie(l.$$.fragment,A),A.forEach(u),s=Y(ne),i=w(ne,"DIV",{});var K=O(i);r=w(K,"PRE",{class:!0});var x=O(r);f=w(x,"SPAN",{class:!0,"data-svelte-h":!0}),te(f)!=="svelte-18n0cfl"&&(f.textContent=o),a=m(x," gradio_client "),c=w(x,"SPAN",{class:!0,"data-svelte-h":!0}),te(c)!=="svelte-18nlj6v"&&(c.textContent=_),d=m(x," Client"),M&&M.l(x),g=m(x,`

client = Client(`),b=w(x,"SPAN",{class:!0});var se=O(b);N=m(se,'"'),I=m(se,E),y=m(se,'"'),se.forEach(u),J&&J.l(x),D=m(x,`)
result = client.`),S=w(x,"SPAN",{class:!0,"data-svelte-h":!0}),te(S)!=="svelte-1qlwf3g"&&(S.textContent=C),P=m(x,"(");for(let Se=0;Se<ee.length;Se+=1)ee[Se].l(x);T=m(x,`
		api_name=`),V=w(x,"SPAN",{class:!0});var me=O(V);j=m(me,'"/'),z=m(me,F),B=m(me,'"'),me.forEach(u),q=m(x,`
)
`),H=w(x,"SPAN",{class:!0,"data-svelte-h":!0}),te(H)!=="svelte-g689qk"&&(H.textContent=L),Z=m(x,"(result)"),x.forEach(u),K.forEach(u),ne.forEach(u),this.h()},h(){$(t,"class","copy svelte-114qcyq"),$(f,"class","highlight"),$(c,"class","highlight"),$(b,"class","token string svelte-114qcyq"),$(S,"class","highlight"),$(V,"class","api-name svelte-114qcyq"),$(H,"class","highlight"),$(r,"class","svelte-114qcyq"),$(e,"class","svelte-114qcyq")},m(U,ne){v(U,e,ne),p(e,t),ae(l,t,null),p(e,s),p(e,i),p(i,r),p(r,f),p(r,a),p(r,c),p(r,d),M&&M.m(r,null),p(r,g),p(r,b),p(b,N),p(b,I),p(b,y),J&&J.m(r,null),p(r,D),p(r,S),p(r,P);for(let A=0;A<ee.length;A+=1)ee[A]&&ee[A].m(r,null);p(r,T),p(r,V),p(V,j),p(V,z),p(V,B),p(r,q),p(r,H),p(r,Z),n[16](i),R=!0},p(U,ne){var K;const A={};if(ne[0]&128&&(A.code=(K=U[7])==null?void 0:K.innerText),l.$set(A),(!R||ne[0]&6)&&E!==(E=(U[2]||U[1])+"")&&X(I,E),U[4]!==null?J?J.p(U,ne):(J=it(U),J.c(),J.m(r,D)):J&&(J.d(1),J=null),ne[0]&8){le=fe(U[3]);let x;for(x=0;x<le.length;x+=1){const se=et(U,le,x);ee[x]?ee[x].p(se,ne):(ee[x]=at(se),ee[x].c(),ee[x].m(r,T))}for(;x<ee.length;x+=1)ee[x].d(1);ee.length=le.length}(!R||ne[0]&1)&&F!==(F=U[0].api_name+"")&&X(z,F)},i(U){R||(G(l.$$.fragment,U),R=!0)},o(U){Q(l.$$.fragment,U),R=!1},d(U){U&&u(e),oe(l),M&&M.d(),J&&J.d(),ve(ee,U),n[16](null)}}}function Ll(n){let e,t,l,s,i,r;t=new Dl({props:{api_name:n[0].api_name,description:n[6]}});const f=[Ol,jl,Al],o=[];function a(c,_){return c[5]==="python"?0:c[5]==="javascript"?1:c[5]==="bash"?2:-1}return~(s=a(n))&&(i=o[s]=f[s](n)),{c(){e=k("div"),re(t.$$.fragment),l=W(),i&&i.c(),this.h()},l(c){e=w(c,"DIV",{class:!0});var _=O(e);ie(t.$$.fragment,_),l=Y(_),i&&i.l(_),_.forEach(u),this.h()},h(){$(e,"class","container svelte-114qcyq")},m(c,_){v(c,e,_),ae(t,e,null),p(e,l),~s&&o[s].m(e,null),r=!0},p(c,_){const d={};_[0]&1&&(d.api_name=c[0].api_name),_[0]&64&&(d.description=c[6]),t.$set(d);let g=s;s=a(c),s===g?~s&&o[s].p(c,_):(i&&(pe(),Q(o[g],1,1,()=>{o[g]=null}),de()),~s?(i=o[s],i?i.p(c,_):(i=o[s]=f[s](c),i.c()),G(i,1),i.m(e,null)):i=null)},i(c){r||(G(t.$$.fragment,c),G(i),r=!0)},o(c){Q(t.$$.fragment,c),Q(i),r=!1},d(c){c&&u(e),oe(t),~s&&o[s].d()}}}function Ul(n,e,t){let l,s,{dependency:i}=e,{root:r}=e,{api_prefix:f}=e,{space_id:o}=e,{endpoint_parameters:a}=e,{username:c}=e,{current_language:_}=e,{api_description:d=null}=e,g,b,N,E=a.some(P=>Yt(P.example_input)),I=["Audio","File","Image","Video"],y=a.filter(P=>I.includes(P.component));function D(P){$e[P?"unshift":"push"](()=>{g=P,t(7,g)})}function S(P){$e[P?"unshift":"push"](()=>{b=P,t(8,b)})}function C(P){$e[P?"unshift":"push"](()=>{N=P,t(9,N)})}return n.$$set=P=>{"dependency"in P&&t(0,i=P.dependency),"root"in P&&t(1,r=P.root),"api_prefix"in P&&t(15,f=P.api_prefix),"space_id"in P&&t(2,o=P.space_id),"endpoint_parameters"in P&&t(3,a=P.endpoint_parameters),"username"in P&&t(4,c=P.username),"current_language"in P&&t(5,_=P.current_language),"api_description"in P&&t(6,d=P.api_description)},n.$$.update=()=>{n.$$.dirty[0]&32768&&t(11,l=f||"/"),n.$$.dirty[0]&2&&t(10,s=r.replace(/\/$/,""))},[i,r,o,a,c,_,d,g,b,N,s,l,E,I,y,f,D,S,C]}class Fl extends ke{constructor(e){super(),we(this,e,Ul,Ll,ye,{dependency:0,root:1,api_prefix:15,space_id:2,endpoint_parameters:3,username:4,current_language:5,api_description:6},null,[-1,-1])}}function ot(n,e,t){const l=n.slice();return l[20]=e[t].call,l[21]=e[t].api_name,l}function ct(n,e,t){const l=n.slice();return l[20]=e[t].call,l[21]=e[t].api_name,l}function ft(n,e,t){const l=n.slice();return l[20]=e[t].call,l[21]=e[t].api_name,l}function Bl(n){var a;let e,t,l,s,i,r;l=new ge({props:{code:(a=n[6])==null?void 0:a.innerText}});let f=fe(n[9]),o=[];for(let c=0;c<f.length;c+=1)o[c]=ut(ot(n,f,c));return{c(){e=k("code"),t=k("div"),re(l.$$.fragment),s=W(),i=k("div");for(let c=0;c<o.length;c+=1)o[c].c();this.h()},l(c){e=w(c,"CODE",{class:!0});var _=O(e);t=w(_,"DIV",{class:!0});var d=O(t);ie(l.$$.fragment,d),d.forEach(u),s=Y(_),i=w(_,"DIV",{});var g=O(i);for(let b=0;b<o.length;b+=1)o[b].l(g);g.forEach(u),_.forEach(u),this.h()},h(){$(t,"class","copy svelte-j71ub0"),$(e,"class","svelte-j71ub0")},m(c,_){v(c,e,_),p(e,t),ae(l,t,null),p(e,s),p(e,i);for(let d=0;d<o.length;d+=1)o[d]&&o[d].m(i,null);n[16](i),r=!0},p(c,_){var g;const d={};if(_&64&&(d.code=(g=c[6])==null?void 0:g.innerText),l.$set(d),_&513){f=fe(c[9]);let b;for(b=0;b<f.length;b+=1){const N=ot(c,f,b);o[b]?o[b].p(N,_):(o[b]=ut(N),o[b].c(),o[b].m(i,null))}for(;b<o.length;b+=1)o[b].d(1);o.length=f.length}},i(c){r||(G(l.$$.fragment,c),r=!0)},o(c){Q(l.$$.fragment,c),r=!1},d(c){c&&u(e),oe(l),ve(o,c),n[16](null)}}}function Jl(n){var I;let e,t,l,s,i,r,f,o,a,c,_,d,g;l=new ge({props:{code:(I=n[5])==null?void 0:I.innerText}});let b=n[2]!==null&&_t(n),N=fe(n[8]),E=[];for(let y=0;y<N.length;y+=1)E[y]=dt(ct(n,N,y));return{c(){e=k("code"),t=k("div"),re(l.$$.fragment),s=W(),i=k("div"),r=k("pre"),f=h(`import { Client } from "@gradio/client";

const app = await Client.connect(`),o=k("span"),a=h('"'),c=h(n[0]),_=h('"'),b&&b.c(),d=h(`);
					`);for(let y=0;y<E.length;y+=1)E[y].c();this.h()},l(y){e=w(y,"CODE",{class:!0});var D=O(e);t=w(D,"DIV",{class:!0});var S=O(t);ie(l.$$.fragment,S),S.forEach(u),s=Y(D),i=w(D,"DIV",{});var C=O(i);r=w(C,"PRE",{class:!0});var P=O(r);f=m(P,`import { Client } from "@gradio/client";

const app = await Client.connect(`),o=w(P,"SPAN",{class:!0});var T=O(o);a=m(T,'"'),c=m(T,n[0]),_=m(T,'"'),T.forEach(u),b&&b.l(P),d=m(P,`);
					`);for(let V=0;V<E.length;V+=1)E[V].l(P);P.forEach(u),C.forEach(u),D.forEach(u),this.h()},h(){$(t,"class","copy svelte-j71ub0"),$(o,"class","token string svelte-j71ub0"),$(r,"class","svelte-j71ub0"),$(e,"class","svelte-j71ub0")},m(y,D){v(y,e,D),p(e,t),ae(l,t,null),p(e,s),p(e,i),p(i,r),p(r,f),p(r,o),p(o,a),p(o,c),p(o,_),b&&b.m(r,null),p(r,d);for(let S=0;S<E.length;S+=1)E[S]&&E[S].m(r,null);n[15](i),g=!0},p(y,D){var C;const S={};if(D&32&&(S.code=(C=y[5])==null?void 0:C.innerText),l.$set(S),(!g||D&1)&&X(c,y[0]),y[2]!==null?b?b.p(y,D):(b=_t(y),b.c(),b.m(r,d)):b&&(b.d(1),b=null),D&256){N=fe(y[8]);let P;for(P=0;P<N.length;P+=1){const T=ct(y,N,P);E[P]?E[P].p(T,D):(E[P]=dt(T),E[P].c(),E[P].m(r,null))}for(;P<E.length;P+=1)E[P].d(1);E.length=N.length}},i(y){g||(G(l.$$.fragment,y),g=!0)},o(y){Q(l.$$.fragment,y),g=!1},d(y){y&&u(e),oe(l),b&&b.d(),ve(E,y),n[15](null)}}}function Gl(n){let e,t,l,s,i,r,f,o="from",a,c,_="import",d,g,b,N,E,I,y;l=new ge({props:{code:n[4]}});let D=n[2]!==null&&ht(n),S=fe(n[7]),C=[];for(let P=0;P<S.length;P+=1)C[P]=mt(ft(n,S,P));return{c(){e=k("code"),t=k("div"),re(l.$$.fragment),s=W(),i=k("div"),r=k("pre"),f=k("span"),f.textContent=o,a=h(" gradio_client "),c=k("span"),c.textContent=_,d=h(` Client, file

client = Client(`),g=k("span"),b=h('"'),N=h(n[0]),E=h('"'),D&&D.c(),I=h(`)
`);for(let P=0;P<C.length;P+=1)C[P].c();this.h()},l(P){e=w(P,"CODE",{class:!0});var T=O(e);t=w(T,"DIV",{class:!0});var V=O(t);ie(l.$$.fragment,V),V.forEach(u),s=Y(T),i=w(T,"DIV",{});var j=O(i);r=w(j,"PRE",{class:!0});var F=O(r);f=w(F,"SPAN",{class:!0,"data-svelte-h":!0}),te(f)!=="svelte-18n0cfl"&&(f.textContent=o),a=m(F," gradio_client "),c=w(F,"SPAN",{class:!0,"data-svelte-h":!0}),te(c)!=="svelte-18nlj6v"&&(c.textContent=_),d=m(F,` Client, file

client = Client(`),g=w(F,"SPAN",{class:!0});var z=O(g);b=m(z,'"'),N=m(z,n[0]),E=m(z,'"'),z.forEach(u),D&&D.l(F),I=m(F,`)
`);for(let B=0;B<C.length;B+=1)C[B].l(F);F.forEach(u),j.forEach(u),T.forEach(u),this.h()},h(){$(t,"class","copy svelte-j71ub0"),$(f,"class","highlight"),$(c,"class","highlight"),$(g,"class","token string svelte-j71ub0"),$(r,"class","svelte-j71ub0"),$(e,"class","svelte-j71ub0")},m(P,T){v(P,e,T),p(e,t),ae(l,t,null),p(e,s),p(e,i),p(i,r),p(r,f),p(r,a),p(r,c),p(r,d),p(r,g),p(g,b),p(g,N),p(g,E),D&&D.m(r,null),p(r,I);for(let V=0;V<C.length;V+=1)C[V]&&C[V].m(r,null);n[14](i),y=!0},p(P,T){const V={};if(T&16&&(V.code=P[4]),l.$set(V),(!y||T&1)&&X(N,P[0]),P[2]!==null?D?D.p(P,T):(D=ht(P),D.c(),D.m(r,I)):D&&(D.d(1),D=null),T&128){S=fe(P[7]);let j;for(j=0;j<S.length;j+=1){const F=ft(P,S,j);C[j]?C[j].p(F,T):(C[j]=mt(F),C[j].c(),C[j].m(r,null))}for(;j<C.length;j+=1)C[j].d(1);C.length=S.length}},i(P){y||(G(l.$$.fragment,P),y=!0)},o(P){Q(l.$$.fragment,P),y=!1},d(P){P&&u(e),oe(l),D&&D.d(),ve(C,P),n[14](null)}}}function ut(n){let e,t,l,s,i=n[21]+"",r,f,o="{",a,c,_=n[20]+"",d,g,b="}",N,E,I="{",y,D,S="}",C,P,T,V,j=n[21]+"",F,z,B,q;return{c(){e=k("pre"),t=h("curl -X POST "),l=h(n[0]),s=h("call/"),r=h(i),f=h(` -s -H "Content-Type: application/json" -d '`),a=h(o),c=h(` 
	"data": [`),d=h(_),g=h("]"),N=h(b),E=h(`' \\
  | awk -F'"' '`),y=h(I),D=h(" print $4"),C=h(S),P=h(`' \\
  | read EVENT_ID; curl -N `),T=h(n[0]),V=h("call/"),F=h(j),z=h("/$EVENT_ID"),B=W(),q=k("br"),this.h()},l(H){e=w(H,"PRE",{class:!0});var L=O(e);t=m(L,"curl -X POST "),l=m(L,n[0]),s=m(L,"call/"),r=m(L,i),f=m(L,` -s -H "Content-Type: application/json" -d '`),a=m(L,o),c=m(L,` 
	"data": [`),d=m(L,_),g=m(L,"]"),N=m(L,b),E=m(L,`' \\
  | awk -F'"' '`),y=m(L,I),D=m(L," print $4"),C=m(L,S),P=m(L,`' \\
  | read EVENT_ID; curl -N `),T=m(L,n[0]),V=m(L,"call/"),F=m(L,j),z=m(L,"/$EVENT_ID"),L.forEach(u),B=Y(H),q=w(H,"BR",{}),this.h()},h(){$(e,"class","svelte-j71ub0")},m(H,L){v(H,e,L),p(e,t),p(e,l),p(e,s),p(e,r),p(e,f),p(e,a),p(e,c),p(e,d),p(e,g),p(e,N),p(e,E),p(e,y),p(e,D),p(e,C),p(e,P),p(e,T),p(e,V),p(e,F),p(e,z),v(H,B,L),v(H,q,L)},p(H,L){L&1&&X(l,H[0]),L&512&&i!==(i=H[21]+"")&&X(r,i),L&512&&_!==(_=H[20]+"")&&X(d,_),L&1&&X(T,H[0]),L&512&&j!==(j=H[21]+"")&&X(F,j)},d(H){H&&(u(e),u(B),u(q))}}}function _t(n){let e,t,l;return{c(){e=h(', {auth: ["'),t=h(n[2]),l=h('", **password**]}')},l(s){e=m(s,', {auth: ["'),t=m(s,n[2]),l=m(s,'", **password**]}')},m(s,i){v(s,e,i),v(s,t,i),v(s,l,i)},p(s,i){i&4&&X(t,s[2])},d(s){s&&(u(e),u(t),u(l))}}}function pt(n){let e,t=n[20]+"",l;return{c(){e=h(", "),l=h(t)},l(s){e=m(s,", "),l=m(s,t)},m(s,i){v(s,e,i),v(s,l,i)},p(s,i){i&256&&t!==(t=s[20]+"")&&X(l,t)},d(s){s&&(u(e),u(l))}}}function dt(n){let e,t,l,s=n[21]+"",i,r,f,o=n[20]&&pt(n);return{c(){e=h(`
await client.predict(`),t=k("span"),l=h(`
  "/`),i=h(s),r=h('"'),o&&o.c(),f=h(`);
						`),this.h()},l(a){e=m(a,`
await client.predict(`),t=w(a,"SPAN",{class:!0});var c=O(t);l=m(c,`
  "/`),i=m(c,s),r=m(c,'"'),c.forEach(u),o&&o.l(a),f=m(a,`);
						`),this.h()},h(){$(t,"class","api-name svelte-j71ub0")},m(a,c){v(a,e,c),v(a,t,c),p(t,l),p(t,i),p(t,r),o&&o.m(a,c),v(a,f,c)},p(a,c){c&256&&s!==(s=a[21]+"")&&X(i,s),a[20]?o?o.p(a,c):(o=pt(a),o.c(),o.m(f.parentNode,f)):o&&(o.d(1),o=null)},d(a){a&&(u(e),u(t),u(f)),o&&o.d(a)}}}function ht(n){let e,t,l;return{c(){e=h(', auth=("'),t=h(n[2]),l=h('", **password**)')},l(s){e=m(s,', auth=("'),t=m(s,n[2]),l=m(s,'", **password**)')},m(s,i){v(s,e,i),v(s,t,i),v(s,l,i)},p(s,i){i&4&&X(t,s[2])},d(s){s&&(u(e),u(t),u(l))}}}function mt(n){let e,t,l,s=n[20]+"",i,r,f,o,a=n[21]+"",c,_,d;return{c(){e=h(`
client.`),t=k("span"),l=h(`predict(
`),i=h(s),r=h("  api_name="),f=k("span"),o=h('"/'),c=h(a),_=h('"'),d=h(`
)
`),this.h()},l(g){e=m(g,`
client.`),t=w(g,"SPAN",{class:!0});var b=O(t);l=m(b,`predict(
`),i=m(b,s),r=m(b,"  api_name="),f=w(b,"SPAN",{class:!0});var N=O(f);o=m(N,'"/'),c=m(N,a),_=m(N,'"'),N.forEach(u),d=m(b,`
)
`),b.forEach(u),this.h()},h(){$(f,"class","api-name svelte-j71ub0"),$(t,"class","highlight")},m(g,b){v(g,e,b),v(g,t,b),p(t,l),p(t,i),p(t,r),p(t,f),p(f,o),p(f,c),p(f,_),p(t,d)},p(g,b){b&128&&s!==(s=g[20]+"")&&X(i,s),b&128&&a!==(a=g[21]+"")&&X(c,a)},d(g){g&&(u(e),u(t))}}}function Wl(n){let e,t,l,s;const i=[Gl,Jl,Bl],r=[];function f(o,a){return o[1]==="python"?0:o[1]==="javascript"?1:o[1]==="bash"?2:-1}return~(e=f(n))&&(t=r[e]=i[e](n)),{c(){t&&t.c(),l=ue()},l(o){t&&t.l(o),l=ue()},m(o,a){~e&&r[e].m(o,a),v(o,l,a),s=!0},p(o,a){let c=e;e=f(o),e===c?~e&&r[e].p(o,a):(t&&(pe(),Q(r[c],1,1,()=>{r[c]=null}),de()),~e?(t=r[e],t?t.p(o,a):(t=r[e]=i[e](o),t.c()),G(t,1),t.m(l.parentNode,l)):t=null)},i(o){s||(G(t),s=!0)},o(o){Q(t),s=!1},d(o){o&&u(l),~e&&r[e].d(o)}}}function Yl(n){let e,t,l;return t=new Ce({props:{border_mode:"focus",$$slots:{default:[Wl]},$$scope:{ctx:n}}}),{c(){e=k("div"),re(t.$$.fragment),this.h()},l(s){e=w(s,"DIV",{class:!0});var i=O(e);ie(t.$$.fragment,i),i.forEach(u),this.h()},h(){$(e,"class","container svelte-j71ub0")},m(s,i){v(s,e,i),ae(t,e,null),l=!0},p(s,[i]){const r={};i&268436479&&(r.$$scope={dirty:i,ctx:s}),t.$set(r)},i(s){l||(G(t.$$.fragment,s),l=!0)},o(s){Q(t.$$.fragment,s),l=!1},d(s){s&&u(e),oe(t)}}}function Zl(n,e,t){let{dependencies:l}=e,{short_root:s}=e,{root:i}=e,{api_prefix:r=""}=e,{current_language:f}=e,{username:o}=e,a,c,_,d,{api_calls:g=[]}=e;async function b(){return await(await fetch(i.replace(/\/$/,"")+r+"/info/?all_endpoints=true")).json()}let N,E=[],I=[],y=[];function D(T,V){const j=`/${l[T.fn_index].api_name}`,z=T.data.filter(B=>typeof B<"u").map((B,q)=>{if(N[j]){const H=N[j].parameters[q];if(!H)return;const L=H.parameter_name,Z=H.python_type.type;if(V==="py")return`  ${L}=${be(B,Z,"py")}`;if(V==="js")return`    ${L}: ${be(B,Z,"js")}`;if(V==="bash")return`    ${be(B,Z,"bash")}`}return`  ${be(B,void 0,V)}`}).filter(B=>typeof B<"u").join(`,
`);if(z){if(V==="py")return`${z},
`;if(V==="js")return`{
${z},
}`;if(V==="bash")return`
${z}
`}return V==="py"?"":`
`}Jt(async()=>{N=(await b()).named_endpoints;let V=g.map(B=>D(B,"py")),j=g.map(B=>D(B,"js")),F=g.map(B=>D(B,"bash")),z=g.map(B=>l[B.fn_index].api_name||"");t(7,E=V.map((B,q)=>({call:B,api_name:z[q]}))),t(8,I=j.map((B,q)=>({call:B,api_name:z[q]}))),t(9,y=F.map((B,q)=>({call:B,api_name:z[q]}))),await Zt(),t(4,c=a.innerText)});function S(T){$e[T?"unshift":"push"](()=>{a=T,t(3,a)})}function C(T){$e[T?"unshift":"push"](()=>{_=T,t(5,_)})}function P(T){$e[T?"unshift":"push"](()=>{d=T,t(6,d)})}return n.$$set=T=>{"dependencies"in T&&t(10,l=T.dependencies),"short_root"in T&&t(0,s=T.short_root),"root"in T&&t(11,i=T.root),"api_prefix"in T&&t(12,r=T.api_prefix),"current_language"in T&&t(1,f=T.current_language),"username"in T&&t(2,o=T.username),"api_calls"in T&&t(13,g=T.api_calls)},[s,f,o,a,c,_,d,E,I,y,l,i,r,g,S,C,P]}class Ql extends ke{constructor(e){super(),we(this,e,Zl,Yl,ye,{dependencies:10,short_root:0,root:11,api_prefix:12,current_language:1,username:2,api_calls:13})}}const Xl="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3cpath%20d='M15.84.5a16.4,16.4,0,0,0-3.57.32C9.1,1.39,8.53,2.53,8.53,4.64V7.48H16v1H5.77a4.73,4.73,0,0,0-4.7,3.74,14.82,14.82,0,0,0,0,7.54c.57,2.28,1.86,3.82,4,3.82h2.6V20.14a4.73,4.73,0,0,1,4.63-4.63h7.38a3.72,3.72,0,0,0,3.73-3.73V4.64A4.16,4.16,0,0,0,19.65.82,20.49,20.49,0,0,0,15.84.5ZM11.78,2.77a1.39,1.39,0,0,1,1.38,1.46,1.37,1.37,0,0,1-1.38,1.38A1.42,1.42,0,0,1,10.4,4.23,1.44,1.44,0,0,1,11.78,2.77Z'%20fill='%235a9fd4'%20%3e%3c/path%3e%3cpath%20d='M16.16,31.5a16.4,16.4,0,0,0,3.57-.32c3.17-.57,3.74-1.71,3.74-3.82V24.52H16v-1H26.23a4.73,4.73,0,0,0,4.7-3.74,14.82,14.82,0,0,0,0-7.54c-.57-2.28-1.86-3.82-4-3.82h-2.6v3.41a4.73,4.73,0,0,1-4.63,4.63H12.35a3.72,3.72,0,0,0-3.73,3.73v7.14a4.16,4.16,0,0,0,3.73,3.82A20.49,20.49,0,0,0,16.16,31.5Zm4.06-2.27a1.39,1.39,0,0,1-1.38-1.46,1.37,1.37,0,0,1,1.38-1.38,1.42,1.42,0,0,1,1.38,1.38A1.44,1.44,0,0,1,20.22,29.23Z'%20fill='%23ffd43b'%20%3e%3c/path%3e%3c/svg%3e",Kl="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3crect%20width='32'%20height='32'%20fill='%23f7df1e'%3e%3c/rect%3e%3cpath%20d='M21.5,25a3.27,3.27,0,0,0,3,1.83c1.25,0,2-.63,2-1.49,0-1-.81-1.39-2.19-2L23.56,23C21.39,22.1,20,20.94,20,18.49c0-2.25,1.72-4,4.41-4a4.44,4.44,0,0,1,4.27,2.41l-2.34,1.5a2,2,0,0,0-1.93-1.29,1.31,1.31,0,0,0-1.44,1.29c0,.9.56,1.27,1.85,1.83l.75.32c2.55,1.1,4,2.21,4,4.72,0,2.71-2.12,4.19-5,4.19a5.78,5.78,0,0,1-5.48-3.07Zm-10.63.26c.48.84.91,1.55,1.94,1.55s1.61-.39,1.61-1.89V14.69h3V25c0,3.11-1.83,4.53-4.49,4.53a4.66,4.66,0,0,1-4.51-2.75Z'%20%3e%3c/path%3e%3c/svg%3e",xl="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20version='1.1'%20id='Layer_1'%20x='0px'%20y='0px'%20viewBox='0%200%20150%20150'%20style='enable-background:new%200%200%20150%20150;%20background-color:%20%2372a824;'%20xml:space='preserve'%3e%3cscript%20xmlns=''/%3e%3cstyle%20type='text/css'%3e%20.st0{fill:%23FFFFFF;}%20%3c/style%3e%3cg%3e%3cpath%20class='st0'%20d='M118.9,40.3L81.7,18.2c-2.2-1.3-4.7-2-7.2-2s-5,0.7-7.2,2L30.1,40.3c-4.4,2.6-7.2,7.5-7.2,12.8v44.2%20c0,5.3,2.7,10.1,7.2,12.8l37.2,22.1c2.2,1.3,4.7,2,7.2,2c2.5,0,5-0.7,7.2-2l37.2-22.1c4.4-2.6,7.2-7.5,7.2-12.8V53%20C126.1,47.8,123.4,42.9,118.9,40.3z%20M90.1,109.3l0.1,3.2c0,0.4-0.2,0.8-0.5,1l-1.9,1.1c-0.3,0.2-0.5,0-0.6-0.4l0-3.1%20c-1.6,0.7-3.2,0.8-4.3,0.4c-0.2-0.1-0.3-0.4-0.2-0.7l0.7-2.9c0.1-0.2,0.2-0.5,0.3-0.6c0.1-0.1,0.1-0.1,0.2-0.1%20c0.1-0.1,0.2-0.1,0.3,0c1.1,0.4,2.6,0.2,3.9-0.5c1.8-0.9,2.9-2.7,2.9-4.5c0-1.6-0.9-2.3-3-2.3c-2.7,0-5.2-0.5-5.3-4.5%20c0-3.3,1.7-6.7,4.4-8.8l0-3.2c0-0.4,0.2-0.8,0.5-1l1.8-1.2c0.3-0.2,0.5,0,0.6,0.4l0,3.2c1.3-0.5,2.5-0.7,3.6-0.4%20c0.2,0.1,0.3,0.4,0.2,0.7l-0.7,2.8c-0.1,0.2-0.2,0.4-0.3,0.6c-0.1,0.1-0.1,0.1-0.2,0.1c-0.1,0-0.2,0.1-0.3,0%20c-0.5-0.1-1.6-0.4-3.4,0.6c-1.9,1-2.6,2.6-2.5,3.8c0,1.5,0.8,1.9,3.3,1.9c3.4,0.1,4.9,1.6,5,5C94.7,103.4,92.9,107,90.1,109.3z%20M109.6,103.9c0,0.3,0,0.6-0.3,0.7l-9.4,5.7c-0.2,0.1-0.4,0-0.4-0.3v-2.4c0-0.3,0.2-0.5,0.4-0.6l9.3-5.5c0.2-0.1,0.4,0,0.4,0.3%20V103.9z%20M116.1,49.6L80.9,71.3c-4.4,2.6-7.6,5.4-7.6,10.7v43.4c0,3.2,1.3,5.2,3.2,5.8c-0.6,0.1-1.3,0.2-2,0.2%20c-2.1,0-4.1-0.6-5.9-1.6l-37.2-22.1c-3.6-2.2-5.9-6.2-5.9-10.5V53c0-4.3,2.3-8.4,5.9-10.5l37.2-22.1c1.8-1.1,3.8-1.6,5.9-1.6%20s4.1,0.6,5.9,1.6l37.2,22.1c3.1,1.8,5.1,5,5.7,8.5C122.1,48.4,119.3,47.7,116.1,49.6z'/%3e%3c/g%3e%3c/svg%3e";function vt(n,e,t){const l=n.slice();return l[4]=e[t].label,l[5]=e[t].type,l[6]=e[t].python_type,l[7]=e[t].component,l[8]=e[t].serializer,l[10]=t,l}function en(n){let e;return{c(){e=h("1 element")},l(t){e=m(t,"1 element")},m(t,l){v(t,e,l)},p:_e,d(t){t&&u(e)}}}function tn(n){let e=n[3]=="python"?"tuple":"list",t,l,s=n[1].length+"",i,r;return{c(){t=h(e),l=h(" of "),i=h(s),r=h(`
		elements`)},l(f){t=m(f,e),l=m(f," of "),i=m(f,s),r=m(f,`
		elements`)},m(f,o){v(f,t,o),v(f,l,o),v(f,i,o),v(f,r,o)},p(f,o){o&8&&e!==(e=f[3]=="python"?"tuple":"list")&&X(t,e),o&2&&s!==(s=f[1].length+"")&&X(i,s)},d(f){f&&(u(t),u(l),u(i),u(r))}}}function gt(n){let e,t,l,s;return{c(){e=k("span"),t=h("["),l=h(n[10]),s=h("]"),this.h()},l(i){e=w(i,"SPAN",{class:!0});var r=O(e);t=m(r,"["),l=m(r,n[10]),s=m(r,"]"),r.forEach(u),this.h()},h(){$(e,"class","code svelte-16h224k")},m(i,r){v(i,e,r),p(e,t),p(e,l),p(e,s)},d(i){i&&u(e)}}}function ln(n){let e=n[2][n[10]].type+"",t;return{c(){t=h(e)},l(l){t=m(l,e)},m(l,s){v(l,t,s)},p(l,s){s&4&&e!==(e=l[2][l[10]].type+"")&&X(t,e)},d(l){l&&u(t)}}}function nn(n){let e=n[6].type+"",t;return{c(){t=h(e)},l(l){t=m(l,e)},m(l,s){v(l,t,s)},p(l,s){s&2&&e!==(e=l[6].type+"")&&X(t,e)},d(l){l&&u(t)}}}function bt(n){let e,t,l,s,i,r,f,o,a,c=n[4]+"",_,d,g=n[7]+"",b,N,E,I=n[1].length>1&&gt(n);function y(C,P){return C[3]==="python"?nn:ln}let D=y(n),S=D(n);return{c(){e=k("hr"),t=W(),l=k("div"),s=k("p"),I&&I.c(),i=W(),r=k("span"),S.c(),f=W(),o=k("p"),a=h('The output value that appears in the "'),_=h(c),d=h('" '),b=h(g),N=h(`
				component.`),E=W(),this.h()},l(C){e=w(C,"HR",{class:!0}),t=Y(C),l=w(C,"DIV",{style:!0});var P=O(l);s=w(P,"P",{});var T=O(s);I&&I.l(T),i=Y(T),r=w(T,"SPAN",{class:!0});var V=O(r);S.l(V),V.forEach(u),T.forEach(u),f=Y(P),o=w(P,"P",{class:!0});var j=O(o);a=m(j,'The output value that appears in the "'),_=m(j,c),d=m(j,'" '),b=m(j,g),N=m(j,`
				component.`),j.forEach(u),E=Y(P),P.forEach(u),this.h()},h(){$(e,"class","hr svelte-16h224k"),$(r,"class","code highlight svelte-16h224k"),$(o,"class","desc svelte-16h224k"),he(l,"margin","10px")},m(C,P){v(C,e,P),v(C,t,P),v(C,l,P),p(l,s),I&&I.m(s,null),p(s,i),p(s,r),S.m(r,null),p(l,f),p(l,o),p(o,a),p(o,_),p(o,d),p(o,b),p(o,N),p(l,E)},p(C,P){C[1].length>1?I||(I=gt(C),I.c(),I.m(s,i)):I&&(I.d(1),I=null),D===(D=y(C))&&S?S.p(C,P):(S.d(1),S=D(C),S&&(S.c(),S.m(r,null))),P&2&&c!==(c=C[4]+"")&&X(_,c),P&2&&g!==(g=C[7]+"")&&X(b,g)},d(C){C&&(u(e),u(t),u(l)),I&&I.d(),S.d()}}}function kt(n){let e,t,l;return t=new Wt({props:{margin:!1}}),{c(){e=k("div"),re(t.$$.fragment),this.h()},l(s){e=w(s,"DIV",{class:!0});var i=O(e);ie(t.$$.fragment,i),i.forEach(u),this.h()},h(){$(e,"class","load-wrap")},m(s,i){v(s,e,i),ae(t,e,null),l=!0},i(s){l||(G(t.$$.fragment,s),l=!0)},o(s){Q(t.$$.fragment,s),l=!1},d(s){s&&u(e),oe(t)}}}function sn(n){let e,t,l='<div class="toggle-dot toggle-right svelte-16h224k"></div>',s,i,r,f,o,a;function c(E,I){return E[1].length>1?tn:en}let _=c(n),d=_(n),g=fe(n[1]),b=[];for(let E=0;E<g.length;E+=1)b[E]=bt(vt(n,g,E));let N=n[0]&&kt();return{c(){e=k("h4"),t=k("div"),t.innerHTML=l,s=h(`
	Returns `),d.c(),i=W(),r=k("div");for(let E=0;E<b.length;E+=1)b[E].c();f=W(),N&&N.c(),o=ue(),this.h()},l(E){e=w(E,"H4",{class:!0});var I=O(e);t=w(I,"DIV",{class:!0,"data-svelte-h":!0}),te(t)!=="svelte-1q6qbuq"&&(t.innerHTML=l),s=m(I,`
	Returns `),d.l(I),I.forEach(u),i=Y(E),r=w(E,"DIV",{});var y=O(r);for(let D=0;D<b.length;D+=1)b[D].l(y);y.forEach(u),f=Y(E),N&&N.l(E),o=ue(),this.h()},h(){$(t,"class","toggle-icon svelte-16h224k"),$(e,"class","svelte-16h224k"),Ne(r,"hide",n[0])},m(E,I){v(E,e,I),p(e,t),p(e,s),d.m(e,null),v(E,i,I),v(E,r,I);for(let y=0;y<b.length;y+=1)b[y]&&b[y].m(r,null);v(E,f,I),N&&N.m(E,I),v(E,o,I),a=!0},p(E,[I]){if(_===(_=c(E))&&d?d.p(E,I):(d.d(1),d=_(E),d&&(d.c(),d.m(e,null))),I&14){g=fe(E[1]);let y;for(y=0;y<g.length;y+=1){const D=vt(E,g,y);b[y]?b[y].p(D,I):(b[y]=bt(D),b[y].c(),b[y].m(r,null))}for(;y<b.length;y+=1)b[y].d(1);b.length=g.length}(!a||I&1)&&Ne(r,"hide",E[0]),E[0]?N?I&1&&G(N,1):(N=kt(),N.c(),G(N,1),N.m(o.parentNode,o)):N&&(pe(),Q(N,1,1,()=>{N=null}),de())},i(E){a||(G(N),a=!0)},o(E){Q(N),a=!1},d(E){E&&(u(e),u(i),u(r),u(f),u(o)),d.d(),ve(b,E),N&&N.d(E)}}}function rn(n,e,t){let{is_running:l}=e,{endpoint_returns:s}=e,{js_returns:i}=e,{current_language:r}=e;return n.$$set=f=>{"is_running"in f&&t(0,l=f.is_running),"endpoint_returns"in f&&t(1,s=f.endpoint_returns),"js_returns"in f&&t(2,i=f.js_returns),"current_language"in f&&t(3,r=f.current_language)},[l,s,i,r]}class an extends ke{constructor(e){super(),we(this,e,rn,sn,ye,{is_running:0,endpoint_returns:1,js_returns:2,current_language:3})}}const on=""+new URL("../assets/mcp.DNm9doVd.svg",import.meta.url).href;function wt(n,e,t){const l=n.slice();return l[24]=e[t],l[25]=e,l[26]=t,l}function yt(n,e,t){const l=n.slice();return l[27]=e[t][0],l[28]=e[t][1],l}function Et(n,e,t){const l=n.slice();return l[31]=e[t][0],l[32]=e[t][1],l}function cn(n){let e,t,l=".launch(mcp_server=True)",s,i,r="GRADIO_MCP_SERVER",f,o,a='"True"',c;return{c(){e=h(`This Gradio app can also serve as an MCP server, with an MCP tool
	corresponding to each API endpoint. To enable this, launch this Gradio app
	with `),t=k("code"),t.textContent=l,s=h(` or set the
	`),i=k("code"),i.textContent=r,f=h(`
	env variable to
	`),o=k("code"),o.textContent=a,c=h(".")},l(_){e=m(_,`This Gradio app can also serve as an MCP server, with an MCP tool
	corresponding to each API endpoint. To enable this, launch this Gradio app
	with `),t=w(_,"CODE",{"data-svelte-h":!0}),te(t)!=="svelte-yfuruz"&&(t.textContent=l),s=m(_,` or set the
	`),i=w(_,"CODE",{"data-svelte-h":!0}),te(i)!=="svelte-8yedgr"&&(i.textContent=r),f=m(_,`
	env variable to
	`),o=w(_,"CODE",{"data-svelte-h":!0}),te(o)!=="svelte-s5r5d4"&&(o.textContent=a),c=m(_,".")},m(_,d){v(_,e,d),v(_,t,d),v(_,s,d),v(_,i,d),v(_,f,d),v(_,o,d),v(_,c,d)},p:_e,i:_e,o:_e,d(_){_&&(u(e),u(t),u(s),u(i),u(f),u(o),u(c))}}}function fn(n){let e,t,l,s="Transport:",i,r,f,o,a,c=(n[1].length>0?n[1].length:n[0].length)+"",_,d,g,b,N,E,I,y=" ",D,S,C,P,T,V,j=" ",F,z,B,q,H,L=fe(n[12]),Z=[];for(let A=0;A<L.length;A+=1)Z[A]=Ct(Et(n,L,A));let R=n[6]!=="stdio"&&Pt(n),M=n[1].length>0&&$t(n),J=fe(n[1].length>0?n[1]:n[0]),le=[];for(let A=0;A<J.length;A+=1)le[A]=It(wt(n,J,A));const ee=[mn,hn,dn],ce=[];function U(A,K){return A[6]==="streamable_http"?0:A[6]==="sse"?1:A[6]==="stdio"?2:-1}~(S=U(n))&&(C=ce[S]=ee[S](n));let ne=n[4]&&Dt(n);return{c(){e=k("div"),t=k("div"),l=k("span"),l.textContent=s,i=W();for(let A=0;A<Z.length;A+=1)Z[A].c();r=W(),R&&R.c(),f=W(),o=k("div"),a=k("strong"),_=h(c),d=h(" Available MCP Tools"),g=W(),M&&M.c(),b=W(),N=k("div");for(let A=0;A<le.length;A+=1)le[A].c();E=W(),I=k("p"),I.textContent=y,D=W(),C&&C.c(),P=W(),ne&&ne.c(),T=W(),V=k("p"),V.textContent=j,F=W(),z=k("p"),B=k("a"),q=h("Read more about MCP in the Gradio docs"),this.h()},l(A){e=w(A,"DIV",{class:!0});var K=O(e);t=w(K,"DIV",{class:!0});var x=O(t);l=w(x,"SPAN",{class:!0,"data-svelte-h":!0}),te(l)!=="svelte-1vh3xy2"&&(l.textContent=s),i=Y(x);for(let Pe=0;Pe<Z.length;Pe+=1)Z[Pe].l(x);x.forEach(u),K.forEach(u),r=Y(A),R&&R.l(A),f=Y(A),o=w(A,"DIV",{class:!0});var se=O(o);a=w(se,"STRONG",{});var me=O(a);_=m(me,c),d=m(me," Available MCP Tools"),me.forEach(u),g=Y(se),M&&M.l(se),se.forEach(u),b=Y(A),N=w(A,"DIV",{class:!0});var Se=O(N);for(let Pe=0;Pe<le.length;Pe+=1)le[Pe].l(Se);Se.forEach(u),E=Y(A),I=w(A,"P",{"data-svelte-h":!0}),te(I)!=="svelte-9hmwf2"&&(I.textContent=y),D=Y(A),C&&C.l(A),P=Y(A),ne&&ne.l(A),T=Y(A),V=w(A,"P",{"data-svelte-h":!0}),te(V)!=="svelte-9hmwf2"&&(V.textContent=j),F=Y(A),z=w(A,"P",{});var Le=O(z);B=w(Le,"A",{href:!0,target:!0,class:!0});var Ue=O(B);q=m(Ue,"Read more about MCP in the Gradio docs"),Ue.forEach(u),Le.forEach(u),this.h()},h(){$(l,"class","transport-label svelte-1czpo3w"),$(t,"class","snippets svelte-1czpo3w"),$(e,"class","transport-selection svelte-1czpo3w"),$(o,"class","tool-selection svelte-1czpo3w"),$(N,"class","mcp-tools svelte-1czpo3w"),$(B,"href",n[5]),$(B,"target","_blank"),$(B,"class","svelte-1czpo3w")},m(A,K){v(A,e,K),p(e,t),p(t,l),p(t,i);for(let x=0;x<Z.length;x+=1)Z[x]&&Z[x].m(t,null);v(A,r,K),R&&R.m(A,K),v(A,f,K),v(A,o,K),p(o,a),p(a,_),p(a,d),p(o,g),M&&M.m(o,null),v(A,b,K),v(A,N,K);for(let x=0;x<le.length;x+=1)le[x]&&le[x].m(N,null);v(A,E,K),v(A,I,K),v(A,D,K),~S&&ce[S].m(A,K),v(A,P,K),ne&&ne.m(A,K),v(A,T,K),v(A,V,K),v(A,F,K),v(A,z,K),p(z,B),p(B,q),H=!0},p(A,K){if(K[0]&4160){L=fe(A[12]);let se;for(se=0;se<L.length;se+=1){const me=Et(A,L,se);Z[se]?Z[se].p(me,K):(Z[se]=Ct(me),Z[se].c(),Z[se].m(t,null))}for(;se<Z.length;se+=1)Z[se].d(1);Z.length=L.length}if(A[6]!=="stdio"?R?(R.p(A,K),K[0]&64&&G(R,1)):(R=Pt(A),R.c(),G(R,1),R.m(f.parentNode,f)):R&&(pe(),Q(R,1,1,()=>{R=null}),de()),(!H||K[0]&3)&&c!==(c=(A[1].length>0?A[1].length:A[0].length)+"")&&X(_,c),A[1].length>0?M?M.p(A,K):(M=$t(A),M.c(),M.m(o,null)):M&&(M.d(1),M=null),K[0]&71){J=fe(A[1].length>0?A[1]:A[0]);let se;for(se=0;se<J.length;se+=1){const me=wt(A,J,se);le[se]?le[se].p(me,K):(le[se]=It(me),le[se].c(),le[se].m(N,null))}for(;se<le.length;se+=1)le[se].d(1);le.length=J.length}let x=S;S=U(A),S===x?~S&&ce[S].p(A,K):(C&&(pe(),Q(ce[x],1,1,()=>{ce[x]=null}),de()),~S?(C=ce[S],C?C.p(A,K):(C=ce[S]=ee[S](A),C.c()),G(C,1),C.m(P.parentNode,P)):C=null),A[4]?ne?ne.p(A,K):(ne=Dt(A),ne.c(),ne.m(T.parentNode,T)):ne&&(ne.d(1),ne=null),(!H||K[0]&32)&&$(B,"href",A[5])},i(A){H||(G(R),G(C),H=!0)},o(A){Q(R),Q(C),H=!1},d(A){A&&(u(e),u(r),u(f),u(o),u(b),u(N),u(E),u(I),u(D),u(P),u(T),u(V),u(F),u(z)),ve(Z,A),R&&R.d(A),M&&M.d(),ve(le,A),~S&&ce[S].d(A),ne&&ne.d(A)}}}function Ct(n){let e,t=n[32]+"",l,s,i,r,f;function o(){return n[17](n[31])}return{c(){e=k("button"),l=h(t),s=W(),this.h()},l(a){e=w(a,"BUTTON",{type:!0,class:!0});var c=O(e);l=m(c,t),s=Y(c),c.forEach(u),this.h()},h(){$(e,"type","button"),$(e,"class",i="snippet "+(n[6]===n[31]?"current-lang":"inactive-lang")+" svelte-1czpo3w")},m(a,c){v(a,e,c),p(e,l),p(e,s),r||(f=Ee(e,"click",o),r=!0)},p(a,c){n=a,c[0]&64&&i!==(i="snippet "+(n[6]===n[31]?"current-lang":"inactive-lang")+" svelte-1czpo3w")&&$(e,"class",i)},d(a){a&&u(e),r=!1,f()}}}function Pt(n){let e,t,l,s=" ",i;return e=new Ce({props:{$$slots:{default:[un]},$$scope:{ctx:n}}}),{c(){re(e.$$.fragment),t=W(),l=k("p"),l.textContent=s},l(r){ie(e.$$.fragment,r),t=Y(r),l=w(r,"P",{"data-svelte-h":!0}),te(l)!=="svelte-9hmwf2"&&(l.textContent=s)},m(r,f){ae(e,r,f),v(r,t,f),v(r,l,f),i=!0},p(r,f){const o={};f[0]&2112|f[1]&16&&(o.$$scope={dirty:f,ctx:r}),e.$set(o)},i(r){i||(G(e.$$.fragment,r),i=!0)},o(r){Q(e.$$.fragment,r),i=!1},d(r){r&&(u(t),u(l)),oe(e,r)}}}function un(n){let e,t,l,s="●",i,r=n[6]==="sse"?"SSE":"Streamable HTTP",f,o,a,c,_,d,g,b;return g=new ge({props:{code:n[11]}}),{c(){e=k("div"),t=k("label"),l=k("span"),l.textContent=s,i=h("MCP Server URL ("),f=h(r),o=h(")"),a=W(),c=k("div"),_=k("input"),d=W(),re(g.$$.fragment),this.h()},l(N){e=w(N,"DIV",{class:!0});var E=O(e);t=w(E,"LABEL",{for:!0,class:!0});var I=O(t);l=w(I,"SPAN",{class:!0,"data-svelte-h":!0}),te(l)!=="svelte-ygpcki"&&(l.textContent=s),i=m(I,"MCP Server URL ("),f=m(I,r),o=m(I,")"),I.forEach(u),a=Y(E),c=w(E,"DIV",{class:!0});var y=O(c);_=w(y,"INPUT",{id:!0,type:!0,class:!0}),d=Y(y),ie(g.$$.fragment,y),y.forEach(u),E.forEach(u),this.h()},h(){$(l,"class","status-indicator active svelte-1czpo3w"),$(t,"for","mcp-server-url"),$(t,"class","svelte-1czpo3w"),$(_,"id","mcp-server-url"),$(_,"type","text"),_.readOnly=!0,_.value=n[11],$(_,"class","svelte-1czpo3w"),$(c,"class","textbox svelte-1czpo3w"),$(e,"class","mcp-url svelte-1czpo3w")},m(N,E){v(N,e,E),p(e,t),p(t,l),p(t,i),p(t,f),p(t,o),p(e,a),p(e,c),p(c,_),p(c,d),ae(g,c,null),b=!0},p(N,E){(!b||E[0]&64)&&r!==(r=N[6]==="sse"?"SSE":"Streamable HTTP")&&X(f,r),(!b||E[0]&2048&&_.value!==N[11])&&(_.value=N[11]);const I={};E[0]&2048&&(I.code=N[11]),g.$set(I)},i(N){b||(G(g.$$.fragment,N),b=!0)},o(N){Q(g.$$.fragment,N),b=!1},d(N){N&&u(e),oe(g)}}}function $t(n){let e,t,l="Select All",s,i,r="Select None",f,o;return{c(){e=k("div"),t=k("button"),t.textContent=l,s=W(),i=k("button"),i.textContent=r,this.h()},l(a){e=w(a,"DIV",{class:!0});var c=O(e);t=w(c,"BUTTON",{class:!0,"data-svelte-h":!0}),te(t)!=="svelte-113vfrc"&&(t.textContent=l),s=Y(c),i=w(c,"BUTTON",{class:!0,"data-svelte-h":!0}),te(i)!=="svelte-49075b"&&(i.textContent=r),c.forEach(u),this.h()},h(){$(t,"class","select-all-btn svelte-1czpo3w"),$(i,"class","select-none-btn svelte-1czpo3w"),$(e,"class","tool-selection-controls svelte-1czpo3w")},m(a,c){v(a,e,c),p(e,t),p(e,s),p(e,i),f||(o=[Ee(t,"click",n[18]),Ee(i,"click",n[19])],f=!0)},p:_e,d(a){a&&u(e),f=!1,Qt(o)}}}function St(n){let e,t,l,s,i,r;function f(...o){return n[20](n[24],...o)}return{c(){e=k("input"),this.h()},l(o){e=w(o,"INPUT",{type:!0,class:!0,style:!0}),this.h()},h(){$(e,"type","checkbox"),$(e,"class","tool-checkbox svelte-1czpo3w"),e.checked=t=n[2].has(n[24].name)||n[6]!=="streamable_http",e.disabled=l=n[6]!=="streamable_http",$(e,"style",s=n[6]!=="streamable_http"?"opacity: 0.5; cursor: not-allowed;":"")},m(o,a){v(o,e,a),i||(r=Ee(e,"change",f),i=!0)},p(o,a){n=o,a[0]&71&&t!==(t=n[2].has(n[24].name)||n[6]!=="streamable_http")&&(e.checked=t),a[0]&64&&l!==(l=n[6]!=="streamable_http")&&(e.disabled=l),a[0]&64&&s!==(s=n[6]!=="streamable_http"?"opacity: 0.5; cursor: not-allowed;":"")&&$(e,"style",s)},d(o){o&&u(e),i=!1,r()}}}function Tt(n){let e,t;function l(r,f){return f[0]&3&&(t=null),t==null&&(t=Object.keys(r[24].parameters).length>0),t?pn:_n}let s=l(n,[-1,-1]),i=s(n);return{c(){e=k("div"),i.c(),this.h()},l(r){e=w(r,"DIV",{class:!0});var f=O(e);i.l(f),f.forEach(u),this.h()},h(){$(e,"class","tool-content svelte-1czpo3w")},m(r,f){v(r,e,f),i.m(e,null)},p(r,f){s===(s=l(r,f))&&i?i.p(r,f):(i.d(1),i=s(r),i&&(i.c(),i.m(e,null)))},d(r){r&&u(e),i.d()}}}function _n(n){let e,t="Takes no input parameters";return{c(){e=k("p"),e.textContent=t},l(l){e=w(l,"P",{"data-svelte-h":!0}),te(e)!=="svelte-wp9k8h"&&(e.textContent=t)},m(l,s){v(l,e,s)},p:_e,d(l){l&&u(e)}}}function pn(n){let e,t=fe(Object.entries(n[24].parameters)),l=[];for(let s=0;s<t.length;s+=1)l[s]=Nt(yt(n,t,s));return{c(){e=k("div");for(let s=0;s<l.length;s+=1)l[s].c();this.h()},l(s){e=w(s,"DIV",{class:!0});var i=O(e);for(let r=0;r<l.length;r+=1)l[r].l(i);i.forEach(u),this.h()},h(){$(e,"class","tool-parameters")},m(s,i){v(s,e,i);for(let r=0;r<l.length;r+=1)l[r]&&l[r].m(e,null)},p(s,i){if(i[0]&3){t=fe(Object.entries(s[24].parameters));let r;for(r=0;r<t.length;r+=1){const f=yt(s,t,r);l[r]?l[r].p(f,i):(l[r]=Nt(f),l[r].c(),l[r].m(e,null))}for(;r<l.length;r+=1)l[r].d(1);l.length=t.length}},d(s){s&&u(e),ve(l,s)}}}function Nt(n){let e,t,l=n[27]+"",s,i,r,f,o=n[28].type+"",a,c=n[28].default!==void 0?`, default: ${JSON.stringify(n[28].default)}`:"",_,d,g,b,N=(n[28].description?n[28].description:"⚠︎ No description for this parameter in function docstring")+"",E,I;return{c(){e=k("div"),t=k("code"),s=h(l),i=W(),r=k("span"),f=h("("),a=h(o),_=h(c),d=h(")"),g=W(),b=k("p"),E=h(N),I=W(),this.h()},l(y){e=w(y,"DIV",{class:!0});var D=O(e);t=w(D,"CODE",{class:!0});var S=O(t);s=m(S,l),S.forEach(u),i=Y(D),r=w(D,"SPAN",{class:!0});var C=O(r);f=m(C,"("),a=m(C,o),_=m(C,c),d=m(C,")"),C.forEach(u),g=Y(D),b=w(D,"P",{class:!0});var P=O(b);E=m(P,N),P.forEach(u),I=Y(D),D.forEach(u),this.h()},h(){$(t,"class","svelte-1czpo3w"),$(r,"class","parameter-type svelte-1czpo3w"),$(b,"class","parameter-description svelte-1czpo3w"),$(e,"class","parameter svelte-1czpo3w")},m(y,D){v(y,e,D),p(e,t),p(t,s),p(e,i),p(e,r),p(r,f),p(r,a),p(r,_),p(r,d),p(e,g),p(e,b),p(b,E),p(e,I)},p(y,D){D[0]&3&&l!==(l=y[27]+"")&&X(s,l),D[0]&3&&o!==(o=y[28].type+"")&&X(a,o),D[0]&3&&c!==(c=y[28].default!==void 0?`, default: ${JSON.stringify(y[28].default)}`:"")&&X(_,c),D[0]&3&&N!==(N=(y[28].description?y[28].description:"⚠︎ No description for this parameter in function docstring")+"")&&X(E,N)},d(y){y&&u(e)}}}function It(n){let e,t,l,s,i,r,f=n[24].name+"",o,a,c,_=(n[24].description?n[24].description:"⚠︎ No description provided in function docstring")+"",d,g,b,N=n[24].expanded?"▼":"▶",E,I,y,D,S,C=n[1].length>0&&St(n);function P(){return n[21](n[24],n[25],n[26])}let T=n[24].expanded&&Tt(n);return{c(){e=k("div"),t=k("div"),C&&C.c(),l=W(),s=k("button"),i=k("span"),r=k("span"),o=h(f),a=h(`  
							`),c=k("span"),d=h(_),g=W(),b=k("span"),E=h(N),I=W(),T&&T.c(),y=W(),this.h()},l(V){e=w(V,"DIV",{class:!0});var j=O(e);t=w(j,"DIV",{class:!0});var F=O(t);C&&C.l(F),l=Y(F),s=w(F,"BUTTON",{class:!0});var z=O(s);i=w(z,"SPAN",{});var B=O(i);r=w(B,"SPAN",{class:!0});var q=O(r);o=m(q,f),q.forEach(u),a=m(B,`  
							`),c=w(B,"SPAN",{class:!0});var H=O(c);d=m(H,_),H.forEach(u),B.forEach(u),g=Y(z),b=w(z,"SPAN",{class:!0});var L=O(b);E=m(L,N),L.forEach(u),z.forEach(u),F.forEach(u),I=Y(j),T&&T.l(j),y=Y(j),j.forEach(u),this.h()},h(){$(r,"class","tool-name svelte-1czpo3w"),$(c,"class","tool-description svelte-1czpo3w"),$(b,"class","tool-arrow svelte-1czpo3w"),$(s,"class","tool-header svelte-1czpo3w"),$(t,"class","tool-header-wrapper svelte-1czpo3w"),$(e,"class","tool-item svelte-1czpo3w")},m(V,j){v(V,e,j),p(e,t),C&&C.m(t,null),p(t,l),p(t,s),p(s,i),p(i,r),p(r,o),p(i,a),p(i,c),p(c,d),p(s,g),p(s,b),p(b,E),p(e,I),T&&T.m(e,null),p(e,y),D||(S=Ee(s,"click",P),D=!0)},p(V,j){n=V,n[1].length>0?C?C.p(n,j):(C=St(n),C.c(),C.m(t,l)):C&&(C.d(1),C=null),j[0]&3&&f!==(f=n[24].name+"")&&X(o,f),j[0]&3&&_!==(_=(n[24].description?n[24].description:"⚠︎ No description provided in function docstring")+"")&&X(d,_),j[0]&3&&N!==(N=n[24].expanded?"▼":"▶")&&X(E,N),n[24].expanded?T?T.p(n,j):(T=Tt(n),T.c(),T.m(e,y)):T&&(T.d(1),T=null)},d(V){V&&u(e),C&&C.d(),T&&T.d(),D=!1,S()}}}function dn(n){let e,t="STDIO Transport",l,s,i="install Node.js",r,f,o=" ",a,c,_;return c=new Ce({props:{$$slots:{default:[vn]},$$scope:{ctx:n}}}),{c(){e=k("strong"),e.textContent=t,l=h(`: For clients that only support stdio (e.g.
		Claude Desktop), first
		`),s=k("a"),s.textContent=i,r=h(`. Then, you can use the following command:
		`),f=k("p"),f.textContent=o,a=W(),re(c.$$.fragment),this.h()},l(d){e=w(d,"STRONG",{"data-svelte-h":!0}),te(e)!=="svelte-1eu1x9e"&&(e.textContent=t),l=m(d,`: For clients that only support stdio (e.g.
		Claude Desktop), first
		`),s=w(d,"A",{href:!0,target:!0,class:!0,"data-svelte-h":!0}),te(s)!=="svelte-1s7lfgl"&&(s.textContent=i),r=m(d,`. Then, you can use the following command:
		`),f=w(d,"P",{"data-svelte-h":!0}),te(f)!=="svelte-9hmwf2"&&(f.textContent=o),a=Y(d),ie(c.$$.fragment,d),this.h()},h(){$(s,"href","https://nodejs.org/en/download/"),$(s,"target","_blank"),$(s,"class","svelte-1czpo3w")},m(d,g){v(d,e,g),v(d,l,g),v(d,s,g),v(d,r,g),v(d,f,g),v(d,a,g),ae(c,d,g),_=!0},p(d,g){const b={};g[0]&256|g[1]&16&&(b.$$scope={dirty:g,ctx:d}),c.$set(b)},i(d){_||(G(c.$$.fragment,d),_=!0)},o(d){Q(c.$$.fragment,d),_=!1},d(d){d&&(u(e),u(l),u(s),u(r),u(f),u(a)),oe(c,d)}}}function hn(n){let e,t="SSE Transport",l,s,i=" ",r,f,o;return f=new Ce({props:{$$slots:{default:[gn]},$$scope:{ctx:n}}}),{c(){e=k("strong"),e.textContent=t,l=h(`: The SSE transport has been deprecated by the
		MCP spec. We recommend using the Streamable HTTP transport instead. But to
		add this MCP to clients that only support server-sent events (SSE), simply
		add the following configuration to your MCP config.
		`),s=k("p"),s.textContent=i,r=W(),re(f.$$.fragment)},l(a){e=w(a,"STRONG",{"data-svelte-h":!0}),te(e)!=="svelte-1y7xdr6"&&(e.textContent=t),l=m(a,`: The SSE transport has been deprecated by the
		MCP spec. We recommend using the Streamable HTTP transport instead. But to
		add this MCP to clients that only support server-sent events (SSE), simply
		add the following configuration to your MCP config.
		`),s=w(a,"P",{"data-svelte-h":!0}),te(s)!=="svelte-9hmwf2"&&(s.textContent=i),r=Y(a),ie(f.$$.fragment,a)},m(a,c){v(a,e,c),v(a,l,c),v(a,s,c),v(a,r,c),ae(f,a,c),o=!0},p(a,c){const _={};c[0]&512|c[1]&16&&(_.$$scope={dirty:c,ctx:a}),f.$set(_)},i(a){o||(G(f.$$.fragment,a),o=!0)},o(a){Q(f.$$.fragment,a),o=!1},d(a){a&&(u(e),u(l),u(s),u(r)),oe(f,a)}}}function mn(n){let e,t="Streamable HTTP Transport",l,s,i=" ",r,f,o;return f=new Ce({props:{$$slots:{default:[bn]},$$scope:{ctx:n}}}),{c(){e=k("strong"),e.textContent=t,l=h(`: To add this MCP to clients that
		support Streamable HTTP, simply add the following configuration to your MCP
		config.
		`),s=k("p"),s.textContent=i,r=W(),re(f.$$.fragment)},l(a){e=w(a,"STRONG",{"data-svelte-h":!0}),te(e)!=="svelte-1xsk0f1"&&(e.textContent=t),l=m(a,`: To add this MCP to clients that
		support Streamable HTTP, simply add the following configuration to your MCP
		config.
		`),s=w(a,"P",{"data-svelte-h":!0}),te(s)!=="svelte-9hmwf2"&&(s.textContent=i),r=Y(a),ie(f.$$.fragment,a)},m(a,c){v(a,e,c),v(a,l,c),v(a,s,c),v(a,r,c),ae(f,a,c),o=!0},p(a,c){const _={};c[0]&1024|c[1]&16&&(_.$$scope={dirty:c,ctx:a}),f.$set(_)},i(a){o||(G(f.$$.fragment,a),o=!0)},o(a){Q(f.$$.fragment,a),o=!1},d(a){a&&(u(e),u(l),u(s),u(r)),oe(f,a)}}}function vn(n){let e,t,l,s,i,r,f=JSON.stringify(n[8],null,2)+"",o,a;return l=new ge({props:{code:JSON.stringify(n[8],null,2)}}),{c(){e=k("code"),t=k("div"),re(l.$$.fragment),s=W(),i=k("div"),r=k("pre"),o=h(f),this.h()},l(c){e=w(c,"CODE",{class:!0});var _=O(e);t=w(_,"DIV",{class:!0});var d=O(t);ie(l.$$.fragment,d),d.forEach(u),s=Y(_),i=w(_,"DIV",{});var g=O(i);r=w(g,"PRE",{class:!0});var b=O(r);o=m(b,f),b.forEach(u),g.forEach(u),_.forEach(u),this.h()},h(){$(t,"class","copy svelte-1czpo3w"),$(r,"class","svelte-1czpo3w"),$(e,"class","svelte-1czpo3w")},m(c,_){v(c,e,_),p(e,t),ae(l,t,null),p(e,s),p(e,i),p(i,r),p(r,o),a=!0},p(c,_){const d={};_[0]&256&&(d.code=JSON.stringify(c[8],null,2)),l.$set(d),(!a||_[0]&256)&&f!==(f=JSON.stringify(c[8],null,2)+"")&&X(o,f)},i(c){a||(G(l.$$.fragment,c),a=!0)},o(c){Q(l.$$.fragment,c),a=!1},d(c){c&&u(e),oe(l)}}}function gn(n){let e,t,l,s,i,r,f=JSON.stringify(n[9],null,2)+"",o,a;return l=new ge({props:{code:JSON.stringify(n[9],null,2)}}),{c(){e=k("code"),t=k("div"),re(l.$$.fragment),s=W(),i=k("div"),r=k("pre"),o=h(f),this.h()},l(c){e=w(c,"CODE",{class:!0});var _=O(e);t=w(_,"DIV",{class:!0});var d=O(t);ie(l.$$.fragment,d),d.forEach(u),s=Y(_),i=w(_,"DIV",{});var g=O(i);r=w(g,"PRE",{class:!0});var b=O(r);o=m(b,f),b.forEach(u),g.forEach(u),_.forEach(u),this.h()},h(){$(t,"class","copy svelte-1czpo3w"),$(r,"class","svelte-1czpo3w"),$(e,"class","svelte-1czpo3w")},m(c,_){v(c,e,_),p(e,t),ae(l,t,null),p(e,s),p(e,i),p(i,r),p(r,o),a=!0},p(c,_){const d={};_[0]&512&&(d.code=JSON.stringify(c[9],null,2)),l.$set(d),(!a||_[0]&512)&&f!==(f=JSON.stringify(c[9],null,2)+"")&&X(o,f)},i(c){a||(G(l.$$.fragment,c),a=!0)},o(c){Q(l.$$.fragment,c),a=!1},d(c){c&&u(e),oe(l)}}}function bn(n){let e,t,l,s,i,r,f=JSON.stringify(n[10],null,2)+"",o,a;return l=new ge({props:{code:JSON.stringify(n[10],null,2)}}),{c(){e=k("code"),t=k("div"),re(l.$$.fragment),s=W(),i=k("div"),r=k("pre"),o=h(f),this.h()},l(c){e=w(c,"CODE",{class:!0});var _=O(e);t=w(_,"DIV",{class:!0});var d=O(t);ie(l.$$.fragment,d),d.forEach(u),s=Y(_),i=w(_,"DIV",{});var g=O(i);r=w(g,"PRE",{class:!0});var b=O(r);o=m(b,f),b.forEach(u),g.forEach(u),_.forEach(u),this.h()},h(){$(t,"class","copy svelte-1czpo3w"),$(r,"class","svelte-1czpo3w"),$(e,"class","svelte-1czpo3w")},m(c,_){v(c,e,_),p(e,t),ae(l,t,null),p(e,s),p(e,i),p(i,r),p(r,o),a=!0},p(c,_){const d={};_[0]&1024&&(d.code=JSON.stringify(c[10],null,2)),l.$set(d),(!a||_[0]&1024)&&f!==(f=JSON.stringify(c[10],null,2)+"")&&X(o,f)},i(c){a||(G(l.$$.fragment,c),a=!0)},o(c){Q(l.$$.fragment,c),a=!1},d(c){c&&u(e),oe(l)}}}function Dt(n){let e,t,l,s,i,r,f=`The <code>upload_files_to_gradio</code> tool uploads files from your
				local <code>UPLOAD_DIRECTORY</code> (or any of its subdirectories) to
				the Gradio app. This is needed because MCP servers require files to be
				provided as URLs. You can omit this tool if you prefer to upload files
				manually. This tool requires
				<a href="https://docs.astral.sh/uv/getting-started/installation/" target="_blank" class="svelte-1czpo3w">uv</a> to be installed.`,o,a;return{c(){e=k("div"),t=k("label"),l=k("input"),s=h(`
				Include Gradio file upload tool`),i=W(),r=k("p"),r.innerHTML=f,this.h()},l(c){e=w(c,"DIV",{class:!0});var _=O(e);t=w(_,"LABEL",{class:!0});var d=O(t);l=w(d,"INPUT",{type:!0,class:!0}),s=m(d,`
				Include Gradio file upload tool`),d.forEach(u),i=Y(_),r=w(_,"P",{class:!0,"data-svelte-h":!0}),te(r)!=="svelte-1md7mqc"&&(r.innerHTML=f),_.forEach(u),this.h()},h(){$(l,"type","checkbox"),$(l,"class","checkbox svelte-1czpo3w"),$(t,"class","checkbox-label svelte-1czpo3w"),$(r,"class","file-upload-explanation svelte-1czpo3w"),$(e,"class","file-upload-section svelte-1czpo3w")},m(c,_){v(c,e,_),p(e,t),p(t,l),l.checked=n[7],p(t,s),p(e,i),p(e,r),o||(a=Ee(l,"change",n[22]),o=!0)},p(c,_){_[0]&128&&(l.checked=c[7])},d(c){c&&u(e),o=!1,a()}}}function kn(n){let e,t,l,s;const i=[fn,cn],r=[];function f(o,a){return o[3]?0:1}return e=f(n),t=r[e]=i[e](n),{c(){t.c(),l=ue()},l(o){t.l(o),l=ue()},m(o,a){r[e].m(o,a),v(o,l,a),s=!0},p(o,a){let c=e;e=f(o),e===c?r[e].p(o,a):(pe(),Q(r[c],1,1,()=>{r[c]=null}),de(),t=r[e],t?t.p(o,a):(t=r[e]=i[e](o),t.c()),G(t,1),t.m(l.parentNode,l))},i(o){s||(G(t),s=!0)},o(o){Q(t),s=!1},d(o){o&&u(l),r[e].d(o)}}}function wn(n,e,t){let l,s,i,r,{mcp_server_active:f}=e,{mcp_server_url:o}=e,{mcp_server_url_streamable:a}=e,{tools:c}=e,{all_tools:_=[]}=e,{selected_tools:d=new Set}=e,{mcp_json_sse:g}=e,{mcp_json_stdio:b}=e,{file_data_present:N}=e,{mcp_docs:E}=e,I="streamable_http",y=!0;const D=[["streamable_http","Streamable HTTP"],["sse","SSE"],["stdio","STDIO"]];function S(z,B){var H;if(!z)return null;const q=JSON.parse(JSON.stringify(z));if(B&&N){const L={command:"uvx",args:["--from","gradio[mcp]","gradio","upload-mcp",I==="sse"?o:a,"<UPLOAD_DIRECTORY>"]};q.mcpServers.upload_files_to_gradio=L}else(H=q.mcpServers)==null||delete H.upload_files_to_gradio;return q}const C=z=>t(6,I=z),P=()=>{t(2,d=new Set(_.map(z=>z.name)))},T=()=>{t(2,d=new Set)},V=(z,B)=>{B.currentTarget.checked?d.add(z.name):d.delete(z.name),t(2,d)},j=(z,B,q)=>t(1,B[q].expanded=!z.expanded,_,t(0,c));function F(){y=this.checked,t(7,y)}return n.$$set=z=>{"mcp_server_active"in z&&t(3,f=z.mcp_server_active),"mcp_server_url"in z&&t(13,o=z.mcp_server_url),"mcp_server_url_streamable"in z&&t(14,a=z.mcp_server_url_streamable),"tools"in z&&t(0,c=z.tools),"all_tools"in z&&t(1,_=z.all_tools),"selected_tools"in z&&t(2,d=z.selected_tools),"mcp_json_sse"in z&&t(15,g=z.mcp_json_sse),"mcp_json_stdio"in z&&t(16,b=z.mcp_json_stdio),"file_data_present"in z&&t(4,N=z.file_data_present),"mcp_docs"in z&&t(5,E=z.mcp_docs)},n.$$.update=()=>{n.$$.dirty[0]&24640&&t(11,l=I==="sse"?o:a),n.$$.dirty[0]&49280&&t(10,s=S(g?{...g,mcpServers:{...g.mcpServers,gradio:{...g.mcpServers.gradio,url:a}}}:null,y)),n.$$.dirty[0]&32896&&t(9,i=S(g,y)),n.$$.dirty[0]&65664&&t(8,r=S(b,y))},[c,_,d,f,N,E,I,y,r,i,s,l,D,o,a,g,b,C,P,T,V,j,F]}class yn extends ke{constructor(e){super(),we(this,e,wn,kn,ye,{mcp_server_active:3,mcp_server_url:13,mcp_server_url_streamable:14,tools:0,all_tools:1,selected_tools:2,mcp_json_sse:15,mcp_json_stdio:16,file_data_present:4,mcp_docs:5},null,[-1,-1])}}function At(n,e,t){const l=n.slice();return l[36]=e[t],l}function jt(n,e,t){const l=n.slice();return l[39]=e[t][0],l[40]=e[t][1],l[41]=e[t][2],l}function Ot(n){let e,t,l,s;const i=[Cn,En],r=[];function f(o,a){return o[17]?0:1}return e=f(n),t=r[e]=i[e](n),{c(){t.c(),l=ue()},l(o){t.l(o),l=ue()},m(o,a){r[e].m(o,a),v(o,l,a),s=!0},p(o,a){t.p(o,a)},i(o){s||(G(t),s=!0)},o(o){Q(t),s=!1},d(o){o&&u(l),r[e].d(o)}}}function En(n){let e,t;return e=new ll({props:{root:n[0]}}),e.$on("close",n[28]),{c(){re(e.$$.fragment)},l(l){ie(e.$$.fragment,l)},m(l,s){ae(e,l,s),t=!0},p(l,s){const i={};s[0]&1&&(i.root=l[0]),e.$set(i)},i(l){t||(G(e.$$.fragment,l),t=!0)},o(l){Q(e.$$.fragment,l),t=!1},d(l){oe(e,l)}}}function Cn(n){let e,t,l,s,i,r='<p style="font-size: var(--text-lg);">Choose one of the following ways to interact with the API.</p>',f,o,a,c,_,d,g,b;t=new fl({props:{root:n[3]||n[0],api_count:n[17],current_language:n[10]}}),t.$on("close",n[23]);let N=fe(n[18]),E=[];for(let C=0;C<N.length;C+=1)E[C]=Vt(jt(n,N,C));const I=[$n,Pn],y=[];function D(C,P){return C[5].length?0:1}_=D(n),d=y[_]=I[_](n);let S=n[10]!=="mcp"&&Ht(n);return{c(){e=k("div"),re(t.$$.fragment),l=W(),s=k("div"),i=k("div"),i.innerHTML=r,f=W(),o=k("div"),a=k("div");for(let C=0;C<E.length;C+=1)E[C].c();c=W(),d.c(),g=W(),S&&S.c(),this.h()},l(C){e=w(C,"DIV",{class:!0});var P=O(e);ie(t.$$.fragment,P),P.forEach(u),l=Y(C),s=w(C,"DIV",{class:!0});var T=O(s);i=w(T,"DIV",{class:!0,"data-svelte-h":!0}),te(i)!=="svelte-1xla08l"&&(i.innerHTML=r),f=Y(T),o=w(T,"DIV",{class:!0});var V=O(o);a=w(V,"DIV",{class:!0});var j=O(a);for(let F=0;F<E.length;F+=1)E[F].l(j);j.forEach(u),c=Y(V),d.l(V),g=Y(V),S&&S.l(V),V.forEach(u),T.forEach(u),this.h()},h(){$(e,"class","banner-wrap svelte-1b8jye8"),$(i,"class","client-doc svelte-1b8jye8"),$(a,"class","snippets svelte-1b8jye8"),$(o,"class","endpoint svelte-1b8jye8"),$(s,"class","docs-wrap svelte-1b8jye8")},m(C,P){v(C,e,P),ae(t,e,null),v(C,l,P),v(C,s,P),p(s,i),p(s,f),p(s,o),p(o,a);for(let T=0;T<E.length;T+=1)E[T]&&E[T].m(a,null);p(o,c),y[_].m(o,null),p(o,g),S&&S.m(o,null),b=!0},p(C,P){const T={};if(P[0]&9&&(T.root=C[3]||C[0]),P[0]&1024&&(T.current_language=C[10]),t.$set(T),P[0]&263168){N=fe(C[18]);let j;for(j=0;j<N.length;j+=1){const F=jt(C,N,j);E[j]?E[j].p(F,P):(E[j]=Vt(F),E[j].c(),E[j].m(a,null))}for(;j<E.length;j+=1)E[j].d(1);E.length=N.length}let V=_;_=D(C),_===V?y[_].p(C,P):(pe(),Q(y[V],1,1,()=>{y[V]=null}),de(),d=y[_],d?d.p(C,P):(d=y[_]=I[_](C),d.c()),G(d,1),d.m(o,g)),C[10]!=="mcp"?S?(S.p(C,P),P[0]&1024&&G(S,1)):(S=Ht(C),S.c(),G(S,1),S.m(o,null)):S&&(pe(),Q(S,1,1,()=>{S=null}),de())},i(C){b||(G(t.$$.fragment,C),G(d),G(S),b=!0)},o(C){Q(t.$$.fragment,C),Q(d),Q(S),b=!1},d(C){C&&(u(e),u(l),u(s)),oe(t),ve(E,C),y[_].d(),S&&S.d()}}}function Vt(n){let e,t,l,s,i=n[40]+"",r,f,o,a,c;function _(){return n[24](n[39])}return{c(){e=k("li"),t=k("img"),s=W(),r=h(i),f=W(),this.h()},l(d){e=w(d,"LI",{class:!0});var g=O(e);t=w(g,"IMG",{src:!0,alt:!0,class:!0}),s=Y(g),r=m(g,i),f=Y(g),g.forEach(u),this.h()},h(){Bt(t.src,l=n[41])||$(t,"src",l),$(t,"alt",""),$(t,"class","svelte-1b8jye8"),$(e,"class",o="snippet "+(n[10]===n[39]?"current-lang":"inactive-lang")+" svelte-1b8jye8")},m(d,g){v(d,e,g),p(e,t),p(e,s),p(e,r),p(e,f),a||(c=Ee(e,"click",_),a=!0)},p(d,g){n=d,g[0]&1024&&o!==(o="snippet "+(n[10]===n[39]?"current-lang":"inactive-lang")+" svelte-1b8jye8")&&$(e,"class",o)},d(d){d&&u(e),a=!1,c()}}}function Pn(n){let e,t,l,s,i,r;const f=[Nn,Tn,Sn],o=[];function a(_,d){return _[10]=="python"||_[10]=="javascript"?0:_[10]=="mcp"?1:2}t=a(n),l=o[t]=f[t](n);let c=n[10]!=="mcp"&&zt(n);return{c(){e=k("p"),l.c(),s=W(),c&&c.c(),i=ue(),this.h()},l(_){e=w(_,"P",{class:!0});var d=O(e);l.l(d),d.forEach(u),s=Y(_),c&&c.l(_),i=ue(),this.h()},h(){$(e,"class","padded svelte-1b8jye8")},m(_,d){v(_,e,d),o[t].m(e,null),v(_,s,d),c&&c.m(_,d),v(_,i,d),r=!0},p(_,d){let g=t;t=a(_),t===g?o[t].p(_,d):(pe(),Q(o[g],1,1,()=>{o[g]=null}),de(),l=o[t],l?l.p(_,d):(l=o[t]=f[t](_),l.c()),G(l,1),l.m(e,null)),_[10]!=="mcp"?c?(c.p(_,d),d[0]&1024&&G(c,1)):(c=zt(_),c.c(),G(c,1),c.m(i.parentNode,i)):c&&(pe(),Q(c,1,1,()=>{c=null}),de())},i(_){r||(G(l),G(c),r=!0)},o(_){Q(l),Q(c),r=!1},d(_){_&&(u(e),u(s),u(i)),o[t].d(),c&&c.d(_)}}}function $n(n){let e,t,l,s,i,r=n[5].length+"",f,o,a,c,_,d,g,b,N,E,I,y=`Note: Some API calls only affect the UI, so when using the
							clients, the desired result may be achieved with only a subset of
							the recorded calls.`,D,S,C="API Documentation",P;return N=new Ql({props:{current_language:n[10],api_calls:n[5],dependencies:n[1],root:n[0],api_prefix:n[2].api_prefix,short_root:n[3]||n[0],username:n[4]}}),{c(){e=k("div"),t=k("p"),l=h("🪄 Recorded API Calls "),s=k("span"),i=h("["),f=h(r),o=h("]"),a=W(),c=k("p"),_=h(`Here is the code snippet to replay the most recently recorded API
							calls using the `),d=h(n[10]),g=h(`
							client.`),b=W(),re(N.$$.fragment),E=W(),I=k("p"),I.textContent=y,D=W(),S=k("p"),S.textContent=C,this.h()},l(T){e=w(T,"DIV",{});var V=O(e);t=w(V,"P",{id:!0,style:!0});var j=O(t);l=m(j,"🪄 Recorded API Calls "),s=w(j,"SPAN",{class:!0});var F=O(s);i=m(F,"["),f=m(F,r),o=m(F,"]"),F.forEach(u),j.forEach(u),a=Y(V),c=w(V,"P",{});var z=O(c);_=m(z,`Here is the code snippet to replay the most recently recorded API
							calls using the `),d=m(z,n[10]),g=m(z,`
							client.`),z.forEach(u),b=Y(V),ie(N.$$.fragment,V),E=Y(V),I=w(V,"P",{"data-svelte-h":!0}),te(I)!=="svelte-1nzar32"&&(I.textContent=y),V.forEach(u),D=Y(T),S=w(T,"P",{style:!0,"data-svelte-h":!0}),te(S)!=="svelte-oqhm8o"&&(S.textContent=C),this.h()},h(){$(s,"class","api-count svelte-1b8jye8"),$(t,"id","num-recorded-api-calls"),he(t,"font-size","var(--text-lg)"),he(t,"font-weight","bold"),he(t,"margin","10px 0px"),he(S,"font-size","var(--text-lg)"),he(S,"font-weight","bold"),he(S,"margin","30px 0px 10px")},m(T,V){v(T,e,V),p(e,t),p(t,l),p(t,s),p(s,i),p(s,f),p(s,o),p(e,a),p(e,c),p(c,_),p(c,d),p(c,g),p(e,b),ae(N,e,null),p(e,E),p(e,I),v(T,D,V),v(T,S,V),P=!0},p(T,V){(!P||V[0]&32)&&r!==(r=T[5].length+"")&&X(f,r),(!P||V[0]&1024)&&X(d,T[10]);const j={};V[0]&1024&&(j.current_language=T[10]),V[0]&32&&(j.api_calls=T[5]),V[0]&2&&(j.dependencies=T[1]),V[0]&1&&(j.root=T[0]),V[0]&4&&(j.api_prefix=T[2].api_prefix),V[0]&9&&(j.short_root=T[3]||T[0]),V[0]&16&&(j.username=T[4]),N.$set(j)},i(T){P||(G(N.$$.fragment,T),P=!0)},o(T){Q(N.$$.fragment,T),P=!1},d(T){T&&(u(e),u(D),u(S)),oe(N)}}}function Sn(n){let e;return{c(){e=h("1. Confirm that you have cURL installed on your system.")},l(t){e=m(t,"1. Confirm that you have cURL installed on your system.")},m(t,l){v(t,e,l)},p:_e,i:_e,o:_e,d(t){t&&u(e)}}}function Tn(n){let e,t,l;function s(r){n[26](r)}let i={mcp_server_active:n[11],mcp_server_url:n[15],mcp_server_url_streamable:n[16],tools:n[6].filter(n[25]),all_tools:n[6],mcp_json_sse:n[7],mcp_json_stdio:n[8],file_data_present:n[14],mcp_docs:An};return n[9]!==void 0&&(i.selected_tools=n[9]),e=new yn({props:i}),$e.push(()=>Xt(e,"selected_tools",s)),{c(){re(e.$$.fragment)},l(r){ie(e.$$.fragment,r)},m(r,f){ae(e,r,f),l=!0},p(r,f){const o={};f[0]&2048&&(o.mcp_server_active=r[11]),f[0]&32768&&(o.mcp_server_url=r[15]),f[0]&65536&&(o.mcp_server_url_streamable=r[16]),f[0]&576&&(o.tools=r[6].filter(r[25])),f[0]&64&&(o.all_tools=r[6]),f[0]&128&&(o.mcp_json_sse=r[7]),f[0]&256&&(o.mcp_json_stdio=r[8]),f[0]&16384&&(o.file_data_present=r[14]),!t&&f[0]&512&&(t=!0,o.selected_tools=r[9],Kt(()=>t=!1)),e.$set(o)},i(r){l||(G(e.$$.fragment,r),l=!0)},o(r){Q(e.$$.fragment,r),l=!1},d(r){oe(e,r)}}}function Nn(n){let e,t,l,s,i,r,f,o;return{c(){e=h(`1. Install the
							`),t=k("span"),l=h(n[10]),s=h(`
							client (`),i=k("a"),r=h("docs"),o=h(") if you don't already have it installed."),this.h()},l(a){e=m(a,`1. Install the
							`),t=w(a,"SPAN",{style:!0});var c=O(t);l=m(c,n[10]),c.forEach(u),s=m(a,`
							client (`),i=w(a,"A",{href:!0,target:!0,class:!0});var _=O(i);r=m(_,"docs"),_.forEach(u),o=m(a,") if you don't already have it installed."),this.h()},h(){he(t,"text-transform","capitalize"),$(i,"href",f=n[10]=="python"?De:Ie),$(i,"target","_blank"),$(i,"class","svelte-1b8jye8")},m(a,c){v(a,e,c),v(a,t,c),p(t,l),v(a,s,c),v(a,i,c),p(i,r),v(a,o,c)},p(a,c){c[0]&1024&&X(l,a[10]),c[0]&1024&&f!==(f=a[10]=="python"?De:Ie)&&$(i,"href",f)},i:_e,o:_e,d(a){a&&(u(e),u(t),u(s),u(i),u(o))}}}function zt(n){let e,t,l,s,i,r,f,o;e=new Tl({props:{current_language:n[10]}});let a=n[3]&&qt(n);r=new He({props:{size:"sm",variant:"secondary",$$slots:{default:[In]},$$scope:{ctx:n}}}),r.$on("click",n[27]);let c=n[10]=="bash"&&Rt(n);return{c(){re(e.$$.fragment),t=W(),l=k("p"),s=h(`2. Find the API endpoint below corresponding to your desired
							function in the app. Copy the code snippet, replacing the
							placeholder values with your own input data.
							`),a&&a.c(),i=h(`

							Or use the
							`),re(r.$$.fragment),f=h(`
							to automatically generate your API requests.
							`),c&&c.c(),this.h()},l(_){ie(e.$$.fragment,_),t=Y(_),l=w(_,"P",{class:!0});var d=O(l);s=m(d,`2. Find the API endpoint below corresponding to your desired
							function in the app. Copy the code snippet, replacing the
							placeholder values with your own input data.
							`),a&&a.l(d),i=m(d,`

							Or use the
							`),ie(r.$$.fragment,d),f=m(d,`
							to automatically generate your API requests.
							`),c&&c.l(d),d.forEach(u),this.h()},h(){$(l,"class","padded svelte-1b8jye8")},m(_,d){ae(e,_,d),v(_,t,d),v(_,l,d),p(l,s),a&&a.m(l,null),p(l,i),ae(r,l,null),p(l,f),c&&c.m(l,null),o=!0},p(_,d){const g={};d[0]&1024&&(g.current_language=_[10]),e.$set(g),_[3]?a?a.p(_,d):(a=qt(_),a.c(),a.m(l,i)):a&&(a.d(1),a=null);const b={};d[1]&8192&&(b.$$scope={dirty:d,ctx:_}),r.$set(b),_[10]=="bash"?c?c.p(_,d):(c=Rt(_),c.c(),c.m(l,null)):c&&(c.d(1),c=null)},i(_){o||(G(e.$$.fragment,_),G(r.$$.fragment,_),o=!0)},o(_){Q(e.$$.fragment,_),Q(r.$$.fragment,_),o=!1},d(_){_&&(u(t),u(l)),oe(e,_),a&&a.d(),oe(r),c&&c.d()}}}function qt(n){let e,t,l,s,i;return{c(){e=h(`If this is a private Space, you may need to pass
								your Hugging Face token as well (`),t=k("a"),l=h("read more"),i=h(")."),this.h()},l(r){e=m(r,`If this is a private Space, you may need to pass
								your Hugging Face token as well (`),t=w(r,"A",{href:!0,class:!0,target:!0});var f=O(t);l=m(f,"read more"),f.forEach(u),i=m(r,")."),this.h()},h(){$(t,"href",s=n[10]=="python"?De+Te:n[10]=="javascript"?Ie+Te:Re),$(t,"class","underline svelte-1b8jye8"),$(t,"target","_blank")},m(r,f){v(r,e,f),v(r,t,f),p(t,l),v(r,i,f)},p(r,f){f[0]&1024&&s!==(s=r[10]=="python"?De+Te:r[10]=="javascript"?Ie+Te:Re)&&$(t,"href",s)},d(r){r&&(u(e),u(t),u(i))}}}function In(n){let e,t,l,s="API Recorder";return{c(){e=k("div"),t=W(),l=k("p"),l.textContent=s,this.h()},l(i){e=w(i,"DIV",{class:!0}),O(e).forEach(u),t=Y(i),l=w(i,"P",{class:!0,"data-svelte-h":!0}),te(l)!=="svelte-1ycmyh9"&&(l.textContent=s),this.h()},h(){$(e,"class","loading-dot svelte-1b8jye8"),$(l,"class","self-baseline svelte-1b8jye8")},m(i,r){v(i,e,r),v(i,t,r),v(i,l,r)},p:_e,d(i){i&&(u(e),u(t),u(l))}}}function Rt(n){let e,t,l,s,i,r="2 requests",f,o,a="POST",c,_,d="GET",g,b,N="POST",E,I,y="EVENT_ID",D,S,C="GET",P,T,V="awk",j,F,z="read",B,q,H,L,Z,R=n[4]!==null&&Mt();return{c(){e=k("br"),t=h(" "),l=k("br"),s=h(`Making a
								prediction and getting a result requires
								`),i=k("strong"),i.textContent=r,f=h(`: a
								`),o=k("code"),o.textContent=a,c=h(`
								and a `),_=k("code"),_.textContent=d,g=h(" request. The "),b=k("code"),b.textContent=N,E=h(` request
								returns an `),I=k("code"),I.textContent=y,D=h(`, which is used in the second
								`),S=k("code"),S.textContent=C,P=h(` request to fetch the results. In these
								snippets, we've used `),T=k("code"),T.textContent=V,j=h(" and "),F=k("code"),F.textContent=z,B=h(` to
								parse the results, combining these two requests into one command
								for ease of use. `),R&&R.c(),q=h(` See
								`),H=k("a"),L=h("curl docs"),Z=h("."),this.h()},l(M){e=w(M,"BR",{}),t=m(M," "),l=w(M,"BR",{}),s=m(M,`Making a
								prediction and getting a result requires
								`),i=w(M,"STRONG",{"data-svelte-h":!0}),te(i)!=="svelte-3tg330"&&(i.textContent=r),f=m(M,`: a
								`),o=w(M,"CODE",{class:!0,"data-svelte-h":!0}),te(o)!=="svelte-12krxoq"&&(o.textContent=a),c=m(M,`
								and a `),_=w(M,"CODE",{class:!0,"data-svelte-h":!0}),te(_)!=="svelte-1r51gq6"&&(_.textContent=d),g=m(M," request. The "),b=w(M,"CODE",{class:!0,"data-svelte-h":!0}),te(b)!=="svelte-12krxoq"&&(b.textContent=N),E=m(M,` request
								returns an `),I=w(M,"CODE",{class:!0,"data-svelte-h":!0}),te(I)!=="svelte-wz35l4"&&(I.textContent=y),D=m(M,`, which is used in the second
								`),S=w(M,"CODE",{class:!0,"data-svelte-h":!0}),te(S)!=="svelte-1r51gq6"&&(S.textContent=C),P=m(M,` request to fetch the results. In these
								snippets, we've used `),T=w(M,"CODE",{class:!0,"data-svelte-h":!0}),te(T)!=="svelte-qg98el"&&(T.textContent=V),j=m(M," and "),F=w(M,"CODE",{class:!0,"data-svelte-h":!0}),te(F)!=="svelte-wk48ls"&&(F.textContent=z),B=m(M,` to
								parse the results, combining these two requests into one command
								for ease of use. `),R&&R.l(M),q=m(M,` See
								`),H=w(M,"A",{href:!0,target:!0,class:!0});var J=O(H);L=m(J,"curl docs"),J.forEach(u),Z=m(M,"."),this.h()},h(){$(o,"class","svelte-1b8jye8"),$(_,"class","svelte-1b8jye8"),$(b,"class","svelte-1b8jye8"),$(I,"class","svelte-1b8jye8"),$(S,"class","svelte-1b8jye8"),$(T,"class","svelte-1b8jye8"),$(F,"class","svelte-1b8jye8"),$(H,"href",Re),$(H,"target","_blank"),$(H,"class","svelte-1b8jye8")},m(M,J){v(M,e,J),v(M,t,J),v(M,l,J),v(M,s,J),v(M,i,J),v(M,f,J),v(M,o,J),v(M,c,J),v(M,_,J),v(M,g,J),v(M,b,J),v(M,E,J),v(M,I,J),v(M,D,J),v(M,S,J),v(M,P,J),v(M,T,J),v(M,j,J),v(M,F,J),v(M,B,J),R&&R.m(M,J),v(M,q,J),v(M,H,J),p(H,L),v(M,Z,J)},p(M,J){M[4]!==null?R||(R=Mt(),R.c(),R.m(q.parentNode,q)):R&&(R.d(1),R=null)},d(M){M&&(u(e),u(t),u(l),u(s),u(i),u(f),u(o),u(c),u(_),u(g),u(b),u(E),u(I),u(D),u(S),u(P),u(T),u(j),u(F),u(B),u(q),u(H),u(Z)),R&&R.d(M)}}}function Mt(n){let e;return{c(){e=h(`Note: connecting to an authenticated app requires an
									additional request.`)},l(t){e=m(t,`Note: connecting to an authenticated app requires an
									additional request.`)},m(t,l){v(t,e,l)},d(t){t&&u(e)}}}function Ht(n){let e,t,l=fe(n[1]),s=[];for(let r=0;r<l.length;r+=1)s[r]=Ut(At(n,l,r));const i=r=>Q(s[r],1,1,()=>{s[r]=null});return{c(){for(let r=0;r<s.length;r+=1)s[r].c();e=ue()},l(r){for(let f=0;f<s.length;f+=1)s[f].l(r);e=ue()},m(r,f){for(let o=0;o<s.length;o+=1)s[o]&&s[o].m(r,f);v(r,e,f),t=!0},p(r,f){if(f[0]&13343){l=fe(r[1]);let o;for(o=0;o<l.length;o+=1){const a=At(r,l,o);s[o]?(s[o].p(a,f),G(s[o],1)):(s[o]=Ut(a),s[o].c(),G(s[o],1),s[o].m(e.parentNode,e))}for(pe(),o=l.length;o<s.length;o+=1)i(o);de()}},i(r){if(!t){for(let f=0;f<l.length;f+=1)G(s[f]);t=!0}},o(r){s=s.filter(Boolean);for(let f=0;f<s.length;f+=1)Q(s[f]);t=!1},d(r){r&&u(e),ve(s,r)}}}function Lt(n){let e,t,l,s,i,r,f,o;return t=new Fl({props:{endpoint_parameters:n[12].named_endpoints["/"+n[36].api_name].parameters,dependency:n[36],current_language:n[10],root:n[0],space_id:n[3],username:n[4],api_prefix:n[2].api_prefix,api_description:n[12].named_endpoints["/"+n[36].api_name].description}}),s=new gl({props:{endpoint_returns:n[12].named_endpoints["/"+n[36].api_name].parameters,js_returns:n[13].named_endpoints["/"+n[36].api_name].parameters,is_running:Ft,current_language:n[10]}}),r=new an({props:{endpoint_returns:n[12].named_endpoints["/"+n[36].api_name].returns,js_returns:n[13].named_endpoints["/"+n[36].api_name].returns,is_running:Ft,current_language:n[10]}}),{c(){e=k("div"),re(t.$$.fragment),l=W(),re(s.$$.fragment),i=W(),re(r.$$.fragment),f=W(),this.h()},l(a){e=w(a,"DIV",{class:!0});var c=O(e);ie(t.$$.fragment,c),l=Y(c),ie(s.$$.fragment,c),i=Y(c),ie(r.$$.fragment,c),f=Y(c),c.forEach(u),this.h()},h(){$(e,"class","endpoint-container svelte-1b8jye8")},m(a,c){v(a,e,c),ae(t,e,null),p(e,l),ae(s,e,null),p(e,i),ae(r,e,null),p(e,f),o=!0},p(a,c){const _={};c[0]&4098&&(_.endpoint_parameters=a[12].named_endpoints["/"+a[36].api_name].parameters),c[0]&2&&(_.dependency=a[36]),c[0]&1024&&(_.current_language=a[10]),c[0]&1&&(_.root=a[0]),c[0]&8&&(_.space_id=a[3]),c[0]&16&&(_.username=a[4]),c[0]&4&&(_.api_prefix=a[2].api_prefix),c[0]&4098&&(_.api_description=a[12].named_endpoints["/"+a[36].api_name].description),t.$set(_);const d={};c[0]&4098&&(d.endpoint_returns=a[12].named_endpoints["/"+a[36].api_name].parameters),c[0]&8194&&(d.js_returns=a[13].named_endpoints["/"+a[36].api_name].parameters),c[0]&1024&&(d.current_language=a[10]),s.$set(d);const g={};c[0]&4098&&(g.endpoint_returns=a[12].named_endpoints["/"+a[36].api_name].returns),c[0]&8194&&(g.js_returns=a[13].named_endpoints["/"+a[36].api_name].returns),c[0]&1024&&(g.current_language=a[10]),r.$set(g)},i(a){o||(G(t.$$.fragment,a),G(s.$$.fragment,a),G(r.$$.fragment,a),o=!0)},o(a){Q(t.$$.fragment,a),Q(s.$$.fragment,a),Q(r.$$.fragment,a),o=!1},d(a){a&&u(e),oe(t),oe(s),oe(r)}}}function Ut(n){let e,t,l=n[36].show_api&&n[12].named_endpoints["/"+n[36].api_name]&&Lt(n);return{c(){l&&l.c(),e=ue()},l(s){l&&l.l(s),e=ue()},m(s,i){l&&l.m(s,i),v(s,e,i),t=!0},p(s,i){s[36].show_api&&s[12].named_endpoints["/"+s[36].api_name]?l?(l.p(s,i),i[0]&4098&&G(l,1)):(l=Lt(s),l.c(),G(l,1),l.m(e.parentNode,e)):l&&(pe(),Q(l,1,1,()=>{l=null}),de())},i(s){t||(G(l),t=!0)},o(s){Q(l),t=!1},d(s){s&&u(e),l&&l.d(s)}}}function Dn(n){let e,t,l=n[12]&&Ot(n);return{c(){l&&l.c(),e=ue()},l(s){l&&l.l(s),e=ue()},m(s,i){l&&l.m(s,i),v(s,e,i),t=!0},p(s,i){s[12]?l?(l.p(s,i),i[0]&4096&&G(l,1)):(l=Ot(s),l.c(),G(l,1),l.m(e.parentNode,e)):l&&(pe(),Q(l,1,1,()=>{l=null}),de())},i(s){t||(G(l),t=!0)},o(s){Q(l),t=!1},d(s){s&&u(e),l&&l.d(s)}}}const Ie="https://www.gradio.app/guides/getting-started-with-the-js-client",De="https://www.gradio.app/guides/getting-started-with-the-python-client",Re="https://www.gradio.app/guides/querying-gradio-apps-with-curl",Te="#connecting-to-a-hugging-face-space",An="https://www.gradio.app/guides/building-mcp-server-with-gradio";let Ft=!1;function jn(n,e){const t=new URL(window.location.href);t.searchParams.set(n,e),history.replaceState(null,"",t.toString())}function On(n){return new URL(window.location.href).searchParams.get(n)}function Ve(n){return["python","javascript","bash","mcp"].includes(n??"")}function Vn(n,e,t){let l,s,i,r,{dependencies:f}=e,{root:o}=e,{app:a}=e,{space_id:c}=e,{root_node:_}=e,{username:d}=e,g=f.filter(U=>U.show_api).length;o===""&&(o=location.protocol+"//"+location.host+location.pathname),o.endsWith("/")||(o+="/");let{api_calls:b=[]}=e,N="python";const E=[["python","Python",Xl],["javascript","JavaScript",Kl],["bash","cURL",xl],["mcp","MCP",on]];let I=!1;async function y(){return await(await fetch(o.replace(/\/$/,"")+a.api_prefix+"/info")).json()}async function D(){return await a.view_api()}let S,C;y().then(U=>{t(12,S=U)}),D().then(U=>{t(13,C=U)});const P=Me();let T=[],V=[],j,F,z=!1,B=new Set,q=c?c.split("/").pop()+"_":"";function H(U){return q&&U.startsWith(q)?U.slice(q.length):U}const L={command:"uvx",args:["--from","gradio[mcp]","gradio","upload-mcp",o,"<UPLOAD_DIRECTORY>"]};async function Z(){try{let U=`${o}gradio_api/mcp/schema`;const A=await(await fetch(U)).json();t(14,z=A.map(K=>{var x;return(x=K.meta)==null?void 0:x.file_data_present}).some(K=>K)),t(6,T=A.map(K=>{var x;return{name:K.name,description:K.description||"",parameters:((x=K.inputSchema)==null?void 0:x.properties)||{},expanded:!1}})),t(9,B=new Set(T.map(K=>K.name))),V=A.map(K=>{var x;return((x=K.meta)==null?void 0:x.headers)||[]}).flat(),V.length>0?(t(7,j={mcpServers:{gradio:{url:i,headers:V.reduce((K,x)=>(K[x]="<YOUR_HEADER_VALUE>",K),{})}}}),t(8,F={mcpServers:{gradio:{command:"npx",args:["mcp-remote",i,"--transport","sse-only",...V.map(K=>["--header",`${K}: <YOUR_HEADER_VALUE>`]).flat()]}}})):(t(7,j={mcpServers:{gradio:{url:i}}}),t(8,F={mcpServers:{gradio:{command:"npx",args:["mcp-remote",i,"--transport","sse-only"]}}}),z&&(t(7,j.mcpServers.upload_files_to_gradio=L,j),t(8,F.mcpServers.upload_files_to_gradio=L,F)))}catch(U){console.error("Failed to fetch MCP tools:",U),t(6,T=[])}}Jt(()=>{var ne;document.body.style.overflow="hidden","parentIFrame"in window&&((ne=window.parentIFrame)==null||ne.scrollTo(0,0));const U=On("lang");return Ve(U)&&t(10,N=U),fetch(i).then(A=>{t(11,I=A.ok),I?(Z(),Ve(U)||t(10,N="mcp")):Ve(U)||t(10,N="python")}).catch(()=>{t(11,I=!1)}),()=>{document.body.style.overflow="auto"}});function R(U){Fe.call(this,n,U)}const M=U=>{t(10,N=U),jn("lang",U)},J=U=>B.has(U.name);function le(U){B=U,t(9,B)}const ee=()=>P("close",{api_recorder_visible:!0});function ce(U){Fe.call(this,n,U)}return n.$$set=U=>{"dependencies"in U&&t(1,f=U.dependencies),"root"in U&&t(0,o=U.root),"app"in U&&t(2,a=U.app),"space_id"in U&&t(3,c=U.space_id),"root_node"in U&&t(20,_=U.root_node),"username"in U&&t(4,d=U.username),"api_calls"in U&&t(5,b=U.api_calls)},n.$$.update=()=>{if(n.$$.dirty[0]&512&&t(22,l=Array.from(B)),n.$$.dirty[0]&4194304&&t(21,s=l.map(H)),n.$$.dirty[0]&1&&t(15,i=`${o}gradio_api/mcp/sse`),n.$$.dirty[0]&6291521&&t(16,r=l.length>0&&l.length<T.length?`${o}gradio_api/mcp/?tools=${s.join(",")}`:`${o}gradio_api/mcp/`),n.$$.dirty[0]&6292417&&j&&B.size>0){const U=l.length>0&&l.length<T.length?`${o}gradio_api/mcp/sse?tools=${s.join(",")}`:`${o}gradio_api/mcp/sse`;t(7,j.mcpServers.gradio.url=U,j),F&&t(8,F.mcpServers.gradio.args[1]=U,F)}},[o,f,a,c,d,b,T,j,F,B,N,I,S,C,z,i,r,g,E,P,_,s,l,R,M,J,le,ee,ce]}class Hn extends ke{constructor(e){super(),we(this,e,Vn,Dn,ye,{dependencies:1,root:0,app:2,space_id:3,root_node:20,username:4,api_calls:5},null,[-1,-1])}}export{Hn as default};
//# sourceMappingURL=ApiDocs.DrqKfr4o.js.map
