{"version": 3, "mappings": ";8WAKA,SAASA,GAAgBC,EAAW,CAChC,OAAQA,IAAc,QAClBA,IAAc,gBACdA,IAAc,cACdA,IAAc,gBACdA,IAAc,aACdA,IAAc,gBACdA,IAAc,mBACdA,IAAc,WACtB,CACA,SAASC,GAAkBD,EAAW,CAClC,OAAQA,IAAc,WAClBA,IAAc,WACdA,IAAc,WACdA,IAAc,cACdA,IAAc,UACdA,IAAc,QACtB,CACA,SAASE,GAAkBF,EAAW,CAClC,OAAOA,IAAc,UAAwCA,IAAc,YAA4CA,IAAc,UACzI,CACA,SAASG,GAA0BH,EAAW,CAC1C,OAAOA,IAAc,gBACzB,CACA,SAASI,GAAYJ,EAAWK,EAAOC,EAAiB,GAAO,CAC3D,GAAIN,IAAc,UACd,OAAOO,GAAQ,UAAUF,CAAK,EAE7B,GAAIL,IAAc,UACnB,OAAIM,IACAD,EAAM,CAAC,GAAK,IAETG,GAAQ,UAAUH,CAAK,EAE7B,GAAIL,IAAc,UACnB,OAAOS,GAAQ,UAAUJ,CAAK,EAE7B,GAAIL,IAAc,aACnB,OAAIM,IACAD,EAAM,CAAC,GAAK,GACZA,EAAM,CAAC,GAAK,IAETK,EAAW,UAAUL,CAAK,EAEhC,GAAIL,IAAc,SACnB,OAAO,IAAIW,GAAON,EAAM,CAAC,EAAGA,EAAM,CAAC,EAAGA,EAAM,CAAC,CAAC,EAE7C,GAAIL,IAAc,SACnB,OAAO,IAAIY,GAAOP,EAAM,CAAC,EAAGA,EAAM,CAAC,EAAGA,EAAM,CAAC,EAAGA,EAAM,CAAC,CAAC,EAGxD,MAAM,IAAI,MAAM,6BAA6BL,CAAS,EAAE,CAEhE,CAOO,SAASa,GAAkCC,EAAKT,EAAOU,EAAqB,OAC/E,MAAMf,IAAYgB,EAAAX,GAAA,YAAAA,EAAO,eAAP,YAAAW,EAAA,KAAAX,KAA2B,GAC7C,GAAIJ,GAAkBD,CAAS,GAAKE,GAAkBF,CAAS,EAC3De,EAAoBD,CAAG,EAAI,CACvB,MAAOT,EAAM,QAAS,EACtB,UAAAL,CACZ,UAEaA,IAAc,mBACnBe,EAAoBD,CAAG,EAAI,CACvB,MAAOT,EAAM,MACb,UAAAL,CACZ,UAGYA,IAAcK,EAAM,IAAMA,EAAM,MAChCU,EAAoBD,CAAG,EAAI,CACvB,GAAIT,EAAM,GACV,KAAMA,EAAM,KACZ,UAAAL,CAChB,UAIgB,OAAOK,GAAU,SACjBU,EAAoBD,CAAG,EAAIT,MAG3B,OAAM,IAAI,MAAM,6BAA6BA,CAAK,EAAE,CAIpE,CASO,SAASY,EAA0BH,EAAKC,EAAqBG,EAAiBC,EAAO,CACxF,MAAMC,EAAoBL,EAAoBD,CAAG,EACjD,IAAIO,EACJ,MAAMrB,GAAYoB,GAAA,YAAAA,EAAmB,QAAQA,GAAA,YAAAA,EAAmB,WAChE,GAAIrB,GAAgBC,CAAS,EAAG,CAC5B,IAAIsB,EAAQH,EAAM,OAAO,OAAQI,GAAOH,EAAkB,GAAKG,EAAE,KAAOH,EAAkB,GAAKG,EAAE,OAASH,EAAkB,IAAK,EAC7HE,EAAM,SAAW,IACjBA,EAAQH,EAAM,eAAe,OAAQI,GAAOH,EAAkB,GAAKG,EAAE,KAAOH,EAAkB,GAAKG,EAAE,OAASH,EAAkB,IAAK,GAEzIC,EAAaD,EAAkB,SAAWE,EAAM,KAAMC,GAAMA,EAAE,WAAaH,EAAkB,QAAQ,EAAIE,EAAM,CAAC,CACnH,SACQrB,GAAkBD,CAAS,EAChCqB,EAAajB,GAAYJ,EAAWoB,EAAkB,KAAK,UAEtDjB,GAA0BH,CAAS,EAAG,CAE3C,MAAMwB,EAAML,EAAM,gBAAgB,OAAQM,GAAOA,EAAG,OAASL,EAAkB,IAAI,EAEnFC,EAAaG,EAAI,SAAW,EAAIA,EAAI,CAAC,EAAIA,EAAI,KAAMC,GAAOA,EAAG,WAAaL,EAAkB,QAAQ,CACvG,MACQpB,IAAc,SACnBqB,EAAaK,GAAO,UAAUN,EAAkB,KAAK,EAEhDpB,IAAc,WACnBqB,EAAa,IAAIM,GAAkBP,EAAkB,KAAK,EAErDpB,IAAc,WACnBqB,EAAa,IAAIO,GAAkBR,EAAkB,KAAK,EAErDpB,IAAc,mBACnBqB,EAAaQ,GAAiB,UAAUT,EAAkB,KAAK,EAE1DpB,IAAc,UAAwCA,IAAc,UAAwCA,IAAc,UAC/HqB,EAAaD,EAAkB,MAAM,CAAC,EAEjCA,GAAqBA,EAAkB,QAAU,OACtDC,EAAaD,EAAkB,MAG3B,MAAM,QAAQA,CAAiB,EAE/BC,EAAaD,EAAkB,OAAO,CAACU,EAAKC,KACnCA,EAAI,YAGTD,EAAIC,EAAI,EAAE,EAAI,CACV,KAAMC,GAA2BD,EAAI,IAAI,CAC7D,EACoB,OAAOA,EAAI,MAAU,MACrBD,EAAIC,EAAI,EAAE,EAAE,MAAQd,EAA0B,QAASc,EAAKb,EAAiBC,CAAK,IAE/EW,GACR,CAAE,GAGLT,EAAaD,EAGrB,OAAOC,CACX,CAQO,SAASY,GAAmBjC,EAAW,CAG1C,OAAOA,IAAc,iCACzB,CC9KO,IAAIkC,IACV,SAAUA,EAAoB,CAC3BA,EAAmB,UAAe,YAClCA,EAAmB,eAAoB,iBACvCA,EAAmB,KAAU,OAC7BA,EAAmB,SAAc,WACjCA,EAAmB,OAAY,SAC/BA,EAAmB,MAAW,OAElC,GAAGA,KAAuBA,GAAqB,CAAE,EAAC,EAS3C,SAASC,GAA0BC,EAAeC,EAAMC,EAAOC,EAAoB,CACtF,OAAQF,EAAI,CACR,IAAK,YACD,OAAOE,EACAH,EAAc,WAAW,KAAMI,GAAMA,EAAE,WAAaF,CAAK,GAAK,KAC9DF,EAAc,WAAWE,CAAK,GAAK,KAC9C,IAAK,iBACD,OAAOC,EACAH,EAAc,gBAAgB,KAAMI,GAAMA,EAAE,WAAaF,CAAK,GAAK,KACnEF,EAAc,gBAAgBE,CAAK,GAAK,KACnD,IAAK,OACD,OAAOC,EACAH,EAAc,OAAO,KAAMI,GAAMA,EAAE,WAAaF,CAAK,GAAK,KAC1DF,EAAc,OAAOE,CAAK,GAAK,KAC1C,IAAK,WACD,OAAOC,EACAH,EAAc,UAAU,KAAMI,GAAMA,EAAE,WAAaF,CAAK,GAAK,KAC7DF,EAAc,UAAUE,CAAK,GAAK,KAC7C,IAAK,SACD,OAAOC,EACAH,EAAc,QAAQ,KAAMI,GAAMA,EAAE,WAAaF,CAAK,GAAK,KAC3DF,EAAc,QAAQE,CAAK,GAAK,KAC3C,IAAK,QACD,OAAOC,EACAH,EAAc,OAAO,KAAMI,GAAMA,EAAE,WAAaF,CAAK,GAAK,KAC1DF,EAAc,OAAOE,CAAK,GAAK,KAC1C,QACI,OAAO,IACd,CACL,CCjDO,IAAIG,IACV,SAAUA,EAAiB,CACxBA,EAAgB,aAAkB,eAClCA,EAAgB,aAAkB,eAClCA,EAAgB,kBAAuB,oBACvCA,EAAgB,mBAAwB,qBACxCA,EAAgB,kBAAuB,oBACvCA,EAAgB,qBAA0B,uBAC1CA,EAAgB,kBAAuB,oBACvCA,EAAgB,cAAmB,gBACnCA,EAAgB,mBAAwB,qBACxCA,EAAgB,mBAAwB,qBACxCA,EAAgB,eAAoB,iBACpCA,EAAgB,mBAAwB,oBAC5C,GAAGA,KAAoBA,GAAkB,CAAE,EAAC,EAMrC,MAAMC,EAAgB,CACzB,aAAc,CAIV,KAAK,aAAe,GAKpB,KAAK,IAAM,EACd,CACD,WAAWC,EAAM,OAKb,GAJKA,EAAK,OACNA,EAAK,KAAO,KAAK,OAErB,KAAK,IAAI,KAAKA,CAAI,EACd,KAAK,aAAc,CACnB,MAAMtC,GAAQW,EAAA2B,EAAK,UAAL,YAAA3B,EAAc,MACxB,OAAOX,GAAU,UAAYA,EAAM,aACnCuC,EAAO,IAAI,WAAWD,EAAK,SAAS,IAAIA,EAAK,SAAS,MAAM,GAAG,EAAE,CAAC,CAAC,IAAIA,EAAK,MAAM,MAAM,KAAK,UAAUtC,EAAM,cAAc,CAAC,KAAKA,EAAM,SAAU,GAAE,EAGnJuC,EAAO,IAAI,WAAWD,EAAK,SAAS,IAAIA,EAAK,SAAS,MAAM,GAAG,EAAE,CAAC,CAAC,IAAIA,EAAK,MAAM,MAAM,KAAK,UAAUA,EAAK,OAAO,CAAC,EAAE,CAE7H,CACJ,CACD,eAAeE,EAAQ,CACnB,OAAO,KAAK,IAAI,OAAQC,GAAMA,EAAE,SAAWD,CAAM,CACpD,CACL,CCtCO,MAAME,EAAiB,CAI1B,IAAI,eAAgB,CAChB,OAAO,KAAK,cACf,CACD,IAAI,cAAc1C,EAAO,CACjB,KAAK,iBAAmBA,IAG5B,KAAK,eAAiBA,EAClB,KAAK,gBACL,KAAK,OAAS,IAAIqC,GAClB,KAAK,OAAO,aAAe,IAG3B,KAAK,OAAS,KAErB,CACD,YAAYM,EAAQ,CAIhB,KAAK,SAAWC,IAIhB,KAAK,eAAiB,GAItB,KAAK,oBAAsB,GAI3B,KAAK,wBAA0B,GAI/B,KAAK,kBAAoB,GAIzB,KAAK,eAAiB,GAKtB,KAAK,aAAe,EAIpB,KAAK,yBAA2B,IAAIC,EAMpC,KAAK,uBAAyB,GAC9B,KAAK,eAAiB,GACtB,KAAK,eAAiBF,EACtB,KAAK,cAAgBA,EAAO,eAAiBA,EAAO,KACvD,CAMD,YAAYG,EAAM,CACd,OAAOA,KAAQ,KAAK,cACvB,CAMD,YAAYA,EAAM9C,EAAO,OACrB,KAAK,eAAe8C,CAAI,EAAI9C,GAC5BW,EAAA,KAAK,SAAL,MAAAA,EAAa,WAAW,CACpB,KAAM,KAAK,IAAK,EAChB,UAAW,KAAK,aAAc,EAC9B,SAAU,KAAK,SACf,OAAQ,qBACR,QAAS,CACL,KAAAmC,EACA,MAAA9C,CACH,CACb,EACK,CAOD,SAASgC,EAAMC,EAAO,CAClB,OAAOH,GAA0B,KAAK,cAAeE,EAAMC,CAAK,CACnE,CAMD,YAAYa,EAAM,OACd,OAAAnC,EAAA,KAAK,SAAL,MAAAA,EAAa,WAAW,CACpB,KAAM,KAAK,IAAK,EAChB,UAAW,KAAK,aAAc,EAC9B,SAAU,KAAK,SACf,OAAQ,qBACR,QAAS,CACL,KAAAmC,EACA,MAAO,KAAK,eAAeA,CAAI,CAClC,CACb,GACe,KAAK,eAAeA,CAAI,CAClC,CAID,IAAI,eAAgB,CAChB,OAAO,KAAK,cACf,CAKD,UAAW,CACP,OAAO,KAAK,eAAe,KAC9B,CACD,yBAAyBC,EAAKD,EAAM,CAChC,MAAO,GAAGC,EAAI,QAAQ,IAAID,CAAI,EACjC,CAOD,0BAA0BA,EAAME,EAAc,OAY1C,OAXArC,EAAA,KAAK,SAAL,MAAAA,EAAa,WAAW,CACpB,KAAM,KAAK,IAAK,EAChB,UAAW,KAAK,aAAc,EAC9B,SAAU,KAAK,SACf,OAAQ,oBACR,QAAS,CACL,KAAAmC,EACA,aAAAE,EACA,cAAe,KAAK,wBAAwBF,CAAI,CACnD,CACb,GACY,KAAK,0BAA0BA,CAAI,EAC5B,KAAK,wBAAwBA,CAAI,EAGjCE,CAEd,CAOD,0BAA0BF,EAAM9C,EAAO,QACnCW,EAAA,KAAK,SAAL,MAAAA,EAAa,WAAW,CACpB,KAAM,KAAK,IAAK,EAChB,UAAW,KAAK,aAAc,EAC9B,SAAU,KAAK,SACf,OAAQ,oBACR,QAAS,CAAE,KAAAmC,EAAM,MAAA9C,CAAO,CACpC,GACQ,KAAK,wBAAwB8C,CAAI,EAAI9C,CACxC,CAMD,6BAA6B8C,EAAM,QAC/BnC,EAAA,KAAK,SAAL,MAAAA,EAAa,WAAW,CACpB,KAAM,KAAK,IAAK,EAChB,UAAW,KAAK,aAAc,EAC9B,SAAU,KAAK,SACf,OAAQ,uBACR,QAAS,CAAE,KAAAmC,CAAM,CAC7B,GACQ,OAAO,KAAK,wBAAwBA,CAAI,CAC3C,CAOD,0BAA0BA,EAAM,CAC5B,OAAOA,KAAQ,KAAK,uBACvB,CAOD,sBAAsBG,EAAOH,EAAM9C,EAAO,CACtC,KAAK,oBAAoB,KAAK,yBAAyBiD,EAAOH,CAAI,CAAC,EAAI9C,CAC1E,CAOD,sBAAsBiD,EAAOH,EAAME,EAAc,CAC7C,OAAI,KAAK,sBAAsBC,EAAOH,CAAI,EAC/B,KAAK,oBAAoB,KAAK,yBAAyBG,EAAOH,CAAI,CAAC,EAGnEE,CAEd,CAOD,yBAAyBC,EAAOH,EAAM,CAClC,OAAO,KAAK,oBAAoB,KAAK,yBAAyBG,EAAOH,CAAI,CAAC,CAC7E,CAQD,sBAAsBG,EAAOH,EAAM,CAC/B,OAAO,KAAK,yBAAyBG,EAAOH,CAAI,IAAK,KAAK,mBAC7D,CAOD,oBAAoBI,EAAiB,CACjC,OAAOA,EAAgB,YAAY,KAAK,iBAC3C,CAOD,oBAAoBA,EAAiBlD,EAAO,OACxC,KAAK,kBAAkBkD,EAAgB,QAAQ,EAAIlD,GACnDW,EAAA,KAAK,SAAL,MAAAA,EAAa,WAAW,CACpB,KAAM,KAAK,IAAK,EAChB,UAAW,KAAK,aAAc,EAC9B,SAAU,KAAK,SACf,OAAQ,qBACR,QAAS,CACL,kBAAmBuC,EAAgB,SACnC,MAAAlD,CACH,CACb,EACK,CAOD,yBAAyBS,EAAKT,EAAO,CACjC,KAAK,kBAAkBS,CAAG,EAAIT,CACjC,CAOD,oBAAoBkD,EAAiB,OACjC,OAAAvC,EAAA,KAAK,SAAL,MAAAA,EAAa,WAAW,CACpB,KAAM,KAAK,IAAK,EAChB,UAAW,KAAK,aAAc,EAC9B,SAAU,KAAK,SACf,OAAQ,qBACR,QAAS,CACL,kBAAmBuC,EAAgB,SACnC,MAAO,KAAK,kBAAkBA,EAAgB,QAAQ,CACzD,CACb,GACe,KAAK,kBAAkBA,EAAgB,QAAQ,CACzD,CAOD,IAAI,eAAgB,CAChB,OAAO,KAAK,cACf,CAKD,IAAI,kBAAmB,CACnB,OAAO,KAAK,eAAe,OAAS,CACvC,CAMD,iBAAiBD,EAAO,CAEhB,KAAK,eAAe,SAASA,CAAK,IAGtC,KAAK,eAAe,KAAKA,CAAK,EAE9B,KAAK,eAAe,KAAK,CAACd,EAAGgB,IAAMhB,EAAE,SAAWgB,EAAE,QAAQ,EAC7D,CAMD,oBAAoBF,EAAO,CACvB,MAAMhB,EAAQ,KAAK,eAAe,QAAQgB,CAAK,EAC3ChB,IAAU,IACV,KAAK,eAAe,OAAOA,EAAO,CAAC,CAE1C,CAKD,qBAAsB,CAClB,UAAWgB,KAAS,KAAK,eACrBA,EAAM,oBAAoB,IAAI,EAElC,KAAK,eAAe,OAAS,CAChC,CAMD,mBAAmBG,EAAM,OACrB,KAAK,yBAAyB,gBAAgBA,CAAI,GAClDzC,EAAA,KAAK,SAAL,MAAAA,EAAa,WAAW,CACpB,KAAM,KAAK,IAAK,EAChB,UAAWyC,EAAK,aAAc,EAC9B,SAAUA,EAAK,SACf,OAAQ,cACpB,EACK,CACD,cAAcC,EAAc,OAExB,KAAK,0BAA0B,iBAAkBA,EAAa,cAAc,EAC5E,KAAK,0BAA0B,YAAaA,EAAa,SAAS,EAElE,UAAWJ,KAAS,KAAK,gBACrBtC,EAAAsC,EAAM,iBAAN,MAAAtC,EAAA,KAAAsC,EAAuB,KAE9B,CAID,sBAAuB,CACnB,KAAK,cACR,CAKD,IAAI,aAAc,CACd,OAAO,KAAK,YACf,CAMD,UAAUvC,EAAsB,GAAI4C,EAA6B9C,GAAmC,OAChGE,EAAoB,SAAW,KAAK,SACpCA,EAAoB,eAAiB,GACrC,UAAWD,KAAO,KAAK,eACnB6C,EAA2B7C,EAAK,KAAK,eAAeA,CAAG,EAAGC,EAAoB,cAAc,EAEhGA,EAAoB,kBAAoB,GACxC,UAAWD,KAAO,KAAK,kBACnB6C,EAA2B7C,EAAK,KAAK,kBAAkBA,CAAG,EAAGC,EAAoB,iBAAiB,EAGlG,KAAK,gBAAkB,KAAK,SAAQ,IACpCA,EAAoB,eAAiB,CACjC,OAAQ,KAAK,cAAc,OAAO,IAAKQ,GAAMA,EAAE,EAAE,EACjD,UAAW,KAAK,cAAc,UAAU,IAAKA,GAAMA,EAAE,EAAE,EACvD,SAAU,KAAK,cAAc,SAAS,IAAKA,GAAMA,EAAE,IAAI,EACvD,WAAY,KAAK,cAAc,WAAW,IAAKA,GAAMA,EAAE,IAAI,EAC3D,OAAQ,KAAK,cAAc,OAAO,IAAKA,GAAMA,EAAE,EAAE,EACjD,QAAS,KAAK,cAAc,QAAQ,IAAKA,GAAMA,EAAE,EAAE,EACnD,QAAQP,EAAA,KAAK,cAAc,SAAnB,YAAAA,EAA2B,IAAKO,GAAMA,EAAE,MAChD,UAAW,KAAK,cAAc,UAAU,IAAKA,GAAMA,EAAE,EAAE,EACvD,gBAAiB,KAAK,cAAc,gBAAgB,IAAKA,GAAMA,EAAE,IAAI,EACrE,WAAY,KAAK,cAAc,WAAW,IAAKA,GAAMA,EAAE,EAAE,EACzD,eAAgB,KAAK,cAAc,eAAe,IAAKA,GAAMA,EAAE,EAAE,EACjE,eAAgB,KAAK,cAAc,eAAe,IAAKA,GAAMA,EAAE,EAAE,CACjF,EAEK,CAID,cAAe,CACX,MAAO,kBACV,CACL,CACAqC,GAAW,CACPC,GAAW,CACf,EAAGd,GAAiB,UAAW,WAAY,MAAM,EClb1C,IAAIe,IACV,SAAUA,EAAyB,CAChCA,EAAwBA,EAAwB,MAAW,CAAC,EAAI,QAChEA,EAAwBA,EAAwB,OAAY,CAAC,EAAI,QACrE,GAAGA,KAA4BA,GAA0B,CAAE,EAAC,EAIrD,MAAMC,EAAoB,CAC7B,YAAYZ,EAAMa,EACFC,EAAa,CACzB,KAAK,YAAcA,EAEnB,KAAK,gBAAkB,GAIvB,KAAK,SAAWhB,IAOhB,KAAK,kBAAoB,GACzB,KAAK,KAAOE,EACZ,KAAK,gBAAkBa,CAC1B,CAID,IAAI,gBAAiB,CACjB,OAAO,KAAK,eACf,CAKD,uBAAwB,CACpB,MAAO,EACV,CAKD,aAAc,CACV,OAAO,KAAK,gBAAgB,OAAS,CACxC,CAKD,UAAUE,EAAO,CACb,GAAI,KAAK,kBAAoBA,EAAM,gBAC/B,MAAM,IAAI,MAAM,qCAAqC,KAAK,cAAc,EAAE,EAE9E,GAAK,KAAK,sBAAqB,GAAM,KAAK,gBAAgB,OAAS,GAAOA,EAAM,sBAAuB,GAAIA,EAAM,gBAAgB,OAAS,EACtI,MAAM,IAAI,MAAM,6CAA6C,EAEjE,KAAK,gBAAgB,KAAKA,CAAK,EAC/BA,EAAM,gBAAgB,KAAK,IAAI,CAClC,CAMD,eAAeA,EAAOC,EAAkB,GAAM,CAC1C,MAAMC,EAAa,KAAK,gBAAgB,QAAQF,CAAK,EAC/CG,EAAiBH,EAAM,gBAAgB,QAAQ,IAAI,EACrDE,IAAe,IAAMC,IAAmB,KAGxCF,GACA,KAAK,gBAAgB,OAAOC,EAAY,CAAC,EAE7CF,EAAM,gBAAgB,OAAOG,EAAgB,CAAC,EACjD,CAID,mBAAoB,CAChB,UAAWH,KAAS,KAAK,gBACrB,KAAK,eAAeA,EAAO,EAAK,EAEpC,KAAK,gBAAgB,OAAS,CACjC,CACD,SAAU,CACN,UAAWA,KAAS,KAAK,gBACrB,KAAK,eAAeA,CAAK,CAEhC,CAKD,UAAUnD,EAAsB,GAAI,CAChCA,EAAoB,SAAW,KAAK,SACpCA,EAAoB,KAAO,KAAK,KAChCA,EAAoB,gBAAkB,KAAK,gBAC3CA,EAAoB,kBAAoB,GACxCA,EAAoB,UAAY,KAAK,eACrC,UAAWmD,KAAS,KAAK,gBACrBnD,EAAoB,kBAAkB,KAAKmD,EAAM,QAAQ,CAEhE,CAID,cAAe,CACX,MAAO,cACV,CAKD,YAAYnD,EAAqB,CAC7B,KAAK,SAAWA,EAAoB,SACpC,KAAK,KAAOA,EAAoB,KAChC,KAAK,gBAAkBA,EAAoB,gBAC3C,KAAK,kBAAoBA,EAAoB,iBAChD,CACL,CCpHO,MAAMuD,UAAgCP,EAAoB,CAU7D,YAAYZ,EAAMoB,EAAgBC,EAIlCC,EAIAC,EAAgBD,EAAS,aAIzBE,EAAY,GAAO,CACf,MAAMxB,EAAMoB,EAAgBC,CAAU,EACtC,KAAK,SAAWC,EAChB,KAAK,cAAgBC,EACrB,KAAK,UAAYC,EACjB,KAAK,YAAc,GAKnB,KAAK,WAAa,KAKlB,KAAK,gBAAkB,KAIvB,KAAK,yBAA2B,IAAIzB,CACvC,CAKD,IAAI,UAAW,CACX,OAAO,KAAK,SACf,CAKD,IAAI,YAAa,CACb,OAAO,KAAK,WACf,CACD,IAAI,WAAW7C,EAAO,CACd,KAAK,cAAgBA,IAGzB,KAAK,YAAcA,EACf,KAAK,aACL,KAAK,kBAAiB,EAE7B,CAMD,uBAAwB,CACpB,OAAO,KAAK,iBAAmB,CAClC,CAMD,SAASA,EAAOuE,EAAS,CAEjBA,EAAQ,oBAAoB,IAAI,IAAMvE,IAG1CuE,EAAQ,oBAAoB,KAAMvE,CAAK,EACvC,KAAK,yBAAyB,gBAAgBA,CAAK,EACtD,CAKD,oBAAoBuE,EAAS,CACzBA,EAAQ,oBAAoB,KAAM,KAAK,aAAa,CACvD,CAKD,UAAUV,EAAO,CACT,KAAK,aAGT,MAAM,UAAUA,CAAK,CACxB,CACD,mBAAmBU,EAAS,CACxB,MAAM7C,EAAM6C,EAAQ,oBAAoB,IAAI,GAAK,KAAK,cACtD,OAAO,KAAK,gBAAkB,KAAK,gBAAgB7C,CAAG,EAAIA,CAC7D,CAMD,SAAS6C,EAAS,CACd,GAAI,KAAK,iBAAmB,EAAwC,CAChEA,EAAQ,mBAAmB,KAAK,WAAW,EAC3C,KAAK,YAAY,eAAeA,CAAO,EACvC,MAAMvE,EAAQ,KAAK,mBAAmBuE,CAAO,EAC7C,YAAK,WAAavE,EACX,KAAK,SAAS,gBAAkB,KAAK,SAAS,gBAAgBA,CAAK,EAAIA,CACjF,CACD,MAAMA,EAAS,KAAK,YAAa,EAAsC,KAAK,gBAAgB,CAAC,EAAE,SAASuE,CAAO,EAA3E,KAAK,mBAAmBA,CAAO,EACnE,YAAK,WAAavE,EACX,KAAK,SAAS,gBAAkB,KAAK,SAAS,gBAAgBA,CAAK,EAAIA,CACjF,CAID,eAAgB,CACZ,OAAO,KAAK,UACf,CAID,cAAe,CACX,MAAO,yBACV,CAKD,UAAUU,EAAsB,GAAI,CAChC,MAAM,UAAUA,CAAmB,EACnCA,EAAoB,SAAW,GAC/B,KAAK,SAAS,UAAUA,EAAoB,QAAQ,EACpDA,EAAoB,SAAW,KAAK,UACpCF,GAAkC,eAAgB,KAAK,cAAeE,CAAmB,CAC5F,CACL,CACA8D,GAAc,0BAA2BP,CAAuB,ECvJzD,MAAMQ,EAAe,CAKxB,YAIAC,EAAQ,OACJ,KAAK,OAASA,EAId,KAAK,SAAW9B,IAChB,KAAK,OAAOjC,EAAA,KAAK,SAAL,YAAAA,EAAa,OAAQ,KAAK,eACtC,KAAK,WAAa,GAClB,KAAK,YAAc,EACtB,CAMD,eAAegE,EAAU,CAExB,CAQD,kBAAkB7B,EAAMsB,EAAUpB,EAAc,CAC5C,MAAM4B,EAAQ,IAAIX,EAAwBnB,EAAM,EAAuC,KAAMsB,EAAUpB,CAAY,EACnH,YAAK,WAAW,KAAK4B,CAAK,EACnBA,CACV,CAQD,mBAAmB9B,EAAMsB,EAAUpB,EAAc,CAC7C,MAAM6B,EAAS,IAAIZ,EAAwBnB,EAAM,EAAwC,KAAMsB,EAAUpB,CAAY,EACrH,YAAK,YAAY,KAAK6B,CAAM,EACrBA,CACV,CAMD,aAAa/B,EAAM,CACf,OAAO,KAAK,WAAW,KAAML,GAAMA,EAAE,OAASK,CAAI,CACrD,CAMD,cAAcA,EAAM,CAChB,OAAO,KAAK,YAAY,KAAML,GAAMA,EAAE,OAASK,CAAI,CACtD,CAMD,UAAUpC,EAAsB,GAAIoE,EAA0BtE,GAAmC,CAG7F,GAFAE,EAAoB,SAAW,KAAK,SACpCA,EAAoB,OAAS,GACzB,KAAK,OAAQ,CACb,MAAMgE,EAAS,KAAK,OACpB,OAAO,KAAK,KAAK,MAAM,EAAE,QAASjE,GAAQ,CACtCqE,EAAwBrE,EAAKiE,EAAOjE,CAAG,EAAGC,EAAoB,MAAM,CACpF,CAAa,CACJ,CACDA,EAAoB,WAAa,GACjCA,EAAoB,YAAc,GAClCA,EAAoB,UAAY,KAAK,eACrC,UAAWkE,KAAS,KAAK,WAAY,CACjC,MAAMG,EAAkB,GACxBH,EAAM,UAAUG,CAAe,EAC/BrE,EAAoB,WAAW,KAAKqE,CAAe,CACtD,CACD,UAAWF,KAAU,KAAK,YAAa,CACnC,MAAMG,EAAmB,GACzBH,EAAO,UAAUG,CAAgB,EACjCtE,EAAoB,YAAY,KAAKsE,CAAgB,CACxD,CACJ,CAKD,YAAYC,EAAsB,CAEjC,CACD,KAAKV,EAAS/B,EAAQ0C,EAAS,QAC3BvE,EAAA4D,EAAQ,SAAR,MAAA5D,EAAgB,WAAW,CACvB,OAAA6B,EACA,QAAA0C,EACA,UAAW,KAAK,aAAc,EAC9B,SAAU,KAAK,QAC3B,EACK,CAKD,cAAe,CACX,MAAO,gBACV,CACL,CCtHO,MAAMC,UAAkCzB,EAAoB,CAC/D,aAAc,CACV,MAAM,GAAG,SAAS,EAKlB,KAAK,SAAW,CACnB,CACD,uBAAwB,CACpB,MAAO,EACV,CACD,UAAUG,EAAO,CACb,MAAM,UAAUA,CAAK,EAErB,KAAK,gBAAgB,KAAK,CAAC1B,EAAGgB,IAAMA,EAAE,SAAWhB,EAAE,QAAQ,CAC9D,CAID,gBAAgBoC,EAAS,OAUrB,IATA5D,EAAA4D,EAAQ,SAAR,MAAA5D,EAAgB,WAAW,CACvB,OAAQ,iBACR,UAAW,KAAK,YAAY,aAAc,EAC1C,SAAU,KAAK,YAAY,SAC3B,QAAS,CACL,eAAgB,KAAK,eACrB,KAAM,KAAK,IACd,CACb,GACY,KAAK,iBAAmB,EACxB4D,EAAQ,mBAAmB,KAAK,WAAW,EAC3C,KAAK,YAAY,SAASA,EAAS,IAAI,EACvCA,EAAQ,qBAAoB,MAG5B,WAAWa,KAAkB,KAAK,gBAC9BA,EAAe,gBAAgBb,CAAO,CAGjD,CACL,CACAC,GAAc,4BAA6BW,CAAyB,EC3C7D,MAAME,UAAgCZ,EAAe,CACxD,YAAYC,EAAQ,CAChB,MAAMA,CAAM,EAKZ,KAAK,SAAW,EAChB,KAAK,aAAe,GACpB,KAAK,cAAgB,GACrB,KAAK,GAAK,KAAK,qBAAqB,IAAI,EACxC,KAAK,MAAQ,KAAK,sBAAsB,OAAO,CAClD,CACD,qBAAqB5B,EAAM,CACvB,MAAM8B,EAAQ,IAAIO,EAA0BrC,EAAM,EAAuC,IAAI,EAC7F,YAAK,aAAa,KAAK8B,CAAK,EACrBA,CACV,CACD,sBAAsB9B,EAAM,CACxB,MAAM+B,EAAS,IAAIM,EAA0BrC,EAAM,EAAwC,IAAI,EAC/F,YAAK,cAAc,KAAK+B,CAAM,EACvBA,CACV,CACD,uBAAuB/B,EAAM,CACzB,MAAMb,EAAQ,KAAK,aAAa,UAAW2C,GAAUA,EAAM,OAAS9B,CAAI,EACpEb,IAAU,KACV,KAAK,aAAaA,CAAK,EAAE,QAAO,EAChC,KAAK,aAAa,OAAOA,EAAO,CAAC,EAExC,CACD,wBAAwBa,EAAM,CAC1B,MAAMb,EAAQ,KAAK,cAAc,UAAW4C,GAAWA,EAAO,OAAS/B,CAAI,EACvEb,IAAU,KACV,KAAK,cAAcA,CAAK,EAAE,QAAO,EACjC,KAAK,cAAc,OAAOA,EAAO,CAAC,EAEzC,CACD,aAAasC,EAASe,EAAO,CACzB,KAAK,MAAM,QAAU,OAAOA,GAAU,SAAW,IAAI,MAAMA,CAAK,EAAIA,EACpE,KAAK,MAAM,gBAAgBf,CAAO,CACrC,CAMD,eAAezB,EAAM,CACjB,OAAO,KAAK,aAAa,KAAM8B,GAAUA,EAAM,OAAS9B,CAAI,CAC/D,CAMD,gBAAgBA,EAAM,CAClB,OAAO,KAAK,cAAc,KAAM+B,GAAWA,EAAO,OAAS/B,CAAI,CAClE,CAKD,UAAUpC,EAAsB,GAAI,CAChC,MAAM,UAAUA,CAAmB,EACnCA,EAAoB,aAAe,GACnCA,EAAoB,cAAgB,GACpC,UAAWkE,KAAS,KAAK,aAAc,CACnC,MAAMG,EAAkB,GACxBH,EAAM,UAAUG,CAAe,EAC/BrE,EAAoB,aAAa,KAAKqE,CAAe,CACxD,CACD,UAAWF,KAAU,KAAK,cAAe,CACrC,MAAMG,EAAmB,GACzBH,EAAO,UAAUG,CAAgB,EACjCtE,EAAoB,cAAc,KAAKsE,CAAgB,CAC1D,CACJ,CAKD,YAAYtE,EAAqB,CAC7B,QAAS+B,EAAI,EAAGA,EAAI/B,EAAoB,aAAa,OAAQ+B,IAAK,CAC9D,MAAM8C,EAAc,KAAK,eAAe7E,EAAoB,aAAa+B,CAAC,EAAE,IAAI,EAChF,GAAI8C,EACAA,EAAY,YAAY7E,EAAoB,aAAa+B,CAAC,CAAC,MAG3D,OAAM,IAAI,MAAM,yCAA2C/B,EAAoB,aAAa+B,CAAC,EAAE,KAAO,aAAe/B,EAAoB,SAAS,CAEzJ,CACD,QAAS+B,EAAI,EAAGA,EAAI/B,EAAoB,cAAc,OAAQ+B,IAAK,CAC/D,MAAM+C,EAAe,KAAK,gBAAgB9E,EAAoB,cAAc+B,CAAC,EAAE,IAAI,EACnF,GAAI+C,EACAA,EAAa,YAAY9E,EAAoB,cAAc+B,CAAC,CAAC,MAG7D,OAAM,IAAI,MAAM,0CAA4C/B,EAAoB,cAAc+B,CAAC,EAAE,KAAO,aAAe/B,EAAoB,SAAS,CAE3J,CACJ,CAID,cAAe,CACX,MAAO,yBACV,CACL,CCzGO,MAAM+E,EAA+B,CACxC,YAAY3E,EAAO,CAIf,KAAK,2BAA6B,IAAI+B,EAItC,KAAK,oBAAsB,GAC3B,KAAK,uBAAyB,GAC9B,KAAK,cAAgB,EACrB,KAAK,OAAS/B,EACd,KAAK,YAAW,CACnB,CACD,aAAc,CACV,KAAK,oBAAsB,KAAK,OAAO,kBAAkB,IAAI,IAAM,CAC1D,KAAK,sBACN,KAAK,2BAA2B,gBAAgB,CAAE,KAAM,YAAkD,GAC1G,KAAK,oBAAsB,GAE3C,CAAS,EACD,KAAK,sBAAwB,KAAK,OAAO,oBAAoB,IAAI,IAAM,CACnE,KAAK,2BAA2B,gBAAgB,CAAE,KAAM,cAAsD,EAC1H,CAAS,EACD,KAAK,6BAA+B,KAAK,OAAO,yBAAyB,IAAI,IAAM,CAC/E,MAAM4E,EAAY,KAAK,OAAO,UAAS,EAAG,aAAc,EAAG,IAC3D,KAAK,2BAA2B,gBAAgB,CAC5C,KAAM,oBACN,QAAS,CACL,eAAgB,KAAK,cACrB,UAAAA,CACH,CACjB,CAAa,EACD,KAAK,eAAiBA,CAClC,CAAS,EACD,KAAK,oBAAsB,KAAK,OAAO,oBAAoB,IAAKC,GAAgB,CAC5E,KAAK,2BAA2B,gBAAgB,CAAE,KAAM,WAA8C,QAASA,CAAW,CAAE,CACxI,EAAWC,GAAkB,WAAW,EAChC,KAAK,0BAA4B,KAAK,OAAO,oCAAoC,IAAKC,GAAS,CAK3F,MAAMC,EAAYD,EAAK,UACjBE,EAAOF,EAAK,KACZG,EAAgB,KAAK,uBAAuBF,CAAS,EACvD,CAACE,GAAiBD,EAClB,KAAK,2BAA2B,gBAAgB,CAAE,KAAM,cAAoD,QAAS,CAAE,UAAAD,EAAW,KAAAC,CAAM,EAAE,EAErIC,GAAiB,CAACD,EACvB,KAAK,2BAA2B,gBAAgB,CAAE,KAAM,aAAkD,QAAS,CAAE,UAAAD,EAAW,KAAME,CAAa,CAAI,GAElJA,GAAiBD,GAAQC,IAAkBD,IAChD,KAAK,2BAA2B,gBAAgB,CAAE,KAAM,aAAkD,QAAS,CAAE,UAAAD,EAAW,KAAME,EAAe,KAAMD,CAAI,CAAI,GACnK,KAAK,2BAA2B,gBAAgB,CAAE,KAAM,cAAoD,QAAS,CAAE,UAAAD,EAAW,KAAAC,EAAM,IAAKC,CAAa,CAAI,IAElK,KAAK,uBAAuBF,CAAS,EAAIC,CACrD,EAAWH,GAAkB,WAAW,CACnC,CACD,SAAU,gBACNjF,EAAA,KAAK,wBAAL,MAAAA,EAA4B,UAC5BsF,EAAA,KAAK,sBAAL,MAAAA,EAA0B,UAC1BC,EAAA,KAAK,+BAAL,MAAAA,EAAmC,UACnCC,EAAA,KAAK,sBAAL,MAAAA,EAA0B,UAC1BC,EAAA,KAAK,4BAAL,MAAAA,EAAgC,SAChC,KAAK,2BAA2B,OACnC,CACL,CCpEO,SAASC,GAAiBC,EAAOC,EAAO,CAC3C,MAAO,CAAC,EAAED,EAAM,SAAWA,EAAM,SAAWC,GAASF,GAAiBC,EAAM,OAAQC,CAAK,GAC7F,CAIO,SAASC,GAAgBC,EAAG,CAC/B,GAAIA,EAAE,aACF,OAAOA,EAAE,cAGjB,CAQO,SAASC,GAAoB/G,EAAWgH,EAAY,CACvD,OAAOhH,IAAcgH,IAAehH,IAAc,WAA0CA,IAAc,WAA0CA,IAAc,UACtK,CAQO,SAASiH,GAAoBjH,EAAWgH,EAAY,CACvD,OAAOhH,IAAcgH,IAAehH,IAAc,UAAwCA,IAAc,YAA4CA,IAAc,WACtK,CAQO,SAASkH,GAAqBlH,EAAWgH,EAAY,CACxD,OAAOhH,IAAc,oBAAsBgH,IAAe,kBAC9D,CAOO,SAASG,GAAU3E,EAAG4E,EAAY,CACrC,MAAMD,EAAY,OAAO3E,GAAM,UAAY,OAAOA,GAAA,YAAAA,EAAG,QAAU,SAC/D,OAAI2E,GAAa,CAACC,EACP,CAAC,MAAMC,GAAgB7E,CAAC,CAAC,EAE7B2E,CACX,CAMO,SAASE,GAAgB7E,EAAG,CAC/B,OAAO,OAAOA,GAAM,SAAWA,EAAIA,EAAE,KACzC,CChEO,IAAI8E,IACV,SAAUA,EAAgB,CAIvBA,EAAeA,EAAe,QAAa,CAAC,EAAI,UAIhDA,EAAeA,EAAe,QAAa,CAAC,EAAI,SACpD,GAAGA,KAAmBA,GAAiB,CAAE,EAAC,EASnC,MAAMC,EAAU,CAInB,IAAI,OAAQ,CACR,OAAO,KAAK,MACf,CAID,IAAI,MAAMlH,EAAO,CACb,KAAK,OAASA,EACd,KAAK,yBAAyB,gBAAgBA,CAAK,CACtD,CAKD,YAAY2C,EAAQ,CAIhB,KAAK,yBAA2B,IAAIE,EAEpC,KAAK,aAAe,CACf,WAAmD,CAAE,EACrD,aAAuD,CAAE,EACzD,kBAAiE,CAAE,EACnE,SAA+C,CAAE,EACjD,YAAqD,CAAE,EACvD,UAAiD,CAAE,EACnD,YAAqD,CAAE,EACvD,YAAqD,CAAE,EACvD,WAAmD,CAAE,EACrD,iBAA+D,CAAE,EACjE,UAAiD,CAAE,CAChE,EACQ,KAAK,mBAAqB,GAI1B,KAAK,OAAS,EACd,KAAK,OAASF,EAAO,MACrB,KAAK,uBAAyB,IAAI8C,GAA+B,KAAK,MAAM,EAC5E,KAAK,aAAe9C,EAAO,YAC3B,KAAK,eAAiB,KAAK,uBAAuB,2BAA2B,IAAKwE,GAAU,CACxF,UAAW5C,KAAW,KAAK,mBAAoB,CAC3C,MAAM6C,EAAQ,KAAK,oBAAoBD,EAAM,KAAM5C,CAAO,EAC1D,UAAWtB,KAASmE,EAEhB,GAAI,CAACnE,EAAM,cAAcsB,EAAS4C,EAAM,OAAO,EAC3C,KAGX,CAED,OAAQA,EAAM,KAAI,CACd,IAAK,aACD,KAAK,uBAAuB,oBAAsB,GAClD,MACJ,IAAK,oBACD,UAAW5C,KAAW,KAAK,mBACvBA,EAAQ,cAAc4C,EAAM,OAAO,EAEvC,MACJ,IAAK,eACD,KAAK,QAAO,EACZ,KACP,CACb,CAAS,CACJ,CAKD,eAAgB,CACZ,MAAM5C,EAAU,IAAI7B,GAAiB,CAAE,MAAO,KAAK,OAAQ,YAAa,KAAK,YAAY,CAAE,EAC3F,YAAK,mBAAmB,KAAK6B,CAAO,EAC7BA,CACV,CAMD,WAAWtC,EAAO,CACd,OAAO,KAAK,mBAAmBA,CAAK,CACvC,CAMD,cAAcgB,EAAO,CASjB,IARIA,EAAM,OAAS,eAAsDA,EAAM,OAAS,gBACpF,KAAK,OAAO,iCAAmC,IAG/CA,EAAM,OAAS,aACf,KAAK,aAAaA,EAAM,IAAI,EAAE,KAAKA,CAAK,EAGxC,KAAK,QAAU,EACf,UAAWsB,KAAW,KAAK,mBACvBtB,EAAM,mBAAmBsB,CAAO,OAIpC,KAAK,yBAAyB,QAAS8C,GAAU,CAC7C,GAAIA,IAAU,EACV,UAAW9C,KAAW,KAAK,mBACvBtB,EAAM,mBAAmBsB,CAAO,CAGxD,CAAa,CAER,CAID,OAAQ,CACA,KAAK,QAAU,IAGf,KAAK,mBAAmB,SAAW,GACnC,KAAK,cAAa,EAEtB,KAAK,yBAAyB,IAAK8C,GAAU,CACrCA,IAAU,IACV,KAAK,oBAAmB,EAEpB,KAAK,OAAO,QAAQ,EAAI,GACxB,KAAK,uBAAuB,2BAA2B,gBAAgB,CAAE,KAAM,YAAY,CAAsC,EAGrJ,CAAS,EACD,KAAK,MAAQ,EAChB,CACD,qBAAsB,CAClB,UAAW9C,KAAW,KAAK,mBACvB,UAAWvC,KAAQ,KAAK,aAAc,CAClC,MAAMoF,EAAQ,KAAK,oBAAoBpF,EAAMuC,CAAO,EACpD,UAAWtB,KAASmE,EAChBnE,EAAM,mBAAmBsB,CAAO,CAEvC,CAER,CACD,oBAAoBvC,EAAMuC,EAAS,CAC/B,MAAM6C,EAAQ,KAAK,aAAapF,CAAI,EAAE,KAAK,CAACG,EAAGgB,IAAMA,EAAE,aAAehB,EAAE,YAAY,EACpF,GAAIH,IAAS,WAA8C,CACvD,MAAMsF,EAAgB,GACtB,UAAWC,KAAUH,EAAO,CAExB,MAAMd,EAAQiB,EAAO,MAAM,SAAShD,CAAO,EAC3C,IAAI9B,EAAI,EACR,KAAOA,EAAI2E,EAAM,OAAQ3E,IAAK,CAE1B,MAAM8D,EADSa,EAAM3E,CAAC,EACD,MAAM,SAAS8B,CAAO,EAC3C,GAAI+B,GAASC,GAASF,GAAiBC,EAAOC,CAAK,EAC/C,KAEP,CACDe,EAAc,OAAO7E,EAAG,EAAG8E,CAAM,CACpC,CACD,OAAOD,CACV,CACD,OAAOF,CACV,CAID,SAAU,OACN,GAAI,KAAK,QAAU,EAGnB,MAAK,MAAQ,EACb,UAAW7C,KAAW,KAAK,mBACvBA,EAAQ,oBAAmB,EAE/B,KAAK,mBAAmB,OAAS,EACjC,UAAWvC,KAAQ,KAAK,aACpB,KAAK,aAAaA,CAAI,EAAE,OAAS,GAErCrB,EAAA,KAAK,iBAAL,MAAAA,EAAqB,SACrB,KAAK,uBAAuB,UAC/B,CAKD,eAAe6G,EAAS,CACpB,MAAMC,EAAY,GACZC,EAAsB,IAAI,IAChC,UAAW1F,KAAQ,KAAK,aACpB,UAAWiB,KAAS,KAAK,aAAajB,CAAI,EACtCyF,EAAU,KAAKxE,CAAK,EACpByE,EAAoB,IAAIzE,EAAM,QAAQ,EAG9C,KAAOwE,EAAU,OAAS,GAAG,CACzB,MAAMxE,EAAQwE,EAAU,MACxBD,EAAQvE,CAAK,EACb,UAAW0E,KAAU1E,EAAM,WACvB,UAAW2E,KAAcD,EAAO,gBACvBD,EAAoB,IAAIE,EAAW,YAAY,QAAQ,IACxDH,EAAU,KAAKG,EAAW,WAAW,EACrCF,EAAoB,IAAIE,EAAW,YAAY,QAAQ,GAInE,GAAI3E,aAAiBoC,EACjB,UAAWwC,KAAa5E,EAAM,cAC1B,UAAW2E,KAAcC,EAAU,gBAC1BH,EAAoB,IAAIE,EAAW,YAAY,QAAQ,IACxDH,EAAU,KAAKG,EAAW,WAAW,EACrCF,EAAoB,IAAIE,EAAW,YAAY,QAAQ,EAK1E,CACJ,CAMD,UAAUlH,EAAsB,CAAE,EAAEoH,EAAwB,CACxDpH,EAAoB,UAAY,GAChC,KAAK,eAAgBuC,GAAU,CAC3B,MAAM8E,EAAkB,GACxB9E,EAAM,UAAU8E,CAAe,EAC/BrH,EAAoB,UAAU,KAAKqH,CAAe,CAC9D,CAAS,EACDrH,EAAoB,kBAAoB,GACxC,UAAW6D,KAAW,KAAK,mBAAoB,CAC3C,MAAMyD,EAAoB,GAC1BzD,EAAQ,UAAUyD,EAAmBF,CAAsB,EAC3DpH,EAAoB,kBAAkB,KAAKsH,CAAiB,CAC/D,CACJ,CACL,CCjQO,MAAMC,CAAqB,CAC9B,YAIAvD,EAAQ,CACJ,KAAK,OAASA,EAKd,KAAK,4BAA8B,GACnC,KAAK,YAAc,GACnB,KAAK,iBAAmB,IAAI,IAC5B,KAAK,uBAAyB,IAAI,IAClC,KAAK,oBAAsB,GAC3B,KAAK,eAAiB,EAEtB,KAAK,iBAAmB,KAAK,OAAO,MAAM,oBAAoB,IAAI,IAAM,CACpE,KAAK,QAAO,CACxB,CAAS,EACD,KAAK,wBAA0B,KAAK,OAAO,MAAM,yBAAyB,IAAI,IAAM,CAEhF,KAAK,uBAAuB,QAE5B,MAAMwD,EAAqB,KAAK,oBAAoB,MAAM,CAAC,EACvDA,EAAmB,QAEnBA,EAAmB,QAASf,GAAU,CAClC,KAAK,kBAAkBA,EAAM,GAAIA,EAAM,KAAM,EAAK,EAElD,MAAMlF,EAAQ,KAAK,oBAAoB,UAAWkG,GAAMA,EAAE,WAAahB,EAAM,QAAQ,EACjFlF,IAAU,IACV,KAAK,oBAAoB,OAAOA,EAAO,CAAC,CAEhE,CAAiB,CAEjB,CAAS,GAEoBgG,EAAqB,kBAAkB,IAAI,KAAK,OAAO,KAAK,GAAK,IACzE,KAAK,IAAI,CACzB,CAKD,aAAc,CACV,MAAMG,EAAQ,IAAIlB,GAAU,CAAE,MAAO,KAAK,OAAO,MAAO,YAAa,IAAI,CAAE,EAC3E,YAAK,YAAY,KAAKkB,CAAK,EACpBA,CACV,CAKD,YAAYA,EAAO,CACf,MAAMnG,EAAQ,KAAK,YAAY,QAAQmG,CAAK,EACxCnG,IAAU,KACVmG,EAAM,QAAO,EACb,KAAK,YAAY,OAAOnG,EAAO,CAAC,EAEvC,CAID,OAAQ,CACJ,KAAK,YAAY,QAASmG,GAAUA,EAAM,MAAK,CAAE,CACpD,CAID,SAAU,SACN,KAAK,YAAY,QAASA,GAAUA,EAAM,QAAO,CAAE,EACnD,KAAK,YAAY,OAAS,GAC1BzH,EAAA,KAAK,mBAAL,MAAAA,EAAuB,UACvBsF,EAAA,KAAK,0BAAL,MAAAA,EAA8B,SAE9B,MAAMoC,EAAeJ,EAAqB,kBAAkB,IAAI,KAAK,OAAO,KAAK,GAAK,GAChFhG,EAAQoG,EAAa,QAAQ,IAAI,EACnCpG,IAAU,IACVoG,EAAa,OAAOpG,EAAO,CAAC,CAEnC,CAMD,UAAUvB,EAAqBoH,EAAwB,CACnDpH,EAAoB,YAAc,GAClC,KAAK,YAAY,QAAS0H,GAAU,CAChC,MAAME,EAAkB,GACxBF,EAAM,UAAUE,EAAiBR,CAAsB,EACvDpH,EAAoB,YAAY,KAAK4H,CAAe,CAChE,CAAS,EACD5H,EAAoB,4BAA8B,KAAK,2BAC1D,CAID,IAAI,YAAa,CACb,OAAO,KAAK,WACf,CAMD,yBAAyB6H,EAAI,CACzB,IAAIC,EAAa,KAAK,iBAAiB,IAAID,CAAE,EAC7C,OAAKC,IAEDA,EAAa,IAAI3F,EACjB,KAAK,iBAAiB,IAAI0F,EAAIC,CAAU,GAErCA,CACV,CAOD,kBAAkBD,EAAI1C,EAAM4C,EAAQ,CAAC,KAAK,4BAA6B,CACnE,GAAIA,EAAO,CACP,KAAK,oBAAoB,KAAK,CAAE,GAAAF,EAAI,KAAA1C,EAAM,SAAU,KAAK,gBAAgB,CAAE,EAC3E,MACH,CAED,GAAI,KAAK,uBAAuB,IAAI0C,CAAE,EAAG,CACrC,MAAMG,EAAQ,KAAK,uBAAuB,IAAIH,CAAE,EAEhD,GADA,KAAK,uBAAuB,IAAIA,EAAIG,EAAQ,CAAC,EACzCA,GAAST,EAAqB,8BAA+B,CAC7DS,IAAUT,EAAqB,+BAAiC1F,EAAO,KAAK,uDAAuDgG,CAAE,IAAI,EACzI,MACH,CACJ,MAEG,KAAK,uBAAuB,IAAIA,EAAI,CAAC,EAEzC,MAAMC,EAAa,KAAK,iBAAiB,IAAID,CAAE,EAC3CC,GACAA,EAAW,gBAAgB3C,CAAI,CAEtC,CACL,CAMAoC,EAAqB,iBAAmB,GAIxCA,EAAqB,8BAAgC,GAKrDA,EAAqB,kBAAoB,IAAI,ICtK7C,MAAMU,EAAe,GAQd,SAASC,GAAkBC,EAAQC,EAAWC,EAAS,CAC1DJ,EAAa,GAAGE,CAAM,IAAIC,CAAS,EAAE,EAAIC,CAC7C,CAMO,SAASC,GAAaF,EAAW,CACpC,OAAQA,EAAS,CACb,IAAK,8BACD,MAAO,WAAa,MAAMG,EAAA,WAAO,2CAAsD,mDAAG,4BAC9F,IAAK,8BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,2CAAsD,+CAAG,4BAC9F,IAAK,+BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,4CAAuD,+CAAG,6BAC/F,IAAK,8BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,2CAAsD,+CAAG,4BAC9F,IAAK,gCACD,MAAO,WAAa,MAAKA,EAAA,IAAC,OAAO,6CAA0C,6CAAG,8BAClF,IAAK,+BACD,MAAO,WAAa,MAAKA,EAAA,IAAC,OAAO,4CAAyC,gDAAG,6BACjF,IAAK,gCACD,MAAO,WAAa,MAAKA,EAAA,IAAC,OAAO,6CAA0C,8CAAG,8BAClF,IAAK,mCACD,MAAO,WAAa,MAAKA,EAAA,IAAC,OAAO,gDAA6C,8CAAG,iCACrF,IAAK,8BACD,MAAO,WAAa,MAAKA,EAAA,IAAC,OAAO,2CAAwC,gDAAG,4BAChF,IAAK,kBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,gBAC5E,IAAK,mBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,iBAC5E,IAAK,oBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,kBAC5E,IAAK,oBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,kBAC5E,IAAK,uBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,qBAC5E,IAAK,oBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,kBAC5E,IAAK,yBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,uBAC5E,IAAK,yBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,uBAC5E,IAAK,uBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,qBAC5E,IAAK,oBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,kBAC5E,IAAK,qBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,mBAC5E,IAAK,sBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,oBAC5E,IAAK,sBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,oBAC5E,IAAK,qBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,mBAC5E,IAAK,sBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,oBAC5E,IAAK,sBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,uBAC5E,IAAK,yBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,uBAC5E,IAAK,uBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,qBAC5E,IAAK,oBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,kBAC5E,IAAK,oBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,kBAC5E,IAAK,sBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,oBAC5E,IAAK,yBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,uBAC5E,IAAK,kCACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,gCAC5E,IAAK,yBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,uBAC5E,IAAK,yBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,uBAC5E,IAAK,gCACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,8BAC5E,IAAK,4BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,0BAC5E,IAAK,mCACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,iCAC5E,IAAK,sBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,oBAC5E,IAAK,sBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,yBAC5E,IAAK,yBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,uBAC5E,IAAK,yBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,uBAC5E,IAAK,oBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,kBAC5E,IAAK,oBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,kBAC5E,IAAK,oBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,kBAC5E,IAAK,qBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,mBAC5E,IAAK,qBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,mBAC5E,IAAK,qBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,mBAC5E,IAAK,sBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,oBAC5E,IAAK,qBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,mBAC5E,IAAK,qBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,mBAC5E,IAAK,qBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,mBAC5E,IAAK,sBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,oBAC5E,IAAK,sBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,oBAC5E,IAAK,sBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,oBAC5E,IAAK,4BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,kBAC5E,IAAK,oBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,kBAC5E,IAAK,qBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,mBAC5E,IAAK,sBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,oBAC5E,IAAK,2BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,yBAC5E,IAAK,sBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,oBAC5E,IAAK,yBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,uBAC5E,IAAK,2BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,yBAC5E,IAAK,0BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,wBAC5E,IAAK,2BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,yBAC5E,IAAK,2BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,yBAC5E,IAAK,iCACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,+BAC5E,IAAK,kCACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,gCAC5E,IAAK,uBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAA0C,4DAAG,qBAClF,IAAK,0BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAA0C,4DAAG,wBAClF,IAAK,oBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAA0C,4DAAG,kBAClF,IAAK,sBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAA0C,4DAAG,oBAClF,IAAK,yBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAA0C,4DAAG,uBAClF,IAAK,yBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAA0C,4DAAG,uBAClF,IAAK,0BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAA0C,yDAAG,wBAClF,IAAK,4BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAA0C,yDAAG,0BAClF,IAAK,6BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAA0C,yDAAG,2BAClF,IAAK,qCACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAA0C,yDAAG,mCAClF,IAAK,uBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,oCAAiD,gDAAG,qBACzF,IAAK,yBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,sCAAmD,gDAAG,uBAC3F,IAAK,4BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAAsD,gDAAG,0BAC9F,IAAK,4BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,qCAAkD,gDAAG,0BAC1F,IAAK,yBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,sCAAmD,gDAAG,uBAC3F,IAAK,yBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,sCAAmD,gDAAG,uBAC3F,IAAK,oBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,iCAA8C,gDAAG,kBACtF,IAAK,yBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,sCAAmD,gDAAG,uBAC3F,IAAK,wBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,qCAAkD,gDAAG,sBAC1F,IAAK,0BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,uCAAoD,gDAAG,wBAC5F,IAAK,yBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,sCAAmD,8CAAG,uBAC3F,IAAK,uBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,oCAAiD,gDAAG,qBACzF,IAAK,wBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,qCAAkD,gDAAG,sBAC1F,IAAK,0BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,uCAAoD,gDAAG,wBAC5F,IAAK,2BACD,MAAO,WAAa,MAAKA,EAAA,IAAC,OAAO,wCAAyC,gDAAG,yBACjF,IAAK,4BACD,MAAO,WAAa,MAAKA,EAAA,IAAC,OAAO,6CAAyC,gDAAG,8BACjF,IAAK,yBACD,MAAO,WAAa,MAAKA,EAAA,IAAC,OAAO,sCAAkC,gDAAG,uBAC1E,IAAK,2CACD,MAAO,WAAa,MAAKA,EAAA,IAAC,OAAO,wDAAoD,gDAAG,yCAC5F,IAAK,yBACD,MAAO,WAAa,MAAKA,EAAA,IAAC,OAAO,sCAAkC,gDAAG,uBAC1E,IAAK,4BACD,MAAO,WAAa,MAAKA,EAAA,IAAC,OAAO,yCAAqC,mDAAG,0BAC7E,IAAK,4BACD,MAAO,WAAa,MAAKA,EAAA,IAAC,OAAO,yCAA0C,gDAAG,0BAClF,IAAK,4BACD,MAAO,WAAa,MAAKA,EAAA,IAAC,OAAO,yCAAqC,gDAAG,0BAC7E,IAAK,4BACD,MAAO,WAAa,MAAKA,EAAA,IAAC,OAAO,yCAA0C,gDAAG,0BAClF,IAAK,kCACD,MAAO,WAAa,MAAMA,EAAA,WAAO,+CAAwD,mDAAG,gCAChG,IAAK,6BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,2BAC5E,IAAK,8BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,4BAC5E,IAAK,+BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,mCAAoC,4DAAG,6BAC5E,IAAK,+BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,iDAAkD,mDAAG,6BAC1F,IAAK,+BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,iDAAkD,mDAAG,6BAC1F,IAAK,+BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,iDAAkD,mDAAG,6BAC1F,IAAK,8BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,iDAAkD,mDAAG,4BAC1F,IAAK,+BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,iDAAkD,mDAAG,6BAC1F,IAAK,+BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,iDAAkD,mDAAG,6BAC1F,IAAK,+BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,iDAAkD,mDAAG,6BAC1F,IAAK,8BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,iDAAkD,mDAAG,4BAC1F,IAAK,gCACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAA0C,4DAAG,wBAClF,IAAK,qCACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAA0C,4DAAG,mCAClF,IAAK,2BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAA0C,yDAAG,8BAClF,IAAK,yBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAA0C,yDAAG,4BAClF,IAAK,0BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAAkD,sDAAG,wBAC1F,IAAK,wBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAAkD,sDAAG,sBAC1F,IAAK,0BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAAkD,sDAAG,wBAC1F,IAAK,wBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAAkD,sDAAG,sBAC1F,IAAK,sBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAAkD,sDAAG,oBAC1F,IAAK,sBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,yCAAkD,sDAAG,oBAC1F,IAAK,uBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,oCAA+C,gDAAG,qBACvF,IAAK,6BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,+CAA0D,gDAAG,gCAClG,IAAK,iCACD,MAAO,WAAa,MAAKA,EAAA,IAAC,OAAO,8CAA2C,gDAAG,+BACnF,IAAK,gCACD,MAAO,WAAa,MAAKA,EAAA,IAAC,OAAO,6CAA0C,gDAAG,8BAClF,IAAK,wBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,qCAAuC,gDAAG,sBAC/E,IAAK,2BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,wCAA0C,gDAAG,yBAClF,IAAK,8BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,2CAA6C,gDAAG,4BACrF,IAAK,wBACD,MAAO,WAAa,MAAMA,EAAA,WAAO,qCAAuC,gDAAG,sBAC/E,IAAK,6BACD,MAAO,WAAa,MAAMA,EAAA,WAAO,+CAAiD,gDAAG,gCACzF,IAAK,2BACD,MAAO,WAAa,MAAKA,EAAA,IAAC,OAAO,wCAAoC,gDAAG,yBAC5E,QAEI,GAAIN,EAAaG,CAAS,EACtB,OAAOH,EAAaG,CAAS,EAEjC,MAAM,IAAI,MAAM,sBAAsBA,CAAS,EAAE,CACxD,CACL,CC9RO,MAAMI,WAA6C7D,CAAwB,CAC9E,YAAYX,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,IAAM,KAAK,sBAAsB,KAAK,CAC9C,CACL,CCNO,MAAMyE,WAAqCD,EAAqC,CACnF,YAAYxE,EAAQ0E,EAAQ,CACxB,MAAM1E,CAAM,EACZ,KAAK,qBAAuB,GAC5B,KAAK,KAAO,KAAK,sBAAsB,MAAM,EAC7C0E,GAAA,MAAAA,EAAQ,QAASC,GAAc,CAC3B,KAAK,qBAAqBA,CAAS,EAAI,KAAK,sBAAsBA,EAAY,OAAO,CACjG,EACK,CAQD,eAAe1E,EAAU,CAAG,CAK5B,mBAAmBJ,EAAS,CACpBA,EAAQ,sBAAsB,KAAM,eAAgB,EAAK,IACzD,KAAK,oBAAoBA,CAAO,EAChC,KAAK,oBAAoBA,CAAO,GAEpC,KAAK,qBAAqBA,CAAO,EACjCA,EAAQ,iBAAiB,IAAI,EAC7B,KAAK,IAAI,gBAAgBA,CAAO,EAChCA,EAAQ,sBAAsB,KAAM,eAAgB,EAAI,CAC3D,CACD,oBAAoBA,EAAS,CACzBA,EAAQ,yBAAyB,KAAM,cAAc,EACrDA,EAAQ,oBAAoB,IAAI,CACnC,CACL,CCnCO,MAAM+E,WAA4BH,EAA6B,CAClE,aAAc,CACV,MAAM,GAAG,SAAS,EAKlB,KAAK,aAAe,EAIpB,KAAK,KAAO,WACf,CAID,SAAS5E,EAAS,CACdA,EAAQ,mBAAmB,IAAI,EAC/B,KAAK,KAAK,gBAAgBA,CAAO,CACpC,CACL,CCbO,SAASgF,GAA+BC,EAAQC,EAAU,CAC7D,UAAWxG,KAASuG,EAChB,UAAWE,KAAWzG,EAAM,YACxB,GAAIyG,EAAQ,WAAaD,EACrB,OAAOC,EAInB,MAAM,IAAI,MAAM,qDAAuDD,CAAQ,CACnF,CAOO,SAASE,GAAgCH,EAAQC,EAAU,CAC9D,UAAWxG,KAASuG,EAChB,GAAIvG,aAAiBoC,GACjB,UAAWuE,KAAY3G,EAAM,aACzB,GAAI2G,EAAS,WAAaH,EACtB,OAAOG,EAKvB,MAAM,IAAI,MAAM,sDAAwDH,CAAQ,CACpF,CAgCO,eAAeI,GAAoBnJ,EAAqBoJ,EAAS,CAEpE,MAAMC,EAAkB,MAAM,QAAQ,IAAIrJ,EAAoB,UAAU,IAAI,MAAOqH,GAC1DiB,GAAajB,EAAgB,SAAS,EACxC,CACtB,CAAC,EAEF,OAAOiC,GAAetJ,EAAqBoJ,EAASC,CAAe,CACvE,CAQO,SAASC,GAAetJ,EAAqBoJ,EAASC,EAAiB,CAC1E,MAAM3B,EAAQ0B,EAAQ,YAAY,YAAW,EACvCN,EAAS,GACTS,EAAqBH,EAAQ,oBAAsBlJ,EAGzD,QAAS6B,EAAI,EAAGA,EAAI/B,EAAoB,UAAU,OAAQ+B,IAAK,CAC3D,MAAMsF,EAAkBrH,EAAoB,UAAU+B,CAAC,EACjDQ,EAAQiH,GAAiCnC,EAAiB,CAAE,MAAO+B,EAAQ,YAAY,OAAO,MAAO,cAAeA,EAAQ,cAAe,gBAAiBA,EAAQ,YAAY,OAAO,MAAO,mBAAAG,GAAsBF,EAAgBtH,CAAC,CAAC,EAC5O+G,EAAO,KAAKvG,CAAK,EACbA,aAAiBqG,IACjBlB,EAAM,cAAcnF,CAAK,CAEhC,CAED,UAAWA,KAASuG,EAAQ,CACxB,UAAW7B,KAAU1E,EAAM,WACvB,UAAWkH,KAAwBxC,EAAO,kBAAmB,CACzD,MAAMC,EAAa2B,GAA+BC,EAAQW,CAAoB,EAC9ExC,EAAO,UAAUC,CAAU,CAC9B,CAEL,GAAI3E,aAAiBoC,EACjB,UAAWwC,KAAa5E,EAAM,cAC1B,UAAWkH,KAAwBtC,EAAU,kBAAmB,CAC5D,MAAMD,EAAa+B,GAAgCH,EAAQW,CAAoB,EAC/EtC,EAAU,UAAUD,CAAU,CACjC,CAGZ,CACD,UAAWI,KAAqBtH,EAAoB,kBAChD0J,GAAsBpC,EAAmB,CAAE,MAAAI,EAAO,mBAAA6B,CAAkB,EAAIvJ,EAAoB,WAAW,EAE3G,OAAO0H,CACX,CAQO,SAASgC,GAAsB1J,EAAqBoJ,EAASO,EAAa,yBAC7E,MAAMC,EAASR,EAAQ,MAAM,cAAa,EACtCpJ,EAAoB,gBACpB4J,EAAO,cAAgB,IAE3BA,EAAO,uBAAyBD,GAAe,GAC/C,MAAMJ,EAAqBH,EAAQ,oBAAsBlJ,EACzD0J,EAAO,SAAW5J,EAAoB,SACtC,MAAMI,EAAQwJ,EAAO,WAErB,GAAI5J,EAAoB,eAAgB,CACpC,MAAM6J,EAAK7J,EAAoB,eACzBqB,EAAgB,CAClB,QAAQpB,EAAA4J,EAAG,SAAH,YAAA5J,EAAW,IAAKO,GAAMJ,EAAM,YAAYI,CAAC,GACjD,QAAQ+E,EAAAsE,EAAG,SAAH,YAAAtE,EAAW,IAAKuE,GAAM1J,EAAM,eAAe0J,CAAC,GACpD,SAAStE,EAAAqE,EAAG,UAAH,YAAArE,EAAY,IAAKuE,GAAM3J,EAAM,gBAAgB2J,CAAC,GACvD,WAAWtE,EAAAoE,EAAG,YAAH,YAAApE,EAAc,IAAKjF,GAAMJ,EAAM,gBAAgBI,CAAC,GAC3D,UAAUkF,EAAAmE,EAAG,WAAH,YAAAnE,EAAa,IAAKsE,GAAM5J,EAAM,iBAAiB4J,CAAC,GAC1D,YAAYC,EAAAJ,EAAG,aAAH,YAAAI,EAAe,IAAKxI,GAAMrB,EAAM,WAAW,KAAM8J,GAASA,EAAK,OAASzI,CAAC,GACrF,WAAW0I,EAAAN,EAAG,YAAH,YAAAM,EAAc,IAAKC,GAAMhK,EAAM,kBAAkBgK,CAAC,GAC7D,iBAAiBC,EAAAR,EAAG,kBAAH,YAAAQ,EAAoB,IAAKC,GAAOlK,EAAM,sBAAsBkK,CAAE,GAC/E,iBAAiBC,EAAAV,EAAG,kBAAH,YAAAU,EAAoB,IAAK7J,GAAON,EAAM,wBAAwBM,CAAE,GACjF,gBAAgB8J,EAAAX,EAAG,iBAAH,YAAAW,EAAmB,IAAKC,GAAOrK,EAAM,qBAAqBqK,CAAE,GAC5E,UAAW,CAAE,EACb,eAAgB,CAAE,EAClB,oBAAqB,CAAE,EACvB,WAAY,CAAE,EACd,eAAgB,CAAE,EAClB,mBAAoB,KACpB,cAAe,CAAE,EACjB,OAAQ,KACR,aAAc,CAAE,EAChB,OAAQ,CAAE,EACV,iBAAkB,CAAE,EACpB,iBAAkB,CAAE,EACpB,mBAAoB,CAAE,EACtB,SAAU,UAAY,CAClB,MAAM,IAAI,MAAM,2BAA2B,CAC9C,CACb,EACQb,EAAO,cAAgBvI,CAC1B,CACD,UAAWtB,KAAOC,EAAoB,eAAgB,CAClD,MAAMV,EAAQiK,EAAmBxJ,EAAKC,EAAoB,eAAgB4J,EAAO,cAAexJ,CAAK,EACrGwJ,EAAO,cAAc7J,CAAG,EAAIT,CAC/B,CACD,UAAWS,KAAOC,EAAoB,kBAAmB,CACrD,MAAMV,EAAQiK,EAAmBxJ,EAAKC,EAAoB,kBAAmB4J,EAAO,cAAexJ,CAAK,EACxGwJ,EAAO,yBAAyB7J,EAAKT,CAAK,CAC7C,CACD,OAAOsK,CACX,CAoBO,SAASJ,GAAiCxJ,EAAqB0K,EAAcC,EAAW,CAC3F,MAAMC,EAAe,GACfrB,EAAqBmB,EAAa,oBAAsBxK,EAC9D,GAAIF,EAAoB,OACpB,UAAWD,KAAOC,EAAoB,OAClC4K,EAAa7K,CAAG,EAAIwJ,EAAmBxJ,EAAKC,EAAoB,OAAQ0K,EAAa,iBAAmBA,EAAa,MAAOA,EAAa,KAAK,EAGtJ,GAAIxJ,GAAmBlB,EAAoB,SAAS,EAAG,CACnD,GAAI,CAAC0K,EAAa,cACd,MAAM,IAAI,MAAM,2CAA2C,EAE/DE,EAAa,cAAgBF,EAAa,aAC7C,CACD,MAAMrI,EAAM,IAAIsI,EAAUC,CAAY,EACtCvI,EAAI,SAAWrC,EAAoB,SACnC,QAAS+B,EAAI,EAAGA,EAAI/B,EAAoB,WAAW,OAAQ+B,IAAK,CAC5D,MAAM8I,EAAYxI,EAAI,aAAarC,EAAoB,WAAW+B,CAAC,EAAE,IAAI,EACzE,GAAI8I,EACAA,EAAU,YAAY7K,EAAoB,WAAW+B,CAAC,CAAC,MAGvD,OAAM,IAAI,MAAM,uCAAyC/B,EAAoB,WAAW+B,CAAC,EAAE,KAAO,aAAe/B,EAAoB,SAAS,CAErJ,CACD,QAAS+B,EAAI,EAAGA,EAAI/B,EAAoB,YAAY,OAAQ+B,IAAK,CAC7D,MAAM+I,EAAazI,EAAI,cAAcrC,EAAoB,YAAY+B,CAAC,EAAE,IAAI,EAC5E,GAAI+I,EACAA,EAAW,YAAY9K,EAAoB,YAAY+B,CAAC,CAAC,MAGzD,OAAM,IAAI,MAAM,wCAA0C/B,EAAoB,YAAY+B,CAAC,EAAE,KAAO,aAAe/B,EAAoB,SAAS,CAEvJ,CACD,OAAAqC,EAAI,SAAWrC,EAAoB,SACnCqC,EAAI,aAAeA,EAAI,YAAYrC,CAAmB,EAC/CqC,CACX,CC3OO,MAAM0I,GAAwB,CACjC,MAAO,CAAE,OAAQ,EAAG,cAAe,SAAsC,YAAa,QAAU,EAChG,KAAM,CAAE,OAAQ,EAAG,cAAe,UAAwC,YAAa,SAAW,EAClG,OAAQ,CAAE,OAAQ,EAAG,cAAe,UAAwC,YAAa,QAAU,EACnG,OAAQ,CAAE,OAAQ,EAAG,cAAe,UAAwC,YAAa,QAAU,EACnG,OAAQ,CAAE,OAAQ,EAAG,cAAe,UAAwC,YAAa,QAAU,EACnG,SAAU,CAAE,OAAQ,GAAI,cAAe,SAAsC,YAAa,QAAU,EACpG,SAAU,CAAE,OAAQ,EAAG,cAAe,WAA0C,YAAa,QAAU,EACvG,SAAU,CAAE,OAAQ,EAAG,cAAe,WAA0C,YAAa,QAAU,EACvG,IAAK,CAAE,OAAQ,EAAG,cAAe,mBAAiD,YAAa,QAAU,CAC7G,EACO,MAAMC,EAAoC,CAC7C,YAAYC,EAAqBC,EAAOC,EAAS,CAC7C,KAAK,oBAAsBF,EAC3B,KAAK,MAAQC,EACb,KAAK,QAAUC,EAKf,KAAK,OAAS,GACd,KAAK,UAAY,GACjB,KAAK,iBAAmB,GACxB,KAAK,QAAU,GACf,KAAK,uBAAyB,EAC9B,KAAK,OAAS,GAEd,KAAK,YAAW,EAEhB,KAAK,mBAAkB,EACvB,KAAK,gBAAe,EACpB,KAAK,aAAY,EACjB,KAAK,YAAW,CACnB,CACD,IAAI,QAAS,CACT,MAAO,CACH,MAAO,KAAK,OACZ,SAAU,KAAK,UACf,gBAAiB,KAAK,iBACtB,OAAQ,KAAK,QACb,MAAO,KAAK,MACxB,CACK,CACD,aAAc,CACV,GAAK,KAAK,oBAAoB,MAG9B,UAAW7J,KAAQ,KAAK,oBAAoB,MACxC,KAAK,OAAO,KAAKyJ,GAAsBzJ,EAAK,SAAS,CAAC,CAE7D,CACD,oBAAqB,CACjB,GAAK,KAAK,oBAAoB,aAG9B,UAAW8J,KAAe,KAAK,oBAAoB,aAAc,CAE7D,MAAMC,EAAUC,GAAyBF,CAAW,EAEpD,GAAI,CAACC,EACD,MAAAxJ,EAAO,MAAM,CAAC,mCAAoCuJ,CAAW,CAAC,EACxD,IAAI,MAAM,4BAA4B,EAEhD,KAAK,UAAU,KAAK,CAChB,iBAAkBC,EAClB,kBAAmBD,EAAY,UAAYA,EAAY,GAAK,IAAMA,EAAY,UAAYA,EAAY,EACtH,CAAa,CACJ,CACJ,CACD,iBAAkB,CACd,GAAK,KAAK,oBAAoB,UAG9B,UAAWG,KAAY,KAAK,oBAAoB,UAAW,CACvD,MAAMC,EAAS,KAAK,eAAeD,CAAQ,EAE3C,KAAK,iBAAiB,KAAKC,CAAM,CACpC,CACJ,CACD,eAAeD,EAAUE,EAAe,CACpC,MAAMnK,EAAO,KAAK,OAAOiK,EAAS,IAAI,EACtC,GAAI,CAACjK,EACD,MAAAO,EAAO,MAAM,CAAC,6BAA8B0J,CAAQ,CAAC,EAC/C,IAAI,MAAM,yBAAyB,EAE7C,GAAIA,EAAS,OACLA,EAAS,MAAM,SAAWjK,EAAK,OAC/B,MAAAO,EAAO,MAAM,CAAC,oCAAqC0J,EAAUjK,CAAI,CAAC,EAC5D,IAAI,MAAM,yBAAyB,EAGjD,MAAMhC,EAAQiM,EAAS,OAAS,GAChC,GAAI,CAACjM,EAAM,OACP,OAAQgC,EAAK,cAAa,CACtB,IAAK,UACDhC,EAAM,KAAK,EAAK,EAChB,MACJ,IAAK,mBACDA,EAAM,KAAK,CAAC,EACZ,MACJ,IAAK,SACDA,EAAM,KAAK,GAAG,EACd,MACJ,IAAK,UACDA,EAAM,KAAK,IAAK,GAAG,EACnB,MACJ,IAAK,UACDA,EAAM,KAAK,IAAK,IAAK,GAAG,EACxB,MACJ,IAAK,UACL,IAAK,WACL,IAAK,aACDA,EAAM,KAAK,IAAK,EAAG,CAAC,EACpB,MACJ,IAAK,SACDA,EAAM,KAAK,IAAK,EAAG,EAAE,EACrB,MACJ,IAAK,WACDA,EAAM,KAAK,IAAK,EAAG,CAAC,EACpB,KAGP,CAEL,MAAO,CAAE,KAAMgC,EAAK,cAAe,MAAOmK,EAAgBA,EAAcnM,EAAO,IAAI,EAAIA,CAAK,CAC/F,CACD,cAAe,CACX,GAAK,KAAK,oBAAoB,OAG9B,UAAWmH,KAAS,KAAK,oBAAoB,OAAQ,CACjD,MAAMiF,EAAY,CACd,QAASjF,EAAM,IAAM,iBAAmB,KAAK,wBAC7D,EACgBA,EAAM,SACNiF,EAAU,UAAY,OAAO,KAAKjF,EAAM,MAAM,EAAE,IAAK1G,GAAQ,OACzD,MAAM4L,GAAa1L,EAAAwG,EAAM,SAAN,YAAAxG,EAAeF,GAClC,GAAI,CAAC4L,EACD,MAAA9J,EAAO,MAAM,CAAC,+BAAgC9B,CAAG,CAAC,EAC5C,IAAI,MAAM,sBAAsB,EAE1C,MAAMuB,EAAO,KAAK,OAAOqK,EAAW,IAAI,EACxC,GAAI,CAACrK,EACD,MAAAO,EAAO,MAAM,CAAC,gCAAiC8J,CAAU,CAAC,EACpD,IAAI,MAAM,sBAAsB,EAE1C,MAAMrM,EAAQ,OAAOqM,EAAW,MAAU,IAAc,KAAK,eAAeA,CAAU,EAAI,OAC1F,MAAO,CACH,GAAI5L,EACJ,KAAMuB,EAAK,cACX,UAAW,GACX,MAAAhC,CACxB,CACA,CAAiB,GAEL,KAAK,QAAQ,KAAKoM,CAAS,CAC9B,CACJ,CACD,aAAc,CACV,GAAK,KAAK,oBAAoB,MAG9B,UAAWhJ,KAAQ,KAAK,oBAAoB,MAAO,CAE/C,GAAI,OAAOA,EAAK,aAAgB,SAC5B,MAAAb,EAAO,MAAM,CAAC,gCAAiCa,CAAI,CAAC,EAC9C,IAAI,MAAM,qBAAqB,EAEzC,MAAM2I,EAAU,KAAK,UAAU3I,EAAK,WAAW,EAC/C,GAAI,CAAC2I,EACD,MAAAxJ,EAAO,MAAM,CAAC,4BAA6Ba,CAAI,CAAC,EAC1C,IAAI,MAAM,qBAAqB,EAEzC,GAAI2I,EAAQ,iBAAiB,YACrB,CAACA,EAAQ,iBAAiB,WAAW3I,EAAM,KAAK,oBAAqB,KAAK,KAAK,EAC/E,MAAM,IAAI,MAAM,uCAAuCA,CAAI,EAAE,EAGrE,MAAMoG,EAAS,GAEf,UAAW8C,KAAaP,EAAQ,iBAAiB,OAAQ,CACrD,MAAM9I,EAAQ,KAAK,eAAeqJ,EAAWP,EAAQ,iBAAiB,EACtE,KAAK,wBAAwB3I,EAAMH,EAAO8I,EAAQ,iBAAkBO,CAAS,EAC7E9C,EAAO,KAAKvG,CAAK,CACpB,CACD,KAAK,OAAO,KAAK,CAAE,OAAAuG,EAAQ,kBAAmBuC,EAAQ,iBAAiB,CAAE,CAC5E,CACJ,CACD,eAAepM,EAAWqC,EAAM,CAQ5B,MAAO,CACH,SARaY,IASb,UAAAjD,EACA,WATe,GAUf,YATgB,GAUhB,aATiB,GAUjB,cATkB,GAUlB,OATW,GAUX,KAAAqC,EACA,SAVa,EAWzB,CACK,CACD,wBAAwBoB,EAAMH,EAAOsJ,EAAaD,EAAW,CACzD,MAAME,EAAgBvJ,EAAM,OACxBG,EAAK,eACL,OAAO,KAAKA,EAAK,aAAa,EAAE,QAAS3C,GAAQ,SAC7C,MAAMT,GAAQW,EAAAyC,EAAK,gBAAL,YAAAzC,EAAqBF,GAEnC,GAAI,CAACT,EACD,MAAAuC,EAAO,MAAM,CAAC,wCAAyC9B,CAAG,CAAC,EACrD,IAAI,MAAM,kCAAkC,EAEtD,MAAMgM,GAAgBxG,EAAAsG,EAAY,gBAAZ,YAAAtG,EAA4BxF,GAElD,GADuBgM,GAAiBA,EAAc,QAAUA,EAAc,UAAYH,EAAYC,EAAY,OAAO,QAAQD,CAAS,IAAM,EAC5H,CAEhB,MAAMI,GAAYD,GAAA,YAAAA,EAAe,OAAQhM,GACpC,CAACT,GAAS,OAAOA,EAAM,MAAU,MAAgB,OAAOyM,GAAA,YAAAA,EAAe,cAAiB,IACzFD,EAAcE,CAAS,EAAI,CACvB,MAAOD,EAAc,YACjD,EAE6BzM,EAAM,MAAM,QAAU,EAE3BwM,EAAcE,CAAS,EAAI,CACvB,MAAO1M,EAAM,MAAM,SAAW,EAAIA,EAAM,MAAM,CAAC,EAAIA,EAAM,KACrF,EAGwBuC,EAAO,KAAK,CAAC,uCAAwCvC,CAAK,CAAC,EAG3DyM,GAAiBA,EAAc,kBAC/BD,EAAcE,CAAS,EAAE,MAAQD,EAAc,gBAAgB,CAACD,EAAcE,CAAS,EAAE,KAAK,EAAG,IAAI,EAAE,CAAC,EAE/G,CACjB,CAAa,CAER,CACD,sBAAsBnI,EAAS,4CAC3B,QAAS9B,EAAI,EAAGA,EAAI,KAAK,OAAO,OAAQA,IAAK,CAEzC,MAAMkK,GAAWhM,EAAA,KAAK,oBAAoB,QAAzB,YAAAA,EAAiC8B,GAClD,GAAI,CAACkK,EAED,MAAApK,EAAO,MAAM,CAAC,uCAAwC,KAAK,OAAOE,CAAC,CAAC,CAAC,EAC/D,IAAI,MAAM,gCAAgC,EAEpD,MAAMmK,EAAkB,KAAK,OAAOnK,CAAC,EAC/BoK,EAAe,KAAK,UAAUF,EAAS,WAAW,EAExD,GAAI,CAACE,EACD,MAAAtK,EAAO,MAAM,CAAC,4BAA6BoK,CAAQ,CAAC,EAC9C,IAAI,MAAM,gCAAgC,EAEpD,MAAMG,GAAgBH,EAAS,OAAS,GAClCI,GAAY,OAAO,KAAKD,EAAa,EAAE,KAAI,EAEjD,UAAWE,KAAWD,GAAW,CAC7B,MAAME,EAAOH,GAAcE,CAAO,EAC5BE,GAAchH,GAAAD,EAAA4G,EAAa,iBAAiB,UAA9B,YAAA5G,EAAuC,QAAvC,YAAAC,EAA+C8G,GAC7DG,GAAgBD,GAAA,YAAAA,EAAa,OAAQF,EAErCI,EAAY,KAAK,2BAA2BD,EAAe,EAAI,GACtDD,GAAeA,EAAY,SAAWN,EAAgB,OAAO,KAAMzJ,GAAMA,EAAE,YAAc+J,EAAY,OAAO,GAAMN,EAAgB,OAAO,CAAC,GACnJ,cAAc,KAAKQ,CAAS,EAElC,MAAMC,EAAcJ,EAAK,KACnBK,EAAS,KAAK,OAAOD,CAAW,EACtC,GAAI,CAACC,EACD,MAAA/K,EAAO,MAAM,CAAC,kCAAmC8K,CAAW,CAAC,EACvD,IAAI,MAAM,gCAAgC,EAGpD,MAAME,EAAcC,GAA+BF,EAAO,iBAAiB,EAC3E,GAAI,CAACC,EACD,MAAAhL,EAAO,MAAM,CAAC,kCAAmC+K,CAAM,CAAC,EAClD,IAAI,MAAM,gCAAgC,EAEpD,IAAIG,GAAgBrH,GAAAD,EAAAoH,EAAY,SAAZ,YAAApH,EAAoB,QAApB,YAAAC,EAA4B6G,EAAK,QAAU,MAC3DS,EAAe,GACnB,GAAI,CAACD,EACD,UAAWhN,KAAOkK,EAAA4C,EAAY,SAAZ,YAAA5C,EAAoB,MAC9BlK,EAAI,WAAW,GAAG,GAAKA,EAAI,SAAS,GAAG,IACvCiN,EAAe,GACfD,GAAgB1C,GAAAF,EAAA0C,EAAY,SAAZ,YAAA1C,EAAoB,QAApB,YAAAE,EAA4BtK,IAIxD,MAAMkN,EAAmBF,EAAiBC,EAAeD,EAAc,KAAK,QAAQ,KAAMR,EAAK,QAAU,EAAE,EAAIQ,EAAc,KAAQR,EAAK,QAAU,KAC9IW,EAAcH,GAAiBA,EAAc,SAAWH,EAAO,OAAO,KAAMnK,GAAMA,EAAE,YAAcsK,EAAc,OAAO,GAAMH,EAAO,OAAO,CAAC,EAElJ,IAAIO,EAAWD,EAAW,aAAa,KAAM9C,GAAMA,EAAE,OAAS6C,CAAgB,EAEzEE,IACDA,EAAW,KAAK,2BAA2BF,CAAgB,EAC3DC,EAAW,aAAa,KAAKC,CAAQ,GAGzCA,EAAS,kBAAkB,KAAKT,EAAU,QAAQ,EAClDA,EAAU,kBAAkB,KAAKS,EAAS,QAAQ,CACrD,CAED,MAAMC,GAAiBnB,EAAS,QAAU,GACpCoB,GAAa,OAAO,KAAKD,EAAc,EAC7C,UAAWE,KAAYD,GAAY,CAC/B,MAAM/N,EAAQ8N,GAAeE,CAAQ,EACrC,IAAIC,GAAe/C,GAAAD,EAAA4B,EAAa,iBAAiB,SAA9B,YAAA5B,EAAsC,SAAtC,YAAAC,EAA+C8C,GAC9DN,EAAe,GACnB,GAAI,CAACO,EACD,UAAWxN,KAAOyN,EAAArB,EAAa,iBAAiB,SAA9B,YAAAqB,EAAsC,OAChDzN,EAAI,WAAW,GAAG,GAAKA,EAAI,SAAS,GAAG,IACvCiN,EAAe,GACfO,GAAeE,GAAAC,EAAAvB,EAAa,iBAAiB,SAA9B,YAAAuB,EAAsC,SAAtC,YAAAD,EAA+C1N,IAI1E,MAAM4N,EAAeJ,EAAgBP,EAAeO,EAAa,KAAK,QAAQ,KAAMD,CAAQ,EAAIC,EAAa,KAAQD,EAE/GH,EAAW,KAAK,2BAA2BQ,CAAY,EAG7D,IAFeJ,GAAgBA,EAAa,SAAWrB,EAAgB,OAAO,KAAMzJ,GAAMA,EAAE,YAAc8K,EAAa,OAAO,GAAMrB,EAAgB,OAAO,CAAC,GACtJ,WAAW,KAAKiB,CAAQ,EAC1B7N,EAAM,QAAU,OAAW,CAC3B,MAAMsO,EAAiB,KAAK,eAAetO,EAAOiO,GAAgBA,EAAa,eAAe,EAC9F1J,EAAQ,kBAAkBsJ,EAAS,QAAQ,EAAIS,CAClD,SACQ,OAAOtO,EAAM,KAAS,IAAa,CACxC,MAAMuO,EAAYvO,EAAM,KAClBwO,EAAoBxO,EAAM,QAAU,QACpCyO,EAAU,KAAK,OAAOF,CAAS,EACrC,GAAI,CAACE,EACD,MAAAlM,EAAO,MAAM,CAAC,4CAA6CvC,CAAK,CAAC,EAC3D,IAAI,MAAM,gCAAgC,EAEpD,MAAM6M,EAAeW,GAA+BiB,EAAQ,iBAAiB,EAC7E,GAAI,CAAC5B,EACD,MAAAtK,EAAO,MAAM,CAAC,+CAAgDvC,CAAK,CAAC,EAC9D,IAAI,MAAM,gCAAgC,EAEpD,IAAIiO,GAAeS,GAAAC,EAAA9B,EAAa,UAAb,YAAA8B,EAAsB,SAAtB,YAAAD,EAA+BF,GAC9Cd,EAAe,GAEnB,GAAI,CAACO,EAED,UAAWxN,KAAOmO,EAAA/B,EAAa,UAAb,YAAA+B,EAAsB,OAChCnO,EAAI,WAAW,GAAG,GAAKA,EAAI,SAAS,GAAG,IACvCiN,EAAe,GACfO,GAAeY,GAAAC,EAAAjC,EAAa,UAAb,YAAAiC,EAAsB,SAAtB,YAAAD,EAA+BpO,IAI1D,MAAM0M,EAAgBc,EAAgBP,EAAeO,EAAa,KAAK,QAAQ,KAAMO,CAAiB,EAAIP,GAAA,YAAAA,EAAc,KAAQO,EAC1HO,EAAYd,GAAgBA,EAAa,SAAWQ,EAAQ,OAAO,KAAMtL,GAAMA,EAAE,YAAc8K,EAAa,OAAO,GAAMQ,EAAQ,OAAO,CAAC,EAC/I,IAAIrB,EAAY2B,EAAS,YAAY,KAAMjE,GAAMA,EAAE,OAASqC,CAAa,EAEpEC,IACDA,EAAY,KAAK,2BAA2BD,EAAe,EAAI,EAC/D4B,EAAS,YAAY,KAAK3B,CAAS,GAGvCS,EAAS,kBAAkB,KAAKT,EAAU,QAAQ,EAClDA,EAAU,kBAAkB,KAAKS,EAAS,QAAQ,CACrD,KAEG,OAAAtL,EAAO,MAAM,CAAC,qCAAsCvC,CAAK,CAAC,EACpD,IAAI,MAAM,gCAAgC,CAEvD,CAED,GAAI6M,EAAa,iBAAiB,qBAC9B,UAAWmC,KAAanC,EAAa,iBAAiB,qBAAsB,CACxE,MAAMjI,EAAQoK,EAAU,MAClBnK,EAASmK,EAAU,OACnBC,EAAaD,EAAU,WAC7B,KAAK,uBAAuBpK,EAAOC,EAAQ+H,EAAgB,OAAOoC,EAAU,eAAe,EAAGpC,EAAgB,OAAOoC,EAAU,gBAAgB,EAAGC,CAAU,CAC/J,CAEL,GAAIpC,EAAa,iBAAiB,eAAgB,CAC9C,MAAMf,GAAcoD,GAAA,KAAK,oBAAoB,eAAzB,YAAAA,GAAwCvC,EAAS,aACrE,GAAI,CAACb,EACD,MAAAvJ,EAAO,MAAM,CAAC,2CAA4CoK,CAAQ,CAAC,EAC7D,IAAI,MAAM,gCAAgC,EAEpDC,EAAgB,OAASC,EAAa,iBAAiB,eAAeF,EAAUb,EAAae,EAAa,iBAAkB,KAAMD,EAAgB,OAAQrI,EAAS,KAAK,KAAK,CAChL,CACJ,CACJ,CACD,2BAA2BzB,EAAMqM,EAAU,CACvC,MAAO,CACH,SAAUvM,EAAY,EACtB,KAAAE,EACA,gBAAiBqM,EAAW,EAAyC,EACrE,kBAAmB,CAAE,CACjC,CACK,CACD,uBAAuBvK,EAAOC,EAAQE,EAAiBC,EAAkBiK,EAAY,CACjF,MAAMG,EAAaH,EAAalK,EAAgB,WAAaA,EAAgB,aACvEsK,EAAcJ,EAAajK,EAAiB,YAAcA,EAAiB,cAC3EsK,EAAkBF,EAAW,KAAMtE,GAAMA,EAAE,OAASlG,CAAK,GAAK,KAAK,2BAA2BA,CAAK,EACnG2K,EAAmBF,EAAY,KAAMvE,GAAMA,EAAE,OAASjG,CAAM,GAAK,KAAK,2BAA2BA,EAAQ,EAAI,EAE9GuK,EAAW,KAAMtE,GAAMA,EAAE,OAASlG,CAAK,GACxCwK,EAAW,KAAKE,CAAe,EAE9BD,EAAY,KAAMvE,GAAMA,EAAE,OAASjG,CAAM,GAC1CwK,EAAY,KAAKE,CAAgB,EAGrCD,EAAgB,kBAAkB,KAAKC,EAAiB,QAAQ,EAChEA,EAAiB,kBAAkB,KAAKD,EAAgB,QAAQ,CACnE,CACD,gBAAgBrN,EAAO,CACnB,MAAO,kBAAoBA,CAC9B,CACD,sBAAuB,CACnB,MAAMsC,EAAU,CACZ,SAAU3B,EAAY,EACtB,eAAgB,CAAE,EAClB,kBAAmB,CAAE,CACjC,EACQ,KAAK,sBAAsB2B,CAAO,EAClC,QAAS9B,EAAI,EAAGA,EAAI,KAAK,iBAAiB,OAAQA,IAAK,CACnD,MAAMwJ,EAAW,KAAK,iBAAiBxJ,CAAC,EACxC8B,EAAQ,eAAe,KAAK,gBAAgB9B,CAAC,CAAC,EAAIwJ,CACrD,CAED,MAAO,CACH,YAAa,GACb,UAHc,KAAK,OAAO,OAAO,CAACxK,EAAKC,IAAQD,EAAI,OAAOC,EAAI,MAAM,EAAG,CAAE,GAIzE,kBAAmB,CAAC6C,CAAO,CACvC,CACK,CACL,CClbA,MAAMiL,EAAO,oBAIN,MAAMC,EAAkB,CAK3B,YAAY5D,EAAS,CACjB,KAAK,QAAUA,EAIf,KAAK,KAAO2D,EACZ,KAAK,QAAU,KAAK,QAAQ,gBAAgBA,CAAI,EAChD,KAAK,eAAiBE,GAAyB,KAAK,QAAQ,IAAI,EAEhE7D,EAAQ,wBAA0B,GAElC,MAAM/K,EAAQ+K,EAAQ,aAClB/K,GACA6O,GAA6B7O,CAAK,CAEzC,CACD,SAAU,CACN,KAAK,QAAU,KACf,OAAO,KAAK,cACf,CACD,MAAM,SAAU,OACZ,GAAI,CAAC,KAAK,QAAQ,cAAgB,CAAC,KAAK,eACpC,OAEJ,MAAMA,EAAQ,KAAK,QAAQ,aACrB8O,GAA0BjP,EAAA,KAAK,QAAQ,KAAK,aAAlB,YAAAA,EAA8B,kBAC9D,GAAI,CAACiP,EAED,OAEJ,MAAMC,EAAc,IAAI5H,EAAqB,CAAE,MAAAnH,CAAO,GACtD+O,EAAY,4BAA8B,GAC1C,MAAMC,EAASF,EAAwB,OAAO,IAAKxH,GAChC,IAAIsD,GAAoCtD,EAAO,KAAK,QAAQ,KAAM,KAAK,OAAO,EAC/E,sBACjB,EAED,MAAM,QAAQ,IAAI0H,EAAO,IAAK1H,GAAUyB,GAAoBzB,EAAO,CAAE,YAAAyH,EAAa,cAAe,KAAK,cAAgB,EAAC,CAAC,EACxHA,EAAY,MAAK,CACpB,CACL,CAKO,SAASF,GAA6B7O,EAAO,CAGhDiP,EAAuB,wDAAyD,CAC5E,IAAK,IACIjP,EAAM,aAGJT,EAAW,mBAAmBS,EAAM,aAAa,eAAgB,GAAE,YAF/D,IAAIT,EAAW,IAAK,IAAK,IAAK,GAAG,EAIhD,KAAM,aACN,UAAW,IAAMS,EAAM,YAC/B,CAAK,EAEDiP,EAAuB,wDAAyD,CAC5E,IAAK,IACIjP,EAAM,aAGJA,EAAM,aAAa,SAFf,IAAIX,GAAQ,IAAK,IAAK,GAAG,EAIxC,KAAM,UACN,UAAW,IAAMW,EAAM,YAC/B,CAAK,EAEDiP,EAAuB,wDAAyD,CAC5E,IAAMC,GAAc,OAChB,QAAOrP,EAAAqP,EAAU,yBAAV,YAAArP,EAAkC,YAAa,EACzD,EACD,KAAM,UACN,UAAYqP,GACDA,EAAU,sBAE7B,CAAK,EACDD,EAAuB,sDAAuD,CAC1E,IAAMC,GAAc,OAChB,SAAQrP,EAAAqP,EAAU,yBAAV,YAAArP,EAAkC,OAAQ,GAAK,EAC1D,EACD,KAAM,SACN,UAAYqP,GACDA,EAAU,sBAE7B,CAAK,EACDD,EAAuB,sDAAuD,CAC1E,IAAMC,GAAc,OAChB,SAAQrP,EAAAqP,EAAU,yBAAV,YAAArP,EAAkC,KAAM,GAAK,EACxD,EACD,KAAM,SACN,UAAYqP,GACDA,EAAU,sBAE7B,CAAK,EAEDD,EAAuB,uDAAwD,CAC3E,IAAMC,GAAc,OAChB,SAAQrP,EAAAqP,EAAU,yBAAV,YAAArP,EAAkC,oBAAqB,GAAK,EACvE,EACD,KAAM,SACN,UAAYqP,GACDA,EAAU,sBAE7B,CAAK,EAEDD,EAAuB,8DAA+D,CAClF,IAAMC,GAAc,OAChB,SAAQrP,EAAAqP,EAAU,yBAAV,YAAArP,EAAkC,oBAAqB,GAAK,EACvE,EACD,KAAM,SACN,UAAYqP,GACDA,EAAU,sBAE7B,CAAK,CACL,CAEApH,GAAkB4G,EAAM,4BAA6B,UACzC,MAAMvG,EAAA,WAAO,yCAAkD,EAAC,8CAAE,yBAC7E,EACDgH,GAAwBT,CAAI,EAC5BU,GAAsBV,EAAM,GAAOW,GAAW,IAAIV,GAAkBU,CAAM,CAAC", "names": ["isMeshClassName", "className", "isVectorClassName", "isMatrixClassName", "isAnimationGroupClassName", "parseVector", "value", "flipHandedness", "Vector2", "Vector3", "Vector4", "Quaternion", "Color3", "Color4", "defaultValueSerializationFunction", "key", "serializationObject", "_a", "defaultValueParseFunction", "assetsContainer", "scene", "intermediateValue", "finalValue", "nodes", "m", "ags", "ag", "Matrix", "FlowGraphMatrix2D", "FlowGraphMatrix3D", "FlowGraphInteger", "acc", "val", "getRichTypeByFlowGraphType", "needsPathConverter", "FlowGraphAssetType", "GetFlowGraphAssetWithType", "assetsContext", "type", "index", "useIndexAsUniqueId", "a", "FlowGraphAction", "FlowGraphLogger", "item", "<PERSON><PERSON>", "action", "i", "FlowGraphContext", "params", "RandomGUID", "Observable", "name", "obj", "defaultValue", "block", "connectionPoint", "b", "node", "framePayload", "valueSerializationFunction", "__decorate", "serialize", "FlowGraphConnectionType", "FlowGraphConnection", "_connectionType", "_owner<PERSON><PERSON>", "point", "removeFromLocal", "indexLocal", "indexConnected", "FlowGraphDataConnection", "connectionType", "owner<PERSON>lock", "richType", "_defaultValue", "_optional", "context", "RegisterClass", "FlowGraphBlock", "config", "_context", "input", "output", "_valueSerializeFunction", "serializedInput", "serializedOutput", "_serializationObject", "payload", "FlowGraphSignalConnection", "connectedPoint", "FlowGraphExecutionBlock", "error", "signalInput", "signalOutput", "FlowGraphSceneEventCoordinator", "deltaTime", "pointerInfo", "PointerEventTypes", "data", "pointerId", "mesh", "previousState", "_b", "_c", "_d", "_e", "_isADescendantOf", "mesh1", "mesh2", "_getClassNameOf", "v", "_areSameVectorClass", "className2", "_areSameMatrixClass", "_areSameIntegerClass", "isNumeric", "validIfNaN", "getNumericValue", "FlowGraphState", "FlowGraph", "event", "order", "state", "meshPickOrder", "block1", "visitor", "visitList", "idsAddedToVisitList", "dataIn", "connection", "signalOut", "valueSerializeFunction", "serializedBlock", "serializedContext", "FlowGraphCoordinator", "executeOnNextFrame", "e", "graph", "coordinators", "serializedGraph", "id", "observable", "async", "count", "customBlocks", "addToBlockFactory", "module", "blockName", "factory", "blockFactory", "__vitePreload", "FlowGraphExecutionBlockWithOutSignal", "FlowGraphAsyncExecutionBlock", "events", "eventName", "FlowGraphEventBlock", "GetDataOutConnectionByUniqueId", "blocks", "uniqueId", "dataOut", "GetSignalInConnectionByUniqueId", "signalIn", "ParseFlowGraphAsync", "options", "resolvedClasses", "ParseFlowGraph", "valueParseFunction", "ParseFlowGraphBlockWithClassType", "serializedConnection", "ParseFlowGraphContext", "rightHanded", "result", "ac", "l", "c", "t", "_f", "anim", "_g", "s", "_h", "ps", "_i", "_j", "tn", "parseOptions", "classType", "parsedConfig", "dataInput", "dataOutput", "gltfTypeToBabylonType", "InteractivityGraphToFlowGraphParser", "_interactivityGraph", "_gltf", "_loader", "declaration", "mapping", "getMappingForDeclaration", "variable", "parsed", "dataTransform", "converted", "eventValue", "blockType", "nodeMapping", "configuration", "configMapping", "config<PERSON><PERSON>", "gltfNode", "flowGraphBlocks", "outputMapper", "flowsFromGLTF", "flowsKeys", "flowKey", "flow", "flowMapping", "socketOutName", "socketOut", "inputNodeId", "nodeIn", "inputMapper", "getMappingForFullOperationName", "flowInMapping", "arrayMapping", "nodeInSocketName", "inputBlock", "socketIn", "valuesFromGLTF", "valuesKeys", "valueKey", "valueMapping", "_k", "_m", "_l", "socketInName", "convertedValue", "nodeOutId", "nodeOutSocketName", "nodeOut", "_o", "_n", "_p", "_r", "_q", "outBlock", "connector", "isVariable", "_s", "isOutput", "inputArray", "outputArray", "inputConnection", "outputConnection", "NAME", "KHR_interactivity", "GetPathToObjectConverter", "_AddInteractivityObjectModel", "interactivityDefinition", "coordinator", "graphs", "AddObjectAccessorToKey", "animation", "unregisterGLTFExtension", "registerGLTFExtension", "loader"], "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19], "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/serialization.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/flowGraphAssetsContext.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/flowGraphLogger.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/flowGraphContext.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/flowGraphConnection.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/flowGraphDataConnection.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/flowGraphBlock.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/flowGraphSignalConnection.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/flowGraphExecutionBlock.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/flowGraphSceneEventCoordinator.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/utils.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/flowGraph.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/flowGraphCoordinator.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/flowGraphBlockFactory.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/flowGraphExecutionBlockWithOutSignal.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/flowGraphAsyncExecutionBlock.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/flowGraphEventBlock.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/flowGraphParser.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_interactivity/interactivityGraphParser.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_interactivity.js"], "sourcesContent": ["import { Color3, Color4 } from \"../Maths/math.color.js\";\nimport { Matrix, Quaternion, Vector2, Vector3, Vector4 } from \"../Maths/math.vector.js\";\nimport { FlowGraphInteger } from \"./CustomTypes/flowGraphInteger.js\";\nimport { getRichTypeByFlowGraphType } from \"./flowGraphRichTypes.js\";\nimport { FlowGraphMatrix2D, FlowGraphMatrix3D } from \"./CustomTypes/flowGraphMatrix.js\";\nfunction isMeshClassName(className) {\n    return (className === \"Mesh\" ||\n        className === \"AbstractMesh\" ||\n        className === \"GroundMesh\" ||\n        className === \"InstanceMesh\" ||\n        className === \"LinesMesh\" ||\n        className === \"GoldbergMesh\" ||\n        className === \"GreasedLineMesh\" ||\n        className === \"TrailMesh\");\n}\nfunction isVectorClassName(className) {\n    return (className === \"Vector2\" /* FlowGraphTypes.Vector2 */ ||\n        className === \"Vector3\" /* FlowGraphTypes.Vector3 */ ||\n        className === \"Vector4\" /* FlowGraphTypes.Vector4 */ ||\n        className === \"Quaternion\" /* FlowGraphTypes.Quaternion */ ||\n        className === \"Color3\" /* FlowGraphTypes.Color3 */ ||\n        className === \"Color4\" /* FlowGraphTypes.Color4 */);\n}\nfunction isMatrixClassName(className) {\n    return className === \"Matrix\" /* FlowGraphTypes.Matrix */ || className === \"Matrix2D\" /* FlowGraphTypes.Matrix2D */ || className === \"Matrix3D\" /* FlowGraphTypes.Matrix3D */;\n}\nfunction isAnimationGroupClassName(className) {\n    return className === \"AnimationGroup\";\n}\nfunction parseVector(className, value, flipHandedness = false) {\n    if (className === \"Vector2\" /* FlowGraphTypes.Vector2 */) {\n        return Vector2.FromArray(value);\n    }\n    else if (className === \"Vector3\" /* FlowGraphTypes.Vector3 */) {\n        if (flipHandedness) {\n            value[2] *= -1;\n        }\n        return Vector3.FromArray(value);\n    }\n    else if (className === \"Vector4\" /* FlowGraphTypes.Vector4 */) {\n        return Vector4.FromArray(value);\n    }\n    else if (className === \"Quaternion\" /* FlowGraphTypes.Quaternion */) {\n        if (flipHandedness) {\n            value[2] *= -1;\n            value[3] *= -1;\n        }\n        return Quaternion.FromArray(value);\n    }\n    else if (className === \"Color3\" /* FlowGraphTypes.Color3 */) {\n        return new Color3(value[0], value[1], value[2]);\n    }\n    else if (className === \"Color4\" /* FlowGraphTypes.Color4 */) {\n        return new Color4(value[0], value[1], value[2], value[3]);\n    }\n    else {\n        throw new Error(`Unknown vector class name ${className}`);\n    }\n}\n/**\n * The default function that serializes values in a context object to a serialization object\n * @param key the key where the value should be stored in the serialization object\n * @param value the value to store\n * @param serializationObject the object where the value will be stored\n */\nexport function defaultValueSerializationFunction(key, value, serializationObject) {\n    const className = value?.getClassName?.() ?? \"\";\n    if (isVectorClassName(className) || isMatrixClassName(className)) {\n        serializationObject[key] = {\n            value: value.asArray(),\n            className,\n        };\n    }\n    else if (className === \"FlowGraphInteger\" /* FlowGraphTypes.Integer */) {\n        serializationObject[key] = {\n            value: value.value,\n            className,\n        };\n    }\n    else {\n        if (className && (value.id || value.name)) {\n            serializationObject[key] = {\n                id: value.id,\n                name: value.name,\n                className,\n            };\n        }\n        else {\n            // only if it is not an object\n            if (typeof value !== \"object\") {\n                serializationObject[key] = value;\n            }\n            else {\n                throw new Error(`Could not serialize value ${value}`);\n            }\n        }\n    }\n}\n/**\n * The default function that parses values stored in a serialization object\n * @param key the key to the value that will be parsed\n * @param serializationObject the object that will be parsed\n * @param assetsContainer the assets container that will be used to find the objects\n * @param scene\n * @returns\n */\nexport function defaultValueParseFunction(key, serializationObject, assetsContainer, scene) {\n    const intermediateValue = serializationObject[key];\n    let finalValue;\n    const className = intermediateValue?.type ?? intermediateValue?.className;\n    if (isMeshClassName(className)) {\n        let nodes = scene.meshes.filter((m) => (intermediateValue.id ? m.id === intermediateValue.id : m.name === intermediateValue.name));\n        if (nodes.length === 0) {\n            nodes = scene.transformNodes.filter((m) => (intermediateValue.id ? m.id === intermediateValue.id : m.name === intermediateValue.name));\n        }\n        finalValue = intermediateValue.uniqueId ? nodes.find((m) => m.uniqueId === intermediateValue.uniqueId) : nodes[0];\n    }\n    else if (isVectorClassName(className)) {\n        finalValue = parseVector(className, intermediateValue.value);\n    }\n    else if (isAnimationGroupClassName(className)) {\n        // do not use the scene.getAnimationGroupByName because it is possible that two AGs will have the same name\n        const ags = scene.animationGroups.filter((ag) => ag.name === intermediateValue.name);\n        // uniqueId changes on each load. this is used for the glTF loader, that uses serialization after the scene was loaded.\n        finalValue = ags.length === 1 ? ags[0] : ags.find((ag) => ag.uniqueId === intermediateValue.uniqueId);\n    }\n    else if (className === \"Matrix\" /* FlowGraphTypes.Matrix */) {\n        finalValue = Matrix.FromArray(intermediateValue.value);\n    }\n    else if (className === \"Matrix2D\" /* FlowGraphTypes.Matrix2D */) {\n        finalValue = new FlowGraphMatrix2D(intermediateValue.value);\n    }\n    else if (className === \"Matrix3D\" /* FlowGraphTypes.Matrix3D */) {\n        finalValue = new FlowGraphMatrix3D(intermediateValue.value);\n    }\n    else if (className === \"FlowGraphInteger\" /* FlowGraphTypes.Integer */) {\n        finalValue = FlowGraphInteger.FromValue(intermediateValue.value);\n    }\n    else if (className === \"number\" /* FlowGraphTypes.Number */ || className === \"string\" /* FlowGraphTypes.String */ || className === \"boolean\" /* FlowGraphTypes.Boolean */) {\n        finalValue = intermediateValue.value[0];\n    }\n    else if (intermediateValue && intermediateValue.value !== undefined) {\n        finalValue = intermediateValue.value;\n    }\n    else {\n        if (Array.isArray(intermediateValue)) {\n            // configuration data of an event\n            finalValue = intermediateValue.reduce((acc, val) => {\n                if (!val.eventData) {\n                    return acc;\n                }\n                acc[val.id] = {\n                    type: getRichTypeByFlowGraphType(val.type),\n                };\n                if (typeof val.value !== \"undefined\") {\n                    acc[val.id].value = defaultValueParseFunction(\"value\", val, assetsContainer, scene);\n                }\n                return acc;\n            }, {});\n        }\n        else {\n            finalValue = intermediateValue;\n        }\n    }\n    return finalValue;\n}\n/**\n * Given a name of a flow graph block class, return if this\n * class needs to be created with a path converter. Used in\n * parsing.\n * @param className the name of the flow graph block class\n * @returns a boolean indicating if the class needs a path converter\n */\nexport function needsPathConverter(className) {\n    // I am not using the ClassName property here because it was causing a circular dependency\n    // that jest didn't like!\n    return className === \"FlowGraphJsonPointerParserBlock\" /* FlowGraphBlockNames.JsonPointerParser */;\n}\n//# sourceMappingURL=serialization.js.map", "/**\n * The type of the assets that flow graph supports\n */\nexport var FlowGraphAssetType;\n(function (FlowGraphAssetType) {\n    FlowGraphAssetType[\"Animation\"] = \"Animation\";\n    FlowGraphAssetType[\"AnimationGroup\"] = \"AnimationGroup\";\n    FlowGraphAssetType[\"Mesh\"] = \"Mesh\";\n    FlowGraphAssetType[\"Material\"] = \"Material\";\n    FlowGraphAssetType[\"Camera\"] = \"Camera\";\n    FlowGraphAssetType[\"Light\"] = \"Light\";\n    // Further asset types will be added here when needed.\n})(FlowGraphAssetType || (FlowGraphAssetType = {}));\n/**\n * Returns the asset with the given index and type from the assets context.\n * @param assetsContext The assets context to get the asset from\n * @param type The type of the asset\n * @param index The index of the asset\n * @param useIndexAsUniqueId If set to true, instead of the index in the array it will search for the unique id of the asset.\n * @returns The asset or null if not found\n */\nexport function GetFlowGraphAssetWithType(assetsContext, type, index, useIndexAsUniqueId) {\n    switch (type) {\n        case \"Animation\" /* FlowGraphAssetType.Animation */:\n            return useIndexAsUniqueId\n                ? (assetsContext.animations.find((a) => a.uniqueId === index) ?? null)\n                : (assetsContext.animations[index] ?? null);\n        case \"AnimationGroup\" /* FlowGraphAssetType.AnimationGroup */:\n            return useIndexAsUniqueId\n                ? (assetsContext.animationGroups.find((a) => a.uniqueId === index) ?? null)\n                : (assetsContext.animationGroups[index] ?? null);\n        case \"Mesh\" /* FlowGraphAssetType.Mesh */:\n            return useIndexAsUniqueId\n                ? (assetsContext.meshes.find((a) => a.uniqueId === index) ?? null)\n                : (assetsContext.meshes[index] ?? null);\n        case \"Material\" /* FlowGraphAssetType.Material */:\n            return useIndexAsUniqueId\n                ? (assetsContext.materials.find((a) => a.uniqueId === index) ?? null)\n                : (assetsContext.materials[index] ?? null);\n        case \"Camera\" /* FlowGraphAssetType.Camera */:\n            return useIndexAsUniqueId\n                ? (assetsContext.cameras.find((a) => a.uniqueId === index) ?? null)\n                : (assetsContext.cameras[index] ?? null);\n        case \"Light\" /* FlowGraphAssetType.Light */:\n            return useIndexAsUniqueId\n                ? (assetsContext.lights.find((a) => a.uniqueId === index) ?? null)\n                : (assetsContext.lights[index] ?? null);\n        default:\n            return null;\n    }\n}\n//# sourceMappingURL=flowGraphAssetsContext.js.map", "import { Logger } from \"../Misc/logger.js\";\nexport var FlowGraphAction;\n(function (FlowGraphAction) {\n    FlowGraphAction[\"ExecuteBlock\"] = \"ExecuteBlock\";\n    FlowGraphAction[\"ExecuteEvent\"] = \"ExecuteEvent\";\n    FlowGraphAction[\"TriggerConnection\"] = \"TriggerConnection\";\n    FlowGraphAction[\"ContextVariableSet\"] = \"ContextVariableSet\";\n    FlowGraphAction[\"GlobalVariableSet\"] = \"GlobalVariableSet\";\n    FlowGraphAction[\"GlobalVariableDelete\"] = \"GlobalVariableDelete\";\n    FlowGraphAction[\"GlobalVariableGet\"] = \"GlobalVariableGet\";\n    FlowGraphAction[\"AddConnection\"] = \"AddConnection\";\n    FlowGraphAction[\"GetConnectionValue\"] = \"GetConnectionValue\";\n    FlowGraphAction[\"SetConnectionValue\"] = \"SetConnectionValue\";\n    FlowGraphAction[\"ActivateSignal\"] = \"ActivateSignal\";\n    FlowGraphAction[\"ContextVariableGet\"] = \"ContextVariableGet\";\n})(FlowGraphAction || (FlowGraphAction = {}));\n/**\n * This class will be responsible of logging the flow graph activity.\n * Note that using this class might reduce performance, as it will log every action, according to the configuration.\n * It attaches to a flow graph and uses meta-programming to replace the methods of the flow graph to add logging abilities.\n */\nexport class FlowGraphLogger {\n    constructor() {\n        /**\n         * Whether to log to the console.\n         */\n        this.logToConsole = false;\n        /**\n         * The log cache of the flow graph.\n         * Each item is a logged item, in order of execution.\n         */\n        this.log = [];\n    }\n    addLogItem(item) {\n        if (!item.time) {\n            item.time = Date.now();\n        }\n        this.log.push(item);\n        if (this.logToConsole) {\n            const value = item.payload?.value;\n            if (typeof value === \"object\" && value.getClassName) {\n                Logger.Log(`[FGLog] ${item.className}:${item.uniqueId.split(\"-\")[0]} ${item.action} - ${JSON.stringify(value.getClassName())}: ${value.toString()}`);\n            }\n            else {\n                Logger.Log(`[FGLog] ${item.className}:${item.uniqueId.split(\"-\")[0]} ${item.action} - ${JSON.stringify(item.payload)}`);\n            }\n        }\n    }\n    getItemsOfType(action) {\n        return this.log.filter((i) => i.action === action);\n    }\n}\n//# sourceMappingURL=flowGraphLogger.js.map", "import { __decorate } from \"../tslib.es6.js\";\nimport { serialize } from \"../Misc/decorators.js\";\nimport { RandomGUID } from \"../Misc/guid.js\";\nimport { defaultValueSerializationFunction } from \"./serialization.js\";\nimport { Observable } from \"../Misc/observable.js\";\nimport { GetFlowGraphAssetWithType } from \"./flowGraphAssetsContext.js\";\nimport { FlowGraphLogger } from \"./flowGraphLogger.js\";\n/**\n * The context represents the current state and execution of the flow graph.\n * It contains both user-defined variables, which are derived from\n * a more general variable definition, and execution variables that\n * are set by the blocks.\n */\nexport class FlowGraphContext {\n    /**\n     * Enable logging on this context\n     */\n    get enableLogging() {\n        return this._enableLogging;\n    }\n    set enableLogging(value) {\n        if (this._enableLogging === value) {\n            return;\n        }\n        this._enableLogging = value;\n        if (this._enableLogging) {\n            this.logger = new FlowGraphLogger();\n            this.logger.logToConsole = true;\n        }\n        else {\n            this.logger = null;\n        }\n    }\n    constructor(params) {\n        /**\n         * A randomly generated GUID for each context.\n         */\n        this.uniqueId = RandomGUID();\n        /**\n         * These are the variables defined by a user.\n         */\n        this._userVariables = {};\n        /**\n         * These are the variables set by the blocks.\n         */\n        this._executionVariables = {};\n        /**\n         * A context-specific global variables, available to all blocks in the context.\n         */\n        this._globalContextVariables = {};\n        /**\n         * These are the values for the data connection points\n         */\n        this._connectionValues = {};\n        /**\n         * These are blocks that have currently pending tasks/listeners that need to be cleaned up.\n         */\n        this._pendingBlocks = [];\n        /**\n         * A monotonically increasing ID for each execution.\n         * Incremented for every block executed.\n         */\n        this._executionId = 0;\n        /**\n         * Observable that is triggered when a node is executed.\n         */\n        this.onNodeExecutedObservable = new Observable();\n        /**\n         * Whether to treat data as right-handed.\n         * This is used when serializing data from a right-handed system, while running the context in a left-handed system, for example in glTF parsing.\n         * Default is false.\n         */\n        this.treatDataAsRightHanded = false;\n        this._enableLogging = false;\n        this._configuration = params;\n        this.assetsContext = params.assetsContext ?? params.scene;\n    }\n    /**\n     * Check if a user-defined variable is defined.\n     * @param name the name of the variable\n     * @returns true if the variable is defined\n     */\n    hasVariable(name) {\n        return name in this._userVariables;\n    }\n    /**\n     * Set a user-defined variable.\n     * @param name the name of the variable\n     * @param value the value of the variable\n     */\n    setVariable(name, value) {\n        this._userVariables[name] = value;\n        this.logger?.addLogItem({\n            time: Date.now(),\n            className: this.getClassName(),\n            uniqueId: this.uniqueId,\n            action: \"ContextVariableSet\" /* FlowGraphAction.ContextVariableSet */,\n            payload: {\n                name,\n                value,\n            },\n        });\n    }\n    /**\n     * Get an assets from the assets context based on its type and index in the array\n     * @param type The type of the asset\n     * @param index The index of the asset\n     * @returns The asset or null if not found\n     */\n    getAsset(type, index) {\n        return GetFlowGraphAssetWithType(this.assetsContext, type, index);\n    }\n    /**\n     * Get a user-defined variable.\n     * @param name the name of the variable\n     * @returns the value of the variable\n     */\n    getVariable(name) {\n        this.logger?.addLogItem({\n            time: Date.now(),\n            className: this.getClassName(),\n            uniqueId: this.uniqueId,\n            action: \"ContextVariableGet\" /* FlowGraphAction.ContextVariableGet */,\n            payload: {\n                name,\n                value: this._userVariables[name],\n            },\n        });\n        return this._userVariables[name];\n    }\n    /**\n     * Gets all user variables map\n     */\n    get userVariables() {\n        return this._userVariables;\n    }\n    /**\n     * Get the scene that the context belongs to.\n     * @returns the scene\n     */\n    getScene() {\n        return this._configuration.scene;\n    }\n    _getUniqueIdPrefixedName(obj, name) {\n        return `${obj.uniqueId}_${name}`;\n    }\n    /**\n     * @internal\n     * @param name name of the variable\n     * @param defaultValue default value to return if the variable is not defined\n     * @returns the variable value or the default value if the variable is not defined\n     */\n    _getGlobalContextVariable(name, defaultValue) {\n        this.logger?.addLogItem({\n            time: Date.now(),\n            className: this.getClassName(),\n            uniqueId: this.uniqueId,\n            action: \"GlobalVariableGet\" /* FlowGraphAction.GlobalVariableGet */,\n            payload: {\n                name,\n                defaultValue,\n                possibleValue: this._globalContextVariables[name],\n            },\n        });\n        if (this._hasGlobalContextVariable(name)) {\n            return this._globalContextVariables[name];\n        }\n        else {\n            return defaultValue;\n        }\n    }\n    /**\n     * Set a global context variable\n     * @internal\n     * @param name the name of the variable\n     * @param value the value of the variable\n     */\n    _setGlobalContextVariable(name, value) {\n        this.logger?.addLogItem({\n            time: Date.now(),\n            className: this.getClassName(),\n            uniqueId: this.uniqueId,\n            action: \"GlobalVariableSet\" /* FlowGraphAction.GlobalVariableSet */,\n            payload: { name, value },\n        });\n        this._globalContextVariables[name] = value;\n    }\n    /**\n     * Delete a global context variable\n     * @internal\n     * @param name the name of the variable\n     */\n    _deleteGlobalContextVariable(name) {\n        this.logger?.addLogItem({\n            time: Date.now(),\n            className: this.getClassName(),\n            uniqueId: this.uniqueId,\n            action: \"GlobalVariableDelete\" /* FlowGraphAction.GlobalVariableDelete */,\n            payload: { name },\n        });\n        delete this._globalContextVariables[name];\n    }\n    /**\n     * Check if a global context variable is defined\n     * @internal\n     * @param name the name of the variable\n     * @returns true if the variable is defined\n     */\n    _hasGlobalContextVariable(name) {\n        return name in this._globalContextVariables;\n    }\n    /**\n     * Set an internal execution variable\n     * @internal\n     * @param name\n     * @param value\n     */\n    _setExecutionVariable(block, name, value) {\n        this._executionVariables[this._getUniqueIdPrefixedName(block, name)] = value;\n    }\n    /**\n     * Get an internal execution variable\n     * @internal\n     * @param name\n     * @returns\n     */\n    _getExecutionVariable(block, name, defaultValue) {\n        if (this._hasExecutionVariable(block, name)) {\n            return this._executionVariables[this._getUniqueIdPrefixedName(block, name)];\n        }\n        else {\n            return defaultValue;\n        }\n    }\n    /**\n     * Delete an internal execution variable\n     * @internal\n     * @param block\n     * @param name\n     */\n    _deleteExecutionVariable(block, name) {\n        delete this._executionVariables[this._getUniqueIdPrefixedName(block, name)];\n    }\n    /**\n     * Check if an internal execution variable is defined\n     * @internal\n     * @param block\n     * @param name\n     * @returns\n     */\n    _hasExecutionVariable(block, name) {\n        return this._getUniqueIdPrefixedName(block, name) in this._executionVariables;\n    }\n    /**\n     * Check if a connection value is defined\n     * @internal\n     * @param connectionPoint\n     * @returns\n     */\n    _hasConnectionValue(connectionPoint) {\n        return connectionPoint.uniqueId in this._connectionValues;\n    }\n    /**\n     * Set a connection value\n     * @internal\n     * @param connectionPoint\n     * @param value\n     */\n    _setConnectionValue(connectionPoint, value) {\n        this._connectionValues[connectionPoint.uniqueId] = value;\n        this.logger?.addLogItem({\n            time: Date.now(),\n            className: this.getClassName(),\n            uniqueId: this.uniqueId,\n            action: \"SetConnectionValue\" /* FlowGraphAction.SetConnectionValue */,\n            payload: {\n                connectionPointId: connectionPoint.uniqueId,\n                value,\n            },\n        });\n    }\n    /**\n     * Set a connection value by key\n     * @internal\n     * @param key the key of the connection value\n     * @param value the value of the connection\n     */\n    _setConnectionValueByKey(key, value) {\n        this._connectionValues[key] = value;\n    }\n    /**\n     * Get a connection value\n     * @internal\n     * @param connectionPoint\n     * @returns\n     */\n    _getConnectionValue(connectionPoint) {\n        this.logger?.addLogItem({\n            time: Date.now(),\n            className: this.getClassName(),\n            uniqueId: this.uniqueId,\n            action: \"GetConnectionValue\" /* FlowGraphAction.GetConnectionValue */,\n            payload: {\n                connectionPointId: connectionPoint.uniqueId,\n                value: this._connectionValues[connectionPoint.uniqueId],\n            },\n        });\n        return this._connectionValues[connectionPoint.uniqueId];\n    }\n    /**\n     * Get the configuration\n     * @internal\n     * @param name\n     * @param value\n     */\n    get configuration() {\n        return this._configuration;\n    }\n    /**\n     * Check if there are any pending blocks in this context\n     * @returns true if there are pending blocks\n     */\n    get hasPendingBlocks() {\n        return this._pendingBlocks.length > 0;\n    }\n    /**\n     * Add a block to the list of blocks that have pending tasks.\n     * @internal\n     * @param block\n     */\n    _addPendingBlock(block) {\n        // check if block is already in the array\n        if (this._pendingBlocks.includes(block)) {\n            return;\n        }\n        this._pendingBlocks.push(block);\n        // sort pending blocks by priority\n        this._pendingBlocks.sort((a, b) => a.priority - b.priority);\n    }\n    /**\n     * Remove a block from the list of blocks that have pending tasks.\n     * @internal\n     * @param block\n     */\n    _removePendingBlock(block) {\n        const index = this._pendingBlocks.indexOf(block);\n        if (index !== -1) {\n            this._pendingBlocks.splice(index, 1);\n        }\n    }\n    /**\n     * Clear all pending blocks.\n     * @internal\n     */\n    _clearPendingBlocks() {\n        for (const block of this._pendingBlocks) {\n            block._cancelPendingTasks(this);\n        }\n        this._pendingBlocks.length = 0;\n    }\n    /**\n     * @internal\n     * Function that notifies the node executed observable\n     * @param node\n     */\n    _notifyExecuteNode(node) {\n        this.onNodeExecutedObservable.notifyObservers(node);\n        this.logger?.addLogItem({\n            time: Date.now(),\n            className: node.getClassName(),\n            uniqueId: node.uniqueId,\n            action: \"ExecuteBlock\" /* FlowGraphAction.ExecuteBlock */,\n        });\n    }\n    _notifyOnTick(framePayload) {\n        // set the values as global variables\n        this._setGlobalContextVariable(\"timeSinceStart\", framePayload.timeSinceStart);\n        this._setGlobalContextVariable(\"deltaTime\", framePayload.deltaTime);\n        // iterate the pending blocks and run each one's onFrame function\n        for (const block of this._pendingBlocks) {\n            block._executeOnTick?.(this);\n        }\n    }\n    /**\n     * @internal\n     */\n    _increaseExecutionId() {\n        this._executionId++;\n    }\n    /**\n     * A monotonically increasing ID for each execution.\n     * Incremented for every block executed.\n     */\n    get executionId() {\n        return this._executionId;\n    }\n    /**\n     * Serializes a context\n     * @param serializationObject the object to write the values in\n     * @param valueSerializationFunction a function to serialize complex values\n     */\n    serialize(serializationObject = {}, valueSerializationFunction = defaultValueSerializationFunction) {\n        serializationObject.uniqueId = this.uniqueId;\n        serializationObject._userVariables = {};\n        for (const key in this._userVariables) {\n            valueSerializationFunction(key, this._userVariables[key], serializationObject._userVariables);\n        }\n        serializationObject._connectionValues = {};\n        for (const key in this._connectionValues) {\n            valueSerializationFunction(key, this._connectionValues[key], serializationObject._connectionValues);\n        }\n        // serialize assets context, if not scene\n        if (this.assetsContext !== this.getScene()) {\n            serializationObject._assetsContext = {\n                meshes: this.assetsContext.meshes.map((m) => m.id),\n                materials: this.assetsContext.materials.map((m) => m.id),\n                textures: this.assetsContext.textures.map((m) => m.name),\n                animations: this.assetsContext.animations.map((m) => m.name),\n                lights: this.assetsContext.lights.map((m) => m.id),\n                cameras: this.assetsContext.cameras.map((m) => m.id),\n                sounds: this.assetsContext.sounds?.map((m) => m.name),\n                skeletons: this.assetsContext.skeletons.map((m) => m.id),\n                particleSystems: this.assetsContext.particleSystems.map((m) => m.name),\n                geometries: this.assetsContext.geometries.map((m) => m.id),\n                multiMaterials: this.assetsContext.multiMaterials.map((m) => m.id),\n                transformNodes: this.assetsContext.transformNodes.map((m) => m.id),\n            };\n        }\n    }\n    /**\n     * @returns the class name of the object.\n     */\n    getClassName() {\n        return \"FlowGraphContext\";\n    }\n}\n__decorate([\n    serialize()\n], FlowGraphContext.prototype, \"uniqueId\", void 0);\n//# sourceMappingURL=flowGraphContext.js.map", "import { RandomGUID } from \"../Misc/guid.js\";\n/**\n * The type of a connection point - input or output.\n */\nexport var FlowGraphConnectionType;\n(function (FlowGraphConnectionType) {\n    FlowGraphConnectionType[FlowGraphConnectionType[\"Input\"] = 0] = \"Input\";\n    FlowGraphConnectionType[FlowGraphConnectionType[\"Output\"] = 1] = \"Output\";\n})(FlowGraphConnectionType || (FlowGraphConnectionType = {}));\n/**\n * The base connection class.\n */\nexport class FlowGraphConnection {\n    constructor(name, _connectionType, \n    /* @internal */ _ownerBlock) {\n        this._ownerBlock = _ownerBlock;\n        /** @internal */\n        this._connectedPoint = [];\n        /**\n         * A uniquely identifying string for the connection.\n         */\n        this.uniqueId = RandomGUID();\n        /**\n         * Used for parsing connections.\n         * @internal\n         */\n        // disable warning as this is used for parsing\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        this.connectedPointIds = [];\n        this.name = name;\n        this._connectionType = _connectionType;\n    }\n    /**\n     * The type of the connection\n     */\n    get connectionType() {\n        return this._connectionType;\n    }\n    /**\n     * @internal\n     * Override this to indicate if a point can connect to more than one point.\n     */\n    _isSingularConnection() {\n        return true;\n    }\n    /**\n     * Returns if a point is connected to any other point.\n     * @returns boolean indicating if the point is connected.\n     */\n    isConnected() {\n        return this._connectedPoint.length > 0;\n    }\n    /**\n     * Connects two connections together.\n     * @param point the connection to connect to.\n     */\n    connectTo(point) {\n        if (this._connectionType === point._connectionType) {\n            throw new Error(`Cannot connect two points of type ${this.connectionType}`);\n        }\n        if ((this._isSingularConnection() && this._connectedPoint.length > 0) || (point._isSingularConnection() && point._connectedPoint.length > 0)) {\n            throw new Error(\"Max number of connections for point reached\");\n        }\n        this._connectedPoint.push(point);\n        point._connectedPoint.push(this);\n    }\n    /**\n     * Disconnects two connections.\n     * @param point the connection to disconnect from.\n     * @param removeFromLocal if true, the connection will be removed from the local connection list.\n     */\n    disconnectFrom(point, removeFromLocal = true) {\n        const indexLocal = this._connectedPoint.indexOf(point);\n        const indexConnected = point._connectedPoint.indexOf(this);\n        if (indexLocal === -1 || indexConnected === -1) {\n            return;\n        }\n        if (removeFromLocal) {\n            this._connectedPoint.splice(indexLocal, 1);\n        }\n        point._connectedPoint.splice(indexConnected, 1);\n    }\n    /**\n     * Disconnects all connected points.\n     */\n    disconnectFromAll() {\n        for (const point of this._connectedPoint) {\n            this.disconnectFrom(point, false);\n        }\n        this._connectedPoint.length = 0;\n    }\n    dispose() {\n        for (const point of this._connectedPoint) {\n            this.disconnectFrom(point);\n        }\n    }\n    /**\n     * Saves the connection to a JSON object.\n     * @param serializationObject the object to serialize to.\n     */\n    serialize(serializationObject = {}) {\n        serializationObject.uniqueId = this.uniqueId;\n        serializationObject.name = this.name;\n        serializationObject._connectionType = this._connectionType;\n        serializationObject.connectedPointIds = [];\n        serializationObject.className = this.getClassName();\n        for (const point of this._connectedPoint) {\n            serializationObject.connectedPointIds.push(point.uniqueId);\n        }\n    }\n    /**\n     * @returns class name of the connection.\n     */\n    getClassName() {\n        return \"FGConnection\";\n    }\n    /**\n     * Deserialize from a object into this\n     * @param serializationObject the object to deserialize from.\n     */\n    deserialize(serializationObject) {\n        this.uniqueId = serializationObject.uniqueId;\n        this.name = serializationObject.name;\n        this._connectionType = serializationObject._connectionType;\n        this.connectedPointIds = serializationObject.connectedPointIds;\n    }\n}\n//# sourceMappingURL=flowGraphConnection.js.map", "import { RegisterClass } from \"../Misc/typeStore.js\";\nimport { FlowGraphConnection } from \"./flowGraphConnection.js\";\nimport { Observable } from \"../Misc/observable.js\";\nimport { defaultValueSerializationFunction } from \"./serialization.js\";\n/**\n * Represents a connection point for data.\n * An unconnected input point can have a default value.\n * An output point will only have a value if it is connected to an input point. Furthermore,\n * if the point belongs to a \"function\" node, the node will run its function to update the value.\n */\nexport class FlowGraphDataConnection extends FlowGraphConnection {\n    /**\n     * Create a new data connection point.\n     * @param name the name of the connection\n     * @param connectionType the type of the connection\n     * @param ownerBlock the block that owns this connection\n     * @param richType the type of the data in this block\n     * @param _defaultValue the default value of the connection\n     * @param _optional if the connection is optional\n     */\n    constructor(name, connectionType, ownerBlock, \n    /**\n     * the type of the data in this block\n     */\n    richType, \n    /**\n     * [any] the default value of the connection\n     */\n    _defaultValue = richType.defaultValue, \n    /**\n     * [false] if the connection is optional\n     */\n    _optional = false) {\n        super(name, connectionType, ownerBlock);\n        this.richType = richType;\n        this._defaultValue = _defaultValue;\n        this._optional = _optional;\n        this._isDisabled = false;\n        /**\n         * This is used for debugging purposes! It is the last value that was set to this connection with ANY context.\n         * Do not use this value for anything else, as it might be wrong if used in a different context.\n         */\n        this._lastValue = null;\n        /**\n         * a data transformer function, if needed.\n         * This can be used, for example, to force seconds into milliseconds output, if it makes sense in your case.\n         */\n        this.dataTransformer = null;\n        /**\n         * An observable that is triggered when the value of the connection changes.\n         */\n        this.onValueChangedObservable = new Observable();\n    }\n    /**\n     * Whether or not the connection is optional.\n     * Currently only used for UI control.\n     */\n    get optional() {\n        return this._optional;\n    }\n    /**\n     * is this connection disabled\n     * If the connection is disabled you will not be able to connect anything to it.\n     */\n    get isDisabled() {\n        return this._isDisabled;\n    }\n    set isDisabled(value) {\n        if (this._isDisabled === value) {\n            return;\n        }\n        this._isDisabled = value;\n        if (this._isDisabled) {\n            this.disconnectFromAll();\n        }\n    }\n    /**\n     * An output data block can connect to multiple input data blocks,\n     * but an input data block can only connect to one output data block.\n     * @returns true if the connection is singular\n     */\n    _isSingularConnection() {\n        return this.connectionType === 0 /* FlowGraphConnectionType.Input */;\n    }\n    /**\n     * Set the value of the connection in a specific context.\n     * @param value the value to set\n     * @param context the context to which the value is set\n     */\n    setValue(value, context) {\n        // check if the value is different\n        if (context._getConnectionValue(this) === value) {\n            return;\n        }\n        context._setConnectionValue(this, value);\n        this.onValueChangedObservable.notifyObservers(value);\n    }\n    /**\n     * Reset the value of the connection to the default value.\n     * @param context the context in which the value is reset\n     */\n    resetToDefaultValue(context) {\n        context._setConnectionValue(this, this._defaultValue);\n    }\n    /**\n     * Connect this point to another point.\n     * @param point the point to connect to.\n     */\n    connectTo(point) {\n        if (this._isDisabled) {\n            return;\n        }\n        super.connectTo(point);\n    }\n    _getValueOrDefault(context) {\n        const val = context._getConnectionValue(this) ?? this._defaultValue;\n        return this.dataTransformer ? this.dataTransformer(val) : val;\n    }\n    /**\n     * Gets the value of the connection in a specific context.\n     * @param context the context from which the value is retrieved\n     * @returns the value of the connection\n     */\n    getValue(context) {\n        if (this.connectionType === 1 /* FlowGraphConnectionType.Output */) {\n            context._notifyExecuteNode(this._ownerBlock);\n            this._ownerBlock._updateOutputs(context);\n            const value = this._getValueOrDefault(context);\n            this._lastValue = value;\n            return this.richType.typeTransformer ? this.richType.typeTransformer(value) : value;\n        }\n        const value = !this.isConnected() ? this._getValueOrDefault(context) : this._connectedPoint[0].getValue(context);\n        this._lastValue = value;\n        return this.richType.typeTransformer ? this.richType.typeTransformer(value) : value;\n    }\n    /**\n     * @internal\n     */\n    _getLastValue() {\n        return this._lastValue;\n    }\n    /**\n     * @returns class name of the object.\n     */\n    getClassName() {\n        return \"FlowGraphDataConnection\";\n    }\n    /**\n     * Serializes this object.\n     * @param serializationObject the object to serialize to\n     */\n    serialize(serializationObject = {}) {\n        super.serialize(serializationObject);\n        serializationObject.richType = {};\n        this.richType.serialize(serializationObject.richType);\n        serializationObject.optional = this._optional;\n        defaultValueSerializationFunction(\"defaultValue\", this._defaultValue, serializationObject);\n    }\n}\nRegisterClass(\"FlowGraphDataConnection\", FlowGraphDataConnection);\n//# sourceMappingURL=flowGraphDataConnection.js.map", "import { RandomGUID } from \"../Misc/guid.js\";\nimport { FlowGraphDataConnection } from \"./flowGraphDataConnection.js\";\nimport { defaultValueSerializationFunction } from \"./serialization.js\";\n/**\n * A block in a flow graph. The most basic form\n * of a block has inputs and outputs that contain\n * data.\n */\nexport class FlowGraphBlock {\n    /** Constructor is protected so only subclasses can be instantiated\n     * @param config optional configuration for this block\n     * @internal - do not use directly. Extend this class instead.\n     */\n    constructor(\n    /**\n     * the configuration of the block\n     */\n    config) {\n        this.config = config;\n        /**\n         * A randomly generated GUID for each block.\n         */\n        this.uniqueId = RandomGUID();\n        this.name = this.config?.name ?? this.getClassName();\n        this.dataInputs = [];\n        this.dataOutputs = [];\n    }\n    /**\n     * @internal\n     * This function is called when the block needs to update its output flows.\n     * @param _context the context in which it is running\n     */\n    _updateOutputs(_context) {\n        // empty by default, overridden in data blocks\n    }\n    /**\n     * Registers a data input on the block.\n     * @param name the name of the input\n     * @param richType the type of the input\n     * @param defaultValue optional default value of the input. If not set, the rich type's default value will be used.\n     * @returns the created connection\n     */\n    registerDataInput(name, richType, defaultValue) {\n        const input = new FlowGraphDataConnection(name, 0 /* FlowGraphConnectionType.Input */, this, richType, defaultValue);\n        this.dataInputs.push(input);\n        return input;\n    }\n    /**\n     * Registers a data output on the block.\n     * @param name the name of the input\n     * @param richType the type of the input\n     * @param defaultValue optional default value of the input. If not set, the rich type's default value will be used.\n     * @returns the created connection\n     */\n    registerDataOutput(name, richType, defaultValue) {\n        const output = new FlowGraphDataConnection(name, 1 /* FlowGraphConnectionType.Output */, this, richType, defaultValue);\n        this.dataOutputs.push(output);\n        return output;\n    }\n    /**\n     * Given the name of a data input, returns the connection if it exists\n     * @param name the name of the input\n     * @returns the connection if it exists, undefined otherwise\n     */\n    getDataInput(name) {\n        return this.dataInputs.find((i) => i.name === name);\n    }\n    /**\n     * Given the name of a data output, returns the connection if it exists\n     * @param name the name of the output\n     * @returns the connection if it exists, undefined otherwise\n     */\n    getDataOutput(name) {\n        return this.dataOutputs.find((i) => i.name === name);\n    }\n    /**\n     * Serializes this block\n     * @param serializationObject the object to serialize to\n     * @param _valueSerializeFunction a function that serializes a specific value\n     */\n    serialize(serializationObject = {}, _valueSerializeFunction = defaultValueSerializationFunction) {\n        serializationObject.uniqueId = this.uniqueId;\n        serializationObject.config = {};\n        if (this.config) {\n            const config = this.config;\n            Object.keys(this.config).forEach((key) => {\n                _valueSerializeFunction(key, config[key], serializationObject.config);\n            });\n        }\n        serializationObject.dataInputs = [];\n        serializationObject.dataOutputs = [];\n        serializationObject.className = this.getClassName();\n        for (const input of this.dataInputs) {\n            const serializedInput = {};\n            input.serialize(serializedInput);\n            serializationObject.dataInputs.push(serializedInput);\n        }\n        for (const output of this.dataOutputs) {\n            const serializedOutput = {};\n            output.serialize(serializedOutput);\n            serializationObject.dataOutputs.push(serializedOutput);\n        }\n    }\n    /**\n     * Deserializes this block\n     * @param _serializationObject the object to deserialize from\n     */\n    deserialize(_serializationObject) {\n        // no-op by default\n    }\n    _log(context, action, payload) {\n        context.logger?.addLogItem({\n            action,\n            payload,\n            className: this.getClassName(),\n            uniqueId: this.uniqueId,\n        });\n    }\n    /**\n     * Gets the class name of this block\n     * @returns the class name\n     */\n    getClassName() {\n        return \"FlowGraphBlock\";\n    }\n}\n//# sourceMappingURL=flowGraphBlock.js.map", "import { FlowGraphConnection } from \"./flowGraphConnection.js\";\nimport { RegisterClass } from \"../Misc/typeStore.js\";\n/**\n * Represents a connection point for a signal.\n * When an output point is activated, it will activate the connected input point.\n * When an input point is activated, it will execute the block it belongs to.\n */\nexport class FlowGraphSignalConnection extends FlowGraphConnection {\n    constructor() {\n        super(...arguments);\n        /**\n         * The priority of the signal. Signals with higher priority will be executed first.\n         * Set priority before adding the connection as sorting happens only when the connection is added.\n         */\n        this.priority = 0;\n    }\n    _isSingularConnection() {\n        return false;\n    }\n    connectTo(point) {\n        super.connectTo(point);\n        // sort according to priority to handle execution order\n        this._connectedPoint.sort((a, b) => b.priority - a.priority);\n    }\n    /**\n     * @internal\n     */\n    _activateSignal(context) {\n        context.logger?.addLogItem({\n            action: \"ActivateSignal\" /* FlowGraphAction.ActivateSignal */,\n            className: this._ownerBlock.getClassName(),\n            uniqueId: this._ownerBlock.uniqueId,\n            payload: {\n                connectionType: this.connectionType,\n                name: this.name,\n            },\n        });\n        if (this.connectionType === 0 /* FlowGraphConnectionType.Input */) {\n            context._notifyExecuteNode(this._ownerBlock);\n            this._ownerBlock._execute(context, this);\n            context._increaseExecutionId();\n        }\n        else {\n            for (const connectedPoint of this._connectedPoint) {\n                connectedPoint._activateSignal(context);\n            }\n        }\n    }\n}\nRegisterClass(\"FlowGraphSignalConnection\", FlowGraphSignalConnection);\n//# sourceMappingURL=flowGraphSignalConnection.js.map", "import { FlowGraphBlock } from \"./flowGraphBlock.js\";\nimport { FlowGraphSignalConnection } from \"./flowGraphSignalConnection.js\";\n/**\n * A block that executes some action. Always has an input signal (which is not used by event blocks).\n * Can have one or more output signals.\n */\nexport class FlowGraphExecutionBlock extends FlowGraphBlock {\n    constructor(config) {\n        super(config);\n        /**\n         * The priority of the block. Higher priority blocks will be executed first.\n         * Note that priority cannot be change AFTER the block was added as sorting happens when the block is added to the execution queue.\n         */\n        this.priority = 0;\n        this.signalInputs = [];\n        this.signalOutputs = [];\n        this.in = this._registerSignalInput(\"in\");\n        this.error = this._registerSignalOutput(\"error\");\n    }\n    _registerSignalInput(name) {\n        const input = new FlowGraphSignalConnection(name, 0 /* FlowGraphConnectionType.Input */, this);\n        this.signalInputs.push(input);\n        return input;\n    }\n    _registerSignalOutput(name) {\n        const output = new FlowGraphSignalConnection(name, 1 /* FlowGraphConnectionType.Output */, this);\n        this.signalOutputs.push(output);\n        return output;\n    }\n    _unregisterSignalInput(name) {\n        const index = this.signalInputs.findIndex((input) => input.name === name);\n        if (index !== -1) {\n            this.signalInputs[index].dispose();\n            this.signalInputs.splice(index, 1);\n        }\n    }\n    _unregisterSignalOutput(name) {\n        const index = this.signalOutputs.findIndex((output) => output.name === name);\n        if (index !== -1) {\n            this.signalOutputs[index].dispose();\n            this.signalOutputs.splice(index, 1);\n        }\n    }\n    _reportError(context, error) {\n        this.error.payload = typeof error === \"string\" ? new Error(error) : error;\n        this.error._activateSignal(context);\n    }\n    /**\n     * Given a name of a signal input, return that input if it exists\n     * @param name the name of the input\n     * @returns if the input exists, the input. Otherwise, undefined.\n     */\n    getSignalInput(name) {\n        return this.signalInputs.find((input) => input.name === name);\n    }\n    /**\n     * Given a name of a signal output, return that input if it exists\n     * @param name the name of the input\n     * @returns if the input exists, the input. Otherwise, undefined.\n     */\n    getSignalOutput(name) {\n        return this.signalOutputs.find((output) => output.name === name);\n    }\n    /**\n     * Serializes this block\n     * @param serializationObject the object to serialize in\n     */\n    serialize(serializationObject = {}) {\n        super.serialize(serializationObject);\n        serializationObject.signalInputs = [];\n        serializationObject.signalOutputs = [];\n        for (const input of this.signalInputs) {\n            const serializedInput = {};\n            input.serialize(serializedInput);\n            serializationObject.signalInputs.push(serializedInput);\n        }\n        for (const output of this.signalOutputs) {\n            const serializedOutput = {};\n            output.serialize(serializedOutput);\n            serializationObject.signalOutputs.push(serializedOutput);\n        }\n    }\n    /**\n     * Deserializes from an object\n     * @param serializationObject the object to deserialize from\n     */\n    deserialize(serializationObject) {\n        for (let i = 0; i < serializationObject.signalInputs.length; i++) {\n            const signalInput = this.getSignalInput(serializationObject.signalInputs[i].name);\n            if (signalInput) {\n                signalInput.deserialize(serializationObject.signalInputs[i]);\n            }\n            else {\n                throw new Error(\"Could not find signal input with name \" + serializationObject.signalInputs[i].name + \" in block \" + serializationObject.className);\n            }\n        }\n        for (let i = 0; i < serializationObject.signalOutputs.length; i++) {\n            const signalOutput = this.getSignalOutput(serializationObject.signalOutputs[i].name);\n            if (signalOutput) {\n                signalOutput.deserialize(serializationObject.signalOutputs[i]);\n            }\n            else {\n                throw new Error(\"Could not find signal output with name \" + serializationObject.signalOutputs[i].name + \" in block \" + serializationObject.className);\n            }\n        }\n    }\n    /**\n     * @returns the class name\n     */\n    getClassName() {\n        return \"FlowGraphExecutionBlock\";\n    }\n}\n//# sourceMappingURL=flowGraphExecutionBlock.js.map", "import { PointerEventTypes } from \"../Events/pointerEvents.js\";\nimport { Observable } from \"../Misc/observable.js\";\n/**\n * This class is responsible for coordinating the events that are triggered in the scene.\n * It registers all observers needed to track certain events and triggers the blocks that are listening to them.\n * Abstracting the events from the class will allow us to easily change the events that are being listened to, and trigger them in any order.\n */\nexport class FlowGraphSceneEventCoordinator {\n    constructor(scene) {\n        /**\n         * register to this observable to get flow graph event notifications.\n         */\n        this.onEventTriggeredObservable = new Observable();\n        /**\n         * Was scene-ready already triggered?\n         */\n        this.sceneReadyTriggered = false;\n        this._pointerUnderMeshState = {};\n        this._startingTime = 0;\n        this._scene = scene;\n        this._initialize();\n    }\n    _initialize() {\n        this._sceneReadyObserver = this._scene.onReadyObservable.add(() => {\n            if (!this.sceneReadyTriggered) {\n                this.onEventTriggeredObservable.notifyObservers({ type: \"SceneReady\" /* FlowGraphEventType.SceneReady */ });\n                this.sceneReadyTriggered = true;\n            }\n        });\n        this._sceneDisposeObserver = this._scene.onDisposeObservable.add(() => {\n            this.onEventTriggeredObservable.notifyObservers({ type: \"SceneDispose\" /* FlowGraphEventType.SceneDispose */ });\n        });\n        this._sceneOnBeforeRenderObserver = this._scene.onBeforeRenderObservable.add(() => {\n            const deltaTime = this._scene.getEngine().getDeltaTime() / 1000; // set in seconds\n            this.onEventTriggeredObservable.notifyObservers({\n                type: \"SceneBeforeRender\" /* FlowGraphEventType.SceneBeforeRender */,\n                payload: {\n                    timeSinceStart: this._startingTime,\n                    deltaTime,\n                },\n            });\n            this._startingTime += deltaTime;\n        });\n        this._meshPickedObserver = this._scene.onPointerObservable.add((pointerInfo) => {\n            this.onEventTriggeredObservable.notifyObservers({ type: \"MeshPick\" /* FlowGraphEventType.MeshPick */, payload: pointerInfo });\n        }, PointerEventTypes.POINTERPICK); // should it be pointerdown?\n        this._meshUnderPointerObserver = this._scene.onMeshUnderPointerUpdatedObservable.add((data) => {\n            // check if the data has changed. Check the state of the last change and see if it is a mesh or null.\n            // if it is a mesh and the previous state was null, trigger over event. If it is null and the previous state was a mesh, trigger out event.\n            // if it is a mesh and the previous state was a mesh, trigger out from the old mesh and over the new mesh\n            // if it is null and the previous state was null, do nothing.\n            const pointerId = data.pointerId;\n            const mesh = data.mesh;\n            const previousState = this._pointerUnderMeshState[pointerId];\n            if (!previousState && mesh) {\n                this.onEventTriggeredObservable.notifyObservers({ type: \"PointerOver\" /* FlowGraphEventType.PointerOver */, payload: { pointerId, mesh } });\n            }\n            else if (previousState && !mesh) {\n                this.onEventTriggeredObservable.notifyObservers({ type: \"PointerOut\" /* FlowGraphEventType.PointerOut */, payload: { pointerId, mesh: previousState } });\n            }\n            else if (previousState && mesh && previousState !== mesh) {\n                this.onEventTriggeredObservable.notifyObservers({ type: \"PointerOut\" /* FlowGraphEventType.PointerOut */, payload: { pointerId, mesh: previousState, over: mesh } });\n                this.onEventTriggeredObservable.notifyObservers({ type: \"PointerOver\" /* FlowGraphEventType.PointerOver */, payload: { pointerId, mesh, out: previousState } });\n            }\n            this._pointerUnderMeshState[pointerId] = mesh;\n        }, PointerEventTypes.POINTERMOVE);\n    }\n    dispose() {\n        this._sceneDisposeObserver?.remove();\n        this._sceneReadyObserver?.remove();\n        this._sceneOnBeforeRenderObserver?.remove();\n        this._meshPickedObserver?.remove();\n        this._meshUnderPointerObserver?.remove();\n        this.onEventTriggeredObservable.clear();\n    }\n}\n//# sourceMappingURL=flowGraphSceneEventCoordinator.js.map", "/**\n * @internal\n * Returns if mesh1 is a descendant of mesh2\n * @param mesh1\n * @param mesh2\n * @returns\n */\nexport function _isADescendantOf(mesh1, mesh2) {\n    return !!(mesh1.parent && (mesh1.parent === mesh2 || _isADescendantOf(mesh1.parent, mesh2)));\n}\n/**\n * @internal\n */\nexport function _getClassNameOf(v) {\n    if (v.getClassName) {\n        return v.getClassName();\n    }\n    return;\n}\n/**\n * @internal\n * Check if two classname are the same and are vector classes.\n * @param className the first class name\n * @param className2 the second class name\n * @returns whether the two class names are the same and are vector classes.\n */\nexport function _areSameVectorClass(className, className2) {\n    return className === className2 && (className === \"Vector2\" /* FlowGraphTypes.Vector2 */ || className === \"Vector3\" /* FlowGraphTypes.Vector3 */ || className === \"Vector4\" /* FlowGraphTypes.Vector4 */);\n}\n/**\n * @internal\n * Check if two classname are the same and are matrix classes.\n * @param className the first class name\n * @param className2 the second class name\n * @returns whether the two class names are the same and are matrix classes.\n */\nexport function _areSameMatrixClass(className, className2) {\n    return className === className2 && (className === \"Matrix\" /* FlowGraphTypes.Matrix */ || className === \"Matrix2D\" /* FlowGraphTypes.Matrix2D */ || className === \"Matrix3D\" /* FlowGraphTypes.Matrix3D */);\n}\n/**\n * @internal\n * Check if two classname are the same and are integer classes.\n * @param className the first class name\n * @param className2 the second class name\n * @returns whether the two class names are the same and are integer classes.\n */\nexport function _areSameIntegerClass(className, className2) {\n    return className === \"FlowGraphInteger\" && className2 === \"FlowGraphInteger\";\n}\n/**\n * Check if an object has a numeric value.\n * @param a the object to check if it is a number.\n * @param validIfNaN whether to consider NaN as a valid number.\n * @returns whether a is a FlowGraphNumber (Integer or number).\n */\nexport function isNumeric(a, validIfNaN) {\n    const isNumeric = typeof a === \"number\" || typeof a?.value === \"number\";\n    if (isNumeric && !validIfNaN) {\n        return !isNaN(getNumericValue(a));\n    }\n    return isNumeric;\n}\n/**\n * Get the numeric value of a FlowGraphNumber.\n * @param a the object to get the numeric value from.\n * @returns the numeric value.\n */\nexport function getNumericValue(a) {\n    return typeof a === \"number\" ? a : a.value;\n}\n//# sourceMappingURL=utils.js.map", "import { Observable } from \"../Misc/observable.js\";\nimport { FlowGraphContext } from \"./flowGraphContext.js\";\nimport { FlowGraphExecutionBlock } from \"./flowGraphExecutionBlock.js\";\nimport { FlowGraphSceneEventCoordinator } from \"./flowGraphSceneEventCoordinator.js\";\nimport { _isADescendantOf } from \"./utils.js\";\nexport var FlowGraphState;\n(function (FlowGraphState) {\n    /**\n     * The graph is stopped\n     */\n    FlowGraphState[FlowGraphState[\"Stopped\"] = 0] = \"Stopped\";\n    /**\n     * The graph is running\n     */\n    FlowGraphState[FlowGraphState[\"Started\"] = 1] = \"Started\";\n})(FlowGraphState || (FlowGraphState = {}));\n/**\n * Class used to represent a flow graph.\n * A flow graph is a graph of blocks that can be used to create complex logic.\n * Blocks can be added to the graph and connected to each other.\n * The graph can then be started, which will init and start all of its event blocks.\n *\n * @experimental FlowGraph is still in development and is subject to change.\n */\nexport class FlowGraph {\n    /**\n     * The state of the graph\n     */\n    get state() {\n        return this._state;\n    }\n    /**\n     * The state of the graph\n     */\n    set state(value) {\n        this._state = value;\n        this.onStateChangedObservable.notifyObservers(value);\n    }\n    /**\n     * Construct a Flow Graph\n     * @param params construction parameters. currently only the scene\n     */\n    constructor(params) {\n        /**\n         * An observable that is triggered when the state of the graph changes.\n         */\n        this.onStateChangedObservable = new Observable();\n        /** @internal */\n        this._eventBlocks = {\n            [\"SceneReady\" /* FlowGraphEventType.SceneReady */]: [],\n            [\"SceneDispose\" /* FlowGraphEventType.SceneDispose */]: [],\n            [\"SceneBeforeRender\" /* FlowGraphEventType.SceneBeforeRender */]: [],\n            [\"MeshPick\" /* FlowGraphEventType.MeshPick */]: [],\n            [\"PointerDown\" /* FlowGraphEventType.PointerDown */]: [],\n            [\"PointerUp\" /* FlowGraphEventType.PointerUp */]: [],\n            [\"PointerMove\" /* FlowGraphEventType.PointerMove */]: [],\n            [\"PointerOver\" /* FlowGraphEventType.PointerOver */]: [],\n            [\"PointerOut\" /* FlowGraphEventType.PointerOut */]: [],\n            [\"SceneAfterRender\" /* FlowGraphEventType.SceneAfterRender */]: [],\n            [\"NoTrigger\" /* FlowGraphEventType.NoTrigger */]: [],\n        };\n        this._executionContexts = [];\n        /**\n         * The state of the graph\n         */\n        this._state = 0 /* FlowGraphState.Stopped */;\n        this._scene = params.scene;\n        this._sceneEventCoordinator = new FlowGraphSceneEventCoordinator(this._scene);\n        this._coordinator = params.coordinator;\n        this._eventObserver = this._sceneEventCoordinator.onEventTriggeredObservable.add((event) => {\n            for (const context of this._executionContexts) {\n                const order = this._getContextualOrder(event.type, context);\n                for (const block of order) {\n                    // iterate contexts\n                    if (!block._executeEvent(context, event.payload)) {\n                        break;\n                    }\n                }\n            }\n            // custom behavior(s) of specific events\n            switch (event.type) {\n                case \"SceneReady\" /* FlowGraphEventType.SceneReady */:\n                    this._sceneEventCoordinator.sceneReadyTriggered = true;\n                    break;\n                case \"SceneBeforeRender\" /* FlowGraphEventType.SceneBeforeRender */:\n                    for (const context of this._executionContexts) {\n                        context._notifyOnTick(event.payload);\n                    }\n                    break;\n                case \"SceneDispose\" /* FlowGraphEventType.SceneDispose */:\n                    this.dispose();\n                    break;\n            }\n        });\n    }\n    /**\n     * Create a context. A context represents one self contained execution for the graph, with its own variables.\n     * @returns the context, where you can get and set variables\n     */\n    createContext() {\n        const context = new FlowGraphContext({ scene: this._scene, coordinator: this._coordinator });\n        this._executionContexts.push(context);\n        return context;\n    }\n    /**\n     * Returns the execution context at a given index\n     * @param index the index of the context\n     * @returns the execution context at that index\n     */\n    getContext(index) {\n        return this._executionContexts[index];\n    }\n    /**\n     * Add an event block. When the graph is started, it will start listening to events\n     * from the block and execute the graph when they are triggered.\n     * @param block the event block to be added\n     */\n    addEventBlock(block) {\n        if (block.type === \"PointerOver\" /* FlowGraphEventType.PointerOver */ || block.type === \"PointerOut\" /* FlowGraphEventType.PointerOut */) {\n            this._scene.constantlyUpdateMeshUnderPointer = true;\n        }\n        // don't add if NoTrigger, but still start the pending tasks\n        if (block.type !== \"NoTrigger\" /* FlowGraphEventType.NoTrigger */) {\n            this._eventBlocks[block.type].push(block);\n        }\n        // if already started, sort and add to the pending\n        if (this.state === 1 /* FlowGraphState.Started */) {\n            for (const context of this._executionContexts) {\n                block._startPendingTasks(context);\n            }\n        }\n        else {\n            this.onStateChangedObservable.addOnce((state) => {\n                if (state === 1 /* FlowGraphState.Started */) {\n                    for (const context of this._executionContexts) {\n                        block._startPendingTasks(context);\n                    }\n                }\n            });\n        }\n    }\n    /**\n     * Starts the flow graph. Initializes the event blocks and starts listening to events.\n     */\n    start() {\n        if (this.state === 1 /* FlowGraphState.Started */) {\n            return;\n        }\n        if (this._executionContexts.length === 0) {\n            this.createContext();\n        }\n        this.onStateChangedObservable.add((state) => {\n            if (state === 1 /* FlowGraphState.Started */) {\n                this._startPendingEvents();\n                // the only event we need to check is the scene ready event. If the scene is already ready when the graph starts, we should start the pending tasks.\n                if (this._scene.isReady(true)) {\n                    this._sceneEventCoordinator.onEventTriggeredObservable.notifyObservers({ type: \"SceneReady\" /* FlowGraphEventType.SceneReady */ });\n                }\n            }\n        });\n        this.state = 1 /* FlowGraphState.Started */;\n    }\n    _startPendingEvents() {\n        for (const context of this._executionContexts) {\n            for (const type in this._eventBlocks) {\n                const order = this._getContextualOrder(type, context);\n                for (const block of order) {\n                    block._startPendingTasks(context);\n                }\n            }\n        }\n    }\n    _getContextualOrder(type, context) {\n        const order = this._eventBlocks[type].sort((a, b) => b.initPriority - a.initPriority);\n        if (type === \"MeshPick\" /* FlowGraphEventType.MeshPick */) {\n            const meshPickOrder = [];\n            for (const block1 of order) {\n                // If the block is a mesh pick, guarantee that picks of children meshes come before picks of parent meshes\n                const mesh1 = block1.asset.getValue(context);\n                let i = 0;\n                for (; i < order.length; i++) {\n                    const block2 = order[i];\n                    const mesh2 = block2.asset.getValue(context);\n                    if (mesh1 && mesh2 && _isADescendantOf(mesh1, mesh2)) {\n                        break;\n                    }\n                }\n                meshPickOrder.splice(i, 0, block1);\n            }\n            return meshPickOrder;\n        }\n        return order;\n    }\n    /**\n     * Disposes of the flow graph. Cancels any pending tasks and removes all event listeners.\n     */\n    dispose() {\n        if (this.state === 0 /* FlowGraphState.Stopped */) {\n            return;\n        }\n        this.state = 0 /* FlowGraphState.Stopped */;\n        for (const context of this._executionContexts) {\n            context._clearPendingBlocks();\n        }\n        this._executionContexts.length = 0;\n        for (const type in this._eventBlocks) {\n            this._eventBlocks[type].length = 0;\n        }\n        this._eventObserver?.remove();\n        this._sceneEventCoordinator.dispose();\n    }\n    /**\n     * Executes a function in all blocks of a flow graph, starting with the event blocks.\n     * @param visitor the function to execute.\n     */\n    visitAllBlocks(visitor) {\n        const visitList = [];\n        const idsAddedToVisitList = new Set();\n        for (const type in this._eventBlocks) {\n            for (const block of this._eventBlocks[type]) {\n                visitList.push(block);\n                idsAddedToVisitList.add(block.uniqueId);\n            }\n        }\n        while (visitList.length > 0) {\n            const block = visitList.pop();\n            visitor(block);\n            for (const dataIn of block.dataInputs) {\n                for (const connection of dataIn._connectedPoint) {\n                    if (!idsAddedToVisitList.has(connection._ownerBlock.uniqueId)) {\n                        visitList.push(connection._ownerBlock);\n                        idsAddedToVisitList.add(connection._ownerBlock.uniqueId);\n                    }\n                }\n            }\n            if (block instanceof FlowGraphExecutionBlock) {\n                for (const signalOut of block.signalOutputs) {\n                    for (const connection of signalOut._connectedPoint) {\n                        if (!idsAddedToVisitList.has(connection._ownerBlock.uniqueId)) {\n                            visitList.push(connection._ownerBlock);\n                            idsAddedToVisitList.add(connection._ownerBlock.uniqueId);\n                        }\n                    }\n                }\n            }\n        }\n    }\n    /**\n     * Serializes a graph\n     * @param serializationObject the object to write the values in\n     * @param valueSerializeFunction a function to serialize complex values\n     */\n    serialize(serializationObject = {}, valueSerializeFunction) {\n        serializationObject.allBlocks = [];\n        this.visitAllBlocks((block) => {\n            const serializedBlock = {};\n            block.serialize(serializedBlock);\n            serializationObject.allBlocks.push(serializedBlock);\n        });\n        serializationObject.executionContexts = [];\n        for (const context of this._executionContexts) {\n            const serializedContext = {};\n            context.serialize(serializedContext, valueSerializeFunction);\n            serializationObject.executionContexts.push(serializedContext);\n        }\n    }\n}\n//# sourceMappingURL=flowGraph.js.map", "import { Observable } from \"../Misc/observable.js\";\nimport { FlowGraph } from \"./flowGraph.js\";\nimport { Logger } from \"../Misc/logger.js\";\n/**\n * This class holds all of the existing flow graphs and is responsible for creating new ones.\n * It also handles starting/stopping multiple graphs and communication between them through an Event Coordinator\n * This is the entry point for the flow graph system.\n * @experimental This class is still in development and is subject to change.\n */\nexport class FlowGraphCoordinator {\n    constructor(\n    /**\n     * the configuration of the block\n     */\n    config) {\n        this.config = config;\n        /**\n         * When set to true (default) custom events will be dispatched synchronously.\n         * This means that the events will be dispatched immediately when they are triggered.\n         */\n        this.dispatchEventsSynchronously = true;\n        this._flowGraphs = [];\n        this._customEventsMap = new Map();\n        this._eventExecutionCounter = new Map();\n        this._executeOnNextFrame = [];\n        this._eventUniqueId = 0;\n        // When the scene is disposed, dispose all graphs currently running on it.\n        this._disposeObserver = this.config.scene.onDisposeObservable.add(() => {\n            this.dispose();\n        });\n        this._onBeforeRenderObserver = this.config.scene.onBeforeRenderObservable.add(() => {\n            // Reset the event execution counter at the beginning of each frame.\n            this._eventExecutionCounter.clear();\n            // duplicate the _executeOnNextFrame array to avoid modifying it while iterating over it\n            const executeOnNextFrame = this._executeOnNextFrame.slice(0);\n            if (executeOnNextFrame.length) {\n                // Execute the events that were triggered on the next frame.\n                executeOnNextFrame.forEach((event) => {\n                    this.notifyCustomEvent(event.id, event.data, false);\n                    // remove the event from the array\n                    const index = this._executeOnNextFrame.findIndex((e) => e.uniqueId === event.uniqueId);\n                    if (index !== -1) {\n                        this._executeOnNextFrame.splice(index, 1);\n                    }\n                });\n            }\n        });\n        // Add itself to the SceneCoordinators list for the Inspector.\n        const coordinators = FlowGraphCoordinator.SceneCoordinators.get(this.config.scene) ?? [];\n        coordinators.push(this);\n    }\n    /**\n     * Creates a new flow graph and adds it to the list of existing flow graphs\n     * @returns a new flow graph\n     */\n    createGraph() {\n        const graph = new FlowGraph({ scene: this.config.scene, coordinator: this });\n        this._flowGraphs.push(graph);\n        return graph;\n    }\n    /**\n     * Removes a flow graph from the list of existing flow graphs and disposes it\n     * @param graph the graph to remove\n     */\n    removeGraph(graph) {\n        const index = this._flowGraphs.indexOf(graph);\n        if (index !== -1) {\n            graph.dispose();\n            this._flowGraphs.splice(index, 1);\n        }\n    }\n    /**\n     * Starts all graphs\n     */\n    start() {\n        this._flowGraphs.forEach((graph) => graph.start());\n    }\n    /**\n     * Disposes all graphs\n     */\n    dispose() {\n        this._flowGraphs.forEach((graph) => graph.dispose());\n        this._flowGraphs.length = 0;\n        this._disposeObserver?.remove();\n        this._onBeforeRenderObserver?.remove();\n        // Remove itself from the SceneCoordinators list for the Inspector.\n        const coordinators = FlowGraphCoordinator.SceneCoordinators.get(this.config.scene) ?? [];\n        const index = coordinators.indexOf(this);\n        if (index !== -1) {\n            coordinators.splice(index, 1);\n        }\n    }\n    /**\n     * Serializes this coordinator to a JSON object.\n     * @param serializationObject the object to serialize to\n     * @param valueSerializeFunction the function to use to serialize the value\n     */\n    serialize(serializationObject, valueSerializeFunction) {\n        serializationObject._flowGraphs = [];\n        this._flowGraphs.forEach((graph) => {\n            const serializedGraph = {};\n            graph.serialize(serializedGraph, valueSerializeFunction);\n            serializationObject._flowGraphs.push(serializedGraph);\n        });\n        serializationObject.dispatchEventsSynchronously = this.dispatchEventsSynchronously;\n    }\n    /**\n     * Gets the list of flow graphs\n     */\n    get flowGraphs() {\n        return this._flowGraphs;\n    }\n    /**\n     * Get an observable that will be notified when the event with the given id is fired.\n     * @param id the id of the event\n     * @returns the observable for the event\n     */\n    getCustomEventObservable(id) {\n        let observable = this._customEventsMap.get(id);\n        if (!observable) {\n            // receive event is initialized before scene start, so no need to notify if triggered. but possible!\n            observable = new Observable( /*undefined, true*/);\n            this._customEventsMap.set(id, observable);\n        }\n        return observable;\n    }\n    /**\n     * Notifies the observable for the given event id with the given data.\n     * @param id the id of the event\n     * @param data the data to send with the event\n     * @param async if true, the event will be dispatched asynchronously\n     */\n    notifyCustomEvent(id, data, async = !this.dispatchEventsSynchronously) {\n        if (async) {\n            this._executeOnNextFrame.push({ id, data, uniqueId: this._eventUniqueId++ });\n            return;\n        }\n        // check if we are not exceeding the max number of events\n        if (this._eventExecutionCounter.has(id)) {\n            const count = this._eventExecutionCounter.get(id);\n            this._eventExecutionCounter.set(id, count + 1);\n            if (count >= FlowGraphCoordinator.MaxEventTypeExecutionPerFrame) {\n                count === FlowGraphCoordinator.MaxEventTypeExecutionPerFrame && Logger.Warn(`FlowGraphCoordinator: Too many executions of event \"${id}\".`);\n                return;\n            }\n        }\n        else {\n            this._eventExecutionCounter.set(id, 1);\n        }\n        const observable = this._customEventsMap.get(id);\n        if (observable) {\n            observable.notifyObservers(data);\n        }\n    }\n}\n/**\n * The maximum number of events per type.\n * This is used to limit the number of events that can be created in a single scene.\n * This is to prevent infinite loops.\n */\nFlowGraphCoordinator.MaxEventsPerType = 30;\n/**\n * The maximum number of execution of a specific event in a single frame.\n */\nFlowGraphCoordinator.MaxEventTypeExecutionPerFrame = 30;\n/**\n * @internal\n * A list of all the coordinators per scene. Will be used by the inspector\n */\nFlowGraphCoordinator.SceneCoordinators = new Map();\n//# sourceMappingURL=flowGraphCoordinator.js.map", "/**\n * Any external module that wishes to add a new block to the flow graph can add to this object using the helper function.\n */\nconst customBlocks = {};\n/**\n * If you want to add a new block to the block factory, you should use this function.\n * Please be sure to choose a unique name and define the responsible module.\n * @param module the name of the module that is responsible for the block\n * @param blockName the name of the block. This should be unique.\n * @param factory an async factory function to generate the block\n */\nexport function addToBlockFactory(module, blockName, factory) {\n    customBlocks[`${module}/${blockName}`] = factory;\n}\n/**\n * a function to get a factory function for a block.\n * @param blockName the block name to initialize. If the block comes from an external module, the name should be in the format \"module/blockName\"\n * @returns an async factory function that will return the block class when called.\n */\nexport function blockFactory(blockName) {\n    switch (blockName) {\n        case \"FlowGraphPlayAnimationBlock\" /* FlowGraphBlockNames.PlayAnimation */:\n            return async () => (await import(\"./Execution/Animation/flowGraphPlayAnimationBlock.js\")).FlowGraphPlayAnimationBlock;\n        case \"FlowGraphStopAnimationBlock\" /* FlowGraphBlockNames.StopAnimation */:\n            return async () => (await import(\"./Execution/Animation/flowGraphStopAnimationBlock.js\")).FlowGraphStopAnimationBlock;\n        case \"FlowGraphPauseAnimationBlock\" /* FlowGraphBlockNames.PauseAnimation */:\n            return async () => (await import(\"./Execution/Animation/flowGraphPauseAnimationBlock.js\")).FlowGraphPauseAnimationBlock;\n        case \"FlowGraphInterpolationBlock\" /* FlowGraphBlockNames.ValueInterpolation */:\n            return async () => (await import(\"./Execution/Animation/flowGraphInterpolationBlock.js\")).FlowGraphInterpolationBlock;\n        case \"FlowGraphSceneReadyEventBlock\" /* FlowGraphBlockNames.SceneReadyEvent */:\n            return async () => (await import(\"./Event/flowGraphSceneReadyEventBlock.js\")).FlowGraphSceneReadyEventBlock;\n        case \"FlowGraphSceneTickEventBlock\" /* FlowGraphBlockNames.SceneTickEvent */:\n            return async () => (await import(\"./Event/flowGraphSceneTickEventBlock.js\")).FlowGraphSceneTickEventBlock;\n        case \"FlowGraphSendCustomEventBlock\" /* FlowGraphBlockNames.SendCustomEvent */:\n            return async () => (await import(\"./Event/flowGraphSendCustomEventBlock.js\")).FlowGraphSendCustomEventBlock;\n        case \"FlowGraphReceiveCustomEventBlock\" /* FlowGraphBlockNames.ReceiveCustomEvent */:\n            return async () => (await import(\"./Event/flowGraphReceiveCustomEventBlock.js\")).FlowGraphReceiveCustomEventBlock;\n        case \"FlowGraphMeshPickEventBlock\" /* FlowGraphBlockNames.MeshPickEvent */:\n            return async () => (await import(\"./Event/flowGraphMeshPickEventBlock.js\")).FlowGraphMeshPickEventBlock;\n        case \"FlowGraphEBlock\" /* FlowGraphBlockNames.E */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphEBlock;\n        case \"FlowGraphPIBlock\" /* FlowGraphBlockNames.PI */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphPiBlock;\n        case \"FlowGraphInfBlock\" /* FlowGraphBlockNames.Inf */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphInfBlock;\n        case \"FlowGraphNaNBlock\" /* FlowGraphBlockNames.NaN */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphNaNBlock;\n        case \"FlowGraphRandomBlock\" /* FlowGraphBlockNames.Random */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphRandomBlock;\n        case \"FlowGraphAddBlock\" /* FlowGraphBlockNames.Add */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphAddBlock;\n        case \"FlowGraphSubtractBlock\" /* FlowGraphBlockNames.Subtract */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphSubtractBlock;\n        case \"FlowGraphMultiplyBlock\" /* FlowGraphBlockNames.Multiply */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphMultiplyBlock;\n        case \"FlowGraphDivideBlock\" /* FlowGraphBlockNames.Divide */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphDivideBlock;\n        case \"FlowGraphAbsBlock\" /* FlowGraphBlockNames.Abs */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphAbsBlock;\n        case \"FlowGraphSignBlock\" /* FlowGraphBlockNames.Sign */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphSignBlock;\n        case \"FlowGraphTruncBlock\" /* FlowGraphBlockNames.Trunc */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphTruncBlock;\n        case \"FlowGraphFloorBlock\" /* FlowGraphBlockNames.Floor */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphFloorBlock;\n        case \"FlowGraphCeilBlock\" /* FlowGraphBlockNames.Ceil */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphCeilBlock;\n        case \"FlowGraphRoundBlock\" /* FlowGraphBlockNames.Round */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphRoundBlock;\n        case \"FlowGraphFractBlock\" /* FlowGraphBlockNames.Fraction */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphFractionBlock;\n        case \"FlowGraphNegationBlock\" /* FlowGraphBlockNames.Negation */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphNegationBlock;\n        case \"FlowGraphModuloBlock\" /* FlowGraphBlockNames.Modulo */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphModuloBlock;\n        case \"FlowGraphMinBlock\" /* FlowGraphBlockNames.Min */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphMinBlock;\n        case \"FlowGraphMaxBlock\" /* FlowGraphBlockNames.Max */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphMaxBlock;\n        case \"FlowGraphClampBlock\" /* FlowGraphBlockNames.Clamp */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphClampBlock;\n        case \"FlowGraphSaturateBlock\" /* FlowGraphBlockNames.Saturate */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphSaturateBlock;\n        case \"FlowGraphMathInterpolationBlock\" /* FlowGraphBlockNames.MathInterpolation */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphMathInterpolationBlock;\n        case \"FlowGraphEqualityBlock\" /* FlowGraphBlockNames.Equality */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphEqualityBlock;\n        case \"FlowGraphLessThanBlock\" /* FlowGraphBlockNames.LessThan */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphLessThanBlock;\n        case \"FlowGraphLessThanOrEqualBlock\" /* FlowGraphBlockNames.LessThanOrEqual */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphLessThanOrEqualBlock;\n        case \"FlowGraphGreaterThanBlock\" /* FlowGraphBlockNames.GreaterThan */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphGreaterThanBlock;\n        case \"FlowGraphGreaterThanOrEqualBlock\" /* FlowGraphBlockNames.GreaterThanOrEqual */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphGreaterThanOrEqualBlock;\n        case \"FlowGraphIsNaNBlock\" /* FlowGraphBlockNames.IsNaN */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphIsNanBlock;\n        case \"FlowGraphIsInfBlock\" /* FlowGraphBlockNames.IsInfinity */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphIsInfinityBlock;\n        case \"FlowGraphDegToRadBlock\" /* FlowGraphBlockNames.DegToRad */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphDegToRadBlock;\n        case \"FlowGraphRadToDegBlock\" /* FlowGraphBlockNames.RadToDeg */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphRadToDegBlock;\n        case \"FlowGraphSinBlock\" /* FlowGraphBlockNames.Sin */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphSinBlock;\n        case \"FlowGraphCosBlock\" /* FlowGraphBlockNames.Cos */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphCosBlock;\n        case \"FlowGraphTanBlock\" /* FlowGraphBlockNames.Tan */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphTanBlock;\n        case \"FlowGraphASinBlock\" /* FlowGraphBlockNames.Asin */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphAsinBlock;\n        case \"FlowGraphACosBlock\" /* FlowGraphBlockNames.Acos */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphAcosBlock;\n        case \"FlowGraphATanBlock\" /* FlowGraphBlockNames.Atan */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphAtanBlock;\n        case \"FlowGraphATan2Block\" /* FlowGraphBlockNames.Atan2 */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphAtan2Block;\n        case \"FlowGraphSinhBlock\" /* FlowGraphBlockNames.Sinh */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphSinhBlock;\n        case \"FlowGraphCoshBlock\" /* FlowGraphBlockNames.Cosh */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphCoshBlock;\n        case \"FlowGraphTanhBlock\" /* FlowGraphBlockNames.Tanh */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphTanhBlock;\n        case \"FlowGraphASinhBlock\" /* FlowGraphBlockNames.Asinh */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphAsinhBlock;\n        case \"FlowGraphACoshBlock\" /* FlowGraphBlockNames.Acosh */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphAcoshBlock;\n        case \"FlowGraphATanhBlock\" /* FlowGraphBlockNames.Atanh */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphAtanhBlock;\n        case \"FlowGraphExponentialBlock\" /* FlowGraphBlockNames.Exponential */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphExpBlock;\n        case \"FlowGraphLogBlock\" /* FlowGraphBlockNames.Log */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphLogBlock;\n        case \"FlowGraphLog2Block\" /* FlowGraphBlockNames.Log2 */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphLog2Block;\n        case \"FlowGraphLog10Block\" /* FlowGraphBlockNames.Log10 */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphLog10Block;\n        case \"FlowGraphSquareRootBlock\" /* FlowGraphBlockNames.SquareRoot */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphSquareRootBlock;\n        case \"FlowGraphPowerBlock\" /* FlowGraphBlockNames.Power */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphPowerBlock;\n        case \"FlowGraphCubeRootBlock\" /* FlowGraphBlockNames.CubeRoot */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphCubeRootBlock;\n        case \"FlowGraphBitwiseAndBlock\" /* FlowGraphBlockNames.BitwiseAnd */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphBitwiseAndBlock;\n        case \"FlowGraphBitwiseOrBlock\" /* FlowGraphBlockNames.BitwiseOr */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphBitwiseOrBlock;\n        case \"FlowGraphBitwiseNotBlock\" /* FlowGraphBlockNames.BitwiseNot */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphBitwiseNotBlock;\n        case \"FlowGraphBitwiseXorBlock\" /* FlowGraphBlockNames.BitwiseXor */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphBitwiseXorBlock;\n        case \"FlowGraphBitwiseLeftShiftBlock\" /* FlowGraphBlockNames.BitwiseLeftShift */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphBitwiseLeftShiftBlock;\n        case \"FlowGraphBitwiseRightShiftBlock\" /* FlowGraphBlockNames.BitwiseRightShift */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphBitwiseRightShiftBlock;\n        case \"FlowGraphLengthBlock\" /* FlowGraphBlockNames.Length */:\n            return async () => (await import(\"./Data/Math/flowGraphVectorMathBlocks.js\")).FlowGraphLengthBlock;\n        case \"FlowGraphNormalizeBlock\" /* FlowGraphBlockNames.Normalize */:\n            return async () => (await import(\"./Data/Math/flowGraphVectorMathBlocks.js\")).FlowGraphNormalizeBlock;\n        case \"FlowGraphDotBlock\" /* FlowGraphBlockNames.Dot */:\n            return async () => (await import(\"./Data/Math/flowGraphVectorMathBlocks.js\")).FlowGraphDotBlock;\n        case \"FlowGraphCrossBlock\" /* FlowGraphBlockNames.Cross */:\n            return async () => (await import(\"./Data/Math/flowGraphVectorMathBlocks.js\")).FlowGraphCrossBlock;\n        case \"FlowGraphRotate2DBlock\" /* FlowGraphBlockNames.Rotate2D */:\n            return async () => (await import(\"./Data/Math/flowGraphVectorMathBlocks.js\")).FlowGraphRotate2DBlock;\n        case \"FlowGraphRotate3DBlock\" /* FlowGraphBlockNames.Rotate3D */:\n            return async () => (await import(\"./Data/Math/flowGraphVectorMathBlocks.js\")).FlowGraphRotate3DBlock;\n        case \"FlowGraphTransposeBlock\" /* FlowGraphBlockNames.Transpose */:\n            return async () => (await import(\"./Data/Math/flowGraphMatrixMathBlocks.js\")).FlowGraphTransposeBlock;\n        case \"FlowGraphDeterminantBlock\" /* FlowGraphBlockNames.Determinant */:\n            return async () => (await import(\"./Data/Math/flowGraphMatrixMathBlocks.js\")).FlowGraphDeterminantBlock;\n        case \"FlowGraphInvertMatrixBlock\" /* FlowGraphBlockNames.InvertMatrix */:\n            return async () => (await import(\"./Data/Math/flowGraphMatrixMathBlocks.js\")).FlowGraphInvertMatrixBlock;\n        case \"FlowGraphMatrixMultiplicationBlock\" /* FlowGraphBlockNames.MatrixMultiplication */:\n            return async () => (await import(\"./Data/Math/flowGraphMatrixMathBlocks.js\")).FlowGraphMatrixMultiplicationBlock;\n        case \"FlowGraphBranchBlock\" /* FlowGraphBlockNames.Branch */:\n            return async () => (await import(\"./Execution/ControlFlow/flowGraphBranchBlock.js\")).FlowGraphBranchBlock;\n        case \"FlowGraphSetDelayBlock\" /* FlowGraphBlockNames.SetDelay */:\n            return async () => (await import(\"./Execution/ControlFlow/flowGraphSetDelayBlock.js\")).FlowGraphSetDelayBlock;\n        case \"FlowGraphCancelDelayBlock\" /* FlowGraphBlockNames.CancelDelay */:\n            return async () => (await import(\"./Execution/ControlFlow/flowGraphCancelDelayBlock.js\")).FlowGraphCancelDelayBlock;\n        case \"FlowGraphCallCounterBlock\" /* FlowGraphBlockNames.CallCounter */:\n            return async () => (await import(\"./Execution/ControlFlow/flowGraphCounterBlock.js\")).FlowGraphCallCounterBlock;\n        case \"FlowGraphDebounceBlock\" /* FlowGraphBlockNames.Debounce */:\n            return async () => (await import(\"./Execution/ControlFlow/flowGraphDebounceBlock.js\")).FlowGraphDebounceBlock;\n        case \"FlowGraphThrottleBlock\" /* FlowGraphBlockNames.Throttle */:\n            return async () => (await import(\"./Execution/ControlFlow/flowGraphThrottleBlock.js\")).FlowGraphThrottleBlock;\n        case \"FlowGraphDoNBlock\" /* FlowGraphBlockNames.DoN */:\n            return async () => (await import(\"./Execution/ControlFlow/flowGraphDoNBlock.js\")).FlowGraphDoNBlock;\n        case \"FlowGraphFlipFlopBlock\" /* FlowGraphBlockNames.FlipFlop */:\n            return async () => (await import(\"./Execution/ControlFlow/flowGraphFlipFlopBlock.js\")).FlowGraphFlipFlopBlock;\n        case \"FlowGraphForLoopBlock\" /* FlowGraphBlockNames.ForLoop */:\n            return async () => (await import(\"./Execution/ControlFlow/flowGraphForLoopBlock.js\")).FlowGraphForLoopBlock;\n        case \"FlowGraphMultiGateBlock\" /* FlowGraphBlockNames.MultiGate */:\n            return async () => (await import(\"./Execution/ControlFlow/flowGraphMultiGateBlock.js\")).FlowGraphMultiGateBlock;\n        case \"FlowGraphSequenceBlock\" /* FlowGraphBlockNames.Sequence */:\n            return async () => (await import(\"./Execution/ControlFlow/flowGraphSequenceBlock.js\")).FlowGraphSequenceBlock;\n        case \"FlowGraphSwitchBlock\" /* FlowGraphBlockNames.Switch */:\n            return async () => (await import(\"./Execution/ControlFlow/flowGraphSwitchBlock.js\")).FlowGraphSwitchBlock;\n        case \"FlowGraphWaitAllBlock\" /* FlowGraphBlockNames.WaitAll */:\n            return async () => (await import(\"./Execution/ControlFlow/flowGraphWaitAllBlock.js\")).FlowGraphWaitAllBlock;\n        case \"FlowGraphWhileLoopBlock\" /* FlowGraphBlockNames.WhileLoop */:\n            return async () => (await import(\"./Execution/ControlFlow/flowGraphWhileLoopBlock.js\")).FlowGraphWhileLoopBlock;\n        case \"FlowGraphConsoleLogBlock\" /* FlowGraphBlockNames.ConsoleLog */:\n            return async () => (await import(\"./Execution/flowGraphConsoleLogBlock.js\")).FlowGraphConsoleLogBlock;\n        case \"FlowGraphConditionalBlock\" /* FlowGraphBlockNames.Conditional */:\n            return async () => (await import(\"./Data/flowGraphConditionalDataBlock.js\")).FlowGraphConditionalDataBlock;\n        case \"FlowGraphConstantBlock\" /* FlowGraphBlockNames.Constant */:\n            return async () => (await import(\"./Data/flowGraphConstantBlock.js\")).FlowGraphConstantBlock;\n        case \"FlowGraphTransformCoordinatesSystemBlock\" /* FlowGraphBlockNames.TransformCoordinatesSystem */:\n            return async () => (await import(\"./Data/flowGraphTransformCoordinatesSystemBlock.js\")).FlowGraphTransformCoordinatesSystemBlock;\n        case \"FlowGraphGetAssetBlock\" /* FlowGraphBlockNames.GetAsset */:\n            return async () => (await import(\"./Data/flowGraphGetAssetBlock.js\")).FlowGraphGetAssetBlock;\n        case \"FlowGraphGetPropertyBlock\" /* FlowGraphBlockNames.GetProperty */:\n            return async () => (await import(\"./Data/flowGraphGetPropertyBlock.js\")).FlowGraphGetPropertyBlock;\n        case \"FlowGraphSetPropertyBlock\" /* FlowGraphBlockNames.SetProperty */:\n            return async () => (await import(\"./Execution/flowGraphSetPropertyBlock.js\")).FlowGraphSetPropertyBlock;\n        case \"FlowGraphGetVariableBlock\" /* FlowGraphBlockNames.GetVariable */:\n            return async () => (await import(\"./Data/flowGraphGetVariableBlock.js\")).FlowGraphGetVariableBlock;\n        case \"FlowGraphSetVariableBlock\" /* FlowGraphBlockNames.SetVariable */:\n            return async () => (await import(\"./Execution/flowGraphSetVariableBlock.js\")).FlowGraphSetVariableBlock;\n        case \"FlowGraphJsonPointerParserBlock\" /* FlowGraphBlockNames.JsonPointerParser */:\n            return async () => (await import(\"./Data/Transformers/flowGraphJsonPointerParserBlock.js\")).FlowGraphJsonPointerParserBlock;\n        case \"FlowGraphLeadingZerosBlock\" /* FlowGraphBlockNames.LeadingZeros */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphLeadingZerosBlock;\n        case \"FlowGraphTrailingZerosBlock\" /* FlowGraphBlockNames.TrailingZeros */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphTrailingZerosBlock;\n        case \"FlowGraphOneBitsCounterBlock\" /* FlowGraphBlockNames.OneBitsCounter */:\n            return async () => (await import(\"./Data/Math/flowGraphMathBlocks.js\")).FlowGraphOneBitsCounterBlock;\n        case \"FlowGraphCombineVector2Block\" /* FlowGraphBlockNames.CombineVector2 */:\n            return async () => (await import(\"./Data/Math/flowGraphMathCombineExtractBlocks.js\")).FlowGraphCombineVector2Block;\n        case \"FlowGraphCombineVector3Block\" /* FlowGraphBlockNames.CombineVector3 */:\n            return async () => (await import(\"./Data/Math/flowGraphMathCombineExtractBlocks.js\")).FlowGraphCombineVector3Block;\n        case \"FlowGraphCombineVector4Block\" /* FlowGraphBlockNames.CombineVector4 */:\n            return async () => (await import(\"./Data/Math/flowGraphMathCombineExtractBlocks.js\")).FlowGraphCombineVector4Block;\n        case \"FlowGraphCombineMatrixBlock\" /* FlowGraphBlockNames.CombineMatrix */:\n            return async () => (await import(\"./Data/Math/flowGraphMathCombineExtractBlocks.js\")).FlowGraphCombineMatrixBlock;\n        case \"FlowGraphExtractVector2Block\" /* FlowGraphBlockNames.ExtractVector2 */:\n            return async () => (await import(\"./Data/Math/flowGraphMathCombineExtractBlocks.js\")).FlowGraphExtractVector2Block;\n        case \"FlowGraphExtractVector3Block\" /* FlowGraphBlockNames.ExtractVector3 */:\n            return async () => (await import(\"./Data/Math/flowGraphMathCombineExtractBlocks.js\")).FlowGraphExtractVector3Block;\n        case \"FlowGraphExtractVector4Block\" /* FlowGraphBlockNames.ExtractVector4 */:\n            return async () => (await import(\"./Data/Math/flowGraphMathCombineExtractBlocks.js\")).FlowGraphExtractVector4Block;\n        case \"FlowGraphExtractMatrixBlock\" /* FlowGraphBlockNames.ExtractMatrix */:\n            return async () => (await import(\"./Data/Math/flowGraphMathCombineExtractBlocks.js\")).FlowGraphExtractMatrixBlock;\n        case \"FlowGraphTransformVectorBlock\" /* FlowGraphBlockNames.TransformVector */:\n            return async () => (await import(\"./Data/Math/flowGraphVectorMathBlocks.js\")).FlowGraphTransformBlock;\n        case \"FlowGraphTransformCoordinatesBlock\" /* FlowGraphBlockNames.TransformCoordinates */:\n            return async () => (await import(\"./Data/Math/flowGraphVectorMathBlocks.js\")).FlowGraphTransformCoordinatesBlock;\n        case \"FlowGraphMatrixDecompose\" /* FlowGraphBlockNames.MatrixDecompose */:\n            return async () => (await import(\"./Data/Math/flowGraphMatrixMathBlocks.js\")).FlowGraphMatrixDecomposeBlock;\n        case \"FlowGraphMatrixCompose\" /* FlowGraphBlockNames.MatrixCompose */:\n            return async () => (await import(\"./Data/Math/flowGraphMatrixMathBlocks.js\")).FlowGraphMatrixComposeBlock;\n        case \"FlowGraphBooleanToFloat\" /* FlowGraphBlockNames.BooleanToFloat */:\n            return async () => (await import(\"./Data/Transformers/flowGraphTypeToTypeBlocks.js\")).FlowGraphBooleanToFloat;\n        case \"FlowGraphBooleanToInt\" /* FlowGraphBlockNames.BooleanToInt */:\n            return async () => (await import(\"./Data/Transformers/flowGraphTypeToTypeBlocks.js\")).FlowGraphBooleanToInt;\n        case \"FlowGraphFloatToBoolean\" /* FlowGraphBlockNames.FloatToBoolean */:\n            return async () => (await import(\"./Data/Transformers/flowGraphTypeToTypeBlocks.js\")).FlowGraphFloatToBoolean;\n        case \"FlowGraphIntToBoolean\" /* FlowGraphBlockNames.IntToBoolean */:\n            return async () => (await import(\"./Data/Transformers/flowGraphTypeToTypeBlocks.js\")).FlowGraphIntToBoolean;\n        case \"FlowGraphIntToFloat\" /* FlowGraphBlockNames.IntToFloat */:\n            return async () => (await import(\"./Data/Transformers/flowGraphTypeToTypeBlocks.js\")).FlowGraphIntToFloat;\n        case \"FlowGraphFloatToInt\" /* FlowGraphBlockNames.FloatToInt */:\n            return async () => (await import(\"./Data/Transformers/flowGraphTypeToTypeBlocks.js\")).FlowGraphFloatToInt;\n        case \"FlowGraphEasingBlock\" /* FlowGraphBlockNames.Easing */:\n            return async () => (await import(\"./Execution/Animation/flowGraphEasingBlock.js\")).FlowGraphEasingBlock;\n        case \"FlowGraphBezierCurveEasing\" /* FlowGraphBlockNames.BezierCurveEasing */:\n            return async () => (await import(\"./Execution/Animation/flowGraphBezierCurveEasingBlock.js\")).FlowGraphBezierCurveEasingBlock;\n        case \"FlowGraphPointerOverEventBlock\" /* FlowGraphBlockNames.PointerOverEvent */:\n            return async () => (await import(\"./Event/flowGraphPointerOverEventBlock.js\")).FlowGraphPointerOverEventBlock;\n        case \"FlowGraphPointerOutEventBlock\" /* FlowGraphBlockNames.PointerOutEvent */:\n            return async () => (await import(\"./Event/flowGraphPointerOutEventBlock.js\")).FlowGraphPointerOutEventBlock;\n        case \"FlowGraphContextBlock\" /* FlowGraphBlockNames.Context */:\n            return async () => (await import(\"./Data/Utils/flowGraphContextBlock.js\")).FlowGraphContextBlock;\n        case \"FlowGraphArrayIndexBlock\" /* FlowGraphBlockNames.ArrayIndex */:\n            return async () => (await import(\"./Data/Utils/flowGraphArrayIndexBlock.js\")).FlowGraphArrayIndexBlock;\n        case \"FlowGraphCodeExecutionBlock\" /* FlowGraphBlockNames.CodeExecution */:\n            return async () => (await import(\"./Data/Utils/flowGraphCodeExecutionBlock.js\")).FlowGraphCodeExecutionBlock;\n        case \"FlowGraphIndexOfBlock\" /* FlowGraphBlockNames.IndexOf */:\n            return async () => (await import(\"./Data/Utils/flowGraphIndexOfBlock.js\")).FlowGraphIndexOfBlock;\n        case \"FlowGraphFunctionReference\" /* FlowGraphBlockNames.FunctionReference */:\n            return async () => (await import(\"./Data/Utils/flowGraphFunctionReferenceBlock.js\")).FlowGraphFunctionReferenceBlock;\n        case \"FlowGraphDataSwitchBlock\" /* FlowGraphBlockNames.DataSwitch */:\n            return async () => (await import(\"./Data/flowGraphDataSwitchBlock.js\")).FlowGraphDataSwitchBlock;\n        default:\n            // check if the block is a custom block\n            if (customBlocks[blockName]) {\n                return customBlocks[blockName];\n            }\n            throw new Error(`Unknown block name ${blockName}`);\n    }\n}\n//# sourceMappingURL=flowGraphBlockFactory.js.map", "import { FlowGraphExecutionBlock } from \"./flowGraphExecutionBlock.js\";\n/**\n * An execution block that has an out signal. This signal is triggered when the synchronous execution of this block is done.\n * Most execution blocks will inherit from this, except for the ones that have multiple signals to be triggered.\n * (such as if blocks)\n */\nexport class FlowGraphExecutionBlockWithOutSignal extends FlowGraphExecutionBlock {\n    constructor(config) {\n        super(config);\n        this.out = this._registerSignalOutput(\"out\");\n    }\n}\n//# sourceMappingURL=flowGraphExecutionBlockWithOutSignal.js.map", "import { FlowGraphExecutionBlockWithOutSignal } from \"./flowGraphExecutionBlockWithOutSignal.js\";\n/**\n * An async execution block can start tasks that will be executed asynchronously.\n * It should also be responsible for clearing it in _cancelPendingTasks.\n */\nexport class FlowGraphAsyncExecutionBlock extends FlowGraphExecutionBlockWithOutSignal {\n    constructor(config, events) {\n        super(config);\n        this._eventsSignalOutputs = {};\n        this.done = this._registerSignalOutput(\"done\");\n        events?.forEach((eventName) => {\n            this._eventsSignalOutputs[eventName] = this._registerSignalOutput(eventName + \"Event\");\n        });\n    }\n    /**\n     * @internal\n     * This function can be overridden to execute any\n     * logic that should be executed on every frame\n     * while the async task is pending.\n     * @param context the context in which it is running\n     */\n    _executeOnTick(_context) { }\n    /**\n     * @internal\n     * @param context\n     */\n    _startPendingTasks(context) {\n        if (context._getExecutionVariable(this, \"_initialized\", false)) {\n            this._cancelPendingTasks(context);\n            this._resetAfterCanceled(context);\n        }\n        this._preparePendingTasks(context);\n        context._addPendingBlock(this);\n        this.out._activateSignal(context);\n        context._setExecutionVariable(this, \"_initialized\", true);\n    }\n    _resetAfterCanceled(context) {\n        context._deleteExecutionVariable(this, \"_initialized\");\n        context._removePendingBlock(this);\n    }\n}\n//# sourceMappingURL=flowGraphAsyncExecutionBlock.js.map", "import { FlowGraphAsyncExecutionBlock } from \"./flowGraphAsyncExecutionBlock.js\";\n/**\n * A type of block that listens to an event observable and activates\n * its output signal when the event is triggered.\n */\nexport class FlowGraphEventBlock extends FlowGraphAsyncExecutionBlock {\n    constructor() {\n        super(...arguments);\n        /**\n         * the priority of initialization of this block.\n         * For example, scene start should have a negative priority because it should be initialized last.\n         */\n        this.initPriority = 0;\n        /**\n         * The type of the event\n         */\n        this.type = \"NoTrigger\" /* FlowGraphEventType.NoTrigger */;\n    }\n    /**\n     * @internal\n     */\n    _execute(context) {\n        context._notifyExecuteNode(this);\n        this.done._activateSignal(context);\n    }\n}\n//# sourceMappingURL=flowGraphEventBlock.js.map", "import { blockFactory } from \"./Blocks/flowGraphBlockFactory.js\";\nimport { FlowGraphCoordinator } from \"./flowGraphCoordinator.js\";\nimport { FlowGraphEventBlock } from \"./flowGraphEventBlock.js\";\nimport { FlowGraphExecutionBlock } from \"./flowGraphExecutionBlock.js\";\nimport { defaultValueParseFunction, needsPathConverter } from \"./serialization.js\";\nimport { getRichTypeByFlowGraphType, RichType } from \"./flowGraphRichTypes.js\";\n/**\n * Given a list of blocks, find an output data connection that has a specific unique id\n * @param blocks a list of flow graph blocks\n * @param uniqueId the unique id of a connection\n * @returns the connection that has this unique id. throws an error if none was found\n */\nexport function GetDataOutConnectionByUniqueId(blocks, uniqueId) {\n    for (const block of blocks) {\n        for (const dataOut of block.dataOutputs) {\n            if (dataOut.uniqueId === uniqueId) {\n                return dataOut;\n            }\n        }\n    }\n    throw new Error(\"Could not find data out connection with unique id \" + uniqueId);\n}\n/**\n * Given a list of blocks, find an input signal connection that has a specific unique id\n * @param blocks a list of flow graph blocks\n * @param uniqueId the unique id of a connection\n * @returns the connection that has this unique id. throws an error if none was found\n */\nexport function GetSignalInConnectionByUniqueId(blocks, uniqueId) {\n    for (const block of blocks) {\n        if (block instanceof FlowGraphExecutionBlock) {\n            for (const signalIn of block.signalInputs) {\n                if (signalIn.uniqueId === uniqueId) {\n                    return signalIn;\n                }\n            }\n        }\n    }\n    throw new Error(\"Could not find signal in connection with unique id \" + uniqueId);\n}\n/**\n * Parses a serialized coordinator.\n * @param serializedObject the object to parse\n * @param options the options to use when parsing\n * @returns the parsed coordinator\n */\nexport async function ParseCoordinatorAsync(serializedObject, options) {\n    const valueParseFunction = options.valueParseFunction ?? defaultValueParseFunction;\n    const coordinator = new FlowGraphCoordinator({ scene: options.scene });\n    if (serializedObject.dispatchEventsSynchronously) {\n        coordinator.dispatchEventsSynchronously = serializedObject.dispatchEventsSynchronously;\n    }\n    await options.scene.whenReadyAsync();\n    // if custom default values are defined, set them in the global context\n    if (serializedObject._defaultValues) {\n        for (const key in serializedObject._defaultValues) {\n            // key is the FlowGraphType, value is the default value\n            const value = serializedObject._defaultValues[key];\n            getRichTypeByFlowGraphType(key).defaultValue = value;\n        }\n    }\n    // async-parse the flow graphs. This can be done in parallel\n    await Promise.all(serializedObject._flowGraphs?.map((serializedGraph) => ParseFlowGraphAsync(serializedGraph, { coordinator, valueParseFunction, pathConverter: options.pathConverter })));\n    return coordinator;\n}\n/**\n * Parses a graph from a given serialization object\n * @param serializationObject the object where the values are written\n * @param options options for parsing the graph\n * @returns the parsed graph\n */\nexport async function ParseFlowGraphAsync(serializationObject, options) {\n    // get all classes types needed for the blocks using the block factory\n    const resolvedClasses = await Promise.all(serializationObject.allBlocks.map(async (serializedBlock) => {\n        const classFactory = blockFactory(serializedBlock.className);\n        return classFactory();\n    }));\n    // async will be used when we start using the block async factory\n    return ParseFlowGraph(serializationObject, options, resolvedClasses);\n}\n/**\n * Parses a graph from a given serialization object\n * @param serializationObject the object where the values are written\n * @param options options for parsing the graph\n * @param resolvedClasses the resolved classes for the blocks\n * @returns the parsed graph\n */\nexport function ParseFlowGraph(serializationObject, options, resolvedClasses) {\n    const graph = options.coordinator.createGraph();\n    const blocks = [];\n    const valueParseFunction = options.valueParseFunction ?? defaultValueParseFunction;\n    // Parse all blocks\n    // for (const serializedBlock of serializationObject.allBlocks) {\n    for (let i = 0; i < serializationObject.allBlocks.length; i++) {\n        const serializedBlock = serializationObject.allBlocks[i];\n        const block = ParseFlowGraphBlockWithClassType(serializedBlock, { scene: options.coordinator.config.scene, pathConverter: options.pathConverter, assetsContainer: options.coordinator.config.scene, valueParseFunction }, resolvedClasses[i]);\n        blocks.push(block);\n        if (block instanceof FlowGraphEventBlock) {\n            graph.addEventBlock(block);\n        }\n    }\n    // After parsing all blocks, connect them\n    for (const block of blocks) {\n        for (const dataIn of block.dataInputs) {\n            for (const serializedConnection of dataIn.connectedPointIds) {\n                const connection = GetDataOutConnectionByUniqueId(blocks, serializedConnection);\n                dataIn.connectTo(connection);\n            }\n        }\n        if (block instanceof FlowGraphExecutionBlock) {\n            for (const signalOut of block.signalOutputs) {\n                for (const serializedConnection of signalOut.connectedPointIds) {\n                    const connection = GetSignalInConnectionByUniqueId(blocks, serializedConnection);\n                    signalOut.connectTo(connection);\n                }\n            }\n        }\n    }\n    for (const serializedContext of serializationObject.executionContexts) {\n        ParseFlowGraphContext(serializedContext, { graph, valueParseFunction }, serializationObject.rightHanded);\n    }\n    return graph;\n}\n/**\n * Parses a context\n * @param serializationObject the object containing the context serialization values\n * @param options the options for parsing the context\n * @param rightHanded whether the serialized data is right handed\n * @returns\n */\nexport function ParseFlowGraphContext(serializationObject, options, rightHanded) {\n    const result = options.graph.createContext();\n    if (serializationObject.enableLogging) {\n        result.enableLogging = true;\n    }\n    result.treatDataAsRightHanded = rightHanded || false;\n    const valueParseFunction = options.valueParseFunction ?? defaultValueParseFunction;\n    result.uniqueId = serializationObject.uniqueId;\n    const scene = result.getScene();\n    // check if assets context is available\n    if (serializationObject._assetsContext) {\n        const ac = serializationObject._assetsContext;\n        const assetsContext = {\n            meshes: ac.meshes?.map((m) => scene.getMeshById(m)),\n            lights: ac.lights?.map((l) => scene.getLightByName(l)),\n            cameras: ac.cameras?.map((c) => scene.getCameraByName(c)),\n            materials: ac.materials?.map((m) => scene.getMaterialById(m)),\n            textures: ac.textures?.map((t) => scene.getTextureByName(t)),\n            animations: ac.animations?.map((a) => scene.animations.find((anim) => anim.name === a)),\n            skeletons: ac.skeletons?.map((s) => scene.getSkeletonByName(s)),\n            particleSystems: ac.particleSystems?.map((ps) => scene.getParticleSystemById(ps)),\n            animationGroups: ac.animationGroups?.map((ag) => scene.getAnimationGroupByName(ag)),\n            transformNodes: ac.transformNodes?.map((tn) => scene.getTransformNodeById(tn)),\n            rootNodes: [],\n            multiMaterials: [],\n            morphTargetManagers: [],\n            geometries: [],\n            actionManagers: [],\n            environmentTexture: null,\n            postProcesses: [],\n            sounds: null,\n            effectLayers: [],\n            layers: [],\n            reflectionProbes: [],\n            lensFlareSystems: [],\n            proceduralTextures: [],\n            getNodes: function () {\n                throw new Error(\"Function not implemented.\");\n            },\n        };\n        result.assetsContext = assetsContext;\n    }\n    for (const key in serializationObject._userVariables) {\n        const value = valueParseFunction(key, serializationObject._userVariables, result.assetsContext, scene);\n        result.userVariables[key] = value;\n    }\n    for (const key in serializationObject._connectionValues) {\n        const value = valueParseFunction(key, serializationObject._connectionValues, result.assetsContext, scene);\n        result._setConnectionValueByKey(key, value);\n    }\n    return result;\n}\n/**\n * Parses a block from a serialization object\n * This function is async due to the factory method that is used to create the block's class. If you load the class externally use ParseBlockWithClassType\n * @param serializationObject the object to parse from\n * @param parseOptions options for parsing the block\n * @returns the parsed block\n */\nexport async function ParseBlockAsync(serializationObject, parseOptions) {\n    const classFactory = blockFactory(serializationObject.className);\n    const classType = await classFactory();\n    return ParseFlowGraphBlockWithClassType(serializationObject, parseOptions, classType);\n}\n/**\n * Parses a block from a serialization object\n * @param serializationObject the object to parse from\n * @param parseOptions options for parsing the block\n * @param classType the class type of the block. This is used when the class is not loaded asynchronously\n * @returns the parsed block\n */\nexport function ParseFlowGraphBlockWithClassType(serializationObject, parseOptions, classType) {\n    const parsedConfig = {};\n    const valueParseFunction = parseOptions.valueParseFunction ?? defaultValueParseFunction;\n    if (serializationObject.config) {\n        for (const key in serializationObject.config) {\n            parsedConfig[key] = valueParseFunction(key, serializationObject.config, parseOptions.assetsContainer || parseOptions.scene, parseOptions.scene);\n        }\n    }\n    if (needsPathConverter(serializationObject.className)) {\n        if (!parseOptions.pathConverter) {\n            throw new Error(\"Path converter is required for this block\");\n        }\n        parsedConfig.pathConverter = parseOptions.pathConverter;\n    }\n    const obj = new classType(parsedConfig);\n    obj.uniqueId = serializationObject.uniqueId;\n    for (let i = 0; i < serializationObject.dataInputs.length; i++) {\n        const dataInput = obj.getDataInput(serializationObject.dataInputs[i].name);\n        if (dataInput) {\n            dataInput.deserialize(serializationObject.dataInputs[i]);\n        }\n        else {\n            throw new Error(\"Could not find data input with name \" + serializationObject.dataInputs[i].name + \" in block \" + serializationObject.className);\n        }\n    }\n    for (let i = 0; i < serializationObject.dataOutputs.length; i++) {\n        const dataOutput = obj.getDataOutput(serializationObject.dataOutputs[i].name);\n        if (dataOutput) {\n            dataOutput.deserialize(serializationObject.dataOutputs[i]);\n        }\n        else {\n            throw new Error(\"Could not find data output with name \" + serializationObject.dataOutputs[i].name + \" in block \" + serializationObject.className);\n        }\n    }\n    obj.metadata = serializationObject.metadata;\n    obj.deserialize && obj.deserialize(serializationObject);\n    return obj;\n}\n/**\n * Parses a connection from an object\n * @param serializationObject the object to parse from.\n * @param ownerBlock the block that owns the connection.\n * @param classType the class type of the connection.\n * @returns the parsed connection.\n */\nexport function ParseGraphConnectionWithClassType(serializationObject = {}, ownerBlock, classType) {\n    const connection = new classType(serializationObject.name, serializationObject._connectionType, ownerBlock);\n    connection.deserialize(serializationObject);\n    return connection;\n}\n/**\n * Parses a data connection from a serialized object.\n * @param serializationObject the object to parse from\n * @param ownerBlock the block that owns the connection\n * @param classType the class type of the data connection\n * @returns the parsed connection\n */\nexport function ParseGraphDataConnection(serializationObject, ownerBlock, classType) {\n    const richType = ParseRichType(serializationObject.richType);\n    const defaultValue = serializationObject.defaultValue;\n    const connection = new classType(serializationObject.name, serializationObject._connectionType, ownerBlock, richType, defaultValue, !!serializationObject._optional);\n    connection.deserialize(serializationObject);\n    return connection;\n}\n/**\n * Parses a rich type from a serialization object.\n * @param serializationObject a serialization object\n * @returns the parsed rich type\n */\nfunction ParseRichType(serializationObject) {\n    return new RichType(serializationObject.typeName, serializationObject.defaultValue);\n}\n//# sourceMappingURL=flowGraphParser.js.map", "import { getMappingForDeclaration, getMappingForFullOperationName } from \"./declarationMapper.js\";\nimport { Logger } from \"@babylonjs/core/Misc/logger.js\";\nimport { RandomGUID } from \"@babylonjs/core/Misc/guid.js\";\nexport const gltfTypeToBabylonType = {\n    float: { length: 1, flowGraphType: \"number\" /* FlowGraphTypes.Number */, elementType: \"number\" },\n    bool: { length: 1, flowGraphType: \"boolean\" /* FlowGraphTypes.Boolean */, elementType: \"boolean\" },\n    float2: { length: 2, flowGraphType: \"Vector2\" /* FlowGraphTypes.Vector2 */, elementType: \"number\" },\n    float3: { length: 3, flowGraphType: \"Vector3\" /* FlowGraphTypes.Vector3 */, elementType: \"number\" },\n    float4: { length: 4, flowGraphType: \"Vector4\" /* FlowGraphTypes.Vector4 */, elementType: \"number\" },\n    float4x4: { length: 16, flowGraphType: \"Matrix\" /* FlowGraphTypes.Matrix */, elementType: \"number\" },\n    float2x2: { length: 4, flowGraphType: \"Matrix2D\" /* FlowGraphTypes.Matrix2D */, elementType: \"number\" },\n    float3x3: { length: 9, flowGraphType: \"Matrix3D\" /* FlowGraphTypes.Matrix3D */, elementType: \"number\" },\n    int: { length: 1, flowGraphType: \"FlowGraphInteger\" /* FlowGraphTypes.Integer */, elementType: \"number\" },\n};\nexport class InteractivityGraphToFlowGraphParser {\n    constructor(_interactivityGraph, _gltf, _loader) {\n        this._interactivityGraph = _interactivityGraph;\n        this._gltf = _gltf;\n        this._loader = _loader;\n        /**\n         * Note - the graph should be rejected if the same type is defined twice.\n         * We currently don't validate that.\n         */\n        this._types = [];\n        this._mappings = [];\n        this._staticVariables = [];\n        this._events = [];\n        this._internalEventsCounter = 0;\n        this._nodes = [];\n        // start with types\n        this._parseTypes();\n        // continue with declarations\n        this._parseDeclarations();\n        this._parseVariables();\n        this._parseEvents();\n        this._parseNodes();\n    }\n    get arrays() {\n        return {\n            types: this._types,\n            mappings: this._mappings,\n            staticVariables: this._staticVariables,\n            events: this._events,\n            nodes: this._nodes,\n        };\n    }\n    _parseTypes() {\n        if (!this._interactivityGraph.types) {\n            return;\n        }\n        for (const type of this._interactivityGraph.types) {\n            this._types.push(gltfTypeToBabylonType[type.signature]);\n        }\n    }\n    _parseDeclarations() {\n        if (!this._interactivityGraph.declarations) {\n            return;\n        }\n        for (const declaration of this._interactivityGraph.declarations) {\n            // make sure we have the mapping for this operation\n            const mapping = getMappingForDeclaration(declaration);\n            // mapping is defined, because we generate an empty mapping if it's not found\n            if (!mapping) {\n                Logger.Error([\"No mapping found for declaration\", declaration]);\n                throw new Error(\"Error parsing declarations\");\n            }\n            this._mappings.push({\n                flowGraphMapping: mapping,\n                fullOperationName: declaration.extension ? declaration.op + \":\" + declaration.extension : declaration.op,\n            });\n        }\n    }\n    _parseVariables() {\n        if (!this._interactivityGraph.variables) {\n            return;\n        }\n        for (const variable of this._interactivityGraph.variables) {\n            const parsed = this._parseVariable(variable);\n            // set the default values here\n            this._staticVariables.push(parsed);\n        }\n    }\n    _parseVariable(variable, dataTransform) {\n        const type = this._types[variable.type];\n        if (!type) {\n            Logger.Error([\"No type found for variable\", variable]);\n            throw new Error(\"Error parsing variables\");\n        }\n        if (variable.value) {\n            if (variable.value.length !== type.length) {\n                Logger.Error([\"Invalid value length for variable\", variable, type]);\n                throw new Error(\"Error parsing variables\");\n            }\n        }\n        const value = variable.value || [];\n        if (!value.length) {\n            switch (type.flowGraphType) {\n                case \"boolean\" /* FlowGraphTypes.Boolean */:\n                    value.push(false);\n                    break;\n                case \"FlowGraphInteger\" /* FlowGraphTypes.Integer */:\n                    value.push(0);\n                    break;\n                case \"number\" /* FlowGraphTypes.Number */:\n                    value.push(NaN);\n                    break;\n                case \"Vector2\" /* FlowGraphTypes.Vector2 */:\n                    value.push(NaN, NaN);\n                    break;\n                case \"Vector3\" /* FlowGraphTypes.Vector3 */:\n                    value.push(NaN, NaN, NaN);\n                    break;\n                case \"Vector4\" /* FlowGraphTypes.Vector4 */:\n                case \"Matrix2D\" /* FlowGraphTypes.Matrix2D */:\n                case \"Quaternion\" /* FlowGraphTypes.Quaternion */:\n                    value.fill(NaN, 0, 4);\n                    break;\n                case \"Matrix\" /* FlowGraphTypes.Matrix */:\n                    value.fill(NaN, 0, 16);\n                    break;\n                case \"Matrix3D\" /* FlowGraphTypes.Matrix3D */:\n                    value.fill(NaN, 0, 9);\n                    break;\n                default:\n                    break;\n            }\n        }\n        return { type: type.flowGraphType, value: dataTransform ? dataTransform(value, this) : value };\n    }\n    _parseEvents() {\n        if (!this._interactivityGraph.events) {\n            return;\n        }\n        for (const event of this._interactivityGraph.events) {\n            const converted = {\n                eventId: event.id || \"internalEvent_\" + this._internalEventsCounter++,\n            };\n            if (event.values) {\n                converted.eventData = Object.keys(event.values).map((key) => {\n                    const eventValue = event.values?.[key];\n                    if (!eventValue) {\n                        Logger.Error([\"No value found for event key\", key]);\n                        throw new Error(\"Error parsing events\");\n                    }\n                    const type = this._types[eventValue.type];\n                    if (!type) {\n                        Logger.Error([\"No type found for event value\", eventValue]);\n                        throw new Error(\"Error parsing events\");\n                    }\n                    const value = typeof eventValue.value !== \"undefined\" ? this._parseVariable(eventValue) : undefined;\n                    return {\n                        id: key,\n                        type: type.flowGraphType,\n                        eventData: true,\n                        value,\n                    };\n                });\n            }\n            this._events.push(converted);\n        }\n    }\n    _parseNodes() {\n        if (!this._interactivityGraph.nodes) {\n            return;\n        }\n        for (const node of this._interactivityGraph.nodes) {\n            // some validation\n            if (typeof node.declaration !== \"number\") {\n                Logger.Error([\"No declaration found for node\", node]);\n                throw new Error(\"Error parsing nodes\");\n            }\n            const mapping = this._mappings[node.declaration];\n            if (!mapping) {\n                Logger.Error([\"No mapping found for node\", node]);\n                throw new Error(\"Error parsing nodes\");\n            }\n            if (mapping.flowGraphMapping.validation) {\n                if (!mapping.flowGraphMapping.validation(node, this._interactivityGraph, this._gltf)) {\n                    throw new Error(`Error validating interactivity node ${node}`);\n                }\n            }\n            const blocks = [];\n            // create block(s) for this node using the mapping\n            for (const blockType of mapping.flowGraphMapping.blocks) {\n                const block = this._getEmptyBlock(blockType, mapping.fullOperationName);\n                this._parseNodeConfiguration(node, block, mapping.flowGraphMapping, blockType);\n                blocks.push(block);\n            }\n            this._nodes.push({ blocks, fullOperationName: mapping.fullOperationName });\n        }\n    }\n    _getEmptyBlock(className, type) {\n        const uniqueId = RandomGUID();\n        const dataInputs = [];\n        const dataOutputs = [];\n        const signalInputs = [];\n        const signalOutputs = [];\n        const config = {};\n        const metadata = {};\n        return {\n            uniqueId,\n            className,\n            dataInputs,\n            dataOutputs,\n            signalInputs,\n            signalOutputs,\n            config,\n            type,\n            metadata,\n        };\n    }\n    _parseNodeConfiguration(node, block, nodeMapping, blockType) {\n        const configuration = block.config;\n        if (node.configuration) {\n            Object.keys(node.configuration).forEach((key) => {\n                const value = node.configuration?.[key];\n                // value is always an array, never a number or string\n                if (!value) {\n                    Logger.Error([\"No value found for node configuration\", key]);\n                    throw new Error(\"Error parsing node configuration\");\n                }\n                const configMapping = nodeMapping.configuration?.[key];\n                const belongsToBlock = configMapping && configMapping.toBlock ? configMapping.toBlock === blockType : nodeMapping.blocks.indexOf(blockType) === 0;\n                if (belongsToBlock) {\n                    // get the right name for the configuration key\n                    const configKey = configMapping?.name || key;\n                    if ((!value || typeof value.value === \"undefined\") && typeof configMapping?.defaultValue !== \"undefined\") {\n                        configuration[configKey] = {\n                            value: configMapping.defaultValue,\n                        };\n                    }\n                    else if (value.value.length >= 1) {\n                        // supporting int[] and int/boolean/string\n                        configuration[configKey] = {\n                            value: value.value.length === 1 ? value.value[0] : value.value,\n                        };\n                    }\n                    else {\n                        Logger.Warn([\"Invalid value for node configuration\", value]);\n                    }\n                    // make sure we transform the data if needed\n                    if (configMapping && configMapping.dataTransformer) {\n                        configuration[configKey].value = configMapping.dataTransformer([configuration[configKey].value], this)[0];\n                    }\n                }\n            });\n        }\n    }\n    _parseNodeConnections(context) {\n        for (let i = 0; i < this._nodes.length; i++) {\n            // get the corresponding gltf node\n            const gltfNode = this._interactivityGraph.nodes?.[i];\n            if (!gltfNode) {\n                // should never happen but let's still check\n                Logger.Error([\"No node found for interactivity node\", this._nodes[i]]);\n                throw new Error(\"Error parsing node connections\");\n            }\n            const flowGraphBlocks = this._nodes[i];\n            const outputMapper = this._mappings[gltfNode.declaration];\n            // validate\n            if (!outputMapper) {\n                Logger.Error([\"No mapping found for node\", gltfNode]);\n                throw new Error(\"Error parsing node connections\");\n            }\n            const flowsFromGLTF = gltfNode.flows || {};\n            const flowsKeys = Object.keys(flowsFromGLTF).sort(); // sorting as some operations require sorted keys\n            // connect the flows\n            for (const flowKey of flowsKeys) {\n                const flow = flowsFromGLTF[flowKey];\n                const flowMapping = outputMapper.flowGraphMapping.outputs?.flows?.[flowKey];\n                const socketOutName = flowMapping?.name || flowKey;\n                // create a serialized socket\n                const socketOut = this._createNewSocketConnection(socketOutName, true);\n                const block = (flowMapping && flowMapping.toBlock && flowGraphBlocks.blocks.find((b) => b.className === flowMapping.toBlock)) || flowGraphBlocks.blocks[0];\n                block.signalOutputs.push(socketOut);\n                // get the input node of this block\n                const inputNodeId = flow.node;\n                const nodeIn = this._nodes[inputNodeId];\n                if (!nodeIn) {\n                    Logger.Error([\"No node found for input node id\", inputNodeId]);\n                    throw new Error(\"Error parsing node connections\");\n                }\n                // get the mapper for the input node - in case it mapped to multiple blocks\n                const inputMapper = getMappingForFullOperationName(nodeIn.fullOperationName);\n                if (!inputMapper) {\n                    Logger.Error([\"No mapping found for input node\", nodeIn]);\n                    throw new Error(\"Error parsing node connections\");\n                }\n                let flowInMapping = inputMapper.inputs?.flows?.[flow.socket || \"in\"];\n                let arrayMapping = false;\n                if (!flowInMapping) {\n                    for (const key in inputMapper.inputs?.flows) {\n                        if (key.startsWith(\"[\") && key.endsWith(\"]\")) {\n                            arrayMapping = true;\n                            flowInMapping = inputMapper.inputs?.flows?.[key];\n                        }\n                    }\n                }\n                const nodeInSocketName = flowInMapping ? (arrayMapping ? flowInMapping.name.replace(\"$1\", flow.socket || \"\") : flowInMapping.name) : flow.socket || \"in\";\n                const inputBlock = (flowInMapping && flowInMapping.toBlock && nodeIn.blocks.find((b) => b.className === flowInMapping.toBlock)) || nodeIn.blocks[0];\n                // in all of the flow graph input connections, find the one with the same name as the socket\n                let socketIn = inputBlock.signalInputs.find((s) => s.name === nodeInSocketName);\n                // if the socket doesn't exist, create the input socket for the connection\n                if (!socketIn) {\n                    socketIn = this._createNewSocketConnection(nodeInSocketName);\n                    inputBlock.signalInputs.push(socketIn);\n                }\n                // connect the sockets\n                socketIn.connectedPointIds.push(socketOut.uniqueId);\n                socketOut.connectedPointIds.push(socketIn.uniqueId);\n            }\n            // connect the values\n            const valuesFromGLTF = gltfNode.values || {};\n            const valuesKeys = Object.keys(valuesFromGLTF);\n            for (const valueKey of valuesKeys) {\n                const value = valuesFromGLTF[valueKey];\n                let valueMapping = outputMapper.flowGraphMapping.inputs?.values?.[valueKey];\n                let arrayMapping = false;\n                if (!valueMapping) {\n                    for (const key in outputMapper.flowGraphMapping.inputs?.values) {\n                        if (key.startsWith(\"[\") && key.endsWith(\"]\")) {\n                            arrayMapping = true;\n                            valueMapping = outputMapper.flowGraphMapping.inputs?.values?.[key];\n                        }\n                    }\n                }\n                const socketInName = valueMapping ? (arrayMapping ? valueMapping.name.replace(\"$1\", valueKey) : valueMapping.name) : valueKey;\n                // create a serialized socket\n                const socketIn = this._createNewSocketConnection(socketInName);\n                const block = (valueMapping && valueMapping.toBlock && flowGraphBlocks.blocks.find((b) => b.className === valueMapping.toBlock)) || flowGraphBlocks.blocks[0];\n                block.dataInputs.push(socketIn);\n                if (value.value !== undefined) {\n                    const convertedValue = this._parseVariable(value, valueMapping && valueMapping.dataTransformer);\n                    context._connectionValues[socketIn.uniqueId] = convertedValue;\n                }\n                else if (typeof value.node !== \"undefined\") {\n                    const nodeOutId = value.node;\n                    const nodeOutSocketName = value.socket || \"value\";\n                    const nodeOut = this._nodes[nodeOutId];\n                    if (!nodeOut) {\n                        Logger.Error([\"No node found for output socket reference\", value]);\n                        throw new Error(\"Error parsing node connections\");\n                    }\n                    const outputMapper = getMappingForFullOperationName(nodeOut.fullOperationName);\n                    if (!outputMapper) {\n                        Logger.Error([\"No mapping found for output socket reference\", value]);\n                        throw new Error(\"Error parsing node connections\");\n                    }\n                    let valueMapping = outputMapper.outputs?.values?.[nodeOutSocketName];\n                    let arrayMapping = false;\n                    // check if there is an array mapping defined\n                    if (!valueMapping) {\n                        // search for a value mapping that has an array mapping\n                        for (const key in outputMapper.outputs?.values) {\n                            if (key.startsWith(\"[\") && key.endsWith(\"]\")) {\n                                arrayMapping = true;\n                                valueMapping = outputMapper.outputs?.values?.[key];\n                            }\n                        }\n                    }\n                    const socketOutName = valueMapping ? (arrayMapping ? valueMapping.name.replace(\"$1\", nodeOutSocketName) : valueMapping?.name) : nodeOutSocketName;\n                    const outBlock = (valueMapping && valueMapping.toBlock && nodeOut.blocks.find((b) => b.className === valueMapping.toBlock)) || nodeOut.blocks[0];\n                    let socketOut = outBlock.dataOutputs.find((s) => s.name === socketOutName);\n                    // if the socket doesn't exist, create it\n                    if (!socketOut) {\n                        socketOut = this._createNewSocketConnection(socketOutName, true);\n                        outBlock.dataOutputs.push(socketOut);\n                    }\n                    // connect the sockets\n                    socketIn.connectedPointIds.push(socketOut.uniqueId);\n                    socketOut.connectedPointIds.push(socketIn.uniqueId);\n                }\n                else {\n                    Logger.Error([\"Invalid value for value connection\", value]);\n                    throw new Error(\"Error parsing node connections\");\n                }\n            }\n            // inter block connections\n            if (outputMapper.flowGraphMapping.interBlockConnectors) {\n                for (const connector of outputMapper.flowGraphMapping.interBlockConnectors) {\n                    const input = connector.input;\n                    const output = connector.output;\n                    const isVariable = connector.isVariable;\n                    this._connectFlowGraphNodes(input, output, flowGraphBlocks.blocks[connector.inputBlockIndex], flowGraphBlocks.blocks[connector.outputBlockIndex], isVariable);\n                }\n            }\n            if (outputMapper.flowGraphMapping.extraProcessor) {\n                const declaration = this._interactivityGraph.declarations?.[gltfNode.declaration];\n                if (!declaration) {\n                    Logger.Error([\"No declaration found for extra processor\", gltfNode]);\n                    throw new Error(\"Error parsing node connections\");\n                }\n                flowGraphBlocks.blocks = outputMapper.flowGraphMapping.extraProcessor(gltfNode, declaration, outputMapper.flowGraphMapping, this, flowGraphBlocks.blocks, context, this._gltf);\n            }\n        }\n    }\n    _createNewSocketConnection(name, isOutput) {\n        return {\n            uniqueId: RandomGUID(),\n            name,\n            _connectionType: isOutput ? 1 /* FlowGraphConnectionType.Output */ : 0 /* FlowGraphConnectionType.Input */,\n            connectedPointIds: [],\n        };\n    }\n    _connectFlowGraphNodes(input, output, serializedInput, serializedOutput, isVariable) {\n        const inputArray = isVariable ? serializedInput.dataInputs : serializedInput.signalInputs;\n        const outputArray = isVariable ? serializedOutput.dataOutputs : serializedOutput.signalOutputs;\n        const inputConnection = inputArray.find((s) => s.name === input) || this._createNewSocketConnection(input);\n        const outputConnection = outputArray.find((s) => s.name === output) || this._createNewSocketConnection(output, true);\n        // of not found add it to the array\n        if (!inputArray.find((s) => s.name === input)) {\n            inputArray.push(inputConnection);\n        }\n        if (!outputArray.find((s) => s.name === output)) {\n            outputArray.push(outputConnection);\n        }\n        // connect the sockets\n        inputConnection.connectedPointIds.push(outputConnection.uniqueId);\n        outputConnection.connectedPointIds.push(inputConnection.uniqueId);\n    }\n    getVariableName(index) {\n        return \"staticVariable_\" + index;\n    }\n    serializeToFlowGraph() {\n        const context = {\n            uniqueId: RandomGUID(),\n            _userVariables: {},\n            _connectionValues: {},\n        };\n        this._parseNodeConnections(context);\n        for (let i = 0; i < this._staticVariables.length; i++) {\n            const variable = this._staticVariables[i];\n            context._userVariables[this.getVariableName(i)] = variable;\n        }\n        const allBlocks = this._nodes.reduce((acc, val) => acc.concat(val.blocks), []);\n        return {\n            rightHanded: true,\n            allBlocks,\n            executionContexts: [context],\n        };\n    }\n}\n//# sourceMappingURL=interactivityGraphParser.js.map", "import { FlowGraphCoordinator } from \"@babylonjs/core/FlowGraph/flowGraphCoordinator.js\";\nimport { ParseFlowGraphAsync } from \"@babylonjs/core/FlowGraph/flowGraphParser.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nimport { AddObjectAccessorToKey, GetPathToObjectConverter } from \"./objectModelMapping.js\";\nimport { InteractivityGraphToFlowGraphParser } from \"./KHR_interactivity/interactivityGraphParser.js\";\nimport { addToBlockFactory } from \"@babylonjs/core/FlowGraph/Blocks/flowGraphBlockFactory.js\";\nimport { Quaternion, Vector3 } from \"@babylonjs/core/Maths/math.vector.js\";\nconst NAME = \"KHR_interactivity\";\n/**\n * Loader extension for KHR_interactivity\n */\nexport class KHR_interactivity {\n    /**\n     * @internal\n     * @param _loader\n     */\n    constructor(_loader) {\n        this._loader = _loader;\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n        this._pathConverter = GetPathToObjectConverter(this._loader.gltf);\n        // avoid starting animations automatically.\n        _loader._skipStartAnimationStep = true;\n        // Update object model with new pointers\n        const scene = _loader.babylonScene;\n        if (scene) {\n            _AddInteractivityObjectModel(scene);\n        }\n    }\n    dispose() {\n        this._loader = null;\n        delete this._pathConverter;\n    }\n    async onReady() {\n        if (!this._loader.babylonScene || !this._pathConverter) {\n            return;\n        }\n        const scene = this._loader.babylonScene;\n        const interactivityDefinition = this._loader.gltf.extensions?.KHR_interactivity;\n        if (!interactivityDefinition) {\n            // This can technically throw, but it's not a critical error\n            return;\n        }\n        const coordinator = new FlowGraphCoordinator({ scene });\n        coordinator.dispatchEventsSynchronously = false; // glTF interactivity dispatches events asynchronously\n        const graphs = interactivityDefinition.graphs.map((graph) => {\n            const parser = new InteractivityGraphToFlowGraphParser(graph, this._loader.gltf, this._loader);\n            return parser.serializeToFlowGraph();\n        });\n        // parse each graph async\n        await Promise.all(graphs.map((graph) => ParseFlowGraphAsync(graph, { coordinator, pathConverter: this._pathConverter })));\n        coordinator.start();\n    }\n}\n/**\n * @internal\n * populates the object model with the interactivity extension\n */\nexport function _AddInteractivityObjectModel(scene) {\n    // Note - all of those are read-only, as per the specs!\n    // active camera rotation\n    AddObjectAccessorToKey(\"/extensions/KHR_interactivity/?/activeCamera/rotation\", {\n        get: () => {\n            if (!scene.activeCamera) {\n                return new Quaternion(NaN, NaN, NaN, NaN);\n            }\n            return Quaternion.FromRotationMatrix(scene.activeCamera.getWorldMatrix()).normalize();\n        },\n        type: \"Quaternion\",\n        getTarget: () => scene.activeCamera,\n    });\n    // activeCamera position\n    AddObjectAccessorToKey(\"/extensions/KHR_interactivity/?/activeCamera/position\", {\n        get: () => {\n            if (!scene.activeCamera) {\n                return new Vector3(NaN, NaN, NaN);\n            }\n            return scene.activeCamera.position; // not global position\n        },\n        type: \"Vector3\",\n        getTarget: () => scene.activeCamera,\n    });\n    // /animations/{} pointers:\n    AddObjectAccessorToKey(\"/animations/{}/extensions/KHR_interactivity/isPlaying\", {\n        get: (animation) => {\n            return animation._babylonAnimationGroup?.isPlaying ?? false;\n        },\n        type: \"boolean\",\n        getTarget: (animation) => {\n            return animation._babylonAnimationGroup;\n        },\n    });\n    AddObjectAccessorToKey(\"/animations/{}/extensions/KHR_interactivity/minTime\", {\n        get: (animation) => {\n            return (animation._babylonAnimationGroup?.from ?? 0) / 60; // fixed factor for duration-to-frames conversion\n        },\n        type: \"number\",\n        getTarget: (animation) => {\n            return animation._babylonAnimationGroup;\n        },\n    });\n    AddObjectAccessorToKey(\"/animations/{}/extensions/KHR_interactivity/maxTime\", {\n        get: (animation) => {\n            return (animation._babylonAnimationGroup?.to ?? 0) / 60; // fixed factor for duration-to-frames conversion\n        },\n        type: \"number\",\n        getTarget: (animation) => {\n            return animation._babylonAnimationGroup;\n        },\n    });\n    // playhead\n    AddObjectAccessorToKey(\"/animations/{}/extensions/KHR_interactivity/playhead\", {\n        get: (animation) => {\n            return (animation._babylonAnimationGroup?.getCurrentFrame() ?? 0) / 60; // fixed factor for duration-to-frames conversion\n        },\n        type: \"number\",\n        getTarget: (animation) => {\n            return animation._babylonAnimationGroup;\n        },\n    });\n    //virtualPlayhead - TODO, do we support this property in our animations? getCurrentFrame  is the only method we have for this.\n    AddObjectAccessorToKey(\"/animations/{}/extensions/KHR_interactivity/virtualPlayhead\", {\n        get: (animation) => {\n            return (animation._babylonAnimationGroup?.getCurrentFrame() ?? 0) / 60; // fixed factor for duration-to-frames conversion\n        },\n        type: \"number\",\n        getTarget: (animation) => {\n            return animation._babylonAnimationGroup;\n        },\n    });\n}\n// Register flow graph blocks. Do it here so they are available when the extension is enabled.\naddToBlockFactory(NAME, \"FlowGraphGLTFDataProvider\", async () => {\n    return (await import(\"./KHR_interactivity/flowGraphGLTFDataProvider.js\")).FlowGraphGLTFDataProvider;\n});\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_interactivity(loader));\n//# sourceMappingURL=KHR_interactivity.js.map"], "file": "_app/immutable/chunks/KHR_interactivity.DEAVS2UW.js"}