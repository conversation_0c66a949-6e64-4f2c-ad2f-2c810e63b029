import{c as a}from"./declarationMapper-BZjsjg7g.js";import{h as l,R as r}from"./index-Dpxo-yl_.js";import{b as n}from"./KHR_interactivity-DTxiAnOo.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class t extends n{constructor(i){super(i),this.config=i,this.condition=this.registerDataInput("condition",a),this.executionFlow=this._registerSignalOutput("executionFlow"),this.completed=this._registerSignalOutput("completed"),this._unregisterSignalOutput("out")}_execute(i,s){let o=this.condition.getValue(i);this.config?.doWhile&&!o&&this.executionFlow._activateSignal(i);let e=0;for(;o;){if(this.executionFlow._activateSignal(i),++e,e>=t.MaxLoopCount){l.Warn("FlowGraphWhileLoopBlock: Max loop count reached. Breaking.");break}o=this.condition.getValue(i)}this.completed._activateSignal(i)}getClassName(){return"FlowGraphWhileLoopBlock"}}t.MaxLoopCount=1e3;r("FlowGraphWhileLoopBlock",t);export{t as FlowGraphWhileLoopBlock};
//# sourceMappingURL=flowGraphWhileLoopBlock--KMS7QCt.js.map
