{"version": 3, "file": "KHR_texture_transform.DzMk5bDN.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_texture_transform.js"], "sourcesContent": ["import { Texture } from \"@babylonjs/core/Materials/Textures/texture.js\";\nimport { GLTFLoader } from \"../glTFLoader.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"KHR_texture_transform\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_texture_transform/README.md)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_texture_transform {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /**\n     * @internal\n     */\n    loadTextureInfoAsync(context, textureInfo, assign) {\n        return GLTFLoader.LoadExtensionAsync(context, textureInfo, this.name, (extensionContext, extension) => {\n            return this._loader.loadTextureInfoAsync(context, textureInfo, (babylonTexture) => {\n                if (!(babylonTexture instanceof Texture)) {\n                    throw new Error(`${extensionContext}: Texture type not supported`);\n                }\n                if (extension.offset) {\n                    babylonTexture.uOffset = extension.offset[0];\n                    babylonTexture.vOffset = extension.offset[1];\n                }\n                // Always rotate around the origin.\n                babylonTexture.uRotationCenter = 0;\n                babylonTexture.vRotationCenter = 0;\n                if (extension.rotation) {\n                    babylonTexture.wAng = -extension.rotation;\n                }\n                if (extension.scale) {\n                    babylonTexture.uScale = extension.scale[0];\n                    babylonTexture.vScale = extension.scale[1];\n                }\n                if (extension.texCoord != undefined) {\n                    babylonTexture.coordinatesIndex = extension.texCoord;\n                }\n                assign(babylonTexture);\n            });\n        });\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_texture_transform(loader));\n//# sourceMappingURL=KHR_texture_transform.js.map"], "names": ["NAME", "KHR_texture_transform", "loader", "context", "textureInfo", "assign", "GLTFLoader", "extensionContext", "extension", "babylonTexture", "Texture", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "8GAGA,MAAMA,EAAO,wBAKN,MAAMC,CAAsB,CAI/B,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EACZ,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,CACnD,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAID,qBAAqBG,EAASC,EAAaC,EAAQ,CAC/C,OAAOC,EAAW,mBAAmBH,EAASC,EAAa,KAAK,KAAM,CAACG,EAAkBC,IAC9E,KAAK,QAAQ,qBAAqBL,EAASC,EAAcK,GAAmB,CAC/E,GAAI,EAAEA,aAA0BC,GAC5B,MAAM,IAAI,MAAM,GAAGH,CAAgB,8BAA8B,EAEjEC,EAAU,SACVC,EAAe,QAAUD,EAAU,OAAO,CAAC,EAC3CC,EAAe,QAAUD,EAAU,OAAO,CAAC,GAG/CC,EAAe,gBAAkB,EACjCA,EAAe,gBAAkB,EAC7BD,EAAU,WACVC,EAAe,KAAO,CAACD,EAAU,UAEjCA,EAAU,QACVC,EAAe,OAASD,EAAU,MAAM,CAAC,EACzCC,EAAe,OAASD,EAAU,MAAM,CAAC,GAEzCA,EAAU,UAAY,OACtBC,EAAe,iBAAmBD,EAAU,UAEhDH,EAAOI,CAAc,CACrC,CAAa,CACJ,CACJ,CACL,CACAE,EAAwBX,CAAI,EAC5BY,EAAsBZ,EAAM,GAAOE,GAAW,IAAID,EAAsBC,CAAM,CAAC", "x_google_ignoreList": [0]}