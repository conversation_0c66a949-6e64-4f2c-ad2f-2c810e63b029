const{SvelteComponent:N,append:h,attr:C,destroy_each:q,detach:_,element:d,empty:E,ensure_array_like:m,flush:g,init:S,insert:o,noop:b,safe_not_equal:j,set_data:z,space:B,text:v,toggle_class:u}=window.__gradio__svelte__internal;function k(a,e,l){const i=a.slice();return i[3]=e[l],i}function p(a){let e,l=Array.isArray(a[0])&&a[0].length>3,i,f=m(Array.isArray(a[0])?a[0].slice(0,3):[a[0]]),s=[];for(let t=0;t<f.length;t+=1)s[t]=A(k(a,f,t));let n=l&&w();return{c(){for(let t=0;t<s.length;t+=1)s[t].c();e=B(),n&&n.c(),i=E()},m(t,c){for(let r=0;r<s.length;r+=1)s[r]&&s[r].m(t,c);o(t,e,c),n&&n.m(t,c),o(t,i,c)},p(t,c){if(c&1){f=m(Array.isArray(t[0])?t[0].slice(0,3):[t[0]]);let r;for(r=0;r<f.length;r+=1){const y=k(t,f,r);s[r]?s[r].p(y,c):(s[r]=A(y),s[r].c(),s[r].m(e.parentNode,e))}for(;r<s.length;r+=1)s[r].d(1);s.length=f.length}c&1&&(l=Array.isArray(t[0])&&t[0].length>3),l?n||(n=w(),n.c(),n.m(i.parentNode,i)):n&&(n.d(1),n=null)},d(t){t&&(_(e),_(i)),q(s,t),n&&n.d(t)}}}function A(a){let e,l,i,f=a[3]+"",s;return{c(){e=d("li"),l=d("code"),i=v("./"),s=v(f)},m(n,t){o(n,e,t),h(e,l),h(l,i),h(l,s)},p(n,t){t&1&&f!==(f=n[3]+"")&&z(s,f)},d(n){n&&_(e)}}}function w(a){let e;return{c(){e=d("li"),e.textContent="...",C(e,"class","extra svelte-4tf8f")},m(l,i){o(l,e,i)},d(l){l&&_(e)}}}function D(a){let e,l=a[0]&&p(a);return{c(){e=d("ul"),l&&l.c(),C(e,"class","svelte-4tf8f"),u(e,"table",a[1]==="table"),u(e,"gallery",a[1]==="gallery"),u(e,"selected",a[2])},m(i,f){o(i,e,f),l&&l.m(e,null)},p(i,[f]){i[0]?l?l.p(i,f):(l=p(i),l.c(),l.m(e,null)):l&&(l.d(1),l=null),f&2&&u(e,"table",i[1]==="table"),f&2&&u(e,"gallery",i[1]==="gallery"),f&4&&u(e,"selected",i[2])},i:b,o:b,d(i){i&&_(e),l&&l.d()}}}function F(a,e,l){let{value:i}=e,{type:f}=e,{selected:s=!1}=e;return a.$$set=n=>{"value"in n&&l(0,i=n.value),"type"in n&&l(1,f=n.type),"selected"in n&&l(2,s=n.selected)},[i,f,s]}class G extends N{constructor(e){super(),S(this,e,F,D,j,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),g()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),g()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),g()}}export{G as default};
//# sourceMappingURL=Example-CIFMxn5c.js.map
