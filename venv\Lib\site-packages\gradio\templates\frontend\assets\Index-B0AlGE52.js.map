{"version": 3, "file": "Index-B0AlGE52.js", "sources": ["../../../../js/checkbox/Index.svelte"], "sourcesContent": ["<script context=\"module\" lang=\"ts\">\n\texport { default as BaseCheckbox } from \"./shared/Checkbox.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport { Block, Info } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { afterUpdate } from \"svelte\";\n\timport BaseCheckbox from \"./shared/Checkbox.svelte\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value = false;\n\texport let value_is_output = false;\n\texport let label = \"Checkbox\";\n\texport let info: string | undefined = undefined;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t\tinput: never;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let interactive: boolean;\n\n\tfunction handle_change(): void {\n\t\tgradio.dispatch(\"change\");\n\t\tif (!value_is_output) {\n\t\t\tgradio.dispatch(\"input\");\n\t\t}\n\t}\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\n\t// When the value changes, dispatch the change event via handle_change()\n\t// See the docs for an explanation: https://svelte.dev/docs/svelte-components#script-3-$-marks-a-statement-as-reactive\n</script>\n\n<Block {visible} {elem_id} {elem_classes} {container} {scale} {min_width}>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\n\t{#if info}\n\t\t<Info {info} />\n\t{/if}\n\n\t<BaseCheckbox\n\t\tbind:value\n\t\t{label}\n\t\t{interactive}\n\t\ton:change={handle_change}\n\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t/>\n</Block>\n"], "names": ["ctx", "create_if_block", "dirty", "elem_id", "$$props", "elem_classes", "visible", "value", "value_is_output", "label", "info", "container", "scale", "min_width", "loading_status", "gradio", "interactive", "handle_change", "afterUpdate", "clear_status_handler", "e"], "mappings": "g4BAU6B,EAAA,OAAA,0SAsCf,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,yGAIdA,EAAI,CAAA,GAAAC,EAAAD,CAAA,gKAQGA,EAAa,EAAA,CAAA,sLAdZ,WAAAA,MAAO,YACbE,EAAA,MAAA,CAAA,KAAAF,MAAO,IAAI,WACbA,EAAc,CAAA,CAAA,iBAIdA,EAAI,CAAA,44BAzCE,QAAAG,EAAU,EAAA,EAAAC,EACV,CAAA,aAAAC,EAAA,EAAA,EAAAD,GACA,QAAAE,EAAU,EAAA,EAAAF,GACV,MAAAG,EAAQ,EAAA,EAAAH,GACR,gBAAAI,EAAkB,EAAA,EAAAJ,GAClB,MAAAK,EAAQ,UAAA,EAAAL,GACR,KAAAM,EAA2B,MAAA,EAAAN,GAC3B,UAAAO,EAAY,EAAA,EAAAP,GACZ,MAAAQ,EAAuB,IAAA,EAAAR,GACvB,UAAAS,EAAgC,MAAA,EAAAT,EAChC,CAAA,eAAAU,CAAA,EAAAV,EACA,CAAA,OAAAW,CAAA,EAAAX,EAMA,CAAA,YAAAY,CAAA,EAAAZ,EAEF,SAAAa,GAAA,CACRF,EAAO,SAAS,QAAQ,EACnBP,GACJO,EAAO,SAAS,OAAO,EAGzBG,EAAA,IAAA,MACCV,EAAkB,EAAA,IAYK,MAAAW,EAAA,IAAAJ,EAAO,SAAS,eAAgBD,CAAc,mCAYzDM,GAAML,EAAO,SAAS,SAAUK,EAAE,MAAM"}