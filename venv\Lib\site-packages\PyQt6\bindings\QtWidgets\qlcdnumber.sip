// qlcdnumber.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QLCDNumber : public QFrame
{
%TypeHeaderCode
#include <qlcdnumber.h>
%End

public:
    explicit QLCDNumber(QWidget *parent /TransferThis/ = 0);
    QLCDNumber(uint numDigits, QWidget *parent /TransferThis/ = 0);
    virtual ~QLCDNumber();

    enum Mode
    {
        Hex,
        Dec,
        Oct,
        Bin,
    };

    enum SegmentStyle
    {
        Outline,
        Filled,
        Flat,
    };

    bool smallDecimalPoint() const;
    int digitCount() const;
    void setDigitCount(int nDigits);
    void setNumDigits(int nDigits);
%MethodCode
        // This is implemented for Qt v5 so that .ui files created with Designer for Qt v4 will continue to work.
        sipCpp->setDigitCount(a0);
%End

    bool checkOverflow(double num /Constrained/) const;
    bool checkOverflow(int num) const;
    QLCDNumber::Mode mode() const;
    void setMode(QLCDNumber::Mode);
    QLCDNumber::SegmentStyle segmentStyle() const;
    void setSegmentStyle(QLCDNumber::SegmentStyle);
    double value() const;
    int intValue() const;
    virtual QSize sizeHint() const;
    void display(const QString &str);
    void display(double num /Constrained/);
    void display(int num);
    void setHexMode();
    void setDecMode();
    void setOctMode();
    void setBinMode();
    void setSmallDecimalPoint(bool);

signals:
    void overflow();

protected:
    virtual bool event(QEvent *e);
    virtual void paintEvent(QPaintEvent *);
};
