import{V as c,Q as h,ap as l}from"./index.BoI39RQH.js";import{S as m}from"./objectModelMapping.ha_8hIyl.js";function g(r,n,t,e){return c.FromArray(n,t).scaleInPlace(e)}function f(r,n,t,e){return h.FromArray(n,t).scaleInPlace(e)}function y(r,n,t,e){const o=new Array(r._numMorphTargets);for(let i=0;i<o.length;i++)o[i]=n[t++]*e;return o}class p{constructor(n,t,e,o){this.type=n,this.name=t,this.getValue=e,this.getStride=o}_buildAnimation(n,t,e){const o=new l(n,this.name,t,this.type);return o.setKeys(e),o}}class u extends p{buildAnimations(n,t,e,o){const i=[];return i.push({babylonAnimatable:n._babylonTransformNode,babylonAnimation:this._buildAnimation(t,e,o)}),i}}class d extends p{buildAnimations(n,t,e,o){const i=[];if(n._numMorphTargets)for(let s=0;s<n._numMorphTargets;s++){const b=new l(`${t}_${s}`,this.name,e,this.type);if(b.setKeys(o.map(a=>({frame:a.frame,inTangent:a.inTangent?a.inTangent[s]:void 0,value:a.value[s],outTangent:a.outTangent?a.outTangent[s]:void 0,interpolation:a.interpolation}))),n._primitiveBabylonMeshes){for(const a of n._primitiveBabylonMeshes)if(a.morphTargetManager){const A=a.morphTargetManager.getTarget(s),T=b.clone();A.animations.push(T),i.push({babylonAnimatable:A,babylonAnimation:T})}}}return i}}m("/nodes/{}/translation",[new u(l.ANIMATIONTYPE_VECTOR3,"position",g,()=>3)]);m("/nodes/{}/rotation",[new u(l.ANIMATIONTYPE_QUATERNION,"rotationQuaternion",f,()=>4)]);m("/nodes/{}/scale",[new u(l.ANIMATIONTYPE_VECTOR3,"scaling",g,()=>3)]);m("/nodes/{}/weights",[new d(l.ANIMATIONTYPE_FLOAT,"influence",y,r=>r._numMorphTargets)]);export{p as AnimationPropertyInfo,u as TransformNodeAnimationPropertyInfo,d as WeightAnimationPropertyInfo,f as getQuaternion,g as getVector3,y as getWeights};
//# sourceMappingURL=glTFLoaderAnimation.CKms7EUf.js.map
