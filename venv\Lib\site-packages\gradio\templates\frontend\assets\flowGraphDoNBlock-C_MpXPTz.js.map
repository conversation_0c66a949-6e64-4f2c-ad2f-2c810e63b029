{"version": 3, "file": "flowGraphDoNBlock-C_MpXPTz.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphDoNBlock.js"], "sourcesContent": ["import { RichTypeFlowGraphInteger } from \"../../../flowGraphRichTypes.js\";\nimport { FlowGraphExecutionBlockWithOutSignal } from \"../../../flowGraphExecutionBlockWithOutSignal.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\nimport { FlowGraphInteger } from \"../../../CustomTypes/flowGraphInteger.js\";\n/**\n * A block that executes a branch a set number of times.\n */\nexport class FlowGraphDoNBlock extends FlowGraphExecutionBlockWithOutSignal {\n    constructor(\n    /**\n     * [Object] the configuration of the block\n     */\n    config = {}) {\n        super(config);\n        this.config = config;\n        this.config.startIndex = config.startIndex ?? new FlowGraphInteger(0);\n        this.reset = this._registerSignalInput(\"reset\");\n        this.maxExecutions = this.registerDataInput(\"maxExecutions\", RichTypeFlowGraphInteger);\n        this.executionCount = this.registerDataOutput(\"executionCount\", RichTypeFlowGraphInteger, new FlowGraphInteger(0));\n    }\n    _execute(context, callingSignal) {\n        if (callingSignal === this.reset) {\n            this.executionCount.setValue(this.config.startIndex, context);\n        }\n        else {\n            const currentCountValue = this.executionCount.getValue(context);\n            if (currentCountValue.value < this.maxExecutions.getValue(context).value) {\n                this.executionCount.setValue(new FlowGraphInteger(currentCountValue.value + 1), context);\n                this.out._activateSignal(context);\n            }\n        }\n    }\n    /**\n     * @returns class name of the block.\n     */\n    getClassName() {\n        return \"FlowGraphDoNBlock\" /* FlowGraphBlockNames.DoN */;\n    }\n}\nRegisterClass(\"FlowGraphDoNBlock\" /* FlowGraphBlockNames.DoN */, FlowGraphDoNBlock);\n//# sourceMappingURL=flowGraphDoNBlock.js.map"], "names": ["FlowGraphDoNBlock", "FlowGraphExecutionBlockWithOutSignal", "config", "FlowGraphInteger", "RichTypeFlowGraphInteger", "context", "callingSignal", "currentCountValue", "RegisterClass"], "mappings": "uPAOO,MAAMA,UAA0BC,CAAqC,CACxE,YAIAC,EAAS,CAAA,EAAI,CACT,MAAMA,CAAM,EACZ,KAAK,OAASA,EACd,KAAK,OAAO,WAAaA,EAAO,YAAc,IAAIC,EAAiB,CAAC,EACpE,KAAK,MAAQ,KAAK,qBAAqB,OAAO,EAC9C,KAAK,cAAgB,KAAK,kBAAkB,gBAAiBC,CAAwB,EACrF,KAAK,eAAiB,KAAK,mBAAmB,iBAAkBA,EAA0B,IAAID,EAAiB,CAAC,CAAC,CACpH,CACD,SAASE,EAASC,EAAe,CAC7B,GAAIA,IAAkB,KAAK,MACvB,KAAK,eAAe,SAAS,KAAK,OAAO,WAAYD,CAAO,MAE3D,CACD,MAAME,EAAoB,KAAK,eAAe,SAASF,CAAO,EAC1DE,EAAkB,MAAQ,KAAK,cAAc,SAASF,CAAO,EAAE,QAC/D,KAAK,eAAe,SAAS,IAAIF,EAAiBI,EAAkB,MAAQ,CAAC,EAAGF,CAAO,EACvF,KAAK,IAAI,gBAAgBA,CAAO,EAEvC,CACJ,CAID,cAAe,CACX,MAAO,mBACV,CACL,CACAG,EAAc,oBAAmDR,CAAiB", "x_google_ignoreList": [0]}