{"version": 3, "file": "flowGraphWhileLoopBlock.CE8CW0jy.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphWhileLoopBlock.js"], "sourcesContent": ["import { RichTypeBoolean } from \"../../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\nimport { FlowGraphExecutionBlockWithOutSignal } from \"../../../flowGraphExecutionBlockWithOutSignal.js\";\nimport { Logger } from \"../../../../Misc/logger.js\";\n/**\n * A block that executes a branch while a condition is true.\n */\nexport class FlowGraphWhileLoopBlock extends FlowGraphExecutionBlockWithOutSignal {\n    constructor(\n    /**\n     * the configuration of the block\n     */\n    config) {\n        super(config);\n        this.config = config;\n        this.condition = this.registerDataInput(\"condition\", RichTypeBoolean);\n        this.executionFlow = this._registerSignalOutput(\"executionFlow\");\n        this.completed = this._registerSignalOutput(\"completed\");\n        // unregister \"out\" signal\n        this._unregisterSignalOutput(\"out\");\n    }\n    _execute(context, _callingSignal) {\n        let conditionValue = this.condition.getValue(context);\n        if (this.config?.doWhile && !conditionValue) {\n            this.executionFlow._activateSignal(context);\n        }\n        let i = 0;\n        while (conditionValue) {\n            this.executionFlow._activateSignal(context);\n            ++i;\n            if (i >= FlowGraphWhileLoopBlock.MaxLoopCount) {\n                Logger.Warn(\"FlowGraphWhileLoopBlock: Max loop count reached. Breaking.\");\n                break;\n            }\n            conditionValue = this.condition.getValue(context);\n        }\n        // out is not triggered - completed is triggered\n        this.completed._activateSignal(context);\n    }\n    getClassName() {\n        return \"FlowGraphWhileLoopBlock\" /* FlowGraphBlockNames.WhileLoop */;\n    }\n}\n/**\n * The maximum number of iterations allowed in a loop.\n * This can be set to avoid an infinite loop.\n */\nFlowGraphWhileLoopBlock.MaxLoopCount = 1000;\nRegisterClass(\"FlowGraphWhileLoopBlock\" /* FlowGraphBlockNames.WhileLoop */, FlowGraphWhileLoopBlock);\n//# sourceMappingURL=flowGraphWhileLoopBlock.js.map"], "names": ["FlowGraphWhileLoopBlock", "FlowGraphExecutionBlockWithOutSignal", "config", "RichTypeBoolean", "context", "_callingSignal", "conditionValue", "_a", "i", "<PERSON><PERSON>", "RegisterClass"], "mappings": "uJAOO,MAAMA,UAAgCC,CAAqC,CAC9E,YAIAC,EAAQ,CACJ,MAAMA,CAAM,EACZ,KAAK,OAASA,EACd,KAAK,UAAY,KAAK,kBAAkB,YAAaC,CAAe,EACpE,KAAK,cAAgB,KAAK,sBAAsB,eAAe,EAC/D,KAAK,UAAY,KAAK,sBAAsB,WAAW,EAEvD,KAAK,wBAAwB,KAAK,CACrC,CACD,SAASC,EAASC,EAAgB,OAC9B,IAAIC,EAAiB,KAAK,UAAU,SAASF,CAAO,GAChDG,EAAA,KAAK,SAAL,MAAAA,EAAa,SAAW,CAACD,GACzB,KAAK,cAAc,gBAAgBF,CAAO,EAE9C,IAAII,EAAI,EACR,KAAOF,GAAgB,CAGnB,GAFA,KAAK,cAAc,gBAAgBF,CAAO,EAC1C,EAAEI,EACEA,GAAKR,EAAwB,aAAc,CAC3CS,EAAO,KAAK,4DAA4D,EACxE,KACH,CACDH,EAAiB,KAAK,UAAU,SAASF,CAAO,CACnD,CAED,KAAK,UAAU,gBAAgBA,CAAO,CACzC,CACD,cAAe,CACX,MAAO,yBACV,CACL,CAKAJ,EAAwB,aAAe,IACvCU,EAAc,0BAA+DV,CAAuB", "x_google_ignoreList": [0]}