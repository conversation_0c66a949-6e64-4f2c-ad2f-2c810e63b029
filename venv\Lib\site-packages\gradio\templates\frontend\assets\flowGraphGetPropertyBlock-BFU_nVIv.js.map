{"version": 3, "file": "flowGraphGetPropertyBlock-BFU_nVIv.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphGetPropertyBlock.js"], "sourcesContent": ["import { RichTypeAny } from \"../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../Misc/typeStore.js\";\nimport { FlowGraphCachedOperationBlock } from \"./flowGraphCachedOperationBlock.js\";\n/**\n * This block will deliver a property of an asset, based on the property name and an input asset.\n * The property name can include dots (\".\"), which will be interpreted as a path to the property.\n *\n * For example, with an input of a mesh asset, the property name \"position.x\" will deliver the x component of the position of the mesh.\n *\n * Note that it is recommended to input the object on which you are working on (i.e. a material) rather than providing a mesh as object and then getting the material from it.\n */\nexport class FlowGraphGetPropertyBlock extends FlowGraphCachedOperationBlock {\n    constructor(\n    /**\n     * the configuration of the block\n     */\n    config) {\n        super(RichTypeAny, config);\n        this.config = config;\n        this.object = this.registerDataInput(\"object\", RichTypeAny, config.object);\n        this.propertyName = this.registerDataInput(\"propertyName\", RichTypeAny, config.propertyName);\n        this.customGetFunction = this.registerDataInput(\"customGetFunction\", RichTypeAny);\n    }\n    _doOperation(context) {\n        const getter = this.customGetFunction.getValue(context);\n        let value;\n        if (getter) {\n            value = getter(this.object.getValue(context), this.propertyName.getValue(context), context);\n        }\n        else {\n            const target = this.object.getValue(context);\n            const propertyName = this.propertyName.getValue(context);\n            value = target && propertyName ? this._getPropertyValue(target, propertyName) : undefined;\n        }\n        return value;\n    }\n    _getPropertyValue(target, propertyName) {\n        const path = propertyName.split(\".\");\n        let value = target;\n        for (const prop of path) {\n            value = value[prop];\n            if (value === undefined) {\n                return;\n            }\n        }\n        return value;\n    }\n    getClassName() {\n        return \"FlowGraphGetPropertyBlock\" /* FlowGraphBlockNames.GetProperty */;\n    }\n}\nRegisterClass(\"FlowGraphGetPropertyBlock\" /* FlowGraphBlockNames.GetProperty */, FlowGraphGetPropertyBlock);\n//# sourceMappingURL=flowGraphGetPropertyBlock.js.map"], "names": ["FlowGraphGetPropertyBlock", "FlowGraphCachedOperationBlock", "config", "RichTypeAny", "context", "getter", "value", "target", "propertyName", "path", "prop", "RegisterClass"], "mappings": "oSAWO,MAAMA,UAAkCC,CAA8B,CACzE,YAIAC,EAAQ,CACJ,MAAMC,EAAaD,CAAM,EACzB,KAAK,OAASA,EACd,KAAK,OAAS,KAAK,kBAAkB,SAAUC,EAAaD,EAAO,MAAM,EACzE,KAAK,aAAe,KAAK,kBAAkB,eAAgBC,EAAaD,EAAO,YAAY,EAC3F,KAAK,kBAAoB,KAAK,kBAAkB,oBAAqBC,CAAW,CACnF,CACD,aAAaC,EAAS,CAClB,MAAMC,EAAS,KAAK,kBAAkB,SAASD,CAAO,EACtD,IAAIE,EACJ,GAAID,EACAC,EAAQD,EAAO,KAAK,OAAO,SAASD,CAAO,EAAG,KAAK,aAAa,SAASA,CAAO,EAAGA,CAAO,MAEzF,CACD,MAAMG,EAAS,KAAK,OAAO,SAASH,CAAO,EACrCI,EAAe,KAAK,aAAa,SAASJ,CAAO,EACvDE,EAAQC,GAAUC,EAAe,KAAK,kBAAkBD,EAAQC,CAAY,EAAI,MACnF,CACD,OAAOF,CACV,CACD,kBAAkBC,EAAQC,EAAc,CACpC,MAAMC,EAAOD,EAAa,MAAM,GAAG,EACnC,IAAIF,EAAQC,EACZ,UAAWG,KAAQD,EAEf,GADAH,EAAQA,EAAMI,CAAI,EACdJ,IAAU,OACV,OAGR,OAAOA,CACV,CACD,cAAe,CACX,MAAO,2BACV,CACL,CACAK,EAAc,4BAAmEX,CAAyB", "x_google_ignoreList": [0]}