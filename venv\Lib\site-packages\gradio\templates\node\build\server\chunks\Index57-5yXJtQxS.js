import { c as create_ssr_component, b as createEventDispatcher, v as validate_component, e as escape, f as each, d as add_attribute, h as add_styles } from './ssr-C3HYbsxA.js';
import { N as xt, ai as ae, ag as Tt, m as mt, z as zA } from './2-DJbI4FWc.js';
export { default as BaseExample } from './Example11-Dm_QueAJ.js';
import './index-ClteBeTX.js';
import './Component-NmRBwSfF.js';
import 'path';
import 'url';
import 'fs';

const H={code:".options.svelte-y6qw75{--window-padding:var(--size-8);position:fixed;z-index:var(--layer-top);margin-left:0;box-shadow:var(--shadow-drop-lg);border-radius:var(--container-radius);background:var(--background-fill-primary);min-width:fit-content;max-width:inherit;overflow:auto;color:var(--body-text-color);list-style:none}.item.svelte-y6qw75{display:flex;cursor:pointer;padding:var(--size-2);word-break:break-word}.item.svelte-y6qw75:hover,.active.svelte-y6qw75{background:var(--background-fill-secondary)}.inner-item.svelte-y6qw75{padding-right:var(--size-1)}.hide.svelte-y6qw75{visibility:hidden}",map:'{"version":3,"file":"DropdownOptions.svelte","sources":["DropdownOptions.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { fly } from \\"svelte/transition\\";\\nimport { createEventDispatcher } from \\"svelte\\";\\nexport let choices;\\nexport let filtered_indices;\\nexport let show_options = false;\\nexport let disabled = false;\\nexport let selected_indices = [];\\nexport let active_index = null;\\nexport let remember_scroll = false;\\nlet distance_from_top;\\nlet distance_from_bottom;\\nlet input_height;\\nlet input_width;\\nlet refElement;\\nlet listElement;\\nlet top, bottom, max_height;\\nlet innerHeight;\\nlet list_scroll_y = 0;\\nfunction calculate_window_distance() {\\n    const { top: ref_top, bottom: ref_bottom } = refElement.getBoundingClientRect();\\n    distance_from_top = ref_top;\\n    distance_from_bottom = innerHeight - ref_bottom;\\n}\\nlet scroll_timeout = null;\\nfunction scroll_listener() {\\n    if (!show_options)\\n        return;\\n    if (scroll_timeout !== null) {\\n        clearTimeout(scroll_timeout);\\n    }\\n    scroll_timeout = setTimeout(() => {\\n        calculate_window_distance();\\n        scroll_timeout = null;\\n    }, 10);\\n}\\nfunction restore_last_scroll() {\\n    listElement?.scrollTo?.(0, list_scroll_y);\\n}\\n$: {\\n    if (show_options && refElement) {\\n        if (remember_scroll) {\\n            restore_last_scroll();\\n        }\\n        else {\\n            if (listElement && selected_indices.length > 0) {\\n                let elements = listElement.querySelectorAll(\\"li\\");\\n                for (const element of Array.from(elements)) {\\n                    if (element.getAttribute(\\"data-index\\") === selected_indices[0].toString()) {\\n                        listElement?.scrollTo?.(0, element.offsetTop);\\n                        break;\\n                    }\\n                }\\n            }\\n        }\\n        calculate_window_distance();\\n        const rect = refElement.parentElement?.getBoundingClientRect();\\n        input_height = rect?.height || 0;\\n        input_width = rect?.width || 0;\\n    }\\n    if (distance_from_bottom > distance_from_top) {\\n        top = `${distance_from_top}px`;\\n        max_height = distance_from_bottom;\\n        bottom = null;\\n    }\\n    else {\\n        bottom = `${distance_from_bottom + input_height}px`;\\n        max_height = distance_from_top - input_height;\\n        top = null;\\n    }\\n}\\nconst dispatch = createEventDispatcher();\\n<\/script>\\n\\n<svelte:window on:scroll={scroll_listener} bind:innerHeight />\\n\\n<div class=\\"reference\\" bind:this={refElement} />\\n{#if show_options && !disabled}\\n\\t<ul\\n\\t\\tclass=\\"options\\"\\n\\t\\ttransition:fly={{ duration: 200, y: 5 }}\\n\\t\\ton:mousedown|preventDefault={(e) => dispatch(\\"change\\", e)}\\n\\t\\ton:scroll={(e) => (list_scroll_y = e.currentTarget.scrollTop)}\\n\\t\\tstyle:top\\n\\t\\tstyle:bottom\\n\\t\\tstyle:max-height={`calc(${max_height}px - var(--window-padding))`}\\n\\t\\tstyle:width={input_width + \\"px\\"}\\n\\t\\tbind:this={listElement}\\n\\t\\trole=\\"listbox\\"\\n\\t>\\n\\t\\t{#each filtered_indices as index}\\n\\t\\t\\t<li\\n\\t\\t\\t\\tclass=\\"item\\"\\n\\t\\t\\t\\tclass:selected={selected_indices.includes(index)}\\n\\t\\t\\t\\tclass:active={index === active_index}\\n\\t\\t\\t\\tclass:bg-gray-100={index === active_index}\\n\\t\\t\\t\\tclass:dark:bg-gray-600={index === active_index}\\n\\t\\t\\t\\tstyle:width={input_width + \\"px\\"}\\n\\t\\t\\t\\tdata-index={index}\\n\\t\\t\\t\\taria-label={choices[index][0]}\\n\\t\\t\\t\\tdata-testid=\\"dropdown-option\\"\\n\\t\\t\\t\\trole=\\"option\\"\\n\\t\\t\\t\\taria-selected={selected_indices.includes(index)}\\n\\t\\t\\t>\\n\\t\\t\\t\\t<span class:hide={!selected_indices.includes(index)} class=\\"inner-item\\">\\n\\t\\t\\t\\t\\t✓\\n\\t\\t\\t\\t</span>\\n\\t\\t\\t\\t{choices[index][0]}\\n\\t\\t\\t</li>\\n\\t\\t{/each}\\n\\t</ul>\\n{/if}\\n\\n<style>\\n\\t.options {\\n\\t\\t--window-padding: var(--size-8);\\n\\t\\tposition: fixed;\\n\\t\\tz-index: var(--layer-top);\\n\\t\\tmargin-left: 0;\\n\\t\\tbox-shadow: var(--shadow-drop-lg);\\n\\t\\tborder-radius: var(--container-radius);\\n\\t\\tbackground: var(--background-fill-primary);\\n\\t\\tmin-width: fit-content;\\n\\t\\tmax-width: inherit;\\n\\t\\toverflow: auto;\\n\\t\\tcolor: var(--body-text-color);\\n\\t\\tlist-style: none;\\n\\t}\\n\\n\\t.item {\\n\\t\\tdisplay: flex;\\n\\t\\tcursor: pointer;\\n\\t\\tpadding: var(--size-2);\\n\\t\\tword-break: break-word;\\n\\t}\\n\\n\\t.item:hover,\\n\\t.active {\\n\\t\\tbackground: var(--background-fill-secondary);\\n\\t}\\n\\n\\t.inner-item {\\n\\t\\tpadding-right: var(--size-1);\\n\\t}\\n\\n\\t.hide {\\n\\t\\tvisibility: hidden;\\n\\t}</style>\\n"],"names":[],"mappings":"AAiHC,sBAAS,CACR,gBAAgB,CAAE,aAAa,CAC/B,QAAQ,CAAE,KAAK,CACf,OAAO,CAAE,IAAI,WAAW,CAAC,CACzB,WAAW,CAAE,CAAC,CACd,UAAU,CAAE,IAAI,gBAAgB,CAAC,CACjC,aAAa,CAAE,IAAI,kBAAkB,CAAC,CACtC,UAAU,CAAE,IAAI,yBAAyB,CAAC,CAC1C,SAAS,CAAE,WAAW,CACtB,SAAS,CAAE,OAAO,CAClB,QAAQ,CAAE,IAAI,CACd,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,UAAU,CAAE,IACb,CAEA,mBAAM,CACL,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,OAAO,CACf,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,UAAU,CAAE,UACb,CAEA,mBAAK,MAAM,CACX,qBAAQ,CACP,UAAU,CAAE,IAAI,2BAA2B,CAC5C,CAEA,yBAAY,CACX,aAAa,CAAE,IAAI,QAAQ,CAC5B,CAEA,mBAAM,CACL,UAAU,CAAE,MACb"}'},G=create_ssr_component((l,t,e,M)=>{let{choices:d}=t,{filtered_indices:m}=t,{show_options:n=!1}=t,{disabled:b=!1}=t,{selected_indices:_=[]}=t,{active_index:i=null}=t,{remember_scroll:c=!1}=t,u,f,w,C,p,y,x,E,h,a;function I(){const{top:s,bottom:v}=p.getBoundingClientRect();u=s,f=a-v;}return createEventDispatcher(),t.choices===void 0&&e.choices&&d!==void 0&&e.choices(d),t.filtered_indices===void 0&&e.filtered_indices&&m!==void 0&&e.filtered_indices(m),t.show_options===void 0&&e.show_options&&n!==void 0&&e.show_options(n),t.disabled===void 0&&e.disabled&&b!==void 0&&e.disabled(b),t.selected_indices===void 0&&e.selected_indices&&_!==void 0&&e.selected_indices(_),t.active_index===void 0&&e.active_index&&i!==void 0&&e.active_index(i),t.remember_scroll===void 0&&e.remember_scroll&&c!==void 0&&e.remember_scroll(c),l.css.add(H),n&&p&&(I(),w=0,C=0),f>u?(x=`${u}px`,h=f,E=null):(E=`${f+w}px`,h=u-w,x=null),` <div class="reference"${add_attribute("this",p,0)}></div> ${n&&!b?`<ul class="options svelte-y6qw75" role="listbox"${add_styles({top:x,bottom:E,"max-height":`calc(${h}px - var(--window-padding))`,width:C+"px"})}${add_attribute("this",y,0)}>${each(m,s=>`<li class="${["item svelte-y6qw75",(_.includes(s)?"selected":"")+" "+(s===i?"active":"")+" "+(s===i?"bg-gray-100":"")+" "+(s===i?"dark:bg-gray-600":"")].join(" ").trim()}"${add_attribute("data-index",s,0)}${add_attribute("aria-label",d[s][0],0)} data-testid="dropdown-option" role="option"${add_attribute("aria-selected",_.includes(s),0)}${add_styles({width:C+"px"})}><span class="${["inner-item svelte-y6qw75",_.includes(s)?"":"hide"].join(" ").trim()}" data-svelte-h="svelte-1id9b8g">✓</span> ${escape(d[s][0])} </li>`)}</ul>`:""}`});function S(l,t){return l.reduce((e,M,d)=>((!t||M[0].toLowerCase().includes(t.toLowerCase()))&&e.push(d),e),[])}function J(l,t,e){l("change",t),e||l("input");}const V={code:`.icon-wrap.svelte-1scun43.svelte-1scun43.svelte-1scun43{color:var(--body-text-color);margin-right:var(--size-2);width:var(--size-5)}label.svelte-1scun43.svelte-1scun43.svelte-1scun43:not(.container),label.svelte-1scun43:not(.container) .wrap.svelte-1scun43.svelte-1scun43,label.svelte-1scun43:not(.container) .wrap-inner.svelte-1scun43.svelte-1scun43,label.svelte-1scun43:not(.container) .secondary-wrap.svelte-1scun43.svelte-1scun43,label.svelte-1scun43:not(.container) .token.svelte-1scun43.svelte-1scun43,label.svelte-1scun43:not(.container) input.svelte-1scun43.svelte-1scun43{height:100%}.container.svelte-1scun43 .wrap.svelte-1scun43.svelte-1scun43{box-shadow:var(--input-shadow);border:var(--input-border-width) solid var(--border-color-primary)}.wrap.svelte-1scun43.svelte-1scun43.svelte-1scun43{position:relative;border-radius:var(--input-radius);background:var(--input-background-fill)}.wrap.svelte-1scun43.svelte-1scun43.svelte-1scun43:focus-within{box-shadow:var(--input-shadow-focus);border-color:var(--input-border-color-focus)}.wrap-inner.svelte-1scun43.svelte-1scun43.svelte-1scun43{display:flex;position:relative;flex-wrap:wrap;align-items:center;gap:var(--checkbox-label-gap);padding:var(--checkbox-label-padding)}.token.svelte-1scun43.svelte-1scun43.svelte-1scun43{display:flex;align-items:center;transition:var(--button-transition);cursor:pointer;box-shadow:var(--checkbox-label-shadow);border:var(--checkbox-label-border-width) solid
			var(--checkbox-label-border-color);border-radius:var(--button-small-radius);background:var(--checkbox-label-background-fill);padding:var(--checkbox-label-padding);color:var(--checkbox-label-text-color);font-weight:var(--checkbox-label-text-weight);font-size:var(--checkbox-label-text-size);line-height:var(--line-md);word-break:break-word}.token.svelte-1scun43>.svelte-1scun43+.svelte-1scun43{margin-left:var(--size-2)}.token-remove.svelte-1scun43.svelte-1scun43.svelte-1scun43{fill:var(--body-text-color);display:flex;justify-content:center;align-items:center;cursor:pointer;border:var(--checkbox-border-width) solid var(--border-color-primary);border-radius:var(--radius-full);background:var(--background-fill-primary);padding:var(--size-0-5);width:16px;height:16px;flex-shrink:0}.secondary-wrap.svelte-1scun43.svelte-1scun43.svelte-1scun43{display:flex;flex:1 1 0%;align-items:center;border:none;min-width:min-content}input.svelte-1scun43.svelte-1scun43.svelte-1scun43{margin:var(--spacing-sm);outline:none;border:none;background:inherit;width:var(--size-full);color:var(--body-text-color);font-size:var(--input-text-size)}input.svelte-1scun43.svelte-1scun43.svelte-1scun43:disabled{-webkit-text-fill-color:var(--body-text-color);-webkit-opacity:1;opacity:1;cursor:not-allowed}.remove-all.svelte-1scun43.svelte-1scun43.svelte-1scun43{margin-left:var(--size-1);width:20px;height:20px}.subdued.svelte-1scun43.svelte-1scun43.svelte-1scun43{color:var(--body-text-color-subdued)}input[readonly].svelte-1scun43.svelte-1scun43.svelte-1scun43{cursor:pointer}`,map:'{"version":3,"file":"Multiselect.svelte","sources":["Multiselect.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { afterUpdate, createEventDispatcher } from \\"svelte\\";\\nimport { _ } from \\"svelte-i18n\\";\\nimport { BlockTitle } from \\"@gradio/atoms\\";\\nimport { Remove, DropdownArrow } from \\"@gradio/icons\\";\\nimport DropdownOptions from \\"./DropdownOptions.svelte\\";\\nimport { handle_filter, handle_change, handle_shared_keys } from \\"./utils\\";\\nexport let label;\\nexport let info = void 0;\\nexport let value = [];\\nlet old_value = [];\\nexport let value_is_output = false;\\nexport let max_choices = null;\\nexport let choices;\\nlet old_choices;\\nexport let disabled = false;\\nexport let show_label;\\nexport let container = true;\\nexport let allow_custom_value = false;\\nexport let filterable = true;\\nexport let i18n;\\nlet filter_input;\\nlet input_text = \\"\\";\\nlet old_input_text = \\"\\";\\nlet show_options = false;\\nlet choices_names;\\nlet choices_values;\\nlet filtered_indices = [];\\nlet active_index = null;\\nlet selected_indices = [];\\nlet old_selected_index = [];\\nconst dispatch = createEventDispatcher();\\nif (Array.isArray(value)) {\\n    value.forEach((element) => {\\n        const index = choices.map((c) => c[1]).indexOf(element);\\n        if (index !== -1) {\\n            selected_indices.push(index);\\n        }\\n        else {\\n            selected_indices.push(element);\\n        }\\n    });\\n}\\n$: {\\n    choices_names = choices.map((c) => c[0]);\\n    choices_values = choices.map((c) => c[1]);\\n}\\n$: {\\n    if (choices !== old_choices || input_text !== old_input_text) {\\n        filtered_indices = handle_filter(choices, input_text);\\n        old_choices = choices;\\n        old_input_text = input_text;\\n        if (!allow_custom_value) {\\n            active_index = filtered_indices[0];\\n        }\\n    }\\n}\\n$: {\\n    if (JSON.stringify(value) != JSON.stringify(old_value)) {\\n        handle_change(dispatch, value, value_is_output);\\n        old_value = Array.isArray(value) ? value.slice() : value;\\n    }\\n}\\n$: {\\n    if (JSON.stringify(selected_indices) != JSON.stringify(old_selected_index)) {\\n        value = selected_indices.map((index) => typeof index === \\"number\\" ? choices_values[index] : index);\\n        old_selected_index = selected_indices.slice();\\n    }\\n}\\nfunction handle_blur() {\\n    if (!allow_custom_value) {\\n        input_text = \\"\\";\\n    }\\n    if (allow_custom_value && input_text !== \\"\\") {\\n        add_selected_choice(input_text);\\n        input_text = \\"\\";\\n    }\\n    show_options = false;\\n    active_index = null;\\n    dispatch(\\"blur\\");\\n}\\nfunction remove_selected_choice(option_index) {\\n    selected_indices = selected_indices.filter((v) => v !== option_index);\\n    dispatch(\\"select\\", {\\n        index: typeof option_index === \\"number\\" ? option_index : -1,\\n        value: typeof option_index === \\"number\\" ? choices_values[option_index] : option_index,\\n        selected: false\\n    });\\n}\\nfunction add_selected_choice(option_index) {\\n    if (max_choices === null || selected_indices.length < max_choices) {\\n        selected_indices = [...selected_indices, option_index];\\n        dispatch(\\"select\\", {\\n            index: typeof option_index === \\"number\\" ? option_index : -1,\\n            value: typeof option_index === \\"number\\" ? choices_values[option_index] : option_index,\\n            selected: true\\n        });\\n    }\\n    if (selected_indices.length === max_choices) {\\n        show_options = false;\\n        active_index = null;\\n        filter_input.blur();\\n    }\\n}\\nfunction handle_option_selected(e) {\\n    const option_index = parseInt(e.detail.target.dataset.index);\\n    add_or_remove_index(option_index);\\n}\\nfunction add_or_remove_index(option_index) {\\n    if (selected_indices.includes(option_index)) {\\n        remove_selected_choice(option_index);\\n    }\\n    else {\\n        add_selected_choice(option_index);\\n    }\\n    input_text = \\"\\";\\n}\\nfunction remove_all(e) {\\n    selected_indices = [];\\n    input_text = \\"\\";\\n    e.preventDefault();\\n}\\nfunction handle_focus(e) {\\n    filtered_indices = choices.map((_2, i) => i);\\n    if (max_choices === null || selected_indices.length < max_choices) {\\n        show_options = true;\\n    }\\n    dispatch(\\"focus\\");\\n}\\nfunction handle_key_down(e) {\\n    [show_options, active_index] = handle_shared_keys(e, active_index, filtered_indices);\\n    if (e.key === \\"Enter\\") {\\n        if (active_index !== null) {\\n            add_or_remove_index(active_index);\\n        }\\n        else {\\n            if (allow_custom_value) {\\n                add_selected_choice(input_text);\\n                input_text = \\"\\";\\n            }\\n        }\\n    }\\n    if (e.key === \\"Backspace\\" && input_text === \\"\\") {\\n        selected_indices = [...selected_indices.slice(0, -1)];\\n    }\\n    if (selected_indices.length === max_choices) {\\n        show_options = false;\\n        active_index = null;\\n    }\\n}\\nfunction set_selected_indices() {\\n    if (value === void 0) {\\n        selected_indices = [];\\n    }\\n    else if (Array.isArray(value)) {\\n        selected_indices = value.map((v) => {\\n            const index = choices_values.indexOf(v);\\n            if (index !== -1) {\\n                return index;\\n            }\\n            if (allow_custom_value) {\\n                return v;\\n            }\\n            return void 0;\\n        }).filter((val) => val !== void 0);\\n    }\\n}\\n$: value, set_selected_indices();\\nafterUpdate(() => {\\n    value_is_output = false;\\n});\\n<\/script>\\n\\n<label class:container>\\n\\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\\n\\n\\t<div class=\\"wrap\\">\\n\\t\\t<div class=\\"wrap-inner\\" class:show_options>\\n\\t\\t\\t{#each selected_indices as s}\\n\\t\\t\\t\\t<div class=\\"token\\">\\n\\t\\t\\t\\t\\t<span>\\n\\t\\t\\t\\t\\t\\t{#if typeof s === \\"number\\"}\\n\\t\\t\\t\\t\\t\\t\\t{choices_names[s]}\\n\\t\\t\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t\\t\\t{s}\\n\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t</span>\\n\\t\\t\\t\\t\\t{#if !disabled}\\n\\t\\t\\t\\t\\t\\t<div\\n\\t\\t\\t\\t\\t\\t\\tclass=\\"token-remove\\"\\n\\t\\t\\t\\t\\t\\t\\ton:click|preventDefault={() => remove_selected_choice(s)}\\n\\t\\t\\t\\t\\t\\t\\ton:keydown|preventDefault={(event) => {\\n\\t\\t\\t\\t\\t\\t\\t\\tif (event.key === \\"Enter\\") {\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tremove_selected_choice(s);\\n\\t\\t\\t\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\t\\t\\t}}\\n\\t\\t\\t\\t\\t\\t\\trole=\\"button\\"\\n\\t\\t\\t\\t\\t\\t\\ttabindex=\\"0\\"\\n\\t\\t\\t\\t\\t\\t\\ttitle={i18n(\\"common.remove\\") + \\" \\" + s}\\n\\t\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t\\t<Remove />\\n\\t\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t{/each}\\n\\t\\t\\t<div class=\\"secondary-wrap\\">\\n\\t\\t\\t\\t<input\\n\\t\\t\\t\\t\\tclass=\\"border-none\\"\\n\\t\\t\\t\\t\\tclass:subdued={(!choices_names.includes(input_text) &&\\n\\t\\t\\t\\t\\t\\t!allow_custom_value) ||\\n\\t\\t\\t\\t\\t\\tselected_indices.length === max_choices}\\n\\t\\t\\t\\t\\t{disabled}\\n\\t\\t\\t\\t\\tautocomplete=\\"off\\"\\n\\t\\t\\t\\t\\tbind:value={input_text}\\n\\t\\t\\t\\t\\tbind:this={filter_input}\\n\\t\\t\\t\\t\\ton:keydown={handle_key_down}\\n\\t\\t\\t\\t\\ton:keyup={(e) =>\\n\\t\\t\\t\\t\\t\\tdispatch(\\"key_up\\", {\\n\\t\\t\\t\\t\\t\\t\\tkey: e.key,\\n\\t\\t\\t\\t\\t\\t\\tinput_value: input_text\\n\\t\\t\\t\\t\\t\\t})}\\n\\t\\t\\t\\t\\ton:blur={handle_blur}\\n\\t\\t\\t\\t\\ton:focus={handle_focus}\\n\\t\\t\\t\\t\\treadonly={!filterable}\\n\\t\\t\\t\\t/>\\n\\n\\t\\t\\t\\t{#if !disabled}\\n\\t\\t\\t\\t\\t{#if selected_indices.length > 0}\\n\\t\\t\\t\\t\\t\\t<div\\n\\t\\t\\t\\t\\t\\t\\trole=\\"button\\"\\n\\t\\t\\t\\t\\t\\t\\ttabindex=\\"0\\"\\n\\t\\t\\t\\t\\t\\t\\tclass=\\"token-remove remove-all\\"\\n\\t\\t\\t\\t\\t\\t\\ttitle={i18n(\\"common.clear\\")}\\n\\t\\t\\t\\t\\t\\t\\ton:click={remove_all}\\n\\t\\t\\t\\t\\t\\t\\ton:keydown={(event) => {\\n\\t\\t\\t\\t\\t\\t\\t\\tif (event.key === \\"Enter\\") {\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tremove_all(event);\\n\\t\\t\\t\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\t\\t\\t}}\\n\\t\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t\\t<Remove />\\n\\t\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t<span class=\\"icon-wrap\\"> <DropdownArrow /></span>\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t</div>\\n\\t\\t</div>\\n\\t\\t<DropdownOptions\\n\\t\\t\\t{show_options}\\n\\t\\t\\t{choices}\\n\\t\\t\\t{filtered_indices}\\n\\t\\t\\t{disabled}\\n\\t\\t\\t{selected_indices}\\n\\t\\t\\t{active_index}\\n\\t\\t\\tremember_scroll={true}\\n\\t\\t\\ton:change={handle_option_selected}\\n\\t\\t/>\\n\\t</div>\\n</label>\\n\\n<style>\\n\\t.icon-wrap {\\n\\t\\tcolor: var(--body-text-color);\\n\\t\\tmargin-right: var(--size-2);\\n\\t\\twidth: var(--size-5);\\n\\t}\\n\\tlabel:not(.container),\\n\\tlabel:not(.container) .wrap,\\n\\tlabel:not(.container) .wrap-inner,\\n\\tlabel:not(.container) .secondary-wrap,\\n\\tlabel:not(.container) .token,\\n\\tlabel:not(.container) input {\\n\\t\\theight: 100%;\\n\\t}\\n\\t.container .wrap {\\n\\t\\tbox-shadow: var(--input-shadow);\\n\\t\\tborder: var(--input-border-width) solid var(--border-color-primary);\\n\\t}\\n\\n\\t.wrap {\\n\\t\\tposition: relative;\\n\\t\\tborder-radius: var(--input-radius);\\n\\t\\tbackground: var(--input-background-fill);\\n\\t}\\n\\n\\t.wrap:focus-within {\\n\\t\\tbox-shadow: var(--input-shadow-focus);\\n\\t\\tborder-color: var(--input-border-color-focus);\\n\\t}\\n\\n\\t.wrap-inner {\\n\\t\\tdisplay: flex;\\n\\t\\tposition: relative;\\n\\t\\tflex-wrap: wrap;\\n\\t\\talign-items: center;\\n\\t\\tgap: var(--checkbox-label-gap);\\n\\t\\tpadding: var(--checkbox-label-padding);\\n\\t}\\n\\n\\t.token {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\ttransition: var(--button-transition);\\n\\t\\tcursor: pointer;\\n\\t\\tbox-shadow: var(--checkbox-label-shadow);\\n\\t\\tborder: var(--checkbox-label-border-width) solid\\n\\t\\t\\tvar(--checkbox-label-border-color);\\n\\t\\tborder-radius: var(--button-small-radius);\\n\\t\\tbackground: var(--checkbox-label-background-fill);\\n\\t\\tpadding: var(--checkbox-label-padding);\\n\\t\\tcolor: var(--checkbox-label-text-color);\\n\\t\\tfont-weight: var(--checkbox-label-text-weight);\\n\\t\\tfont-size: var(--checkbox-label-text-size);\\n\\t\\tline-height: var(--line-md);\\n\\t\\tword-break: break-word;\\n\\t}\\n\\n\\t.token > * + * {\\n\\t\\tmargin-left: var(--size-2);\\n\\t}\\n\\n\\t.token-remove {\\n\\t\\tfill: var(--body-text-color);\\n\\t\\tdisplay: flex;\\n\\t\\tjustify-content: center;\\n\\t\\talign-items: center;\\n\\t\\tcursor: pointer;\\n\\t\\tborder: var(--checkbox-border-width) solid var(--border-color-primary);\\n\\t\\tborder-radius: var(--radius-full);\\n\\t\\tbackground: var(--background-fill-primary);\\n\\t\\tpadding: var(--size-0-5);\\n\\t\\twidth: 16px;\\n\\t\\theight: 16px;\\n\\t\\tflex-shrink: 0;\\n\\t}\\n\\n\\t.secondary-wrap {\\n\\t\\tdisplay: flex;\\n\\t\\tflex: 1 1 0%;\\n\\t\\talign-items: center;\\n\\t\\tborder: none;\\n\\t\\tmin-width: min-content;\\n\\t}\\n\\n\\tinput {\\n\\t\\tmargin: var(--spacing-sm);\\n\\t\\toutline: none;\\n\\t\\tborder: none;\\n\\t\\tbackground: inherit;\\n\\t\\twidth: var(--size-full);\\n\\t\\tcolor: var(--body-text-color);\\n\\t\\tfont-size: var(--input-text-size);\\n\\t}\\n\\n\\tinput:disabled {\\n\\t\\t-webkit-text-fill-color: var(--body-text-color);\\n\\t\\t-webkit-opacity: 1;\\n\\t\\topacity: 1;\\n\\t\\tcursor: not-allowed;\\n\\t}\\n\\n\\t.remove-all {\\n\\t\\tmargin-left: var(--size-1);\\n\\t\\twidth: 20px;\\n\\t\\theight: 20px;\\n\\t}\\n\\t.subdued {\\n\\t\\tcolor: var(--body-text-color-subdued);\\n\\t}\\n\\tinput[readonly] {\\n\\t\\tcursor: pointer;\\n\\t}</style>\\n"],"names":[],"mappings":"AAoQC,uDAAW,CACV,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,YAAY,CAAE,IAAI,QAAQ,CAAC,CAC3B,KAAK,CAAE,IAAI,QAAQ,CACpB,CACA,kDAAK,KAAK,UAAU,CAAC,CACrB,oBAAK,KAAK,UAAU,CAAC,CAAC,mCAAK,CAC3B,oBAAK,KAAK,UAAU,CAAC,CAAC,yCAAW,CACjC,oBAAK,KAAK,UAAU,CAAC,CAAC,6CAAe,CACrC,oBAAK,KAAK,UAAU,CAAC,CAAC,oCAAM,CAC5B,oBAAK,KAAK,UAAU,CAAC,CAAC,mCAAM,CAC3B,MAAM,CAAE,IACT,CACA,yBAAU,CAAC,mCAAM,CAChB,UAAU,CAAE,IAAI,cAAc,CAAC,CAC/B,MAAM,CAAE,IAAI,oBAAoB,CAAC,CAAC,KAAK,CAAC,IAAI,sBAAsB,CACnE,CAEA,kDAAM,CACL,QAAQ,CAAE,QAAQ,CAClB,aAAa,CAAE,IAAI,cAAc,CAAC,CAClC,UAAU,CAAE,IAAI,uBAAuB,CACxC,CAEA,kDAAK,aAAc,CAClB,UAAU,CAAE,IAAI,oBAAoB,CAAC,CACrC,YAAY,CAAE,IAAI,0BAA0B,CAC7C,CAEA,wDAAY,CACX,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,SAAS,CAAE,IAAI,CACf,WAAW,CAAE,MAAM,CACnB,GAAG,CAAE,IAAI,oBAAoB,CAAC,CAC9B,OAAO,CAAE,IAAI,wBAAwB,CACtC,CAEA,mDAAO,CACN,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,UAAU,CAAE,IAAI,mBAAmB,CAAC,CACpC,MAAM,CAAE,OAAO,CACf,UAAU,CAAE,IAAI,uBAAuB,CAAC,CACxC,MAAM,CAAE,IAAI,6BAA6B,CAAC,CAAC,KAAK;AAClD,GAAG,IAAI,6BAA6B,CAAC,CACnC,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,UAAU,CAAE,IAAI,gCAAgC,CAAC,CACjD,OAAO,CAAE,IAAI,wBAAwB,CAAC,CACtC,KAAK,CAAE,IAAI,2BAA2B,CAAC,CACvC,WAAW,CAAE,IAAI,4BAA4B,CAAC,CAC9C,SAAS,CAAE,IAAI,0BAA0B,CAAC,CAC1C,WAAW,CAAE,IAAI,SAAS,CAAC,CAC3B,UAAU,CAAE,UACb,CAEA,qBAAM,CAAG,eAAC,CAAG,eAAE,CACd,WAAW,CAAE,IAAI,QAAQ,CAC1B,CAEA,0DAAc,CACb,IAAI,CAAE,IAAI,iBAAiB,CAAC,CAC5B,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,OAAO,CACf,MAAM,CAAE,IAAI,uBAAuB,CAAC,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CACtE,aAAa,CAAE,IAAI,aAAa,CAAC,CACjC,UAAU,CAAE,IAAI,yBAAyB,CAAC,CAC1C,OAAO,CAAE,IAAI,UAAU,CAAC,CACxB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,WAAW,CAAE,CACd,CAEA,4DAAgB,CACf,OAAO,CAAE,IAAI,CACb,IAAI,CAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CACZ,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,IAAI,CACZ,SAAS,CAAE,WACZ,CAEA,kDAAM,CACL,MAAM,CAAE,IAAI,YAAY,CAAC,CACzB,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,OAAO,CACnB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,SAAS,CAAE,IAAI,iBAAiB,CACjC,CAEA,kDAAK,SAAU,CACd,uBAAuB,CAAE,IAAI,iBAAiB,CAAC,CAC/C,eAAe,CAAE,CAAC,CAClB,OAAO,CAAE,CAAC,CACV,MAAM,CAAE,WACT,CAEA,wDAAY,CACX,WAAW,CAAE,IAAI,QAAQ,CAAC,CAC1B,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CACA,qDAAS,CACR,KAAK,CAAE,IAAI,yBAAyB,CACrC,CACA,KAAK,CAAC,QAAQ,8CAAE,CACf,MAAM,CAAE,OACT"}'},P=create_ssr_component((l,t,e,M)=>{let{label:d}=t,{info:m=void 0}=t,{value:n=[]}=t,b=[],{value_is_output:_=!1}=t,{max_choices:i=null}=t,{choices:c}=t,u,{disabled:f=!1}=t,{show_label:w}=t,{container:C=!0}=t,{allow_custom_value:p=!1}=t,{filterable:y=!0}=t,{i18n:x}=t,E,h="",a="",I=!1,s,v,O=[],r=null,o=[],K=[];const D=createEventDispatcher();Array.isArray(n)&&n.forEach(A=>{const g=c.map(L=>L[1]).indexOf(A);g!==-1?o.push(g):o.push(A);});function Q(){n===void 0?o=[]:Array.isArray(n)&&(o=n.map(A=>{const g=v.indexOf(A);if(g!==-1)return g;if(p)return A}).filter(A=>A!==void 0));}return t.label===void 0&&e.label&&d!==void 0&&e.label(d),t.info===void 0&&e.info&&m!==void 0&&e.info(m),t.value===void 0&&e.value&&n!==void 0&&e.value(n),t.value_is_output===void 0&&e.value_is_output&&_!==void 0&&e.value_is_output(_),t.max_choices===void 0&&e.max_choices&&i!==void 0&&e.max_choices(i),t.choices===void 0&&e.choices&&c!==void 0&&e.choices(c),t.disabled===void 0&&e.disabled&&f!==void 0&&e.disabled(f),t.show_label===void 0&&e.show_label&&w!==void 0&&e.show_label(w),t.container===void 0&&e.container&&C!==void 0&&e.container(C),t.allow_custom_value===void 0&&e.allow_custom_value&&p!==void 0&&e.allow_custom_value(p),t.filterable===void 0&&e.filterable&&y!==void 0&&e.filterable(y),t.i18n===void 0&&e.i18n&&x!==void 0&&e.i18n(x),l.css.add(V),s=c.map(A=>A[0]),v=c.map(A=>A[1]),(c!==u||h!==a)&&(O=S(c,h),u=c,a=h,p||(r=O[0])),JSON.stringify(o)!=JSON.stringify(K)&&(n=o.map(A=>typeof A=="number"?v[A]:A),K=o.slice()),JSON.stringify(n)!=JSON.stringify(b)&&(J(D,n,_),b=Array.isArray(n)?n.slice():n),Q(),`<label class="${["svelte-1scun43",C?"container":""].join(" ").trim()}">${validate_component(xt,"BlockTitle").$$render(l,{show_label:w,info:m},{},{default:()=>`${escape(d)}`})} <div class="wrap svelte-1scun43"><div class="${["wrap-inner svelte-1scun43",""].join(" ").trim()}">${each(o,A=>`<div class="token svelte-1scun43"><span class="svelte-1scun43">${typeof A=="number"?`${escape(s[A])}`:`${escape(A)}`}</span> ${f?"":`<div class="token-remove svelte-1scun43" role="button" tabindex="0"${add_attribute("title",x("common.remove")+" "+A,0)}>${validate_component(ae,"Remove").$$render(l,{},{},{})} </div>`} </div>`)} <div class="secondary-wrap svelte-1scun43"><input class="${["border-none svelte-1scun43",!s.includes(h)&&!p||o.length===i?"subdued":""].join(" ").trim()}" ${f?"disabled":""} autocomplete="off" ${y?"":"readonly"}${add_attribute("value",h,0)}${add_attribute("this",E,0)}> ${f?"":`${o.length>0?`<div role="button" tabindex="0" class="token-remove remove-all svelte-1scun43"${add_attribute("title",x("common.clear"),0)}>${validate_component(ae,"Remove").$$render(l,{},{},{})}</div>`:""} <span class="icon-wrap svelte-1scun43">${validate_component(Tt,"DropdownArrow").$$render(l,{},{},{})}</span>`}</div></div> ${validate_component(G,"DropdownOptions").$$render(l,{show_options:I,choices:c,filtered_indices:O,disabled:f,selected_indices:o,active_index:r,remember_scroll:!0},{},{})}</div> </label>`}),F={code:".icon-wrap.svelte-1hfxrpf.svelte-1hfxrpf{position:absolute;top:50%;transform:translateY(-50%);right:var(--size-5);color:var(--body-text-color);width:var(--size-5);pointer-events:none}.container.svelte-1hfxrpf.svelte-1hfxrpf{height:100%}.container.svelte-1hfxrpf .wrap.svelte-1hfxrpf{box-shadow:var(--input-shadow);border:var(--input-border-width) solid var(--border-color-primary)}.wrap.svelte-1hfxrpf.svelte-1hfxrpf{position:relative;border-radius:var(--input-radius);background:var(--input-background-fill)}.wrap.svelte-1hfxrpf.svelte-1hfxrpf:focus-within{box-shadow:var(--input-shadow-focus);border-color:var(--input-border-color-focus);background:var(--input-background-fill-focus)}.wrap-inner.svelte-1hfxrpf.svelte-1hfxrpf{display:flex;position:relative;flex-wrap:wrap;align-items:center;gap:var(--checkbox-label-gap);padding:var(--checkbox-label-padding);height:100%}.secondary-wrap.svelte-1hfxrpf.svelte-1hfxrpf{display:flex;flex:1 1 0%;align-items:center;border:none;min-width:min-content;height:100%}input.svelte-1hfxrpf.svelte-1hfxrpf{margin:var(--spacing-sm);outline:none;border:none;background:inherit;width:var(--size-full);color:var(--body-text-color);font-size:var(--input-text-size);height:100%}input.svelte-1hfxrpf.svelte-1hfxrpf:disabled{-webkit-text-fill-color:var(--body-text-color);-webkit-opacity:1;opacity:1;cursor:not-allowed}.subdued.svelte-1hfxrpf.svelte-1hfxrpf{color:var(--body-text-color-subdued)}input[readonly].svelte-1hfxrpf.svelte-1hfxrpf{cursor:pointer}",map:'{"version":3,"file":"Dropdown.svelte","sources":["Dropdown.svelte"],"sourcesContent":["<script lang=\\"ts\\">import DropdownOptions from \\"./DropdownOptions.svelte\\";\\nimport { createEventDispatcher, afterUpdate } from \\"svelte\\";\\nimport { BlockTitle } from \\"@gradio/atoms\\";\\nimport { DropdownArrow } from \\"@gradio/icons\\";\\nimport { handle_filter, handle_change, handle_shared_keys } from \\"./utils\\";\\nexport let label;\\nexport let info = void 0;\\nexport let value = void 0;\\nlet old_value = void 0;\\nexport let value_is_output = false;\\nexport let choices;\\nlet old_choices;\\nexport let disabled = false;\\nexport let show_label;\\nexport let container = true;\\nexport let allow_custom_value = false;\\nexport let filterable = true;\\nlet filter_input;\\nlet show_options = false;\\nlet choices_names;\\nlet choices_values;\\nlet input_text = \\"\\";\\nlet old_input_text = \\"\\";\\nlet initialized = false;\\nlet filtered_indices = [];\\nlet active_index = null;\\nlet selected_index = null;\\nlet old_selected_index;\\nconst dispatch = createEventDispatcher();\\nif (value) {\\n    old_selected_index = choices.map((c) => c[1]).indexOf(value);\\n    selected_index = old_selected_index;\\n    if (selected_index === -1) {\\n        old_value = value;\\n        selected_index = null;\\n    }\\n    else {\\n        [input_text, old_value] = choices[selected_index];\\n        old_input_text = input_text;\\n    }\\n    set_input_text();\\n}\\n$: {\\n    if (selected_index !== old_selected_index && selected_index !== null && initialized) {\\n        [input_text, value] = choices[selected_index];\\n        old_selected_index = selected_index;\\n        dispatch(\\"select\\", {\\n            index: selected_index,\\n            value: choices_values[selected_index],\\n            selected: true\\n        });\\n    }\\n}\\n$: if (JSON.stringify(old_value) !== JSON.stringify(value)) {\\n    set_input_text();\\n    handle_change(dispatch, value, value_is_output);\\n    old_value = value;\\n}\\nfunction set_choice_names_values() {\\n    choices_names = choices.map((c) => c[0]);\\n    choices_values = choices.map((c) => c[1]);\\n}\\n$: choices, set_choice_names_values();\\nconst is_browser = typeof window !== \\"undefined\\";\\n$: {\\n    if (choices !== old_choices) {\\n        if (!allow_custom_value) {\\n            set_input_text();\\n        }\\n        old_choices = choices;\\n        filtered_indices = handle_filter(choices, input_text);\\n        if (!allow_custom_value && filtered_indices.length > 0) {\\n            active_index = filtered_indices[0];\\n        }\\n        if (is_browser && filter_input === document.activeElement) {\\n            show_options = true;\\n        }\\n    }\\n}\\n$: {\\n    if (input_text !== old_input_text) {\\n        filtered_indices = handle_filter(choices, input_text);\\n        old_input_text = input_text;\\n        if (!allow_custom_value && filtered_indices.length > 0) {\\n            active_index = filtered_indices[0];\\n        }\\n    }\\n}\\nfunction set_input_text() {\\n    set_choice_names_values();\\n    if (value === void 0 || Array.isArray(value) && value.length === 0) {\\n        input_text = \\"\\";\\n        selected_index = null;\\n    }\\n    else if (choices_values.includes(value)) {\\n        input_text = choices_names[choices_values.indexOf(value)];\\n        selected_index = choices_values.indexOf(value);\\n    }\\n    else if (allow_custom_value) {\\n        input_text = value;\\n        selected_index = null;\\n    }\\n    else {\\n        input_text = \\"\\";\\n        selected_index = null;\\n    }\\n    old_selected_index = selected_index;\\n}\\nfunction handle_option_selected(e) {\\n    selected_index = parseInt(e.detail.target.dataset.index);\\n    if (isNaN(selected_index)) {\\n        selected_index = null;\\n        return;\\n    }\\n    show_options = false;\\n    active_index = null;\\n    filter_input.blur();\\n}\\nfunction handle_focus(e) {\\n    filtered_indices = choices.map((_, i) => i);\\n    show_options = true;\\n    dispatch(\\"focus\\");\\n}\\nfunction handle_blur() {\\n    if (!allow_custom_value) {\\n        input_text = choices_names[choices_values.indexOf(value)];\\n    }\\n    else {\\n        value = input_text;\\n    }\\n    show_options = false;\\n    active_index = null;\\n    dispatch(\\"blur\\");\\n}\\nfunction handle_key_down(e) {\\n    [show_options, active_index] = handle_shared_keys(e, active_index, filtered_indices);\\n    if (e.key === \\"Enter\\") {\\n        if (active_index !== null) {\\n            selected_index = active_index;\\n            show_options = false;\\n            filter_input.blur();\\n            active_index = null;\\n        }\\n        else if (choices_names.includes(input_text)) {\\n            selected_index = choices_names.indexOf(input_text);\\n            show_options = false;\\n            active_index = null;\\n            filter_input.blur();\\n        }\\n        else if (allow_custom_value) {\\n            value = input_text;\\n            selected_index = null;\\n            show_options = false;\\n            active_index = null;\\n            filter_input.blur();\\n        }\\n    }\\n}\\nafterUpdate(() => {\\n    value_is_output = false;\\n    initialized = true;\\n});\\n<\/script>\\n\\n<div class:container>\\n\\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\\n\\n\\t<div class=\\"wrap\\">\\n\\t\\t<div class=\\"wrap-inner\\" class:show_options>\\n\\t\\t\\t<div class=\\"secondary-wrap\\">\\n\\t\\t\\t\\t<input\\n\\t\\t\\t\\t\\trole=\\"listbox\\"\\n\\t\\t\\t\\t\\taria-controls=\\"dropdown-options\\"\\n\\t\\t\\t\\t\\taria-expanded={show_options}\\n\\t\\t\\t\\t\\taria-label={label}\\n\\t\\t\\t\\t\\tclass=\\"border-none\\"\\n\\t\\t\\t\\t\\tclass:subdued={!choices_names.includes(input_text) &&\\n\\t\\t\\t\\t\\t\\t!allow_custom_value}\\n\\t\\t\\t\\t\\t{disabled}\\n\\t\\t\\t\\t\\tautocomplete=\\"off\\"\\n\\t\\t\\t\\t\\tbind:value={input_text}\\n\\t\\t\\t\\t\\tbind:this={filter_input}\\n\\t\\t\\t\\t\\ton:keydown={handle_key_down}\\n\\t\\t\\t\\t\\ton:keyup={(e) =>\\n\\t\\t\\t\\t\\t\\tdispatch(\\"key_up\\", {\\n\\t\\t\\t\\t\\t\\t\\tkey: e.key,\\n\\t\\t\\t\\t\\t\\t\\tinput_value: input_text\\n\\t\\t\\t\\t\\t\\t})}\\n\\t\\t\\t\\t\\ton:blur={handle_blur}\\n\\t\\t\\t\\t\\ton:focus={handle_focus}\\n\\t\\t\\t\\t\\treadonly={!filterable}\\n\\t\\t\\t\\t/>\\n\\t\\t\\t\\t{#if !disabled}\\n\\t\\t\\t\\t\\t<div class=\\"icon-wrap\\">\\n\\t\\t\\t\\t\\t\\t<DropdownArrow />\\n\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t</div>\\n\\t\\t</div>\\n\\t\\t<DropdownOptions\\n\\t\\t\\t{show_options}\\n\\t\\t\\t{choices}\\n\\t\\t\\t{filtered_indices}\\n\\t\\t\\t{disabled}\\n\\t\\t\\tselected_indices={selected_index === null ? [] : [selected_index]}\\n\\t\\t\\t{active_index}\\n\\t\\t\\ton:change={handle_option_selected}\\n\\t\\t/>\\n\\t</div>\\n</div>\\n\\n<style>\\n\\t.icon-wrap {\\n\\t\\tposition: absolute;\\n\\t\\ttop: 50%;\\n\\t\\ttransform: translateY(-50%);\\n\\t\\tright: var(--size-5);\\n\\t\\tcolor: var(--body-text-color);\\n\\t\\twidth: var(--size-5);\\n\\t\\tpointer-events: none;\\n\\t}\\n\\t.container {\\n\\t\\theight: 100%;\\n\\t}\\n\\t.container .wrap {\\n\\t\\tbox-shadow: var(--input-shadow);\\n\\t\\tborder: var(--input-border-width) solid var(--border-color-primary);\\n\\t}\\n\\n\\t.wrap {\\n\\t\\tposition: relative;\\n\\t\\tborder-radius: var(--input-radius);\\n\\t\\tbackground: var(--input-background-fill);\\n\\t}\\n\\n\\t.wrap:focus-within {\\n\\t\\tbox-shadow: var(--input-shadow-focus);\\n\\t\\tborder-color: var(--input-border-color-focus);\\n\\t\\tbackground: var(--input-background-fill-focus);\\n\\t}\\n\\n\\t.wrap-inner {\\n\\t\\tdisplay: flex;\\n\\t\\tposition: relative;\\n\\t\\tflex-wrap: wrap;\\n\\t\\talign-items: center;\\n\\t\\tgap: var(--checkbox-label-gap);\\n\\t\\tpadding: var(--checkbox-label-padding);\\n\\t\\theight: 100%;\\n\\t}\\n\\t.secondary-wrap {\\n\\t\\tdisplay: flex;\\n\\t\\tflex: 1 1 0%;\\n\\t\\talign-items: center;\\n\\t\\tborder: none;\\n\\t\\tmin-width: min-content;\\n\\t\\theight: 100%;\\n\\t}\\n\\n\\tinput {\\n\\t\\tmargin: var(--spacing-sm);\\n\\t\\toutline: none;\\n\\t\\tborder: none;\\n\\t\\tbackground: inherit;\\n\\t\\twidth: var(--size-full);\\n\\t\\tcolor: var(--body-text-color);\\n\\t\\tfont-size: var(--input-text-size);\\n\\t\\theight: 100%;\\n\\t}\\n\\n\\tinput:disabled {\\n\\t\\t-webkit-text-fill-color: var(--body-text-color);\\n\\t\\t-webkit-opacity: 1;\\n\\t\\topacity: 1;\\n\\t\\tcursor: not-allowed;\\n\\t}\\n\\n\\t.subdued {\\n\\t\\tcolor: var(--body-text-color-subdued);\\n\\t}\\n\\n\\tinput[readonly] {\\n\\t\\tcursor: pointer;\\n\\t}</style>\\n"],"names":[],"mappings":"AAoNC,wCAAW,CACV,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,GAAG,CACR,SAAS,CAAE,WAAW,IAAI,CAAC,CAC3B,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,cAAc,CAAE,IACjB,CACA,wCAAW,CACV,MAAM,CAAE,IACT,CACA,yBAAU,CAAC,oBAAM,CAChB,UAAU,CAAE,IAAI,cAAc,CAAC,CAC/B,MAAM,CAAE,IAAI,oBAAoB,CAAC,CAAC,KAAK,CAAC,IAAI,sBAAsB,CACnE,CAEA,mCAAM,CACL,QAAQ,CAAE,QAAQ,CAClB,aAAa,CAAE,IAAI,cAAc,CAAC,CAClC,UAAU,CAAE,IAAI,uBAAuB,CACxC,CAEA,mCAAK,aAAc,CAClB,UAAU,CAAE,IAAI,oBAAoB,CAAC,CACrC,YAAY,CAAE,IAAI,0BAA0B,CAAC,CAC7C,UAAU,CAAE,IAAI,6BAA6B,CAC9C,CAEA,yCAAY,CACX,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,SAAS,CAAE,IAAI,CACf,WAAW,CAAE,MAAM,CACnB,GAAG,CAAE,IAAI,oBAAoB,CAAC,CAC9B,OAAO,CAAE,IAAI,wBAAwB,CAAC,CACtC,MAAM,CAAE,IACT,CACA,6CAAgB,CACf,OAAO,CAAE,IAAI,CACb,IAAI,CAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CACZ,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,IAAI,CACZ,SAAS,CAAE,WAAW,CACtB,MAAM,CAAE,IACT,CAEA,mCAAM,CACL,MAAM,CAAE,IAAI,YAAY,CAAC,CACzB,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,OAAO,CACnB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,SAAS,CAAE,IAAI,iBAAiB,CAAC,CACjC,MAAM,CAAE,IACT,CAEA,mCAAK,SAAU,CACd,uBAAuB,CAAE,IAAI,iBAAiB,CAAC,CAC/C,eAAe,CAAE,CAAC,CAClB,OAAO,CAAE,CAAC,CACV,MAAM,CAAE,WACT,CAEA,sCAAS,CACR,KAAK,CAAE,IAAI,yBAAyB,CACrC,CAEA,KAAK,CAAC,QAAQ,+BAAE,CACf,MAAM,CAAE,OACT"}'},$=create_ssr_component((l,t,e,M)=>{let{label:d}=t,{info:m=void 0}=t,{value:n=void 0}=t,b,{value_is_output:_=!1}=t,{choices:i}=t,c,{disabled:u=!1}=t,{show_label:f}=t,{container:w=!0}=t,{allow_custom_value:C=!1}=t,{filterable:p=!0}=t,y,x=!1,E,h,a="",I="",s=!1,v=[],O=null,r=null,o;const K=createEventDispatcher();n&&(o=i.map(g=>g[1]).indexOf(n),r=o,r===-1?(b=n,r=null):([a,b]=i[r],I=a),A());function D(){E=i.map(g=>g[0]),h=i.map(g=>g[1]);}const Q=typeof window<"u";function A(){D(),n===void 0||Array.isArray(n)&&n.length===0?(a="",r=null):h.includes(n)?(a=E[h.indexOf(n)],r=h.indexOf(n)):C?(a=n,r=null):(a="",r=null),o=r;}return t.label===void 0&&e.label&&d!==void 0&&e.label(d),t.info===void 0&&e.info&&m!==void 0&&e.info(m),t.value===void 0&&e.value&&n!==void 0&&e.value(n),t.value_is_output===void 0&&e.value_is_output&&_!==void 0&&e.value_is_output(_),t.choices===void 0&&e.choices&&i!==void 0&&e.choices(i),t.disabled===void 0&&e.disabled&&u!==void 0&&e.disabled(u),t.show_label===void 0&&e.show_label&&f!==void 0&&e.show_label(f),t.container===void 0&&e.container&&w!==void 0&&e.container(w),t.allow_custom_value===void 0&&e.allow_custom_value&&C!==void 0&&e.allow_custom_value(C),t.filterable===void 0&&e.filterable&&p!==void 0&&e.filterable(p),l.css.add(F),r!==o&&r!==null&&s&&([a,n]=i[r],o=r,K("select",{index:r,value:h[r],selected:!0})),JSON.stringify(b)!==JSON.stringify(n)&&(A(),J(K,n,_),b=n),D(),i!==c&&(C||A(),c=i,v=S(i,a),!C&&v.length>0&&(O=v[0]),Q&&y===document.activeElement&&(x=!0)),a!==I&&(v=S(i,a),I=a,!C&&v.length>0&&(O=v[0])),`<div class="${["svelte-1hfxrpf",w?"container":""].join(" ").trim()}">${validate_component(xt,"BlockTitle").$$render(l,{show_label:f,info:m},{},{default:()=>`${escape(d)}`})} <div class="wrap svelte-1hfxrpf"><div class="${["wrap-inner svelte-1hfxrpf",x?"show_options":""].join(" ").trim()}"><div class="secondary-wrap svelte-1hfxrpf"><input role="listbox" aria-controls="dropdown-options"${add_attribute("aria-expanded",x,0)}${add_attribute("aria-label",d,0)} class="${["border-none svelte-1hfxrpf",!E.includes(a)&&!C?"subdued":""].join(" ").trim()}" ${u?"disabled":""} autocomplete="off" ${p?"":"readonly"}${add_attribute("value",a,0)}${add_attribute("this",y,0)}> ${u?"":`<div class="icon-wrap svelte-1hfxrpf">${validate_component(Tt,"DropdownArrow").$$render(l,{},{},{})}</div>`}</div></div> ${validate_component(G,"DropdownOptions").$$render(l,{show_options:x,choices:i,filtered_indices:v,disabled:u,selected_indices:r===null?[]:[r],active_index:O},{},{})}</div> </div>`}),At=create_ssr_component((l,t,e,M)=>{let{label:d="Dropdown"}=t,{info:m=void 0}=t,{elem_id:n=""}=t,{elem_classes:b=[]}=t,{visible:_=!0}=t,{multiselect:i=!1}=t,{value:c=i?[]:void 0}=t,{value_is_output:u=!1}=t,{max_choices:f=null}=t,{choices:w}=t,{show_label:C}=t,{filterable:p}=t,{container:y=!0}=t,{scale:x=null}=t,{min_width:E=void 0}=t,{loading_status:h}=t,{allow_custom_value:a=!1}=t,{gradio:I}=t,{interactive:s}=t;t.label===void 0&&e.label&&d!==void 0&&e.label(d),t.info===void 0&&e.info&&m!==void 0&&e.info(m),t.elem_id===void 0&&e.elem_id&&n!==void 0&&e.elem_id(n),t.elem_classes===void 0&&e.elem_classes&&b!==void 0&&e.elem_classes(b),t.visible===void 0&&e.visible&&_!==void 0&&e.visible(_),t.multiselect===void 0&&e.multiselect&&i!==void 0&&e.multiselect(i),t.value===void 0&&e.value&&c!==void 0&&e.value(c),t.value_is_output===void 0&&e.value_is_output&&u!==void 0&&e.value_is_output(u),t.max_choices===void 0&&e.max_choices&&f!==void 0&&e.max_choices(f),t.choices===void 0&&e.choices&&w!==void 0&&e.choices(w),t.show_label===void 0&&e.show_label&&C!==void 0&&e.show_label(C),t.filterable===void 0&&e.filterable&&p!==void 0&&e.filterable(p),t.container===void 0&&e.container&&y!==void 0&&e.container(y),t.scale===void 0&&e.scale&&x!==void 0&&e.scale(x),t.min_width===void 0&&e.min_width&&E!==void 0&&e.min_width(E),t.loading_status===void 0&&e.loading_status&&h!==void 0&&e.loading_status(h),t.allow_custom_value===void 0&&e.allow_custom_value&&a!==void 0&&e.allow_custom_value(a),t.gradio===void 0&&e.gradio&&I!==void 0&&e.gradio(I),t.interactive===void 0&&e.interactive&&s!==void 0&&e.interactive(s);let v,O,r=l.head;do v=!0,l.head=r,O=`${validate_component(mt,"Block").$$render(l,{visible:_,elem_id:n,elem_classes:b,padding:y,allow_overflow:!1,scale:x,min_width:E},{},{default:()=>`${validate_component(zA,"StatusTracker").$$render(l,Object.assign({},{autoscroll:I.autoscroll},{i18n:I.i18n},h),{},{})} ${i?`${validate_component(P,"Multiselect").$$render(l,{choices:w,max_choices:f,label:d,info:m,show_label:C,allow_custom_value:a,filterable:p,container:y,i18n:I.i18n,disabled:!s,value:c,value_is_output:u},{value:o=>{c=o,v=!1;},value_is_output:o=>{u=o,v=!1;}},{})}`:`${validate_component($,"Dropdown").$$render(l,{choices:w,label:d,info:m,show_label:C,filterable:p,allow_custom_value:a,container:y,disabled:!s,value:c,value_is_output:u},{value:o=>{c=o,v=!1;},value_is_output:o=>{u=o,v=!1;}},{})}`}`})}`;while(!v);return O});

export { $ as BaseDropdown, P as BaseMultiselect, At as default };
//# sourceMappingURL=Index57-5yXJtQxS.js.map
