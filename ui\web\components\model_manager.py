"""
Web界面模型管理组件
"""

import gradio as gr
import os
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

from ....core.utils.logger import get_logger
from ....core.llm.model_manager import model_manager
from ....core.llm.model_downloader import model_downloader
from ....engine_2d.api import engine_2d

logger = get_logger(__name__)

class ModelManagerComponent:
    """模型管理组件"""
    
    def __init__(self):
        self.download_progress = {}
        
        # 预设模型列表
        self.preset_models = {
            "语言模型": {
                "Lucy-128k-4bit": {
                    "url": "https://huggingface.co/bartowski/Lucy-128k-GGUF/resolve/main/Lucy-128k-Q4_K_M.gguf",
                    "size": "4.7GB",
                    "description": "高质量对话模型，支持128k上下文"
                },
                "Qwen2.5-7B-4bit": {
                    "url": "https://huggingface.co/Qwen/Qwen2.5-7B-Instruct-GGUF/resolve/main/qwen2.5-7b-instruct-q4_k_m.gguf",
                    "size": "4.4GB",
                    "description": "阿里通义千问2.5，中文优化"
                },
                "Llama-3.1-8B-4bit": {
                    "url": "https://huggingface.co/bartowski/Meta-Llama-3.1-8B-Instruct-GGUF/resolve/main/Meta-Llama-3.1-8B-Instruct-Q4_K_M.gguf",
                    "size": "4.6GB",
                    "description": "Meta Llama 3.1，英文优秀"
                }
            },
            "图像模型": {
                "Flux-Dev-4bit": {
                    "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q4_K_S.gguf",
                    "size": "8.9GB",
                    "description": "FLUX.1开发版，高质量图像生成"
                },
                "SD-XL-Base-4bit": {
                    "url": "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0/resolve/main/sd_xl_base_1.0.safetensors",
                    "size": "6.9GB",
                    "description": "Stable Diffusion XL基础模型"
                }
            }
        }
    
    def create_interface(self) -> gr.Blocks:
        """创建模型管理界面"""
        with gr.Blocks() as interface:
            gr.HTML("<h2>🤖 模型管理</h2>")
            
            with gr.Tabs():
                # 语言模型标签页
                with gr.TabItem("语言模型"):
                    self.create_llm_tab()
                
                # 图像模型标签页
                with gr.TabItem("图像模型"):
                    self.create_image_tab()
                
                # 模型下载标签页
                with gr.TabItem("模型下载"):
                    self.create_download_tab()
        
        return interface
    
    def create_llm_tab(self):
        """创建语言模型标签页"""
        with gr.Row():
            with gr.Column(scale=2):
                gr.HTML("<h3>当前语言模型</h3>")
                
                current_llm_info = gr.Markdown(
                    value=self.get_current_llm_info(),
                    label="模型信息"
                )
                
                with gr.Row():
                    refresh_llm_btn = gr.Button("刷新", size="sm")
                    unload_llm_btn = gr.Button("卸载模型", variant="secondary", size="sm")
            
            with gr.Column(scale=3):
                gr.HTML("<h3>可用模型</h3>")
                
                llm_model_list = gr.Dropdown(
                    choices=self.get_available_llm_models(),
                    label="选择模型",
                    info="选择要加载的语言模型"
                )
                
                load_llm_btn = gr.Button("加载模型", variant="primary")
                
                llm_status = gr.Markdown(value="", label="状态")
        
        # 事件绑定
        refresh_llm_btn.click(
            fn=self.refresh_llm_models,
            outputs=[llm_model_list, current_llm_info]
        )
        
        load_llm_btn.click(
            fn=self.load_llm_model,
            inputs=[llm_model_list],
            outputs=[current_llm_info, llm_status]
        )
        
        unload_llm_btn.click(
            fn=self.unload_llm_model,
            outputs=[current_llm_info, llm_status]
        )
    
    def create_image_tab(self):
        """创建图像模型标签页"""
        with gr.Row():
            with gr.Column(scale=2):
                gr.HTML("<h3>当前图像模型</h3>")
                
                current_image_info = gr.Markdown(
                    value=self.get_current_image_info(),
                    label="模型信息"
                )
                
                with gr.Row():
                    refresh_image_btn = gr.Button("刷新", size="sm")
                    unload_image_btn = gr.Button("卸载模型", variant="secondary", size="sm")
            
            with gr.Column(scale=3):
                gr.HTML("<h3>可用模型</h3>")
                
                image_model_list = gr.Dropdown(
                    choices=self.get_available_image_models(),
                    label="选择模型",
                    info="选择要加载的图像模型"
                )
                
                load_image_btn = gr.Button("加载模型", variant="primary")
                
                image_status = gr.Markdown(value="", label="状态")
        
        # 事件绑定
        refresh_image_btn.click(
            fn=self.refresh_image_models,
            outputs=[image_model_list, current_image_info]
        )
        
        load_image_btn.click(
            fn=self.load_image_model,
            inputs=[image_model_list],
            outputs=[current_image_info, image_status]
        )
        
        unload_image_btn.click(
            fn=self.unload_image_model,
            outputs=[current_image_info, image_status]
        )
    
    def create_download_tab(self):
        """创建模型下载标签页"""
        gr.HTML("<h3>模型下载</h3>")
        
        with gr.Tabs():
            # 预设模型下载
            with gr.TabItem("预设模型"):
                for category, models in self.preset_models.items():
                    gr.HTML(f"<h4>{category}</h4>")
                    
                    for model_name, model_info in models.items():
                        with gr.Row():
                            with gr.Column(scale=3):
                                gr.Markdown(f"""
                                **{model_name}**
                                
                                {model_info['description']}
                                
                                大小: {model_info['size']}
                                """)
                            
                            with gr.Column(scale=1):
                                download_btn = gr.Button(
                                    f"下载 {model_name}",
                                    variant="primary",
                                    elem_id=f"download_{model_name}"
                                )
                                
                                progress_bar = gr.Progress(
                                    label=f"{model_name} 下载进度",
                                    visible=False
                                )
                                
                                # 绑定下载事件
                                download_btn.click(
                                    fn=lambda name=model_name, url=model_info['url']: self.download_model(name, url),
                                    outputs=[progress_bar]
                                )
            
            # 自定义下载
            with gr.TabItem("自定义下载"):
                custom_name = gr.Textbox(
                    label="模型名称",
                    placeholder="输入模型名称..."
                )
                
                custom_url = gr.Textbox(
                    label="下载URL",
                    placeholder="输入模型下载链接..."
                )
                
                custom_download_btn = gr.Button("开始下载", variant="primary")
                custom_progress = gr.Progress(label="下载进度")
                custom_status = gr.Markdown(value="", label="下载状态")
                
                custom_download_btn.click(
                    fn=self.download_custom_model,
                    inputs=[custom_name, custom_url],
                    outputs=[custom_progress, custom_status]
                )
    
    def get_current_llm_info(self) -> str:
        """获取当前语言模型信息"""
        try:
            model_info = model_manager.get_current_model_info()
            if model_info:
                return f"""
**当前模型**: {model_info['name']} ✅

**路径**: {model_info.get('path', '未知')}

**大小**: {model_info.get('size', '未知')}

**状态**: 已加载
                """
            else:
                return "**状态**: 未加载任何模型 ❌"
        except Exception as e:
            return f"**错误**: {str(e)}"
    
    def get_current_image_info(self) -> str:
        """获取当前图像模型信息"""
        try:
            current_model = engine_2d.get_current_model()
            if current_model:
                return f"""
**当前模型**: {current_model} ✅

**状态**: 已加载
                """
            else:
                return "**状态**: 未加载任何模型 ❌"
        except Exception as e:
            return f"**错误**: {str(e)}"
    
    def get_available_llm_models(self) -> List[str]:
        """获取可用语言模型列表"""
        try:
            models = model_manager.get_available_models()
            return list(models.keys())
        except Exception as e:
            logger.error(f"获取语言模型列表失败: {e}")
            return []
    
    def get_available_image_models(self) -> List[str]:
        """获取可用图像模型列表"""
        try:
            models = engine_2d.list_models()
            return list(models.keys())
        except Exception as e:
            logger.error(f"获取图像模型列表失败: {e}")
            return []
    
    def refresh_llm_models(self) -> Tuple[gr.Dropdown, str]:
        """刷新语言模型列表"""
        models = self.get_available_llm_models()
        current_info = self.get_current_llm_info()
        return gr.Dropdown(choices=models), current_info
    
    def refresh_image_models(self) -> Tuple[gr.Dropdown, str]:
        """刷新图像模型列表"""
        models = self.get_available_image_models()
        current_info = self.get_current_image_info()
        return gr.Dropdown(choices=models), current_info
    
    def load_llm_model(self, model_name: str) -> Tuple[str, str]:
        """加载语言模型"""
        if not model_name:
            return self.get_current_llm_info(), "❌ 请选择一个模型"
        
        try:
            if model_manager.load_model(model_name):
                return self.get_current_llm_info(), f"✅ 模型 {model_name} 加载成功"
            else:
                return self.get_current_llm_info(), f"❌ 模型 {model_name} 加载失败"
        except Exception as e:
            return self.get_current_llm_info(), f"❌ 加载出错: {str(e)}"
    
    def load_image_model(self, model_name: str) -> Tuple[str, str]:
        """加载图像模型"""
        if not model_name:
            return self.get_current_image_info(), "❌ 请选择一个模型"
        
        try:
            if engine_2d.load_model(model_name):
                return self.get_current_image_info(), f"✅ 模型 {model_name} 加载成功"
            else:
                return self.get_current_image_info(), f"❌ 模型 {model_name} 加载失败"
        except Exception as e:
            return self.get_current_image_info(), f"❌ 加载出错: {str(e)}"
    
    def unload_llm_model(self) -> Tuple[str, str]:
        """卸载语言模型"""
        try:
            model_manager.unload_model()
            return self.get_current_llm_info(), "✅ 语言模型已卸载"
        except Exception as e:
            return self.get_current_llm_info(), f"❌ 卸载出错: {str(e)}"
    
    def unload_image_model(self) -> Tuple[str, str]:
        """卸载图像模型"""
        try:
            engine_2d.unload_model()
            return self.get_current_image_info(), "✅ 图像模型已卸载"
        except Exception as e:
            return self.get_current_image_info(), f"❌ 卸载出错: {str(e)}"
    
    def download_model(self, model_name: str, model_url: str) -> gr.Progress:
        """下载预设模型"""
        try:
            def progress_callback(downloaded: int, total: int, status: str):
                if total > 0:
                    progress = downloaded / total
                    return gr.Progress(progress, desc=status)
                return gr.Progress(0, desc=status)
            
            success = model_downloader.download_model(
                model_url,
                model_name,
                progress_callback=progress_callback
            )
            
            if success:
                return gr.Progress(1.0, desc=f"✅ {model_name} 下载完成")
            else:
                return gr.Progress(0, desc=f"❌ {model_name} 下载失败")
                
        except Exception as e:
            logger.error(f"下载模型失败: {e}")
            return gr.Progress(0, desc=f"❌ 下载出错: {str(e)}")
    
    def download_custom_model(self, model_name: str, model_url: str) -> Tuple[gr.Progress, str]:
        """下载自定义模型"""
        if not model_name or not model_url:
            return gr.Progress(0), "❌ 请输入模型名称和下载URL"
        
        try:
            def progress_callback(downloaded: int, total: int, status: str):
                if total > 0:
                    progress = downloaded / total
                    return progress
                return 0
            
            success = model_downloader.download_model(
                model_url,
                model_name,
                progress_callback=progress_callback
            )
            
            if success:
                return gr.Progress(1.0), f"✅ {model_name} 下载完成"
            else:
                return gr.Progress(0), f"❌ {model_name} 下载失败"
                
        except Exception as e:
            logger.error(f"下载自定义模型失败: {e}")
            return gr.Progress(0), f"❌ 下载出错: {str(e)}"
