import{f as a,i as u,g as o}from"./KHR_interactivity.DEAVS2UW.js";import{R as c}from"./declarationMapper.UBCwU7BT.js";import{R as l}from"./index.BoI39RQH.js";class r extends a{constructor(t){super(t),this.config=t,this.default=this._registerSignalOutput("default"),this._caseToOutputFlow=new Map,this.case=this.registerDataInput("case",c),(this.config.cases||[]).forEach(s=>{this._caseToOutputFlow.set(s,this._registerSignalOutput(`out_${s}`))})}_execute(t,s){const i=this.case.getValue(t);let e;u(i)?e=this._getOutputFlowForCase(o(i)):e=this._getOutputFlowForCase(i),e?e._activateSignal(t):this.default._activateSignal(t)}addCase(t){this.config.cases.includes(t)||(this.config.cases.push(t),this._caseToOutputFlow.set(t,this._registerSignalOutput(`out_${t}`)))}removeCase(t){if(!this.config.cases.includes(t))return;const s=this.config.cases.indexOf(t);this.config.cases.splice(s,1),this._caseToOutputFlow.delete(t)}_getOutputFlowForCase(t){return this._caseToOutputFlow.get(t)}getClassName(){return"FlowGraphSwitchBlock"}serialize(t){super.serialize(t),t.cases=this.config.cases}}l("FlowGraphSwitchBlock",r);export{r as FlowGraphSwitchBlock};
//# sourceMappingURL=flowGraphSwitchBlock.rlj1f3Pb.js.map
