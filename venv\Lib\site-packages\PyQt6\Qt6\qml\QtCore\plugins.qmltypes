import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "qpermissions.h"
        name: "QBluetoothPermission"
        accessSemantics: "value"
        Enum {
            name: "CommunicationModes"
            alias: "CommunicationMode"
            isFlag: true
            type: "quint8"
            values: ["Access", "Advertise", "Default"]
        }
    }
    Component {
        file: "qpermissions.h"
        name: "QCalendarPermission"
        accessSemantics: "value"
        Enum {
            name: "AccessMode"
            type: "quint8"
            values: ["ReadOnly", "ReadWrite"]
        }
    }
    Component {
        file: "qpermissions.h"
        name: "QContactsPermission"
        accessSemantics: "value"
        Enum {
            name: "AccessMode"
            type: "quint8"
            values: ["ReadOnly", "ReadWrite"]
        }
    }
    Component {
        file: "qpermissions.h"
        name: "QLocationPermission"
        accessSemantics: "value"
        Enum {
            name: "Accuracy"
            type: "quint8"
            values: ["Approximate", "Precise"]
        }
        Enum {
            name: "Availability"
            type: "quint8"
            values: ["WhenInUse", "Always"]
        }
    }
    Component {
        file: "private/qqmlpermissions_p.h"
        name: "QQmlBluetoothPermission"
        accessSemantics: "reference"
        prototype: "QObject"
        extension: "QBluetoothPermission"
        extensionIsNamespace: true
        exports: ["QtCore/BluetoothPermission 6.6"]
        exportMetaObjectRevisions: [1542]
        Property {
            name: "status"
            type: "Qt::PermissionStatus"
            read: "status"
            notify: "statusChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "communicationModes"
            type: "QBluetoothPermission::CommunicationModes"
            read: "communicationModes"
            write: "setCommunicationModes"
            notify: "communicationModesChanged"
            index: 1
        }
        Signal { name: "statusChanged" }
        Signal { name: "communicationModesChanged" }
        Method { name: "request" }
    }
    Component {
        file: "private/qqmlpermissions_p.h"
        name: "QQmlCalendarPermission"
        accessSemantics: "reference"
        prototype: "QObject"
        extension: "QCalendarPermission"
        extensionIsNamespace: true
        exports: ["QtCore/CalendarPermission 6.6"]
        exportMetaObjectRevisions: [1542]
        Property {
            name: "status"
            type: "Qt::PermissionStatus"
            read: "status"
            notify: "statusChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "accessMode"
            type: "QCalendarPermission::AccessMode"
            read: "accessMode"
            write: "setAccessMode"
            notify: "accessModeChanged"
            index: 1
        }
        Signal { name: "statusChanged" }
        Signal { name: "accessModeChanged" }
        Method { name: "request" }
    }
    Component {
        file: "private/qqmlpermissions_p.h"
        name: "QQmlCameraPermission"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtCore/CameraPermission 6.6"]
        exportMetaObjectRevisions: [1542]
        Property {
            name: "status"
            type: "Qt::PermissionStatus"
            read: "status"
            notify: "statusChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "statusChanged" }
        Method { name: "request" }
    }
    Component {
        file: "private/qqmlpermissions_p.h"
        name: "QQmlContactsPermission"
        accessSemantics: "reference"
        prototype: "QObject"
        extension: "QContactsPermission"
        extensionIsNamespace: true
        exports: ["QtCore/ContactsPermission 6.6"]
        exportMetaObjectRevisions: [1542]
        Property {
            name: "status"
            type: "Qt::PermissionStatus"
            read: "status"
            notify: "statusChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "accessMode"
            type: "QContactsPermission::AccessMode"
            read: "accessMode"
            write: "setAccessMode"
            notify: "accessModeChanged"
            index: 1
        }
        Signal { name: "statusChanged" }
        Signal { name: "accessModeChanged" }
        Method { name: "request" }
    }
    Component {
        file: "private/qqmlpermissions_p.h"
        name: "QQmlMicrophonePermission"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtCore/MicrophonePermission 6.6"]
        exportMetaObjectRevisions: [1542]
        Property {
            name: "status"
            type: "Qt::PermissionStatus"
            read: "status"
            notify: "statusChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "statusChanged" }
        Method { name: "request" }
    }
    Component {
        file: "private/qqmlpermissions_p.h"
        name: "QQmlQLocationPermission"
        accessSemantics: "reference"
        prototype: "QObject"
        extension: "QLocationPermission"
        extensionIsNamespace: true
        exports: ["QtCore/LocationPermission 6.6"]
        exportMetaObjectRevisions: [1542]
        Property {
            name: "status"
            type: "Qt::PermissionStatus"
            read: "status"
            notify: "statusChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "availability"
            type: "QLocationPermission::Availability"
            read: "availability"
            write: "setAvailability"
            notify: "availabilityChanged"
            index: 1
        }
        Property {
            name: "accuracy"
            type: "QLocationPermission::Accuracy"
            read: "accuracy"
            write: "setAccuracy"
            notify: "accuracyChanged"
            index: 2
        }
        Signal { name: "statusChanged" }
        Signal { name: "availabilityChanged" }
        Signal { name: "accuracyChanged" }
        Method { name: "request" }
    }
    Component {
        file: "private/qqmlsettings_p.h"
        name: "QQmlSettings"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: ["QtCore/Settings 6.5"]
        exportMetaObjectRevisions: [1541]
        Property {
            name: "category"
            type: "QString"
            read: "category"
            write: "setCategory"
            notify: "categoryChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "location"
            type: "QUrl"
            read: "location"
            write: "setLocation"
            notify: "locationChanged"
            index: 1
            isFinal: true
        }
        Signal {
            name: "categoryChanged"
            Parameter { name: "arg"; type: "QString" }
        }
        Signal {
            name: "locationChanged"
            Parameter { name: "arg"; type: "QUrl" }
        }
        Method { name: "_q_propertyChanged" }
        Method {
            name: "value"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "key"; type: "QString" }
            Parameter { name: "defaultValue"; type: "QVariant" }
        }
        Method {
            name: "value"
            type: "QVariant"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "key"; type: "QString" }
        }
        Method {
            name: "setValue"
            Parameter { name: "key"; type: "QString" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method { name: "sync" }
    }
    Component {
        file: "private/qqmlstandardpaths_p.h"
        name: "QQmlStandardPaths"
        accessSemantics: "reference"
        prototype: "QObject"
        extension: "QStandardPaths"
        extensionIsNamespace: true
        exports: ["QtCore/StandardPaths 6.2"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [1538]
        Method {
            name: "displayName"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "type"; type: "QStandardPaths::StandardLocation" }
        }
        Method {
            name: "findExecutable"
            type: "QUrl"
            isMethodConstant: true
            Parameter { name: "executableName"; type: "QString" }
            Parameter { name: "paths"; type: "QStringList" }
        }
        Method {
            name: "findExecutable"
            type: "QUrl"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "executableName"; type: "QString" }
        }
        Method {
            name: "locate"
            type: "QUrl"
            isMethodConstant: true
            Parameter { name: "type"; type: "QStandardPaths::StandardLocation" }
            Parameter { name: "fileName"; type: "QString" }
            Parameter { name: "options"; type: "QStandardPaths::LocateOptions" }
        }
        Method {
            name: "locate"
            type: "QUrl"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "type"; type: "QStandardPaths::StandardLocation" }
            Parameter { name: "fileName"; type: "QString" }
        }
        Method {
            name: "locateAll"
            type: "QUrl"
            isList: true
            isMethodConstant: true
            Parameter { name: "type"; type: "QStandardPaths::StandardLocation" }
            Parameter { name: "fileName"; type: "QString" }
            Parameter { name: "options"; type: "QStandardPaths::LocateOptions" }
        }
        Method {
            name: "locateAll"
            type: "QUrl"
            isList: true
            isCloned: true
            isMethodConstant: true
            Parameter { name: "type"; type: "QStandardPaths::StandardLocation" }
            Parameter { name: "fileName"; type: "QString" }
        }
        Method {
            name: "standardLocations"
            type: "QUrl"
            isList: true
            isMethodConstant: true
            Parameter { name: "type"; type: "QStandardPaths::StandardLocation" }
        }
        Method {
            name: "writableLocation"
            type: "QUrl"
            isMethodConstant: true
            Parameter { name: "type"; type: "QStandardPaths::StandardLocation" }
        }
    }
    Component {
        file: "private/qqmlsysteminformation_p.h"
        name: "QQmlSystemInformation"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtCore/SystemInformation 6.4"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [1540]
        Enum {
            name: "Endian"
            isScoped: true
            values: ["Big", "Little"]
        }
        Property {
            name: "wordSize"
            type: "int"
            read: "wordSize"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "byteOrder"
            type: "QQmlSystemInformation::Endian"
            read: "byteOrder"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "buildCpuArchitecture"
            type: "QString"
            read: "buildCpuArchitecture"
            index: 2
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "currentCpuArchitecture"
            type: "QString"
            read: "currentCpuArchitecture"
            index: 3
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "buildAbi"
            type: "QString"
            read: "buildAbi"
            index: 4
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "kernelType"
            type: "QString"
            read: "kernelType"
            index: 5
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "kernelVersion"
            type: "QString"
            read: "kernelVersion"
            index: 6
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "productType"
            type: "QString"
            read: "productType"
            index: 7
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "productVersion"
            type: "QString"
            read: "productVersion"
            index: 8
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "prettyProductName"
            type: "QString"
            read: "prettyProductName"
            index: 9
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "machineHostName"
            type: "QString"
            read: "machineHostName"
            index: 10
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "machineUniqueId"
            type: "QByteArray"
            read: "machineUniqueId"
            index: 11
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "bootUniqueId"
            type: "QByteArray"
            read: "bootUniqueId"
            index: 12
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "qstandardpaths.h"
        name: "QStandardPaths"
        accessSemantics: "value"
        Enum {
            name: "StandardLocation"
            values: [
                "DesktopLocation",
                "DocumentsLocation",
                "FontsLocation",
                "ApplicationsLocation",
                "MusicLocation",
                "MoviesLocation",
                "PicturesLocation",
                "TempLocation",
                "HomeLocation",
                "AppLocalDataLocation",
                "CacheLocation",
                "GenericDataLocation",
                "RuntimeLocation",
                "ConfigLocation",
                "DownloadLocation",
                "GenericCacheLocation",
                "GenericConfigLocation",
                "AppDataLocation",
                "AppConfigLocation",
                "PublicShareLocation",
                "TemplatesLocation",
                "StateLocation",
                "GenericStateLocation"
            ]
        }
        Enum {
            name: "LocateOptions"
            alias: "LocateOption"
            isFlag: true
            values: ["LocateFile", "LocateDirectory"]
        }
    }
}
