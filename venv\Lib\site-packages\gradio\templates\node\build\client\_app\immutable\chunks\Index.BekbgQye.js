import{SvelteComponent as Ae,init as Ge,safe_not_equal as He,flush as m,empty as L,insert_hydration as M,group_outros as se,transition_out as b,check_outros as ne,transition_in as w,detach as Q,afterUpdate as Ke,binding_callbacks as N,bind as D,create_component as I,claim_component as B,mount_component as S,add_flush_callback as E,destroy_component as z,assign as le,space as ie,claim_space as ae,get_spread_update as re,get_spread_object as oe}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import Le from"./ImagePreview.B6xPgKdu.js";import{I as Me}from"./ImageUploader.Dvb2Mtrn.js";import{W as ht}from"./ImageUploader.Dvb2Mtrn.js";import{B as _e,S as ue}from"./2.B2AoQPnG.js";import{r as bt}from"./2.B2AoQPnG.js";import{E as Qe}from"./Empty.DwQ6nkN6.js";import{I as Re}from"./Image.CTVzPhL7.js";import{U as fe}from"./UploadText.CJcy9n89.js";import{default as pt}from"./Example.BnEs1fCL.js";function Ve(t){let e,s,n;function _(r){t[63](r)}let f={visible:t[5],variant:t[0]===null?"dashed":"solid",border_mode:t[29]?"focus":"base",padding:!1,elem_id:t[3],elem_classes:t[4],height:t[10]||void 0,width:t[11],allow_overflow:!1,container:t[14],scale:t[15],min_width:t[16],$$slots:{default:[et]},$$scope:{ctx:t}};return t[28]!==void 0&&(f.fullscreen=t[28]),e=new _e({props:f}),N.push(()=>D(e,"fullscreen",_)),e.$on("dragenter",t[32]),e.$on("dragleave",t[32]),e.$on("dragover",t[32]),e.$on("drop",t[33]),{c(){I(e.$$.fragment)},l(r){B(e.$$.fragment,r)},m(r,a){S(e,r,a),n=!0},p(r,a){const i={};a[0]&32&&(i.visible=r[5]),a[0]&1&&(i.variant=r[0]===null?"dashed":"solid"),a[0]&536870912&&(i.border_mode=r[29]?"focus":"base"),a[0]&8&&(i.elem_id=r[3]),a[0]&16&&(i.elem_classes=r[4]),a[0]&1024&&(i.height=r[10]||void 0),a[0]&2048&&(i.width=r[11]),a[0]&16384&&(i.container=r[14]),a[0]&32768&&(i.scale=r[15]),a[0]&65536&&(i.min_width=r[16]),a[0]&2138321607|a[1]&1|a[2]&8&&(i.$$scope={dirty:a,ctx:r}),!s&&a[0]&268435456&&(s=!0,i.fullscreen=r[28],E(()=>s=!1)),e.$set(i)},i(r){n||(w(e.$$.fragment,r),n=!0)},o(r){b(e.$$.fragment,r),n=!1},d(r){z(e,r)}}}function Xe(t){let e,s,n;function _(r){t[43](r)}let f={visible:t[5],variant:"solid",border_mode:t[29]?"focus":"base",padding:!1,elem_id:t[3],elem_classes:t[4],height:t[10]||void 0,width:t[11],allow_overflow:!1,container:t[14],scale:t[15],min_width:t[16],$$slots:{default:[tt]},$$scope:{ctx:t}};return t[28]!==void 0&&(f.fullscreen=t[28]),e=new _e({props:f}),N.push(()=>D(e,"fullscreen",_)),{c(){I(e.$$.fragment)},l(r){B(e.$$.fragment,r)},m(r,a){S(e,r,a),n=!0},p(r,a){const i={};a[0]&32&&(i.visible=r[5]),a[0]&536870912&&(i.border_mode=r[29]?"focus":"base"),a[0]&8&&(i.elem_id=r[3]),a[0]&16&&(i.elem_classes=r[4]),a[0]&1024&&(i.height=r[10]||void 0),a[0]&2048&&(i.width=r[11]),a[0]&16384&&(i.container=r[14]),a[0]&32768&&(i.scale=r[15]),a[0]&65536&&(i.min_width=r[16]),a[0]&310518213|a[2]&8&&(i.$$scope={dirty:a,ctx:r}),!s&&a[0]&268435456&&(s=!0,i.fullscreen=r[28],E(()=>s=!1)),e.$set(i)},i(r){n||(w(e.$$.fragment,r),n=!0)},o(r){b(e.$$.fragment,r),n=!1},d(r){z(e,r)}}}function Ye(t){let e,s;return e=new Qe({props:{unpadded_box:!0,size:"large",$$slots:{default:[ye]},$$scope:{ctx:t}}}),{c(){I(e.$$.fragment)},l(n){B(e.$$.fragment,n)},m(n,_){S(e,n,_),s=!0},p(n,_){const f={};_[2]&8&&(f.$$scope={dirty:_,ctx:n}),e.$set(f)},i(n){s||(w(e.$$.fragment,n),s=!0)},o(n){b(e.$$.fragment,n),s=!1},d(n){z(e,n)}}}function Ze(t){let e,s;return e=new fe({props:{i18n:t[25].i18n,type:"clipboard",mode:"short"}}),{c(){I(e.$$.fragment)},l(n){B(e.$$.fragment,n)},m(n,_){S(e,n,_),s=!0},p(n,_){const f={};_[0]&33554432&&(f.i18n=n[25].i18n),e.$set(f)},i(n){s||(w(e.$$.fragment,n),s=!0)},o(n){b(e.$$.fragment,n),s=!1},d(n){z(e,n)}}}function $e(t){let e,s;return e=new fe({props:{i18n:t[25].i18n,type:"image",placeholder:t[22]}}),{c(){I(e.$$.fragment)},l(n){B(e.$$.fragment,n)},m(n,_){S(e,n,_),s=!0},p(n,_){const f={};_[0]&33554432&&(f.i18n=n[25].i18n),_[0]&4194304&&(f.placeholder=n[22]),e.$set(f)},i(n){s||(w(e.$$.fragment,n),s=!0)},o(n){b(e.$$.fragment,n),s=!1},d(n){z(e,n)}}}function ye(t){let e,s;return e=new Re({}),{c(){I(e.$$.fragment)},l(n){B(e.$$.fragment,n)},m(n,_){S(e,n,_),s=!0},i(n){s||(w(e.$$.fragment,n),s=!0)},o(n){b(e.$$.fragment,n),s=!1},d(n){z(e,n)}}}function xe(t){let e,s,n,_;const f=[$e,Ze,Ye],r=[];function a(i,h){return i[30]==="upload"||!i[30]?0:i[30]==="clipboard"?1:2}return e=a(t),s=r[e]=f[e](t),{c(){s.c(),n=L()},l(i){s.l(i),n=L()},m(i,h){r[e].m(i,h),M(i,n,h),_=!0},p(i,h){let c=e;e=a(i),e===c?r[e].p(i,h):(se(),b(r[c],1,1,()=>{r[c]=null}),ne(),s=r[e],s?s.p(i,h):(s=r[e]=f[e](i),s.c()),w(s,1),s.m(n.parentNode,n))},i(i){_||(w(s),_=!0)},o(i){b(s),_=!1},d(i){i&&Q(n),r[e].d(i)}}}function et(t){var W;let e,s,n,_,f,r,a,i,h,c;const J=[{autoscroll:t[25].autoscroll},{i18n:t[25].i18n},t[2]];let p={};for(let o=0;o<J.length;o+=1)p=le(p,J[o]);e=new ue({props:p}),e.$on("clear_status",t[44]);function O(o){t[47](o)}function q(o){t[48](o)}function C(o){t[49](o)}function T(o){t[50](o)}function A(o){t[51](o)}function G(o){t[52](o)}let k={selectable:t[13],root:t[9],sources:t[18],fullscreen:t[28],label:t[6],show_label:t[7],pending:t[21],streaming:t[20],webcam_options:t[24],stream_every:t[12],max_file_size:t[25].max_file_size,i18n:t[25].i18n,upload:t[45],stream_handler:(W=t[25].client)==null?void 0:W.stream,$$slots:{default:[xe]},$$scope:{ctx:t}};return t[26]!==void 0&&(k.uploading=t[26]),t[30]!==void 0&&(k.active_source=t[30]),t[0]!==void 0&&(k.value=t[0]),t[29]!==void 0&&(k.dragging=t[29]),t[27]!==void 0&&(k.modify_stream=t[27]),t[1]!==void 0&&(k.set_time_limit=t[1]),n=new Me({props:k}),t[46](n),N.push(()=>D(n,"uploading",O)),N.push(()=>D(n,"active_source",q)),N.push(()=>D(n,"value",C)),N.push(()=>D(n,"dragging",T)),N.push(()=>D(n,"modify_stream",A)),N.push(()=>D(n,"set_time_limit",G)),n.$on("edit",t[53]),n.$on("clear",t[54]),n.$on("stream",t[55]),n.$on("drag",t[56]),n.$on("upload",t[57]),n.$on("select",t[58]),n.$on("share",t[59]),n.$on("error",t[60]),n.$on("close_stream",t[61]),n.$on("fullscreen",t[62]),{c(){I(e.$$.fragment),s=ie(),I(n.$$.fragment)},l(o){B(e.$$.fragment,o),s=ae(o),B(n.$$.fragment,o)},m(o,u){S(e,o,u),M(o,s,u),S(n,o,u),c=!0},p(o,u){var v;const H=u[0]&33554436?re(J,[u[0]&33554432&&{autoscroll:o[25].autoscroll},u[0]&33554432&&{i18n:o[25].i18n},u[0]&4&&oe(o[2])]):{};e.$set(H);const g={};u[0]&8192&&(g.selectable=o[13]),u[0]&512&&(g.root=o[9]),u[0]&262144&&(g.sources=o[18]),u[0]&268435456&&(g.fullscreen=o[28]),u[0]&64&&(g.label=o[6]),u[0]&128&&(g.show_label=o[7]),u[0]&2097152&&(g.pending=o[21]),u[0]&1048576&&(g.streaming=o[20]),u[0]&16777216&&(g.webcam_options=o[24]),u[0]&4096&&(g.stream_every=o[12]),u[0]&33554432&&(g.max_file_size=o[25].max_file_size),u[0]&33554432&&(g.i18n=o[25].i18n),u[0]&33554432&&(g.upload=o[45]),u[0]&33554432&&(g.stream_handler=(v=o[25].client)==null?void 0:v.stream),u[0]&1111490560|u[2]&8&&(g.$$scope={dirty:u,ctx:o}),!_&&u[0]&67108864&&(_=!0,g.uploading=o[26],E(()=>_=!1)),!f&&u[0]&1073741824&&(f=!0,g.active_source=o[30],E(()=>f=!1)),!r&&u[0]&1&&(r=!0,g.value=o[0],E(()=>r=!1)),!a&&u[0]&536870912&&(a=!0,g.dragging=o[29],E(()=>a=!1)),!i&&u[0]&134217728&&(i=!0,g.modify_stream=o[27],E(()=>i=!1)),!h&&u[0]&2&&(h=!0,g.set_time_limit=o[1],E(()=>h=!1)),n.$set(g)},i(o){c||(w(e.$$.fragment,o),w(n.$$.fragment,o),c=!0)},o(o){b(e.$$.fragment,o),b(n.$$.fragment,o),c=!1},d(o){o&&Q(s),z(e,o),t[46](null),z(n,o)}}}function tt(t){let e,s,n,_;const f=[{autoscroll:t[25].autoscroll},{i18n:t[25].i18n},t[2]];let r={};for(let a=0;a<f.length;a+=1)r=le(r,f[a]);return e=new ue({props:r}),n=new Le({props:{fullscreen:t[28],value:t[0],label:t[6],show_label:t[7],show_download_button:t[8],selectable:t[13],show_share_button:t[17],i18n:t[25].i18n,show_fullscreen_button:t[23]}}),n.$on("select",t[39]),n.$on("share",t[40]),n.$on("error",t[41]),n.$on("fullscreen",t[42]),{c(){I(e.$$.fragment),s=ie(),I(n.$$.fragment)},l(a){B(e.$$.fragment,a),s=ae(a),B(n.$$.fragment,a)},m(a,i){S(e,a,i),M(a,s,i),S(n,a,i),_=!0},p(a,i){const h=i[0]&33554436?re(f,[i[0]&33554432&&{autoscroll:a[25].autoscroll},i[0]&33554432&&{i18n:a[25].i18n},i[0]&4&&oe(a[2])]):{};e.$set(h);const c={};i[0]&268435456&&(c.fullscreen=a[28]),i[0]&1&&(c.value=a[0]),i[0]&64&&(c.label=a[6]),i[0]&128&&(c.show_label=a[7]),i[0]&256&&(c.show_download_button=a[8]),i[0]&8192&&(c.selectable=a[13]),i[0]&131072&&(c.show_share_button=a[17]),i[0]&33554432&&(c.i18n=a[25].i18n),i[0]&8388608&&(c.show_fullscreen_button=a[23]),n.$set(c)},i(a){_||(w(e.$$.fragment,a),w(n.$$.fragment,a),_=!0)},o(a){b(e.$$.fragment,a),b(n.$$.fragment,a),_=!1},d(a){a&&Q(s),z(e,a),z(n,a)}}}function st(t){let e,s,n,_;const f=[Xe,Ve],r=[];function a(i,h){return i[19]?1:0}return e=a(t),s=r[e]=f[e](t),{c(){s.c(),n=L()},l(i){s.l(i),n=L()},m(i,h){r[e].m(i,h),M(i,n,h),_=!0},p(i,h){let c=e;e=a(i),e===c?r[e].p(i,h):(se(),b(r[c],1,1,()=>{r[c]=null}),ne(),s=r[e],s?s.p(i,h):(s=r[e]=f[e](i),s.c()),w(s,1),s.m(n.parentNode,n))},i(i){_||(w(s),_=!0)},o(i){b(s),_=!1},d(i){i&&Q(n),r[e].d(i)}}}function nt(t,e,s){let n="closed",_=()=>{};function f(l){n=l,_(l)}const r=()=>n;let{set_time_limit:a}=e,{value_is_output:i=!1}=e,{elem_id:h=""}=e,{elem_classes:c=[]}=e,{visible:J=!0}=e,{value:p=null}=e,O=null,{label:q}=e,{show_label:C}=e,{show_download_button:T}=e,{root:A}=e,{height:G}=e,{width:k}=e,{stream_every:W}=e,{_selectable:o=!1}=e,{container:u=!0}=e,{scale:H=null}=e,{min_width:g=void 0}=e,{loading_status:v}=e,{show_share_button:Y=!1}=e,{sources:Z=["upload","clipboard","webcam"]}=e,{interactive:R}=e,{streaming:$}=e,{pending:y}=e,{placeholder:x=void 0}=e,{show_fullscreen_button:ee}=e,{input_ready:V}=e,{webcam_options:te}=e,P=!1,K=!1,{gradio:d}=e;Ke(()=>{s(34,i=!1)});let F,X=null,j;const me=l=>{const U=l;U.preventDefault(),U.stopPropagation(),U.type==="dragenter"||U.type==="dragover"?s(29,F=!0):U.type==="dragleave"&&s(29,F=!1)},ce=l=>{if(R){const U=l;U.preventDefault(),U.stopPropagation(),s(29,F=!1),j&&j.loadFilesFromDrop(U)}},ge=({detail:l})=>d.dispatch("select",l),he=({detail:l})=>d.dispatch("share",l),de=({detail:l})=>d.dispatch("error",l),be=({detail:l})=>{s(28,P=l)};function we(l){P=l,s(28,P)}const pe=()=>d.dispatch("clear_status",v),ke=(...l)=>d.client.upload(...l);function ve(l){N[l?"unshift":"push"](()=>{j=l,s(31,j)})}function Ie(l){K=l,s(26,K)}function Be(l){X=l,s(30,X)}function Se(l){p=l,s(0,p)}function ze(l){F=l,s(29,F)}function Ue(l){_=l,s(27,_)}function Ne(l){a=l,s(1,a)}const De=()=>d.dispatch("edit"),Ee=()=>{d.dispatch("clear")},Pe=({detail:l})=>d.dispatch("stream",l),Fe=({detail:l})=>s(29,F=l),Je=()=>d.dispatch("upload"),Oe=({detail:l})=>d.dispatch("select",l),We=({detail:l})=>d.dispatch("share",l),je=({detail:l})=>{s(2,v=v||{}),s(2,v.status="error",v),d.dispatch("error",l)},qe=()=>{d.dispatch("close_stream","stream")},Ce=({detail:l})=>{s(28,P=l)};function Te(l){P=l,s(28,P)}return t.$$set=l=>{"set_time_limit"in l&&s(1,a=l.set_time_limit),"value_is_output"in l&&s(34,i=l.value_is_output),"elem_id"in l&&s(3,h=l.elem_id),"elem_classes"in l&&s(4,c=l.elem_classes),"visible"in l&&s(5,J=l.visible),"value"in l&&s(0,p=l.value),"label"in l&&s(6,q=l.label),"show_label"in l&&s(7,C=l.show_label),"show_download_button"in l&&s(8,T=l.show_download_button),"root"in l&&s(9,A=l.root),"height"in l&&s(10,G=l.height),"width"in l&&s(11,k=l.width),"stream_every"in l&&s(12,W=l.stream_every),"_selectable"in l&&s(13,o=l._selectable),"container"in l&&s(14,u=l.container),"scale"in l&&s(15,H=l.scale),"min_width"in l&&s(16,g=l.min_width),"loading_status"in l&&s(2,v=l.loading_status),"show_share_button"in l&&s(17,Y=l.show_share_button),"sources"in l&&s(18,Z=l.sources),"interactive"in l&&s(19,R=l.interactive),"streaming"in l&&s(20,$=l.streaming),"pending"in l&&s(21,y=l.pending),"placeholder"in l&&s(22,x=l.placeholder),"show_fullscreen_button"in l&&s(23,ee=l.show_fullscreen_button),"input_ready"in l&&s(35,V=l.input_ready),"webcam_options"in l&&s(24,te=l.webcam_options),"gradio"in l&&s(25,d=l.gradio)},t.$$.update=()=>{t.$$.dirty[0]&67108864&&s(35,V=!K),t.$$.dirty[0]&33554433|t.$$.dirty[1]&136&&JSON.stringify(p)!==JSON.stringify(O)&&(s(38,O=p),d.dispatch("change"),i||d.dispatch("input"))},[p,a,v,h,c,J,q,C,T,A,G,k,W,o,u,H,g,Y,Z,R,$,y,x,ee,te,d,K,_,P,F,X,j,me,ce,i,V,f,r,O,ge,he,de,be,we,pe,ke,ve,Ie,Be,Se,ze,Ue,Ne,De,Ee,Pe,Fe,Je,Oe,We,je,qe,Ce,Te]}class mt extends Ae{constructor(e){super(),Ge(this,e,nt,st,He,{modify_stream_state:36,get_stream_state:37,set_time_limit:1,value_is_output:34,elem_id:3,elem_classes:4,visible:5,value:0,label:6,show_label:7,show_download_button:8,root:9,height:10,width:11,stream_every:12,_selectable:13,container:14,scale:15,min_width:16,loading_status:2,show_share_button:17,sources:18,interactive:19,streaming:20,pending:21,placeholder:22,show_fullscreen_button:23,input_ready:35,webcam_options:24,gradio:25},null,[-1,-1,-1])}get modify_stream_state(){return this.$$.ctx[36]}get get_stream_state(){return this.$$.ctx[37]}get set_time_limit(){return this.$$.ctx[1]}set set_time_limit(e){this.$$set({set_time_limit:e}),m()}get value_is_output(){return this.$$.ctx[34]}set value_is_output(e){this.$$set({value_is_output:e}),m()}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),m()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),m()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),m()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),m()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),m()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),m()}get show_download_button(){return this.$$.ctx[8]}set show_download_button(e){this.$$set({show_download_button:e}),m()}get root(){return this.$$.ctx[9]}set root(e){this.$$set({root:e}),m()}get height(){return this.$$.ctx[10]}set height(e){this.$$set({height:e}),m()}get width(){return this.$$.ctx[11]}set width(e){this.$$set({width:e}),m()}get stream_every(){return this.$$.ctx[12]}set stream_every(e){this.$$set({stream_every:e}),m()}get _selectable(){return this.$$.ctx[13]}set _selectable(e){this.$$set({_selectable:e}),m()}get container(){return this.$$.ctx[14]}set container(e){this.$$set({container:e}),m()}get scale(){return this.$$.ctx[15]}set scale(e){this.$$set({scale:e}),m()}get min_width(){return this.$$.ctx[16]}set min_width(e){this.$$set({min_width:e}),m()}get loading_status(){return this.$$.ctx[2]}set loading_status(e){this.$$set({loading_status:e}),m()}get show_share_button(){return this.$$.ctx[17]}set show_share_button(e){this.$$set({show_share_button:e}),m()}get sources(){return this.$$.ctx[18]}set sources(e){this.$$set({sources:e}),m()}get interactive(){return this.$$.ctx[19]}set interactive(e){this.$$set({interactive:e}),m()}get streaming(){return this.$$.ctx[20]}set streaming(e){this.$$set({streaming:e}),m()}get pending(){return this.$$.ctx[21]}set pending(e){this.$$set({pending:e}),m()}get placeholder(){return this.$$.ctx[22]}set placeholder(e){this.$$set({placeholder:e}),m()}get show_fullscreen_button(){return this.$$.ctx[23]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),m()}get input_ready(){return this.$$.ctx[35]}set input_ready(e){this.$$set({input_ready:e}),m()}get webcam_options(){return this.$$.ctx[24]}set webcam_options(e){this.$$set({webcam_options:e}),m()}get gradio(){return this.$$.ctx[25]}set gradio(e){this.$$set({gradio:e}),m()}}export{pt as BaseExample,bt as BaseImage,Me as BaseImageUploader,Le as BaseStaticImage,ht as Webcam,mt as default};
//# sourceMappingURL=Index.BekbgQye.js.map
