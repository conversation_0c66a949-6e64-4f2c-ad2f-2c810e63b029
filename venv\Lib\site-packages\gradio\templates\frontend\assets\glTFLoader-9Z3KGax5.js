const __vite__fileDeps=["./animationGroup-Bwms8CyJ.js","./index-Dpxo-yl_.js","./index-B7J2Z2jS.js","./index-CJsBH6a-.css","./bone-kZWM5-u7.js","./glTFLoaderAnimation-BJGr6qtQ.js","./objectModelMapping-BR4RdEzn.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as he}from"./index-B7J2Z2jS.js";import{_ as y,s as b,b8 as te,b as R,b9 as ve,V as A,O as T,ax as O,ba as xe,M as B,bb as Me,c as ge,R as Ce,bc as we,A as Te,ay as Pe,ak as ae,bd as ue,h as w,ap as ce,a as j,aO as Se,S as de,G as Oe,aa as m,T as M,q as Re,be as ke,bf as K,bg as q,bh as ye,bi as Ee,bj as Ie,af as Le,ad as De,ao as Ne,an as Be,t as N,bk as $e,ab as fe,aM as Ve,aN as _e,bl as Ue,aT as Fe,Q as z,aL as Ge,ac as We,ar as $,C as H,at as Xe,bm as Ye,bn as Ze,bo as Ke,bp as qe}from"./index-Dpxo-yl_.js";import{B as re}from"./bone-kZWM5-u7.js";import{R as pe}from"./rawTexture-DmvUfjqF.js";import{A as ze}from"./assetContainer-BRzQBugc.js";import{c as J}from"./objectModelMapping-BR4RdEzn.js";import"./svelte/svelte.js";class I{constructor(){this.keysUp=[38],this.keysUpward=[33],this.keysDown=[40],this.keysDownward=[34],this.keysLeft=[37],this.keysRight=[39],this.rotationSpeed=.5,this.keysRotateLeft=[],this.keysRotateRight=[],this.keysRotateUp=[],this.keysRotateDown=[],this._keys=new Array}attachControl(e){e=R.BackCompatCameraNoPreventDefault(arguments),!this._onCanvasBlurObserver&&(this._scene=this.camera.getScene(),this._engine=this._scene.getEngine(),this._onCanvasBlurObserver=this._engine.onCanvasBlurObservable.add(()=>{this._keys.length=0}),this._onKeyboardObserver=this._scene.onKeyboardObservable.add(t=>{const s=t.event;if(!s.metaKey){if(t.type===ve.KEYDOWN)(this.keysUp.indexOf(s.keyCode)!==-1||this.keysDown.indexOf(s.keyCode)!==-1||this.keysLeft.indexOf(s.keyCode)!==-1||this.keysRight.indexOf(s.keyCode)!==-1||this.keysUpward.indexOf(s.keyCode)!==-1||this.keysDownward.indexOf(s.keyCode)!==-1||this.keysRotateLeft.indexOf(s.keyCode)!==-1||this.keysRotateRight.indexOf(s.keyCode)!==-1||this.keysRotateUp.indexOf(s.keyCode)!==-1||this.keysRotateDown.indexOf(s.keyCode)!==-1)&&(this._keys.indexOf(s.keyCode)===-1&&this._keys.push(s.keyCode),e||s.preventDefault());else if(this.keysUp.indexOf(s.keyCode)!==-1||this.keysDown.indexOf(s.keyCode)!==-1||this.keysLeft.indexOf(s.keyCode)!==-1||this.keysRight.indexOf(s.keyCode)!==-1||this.keysUpward.indexOf(s.keyCode)!==-1||this.keysDownward.indexOf(s.keyCode)!==-1||this.keysRotateLeft.indexOf(s.keyCode)!==-1||this.keysRotateRight.indexOf(s.keyCode)!==-1||this.keysRotateUp.indexOf(s.keyCode)!==-1||this.keysRotateDown.indexOf(s.keyCode)!==-1){const n=this._keys.indexOf(s.keyCode);n>=0&&this._keys.splice(n,1),e||s.preventDefault()}}}))}detachControl(){this._scene&&(this._onKeyboardObserver&&this._scene.onKeyboardObservable.remove(this._onKeyboardObserver),this._onCanvasBlurObserver&&this._engine.onCanvasBlurObservable.remove(this._onCanvasBlurObserver),this._onKeyboardObserver=null,this._onCanvasBlurObserver=null),this._keys.length=0}checkInputs(){if(this._onKeyboardObserver){const e=this.camera;for(let t=0;t<this._keys.length;t++){const s=this._keys[t],n=e._computeLocalCameraSpeed();this.keysLeft.indexOf(s)!==-1?e._localDirection.copyFromFloats(-n,0,0):this.keysUp.indexOf(s)!==-1?e._localDirection.copyFromFloats(0,0,n):this.keysRight.indexOf(s)!==-1?e._localDirection.copyFromFloats(n,0,0):this.keysDown.indexOf(s)!==-1?e._localDirection.copyFromFloats(0,0,-n):this.keysUpward.indexOf(s)!==-1?e._localDirection.copyFromFloats(0,n,0):this.keysDownward.indexOf(s)!==-1?e._localDirection.copyFromFloats(0,-n,0):this.keysRotateLeft.indexOf(s)!==-1?(e._localDirection.copyFromFloats(0,0,0),e.cameraRotation.y-=this._getLocalRotation()):this.keysRotateRight.indexOf(s)!==-1?(e._localDirection.copyFromFloats(0,0,0),e.cameraRotation.y+=this._getLocalRotation()):this.keysRotateUp.indexOf(s)!==-1?(e._localDirection.copyFromFloats(0,0,0),e.cameraRotation.x-=this._getLocalRotation()):this.keysRotateDown.indexOf(s)!==-1&&(e._localDirection.copyFromFloats(0,0,0),e.cameraRotation.x+=this._getLocalRotation()),e.getScene().useRightHandedSystem&&(e._localDirection.z*=-1),e.getViewMatrix().invertToRef(e._cameraTransformMatrix),A.TransformNormalToRef(e._localDirection,e._cameraTransformMatrix,e._transformedDirection),e.cameraDirection.addInPlace(e._transformedDirection)}}}getClassName(){return"FreeCameraKeyboardMoveInput"}_onLostFocus(){this._keys.length=0}getSimpleName(){return"keyboard"}_getLocalRotation(){const e=this.camera._calculateHandednessMultiplier();return this.rotationSpeed*this._engine.getDeltaTime()/1e3*e}}y([b()],I.prototype,"keysUp",void 0);y([b()],I.prototype,"keysUpward",void 0);y([b()],I.prototype,"keysDown",void 0);y([b()],I.prototype,"keysDownward",void 0);y([b()],I.prototype,"keysLeft",void 0);y([b()],I.prototype,"keysRight",void 0);y([b()],I.prototype,"rotationSpeed",void 0);y([b()],I.prototype,"keysRotateLeft",void 0);y([b()],I.prototype,"keysRotateRight",void 0);y([b()],I.prototype,"keysRotateUp",void 0);y([b()],I.prototype,"keysRotateDown",void 0);te.FreeCameraKeyboardMoveInput=I;class se{constructor(e=!0){this.touchEnabled=e,this.buttons=[0,1,2],this.angularSensibility=2e3,this._previousPosition=null,this.onPointerMovedObservable=new T,this._allowCameraRotation=!0,this._currentActiveButton=-1,this._activePointerId=-1}attachControl(e){e=R.BackCompatCameraNoPreventDefault(arguments);const t=this.camera.getEngine(),s=t.getInputElement();this._pointerInput||(this._pointerInput=n=>{const i=n.event,r=i.pointerType==="touch";if(!this.touchEnabled&&r||n.type!==O.POINTERMOVE&&this.buttons.indexOf(i.button)===-1)return;const o=i.target;if(n.type===O.POINTERDOWN){if(r&&this._activePointerId!==-1||!r&&this._currentActiveButton!==-1)return;this._activePointerId=i.pointerId;try{o?.setPointerCapture(i.pointerId)}catch{}this._currentActiveButton===-1&&(this._currentActiveButton=i.button),this._previousPosition={x:i.clientX,y:i.clientY},e||(i.preventDefault(),s&&s.focus()),t.isPointerLock&&this._onMouseMove&&this._onMouseMove(n.event)}else if(n.type===O.POINTERUP){if(r&&this._activePointerId!==i.pointerId||!r&&this._currentActiveButton!==i.button)return;try{o?.releasePointerCapture(i.pointerId)}catch{}this._currentActiveButton=-1,this._previousPosition=null,e||i.preventDefault(),this._activePointerId=-1}else if(n.type===O.POINTERMOVE&&(this._activePointerId===i.pointerId||!r)){if(t.isPointerLock&&this._onMouseMove)this._onMouseMove(n.event);else if(this._previousPosition){const a=this.camera._calculateHandednessMultiplier(),l=(i.clientX-this._previousPosition.x)*a,h=i.clientY-this._previousPosition.y;this._allowCameraRotation&&(this.camera.cameraRotation.y+=l/this.angularSensibility,this.camera.cameraRotation.x+=h/this.angularSensibility),this.onPointerMovedObservable.notifyObservers({offsetX:l,offsetY:h}),this._previousPosition={x:i.clientX,y:i.clientY},e||i.preventDefault()}}}),this._onMouseMove=n=>{if(!t.isPointerLock)return;const i=this.camera._calculateHandednessMultiplier(),r=n.movementX*i;this.camera.cameraRotation.y+=r/this.angularSensibility;const o=n.movementY;this.camera.cameraRotation.x+=o/this.angularSensibility,this._previousPosition=null,e||n.preventDefault()},this._observer=this.camera.getScene()._inputManager._addCameraPointerObserver(this._pointerInput,O.POINTERDOWN|O.POINTERUP|O.POINTERMOVE),s&&(this._contextMenuBind=n=>this.onContextMenu(n),s.addEventListener("contextmenu",this._contextMenuBind,!1))}onContextMenu(e){e.preventDefault()}detachControl(){if(this._observer){if(this.camera.getScene()._inputManager._removeCameraPointerObserver(this._observer),this._contextMenuBind){const t=this.camera.getEngine().getInputElement();t&&t.removeEventListener("contextmenu",this._contextMenuBind)}this.onPointerMovedObservable&&this.onPointerMovedObservable.clear(),this._observer=null,this._onMouseMove=null,this._previousPosition=null}this._activePointerId=-1,this._currentActiveButton=-1}getClassName(){return"FreeCameraMouseInput"}getSimpleName(){return"mouse"}}y([b()],se.prototype,"buttons",void 0);y([b()],se.prototype,"angularSensibility",void 0);te.FreeCameraMouseInput=se;class ne{constructor(){this.wheelPrecisionX=3,this.wheelPrecisionY=3,this.wheelPrecisionZ=3,this.onChangedObservable=new T,this._wheelDeltaX=0,this._wheelDeltaY=0,this._wheelDeltaZ=0,this._ffMultiplier=12,this._normalize=120}attachControl(e){e=R.BackCompatCameraNoPreventDefault(arguments),this._wheel=t=>{if(t.type!==O.POINTERWHEEL)return;const s=t.event,n=s.deltaMode===xe.DOM_DELTA_LINE?this._ffMultiplier:1;this._wheelDeltaX+=this.wheelPrecisionX*n*s.deltaX/this._normalize,this._wheelDeltaY-=this.wheelPrecisionY*n*s.deltaY/this._normalize,this._wheelDeltaZ+=this.wheelPrecisionZ*n*s.deltaZ/this._normalize,s.preventDefault&&(e||s.preventDefault())},this._observer=this.camera.getScene()._inputManager._addCameraPointerObserver(this._wheel,O.POINTERWHEEL)}detachControl(){this._observer&&(this.camera.getScene()._inputManager._removeCameraPointerObserver(this._observer),this._observer=null,this._wheel=null),this.onChangedObservable&&this.onChangedObservable.clear()}checkInputs(){this.onChangedObservable.notifyObservers({wheelDeltaX:this._wheelDeltaX,wheelDeltaY:this._wheelDeltaY,wheelDeltaZ:this._wheelDeltaZ}),this._wheelDeltaX=0,this._wheelDeltaY=0,this._wheelDeltaZ=0}getClassName(){return"BaseCameraMouseWheelInput"}getSimpleName(){return"mousewheel"}}y([b()],ne.prototype,"wheelPrecisionX",void 0);y([b()],ne.prototype,"wheelPrecisionY",void 0);y([b()],ne.prototype,"wheelPrecisionZ",void 0);var g;(function(f){f[f.MoveRelative=0]="MoveRelative",f[f.RotateRelative=1]="RotateRelative",f[f.MoveScene=2]="MoveScene"})(g||(g={}));class L extends ne{constructor(){super(...arguments),this._moveRelative=A.Zero(),this._rotateRelative=A.Zero(),this._moveScene=A.Zero(),this._wheelXAction=g.MoveRelative,this._wheelXActionCoordinate=0,this._wheelYAction=g.MoveRelative,this._wheelYActionCoordinate=2,this._wheelZAction=null,this._wheelZActionCoordinate=null}getClassName(){return"FreeCameraMouseWheelInput"}set wheelXMoveRelative(e){e===null&&this._wheelXAction!==g.MoveRelative||(this._wheelXAction=g.MoveRelative,this._wheelXActionCoordinate=e)}get wheelXMoveRelative(){return this._wheelXAction!==g.MoveRelative?null:this._wheelXActionCoordinate}set wheelYMoveRelative(e){e===null&&this._wheelYAction!==g.MoveRelative||(this._wheelYAction=g.MoveRelative,this._wheelYActionCoordinate=e)}get wheelYMoveRelative(){return this._wheelYAction!==g.MoveRelative?null:this._wheelYActionCoordinate}set wheelZMoveRelative(e){e===null&&this._wheelZAction!==g.MoveRelative||(this._wheelZAction=g.MoveRelative,this._wheelZActionCoordinate=e)}get wheelZMoveRelative(){return this._wheelZAction!==g.MoveRelative?null:this._wheelZActionCoordinate}set wheelXRotateRelative(e){e===null&&this._wheelXAction!==g.RotateRelative||(this._wheelXAction=g.RotateRelative,this._wheelXActionCoordinate=e)}get wheelXRotateRelative(){return this._wheelXAction!==g.RotateRelative?null:this._wheelXActionCoordinate}set wheelYRotateRelative(e){e===null&&this._wheelYAction!==g.RotateRelative||(this._wheelYAction=g.RotateRelative,this._wheelYActionCoordinate=e)}get wheelYRotateRelative(){return this._wheelYAction!==g.RotateRelative?null:this._wheelYActionCoordinate}set wheelZRotateRelative(e){e===null&&this._wheelZAction!==g.RotateRelative||(this._wheelZAction=g.RotateRelative,this._wheelZActionCoordinate=e)}get wheelZRotateRelative(){return this._wheelZAction!==g.RotateRelative?null:this._wheelZActionCoordinate}set wheelXMoveScene(e){e===null&&this._wheelXAction!==g.MoveScene||(this._wheelXAction=g.MoveScene,this._wheelXActionCoordinate=e)}get wheelXMoveScene(){return this._wheelXAction!==g.MoveScene?null:this._wheelXActionCoordinate}set wheelYMoveScene(e){e===null&&this._wheelYAction!==g.MoveScene||(this._wheelYAction=g.MoveScene,this._wheelYActionCoordinate=e)}get wheelYMoveScene(){return this._wheelYAction!==g.MoveScene?null:this._wheelYActionCoordinate}set wheelZMoveScene(e){e===null&&this._wheelZAction!==g.MoveScene||(this._wheelZAction=g.MoveScene,this._wheelZActionCoordinate=e)}get wheelZMoveScene(){return this._wheelZAction!==g.MoveScene?null:this._wheelZActionCoordinate}checkInputs(){if(this._wheelDeltaX===0&&this._wheelDeltaY===0&&this._wheelDeltaZ==0)return;this._moveRelative.setAll(0),this._rotateRelative.setAll(0),this._moveScene.setAll(0),this._updateCamera(),this.camera.getScene().useRightHandedSystem&&(this._moveRelative.z*=-1);const e=B.Zero();this.camera.getViewMatrix().invertToRef(e);const t=A.Zero();A.TransformNormalToRef(this._moveRelative,e,t),this.camera.cameraRotation.x+=this._rotateRelative.x/200,this.camera.cameraRotation.y+=this._rotateRelative.y/200,this.camera.cameraDirection.addInPlace(t),this.camera.cameraDirection.addInPlace(this._moveScene),super.checkInputs()}_updateCamera(){this._updateCameraProperty(this._wheelDeltaX,this._wheelXAction,this._wheelXActionCoordinate),this._updateCameraProperty(this._wheelDeltaY,this._wheelYAction,this._wheelYActionCoordinate),this._updateCameraProperty(this._wheelDeltaZ,this._wheelZAction,this._wheelZActionCoordinate)}_updateCameraProperty(e,t,s){if(e===0||t===null||s===null)return;let n=null;switch(t){case g.MoveRelative:n=this._moveRelative;break;case g.RotateRelative:n=this._rotateRelative;break;case g.MoveScene:n=this._moveScene;break}switch(s){case 0:n.set(e,0,0);break;case 1:n.set(0,e,0);break;case 2:n.set(0,0,e);break}}}y([b()],L.prototype,"wheelXMoveRelative",null);y([b()],L.prototype,"wheelYMoveRelative",null);y([b()],L.prototype,"wheelZMoveRelative",null);y([b()],L.prototype,"wheelXRotateRelative",null);y([b()],L.prototype,"wheelYRotateRelative",null);y([b()],L.prototype,"wheelZRotateRelative",null);y([b()],L.prototype,"wheelXMoveScene",null);y([b()],L.prototype,"wheelYMoveScene",null);y([b()],L.prototype,"wheelZMoveScene",null);te.FreeCameraMouseWheelInput=L;class ie{constructor(e=!1){this.allowMouse=e,this.touchAngularSensibility=2e5,this.touchMoveSensibility=250,this.singleFingerRotate=!1,this._offsetX=null,this._offsetY=null,this._pointerPressed=new Array,this._isSafari=R.IsSafari()}attachControl(e){e=R.BackCompatCameraNoPreventDefault(arguments);let t=null;if(this._pointerInput===void 0&&(this._onLostFocus=()=>{this._offsetX=null,this._offsetY=null},this._pointerInput=s=>{const n=s.event,i=n.pointerType==="mouse"||this._isSafari&&typeof n.pointerType>"u";if(!(!this.allowMouse&&i)){if(s.type===O.POINTERDOWN){if(e||n.preventDefault(),this._pointerPressed.push(n.pointerId),this._pointerPressed.length!==1)return;t={x:n.clientX,y:n.clientY}}else if(s.type===O.POINTERUP){e||n.preventDefault();const r=this._pointerPressed.indexOf(n.pointerId);if(r===-1||(this._pointerPressed.splice(r,1),r!=0))return;t=null,this._offsetX=null,this._offsetY=null}else if(s.type===O.POINTERMOVE){if(e||n.preventDefault(),!t||this._pointerPressed.indexOf(n.pointerId)!=0)return;this._offsetX=n.clientX-t.x,this._offsetY=-(n.clientY-t.y)}}}),this._observer=this.camera.getScene()._inputManager._addCameraPointerObserver(this._pointerInput,O.POINTERDOWN|O.POINTERUP|O.POINTERMOVE),this._onLostFocus){const n=this.camera.getEngine().getInputElement();n&&n.addEventListener("blur",this._onLostFocus)}}detachControl(){if(this._pointerInput){if(this._observer&&(this.camera.getScene()._inputManager._removeCameraPointerObserver(this._observer),this._observer=null),this._onLostFocus){const t=this.camera.getEngine().getInputElement();t&&t.removeEventListener("blur",this._onLostFocus),this._onLostFocus=null}this._pointerPressed.length=0,this._offsetX=null,this._offsetY=null}}checkInputs(){if(this._offsetX===null||this._offsetY===null||this._offsetX===0&&this._offsetY===0)return;const e=this.camera,t=e._calculateHandednessMultiplier();if(e.cameraRotation.y=t*this._offsetX/this.touchAngularSensibility,this.singleFingerRotate&&this._pointerPressed.length===1||!this.singleFingerRotate&&this._pointerPressed.length>1)e.cameraRotation.x=-this._offsetY/this.touchAngularSensibility;else{const n=e._computeLocalCameraSpeed(),i=new A(0,0,this.touchMoveSensibility!==0?n*this._offsetY/this.touchMoveSensibility:0);B.RotationYawPitchRollToRef(e.rotation.y,e.rotation.x,0,e._cameraRotationMatrix),e.cameraDirection.addInPlace(A.TransformCoordinates(i,e._cameraRotationMatrix))}}getClassName(){return"FreeCameraTouchInput"}getSimpleName(){return"touch"}}y([b()],ie.prototype,"touchAngularSensibility",void 0);y([b()],ie.prototype,"touchMoveSensibility",void 0);te.FreeCameraTouchInput=ie;class He extends Me{constructor(e){super(e),this._mouseInput=null,this._mouseWheelInput=null}addKeyboard(){return this.add(new I),this}addMouse(e=!0){return this._mouseInput||(this._mouseInput=new se(e),this.add(this._mouseInput)),this}removeMouse(){return this._mouseInput&&this.remove(this._mouseInput),this}addMouseWheel(){return this._mouseWheelInput||(this._mouseWheelInput=new L,this.add(this._mouseWheelInput)),this}removeMouseWheel(){return this._mouseWheelInput&&this.remove(this._mouseWheelInput),this}addTouch(){return this.add(new ie),this}clear(){super.clear(),this._mouseInput=null}}class X extends we{get angularSensibility(){const e=this.inputs.attached.mouse;return e?e.angularSensibility:0}set angularSensibility(e){const t=this.inputs.attached.mouse;t&&(t.angularSensibility=e)}get keysUp(){const e=this.inputs.attached.keyboard;return e?e.keysUp:[]}set keysUp(e){const t=this.inputs.attached.keyboard;t&&(t.keysUp=e)}get keysUpward(){const e=this.inputs.attached.keyboard;return e?e.keysUpward:[]}set keysUpward(e){const t=this.inputs.attached.keyboard;t&&(t.keysUpward=e)}get keysDown(){const e=this.inputs.attached.keyboard;return e?e.keysDown:[]}set keysDown(e){const t=this.inputs.attached.keyboard;t&&(t.keysDown=e)}get keysDownward(){const e=this.inputs.attached.keyboard;return e?e.keysDownward:[]}set keysDownward(e){const t=this.inputs.attached.keyboard;t&&(t.keysDownward=e)}get keysLeft(){const e=this.inputs.attached.keyboard;return e?e.keysLeft:[]}set keysLeft(e){const t=this.inputs.attached.keyboard;t&&(t.keysLeft=e)}get keysRight(){const e=this.inputs.attached.keyboard;return e?e.keysRight:[]}set keysRight(e){const t=this.inputs.attached.keyboard;t&&(t.keysRight=e)}get keysRotateLeft(){const e=this.inputs.attached.keyboard;return e?e.keysRotateLeft:[]}set keysRotateLeft(e){const t=this.inputs.attached.keyboard;t&&(t.keysRotateLeft=e)}get keysRotateRight(){const e=this.inputs.attached.keyboard;return e?e.keysRotateRight:[]}set keysRotateRight(e){const t=this.inputs.attached.keyboard;t&&(t.keysRotateRight=e)}get keysRotateUp(){const e=this.inputs.attached.keyboard;return e?e.keysRotateUp:[]}set keysRotateUp(e){const t=this.inputs.attached.keyboard;t&&(t.keysRotateUp=e)}get keysRotateDown(){const e=this.inputs.attached.keyboard;return e?e.keysRotateDown:[]}set keysRotateDown(e){const t=this.inputs.attached.keyboard;t&&(t.keysRotateDown=e)}constructor(e,t,s,n=!0){super(e,t,s,n),this.ellipsoid=new A(.5,1,.5),this.ellipsoidOffset=new A(0,0,0),this.checkCollisions=!1,this.applyGravity=!1,this._needMoveForGravity=!1,this._oldPosition=A.Zero(),this._diffPosition=A.Zero(),this._newPosition=A.Zero(),this._collisionMask=-1,this._onCollisionPositionChange=(i,r,o=null)=>{this._newPosition.copyFrom(r),this._newPosition.subtractToRef(this._oldPosition,this._diffPosition),this._diffPosition.length()>Te.CollisionsEpsilon&&(this.position.addToRef(this._diffPosition,this._deferredPositionUpdate),this._deferOnly?this._deferredUpdated=!0:this.position.copyFrom(this._deferredPositionUpdate),this.onCollide&&o&&this.onCollide(o))},this.inputs=new He(this),this.inputs.addKeyboard().addMouse()}attachControl(e,t){t=R.BackCompatCameraNoPreventDefault(arguments),this.inputs.attachElement(t)}detachControl(){this.inputs.detachElement(),this.cameraDirection=new A(0,0,0),this.cameraRotation=new Pe(0,0)}get collisionMask(){return this._collisionMask}set collisionMask(e){this._collisionMask=isNaN(e)?-1:e}_collideWithWorld(e){let t;this.parent?t=A.TransformCoordinates(this.position,this.parent.getWorldMatrix()):t=this.position,t.subtractFromFloatsToRef(0,this.ellipsoid.y,0,this._oldPosition),this._oldPosition.addInPlace(this.ellipsoidOffset);const s=this.getScene().collisionCoordinator;this._collider||(this._collider=s.createCollider()),this._collider._radius=this.ellipsoid,this._collider.collisionMask=this._collisionMask;let n=e;this.applyGravity&&(n=e.add(this.getScene().gravity)),s.getNewPosition(this._oldPosition,n,this._collider,3,null,this._onCollisionPositionChange,this.uniqueId)}_checkInputs(){this._localDirection||(this._localDirection=A.Zero(),this._transformedDirection=A.Zero()),this.inputs.checkInputs(),super._checkInputs()}set needMoveForGravity(e){this._needMoveForGravity=e}get needMoveForGravity(){return this._needMoveForGravity}_decideIfNeedsToMove(){return this._needMoveForGravity||Math.abs(this.cameraDirection.x)>0||Math.abs(this.cameraDirection.y)>0||Math.abs(this.cameraDirection.z)>0}_updatePosition(){this.checkCollisions&&this.getScene().collisionsEnabled?this._collideWithWorld(this.cameraDirection):super._updatePosition()}dispose(){this.inputs.clear(),super.dispose()}getClassName(){return"FreeCamera"}}y([ge()],X.prototype,"ellipsoid",void 0);y([ge()],X.prototype,"ellipsoidOffset",void 0);y([b()],X.prototype,"checkCollisions",void 0);y([b()],X.prototype,"applyGravity",void 0);Ce("BABYLON.FreeCamera",X);class ee{get useTextureToStoreBoneMatrices(){return this._useTextureToStoreBoneMatrices}set useTextureToStoreBoneMatrices(e){this._useTextureToStoreBoneMatrices=e,this._markAsDirty()}get animationPropertiesOverride(){return this._animationPropertiesOverride?this._animationPropertiesOverride:this._scene.animationPropertiesOverride}set animationPropertiesOverride(e){this._animationPropertiesOverride=e}get isUsingTextureForMatrices(){return this.useTextureToStoreBoneMatrices&&this._canUseTextureForBones}get uniqueId(){return this._uniqueId}constructor(e,t,s){this.name=e,this.id=t,this.bones=[],this.needInitialSkinMatrix=!1,this._isDirty=!0,this._meshesWithPoseMatrix=new Array,this._identity=B.Identity(),this._currentRenderId=-1,this._ranges={},this._absoluteTransformIsDirty=!0,this._canUseTextureForBones=!1,this._uniqueId=0,this._numBonesWithLinkedTransformNode=0,this._hasWaitingData=null,this._parentContainer=null,this.doNotSerialize=!1,this._useTextureToStoreBoneMatrices=!0,this._animationPropertiesOverride=null,this.onBeforeComputeObservable=new T,this.bones=[],this._scene=s||ae.LastCreatedScene,this._uniqueId=this._scene.getUniqueId(),this._scene.addSkeleton(this),this._isDirty=!0;const n=this._scene.getEngine().getCaps();this._canUseTextureForBones=n.textureFloat&&n.maxVertexTextureImageUnits>0}getClassName(){return"Skeleton"}getChildren(){return this.bones.filter(e=>!e.getParent())}getTransformMatrices(e){if(this.needInitialSkinMatrix){if(!e)throw new Error("getTransformMatrices: When using the needInitialSkinMatrix flag, a mesh must be provided");return e._bonesTransformMatrices||this.prepare(!0),e._bonesTransformMatrices}return(!this._transformMatrices||this._isDirty)&&this.prepare(!this._transformMatrices),this._transformMatrices}getTransformMatrixTexture(e){return this.needInitialSkinMatrix&&e._transformMatrixTexture?e._transformMatrixTexture:this._transformMatrixTexture}getScene(){return this._scene}toString(e){let t=`Name: ${this.name}, nBones: ${this.bones.length}`;if(t+=`, nAnimationRanges: ${this._ranges?Object.keys(this._ranges).length:"none"}`,e){t+=", Ranges: {";let s=!0;for(const n in this._ranges)s&&(t+=", ",s=!1),t+=n;t+="}"}return t}getBoneIndexByName(e){for(let t=0,s=this.bones.length;t<s;t++)if(this.bones[t].name===e)return t;return-1}createAnimationRange(e,t,s){if(!this._ranges[e]){this._ranges[e]=new ue(e,t,s);for(let n=0,i=this.bones.length;n<i;n++)this.bones[n].animations[0]&&this.bones[n].animations[0].createRange(e,t,s)}}deleteAnimationRange(e,t=!0){for(let s=0,n=this.bones.length;s<n;s++)this.bones[s].animations[0]&&this.bones[s].animations[0].deleteRange(e,t);this._ranges[e]=null}getAnimationRange(e){return this._ranges[e]||null}getAnimationRanges(){const e=[];let t;for(t in this._ranges)e.push(this._ranges[t]);return e}copyAnimationRange(e,t,s=!1){if(this._ranges[t]||!e.getAnimationRange(t))return!1;let n=!0;const i=this._getHighestAnimationFrame()+1,r={},o=e.bones;let a,l;for(l=0,a=o.length;l<a;l++)r[o[l].name]=o[l];this.bones.length!==o.length&&(w.Warn(`copyAnimationRange: this rig has ${this.bones.length} bones, while source as ${o.length}`),n=!1);const h=s&&this.dimensionsAtRest&&e.dimensionsAtRest?this.dimensionsAtRest.divide(e.dimensionsAtRest):null;for(l=0,a=this.bones.length;l<a;l++){const c=this.bones[l].name,p=r[c];p?n=n&&this.bones[l].copyAnimationRange(p,t,i,s,h):(w.Warn("copyAnimationRange: not same rig, missing source bone "+c),n=!1)}const u=e.getAnimationRange(t);return u&&(this._ranges[t]=new ue(t,u.from+i,u.to+i)),n}returnToRest(){for(const e of this.bones)e._index!==-1&&e.returnToRest()}_getHighestAnimationFrame(){let e=0;for(let t=0,s=this.bones.length;t<s;t++)if(this.bones[t].animations[0]){const n=this.bones[t].animations[0].getHighestFrame();e<n&&(e=n)}return e}beginAnimation(e,t,s,n){const i=this.getAnimationRange(e);return i?this._scene.beginAnimation(this,i.from,i.to,t,s,n):null}static MakeAnimationAdditive(e,t=0,s){const n=e.getAnimationRange(s);if(!n)return null;const i=e._scene.getAllAnimatablesByTarget(e);let r=null;for(let a=0;a<i.length;a++){const l=i[a];if(l.fromFrame===n?.from&&l.toFrame===n?.to){r=l;break}}const o=e.getAnimatables();for(let a=0;a<o.length;a++){const h=o[a].animations;if(h)for(let u=0;u<h.length;u++)ce.MakeAnimationAdditive(h[u],t,s)}return r&&(r.isAdditive=!0),e}_markAsDirty(){this._isDirty=!0,this._absoluteTransformIsDirty=!0}_registerMeshWithPoseMatrix(e){this._meshesWithPoseMatrix.push(e)}_unregisterMeshWithPoseMatrix(e){const t=this._meshesWithPoseMatrix.indexOf(e);t>-1&&this._meshesWithPoseMatrix.splice(t,1)}_computeTransformMatrices(e,t){this.onBeforeComputeObservable.notifyObservers(this);for(let s=0;s<this.bones.length;s++){const n=this.bones[s];n._childUpdateId++;const i=n.getParent();if(i?n.getLocalMatrix().multiplyToRef(i.getFinalMatrix(),n.getFinalMatrix()):t?n.getLocalMatrix().multiplyToRef(t,n.getFinalMatrix()):n.getFinalMatrix().copyFrom(n.getLocalMatrix()),n._index!==-1){const r=n._index===null?s:n._index;n.getAbsoluteInverseBindMatrix().multiplyToArray(n.getFinalMatrix(),e,r*16)}}this._identity.copyToArray(e,this.bones.length*16)}prepare(e=!1){if(!e){const t=this.getScene().getRenderId();if(this._currentRenderId===t)return;this._currentRenderId=t}if(this._numBonesWithLinkedTransformNode>0){for(const t of this.bones)if(t._linkedTransformNode){const s=t._linkedTransformNode;t.position=s.position,s.rotationQuaternion?t.rotationQuaternion=s.rotationQuaternion:t.rotation=s.rotation,t.scaling=s.scaling}}if(this.needInitialSkinMatrix)for(const t of this._meshesWithPoseMatrix){const s=t.getPoseMatrix();let n=this._isDirty;if((!t._bonesTransformMatrices||t._bonesTransformMatrices.length!==16*(this.bones.length+1))&&(t._bonesTransformMatrices=new Float32Array(16*(this.bones.length+1)),n=!0),!!n){if(this._synchronizedWithMesh!==t){this._synchronizedWithMesh=t;for(const i of this.bones)i.getParent()||(i.getBindMatrix().multiplyToRef(s,j.Matrix[1]),i._updateAbsoluteBindMatrices(j.Matrix[1]));if(this.isUsingTextureForMatrices){const i=(this.bones.length+1)*4;(!t._transformMatrixTexture||t._transformMatrixTexture.getSize().width!==i)&&(t._transformMatrixTexture&&t._transformMatrixTexture.dispose(),t._transformMatrixTexture=pe.CreateRGBATexture(t._bonesTransformMatrices,(this.bones.length+1)*4,1,this._scene,!1,!1,1,1))}}this._computeTransformMatrices(t._bonesTransformMatrices,s),this.isUsingTextureForMatrices&&t._transformMatrixTexture&&t._transformMatrixTexture.update(t._bonesTransformMatrices)}}else{if(!this._isDirty)return;(!this._transformMatrices||this._transformMatrices.length!==16*(this.bones.length+1))&&(this._transformMatrices=new Float32Array(16*(this.bones.length+1)),this.isUsingTextureForMatrices&&(this._transformMatrixTexture&&this._transformMatrixTexture.dispose(),this._transformMatrixTexture=pe.CreateRGBATexture(this._transformMatrices,(this.bones.length+1)*4,1,this._scene,!1,!1,1,1))),this._computeTransformMatrices(this._transformMatrices,null),this.isUsingTextureForMatrices&&this._transformMatrixTexture&&this._transformMatrixTexture.update(this._transformMatrices)}this._isDirty=!1}getAnimatables(){if(!this._animatables||this._animatables.length!==this.bones.length){this._animatables=[];for(let e=0;e<this.bones.length;e++)this._animatables.push(this.bones[e])}return this._animatables}clone(e,t){const s=new ee(e,t||e,this._scene);s.needInitialSkinMatrix=this.needInitialSkinMatrix;for(let n=0;n<this.bones.length;n++){const i=this.bones[n];let r=null;const o=i.getParent();if(o){const l=this.bones.indexOf(o);r=s.bones[l]}const a=new re(i.name,s,r,i.getBindMatrix().clone(),i.getRestMatrix().clone());a._index=i._index,i._linkedTransformNode&&a.linkTransformNode(i._linkedTransformNode),Se.DeepCopy(i.animations,a.animations)}if(this._ranges){s._ranges={};for(const n in this._ranges){const i=this._ranges[n];i&&(s._ranges[n]=i.clone())}}return this._isDirty=!0,s.prepare(!0),s}enableBlending(e=.01){this.bones.forEach(t=>{t.animations.forEach(s=>{s.enableBlending=!0,s.blendingSpeed=e})})}dispose(){if(this._meshesWithPoseMatrix.length=0,this.getScene().stopAnimation(this),this.getScene().removeSkeleton(this),this._parentContainer){const e=this._parentContainer.skeletons.indexOf(this);e>-1&&this._parentContainer.skeletons.splice(e,1),this._parentContainer=null}this._transformMatrixTexture&&(this._transformMatrixTexture.dispose(),this._transformMatrixTexture=null)}serialize(){const e={};e.name=this.name,e.id=this.id,this.dimensionsAtRest&&(e.dimensionsAtRest=this.dimensionsAtRest.asArray()),e.bones=[],e.needInitialSkinMatrix=this.needInitialSkinMatrix;for(let t=0;t<this.bones.length;t++){const s=this.bones[t],n=s.getParent(),i={parentBoneIndex:n?this.bones.indexOf(n):-1,index:s.getIndex(),name:s.name,id:s.id,matrix:s.getBindMatrix().asArray(),rest:s.getRestMatrix().asArray(),linkedTransformNodeId:s.getTransformNode()?.id};e.bones.push(i),s.length&&(i.length=s.length),s.metadata&&(i.metadata=s.metadata),s.animations&&s.animations.length>0&&(i.animation=s.animations[0].serialize()),e.ranges=[];for(const r in this._ranges){const o=this._ranges[r];if(!o)continue;const a={};a.name=r,a.from=o.from,a.to=o.to,e.ranges.push(a)}}return e}static Parse(e,t){const s=new ee(e.name,e.id,t);e.dimensionsAtRest&&(s.dimensionsAtRest=A.FromArray(e.dimensionsAtRest)),s.needInitialSkinMatrix=e.needInitialSkinMatrix;let n;for(n=0;n<e.bones.length;n++){const i=e.bones[n],r=e.bones[n].index;let o=null;i.parentBoneIndex>-1&&(o=s.bones[i.parentBoneIndex]);const a=i.rest?B.FromArray(i.rest):null,l=new re(i.name,s,o,B.FromArray(i.matrix),a,null,r);i.id!==void 0&&i.id!==null&&(l.id=i.id),i.length&&(l.length=i.length),i.metadata&&(l.metadata=i.metadata),i.animation&&l.animations.push(ce.Parse(i.animation)),i.linkedTransformNodeId!==void 0&&i.linkedTransformNodeId!==null&&(s._hasWaitingData=!0,l._waitingTransformNodeId=i.linkedTransformNodeId)}if(e.ranges)for(n=0;n<e.ranges.length;n++){const i=e.ranges[n];s.createAnimationRange(i.name,i.from,i.to)}return s}computeAbsoluteMatrices(e=!1){(this._absoluteTransformIsDirty||e)&&(this.bones[0].computeAbsoluteMatrices(),this._absoluteTransformIsDirty=!1)}computeAbsoluteTransforms(e=!1){this.computeAbsoluteMatrices(e)}getPoseMatrix(){let e=null;return this._meshesWithPoseMatrix.length>0&&(e=this._meshesWithPoseMatrix[0].getPoseMatrix()),e}sortBones(){const e=[],t=new Array(this.bones.length);for(let s=0;s<this.bones.length;s++)this._sortBones(s,e,t);this.bones=e}_sortBones(e,t,s){if(s[e])return;s[e]=!0;const n=this.bones[e];if(!n)return;n._index===void 0&&(n._index=e);const i=n.getParent();i&&this._sortBones(this.bones.indexOf(i),t,s),t.push(n)}setCurrentPoseAsRest(){this.bones.forEach(e=>{e.setCurrentPoseAsRest()})}}class G{get influence(){return this._influence}set influence(e){if(this._influence===e)return;const t=this._influence;this._influence=e,this.onInfluenceChanged.hasObservers()&&this.onInfluenceChanged.notifyObservers(t===0||e===0)}get animationPropertiesOverride(){return!this._animationPropertiesOverride&&this._scene?this._scene.animationPropertiesOverride:this._animationPropertiesOverride}set animationPropertiesOverride(e){this._animationPropertiesOverride=e}constructor(e,t=0,s=null){this.name=e,this.animations=[],this._positions=null,this._normals=null,this._tangents=null,this._uvs=null,this._uv2s=null,this._colors=null,this._uniqueId=0,this.onInfluenceChanged=new T,this._onDataLayoutChanged=new T,this._animationPropertiesOverride=null,this.id=e,this._scene=s||ae.LastCreatedScene,this.influence=t,this._scene&&(this._uniqueId=this._scene.getUniqueId())}get uniqueId(){return this._uniqueId}get hasPositions(){return!!this._positions}get hasNormals(){return!!this._normals}get hasTangents(){return!!this._tangents}get hasUVs(){return!!this._uvs}get hasUV2s(){return!!this._uv2s}get hasColors(){return!!this._colors}get vertexCount(){return this._positions?this._positions.length/3:this._normals?this._normals.length/3:this._tangents?this._tangents.length/3:this._uvs?this._uvs.length/2:this._uv2s?this._uv2s.length/2:this._colors?this._colors.length/4:0}setPositions(e){const t=this.hasPositions;this._positions=e,t!==this.hasPositions&&this._onDataLayoutChanged.notifyObservers(void 0)}getPositions(){return this._positions}setNormals(e){const t=this.hasNormals;this._normals=e,t!==this.hasNormals&&this._onDataLayoutChanged.notifyObservers(void 0)}getNormals(){return this._normals}setTangents(e){const t=this.hasTangents;this._tangents=e,t!==this.hasTangents&&this._onDataLayoutChanged.notifyObservers(void 0)}getTangents(){return this._tangents}setUVs(e){const t=this.hasUVs;this._uvs=e,t!==this.hasUVs&&this._onDataLayoutChanged.notifyObservers(void 0)}getUVs(){return this._uvs}setUV2s(e){const t=this.hasUV2s;this._uv2s=e,t!==this.hasUV2s&&this._onDataLayoutChanged.notifyObservers(void 0)}getUV2s(){return this._uv2s}setColors(e){const t=this.hasColors;this._colors=e,t!==this.hasColors&&this._onDataLayoutChanged.notifyObservers(void 0)}getColors(){return this._colors}clone(){const e=de.Clone(()=>new G(this.name,this.influence,this._scene),this);return e._positions=this._positions,e._normals=this._normals,e._tangents=this._tangents,e._uvs=this._uvs,e._uv2s=this._uv2s,e._colors=this._colors,e}serialize(){const e={};return e.name=this.name,e.influence=this.influence,e.positions=Array.prototype.slice.call(this.getPositions()),this.id!=null&&(e.id=this.id),this.hasNormals&&(e.normals=Array.prototype.slice.call(this.getNormals())),this.hasTangents&&(e.tangents=Array.prototype.slice.call(this.getTangents())),this.hasUVs&&(e.uvs=Array.prototype.slice.call(this.getUVs())),this.hasUV2s&&(e.uv2s=Array.prototype.slice.call(this.getUV2s())),this.hasColors&&(e.colors=Array.prototype.slice.call(this.getColors())),de.AppendSerializedAnimations(this,e),e}getClassName(){return"MorphTarget"}static Parse(e,t){const s=new G(e.name,e.influence);if(s.setPositions(e.positions),e.id!=null&&(s.id=e.id),e.normals&&s.setNormals(e.normals),e.tangents&&s.setTangents(e.tangents),e.uvs&&s.setUVs(e.uvs),e.uv2s&&s.setUV2s(e.uv2s),e.colors&&s.setColors(e.colors),e.animations){for(let n=0;n<e.animations.length;n++){const i=e.animations[n],r=Oe("BABYLON.Animation");r&&s.animations.push(r.Parse(i))}e.autoAnimate&&t&&t.beginAnimation(s,e.autoAnimateFrom,e.autoAnimateTo,e.autoAnimateLoop,e.autoAnimateSpeed||1)}return s}static FromMesh(e,t,s){t||(t=e.name);const n=new G(t,s,e.getScene());return n.setPositions(e.getVerticesData(m.PositionKind)),e.isVerticesDataPresent(m.NormalKind)&&n.setNormals(e.getVerticesData(m.NormalKind)),e.isVerticesDataPresent(m.TangentKind)&&n.setTangents(e.getVerticesData(m.TangentKind)),e.isVerticesDataPresent(m.UVKind)&&n.setUVs(e.getVerticesData(m.UVKind)),e.isVerticesDataPresent(m.UV2Kind)&&n.setUV2s(e.getVerticesData(m.UV2Kind)),e.isVerticesDataPresent(m.ColorKind)&&n.setColors(e.getVerticesData(m.ColorKind)),n}}y([b()],G.prototype,"id",void 0);class le extends M{get depth(){return this._depth}constructor(e,t,s,n,i,r,o=!0,a=!1,l=M.TRILINEAR_SAMPLINGMODE,h=0,u){super(null,r,!o,a),this.format=i,this._texture=r.getEngine().createRawTexture2DArray(e,t,s,n,i,o,a,l,null,h,u),this._depth=n,this.is2DArray=!0}update(e){this._texture&&this._getEngine().updateRawTexture2DArray(this._texture,e,this._texture.format,this._texture.invertY,null,this._texture.type)}static CreateRGBATexture(e,t,s,n,i,r=!0,o=!1,a=3,l=0){return new le(e,t,s,n,5,i,r,o,a,l)}}class V{set areUpdatesFrozen(e){e?this._blockCounter++:(this._blockCounter--,this._blockCounter<=0&&(this._blockCounter=0,this._syncActiveTargets(this._forceUpdateWhenUnfrozen),this._forceUpdateWhenUnfrozen=!1))}get areUpdatesFrozen(){return this._blockCounter>0}constructor(e=null){if(this._targets=new Array,this._targetInfluenceChangedObservers=new Array,this._targetDataLayoutChangedObservers=new Array,this._activeTargets=new Re(16),this._supportsPositions=!1,this._supportsNormals=!1,this._supportsTangents=!1,this._supportsUVs=!1,this._supportsUV2s=!1,this._supportsColors=!1,this._vertexCount=0,this._uniqueId=0,this._tempInfluences=new Array,this._canUseTextureForTargets=!1,this._blockCounter=0,this._mustSynchronize=!0,this._forceUpdateWhenUnfrozen=!1,this._textureVertexStride=0,this._textureWidth=0,this._textureHeight=1,this._parentContainer=null,this.optimizeInfluencers=!0,this.enablePositionMorphing=!0,this.enableNormalMorphing=!0,this.enableTangentMorphing=!0,this.enableUVMorphing=!0,this.enableUV2Morphing=!0,this.enableColorMorphing=!0,this._numMaxInfluencers=0,this._useTextureToStoreTargets=!0,e||(e=ae.LastCreatedScene),this._scene=e,this._scene){this._scene.addMorphTargetManager(this),this._uniqueId=this._scene.getUniqueId();const t=this._scene.getEngine().getCaps();this._canUseTextureForTargets=t.canUseGLVertexID&&t.textureFloat&&t.maxVertexTextureImageUnits>0&&t.texture2DArrayMaxLayerCount>1}}get numMaxInfluencers(){return this._numMaxInfluencers}set numMaxInfluencers(e){this._numMaxInfluencers!==e&&(this._numMaxInfluencers=e,this._mustSynchronize=!0,this._syncActiveTargets())}get uniqueId(){return this._uniqueId}get vertexCount(){return this._vertexCount}get supportsPositions(){return this._supportsPositions&&this.enablePositionMorphing}get supportsNormals(){return this._supportsNormals&&this.enableNormalMorphing}get supportsTangents(){return this._supportsTangents&&this.enableTangentMorphing}get supportsUVs(){return this._supportsUVs&&this.enableUVMorphing}get supportsUV2s(){return this._supportsUV2s&&this.enableUV2Morphing}get supportsColors(){return this._supportsColors&&this.enableColorMorphing}get hasPositions(){return this._supportsPositions}get hasNormals(){return this._supportsNormals}get hasTangents(){return this._supportsTangents}get hasUVs(){return this._supportsUVs}get hasUV2s(){return this._supportsUV2s}get hasColors(){return this._supportsColors}get numTargets(){return this._targets.length}get numInfluencers(){return this._activeTargets.length}get influences(){return this._influences}get useTextureToStoreTargets(){return this._useTextureToStoreTargets}set useTextureToStoreTargets(e){this._useTextureToStoreTargets!==e&&(this._useTextureToStoreTargets=e,this._mustSynchronize=!0,this._syncActiveTargets())}get isUsingTextureForTargets(){return V.EnableTextureStorage&&this.useTextureToStoreTargets&&this._canUseTextureForTargets&&!this._scene?.getEngine().getCaps().disableMorphTargetTexture}getActiveTarget(e){return this._activeTargets.data[e]}getTarget(e){return this._targets[e]}getTargetByName(e){for(const t of this._targets)if(t.name===e)return t;return null}addTarget(e){this._targets.push(e),this._targetInfluenceChangedObservers.push(e.onInfluenceChanged.add(t=>{this.areUpdatesFrozen&&t&&(this._forceUpdateWhenUnfrozen=!0),this._syncActiveTargets(t)})),this._targetDataLayoutChangedObservers.push(e._onDataLayoutChanged.add(()=>{this._mustSynchronize=!0,this._syncActiveTargets()})),this._mustSynchronize=!0,this._syncActiveTargets()}removeTarget(e){const t=this._targets.indexOf(e);t>=0&&(this._targets.splice(t,1),e.onInfluenceChanged.remove(this._targetInfluenceChangedObservers.splice(t,1)[0]),e._onDataLayoutChanged.remove(this._targetDataLayoutChangedObservers.splice(t,1)[0]),this._mustSynchronize=!0,this._syncActiveTargets()),this._scene&&this._scene.stopAnimation(e)}_bind(e){e.setFloat3("morphTargetTextureInfo",this._textureVertexStride,this._textureWidth,this._textureHeight),e.setFloatArray("morphTargetTextureIndices",this._morphTargetTextureIndices),e.setTexture("morphTargets",this._targetStoreTexture),e.setInt("morphTargetCount",this.numInfluencers)}clone(){const e=new V(this._scene);for(const t of this._targets)e.addTarget(t.clone());return e.enablePositionMorphing=this.enablePositionMorphing,e.enableNormalMorphing=this.enableNormalMorphing,e.enableTangentMorphing=this.enableTangentMorphing,e.enableUVMorphing=this.enableUVMorphing,e.enableUV2Morphing=this.enableUV2Morphing,e.enableColorMorphing=this.enableColorMorphing,e}serialize(){const e={};e.id=this.uniqueId,e.targets=[];for(const t of this._targets)e.targets.push(t.serialize());return e}_syncActiveTargets(e=!1){if(this.areUpdatesFrozen)return;const t=!!this._targetStoreTexture,s=this.isUsingTextureForTargets;(this._mustSynchronize||t!==s)&&(this._mustSynchronize=!1,this.synchronize());let n=0;this._activeTargets.reset(),(!this._morphTargetTextureIndices||this._morphTargetTextureIndices.length!==this._targets.length)&&(this._morphTargetTextureIndices=new Float32Array(this._targets.length));let i=-1;for(const r of this._targets)if(i++,!(r.influence===0&&this.optimizeInfluencers)){if(this._activeTargets.length>=V.MaxActiveMorphTargetsInVertexAttributeMode&&!this.isUsingTextureForTargets)break;this._activeTargets.push(r),this._morphTargetTextureIndices[n]=i,this._tempInfluences[n++]=r.influence}this._morphTargetTextureIndices.length!==n&&(this._morphTargetTextureIndices=this._morphTargetTextureIndices.slice(0,n)),(!this._influences||this._influences.length!==n)&&(this._influences=new Float32Array(n));for(let r=0;r<n;r++)this._influences[r]=this._tempInfluences[r];if(e&&this._scene)for(const r of this._scene.meshes)r.morphTargetManager===this&&(s?r._markSubMeshesAsAttributesDirty():r._syncGeometryWithMorphTargetManager())}synchronize(){if(!this._scene||this.areUpdatesFrozen)return;const e=this._scene.getEngine();this._supportsPositions=!0,this._supportsNormals=!0,this._supportsTangents=!0,this._supportsUVs=!0,this._supportsUV2s=!0,this._supportsColors=!0,this._vertexCount=0,this._targetStoreTexture?.dispose(),this._targetStoreTexture=null,this.isUsingTextureForTargets&&this._targets.length>e.getCaps().texture2DArrayMaxLayerCount&&(this.useTextureToStoreTargets=!1);for(const t of this._targets){this._supportsPositions=this._supportsPositions&&t.hasPositions,this._supportsNormals=this._supportsNormals&&t.hasNormals,this._supportsTangents=this._supportsTangents&&t.hasTangents,this._supportsUVs=this._supportsUVs&&t.hasUVs,this._supportsUV2s=this._supportsUV2s&&t.hasUV2s,this._supportsColors=this._supportsColors&&t.hasColors;const s=t.vertexCount;if(this._vertexCount===0)this._vertexCount=s;else if(this._vertexCount!==s){w.Error(`Incompatible target. Targets must all have the same vertices count. Current vertex count: ${this._vertexCount}, vertex count for target "${t.name}": ${s}`);return}}if(this.isUsingTextureForTargets){this._textureVertexStride=0,this._supportsPositions&&this._textureVertexStride++,this._supportsNormals&&this._textureVertexStride++,this._supportsTangents&&this._textureVertexStride++,this._supportsUVs&&this._textureVertexStride++,this._supportsUV2s&&this._textureVertexStride++,this._supportsColors&&this._textureVertexStride++,this._textureWidth=this._vertexCount*this._textureVertexStride||1,this._textureHeight=1;const t=e.getCaps().maxTextureSize;this._textureWidth>t&&(this._textureHeight=Math.ceil(this._textureWidth/t),this._textureWidth=t);const s=this._targets.length,n=new Float32Array(s*this._textureWidth*this._textureHeight*4);let i=0;for(let r=0;r<s;r++){const o=this._targets[r],a=o.getPositions(),l=o.getNormals(),h=o.getUVs(),u=o.getTangents(),c=o.getUV2s(),p=o.getColors();i=r*this._textureWidth*this._textureHeight*4;for(let d=0;d<this._vertexCount;d++)this._supportsPositions&&a&&(n[i]=a[d*3],n[i+1]=a[d*3+1],n[i+2]=a[d*3+2],i+=4),this._supportsNormals&&l&&(n[i]=l[d*3],n[i+1]=l[d*3+1],n[i+2]=l[d*3+2],i+=4),this._supportsUVs&&h&&(n[i]=h[d*2],n[i+1]=h[d*2+1],i+=4),this._supportsTangents&&u&&(n[i]=u[d*3],n[i+1]=u[d*3+1],n[i+2]=u[d*3+2],i+=4),this._supportsUV2s&&c&&(n[i]=c[d*2],n[i+1]=c[d*2+1],i+=4),this._supportsColors&&p&&(n[i]=p[d*4],n[i+1]=p[d*4+1],n[i+2]=p[d*4+2],n[i+3]=p[d*4+3],i+=4)}this._targetStoreTexture=le.CreateRGBATexture(n,this._textureWidth,this._textureHeight,s,this._scene,!1,!1,1,1),this._targetStoreTexture.name=`Morph texture_${this.uniqueId}`}for(const t of this._scene.meshes)t.morphTargetManager===this&&t._syncGeometryWithMorphTargetManager()}dispose(){if(this._targetStoreTexture&&this._targetStoreTexture.dispose(),this._targetStoreTexture=null,this._scene){if(this._scene.removeMorphTargetManager(this),this._parentContainer){const e=this._parentContainer.morphTargetManagers.indexOf(this);e>-1&&this._parentContainer.morphTargetManagers.splice(e,1),this._parentContainer=null}for(const e of this._targets)this._scene.stopAnimation(e)}}static Parse(e,t){const s=new V(t);for(const n of e.targets)s.addTarget(G.Parse(n,t));return s}}V.EnableTextureStorage=!0;V.MaxActiveMorphTargetsInVertexAttributeMode=8;class Q{constructor(e){this.byteOffset=0,this.buffer=e}loadAsync(e){return this.buffer.readAsync(this.byteOffset,e).then(t=>{this._dataView=new DataView(t.buffer,t.byteOffset,t.byteLength),this._dataByteOffset=0})}readUint32(){const e=this._dataView.getUint32(this._dataByteOffset,!0);return this._dataByteOffset+=4,this.byteOffset+=4,e}readUint8Array(e){const t=new Uint8Array(this._dataView.buffer,this._dataView.byteOffset+this._dataByteOffset,e);return this._dataByteOffset+=e,this.byteOffset+=e,t}readString(e){return ke(this.readUint8Array(e))}skipBytes(e){this._dataByteOffset+=e,this.byteOffset+=e}}function oe(f,e,t,s){const n={externalResourceFunction:s};return t&&(n.uri=e==="file:"?t:e+t),ArrayBuffer.isView(f)?GLTFValidator.validateBytes(f,n):GLTFValidator.validateString(f,n)}function Je(){const f=[];onmessage=e=>{const t=e.data;switch(t.id){case"init":{importScripts(t.url);break}case"validate":{oe(t.data,t.rootUrl,t.fileName,s=>new Promise((n,i)=>{const r=f.length;f.push({resolve:n,reject:i}),postMessage({id:"getExternalResource",index:r,uri:s})})).then(s=>{postMessage({id:"validate.resolve",value:s})},s=>{postMessage({id:"validate.reject",reason:s})});break}case"getExternalResource.resolve":{f[t.index].resolve(t.value);break}case"getExternalResource.reject":{f[t.index].reject(t.reason);break}}}}class be{static ValidateAsync(e,t,s,n){return typeof Worker=="function"?new Promise((i,r)=>{const o=`${oe}(${Je})()`,a=URL.createObjectURL(new Blob([o],{type:"application/javascript"})),l=new Worker(a),h=c=>{l.removeEventListener("error",h),l.removeEventListener("message",u),r(c)},u=c=>{const p=c.data;switch(p.id){case"getExternalResource":{n(p.uri).then(d=>{l.postMessage({id:"getExternalResource.resolve",index:p.index,value:d},[d.buffer])},d=>{l.postMessage({id:"getExternalResource.reject",index:p.index,reason:d})});break}case"validate.resolve":{l.removeEventListener("error",h),l.removeEventListener("message",u),i(p.value),l.terminate();break}case"validate.reject":l.removeEventListener("error",h),l.removeEventListener("message",u),r(p.reason),l.terminate()}};if(l.addEventListener("error",h),l.addEventListener("message",u),l.postMessage({id:"init",url:R.GetBabylonScriptURL(this.Configuration.url)}),ArrayBuffer.isView(e)){const c=e.slice();l.postMessage({id:"validate",data:c,rootUrl:t,fileName:s},[c.buffer])}else l.postMessage({id:"validate",data:e,rootUrl:t,fileName:s})}):(this._LoadScriptPromise||(this._LoadScriptPromise=R.LoadBabylonScriptAsync(this.Configuration.url)),this._LoadScriptPromise.then(()=>oe(e,t,s,n)))}}be.Configuration={url:`${R._DefaultCdnUrl}/gltf_validator.js`};function me(f,e,t){try{return Promise.resolve(new Uint8Array(f,e,t))}catch(s){return Promise.reject(s)}}function Qe(f,e,t){try{if(e<0||e>=f.byteLength)throw new RangeError("Offset is out of range.");if(e+t>f.byteLength)throw new RangeError("Length is out of range.");return Promise.resolve(new Uint8Array(f.buffer,f.byteOffset+e,t))}catch(s){return Promise.reject(s)}}var Y;(function(f){f[f.AUTO=0]="AUTO",f[f.FORCE_RIGHT_HANDED=1]="FORCE_RIGHT_HANDED"})(Y||(Y={}));var W;(function(f){f[f.NONE=0]="NONE",f[f.FIRST=1]="FIRST",f[f.ALL=2]="ALL"})(W||(W={}));var k;(function(f){f[f.LOADING=0]="LOADING",f[f.READY=1]="READY",f[f.COMPLETE=2]="COMPLETE"})(k||(k={}));class je{constructor(){this.coordinateSystemMode=Y.AUTO,this.animationStartMode=W.FIRST,this.loadNodeAnimations=!0,this.loadSkins=!0,this.loadMorphTargets=!0,this.compileMaterials=!1,this.useClipPlane=!1,this.compileShadowGenerators=!1,this.transparencyAsCoverage=!1,this.useRangeRequests=!1,this.createInstances=!0,this.alwaysComputeBoundingBox=!1,this.loadAllMaterials=!1,this.loadOnlyMaterials=!1,this.skipMaterials=!1,this.useSRGBBuffers=!0,this.targetFps=60,this.alwaysComputeSkeletonRootNode=!1,this.useGltfTextureNames=!1,this.preprocessUrlAsync=e=>Promise.resolve(e),this.extensionOptions={}}copyFrom(e){e&&(this.onParsed=e.onParsed,this.coordinateSystemMode=e.coordinateSystemMode??this.coordinateSystemMode,this.animationStartMode=e.animationStartMode??this.animationStartMode,this.loadNodeAnimations=e.loadNodeAnimations??this.loadNodeAnimations,this.loadSkins=e.loadSkins??this.loadSkins,this.loadMorphTargets=e.loadMorphTargets??this.loadMorphTargets,this.compileMaterials=e.compileMaterials??this.compileMaterials,this.useClipPlane=e.useClipPlane??this.useClipPlane,this.compileShadowGenerators=e.compileShadowGenerators??this.compileShadowGenerators,this.transparencyAsCoverage=e.transparencyAsCoverage??this.transparencyAsCoverage,this.useRangeRequests=e.useRangeRequests??this.useRangeRequests,this.createInstances=e.createInstances??this.createInstances,this.alwaysComputeBoundingBox=e.alwaysComputeBoundingBox??this.alwaysComputeBoundingBox,this.loadAllMaterials=e.loadAllMaterials??this.loadAllMaterials,this.loadOnlyMaterials=e.loadOnlyMaterials??this.loadOnlyMaterials,this.skipMaterials=e.skipMaterials??this.skipMaterials,this.useSRGBBuffers=e.useSRGBBuffers??this.useSRGBBuffers,this.targetFps=e.targetFps??this.targetFps,this.alwaysComputeSkeletonRootNode=e.alwaysComputeSkeletonRootNode??this.alwaysComputeSkeletonRootNode,this.useGltfTextureNames=e.useGltfTextureNames??this.useGltfTextureNames,this.preprocessUrlAsync=e.preprocessUrlAsync??this.preprocessUrlAsync,this.customRootNode=e.customRootNode,this.onMeshLoaded=e.onMeshLoaded,this.onSkinLoaded=e.onSkinLoaded,this.onTextureLoaded=e.onTextureLoaded,this.onMaterialLoaded=e.onMaterialLoaded,this.onCameraLoaded=e.onCameraLoaded,this.extensionOptions=e.extensionOptions??this.extensionOptions)}}class E extends je{constructor(e){super(),this.onParsedObservable=new T,this.onMeshLoadedObservable=new T,this.onSkinLoadedObservable=new T,this.onTextureLoadedObservable=new T,this.onMaterialLoadedObservable=new T,this.onCameraLoadedObservable=new T,this.onCompleteObservable=new T,this.onErrorObservable=new T,this.onDisposeObservable=new T,this.onExtensionLoadedObservable=new T,this.validate=!1,this.onValidatedObservable=new T,this._loader=null,this._state=null,this._requests=new Array,this.name=K.name,this.extensions=K.extensions,this.onLoaderStateChangedObservable=new T,this._logIndentLevel=0,this._loggingEnabled=!1,this._log=this._logDisabled,this._capturePerformanceCounters=!1,this._startPerformanceCounter=this._startPerformanceCounterDisabled,this._endPerformanceCounter=this._endPerformanceCounterDisabled,this.copyFrom(e)}set onParsed(e){this._onParsedObserver&&this.onParsedObservable.remove(this._onParsedObserver),e&&(this._onParsedObserver=this.onParsedObservable.add(e))}set onMeshLoaded(e){this._onMeshLoadedObserver&&this.onMeshLoadedObservable.remove(this._onMeshLoadedObserver),e&&(this._onMeshLoadedObserver=this.onMeshLoadedObservable.add(e))}set onSkinLoaded(e){this._onSkinLoadedObserver&&this.onSkinLoadedObservable.remove(this._onSkinLoadedObserver),e&&(this._onSkinLoadedObserver=this.onSkinLoadedObservable.add(t=>e(t.node,t.skinnedNode)))}set onTextureLoaded(e){this._onTextureLoadedObserver&&this.onTextureLoadedObservable.remove(this._onTextureLoadedObserver),e&&(this._onTextureLoadedObserver=this.onTextureLoadedObservable.add(e))}set onMaterialLoaded(e){this._onMaterialLoadedObserver&&this.onMaterialLoadedObservable.remove(this._onMaterialLoadedObserver),e&&(this._onMaterialLoadedObserver=this.onMaterialLoadedObservable.add(e))}set onCameraLoaded(e){this._onCameraLoadedObserver&&this.onCameraLoadedObservable.remove(this._onCameraLoadedObserver),e&&(this._onCameraLoadedObserver=this.onCameraLoadedObservable.add(e))}set onComplete(e){this._onCompleteObserver&&this.onCompleteObservable.remove(this._onCompleteObserver),this._onCompleteObserver=this.onCompleteObservable.add(e)}set onError(e){this._onErrorObserver&&this.onErrorObservable.remove(this._onErrorObserver),this._onErrorObserver=this.onErrorObservable.add(e)}set onDispose(e){this._onDisposeObserver&&this.onDisposeObservable.remove(this._onDisposeObserver),this._onDisposeObserver=this.onDisposeObservable.add(e)}set onExtensionLoaded(e){this._onExtensionLoadedObserver&&this.onExtensionLoadedObservable.remove(this._onExtensionLoadedObserver),this._onExtensionLoadedObserver=this.onExtensionLoadedObservable.add(e)}get loggingEnabled(){return this._loggingEnabled}set loggingEnabled(e){this._loggingEnabled!==e&&(this._loggingEnabled=e,this._loggingEnabled?this._log=this._logEnabled:this._log=this._logDisabled)}get capturePerformanceCounters(){return this._capturePerformanceCounters}set capturePerformanceCounters(e){this._capturePerformanceCounters!==e&&(this._capturePerformanceCounters=e,this._capturePerformanceCounters?(this._startPerformanceCounter=this._startPerformanceCounterEnabled,this._endPerformanceCounter=this._endPerformanceCounterEnabled):(this._startPerformanceCounter=this._startPerformanceCounterDisabled,this._endPerformanceCounter=this._endPerformanceCounterDisabled))}set onValidated(e){this._onValidatedObserver&&this.onValidatedObservable.remove(this._onValidatedObserver),this._onValidatedObserver=this.onValidatedObservable.add(e)}dispose(){this._loader&&(this._loader.dispose(),this._loader=null);for(const e of this._requests)e.abort();this._requests.length=0,delete this._progressCallback,this.preprocessUrlAsync=e=>Promise.resolve(e),this.onMeshLoadedObservable.clear(),this.onSkinLoadedObservable.clear(),this.onTextureLoadedObservable.clear(),this.onMaterialLoadedObservable.clear(),this.onCameraLoadedObservable.clear(),this.onCompleteObservable.clear(),this.onExtensionLoadedObservable.clear(),this.onDisposeObservable.notifyObservers(void 0),this.onDisposeObservable.clear()}loadFile(e,t,s,n,i,r,o,a){if(ArrayBuffer.isView(t))return this._loadBinary(e,t,s,n,o,a),null;this._progressCallback=i;const l=t.name||R.GetFilename(t);if(r){if(this.useRangeRequests){this.validate&&w.Warn("glTF validation is not supported when range requests are enabled");const h={abort:()=>{},onCompleteObservable:new T},u={readAsync:(c,p)=>new Promise((d,P)=>{this._loadFile(e,t,C=>{d(new Uint8Array(C))},!0,C=>{P(C)},C=>{C.setRequestHeader("Range",`bytes=${c}-${c+p-1}`)})}),byteLength:0};return this._unpackBinaryAsync(new Q(u)).then(c=>{h.onCompleteObservable.notifyObservers(h),n(c)},o?c=>o(void 0,c):void 0),h}return this._loadFile(e,t,h=>{this._validate(e,new Uint8Array(h,0,h.byteLength),s,l),this._unpackBinaryAsync(new Q({readAsync:(u,c)=>me(h,u,c),byteLength:h.byteLength})).then(u=>{n(u)},o?u=>o(void 0,u):void 0)},!0,o)}else return this._loadFile(e,t,h=>{try{this._validate(e,h,s,l),n({json:this._parseJson(h)})}catch{o&&o()}},!1,o)}_loadBinary(e,t,s,n,i,r){this._validate(e,new Uint8Array(t.buffer,t.byteOffset,t.byteLength),s,r),this._unpackBinaryAsync(new Q({readAsync:(o,a)=>Qe(t,o,a),byteLength:t.byteLength})).then(o=>{n(o)},i?o=>i(void 0,o):void 0)}importMeshAsync(e,t,s,n,i,r){return Promise.resolve().then(()=>(this.onParsedObservable.notifyObservers(s),this.onParsedObservable.clear(),this._log(`Loading ${r||""}`),this._loader=this._getLoader(s),this._loader.importMeshAsync(e,t,null,s,n,i,r)))}loadAsync(e,t,s,n,i){return Promise.resolve().then(()=>(this.onParsedObservable.notifyObservers(t),this.onParsedObservable.clear(),this._log(`Loading ${i||""}`),this._loader=this._getLoader(t),this._loader.loadAsync(e,t,s,n,i)))}loadAssetContainerAsync(e,t,s,n,i){return Promise.resolve().then(()=>{this.onParsedObservable.notifyObservers(t),this.onParsedObservable.clear(),this._log(`Loading ${i||""}`),this._loader=this._getLoader(t);const r=new ze(e),o=[];this.onMaterialLoadedObservable.add(u=>{o.push(u)});const a=[];this.onTextureLoadedObservable.add(u=>{a.push(u)});const l=[];this.onCameraLoadedObservable.add(u=>{l.push(u)});const h=[];return this.onMeshLoadedObservable.add(u=>{u.morphTargetManager&&h.push(u.morphTargetManager)}),this._loader.importMeshAsync(null,e,r,t,s,n,i).then(u=>(Array.prototype.push.apply(r.geometries,u.geometries),Array.prototype.push.apply(r.meshes,u.meshes),Array.prototype.push.apply(r.particleSystems,u.particleSystems),Array.prototype.push.apply(r.skeletons,u.skeletons),Array.prototype.push.apply(r.animationGroups,u.animationGroups),Array.prototype.push.apply(r.materials,o),Array.prototype.push.apply(r.textures,a),Array.prototype.push.apply(r.lights,u.lights),Array.prototype.push.apply(r.transformNodes,u.transformNodes),Array.prototype.push.apply(r.cameras,l),Array.prototype.push.apply(r.morphTargetManagers,h),r))})}canDirectLoad(e){return K.canDirectLoad(e)}directLoad(e,t){if(t.startsWith("base64,"+q)||t.startsWith(";base64,"+q)||t.startsWith("application/octet-stream;base64,"+q)||t.startsWith("model/gltf-binary;base64,"+q)){const s=ye(t);return this._validate(e,new Uint8Array(s,0,s.byteLength)),this._unpackBinaryAsync(new Q({readAsync:(n,i)=>me(s,n,i),byteLength:s.byteLength}))}return this._validate(e,t),Promise.resolve({json:this._parseJson(t)})}createPlugin(e){return new E(e[K.name])}get loaderState(){return this._state}whenCompleteAsync(){return new Promise((e,t)=>{this.onCompleteObservable.addOnce(()=>{e()}),this.onErrorObservable.addOnce(s=>{t(s)})})}_setState(e){this._state!==e&&(this._state=e,this.onLoaderStateChangedObservable.notifyObservers(this._state),this._log(k[this._state]))}_loadFile(e,t,s,n,i,r){const o=e._loadFile(t,s,a=>{this._onProgress(a,o)},!0,n,i,r);return o.onCompleteObservable.add(()=>{o._lengthComputable=!0,o._total=o._loaded}),this._requests.push(o),o}_onProgress(e,t){if(!this._progressCallback)return;t._lengthComputable=e.lengthComputable,t._loaded=e.loaded,t._total=e.total;let s=!0,n=0,i=0;for(const r of this._requests){if(r._lengthComputable===void 0||r._loaded===void 0||r._total===void 0)return;s=s&&r._lengthComputable,n+=r._loaded,i+=r._total}this._progressCallback({lengthComputable:s,loaded:n,total:s?i:0})}_validate(e,t,s="",n=""){this.validate&&(this._startPerformanceCounter("Validate JSON"),be.ValidateAsync(t,s,n,i=>this.preprocessUrlAsync(s+i).then(r=>e._loadFileAsync(r,void 0,!0,!0).then(o=>new Uint8Array(o,0,o.byteLength)))).then(i=>{this._endPerformanceCounter("Validate JSON"),this.onValidatedObservable.notifyObservers(i),this.onValidatedObservable.clear()},i=>{this._endPerformanceCounter("Validate JSON"),R.Warn(`Failed to validate: ${i.message}`),this.onValidatedObservable.clear()}))}_getLoader(e){const t=e.json.asset||{};this._log(`Asset version: ${t.version}`),t.minVersion&&this._log(`Asset minimum version: ${t.minVersion}`),t.generator&&this._log(`Asset generator: ${t.generator}`);const s=E._parseVersion(t.version);if(!s)throw new Error("Invalid version: "+t.version);if(t.minVersion!==void 0){const r=E._parseVersion(t.minVersion);if(!r)throw new Error("Invalid minimum version: "+t.minVersion);if(E._compareVersion(r,{major:2,minor:0})>0)throw new Error("Incompatible minimum version: "+t.minVersion)}const i={1:E._CreateGLTF1Loader,2:E._CreateGLTF2Loader}[s.major];if(!i)throw new Error("Unsupported version: "+t.version);return i(this)}_parseJson(e){this._startPerformanceCounter("Parse JSON"),this._log(`JSON length: ${e.length}`);const t=JSON.parse(e);return this._endPerformanceCounter("Parse JSON"),t}_unpackBinaryAsync(e){return this._startPerformanceCounter("Unpack Binary"),e.loadAsync(20).then(()=>{const t={Magic:1179937895},s=e.readUint32();if(s!==t.Magic)throw new Ee("Unexpected magic: "+s,Ie.GLTFLoaderUnexpectedMagicError);const n=e.readUint32();this.loggingEnabled&&this._log(`Binary version: ${n}`);const i=e.readUint32();!this.useRangeRequests&&i!==e.buffer.byteLength&&w.Warn(`Length in header does not match actual data length: ${i} != ${e.buffer.byteLength}`);let r;switch(n){case 1:{r=this._unpackBinaryV1Async(e,i);break}case 2:{r=this._unpackBinaryV2Async(e,i);break}default:throw new Error("Unsupported version: "+n)}return this._endPerformanceCounter("Unpack Binary"),r})}_unpackBinaryV1Async(e,t){const s={JSON:0},n=e.readUint32(),i=e.readUint32();if(i!==s.JSON)throw new Error(`Unexpected content format: ${i}`);const r=t-e.byteOffset,o={json:this._parseJson(e.readString(n)),bin:null};if(r!==0){const a=e.byteOffset;o.bin={readAsync:(l,h)=>e.buffer.readAsync(a+l,h),byteLength:r}}return Promise.resolve(o)}_unpackBinaryV2Async(e,t){const s={JSON:1313821514,BIN:5130562},n=e.readUint32();if(e.readUint32()!==s.JSON)throw new Error("First chunk format is not JSON");return e.byteOffset+n===t?e.loadAsync(n).then(()=>({json:this._parseJson(e.readString(n)),bin:null})):e.loadAsync(n+8).then(()=>{const r={json:this._parseJson(e.readString(n)),bin:null},o=()=>{const a=e.readUint32();switch(e.readUint32()){case s.JSON:throw new Error("Unexpected JSON chunk");case s.BIN:{const h=e.byteOffset;r.bin={readAsync:(u,c)=>e.buffer.readAsync(h+u,c),byteLength:a},e.skipBytes(a);break}default:{e.skipBytes(a);break}}return e.byteOffset!==t?e.loadAsync(8).then(o):Promise.resolve(r)};return o()})}static _parseVersion(e){if(e==="1.0"||e==="1.0.1")return{major:1,minor:0};const t=(e+"").match(/^(\d+)\.(\d+)/);return t?{major:parseInt(t[1]),minor:parseInt(t[2])}:null}static _compareVersion(e,t){return e.major>t.major?1:e.major<t.major?-1:e.minor>t.minor?1:e.minor<t.minor?-1:0}_logOpen(e){this._log(e),this._logIndentLevel++}_logClose(){--this._logIndentLevel}_logEnabled(e){const t=E._logSpaces.substring(0,this._logIndentLevel*2);w.Log(`${t}${e}`)}_logDisabled(e){}_startPerformanceCounterEnabled(e){R.StartPerformanceCounter(e)}_startPerformanceCounterDisabled(e){}_endPerformanceCounterEnabled(e){R.EndPerformanceCounter(e)}_endPerformanceCounterDisabled(e){}}E.IncrementalLoading=!0;E.HomogeneousCoordinates=!1;E._logSpaces="                                ";Le(new E);class _{static Get(e,t,s){if(!t||s==null||!t[s])throw new Error(`${e}: Failed to find index (${s})`);return t[s]}static TryGet(e,t){return!e||t==null||!e[t]?null:e[t]}static Assign(e){if(e)for(let t=0;t<e.length;t++)e[t].index=t}}function et(f){if(f.min&&f.max){const e=f.min,t=f.max,s=j.Vector3[0].copyFromFloats(e[0],e[1],e[2]),n=j.Vector3[1].copyFromFloats(t[0],t[1],t[2]);if(f.normalized&&f.componentType!==5126){let i=1;switch(f.componentType){case 5120:i=127;break;case 5121:i=255;break;case 5122:i=32767;break;case 5123:i=65535;break}const r=1/i;s.scaleInPlace(r),n.scaleInPlace(r)}return new De(s,n)}return null}class v{static RegisterExtension(e,t){Ne(e,!1,t)}static UnregisterExtension(e){return Be(e)}get gltf(){if(!this._gltf)throw new Error("glTF JSON is not available");return this._gltf}get bin(){return this._bin}get parent(){return this._parent}get babylonScene(){if(!this._babylonScene)throw new Error("Scene is not available");return this._babylonScene}get rootBabylonMesh(){return this._rootBabylonMesh}get rootUrl(){return this._rootUrl}constructor(e){this._completePromises=new Array,this._assetContainer=null,this._babylonLights=[],this._disableInstancedMesh=0,this._allMaterialsDirtyRequired=!1,this._skipStartAnimationStep=!1,this._extensions=new Array,this._disposed=!1,this._rootUrl=null,this._fileName=null,this._uniqueRootUrl=null,this._bin=null,this._rootBabylonMesh=null,this._defaultBabylonMaterialData={},this._postSceneLoadActions=new Array,this._parent=e}dispose(){this._disposed||(this._disposed=!0,this._completePromises.length=0,this._extensions.forEach(e=>e.dispose&&e.dispose()),this._extensions.length=0,this._gltf=null,this._bin=null,this._babylonScene=null,this._rootBabylonMesh=null,this._defaultBabylonMaterialData={},this._postSceneLoadActions.length=0,this._parent.dispose())}importMeshAsync(e,t,s,n,i,r,o=""){return Promise.resolve().then(()=>{this._babylonScene=t,this._assetContainer=s,this._loadData(n);let a=null;if(e){const l={};if(this._gltf.nodes)for(const u of this._gltf.nodes)u.name&&(l[u.name]=u.index);a=(e instanceof Array?e:[e]).map(u=>{const c=l[u];if(c===void 0)throw new Error(`Failed to find node '${u}'`);return c})}return this._loadAsync(i,o,a,()=>({meshes:this._getMeshes(),particleSystems:[],skeletons:this._getSkeletons(),animationGroups:this._getAnimationGroups(),lights:this._babylonLights,transformNodes:this._getTransformNodes(),geometries:this._getGeometries(),spriteManagers:[]}))})}loadAsync(e,t,s,n,i=""){return Promise.resolve().then(()=>(this._babylonScene=e,this._loadData(t),this._loadAsync(s,i,null,()=>{})))}_loadAsync(e,t,s,n){return Promise.resolve().then(async()=>{this._rootUrl=e,this._uniqueRootUrl=!e.startsWith("file:")&&t?e:`${e}${Date.now()}/`,this._fileName=t,this._allMaterialsDirtyRequired=!1,await this._loadExtensionsAsync();const i=`${k[k.LOADING]} => ${k[k.READY]}`,r=`${k[k.LOADING]} => ${k[k.COMPLETE]}`;this._parent._startPerformanceCounter(i),this._parent._startPerformanceCounter(r),this._parent._setState(k.LOADING),this._extensionsOnLoading();const o=new Array,a=this._babylonScene.blockMaterialDirtyMechanism;if(this._babylonScene.blockMaterialDirtyMechanism=!0,!this.parent.loadOnlyMaterials){if(s)o.push(this.loadSceneAsync("/nodes",{nodes:s,index:-1}));else if(this._gltf.scene!=null||this._gltf.scenes&&this._gltf.scenes[0]){const h=_.Get("/scene",this._gltf.scenes,this._gltf.scene||0);o.push(this.loadSceneAsync(`/scenes/${h.index}`,h))}}if(!this.parent.skipMaterials&&this.parent.loadAllMaterials&&this._gltf.materials)for(let h=0;h<this._gltf.materials.length;++h){const u=this._gltf.materials[h],c="/materials/"+h,p=N.TriangleFillMode;o.push(this._loadMaterialAsync(c,u,null,p,()=>{}))}return this._allMaterialsDirtyRequired?this._babylonScene.blockMaterialDirtyMechanism=a:this._babylonScene._forceBlockMaterialDirtyMechanism(a),this._parent.compileMaterials&&o.push(this._compileMaterialsAsync()),this._parent.compileShadowGenerators&&o.push(this._compileShadowGeneratorsAsync()),Promise.all(o).then(()=>{this._rootBabylonMesh&&this._rootBabylonMesh!==this._parent.customRootNode&&this._rootBabylonMesh.setEnabled(!0);for(const h of this._babylonScene.materials){const u=h;u.maxSimultaneousLights!==void 0&&(u.maxSimultaneousLights=Math.max(u.maxSimultaneousLights,this._babylonScene.lights.length))}return this._extensionsOnReady(),this._parent._setState(k.READY),this._skipStartAnimationStep||this._startAnimations(),n()}).then(h=>(this._parent._endPerformanceCounter(i),R.SetImmediate(()=>{this._disposed||Promise.all(this._completePromises).then(()=>{this._parent._endPerformanceCounter(r),this._parent._setState(k.COMPLETE),this._parent.onCompleteObservable.notifyObservers(void 0),this._parent.onCompleteObservable.clear(),this.dispose()},u=>{this._parent.onErrorObservable.notifyObservers(u),this._parent.onErrorObservable.clear(),this.dispose()})}),h))}).catch(i=>{throw this._disposed||(this._parent.onErrorObservable.notifyObservers(i),this._parent.onErrorObservable.clear(),this.dispose()),i})}_loadData(e){if(this._gltf=e.json,this._setupData(),e.bin){const t=this._gltf.buffers;if(t&&t[0]&&!t[0].uri){const s=t[0];(s.byteLength<e.bin.byteLength-3||s.byteLength>e.bin.byteLength)&&w.Warn(`Binary buffer length (${s.byteLength}) from JSON does not match chunk length (${e.bin.byteLength})`),this._bin=e.bin}else w.Warn("Unexpected BIN chunk")}}_setupData(){if(_.Assign(this._gltf.accessors),_.Assign(this._gltf.animations),_.Assign(this._gltf.buffers),_.Assign(this._gltf.bufferViews),_.Assign(this._gltf.cameras),_.Assign(this._gltf.images),_.Assign(this._gltf.materials),_.Assign(this._gltf.meshes),_.Assign(this._gltf.nodes),_.Assign(this._gltf.samplers),_.Assign(this._gltf.scenes),_.Assign(this._gltf.skins),_.Assign(this._gltf.textures),this._gltf.nodes){const e={};for(const s of this._gltf.nodes)if(s.children)for(const n of s.children)e[n]=s.index;const t=this._createRootNode();for(const s of this._gltf.nodes){const n=e[s.index];s.parent=n===void 0?t:this._gltf.nodes[n]}}}async _loadExtensionsAsync(){const e=[];if($e.forEach((t,s)=>{this.parent.extensionOptions[s]?.enabled===!1?t.isGLTFExtension&&this.isExtensionUsed(s)&&w.Warn(`Extension ${s} is used but has been explicitly disabled.`):(!t.isGLTFExtension||this.isExtensionUsed(s))&&e.push((async()=>{const n=await t.factory(this);return n.name!==s&&w.Warn(`The name of the glTF loader extension instance does not match the registered name: ${n.name} !== ${s}`),this._parent.onExtensionLoadedObservable.notifyObservers(n),n})())}),this._extensions.push(...await Promise.all(e)),this._extensions.sort((t,s)=>(t.order||Number.MAX_VALUE)-(s.order||Number.MAX_VALUE)),this._parent.onExtensionLoadedObservable.clear(),this._gltf.extensionsRequired){for(const t of this._gltf.extensionsRequired)if(!this._extensions.some(n=>n.name===t&&n.enabled))throw this.parent.extensionOptions[t]?.enabled===!1?new Error(`Required extension ${t} is disabled`):new Error(`Required extension ${t} is not available`)}}_createRootNode(){if(this._parent.customRootNode!==void 0)return this._rootBabylonMesh=this._parent.customRootNode,{_babylonTransformNode:this._rootBabylonMesh===null?void 0:this._rootBabylonMesh,index:-1};this._babylonScene._blockEntityCollection=!!this._assetContainer;const e=new fe("__root__",this._babylonScene);this._rootBabylonMesh=e,this._rootBabylonMesh._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,this._rootBabylonMesh.setEnabled(!1);const t={_babylonTransformNode:this._rootBabylonMesh,index:-1};switch(this._parent.coordinateSystemMode){case Y.AUTO:{this._babylonScene.useRightHandedSystem||(t.rotation=[0,1,0,0],t.scale=[1,1,-1],v._LoadTransform(t,this._rootBabylonMesh));break}case Y.FORCE_RIGHT_HANDED:{this._babylonScene.useRightHandedSystem=!0;break}default:throw new Error(`Invalid coordinate system mode (${this._parent.coordinateSystemMode})`)}return this._parent.onMeshLoadedObservable.notifyObservers(e),t}loadSceneAsync(e,t){const s=this._extensionsLoadSceneAsync(e,t);if(s)return s;const n=new Array;if(this.logOpen(`${e} ${t.name||""}`),t.nodes)for(const i of t.nodes){const r=_.Get(`${e}/nodes/${i}`,this._gltf.nodes,i);n.push(this.loadNodeAsync(`/nodes/${r.index}`,r,o=>{o.parent=this._rootBabylonMesh}))}for(const i of this._postSceneLoadActions)i();return n.push(this._loadAnimationsAsync()),this.logClose(),Promise.all(n).then(()=>{})}_forEachPrimitive(e,t){if(e._primitiveBabylonMeshes)for(const s of e._primitiveBabylonMeshes)t(s)}_getGeometries(){const e=[],t=this._gltf.nodes;if(t)for(const s of t)this._forEachPrimitive(s,n=>{const i=n.geometry;i&&e.indexOf(i)===-1&&e.push(i)});return e}_getMeshes(){const e=[];this._rootBabylonMesh instanceof Ve&&e.push(this._rootBabylonMesh);const t=this._gltf.nodes;if(t)for(const s of t)this._forEachPrimitive(s,n=>{e.push(n)});return e}_getTransformNodes(){const e=[],t=this._gltf.nodes;if(t)for(const s of t)s._babylonTransformNode&&s._babylonTransformNode.getClassName()==="TransformNode"&&e.push(s._babylonTransformNode),s._babylonTransformNodeForSkin&&e.push(s._babylonTransformNodeForSkin);return e}_getSkeletons(){const e=[],t=this._gltf.skins;if(t)for(const s of t)s._data&&e.push(s._data.babylonSkeleton);return e}_getAnimationGroups(){const e=[],t=this._gltf.animations;if(t)for(const s of t)s._babylonAnimationGroup&&e.push(s._babylonAnimationGroup);return e}_startAnimations(){switch(this._parent.animationStartMode){case W.NONE:break;case W.FIRST:{const e=this._getAnimationGroups();e.length!==0&&e[0].start(!0);break}case W.ALL:{const e=this._getAnimationGroups();for(const t of e)t.start(!0);break}default:{w.Error(`Invalid animation start mode (${this._parent.animationStartMode})`);return}}}loadNodeAsync(e,t,s=()=>{}){const n=this._extensionsLoadNodeAsync(e,t,s);if(n)return n;if(t._babylonTransformNode)throw new Error(`${e}: Invalid recursive node hierarchy`);const i=new Array;this.logOpen(`${e} ${t.name||""}`);const r=l=>{if(v.AddPointerMetadata(l,e),v._LoadTransform(t,l),t.camera!=null){const h=_.Get(`${e}/camera`,this._gltf.cameras,t.camera);i.push(this.loadCameraAsync(`/cameras/${h.index}`,h,u=>{u.parent=l}))}if(t.children)for(const h of t.children){const u=_.Get(`${e}/children/${h}`,this._gltf.nodes,h);i.push(this.loadNodeAsync(`/nodes/${u.index}`,u,c=>{c.parent=l}))}s(l)},o=t.mesh!=null,a=this._parent.loadSkins&&t.skin!=null;if(!o||a){const l=t.name||`node${t.index}`;this._babylonScene._blockEntityCollection=!!this._assetContainer;const h=new _e(l,this._babylonScene);h._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,t.mesh==null?t._babylonTransformNode=h:t._babylonTransformNodeForSkin=h,r(h)}if(o)if(a){const l=_.Get(`${e}/mesh`,this._gltf.meshes,t.mesh);i.push(this._loadMeshAsync(`/meshes/${l.index}`,t,l,h=>{const u=t._babylonTransformNodeForSkin;h.metadata=Ue(u.metadata,h.metadata||{});const c=_.Get(`${e}/skin`,this._gltf.skins,t.skin);i.push(this._loadSkinAsync(`/skins/${c.index}`,t,c,p=>{this._forEachPrimitive(t,d=>{d.skeleton=p}),this._postSceneLoadActions.push(()=>{if(c.skeleton!=null){const d=_.Get(`/skins/${c.index}/skeleton`,this._gltf.nodes,c.skeleton).parent;t.index===d.index?h.parent=u.parent:h.parent=d._babylonTransformNode}else h.parent=this._rootBabylonMesh;this._parent.onSkinLoadedObservable.notifyObservers({node:u,skinnedNode:h})})}))}))}else{const l=_.Get(`${e}/mesh`,this._gltf.meshes,t.mesh);i.push(this._loadMeshAsync(`/meshes/${l.index}`,t,l,r))}return this.logClose(),Promise.all(i).then(()=>(this._forEachPrimitive(t,l=>{const h=l;!h.isAnInstance&&h.geometry&&h.geometry.useBoundingInfoFromGeometry?l._updateBoundingInfo():l.refreshBoundingInfo(!0,!0)}),t._babylonTransformNode))}_loadMeshAsync(e,t,s,n){const i=s.primitives;if(!i||!i.length)throw new Error(`${e}: Primitives are missing`);i[0].index==null&&_.Assign(i);const r=new Array;this.logOpen(`${e} ${s.name||""}`);const o=t.name||`node${t.index}`;if(i.length===1){const a=s.primitives[0];r.push(this._loadMeshPrimitiveAsync(`${e}/primitives/${a.index}`,o,t,s,a,l=>{t._babylonTransformNode=l,t._primitiveBabylonMeshes=[l]}))}else{this._babylonScene._blockEntityCollection=!!this._assetContainer,t._babylonTransformNode=new _e(o,this._babylonScene),t._babylonTransformNode._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,t._primitiveBabylonMeshes=[];for(const a of i)r.push(this._loadMeshPrimitiveAsync(`${e}/primitives/${a.index}`,`${o}_primitive${a.index}`,t,s,a,l=>{l.parent=t._babylonTransformNode,t._primitiveBabylonMeshes.push(l)}))}return n(t._babylonTransformNode),this.logClose(),Promise.all(r).then(()=>t._babylonTransformNode)}_loadMeshPrimitiveAsync(e,t,s,n,i,r){const o=this._extensionsLoadMeshPrimitiveAsync(e,t,s,n,i,r);if(o)return o;this.logOpen(`${e}`);const a=this._disableInstancedMesh===0&&this._parent.createInstances&&s.skin==null&&!n.primitives[0].targets;let l,h;if(a&&i._instanceData)this._babylonScene._blockEntityCollection=!!this._assetContainer,l=i._instanceData.babylonSourceMesh.createInstance(t),l._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,h=i._instanceData.promise;else{const u=new Array;this._babylonScene._blockEntityCollection=!!this._assetContainer;const c=new fe(t,this._babylonScene);c._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,c.sideOrientation=this._babylonScene.useRightHandedSystem?N.CounterClockWiseSideOrientation:N.ClockWiseSideOrientation,this._createMorphTargets(e,s,n,i,c),u.push(this._loadVertexDataAsync(e,i,c).then(d=>this._loadMorphTargetsAsync(e,i,c,d).then(()=>{this._disposed||(this._babylonScene._blockEntityCollection=!!this._assetContainer,d.applyToMesh(c),d._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1)})));const p=v._GetDrawMode(e,i.mode);if(i.material==null){let d=this._defaultBabylonMaterialData[p];d||(d=this._createDefaultMaterial("__GLTFLoader._default",p),this._parent.onMaterialLoadedObservable.notifyObservers(d),this._defaultBabylonMaterialData[p]=d),c.material=d}else if(!this.parent.skipMaterials){const d=_.Get(`${e}/material`,this._gltf.materials,i.material);u.push(this._loadMaterialAsync(`/materials/${d.index}`,d,c,p,P=>{c.material=P}))}h=Promise.all(u),a&&(i._instanceData={babylonSourceMesh:c,promise:h}),l=c}return v.AddPointerMetadata(l,e),this._parent.onMeshLoadedObservable.notifyObservers(l),r(l),this.logClose(),h.then(()=>l)}_loadVertexDataAsync(e,t,s){const n=this._extensionsLoadVertexDataAsync(e,t,s);if(n)return n;const i=t.attributes;if(!i)throw new Error(`${e}: Attributes are missing`);const r=new Array,o=new Fe(s.name,this._babylonScene);if(t.indices==null)s.isUnIndexed=!0;else{const l=_.Get(`${e}/indices`,this._gltf.accessors,t.indices);r.push(this._loadIndicesAccessorAsync(`/accessors/${l.index}`,l).then(h=>{o.setIndices(h)}))}const a=(l,h,u)=>{if(i[l]==null)return;s._delayInfo=s._delayInfo||[],s._delayInfo.indexOf(h)===-1&&s._delayInfo.push(h);const c=_.Get(`${e}/attributes/${l}`,this._gltf.accessors,i[l]);r.push(this._loadVertexAccessorAsync(`/accessors/${c.index}`,c,h).then(p=>{if(p.getKind()===m.PositionKind&&!this.parent.alwaysComputeBoundingBox&&!s.skeleton){const d=et(c);d&&(o._boundingInfo=d,o.useBoundingInfoFromGeometry=!0)}o.setVerticesBuffer(p,c.count)})),h==m.MatricesIndicesExtraKind&&(s.numBoneInfluencers=8),u&&u(c)};return a("POSITION",m.PositionKind),a("NORMAL",m.NormalKind),a("TANGENT",m.TangentKind),a("TEXCOORD_0",m.UVKind),a("TEXCOORD_1",m.UV2Kind),a("TEXCOORD_2",m.UV3Kind),a("TEXCOORD_3",m.UV4Kind),a("TEXCOORD_4",m.UV5Kind),a("TEXCOORD_5",m.UV6Kind),a("JOINTS_0",m.MatricesIndicesKind),a("WEIGHTS_0",m.MatricesWeightsKind),a("JOINTS_1",m.MatricesIndicesExtraKind),a("WEIGHTS_1",m.MatricesWeightsExtraKind),a("COLOR_0",m.ColorKind,l=>{l.type==="VEC4"&&(s.hasVertexAlpha=!0)}),Promise.all(r).then(()=>o)}_createMorphTargets(e,t,s,n,i){if(!n.targets||!this._parent.loadMorphTargets)return;if(t._numMorphTargets==null)t._numMorphTargets=n.targets.length;else if(n.targets.length!==t._numMorphTargets)throw new Error(`${e}: Primitives do not have the same number of targets`);const r=s.extras?s.extras.targetNames:null;this._babylonScene._blockEntityCollection=!!this._assetContainer,i.morphTargetManager=new V(this._babylonScene),i.morphTargetManager._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,i.morphTargetManager.areUpdatesFrozen=!0;for(let o=0;o<n.targets.length;o++){const a=t.weights?t.weights[o]:s.weights?s.weights[o]:0,l=r?r[o]:`morphTarget${o}`;i.morphTargetManager.addTarget(new G(l,a,i.getScene()))}}_loadMorphTargetsAsync(e,t,s,n){if(!t.targets||!this._parent.loadMorphTargets)return Promise.resolve();const i=new Array,r=s.morphTargetManager;for(let o=0;o<r.numTargets;o++){const a=r.getTarget(o);i.push(this._loadMorphTargetVertexDataAsync(`${e}/targets/${o}`,n,t.targets[o],a))}return Promise.all(i).then(()=>{r.areUpdatesFrozen=!1})}_loadMorphTargetVertexDataAsync(e,t,s,n){const i=new Array,r=(o,a,l)=>{if(s[o]==null)return;const h=t.getVertexBuffer(a);if(!h)return;const u=_.Get(`${e}/${o}`,this._gltf.accessors,s[o]);i.push(this._loadFloatAccessorAsync(`/accessors/${u.index}`,u).then(c=>{l(h,c)}))};return r("POSITION",m.PositionKind,(o,a)=>{const l=new Float32Array(a.length);o.forEach(a.length,(h,u)=>{l[u]=a[u]+h}),n.setPositions(l)}),r("NORMAL",m.NormalKind,(o,a)=>{const l=new Float32Array(a.length);o.forEach(l.length,(h,u)=>{l[u]=a[u]+h}),n.setNormals(l)}),r("TANGENT",m.TangentKind,(o,a)=>{const l=new Float32Array(a.length/3*4);let h=0;o.forEach(a.length/3*4,(u,c)=>{(c+1)%4!==0&&(l[h]=a[h]+u,h++)}),n.setTangents(l)}),r("TEXCOORD_0",m.UVKind,(o,a)=>{const l=new Float32Array(a.length);o.forEach(a.length,(h,u)=>{l[u]=a[u]+h}),n.setUVs(l)}),r("TEXCOORD_1",m.UV2Kind,(o,a)=>{const l=new Float32Array(a.length);o.forEach(a.length,(h,u)=>{l[u]=a[u]+h}),n.setUV2s(l)}),r("COLOR_0",m.ColorKind,(o,a)=>{let l=null;const h=o.getSize();if(h===3){l=new Float32Array(a.length/3*4),o.forEach(a.length,(u,c)=>{const p=Math.floor(c/3),d=c%3;l[4*p+d]=a[3*p+d]+u});for(let u=0;u<a.length/3;++u)l[4*u+3]=1}else if(h===4)l=new Float32Array(a.length),o.forEach(a.length,(u,c)=>{l[c]=a[c]+u});else throw new Error(`${e}: Invalid number of components (${h}) for COLOR_0 attribute`);n.setColors(l)}),Promise.all(i).then(()=>{})}static _LoadTransform(e,t){if(e.skin!=null)return;let s=A.Zero(),n=z.Identity(),i=A.One();e.matrix?B.FromArray(e.matrix).decompose(i,n,s):(e.translation&&(s=A.FromArray(e.translation)),e.rotation&&(n=z.FromArray(e.rotation)),e.scale&&(i=A.FromArray(e.scale))),t.position=s,t.rotationQuaternion=n,t.scaling=i}_loadSkinAsync(e,t,s,n){if(!this._parent.loadSkins)return Promise.resolve();const i=this._extensionsLoadSkinAsync(e,t,s);if(i)return i;if(s._data)return n(s._data.babylonSkeleton),s._data.promise;const r=`skeleton${s.index}`;this._babylonScene._blockEntityCollection=!!this._assetContainer;const o=new ee(s.name||r,r,this._babylonScene);o._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,this._loadBones(e,s,o);const a=this._loadSkinInverseBindMatricesDataAsync(e,s).then(l=>{this._updateBoneMatrices(o,l)});return s._data={babylonSkeleton:o,promise:a},n(o),a}_loadBones(e,t,s){if(t.skeleton==null||this._parent.alwaysComputeSkeletonRootNode){const i=this._findSkeletonRootNode(`${e}/joints`,t.joints);if(i)if(t.skeleton===void 0)t.skeleton=i.index;else{const r=(a,l)=>{for(;l.parent;l=l.parent)if(l.parent===a)return!0;return!1},o=_.Get(`${e}/skeleton`,this._gltf.nodes,t.skeleton);o!==i&&!r(o,i)&&(w.Warn(`${e}/skeleton: Overriding with nearest common ancestor as skeleton node is not a common root`),t.skeleton=i.index)}else w.Warn(`${e}: Failed to find common root`)}const n={};for(const i of t.joints){const r=_.Get(`${e}/joints/${i}`,this._gltf.nodes,i);this._loadBone(r,t,s,n)}}_findSkeletonRootNode(e,t){if(t.length===0)return null;const s={};for(const i of t){const r=[];let o=_.Get(`${e}/${i}`,this._gltf.nodes,i);for(;o.index!==-1;)r.unshift(o),o=o.parent;s[i]=r}let n=null;for(let i=0;;++i){let r=s[t[0]];if(i>=r.length)return n;const o=r[i];for(let a=1;a<t.length;++a)if(r=s[t[a]],i>=r.length||o!==r[i])return n;n=o}}_loadBone(e,t,s,n){e._isJoint=!0;let i=n[e.index];if(i)return i;let r=null;e.index!==t.skeleton&&(e.parent&&e.parent.index!==-1?r=this._loadBone(e.parent,t,s,n):t.skeleton!==void 0&&w.Warn(`/skins/${t.index}/skeleton: Skeleton node is not a common root`));const o=t.joints.indexOf(e.index);return i=new re(e.name||`joint${e.index}`,s,r,this._getNodeMatrix(e),null,null,o),n[e.index]=i,this._postSceneLoadActions.push(()=>{i.linkTransformNode(e._babylonTransformNode)}),i}_loadSkinInverseBindMatricesDataAsync(e,t){if(t.inverseBindMatrices==null)return Promise.resolve(null);const s=_.Get(`${e}/inverseBindMatrices`,this._gltf.accessors,t.inverseBindMatrices);return this._loadFloatAccessorAsync(`/accessors/${s.index}`,s)}_updateBoneMatrices(e,t){for(const s of e.bones){const n=B.Identity(),i=s._index;t&&i!==-1&&(B.FromArrayToRef(t,i*16,n),n.invertToRef(n));const r=s.getParent();r&&n.multiplyToRef(r.getAbsoluteInverseBindMatrix(),n),s.updateMatrix(n,!1,!1),s._updateAbsoluteBindMatrices(void 0,!1)}}_getNodeMatrix(e){return e.matrix?B.FromArray(e.matrix):B.Compose(e.scale?A.FromArray(e.scale):A.One(),e.rotation?z.FromArray(e.rotation):z.Identity(),e.translation?A.FromArray(e.translation):A.Zero())}loadCameraAsync(e,t,s=()=>{}){const n=this._extensionsLoadCameraAsync(e,t,s);if(n)return n;const i=new Array;this.logOpen(`${e} ${t.name||""}`),this._babylonScene._blockEntityCollection=!!this._assetContainer;const r=new X(t.name||`camera${t.index}`,A.Zero(),this._babylonScene,!1);switch(r._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,r.ignoreParentScaling=!0,t._babylonCamera=r,r.rotation.set(0,Math.PI,0),t.type){case"perspective":{const o=t.perspective;if(!o)throw new Error(`${e}: Camera perspective properties are missing`);r.fov=o.yfov,r.minZ=o.znear,r.maxZ=o.zfar||0;break}case"orthographic":{if(!t.orthographic)throw new Error(`${e}: Camera orthographic properties are missing`);r.mode=Ge.ORTHOGRAPHIC_CAMERA,r.orthoLeft=-t.orthographic.xmag,r.orthoRight=t.orthographic.xmag,r.orthoBottom=-t.orthographic.ymag,r.orthoTop=t.orthographic.ymag,r.minZ=t.orthographic.znear,r.maxZ=t.orthographic.zfar;break}default:throw new Error(`${e}: Invalid camera type (${t.type})`)}return v.AddPointerMetadata(r,e),this._parent.onCameraLoadedObservable.notifyObservers(r),s(r),this.logClose(),Promise.all(i).then(()=>r)}_loadAnimationsAsync(){const e=this._gltf.animations;if(!e)return Promise.resolve();const t=new Array;for(let s=0;s<e.length;s++){const n=e[s];t.push(this.loadAnimationAsync(`/animations/${n.index}`,n).then(i=>{i.targetedAnimations.length===0&&i.dispose()}))}return Promise.all(t).then(()=>{})}loadAnimationAsync(e,t){const s=this._extensionsLoadAnimationAsync(e,t);return s||he(()=>import("./animationGroup-Bwms8CyJ.js"),__vite__mapDeps([0,1,2,3,4]),import.meta.url).then(({AnimationGroup:n})=>{this._babylonScene._blockEntityCollection=!!this._assetContainer;const i=new n(t.name||`animation${t.index}`,this._babylonScene);i._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,t._babylonAnimationGroup=i;const r=new Array;_.Assign(t.channels),_.Assign(t.samplers);for(const o of t.channels)r.push(this._loadAnimationChannelAsync(`${e}/channels/${o.index}`,e,t,o,(a,l)=>{a.animations=a.animations||[],a.animations.push(l),i.addTargetedAnimation(l,a)}));return Promise.all(r).then(()=>(i.normalize(0),i))})}async _loadAnimationChannelAsync(e,t,s,n,i){const r=this._extensionsLoadAnimationChannelAsync(e,t,s,n,i);if(r)return r;if(n.target.node==null)return Promise.resolve();const o=_.Get(`${e}/target/node`,this._gltf.nodes,n.target.node),a=n.target.path,l=a==="weights";if(l&&!o._numMorphTargets||!l&&!o._babylonTransformNode||!this._parent.loadNodeAnimations&&!l&&!o._isJoint)return Promise.resolve();await he(()=>import("./glTFLoaderAnimation-BJGr6qtQ.js"),__vite__mapDeps([5,1,2,3,6]),import.meta.url);let h;switch(a){case"translation":{h=J("/nodes/{}/translation")?.interpolation;break}case"rotation":{h=J("/nodes/{}/rotation")?.interpolation;break}case"scale":{h=J("/nodes/{}/scale")?.interpolation;break}case"weights":{h=J("/nodes/{}/weights")?.interpolation;break}default:throw new Error(`${e}/target/path: Invalid value (${n.target.path})`)}if(!h)throw new Error(`${e}/target/path: Could not find interpolation properties for target path (${n.target.path})`);const u={object:o,info:h};return this._loadAnimationChannelFromTargetInfoAsync(e,t,s,n,u,i)}_loadAnimationChannelFromTargetInfoAsync(e,t,s,n,i,r){const o=this.parent.targetFps,a=1/o,l=_.Get(`${e}/sampler`,s.samplers,n.sampler);return this._loadAnimationSamplerAsync(`${t}/samplers/${n.sampler}`,l).then(h=>{let u=0;const c=i.object,p=i.info;for(const d of p){const P=d.getStride(c),C=h.input,U=h.output,D=new Array(C.length);let S=0;switch(h.interpolation){case"STEP":{for(let x=0;x<C.length;x++){const F=d.getValue(c,U,S,1);S+=P,D[x]={frame:C[x]*o,value:F,interpolation:1}}break}case"CUBICSPLINE":{for(let x=0;x<C.length;x++){const F=d.getValue(c,U,S,a);S+=P;const Z=d.getValue(c,U,S,1);S+=P;const Ae=d.getValue(c,U,S,a);S+=P,D[x]={frame:C[x]*o,inTangent:F,value:Z,outTangent:Ae}}break}case"LINEAR":{for(let x=0;x<C.length;x++){const F=d.getValue(c,U,S,1);S+=P,D[x]={frame:C[x]*o,value:F}}break}}if(S>0){const x=`${s.name||`animation${s.index}`}_channel${n.index}_${u}`,F=d.buildAnimations(c,x,o,D);for(const Z of F)u++,r(Z.babylonAnimatable,Z.babylonAnimation)}}})}_loadAnimationSamplerAsync(e,t){if(t._data)return t._data;const s=t.interpolation||"LINEAR";switch(s){case"STEP":case"LINEAR":case"CUBICSPLINE":break;default:throw new Error(`${e}/interpolation: Invalid value (${t.interpolation})`)}const n=_.Get(`${e}/input`,this._gltf.accessors,t.input),i=_.Get(`${e}/output`,this._gltf.accessors,t.output);return t._data=Promise.all([this._loadFloatAccessorAsync(`/accessors/${n.index}`,n),this._loadFloatAccessorAsync(`/accessors/${i.index}`,i)]).then(([r,o])=>({input:r,interpolation:s,output:o})),t._data}loadBufferAsync(e,t,s,n){const i=this._extensionsLoadBufferAsync(e,t,s,n);if(i)return i;if(!t._data)if(t.uri)t._data=this.loadUriAsync(`${e}/uri`,t,t.uri);else{if(!this._bin)throw new Error(`${e}: Uri is missing or the binary glTF is missing its binary chunk`);t._data=this._bin.readAsync(0,t.byteLength)}return t._data.then(r=>{try{return new Uint8Array(r.buffer,r.byteOffset+s,n)}catch(o){throw new Error(`${e}: ${o.message}`)}})}loadBufferViewAsync(e,t){const s=this._extensionsLoadBufferViewAsync(e,t);if(s)return s;if(t._data)return t._data;const n=_.Get(`${e}/buffer`,this._gltf.buffers,t.buffer);return t._data=this.loadBufferAsync(`/buffers/${n.index}`,n,t.byteOffset||0,t.byteLength),t._data}_loadAccessorAsync(e,t,s){if(t._data)return t._data;const n=v._GetNumComponents(e,t.type),i=n*m.GetTypeByteLength(t.componentType),r=n*t.count;if(t.bufferView==null)t._data=Promise.resolve(new s(r));else{const o=_.Get(`${e}/bufferView`,this._gltf.bufferViews,t.bufferView);t._data=this.loadBufferViewAsync(`/bufferViews/${o.index}`,o).then(a=>{if(t.componentType===5126&&!t.normalized&&(!o.byteStride||o.byteStride===i))return v._GetTypedArray(e,t.componentType,a,t.byteOffset,r);{const l=new s(r);return m.ForEach(a,t.byteOffset||0,o.byteStride||i,n,t.componentType,l.length,t.normalized||!1,(h,u)=>{l[u]=h}),l}})}if(t.sparse){const o=t.sparse;t._data=t._data.then(a=>{const l=a,h=_.Get(`${e}/sparse/indices/bufferView`,this._gltf.bufferViews,o.indices.bufferView),u=_.Get(`${e}/sparse/values/bufferView`,this._gltf.bufferViews,o.values.bufferView);return Promise.all([this.loadBufferViewAsync(`/bufferViews/${h.index}`,h),this.loadBufferViewAsync(`/bufferViews/${u.index}`,u)]).then(([c,p])=>{const d=v._GetTypedArray(`${e}/sparse/indices`,o.indices.componentType,c,o.indices.byteOffset,o.count),P=n*o.count;let C;if(t.componentType===5126&&!t.normalized)C=v._GetTypedArray(`${e}/sparse/values`,t.componentType,p,o.values.byteOffset,P);else{const D=v._GetTypedArray(`${e}/sparse/values`,t.componentType,p,o.values.byteOffset,P);C=new s(P),m.ForEach(D,0,i,n,t.componentType,C.length,t.normalized||!1,(S,x)=>{C[x]=S})}let U=0;for(let D=0;D<d.length;D++){let S=d[D]*n;for(let x=0;x<n;x++)l[S++]=C[U++]}return l})})}return t._data}_loadFloatAccessorAsync(e,t){return this._loadAccessorAsync(e,t,Float32Array)}_loadIndicesAccessorAsync(e,t){if(t.type!=="SCALAR")throw new Error(`${e}/type: Invalid value ${t.type}`);if(t.componentType!==5121&&t.componentType!==5123&&t.componentType!==5125)throw new Error(`${e}/componentType: Invalid value ${t.componentType}`);if(t._data)return t._data;if(t.sparse){const s=v._GetTypedArrayConstructor(`${e}/componentType`,t.componentType);t._data=this._loadAccessorAsync(e,t,s)}else{const s=_.Get(`${e}/bufferView`,this._gltf.bufferViews,t.bufferView);t._data=this.loadBufferViewAsync(`/bufferViews/${s.index}`,s).then(n=>v._GetTypedArray(e,t.componentType,n,t.byteOffset,t.count))}return t._data}_loadVertexBufferViewAsync(e){if(e._babylonBuffer)return e._babylonBuffer;const t=this._babylonScene.getEngine();return e._babylonBuffer=this.loadBufferViewAsync(`/bufferViews/${e.index}`,e).then(s=>new We(t,s,!1)),e._babylonBuffer}_loadVertexAccessorAsync(e,t,s){if(t._babylonVertexBuffer?.[s])return t._babylonVertexBuffer[s];t._babylonVertexBuffer||(t._babylonVertexBuffer={});const n=this._babylonScene.getEngine();if(t.sparse||t.bufferView==null)t._babylonVertexBuffer[s]=this._loadFloatAccessorAsync(e,t).then(i=>new m(n,i,s,!1));else{const i=_.Get(`${e}/bufferView`,this._gltf.bufferViews,t.bufferView);t._babylonVertexBuffer[s]=this._loadVertexBufferViewAsync(i).then(r=>{const o=v._GetNumComponents(e,t.type);return new m(n,r,s,!1,void 0,i.byteStride,void 0,t.byteOffset,o,t.componentType,t.normalized,!0,void 0,!0)})}return t._babylonVertexBuffer[s]}_loadMaterialMetallicRoughnessPropertiesAsync(e,t,s){if(!(s instanceof $))throw new Error(`${e}: Material type not supported`);const n=new Array;return t&&(t.baseColorFactor?(s.albedoColor=H.FromArray(t.baseColorFactor),s.alpha=t.baseColorFactor[3]):s.albedoColor=H.White(),s.metallic=t.metallicFactor==null?1:t.metallicFactor,s.roughness=t.roughnessFactor==null?1:t.roughnessFactor,t.baseColorTexture&&n.push(this.loadTextureInfoAsync(`${e}/baseColorTexture`,t.baseColorTexture,i=>{i.name=`${s.name} (Base Color)`,s.albedoTexture=i})),t.metallicRoughnessTexture&&(t.metallicRoughnessTexture.nonColorData=!0,n.push(this.loadTextureInfoAsync(`${e}/metallicRoughnessTexture`,t.metallicRoughnessTexture,i=>{i.name=`${s.name} (Metallic Roughness)`,s.metallicTexture=i})),s.useMetallnessFromMetallicTextureBlue=!0,s.useRoughnessFromMetallicTextureGreen=!0,s.useRoughnessFromMetallicTextureAlpha=!1)),Promise.all(n).then(()=>{})}_loadMaterialAsync(e,t,s,n,i=()=>{}){const r=this._extensionsLoadMaterialAsync(e,t,s,n,i);if(r)return r;t._data=t._data||{};let o=t._data[n];if(!o){this.logOpen(`${e} ${t.name||""}`);const a=this.createMaterial(e,t,n);o={babylonMaterial:a,babylonMeshes:[],promise:this.loadMaterialPropertiesAsync(e,t,a)},t._data[n]=o,v.AddPointerMetadata(a,e),this._parent.onMaterialLoadedObservable.notifyObservers(a),this.logClose()}return s&&(o.babylonMeshes.push(s),s.onDisposeObservable.addOnce(()=>{const a=o.babylonMeshes.indexOf(s);a!==-1&&o.babylonMeshes.splice(a,1)})),i(o.babylonMaterial),o.promise.then(()=>o.babylonMaterial)}_createDefaultMaterial(e,t){this._babylonScene._blockEntityCollection=!!this._assetContainer;const s=new $(e,this._babylonScene);return s._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,s.fillMode=t,s.enableSpecularAntiAliasing=!0,s.useRadianceOverAlpha=!this._parent.transparencyAsCoverage,s.useSpecularOverAlpha=!this._parent.transparencyAsCoverage,s.transparencyMode=$.PBRMATERIAL_OPAQUE,s.metallic=1,s.roughness=1,s}createMaterial(e,t,s){const n=this._extensionsCreateMaterial(e,t,s);if(n)return n;const i=t.name||`material${t.index}`;return this._createDefaultMaterial(i,s)}loadMaterialPropertiesAsync(e,t,s){const n=this._extensionsLoadMaterialPropertiesAsync(e,t,s);if(n)return n;const i=new Array;return i.push(this.loadMaterialBasePropertiesAsync(e,t,s)),t.pbrMetallicRoughness&&i.push(this._loadMaterialMetallicRoughnessPropertiesAsync(`${e}/pbrMetallicRoughness`,t.pbrMetallicRoughness,s)),this.loadMaterialAlphaProperties(e,t,s),Promise.all(i).then(()=>{})}loadMaterialBasePropertiesAsync(e,t,s){if(!(s instanceof $))throw new Error(`${e}: Material type not supported`);const n=new Array;return s.emissiveColor=t.emissiveFactor?H.FromArray(t.emissiveFactor):new H(0,0,0),t.doubleSided&&(s.backFaceCulling=!1,s.twoSidedLighting=!0),t.normalTexture&&(t.normalTexture.nonColorData=!0,n.push(this.loadTextureInfoAsync(`${e}/normalTexture`,t.normalTexture,i=>{i.name=`${s.name} (Normal)`,s.bumpTexture=i})),s.invertNormalMapX=!this._babylonScene.useRightHandedSystem,s.invertNormalMapY=this._babylonScene.useRightHandedSystem,t.normalTexture.scale!=null&&s.bumpTexture&&(s.bumpTexture.level=t.normalTexture.scale),s.forceIrradianceInFragment=!0),t.occlusionTexture&&(t.occlusionTexture.nonColorData=!0,n.push(this.loadTextureInfoAsync(`${e}/occlusionTexture`,t.occlusionTexture,i=>{i.name=`${s.name} (Occlusion)`,s.ambientTexture=i})),s.useAmbientInGrayScale=!0,t.occlusionTexture.strength!=null&&(s.ambientTextureStrength=t.occlusionTexture.strength)),t.emissiveTexture&&n.push(this.loadTextureInfoAsync(`${e}/emissiveTexture`,t.emissiveTexture,i=>{i.name=`${s.name} (Emissive)`,s.emissiveTexture=i})),Promise.all(n).then(()=>{})}loadMaterialAlphaProperties(e,t,s){if(!(s instanceof $))throw new Error(`${e}: Material type not supported`);switch(t.alphaMode||"OPAQUE"){case"OPAQUE":{s.transparencyMode=$.PBRMATERIAL_OPAQUE,s.alpha=1;break}case"MASK":{s.transparencyMode=$.PBRMATERIAL_ALPHATEST,s.alphaCutOff=t.alphaCutoff==null?.5:t.alphaCutoff,s.albedoTexture&&(s.albedoTexture.hasAlpha=!0);break}case"BLEND":{s.transparencyMode=$.PBRMATERIAL_ALPHABLEND,s.albedoTexture&&(s.albedoTexture.hasAlpha=!0,s.useAlphaFromAlbedoTexture=!0);break}default:throw new Error(`${e}/alphaMode: Invalid value (${t.alphaMode})`)}}loadTextureInfoAsync(e,t,s=()=>{}){const n=this._extensionsLoadTextureInfoAsync(e,t,s);if(n)return n;if(this.logOpen(`${e}`),t.texCoord>=6)throw new Error(`${e}/texCoord: Invalid value (${t.texCoord})`);const i=_.Get(`${e}/index`,this._gltf.textures,t.index);i._textureInfo=t;const r=this._loadTextureAsync(`/textures/${t.index}`,i,o=>{o.coordinatesIndex=t.texCoord||0,v.AddPointerMetadata(o,e),this._parent.onTextureLoadedObservable.notifyObservers(o),s(o)});return this.logClose(),r}_loadTextureAsync(e,t,s=()=>{}){const n=this._extensionsLoadTextureAsync(e,t,s);if(n)return n;this.logOpen(`${e} ${t.name||""}`);const i=t.sampler==null?v.DefaultSampler:_.Get(`${e}/sampler`,this._gltf.samplers,t.sampler),r=_.Get(`${e}/source`,this._gltf.images,t.source),o=this._createTextureAsync(e,i,r,s,void 0,!t._textureInfo.nonColorData);return this.logClose(),o}_createTextureAsync(e,t,s,n=()=>{},i,r){const o=this._loadSampler(`/samplers/${t.index}`,t),a=new Array,l=new Xe;this._babylonScene._blockEntityCollection=!!this._assetContainer;const h={noMipmap:o.noMipMaps,invertY:!1,samplingMode:o.samplingMode,onLoad:()=>{this._disposed||l.resolve()},onError:(c,p)=>{this._disposed||l.reject(new Error(`${e}: ${p&&p.message?p.message:c||"Failed to load texture"}`))},mimeType:s.mimeType??Ye(s.uri??""),loaderOptions:i,useSRGBBuffer:!!r&&this._parent.useSRGBBuffers},u=new M(null,this._babylonScene,h);return u._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,a.push(l.promise),a.push(this.loadImageAsync(`/images/${s.index}`,s).then(c=>{const p=s.uri||`${this._fileName}#image${s.index}`,d=`data:${this._uniqueRootUrl}${p}`;u.updateURL(d,c);const P=u.getInternalTexture();P&&(P.label=s.name)})),u.wrapU=o.wrapU,u.wrapV=o.wrapV,n(u),this._parent.useGltfTextureNames&&(u.name=s.name||s.uri||`image${s.index}`),Promise.all(a).then(()=>u)}_loadSampler(e,t){return t._data||(t._data={noMipMaps:t.minFilter===9728||t.minFilter===9729,samplingMode:v._GetTextureSamplingMode(e,t),wrapU:v._GetTextureWrapMode(`${e}/wrapS`,t.wrapS),wrapV:v._GetTextureWrapMode(`${e}/wrapT`,t.wrapT)}),t._data}loadImageAsync(e,t){if(!t._data){if(this.logOpen(`${e} ${t.name||""}`),t.uri)t._data=this.loadUriAsync(`${e}/uri`,t,t.uri);else{const s=_.Get(`${e}/bufferView`,this._gltf.bufferViews,t.bufferView);t._data=this.loadBufferViewAsync(`/bufferViews/${s.index}`,s)}this.logClose()}return t._data}loadUriAsync(e,t,s){const n=this._extensionsLoadUriAsync(e,t,s);if(n)return n;if(!v._ValidateUri(s))throw new Error(`${e}: '${s}' is invalid`);if(Ze(s)){const i=new Uint8Array(ye(s));return this.log(`${e}: Decoded ${s.substring(0,64)}... (${i.length} bytes)`),Promise.resolve(i)}return this.log(`${e}: Loading ${s}`),this._parent.preprocessUrlAsync(this._rootUrl+s).then(i=>new Promise((r,o)=>{this._parent._loadFile(this._babylonScene,i,a=>{this._disposed||(this.log(`${e}: Loaded ${s} (${a.byteLength} bytes)`),r(new Uint8Array(a)))},!0,a=>{o(new Ke(`${e}: Failed to load '${s}'${a?": "+a.status+" "+a.statusText:""}`,a))})}))}static AddPointerMetadata(e,t){e.metadata=e.metadata||{};const s=e._internalMetadata=e._internalMetadata||{},n=s.gltf=s.gltf||{};(n.pointers=n.pointers||[]).push(t)}static _GetTextureWrapMode(e,t){switch(t=t??10497,t){case 33071:return M.CLAMP_ADDRESSMODE;case 33648:return M.MIRROR_ADDRESSMODE;case 10497:return M.WRAP_ADDRESSMODE;default:return w.Warn(`${e}: Invalid value (${t})`),M.WRAP_ADDRESSMODE}}static _GetTextureSamplingMode(e,t){const s=t.magFilter==null?9729:t.magFilter,n=t.minFilter==null?9987:t.minFilter;if(s===9729)switch(n){case 9728:return M.LINEAR_NEAREST;case 9729:return M.LINEAR_LINEAR;case 9984:return M.LINEAR_NEAREST_MIPNEAREST;case 9985:return M.LINEAR_LINEAR_MIPNEAREST;case 9986:return M.LINEAR_NEAREST_MIPLINEAR;case 9987:return M.LINEAR_LINEAR_MIPLINEAR;default:return w.Warn(`${e}/minFilter: Invalid value (${n})`),M.LINEAR_LINEAR_MIPLINEAR}else switch(s!==9728&&w.Warn(`${e}/magFilter: Invalid value (${s})`),n){case 9728:return M.NEAREST_NEAREST;case 9729:return M.NEAREST_LINEAR;case 9984:return M.NEAREST_NEAREST_MIPNEAREST;case 9985:return M.NEAREST_LINEAR_MIPNEAREST;case 9986:return M.NEAREST_NEAREST_MIPLINEAR;case 9987:return M.NEAREST_LINEAR_MIPLINEAR;default:return w.Warn(`${e}/minFilter: Invalid value (${n})`),M.NEAREST_NEAREST_MIPNEAREST}}static _GetTypedArrayConstructor(e,t){try{return qe(t)}catch(s){throw new Error(`${e}: ${s.message}`)}}static _GetTypedArray(e,t,s,n,i){const r=s.buffer;n=s.byteOffset+(n||0);const o=v._GetTypedArrayConstructor(`${e}/componentType`,t),a=m.GetTypeByteLength(t);return n%a!==0?(w.Warn(`${e}: Copying buffer as byte offset (${n}) is not a multiple of component type byte length (${a})`),new o(r.slice(n,n+i*a),0)):new o(r,n,i)}static _GetNumComponents(e,t){switch(t){case"SCALAR":return 1;case"VEC2":return 2;case"VEC3":return 3;case"VEC4":return 4;case"MAT2":return 4;case"MAT3":return 9;case"MAT4":return 16}throw new Error(`${e}: Invalid type (${t})`)}static _ValidateUri(e){return R.IsBase64(e)||e.indexOf("..")===-1}static _GetDrawMode(e,t){switch(t==null&&(t=4),t){case 0:return N.PointListDrawMode;case 1:return N.LineListDrawMode;case 2:return N.LineLoopDrawMode;case 3:return N.LineStripDrawMode;case 4:return N.TriangleFillMode;case 5:return N.TriangleStripDrawMode;case 6:return N.TriangleFanDrawMode}throw new Error(`${e}: Invalid mesh primitive mode (${t})`)}_compileMaterialsAsync(){this._parent._startPerformanceCounter("Compile materials");const e=new Array;if(this._gltf.materials){for(const t of this._gltf.materials)if(t._data)for(const s in t._data){const n=t._data[s];for(const i of n.babylonMeshes){i.computeWorldMatrix(!0);const r=n.babylonMaterial;e.push(r.forceCompilationAsync(i)),e.push(r.forceCompilationAsync(i,{useInstances:!0})),this._parent.useClipPlane&&(e.push(r.forceCompilationAsync(i,{clipPlane:!0})),e.push(r.forceCompilationAsync(i,{clipPlane:!0,useInstances:!0})))}}}return Promise.all(e).then(()=>{this._parent._endPerformanceCounter("Compile materials")})}_compileShadowGeneratorsAsync(){this._parent._startPerformanceCounter("Compile shadow generators");const e=new Array,t=this._babylonScene.lights;for(const s of t){const n=s.getShadowGenerator();n&&e.push(n.forceCompilationAsync())}return Promise.all(e).then(()=>{this._parent._endPerformanceCounter("Compile shadow generators")})}_forEachExtensions(e){for(const t of this._extensions)t.enabled&&e(t)}_applyExtensions(e,t,s){for(const n of this._extensions)if(n.enabled){const i=`${n.name}.${t}`,r=e;r._activeLoaderExtensionFunctions=r._activeLoaderExtensionFunctions||{};const o=r._activeLoaderExtensionFunctions;if(!o[i]){o[i]=!0;try{const a=s(n);if(a)return a}finally{delete o[i]}}}return null}_extensionsOnLoading(){this._forEachExtensions(e=>e.onLoading&&e.onLoading())}_extensionsOnReady(){this._forEachExtensions(e=>e.onReady&&e.onReady())}_extensionsLoadSceneAsync(e,t){return this._applyExtensions(t,"loadScene",s=>s.loadSceneAsync&&s.loadSceneAsync(e,t))}_extensionsLoadNodeAsync(e,t,s){return this._applyExtensions(t,"loadNode",n=>n.loadNodeAsync&&n.loadNodeAsync(e,t,s))}_extensionsLoadCameraAsync(e,t,s){return this._applyExtensions(t,"loadCamera",n=>n.loadCameraAsync&&n.loadCameraAsync(e,t,s))}_extensionsLoadVertexDataAsync(e,t,s){return this._applyExtensions(t,"loadVertexData",n=>n._loadVertexDataAsync&&n._loadVertexDataAsync(e,t,s))}_extensionsLoadMeshPrimitiveAsync(e,t,s,n,i,r){return this._applyExtensions(i,"loadMeshPrimitive",o=>o._loadMeshPrimitiveAsync&&o._loadMeshPrimitiveAsync(e,t,s,n,i,r))}_extensionsLoadMaterialAsync(e,t,s,n,i){return this._applyExtensions(t,"loadMaterial",r=>r._loadMaterialAsync&&r._loadMaterialAsync(e,t,s,n,i))}_extensionsCreateMaterial(e,t,s){return this._applyExtensions(t,"createMaterial",n=>n.createMaterial&&n.createMaterial(e,t,s))}_extensionsLoadMaterialPropertiesAsync(e,t,s){return this._applyExtensions(t,"loadMaterialProperties",n=>n.loadMaterialPropertiesAsync&&n.loadMaterialPropertiesAsync(e,t,s))}_extensionsLoadTextureInfoAsync(e,t,s){return this._applyExtensions(t,"loadTextureInfo",n=>n.loadTextureInfoAsync&&n.loadTextureInfoAsync(e,t,s))}_extensionsLoadTextureAsync(e,t,s){return this._applyExtensions(t,"loadTexture",n=>n._loadTextureAsync&&n._loadTextureAsync(e,t,s))}_extensionsLoadAnimationAsync(e,t){return this._applyExtensions(t,"loadAnimation",s=>s.loadAnimationAsync&&s.loadAnimationAsync(e,t))}_extensionsLoadAnimationChannelAsync(e,t,s,n,i){return this._applyExtensions(s,"loadAnimationChannel",r=>r._loadAnimationChannelAsync&&r._loadAnimationChannelAsync(e,t,s,n,i))}_extensionsLoadSkinAsync(e,t,s){return this._applyExtensions(s,"loadSkin",n=>n._loadSkinAsync&&n._loadSkinAsync(e,t,s))}_extensionsLoadUriAsync(e,t,s){return this._applyExtensions(t,"loadUri",n=>n._loadUriAsync&&n._loadUriAsync(e,t,s))}_extensionsLoadBufferViewAsync(e,t){return this._applyExtensions(t,"loadBufferView",s=>s.loadBufferViewAsync&&s.loadBufferViewAsync(e,t))}_extensionsLoadBufferAsync(e,t,s,n){return this._applyExtensions(t,"loadBuffer",i=>i.loadBufferAsync&&i.loadBufferAsync(e,t,s,n))}static LoadExtensionAsync(e,t,s,n){if(!t.extensions)return null;const r=t.extensions[s];return r?n(`${e}/extensions/${s}`,r):null}static LoadExtraAsync(e,t,s,n){if(!t.extras)return null;const r=t.extras[s];return r?n(`${e}/extras/${s}`,r):null}isExtensionUsed(e){return!!this._gltf.extensionsUsed&&this._gltf.extensionsUsed.indexOf(e)!==-1}logOpen(e){this._parent._logOpen(e)}logClose(){this._parent._logClose()}log(e){this._parent._log(e)}startPerformanceCounter(e){this._parent._startPerformanceCounter(e)}endPerformanceCounter(e){this._parent._endPerformanceCounter(e)}}v.DefaultSampler={index:-1};E._CreateGLTF2Loader=f=>new v(f);export{_ as ArrayItem,E as GLTFFileLoader,v as GLTFLoader,et as LoadBoundingInfoFromPositionAccessor};
//# sourceMappingURL=glTFLoader-9Z3KGax5.js.map
