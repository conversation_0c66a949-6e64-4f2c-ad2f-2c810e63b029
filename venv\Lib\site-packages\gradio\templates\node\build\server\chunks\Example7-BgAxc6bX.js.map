{"version": 3, "file": "Example7-BgAxc6bX.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Example7.js"], "sourcesContent": ["import{create_ssr_component as r,escape as c}from\"svelte/internal\";const A={code:\"pre.svelte-agpzo2{text-align:left}.gallery.svelte-agpzo2{padding:var(--size-1) var(--size-2)}\",map:'{\"version\":3,\"file\":\"Example.svelte\",\"sources\":[\"Example.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">export let value;\\\\nexport let type;\\\\nexport let selected = false;\\\\nfunction truncate_text(text, max_length = 60) {\\\\n    if (!text)\\\\n        return \\\\\"\\\\\";\\\\n    const str = String(text);\\\\n    if (str.length <= max_length)\\\\n        return str;\\\\n    return str.slice(0, max_length) + \\\\\"...\\\\\";\\\\n}\\\\n<\\/script>\\\\n\\\\n<pre\\\\n\\\\tclass:table={type === \\\\\"table\\\\\"}\\\\n\\\\tclass:gallery={type === \\\\\"gallery\\\\\"}\\\\n\\\\tclass:selected>{truncate_text(value)}</pre>\\\\n\\\\n<style>\\\\n\\\\tpre {\\\\n\\\\t\\\\ttext-align: left;\\\\n\\\\t}\\\\n\\\\t.gallery {\\\\n\\\\t\\\\tpadding: var(--size-1) var(--size-2);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAmBC,iBAAI,CACH,UAAU,CAAE,IACb,CACA,sBAAS,CACR,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CACpC\"}'};function i(l,t=60){if(!l)return\"\";const e=String(l);return e.length<=t?e:e.slice(0,t)+\"...\"}const v=r((l,t,e,o)=>{let{value:a}=t,{type:n}=t,{selected:s=!1}=t;return t.value===void 0&&e.value&&a!==void 0&&e.value(a),t.type===void 0&&e.type&&n!==void 0&&e.type(n),t.selected===void 0&&e.selected&&s!==void 0&&e.selected(s),l.css.add(A),`<pre class=\"${[\"svelte-agpzo2\",(n===\"table\"?\"table\":\"\")+\" \"+(n===\"gallery\"?\"gallery\":\"\")+\" \"+(s?\"selected\":\"\")].join(\" \").trim()}\">${c(i(a))}</pre>`});export{v as default};\n//# sourceMappingURL=Example7.js.map\n"], "names": ["r", "c"], "mappings": ";;AAAmE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,+FAA+F,CAAC,GAAG,CAAC,2zBAA2zB,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAM,EAAE,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAM,MAAC,CAAC,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAEC,MAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;;;;"}