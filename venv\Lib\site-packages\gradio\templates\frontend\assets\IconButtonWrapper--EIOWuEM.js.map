{"version": 3, "file": "IconButtonWrapper--EIOWuEM.js", "sources": ["../../../../js/atoms/src/IconButtonWrapper.svelte"], "sourcesContent": ["<script>\n\texport let top_panel = true;\n\texport let display_top_corner = false;\n</script>\n\n<div\n\tclass={`icon-button-wrapper ${top_panel ? \"top-panel\" : \"\"} ${display_top_corner ? \"display-top-corner\" : \"hide-top-corner\"}`}\n>\n\t<slot></slot>\n</div>\n\n<style>\n\t.icon-button-wrapper {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tz-index: var(--layer-3);\n\t\tgap: var(--spacing-sm);\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tbackground: var(--block-background-fill);\n\t\tpadding: var(--spacing-xxs);\n\t}\n\n\t.icon-button-wrapper.hide-top-corner {\n\t\tborder-top: none;\n\t\tborder-right: none;\n\t\tborder-radius: var(--block-label-right-radius);\n\t}\n\n\t.icon-button-wrapper.display-top-corner {\n\t\tborder-radius: var(--radius-sm) 0 0 var(--radius-sm);\n\t\ttop: var(--spacing-sm);\n\t\tright: -1px;\n\t}\n\n\t.icon-button-wrapper:not(.top-panel) {\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t}\n\n\t.top-panel {\n\t\tposition: absolute;\n\t\ttop: var(--block-label-margin);\n\t\tright: var(--block-label-margin);\n\t\tmargin: 0;\n\t}\n\n\t.icon-button-wrapper :global(button) {\n\t\tmargin: var(--spacing-xxs);\n\t\tborder-radius: var(--radius-xs);\n\t\tposition: relative;\n\t}\n\n\t.icon-button-wrapper :global(a.download-link:not(:last-child)),\n\t.icon-button-wrapper :global(button:not(:last-child)) {\n\t\tmargin-right: var(--spacing-xxs);\n\t}\n\n\t.icon-button-wrapper\n\t\t:global(a.download-link:not(:last-child):not(.no-border *)::after),\n\t.icon-button-wrapper\n\t\t:global(button:not(:last-child):not(.no-border *)::after) {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\tright: -4.5px;\n\t\ttop: 15%;\n\t\theight: 70%;\n\t\twidth: 1px;\n\t\tbackground-color: var(--border-color-primary);\n\t}\n\n\t.icon-button-wrapper :global(> *) {\n\t\theight: 100%;\n\t}\n</style>\n"], "names": ["attr", "div", "div_class_value", "null_to_empty", "ctx", "insert", "target", "anchor", "current", "dirty", "top_panel", "$$props", "display_top_corner"], "mappings": "maAM+BA,EAAAC,EAAA,QAAAC,EAAAC,EAAA,uBAAAC,EAAY,CAAA,EAAA,YAAc,EAAE,IAAIA,EAAkB,CAAA,EAAG,qBAAuB,iBAAiB,EAAA,EAAA,gBAAA,UAD5HC,EAIKC,EAAAL,EAAAM,CAAA,kGAH0B,CAAAC,GAAAC,EAAA,GAAAP,KAAAA,EAAAC,EAAA,uBAAAC,EAAY,CAAA,EAAA,YAAc,EAAE,IAAIA,EAAkB,CAAA,EAAG,qBAAuB,iBAAiB,EAAA,EAAA,sJALhH,CAAA,UAAAM,EAAY,EAAI,EAAAC,EAChB,CAAA,mBAAAC,EAAqB,EAAK,EAAAD"}