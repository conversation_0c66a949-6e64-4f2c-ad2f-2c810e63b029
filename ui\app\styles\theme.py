"""
主题样式定义
包含深色和浅色主题
"""

class BaseTheme:
    """基础主题类"""
    
    def __init__(self):
        self.colors = {}
        self.fonts = {}
    
    def get_main_stylesheet(self) -> str:
        """获取主窗口样式"""
        return ""
    
    def get_sidebar_stylesheet(self) -> str:
        """获取侧边栏样式"""
        return f"""
        /* 侧边栏主容器 */
        QWidget#sidebar {{
            background: {self.colors['glass_bg']};
            border-right: 1px solid {self.colors['border']};
            border-radius: 16px 0 0 16px;
        }}

        /* 侧边栏标题 */
        QLabel#sidebar_title {{
            background: {self.colors['gradient_primary']};
            color: white;
            font-size: 18px;
            font-weight: 700;
            padding: 16px;
            border-radius: 12px;
            margin: 10px;
        }}

        /* 新建对话按钮 */
        QPushButton#new_chat_button {{
            background: {self.colors['gradient_secondary']};
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 14px;
            font-weight: 600;
            margin: 5px 10px;
        }}

        QPushButton#new_chat_button:hover {{
            background: {self.colors['gradient_accent']};
            border: 2px solid {self.colors['secondary']};
        }}

        /* 对话历史列表 */
        QListWidget {{
            background: transparent;
            border: none;
            border-radius: 8px;
            padding: 5px;
            margin: 0 10px;
        }}

        QListWidget::item {{
            background: {self.colors['surface']};
            border: 1px solid {self.colors['border']};
            border-radius: 10px;
            padding: 12px;
            margin: 3px 0;
            color: {self.colors['text_primary']};
            font-size: 13px;
        }}

        QListWidget::item:hover {{
            background: {self.colors['surface_elevated']};
            border: 1px solid {self.colors['primary']};
        }}

        QListWidget::item:selected {{
            background: {self.colors['gradient_primary']};
            border: 1px solid {self.colors['primary']};
            color: white;
            font-weight: 600;
        }}

        /* 设置按钮 */
        QPushButton#settings_button {{
            background: {self.colors['surface_variant']};
            color: {self.colors['text_secondary']};
            border: 1px solid {self.colors['border']};
            border-radius: 10px;
            padding: 10px;
            margin: 10px;
            font-size: 13px;
        }}

        QPushButton#settings_button:hover {{
            background: {self.colors['hover']};
            color: {self.colors['text_primary']};
            border: 1px solid {self.colors['primary']};
        }}
        """
    
    def get_chat_stylesheet(self) -> str:
        """获取聊天区域样式"""
        return f"""
        /* 聊天区域主容器 */
        QWidget#chat_widget {{
            background: transparent;
            border-radius: 16px;
        }}

        /* 消息滚动区域 */
        QScrollArea {{
            background: transparent;
            border: none;
            border-radius: 16px;
        }}

        /* 消息气泡样式 */
        QFrame#user_message {{
            background: {self.colors['gradient_primary']};
            border-radius: 18px;
            padding: 12px 16px;
            margin: 5px 50px 5px 5px;
            color: white;
            font-size: 14px;
            line-height: 1.4;
        }}

        QFrame#assistant_message {{
            background: {self.colors['surface_elevated']};
            border: 1px solid {self.colors['border']};
            border-radius: 18px;
            padding: 12px 16px;
            margin: 5px 5px 5px 50px;
            color: {self.colors['text_primary']};
            font-size: 14px;
            line-height: 1.4;
        }}

        /* 输入区域 */
        QFrame#input_frame {{
            background: {self.colors['glass_bg']};
            border: 1px solid {self.colors['border']};
            border-radius: 20px;
            padding: 8px;
            margin: 10px;
        }}

        /* 聊天输入框 */
        QTextEdit#chat_input {{
            background: transparent;
            border: none;
            border-radius: 16px;
            padding: 12px 16px;
            font-size: 14px;
            color: {self.colors['text_primary']};
            min-height: 20px;
            max-height: 120px;
        }}

        QTextEdit#chat_input:focus {{
            background: {self.colors['surface']};
            border: 2px solid {self.colors['primary']};
        }}

        /* 发送按钮特殊样式 */
        QPushButton#send_button {{
            background: {self.colors['gradient_secondary']};
            border: none;
            border-radius: 20px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 700;
            color: white;
            min-width: 60px;
        }}

        QPushButton#send_button:hover {{
            background: {self.colors['gradient_accent']};
            border: 2px solid {self.colors['secondary']};
        }}

        QPushButton#send_button:pressed {{
            background: {self.colors['pressed']};
        }}

        /* 打字指示器 */
        QLabel#typing_indicator {{
            background: {self.colors['surface_elevated']};
            border-radius: 12px;
            padding: 8px 16px;
            color: {self.colors['text_secondary']};
            font-style: italic;
        }}
        """
    
    def get_persona_selector_stylesheet(self) -> str:
        """获取人设选择器样式"""
        return f"""
        /* 人设选择器主容器 */
        QWidget#persona_selector {{
            background: {self.colors['glass_bg']};
            border: 1px solid {self.colors['border']};
            border-radius: 16px;
            margin: 10px;
            padding: 10px;
        }}

        /* 人设标签 */
        QLabel#persona_label {{
            color: {self.colors['text_secondary']};
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 5px;
        }}

        /* 人设下拉框 */
        QComboBox#persona_combo {{
            background: {self.colors['surface_elevated']};
            border: 2px solid {self.colors['border']};
            border-radius: 12px;
            padding: 10px 16px;
            font-size: 14px;
            font-weight: 600;
            color: {self.colors['text_primary']};
            min-height: 25px;
        }}

        QComboBox#persona_combo:hover {{
            border: 2px solid {self.colors['primary']};
            background: {self.colors['surface']};
        }}

        QComboBox#persona_combo:focus {{
            border: 2px solid {self.colors['primary']};
        }}

        /* 人设头像 */
        QLabel#persona_avatar {{
            border: 2px solid {self.colors['primary']};
            border-radius: 25px;
            background: {self.colors['surface']};
            padding: 2px;
        }}
        """

    def get_status_bar_stylesheet(self) -> str:
        """获取状态栏样式"""
        return f"""
        /* 状态栏主容器 */
        QWidget#status_bar {{
            background: {self.colors['glass_bg']};
            border-top: 1px solid {self.colors['border']};
            border-radius: 0 0 16px 16px;
            padding: 8px 16px;
        }}

        /* 状态文本 */
        QLabel#status_text {{
            color: {self.colors['text_secondary']};
            font-size: 12px;
            font-weight: 500;
        }}

        /* 连接状态指示器 */
        QLabel#connection_status {{
            background: {self.colors['success']};
            color: white;
            border-radius: 6px;
            padding: 4px 8px;
            font-size: 11px;
            font-weight: 600;
        }}

        QLabel#connection_status[status="disconnected"] {{
            background: {self.colors['error']};
        }}

        QLabel#connection_status[status="connecting"] {{
            background: {self.colors['warning']};
        }}

        /* 模型信息 */
        QLabel#model_info {{
            background: {self.colors['surface_variant']};
            color: {self.colors['text_primary']};
            border-radius: 8px;
            padding: 4px 10px;
            font-size: 11px;
            font-weight: 500;
        }}
        """

class DarkTheme(BaseTheme):
    """深色主题 - 现代化设计"""

    def __init__(self):
        super().__init__()
        self.colors = {
            # 主要背景色 - 深邃的蓝黑色调
            'background': '#0a0e1a',      # 深邃背景
            'surface': '#1a1f2e',         # 卡片背景
            'surface_variant': '#252b3d', # 变体表面
            'surface_elevated': '#2d3548', # 提升表面

            # 主色调 - 现代蓝紫渐变
            'primary': '#667eea',         # 主色调
            'primary_variant': '#5a67d8', # 深主色
            'primary_light': '#7c3aed',   # 浅主色
            'secondary': '#06d6a0',       # 青绿辅助色
            'accent': '#f093fb',          # 粉紫强调色

            # 文本颜色
            'text_primary': '#ffffff',    # 主要文本
            'text_secondary': '#a0aec0',  # 次要文本
            'text_disabled': '#4a5568',   # 禁用文本
            'text_hint': '#718096',       # 提示文本

            # 边框和分割线
            'border': '#2d3748',          # 边框
            'border_light': '#4a5568',    # 浅边框
            'divider': '#2d3748',         # 分割线

            # 交互状态
            'hover': '#2d3748',           # 悬停背景
            'pressed': '#4a5568',         # 按下背景
            'selected': '#667eea',        # 选中背景
            'focus': '#7c3aed',           # 焦点颜色

            # 状态颜色
            'error': '#fc8181',           # 错误色
            'success': '#68d391',         # 成功色
            'warning': '#f6ad55',         # 警告色
            'info': '#63b3ed',            # 信息色

            # 渐变色
            'gradient_primary': 'qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #667eea, stop:1 #764ba2)',
            'gradient_secondary': 'qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #06d6a0, stop:1 #0891b2)',
            'gradient_accent': 'qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #f093fb, stop:1 #f5576c)',
            'gradient_background': 'qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #0a0e1a, stop:1 #1a1f2e)',

            # 阴影
            'shadow_light': 'rgba(102, 126, 234, 0.1)',
            'shadow_medium': 'rgba(0, 0, 0, 0.2)',
            'shadow_heavy': 'rgba(0, 0, 0, 0.4)',

            # 特殊效果
            'glass_bg': 'rgba(26, 31, 46, 0.8)',  # 毛玻璃背景
            'neon_glow': '#667eea',               # 霓虹发光
        }
    
    def get_main_stylesheet(self) -> str:
        return f"""
        /* 主窗口 - 现代化设计 */
        QMainWindow {{
            background: {self.colors['gradient_background']};
            color: {self.colors['text_primary']};
            border-radius: 16px;
            border: 1px solid {self.colors['border']};
        }}

        /* 基础组件样式 */
        QWidget {{
            background-color: transparent;
            color: {self.colors['text_primary']};
            font-family: 'SF Pro Display', 'Microsoft YaHei UI', 'Segoe UI Variable', 'Segoe UI', sans-serif;
            font-weight: 400;
            selection-background-color: {self.colors['primary']};
            selection-color: white;
        }}

        /* 现代化滚动条 */
        QScrollBar:vertical {{
            background: transparent;
            width: 12px;
            border-radius: 6px;
            margin: 2px;
        }}

        QScrollBar::handle:vertical {{
            background: {self.colors['gradient_primary']};
            border-radius: 6px;
            min-height: 30px;
            margin: 2px;
        }}

        QScrollBar::handle:vertical:hover {{
            background: {self.colors['primary_light']};
        }}

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            border: none;
            background: none;
        }}

        QScrollBar:horizontal {{
            background: transparent;
            height: 12px;
            border-radius: 6px;
            margin: 2px;
        }}

        QScrollBar::handle:horizontal {{
            background: {self.colors['gradient_primary']};
            border-radius: 6px;
            min-width: 30px;
            margin: 2px;
        }}

        QScrollBar::handle:horizontal:hover {{
            background: {self.colors['primary_light']};
        }}

        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
            border: none;
            background: none;
        }}

        /* 现代化按钮样式 */
        QPushButton {{
            background: {self.colors['gradient_primary']};
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 600;
            min-height: 20px;
        }}

        QPushButton:hover {{
            background: {self.colors['gradient_accent']};
            border: 2px solid {self.colors['primary_light']};
        }}

        QPushButton:pressed {{
            background: {self.colors['pressed']};
            border: 2px solid {self.colors['primary']};
        }}

        QPushButton:disabled {{
            background: {self.colors['surface_variant']};
            color: {self.colors['text_disabled']};
        }}

        /* 特殊按钮样式 */
        QPushButton#send_button {{
            background: {self.colors['gradient_secondary']};
            border-radius: 20px;
            padding: 10px 20px;
            font-weight: 700;
        }}

        QPushButton#send_button:hover {{
            background: {self.colors['gradient_accent']};
            border: 2px solid {self.colors['secondary']};
        }}

        /* 现代化输入框样式 */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            background: {self.colors['surface']};
            border: 2px solid {self.colors['border']};
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 14px;
            color: {self.colors['text_primary']};
        }}

        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border: 2px solid {self.colors['primary']};
            background: {self.colors['surface_elevated']};
        }}

        QLineEdit:hover, QTextEdit:hover, QPlainTextEdit:hover {{
            border: 2px solid {self.colors['border_light']};
            background: {self.colors['surface_elevated']};
        }}

        /* 现代化下拉框样式 */
        QComboBox {{
            background: {self.colors['surface']};
            border: 2px solid {self.colors['border']};
            border-radius: 12px;
            padding: 10px 16px;
            font-size: 14px;
            color: {self.colors['text_primary']};
            min-height: 20px;
        }}

        QComboBox:hover {{
            border: 2px solid {self.colors['border_light']};
            background: {self.colors['surface_elevated']};
        }}

        QComboBox:focus {{
            border: 2px solid {self.colors['primary']};
            background: {self.colors['surface_elevated']};
        }}

        QComboBox::drop-down {{
            border: none;
            width: 30px;
        }}

        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 8px solid {self.colors['text_secondary']};
            margin-right: 10px;
        }}

        QComboBox QAbstractItemView {{
            background: {self.colors['surface_elevated']};
            border: 1px solid {self.colors['border']};
            border-radius: 8px;
            padding: 5px;
            selection-background-color: {self.colors['primary']};
            selection-color: white;
        }}

        /* 现代化标签页样式 */
        QTabWidget::pane {{
            background: {self.colors['surface']};
            border: 1px solid {self.colors['border']};
            border-radius: 12px;
            margin-top: 10px;
        }}

        QTabBar::tab {{
            background: {self.colors['surface_variant']};
            color: {self.colors['text_secondary']};
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            margin: 2px;
            font-weight: 500;
        }}

        QTabBar::tab:selected {{
            background: {self.colors['primary_light']};
            color: white;
            font-weight: 700;
            border: 1px solid {self.colors['primary']};
        }}

        QTabBar::tab:hover:!selected {{
            background: {self.colors['hover']};
            color: {self.colors['text_primary']};
        }}

        /* 现代化分组框样式 */
        QGroupBox {{
            background: {self.colors['glass_bg']};
            border: 1px solid {self.colors['border']};
            border-radius: 16px;
            margin-top: 20px;
            padding-top: 15px;
            font-weight: 600;
            font-size: 15px;
            color: {self.colors['text_primary']};
        }}

        QGroupBox::title {{
            subcontrol-origin: margin;
            subcontrol-position: top left;
            padding: 5px 15px;
            background: {self.colors['gradient_primary']};
            color: white;
            border-radius: 8px;
            margin-left: 10px;
            font-weight: 700;
        }}

        /* 现代化复选框样式 */
        QCheckBox {{
            color: {self.colors['text_primary']};
            font-size: 14px;
            spacing: 8px;
        }}

        QCheckBox::indicator {{
            width: 20px;
            height: 20px;
            border-radius: 6px;
            border: 2px solid {self.colors['border']};
            background: {self.colors['surface']};
        }}

        QCheckBox::indicator:hover {{
            border: 2px solid {self.colors['primary']};
            background: {self.colors['surface_elevated']};
        }}

        QCheckBox::indicator:checked {{
            background: {self.colors['gradient_primary']};
            border: 2px solid {self.colors['primary']};
        }}

        QCheckBox::indicator:checked:hover {{
            background: {self.colors['gradient_accent']};
        }}

        /* 现代化数字输入框样式 */
        QSpinBox, QDoubleSpinBox {{
            background: {self.colors['surface']};
            border: 2px solid {self.colors['border']};
            border-radius: 12px;
            padding: 8px 12px;
            font-size: 14px;
            color: {self.colors['text_primary']};
            min-height: 20px;
        }}

        QSpinBox:focus, QDoubleSpinBox:focus {{
            border: 2px solid {self.colors['primary']};
            background: {self.colors['surface_elevated']};
        }}

        QSpinBox::up-button, QDoubleSpinBox::up-button {{
            background: {self.colors['gradient_primary']};
            border: none;
            border-radius: 6px;
            width: 20px;
            margin: 2px;
        }}

        QSpinBox::down-button, QDoubleSpinBox::down-button {{
            background: {self.colors['gradient_primary']};
            border: none;
            border-radius: 6px;
            width: 20px;
            margin: 2px;
        }}

        QSpinBox::up-arrow, QDoubleSpinBox::up-arrow {{
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-bottom: 6px solid white;
        }}

        QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {{
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 6px solid white;
        }}

        QScrollBar::handle:vertical:hover {{
            background: {self.colors['primary']};
        }}

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px;
        }}

        /* 工具提示美化 */
        QToolTip {{
            background-color: {self.colors['surface_variant']};
            color: {self.colors['text_primary']};
            border: 1px solid {self.colors['border']};
            border-radius: 6px;
            padding: 8px;
            font-size: 12px;
        }}
        """
    
    def get_sidebar_stylesheet(self) -> str:
        return f"""
        #sidebar {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {self.colors['surface']},
                stop:1 {self.colors['surface_variant']});
            border-right: 2px solid {self.colors['border']};
        }}

        #sidebar_top {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {self.colors['surface_variant']},
                stop:1 {self.colors['surface']});
            border-bottom: 2px solid {self.colors['border']};
            border-radius: 0px 0px 12px 0px;
        }}
        
        #app_title {{
            color: {self.colors['text_primary']};
            font-size: 18px;
            font-weight: 700;
            padding: 12px;
            background: {self.colors['gradient_primary']};
            border-radius: 8px;
            text-align: center;
            border: 2px solid {self.colors['primary']};
        }}
        
        #new_chat_button {{
            background: {self.colors['gradient_secondary']};
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-weight: 600;
            font-size: 14px;
            text-align: center;
        }}

        #new_chat_button:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {self.colors['primary']},
                stop:1 {self.colors['primary_variant']});
            border: 2px solid {self.colors['primary']};
        }}

        #new_chat_button:pressed {{
            background: {self.colors['primary_variant']};
            border: 2px solid {self.colors['primary_variant']};
        }}
        
        #history_title {{
            color: {self.colors['text_secondary']};
            font-size: 12px;
            font-weight: bold;
            padding: 8px 12px;
        }}
        
        #chat_history_item {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 transparent,
                stop:1 {self.colors['surface']});
            border: 1px solid transparent;
            border-radius: 10px;
            margin: 3px 8px;
            padding: 10px;
        }}

        #chat_history_item:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {self.colors['hover']},
                stop:1 {self.colors['surface_variant']});
            border: 1px solid {self.colors['border']};
        }}

        #chat_history_item[selected="true"] {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {self.colors['selected']},
                stop:1 {self.colors['primary_variant']});
            border: 2px solid {self.colors['primary']};
        }}
        
        #chat_title {{
            color: {self.colors['text_primary']};
            font-size: 13px;
            font-weight: bold;
        }}
        
        #chat_preview {{
            color: {self.colors['text_secondary']};
            font-size: 11px;
        }}
        
        #chat_timestamp {{
            color: {self.colors['text_disabled']};
            font-size: 10px;
        }}
        
        #settings_button {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 transparent,
                stop:1 {self.colors['surface']});
            color: {self.colors['text_secondary']};
            border: 2px solid {self.colors['border']};
            border-radius: 10px;
            padding: 10px 15px;
            text-align: left;
            font-weight: 500;
        }}

        #settings_button:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {self.colors['hover']},
                stop:1 {self.colors['surface_variant']});
            color: {self.colors['text_primary']};
            border: 2px solid {self.colors['primary']};
        }}
        """
    
    def get_chat_stylesheet(self) -> str:
        return f"""
        /* 聊天区域主容器 - 深色背景 */
        QWidget#chat_widget {{
            background: #212121;
            border: none;
        }}

        /* 滚动区域样式 */
        QScrollArea {{
            background: #212121;
            border: none;
        }}

        QScrollArea QScrollBar:vertical {{
            background: transparent;
            width: 12px;
            border-radius: 6px;
            margin: 2px;
        }}

        QScrollArea QScrollBar::handle:vertical {{
            background: {self.colors['gradient_primary']};
            border-radius: 6px;
            min-height: 30px;
            margin: 2px;
        }}

        QScrollArea QScrollBar::handle:vertical:hover {{
            background: {self.colors['primary_light']};
        }}
        
        QScrollBar:vertical {{
            background: rgba(45, 53, 72, 0.5);
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background: rgba(102, 126, 234, 0.6);
            border-radius: 6px;
            min-height: 20px;
            margin: 2px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background: rgba(102, 126, 234, 0.8);
        }}

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0;
            width: 0;
        }}

        QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
            background: transparent;
        }}
        
        /* 用户消息气泡 - 深色背景 */
        QFrame#user_message {{
            background: transparent;
            border: none;
        }}

        /* AI消息气泡 - 无背景 */
        QFrame#assistant_message {{
            background: transparent;
            border: none;
        }}
        
        /* 用户头像样式 */
        QLabel[objectName="user_avatar"] {{
            background: #2f2f2f;
            color: #e3e3e3;
            border: 1px solid #404040;
            border-radius: 16px;
            font-size: 11px;
            font-weight: bold;
        }}

        /* AI头像样式 */
        QLabel[objectName="ai_avatar"] {{
            background: #10a37f;
            color: white;
            border: none;
            border-radius: 16px;
            font-size: 12px;
            font-weight: bold;
        }}
        
        /* 输入指示器 */
        QLabel#typing_indicator {{
            color: #e2e8f0;
            font-size: 14px;
            background: transparent;
        }}
        
        /* 输入区域框架 */
        QFrame#input_frame {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(26, 31, 46, 0.9), stop:1 rgba(37, 43, 61, 0.9));
            border: 2px solid rgba(102, 126, 234, 0.4);
            border-radius: 28px;
            margin: 8px;
            padding: 6px;
        }}
        
        /* 聊天输入框 */
        QTextEdit#chat_input {{
            background: rgba(45, 53, 72, 0.3);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 18px;
            padding: 14px 18px;
            font-size: 15px;
            color: #f8fafc;
            font-family: 'Microsoft YaHei UI', 'Segoe UI', 'SF Pro Display', system-ui, sans-serif;
            line-height: 1.6;
            selection-background-color: rgba(102, 126, 234, 0.3);
            min-width: 0;
        }}

        QTextEdit#chat_input:focus {{
            background: rgba(45, 53, 72, 0.6);
            border: 2px solid rgba(102, 126, 234, 0.7);
        }}

        QTextEdit#chat_input:hover {{
            background: rgba(45, 53, 72, 0.4);
            border: 1px solid rgba(102, 126, 234, 0.4);
        }}

        /* 发送按钮 */
        QPushButton#send_button {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #667eea, stop:1 #764ba2);
            color: white;
            border: 2px solid rgba(255,255,255,0.1);
            border-radius: 25px;
            font-size: 13px;
            font-weight: 700;
            font-family: 'Microsoft YaHei UI', 'Segoe UI', system-ui, sans-serif;
            padding: 0;
        }}

        QPushButton#send_button:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #7c3aed, stop:1 #06d6a0);
            border: 2px solid rgba(255,255,255,0.4);
        }}

        QPushButton#send_button:pressed {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #5b21b6, stop:1 #059669);
        }}

        QPushButton#send_button:disabled {{
            background: rgba(45, 53, 72, 0.5);
            color: rgba(248, 250, 252, 0.5);
        }}
        
        /* 错误消息样式 */
        QFrame#error_message_frame {{
            background: rgba(220, 38, 38, 0.2);
            border: 1px solid rgba(220, 38, 38, 0.5);
            border-radius: 18px;
            margin: 5px 20px;
        }}

        QLabel#error_message {{
            color: #f87171;
            font-size: 14px;
            line-height: 1.6;
            background: transparent;
        }}
        """
    
    def get_persona_selector_stylesheet(self) -> str:
        return f"""
        #persona_selector {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {self.colors['surface']},
                stop:1 {self.colors['surface_variant']});
            border-bottom: 2px solid {self.colors['border']};
            border-radius: 0px 0px 12px 12px;
            padding: 8px;
        }}

        #persona_label {{
            color: {self.colors['text_primary']};
            font-weight: 600;
            font-size: 14px;
            padding: 4px;
        }}

        #persona_combo {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {self.colors['background']},
                stop:1 {self.colors['surface']});
            color: {self.colors['text_primary']};
            border: 2px solid {self.colors['border']};
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 14px;
            font-weight: 500;
        }}

        #persona_combo:hover {{
            border: 2px solid {self.colors['primary']};
            background: {self.colors['background']};
        }}
        
        #persona_combo::drop-down {{
            border: none;
            width: 20px;
        }}
        
        #persona_combo::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {self.colors['text_secondary']};
        }}
        
        #persona_combo QAbstractItemView {{
            background-color: {self.colors['surface']};
            color: {self.colors['text_primary']};
            border: 1px solid {self.colors['border']};
            selection-background-color: {self.colors['primary']};
        }}
        
        #persona_description {{
            color: {self.colors['text_secondary']};
            font-size: 12px;
        }}
        
        #nsfw_indicator {{
            font-size: 16px;
        }}
        
        #refresh_button {{
            background-color: transparent;
            border: 1px solid {self.colors['border']};
            border-radius: 15px;
            color: {self.colors['text_secondary']};
        }}
        
        #refresh_button:hover {{
            background-color: {self.colors['hover']};
            color: {self.colors['text_primary']};
        }}
        """
    
    def get_status_bar_stylesheet(self) -> str:
        return f"""
        #status_bar {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {self.colors['surface']},
                stop:1 {self.colors['surface_variant']});
            border-top: 2px solid {self.colors['border']};
            border-radius: 0px 0px 12px 12px;
            padding: 4px 8px;
        }}

        #status_label {{
            color: {self.colors['text_primary']};
            font-size: 12px;
            font-weight: 500;
            padding: 2px 6px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 transparent,
                stop:1 {self.colors['surface']});
            border-radius: 6px;
        }}

        #persona_label, #model_label, #token_label, #time_label {{
            color: {self.colors['text_primary']};
            font-size: 12px;
            padding: 2px 4px;
            background: {self.colors['surface']};
            border-radius: 4px;
            border: 1px solid {self.colors['border']};
        }}

        #separator {{
            color: {self.colors['border']};
            font-size: 11px;
            font-weight: bold;
        }}
        """

class LightTheme(BaseTheme):
    """浅色主题"""
    
    def __init__(self):
        super().__init__()
        self.colors = {
            'background': '#ffffff',
            'surface': '#f5f5f5',
            'surface_variant': '#e0e0e0',
            'primary': '#1976d2',
            'primary_variant': '#1565c0',
            'secondary': '#03dac6',
            'text_primary': '#212121',
            'text_secondary': '#757575',
            'text_disabled': '#9e9e9e',
            'border': '#e0e0e0',
            'hover': '#f0f0f0',
            'selected': '#1976d2',
            'error': '#d32f2f',
            'success': '#388e3c',
            'warning': '#f57c00'
        }
    
    def get_main_stylesheet(self) -> str:
        return f"""
        QMainWindow {{
            background-color: {self.colors['background']};
            color: {self.colors['text_primary']};
        }}
        
        QWidget {{
            background-color: transparent;
            color: {self.colors['text_primary']};
            font-family: 'Microsoft YaHei UI', 'Segoe UI', sans-serif;
        }}
        """
    
    # 其他方法类似深色主题，但使用浅色配色方案
    def get_sidebar_stylesheet(self) -> str:
        # 浅色主题的侧边栏样式
        return self._get_light_sidebar_styles()
    
    def _get_light_sidebar_styles(self) -> str:
        """获取浅色侧边栏样式"""
        # 这里可以实现浅色主题的具体样式
        # 为了简化，暂时返回基础样式
        return f"""
        #sidebar {{
            background-color: {self.colors['surface']};
            border-right: 1px solid {self.colors['border']};
        }}
        """
    
    # 其他样式方法可以类似实现...

class ForestTheme(BaseTheme):
    """森林主题 - 绿意盎然的设计"""

    def __init__(self):
        super().__init__()
        self.colors = {
            # 主要背景色 - 沉稳的森林色调
            'background': '#2d3a32',      # 深绿背景
            'surface': '#3c4d43',         # 卡片背景
            'surface_variant': '#4a5f54', # 变体表面
            'surface_elevated': '#587165', # 提升表面

            # 主色调 - 自然的绿色渐变
            'primary': '#4caf50',         # 主色调 (活力绿)
            'primary_variant': '#388e3c', # 深主色
            'primary_light': '#66bb6a',   # 浅主色
            'secondary': '#81c784',       # 辅助色 (薄荷绿)
            'accent': '#ffb74d',          # 强调色 (暖橙)

            # 文本颜色
            'text_primary': '#e8f5e9',    # 主要文本 (淡绿白)
            'text_secondary': '#a5d6a7',  # 次要文本 (浅绿)
            'text_disabled': '#616161',   # 禁用文本
            'text_hint': '#81c784',       # 提示文本

            # 边框和分割线
            'border': '#4a5f54',          # 边框
            'border_light': '#587165',    # 浅边框
            'divider': '#4a5f54',         # 分割线

            # 交互状态
            'hover': '#587165',           # 悬停背景
            'pressed': '#388e3c',         # 按下背景
            'selected': '#4caf50',        # 选中背景
            'focus': '#66bb6a',           # 焦点颜色

            # 状态颜色
            'error': '#e57373',           # 错误色
            'success': '#66bb6a',         # 成功色
            'warning': '#ffb74d',         # 警告色
            'info': '#4fc3f7',            # 信息色

            # 渐变色
            'gradient_primary': 'qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #4caf50, stop:1 #388e3c)',
            'gradient_secondary': 'qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #81c784, stop:1 #66bb6a)',
            'gradient_accent': 'qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ffb74d, stop:1 #ff9800)',
            'gradient_background': 'qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #2d3a32, stop:1 #3c4d43)',

            # 特殊效果
            'glass_bg': 'rgba(60, 77, 67, 0.8)',  # 毛玻璃背景
        }
    
    def get_main_stylesheet(self) -> str:
        # 复用深色主题的结构，但使用森林配色
        dark_theme_styles = DarkTheme().get_main_stylesheet()
        return self._replace_colors(dark_theme_styles)

    def get_sidebar_stylesheet(self) -> str:
        dark_theme_styles = DarkTheme().get_sidebar_stylesheet()
        return self._replace_colors(dark_theme_styles)

    def get_chat_stylesheet(self) -> str:
        dark_theme_styles = DarkTheme().get_chat_stylesheet()
        return self._replace_colors(dark_theme_styles)

    def get_persona_selector_stylesheet(self) -> str:
        dark_theme_styles = DarkTheme().get_persona_selector_stylesheet()
        return self._replace_colors(dark_theme_styles)

    def get_status_bar_stylesheet(self) -> str:
        dark_theme_styles = DarkTheme().get_status_bar_stylesheet()
        return self._replace_colors(dark_theme_styles)

    def _replace_colors(self, stylesheet: str) -> str:
        # 这是一个简化的替换，实际应用可能需要更精细的调整
        # 这里我们用森林主题的颜色替换深色主题的颜色
        temp_theme = DarkTheme()
        for color_name, color_value in temp_theme.colors.items():
            if color_name in self.colors:
                stylesheet = stylesheet.replace(color_value, self.colors[color_name])
        return stylesheet

class OceanTheme(BaseTheme):
    """海洋主题 - 清爽的蓝色调"""

    def __init__(self):
        super().__init__()
        self.colors = {
            # 主要背景色 - 深海蓝
            'background': '#1a233a',      # 深海背景
            'surface': '#23314e',         # 卡片背景
            'surface_variant': '#2d3f63', # 变体表面
            'surface_elevated': '#3a5078', # 提升表面

            # 主色调 - 活力的蓝色渐变
            'primary': '#2196f3',         # 主色调 (活力蓝)
            'primary_variant': '#1976d2', # 深主色
            'primary_light': '#64b5f6',   # 浅主色
            'secondary': '#00bcd4',       # 辅助色 (青色)
            'accent': '#ffeb3b',          # 强调色 (柠檬黄)

            # 文本颜色
            'text_primary': '#e3f2fd',    # 主要文本 (淡蓝白)
            'text_secondary': '#90caf9',  # 次要文本 (浅蓝)
            'text_disabled': '#4b5a76',   # 禁用文本
            'text_hint': '#64b5f6',       # 提示文本

            # 边框和分割线
            'border': '#2d3f63',          # 边框
            'border_light': '#3a5078',    # 浅边框
            'divider': '#2d3f63',         # 分割线

            # 交互状态
            'hover': '#3a5078',           # 悬停背景
            'pressed': '#1976d2',         # 按下背景
            'selected': '#2196f3',        # 选中背景
            'focus': '#64b5f6',           # 焦点颜色

            # 状态颜色
            'error': '#ef5350',           # 错误色
            'success': '#4dd0e1',         # 成功色
            'warning': '#ffeb3b',         # 警告色
            'info': '#64b5f6',            # 信息色

            # 渐变色
            'gradient_primary': 'qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #2196f3, stop:1 #1976d2)',
            'gradient_secondary': 'qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #00bcd4, stop:1 #0097a7)',
            'gradient_accent': 'qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ffeb3b, stop:1 #fdd835)',
            'gradient_background': 'qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1a233a, stop:1 #23314e)',

            # 特殊效果
            'glass_bg': 'rgba(35, 49, 78, 0.8)',  # 毛玻璃背景
        }

    def get_main_stylesheet(self) -> str:
        dark_theme_styles = DarkTheme().get_main_stylesheet()
        return self._replace_colors(dark_theme_styles)

    def get_sidebar_stylesheet(self) -> str:
        dark_theme_styles = DarkTheme().get_sidebar_stylesheet()
        return self._replace_colors(dark_theme_styles)

    def get_chat_stylesheet(self) -> str:
        dark_theme_styles = DarkTheme().get_chat_stylesheet()
        return self._replace_colors(dark_theme_styles)

    def get_persona_selector_stylesheet(self) -> str:
        dark_theme_styles = DarkTheme().get_persona_selector_stylesheet()
        return self._replace_colors(dark_theme_styles)

    def get_status_bar_stylesheet(self) -> str:
        dark_theme_styles = DarkTheme().get_status_bar_stylesheet()
        return self._replace_colors(dark_theme_styles)

    def _replace_colors(self, stylesheet: str) -> str:
        temp_theme = DarkTheme()
        for color_name, color_value in temp_theme.colors.items():
            if color_name in self.colors:
                stylesheet = stylesheet.replace(color_value, self.colors[color_name])
        return stylesheet
