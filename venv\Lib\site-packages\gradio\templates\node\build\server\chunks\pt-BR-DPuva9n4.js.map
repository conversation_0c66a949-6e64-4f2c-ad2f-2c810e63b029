{"version": 3, "file": "pt-BR-DPuva9n4.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/pt-BR.js"], "sourcesContent": ["const o=\"Português do Brasil\",a={built_with_gradio:\"Construído com Gradio\",clear:\"Limpar\",error:\"<PERSON>rro\",flag:\"<PERSON><PERSON>\",loading:\"Carregando\",or:\"ou\",submit:\"Enviar\",built_with:\"Construído com\",download:\"Baixar\",edit:\"Editar\",empty:\"Vazio\",hosted_on:\"Hospedado em\",logo:\"Logotipo\",settings:\"Configurações\",undo:\"Desfazer\",no_devices:\"Nenhum dispositivo encontrado\",language:\"Idioma\",display_theme:\"Tema de exibição\",pwa:\"Aplicativo web progressivo\",share:\"Compartilhar\",remove:\"Remover\"},e={click_to_upload:\"Clique para fazer upload\",drop_audio:\"Solte o áudio aqui\",drop_csv:\"Solte o CSV aqui\",drop_file:\"Solte o arquivo aqui\",drop_image:\"Solte a imagem aqui\",drop_video:\"Solte o vídeo aqui\",drop_gallery:\"Solte a mídia aqui\",paste_clipboard:\"Colar da área de transferência\"},r={annotated_image:\"Imagem anotada\"},i={allow_recording_access:\"Por favor, permita o acesso ao microfone para gravação.\",audio:\"Áudio\",drop_to_upload:\"Solte um arquivo de áudio aqui para fazer upload\",record_from_microphone:\"Gravar do microfone\",stop_recording:\"Parar gravação\",no_device_support:\"Os dispositivos de mídia não puderam ser acessados. Verifique se você está executando em uma origem segura (https) ou localhost (ou se forneceu um certificado SSL válido para ssl_verify), e se permitiu o acesso do navegador ao seu dispositivo.\",stop:\"Parar\",resume:\"Retomar\",record:\"Gravar\",no_microphone:\"Nenhum microfone encontrado\",pause:\"Pausar\",play:\"Reproduzir\",waiting:\"Aguardando\"},d={connection_can_break:\"No celular, a conexão pode ser interrompida se esta aba ficar em segundo plano ou se o dispositivo entrar em modo de repouso, fazendo você perder sua posição na fila.\",long_requests_queue:\"Há uma longa fila de solicitações pendentes. Duplique este Space para pular a fila.\",lost_connection:\"Conexão perdida ao sair da página. Retornando para a fila...\",waiting_for_inputs:\"Aguardando o(s) arquivo(s) terminar(em) de ser carregado(s), por favor, tente novamente.\"},t={checkbox:\"Caixa de seleção\",checkbox_group:\"Grupo de caixas de seleção\"},n={code:\"Código\"},s={color_picker:\"Seletor de cor\"},c={incorrect_format:\"Formato incorreto, apenas arquivos CSV e TSV são suportados\",new_column:\"Adicionar coluna\",new_row:\"Nova linha\",add_row_above:\"Adicionar linha acima\",add_row_below:\"Adicionar linha abaixo\",delete_row:\"Excluir linha\",delete_column:\"Excluir coluna\",add_column_left:\"Adicionar coluna à esquerda\",add_column_right:\"Adicionar coluna à direita\",sort_column:\"Classificar coluna\",sort_ascending:\"Ordenar em ordem crescente\",sort_descending:\"Ordenar em ordem decrescente\",drop_to_upload:\"Solte arquivos CSV ou TSV aqui para importar dados para o dataframe\",clear_sort:\"Limpar classificação\",filter:\"Filtrar\",clear_filter:\"Limpar filtros\"},l={dropdown:\"Menu suspenso\"},u={build_error:\"Houve um erro de compilação\",config_error:\"Há um erro de configuração\",contact_page_author:\"Por favor, entre em contato com o autor da página para informá-lo.\",no_app_file:\"Não há arquivo de aplicativo\",space_not_working:'\"O Space não está funcionando porque\" {0}',space_paused:\"O Space está pausado\",use_via_api:\"Usar via API\",use_via_api_or_mcp:\"Usar via API ou MCP\",runtime_error:\"Houve um erro de execução\"},p={uploading:\"Carregando...\"},m={highlighted_text:\"Texto destacado\"},_={allow_webcam_access:\"Por favor, permita o acesso à webcam para gravação.\",brush_color:\"Cor do pincel\",brush_radius:\"Tamanho do pincel\",image:\"Imagem\",remove_image:\"Remover imagem\",select_brush_color:\"Selecione a cor do pincel\",start_drawing:\"Começar a desenhar\",use_brush:\"Usar pincel\",drop_to_upload:\"Solte um arquivo de imagem aqui para fazer upload\"},g={label:\"Rótulo\"},v={enable_cookies:\"Se você estiver visitando um HuggingFace Space no modo incógnito, você deve habilitar cookies de terceiros.\",incorrect_credentials:\"Credenciais incorretas\",username:\"Nome de usuário\",password:\"Senha\",login:\"Entrar\"},f={number:\"Número\"},h={plot:\"Gráfico\"},b={radio:\"Botão de opção\"},q={slider:\"Controle deslizante\"},S={drop_to_upload:\"Solte um arquivo de vídeo aqui para fazer upload\"},w={edit:\"Editar\",retry:\"Tentar novamente\",undo:\"Desfazer\",submit:\"Enviar\",cancel:\"Cancelar\",like:\"Gostei\",dislike:\"Não gostei\",clear:\"Limpar conversa\"},x={_name:o,common:a,upload_text:e,\"3D_model\":{\"3d_model\":\"Modelo 3D\",drop_to_upload:\"Solte um arquivo de modelo 3D (.obj, .glb, .stl, .gltf, .splat ou .ply) aqui para fazer upload\"},annotated_image:r,audio:i,blocks:d,checkbox:t,code:n,color_picker:s,dataframe:c,dropdown:l,errors:u,file:p,highlighted_text:m,image:_,label:g,login:v,number:f,plot:h,radio:b,slider:q,video:S,chatbot:w};export{o as _name,r as annotated_image,i as audio,d as blocks,w as chatbot,t as checkbox,n as code,s as color_picker,a as common,c as dataframe,x as default,l as dropdown,u as errors,p as file,m as highlighted_text,_ as image,g as label,v as login,f as number,h as plot,b as radio,q as slider,e as upload_text,S as video};\n//# sourceMappingURL=pt-BR.js.map\n"], "names": [], "mappings": "AAAK,MAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,+BAA+B,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,kBAAkB,CAAC,GAAG,CAAC,4BAA4B,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,0BAA0B,CAAC,UAAU,CAAC,oBAAoB,CAAC,QAAQ,CAAC,kBAAkB,CAAC,SAAS,CAAC,sBAAsB,CAAC,UAAU,CAAC,qBAAqB,CAAC,UAAU,CAAC,oBAAoB,CAAC,YAAY,CAAC,oBAAoB,CAAC,eAAe,CAAC,gCAAgC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,yDAAyD,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,kDAAkD,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,cAAc,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,qPAAqP,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,6BAA6B,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,wKAAwK,CAAC,mBAAmB,CAAC,qFAAqF,CAAC,eAAe,CAAC,8DAA8D,CAAC,kBAAkB,CAAC,0FAA0F,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC,cAAc,CAAC,4BAA4B,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,6DAA6D,CAAC,UAAU,CAAC,kBAAkB,CAAC,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC,uBAAuB,CAAC,aAAa,CAAC,wBAAwB,CAAC,UAAU,CAAC,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC,eAAe,CAAC,6BAA6B,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,WAAW,CAAC,oBAAoB,CAAC,cAAc,CAAC,4BAA4B,CAAC,eAAe,CAAC,8BAA8B,CAAC,cAAc,CAAC,qEAAqE,CAAC,UAAU,CAAC,sBAAsB,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,6BAA6B,CAAC,YAAY,CAAC,4BAA4B,CAAC,mBAAmB,CAAC,oEAAoE,CAAC,WAAW,CAAC,8BAA8B,CAAC,iBAAiB,CAAC,2CAA2C,CAAC,YAAY,CAAC,sBAAsB,CAAC,WAAW,CAAC,cAAc,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,aAAa,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,qDAAqD,CAAC,WAAW,CAAC,eAAe,CAAC,YAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,2BAA2B,CAAC,aAAa,CAAC,oBAAoB,CAAC,SAAS,CAAC,aAAa,CAAC,cAAc,CAAC,mDAAmD,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,6GAA6G,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,kDAAkD,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,cAAc,CAAC,gGAAgG,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;;;;"}