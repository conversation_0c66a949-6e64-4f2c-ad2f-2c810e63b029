import{c as t}from"./KHR_interactivity-DTxiAnOo.js";import{R as r}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./declarationMapper-BZjsjg7g.js";import"./objectModelMapping-BR4RdEzn.js";class o extends t{constructor(){super(...arguments),this.initPriority=-1,this.type="SceneReady"}_executeEvent(e,a){return this._execute(e),!0}_preparePendingTasks(e){}_cancelPendingTasks(e){}getClassName(){return"FlowGraphSceneReadyEventBlock"}}r("FlowGraphSceneReadyEventBlock",o);export{o as FlowGraphSceneReadyEventBlock};
//# sourceMappingURL=flowGraphSceneReadyEventBlock-Dr09mBVz.js.map
