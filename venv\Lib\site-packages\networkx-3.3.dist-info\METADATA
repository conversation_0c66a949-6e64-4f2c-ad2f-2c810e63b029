Metadata-Version: 2.1
Name: networkx
Version: 3.3
Summary: Python package for creating and manipulating graphs and networks
Author-email: <PERSON><PERSON> <<EMAIL>>
Maintainer-email: NetworkX Developers <<EMAIL>>
Project-URL: Homepage, https://networkx.org/
Project-URL: Bug Tracker, https://github.com/networkx/networkx/issues
Project-URL: Documentation, https://networkx.org/documentation/stable/
Project-URL: Source Code, https://github.com/networkx/networkx
Keywords: Networks,Graph Theory,Mathematics,network,graph,discrete mathematics,math
Platform: Linux
Platform: Mac OSX
Platform: Windows
Platform: Unix
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Scientific/Engineering :: Bio-Informatics
Classifier: Topic :: Scientific/Engineering :: Information Analysis
Classifier: Topic :: Scientific/Engineering :: Mathematics
Classifier: Topic :: Scientific/Engineering :: Physics
Requires-Python: >=3.10
Description-Content-Type: text/x-rst
License-File: LICENSE.txt
Provides-Extra: default
Requires-Dist: numpy >=1.23 ; extra == 'default'
Requires-Dist: scipy !=1.11.0,!=1.11.1,>=1.9 ; extra == 'default'
Requires-Dist: matplotlib >=3.6 ; extra == 'default'
Requires-Dist: pandas >=1.4 ; extra == 'default'
Provides-Extra: developer
Requires-Dist: changelist ==0.5 ; extra == 'developer'
Requires-Dist: pre-commit >=3.2 ; extra == 'developer'
Requires-Dist: mypy >=1.1 ; extra == 'developer'
Requires-Dist: rtoml ; extra == 'developer'
Provides-Extra: doc
Requires-Dist: sphinx >=7 ; extra == 'doc'
Requires-Dist: pydata-sphinx-theme >=0.14 ; extra == 'doc'
Requires-Dist: sphinx-gallery >=0.14 ; extra == 'doc'
Requires-Dist: numpydoc >=1.7 ; extra == 'doc'
Requires-Dist: pillow >=9.4 ; extra == 'doc'
Requires-Dist: texext >=0.6.7 ; extra == 'doc'
Requires-Dist: myst-nb >=1.0 ; extra == 'doc'
Provides-Extra: extra
Requires-Dist: lxml >=4.6 ; extra == 'extra'
Requires-Dist: pygraphviz >=1.12 ; extra == 'extra'
Requires-Dist: pydot >=2.0 ; extra == 'extra'
Requires-Dist: sympy >=1.10 ; extra == 'extra'
Provides-Extra: test
Requires-Dist: pytest >=7.2 ; extra == 'test'
Requires-Dist: pytest-cov >=4.0 ; extra == 'test'

NetworkX
========


.. image:: https://github.com/networkx/networkx/workflows/test/badge.svg?branch=main
  :target: https://github.com/networkx/networkx/actions?query=workflow%3A%22test%22

.. image:: https://codecov.io/gh/networkx/networkx/branch/main/graph/badge.svg
   :target: https://app.codecov.io/gh/networkx/networkx/branch/main
   
.. image:: https://img.shields.io/github/labels/networkx/networkx/Good%20First%20Issue?color=green&label=Contribute%20&style=flat-square
   :target: https://github.com/networkx/networkx/issues?q=is%3Aopen+is%3Aissue+label%3A%22Good+First+Issue%22
   

NetworkX is a Python package for the creation, manipulation,
and study of the structure, dynamics, and functions
of complex networks.

- **Website (including documentation):** https://networkx.org
- **Mailing list:** https://groups.google.com/forum/#!forum/networkx-discuss
- **Source:** https://github.com/networkx/networkx
- **Bug reports:** https://github.com/networkx/networkx/issues
- **Report a security vulnerability:** https://tidelift.com/security
- **Tutorial:** https://networkx.org/documentation/latest/tutorial.html
- **GitHub Discussions:** https://github.com/networkx/networkx/discussions

Simple example
--------------

Find the shortest path between two nodes in an undirected graph:

.. code:: pycon

    >>> import networkx as nx
    >>> G = nx.Graph()
    >>> G.add_edge("A", "B", weight=4)
    >>> G.add_edge("B", "D", weight=2)
    >>> G.add_edge("A", "C", weight=3)
    >>> G.add_edge("C", "D", weight=4)
    >>> nx.shortest_path(G, "A", "D", weight="weight")
    ['A', 'B', 'D']

Install
-------

Install the latest version of NetworkX::

    $ pip install networkx

Install with all optional dependencies::

    $ pip install networkx[all]

For additional details, please see `INSTALL.rst`.

Bugs
----

Please report any bugs that you find `here <https://github.com/networkx/networkx/issues>`_.
Or, even better, fork the repository on `GitHub <https://github.com/networkx/networkx>`_
and create a pull request (PR). We welcome all changes, big or small, and we
will help you make the PR if you are new to `git` (just ask on the issue and/or
see `CONTRIBUTING.rst`).

License
-------

Released under the 3-Clause BSD license (see `LICENSE.txt`)::

   Copyright (C) 2004-2024 NetworkX Developers
   Aric Hagberg <<EMAIL>>
   Dan Schult <<EMAIL>>
   Pieter Swart <<EMAIL>>
