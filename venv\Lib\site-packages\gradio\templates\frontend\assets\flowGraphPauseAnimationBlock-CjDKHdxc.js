import{b as o}from"./KHR_interactivity-DTxiAnOo.js";import{R as t}from"./declarationMapper-BZjsjg7g.js";import{R as i}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class e extends o{constructor(a){super(a),this.animationToPause=this.registerDataInput("animationToPause",t)}_execute(a){this.animationToPause.getValue(a).pause(),this.out._activateSignal(a)}getClassName(){return"FlowGraphPauseAnimationBlock"}}i("FlowGraphPauseAnimationBlock",e);export{e as FlowGraphPauseAnimationBlock};
//# sourceMappingURL=flowGraphPauseAnimationBlock-CjDKHdxc.js.map
