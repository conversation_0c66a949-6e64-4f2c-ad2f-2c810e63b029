import{ar as o,an as m,ao as h}from"./index-Dpxo-yl_.js";import{GLTFLoader as u}from"./glTFLoader-9Z3KGax5.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./bone-kZWM5-u7.js";import"./rawTexture-DmvUfjqF.js";import"./assetContainer-BRzQBugc.js";import"./objectModelMapping-BR4RdEzn.js";const r="KHR_materials_iridescence";class T{constructor(i){this.name=r,this.order=195,this._loader=i,this.enabled=this._loader.isExtensionUsed(r)}dispose(){this._loader=null}loadMaterialPropertiesAsync(i,s,e){return u.LoadExtensionAsync(i,s,this.name,(n,c)=>{const d=new Array;return d.push(this._loader.loadMaterialPropertiesAsync(i,s,e)),d.push(this._loadIridescencePropertiesAsync(n,c,e)),Promise.all(d).then(()=>{})})}_loadIridescencePropertiesAsync(i,s,e){if(!(e instanceof o))throw new Error(`${i}: Material type not supported`);const n=new Array;return e.iridescence.isEnabled=!0,e.iridescence.intensity=s.iridescenceFactor??0,e.iridescence.indexOfRefraction=s.iridescenceIor??s.iridescenceIOR??1.3,e.iridescence.minimumThickness=s.iridescenceThicknessMinimum??100,e.iridescence.maximumThickness=s.iridescenceThicknessMaximum??400,s.iridescenceTexture&&n.push(this._loader.loadTextureInfoAsync(`${i}/iridescenceTexture`,s.iridescenceTexture,c=>{c.name=`${e.name} (Iridescence)`,e.iridescence.texture=c})),s.iridescenceThicknessTexture&&n.push(this._loader.loadTextureInfoAsync(`${i}/iridescenceThicknessTexture`,s.iridescenceThicknessTexture,c=>{c.name=`${e.name} (Iridescence Thickness)`,e.iridescence.thicknessTexture=c})),Promise.all(n).then(()=>{})}}m(r);h(r,!0,t=>new T(t));export{T as KHR_materials_iridescence};
//# sourceMappingURL=KHR_materials_iridescence-CB24yK8h.js.map
