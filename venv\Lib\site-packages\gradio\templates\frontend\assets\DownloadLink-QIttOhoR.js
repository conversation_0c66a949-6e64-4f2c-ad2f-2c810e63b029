import{g as M}from"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import{s as S,g as T}from"./file-url-DoxvUUVV.js";const{SvelteComponent:V,assign:h,check_outros:C,compute_rest_props:D,create_slot:y,detach:k,element:N,empty:O,exclude_internal_props:z,flush:W,get_all_dirty_from_scope:E,get_slot_changes:L,get_spread_update:P,group_outros:B,init:A,insert:w,listen:F,prevent_default:I,safe_not_equal:J,set_attributes:p,set_style:j,toggle_class:b,transition_in:d,transition_out:m,update_slot_base:R}=window.__gradio__svelte__internal,{createEventDispatcher:K,onMount:te}=window.__gradio__svelte__internal;function Q(f){let e,t,o,r,u;const i=f[8].default,s=y(i,f,f[7],null);let a=[{class:"download-link"},{href:f[0]},{target:t=typeof window<"u"&&window.__is_colab__?"_blank":null},{rel:"noopener noreferrer"},{download:f[1]},f[6]],l={};for(let n=0;n<a.length;n+=1)l=h(l,a[n]);return{c(){e=N("a"),s&&s.c(),p(e,l),j(e,"position","relative"),b(e,"svelte-1s8vnbx",!0)},m(n,_){w(n,e,_),s&&s.m(e,null),o=!0,r||(u=F(e,"click",f[3].bind(null,"click")),r=!0)},p(n,_){s&&s.p&&(!o||_&128)&&R(s,i,n,n[7],o?L(i,n[7],_,null):E(n[7]),null),p(e,l=P(a,[{class:"download-link"},(!o||_&1)&&{href:n[0]},{target:t},{rel:"noopener noreferrer"},(!o||_&2)&&{download:n[1]},_&64&&n[6]])),j(e,"position","relative"),b(e,"svelte-1s8vnbx",!0)},i(n){o||(d(s,n),o=!0)},o(n){m(s,n),o=!1},d(n){n&&k(e),s&&s.d(n),r=!1,u()}}}function X(f){let e,t,o,r;const u=[Z,Y],i=[];function s(a,l){return a[2]?0:1}return e=s(f),t=i[e]=u[e](f),{c(){t.c(),o=O()},m(a,l){i[e].m(a,l),w(a,o,l),r=!0},p(a,l){let n=e;e=s(a),e===n?i[e].p(a,l):(B(),m(i[n],1,1,()=>{i[n]=null}),C(),t=i[e],t?t.p(a,l):(t=i[e]=u[e](a),t.c()),d(t,1),t.m(o.parentNode,o))},i(a){r||(d(t),r=!0)},o(a){m(t),r=!1},d(a){a&&k(o),i[e].d(a)}}}function Y(f){let e,t,o,r;const u=f[8].default,i=y(u,f,f[7],null);let s=[f[6],{href:f[0]}],a={};for(let l=0;l<s.length;l+=1)a=h(a,s[l]);return{c(){e=N("a"),i&&i.c(),p(e,a),b(e,"svelte-1s8vnbx",!0)},m(l,n){w(l,e,n),i&&i.m(e,null),t=!0,o||(r=F(e,"click",I(f[5])),o=!0)},p(l,n){i&&i.p&&(!t||n&128)&&R(i,u,l,l[7],t?L(u,l[7],n,null):E(l[7]),null),p(e,a=P(s,[n&64&&l[6],(!t||n&1)&&{href:l[0]}])),b(e,"svelte-1s8vnbx",!0)},i(l){t||(d(i,l),t=!0)},o(l){m(i,l),t=!1},d(l){l&&k(e),i&&i.d(l),o=!1,r()}}}function Z(f){let e;const t=f[8].default,o=y(t,f,f[7],null);return{c(){o&&o.c()},m(r,u){o&&o.m(r,u),e=!0},p(r,u){o&&o.p&&(!e||u&128)&&R(o,t,r,r[7],e?L(t,r[7],u,null):E(r[7]),null)},i(r){e||(d(o,r),e=!0)},o(r){m(o,r),e=!1},d(r){o&&o.d(r)}}}function $(f){let e,t,o,r,u;const i=[X,Q],s=[];function a(l,n){return n&1&&(e=null),e==null&&(e=!!(l[4]&&S(l[0]))),e?0:1}return t=a(f,-1),o=s[t]=i[t](f),{c(){o.c(),r=O()},m(l,n){s[t].m(l,n),w(l,r,n),u=!0},p(l,[n]){let _=t;t=a(l,n),t===_?s[t].p(l,n):(B(),m(s[_],1,1,()=>{s[_]=null}),C(),o=s[t],o?o.p(l,n):(o=s[t]=i[t](l),o.c()),d(o,1),o.m(r.parentNode,r))},i(l){u||(d(o),u=!0)},o(l){m(o),u=!1},d(l){l&&k(r),s[t].d(l)}}}function x(f,e,t){const o=["href","download"];let r=D(e,o),{$$slots:u={},$$scope:i}=e,{href:s=void 0}=e,{download:a}=e;const l=K();let n=!1;const _=M();async function G(){if(n)return;if(l("click"),s==null)throw new Error("href is not defined.");if(_==null)throw new Error("Wasm worker proxy is not available.");const U=new URL(s,window.location.href).pathname;t(2,n=!0),_.httpRequest({method:"GET",path:U,headers:{},query_string:""}).then(g=>{if(g.status!==200)throw new Error(`Failed to get file ${U} from the Wasm worker.`);const H=new Blob([g.body],{type:T(g.headers,"content-type")}),q=URL.createObjectURL(H),v=document.createElement("a");v.href=q,v.download=a,v.click(),URL.revokeObjectURL(q)}).finally(()=>{t(2,n=!1)})}return f.$$set=c=>{e=h(h({},e),z(c)),t(6,r=D(e,o)),"href"in c&&t(0,s=c.href),"download"in c&&t(1,a=c.download),"$$scope"in c&&t(7,i=c.$$scope)},[s,a,n,l,_,G,r,i,u]}class oe extends V{constructor(e){super(),A(this,e,x,$,J,{href:0,download:1})}get href(){return this.$$.ctx[0]}set href(e){this.$$set({href:e}),W()}get download(){return this.$$.ctx[1]}set download(e){this.$$set({download:e}),W()}}export{oe as D};
//# sourceMappingURL=DownloadLink-QIttOhoR.js.map
