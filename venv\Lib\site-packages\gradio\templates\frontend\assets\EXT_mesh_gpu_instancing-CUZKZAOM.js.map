{"version": 3, "file": "EXT_mesh_gpu_instancing-CUZKZAOM.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/EXT_mesh_gpu_instancing.js"], "sourcesContent": ["import { Vector3, <PERSON><PERSON>rn<PERSON>, <PERSON>, TmpVectors } from \"@babylonjs/core/Maths/math.vector.js\";\nimport { GLTFLoader, ArrayItem } from \"../glTFLoader.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nimport \"@babylonjs/core/Meshes/thinInstanceMesh.js\";\nconst NAME = \"EXT_mesh_gpu_instancing\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Vendor/EXT_mesh_gpu_instancing/README.md)\n * [Playground Sample](https://playground.babylonjs.com/#QFIGLW#9)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class EXT_mesh_gpu_instancing {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /**\n     * @internal\n     */\n    loadNodeAsync(context, node, assign) {\n        return GLTFLoader.LoadExtensionAsync(context, node, this.name, (extensionContext, extension) => {\n            this._loader._disableInstancedMesh++;\n            const promise = this._loader.loadNodeAsync(`/nodes/${node.index}`, node, assign);\n            this._loader._disableInstancedMesh--;\n            if (!node._primitiveBabylonMeshes) {\n                return promise;\n            }\n            const promises = new Array();\n            let instanceCount = 0;\n            const loadAttribute = (attribute) => {\n                if (extension.attributes[attribute] == undefined) {\n                    promises.push(Promise.resolve(null));\n                    return;\n                }\n                const accessor = ArrayItem.Get(`${extensionContext}/attributes/${attribute}`, this._loader.gltf.accessors, extension.attributes[attribute]);\n                promises.push(this._loader._loadFloatAccessorAsync(`/accessors/${accessor.bufferView}`, accessor));\n                if (instanceCount === 0) {\n                    instanceCount = accessor.count;\n                }\n                else if (instanceCount !== accessor.count) {\n                    throw new Error(`${extensionContext}/attributes: Instance buffer accessors do not have the same count.`);\n                }\n            };\n            loadAttribute(\"TRANSLATION\");\n            loadAttribute(\"ROTATION\");\n            loadAttribute(\"SCALE\");\n            return promise.then((babylonTransformNode) => {\n                return Promise.all(promises).then(([translationBuffer, rotationBuffer, scaleBuffer]) => {\n                    const matrices = new Float32Array(instanceCount * 16);\n                    TmpVectors.Vector3[0].copyFromFloats(0, 0, 0); // translation\n                    TmpVectors.Quaternion[0].copyFromFloats(0, 0, 0, 1); // rotation\n                    TmpVectors.Vector3[1].copyFromFloats(1, 1, 1); // scale\n                    for (let i = 0; i < instanceCount; ++i) {\n                        translationBuffer && Vector3.FromArrayToRef(translationBuffer, i * 3, TmpVectors.Vector3[0]);\n                        rotationBuffer && Quaternion.FromArrayToRef(rotationBuffer, i * 4, TmpVectors.Quaternion[0]);\n                        scaleBuffer && Vector3.FromArrayToRef(scaleBuffer, i * 3, TmpVectors.Vector3[1]);\n                        Matrix.ComposeToRef(TmpVectors.Vector3[1], TmpVectors.Quaternion[0], TmpVectors.Vector3[0], TmpVectors.Matrix[0]);\n                        TmpVectors.Matrix[0].copyToArray(matrices, i * 16);\n                    }\n                    for (const babylonMesh of node._primitiveBabylonMeshes) {\n                        babylonMesh.thinInstanceSetBuffer(\"matrix\", matrices, 16, true);\n                    }\n                    return babylonTransformNode;\n                });\n            });\n        });\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new EXT_mesh_gpu_instancing(loader));\n//# sourceMappingURL=EXT_mesh_gpu_instancing.js.map"], "names": ["NAME", "EXT_mesh_gpu_instancing", "loader", "context", "node", "assign", "GLTFLoader", "extensionContext", "extension", "promise", "promises", "instanceCount", "loadAttribute", "attribute", "accessor", "ArrayItem", "babylonTransformNode", "translation<PERSON>uffer", "rotationBuffer", "scaleBuffer", "matrices", "TmpVectors", "i", "Vector3", "Quaternion", "Matrix", "<PERSON><PERSON><PERSON><PERSON>", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "0XAIA,MAAMA,EAAO,0BAMN,MAAMC,CAAwB,CAIjC,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EACZ,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,CACnD,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAID,cAAcG,EAASC,EAAMC,EAAQ,CACjC,OAAOC,EAAW,mBAAmBH,EAASC,EAAM,KAAK,KAAM,CAACG,EAAkBC,IAAc,CAC5F,KAAK,QAAQ,wBACb,MAAMC,EAAU,KAAK,QAAQ,cAAc,UAAUL,EAAK,KAAK,GAAIA,EAAMC,CAAM,EAE/E,GADA,KAAK,QAAQ,wBACT,CAACD,EAAK,wBACN,OAAOK,EAEX,MAAMC,EAAW,IAAI,MACrB,IAAIC,EAAgB,EACpB,MAAMC,EAAiBC,GAAc,CACjC,GAAIL,EAAU,WAAWK,CAAS,GAAK,KAAW,CAC9CH,EAAS,KAAK,QAAQ,QAAQ,IAAI,CAAC,EACnC,MACH,CACD,MAAMI,EAAWC,EAAU,IAAI,GAAGR,CAAgB,eAAeM,CAAS,GAAI,KAAK,QAAQ,KAAK,UAAWL,EAAU,WAAWK,CAAS,CAAC,EAE1I,GADAH,EAAS,KAAK,KAAK,QAAQ,wBAAwB,cAAcI,EAAS,UAAU,GAAIA,CAAQ,CAAC,EAC7FH,IAAkB,EAClBA,EAAgBG,EAAS,cAEpBH,IAAkBG,EAAS,MAChC,MAAM,IAAI,MAAM,GAAGP,CAAgB,oEAAoE,CAE3H,EACY,OAAAK,EAAc,aAAa,EAC3BA,EAAc,UAAU,EACxBA,EAAc,OAAO,EACdH,EAAQ,KAAMO,GACV,QAAQ,IAAIN,CAAQ,EAAE,KAAK,CAAC,CAACO,EAAmBC,EAAgBC,CAAW,IAAM,CACpF,MAAMC,EAAW,IAAI,aAAaT,EAAgB,EAAE,EACpDU,EAAW,QAAQ,CAAC,EAAE,eAAe,EAAG,EAAG,CAAC,EAC5CA,EAAW,WAAW,CAAC,EAAE,eAAe,EAAG,EAAG,EAAG,CAAC,EAClDA,EAAW,QAAQ,CAAC,EAAE,eAAe,EAAG,EAAG,CAAC,EAC5C,QAASC,EAAI,EAAGA,EAAIX,EAAe,EAAEW,EACjCL,GAAqBM,EAAQ,eAAeN,EAAmBK,EAAI,EAAGD,EAAW,QAAQ,CAAC,CAAC,EAC3FH,GAAkBM,EAAW,eAAeN,EAAgBI,EAAI,EAAGD,EAAW,WAAW,CAAC,CAAC,EAC3FF,GAAeI,EAAQ,eAAeJ,EAAaG,EAAI,EAAGD,EAAW,QAAQ,CAAC,CAAC,EAC/EI,EAAO,aAAaJ,EAAW,QAAQ,CAAC,EAAGA,EAAW,WAAW,CAAC,EAAGA,EAAW,QAAQ,CAAC,EAAGA,EAAW,OAAO,CAAC,CAAC,EAChHA,EAAW,OAAO,CAAC,EAAE,YAAYD,EAAUE,EAAI,EAAE,EAErD,UAAWI,KAAetB,EAAK,wBAC3BsB,EAAY,sBAAsB,SAAUN,EAAU,GAAI,EAAI,EAElE,OAAOJ,CAC3B,CAAiB,CACJ,CACb,CAAS,CACJ,CACL,CACAW,EAAwB3B,CAAI,EAC5B4B,EAAsB5B,EAAM,GAAOE,GAAW,IAAID,EAAwBC,CAAM,CAAC", "x_google_ignoreList": [0]}