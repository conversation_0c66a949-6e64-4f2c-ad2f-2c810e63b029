// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only

import QtQuick

Gradient {
    GradientStop {
        position: 0
        color: "#ff0000"
    }
    GradientStop {
        position: 0.166666
        color: "#ffff00"
    }
    GradientStop {
        position: 0.333333
        color: "#00ff00"
    }
    GradientStop {
        position: 0.5
        color: "#00ffff"
    }
    GradientStop {
        position: 0.666666
        color: "#0000ff"
    }
    GradientStop {
        position: 0.833333
        color: "#ff00ff"
    }
    GradientStop {
        position: 1
        color: "#ff0000"
    }
}
