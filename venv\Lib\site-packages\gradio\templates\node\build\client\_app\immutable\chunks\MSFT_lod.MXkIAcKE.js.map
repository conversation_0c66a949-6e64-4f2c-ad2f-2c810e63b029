{"version": 3, "file": "MSFT_lod.MXkIAcKE.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/MSFT_lod.js"], "sourcesContent": ["import { Observable } from \"@babylonjs/core/Misc/observable.js\";\nimport { Deferred } from \"@babylonjs/core/Misc/deferred.js\";\nimport { GLTFLoader, ArrayItem } from \"../glTFLoader.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"MSFT_lod\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Vendor/MSFT_lod/README.md)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class MSFT_lod {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        /**\n         * Defines a number that determines the order the extensions are applied.\n         */\n        this.order = 100;\n        /**\n         * Maximum number of LODs to load, starting from the lowest LOD.\n         */\n        this.maxLODsToLoad = 10;\n        /**\n         * Observable raised when all node LODs of one level are loaded.\n         * The event data is the index of the loaded LOD starting from zero.\n         * Dispose the loader to cancel the loading of the next level of LODs.\n         */\n        this.onNodeLODsLoadedObservable = new Observable();\n        /**\n         * Observable raised when all material LODs of one level are loaded.\n         * The event data is the index of the loaded LOD starting from zero.\n         * Dispose the loader to cancel the loading of the next level of LODs.\n         */\n        this.onMaterialLODsLoadedObservable = new Observable();\n        this._bufferLODs = new Array();\n        this._nodeIndexLOD = null;\n        this._nodeSignalLODs = new Array();\n        this._nodePromiseLODs = new Array();\n        this._nodeBufferLODs = new Array();\n        this._materialIndexLOD = null;\n        this._materialSignalLODs = new Array();\n        this._materialPromiseLODs = new Array();\n        this._materialBufferLODs = new Array();\n        this._loader = loader;\n        // Options takes precedence. The maxLODsToLoad extension property is retained for back compat.\n        // For new extensions, they should only use options.\n        this.maxLODsToLoad = this._loader.parent.extensionOptions[NAME]?.maxLODsToLoad ?? this.maxLODsToLoad;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n        this._nodeIndexLOD = null;\n        this._nodeSignalLODs.length = 0;\n        this._nodePromiseLODs.length = 0;\n        this._nodeBufferLODs.length = 0;\n        this._materialIndexLOD = null;\n        this._materialSignalLODs.length = 0;\n        this._materialPromiseLODs.length = 0;\n        this._materialBufferLODs.length = 0;\n        this.onMaterialLODsLoadedObservable.clear();\n        this.onNodeLODsLoadedObservable.clear();\n    }\n    /** @internal */\n    onReady() {\n        for (let indexLOD = 0; indexLOD < this._nodePromiseLODs.length; indexLOD++) {\n            const promise = Promise.all(this._nodePromiseLODs[indexLOD]).then(() => {\n                if (indexLOD !== 0) {\n                    this._loader.endPerformanceCounter(`Node LOD ${indexLOD}`);\n                    this._loader.log(`Loaded node LOD ${indexLOD}`);\n                }\n                this.onNodeLODsLoadedObservable.notifyObservers(indexLOD);\n                if (indexLOD !== this._nodePromiseLODs.length - 1) {\n                    this._loader.startPerformanceCounter(`Node LOD ${indexLOD + 1}`);\n                    this._loadBufferLOD(this._nodeBufferLODs, indexLOD + 1);\n                    if (this._nodeSignalLODs[indexLOD]) {\n                        this._nodeSignalLODs[indexLOD].resolve();\n                    }\n                }\n            });\n            this._loader._completePromises.push(promise);\n        }\n        for (let indexLOD = 0; indexLOD < this._materialPromiseLODs.length; indexLOD++) {\n            const promise = Promise.all(this._materialPromiseLODs[indexLOD]).then(() => {\n                if (indexLOD !== 0) {\n                    this._loader.endPerformanceCounter(`Material LOD ${indexLOD}`);\n                    this._loader.log(`Loaded material LOD ${indexLOD}`);\n                }\n                this.onMaterialLODsLoadedObservable.notifyObservers(indexLOD);\n                if (indexLOD !== this._materialPromiseLODs.length - 1) {\n                    this._loader.startPerformanceCounter(`Material LOD ${indexLOD + 1}`);\n                    this._loadBufferLOD(this._materialBufferLODs, indexLOD + 1);\n                    if (this._materialSignalLODs[indexLOD]) {\n                        this._materialSignalLODs[indexLOD].resolve();\n                    }\n                }\n            });\n            this._loader._completePromises.push(promise);\n        }\n    }\n    /**\n     * @internal\n     */\n    loadSceneAsync(context, scene) {\n        const promise = this._loader.loadSceneAsync(context, scene);\n        this._loadBufferLOD(this._bufferLODs, 0);\n        return promise;\n    }\n    /**\n     * @internal\n     */\n    loadNodeAsync(context, node, assign) {\n        return GLTFLoader.LoadExtensionAsync(context, node, this.name, (extensionContext, extension) => {\n            let firstPromise;\n            const nodeLODs = this._getLODs(extensionContext, node, this._loader.gltf.nodes, extension.ids);\n            this._loader.logOpen(`${extensionContext}`);\n            for (let indexLOD = 0; indexLOD < nodeLODs.length; indexLOD++) {\n                const nodeLOD = nodeLODs[indexLOD];\n                if (indexLOD !== 0) {\n                    this._nodeIndexLOD = indexLOD;\n                    this._nodeSignalLODs[indexLOD] = this._nodeSignalLODs[indexLOD] || new Deferred();\n                }\n                const assignWrap = (babylonTransformNode) => {\n                    assign(babylonTransformNode);\n                    babylonTransformNode.setEnabled(false);\n                };\n                const promise = this._loader.loadNodeAsync(`/nodes/${nodeLOD.index}`, nodeLOD, assignWrap).then((babylonMesh) => {\n                    if (indexLOD !== 0) {\n                        // TODO: should not rely on _babylonTransformNode\n                        const previousNodeLOD = nodeLODs[indexLOD - 1];\n                        if (previousNodeLOD._babylonTransformNode) {\n                            this._disposeTransformNode(previousNodeLOD._babylonTransformNode);\n                            delete previousNodeLOD._babylonTransformNode;\n                        }\n                    }\n                    babylonMesh.setEnabled(true);\n                    return babylonMesh;\n                });\n                this._nodePromiseLODs[indexLOD] = this._nodePromiseLODs[indexLOD] || [];\n                if (indexLOD === 0) {\n                    firstPromise = promise;\n                }\n                else {\n                    this._nodeIndexLOD = null;\n                    this._nodePromiseLODs[indexLOD].push(promise);\n                }\n            }\n            this._loader.logClose();\n            return firstPromise;\n        });\n    }\n    /**\n     * @internal\n     */\n    _loadMaterialAsync(context, material, babylonMesh, babylonDrawMode, assign) {\n        // Don't load material LODs if already loading a node LOD.\n        if (this._nodeIndexLOD) {\n            return null;\n        }\n        return GLTFLoader.LoadExtensionAsync(context, material, this.name, (extensionContext, extension) => {\n            let firstPromise;\n            const materialLODs = this._getLODs(extensionContext, material, this._loader.gltf.materials, extension.ids);\n            this._loader.logOpen(`${extensionContext}`);\n            for (let indexLOD = 0; indexLOD < materialLODs.length; indexLOD++) {\n                const materialLOD = materialLODs[indexLOD];\n                if (indexLOD !== 0) {\n                    this._materialIndexLOD = indexLOD;\n                }\n                const promise = this._loader\n                    ._loadMaterialAsync(`/materials/${materialLOD.index}`, materialLOD, babylonMesh, babylonDrawMode, (babylonMaterial) => {\n                    if (indexLOD === 0) {\n                        assign(babylonMaterial);\n                    }\n                })\n                    .then((babylonMaterial) => {\n                    if (indexLOD !== 0) {\n                        assign(babylonMaterial);\n                        // TODO: should not rely on _data\n                        const previousDataLOD = materialLODs[indexLOD - 1]._data;\n                        if (previousDataLOD[babylonDrawMode]) {\n                            this._disposeMaterials([previousDataLOD[babylonDrawMode].babylonMaterial]);\n                            delete previousDataLOD[babylonDrawMode];\n                        }\n                    }\n                    return babylonMaterial;\n                });\n                this._materialPromiseLODs[indexLOD] = this._materialPromiseLODs[indexLOD] || [];\n                if (indexLOD === 0) {\n                    firstPromise = promise;\n                }\n                else {\n                    this._materialIndexLOD = null;\n                    this._materialPromiseLODs[indexLOD].push(promise);\n                }\n            }\n            this._loader.logClose();\n            return firstPromise;\n        });\n    }\n    /**\n     * @internal\n     */\n    _loadUriAsync(context, property, uri) {\n        // Defer the loading of uris if loading a node or material LOD.\n        if (this._nodeIndexLOD !== null) {\n            this._loader.log(`deferred`);\n            const previousIndexLOD = this._nodeIndexLOD - 1;\n            this._nodeSignalLODs[previousIndexLOD] = this._nodeSignalLODs[previousIndexLOD] || new Deferred();\n            return this._nodeSignalLODs[this._nodeIndexLOD - 1].promise.then(() => {\n                return this._loader.loadUriAsync(context, property, uri);\n            });\n        }\n        else if (this._materialIndexLOD !== null) {\n            this._loader.log(`deferred`);\n            const previousIndexLOD = this._materialIndexLOD - 1;\n            this._materialSignalLODs[previousIndexLOD] = this._materialSignalLODs[previousIndexLOD] || new Deferred();\n            return this._materialSignalLODs[previousIndexLOD].promise.then(() => {\n                return this._loader.loadUriAsync(context, property, uri);\n            });\n        }\n        return null;\n    }\n    /**\n     * @internal\n     */\n    loadBufferAsync(context, buffer, byteOffset, byteLength) {\n        if (this._loader.parent.useRangeRequests && !buffer.uri) {\n            if (!this._loader.bin) {\n                throw new Error(`${context}: Uri is missing or the binary glTF is missing its binary chunk`);\n            }\n            const loadAsync = (bufferLODs, indexLOD) => {\n                const start = byteOffset;\n                const end = start + byteLength - 1;\n                let bufferLOD = bufferLODs[indexLOD];\n                if (bufferLOD) {\n                    bufferLOD.start = Math.min(bufferLOD.start, start);\n                    bufferLOD.end = Math.max(bufferLOD.end, end);\n                }\n                else {\n                    bufferLOD = { start: start, end: end, loaded: new Deferred() };\n                    bufferLODs[indexLOD] = bufferLOD;\n                }\n                return bufferLOD.loaded.promise.then((data) => {\n                    return new Uint8Array(data.buffer, data.byteOffset + byteOffset - bufferLOD.start, byteLength);\n                });\n            };\n            this._loader.log(`deferred`);\n            if (this._nodeIndexLOD !== null) {\n                return loadAsync(this._nodeBufferLODs, this._nodeIndexLOD);\n            }\n            else if (this._materialIndexLOD !== null) {\n                return loadAsync(this._materialBufferLODs, this._materialIndexLOD);\n            }\n            else {\n                return loadAsync(this._bufferLODs, 0);\n            }\n        }\n        return null;\n    }\n    _loadBufferLOD(bufferLODs, indexLOD) {\n        const bufferLOD = bufferLODs[indexLOD];\n        if (bufferLOD) {\n            this._loader.log(`Loading buffer range [${bufferLOD.start}-${bufferLOD.end}]`);\n            this._loader.bin.readAsync(bufferLOD.start, bufferLOD.end - bufferLOD.start + 1).then((data) => {\n                bufferLOD.loaded.resolve(data);\n            }, (error) => {\n                bufferLOD.loaded.reject(error);\n            });\n        }\n    }\n    /**\n     * @returns an array of LOD properties from lowest to highest.\n     * @param context\n     * @param property\n     * @param array\n     * @param ids\n     */\n    _getLODs(context, property, array, ids) {\n        if (this.maxLODsToLoad <= 0) {\n            throw new Error(\"maxLODsToLoad must be greater than zero\");\n        }\n        const properties = [];\n        for (let i = ids.length - 1; i >= 0; i--) {\n            properties.push(ArrayItem.Get(`${context}/ids/${ids[i]}`, array, ids[i]));\n            if (properties.length === this.maxLODsToLoad) {\n                return properties;\n            }\n        }\n        properties.push(property);\n        return properties;\n    }\n    _disposeTransformNode(babylonTransformNode) {\n        const babylonMaterials = [];\n        const babylonMaterial = babylonTransformNode.material;\n        if (babylonMaterial) {\n            babylonMaterials.push(babylonMaterial);\n        }\n        for (const babylonMesh of babylonTransformNode.getChildMeshes()) {\n            if (babylonMesh.material) {\n                babylonMaterials.push(babylonMesh.material);\n            }\n        }\n        babylonTransformNode.dispose();\n        const babylonMaterialsToDispose = babylonMaterials.filter((babylonMaterial) => this._loader.babylonScene.meshes.every((mesh) => mesh.material != babylonMaterial));\n        this._disposeMaterials(babylonMaterialsToDispose);\n    }\n    _disposeMaterials(babylonMaterials) {\n        const babylonTextures = {};\n        for (const babylonMaterial of babylonMaterials) {\n            for (const babylonTexture of babylonMaterial.getActiveTextures()) {\n                babylonTextures[babylonTexture.uniqueId] = babylonTexture;\n            }\n            babylonMaterial.dispose();\n        }\n        for (const uniqueId in babylonTextures) {\n            for (const babylonMaterial of this._loader.babylonScene.materials) {\n                if (babylonMaterial.hasTexture(babylonTextures[uniqueId])) {\n                    delete babylonTextures[uniqueId];\n                }\n            }\n        }\n        for (const uniqueId in babylonTextures) {\n            babylonTextures[uniqueId].dispose();\n        }\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new MSFT_lod(loader));\n//# sourceMappingURL=MSFT_lod.js.map"], "names": ["NAME", "MSFT_lod", "loader", "Observable", "_a", "indexLOD", "promise", "context", "scene", "node", "assign", "GLTFLoader", "extensionContext", "extension", "firstPromise", "nodeLODs", "nodeLOD", "Deferred", "assignWrap", "babylonTransformNode", "<PERSON><PERSON><PERSON><PERSON>", "previousNodeLOD", "material", "babylonDrawMode", "materialLODs", "materialLOD", "babylonMaterial", "previousDataLOD", "property", "uri", "previousIndexLOD", "buffer", "byteOffset", "byteLength", "loadAsync", "bufferLODs", "start", "end", "bufferLOD", "data", "error", "array", "ids", "properties", "i", "ArrayItem", "babylonMaterials", "babylonMaterialsToDispose", "mesh", "babylonTextures", "babylonTexture", "uniqueId", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "qIAIA,MAAMA,EAAO,WAKN,MAAMC,CAAS,CAIlB,YAAYC,EAAQ,OAIhB,KAAK,KAAOF,EAIZ,KAAK,MAAQ,IAIb,KAAK,cAAgB,GAMrB,KAAK,2BAA6B,IAAIG,EAMtC,KAAK,+BAAiC,IAAIA,EAC1C,KAAK,YAAc,IAAI,MACvB,KAAK,cAAgB,KACrB,KAAK,gBAAkB,IAAI,MAC3B,KAAK,iBAAmB,IAAI,MAC5B,KAAK,gBAAkB,IAAI,MAC3B,KAAK,kBAAoB,KACzB,KAAK,oBAAsB,IAAI,MAC/B,KAAK,qBAAuB,IAAI,MAChC,KAAK,oBAAsB,IAAI,MAC/B,KAAK,QAAUD,EAGf,KAAK,gBAAgBE,EAAA,KAAK,QAAQ,OAAO,iBAAiBJ,CAAI,IAAzC,YAAAI,EAA4C,gBAAiB,KAAK,cACvF,KAAK,QAAU,KAAK,QAAQ,gBAAgBJ,CAAI,CACnD,CAED,SAAU,CACN,KAAK,QAAU,KACf,KAAK,cAAgB,KACrB,KAAK,gBAAgB,OAAS,EAC9B,KAAK,iBAAiB,OAAS,EAC/B,KAAK,gBAAgB,OAAS,EAC9B,KAAK,kBAAoB,KACzB,KAAK,oBAAoB,OAAS,EAClC,KAAK,qBAAqB,OAAS,EACnC,KAAK,oBAAoB,OAAS,EAClC,KAAK,+BAA+B,QACpC,KAAK,2BAA2B,OACnC,CAED,SAAU,CACN,QAASK,EAAW,EAAGA,EAAW,KAAK,iBAAiB,OAAQA,IAAY,CACxE,MAAMC,EAAU,QAAQ,IAAI,KAAK,iBAAiBD,CAAQ,CAAC,EAAE,KAAK,IAAM,CAChEA,IAAa,IACb,KAAK,QAAQ,sBAAsB,YAAYA,CAAQ,EAAE,EACzD,KAAK,QAAQ,IAAI,mBAAmBA,CAAQ,EAAE,GAElD,KAAK,2BAA2B,gBAAgBA,CAAQ,EACpDA,IAAa,KAAK,iBAAiB,OAAS,IAC5C,KAAK,QAAQ,wBAAwB,YAAYA,EAAW,CAAC,EAAE,EAC/D,KAAK,eAAe,KAAK,gBAAiBA,EAAW,CAAC,EAClD,KAAK,gBAAgBA,CAAQ,GAC7B,KAAK,gBAAgBA,CAAQ,EAAE,QAAO,EAG9D,CAAa,EACD,KAAK,QAAQ,kBAAkB,KAAKC,CAAO,CAC9C,CACD,QAASD,EAAW,EAAGA,EAAW,KAAK,qBAAqB,OAAQA,IAAY,CAC5E,MAAMC,EAAU,QAAQ,IAAI,KAAK,qBAAqBD,CAAQ,CAAC,EAAE,KAAK,IAAM,CACpEA,IAAa,IACb,KAAK,QAAQ,sBAAsB,gBAAgBA,CAAQ,EAAE,EAC7D,KAAK,QAAQ,IAAI,uBAAuBA,CAAQ,EAAE,GAEtD,KAAK,+BAA+B,gBAAgBA,CAAQ,EACxDA,IAAa,KAAK,qBAAqB,OAAS,IAChD,KAAK,QAAQ,wBAAwB,gBAAgBA,EAAW,CAAC,EAAE,EACnE,KAAK,eAAe,KAAK,oBAAqBA,EAAW,CAAC,EACtD,KAAK,oBAAoBA,CAAQ,GACjC,KAAK,oBAAoBA,CAAQ,EAAE,QAAO,EAGlE,CAAa,EACD,KAAK,QAAQ,kBAAkB,KAAKC,CAAO,CAC9C,CACJ,CAID,eAAeC,EAASC,EAAO,CAC3B,MAAMF,EAAU,KAAK,QAAQ,eAAeC,EAASC,CAAK,EAC1D,YAAK,eAAe,KAAK,YAAa,CAAC,EAChCF,CACV,CAID,cAAcC,EAASE,EAAMC,EAAQ,CACjC,OAAOC,EAAW,mBAAmBJ,EAASE,EAAM,KAAK,KAAM,CAACG,EAAkBC,IAAc,CAC5F,IAAIC,EACJ,MAAMC,EAAW,KAAK,SAASH,EAAkBH,EAAM,KAAK,QAAQ,KAAK,MAAOI,EAAU,GAAG,EAC7F,KAAK,QAAQ,QAAQ,GAAGD,CAAgB,EAAE,EAC1C,QAASP,EAAW,EAAGA,EAAWU,EAAS,OAAQV,IAAY,CAC3D,MAAMW,EAAUD,EAASV,CAAQ,EAC7BA,IAAa,IACb,KAAK,cAAgBA,EACrB,KAAK,gBAAgBA,CAAQ,EAAI,KAAK,gBAAgBA,CAAQ,GAAK,IAAIY,GAE3E,MAAMC,EAAcC,GAAyB,CACzCT,EAAOS,CAAoB,EAC3BA,EAAqB,WAAW,EAAK,CACzD,EACsBb,EAAU,KAAK,QAAQ,cAAc,UAAUU,EAAQ,KAAK,GAAIA,EAASE,CAAU,EAAE,KAAME,GAAgB,CAC7G,GAAIf,IAAa,EAAG,CAEhB,MAAMgB,EAAkBN,EAASV,EAAW,CAAC,EACzCgB,EAAgB,wBAChB,KAAK,sBAAsBA,EAAgB,qBAAqB,EAChE,OAAOA,EAAgB,sBAE9B,CACD,OAAAD,EAAY,WAAW,EAAI,EACpBA,CAC3B,CAAiB,EACD,KAAK,iBAAiBf,CAAQ,EAAI,KAAK,iBAAiBA,CAAQ,GAAK,GACjEA,IAAa,EACbS,EAAeR,GAGf,KAAK,cAAgB,KACrB,KAAK,iBAAiBD,CAAQ,EAAE,KAAKC,CAAO,EAEnD,CACD,YAAK,QAAQ,WACNQ,CACnB,CAAS,CACJ,CAID,mBAAmBP,EAASe,EAAUF,EAAaG,EAAiBb,EAAQ,CAExE,OAAI,KAAK,cACE,KAEJC,EAAW,mBAAmBJ,EAASe,EAAU,KAAK,KAAM,CAACV,EAAkBC,IAAc,CAChG,IAAIC,EACJ,MAAMU,EAAe,KAAK,SAASZ,EAAkBU,EAAU,KAAK,QAAQ,KAAK,UAAWT,EAAU,GAAG,EACzG,KAAK,QAAQ,QAAQ,GAAGD,CAAgB,EAAE,EAC1C,QAASP,EAAW,EAAGA,EAAWmB,EAAa,OAAQnB,IAAY,CAC/D,MAAMoB,EAAcD,EAAanB,CAAQ,EACrCA,IAAa,IACb,KAAK,kBAAoBA,GAE7B,MAAMC,EAAU,KAAK,QAChB,mBAAmB,cAAcmB,EAAY,KAAK,GAAIA,EAAaL,EAAaG,EAAkBG,GAAoB,CACnHrB,IAAa,GACbK,EAAOgB,CAAe,CAE9C,CAAiB,EACI,KAAMA,GAAoB,CAC3B,GAAIrB,IAAa,EAAG,CAChBK,EAAOgB,CAAe,EAEtB,MAAMC,EAAkBH,EAAanB,EAAW,CAAC,EAAE,MAC/CsB,EAAgBJ,CAAe,IAC/B,KAAK,kBAAkB,CAACI,EAAgBJ,CAAe,EAAE,eAAe,CAAC,EACzE,OAAOI,EAAgBJ,CAAe,EAE7C,CACD,OAAOG,CAC3B,CAAiB,EACD,KAAK,qBAAqBrB,CAAQ,EAAI,KAAK,qBAAqBA,CAAQ,GAAK,GACzEA,IAAa,EACbS,EAAeR,GAGf,KAAK,kBAAoB,KACzB,KAAK,qBAAqBD,CAAQ,EAAE,KAAKC,CAAO,EAEvD,CACD,YAAK,QAAQ,WACNQ,CACnB,CAAS,CACJ,CAID,cAAcP,EAASqB,EAAUC,EAAK,CAElC,GAAI,KAAK,gBAAkB,KAAM,CAC7B,KAAK,QAAQ,IAAI,UAAU,EAC3B,MAAMC,EAAmB,KAAK,cAAgB,EAC9C,YAAK,gBAAgBA,CAAgB,EAAI,KAAK,gBAAgBA,CAAgB,GAAK,IAAIb,EAChF,KAAK,gBAAgB,KAAK,cAAgB,CAAC,EAAE,QAAQ,KAAK,IACtD,KAAK,QAAQ,aAAaV,EAASqB,EAAUC,CAAG,CAC1D,CACJ,SACQ,KAAK,oBAAsB,KAAM,CACtC,KAAK,QAAQ,IAAI,UAAU,EAC3B,MAAMC,EAAmB,KAAK,kBAAoB,EAClD,YAAK,oBAAoBA,CAAgB,EAAI,KAAK,oBAAoBA,CAAgB,GAAK,IAAIb,EACxF,KAAK,oBAAoBa,CAAgB,EAAE,QAAQ,KAAK,IACpD,KAAK,QAAQ,aAAavB,EAASqB,EAAUC,CAAG,CAC1D,CACJ,CACD,OAAO,IACV,CAID,gBAAgBtB,EAASwB,EAAQC,EAAYC,EAAY,CACrD,GAAI,KAAK,QAAQ,OAAO,kBAAoB,CAACF,EAAO,IAAK,CACrD,GAAI,CAAC,KAAK,QAAQ,IACd,MAAM,IAAI,MAAM,GAAGxB,CAAO,iEAAiE,EAE/F,MAAM2B,EAAY,CAACC,EAAY9B,IAAa,CACxC,MAAM+B,EAAQJ,EACRK,EAAMD,EAAQH,EAAa,EACjC,IAAIK,EAAYH,EAAW9B,CAAQ,EACnC,OAAIiC,GACAA,EAAU,MAAQ,KAAK,IAAIA,EAAU,MAAOF,CAAK,EACjDE,EAAU,IAAM,KAAK,IAAIA,EAAU,IAAKD,CAAG,IAG3CC,EAAY,CAAE,MAAOF,EAAO,IAAKC,EAAK,OAAQ,IAAIpB,GAClDkB,EAAW9B,CAAQ,EAAIiC,GAEpBA,EAAU,OAAO,QAAQ,KAAMC,GAC3B,IAAI,WAAWA,EAAK,OAAQA,EAAK,WAAaP,EAAaM,EAAU,MAAOL,CAAU,CAChG,CACjB,EAEY,OADA,KAAK,QAAQ,IAAI,UAAU,EACvB,KAAK,gBAAkB,KAChBC,EAAU,KAAK,gBAAiB,KAAK,aAAa,EAEpD,KAAK,oBAAsB,KACzBA,EAAU,KAAK,oBAAqB,KAAK,iBAAiB,EAG1DA,EAAU,KAAK,YAAa,CAAC,CAE3C,CACD,OAAO,IACV,CACD,eAAeC,EAAY9B,EAAU,CACjC,MAAMiC,EAAYH,EAAW9B,CAAQ,EACjCiC,IACA,KAAK,QAAQ,IAAI,yBAAyBA,EAAU,KAAK,IAAIA,EAAU,GAAG,GAAG,EAC7E,KAAK,QAAQ,IAAI,UAAUA,EAAU,MAAOA,EAAU,IAAMA,EAAU,MAAQ,CAAC,EAAE,KAAMC,GAAS,CAC5FD,EAAU,OAAO,QAAQC,CAAI,CAChC,EAAGC,GAAU,CACVF,EAAU,OAAO,OAAOE,CAAK,CAC7C,CAAa,EAER,CAQD,SAASjC,EAASqB,EAAUa,EAAOC,EAAK,CACpC,GAAI,KAAK,eAAiB,EACtB,MAAM,IAAI,MAAM,yCAAyC,EAE7D,MAAMC,EAAa,CAAA,EACnB,QAASC,EAAIF,EAAI,OAAS,EAAGE,GAAK,EAAGA,IAEjC,GADAD,EAAW,KAAKE,EAAU,IAAI,GAAGtC,CAAO,QAAQmC,EAAIE,CAAC,CAAC,GAAIH,EAAOC,EAAIE,CAAC,CAAC,CAAC,EACpED,EAAW,SAAW,KAAK,cAC3B,OAAOA,EAGf,OAAAA,EAAW,KAAKf,CAAQ,EACjBe,CACV,CACD,sBAAsBxB,EAAsB,CACxC,MAAM2B,EAAmB,CAAA,EACnBpB,EAAkBP,EAAqB,SACzCO,GACAoB,EAAiB,KAAKpB,CAAe,EAEzC,UAAWN,KAAeD,EAAqB,iBACvCC,EAAY,UACZ0B,EAAiB,KAAK1B,EAAY,QAAQ,EAGlDD,EAAqB,QAAO,EAC5B,MAAM4B,EAA4BD,EAAiB,OAAQpB,GAAoB,KAAK,QAAQ,aAAa,OAAO,MAAOsB,GAASA,EAAK,UAAYtB,CAAe,CAAC,EACjK,KAAK,kBAAkBqB,CAAyB,CACnD,CACD,kBAAkBD,EAAkB,CAChC,MAAMG,EAAkB,CAAA,EACxB,UAAWvB,KAAmBoB,EAAkB,CAC5C,UAAWI,KAAkBxB,EAAgB,oBACzCuB,EAAgBC,EAAe,QAAQ,EAAIA,EAE/CxB,EAAgB,QAAO,CAC1B,CACD,UAAWyB,KAAYF,EACnB,UAAWvB,KAAmB,KAAK,QAAQ,aAAa,UAChDA,EAAgB,WAAWuB,EAAgBE,CAAQ,CAAC,GACpD,OAAOF,EAAgBE,CAAQ,EAI3C,UAAWA,KAAYF,EACnBA,EAAgBE,CAAQ,EAAE,SAEjC,CACL,CACAC,EAAwBpD,CAAI,EAC5BqD,EAAsBrD,EAAM,GAAOE,GAAW,IAAID,EAASC,CAAM,CAAC", "x_google_ignoreList": [0]}