import{$ as I}from"./index-B7J2Z2jS.js";const J="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='10'%20height='10'%20fill='none'%3e%3cpath%20fill='%23FF3270'%20d='M1.93%206.03v2.04h2.04V6.03H1.93Z'/%3e%3cpath%20fill='%23861FFF'%20d='M6.03%206.03v2.04h2.04V6.03H6.03Z'/%3e%3cpath%20fill='%23097EFF'%20d='M1.93%201.93v2.04h2.04V1.93H1.93Z'/%3e%3cpath%20fill='%23000'%20fill-rule='evenodd'%20d='M.5%201.4c0-.5.4-.9.9-.9h3.1a.9.9%200%200%201%20.87.67A2.44%202.44%200%200%201%209.5%202.95c0%20.65-.25%201.24-.67%***********.67.46.67.88v3.08c0%20.5-.4.91-.9.91H1.4a.9.9%200%200%201-.9-.9V1.4Zm1.43.53v2.04h2.04V1.93H1.93Zm0%206.14V6.03h2.04v2.04H1.93Zm4.1%200V6.03h2.04v2.04H6.03Zm0-5.12a1.02%201.02%200%201%201%202.04%200%201.02%201.02%200%200%201-2.04%200Z'%20clip-rule='evenodd'/%3e%3cpath%20fill='%23FFD702'%20d='M7.05%201.93a1.02%201.02%200%201%200%200%202.04%201.02%201.02%200%200%200%200-2.04Z'/%3e%3c/svg%3e",{SvelteComponent:K,append:c,attr:u,binding_callbacks:O,component_subscribe:P,create_slot:Q,destroy_each:R,detach:E,element:p,empty:U,ensure_array_like:G,flush:b,get_all_dirty_from_scope:W,get_slot_changes:X,init:Y,insert:S,listen:y,safe_not_equal:$,set_data:q,set_style:A,space:Z,text:C,toggle_class:m,transition_in:x,transition_out:ee,update_slot_base:te}=window.__gradio__svelte__internal,{getContext:le}=window.__gradio__svelte__internal;function L(t,e,i){const s=t.slice();return s[19]=e[i][0],s[20]=e[i][1],s[22]=i,s}function N(t){let e,i,s=G(t[10]),a=[];for(let l=0;l<s.length;l+=1)a[l]=T(L(t,s,l));return{c(){e=p("div"),i=p("nav");for(let l=0;l<a.length;l+=1)a[l].c();u(i,"class","fillable svelte-1anp163"),m(i,"fill_width",t[3]),u(e,"class","nav-holder svelte-1anp163")},m(l,f){S(l,e,f),c(e,i);for(let n=0;n<a.length;n+=1)a[n]&&a[n].m(i,null)},p(l,f){if(f&23584){s=G(l[10]);let n;for(n=0;n<s.length;n+=1){const o=L(l,s,n);a[n]?a[n].p(o,f):(a[n]=T(o),a[n].c(),a[n].m(i,null))}for(;n<a.length;n+=1)a[n].d(1);a.length=s.length}f&8&&m(i,"fill_width",l[3])},d(l){l&&E(e),R(a,l)}}}function ie(t){let e,i=t[20]+"",s,a,l;return{c(){e=p("a"),s=C(i),a=Z(),u(e,"href",l=`${t[12]}/${t[19]}`),u(e,"data-sveltekit-reload",""),u(e,"class","svelte-1anp163"),m(e,"active",t[19]===t[11])},m(f,n){S(f,e,n),c(e,s),c(e,a)},p(f,n){n&1024&&i!==(i=f[20]+"")&&q(s,i),n&5120&&l!==(l=`${f[12]}/${f[19]}`)&&u(e,"href",l),n&3072&&m(e,"active",f[19]===f[11])},d(f){f&&E(e)}}}function ne(t){let e,i=t[20]+"",s,a,l,f;function n(...o){return t[17](t[19],...o)}return{c(){e=p("button"),s=C(i),a=Z(),u(e,"class","svelte-1anp163"),m(e,"active",t[19]===t[11])},m(o,v){S(o,e,v),c(e,s),c(e,a),l||(f=y(e,"click",n),l=!0)},p(o,v){t=o,v&1024&&i!==(i=t[20]+"")&&q(s,i),v&3072&&m(e,"active",t[19]===t[11])},d(o){o&&E(e),l=!1,f()}}}function T(t){let e;function i(l,f){return l[5]?ne:ie}let s=i(t),a=s(t);return{c(){a.c(),e=U()},m(l,f){a.m(l,f),S(l,e,f)},p(l,f){s===(s=i(l))&&a?a.p(l,f):(a.d(1),a=s(l),a&&(a.c(),a.m(e.parentNode,e)))},d(l){l&&E(e),a.d(l)}}}function j(t){let e,i,s,a,l,f,n,o=t[13]("common.built_with")+"",v,d,r,_,g,k,V=t[13]("common.hosted_on")+"",M,D,F;return{c(){e=p("div"),i=p("span"),s=p("a"),a=C(t[6]),f=Z(),n=p("span"),v=C(o),d=Z(),r=p("a"),r.textContent="Gradio",_=C("."),g=Z(),k=p("span"),M=C(V),D=Z(),F=p("a"),F.innerHTML=`<span class="space-logo svelte-1anp163"><img src="${J}" alt="Hugging Face Space" class="svelte-1anp163"/></span> Spaces`,u(s,"href",l="https://huggingface.co/spaces/"+t[6]),u(s,"class","title svelte-1anp163"),u(i,"class","svelte-1anp163"),u(r,"class","gradio svelte-1anp163"),u(r,"href","https://gradio.app"),u(n,"class","svelte-1anp163"),u(F,"class","hf svelte-1anp163"),u(F,"href","https://huggingface.co/spaces"),u(k,"class","svelte-1anp163"),u(e,"class","info svelte-1anp163")},m(w,H){S(w,e,H),c(e,i),c(i,s),c(s,a),c(e,f),c(e,n),c(n,v),c(n,d),c(n,r),c(n,_),c(e,g),c(e,k),c(k,M),c(k,D),c(k,F)},p(w,H){H&64&&q(a,w[6]),H&64&&l!==(l="https://huggingface.co/spaces/"+w[6])&&u(s,"href",l),H&8192&&o!==(o=w[13]("common.built_with")+"")&&q(v,o),H&8192&&V!==(V=w[13]("common.hosted_on")+"")&&q(M,V)},d(w){w&&E(e)}}}function se(t){let e,i,s,a,l,f,n,o=t[10].length>1&&N(t);const v=t[16].default,d=Q(v,t,t[15],null);let r=t[7]&&t[6]&&t[8]&&j(t);return{c(){e=p("div"),o&&o.c(),i=Z(),s=p("main"),d&&d.c(),a=Z(),l=p("div"),r&&r.c(),u(s,"class","fillable svelte-1anp163"),m(s,"fill_width",t[3]),m(s,"app",!t[7]&&!t[4]),u(e,"class",f="gradio-container gradio-container-"+t[1]+" svelte-1anp163"),u(e,"data-iframe-height",""),m(e,"fill_width",t[3]),m(e,"embed-container",t[7]),m(e,"with-info",t[8]),A(e,"min-height",t[9]?"initial":t[2]),A(e,"flex-grow",t[7]?"auto":"1")},m(_,g){S(_,e,g),o&&o.m(e,null),c(e,i),c(e,s),d&&d.m(s,null),c(s,a),c(s,l),r&&r.m(l,null),t[18](e),n=!0},p(_,[g]){_[10].length>1?o?o.p(_,g):(o=N(_),o.c(),o.m(e,i)):o&&(o.d(1),o=null),d&&d.p&&(!n||g&32768)&&te(d,v,_,_[15],n?X(v,_[15],g,null):W(_[15]),null),_[7]&&_[6]&&_[8]?r?r.p(_,g):(r=j(_),r.c(),r.m(l,null)):r&&(r.d(1),r=null),(!n||g&8)&&m(s,"fill_width",_[3]),(!n||g&144)&&m(s,"app",!_[7]&&!_[4]),(!n||g&2&&f!==(f="gradio-container gradio-container-"+_[1]+" svelte-1anp163"))&&u(e,"class",f),(!n||g&10)&&m(e,"fill_width",_[3]),(!n||g&130)&&m(e,"embed-container",_[7]),(!n||g&258)&&m(e,"with-info",_[8]),g&516&&A(e,"min-height",_[9]?"initial":_[2]),g&128&&A(e,"flex-grow",_[7]?"auto":"1")},i(_){n||(x(d,_),n=!0)},o(_){ee(d,_),n=!1},d(_){_&&E(e),o&&o.d(),d&&d.d(_),r&&r.d(),t[18](null)}}}function ae(t,e,i){let s;P(t,I,h=>i(13,s=h));let{$$slots:a={},$$scope:l}=e,{wrapper:f}=e,{version:n}=e,{initial_height:o}=e,{fill_width:v}=e,{is_embed:d}=e,{is_lite:r}=e,{space:_}=e,{display:g}=e,{info:k}=e,{loaded:V}=e,{pages:M=[]}=e,{current_page:D=""}=e,{root:F}=e;const w=le("set_lite_page"),H=(h,B)=>{B.preventDefault(),w?.(h)};function z(h){O[h?"unshift":"push"](()=>{f=h,i(0,f)})}return t.$$set=h=>{"wrapper"in h&&i(0,f=h.wrapper),"version"in h&&i(1,n=h.version),"initial_height"in h&&i(2,o=h.initial_height),"fill_width"in h&&i(3,v=h.fill_width),"is_embed"in h&&i(4,d=h.is_embed),"is_lite"in h&&i(5,r=h.is_lite),"space"in h&&i(6,_=h.space),"display"in h&&i(7,g=h.display),"info"in h&&i(8,k=h.info),"loaded"in h&&i(9,V=h.loaded),"pages"in h&&i(10,M=h.pages),"current_page"in h&&i(11,D=h.current_page),"root"in h&&i(12,F=h.root),"$$scope"in h&&i(15,l=h.$$scope)},[f,n,o,v,d,r,_,g,k,V,M,D,F,s,w,l,a,H,z]}class _e extends K{constructor(e){super(),Y(this,e,ae,se,$,{wrapper:0,version:1,initial_height:2,fill_width:3,is_embed:4,is_lite:5,space:6,display:7,info:8,loaded:9,pages:10,current_page:11,root:12})}get wrapper(){return this.$$.ctx[0]}set wrapper(e){this.$$set({wrapper:e}),b()}get version(){return this.$$.ctx[1]}set version(e){this.$$set({version:e}),b()}get initial_height(){return this.$$.ctx[2]}set initial_height(e){this.$$set({initial_height:e}),b()}get fill_width(){return this.$$.ctx[3]}set fill_width(e){this.$$set({fill_width:e}),b()}get is_embed(){return this.$$.ctx[4]}set is_embed(e){this.$$set({is_embed:e}),b()}get is_lite(){return this.$$.ctx[5]}set is_lite(e){this.$$set({is_lite:e}),b()}get space(){return this.$$.ctx[6]}set space(e){this.$$set({space:e}),b()}get display(){return this.$$.ctx[7]}set display(e){this.$$set({display:e}),b()}get info(){return this.$$.ctx[8]}set info(e){this.$$set({info:e}),b()}get loaded(){return this.$$.ctx[9]}set loaded(e){this.$$set({loaded:e}),b()}get pages(){return this.$$.ctx[10]}set pages(e){this.$$set({pages:e}),b()}get current_page(){return this.$$.ctx[11]}set current_page(e){this.$$set({current_page:e}),b()}get root(){return this.$$.ctx[12]}set root(e){this.$$set({root:e}),b()}}export{_e as E};
//# sourceMappingURL=Embed-HnJWX_lT.js.map
