{"version": 3, "file": "KHR_node_visibility.DrdPfsSp.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_node_visibility.js"], "sourcesContent": ["import { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nimport { AddObjectAccessorToKey } from \"./objectModelMapping.js\";\nconst NAME = \"KHR_node_visibility\";\n// object model extension for visibility\nAddObjectAccessorToKey(\"/nodes/{}/extensions/KHR_node_visibility/visible\", {\n    get: (node) => {\n        const tn = node._babylonTransformNode;\n        if (tn && tn.isVisible !== undefined) {\n            return tn.isVisible;\n        }\n        return true;\n    },\n    set: (value, node) => {\n        node._primitiveBabylonMeshes?.forEach((mesh) => {\n            mesh.inheritVisibility = true;\n        });\n        if (node._babylonTransformNode) {\n            node._babylonTransformNode.isVisible = value;\n        }\n        node._primitiveBabylonMeshes?.forEach((mesh) => {\n            mesh.isVisible = value;\n        });\n    },\n    getTarget: (node) => node._babylonTransformNode,\n    getPropertyName: [() => \"isVisible\"],\n    type: \"boolean\",\n});\n/**\n * Loader extension for KHR_node_visibility\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_node_visibility {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        this._loader = loader;\n        this.enabled = loader.isExtensionUsed(NAME);\n    }\n    async onReady() {\n        this._loader.gltf.nodes?.forEach((node) => {\n            node._primitiveBabylonMeshes?.forEach((mesh) => {\n                mesh.inheritVisibility = true;\n            });\n            // When the JSON Pointer is used we need to change both the transform node and the primitive meshes to the new value.\n            if (node.extensions?.KHR_node_visibility) {\n                if (node.extensions?.KHR_node_visibility.visible === false) {\n                    if (node._babylonTransformNode) {\n                        node._babylonTransformNode.isVisible = false;\n                    }\n                    node._primitiveBabylonMeshes?.forEach((mesh) => {\n                        mesh.isVisible = false;\n                    });\n                }\n            }\n        });\n    }\n    dispose() {\n        this._loader = null;\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_node_visibility(loader));\n//# sourceMappingURL=KHR_node_visibility.js.map"], "names": ["NAME", "AddObjectAccessorToKey", "node", "tn", "value", "_a", "mesh", "_b", "KHR_node_visibility", "loader", "_c", "_d", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "sGAEA,MAAMA,EAAO,sBAEbC,EAAuB,mDAAoD,CACvE,IAAMC,GAAS,CACX,MAAMC,EAAKD,EAAK,sBAChB,OAAIC,GAAMA,EAAG,YAAc,OAChBA,EAAG,UAEP,EACV,EACD,IAAK,CAACC,EAAOF,IAAS,UAClBG,EAAAH,EAAK,0BAAL,MAAAG,EAA8B,QAASC,GAAS,CAC5CA,EAAK,kBAAoB,EACrC,GACYJ,EAAK,wBACLA,EAAK,sBAAsB,UAAYE,IAE3CG,EAAAL,EAAK,0BAAL,MAAAK,EAA8B,QAASD,GAAS,CAC5CA,EAAK,UAAYF,CAC7B,EACK,EACD,UAAYF,GAASA,EAAK,sBAC1B,gBAAiB,CAAC,IAAM,WAAW,EACnC,KAAM,SACV,CAAC,EAKM,MAAMM,CAAoB,CAI7B,YAAYC,EAAQ,CAIhB,KAAK,KAAOT,EACZ,KAAK,QAAUS,EACf,KAAK,QAAUA,EAAO,gBAAgBT,CAAI,CAC7C,CACD,MAAM,SAAU,QACZK,EAAA,KAAK,QAAQ,KAAK,QAAlB,MAAAA,EAAyB,QAASH,GAAS,cACvCG,EAAAH,EAAK,0BAAL,MAAAG,EAA8B,QAASC,GAAS,CAC5CA,EAAK,kBAAoB,EACzC,IAEgBC,EAAAL,EAAK,aAAL,MAAAK,EAAiB,uBACbG,EAAAR,EAAK,aAAL,YAAAQ,EAAiB,oBAAoB,WAAY,KAC7CR,EAAK,wBACLA,EAAK,sBAAsB,UAAY,KAE3CS,EAAAT,EAAK,0BAAL,MAAAS,EAA8B,QAASL,GAAS,CAC5CA,EAAK,UAAY,EACzC,GAGA,EACK,CACD,SAAU,CACN,KAAK,QAAU,IAClB,CACL,CACAM,EAAwBZ,CAAI,EAC5Ba,EAAsBb,EAAM,GAAOS,GAAW,IAAID,EAAoBC,CAAM,CAAC", "x_google_ignoreList": [0]}