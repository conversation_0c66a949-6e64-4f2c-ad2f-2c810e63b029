import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "qqmlwebchannel.h"
        name: "QQmlWebChannel"
        accessSemantics: "reference"
        prototype: "QWebChannel"
        exports: [
            "QtWebChannel/WebChannel 1.0",
            "QtWebChannel/WebChannel 6.0"
        ]
        exportMetaObjectRevisions: [256, 1536]
        attachedType: "QQmlWebChannelAttached"
        Property {
            name: "transports"
            type: "QObject"
            isList: true
            read: "transports"
            index: 0
            isReadonly: true
        }
        Property {
            name: "registeredObjects"
            type: "QObject"
            isList: true
            read: "registeredObjects"
            index: 1
            isReadonly: true
        }
        Method {
            name: "_q_objectIdChanged"
            Parameter { name: "newId"; type: "QString" }
        }
        Method {
            name: "registerObjects"
            Parameter { name: "objects"; type: "QVariantMap" }
        }
        Method {
            name: "connectTo"
            Parameter { name: "transport"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "disconnectFrom"
            Parameter { name: "transport"; type: "QObject"; isPointer: true }
        }
    }
    Component {
        file: "private/qqmlwebchannelattached_p.h"
        name: "QQmlWebChannelAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "id"
            type: "QString"
            read: "id"
            write: "setId"
            notify: "idChanged"
            index: 0
            isFinal: true
        }
        Signal {
            name: "idChanged"
            Parameter { name: "id"; type: "QString" }
        }
    }
    Component {
        file: "qwebchannel.h"
        name: "QWebChannel"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "blockUpdates"
            type: "bool"
            bindable: "bindableBlockUpdates"
            read: "blockUpdates"
            write: "setBlockUpdates"
            notify: "blockUpdatesChanged"
            index: 0
        }
        Property {
            name: "propertyUpdateInterval"
            type: "int"
            bindable: "bindablePropertyUpdateInterval"
            read: "propertyUpdateInterval"
            write: "setPropertyUpdateInterval"
            index: 1
        }
        Signal {
            name: "blockUpdatesChanged"
            Parameter { name: "block"; type: "bool" }
        }
        Method {
            name: "connectTo"
            Parameter { name: "transport"; type: "QWebChannelAbstractTransport"; isPointer: true }
        }
        Method {
            name: "disconnectFrom"
            Parameter { name: "transport"; type: "QWebChannelAbstractTransport"; isPointer: true }
        }
        Method {
            name: "_q_transportDestroyed"
            Parameter { type: "QObject"; isPointer: true }
        }
        Method {
            name: "registerObject"
            Parameter { name: "id"; type: "QString" }
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "deregisterObject"
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
    }
}
