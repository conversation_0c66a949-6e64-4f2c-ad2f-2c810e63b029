import{O as u,at as f,an as g,ao as p}from"./index-Dpxo-yl_.js";import{GLTFLoader as c,ArrayItem as b}from"./glTFLoader-9Z3KGax5.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./bone-kZWM5-u7.js";import"./rawTexture-DmvUfjqF.js";import"./assetContainer-BRzQBugc.js";import"./objectModelMapping-BR4RdEzn.js";const O="MSFT_lod";class x{constructor(e){this.name=O,this.order=100,this.maxLODsToLoad=10,this.onNodeLODsLoadedObservable=new u,this.onMaterialLODsLoadedObservable=new u,this._bufferLODs=new Array,this._nodeIndexLOD=null,this._nodeSignalLODs=new Array,this._nodePromiseLODs=new Array,this._nodeBufferLODs=new Array,this._materialIndexLOD=null,this._materialSignalLODs=new Array,this._materialPromiseLODs=new Array,this._materialBufferLODs=new Array,this._loader=e,this.maxLODsToLoad=this._loader.parent.extensionOptions[O]?.maxLODsToLoad??this.maxLODsToLoad,this.enabled=this._loader.isExtensionUsed(O)}dispose(){this._loader=null,this._nodeIndexLOD=null,this._nodeSignalLODs.length=0,this._nodePromiseLODs.length=0,this._nodeBufferLODs.length=0,this._materialIndexLOD=null,this._materialSignalLODs.length=0,this._materialPromiseLODs.length=0,this._materialBufferLODs.length=0,this.onMaterialLODsLoadedObservable.clear(),this.onNodeLODsLoadedObservable.clear()}onReady(){for(let e=0;e<this._nodePromiseLODs.length;e++){const i=Promise.all(this._nodePromiseLODs[e]).then(()=>{e!==0&&(this._loader.endPerformanceCounter(`Node LOD ${e}`),this._loader.log(`Loaded node LOD ${e}`)),this.onNodeLODsLoadedObservable.notifyObservers(e),e!==this._nodePromiseLODs.length-1&&(this._loader.startPerformanceCounter(`Node LOD ${e+1}`),this._loadBufferLOD(this._nodeBufferLODs,e+1),this._nodeSignalLODs[e]&&this._nodeSignalLODs[e].resolve())});this._loader._completePromises.push(i)}for(let e=0;e<this._materialPromiseLODs.length;e++){const i=Promise.all(this._materialPromiseLODs[e]).then(()=>{e!==0&&(this._loader.endPerformanceCounter(`Material LOD ${e}`),this._loader.log(`Loaded material LOD ${e}`)),this.onMaterialLODsLoadedObservable.notifyObservers(e),e!==this._materialPromiseLODs.length-1&&(this._loader.startPerformanceCounter(`Material LOD ${e+1}`),this._loadBufferLOD(this._materialBufferLODs,e+1),this._materialSignalLODs[e]&&this._materialSignalLODs[e].resolve())});this._loader._completePromises.push(i)}}loadSceneAsync(e,i){const s=this._loader.loadSceneAsync(e,i);return this._loadBufferLOD(this._bufferLODs,0),s}loadNodeAsync(e,i,s){return c.LoadExtensionAsync(e,i,this.name,(t,n)=>{let a;const d=this._getLODs(t,i,this._loader.gltf.nodes,n.ids);this._loader.logOpen(`${t}`);for(let o=0;o<d.length;o++){const l=d[o];o!==0&&(this._nodeIndexLOD=o,this._nodeSignalLODs[o]=this._nodeSignalLODs[o]||new f);const r=L=>{s(L),L.setEnabled(!1)},h=this._loader.loadNodeAsync(`/nodes/${l.index}`,l,r).then(L=>{if(o!==0){const _=d[o-1];_._babylonTransformNode&&(this._disposeTransformNode(_._babylonTransformNode),delete _._babylonTransformNode)}return L.setEnabled(!0),L});this._nodePromiseLODs[o]=this._nodePromiseLODs[o]||[],o===0?a=h:(this._nodeIndexLOD=null,this._nodePromiseLODs[o].push(h))}return this._loader.logClose(),a})}_loadMaterialAsync(e,i,s,t,n){return this._nodeIndexLOD?null:c.LoadExtensionAsync(e,i,this.name,(a,d)=>{let o;const l=this._getLODs(a,i,this._loader.gltf.materials,d.ids);this._loader.logOpen(`${a}`);for(let r=0;r<l.length;r++){const h=l[r];r!==0&&(this._materialIndexLOD=r);const L=this._loader._loadMaterialAsync(`/materials/${h.index}`,h,s,t,_=>{r===0&&n(_)}).then(_=>{if(r!==0){n(_);const m=l[r-1]._data;m[t]&&(this._disposeMaterials([m[t].babylonMaterial]),delete m[t])}return _});this._materialPromiseLODs[r]=this._materialPromiseLODs[r]||[],r===0?o=L:(this._materialIndexLOD=null,this._materialPromiseLODs[r].push(L))}return this._loader.logClose(),o})}_loadUriAsync(e,i,s){if(this._nodeIndexLOD!==null){this._loader.log("deferred");const t=this._nodeIndexLOD-1;return this._nodeSignalLODs[t]=this._nodeSignalLODs[t]||new f,this._nodeSignalLODs[this._nodeIndexLOD-1].promise.then(()=>this._loader.loadUriAsync(e,i,s))}else if(this._materialIndexLOD!==null){this._loader.log("deferred");const t=this._materialIndexLOD-1;return this._materialSignalLODs[t]=this._materialSignalLODs[t]||new f,this._materialSignalLODs[t].promise.then(()=>this._loader.loadUriAsync(e,i,s))}return null}loadBufferAsync(e,i,s,t){if(this._loader.parent.useRangeRequests&&!i.uri){if(!this._loader.bin)throw new Error(`${e}: Uri is missing or the binary glTF is missing its binary chunk`);const n=(a,d)=>{const o=s,l=o+t-1;let r=a[d];return r?(r.start=Math.min(r.start,o),r.end=Math.max(r.end,l)):(r={start:o,end:l,loaded:new f},a[d]=r),r.loaded.promise.then(h=>new Uint8Array(h.buffer,h.byteOffset+s-r.start,t))};return this._loader.log("deferred"),this._nodeIndexLOD!==null?n(this._nodeBufferLODs,this._nodeIndexLOD):this._materialIndexLOD!==null?n(this._materialBufferLODs,this._materialIndexLOD):n(this._bufferLODs,0)}return null}_loadBufferLOD(e,i){const s=e[i];s&&(this._loader.log(`Loading buffer range [${s.start}-${s.end}]`),this._loader.bin.readAsync(s.start,s.end-s.start+1).then(t=>{s.loaded.resolve(t)},t=>{s.loaded.reject(t)}))}_getLODs(e,i,s,t){if(this.maxLODsToLoad<=0)throw new Error("maxLODsToLoad must be greater than zero");const n=[];for(let a=t.length-1;a>=0;a--)if(n.push(b.Get(`${e}/ids/${t[a]}`,s,t[a])),n.length===this.maxLODsToLoad)return n;return n.push(i),n}_disposeTransformNode(e){const i=[],s=e.material;s&&i.push(s);for(const n of e.getChildMeshes())n.material&&i.push(n.material);e.dispose();const t=i.filter(n=>this._loader.babylonScene.meshes.every(a=>a.material!=n));this._disposeMaterials(t)}_disposeMaterials(e){const i={};for(const s of e){for(const t of s.getActiveTextures())i[t.uniqueId]=t;s.dispose()}for(const s in i)for(const t of this._loader.babylonScene.materials)t.hasTexture(i[s])&&delete i[s];for(const s in i)i[s].dispose()}}g(O);p(O,!0,D=>new x(D));export{x as MSFT_lod};
//# sourceMappingURL=MSFT_lod-D56EDBVU.js.map
