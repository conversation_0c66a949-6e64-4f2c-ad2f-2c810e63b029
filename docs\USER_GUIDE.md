# 📖 Reverie Agents 用户指南

## 🚀 快速开始

### 1. 首次启动

**桌面版**:
```bash
python run_desktop.py
```

**Web版**:
```bash
python run_web.py
```

### 2. 选择人设

启动后，首先选择一个AI人设：
- **妻子模式** 🥰: 温柔体贴的AI伴侣
- **推理专家** 🧠: 专业的逻辑分析师
- **旅行向导** 🗺️: 专业的旅行规划师
- **职场导师** 💼: 职业发展指导师

### 3. 开始对话

在输入框中输入消息，按Enter或点击发送按钮即可开始对话。

## 🎭 人设详解

### 妻子模式 (Wife Mode)
- **特点**: 温柔、体贴、有情感深度
- **支持NSFW**: 是
- **适用场景**: 情感陪伴、日常聊天、亲密对话
- **示例对话**:
  ```
  用户: 今天工作好累
  Reverie: 亲爱的，辛苦了～ (◕‿◕) 要不要我给你按按肩膀？或者我们一起看个电影放松一下？
  ```

### 推理专家 (Reasoning Expert)
- **特点**: 逻辑严谨、分析透彻、专业客观
- **支持NSFW**: 否
- **适用场景**: 问题分析、决策支持、学术讨论
- **示例对话**:
  ```
  用户: 我应该换工作吗？
  Reverie: 让我们用SWOT分析法来评估这个决定：
  优势(Strengths): ...
  劣势(Weaknesses): ...
  ```

### 旅行向导 (Travel Guide)
- **特点**: 专业、热情、经验丰富
- **支持NSFW**: 否
- **适用场景**: 旅行规划、景点推荐、文化介绍
- **示例对话**:
  ```
  用户: 想去日本旅游
  Reverie: 太棒了！日本是个美丽的国家 🗾 你比较喜欢传统文化还是现代都市？我可以为你制定详细的行程规划。
  ```

### 职场导师 (Career Mentor)
- **特点**: 专业、务实、有指导性
- **支持NSFW**: 否
- **适用场景**: 职业规划、技能提升、面试准备
- **示例对话**:
  ```
  用户: 如何提升编程技能？
  Reverie: 作为你的职场导师，我建议从以下几个方面入手：
  1. 基础巩固...
  2. 项目实践...
  ```

## 🎨 图像生成功能

### 基本使用
1. 在对话中使用生成指令：
   - "画一张..."
   - "生成图片..."
   - "帮我画..."

2. 描述你想要的图像：
   ```
   用户: 画一张夕阳下的山景
   Reverie: 好的，我来为你生成图像：夕阳下的山景
   
   正在生成图像，请稍候...
   ✅ 图像生成完成！
   图像 1: outputs/images/sunset_mountains_20241201_143022.png
   ```

### 高级参数（Web界面）
- **宽度/高度**: 控制图像尺寸（256-2048px）
- **推理步数**: 影响图像质量（1-100步）
- **引导强度**: 控制提示词遵循程度（1.0-20.0）

### 支持的模型类型
- **FLUX.1**: 最新高质量模型
- **Stable Diffusion XL**: 经典高质量模型
- **Illustrious**: 动漫风格特化模型

## 🔍 智能搜索功能

### 自动触发搜索
系统会在以下情况自动搜索：
- 询问最新信息："今天的新闻"
- 包含时间词汇："2024年的..."
- 使用搜索关键词："搜索..."、"查找..."

### 搜索结果整合
搜索结果会自动整合到AI回复中：
```
用户: 最新的AI技术发展
Reverie: 正在搜索最新AI技术信息...

根据最新搜索结果，2024年AI技术发展主要体现在：
1. 大语言模型的突破...
2. 多模态AI的进展...
（基于搜索到的实时信息）
```

## 💾 记忆系统

### 会话管理
- **新建对话**: 点击"新对话"按钮
- **历史记录**: 左侧边栏显示对话历史
- **会话切换**: 点击历史记录项切换对话

### 记忆特点
- **长期记忆**: 重要对话内容会被长期保存
- **上下文理解**: AI能记住之前的对话内容
- **个性化**: 根据对话历史调整回复风格

## ⚙️ 设置和配置

### 桌面版设置
点击左下角"设置"按钮，可以配置：

**模型设置**:
- 当前加载的模型
- 可用模型列表
- 模型下载和管理

**生成参数**:
- 最大Token数（1-8192）
- 温度（0.1-2.0）
- Top-p（0.1-1.0）
- 图像生成参数

**界面设置**:
- 主题选择（深色/浅色）
- 字体设置
- 窗口大小

**高级设置**:
- NSFW内容开关
- 自动搜索开关
- 对话历史保存
- 模型路径配置

### Web版设置
在Web界面右侧面板中：
- 图像生成参数调整
- 模型状态查看
- 系统状态监控

## 🤖 模型管理

### 下载模型

**预设模型**（推荐）:
- Lucy-128k-4bit (4.7GB) - 高质量对话
- Qwen2.5-7B-4bit (4.4GB) - 中文优化
- Llama-3.1-8B-4bit (4.6GB) - 英文优秀

**自定义下载**:
1. 获取HuggingFace模型URL
2. 在设置中输入URL和模型名称
3. 点击下载并等待完成

### 模型切换
1. 在设置中选择要加载的模型
2. 点击"加载模型"
3. 等待加载完成（首次加载较慢）

### 模型要求
- **最低配置**: 8GB RAM
- **推荐配置**: 16GB RAM + GPU
- **存储空间**: 每个模型4-10GB

## 🎯 使用技巧

### 对话技巧
1. **明确表达**: 清楚描述你的需求
2. **上下文连贯**: 利用AI的记忆能力
3. **角色扮演**: 根据人设特点调整对话风格
4. **多轮对话**: 通过多轮交互获得更好的结果

### 图像生成技巧
1. **详细描述**: 提供具体的视觉细节
2. **风格指定**: 明确艺术风格或参考
3. **质量词汇**: 使用"masterpiece"、"high quality"等
4. **避免模糊**: 避免使用模糊或矛盾的描述

### 搜索优化
1. **关键词准确**: 使用准确的关键词
2. **时间限定**: 指定时间范围获得最新信息
3. **领域专业**: 使用专业术语获得精确结果

## 🔧 故障排除

### 常见问题

**Q: 模型加载失败**
A: 检查模型文件是否完整，重新下载或选择其他模型

**Q: 图像生成失败**
A: 确保图像模型已正确加载，检查显存是否足够

**Q: 搜索功能不工作**
A: 检查网络连接，确认DuckDuckGo可访问

**Q: 界面卡顿**
A: 关闭不必要的程序，增加系统内存

**Q: 对话历史丢失**
A: 检查数据库文件是否损坏，重新初始化数据库

### 性能优化
1. **关闭不用的功能**: 禁用自动搜索或图像生成
2. **调整参数**: 降低最大Token数或图像分辨率
3. **清理缓存**: 定期清理日志和临时文件
4. **硬件升级**: 增加内存或使用GPU加速

### 日志查看
日志文件位置：`logs/reverie_agents.log`
```bash
# 查看最新日志
tail -f logs/reverie_agents.log

# 搜索错误信息
grep "ERROR" logs/reverie_agents.log
```

## 📞 获取帮助

### 社区支持
- GitHub Issues: 报告bug和功能请求
- 用户论坛: 交流使用经验
- QQ群: 实时技术支持

### 文档资源
- [API文档](API_REFERENCE.md)
- [开发指南](DEVELOPMENT.md)
- [人设创建指南](PERSONA_GUIDE.md)
- [模型兼容性列表](MODEL_COMPATIBILITY.md)

### 联系方式
- 邮箱: <EMAIL>
- 官网: https://reverie-ai.com
- GitHub: https://github.com/reverie-ai/reverie-agents

---

**享受与Reverie的美好时光！** 💕
