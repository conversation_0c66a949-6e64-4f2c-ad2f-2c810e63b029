import{SvelteComponent as K,init as M,safe_not_equal as O,binding_callbacks as Q,bind as R,create_component as k,claim_component as d,mount_component as B,add_flush_callback as T,transition_in as r,transition_out as b,destroy_component as v,assign as U,space as F,claim_space as j,insert_hydration as q,group_outros as V,check_outros as X,get_spread_update as Y,get_spread_object as Z,detach as C}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{P as y,a as p}from"./Plot.BOh0qlQz.js";import{B as x,S as $}from"./2.B2AoQPnG.js";import{B as ee}from"./BlockLabel.BTSz9r5s.js";import{I as le}from"./IconButtonWrapper.D5aGR59h.js";import{F as ne}from"./FullscreenButton.g_8wwg6y.js";function A(t){let e,s;return e=new le({props:{$$slots:{default:[te]},$$scope:{ctx:t}}}),{c(){k(e.$$.fragment)},l(a){d(e.$$.fragment,a)},m(a,_){B(e,a,_),s=!0},p(a,_){const m={};_&17039360&&(m.$$scope={dirty:_,ctx:a}),e.$set(m)},i(a){s||(r(e.$$.fragment,a),s=!0)},o(a){b(e.$$.fragment,a),s=!1},d(a){v(e,a)}}}function te(t){let e,s;return e=new ne({props:{fullscreen:t[18]}}),e.$on("fullscreen",t[19]),{c(){k(e.$$.fragment)},l(a){d(e.$$.fragment,a)},m(a,_){B(e,a,_),s=!0},p(a,_){const m={};_&262144&&(m.fullscreen=a[18]),e.$set(m)},i(a){s||(r(e.$$.fragment,a),s=!0)},o(a){b(e.$$.fragment,a),s=!1},d(a){v(e,a)}}}function se(t){let e,s,a,_,m,i,u;e=new ee({props:{show_label:t[6],label:t[5]||t[13].i18n("plot.plot"),Icon:y}});let o=t[17]&&A(t);const h=[{autoscroll:t[13].autoscroll},{i18n:t[13].i18n},t[4]];let g={};for(let l=0;l<h.length;l+=1)g=U(g,h[l]);return _=new $({props:g}),_.$on("clear_status",t[20]),i=new p({props:{value:t[0],theme_mode:t[10],caption:t[11],bokeh_version:t[12],show_actions_button:t[14],gradio:t[13],show_label:t[6],_selectable:t[15],x_lim:t[16]}}),i.$on("change",t[21]),i.$on("select",t[22]),{c(){k(e.$$.fragment),s=F(),o&&o.c(),a=F(),k(_.$$.fragment),m=F(),k(i.$$.fragment)},l(l){d(e.$$.fragment,l),s=j(l),o&&o.l(l),a=j(l),d(_.$$.fragment,l),m=j(l),d(i.$$.fragment,l)},m(l,f){B(e,l,f),q(l,s,f),o&&o.m(l,f),q(l,a,f),B(_,l,f),q(l,m,f),B(i,l,f),u=!0},p(l,f){const w={};f&64&&(w.show_label=l[6]),f&8224&&(w.label=l[5]||l[13].i18n("plot.plot")),e.$set(w),l[17]?o?(o.p(l,f),f&131072&&r(o,1)):(o=A(l),o.c(),r(o,1),o.m(a.parentNode,a)):o&&(V(),b(o,1,1,()=>{o=null}),X());const P=f&8208?Y(h,[f&8192&&{autoscroll:l[13].autoscroll},f&8192&&{i18n:l[13].i18n},f&16&&Z(l[4])]):{};_.$set(P);const c={};f&1&&(c.value=l[0]),f&1024&&(c.theme_mode=l[10]),f&2048&&(c.caption=l[11]),f&4096&&(c.bokeh_version=l[12]),f&16384&&(c.show_actions_button=l[14]),f&8192&&(c.gradio=l[13]),f&64&&(c.show_label=l[6]),f&32768&&(c._selectable=l[15]),f&65536&&(c.x_lim=l[16]),i.$set(c)},i(l){u||(r(e.$$.fragment,l),r(o),r(_.$$.fragment,l),r(i.$$.fragment,l),u=!0)},o(l){b(e.$$.fragment,l),b(o),b(_.$$.fragment,l),b(i.$$.fragment,l),u=!1},d(l){l&&(C(s),C(a),C(m)),v(e,l),o&&o.d(l),v(_,l),v(i,l)}}}function ae(t){let e,s,a;function _(i){t[23](i)}let m={padding:!1,elem_id:t[1],elem_classes:t[2],visible:t[3],container:t[7],scale:t[8],min_width:t[9],allow_overflow:!1,$$slots:{default:[se]},$$scope:{ctx:t}};return t[18]!==void 0&&(m.fullscreen=t[18]),e=new x({props:m}),Q.push(()=>R(e,"fullscreen",_)),{c(){k(e.$$.fragment)},l(i){d(e.$$.fragment,i)},m(i,u){B(e,i,u),a=!0},p(i,[u]){const o={};u&2&&(o.elem_id=i[1]),u&4&&(o.elem_classes=i[2]),u&8&&(o.visible=i[3]),u&128&&(o.container=i[7]),u&256&&(o.scale=i[8]),u&512&&(o.min_width=i[9]),u&17300593&&(o.$$scope={dirty:u,ctx:i}),!s&&u&262144&&(s=!0,o.fullscreen=i[18],T(()=>s=!1)),e.$set(o)},i(i){a||(r(e.$$.fragment,i),a=!0)},o(i){b(e.$$.fragment,i),a=!1},d(i){v(e,i)}}}function ie(t,e,s){let{value:a=null}=e,{elem_id:_=""}=e,{elem_classes:m=[]}=e,{visible:i=!0}=e,{loading_status:u}=e,{label:o}=e,{show_label:h}=e,{container:g=!0}=e,{scale:l=null}=e,{min_width:f=void 0}=e,{theme_mode:w}=e,{caption:P}=e,{bokeh_version:c}=e,{gradio:I}=e,{show_actions_button:L=!1}=e,{_selectable:N=!1}=e,{x_lim:W=null}=e,{show_fullscreen_button:z=!1}=e,S=!1;const D=({detail:n})=>{s(18,S=n)},E=()=>I.dispatch("clear_status",u),G=()=>I.dispatch("change"),H=n=>I.dispatch("select",n.detail);function J(n){S=n,s(18,S)}return t.$$set=n=>{"value"in n&&s(0,a=n.value),"elem_id"in n&&s(1,_=n.elem_id),"elem_classes"in n&&s(2,m=n.elem_classes),"visible"in n&&s(3,i=n.visible),"loading_status"in n&&s(4,u=n.loading_status),"label"in n&&s(5,o=n.label),"show_label"in n&&s(6,h=n.show_label),"container"in n&&s(7,g=n.container),"scale"in n&&s(8,l=n.scale),"min_width"in n&&s(9,f=n.min_width),"theme_mode"in n&&s(10,w=n.theme_mode),"caption"in n&&s(11,P=n.caption),"bokeh_version"in n&&s(12,c=n.bokeh_version),"gradio"in n&&s(13,I=n.gradio),"show_actions_button"in n&&s(14,L=n.show_actions_button),"_selectable"in n&&s(15,N=n._selectable),"x_lim"in n&&s(16,W=n.x_lim),"show_fullscreen_button"in n&&s(17,z=n.show_fullscreen_button)},[a,_,m,i,u,o,h,g,l,f,w,P,c,I,L,N,W,z,S,D,E,G,H,J]}class be extends K{constructor(e){super(),M(this,e,ie,ae,O,{value:0,elem_id:1,elem_classes:2,visible:3,loading_status:4,label:5,show_label:6,container:7,scale:8,min_width:9,theme_mode:10,caption:11,bokeh_version:12,gradio:13,show_actions_button:14,_selectable:15,x_lim:16,show_fullscreen_button:17})}}export{p as BasePlot,be as default};
//# sourceMappingURL=Index.DVGOl5fo.js.map
