const e="Norsk bokmål",o={annotated_image:"Annotert bilde"},r={allow_recording_access:"Vennligst tillat mikrofontilgang for opptak.",audio:"Lyd",record_from_microphone:"Ta opp fra mikrofon",stop_recording:"Stopp opptak",no_device_support:"Kan ikke få tilgang til medieenheter. Sørg for at du kjører på en sikker kilde (https) eller localhost (eller har gitt et gyldig SSL-sertifikat til ssl_verify), og at du har gitt nettleseren tilgang til enheten din.",stop:"Stopp",resume:"Fortsett",record:"Ta opp",no_microphone:"Ingen mikrofon funnet",pause:"Pause",play:"Spill av",waiting:"Venter",drop_to_upload:"Slipp en lydfil her for å laste opp"},t={connection_can_break:"På mobil kan tilkoblingen brytes hvis denne fanen mister fokus eller enheten går i dvale, og du mister plassen din i køen.",long_requests_queue:"Det er en lang kø med ventende forespørsler. Dupliser denne Space for å hoppe over køen.",lost_connection:"Mistet tilkobling på grunn av at siden ble forlatt. Går tilbake til køen...",waiting_for_inputs:"Venter på at fil(er) skal bli lastet opp, vennligst prøv igjen."},n={checkbox:"Avkrysningsboks",checkbox_group:"Avkrysningsboksgruppe"},i={code:"Kode"},l={color_picker:"Fargevelger"},a={built_with:"bygget med",built_with_gradio:"Bygget med Gradio",clear:"Tøm",download:"Last ned",edit:"Rediger",empty:"Tom",error:"Feil",hosted_on:"Hostet på",loading:"Laster",logo:"Logo",or:"eller",remove:"Fjern",settings:"Innstillinger",share:"Del",submit:"Send",undo:"Angre",no_devices:"Ingen enheter funnet",language:"Språk",display_theme:"Visningstema",pwa:"Progressiv webapplikasjon"},s={incorrect_format:"Feil format, kun CSV- og TSV-filer støttes",new_column:"Legg til kolonne",new_row:"Ny rad",add_row_above:"Legg til rad over",add_row_below:"Legg til rad under",add_column_left:"Legg til kolonne til venstre",add_column_right:"Legg til kolonne til høyre",delete_row:"Slett rad",delete_column:"Slett kolonne",sort_column:"Sorter kolonne",sort_ascending:"Sorter stigende",sort_descending:"Sorter synkende",drop_to_upload:"Slipp CSV- eller TSV-filer her for å importere data til dataramme",clear_sort:"Fjern sortering"},d={dropdown:"Nedtrekksmeny"},p={build_error:"Det er en byggefeil",config_error:"Det er en konfigurasjonsfeil",contact_page_author:"Vennligst kontakt sidens forfatter for å informere dem.",no_app_file:"Det er ingen app-fil",runtime_error:"Det er en kjøretidsfeil",space_not_working:'"Space fungerer ikke fordi" {0}',space_paused:"Space er pauset",use_via_api:"Bruk via API",use_via_api_or_mcp:"Bruk via API eller MCP"},g={uploading:"Laster opp..."},c={highlighted_text:"Uthevet tekst"},_={allow_webcam_access:"Vennligst tillat webkameratilgang for opptak.",brush_color:"Penselfarge",brush_radius:"Penselstørrelse",image:"Bilde",remove_image:"Fjern bilde",select_brush_color:"Velg penselfarge",start_drawing:"Start tegning",use_brush:"Bruk pensel",drop_to_upload:"Slipp en bildefil her for å laste opp"},k={label:"Etikett"},u={enable_cookies:"Hvis du besøker en HuggingFace Space i inkognitomodus, må du aktivere tredjeparts informasjonskapsler.",incorrect_credentials:"Feil påloggingsinformasjon",username:"Brukernavn",password:"Passord",login:"Logg inn"},m={number:"Tall"},f={plot:"Plott"},b={radio:"Radioknapp"},h={slider:"Glidebryter"},v={click_to_upload:"Klikk for å laste opp",drop_audio:"Slipp lyd her",drop_csv:"Slipp CSV her",drop_file:"Slipp fil her",drop_image:"Slipp bilde her",drop_video:"Slipp video her",drop_gallery:"Slipp media her",paste_clipboard:"Lim inn fra utklippstavle"},S={drop_to_upload:"Slipp en videofil her for å laste opp"},w={edit:"Rediger",retry:"Prøv igjen",undo:"Angre",submit:"Send",cancel:"Avbryt",like:"Liker",dislike:"Liker ikke",clear:"Tøm samtalen"},y={_name:e,"3D_model":{"3d_model":"3D-modell",drop_to_upload:"Slipp en 3D-modell (.obj, .glb, .stl, .gltf, .splat eller .ply) fil her for å laste opp"},annotated_image:o,audio:r,blocks:t,checkbox:n,code:i,color_picker:l,common:a,dataframe:s,dropdown:d,errors:p,file:g,highlighted_text:c,image:_,label:k,login:u,number:m,plot:f,radio:b,slider:h,upload_text:v,video:S,chatbot:w};export{e as _name,o as annotated_image,r as audio,t as blocks,w as chatbot,n as checkbox,i as code,l as color_picker,a as common,s as dataframe,y as default,d as dropdown,p as errors,g as file,c as highlighted_text,_ as image,k as label,u as login,m as number,f as plot,b as radio,h as slider,v as upload_text,S as video};
//# sourceMappingURL=nb.CGxYPyHM.js.map
