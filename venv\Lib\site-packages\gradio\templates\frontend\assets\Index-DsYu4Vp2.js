import"./index-B7J2Z2jS.js";import{c as q,a as X}from"./utils-BsGrhMNe.js";import{C as K}from"./Check-CEkiXcyC.js";import{C as L}from"./Copy-CxQ9EyK2.js";/* empty css                                                        */import{M as Y}from"./MarkdownCode-CkSMBRHJ.js";import{I as Z}from"./IconButton-C_HS7fTi.js";import{I as y}from"./IconButtonWrapper--EIOWuEM.js";import{S as x}from"./index-B1FJGuzG.js";import"./StreamingBar.svelte_svelte_type_style_lang-DyfAPhwM.js";import{B as $}from"./Block-CJdXVpa7.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import{default as Ve}from"./Example-CyYX3U0b.js";import"./svelte/svelte.js";import"./Clear-By3xiIwg.js";import"./prism-python-MMh3z1bK.js";const{SvelteComponent:p,action_destroyer:ee,append:te,attr:w,check_outros:ie,create_component:G,destroy_component:H,detach:se,element:ne,flush:b,group_outros:le,init:ae,insert:_e,mount_component:J,safe_not_equal:he,set_style:N,space:oe,toggle_class:O,transition_in:C,transition_out:M}=window.__gradio__svelte__internal,{createEventDispatcher:re}=window.__gradio__svelte__internal;function P(i){let e,s;return e=new y({props:{$$slots:{default:[me]},$$scope:{ctx:i}}}),{c(){G(e.$$.fragment)},m(t,a){J(e,t,a),s=!0},p(t,a){const h={};a&270336&&(h.$$scope={dirty:a,ctx:t}),e.$set(h)},i(t){s||(C(e.$$.fragment,t),s=!0)},o(t){M(e.$$.fragment,t),s=!1},d(t){H(e,t)}}}function me(i){let e,s;return e=new Z({props:{Icon:i[13]?K:L,label:i[13]?"Copied conversation":"Copy conversation"}}),e.$on("click",i[14]),{c(){G(e.$$.fragment)},m(t,a){J(e,t,a),s=!0},p(t,a){const h={};a&8192&&(h.Icon=t[13]?K:L),a&8192&&(h.label=t[13]?"Copied conversation":"Copy conversation"),e.$set(h)},i(t){s||(C(e.$$.fragment,t),s=!0)},o(t){M(e.$$.fragment,t),s=!1},d(t){H(e,t)}}}function ue(i){let e,s,t,a,h,c,f,l,o,m=i[10]&&P(i);return t=new Y({props:{message:i[2],latex_delimiters:i[7],sanitize_html:i[5],line_breaks:i[6],chatbot:!1,header_links:i[8],theme_mode:i[12]}}),{c(){e=ne("div"),m&&m.c(),s=oe(),G(t.$$.fragment),w(e,"class",a="prose "+(i[0]?.join(" ")||"")+" svelte-lag733"),w(e,"data-testid","markdown"),w(e,"dir",h=i[4]?"rtl":"ltr"),w(e,"style",c=i[9]?`max-height: ${q(i[9])}; overflow-y: auto;`:""),O(e,"hide",!i[1]),N(e,"min-height",i[3]&&i[11]?.status!=="pending"?q(i[3]):void 0)},m(n,u){_e(n,e,u),m&&m.m(e,null),te(e,s),J(t,e,null),f=!0,l||(o=ee(X.call(null,e)),l=!0)},p(n,[u]){n[10]?m?(m.p(n,u),u&1024&&C(m,1)):(m=P(n),m.c(),C(m,1),m.m(e,s)):m&&(le(),M(m,1,1,()=>{m=null}),ie());const d={};u&4&&(d.message=n[2]),u&128&&(d.latex_delimiters=n[7]),u&32&&(d.sanitize_html=n[5]),u&64&&(d.line_breaks=n[6]),u&256&&(d.header_links=n[8]),u&4096&&(d.theme_mode=n[12]),t.$set(d),(!f||u&1&&a!==(a="prose "+(n[0]?.join(" ")||"")+" svelte-lag733"))&&w(e,"class",a),(!f||u&16&&h!==(h=n[4]?"rtl":"ltr"))&&w(e,"dir",h),(!f||u&512&&c!==(c=n[9]?`max-height: ${q(n[9])}; overflow-y: auto;`:""))&&w(e,"style",c),(!f||u&3)&&O(e,"hide",!n[1]);const k=u&512;(u&2568||k)&&N(e,"min-height",n[3]&&n[11]?.status!=="pending"?q(n[3]):void 0)},i(n){f||(C(m),C(t.$$.fragment,n),f=!0)},o(n){M(m),M(t.$$.fragment,n),f=!1},d(n){n&&se(e),m&&m.d(),H(t),l=!1,o()}}}function fe(i,e,s){let{elem_classes:t=[]}=e,{visible:a=!0}=e,{value:h}=e,{min_height:c=void 0}=e,{rtl:f=!1}=e,{sanitize_html:l=!0}=e,{line_breaks:o=!1}=e,{latex_delimiters:m}=e,{header_links:n=!1}=e,{height:u=void 0}=e,{show_copy_button:d=!1}=e,{loading_status:k=void 0}=e,{theme_mode:I}=e,v=!1,z;const B=re();async function S(){"clipboard"in navigator&&(await navigator.clipboard.writeText(h),B("copy",{value:h}),j())}function j(){s(13,v=!0),z&&clearTimeout(z),z=setTimeout(()=>{s(13,v=!1)},1e3)}return i.$$set=r=>{"elem_classes"in r&&s(0,t=r.elem_classes),"visible"in r&&s(1,a=r.visible),"value"in r&&s(2,h=r.value),"min_height"in r&&s(3,c=r.min_height),"rtl"in r&&s(4,f=r.rtl),"sanitize_html"in r&&s(5,l=r.sanitize_html),"line_breaks"in r&&s(6,o=r.line_breaks),"latex_delimiters"in r&&s(7,m=r.latex_delimiters),"header_links"in r&&s(8,n=r.header_links),"height"in r&&s(9,u=r.height),"show_copy_button"in r&&s(10,d=r.show_copy_button),"loading_status"in r&&s(11,k=r.loading_status),"theme_mode"in r&&s(12,I=r.theme_mode)},i.$$.update=()=>{i.$$.dirty&4&&B("change")},[t,a,h,c,f,l,o,m,n,u,d,k,I,v,S]}class ge extends p{constructor(e){super(),ae(this,e,fe,ue,he,{elem_classes:0,visible:1,value:2,min_height:3,rtl:4,sanitize_html:5,line_breaks:6,latex_delimiters:7,header_links:8,height:9,show_copy_button:10,loading_status:11,theme_mode:12})}get elem_classes(){return this.$$.ctx[0]}set elem_classes(e){this.$$set({elem_classes:e}),b()}get visible(){return this.$$.ctx[1]}set visible(e){this.$$set({visible:e}),b()}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),b()}get min_height(){return this.$$.ctx[3]}set min_height(e){this.$$set({min_height:e}),b()}get rtl(){return this.$$.ctx[4]}set rtl(e){this.$$set({rtl:e}),b()}get sanitize_html(){return this.$$.ctx[5]}set sanitize_html(e){this.$$set({sanitize_html:e}),b()}get line_breaks(){return this.$$.ctx[6]}set line_breaks(e){this.$$set({line_breaks:e}),b()}get latex_delimiters(){return this.$$.ctx[7]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),b()}get header_links(){return this.$$.ctx[8]}set header_links(e){this.$$set({header_links:e}),b()}get height(){return this.$$.ctx[9]}set height(e){this.$$set({height:e}),b()}get show_copy_button(){return this.$$.ctx[10]}set show_copy_button(e){this.$$set({show_copy_button:e}),b()}get loading_status(){return this.$$.ctx[11]}set loading_status(e){this.$$set({loading_status:e}),b()}get theme_mode(){return this.$$.ctx[12]}set theme_mode(e){this.$$set({theme_mode:e}),b()}}const ce=ge,{SvelteComponent:de,assign:be,attr:ke,create_component:E,destroy_component:D,detach:Q,element:we,flush:g,get_spread_object:ve,get_spread_update:ze,init:Ce,insert:R,mount_component:W,safe_not_equal:Ie,space:Be,toggle_class:T,transition_in:A,transition_out:F}=window.__gradio__svelte__internal;function Me(i){let e,s,t,a,h;const c=[{autoscroll:i[8].autoscroll},{i18n:i[8].i18n},i[4],{variant:"center"}];let f={};for(let l=0;l<c.length;l+=1)f=be(f,c[l]);return e=new x({props:f}),e.$on("clear_status",i[18]),a=new ce({props:{value:i[3],elem_classes:i[1],visible:i[2],rtl:i[5],latex_delimiters:i[9],sanitize_html:i[6],line_breaks:i[7],header_links:i[10],show_copy_button:i[14],loading_status:i[4],theme_mode:i[16]}}),a.$on("change",i[19]),a.$on("copy",i[20]),{c(){E(e.$$.fragment),s=Be(),t=we("div"),E(a.$$.fragment),ke(t,"class","svelte-vuh1yp"),T(t,"padding",i[17]),T(t,"pending",i[4]?.status==="pending")},m(l,o){W(e,l,o),R(l,s,o),R(l,t,o),W(a,t,null),h=!0},p(l,o){const m=o&272?ze(c,[o&256&&{autoscroll:l[8].autoscroll},o&256&&{i18n:l[8].i18n},o&16&&ve(l[4]),c[3]]):{};e.$set(m);const n={};o&8&&(n.value=l[3]),o&2&&(n.elem_classes=l[1]),o&4&&(n.visible=l[2]),o&32&&(n.rtl=l[5]),o&512&&(n.latex_delimiters=l[9]),o&64&&(n.sanitize_html=l[6]),o&128&&(n.line_breaks=l[7]),o&1024&&(n.header_links=l[10]),o&16384&&(n.show_copy_button=l[14]),o&16&&(n.loading_status=l[4]),o&65536&&(n.theme_mode=l[16]),a.$set(n),(!h||o&131072)&&T(t,"padding",l[17]),(!h||o&16)&&T(t,"pending",l[4]?.status==="pending")},i(l){h||(A(e.$$.fragment,l),A(a.$$.fragment,l),h=!0)},o(l){F(e.$$.fragment,l),F(a.$$.fragment,l),h=!1},d(l){l&&(Q(s),Q(t)),D(e,l),D(a)}}}function Se(i){let e,s;return e=new $({props:{visible:i[2],elem_id:i[0],elem_classes:i[1],container:i[15],allow_overflow:!0,overflow_behavior:"auto",height:i[11],min_height:i[12],max_height:i[13],$$slots:{default:[Me]},$$scope:{ctx:i}}}),{c(){E(e.$$.fragment)},m(t,a){W(e,t,a),s=!0},p(t,[a]){const h={};a&4&&(h.visible=t[2]),a&1&&(h.elem_id=t[0]),a&2&&(h.elem_classes=t[1]),a&32768&&(h.container=t[15]),a&2048&&(h.height=t[11]),a&4096&&(h.min_height=t[12]),a&8192&&(h.max_height=t[13]),a&2312190&&(h.$$scope={dirty:a,ctx:t}),e.$set(h)},i(t){s||(A(e.$$.fragment,t),s=!0)},o(t){F(e.$$.fragment,t),s=!1},d(t){D(e,t)}}}function je(i,e,s){let{elem_id:t=""}=e,{elem_classes:a=[]}=e,{visible:h=!0}=e,{value:c=""}=e,{loading_status:f}=e,{rtl:l=!1}=e,{sanitize_html:o=!0}=e,{line_breaks:m=!1}=e,{gradio:n}=e,{latex_delimiters:u}=e,{header_links:d=!1}=e,{height:k}=e,{min_height:I}=e,{max_height:v}=e,{show_copy_button:z=!1}=e,{container:B=!1}=e,{theme_mode:S}=e,{padding:j=!1}=e;const r=()=>n.dispatch("clear_status",f),U=()=>n.dispatch("change"),V=_=>n.dispatch("copy",_.detail);return i.$$set=_=>{"elem_id"in _&&s(0,t=_.elem_id),"elem_classes"in _&&s(1,a=_.elem_classes),"visible"in _&&s(2,h=_.visible),"value"in _&&s(3,c=_.value),"loading_status"in _&&s(4,f=_.loading_status),"rtl"in _&&s(5,l=_.rtl),"sanitize_html"in _&&s(6,o=_.sanitize_html),"line_breaks"in _&&s(7,m=_.line_breaks),"gradio"in _&&s(8,n=_.gradio),"latex_delimiters"in _&&s(9,u=_.latex_delimiters),"header_links"in _&&s(10,d=_.header_links),"height"in _&&s(11,k=_.height),"min_height"in _&&s(12,I=_.min_height),"max_height"in _&&s(13,v=_.max_height),"show_copy_button"in _&&s(14,z=_.show_copy_button),"container"in _&&s(15,B=_.container),"theme_mode"in _&&s(16,S=_.theme_mode),"padding"in _&&s(17,j=_.padding)},[t,a,h,c,f,l,o,m,n,u,d,k,I,v,z,B,S,j,r,U,V]}class Qe extends de{constructor(e){super(),Ce(this,e,je,Se,Ie,{elem_id:0,elem_classes:1,visible:2,value:3,loading_status:4,rtl:5,sanitize_html:6,line_breaks:7,gradio:8,latex_delimiters:9,header_links:10,height:11,min_height:12,max_height:13,show_copy_button:14,container:15,theme_mode:16,padding:17})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),g()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),g()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),g()}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),g()}get loading_status(){return this.$$.ctx[4]}set loading_status(e){this.$$set({loading_status:e}),g()}get rtl(){return this.$$.ctx[5]}set rtl(e){this.$$set({rtl:e}),g()}get sanitize_html(){return this.$$.ctx[6]}set sanitize_html(e){this.$$set({sanitize_html:e}),g()}get line_breaks(){return this.$$.ctx[7]}set line_breaks(e){this.$$set({line_breaks:e}),g()}get gradio(){return this.$$.ctx[8]}set gradio(e){this.$$set({gradio:e}),g()}get latex_delimiters(){return this.$$.ctx[9]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),g()}get header_links(){return this.$$.ctx[10]}set header_links(e){this.$$set({header_links:e}),g()}get height(){return this.$$.ctx[11]}set height(e){this.$$set({height:e}),g()}get min_height(){return this.$$.ctx[12]}set min_height(e){this.$$set({min_height:e}),g()}get max_height(){return this.$$.ctx[13]}set max_height(e){this.$$set({max_height:e}),g()}get show_copy_button(){return this.$$.ctx[14]}set show_copy_button(e){this.$$set({show_copy_button:e}),g()}get container(){return this.$$.ctx[15]}set container(e){this.$$set({container:e}),g()}get theme_mode(){return this.$$.ctx[16]}set theme_mode(e){this.$$set({theme_mode:e}),g()}get padding(){return this.$$.ctx[17]}set padding(e){this.$$set({padding:e}),g()}}export{Ve as BaseExample,ce as BaseMarkdown,Qe as default};
//# sourceMappingURL=Index-DsYu4Vp2.js.map
