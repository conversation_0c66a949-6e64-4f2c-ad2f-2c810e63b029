import{F as e,j as i}from"./declarationMapper.UBCwU7BT.js";import{b as o}from"./KHR_interactivity.DEAVS2UW.js";import{R as r}from"./index.BoI39RQH.js";class u extends o{constructor(t={}){super(t),this.config=t,this.config.startIndex=t.startIndex??new e(0),this.reset=this._registerSignalInput("reset"),this.maxExecutions=this.registerDataInput("maxExecutions",i),this.executionCount=this.registerDataOutput("executionCount",i,new e(0))}_execute(t,a){if(a===this.reset)this.executionCount.setValue(this.config.startIndex,t);else{const s=this.executionCount.getValue(t);s.value<this.maxExecutions.getValue(t).value&&(this.executionCount.setValue(new e(s.value+1),t),this.out._activateSignal(t))}}getClassName(){return"FlowGraphDoNBlock"}}r("FlowGraphDoNBlock",u);export{u as FlowGraphDoNBlock};
//# sourceMappingURL=flowGraphDoNBlock.DCMR9_vT.js.map
