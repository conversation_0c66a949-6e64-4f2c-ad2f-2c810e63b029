import{F as r,g as a,i as u}from"./KHR_interactivity-DTxiAnOo.js";import{R as e}from"./declarationMapper-BZjsjg7g.js";import{R as h}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class p extends r{constructor(s){super(s),this.config=s,this._inputCases=new Map,this.case=this.registerDataInput("case",e,NaN),this.default=this.registerDataInput("default",e),this.value=this.registerDataOutput("value",e),(this.config.cases||[]).forEach(t=>{t=a(t),!(this.config.treatCasesAsIntegers&&(t=t|0,this._inputCases.has(t)))&&this._inputCases.set(t,this.registerDataInput(`in_${t}`,e))})}_updateOutputs(s){const t=this.case.getValue(s);let i;u(t)?i=this._getOutputValueForCase(a(t),s):i=this.default.getValue(s),this.value.setValue(i,s)}_getOutputValueForCase(s,t){return this._inputCases.get(s)?.getValue(t)}getClassName(){return"FlowGraphDataSwitchBlock"}}h("FlowGraphDataSwitchBlock",p);export{p as FlowGraphDataSwitchBlock};
//# sourceMappingURL=flowGraphDataSwitchBlock-HCrcDcCn.js.map
