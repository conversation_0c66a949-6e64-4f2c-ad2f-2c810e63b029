import './index4-HDyePXPv.js';

function t({url:o}){}

var _layout_server_ts = /*#__PURE__*/Object.freeze({
	__proto__: null,
	load: t
});

const index = 0;
let component_cache;
const component = async () => component_cache ??= (await import('./_layout.svelte-DtUVntm9.js')).default;
const server_id = "src/routes/+layout.server.ts";
const imports = ["_app/immutable/nodes/0.Djr7cQdC.js"];
const stylesheets = ["_app/immutable/assets/0.e0pfnN6l.css"];
const fonts = [];

export { component, fonts, imports, index, _layout_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=0-C5IPoxrR.js.map
