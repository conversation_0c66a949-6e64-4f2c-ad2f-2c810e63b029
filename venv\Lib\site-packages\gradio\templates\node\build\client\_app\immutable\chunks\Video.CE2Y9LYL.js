import{SvelteComponent as p,init as g,safe_not_equal as f,svg_element as h,claim_svg_element as l,children as u,detach as s,attr as e,insert_hydration as v,append_hydration as c,noop as a}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function w(d){let t,o,r;return{c(){t=h("svg"),o=h("polygon"),r=h("rect"),this.h()},l(i){t=l(i,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0,class:!0});var n=u(t);o=l(n,"polygon",{points:!0}),u(o).forEach(s),r=l(n,"rect",{x:!0,y:!0,width:!0,height:!0,rx:!0,ry:!0}),u(r).forEach(s),n.forEach(s),this.h()},h(){e(o,"points","23 7 16 12 23 17 23 7"),e(r,"x","1"),e(r,"y","5"),e(r,"width","15"),e(r,"height","14"),e(r,"rx","2"),e(r,"ry","2"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 24 24"),e(t,"fill","none"),e(t,"stroke","currentColor"),e(t,"stroke-width","1.5"),e(t,"stroke-linecap","round"),e(t,"stroke-linejoin","round"),e(t,"class","feather feather-video")},m(i,n){v(i,t,n),c(t,o),c(t,r)},p:a,i:a,o:a,d(i){i&&s(t)}}}class y extends p{constructor(t){super(),g(this,t,null,w,f,{})}}export{y as V};
//# sourceMappingURL=Video.CE2Y9LYL.js.map
