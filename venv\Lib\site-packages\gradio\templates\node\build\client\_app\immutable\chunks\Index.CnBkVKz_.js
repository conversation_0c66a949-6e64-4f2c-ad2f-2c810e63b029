import{SvelteComponent as je,init as Ge,safe_not_equal as Le,svg_element as pe,claim_svg_element as Ee,children as O,detach as S,attr as w,insert_hydration as J,append_hydration as R,noop as $,tick as Re,element as ee,create_component as W,space as ne,claim_element as te,claim_component as K,claim_space as se,toggle_class as ue,mount_component as X,set_input_value as Ye,action_destroyer as Et,listen as L,prevent_default as Tt,transition_in as v,group_outros as oe,transition_out as z,check_outros as ae,is_function as Ce,destroy_component as Y,run_all as zt,createEventDispatcher as Ct,beforeUpdate as Bt,onMount as ot,afterUpdate as Dt,text as We,claim_text as Ke,set_data as Xe,ensure_array_like as Qe,set_style as Je,destroy_each as qt,binding_callbacks as he,bind as ke,add_flush_callback as ve,bubble as ze,flush as U,assign as St,get_spread_update as Mt,get_spread_object as Ut}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{y as Vt,C as At,P as Ht,Q as Ft,r as It,B as Nt,S as Pt}from"./2.B2AoQPnG.js";import{F as Rt}from"./File.Dl9hvYLG.js";import{M as Ot}from"./SelectSource.CTC8Kkgx.js";import{M as jt}from"./Music.BVFRDHso.js";import{V as Gt}from"./Video.CE2Y9LYL.js";import{a as Lt}from"./Upload.yOHVlgUe.js";import{I as Wt}from"./InteractiveAudio.BOik-Knm.js";import{default as El}from"./Example.CLHP3nyK.js";function Kt(l){let e,t,i,o,n;return{c(){e=pe("svg"),t=pe("g"),i=pe("g"),o=pe("g"),n=pe("path"),this.h()},l(m){e=Ee(m,"svg",{fill:!0,width:!0,height:!0,viewBox:!0,xmlns:!0});var r=O(e);t=Ee(r,"g",{id:!0,"stroke-width":!0}),O(t).forEach(S),i=Ee(r,"g",{id:!0,"stroke-linecap":!0,"stroke-linejoin":!0}),O(i).forEach(S),o=Ee(r,"g",{id:!0});var a=O(o);n=Ee(a,"path",{d:!0,"fill-rule":!0}),O(n).forEach(S),a.forEach(S),r.forEach(S),this.h()},h(){w(t,"id","SVGRepo_bgCarrier"),w(t,"stroke-width","0"),w(i,"id","SVGRepo_tracerCarrier"),w(i,"stroke-linecap","round"),w(i,"stroke-linejoin","round"),w(n,"d","M1752.768 221.109C1532.646.986 1174.283.986 954.161 221.109l-838.588 838.588c-154.052 154.165-154.052 404.894 0 558.946 149.534 149.421 409.976 149.308 559.059 0l758.738-758.626c87.982-88.094 87.982-231.417 0-319.51-88.32-88.208-231.642-87.982-319.51 0l-638.796 638.908 79.85 79.849 638.795-638.908c43.934-43.821 115.539-43.934 159.812 0 43.934 44.047 43.934 115.877 0 159.812l-758.739 758.625c-110.23 110.118-289.355 110.005-399.36 0-110.118-110.117-110.005-289.242 0-399.247l838.588-838.588c175.963-175.962 462.382-176.188 638.909 0 176.075 176.188 176.075 462.833 0 638.908l-798.607 798.72 79.849 79.85 798.607-798.72c220.01-220.123 220.01-578.485 0-798.607"),w(n,"fill-rule","evenodd"),w(o,"id","SVGRepo_iconCarrier"),w(e,"fill","currentColor"),w(e,"width","100%"),w(e,"height","100%"),w(e,"viewBox","0 0 1920 1920"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(m,r){J(m,e,r),R(e,t),R(e,i),R(e,o),R(o,n)},p:$,i:$,o:$,d(m){m&&S(e)}}}class Xt extends je{constructor(e){super(),Ge(this,e,null,Kt,Le,{})}}async function Oe(l,e,t){if(await Re(),e===t)return;const i=window.getComputedStyle(l),o=parseFloat(i.paddingTop),n=parseFloat(i.paddingBottom),m=parseFloat(i.lineHeight);let r=t===void 0?!1:o+n+m*t,a=o+n+e*m;l.style.height="1px";let d;r&&l.scrollHeight>r?d=r:l.scrollHeight<a?d=a:d=l.scrollHeight,l.style.height=`${d}px`}function Yt(l,e){if(e.lines===e.max_lines)return;l.style.overflowY="scroll";function t(i){Oe(i.target,e.lines,e.max_lines)}if(l.addEventListener("input",t),!!e.text.trim())return Oe(l,e.lines,e.max_lines),{destroy:()=>l.removeEventListener("input",t)}}function Ze(l,e,t){const i=l.slice();return i[70]=e[t],i[72]=t,i}function Qt(l){let e;return{c(){e=We(l[7])},l(t){e=Ke(t,l[7])},m(t,i){J(t,e,i)},p(t,i){i[0]&128&&Xe(e,t[7])},d(t){t&&S(e)}}}function ye(l){let e,t,i,o=Qe(l[0].files),n=[];for(let a=0;a<o.length;a+=1)n[a]=xe(Ze(l,o,a));const m=a=>z(n[a],1,1,()=>{n[a]=null});let r=l[28]&&$e();return{c(){e=ee("div");for(let a=0;a<n.length;a+=1)n[a].c();t=ne(),r&&r.c(),this.h()},l(a){e=te(a,"DIV",{class:!0,"aria-label":!0,"data-testid":!0,style:!0});var d=O(e);for(let f=0;f<n.length;f+=1)n[f].l(d);t=se(d),r&&r.l(d),d.forEach(S),this.h()},h(){w(e,"class","thumbnails scroll-hide svelte-5gfv2q"),w(e,"aria-label","Uploaded files"),w(e,"data-testid","container_el"),Je(e,"display",l[0].files.length>0||l[28]?"flex":"none")},m(a,d){J(a,e,d);for(let f=0;f<n.length;f+=1)n[f]&&n[f].m(e,null);R(e,t),r&&r.m(e,null),i=!0},p(a,d){if(d[0]&65|d[1]&32){o=Qe(a[0].files);let f;for(f=0;f<o.length;f+=1){const p=Ze(a,o,f);n[f]?(n[f].p(p,d),v(n[f],1)):(n[f]=xe(p),n[f].c(),v(n[f],1),n[f].m(e,t))}for(oe(),f=o.length;f<n.length;f+=1)m(f);ae()}a[28]?r||(r=$e(),r.c(),r.m(e,null)):r&&(r.d(1),r=null),(!i||d[0]&268435457)&&Je(e,"display",a[0].files.length>0||a[28]?"flex":"none")},i(a){if(!i){for(let d=0;d<o.length;d+=1)v(n[d]);i=!0}},o(a){n=n.filter(Boolean);for(let d=0;d<n.length;d+=1)z(n[d]);i=!1},d(a){a&&S(e),qt(n,a),r&&r.d()}}}function Jt(l){let e,t;return e=new Rt({}),{c(){W(e.$$.fragment)},l(i){K(e.$$.fragment,i)},m(i,o){X(e,i,o),t=!0},p:$,i(i){t||(v(e.$$.fragment,i),t=!0)},o(i){z(e.$$.fragment,i),t=!1},d(i){Y(e,i)}}}function Zt(l){let e,t;return e=new Gt({}),{c(){W(e.$$.fragment)},l(i){K(e.$$.fragment,i)},m(i,o){X(e,i,o),t=!0},p:$,i(i){t||(v(e.$$.fragment,i),t=!0)},o(i){z(e.$$.fragment,i),t=!1},d(i){Y(e,i)}}}function yt(l){let e,t;return e=new jt({}),{c(){W(e.$$.fragment)},l(i){K(e.$$.fragment,i)},m(i,o){X(e,i,o),t=!0},p:$,i(i){t||(v(e.$$.fragment,i),t=!0)},o(i){z(e.$$.fragment,i),t=!1},d(i){Y(e,i)}}}function xt(l){let e,t;return e=new It({props:{src:l[70].url,title:null,alt:"",loading:"lazy",class:"thumbnail-image"}}),{c(){W(e.$$.fragment)},l(i){K(e.$$.fragment,i)},m(i,o){X(e,i,o),t=!0},p(i,o){const n={};o[0]&1&&(n.src=i[70].url),e.$set(n)},i(i){t||(v(e.$$.fragment,i),t=!0)},o(i){z(e.$$.fragment,i),t=!1},d(i){Y(e,i)}}}function xe(l){let e,t,i,o,n,m,r,a,d,f,p,h,E;o=new At({});function u(...C){return l[51](l[72],...C)}const b=[xt,yt,Zt,Jt],k=[];function Z(C,F){return F[0]&1&&(m=null),F[0]&1&&(r=null),F[0]&1&&(a=null),m==null&&(m=!!(C[70].mime_type&&C[70].mime_type.includes("image"))),m?0:(r==null&&(r=!!(C[70].mime_type&&C[70].mime_type.includes("audio"))),r?1:(a==null&&(a=!!(C[70].mime_type&&C[70].mime_type.includes("video"))),a?2:3))}return d=Z(l,[-1,-1,-1]),f=k[d]=b[d](l),{c(){e=ee("span"),t=ee("button"),i=ee("button"),W(o.$$.fragment),n=ne(),f.c(),this.h()},l(C){e=te(C,"SPAN",{role:!0,"aria-label":!0});var F=O(e);t=te(F,"BUTTON",{class:!0});var N=O(t);i=te(N,"BUTTON",{class:!0});var y=O(i);K(o.$$.fragment,y),y.forEach(S),n=se(N),f.l(N),N.forEach(S),F.forEach(S),this.h()},h(){w(i,"class","delete-button svelte-5gfv2q"),ue(i,"disabled",l[6]),w(t,"class","thumbnail-item thumbnail-small svelte-5gfv2q"),w(e,"role","listitem"),w(e,"aria-label","File thumbnail")},m(C,F){J(C,e,F),R(e,t),R(t,i),X(o,i,null),R(t,n),k[d].m(t,null),p=!0,h||(E=L(i,"click",u),h=!0)},p(C,F){l=C,(!p||F[0]&64)&&ue(i,"disabled",l[6]);let N=d;d=Z(l,F),d===N?k[d].p(l,F):(oe(),z(k[N],1,1,()=>{k[N]=null}),ae(),f=k[d],f?f.p(l,F):(f=k[d]=b[d](l),f.c()),v(f,1),f.m(t,null))},i(C){p||(v(o.$$.fragment,C),v(f),p=!0)},o(C){z(o.$$.fragment,C),z(f),p=!1},d(C){C&&S(e),Y(o),k[d].d(),h=!1,E()}}}function $e(l){let e;return{c(){e=ee("div"),this.h()},l(t){e=te(t,"DIV",{class:!0,role:!0,"aria-label":!0}),O(e).forEach(S),this.h()},h(){w(e,"class","loader svelte-5gfv2q"),w(e,"role","status"),w(e,"aria-label","Uploading")},m(t,i){J(t,e,i)},d(t){t&&S(e)}}}function et(l){let e,t;return e=new Wt({props:{sources:["microphone"],class_name:"compact-audio",recording:at,waveform_settings:l[22],waveform_options:l[23],i18n:l[4],active_source:l[2],upload:l[19],stream_handler:l[20],stream_every:1,editable:!0,label:l[7],root:l[16],loop:!1,show_label:!1,show_download_button:!1,dragging:!1}}),e.$on("change",l[52]),e.$on("clear",l[53]),e.$on("start_recording",l[54]),e.$on("pause_recording",l[55]),e.$on("stop_recording",l[56]),{c(){W(e.$$.fragment)},l(i){K(e.$$.fragment,i)},m(i,o){X(e,i,o),t=!0},p(i,o){const n={};o[0]&4194304&&(n.waveform_settings=i[22]),o[0]&8388608&&(n.waveform_options=i[23]),o[0]&16&&(n.i18n=i[4]),o[0]&4&&(n.active_source=i[2]),o[0]&524288&&(n.upload=i[19]),o[0]&1048576&&(n.stream_handler=i[20]),o[0]&128&&(n.label=i[7]),o[0]&65536&&(n.root=i[16]),e.$set(n)},i(i){t||(v(e.$$.fragment,i),t=!0)},o(i){z(e.$$.fragment,i),t=!1},d(i){Y(e,i)}}}function tt(l){let e,t,i,o,n,m,r,a,d;function f(E){l[58](E)}function p(E){l[59](E)}let h={file_count:l[21],filetype:l[17],root:l[16],max_file_size:l[18],show_progress:!1,disable_click:!0,hidden:!0,upload:l[19],stream_handler:l[20]};return l[1]!==void 0&&(h.dragging=l[1]),l[28]!==void 0&&(h.uploading=l[28]),e=new Lt({props:h}),l[57](e),he.push(()=>ke(e,"dragging",f)),he.push(()=>ke(e,"uploading",p)),e.$on("load",l[35]),e.$on("error",l[60]),m=new Xt({}),{c(){W(e.$$.fragment),o=ne(),n=ee("button"),W(m.$$.fragment),this.h()},l(E){K(e.$$.fragment,E),o=se(E),n=te(E,"BUTTON",{"data-testid":!0,class:!0});var u=O(n);K(m.$$.fragment,u),u.forEach(S),this.h()},h(){w(n,"data-testid","upload-button"),w(n,"class","upload-button svelte-5gfv2q"),n.disabled=l[6]},m(E,u){X(e,E,u),J(E,o,u),J(E,n,u),X(m,n,null),r=!0,a||(d=L(n,"click",function(){Ce(l[6]?void 0:l[37])&&(l[6]?void 0:l[37]).apply(this,arguments)}),a=!0)},p(E,u){l=E;const b={};u[0]&2097152&&(b.file_count=l[21]),u[0]&131072&&(b.filetype=l[17]),u[0]&65536&&(b.root=l[16]),u[0]&262144&&(b.max_file_size=l[18]),u[0]&524288&&(b.upload=l[19]),u[0]&1048576&&(b.stream_handler=l[20]),!t&&u[0]&2&&(t=!0,b.dragging=l[1],ve(()=>t=!1)),!i&&u[0]&268435456&&(i=!0,b.uploading=l[28],ve(()=>i=!1)),e.$set(b),(!r||u[0]&64)&&(n.disabled=l[6])},i(E){r||(v(e.$$.fragment,E),v(m.$$.fragment,E),r=!0)},o(E){z(e.$$.fragment,E),z(m.$$.fragment,E),r=!1},d(E){E&&(S(o),S(n)),l[57](null),Y(e,E),Y(m),a=!1,d()}}}function lt(l){let e,t,i,o,n;return t=new Ot({}),{c(){e=ee("button"),W(t.$$.fragment),this.h()},l(m){e=te(m,"BUTTON",{"data-testid":!0,class:!0});var r=O(e);K(t.$$.fragment,r),r.forEach(S),this.h()},h(){w(e,"data-testid","microphone-button"),w(e,"class","microphone-button svelte-5gfv2q"),e.disabled=l[6],ue(e,"recording",at)},m(m,r){J(m,e,r),X(t,e,null),i=!0,o||(n=L(e,"click",function(){Ce(l[6]?void 0:l[61])&&(l[6]?void 0:l[61]).apply(this,arguments)}),o=!0)},p(m,r){l=m,(!i||r[0]&64)&&(e.disabled=l[6])},i(m){i||(v(t.$$.fragment,m),i=!0)},o(m){z(t.$$.fragment,m),i=!1},d(m){m&&S(e),Y(t),o=!1,n()}}}function it(l){let e,t,i,o,n,m;const r=[el,$t],a=[];function d(f,p){return f[11]===!0?0:1}return t=d(l),i=a[t]=r[t](l),{c(){e=ee("button"),i.c(),this.h()},l(f){e=te(f,"BUTTON",{class:!0});var p=O(e);i.l(p),p.forEach(S),this.h()},h(){w(e,"class","submit-button svelte-5gfv2q"),e.disabled=l[6],ue(e,"padded-button",l[11]!==!0)},m(f,p){J(f,e,p),a[t].m(e,null),o=!0,n||(m=L(e,"click",function(){Ce(l[6]?void 0:l[39])&&(l[6]?void 0:l[39]).apply(this,arguments)}),n=!0)},p(f,p){l=f;let h=t;t=d(l),t===h?a[t].p(l,p):(oe(),z(a[h],1,1,()=>{a[h]=null}),ae(),i=a[t],i?i.p(l,p):(i=a[t]=r[t](l),i.c()),v(i,1),i.m(e,null)),(!o||p[0]&64)&&(e.disabled=l[6]),(!o||p[0]&2048)&&ue(e,"padded-button",l[11]!==!0)},i(f){o||(v(i),o=!0)},o(f){z(i),o=!1},d(f){f&&S(e),a[t].d(),n=!1,m()}}}function $t(l){let e;return{c(){e=We(l[11])},l(t){e=Ke(t,l[11])},m(t,i){J(t,e,i)},p(t,i){i[0]&2048&&Xe(e,t[11])},i:$,o:$,d(t){t&&S(e)}}}function el(l){let e,t;return e=new Ht({}),{c(){W(e.$$.fragment)},l(i){K(e.$$.fragment,i)},m(i,o){X(e,i,o),t=!0},p:$,i(i){t||(v(e.$$.fragment,i),t=!0)},o(i){z(e.$$.fragment,i),t=!1},d(i){Y(e,i)}}}function nt(l){let e,t,i,o,n,m;const r=[ll,tl],a=[];function d(f,p){return f[12]===!0?0:1}return t=d(l),i=a[t]=r[t](l),{c(){e=ee("button"),i.c(),this.h()},l(f){e=te(f,"BUTTON",{class:!0});var p=O(e);i.l(p),p.forEach(S),this.h()},h(){w(e,"class","stop-button svelte-5gfv2q"),ue(e,"padded-button",l[12]!==!0)},m(f,p){J(f,e,p),a[t].m(e,null),o=!0,n||(m=L(e,"click",l[38]),n=!0)},p(f,p){let h=t;t=d(f),t===h?a[t].p(f,p):(oe(),z(a[h],1,1,()=>{a[h]=null}),ae(),i=a[t],i?i.p(f,p):(i=a[t]=r[t](f),i.c()),v(i,1),i.m(e,null)),(!o||p[0]&4096)&&ue(e,"padded-button",f[12]!==!0)},i(f){o||(v(i),o=!0)},o(f){z(i),o=!1},d(f){f&&S(e),a[t].d(),n=!1,m()}}}function tl(l){let e;return{c(){e=We(l[12])},l(t){e=Ke(t,l[12])},m(t,i){J(t,e,i)},p(t,i){i[0]&4096&&Xe(e,t[12])},i:$,o:$,d(t){t&&S(e)}}}function ll(l){let e,t;return e=new Ft({props:{fill:"none",stroke_width:2.5}}),{c(){W(e.$$.fragment)},l(i){K(e.$$.fragment,i)},m(i,o){X(e,i,o),t=!0},p:$,i(i){t||(v(e.$$.fragment,i),t=!0)},o(i){z(e.$$.fragment,i),t=!1},d(i){Y(e,i)}}}function il(l){let e,t,i,o,n=l[24]&&l[24].includes("microphone")&&l[2]==="microphone",m,r,a=l[24]&&l[24].includes("upload")&&!(l[21]==="single"&&l[0].files.length>0),d,f=l[24]&&l[24].includes("microphone"),p,h,E,u,b,k,Z,C,F,N,y,re,_e,fe,A,ce,me;t=new Vt({props:{show_label:l[9],info:l[8],rtl:l[13],$$slots:{default:[Qt]},$$scope:{ctx:l}}});let H=(l[0].files.length>0||l[28])&&ye(l),D=n&&et(l),q=a&&tt(l),B=f&&lt(l),T=l[11]&&it(l),V=l[12]&&nt(l);return{c(){e=ee("div"),W(t.$$.fragment),i=ne(),H&&H.c(),o=ne(),D&&D.c(),m=ne(),r=ee("div"),q&&q.c(),d=ne(),B&&B.c(),p=ne(),h=ee("textarea"),_e=ne(),T&&T.c(),fe=ne(),V&&V.c(),this.h()},l(c){e=te(c,"DIV",{class:!0,role:!0,"aria-label":!0});var g=O(e);K(t.$$.fragment,g),i=se(g),H&&H.l(g),o=se(g),D&&D.l(g),m=se(g),r=te(g,"DIV",{class:!0});var M=O(r);q&&q.l(M),d=se(M),B&&B.l(M),p=se(M),h=te(M,"TEXTAREA",{"data-testid":!0,class:!0,dir:!0,placeholder:!0,rows:!0,style:!0,autocapitalize:!0,autocorrect:!0,spellcheck:!0,autocomplete:!0,tabindex:!0,enterkeyhint:!0,lang:!0}),O(h).forEach(S),_e=se(M),T&&T.l(M),fe=se(M),V&&V.l(M),M.forEach(S),g.forEach(S),this.h()},h(){var c,g,M,le,ie,j,x;w(h,"data-testid","textbox"),w(h,"class","scroll-hide svelte-5gfv2q"),w(h,"dir",E=l[13]?"rtl":"ltr"),w(h,"placeholder",l[5]),w(h,"rows",l[3]),h.disabled=l[6],h.autofocus=l[14],w(h,"style",u=l[15]?"text-align: "+l[15]:""),w(h,"autocapitalize",b=(c=l[25])==null?void 0:c.autocapitalize),w(h,"autocorrect",k=(g=l[25])==null?void 0:g.autocorrect),w(h,"spellcheck",Z=(M=l[25])==null?void 0:M.spellcheck),w(h,"autocomplete",C=(le=l[25])==null?void 0:le.autocomplete),w(h,"tabindex",F=(ie=l[25])==null?void 0:ie.tabindex),w(h,"enterkeyhint",N=(j=l[25])==null?void 0:j.enterkeyhint),w(h,"lang",y=(x=l[25])==null?void 0:x.lang),ue(h,"no-label",!l[9]),w(r,"class","input-container svelte-5gfv2q"),w(e,"class","full-container svelte-5gfv2q"),w(e,"role","group"),w(e,"aria-label","Multimedia input field"),ue(e,"dragging",l[1])},m(c,g){J(c,e,g),X(t,e,null),R(e,i),H&&H.m(e,null),R(e,o),D&&D.m(e,null),R(e,m),R(e,r),q&&q.m(r,null),R(r,d),B&&B.m(r,null),R(r,p),R(r,h),Ye(h,l[0].text),l[63](h),R(r,_e),T&&T.m(r,null),R(r,fe),V&&V.m(r,null),l[64](e),A=!0,l[14]&&h.focus(),ce||(me=[Et(re=Yt.call(null,h,{text:l[0].text,lines:l[3],max_lines:l[10]})),L(h,"input",l[62]),L(h,"keypress",l[33]),L(h,"blur",l[49]),L(h,"select",l[32]),L(h,"focus",l[50]),L(h,"scroll",l[34]),L(h,"paste",l[40]),L(e,"dragenter",l[41]),L(e,"dragleave",l[42]),L(e,"dragover",Tt(l[48])),L(e,"drop",l[43])],ce=!0)},p(c,g){var le,ie,j,x,P,be,ge;const M={};g[0]&512&&(M.show_label=c[9]),g[0]&256&&(M.info=c[8]),g[0]&8192&&(M.rtl=c[13]),g[0]&128|g[2]&2048&&(M.$$scope={dirty:g,ctx:c}),t.$set(M),c[0].files.length>0||c[28]?H?(H.p(c,g),g[0]&268435457&&v(H,1)):(H=ye(c),H.c(),v(H,1),H.m(e,o)):H&&(oe(),z(H,1,1,()=>{H=null}),ae()),g[0]&16777220&&(n=c[24]&&c[24].includes("microphone")&&c[2]==="microphone"),n?D?(D.p(c,g),g[0]&16777220&&v(D,1)):(D=et(c),D.c(),v(D,1),D.m(e,m)):D&&(oe(),z(D,1,1,()=>{D=null}),ae()),g[0]&18874369&&(a=c[24]&&c[24].includes("upload")&&!(c[21]==="single"&&c[0].files.length>0)),a?q?(q.p(c,g),g[0]&18874369&&v(q,1)):(q=tt(c),q.c(),v(q,1),q.m(r,d)):q&&(oe(),z(q,1,1,()=>{q=null}),ae()),g[0]&16777216&&(f=c[24]&&c[24].includes("microphone")),f?B?(B.p(c,g),g[0]&16777216&&v(B,1)):(B=lt(c),B.c(),v(B,1),B.m(r,p)):B&&(oe(),z(B,1,1,()=>{B=null}),ae()),(!A||g[0]&8192&&E!==(E=c[13]?"rtl":"ltr"))&&w(h,"dir",E),(!A||g[0]&32)&&w(h,"placeholder",c[5]),(!A||g[0]&8)&&w(h,"rows",c[3]),(!A||g[0]&64)&&(h.disabled=c[6]),(!A||g[0]&16384)&&(h.autofocus=c[14]),(!A||g[0]&32768&&u!==(u=c[15]?"text-align: "+c[15]:""))&&w(h,"style",u),(!A||g[0]&33554432&&b!==(b=(le=c[25])==null?void 0:le.autocapitalize))&&w(h,"autocapitalize",b),(!A||g[0]&33554432&&k!==(k=(ie=c[25])==null?void 0:ie.autocorrect))&&w(h,"autocorrect",k),(!A||g[0]&33554432&&Z!==(Z=(j=c[25])==null?void 0:j.spellcheck))&&w(h,"spellcheck",Z),(!A||g[0]&33554432&&C!==(C=(x=c[25])==null?void 0:x.autocomplete))&&w(h,"autocomplete",C),(!A||g[0]&33554432&&F!==(F=(P=c[25])==null?void 0:P.tabindex))&&w(h,"tabindex",F),(!A||g[0]&33554432&&N!==(N=(be=c[25])==null?void 0:be.enterkeyhint))&&w(h,"enterkeyhint",N),(!A||g[0]&33554432&&y!==(y=(ge=c[25])==null?void 0:ge.lang))&&w(h,"lang",y),re&&Ce(re.update)&&g[0]&1033&&re.update.call(null,{text:c[0].text,lines:c[3],max_lines:c[10]}),g[0]&1&&Ye(h,c[0].text),(!A||g[0]&512)&&ue(h,"no-label",!c[9]),c[11]?T?(T.p(c,g),g[0]&2048&&v(T,1)):(T=it(c),T.c(),v(T,1),T.m(r,fe)):T&&(oe(),z(T,1,1,()=>{T=null}),ae()),c[12]?V?(V.p(c,g),g[0]&4096&&v(V,1)):(V=nt(c),V.c(),v(V,1),V.m(r,null)):V&&(oe(),z(V,1,1,()=>{V=null}),ae()),(!A||g[0]&2)&&ue(e,"dragging",c[1])},i(c){A||(v(t.$$.fragment,c),v(H),v(D),v(q),v(B),v(T),v(V),A=!0)},o(c){z(t.$$.fragment,c),z(H),z(D),z(q),z(B),z(T),z(V),A=!1},d(c){c&&S(e),Y(t),H&&H.d(),D&&D.d(),q&&q.d(),B&&B.d(),l[63](null),T&&T.d(),V&&V.d(),l[64](null),ce=!1,zt(me)}}}let at=!1;function nl(l,e,t){let{value:i={text:"",files:[]}}=e,{value_is_output:o=!1}=e,{lines:n=1}=e,{i18n:m}=e,{placeholder:r="Type here..."}=e,{disabled:a=!1}=e,{label:d}=e,{info:f=void 0}=e,{show_label:p=!0}=e,{max_lines:h}=e,{submit_btn:E=null}=e,{stop_btn:u=null}=e,{rtl:b=!1}=e,{autofocus:k=!1}=e,{text_align:Z=void 0}=e,{autoscroll:C=!0}=e,{root:F}=e,{file_types:N=null}=e,{max_file_size:y=null}=e,{upload:re}=e,{stream_handler:_e}=e,{file_count:fe="multiple"}=e,{max_plain_text_length:A=1e3}=e,{waveform_settings:ce}=e,{waveform_options:me={show_recording_waveform:!0}}=e,{sources:H=["upload"]}=e,{active_source:D=null}=e,{html_attributes:q=null}=e,B,T,V,c=0,g=!1,{dragging:M=!1}=e,le=!1,ie=(i==null?void 0:i.text)??"",j=null,x;const P=Ct();Bt(()=>{V=T&&T.offsetHeight+T.scrollTop>T.scrollHeight-100});const be=()=>{V&&C&&!g&&T.scrollTo(0,T.scrollHeight)};async function ge(){P("change",i),o||P("input")}ot(()=>{k&&T!==null&&T.focus()}),Dt(()=>{V&&C&&be(),t(44,o=!1)});function Be(s){const I=s.target,G=I.value,Q=[I.selectionStart,I.selectionEnd];P("select",{value:G.substring(...Q),index:Q})}async function De(s){await Re(),s.key==="Enter"&&s.shiftKey&&n>1?(s.preventDefault(),P("submit")):s.key==="Enter"&&!s.shiftKey&&n===1&&h>=1&&(s.preventDefault(),P("submit"),t(2,D=null),j&&(i.files.push(j),t(0,i),t(29,j=null)))}function qe(s){const I=s.target,G=I.scrollTop;G<c&&(g=!0),c=G;const Q=I.scrollHeight-I.clientHeight;G>=Q&&(g=!1)}async function Se({detail:s}){if(ge(),Array.isArray(s)){for(let I of s)i.files.push(I);t(0,i)}else i.files.push(s),t(0,i);await Re(),P("change",i),P("upload",s)}function Te(s,I){ge(),s.stopPropagation(),i.files.splice(I,1),t(0,i)}function Me(){B.open_upload()}function Ue(){P("stop")}function Ve(){P("submit"),t(2,D=null),j&&(i.files.push(j),t(0,i),t(29,j=null))}async function Ae(s){if(!s.clipboardData)return;const I=s.clipboardData.items,G=s.clipboardData.getData("text");if(G&&G.length>A){s.preventDefault();const Q=new window.File([G],"pasted_text.txt",{type:"text/plain",lastModified:Date.now()});B&&B.load_files([Q]);return}for(let Q in I){const de=I[Q];if(de.kind==="file"&&de.type.includes("image")){const we=de.getAsFile();we&&B.load_files([we])}}}function He(s){s.preventDefault(),t(1,M=!0)}function Fe(s){s.preventDefault();const I=x.getBoundingClientRect(),{clientX:G,clientY:Q}=s;(G<=I.left||G>=I.right||Q<=I.top||Q>=I.bottom)&&t(1,M=!1)}function Ie(s){if(s.preventDefault(),t(1,M=!1),s.dataTransfer&&s.dataTransfer.files){const I=Array.from(s.dataTransfer.files);if(N){const G=I.filter(de=>N.some(we=>we.startsWith(".")?de.name.toLowerCase().endsWith(we.toLowerCase()):de.type.match(new RegExp(we.replace("*",".*"))))),Q=I.length-G.length;Q>0&&P("error",`${Q} file(s) were rejected. Accepted formats: ${N.join(", ")}`),G.length>0&&B.load_files(G)}else B.load_files(I)}}function Ne(s){ze.call(this,l,s)}function Pe(s){ze.call(this,l,s)}function _(s){ze.call(this,l,s)}const ut=(s,I)=>Te(I,s),rt=({detail:s})=>{s!==null&&t(29,j=s)},ft=()=>{t(2,D=null)},_t=()=>P("start_recording"),ct=()=>P("pause_recording"),ht=()=>P("stop_recording");function mt(s){he[s?"unshift":"push"](()=>{B=s,t(27,B)})}function gt(s){M=s,t(1,M)}function dt(s){le=s,t(28,le)}function bt(s){ze.call(this,l,s)}const wt=()=>{t(2,D=D!=="microphone"?"microphone":null)};function kt(){i.text=this.value,t(0,i)}function vt(s){he[s?"unshift":"push"](()=>{T=s,t(26,T)})}function pt(s){he[s?"unshift":"push"](()=>{x=s,t(30,x)})}return l.$$set=s=>{"value"in s&&t(0,i=s.value),"value_is_output"in s&&t(44,o=s.value_is_output),"lines"in s&&t(3,n=s.lines),"i18n"in s&&t(4,m=s.i18n),"placeholder"in s&&t(5,r=s.placeholder),"disabled"in s&&t(6,a=s.disabled),"label"in s&&t(7,d=s.label),"info"in s&&t(8,f=s.info),"show_label"in s&&t(9,p=s.show_label),"max_lines"in s&&t(10,h=s.max_lines),"submit_btn"in s&&t(11,E=s.submit_btn),"stop_btn"in s&&t(12,u=s.stop_btn),"rtl"in s&&t(13,b=s.rtl),"autofocus"in s&&t(14,k=s.autofocus),"text_align"in s&&t(15,Z=s.text_align),"autoscroll"in s&&t(45,C=s.autoscroll),"root"in s&&t(16,F=s.root),"file_types"in s&&t(17,N=s.file_types),"max_file_size"in s&&t(18,y=s.max_file_size),"upload"in s&&t(19,re=s.upload),"stream_handler"in s&&t(20,_e=s.stream_handler),"file_count"in s&&t(21,fe=s.file_count),"max_plain_text_length"in s&&t(46,A=s.max_plain_text_length),"waveform_settings"in s&&t(22,ce=s.waveform_settings),"waveform_options"in s&&t(23,me=s.waveform_options),"sources"in s&&t(24,H=s.sources),"active_source"in s&&t(2,D=s.active_source),"html_attributes"in s&&t(25,q=s.html_attributes),"dragging"in s&&t(1,M=s.dragging)},l.$$.update=()=>{l.$$.dirty[0]&2&&P("drag",M),l.$$.dirty[0]&1&&i===null&&t(0,i={text:"",files:[]}),l.$$.dirty[0]&1|l.$$.dirty[1]&65536&&ie!==i.text&&(P("change",i),t(47,ie=i.text)),l.$$.dirty[0]&67109897&&T&&n!==h&&Oe(T,n,h)},[i,M,D,n,m,r,a,d,f,p,h,E,u,b,k,Z,F,N,y,re,_e,fe,ce,me,H,q,T,B,le,j,x,P,Be,De,qe,Se,Te,Me,Ue,Ve,Ae,He,Fe,Ie,o,C,A,ie,Ne,Pe,_,ut,rt,ft,_t,ct,ht,mt,gt,dt,bt,wt,kt,vt,pt]}class sl extends je{constructor(e){super(),Ge(this,e,nl,il,Le,{value:0,value_is_output:44,lines:3,i18n:4,placeholder:5,disabled:6,label:7,info:8,show_label:9,max_lines:10,submit_btn:11,stop_btn:12,rtl:13,autofocus:14,text_align:15,autoscroll:45,root:16,file_types:17,max_file_size:18,upload:19,stream_handler:20,file_count:21,max_plain_text_length:46,waveform_settings:22,waveform_options:23,sources:24,active_source:2,html_attributes:25,dragging:1},null,[-1,-1,-1])}}const ol=sl;function st(l){let e,t;const i=[{autoscroll:l[2].autoscroll},{i18n:l[2].i18n},l[17]];let o={};for(let n=0;n<i.length;n+=1)o=St(o,i[n]);return e=new Pt({props:o}),e.$on("clear_status",l[32]),{c(){W(e.$$.fragment)},l(n){K(e.$$.fragment,n)},m(n,m){X(e,n,m),t=!0},p(n,m){const r=m[0]&131076?Mt(i,[m[0]&4&&{autoscroll:n[2].autoscroll},m[0]&4&&{i18n:n[2].i18n},m[0]&131072&&Ut(n[17])]):{};e.$set(r)},i(n){t||(v(e.$$.fragment,n),t=!0)},o(n){z(e.$$.fragment,n),t=!1},d(n){Y(e,n)}}}function al(l){let e,t,i,o,n,m,r,a=l[17]&&st(l);function d(u){l[35](u)}function f(u){l[36](u)}function p(u){l[37](u)}function h(u){l[38](u)}let E={file_types:l[6],root:l[23],label:l[9],info:l[10],show_label:l[11],lines:l[7],rtl:l[18],text_align:l[19],waveform_settings:l[30],i18n:l[2].i18n,max_lines:l[12]?l[12]:l[7]+1,placeholder:l[8],submit_btn:l[15],stop_btn:l[16],autofocus:l[20],autoscroll:l[21],file_count:l[24],sources:l[26],max_file_size:l[2].max_file_size,disabled:!l[22],upload:l[33],stream_handler:l[34],max_plain_text_length:l[25],html_attributes:l[27]};return l[0]!==void 0&&(E.value=l[0]),l[1]!==void 0&&(E.value_is_output=l[1]),l[28]!==void 0&&(E.dragging=l[28]),l[29]!==void 0&&(E.active_source=l[29]),t=new ol({props:E}),he.push(()=>ke(t,"value",d)),he.push(()=>ke(t,"value_is_output",f)),he.push(()=>ke(t,"dragging",p)),he.push(()=>ke(t,"active_source",h)),t.$on("change",l[39]),t.$on("input",l[40]),t.$on("submit",l[41]),t.$on("stop",l[42]),t.$on("blur",l[43]),t.$on("select",l[44]),t.$on("focus",l[45]),t.$on("error",l[46]),t.$on("start_recording",l[47]),t.$on("pause_recording",l[48]),t.$on("stop_recording",l[49]),t.$on("upload",l[50]),t.$on("clear",l[51]),{c(){a&&a.c(),e=ne(),W(t.$$.fragment)},l(u){a&&a.l(u),e=se(u),K(t.$$.fragment,u)},m(u,b){a&&a.m(u,b),J(u,e,b),X(t,u,b),r=!0},p(u,b){u[17]?a?(a.p(u,b),b[0]&131072&&v(a,1)):(a=st(u),a.c(),v(a,1),a.m(e.parentNode,e)):a&&(oe(),z(a,1,1,()=>{a=null}),ae());const k={};b[0]&64&&(k.file_types=u[6]),b[0]&8388608&&(k.root=u[23]),b[0]&512&&(k.label=u[9]),b[0]&1024&&(k.info=u[10]),b[0]&2048&&(k.show_label=u[11]),b[0]&128&&(k.lines=u[7]),b[0]&262144&&(k.rtl=u[18]),b[0]&524288&&(k.text_align=u[19]),b[0]&1073741824&&(k.waveform_settings=u[30]),b[0]&4&&(k.i18n=u[2].i18n),b[0]&4224&&(k.max_lines=u[12]?u[12]:u[7]+1),b[0]&256&&(k.placeholder=u[8]),b[0]&32768&&(k.submit_btn=u[15]),b[0]&65536&&(k.stop_btn=u[16]),b[0]&1048576&&(k.autofocus=u[20]),b[0]&2097152&&(k.autoscroll=u[21]),b[0]&16777216&&(k.file_count=u[24]),b[0]&67108864&&(k.sources=u[26]),b[0]&4&&(k.max_file_size=u[2].max_file_size),b[0]&4194304&&(k.disabled=!u[22]),b[0]&4&&(k.upload=u[33]),b[0]&4&&(k.stream_handler=u[34]),b[0]&33554432&&(k.max_plain_text_length=u[25]),b[0]&134217728&&(k.html_attributes=u[27]),!i&&b[0]&1&&(i=!0,k.value=u[0],ve(()=>i=!1)),!o&&b[0]&2&&(o=!0,k.value_is_output=u[1],ve(()=>o=!1)),!n&&b[0]&268435456&&(n=!0,k.dragging=u[28],ve(()=>n=!1)),!m&&b[0]&536870912&&(m=!0,k.active_source=u[29],ve(()=>m=!1)),t.$set(k)},i(u){r||(v(a),v(t.$$.fragment,u),r=!0)},o(u){z(a),z(t.$$.fragment,u),r=!1},d(u){u&&S(e),a&&a.d(u),Y(t,u)}}}function ul(l){let e,t;return e=new Nt({props:{visible:l[5],elem_id:l[3],elem_classes:[...l[4],"multimodal-textbox"],scale:l[13],min_width:l[14],allow_overflow:!1,padding:!1,border_mode:l[28]?"focus":"base",$$slots:{default:[al]},$$scope:{ctx:l}}}),{c(){W(e.$$.fragment)},l(i){K(e.$$.fragment,i)},m(i,o){X(e,i,o),t=!0},p(i,o){const n={};o[0]&32&&(n.visible=i[5]),o[0]&8&&(n.elem_id=i[3]),o[0]&16&&(n.elem_classes=[...i[4],"multimodal-textbox"]),o[0]&8192&&(n.scale=i[13]),o[0]&16384&&(n.min_width=i[14]),o[0]&268435456&&(n.border_mode=i[28]?"focus":"base"),o[0]&2147459015|o[1]&16777216&&(n.$$scope={dirty:o,ctx:i}),e.$set(n)},i(i){t||(v(e.$$.fragment,i),t=!0)},o(i){z(e.$$.fragment,i),t=!1},d(i){Y(e,i)}}}function rl(l,e,t){let{gradio:i}=e,{elem_id:o=""}=e,{elem_classes:n=[]}=e,{visible:m=!0}=e,{value:r={text:"",files:[]}}=e,{file_types:a=null}=e,{lines:d}=e,{placeholder:f=""}=e,{label:p="MultimodalTextbox"}=e,{info:h=void 0}=e,{show_label:E}=e,{max_lines:u}=e,{scale:b=null}=e,{min_width:k=void 0}=e,{submit_btn:Z=null}=e,{stop_btn:C=null}=e,{loading_status:F=void 0}=e,{value_is_output:N=!1}=e,{rtl:y=!1}=e,{text_align:re=void 0}=e,{autofocus:_e=!1}=e,{autoscroll:fe=!0}=e,{interactive:A}=e,{root:ce}=e,{file_count:me}=e,{max_plain_text_length:H}=e,{sources:D=["upload"]}=e,{waveform_options:q={}}=e,{html_attributes:B=null}=e,T,V=null,c,g="darkorange";ot(()=>{g=getComputedStyle(document==null?void 0:document.documentElement).getPropertyValue("--color-accent"),le(),t(30,c.waveColor=q.waveform_color||"#9ca3af",c),t(30,c.progressColor=q.waveform_progress_color||g,c),t(30,c.mediaControls=q.show_controls,c),t(30,c.sampleRate=q.sample_rate||44100,c)});const M={color:q.trim_region_color,drag:!0,resize:!0};function le(){document.documentElement.style.setProperty("--trim-region-color",M.color||g)}const ie=()=>i.dispatch("clear_status",F),j=(..._)=>i.client.upload(..._),x=(..._)=>i.client.stream(..._);function P(_){r=_,t(0,r)}function be(_){N=_,t(1,N)}function ge(_){T=_,t(28,T)}function Be(_){V=_,t(29,V)}const De=()=>i.dispatch("change",r),qe=()=>i.dispatch("input"),Se=()=>i.dispatch("submit"),Te=()=>i.dispatch("stop"),Me=()=>i.dispatch("blur"),Ue=_=>i.dispatch("select",_.detail),Ve=()=>i.dispatch("focus"),Ae=({detail:_})=>{i.dispatch("error",_)},He=()=>i.dispatch("start_recording"),Fe=()=>i.dispatch("pause_recording"),Ie=()=>i.dispatch("stop_recording"),Ne=_=>i.dispatch("upload",_.detail),Pe=()=>i.dispatch("clear");return l.$$set=_=>{"gradio"in _&&t(2,i=_.gradio),"elem_id"in _&&t(3,o=_.elem_id),"elem_classes"in _&&t(4,n=_.elem_classes),"visible"in _&&t(5,m=_.visible),"value"in _&&t(0,r=_.value),"file_types"in _&&t(6,a=_.file_types),"lines"in _&&t(7,d=_.lines),"placeholder"in _&&t(8,f=_.placeholder),"label"in _&&t(9,p=_.label),"info"in _&&t(10,h=_.info),"show_label"in _&&t(11,E=_.show_label),"max_lines"in _&&t(12,u=_.max_lines),"scale"in _&&t(13,b=_.scale),"min_width"in _&&t(14,k=_.min_width),"submit_btn"in _&&t(15,Z=_.submit_btn),"stop_btn"in _&&t(16,C=_.stop_btn),"loading_status"in _&&t(17,F=_.loading_status),"value_is_output"in _&&t(1,N=_.value_is_output),"rtl"in _&&t(18,y=_.rtl),"text_align"in _&&t(19,re=_.text_align),"autofocus"in _&&t(20,_e=_.autofocus),"autoscroll"in _&&t(21,fe=_.autoscroll),"interactive"in _&&t(22,A=_.interactive),"root"in _&&t(23,ce=_.root),"file_count"in _&&t(24,me=_.file_count),"max_plain_text_length"in _&&t(25,H=_.max_plain_text_length),"sources"in _&&t(26,D=_.sources),"waveform_options"in _&&t(31,q=_.waveform_options),"html_attributes"in _&&t(27,B=_.html_attributes)},t(30,c={height:50,barWidth:2,barGap:3,cursorWidth:2,cursorColor:"#ddd5e9",autoplay:!1,barRadius:10,dragToSeek:!0,normalize:!0,minPxPerSec:20}),[r,N,i,o,n,m,a,d,f,p,h,E,u,b,k,Z,C,F,y,re,_e,fe,A,ce,me,H,D,B,T,V,c,q,ie,j,x,P,be,ge,Be,De,qe,Se,Te,Me,Ue,Ve,Ae,He,Fe,Ie,Ne,Pe]}class kl extends je{constructor(e){super(),Ge(this,e,rl,ul,Le,{gradio:2,elem_id:3,elem_classes:4,visible:5,value:0,file_types:6,lines:7,placeholder:8,label:9,info:10,show_label:11,max_lines:12,scale:13,min_width:14,submit_btn:15,stop_btn:16,loading_status:17,value_is_output:1,rtl:18,text_align:19,autofocus:20,autoscroll:21,interactive:22,root:23,file_count:24,max_plain_text_length:25,sources:26,waveform_options:31,html_attributes:27},null,[-1,-1])}get gradio(){return this.$$.ctx[2]}set gradio(e){this.$$set({gradio:e}),U()}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),U()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),U()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),U()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),U()}get file_types(){return this.$$.ctx[6]}set file_types(e){this.$$set({file_types:e}),U()}get lines(){return this.$$.ctx[7]}set lines(e){this.$$set({lines:e}),U()}get placeholder(){return this.$$.ctx[8]}set placeholder(e){this.$$set({placeholder:e}),U()}get label(){return this.$$.ctx[9]}set label(e){this.$$set({label:e}),U()}get info(){return this.$$.ctx[10]}set info(e){this.$$set({info:e}),U()}get show_label(){return this.$$.ctx[11]}set show_label(e){this.$$set({show_label:e}),U()}get max_lines(){return this.$$.ctx[12]}set max_lines(e){this.$$set({max_lines:e}),U()}get scale(){return this.$$.ctx[13]}set scale(e){this.$$set({scale:e}),U()}get min_width(){return this.$$.ctx[14]}set min_width(e){this.$$set({min_width:e}),U()}get submit_btn(){return this.$$.ctx[15]}set submit_btn(e){this.$$set({submit_btn:e}),U()}get stop_btn(){return this.$$.ctx[16]}set stop_btn(e){this.$$set({stop_btn:e}),U()}get loading_status(){return this.$$.ctx[17]}set loading_status(e){this.$$set({loading_status:e}),U()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),U()}get rtl(){return this.$$.ctx[18]}set rtl(e){this.$$set({rtl:e}),U()}get text_align(){return this.$$.ctx[19]}set text_align(e){this.$$set({text_align:e}),U()}get autofocus(){return this.$$.ctx[20]}set autofocus(e){this.$$set({autofocus:e}),U()}get autoscroll(){return this.$$.ctx[21]}set autoscroll(e){this.$$set({autoscroll:e}),U()}get interactive(){return this.$$.ctx[22]}set interactive(e){this.$$set({interactive:e}),U()}get root(){return this.$$.ctx[23]}set root(e){this.$$set({root:e}),U()}get file_count(){return this.$$.ctx[24]}set file_count(e){this.$$set({file_count:e}),U()}get max_plain_text_length(){return this.$$.ctx[25]}set max_plain_text_length(e){this.$$set({max_plain_text_length:e}),U()}get sources(){return this.$$.ctx[26]}set sources(e){this.$$set({sources:e}),U()}get waveform_options(){return this.$$.ctx[31]}set waveform_options(e){this.$$set({waveform_options:e}),U()}get html_attributes(){return this.$$.ctx[27]}set html_attributes(e){this.$$set({html_attributes:e}),U()}}export{El as BaseExample,ol as BaseMultimodalTextbox,kl as default};
//# sourceMappingURL=Index.CnBkVKz_.js.map
