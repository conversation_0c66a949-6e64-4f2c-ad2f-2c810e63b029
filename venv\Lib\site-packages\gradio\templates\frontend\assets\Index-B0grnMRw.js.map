{"version": 3, "file": "Index-B0grnMRw.js", "sources": ["../../../../js/dataframe/shared/utils/sort_utils.ts", "../../../../js/dataframe/shared/utils/filter_utils.ts", "../../../../js/dataframe/shared/utils/table_utils.ts", "../../../../js/dataframe/shared/selection_utils.ts", "../../../../js/dataframe/shared/context/dataframe_context.ts", "../../../../js/dataframe/shared/icons/SelectionButtons.svelte", "../../../../js/dataframe/shared/BooleanCell.svelte", "../../../../js/dataframe/shared/EditableCell.svelte", "../../../../js/dataframe/shared/RowNumber.svelte", "../../../../js/dataframe/shared/CellMenuButton.svelte", "../../../../js/dataframe/shared/icons/Padlock.svelte", "../../../../js/dataframe/shared/icons/SortArrowUp.svelte", "../../../../js/dataframe/shared/icons/SortArrowDown.svelte", "../../../../js/dataframe/shared/CellMenuIcons.svelte", "../../../../js/dataframe/shared/TableHeader.svelte", "../../../../js/dataframe/shared/TableCell.svelte", "../../../../js/dataframe/shared/EmptyRowButton.svelte", "../../../../js/dataframe/shared/VirtualTable.svelte", "../../../../js/dataframe/shared/FilterMenu.svelte", "../../../../js/dataframe/shared/CellMenu.svelte", "../../../../js/dataframe/shared/Toolbar.svelte", "../../../../js/dataframe/shared/utils/data_processing.ts", "../../../../js/dataframe/shared/utils.ts", "../../../../js/dataframe/shared/utils/keyboard_utils.ts", "../../../../js/dataframe/shared/utils/drag_utils.ts", "../../../../js/dataframe/shared/Table.svelte", "../../../../js/dataframe/Index.svelte"], "sourcesContent": ["import type { Headers } from \"../types\";\nimport { sort_table_data } from \"./table_utils\";\n\nexport type SortDirection = \"asc\" | \"desc\";\n\nexport function get_sort_status(\n\tname: string,\n\tsort_columns: { col: number; direction: SortDirection }[],\n\theaders: Headers\n): \"none\" | \"asc\" | \"desc\" {\n\tif (!sort_columns.length) return \"none\";\n\n\tconst sort_item = sort_columns.find((item) => {\n\t\tconst col = item.col;\n\t\tif (col < 0 || col >= headers.length) return false;\n\t\treturn headers[col] === name;\n\t});\n\n\tif (!sort_item) return \"none\";\n\treturn sort_item.direction;\n}\n\nexport function sort_data(\n\tdata: { id: string; value: string | number }[][],\n\tsort_columns: { col: number; direction: SortDirection }[]\n): number[] {\n\tif (!data || !data.length || !data[0]) {\n\t\treturn [];\n\t}\n\n\tif (sort_columns.length > 0) {\n\t\tconst row_indices = [...Array(data.length)].map((_, i) => i);\n\t\trow_indices.sort((row_a_idx, row_b_idx) => {\n\t\t\tconst row_a = data[row_a_idx];\n\t\t\tconst row_b = data[row_b_idx];\n\n\t\t\tfor (const { col: sort_by, direction } of sort_columns) {\n\t\t\t\tif (\n\t\t\t\t\t!row_a ||\n\t\t\t\t\t!row_b ||\n\t\t\t\t\tsort_by < 0 ||\n\t\t\t\t\tsort_by >= row_a.length ||\n\t\t\t\t\tsort_by >= row_b.length ||\n\t\t\t\t\t!row_a[sort_by] ||\n\t\t\t\t\t!row_b[sort_by]\n\t\t\t\t) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tconst val_a = row_a[sort_by].value;\n\t\t\t\tconst val_b = row_b[sort_by].value;\n\t\t\t\tconst comparison = val_a < val_b ? -1 : val_a > val_b ? 1 : 0;\n\n\t\t\t\tif (comparison !== 0) {\n\t\t\t\t\treturn direction === \"asc\" ? comparison : -comparison;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn 0;\n\t\t});\n\t\treturn row_indices;\n\t}\n\treturn [...Array(data.length)].map((_, i) => i);\n}\n\nexport function sort_data_and_preserve_selection(\n\tdata: { id: string; value: string | number }[][],\n\tdisplay_value: string[][] | null,\n\tstyling: string[][] | null,\n\tsort_columns: { col: number; direction: SortDirection }[],\n\tselected: [number, number] | false,\n\tget_current_indices: (\n\t\tid: string,\n\t\tdata: { id: string; value: string | number }[][]\n\t) => [number, number]\n): { data: typeof data; selected: [number, number] | false } {\n\tlet id = null;\n\tif (selected && selected[0] in data && selected[1] in data[selected[0]]) {\n\t\tid = data[selected[0]][selected[1]].id;\n\t}\n\n\tsort_table_data(data, display_value, styling, sort_columns);\n\n\tlet new_selected = selected;\n\tif (id) {\n\t\tconst [i, j] = get_current_indices(id, data);\n\t\tnew_selected = [i, j];\n\t}\n\n\treturn { data, selected: new_selected };\n}\n", "import { filter_table_data } from \"./table_utils\";\n\nexport type FilterDatatype = \"string\" | \"number\";\n\nexport function filter_data(\n\tdata: { id: string; value: string | number }[][],\n\tfilter_columns: {\n\t\tcol: number;\n\t\tdatatype: FilterDatatype;\n\t\tfilter: string;\n\t\tvalue: string;\n\t}[]\n): number[] {\n\tif (!data || !data.length || !data[0]) {\n\t\treturn [];\n\t}\n\tlet row_indices = [...Array(data.length)].map((_, i) => i);\n\n\tif (filter_columns.length > 0) {\n\t\tfilter_columns.forEach((column) => {\n\t\t\tif (column.datatype === \"string\") {\n\t\t\t\tswitch (column.filter) {\n\t\t\t\t\tcase \"Contains\":\n\t\t\t\t\t\trow_indices = row_indices.filter((i) =>\n\t\t\t\t\t\t\tdata[i][column.col]?.value.toString().includes(column.value)\n\t\t\t\t\t\t);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"Does not contain\":\n\t\t\t\t\t\trow_indices = row_indices.filter(\n\t\t\t\t\t\t\t(i) =>\n\t\t\t\t\t\t\t\t!data[i][column.col]?.value.toString().includes(column.value)\n\t\t\t\t\t\t);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"Starts with\":\n\t\t\t\t\t\trow_indices = row_indices.filter((i) =>\n\t\t\t\t\t\t\tdata[i][column.col]?.value.toString().startsWith(column.value)\n\t\t\t\t\t\t);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"Ends with\":\n\t\t\t\t\t\trow_indices = row_indices.filter((i) =>\n\t\t\t\t\t\t\tdata[i][column.col]?.value.toString().endsWith(column.value)\n\t\t\t\t\t\t);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"Is\":\n\t\t\t\t\t\trow_indices = row_indices.filter(\n\t\t\t\t\t\t\t(i) => data[i][column.col]?.value.toString() === column.value\n\t\t\t\t\t\t);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"Is not\":\n\t\t\t\t\t\trow_indices = row_indices.filter(\n\t\t\t\t\t\t\t(i) => !(data[i][column.col]?.value.toString() === column.value)\n\t\t\t\t\t\t);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"Is empty\":\n\t\t\t\t\t\trow_indices = row_indices.filter(\n\t\t\t\t\t\t\t(i) => data[i][column.col]?.value.toString() === \"\"\n\t\t\t\t\t\t);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"Is not empty\":\n\t\t\t\t\t\trow_indices = row_indices.filter(\n\t\t\t\t\t\t\t(i) => !(data[i][column.col]?.value.toString() === \"\")\n\t\t\t\t\t\t);\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t} else if (column.datatype === \"number\") {\n\t\t\t\tswitch (column.filter) {\n\t\t\t\t\tcase \"=\":\n\t\t\t\t\t\trow_indices = row_indices.filter((i) => {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t!isNaN(Number(data[i][column.col]?.value)) &&\n\t\t\t\t\t\t\t\t!isNaN(Number(column.value))\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\t\tNumber(data[i][column.col]?.value) === Number(column.value)\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"≠\":\n\t\t\t\t\t\trow_indices = row_indices.filter((i) => {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t!isNaN(Number(data[i][column.col]?.value)) &&\n\t\t\t\t\t\t\t\t!isNaN(Number(column.value))\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\treturn !(\n\t\t\t\t\t\t\t\t\tNumber(data[i][column.col]?.value) === Number(column.value)\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \">\":\n\t\t\t\t\t\trow_indices = row_indices.filter((i) => {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t!isNaN(Number(data[i][column.col]?.value)) &&\n\t\t\t\t\t\t\t\t!isNaN(Number(column.value))\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\t\tNumber(data[i][column.col]?.value) > Number(column.value)\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"<\":\n\t\t\t\t\t\trow_indices = row_indices.filter((i) => {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t!isNaN(Number(data[i][column.col]?.value)) &&\n\t\t\t\t\t\t\t\t!isNaN(Number(column.value))\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\t\tNumber(data[i][column.col]?.value) < Number(column.value)\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"≥\":\n\t\t\t\t\t\trow_indices = row_indices.filter((i) => {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t!isNaN(Number(data[i][column.col]?.value)) &&\n\t\t\t\t\t\t\t\t!isNaN(Number(column.value))\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\t\tNumber(data[i][column.col]?.value) >= Number(column.value)\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"≤\":\n\t\t\t\t\t\trow_indices = row_indices.filter((i) => {\n\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t!isNaN(Number(data[i][column.col]?.value)) &&\n\t\t\t\t\t\t\t\t!isNaN(Number(column.value))\n\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\t\tNumber(data[i][column.col]?.value) <= Number(column.value)\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"Is empty\":\n\t\t\t\t\t\trow_indices = row_indices.filter(\n\t\t\t\t\t\t\t(i) => data[i][column.col]?.value.toString() === \"\"\n\t\t\t\t\t\t);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"Is not empty\":\n\t\t\t\t\t\trow_indices = row_indices.filter((i) => {\n\t\t\t\t\t\t\tif (!isNaN(Number(data[i][column.col]?.value))) {\n\t\t\t\t\t\t\t\treturn !(data[i][column.col]?.value.toString() === \"\");\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t\treturn row_indices;\n\t}\n\treturn [...Array(data.length)].map((_, i) => i);\n}\n\nexport function filter_data_and_preserve_selection(\n\tdata: { id: string; value: string | number }[][],\n\tdisplay_value: string[][] | null,\n\tstyling: string[][] | null,\n\tfilter_columns: {\n\t\tcol: number;\n\t\tdatatype: FilterDatatype;\n\t\tfilter: string;\n\t\tvalue: string;\n\t}[],\n\tselected: [number, number] | false,\n\tget_current_indices: (\n\t\tid: string,\n\t\tdata: { id: string; value: string | number }[][]\n\t) => [number, number],\n\toriginal_data?: { id: string; value: string | number }[][],\n\toriginal_display_value?: string[][] | null,\n\toriginal_styling?: string[][] | null\n): { data: typeof data; selected: [number, number] | false } {\n\tlet id = null;\n\tif (selected && selected[0] in data && selected[1] in data[selected[0]]) {\n\t\tid = data[selected[0]][selected[1]].id;\n\t}\n\n\tfilter_table_data(\n\t\tdata,\n\t\tdisplay_value,\n\t\tstyling,\n\t\tfilter_columns,\n\t\toriginal_data,\n\t\toriginal_display_value,\n\t\toriginal_styling\n\t);\n\n\tlet new_selected = selected;\n\tif (id) {\n\t\tconst [i, j] = get_current_indices(id, data);\n\t\tnew_selected = [i, j];\n\t}\n\n\treturn { data, selected: new_selected };\n}\n", "import type { <PERSON><PERSON>, <PERSON>ersWithIDs, TableCell, TableData } from \"../types\";\nimport { sort_data } from \"./sort_utils\";\nimport { filter_data } from \"./filter_utils\";\nimport type { SortDirection } from \"./sort_utils\";\nimport type { FilterDatatype } from \"./filter_utils\";\nimport { dsvFormat } from \"d3-dsv\";\n\nexport function make_cell_id(row: number, col: number): string {\n\treturn `cell-${row}-${col}`;\n}\n\nexport function make_header_id(col: number): string {\n\treturn `header-${col}`;\n}\n\nexport function get_max(data: TableData): TableCell[] {\n\tif (!data || !data.length) return [];\n\tlet max = data[0].slice();\n\tfor (let i = 0; i < data.length; i++) {\n\t\tfor (let j = 0; j < data[i].length; j++) {\n\t\t\tif (`${max[j].value}`.length < `${data[i][j].value}`.length) {\n\t\t\t\tmax[j] = data[i][j];\n\t\t\t}\n\t\t}\n\t}\n\treturn max;\n}\n\nexport function sort_table_data(\n\tdata: TableData,\n\tdisplay_value: string[][] | null,\n\tstyling: string[][] | null,\n\tsort_columns: { col: number; direction: SortDirection }[]\n): void {\n\tif (!sort_columns.length) return;\n\tif (!data || !data.length) return;\n\n\tconst indices = sort_data(data, sort_columns);\n\n\tconst new_data = indices.map((i: number) => data[i]);\n\tdata.splice(0, data.length, ...new_data);\n\n\tif (display_value) {\n\t\tconst new_display = indices.map((i: number) => display_value[i]);\n\t\tdisplay_value.splice(0, display_value.length, ...new_display);\n\t}\n\n\tif (styling) {\n\t\tconst new_styling = indices.map((i: number) => styling[i]);\n\t\tstyling.splice(0, styling.length, ...new_styling);\n\t}\n}\n\nexport function filter_table_data(\n\tdata: TableData,\n\tdisplay_value: string[][] | null,\n\tstyling: string[][] | null,\n\tfilter_columns: {\n\t\tcol: number;\n\t\tdatatype: FilterDatatype;\n\t\tfilter: string;\n\t\tvalue: string;\n\t}[],\n\toriginal_data?: TableData,\n\toriginal_display_value?: string[][] | null,\n\toriginal_styling?: string[][] | null\n): void {\n\tconst base_data = original_data ?? data;\n\tconst base_display_value = original_display_value ?? display_value;\n\tconst base_styling = original_styling ?? styling;\n\n\tif (!filter_columns.length) {\n\t\tdata.splice(0, data.length, ...base_data.map((row) => [...row]));\n\t\tif (display_value && base_display_value) {\n\t\t\tdisplay_value.splice(\n\t\t\t\t0,\n\t\t\t\tdisplay_value.length,\n\t\t\t\t...base_display_value.map((row) => [...row])\n\t\t\t);\n\t\t}\n\t\tif (styling && base_styling) {\n\t\t\tstyling.splice(0, styling.length, ...base_styling.map((row) => [...row]));\n\t\t}\n\t\treturn;\n\t}\n\tif (!data || !data.length) return;\n\n\tconst indices = filter_data(base_data, filter_columns);\n\n\tconst new_data = indices.map((i: number) => base_data[i]);\n\tdata.splice(0, data.length, ...new_data);\n\n\tif (display_value && base_display_value) {\n\t\tconst new_display = indices.map((i: number) => base_display_value[i]);\n\t\tdisplay_value.splice(0, display_value.length, ...new_display);\n\t}\n\n\tif (styling && base_styling) {\n\t\tconst new_styling = indices.map((i: number) => base_styling[i]);\n\t\tstyling.splice(0, styling.length, ...new_styling);\n\t}\n}\n\nexport async function copy_table_data(\n\tdata: TableData,\n\tselected_cells: [number, number][] | null\n): Promise<void> {\n\tif (!data || !data.length) return;\n\n\tconst cells_to_copy =\n\t\tselected_cells ||\n\t\tdata.flatMap((row, r) => row.map((_, c) => [r, c] as [number, number]));\n\n\tconst csv = cells_to_copy.reduce(\n\t\t(acc: { [key: string]: { [key: string]: string } }, [row, col]) => {\n\t\t\tacc[row] = acc[row] || {};\n\t\t\tconst value = String(data[row][col].value);\n\t\t\tacc[row][col] =\n\t\t\t\tvalue.includes(\",\") || value.includes('\"') || value.includes(\"\\n\")\n\t\t\t\t\t? `\"${value.replace(/\"/g, '\"\"')}\"`\n\t\t\t\t\t: value;\n\t\t\treturn acc;\n\t\t},\n\t\t{}\n\t);\n\n\tconst rows = Object.keys(csv).sort((a, b) => +a - +b);\n\tif (!rows.length) return;\n\n\tconst cols = Object.keys(csv[rows[0]]).sort((a, b) => +a - +b);\n\tconst text = rows\n\t\t.map((r) => cols.map((c) => csv[r][c] || \"\").join(\",\"))\n\t\t.join(\"\\n\");\n\n\ttry {\n\t\tawait navigator.clipboard.writeText(text);\n\t} catch (err) {\n\t\tthrow new Error(\"Failed to copy to clipboard: \" + (err as Error).message);\n\t}\n}\n\n// File Import/Export\nexport function guess_delimiter(\n\ttext: string,\n\tpossibleDelimiters: string[]\n): string[] {\n\treturn possibleDelimiters.filter(weedOut);\n\n\tfunction weedOut(delimiter: string): boolean {\n\t\tvar cache = -1;\n\t\treturn text.split(\"\\n\").every(checkLength);\n\n\t\tfunction checkLength(line: string): boolean {\n\t\t\tif (!line) return true;\n\t\t\tvar length = line.split(delimiter).length;\n\t\t\tif (cache < 0) cache = length;\n\t\t\treturn cache === length && length > 1;\n\t\t}\n\t}\n}\n\nexport function data_uri_to_blob(data_uri: string): Blob {\n\tconst byte_str = atob(data_uri.split(\",\")[1]);\n\tconst mime_str = data_uri.split(\",\")[0].split(\":\")[1].split(\";\")[0];\n\tconst ab = new ArrayBuffer(byte_str.length);\n\tconst ia = new Uint8Array(ab);\n\tfor (let i = 0; i < byte_str.length; i++) {\n\t\tia[i] = byte_str.charCodeAt(i);\n\t}\n\treturn new Blob([ab], { type: mime_str });\n}\n\nexport function handle_file_upload(\n\tdata_uri: string,\n\tupdate_headers: (headers: Headers) => HeadersWithIDs[],\n\tupdate_values: (values: (string | number)[][]) => void\n): void {\n\tconst blob = data_uri_to_blob(data_uri);\n\tconst reader = new FileReader();\n\treader.addEventListener(\"loadend\", (e) => {\n\t\tif (!e?.target?.result || typeof e.target.result !== \"string\") return;\n\t\tconst [delimiter] = guess_delimiter(e.target.result, [\",\", \"\\t\"]);\n\t\tconst [head, ...rest] = dsvFormat(delimiter).parseRows(e.target.result);\n\t\tupdate_headers(head);\n\t\tupdate_values(rest);\n\t});\n\treader.readAsText(blob);\n}\n", "import type { CellCoordinate } from \"./types\";\n\nexport type CellData = { id: string; value: string | number };\n\nexport function is_cell_in_selection(\n\tcoords: [number, number],\n\tselected_cells: [number, number][]\n): boolean {\n\tconst [row, col] = coords;\n\treturn selected_cells.some(([r, c]) => r === row && c === col);\n}\n\nexport function is_cell_selected(\n\tcell: CellCoordinate,\n\tselected_cells: CellCoordinate[]\n): string {\n\tconst [row, col] = cell;\n\tif (!selected_cells.some(([r, c]) => r === row && c === col)) return \"\";\n\n\tconst up = selected_cells.some(([r, c]) => r === row - 1 && c === col);\n\tconst down = selected_cells.some(([r, c]) => r === row + 1 && c === col);\n\tconst left = selected_cells.some(([r, c]) => r === row && c === col - 1);\n\tconst right = selected_cells.some(([r, c]) => r === row && c === col + 1);\n\n\treturn `cell-selected${up ? \" no-top\" : \"\"}${down ? \" no-bottom\" : \"\"}${left ? \" no-left\" : \"\"}${right ? \" no-right\" : \"\"}`;\n}\n\nexport function get_range_selection(\n\tstart: CellCoordinate,\n\tend: CellCoordinate\n): CellCoordinate[] {\n\tconst [start_row, start_col] = start;\n\tconst [end_row, end_col] = end;\n\tconst min_row = Math.min(start_row, end_row);\n\tconst max_row = Math.max(start_row, end_row);\n\tconst min_col = Math.min(start_col, end_col);\n\tconst max_col = Math.max(start_col, end_col);\n\n\tconst cells: CellCoordinate[] = [];\n\t// Add the start cell as the \"anchor\" cell so that when\n\t// we press shift+arrow keys, the selection will always\n\t// include the anchor cell.\n\tcells.push(start);\n\n\tfor (let i = min_row; i <= max_row; i++) {\n\t\tfor (let j = min_col; j <= max_col; j++) {\n\t\t\tif (i === start_row && j === start_col) continue;\n\t\t\tcells.push([i, j]);\n\t\t}\n\t}\n\treturn cells;\n}\n\nexport function handle_selection(\n\tcurrent: CellCoordinate,\n\tselected_cells: CellCoordinate[],\n\tevent: { shiftKey: boolean; metaKey: boolean; ctrlKey: boolean }\n): CellCoordinate[] {\n\tif (event.shiftKey && selected_cells.length > 0) {\n\t\treturn get_range_selection(\n\t\t\tselected_cells[selected_cells.length - 1],\n\t\t\tcurrent\n\t\t);\n\t}\n\n\tif (event.metaKey || event.ctrlKey) {\n\t\tconst is_cell_match = ([r, c]: CellCoordinate): boolean =>\n\t\t\tr === current[0] && c === current[1];\n\t\tconst index = selected_cells.findIndex(is_cell_match);\n\t\treturn index === -1\n\t\t\t? [...selected_cells, current]\n\t\t\t: selected_cells.filter((_, i) => i !== index);\n\t}\n\n\treturn [current];\n}\n\nexport function handle_delete_key(\n\tdata: CellData[][],\n\tselected_cells: CellCoordinate[]\n): CellData[][] {\n\tconst new_data = data.map((row) => [...row]);\n\tselected_cells.forEach(([row, col]) => {\n\t\tif (new_data[row] && new_data[row][col]) {\n\t\t\tnew_data[row][col] = { ...new_data[row][col], value: \"\" };\n\t\t}\n\t});\n\treturn new_data;\n}\n\nexport function should_show_cell_menu(\n\tcell: CellCoordinate,\n\tselected_cells: CellCoordinate[],\n\teditable: boolean\n): boolean {\n\tconst [row, col] = cell;\n\treturn (\n\t\teditable &&\n\t\tselected_cells.length === 1 &&\n\t\tselected_cells[0][0] === row &&\n\t\tselected_cells[0][1] === col\n\t);\n}\n\nexport function get_next_cell_coordinates(\n\tcurrent: CellCoordinate,\n\tdata: CellData[][],\n\tshift_key: boolean\n): CellCoordinate | false {\n\tconst [row, col] = current;\n\tconst direction = shift_key ? -1 : 1;\n\n\tif (data[row]?.[col + direction]) {\n\t\treturn [row, col + direction];\n\t}\n\n\tconst next_row = row + (direction > 0 ? 1 : 0);\n\tconst prev_row = row + (direction < 0 ? -1 : 0);\n\n\tif (direction > 0 && data[next_row]?.[0]) {\n\t\treturn [next_row, 0];\n\t}\n\n\tif (direction < 0 && data[prev_row]?.[data[0].length - 1]) {\n\t\treturn [prev_row, data[0].length - 1];\n\t}\n\n\treturn false;\n}\n\nexport function move_cursor(\n\tevent: KeyboardEvent,\n\tcurrent_coords: CellCoordinate,\n\tdata: CellData[][]\n): CellCoordinate | false {\n\tconst key = event.key as \"ArrowRight\" | \"ArrowLeft\" | \"ArrowDown\" | \"ArrowUp\";\n\tconst dir = {\n\t\tArrowRight: [0, 1],\n\t\tArrowLeft: [0, -1],\n\t\tArrowDown: [1, 0],\n\t\tArrowUp: [-1, 0]\n\t}[key];\n\n\tlet i, j;\n\tif (event.metaKey || event.ctrlKey) {\n\t\tif (key === \"ArrowRight\") {\n\t\t\ti = current_coords[0];\n\t\t\tj = data[0].length - 1;\n\t\t} else if (key === \"ArrowLeft\") {\n\t\t\ti = current_coords[0];\n\t\t\tj = 0;\n\t\t} else if (key === \"ArrowDown\") {\n\t\t\ti = data.length - 1;\n\t\t\tj = current_coords[1];\n\t\t} else if (key === \"ArrowUp\") {\n\t\t\ti = 0;\n\t\t\tj = current_coords[1];\n\t\t} else {\n\t\t\treturn false;\n\t\t}\n\t} else {\n\t\ti = current_coords[0] + dir[0];\n\t\tj = current_coords[1] + dir[1];\n\t}\n\n\tif (i < 0 && j <= 0) {\n\t\treturn false;\n\t}\n\n\tconst is_data = data[i]?.[j];\n\tif (is_data) {\n\t\treturn [i, j];\n\t}\n\treturn false;\n}\n\nexport function get_current_indices(\n\tid: string,\n\tdata: CellData[][]\n): [number, number] {\n\treturn data.reduce(\n\t\t(acc, arr, i) => {\n\t\t\tconst j = arr.reduce(\n\t\t\t\t(_acc, _data, k) => (id === _data.id ? k : _acc),\n\t\t\t\t-1\n\t\t\t);\n\t\t\treturn j === -1 ? acc : [i, j];\n\t\t},\n\t\t[-1, -1]\n\t);\n}\n\nexport function handle_click_outside(\n\tevent: Event,\n\tparent: HTMLElement\n): boolean {\n\tconst [trigger] = event.composedPath() as HTMLElement[];\n\treturn !parent.contains(trigger);\n}\n\nexport function select_column(data: any[][], col: number): CellCoordinate[] {\n\treturn Array.from({ length: data.length }, (_, i) => [i, col]);\n}\n\nexport function select_row(data: any[][], row: number): CellCoordinate[] {\n\treturn Array.from({ length: data[0].length }, (_, i) => [row, i]);\n}\n\nexport function calculate_selection_positions(\n\tselected: CellCoordinate,\n\tdata: { id: string; value: string | number }[][],\n\tels: Record<string, { cell: HTMLTableCellElement | null }>,\n\tparent: HTMLElement,\n\ttable: HTMLElement\n): { col_pos: string; row_pos: string | undefined } {\n\tconst [row, col] = selected;\n\tif (!data[row]?.[col]) {\n\t\treturn { col_pos: \"0px\", row_pos: undefined };\n\t}\n\n\tconst cell_id = data[row][col].id;\n\tconst cell_el = els[cell_id]?.cell;\n\n\tif (!cell_el) {\n\t\treturn { col_pos: \"0px\", row_pos: undefined };\n\t}\n\n\tconst cell_rect = cell_el.getBoundingClientRect();\n\tconst table_rect = table.getBoundingClientRect();\n\tconst col_pos = `${cell_rect.left - table_rect.left + cell_rect.width / 2}px`;\n\tconst row_pos = `${cell_rect.top - table_rect.top + cell_rect.height / 2}px`;\n\n\treturn { col_pos, row_pos };\n}\n", "import { getContext, setContext } from \"svelte\";\nimport { dequal } from \"dequal\";\nimport { writable, get } from \"svelte/store\";\nimport { sort_table_data } from \"../utils/table_utils\";\nimport { tick } from \"svelte\";\nimport {\n\thandle_selection,\n\tget_next_cell_coordinates,\n\tget_range_selection,\n\tmove_cursor\n} from \"../selection_utils\";\n\nexport const DATAFRAME_KEY = Symbol(\"dataframe\");\n\nexport type SortDirection = \"asc\" | \"desc\";\nexport type FilterDatatype = \"string\" | \"number\";\nexport type CellCoordinate = [number, number];\n\ninterface DataFrameState {\n\tconfig: {\n\t\tshow_fullscreen_button: boolean;\n\t\tshow_copy_button: boolean;\n\t\tshow_search: \"none\" | \"search\" | \"filter\";\n\t\tshow_row_numbers: boolean;\n\t\teditable: boolean;\n\t\tpinned_columns: number;\n\t\tshow_label: boolean;\n\t\tline_breaks: boolean;\n\t\twrap: boolean;\n\t\tmax_height: number;\n\t\tcolumn_widths: string[];\n\t\tmax_chars?: number;\n\t\tstatic_columns?: (string | number)[];\n\t};\n\tcurrent_search_query: string | null;\n\tsort_state: {\n\t\tsort_columns: { col: number; direction: SortDirection }[];\n\t\trow_order: number[];\n\t\tinitial_data: {\n\t\t\tdata: { id: string; value: string | number }[][];\n\t\t\tdisplay_value: string[][] | null;\n\t\t\tstyling: string[][] | null;\n\t\t} | null;\n\t};\n\tfilter_state: {\n\t\tfilter_columns: {\n\t\t\tcol: number;\n\t\t\tdatatype: FilterDatatype;\n\t\t\tfilter: string;\n\t\t\tvalue: string;\n\t\t}[];\n\t\tinitial_data: {\n\t\t\tdata: { id: string; value: string | number }[][];\n\t\t\tdisplay_value: string[][] | null;\n\t\t\tstyling: string[][] | null;\n\t\t} | null;\n\t};\n\tui_state: {\n\t\tactive_cell_menu: { row: number; col: number; x: number; y: number } | null;\n\t\tactive_header_menu: { col: number; x: number; y: number } | null;\n\t\tselected_cells: CellCoordinate[];\n\t\tselected: CellCoordinate | false;\n\t\tediting: CellCoordinate | false;\n\t\theader_edit: number | false;\n\t\tselected_header: number | false;\n\t\tactive_button: {\n\t\t\ttype: \"header\" | \"cell\";\n\t\t\trow?: number;\n\t\t\tcol: number;\n\t\t} | null;\n\t\tcopy_flash: boolean;\n\t};\n}\n\ninterface DataFrameActions {\n\thandle_search: (query: string | null) => void;\n\thandle_sort: (col: number, direction: SortDirection) => void;\n\thandle_filter: (\n\t\tcol: number,\n\t\tdatatype: FilterDatatype,\n\t\tfilter: string,\n\t\tvalue: string\n\t) => void;\n\tget_sort_status: (name: string, headers: string[]) => \"none\" | \"asc\" | \"desc\";\n\tsort_data: (\n\t\tdata: any[][],\n\t\tdisplay_value: string[][] | null,\n\t\tstyling: string[][] | null\n\t) => void;\n\tupdate_row_order: (data: any[][]) => void;\n\tfilter_data: (data: any[][]) => any[][];\n\tadd_row: (data: any[][], make_id: () => string, index?: number) => any[][];\n\tadd_col: (\n\t\tdata: any[][],\n\t\theaders: string[],\n\t\tmake_id: () => string,\n\t\tindex?: number\n\t) => { data: any[][]; headers: string[] };\n\tadd_row_at: (\n\t\tdata: any[][],\n\t\tindex: number,\n\t\tposition: \"above\" | \"below\",\n\t\tmake_id: () => string\n\t) => any[][];\n\tadd_col_at: (\n\t\tdata: any[][],\n\t\theaders: string[],\n\t\tindex: number,\n\t\tposition: \"left\" | \"right\",\n\t\tmake_id: () => string\n\t) => { data: any[][]; headers: string[] };\n\tdelete_row: (data: any[][], index: number) => any[][];\n\tdelete_col: (\n\t\tdata: any[][],\n\t\theaders: string[],\n\t\tindex: number\n\t) => { data: any[][]; headers: string[] };\n\tdelete_row_at: (data: any[][], index: number) => any[][];\n\tdelete_col_at: (\n\t\tdata: any[][],\n\t\theaders: string[],\n\t\tindex: number\n\t) => { data: any[][]; headers: string[] };\n\ttrigger_change: (\n\t\tdata: any[][],\n\t\theaders: any[],\n\t\tprevious_data: string[][],\n\t\tprevious_headers: string[],\n\t\tvalue_is_output: boolean,\n\t\tdispatch: (e: \"change\" | \"input\", detail?: any) => void\n\t) => Promise<void>;\n\treset_sort_state: () => void;\n\treset_filter_state: () => void;\n\tset_active_cell_menu: (\n\t\tmenu: { row: number; col: number; x: number; y: number } | null\n\t) => void;\n\tset_active_header_menu: (\n\t\tmenu: { col: number; x: number; y: number } | null\n\t) => void;\n\tset_selected_cells: (cells: CellCoordinate[]) => void;\n\tset_selected: (selected: CellCoordinate | false) => void;\n\tset_editing: (editing: CellCoordinate | false) => void;\n\tclear_ui_state: () => void;\n\tset_header_edit: (header_index: number | false) => void;\n\tset_selected_header: (header_index: number | false) => void;\n\thandle_header_click: (col: number, editable: boolean) => void;\n\tend_header_edit: (key: string) => void;\n\tget_selected_cells: () => CellCoordinate[];\n\tget_active_cell_menu: () => {\n\t\trow: number;\n\t\tcol: number;\n\t\tx: number;\n\t\ty: number;\n\t} | null;\n\tget_active_button: () => {\n\t\ttype: \"header\" | \"cell\";\n\t\trow?: number;\n\t\tcol: number;\n\t} | null;\n\tset_active_button: (\n\t\tbutton: { type: \"header\" | \"cell\"; row?: number; col: number } | null\n\t) => void;\n\tset_copy_flash: (value: boolean) => void;\n\thandle_cell_click: (event: MouseEvent, row: number, col: number) => void;\n\ttoggle_cell_menu: (event: MouseEvent, row: number, col: number) => void;\n\ttoggle_cell_button: (row: number, col: number) => void;\n\thandle_select_column: (col: number) => void;\n\thandle_select_row: (row: number) => void;\n\tget_next_cell_coordinates: typeof get_next_cell_coordinates;\n\tget_range_selection: typeof get_range_selection;\n\tmove_cursor: typeof move_cursor;\n}\n\nexport interface DataFrameContext {\n\tstate: ReturnType<typeof writable<DataFrameState>>;\n\tactions: DataFrameActions;\n\tdata?: any[][];\n\theaders?: { id: string; value: string }[];\n\tdisplay_value?: string[][] | null;\n\tstyling?: string[][] | null;\n\tels?: Record<\n\t\tstring,\n\t\t{ cell: HTMLTableCellElement | null; input: HTMLTextAreaElement | null }\n\t>;\n\tparent_element?: HTMLElement;\n\tget_data_at?: (row: number, col: number) => string | number;\n\tget_column?: (col: number) => (string | number)[];\n\tget_row?: (row: number) => (string | number)[];\n\tdispatch?: (e: \"change\" | \"select\" | \"search\", detail?: any) => void;\n}\n\nfunction create_actions(\n\tstate: ReturnType<typeof writable<DataFrameState>>,\n\tcontext: DataFrameContext\n): DataFrameActions {\n\tconst update_state = (\n\t\tupdater: (s: DataFrameState) => Partial<DataFrameState>\n\t): void => state.update((s) => ({ ...s, ...updater(s) }));\n\n\tconst add_row = (\n\t\tdata: any[][],\n\t\tmake_id: () => string,\n\t\tindex?: number\n\t): any[][] => {\n\t\tconst new_row = data[0]?.length\n\t\t\t? Array(data[0].length)\n\t\t\t\t\t.fill(null)\n\t\t\t\t\t.map(() => ({ value: \"\", id: make_id() }))\n\t\t\t: [{ value: \"\", id: make_id() }];\n\t\tconst new_data = [...data];\n\t\tindex !== undefined\n\t\t\t? new_data.splice(index, 0, new_row)\n\t\t\t: new_data.push(new_row);\n\t\treturn new_data;\n\t};\n\n\tconst add_col = (\n\t\tdata: any[][],\n\t\theaders: string[],\n\t\tmake_id: () => string,\n\t\tindex?: number\n\t): { data: any[][]; headers: string[] } => {\n\t\tconst new_headers = context.headers\n\t\t\t? [...headers.map((h) => context.headers![headers.indexOf(h)].value)]\n\t\t\t: [...headers, `Header ${headers.length + 1}`];\n\t\tconst new_data = data.map((row) => [...row, { value: \"\", id: make_id() }]);\n\t\tif (index !== undefined) {\n\t\t\tnew_headers.splice(index, 0, new_headers.pop()!);\n\t\t\tnew_data.forEach((row) => row.splice(index, 0, row.pop()!));\n\t\t}\n\t\treturn { data: new_data, headers: new_headers };\n\t};\n\n\tconst update_array = (\n\t\tsource: { id: string; value: string | number }[][] | string[][] | null,\n\t\ttarget: any[] | null | undefined\n\t): void => {\n\t\tif (source && target) {\n\t\t\ttarget.splice(0, target.length, ...JSON.parse(JSON.stringify(source)));\n\t\t}\n\t};\n\n\treturn {\n\t\thandle_search: (query) =>\n\t\t\tupdate_state((s) => ({ current_search_query: query })),\n\t\thandle_sort: (col, direction) =>\n\t\t\tupdate_state((s) => {\n\t\t\t\tconst sort_cols = s.sort_state.sort_columns.filter(\n\t\t\t\t\t(c) => c.col !== col\n\t\t\t\t);\n\t\t\t\tif (\n\t\t\t\t\t!s.sort_state.sort_columns.some(\n\t\t\t\t\t\t(c) => c.col === col && c.direction === direction\n\t\t\t\t\t)\n\t\t\t\t) {\n\t\t\t\t\tsort_cols.push({ col, direction });\n\t\t\t\t}\n\n\t\t\t\tconst initial_data =\n\t\t\t\t\ts.sort_state.initial_data ||\n\t\t\t\t\t(context.data && sort_cols.length > 0\n\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\tdata: JSON.parse(JSON.stringify(context.data)),\n\t\t\t\t\t\t\t\tdisplay_value: context.display_value\n\t\t\t\t\t\t\t\t\t? JSON.parse(JSON.stringify(context.display_value))\n\t\t\t\t\t\t\t\t\t: null,\n\t\t\t\t\t\t\t\tstyling: context.styling\n\t\t\t\t\t\t\t\t\t? JSON.parse(JSON.stringify(context.styling))\n\t\t\t\t\t\t\t\t\t: null\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t: null);\n\n\t\t\t\treturn {\n\t\t\t\t\tsort_state: {\n\t\t\t\t\t\t...s.sort_state,\n\t\t\t\t\t\tsort_columns: sort_cols.slice(-3),\n\t\t\t\t\t\tinitial_data: initial_data\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}),\n\t\thandle_filter: (col, datatype, filter, value) =>\n\t\t\tupdate_state((s) => {\n\t\t\t\tconst filter_cols = s.filter_state.filter_columns.some(\n\t\t\t\t\t(c) => c.col === col\n\t\t\t\t)\n\t\t\t\t\t? s.filter_state.filter_columns.filter((c) => c.col !== col)\n\t\t\t\t\t: [\n\t\t\t\t\t\t\t...s.filter_state.filter_columns,\n\t\t\t\t\t\t\t{ col, datatype, filter, value }\n\t\t\t\t\t\t];\n\n\t\t\t\tconst initial_data =\n\t\t\t\t\ts.filter_state.initial_data ||\n\t\t\t\t\t(context.data && filter_cols.length > 0\n\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\tdata: JSON.parse(JSON.stringify(context.data)),\n\t\t\t\t\t\t\t\tdisplay_value: context.display_value\n\t\t\t\t\t\t\t\t\t? JSON.parse(JSON.stringify(context.display_value))\n\t\t\t\t\t\t\t\t\t: null,\n\t\t\t\t\t\t\t\tstyling: context.styling\n\t\t\t\t\t\t\t\t\t? JSON.parse(JSON.stringify(context.styling))\n\t\t\t\t\t\t\t\t\t: null\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t: null);\n\n\t\t\t\treturn {\n\t\t\t\t\tfilter_state: {\n\t\t\t\t\t\t...s.filter_state,\n\t\t\t\t\t\tfilter_columns: filter_cols,\n\t\t\t\t\t\tinitial_data: initial_data\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}),\n\t\tget_sort_status: (name, headers) => {\n\t\t\tconst s = get(state);\n\t\t\tconst sort_item = s.sort_state.sort_columns.find(\n\t\t\t\t(item) => headers[item.col] === name\n\t\t\t);\n\t\t\treturn sort_item ? sort_item.direction : \"none\";\n\t\t},\n\t\tsort_data: (data, display_value, styling) => {\n\t\t\tconst {\n\t\t\t\tsort_state: { sort_columns }\n\t\t\t} = get(state);\n\t\t\tif (sort_columns.length)\n\t\t\t\tsort_table_data(data, display_value, styling, sort_columns);\n\t\t},\n\t\tupdate_row_order: (data) =>\n\t\t\tupdate_state((s) => ({\n\t\t\t\tsort_state: {\n\t\t\t\t\t...s.sort_state,\n\t\t\t\t\trow_order:\n\t\t\t\t\t\ts.sort_state.sort_columns.length && data[0]\n\t\t\t\t\t\t\t? [...Array(data.length)]\n\t\t\t\t\t\t\t\t\t.map((_, i) => i)\n\t\t\t\t\t\t\t\t\t.sort((a, b) => {\n\t\t\t\t\t\t\t\t\t\tfor (const { col, direction } of s.sort_state\n\t\t\t\t\t\t\t\t\t\t\t.sort_columns) {\n\t\t\t\t\t\t\t\t\t\t\tconst comp =\n\t\t\t\t\t\t\t\t\t\t\t\t(data[a]?.[col]?.value ?? \"\") <\n\t\t\t\t\t\t\t\t\t\t\t\t(data[b]?.[col]?.value ?? \"\")\n\t\t\t\t\t\t\t\t\t\t\t\t\t? -1\n\t\t\t\t\t\t\t\t\t\t\t\t\t: 1;\n\t\t\t\t\t\t\t\t\t\t\tif (comp) return direction === \"asc\" ? comp : -comp;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\treturn 0;\n\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t: [...Array(data.length)].map((_, i) => i)\n\t\t\t\t}\n\t\t\t})),\n\t\tfilter_data: (data) => {\n\t\t\tconst query = get(state).current_search_query?.toLowerCase();\n\t\t\treturn query\n\t\t\t\t? data.filter((row) =>\n\t\t\t\t\t\trow.some((cell) =>\n\t\t\t\t\t\t\tString(cell?.value).toLowerCase().includes(query)\n\t\t\t\t\t\t)\n\t\t\t\t\t)\n\t\t\t\t: data;\n\t\t},\n\t\tadd_row,\n\t\tadd_col,\n\t\tadd_row_at: (data, index, position, make_id) =>\n\t\t\tadd_row(data, make_id, position === \"above\" ? index : index + 1),\n\t\tadd_col_at: (data, headers, index, position, make_id) =>\n\t\t\tadd_col(data, headers, make_id, position === \"left\" ? index : index + 1),\n\t\tdelete_row: (data, index) =>\n\t\t\tdata.length > 1 ? data.filter((_, i) => i !== index) : data,\n\t\tdelete_col: (data, headers, index) =>\n\t\t\theaders.length > 1\n\t\t\t\t? {\n\t\t\t\t\t\tdata: data.map((row) => row.filter((_, i) => i !== index)),\n\t\t\t\t\t\theaders: headers.filter((_, i) => i !== index)\n\t\t\t\t\t}\n\t\t\t\t: { data, headers },\n\t\tdelete_row_at: (data, index) =>\n\t\t\tdata.length > 1\n\t\t\t\t? [...data.slice(0, index), ...data.slice(index + 1)]\n\t\t\t\t: data,\n\t\tdelete_col_at: (data, headers, index) =>\n\t\t\theaders.length > 1\n\t\t\t\t? {\n\t\t\t\t\t\tdata: data.map((row) => [\n\t\t\t\t\t\t\t...row.slice(0, index),\n\t\t\t\t\t\t\t...row.slice(index + 1)\n\t\t\t\t\t\t]),\n\t\t\t\t\t\theaders: [...headers.slice(0, index), ...headers.slice(index + 1)]\n\t\t\t\t\t}\n\t\t\t\t: { data, headers },\n\t\ttrigger_change: async (\n\t\t\tdata,\n\t\t\theaders,\n\t\t\tprevious_data,\n\t\t\tprevious_headers,\n\t\t\tvalue_is_output,\n\t\t\tdispatch\n\t\t) => {\n\t\t\tconst s = get(state);\n\t\t\tif (s.current_search_query) return;\n\n\t\t\tconst current_headers = headers.map((h) => h.value);\n\t\t\tconst current_data = data.map((row) =>\n\t\t\t\trow.map((cell) => String(cell.value))\n\t\t\t);\n\n\t\t\tif (\n\t\t\t\t!dequal(current_data, previous_data) ||\n\t\t\t\t!dequal(current_headers, previous_headers)\n\t\t\t) {\n\t\t\t\tif (!dequal(current_headers, previous_headers)) {\n\t\t\t\t\tupdate_state((s) => ({\n\t\t\t\t\t\tsort_state: { sort_columns: [], row_order: [], initial_data: null },\n\t\t\t\t\t\tfilter_state: { filter_columns: [], initial_data: null }\n\t\t\t\t\t}));\n\t\t\t\t}\n\t\t\t\tdispatch(\"change\", {\n\t\t\t\t\tdata: data.map((row) => row.map((cell) => cell.value)),\n\t\t\t\t\theaders: current_headers,\n\t\t\t\t\tmetadata: null\n\t\t\t\t});\n\t\t\t\tif (!value_is_output) dispatch(\"input\");\n\t\t\t}\n\t\t},\n\t\treset_sort_state: () =>\n\t\t\tupdate_state((s) => {\n\t\t\t\tif (s.sort_state.initial_data && context.data) {\n\t\t\t\t\tconst original = s.sort_state.initial_data;\n\n\t\t\t\t\tupdate_array(original.data, context.data);\n\t\t\t\t\tupdate_array(original.display_value, context.display_value);\n\t\t\t\t\tupdate_array(original.styling, context.styling);\n\t\t\t\t}\n\n\t\t\t\treturn {\n\t\t\t\t\tsort_state: { sort_columns: [], row_order: [], initial_data: null }\n\t\t\t\t};\n\t\t\t}),\n\t\treset_filter_state: () =>\n\t\t\tupdate_state((s) => {\n\t\t\t\tif (s.filter_state.initial_data && context.data) {\n\t\t\t\t\tconst original = s.filter_state.initial_data;\n\n\t\t\t\t\tupdate_array(original.data, context.data);\n\t\t\t\t\tupdate_array(original.display_value, context.display_value);\n\t\t\t\t\tupdate_array(original.styling, context.styling);\n\t\t\t\t}\n\n\t\t\t\treturn {\n\t\t\t\t\tfilter_state: { filter_columns: [], initial_data: null }\n\t\t\t\t};\n\t\t\t}),\n\t\tset_active_cell_menu: (menu) =>\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: { ...s.ui_state, active_cell_menu: menu }\n\t\t\t})),\n\t\tset_active_header_menu: (menu) =>\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: { ...s.ui_state, active_header_menu: menu }\n\t\t\t})),\n\t\tset_selected_cells: (cells) =>\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: { ...s.ui_state, selected_cells: cells }\n\t\t\t})),\n\t\tset_selected: (selected) =>\n\t\t\tupdate_state((s) => ({ ui_state: { ...s.ui_state, selected } })),\n\t\tset_editing: (editing) =>\n\t\t\tupdate_state((s) => ({ ui_state: { ...s.ui_state, editing } })),\n\t\tclear_ui_state: () =>\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: {\n\t\t\t\t\tactive_cell_menu: null,\n\t\t\t\t\tactive_header_menu: null,\n\t\t\t\t\tselected_cells: [],\n\t\t\t\t\tselected: false,\n\t\t\t\t\tediting: false,\n\t\t\t\t\theader_edit: false,\n\t\t\t\t\tselected_header: false,\n\t\t\t\t\tactive_button: null,\n\t\t\t\t\tcopy_flash: false\n\t\t\t\t}\n\t\t\t})),\n\t\tset_header_edit: (header_index) =>\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: {\n\t\t\t\t\t...s.ui_state,\n\t\t\t\t\tselected_cells: [],\n\t\t\t\t\tselected_header: header_index,\n\t\t\t\t\theader_edit: header_index\n\t\t\t\t}\n\t\t\t})),\n\t\tset_selected_header: (header_index) =>\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: {\n\t\t\t\t\t...s.ui_state,\n\t\t\t\t\tselected_header: header_index,\n\t\t\t\t\tselected: false,\n\t\t\t\t\tselected_cells: []\n\t\t\t\t}\n\t\t\t})),\n\t\thandle_header_click: (col, editable) =>\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: {\n\t\t\t\t\t...s.ui_state,\n\t\t\t\t\tactive_cell_menu: null,\n\t\t\t\t\tactive_header_menu: null,\n\t\t\t\t\tselected: false,\n\t\t\t\t\tselected_cells: [],\n\t\t\t\t\tselected_header: col,\n\t\t\t\t\theader_edit: editable ? col : false\n\t\t\t\t}\n\t\t\t})),\n\t\tend_header_edit: (key) => {\n\t\t\tif ([\"Escape\", \"Enter\", \"Tab\"].includes(key)) {\n\t\t\t\tupdate_state((s) => ({\n\t\t\t\t\tui_state: { ...s.ui_state, selected: false, header_edit: false }\n\t\t\t\t}));\n\t\t\t}\n\t\t},\n\t\tget_selected_cells: () => get(state).ui_state.selected_cells,\n\t\tget_active_cell_menu: () => get(state).ui_state.active_cell_menu,\n\t\tget_active_button: () => get(state).ui_state.active_button,\n\t\tset_active_button: (button) =>\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: { ...s.ui_state, active_button: button }\n\t\t\t})),\n\t\tset_copy_flash: (value) =>\n\t\t\tupdate_state((s) => ({ ui_state: { ...s.ui_state, copy_flash: value } })),\n\t\thandle_cell_click: (event, row, col) => {\n\t\t\tevent.preventDefault();\n\t\t\tevent.stopPropagation();\n\n\t\t\tconst s = get(state);\n\t\t\tif (s.config.show_row_numbers && col === -1) return;\n\n\t\t\tlet actual_row = row;\n\t\t\tif (s.current_search_query && context.data) {\n\t\t\t\tconst filtered_indices: number[] = [];\n\t\t\t\tcontext.data.forEach((dataRow, idx) => {\n\t\t\t\t\tif (\n\t\t\t\t\t\tdataRow.some((cell) =>\n\t\t\t\t\t\t\tString(cell?.value)\n\t\t\t\t\t\t\t\t.toLowerCase()\n\t\t\t\t\t\t\t\t.includes(s.current_search_query?.toLowerCase() || \"\")\n\t\t\t\t\t\t)\n\t\t\t\t\t) {\n\t\t\t\t\t\tfiltered_indices.push(idx);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tactual_row = filtered_indices[row] ?? row;\n\t\t\t}\n\n\t\t\tconst cells = handle_selection(\n\t\t\t\t[actual_row, col],\n\t\t\t\ts.ui_state.selected_cells,\n\t\t\t\tevent\n\t\t\t);\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: {\n\t\t\t\t\t...s.ui_state,\n\t\t\t\t\tactive_cell_menu: null,\n\t\t\t\t\tactive_header_menu: null,\n\t\t\t\t\tselected_header: false,\n\t\t\t\t\theader_edit: false,\n\t\t\t\t\tselected_cells: cells,\n\t\t\t\t\tselected: cells[0]\n\t\t\t\t}\n\t\t\t}));\n\n\t\t\tif (s.config.editable && cells.length === 1) {\n\t\t\t\tupdate_state((s) => ({\n\t\t\t\t\tui_state: { ...s.ui_state, editing: [actual_row, col] }\n\t\t\t\t}));\n\t\t\t\ttick().then(() =>\n\t\t\t\t\tcontext.els![context.data![actual_row][col].id]?.input?.focus()\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\t// ensure parent has focus for keyboard navigation\n\t\t\t\ttick().then(() => {\n\t\t\t\t\tif (context.parent_element) {\n\t\t\t\t\t\tcontext.parent_element.focus();\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tcontext.dispatch?.(\"select\", {\n\t\t\t\tindex: [actual_row, col],\n\t\t\t\tcol_value: context.get_column!(col),\n\t\t\t\trow_value: context.get_row!(actual_row),\n\t\t\t\tvalue: context.get_data_at!(actual_row, col)\n\t\t\t});\n\t\t},\n\t\ttoggle_cell_menu: (event, row, col) => {\n\t\t\tevent.stopPropagation();\n\t\t\tconst current_menu = get(state).ui_state.active_cell_menu;\n\t\t\tif (current_menu?.row === row && current_menu.col === col) {\n\t\t\t\tupdate_state((s) => ({\n\t\t\t\t\tui_state: { ...s.ui_state, active_cell_menu: null }\n\t\t\t\t}));\n\t\t\t} else {\n\t\t\t\tconst cell = (event.target as HTMLElement).closest(\"td\");\n\t\t\t\tif (cell) {\n\t\t\t\t\tconst rect = cell.getBoundingClientRect();\n\t\t\t\t\tupdate_state((s) => ({\n\t\t\t\t\t\tui_state: {\n\t\t\t\t\t\t\t...s.ui_state,\n\t\t\t\t\t\t\tactive_cell_menu: { row, col, x: rect.right, y: rect.bottom }\n\t\t\t\t\t\t}\n\t\t\t\t\t}));\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\ttoggle_cell_button: (row, col) => {\n\t\t\tconst current_button = get(state).ui_state.active_button;\n\t\t\tconst new_button =\n\t\t\t\tcurrent_button?.type === \"cell\" &&\n\t\t\t\tcurrent_button.row === row &&\n\t\t\t\tcurrent_button.col === col\n\t\t\t\t\t? null\n\t\t\t\t\t: { type: \"cell\" as const, row, col };\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: { ...s.ui_state, active_button: new_button }\n\t\t\t}));\n\t\t},\n\t\thandle_select_column: (col) => {\n\t\t\tif (!context.data) return;\n\t\t\tconst cells = context.data.map((_, row) => [row, col] as CellCoordinate);\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: {\n\t\t\t\t\t...s.ui_state,\n\t\t\t\t\tselected_cells: cells,\n\t\t\t\t\tselected: cells[0],\n\t\t\t\t\tediting: false\n\t\t\t\t}\n\t\t\t}));\n\t\t\tsetTimeout(() => context.parent_element?.focus(), 0);\n\t\t},\n\t\thandle_select_row: (row) => {\n\t\t\tif (!context.data || !context.data[0]) return;\n\t\t\tconst cells = context.data[0].map(\n\t\t\t\t(_, col) => [row, col] as CellCoordinate\n\t\t\t);\n\t\t\tupdate_state((s) => ({\n\t\t\t\tui_state: {\n\t\t\t\t\t...s.ui_state,\n\t\t\t\t\tselected_cells: cells,\n\t\t\t\t\tselected: cells[0],\n\t\t\t\t\tediting: false\n\t\t\t\t}\n\t\t\t}));\n\t\t\tsetTimeout(() => context.parent_element?.focus(), 0);\n\t\t},\n\t\tget_next_cell_coordinates,\n\t\tget_range_selection,\n\t\tmove_cursor\n\t};\n}\n\nexport function create_dataframe_context(\n\tconfig: DataFrameState[\"config\"]\n): DataFrameContext {\n\tconst state = writable<DataFrameState>({\n\t\tconfig,\n\t\tcurrent_search_query: null,\n\t\tsort_state: { sort_columns: [], row_order: [], initial_data: null },\n\t\tfilter_state: { filter_columns: [], initial_data: null },\n\t\tui_state: {\n\t\t\tactive_cell_menu: null,\n\t\t\tactive_header_menu: null,\n\t\t\tselected_cells: [],\n\t\t\tselected: false,\n\t\t\tediting: false,\n\t\t\theader_edit: false,\n\t\t\tselected_header: false,\n\t\t\tactive_button: null,\n\t\t\tcopy_flash: false\n\t\t}\n\t});\n\n\tconst context: DataFrameContext = { state, actions: null as any };\n\tcontext.actions = create_actions(state, context);\n\n\tconst instance_id = Symbol(\n\t\t`dataframe_${Math.random().toString(36).substring(2)}`\n\t);\n\tsetContext(instance_id, context);\n\tsetContext(DATAFRAME_KEY, { instance_id, context });\n\n\treturn context;\n}\n\nexport function get_dataframe_context(): DataFrameContext {\n\tconst ctx = getContext<{ instance_id: symbol; context: DataFrameContext }>(\n\t\tDATAFRAME_KEY\n\t);\n\treturn ctx?.context ?? getContext<DataFrameContext>(DATAFRAME_KEY);\n}\n", "<script lang=\"ts\">\n\texport let position: \"column\" | \"row\";\n\texport let coords: [number, number];\n\texport let on_click: (() => void) | null = null;\n\n\t$: is_first_position =\n\t\tposition === \"column\" ? coords[0] === 0 : coords[1] === 0;\n\t$: direction =\n\t\tposition === \"column\"\n\t\t\t? is_first_position\n\t\t\t\t? \"down\"\n\t\t\t\t: \"up\"\n\t\t\t: is_first_position\n\t\t\t\t? \"right\"\n\t\t\t\t: \"left\";\n</script>\n\n<button\n\tclass=\"selection-button selection-button-{position} {is_first_position\n\t\t? `move-${direction}`\n\t\t: ''}\"\n\ton:click|stopPropagation={() => on_click && on_click()}\n\taria-label={`Select ${position}`}\n>\n\t<span class={direction}>\n\t\t<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n\t\t\t<path\n\t\t\t\td=\"m16.707 13.293-4-4a1 1 0 0 0-1.414 0l-4 4A1 1 0 0 0 8 15h8a1 1 0 0 0 .707-1.707z\"\n\t\t\t\tdata-name={direction}\n\t\t\t/>\n\t\t</svg>\n\t</span>\n</button>\n\n<style>\n\t.selection-button {\n\t\tposition: absolute;\n\t\tbackground: var(--color-accent);\n\t\twidth: var(--size-3);\n\t\theight: var(--size-5);\n\t\tcolor: var(--background-fill-primary);\n\t}\n\n\t.selection-button-column {\n\t\ttop: -15px;\n\t\tleft: 50%;\n\t\ttransform: translateX(-50%) rotate(90deg);\n\t\tborder-radius: var(--radius-sm) 0 0 var(--radius-sm);\n\t}\n\n\t.selection-button-row {\n\t\tleft: calc(var(--size-2-5) * -1);\n\t\tborder-radius: var(--radius-sm) 0 0 var(--radius-sm);\n\t}\n\n\t.move-down {\n\t\tbottom: -14px;\n\t\ttop: auto;\n\t\tborder-radius: 0 var(--radius-sm) var(--radius-sm) 0;\n\t}\n\n\t.move-right {\n\t\tleft: auto;\n\t\tright: calc(var(--size-2-5) * -1);\n\t\tborder-radius: 0 var(--radius-sm) var(--radius-sm) 0;\n\t}\n\n\tsvg {\n\t\tfill: currentColor;\n\t}\n\n\tspan {\n\t\tdisplay: flex;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.up {\n\t\ttransform: rotate(-90deg);\n\t}\n\n\t.down {\n\t\ttransform: rotate(90deg);\n\t}\n\n\t.left {\n\t\ttransform: rotate(-90deg);\n\t}\n\n\t.right {\n\t\ttransform: rotate(90deg);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { BaseCheckbox } from \"@gradio/checkbox\";\n\n\texport let value: boolean | string = false;\n\texport let editable = true;\n\texport let on_change: (value: boolean) => void;\n\n\t$: bool_value =\n\t\ttypeof value === \"string\" ? value.toLowerCase() === \"true\" : !!value;\n\n\tfunction handle_change(event: CustomEvent<boolean>): void {\n\t\tif (editable) {\n\t\t\ton_change(event.detail);\n\t\t}\n\t}\n</script>\n\n<div class=\"bool-cell\" role=\"button\" tabindex=\"-1\">\n\t<BaseCheckbox\n\t\tbind:value={bool_value}\n\t\tlabel=\"\"\n\t\tinteractive={editable}\n\t\ton:change={handle_change}\n\t/>\n</div>\n\n<style>\n\t.bool-cell {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\twidth: min-content;\n\t\theight: var(--size-full);\n\t\tmargin: 0 auto;\n\t}\n\n\t.bool-cell :global(input:disabled) {\n\t\tcursor: not-allowed;\n\t}\n\n\t.bool-cell :global(label) {\n\t\tmargin: 0;\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\n\t.bool-cell :global(span) {\n\t\tdisplay: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { MarkdownCode } from \"@gradio/markdown-code\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport SelectionButtons from \"./icons/SelectionButtons.svelte\";\n\timport BooleanCell from \"./BooleanCell.svelte\";\n\n\texport let edit: boolean;\n\texport let value: string | number = \"\";\n\texport let display_value: string | null = null;\n\texport let styling = \"\";\n\texport let header = false;\n\texport let datatype:\n\t\t| \"str\"\n\t\t| \"markdown\"\n\t\t| \"html\"\n\t\t| \"number\"\n\t\t| \"bool\"\n\t\t| \"date\"\n\t\t| \"image\" = \"str\";\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let line_breaks = true;\n\texport let editable = true;\n\texport let is_static = false;\n\texport let max_chars: number | null = null;\n\texport let components: Record<string, any> = {};\n\texport let i18n: I18nFormatter;\n\texport let is_dragging = false;\n\texport let wrap_text = false;\n\n\texport let show_selection_buttons = false;\n\texport let coords: [number, number];\n\texport let on_select_column: ((col: number) => void) | null = null;\n\texport let on_select_row: ((row: number) => void) | null = null;\n\texport let el: HTMLTextAreaElement | null;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tblur: { blur_event: FocusEvent; coords: [number, number] };\n\t\tkeydown: KeyboardEvent;\n\t}>();\n\n\tfunction truncate_text(\n\t\ttext: string | number,\n\t\tmax_length: number | null = null,\n\t\tis_image = false\n\t): string {\n\t\tif (is_image) return String(text);\n\t\tconst str = String(text);\n\t\tif (!max_length || max_length <= 0) return str;\n\t\tif (str.length <= max_length) return str;\n\t\treturn str.slice(0, max_length) + \"...\";\n\t}\n\n\t$: should_truncate = !edit && max_chars !== null && max_chars > 0;\n\n\t$: display_content = editable\n\t\t? value\n\t\t: display_value !== null\n\t\t\t? display_value\n\t\t\t: value;\n\n\t$: display_text = should_truncate\n\t\t? truncate_text(display_content, max_chars, datatype === \"image\")\n\t\t: display_content;\n\n\tfunction use_focus(node: HTMLTextAreaElement): any {\n\t\trequestAnimationFrame(() => {\n\t\t\tnode.focus();\n\t\t});\n\n\t\treturn {};\n\t}\n\n\tfunction handle_blur(event: FocusEvent): void {\n\t\tdispatch(\"blur\", {\n\t\t\tblur_event: event,\n\t\t\tcoords: coords\n\t\t});\n\t}\n\n\tfunction handle_keydown(event: KeyboardEvent): void {\n\t\tdispatch(\"keydown\", event);\n\t}\n\n\tfunction handle_bool_change(new_value: boolean): void {\n\t\tvalue = new_value.toString();\n\t\tdispatch(\"blur\", {\n\t\t\tblur_event: {\n\t\t\t\ttarget: {\n\t\t\t\t\ttype: \"checkbox\",\n\t\t\t\t\tchecked: new_value,\n\t\t\t\t\tvalue: new_value.toString()\n\t\t\t\t}\n\t\t\t} as unknown as FocusEvent,\n\t\t\tcoords: coords\n\t\t});\n\t}\n</script>\n\n{#if edit && datatype !== \"bool\"}\n\t<textarea\n\t\treadonly={is_static}\n\t\taria-readonly={is_static}\n\t\taria-label={is_static ? \"Cell is read-only\" : \"Edit cell\"}\n\t\tbind:this={el}\n\t\tbind:value\n\t\tclass:header\n\t\ttabindex=\"-1\"\n\t\ton:blur={handle_blur}\n\t\ton:mousedown|stopPropagation\n\t\ton:click|stopPropagation\n\t\tuse:use_focus\n\t\ton:keydown={handle_keydown}\n\t/>\n{/if}\n\n{#if datatype === \"bool\"}\n\t<BooleanCell\n\t\tvalue={String(display_content)}\n\t\t{editable}\n\t\ton_change={handle_bool_change}\n\t/>\n{:else}\n\t<span\n\t\tclass:dragging={is_dragging}\n\t\ton:keydown={handle_keydown}\n\t\ttabindex=\"0\"\n\t\trole=\"button\"\n\t\tclass:edit\n\t\tclass:expanded={edit}\n\t\tclass:multiline={header}\n\t\ton:focus|preventDefault\n\t\tstyle={styling}\n\t\tdata-editable={editable}\n\t\tdata-max-chars={max_chars}\n\t\tdata-expanded={edit}\n\t\tplaceholder=\" \"\n\t\tclass:text={datatype === \"str\"}\n\t\tclass:wrap={wrap_text}\n\t>\n\t\t{#if datatype === \"image\" && components.image}\n\t\t\t<svelte:component\n\t\t\t\tthis={components.image}\n\t\t\t\tvalue={{ url: display_text }}\n\t\t\t\tshow_label={false}\n\t\t\t\tlabel=\"cell-image\"\n\t\t\t\tshow_download_button={false}\n\t\t\t\t{i18n}\n\t\t\t\tgradio={{ dispatch: () => {} }}\n\t\t\t/>\n\t\t{:else if datatype === \"html\"}\n\t\t\t{@html display_text}\n\t\t{:else if datatype === \"markdown\"}\n\t\t\t<MarkdownCode\n\t\t\t\tmessage={display_text.toLocaleString()}\n\t\t\t\t{latex_delimiters}\n\t\t\t\t{line_breaks}\n\t\t\t\tchatbot={false}\n\t\t\t/>\n\t\t{:else}\n\t\t\t{display_text}\n\t\t{/if}\n\t</span>\n{/if}\n\n{#if show_selection_buttons && coords && on_select_column && on_select_row}\n\t<SelectionButtons\n\t\tposition=\"column\"\n\t\t{coords}\n\t\ton_click={() => on_select_column(coords[1])}\n\t/>\n\t<SelectionButtons\n\t\tposition=\"row\"\n\t\t{coords}\n\t\ton_click={() => on_select_row(coords[0])}\n\t/>\n{/if}\n\n<style>\n\t.dragging {\n\t\tcursor: crosshair !important;\n\t}\n\n\ttextarea {\n\t\tposition: absolute;\n\t\tflex: 1 1 0%;\n\t\ttransform: translateX(-0.1px);\n\t\toutline: none;\n\t\tborder: none;\n\t\tbackground: transparent;\n\t\tcursor: text;\n\t\twidth: calc(100% - var(--size-2));\n\t\tresize: none;\n\t\theight: 100%;\n\t\tpadding-left: 0;\n\t\tfont-size: inherit;\n\t\tfont-weight: inherit;\n\t\tline-height: var(--line-lg);\n\t}\n\n\ttextarea:focus {\n\t\toutline: none;\n\t}\n\n\tspan {\n\t\tflex: 1 1 0%;\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\toutline: none;\n\t\t-webkit-user-select: text;\n\t\t-moz-user-select: text;\n\t\t-ms-user-select: text;\n\t\tuser-select: text;\n\t\tcursor: text;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\toverflow: hidden;\n\t}\n\n\tspan.text.expanded {\n\t\theight: auto;\n\t\tmin-height: 100%;\n\t\twhite-space: pre-wrap;\n\t\tword-break: break-word;\n\t\toverflow: visible;\n\t}\n\n\t.multiline {\n\t\twhite-space: pre;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t}\n\n\t.header {\n\t\ttransform: translateX(0);\n\t\tfont-weight: var(--weight-bold);\n\t\twhite-space: nowrap;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\tmargin-left: var(--size-1);\n\t}\n\n\t.edit {\n\t\topacity: 0;\n\t\tpointer-events: none;\n\t}\n\n\tspan :global(img) {\n\t\tmax-height: 100px;\n\t\twidth: auto;\n\t\tobject-fit: contain;\n\t}\n\n\ttextarea:read-only {\n\t\tcursor: not-allowed;\n\t}\n\n\t.wrap,\n\t.wrap.expanded {\n\t\twhite-space: normal;\n\t\tword-wrap: break-word;\n\t\toverflow-wrap: break-word;\n\t\tword-wrap: break-word;\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let index: number | null = null;\n\texport let is_header = false;\n</script>\n\n{#if is_header}\n\t<th tabindex=\"-1\" class=\"row-number\">\n\t\t<div class=\"cell-wrap\">\n\t\t\t<div class=\"header-content\">\n\t\t\t\t<div class=\"header-text\"></div>\n\t\t\t</div>\n\t\t</div>\n\t</th>\n{:else}\n\t<td class=\"row-number\" tabindex=\"-1\" data-row={index} data-col=\"row-number\">\n\t\t{index !== null ? index + 1 : \"\"}\n\t</td>\n{/if}\n\n<style>\n\t.row-number {\n\t\ttext-align: center;\n\t\tpadding: var(--size-1);\n\t\tmin-width: var(--size-12);\n\t\twidth: var(--size-12);\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t\tfont-weight: var(--weight-semibold);\n\t\tborder-right: 1px solid var(--border-color-primary);\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let on_click: (event: MouseEvent) => void;\n</script>\n\n<button\n\taria-label=\"Open cell menu\"\n\tclass=\"cell-menu-button\"\n\taria-haspopup=\"menu\"\n\ton:click={on_click}\n\ton:touchstart={(event) => {\n\t\tevent.preventDefault();\n\t\tconst touch = event.touches[0];\n\t\tconst mouseEvent = new MouseEvent(\"click\", {\n\t\t\tclientX: touch.clientX,\n\t\t\tclientY: touch.clientY,\n\t\t\tbubbles: true,\n\t\t\tcancelable: true,\n\t\t\tview: window\n\t\t});\n\t\ton_click(mouseEvent);\n\t}}\n>\n\t&#8942;\n</button>\n\n<style>\n\t.cell-menu-button {\n\t\tflex-shrink: 0;\n\t\tdisplay: none;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--block-radius);\n\t\twidth: var(--size-5);\n\t\theight: var(--size-5);\n\t\tmin-width: var(--size-5);\n\t\tpadding: 0;\n\t\tmargin-right: var(--spacing-sm);\n\t\tz-index: 2;\n\t\tposition: absolute;\n\t\tright: var(--size-1);\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t}\n</style>\n", "<div class=\"wrapper\" aria-label=\"Static column\">\n\t<svg\n\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\twidth=\"13\"\n\t\theight=\"13\"\n\t\tviewBox=\"0 0 24 24\"\n\t\tfill=\"none\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"2\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t>\n\t\t<rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\"></rect>\n\t\t<path d=\"M7 11V7a5 5 0 0 1 10 0v4\"></path>\n\t</svg>\n</div>\n\n<style>\n\t.wrapper {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let size = 16;\n</script>\n\n<svg\n\twidth={size}\n\theight={size}\n\tviewBox=\"0 0 16 16\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M4 8L8 4L12 8\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t/>\n\t<path\n\t\td=\"M8 4V12\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\texport let size = 16;\n</script>\n\n<svg\n\twidth={size}\n\theight={size}\n\tviewBox=\"0 0 16 16\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M4 8L8 12L12 8\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t/>\n\t<path\n\t\td=\"M8 12V4\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\texport let icon: string;\n</script>\n\n{#if icon == \"add-column-right\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<rect\n\t\t\tx=\"4\"\n\t\t\ty=\"6\"\n\t\t\twidth=\"4\"\n\t\t\theight=\"12\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M12 12H19M16 8L19 12L16 16\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"add-column-left\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<rect\n\t\t\tx=\"16\"\n\t\t\ty=\"6\"\n\t\t\twidth=\"4\"\n\t\t\theight=\"12\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M12 12H5M8 8L5 12L8 16\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"add-row-above\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<rect\n\t\t\tx=\"6\"\n\t\t\ty=\"16\"\n\t\t\twidth=\"12\"\n\t\t\theight=\"4\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M12 12V5M8 8L12 5L16 8\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"add-row-below\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<rect\n\t\t\tx=\"6\"\n\t\t\ty=\"4\"\n\t\t\twidth=\"12\"\n\t\t\theight=\"4\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M12 12V19M8 16L12 19L16 16\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"delete-row\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<rect\n\t\t\tx=\"5\"\n\t\t\ty=\"10\"\n\t\t\twidth=\"14\"\n\t\t\theight=\"4\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M8 7L16 17M16 7L8 17\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"delete-column\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<rect\n\t\t\tx=\"10\"\n\t\t\ty=\"5\"\n\t\t\twidth=\"4\"\n\t\t\theight=\"14\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M7 8L17 16M17 8L7 16\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"sort-asc\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<path\n\t\t\td=\"M8 16L12 12L16 16\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t\tstroke-linecap=\"round\"\n\t\t\tstroke-linejoin=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M12 12V19\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M5 7H19\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"sort-desc\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<path\n\t\t\td=\"M8 12L12 16L16 12\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tfill=\"none\"\n\t\t\tstroke-linecap=\"round\"\n\t\t\tstroke-linejoin=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M12 16V9\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M5 5H19\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"clear-sort\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<path\n\t\t\td=\"M5 5H19\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M5 9H15\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M5 13H11\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M5 17H7\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M17 17L21 21M21 17L17 21\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"filter\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<path\n\t\t\td=\"M5 5H19\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M8 9H16\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M11 13H13\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{:else if icon == \"clear-filter\"}\n\t<svg viewBox=\"0 0 24 24\" width=\"16\" height=\"16\">\n\t\t<path\n\t\t\td=\"M5 5H19\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M8 9H16\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M11 13H13\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t\t<path\n\t\t\td=\"M17 17L21 21M21 17L17 21\"\n\t\t\tstroke=\"currentColor\"\n\t\t\tstroke-width=\"2\"\n\t\t\tstroke-linecap=\"round\"\n\t\t/>\n\t</svg>\n{/if}\n", "<script lang=\"ts\">\n\timport EditableCell from \"./EditableCell.svelte\";\n\timport CellMenuButton from \"./CellMenuButton.svelte\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport { get_sort_status } from \"./utils/sort_utils\";\n\timport Padlock from \"./icons/Padlock.svelte\";\n\timport SortArrowUp from \"./icons/SortArrowUp.svelte\";\n\timport SortArrowDown from \"./icons/SortArrowDown.svelte\";\n\timport type { SortDirection } from \"./context/dataframe_context\";\n\timport CellMenuIcons from \"./CellMenuIcons.svelte\";\n\timport type { FilterDatatype } from \"./context/dataframe_context\";\n\texport let value: string;\n\texport let i: number;\n\texport let actual_pinned_columns: number;\n\texport let header_edit: number | false;\n\texport let selected_header: number | false;\n\texport let headers: string[];\n\texport let get_cell_width: (index: number) => string;\n\texport let handle_header_click: (event: MouseEvent, col: number) => void;\n\texport let toggle_header_menu: (event: MouseEvent, col: number) => void;\n\texport let end_header_edit: (event: CustomEvent<KeyboardEvent>) => void;\n\texport let sort_columns: { col: number; direction: SortDirection }[] = [];\n\texport let filter_columns: {\n\t\tcol: number;\n\t\tdatatype: FilterDatatype;\n\t\tfilter: string;\n\t\tvalue: string;\n\t}[] = [];\n\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let line_breaks: boolean;\n\texport let max_chars: number | undefined;\n\texport let editable: boolean;\n\texport let i18n: I18nFormatter;\n\texport let el: HTMLTextAreaElement | null;\n\texport let is_static: boolean;\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\n\t$: can_add_columns = col_count && col_count[1] === \"dynamic\";\n\t$: sort_index = sort_columns.findIndex((item) => item.col === i);\n\t$: filter_index = filter_columns.findIndex((item) => item.col === i);\n\t$: sort_priority = sort_index !== -1 ? sort_index + 1 : null;\n\t$: current_direction =\n\t\tsort_index !== -1 ? sort_columns[sort_index].direction : null;\n\n\tfunction get_header_position(col_index: number): string {\n\t\tif (col_index >= actual_pinned_columns) {\n\t\t\treturn \"auto\";\n\t\t}\n\n\t\tif (col_index === 0) {\n\t\t\treturn \"0\";\n\t\t}\n\n\t\tconst previous_widths = Array(col_index)\n\t\t\t.fill(0)\n\t\t\t.map((_, idx) => {\n\t\t\t\treturn get_cell_width(idx);\n\t\t\t})\n\t\t\t.join(\" + \");\n\n\t\treturn `calc(${previous_widths})`;\n\t}\n</script>\n\n<th\n\tclass:pinned-column={i < actual_pinned_columns}\n\tclass:last-pinned={i === actual_pinned_columns - 1}\n\tclass:focus={header_edit === i || selected_header === i}\n\tclass:sorted={sort_index !== -1}\n\tclass:filtered={filter_index !== -1}\n\taria-sort={get_sort_status(value, sort_columns, headers) === \"none\"\n\t\t? \"none\"\n\t\t: get_sort_status(value, sort_columns, headers) === \"asc\"\n\t\t\t? \"ascending\"\n\t\t\t: \"descending\"}\n\tstyle=\"width: {get_cell_width(i)}; left: {get_header_position(i)};\"\n\ton:click={(event) => handle_header_click(event, i)}\n\ton:mousedown={(event) => {\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\t}}\n\ttitle={value}\n>\n\t<div class=\"cell-wrap\">\n\t\t<div class=\"header-content\">\n\t\t\t<button\n\t\t\t\tclass=\"header-button\"\n\t\t\t\ton:click={(event) => handle_header_click(event, i)}\n\t\t\t\ton:mousedown={(event) => {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tevent.stopPropagation();\n\t\t\t\t}}\n\t\t\t\ttitle={value}\n\t\t\t>\n\t\t\t\t<EditableCell\n\t\t\t\t\t{max_chars}\n\t\t\t\t\tbind:value\n\t\t\t\t\tbind:el\n\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t{line_breaks}\n\t\t\t\t\tedit={header_edit === i}\n\t\t\t\t\ton:keydown={(event) => {\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\tevent.detail.key === \"Enter\" ||\n\t\t\t\t\t\t\tevent.detail.key === \"Escape\" ||\n\t\t\t\t\t\t\tevent.detail.key === \"Tab\"\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tend_header_edit(event);\n\t\t\t\t\t\t}\n\t\t\t\t\t}}\n\t\t\t\t\theader\n\t\t\t\t\t{editable}\n\t\t\t\t\t{is_static}\n\t\t\t\t\t{i18n}\n\t\t\t\t\tcoords={[i, 0]}\n\t\t\t\t/>\n\t\t\t\t{#if sort_index !== -1}\n\t\t\t\t\t<div class=\"sort-indicators\">\n\t\t\t\t\t\t<span class=\"sort-arrow\">\n\t\t\t\t\t\t\t{#if current_direction === \"asc\"}\n\t\t\t\t\t\t\t\t<SortArrowUp size={12} />\n\t\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t\t<SortArrowDown size={12} />\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t{#if sort_columns.length > 1}\n\t\t\t\t\t\t\t<span class=\"sort-priority\">\n\t\t\t\t\t\t\t\t{sort_priority}\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t\t{#if filter_index !== -1}\n\t\t\t\t\t<div class=\"filter-indicators\">\n\t\t\t\t\t\t<span class=\"filter-icon\">\n\t\t\t\t\t\t\t<CellMenuIcons icon=\"filter\" />\n\t\t\t\t\t\t</span>\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t</button>\n\t\t\t{#if is_static}\n\t\t\t\t<Padlock />\n\t\t\t{/if}\n\t\t</div>\n\t\t{#if can_add_columns}\n\t\t\t<CellMenuButton on_click={(event) => toggle_header_menu(event, i)} />\n\t\t{/if}\n\t</div>\n</th>\n\n<style>\n\tth {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\toutline: none;\n\t\tbox-shadow: inset 0 0 0 1px var(--ring-color);\n\t\tpadding: 0;\n\t\tbackground: var(--table-even-background-fill);\n\t\tborder-right-width: 0px;\n\t\tborder-left-width: 1px;\n\t\tborder-style: solid;\n\t\tborder-color: var(--border-color-primary);\n\t}\n\n\tth:first-child {\n\t\tborder-top-left-radius: var(--table-radius);\n\t\tborder-bottom-left-radius: var(--table-radius);\n\t\tborder-left-width: 0px;\n\t}\n\n\tth:last-child {\n\t\tborder-top-right-radius: var(--table-radius);\n\t\tborder-bottom-right-radius: var(--table-radius);\n\t}\n\n\tth.focus {\n\t\t--ring-color: var(--color-accent);\n\t\tbox-shadow: inset 0 0 0 2px var(--ring-color);\n\t\tz-index: 4;\n\t}\n\n\tth.focus :global(.cell-menu-button) {\n\t\tdisplay: flex;\n\t}\n\n\tth:hover :global(.cell-menu-button) {\n\t\tdisplay: flex;\n\t}\n\n\t.cell-wrap {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: flex-start;\n\t\toutline: none;\n\t\tmin-height: var(--size-9);\n\t\tposition: relative;\n\t\theight: 100%;\n\t\tpadding: var(--size-2);\n\t\tbox-sizing: border-box;\n\t\tmargin: 0;\n\t\tgap: var(--size-1);\n\t\toverflow: visible;\n\t\tmin-width: 0;\n\t\tborder-radius: var(--table-radius);\n\t}\n\n\t.header-content {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\toverflow: hidden;\n\t\tflex-grow: 1;\n\t\tmin-width: 0;\n\t\twhite-space: normal;\n\t\toverflow-wrap: break-word;\n\t\tword-break: normal;\n\t\theight: 100%;\n\t\tgap: var(--size-1);\n\t}\n\n\t.header-button {\n\t\tdisplay: flex;\n\t\ttext-align: left;\n\t\twidth: 100%;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tposition: relative;\n\t}\n\n\t.sort-indicators {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-left: var(--size-1);\n\t\tgap: var(--size-1);\n\t}\n\n\t.sort-arrow {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.sort-priority {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: var(--size-2);\n\t\tbackground-color: var(--button-secondary-background-fill);\n\t\tcolor: var(--body-text-color);\n\t\tborder-radius: var(--radius-sm);\n\t\twidth: var(--size-2-5);\n\t\theight: var(--size-2-5);\n\t\tpadding: var(--size-1-5);\n\t}\n\n\t.filter-indicators {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-left: var(--size-1);\n\t\tgap: var(--size-1);\n\t}\n\n\t.filter-icon {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.pinned-column {\n\t\tposition: sticky;\n\t\tz-index: 5;\n\t\tborder-right: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport EditableCell from \"./EditableCell.svelte\";\n\timport CellMenuButton from \"./CellMenuButton.svelte\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport type { Datatype } from \"./utils\";\n\timport { is_cell_in_selection } from \"./selection_utils\";\n\n\texport let value: string | number;\n\texport let index: number;\n\texport let j: number;\n\texport let actual_pinned_columns: number;\n\texport let get_cell_width: (index: number) => string;\n\texport let handle_cell_click: (\n\t\tevent: MouseEvent,\n\t\trow: number,\n\t\tcol: number\n\t) => void;\n\texport let handle_blur: (\n\t\tevent: CustomEvent<{\n\t\t\tblur_event: FocusEvent;\n\t\t\tcoords: [number, number];\n\t\t}>\n\t) => void;\n\texport let toggle_cell_menu: (\n\t\tevent: MouseEvent,\n\t\trow: number,\n\t\tcol: number\n\t) => void;\n\texport let is_cell_selected: (\n\t\tcoords: [number, number],\n\t\tselected_cells: [number, number][]\n\t) => string;\n\texport let should_show_cell_menu: (\n\t\tcoords: [number, number],\n\t\tselected_cells: [number, number][],\n\t\teditable: boolean\n\t) => boolean;\n\texport let selected_cells: [number, number][];\n\texport let copy_flash: boolean;\n\texport let active_cell_menu: {\n\t\trow: number;\n\t\tcol: number;\n\t\tx: number;\n\t\ty: number;\n\t} | null;\n\texport let styling: string | undefined;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let line_breaks: boolean;\n\texport let datatype: Datatype;\n\texport let editing: [number, number] | false;\n\texport let max_chars: number | undefined;\n\texport let editable: boolean;\n\texport let is_static = false;\n\texport let i18n: I18nFormatter;\n\texport let components: Record<string, any> = {};\n\texport let el: {\n\t\tcell: HTMLTableCellElement | null;\n\t\tinput: HTMLTextAreaElement | null;\n\t};\n\texport let handle_select_column: (col: number) => void;\n\texport let handle_select_row: (row: number) => void;\n\texport let is_dragging: boolean;\n\texport let display_value: string | undefined;\n\texport let wrap = false;\n\n\tfunction get_cell_position(col_index: number): string {\n\t\tif (col_index >= actual_pinned_columns) {\n\t\t\treturn \"auto\";\n\t\t}\n\n\t\tif (col_index === 0) {\n\t\t\treturn \"0\";\n\t\t}\n\n\t\tconst previous_widths = Array(col_index)\n\t\t\t.fill(0)\n\t\t\t.map((_, idx) => {\n\t\t\t\treturn get_cell_width(idx);\n\t\t\t})\n\t\t\t.join(\" + \");\n\n\t\treturn `calc(${previous_widths})`;\n\t}\n\n\t$: cell_classes = is_cell_selected([index, j], selected_cells || []);\n\t$: is_in_selection = is_cell_in_selection([index, j], selected_cells);\n\t$: has_no_top = cell_classes.includes(\"no-top\");\n\t$: has_no_bottom = cell_classes.includes(\"no-bottom\");\n\t$: has_no_left = cell_classes.includes(\"no-left\");\n\t$: has_no_right = cell_classes.includes(\"no-right\");\n</script>\n\n<td\n\tclass:pinned-column={j < actual_pinned_columns}\n\tclass:last-pinned={j === actual_pinned_columns - 1}\n\ttabindex={j < actual_pinned_columns ? -1 : 0}\n\tbind:this={el.cell}\n\tdata-row={index}\n\tdata-col={j}\n\tdata-testid={`cell-${index}-${j}`}\n\ton:mousedown={(e) => handle_cell_click(e, index, j)}\n\ton:contextmenu|preventDefault={(e) => toggle_cell_menu(e, index, j)}\n\tstyle=\"width: {get_cell_width(j)}; left: {get_cell_position(j)}; {styling ||\n\t\t''}\"\n\tclass:flash={copy_flash && is_in_selection}\n\tclass:cell-selected={is_in_selection}\n\tclass:no-top={has_no_top}\n\tclass:no-bottom={has_no_bottom}\n\tclass:no-left={has_no_left}\n\tclass:no-right={has_no_right}\n\tclass:menu-active={active_cell_menu &&\n\t\tactive_cell_menu.row === index &&\n\t\tactive_cell_menu.col === j}\n\tclass:dragging={is_dragging}\n>\n\t<div class=\"cell-wrap\">\n\t\t<EditableCell\n\t\t\tbind:value\n\t\t\tbind:el={el.input}\n\t\t\tdisplay_value={display_value !== undefined\n\t\t\t\t? display_value\n\t\t\t\t: String(value)}\n\t\t\t{latex_delimiters}\n\t\t\t{line_breaks}\n\t\t\t{editable}\n\t\t\t{is_static}\n\t\t\tedit={editing && editing[0] === index && editing[1] === j}\n\t\t\t{datatype}\n\t\t\ton:focus={() => {\n\t\t\t\tconst row = index;\n\t\t\t\tconst col = j;\n\t\t\t\tif (!selected_cells.some(([r, c]) => r === row && c === col)) {\n\t\t\t\t\tselected_cells = [[row, col]];\n\t\t\t\t}\n\t\t\t}}\n\t\t\ton:blur={handle_blur}\n\t\t\t{max_chars}\n\t\t\t{i18n}\n\t\t\t{components}\n\t\t\tshow_selection_buttons={selected_cells.length === 1 &&\n\t\t\t\tselected_cells[0][0] === index &&\n\t\t\t\tselected_cells[0][1] === j}\n\t\t\tcoords={[index, j]}\n\t\t\ton_select_column={handle_select_column}\n\t\t\ton_select_row={handle_select_row}\n\t\t\t{is_dragging}\n\t\t\twrap_text={wrap}\n\t\t/>\n\t\t{#if editable && should_show_cell_menu([index, j], selected_cells, editable)}\n\t\t\t<CellMenuButton on_click={(event) => toggle_cell_menu(event, index, j)} />\n\t\t{/if}\n\t</div>\n</td>\n\n<style>\n\ttd {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\toutline: none;\n\t\tbox-shadow: inset 0 0 0 1px var(--ring-color);\n\t\tpadding: 0;\n\t\tborder-right-width: 0px;\n\t\tborder-left-width: 1px;\n\t\tborder-style: solid;\n\t\tborder-color: var(--border-color-primary);\n\t}\n\n\t.cell-wrap {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: flex-start;\n\t\toutline: none;\n\t\tmin-height: var(--size-9);\n\t\tposition: relative;\n\t\theight: 100%;\n\t\tpadding: var(--size-2);\n\t\tbox-sizing: border-box;\n\t\tmargin: 0;\n\t\tgap: var(--size-1);\n\t\toverflow: visible;\n\t\tmin-width: 0;\n\t\tborder-radius: var(--table-radius);\n\t}\n\n\t.cell-selected {\n\t\t--ring-color: var(--color-accent);\n\t\tbox-shadow: inset 0 0 0 2px var(--ring-color);\n\t\tz-index: 2;\n\t\tposition: relative;\n\t}\n\n\t.cell-selected :global(.cell-menu-button) {\n\t\tdisplay: flex;\n\t}\n\n\t.flash.cell-selected {\n\t\tanimation: flash-color 700ms ease-out;\n\t}\n\n\t@keyframes flash-color {\n\t\t0%,\n\t\t30% {\n\t\t\tbackground: var(--color-accent-copied);\n\t\t}\n\n\t\t100% {\n\t\t\tbackground: transparent;\n\t\t}\n\t}\n\n\t.pinned-column {\n\t\tposition: sticky;\n\t\tz-index: 3;\n\t\tborder-right: none;\n\t}\n\n\t.pinned-column:nth-child(odd) {\n\t\tbackground: var(--table-odd-background-fill);\n\t}\n\n\t.pinned-column:nth-child(even) {\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\ttd:first-child {\n\t\tborder-left-width: 0px;\n\t}\n\n\t:global(tr:last-child) td:first-child {\n\t\tborder-bottom-left-radius: var(--table-radius);\n\t}\n\n\t:global(tr:last-child) td:last-child {\n\t\tborder-bottom-right-radius: var(--table-radius);\n\t}\n\n\t.dragging {\n\t\tcursor: crosshair;\n\t}\n\n\t/* Add back the cell selection border styles */\n\t.cell-selected.no-top {\n\t\tbox-shadow:\n\t\t\tinset 2px 0 0 var(--ring-color),\n\t\t\tinset -2px 0 0 var(--ring-color),\n\t\t\tinset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-bottom {\n\t\tbox-shadow:\n\t\t\tinset 2px 0 0 var(--ring-color),\n\t\t\tinset -2px 0 0 var(--ring-color),\n\t\t\tinset 0 2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-left {\n\t\tbox-shadow:\n\t\t\tinset 0 2px 0 var(--ring-color),\n\t\t\tinset -2px 0 0 var(--ring-color),\n\t\t\tinset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-right {\n\t\tbox-shadow:\n\t\t\tinset 0 2px 0 var(--ring-color),\n\t\t\tinset 2px 0 0 var(--ring-color),\n\t\t\tinset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-top.no-left {\n\t\tbox-shadow:\n\t\t\tinset -2px 0 0 var(--ring-color),\n\t\t\tinset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-top.no-right {\n\t\tbox-shadow:\n\t\t\tinset 2px 0 0 var(--ring-color),\n\t\t\tinset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-bottom.no-left {\n\t\tbox-shadow:\n\t\t\tinset -2px 0 0 var(--ring-color),\n\t\t\tinset 0 2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-bottom.no-right {\n\t\tbox-shadow:\n\t\t\tinset 2px 0 0 var(--ring-color),\n\t\t\tinset 0 2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-top.no-bottom {\n\t\tbox-shadow:\n\t\t\tinset 2px 0 0 var(--ring-color),\n\t\t\tinset -2px 0 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-left.no-right {\n\t\tbox-shadow:\n\t\t\tinset 0 2px 0 var(--ring-color),\n\t\t\tinset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-top.no-left.no-right {\n\t\tbox-shadow: inset 0 -2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-bottom.no-left.no-right {\n\t\tbox-shadow: inset 0 2px 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-left.no-top.no-bottom {\n\t\tbox-shadow: inset -2px 0 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-right.no-top.no-bottom {\n\t\tbox-shadow: inset 2px 0 0 var(--ring-color);\n\t}\n\n\t.cell-selected.no-top.no-bottom.no-left.no-right {\n\t\tbox-shadow: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\texport let on_click: () => void;\n</script>\n\n<button class=\"add-row-button\" on:click={on_click} aria-label=\"Add row\">\n\t+\n</button>\n\n<style>\n\t.add-row-button {\n\t\twidth: 100%;\n\t\tpadding: var(--size-1);\n\t\tbackground: transparent;\n\t\tborder: 1px dashed var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tcolor: var(--body-text-color);\n\t\tcursor: pointer;\n\t\ttransition: all 150ms;\n\t\tmargin-top: var(--size-2);\n\t\tz-index: 10;\n\t\tposition: relative;\n\t\tpointer-events: auto;\n\t}\n\n\t.add-row-button:hover {\n\t\tbackground: var(--background-fill-secondary);\n\t\tborder-style: solid;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onMount, tick, createEventDispatcher } from \"svelte\";\n\timport { _ } from \"svelte-i18n\";\n\n\texport let items: any[][] = [];\n\n\texport let max_height: number;\n\texport let actual_height: number;\n\texport let table_scrollbar_width: number;\n\texport let start = 0;\n\texport let end = 20;\n\texport let selected: number | false;\n\texport let disable_scroll = false;\n\texport let show_scroll_button = false;\n\texport let viewport: HTMLTableElement;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tscroll_top: number;\n\t}>();\n\n\tlet height = \"100%\";\n\n\tlet average_height = 30;\n\tlet bottom = 0;\n\tlet contents: HTMLTableSectionElement;\n\tlet head_height = 0;\n\tlet foot_height = 0;\n\tlet height_map: number[] = [];\n\tlet mounted: boolean;\n\tlet rows: HTMLCollectionOf<HTMLTableRowElement>;\n\tlet top = 0;\n\tlet viewport_height = 200;\n\tlet visible: { index: number; data: any[] }[] = [];\n\tlet viewport_box: DOMRectReadOnly;\n\n\t$: viewport_height = viewport_box?.height || 200;\n\n\tconst is_browser = typeof window !== \"undefined\";\n\tconst raf = is_browser\n\t\t? window.requestAnimationFrame\n\t\t: (cb: (...args: any[]) => void) => cb();\n\n\t$: {\n\t\tif (mounted && viewport_height && viewport.offsetParent) {\n\t\t\tsortedItems, raf(refresh_height_map);\n\t\t}\n\t}\n\n\tasync function refresh_height_map(): Promise<void> {\n\t\tif (sortedItems.length < start) {\n\t\t\tawait scroll_to_index(sortedItems.length - 1, { behavior: \"auto\" });\n\t\t}\n\n\t\tconst scrollTop = Math.max(0, viewport.scrollTop);\n\t\tshow_scroll_button = scrollTop > 100;\n\t\ttable_scrollbar_width = viewport.offsetWidth - viewport.clientWidth;\n\n\t\t// acquire height map for currently visible rows\n\t\tfor (let v = 0; v < rows.length; v += 1) {\n\t\t\theight_map[start + v] = rows[v].getBoundingClientRect().height;\n\t\t}\n\t\tlet i = 0;\n\t\tlet y = head_height;\n\t\t// loop items to find new start\n\t\twhile (i < sortedItems.length) {\n\t\t\tconst row_height = height_map[i] || average_height;\n\t\t\t// keep a page of rows buffered above\n\t\t\tif (y + row_height > scrollTop - max_height) {\n\t\t\t\tstart = i;\n\t\t\t\ttop = y - head_height;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\ty += row_height;\n\t\t\ti += 1;\n\t\t}\n\n\t\tlet content_height = head_height;\n\t\twhile (i < sortedItems.length) {\n\t\t\tconst row_height = height_map[i] || average_height;\n\t\t\tcontent_height += row_height;\n\t\t\ti += 1;\n\t\t\t// keep a page of rows buffered below\n\t\t\tif (content_height - head_height > 3 * max_height) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tend = i;\n\t\tconst remaining = sortedItems.length - end;\n\n\t\tconst scrollbar_height = viewport.offsetHeight - viewport.clientHeight;\n\t\tif (scrollbar_height > 0) {\n\t\t\tcontent_height += scrollbar_height;\n\t\t}\n\n\t\tlet filtered_height_map = height_map.filter((v) => typeof v === \"number\");\n\t\taverage_height =\n\t\t\tfiltered_height_map.reduce((a, b) => a + b, 0) /\n\t\t\t\tfiltered_height_map.length || 30;\n\n\t\tbottom = remaining * average_height;\n\t\tif (!isFinite(bottom)) {\n\t\t\tbottom = 200000;\n\t\t}\n\t\theight_map.length = sortedItems.length;\n\t\twhile (i < sortedItems.length) {\n\t\t\ti += 1;\n\t\t\theight_map[i] = average_height;\n\t\t}\n\t\tif (max_height && content_height > max_height) {\n\t\t\tactual_height = max_height;\n\t\t} else {\n\t\t\tactual_height = content_height;\n\t\t}\n\t}\n\n\t$: scroll_and_render(selected);\n\n\tasync function scroll_and_render(n: number | false): Promise<void> {\n\t\traf(async () => {\n\t\t\tif (typeof n !== \"number\") return;\n\t\t\tconst direction = typeof n !== \"number\" ? false : is_in_view(n);\n\t\t\tif (direction === true) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (direction === \"back\") {\n\t\t\t\tawait scroll_to_index(n, { behavior: \"instant\" });\n\t\t\t}\n\n\t\t\tif (direction === \"forwards\") {\n\t\t\t\tawait scroll_to_index(n, { behavior: \"instant\" }, true);\n\t\t\t}\n\t\t});\n\t}\n\n\tfunction is_in_view(n: number): \"back\" | \"forwards\" | true {\n\t\tconst current = rows && rows[n - start];\n\t\tif (!current && n < start) {\n\t\t\treturn \"back\";\n\t\t}\n\t\tif (!current && n >= end - 1) {\n\t\t\treturn \"forwards\";\n\t\t}\n\n\t\tconst { top: viewport_top } = viewport.getBoundingClientRect();\n\t\tconst { top, bottom } = current.getBoundingClientRect();\n\n\t\tif (top - viewport_top < 37) {\n\t\t\treturn \"back\";\n\t\t}\n\n\t\tif (bottom - viewport_top > viewport_height) {\n\t\t\treturn \"forwards\";\n\t\t}\n\n\t\treturn true;\n\t}\n\n\texport async function scroll_to_index(\n\t\tindex: number,\n\t\topts: ScrollToOptions,\n\t\talign_end = false\n\t): Promise<void> {\n\t\tawait tick();\n\n\t\tconst _itemHeight = average_height;\n\n\t\tlet distance = index * _itemHeight;\n\t\tif (align_end) {\n\t\t\tdistance = distance - viewport_height + _itemHeight + head_height;\n\t\t}\n\n\t\tconst scrollbar_height = viewport.offsetHeight - viewport.clientHeight;\n\t\tif (scrollbar_height > 0) {\n\t\t\tdistance += scrollbar_height;\n\t\t}\n\n\t\tconst _opts = {\n\t\t\ttop: distance,\n\t\t\tbehavior: \"smooth\" as ScrollBehavior,\n\t\t\t...opts\n\t\t};\n\n\t\tviewport.scrollTo(_opts);\n\t}\n\n\t$: sortedItems = items;\n\n\t$: visible = is_browser\n\t\t? sortedItems.slice(start, end).map((data, i) => {\n\t\t\t\treturn { index: i + start, data };\n\t\t\t})\n\t\t: sortedItems\n\t\t\t\t.slice(0, (max_height / sortedItems.length) * average_height + 1)\n\t\t\t\t.map((data, i) => {\n\t\t\t\t\treturn { index: i + start, data };\n\t\t\t\t});\n\n\tonMount(() => {\n\t\trows = contents.children as HTMLCollectionOf<HTMLTableRowElement>;\n\t\tmounted = true;\n\t});\n</script>\n\n<svelte-virtual-table-viewport>\n\t<div>\n\t\t<table\n\t\t\tclass=\"table\"\n\t\t\tclass:disable-scroll={disable_scroll}\n\t\t\tbind:this={viewport}\n\t\t\tbind:contentRect={viewport_box}\n\t\t\ton:scroll={refresh_height_map}\n\t\t\tstyle=\"height: {height}; --bw-svt-p-top: {top}px; --bw-svt-p-bottom: {bottom}px; --bw-svt-head-height: {head_height}px; --bw-svt-foot-height: {foot_height}px; --bw-svt-avg-row-height: {average_height}px; --max-height: {max_height}px\"\n\t\t>\n\t\t\t<thead class=\"thead\" bind:offsetHeight={head_height}>\n\t\t\t\t<slot name=\"thead\" />\n\t\t\t</thead>\n\t\t\t<tbody bind:this={contents} class=\"tbody\">\n\t\t\t\t{#if visible.length && visible[0].data.length}\n\t\t\t\t\t{#each visible as item (item.data[0].id)}\n\t\t\t\t\t\t<slot name=\"tbody\" item={item.data} index={item.index}>\n\t\t\t\t\t\t\tMissing Table Row\n\t\t\t\t\t\t</slot>\n\t\t\t\t\t{/each}\n\t\t\t\t{/if}\n\t\t\t</tbody>\n\t\t\t<tfoot class=\"tfoot\" bind:offsetHeight={foot_height}>\n\t\t\t\t<slot name=\"tfoot\" />\n\t\t\t</tfoot>\n\t\t</table>\n\t</div>\n</svelte-virtual-table-viewport>\n\n<style type=\"text/css\">\n\ttable {\n\t\tposition: relative;\n\t\toverflow: auto;\n\t\t-webkit-overflow-scrolling: touch;\n\t\tmax-height: var(--max-height);\n\t\tbox-sizing: border-box;\n\t\tdisplay: block;\n\t\tpadding: 0;\n\t\tmargin: 0;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-md);\n\t\tfont-family: var(--font-mono);\n\t\tborder-spacing: 0;\n\t\twidth: 100%;\n\t\tscroll-snap-type: x proximity;\n\t\tborder-collapse: separate;\n\t\tscrollbar-width: thin;\n\t\tscrollbar-color: rgba(128, 128, 128, 0.5) transparent;\n\t}\n\n\ttable::-webkit-scrollbar {\n\t\twidth: 4px;\n\t\theight: 4px;\n\t}\n\n\ttable::-webkit-scrollbar-track {\n\t\tbackground: transparent;\n\t}\n\n\ttable::-webkit-scrollbar-thumb {\n\t\tbackground-color: rgba(128, 128, 128, 0.5);\n\t\tborder-radius: 4px;\n\t}\n\n\ttable:hover {\n\t\tscrollbar-color: rgba(160, 160, 160, 0.7) transparent;\n\t}\n\n\ttable:hover::-webkit-scrollbar-thumb {\n\t\tbackground-color: rgba(160, 160, 160, 0.7);\n\t\tborder-radius: 4px;\n\t\twidth: 4px;\n\t}\n\n\t@media (hover: none) {\n\t\ttable {\n\t\t\tscrollbar-color: rgba(160, 160, 160, 0.7) transparent;\n\t\t}\n\n\t\ttable::-webkit-scrollbar-thumb {\n\t\t\tbackground-color: rgba(160, 160, 160, 0.7);\n\t\t\tborder-radius: 4px;\n\t\t}\n\t}\n\n\t@media (pointer: coarse) {\n\t\ttable::-webkit-scrollbar {\n\t\t\twidth: 8px;\n\t\t\theight: 8px;\n\t\t}\n\t}\n\n\ttable :is(thead, tfoot, tbody) {\n\t\tdisplay: table;\n\t\ttable-layout: fixed;\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t}\n\n\ttbody {\n\t\toverflow-x: scroll;\n\t\toverflow-y: hidden;\n\t}\n\n\ttable tbody {\n\t\tpadding-top: var(--bw-svt-p-top);\n\t\tpadding-bottom: var(--bw-svt-p-bottom);\n\t}\n\ttbody {\n\t\tposition: relative;\n\t\tbox-sizing: border-box;\n\t\tborder: 0px solid currentColor;\n\t}\n\n\ttbody > :global(tr:last-child) {\n\t\tborder: none;\n\t}\n\n\ttable :global(td) {\n\t\tscroll-snap-align: start;\n\t}\n\n\ttbody :global(td.pinned-column) {\n\t\tposition: sticky;\n\t\tz-index: 3;\n\t}\n\n\ttbody :global(tr:nth-child(odd)) :global(td.pinned-column) {\n\t\tbackground: var(--table-odd-background-fill);\n\t}\n\n\ttbody :global(tr:nth-child(even)) :global(td.pinned-column) {\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\ttbody :global(td.last-pinned) {\n\t\tborder-right: 1px solid var(--border-color-primary);\n\t}\n\n\tthead {\n\t\tposition: sticky;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tbackground: var(--background-fill-primary);\n\t\tz-index: 7;\n\t}\n\n\tthead :global(th) {\n\t\tbackground: var(--table-even-background-fill) !important;\n\t}\n\n\tthead :global(th.pinned-column) {\n\t\tposition: sticky;\n\t\tz-index: 7;\n\t\tbackground: var(--table-even-background-fill) !important;\n\t}\n\n\tthead :global(th.last-pinned) {\n\t\tborder-right: 1px solid var(--border-color-primary);\n\t}\n\n\t.table.disable-scroll {\n\t\toverflow: hidden !important;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport { Check } from \"@gradio/icons\";\n\timport DropdownArrow from \"../../icons/src/DropdownArrow.svelte\";\n\timport type { FilterDatatype } from \"./context/dataframe_context\";\n\n\texport let on_filter: (\n\t\tdatatype: FilterDatatype,\n\t\tselected_filter: string,\n\t\tvalue: string\n\t) => void = () => {};\n\n\tlet menu_element: HTMLDivElement;\n\tlet datatype: \"string\" | \"number\" = \"string\";\n\tlet current_filter = \"Contains\";\n\tlet filter_dropdown_open = false;\n\tlet filter_input_value = \"\";\n\n\tconst filter_options = {\n\t\tstring: [\n\t\t\t\"Contains\",\n\t\t\t\"Does not contain\",\n\t\t\t\"Starts with\",\n\t\t\t\"Ends with\",\n\t\t\t\"Is\",\n\t\t\t\"Is not\",\n\t\t\t\"Is empty\",\n\t\t\t\"Is not empty\"\n\t\t],\n\t\tnumber: [\"=\", \"≠\", \">\", \"<\", \"≥\", \"≤\", \"Is empty\", \"Is not empty\"]\n\t};\n\n\tonMount(() => {\n\t\tposition_menu();\n\t});\n\n\tfunction position_menu(): void {\n\t\tif (!menu_element) return;\n\n\t\tconst viewport_width = window.innerWidth;\n\t\tconst viewport_height = window.innerHeight;\n\t\tconst menu_rect = menu_element.getBoundingClientRect();\n\n\t\tconst x = (viewport_width - menu_rect.width) / 2;\n\t\tconst y = (viewport_height - menu_rect.height) / 2;\n\n\t\tmenu_element.style.left = `${x}px`;\n\t\tmenu_element.style.top = `${y}px`;\n\t}\n\n\tfunction handle_filter_input(e: Event): void {\n\t\tconst target = e.target as HTMLInputElement;\n\t\tfilter_input_value = target.value;\n\t}\n</script>\n\n<div>\n\t<div class=\"background\"></div>\n\t<div bind:this={menu_element} class=\"filter-menu\">\n\t\t<div class=\"filter-datatype-container\">\n\t\t\t<span>Filter as</span>\n\t\t\t<button\n\t\t\t\ton:click|stopPropagation={() => {\n\t\t\t\t\tdatatype = datatype === \"string\" ? \"number\" : \"string\";\n\t\t\t\t\tcurrent_filter = filter_options[datatype][0];\n\t\t\t\t}}\n\t\t\t\taria-label={`Change filter type. Filtering ${datatype}s`}\n\t\t\t>\n\t\t\t\t{datatype}\n\t\t\t</button>\n\t\t</div>\n\n\t\t<div class=\"input-container\">\n\t\t\t<div class=\"filter-dropdown\">\n\t\t\t\t<button\n\t\t\t\t\ton:click|stopPropagation={() =>\n\t\t\t\t\t\t(filter_dropdown_open = !filter_dropdown_open)}\n\t\t\t\t\taria-label={`Change filter. Using '${current_filter}'`}\n\t\t\t\t>\n\t\t\t\t\t{current_filter}\n\t\t\t\t\t<DropdownArrow />\n\t\t\t\t</button>\n\n\t\t\t\t{#if filter_dropdown_open}\n\t\t\t\t\t<div class=\"dropdown-filter-options\">\n\t\t\t\t\t\t{#each filter_options[datatype] as opt}\n\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\ton:click|stopPropagation={() => {\n\t\t\t\t\t\t\t\t\tcurrent_filter = opt;\n\t\t\t\t\t\t\t\t\tfilter_dropdown_open = !filter_dropdown_open;\n\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\tclass=\"filter-option\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{opt}\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t{/each}\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t</div>\n\n\t\t\t<input\n\t\t\t\ttype=\"text\"\n\t\t\t\tvalue={filter_input_value}\n\t\t\t\ton:click|stopPropagation\n\t\t\t\ton:input={handle_filter_input}\n\t\t\t\tplaceholder=\"Type a value\"\n\t\t\t\tclass=\"filter-input\"\n\t\t\t/>\n\t\t</div>\n\n\t\t<button\n\t\t\tclass=\"check-button\"\n\t\t\ton:click={() => on_filter(datatype, current_filter, filter_input_value)}\n\t\t>\n\t\t\t<Check />\n\t\t</button>\n\t</div>\n</div>\n\n<style>\n\t.background {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100vw;\n\t\theight: 100vh;\n\t\tbackground-color: rgba(0, 0, 0, 0.4);\n\t\tz-index: 20;\n\t}\n\n\t.filter-menu {\n\t\tposition: fixed;\n\t\tbackground: var(--background-fill-primary);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tpadding: var(--size-2);\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--size-2);\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\twidth: 300px;\n\t\tz-index: 21;\n\t}\n\n\t.filter-datatype-container {\n\t\tdisplay: flex;\n\t\tgap: var(--size-2);\n\t\talign-items: center;\n\t}\n\n\t.filter-menu span {\n\t\tfont-size: var(--text-sm);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.filter-menu button {\n\t\theight: var(--size-6);\n\t\tbackground: none;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--text-sm);\n\t\tcursor: pointer;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tgap: var(--size-2);\n\t}\n\n\t.filter-menu button:hover {\n\t\tbackground-color: var(--background-fill-secondary);\n\t}\n\n\t.filter-input {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-6);\n\t\tpadding: var(--size-2);\n\t\tpadding-right: var(--size-8);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--table-radius);\n\t\tfont-size: var(--text-sm);\n\t\tcolor: var(--body-text-color);\n\t\tbackground: var(--background-fill-secondary);\n\t\ttransition: all 0.2s ease;\n\t}\n\n\t.filter-input:hover {\n\t\tborder-color: var(--border-color-secondary);\n\t\tbackground: var(--background-fill-primary);\n\t}\n\n\t.filter-input:focus {\n\t\toutline: none;\n\t\tborder-color: var(--color-accent);\n\t\tbackground: var(--background-fill-primary);\n\t\tbox-shadow: 0 0 0 1px var(--color-accent);\n\t}\n\n\t.dropdown-filter-options {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tbackground: var(--background-fill-primary);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tbox-shadow: var(--shadow-drop-md);\n\t\tposition: absolute;\n\t\tz-index: var(--layer-1);\n\t}\n\n\t.dropdown-filter-options .filter-option {\n\t\tborder: none;\n\t\tjustify-content: flex-start;\n\t}\n\n\t.input-container {\n\t\tdisplay: flex;\n\t\tgap: var(--size-2);\n\t\talign-items: center;\n\t}\n\n\t.input-container button {\n\t\twidth: 130px;\n\t}\n\n\t:global(svg.dropdown-arrow) {\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t\tmargin-left: auto;\n\t}\n\n\t.filter-menu .check-button {\n\t\tbackground: var(--color-accent);\n\t\tcolor: white;\n\t\tborder: none;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-6);\n\t\tborder-radius: var(--radius-sm);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: var(--size-1);\n\t}\n\n\t.check-button:hover {\n\t\tbackground: var(--color-accent-soft);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport CellMenuIcons from \"./CellMenuIcons.svelte\";\n\timport FilterMenu from \"./FilterMenu.svelte\";\n\timport type { I18nFormatter } from \"js/utils/src\";\n\timport type {\n\t\tSortDirection,\n\t\tFilterDatatype\n\t} from \"./context/dataframe_context\";\n\n\texport let x: number;\n\texport let y: number;\n\texport let on_add_row_above: () => void;\n\texport let on_add_row_below: () => void;\n\texport let on_add_column_left: () => void;\n\texport let on_add_column_right: () => void;\n\texport let row: number;\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\texport let row_count: [number, \"fixed\" | \"dynamic\"];\n\texport let on_delete_row: () => void;\n\texport let on_delete_col: () => void;\n\texport let can_delete_rows: boolean;\n\texport let can_delete_cols: boolean;\n\texport let on_sort: (direction: SortDirection) => void = () => {};\n\texport let on_clear_sort: () => void = () => {};\n\texport let sort_direction: SortDirection | null = null;\n\texport let sort_priority: number | null = null;\n\texport let on_filter: (\n\t\tdatatype: FilterDatatype,\n\t\tselected_filter: string,\n\t\tvalue: string\n\t) => void = () => {};\n\texport let on_clear_filter: () => void = () => {};\n\texport let filter_active: boolean | null = null;\n\texport let editable = true;\n\n\texport let i18n: I18nFormatter;\n\tlet menu_element: HTMLDivElement;\n\tlet active_filter_menu: { x: number; y: number } | null = null;\n\n\t$: is_header = row === -1;\n\t$: can_add_rows = editable && row_count[1] === \"dynamic\";\n\t$: can_add_columns = editable && col_count[1] === \"dynamic\";\n\n\tonMount(() => {\n\t\tposition_menu();\n\t});\n\n\tfunction position_menu(): void {\n\t\tif (!menu_element) return;\n\n\t\tconst viewport_width = window.innerWidth;\n\t\tconst viewport_height = window.innerHeight;\n\t\tconst menu_rect = menu_element.getBoundingClientRect();\n\n\t\tlet new_x = x - 30;\n\t\tlet new_y = y - 20;\n\n\t\tif (new_x + menu_rect.width > viewport_width) {\n\t\t\tnew_x = x - menu_rect.width + 10;\n\t\t}\n\n\t\tif (new_y + menu_rect.height > viewport_height) {\n\t\t\tnew_y = y - menu_rect.height + 10;\n\t\t}\n\n\t\tmenu_element.style.left = `${new_x}px`;\n\t\tmenu_element.style.top = `${new_y}px`;\n\t}\n\n\tfunction toggle_filter_menu(): void {\n\t\tif (filter_active) {\n\t\t\ton_filter(\"string\", \"\", \"\");\n\t\t\treturn;\n\t\t}\n\n\t\tconst menu_rect = menu_element.getBoundingClientRect();\n\t\tactive_filter_menu = {\n\t\t\tx: menu_rect.right,\n\t\t\ty: menu_rect.top + menu_rect.height / 2\n\t\t};\n\t}\n</script>\n\n<div bind:this={menu_element} class=\"cell-menu\" role=\"menu\">\n\t{#if is_header}\n\t\t<button\n\t\t\trole=\"menuitem\"\n\t\t\ton:click={() => on_sort(\"asc\")}\n\t\t\tclass:active={sort_direction === \"asc\"}\n\t\t>\n\t\t\t<CellMenuIcons icon=\"sort-asc\" />\n\t\t\t{i18n(\"dataframe.sort_ascending\")}\n\t\t\t{#if sort_direction === \"asc\" && sort_priority !== null}\n\t\t\t\t<span class=\"priority\">{sort_priority}</span>\n\t\t\t{/if}\n\t\t</button>\n\t\t<button\n\t\t\trole=\"menuitem\"\n\t\t\ton:click={() => on_sort(\"desc\")}\n\t\t\tclass:active={sort_direction === \"desc\"}\n\t\t>\n\t\t\t<CellMenuIcons icon=\"sort-desc\" />\n\t\t\t{i18n(\"dataframe.sort_descending\")}\n\t\t\t{#if sort_direction === \"desc\" && sort_priority !== null}\n\t\t\t\t<span class=\"priority\">{sort_priority}</span>\n\t\t\t{/if}\n\t\t</button>\n\t\t<button role=\"menuitem\" on:click={on_clear_sort}>\n\t\t\t<CellMenuIcons icon=\"clear-sort\" />\n\t\t\t{i18n(\"dataframe.clear_sort\")}\n\t\t</button>\n\t\t<button\n\t\t\trole=\"menuitem\"\n\t\t\ton:click|stopPropagation={toggle_filter_menu}\n\t\t\tclass:active={filter_active || active_filter_menu}\n\t\t>\n\t\t\t<CellMenuIcons icon=\"filter\" />\n\t\t\t{i18n(\"dataframe.filter\")}\n\t\t\t{#if filter_active}\n\t\t\t\t<span class=\"priority\">1</span>\n\t\t\t{/if}\n\t\t</button>\n\t\t<button role=\"menuitem\" on:click={on_clear_filter}>\n\t\t\t<CellMenuIcons icon=\"clear-filter\" />\n\t\t\t{i18n(\"dataframe.clear_filter\")}\n\t\t</button>\n\t{/if}\n\n\t{#if !is_header && can_add_rows}\n\t\t<button\n\t\t\trole=\"menuitem\"\n\t\t\ton:click={() => on_add_row_above()}\n\t\t\taria-label=\"Add row above\"\n\t\t>\n\t\t\t<CellMenuIcons icon=\"add-row-above\" />\n\t\t\t{i18n(\"dataframe.add_row_above\")}\n\t\t</button>\n\t\t<button\n\t\t\trole=\"menuitem\"\n\t\t\ton:click={() => on_add_row_below()}\n\t\t\taria-label=\"Add row below\"\n\t\t>\n\t\t\t<CellMenuIcons icon=\"add-row-below\" />\n\t\t\t{i18n(\"dataframe.add_row_below\")}\n\t\t</button>\n\t\t{#if can_delete_rows}\n\t\t\t<button\n\t\t\t\trole=\"menuitem\"\n\t\t\t\ton:click={on_delete_row}\n\t\t\t\tclass=\"delete\"\n\t\t\t\taria-label=\"Delete row\"\n\t\t\t>\n\t\t\t\t<CellMenuIcons icon=\"delete-row\" />\n\t\t\t\t{i18n(\"dataframe.delete_row\")}\n\t\t\t</button>\n\t\t{/if}\n\t{/if}\n\t{#if can_add_columns}\n\t\t<button\n\t\t\trole=\"menuitem\"\n\t\t\ton:click={() => on_add_column_left()}\n\t\t\taria-label=\"Add column to the left\"\n\t\t>\n\t\t\t<CellMenuIcons icon=\"add-column-left\" />\n\t\t\t{i18n(\"dataframe.add_column_left\")}\n\t\t</button>\n\t\t<button\n\t\t\trole=\"menuitem\"\n\t\t\ton:click={() => on_add_column_right()}\n\t\t\taria-label=\"Add column to the right\"\n\t\t>\n\t\t\t<CellMenuIcons icon=\"add-column-right\" />\n\t\t\t{i18n(\"dataframe.add_column_right\")}\n\t\t</button>\n\t\t{#if can_delete_cols}\n\t\t\t<button\n\t\t\t\trole=\"menuitem\"\n\t\t\t\ton:click={on_delete_col}\n\t\t\t\tclass=\"delete\"\n\t\t\t\taria-label=\"Delete column\"\n\t\t\t>\n\t\t\t\t<CellMenuIcons icon=\"delete-column\" />\n\t\t\t\t{i18n(\"dataframe.delete_column\")}\n\t\t\t</button>\n\t\t{/if}\n\t{/if}\n</div>\n\n{#if active_filter_menu}\n\t<FilterMenu {on_filter} />\n{/if}\n\n<style>\n\t.cell-menu {\n\t\tposition: fixed;\n\t\tz-index: 9;\n\t\tbackground: var(--background-fill-primary);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--radius-sm);\n\t\tpadding: var(--size-1);\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--size-1);\n\t\tbox-shadow: var(--shadow-drop-lg);\n\t\tmin-width: 150px;\n\t\tz-index: var(--layer-1);\n\t}\n\n\t.cell-menu button {\n\t\tbackground: none;\n\t\tborder: none;\n\t\tcursor: pointer;\n\t\ttext-align: left;\n\t\tpadding: var(--size-1) var(--size-2);\n\t\tborder-radius: var(--radius-sm);\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--text-sm);\n\t\ttransition:\n\t\t\tbackground-color 0.2s,\n\t\t\tcolor 0.2s;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: var(--size-2);\n\t\tposition: relative;\n\t}\n\n\t.cell-menu button.active {\n\t\tbackground-color: var(--background-fill-secondary);\n\t}\n\n\t.cell-menu button:hover {\n\t\tbackground-color: var(--background-fill-secondary);\n\t}\n\n\t.cell-menu button :global(svg) {\n\t\tfill: currentColor;\n\t\ttransition: fill 0.2s;\n\t}\n\n\t.priority {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-left: auto;\n\t\tfont-size: var(--size-2);\n\t\tbackground-color: var(--button-secondary-background-fill);\n\t\tcolor: var(--body-text-color);\n\t\tborder-radius: var(--radius-sm);\n\t\twidth: var(--size-2-5);\n\t\theight: var(--size-2-5);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { Copy, Check } from \"@gradio/icons\";\n\timport { FullscreenButton } from \"@gradio/atoms\";\n\timport { onDestroy } from \"svelte\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\texport let show_fullscreen_button = false;\n\texport let show_copy_button = false;\n\texport let show_search: \"none\" | \"search\" | \"filter\" = \"none\";\n\texport let fullscreen = false;\n\texport let on_copy: () => Promise<void>;\n\texport let on_commit_filter: () => void;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tsearch: string | null;\n\t}>();\n\n\tlet copied = false;\n\tlet timer: ReturnType<typeof setTimeout>;\n\texport let current_search_query: string | null = null;\n\tlet input_value = \"\";\n\n\tfunction handle_search_input(e: Event): void {\n\t\tconst target = e.target as HTMLTextAreaElement;\n\t\tinput_value = target.value;\n\t\tconst new_query = input_value || null;\n\t\tif (current_search_query !== new_query) {\n\t\t\tcurrent_search_query = new_query;\n\t\t\tdispatch(\"search\", current_search_query);\n\t\t}\n\t}\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 2000);\n\t}\n\n\tasync function handle_copy(): Promise<void> {\n\t\tawait on_copy();\n\t\tcopy_feedback();\n\t}\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n<div class=\"toolbar\" role=\"toolbar\" aria-label=\"Table actions\">\n\t<div class=\"toolbar-buttons\">\n\t\t{#if show_search !== \"none\"}\n\t\t\t<div class=\"search-container\">\n\t\t\t\t<input\n\t\t\t\t\ttype=\"text\"\n\t\t\t\t\tvalue={current_search_query || \"\"}\n\t\t\t\t\ton:input={handle_search_input}\n\t\t\t\t\tplaceholder={show_search === \"filter\" ? \"Filter...\" : \"Search...\"}\n\t\t\t\t\tclass=\"search-input\"\n\t\t\t\t\tclass:filter-mode={show_search === \"filter\"}\n\t\t\t\t\ttitle={`Enter text to ${show_search} the table`}\n\t\t\t\t/>\n\t\t\t\t{#if current_search_query && show_search === \"filter\"}\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass=\"toolbar-button check-button\"\n\t\t\t\t\t\ton:click={on_commit_filter}\n\t\t\t\t\t\taria-label=\"Apply filter and update dataframe values\"\n\t\t\t\t\t\ttitle=\"Apply filter and update dataframe values\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<Check />\n\t\t\t\t\t</button>\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t{/if}\n\t\t{#if show_copy_button}\n\t\t\t<button\n\t\t\t\tclass=\"toolbar-button\"\n\t\t\t\ton:click={handle_copy}\n\t\t\t\taria-label={copied ? \"Copied to clipboard\" : \"Copy table data\"}\n\t\t\t\ttitle={copied ? \"Copied to clipboard\" : \"Copy table data\"}\n\t\t\t>\n\t\t\t\t{#if copied}\n\t\t\t\t\t<Check />\n\t\t\t\t{:else}\n\t\t\t\t\t<Copy />\n\t\t\t\t{/if}\n\t\t\t</button>\n\t\t{/if}\n\t\t{#if show_fullscreen_button}\n\t\t\t<FullscreenButton {fullscreen} on:fullscreen />\n\t\t{/if}\n\t</div>\n</div>\n\n<style>\n\t.toolbar {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: var(--size-2);\n\t\tflex: 0 0 auto;\n\t}\n\n\t.toolbar-buttons {\n\t\tdisplay: flex;\n\t\tgap: var(--size-1);\n\t\tflex-wrap: nowrap;\n\t}\n\n\t.toolbar-button {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\twidth: var(--size-6);\n\t\theight: var(--size-6);\n\t\tpadding: var(--size-1);\n\t\tborder: none;\n\t\tborder-radius: var(--radius-sm);\n\t\tbackground: transparent;\n\t\tcolor: var(--body-text-color-subdued);\n\t\tcursor: pointer;\n\t\ttransition: all 0.2s;\n\t}\n\n\t.toolbar-button:hover {\n\t\tbackground: var(--background-fill-secondary);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.toolbar-button :global(svg) {\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t}\n\n\t.search-container {\n\t\tposition: relative;\n\t}\n\n\t.search-input {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-6);\n\t\tpadding: var(--size-2);\n\t\tpadding-right: var(--size-8);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--table-radius);\n\t\tfont-size: var(--text-sm);\n\t\tcolor: var(--body-text-color);\n\t\tbackground: var(--background-fill-secondary);\n\t\ttransition: all 0.2s ease;\n\t}\n\n\t.search-input:hover {\n\t\tborder-color: var(--border-color-secondary);\n\t\tbackground: var(--background-fill-primary);\n\t}\n\n\t.search-input:focus {\n\t\toutline: none;\n\t\tborder-color: var(--color-accent);\n\t\tbackground: var(--background-fill-primary);\n\t\tbox-shadow: 0 0 0 1px var(--color-accent);\n\t}\n\n\t.check-button {\n\t\tposition: absolute;\n\t\tright: var(--size-1);\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\tbackground: var(--color-accent);\n\t\tcolor: white;\n\t\tborder: none;\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t\tborder-radius: var(--radius-sm);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: var(--size-1);\n\t}\n\n\t.check-button :global(svg) {\n\t\twidth: var(--size-3);\n\t\theight: var(--size-3);\n\t}\n\n\t.check-button:hover {\n\t\tbackground: var(--color-accent-soft);\n\t}\n</style>\n", "import type { Headers, HeadersWithIDs } from \"../utils\";\n\nexport function make_headers(\n\t_head: Headers,\n\tcol_count: [number, \"fixed\" | \"dynamic\"],\n\tels: Record<\n\t\tstring,\n\t\t{ cell: null | HTMLTableCellElement; input: null | HTMLTextAreaElement }\n\t>,\n\tmake_id: () => string\n): HeadersWithIDs {\n\tlet _h = _head || [];\n\tif (col_count[1] === \"fixed\" && _h.length < col_count[0]) {\n\t\tconst fill = Array(col_count[0] - _h.length)\n\t\t\t.fill(\"\")\n\t\t\t.map((_, i) => `${i + _h.length}`);\n\t\t_h = _h.concat(fill);\n\t}\n\n\tif (!_h || _h.length === 0) {\n\t\treturn Array(col_count[0])\n\t\t\t.fill(0)\n\t\t\t.map((_, i) => {\n\t\t\t\tconst _id = make_id();\n\t\t\t\tels[_id] = { cell: null, input: null };\n\t\t\t\treturn { id: _id, value: JSON.stringify(i + 1) };\n\t\t\t});\n\t}\n\n\treturn _h.map((h, i) => {\n\t\tconst _id = make_id();\n\t\tels[_id] = { cell: null, input: null };\n\t\treturn { id: _id, value: h ?? \"\" };\n\t});\n}\n\nexport function process_data(\n\tvalues: (string | number)[][],\n\tels: Record<\n\t\tstring,\n\t\t{ cell: null | HTMLTableCellElement; input: null | HTMLTextAreaElement }\n\t>,\n\tdata_binding: Record<string, any>,\n\tmake_id: () => string,\n\tdisplay_value: string[][] | null = null\n): { id: string; value: string | number; display_value?: string }[][] {\n\tif (!values || values.length === 0) {\n\t\treturn [];\n\t}\n\n\tconst result = values.map((row, i) => {\n\t\treturn row.map((value, j) => {\n\t\t\tconst _id = make_id();\n\t\t\tels[_id] = { cell: null, input: null };\n\t\t\tdata_binding[_id] = value;\n\n\t\t\tlet display = display_value?.[i]?.[j];\n\n\t\t\tif (display === undefined) {\n\t\t\t\tdisplay = String(value);\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\tid: _id,\n\t\t\t\tvalue,\n\t\t\t\tdisplay_value: display\n\t\t\t};\n\t\t});\n\t});\n\n\treturn result;\n}\n", "export type Headers = string[];\nexport type Data = (string | number)[][];\nexport type Datatype = \"str\" | \"markdown\" | \"html\" | \"number\" | \"bool\" | \"date\";\nexport type Metadata = {\n\t[key: string]: string[][] | null;\n} | null;\nexport type HeadersWithIDs = { value: string; id: string }[];\nexport type DataframeValue = {\n\tdata: Data;\n\theaders: Headers;\n\tmetadata: Metadata;\n};\n\n/**\n * Coerce a value to a given type.\n * @param v - The value to coerce.\n * @param t - The type to coerce to.\n * @returns The coerced value.\n */\nexport function cast_value_to_type(\n\tv: any,\n\tt: Datatype\n): string | number | boolean {\n\tif (t === \"number\") {\n\t\tconst n = Number(v);\n\t\treturn isNaN(n) ? v : n;\n\t}\n\tif (t === \"bool\") {\n\t\tif (typeof v === \"boolean\") return v;\n\t\tif (typeof v === \"number\") return v !== 0;\n\t\tconst s = String(v).toLowerCase();\n\t\tif (s === \"true\" || s === \"1\") return true;\n\t\tif (s === \"false\" || s === \"0\") return false;\n\t\treturn v;\n\t}\n\tif (t === \"date\") {\n\t\tconst d = new Date(v);\n\t\treturn isNaN(d.getTime()) ? v : d.toISOString();\n\t}\n\treturn v;\n}\n", "import { dequal } from \"dequal/lite\";\nimport { handle_delete_key } from \"../selection_utils\";\nimport type { DataFrameContext } from \"../context/dataframe_context\";\nimport { tick } from \"svelte\";\nimport { get } from \"svelte/store\";\nimport { copy_table_data } from \"./table_utils\";\n\nasync function save_cell_value(\n\tinput_value: string,\n\tctx: DataFrameContext,\n\trow: number,\n\tcol: number\n): Promise<void> {\n\tif (!ctx.data || !ctx.data[row] || !ctx.data[row][col]) return;\n\n\tconst old_value = ctx.data[row][col].value;\n\tctx.data[row][col].value = input_value;\n\n\tif (old_value !== input_value && ctx.dispatch) {\n\t\tctx.dispatch(\"change\", {\n\t\t\tdata: ctx.data.map((row) => row.map((cell) => cell.value)),\n\t\t\theaders: ctx.headers?.map((h) => h.value) || [],\n\t\t\tmetadata: null\n\t\t});\n\t}\n\n\tctx.actions.set_selected([row, col]);\n}\n\nexport async function handle_cell_blur(\n\tevent: FocusEvent,\n\tctx: DataFrameContext,\n\tcoords: [number, number]\n): Promise<void> {\n\tif (!ctx.data || !ctx.headers || !ctx.els) return;\n\n\tconst input_el = event.target as HTMLInputElement;\n\tif (!input_el || input_el.value === undefined) return;\n\n\tawait save_cell_value(\n\t\tinput_el.type === \"checkbox\" ? String(input_el.checked) : input_el.value,\n\t\tctx,\n\t\tcoords[0],\n\t\tcoords[1]\n\t);\n}\n\nfunction handle_header_navigation(\n\tevent: KeyboardEvent,\n\tctx: DataFrameContext\n): boolean {\n\tconst state = get(ctx.state);\n\tconst selected_header = state.ui_state.selected_header;\n\tconst header_edit = state.ui_state.header_edit;\n\tconst headers = ctx.headers || [];\n\n\tif (selected_header === false || header_edit !== false) return false;\n\n\tswitch (event.key) {\n\t\tcase \"ArrowDown\":\n\t\t\tctx.actions.set_selected_header(false);\n\t\t\tctx.actions.set_selected([0, selected_header as number]);\n\t\t\tctx.actions.set_selected_cells([[0, selected_header as number]]);\n\t\t\treturn true;\n\t\tcase \"ArrowLeft\":\n\t\t\tctx.actions.set_selected_header(\n\t\t\t\tselected_header > 0 ? selected_header - 1 : selected_header\n\t\t\t);\n\t\t\treturn true;\n\t\tcase \"ArrowRight\":\n\t\t\tctx.actions.set_selected_header(\n\t\t\t\tselected_header < headers.length - 1\n\t\t\t\t\t? selected_header + 1\n\t\t\t\t\t: selected_header\n\t\t\t);\n\t\t\treturn true;\n\t\tcase \"Escape\":\n\t\t\tevent.preventDefault();\n\t\t\tctx.actions.set_selected_header(false);\n\t\t\treturn true;\n\t\tcase \"Enter\":\n\t\t\tevent.preventDefault();\n\t\t\tif (state.config.editable) {\n\t\t\t\tctx.actions.set_header_edit(selected_header);\n\t\t\t}\n\t\t\treturn true;\n\t}\n\treturn false;\n}\n\n// eslint-disable-next-line complexity\nfunction handle_delete_operation(\n\tevent: KeyboardEvent,\n\tctx: DataFrameContext\n): boolean {\n\tif (!ctx.data || !ctx.headers || !ctx.els || !ctx.dispatch) return false;\n\n\tconst state = get(ctx.state);\n\tif (!state.config.editable) return false;\n\tif (event.key !== \"Delete\" && event.key !== \"Backspace\") return false;\n\n\tconst editing = state.ui_state.editing;\n\tconst selected_cells = state.ui_state.selected_cells;\n\n\tconst static_columns = state.config.static_columns || [];\n\tif (selected_cells.some(([_, col]) => static_columns.includes(col))) {\n\t\treturn false;\n\t}\n\n\tif (editing) {\n\t\tconst [row, col] = editing;\n\t\tconst input_el = ctx.els[ctx.data[row][col].id]?.input;\n\t\tif (input_el && input_el.selectionStart !== input_el.selectionEnd) {\n\t\t\treturn false;\n\t\t}\n\t\tif (\n\t\t\tevent.key === \"Delete\" &&\n\t\t\tinput_el?.selectionStart !== input_el?.value.length\n\t\t) {\n\t\t\treturn false;\n\t\t}\n\t\tif (event.key === \"Backspace\" && input_el?.selectionStart !== 0) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\tevent.preventDefault();\n\tif (selected_cells.length > 0) {\n\t\tconst new_data = handle_delete_key(ctx.data, selected_cells);\n\t\tctx.dispatch(\"change\", {\n\t\t\tdata: new_data.map((row) => row.map((cell) => cell.value)),\n\t\t\theaders: ctx.headers.map((h) => h.value),\n\t\t\tmetadata: null\n\t\t});\n\t}\n\treturn true;\n}\n\nfunction handle_arrow_keys(\n\tevent: KeyboardEvent,\n\tctx: DataFrameContext,\n\ti: number,\n\tj: number\n): boolean {\n\tconst state = get(ctx.state);\n\tconst editing = state.ui_state.editing;\n\tconst selected_cells = state.ui_state.selected_cells;\n\n\tif (editing) return false;\n\tif (!ctx.data) return false;\n\n\tevent.preventDefault();\n\n\tconst next_coords = ctx.actions.move_cursor(event, [i, j], ctx.data);\n\tif (next_coords) {\n\t\tif (event.shiftKey) {\n\t\t\tctx.actions.set_selected_cells(\n\t\t\t\tctx.actions.get_range_selection(\n\t\t\t\t\tselected_cells.length > 0 ? selected_cells[0] : [i, j],\n\t\t\t\t\tnext_coords\n\t\t\t\t)\n\t\t\t);\n\t\t\tctx.actions.set_editing(false);\n\t\t} else {\n\t\t\tctx.actions.set_selected_cells([next_coords]);\n\t\t\tctx.actions.set_editing(false);\n\t\t}\n\t\tctx.actions.set_selected(next_coords);\n\t} else if (next_coords === false && event.key === \"ArrowUp\" && i === 0) {\n\t\tctx.actions.set_selected_header(j);\n\t\tctx.actions.set_selected(false);\n\t\tctx.actions.set_selected_cells([]);\n\t\tctx.actions.set_editing(false);\n\t}\n\treturn true;\n}\n\nasync function handle_enter_key(\n\tevent: KeyboardEvent,\n\tctx: DataFrameContext,\n\ti: number,\n\tj: number\n): Promise<boolean> {\n\tif (!ctx.data || !ctx.els) return false;\n\n\tconst state = get(ctx.state);\n\tif (!state.config.editable) return false;\n\n\tconst editing = state.ui_state.editing;\n\tif (editing && event.shiftKey) return false;\n\n\tevent.preventDefault();\n\n\tif (editing && dequal(editing, [i, j])) {\n\t\tconst cell_id = ctx.data[i][j].id;\n\t\tconst input_el = ctx.els[cell_id]?.input;\n\t\tif (input_el) {\n\t\t\tawait save_cell_value(input_el.value, ctx, i, j);\n\t\t}\n\t\tctx.actions.set_editing(false);\n\t\tawait tick();\n\t\tctx.parent_element?.focus();\n\t} else {\n\t\tctx.actions.set_editing([i, j]);\n\t}\n\n\treturn true;\n}\n\nfunction handle_tab_key(\n\tevent: KeyboardEvent,\n\tctx: DataFrameContext,\n\ti: number,\n\tj: number\n): boolean {\n\tif (!ctx.data) return false;\n\n\tevent.preventDefault();\n\tctx.actions.set_editing(false);\n\tconst next_cell = ctx.actions.get_next_cell_coordinates(\n\t\t[i, j],\n\t\tctx.data,\n\t\tevent.shiftKey\n\t);\n\tif (next_cell) {\n\t\tctx.actions.set_selected_cells([next_cell]);\n\t\tctx.actions.set_selected(next_cell);\n\t\tif (get(ctx.state).config.editable) {\n\t\t\tctx.actions.set_editing(next_cell);\n\t\t}\n\t}\n\treturn true;\n}\n\nfunction handle_default_key(\n\tevent: KeyboardEvent,\n\tctx: DataFrameContext,\n\ti: number,\n\tj: number\n): boolean {\n\tconst state = get(ctx.state);\n\tif (!state.config.editable) return false;\n\n\tconst editing = state.ui_state.editing;\n\n\tif (\n\t\t(!editing || (editing && dequal(editing, [i, j]))) &&\n\t\tevent.key.length === 1\n\t) {\n\t\tctx.actions.set_editing([i, j]);\n\t\treturn true;\n\t}\n\treturn false;\n}\n\nasync function handle_cell_navigation(\n\tevent: KeyboardEvent,\n\tctx: DataFrameContext\n): Promise<boolean> {\n\tif (!ctx.data) return false;\n\n\tconst state = get(ctx.state);\n\tconst selected = state.ui_state.selected;\n\tconst selected_cells = state.ui_state.selected_cells;\n\n\tif (!selected) return false;\n\tif (event.key === \"c\" && (event.metaKey || event.ctrlKey)) {\n\t\tevent.preventDefault();\n\t\tif (selected_cells.length > 0) {\n\t\t\tawait copy_table_data(ctx.data, selected_cells);\n\t\t}\n\t\tctx.actions.set_copy_flash(true);\n\t\treturn true;\n\t}\n\n\tconst [i, j] = selected;\n\n\tswitch (event.key) {\n\t\tcase \"ArrowRight\":\n\t\tcase \"ArrowLeft\":\n\t\tcase \"ArrowDown\":\n\t\tcase \"ArrowUp\":\n\t\t\treturn handle_arrow_keys(event, ctx, i, j);\n\t\tcase \"Escape\":\n\t\t\tif (!state.config.editable) return false;\n\t\t\tevent.preventDefault();\n\t\t\tctx.actions.set_editing(false);\n\t\t\ttick().then(() => {\n\t\t\t\tif (ctx.parent_element) {\n\t\t\t\t\tctx.parent_element.focus();\n\t\t\t\t}\n\t\t\t});\n\n\t\t\treturn true;\n\t\tcase \"Enter\":\n\t\t\treturn await handle_enter_key(event, ctx, i, j);\n\t\tcase \"Tab\":\n\t\t\treturn handle_tab_key(event, ctx, i, j);\n\t\tcase \"Delete\":\n\t\tcase \"Backspace\":\n\t\t\treturn handle_delete_operation(event, ctx);\n\t\tdefault:\n\t\t\treturn handle_default_key(event, ctx, i, j);\n\t}\n}\n\nexport async function handle_keydown(\n\tevent: KeyboardEvent,\n\tcontext: DataFrameContext\n): Promise<void> {\n\tif (handle_header_navigation(event, context)) return;\n\tif (handle_delete_operation(event, context)) return;\n\tawait handle_cell_navigation(event, context);\n}\n", "import type { CellCoordinate } from \"../types\";\nimport { get_range_selection } from \"../selection_utils\";\n\nexport type DragState = {\n\tis_dragging: boolean;\n\tdrag_start: CellCoordinate | null;\n\tmouse_down_pos: { x: number; y: number } | null;\n};\n\nexport type DragHandlers = {\n\thandle_mouse_down: (event: MouseEvent, row: number, col: number) => void;\n\thandle_mouse_move: (event: <PERSON><PERSON>vent) => void;\n\thandle_mouse_up: (event: MouseEvent) => void;\n};\n\nexport function create_drag_handlers(\n\tstate: DragState,\n\tset_is_dragging: (value: boolean) => void,\n\tset_selected_cells: (cells: CellCoordinate[]) => void,\n\tset_selected: (cell: CellCoordinate | false) => void,\n\thandle_cell_click: (event: MouseEvent, row: number, col: number) => void,\n\tshow_row_numbers: boolean,\n\tparent_element?: HTMLElement\n): DragHandlers {\n\tconst start_drag = (event: MouseEvent, row: number, col: number): void => {\n\t\tconst target = event.target as HTMLElement;\n\t\tconst is_checkbox_click =\n\t\t\t(target as HTMLInputElement).type === \"checkbox\" ||\n\t\t\ttarget.closest('input[type=\"checkbox\"]') ||\n\t\t\ttarget.closest(\".bool-cell\");\n\n\t\tif (\n\t\t\tevent.target instanceof HTMLAnchorElement ||\n\t\t\t(show_row_numbers && col === -1) ||\n\t\t\tis_checkbox_click\n\t\t)\n\t\t\treturn;\n\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\n\t\tstate.mouse_down_pos = { x: event.clientX, y: event.clientY };\n\t\tstate.drag_start = [row, col];\n\n\t\tif (!event.shiftKey && !event.metaKey && !event.ctrlKey) {\n\t\t\tset_selected_cells([[row, col]]);\n\t\t\tset_selected([row, col]);\n\t\t\thandle_cell_click(event, row, col);\n\t\t}\n\t};\n\n\tconst update_selection = (event: MouseEvent): void => {\n\t\tconst cell = (event.target as HTMLElement).closest(\"td\");\n\t\tif (!cell) return;\n\n\t\tconst row = parseInt(cell.getAttribute(\"data-row\") || \"0\");\n\t\tconst col = parseInt(cell.getAttribute(\"data-col\") || \"0\");\n\n\t\tif (isNaN(row) || isNaN(col)) return;\n\n\t\tconst selection_range = get_range_selection(state.drag_start!, [row, col]);\n\t\tset_selected_cells(selection_range);\n\t\tset_selected([row, col]);\n\t};\n\n\tconst end_drag = (event: MouseEvent): void => {\n\t\tif (!state.is_dragging && state.drag_start) {\n\t\t\thandle_cell_click(event, state.drag_start[0], state.drag_start[1]);\n\t\t} else if (state.is_dragging && parent_element) {\n\t\t\tparent_element.focus();\n\t\t}\n\n\t\tstate.is_dragging = false;\n\t\tset_is_dragging(false);\n\t\tstate.drag_start = null;\n\t\tstate.mouse_down_pos = null;\n\t};\n\n\treturn {\n\t\thandle_mouse_down: start_drag,\n\n\t\thandle_mouse_move(event: MouseEvent): void {\n\t\t\tif (!state.drag_start || !state.mouse_down_pos) return;\n\n\t\t\tif (!(event.buttons & 1)) {\n\t\t\t\tend_drag(event);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst dx = Math.abs(event.clientX - state.mouse_down_pos.x);\n\t\t\tconst dy = Math.abs(event.clientY - state.mouse_down_pos.y);\n\n\t\t\tif (!state.is_dragging && (dx > 3 || dy > 3)) {\n\t\t\t\tstate.is_dragging = true;\n\t\t\t\tset_is_dragging(true);\n\t\t\t}\n\n\t\t\tif (state.is_dragging) {\n\t\t\t\tupdate_selection(event);\n\t\t\t}\n\t\t},\n\n\t\thandle_mouse_up: end_drag\n\t};\n}\n", "<script lang=\"ts\" context=\"module\">\n\timport {\n\t\tcreate_dataframe_context,\n\t\ttype SortDirection,\n\t\ttype FilterDatatype\n\t} from \"./context/dataframe_context\";\n</script>\n\n<script lang=\"ts\">\n\timport { afterUpdate, createEventDispatcher, tick, onMount } from \"svelte\";\n\timport { dequal } from \"dequal/lite\";\n\timport { Upload } from \"@gradio/upload\";\n\n\timport EditableCell from \"./EditableCell.svelte\";\n\timport RowN<PERSON>ber from \"./RowNumber.svelte\";\n\timport TableHeader from \"./TableHeader.svelte\";\n\timport TableCell from \"./TableCell.svelte\";\n\timport EmptyRowButton from \"./EmptyRowButton.svelte\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport { type Client } from \"@gradio/client\";\n\timport VirtualTable from \"./VirtualTable.svelte\";\n\timport type { Headers, DataframeValue, Datatype } from \"./utils\";\n\timport CellMenu from \"./CellMenu.svelte\";\n\timport Toolbar from \"./Toolbar.svelte\";\n\timport type { CellCoordinate } from \"./types\";\n\timport {\n\t\tis_cell_selected,\n\t\tshould_show_cell_menu,\n\t\tget_current_indices,\n\t\thandle_click_outside as handle_click_outside_util,\n\t\tcalculate_selection_positions\n\t} from \"./selection_utils\";\n\timport {\n\t\tcopy_table_data,\n\t\tget_max,\n\t\thandle_file_upload\n\t} from \"./utils/table_utils\";\n\timport { make_headers, process_data } from \"./utils/data_processing\";\n\timport { cast_value_to_type } from \"./utils\";\n\timport { handle_keydown, handle_cell_blur } from \"./utils/keyboard_utils\";\n\timport {\n\t\tcreate_drag_handlers,\n\t\ttype DragState,\n\t\ttype DragHandlers\n\t} from \"./utils/drag_utils\";\n\timport { sort_data_and_preserve_selection } from \"./utils/sort_utils\";\n\timport { filter_data_and_preserve_selection } from \"./utils/filter_utils\";\n\n\texport let datatype: Datatype | Datatype[];\n\texport let label: string | null = null;\n\texport let show_label = true;\n\texport let headers: Headers = [];\n\texport let values: (string | number)[][] = [];\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\texport let row_count: [number, \"fixed\" | \"dynamic\"];\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let components: Record<string, any> = {};\n\n\texport let editable = true;\n\texport let wrap = false;\n\texport let root: string;\n\texport let i18n: I18nFormatter;\n\n\texport let max_height = 500;\n\texport let line_breaks = true;\n\texport let column_widths: string[] = [];\n\texport let show_row_numbers = false;\n\texport let upload: Client[\"upload\"];\n\texport let stream_handler: Client[\"stream\"];\n\texport let show_fullscreen_button = false;\n\texport let show_copy_button = false;\n\texport let value_is_output = false;\n\texport let max_chars: number | undefined = undefined;\n\texport let show_search: \"none\" | \"search\" | \"filter\" = \"none\";\n\texport let pinned_columns = 0;\n\texport let static_columns: (string | number)[] = [];\n\texport let fullscreen = false;\n\n\tconst df_ctx = create_dataframe_context({\n\t\tshow_fullscreen_button,\n\t\tshow_copy_button,\n\t\tshow_search,\n\t\tshow_row_numbers,\n\t\teditable,\n\t\tpinned_columns,\n\t\tshow_label,\n\t\tline_breaks,\n\t\twrap,\n\t\tmax_height,\n\t\tcolumn_widths,\n\t\tmax_chars,\n\t\tstatic_columns\n\t});\n\n\tconst { state: df_state, actions: df_actions } = df_ctx;\n\n\t$: selected_cells = $df_state.ui_state.selected_cells;\n\t$: selected = $df_state.ui_state.selected;\n\t$: editing = $df_state.ui_state.editing;\n\t$: header_edit = $df_state.ui_state.header_edit;\n\t$: selected_header = $df_state.ui_state.selected_header;\n\t$: active_cell_menu = $df_state.ui_state.active_cell_menu;\n\t$: active_header_menu = $df_state.ui_state.active_header_menu;\n\t$: copy_flash = $df_state.ui_state.copy_flash;\n\n\t$: actual_pinned_columns =\n\t\tpinned_columns && data?.[0]?.length\n\t\t\t? Math.min(pinned_columns, data[0].length)\n\t\t\t: 0;\n\n\tonMount(() => {\n\t\tdf_ctx.parent_element = parent;\n\t\tdf_ctx.get_data_at = get_data_at;\n\t\tdf_ctx.get_column = get_column;\n\t\tdf_ctx.get_row = get_row;\n\t\tdf_ctx.dispatch = dispatch;\n\t\tinit_drag_handlers();\n\n\t\tconst observer = new IntersectionObserver((entries) => {\n\t\t\tentries.forEach((entry) => {\n\t\t\t\tif (entry.isIntersecting && !is_visible) {\n\t\t\t\t\twidth_calculated = false;\n\t\t\t\t}\n\t\t\t\tis_visible = entry.isIntersecting;\n\t\t\t});\n\t\t});\n\t\tobserver.observe(parent);\n\t\tdocument.addEventListener(\"click\", handle_click_outside);\n\t\twindow.addEventListener(\"resize\", handle_resize);\n\n\t\tconst global_mouse_up = (event: MouseEvent): void => {\n\t\t\tif (is_dragging || drag_start) {\n\t\t\t\thandle_mouse_up(event);\n\t\t\t}\n\t\t};\n\t\tdocument.addEventListener(\"mouseup\", global_mouse_up);\n\n\t\treturn () => {\n\t\t\tobserver.disconnect();\n\t\t\tdocument.removeEventListener(\"click\", handle_click_outside);\n\t\t\twindow.removeEventListener(\"resize\", handle_resize);\n\t\t\tdocument.removeEventListener(\"mouseup\", global_mouse_up);\n\t\t};\n\t});\n\n\t$: {\n\t\tif (data || _headers || els) {\n\t\t\tdf_ctx.data = data;\n\t\t\tdf_ctx.headers = _headers;\n\t\t\tdf_ctx.els = els;\n\t\t\tdf_ctx.display_value = display_value;\n\t\t\tdf_ctx.styling = styling;\n\t\t}\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: DataframeValue;\n\t\tinput: undefined;\n\t\tselect: SelectData;\n\t\tsearch: string | null;\n\t}>();\n\n\tlet els: Record<\n\t\tstring,\n\t\t{ cell: null | HTMLTableCellElement; input: null | HTMLTextAreaElement }\n\t> = {};\n\tlet data_binding: Record<string, (typeof data)[0][0]> = {};\n\tlet _headers = make_headers(headers, col_count, els, make_id);\n\tlet old_headers: string[] = headers;\n\tlet data: { id: string; value: string | number; display_value?: string }[][] =\n\t\t[[]];\n\tlet old_val: undefined | (string | number)[][] = undefined;\n\tlet search_results: {\n\t\tid: string;\n\t\tvalue: string | number;\n\t\tdisplay_value?: string;\n\t\tstyling?: string;\n\t}[][] = [[]];\n\tlet dragging = false;\n\tlet color_accent_copied: string;\n\tlet filtered_to_original_map: number[] = [];\n\n\tonMount(() => {\n\t\tconst color = getComputedStyle(document.documentElement)\n\t\t\t.getPropertyValue(\"--color-accent\")\n\t\t\t.trim();\n\t\tcolor_accent_copied = color + \"40\"; // 80 is 50% opacity in hex\n\t\tdocument.documentElement.style.setProperty(\n\t\t\t\"--color-accent-copied\",\n\t\t\tcolor_accent_copied\n\t\t);\n\t});\n\n\tconst get_data_at = (row: number, col: number): string | number =>\n\t\tdata?.[row]?.[col]?.value;\n\n\tconst get_column = (col: number): (string | number)[] =>\n\t\tdata?.map((row) => row[col]?.value) ?? [];\n\n\tconst get_row = (row: number): (string | number)[] =>\n\t\tdata?.[row]?.map((cell) => cell.value) ?? [];\n\n\t$: {\n\t\tif (!dequal(headers, old_headers)) {\n\t\t\t_headers = make_headers(headers, col_count, els, make_id);\n\t\t\told_headers = JSON.parse(JSON.stringify(headers));\n\t\t}\n\t}\n\n\tfunction make_id(): string {\n\t\treturn Math.random().toString(36).substring(2, 15);\n\t}\n\n\texport let display_value: string[][] | null = null;\n\texport let styling: string[][] | null = null;\n\n\t$: if (!dequal(values, old_val)) {\n\t\tif (parent) {\n\t\t\t// only clear column widths when the data structure changes\n\t\t\tconst is_reset =\n\t\t\t\tvalues.length === 0 || (values.length === 1 && values[0].length === 0);\n\t\t\tconst is_different_structure =\n\t\t\t\told_val !== undefined &&\n\t\t\t\t(values.length !== old_val.length ||\n\t\t\t\t\t(values[0] && old_val[0] && values[0].length !== old_val[0].length));\n\n\t\t\tif (is_reset || is_different_structure) {\n\t\t\t\tfor (let i = 0; i < 50; i++) {\n\t\t\t\t\tparent.style.removeProperty(`--cell-width-${i}`);\n\t\t\t\t}\n\t\t\t\tlast_width_data_length = 0;\n\t\t\t\tlast_width_column_count = 0;\n\t\t\t\twidth_calculated = false;\n\t\t\t}\n\t\t}\n\n\t\t// only reset sort state when values are changed\n\t\tconst is_reset =\n\t\t\tvalues.length === 0 || (values.length === 1 && values[0].length === 0);\n\t\tconst is_different_structure =\n\t\t\told_val !== undefined &&\n\t\t\t(values.length !== old_val.length ||\n\t\t\t\t(values[0] && old_val[0] && values[0].length !== old_val[0].length));\n\n\t\tdata = process_data(\n\t\t\tvalues as (string | number)[][],\n\t\t\tels,\n\t\t\tdata_binding,\n\t\t\tmake_id,\n\t\t\tdisplay_value\n\t\t);\n\t\told_val = JSON.parse(JSON.stringify(values)) as (string | number)[][];\n\n\t\tif (is_reset || is_different_structure) {\n\t\t\tdf_actions.reset_sort_state();\n\t\t} else if ($df_state.sort_state.sort_columns.length > 0) {\n\t\t\tsort_data(data, display_value, styling);\n\t\t} else {\n\t\t\tdf_actions.handle_sort(-1, \"asc\");\n\t\t\tdf_actions.reset_sort_state();\n\t\t}\n\n\t\tif ($df_state.filter_state.filter_columns.length > 0) {\n\t\t\tfilter_data(data, display_value, styling);\n\t\t} else {\n\t\t\tdf_actions.reset_filter_state();\n\t\t}\n\n\t\tif ($df_state.current_search_query) {\n\t\t\tdf_actions.handle_search(null);\n\t\t}\n\n\t\tif (parent && cells.length > 0 && (is_reset || is_different_structure)) {\n\t\t\twidth_calculated = false;\n\t\t}\n\t}\n\n\t$: if ($df_state.current_search_query !== undefined) {\n\t\tconst cell_map = new Map();\n\t\tfiltered_to_original_map = [];\n\n\t\tdata.forEach((row, row_idx) => {\n\t\t\tif (\n\t\t\t\trow.some((cell) =>\n\t\t\t\t\tString(cell?.value)\n\t\t\t\t\t\t.toLowerCase()\n\t\t\t\t\t\t.includes($df_state.current_search_query?.toLowerCase() || \"\")\n\t\t\t\t)\n\t\t\t) {\n\t\t\t\tfiltered_to_original_map.push(row_idx);\n\t\t\t}\n\t\t\trow.forEach((cell, col_idx) => {\n\t\t\t\tcell_map.set(cell.id, {\n\t\t\t\t\tvalue: cell.value,\n\t\t\t\t\tdisplay_value:\n\t\t\t\t\t\tcell.display_value !== undefined\n\t\t\t\t\t\t\t? cell.display_value\n\t\t\t\t\t\t\t: String(cell.value),\n\t\t\t\t\tstyling: styling?.[row_idx]?.[col_idx] || \"\"\n\t\t\t\t});\n\t\t\t});\n\t\t});\n\n\t\tconst filtered = df_actions.filter_data(data);\n\n\t\tsearch_results = filtered.map((row) =>\n\t\t\trow.map((cell) => {\n\t\t\t\tconst original = cell_map.get(cell.id);\n\t\t\t\treturn {\n\t\t\t\t\t...cell,\n\t\t\t\t\tdisplay_value:\n\t\t\t\t\t\toriginal?.display_value !== undefined\n\t\t\t\t\t\t\t? original.display_value\n\t\t\t\t\t\t\t: String(cell.value),\n\t\t\t\t\tstyling: original?.styling || \"\"\n\t\t\t\t};\n\t\t\t})\n\t\t);\n\t} else {\n\t\tfiltered_to_original_map = [];\n\t}\n\n\tlet previous_headers = _headers.map((h) => h.value);\n\tlet previous_data = data.map((row) => row.map((cell) => String(cell.value)));\n\n\t$: {\n\t\tif (data || _headers) {\n\t\t\tdf_actions.trigger_change(\n\t\t\t\tdata.map((row, rowIdx) =>\n\t\t\t\t\trow.map((cell, colIdx) => {\n\t\t\t\t\t\tconst dtype = Array.isArray(datatype) ? datatype[colIdx] : datatype;\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t...cell,\n\t\t\t\t\t\t\tvalue: cast_value_to_type(cell.value, dtype)\n\t\t\t\t\t\t};\n\t\t\t\t\t})\n\t\t\t\t),\n\t\t\t\t_headers,\n\t\t\t\tprevious_data,\n\t\t\t\tprevious_headers,\n\t\t\t\tvalue_is_output,\n\t\t\t\tdispatch\n\t\t\t);\n\t\t\tprevious_data = data.map((row) => row.map((cell) => String(cell.value)));\n\t\t\tprevious_headers = _headers.map((h) => h.value);\n\t\t}\n\t}\n\n\tfunction handle_sort(col: number, direction: SortDirection): void {\n\t\tdf_actions.handle_sort(col, direction);\n\t\tsort_data(data, display_value, styling);\n\t}\n\n\tfunction clear_sort(): void {\n\t\tdf_actions.reset_sort_state();\n\t\tsort_data(data, display_value, styling);\n\t}\n\n\t$: {\n\t\tif ($df_state.filter_state.filter_columns.length > 0) {\n\t\t\tfilter_data(data, display_value, styling);\n\t\t}\n\n\t\tif ($df_state.sort_state.sort_columns.length > 0) {\n\t\t\tsort_data(data, display_value, styling);\n\t\t\tdf_actions.update_row_order(data);\n\t\t}\n\t}\n\n\tfunction handle_filter(\n\t\tcol: number,\n\t\tdatatype: FilterDatatype,\n\t\tfilter: string,\n\t\tvalue: string\n\t): void {\n\t\tdf_actions.handle_filter(col, datatype, filter, value);\n\t\tfilter_data(data, display_value, styling);\n\t}\n\n\tfunction clear_filter(): void {\n\t\tdf_actions.reset_filter_state();\n\t\tfilter_data(data, display_value, styling);\n\t}\n\n\tasync function edit_header(i: number, _select = false): Promise<void> {\n\t\tif (!editable || header_edit === i || col_count[1] !== \"dynamic\") return;\n\t\tdf_actions.set_header_edit(i);\n\t}\n\n\tfunction handle_header_click(event: MouseEvent, col: number): void {\n\t\tif (event.target instanceof HTMLAnchorElement) {\n\t\t\treturn;\n\t\t}\n\t\tevent.preventDefault();\n\t\tevent.stopPropagation();\n\t\tif (!editable) return;\n\t\tdf_actions.set_editing(false);\n\t\tdf_actions.handle_header_click(col, editable);\n\t\tparent.focus();\n\t}\n\n\tfunction end_header_edit(event: CustomEvent<KeyboardEvent>): void {\n\t\tif (!editable) return;\n\t\tdf_actions.end_header_edit(event.detail.key);\n\t\tparent.focus();\n\t}\n\n\tasync function add_row(index?: number): Promise<void> {\n\t\tparent.focus();\n\n\t\tif (row_count[1] !== \"dynamic\") return;\n\n\t\tconst new_row = Array(data[0]?.length || headers.length)\n\t\t\t.fill(0)\n\t\t\t.map((_, i) => {\n\t\t\t\tconst _id = make_id();\n\t\t\t\tels[_id] = { cell: null, input: null };\n\t\t\t\treturn { id: _id, value: \"\" };\n\t\t\t});\n\n\t\tif (data.length === 0) {\n\t\t\tdata = [new_row];\n\t\t} else if (index !== undefined && index >= 0 && index <= data.length) {\n\t\t\tdata.splice(index, 0, new_row);\n\t\t} else {\n\t\t\tdata.push(new_row);\n\t\t}\n\n\t\tselected = [index !== undefined ? index : data.length - 1, 0];\n\t}\n\n\tasync function add_col(index?: number): Promise<void> {\n\t\tparent.focus();\n\t\tif (col_count[1] !== \"dynamic\") return;\n\n\t\tconst result = df_actions.add_col(data, headers, make_id, index);\n\n\t\tresult.data.forEach((row) => {\n\t\t\trow.forEach((cell) => {\n\t\t\t\tif (!els[cell.id]) {\n\t\t\t\t\tels[cell.id] = { cell: null, input: null };\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\n\t\tdata = result.data;\n\t\theaders = result.headers;\n\n\t\tawait tick();\n\n\t\trequestAnimationFrame(() => {\n\t\t\tedit_header(index !== undefined ? index : data[0].length - 1, true);\n\t\t\tconst new_w = parent.querySelectorAll(\"tbody\")[1].offsetWidth;\n\t\t\tparent.querySelectorAll(\"table\")[1].scrollTo({ left: new_w });\n\t\t});\n\t}\n\n\tfunction handle_click_outside(event: Event): void {\n\t\tif (handle_click_outside_util(event, parent)) {\n\t\t\tdf_actions.clear_ui_state();\n\t\t\theader_edit = false;\n\t\t\tselected_header = false;\n\t\t}\n\t}\n\n\t$: max = get_max(data);\n\n\tlet width_calc_timeout: ReturnType<typeof setTimeout>;\n\t$: if (cells[0] && cells[0]?.clientWidth) {\n\t\tclearTimeout(width_calc_timeout);\n\t\twidth_calc_timeout = setTimeout(() => set_cell_widths(), 100);\n\t}\n\n\tlet width_calculated = false;\n\t$: if (cells[0] && !width_calculated) {\n\t\tset_cell_widths();\n\t\twidth_calculated = true;\n\t}\n\tlet cells: HTMLTableCellElement[] = [];\n\tlet parent: HTMLDivElement;\n\tlet table: HTMLTableElement;\n\tlet last_width_data_length = 0;\n\tlet last_width_column_count = 0;\n\n\tfunction set_cell_widths(): void {\n\t\tconst column_count = data[0]?.length || 0;\n\t\tif ($df_state.filter_state.filter_columns.length > 0) {\n\t\t\treturn;\n\t\t}\n\t\tif (\n\t\t\tlast_width_data_length === data.length &&\n\t\t\tlast_width_column_count === column_count &&\n\t\t\t$df_state.sort_state.sort_columns.length > 0\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\tlast_width_data_length = data.length;\n\t\tlast_width_column_count = column_count;\n\n\t\tconst widths = cells.map((el) => el?.clientWidth || 0);\n\t\tif (widths.length === 0) return;\n\n\t\tif (show_row_numbers) {\n\t\t\tparent.style.setProperty(`--cell-width-row-number`, `${widths[0]}px`);\n\t\t}\n\n\t\tfor (let i = 0; i < 50; i++) {\n\t\t\tif (!column_widths[i]) {\n\t\t\t\tparent.style.removeProperty(`--cell-width-${i}`);\n\t\t\t} else if (column_widths[i].endsWith(\"%\")) {\n\t\t\t\tconst percentage = parseFloat(column_widths[i]);\n\t\t\t\tconst pixel_width = Math.floor((percentage / 100) * parent.clientWidth);\n\t\t\t\tparent.style.setProperty(`--cell-width-${i}`, `${pixel_width}px`);\n\t\t\t} else {\n\t\t\t\tparent.style.setProperty(`--cell-width-${i}`, column_widths[i]);\n\t\t\t}\n\t\t}\n\n\t\twidths.forEach((width, i) => {\n\t\t\tif (!column_widths[i]) {\n\t\t\t\tconst calculated_width = `${Math.max(width, 45)}px`;\n\t\t\t\tparent.style.setProperty(`--cell-width-${i}`, calculated_width);\n\t\t\t}\n\t\t});\n\t}\n\n\tfunction get_cell_width(index: number): string {\n\t\treturn `var(--cell-width-${index})`;\n\t}\n\n\tlet table_height: number =\n\t\tvalues.slice(0, (max_height / values.length) * 37).length * 37 + 37;\n\tlet scrollbar_width = 0;\n\n\tfunction sort_data(\n\t\t_data: typeof data,\n\t\t_display_value: string[][] | null,\n\t\t_styling: string[][] | null\n\t): void {\n\t\tconst result = sort_data_and_preserve_selection(\n\t\t\t_data,\n\t\t\t_display_value,\n\t\t\t_styling,\n\t\t\t$df_state.sort_state.sort_columns,\n\t\t\tselected,\n\t\t\tget_current_indices\n\t\t);\n\n\t\tdata = result.data;\n\t\tselected = result.selected;\n\t}\n\n\tfunction filter_data(\n\t\t_data: typeof data,\n\t\t_display_value: string[][] | null,\n\t\t_styling: string[][] | null\n\t): void {\n\t\tconst result = filter_data_and_preserve_selection(\n\t\t\t_data,\n\t\t\t_display_value,\n\t\t\t_styling,\n\t\t\t$df_state.filter_state.filter_columns,\n\t\t\tselected,\n\t\t\tget_current_indices,\n\t\t\t$df_state.filter_state.initial_data?.data,\n\t\t\t$df_state.filter_state.initial_data?.display_value,\n\t\t\t$df_state.filter_state.initial_data?.styling\n\t\t);\n\t\tdata = result.data;\n\t\tselected = result.selected;\n\t}\n\n\t$: selected_index = !!selected && selected[0];\n\n\tlet is_visible = false;\n\n\tconst set_copy_flash = (value: boolean): void => {\n\t\tdf_actions.set_copy_flash(value);\n\t\tif (value) {\n\t\t\tsetTimeout(() => df_actions.set_copy_flash(false), 800);\n\t\t}\n\t};\n\n\tlet previous_selected_cells: [number, number][] = [];\n\n\t$: {\n\t\tif (copy_flash && !dequal(selected_cells, previous_selected_cells)) {\n\t\t\tset_copy_flash(false);\n\t\t}\n\t\tprevious_selected_cells = selected_cells;\n\t}\n\n\tfunction handle_blur(\n\t\tevent: CustomEvent<{\n\t\t\tblur_event: FocusEvent;\n\t\t\tcoords: [number, number];\n\t\t}>\n\t): void {\n\t\tconst { blur_event, coords } = event.detail;\n\t\thandle_cell_blur(blur_event, df_ctx, coords);\n\t}\n\n\tfunction toggle_header_menu(event: MouseEvent, col: number): void {\n\t\tevent.stopPropagation();\n\t\tif (active_header_menu && active_header_menu.col === col) {\n\t\t\tdf_actions.set_active_header_menu(null);\n\t\t} else {\n\t\t\tconst header = (event.target as HTMLElement).closest(\"th\");\n\t\t\tif (header) {\n\t\t\t\tconst rect = header.getBoundingClientRect();\n\t\t\t\tdf_actions.set_active_header_menu({\n\t\t\t\t\tcol,\n\t\t\t\t\tx: rect.right,\n\t\t\t\t\ty: rect.bottom\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\n\tfunction delete_col_at(index: number): void {\n\t\tif (col_count[1] !== \"dynamic\") return;\n\t\tif (data[0].length <= 1) return;\n\n\t\tconst result = df_actions.delete_col_at(data, headers, index);\n\t\tdata = result.data;\n\t\theaders = result.headers;\n\t\t_headers = make_headers(headers, col_count, els, make_id);\n\t\tdf_actions.set_active_cell_menu(null);\n\t\tdf_actions.set_active_header_menu(null);\n\t\tdf_actions.set_selected(false);\n\t\tdf_actions.set_selected_cells([]);\n\t\tdf_actions.set_editing(false);\n\t}\n\n\tfunction delete_row_at(index: number): void {\n\t\tdata = df_actions.delete_row_at(data, index);\n\t\tdf_actions.set_active_cell_menu(null);\n\t\tdf_actions.set_active_header_menu(null);\n\t}\n\n\tlet selected_cell_coords: CellCoordinate;\n\t$: if (selected !== false) selected_cell_coords = selected;\n\n\t$: if (selected !== false) {\n\t\tconst positions = calculate_selection_positions(\n\t\t\tselected,\n\t\t\tdata,\n\t\t\tels,\n\t\t\tparent,\n\t\t\ttable\n\t\t);\n\t\tdocument.documentElement.style.setProperty(\n\t\t\t\"--selected-col-pos\",\n\t\t\tpositions.col_pos\n\t\t);\n\t\tdocument.documentElement.style.setProperty(\n\t\t\t\"--selected-row-pos\",\n\t\t\tpositions.row_pos || \"0px\"\n\t\t);\n\t}\n\n\tfunction commit_filter(): void {\n\t\tif ($df_state.current_search_query && show_search === \"filter\") {\n\t\t\tconst filtered_data: (string | number)[][] = [];\n\t\t\tconst filtered_display_values: string[][] = [];\n\t\t\tconst filtered_styling: string[][] = [];\n\n\t\t\tsearch_results.forEach((row) => {\n\t\t\t\tconst data_row: (string | number)[] = [];\n\t\t\t\tconst display_row: string[] = [];\n\t\t\t\tconst styling_row: string[] = [];\n\n\t\t\t\trow.forEach((cell) => {\n\t\t\t\t\tdata_row.push(cell.value);\n\t\t\t\t\tdisplay_row.push(\n\t\t\t\t\t\tcell.display_value !== undefined\n\t\t\t\t\t\t\t? cell.display_value\n\t\t\t\t\t\t\t: String(cell.value)\n\t\t\t\t\t);\n\t\t\t\t\tstyling_row.push(cell.styling || \"\");\n\t\t\t\t});\n\n\t\t\t\tfiltered_data.push(data_row);\n\t\t\t\tfiltered_display_values.push(display_row);\n\t\t\t\tfiltered_styling.push(styling_row);\n\t\t\t});\n\n\t\t\tconst change_payload = {\n\t\t\t\tdata: filtered_data,\n\t\t\t\theaders: _headers.map((h) => h.value),\n\t\t\t\tmetadata: {\n\t\t\t\t\tdisplay_value: filtered_display_values,\n\t\t\t\t\tstyling: filtered_styling\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tdispatch(\"change\", change_payload);\n\n\t\t\tif (!value_is_output) {\n\t\t\t\tdispatch(\"input\");\n\t\t\t}\n\n\t\t\tdf_actions.handle_search(null);\n\t\t}\n\t}\n\n\tlet viewport: HTMLTableElement;\n\tlet show_scroll_button = false;\n\n\tfunction scroll_to_top(): void {\n\t\tviewport.scrollTo({\n\t\t\ttop: 0\n\t\t});\n\t}\n\n\tfunction handle_resize(): void {\n\t\tdf_actions.set_active_cell_menu(null);\n\t\tdf_actions.set_active_header_menu(null);\n\t\tselected_cells = [];\n\t\tselected = false;\n\t\tediting = false;\n\t\twidth_calculated = false;\n\t\tset_cell_widths();\n\t}\n\n\tfunction add_row_at(index: number, position: \"above\" | \"below\"): void {\n\t\tconst row_index = position === \"above\" ? index : index + 1;\n\t\tadd_row(row_index);\n\t\tactive_cell_menu = null;\n\t\tactive_header_menu = null;\n\t}\n\n\tfunction add_col_at(index: number, position: \"left\" | \"right\"): void {\n\t\tconst col_index = position === \"left\" ? index : index + 1;\n\t\tadd_col(col_index);\n\t\tactive_cell_menu = null;\n\t\tactive_header_menu = null;\n\t}\n\n\texport function reset_sort_state(): void {\n\t\tdf_actions.reset_sort_state();\n\t}\n\n\tlet is_dragging = false;\n\tlet drag_start: [number, number] | null = null;\n\tlet mouse_down_pos: { x: number; y: number } | null = null;\n\n\tconst drag_state: DragState = {\n\t\tis_dragging,\n\t\tdrag_start,\n\t\tmouse_down_pos\n\t};\n\n\t$: {\n\t\tis_dragging = drag_state.is_dragging;\n\t\tdrag_start = drag_state.drag_start;\n\t\tmouse_down_pos = drag_state.mouse_down_pos;\n\t}\n\n\tlet drag_handlers: DragHandlers;\n\n\tfunction init_drag_handlers(): void {\n\t\tdrag_handlers = create_drag_handlers(\n\t\t\tdrag_state,\n\t\t\t(value) => (is_dragging = value),\n\t\t\t(cells) => df_actions.set_selected_cells(cells),\n\t\t\t(cell) => df_actions.set_selected(cell),\n\t\t\t(event, row, col) => df_actions.handle_cell_click(event, row, col),\n\t\t\tshow_row_numbers,\n\t\t\tparent\n\t\t);\n\t}\n\n\t$: if (parent) init_drag_handlers();\n\n\t$: handle_mouse_down = drag_handlers?.handle_mouse_down || (() => {});\n\t$: handle_mouse_move = drag_handlers?.handle_mouse_move || (() => {});\n\t$: handle_mouse_up = drag_handlers?.handle_mouse_up || (() => {});\n\n\tfunction get_cell_display_value(row: number, col: number): string {\n\t\tconst is_search_active = $df_state.current_search_query !== undefined;\n\n\t\tif (is_search_active && search_results?.[row]?.[col]) {\n\t\t\treturn search_results[row][col].display_value !== undefined\n\t\t\t\t? search_results[row][col].display_value\n\t\t\t\t: String(search_results[row][col].value);\n\t\t}\n\n\t\tif (data?.[row]?.[col]) {\n\t\t\treturn data[row][col].display_value !== undefined\n\t\t\t\t? data[row][col].display_value\n\t\t\t\t: String(data[row][col].value);\n\t\t}\n\n\t\treturn \"\";\n\t}\n</script>\n\n<svelte:window on:resize={() => set_cell_widths()} />\n\n<div class=\"table-container\">\n\t{#if (label && label.length !== 0 && show_label) || show_fullscreen_button || show_copy_button || show_search !== \"none\"}\n\t\t<div class=\"header-row\">\n\t\t\t{#if label && label.length !== 0 && show_label}\n\t\t\t\t<div class=\"label\">\n\t\t\t\t\t<p>{label}</p>\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t\t<Toolbar\n\t\t\t\t{show_fullscreen_button}\n\t\t\t\t{fullscreen}\n\t\t\t\ton_copy={async () => await copy_table_data(data, null)}\n\t\t\t\t{show_copy_button}\n\t\t\t\t{show_search}\n\t\t\t\ton:search={(e) => df_actions.handle_search(e.detail)}\n\t\t\t\ton:fullscreen\n\t\t\t\ton_commit_filter={commit_filter}\n\t\t\t\tcurrent_search_query={$df_state.current_search_query}\n\t\t\t/>\n\t\t</div>\n\t{/if}\n\t<div\n\t\tbind:this={parent}\n\t\tclass=\"table-wrap\"\n\t\tclass:dragging={is_dragging}\n\t\tclass:no-wrap={!wrap}\n\t\tstyle=\"height:{table_height}px;\"\n\t\tclass:menu-open={active_cell_menu || active_header_menu}\n\t\ton:keydown={(e) => handle_keydown(e, df_ctx)}\n\t\ton:mousemove={handle_mouse_move}\n\t\ton:mouseup={handle_mouse_up}\n\t\ton:mouseleave={handle_mouse_up}\n\t\trole=\"grid\"\n\t\ttabindex=\"0\"\n\t>\n\t\t<table bind:this={table} aria-hidden=\"true\">\n\t\t\t{#if label && label.length !== 0}\n\t\t\t\t<caption class=\"sr-only\">{label}</caption>\n\t\t\t{/if}\n\t\t\t<thead>\n\t\t\t\t<tr>\n\t\t\t\t\t{#if show_row_numbers}\n\t\t\t\t\t\t<RowNumber is_header={true} />\n\t\t\t\t\t{/if}\n\t\t\t\t\t{#each _headers as { value, id }, i (id)}\n\t\t\t\t\t\t<TableHeader\n\t\t\t\t\t\t\tbind:value={_headers[i].value}\n\t\t\t\t\t\t\t{i}\n\t\t\t\t\t\t\t{actual_pinned_columns}\n\t\t\t\t\t\t\t{header_edit}\n\t\t\t\t\t\t\t{selected_header}\n\t\t\t\t\t\t\t{headers}\n\t\t\t\t\t\t\t{get_cell_width}\n\t\t\t\t\t\t\t{handle_header_click}\n\t\t\t\t\t\t\t{toggle_header_menu}\n\t\t\t\t\t\t\t{end_header_edit}\n\t\t\t\t\t\t\tsort_columns={$df_state.sort_state.sort_columns}\n\t\t\t\t\t\t\tfilter_columns={$df_state.filter_state.filter_columns}\n\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t{max_chars}\n\t\t\t\t\t\t\t{editable}\n\t\t\t\t\t\t\tis_static={static_columns.includes(i)}\n\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\tbind:el={els[id].input}\n\t\t\t\t\t\t\t{col_count}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/each}\n\t\t\t\t</tr>\n\t\t\t</thead>\n\t\t\t<tbody>\n\t\t\t\t<tr>\n\t\t\t\t\t{#if show_row_numbers}\n\t\t\t\t\t\t<RowNumber index={0} />\n\t\t\t\t\t{/if}\n\t\t\t\t\t{#each max as { value, id }, j (id)}\n\t\t\t\t\t\t<td tabindex=\"-1\" bind:this={cells[j]}>\n\t\t\t\t\t\t\t<div class=\"cell-wrap\">\n\t\t\t\t\t\t\t\t<EditableCell\n\t\t\t\t\t\t\t\t\t{value}\n\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\tdatatype={Array.isArray(datatype) ? datatype[j] : datatype}\n\t\t\t\t\t\t\t\t\tedit={false}\n\t\t\t\t\t\t\t\t\tel={null}\n\t\t\t\t\t\t\t\t\t{editable}\n\t\t\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\t\t\tshow_selection_buttons={selected_cells.length === 1 &&\n\t\t\t\t\t\t\t\t\t\tselected_cells[0][0] === 0 &&\n\t\t\t\t\t\t\t\t\t\tselected_cells[0][1] === j}\n\t\t\t\t\t\t\t\t\tcoords={selected_cell_coords}\n\t\t\t\t\t\t\t\t\ton_select_column={df_actions.handle_select_column}\n\t\t\t\t\t\t\t\t\ton_select_row={df_actions.handle_select_row}\n\t\t\t\t\t\t\t\t\t{is_dragging}\n\t\t\t\t\t\t\t\t\ton:blur={handle_blur}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t{/each}\n\t\t\t\t</tr>\n\t\t\t</tbody>\n\t\t</table>\n\t\t<Upload\n\t\t\t{upload}\n\t\t\t{stream_handler}\n\t\t\tflex={false}\n\t\t\tcenter={false}\n\t\t\tboundedheight={false}\n\t\t\tdisable_click={true}\n\t\t\t{root}\n\t\t\ton:load={({ detail }) =>\n\t\t\t\thandle_file_upload(\n\t\t\t\t\tdetail.data,\n\t\t\t\t\t(head) => {\n\t\t\t\t\t\t_headers = make_headers(\n\t\t\t\t\t\t\thead.map((h) => h ?? \"\"),\n\t\t\t\t\t\t\tcol_count,\n\t\t\t\t\t\t\tels,\n\t\t\t\t\t\t\tmake_id\n\t\t\t\t\t\t);\n\t\t\t\t\t\treturn _headers;\n\t\t\t\t\t},\n\t\t\t\t\t(vals) => {\n\t\t\t\t\t\tvalues = vals;\n\t\t\t\t\t}\n\t\t\t\t)}\n\t\t\tbind:dragging\n\t\t\taria_label={i18n(\"dataframe.drop_to_upload\")}\n\t\t>\n\t\t\t<div class=\"table-wrap\">\n\t\t\t\t<VirtualTable\n\t\t\t\t\tbind:items={search_results}\n\t\t\t\t\t{max_height}\n\t\t\t\t\tbind:actual_height={table_height}\n\t\t\t\t\tbind:table_scrollbar_width={scrollbar_width}\n\t\t\t\t\tselected={selected_index}\n\t\t\t\t\tdisable_scroll={active_cell_menu !== null ||\n\t\t\t\t\t\tactive_header_menu !== null}\n\t\t\t\t\tbind:viewport\n\t\t\t\t\tbind:show_scroll_button\n\t\t\t\t\ton:scroll_top={(_) => {}}\n\t\t\t\t>\n\t\t\t\t\t{#if label && label.length !== 0}\n\t\t\t\t\t\t<caption class=\"sr-only\">{label}</caption>\n\t\t\t\t\t{/if}\n\t\t\t\t\t<tr slot=\"thead\">\n\t\t\t\t\t\t{#if show_row_numbers}\n\t\t\t\t\t\t\t<RowNumber is_header={true} />\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{#each _headers as { value, id }, i (id)}\n\t\t\t\t\t\t\t<TableHeader\n\t\t\t\t\t\t\t\tbind:value={_headers[i].value}\n\t\t\t\t\t\t\t\t{i}\n\t\t\t\t\t\t\t\t{actual_pinned_columns}\n\t\t\t\t\t\t\t\t{header_edit}\n\t\t\t\t\t\t\t\t{selected_header}\n\t\t\t\t\t\t\t\t{headers}\n\t\t\t\t\t\t\t\t{get_cell_width}\n\t\t\t\t\t\t\t\t{handle_header_click}\n\t\t\t\t\t\t\t\t{toggle_header_menu}\n\t\t\t\t\t\t\t\t{end_header_edit}\n\t\t\t\t\t\t\t\tsort_columns={$df_state.sort_state.sort_columns}\n\t\t\t\t\t\t\t\tfilter_columns={$df_state.filter_state.filter_columns}\n\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t{max_chars}\n\t\t\t\t\t\t\t\t{editable}\n\t\t\t\t\t\t\t\tis_static={static_columns.includes(i)}\n\t\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\t\tbind:el={els[id].input}\n\t\t\t\t\t\t\t\t{col_count}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t{/each}\n\t\t\t\t\t</tr>\n\t\t\t\t\t<tr slot=\"tbody\" let:item let:index class:row-odd={index % 2 === 0}>\n\t\t\t\t\t\t{#if show_row_numbers}\n\t\t\t\t\t\t\t<RowNumber {index} />\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{#each item as { value, id }, j (id)}\n\t\t\t\t\t\t\t<TableCell\n\t\t\t\t\t\t\t\tbind:value={search_results[index][j].value}\n\t\t\t\t\t\t\t\tdisplay_value={get_cell_display_value(index, j)}\n\t\t\t\t\t\t\t\tindex={$df_state.current_search_query !== undefined &&\n\t\t\t\t\t\t\t\tfiltered_to_original_map[index] !== undefined\n\t\t\t\t\t\t\t\t\t? filtered_to_original_map[index]\n\t\t\t\t\t\t\t\t\t: index}\n\t\t\t\t\t\t\t\t{j}\n\t\t\t\t\t\t\t\t{actual_pinned_columns}\n\t\t\t\t\t\t\t\t{get_cell_width}\n\t\t\t\t\t\t\t\thandle_cell_click={(e, r, c) => handle_mouse_down(e, r, c)}\n\t\t\t\t\t\t\t\t{handle_blur}\n\t\t\t\t\t\t\t\ttoggle_cell_menu={df_actions.toggle_cell_menu}\n\t\t\t\t\t\t\t\t{is_cell_selected}\n\t\t\t\t\t\t\t\t{should_show_cell_menu}\n\t\t\t\t\t\t\t\t{selected_cells}\n\t\t\t\t\t\t\t\t{copy_flash}\n\t\t\t\t\t\t\t\t{active_cell_menu}\n\t\t\t\t\t\t\t\tstyling={search_results[index][j].styling}\n\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\tdatatype={Array.isArray(datatype) ? datatype[j] : datatype}\n\t\t\t\t\t\t\t\t{editing}\n\t\t\t\t\t\t\t\t{max_chars}\n\t\t\t\t\t\t\t\t{editable}\n\t\t\t\t\t\t\t\tis_static={static_columns.includes(j)}\n\t\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\t\t{components}\n\t\t\t\t\t\t\t\thandle_select_column={df_actions.handle_select_column}\n\t\t\t\t\t\t\t\thandle_select_row={df_actions.handle_select_row}\n\t\t\t\t\t\t\t\tbind:el={els[id]}\n\t\t\t\t\t\t\t\t{is_dragging}\n\t\t\t\t\t\t\t\t{wrap}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t{/each}\n\t\t\t\t\t</tr>\n\t\t\t\t</VirtualTable>\n\t\t\t</div>\n\t\t</Upload>\n\t\t{#if show_scroll_button}\n\t\t\t<button class=\"scroll-top-button\" on:click={scroll_to_top}>\n\t\t\t\t&uarr;\n\t\t\t</button>\n\t\t{/if}\n\t</div>\n</div>\n{#if data.length === 0 && editable && row_count[1] === \"dynamic\"}\n\t<EmptyRowButton on_click={() => add_row()} />\n{/if}\n\n{#if active_cell_menu || active_header_menu}\n\t<CellMenu\n\t\tx={active_cell_menu?.x ?? active_header_menu?.x ?? 0}\n\t\ty={active_cell_menu?.y ?? active_header_menu?.y ?? 0}\n\t\trow={active_header_menu ? -1 : active_cell_menu?.row ?? 0}\n\t\t{col_count}\n\t\t{row_count}\n\t\ton_add_row_above={() => add_row_at(active_cell_menu?.row ?? -1, \"above\")}\n\t\ton_add_row_below={() => add_row_at(active_cell_menu?.row ?? -1, \"below\")}\n\t\ton_add_column_left={() =>\n\t\t\tadd_col_at(\n\t\t\t\tactive_cell_menu?.col ?? active_header_menu?.col ?? -1,\n\t\t\t\t\"left\"\n\t\t\t)}\n\t\ton_add_column_right={() =>\n\t\t\tadd_col_at(\n\t\t\t\tactive_cell_menu?.col ?? active_header_menu?.col ?? -1,\n\t\t\t\t\"right\"\n\t\t\t)}\n\t\ton_delete_row={() => delete_row_at(active_cell_menu?.row ?? -1)}\n\t\ton_delete_col={() =>\n\t\t\tdelete_col_at(active_cell_menu?.col ?? active_header_menu?.col ?? -1)}\n\t\t{editable}\n\t\tcan_delete_rows={!active_header_menu && data.length > 1 && editable}\n\t\tcan_delete_cols={data.length > 0 && data[0]?.length > 1 && editable}\n\t\t{i18n}\n\t\ton_sort={active_header_menu\n\t\t\t? (direction) => {\n\t\t\t\t\tif (active_header_menu) {\n\t\t\t\t\t\thandle_sort(active_header_menu.col, direction);\n\t\t\t\t\t\tdf_actions.set_active_header_menu(null);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t: undefined}\n\t\ton_clear_sort={active_header_menu\n\t\t\t? () => {\n\t\t\t\t\tclear_sort();\n\t\t\t\t\tdf_actions.set_active_header_menu(null);\n\t\t\t\t}\n\t\t\t: undefined}\n\t\tsort_direction={active_header_menu\n\t\t\t? $df_state.sort_state.sort_columns.find(\n\t\t\t\t\t(item) => item.col === (active_header_menu?.col ?? -1)\n\t\t\t\t)?.direction ?? null\n\t\t\t: null}\n\t\tsort_priority={active_header_menu\n\t\t\t? $df_state.sort_state.sort_columns.findIndex(\n\t\t\t\t\t(item) => item.col === (active_header_menu?.col ?? -1)\n\t\t\t\t) + 1 || null\n\t\t\t: null}\n\t\ton_filter={active_header_menu\n\t\t\t? (datatype, filter, value) => {\n\t\t\t\t\tif (active_header_menu) {\n\t\t\t\t\t\thandle_filter(active_header_menu.col, datatype, filter, value);\n\t\t\t\t\t\tdf_actions.set_active_header_menu(null);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t: undefined}\n\t\ton_clear_filter={active_header_menu\n\t\t\t? () => {\n\t\t\t\t\tclear_filter();\n\t\t\t\t\tdf_actions.set_active_header_menu(null);\n\t\t\t\t}\n\t\t\t: undefined}\n\t\tfilter_active={active_header_menu\n\t\t\t? $df_state.filter_state.filter_columns.some(\n\t\t\t\t\t(c) => c.col === (active_header_menu?.col ?? -1)\n\t\t\t\t)\n\t\t\t: null}\n\t/>\n{/if}\n\n<style>\n\t.table-container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--size-2);\n\t\tposition: relative;\n\t}\n\n\t.table-wrap {\n\t\tposition: relative;\n\t\ttransition: 150ms;\n\t}\n\n\t.table-wrap.menu-open {\n\t\toverflow: hidden;\n\t}\n\n\t.table-wrap:focus-within {\n\t\toutline: none;\n\t}\n\n\t.table-wrap.dragging {\n\t\tcursor: crosshair !important;\n\t\tuser-select: none;\n\t}\n\n\t.table-wrap.dragging * {\n\t\tcursor: crosshair !important;\n\t\tuser-select: none;\n\t}\n\n\t.table-wrap > :global(button) {\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--table-radius);\n\t\toverflow: hidden;\n\t}\n\n\ttable {\n\t\tposition: absolute;\n\t\topacity: 0;\n\t\tz-index: -1;\n\t\ttransition: 150ms;\n\t\twidth: var(--size-full);\n\t\ttable-layout: auto;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--input-text-size);\n\t\tline-height: var(--line-md);\n\t\tfont-family: var(--font-mono);\n\t\tborder-spacing: 0;\n\t\tborder-collapse: separate;\n\t}\n\n\tthead {\n\t\tposition: sticky;\n\t\ttop: 0;\n\t\tz-index: 5;\n\t\tbox-shadow: var(--shadow-drop);\n\t}\n\n\tthead :global(th.pinned-column) {\n\t\tposition: sticky;\n\t\tz-index: 6;\n\t\tbackground: var(--table-even-background-fill) !important;\n\t}\n\n\t.dragging {\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.no-wrap {\n\t\twhite-space: nowrap;\n\t}\n\n\tdiv:not(.no-wrap) td {\n\t\toverflow-wrap: anywhere;\n\t}\n\n\tdiv.no-wrap td {\n\t\toverflow-x: hidden;\n\t}\n\n\ttr {\n\t\tbackground: var(--table-even-background-fill);\n\t}\n\n\ttr.row-odd {\n\t\tbackground: var(--table-odd-background-fill);\n\t}\n\n\t.header-row {\n\t\tdisplay: flex;\n\t\tjustify-content: flex-end;\n\t\talign-items: center;\n\t\tgap: var(--size-2);\n\t\tmin-height: var(--size-6);\n\t\tflex-wrap: nowrap;\n\t\twidth: 100%;\n\t}\n\n\t.header-row .label {\n\t\tflex: 1 1 auto;\n\t\tmargin-right: auto;\n\t}\n\n\t.header-row .label p {\n\t\tmargin: 0;\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-size: var(--block-label-text-size);\n\t\tline-height: var(--line-sm);\n\t\tposition: relative;\n\t\tz-index: 4;\n\t}\n\n\t.scroll-top-button {\n\t\tposition: absolute;\n\t\tright: var(--size-4);\n\t\tbottom: var(--size-4);\n\t\twidth: var(--size-8);\n\t\theight: var(--size-8);\n\t\tborder-radius: var(--table-radius);\n\t\tbackground: var(--color-accent);\n\t\tcolor: white;\n\t\tborder: none;\n\t\tcursor: pointer;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: var(--text-lg);\n\t\tz-index: 9;\n\t\topacity: 0.5;\n\t}\n\n\t.scroll-top-button:hover {\n\t\topacity: 1;\n\t}\n\n\ttr {\n\t\tborder-bottom: 1px solid var(--border-color-primary);\n\t\ttext-align: left;\n\t}\n</style>\n", "<svelte:options accessors={true} />\n\n<script context=\"module\" lang=\"ts\">\n\texport { default as BaseDataFrame } from \"./shared/Table.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { Block } from \"@gradio/atoms\";\n\timport Table from \"./shared/Table.svelte\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport type { Headers, Datatype, DataframeValue } from \"./shared/utils\";\n\timport Image from \"@gradio/image\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: DataframeValue = {\n\t\tdata: [[\"\", \"\", \"\"]],\n\t\theaders: [\"1\", \"2\", \"3\"],\n\t\tmetadata: null\n\t};\n\texport let value_is_output = false;\n\texport let col_count: [number, \"fixed\" | \"dynamic\"];\n\texport let row_count: [number, \"fixed\" | \"dynamic\"];\n\texport let label: string | null = null;\n\texport let show_label = true;\n\texport let wrap: boolean;\n\texport let datatype: Datatype | Datatype[];\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let root: string;\n\n\texport let line_breaks = true;\n\texport let column_widths: string[] = [];\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t\tinput: never;\n\t\tclear_status: LoadingStatus;\n\t\tsearch: string | null;\n\t}>;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let max_height: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let interactive: boolean;\n\texport let show_fullscreen_button = false;\n\texport let max_chars: number | undefined = undefined;\n\texport let show_copy_button = false;\n\texport let show_row_numbers = false;\n\texport let show_search: \"none\" | \"search\" | \"filter\" = \"none\";\n\texport let pinned_columns = 0;\n\texport let static_columns: (string | number)[] = [];\n\texport let fullscreen = false;\n</script>\n\n<Block\n\t{visible}\n\tpadding={false}\n\t{elem_id}\n\t{elem_classes}\n\tcontainer={false}\n\t{scale}\n\t{min_width}\n\toverflow_behavior=\"visible\"\n\tbind:fullscreen\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t<Table\n\t\t{root}\n\t\t{label}\n\t\t{show_label}\n\t\t{row_count}\n\t\t{col_count}\n\t\tvalues={value.data}\n\t\tdisplay_value={value.metadata?.display_value}\n\t\tstyling={value.metadata?.styling}\n\t\theaders={value.headers}\n\t\t{fullscreen}\n\t\ton:change={(e) => {\n\t\t\tvalue.data = e.detail.data;\n\t\t\tvalue.headers = e.detail.headers;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}}\n\t\ton:input={(e) => gradio.dispatch(\"input\")}\n\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\ton:fullscreen={({ detail }) => {\n\t\t\tfullscreen = detail;\n\t\t}}\n\t\t{wrap}\n\t\t{datatype}\n\t\t{latex_delimiters}\n\t\teditable={interactive}\n\t\t{max_height}\n\t\ti18n={gradio.i18n}\n\t\t{line_breaks}\n\t\t{column_widths}\n\t\tupload={(...args) => gradio.client.upload(...args)}\n\t\tstream_handler={(...args) => gradio.client.stream(...args)}\n\t\tbind:value_is_output\n\t\t{show_fullscreen_button}\n\t\t{max_chars}\n\t\t{show_copy_button}\n\t\t{show_row_numbers}\n\t\t{show_search}\n\t\t{pinned_columns}\n\t\tcomponents={{ image: Image }}\n\t\t{static_columns}\n\t/>\n</Block>\n"], "names": ["get_sort_status", "name", "sort_columns", "headers", "sort_item", "item", "col", "sort_data", "data", "row_indices", "_", "row_a_idx", "row_b_idx", "row_a", "row_b", "sort_by", "direction", "val_a", "val_b", "comparison", "i", "sort_data_and_preserve_selection", "display_value", "styling", "selected", "get_current_indices", "id", "sort_table_data", "new_selected", "j", "filter_data", "filter_columns", "column", "filter_data_and_preserve_selection", "original_data", "original_display_value", "original_styling", "filter_table_data", "get_max", "max", "indices", "new_data", "new_display", "new_styling", "base_data", "base_display_value", "base_styling", "row", "copy_table_data", "selected_cells", "csv", "r", "c", "acc", "value", "rows", "a", "b", "cols", "text", "err", "guess_delimiter", "possibleDelimiters", "weedOut", "delimiter", "cache", "checkLength", "line", "length", "data_uri_to_blob", "data_uri", "byte_str", "mime_str", "ab", "ia", "handle_file_upload", "update_headers", "update_values", "blob", "reader", "e", "head", "rest", "dsvFormat", "is_cell_in_selection", "coords", "is_cell_selected", "cell", "up", "down", "left", "right", "get_range_selection", "start", "end", "start_row", "start_col", "end_row", "end_col", "min_row", "max_row", "min_col", "max_col", "cells", "handle_selection", "current", "event", "is_cell_match", "index", "handle_delete_key", "should_show_cell_menu", "editable", "get_next_cell_coordinates", "shift_key", "next_row", "prev_row", "move_cursor", "current_coords", "key", "dir", "arr", "_acc", "_data", "k", "handle_click_outside", "parent", "trigger", "calculate_selection_positions", "els", "table", "cell_id", "cell_el", "cell_rect", "table_rect", "col_pos", "row_pos", "getContext", "tick", "DATAFRAME_KEY", "create_actions", "state", "context", "update_state", "updater", "s", "add_row", "make_id", "new_row", "add_col", "new_headers", "h", "update_array", "source", "target", "query", "sort_cols", "initial_data", "datatype", "filter", "filter_cols", "get", "comp", "position", "previous_data", "previous_headers", "value_is_output", "dispatch", "current_headers", "current_data", "dequal", "original", "menu", "editing", "header_index", "button", "actual_row", "filtered_indices", "dataRow", "idx", "current_menu", "rect", "current_button", "new_button", "create_dataframe_context", "config", "writable", "instance_id", "setContext", "ctx", "attr", "button_class_value", "insert", "anchor", "append", "span", "svg", "path", "dirty", "$$props", "on_click", "click_handler", "$$invalidate", "is_first_position", "div", "on_change", "handle_change", "bool_value", "value$1", "textarea", "toggle_class", "booleancell_changes", "markdowncode_changes", "switch_value", "func", "switch_instance_changes", "create_if_block_5", "if_block2", "create_if_block", "truncate_text", "max_length", "is_image", "str", "use_focus", "node", "edit", "header", "latex_delimiters", "line_breaks", "is_static", "max_chars", "components", "i18n", "is_dragging", "wrap_text", "show_selection_buttons", "on_select_column", "on_select_row", "el", "createEventDispatcher", "handle_blur", "handle_keydown", "handle_bool_change", "new_value", "$$value", "should_truncate", "display_content", "display_text", "t_value", "td", "set_data", "t", "th", "is_header", "is_function", "touch", "mouseEvent", "path0", "path1", "size", "path2", "path3", "path4", "create_if_block_1", "create_if_block_2", "create_if_block_3", "create_if_block_4", "create_if_block_6", "create_if_block_7", "create_if_block_8", "create_if_block_9", "create_if_block_10", "icon", "if_block0", "if_block1", "th_aria_sort_value", "set_style", "div1", "div0", "editablecell_changes", "actual_pinned_columns", "header_edit", "selected_header", "get_cell_width", "handle_header_click", "toggle_header_menu", "end_header_edit", "col_count", "get_header_position", "col_index", "click_handler_1", "can_add_columns", "sort_index", "filter_index", "sort_priority", "current_direction", "editablecell_props", "td_tabindex_value", "td_data_testid_value", "handle_cell_click", "toggle_cell_menu", "copy_flash", "active_cell_menu", "handle_select_column", "handle_select_row", "wrap", "get_cell_position", "$$self", "mousedown_handler", "contextmenu_handler", "cell_classes", "is_in_selection", "has_no_top", "has_no_bottom", "has_no_left", "has_no_right", "onMount", "get_key", "height", "svelte_virtual_table_viewport", "thead", "tbody", "tfoot", "items", "max_height", "actual_height", "table_scrollbar_width", "disable_scroll", "show_scroll_button", "viewport", "average_height", "bottom", "contents", "head_height", "foot_height", "height_map", "mounted", "top", "viewport_height", "visible", "viewport_box", "is_browser", "raf", "cb", "refresh_height_map", "sortedItems", "scroll_to_index", "scrollTop", "v", "y", "row_height", "content_height", "remaining", "scrollbar_height", "filtered_height_map", "scroll_and_render", "n", "is_in_view", "viewport_top", "opts", "align_end", "_itemHeight", "distance", "_opts", "ResizeObserverSingleton", "each_value", "ensure_array_like", "t0", "t0_value", "div5", "div4", "button0", "div3", "div2", "button1", "input", "button2", "on_filter", "menu_element", "current_filter", "filter_dropdown_open", "filter_input_value", "filter_options", "position_menu", "viewport_width", "menu_rect", "x", "handle_filter_input", "click_handler_2", "opt", "click_handler_4", "t1_value", "t5_value", "t9_value", "t12_value", "t16_value", "button3", "button4", "t1", "t5", "t9", "t12", "t16", "t4_value", "t4", "on_add_row_above", "on_add_row_below", "on_add_column_left", "on_add_column_right", "row_count", "on_delete_row", "on_delete_col", "can_delete_rows", "can_delete_cols", "on_sort", "on_clear_sort", "sort_direction", "on_clear_filter", "filter_active", "active_filter_menu", "new_x", "new_y", "toggle_filter_menu", "can_add_rows", "input_value_value", "input_placeholder_value", "show_fullscreen_button", "show_copy_button", "show_search", "fullscreen", "on_copy", "on_commit_filter", "copied", "timer", "current_search_query", "input_value", "handle_search_input", "new_query", "copy_feedback", "handle_copy", "onDestroy", "make_headers", "_head", "_h", "fill", "_id", "process_data", "values", "data_binding", "display", "cast_value_to_type", "d", "save_cell_value", "old_value", "handle_cell_blur", "input_el", "handle_header_navigation", "handle_delete_operation", "static_columns", "handle_arrow_keys", "next_coords", "handle_enter_key", "handle_tab_key", "next_cell", "handle_default_key", "handle_cell_navigation", "create_drag_handlers", "set_is_dragging", "set_selected_cells", "set_selected", "show_row_numbers", "parent_element", "start_drag", "is_checkbox_click", "update_selection", "selection_range", "end_drag", "dx", "dy", "if_block", "toolbar_changes", "p", "caption", "tr", "tablecell_props", "tablecell_changes", "virtualtable_changes", "cellmenu_changes", "if_block6", "table_1", "tr0", "tr1", "upload_1_changes", "label", "show_label", "root", "column_widths", "upload", "stream_handler", "pinned_columns", "df_ctx", "df_state", "df_actions", "get_data_at", "get_column", "get_row", "init_drag_handlers", "observer", "entries", "entry", "is_visible", "width_calculated", "handle_resize", "global_mouse_up", "drag_start", "handle_mouse_up", "_headers", "old_headers", "old_val", "search_results", "dragging", "color_accent_copied", "filtered_to_original_map", "handle_sort", "clear_sort", "handle_filter", "clear_filter", "edit_header", "_select", "result", "new_w", "handle_click_outside_util", "width_calc_timeout", "last_width_data_length", "last_width_column_count", "set_cell_widths", "column_count", "$df_state", "widths", "percentage", "pixel_width", "width", "calculated_width", "table_height", "scrollbar_width", "_display_value", "_styling", "set_copy_flash", "previous_selected_cells", "blur_event", "active_header_menu", "afterUpdate", "delete_col_at", "delete_row_at", "selected_cell_coords", "commit_filter", "filtered_data", "filtered_display_values", "filtered_styling", "data_row", "display_row", "styling_row", "change_payload", "scroll_to_top", "add_row_at", "row_index", "add_col_at", "reset_sort_state", "mouse_down_pos", "drag_state", "drag_handlers", "get_cell_display_value", "search_handler", "handle_mouse_down", "load_handler", "detail", "vals", "keydown_handler", "func_3", "func_4", "func_7", "is_reset", "is_different_structure", "cell_map", "row_idx", "col_idx", "filtered", "rowIdx", "colIdx", "dtype", "selected_index", "positions", "handle_mouse_move", "Image", "table_changes", "elem_id", "elem_classes", "scale", "min_width", "gradio", "loading_status", "interactive", "clear_status_handler", "args", "func_1", "input_handler"], "mappings": "s0DAKgB,SAAAA,GACfC,EACAC,EACAC,EAC0B,CAC1B,GAAI,CAACD,EAAa,OAAe,MAAA,OAEjC,MAAME,EAAYF,EAAa,KAAMG,GAAS,CAC7C,MAAMC,EAAMD,EAAK,IACb,OAAAC,EAAM,GAAKA,GAAOH,EAAQ,OAAe,GACtCA,EAAQG,CAAG,IAAML,CAAA,CACxB,EAED,OAAKG,EACEA,EAAU,UADM,MAExB,CAEgB,SAAAG,GACfC,EACAN,EACW,CACP,GAAA,CAACM,GAAQ,CAACA,EAAK,QAAU,CAACA,EAAK,CAAC,EACnC,MAAO,GAGJ,GAAAN,EAAa,OAAS,EAAG,CAC5B,MAAMO,EAAc,CAAC,GAAG,MAAMD,EAAK,MAAM,CAAC,EAAE,IAAI,CAACE,EAAG,IAAM,CAAC,EAC/C,OAAAD,EAAA,KAAK,CAACE,EAAWC,IAAc,CACpC,MAAAC,EAAQL,EAAKG,CAAS,EACtBG,EAAQN,EAAKI,CAAS,EAE5B,SAAW,CAAE,IAAKG,EAAS,UAAAC,CAAA,IAAed,EAAc,CACvD,GACC,CAACW,GACD,CAACC,GACDC,EAAU,GACVA,GAAWF,EAAM,QACjBE,GAAWD,EAAM,QACjB,CAACD,EAAME,CAAO,GACd,CAACD,EAAMC,CAAO,EAEd,SAGK,MAAAE,EAAQJ,EAAME,CAAO,EAAE,MACvBG,EAAQJ,EAAMC,CAAO,EAAE,MACvBI,EAAaF,EAAQC,EAAQ,GAAKD,EAAQC,EAAQ,EAAI,EAE5D,GAAIC,IAAe,EACX,OAAAH,IAAc,MAAQG,EAAa,CAACA,CAE7C,CAEO,MAAA,EAAA,CACP,EACMV,CACR,CACO,MAAA,CAAC,GAAG,MAAMD,EAAK,MAAM,CAAC,EAAE,IAAI,CAACE,EAAGU,IAAMA,CAAC,CAC/C,CAEO,SAASC,GACfb,EACAc,EACAC,EACArB,EACAsB,EACAC,EAI4D,CAC5D,IAAIC,EAAK,KACLF,GAAYA,EAAS,CAAC,IAAKhB,GAAQgB,EAAS,CAAC,IAAKhB,EAAKgB,EAAS,CAAC,CAAC,IAChEE,EAAAlB,EAAKgB,EAAS,CAAC,CAAC,EAAEA,EAAS,CAAC,CAAC,EAAE,IAGrBG,GAAAnB,EAAMc,EAAeC,EAASrB,CAAY,EAE1D,IAAI0B,EAAeJ,EACnB,GAAIE,EAAI,CACP,KAAM,CAACN,EAAGS,CAAC,EAAIJ,EAAoBC,EAAIlB,CAAI,EAC5BoB,EAAA,CAACR,EAAGS,CAAC,CACrB,CAEO,MAAA,CAAE,KAAArB,EAAM,SAAUoB,EAC1B,CCtFgB,SAAAE,GACftB,EACAuB,EAMW,CACP,GAAA,CAACvB,GAAQ,CAACA,EAAK,QAAU,CAACA,EAAK,CAAC,EACnC,MAAO,GAER,IAAIC,EAAc,CAAC,GAAG,MAAMD,EAAK,MAAM,CAAC,EAAE,IAAI,CAACE,EAAG,IAAM,CAAC,EAErD,OAAAqB,EAAe,OAAS,GACZA,EAAA,QAASC,GAAW,CAC9B,GAAAA,EAAO,WAAa,SACvB,OAAQA,EAAO,OAAQ,CACtB,IAAK,WACJvB,EAAcA,EAAY,OAAQ,GACjCD,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,MAAM,SAAW,EAAA,SAASA,EAAO,KAAK,CAAA,EAE5D,MACD,IAAK,mBACJvB,EAAcA,EAAY,OACxB,GACA,CAACD,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,MAAM,SAAW,EAAA,SAASA,EAAO,KAAK,CAAA,EAE9D,MACD,IAAK,cACJvB,EAAcA,EAAY,OAAQ,GACjCD,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,MAAM,SAAW,EAAA,WAAWA,EAAO,KAAK,CAAA,EAE9D,MACD,IAAK,YACJvB,EAAcA,EAAY,OAAQ,GACjCD,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,MAAM,SAAW,EAAA,SAASA,EAAO,KAAK,CAAA,EAE5D,MACD,IAAK,KACJvB,EAAcA,EAAY,OACxB,GAAMD,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,MAAM,aAAeA,EAAO,KAAA,EAEzD,MACD,IAAK,SACJvB,EAAcA,EAAY,OACxB,GAAQD,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,MAAM,aAAeA,EAAO,KAAA,EAE3D,MACD,IAAK,WACJvB,EAAcA,EAAY,OACxB,GAAMD,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,MAAM,SAAA,IAAe,EAAA,EAElD,MACD,IAAK,eACJvB,EAAcA,EAAY,OACxB,GAAQD,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,MAAM,SAAe,IAAA,EAAA,EAEpD,KACF,SACUA,EAAO,WAAa,SAC9B,OAAQA,EAAO,OAAQ,CACtB,IAAK,IACUvB,EAAAA,EAAY,OAAQ,GAEhC,CAAC,MAAM,OAAOD,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,KAAK,CAAC,GACzC,CAAC,MAAM,OAAOA,EAAO,KAAK,CAAC,EAG1B,OAAOxB,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,KAAK,IAAM,OAAOA,EAAO,KAAK,EAGrD,EACP,EACD,MACD,IAAK,IACUvB,EAAAA,EAAY,OAAQ,GAEhC,CAAC,MAAM,OAAOD,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,KAAK,CAAC,GACzC,CAAC,MAAM,OAAOA,EAAO,KAAK,CAAC,EAG1B,OAAOxB,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,KAAK,IAAM,OAAOA,EAAO,KAAK,EAGrD,EACP,EACD,MACD,IAAK,IACUvB,EAAAA,EAAY,OAAQ,GAEhC,CAAC,MAAM,OAAOD,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,KAAK,CAAC,GACzC,CAAC,MAAM,OAAOA,EAAO,KAAK,CAAC,EAG1B,OAAOxB,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,KAAK,EAAI,OAAOA,EAAO,KAAK,EAGnD,EACP,EACD,MACD,IAAK,IACUvB,EAAAA,EAAY,OAAQ,GAEhC,CAAC,MAAM,OAAOD,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,KAAK,CAAC,GACzC,CAAC,MAAM,OAAOA,EAAO,KAAK,CAAC,EAG1B,OAAOxB,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,KAAK,EAAI,OAAOA,EAAO,KAAK,EAGnD,EACP,EACD,MACD,IAAK,IACUvB,EAAAA,EAAY,OAAQ,GAEhC,CAAC,MAAM,OAAOD,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,KAAK,CAAC,GACzC,CAAC,MAAM,OAAOA,EAAO,KAAK,CAAC,EAG1B,OAAOxB,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,KAAK,GAAK,OAAOA,EAAO,KAAK,EAGpD,EACP,EACD,MACD,IAAK,IACUvB,EAAAA,EAAY,OAAQ,GAEhC,CAAC,MAAM,OAAOD,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,KAAK,CAAC,GACzC,CAAC,MAAM,OAAOA,EAAO,KAAK,CAAC,EAG1B,OAAOxB,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,KAAK,GAAK,OAAOA,EAAO,KAAK,EAGpD,EACP,EACD,MACD,IAAK,WACJvB,EAAcA,EAAY,OACxB,GAAMD,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,MAAM,SAAA,IAAe,EAAA,EAElD,MACD,IAAK,eACUvB,EAAAA,EAAY,OAAQ,GAC5B,MAAM,OAAOD,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,KAAK,CAAC,EAGtC,GAFGxB,EAAK,CAAC,EAAEwB,EAAO,GAAG,GAAG,MAAM,aAAe,EAGpD,EACD,KACF,CACD,CACA,EACMvB,GAED,CAAC,GAAG,MAAMD,EAAK,MAAM,CAAC,EAAE,IAAI,CAACE,EAAG,IAAM,CAAC,CAC/C,CAEgB,SAAAuB,GACfzB,EACAc,EACAC,EACAQ,EAMAP,EACAC,EAIAS,EACAC,EACAC,EAC4D,CAC5D,IAAIV,EAAK,KACLF,GAAYA,EAAS,CAAC,IAAKhB,GAAQgB,EAAS,CAAC,IAAKhB,EAAKgB,EAAS,CAAC,CAAC,IAChEE,EAAAlB,EAAKgB,EAAS,CAAC,CAAC,EAAEA,EAAS,CAAC,CAAC,EAAE,IAGrCa,GACC7B,EACAc,EACAC,EACAQ,EACAG,EACAC,EACAC,CAAA,EAGD,IAAIR,EAAeJ,EACnB,GAAIE,EAAI,CACP,KAAM,CAACN,EAAGS,CAAC,EAAIJ,EAAoBC,EAAIlB,CAAI,EAC5BoB,EAAA,CAACR,EAAGS,CAAC,CACrB,CAEO,MAAA,CAAE,KAAArB,EAAM,SAAUoB,EAC1B,CC/LO,SAASU,GAAQ9B,EAA8B,CACjD,GAAA,CAACA,GAAQ,CAACA,EAAK,OAAQ,MAAO,GAClC,IAAI+B,EAAM/B,EAAK,CAAC,EAAE,MAAM,EACxB,QAASY,EAAI,EAAGA,EAAIZ,EAAK,OAAQY,IAChC,QAASS,EAAI,EAAGA,EAAIrB,EAAKY,CAAC,EAAE,OAAQS,IAC/B,GAAGU,EAAIV,CAAC,EAAE,KAAK,GAAG,OAAS,GAAGrB,EAAKY,CAAC,EAAES,CAAC,EAAE,KAAK,GAAG,SACpDU,EAAIV,CAAC,EAAIrB,EAAKY,CAAC,EAAES,CAAC,GAId,OAAAU,CACR,CAEO,SAASZ,GACfnB,EACAc,EACAC,EACArB,EACO,CAEH,GADA,CAACA,EAAa,QACd,CAACM,GAAQ,CAACA,EAAK,OAAQ,OAErB,MAAAgC,EAAUjC,GAAUC,EAAMN,CAAY,EAEtCuC,EAAWD,EAAQ,IAAKpB,GAAcZ,EAAKY,CAAC,CAAC,EAGnD,GAFAZ,EAAK,OAAO,EAAGA,EAAK,OAAQ,GAAGiC,CAAQ,EAEnCnB,EAAe,CAClB,MAAMoB,EAAcF,EAAQ,IAAKpB,GAAcE,EAAcF,CAAC,CAAC,EAC/DE,EAAc,OAAO,EAAGA,EAAc,OAAQ,GAAGoB,CAAW,CAC7D,CAEA,GAAInB,EAAS,CACZ,MAAMoB,EAAcH,EAAQ,IAAKpB,GAAcG,EAAQH,CAAC,CAAC,EACzDG,EAAQ,OAAO,EAAGA,EAAQ,OAAQ,GAAGoB,CAAW,CACjD,CACD,CAEO,SAASN,GACf7B,EACAc,EACAC,EACAQ,EAMAG,EACAC,EACAC,EACO,CACP,MAAMQ,EAAYV,GAAiB1B,EAC7BqC,EAAqBV,GAA0Bb,EAC/CwB,EAAeV,GAAoBb,EAErC,GAAA,CAACQ,EAAe,OAAQ,CAC3BvB,EAAK,OAAO,EAAGA,EAAK,OAAQ,GAAGoC,EAAU,IAAKG,GAAQ,CAAC,GAAGA,CAAG,CAAC,CAAC,EAC3DzB,GAAiBuB,GACNvB,EAAA,OACb,EACAA,EAAc,OACd,GAAGuB,EAAmB,IAAKE,GAAQ,CAAC,GAAGA,CAAG,CAAC,CAAA,EAGzCxB,GAAWuB,GACdvB,EAAQ,OAAO,EAAGA,EAAQ,OAAQ,GAAGuB,EAAa,IAAKC,GAAQ,CAAC,GAAGA,CAAG,CAAC,CAAC,EAEzE,MACD,CACI,GAAA,CAACvC,GAAQ,CAACA,EAAK,OAAQ,OAErB,MAAAgC,EAAUV,GAAYc,EAAWb,CAAc,EAE/CU,EAAWD,EAAQ,IAAKpB,GAAcwB,EAAUxB,CAAC,CAAC,EAGxD,GAFAZ,EAAK,OAAO,EAAGA,EAAK,OAAQ,GAAGiC,CAAQ,EAEnCnB,GAAiBuB,EAAoB,CACxC,MAAMH,EAAcF,EAAQ,IAAKpB,GAAcyB,EAAmBzB,CAAC,CAAC,EACpEE,EAAc,OAAO,EAAGA,EAAc,OAAQ,GAAGoB,CAAW,CAC7D,CAEA,GAAInB,GAAWuB,EAAc,CAC5B,MAAMH,EAAcH,EAAQ,IAAKpB,GAAc0B,EAAa1B,CAAC,CAAC,EAC9DG,EAAQ,OAAO,EAAGA,EAAQ,OAAQ,GAAGoB,CAAW,CACjD,CACD,CAEsB,eAAAK,GACrBxC,EACAyC,EACgB,CACZ,GAAA,CAACzC,GAAQ,CAACA,EAAK,OAAQ,OAM3B,MAAM0C,GAHLD,GACAzC,EAAK,QAAQ,CAACuC,EAAKI,IAAMJ,EAAI,IAAI,CAACrC,EAAG0C,IAAM,CAACD,EAAGC,CAAC,CAAqB,CAAC,GAE7C,OACzB,CAACC,EAAmD,CAACN,EAAKzC,CAAG,IAAM,CAClE+C,EAAIN,CAAG,EAAIM,EAAIN,CAAG,GAAK,CAAA,EACvB,MAAMO,EAAQ,OAAO9C,EAAKuC,CAAG,EAAEzC,CAAG,EAAE,KAAK,EACrC,OAAA+C,EAAAN,CAAG,EAAEzC,CAAG,EACXgD,EAAM,SAAS,GAAG,GAAKA,EAAM,SAAS,GAAG,GAAKA,EAAM,SAAS;AAAA,CAAI,EAC9D,IAAIA,EAAM,QAAQ,KAAM,IAAI,CAAC,IAC7BA,EACGD,CACR,EACA,CAAC,CAAA,EAGIE,EAAO,OAAO,KAAKL,CAAG,EAAE,KAAK,CAACM,EAAGC,IAAM,CAACD,EAAI,CAACC,CAAC,EACpD,GAAI,CAACF,EAAK,OAAQ,OAElB,MAAMG,EAAO,OAAO,KAAKR,EAAIK,EAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAACC,EAAGC,IAAM,CAACD,EAAI,CAACC,CAAC,EACvDE,EAAOJ,EACX,IAAKJ,GAAMO,EAAK,IAAKN,GAAMF,EAAIC,CAAC,EAAEC,CAAC,GAAK,EAAE,EAAE,KAAK,GAAG,CAAC,EACrD,KAAK;AAAA,CAAI,EAEP,GAAA,CACG,MAAA,UAAU,UAAU,UAAUO,CAAI,QAChCC,EAAK,CACb,MAAM,IAAI,MAAM,gCAAmCA,EAAc,OAAO,CACzE,CACD,CAGgB,SAAAC,GACfF,EACAG,EACW,CACJ,OAAAA,EAAmB,OAAOC,CAAO,EAExC,SAASA,EAAQC,EAA4B,CAC5C,IAAIC,EAAQ,GACZ,OAAON,EAAK,MAAM;AAAA,CAAI,EAAE,MAAMO,CAAW,EAEzC,SAASA,EAAYC,EAAuB,CAC3C,GAAI,CAACA,EAAa,MAAA,GAClB,IAAIC,EAASD,EAAK,MAAMH,CAAS,EAAE,OACnC,OAAIC,EAAQ,IAAWA,EAAAG,GAChBH,IAAUG,GAAUA,EAAS,CACrC,CACD,CACD,CAEO,SAASC,GAAiBC,EAAwB,CACxD,MAAMC,EAAW,KAAKD,EAAS,MAAM,GAAG,EAAE,CAAC,CAAC,EACtCE,EAAWF,EAAS,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAC5DG,EAAK,IAAI,YAAYF,EAAS,MAAM,EACpCG,EAAK,IAAI,WAAWD,CAAE,EAC5B,QAASrD,EAAI,EAAGA,EAAImD,EAAS,OAAQnD,IACpCsD,EAAGtD,CAAC,EAAImD,EAAS,WAAWnD,CAAC,EAEvB,OAAA,IAAI,KAAK,CAACqD,CAAE,EAAG,CAAE,KAAMD,EAAU,CACzC,CAEgB,SAAAG,GACfL,EACAM,EACAC,EACO,CACD,MAAAC,EAAOT,GAAiBC,CAAQ,EAChCS,EAAS,IAAI,WACZA,EAAA,iBAAiB,UAAYC,GAAM,CACzC,GAAI,CAACA,GAAG,QAAQ,QAAU,OAAOA,EAAE,OAAO,QAAW,SAAU,OACzD,KAAA,CAAChB,CAAS,EAAIH,GAAgBmB,EAAE,OAAO,OAAQ,CAAC,IAAK,GAAI,CAAC,EAC1D,CAACC,EAAM,GAAGC,CAAI,EAAIC,GAAUnB,CAAS,EAAE,UAAUgB,EAAE,OAAO,MAAM,EACtEJ,EAAeK,CAAI,EACnBJ,EAAcK,CAAI,CAAA,CAClB,EACDH,EAAO,WAAWD,CAAI,CACvB,CCvLgB,SAAAM,GACfC,EACApC,EACU,CACJ,KAAA,CAACF,EAAKzC,CAAG,EAAI+E,EACZ,OAAApC,EAAe,KAAK,CAAC,CAACE,EAAGC,CAAC,IAAMD,IAAMJ,GAAOK,IAAM9C,CAAG,CAC9D,CAEgB,SAAAgF,GACfC,EACAtC,EACS,CACH,KAAA,CAACF,EAAKzC,CAAG,EAAIiF,EACf,GAAA,CAACtC,EAAe,KAAK,CAAC,CAACE,EAAGC,CAAC,IAAMD,IAAMJ,GAAOK,IAAM9C,CAAG,EAAU,MAAA,GAErE,MAAMkF,EAAKvC,EAAe,KAAK,CAAC,CAACE,EAAGC,CAAC,IAAMD,IAAMJ,EAAM,GAAKK,IAAM9C,CAAG,EAC/DmF,EAAOxC,EAAe,KAAK,CAAC,CAACE,EAAGC,CAAC,IAAMD,IAAMJ,EAAM,GAAKK,IAAM9C,CAAG,EACjEoF,EAAOzC,EAAe,KAAK,CAAC,CAACE,EAAGC,CAAC,IAAMD,IAAMJ,GAAOK,IAAM9C,EAAM,CAAC,EACjEqF,EAAQ1C,EAAe,KAAK,CAAC,CAACE,EAAGC,CAAC,IAAMD,IAAMJ,GAAOK,IAAM9C,EAAM,CAAC,EAExE,MAAO,gBAAgBkF,EAAK,UAAY,EAAE,GAAGC,EAAO,aAAe,EAAE,GAAGC,EAAO,WAAa,EAAE,GAAGC,EAAQ,YAAc,EAAE,EAC1H,CAEgB,SAAAC,GACfC,EACAC,EACmB,CACb,KAAA,CAACC,EAAWC,CAAS,EAAIH,EACzB,CAACI,EAASC,CAAO,EAAIJ,EACrBK,EAAU,KAAK,IAAIJ,EAAWE,CAAO,EACrCG,EAAU,KAAK,IAAIL,EAAWE,CAAO,EACrCI,EAAU,KAAK,IAAIL,EAAWE,CAAO,EACrCI,EAAU,KAAK,IAAIN,EAAWE,CAAO,EAErCK,EAA0B,CAAA,EAIhCA,EAAM,KAAKV,CAAK,EAEhB,QAASzE,EAAI+E,EAAS/E,GAAKgF,EAAShF,IACnC,QAASS,EAAIwE,EAASxE,GAAKyE,EAASzE,IAC/BT,IAAM2E,GAAalE,IAAMmE,GAC7BO,EAAM,KAAK,CAACnF,EAAGS,CAAC,CAAC,EAGZ,OAAA0E,CACR,CAEgB,SAAAC,GACfC,EACAxD,EACAyD,EACmB,CACnB,GAAIA,EAAM,UAAYzD,EAAe,OAAS,EACtC,OAAA2C,GACN3C,EAAeA,EAAe,OAAS,CAAC,EACxCwD,CAAA,EAIE,GAAAC,EAAM,SAAWA,EAAM,QAAS,CACnC,MAAMC,EAAgB,CAAC,CAAC,EAAGvD,CAAC,IAC3B,IAAMqD,EAAQ,CAAC,GAAKrD,IAAMqD,EAAQ,CAAC,EAC9BG,EAAQ3D,EAAe,UAAU0D,CAAa,EACpD,OAAOC,IAAU,GACd,CAAC,GAAG3D,EAAgBwD,CAAO,EAC3BxD,EAAe,OAAO,CAACvC,EAAGU,IAAMA,IAAMwF,CAAK,CAC/C,CAEA,MAAO,CAACH,CAAO,CAChB,CAEgB,SAAAI,GACfrG,EACAyC,EACe,CACT,MAAAR,EAAWjC,EAAK,IAAKuC,GAAQ,CAAC,GAAGA,CAAG,CAAC,EAC3C,OAAAE,EAAe,QAAQ,CAAC,CAACF,EAAKzC,CAAG,IAAM,CAClCmC,EAASM,CAAG,GAAKN,EAASM,CAAG,EAAEzC,CAAG,IACrCmC,EAASM,CAAG,EAAEzC,CAAG,EAAI,CAAE,GAAGmC,EAASM,CAAG,EAAEzC,CAAG,EAAG,MAAO,EAAG,EACzD,CACA,EACMmC,CACR,CAEgB,SAAAqE,GACfvB,EACAtC,EACA8D,EACU,CACJ,KAAA,CAAChE,EAAKzC,CAAG,EAAIiF,EACnB,OACCwB,GACA9D,EAAe,SAAW,GAC1BA,EAAe,CAAC,EAAE,CAAC,IAAMF,GACzBE,EAAe,CAAC,EAAE,CAAC,IAAM3C,CAE3B,CAEgB,SAAA0G,GACfP,EACAjG,EACAyG,EACyB,CACnB,KAAA,CAAClE,EAAKzC,CAAG,EAAImG,EACbzF,EAAYiG,EAAY,GAAK,EAEnC,GAAIzG,EAAKuC,CAAG,IAAIzC,EAAMU,CAAS,EACvB,MAAA,CAAC+B,EAAKzC,EAAMU,CAAS,EAG7B,MAAMkG,EAAWnE,GAAO/B,EAAY,EAAI,EAAI,GACtCmG,EAAWpE,GAAO/B,EAAY,EAAI,GAAK,GAE7C,OAAIA,EAAY,GAAKR,EAAK0G,CAAQ,IAAI,CAAC,EAC/B,CAACA,EAAU,CAAC,EAGhBlG,EAAY,GAAKR,EAAK2G,CAAQ,IAAI3G,EAAK,CAAC,EAAE,OAAS,CAAC,EAChD,CAAC2G,EAAU3G,EAAK,CAAC,EAAE,OAAS,CAAC,EAG9B,EACR,CAEgB,SAAA4G,GACfV,EACAW,EACA7G,EACyB,CACzB,MAAM8G,EAAMZ,EAAM,IACZa,EAAM,CACX,WAAY,CAAC,EAAG,CAAC,EACjB,UAAW,CAAC,EAAG,EAAE,EACjB,UAAW,CAAC,EAAG,CAAC,EAChB,QAAS,CAAC,GAAI,CAAC,GACdD,CAAG,EAEL,IAAIlG,EAAGS,EACH,GAAA6E,EAAM,SAAWA,EAAM,QAC1B,GAAIY,IAAQ,aACXlG,EAAIiG,EAAe,CAAC,EAChBxF,EAAArB,EAAK,CAAC,EAAE,OAAS,UACX8G,IAAQ,YAClBlG,EAAIiG,EAAe,CAAC,EAChBxF,EAAA,UACMyF,IAAQ,YAClBlG,EAAIZ,EAAK,OAAS,EAClBqB,EAAIwF,EAAe,CAAC,UACVC,IAAQ,UACdlG,EAAA,EACJS,EAAIwF,EAAe,CAAC,MAEb,OAAA,QAGRjG,EAAIiG,EAAe,CAAC,EAAIE,EAAI,CAAC,EAC7B1F,EAAIwF,EAAe,CAAC,EAAIE,EAAI,CAAC,EAG1B,OAAAnG,EAAI,GAAKS,GAAK,EACV,GAGQrB,EAAKY,CAAC,IAAIS,CAAC,EAEnB,CAACT,EAAGS,CAAC,EAEN,EACR,CAEgB,SAAAJ,GACfC,EACAlB,EACmB,CACnB,OAAOA,EAAK,OACX,CAAC6C,EAAKmE,EAAK,IAAM,CAChB,MAAM3F,EAAI2F,EAAI,OACb,CAACC,EAAMC,EAAOC,IAAOjG,IAAOgG,EAAM,GAAKC,EAAIF,EAC3C,EAAA,EAED,OAAO5F,IAAM,GAAKwB,EAAM,CAAC,EAAGxB,CAAC,CAC9B,EACA,CAAC,GAAI,EAAE,CAAA,CAET,CAEgB,SAAA+F,GACflB,EACAmB,EACU,CACV,KAAM,CAACC,CAAO,EAAIpB,EAAM,aAAa,EAC9B,MAAA,CAACmB,EAAO,SAASC,CAAO,CAChC,CAUO,SAASC,GACfvG,EACAhB,EACAwH,EACAH,EACAI,EACmD,CAC7C,KAAA,CAAClF,EAAKzC,CAAG,EAAIkB,EACnB,GAAI,CAAChB,EAAKuC,CAAG,IAAIzC,CAAG,EACnB,MAAO,CAAE,QAAS,MAAO,QAAS,MAAU,EAG7C,MAAM4H,EAAU1H,EAAKuC,CAAG,EAAEzC,CAAG,EAAE,GACzB6H,EAAUH,EAAIE,CAAO,GAAG,KAE9B,GAAI,CAACC,EACJ,MAAO,CAAE,QAAS,MAAO,QAAS,MAAU,EAGvC,MAAAC,EAAYD,EAAQ,wBACpBE,EAAaJ,EAAM,wBACnBK,EAAU,GAAGF,EAAU,KAAOC,EAAW,KAAOD,EAAU,MAAQ,CAAC,KACnEG,EAAU,GAAGH,EAAU,IAAMC,EAAW,IAAMD,EAAU,OAAS,CAAC,KAEjE,MAAA,CAAE,QAAAE,EAAS,QAAAC,EACnB,CCzOA,KAAA,CAAA,WAAAC,gBAAuC,EAAA,OAAA,2BAIvC,CAAAC,KAAAA,IAAqB,OAAA,2BAQRC,GAAgB,OAAO,WAAW,EAmL/C,SAASC,GACRC,EACAC,EACmB,CACnB,MAAMC,EACLC,GACUH,EAAM,OAAQI,IAAO,CAAE,GAAGA,EAAG,GAAGD,EAAQC,CAAC,GAAI,EAElDC,EAAU,CACfzI,EACA0I,EACAtC,IACa,CACb,MAAMuC,EAAU3I,EAAK,CAAC,GAAG,OACtB,MAAMA,EAAK,CAAC,EAAE,MAAM,EACnB,KAAK,IAAI,EACT,IAAI,KAAO,CAAE,MAAO,GAAI,GAAI0I,KAAY,EACzC,CAAC,CAAE,MAAO,GAAI,GAAIA,EAAA,CAAW,CAAA,EAC1BzG,EAAW,CAAC,GAAGjC,CAAI,EACf,OAAAoG,IAAA,OACPnE,EAAS,OAAOmE,EAAO,EAAGuC,CAAO,EACjC1G,EAAS,KAAK0G,CAAO,EACjB1G,CAAA,EAGF2G,EAAU,CACf5I,EACAL,EACA+I,EACAtC,IAC0C,CACpC,MAAAyC,EAAcR,EAAQ,QACzB,CAAC,GAAG1I,EAAQ,IAAKmJ,GAAMT,EAAQ,QAAS1I,EAAQ,QAAQmJ,CAAC,CAAC,EAAE,KAAK,CAAC,EAClE,CAAC,GAAGnJ,EAAS,UAAUA,EAAQ,OAAS,CAAC,EAAE,EACxCsC,EAAWjC,EAAK,IAAKuC,GAAQ,CAAC,GAAGA,EAAK,CAAE,MAAO,GAAI,GAAImG,EAAQ,CAAA,CAAG,CAAC,EACzE,OAAItC,IAAU,SACbyC,EAAY,OAAOzC,EAAO,EAAGyC,EAAY,KAAM,EACtC5G,EAAA,QAASM,GAAQA,EAAI,OAAO6D,EAAO,EAAG7D,EAAI,IAAI,CAAE,CAAC,GAEpD,CAAE,KAAMN,EAAU,QAAS4G,CAAY,CAAA,EAGzCE,EAAe,CACpBC,EACAC,IACU,CACND,GAAUC,GACNA,EAAA,OAAO,EAAGA,EAAO,OAAQ,GAAG,KAAK,MAAM,KAAK,UAAUD,CAAM,CAAC,CAAC,CACtE,EAGM,MAAA,CACN,cAAgBE,GACfZ,EAAcE,IAAO,CAAE,qBAAsBU,CAAA,EAAQ,EACtD,YAAa,CAACpJ,EAAKU,IAClB8H,EAAcE,GAAM,CACb,MAAAW,EAAYX,EAAE,WAAW,aAAa,OAC1C5F,GAAMA,EAAE,MAAQ9C,CAAA,EAGhB0I,EAAE,WAAW,aAAa,KACzB5F,GAAMA,EAAE,MAAQ9C,GAAO8C,EAAE,YAAcpC,CAAA,GAGzC2I,EAAU,KAAK,CAAE,IAAArJ,EAAK,UAAAU,CAAW,CAAA,EAG5B,MAAA4I,EACLZ,EAAE,WAAW,eACZH,EAAQ,MAAQc,EAAU,OAAS,EACjC,CACA,KAAM,KAAK,MAAM,KAAK,UAAUd,EAAQ,IAAI,CAAC,EAC7C,cAAeA,EAAQ,cACpB,KAAK,MAAM,KAAK,UAAUA,EAAQ,aAAa,CAAC,EAChD,KACH,QAASA,EAAQ,QACd,KAAK,MAAM,KAAK,UAAUA,EAAQ,OAAO,CAAC,EAC1C,IAEH,EAAA,MAEG,MAAA,CACN,WAAY,CACX,GAAGG,EAAE,WACL,aAAcW,EAAU,MAAM,EAAE,EAChC,aAAAC,CACD,CAAA,CACD,CACA,EACF,cAAe,CAACtJ,EAAKuJ,EAAUC,EAAQxG,IACtCwF,EAAcE,GAAM,CACb,MAAAe,EAAcf,EAAE,aAAa,eAAe,KAChD5F,GAAMA,EAAE,MAAQ9C,CAAA,EAEf0I,EAAE,aAAa,eAAe,OAAQ5F,GAAMA,EAAE,MAAQ9C,CAAG,EACzD,CACA,GAAG0I,EAAE,aAAa,eAClB,CAAE,IAAA1I,EAAK,SAAAuJ,EAAU,OAAAC,EAAQ,MAAAxG,CAAM,CAAA,EAG5BsG,EACLZ,EAAE,aAAa,eACdH,EAAQ,MAAQkB,EAAY,OAAS,EACnC,CACA,KAAM,KAAK,MAAM,KAAK,UAAUlB,EAAQ,IAAI,CAAC,EAC7C,cAAeA,EAAQ,cACpB,KAAK,MAAM,KAAK,UAAUA,EAAQ,aAAa,CAAC,EAChD,KACH,QAASA,EAAQ,QACd,KAAK,MAAM,KAAK,UAAUA,EAAQ,OAAO,CAAC,EAC1C,IAEH,EAAA,MAEG,MAAA,CACN,aAAc,CACb,GAAGG,EAAE,aACL,eAAgBe,EAChB,aAAAH,CACD,CAAA,CACD,CACA,EACF,gBAAiB,CAAC3J,EAAME,IAAY,CAE7B,MAAAC,EADI4J,GAAIpB,CAAK,EACC,WAAW,aAAa,KAC1CvI,GAASF,EAAQE,EAAK,GAAG,IAAMJ,CAAA,EAE1B,OAAAG,EAAYA,EAAU,UAAY,MAC1C,EACA,UAAW,CAACI,EAAMc,EAAeC,IAAY,CACtC,KAAA,CACL,WAAY,CAAE,aAAArB,CAAa,CAAA,EACxB8J,GAAIpB,CAAK,EACT1I,EAAa,QACAyB,GAAAnB,EAAMc,EAAeC,EAASrB,CAAY,CAC5D,EACA,iBAAmBM,GAClBsI,EAAcE,IAAO,CACpB,WAAY,CACX,GAAGA,EAAE,WACL,UACCA,EAAE,WAAW,aAAa,QAAUxI,EAAK,CAAC,EACvC,CAAC,GAAG,MAAMA,EAAK,MAAM,CAAC,EACrB,IAAI,CAAC,EAAGY,IAAMA,CAAC,EACf,KAAK,CAACoC,EAAGC,IAAM,CACf,SAAW,CAAE,IAAAnD,EAAK,UAAAU,CAAe,IAAAgI,EAAE,WACjC,aAAc,CACf,MAAMiB,GACJzJ,EAAKgD,CAAC,IAAIlD,CAAG,GAAG,OAAS,KACzBE,EAAKiD,CAAC,IAAInD,CAAG,GAAG,OAAS,IACvB,GACA,EACA,GAAA2J,EAAa,OAAAjJ,IAAc,MAAQiJ,EAAO,CAACA,CAChD,CACO,MAAA,EACP,CAAA,EACD,CAAC,GAAG,MAAMzJ,EAAK,MAAM,CAAC,EAAE,IAAI,CAAC,EAAGY,IAAMA,CAAC,CAC5C,CAAA,EACC,EACH,YAAcZ,GAAS,CACtB,MAAMkJ,EAAQM,GAAIpB,CAAK,EAAE,sBAAsB,YAAY,EAC3D,OAAOc,EACJlJ,EAAK,OAAQuC,GACbA,EAAI,KAAMwC,GACT,OAAOA,GAAM,KAAK,EAAE,YAAc,EAAA,SAASmE,CAAK,CACjD,CAEA,EAAAlJ,CACJ,EACA,QAAAyI,EACA,QAAAG,EACA,WAAY,CAAC5I,EAAMoG,EAAOsD,EAAUhB,IACnCD,EAAQzI,EAAM0I,EAASgB,IAAa,QAAUtD,EAAQA,EAAQ,CAAC,EAChE,WAAY,CAACpG,EAAML,EAASyG,EAAOsD,EAAUhB,IAC5CE,EAAQ5I,EAAML,EAAS+I,EAASgB,IAAa,OAAStD,EAAQA,EAAQ,CAAC,EACxE,WAAY,CAACpG,EAAMoG,IAClBpG,EAAK,OAAS,EAAIA,EAAK,OAAO,CAAC,EAAGY,IAAMA,IAAMwF,CAAK,EAAIpG,EACxD,WAAY,CAACA,EAAML,EAASyG,IAC3BzG,EAAQ,OAAS,EACd,CACA,KAAMK,EAAK,IAAKuC,GAAQA,EAAI,OAAO,CAACrC,EAAGU,IAAMA,IAAMwF,CAAK,CAAC,EACzD,QAASzG,EAAQ,OAAO,CAACO,EAAGU,IAAMA,IAAMwF,CAAK,CAAA,EAE7C,CAAE,KAAApG,EAAM,QAAAL,CAAQ,EACpB,cAAe,CAACK,EAAMoG,IACrBpG,EAAK,OAAS,EACX,CAAC,GAAGA,EAAK,MAAM,EAAGoG,CAAK,EAAG,GAAGpG,EAAK,MAAMoG,EAAQ,CAAC,CAAC,EAClDpG,EACJ,cAAe,CAACA,EAAML,EAASyG,IAC9BzG,EAAQ,OAAS,EACd,CACA,KAAMK,EAAK,IAAKuC,GAAQ,CACvB,GAAGA,EAAI,MAAM,EAAG6D,CAAK,EACrB,GAAG7D,EAAI,MAAM6D,EAAQ,CAAC,CAAA,CACtB,EACD,QAAS,CAAC,GAAGzG,EAAQ,MAAM,EAAGyG,CAAK,EAAG,GAAGzG,EAAQ,MAAMyG,EAAQ,CAAC,CAAC,CAAA,EAEjE,CAAE,KAAApG,EAAM,QAAAL,CAAQ,EACpB,eAAgB,MACfK,EACAL,EACAgK,EACAC,EACAC,EACAC,IACI,CAEJ,GADUN,GAAIpB,CAAK,EACb,qBAAsB,OAE5B,MAAM2B,EAAkBpK,EAAQ,IAAKmJ,GAAMA,EAAE,KAAK,EAC5CkB,EAAehK,EAAK,IAAKuC,GAC9BA,EAAI,IAAKwC,GAAS,OAAOA,EAAK,KAAK,CAAC,CAAA,GAIpC,CAACkF,GAAOD,EAAcL,CAAa,GACnC,CAACM,GAAOF,EAAiBH,CAAgB,KAEpCK,GAAOF,EAAiBH,CAAgB,GAC5CtB,EAAcE,IAAO,CACpB,WAAY,CAAE,aAAc,GAAI,UAAW,CAAI,EAAA,aAAc,IAAK,EAClE,aAAc,CAAE,eAAgB,GAAI,aAAc,IAAK,CACtD,EAAA,EAEHsB,EAAS,SAAU,CAClB,KAAM9J,EAAK,IAAKuC,GAAQA,EAAI,IAAKwC,GAASA,EAAK,KAAK,CAAC,EACrD,QAASgF,EACT,SAAU,IAAA,CACV,EACIF,GAAiBC,EAAS,OAAO,EAExC,EACA,iBAAkB,IACjBxB,EAAc,GAAM,CACnB,GAAI,EAAE,WAAW,cAAgBD,EAAQ,KAAM,CACxC,MAAA6B,EAAW,EAAE,WAAW,aAEjBnB,EAAAmB,EAAS,KAAM7B,EAAQ,IAAI,EAC3BU,EAAAmB,EAAS,cAAe7B,EAAQ,aAAa,EAC7CU,EAAAmB,EAAS,QAAS7B,EAAQ,OAAO,CAC/C,CAEO,MAAA,CACN,WAAY,CAAE,aAAc,GAAI,UAAW,CAAI,EAAA,aAAc,IAAK,CAAA,CACnE,CACA,EACF,mBAAoB,IACnBC,EAAc,GAAM,CACnB,GAAI,EAAE,aAAa,cAAgBD,EAAQ,KAAM,CAC1C,MAAA6B,EAAW,EAAE,aAAa,aAEnBnB,EAAAmB,EAAS,KAAM7B,EAAQ,IAAI,EAC3BU,EAAAmB,EAAS,cAAe7B,EAAQ,aAAa,EAC7CU,EAAAmB,EAAS,QAAS7B,EAAQ,OAAO,CAC/C,CAEO,MAAA,CACN,aAAc,CAAE,eAAgB,GAAI,aAAc,IAAK,CAAA,CACxD,CACA,EACF,qBAAuB8B,GACtB7B,EAAcE,IAAO,CACpB,SAAU,CAAE,GAAGA,EAAE,SAAU,iBAAkB2B,CAAK,CAAA,EACjD,EACH,uBAAyBA,GACxB7B,EAAcE,IAAO,CACpB,SAAU,CAAE,GAAGA,EAAE,SAAU,mBAAoB2B,CAAK,CAAA,EACnD,EACH,mBAAqBpE,GACpBuC,EAAcE,IAAO,CACpB,SAAU,CAAE,GAAGA,EAAE,SAAU,eAAgBzC,CAAM,CAAA,EAChD,EACH,aAAe/E,GACdsH,EAAcE,IAAO,CAAE,SAAU,CAAE,GAAGA,EAAE,SAAU,SAAAxH,CAAA,CAAa,EAAA,EAChE,YAAcoJ,GACb9B,EAAcE,IAAO,CAAE,SAAU,CAAE,GAAGA,EAAE,SAAU,QAAA4B,CAAA,CAAY,EAAA,EAC/D,eAAgB,IACf9B,EAAc,IAAO,CACpB,SAAU,CACT,iBAAkB,KAClB,mBAAoB,KACpB,eAAgB,CAAC,EACjB,SAAU,GACV,QAAS,GACT,YAAa,GACb,gBAAiB,GACjB,cAAe,KACf,WAAY,EACb,CAAA,EACC,EACH,gBAAkB+B,GACjB/B,EAAcE,IAAO,CACpB,SAAU,CACT,GAAGA,EAAE,SACL,eAAgB,CAAC,EACjB,gBAAiB6B,EACjB,YAAaA,CACd,CAAA,EACC,EACH,oBAAsBA,GACrB/B,EAAcE,IAAO,CACpB,SAAU,CACT,GAAGA,EAAE,SACL,gBAAiB6B,EACjB,SAAU,GACV,eAAgB,CAAC,CAClB,CAAA,EACC,EACH,oBAAqB,CAACvK,EAAKyG,IAC1B+B,EAAcE,IAAO,CACpB,SAAU,CACT,GAAGA,EAAE,SACL,iBAAkB,KAClB,mBAAoB,KACpB,SAAU,GACV,eAAgB,CAAC,EACjB,gBAAiB1I,EACjB,YAAayG,EAAWzG,EAAM,EAC/B,CAAA,EACC,EACH,gBAAkBgH,GAAQ,CACrB,CAAC,SAAU,QAAS,KAAK,EAAE,SAASA,CAAG,GAC1CwB,EAAcE,IAAO,CACpB,SAAU,CAAE,GAAGA,EAAE,SAAU,SAAU,GAAO,YAAa,EAAM,CAC9D,EAAA,CAEJ,EACA,mBAAoB,IAAMgB,GAAIpB,CAAK,EAAE,SAAS,eAC9C,qBAAsB,IAAMoB,GAAIpB,CAAK,EAAE,SAAS,iBAChD,kBAAmB,IAAMoB,GAAIpB,CAAK,EAAE,SAAS,cAC7C,kBAAoBkC,GACnBhC,EAAcE,IAAO,CACpB,SAAU,CAAE,GAAGA,EAAE,SAAU,cAAe8B,CAAO,CAAA,EAChD,EACH,eAAiBxH,GAChBwF,EAAcE,IAAO,CAAE,SAAU,CAAE,GAAGA,EAAE,SAAU,WAAY1F,CAAA,CAAU,EAAA,EACzE,kBAAmB,CAACoD,EAAO3D,EAAKzC,IAAQ,CACvCoG,EAAM,eAAe,EACrBA,EAAM,gBAAgB,EAEhB,MAAAsC,EAAIgB,GAAIpB,CAAK,EACf,GAAAI,EAAE,OAAO,kBAAoB1I,IAAQ,GAAI,OAE7C,IAAIyK,EAAahI,EACb,GAAAiG,EAAE,sBAAwBH,EAAQ,KAAM,CAC3C,MAAMmC,EAA6B,CAAA,EACnCnC,EAAQ,KAAK,QAAQ,CAACoC,EAASC,IAAQ,CAErCD,EAAQ,KAAM1F,GACb,OAAOA,GAAM,KAAK,EAChB,cACA,SAASyD,EAAE,sBAAsB,YAAA,GAAiB,EAAE,CAAA,GAGvDgC,EAAiB,KAAKE,CAAG,CAC1B,CACA,EACYH,EAAAC,EAAiBjI,CAAG,GAAKA,CACvC,CAEA,MAAMwD,EAAQC,GACb,CAACuE,EAAYzK,CAAG,EAChB0I,EAAE,SAAS,eACXtC,CAAA,EAEDoC,EAAcE,IAAO,CACpB,SAAU,CACT,GAAGA,EAAE,SACL,iBAAkB,KAClB,mBAAoB,KACpB,gBAAiB,GACjB,YAAa,GACb,eAAgBzC,EAChB,SAAUA,EAAM,CAAC,CAClB,CACC,EAAA,EAEEyC,EAAE,OAAO,UAAYzC,EAAM,SAAW,GACzCuC,EAAcE,IAAO,CACpB,SAAU,CAAE,GAAGA,EAAE,SAAU,QAAS,CAAC+B,EAAYzK,CAAG,CAAE,CACrD,EAAA,EACFmI,GAAO,EAAA,KAAK,IACXI,EAAQ,IAAKA,EAAQ,KAAMkC,CAAU,EAAEzK,CAAG,EAAE,EAAE,GAAG,OAAO,MAAM,CAAA,GAI1DmI,GAAA,EAAE,KAAK,IAAM,CACbI,EAAQ,gBACXA,EAAQ,eAAe,OACxB,CACA,EAGFA,EAAQ,WAAW,SAAU,CAC5B,MAAO,CAACkC,EAAYzK,CAAG,EACvB,UAAWuI,EAAQ,WAAYvI,CAAG,EAClC,UAAWuI,EAAQ,QAASkC,CAAU,EACtC,MAAOlC,EAAQ,YAAakC,EAAYzK,CAAG,CAAA,CAC3C,CACF,EACA,iBAAkB,CAACoG,EAAO3D,EAAKzC,IAAQ,CACtCoG,EAAM,gBAAgB,EACtB,MAAMyE,EAAenB,GAAIpB,CAAK,EAAE,SAAS,iBACzC,GAAIuC,GAAc,MAAQpI,GAAOoI,EAAa,MAAQ7K,EACrDwI,EAAcE,IAAO,CACpB,SAAU,CAAE,GAAGA,EAAE,SAAU,iBAAkB,IAAK,CACjD,EAAA,MACI,CACN,MAAMzD,EAAQmB,EAAM,OAAuB,QAAQ,IAAI,EACvD,GAAInB,EAAM,CACH,MAAA6F,EAAO7F,EAAK,wBAClBuD,EAAcE,IAAO,CACpB,SAAU,CACT,GAAGA,EAAE,SACL,iBAAkB,CAAE,IAAAjG,EAAK,IAAAzC,EAAK,EAAG8K,EAAK,MAAO,EAAGA,EAAK,MAAO,CAC7D,CACC,EAAA,CACH,CACD,CACD,EACA,mBAAoB,CAACrI,EAAKzC,IAAQ,CACjC,MAAM+K,EAAiBrB,GAAIpB,CAAK,EAAE,SAAS,cACrC0C,EACLD,GAAgB,OAAS,QACzBA,EAAe,MAAQtI,GACvBsI,EAAe,MAAQ/K,EACpB,KACA,CAAE,KAAM,OAAiB,IAAAyC,EAAK,IAAAzC,GAClCwI,EAAcE,IAAO,CACpB,SAAU,CAAE,GAAGA,EAAE,SAAU,cAAesC,CAAW,CACpD,EAAA,CACH,EACA,qBAAuBhL,GAAQ,CAC9B,GAAI,CAACuI,EAAQ,KAAM,OACb,MAAAtC,EAAQsC,EAAQ,KAAK,IAAI,CAAC,EAAG9F,IAAQ,CAACA,EAAKzC,CAAG,CAAmB,EACvEwI,EAAcE,IAAO,CACpB,SAAU,CACT,GAAGA,EAAE,SACL,eAAgBzC,EAChB,SAAUA,EAAM,CAAC,EACjB,QAAS,EACV,CACC,EAAA,EACF,WAAW,IAAMsC,EAAQ,gBAAgB,QAAS,CAAC,CACpD,EACA,kBAAoB9F,GAAQ,CAC3B,GAAI,CAAC8F,EAAQ,MAAQ,CAACA,EAAQ,KAAK,CAAC,EAAG,OACvC,MAAMtC,EAAQsC,EAAQ,KAAK,CAAC,EAAE,IAC7B,CAAC,EAAGvI,IAAQ,CAACyC,EAAKzC,CAAG,CAAA,EAEtBwI,EAAcE,IAAO,CACpB,SAAU,CACT,GAAGA,EAAE,SACL,eAAgBzC,EAChB,SAAUA,EAAM,CAAC,EACjB,QAAS,EACV,CACC,EAAA,EACF,WAAW,IAAMsC,EAAQ,gBAAgB,QAAS,CAAC,CACpD,EACA,0BAAA7B,GACA,oBAAApB,GACA,YAAAwB,EAAA,CAEF,CAEO,SAASmE,GACfC,EACmB,CACnB,MAAM5C,EAAQ6C,GAAyB,CACtC,OAAAD,EACA,qBAAsB,KACtB,WAAY,CAAE,aAAc,GAAI,UAAW,CAAI,EAAA,aAAc,IAAK,EAClE,aAAc,CAAE,eAAgB,GAAI,aAAc,IAAK,EACvD,SAAU,CACT,iBAAkB,KAClB,mBAAoB,KACpB,eAAgB,CAAC,EACjB,SAAU,GACV,QAAS,GACT,YAAa,GACb,gBAAiB,GACjB,cAAe,KACf,WAAY,EACb,CAAA,CACA,EAEK3C,EAA4B,CAAE,MAAAD,EAAO,QAAS,IAAY,EACxDC,EAAA,QAAUF,GAAeC,EAAOC,CAAO,EAE/C,MAAM6C,EAAc,OACnB,aAAa,KAAK,SAAS,SAAS,EAAE,EAAE,UAAU,CAAC,CAAC,EAAA,EAErD,OAAAC,GAAWD,EAAa7C,CAAO,EAC/B8C,GAAWjD,GAAe,CAAE,YAAAgD,EAAa,QAAA7C,CAAS,CAAA,EAE3CA,CACR,0aCppBe+C,EAAS,CAAA,CAAA,0HAJVA,EAAS,CAAA,CAAA,EAAA,iBAAA,EANoBC,GAAAf,EAAA,QAAAgB,EAAA,qCAAAF,EAAW,CAAA,EAAA,KAAAA,EAAA,CAAA,UAC1CA,EAAS,CAAA,CAAA,GACjB,IAAE,iBAAA,gCAEiBA,EAAQ,CAAA,CAAA,EAAA,UAL/BG,GAeQtC,EAAAqB,EAAAkB,CAAA,EARPC,GAOMnB,EAAAoB,CAAA,EANLD,GAKKC,EAAAC,CAAA,EAJJF,GAGCE,EAAAC,CAAA,qEADWR,EAAS,CAAA,CAAA,iBAJVA,EAAS,CAAA,CAAA,EAAA,oCANoBS,EAAA,IAAAP,KAAAA,EAAA,qCAAAF,EAAW,CAAA,EAAA,KAAAA,EAAA,CAAA,UAC1CA,EAAS,CAAA,CAAA,GACjB,IAAE,0DAEiBA,EAAQ,CAAA,CAAA,yFArBnB,CAAA,SAAA1B,CAAA,EAAAoC,EACA,CAAA,OAAAjH,CAAA,EAAAiH,GACA,SAAAC,EAAgC,IAAA,EAAAD,EAkBX,MAAAE,EAAA,IAAAD,GAAYA,0JAhB5CE,EAAA,EAAGC,EACFxC,IAAa,SAAW7E,EAAO,CAAC,IAAM,EAAIA,EAAO,CAAC,IAAM,CAAA,gBACzDoH,EAAA,EAAGzL,EACFkJ,IAAa,SACVwC,EACC,OACA,KACDA,EACC,QACA,MAAA,0sBCOSd,EAAQ,CAAA,UAFTA,EAAU,CAAA,IAAA,iBAAVA,EAAU,CAAA,mEAGXA,EAAa,CAAA,CAAA,kIAL1BG,GAOKtC,EAAAkD,EAAAX,CAAA,6DAHUJ,EAAQ,CAAA,0BAFTA,EAAU,CAAA,8IAhBZ,MAAAtI,EAA0B,EAAA,EAAAgJ,GAC1B,SAAAvF,EAAW,EAAA,EAAAuF,EACX,CAAA,UAAAM,CAAA,EAAAN,WAKFO,EAAcnG,EAAA,CAClBK,GACH6F,EAAUlG,EAAM,MAAM,gBAOXoG,EAAUC,qKAZvBN,EAAA,EAAGK,SACKxJ,GAAU,SAAWA,EAAM,YAAkB,IAAA,OAAW,CAAA,CAAAA,CAAA,42BCP1B,EAAA,OAAA,6FAwG3BsI,EAAS,CAAA,uBACJA,EAAS,CAAA,CAAA,sBACZA,EAAS,CAAA,EAAG,oBAAsB,WAAW,iFAH1DG,GAaCtC,EAAAuD,EAAAhB,CAAA,6DALSJ,EAAW,EAAA,CAAA,yFAIRA,EAAc,EAAA,CAAA,uCAXhBA,EAAS,CAAA,kCACJA,EAAS,CAAA,CAAA,mBACZA,EAAS,CAAA,EAAG,oBAAsB,qMAqCzC,OAAAA,EAAa,CAAA,IAAA,SAAWA,MAAW,MAAK,EAUnCA,OAAa,OAAM,EAEnBA,OAAa,WAAU,iHApB1BA,EAAO,CAAA,CAAA,uBACCA,EAAQ,CAAA,CAAA,wBACPA,EAAS,EAAA,CAAA,uBACVA,EAAI,CAAA,CAAA,uEAXHA,EAAW,EAAA,CAAA,oCAKXA,EAAI,CAAA,CAAA,mBACHA,EAAM,CAAA,CAAA,EAOXqB,GAAAf,EAAA,OAAAN,OAAa,KAAK,cAClBA,EAAS,EAAA,CAAA,UAftBG,GAuCMtC,EAAAyC,EAAAF,CAAA,4CArCOJ,EAAc,EAAA,CAAA,2MAOnBA,EAAO,CAAA,CAAA,uCACCA,EAAQ,CAAA,CAAA,yCACPA,EAAS,EAAA,CAAA,qCACVA,EAAI,CAAA,CAAA,mCAXHA,EAAW,EAAA,CAAA,gEAKXA,EAAI,CAAA,CAAA,kCACHA,EAAM,CAAA,CAAA,iBAOXqB,GAAAf,EAAA,OAAAN,OAAa,KAAK,gCAClBA,EAAS,EAAA,CAAA,6HApBd,MAAA,OAAOA,EAAe,EAAA,CAAA,0BAElBA,EAAkB,EAAA,sEAFtBS,EAAA,CAAA,EAAA,SAAAa,EAAA,MAAA,OAAOtB,EAAe,EAAA,CAAA,iKA0C3BA,EAAY,EAAA,CAAA,+CAAZA,EAAY,EAAA,CAAA,4EANH,QAAAA,MAAa,eAAc,iDAG3B,uEAHAS,EAAA,CAAA,EAAA,UAAAc,EAAA,QAAAvB,MAAa,yPAHhBA,EAAY,EAAA,EAAAnC,EAAAuC,CAAA,sCAAZJ,EAAY,EAAA,CAAA,8DATZ,IAAAwB,EAAAxB,MAAW,oCACR,MAAA,CAAA,IAAKA,EAAY,EAAA,CAAA,aACd,2CAEU,sBAEZ,SAAQyB,EAAA,0GANZ,GAAAhB,EAAA,CAAA,EAAA,MAAAe,KAAAA,EAAAxB,MAAW,OAAK,wKACbS,EAAA,CAAA,EAAA,UAAAiB,EAAA,MAAA,CAAA,IAAK1B,EAAY,EAAA,quBA5CzBA,EAAI,CAAA,GAAIA,EAAQ,CAAA,IAAK,QAAM2B,GAAA3B,CAAA,uCAiB3B,OAAAA,OAAa,OAAM,0BAiDnB,IAAA4B,EAAA5B,EAA0B,EAAA,GAAAA,EAAU,EAAA,GAAAA,OAAoBA,EAAa,EAAA,GAAA6B,GAAA7B,CAAA,iJAlErEA,EAAI,CAAA,GAAIA,EAAQ,CAAA,IAAK,wNAkErBA,EAA0B,EAAA,GAAAA,EAAU,EAAA,GAAAA,OAAoBA,EAAa,EAAA,+OA5HhE8B,GACR/J,EACAgK,EAA4B,KAC5BC,EAAW,GAAA,CAEP,GAAAA,EAAA,OAAiB,OAAOjK,CAAI,EAC1B,MAAAkK,EAAM,OAAOlK,CAAI,EAEnB,MADC,CAAAgK,GAAcA,GAAc,GAC7BE,EAAI,QAAUF,EAAmBE,EAC9BA,EAAI,MAAM,EAAGF,CAAU,EAAI,eAe1BG,GAAUC,EAAA,CAClB,6BAAA,IAAA,CACCA,EAAK,MAAA,oDAhEI,CAAA,KAAAC,CAAA,EAAA1B,GACA,MAAAhJ,EAAyB,EAAA,EAAAgJ,GACzB,cAAAhL,EAA+B,IAAA,EAAAgL,GAC/B,QAAA/K,EAAU,EAAA,EAAA+K,GACV,OAAA2B,EAAS,EAAA,EAAA3B,GACT,SAAAzC,EAOE,KAAA,EAAAyC,EACF,CAAA,iBAAA4B,CAAA,EAAA5B,GAKA,YAAA6B,EAAc,EAAA,EAAA7B,GACd,SAAAvF,EAAW,EAAA,EAAAuF,GACX,UAAA8B,EAAY,EAAA,EAAA9B,GACZ,UAAA+B,EAA2B,IAAA,EAAA/B,EAC3B,CAAA,WAAAgC,EAAA,EAAA,EAAAhC,EACA,CAAA,KAAAiC,CAAA,EAAAjC,GACA,YAAAkC,EAAc,EAAA,EAAAlC,GACd,UAAAmC,EAAY,EAAA,EAAAnC,GAEZ,uBAAAoC,EAAyB,EAAA,EAAApC,EACzB,CAAA,OAAAjH,CAAA,EAAAiH,GACA,iBAAAqC,EAAmD,IAAA,EAAArC,GACnD,cAAAsC,EAAgD,IAAA,EAAAtC,EAChD,CAAA,GAAAuC,CAAA,EAAAvC,QAELhC,EAAWwE,cAqCRC,EAAYrI,EAAA,CACpB4D,EAAS,OAAA,CACR,WAAY5D,EACZ,OAAArB,CAAA,CAAA,WAIO2J,EAAetI,EAAA,CACvB4D,EAAS,UAAW5D,CAAK,WAGjBuI,EAAmBC,EAAA,CAC3BzC,EAAA,EAAAnJ,EAAQ4L,EAAU,SAAA,CAAA,EAClB5E,EAAS,OAAA,CACR,WAAA,CACC,OAAA,CACC,KAAM,WACN,QAAS4E,EACT,MAAOA,EAAU,SAAA,IAGnB,OAAA7J,+IAUUwJ,EAAEM,yDAiEGR,EAAiBtJ,EAAO,CAAC,CAAA,QAKzBuJ,EAAcvJ,EAAO,CAAC,CAAA,uyBAzHvCoH,EAAA,GAAG2C,EAAmB,CAAApB,GAAQK,IAAc,MAAQA,EAAY,CAAA,0BAEhE5B,EAAA,GAAG4C,EAAkBtI,EAClBzD,EACAhC,IAAkB,KACjBA,EACAgC,CAAA,0BAEJmJ,EAAA,GAAG6C,EAAeF,EACf1B,GAAc2B,EAAiBhB,EAAWxE,IAAa,OAAO,EAC9DwF,CAAA,k0ECpDDE,GAAA3D,OAAU,KAAOA,EAAQ,CAAA,EAAA,EAAI,IAAE,kHADcA,EAAK,CAAA,CAAA,wCAApDG,GAEItC,EAAA+F,EAAAxD,CAAA,kBADFK,EAAA,GAAAkD,KAAAA,GAAA3D,OAAU,KAAOA,EAAQ,CAAA,EAAA,EAAI,IAAE,KAAA6D,GAAAC,EAAAH,CAAA,uBADc3D,EAAK,CAAA,CAAA,kPARpDG,GAMItC,EAAAkG,EAAA3D,CAAA,oEAPAJ,EAAS,CAAA,EAAA6B,sNAJF,MAAA7G,EAAuB,IAAA,EAAA0F,GACvB,UAAAsD,EAAY,EAAA,EAAAtD,isBCExBP,GAmBQtC,EAAAqB,EAAAkB,CAAA,iCAfG6D,GAAAjE,OAAAA,EAAQ,CAAA,EAAA,MAAA,KAAA,SAAA,0GAPP,GAAA,CAAA,SAAAW,CAAA,EAAAD,UAQK5F,GAAK,CACpBA,EAAM,eAAc,EACd,MAAAoJ,EAAQpJ,EAAM,QAAQ,CAAC,EACvBqJ,EAAU,IAAO,WAAW,SACjC,QAASD,EAAM,QACf,QAASA,EAAM,QACf,QAAS,GACT,WAAY,GACZ,KAAM,SAEPvD,EAASwD,CAAU,qxBCnBrBhE,GAeKtC,EAAAkD,EAAAX,CAAA,wlBCVGJ,EAAI,CAAA,CAAA,gBACHA,EAAI,CAAA,CAAA,qGAFbG,GAoBKtC,EAAA0C,EAAAH,CAAA,EAbJC,GAMCE,EAAA6D,CAAA,EACD/D,GAKCE,EAAA8D,CAAA,8BAlBMrE,EAAI,CAAA,CAAA,qBACHA,EAAI,CAAA,CAAA,oDALD,KAAAsE,EAAO,EAAA,EAAA5D,srBCIXV,EAAI,CAAA,CAAA,gBACHA,EAAI,CAAA,CAAA,qGAFbG,GAoBKtC,EAAA0C,EAAAH,CAAA,EAbJC,GAMCE,EAAA6D,CAAA,EACD/D,GAKCE,EAAA8D,CAAA,8BAlBMrE,EAAI,CAAA,CAAA,qBACHA,EAAI,CAAA,CAAA,oDALD,KAAAsE,EAAO,EAAA,EAAA5D,67BCoNlBP,GAyBKtC,EAAA0C,EAAAH,CAAA,EAxBJC,GAKCE,EAAA6D,CAAA,EACD/D,GAKCE,EAAA8D,CAAA,EACDhE,GAKCE,EAAAgE,CAAA,EACDlE,GAKCE,EAAAiE,CAAA,oeA7CFrE,GAmBKtC,EAAA0C,EAAAH,CAAA,EAlBJC,GAKCE,EAAA6D,CAAA,EACD/D,GAKCE,EAAA8D,CAAA,EACDhE,GAKCE,EAAAgE,CAAA,4tBAnDFpE,GA+BKtC,EAAA0C,EAAAH,CAAA,EA9BJC,GAKCE,EAAA6D,CAAA,EACD/D,GAKCE,EAAA8D,CAAA,EACDhE,GAKCE,EAAAgE,CAAA,EACDlE,GAKCE,EAAAiE,CAAA,EACDnE,GAKCE,EAAAkE,CAAA,+hBArDFtE,GAqBKtC,EAAA0C,EAAAH,CAAA,EApBJC,GAOCE,EAAA6D,CAAA,EACD/D,GAKCE,EAAA8D,CAAA,EACDhE,GAKCE,EAAAgE,CAAA,giBA3CFpE,GAqBKtC,EAAA0C,EAAAH,CAAA,EApBJC,GAOCE,EAAA6D,CAAA,EACD/D,GAKCE,EAAA8D,CAAA,EACDhE,GAKCE,EAAAgE,CAAA,yYArCFpE,GAeKtC,EAAA0C,EAAAH,CAAA,EAdJC,GAOCE,EAAAf,CAAA,EACDa,GAKCE,EAAAC,CAAA,yYA/BFL,GAeKtC,EAAA0C,EAAAH,CAAA,EAdJC,GAOCE,EAAAf,CAAA,EACDa,GAKCE,EAAAC,CAAA,iaAhCFL,GAgBKtC,EAAA0C,EAAAH,CAAA,EAfJC,GAOCE,EAAAf,CAAA,EACDa,GAMCE,EAAAC,CAAA,8ZAjCFL,GAgBKtC,EAAA0C,EAAAH,CAAA,EAfJC,GAOCE,EAAAf,CAAA,EACDa,GAMCE,EAAAC,CAAA,ibAlCFL,GAiBKtC,EAAA0C,EAAAH,CAAA,EAhBJC,GAQCE,EAAAf,CAAA,EACDa,GAMCE,EAAAC,CAAA,obAnCFL,GAiBKtC,EAAA0C,EAAAH,CAAA,EAhBJC,GAQCE,EAAAf,CAAA,EACDa,GAMCE,EAAAC,CAAA,wDAjBE,GAAAR,MAAQ,mBAAkB,OAAA6B,GAmBrB,GAAA7B,MAAQ,kBAAiB,OAAA0E,GAmBzB,GAAA1E,MAAQ,gBAAe,OAAA2E,GAkBvB,GAAA3E,MAAQ,gBAAe,OAAA4E,GAkBvB,GAAA5E,MAAQ,aAAY,OAAA6E,GAiBpB,GAAA7E,MAAQ,gBAAe,OAAA2B,GAiBvB,GAAA3B,MAAQ,WAAU,OAAA8E,GAuBlB,GAAA9E,MAAQ,YAAW,OAAA+E,GAuBnB,GAAA/E,MAAQ,aAAY,OAAAgF,GAiCpB,GAAAhF,MAAQ,SAAQ,OAAAiF,GAqBhB,GAAAjF,MAAQ,eAAc,OAAAkF,sNAnNpB,GAAA,CAAA,KAAAC,CAAA,EAAAzE,ypBC2HA,OAAAV,QAAsB,MAAK,gCAM5BA,EAAY,EAAA,EAAC,OAAS,GAAC6E,GAAA7E,CAAA,2JAR7BG,GAaKtC,EAAAkD,EAAAX,CAAA,EAZJC,GAMMU,EAAAT,CAAA,yKACDN,EAAY,EAAA,EAAC,OAAS,0LAHJ,EAAE,CAAA,CAAA,uLAFJ,EAAE,CAAA,CAAA,uLAOpBA,EAAa,EAAA,CAAA,sDADfG,GAEMtC,EAAAyC,EAAAF,CAAA,qCADJJ,EAAa,EAAA,CAAA,yOAMjBG,GAIKtC,EAAAkD,EAAAX,CAAA,EAHJC,GAEMU,EAAAT,CAAA,qrBApCD,KAAAN,OAAgBA,EAAC,CAAA,sDAcd,OAAA,CAAAA,KAAG,CAAC,8JAET,IAAAoF,EAAApF,YAAiB4E,GAAA5E,CAAA,EAgBjBqF,EAAArF,YAAmB2E,GAAA,IAQpB3E,EAAS,EAAA,GAAA0E,GAAA,IAIV1E,EAAe,EAAA,GAAA6B,GAAA7B,CAAA,0MApDXA,EAAK,CAAA,CAAA,uFAtBJC,GAAA8D,EAAA,YAAAuB,EAAAlR,GAAgB4L,EAAK,CAAA,EAAEA,EAAc,EAAA,EAAAA,QAAa,OAC1D,OACA5L,GAAgB4L,EAAK,CAAA,EAAEA,EAAc,EAAA,EAAAA,QAAa,MACjD,YACA,YAAY,EACDuF,GAAAxB,EAAA,QAAA/D,KAAeA,EAAC,CAAA,CAAA,CAAA,EAAWuF,GAAAxB,EAAA,OAAA/D,MAAoBA,EAAC,CAAA,CAAA,CAAA,eAMxDA,EAAK,CAAA,CAAA,gCAhBSqB,GAAA0C,EAAA,gBAAA/D,KAAIA,EAAqB,CAAA,CAAA,qBAC3BA,EAAC,CAAA,IAAKA,EAAqB,CAAA,EAAG,CAAC,EACrCqB,GAAA0C,EAAA,QAAA/D,EAAgB,CAAA,IAAAA,EAAK,CAAA,GAAAA,OAAoBA,EAAC,CAAA,CAAA,EACzCqB,GAAA0C,EAAA,SAAA/D,UAAiB,EACfqB,GAAA0C,EAAA,WAAA/D,UAAmB,UALpCG,GAoFItC,EAAAkG,EAAA3D,CAAA,EAjEHC,GAgEK0D,EAAAyB,CAAA,EA/DJnF,GA2DKmF,EAAAC,CAAA,EA1DJpF,GAsDQoF,EAAAvG,CAAA,0UAvCAuB,EAAA,CAAA,EAAA,KAAAiF,EAAA,KAAA1F,OAAgBA,EAAC,CAAA,8FAcdS,EAAA,CAAA,EAAA,IAAAiF,EAAA,OAAA,CAAA1F,KAAG,CAAC,oGAETA,oHAgBAA,uIAxCEA,EAAK,CAAA,CAAA,EAgDRA,EAAS,EAAA,kGAIVA,EAAe,EAAA,gHA1EV,CAAAnF,GAAA4F,EAAA,CAAA,EAAA,MAAA6E,KAAAA,EAAAlR,GAAgB4L,EAAK,CAAA,EAAEA,EAAc,EAAA,EAAAA,QAAa,OAC1D,OACA5L,GAAgB4L,EAAK,CAAA,EAAEA,EAAc,EAAA,EAAAA,QAAa,MACjD,YACA,oDACWuF,GAAAxB,EAAA,QAAA/D,KAAeA,EAAC,CAAA,CAAA,CAAA,gBAAWuF,GAAAxB,EAAA,OAAA/D,MAAoBA,EAAC,CAAA,CAAA,CAAA,6BAMxDA,EAAK,CAAA,CAAA,iBAhBSqB,GAAA0C,EAAA,gBAAA/D,KAAIA,EAAqB,CAAA,CAAA,oCAC3BA,EAAC,CAAA,IAAKA,EAAqB,CAAA,EAAG,CAAC,iBACrCqB,GAAA0C,EAAA,QAAA/D,EAAgB,CAAA,IAAAA,EAAK,CAAA,GAAAA,OAAoBA,EAAC,CAAA,CAAA,qBACzCqB,GAAA0C,EAAA,SAAA/D,UAAiB,sBACfqB,GAAA0C,EAAA,WAAA/D,UAAmB,oMAmBjBlF,GAAK,CACnBA,EAAM,eAAc,EACpBA,EAAM,gBAAe,MAbVA,GAAK,CACnBA,EAAM,eAAc,EACpBA,EAAM,gBAAe,oCAzEX,CAAA,MAAApD,CAAA,EAAAgJ,EACA,CAAA,EAAAlL,CAAA,EAAAkL,EACA,CAAA,sBAAAiF,CAAA,EAAAjF,EACA,CAAA,YAAAkF,CAAA,EAAAlF,EACA,CAAA,gBAAAmF,CAAA,EAAAnF,EACA,CAAA,QAAAnM,CAAA,EAAAmM,EACA,CAAA,eAAAoF,CAAA,EAAApF,EACA,CAAA,oBAAAqF,CAAA,EAAArF,EACA,CAAA,mBAAAsF,CAAA,EAAAtF,EACA,CAAA,gBAAAuF,CAAA,EAAAvF,EACA,CAAA,aAAApM,EAAA,EAAA,EAAAoM,EACA,CAAA,eAAAvK,EAAA,EAAA,EAAAuK,EAOA,CAAA,iBAAA4B,CAAA,EAAA5B,EAKA,CAAA,YAAA6B,CAAA,EAAA7B,EACA,CAAA,UAAA+B,CAAA,EAAA/B,EACA,CAAA,SAAAvF,CAAA,EAAAuF,EACA,CAAA,KAAAiC,CAAA,EAAAjC,EACA,CAAA,GAAAuC,CAAA,EAAAvC,EACA,CAAA,UAAA8B,CAAA,EAAA9B,EACA,CAAA,UAAAwF,CAAA,EAAAxF,WASFyF,EAAoBC,EAAA,QACxBA,GAAaT,EACT,OAGJS,IAAc,EACV,YAGgB,MAAMA,CAAS,EACrC,KAAK,CAAC,EACN,KAAKtR,EAAGwK,IACDwG,EAAexG,CAAG,CAEzB,EAAA,KAAK,KAAK,CAEkB,+DAyCdxE,GAAK,EAEhBA,EAAM,OAAO,MAAQ,SACrBA,EAAM,OAAO,MAAQ,UACrBA,EAAM,OAAO,MAAQ,QAErBmL,EAAgBnL,CAAK,GApBb8F,EAAA9F,GAAUiL,EAAoBjL,EAAOtF,CAAC,EA0DvBiM,EAAA3G,GAAUkL,EAAmBlL,EAAOtF,CAAC,EArEvD6Q,GAAAvL,GAAUiL,EAAoBjL,EAAOtF,CAAC,y2BAvCjDqL,EAAA,GAAGyF,EAAkBJ,GAAaA,EAAU,CAAC,IAAM,SAAA,sBACnDrF,EAAA,GAAG0F,EAAajS,EAAa,UAAWG,GAASA,EAAK,MAAQe,CAAC,CAAA,0BAC/DqL,EAAA,GAAG2F,EAAerQ,EAAe,UAAW1B,GAASA,EAAK,MAAQe,CAAC,CAAA,wBACnEqL,EAAA,GAAG4F,EAAgBF,IAAA,GAAoBA,EAAa,EAAI,IAAA,wBACxD1F,EAAA,GAAG6F,EACFH,IAAe,GAAKjS,EAAaiS,CAAU,EAAE,UAAY,IAAA,i6FCyGpDvG,EAAQ,EAAA,GAAIA,EAAuB,EAAA,EAAA,CAAAA,KAAOA,EAAC,CAAA,CAAA,EAAGA,EAAc,CAAA,EAAEA,EAAQ,EAAA,CAAA,mEA7B3D,cAAAA,EAAkB,EAAA,IAAA,OAC9BA,EAAA,EAAA,EACA,OAAOA,EAAK,CAAA,CAAA,+EAKTA,EAAO,EAAA,GAAIA,EAAO,EAAA,EAAC,CAAC,IAAMA,EAAK,CAAA,GAAIA,EAAO,EAAA,EAAC,CAAC,IAAMA,EAAC,CAAA,6DAajC,uBAAAA,KAAe,SAAW,GACjDA,KAAe,CAAC,EAAE,CAAC,IAAMA,MACzBA,EAAc,CAAA,EAAC,CAAC,EAAE,CAAC,IAAMA,EAAC,CAAA,EAClB,OAAA,CAAAA,KAAOA,EAAC,CAAA,CAAA,mBACCA,EAAoB,EAAA,gBACvBA,EAAiB,EAAA,8BAErBA,EAAI,EAAA,iCA5BNA,KAAG,QAAK,SAAR2G,EAAA,GAAA3G,KAAG,+HAiBHiE,GAAAjE,OAAAA,EAAW,CAAA,EAAA,MAAA,KAAA,SAAA,+HAxCZC,GAAA2D,EAAA,WAAAgD,EAAA5G,EAAI,CAAA,EAAAA,EAAyB,CAAA,EAAA,GAAI,CAAC,kBAElCA,EAAK,CAAA,CAAA,kBACLA,EAAC,CAAA,CAAA,EACUC,GAAA2D,EAAA,cAAAiD,EAAA,QAAA7G,QAASA,EAAC,CAAA,CAAA,EAAA,2BAGhBA,EAAc,CAAA,EAACA,EAAY,CAAA,CAAA,EAAA,WAAAA,MAAkBA,EAAC,CAAA,CAAA,EAAA,MAAKA,EAAO,EAAA,GACxE,GAAE,gCAVkBqB,GAAAuC,EAAA,gBAAA5D,KAAIA,EAAqB,CAAA,CAAA,qBAC3BA,EAAC,CAAA,IAAKA,EAAqB,CAAA,EAAG,CAAC,EAUrCqB,GAAAuC,EAAA,QAAA5D,OAAcA,EAAe,EAAA,CAAA,uBACrBA,EAAe,EAAA,CAAA,gBACtBA,EAAU,EAAA,CAAA,mBACPA,EAAa,EAAA,CAAA,iBACfA,EAAW,EAAA,CAAA,kBACVA,EAAY,EAAA,CAAA,qBACTA,EAAgB,EAAA,GAClCA,EAAgB,EAAA,EAAC,MAAQA,EAAK,CAAA,GAC9BA,EAAgB,EAAA,EAAC,MAAQA,EAAC,CAAA,CAAA,kBACXA,EAAW,EAAA,CAAA,UArB5BG,GA4DItC,EAAA+F,EAAAxD,CAAA,EArCHC,GAoCKuD,EAAA7C,CAAA,+IAhCYN,EAAA,CAAA,EAAA,WAAAiF,EAAA,cAAA1F,EAAkB,EAAA,IAAA,OAC9BA,EAAA,EAAA,EACA,OAAOA,EAAK,CAAA,CAAA,mKAKTA,EAAO,EAAA,GAAIA,EAAO,EAAA,EAAC,CAAC,IAAMA,EAAK,CAAA,GAAIA,EAAO,EAAA,EAAC,CAAC,IAAMA,EAAC,CAAA,mIAajCS,EAAA,CAAA,EAAA,KAAAiF,EAAA,uBAAA1F,KAAe,SAAW,GACjDA,KAAe,CAAC,EAAE,CAAC,IAAMA,MACzBA,EAAc,CAAA,EAAC,CAAC,EAAE,CAAC,IAAMA,EAAC,CAAA,GAClBS,EAAA,CAAA,EAAA,KAAAiF,EAAA,OAAA,CAAA1F,KAAOA,EAAC,CAAA,CAAA,qCACCA,EAAoB,EAAA,mCACvBA,EAAiB,EAAA,qEAErBA,EAAI,EAAA,kEA5BN0F,EAAA,GAAA1F,KAAG,8CA8BRA,EAAQ,EAAA,GAAIA,EAAuB,EAAA,EAAA,CAAAA,KAAOA,EAAC,CAAA,CAAA,EAAGA,EAAc,CAAA,EAAEA,EAAQ,EAAA,CAAA,kHArDlE,CAAAnF,GAAA4F,EAAA,CAAA,EAAA,IAAAmG,KAAAA,EAAA5G,EAAI,CAAA,EAAAA,EAAyB,CAAA,EAAA,GAAI,sDAEjCA,EAAK,CAAA,CAAA,iCACLA,EAAC,CAAA,CAAA,GACU,CAAAnF,GAAA4F,EAAA,CAAA,EAAA,IAAAoG,KAAAA,EAAA,QAAA7G,QAASA,EAAC,CAAA,CAAA,6DAGhBA,EAAc,CAAA,EAACA,EAAY,CAAA,CAAA,EAAA,WAAAA,MAAkBA,EAAC,CAAA,CAAA,EAAA,MAAKA,EAAO,EAAA,GACxE,sCAVoBqB,GAAAuC,EAAA,gBAAA5D,KAAIA,EAAqB,CAAA,CAAA,oCAC3BA,EAAC,CAAA,IAAKA,EAAqB,CAAA,EAAG,CAAC,0BAUrCqB,GAAAuC,EAAA,QAAA5D,OAAcA,EAAe,EAAA,CAAA,qCACrBA,EAAe,EAAA,CAAA,8BACtBA,EAAU,EAAA,CAAA,0CACPA,EAAa,EAAA,CAAA,uCACfA,EAAW,EAAA,CAAA,wCACVA,EAAY,EAAA,CAAA,sCACTA,EAAgB,EAAA,GAClCA,EAAgB,EAAA,EAAC,MAAQA,EAAK,CAAA,GAC9BA,EAAgB,EAAA,EAAC,MAAQA,EAAC,CAAA,CAAA,uCACXA,EAAW,EAAA,CAAA,2KA9GhB,CAAA,MAAAtI,CAAA,EAAAgJ,EACA,CAAA,MAAA1F,CAAA,EAAA0F,EACA,CAAA,EAAAzK,CAAA,EAAAyK,EACA,CAAA,sBAAAiF,CAAA,EAAAjF,EACA,CAAA,eAAAoF,CAAA,EAAApF,EACA,CAAA,kBAAAoG,CAAA,EAAApG,EAKA,CAAA,YAAAyC,CAAA,EAAAzC,EAMA,CAAA,iBAAAqG,CAAA,EAAArG,EAKA,CAAA,iBAAAhH,CAAA,EAAAgH,EAIA,CAAA,sBAAAxF,CAAA,EAAAwF,EAKA,CAAA,eAAArJ,CAAA,EAAAqJ,EACA,CAAA,WAAAsG,CAAA,EAAAtG,EACA,CAAA,iBAAAuG,CAAA,EAAAvG,EAMA,CAAA,QAAA/K,CAAA,EAAA+K,EACA,CAAA,iBAAA4B,CAAA,EAAA5B,EAKA,CAAA,YAAA6B,CAAA,EAAA7B,EACA,CAAA,SAAAzC,CAAA,EAAAyC,EACA,CAAA,QAAA1B,CAAA,EAAA0B,EACA,CAAA,UAAA+B,CAAA,EAAA/B,EACA,CAAA,SAAAvF,CAAA,EAAAuF,GACA,UAAA8B,EAAY,EAAA,EAAA9B,EACZ,CAAA,KAAAiC,CAAA,EAAAjC,EACA,CAAA,WAAAgC,GAAA,EAAA,EAAAhC,EACA,CAAA,GAAAuC,CAAA,EAAAvC,EAIA,CAAA,qBAAAwG,CAAA,EAAAxG,EACA,CAAA,kBAAAyG,EAAA,EAAAzG,EACA,CAAA,YAAAkC,CAAA,EAAAlC,EACA,CAAA,cAAAhL,CAAA,EAAAgL,GACA,KAAA0G,EAAO,EAAA,EAAA1G,WAET2G,EAAkBjB,EAAA,QACtBA,GAAaT,EACT,OAGJS,IAAc,EACV,YAGgB,MAAMA,CAAS,EACrC,KAAK,CAAC,EACN,KAAKtR,GAAGwK,IACDwG,EAAexG,CAAG,CAEzB,EAAA,KAAK,KAAK,CAEkB,2CAqCpBgI,EAAA,GAAA,UAAArE,EAAG,MAAKvL,CAAA,IAARuL,EAAG,MAAKvL,uBAWV,MAAAP,EAAM6D,EACNtG,EAAMuB,EACPoB,EAAe,KAAO,CAAA,CAAAE,GAAGC,CAAC,IAAMD,KAAMJ,GAAOK,IAAM9C,CAAG,OAC1D2C,EAAc,CAAA,CAAKF,EAAKzC,CAAG,CAAA,CAAA,GAiBF+M,EAAA3G,GAAUiM,EAAiBjM,EAAOE,EAAO/E,CAAC,6CArD5DgN,EAAG,KAAIM,WAIH,MAAAgE,GAAAnO,GAAM0N,EAAkB1N,EAAG4B,EAAO/E,CAAC,EAClBuR,EAAApO,GAAM2N,EAAiB3N,EAAG4B,EAAO/E,CAAC,8uCAjBlE4K,EAAA,GAAG4G,EAAe/N,EAAA,CAAkBsB,EAAO/E,CAAC,EAAGoB,GAAA,CAAA,CAAA,CAAA,oBAC/CwJ,EAAA,GAAG6G,EAAkBlO,GAAA,CAAsBwB,EAAO/E,CAAC,EAAGoB,CAAc,CAAA,oBACjEwJ,EAAA,GAAA8G,EAAaF,EAAa,SAAS,QAAQ,CAAA,oBAC3C5G,EAAA,GAAA+G,EAAgBH,EAAa,SAAS,WAAW,CAAA,oBACjD5G,EAAA,GAAAgH,EAAcJ,EAAa,SAAS,SAAS,CAAA,oBAC7C5G,EAAA,GAAAiH,EAAeL,EAAa,SAAS,UAAU,CAAA,oiHCzFnDtH,GAEQtC,EAAAqB,EAAAkB,CAAA,gCAFiC6D,GAAAjE,OAAAA,EAAQ,CAAA,EAAA,MAAA,KAAA,SAAA,+EAHrC,GAAA,CAAA,SAAAW,CAAA,EAAAD,4wBCAF,SAAAqH,GAAA,KAAAlL,6BAA4C,OAAA,iKA2NvB,KAAAmD,MAAK,KAAa,MAAAA,MAAK,yEAD1CA,EAAO,EAAA,CAAA,EAAU,MAAAgI,EAAAhI,GAAAA,EAAK,EAAA,EAAA,KAAK,CAAC,EAAE,mBAAnC,OAAIxK,GAAA,EAAA,sMAACwK,EAAO,EAAA,CAAA,sFAAZ,OAAIxK,GAAA,6JACiD;AAAA,OAEtD,qcAJGwK,EAAO,EAAA,EAAC,QAAUA,MAAQ,CAAC,EAAE,KAAK,QAAM6B,GAAA7B,CAAA,iZAN9BiI,EAAM,wBAAoBjI,EAAG,CAAA,EAAA,IAAA,2BAAyBA,EAAM,CAAA,EAAA,IAAA,8BAA4BA,EAAW,CAAA,EAAA,IAAA,8BAA4BA,EAAW,CAAA,EAAA,IAAA,iCAA+BA,EAAc,CAAA,EAAA,IAAA,sBAAoBA,EAAU,CAAA,EAAA,IAAA,wBAJ/MA,EAAc,CAAA,CAAA,UAJvCG,GA2B+BtC,EAAAqK,EAAA9H,CAAA,EA1B9BC,GAyBK6H,EAAAnH,CAAA,EAxBJV,GAuBOU,EAAA1E,CAAA,EAfNgE,GAEOhE,EAAA8L,CAAA,+CACP9H,GAQOhE,EAAA+L,CAAA,kCACP/H,GAEOhE,EAAAgM,CAAA,uGAjBIrI,EAAkB,EAAA,CAAA,4FAOvBA,EAAO,EAAA,EAAC,QAAUA,MAAQ,CAAC,EAAE,KAAK,mOANEA,EAAG,CAAA,EAAA,IAAA,0CAAyBA,EAAM,CAAA,EAAA,IAAA,8CAA4BA,EAAW,CAAA,EAAA,IAAA,8CAA4BA,EAAW,CAAA,EAAA,IAAA,+CAA+BA,EAAc,CAAA,EAAA,IAAA,oCAAoBA,EAAU,CAAA,EAAA,IAAA,sCAJ/MA,EAAc,CAAA,CAAA,wKA5LlCiI,GAAS,2DAhBF,CAAA,MAAAK,EAAA,EAAA,EAAA5H,EAEA,CAAA,WAAA6H,CAAA,EAAA7H,EACA,CAAA,cAAA8H,CAAA,EAAA9H,EACA,CAAA,sBAAA+H,CAAA,EAAA/H,GACA,MAAAzG,EAAQ,CAAA,EAAAyG,GACR,IAAAxG,EAAM,EAAA,EAAAwG,EACN,CAAA,SAAA9K,CAAA,EAAA8K,GACA,eAAAgI,EAAiB,EAAA,EAAAhI,GACjB,mBAAAiI,EAAqB,EAAA,EAAAjI,EACrB,CAAA,SAAAkI,CAAA,EAAAlI,EAEMwC,GAAA,MAMb2F,EAAiB,GACjBC,EAAS,EACTC,EACAC,EAAc,EACdC,EAAc,EACdC,EAAA,CAAA,EACAC,EACAxR,EACAyR,EAAM,EACNC,EAAkB,IAClBC,EAAA,CAAA,EACAC,EAIE,MAAAC,EAAA,OAAoB,OAAW,IAC/BC,EAAMD,EACT,OAAO,sBACNE,GAAiCA,IAQtB,eAAAC,GAAA,CACVC,EAAY,OAAS3P,SAClB4P,EAAgBD,EAAY,OAAS,EAAA,CAAK,SAAU,MAAA,CAAA,EAGrD,MAAAE,EAAY,KAAK,IAAI,EAAGlB,EAAS,SAAS,EAChD/H,EAAA,GAAA8H,EAAqBmB,EAAY,GAAA,OACjCrB,EAAwBG,EAAS,YAAcA,EAAS,WAAA,EAG/C,QAAAmB,EAAI,EAAGA,EAAIpS,EAAK,OAAQoS,GAAK,EACrCb,EAAWjP,EAAQ8P,CAAC,EAAIpS,EAAKoS,CAAC,EAAE,sBAAwB,EAAA,WAErDvU,EAAI,EACJwU,EAAIhB,EAED,KAAAxT,EAAIoU,EAAY,QAAA,OAChBK,EAAaf,EAAW1T,CAAC,GAAKqT,EAEhC,GAAAmB,EAAIC,EAAaH,EAAYvB,EAAA,MAChCtO,EAAQzE,CAAA,EACRqL,EAAA,EAAAuI,EAAMY,EAAIhB,CAAA,QAGXgB,GAAKC,EACLzU,GAAK,MAGF0U,EAAiBlB,EACd,KAAAxT,EAAIoU,EAAY,QAAA,OAChBK,EAAaf,EAAW1T,CAAC,GAAKqT,EAIhC,GAHJqB,GAAkBD,EAClBzU,GAAK,EAED0U,EAAiBlB,EAAc,EAAIT,aAKxCrO,EAAM1E,CAAA,EACA,MAAA2U,GAAYP,EAAY,OAAS1P,EAEjCkQ,GAAmBxB,EAAS,aAAeA,EAAS,aACtDwB,GAAmB,IACtBF,GAAkBE,IAGf,IAAAC,EAAsBnB,EAAW,OAAQa,GAAa,OAAAA,GAAM,QAAQ,EAUjE,QATPlB,EACCwB,EAAoB,QAAQzS,EAAGC,IAAMD,EAAIC,EAAG,CAAC,EAC5CwS,EAAoB,QAAU,EAAA,EAEhCxJ,EAAA,EAAAiI,EAASqB,GAAYtB,CAAA,EAChB,SAASC,CAAM,OACnBA,EAAS,GAAA,EAEVI,EAAW,OAASU,EAAY,OACzBpU,EAAIoU,EAAY,QACtBpU,GAAK,EACL0T,EAAW1T,CAAC,EAAIqT,EAEbN,GAAc2B,EAAiB3B,OAClCC,EAAgBD,CAAA,OAEhBC,EAAgB0B,CAAA,iBAMHI,GAAkBC,EAAA,CAChCd,EAAA,SAAA,WACYc,GAAM,SAAA,OACX,MAAAnV,EAAA,OAAmBmV,GAAM,SAAW,GAAQC,EAAWD,CAAC,EAC1DnV,IAAc,KAGdA,IAAc,QACX,MAAAyU,EAAgBU,GAAK,SAAU,SAAA,CAAA,EAGlCnV,IAAc,kBACXyU,EAAgBU,EAAA,CAAK,SAAU,SAAA,EAAa,EAAI,cAKhDC,EAAWD,EAAA,OACb1P,EAAUlD,GAAQA,EAAK4S,EAAItQ,CAAK,EACjC,GAAA,CAAAY,GAAW0P,EAAItQ,EACZ,MAAA,OAEH,GAAA,CAAAY,GAAW0P,GAAKrQ,EAAM,EACnB,MAAA,WAGA,KAAA,CAAA,IAAKuQ,GAAiB7B,EAAS,sBAAA,GAC/B,IAAAQ,EAAK,OAAAN,IAAWjO,EAAQ,wBAE5BuO,OAAAA,EAAMqB,EAAe,GACjB,OAGJ3B,GAAS2B,EAAepB,EACpB,WAGD,kBAGcQ,EACrB7O,EACA0P,EACAC,EAAY,GAAA,CAEN,MAAA9N,GAAA,QAEA+N,EAAc/B,EAEhB,IAAAgC,GAAW7P,EAAQ4P,EACnBD,IACHE,GAAWA,GAAWxB,EAAkBuB,EAAc5B,SAGjDoB,GAAmBxB,EAAS,aAAeA,EAAS,aACtDwB,GAAmB,IACtBS,IAAYT,IAGP,MAAAU,EAAA,CACL,IAAKD,GACL,SAAU,SACP,GAAAH,GAGJ9B,EAAS,SAASkC,CAAK,EAexB/C,GAAA,IAAA,CACCpQ,EAAOoR,EAAS,cAChBI,EAAU,EAAA,kBAc+BH,EAAW,KAAA,8DAGjCD,EAAQxF,wBASc0F,EAAW,KAAA,8DAjBxCL,EAAQrF,wBACDgG,EAAYwB,GAAA,QAAA,IAAA,IAAA,GAAA,wfA/K7BlK,EAAA,GAAAwI,EAAkBE,GAAc,QAAU,GAAA,wBAuJ7C1I,EAAA,GAAG+I,EAActB,CAAA,yBA/IZa,GAAWE,GAAmBT,EAAS,cAC7Ba,EAAIE,CAAkB,wBAwElCW,GAAkB1U,CAAQ,yBAwE7BiL,EAAA,GAAGyI,EAAUE,EACVI,EAAY,MAAM3P,EAAOC,CAAG,EAAE,KAAKtF,EAAMY,KAChC,CAAA,MAAOA,EAAIyE,EAAO,KAAArF,CAAA,IAE3BgV,EACC,MAAM,EAAIrB,EAAaqB,EAAY,OAAUf,EAAiB,CAAC,EAC/D,IAAA,CAAKjU,EAAMY,KACF,CAAA,MAAOA,EAAIyE,EAAO,KAAArF,CAAA,snDClMP,EAAA,OAAA,yGAoFZoW,EAAAC,GAAAjL,KAAeA,EAAQ,CAAA,CAAA,CAAA,uBAA5B,OAAI,GAAA,gJADPG,GAYKtC,EAAAkD,EAAAX,CAAA,oEAXG4K,EAAAC,GAAAjL,KAAeA,EAAQ,CAAA,CAAA,CAAA,oBAA5B,OAAIxK,GAAA,EAAA,mHAAJ,wDAQCwK,EAAG,EAAA,EAAA,0IAPLG,GAQQtC,EAAAqB,EAAAkB,CAAA,0EADNJ,EAAG,EAAA,EAAA,KAAA6D,GAAAqH,EAAAC,CAAA,wHAVHnL,EAAoB,CAAA,GAAA6B,GAAA7B,CAAA,oJAfxBA,EAAQ,CAAA,CAAA,qDAWPA,EAAc,CAAA,CAAA,sOAb6BA,EAAQ,CAAA,CAAA,GAAA,uIAWfA,EAAc,CAAA,CAAA,GAAA,6FAyB7CA,EAAkB,CAAA,+NA9C7BG,GA6DKtC,EAAAuN,EAAAhL,CAAA,EA5DJC,GAA6B+K,EAAA3F,CAAA,UAC7BpF,GA0DK+K,EAAAC,CAAA,EAzDJhL,GAWKgL,EAAA7F,CAAA,EAVJnF,GAAqBmF,EAAAlF,CAAA,UACrBD,GAQQmF,EAAA8F,CAAA,kBAGTjL,GAoCKgL,EAAAE,CAAA,EAnCJlL,GAyBKkL,EAAAC,CAAA,EAxBJnL,GAOQmL,EAAAC,CAAA,8DAmBTpL,GAOCkL,EAAAG,CAAA,UAGFrL,GAKQgL,EAAAM,CAAA,uHAXI3L,EAAmB,CAAA,CAAA,uDApC5BA,EAAQ,CAAA,CAAA,oDAFoCA,EAAQ,CAAA,CAAA,4CAanDA,EAAc,CAAA,CAAA,4CAFsBA,EAAc,CAAA,CAAA,4BAM/CA,EAAoB,CAAA,iFAmBlBA,EAAkB,CAAA,aAAlBA,EAAkB,CAAA,8LAhGjB,GAAA,CAAA,UAAA4L,EAAA,IAAA,MAMPC,EACA5N,EAAgC,SAChC6N,EAAiB,WACjBC,EAAuB,GACvBC,EAAqB,GAEnB,MAAAC,EAAA,CACL,OAAA,CACC,WACA,mBACA,cACA,YACA,KACA,SACA,WACA,gBAED,OAAA,CAAS,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,WAAY,cAAc,GAGlElE,GAAA,IAAA,CACCmE,MAGQ,SAAAA,GAAA,CACH,GAAA,CAAAL,EAAA,OAEC,MAAAM,EAAiB,OAAO,WACxB9C,EAAkB,OAAO,YACzB+C,EAAYP,EAAa,wBAEzBQ,GAAKF,EAAiBC,EAAU,OAAS,EACzCpC,GAAKX,EAAkB+C,EAAU,QAAU,MAEjDP,EAAa,MAAM,QAAUQ,CAAC,KAAAR,CAAA,MAC9BA,EAAa,MAAM,OAAS7B,CAAC,KAAA6B,CAAA,WAGrBS,EAAoBlT,EAAA,CACtB,MAAAyE,EAASzE,EAAE,OACjByH,EAAA,EAAAmL,EAAqBnO,EAAO,KAAA,+CAWzBgD,EAAA,EAAA5C,EAAWA,IAAa,SAAW,SAAW,QAAQ,EACtD4C,EAAA,EAAAiL,EAAiBG,EAAehO,CAAQ,EAAE,CAAC,CAAA,GAYzCsO,EAAA,IAAA1L,EAAA,EAAAkL,GAAwBA,CAAoB,QAY1ClL,EAAA,EAAAiL,EAAiBU,CAAG,EACpB3L,EAAA,EAAAkL,GAAwBA,CAAoB,GAuBlCU,EAAA,IAAAb,EAAU3N,EAAU6N,EAAgBE,CAAkB,4CAtDxDH,EAAYtI,iqBCzDJ,EAAA,OAAA,oDA2FrBmJ,EAAA1M,MAAK,0BAA0B,EAAA,eAW/B2M,EAAA3M,MAAK,2BAA2B,EAAA,eAOhC4M,EAAA5M,MAAK,sBAAsB,EAAA,aAQ3B6M,EAAA7M,MAAK,kBAAkB,EAAA,eAOvB8M,GAAA9M,MAAK,wBAAwB,EAAA,gDAhCzB,IAAAoF,EAAApF,EAAmB,EAAA,IAAA,OAASA,QAAkB,MAAIgF,GAAAhF,CAAA,uCAWlD,IAAAqF,EAAArF,EAAmB,EAAA,IAAA,QAAUA,QAAkB,MAAI+E,GAAA/E,CAAA,gFAenDA,EAAa,EAAA,GAAA8E,GAAA,uaA9BJzD,GAAAiK,EAAA,SAAAtL,QAAmB,KAAK,sDAWxBqB,GAAAoK,EAAA,SAAAzL,QAAmB,MAAM,0GAezBqB,GAAA0L,EAAA,SAAA/M,OAAiBA,EAAkB,EAAA,CAAA,8DA7BlDG,GAUQtC,EAAAyN,EAAAlL,CAAA,gEACRD,GAUQtC,EAAA4N,EAAArL,CAAA,gEACRD,GAGQtC,EAAA8N,EAAAvL,CAAA,yCACRD,GAUQtC,EAAAkP,EAAA3M,CAAA,gEACRD,GAGQtC,EAAAmP,EAAA5M,CAAA,4GAlB0B6D,GAAAjE,OAAAA,EAAa,CAAA,EAAA,MAAA,KAAA,SAAA,oBAMpBA,EAAkB,EAAA,CAAA,CAAA,0BASXiE,GAAAjE,QAAAA,EAAe,EAAA,EAAA,MAAA,KAAA,SAAA,yBA/B/C,CAAAnF,GAAA4F,EAAA,CAAA,EAAA,QAAAiM,KAAAA,EAAA1M,MAAK,0BAA0B,EAAA,KAAA6D,GAAAoJ,EAAAP,CAAA,EAC3B1M,EAAmB,EAAA,IAAA,OAASA,QAAkB,gFAJrCqB,GAAAiK,EAAA,SAAAtL,QAAmB,KAAK,GAcrC,CAAAnF,GAAA4F,EAAA,CAAA,EAAA,QAAAkM,KAAAA,EAAA3M,MAAK,2BAA2B,EAAA,KAAA6D,GAAAqJ,EAAAP,CAAA,EAC5B3M,EAAmB,EAAA,IAAA,QAAUA,QAAkB,gFAJtCqB,GAAAoK,EAAA,SAAAzL,QAAmB,MAAM,GAUtC,CAAAnF,GAAA4F,EAAA,CAAA,EAAA,QAAAmM,KAAAA,EAAA5M,MAAK,sBAAsB,EAAA,KAAA6D,GAAAsJ,EAAAP,CAAA,GAQ3B,CAAA/R,GAAA4F,EAAA,CAAA,EAAA,QAAAoM,KAAAA,EAAA7M,MAAK,kBAAkB,EAAA,KAAA6D,GAAAuJ,EAAAP,CAAA,EACnB7M,EAAa,EAAA,sEAJJqB,GAAA0L,EAAA,SAAA/M,OAAiBA,EAAkB,EAAA,CAAA,GAUhD,CAAAnF,GAAA4F,EAAA,CAAA,EAAA,QAAAqM,MAAAA,GAAA9M,MAAK,wBAAwB,EAAA,KAAA6D,GAAAwJ,EAAAP,EAAA,iaA/BL9M,EAAa,EAAA,CAAA,gDAArCG,GAA4CtC,EAAAyC,EAAAF,CAAA,kCAApBJ,EAAa,EAAA,CAAA,uEAWbA,EAAa,EAAA,CAAA,gDAArCG,GAA4CtC,EAAAyC,EAAAF,CAAA,kCAApBJ,EAAa,EAAA,CAAA,gIAerCG,GAA8BtC,EAAAyC,EAAAF,CAAA,4CAgB9BsM,EAAA1M,MAAK,yBAAyB,EAAA,aAQ9BsN,EAAAtN,MAAK,yBAAyB,EAAA,uGAE3BA,EAAe,CAAA,GAAA6E,GAAA7E,CAAA,+TAhBpBG,GAOQtC,EAAAyN,EAAAlL,CAAA,yCACRD,GAOQtC,EAAA4N,EAAArL,CAAA,mIATN,CAAAvF,GAAA4F,EAAA,CAAA,EAAA,QAAAiM,KAAAA,EAAA1M,MAAK,yBAAyB,EAAA,KAAA6D,GAAAoJ,EAAAP,CAAA,GAQ9B,CAAA7R,GAAA4F,EAAA,CAAA,EAAA,QAAA6M,KAAAA,EAAAtN,MAAK,yBAAyB,EAAA,KAAA6D,GAAA0J,EAAAD,CAAA,EAE3BtN,EAAe,CAAA,8UAQjB0M,EAAA1M,MAAK,sBAAsB,EAAA,+MAP7BG,GAQQtC,EAAAqB,EAAAkB,CAAA,kEANG6D,GAAAjE,OAAAA,EAAa,CAAA,EAAA,MAAA,KAAA,SAAA,uBAKtB,CAAAnF,GAAA4F,EAAA,CAAA,EAAA,QAAAiM,KAAAA,EAAA1M,MAAK,sBAAsB,EAAA,KAAA6D,GAAAoJ,EAAAP,CAAA,8HAW5BA,EAAA1M,MAAK,2BAA2B,EAAA,aAQhCsN,EAAAtN,MAAK,4BAA4B,EAAA,4GAE9BA,EAAe,CAAA,GAAA2E,GAAA3E,CAAA,kVAhBpBG,GAOQtC,EAAAyN,EAAAlL,CAAA,yCACRD,GAOQtC,EAAA4N,EAAArL,CAAA,mIATN,CAAAvF,GAAA4F,EAAA,CAAA,EAAA,QAAAiM,KAAAA,EAAA1M,MAAK,2BAA2B,EAAA,KAAA6D,GAAAoJ,EAAAP,CAAA,GAQhC,CAAA7R,GAAA4F,EAAA,CAAA,EAAA,QAAA6M,KAAAA,EAAAtN,MAAK,4BAA4B,EAAA,KAAA6D,GAAA0J,EAAAD,CAAA,EAE9BtN,EAAe,CAAA,+UAQjB0M,EAAA1M,MAAK,yBAAyB,EAAA,qNAPhCG,GAQQtC,EAAAqB,EAAAkB,CAAA,kEANG6D,GAAAjE,OAAAA,EAAa,CAAA,EAAA,MAAA,KAAA,SAAA,uBAKtB,CAAAnF,GAAA4F,EAAA,CAAA,EAAA,QAAAiM,KAAAA,EAAA1M,MAAK,yBAAyB,EAAA,KAAA6D,GAAAoJ,EAAAP,CAAA,qYAlG7B1M,EAAS,EAAA,GAAA2B,GAAA3B,CAAA,EA4CRqF,EAAA,CAAArF,OAAaA,EAAY,EAAA,GAAA4E,GAAA5E,CAAA,IA6B1BA,EAAe,EAAA,GAAA0E,GAAA1E,CAAA,IA+BhBA,EAAkB,EAAA,GAAA6B,GAAA7B,CAAA,2JAzGvBG,GAuGKtC,EAAAkD,EAAAX,CAAA,qHAtGCJ,EAAS,EAAA,4GA4CR,CAAAA,OAAaA,EAAY,EAAA,4GA6B1BA,EAAe,EAAA,8GA+BhBA,EAAkB,EAAA,8SAnLX,CAAA,EAAAqM,CAAA,EAAA3L,EACA,CAAA,EAAAsJ,CAAA,EAAAtJ,EACA,CAAA,iBAAA8M,CAAA,EAAA9M,EACA,CAAA,iBAAA+M,CAAA,EAAA/M,EACA,CAAA,mBAAAgN,CAAA,EAAAhN,EACA,CAAA,oBAAAiN,CAAA,EAAAjN,EACA,CAAA,IAAAvJ,CAAA,EAAAuJ,EACA,CAAA,UAAAwF,CAAA,EAAAxF,EACA,CAAA,UAAAkN,CAAA,EAAAlN,EACA,CAAA,cAAAmN,CAAA,EAAAnN,EACA,CAAA,cAAAoN,CAAA,EAAApN,EACA,CAAA,gBAAAqN,CAAA,EAAArN,EACA,CAAA,gBAAAsN,CAAA,EAAAtN,EACA,CAAA,QAAAuN,EAAA,IAAA,MACA,CAAA,cAAAC,EAAA,IAAA,OACA,eAAAC,EAAuC,IAAA,EAAAzN,GACvC,cAAA+F,EAA+B,IAAA,EAAA/F,EAC/B,CAAA,UAAAkL,EAAA,IAAA,MAKA,CAAA,gBAAAwC,EAAA,IAAA,OACA,cAAAC,EAAgC,IAAA,EAAA3N,GAChC,SAAAvF,EAAW,EAAA,EAAAuF,EAEX,CAAA,KAAAiC,CAAA,EAAAjC,EACPmL,EACAyC,EAAsD,KAM1DvG,GAAA,IAAA,CACCmE,MAGQ,SAAAA,GAAA,CACH,GAAA,CAAAL,EAAA,OAEC,MAAAM,EAAiB,OAAO,WACxB9C,EAAkB,OAAO,YACzB+C,EAAYP,EAAa,wBAE3B,IAAA0C,EAAQlC,EAAI,GACZmC,GAAQxE,EAAI,GAEZuE,EAAQnC,EAAU,MAAQD,IAC7BoC,EAAQlC,EAAID,EAAU,MAAQ,IAG3BoC,GAAQpC,EAAU,OAAS/C,IAC9BmF,GAAQxE,EAAIoC,EAAU,OAAS,SAGhCP,EAAa,MAAM,QAAU0C,CAAK,KAAA1C,CAAA,OAClCA,EAAa,MAAM,OAAS2C,EAAK,KAAA3C,CAAA,EAGzB,SAAA4C,IAAA,CACJ,GAAAJ,EAAA,CACHzC,EAAU,SAAU,GAAI,EAAE,SAIrB,MAAAQ,EAAYP,EAAa,wBAC/BhL,EAAA,GAAAyN,EAAA,CACC,EAAGlC,EAAU,MACb,EAAGA,EAAU,IAAMA,EAAU,OAAS,IAStB,MAAAxL,EAAA,IAAAqN,EAAQ,KAAK,EAWb5H,EAAA,IAAA4H,EAAQ,MAAM,SAiCdT,UAQAC,UAqBAC,UAQAC,8CArFH9B,EAAYtI,y8BA5C3B1C,EAAA,GAAGmD,EAAY7M,IAAQ,EAAA,2BACvB0J,EAAA,GAAG6N,EAAevT,GAAYyS,EAAU,CAAC,IAAM,SAAA,2BAC/C/M,EAAA,GAAGyF,EAAkBnL,GAAY+K,EAAU,CAAC,IAAM,SAAA,gzFCvCxB,EAAA,OAAA,sDACY,OAAA,kEA2D9BlG,EAAoB,CAAA,GAAIA,EAAW,CAAA,IAAK,UAAQ6E,GAAA7E,CAAA,2EAP7C0L,EAAA,MAAAiD,EAAA3O,MAAwB,GAElBC,GAAAyL,EAAA,cAAAkD,EAAA5O,OAAgB,SAAW,YAAc,WAAW,6EAGzCA,EAAW,CAAA,CAAA,YAAA,EADhBqB,GAAAqK,EAAA,cAAA1L,OAAgB,QAAQ,yDAP7CG,GAoBKtC,EAAAkD,EAAAX,CAAA,EAnBJC,GAQCU,EAAA2K,CAAA,iDALU1L,EAAmB,CAAA,CAAA,iBADtB,CAAAnF,GAAA4F,EAAA,GAAAkO,KAAAA,EAAA3O,MAAwB,KAAE0L,EAAA,QAAAiD,iBAEpB,CAAA9T,GAAA4F,EAAA,GAAAmO,KAAAA,EAAA5O,OAAgB,SAAW,YAAc,uEAG9BA,EAAW,CAAA,CAAA,2CADhBqB,GAAAqK,EAAA,cAAA1L,OAAgB,QAAQ,EAGvCA,EAAoB,CAAA,GAAIA,EAAW,CAAA,IAAK,ucAC5CG,GAOQtC,EAAAqB,EAAAkB,CAAA,kDALG6D,GAAAjE,OAAAA,EAAgB,CAAA,EAAA,MAAA,KAAA,SAAA,wMAgBvBA,EAAM,CAAA,EAAA,4HAHCA,EAAM,CAAA,EAAG,sBAAwB,iBAAiB,iBACvDA,EAAM,CAAA,EAAG,sBAAwB,iBAAiB,UAJ1DG,GAWQtC,EAAAqB,EAAAkB,CAAA,yCATGJ,EAAW,CAAA,CAAA,sJACTA,EAAM,CAAA,EAAG,sBAAwB,4DACtCA,EAAM,CAAA,EAAG,sBAAwB,wvBA5BrCoF,EAAApF,OAAgB,QAAM4E,GAAA5E,CAAA,IAuBtBA,EAAgB,CAAA,GAAA0E,GAAA1E,CAAA,IAchBA,EAAsB,CAAA,GAAA6B,GAAA7B,CAAA,oOAvC7BG,GA2CKtC,EAAA2H,EAAApF,CAAA,EA1CJC,GAyCKmF,EAAAC,CAAA,8EAxCCzF,OAAgB,wGAuBhBA,EAAgB,CAAA,mGAchBA,EAAsB,CAAA,wOAnFjB,uBAAA6O,EAAyB,EAAA,EAAAnO,GACzB,iBAAAoO,EAAmB,EAAA,EAAApO,GACnB,YAAAqO,EAA4C,MAAA,EAAArO,GAC5C,WAAAsO,EAAa,EAAA,EAAAtO,EACb,CAAA,QAAAuO,CAAA,EAAAvO,EACA,CAAA,iBAAAwO,CAAA,EAAAxO,QAELhC,EAAWwE,SAIbiM,EAAS,GACTC,GACO,qBAAAC,EAAsC,IAAA,EAAA3O,EAC7C4O,EAAc,YAETC,EAAoBnW,EAAA,CAE5BkW,EADelW,EAAE,OACI,MACf,MAAAoW,EAAYF,GAAe,KAC7BD,IAAyBG,QAC5BH,EAAuBG,CAAA,EACvB9Q,EAAS,SAAU2Q,CAAoB,GAIhC,SAAAI,GAAA,KACRN,EAAS,EAAA,EACLC,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,oBACPD,EAAS,EAAA,GACP,KAGW,eAAAO,GAAA,CACR,MAAAT,EAAA,EACNQ,IAGDE,GAAA,IAAA,CACKP,GAAO,aAAaA,CAAK,syCC5CxB,SAASQ,GACfC,EACA3J,EACA9J,EAIAkB,EACiB,CACb,IAAAwS,EAAKD,GAAS,GACd,GAAA3J,EAAU,CAAC,IAAM,SAAW4J,EAAG,OAAS5J,EAAU,CAAC,EAAG,CACzD,MAAM6J,EAAO,MAAM7J,EAAU,CAAC,EAAI4J,EAAG,MAAM,EACzC,KAAK,EAAE,EACP,IAAI,CAAChb,EAAGU,IAAM,GAAGA,EAAIsa,EAAG,MAAM,EAAE,EAC7BA,EAAAA,EAAG,OAAOC,CAAI,CACpB,CAEA,MAAI,CAACD,GAAMA,EAAG,SAAW,EACjB,MAAM5J,EAAU,CAAC,CAAC,EACvB,KAAK,CAAC,EACN,IAAI,CAACpR,EAAGU,IAAM,CACd,MAAMwa,EAAM1S,IACZ,OAAAlB,EAAI4T,CAAG,EAAI,CAAE,KAAM,KAAM,MAAO,MACzB,CAAE,GAAIA,EAAK,MAAO,KAAK,UAAUxa,EAAI,CAAC,EAAE,CAC/C,EAGIsa,EAAG,IAAI,CAACpS,EAAGlI,IAAM,CACvB,MAAMwa,EAAM1S,IACZ,OAAAlB,EAAI4T,CAAG,EAAI,CAAE,KAAM,KAAM,MAAO,MACzB,CAAE,GAAIA,EAAK,MAAOtS,GAAK,EAAG,CAAA,CACjC,CACF,CAEO,SAASuS,GACfC,EACA9T,EAIA+T,EACA7S,EACA5H,EAAmC,KACkC,CACrE,MAAI,CAACwa,GAAUA,EAAO,SAAW,EACzB,GAGOA,EAAO,IAAI,CAAC/Y,EAAK3B,IACxB2B,EAAI,IAAI,CAACO,EAAOzB,IAAM,CAC5B,MAAM+Z,EAAM1S,IACZlB,EAAI4T,CAAG,EAAI,CAAE,KAAM,KAAM,MAAO,MAChCG,EAAaH,CAAG,EAAItY,EAEpB,IAAI0Y,EAAU1a,IAAgBF,CAAC,IAAIS,CAAC,EAEpC,OAAIma,IAAY,SACfA,EAAU,OAAO1Y,CAAK,GAGhB,CACN,GAAIsY,EACJ,MAAAtY,EACA,cAAe0Y,CAAA,CAChB,CACA,CACD,CAGF,CCpDgB,SAAAC,GACftG,EACAjG,EAC4B,CAC5B,GAAIA,IAAM,SAAU,CACb,MAAAyG,EAAI,OAAOR,CAAC,EACX,OAAA,MAAMQ,CAAC,EAAIR,EAAIQ,CACvB,CACA,GAAIzG,IAAM,OAAQ,CACjB,GAAI,OAAOiG,GAAM,UAAkB,OAAAA,EACnC,GAAI,OAAOA,GAAM,SAAU,OAAOA,IAAM,EACxC,MAAM3M,EAAI,OAAO2M,CAAC,EAAE,YAAY,EAC5B,OAAA3M,IAAM,QAAUA,IAAM,IAAY,GAClCA,IAAM,SAAWA,IAAM,IAAY,GAChC2M,CACR,CACA,GAAIjG,IAAM,OAAQ,CACX,MAAAwM,EAAI,IAAI,KAAKvG,CAAC,EACpB,OAAO,MAAMuG,EAAE,QAAA,CAAS,EAAIvG,EAAIuG,EAAE,aACnC,CACO,OAAAvG,CACR,CCrCA,KAAA,MAAAlN,EAAqB,EAAA,OAAA,2BAIrB,eAAe0T,GACdjB,EACAtP,EACA7I,EACAzC,EACgB,CAChB,GAAI,CAACsL,EAAI,MAAQ,CAACA,EAAI,KAAK7I,CAAG,GAAK,CAAC6I,EAAI,KAAK7I,CAAG,EAAEzC,CAAG,EAAG,OAExD,MAAM8b,EAAYxQ,EAAI,KAAK7I,CAAG,EAAEzC,CAAG,EAAE,MACrCsL,EAAI,KAAK7I,CAAG,EAAEzC,CAAG,EAAE,MAAQ4a,EAEvBkB,IAAclB,GAAetP,EAAI,UACpCA,EAAI,SAAS,SAAU,CACtB,KAAMA,EAAI,KAAK,IAAK7I,GAAQA,EAAI,IAAKwC,GAASA,EAAK,KAAK,CAAC,EACzD,QAASqG,EAAI,SAAS,IAAKtC,GAAMA,EAAE,KAAK,GAAK,CAAC,EAC9C,SAAU,IAAA,CACV,EAGFsC,EAAI,QAAQ,aAAa,CAAC7I,EAAKzC,CAAG,CAAC,CACpC,CAEsB,eAAA+b,GACrB3V,EACAkF,EACAvG,EACgB,CAChB,GAAI,CAACuG,EAAI,MAAQ,CAACA,EAAI,SAAW,CAACA,EAAI,IAAK,OAE3C,MAAM0Q,EAAW5V,EAAM,OACnB,CAAC4V,GAAYA,EAAS,QAAU,QAE9B,MAAAH,GACLG,EAAS,OAAS,WAAa,OAAOA,EAAS,OAAO,EAAIA,EAAS,MACnE1Q,EACAvG,EAAO,CAAC,EACRA,EAAO,CAAC,CAAA,CAEV,CAEA,SAASkX,GACR7V,EACAkF,EACU,CACJ,MAAAhD,EAAQoB,GAAI4B,EAAI,KAAK,EACrB6F,EAAkB7I,EAAM,SAAS,gBACjC4I,EAAc5I,EAAM,SAAS,YAC7BzI,EAAUyL,EAAI,SAAW,GAE3B,GAAA6F,IAAoB,IAASD,IAAgB,GAAc,MAAA,GAE/D,OAAQ9K,EAAM,IAAK,CAClB,IAAK,YACA,OAAAkF,EAAA,QAAQ,oBAAoB,EAAK,EACrCA,EAAI,QAAQ,aAAa,CAAC,EAAG6F,CAAyB,CAAC,EACvD7F,EAAI,QAAQ,mBAAmB,CAAC,CAAC,EAAG6F,CAAyB,CAAC,CAAC,EACxD,GACR,IAAK,YACJ,OAAA7F,EAAI,QAAQ,oBACX6F,EAAkB,EAAIA,EAAkB,EAAIA,CAAA,EAEtC,GACR,IAAK,aACJ,OAAA7F,EAAI,QAAQ,oBACX6F,EAAkBtR,EAAQ,OAAS,EAChCsR,EAAkB,EAClBA,CAAA,EAEG,GACR,IAAK,SACJ,OAAA/K,EAAM,eAAe,EACjBkF,EAAA,QAAQ,oBAAoB,EAAK,EAC9B,GACR,IAAK,QACJ,OAAAlF,EAAM,eAAe,EACjBkC,EAAM,OAAO,UACZgD,EAAA,QAAQ,gBAAgB6F,CAAe,EAErC,EACT,CACO,MAAA,EACR,CAGA,SAAS+K,GACR9V,EACAkF,EACU,CACN,GAAA,CAACA,EAAI,MAAQ,CAACA,EAAI,SAAW,CAACA,EAAI,KAAO,CAACA,EAAI,SAAiB,MAAA,GAE7D,MAAAhD,EAAQoB,GAAI4B,EAAI,KAAK,EAE3B,GADI,CAAChD,EAAM,OAAO,UACdlC,EAAM,MAAQ,UAAYA,EAAM,MAAQ,YAAoB,MAAA,GAE1D,MAAAkE,EAAUhC,EAAM,SAAS,QACzB3F,EAAiB2F,EAAM,SAAS,eAEhC6T,EAAiB7T,EAAM,OAAO,gBAAkB,CAAA,EAClD,GAAA3F,EAAe,KAAK,CAAC,CAACvC,EAAGJ,CAAG,IAAMmc,EAAe,SAASnc,CAAG,CAAC,EAC1D,MAAA,GAGR,GAAIsK,EAAS,CACN,KAAA,CAAC7H,EAAKzC,CAAG,EAAIsK,EACb0R,EAAW1Q,EAAI,IAAIA,EAAI,KAAK7I,CAAG,EAAEzC,CAAG,EAAE,EAAE,GAAG,MAUjD,GATIgc,GAAYA,EAAS,iBAAmBA,EAAS,cAIpD5V,EAAM,MAAQ,UACd4V,GAAU,iBAAmBA,GAAU,MAAM,QAI1C5V,EAAM,MAAQ,aAAe4V,GAAU,iBAAmB,EACtD,MAAA,EAET,CAGI,GADJ5V,EAAM,eAAe,EACjBzD,EAAe,OAAS,EAAG,CAC9B,MAAMR,EAAWoE,GAAkB+E,EAAI,KAAM3I,CAAc,EAC3D2I,EAAI,SAAS,SAAU,CACtB,KAAMnJ,EAAS,IAAKM,GAAQA,EAAI,IAAKwC,GAASA,EAAK,KAAK,CAAC,EACzD,QAASqG,EAAI,QAAQ,IAAKtC,GAAMA,EAAE,KAAK,EACvC,SAAU,IAAA,CACV,CACF,CACO,MAAA,EACR,CAEA,SAASoT,GACRhW,EACAkF,EACAxK,EACAS,EACU,CACJ,MAAA+G,EAAQoB,GAAI4B,EAAI,KAAK,EACrBhB,EAAUhC,EAAM,SAAS,QACzB3F,EAAiB2F,EAAM,SAAS,eAGtC,GADIgC,GACA,CAACgB,EAAI,KAAa,MAAA,GAEtBlF,EAAM,eAAe,EAEf,MAAAiW,EAAc/Q,EAAI,QAAQ,YAAYlF,EAAO,CAACtF,EAAGS,CAAC,EAAG+J,EAAI,IAAI,EACnE,OAAI+Q,GACCjW,EAAM,UACTkF,EAAI,QAAQ,mBACXA,EAAI,QAAQ,oBACX3I,EAAe,OAAS,EAAIA,EAAe,CAAC,EAAI,CAAC7B,EAAGS,CAAC,EACrD8a,CACD,CAAA,EAEG/Q,EAAA,QAAQ,YAAY,EAAK,IAE7BA,EAAI,QAAQ,mBAAmB,CAAC+Q,CAAW,CAAC,EACxC/Q,EAAA,QAAQ,YAAY,EAAK,GAE1BA,EAAA,QAAQ,aAAa+Q,CAAW,GAC1BA,IAAgB,IAASjW,EAAM,MAAQ,WAAatF,IAAM,IAChEwK,EAAA,QAAQ,oBAAoB/J,CAAC,EAC7B+J,EAAA,QAAQ,aAAa,EAAK,EAC1BA,EAAA,QAAQ,mBAAmB,CAAA,CAAE,EAC7BA,EAAA,QAAQ,YAAY,EAAK,GAEvB,EACR,CAEA,eAAegR,GACdlW,EACAkF,EACAxK,EACAS,EACmB,CACnB,GAAI,CAAC+J,EAAI,MAAQ,CAACA,EAAI,IAAY,MAAA,GAE5B,MAAAhD,EAAQoB,GAAI4B,EAAI,KAAK,EACvB,GAAA,CAAChD,EAAM,OAAO,SAAiB,MAAA,GAE7B,MAAAgC,EAAUhC,EAAM,SAAS,QAC/B,GAAIgC,GAAWlE,EAAM,SAAiB,MAAA,GAItC,GAFAA,EAAM,eAAe,EAEjBkE,GAAWH,GAAOG,EAAS,CAACxJ,EAAGS,CAAC,CAAC,EAAG,CACvC,MAAMqG,EAAU0D,EAAI,KAAKxK,CAAC,EAAES,CAAC,EAAE,GACzBya,EAAW1Q,EAAI,IAAI1D,CAAO,GAAG,MAC/BoU,GACH,MAAMH,GAAgBG,EAAS,MAAO1Q,EAAKxK,EAAGS,CAAC,EAE5C+J,EAAA,QAAQ,YAAY,EAAK,EAC7B,MAAMnD,GAAK,EACXmD,EAAI,gBAAgB,OAAM,MAE1BA,EAAI,QAAQ,YAAY,CAACxK,EAAGS,CAAC,CAAC,EAGxB,MAAA,EACR,CAEA,SAASgb,GACRnW,EACAkF,EACAxK,EACAS,EACU,CACV,GAAI,CAAC+J,EAAI,KAAa,MAAA,GAEtBlF,EAAM,eAAe,EACjBkF,EAAA,QAAQ,YAAY,EAAK,EACvB,MAAAkR,EAAYlR,EAAI,QAAQ,0BAC7B,CAACxK,EAAGS,CAAC,EACL+J,EAAI,KACJlF,EAAM,QAAA,EAEP,OAAIoW,IACHlR,EAAI,QAAQ,mBAAmB,CAACkR,CAAS,CAAC,EACtClR,EAAA,QAAQ,aAAakR,CAAS,EAC9B9S,GAAI4B,EAAI,KAAK,EAAE,OAAO,UACrBA,EAAA,QAAQ,YAAYkR,CAAS,GAG5B,EACR,CAEA,SAASC,GACRrW,EACAkF,EACAxK,EACAS,EACU,CACJ,MAAA+G,EAAQoB,GAAI4B,EAAI,KAAK,EACvB,GAAA,CAAChD,EAAM,OAAO,SAAiB,MAAA,GAE7B,MAAAgC,EAAUhC,EAAM,SAAS,QAE/B,OACE,CAACgC,GAAYA,GAAWH,GAAOG,EAAS,CAACxJ,EAAGS,CAAC,CAAC,IAC/C6E,EAAM,IAAI,SAAW,GAErBkF,EAAI,QAAQ,YAAY,CAACxK,EAAGS,CAAC,CAAC,EACvB,IAED,EACR,CAEA,eAAemb,GACdtW,EACAkF,EACmB,CACnB,GAAI,CAACA,EAAI,KAAa,MAAA,GAEhB,MAAAhD,EAAQoB,GAAI4B,EAAI,KAAK,EACrBpK,EAAWoH,EAAM,SAAS,SAC1B3F,EAAiB2F,EAAM,SAAS,eAEtC,GAAI,CAACpH,EAAiB,MAAA,GACtB,GAAIkF,EAAM,MAAQ,MAAQA,EAAM,SAAWA,EAAM,SAChD,OAAAA,EAAM,eAAe,EACjBzD,EAAe,OAAS,GACrB,MAAAD,GAAgB4I,EAAI,KAAM3I,CAAc,EAE3C2I,EAAA,QAAQ,eAAe,EAAI,EACxB,GAGF,KAAA,CAACxK,EAAGS,CAAC,EAAIL,EAEf,OAAQkF,EAAM,IAAK,CAClB,IAAK,aACL,IAAK,YACL,IAAK,YACL,IAAK,UACJ,OAAOgW,GAAkBhW,EAAOkF,EAAKxK,EAAGS,CAAC,EAC1C,IAAK,SACA,OAAC+G,EAAM,OAAO,UAClBlC,EAAM,eAAe,EACjBkF,EAAA,QAAQ,YAAY,EAAK,EACxBnD,GAAA,EAAE,KAAK,IAAM,CACbmD,EAAI,gBACPA,EAAI,eAAe,OACpB,CACA,EAEM,IAT4B,GAUpC,IAAK,QACJ,OAAO,MAAMgR,GAAiBlW,EAAOkF,EAAKxK,EAAGS,CAAC,EAC/C,IAAK,MACJ,OAAOgb,GAAenW,EAAOkF,EAAKxK,EAAGS,CAAC,EACvC,IAAK,SACL,IAAK,YACG,OAAA2a,GAAwB9V,EAAOkF,CAAG,EAC1C,QACC,OAAOmR,GAAmBrW,EAAOkF,EAAKxK,EAAGS,CAAC,CAC5C,CACD,CAEsB,eAAAmN,GACrBtI,EACAmC,EACgB,CACZ0T,GAAyB7V,EAAOmC,CAAO,GACvC2T,GAAwB9V,EAAOmC,CAAO,GACpC,MAAAmU,GAAuBtW,EAAOmC,CAAO,CAC5C,CC1SO,SAASoU,GACfrU,EACAsU,EACAC,EACAC,EACA1K,EACA2K,EACAC,EACe,CACf,MAAMC,EAAa,CAAC7W,EAAmB3D,EAAazC,IAAsB,CACzE,MAAMmJ,EAAS/C,EAAM,OACf8W,EACJ/T,EAA4B,OAAS,YACtCA,EAAO,QAAQ,wBAAwB,GACvCA,EAAO,QAAQ,YAAY,EAG3B/C,EAAM,kBAAkB,mBACvB2W,GAAoB/c,IAAQ,IAC7Bkd,IAID9W,EAAM,eAAe,EACrBA,EAAM,gBAAgB,EAEtBkC,EAAM,eAAiB,CAAE,EAAGlC,EAAM,QAAS,EAAGA,EAAM,SAC9CkC,EAAA,WAAa,CAAC7F,EAAKzC,CAAG,EAExB,CAACoG,EAAM,UAAY,CAACA,EAAM,SAAW,CAACA,EAAM,UAC/CyW,EAAmB,CAAC,CAACpa,EAAKzC,CAAG,CAAC,CAAC,EAClB8c,EAAA,CAACra,EAAKzC,CAAG,CAAC,EACLoS,EAAAhM,EAAO3D,EAAKzC,CAAG,GAClC,EAGKmd,EAAoB/W,GAA4B,CACrD,MAAMnB,EAAQmB,EAAM,OAAuB,QAAQ,IAAI,EACvD,GAAI,CAACnB,EAAM,OAEX,MAAMxC,EAAM,SAASwC,EAAK,aAAa,UAAU,GAAK,GAAG,EACnDjF,EAAM,SAASiF,EAAK,aAAa,UAAU,GAAK,GAAG,EAEzD,GAAI,MAAMxC,CAAG,GAAK,MAAMzC,CAAG,EAAG,OAE9B,MAAMod,EAAkB9X,GAAoBgD,EAAM,WAAa,CAAC7F,EAAKzC,CAAG,CAAC,EACzE6c,EAAmBO,CAAe,EACrBN,EAAA,CAACra,EAAKzC,CAAG,CAAC,CAAA,EAGlBqd,EAAYjX,GAA4B,CACzC,CAACkC,EAAM,aAAeA,EAAM,WACb8J,EAAAhM,EAAOkC,EAAM,WAAW,CAAC,EAAGA,EAAM,WAAW,CAAC,CAAC,EACvDA,EAAM,aAAe0U,GAC/BA,EAAe,MAAM,EAGtB1U,EAAM,YAAc,GACpBsU,EAAgB,EAAK,EACrBtU,EAAM,WAAa,KACnBA,EAAM,eAAiB,IAAA,EAGjB,MAAA,CACN,kBAAmB2U,EAEnB,kBAAkB7W,EAAyB,CAC1C,GAAI,CAACkC,EAAM,YAAc,CAACA,EAAM,eAAgB,OAE5C,GAAA,EAAElC,EAAM,QAAU,GAAI,CACzBiX,EAASjX,CAAK,EACd,MACD,CAEA,MAAMkX,EAAK,KAAK,IAAIlX,EAAM,QAAUkC,EAAM,eAAe,CAAC,EACpDiV,EAAK,KAAK,IAAInX,EAAM,QAAUkC,EAAM,eAAe,CAAC,EAEtD,CAACA,EAAM,cAAgBgV,EAAK,GAAKC,EAAK,KACzCjV,EAAM,YAAc,GACpBsU,EAAgB,EAAI,GAGjBtU,EAAM,aACT6U,EAAiB/W,CAAK,CAExB,EAEA,gBAAiBiX,CAAA,CAEnB,wkBC/FuB,sBAAA7O,GAAA,KAAArG,aAA4C,EAAA,OAAA,0bAoyB3DqV,EAAAlS,MAASA,EAAK,CAAA,EAAC,SAAW,GAAKA,EAAU,CAAA,GAAAkF,GAAAlF,CAAA,gJAa3BA,EAAa,EAAA,EACT,qBAAAA,MAAU,6KAflCG,GAiBKtC,EAAAkD,EAAAX,CAAA,mDAhBCJ,MAASA,EAAK,CAAA,EAAC,SAAW,GAAKA,EAAU,CAAA,kQAcvBS,EAAA,CAAA,EAAA,IAAA0R,EAAA,qBAAAnS,MAAU,iMAZ3BA,EAAK,CAAA,CAAA,8EADVG,GAEKtC,EAAAkD,EAAAX,CAAA,EADJC,GAAaU,EAAAqR,CAAA,+BAATpS,EAAK,CAAA,CAAA,0EAgCgBA,EAAK,CAAA,CAAA,iDAA/BG,GAAyCtC,EAAAwU,EAAAjS,CAAA,+BAAfJ,EAAK,CAAA,CAAA,4EAKP,EAAI,CAAA,CAAA,saAcXA,EAAS,EAAA,EAAC,WAAW,4BACnBA,EAAS,EAAA,EAAC,aAAa,+FAK5BA,EAAc,EAAA,EAAC,SAASA,EAAC,GAAA,CAAA,oCAhBxBA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,QAAK,iBAAjBA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,OAkBfA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,QAAK,cAAbA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,6YARHA,EAAS,EAAA,EAAC,WAAW,wCACnBA,EAAS,EAAA,EAAC,aAAa,gLAK5BA,EAAc,EAAA,EAAC,SAASA,EAAC,GAAA,CAAA,0FAhBxBA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,kDAkBfA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,8KASA,CAAC,CAAA,CAAA,yPASN,MAAM,QAAQA,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,GAAA,CAAA,EAAIA,EAAQ,CAAA,OACpD,MACF,8BAGoB,uBAAAA,MAAe,SAAW,GACjDA,MAAe,CAAC,EAAE,CAAC,IAAM,GACzBA,EAAc,EAAA,EAAC,CAAC,EAAE,CAAC,IAAMA,EAAC,GAAA,SACnBA,EAAoB,EAAA,EACV,iBAAAA,MAAW,qBACd,cAAAA,MAAW,oDAEjBA,EAAW,EAAA,CAAA,4OAlBvBG,GAqBItC,EAAA+F,EAAAxD,CAAA,EApBHC,GAmBKuD,EAAA7C,CAAA,2LAdO,MAAM,QAAQf,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,GAAA,CAAA,EAAIA,EAAQ,CAAA,yDAKlCS,EAAA,CAAA,EAAA,SAAAiF,EAAA,uBAAA1F,MAAe,SAAW,GACjDA,MAAe,CAAC,EAAE,CAAC,IAAM,GACzBA,EAAc,EAAA,EAAC,CAAC,EAAE,CAAC,IAAMA,EAAC,GAAA,uBACnBA,EAAoB,EAAA,iOAqDLA,EAAK,CAAA,CAAA,iDAA/BG,GAAyCtC,EAAAwU,EAAAjS,CAAA,+BAAfJ,EAAK,CAAA,CAAA,wCAD3BkS,EAAAlS,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAAC2B,GAAA3B,CAAA,mEAA3BA,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,wJAKP,EAAI,CAAA,CAAA,uaAcXA,EAAS,EAAA,EAAC,WAAW,4BACnBA,EAAS,EAAA,EAAC,aAAa,+FAK5BA,EAAc,EAAA,EAAC,SAASA,EAAC,GAAA,CAAA,oCAhBxBA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,QAAK,iBAAjBA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,OAkBfA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,QAAK,cAAbA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,6YARHA,EAAS,EAAA,EAAC,WAAW,wCACnBA,EAAS,EAAA,EAAC,aAAa,gLAK5BA,EAAc,EAAA,EAAC,SAASA,EAAC,GAAA,CAAA,0FAhBxBA,EAAQ,EAAA,EAACA,EAAC,GAAA,CAAA,EAAE,kDAkBfA,EAAG,EAAA,EAACA,EAAE,GAAA,CAAA,EAAE,kKAvBdA,EAAgB,EAAA,GAAA6E,GAAA,OAGd7E,EAAQ,EAAA,CAAA,aAAsBA,EAAE,GAAA,kBAArC,OAAIxK,GAAA,EAAA,qLAJP2K,GA4BItC,EAAAyU,EAAAlS,CAAA,uFA3BEJ,EAAgB,EAAA,wIAGdA,EAAQ,EAAA,CAAA,mFAAb,OAAIxK,GAAA,ufAgCWwK,EAAsB,EAAA,EAACA,EAAK,GAAA,EAAEA,EAAC,GAAA,CAAA,QACvCA,EAAS,EAAA,EAAC,uBAAyB,QAC1CA,EAAwB,EAAA,EAACA,EAAW,GAAA,CAAA,IAAA,OACjCA,MAAyBA,EAAK,GAAA,CAAA,EAC9BA,EAAK,GAAA,mGAMU,iBAAAA,MAAW,2HAMpB,QAAAA,EAAe,EAAA,EAAAA,EAAO,GAAA,CAAA,EAAAA,QAAG,yDAGxB,MAAM,QAAQA,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,GAAA,CAAA,EAAIA,EAAQ,CAAA,wDAI/CA,EAAc,EAAA,EAAC,SAASA,EAAC,GAAA,CAAA,6BAGd,qBAAAA,MAAW,qBACd,kBAAAA,MAAW,gDA5BlB,OAAAA,EAAe,EAAA,EAAAA,EAAO,GAAA,CAAA,EAAAA,QAAG,QAAK,SAA9BuS,EAAA,MAAAvS,EAAe,EAAA,EAAAA,EAAO,GAAA,CAAA,EAAAA,QAAG,OA6B5BA,MAAIA,EAAE,GAAA,CAAA,IAAA,SAANuS,EAAA,GAAAvS,MAAIA,EAAE,GAAA,CAAA,iOA5BAA,EAAsB,EAAA,EAACA,EAAK,GAAA,EAAEA,EAAC,GAAA,CAAA,gDACvCA,EAAS,EAAA,EAAC,uBAAyB,QAC1CA,EAAwB,EAAA,EAACA,EAAW,GAAA,CAAA,IAAA,OACjCA,MAAyBA,EAAK,GAAA,CAAA,EAC9BA,EAAK,GAAA,yNAYCS,EAAA,CAAA,EAAA,GAAAA,EAAA,CAAA,EAAA,UAAA+R,EAAA,QAAAxS,EAAe,EAAA,EAAAA,EAAO,GAAA,CAAA,EAAAA,QAAG,gHAGxB,MAAM,QAAQA,EAAQ,CAAA,CAAA,EAAIA,KAASA,EAAC,GAAA,CAAA,EAAIA,EAAQ,CAAA,wIAI/CA,EAAc,EAAA,EAAC,SAASA,EAAC,GAAA,CAAA,sJAxBxBwS,EAAA,MAAAxS,EAAe,EAAA,EAAAA,EAAO,GAAA,CAAA,EAAAA,QAAG,0DA6B5BwS,EAAA,GAAAxS,MAAIA,EAAE,GAAA,CAAA,8JAlCZA,EAAgB,EAAA,GAAA4E,GAAA5E,CAAA,OAGdA,EAAI,GAAA,CAAA,aAAsBA,EAAE,GAAA,kBAAjC,OAAIxK,GAAA,EAAA,4LAJ4CwK,EAAK,GAAA,EAAG,IAAM,CAAC,UAAlEG,GAwCItC,EAAAyU,EAAAlS,CAAA,uFAvCEJ,EAAgB,EAAA,mKAGdA,EAAI,GAAA,CAAA,oFAJuCA,EAAK,GAAA,EAAG,IAAM,CAAC,oCAI/D,OAAIxK,GAAA,iUA3CGwK,EAAc,EAAA,EACR,eAAAA,EAAqB,EAAA,IAAA,MACpCA,QAAuB,wKANZA,EAAc,EAAA,IAAA,iBAAdA,EAAc,EAAA,GAENA,EAAY,EAAA,IAAA,yBAAZA,EAAY,EAAA,GACJA,EAAe,EAAA,IAAA,iCAAfA,EAAe,EAAA,4YAL7CG,GAuFKtC,EAAAkD,EAAAX,CAAA,gGAjFOJ,EAAc,EAAA,GACRS,EAAA,CAAA,EAAA,QAAAgS,EAAA,eAAAzS,EAAqB,EAAA,IAAA,MACpCA,QAAuB,0GANZA,EAAc,EAAA,mDAENA,EAAY,EAAA,4DACJA,EAAe,EAAA,kWAqF7CG,GAEQtC,EAAAqB,EAAAkB,CAAA,qBAFoCJ,EAAa,EAAA,CAAA,6RAYvD,EAAAA,OAAkB,GAAKA,EAAoB,EAAA,GAAA,GAAK,EAChD,EAAAA,OAAkB,GAAKA,EAAoB,EAAA,GAAA,GAAK,MAC9CA,EAAkB,EAAA,KAAQA,EAAgB,EAAA,GAAE,KAAO,6LAmBtC,gBAAA,CAAAA,OAAsBA,EAAI,EAAA,EAAC,OAAS,GAAKA,EAAQ,CAAA,EAClD,gBAAAA,EAAK,EAAA,EAAA,OAAS,GAAKA,EAAK,EAAA,EAAA,CAAC,GAAG,OAAS,GAAKA,EAAQ,CAAA,aAE1D,QAAAA,EAAA,EAAA,SAON,OACY,cAAAA,EAAA,EAAA,SAKZ,OACa,eAAAA,EAAA,EAAA,EACbA,EAAS,EAAA,EAAC,WAAW,aAAa,KAAIA,EAAA,GAAA,CAAA,GAEpC,WAAa,KACf,KACY,cAAAA,EAAA,EAAA,GACZA,EAAS,EAAA,EAAC,WAAW,aAAa,UAASA,EAAA,GAAA,CAAA,EAExC,GAAK,KAEA,UAAAA,EAAA,EAAA,SAOR,OACc,gBAAAA,EAAA,EAAA,SAKd,OACY,cAAAA,EAAA,EAAA,EACZA,EAAU,EAAA,EAAA,aAAa,eAAe,KAAIA,EAAA,GAAA,CAAA,EAG1C,yEAlEAS,EAAA,CAAA,EAAA,QAAAiS,EAAA,EAAA1S,OAAkB,GAAKA,EAAoB,EAAA,GAAA,GAAK,GAChDS,EAAA,CAAA,EAAA,QAAAiS,EAAA,EAAA1S,OAAkB,GAAKA,EAAoB,EAAA,GAAA,GAAK,sBAC9CA,EAAkB,EAAA,KAAQA,EAAgB,EAAA,GAAE,KAAO,sUAmBtCS,EAAA,CAAA,EAAA,SAAAA,EAAA,CAAA,EAAA,QAAAiS,EAAA,gBAAA,CAAA1S,OAAsBA,EAAI,EAAA,EAAC,OAAS,GAAKA,EAAQ,CAAA,GAClDS,EAAA,CAAA,EAAA,WAAAiS,EAAA,gBAAA1S,EAAK,EAAA,EAAA,OAAS,GAAKA,EAAK,EAAA,EAAA,CAAC,GAAG,OAAS,GAAKA,EAAQ,CAAA,6BAE1DS,EAAA,CAAA,EAAA,QAAAiS,EAAA,QAAA1S,EAAA,EAAA,SAON,QACYS,EAAA,CAAA,EAAA,QAAAiS,EAAA,cAAA1S,EAAA,EAAA,SAKZ,QACaS,EAAA,CAAA,EAAA,QAAAiS,EAAA,eAAA1S,EAAA,EAAA,EACbA,EAAS,EAAA,EAAC,WAAW,aAAa,KAAIA,EAAA,GAAA,CAAA,GAEpC,WAAa,KACf,MACYS,EAAA,CAAA,EAAA,QAAAiS,EAAA,cAAA1S,EAAA,EAAA,GACZA,EAAS,EAAA,EAAC,WAAW,aAAa,UAASA,EAAA,GAAA,CAAA,EAExC,GAAK,MAEAS,EAAA,CAAA,EAAA,QAAAiS,EAAA,UAAA1S,EAAA,EAAA,SAOR,QACcS,EAAA,CAAA,EAAA,QAAAiS,EAAA,gBAAA1S,EAAA,EAAA,SAKd,QACYS,EAAA,CAAA,EAAA,QAAAiS,EAAA,cAAA1S,EAAA,EAAA,EACZA,EAAU,EAAA,EAAA,aAAa,eAAe,KAAIA,EAAA,GAAA,CAAA,EAG1C,6LAxSEoF,GAAApF,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAAKA,EAAU,CAAA,GAAKA,EAA0B,EAAA,GAAAA,EAAoB,EAAA,GAAAA,QAAgB,SAAMiF,GAAAjF,CAAA,EAmCjHqF,EAAArF,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAACgF,GAAAhF,CAAA,IAKzBA,EAAgB,EAAA,GAAA+E,GAAA,OAGd/E,EAAQ,EAAA,CAAA,cAAsBA,EAAE,GAAA,kBAArC,OAAIxK,GAAA,EAAA,qDA4BDwK,EAAgB,EAAA,GAAA8E,GAAA,OAGd9E,EAAG,EAAA,CAAA,cAAsBA,EAAE,GAAA,kBAAhC,OAAIxK,GAAA,EAAA,qHA8BF,UACE,iBACO,iBACA,cAmBH,WAAAwK,MAAK,0BAA0B,4JA2FvCA,EAAkB,EAAA,GAAA2E,GAAA3E,CAAA,IAOpBA,EAAI,EAAA,EAAC,SAAW,GAAKA,EAAQ,CAAA,GAAIA,EAAS,CAAA,EAAC,CAAC,IAAM,WAAS0E,GAAA1E,CAAA,EAI3D2S,GAAA3S,OAAoBA,EAAkB,EAAA,IAAA6B,GAAA7B,CAAA,ojBA3M1BA,EAAY,EAAA,EAAA,IAAA,2DAFXA,EAAW,EAAA,CAAA,kBACXA,EAAI,EAAA,CAAA,EAEHqB,GAAAoE,EAAA,YAAAzF,OAAoBA,EAAkB,EAAA,CAAA,yDA3BzDG,GAgOKtC,EAAA2H,EAAApF,CAAA,yBA3MJC,GA0MKmF,EAAAC,CAAA,EA5LJpF,GAkEOoF,EAAAmN,CAAA,yBA9DNvS,GA8BOuS,EAAAzK,CAAA,EA7BN9H,GA4BI8H,EAAA0K,CAAA,kFAELxS,GA8BOuS,EAAAxK,CAAA,EA7BN/H,GA4BI+H,EAAA0K,CAAA,oRAtEQ7O,GAAAjE,QAAAA,EAAiB,EAAA,EAAA,MAAA,KAAA,SAAA,8BACnBiE,GAAAjE,QAAAA,EAAe,EAAA,EAAA,MAAA,KAAA,SAAA,iCACZiE,GAAAjE,QAAAA,EAAe,EAAA,EAAA,MAAA,KAAA,SAAA,uBA9BzBA,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,GAAKA,EAAU,CAAA,GAAKA,EAA0B,EAAA,GAAAA,EAAoB,EAAA,GAAAA,QAAgB,+GAmC3GA,EAAS,CAAA,GAAAA,EAAM,CAAA,EAAA,SAAW,yDAKxBA,EAAgB,EAAA,wIAGdA,EAAQ,EAAA,CAAA,kDA4BVA,EAAgB,EAAA,uIAGdA,EAAG,EAAA,CAAA,0JAoDAS,EAAA,CAAA,EAAA,OAAAsS,EAAA,WAAA/S,MAAK,0BAA0B,wIA2FvCA,EAAkB,EAAA,yFAhMRA,EAAY,EAAA,EAAA,IAAA,mCAFXA,EAAW,EAAA,CAAA,mCACXA,EAAI,EAAA,CAAA,oBAEHqB,GAAAoE,EAAA,YAAAzF,OAAoBA,EAAkB,EAAA,CAAA,EAsMpDA,EAAI,EAAA,EAAC,SAAW,GAAKA,EAAQ,CAAA,GAAIA,EAAS,CAAA,EAAC,CAAC,IAAM,8HAIlDA,OAAoBA,EAAkB,EAAA,0JAzLpC,OAAIxK,GAAA,+BA+BJ,OAAIA,GAAA,8YA/pBD,SAAA8H,IAAA,CACD,OAAA,KAAK,SAAS,SAAS,EAAE,EAAE,UAAU,EAAG,EAAE,WA6TzCwI,GAAe9K,EAAA,2BACIA,CAAK,aAiablG,GAAC,wDAr4BV,CAAA,SAAAmJ,CAAA,EAAAyC,GACA,MAAAsS,EAAuB,IAAA,EAAAtS,GACvB,WAAAuS,EAAa,EAAA,EAAAvS,EACb,CAAA,QAAAnM,EAAA,EAAA,EAAAmM,EACA,CAAA,OAAAwP,EAAA,EAAA,EAAAxP,EACA,CAAA,UAAAwF,CAAA,EAAAxF,EACA,CAAA,UAAAkN,CAAA,EAAAlN,EACA,CAAA,iBAAA4B,CAAA,EAAA5B,EAKA,CAAA,WAAAgC,EAAA,EAAA,EAAAhC,GAEA,SAAAvF,EAAW,EAAA,EAAAuF,GACX,KAAA0G,EAAO,EAAA,EAAA1G,EACP,CAAA,KAAAwS,CAAA,EAAAxS,EACA,CAAA,KAAAiC,CAAA,EAAAjC,GAEA,WAAA6H,GAAa,GAAA,EAAA7H,GACb,YAAA6B,EAAc,EAAA,EAAA7B,EACd,CAAA,cAAAyS,EAAA,EAAA,EAAAzS,GACA,iBAAA+Q,GAAmB,EAAA,EAAA/Q,EACnB,CAAA,OAAA0S,CAAA,EAAA1S,EACA,CAAA,eAAA2S,CAAA,EAAA3S,GACA,uBAAAmO,EAAyB,EAAA,EAAAnO,GACzB,iBAAAoO,EAAmB,EAAA,EAAApO,GACnB,gBAAAjC,EAAkB,EAAA,EAAAiC,GAClB,UAAA+B,EAAgC,MAAA,EAAA/B,GAChC,YAAAqO,EAA4C,MAAA,EAAArO,GAC5C,eAAA4S,EAAiB,CAAA,EAAA5S,EACjB,CAAA,eAAAmQ,GAAA,EAAA,EAAAnQ,GACA,WAAAsO,GAAa,EAAA,EAAAtO,QAElB6S,EAAS5T,GAAA,CACd,uBAAAkP,EACA,iBAAAC,EACA,YAAAC,EACA,iBAAA0C,GACA,SAAAtW,EACA,eAAAmY,EACA,WAAAL,EACA,YAAA1Q,EACA,KAAA6E,EACA,WAAAmB,GACA,cAAA4K,EACA,UAAA1Q,EACA,eAAAoO,MAGO,MAAO2C,EAAU,QAASC,CAAe,EAAAF,uBAgBjDxL,GAAA,IAAA,CACClH,EAAA,GAAA0S,EAAO,eAAiBtX,GAAAsX,CAAA,EACxB1S,EAAA,GAAA0S,EAAO,YAAcG,GAAAH,CAAA,EACrB1S,EAAA,GAAA0S,EAAO,WAAaI,GAAAJ,CAAA,EACpB1S,EAAA,GAAA0S,EAAO,QAAUK,GAAAL,CAAA,EACjB1S,EAAA,GAAA0S,EAAO,SAAW7U,GAAA6U,CAAA,EAClBM,KAEM,MAAAC,EAAA,IAAe,qBAAsBC,GAAA,CAC1CA,EAAQ,QAASC,GAAA,CACZA,EAAM,gBAAmB,CAAAC,SAC5BC,GAAmB,EAAA,EAEpBD,GAAaD,EAAM,mBAGrBF,EAAS,QAAQ7X,EAAM,EACvB,SAAS,iBAAiB,QAASD,EAAoB,EACvD,OAAO,iBAAiB,SAAUmY,EAAa,QAEzCC,EAAmBtZ,GAAA,EACpB8H,IAAeyR,KAClBC,EAAgBxZ,CAAK,GAGvB,gBAAS,iBAAiB,UAAWsZ,CAAe,OAGnDN,EAAS,WAAA,EACT,SAAS,oBAAoB,QAAS9X,EAAoB,EAC1D,OAAO,oBAAoB,SAAUmY,EAAa,EAClD,SAAS,oBAAoB,UAAWC,CAAe,WAcnD1V,GAAWwE,KAOb,IAAA9G,EAAA,CAAA,EAIA+T,GAAA,CAAA,EACAoE,GAAW3E,GAAarb,EAAS2R,EAAW9J,EAAKkB,EAAO,EACxDkX,GAAwBjgB,EACxBK,EAAA,CAAA,CAAA,CAAA,EAEA6f,GACAC,GAAA,CAAA,CAAA,CAAA,EAMAC,GAAW,GACXC,GACAC,GAAA,CAAA,EAEJ9M,GAAA,IAAA,CAIC6M,GAHc,iBAAiB,SAAS,eAAe,EACrD,iBAAiB,gBAAgB,EACjC,OAC4B,KAC9B,SAAS,gBAAgB,MAAM,YAC9B,wBACAA,EAAA,IAII,MAAAlB,GAAA,CAAevc,EAAazC,IACjCE,IAAOuC,CAAG,IAAIzC,CAAG,GAAG,MAEfif,GAAcjf,GACnBE,GAAM,IAAKuC,GAAQA,EAAIzC,CAAG,GAAG,KAAK,GAAA,GAE7Bkf,GAAWzc,GAChBvC,IAAOuC,CAAG,GAAG,IAAKwC,GAASA,EAAK,KAAK,GAAA,OAa3B,cAAAjE,GAAmC,IAAA,EAAAgL,GACnC,QAAA/K,GAA6B,IAAA,EAAA+K,EA4GpClC,GAAmB+V,GAAS,IAAK,GAAM,EAAE,KAAK,EAC9ChW,GAAgB3J,EAAK,IAAKuC,GAAQA,EAAI,IAAKwC,GAAS,OAAOA,EAAK,KAAK,CAAA,CAAA,EAyBhE,SAAAmb,GAAYpgB,EAAaU,EAAA,CACjCqe,EAAW,YAAY/e,EAAKU,CAAS,EACrCT,GAAUC,EAAMc,GAAeC,EAAO,EAG9B,SAAAof,IAAA,CACRtB,EAAW,iBAAA,EACX9e,GAAUC,EAAMc,GAAeC,EAAO,WAc9Bqf,GACRtgB,EACAuJ,EACAC,EACAxG,EAAA,CAEA+b,EAAW,cAAc/e,EAAKuJ,EAAUC,EAAQxG,CAAK,EACrDxB,GAAYtB,EAAMc,GAAeC,EAAO,EAGhC,SAAAsf,IAAA,CACRxB,EAAW,mBAAA,EACXvd,GAAYtB,EAAMc,GAAeC,EAAO,EAG1B,eAAAuf,GAAY1f,EAAW2f,EAAU,GAAA,CAC1C,CAAAha,GAAYyK,IAAgBpQ,GAAK0Q,EAAU,CAAC,IAAM,WACvDuN,EAAW,gBAAgBje,CAAC,EAGpB,SAAAuQ,GAAoBjL,EAAmBpG,EAAA,CAC3CoG,EAAM,kBAAkB,oBAG5BA,EAAM,eAAA,EACNA,EAAM,gBAAA,EACDK,IACLsY,EAAW,YAAY,EAAK,EAC5BA,EAAW,oBAAoB/e,EAAKyG,CAAQ,EAC5Cc,GAAO,MAAA,aAGCgK,GAAgBnL,EAAA,CACnBK,IACLsY,EAAW,gBAAgB3Y,EAAM,OAAO,GAAG,EAC3CmB,GAAO,MAAA,kBAGOoB,GAAQrC,EAAA,CAGlB,GAFJiB,GAAO,MAAA,EAEH2R,EAAU,CAAC,IAAM,UAAA,OAEf,MAAArQ,EAAU,MAAM3I,EAAK,CAAC,GAAG,QAAUL,EAAQ,MAAM,EACrD,KAAK,CAAC,EACN,IAAA,CAAKO,EAAGU,IAAA,OACFwa,GAAM1S,KACZ,OAAAuD,EAAA,GAAAzE,EAAI4T,EAAG,EAAA,CAAM,KAAM,KAAM,MAAO,IAAA,EAAA5T,CAAA,EACvB,CAAA,GAAI4T,GAAK,MAAO,EAAA,IAGvBpb,EAAK,SAAW,EACnBiM,EAAA,GAAAjM,EAAA,CAAQ2I,CAAO,CAAA,EACLvC,IAAA,QAAuBA,GAAS,GAAKA,GAASpG,EAAK,OAC7DA,EAAK,OAAOoG,EAAO,EAAGuC,CAAO,EAE7B3I,EAAK,KAAK2I,CAAO,EAGlBsD,EAAA,GAAAjL,EAAA,CAAYoF,WAAsBA,EAAQpG,EAAK,OAAS,EAAG,CAAC,CAAA,iBAG9C4I,GAAQxC,EAAA,CAElB,GADJiB,GAAO,MAAA,EACHiK,EAAU,CAAC,IAAM,UAAA,OAEf,MAAAkP,EAAS3B,EAAW,QAAQ7e,EAAML,EAAS+I,GAAStC,CAAK,EAE/Doa,EAAO,KAAK,QAASje,GAAA,CACpBA,EAAI,QAASwC,GAAA,CACPyC,EAAIzC,EAAK,EAAE,GACfkH,EAAA,GAAAzE,EAAIzC,EAAK,EAAE,EAAM,CAAA,KAAM,KAAM,MAAO,IAAA,EAAAyC,CAAA,MAKvCyE,EAAA,GAAAjM,EAAOwgB,EAAO,IAAA,EACdvU,EAAA,EAAAtM,EAAU6gB,EAAO,OAAA,EAEX,MAAAvY,GAAA,EAEN,sBAAA,IAAA,CACCqY,GAAYla,IAAA,OAAsBA,EAAQpG,EAAK,CAAC,EAAE,OAAS,EAAG,EAAI,EAC5D,MAAAygB,EAAQpZ,GAAO,iBAAiB,OAAO,EAAE,CAAC,EAAE,YAClDA,GAAO,iBAAiB,OAAO,EAAE,CAAC,EAAE,UAAW,KAAMoZ,CAAA,CAAA,aAI9CrZ,GAAqBlB,EAAA,CACzBwa,GAA0Bxa,EAAOmB,EAAM,IAC1CwX,EAAW,eAAA,OACX7N,EAAc,EAAA,OACdC,EAAkB,EAAA,GAMhB,IAAA0P,GAMArB,GAAmB,GAKnBvZ,GAAA,CAAA,EACAsB,GACAI,GACAmZ,GAAyB,EACzBC,GAA0B,EAErB,SAAAC,IAAA,OACFC,EAAe/gB,EAAK,CAAC,GAAG,QAAU,EAKvC,GAJGghB,EAAU,aAAa,eAAe,OAAS,GAIlDJ,KAA2B5gB,EAAK,QAChC6gB,KAA4BE,GAC5BC,EAAU,WAAW,aAAa,OAAS,SAK5CJ,GAAyB5gB,EAAK,OAC9B6gB,GAA0BE,EAEpB,MAAAE,EAASlb,GAAM,IAAKsI,GAAOA,GAAI,aAAe,CAAC,EACjD,GAAA4S,EAAO,SAAW,EAElB,CAAApE,IACHxV,GAAO,MAAM,YAA0C,0BAAA,GAAA4Z,EAAO,CAAC,CAAA,IAAA,UAGvDrgB,EAAI,EAAGA,EAAI,GAAIA,IAClB,GAAA,CAAA2d,EAAc3d,CAAC,EACnByG,GAAO,MAAM,+BAA+BzG,CAAC,EAAA,UACnC2d,EAAc3d,CAAC,EAAE,SAAS,GAAG,EAAA,OACjCsgB,EAAa,WAAW3C,EAAc3d,CAAC,CAAA,EACvCugB,GAAc,KAAK,MAAOD,EAAa,IAAO7Z,GAAO,WAAW,EACtEA,GAAO,MAAM,YAAA,gBAA4BzG,CAAC,GAAA,GAAOugB,EAAW,IAAA,OAE5D9Z,GAAO,MAAM,YAAA,gBAA4BzG,CAAC,GAAI2d,EAAc3d,CAAC,CAAA,EAI/DqgB,EAAO,SAASG,EAAOxgB,IAAA,CACjB,GAAA,CAAA2d,EAAc3d,CAAC,EAAA,OACbygB,GAAsB,GAAA,KAAK,IAAID,EAAO,EAAE,CAAA,KAC9C/Z,GAAO,MAAM,YAAA,gBAA4BzG,CAAC,GAAIygB,EAAgB,UAS7DC,GACHhG,EAAO,MAAM,EAAI3H,GAAa2H,EAAO,OAAU,EAAE,EAAE,OAAS,GAAK,GAC9DiG,GAAkB,EAEb,SAAAxhB,GACRmH,EACAsa,EACAC,EAAA,CAEM,MAAAjB,EAAS3f,GACdqG,EACAsa,EACAC,EACAT,EAAU,WAAW,aACrBhgB,EACAC,EAAA,EAGDgL,EAAA,GAAAjM,EAAOwgB,EAAO,IAAA,EACdvU,EAAA,GAAAjL,EAAWwf,EAAO,QAAA,EAGV,SAAAlf,GACR4F,EACAsa,EACAC,EAAA,CAEM,MAAAjB,EAAS/e,GACdyF,EACAsa,EACAC,EACAT,EAAU,aAAa,eACvBhgB,EACAC,GACA+f,EAAU,aAAa,cAAc,KACrCA,EAAU,aAAa,cAAc,cACrCA,EAAU,aAAa,cAAc,OAAA,EAEtC/U,EAAA,GAAAjM,EAAOwgB,EAAO,IAAA,EACdvU,EAAA,GAAAjL,EAAWwf,EAAO,QAAA,MAKfnB,GAAa,SAEXqC,GAAkB5e,GAAA,CACvB+b,EAAW,eAAe/b,CAAK,GAM5B,IAAA6e,GAAA,CAAA,WASKpT,GACRrI,EAAA,CAKQ,KAAA,CAAA,WAAA0b,EAAY,OAAA/c,GAAWqB,EAAM,OACrC2V,GAAiB+F,EAAYjD,EAAQ9Z,CAAM,EAGnC,SAAAuM,GAAmBlL,EAAmBpG,EAAA,CAE1C,GADJoG,EAAM,gBAAA,EACF2b,GAAsBA,EAAmB,MAAQ/hB,EACpD+e,EAAW,uBAAuB,IAAI,aAEhCpR,EAAUvH,EAAM,OAAuB,QAAQ,IAAI,EACrD,GAAAuH,EAAA,CACG,MAAA7C,EAAO6C,EAAO,wBACpBoR,EAAW,wBACV,IAAA/e,EACA,EAAG8K,EAAK,MACR,EAAGA,EAAK,MAAA,CAAA,IAMZkX,GAAA,IAAA,MACCjY,EAAkB,EAAA,aAGVkY,GAAc3b,EAAA,IAClBkL,EAAU,CAAC,IAAM,WACjBtR,EAAK,CAAC,EAAE,QAAU,EAAA,OAEhB,MAAAwgB,EAAS3B,EAAW,cAAc7e,EAAML,EAASyG,CAAK,EAC5D6F,EAAA,GAAAjM,EAAOwgB,EAAO,IAAA,EACdvU,EAAA,EAAAtM,EAAU6gB,EAAO,OAAA,EACjBvU,EAAA,GAAA0T,GAAW3E,GAAarb,EAAS2R,EAAW9J,EAAKkB,EAAO,CAAA,EACxDmW,EAAW,qBAAqB,IAAI,EACpCA,EAAW,uBAAuB,IAAI,EACtCA,EAAW,aAAa,EAAK,EAC7BA,EAAW,mBAAA,CAAA,CAAA,EACXA,EAAW,YAAY,EAAK,WAGpBmD,GAAc5b,EAAA,MACtBpG,EAAO6e,EAAW,cAAc7e,EAAMoG,CAAK,CAAA,EAC3CyY,EAAW,qBAAqB,IAAI,EACpCA,EAAW,uBAAuB,IAAI,EAGnC,IAAAoD,GAqBK,SAAAC,IAAA,CACJ,GAAAlB,EAAU,sBAAwB7G,IAAgB,SAAA,CAC/C,MAAAgI,EAAA,CAAA,EACAC,EAAA,CAAA,EACAC,EAAA,CAAA,EAENvC,GAAe,QAASvd,IAAA,CACjB,MAAA+f,GAAA,CAAA,EACAC,GAAA,CAAA,EACAC,GAAA,CAAA,EAENjgB,GAAI,QAASwC,IAAA,CACZud,GAAS,KAAKvd,GAAK,KAAK,EACxBwd,GAAY,KACXxd,GAAK,gBAAkB,OACpBA,GAAK,cACL,OAAOA,GAAK,KAAK,CAAA,EAErByd,GAAY,KAAKzd,GAAK,SAAW,EAAE,IAGpCod,EAAc,KAAKG,EAAQ,EAC3BF,EAAwB,KAAKG,EAAW,EACxCF,EAAiB,KAAKG,EAAW,IAG5B,MAAAC,EAAA,CACL,KAAMN,EACN,QAASxC,GAAS,IAAK7W,IAAMA,GAAE,KAAK,EACpC,SAAA,CACC,cAAesZ,EACf,QAASC,IAIXvY,GAAS,SAAU2Y,CAAc,EAE5B5Y,GACJC,GAAS,OAAO,EAGjB+U,EAAW,cAAc,IAAI,GAI3B,IAAA7K,GACAD,GAAqB,GAEhB,SAAA2O,IAAA,CACR1O,GAAS,UACR,IAAK,CAAA,CAAA,EAIE,SAAAuL,IAAA,CACRV,EAAW,qBAAqB,IAAI,EACpCA,EAAW,uBAAuB,IAAI,EACtC5S,EAAA,GAAAxJ,EAAA,CAAA,CAAA,OACAzB,EAAW,EAAA,OACXoJ,EAAU,EAAA,OACVkV,GAAmB,EAAA,EACnBwB,KAGQ,SAAA6B,GAAWvc,EAAesD,EAAA,OAC5BkZ,EAAYlZ,IAAa,QAAUtD,EAAQA,EAAQ,EACzDqC,GAAQma,CAAS,OACjBvQ,EAAmB,IAAA,OACnBwP,EAAqB,IAAA,EAGb,SAAAgB,GAAWzc,EAAesD,EAAA,OAC5B8H,EAAY9H,IAAa,OAAStD,EAAQA,EAAQ,EACxDwC,GAAQ4I,CAAS,OACjBa,EAAmB,IAAA,OACnBwP,EAAqB,IAAA,EAGN,SAAAiB,IAAA,CACfjE,EAAW,iBAAA,MAGR7Q,GAAc,GACdyR,GAAsC,KACtCsD,GAAkD,KAEhD,MAAAC,GAAA,CACL,YAAAhV,GACA,WAAAyR,GACA,eAAAsD,EAAA,EASG,IAAAE,GAEK,SAAAhE,IAAA,MACRgE,GAAgBxG,GACfuG,GACClgB,GAAAmJ,EAAA,GAAW+B,GAAclL,CAAA,EACzBiD,GAAU8Y,EAAW,mBAAmB9Y,CAAK,EAC7ChB,GAAS8Z,EAAW,aAAa9Z,CAAI,EAAA,CACrCmB,EAAO3D,EAAKzC,IAAQ+e,EAAW,kBAAkB3Y,EAAO3D,EAAKzC,CAAG,EACjE+c,GACAxV,EAAA,CAAA,EAUO,SAAA6b,GAAuB3gB,EAAazC,EAAA,QACnBkhB,EAAU,uBAAyB,QAEpClB,KAAiBvd,CAAG,IAAIzC,CAAG,EAC3CggB,GAAevd,CAAG,EAAEzC,CAAG,EAAE,gBAAkB,OAC/CggB,GAAevd,CAAG,EAAEzC,CAAG,EAAE,cACzB,OAAOggB,GAAevd,CAAG,EAAEzC,CAAG,EAAE,KAAK,EAGrCE,IAAOuC,CAAG,IAAIzC,CAAG,EACbE,EAAKuC,CAAG,EAAEzC,CAAG,EAAE,gBAAkB,OACrCE,EAAKuC,CAAG,EAAEzC,CAAG,EAAE,cACf,OAAOE,EAAKuC,CAAG,EAAEzC,CAAG,EAAE,KAAK,EAGxB,gBAIuBghB,uBAaDte,GAAgBxC,EAAM,IAAI,EAGzCmjB,GAAA3e,GAAMqa,EAAW,cAAcra,EAAE,MAAM,mEAgCpCmb,GAAS/e,CAAC,EAAE,MAAKkC,CAAA,IAAjB6c,GAAS/e,CAAC,EAAE,MAAKkC,2EAkBpB0E,EAAItG,CAAE,EAAE,MAAK4B,CAAA,IAAb0E,EAAItG,CAAE,EAAE,MAAK4B,wDAYMiD,GAAM1E,CAAC,EAAAsN,wDAzCtBlH,GAAKkH,uBA0JGnK,EAAG7B,EAAGC,IAAMwgB,EAAkB5e,EAAG7B,EAAGC,CAAC,qBAT7C8P,EAAA,GAAA,UAAAoN,GAAe1Z,CAAK,EAAE/E,CAAC,EAAE,MAAKyB,CAAA,IAA9Bgd,GAAe1Z,CAAK,EAAE/E,CAAC,EAAE,MAAKyB,4HA6BjC4P,EAAA,GAAA,UAAAlL,EAAItG,CAAE,EAAA4B,CAAA,IAAN0E,EAAItG,CAAE,EAAA4B,2CA1DH6c,GAAS/e,CAAC,EAAE,MAAKkC,CAAA,IAAjB6c,GAAS/e,CAAC,EAAE,MAAKkC,2EAkBpB0E,EAAItG,CAAE,EAAE,MAAK4B,CAAA,IAAb0E,EAAItG,CAAE,EAAE,MAAK4B,0BAtCbgd,GAAchd,yHAENwe,GAAYxe,0BACJye,GAAeze,kGAxBjC,MAAAugB,GAAA,CAAA,CAAA,OAAAC,CAAM,IACjBnf,GACCmf,EAAO,KACN7e,IACAwH,EAAA,GAAA0T,GAAW3E,GACVvW,EAAK,IAAKqE,GAAMA,GAAK,EAAE,EACvBwI,EACA9J,EACAkB,EAAA,CAAA,EAEMiX,IAEP4D,GAAI,CACJtX,EAAA,EAAAqP,EAASiI,CAAI,+CArGNlc,GAAMsH,aAMJ,MAAA6U,GAAAhf,GAAMgK,GAAehK,EAAGma,CAAM,SAsMZlW,KAUPgb,GAAA,IAAAd,GAAWtQ,GAAkB,KAAQ,GAAG,OAAO,EAC/CqR,GAAA,IAAAf,GAAWtQ,GAAkB,KAAQ,GAAG,OAAO,SAEtEwQ,GACCxQ,GAAkB,KAAOwP,GAAoB,KAAG,GAChD,MAAK,SAGNgB,GACCxQ,GAAkB,KAAOwP,GAAoB,KAAG,GAChD,OAAM,EAEa8B,GAAA,IAAA3B,GAAc3P,GAAkB,OAAS,SAE7D0P,GAAc1P,GAAkB,KAAOwP,GAAoB,KAAG,EAAM,KAMjErhB,GAAS,CACNqhB,IACH3B,GAAY2B,EAAmB,IAAKrhB,CAAS,EAC7Cqe,EAAW,uBAAuB,IAAI,YAMvCsB,KACAtB,EAAW,uBAAuB,IAAI,MAKrChf,GAASA,EAAK,OAASgiB,GAAoB,KAAG,OAK9ChiB,GAASA,EAAK,OAASgiB,GAAoB,KAAG,QAI9CxY,EAAUC,EAAQxG,IAAK,CACpB+e,IACHzB,GAAcyB,EAAmB,IAAKxY,EAAUC,EAAQxG,CAAK,EAC7D+b,EAAW,uBAAuB,IAAI,YAMvCwB,KACAxB,EAAW,uBAAuB,IAAI,MAKrCjc,GAAMA,EAAE,OAASif,GAAoB,KAAG,0rCA5+B1C5V,EAAA,GAAAxJ,EAAiBue,EAAU,SAAS,cAAA,mBACpC/U,EAAA,GAAAjL,EAAWggB,EAAU,SAAS,QAAA,mBAC9B/U,EAAA,GAAA7B,EAAU4W,EAAU,SAAS,OAAA,mBAC7B/U,EAAA,GAAA+E,EAAcgQ,EAAU,SAAS,WAAA,mBACjC/U,EAAA,GAAAgF,EAAkB+P,EAAU,SAAS,eAAA,mBACrC/U,EAAA,GAAAoG,EAAmB2O,EAAU,SAAS,gBAAA,mBACtC/U,EAAA,GAAA4V,EAAqBb,EAAU,SAAS,kBAAA,mBACxC/U,EAAA,GAAAmG,EAAa4O,EAAU,SAAS,UAAA,gEAiH3B,CAAA/W,GAAOqR,EAAQuE,EAAO,EAAA,CACzB,GAAAxY,GAAA,OAEGuc,EACLtI,EAAO,SAAW,GAAMA,EAAO,SAAW,GAAKA,EAAO,CAAC,EAAE,SAAW,EAC/DuI,EACLhE,cACCvE,EAAO,SAAWuE,GAAQ,QACzBvE,EAAO,CAAC,GAAKuE,GAAQ,CAAC,GAAKvE,EAAO,CAAC,EAAE,SAAWuE,GAAQ,CAAC,EAAE,WAE1D+D,GAAYC,EAAAA,SACNjjB,GAAI,EAAGA,GAAI,GAAIA,KACvByG,GAAO,MAAM,+BAA+BzG,EAAC,EAAA,EAE9CggB,GAAyB,EACzBC,GAA0B,OAC1BvB,GAAmB,EAAA,SAKfsE,EACLtI,EAAO,SAAW,GAAMA,EAAO,SAAW,GAAKA,EAAO,CAAC,EAAE,SAAW,EAC/DuI,EACLhE,cACCvE,EAAO,SAAWuE,GAAQ,QACzBvE,EAAO,CAAC,GAAKuE,GAAQ,CAAC,GAAKvE,EAAO,CAAC,EAAE,SAAWuE,GAAQ,CAAC,EAAE,QAE9D5T,EAAA,GAAAjM,EAAOqb,GACNC,EACA9T,EACA+T,GACA7S,GACA5H,EAAA,CAAA,EAEDmL,EAAA,GAAA4T,GAAU,KAAK,MAAM,KAAK,UAAUvE,CAAM,CAAA,CAAA,EAEtCsI,GAAYC,EACfhF,EAAW,iBAAA,EACDmC,EAAU,WAAW,aAAa,OAAS,EACrDjhB,GAAUC,EAAMc,GAAeC,EAAO,GAEtC8d,EAAW,YAAA,GAAgB,KAAK,EAChCA,EAAW,iBAAA,GAGRmC,EAAU,aAAa,eAAe,OAAS,EAClD1f,GAAYtB,EAAMc,GAAeC,EAAO,EAExC8d,EAAW,mBAAA,EAGRmC,EAAU,sBACbnC,EAAW,cAAc,IAAI,EAG1BxX,IAAUtB,GAAM,OAAS,IAAM6d,GAAYC,SAC9CvE,GAAmB,EAAA,gDAxKrBrT,EAAA,GAAG8E,EACF2N,GAAkB1e,IAAO,CAAC,GAAG,OAC1B,KAAK,IAAI0e,EAAgB1e,EAAK,CAAC,EAAE,MAAM,EACvC,CAAA,gDA+FEiK,GAAOtK,EAASigB,EAAW,IAC/B3T,EAAA,GAAA0T,GAAW3E,GAAarb,EAAS2R,EAAW9J,EAAKkB,EAAO,CAAA,EACxDuD,EAAA,GAAA2T,GAAc,KAAK,MAAM,KAAK,UAAUjgB,CAAO,CAAA,CAAA,kDA3D5CK,GAAQ2f,IAAYnY,KACvByE,EAAA,GAAA0S,EAAO,KAAO3e,EAAA2e,CAAA,EACd1S,EAAA,GAAA0S,EAAO,QAAUgB,GAAAhB,CAAA,EACjB1S,EAAA,GAAA0S,EAAO,IAAMnX,EAAAmX,CAAA,EACb1S,EAAA,GAAA0S,EAAO,cAAgB7d,GAAA6d,CAAA,EACvB1S,EAAA,GAAA0S,EAAO,QAAU5d,GAAA4d,CAAA,+DA8HnB,GAAOqC,EAAU,uBAAyB,OAAA,OACnC8C,EAAe,IAAA,IACrB7X,EAAA,GAAAgU,GAAA,CAAA,CAAA,EAEAjgB,EAAK,SAASuC,EAAKwhB,IAAA,CAEjBxhB,EAAI,KAAMwC,IACT,OAAOA,IAAM,KAAK,EAChB,YAAA,EACA,SAASic,EAAU,sBAAsB,YAAA,GAAiB,EAAE,CAAA,GAG/Df,GAAyB,KAAK8D,CAAO,EAEtCxhB,EAAI,SAASwC,GAAMif,KAAA,CAClBF,EAAS,IAAI/e,GAAK,GAAA,CACjB,MAAOA,GAAK,MACZ,cACCA,GAAK,gBAAkB,OACpBA,GAAK,cACL,OAAOA,GAAK,KAAK,EACrB,QAAShE,KAAUgjB,CAAO,IAAIC,EAAO,GAAK,eAKvCC,EAAWpF,EAAW,YAAY7e,CAAI,EAE5CiM,EAAA,GAAA6T,GAAiBmE,EAAS,IAAK1hB,GAC9BA,EAAI,IAAKwC,GAAA,OACFmF,GAAW4Z,EAAS,IAAI/e,EAAK,EAAE,SAEjC,GAAAA,EACH,cACCmF,IAAU,gBAAkB,OACzBA,GAAS,cACT,OAAOnF,EAAK,KAAK,EACrB,QAASmF,IAAU,SAAW,aAKjC+B,EAAA,GAAAgU,GAAA,CAAA,CAAA,qDAOIjgB,GAAQ2f,MACXd,EAAW,eACV7e,EAAK,IAAK,CAAAuC,EAAK2hB,IACd3hB,EAAI,KAAKwC,EAAMof,IAAA,CACR,MAAAC,GAAQ,MAAM,QAAQ/a,CAAQ,EAAIA,EAAS8a,CAAM,EAAI9a,SAEvD,GAAAtE,EACH,MAAO0W,GAAmB1W,EAAK,MAAOqf,EAAK,MAI9CzE,GACAhW,GACAC,GACAC,EACAC,SAEDH,GAAgB3J,EAAK,IAAKuC,GAAQA,EAAI,IAAKwC,GAAS,OAAOA,EAAK,KAAK,CAAA,CAAA,CAAA,EACrEkH,EAAA,GAAArC,GAAmB+V,GAAS,IAAK,GAAM,EAAE,KAAK,CAAA,gEAe3CqB,EAAU,aAAa,eAAe,OAAS,GAClD1f,GAAYtB,EAAMc,GAAeC,EAAO,EAGrCigB,EAAU,WAAW,aAAa,OAAS,IAC9CjhB,GAAUC,EAAMc,GAAeC,EAAO,EACtC8d,EAAW,iBAAiB7e,CAAI,4BAoG/BiM,EAAA,GAAAlK,EAAMD,GAAQ9B,CAAI,CAAA,iDAGd+F,GAAM,CAAC,GAAKA,GAAM,CAAC,GAAG,cAC5B,aAAa4a,EAAkB,OAC/BA,GAAqB,WAAA,IAAiBG,KAAmB,GAAG,CAAA,kDAItD/a,GAAM,CAAC,GAAM,CAAAuZ,KACnBwB,UACAxB,GAAmB,EAAA,2BAiGjBrT,EAAA,GAAAoY,EAAA,CAAA,CAAmBrjB,GAAYA,EAAS,CAAC,CAAA,0CAcvCoR,GAAe,CAAAnI,GAAOxH,EAAgBkf,EAAuB,GAChED,GAAe,EAAK,OAErBC,GAA0Blf,CAAA,2BAwDpBzB,IAAa,SAAOihB,GAAuBjhB,CAAA,mDAE3CA,IAAa,GAAA,CACb,MAAAsjB,EAAY/c,GACjBvG,EACAhB,EACAwH,EACAH,GACAI,EAAA,EAED,SAAS,gBAAgB,MAAM,YAC9B,qBACA6c,EAAU,OAAA,EAEX,SAAS,gBAAgB,MAAM,YAC9B,qBACAA,EAAU,SAAW,KAAA,2BAoHhBjd,IAAQ4X,6BAEfhT,EAAA,GAAGmX,EAAoBH,IAAe,oBAAA,IAAA,6BACtChX,EAAA,GAAGsY,EAAoBtB,IAAe,oBAAA,IAAA,6BACtChX,EAAA,GAAGyT,EAAkBuD,IAAe,kBAAA,IAAA,MAvBnChX,EAAA,GAAA+B,GAAcgV,GAAW,WAAA,EACzBvD,GAAauD,GAAW,WACxBD,GAAiBC,GAAW,o0HCprBhB,WAAA5X,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,EAAA,uMASV,OAAAA,KAAM,mBACCA,EAAK,CAAA,EAAC,UAAU,sBACtBA,EAAK,CAAA,EAAC,UAAU,QAChB,QAAAA,KAAM,kFAeLA,EAAW,EAAA,mBAEf,KAAAA,MAAO,+MAYC,WAAA,CAAA,MAAOoZ,EAAK,yWA3Cd,WAAApZ,MAAO,YACbS,EAAA,CAAA,EAAA,QAAA,CAAA,KAAAT,MAAO,IAAI,mBACbA,EAAc,EAAA,CAAA,uKASVS,EAAA,CAAA,EAAA,IAAA4Y,EAAA,OAAArZ,KAAM,+BACCA,EAAK,CAAA,EAAC,UAAU,kCACtBA,EAAK,CAAA,EAAC,UAAU,SAChBS,EAAA,CAAA,EAAA,IAAA4Y,EAAA,QAAArZ,KAAM,+JAeLA,EAAW,EAAA,qCAEfS,EAAA,CAAA,EAAA,SAAA4Y,EAAA,KAAArZ,MAAO,qsBAzCL,4CAGE,2mBAnDA,QAAAsZ,EAAU,EAAA,EAAA5Y,EACV,CAAA,aAAA6Y,EAAA,EAAA,EAAA7Y,GACA,QAAA4I,EAAU,EAAA,EAAA5I,EACV,CAAA,MAAAhJ,EAAA,CACV,KAAQ,CAAA,CAAA,GAAI,GAAI,EAAE,CAAA,EAClB,QAAU,CAAA,IAAK,IAAK,GAAG,EACvB,SAAU,UAEA,gBAAA+G,EAAkB,EAAA,EAAAiC,EAClB,CAAA,UAAAwF,CAAA,EAAAxF,EACA,CAAA,UAAAkN,CAAA,EAAAlN,GACA,MAAAsS,EAAuB,IAAA,EAAAtS,GACvB,WAAAuS,EAAa,EAAA,EAAAvS,EACb,CAAA,KAAA0G,CAAA,EAAA1G,EACA,CAAA,SAAAzC,CAAA,EAAAyC,GACA,MAAA8Y,EAAuB,IAAA,EAAA9Y,GACvB,UAAA+Y,EAAgC,MAAA,EAAA/Y,EAChC,CAAA,KAAAwS,CAAA,EAAAxS,GAEA,YAAA6B,EAAc,EAAA,EAAA7B,EACd,CAAA,cAAAyS,EAAA,EAAA,EAAAzS,EACA,CAAA,OAAAgZ,CAAA,EAAAhZ,EAOA,CAAA,iBAAA4B,CAAA,EAAA5B,GAKA,WAAA6H,EAAiC,MAAA,EAAA7H,EACjC,CAAA,eAAAiZ,CAAA,EAAAjZ,EACA,CAAA,YAAAkZ,CAAA,EAAAlZ,GACA,uBAAAmO,EAAyB,EAAA,EAAAnO,GACzB,UAAA+B,EAAgC,MAAA,EAAA/B,GAChC,iBAAAoO,EAAmB,EAAA,EAAApO,GACnB,iBAAA+Q,EAAmB,EAAA,EAAA/Q,GACnB,YAAAqO,EAA4C,MAAA,EAAArO,GAC5C,eAAA4S,EAAiB,CAAA,EAAA5S,EACjB,CAAA,eAAAmQ,EAAA,EAAA,EAAAnQ,GACA,WAAAsO,GAAa,EAAA,EAAAtO,EAkBA,MAAAmZ,EAAA,IAAAH,EAAO,SAAS,eAAgBC,CAAc,EA+BzDlY,EAAA,IAAAqY,IAASJ,EAAO,OAAO,UAAUI,CAAI,EAC7BC,GAAA,IAAAD,IAASJ,EAAO,OAAO,UAAUI,CAAI,mCAnB7C1gB,GAAC,CACZyH,EAAA,EAAAnJ,EAAM,KAAO0B,EAAE,OAAO,KAAI1B,CAAA,EAC1BmJ,EAAA,EAAAnJ,EAAM,QAAU0B,EAAE,OAAO,QAAO1B,CAAA,EAChCgiB,EAAO,SAAS,QAAQ,GAEdM,EAAA5gB,GAAMsgB,EAAO,SAAS,OAAO,IAC5BtgB,GAAMsgB,EAAO,SAAS,SAAUtgB,EAAE,MAAM,MAClC,OAAA8e,KAAM,CACvBrX,EAAA,EAAAmO,GAAakJ,CAAM"}