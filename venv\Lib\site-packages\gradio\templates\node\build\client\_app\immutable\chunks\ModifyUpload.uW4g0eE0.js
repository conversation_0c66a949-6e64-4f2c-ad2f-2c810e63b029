import{SvelteComponent as L,init as P,safe_not_equal as S,create_component as d,claim_component as p,mount_component as b,transition_in as m,transition_out as _,destroy_component as g,createEventDispatcher as W,create_slot as j,space as k,claim_space as I,insert_hydration as h,group_outros as E,check_outros as N,update_slot_base as z,get_all_dirty_from_scope as A,get_slot_changes as F,detach as D}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{I as C,C as G}from"./2.B2AoQPnG.js";import{D as H}from"./Download.CpfEFmFf.js";import{E as J}from"./Edit.jvyxqbov.js";import{U as K}from"./Undo.LhwFM5M8.js";import{I as O}from"./IconButtonWrapper.D5aGR59h.js";import{D as Q}from"./DownloadLink.D1g3Q1HV.js";function B(r){let n,o;return n=new C({props:{Icon:J,label:r[3]("common.edit")}}),n.$on("click",r[6]),{c(){d(n.$$.fragment)},l(e){p(n.$$.fragment,e)},m(e,f){b(n,e,f),o=!0},p(e,f){const i={};f&8&&(i.label=e[3]("common.edit")),n.$set(i)},i(e){o||(m(n.$$.fragment,e),o=!0)},o(e){_(n.$$.fragment,e),o=!1},d(e){g(n,e)}}}function M(r){let n,o;return n=new C({props:{Icon:K,label:r[3]("common.undo")}}),n.$on("click",r[7]),{c(){d(n.$$.fragment)},l(e){p(n.$$.fragment,e)},m(e,f){b(n,e,f),o=!0},p(e,f){const i={};f&8&&(i.label=e[3]("common.undo")),n.$set(i)},i(e){o||(m(n.$$.fragment,e),o=!0)},o(e){_(n.$$.fragment,e),o=!1},d(e){g(n,e)}}}function q(r){let n,o;return n=new Q({props:{href:r[2],download:!0,$$slots:{default:[R]},$$scope:{ctx:r}}}),{c(){d(n.$$.fragment)},l(e){p(n.$$.fragment,e)},m(e,f){b(n,e,f),o=!0},p(e,f){const i={};f&4&&(i.href=e[2]),f&520&&(i.$$scope={dirty:f,ctx:e}),n.$set(i)},i(e){o||(m(n.$$.fragment,e),o=!0)},o(e){_(n.$$.fragment,e),o=!1},d(e){g(n,e)}}}function R(r){let n,o;return n=new C({props:{Icon:H,label:r[3]("common.download")}}),{c(){d(n.$$.fragment)},l(e){p(n.$$.fragment,e)},m(e,f){b(n,e,f),o=!0},p(e,f){const i={};f&8&&(i.label=e[3]("common.download")),n.$set(i)},i(e){o||(m(n.$$.fragment,e),o=!0)},o(e){_(n.$$.fragment,e),o=!1},d(e){g(n,e)}}}function T(r){let n,o,e,f,i,$,c=r[0]&&B(r),u=r[1]&&M(r),a=r[2]&&q(r);const w=r[5].default,s=j(w,r,r[9],null);return i=new C({props:{Icon:G,label:r[3]("common.clear")}}),i.$on("click",r[8]),{c(){c&&c.c(),n=k(),u&&u.c(),o=k(),a&&a.c(),e=k(),s&&s.c(),f=k(),d(i.$$.fragment)},l(t){c&&c.l(t),n=I(t),u&&u.l(t),o=I(t),a&&a.l(t),e=I(t),s&&s.l(t),f=I(t),p(i.$$.fragment,t)},m(t,l){c&&c.m(t,l),h(t,n,l),u&&u.m(t,l),h(t,o,l),a&&a.m(t,l),h(t,e,l),s&&s.m(t,l),h(t,f,l),b(i,t,l),$=!0},p(t,l){t[0]?c?(c.p(t,l),l&1&&m(c,1)):(c=B(t),c.c(),m(c,1),c.m(n.parentNode,n)):c&&(E(),_(c,1,1,()=>{c=null}),N()),t[1]?u?(u.p(t,l),l&2&&m(u,1)):(u=M(t),u.c(),m(u,1),u.m(o.parentNode,o)):u&&(E(),_(u,1,1,()=>{u=null}),N()),t[2]?a?(a.p(t,l),l&4&&m(a,1)):(a=q(t),a.c(),m(a,1),a.m(e.parentNode,e)):a&&(E(),_(a,1,1,()=>{a=null}),N()),s&&s.p&&(!$||l&512)&&z(s,w,t,t[9],$?F(w,t[9],l,null):A(t[9]),null);const U={};l&8&&(U.label=t[3]("common.clear")),i.$set(U)},i(t){$||(m(c),m(u),m(a),m(s,t),m(i.$$.fragment,t),$=!0)},o(t){_(c),_(u),_(a),_(s,t),_(i.$$.fragment,t),$=!1},d(t){t&&(D(n),D(o),D(e),D(f)),c&&c.d(t),u&&u.d(t),a&&a.d(t),s&&s.d(t),g(i,t)}}}function V(r){let n,o;return n=new O({props:{$$slots:{default:[T]},$$scope:{ctx:r}}}),{c(){d(n.$$.fragment)},l(e){p(n.$$.fragment,e)},m(e,f){b(n,e,f),o=!0},p(e,[f]){const i={};f&527&&(i.$$scope={dirty:f,ctx:e}),n.$set(i)},i(e){o||(m(n.$$.fragment,e),o=!0)},o(e){_(n.$$.fragment,e),o=!1},d(e){g(n,e)}}}function X(r,n,o){let{$$slots:e={},$$scope:f}=n,{editable:i=!1}=n,{undoable:$=!1}=n,{download:c=null}=n,{i18n:u}=n;const a=W(),w=()=>a("edit"),s=()=>a("undo"),t=l=>{a("clear"),l.stopPropagation()};return r.$$set=l=>{"editable"in l&&o(0,i=l.editable),"undoable"in l&&o(1,$=l.undoable),"download"in l&&o(2,c=l.download),"i18n"in l&&o(3,u=l.i18n),"$$scope"in l&&o(9,f=l.$$scope)},[i,$,c,u,a,e,w,s,t,f]}class oe extends L{constructor(n){super(),P(this,n,X,V,S,{editable:0,undoable:1,download:2,i18n:3})}}export{oe as M};
//# sourceMappingURL=ModifyUpload.uW4g0eE0.js.map
