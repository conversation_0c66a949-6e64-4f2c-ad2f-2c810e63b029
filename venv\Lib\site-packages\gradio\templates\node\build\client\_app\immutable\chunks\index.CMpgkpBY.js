import{SvelteComponent as ke,init as pe,safe_not_equal as ce,binding_callbacks as X,bind as Y,create_component as B,space as H,element as Z,claim_component as S,claim_space as K,claim_element as y,children as $,detach as z,attr as M,mount_component as E,insert_hydration as D,append_hydration as he,group_outros as le,transition_out as v,check_outros as ne,transition_in as k,add_flush_callback as x,destroy_component as j,createEventDispatcher as He,bubble as W,text as me,claim_text as ge,set_data as be,noop as de,empty as ee,create_slot as Ke,update_slot_base as Me,get_all_dirty_from_scope as Qe,get_slot_changes as Re,flush as w,assign as ze,get_spread_update as Ve,get_spread_object as Ie}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{a as Xe}from"./Upload.yOHVlgUe.js";import{B as Be,S as Se}from"./2.B2AoQPnG.js";import{B as Ye}from"./BlockLabel.BTSz9r5s.js";import{V as Ze}from"./Video.CE2Y9LYL.js";import{S as ye}from"./SelectSource.CTC8Kkgx.js";import{W as $e}from"./ImageUploader.Dvb2Mtrn.js";/* empty css                                              */import{p as we}from"./Video.SijWdeHX.js";import{l as Wl,a as ql}from"./Video.SijWdeHX.js";import{P as xe,V as el}from"./VideoPreview.BI_25RC8.js";import{default as Ll}from"./Example.Dzb6Yct_.js";import{U as ll}from"./UploadText.CJcy9n89.js";function nl(l){let e,t=(l[0].orig_name||l[0].url)+"",n,o,i,u=we(l[0].size)+"",r;return{c(){e=Z("div"),n=me(t),o=H(),i=Z("div"),r=me(u),this.h()},l(a){e=y(a,"DIV",{class:!0});var h=$(e);n=ge(h,t),h.forEach(z),o=K(a),i=y(a,"DIV",{class:!0});var s=$(i);r=ge(s,u),s.forEach(z),this.h()},h(){M(e,"class","file-name svelte-14jis2k"),M(i,"class","file-size svelte-14jis2k")},m(a,h){D(a,e,h),he(e,n),D(a,o,h),D(a,i,h),he(i,r)},p(a,h){h[0]&1&&t!==(t=(a[0].orig_name||a[0].url)+"")&&be(n,t),h[0]&1&&u!==(u=we(a[0].size)+"")&&be(r,u)},i:de,o:de,d(a){a&&(z(e),z(o),z(i))}}}function tl(l){var i;let e=(i=l[0])==null?void 0:i.url,t,n,o=ve(l);return{c(){o.c(),t=ee()},l(u){o.l(u),t=ee()},m(u,r){o.m(u,r),D(u,t,r),n=!0},p(u,r){var a;r[0]&1&&ce(e,e=(a=u[0])==null?void 0:a.url)?(le(),v(o,1,1,de),ne(),o=ve(u),o.c(),k(o,1),o.m(t.parentNode,t)):o.p(u,r)},i(u){n||(k(o),n=!0)},o(u){v(o),n=!1},d(u){u&&z(t),o.d(u)}}}function ol(l){let e,t,n,o;const i=[al,il],u=[];function r(a,h){return a[1]==="upload"?0:a[1]==="webcam"?1:-1}return~(t=r(l))&&(n=u[t]=i[t](l)),{c(){e=Z("div"),n&&n.c(),this.h()},l(a){e=y(a,"DIV",{class:!0});var h=$(e);n&&n.l(h),h.forEach(z),this.h()},h(){M(e,"class","upload-container svelte-14jis2k")},m(a,h){D(a,e,h),~t&&u[t].m(e,null),o=!0},p(a,h){let s=t;t=r(a),t===s?~t&&u[t].p(a,h):(n&&(le(),v(u[s],1,1,()=>{u[s]=null}),ne()),~t?(n=u[t],n?n.p(a,h):(n=u[t]=i[t](a),n.c()),k(n,1),n.m(e,null)):n=null)},i(a){o||(k(n),o=!0)},o(a){v(n),o=!1},d(a){a&&z(e),~t&&u[t].d()}}}function ve(l){var n;let e,t;return e=new xe({props:{upload:l[15],root:l[11],interactive:!0,autoplay:l[10],src:l[0].url,subtitle:(n=l[3])==null?void 0:n.url,is_stream:!1,mirror:l[8].mirror&&l[1]==="webcam",label:l[5],handle_change:l[23],handle_reset_value:l[13],loop:l[17],value:l[0],i18n:l[12],show_download_button:l[6],handle_clear:l[22],has_change_history:l[19]}}),e.$on("play",l[32]),e.$on("pause",l[33]),e.$on("stop",l[34]),e.$on("end",l[35]),e.$on("error",l[36]),{c(){B(e.$$.fragment)},l(o){S(e.$$.fragment,o)},m(o,i){E(e,o,i),t=!0},p(o,i){var r;const u={};i[0]&32768&&(u.upload=o[15]),i[0]&2048&&(u.root=o[11]),i[0]&1024&&(u.autoplay=o[10]),i[0]&1&&(u.src=o[0].url),i[0]&8&&(u.subtitle=(r=o[3])==null?void 0:r.url),i[0]&258&&(u.mirror=o[8].mirror&&o[1]==="webcam"),i[0]&32&&(u.label=o[5]),i[0]&8192&&(u.handle_reset_value=o[13]),i[0]&131072&&(u.loop=o[17]),i[0]&1&&(u.value=o[0]),i[0]&4096&&(u.i18n=o[12]),i[0]&64&&(u.show_download_button=o[6]),i[0]&524288&&(u.has_change_history=o[19]),e.$set(u)},i(o){t||(k(e.$$.fragment,o),t=!0)},o(o){v(e.$$.fragment,o),t=!1},d(o){j(e,o)}}}function il(l){let e,t;return e=new $e({props:{root:l[11],mirror_webcam:l[8].mirror,webcam_constraints:l[8].constraints,include_audio:l[9],mode:"video",i18n:l[12],upload:l[15],stream_every:1}}),e.$on("error",l[29]),e.$on("capture",l[24]),e.$on("start_recording",l[30]),e.$on("stop_recording",l[31]),{c(){B(e.$$.fragment)},l(n){S(e.$$.fragment,n)},m(n,o){E(e,n,o),t=!0},p(n,o){const i={};o[0]&2048&&(i.root=n[11]),o[0]&256&&(i.mirror_webcam=n[8].mirror),o[0]&256&&(i.webcam_constraints=n[8].constraints),o[0]&512&&(i.include_audio=n[9]),o[0]&4096&&(i.i18n=n[12]),o[0]&32768&&(i.upload=n[15]),e.$set(i)},i(n){t||(k(e.$$.fragment,n),t=!0)},o(n){v(e.$$.fragment,n),t=!1},d(n){j(e,n)}}}function al(l){let e,t,n,o;function i(a){l[26](a)}function u(a){l[27](a)}let r={filetype:"video/x-m4v,video/*",max_file_size:l[14],root:l[11],upload:l[15],stream_handler:l[16],aria_label:l[12]("video.drop_to_upload"),$$slots:{default:[sl]},$$scope:{ctx:l}};return l[18]!==void 0&&(r.dragging=l[18]),l[2]!==void 0&&(r.uploading=l[2]),e=new Xe({props:r}),X.push(()=>Y(e,"dragging",i)),X.push(()=>Y(e,"uploading",u)),e.$on("load",l[21]),e.$on("error",l[28]),{c(){B(e.$$.fragment)},l(a){S(e.$$.fragment,a)},m(a,h){E(e,a,h),o=!0},p(a,h){const s={};h[0]&16384&&(s.max_file_size=a[14]),h[0]&2048&&(s.root=a[11]),h[0]&32768&&(s.upload=a[15]),h[0]&65536&&(s.stream_handler=a[16]),h[0]&4096&&(s.aria_label=a[12]("video.drop_to_upload")),h[1]&128&&(s.$$scope={dirty:h,ctx:a}),!t&&h[0]&262144&&(t=!0,s.dragging=a[18],x(()=>t=!1)),!n&&h[0]&4&&(n=!0,s.uploading=a[2],x(()=>n=!1)),e.$set(s)},i(a){o||(k(e.$$.fragment,a),o=!0)},o(a){v(e.$$.fragment,a),o=!1},d(a){j(e,a)}}}function sl(l){let e;const t=l[25].default,n=Ke(t,l,l[38],null);return{c(){n&&n.c()},l(o){n&&n.l(o)},m(o,i){n&&n.m(o,i),e=!0},p(o,i){n&&n.p&&(!e||i[1]&128)&&Me(n,t,o,o[38],e?Re(t,o[38],i,null):Qe(o[38]),null)},i(o){e||(k(n,o),e=!0)},o(o){v(n,o),e=!1},d(o){n&&n.d(o)}}}function ul(l){let e,t,n,o,i,u,r,a,h;e=new Ye({props:{show_label:l[7],Icon:Ze,label:l[5]||"Video"}});const s=[ol,tl,nl],d=[];function p(c,b){var V;return c[0]===null||c[0].url===void 0?0:(V=c[0])!=null&&V.url?1:c[0].size?2:-1}~(o=p(l))&&(i=d[o]=s[o](l));function m(c){l[37](c)}let U={sources:l[4],handle_clear:l[22]};return l[1]!==void 0&&(U.active_source=l[1]),r=new ye({props:U}),X.push(()=>Y(r,"active_source",m)),{c(){B(e.$$.fragment),t=H(),n=Z("div"),i&&i.c(),u=H(),B(r.$$.fragment),this.h()},l(c){S(e.$$.fragment,c),t=K(c),n=y(c,"DIV",{"data-testid":!0,class:!0});var b=$(n);i&&i.l(b),u=K(b),S(r.$$.fragment,b),b.forEach(z),this.h()},h(){M(n,"data-testid","video"),M(n,"class","video-container svelte-14jis2k")},m(c,b){E(e,c,b),D(c,t,b),D(c,n,b),~o&&d[o].m(n,null),he(n,u),E(r,n,null),h=!0},p(c,b){const V={};b[0]&128&&(V.show_label=c[7]),b[0]&32&&(V.label=c[5]||"Video"),e.$set(V);let N=o;o=p(c),o===N?~o&&d[o].p(c,b):(i&&(le(),v(d[N],1,1,()=>{d[N]=null}),ne()),~o?(i=d[o],i?i.p(c,b):(i=d[o]=s[o](c),i.c()),k(i,1),i.m(n,u)):i=null);const P={};b[0]&16&&(P.sources=c[4]),!a&&b[0]&2&&(a=!0,P.active_source=c[1],x(()=>a=!1)),r.$set(P)},i(c){h||(k(e.$$.fragment,c),k(i),k(r.$$.fragment,c),h=!0)},o(c){v(e.$$.fragment,c),v(i),v(r.$$.fragment,c),h=!1},d(c){c&&(z(t),z(n)),j(e,c),~o&&d[o].d(),j(r)}}}function rl(l,e,t){let{$$slots:n={},$$scope:o}=e,{value:i=null}=e,{subtitle:u=null}=e,{sources:r=["webcam","upload"]}=e,{label:a=void 0}=e,{show_download_button:h=!1}=e,{show_label:s=!0}=e,{webcam_options:d}=e,{include_audio:p}=e,{autoplay:m}=e,{root:U}=e,{i18n:c}=e,{active_source:b="webcam"}=e,{handle_reset_value:V=()=>{}}=e,{max_file_size:N=null}=e,{upload:P}=e,{stream_handler:L}=e,{loop:g}=e,{uploading:q=!1}=e,T=!1;const I=He();function Q({detail:_}){t(0,i=_),I("change",_),I("upload",_)}function A(){t(0,i=null),I("change",null),I("clear")}function C(_){t(19,T=!0),I("change",_)}function F({detail:_}){I("change",_)}let J=!1;function G(_){J=_,t(18,J)}function O(_){q=_,t(2,q)}const te=({detail:_})=>I("error",_);function R(_){W.call(this,l,_)}function oe(_){W.call(this,l,_)}function ie(_){W.call(this,l,_)}function ae(_){W.call(this,l,_)}function se(_){W.call(this,l,_)}function ue(_){W.call(this,l,_)}function re(_){W.call(this,l,_)}function _e(_){W.call(this,l,_)}function fe(_){b=_,t(1,b)}return l.$$set=_=>{"value"in _&&t(0,i=_.value),"subtitle"in _&&t(3,u=_.subtitle),"sources"in _&&t(4,r=_.sources),"label"in _&&t(5,a=_.label),"show_download_button"in _&&t(6,h=_.show_download_button),"show_label"in _&&t(7,s=_.show_label),"webcam_options"in _&&t(8,d=_.webcam_options),"include_audio"in _&&t(9,p=_.include_audio),"autoplay"in _&&t(10,m=_.autoplay),"root"in _&&t(11,U=_.root),"i18n"in _&&t(12,c=_.i18n),"active_source"in _&&t(1,b=_.active_source),"handle_reset_value"in _&&t(13,V=_.handle_reset_value),"max_file_size"in _&&t(14,N=_.max_file_size),"upload"in _&&t(15,P=_.upload),"stream_handler"in _&&t(16,L=_.stream_handler),"loop"in _&&t(17,g=_.loop),"uploading"in _&&t(2,q=_.uploading),"$$scope"in _&&t(38,o=_.$$scope)},l.$$.update=()=>{l.$$.dirty[0]&262144&&I("drag",J)},[i,b,q,u,r,a,h,s,d,p,m,U,c,V,N,P,L,g,J,T,I,Q,A,C,F,n,G,O,te,R,oe,ie,ae,se,ue,re,_e,fe,o]}class _l extends ke{constructor(e){super(),pe(this,e,rl,ul,ce,{value:0,subtitle:3,sources:4,label:5,show_download_button:6,show_label:7,webcam_options:8,include_audio:9,autoplay:10,root:11,i18n:12,active_source:1,handle_reset_value:13,max_file_size:14,upload:15,stream_handler:16,loop:17,uploading:2},null,[-1,-1])}}const fl=_l;function hl(l){let e,t;return e=new Be({props:{visible:l[4],variant:l[0]===null&&l[23]==="upload"?"dashed":"solid",border_mode:l[26]?"focus":"base",padding:!1,elem_id:l[2],elem_classes:l[3],height:l[9],width:l[10],container:l[11],scale:l[12],min_width:l[13],allow_overflow:!1,$$slots:{default:[ml]},$$scope:{ctx:l}}}),{c(){B(e.$$.fragment)},l(n){S(e.$$.fragment,n)},m(n,o){E(e,n,o),t=!0},p(n,o){const i={};o[0]&16&&(i.visible=n[4]),o[0]&8388609&&(i.variant=n[0]===null&&n[23]==="upload"?"dashed":"solid"),o[0]&67108864&&(i.border_mode=n[26]?"focus":"base"),o[0]&4&&(i.elem_id=n[2]),o[0]&8&&(i.elem_classes=n[3]),o[0]&512&&(i.height=n[9]),o[0]&1024&&(i.width=n[10]),o[0]&2048&&(i.container=n[11]),o[0]&4096&&(i.scale=n[12]),o[0]&8192&&(i.min_width=n[13]),o[0]&133906914|o[1]&8388608&&(i.$$scope={dirty:o,ctx:n}),e.$set(i)},i(n){t||(k(e.$$.fragment,n),t=!0)},o(n){v(e.$$.fragment,n),t=!1},d(n){j(e,n)}}}function dl(l){let e,t;return e=new Be({props:{visible:l[4],variant:l[0]===null&&l[23]==="upload"?"dashed":"solid",border_mode:l[26]?"focus":"base",padding:!1,elem_id:l[2],elem_classes:l[3],height:l[9],width:l[10],container:l[11],scale:l[12],min_width:l[13],allow_overflow:!1,$$slots:{default:[gl]},$$scope:{ctx:l}}}),{c(){B(e.$$.fragment)},l(n){S(e.$$.fragment,n)},m(n,o){E(e,n,o),t=!0},p(n,o){const i={};o[0]&16&&(i.visible=n[4]),o[0]&8388609&&(i.variant=n[0]===null&&n[23]==="upload"?"dashed":"solid"),o[0]&67108864&&(i.border_mode=n[26]?"focus":"base"),o[0]&4&&(i.elem_id=n[2]),o[0]&8&&(i.elem_classes=n[3]),o[0]&512&&(i.height=n[9]),o[0]&1024&&(i.width=n[10]),o[0]&2048&&(i.container=n[11]),o[0]&4096&&(i.scale=n[12]),o[0]&8192&&(i.min_width=n[13]),o[0]&52674850|o[1]&8388608&&(i.$$scope={dirty:o,ctx:n}),e.$set(i)},i(n){t||(k(e.$$.fragment,n),t=!0)},o(n){v(e.$$.fragment,n),t=!1},d(n){j(e,n)}}}function cl(l){let e,t;return e=new ll({props:{i18n:l[17].i18n,type:"video"}}),{c(){B(e.$$.fragment)},l(n){S(e.$$.fragment,n)},m(n,o){E(e,n,o),t=!0},p(n,o){const i={};o[0]&131072&&(i.i18n=n[17].i18n),e.$set(i)},i(n){t||(k(e.$$.fragment,n),t=!0)},o(n){v(e.$$.fragment,n),t=!1},d(n){j(e,n)}}}function ml(l){let e,t,n,o,i;const u=[{autoscroll:l[17].autoscroll},{i18n:l[17].i18n},l[1]];let r={};for(let s=0;s<u.length;s+=1)r=ze(r,u[s]);e=new Se({props:r}),e.$on("clear_status",l[41]);function a(s){l[44](s)}let h={value:l[24],subtitle:l[25],label:l[5],show_label:l[8],show_download_button:l[16],sources:l[6],active_source:l[23],webcam_options:l[19],include_audio:l[20],autoplay:l[14],root:l[7],loop:l[21],handle_reset_value:l[27],i18n:l[17].i18n,max_file_size:l[17].max_file_size,upload:l[42],stream_handler:l[43],$$slots:{default:[cl]},$$scope:{ctx:l}};return l[22]!==void 0&&(h.uploading=l[22]),n=new fl({props:h}),X.push(()=>Y(n,"uploading",a)),n.$on("change",l[28]),n.$on("drag",l[45]),n.$on("error",l[29]),n.$on("clear",l[46]),n.$on("play",l[47]),n.$on("pause",l[48]),n.$on("upload",l[49]),n.$on("stop",l[50]),n.$on("end",l[51]),n.$on("start_recording",l[52]),n.$on("stop_recording",l[53]),{c(){B(e.$$.fragment),t=H(),B(n.$$.fragment)},l(s){S(e.$$.fragment,s),t=K(s),S(n.$$.fragment,s)},m(s,d){E(e,s,d),D(s,t,d),E(n,s,d),i=!0},p(s,d){const p=d[0]&131074?Ve(u,[d[0]&131072&&{autoscroll:s[17].autoscroll},d[0]&131072&&{i18n:s[17].i18n},d[0]&2&&Ie(s[1])]):{};e.$set(p);const m={};d[0]&16777216&&(m.value=s[24]),d[0]&33554432&&(m.subtitle=s[25]),d[0]&32&&(m.label=s[5]),d[0]&256&&(m.show_label=s[8]),d[0]&65536&&(m.show_download_button=s[16]),d[0]&64&&(m.sources=s[6]),d[0]&8388608&&(m.active_source=s[23]),d[0]&524288&&(m.webcam_options=s[19]),d[0]&1048576&&(m.include_audio=s[20]),d[0]&16384&&(m.autoplay=s[14]),d[0]&128&&(m.root=s[7]),d[0]&2097152&&(m.loop=s[21]),d[0]&131072&&(m.i18n=s[17].i18n),d[0]&131072&&(m.max_file_size=s[17].max_file_size),d[0]&131072&&(m.upload=s[42]),d[0]&131072&&(m.stream_handler=s[43]),d[0]&131072|d[1]&8388608&&(m.$$scope={dirty:d,ctx:s}),!o&&d[0]&4194304&&(o=!0,m.uploading=s[22],x(()=>o=!1)),n.$set(m)},i(s){i||(k(e.$$.fragment,s),k(n.$$.fragment,s),i=!0)},o(s){v(e.$$.fragment,s),v(n.$$.fragment,s),i=!1},d(s){s&&z(t),j(e,s),j(n,s)}}}function gl(l){let e,t,n,o;const i=[{autoscroll:l[17].autoscroll},{i18n:l[17].i18n},l[1]];let u={};for(let r=0;r<i.length;r+=1)u=ze(u,i[r]);return e=new Se({props:u}),e.$on("clear_status",l[33]),n=new el({props:{value:l[24],subtitle:l[25],label:l[5],show_label:l[8],autoplay:l[14],loop:l[21],show_share_button:l[15],show_download_button:l[16],i18n:l[17].i18n,upload:l[34]}}),n.$on("play",l[35]),n.$on("pause",l[36]),n.$on("stop",l[37]),n.$on("end",l[38]),n.$on("share",l[39]),n.$on("error",l[40]),{c(){B(e.$$.fragment),t=H(),B(n.$$.fragment)},l(r){S(e.$$.fragment,r),t=K(r),S(n.$$.fragment,r)},m(r,a){E(e,r,a),D(r,t,a),E(n,r,a),o=!0},p(r,a){const h=a[0]&131074?Ve(i,[a[0]&131072&&{autoscroll:r[17].autoscroll},a[0]&131072&&{i18n:r[17].i18n},a[0]&2&&Ie(r[1])]):{};e.$set(h);const s={};a[0]&16777216&&(s.value=r[24]),a[0]&33554432&&(s.subtitle=r[25]),a[0]&32&&(s.label=r[5]),a[0]&256&&(s.show_label=r[8]),a[0]&16384&&(s.autoplay=r[14]),a[0]&2097152&&(s.loop=r[21]),a[0]&32768&&(s.show_share_button=r[15]),a[0]&65536&&(s.show_download_button=r[16]),a[0]&131072&&(s.i18n=r[17].i18n),a[0]&131072&&(s.upload=r[34]),n.$set(s)},i(r){o||(k(e.$$.fragment,r),k(n.$$.fragment,r),o=!0)},o(r){v(e.$$.fragment,r),v(n.$$.fragment,r),o=!1},d(r){r&&z(t),j(e,r),j(n,r)}}}function bl(l){let e,t,n,o;const i=[dl,hl],u=[];function r(a,h){return a[18]?1:0}return e=r(l),t=u[e]=i[e](l),{c(){t.c(),n=ee()},l(a){t.l(a),n=ee()},m(a,h){u[e].m(a,h),D(a,n,h),o=!0},p(a,h){let s=e;e=r(a),e===s?u[e].p(a,h):(le(),v(u[s],1,1,()=>{u[s]=null}),ne(),t=u[e],t?t.p(a,h):(t=u[e]=i[e](a),t.c()),k(t,1),t.m(n.parentNode,n))},i(a){o||(k(t),o=!0)},o(a){v(t),o=!1},d(a){a&&z(n),u[e].d(a)}}}function wl(l,e,t){let{elem_id:n=""}=e,{elem_classes:o=[]}=e,{visible:i=!0}=e,{value:u=null}=e,r=null,{label:a}=e,{sources:h}=e,{root:s}=e,{show_label:d}=e,{loading_status:p}=e,{height:m}=e,{width:U}=e,{container:c=!1}=e,{scale:b=null}=e,{min_width:V=void 0}=e,{autoplay:N=!1}=e,{show_share_button:P=!0}=e,{show_download_button:L}=e,{gradio:g}=e,{interactive:q}=e,{webcam_options:T}=e,{include_audio:I}=e,{loop:Q=!1}=e,{input_ready:A}=e,C=!1,F=null,J=null,G,O=u;const te=()=>{O===null||u===O||t(0,u=O)};let R=!1;function oe({detail:f}){f!=null?t(0,u={video:f,subtitles:null}):t(0,u=null)}function ie({detail:f}){const[Fe,Ge]=f.includes("Invalid file type")?["warning","complete"]:["error","error"];t(1,p=p||{}),t(1,p.status=Ge,p),t(1,p.message=f,p),g.dispatch(Fe,f)}const ae=()=>g.dispatch("clear_status",p),se=(...f)=>g.client.upload(...f),ue=()=>g.dispatch("play"),re=()=>g.dispatch("pause"),_e=()=>g.dispatch("stop"),fe=()=>g.dispatch("end"),_=({detail:f})=>g.dispatch("share",f),Ee=({detail:f})=>g.dispatch("error",f),je=()=>g.dispatch("clear_status",p),De=(...f)=>g.client.upload(...f),Ne=(...f)=>g.client.stream(...f);function Pe(f){C=f,t(22,C)}const Ue=({detail:f})=>t(26,R=f),Je=()=>g.dispatch("clear"),Oe=()=>g.dispatch("play"),We=()=>g.dispatch("pause"),qe=()=>g.dispatch("upload"),Ce=()=>g.dispatch("stop"),Le=()=>g.dispatch("end"),Te=()=>g.dispatch("start_recording"),Ae=()=>g.dispatch("stop_recording");return l.$$set=f=>{"elem_id"in f&&t(2,n=f.elem_id),"elem_classes"in f&&t(3,o=f.elem_classes),"visible"in f&&t(4,i=f.visible),"value"in f&&t(0,u=f.value),"label"in f&&t(5,a=f.label),"sources"in f&&t(6,h=f.sources),"root"in f&&t(7,s=f.root),"show_label"in f&&t(8,d=f.show_label),"loading_status"in f&&t(1,p=f.loading_status),"height"in f&&t(9,m=f.height),"width"in f&&t(10,U=f.width),"container"in f&&t(11,c=f.container),"scale"in f&&t(12,b=f.scale),"min_width"in f&&t(13,V=f.min_width),"autoplay"in f&&t(14,N=f.autoplay),"show_share_button"in f&&t(15,P=f.show_share_button),"show_download_button"in f&&t(16,L=f.show_download_button),"gradio"in f&&t(17,g=f.gradio),"interactive"in f&&t(18,q=f.interactive),"webcam_options"in f&&t(19,T=f.webcam_options),"include_audio"in f&&t(20,I=f.include_audio),"loop"in f&&t(21,Q=f.loop),"input_ready"in f&&t(30,A=f.input_ready)},l.$$.update=()=>{l.$$.dirty[0]&4194304&&t(30,A=!C),l.$$.dirty[0]&1|l.$$.dirty[1]&2&&u&&O===null&&t(32,O=u),l.$$.dirty[0]&8388672&&h&&!G&&t(23,G=h[0]),l.$$.dirty[0]&1&&(u!=null?(t(24,F=u.video),t(25,J=u.subtitles)):(t(24,F=null),t(25,J=null))),l.$$.dirty[0]&131073|l.$$.dirty[1]&1&&JSON.stringify(u)!==JSON.stringify(r)&&(t(31,r=u),g.dispatch("change"))},[u,p,n,o,i,a,h,s,d,m,U,c,b,V,N,P,L,g,q,T,I,Q,C,G,F,J,R,te,oe,ie,A,r,O,ae,se,ue,re,_e,fe,_,Ee,je,De,Ne,Pe,Ue,Je,Oe,We,qe,Ce,Le,Te,Ae]}class vl extends ke{constructor(e){super(),pe(this,e,wl,bl,ce,{elem_id:2,elem_classes:3,visible:4,value:0,label:5,sources:6,root:7,show_label:8,loading_status:1,height:9,width:10,container:11,scale:12,min_width:13,autoplay:14,show_share_button:15,show_download_button:16,gradio:17,interactive:18,webcam_options:19,include_audio:20,loop:21,input_ready:30},null,[-1,-1])}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),w()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),w()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),w()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),w()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),w()}get sources(){return this.$$.ctx[6]}set sources(e){this.$$set({sources:e}),w()}get root(){return this.$$.ctx[7]}set root(e){this.$$set({root:e}),w()}get show_label(){return this.$$.ctx[8]}set show_label(e){this.$$set({show_label:e}),w()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),w()}get height(){return this.$$.ctx[9]}set height(e){this.$$set({height:e}),w()}get width(){return this.$$.ctx[10]}set width(e){this.$$set({width:e}),w()}get container(){return this.$$.ctx[11]}set container(e){this.$$set({container:e}),w()}get scale(){return this.$$.ctx[12]}set scale(e){this.$$set({scale:e}),w()}get min_width(){return this.$$.ctx[13]}set min_width(e){this.$$set({min_width:e}),w()}get autoplay(){return this.$$.ctx[14]}set autoplay(e){this.$$set({autoplay:e}),w()}get show_share_button(){return this.$$.ctx[15]}set show_share_button(e){this.$$set({show_share_button:e}),w()}get show_download_button(){return this.$$.ctx[16]}set show_download_button(e){this.$$set({show_download_button:e}),w()}get gradio(){return this.$$.ctx[17]}set gradio(e){this.$$set({gradio:e}),w()}get interactive(){return this.$$.ctx[18]}set interactive(e){this.$$set({interactive:e}),w()}get webcam_options(){return this.$$.ctx[19]}set webcam_options(e){this.$$set({webcam_options:e}),w()}get include_audio(){return this.$$.ctx[20]}set include_audio(e){this.$$set({include_audio:e}),w()}get loop(){return this.$$.ctx[21]}set loop(e){this.$$set({loop:e}),w()}get input_ready(){return this.$$.ctx[30]}set input_ready(e){this.$$set({input_ready:e}),w()}}const Ul=vl;export{Ll as BaseExample,fl as BaseInteractiveVideo,xe as BasePlayer,el as BaseStaticVideo,Ul as default,Wl as loaded,ql as playable,we as prettyBytes};
//# sourceMappingURL=index.CMpgkpBY.js.map
