import { c as create_ssr_component, v as validate_component } from './ssr-C3HYbsxA.js';
import q from './StaticAudio-CKEuBlEq.js';
import { T as Tt } from './InteractiveAudio-DKNRgR2D.js';
import { m as mt, z as zA, _ as _e } from './2-DJbI4FWc.js';
export { Y as BasePlayer } from './AudioPlayer-CFZK-O44.js';
export { default as BaseExample } from './Example4-7u--UOCG.js';
import './ModifyUpload-wYzp15o5.js';
import './Component-NmRBwSfF.js';
import './index-ClteBeTX.js';
import 'path';
import 'url';
import 'fs';
import './hls-DpKhbIaL.js';

const ae=create_ssr_component((i,t,e,le)=>{let{value_is_output:f=!1}=t,{elem_id:c=""}=t,{elem_classes:s=[]}=t,{visible:h=!0}=t,{interactive:O}=t,{value:l=null}=t,{sources:m}=t,{label:w}=t,{root:U}=t,{show_label:n}=t,{container:y=!0}=t,{scale:S=null}=t,{min_width:x=void 0}=t,{loading_status:k}=t,{autoplay:E=!1}=t,{loop:A=!1}=t,{show_download_button:B}=t,{show_share_button:j=!1}=t,{editable:I=!0}=t,{waveform_options:r={show_recording_waveform:!0}}=t,{pending:J}=t,{streaming:N}=t,{stream_every:R}=t,{input_ready:W}=t,{recording:C=!1}=t,G=!1,q$1="closed",M;function D(o){q$1=o,M(o);}const F=()=>q$1;let{set_time_limit:P}=t,{gradio:a}=t,H=null,T,u=l;const Y=()=>{u===null||l===u||(l=u);};let z,d;const K={color:r.trim_region_color,drag:!0,resize:!0};t.value_is_output===void 0&&e.value_is_output&&f!==void 0&&e.value_is_output(f),t.elem_id===void 0&&e.elem_id&&c!==void 0&&e.elem_id(c),t.elem_classes===void 0&&e.elem_classes&&s!==void 0&&e.elem_classes(s),t.visible===void 0&&e.visible&&h!==void 0&&e.visible(h),t.interactive===void 0&&e.interactive&&O!==void 0&&e.interactive(O),t.value===void 0&&e.value&&l!==void 0&&e.value(l),t.sources===void 0&&e.sources&&m!==void 0&&e.sources(m),t.label===void 0&&e.label&&w!==void 0&&e.label(w),t.root===void 0&&e.root&&U!==void 0&&e.root(U),t.show_label===void 0&&e.show_label&&n!==void 0&&e.show_label(n),t.container===void 0&&e.container&&y!==void 0&&e.container(y),t.scale===void 0&&e.scale&&S!==void 0&&e.scale(S),t.min_width===void 0&&e.min_width&&x!==void 0&&e.min_width(x),t.loading_status===void 0&&e.loading_status&&k!==void 0&&e.loading_status(k),t.autoplay===void 0&&e.autoplay&&E!==void 0&&e.autoplay(E),t.loop===void 0&&e.loop&&A!==void 0&&e.loop(A),t.show_download_button===void 0&&e.show_download_button&&B!==void 0&&e.show_download_button(B),t.show_share_button===void 0&&e.show_share_button&&j!==void 0&&e.show_share_button(j),t.editable===void 0&&e.editable&&I!==void 0&&e.editable(I),t.waveform_options===void 0&&e.waveform_options&&r!==void 0&&e.waveform_options(r),t.pending===void 0&&e.pending&&J!==void 0&&e.pending(J),t.streaming===void 0&&e.streaming&&N!==void 0&&e.streaming(N),t.stream_every===void 0&&e.stream_every&&R!==void 0&&e.stream_every(R),t.input_ready===void 0&&e.input_ready&&W!==void 0&&e.input_ready(W),t.recording===void 0&&e.recording&&C!==void 0&&e.recording(C),t.modify_stream_state===void 0&&e.modify_stream_state&&D!==void 0&&e.modify_stream_state(D),t.get_stream_state===void 0&&e.get_stream_state&&F!==void 0&&e.get_stream_state(F),t.set_time_limit===void 0&&e.set_time_limit&&P!==void 0&&e.set_time_limit(P),t.gradio===void 0&&e.gradio&&a!==void 0&&e.gradio(a);let _,L,g=i.head;do _=!0,i.head=g,W=!G,l&&u===null&&(u=l),JSON.stringify(l)!==JSON.stringify(H)&&(H=l,a.dispatch("change"),f||a.dispatch("input")),!T&&m&&(T=m[0]),d={height:50,barWidth:2,barGap:3,cursorWidth:2,cursorColor:"#ddd5e9",autoplay:E,barRadius:10,dragToSeek:!0,normalize:!0,minPxPerSec:20},L=`  ${O?`${validate_component(mt,"Block").$$render(i,{variant:l===null&&T==="upload"?"dashed":"solid",border_mode:z?"focus":"base",padding:!1,allow_overflow:!1,elem_id:c,elem_classes:s,visible:h,container:y,scale:S,min_width:x},{},{default:()=>`${validate_component(zA,"StatusTracker").$$render(i,Object.assign({},{autoscroll:a.autoscroll},{i18n:a.i18n},k),{},{})} ${validate_component(Tt,"InteractiveAudio").$$render(i,{label:w,show_label:n,show_download_button:B,value:l,root:U,sources:m,active_source:T,pending:J,streaming:N,loop:A,max_file_size:a.max_file_size,handle_reset_value:Y,editable:I,i18n:a.i18n,waveform_settings:d,waveform_options:r,trim_region_settings:K,stream_every:R,upload:(...o)=>a.client.upload(...o),stream_handler:(...o)=>a.client.stream(...o),recording:C,dragging:z,uploading:G,modify_stream:M,set_time_limit:P},{recording:o=>{C=o,_=!1;},dragging:o=>{z=o,_=!1;},uploading:o=>{G=o,_=!1;},modify_stream:o=>{M=o,_=!1;},set_time_limit:o=>{P=o,_=!1;}},{default:()=>`${validate_component(_e,"UploadText").$$render(i,{i18n:a.i18n,type:"audio"},{},{})}`})}`})}`:`${validate_component(mt,"Block").$$render(i,{variant:"solid",border_mode:z?"focus":"base",padding:!1,allow_overflow:!1,elem_id:c,elem_classes:s,visible:h,container:y,scale:S,min_width:x},{},{default:()=>`${validate_component(zA,"StatusTracker").$$render(i,Object.assign({},{autoscroll:a.autoscroll},{i18n:a.i18n},k),{},{})} ${validate_component(q,"StaticAudio").$$render(i,{i18n:a.i18n,show_label:n,show_download_button:B,show_share_button:j,value:l,label:w,loop:A,waveform_settings:d,waveform_options:r,editable:I},{},{})}`})}`}`;while(!_);return L}),ue=ae;

export { Tt as BaseInteractiveAudio, q as BaseStaticAudio, ue as default };
//# sourceMappingURL=index21-Bfx-D4Rz.js.map
