import{a as f}from"./KHR_interactivity-DTxiAnOo.js";import{b as u,c as _,R as m}from"./declarationMapper-BZjsjg7g.js";import{R as b}from"./index-Dpxo-yl_.js";import{AnimationGroup as y}from"./animationGroup-Bwms8CyJ.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";import"./bone-kZWM5-u7.js";class V extends f{constructor(i){super(i,["animationLoop","animationEnd","animationGroupLoop"]),this.config=i,this.speed=this.registerDataInput("speed",u),this.loop=this.registerDataInput("loop",_),this.from=this.registerDataInput("from",u,0),this.to=this.registerDataInput("to",u),this.currentFrame=this.registerDataOutput("currentFrame",u),this.currentTime=this.registerDataOutput("currentTime",u),this.currentAnimationGroup=this.registerDataOutput("currentAnimationGroup",m),this.animationGroup=this.registerDataInput("animationGroup",m,i?.animationGroup),this.animation=this.registerDataInput("animation",m),this.object=this.registerDataInput("object",m)}_preparePendingTasks(i){const t=this.animationGroup.getValue(i),r=this.animation.getValue(i);if(!t&&!r)return this._reportError(i,"No animation or animation group provided");{const a=this.currentAnimationGroup.getValue(i);a&&a!==t&&a.dispose();let n=t;if(r&&!n){const o=this.object.getValue(i);if(!o)return this._reportError(i,"No target object provided");const h=Array.isArray(r)?r:[r],G=h[0].name;n=new y("flowGraphAnimationGroup-"+G+"-"+o.name,i.configuration.scene);let g=!1;const A=i._getGlobalContextVariable("interpolationAnimations",[]);for(const c of h)n.addTargetedAnimation(c,o),A.indexOf(c.uniqueId)!==-1&&(g=!0);g&&this._checkInterpolationDuplications(i,h,o)}const s=this.speed.getValue(i)||1,e=this.from.getValue(i)??0,p=this.to.getValue(i)||n.to,d=!isFinite(p)||this.loop.getValue(i);this.currentAnimationGroup.setValue(n,i);const l=i._getGlobalContextVariable("currentlyRunningAnimationGroups",[]);l.indexOf(n.uniqueId)!==-1&&n.stop();try{n.start(d,s,e,p),n.onAnimationGroupEndObservable.add(()=>this._onAnimationGroupEnd(i)),n.onAnimationEndObservable.add(()=>this._eventsSignalOutputs.animationEnd._activateSignal(i)),n.onAnimationLoopObservable.add(()=>this._eventsSignalOutputs.animationLoop._activateSignal(i)),n.onAnimationGroupLoopObservable.add(()=>this._eventsSignalOutputs.animationGroupLoop._activateSignal(i)),l.push(n.uniqueId),i._setGlobalContextVariable("currentlyRunningAnimationGroups",l)}catch(o){this._reportError(i,o)}}}_reportError(i,t){super._reportError(i,t),this.currentFrame.setValue(-1,i),this.currentTime.setValue(-1,i)}_executeOnTick(i){const t=this.currentAnimationGroup.getValue(i);t&&(this.currentFrame.setValue(t.getCurrentFrame(),i),this.currentTime.setValue(t.animatables[0]?.elapsedTime??0,i))}_execute(i){this._startPendingTasks(i)}_onAnimationGroupEnd(i){this._removeFromCurrentlyRunning(i,this.currentAnimationGroup.getValue(i)),this._resetAfterCanceled(i),this.done._activateSignal(i)}_checkInterpolationDuplications(i,t,r){const a=i._getGlobalContextVariable("currentlyRunningAnimationGroups",[]);for(const n of a){const s=i.assetsContext.animationGroups.find(e=>e.uniqueId===n);if(s)for(const e of s.targetedAnimations)for(const p of t)e.animation.targetProperty===p.targetProperty&&e.target===r&&this._stopAnimationGroup(i,s)}}_stopAnimationGroup(i,t){t.stop(!0),t.dispose(),this._removeFromCurrentlyRunning(i,t)}_removeFromCurrentlyRunning(i,t){const r=i._getGlobalContextVariable("currentlyRunningAnimationGroups",[]),a=r.indexOf(t.uniqueId);a!==-1&&(r.splice(a,1),i._setGlobalContextVariable("currentlyRunningAnimationGroups",r))}_cancelPendingTasks(i){const t=this.currentAnimationGroup.getValue(i);t&&this._stopAnimationGroup(i,t)}getClassName(){return"FlowGraphPlayAnimationBlock"}}b("FlowGraphPlayAnimationBlock",V);export{V as FlowGraphPlayAnimationBlock};
//# sourceMappingURL=flowGraphPlayAnimationBlock-SS81a80Y.js.map
