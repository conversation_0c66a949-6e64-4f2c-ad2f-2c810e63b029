import{SvelteComponent as D,init as N,safe_not_equal as R,element as _,create_component as V,claim_element as m,children as g,claim_component as q,detach as f,attr as h,insert_hydration as d,mount_component as w,transition_in as z,transition_out as B,destroy_component as x,space as A,text as b,claim_space as k,get_svelte_dataset as F,claim_text as y,append_hydration as u,set_data as S}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{o as G}from"./2.B2AoQPnG.js";function I(r){let e,a,n=r[1][r[0][r[0].length-1].fn_index].api_name+"",l;return{c(){e=_("span"),a=b("/"),l=b(n),this.h()},l(t){e=m(t,"SPAN",{class:!0});var s=g(e);a=y(s,"/"),l=y(s,n),s.forEach(f),this.h()},h(){h(e,"class","api-name svelte-sy28j6")},m(t,s){d(t,e,s),u(e,a),u(e,l)},p(t,s){s&3&&n!==(n=t[1][t[0][t[0].length-1].fn_index].api_name+"")&&S(l,n)},d(t){t&&f(e)}}}function H(r){let e,a,n,l="Recording API Calls:",t,s,p,C,v=r[0].length+"",$,E,P,c=r[0].length>0&&I(r);return{c(){e=_("div"),a=A(),n=_("p"),n.textContent=l,t=A(),s=_("p"),p=_("span"),C=b("["),$=b(v),E=b("]"),P=A(),c&&c.c(),this.h()},l(i){e=m(i,"DIV",{class:!0}),g(e).forEach(f),a=k(i),n=m(i,"P",{class:!0,"data-svelte-h":!0}),F(n)!=="svelte-1fgk0o3"&&(n.textContent=l),t=k(i),s=m(i,"P",{class:!0});var o=g(s);p=m(o,"SPAN",{class:!0});var j=g(p);C=y(j,"["),$=y(j,v),E=y(j,"]"),j.forEach(f),P=k(o),c&&c.l(o),o.forEach(f),this.h()},h(){h(e,"class","loading-dot self-baseline svelte-sy28j6"),h(n,"class","self-baseline svelte-sy28j6"),h(p,"class","api-count svelte-sy28j6"),h(s,"class","self-baseline api-section svelte-sy28j6")},m(i,o){d(i,e,o),d(i,a,o),d(i,n,o),d(i,t,o),d(i,s,o),u(s,p),u(p,C),u(p,$),u(p,E),u(s,P),c&&c.m(s,null)},p(i,o){o&1&&v!==(v=i[0].length+"")&&S($,v),i[0].length>0?c?c.p(i,o):(c=I(i),c.c(),c.m(s,null)):c&&(c.d(1),c=null)},d(i){i&&(f(e),f(a),f(n),f(t),f(s)),c&&c.d()}}}function J(r){let e,a,n;return a=new G({props:{size:"sm",variant:"secondary",$$slots:{default:[H]},$$scope:{ctx:r}}}),{c(){e=_("div"),V(a.$$.fragment),this.h()},l(l){e=m(l,"DIV",{id:!0});var t=g(e);q(a.$$.fragment,t),t.forEach(f),this.h()},h(){h(e,"id","api-recorder")},m(l,t){d(l,e,t),w(a,e,null),n=!0},p(l,[t]){const s={};t&7&&(s.$$scope={dirty:t,ctx:l}),a.$set(s)},i(l){n||(z(a.$$.fragment,l),n=!0)},o(l){B(a.$$.fragment,l),n=!1},d(l){l&&f(e),x(a)}}}function K(r,e,a){let{api_calls:n=[]}=e,{dependencies:l}=e;return r.$$set=t=>{"api_calls"in t&&a(0,n=t.api_calls),"dependencies"in t&&a(1,l=t.dependencies)},[n,l]}class Q extends D{constructor(e){super(),N(this,e,K,J,R,{api_calls:0,dependencies:1})}}export{Q as default};
//# sourceMappingURL=ApiRecorder.r4QAwWei.js.map
