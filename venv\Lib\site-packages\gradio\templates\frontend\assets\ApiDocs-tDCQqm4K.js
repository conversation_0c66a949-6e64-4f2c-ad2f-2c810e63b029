import{C as Ml}from"./clear-CvR6Cdu7.js";import{a as Bl}from"./Blocks-DQBXfWaA.js";import"./index-B7J2Z2jS.js";import{B as gn}from"./Button-B3gqVEq2.js";import{L as Il}from"./index-B1FJGuzG.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import"./StreamingBar.svelte_svelte_type_style_lang-DyfAPhwM.js";import{B as We}from"./Block-CJdXVpa7.js";import"./Toast-BMPuxKCO.js";import"./index-CEGzm7H5.js";import"./utils-BsGrhMNe.js";import"./svelte/svelte.js";import"./Image-CnqB5dbD.js";import"./file-url-DoxvUUVV.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";/* empty css                                                   */import"./IconButton-C_HS7fTi.js";import"./Clear-By3xiIwg.js";import"./prism-python-MMh3z1bK.js";const{SvelteComponent:Gl,append:Ze,attr:At,create_component:Yl,destroy_component:Wl,detach:Xt,element:ht,flush:Zl,init:Ql,insert:Kt,listen:Xl,mount_component:Kl,safe_not_equal:xl,set_data:es,space:xt,text:yn,transition_in:ts,transition_out:ns}=window.__gradio__svelte__internal,{createEventDispatcher:ls}=window.__gradio__svelte__internal;function ss(l){let e,t,n,s,o,i,a,r,c,_,u,f,d,g,m;return f=new Ml({}),{c(){e=ht("div"),t=ht("h1"),t.textContent="API Docs",n=xt(),s=ht("p"),o=yn(`No API Routes found for
		`),i=ht("code"),a=yn(l[0]),r=xt(),c=ht("p"),c.innerHTML=`To expose an API endpoint of your app in this page, set the <code>api_name</code>
		parameter of the event listener.
		<br/>
		For more information, visit the
		<a href="https://gradio.app/sharing_your_app/#api-page" target="_blank">API Page guide</a>
		. To hide the API documentation button and this page, set
		<code>show_api=False</code>
		in the
		<code>Blocks.launch()</code>
		method.`,_=xt(),u=ht("button"),Yl(f.$$.fragment),At(i,"class","svelte-e1ha0f"),At(s,"class","attention svelte-e1ha0f"),At(e,"class","wrap prose svelte-e1ha0f"),At(u,"class","svelte-e1ha0f")},m(k,p){Kt(k,e,p),Ze(e,t),Ze(e,n),Ze(e,s),Ze(s,o),Ze(s,i),Ze(i,a),Ze(e,r),Ze(e,c),Kt(k,_,p),Kt(k,u,p),Kl(f,u,null),d=!0,g||(m=Xl(u,"click",l[2]),g=!0)},p(k,[p]){(!d||p&1)&&es(a,k[0])},i(k){d||(ts(f.$$.fragment,k),d=!0)},o(k){ns(f.$$.fragment,k),d=!1},d(k){k&&(Xt(e),Xt(_),Xt(u)),Wl(f),g=!1,m()}}}function is(l,e,t){const n=ls();let{root:s}=e;const o=()=>n("close");return l.$$set=i=>{"root"in i&&t(0,s=i.root)},[s,n,o]}class os extends Gl{constructor(e){super(),Ql(this,e,is,ss,xl,{root:0})}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),Zl()}}const{SvelteComponent:rs,append:Ce,attr:Ee,check_outros:as,create_component:Dl,destroy_component:Rl,detach:Ie,element:Me,empty:cs,flush:en,group_outros:_s,init:us,insert:De,listen:fs,mount_component:Ll,noop:ps,safe_not_equal:ds,set_data:wn,space:$t,src_url_equal:ms,text:et,transition_in:zt,transition_out:It}=window.__gradio__svelte__internal,{createEventDispatcher:hs}=window.__gradio__svelte__internal;function gs(l){let e;return{c(){e=et("API")},m(t,n){De(t,e,n)},d(t){t&&Ie(e)}}}function $s(l){let e;return{c(){e=et("MCP")},m(t,n){De(t,e,n)},d(t){t&&Ie(e)}}}function Cn(l){let e,t;return e=new gn({props:{size:"sm",variant:"secondary",elem_id:"start-api-recorder",$$slots:{default:[bs]},$$scope:{ctx:l}}}),e.$on("click",l[4]),{c(){Dl(e.$$.fragment)},m(n,s){Ll(e,n,s),t=!0},p(n,s){const o={};s&64&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(zt(e.$$.fragment,n),t=!0)},o(n){It(e.$$.fragment,n),t=!1},d(n){Rl(e,n)}}}function bs(l){let e,t,n;return{c(){e=Me("div"),t=$t(),n=Me("p"),n.textContent="API Recorder",Ee(e,"class","loading-dot self-baseline svelte-1i1gjw2"),Ee(n,"class","self-baseline btn-text svelte-1i1gjw2")},m(s,o){De(s,e,o),De(s,t,o),De(s,n,o)},p:ps,d(s){s&&(Ie(e),Ie(t),Ie(n))}}}function vs(l){let e;return{c(){e=et("MCP Tool")},m(t,n){De(t,e,n)},d(t){t&&Ie(e)}}}function ks(l){let e;return{c(){e=et("API endpoint")},m(t,n){De(t,e,n)},d(t){t&&Ie(e)}}}function Sn(l){let e;return{c(){e=et("s")},m(t,n){De(t,e,n)},d(t){t&&Ie(e)}}}function ys(l){let e,t,n,s,o,i,a,r,c,_,u,f,d,g,m,k,p,w,$,h,v,y,C;function A(F,U){return F[2]==="mcp"?$s:gs}let T=A(l),E=T(l),b=l[2]!=="mcp"&&Cn(l);function S(F,U){return F[2]!=="mcp"?ks:vs}let z=S(l),N=z(l),q=l[1]>1&&Sn();return h=new Ml({}),{c(){e=Me("h2"),t=Me("img"),s=$t(),o=Me("div"),E.c(),i=et(` documentation
		`),a=Me("div"),r=et(l[0]),c=$t(),_=Me("span"),b&&b.c(),u=$t(),f=Me("p"),d=Me("span"),g=et(l[1]),m=$t(),N.c(),k=cs(),q&&q.c(),p=Me("br"),w=$t(),$=Me("button"),Dl(h.$$.fragment),ms(t.src,n=Bl)||Ee(t,"src",n),Ee(t,"alt",""),Ee(t,"class","svelte-1i1gjw2"),Ee(a,"class","url svelte-1i1gjw2"),Ee(o,"class","title svelte-1i1gjw2"),Ee(d,"class","url svelte-1i1gjw2"),Ee(_,"class","counts svelte-1i1gjw2"),Ee(e,"class","svelte-1i1gjw2"),Ee($,"class","svelte-1i1gjw2")},m(F,U){De(F,e,U),Ce(e,t),Ce(e,s),Ce(e,o),E.m(o,null),Ce(o,i),Ce(o,a),Ce(a,r),Ce(e,c),Ce(e,_),b&&b.m(_,null),Ce(_,u),Ce(_,f),Ce(f,d),Ce(d,g),Ce(f,m),N.m(f,null),Ce(f,k),q&&q.m(f,null),Ce(f,p),De(F,w,U),De(F,$,U),Ll(h,$,null),v=!0,y||(C=fs($,"click",l[5]),y=!0)},p(F,[U]){T!==(T=A(F))&&(E.d(1),E=T(F),E&&(E.c(),E.m(o,i))),(!v||U&1)&&wn(r,F[0]),F[2]!=="mcp"?b?(b.p(F,U),U&4&&zt(b,1)):(b=Cn(F),b.c(),zt(b,1),b.m(_,u)):b&&(_s(),It(b,1,1,()=>{b=null}),as()),(!v||U&2)&&wn(g,F[1]),z!==(z=S(F))&&(N.d(1),N=z(F),N&&(N.c(),N.m(f,k))),F[1]>1?q||(q=Sn(),q.c(),q.m(f,p)):q&&(q.d(1),q=null)},i(F){v||(zt(b),zt(h.$$.fragment,F),v=!0)},o(F){It(b),It(h.$$.fragment,F),v=!1},d(F){F&&(Ie(e),Ie(w),Ie($)),E.d(),b&&b.d(),N.d(),q&&q.d(),Rl(h),y=!1,C()}}}function ws(l,e,t){let{root:n}=e,{api_count:s}=e,{current_language:o="python"}=e;const i=hs(),a=()=>i("close",{api_recorder_visible:!0}),r=()=>i("close");return l.$$set=c=>{"root"in c&&t(0,n=c.root),"api_count"in c&&t(1,s=c.api_count),"current_language"in c&&t(2,o=c.current_language)},[n,s,o,i,a,r]}class Cs extends rs{constructor(e){super(),us(this,e,ws,ys,ds,{root:0,api_count:1,current_language:2})}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),en()}get api_count(){return this.$$.ctx[1]}set api_count(e){this.$$set({api_count:e}),en()}get current_language(){return this.$$.ctx[2]}set current_language(e){this.$$set({current_language:e}),en()}}function Ae(l,e,t=null){return e===void 0?t==="py"?"None":null:l===null&&t==="py"?"None":e==="string"||e==="str"?t===null?l:'"'+l+'"':e==="number"?t===null?parseFloat(l):l:e==="boolean"||e=="bool"?t==="py"?(l=String(l),l==="true"?"True":"False"):t==="js"||t==="bash"?l:l==="true":e==="List[str]"?(l=JSON.stringify(l),l):e.startsWith("Literal['")?'"'+l+'"':t===null?l===""?null:JSON.parse(l):typeof l=="string"?l===""?t==="py"?"None":"null":l:(t==="bash"&&(l=an(l)),t==="py"&&(l=cn(l)),Ss(l))}function Ul(l){if(typeof l=="object"&&l!==null&&l.hasOwnProperty("url")&&l.hasOwnProperty("meta")&&typeof l.meta=="object"&&l.meta!==null&&l.meta._type==="gradio.FileData")return!0;if(typeof l=="object"&&l!==null){for(let e in l)if(typeof l[e]=="object"&&Ul(l[e]))return!0}return!1}function an(l){return typeof l=="object"&&l!==null&&!Array.isArray(l)&&"url"in l&&l.url&&"meta"in l&&l.meta?._type==="gradio.FileData"?{path:l.url,meta:{_type:"gradio.FileData"}}:(Array.isArray(l)?l.forEach((e,t)=>{typeof e=="object"&&e!==null&&(l[t]=an(e))}):typeof l=="object"&&l!==null&&Object.keys(l).forEach(e=>{l[e]=an(l[e])}),l)}function cn(l){return typeof l=="object"&&l!==null&&!Array.isArray(l)&&"url"in l&&l.url&&"meta"in l&&l.meta?._type==="gradio.FileData"?`handle_file('${l.url}')`:(Array.isArray(l)?l.forEach((e,t)=>{typeof e=="object"&&e!==null&&(l[t]=cn(e))}):typeof l=="object"&&l!==null&&Object.keys(l).forEach(e=>{l[e]=cn(l[e])}),l)}function Ss(l){let e=JSON.stringify(l,(s,o)=>o===null?"UNQUOTEDNone":typeof o=="string"&&o.startsWith("handle_file(")&&o.endsWith(")")?`UNQUOTED${o}`:o);const t=/"UNQUOTEDhandle_file\(([^)]*)\)"/g;e=e.replace(t,(s,o)=>`handle_file(${o})`);const n=/"UNQUOTEDNone"/g;return e.replace(n,"None")}const{SvelteComponent:js,append:_e,attr:Xe,check_outros:Ts,create_component:qs,destroy_component:zs,destroy_each:Ns,detach:$e,element:qe,empty:Fl,ensure_array_like:jn,flush:Ot,group_outros:Ps,init:As,insert:be,mount_component:Os,noop:Es,safe_not_equal:Ms,set_data:Ke,set_style:rt,space:at,text:ye,toggle_class:Tn,transition_in:Dt,transition_out:_n}=window.__gradio__svelte__internal;function qn(l,e,t){const n=l.slice();return n[4]=e[t].label,n[5]=e[t].python_type,n[6]=e[t].component,n[7]=e[t].parameter_name,n[8]=e[t].parameter_has_default,n[9]=e[t].parameter_default,n[11]=t,n}function zn(l){let e;return{c(){e=ye("s")},m(t,n){be(t,e,n)},d(t){t&&$e(e)}}}function Is(l){let e=(l[2][l[11]].type||"any")+"",t;return{c(){t=ye(e)},m(n,s){be(n,t,s)},p(n,s){s&4&&e!==(e=(n[2][n[11]].type||"any")+"")&&Ke(t,e)},d(n){n&&$e(t)}}}function Ds(l){let e=l[5].type+"",t,n,s=l[8]&&l[9]===null&&Nn();return{c(){t=ye(e),s&&s.c(),n=Fl()},m(o,i){be(o,t,i),s&&s.m(o,i),be(o,n,i)},p(o,i){i&2&&e!==(e=o[5].type+"")&&Ke(t,e),o[8]&&o[9]===null?s||(s=Nn(),s.c(),s.m(n.parentNode,n)):s&&(s.d(1),s=null)},d(o){o&&($e(t),$e(n)),s&&s.d(o)}}}function Nn(l){let e;return{c(){e=ye(` |
							None`)},m(t,n){be(t,e,n)},d(t){t&&$e(e)}}}function Rs(l){let e,t,n=Ae(l[9],l[5].type,"py")+"",s;return{c(){e=qe("span"),e.textContent="Default: ",t=qe("span"),s=ye(n),Xe(t,"class","code svelte-1yt946s"),rt(t,"font-size","var(--text-sm)")},m(o,i){be(o,e,i),be(o,t,i),_e(t,s)},p(o,i){i&2&&n!==(n=Ae(o[9],o[5].type,"py")+"")&&Ke(s,n)},d(o){o&&($e(e),$e(t))}}}function Ls(l){let e;return{c(){e=qe("span"),e.textContent="Required",rt(e,"font-weight","bold")},m(t,n){be(t,e,n)},p:Es,d(t){t&&$e(e)}}}function Pn(l){let e,t,n,s,o,i=(l[3]!=="bash"&&l[7]?l[7]:"["+l[11]+"]")+"",a,r,c,_,u,f,d,g=l[4]+"",m,k,p=l[6]+"",w,$,h=l[5].description+"",v,y;function C(z,N){return z[3]==="python"?Ds:Is}let A=C(l),T=A(l);function E(z,N){return!z[8]||z[3]=="bash"?Ls:Rs}let b=E(l),S=b(l);return{c(){e=qe("hr"),t=at(),n=qe("div"),s=qe("p"),o=qe("span"),a=ye(i),r=at(),c=qe("span"),T.c(),_=at(),S.c(),u=at(),f=qe("p"),d=ye('The input value that is provided in the "'),m=ye(g),k=ye('" '),w=ye(p),$=ye(`
				component. `),v=ye(h),y=at(),Xe(e,"class","hr svelte-1yt946s"),Xe(o,"class","code svelte-1yt946s"),rt(o,"margin-right","10px"),Xe(c,"class","code highlight svelte-1yt946s"),rt(c,"margin-right","10px"),rt(s,"white-space","nowrap"),rt(s,"overflow-x","auto"),Xe(f,"class","desc svelte-1yt946s"),rt(n,"margin","10px")},m(z,N){be(z,e,N),be(z,t,N),be(z,n,N),_e(n,s),_e(s,o),_e(o,a),_e(s,r),_e(s,c),T.m(c,null),_e(s,_),S.m(s,null),_e(n,u),_e(n,f),_e(f,d),_e(f,m),_e(f,k),_e(f,w),_e(f,$),_e(f,v),_e(n,y)},p(z,N){N&10&&i!==(i=(z[3]!=="bash"&&z[7]?z[7]:"["+z[11]+"]")+"")&&Ke(a,i),A===(A=C(z))&&T?T.p(z,N):(T.d(1),T=A(z),T&&(T.c(),T.m(c,null))),b===(b=E(z))&&S?S.p(z,N):(S.d(1),S=b(z),S&&(S.c(),S.m(s,null))),N&2&&g!==(g=z[4]+"")&&Ke(m,g),N&2&&p!==(p=z[6]+"")&&Ke(w,p),N&2&&h!==(h=z[5].description+"")&&Ke(v,h)},d(z){z&&($e(e),$e(t),$e(n)),T.d(),S.d()}}}function An(l){let e,t,n;return t=new Il({props:{margin:!1}}),{c(){e=qe("div"),qs(t.$$.fragment),Xe(e,"class","load-wrap")},m(s,o){be(s,e,o),Os(t,e,null),n=!0},i(s){n||(Dt(t.$$.fragment,s),n=!0)},o(s){_n(t.$$.fragment,s),n=!1},d(s){s&&$e(e),zs(t)}}}function Us(l){let e,t,n,s=l[1].length+"",o,i,a,r,c,_,u,f,d=l[1].length!=1&&zn(),g=jn(l[1]),m=[];for(let p=0;p<g.length;p+=1)m[p]=Pn(qn(l,g,p));let k=l[0]&&An();return{c(){e=qe("h4"),t=qe("div"),t.innerHTML='<div class="toggle-dot svelte-1yt946s"></div>',n=ye(`
	Accepts `),o=ye(s),i=ye(" parameter"),d&&d.c(),a=ye(":"),r=at(),c=qe("div");for(let p=0;p<m.length;p+=1)m[p].c();_=at(),k&&k.c(),u=Fl(),Xe(t,"class","toggle-icon svelte-1yt946s"),Xe(e,"class","svelte-1yt946s"),Tn(c,"hide",l[0])},m(p,w){be(p,e,w),_e(e,t),_e(e,n),_e(e,o),_e(e,i),d&&d.m(e,null),_e(e,a),be(p,r,w),be(p,c,w);for(let $=0;$<m.length;$+=1)m[$]&&m[$].m(c,null);be(p,_,w),k&&k.m(p,w),be(p,u,w),f=!0},p(p,[w]){if((!f||w&2)&&s!==(s=p[1].length+"")&&Ke(o,s),p[1].length!=1?d||(d=zn(),d.c(),d.m(e,a)):d&&(d.d(1),d=null),w&14){g=jn(p[1]);let $;for($=0;$<g.length;$+=1){const h=qn(p,g,$);m[$]?m[$].p(h,w):(m[$]=Pn(h),m[$].c(),m[$].m(c,null))}for(;$<m.length;$+=1)m[$].d(1);m.length=g.length}(!f||w&1)&&Tn(c,"hide",p[0]),p[0]?k?w&1&&Dt(k,1):(k=An(),k.c(),Dt(k,1),k.m(u.parentNode,u)):k&&(Ps(),_n(k,1,1,()=>{k=null}),Ts())},i(p){f||(Dt(k),f=!0)},o(p){_n(k),f=!1},d(p){p&&($e(e),$e(r),$e(c),$e(_),$e(u)),d&&d.d(),Ns(m,p),k&&k.d(p)}}}function Fs(l,e,t){let{is_running:n}=e,{endpoint_returns:s}=e,{js_returns:o}=e,{current_language:i}=e;return l.$$set=a=>{"is_running"in a&&t(0,n=a.is_running),"endpoint_returns"in a&&t(1,s=a.endpoint_returns),"js_returns"in a&&t(2,o=a.js_returns),"current_language"in a&&t(3,i=a.current_language)},[n,s,o,i]}class Hs extends js{constructor(e){super(),As(this,e,Fs,Us,Ms,{is_running:0,endpoint_returns:1,js_returns:2,current_language:3})}get is_running(){return this.$$.ctx[0]}set is_running(e){this.$$set({is_running:e}),Ot()}get endpoint_returns(){return this.$$.ctx[1]}set endpoint_returns(e){this.$$set({endpoint_returns:e}),Ot()}get js_returns(){return this.$$.ctx[2]}set js_returns(e){this.$$set({js_returns:e}),Ot()}get current_language(){return this.$$.ctx[3]}set current_language(e){this.$$set({current_language:e}),Ot()}}const{SvelteComponent:Js,create_component:Vs,destroy_component:Bs,detach:Gs,flush:Ys,init:Ws,insert:Zs,mount_component:Qs,safe_not_equal:Xs,set_data:Ks,text:xs,transition_in:ei,transition_out:ti}=window.__gradio__svelte__internal;function ni(l){let e;return{c(){e=xs(l[0])},m(t,n){Zs(t,e,n)},p(t,n){n&1&&Ks(e,t[0])},d(t){t&&Gs(e)}}}function li(l){let e,t;return e=new gn({props:{size:"sm",$$slots:{default:[ni]},$$scope:{ctx:l}}}),e.$on("click",l[1]),{c(){Vs(e.$$.fragment)},m(n,s){Qs(e,n,s),t=!0},p(n,[s]){const o={};s&9&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(ei(e.$$.fragment,n),t=!0)},o(n){ti(e.$$.fragment,n),t=!1},d(n){Bs(e,n)}}}function si(l,e,t){let{code:n}=e,s="copy";function o(){navigator.clipboard.writeText(n),t(0,s="copied!"),setTimeout(()=>{t(0,s="copy")},1500)}return l.$$set=i=>{"code"in i&&t(2,n=i.code)},[s,o,n]}class Pe extends Js{constructor(e){super(),Ws(this,e,si,li,Xs,{code:2})}get code(){return this.$$.ctx[2]}set code(e){this.$$set({code:e}),Ys()}}const{SvelteComponent:ii,append:$n,attr:_t,check_outros:oi,create_component:Jt,destroy_component:Vt,detach:Ue,element:Fe,flush:ri,group_outros:ai,init:ci,insert:He,mount_component:Bt,noop:bn,safe_not_equal:_i,space:vn,transition_in:yt,transition_out:wt}=window.__gradio__svelte__internal;function ui(l){let e,t,n,s,o,i;return t=new Pe({props:{code:Mn}}),{c(){e=Fe("div"),Jt(t.$$.fragment),n=vn(),s=Fe("div"),o=Fe("pre"),o.textContent=`$ ${Mn}`,_t(e,"class","copy svelte-hq8ezf"),_t(o,"class","svelte-hq8ezf")},m(a,r){He(a,e,r),Bt(t,e,null),He(a,n,r),He(a,s,r),$n(s,o),i=!0},p:bn,i(a){i||(yt(t.$$.fragment,a),i=!0)},o(a){wt(t.$$.fragment,a),i=!1},d(a){a&&(Ue(e),Ue(n),Ue(s)),Vt(t)}}}function fi(l){let e,t,n,s,o,i;return t=new Pe({props:{code:En}}),{c(){e=Fe("div"),Jt(t.$$.fragment),n=vn(),s=Fe("div"),o=Fe("pre"),o.textContent=`$ ${En}`,_t(e,"class","copy svelte-hq8ezf"),_t(o,"class","svelte-hq8ezf")},m(a,r){He(a,e,r),Bt(t,e,null),He(a,n,r),He(a,s,r),$n(s,o),i=!0},p:bn,i(a){i||(yt(t.$$.fragment,a),i=!0)},o(a){wt(t.$$.fragment,a),i=!1},d(a){a&&(Ue(e),Ue(n),Ue(s)),Vt(t)}}}function pi(l){let e,t,n,s,o,i;return t=new Pe({props:{code:On}}),{c(){e=Fe("div"),Jt(t.$$.fragment),n=vn(),s=Fe("div"),o=Fe("pre"),o.textContent=`$ ${On}`,_t(e,"class","copy svelte-hq8ezf"),_t(o,"class","svelte-hq8ezf")},m(a,r){He(a,e,r),Bt(t,e,null),He(a,n,r),He(a,s,r),$n(s,o),i=!0},p:bn,i(a){i||(yt(t.$$.fragment,a),i=!0)},o(a){wt(t.$$.fragment,a),i=!1},d(a){a&&(Ue(e),Ue(n),Ue(s)),Vt(t)}}}function di(l){let e,t,n,s;const o=[pi,fi,ui],i=[];function a(r,c){return r[0]==="python"?0:r[0]==="javascript"?1:r[0]==="bash"?2:-1}return~(t=a(l))&&(n=i[t]=o[t](l)),{c(){e=Fe("code"),n&&n.c(),_t(e,"class","svelte-hq8ezf")},m(r,c){He(r,e,c),~t&&i[t].m(e,null),s=!0},p(r,c){let _=t;t=a(r),t===_?~t&&i[t].p(r,c):(n&&(ai(),wt(i[_],1,1,()=>{i[_]=null}),oi()),~t?(n=i[t],n?n.p(r,c):(n=i[t]=o[t](r),n.c()),yt(n,1),n.m(e,null)):n=null)},i(r){s||(yt(n),s=!0)},o(r){wt(n),s=!1},d(r){r&&Ue(e),~t&&i[t].d()}}}function mi(l){let e,t;return e=new We({props:{$$slots:{default:[di]},$$scope:{ctx:l}}}),{c(){Jt(e.$$.fragment)},m(n,s){Bt(e,n,s),t=!0},p(n,[s]){const o={};s&3&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(yt(e.$$.fragment,n),t=!0)},o(n){wt(e.$$.fragment,n),t=!1},d(n){Vt(e,n)}}}let On="pip install gradio_client",En="npm i -D @gradio/client",Mn="curl --version";function hi(l,e,t){let{current_language:n}=e;return l.$$set=s=>{"current_language"in s&&t(0,n=s.current_language)},[n]}class gi extends ii{constructor(e){super(),ci(this,e,hi,mi,_i,{current_language:0})}get current_language(){return this.$$.ctx[0]}set current_language(e){this.$$set({current_language:e}),ri()}}const{SvelteComponent:$i,append:gt,attr:tn,detach:bi,element:nn,flush:In,init:vi,insert:ki,noop:Dn,safe_not_equal:yi,set_data:Rn,space:wi,text:ln}=window.__gradio__svelte__internal;function Ci(l){let e,t,n,s="/"+l[0],o,i,a,r;return{c(){e=nn("h3"),t=ln(`API name:
	`),n=nn("span"),o=ln(s),i=wi(),a=nn("span"),r=ln(l[1]),tn(n,"class","post svelte-1y4an3z"),tn(a,"class","desc svelte-1y4an3z"),tn(e,"class","svelte-1y4an3z")},m(c,_){ki(c,e,_),gt(e,t),gt(e,n),gt(n,o),gt(e,i),gt(e,a),gt(a,r)},p(c,[_]){_&1&&s!==(s="/"+c[0])&&Rn(o,s),_&2&&Rn(r,c[1])},i:Dn,o:Dn,d(c){c&&bi(e)}}}function Si(l,e,t){let{api_name:n=null}=e,{description:s=null}=e;return l.$$set=o=>{"api_name"in o&&t(0,n=o.api_name),"description"in o&&t(1,s=o.description)},[n,s]}class ji extends $i{constructor(e){super(),vi(this,e,Si,Ci,yi,{api_name:0,description:1})}get api_name(){return this.$$.ctx[0]}set api_name(e){this.$$set({api_name:e}),In()}get description(){return this.$$.ctx[1]}set description(e){this.$$set({description:e}),In()}}const{SvelteComponent:Ti,append:M,attr:ue,binding_callbacks:sn,check_outros:qi,create_component:pt,destroy_component:dt,destroy_each:Lt,detach:Q,element:re,empty:Hl,ensure_array_like:xe,flush:Qe,group_outros:zi,init:Ni,insert:X,mount_component:mt,noop:Pi,safe_not_equal:Ai,set_data:he,space:Gt,text:O,transition_in:Je,transition_out:Ve}=window.__gradio__svelte__internal;function Ln(l,e,t){const n=l.slice();return n[27]=e[t].label,n[22]=e[t].parameter_name,n[28]=e[t].type,n[20]=e[t].python_type,n[29]=e[t].component,n[21]=e[t].example_input,n[30]=e[t].serializer,n[26]=t,n}function Un(l,e,t){const n=l.slice();return n[27]=e[t].label,n[22]=e[t].parameter_name,n[28]=e[t].type,n[20]=e[t].python_type,n[29]=e[t].component,n[21]=e[t].example_input,n[30]=e[t].serializer,n[26]=t,n}function Fn(l,e,t){const n=l.slice();return n[29]=e[t].component,n[21]=e[t].example_input,n[26]=t,n}function Hn(l,e,t){const n=l.slice();return n[20]=e[t].python_type,n[21]=e[t].example_input,n[22]=e[t].parameter_name,n[23]=e[t].parameter_has_default,n[24]=e[t].parameter_default,n[26]=t,n}function Oi(l){let e,t;return e=new We({props:{$$slots:{default:[Ii]},$$scope:{ctx:l}}}),{c(){pt(e.$$.fragment)},m(n,s){mt(e,n,s),t=!0},p(n,s){const o={};s[0]&3593|s[1]&8&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(Je(e.$$.fragment,n),t=!0)},o(n){Ve(e.$$.fragment,n),t=!1},d(n){dt(e,n)}}}function Ei(l){let e,t;return e=new We({props:{$$slots:{default:[Li]},$$scope:{ctx:l}}}),{c(){pt(e.$$.fragment)},m(n,s){mt(e,n,s),t=!0},p(n,s){const o={};s[0]&287|s[1]&8&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(Je(e.$$.fragment,n),t=!0)},o(n){Ve(e.$$.fragment,n),t=!1},d(n){dt(e,n)}}}function Mi(l){let e,t;return e=new We({props:{$$slots:{default:[Fi]},$$scope:{ctx:l}}}),{c(){pt(e.$$.fragment)},m(n,s){mt(e,n,s),t=!0},p(n,s){const o={};s[0]&159|s[1]&8&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(Je(e.$$.fragment,n),t=!0)},o(n){Ve(e.$$.fragment,n),t=!1},d(n){dt(e,n)}}}function Jn(l){let e;return{c(){e=O(",")},m(t,n){X(t,e,n)},d(t){t&&Q(e)}}}function Vn(l){let e,t=Ae(l[21],l[20].type,"bash")+"",n,s,o=l[26]<l[3].length-1&&Jn();return{c(){e=O(`
							`),n=O(t),o&&o.c(),s=Hl()},m(i,a){X(i,e,a),X(i,n,a),o&&o.m(i,a),X(i,s,a)},p(i,a){a[0]&8&&t!==(t=Ae(i[21],i[20].type,"bash")+"")&&he(n,t),i[26]<i[3].length-1?o||(o=Jn(),o.c(),o.m(s.parentNode,s)):o&&(o.d(1),o=null)},d(i){i&&(Q(e),Q(n),Q(s)),o&&o.d(i)}}}function Ii(l){let e,t,n,s,o,i,a,r,c,_,u=l[0].api_name+"",f,d,g="{",m,k,p,w="}",$,h,v="{",y,C,A="}",T,E,b,S,z,N=l[0].api_name+"",q,F,U;n=new Pe({props:{code:l[9]?.innerText}});let Z=xe(l[3]),K=[];for(let W=0;W<Z.length;W+=1)K[W]=Vn(Ln(l,Z,W));return{c(){e=re("code"),t=re("div"),pt(n.$$.fragment),s=Gt(),o=re("div"),i=re("pre"),a=O("curl -X POST "),r=O(l[10]),c=O(l[11]),_=O("/call/"),f=O(u),d=O(` -s -H "Content-Type: application/json" -d '`),m=O(g),k=O(`
  "data": [`);for(let W=0;W<K.length;W+=1)K[W].c();p=O(`
]`),$=O(w),h=O(`' \\
  | awk -F'"' '`),y=O(v),C=O(" print $4"),T=O(A),E=O(`'  \\
  | read EVENT_ID; curl -N `),b=O(l[10]),S=O(l[11]),z=O("/call/"),q=O(N),F=O("/$EVENT_ID"),ue(t,"class","copy svelte-114qcyq"),ue(i,"class","svelte-114qcyq"),ue(e,"class","svelte-114qcyq")},m(W,Y){X(W,e,Y),M(e,t),mt(n,t,null),M(e,s),M(e,o),M(o,i),M(i,a),M(i,r),M(i,c),M(i,_),M(i,f),M(i,d),M(i,m),M(i,k);for(let P=0;P<K.length;P+=1)K[P]&&K[P].m(i,null);M(i,p),M(i,$),M(i,h),M(i,y),M(i,C),M(i,T),M(i,E),M(i,b),M(i,S),M(i,z),M(i,q),M(i,F),l[18](o),U=!0},p(W,Y){const P={};if(Y[0]&512&&(P.code=W[9]?.innerText),n.$set(P),(!U||Y[0]&1024)&&he(r,W[10]),(!U||Y[0]&2048)&&he(c,W[11]),(!U||Y[0]&1)&&u!==(u=W[0].api_name+"")&&he(f,u),Y[0]&8){Z=xe(W[3]);let j;for(j=0;j<Z.length;j+=1){const me=Ln(W,Z,j);K[j]?K[j].p(me,Y):(K[j]=Vn(me),K[j].c(),K[j].m(i,p))}for(;j<K.length;j+=1)K[j].d(1);K.length=Z.length}(!U||Y[0]&1024)&&he(b,W[10]),(!U||Y[0]&2048)&&he(S,W[11]),(!U||Y[0]&1)&&N!==(N=W[0].api_name+"")&&he(q,N)},i(W){U||(Je(n.$$.fragment,W),U=!0)},o(W){Ve(n.$$.fragment,W),U=!1},d(W){W&&Q(e),dt(n),Lt(K,W),l[18](null)}}}function Bn(l){let e,t,n,s=l[21].url+"",o,i,a=l[29]+"",r,c,_,u;return{c(){e=O(`
const response_`),t=O(l[26]),n=O(' = await fetch("'),o=O(s),i=O(`");
const example`),r=O(a),c=O(" = await response_"),_=O(l[26]),u=O(`.blob();
						`)},m(f,d){X(f,e,d),X(f,t,d),X(f,n,d),X(f,o,d),X(f,i,d),X(f,r,d),X(f,c,d),X(f,_,d),X(f,u,d)},p:Pi,d(f){f&&(Q(e),Q(t),Q(n),Q(o),Q(i),Q(r),Q(c),Q(_),Q(u))}}}function Gn(l){let e,t,n;return{c(){e=O(', {auth: ["'),t=O(l[4]),n=O('", **password**]}')},m(s,o){X(s,e,o),X(s,t,o),X(s,n,o)},p(s,o){o[0]&16&&he(t,s[4])},d(s){s&&(Q(e),Q(t),Q(n))}}}function Di(l){let e,t,n=l[22]+"",s,o,i=Ae(l[21],l[20].type,"js")+"",a,r;return{c(){e=O(`		
		`),t=re("span"),s=O(n),o=O(": "),a=O(i),r=O(", "),ue(t,"class","example-inputs")},m(c,_){X(c,e,_),X(c,t,_),M(t,s),M(t,o),M(t,a),X(c,r,_)},p(c,_){_[0]&8&&n!==(n=c[22]+"")&&he(s,n),_[0]&8&&i!==(i=Ae(c[21],c[20].type,"js")+"")&&he(a,i)},d(c){c&&(Q(e),Q(t),Q(r))}}}function Ri(l){let e,t,n=l[22]+"",s,o,i=l[29]+"",a,r,c;return{c(){e=O(`
				`),t=re("span"),s=O(n),o=O(": example"),a=O(i),r=O(", "),c=re("span"),c.innerHTML="",ue(t,"class","example-inputs"),ue(c,"class","desc svelte-114qcyq")},m(_,u){X(_,e,u),X(_,t,u),M(t,s),M(t,o),M(t,a),X(_,r,u),X(_,c,u)},p(_,u){u[0]&8&&n!==(n=_[22]+"")&&he(s,n),u[0]&8&&i!==(i=_[29]+"")&&he(a,i)},d(_){_&&(Q(e),Q(t),Q(r),Q(c))}}}function Yn(l){let e,t;function n(i,a){return a[0]&8&&(e=null),e==null&&(e=!!i[13].includes(i[29])),e?Ri:Di}let s=n(l,[-1,-1]),o=s(l);return{c(){o.c(),t=Hl()},m(i,a){o.m(i,a),X(i,t,a)},p(i,a){s===(s=n(i,a))&&o?o.p(i,a):(o.d(1),o=s(i),o&&(o.c(),o.m(t.parentNode,t)))},d(i){i&&Q(t),o.d(i)}}}function Li(l){let e,t,n,s,o,i,a,r,c,_,u=(l[2]||l[1])+"",f,d,g,m,k,p=l[0].api_name+"",w,$,h,v,y;n=new Pe({props:{code:l[8]?.innerText}});let C=xe(l[14]),A=[];for(let S=0;S<C.length;S+=1)A[S]=Bn(Fn(l,C,S));let T=l[4]!==null&&Gn(l),E=xe(l[3]),b=[];for(let S=0;S<E.length;S+=1)b[S]=Yn(Un(l,E,S));return{c(){e=re("code"),t=re("div"),pt(n.$$.fragment),s=Gt(),o=re("div"),i=re("pre"),a=O(`import { Client } from "@gradio/client";
`);for(let S=0;S<A.length;S+=1)A[S].c();r=O(`
const client = await Client.connect(`),c=re("span"),_=O('"'),f=O(u),d=O('"'),T&&T.c(),g=O(`);
const result = await client.predict(`),m=re("span"),k=O('"/'),w=O(p),$=O('"'),h=O(", { ");for(let S=0;S<b.length;S+=1)b[S].c();v=O(`
});

console.log(result.data);
`),ue(t,"class","copy svelte-114qcyq"),ue(c,"class","token string svelte-114qcyq"),ue(m,"class","api-name svelte-114qcyq"),ue(i,"class","svelte-114qcyq"),ue(e,"class","svelte-114qcyq")},m(S,z){X(S,e,z),M(e,t),mt(n,t,null),M(e,s),M(e,o),M(o,i),M(i,a);for(let N=0;N<A.length;N+=1)A[N]&&A[N].m(i,null);M(i,r),M(i,c),M(c,_),M(c,f),M(c,d),T&&T.m(i,null),M(i,g),M(i,m),M(m,k),M(m,w),M(m,$),M(i,h);for(let N=0;N<b.length;N+=1)b[N]&&b[N].m(i,null);M(i,v),l[17](o),y=!0},p(S,z){const N={};if(z[0]&256&&(N.code=S[8]?.innerText),n.$set(N),z[0]&16384){C=xe(S[14]);let q;for(q=0;q<C.length;q+=1){const F=Fn(S,C,q);A[q]?A[q].p(F,z):(A[q]=Bn(F),A[q].c(),A[q].m(i,r))}for(;q<A.length;q+=1)A[q].d(1);A.length=C.length}if((!y||z[0]&6)&&u!==(u=(S[2]||S[1])+"")&&he(f,u),S[4]!==null?T?T.p(S,z):(T=Gn(S),T.c(),T.m(i,g)):T&&(T.d(1),T=null),(!y||z[0]&1)&&p!==(p=S[0].api_name+"")&&he(w,p),z[0]&8200){E=xe(S[3]);let q;for(q=0;q<E.length;q+=1){const F=Un(S,E,q);b[q]?b[q].p(F,z):(b[q]=Yn(F),b[q].c(),b[q].m(i,v))}for(;q<b.length;q+=1)b[q].d(1);b.length=E.length}},i(S){y||(Je(n.$$.fragment,S),y=!0)},o(S){Ve(n.$$.fragment,S),y=!1},d(S){S&&Q(e),dt(n),Lt(A,S),T&&T.d(),Lt(b,S),l[17](null)}}}function Ui(l){let e;return{c(){e=O(", handle_file")},m(t,n){X(t,e,n)},d(t){t&&Q(e)}}}function Wn(l){let e,t,n;return{c(){e=O(', auth=("'),t=O(l[4]),n=O('", **password**)')},m(s,o){X(s,e,o),X(s,t,o),X(s,n,o)},p(s,o){o[0]&16&&he(t,s[4])},d(s){s&&(Q(e),Q(t),Q(n))}}}function Zn(l){let e,t=l[22]?l[22]+"=":"",n,s,o=Ae(l[23]?l[24]:l[21],l[20].type,"py")+"",i,a;return{c(){e=O(`
		`),n=O(t),s=re("span"),i=O(o),a=O(",")},m(r,c){X(r,e,c),X(r,n,c),X(r,s,c),M(s,i),X(r,a,c)},p(r,c){c[0]&8&&t!==(t=r[22]?r[22]+"=":"")&&he(n,t),c[0]&8&&o!==(o=Ae(r[23]?r[24]:r[21],r[20].type,"py")+"")&&he(i,o)},d(r){r&&(Q(e),Q(n),Q(s),Q(a))}}}function Fi(l){let e,t,n,s,o,i,a,r,c,_,u,f,d,g=(l[2]||l[1])+"",m,k,p,w,$,h,v,y,C=l[0].api_name+"",A,T,E,b,S,z;n=new Pe({props:{code:l[7]?.innerText}});let N=l[12]&&Ui(),q=l[4]!==null&&Wn(l),F=xe(l[3]),U=[];for(let Z=0;Z<F.length;Z+=1)U[Z]=Zn(Hn(l,F,Z));return{c(){e=re("code"),t=re("div"),pt(n.$$.fragment),s=Gt(),o=re("div"),i=re("pre"),a=re("span"),a.textContent="from",r=O(" gradio_client "),c=re("span"),c.textContent="import",_=O(" Client"),N&&N.c(),u=O(`

client = Client(`),f=re("span"),d=O('"'),m=O(g),k=O('"'),q&&q.c(),p=O(`)
result = client.`),w=re("span"),w.textContent="predict",$=O("(");for(let Z=0;Z<U.length;Z+=1)U[Z].c();h=O(`
		api_name=`),v=re("span"),y=O('"/'),A=O(C),T=O('"'),E=O(`
)
`),b=re("span"),b.textContent="print",S=O("(result)"),ue(t,"class","copy svelte-114qcyq"),ue(a,"class","highlight"),ue(c,"class","highlight"),ue(f,"class","token string svelte-114qcyq"),ue(w,"class","highlight"),ue(v,"class","api-name svelte-114qcyq"),ue(b,"class","highlight"),ue(i,"class","svelte-114qcyq"),ue(e,"class","svelte-114qcyq")},m(Z,K){X(Z,e,K),M(e,t),mt(n,t,null),M(e,s),M(e,o),M(o,i),M(i,a),M(i,r),M(i,c),M(i,_),N&&N.m(i,null),M(i,u),M(i,f),M(f,d),M(f,m),M(f,k),q&&q.m(i,null),M(i,p),M(i,w),M(i,$);for(let W=0;W<U.length;W+=1)U[W]&&U[W].m(i,null);M(i,h),M(i,v),M(v,y),M(v,A),M(v,T),M(i,E),M(i,b),M(i,S),l[16](o),z=!0},p(Z,K){const W={};if(K[0]&128&&(W.code=Z[7]?.innerText),n.$set(W),(!z||K[0]&6)&&g!==(g=(Z[2]||Z[1])+"")&&he(m,g),Z[4]!==null?q?q.p(Z,K):(q=Wn(Z),q.c(),q.m(i,p)):q&&(q.d(1),q=null),K[0]&8){F=xe(Z[3]);let Y;for(Y=0;Y<F.length;Y+=1){const P=Hn(Z,F,Y);U[Y]?U[Y].p(P,K):(U[Y]=Zn(P),U[Y].c(),U[Y].m(i,h))}for(;Y<U.length;Y+=1)U[Y].d(1);U.length=F.length}(!z||K[0]&1)&&C!==(C=Z[0].api_name+"")&&he(A,C)},i(Z){z||(Je(n.$$.fragment,Z),z=!0)},o(Z){Ve(n.$$.fragment,Z),z=!1},d(Z){Z&&Q(e),dt(n),N&&N.d(),q&&q.d(),Lt(U,Z),l[16](null)}}}function Hi(l){let e,t,n,s,o,i;t=new ji({props:{api_name:l[0].api_name,description:l[6]}});const a=[Mi,Ei,Oi],r=[];function c(_,u){return _[5]==="python"?0:_[5]==="javascript"?1:_[5]==="bash"?2:-1}return~(s=c(l))&&(o=r[s]=a[s](l)),{c(){e=re("div"),pt(t.$$.fragment),n=Gt(),o&&o.c(),ue(e,"class","container svelte-114qcyq")},m(_,u){X(_,e,u),mt(t,e,null),M(e,n),~s&&r[s].m(e,null),i=!0},p(_,u){const f={};u[0]&1&&(f.api_name=_[0].api_name),u[0]&64&&(f.description=_[6]),t.$set(f);let d=s;s=c(_),s===d?~s&&r[s].p(_,u):(o&&(zi(),Ve(r[d],1,1,()=>{r[d]=null}),qi()),~s?(o=r[s],o?o.p(_,u):(o=r[s]=a[s](_),o.c()),Je(o,1),o.m(e,null)):o=null)},i(_){i||(Je(t.$$.fragment,_),Je(o),i=!0)},o(_){Ve(t.$$.fragment,_),Ve(o),i=!1},d(_){_&&Q(e),dt(t),~s&&r[s].d()}}}function Ji(l,e,t){let n,s,{dependency:o}=e,{root:i}=e,{api_prefix:a}=e,{space_id:r}=e,{endpoint_parameters:c}=e,{username:_}=e,{current_language:u}=e,{api_description:f=null}=e,d,g,m,k=c.some(y=>Ul(y.example_input)),p=["Audio","File","Image","Video"],w=c.filter(y=>p.includes(y.component));function $(y){sn[y?"unshift":"push"](()=>{d=y,t(7,d)})}function h(y){sn[y?"unshift":"push"](()=>{g=y,t(8,g)})}function v(y){sn[y?"unshift":"push"](()=>{m=y,t(9,m)})}return l.$$set=y=>{"dependency"in y&&t(0,o=y.dependency),"root"in y&&t(1,i=y.root),"api_prefix"in y&&t(15,a=y.api_prefix),"space_id"in y&&t(2,r=y.space_id),"endpoint_parameters"in y&&t(3,c=y.endpoint_parameters),"username"in y&&t(4,_=y.username),"current_language"in y&&t(5,u=y.current_language),"api_description"in y&&t(6,f=y.api_description)},l.$$.update=()=>{l.$$.dirty[0]&32768&&t(11,n=a||"/"),l.$$.dirty[0]&2&&t(10,s=i.replace(/\/$/,""))},[o,i,r,c,_,u,f,d,g,m,s,n,k,p,w,a,$,h,v]}class Vi extends Ti{constructor(e){super(),Ni(this,e,Ji,Hi,Ai,{dependency:0,root:1,api_prefix:15,space_id:2,endpoint_parameters:3,username:4,current_language:5,api_description:6},null,[-1,-1])}get dependency(){return this.$$.ctx[0]}set dependency(e){this.$$set({dependency:e}),Qe()}get root(){return this.$$.ctx[1]}set root(e){this.$$set({root:e}),Qe()}get api_prefix(){return this.$$.ctx[15]}set api_prefix(e){this.$$set({api_prefix:e}),Qe()}get space_id(){return this.$$.ctx[2]}set space_id(e){this.$$set({space_id:e}),Qe()}get endpoint_parameters(){return this.$$.ctx[3]}set endpoint_parameters(e){this.$$set({endpoint_parameters:e}),Qe()}get username(){return this.$$.ctx[4]}set username(e){this.$$set({username:e}),Qe()}get current_language(){return this.$$.ctx[5]}set current_language(e){this.$$set({current_language:e}),Qe()}get api_description(){return this.$$.ctx[6]}set api_description(e){this.$$set({api_description:e}),Qe()}}const{SvelteComponent:Bi,append:R,attr:ge,binding_callbacks:on,check_outros:Gi,create_component:Yt,destroy_component:Wt,destroy_each:kn,detach:pe,element:fe,empty:Yi,ensure_array_like:Ct,flush:st,group_outros:Wi,init:Zi,insert:de,mount_component:Zt,safe_not_equal:Qi,set_data:ze,space:Qt,text:V,transition_in:St,transition_out:jt}=window.__gradio__svelte__internal,{onMount:Xi,tick:Ki}=window.__gradio__svelte__internal;function Qn(l,e,t){const n=l.slice();return n[20]=e[t].call,n[21]=e[t].api_name,n}function Xn(l,e,t){const n=l.slice();return n[20]=e[t].call,n[21]=e[t].api_name,n}function Kn(l,e,t){const n=l.slice();return n[20]=e[t].call,n[21]=e[t].api_name,n}function xi(l){let e,t,n,s,o,i;n=new Pe({props:{code:l[6]?.innerText}});let a=Ct(l[9]),r=[];for(let c=0;c<a.length;c+=1)r[c]=xn(Qn(l,a,c));return{c(){e=fe("code"),t=fe("div"),Yt(n.$$.fragment),s=Qt(),o=fe("div");for(let c=0;c<r.length;c+=1)r[c].c();ge(t,"class","copy svelte-j71ub0"),ge(e,"class","svelte-j71ub0")},m(c,_){de(c,e,_),R(e,t),Zt(n,t,null),R(e,s),R(e,o);for(let u=0;u<r.length;u+=1)r[u]&&r[u].m(o,null);l[16](o),i=!0},p(c,_){const u={};if(_&64&&(u.code=c[6]?.innerText),n.$set(u),_&513){a=Ct(c[9]);let f;for(f=0;f<a.length;f+=1){const d=Qn(c,a,f);r[f]?r[f].p(d,_):(r[f]=xn(d),r[f].c(),r[f].m(o,null))}for(;f<r.length;f+=1)r[f].d(1);r.length=a.length}},i(c){i||(St(n.$$.fragment,c),i=!0)},o(c){jt(n.$$.fragment,c),i=!1},d(c){c&&pe(e),Wt(n),kn(r,c),l[16](null)}}}function eo(l){let e,t,n,s,o,i,a,r,c,_,u,f,d;n=new Pe({props:{code:l[5]?.innerText}});let g=l[2]!==null&&el(l),m=Ct(l[8]),k=[];for(let p=0;p<m.length;p+=1)k[p]=nl(Xn(l,m,p));return{c(){e=fe("code"),t=fe("div"),Yt(n.$$.fragment),s=Qt(),o=fe("div"),i=fe("pre"),a=V(`import { Client } from "@gradio/client";

const app = await Client.connect(`),r=fe("span"),c=V('"'),_=V(l[0]),u=V('"'),g&&g.c(),f=V(`);
					`);for(let p=0;p<k.length;p+=1)k[p].c();ge(t,"class","copy svelte-j71ub0"),ge(r,"class","token string svelte-j71ub0"),ge(i,"class","svelte-j71ub0"),ge(e,"class","svelte-j71ub0")},m(p,w){de(p,e,w),R(e,t),Zt(n,t,null),R(e,s),R(e,o),R(o,i),R(i,a),R(i,r),R(r,c),R(r,_),R(r,u),g&&g.m(i,null),R(i,f);for(let $=0;$<k.length;$+=1)k[$]&&k[$].m(i,null);l[15](o),d=!0},p(p,w){const $={};if(w&32&&($.code=p[5]?.innerText),n.$set($),(!d||w&1)&&ze(_,p[0]),p[2]!==null?g?g.p(p,w):(g=el(p),g.c(),g.m(i,f)):g&&(g.d(1),g=null),w&256){m=Ct(p[8]);let h;for(h=0;h<m.length;h+=1){const v=Xn(p,m,h);k[h]?k[h].p(v,w):(k[h]=nl(v),k[h].c(),k[h].m(i,null))}for(;h<k.length;h+=1)k[h].d(1);k.length=m.length}},i(p){d||(St(n.$$.fragment,p),d=!0)},o(p){jt(n.$$.fragment,p),d=!1},d(p){p&&pe(e),Wt(n),g&&g.d(),kn(k,p),l[15](null)}}}function to(l){let e,t,n,s,o,i,a,r,c,_,u,f,d,g,m,k;n=new Pe({props:{code:l[4]}});let p=l[2]!==null&&ll(l),w=Ct(l[7]),$=[];for(let h=0;h<w.length;h+=1)$[h]=sl(Kn(l,w,h));return{c(){e=fe("code"),t=fe("div"),Yt(n.$$.fragment),s=Qt(),o=fe("div"),i=fe("pre"),a=fe("span"),a.textContent="from",r=V(" gradio_client "),c=fe("span"),c.textContent="import",_=V(` Client, file

client = Client(`),u=fe("span"),f=V('"'),d=V(l[0]),g=V('"'),p&&p.c(),m=V(`)
`);for(let h=0;h<$.length;h+=1)$[h].c();ge(t,"class","copy svelte-j71ub0"),ge(a,"class","highlight"),ge(c,"class","highlight"),ge(u,"class","token string svelte-j71ub0"),ge(i,"class","svelte-j71ub0"),ge(e,"class","svelte-j71ub0")},m(h,v){de(h,e,v),R(e,t),Zt(n,t,null),R(e,s),R(e,o),R(o,i),R(i,a),R(i,r),R(i,c),R(i,_),R(i,u),R(u,f),R(u,d),R(u,g),p&&p.m(i,null),R(i,m);for(let y=0;y<$.length;y+=1)$[y]&&$[y].m(i,null);l[14](o),k=!0},p(h,v){const y={};if(v&16&&(y.code=h[4]),n.$set(y),(!k||v&1)&&ze(d,h[0]),h[2]!==null?p?p.p(h,v):(p=ll(h),p.c(),p.m(i,m)):p&&(p.d(1),p=null),v&128){w=Ct(h[7]);let C;for(C=0;C<w.length;C+=1){const A=Kn(h,w,C);$[C]?$[C].p(A,v):($[C]=sl(A),$[C].c(),$[C].m(i,null))}for(;C<$.length;C+=1)$[C].d(1);$.length=w.length}},i(h){k||(St(n.$$.fragment,h),k=!0)},o(h){jt(n.$$.fragment,h),k=!1},d(h){h&&pe(e),Wt(n),p&&p.d(),kn($,h),l[14](null)}}}function xn(l){let e,t,n,s,o=l[21]+"",i,a,r="{",c,_,u=l[20]+"",f,d,g="}",m,k,p="{",w,$,h="}",v,y,C,A,T=l[21]+"",E,b,S,z;return{c(){e=fe("pre"),t=V("curl -X POST "),n=V(l[0]),s=V("call/"),i=V(o),a=V(` -s -H "Content-Type: application/json" -d '`),c=V(r),_=V(` 
	"data": [`),f=V(u),d=V("]"),m=V(g),k=V(`' \\
  | awk -F'"' '`),w=V(p),$=V(" print $4"),v=V(h),y=V(`' \\
  | read EVENT_ID; curl -N `),C=V(l[0]),A=V("call/"),E=V(T),b=V("/$EVENT_ID"),S=Qt(),z=fe("br"),ge(e,"class","svelte-j71ub0")},m(N,q){de(N,e,q),R(e,t),R(e,n),R(e,s),R(e,i),R(e,a),R(e,c),R(e,_),R(e,f),R(e,d),R(e,m),R(e,k),R(e,w),R(e,$),R(e,v),R(e,y),R(e,C),R(e,A),R(e,E),R(e,b),de(N,S,q),de(N,z,q)},p(N,q){q&1&&ze(n,N[0]),q&512&&o!==(o=N[21]+"")&&ze(i,o),q&512&&u!==(u=N[20]+"")&&ze(f,u),q&1&&ze(C,N[0]),q&512&&T!==(T=N[21]+"")&&ze(E,T)},d(N){N&&(pe(e),pe(S),pe(z))}}}function el(l){let e,t,n;return{c(){e=V(', {auth: ["'),t=V(l[2]),n=V('", **password**]}')},m(s,o){de(s,e,o),de(s,t,o),de(s,n,o)},p(s,o){o&4&&ze(t,s[2])},d(s){s&&(pe(e),pe(t),pe(n))}}}function tl(l){let e,t=l[20]+"",n;return{c(){e=V(", "),n=V(t)},m(s,o){de(s,e,o),de(s,n,o)},p(s,o){o&256&&t!==(t=s[20]+"")&&ze(n,t)},d(s){s&&(pe(e),pe(n))}}}function nl(l){let e,t,n,s=l[21]+"",o,i,a,r=l[20]&&tl(l);return{c(){e=V(`
await client.predict(`),t=fe("span"),n=V(`
  "/`),o=V(s),i=V('"'),r&&r.c(),a=V(`);
						`),ge(t,"class","api-name svelte-j71ub0")},m(c,_){de(c,e,_),de(c,t,_),R(t,n),R(t,o),R(t,i),r&&r.m(c,_),de(c,a,_)},p(c,_){_&256&&s!==(s=c[21]+"")&&ze(o,s),c[20]?r?r.p(c,_):(r=tl(c),r.c(),r.m(a.parentNode,a)):r&&(r.d(1),r=null)},d(c){c&&(pe(e),pe(t),pe(a)),r&&r.d(c)}}}function ll(l){let e,t,n;return{c(){e=V(', auth=("'),t=V(l[2]),n=V('", **password**)')},m(s,o){de(s,e,o),de(s,t,o),de(s,n,o)},p(s,o){o&4&&ze(t,s[2])},d(s){s&&(pe(e),pe(t),pe(n))}}}function sl(l){let e,t,n,s=l[20]+"",o,i,a,r,c=l[21]+"",_,u,f;return{c(){e=V(`
client.`),t=fe("span"),n=V(`predict(
`),o=V(s),i=V("  api_name="),a=fe("span"),r=V('"/'),_=V(c),u=V('"'),f=V(`
)
`),ge(a,"class","api-name svelte-j71ub0"),ge(t,"class","highlight")},m(d,g){de(d,e,g),de(d,t,g),R(t,n),R(t,o),R(t,i),R(t,a),R(a,r),R(a,_),R(a,u),R(t,f)},p(d,g){g&128&&s!==(s=d[20]+"")&&ze(o,s),g&128&&c!==(c=d[21]+"")&&ze(_,c)},d(d){d&&(pe(e),pe(t))}}}function no(l){let e,t,n,s;const o=[to,eo,xi],i=[];function a(r,c){return r[1]==="python"?0:r[1]==="javascript"?1:r[1]==="bash"?2:-1}return~(e=a(l))&&(t=i[e]=o[e](l)),{c(){t&&t.c(),n=Yi()},m(r,c){~e&&i[e].m(r,c),de(r,n,c),s=!0},p(r,c){let _=e;e=a(r),e===_?~e&&i[e].p(r,c):(t&&(Wi(),jt(i[_],1,1,()=>{i[_]=null}),Gi()),~e?(t=i[e],t?t.p(r,c):(t=i[e]=o[e](r),t.c()),St(t,1),t.m(n.parentNode,n)):t=null)},i(r){s||(St(t),s=!0)},o(r){jt(t),s=!1},d(r){r&&pe(n),~e&&i[e].d(r)}}}function lo(l){let e,t,n;return t=new We({props:{border_mode:"focus",$$slots:{default:[no]},$$scope:{ctx:l}}}),{c(){e=fe("div"),Yt(t.$$.fragment),ge(e,"class","container svelte-j71ub0")},m(s,o){de(s,e,o),Zt(t,e,null),n=!0},p(s,[o]){const i={};o&268436479&&(i.$$scope={dirty:o,ctx:s}),t.$set(i)},i(s){n||(St(t.$$.fragment,s),n=!0)},o(s){jt(t.$$.fragment,s),n=!1},d(s){s&&pe(e),Wt(t)}}}function so(l,e,t){let{dependencies:n}=e,{short_root:s}=e,{root:o}=e,{api_prefix:i=""}=e,{current_language:a}=e,{username:r}=e,c,_,u,f,{api_calls:d=[]}=e;async function g(){return await(await fetch(o.replace(/\/$/,"")+i+"/info/?all_endpoints=true")).json()}let m,k=[],p=[],w=[];function $(C,A){const T=`/${n[C.fn_index].api_name}`,b=C.data.filter(S=>typeof S<"u").map((S,z)=>{if(m[T]){const N=m[T].parameters[z];if(!N)return;const q=N.parameter_name,F=N.python_type.type;if(A==="py")return`  ${q}=${Ae(S,F,"py")}`;if(A==="js")return`    ${q}: ${Ae(S,F,"js")}`;if(A==="bash")return`    ${Ae(S,F,"bash")}`}return`  ${Ae(S,void 0,A)}`}).filter(S=>typeof S<"u").join(`,
`);if(b){if(A==="py")return`${b},
`;if(A==="js")return`{
${b},
}`;if(A==="bash")return`
${b}
`}return A==="py"?"":`
`}Xi(async()=>{m=(await g()).named_endpoints;let A=d.map(S=>$(S,"py")),T=d.map(S=>$(S,"js")),E=d.map(S=>$(S,"bash")),b=d.map(S=>n[S.fn_index].api_name||"");t(7,k=A.map((S,z)=>({call:S,api_name:b[z]}))),t(8,p=T.map((S,z)=>({call:S,api_name:b[z]}))),t(9,w=E.map((S,z)=>({call:S,api_name:b[z]}))),await Ki(),t(4,_=c.innerText)});function h(C){on[C?"unshift":"push"](()=>{c=C,t(3,c)})}function v(C){on[C?"unshift":"push"](()=>{u=C,t(5,u)})}function y(C){on[C?"unshift":"push"](()=>{f=C,t(6,f)})}return l.$$set=C=>{"dependencies"in C&&t(10,n=C.dependencies),"short_root"in C&&t(0,s=C.short_root),"root"in C&&t(11,o=C.root),"api_prefix"in C&&t(12,i=C.api_prefix),"current_language"in C&&t(1,a=C.current_language),"username"in C&&t(2,r=C.username),"api_calls"in C&&t(13,d=C.api_calls)},[s,a,r,c,_,u,f,k,p,w,n,o,i,d,h,v,y]}class io extends Bi{constructor(e){super(),Zi(this,e,so,lo,Qi,{dependencies:10,short_root:0,root:11,api_prefix:12,current_language:1,username:2,api_calls:13})}get dependencies(){return this.$$.ctx[10]}set dependencies(e){this.$$set({dependencies:e}),st()}get short_root(){return this.$$.ctx[0]}set short_root(e){this.$$set({short_root:e}),st()}get root(){return this.$$.ctx[11]}set root(e){this.$$set({root:e}),st()}get api_prefix(){return this.$$.ctx[12]}set api_prefix(e){this.$$set({api_prefix:e}),st()}get current_language(){return this.$$.ctx[1]}set current_language(e){this.$$set({current_language:e}),st()}get username(){return this.$$.ctx[2]}set username(e){this.$$set({username:e}),st()}get api_calls(){return this.$$.ctx[13]}set api_calls(e){this.$$set({api_calls:e}),st()}}const oo="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3cpath%20d='M15.84.5a16.4,16.4,0,0,0-3.57.32C9.1,1.39,8.53,2.53,8.53,4.64V7.48H16v1H5.77a4.73,4.73,0,0,0-4.7,3.74,14.82,14.82,0,0,0,0,7.54c.57,2.28,1.86,3.82,4,3.82h2.6V20.14a4.73,4.73,0,0,1,4.63-4.63h7.38a3.72,3.72,0,0,0,3.73-3.73V4.64A4.16,4.16,0,0,0,19.65.82,20.49,20.49,0,0,0,15.84.5ZM11.78,2.77a1.39,1.39,0,0,1,1.38,1.46,1.37,1.37,0,0,1-1.38,1.38A1.42,1.42,0,0,1,10.4,4.23,1.44,1.44,0,0,1,11.78,2.77Z'%20fill='%235a9fd4'%20%3e%3c/path%3e%3cpath%20d='M16.16,31.5a16.4,16.4,0,0,0,3.57-.32c3.17-.57,3.74-1.71,3.74-3.82V24.52H16v-1H26.23a4.73,4.73,0,0,0,4.7-3.74,14.82,14.82,0,0,0,0-7.54c-.57-2.28-1.86-3.82-4-3.82h-2.6v3.41a4.73,4.73,0,0,1-4.63,4.63H12.35a3.72,3.72,0,0,0-3.73,3.73v7.14a4.16,4.16,0,0,0,3.73,3.82A20.49,20.49,0,0,0,16.16,31.5Zm4.06-2.27a1.39,1.39,0,0,1-1.38-1.46,1.37,1.37,0,0,1,1.38-1.38,1.42,1.42,0,0,1,1.38,1.38A1.44,1.44,0,0,1,20.22,29.23Z'%20fill='%23ffd43b'%20%3e%3c/path%3e%3c/svg%3e",ro="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20focusable='false'%20role='img'%20width='1em'%20height='1em'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%2032%2032'%20%3e%3crect%20width='32'%20height='32'%20fill='%23f7df1e'%3e%3c/rect%3e%3cpath%20d='M21.5,25a3.27,3.27,0,0,0,3,1.83c1.25,0,2-.63,2-1.49,0-1-.81-1.39-2.19-2L23.56,23C21.39,22.1,20,20.94,20,18.49c0-2.25,1.72-4,4.41-4a4.44,4.44,0,0,1,4.27,2.41l-2.34,1.5a2,2,0,0,0-1.93-1.29,1.31,1.31,0,0,0-1.44,1.29c0,.9.56,1.27,1.85,1.83l.75.32c2.55,1.1,4,2.21,4,4.72,0,2.71-2.12,4.19-5,4.19a5.78,5.78,0,0,1-5.48-3.07Zm-10.63.26c.48.84.91,1.55,1.94,1.55s1.61-.39,1.61-1.89V14.69h3V25c0,3.11-1.83,4.53-4.49,4.53a4.66,4.66,0,0,1-4.51-2.75Z'%20%3e%3c/path%3e%3c/svg%3e",ao="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20version='1.1'%20id='Layer_1'%20x='0px'%20y='0px'%20viewBox='0%200%20150%20150'%20style='enable-background:new%200%200%20150%20150;%20background-color:%20%2372a824;'%20xml:space='preserve'%3e%3cscript%20xmlns=''/%3e%3cstyle%20type='text/css'%3e%20.st0{fill:%23FFFFFF;}%20%3c/style%3e%3cg%3e%3cpath%20class='st0'%20d='M118.9,40.3L81.7,18.2c-2.2-1.3-4.7-2-7.2-2s-5,0.7-7.2,2L30.1,40.3c-4.4,2.6-7.2,7.5-7.2,12.8v44.2%20c0,5.3,2.7,10.1,7.2,12.8l37.2,22.1c2.2,1.3,4.7,2,7.2,2c2.5,0,5-0.7,7.2-2l37.2-22.1c4.4-2.6,7.2-7.5,7.2-12.8V53%20C126.1,47.8,123.4,42.9,118.9,40.3z%20M90.1,109.3l0.1,3.2c0,0.4-0.2,0.8-0.5,1l-1.9,1.1c-0.3,0.2-0.5,0-0.6-0.4l0-3.1%20c-1.6,0.7-3.2,0.8-4.3,0.4c-0.2-0.1-0.3-0.4-0.2-0.7l0.7-2.9c0.1-0.2,0.2-0.5,0.3-0.6c0.1-0.1,0.1-0.1,0.2-0.1%20c0.1-0.1,0.2-0.1,0.3,0c1.1,0.4,2.6,0.2,3.9-0.5c1.8-0.9,2.9-2.7,2.9-4.5c0-1.6-0.9-2.3-3-2.3c-2.7,0-5.2-0.5-5.3-4.5%20c0-3.3,1.7-6.7,4.4-8.8l0-3.2c0-0.4,0.2-0.8,0.5-1l1.8-1.2c0.3-0.2,0.5,0,0.6,0.4l0,3.2c1.3-0.5,2.5-0.7,3.6-0.4%20c0.2,0.1,0.3,0.4,0.2,0.7l-0.7,2.8c-0.1,0.2-0.2,0.4-0.3,0.6c-0.1,0.1-0.1,0.1-0.2,0.1c-0.1,0-0.2,0.1-0.3,0%20c-0.5-0.1-1.6-0.4-3.4,0.6c-1.9,1-2.6,2.6-2.5,3.8c0,1.5,0.8,1.9,3.3,1.9c3.4,0.1,4.9,1.6,5,5C94.7,103.4,92.9,107,90.1,109.3z%20M109.6,103.9c0,0.3,0,0.6-0.3,0.7l-9.4,5.7c-0.2,0.1-0.4,0-0.4-0.3v-2.4c0-0.3,0.2-0.5,0.4-0.6l9.3-5.5c0.2-0.1,0.4,0,0.4,0.3%20V103.9z%20M116.1,49.6L80.9,71.3c-4.4,2.6-7.6,5.4-7.6,10.7v43.4c0,3.2,1.3,5.2,3.2,5.8c-0.6,0.1-1.3,0.2-2,0.2%20c-2.1,0-4.1-0.6-5.9-1.6l-37.2-22.1c-3.6-2.2-5.9-6.2-5.9-10.5V53c0-4.3,2.3-8.4,5.9-10.5l37.2-22.1c1.8-1.1,3.8-1.6,5.9-1.6%20s4.1,0.6,5.9,1.6l37.2,22.1c3.1,1.8,5.1,5,5.7,8.5C122.1,48.4,119.3,47.7,116.1,49.6z'/%3e%3c/g%3e%3c/svg%3e",{SvelteComponent:co,append:Te,attr:ct,check_outros:_o,create_component:uo,destroy_component:fo,destroy_each:po,detach:ve,element:Le,empty:mo,ensure_array_like:il,flush:Et,group_outros:ho,init:go,insert:ke,mount_component:$o,noop:bo,safe_not_equal:vo,set_data:Tt,set_style:ko,space:bt,text:Ne,toggle_class:ol,transition_in:Rt,transition_out:un}=window.__gradio__svelte__internal;function rl(l,e,t){const n=l.slice();return n[4]=e[t].label,n[5]=e[t].type,n[6]=e[t].python_type,n[7]=e[t].component,n[8]=e[t].serializer,n[10]=t,n}function yo(l){let e;return{c(){e=Ne("1 element")},m(t,n){ke(t,e,n)},p:bo,d(t){t&&ve(e)}}}function wo(l){let e=l[3]=="python"?"tuple":"list",t,n,s=l[1].length+"",o,i;return{c(){t=Ne(e),n=Ne(" of "),o=Ne(s),i=Ne(`
		elements`)},m(a,r){ke(a,t,r),ke(a,n,r),ke(a,o,r),ke(a,i,r)},p(a,r){r&8&&e!==(e=a[3]=="python"?"tuple":"list")&&Tt(t,e),r&2&&s!==(s=a[1].length+"")&&Tt(o,s)},d(a){a&&(ve(t),ve(n),ve(o),ve(i))}}}function al(l){let e;return{c(){e=Le("span"),e.textContent=`[${l[10]}]`,ct(e,"class","code svelte-16h224k")},m(t,n){ke(t,e,n)},d(t){t&&ve(e)}}}function Co(l){let e=l[2][l[10]].type+"",t;return{c(){t=Ne(e)},m(n,s){ke(n,t,s)},p(n,s){s&4&&e!==(e=n[2][n[10]].type+"")&&Tt(t,e)},d(n){n&&ve(t)}}}function So(l){let e=l[6].type+"",t;return{c(){t=Ne(e)},m(n,s){ke(n,t,s)},p(n,s){s&2&&e!==(e=n[6].type+"")&&Tt(t,e)},d(n){n&&ve(t)}}}function cl(l){let e,t,n,s,o,i,a,r,c,_=l[4]+"",u,f,d=l[7]+"",g,m,k,p=l[1].length>1&&al(l);function w(v,y){return v[3]==="python"?So:Co}let $=w(l),h=$(l);return{c(){e=Le("hr"),t=bt(),n=Le("div"),s=Le("p"),p&&p.c(),o=bt(),i=Le("span"),h.c(),a=bt(),r=Le("p"),c=Ne('The output value that appears in the "'),u=Ne(_),f=Ne('" '),g=Ne(d),m=Ne(`
				component.`),k=bt(),ct(e,"class","hr svelte-16h224k"),ct(i,"class","code highlight svelte-16h224k"),ct(r,"class","desc svelte-16h224k"),ko(n,"margin","10px")},m(v,y){ke(v,e,y),ke(v,t,y),ke(v,n,y),Te(n,s),p&&p.m(s,null),Te(s,o),Te(s,i),h.m(i,null),Te(n,a),Te(n,r),Te(r,c),Te(r,u),Te(r,f),Te(r,g),Te(r,m),Te(n,k)},p(v,y){v[1].length>1?p||(p=al(v),p.c(),p.m(s,o)):p&&(p.d(1),p=null),$===($=w(v))&&h?h.p(v,y):(h.d(1),h=$(v),h&&(h.c(),h.m(i,null))),y&2&&_!==(_=v[4]+"")&&Tt(u,_),y&2&&d!==(d=v[7]+"")&&Tt(g,d)},d(v){v&&(ve(e),ve(t),ve(n)),p&&p.d(),h.d()}}}function _l(l){let e,t,n;return t=new Il({props:{margin:!1}}),{c(){e=Le("div"),uo(t.$$.fragment),ct(e,"class","load-wrap")},m(s,o){ke(s,e,o),$o(t,e,null),n=!0},i(s){n||(Rt(t.$$.fragment,s),n=!0)},o(s){un(t.$$.fragment,s),n=!1},d(s){s&&ve(e),fo(t)}}}function jo(l){let e,t,n,s,o,i,a,r;function c(m,k){return m[1].length>1?wo:yo}let _=c(l),u=_(l),f=il(l[1]),d=[];for(let m=0;m<f.length;m+=1)d[m]=cl(rl(l,f,m));let g=l[0]&&_l();return{c(){e=Le("h4"),t=Le("div"),t.innerHTML='<div class="toggle-dot toggle-right svelte-16h224k"></div>',n=Ne(`
	Returns `),u.c(),s=bt(),o=Le("div");for(let m=0;m<d.length;m+=1)d[m].c();i=bt(),g&&g.c(),a=mo(),ct(t,"class","toggle-icon svelte-16h224k"),ct(e,"class","svelte-16h224k"),ol(o,"hide",l[0])},m(m,k){ke(m,e,k),Te(e,t),Te(e,n),u.m(e,null),ke(m,s,k),ke(m,o,k);for(let p=0;p<d.length;p+=1)d[p]&&d[p].m(o,null);ke(m,i,k),g&&g.m(m,k),ke(m,a,k),r=!0},p(m,[k]){if(_===(_=c(m))&&u?u.p(m,k):(u.d(1),u=_(m),u&&(u.c(),u.m(e,null))),k&14){f=il(m[1]);let p;for(p=0;p<f.length;p+=1){const w=rl(m,f,p);d[p]?d[p].p(w,k):(d[p]=cl(w),d[p].c(),d[p].m(o,null))}for(;p<d.length;p+=1)d[p].d(1);d.length=f.length}(!r||k&1)&&ol(o,"hide",m[0]),m[0]?g?k&1&&Rt(g,1):(g=_l(),g.c(),Rt(g,1),g.m(a.parentNode,a)):g&&(ho(),un(g,1,1,()=>{g=null}),_o())},i(m){r||(Rt(g),r=!0)},o(m){un(g),r=!1},d(m){m&&(ve(e),ve(s),ve(o),ve(i),ve(a)),u.d(),po(d,m),g&&g.d(m)}}}function To(l,e,t){let{is_running:n}=e,{endpoint_returns:s}=e,{js_returns:o}=e,{current_language:i}=e;return l.$$set=a=>{"is_running"in a&&t(0,n=a.is_running),"endpoint_returns"in a&&t(1,s=a.endpoint_returns),"js_returns"in a&&t(2,o=a.js_returns),"current_language"in a&&t(3,i=a.current_language)},[n,s,o,i]}class qo extends co{constructor(e){super(),go(this,e,To,jo,vo,{is_running:0,endpoint_returns:1,js_returns:2,current_language:3})}get is_running(){return this.$$.ctx[0]}set is_running(e){this.$$set({is_running:e}),Et()}get endpoint_returns(){return this.$$.ctx[1]}set endpoint_returns(e){this.$$set({endpoint_returns:e}),Et()}get js_returns(){return this.$$.ctx[2]}set js_returns(e){this.$$set({js_returns:e}),Et()}get current_language(){return this.$$.ctx[3]}set current_language(e){this.$$set({current_language:e}),Et()}}const zo=""+new URL("mcp-DNm9doVd.svg",import.meta.url).href,{SvelteComponent:No,append:I,attr:L,check_outros:fn,create_component:tt,destroy_component:nt,destroy_each:pn,detach:B,element:D,empty:Po,ensure_array_like:vt,flush:Re,group_outros:dn,init:Ao,insert:G,listen:qt,mount_component:lt,noop:Nt,run_all:Oo,safe_not_equal:Eo,set_data:Oe,space:se,text:ie,transition_in:Se,transition_out:je}=window.__gradio__svelte__internal;function ul(l,e,t){const n=l.slice();return n[24]=e[t],n[25]=e,n[26]=t,n}function fl(l,e,t){const n=l.slice();return n[27]=e[t][0],n[28]=e[t][1],n}function pl(l,e,t){const n=l.slice();return n[31]=e[t][0],n[32]=e[t][1],n}function Mo(l){let e,t,n,s,o,i,a;return{c(){e=ie(`This Gradio app can also serve as an MCP server, with an MCP tool
	corresponding to each API endpoint. To enable this, launch this Gradio app
	with `),t=D("code"),t.textContent=".launch(mcp_server=True)",n=ie(` or set the
	`),s=D("code"),s.textContent="GRADIO_MCP_SERVER",o=ie(`
	env variable to
	`),i=D("code"),i.textContent='"True"',a=ie(".")},m(r,c){G(r,e,c),G(r,t,c),G(r,n,c),G(r,s,c),G(r,o,c),G(r,i,c),G(r,a,c)},p:Nt,i:Nt,o:Nt,d(r){r&&(B(e),B(t),B(n),B(s),B(o),B(i),B(a))}}}function Io(l){let e,t,n,s,o,i,a,r,c=(l[1].length>0?l[1].length:l[0].length)+"",_,u,f,d,g,m,k,p,w,$,h,v,y,C,A,T,E,b,S=vt(l[12]),z=[];for(let P=0;P<S.length;P+=1)z[P]=dl(pl(l,S,P));let N=l[6]!=="stdio"&&ml(l),q=l[1].length>0&&hl(l),F=vt(l[1].length>0?l[1]:l[0]),U=[];for(let P=0;P<F.length;P+=1)U[P]=vl(ul(l,F,P));const Z=[Ho,Fo,Uo],K=[];function W(P,j){return P[6]==="streamable_http"?0:P[6]==="sse"?1:P[6]==="stdio"?2:-1}~(w=W(l))&&($=K[w]=Z[w](l));let Y=l[4]&&kl(l);return{c(){e=D("div"),t=D("div"),n=D("span"),n.textContent="Transport:",s=se();for(let P=0;P<z.length;P+=1)z[P].c();o=se(),N&&N.c(),i=se(),a=D("div"),r=D("strong"),_=ie(c),u=ie(" Available MCP Tools"),f=se(),q&&q.c(),d=se(),g=D("div");for(let P=0;P<U.length;P+=1)U[P].c();m=se(),k=D("p"),k.textContent=" ",p=se(),$&&$.c(),h=se(),Y&&Y.c(),v=se(),y=D("p"),y.textContent=" ",C=se(),A=D("p"),T=D("a"),E=ie("Read more about MCP in the Gradio docs"),L(n,"class","transport-label svelte-1czpo3w"),L(t,"class","snippets svelte-1czpo3w"),L(e,"class","transport-selection svelte-1czpo3w"),L(a,"class","tool-selection svelte-1czpo3w"),L(g,"class","mcp-tools svelte-1czpo3w"),L(T,"href",l[5]),L(T,"target","_blank"),L(T,"class","svelte-1czpo3w")},m(P,j){G(P,e,j),I(e,t),I(t,n),I(t,s);for(let me=0;me<z.length;me+=1)z[me]&&z[me].m(t,null);G(P,o,j),N&&N.m(P,j),G(P,i,j),G(P,a,j),I(a,r),I(r,_),I(r,u),I(a,f),q&&q.m(a,null),G(P,d,j),G(P,g,j);for(let me=0;me<U.length;me+=1)U[me]&&U[me].m(g,null);G(P,m,j),G(P,k,j),G(P,p,j),~w&&K[w].m(P,j),G(P,h,j),Y&&Y.m(P,j),G(P,v,j),G(P,y,j),G(P,C,j),G(P,A,j),I(A,T),I(T,E),b=!0},p(P,j){if(j[0]&4160){S=vt(P[12]);let le;for(le=0;le<S.length;le+=1){const ce=pl(P,S,le);z[le]?z[le].p(ce,j):(z[le]=dl(ce),z[le].c(),z[le].m(t,null))}for(;le<z.length;le+=1)z[le].d(1);z.length=S.length}if(P[6]!=="stdio"?N?(N.p(P,j),j[0]&64&&Se(N,1)):(N=ml(P),N.c(),Se(N,1),N.m(i.parentNode,i)):N&&(dn(),je(N,1,1,()=>{N=null}),fn()),(!b||j[0]&3)&&c!==(c=(P[1].length>0?P[1].length:P[0].length)+"")&&Oe(_,c),P[1].length>0?q?q.p(P,j):(q=hl(P),q.c(),q.m(a,null)):q&&(q.d(1),q=null),j[0]&71){F=vt(P[1].length>0?P[1]:P[0]);let le;for(le=0;le<F.length;le+=1){const ce=ul(P,F,le);U[le]?U[le].p(ce,j):(U[le]=vl(ce),U[le].c(),U[le].m(g,null))}for(;le<U.length;le+=1)U[le].d(1);U.length=F.length}let me=w;w=W(P),w===me?~w&&K[w].p(P,j):($&&(dn(),je(K[me],1,1,()=>{K[me]=null}),fn()),~w?($=K[w],$?$.p(P,j):($=K[w]=Z[w](P),$.c()),Se($,1),$.m(h.parentNode,h)):$=null),P[4]?Y?Y.p(P,j):(Y=kl(P),Y.c(),Y.m(v.parentNode,v)):Y&&(Y.d(1),Y=null),(!b||j[0]&32)&&L(T,"href",P[5])},i(P){b||(Se(N),Se($),b=!0)},o(P){je(N),je($),b=!1},d(P){P&&(B(e),B(o),B(i),B(a),B(d),B(g),B(m),B(k),B(p),B(h),B(v),B(y),B(C),B(A)),pn(z,P),N&&N.d(P),q&&q.d(),pn(U,P),~w&&K[w].d(P),Y&&Y.d(P)}}}function dl(l){let e,t=l[32]+"",n,s,o,i,a;function r(){return l[17](l[31])}return{c(){e=D("button"),n=ie(t),s=se(),L(e,"type","button"),L(e,"class",o="snippet "+(l[6]===l[31]?"current-lang":"inactive-lang")+" svelte-1czpo3w")},m(c,_){G(c,e,_),I(e,n),I(e,s),i||(a=qt(e,"click",r),i=!0)},p(c,_){l=c,_[0]&64&&o!==(o="snippet "+(l[6]===l[31]?"current-lang":"inactive-lang")+" svelte-1czpo3w")&&L(e,"class",o)},d(c){c&&B(e),i=!1,a()}}}function ml(l){let e,t,n,s;return e=new We({props:{$$slots:{default:[Do]},$$scope:{ctx:l}}}),{c(){tt(e.$$.fragment),t=se(),n=D("p"),n.textContent=" "},m(o,i){lt(e,o,i),G(o,t,i),G(o,n,i),s=!0},p(o,i){const a={};i[0]&2112|i[1]&16&&(a.$$scope={dirty:i,ctx:o}),e.$set(a)},i(o){s||(Se(e.$$.fragment,o),s=!0)},o(o){je(e.$$.fragment,o),s=!1},d(o){o&&(B(t),B(n)),nt(e,o)}}}function Do(l){let e,t,n,s,o=l[6]==="sse"?"SSE":"Streamable HTTP",i,a,r,c,_,u,f,d;return f=new Pe({props:{code:l[11]}}),{c(){e=D("div"),t=D("label"),n=D("span"),n.textContent="●",s=ie("MCP Server URL ("),i=ie(o),a=ie(")"),r=se(),c=D("div"),_=D("input"),u=se(),tt(f.$$.fragment),L(n,"class","status-indicator active svelte-1czpo3w"),L(t,"for","mcp-server-url"),L(t,"class","svelte-1czpo3w"),L(_,"id","mcp-server-url"),L(_,"type","text"),_.readOnly=!0,_.value=l[11],L(_,"class","svelte-1czpo3w"),L(c,"class","textbox svelte-1czpo3w"),L(e,"class","mcp-url svelte-1czpo3w")},m(g,m){G(g,e,m),I(e,t),I(t,n),I(t,s),I(t,i),I(t,a),I(e,r),I(e,c),I(c,_),I(c,u),lt(f,c,null),d=!0},p(g,m){(!d||m[0]&64)&&o!==(o=g[6]==="sse"?"SSE":"Streamable HTTP")&&Oe(i,o),(!d||m[0]&2048&&_.value!==g[11])&&(_.value=g[11]);const k={};m[0]&2048&&(k.code=g[11]),f.$set(k)},i(g){d||(Se(f.$$.fragment,g),d=!0)},o(g){je(f.$$.fragment,g),d=!1},d(g){g&&B(e),nt(f)}}}function hl(l){let e,t,n,s,o,i;return{c(){e=D("div"),t=D("button"),t.textContent="Select All",n=se(),s=D("button"),s.textContent="Select None",L(t,"class","select-all-btn svelte-1czpo3w"),L(s,"class","select-none-btn svelte-1czpo3w"),L(e,"class","tool-selection-controls svelte-1czpo3w")},m(a,r){G(a,e,r),I(e,t),I(e,n),I(e,s),o||(i=[qt(t,"click",l[18]),qt(s,"click",l[19])],o=!0)},p:Nt,d(a){a&&B(e),o=!1,Oo(i)}}}function gl(l){let e,t,n,s,o,i;function a(...r){return l[20](l[24],...r)}return{c(){e=D("input"),L(e,"type","checkbox"),L(e,"class","tool-checkbox svelte-1czpo3w"),e.checked=t=l[2].has(l[24].name)||l[6]!=="streamable_http",e.disabled=n=l[6]!=="streamable_http",L(e,"style",s=l[6]!=="streamable_http"?"opacity: 0.5; cursor: not-allowed;":"")},m(r,c){G(r,e,c),o||(i=qt(e,"change",a),o=!0)},p(r,c){l=r,c[0]&71&&t!==(t=l[2].has(l[24].name)||l[6]!=="streamable_http")&&(e.checked=t),c[0]&64&&n!==(n=l[6]!=="streamable_http")&&(e.disabled=n),c[0]&64&&s!==(s=l[6]!=="streamable_http"?"opacity: 0.5; cursor: not-allowed;":"")&&L(e,"style",s)},d(r){r&&B(e),o=!1,i()}}}function $l(l){let e,t;function n(i,a){return a[0]&3&&(t=null),t==null&&(t=Object.keys(i[24].parameters).length>0),t?Lo:Ro}let s=n(l,[-1,-1]),o=s(l);return{c(){e=D("div"),o.c(),L(e,"class","tool-content svelte-1czpo3w")},m(i,a){G(i,e,a),o.m(e,null)},p(i,a){s===(s=n(i,a))&&o?o.p(i,a):(o.d(1),o=s(i),o&&(o.c(),o.m(e,null)))},d(i){i&&B(e),o.d()}}}function Ro(l){let e;return{c(){e=D("p"),e.textContent="Takes no input parameters"},m(t,n){G(t,e,n)},p:Nt,d(t){t&&B(e)}}}function Lo(l){let e,t=vt(Object.entries(l[24].parameters)),n=[];for(let s=0;s<t.length;s+=1)n[s]=bl(fl(l,t,s));return{c(){e=D("div");for(let s=0;s<n.length;s+=1)n[s].c();L(e,"class","tool-parameters")},m(s,o){G(s,e,o);for(let i=0;i<n.length;i+=1)n[i]&&n[i].m(e,null)},p(s,o){if(o[0]&3){t=vt(Object.entries(s[24].parameters));let i;for(i=0;i<t.length;i+=1){const a=fl(s,t,i);n[i]?n[i].p(a,o):(n[i]=bl(a),n[i].c(),n[i].m(e,null))}for(;i<n.length;i+=1)n[i].d(1);n.length=t.length}},d(s){s&&B(e),pn(n,s)}}}function bl(l){let e,t,n=l[27]+"",s,o,i,a,r=l[28].type+"",c,_=l[28].default!==void 0?`, default: ${JSON.stringify(l[28].default)}`:"",u,f,d,g,m=(l[28].description?l[28].description:"⚠︎ No description for this parameter in function docstring")+"",k,p;return{c(){e=D("div"),t=D("code"),s=ie(n),o=se(),i=D("span"),a=ie("("),c=ie(r),u=ie(_),f=ie(")"),d=se(),g=D("p"),k=ie(m),p=se(),L(t,"class","svelte-1czpo3w"),L(i,"class","parameter-type svelte-1czpo3w"),L(g,"class","parameter-description svelte-1czpo3w"),L(e,"class","parameter svelte-1czpo3w")},m(w,$){G(w,e,$),I(e,t),I(t,s),I(e,o),I(e,i),I(i,a),I(i,c),I(i,u),I(i,f),I(e,d),I(e,g),I(g,k),I(e,p)},p(w,$){$[0]&3&&n!==(n=w[27]+"")&&Oe(s,n),$[0]&3&&r!==(r=w[28].type+"")&&Oe(c,r),$[0]&3&&_!==(_=w[28].default!==void 0?`, default: ${JSON.stringify(w[28].default)}`:"")&&Oe(u,_),$[0]&3&&m!==(m=(w[28].description?w[28].description:"⚠︎ No description for this parameter in function docstring")+"")&&Oe(k,m)},d(w){w&&B(e)}}}function vl(l){let e,t,n,s,o,i,a=l[24].name+"",r,c,_,u=(l[24].description?l[24].description:"⚠︎ No description provided in function docstring")+"",f,d,g,m=l[24].expanded?"▼":"▶",k,p,w,$,h,v=l[1].length>0&&gl(l);function y(){return l[21](l[24],l[25],l[26])}let C=l[24].expanded&&$l(l);return{c(){e=D("div"),t=D("div"),v&&v.c(),n=se(),s=D("button"),o=D("span"),i=D("span"),r=ie(a),c=ie(`  
							`),_=D("span"),f=ie(u),d=se(),g=D("span"),k=ie(m),p=se(),C&&C.c(),w=se(),L(i,"class","tool-name svelte-1czpo3w"),L(_,"class","tool-description svelte-1czpo3w"),L(g,"class","tool-arrow svelte-1czpo3w"),L(s,"class","tool-header svelte-1czpo3w"),L(t,"class","tool-header-wrapper svelte-1czpo3w"),L(e,"class","tool-item svelte-1czpo3w")},m(A,T){G(A,e,T),I(e,t),v&&v.m(t,null),I(t,n),I(t,s),I(s,o),I(o,i),I(i,r),I(o,c),I(o,_),I(_,f),I(s,d),I(s,g),I(g,k),I(e,p),C&&C.m(e,null),I(e,w),$||(h=qt(s,"click",y),$=!0)},p(A,T){l=A,l[1].length>0?v?v.p(l,T):(v=gl(l),v.c(),v.m(t,n)):v&&(v.d(1),v=null),T[0]&3&&a!==(a=l[24].name+"")&&Oe(r,a),T[0]&3&&u!==(u=(l[24].description?l[24].description:"⚠︎ No description provided in function docstring")+"")&&Oe(f,u),T[0]&3&&m!==(m=l[24].expanded?"▼":"▶")&&Oe(k,m),l[24].expanded?C?C.p(l,T):(C=$l(l),C.c(),C.m(e,w)):C&&(C.d(1),C=null)},d(A){A&&B(e),v&&v.d(),C&&C.d(),$=!1,h()}}}function Uo(l){let e,t,n,s,o,i,a,r;return a=new We({props:{$$slots:{default:[Jo]},$$scope:{ctx:l}}}),{c(){e=D("strong"),e.textContent="STDIO Transport",t=ie(`: For clients that only support stdio (e.g.
		Claude Desktop), first
		`),n=D("a"),n.textContent="install Node.js",s=ie(`. Then, you can use the following command:
		`),o=D("p"),o.textContent=" ",i=se(),tt(a.$$.fragment),L(n,"href","https://nodejs.org/en/download/"),L(n,"target","_blank"),L(n,"class","svelte-1czpo3w")},m(c,_){G(c,e,_),G(c,t,_),G(c,n,_),G(c,s,_),G(c,o,_),G(c,i,_),lt(a,c,_),r=!0},p(c,_){const u={};_[0]&256|_[1]&16&&(u.$$scope={dirty:_,ctx:c}),a.$set(u)},i(c){r||(Se(a.$$.fragment,c),r=!0)},o(c){je(a.$$.fragment,c),r=!1},d(c){c&&(B(e),B(t),B(n),B(s),B(o),B(i)),nt(a,c)}}}function Fo(l){let e,t,n,s,o,i;return o=new We({props:{$$slots:{default:[Vo]},$$scope:{ctx:l}}}),{c(){e=D("strong"),e.textContent="SSE Transport",t=ie(`: The SSE transport has been deprecated by the
		MCP spec. We recommend using the Streamable HTTP transport instead. But to
		add this MCP to clients that only support server-sent events (SSE), simply
		add the following configuration to your MCP config.
		`),n=D("p"),n.textContent=" ",s=se(),tt(o.$$.fragment)},m(a,r){G(a,e,r),G(a,t,r),G(a,n,r),G(a,s,r),lt(o,a,r),i=!0},p(a,r){const c={};r[0]&512|r[1]&16&&(c.$$scope={dirty:r,ctx:a}),o.$set(c)},i(a){i||(Se(o.$$.fragment,a),i=!0)},o(a){je(o.$$.fragment,a),i=!1},d(a){a&&(B(e),B(t),B(n),B(s)),nt(o,a)}}}function Ho(l){let e,t,n,s,o,i;return o=new We({props:{$$slots:{default:[Bo]},$$scope:{ctx:l}}}),{c(){e=D("strong"),e.textContent="Streamable HTTP Transport",t=ie(`: To add this MCP to clients that
		support Streamable HTTP, simply add the following configuration to your MCP
		config.
		`),n=D("p"),n.textContent=" ",s=se(),tt(o.$$.fragment)},m(a,r){G(a,e,r),G(a,t,r),G(a,n,r),G(a,s,r),lt(o,a,r),i=!0},p(a,r){const c={};r[0]&1024|r[1]&16&&(c.$$scope={dirty:r,ctx:a}),o.$set(c)},i(a){i||(Se(o.$$.fragment,a),i=!0)},o(a){je(o.$$.fragment,a),i=!1},d(a){a&&(B(e),B(t),B(n),B(s)),nt(o,a)}}}function Jo(l){let e,t,n,s,o,i,a=JSON.stringify(l[8],null,2)+"",r,c;return n=new Pe({props:{code:JSON.stringify(l[8],null,2)}}),{c(){e=D("code"),t=D("div"),tt(n.$$.fragment),s=se(),o=D("div"),i=D("pre"),r=ie(a),L(t,"class","copy svelte-1czpo3w"),L(i,"class","svelte-1czpo3w"),L(e,"class","svelte-1czpo3w")},m(_,u){G(_,e,u),I(e,t),lt(n,t,null),I(e,s),I(e,o),I(o,i),I(i,r),c=!0},p(_,u){const f={};u[0]&256&&(f.code=JSON.stringify(_[8],null,2)),n.$set(f),(!c||u[0]&256)&&a!==(a=JSON.stringify(_[8],null,2)+"")&&Oe(r,a)},i(_){c||(Se(n.$$.fragment,_),c=!0)},o(_){je(n.$$.fragment,_),c=!1},d(_){_&&B(e),nt(n)}}}function Vo(l){let e,t,n,s,o,i,a=JSON.stringify(l[9],null,2)+"",r,c;return n=new Pe({props:{code:JSON.stringify(l[9],null,2)}}),{c(){e=D("code"),t=D("div"),tt(n.$$.fragment),s=se(),o=D("div"),i=D("pre"),r=ie(a),L(t,"class","copy svelte-1czpo3w"),L(i,"class","svelte-1czpo3w"),L(e,"class","svelte-1czpo3w")},m(_,u){G(_,e,u),I(e,t),lt(n,t,null),I(e,s),I(e,o),I(o,i),I(i,r),c=!0},p(_,u){const f={};u[0]&512&&(f.code=JSON.stringify(_[9],null,2)),n.$set(f),(!c||u[0]&512)&&a!==(a=JSON.stringify(_[9],null,2)+"")&&Oe(r,a)},i(_){c||(Se(n.$$.fragment,_),c=!0)},o(_){je(n.$$.fragment,_),c=!1},d(_){_&&B(e),nt(n)}}}function Bo(l){let e,t,n,s,o,i,a=JSON.stringify(l[10],null,2)+"",r,c;return n=new Pe({props:{code:JSON.stringify(l[10],null,2)}}),{c(){e=D("code"),t=D("div"),tt(n.$$.fragment),s=se(),o=D("div"),i=D("pre"),r=ie(a),L(t,"class","copy svelte-1czpo3w"),L(i,"class","svelte-1czpo3w"),L(e,"class","svelte-1czpo3w")},m(_,u){G(_,e,u),I(e,t),lt(n,t,null),I(e,s),I(e,o),I(o,i),I(i,r),c=!0},p(_,u){const f={};u[0]&1024&&(f.code=JSON.stringify(_[10],null,2)),n.$set(f),(!c||u[0]&1024)&&a!==(a=JSON.stringify(_[10],null,2)+"")&&Oe(r,a)},i(_){c||(Se(n.$$.fragment,_),c=!0)},o(_){je(n.$$.fragment,_),c=!1},d(_){_&&B(e),nt(n)}}}function kl(l){let e,t,n,s,o,i,a,r;return{c(){e=D("div"),t=D("label"),n=D("input"),s=ie(`
				Include Gradio file upload tool`),o=se(),i=D("p"),i.innerHTML=`The <code>upload_files_to_gradio</code> tool uploads files from your
				local <code>UPLOAD_DIRECTORY</code> (or any of its subdirectories) to
				the Gradio app. This is needed because MCP servers require files to be
				provided as URLs. You can omit this tool if you prefer to upload files
				manually. This tool requires
				<a href="https://docs.astral.sh/uv/getting-started/installation/" target="_blank" class="svelte-1czpo3w">uv</a> to be installed.`,L(n,"type","checkbox"),L(n,"class","checkbox svelte-1czpo3w"),L(t,"class","checkbox-label svelte-1czpo3w"),L(i,"class","file-upload-explanation svelte-1czpo3w"),L(e,"class","file-upload-section svelte-1czpo3w")},m(c,_){G(c,e,_),I(e,t),I(t,n),n.checked=l[7],I(t,s),I(e,o),I(e,i),a||(r=qt(n,"change",l[22]),a=!0)},p(c,_){_[0]&128&&(n.checked=c[7])},d(c){c&&B(e),a=!1,r()}}}function Go(l){let e,t,n,s;const o=[Io,Mo],i=[];function a(r,c){return r[3]?0:1}return e=a(l),t=i[e]=o[e](l),{c(){t.c(),n=Po()},m(r,c){i[e].m(r,c),G(r,n,c),s=!0},p(r,c){let _=e;e=a(r),e===_?i[e].p(r,c):(dn(),je(i[_],1,1,()=>{i[_]=null}),fn(),t=i[e],t?t.p(r,c):(t=i[e]=o[e](r),t.c()),Se(t,1),t.m(n.parentNode,n))},i(r){s||(Se(t),s=!0)},o(r){je(t),s=!1},d(r){r&&B(n),i[e].d(r)}}}function Yo(l,e,t){let n,s,o,i,{mcp_server_active:a}=e,{mcp_server_url:r}=e,{mcp_server_url_streamable:c}=e,{tools:_}=e,{all_tools:u=[]}=e,{selected_tools:f=new Set}=e,{mcp_json_sse:d}=e,{mcp_json_stdio:g}=e,{file_data_present:m}=e,{mcp_docs:k}=e,p="streamable_http",w=!0;const $=[["streamable_http","Streamable HTTP"],["sse","SSE"],["stdio","STDIO"]];function h(b,S){if(!b)return null;const z=JSON.parse(JSON.stringify(b));if(S&&m){const N={command:"uvx",args:["--from","gradio[mcp]","gradio","upload-mcp",p==="sse"?r:c,"<UPLOAD_DIRECTORY>"]};z.mcpServers.upload_files_to_gradio=N}else delete z.mcpServers?.upload_files_to_gradio;return z}const v=b=>t(6,p=b),y=()=>{t(2,f=new Set(u.map(b=>b.name)))},C=()=>{t(2,f=new Set)},A=(b,S)=>{S.currentTarget.checked?f.add(b.name):f.delete(b.name),t(2,f)},T=(b,S,z)=>t(1,S[z].expanded=!b.expanded,u,t(0,_));function E(){w=this.checked,t(7,w)}return l.$$set=b=>{"mcp_server_active"in b&&t(3,a=b.mcp_server_active),"mcp_server_url"in b&&t(13,r=b.mcp_server_url),"mcp_server_url_streamable"in b&&t(14,c=b.mcp_server_url_streamable),"tools"in b&&t(0,_=b.tools),"all_tools"in b&&t(1,u=b.all_tools),"selected_tools"in b&&t(2,f=b.selected_tools),"mcp_json_sse"in b&&t(15,d=b.mcp_json_sse),"mcp_json_stdio"in b&&t(16,g=b.mcp_json_stdio),"file_data_present"in b&&t(4,m=b.file_data_present),"mcp_docs"in b&&t(5,k=b.mcp_docs)},l.$$.update=()=>{l.$$.dirty[0]&24640&&t(11,n=p==="sse"?r:c),l.$$.dirty[0]&49280&&t(10,s=h(d?{...d,mcpServers:{...d.mcpServers,gradio:{...d.mcpServers.gradio,url:c}}}:null,w)),l.$$.dirty[0]&32896&&t(9,o=h(d,w)),l.$$.dirty[0]&65664&&t(8,i=h(g,w))},[_,u,f,a,m,k,p,w,i,o,s,n,$,r,c,d,g,v,y,C,A,T,E]}class Wo extends No{constructor(e){super(),Ao(this,e,Yo,Go,Eo,{mcp_server_active:3,mcp_server_url:13,mcp_server_url_streamable:14,tools:0,all_tools:1,selected_tools:2,mcp_json_sse:15,mcp_json_stdio:16,file_data_present:4,mcp_docs:5},null,[-1,-1])}get mcp_server_active(){return this.$$.ctx[3]}set mcp_server_active(e){this.$$set({mcp_server_active:e}),Re()}get mcp_server_url(){return this.$$.ctx[13]}set mcp_server_url(e){this.$$set({mcp_server_url:e}),Re()}get mcp_server_url_streamable(){return this.$$.ctx[14]}set mcp_server_url_streamable(e){this.$$set({mcp_server_url_streamable:e}),Re()}get tools(){return this.$$.ctx[0]}set tools(e){this.$$set({tools:e}),Re()}get all_tools(){return this.$$.ctx[1]}set all_tools(e){this.$$set({all_tools:e}),Re()}get selected_tools(){return this.$$.ctx[2]}set selected_tools(e){this.$$set({selected_tools:e}),Re()}get mcp_json_sse(){return this.$$.ctx[15]}set mcp_json_sse(e){this.$$set({mcp_json_sse:e}),Re()}get mcp_json_stdio(){return this.$$.ctx[16]}set mcp_json_stdio(e){this.$$set({mcp_json_stdio:e}),Re()}get file_data_present(){return this.$$.ctx[4]}set file_data_present(e){this.$$set({file_data_present:e}),Re()}get mcp_docs(){return this.$$.ctx[5]}set mcp_docs(e){this.$$set({mcp_docs:e}),Re()}}const{SvelteComponent:Zo,add_flush_callback:Qo,append:ee,attr:x,bind:Xo,binding_callbacks:Ko,bubble:yl,check_outros:ut,create_component:Be,destroy_component:Ge,destroy_each:Jl,detach:H,element:ne,empty:Pt,ensure_array_like:Ut,flush:it,group_outros:ft,init:xo,insert:J,listen:er,mount_component:Ye,noop:kt,safe_not_equal:tr,set_data:mn,set_style:ot,space:we,src_url_equal:nr,text:te,transition_in:oe,transition_out:ae}=window.__gradio__svelte__internal,{onMount:lr,createEventDispatcher:sr}=window.__gradio__svelte__internal;function wl(l,e,t){const n=l.slice();return n[36]=e[t],n}function Cl(l,e,t){const n=l.slice();return n[39]=e[t][0],n[40]=e[t][1],n[41]=e[t][2],n}function Sl(l){let e,t,n,s;const o=[or,ir],i=[];function a(r,c){return r[17]?0:1}return e=a(l),t=i[e]=o[e](l),{c(){t.c(),n=Pt()},m(r,c){i[e].m(r,c),J(r,n,c),s=!0},p(r,c){t.p(r,c)},i(r){s||(oe(t),s=!0)},o(r){ae(t),s=!1},d(r){r&&H(n),i[e].d(r)}}}function ir(l){let e,t;return e=new os({props:{root:l[0]}}),e.$on("close",l[28]),{c(){Be(e.$$.fragment)},m(n,s){Ye(e,n,s),t=!0},p(n,s){const o={};s[0]&1&&(o.root=n[0]),e.$set(o)},i(n){t||(oe(e.$$.fragment,n),t=!0)},o(n){ae(e.$$.fragment,n),t=!1},d(n){Ge(e,n)}}}function or(l){let e,t,n,s,o,i,a,r,c,_,u,f,d;t=new Cs({props:{root:l[3]||l[0],api_count:l[17],current_language:l[10]}}),t.$on("close",l[23]);let g=Ut(l[18]),m=[];for(let h=0;h<g.length;h+=1)m[h]=jl(Cl(l,g,h));const k=[ar,rr],p=[];function w(h,v){return h[5].length?0:1}_=w(l),u=p[_]=k[_](l);let $=l[10]!=="mcp"&&Pl(l);return{c(){e=ne("div"),Be(t.$$.fragment),n=we(),s=ne("div"),o=ne("div"),o.innerHTML='<p style="font-size: var(--text-lg);">Choose one of the following ways to interact with the API.</p>',i=we(),a=ne("div"),r=ne("div");for(let h=0;h<m.length;h+=1)m[h].c();c=we(),u.c(),f=we(),$&&$.c(),x(e,"class","banner-wrap svelte-1b8jye8"),x(o,"class","client-doc svelte-1b8jye8"),x(r,"class","snippets svelte-1b8jye8"),x(a,"class","endpoint svelte-1b8jye8"),x(s,"class","docs-wrap svelte-1b8jye8")},m(h,v){J(h,e,v),Ye(t,e,null),J(h,n,v),J(h,s,v),ee(s,o),ee(s,i),ee(s,a),ee(a,r);for(let y=0;y<m.length;y+=1)m[y]&&m[y].m(r,null);ee(a,c),p[_].m(a,null),ee(a,f),$&&$.m(a,null),d=!0},p(h,v){const y={};if(v[0]&9&&(y.root=h[3]||h[0]),v[0]&1024&&(y.current_language=h[10]),t.$set(y),v[0]&263168){g=Ut(h[18]);let A;for(A=0;A<g.length;A+=1){const T=Cl(h,g,A);m[A]?m[A].p(T,v):(m[A]=jl(T),m[A].c(),m[A].m(r,null))}for(;A<m.length;A+=1)m[A].d(1);m.length=g.length}let C=_;_=w(h),_===C?p[_].p(h,v):(ft(),ae(p[C],1,1,()=>{p[C]=null}),ut(),u=p[_],u?u.p(h,v):(u=p[_]=k[_](h),u.c()),oe(u,1),u.m(a,f)),h[10]!=="mcp"?$?($.p(h,v),v[0]&1024&&oe($,1)):($=Pl(h),$.c(),oe($,1),$.m(a,null)):$&&(ft(),ae($,1,1,()=>{$=null}),ut())},i(h){d||(oe(t.$$.fragment,h),oe(u),oe($),d=!0)},o(h){ae(t.$$.fragment,h),ae(u),ae($),d=!1},d(h){h&&(H(e),H(n),H(s)),Ge(t),Jl(m,h),p[_].d(),$&&$.d()}}}function jl(l){let e,t,n,s,o=l[40]+"",i,a,r,c,_;function u(){return l[24](l[39])}return{c(){e=ne("li"),t=ne("img"),s=we(),i=te(o),a=we(),nr(t.src,n=l[41])||x(t,"src",n),x(t,"alt",""),x(t,"class","svelte-1b8jye8"),x(e,"class",r="snippet "+(l[10]===l[39]?"current-lang":"inactive-lang")+" svelte-1b8jye8")},m(f,d){J(f,e,d),ee(e,t),ee(e,s),ee(e,i),ee(e,a),c||(_=er(e,"click",u),c=!0)},p(f,d){l=f,d[0]&1024&&r!==(r="snippet "+(l[10]===l[39]?"current-lang":"inactive-lang")+" svelte-1b8jye8")&&x(e,"class",r)},d(f){f&&H(e),c=!1,_()}}}function rr(l){let e,t,n,s,o,i;const a=[ur,_r,cr],r=[];function c(u,f){return u[10]=="python"||u[10]=="javascript"?0:u[10]=="mcp"?1:2}t=c(l),n=r[t]=a[t](l);let _=l[10]!=="mcp"&&Tl(l);return{c(){e=ne("p"),n.c(),s=we(),_&&_.c(),o=Pt(),x(e,"class","padded svelte-1b8jye8")},m(u,f){J(u,e,f),r[t].m(e,null),J(u,s,f),_&&_.m(u,f),J(u,o,f),i=!0},p(u,f){let d=t;t=c(u),t===d?r[t].p(u,f):(ft(),ae(r[d],1,1,()=>{r[d]=null}),ut(),n=r[t],n?n.p(u,f):(n=r[t]=a[t](u),n.c()),oe(n,1),n.m(e,null)),u[10]!=="mcp"?_?(_.p(u,f),f[0]&1024&&oe(_,1)):(_=Tl(u),_.c(),oe(_,1),_.m(o.parentNode,o)):_&&(ft(),ae(_,1,1,()=>{_=null}),ut())},i(u){i||(oe(n),oe(_),i=!0)},o(u){ae(n),ae(_),i=!1},d(u){u&&(H(e),H(s),H(o)),r[t].d(),_&&_.d(u)}}}function ar(l){let e,t,n,s,o,i=l[5].length+"",a,r,c,_,u,f,d,g,m,k,p,w,$,h;return m=new io({props:{current_language:l[10],api_calls:l[5],dependencies:l[1],root:l[0],api_prefix:l[2].api_prefix,short_root:l[3]||l[0],username:l[4]}}),{c(){e=ne("div"),t=ne("p"),n=te("🪄 Recorded API Calls "),s=ne("span"),o=te("["),a=te(i),r=te("]"),c=we(),_=ne("p"),u=te(`Here is the code snippet to replay the most recently recorded API
							calls using the `),f=te(l[10]),d=te(`
							client.`),g=we(),Be(m.$$.fragment),k=we(),p=ne("p"),p.textContent=`Note: Some API calls only affect the UI, so when using the
							clients, the desired result may be achieved with only a subset of
							the recorded calls.`,w=we(),$=ne("p"),$.textContent="API Documentation",x(s,"class","api-count svelte-1b8jye8"),x(t,"id","num-recorded-api-calls"),ot(t,"font-size","var(--text-lg)"),ot(t,"font-weight","bold"),ot(t,"margin","10px 0px"),ot($,"font-size","var(--text-lg)"),ot($,"font-weight","bold"),ot($,"margin","30px 0px 10px")},m(v,y){J(v,e,y),ee(e,t),ee(t,n),ee(t,s),ee(s,o),ee(s,a),ee(s,r),ee(e,c),ee(e,_),ee(_,u),ee(_,f),ee(_,d),ee(e,g),Ye(m,e,null),ee(e,k),ee(e,p),J(v,w,y),J(v,$,y),h=!0},p(v,y){(!h||y[0]&32)&&i!==(i=v[5].length+"")&&mn(a,i),(!h||y[0]&1024)&&mn(f,v[10]);const C={};y[0]&1024&&(C.current_language=v[10]),y[0]&32&&(C.api_calls=v[5]),y[0]&2&&(C.dependencies=v[1]),y[0]&1&&(C.root=v[0]),y[0]&4&&(C.api_prefix=v[2].api_prefix),y[0]&9&&(C.short_root=v[3]||v[0]),y[0]&16&&(C.username=v[4]),m.$set(C)},i(v){h||(oe(m.$$.fragment,v),h=!0)},o(v){ae(m.$$.fragment,v),h=!1},d(v){v&&(H(e),H(w),H($)),Ge(m)}}}function cr(l){let e;return{c(){e=te("1. Confirm that you have cURL installed on your system.")},m(t,n){J(t,e,n)},p:kt,i:kt,o:kt,d(t){t&&H(e)}}}function _r(l){let e,t,n;function s(i){l[26](i)}let o={mcp_server_active:l[11],mcp_server_url:l[15],mcp_server_url_streamable:l[16],tools:l[6].filter(l[25]),all_tools:l[6],mcp_json_sse:l[7],mcp_json_stdio:l[8],file_data_present:l[14],mcp_docs:dr};return l[9]!==void 0&&(o.selected_tools=l[9]),e=new Wo({props:o}),Ko.push(()=>Xo(e,"selected_tools",s)),{c(){Be(e.$$.fragment)},m(i,a){Ye(e,i,a),n=!0},p(i,a){const r={};a[0]&2048&&(r.mcp_server_active=i[11]),a[0]&32768&&(r.mcp_server_url=i[15]),a[0]&65536&&(r.mcp_server_url_streamable=i[16]),a[0]&576&&(r.tools=i[6].filter(i[25])),a[0]&64&&(r.all_tools=i[6]),a[0]&128&&(r.mcp_json_sse=i[7]),a[0]&256&&(r.mcp_json_stdio=i[8]),a[0]&16384&&(r.file_data_present=i[14]),!t&&a[0]&512&&(t=!0,r.selected_tools=i[9],Qo(()=>t=!1)),e.$set(r)},i(i){n||(oe(e.$$.fragment,i),n=!0)},o(i){ae(e.$$.fragment,i),n=!1},d(i){Ge(e,i)}}}function ur(l){let e,t,n,s,o,i,a,r;return{c(){e=te(`1. Install the
							`),t=ne("span"),n=te(l[10]),s=te(`
							client (`),o=ne("a"),i=te("docs"),r=te(") if you don't already have it installed."),ot(t,"text-transform","capitalize"),x(o,"href",a=l[10]=="python"?Ht:Ft),x(o,"target","_blank"),x(o,"class","svelte-1b8jye8")},m(c,_){J(c,e,_),J(c,t,_),ee(t,n),J(c,s,_),J(c,o,_),ee(o,i),J(c,r,_)},p(c,_){_[0]&1024&&mn(n,c[10]),_[0]&1024&&a!==(a=c[10]=="python"?Ht:Ft)&&x(o,"href",a)},i:kt,o:kt,d(c){c&&(H(e),H(t),H(s),H(o),H(r))}}}function Tl(l){let e,t,n,s,o,i,a,r;e=new gi({props:{current_language:l[10]}});let c=l[3]&&ql(l);i=new gn({props:{size:"sm",variant:"secondary",$$slots:{default:[fr]},$$scope:{ctx:l}}}),i.$on("click",l[27]);let _=l[10]=="bash"&&zl(l);return{c(){Be(e.$$.fragment),t=we(),n=ne("p"),s=te(`2. Find the API endpoint below corresponding to your desired
							function in the app. Copy the code snippet, replacing the
							placeholder values with your own input data.
							`),c&&c.c(),o=te(`

							Or use the
							`),Be(i.$$.fragment),a=te(`
							to automatically generate your API requests.
							`),_&&_.c(),x(n,"class","padded svelte-1b8jye8")},m(u,f){Ye(e,u,f),J(u,t,f),J(u,n,f),ee(n,s),c&&c.m(n,null),ee(n,o),Ye(i,n,null),ee(n,a),_&&_.m(n,null),r=!0},p(u,f){const d={};f[0]&1024&&(d.current_language=u[10]),e.$set(d),u[3]?c?c.p(u,f):(c=ql(u),c.c(),c.m(n,o)):c&&(c.d(1),c=null);const g={};f[1]&8192&&(g.$$scope={dirty:f,ctx:u}),i.$set(g),u[10]=="bash"?_?_.p(u,f):(_=zl(u),_.c(),_.m(n,null)):_&&(_.d(1),_=null)},i(u){r||(oe(e.$$.fragment,u),oe(i.$$.fragment,u),r=!0)},o(u){ae(e.$$.fragment,u),ae(i.$$.fragment,u),r=!1},d(u){u&&(H(t),H(n)),Ge(e,u),c&&c.d(),Ge(i),_&&_.d()}}}function ql(l){let e,t,n,s,o;return{c(){e=te(`If this is a private Space, you may need to pass
								your Hugging Face token as well (`),t=ne("a"),n=te("read more"),o=te(")."),x(t,"href",s=l[10]=="python"?Ht+Mt:l[10]=="javascript"?Ft+Mt:hn),x(t,"class","underline svelte-1b8jye8"),x(t,"target","_blank")},m(i,a){J(i,e,a),J(i,t,a),ee(t,n),J(i,o,a)},p(i,a){a[0]&1024&&s!==(s=i[10]=="python"?Ht+Mt:i[10]=="javascript"?Ft+Mt:hn)&&x(t,"href",s)},d(i){i&&(H(e),H(t),H(o))}}}function fr(l){let e,t,n;return{c(){e=ne("div"),t=we(),n=ne("p"),n.textContent="API Recorder",x(e,"class","loading-dot svelte-1b8jye8"),x(n,"class","self-baseline svelte-1b8jye8")},m(s,o){J(s,e,o),J(s,t,o),J(s,n,o)},p:kt,d(s){s&&(H(e),H(t),H(n))}}}function zl(l){let e,t,n,s,o,i,a,r,c,_,u,f,d,g,m,k,p,w,$,h,v,y,C,A,T=l[4]!==null&&Nl();return{c(){e=ne("br"),t=te(" "),n=ne("br"),s=te(`Making a
								prediction and getting a result requires
								`),o=ne("strong"),o.textContent="2 requests",i=te(`: a
								`),a=ne("code"),a.textContent="POST",r=te(`
								and a `),c=ne("code"),c.textContent="GET",_=te(" request. The "),u=ne("code"),u.textContent="POST",f=te(` request
								returns an `),d=ne("code"),d.textContent="EVENT_ID",g=te(`, which is used in the second
								`),m=ne("code"),m.textContent="GET",k=te(` request to fetch the results. In these
								snippets, we've used `),p=ne("code"),p.textContent="awk",w=te(" and "),$=ne("code"),$.textContent="read",h=te(` to
								parse the results, combining these two requests into one command
								for ease of use. `),T&&T.c(),v=te(` See
								`),y=ne("a"),C=te("curl docs"),A=te("."),x(a,"class","svelte-1b8jye8"),x(c,"class","svelte-1b8jye8"),x(u,"class","svelte-1b8jye8"),x(d,"class","svelte-1b8jye8"),x(m,"class","svelte-1b8jye8"),x(p,"class","svelte-1b8jye8"),x($,"class","svelte-1b8jye8"),x(y,"href",hn),x(y,"target","_blank"),x(y,"class","svelte-1b8jye8")},m(E,b){J(E,e,b),J(E,t,b),J(E,n,b),J(E,s,b),J(E,o,b),J(E,i,b),J(E,a,b),J(E,r,b),J(E,c,b),J(E,_,b),J(E,u,b),J(E,f,b),J(E,d,b),J(E,g,b),J(E,m,b),J(E,k,b),J(E,p,b),J(E,w,b),J(E,$,b),J(E,h,b),T&&T.m(E,b),J(E,v,b),J(E,y,b),ee(y,C),J(E,A,b)},p(E,b){E[4]!==null?T||(T=Nl(),T.c(),T.m(v.parentNode,v)):T&&(T.d(1),T=null)},d(E){E&&(H(e),H(t),H(n),H(s),H(o),H(i),H(a),H(r),H(c),H(_),H(u),H(f),H(d),H(g),H(m),H(k),H(p),H(w),H($),H(h),H(v),H(y),H(A)),T&&T.d(E)}}}function Nl(l){let e;return{c(){e=te(`Note: connecting to an authenticated app requires an
									additional request.`)},m(t,n){J(t,e,n)},d(t){t&&H(e)}}}function Pl(l){let e,t,n=Ut(l[1]),s=[];for(let i=0;i<n.length;i+=1)s[i]=Ol(wl(l,n,i));const o=i=>ae(s[i],1,1,()=>{s[i]=null});return{c(){for(let i=0;i<s.length;i+=1)s[i].c();e=Pt()},m(i,a){for(let r=0;r<s.length;r+=1)s[r]&&s[r].m(i,a);J(i,e,a),t=!0},p(i,a){if(a[0]&13343){n=Ut(i[1]);let r;for(r=0;r<n.length;r+=1){const c=wl(i,n,r);s[r]?(s[r].p(c,a),oe(s[r],1)):(s[r]=Ol(c),s[r].c(),oe(s[r],1),s[r].m(e.parentNode,e))}for(ft(),r=n.length;r<s.length;r+=1)o(r);ut()}},i(i){if(!t){for(let a=0;a<n.length;a+=1)oe(s[a]);t=!0}},o(i){s=s.filter(Boolean);for(let a=0;a<s.length;a+=1)ae(s[a]);t=!1},d(i){i&&H(e),Jl(s,i)}}}function Al(l){let e,t,n,s,o,i,a,r;return t=new Vi({props:{endpoint_parameters:l[12].named_endpoints["/"+l[36].api_name].parameters,dependency:l[36],current_language:l[10],root:l[0],space_id:l[3],username:l[4],api_prefix:l[2].api_prefix,api_description:l[12].named_endpoints["/"+l[36].api_name].description}}),s=new Hs({props:{endpoint_returns:l[12].named_endpoints["/"+l[36].api_name].parameters,js_returns:l[13].named_endpoints["/"+l[36].api_name].parameters,is_running:El,current_language:l[10]}}),i=new qo({props:{endpoint_returns:l[12].named_endpoints["/"+l[36].api_name].returns,js_returns:l[13].named_endpoints["/"+l[36].api_name].returns,is_running:El,current_language:l[10]}}),{c(){e=ne("div"),Be(t.$$.fragment),n=we(),Be(s.$$.fragment),o=we(),Be(i.$$.fragment),a=we(),x(e,"class","endpoint-container svelte-1b8jye8")},m(c,_){J(c,e,_),Ye(t,e,null),ee(e,n),Ye(s,e,null),ee(e,o),Ye(i,e,null),ee(e,a),r=!0},p(c,_){const u={};_[0]&4098&&(u.endpoint_parameters=c[12].named_endpoints["/"+c[36].api_name].parameters),_[0]&2&&(u.dependency=c[36]),_[0]&1024&&(u.current_language=c[10]),_[0]&1&&(u.root=c[0]),_[0]&8&&(u.space_id=c[3]),_[0]&16&&(u.username=c[4]),_[0]&4&&(u.api_prefix=c[2].api_prefix),_[0]&4098&&(u.api_description=c[12].named_endpoints["/"+c[36].api_name].description),t.$set(u);const f={};_[0]&4098&&(f.endpoint_returns=c[12].named_endpoints["/"+c[36].api_name].parameters),_[0]&8194&&(f.js_returns=c[13].named_endpoints["/"+c[36].api_name].parameters),_[0]&1024&&(f.current_language=c[10]),s.$set(f);const d={};_[0]&4098&&(d.endpoint_returns=c[12].named_endpoints["/"+c[36].api_name].returns),_[0]&8194&&(d.js_returns=c[13].named_endpoints["/"+c[36].api_name].returns),_[0]&1024&&(d.current_language=c[10]),i.$set(d)},i(c){r||(oe(t.$$.fragment,c),oe(s.$$.fragment,c),oe(i.$$.fragment,c),r=!0)},o(c){ae(t.$$.fragment,c),ae(s.$$.fragment,c),ae(i.$$.fragment,c),r=!1},d(c){c&&H(e),Ge(t),Ge(s),Ge(i)}}}function Ol(l){let e,t,n=l[36].show_api&&l[12].named_endpoints["/"+l[36].api_name]&&Al(l);return{c(){n&&n.c(),e=Pt()},m(s,o){n&&n.m(s,o),J(s,e,o),t=!0},p(s,o){s[36].show_api&&s[12].named_endpoints["/"+s[36].api_name]?n?(n.p(s,o),o[0]&4098&&oe(n,1)):(n=Al(s),n.c(),oe(n,1),n.m(e.parentNode,e)):n&&(ft(),ae(n,1,1,()=>{n=null}),ut())},i(s){t||(oe(n),t=!0)},o(s){ae(n),t=!1},d(s){s&&H(e),n&&n.d(s)}}}function pr(l){let e,t,n=l[12]&&Sl(l);return{c(){n&&n.c(),e=Pt()},m(s,o){n&&n.m(s,o),J(s,e,o),t=!0},p(s,o){s[12]?n?(n.p(s,o),o[0]&4096&&oe(n,1)):(n=Sl(s),n.c(),oe(n,1),n.m(e.parentNode,e)):n&&(ft(),ae(n,1,1,()=>{n=null}),ut())},i(s){t||(oe(n),t=!0)},o(s){ae(n),t=!1},d(s){s&&H(e),n&&n.d(s)}}}const Ft="https://www.gradio.app/guides/getting-started-with-the-js-client",Ht="https://www.gradio.app/guides/getting-started-with-the-python-client",hn="https://www.gradio.app/guides/querying-gradio-apps-with-curl",Mt="#connecting-to-a-hugging-face-space",dr="https://www.gradio.app/guides/building-mcp-server-with-gradio";let El=!1;function mr(l,e){const t=new URL(window.location.href);t.searchParams.set(l,e),history.replaceState(null,"",t.toString())}function hr(l){return new URL(window.location.href).searchParams.get(l)}function rn(l){return["python","javascript","bash","mcp"].includes(l??"")}function gr(l,e,t){let n,s,o,i,{dependencies:a}=e,{root:r}=e,{app:c}=e,{space_id:_}=e,{root_node:u}=e,{username:f}=e,d=a.filter(j=>j.show_api).length;r===""&&(r=location.protocol+"//"+location.host+location.pathname),r.endsWith("/")||(r+="/");let{api_calls:g=[]}=e,m="python";const k=[["python","Python",oo],["javascript","JavaScript",ro],["bash","cURL",ao],["mcp","MCP",zo]];let p=!1;async function w(){return await(await fetch(r.replace(/\/$/,"")+c.api_prefix+"/info")).json()}async function $(){return await c.view_api()}let h,v;w().then(j=>{t(12,h=j)}),$().then(j=>{t(13,v=j)});const y=sr();let C=[],A=[],T,E,b=!1,S=new Set,z=_?_.split("/").pop()+"_":"";function N(j){return z&&j.startsWith(z)?j.slice(z.length):j}const q={command:"uvx",args:["--from","gradio[mcp]","gradio","upload-mcp",r,"<UPLOAD_DIRECTORY>"]};async function F(){try{let j=`${r}gradio_api/mcp/schema`;const le=await(await fetch(j)).json();t(14,b=le.map(ce=>ce.meta?.file_data_present).some(ce=>ce)),t(6,C=le.map(ce=>({name:ce.name,description:ce.description||"",parameters:ce.inputSchema?.properties||{},expanded:!1}))),t(9,S=new Set(C.map(ce=>ce.name))),A=le.map(ce=>ce.meta?.headers||[]).flat(),A.length>0?(t(7,T={mcpServers:{gradio:{url:o,headers:A.reduce((ce,Vl)=>(ce[Vl]="<YOUR_HEADER_VALUE>",ce),{})}}}),t(8,E={mcpServers:{gradio:{command:"npx",args:["mcp-remote",o,"--transport","sse-only",...A.map(ce=>["--header",`${ce}: <YOUR_HEADER_VALUE>`]).flat()]}}})):(t(7,T={mcpServers:{gradio:{url:o}}}),t(8,E={mcpServers:{gradio:{command:"npx",args:["mcp-remote",o,"--transport","sse-only"]}}}),b&&(t(7,T.mcpServers.upload_files_to_gradio=q,T),t(8,E.mcpServers.upload_files_to_gradio=q,E)))}catch(j){console.error("Failed to fetch MCP tools:",j),t(6,C=[])}}lr(()=>{document.body.style.overflow="hidden","parentIFrame"in window&&window.parentIFrame?.scrollTo(0,0);const j=hr("lang");return rn(j)&&t(10,m=j),fetch(o).then(me=>{t(11,p=me.ok),p?(F(),rn(j)||t(10,m="mcp")):rn(j)||t(10,m="python")}).catch(()=>{t(11,p=!1)}),()=>{document.body.style.overflow="auto"}});function U(j){yl.call(this,l,j)}const Z=j=>{t(10,m=j),mr("lang",j)},K=j=>S.has(j.name);function W(j){S=j,t(9,S)}const Y=()=>y("close",{api_recorder_visible:!0});function P(j){yl.call(this,l,j)}return l.$$set=j=>{"dependencies"in j&&t(1,a=j.dependencies),"root"in j&&t(0,r=j.root),"app"in j&&t(2,c=j.app),"space_id"in j&&t(3,_=j.space_id),"root_node"in j&&t(20,u=j.root_node),"username"in j&&t(4,f=j.username),"api_calls"in j&&t(5,g=j.api_calls)},l.$$.update=()=>{if(l.$$.dirty[0]&512&&t(22,n=Array.from(S)),l.$$.dirty[0]&4194304&&t(21,s=n.map(N)),l.$$.dirty[0]&1&&t(15,o=`${r}gradio_api/mcp/sse`),l.$$.dirty[0]&6291521&&t(16,i=n.length>0&&n.length<C.length?`${r}gradio_api/mcp/?tools=${s.join(",")}`:`${r}gradio_api/mcp/`),l.$$.dirty[0]&6292417&&T&&S.size>0){const j=n.length>0&&n.length<C.length?`${r}gradio_api/mcp/sse?tools=${s.join(",")}`:`${r}gradio_api/mcp/sse`;t(7,T.mcpServers.gradio.url=j,T),E&&t(8,E.mcpServers.gradio.args[1]=j,E)}},[r,a,c,_,f,g,C,T,E,S,m,p,h,v,b,o,i,d,k,y,u,s,n,U,Z,K,W,Y,P]}class Rr extends Zo{constructor(e){super(),xo(this,e,gr,pr,tr,{dependencies:1,root:0,app:2,space_id:3,root_node:20,username:4,api_calls:5},null,[-1,-1])}get dependencies(){return this.$$.ctx[1]}set dependencies(e){this.$$set({dependencies:e}),it()}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),it()}get app(){return this.$$.ctx[2]}set app(e){this.$$set({app:e}),it()}get space_id(){return this.$$.ctx[3]}set space_id(e){this.$$set({space_id:e}),it()}get root_node(){return this.$$.ctx[20]}set root_node(e){this.$$set({root_node:e}),it()}get username(){return this.$$.ctx[4]}set username(e){this.$$set({username:e}),it()}get api_calls(){return this.$$.ctx[5]}set api_calls(e){this.$$set({api_calls:e}),it()}}export{Rr as default};
//# sourceMappingURL=ApiDocs-tDCQqm4K.js.map
