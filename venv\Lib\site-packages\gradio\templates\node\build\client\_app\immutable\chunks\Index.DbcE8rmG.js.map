{"version": 3, "file": "Index.DbcE8rmG.js", "sources": ["../../../../../../../checkboxgroup/Index.svelte"], "sourcesContent": ["<svelte:options immutable={true} />\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { Block, BlockTitle } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t\tinput: never;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: (string | number)[] = [];\n\texport let choices: [string, string | number][];\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let label = gradio.i18n(\"checkbox.checkbox_group\");\n\texport let info: string | undefined = undefined;\n\texport let show_label = true;\n\n\texport let loading_status: LoadingStatus;\n\texport let interactive = true;\n\texport let old_value = value.slice();\n\n\tfunction toggle_choice(choice: string | number): void {\n\t\tif (value.includes(choice)) {\n\t\t\tvalue = value.filter((v) => v !== choice);\n\t\t} else {\n\t\t\tvalue = [...value, choice];\n\t\t}\n\t\tgradio.dispatch(\"input\");\n\t}\n\n\t$: disabled = !interactive;\n\n\t$: if (JSON.stringify(old_value) !== JSON.stringify(value)) {\n\t\told_value = value;\n\t\tgradio.dispatch(\"change\");\n\t}\n</script>\n\n<Block\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\ttype=\"fieldset\"\n\t{container}\n\t{scale}\n\t{min_width}\n>\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\n\n\t<div class=\"wrap\" data-testid=\"checkbox-group\">\n\t\t{#each choices as [display_value, internal_value], i}\n\t\t\t<label class:disabled class:selected={value.includes(internal_value)}>\n\t\t\t\t<input\n\t\t\t\t\t{disabled}\n\t\t\t\t\ton:change={() => toggle_choice(internal_value)}\n\t\t\t\t\ton:input={(evt) =>\n\t\t\t\t\t\tgradio.dispatch(\"select\", {\n\t\t\t\t\t\t\tindex: i,\n\t\t\t\t\t\t\tvalue: internal_value,\n\t\t\t\t\t\t\tselected: evt.currentTarget.checked\n\t\t\t\t\t\t})}\n\t\t\t\t\ton:keydown={(event) => {\n\t\t\t\t\t\tif (event.key === \"Enter\") {\n\t\t\t\t\t\t\ttoggle_choice(internal_value);\n\t\t\t\t\t\t\tgradio.dispatch(\"select\", {\n\t\t\t\t\t\t\t\tindex: i,\n\t\t\t\t\t\t\t\tvalue: internal_value,\n\t\t\t\t\t\t\t\tselected: !value.includes(internal_value)\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}}\n\t\t\t\t\tchecked={value.includes(internal_value)}\n\t\t\t\t\ttype=\"checkbox\"\n\t\t\t\t\tname={internal_value?.toString()}\n\t\t\t\t\ttitle={internal_value?.toString()}\n\t\t\t\t/>\n\t\t\t\t<span class=\"ml-2\">{display_value}</span>\n\t\t\t</label>\n\t\t{/each}\n\t</div>\n</Block>\n\n<style>\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: var(--checkbox-label-gap);\n\t}\n\tlabel {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\ttransition: var(--button-transition);\n\t\tcursor: pointer;\n\t\tbox-shadow: var(--checkbox-label-shadow);\n\t\tborder: var(--checkbox-label-border-width) solid\n\t\t\tvar(--checkbox-label-border-color);\n\t\tborder-radius: var(--checkbox-border-radius);\n\t\tbackground: var(--checkbox-label-background-fill);\n\t\tpadding: var(--checkbox-label-padding);\n\t\tcolor: var(--checkbox-label-text-color);\n\t\tfont-weight: var(--checkbox-label-text-weight);\n\t\tfont-size: var(--checkbox-label-text-size);\n\t\tline-height: var(--line-md);\n\t}\n\n\tlabel:hover {\n\t\tbackground: var(--checkbox-label-background-fill-hover);\n\t}\n\tlabel:focus {\n\t\tbackground: var(--checkbox-label-background-fill-focus);\n\t}\n\tlabel.selected {\n\t\tbackground: var(--checkbox-label-background-fill-selected);\n\t\tcolor: var(--checkbox-label-text-color-selected);\n\t\tborder-color: var(--checkbox-label-border-color-selected);\n\t}\n\n\tlabel > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\tinput {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\tbox-shadow: var(--checkbox-shadow);\n\t\tborder: var(--checkbox-border-width) solid var(--checkbox-border-color);\n\t\tborder-radius: var(--checkbox-border-radius);\n\t\tbackground-color: var(--checkbox-background-color);\n\t\tline-height: var(--line-sm);\n\t}\n\n\tinput:checked,\n\tinput:checked:hover,\n\tinput:checked:focus {\n\t\tborder-color: var(--checkbox-border-color-selected);\n\t\tbackground-image: var(--checkbox-check);\n\t\tbackground-color: var(--checkbox-background-color-selected);\n\t}\n\n\tinput:checked:focus {\n\t\tborder-color: var(--checkbox-border-color-focus);\n\t\tbackground-image: var(--checkbox-check);\n\t\tbackground-color: var(--checkbox-background-color-selected);\n\t}\n\n\tinput:hover {\n\t\tborder-color: var(--checkbox-border-color-hover);\n\t\tbackground-color: var(--checkbox-background-color-hover);\n\t}\n\n\tinput:not(:checked):focus {\n\t\tborder-color: var(--checkbox-border-color-focus);\n\t}\n\n\tinput[disabled],\n\t.disabled {\n\t\tcursor: not-allowed;\n\t}\n\n\tinput:hover {\n\t\tcursor: pointer;\n\t}\n</style>\n"], "names": ["ctx", "attr", "input", "input_name_value", "_a", "input_title_value", "_b", "insert_hydration", "target", "label_1", "anchor", "append_hydration", "span", "dirty", "set_data", "t1", "t1_value", "i", "div", "gradio", "$$props", "elem_id", "elem_classes", "visible", "value", "choices", "container", "scale", "min_width", "label", "info", "show_label", "loading_status", "interactive", "old_value", "toggle_choice", "choice", "$$invalidate", "v", "clear_status_handler", "change_handler", "internal_value", "input_handler", "evt", "event", "disabled"], "mappings": "gvBA8DkCA,EAAK,CAAA,CAAA,cAALA,EAAK,CAAA,CAAA,sCAALA,EAAK,CAAA,CAAA,oDA6BfA,EAAa,EAAA,EAAA,+bALvBA,EAAK,CAAA,EAAC,SAASA,EAAc,EAAA,CAAA,yBAEhCC,EAAAC,EAAA,OAAAC,GAAAC,EAAAJ,QAAA,YAAAI,EAAgB,UAAQ,EACvBH,EAAAC,EAAA,QAAAG,GAAAC,EAAAN,QAAA,YAAAM,EAAgB,UAAQ,sIAvBKN,EAAK,CAAA,EAAC,SAASA,EAAc,EAAA,CAAA,CAAA,UAAnEO,EA0BOC,EAAAC,EAAAC,CAAA,EAzBNC,EAuBCF,EAAAP,CAAA,SACDS,EAAwCF,EAAAG,CAAA,0IAL9BZ,EAAK,CAAA,EAAC,SAASA,EAAc,EAAA,CAAA,kBAEhCa,EAAA,IAAAV,KAAAA,GAAAC,EAAAJ,QAAA,YAAAI,EAAgB,2BACfS,EAAA,IAAAR,KAAAA,GAAAC,EAAAN,QAAA,YAAAM,EAAgB,yCAEJN,EAAa,EAAA,EAAA,KAAAc,EAAAC,EAAAC,CAAA,qDAzBIhB,EAAK,CAAA,EAAC,SAASA,EAAc,EAAA,CAAA,CAAA,qEATxD,CAAA,WAAAA,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,EAAA,mMAMXA,EAAO,CAAA,CAAA,uBAAZ,OAAIiB,GAAA,uaADPV,EA8BKC,EAAAU,EAAAR,CAAA,qFArCQG,EAAA,GAAA,CAAA,WAAAb,KAAO,UAAU,EACvBa,EAAA,GAAA,CAAA,KAAAb,KAAO,IAAI,aACbA,EAAc,EAAA,CAAA,kJAMXA,EAAO,CAAA,CAAA,oBAAZ,OAAIiB,GAAA,EAAA,iHAAJ,quBAzDQ,CAAA,OAAAE,CAAA,EAAAC,GAMA,QAAAC,EAAU,EAAA,EAAAD,EACV,CAAA,aAAAE,EAAA,EAAA,EAAAF,GACA,QAAAG,EAAU,EAAA,EAAAH,EACV,CAAA,MAAAI,EAAA,EAAA,EAAAJ,EACA,CAAA,QAAAK,CAAA,EAAAL,GACA,UAAAM,EAAY,EAAA,EAAAN,GACZ,MAAAO,EAAuB,IAAA,EAAAP,GACvB,UAAAQ,EAAgC,MAAA,EAAAR,GAChC,MAAAS,EAAQV,EAAO,KAAK,yBAAyB,CAAA,EAAAC,GAC7C,KAAAU,EAA2B,MAAA,EAAAV,GAC3B,WAAAW,EAAa,EAAA,EAAAX,EAEb,CAAA,eAAAY,CAAA,EAAAZ,GACA,YAAAa,EAAc,EAAA,EAAAb,EACd,CAAA,UAAAc,EAAYV,EAAM,MAAA,CAAA,EAAAJ,WAEpBe,EAAcC,EAAA,CAClBZ,EAAM,SAASY,CAAM,EACxBC,EAAA,EAAAb,EAAQA,EAAM,OAAQc,GAAMA,IAAMF,CAAM,CAAA,EAExCC,EAAA,EAAAb,EAAA,CAAA,GAAYA,EAAOY,CAAM,CAAA,EAE1BjB,EAAO,SAAS,OAAO,EAwBA,MAAAoB,EAAA,IAAApB,EAAO,SAAS,eAAgBa,CAAc,EASjDQ,EAAAC,GAAAN,EAAcM,CAAc,EAClCC,EAAA,CAAAzB,EAAAwB,EAAAE,IACVxB,EAAO,SAAS,SAAQ,CACvB,MAAOF,EACP,MAAOwB,EACP,SAAUE,EAAI,cAAc,iBAEjBC,IAAK,CACbA,EAAM,MAAQ,UACjBT,EAAcM,CAAc,EAC5BtB,EAAO,SAAS,SAAQ,CACvB,MAAOF,EACP,MAAOwB,EACP,SAAW,CAAAjB,EAAM,SAASiB,CAAc,gkBA3C/CJ,EAAA,GAAGQ,EAAY,CAAAZ,CAAA,oBAER,KAAK,UAAUC,CAAS,IAAM,KAAK,UAAUV,CAAK,SACxDU,EAAYV,CAAA,EACZL,EAAO,SAAS,QAAQ"}