{"version": 3, "mappings": ";6KAaA,MAAMA,EAAqC,YACrCC,EAAiB,EAIjBC,EAAa,CAAC,IAAM,GAAM,IAAM,IAAM,IAAM,IAAM,IAAM,EAAI,EAM3D,SAASC,EAAWC,EAAM,CAC7B,MAAMC,EAAW,IAAI,SAASD,EAAK,OAAQA,EAAK,WAAYA,EAAK,UAAU,EAC3E,IAAIE,EAAM,EACV,QAASC,EAAI,EAAGA,EAAIL,EAAW,OAAQK,IACnC,GAAIF,EAAS,SAASC,GAAK,IAAMJ,EAAWK,CAAC,EACzC,OAAAC,EAAO,MAAM,+BAA+B,EACrC,KAIf,IAAIC,EAAiB,GACjBC,EAAW,EACf,KAAQA,EAAWL,EAAS,SAASC,GAAK,GACtCG,GAAkB,OAAO,aAAaC,CAAQ,EAElD,IAAIC,EAAW,KAAK,MAAMF,CAAc,EACxC,OAAAE,EAAWC,EAAiBD,CAAQ,EAEpCA,EAAS,mBAAqBL,EAC1BK,EAAS,WAETA,EAAS,SAAS,mBAAqBA,EAAS,SAAS,oBAAsB,IAE5EA,CACX,CAOO,SAASC,EAAiBC,EAAM,CACnC,GAAIA,EAAK,QAAUZ,EACf,MAAM,IAAI,MAAM,gDAAgDY,EAAK,OAAO,mCAAmCZ,CAAc,IAAI,EAErI,OAAIY,EAAK,UAAY,IAIrBA,EAAO,CAAE,GAAGA,EAAM,QAAS,EAAG,UAAWb,IAClCa,CACX,CAqNO,SAASC,EAAwCV,EAAMS,EAAM,CAChEA,EAAOD,EAAiBC,CAAI,EAC5B,MAAME,EAAeF,EAAK,SAE1B,IAAIG,EAAe,KAAK,KAAKH,EAAK,KAAK,EAEvC,GADAG,EAAe,KAAK,MAAMA,CAAY,EAAI,EACtCD,EAAa,QAAQ,SAAW,EAAIC,EACpC,MAAM,IAAI,MAAM,wCAAwCD,EAAa,QAAQ,MAAM,GAAG,EAE1F,MAAME,EAAY,IAAI,MAAMD,CAAY,EACxC,QAAST,EAAI,EAAGA,EAAIS,EAAcT,IAAK,CACnCU,EAAUV,CAAC,EAAI,IAAI,MAAM,CAAC,EAC1B,QAASW,EAAO,EAAGA,EAAO,EAAGA,IAAQ,CACjC,MAAMC,EAAYJ,EAAa,QAAQR,EAAI,EAAIW,CAAI,EACnDD,EAAUV,CAAC,EAAEW,CAAI,EAAI,IAAI,WAAWd,EAAK,OAAQA,EAAK,WAAaS,EAAK,mBAAqBM,EAAU,SAAUA,EAAU,MAAM,CACpI,CACJ,CACD,OAAOF,CACX,CAOO,SAASG,EAA0ChB,EAAMS,EAAM,OAClEA,EAAOD,EAAiBC,CAAI,EAC5B,MAAMI,EAAY,IAAI,MAAM,CAAC,EACvBI,GAAoBC,EAAAT,EAAK,aAAL,YAAAS,EAAiB,kBAC3C,GAAID,EAAmB,CACnB,GAAIA,EAAkB,MAAM,SAAW,EACnC,MAAM,IAAI,MAAM,8CAA8CA,EAAkB,MAAM,MAAM,GAAG,EAEnG,QAASH,EAAO,EAAGA,EAAO,EAAGA,IAAQ,CACjC,MAAMC,EAAYE,EAAkB,MAAMH,CAAI,EAC9CD,EAAUC,CAAI,EAAI,IAAI,WAAWd,EAAK,OAAQA,EAAK,WAAaS,EAAK,mBAAqBM,EAAU,SAAUA,EAAU,MAAM,CACjI,CACJ,CACD,OAAOF,CACX,CAQO,SAASM,EAAqBC,EAASpB,EAAMS,EAAM,OACtDA,EAAOD,EAAiBC,CAAI,EAC5B,MAAME,EAAeF,EAAK,SAC1B,GAAI,CAACE,EAED,OAAO,QAAQ,QAAQ,EAAE,EAE7BS,EAAQ,oBAAsBT,EAAa,mBAC3C,MAAMU,EAAW,GACXC,EAAoBZ,EAAwCV,EAAMS,CAAI,EAC5EY,EAAS,KAAKE,EAA0BH,EAASE,EAAmBb,EAAK,SAAS,CAAC,EACnF,MAAMQ,GAAoBC,EAAAT,EAAK,aAAL,YAAAS,EAAiB,kBAC3C,GAAID,EAAmB,CACnB,MAAMO,EAAsBR,EAA0ChB,EAAMS,CAAI,EAChFY,EAAS,KAAKI,EAA4BL,EAASI,EAAqBP,EAAkB,KAAMR,EAAK,SAAS,CAAC,CAClH,CACD,OAAO,QAAQ,IAAIY,CAAQ,CAC/B,CACA,SAASK,EAAmBC,EAAOC,EAAQC,EAAeC,EAAiBC,EAAKjB,EAAMX,EAAG6B,EAAwBC,EAAaC,EAASd,EAAS,CAC5I,OAAO,IAAI,QAAQ,CAACe,EAASC,IAAW,CACpC,GAAIP,EAAe,CACf,MAAMQ,EAAcT,EAAO,cAAc,KAAM,GAAM,GAAM,KAAM,EAAG,KAAOU,GAAY,CACnFF,EAAOE,CAAO,CACjB,EAAEX,CAAK,EACRG,GAAA,MAAAA,EAAiB,0BAA0B,QAASS,GAAW,CAC3DA,EAAO,oBAAoB,IAAM,CAE7BT,EAAgB,8BAAgC,GAChDA,EAAgB,QAAWS,GAAW,CAClCA,EAAO,aAAa,iBAAkBF,CAAW,EACjDE,EAAO,UAAU,QAAS,EAAGX,EAAO,UAAU,sBAAwBD,aAAiB,YAAc,GAAK,CAAC,CACnI,EACyBC,EAAO,OAAO,SAGnBA,EAAO,OAAO,CAAC,EAAE,mBAAmB,aAAa,CAACE,CAAe,EAAGI,EAAS,GAAMpB,EAAMX,CAAC,EAE1FyB,EAAO,0BAAyB,EAChCS,EAAY,QAAO,EACnB,IAAI,gBAAgBN,CAAG,EACvBI,IACpB,CAAiB,CACjB,EACS,KACI,CAGD,GAFAP,EAAO,sBAAsBR,EAASO,EAAOb,EAAMX,CAAC,EAEhD6B,EAAwB,CACxB,MAAMQ,EAAaP,EAAY9B,CAAC,EAC5BqC,GACAZ,EAAO,sBAAsBY,EAAW,SAAUb,EAAOb,EAAM,CAAC,CAEvE,CACDqB,GACH,CACT,CAAK,CACL,CAQO,eAAeZ,EAA0BH,EAASP,EAAW4B,EAAY7C,EAAoC,CAChH,MAAMgC,EAASR,EAAQ,YACvBA,EAAQ,OAAS,EACjBA,EAAQ,KAAO,EACfA,EAAQ,gBAAkB,GAC1BA,EAAQ,iCAAmC,KAC3CQ,EAAO,0BAA0B,EAAGR,CAAO,EAC3C,MAAMsB,EAAmBtB,EAASP,EAAW,GAAM4B,CAAS,EAE5DrB,EAAQ,QAAU,EACtB,CASO,eAAeK,EAA4BkB,EAAa9B,EAAW+B,EAAMH,EAAY7C,EAAoC,CAE5H,MAAMgC,EAASe,EAAY,YACrBvB,EAAU,IAAIyB,EAAgBjB,EAAQ,CAAC,EACvCkB,EAAc,IAAIC,EAAYnB,EAAQR,CAAO,EACnDuB,EAAY,mBAAqBG,EACjC1B,EAAQ,OAAS,GACjBA,EAAQ,OAAS,EACjBA,EAAQ,KAAO,EACfA,EAAQ,gBAAkB,GAC1BA,EAAQ,iCAAmC,KAC3CA,EAAQ,gBAAkB,GAC1BA,EAAQ,MAAQwB,EAChBxB,EAAQ,OAASwB,EACjBhB,EAAO,0BAA0B,EAAGR,CAAO,EAC3C,MAAMsB,EAAmBtB,EAAS,CAACP,CAAS,EAAG,GAAO4B,CAAS,EAC/Db,EAAO,0BAA0BR,CAAO,EAExCA,EAAQ,QAAU,EACtB,CASA,eAAesB,EAAmBtB,EAASP,EAAWmC,EAA2BP,EAAY7C,EAAoC,CAC7H,GAAI,CAACqD,EAAM,gBAAgB7B,EAAQ,KAAK,EACpC,MAAM,IAAI,MAAM,qCAAqC,EAEzD,MAAMR,EAAesC,EAAM9B,EAAQ,KAAK,EAAI,EAEtCQ,EAASR,EAAQ,YACvB,IAAIS,EAAgB,GAChBG,EAAyB,GACzBF,EAAkB,KAClBI,EAAU,KACVD,EAAc,KAClB,MAAMkB,EAAOvB,EAAO,UACfuB,EAAK,WAIAvB,EAAO,UAAU,0CAIlBuB,EAAK,wBAA0BA,EAAK,iCACzCtB,EAAgB,GAChBT,EAAQ,KAAO,GAGV+B,EAAK,oBAAsBA,EAAK,8BACrCtB,EAAgB,GAChBT,EAAQ,KAAO,GAVfS,EAAgB,IAJhBA,EAAgB,GAChBG,EAAyBgB,GAgB7B,IAAII,EAAiB,EACrB,GAAIvB,EACID,EAAO,UACPwB,EAAiB,EACjB,MAAMC,EAAA,WAAO,mCAAuC,+CAGpD,MAAMA,EAAA,WAAO,mCAAmC,8CAGpDvB,EAAkB,IAAIwB,EAAY,aAAc,aAAc,KAAM,KAAM,EAAG,KAAM,EAAG1B,EAAQ,GAAO,OAAWR,EAAQ,KAAM,OAAW,KAAM,GAAO,OAAWgC,CAAc,EAC/KhC,EAAQ,QAAU,GAClBA,EAAQ,QAAU,GAClBc,EAAUN,EAAO,8BAA8BR,EAAQ,MAAO,CAC1D,oBAAqB,GACrB,gBAAiB,GACjB,sBAAuB,GACvB,aAAc,EACd,KAAMA,EAAQ,KACd,OAAQ,CACpB,CAAS,UAGDA,EAAQ,QAAU,GAClBA,EAAQ,QAAU,GAEdY,EAAwB,CAExBC,EAAc,GACd,MAAMsB,EAAQnC,EAAQ,oBAChBoC,EAASpC,EAAQ,qBACvB,QAASjB,EAAI,EAAGA,EAAI,EAAWA,IAAK,CAGhC,MAAMsD,EAAY,EADCtD,EAAK,EAElBuD,EAAcF,EACdG,GAAe/C,EAAe,GAAK2C,EAAQC,EAC3CI,EAAWF,GAAeC,EAAcD,GAAeD,EACvDI,EAAc,KAAK,MAAM,KAAK,IAAI,KAAK,IAAID,EAAU,CAAC,EAAGD,CAAW,CAAC,EAErEG,EAAmB,IAAIjB,EAAgBjB,EAAQ,CAAC,EACtDkC,EAAiB,OAAS,GAC1BA,EAAiB,QAAU,GAC3BA,EAAiB,gBAAkB,GACnClC,EAAO,0BAA0B,EAAGkC,CAAgB,EAEpD,MAAMtB,EAAa,IAAIO,EAAY,IAAI,EAIvC,OAHAP,EAAW,QAAU,GACrBA,EAAW,SAAWsB,EACtB7B,EAAY4B,CAAW,EAAIrB,EACnBrC,EAAC,CACL,IAAK,GACDiB,EAAQ,eAAiBoB,EACzB,MACJ,IAAK,GACDpB,EAAQ,eAAiBoB,EACzB,MACJ,IAAK,GACDpB,EAAQ,gBAAkBoB,EAC1B,KACP,CACJ,CACJ,CAEL,MAAMnB,EAAW,GAEjB,QAAS,EAAI,EAAG,EAAIR,EAAU,OAAQ,IAElC,QAASC,EAAO,EAAGA,EAAO,EAAGA,IAAQ,CAEjC,MAAMiD,EAAQlD,EAAU,CAAC,EAAEC,CAAI,EACzBkD,EAAO,IAAI,KAAK,CAACD,CAAK,EAAG,CAAE,KAAMtB,CAAS,CAAE,EAC5CV,EAAM,IAAI,gBAAgBiC,CAAI,EACpC,IAAIC,EACJ,GAAIrC,EAAO,UAAU,gCACjBqC,EAAUrC,EAAO,kBAAkBoC,EAAM,CAAE,iBAAkB,OAAQ,EAAE,KAAME,GAClExC,EAAmBwC,EAAKtC,EAAQC,EAAeC,EAAiBC,EAAKjB,EAAM,EAAGkB,EAAwBC,EAAaC,EAASd,CAAO,CAC7I,MAEA,CACD,MAAMO,EAAQ,IAAI,MAClBA,EAAM,IAAMI,EAEZkC,EAAU,IAAI,QAAQ,CAAC9B,EAASC,IAAW,CACvCT,EAAM,OAAS,IAAM,CACjBD,EAAmBC,EAAOC,EAAQC,EAAeC,EAAiBC,EAAKjB,EAAM,EAAGkB,EAAwBC,EAAaC,EAASd,CAAO,EAChI,KAAK,IAAMe,GAAS,EACpB,MAAOgC,GAAW,CACnB/B,EAAO+B,CAAM,CACzC,CAAyB,CACzB,EACoBxC,EAAM,QAAWyC,GAAU,CACvBhC,EAAOgC,CAAK,CACpC,CACA,CAAiB,CACJ,CACD/C,EAAS,KAAK4C,CAAO,CACxB,CAIL,GAFA,MAAM,QAAQ,IAAI5C,CAAQ,EAEtBR,EAAU,OAASD,EAAc,CACjC,IAAIZ,EACJ,MAAM4C,EAAO,KAAK,IAAI,EAAGhC,EAAe,EAAIC,EAAU,MAAM,EACtDwD,EAAazB,EAAOA,EAAO,EACjC,OAAQxB,EAAQ,KAAI,CAChB,IAAK,GAAG,CACJpB,EAAO,IAAI,WAAWqE,CAAU,EAChC,KACH,CACD,IAAK,GAAG,CACJrE,EAAO,IAAI,YAAYqE,CAAU,EACjC,KACH,CACD,IAAK,GAAG,CACJrE,EAAO,IAAI,aAAaqE,CAAU,EAClC,KACH,CACJ,CACD,QAASlE,EAAIU,EAAU,OAAQV,EAAIS,EAAcT,IAC7C,QAASW,EAAO,EAAGA,EAAO,EAAGA,IACzBc,EAAO,iCAAgCM,GAAA,YAAAA,EAAS,UAAWd,EAASpB,EAAMc,EAAMX,CAAC,CAG5F,CAED,GAAI+B,EAAS,CACT,MAAMoC,EAAalD,EAAQ,mBAC3BA,EAAQ,mBAAqB,KAC7BQ,EAAO,gBAAgBR,CAAO,EAC9Bc,EAAQ,YAAYd,CAAO,EAC3BA,EAAQ,mBAAqBkD,CAChC,CAEGxC,GACAA,EAAgB,QAAO,EAGvBE,IACIZ,EAAQ,iBAAmBA,EAAQ,gBAAgB,WACnDA,EAAQ,gBAAgB,SAAS,QAAU,IAE3CA,EAAQ,gBAAkBA,EAAQ,eAAe,WACjDA,EAAQ,eAAe,SAAS,QAAU,IAE1CA,EAAQ,gBAAkBA,EAAQ,eAAe,WACjDA,EAAQ,eAAe,SAAS,QAAU,IAGtD,CAMO,SAASmD,EAAmBnD,EAASX,EAAM,CAC9CA,EAAOD,EAAiBC,CAAI,EAC5B,MAAM+D,EAAiB/D,EAAK,WAC5B,GAAI,CAAC+D,EACD,OAEJ,MAAMC,EAAK,IAAIC,EACfC,EAAQ,eAAeH,EAAe,EAAG,EAAGC,EAAG,CAAC,EAChDE,EAAQ,eAAeH,EAAe,EAAG,EAAGC,EAAG,CAAC,EAChDE,EAAQ,eAAeH,EAAe,EAAG,EAAGC,EAAG,CAAC,EAChDE,EAAQ,eAAeH,EAAe,GAAI,EAAGC,EAAG,EAAE,EAClDE,EAAQ,eAAeH,EAAe,GAAI,EAAGC,EAAG,EAAE,EAClDE,EAAQ,eAAeH,EAAe,GAAI,EAAGC,EAAG,EAAE,EAClDE,EAAQ,eAAeH,EAAe,GAAI,EAAGC,EAAG,EAAE,EAClDE,EAAQ,eAAeH,EAAe,GAAI,EAAGC,EAAG,EAAE,EAClDE,EAAQ,eAAeH,EAAe,GAAI,EAAGC,EAAG,EAAE,EAClDrD,EAAQ,qBAAuBqD,CACnC,CAIO,SAASG,EAAiBC,EAAiB7E,EAAM8E,EAAqBC,EAAUC,EAAW,CAC9F,MAAMC,EAAQJ,EACT,UAAW,EACX,qBAAqB,KAAMA,EAAgB,MAAOA,EAAgB,OAAQA,EAAgB,KAAMA,EAAgB,gBAAiBA,EAAgB,QAASA,EAAgB,aAAcA,EAAgB,YAAY,EACnNK,EAAe3D,EAA0B0D,EAAOjF,CAAI,EAAE,KAAK,IAAM6E,CAAe,EACtF,OAAAA,EAAgB,kBAAqBM,IAC1B,CACH,MAAOD,EACP,QAAS,GACT,QAAS,EACrB,GAEIL,EAAgB,QAAU,GAC1BA,EAAgB,sBAAwB7E,EACxC6E,EAAgB,oBAAsBE,EACtCF,EAAgB,qBAAuBG,EACvCH,EAAgB,qBAAuBC,EAChCvD,EAA0BsD,EAAiB7E,CAAI,EAAE,KAAK,KACzD6E,EAAgB,QAAU,GACnBA,EACV,CACL", "names": ["DefaultEnvironmentTextureImageType", "CurrentVersion", "MagicBytes", "GetEnvInfo", "data", "dataView", "pos", "i", "<PERSON><PERSON>", "manifestString", "charCode", "manifest", "normalizeEnvInfo", "info", "CreateRadianceImageDataArrayBufferViews", "specularInfo", "mipmapsCount", "imageData", "face", "imageInfo", "CreateIrradianceImageDataArrayBufferViews", "irradianceTexture", "_a", "UploadEnvLevelsAsync", "texture", "promises", "radianceImageData", "UploadRadianceLevelsAsync", "irradianceImageData", "UploadIrradianceLevelsAsync", "_OnImageReadyAsync", "image", "engine", "expandTexture", "rgbdPostProcess", "url", "generateNonLODTextures", "lodTextures", "cubeRtt", "resolve", "reject", "tempTexture", "message", "effect", "lodTexture", "imageType", "_UploadLevelsAsync", "mainTexture", "size", "InternalTexture", "baseTexture", "BaseTexture", "canGenerateNonLODTextures", "Tools", "ILog2", "caps", "shaderLanguage", "__vitePreload", "PostProcess", "scale", "offset", "roughness", "minLODIndex", "maxLODIndex", "lodIndex", "mipmapIndex", "glTextureFromLod", "bytes", "blob", "promise", "img", "reason", "error", "dataLength", "irradiance", "UploadEnvSpherical", "irradianceInfo", "sp", "SphericalPolynomial", "Vector3", "_UpdateRGBDAsync", "internalTexture", "sphericalPolynomial", "lodScale", "lodOffset", "proxy", "proxyPromise", "_internalTexture"], "ignoreList": [0], "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Misc/environmentTextureTools.js"], "sourcesContent": ["import { Tools } from \"./tools.js\";\nimport { Vector3 } from \"../Maths/math.vector.js\";\nimport { ILog2 } from \"../Maths/math.scalar.functions.js\";\nimport { SphericalPolynomial } from \"../Maths/sphericalPolynomial.js\";\nimport { InternalTexture } from \"../Materials/Textures/internalTexture.js\";\nimport { BaseTexture } from \"../Materials/Textures/baseTexture.js\";\n\nimport { Scene } from \"../scene.js\";\nimport { PostProcess } from \"../PostProcesses/postProcess.js\";\nimport { Logger } from \"../Misc/logger.js\";\nimport { RGBDTextureTools } from \"./rgbdTextureTools.js\";\nimport { DumpDataAsync } from \"../Misc/dumpTools.js\";\nimport \"../Materials/Textures/baseTexture.polynomial.js\";\nconst DefaultEnvironmentTextureImageType = \"image/png\";\nconst CurrentVersion = 2;\n/**\n * Magic number identifying the env file.\n */\nconst MagicBytes = [0x86, 0x16, 0x87, 0x96, 0xf6, 0xd6, 0x96, 0x36];\n/**\n * Gets the environment info from an env file.\n * @param data The array buffer containing the .env bytes.\n * @returns the environment file info (the json header) if successfully parsed, normalized to the latest supported version.\n */\nexport function GetEnvInfo(data) {\n    const dataView = new DataView(data.buffer, data.byteOffset, data.byteLength);\n    let pos = 0;\n    for (let i = 0; i < MagicBytes.length; i++) {\n        if (dataView.getUint8(pos++) !== MagicBytes[i]) {\n            Logger.Error(\"Not a babylon environment map\");\n            return null;\n        }\n    }\n    // Read json manifest - collect characters up to null terminator\n    let manifestString = \"\";\n    let charCode = 0x00;\n    while ((charCode = dataView.getUint8(pos++))) {\n        manifestString += String.fromCharCode(charCode);\n    }\n    let manifest = JSON.parse(manifestString);\n    manifest = normalizeEnvInfo(manifest);\n    // Extend the header with the position of the payload.\n    manifest.binaryDataPosition = pos;\n    if (manifest.specular) {\n        // Fallback to 0.8 exactly if lodGenerationScale is not defined for backward compatibility.\n        manifest.specular.lodGenerationScale = manifest.specular.lodGenerationScale || 0.8;\n    }\n    return manifest;\n}\n/**\n * Normalizes any supported version of the environment file info to the latest version\n * @param info environment file info on any supported version\n * @returns environment file info in the latest supported version\n * @private\n */\nexport function normalizeEnvInfo(info) {\n    if (info.version > CurrentVersion) {\n        throw new Error(`Unsupported babylon environment map version \"${info.version}\". Latest supported version is \"${CurrentVersion}\".`);\n    }\n    if (info.version === 2) {\n        return info;\n    }\n    // Migrate a v1 info to v2\n    info = { ...info, version: 2, imageType: DefaultEnvironmentTextureImageType };\n    return info;\n}\n/**\n * Creates an environment texture from a loaded cube texture.\n * @param texture defines the cube texture to convert in env file\n * @param options options for the conversion process\n * @param options.imageType the mime type for the encoded images, with support for \"image/png\" (default) and \"image/webp\"\n * @param options.imageQuality the image quality of encoded WebP images.\n * @returns a promise containing the environment data if successful.\n */\nexport async function CreateEnvTextureAsync(texture, options = {}) {\n    const internalTexture = texture.getInternalTexture();\n    if (!internalTexture) {\n        return Promise.reject(\"The cube texture is invalid.\");\n    }\n    const engine = internalTexture.getEngine();\n    if (texture.textureType !== 2 &&\n        texture.textureType !== 1 &&\n        texture.textureType !== 0 &&\n        texture.textureType !== 0 &&\n        texture.textureType !== 7 &&\n        texture.textureType !== -1) {\n        return Promise.reject(\"The cube texture should allow HDR (Full Float or Half Float).\");\n    }\n    let textureType = 1;\n    if (!engine.getCaps().textureFloatRender) {\n        textureType = 2;\n        if (!engine.getCaps().textureHalfFloatRender) {\n            return Promise.reject(\"Env texture can only be created when the browser supports half float or full float rendering.\");\n        }\n    }\n    // sphericalPolynomial is lazy loaded so simply accessing it should trigger the computation.\n    texture.sphericalPolynomial;\n    // Lets keep track of the polynomial promise so we can wait for it to be ready before generating the pixels.\n    const sphericalPolynomialPromise = texture.getInternalTexture()?._sphericalPolynomialPromise;\n    const cubeWidth = internalTexture.width;\n    const hostingScene = new Scene(engine);\n    const specularTextures = {};\n    const diffuseTextures = {};\n    // As we are going to readPixels the faces of the cube, make sure the drawing/update commands for the cube texture are fully sent to the GPU in case it is drawn for the first time in this very frame!\n    engine.flushFramebuffer();\n    const imageType = options.imageType ?? DefaultEnvironmentTextureImageType;\n    // Read and collect all mipmaps data from the cube.\n    const mipmapsCount = ILog2(internalTexture.width);\n    for (let i = 0; i <= mipmapsCount; i++) {\n        const faceWidth = Math.pow(2, mipmapsCount - i);\n        // All faces of the cube.\n        for (let face = 0; face < 6; face++) {\n            specularTextures[i * 6 + face] = await _getTextureEncodedData(hostingScene, texture, textureType, face, i, faceWidth, imageType, options.imageQuality);\n        }\n    }\n    // Read and collect all irradiance data from the cube.\n    const irradianceTexture = options.disableIrradianceTexture ? null : texture.irradianceTexture;\n    if (irradianceTexture) {\n        const faceWidth = irradianceTexture.getSize().width;\n        // All faces of the cube.\n        for (let face = 0; face < 6; face++) {\n            diffuseTextures[face] = await _getTextureEncodedData(hostingScene, irradianceTexture, textureType, face, 0, faceWidth, imageType, options.imageQuality);\n        }\n    }\n    // We can delete the hosting scene keeping track of all the creation objects\n    hostingScene.dispose();\n    // Ensure completion of the polynomial creation promise.\n    if (sphericalPolynomialPromise) {\n        await sphericalPolynomialPromise;\n    }\n    // Creates the json header for the env texture\n    const info = {\n        version: CurrentVersion,\n        width: cubeWidth,\n        imageType,\n        irradiance: _CreateEnvTextureIrradiance(texture),\n        specular: {\n            mipmaps: [],\n            lodGenerationScale: texture.lodGenerationScale,\n        },\n    };\n    // Sets the specular image data information\n    let position = 0;\n    for (let i = 0; i <= mipmapsCount; i++) {\n        for (let face = 0; face < 6; face++) {\n            const byteLength = specularTextures[i * 6 + face].byteLength;\n            info.specular.mipmaps.push({\n                length: byteLength,\n                position: position,\n            });\n            position += byteLength;\n        }\n    }\n    // Sets the irradiance image data information\n    if (irradianceTexture) {\n        info.irradiance = info.irradiance || {\n            x: [0, 0, 0],\n            xx: [0, 0, 0],\n            y: [0, 0, 0],\n            yy: [0, 0, 0],\n            z: [0, 0, 0],\n            zz: [0, 0, 0],\n            yz: [0, 0, 0],\n            zx: [0, 0, 0],\n            xy: [0, 0, 0],\n        };\n        info.irradiance.irradianceTexture = {\n            size: irradianceTexture.getSize().width,\n            faces: [],\n        };\n        for (let face = 0; face < 6; face++) {\n            const byteLength = diffuseTextures[face].byteLength;\n            info.irradiance.irradianceTexture.faces.push({\n                length: byteLength,\n                position: position,\n            });\n            position += byteLength;\n        }\n    }\n    // Encode the JSON as an array buffer\n    const infoString = JSON.stringify(info);\n    const infoBuffer = new ArrayBuffer(infoString.length + 1);\n    const infoView = new Uint8Array(infoBuffer); // Limited to ascii subset matching unicode.\n    for (let i = 0, strLen = infoString.length; i < strLen; i++) {\n        infoView[i] = infoString.charCodeAt(i);\n    }\n    // Ends up with a null terminator for easier parsing\n    infoView[infoString.length] = 0x00;\n    // Computes the final required size and creates the storage\n    const totalSize = MagicBytes.length + position + infoBuffer.byteLength;\n    const finalBuffer = new ArrayBuffer(totalSize);\n    const finalBufferView = new Uint8Array(finalBuffer);\n    const dataView = new DataView(finalBuffer);\n    // Copy the magic bytes identifying the file in\n    let pos = 0;\n    for (let i = 0; i < MagicBytes.length; i++) {\n        dataView.setUint8(pos++, MagicBytes[i]);\n    }\n    // Add the json info\n    finalBufferView.set(new Uint8Array(infoBuffer), pos);\n    pos += infoBuffer.byteLength;\n    // Finally inserts the radiance texture data\n    for (let i = 0; i <= mipmapsCount; i++) {\n        for (let face = 0; face < 6; face++) {\n            const dataBuffer = specularTextures[i * 6 + face];\n            finalBufferView.set(new Uint8Array(dataBuffer), pos);\n            pos += dataBuffer.byteLength;\n        }\n    }\n    // Finally inserts the irradiance texture data\n    if (irradianceTexture) {\n        for (let face = 0; face < 6; face++) {\n            const dataBuffer = diffuseTextures[face];\n            finalBufferView.set(new Uint8Array(dataBuffer), pos);\n            pos += dataBuffer.byteLength;\n        }\n    }\n    // Voila\n    return finalBuffer;\n}\n/**\n * Get the texture encoded data from the current texture\n * @internal\n */\nasync function _getTextureEncodedData(hostingScene, texture, textureType, face, i, size, imageType, imageQuality) {\n    let faceData = await texture.readPixels(face, i, undefined, false);\n    if (faceData && faceData.byteLength === faceData.length) {\n        const faceDataFloat = new Float32Array(faceData.byteLength * 4);\n        for (let i = 0; i < faceData.byteLength; i++) {\n            faceDataFloat[i] = faceData[i] / 255;\n            // Gamma to linear\n            faceDataFloat[i] = Math.pow(faceDataFloat[i], 2.2);\n        }\n        faceData = faceDataFloat;\n    }\n    else if (faceData && texture.gammaSpace) {\n        const floatData = faceData;\n        for (let i = 0; i < floatData.length; i++) {\n            // Gamma to linear\n            floatData[i] = Math.pow(floatData[i], 2.2);\n        }\n    }\n    const engine = hostingScene.getEngine();\n    const tempTexture = engine.createRawTexture(faceData, size, size, 5, false, true, 1, null, textureType);\n    await RGBDTextureTools.EncodeTextureToRGBD(tempTexture, hostingScene, textureType);\n    const rgbdEncodedData = await engine._readTexturePixels(tempTexture, size, size);\n    const imageEncodedData = await DumpDataAsync(size, size, rgbdEncodedData, imageType, undefined, false, true, imageQuality);\n    tempTexture.dispose();\n    return imageEncodedData;\n}\n/**\n * Creates a JSON representation of the spherical data.\n * @param texture defines the texture containing the polynomials\n * @returns the JSON representation of the spherical info\n */\nfunction _CreateEnvTextureIrradiance(texture) {\n    const polynmials = texture.sphericalPolynomial;\n    if (polynmials == null) {\n        return null;\n    }\n    return {\n        x: [polynmials.x.x, polynmials.x.y, polynmials.x.z],\n        y: [polynmials.y.x, polynmials.y.y, polynmials.y.z],\n        z: [polynmials.z.x, polynmials.z.y, polynmials.z.z],\n        xx: [polynmials.xx.x, polynmials.xx.y, polynmials.xx.z],\n        yy: [polynmials.yy.x, polynmials.yy.y, polynmials.yy.z],\n        zz: [polynmials.zz.x, polynmials.zz.y, polynmials.zz.z],\n        yz: [polynmials.yz.x, polynmials.yz.y, polynmials.yz.z],\n        zx: [polynmials.zx.x, polynmials.zx.y, polynmials.zx.z],\n        xy: [polynmials.xy.x, polynmials.xy.y, polynmials.xy.z],\n    };\n}\n/**\n * Creates the ArrayBufferViews used for initializing environment texture image data.\n * @param data the image data\n * @param info parameters that determine what views will be created for accessing the underlying buffer\n * @returns the views described by info providing access to the underlying buffer\n */\nexport function CreateRadianceImageDataArrayBufferViews(data, info) {\n    info = normalizeEnvInfo(info);\n    const specularInfo = info.specular;\n    // Double checks the enclosed info\n    let mipmapsCount = Math.log2(info.width);\n    mipmapsCount = Math.round(mipmapsCount) + 1;\n    if (specularInfo.mipmaps.length !== 6 * mipmapsCount) {\n        throw new Error(`Unsupported specular mipmaps number \"${specularInfo.mipmaps.length}\"`);\n    }\n    const imageData = new Array(mipmapsCount);\n    for (let i = 0; i < mipmapsCount; i++) {\n        imageData[i] = new Array(6);\n        for (let face = 0; face < 6; face++) {\n            const imageInfo = specularInfo.mipmaps[i * 6 + face];\n            imageData[i][face] = new Uint8Array(data.buffer, data.byteOffset + info.binaryDataPosition + imageInfo.position, imageInfo.length);\n        }\n    }\n    return imageData;\n}\n/**\n * Creates the ArrayBufferViews used for initializing environment texture image data.\n * @param data the image data\n * @param info parameters that determine what views will be created for accessing the underlying buffer\n * @returns the views described by info providing access to the underlying buffer\n */\nexport function CreateIrradianceImageDataArrayBufferViews(data, info) {\n    info = normalizeEnvInfo(info);\n    const imageData = new Array(6);\n    const irradianceTexture = info.irradiance?.irradianceTexture;\n    if (irradianceTexture) {\n        if (irradianceTexture.faces.length !== 6) {\n            throw new Error(`Incorrect irradiance texture faces number \"${irradianceTexture.faces.length}\"`);\n        }\n        for (let face = 0; face < 6; face++) {\n            const imageInfo = irradianceTexture.faces[face];\n            imageData[face] = new Uint8Array(data.buffer, data.byteOffset + info.binaryDataPosition + imageInfo.position, imageInfo.length);\n        }\n    }\n    return imageData;\n}\n/**\n * Uploads the texture info contained in the env file to the GPU.\n * @param texture defines the internal texture to upload to\n * @param data defines the data to load\n * @param info defines the texture info retrieved through the GetEnvInfo method\n * @returns a promise\n */\nexport function UploadEnvLevelsAsync(texture, data, info) {\n    info = normalizeEnvInfo(info);\n    const specularInfo = info.specular;\n    if (!specularInfo) {\n        // Nothing else parsed so far\n        return Promise.resolve([]);\n    }\n    texture._lodGenerationScale = specularInfo.lodGenerationScale;\n    const promises = [];\n    const radianceImageData = CreateRadianceImageDataArrayBufferViews(data, info);\n    promises.push(UploadRadianceLevelsAsync(texture, radianceImageData, info.imageType));\n    const irradianceTexture = info.irradiance?.irradianceTexture;\n    if (irradianceTexture) {\n        const irradianceImageData = CreateIrradianceImageDataArrayBufferViews(data, info);\n        promises.push(UploadIrradianceLevelsAsync(texture, irradianceImageData, irradianceTexture.size, info.imageType));\n    }\n    return Promise.all(promises);\n}\nfunction _OnImageReadyAsync(image, engine, expandTexture, rgbdPostProcess, url, face, i, generateNonLODTextures, lodTextures, cubeRtt, texture) {\n    return new Promise((resolve, reject) => {\n        if (expandTexture) {\n            const tempTexture = engine.createTexture(null, true, true, null, 1, null, (message) => {\n                reject(message);\n            }, image);\n            rgbdPostProcess?.onEffectCreatedObservable.addOnce((effect) => {\n                effect.executeWhenCompiled(() => {\n                    // Uncompress the data to a RTT\n                    rgbdPostProcess.externalTextureSamplerBinding = true;\n                    rgbdPostProcess.onApply = (effect) => {\n                        effect._bindTexture(\"textureSampler\", tempTexture);\n                        effect.setFloat2(\"scale\", 1, engine._features.needsInvertingBitmap && image instanceof ImageBitmap ? -1 : 1);\n                    };\n                    if (!engine.scenes.length) {\n                        return;\n                    }\n                    engine.scenes[0].postProcessManager.directRender([rgbdPostProcess], cubeRtt, true, face, i);\n                    // Cleanup\n                    engine.restoreDefaultFramebuffer();\n                    tempTexture.dispose();\n                    URL.revokeObjectURL(url);\n                    resolve();\n                });\n            });\n        }\n        else {\n            engine._uploadImageToTexture(texture, image, face, i);\n            // Upload the face to the non lod texture support\n            if (generateNonLODTextures) {\n                const lodTexture = lodTextures[i];\n                if (lodTexture) {\n                    engine._uploadImageToTexture(lodTexture._texture, image, face, 0);\n                }\n            }\n            resolve();\n        }\n    });\n}\n/**\n * Uploads the levels of image data to the GPU.\n * @param texture defines the internal texture to upload to\n * @param imageData defines the array buffer views of image data [mipmap][face]\n * @param imageType the mime type of the image data\n * @returns a promise\n */\nexport async function UploadRadianceLevelsAsync(texture, imageData, imageType = DefaultEnvironmentTextureImageType) {\n    const engine = texture.getEngine();\n    texture.format = 5;\n    texture.type = 0;\n    texture.generateMipMaps = true;\n    texture._cachedAnisotropicFilteringLevel = null;\n    engine.updateTextureSamplingMode(3, texture);\n    await _UploadLevelsAsync(texture, imageData, true, imageType);\n    // Flag internal texture as ready in case they are in use.\n    texture.isReady = true;\n}\n/**\n * Uploads the levels of image data to the GPU.\n * @param mainTexture defines the internal texture to upload to\n * @param imageData defines the array buffer views of image data [mipmap][face]\n * @param size defines the size of the texture faces\n * @param imageType the mime type of the image data\n * @returns a promise\n */\nexport async function UploadIrradianceLevelsAsync(mainTexture, imageData, size, imageType = DefaultEnvironmentTextureImageType) {\n    // Gets everything ready.\n    const engine = mainTexture.getEngine();\n    const texture = new InternalTexture(engine, 5 /* InternalTextureSource.RenderTarget */);\n    const baseTexture = new BaseTexture(engine, texture);\n    mainTexture._irradianceTexture = baseTexture;\n    texture.isCube = true;\n    texture.format = 5;\n    texture.type = 0;\n    texture.generateMipMaps = true;\n    texture._cachedAnisotropicFilteringLevel = null;\n    texture.generateMipMaps = true;\n    texture.width = size;\n    texture.height = size;\n    engine.updateTextureSamplingMode(3, texture);\n    await _UploadLevelsAsync(texture, [imageData], false, imageType);\n    engine.generateMipMapsForCubemap(texture);\n    // Flag internal texture as ready in case they are in use.\n    texture.isReady = true;\n}\n/**\n * Uploads the levels of image data to the GPU.\n * @param texture defines the internal texture to upload to\n * @param imageData defines the array buffer views of image data [mipmap][face]\n * @param canGenerateNonLODTextures defines whether or not to generate non lod textures\n * @param imageType the mime type of the image data\n * @returns a promise\n */\nasync function _UploadLevelsAsync(texture, imageData, canGenerateNonLODTextures, imageType = DefaultEnvironmentTextureImageType) {\n    if (!Tools.IsExponentOfTwo(texture.width)) {\n        throw new Error(\"Texture size must be a power of two\");\n    }\n    const mipmapsCount = ILog2(texture.width) + 1;\n    // Gets everything ready.\n    const engine = texture.getEngine();\n    let expandTexture = false;\n    let generateNonLODTextures = false;\n    let rgbdPostProcess = null;\n    let cubeRtt = null;\n    let lodTextures = null;\n    const caps = engine.getCaps();\n    if (!caps.textureLOD) {\n        expandTexture = false;\n        generateNonLODTextures = canGenerateNonLODTextures;\n    }\n    else if (!engine._features.supportRenderAndCopyToLodForFloatTextures) {\n        expandTexture = false;\n    }\n    // If half float available we can uncompress the texture\n    else if (caps.textureHalfFloatRender && caps.textureHalfFloatLinearFiltering) {\n        expandTexture = true;\n        texture.type = 2;\n    }\n    // If full float available we can uncompress the texture\n    else if (caps.textureFloatRender && caps.textureFloatLinearFiltering) {\n        expandTexture = true;\n        texture.type = 1;\n    }\n    // Expand the texture if possible\n    let shaderLanguage = 0 /* ShaderLanguage.GLSL */;\n    if (expandTexture) {\n        if (engine.isWebGPU) {\n            shaderLanguage = 1 /* ShaderLanguage.WGSL */;\n            await import(\"../ShadersWGSL/rgbdDecode.fragment.js\");\n        }\n        else {\n            await import(\"../Shaders/rgbdDecode.fragment.js\");\n        }\n        // Simply run through the decode PP\n        rgbdPostProcess = new PostProcess(\"rgbdDecode\", \"rgbdDecode\", null, null, 1, null, 3, engine, false, undefined, texture.type, undefined, null, false, undefined, shaderLanguage);\n        texture._isRGBD = false;\n        texture.invertY = false;\n        cubeRtt = engine.createRenderTargetCubeTexture(texture.width, {\n            generateDepthBuffer: false,\n            generateMipMaps: true,\n            generateStencilBuffer: false,\n            samplingMode: 3,\n            type: texture.type,\n            format: 5,\n        });\n    }\n    else {\n        texture._isRGBD = true;\n        texture.invertY = true;\n        // In case of missing support, applies the same patch than DDS files.\n        if (generateNonLODTextures) {\n            const mipSlices = 3;\n            lodTextures = {};\n            const scale = texture._lodGenerationScale;\n            const offset = texture._lodGenerationOffset;\n            for (let i = 0; i < mipSlices; i++) {\n                //compute LOD from even spacing in smoothness (matching shader calculation)\n                const smoothness = i / (mipSlices - 1);\n                const roughness = 1 - smoothness;\n                const minLODIndex = offset; // roughness = 0\n                const maxLODIndex = (mipmapsCount - 1) * scale + offset; // roughness = 1 (mipmaps start from 0)\n                const lodIndex = minLODIndex + (maxLODIndex - minLODIndex) * roughness;\n                const mipmapIndex = Math.round(Math.min(Math.max(lodIndex, 0), maxLODIndex));\n                //compute LOD from even spacing in smoothness (matching shader calculation)\n                const glTextureFromLod = new InternalTexture(engine, 2 /* InternalTextureSource.Temp */);\n                glTextureFromLod.isCube = true;\n                glTextureFromLod.invertY = true;\n                glTextureFromLod.generateMipMaps = false;\n                engine.updateTextureSamplingMode(2, glTextureFromLod);\n                // Wrap in a base texture for easy binding.\n                const lodTexture = new BaseTexture(null);\n                lodTexture._isCube = true;\n                lodTexture._texture = glTextureFromLod;\n                lodTextures[mipmapIndex] = lodTexture;\n                switch (i) {\n                    case 0:\n                        texture._lodTextureLow = lodTexture;\n                        break;\n                    case 1:\n                        texture._lodTextureMid = lodTexture;\n                        break;\n                    case 2:\n                        texture._lodTextureHigh = lodTexture;\n                        break;\n                }\n            }\n        }\n    }\n    const promises = [];\n    // All mipmaps up to provided number of images\n    for (let i = 0; i < imageData.length; i++) {\n        // All faces\n        for (let face = 0; face < 6; face++) {\n            // Constructs an image element from image data\n            const bytes = imageData[i][face];\n            const blob = new Blob([bytes], { type: imageType });\n            const url = URL.createObjectURL(blob);\n            let promise;\n            if (engine._features.forceBitmapOverHTMLImageElement) {\n                promise = engine.createImageBitmap(blob, { premultiplyAlpha: \"none\" }).then((img) => {\n                    return _OnImageReadyAsync(img, engine, expandTexture, rgbdPostProcess, url, face, i, generateNonLODTextures, lodTextures, cubeRtt, texture);\n                });\n            }\n            else {\n                const image = new Image();\n                image.src = url;\n                // Enqueue promise to upload to the texture.\n                promise = new Promise((resolve, reject) => {\n                    image.onload = () => {\n                        _OnImageReadyAsync(image, engine, expandTexture, rgbdPostProcess, url, face, i, generateNonLODTextures, lodTextures, cubeRtt, texture)\n                            .then(() => resolve())\n                            .catch((reason) => {\n                            reject(reason);\n                        });\n                    };\n                    image.onerror = (error) => {\n                        reject(error);\n                    };\n                });\n            }\n            promises.push(promise);\n        }\n    }\n    await Promise.all(promises);\n    // Fill remaining mipmaps with black textures.\n    if (imageData.length < mipmapsCount) {\n        let data;\n        const size = Math.pow(2, mipmapsCount - 1 - imageData.length);\n        const dataLength = size * size * 4;\n        switch (texture.type) {\n            case 0: {\n                data = new Uint8Array(dataLength);\n                break;\n            }\n            case 2: {\n                data = new Uint16Array(dataLength);\n                break;\n            }\n            case 1: {\n                data = new Float32Array(dataLength);\n                break;\n            }\n        }\n        for (let i = imageData.length; i < mipmapsCount; i++) {\n            for (let face = 0; face < 6; face++) {\n                engine._uploadArrayBufferViewToTexture(cubeRtt?.texture || texture, data, face, i);\n            }\n        }\n    }\n    // Release temp RTT.\n    if (cubeRtt) {\n        const irradiance = texture._irradianceTexture;\n        texture._irradianceTexture = null;\n        engine._releaseTexture(texture);\n        cubeRtt._swapAndDie(texture);\n        texture._irradianceTexture = irradiance;\n    }\n    // Release temp Post Process.\n    if (rgbdPostProcess) {\n        rgbdPostProcess.dispose();\n    }\n    // Flag internal texture as ready in case they are in use.\n    if (generateNonLODTextures) {\n        if (texture._lodTextureHigh && texture._lodTextureHigh._texture) {\n            texture._lodTextureHigh._texture.isReady = true;\n        }\n        if (texture._lodTextureMid && texture._lodTextureMid._texture) {\n            texture._lodTextureMid._texture.isReady = true;\n        }\n        if (texture._lodTextureLow && texture._lodTextureLow._texture) {\n            texture._lodTextureLow._texture.isReady = true;\n        }\n    }\n}\n/**\n * Uploads spherical polynomials information to the texture.\n * @param texture defines the texture we are trying to upload the information to\n * @param info defines the environment texture info retrieved through the GetEnvInfo method\n */\nexport function UploadEnvSpherical(texture, info) {\n    info = normalizeEnvInfo(info);\n    const irradianceInfo = info.irradiance;\n    if (!irradianceInfo) {\n        return;\n    }\n    const sp = new SphericalPolynomial();\n    Vector3.FromArrayToRef(irradianceInfo.x, 0, sp.x);\n    Vector3.FromArrayToRef(irradianceInfo.y, 0, sp.y);\n    Vector3.FromArrayToRef(irradianceInfo.z, 0, sp.z);\n    Vector3.FromArrayToRef(irradianceInfo.xx, 0, sp.xx);\n    Vector3.FromArrayToRef(irradianceInfo.yy, 0, sp.yy);\n    Vector3.FromArrayToRef(irradianceInfo.zz, 0, sp.zz);\n    Vector3.FromArrayToRef(irradianceInfo.yz, 0, sp.yz);\n    Vector3.FromArrayToRef(irradianceInfo.zx, 0, sp.zx);\n    Vector3.FromArrayToRef(irradianceInfo.xy, 0, sp.xy);\n    texture._sphericalPolynomial = sp;\n}\n/**\n * @internal\n */\nexport function _UpdateRGBDAsync(internalTexture, data, sphericalPolynomial, lodScale, lodOffset) {\n    const proxy = internalTexture\n        .getEngine()\n        .createRawCubeTexture(null, internalTexture.width, internalTexture.format, internalTexture.type, internalTexture.generateMipMaps, internalTexture.invertY, internalTexture.samplingMode, internalTexture._compression);\n    const proxyPromise = UploadRadianceLevelsAsync(proxy, data).then(() => internalTexture);\n    internalTexture.onRebuildCallback = (_internalTexture) => {\n        return {\n            proxy: proxyPromise,\n            isReady: true,\n            isAsync: true,\n        };\n    };\n    internalTexture._source = 13 /* InternalTextureSource.CubeRawRGBD */;\n    internalTexture._bufferViewArrayArray = data;\n    internalTexture._lodGenerationScale = lodScale;\n    internalTexture._lodGenerationOffset = lodOffset;\n    internalTexture._sphericalPolynomial = sphericalPolynomial;\n    return UploadRadianceLevelsAsync(internalTexture, data).then(() => {\n        internalTexture.isReady = true;\n        return internalTexture;\n    });\n}\n/**\n * Sets of helpers addressing the serialization and deserialization of environment texture\n * stored in a BabylonJS env file.\n * Those files are usually stored as .env files.\n */\nexport const EnvironmentTextureTools = {\n    /**\n     * Gets the environment info from an env file.\n     * @param data The array buffer containing the .env bytes.\n     * @returns the environment file info (the json header) if successfully parsed, normalized to the latest supported version.\n     */\n    GetEnvInfo,\n    /**\n     * Creates an environment texture from a loaded cube texture.\n     * @param texture defines the cube texture to convert in env file\n     * @param options options for the conversion process\n     * @param options.imageType the mime type for the encoded images, with support for \"image/png\" (default) and \"image/webp\"\n     * @param options.imageQuality the image quality of encoded WebP images.\n     * @returns a promise containing the environment data if successful.\n     */\n    CreateEnvTextureAsync,\n    /**\n     * Creates the ArrayBufferViews used for initializing environment texture image data.\n     * @param data the image data\n     * @param info parameters that determine what views will be created for accessing the underlying buffer\n     * @returns the views described by info providing access to the underlying buffer\n     */\n    CreateRadianceImageDataArrayBufferViews,\n    /**\n     * Creates the ArrayBufferViews used for initializing environment texture image data.\n     * @param data the image data\n     * @param info parameters that determine what views will be created for accessing the underlying buffer\n     * @returns the views described by info providing access to the underlying buffer\n     */\n    CreateIrradianceImageDataArrayBufferViews,\n    /**\n     * Uploads the texture info contained in the env file to the GPU.\n     * @param texture defines the internal texture to upload to\n     * @param data defines the data to load\n     * @param info defines the texture info retrieved through the GetEnvInfo method\n     * @returns a promise\n     */\n    UploadEnvLevelsAsync,\n    /**\n     * Uploads the levels of image data to the GPU.\n     * @param texture defines the internal texture to upload to\n     * @param imageData defines the array buffer views of image data [mipmap][face]\n     * @param imageType the mime type of the image data\n     * @returns a promise\n     */\n    UploadRadianceLevelsAsync,\n    /**\n     * Uploads the levels of image data to the GPU.\n     * @param texture defines the internal texture to upload to\n     * @param imageData defines the array buffer views of image data [mipmap][face]\n     * @param imageType the mime type of the image data\n     * @returns a promise\n     */\n    UploadIrradianceLevelsAsync,\n    /**\n     * Uploads spherical polynomials information to the texture.\n     * @param texture defines the texture we are trying to upload the information to\n     * @param info defines the environment texture info retrieved through the GetEnvInfo method\n     */\n    UploadEnvSpherical,\n};\n//# sourceMappingURL=environmentTextureTools.js.map"], "file": "_app/immutable/chunks/environmentTextureTools.DZJEvUsQ.js"}