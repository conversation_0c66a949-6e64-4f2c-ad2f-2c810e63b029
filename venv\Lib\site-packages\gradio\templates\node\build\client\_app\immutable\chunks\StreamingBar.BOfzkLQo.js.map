{"version": 3, "file": "StreamingBar.BOfzkLQo.js", "sources": ["../../../../../../../statustracker/static/StreamingBar.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let time_limit: number | null;\n</script>\n\n{#if time_limit}\n\t<div class=\"streaming-bar\" style:animation-duration=\"{time_limit}s\"></div>\n{/if}\n\n<style>\n\t.streaming-bar {\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\theight: 4px;\n\t\tbackground-color: var(--primary-600);\n\t\tanimation: countdown linear forwards;\n\t\tz-index: 1;\n\t}\n\n\t@keyframes countdown {\n\t\tfrom {\n\t\t\ttransform: translateX(0%);\n\t\t}\n\t\tto {\n\t\t\ttransform: translateX(-100%);\n\t\t}\n\t}\n</style>\n"], "names": ["ctx", "insert_hydration", "target", "div", "anchor", "create_if_block", "time_limit", "$$props"], "mappings": "sTAKuDA,EAAU,CAAA,CAAA,8KAAhEC,EAAyEC,EAAAC,EAAAC,CAAA,yBAAnBJ,EAAU,CAAA,CAAA,wEAD5DA,EAAU,CAAA,GAAAK,EAAAL,CAAA,yFAAVA,EAAU,CAAA,uHAHH,GAAA,CAAA,WAAAM,CAAA,EAAAC"}