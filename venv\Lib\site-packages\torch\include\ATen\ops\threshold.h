#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/threshold_ops.h>

namespace at {


// aten::threshold(Tensor self, Scalar threshold, Scalar value) -> Tensor
inline at::Tensor threshold(const at::Tensor & self, const at::Scalar & threshold, const at::Scalar & value) {
    return at::_ops::threshold::call(self, threshold, value);
}

// aten::threshold_(Tensor(a!) self, Scalar threshold, Scalar value) -> Tensor(a!)
inline at::Tensor & threshold_(at::Tensor & self, const at::Scalar & threshold, const at::Scalar & value) {
    return at::_ops::threshold_::call(self, threshold, value);
}

// aten::threshold.out(Tensor self, Scalar threshold, Scalar value, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & threshold_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & threshold, const at::Scalar & value) {
    return at::_ops::threshold_out::call(self, threshold, value, out);
}
// aten::threshold.out(Tensor self, Scalar threshold, Scalar value, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & threshold_outf(const at::Tensor & self, const at::Scalar & threshold, const at::Scalar & value, at::Tensor & out) {
    return at::_ops::threshold_out::call(self, threshold, value, out);
}

}
