{"version": 3, "file": "frontmatter-CX9oWgfC.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/frontmatter.js"], "sourcesContent": ["import{s,t as o,f as m,c as l,p as i,S as f}from\"./Index16.js\";import{yaml as p}from\"./yaml.js\";const n=/^---\\s*$/m,F={defineNodes:[{name:\"Frontmatter\",block:!0},\"FrontmatterMark\"],props:[s({Frontmatter:[o.documentMeta,o.monospace],FrontmatterMark:o.processingInstruction}),m.add({Frontmatter:l,FrontmatterMark:()=>null})],wrap:i(t=>{const{parser:e}=f.define(p);return t.type.name===\"Frontmatter\"?{parser:e,overlay:[{from:t.from+4,to:t.to-4}]}:null}),parseBlock:[{name:\"Frontmatter\",before:\"HorizontalRule\",parse:(t,e)=>{let r;const a=new Array;if(t.lineStart===0&&n.test(e.text)){for(a.push(t.elt(\"FrontmatterMark\",0,4));t.nextLine();)if(n.test(e.text)){r=t.lineStart+4;break}return r!==void 0&&(a.push(t.elt(\"FrontmatterMark\",r-4,r)),t.addElement(t.elt(\"Frontmatter\",0,r,a))),!0}return!1}}]};export{F as frontmatter};\n//# sourceMappingURL=frontmatter.js.map\n"], "names": ["s", "o", "m", "l", "i", "f", "p"], "mappings": ";;;;;;;;;;;AAAqG,MAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,KAAK,CAAC,CAACA,EAAC,CAAC,CAAC,WAAW,CAAC,CAACC,CAAC,CAAC,YAAY,CAACA,CAAC,CAAC,SAAS,CAAC,CAAC,eAAe,CAACA,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAACC,EAAC,CAAC,GAAG,CAAC,CAAC,WAAW,CAACC,EAAC,CAAC,eAAe,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAACC,EAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAACC,EAAC,CAAC,MAAM,CAACC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,CAAC;;;;"}