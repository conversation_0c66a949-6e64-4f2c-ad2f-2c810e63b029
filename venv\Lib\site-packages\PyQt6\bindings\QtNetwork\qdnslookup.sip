// qdnslookup.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDnsDomainNameRecord
{
%TypeHeaderCode
#include <qdnslookup.h>
%End

public:
    QDnsDomainNameRecord();
    QDnsDomainNameRecord(const QDnsDomainNameRecord &other);
    ~QDnsDomainNameRecord();
    void swap(QDnsDomainNameRecord &other /Constrained/);
    QString name() const;
    quint32 timeToLive() const;
    QString value() const;
};

class QDnsHostAddressRecord
{
%TypeHeaderCode
#include <qdnslookup.h>
%End

public:
    QDnsHostAddressRecord();
    QDnsHostAddressRecord(const QDnsHostAddressRecord &other);
    ~QDnsHostAddressRecord();
    void swap(QDnsHostAddressRecord &other /Constrained/);
    QString name() const;
    quint32 timeToLive() const;
    QHostAddress value() const;
};

class QDnsMailExchangeRecord
{
%TypeHeaderCode
#include <qdnslookup.h>
%End

public:
    QDnsMailExchangeRecord();
    QDnsMailExchangeRecord(const QDnsMailExchangeRecord &other);
    ~QDnsMailExchangeRecord();
    void swap(QDnsMailExchangeRecord &other /Constrained/);
    QString exchange() const;
    QString name() const;
    quint16 preference() const;
    quint32 timeToLive() const;
};

class QDnsServiceRecord
{
%TypeHeaderCode
#include <qdnslookup.h>
%End

public:
    QDnsServiceRecord();
    QDnsServiceRecord(const QDnsServiceRecord &other);
    ~QDnsServiceRecord();
    void swap(QDnsServiceRecord &other /Constrained/);
    QString name() const;
    quint16 port() const;
    quint16 priority() const;
    QString target() const;
    quint32 timeToLive() const;
    quint16 weight() const;
};

class QDnsTextRecord
{
%TypeHeaderCode
#include <qdnslookup.h>
%End

public:
    QDnsTextRecord();
    QDnsTextRecord(const QDnsTextRecord &other);
    ~QDnsTextRecord();
    void swap(QDnsTextRecord &other /Constrained/);
    QString name() const;
    quint32 timeToLive() const;
    QList<QByteArray> values() const;
};

%If (Qt_6_8_0 -)

class QDnsTlsAssociationRecord
{
%TypeHeaderCode
#include <qdnslookup.h>
%End

public:
    enum class CertificateUsage
    {
        CertificateAuthorityConstrait,
        ServiceCertificateConstraint,
        TrustAnchorAssertion,
        DomainIssuedCertificate,
        PrivateUse,
        PKIX_TA,
        PKIX_EE,
        DANE_TA,
        DANE_EE,
        PrivCert,
    };

    enum class Selector
    {
        FullCertificate,
        SubjectPublicKeyInfo,
        PrivateUse,
        Cert,
        SPKI,
        PrivSel,
    };

    enum class MatchingType
    {
        Exact,
        Sha256,
        Sha512,
        PrivateUse,
        PrivMatch,
    };

    QDnsTlsAssociationRecord();
    QDnsTlsAssociationRecord(const QDnsTlsAssociationRecord &other);
    ~QDnsTlsAssociationRecord();
    void swap(QDnsTlsAssociationRecord &other /Constrained/);
    QString name() const;
    quint32 timeToLive() const;
    QDnsTlsAssociationRecord::CertificateUsage usage() const;
    QDnsTlsAssociationRecord::Selector selector() const;
    QDnsTlsAssociationRecord::MatchingType matchType() const;
    QByteArray value() const;
};

%End

class QDnsLookup : public QObject
{
%TypeHeaderCode
#include <qdnslookup.h>
%End

public:
    enum Error
    {
        NoError,
        ResolverError,
        OperationCancelledError,
        InvalidRequestError,
        InvalidReplyError,
        ServerFailureError,
        ServerRefusedError,
        NotFoundError,
%If (Qt_6_6_0 -)
        TimeoutError,
%End
    };

%If (Qt_6_8_0 -)

    enum Protocol
    {
        Standard,
        DnsOverTls,
    };

%End

    enum Type
    {
        A,
        AAAA,
        ANY,
        CNAME,
        MX,
        NS,
        PTR,
        SRV,
        TXT,
%If (Qt_6_8_0 -)
        TLSA,
%End
    };

    explicit QDnsLookup(QObject *parent /TransferThis/ = 0);
    QDnsLookup(QDnsLookup::Type type, const QString &name, QObject *parent /TransferThis/ = 0);
    QDnsLookup(QDnsLookup::Type type, const QString &name, const QHostAddress &nameserver, QObject *parent /TransferThis/ = 0);
%If (Qt_6_6_0 -)
    QDnsLookup(QDnsLookup::Type type, const QString &name, const QHostAddress &nameserver, quint16 port, QObject *parent /TransferThis/ = 0);
%End
%If (Qt_6_8_0 -)
    QDnsLookup(QDnsLookup::Type type, const QString &name, QDnsLookup::Protocol protocol, const QHostAddress &nameserver, quint16 port = 0, QObject *parent /TransferThis/ = 0);
%End
    virtual ~QDnsLookup();
    QDnsLookup::Error error() const;
    QString errorString() const;
    bool isFinished() const;
    QString name() const;
    void setName(const QString &name);
    QDnsLookup::Type type() const;
    void setType(QDnsLookup::Type);
    QList<QDnsDomainNameRecord> canonicalNameRecords() const;
    QList<QDnsHostAddressRecord> hostAddressRecords() const;
    QList<QDnsMailExchangeRecord> mailExchangeRecords() const;
    QList<QDnsDomainNameRecord> nameServerRecords() const;
    QList<QDnsDomainNameRecord> pointerRecords() const;
    QList<QDnsServiceRecord> serviceRecords() const;
    QList<QDnsTextRecord> textRecords() const;

public slots:
    void abort() /ReleaseGIL/;
    void lookup() /ReleaseGIL/;

signals:
    void finished();
    void nameChanged(const QString &name);
%If (Qt_6_8_0 -)
    void typeChanged(QDnsLookup::Type type);
%End
%If (- Qt_6_8_0)
    void typeChanged(QDnsLookup::Type type /ScopesStripped=1/);
%End

public:
    QHostAddress nameserver() const;
    void setNameserver(const QHostAddress &nameserver);
%If (Qt_6_6_0 -)
    void setNameserver(const QHostAddress &nameserver, quint16 port);
%End
%If (Qt_6_8_0 -)
    void setNameserver(QDnsLookup::Protocol protocol, const QHostAddress &nameserver, quint16 port = 0);
%End

signals:
    void nameserverChanged(const QHostAddress &nameserver);

public:
%If (Qt_6_6_0 -)
    quint16 nameserverPort() const;
%End
%If (Qt_6_6_0 -)
    void setNameserverPort(quint16 port);
%End

signals:
%If (Qt_6_6_0 -)
    void nameserverPortChanged(quint16 port);
%End

public:
%If (Qt_6_8_0 -)
    bool isAuthenticData() const;
%End
%If (Qt_6_8_0 -)
    QDnsLookup::Protocol nameserverProtocol() const;
%End
%If (Qt_6_8_0 -)
    void setNameserverProtocol(QDnsLookup::Protocol protocol);
%End
%If (Qt_6_8_0 -)
    QList<QDnsTlsAssociationRecord> tlsAssociationRecords() const;
%End
%If (Qt_6_8_0 -)
%If (PyQt_SSL)
    void setSslConfiguration(const QSslConfiguration &sslConfiguration);
%End
%End
%If (Qt_6_8_0 -)
%If (PyQt_SSL)
    QSslConfiguration sslConfiguration() const;
%End
%End
%If (Qt_6_8_0 -)
    static bool isProtocolSupported(QDnsLookup::Protocol protocol);
%End
%If (Qt_6_8_0 -)
    static quint16 defaultPortForProtocol(QDnsLookup::Protocol protocol);
%End

signals:
%If (Qt_6_8_0 -)
    void nameserverProtocolChanged(QDnsLookup::Protocol protocol);
%End
};
