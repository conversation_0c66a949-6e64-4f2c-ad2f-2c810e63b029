{"version": 3, "file": "Index60-DixF-vjB.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Index60.js"], "sourcesContent": ["import{create_ssr_component as M,validate_component as C,each as ot,add_styles as Ct,escape as I,missing_component as lt,add_attribute as k,null_to_empty as Et}from\"svelte/internal\";import\"./client.js\";import{I as rt}from\"./Image.js\";import\"./ImagePreview.js\";import{a as D,C as yt,I as kt,Z as Qt,h as jt,_ as Dt,q as Tt,r as Zt,j as vt,$ as Lt,M as xt,a0 as Vt,a1 as Ht,b as Ft,B as Xt,a2 as Pt}from\"./FullscreenButton.js\";import{createEventDispatcher as ut,onDestroy as Mt,onMount as ct}from\"svelte\";import{M as _t}from\"./MarkdownCode.js\";import\"svelte/transition\";import{d as Rt}from\"./index7.js\";import{S as Nt}from\"./StreamingBar.js\";const Kt=(A,t)=>A.replace('src=\"/file',`src=\"${t}file`);function Jt(A,t){if(!A){const e=t?.path;if(e){const s=e.toLowerCase();if(s.endsWith(\".glb\")||s.endsWith(\".gltf\")||s.endsWith(\".obj\")||s.endsWith(\".stl\")||s.endsWith(\".splat\")||s.endsWith(\".ply\"))return\"model3d\"}return\"file\"}return A.includes(\"audio\")?\"audio\":A.includes(\"video\")?\"video\":A.includes(\"image\")?\"image\":A.includes(\"model\")?\"model3d\":\"file\"}function Ut(A){const t=Array.isArray(A.file)?A.file[0]:A.file;return{component:Jt(t?.mime_type,t),value:A.file,alt_text:A.alt_text,constructor_args:{},props:{}}}function $t(A,t){if(A===null)return A;const e=new Map;return A.map((s,a)=>{let o=typeof s.content==\"string\"?{role:s.role,metadata:s.metadata,content:Kt(s.content,t),type:\"text\",index:a,options:s.options}:\"file\"in s.content?{content:Ut(s.content),metadata:s.metadata,role:s.role,type:\"component\",index:a,options:s.options}:{type:\"component\",...s};const{id:l,title:n,parent_id:r}=s.metadata||{};if(r){const i=e.get(String(r));if(i){const d={...o,children:[]};return i.children.push(d),l&&n&&e.set(String(l),d),null}}if(l&&n){const i={...o,children:[]};return e.set(String(l),i),i}return o}).filter(s=>s!==null)}function te(A,t){return A===null?A:A.flatMap((s,a)=>s.map((o,l)=>{if(o==null)return null;const n=l==0?\"user\":\"assistant\";return typeof o==\"string\"?{role:n,type:\"text\",content:Kt(o,t),metadata:{title:null},index:[a,l]}:\"file\"in o?{content:Ut(o),role:n,type:\"component\",index:[a,l]}:{role:n,content:o,type:\"component\",index:[a,l]}})).filter(s=>s!=null)}function ee(A){return A.type===\"component\"}function bt(A,t){const e=A[A.length-1].role===\"assistant\",s=A[A.length-1].index;return JSON.stringify(s)===JSON.stringify(t[t.length-1].index)&&e}function Ae(A,t,e=!0){const s=[];let a=[],o=null;for(const l of A)if(l.role===\"assistant\"||l.role===\"user\"){if(!e){s.push([l]);continue}l.role===o?a.push(l):(a.length>0&&s.push(a),a=[l],o=l.role)}return a.length>0&&s.push(a),s}async function ne(A,t,e){let s=[],a=[];return A.forEach(n=>{if(t[n]||n===\"file\")return;const r=n===\"dataframe\"||n===\"model3d\"?\"component\":\"base\",{name:i,component:d}=e(n,r);s.push(i),a.push(d)}),(await Promise.allSettled(a)).map((n,r)=>n.status===\"fulfilled\"?[r,n.value]:null).filter(n=>n!==null).forEach(([n,r])=>{t[s[n]]=r.default}),t}function ae(A){if(!A)return[];let t=new Set;return A.forEach(e=>{e.type===\"component\"&&t.add(e.content.component)}),Array.from(t)}function mt(A,t=0){let e=\"\";const s=\"  \".repeat(t);A.metadata?.title&&(e+=`${s}${t>0?\"- \":\"\"}${A.metadata.title}\n`),typeof A.content==\"string\"&&(e+=`${s}  ${A.content}\n`);const a=A;return a.children?.length>0&&(e+=a.children.map(o=>mt(o,t+1)).join(\"\")),e}function le(A){return Array.isArray(A)?A.map(t=>t.metadata?.title?mt(t):t.content).join(`\n`):A.metadata?.title?mt(A):A.content}function wt(A){return Array.isArray(A)&&A.every(t=>typeof t.content==\"string\")||!Array.isArray(A)&&typeof A.content==\"string\"}const oe=M((A,t,e,s)=>'<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M11.25 6.61523H9.375V1.36523H11.25V6.61523ZM3.375 1.36523H8.625V6.91636L7.48425 8.62748L7.16737 10.8464C7.14108 11.0248 7.05166 11.1879 6.91535 11.3061C6.77904 11.4242 6.60488 11.4896 6.4245 11.4902H6.375C6.07672 11.4899 5.79075 11.3713 5.57983 11.1604C5.36892 10.9495 5.2503 10.6635 5.25 10.3652V8.11523H2.25C1.85233 8.11474 1.47109 7.95654 1.18989 7.67535C0.908691 7.39415 0.750496 7.01291 0.75 6.61523V3.99023C0.750992 3.29435 1.02787 2.62724 1.51994 2.13517C2.01201 1.64311 2.67911 1.36623 3.375 1.36523Z\" fill=\"currentColor\"></path></svg>'),ie=M((A,t,e,s)=>'<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M2.25 8.11523H4.5V10.3652C4.5003 10.6635 4.61892 10.9495 4.82983 11.1604C5.04075 11.3713 5.32672 11.4899 5.625 11.4902H6.42488C6.60519 11.4895 6.77926 11.4241 6.91549 11.3059C7.05172 11.1878 7.14109 11.0248 7.16737 10.8464L7.48425 8.62748L8.82562 6.61523H11.25V1.36523H3.375C2.67911 1.36623 2.01201 1.64311 1.51994 2.13517C1.02787 2.62724 0.750992 3.29435 0.75 3.99023V6.61523C0.750496 7.01291 0.908691 7.39415 1.18989 7.67535C1.47109 7.95654 1.85233 8.11474 2.25 8.11523ZM9 2.11523H10.5V5.86523H9V2.11523ZM1.5 3.99023C1.5006 3.49314 1.69833 3.01657 2.04983 2.66507C2.40133 2.31356 2.8779 2.11583 3.375 2.11523H8.25V6.12661L6.76575 8.35298L6.4245 10.7402H5.625C5.52554 10.7402 5.43016 10.7007 5.35983 10.6304C5.28951 10.5601 5.25 10.4647 5.25 10.3652V7.36523H2.25C2.05118 7.36494 1.86059 7.28582 1.72 7.14524C1.57941 7.00465 1.5003 6.81406 1.5 6.61523V3.99023Z\" fill=\"currentColor\"></path></svg>'),re=M((A,t,e,s)=>'<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0.75 6.24023H2.625V11.4902H0.75V6.24023ZM8.625 11.4902H3.375V5.93911L4.51575 4.22798L4.83263 2.00911C4.85892 1.83065 4.94834 1.66754 5.08465 1.5494C5.22096 1.43125 5.39512 1.36591 5.5755 1.36523H5.625C5.92328 1.36553 6.20925 1.48415 6.42017 1.69507C6.63108 1.90598 6.7497 2.19196 6.75 2.49023V4.74023H9.75C10.1477 4.74073 10.5289 4.89893 10.8101 5.18012C11.0913 5.46132 11.2495 5.84256 11.25 6.24023V8.86523C11.249 9.56112 10.9721 10.2282 10.4801 10.7203C9.98799 11.2124 9.32089 11.4892 8.625 11.4902Z\" fill=\"currentColor\"></path></svg>'),se=M((A,t,e,s)=>'<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M9.75 4.74023H7.5V2.49023C7.4997 2.19196 7.38108 1.90598 7.17017 1.69507C6.95925 1.48415 6.67328 1.36553 6.375 1.36523H5.57512C5.39481 1.366 5.22074 1.43138 5.08451 1.54952C4.94828 1.66766 4.85891 1.83072 4.83262 2.00911L4.51575 4.22798L3.17438 6.24023H0.75V11.4902H8.625C9.32089 11.4892 9.98799 11.2124 10.4801 10.7203C10.9721 10.2282 11.249 9.56112 11.25 8.86523V6.24023C11.2495 5.84256 11.0913 5.46132 10.8101 5.18012C10.5289 4.89893 10.1477 4.74073 9.75 4.74023ZM3 10.7402H1.5V6.99023H3V10.7402ZM10.5 8.86523C10.4994 9.36233 10.3017 9.8389 9.95017 10.1904C9.59867 10.5419 9.1221 10.7396 8.625 10.7402H3.75V6.72886L5.23425 4.50248L5.5755 2.11523H6.375C6.47446 2.11523 6.56984 2.15474 6.64017 2.22507C6.71049 2.2954 6.75 2.39078 6.75 2.49023V5.49023H9.75C9.94882 5.49053 10.1394 5.56965 10.28 5.71023C10.4206 5.85082 10.4997 6.04141 10.5 6.24023V8.86523Z\" fill=\"currentColor\"></path></svg>'),Ce=M((A,t,e,s)=>'<svg id=\"icon\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" fill=\"none\"><path fill=\"currentColor\" d=\"M6,30H4V2H28l-5.8,9L28,20H6ZM6,18H24.33L19.8,11l4.53-7H6Z\"></path></svg>'),de=M((A,t,e,s)=>'<svg id=\"icon\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" fill=\"none\"><path fill=\"currentColor\" d=\"M4,2H28l-5.8,9L28,20H6v10H4V2z\"></path></svg>'),ce={code:\".extra-feedback.svelte-14rmxes.svelte-14rmxes{display:flex;align-items:center;position:relative}.extra-feedback-options.svelte-14rmxes.svelte-14rmxes{display:none;position:absolute;padding:var(--spacing-md) 0;flex-direction:column;gap:var(--spacing-sm);top:100%}.extra-feedback.svelte-14rmxes:hover .extra-feedback-options.svelte-14rmxes{display:flex}.extra-feedback-option.svelte-14rmxes.svelte-14rmxes{border:1px solid var(--border-color-primary);border-radius:var(--radius-sm);color:var(--block-label-text-color);background-color:var(--block-background-fill);font-size:var(--text-xs);padding:var(--spacing-xxs) var(--spacing-sm);width:max-content}\",map:'{\"version\":3,\"file\":\"LikeDislike.svelte\",\"sources\":[\"LikeDislike.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { IconButton } from \\\\\"@gradio/atoms\\\\\";\\\\nimport ThumbDownActive from \\\\\"./ThumbDownActive.svelte\\\\\";\\\\nimport ThumbDownDefault from \\\\\"./ThumbDownDefault.svelte\\\\\";\\\\nimport ThumbUpActive from \\\\\"./ThumbUpActive.svelte\\\\\";\\\\nimport ThumbUpDefault from \\\\\"./ThumbUpDefault.svelte\\\\\";\\\\nimport Flag from \\\\\"./Flag.svelte\\\\\";\\\\nimport FlagActive from \\\\\"./FlagActive.svelte\\\\\";\\\\nexport let i18n;\\\\nexport let handle_action;\\\\nexport let feedback_options;\\\\nexport let selected = null;\\\\n$: extra_feedback = feedback_options.filter((option) => option !== \\\\\"Like\\\\\" && option !== \\\\\"Dislike\\\\\");\\\\nfunction toggleSelection(newSelection) {\\\\n    selected = selected === newSelection ? null : newSelection;\\\\n    handle_action(selected);\\\\n}\\\\n<\\/script>\\\\n\\\\n{#if feedback_options.includes(\\\\\"Like\\\\\") || feedback_options.includes(\\\\\"Dislike\\\\\")}\\\\n\\\\t{#if feedback_options.includes(\\\\\"Dislike\\\\\")}\\\\n\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\tIcon={selected === \\\\\"Dislike\\\\\" ? ThumbDownActive : ThumbDownDefault}\\\\n\\\\t\\\\t\\\\tlabel={selected === \\\\\"Dislike\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t? \\\\\"clicked dislike\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t: i18n(\\\\\"chatbot.dislike\\\\\")}\\\\n\\\\t\\\\t\\\\tcolor={selected === \\\\\"Dislike\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t? \\\\\"var(--color-accent)\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t: \\\\\"var(--block-label-text-color)\\\\\"}\\\\n\\\\t\\\\t\\\\ton:click={() => toggleSelection(\\\\\"Dislike\\\\\")}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n\\\\t{#if feedback_options.includes(\\\\\"Like\\\\\")}\\\\n\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\tIcon={selected === \\\\\"Like\\\\\" ? ThumbUpActive : ThumbUpDefault}\\\\n\\\\t\\\\t\\\\tlabel={selected === \\\\\"Like\\\\\" ? \\\\\"clicked like\\\\\" : i18n(\\\\\"chatbot.like\\\\\")}\\\\n\\\\t\\\\t\\\\tcolor={selected === \\\\\"Like\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t? \\\\\"var(--color-accent)\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t: \\\\\"var(--block-label-text-color)\\\\\"}\\\\n\\\\t\\\\t\\\\ton:click={() => toggleSelection(\\\\\"Like\\\\\")}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n{/if}\\\\n\\\\n{#if extra_feedback.length > 0}\\\\n\\\\t<div class=\\\\\"extra-feedback no-border\\\\\">\\\\n\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\tIcon={selected && extra_feedback.includes(selected) ? FlagActive : Flag}\\\\n\\\\t\\\\t\\\\tlabel=\\\\\"Feedback\\\\\"\\\\n\\\\t\\\\t\\\\tcolor={selected && extra_feedback.includes(selected)\\\\n\\\\t\\\\t\\\\t\\\\t? \\\\\"var(--color-accent)\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t: \\\\\"var(--block-label-text-color)\\\\\"}\\\\n\\\\t\\\\t/>\\\\n\\\\t\\\\t<div class=\\\\\"extra-feedback-options\\\\\">\\\\n\\\\t\\\\t\\\\t{#each extra_feedback as option}\\\\n\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"extra-feedback-option\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tstyle:font-weight={selected === option ? \\\\\"bold\\\\\" : \\\\\"normal\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttoggleSelection(option);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\thandle_action(selected ? selected : null);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t}}>{option}</button\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t</div>\\\\n\\\\t</div>\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.extra-feedback {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t}\\\\n\\\\t.extra-feedback-options {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tpadding: var(--spacing-md) 0;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tgap: var(--spacing-sm);\\\\n\\\\t\\\\ttop: 100%;\\\\n\\\\t}\\\\n\\\\t.extra-feedback:hover .extra-feedback-options {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t}\\\\n\\\\t.extra-feedback-option {\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--radius-sm);\\\\n\\\\t\\\\tcolor: var(--block-label-text-color);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tfont-size: var(--text-xs);\\\\n\\\\t\\\\tpadding: var(--spacing-xxs) var(--spacing-sm);\\\\n\\\\t\\\\twidth: max-content;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAoEC,6CAAgB,CACf,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,QAAQ,CAAE,QACX,CACA,qDAAwB,CACvB,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,YAAY,CAAC,CAAC,CAAC,CAC5B,cAAc,CAAE,MAAM,CACtB,GAAG,CAAE,IAAI,YAAY,CAAC,CACtB,GAAG,CAAE,IACN,CACA,8BAAe,MAAM,CAAC,sCAAwB,CAC7C,OAAO,CAAE,IACV,CACA,oDAAuB,CACtB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,KAAK,CAAE,IAAI,wBAAwB,CAAC,CACpC,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,OAAO,CAAE,IAAI,aAAa,CAAC,CAAC,IAAI,YAAY,CAAC,CAC7C,KAAK,CAAE,WACR\"}'},ve=M((A,t,e,s)=>{let a,{i18n:o}=t,{handle_action:l}=t,{feedback_options:n}=t,{selected:r=null}=t;return t.i18n===void 0&&e.i18n&&o!==void 0&&e.i18n(o),t.handle_action===void 0&&e.handle_action&&l!==void 0&&e.handle_action(l),t.feedback_options===void 0&&e.feedback_options&&n!==void 0&&e.feedback_options(n),t.selected===void 0&&e.selected&&r!==void 0&&e.selected(r),A.css.add(ce),a=n.filter(i=>i!==\"Like\"&&i!==\"Dislike\"),`${n.includes(\"Like\")||n.includes(\"Dislike\")?`${n.includes(\"Dislike\")?`${C(D,\"IconButton\").$$render(A,{Icon:r===\"Dislike\"?oe:ie,label:r===\"Dislike\"?\"clicked dislike\":o(\"chatbot.dislike\"),color:r===\"Dislike\"?\"var(--color-accent)\":\"var(--block-label-text-color)\"},{},{})}`:\"\"} ${n.includes(\"Like\")?`${C(D,\"IconButton\").$$render(A,{Icon:r===\"Like\"?re:se,label:r===\"Like\"?\"clicked like\":o(\"chatbot.like\"),color:r===\"Like\"?\"var(--color-accent)\":\"var(--block-label-text-color)\"},{},{})}`:\"\"}`:\"\"} ${a.length>0?`<div class=\"extra-feedback no-border svelte-14rmxes\">${C(D,\"IconButton\").$$render(A,{Icon:r&&a.includes(r)?de:Ce,label:\"Feedback\",color:r&&a.includes(r)?\"var(--color-accent)\":\"var(--block-label-text-color)\"},{},{})} <div class=\"extra-feedback-options svelte-14rmxes\">${ot(a,i=>`<button class=\"extra-feedback-option svelte-14rmxes\"${Ct({\"font-weight\":r===i?\"bold\":\"normal\"})}>${I(i)}</button>`)}</div></div>`:\"\"}`}),me=M((A,t,e,s)=>{ut();let{value:a}=t,{watermark:o=null}=t;return Mt(()=>{}),t.value===void 0&&e.value&&a!==void 0&&e.value(a),t.watermark===void 0&&e.watermark&&o!==void 0&&e.watermark(o),`${C(D,\"IconButton\").$$render(A,{label:\"Copy message\",Icon:yt},{},{})}`}),ue={code:\".bubble.svelte-j7nkv7 .icon-button-wrapper{margin:0px calc(var(--spacing-xl) * 2)}.message-buttons.svelte-j7nkv7{z-index:var(--layer-1)}.message-buttons-left.svelte-j7nkv7{align-self:flex-start}.bubble.message-buttons-right.svelte-j7nkv7{align-self:flex-end}.message-buttons-right.svelte-j7nkv7 .icon-button-wrapper{margin-left:auto}.bubble.with-avatar.svelte-j7nkv7{margin-left:calc(var(--spacing-xl) * 5);margin-right:calc(var(--spacing-xl) * 5)}.panel.svelte-j7nkv7{display:flex;align-self:flex-start;z-index:var(--layer-1)}\",map:`{\"version\":3,\"file\":\"ButtonPanel.svelte\",\"sources\":[\"ButtonPanel.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import LikeDislike from \\\\\"./LikeDislike.svelte\\\\\";\\\\nimport Copy from \\\\\"./Copy.svelte\\\\\";\\\\nimport { Retry, Undo, Edit, Check, Clear } from \\\\\"@gradio/icons\\\\\";\\\\nimport { IconButtonWrapper, IconButton } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { all_text, is_all_text } from \\\\\"./utils\\\\\";\\\\nexport let i18n;\\\\nexport let likeable;\\\\nexport let feedback_options;\\\\nexport let show_retry;\\\\nexport let show_undo;\\\\nexport let show_edit;\\\\nexport let in_edit_mode;\\\\nexport let show_copy_button;\\\\nexport let watermark = null;\\\\nexport let message;\\\\nexport let position;\\\\nexport let avatar;\\\\nexport let generating;\\\\nexport let current_feedback;\\\\nexport let handle_action;\\\\nexport let layout;\\\\nexport let dispatch;\\\\n$: message_text = is_all_text(message) ? all_text(message) : \\\\\"\\\\\";\\\\n$: show_copy = show_copy_button && message && is_all_text(message);\\\\n<\\/script>\\\\n\\\\n{#if show_copy || show_retry || show_undo || show_edit || likeable}\\\\n\\\\t<div\\\\n\\\\t\\\\tclass=\\\\\"message-buttons-{position} {layout} message-buttons {avatar !==\\\\n\\\\t\\\\t\\\\tnull && 'with-avatar'}\\\\\"\\\\n\\\\t>\\\\n\\\\t\\\\t<IconButtonWrapper top_panel={false}>\\\\n\\\\t\\\\t\\\\t{#if in_edit_mode}\\\\n\\\\t\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tlabel={i18n(\\\\\"chatbot.submit\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tIcon={Check}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => handle_action(\\\\\"edit_submit\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tdisabled={generating}\\\\n\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tlabel={i18n(\\\\\"chatbot.cancel\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tIcon={Clear}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => handle_action(\\\\\"edit_cancel\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tdisabled={generating}\\\\n\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t{#if show_copy}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<Copy\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tvalue={message_text}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:copy={(e) => dispatch(\\\\\"copy\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{watermark}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t{#if show_retry}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tIcon={Retry}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tlabel={i18n(\\\\\"chatbot.retry\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => handle_action(\\\\\"retry\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdisabled={generating}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t{#if show_undo}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tlabel={i18n(\\\\\"chatbot.undo\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tIcon={Undo}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => handle_action(\\\\\"undo\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdisabled={generating}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t{#if show_edit}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tlabel={i18n(\\\\\"chatbot.edit\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tIcon={Edit}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => handle_action(\\\\\"edit\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdisabled={generating}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t{#if likeable}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<LikeDislike\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{handle_action}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{feedback_options}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tselected={current_feedback}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t</IconButtonWrapper>\\\\n\\\\t</div>\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.bubble :global(.icon-button-wrapper) {\\\\n\\\\t\\\\tmargin: 0px calc(var(--spacing-xl) * 2);\\\\n\\\\t}\\\\n\\\\n\\\\t.message-buttons {\\\\n\\\\t\\\\tz-index: var(--layer-1);\\\\n\\\\t}\\\\n\\\\t.message-buttons-left {\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble.message-buttons-right {\\\\n\\\\t\\\\talign-self: flex-end;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-buttons-right :global(.icon-button-wrapper) {\\\\n\\\\t\\\\tmargin-left: auto;\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble.with-avatar {\\\\n\\\\t\\\\tmargin-left: calc(var(--spacing-xl) * 5);\\\\n\\\\t\\\\tmargin-right: calc(var(--spacing-xl) * 5);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\tz-index: var(--layer-1);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA2FC,qBAAO,CAAS,oBAAsB,CACrC,MAAM,CAAE,GAAG,CAAC,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CACvC,CAEA,8BAAiB,CAChB,OAAO,CAAE,IAAI,SAAS,CACvB,CACA,mCAAsB,CACrB,UAAU,CAAE,UACb,CAEA,OAAO,oCAAuB,CAC7B,UAAU,CAAE,QACb,CAEA,oCAAsB,CAAS,oBAAsB,CACpD,WAAW,CAAE,IACd,CAEA,OAAO,0BAAa,CACnB,WAAW,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACxC,YAAY,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CACzC,CAEA,oBAAO,CACN,OAAO,CAAE,IAAI,CACb,UAAU,CAAE,UAAU,CACtB,OAAO,CAAE,IAAI,SAAS,CACvB\"}`},Bt=M((A,t,e,s)=>{let a,o,{i18n:l}=t,{likeable:n}=t,{feedback_options:r}=t,{show_retry:i}=t,{show_undo:d}=t,{show_edit:m}=t,{in_edit_mode:u}=t,{show_copy_button:f}=t,{watermark:g=null}=t,{message:_}=t,{position:x}=t,{avatar:h}=t,{generating:p}=t,{current_feedback:c}=t,{handle_action:E}=t,{layout:v}=t,{dispatch:B}=t;return t.i18n===void 0&&e.i18n&&l!==void 0&&e.i18n(l),t.likeable===void 0&&e.likeable&&n!==void 0&&e.likeable(n),t.feedback_options===void 0&&e.feedback_options&&r!==void 0&&e.feedback_options(r),t.show_retry===void 0&&e.show_retry&&i!==void 0&&e.show_retry(i),t.show_undo===void 0&&e.show_undo&&d!==void 0&&e.show_undo(d),t.show_edit===void 0&&e.show_edit&&m!==void 0&&e.show_edit(m),t.in_edit_mode===void 0&&e.in_edit_mode&&u!==void 0&&e.in_edit_mode(u),t.show_copy_button===void 0&&e.show_copy_button&&f!==void 0&&e.show_copy_button(f),t.watermark===void 0&&e.watermark&&g!==void 0&&e.watermark(g),t.message===void 0&&e.message&&_!==void 0&&e.message(_),t.position===void 0&&e.position&&x!==void 0&&e.position(x),t.avatar===void 0&&e.avatar&&h!==void 0&&e.avatar(h),t.generating===void 0&&e.generating&&p!==void 0&&e.generating(p),t.current_feedback===void 0&&e.current_feedback&&c!==void 0&&e.current_feedback(c),t.handle_action===void 0&&e.handle_action&&E!==void 0&&e.handle_action(E),t.layout===void 0&&e.layout&&v!==void 0&&e.layout(v),t.dispatch===void 0&&e.dispatch&&B!==void 0&&e.dispatch(B),A.css.add(ue),a=wt(_)?le(_):\"\",o=f&&_&&wt(_),`${o||i||d||m||n?`<div class=\"${\"message-buttons-\"+I(x,!0)+\" \"+I(v,!0)+\" message-buttons \"+I(h!==null&&\"with-avatar\",!0)+\" svelte-j7nkv7\"}\">${C(kt,\"IconButtonWrapper\").$$render(A,{top_panel:!1},{},{default:()=>`${u?`${C(D,\"IconButton\").$$render(A,{label:l(\"chatbot.submit\"),Icon:Qt,disabled:p},{},{})} ${C(D,\"IconButton\").$$render(A,{label:l(\"chatbot.cancel\"),Icon:jt,disabled:p},{},{})}`:`${o?`${C(me,\"Copy\").$$render(A,{value:a,watermark:g},{},{})}`:\"\"} ${i?`${C(D,\"IconButton\").$$render(A,{Icon:Dt,label:l(\"chatbot.retry\"),disabled:p},{},{})}`:\"\"} ${d?`${C(D,\"IconButton\").$$render(A,{label:l(\"chatbot.undo\"),Icon:Tt,disabled:p},{},{})}`:\"\"} ${m?`${C(D,\"IconButton\").$$render(A,{label:l(\"chatbot.edit\"),Icon:Zt,disabled:p},{},{})}`:\"\"} ${n?`${C(ve,\"LikeDislike\").$$render(A,{handle_action:E,feedback_options:r,selected:c,i18n:l},{},{})}`:\"\"}`}`})}</div>`:\"\"}`}),_e=M((A,t,e,s)=>{let{type:a}=t,{components:o}=t,{value:l}=t,{target:n}=t,{theme_mode:r}=t,{props:i}=t,{i18n:d}=t,{upload:m}=t,{_fetch:u}=t,{allow_file_downloads:f}=t,{display_icon_button_wrapper_top_corner:g=!1}=t;return t.type===void 0&&e.type&&a!==void 0&&e.type(a),t.components===void 0&&e.components&&o!==void 0&&e.components(o),t.value===void 0&&e.value&&l!==void 0&&e.value(l),t.target===void 0&&e.target&&n!==void 0&&e.target(n),t.theme_mode===void 0&&e.theme_mode&&r!==void 0&&e.theme_mode(r),t.props===void 0&&e.props&&i!==void 0&&e.props(i),t.i18n===void 0&&e.i18n&&d!==void 0&&e.i18n(d),t.upload===void 0&&e.upload&&m!==void 0&&e.upload(m),t._fetch===void 0&&e._fetch&&u!==void 0&&e._fetch(u),t.allow_file_downloads===void 0&&e.allow_file_downloads&&f!==void 0&&e.allow_file_downloads(f),t.display_icon_button_wrapper_top_corner===void 0&&e.display_icon_button_wrapper_top_corner&&g!==void 0&&e.display_icon_button_wrapper_top_corner(g),`${a===\"gallery\"?`${C(o[a]||lt,\"svelte:component\").$$render(A,{value:l,display_icon_button_wrapper_top_corner:g,show_label:!1,i18n:d,label:\"\",_fetch:u,allow_preview:!1,interactive:!1,mode:\"minimal\",fixed_height:1},{},{})}`:`${a===\"dataframe\"?`${C(o[a]||lt,\"svelte:component\").$$render(A,{value:l,show_label:!1,i18n:d,label:\"\",interactive:!1,line_breaks:i.line_breaks,wrap:!0,root:\"\",gradio:{dispatch:()=>{},i18n:d},datatype:i.datatype,latex_delimiters:i.latex_delimiters,col_count:i.col_count,row_count:i.row_count},{},{})}`:`${a===\"plot\"?`${C(o[a]||lt,\"svelte:component\").$$render(A,{value:l,target:n,theme_mode:r,bokeh_version:i.bokeh_version,caption:\"\",show_actions_button:!0},{},{})}`:`${a===\"audio\"?`<div style=\"position: relative;\">${C(o[a]||lt,\"svelte:component\").$$render(A,{value:l,show_label:!1,show_share_button:!0,i18n:d,label:\"\",waveform_settings:{autoplay:i.autoplay},show_download_button:f,display_icon_button_wrapper_top_corner:g},{},{})}</div>`:`${a===\"video\"?`${C(o[a]||lt,\"svelte:component\").$$render(A,{autoplay:i.autoplay,value:l.video||l,show_label:!1,show_share_button:!0,i18n:d,upload:m,display_icon_button_wrapper_top_corner:g,show_download_button:f},{},{default:()=>'<track kind=\"captions\">'})}`:`${a===\"image\"?`${C(o[a]||lt,\"svelte:component\").$$render(A,{value:l,show_label:!1,label:\"chatbot-image\",show_download_button:f,display_icon_button_wrapper_top_corner:g,i18n:d},{},{})}`:`${a===\"html\"?`${C(o[a]||lt,\"svelte:component\").$$render(A,{value:l,show_label:!1,label:\"chatbot-html\",show_share_button:!0,i18n:d,gradio:{dispatch:()=>{}}},{},{})}`:`${a===\"model3d\"?`${C(o[a]||lt,\"svelte:component\").$$render(A,Object.assign({},{value:l},{clear_color:i.clear_color},{display_mode:i.display_mode},{zoom_speed:i.zoom_speed},{pan_speed:i.pan_speed},i.camera_position!==void 0&&{camera_position:i.camera_position},{has_change_history:!0},{show_label:!1},{root:\"\"},{interactive:!1},{label:\"chatbot-model3d\"},{show_share_button:!0},{gradio:{dispatch:()=>{},i18n:d}}),{},{})}`:\"\"}`}`}`}`}`}`}`}`}),fe={code:\".file-container.svelte-ulpe0d{display:flex;align-items:center;gap:var(--spacing-lg);padding:var(--spacing-lg);border-radius:var(--radius-lg);width:fit-content;margin:var(--spacing-sm) 0}.file-icon.svelte-ulpe0d{display:flex;align-items:center;justify-content:center;color:var(--body-text-color)}.file-icon.svelte-ulpe0d svg{width:var(--size-7);height:var(--size-7)}.file-info.svelte-ulpe0d{display:flex;flex-direction:column}.file-link.svelte-ulpe0d{text-decoration:none;color:var(--body-text-color);display:flex;flex-direction:column;gap:var(--spacing-xs)}.file-name.svelte-ulpe0d{font-family:var(--font);font-size:var(--text-md);font-weight:500}.file-type.svelte-ulpe0d{font-family:var(--font);font-size:var(--text-sm);color:var(--body-text-color-subdued);text-transform:uppercase}\",map:'{\"version\":3,\"file\":\"MessageContent.svelte\",\"sources\":[\"MessageContent.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { File } from \\\\\"@gradio/icons\\\\\";\\\\nimport Component from \\\\\"./Component.svelte\\\\\";\\\\nimport { MarkdownCode as Markdown } from \\\\\"@gradio/markdown-code\\\\\";\\\\nexport let latex_delimiters;\\\\nexport let sanitize_html;\\\\nexport let _fetch;\\\\nexport let i18n;\\\\nexport let line_breaks;\\\\nexport let upload;\\\\nexport let target;\\\\nexport let theme_mode;\\\\nexport let _components;\\\\nexport let render_markdown;\\\\nexport let scroll;\\\\nexport let allow_file_downloads;\\\\nexport let display_consecutive_in_same_bubble;\\\\nexport let thought_index;\\\\nexport let allow_tags = false;\\\\nexport let message;\\\\n<\\/script>\\\\n\\\\n{#if message.type === \\\\\"text\\\\\"}\\\\n\\\\t<div class=\\\\\"message-content\\\\\">\\\\n\\\\t\\\\t<Markdown\\\\n\\\\t\\\\t\\\\tmessage={message.content}\\\\n\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t\\\\t{render_markdown}\\\\n\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\ton:load={scroll}\\\\n\\\\t\\\\t\\\\t{allow_tags}\\\\n\\\\t\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\t/>\\\\n\\\\t</div>\\\\n{:else if message.type === \\\\\"component\\\\\" && message.content.component in _components}\\\\n\\\\t<Component\\\\n\\\\t\\\\t{target}\\\\n\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\tprops={message.content.props}\\\\n\\\\t\\\\ttype={message.content.component}\\\\n\\\\t\\\\tcomponents={_components}\\\\n\\\\t\\\\tvalue={message.content.value}\\\\n\\\\t\\\\tdisplay_icon_button_wrapper_top_corner={thought_index > 0 &&\\\\n\\\\t\\\\t\\\\tdisplay_consecutive_in_same_bubble}\\\\n\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t{upload}\\\\n\\\\t\\\\t{_fetch}\\\\n\\\\t\\\\ton:load={() => scroll()}\\\\n\\\\t\\\\t{allow_file_downloads}\\\\n\\\\t/>\\\\n{:else if message.type === \\\\\"component\\\\\" && message.content.component === \\\\\"file\\\\\"}\\\\n\\\\t<div class=\\\\\"file-container\\\\\">\\\\n\\\\t\\\\t<div class=\\\\\"file-icon\\\\\">\\\\n\\\\t\\\\t\\\\t<File />\\\\n\\\\t\\\\t</div>\\\\n\\\\t\\\\t<div class=\\\\\"file-info\\\\\">\\\\n\\\\t\\\\t\\\\t<a\\\\n\\\\t\\\\t\\\\t\\\\tdata-testid=\\\\\"chatbot-file\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"file-link\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\thref={message.content.value.url}\\\\n\\\\t\\\\t\\\\t\\\\ttarget=\\\\\"_blank\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tdownload={window.__is_colab__\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t? null\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t: message.content.value?.orig_name ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmessage.content.value?.path.split(\\\\\"/\\\\\").pop() ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\\"file\\\\\"}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"file-name\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t>{message.content.value?.orig_name ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmessage.content.value?.path.split(\\\\\"/\\\\\").pop() ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\\"file\\\\\"}</span\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t</a>\\\\n\\\\t\\\\t\\\\t<span class=\\\\\"file-type\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t>{(\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tmessage.content.value?.orig_name ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tmessage.content.value?.path ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\\"\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t)\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t.split(\\\\\".\\\\\")\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t.pop()\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t.toUpperCase()}</span\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t</div>\\\\n\\\\t</div>\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.file-container {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tgap: var(--spacing-lg);\\\\n\\\\t\\\\tpadding: var(--spacing-lg);\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t\\\\twidth: fit-content;\\\\n\\\\t\\\\tmargin: var(--spacing-sm) 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.file-icon {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t}\\\\n\\\\n\\\\t.file-icon :global(svg) {\\\\n\\\\t\\\\twidth: var(--size-7);\\\\n\\\\t\\\\theight: var(--size-7);\\\\n\\\\t}\\\\n\\\\n\\\\t.file-info {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t}\\\\n\\\\n\\\\t.file-link {\\\\n\\\\t\\\\ttext-decoration: none;\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tgap: var(--spacing-xs);\\\\n\\\\t}\\\\n\\\\n\\\\t.file-name {\\\\n\\\\t\\\\tfont-family: var(--font);\\\\n\\\\t\\\\tfont-size: var(--text-md);\\\\n\\\\t\\\\tfont-weight: 500;\\\\n\\\\t}\\\\n\\\\n\\\\t.file-type {\\\\n\\\\t\\\\tfont-family: var(--font);\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t\\\\tcolor: var(--body-text-color-subdued);\\\\n\\\\t\\\\ttext-transform: uppercase;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAwFC,6BAAgB,CACf,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,GAAG,CAAE,IAAI,YAAY,CAAC,CACtB,OAAO,CAAE,IAAI,YAAY,CAAC,CAC1B,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,KAAK,CAAE,WAAW,CAClB,MAAM,CAAE,IAAI,YAAY,CAAC,CAAC,CAC3B,CAEA,wBAAW,CACV,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CAEA,wBAAU,CAAS,GAAK,CACvB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CACrB,CAEA,wBAAW,CACV,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MACjB,CAEA,wBAAW,CACV,eAAe,CAAE,IAAI,CACrB,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,GAAG,CAAE,IAAI,YAAY,CACtB,CAEA,wBAAW,CACV,WAAW,CAAE,IAAI,MAAM,CAAC,CACxB,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,WAAW,CAAE,GACd,CAEA,wBAAW,CACV,WAAW,CAAE,IAAI,MAAM,CAAC,CACxB,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,KAAK,CAAE,IAAI,yBAAyB,CAAC,CACrC,cAAc,CAAE,SACjB\"}'},St=M((A,t,e,s)=>{let{latex_delimiters:a}=t,{sanitize_html:o}=t,{_fetch:l}=t,{i18n:n}=t,{line_breaks:r}=t,{upload:i}=t,{target:d}=t,{theme_mode:m}=t,{_components:u}=t,{render_markdown:f}=t,{scroll:g}=t,{allow_file_downloads:_}=t,{display_consecutive_in_same_bubble:x}=t,{thought_index:h}=t,{allow_tags:p=!1}=t,{message:c}=t;return t.latex_delimiters===void 0&&e.latex_delimiters&&a!==void 0&&e.latex_delimiters(a),t.sanitize_html===void 0&&e.sanitize_html&&o!==void 0&&e.sanitize_html(o),t._fetch===void 0&&e._fetch&&l!==void 0&&e._fetch(l),t.i18n===void 0&&e.i18n&&n!==void 0&&e.i18n(n),t.line_breaks===void 0&&e.line_breaks&&r!==void 0&&e.line_breaks(r),t.upload===void 0&&e.upload&&i!==void 0&&e.upload(i),t.target===void 0&&e.target&&d!==void 0&&e.target(d),t.theme_mode===void 0&&e.theme_mode&&m!==void 0&&e.theme_mode(m),t._components===void 0&&e._components&&u!==void 0&&e._components(u),t.render_markdown===void 0&&e.render_markdown&&f!==void 0&&e.render_markdown(f),t.scroll===void 0&&e.scroll&&g!==void 0&&e.scroll(g),t.allow_file_downloads===void 0&&e.allow_file_downloads&&_!==void 0&&e.allow_file_downloads(_),t.display_consecutive_in_same_bubble===void 0&&e.display_consecutive_in_same_bubble&&x!==void 0&&e.display_consecutive_in_same_bubble(x),t.thought_index===void 0&&e.thought_index&&h!==void 0&&e.thought_index(h),t.allow_tags===void 0&&e.allow_tags&&p!==void 0&&e.allow_tags(p),t.message===void 0&&e.message&&c!==void 0&&e.message(c),A.css.add(fe),`${c.type===\"text\"?`<div class=\"message-content\">${C(_t,\"Markdown\").$$render(A,{message:c.content,latex_delimiters:a,sanitize_html:o,render_markdown:f,line_breaks:r,allow_tags:p,theme_mode:m},{},{})}</div>`:`${c.type===\"component\"&&c.content.component in u?`${C(_e,\"Component\").$$render(A,{target:d,theme_mode:m,props:c.content.props,type:c.content.component,components:u,value:c.content.value,display_icon_button_wrapper_top_corner:h>0&&x,i18n:n,upload:i,_fetch:l,allow_file_downloads:_},{},{})}`:`${c.type===\"component\"&&c.content.component===\"file\"?`<div class=\"file-container svelte-ulpe0d\"><div class=\"file-icon svelte-ulpe0d\">${C(vt,\"File\").$$render(A,{},{},{})}</div> <div class=\"file-info svelte-ulpe0d\"><a data-testid=\"chatbot-file\" class=\"file-link svelte-ulpe0d\"${k(\"href\",c.content.value.url,0)} target=\"_blank\"${k(\"download\",window.__is_colab__?null:c.content.value?.orig_name||c.content.value?.path.split(\"/\").pop()||\"file\",0)}><span class=\"file-name svelte-ulpe0d\">${I(c.content.value?.orig_name||c.content.value?.path.split(\"/\").pop()||\"file\")}</span></a> <span class=\"file-type svelte-ulpe0d\">${I((c.content.value?.orig_name||c.content.value?.path||\"\").split(\".\").pop().toUpperCase())}</span></div></div>`:\"\"}`}`}`}),ge={code:\".thought-group.svelte-1qn6r4f{background:var(--background-fill-primary);border:1px solid var(--border-color-primary);border-radius:var(--radius-sm);padding:var(--spacing-md);margin:var(--spacing-md) 0;font-size:var(--text-sm)}.children.svelte-1qn6r4f .thought-group{border:none;margin:0;padding-bottom:0}.children.svelte-1qn6r4f{padding-left:var(--spacing-md)}.title.svelte-1qn6r4f{display:flex;align-items:center;color:var(--body-text-color);cursor:pointer;width:100%}.title.svelte-1qn6r4f .md{font-size:var(--text-sm) !important}.content.svelte-1qn6r4f,.content-preview.svelte-1qn6r4f{overflow-wrap:break-word;word-break:break-word;margin-left:var(--spacing-lg);margin-bottom:var(--spacing-sm)}.content-preview.svelte-1qn6r4f{position:relative;max-height:calc(5 * 1.5em);overflow-y:auto;overscroll-behavior:contain;cursor:default}.content.svelte-1qn6r4f *,.content-preview.svelte-1qn6r4f *{font-size:var(--text-sm);color:var(--body-text-color)}.thought-group.svelte-1qn6r4f .thought:not(.nested){border:none;background:none}.duration.svelte-1qn6r4f{color:var(--body-text-color-subdued);font-size:var(--text-sm);margin-left:var(--size-1)}.arrow.svelte-1qn6r4f{opacity:0.8;width:var(--size-8);height:var(--size-8);display:flex;align-items:center;justify-content:center}.arrow.svelte-1qn6r4f button{background-color:transparent}.loading-spinner.svelte-1qn6r4f{display:inline-block;width:12px;height:12px;border:2px solid var(--body-text-color);border-radius:50%;border-top-color:transparent;animation:svelte-1qn6r4f-spin 1s linear infinite;margin:0 var(--size-1) -1px var(--size-2);opacity:0.8}@keyframes svelte-1qn6r4f-spin{to{transform:rotate(360deg)}}.thought-group.svelte-1qn6r4f .message-content{opacity:0.8}\",map:'{\"version\":3,\"file\":\"Thought.svelte\",\"sources\":[\"Thought.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import MessageContent from \\\\\"./MessageContent.svelte\\\\\";\\\\nimport { DropdownCircularArrow } from \\\\\"@gradio/icons\\\\\";\\\\nimport { IconButton } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { slide } from \\\\\"svelte/transition\\\\\";\\\\nimport { MarkdownCode as Markdown } from \\\\\"@gradio/markdown-code\\\\\";\\\\nexport let thought;\\\\nexport let rtl = false;\\\\nexport let sanitize_html;\\\\nexport let latex_delimiters;\\\\nexport let render_markdown;\\\\nexport let _components;\\\\nexport let upload;\\\\nexport let thought_index;\\\\nexport let target;\\\\nexport let theme_mode;\\\\nexport let _fetch;\\\\nexport let scroll;\\\\nexport let allow_file_downloads;\\\\nexport let display_consecutive_in_same_bubble;\\\\nexport let i18n;\\\\nexport let line_breaks;\\\\nexport let allow_tags = false;\\\\nfunction is_thought_node(msg) {\\\\n    return \\\\\"children\\\\\" in msg;\\\\n}\\\\nlet thought_node;\\\\nlet expanded = false;\\\\nlet user_expanded_toggled = false;\\\\nlet content_preview_element;\\\\nlet user_is_scrolling = false;\\\\n$: thought_node = {\\\\n    ...thought,\\\\n    children: is_thought_node(thought) ? thought.children : []\\\\n};\\\\n$: if (!user_expanded_toggled) {\\\\n    expanded = thought_node?.metadata?.status !== \\\\\"done\\\\\";\\\\n}\\\\nfunction toggleExpanded() {\\\\n    expanded = !expanded;\\\\n    user_expanded_toggled = true;\\\\n}\\\\nfunction scrollToBottom() {\\\\n    if (content_preview_element && !user_is_scrolling) {\\\\n        content_preview_element.scrollTop = content_preview_element.scrollHeight;\\\\n    }\\\\n}\\\\nfunction handleScroll() {\\\\n    if (content_preview_element) {\\\\n        const is_at_bottom = content_preview_element.scrollHeight - content_preview_element.scrollTop <= content_preview_element.clientHeight + 10;\\\\n        if (!is_at_bottom) {\\\\n            user_is_scrolling = true;\\\\n        }\\\\n    }\\\\n}\\\\n$: if (thought_node.content && content_preview_element && thought_node.metadata?.status !== \\\\\"done\\\\\") {\\\\n    setTimeout(scrollToBottom, 0);\\\\n}\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"thought-group\\\\\">\\\\n\\\\t<div\\\\n\\\\t\\\\tclass=\\\\\"title\\\\\"\\\\n\\\\t\\\\tclass:expanded\\\\n\\\\t\\\\ton:click|stopPropagation={toggleExpanded}\\\\n\\\\t\\\\taria-busy={thought_node.content === \\\\\"\\\\\" || thought_node.content === null}\\\\n\\\\t\\\\trole=\\\\\"button\\\\\"\\\\n\\\\t\\\\ttabindex=\\\\\"0\\\\\"\\\\n\\\\t\\\\ton:keydown={(e) => e.key === \\\\\"Enter\\\\\" && toggleExpanded()}\\\\n\\\\t>\\\\n\\\\t\\\\t<span\\\\n\\\\t\\\\t\\\\tclass=\\\\\"arrow\\\\\"\\\\n\\\\t\\\\t\\\\tstyle:transform={expanded ? \\\\\"rotate(180deg)\\\\\" : \\\\\"rotate(0deg)\\\\\"}\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t<IconButton Icon={DropdownCircularArrow} />\\\\n\\\\t\\\\t</span>\\\\n\\\\t\\\\t<Markdown\\\\n\\\\t\\\\t\\\\tmessage={thought_node.metadata?.title || \\\\\"\\\\\"}\\\\n\\\\t\\\\t\\\\t{render_markdown}\\\\n\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t\\\\t{allow_tags}\\\\n\\\\t\\\\t/>\\\\n\\\\t\\\\t{#if thought_node.metadata?.status === \\\\\"pending\\\\\"}\\\\n\\\\t\\\\t\\\\t<span class=\\\\\"loading-spinner\\\\\"></span>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t\\\\t{#if thought_node?.metadata?.log || thought_node?.metadata?.duration}\\\\n\\\\t\\\\t\\\\t<span class=\\\\\"duration\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t{#if thought_node.metadata.log}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{thought_node.metadata.log}\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t{#if thought_node.metadata.duration !== undefined}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t({#if Number.isInteger(thought_node.metadata.duration)}{thought_node\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t.metadata\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t.duration}s{:else if thought_node.metadata.duration >= 0.1}{thought_node.metadata.duration.toFixed(\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t1\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t)}s{:else}{(thought_node.metadata.duration * 1000).toFixed(\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t1\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t)}ms{/if})\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t</div>\\\\n\\\\n\\\\t{#if expanded}\\\\n\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\tclass:content={expanded}\\\\n\\\\t\\\\t\\\\tclass:content-preview={!expanded &&\\\\n\\\\t\\\\t\\\\t\\\\tthought_node.metadata?.status !== \\\\\"done\\\\\"}\\\\n\\\\t\\\\t\\\\tbind:this={content_preview_element}\\\\n\\\\t\\\\t\\\\ton:scroll={handleScroll}\\\\n\\\\t\\\\t\\\\ttransition:slide\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t<MessageContent\\\\n\\\\t\\\\t\\\\t\\\\tmessage={thought_node}\\\\n\\\\t\\\\t\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t\\\\t\\\\t{allow_tags}\\\\n\\\\t\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t\\\\t{render_markdown}\\\\n\\\\t\\\\t\\\\t\\\\t{_components}\\\\n\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\t{thought_index}\\\\n\\\\t\\\\t\\\\t\\\\t{target}\\\\n\\\\t\\\\t\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\t\\\\t\\\\t{_fetch}\\\\n\\\\t\\\\t\\\\t\\\\t{scroll}\\\\n\\\\t\\\\t\\\\t\\\\t{allow_file_downloads}\\\\n\\\\t\\\\t\\\\t\\\\t{display_consecutive_in_same_bubble}\\\\n\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\n\\\\t\\\\t\\\\t{#if thought_node.children?.length > 0}\\\\n\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"children\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#each thought_node.children as child, index}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<svelte:self\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tthought={child}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{rtl}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{render_markdown}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{_components}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tthought_index={thought_index + 1}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{target}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{_fetch}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{scroll}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{allow_file_downloads}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{display_consecutive_in_same_bubble}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.thought-group {\\\\n\\\\t\\\\tbackground: var(--background-fill-primary);\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--radius-sm);\\\\n\\\\t\\\\tpadding: var(--spacing-md);\\\\n\\\\t\\\\tmargin: var(--spacing-md) 0;\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t}\\\\n\\\\n\\\\t.children :global(.thought-group) {\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tmargin: 0;\\\\n\\\\t\\\\tpadding-bottom: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.children {\\\\n\\\\t\\\\tpadding-left: var(--spacing-md);\\\\n\\\\t}\\\\n\\\\n\\\\t.title {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.title :global(.md) {\\\\n\\\\t\\\\tfont-size: var(--text-sm) !important;\\\\n\\\\t}\\\\n\\\\n\\\\t.content,\\\\n\\\\t.content-preview {\\\\n\\\\t\\\\toverflow-wrap: break-word;\\\\n\\\\t\\\\tword-break: break-word;\\\\n\\\\t\\\\tmargin-left: var(--spacing-lg);\\\\n\\\\t\\\\tmargin-bottom: var(--spacing-sm);\\\\n\\\\t}\\\\n\\\\n\\\\t.content-preview {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tmax-height: calc(5 * 1.5em);\\\\n\\\\t\\\\toverflow-y: auto;\\\\n\\\\t\\\\toverscroll-behavior: contain;\\\\n\\\\t\\\\tcursor: default;\\\\n\\\\t}\\\\n\\\\n\\\\t.content :global(*),\\\\n\\\\t.content-preview :global(*) {\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t}\\\\n\\\\n\\\\t.thought-group :global(.thought:not(.nested)) {\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tbackground: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.duration {\\\\n\\\\t\\\\tcolor: var(--body-text-color-subdued);\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t\\\\tmargin-left: var(--size-1);\\\\n\\\\t}\\\\n\\\\n\\\\t.arrow {\\\\n\\\\t\\\\topacity: 0.8;\\\\n\\\\t\\\\twidth: var(--size-8);\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t}\\\\n\\\\n\\\\t.arrow :global(button) {\\\\n\\\\t\\\\tbackground-color: transparent;\\\\n\\\\t}\\\\n\\\\n\\\\t.loading-spinner {\\\\n\\\\t\\\\tdisplay: inline-block;\\\\n\\\\t\\\\twidth: 12px;\\\\n\\\\t\\\\theight: 12px;\\\\n\\\\t\\\\tborder: 2px solid var(--body-text-color);\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tborder-top-color: transparent;\\\\n\\\\t\\\\tanimation: spin 1s linear infinite;\\\\n\\\\t\\\\tmargin: 0 var(--size-1) -1px var(--size-2);\\\\n\\\\t\\\\topacity: 0.8;\\\\n\\\\t}\\\\n\\\\n\\\\t@keyframes spin {\\\\n\\\\t\\\\tto {\\\\n\\\\t\\\\t\\\\ttransform: rotate(360deg);\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.thought-group :global(.message-content) {\\\\n\\\\t\\\\topacity: 0.8;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAgKC,6BAAe,CACd,UAAU,CAAE,IAAI,yBAAyB,CAAC,CAC1C,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,OAAO,CAAE,IAAI,YAAY,CAAC,CAC1B,MAAM,CAAE,IAAI,YAAY,CAAC,CAAC,CAAC,CAC3B,SAAS,CAAE,IAAI,SAAS,CACzB,CAEA,wBAAS,CAAS,cAAgB,CACjC,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,CAAC,CACT,cAAc,CAAE,CACjB,CAEA,wBAAU,CACT,YAAY,CAAE,IAAI,YAAY,CAC/B,CAEA,qBAAO,CACN,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,MAAM,CAAE,OAAO,CACf,KAAK,CAAE,IACR,CAEA,qBAAM,CAAS,GAAK,CACnB,SAAS,CAAE,IAAI,SAAS,CAAC,CAAC,UAC3B,CAEA,uBAAQ,CACR,+BAAiB,CAChB,aAAa,CAAE,UAAU,CACzB,UAAU,CAAE,UAAU,CACtB,WAAW,CAAE,IAAI,YAAY,CAAC,CAC9B,aAAa,CAAE,IAAI,YAAY,CAChC,CAEA,+BAAiB,CAChB,QAAQ,CAAE,QAAQ,CAClB,UAAU,CAAE,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAC3B,UAAU,CAAE,IAAI,CAChB,mBAAmB,CAAE,OAAO,CAC5B,MAAM,CAAE,OACT,CAEA,uBAAQ,CAAS,CAAE,CACnB,+BAAgB,CAAS,CAAG,CAC3B,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CAEA,6BAAc,CAAS,qBAAuB,CAC7C,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,IACb,CAEA,wBAAU,CACT,KAAK,CAAE,IAAI,yBAAyB,CAAC,CACrC,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,WAAW,CAAE,IAAI,QAAQ,CAC1B,CAEA,qBAAO,CACN,OAAO,CAAE,GAAG,CACZ,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAClB,CAEA,qBAAM,CAAS,MAAQ,CACtB,gBAAgB,CAAE,WACnB,CAEA,+BAAiB,CAChB,OAAO,CAAE,YAAY,CACrB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,iBAAiB,CAAC,CACxC,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,WAAW,CAC7B,SAAS,CAAE,mBAAI,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAClC,MAAM,CAAE,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,CAC1C,OAAO,CAAE,GACV,CAEA,WAAW,mBAAK,CACf,EAAG,CACF,SAAS,CAAE,OAAO,MAAM,CACzB,CACD,CAEA,6BAAc,CAAS,gBAAkB,CACxC,OAAO,CAAE,GACV\"}'};function pe(A){return\"children\"in A}const qt=M((A,t,e,s)=>{let{thought:a}=t,{rtl:o=!1}=t,{sanitize_html:l}=t,{latex_delimiters:n}=t,{render_markdown:r}=t,{_components:i}=t,{upload:d}=t,{thought_index:m}=t,{target:u}=t,{theme_mode:f}=t,{_fetch:g}=t,{scroll:_}=t,{allow_file_downloads:x}=t,{display_consecutive_in_same_bubble:h}=t,{i18n:p}=t,{line_breaks:c}=t,{allow_tags:E=!1}=t,v,B=!1,O;function z(){}return t.thought===void 0&&e.thought&&a!==void 0&&e.thought(a),t.rtl===void 0&&e.rtl&&o!==void 0&&e.rtl(o),t.sanitize_html===void 0&&e.sanitize_html&&l!==void 0&&e.sanitize_html(l),t.latex_delimiters===void 0&&e.latex_delimiters&&n!==void 0&&e.latex_delimiters(n),t.render_markdown===void 0&&e.render_markdown&&r!==void 0&&e.render_markdown(r),t._components===void 0&&e._components&&i!==void 0&&e._components(i),t.upload===void 0&&e.upload&&d!==void 0&&e.upload(d),t.thought_index===void 0&&e.thought_index&&m!==void 0&&e.thought_index(m),t.target===void 0&&e.target&&u!==void 0&&e.target(u),t.theme_mode===void 0&&e.theme_mode&&f!==void 0&&e.theme_mode(f),t._fetch===void 0&&e._fetch&&g!==void 0&&e._fetch(g),t.scroll===void 0&&e.scroll&&_!==void 0&&e.scroll(_),t.allow_file_downloads===void 0&&e.allow_file_downloads&&x!==void 0&&e.allow_file_downloads(x),t.display_consecutive_in_same_bubble===void 0&&e.display_consecutive_in_same_bubble&&h!==void 0&&e.display_consecutive_in_same_bubble(h),t.i18n===void 0&&e.i18n&&p!==void 0&&e.i18n(p),t.line_breaks===void 0&&e.line_breaks&&c!==void 0&&e.line_breaks(c),t.allow_tags===void 0&&e.allow_tags&&E!==void 0&&e.allow_tags(E),A.css.add(ge),v={...a,children:pe(a)?a.children:[]},B=v?.metadata?.status!==\"done\",v.content&&O&&v.metadata?.status!==\"done\"&&setTimeout(z,0),`<div class=\"thought-group svelte-1qn6r4f\"><div class=\"${[\"title svelte-1qn6r4f\",B?\"expanded\":\"\"].join(\" \").trim()}\"${k(\"aria-busy\",v.content===\"\"||v.content===null,0)} role=\"button\" tabindex=\"0\"><span class=\"arrow svelte-1qn6r4f\"${Ct({transform:B?\"rotate(180deg)\":\"rotate(0deg)\"})}>${C(D,\"IconButton\").$$render(A,{Icon:Lt},{},{})}</span> ${C(_t,\"Markdown\").$$render(A,{message:v.metadata?.title||\"\",render_markdown:r,latex_delimiters:n,sanitize_html:l,allow_tags:E},{},{})} ${v.metadata?.status===\"pending\"?'<span class=\"loading-spinner svelte-1qn6r4f\"></span>':\"\"} ${v?.metadata?.log||v?.metadata?.duration?`<span class=\"duration svelte-1qn6r4f\">${v.metadata.log?`${I(v.metadata.log)}`:\"\"} ${v.metadata.duration!==void 0?`(${Number.isInteger(v.metadata.duration)?`${I(v.metadata.duration)}s`:`${v.metadata.duration>=.1?`${I(v.metadata.duration.toFixed(1))}s`:`${I((v.metadata.duration*1e3).toFixed(1))}ms`}`})`:\"\"}</span>`:\"\"}</div> ${B?`<div class=\"${[\"svelte-1qn6r4f\",(B?\"content\":\"\")+\" \"+(!B&&v.metadata?.status!==\"done\"?\"content-preview\":\"\")].join(\" \").trim()}\"${k(\"this\",O,0)}>${C(St,\"MessageContent\").$$render(A,{message:v,sanitize_html:l,allow_tags:E,latex_delimiters:n,render_markdown:r,_components:i,upload:d,thought_index:m,target:u,theme_mode:f,_fetch:g,scroll:_,allow_file_downloads:x,display_consecutive_in_same_bubble:h,i18n:p,line_breaks:c},{},{})} ${v.children?.length>0?`<div class=\"children svelte-1qn6r4f\">${ot(v.children,(Y,T)=>`${C(qt,\"svelte:self\").$$render(A,{thought:Y,rtl:o,sanitize_html:l,latex_delimiters:n,render_markdown:r,_components:i,upload:d,thought_index:m+1,target:u,theme_mode:f,_fetch:g,scroll:_,allow_file_downloads:x,display_consecutive_in_same_bubble:h,i18n:p,line_breaks:c},{},{})}`)}</div>`:\"\"}</div>`:\"\"} </div>`}),he={code:\".message.svelte-1csv61q.svelte-1csv61q{position:relative;width:100%;margin-top:var(--spacing-sm)}.message.display_consecutive_in_same_bubble.svelte-1csv61q.svelte-1csv61q{margin-top:0}.avatar-container.svelte-1csv61q.svelte-1csv61q{flex-shrink:0;border-radius:50%;border:1px solid var(--border-color-primary);overflow:hidden}.avatar-container.svelte-1csv61q img{object-fit:cover}.flex-wrap.svelte-1csv61q.svelte-1csv61q{display:flex;flex-direction:column;width:calc(100% - var(--spacing-xxl));max-width:100%;color:var(--body-text-color);font-size:var(--chatbot-text-size);overflow-wrap:break-word;width:100%;height:100%}.component.svelte-1csv61q.svelte-1csv61q{padding:0;border-radius:var(--radius-md);width:fit-content;overflow:hidden}.component.gallery.svelte-1csv61q.svelte-1csv61q{border:none}.bot.svelte-1csv61q.svelte-1csv61q:has(.model3D),.user.svelte-1csv61q.svelte-1csv61q:has(.model3D){border:none;max-width:75%}.message-row.svelte-1csv61q .svelte-1csv61q:not(.avatar-container) img{margin:var(--size-2);max-height:300px}.file-pil.svelte-1csv61q.svelte-1csv61q{display:block;width:fit-content;padding:var(--spacing-sm) var(--spacing-lg);border-radius:var(--radius-md);background:var(--background-fill-secondary);color:var(--body-text-color);text-decoration:none;margin:0;font-family:var(--font-mono);font-size:var(--text-sm)}.file.svelte-1csv61q.svelte-1csv61q{width:auto !important;max-width:fit-content !important}@media(max-width: 600px) or (max-width: 480px){.component.svelte-1csv61q.svelte-1csv61q{width:100%}}.message.svelte-1csv61q .prose{font-size:var(--chatbot-text-size)}.message-bubble-border.svelte-1csv61q.svelte-1csv61q{border-width:1px;border-radius:var(--radius-md)}.panel-full-width.svelte-1csv61q.svelte-1csv61q{width:100%}.message-markdown-disabled.svelte-1csv61q.svelte-1csv61q{white-space:pre-line}.user.svelte-1csv61q.svelte-1csv61q{border-radius:var(--radius-md);align-self:flex-end;border-bottom-right-radius:0;box-shadow:var(--shadow-drop);border:1px solid var(--border-color-accent-subdued);background-color:var(--color-accent-soft);padding:var(--spacing-sm) var(--spacing-xl)}.bot.svelte-1csv61q.svelte-1csv61q{border:1px solid var(--border-color-primary);border-radius:var(--radius-md);border-color:var(--border-color-primary);background-color:var(--background-fill-secondary);box-shadow:var(--shadow-drop);align-self:flex-start;text-align:right;border-bottom-left-radius:0;padding:var(--spacing-sm) var(--spacing-xl)}.bot.svelte-1csv61q.svelte-1csv61q:has(.table-wrap){border:none;box-shadow:none;background:none}.panel.svelte-1csv61q .user.svelte-1csv61q *{text-align:right}.message-row.svelte-1csv61q.svelte-1csv61q{display:flex;position:relative}.bubble.svelte-1csv61q.svelte-1csv61q{margin:calc(var(--spacing-xl) * 2);margin-bottom:var(--spacing-xl)}.bubble.user-row.svelte-1csv61q.svelte-1csv61q{align-self:flex-end;max-width:calc(100% - var(--spacing-xl) * 6)}.bubble.bot-row.svelte-1csv61q.svelte-1csv61q{align-self:flex-start;max-width:calc(100% - var(--spacing-xl) * 6)}.bubble.svelte-1csv61q .user-row.svelte-1csv61q{flex-direction:row;justify-content:flex-end}.bubble.svelte-1csv61q .with_avatar.user-row.svelte-1csv61q{margin-right:calc(var(--spacing-xl) * 2) !important}.bubble.svelte-1csv61q .with_avatar.bot-row.svelte-1csv61q{margin-left:calc(var(--spacing-xl) * 2) !important}.bubble.svelte-1csv61q .with_opposite_avatar.user-row.svelte-1csv61q{margin-left:calc(var(--spacing-xxl) + 35px + var(--spacing-xxl))}.panel.svelte-1csv61q.svelte-1csv61q{margin:0;padding:calc(var(--spacing-lg) * 2) calc(var(--spacing-lg) * 2)}.panel.bot-row.svelte-1csv61q.svelte-1csv61q{background:var(--background-fill-secondary)}.panel.svelte-1csv61q .with_avatar.svelte-1csv61q{padding-left:calc(var(--spacing-xl) * 2) !important;padding-right:calc(var(--spacing-xl) * 2) !important}.panel.svelte-1csv61q .panel-full-width.svelte-1csv61q{width:100%}.panel.svelte-1csv61q .user.svelte-1csv61q *{text-align:right}.flex-wrap.svelte-1csv61q.svelte-1csv61q{display:flex;flex-direction:column;max-width:100%;color:var(--body-text-color);font-size:var(--chatbot-text-size);overflow-wrap:break-word}@media(max-width: 480px){.user-row.bubble.svelte-1csv61q.svelte-1csv61q{align-self:flex-end}.bot-row.bubble.svelte-1csv61q.svelte-1csv61q{align-self:flex-start}.message.svelte-1csv61q.svelte-1csv61q{width:100%}}.avatar-container.svelte-1csv61q.svelte-1csv61q{align-self:flex-start;position:relative;display:flex;justify-content:flex-start;align-items:flex-start;width:35px;height:35px;flex-shrink:0;bottom:0;border-radius:50%;border:1px solid var(--border-color-primary)}.user-row.svelte-1csv61q>.avatar-container.svelte-1csv61q{order:2}.user-row.bubble.svelte-1csv61q>.avatar-container.svelte-1csv61q{margin-left:var(--spacing-xxl)}.bot-row.bubble.svelte-1csv61q>.avatar-container.svelte-1csv61q{margin-left:var(--spacing-xxl)}.panel.user-row.svelte-1csv61q>.avatar-container.svelte-1csv61q{order:0}.bot-row.bubble.svelte-1csv61q>.avatar-container.svelte-1csv61q{margin-right:var(--spacing-xxl);margin-left:0}.avatar-container.svelte-1csv61q:not(.thumbnail-item) img{width:100%;height:100%;object-fit:cover;border-radius:50%;padding:var(--size-1-5)}.selectable.svelte-1csv61q.svelte-1csv61q{cursor:pointer}@keyframes svelte-1csv61q-dot-flashing{0%{opacity:0.8}50%{opacity:0.5}100%{opacity:0.8}}.message.svelte-1csv61q .preview{object-fit:contain;width:95%;max-height:93%}.image-preview.svelte-1csv61q.svelte-1csv61q{position:absolute;z-index:999;left:0;top:0;width:100%;height:100%;overflow:auto;background-color:rgba(0, 0, 0, 0.9);display:flex;justify-content:center;align-items:center}.image-preview.svelte-1csv61q svg{stroke:white}.image-preview-close-button.svelte-1csv61q.svelte-1csv61q{position:absolute;top:10px;right:10px;background:none;border:none;font-size:1.5em;cursor:pointer;height:30px;width:30px;padding:3px;background:var(--bg-color);box-shadow:var(--shadow-drop);border:1px solid var(--button-secondary-border-color);border-radius:var(--radius-lg)}.message.svelte-1csv61q>div.svelte-1csv61q{width:100%}.html.svelte-1csv61q.svelte-1csv61q{padding:0;border:none;background:none}.panel.svelte-1csv61q .bot.svelte-1csv61q,.panel.svelte-1csv61q .user.svelte-1csv61q{border:none;box-shadow:none;background-color:var(--background-fill-secondary)}textarea.svelte-1csv61q.svelte-1csv61q{background:none;border-radius:var(--radius-lg);border:none;display:block;max-width:100%}.user.svelte-1csv61q textarea.svelte-1csv61q{border-bottom-right-radius:0}.bot.svelte-1csv61q textarea.svelte-1csv61q{border-bottom-left-radius:0}.user.svelte-1csv61q textarea.svelte-1csv61q:focus{outline:2px solid var(--border-color-accent)}.bot.svelte-1csv61q textarea.svelte-1csv61q:focus{outline:2px solid var(--border-color-primary)}.panel.user-row.svelte-1csv61q.svelte-1csv61q{background-color:var(--color-accent-soft)}.panel.svelte-1csv61q .user-row.svelte-1csv61q,.panel.svelte-1csv61q .bot-row.svelte-1csv61q{align-self:flex-start}.panel.svelte-1csv61q .user.svelte-1csv61q *,.panel.svelte-1csv61q .bot.svelte-1csv61q *{text-align:left}.panel.svelte-1csv61q .user.svelte-1csv61q{background-color:var(--color-accent-soft)}.panel.svelte-1csv61q .user-row.svelte-1csv61q{background-color:var(--color-accent-soft);align-self:flex-start}.panel.svelte-1csv61q .message.svelte-1csv61q{margin-bottom:var(--spacing-md)}\",map:'{\"version\":3,\"file\":\"Message.svelte\",\"sources\":[\"Message.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { is_component_message } from \\\\\"../shared/utils\\\\\";\\\\nimport { Image } from \\\\\"@gradio/image/shared\\\\\";\\\\nimport ButtonPanel from \\\\\"./ButtonPanel.svelte\\\\\";\\\\nimport MessageContent from \\\\\"./MessageContent.svelte\\\\\";\\\\nimport Thought from \\\\\"./Thought.svelte\\\\\";\\\\nexport let value;\\\\nexport let avatar_img;\\\\nexport let opposite_avatar_img = null;\\\\nexport let role = \\\\\"user\\\\\";\\\\nexport let messages = [];\\\\nexport let layout;\\\\nexport let render_markdown;\\\\nexport let latex_delimiters;\\\\nexport let sanitize_html;\\\\nexport let selectable;\\\\nexport let _fetch;\\\\nexport let rtl;\\\\nexport let dispatch;\\\\nexport let i18n;\\\\nexport let line_breaks;\\\\nexport let upload;\\\\nexport let target;\\\\nexport let theme_mode;\\\\nexport let _components;\\\\nexport let i;\\\\nexport let show_copy_button;\\\\nexport let generating;\\\\nexport let feedback_options;\\\\nexport let show_like;\\\\nexport let show_edit;\\\\nexport let show_retry;\\\\nexport let show_undo;\\\\nexport let msg_format;\\\\nexport let handle_action;\\\\nexport let scroll;\\\\nexport let allow_file_downloads;\\\\nexport let in_edit_mode;\\\\nexport let edit_messages;\\\\nexport let display_consecutive_in_same_bubble;\\\\nexport let current_feedback = null;\\\\nexport let allow_tags = false;\\\\nexport let watermark = null;\\\\nlet messageElements = [];\\\\nlet previous_edit_mode = false;\\\\nlet message_widths = Array(messages.length).fill(160);\\\\nlet message_heights = Array(messages.length).fill(0);\\\\n$: if (in_edit_mode && !previous_edit_mode) {\\\\n    const offset = messageElements.length - messages.length;\\\\n    for (let idx = offset; idx < messageElements.length; idx++) {\\\\n        if (idx >= 0) {\\\\n            message_widths[idx - offset] = messageElements[idx]?.clientWidth;\\\\n            message_heights[idx - offset] = messageElements[idx]?.clientHeight;\\\\n        }\\\\n    }\\\\n}\\\\nfunction handle_select(i2, message) {\\\\n    dispatch(\\\\\"select\\\\\", {\\\\n        index: message.index,\\\\n        value: message.content\\\\n    });\\\\n}\\\\nfunction get_message_label_data(message) {\\\\n    if (message.type === \\\\\"text\\\\\") {\\\\n        return message.content;\\\\n    }\\\\n    else if (message.type === \\\\\"component\\\\\" && message.content.component === \\\\\"file\\\\\") {\\\\n        if (Array.isArray(message.content.value)) {\\\\n            return `file of extension type: ${message.content.value[0].orig_name?.split(\\\\\".\\\\\").pop()}`;\\\\n        }\\\\n        return `file of extension type: ${message.content.value?.orig_name?.split(\\\\\".\\\\\").pop()}` + (message.content.value?.orig_name ?? \\\\\"\\\\\");\\\\n    }\\\\n    return `a component of type ${message.content.component ?? \\\\\"unknown\\\\\"}`;\\\\n}\\\\nlet button_panel_props;\\\\n$: button_panel_props = {\\\\n    handle_action,\\\\n    likeable: show_like,\\\\n    feedback_options,\\\\n    show_retry,\\\\n    show_undo,\\\\n    show_edit,\\\\n    in_edit_mode,\\\\n    generating,\\\\n    show_copy_button,\\\\n    message: msg_format === \\\\\"tuples\\\\\" ? messages[0] : messages,\\\\n    position: role === \\\\\"user\\\\\" ? \\\\\"right\\\\\" : \\\\\"left\\\\\",\\\\n    avatar: avatar_img,\\\\n    layout,\\\\n    dispatch,\\\\n    current_feedback,\\\\n    watermark\\\\n};\\\\n<\\/script>\\\\n\\\\n<div\\\\n\\\\tclass=\\\\\"message-row {layout} {role}-row\\\\\"\\\\n\\\\tclass:with_avatar={avatar_img !== null}\\\\n\\\\tclass:with_opposite_avatar={opposite_avatar_img !== null}\\\\n>\\\\n\\\\t{#if avatar_img !== null}\\\\n\\\\t\\\\t<div class=\\\\\"avatar-container\\\\\">\\\\n\\\\t\\\\t\\\\t<Image class=\\\\\"avatar-image\\\\\" src={avatar_img?.url} alt=\\\\\"{role} avatar\\\\\" />\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n\\\\t<div\\\\n\\\\t\\\\tclass:role\\\\n\\\\t\\\\tclass=\\\\\"flex-wrap\\\\\"\\\\n\\\\t\\\\tclass:component-wrap={messages[0].type === \\\\\"component\\\\\"}\\\\n\\\\t>\\\\n\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\tclass:message={display_consecutive_in_same_bubble}\\\\n\\\\t\\\\t\\\\tclass={display_consecutive_in_same_bubble ? role : \\\\\"\\\\\"}\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t{#each messages as message, thought_index}\\\\n\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"message {!display_consecutive_in_same_bubble ? role : \\'\\'}\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass:panel-full-width={true}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass:message-markdown-disabled={!render_markdown}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass:component={message.type === \\\\\"component\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass:html={is_component_message(message) &&\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmessage.content.component === \\\\\"html\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass:thought={thought_index > 0}\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#if in_edit_mode && message.type === \\\\\"text\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<!-- svelte-ignore a11y-autofocus -->\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<textarea\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"edit-textarea\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle:width={`max(${message_widths[thought_index]}px, 160px)`}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle:min-height={`${message_heights[thought_index]}px`}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tautofocus\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tbind:value={edit_messages[thought_index]}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<!-- svelte-ignore a11y-no-static-element-interactions -->\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdata-testid={role}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:latest={i === value.length - 1}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:message-markdown-disabled={!render_markdown}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle:user-select=\\\\\"text\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:selectable\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle:cursor={selectable ? \\\\\"pointer\\\\\" : \\\\\"auto\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle:text-align={rtl ? \\\\\"right\\\\\" : \\\\\"left\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tbind:this={messageElements[thought_index]}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => handle_select(i, message)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:keydown={(e) => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tif (e.key === \\\\\"Enter\\\\\") {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\thandle_select(i, message);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdir={rtl ? \\\\\"rtl\\\\\" : \\\\\"ltr\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-label={role +\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\\"\\'s message: \\\\\" +\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tget_message_label_data(message)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if message?.metadata?.title}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Thought\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tthought={message}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{rtl}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{allow_tags}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{render_markdown}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{_components}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{thought_index}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{target}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{_fetch}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{scroll}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{allow_file_downloads}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{display_consecutive_in_same_bubble}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<MessageContent\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{message}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{allow_tags}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{render_markdown}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{_components}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{thought_index}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{target}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{_fetch}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{scroll}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{allow_file_downloads}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{display_consecutive_in_same_bubble}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t{#if layout === \\\\\"panel\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<ButtonPanel\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{...button_panel_props}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{current_feedback}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{watermark}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:copy={(e) => dispatch(\\\\\"copy\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t</div>\\\\n\\\\t</div>\\\\n</div>\\\\n\\\\n{#if layout === \\\\\"bubble\\\\\"}\\\\n\\\\t<ButtonPanel {...button_panel_props} {i18n} />\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.message {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tmargin-top: var(--spacing-sm);\\\\n\\\\t}\\\\n\\\\n\\\\t.message.display_consecutive_in_same_bubble {\\\\n\\\\t\\\\tmargin-top: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t/* avatar styles */\\\\n\\\\t.avatar-container {\\\\n\\\\t\\\\tflex-shrink: 0;\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}\\\\n\\\\n\\\\t.avatar-container :global(img) {\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t}\\\\n\\\\n\\\\t/* message wrapper */\\\\n\\\\t.flex-wrap {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\twidth: calc(100% - var(--spacing-xxl));\\\\n\\\\t\\\\tmax-width: 100%;\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\tfont-size: var(--chatbot-text-size);\\\\n\\\\t\\\\toverflow-wrap: break-word;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.component {\\\\n\\\\t\\\\tpadding: 0;\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t\\\\twidth: fit-content;\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}\\\\n\\\\n\\\\t.component.gallery {\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.bot:has(.model3D),\\\\n\\\\t.user:has(.model3D) {\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tmax-width: 75%;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-row :not(.avatar-container) :global(img) {\\\\n\\\\t\\\\tmargin: var(--size-2);\\\\n\\\\t\\\\tmax-height: 300px;\\\\n\\\\t}\\\\n\\\\n\\\\t.file-pil {\\\\n\\\\t\\\\tdisplay: block;\\\\n\\\\t\\\\twidth: fit-content;\\\\n\\\\t\\\\tpadding: var(--spacing-sm) var(--spacing-lg);\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t\\\\tbackground: var(--background-fill-secondary);\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\ttext-decoration: none;\\\\n\\\\t\\\\tmargin: 0;\\\\n\\\\t\\\\tfont-family: var(--font-mono);\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t}\\\\n\\\\n\\\\t.file {\\\\n\\\\t\\\\twidth: auto !important;\\\\n\\\\t\\\\tmax-width: fit-content !important;\\\\n\\\\t}\\\\n\\\\n\\\\t@media (max-width: 600px) or (max-width: 480px) {\\\\n\\\\t\\\\t.component {\\\\n\\\\t\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.message :global(.prose) {\\\\n\\\\t\\\\tfont-size: var(--chatbot-text-size);\\\\n\\\\t}\\\\n\\\\n\\\\t.message-bubble-border {\\\\n\\\\t\\\\tborder-width: 1px;\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel-full-width {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\t.message-markdown-disabled {\\\\n\\\\t\\\\twhite-space: pre-line;\\\\n\\\\t}\\\\n\\\\n\\\\t.user {\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t\\\\talign-self: flex-end;\\\\n\\\\t\\\\tborder-bottom-right-radius: 0;\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop);\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-accent-subdued);\\\\n\\\\t\\\\tbackground-color: var(--color-accent-soft);\\\\n\\\\t\\\\tpadding: var(--spacing-sm) var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.bot {\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t\\\\tborder-color: var(--border-color-primary);\\\\n\\\\t\\\\tbackground-color: var(--background-fill-secondary);\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop);\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\ttext-align: right;\\\\n\\\\t\\\\tborder-bottom-left-radius: 0;\\\\n\\\\t\\\\tpadding: var(--spacing-sm) var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.bot:has(.table-wrap) {\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tbox-shadow: none;\\\\n\\\\t\\\\tbackground: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .user :global(*) {\\\\n\\\\t\\\\ttext-align: right;\\\\n\\\\t}\\\\n\\\\n\\\\t/* Colors */\\\\n\\\\n\\\\t.message-row {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t}\\\\n\\\\n\\\\t/* bubble mode styles */\\\\n\\\\t.bubble {\\\\n\\\\t\\\\tmargin: calc(var(--spacing-xl) * 2);\\\\n\\\\t\\\\tmargin-bottom: var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble.user-row {\\\\n\\\\t\\\\talign-self: flex-end;\\\\n\\\\t\\\\tmax-width: calc(100% - var(--spacing-xl) * 6);\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble.bot-row {\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\tmax-width: calc(100% - var(--spacing-xl) * 6);\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble .user-row {\\\\n\\\\t\\\\tflex-direction: row;\\\\n\\\\t\\\\tjustify-content: flex-end;\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble .with_avatar.user-row {\\\\n\\\\t\\\\tmargin-right: calc(var(--spacing-xl) * 2) !important;\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble .with_avatar.bot-row {\\\\n\\\\t\\\\tmargin-left: calc(var(--spacing-xl) * 2) !important;\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble .with_opposite_avatar.user-row {\\\\n\\\\t\\\\tmargin-left: calc(var(--spacing-xxl) + 35px + var(--spacing-xxl));\\\\n\\\\t}\\\\n\\\\n\\\\t/* panel mode styles */\\\\n\\\\t.panel {\\\\n\\\\t\\\\tmargin: 0;\\\\n\\\\t\\\\tpadding: calc(var(--spacing-lg) * 2) calc(var(--spacing-lg) * 2);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel.bot-row {\\\\n\\\\t\\\\tbackground: var(--background-fill-secondary);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .with_avatar {\\\\n\\\\t\\\\tpadding-left: calc(var(--spacing-xl) * 2) !important;\\\\n\\\\t\\\\tpadding-right: calc(var(--spacing-xl) * 2) !important;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .panel-full-width {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .user :global(*) {\\\\n\\\\t\\\\ttext-align: right;\\\\n\\\\t}\\\\n\\\\n\\\\t/* message content */\\\\n\\\\t.flex-wrap {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tmax-width: 100%;\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\tfont-size: var(--chatbot-text-size);\\\\n\\\\t\\\\toverflow-wrap: break-word;\\\\n\\\\t}\\\\n\\\\n\\\\t@media (max-width: 480px) {\\\\n\\\\t\\\\t.user-row.bubble {\\\\n\\\\t\\\\t\\\\talign-self: flex-end;\\\\n\\\\t\\\\t}\\\\n\\\\n\\\\t\\\\t.bot-row.bubble {\\\\n\\\\t\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t.message {\\\\n\\\\t\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.avatar-container {\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: flex-start;\\\\n\\\\t\\\\talign-items: flex-start;\\\\n\\\\t\\\\twidth: 35px;\\\\n\\\\t\\\\theight: 35px;\\\\n\\\\t\\\\tflex-shrink: 0;\\\\n\\\\t\\\\tbottom: 0;\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t}\\\\n\\\\t.user-row > .avatar-container {\\\\n\\\\t\\\\torder: 2;\\\\n\\\\t}\\\\n\\\\n\\\\t.user-row.bubble > .avatar-container {\\\\n\\\\t\\\\tmargin-left: var(--spacing-xxl);\\\\n\\\\t}\\\\n\\\\n\\\\t.bot-row.bubble > .avatar-container {\\\\n\\\\t\\\\tmargin-left: var(--spacing-xxl);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel.user-row > .avatar-container {\\\\n\\\\t\\\\torder: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.bot-row.bubble > .avatar-container {\\\\n\\\\t\\\\tmargin-right: var(--spacing-xxl);\\\\n\\\\t\\\\tmargin-left: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.avatar-container:not(.thumbnail-item) :global(img) {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tpadding: var(--size-1-5);\\\\n\\\\t}\\\\n\\\\n\\\\t.selectable {\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t}\\\\n\\\\n\\\\t@keyframes dot-flashing {\\\\n\\\\t\\\\t0% {\\\\n\\\\t\\\\t\\\\topacity: 0.8;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t50% {\\\\n\\\\t\\\\t\\\\topacity: 0.5;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t100% {\\\\n\\\\t\\\\t\\\\topacity: 0.8;\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t/* Image preview */\\\\n\\\\t.message :global(.preview) {\\\\n\\\\t\\\\tobject-fit: contain;\\\\n\\\\t\\\\twidth: 95%;\\\\n\\\\t\\\\tmax-height: 93%;\\\\n\\\\t}\\\\n\\\\t.image-preview {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tz-index: 999;\\\\n\\\\t\\\\tleft: 0;\\\\n\\\\t\\\\ttop: 0;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\toverflow: auto;\\\\n\\\\t\\\\tbackground-color: rgba(0, 0, 0, 0.9);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t}\\\\n\\\\t.image-preview :global(svg) {\\\\n\\\\t\\\\tstroke: white;\\\\n\\\\t}\\\\n\\\\t.image-preview-close-button {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\ttop: 10px;\\\\n\\\\t\\\\tright: 10px;\\\\n\\\\t\\\\tbackground: none;\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tfont-size: 1.5em;\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\theight: 30px;\\\\n\\\\t\\\\twidth: 30px;\\\\n\\\\t\\\\tpadding: 3px;\\\\n\\\\t\\\\tbackground: var(--bg-color);\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop);\\\\n\\\\t\\\\tborder: 1px solid var(--button-secondary-border-color);\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t}\\\\n\\\\n\\\\t.message > div {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\t.html {\\\\n\\\\t\\\\tpadding: 0;\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tbackground: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .bot,\\\\n\\\\t.panel .user {\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tbox-shadow: none;\\\\n\\\\t\\\\tbackground-color: var(--background-fill-secondary);\\\\n\\\\t}\\\\n\\\\n\\\\ttextarea {\\\\n\\\\t\\\\tbackground: none;\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tdisplay: block;\\\\n\\\\t\\\\tmax-width: 100%;\\\\n\\\\t}\\\\n\\\\t.user textarea {\\\\n\\\\t\\\\tborder-bottom-right-radius: 0;\\\\n\\\\t}\\\\n\\\\t.bot textarea {\\\\n\\\\t\\\\tborder-bottom-left-radius: 0;\\\\n\\\\t}\\\\n\\\\t.user textarea:focus {\\\\n\\\\t\\\\toutline: 2px solid var(--border-color-accent);\\\\n\\\\t}\\\\n\\\\t.bot textarea:focus {\\\\n\\\\t\\\\toutline: 2px solid var(--border-color-primary);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel.user-row {\\\\n\\\\t\\\\tbackground-color: var(--color-accent-soft);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .user-row,\\\\n\\\\t.panel .bot-row {\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .user :global(*),\\\\n\\\\t.panel .bot :global(*) {\\\\n\\\\t\\\\ttext-align: left;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .user {\\\\n\\\\t\\\\tbackground-color: var(--color-accent-soft);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .user-row {\\\\n\\\\t\\\\tbackground-color: var(--color-accent-soft);\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .message {\\\\n\\\\t\\\\tmargin-bottom: var(--spacing-md);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAyNC,sCAAS,CACR,QAAQ,CAAE,QAAQ,CAClB,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,IAAI,YAAY,CAC7B,CAEA,QAAQ,iEAAoC,CAC3C,UAAU,CAAE,CACb,CAGA,+CAAkB,CACjB,WAAW,CAAE,CAAC,CACd,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,QAAQ,CAAE,MACX,CAEA,gCAAiB,CAAS,GAAK,CAC9B,UAAU,CAAE,KACb,CAGA,wCAAW,CACV,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,KAAK,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CACtC,SAAS,CAAE,IAAI,CACf,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,SAAS,CAAE,IAAI,mBAAmB,CAAC,CACnC,aAAa,CAAE,UAAU,CACzB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CAEA,wCAAW,CACV,OAAO,CAAE,CAAC,CACV,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,KAAK,CAAE,WAAW,CAClB,QAAQ,CAAE,MACX,CAEA,UAAU,sCAAS,CAClB,MAAM,CAAE,IACT,CAEA,kCAAI,KAAK,QAAQ,CAAC,CAClB,mCAAK,KAAK,QAAQ,CAAE,CACnB,MAAM,CAAE,IAAI,CACZ,SAAS,CAAE,GACZ,CAEA,2BAAY,gBAAC,KAAK,iBAAiB,CAAC,CAAS,GAAK,CACjD,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,UAAU,CAAE,KACb,CAEA,uCAAU,CACT,OAAO,CAAE,KAAK,CACd,KAAK,CAAE,WAAW,CAClB,OAAO,CAAE,IAAI,YAAY,CAAC,CAAC,IAAI,YAAY,CAAC,CAC5C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,UAAU,CAAE,IAAI,2BAA2B,CAAC,CAC5C,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,eAAe,CAAE,IAAI,CACrB,MAAM,CAAE,CAAC,CACT,WAAW,CAAE,IAAI,WAAW,CAAC,CAC7B,SAAS,CAAE,IAAI,SAAS,CACzB,CAEA,mCAAM,CACL,KAAK,CAAE,IAAI,CAAC,UAAU,CACtB,SAAS,CAAE,WAAW,CAAC,UACxB,CAEA,MAAO,YAAY,KAAK,CAAC,CAAC,EAAE,CAAC,YAAY,KAAK,CAAE,CAC/C,wCAAW,CACV,KAAK,CAAE,IACR,CACD,CAEA,uBAAQ,CAAS,MAAQ,CACxB,SAAS,CAAE,IAAI,mBAAmB,CACnC,CAEA,oDAAuB,CACtB,YAAY,CAAE,GAAG,CACjB,aAAa,CAAE,IAAI,WAAW,CAC/B,CAEA,+CAAkB,CACjB,KAAK,CAAE,IACR,CACA,wDAA2B,CAC1B,WAAW,CAAE,QACd,CAEA,mCAAM,CACL,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,UAAU,CAAE,QAAQ,CACpB,0BAA0B,CAAE,CAAC,CAC7B,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,6BAA6B,CAAC,CACpD,gBAAgB,CAAE,IAAI,mBAAmB,CAAC,CAC1C,OAAO,CAAE,IAAI,YAAY,CAAC,CAAC,IAAI,YAAY,CAC5C,CAEA,kCAAK,CACJ,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,YAAY,CAAE,IAAI,sBAAsB,CAAC,CACzC,gBAAgB,CAAE,IAAI,2BAA2B,CAAC,CAClD,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,UAAU,CAAE,UAAU,CACtB,UAAU,CAAE,KAAK,CACjB,yBAAyB,CAAE,CAAC,CAC5B,OAAO,CAAE,IAAI,YAAY,CAAC,CAAC,IAAI,YAAY,CAC5C,CAEA,kCAAI,KAAK,WAAW,CAAE,CACrB,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,IACb,CAEA,qBAAM,CAAC,oBAAK,CAAS,CAAG,CACvB,UAAU,CAAE,KACb,CAIA,0CAAa,CACZ,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QACX,CAGA,qCAAQ,CACP,MAAM,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACnC,aAAa,CAAE,IAAI,YAAY,CAChC,CAEA,OAAO,uCAAU,CAChB,UAAU,CAAE,QAAQ,CACpB,SAAS,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAC7C,CAEA,OAAO,sCAAS,CACf,UAAU,CAAE,UAAU,CACtB,SAAS,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAC7C,CAEA,sBAAO,CAAC,wBAAU,CACjB,cAAc,CAAE,GAAG,CACnB,eAAe,CAAE,QAClB,CAEA,sBAAO,CAAC,YAAY,wBAAU,CAC7B,YAAY,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAC3C,CAEA,sBAAO,CAAC,YAAY,uBAAS,CAC5B,WAAW,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAC1C,CAEA,sBAAO,CAAC,qBAAqB,wBAAU,CACtC,WAAW,CAAE,KAAK,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CACjE,CAGA,oCAAO,CACN,MAAM,CAAE,CAAC,CACT,OAAO,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAChE,CAEA,MAAM,sCAAS,CACd,UAAU,CAAE,IAAI,2BAA2B,CAC5C,CAEA,qBAAM,CAAC,2BAAa,CACnB,YAAY,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CACpD,aAAa,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAC5C,CAEA,qBAAM,CAAC,gCAAkB,CACxB,KAAK,CAAE,IACR,CAEA,qBAAM,CAAC,oBAAK,CAAS,CAAG,CACvB,UAAU,CAAE,KACb,CAGA,wCAAW,CACV,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,SAAS,CAAE,IAAI,CACf,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,SAAS,CAAE,IAAI,mBAAmB,CAAC,CACnC,aAAa,CAAE,UAChB,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,SAAS,qCAAQ,CAChB,UAAU,CAAE,QACb,CAEA,QAAQ,qCAAQ,CACf,UAAU,CAAE,UACb,CACA,sCAAS,CACR,KAAK,CAAE,IACR,CACD,CAEA,+CAAkB,CACjB,UAAU,CAAE,UAAU,CACtB,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,UAAU,CAC3B,WAAW,CAAE,UAAU,CACvB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,WAAW,CAAE,CAAC,CACd,MAAM,CAAE,CAAC,CACT,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAC7C,CACA,wBAAS,CAAG,gCAAkB,CAC7B,KAAK,CAAE,CACR,CAEA,SAAS,sBAAO,CAAG,gCAAkB,CACpC,WAAW,CAAE,IAAI,aAAa,CAC/B,CAEA,QAAQ,sBAAO,CAAG,gCAAkB,CACnC,WAAW,CAAE,IAAI,aAAa,CAC/B,CAEA,MAAM,wBAAS,CAAG,gCAAkB,CACnC,KAAK,CAAE,CACR,CAEA,QAAQ,sBAAO,CAAG,gCAAkB,CACnC,YAAY,CAAE,IAAI,aAAa,CAAC,CAChC,WAAW,CAAE,CACd,CAEA,gCAAiB,KAAK,eAAe,CAAC,CAAS,GAAK,CACnD,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,KAAK,CACjB,aAAa,CAAE,GAAG,CAClB,OAAO,CAAE,IAAI,UAAU,CACxB,CAEA,yCAAY,CACX,MAAM,CAAE,OACT,CAEA,WAAW,2BAAa,CACvB,EAAG,CACF,OAAO,CAAE,GACV,CACA,GAAI,CACH,OAAO,CAAE,GACV,CACA,IAAK,CACJ,OAAO,CAAE,GACV,CACD,CAGA,uBAAQ,CAAS,QAAU,CAC1B,UAAU,CAAE,OAAO,CACnB,KAAK,CAAE,GAAG,CACV,UAAU,CAAE,GACb,CACA,4CAAe,CACd,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,GAAG,CACZ,IAAI,CAAE,CAAC,CACP,GAAG,CAAE,CAAC,CACN,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,QAAQ,CAAE,IAAI,CACd,gBAAgB,CAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CACpC,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MACd,CACA,6BAAc,CAAS,GAAK,CAC3B,MAAM,CAAE,KACT,CACA,yDAA4B,CAC3B,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,IAAI,CACT,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,IAAI,CAChB,MAAM,CAAE,IAAI,CACZ,SAAS,CAAE,KAAK,CAChB,MAAM,CAAE,OAAO,CACf,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IAAI,CACX,OAAO,CAAE,GAAG,CACZ,UAAU,CAAE,IAAI,UAAU,CAAC,CAC3B,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,+BAA+B,CAAC,CACtD,aAAa,CAAE,IAAI,WAAW,CAC/B,CAEA,uBAAQ,CAAG,kBAAI,CACd,KAAK,CAAE,IACR,CACA,mCAAM,CACL,OAAO,CAAE,CAAC,CACV,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,IACb,CAEA,qBAAM,CAAC,mBAAI,CACX,qBAAM,CAAC,oBAAM,CACZ,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,IAAI,CAChB,gBAAgB,CAAE,IAAI,2BAA2B,CAClD,CAEA,sCAAS,CACR,UAAU,CAAE,IAAI,CAChB,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,KAAK,CACd,SAAS,CAAE,IACZ,CACA,oBAAK,CAAC,uBAAS,CACd,0BAA0B,CAAE,CAC7B,CACA,mBAAI,CAAC,uBAAS,CACb,yBAAyB,CAAE,CAC5B,CACA,oBAAK,CAAC,uBAAQ,MAAO,CACpB,OAAO,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,qBAAqB,CAC7C,CACA,mBAAI,CAAC,uBAAQ,MAAO,CACnB,OAAO,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAC9C,CAEA,MAAM,uCAAU,CACf,gBAAgB,CAAE,IAAI,mBAAmB,CAC1C,CAEA,qBAAM,CAAC,wBAAS,CAChB,qBAAM,CAAC,uBAAS,CACf,UAAU,CAAE,UACb,CAEA,qBAAM,CAAC,oBAAK,CAAS,CAAE,CACvB,qBAAM,CAAC,mBAAI,CAAS,CAAG,CACtB,UAAU,CAAE,IACb,CAEA,qBAAM,CAAC,oBAAM,CACZ,gBAAgB,CAAE,IAAI,mBAAmB,CAC1C,CAEA,qBAAM,CAAC,wBAAU,CAChB,gBAAgB,CAAE,IAAI,mBAAmB,CAAC,CAC1C,UAAU,CAAE,UACb,CAEA,qBAAM,CAAC,uBAAS,CACf,aAAa,CAAE,IAAI,YAAY,CAChC\"}'};let xe=!1;function be(A){return A.type===\"text\"?A.content:A.type===\"component\"&&A.content.component===\"file\"?Array.isArray(A.content.value)?`file of extension type: ${A.content.value[0].orig_name?.split(\".\").pop()}`:`file of extension type: ${A.content.value?.orig_name?.split(\".\").pop()}`+(A.content.value?.orig_name??\"\"):`a component of type ${A.content.component??\"unknown\"}`}const we=M((A,t,e,s)=>{let{value:a}=t,{avatar_img:o}=t,{opposite_avatar_img:l=null}=t,{role:n=\"user\"}=t,{messages:r=[]}=t,{layout:i}=t,{render_markdown:d}=t,{latex_delimiters:m}=t,{sanitize_html:u}=t,{selectable:f}=t,{_fetch:g}=t,{rtl:_}=t,{dispatch:x}=t,{i18n:h}=t,{line_breaks:p}=t,{upload:c}=t,{target:E}=t,{theme_mode:v}=t,{_components:B}=t,{i:O}=t,{show_copy_button:z}=t,{generating:Y}=t,{feedback_options:T}=t,{show_like:G}=t,{show_edit:R}=t,{show_retry:N}=t,{show_undo:J}=t,{msg_format:$}=t,{handle_action:U}=t,{scroll:Z}=t,{allow_file_downloads:W}=t,{in_edit_mode:Q}=t,{edit_messages:S}=t,{display_consecutive_in_same_bubble:K}=t,{current_feedback:L=null}=t,{allow_tags:V=!1}=t,{watermark:H=null}=t,j=[],At=Array(r.length).fill(160),tt=Array(r.length).fill(0),F;if(t.value===void 0&&e.value&&a!==void 0&&e.value(a),t.avatar_img===void 0&&e.avatar_img&&o!==void 0&&e.avatar_img(o),t.opposite_avatar_img===void 0&&e.opposite_avatar_img&&l!==void 0&&e.opposite_avatar_img(l),t.role===void 0&&e.role&&n!==void 0&&e.role(n),t.messages===void 0&&e.messages&&r!==void 0&&e.messages(r),t.layout===void 0&&e.layout&&i!==void 0&&e.layout(i),t.render_markdown===void 0&&e.render_markdown&&d!==void 0&&e.render_markdown(d),t.latex_delimiters===void 0&&e.latex_delimiters&&m!==void 0&&e.latex_delimiters(m),t.sanitize_html===void 0&&e.sanitize_html&&u!==void 0&&e.sanitize_html(u),t.selectable===void 0&&e.selectable&&f!==void 0&&e.selectable(f),t._fetch===void 0&&e._fetch&&g!==void 0&&e._fetch(g),t.rtl===void 0&&e.rtl&&_!==void 0&&e.rtl(_),t.dispatch===void 0&&e.dispatch&&x!==void 0&&e.dispatch(x),t.i18n===void 0&&e.i18n&&h!==void 0&&e.i18n(h),t.line_breaks===void 0&&e.line_breaks&&p!==void 0&&e.line_breaks(p),t.upload===void 0&&e.upload&&c!==void 0&&e.upload(c),t.target===void 0&&e.target&&E!==void 0&&e.target(E),t.theme_mode===void 0&&e.theme_mode&&v!==void 0&&e.theme_mode(v),t._components===void 0&&e._components&&B!==void 0&&e._components(B),t.i===void 0&&e.i&&O!==void 0&&e.i(O),t.show_copy_button===void 0&&e.show_copy_button&&z!==void 0&&e.show_copy_button(z),t.generating===void 0&&e.generating&&Y!==void 0&&e.generating(Y),t.feedback_options===void 0&&e.feedback_options&&T!==void 0&&e.feedback_options(T),t.show_like===void 0&&e.show_like&&G!==void 0&&e.show_like(G),t.show_edit===void 0&&e.show_edit&&R!==void 0&&e.show_edit(R),t.show_retry===void 0&&e.show_retry&&N!==void 0&&e.show_retry(N),t.show_undo===void 0&&e.show_undo&&J!==void 0&&e.show_undo(J),t.msg_format===void 0&&e.msg_format&&$!==void 0&&e.msg_format($),t.handle_action===void 0&&e.handle_action&&U!==void 0&&e.handle_action(U),t.scroll===void 0&&e.scroll&&Z!==void 0&&e.scroll(Z),t.allow_file_downloads===void 0&&e.allow_file_downloads&&W!==void 0&&e.allow_file_downloads(W),t.in_edit_mode===void 0&&e.in_edit_mode&&Q!==void 0&&e.in_edit_mode(Q),t.edit_messages===void 0&&e.edit_messages&&S!==void 0&&e.edit_messages(S),t.display_consecutive_in_same_bubble===void 0&&e.display_consecutive_in_same_bubble&&K!==void 0&&e.display_consecutive_in_same_bubble(K),t.current_feedback===void 0&&e.current_feedback&&L!==void 0&&e.current_feedback(L),t.allow_tags===void 0&&e.allow_tags&&V!==void 0&&e.allow_tags(V),t.watermark===void 0&&e.watermark&&H!==void 0&&e.watermark(H),A.css.add(he),Q&&!xe){const y=j.length-r.length;for(let b=y;b<j.length;b++)b>=0&&(At[b-y]=j[b]?.clientWidth,tt[b-y]=j[b]?.clientHeight)}return F={handle_action:U,likeable:G,feedback_options:T,show_retry:N,show_undo:J,show_edit:R,in_edit_mode:Q,generating:Y,show_copy_button:z,message:$===\"tuples\"?r[0]:r,position:n===\"user\"?\"right\":\"left\",avatar:o,layout:i,dispatch:x,current_feedback:L,watermark:H},`<div class=\"${[\"message-row \"+I(i,!0)+\" \"+I(n,!0)+\"-row svelte-1csv61q\",(o!==null?\"with_avatar\":\"\")+\" \"+(l!==null?\"with_opposite_avatar\":\"\")].join(\" \").trim()}\">${o!==null?`<div class=\"avatar-container svelte-1csv61q\">${C(rt,\"Image\").$$render(A,{class:\"avatar-image\",src:o?.url,alt:n+\" avatar\"},{},{})}</div>`:\"\"} <div class=\"${[\"flex-wrap svelte-1csv61q\",(n?\"role\":\"\")+\" \"+(r[0].type===\"component\"?\"component-wrap\":\"\")].join(\" \").trim()}\"><div class=\"${[I(Et(K?n:\"\"),!0)+\" svelte-1csv61q\",K?\"message\":\"\"].join(\" \").trim()}\">${ot(r,(y,b)=>`<div class=\"${[\"message \"+I(K?\"\":n,!0)+\" svelte-1csv61q\",\"panel-full-width \"+(d?\"\":\"message-markdown-disabled\")+\" \"+(y.type===\"component\"?\"component\":\"\")+\" \"+(ee(y)&&y.content.component===\"html\"?\"html\":\"\")+\" \"+(b>0?\"thought\":\"\")].join(\" \").trim()}\">${Q&&y.type===\"text\"?` <textarea class=\"edit-textarea svelte-1csv61q\" autofocus${Ct({width:`max(${At[b]}px, 160px)`,\"min-height\":`${tt[b]}px`})}>${I(S[b]||\"\")}</textarea>`:` <div${k(\"data-testid\",n,0)}${k(\"dir\",_?\"rtl\":\"ltr\",0)}${k(\"aria-label\",n+\"'s message: \"+be(y),0)} class=\"${[\"svelte-1csv61q\",(O===a.length-1?\"latest\":\"\")+\" \"+(d?\"\":\"message-markdown-disabled\")+\" \"+(f?\"selectable\":\"\")].join(\" \").trim()}\"${Ct({\"user-select\":\"text\",cursor:f?\"pointer\":\"auto\",\"text-align\":_?\"right\":\"left\"})}${k(\"this\",j[b],0)}>${y?.metadata?.title?`${C(qt,\"Thought\").$$render(A,{thought:y,rtl:_,sanitize_html:u,allow_tags:V,latex_delimiters:m,render_markdown:d,_components:B,upload:c,thought_index:b,target:E,theme_mode:v,_fetch:g,scroll:Z,allow_file_downloads:W,display_consecutive_in_same_bubble:K,i18n:h,line_breaks:p},{},{})}`:`${C(St,\"MessageContent\").$$render(A,{message:y,sanitize_html:u,allow_tags:V,latex_delimiters:m,render_markdown:d,_components:B,upload:c,thought_index:b,target:E,theme_mode:v,_fetch:g,scroll:Z,allow_file_downloads:W,display_consecutive_in_same_bubble:K,i18n:h,line_breaks:p},{},{})}`} </div>`}</div> ${i===\"panel\"?`${C(Bt,\"ButtonPanel\").$$render(A,Object.assign({},F,{current_feedback:L},{watermark:H},{i18n:h}),{},{})}`:\"\"}`)}</div></div></div> ${i===\"bubble\"?`${C(Bt,\"ButtonPanel\").$$render(A,Object.assign({},F,{i18n:h}),{},{})}`:\"\"}`}),Be={code:\".container.svelte-134ihlx{display:flex;margin:calc(var(--spacing-xl) * 2)}.bubble.pending.svelte-134ihlx{border-width:1px;border-radius:var(--radius-lg);border-bottom-left-radius:0;border-color:var(--border-color-primary);background-color:var(--background-fill-secondary);box-shadow:var(--shadow-drop);align-self:flex-start;width:fit-content;margin-bottom:var(--spacing-xl)}.bubble.with_opposite_avatar.svelte-134ihlx{margin-right:calc(var(--spacing-xxl) + 35px + var(--spacing-xxl))}.panel.pending.svelte-134ihlx{margin:0;padding:calc(var(--spacing-lg) * 2) calc(var(--spacing-lg) * 2);width:100%;border:none;background:none;box-shadow:none;border-radius:0}.panel.with_avatar.svelte-134ihlx{padding-left:calc(var(--spacing-xl) * 2) !important;padding-right:calc(var(--spacing-xl) * 2) !important}.avatar-container.svelte-134ihlx{align-self:flex-start;position:relative;display:flex;justify-content:flex-start;align-items:flex-start;width:35px;height:35px;flex-shrink:0;bottom:0;border-radius:50%;border:1px solid var(--border-color-primary);margin-right:var(--spacing-xxl)}.avatar-container.svelte-134ihlx:not(.thumbnail-item) img{width:100%;height:100%;object-fit:cover;border-radius:50%;padding:var(--size-1-5)}.message-content.svelte-134ihlx{padding:var(--spacing-sm) var(--spacing-xl);min-height:var(--size-8);display:flex;align-items:center}.dots.svelte-134ihlx{display:flex;gap:var(--spacing-xs);align-items:center}.dot.svelte-134ihlx{width:var(--size-1-5);height:var(--size-1-5);margin-right:var(--spacing-xs);border-radius:50%;background-color:var(--body-text-color);opacity:0.5;animation:svelte-134ihlx-pulse 1.5s infinite}.dot.svelte-134ihlx:nth-child(2){animation-delay:0.2s}.dot.svelte-134ihlx:nth-child(3){animation-delay:0.4s}@keyframes svelte-134ihlx-pulse{0%,100%{opacity:0.4;transform:scale(1)}50%{opacity:1;transform:scale(1.1)}}\",map:'{\"version\":3,\"file\":\"Pending.svelte\",\"sources\":[\"Pending.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { Image } from \\\\\"@gradio/image/shared\\\\\";\\\\nexport let layout = \\\\\"bubble\\\\\";\\\\nexport let avatar_images = [null, null];\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"container\\\\\">\\\\n\\\\t{#if avatar_images[1] !== null}\\\\n\\\\t\\\\t<div class=\\\\\"avatar-container\\\\\">\\\\n\\\\t\\\\t\\\\t<Image class=\\\\\"avatar-image\\\\\" src={avatar_images[1].url} alt=\\\\\"bot avatar\\\\\" />\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n\\\\n\\\\t<div\\\\n\\\\t\\\\tclass=\\\\\"message bot pending {layout}\\\\\"\\\\n\\\\t\\\\tclass:with_avatar={avatar_images[1] !== null}\\\\n\\\\t\\\\tclass:with_opposite_avatar={avatar_images[0] !== null}\\\\n\\\\t\\\\trole=\\\\\"status\\\\\"\\\\n\\\\t\\\\taria-label=\\\\\"Loading response\\\\\"\\\\n\\\\t\\\\taria-live=\\\\\"polite\\\\\"\\\\n\\\\t>\\\\n\\\\t\\\\t<div class=\\\\\"message-content\\\\\">\\\\n\\\\t\\\\t\\\\t<span class=\\\\\"sr-only\\\\\">Loading content</span>\\\\n\\\\t\\\\t\\\\t<div class=\\\\\"dots\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"dot\\\\\" />\\\\n\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"dot\\\\\" />\\\\n\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"dot\\\\\" />\\\\n\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t</div>\\\\n\\\\t</div>\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.container {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tmargin: calc(var(--spacing-xl) * 2);\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble.pending {\\\\n\\\\t\\\\tborder-width: 1px;\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t\\\\tborder-bottom-left-radius: 0;\\\\n\\\\t\\\\tborder-color: var(--border-color-primary);\\\\n\\\\t\\\\tbackground-color: var(--background-fill-secondary);\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop);\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\twidth: fit-content;\\\\n\\\\t\\\\tmargin-bottom: var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble.with_opposite_avatar {\\\\n\\\\t\\\\tmargin-right: calc(var(--spacing-xxl) + 35px + var(--spacing-xxl));\\\\n\\\\t}\\\\n\\\\n\\\\t.panel.pending {\\\\n\\\\t\\\\tmargin: 0;\\\\n\\\\t\\\\tpadding: calc(var(--spacing-lg) * 2) calc(var(--spacing-lg) * 2);\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tbackground: none;\\\\n\\\\t\\\\tbox-shadow: none;\\\\n\\\\t\\\\tborder-radius: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel.with_avatar {\\\\n\\\\t\\\\tpadding-left: calc(var(--spacing-xl) * 2) !important;\\\\n\\\\t\\\\tpadding-right: calc(var(--spacing-xl) * 2) !important;\\\\n\\\\t}\\\\n\\\\n\\\\t.avatar-container {\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: flex-start;\\\\n\\\\t\\\\talign-items: flex-start;\\\\n\\\\t\\\\twidth: 35px;\\\\n\\\\t\\\\theight: 35px;\\\\n\\\\t\\\\tflex-shrink: 0;\\\\n\\\\t\\\\tbottom: 0;\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tmargin-right: var(--spacing-xxl);\\\\n\\\\t}\\\\n\\\\n\\\\t.avatar-container:not(.thumbnail-item) :global(img) {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tpadding: var(--size-1-5);\\\\n\\\\t}\\\\n\\\\n\\\\t.message-content {\\\\n\\\\t\\\\tpadding: var(--spacing-sm) var(--spacing-xl);\\\\n\\\\t\\\\tmin-height: var(--size-8);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t}\\\\n\\\\n\\\\t.dots {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tgap: var(--spacing-xs);\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t}\\\\n\\\\n\\\\t.dot {\\\\n\\\\t\\\\twidth: var(--size-1-5);\\\\n\\\\t\\\\theight: var(--size-1-5);\\\\n\\\\t\\\\tmargin-right: var(--spacing-xs);\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tbackground-color: var(--body-text-color);\\\\n\\\\t\\\\topacity: 0.5;\\\\n\\\\t\\\\tanimation: pulse 1.5s infinite;\\\\n\\\\t}\\\\n\\\\n\\\\t.dot:nth-child(2) {\\\\n\\\\t\\\\tanimation-delay: 0.2s;\\\\n\\\\t}\\\\n\\\\n\\\\t.dot:nth-child(3) {\\\\n\\\\t\\\\tanimation-delay: 0.4s;\\\\n\\\\t}\\\\n\\\\n\\\\t@keyframes pulse {\\\\n\\\\t\\\\t0%,\\\\n\\\\t\\\\t100% {\\\\n\\\\t\\\\t\\\\topacity: 0.4;\\\\n\\\\t\\\\t\\\\ttransform: scale(1);\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t50% {\\\\n\\\\t\\\\t\\\\topacity: 1;\\\\n\\\\t\\\\t\\\\ttransform: scale(1.1);\\\\n\\\\t\\\\t}\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAgCC,yBAAW,CACV,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CACnC,CAEA,OAAO,uBAAS,CACf,YAAY,CAAE,GAAG,CACjB,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,yBAAyB,CAAE,CAAC,CAC5B,YAAY,CAAE,IAAI,sBAAsB,CAAC,CACzC,gBAAgB,CAAE,IAAI,2BAA2B,CAAC,CAClD,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,UAAU,CAAE,UAAU,CACtB,KAAK,CAAE,WAAW,CAClB,aAAa,CAAE,IAAI,YAAY,CAChC,CAEA,OAAO,oCAAsB,CAC5B,YAAY,CAAE,KAAK,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAClE,CAEA,MAAM,uBAAS,CACd,MAAM,CAAE,CAAC,CACT,OAAO,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAChE,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,IAAI,CAChB,aAAa,CAAE,CAChB,CAEA,MAAM,2BAAa,CAClB,YAAY,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CACpD,aAAa,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAC5C,CAEA,gCAAkB,CACjB,UAAU,CAAE,UAAU,CACtB,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,UAAU,CAC3B,WAAW,CAAE,UAAU,CACvB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,WAAW,CAAE,CAAC,CACd,MAAM,CAAE,CAAC,CACT,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,YAAY,CAAE,IAAI,aAAa,CAChC,CAEA,gCAAiB,KAAK,eAAe,CAAC,CAAS,GAAK,CACnD,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,KAAK,CACjB,aAAa,CAAE,GAAG,CAClB,OAAO,CAAE,IAAI,UAAU,CACxB,CAEA,+BAAiB,CAChB,OAAO,CAAE,IAAI,YAAY,CAAC,CAAC,IAAI,YAAY,CAAC,CAC5C,UAAU,CAAE,IAAI,QAAQ,CAAC,CACzB,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MACd,CAEA,oBAAM,CACL,OAAO,CAAE,IAAI,CACb,GAAG,CAAE,IAAI,YAAY,CAAC,CACtB,WAAW,CAAE,MACd,CAEA,mBAAK,CACJ,KAAK,CAAE,IAAI,UAAU,CAAC,CACtB,MAAM,CAAE,IAAI,UAAU,CAAC,CACvB,YAAY,CAAE,IAAI,YAAY,CAAC,CAC/B,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,IAAI,iBAAiB,CAAC,CACxC,OAAO,CAAE,GAAG,CACZ,SAAS,CAAE,oBAAK,CAAC,IAAI,CAAC,QACvB,CAEA,mBAAI,WAAW,CAAC,CAAE,CACjB,eAAe,CAAE,IAClB,CAEA,mBAAI,WAAW,CAAC,CAAE,CACjB,eAAe,CAAE,IAClB,CAEA,WAAW,oBAAM,CAChB,EAAE,CACF,IAAK,CACJ,OAAO,CAAE,GAAG,CACZ,SAAS,CAAE,MAAM,CAAC,CACnB,CACA,GAAI,CACH,OAAO,CAAE,CAAC,CACV,SAAS,CAAE,MAAM,GAAG,CACrB,CACD\"}'},It=M((A,t,e,s)=>{let{layout:a=\"bubble\"}=t,{avatar_images:o=[null,null]}=t;return t.layout===void 0&&e.layout&&a!==void 0&&e.layout(a),t.avatar_images===void 0&&e.avatar_images&&o!==void 0&&e.avatar_images(o),A.css.add(Be),`<div class=\"container svelte-134ihlx\">${o[1]!==null?`<div class=\"avatar-container svelte-134ihlx\">${C(rt,\"Image\").$$render(A,{class:\"avatar-image\",src:o[1].url,alt:\"bot avatar\"},{},{})}</div>`:\"\"} <div class=\"${[\"message bot pending \"+I(a,!0)+\" svelte-134ihlx\",(o[1]!==null?\"with_avatar\":\"\")+\" \"+(o[0]!==null?\"with_opposite_avatar\":\"\")].join(\" \").trim()}\" role=\"status\" aria-label=\"Loading response\" aria-live=\"polite\"><div class=\"message-content svelte-134ihlx\" data-svelte-h=\"svelte-1vfby8\"><span class=\"sr-only\">Loading content</span> <div class=\"dots svelte-134ihlx\"><div class=\"dot svelte-134ihlx\"></div> <div class=\"dot svelte-134ihlx\"></div> <div class=\"dot svelte-134ihlx\"></div></div></div></div> </div>`}),Ie={code:\".placeholder-content.svelte-9pi8y1{display:flex;flex-direction:column;height:100%}.placeholder.svelte-9pi8y1{align-items:center;display:flex;justify-content:center;height:100%;flex-grow:1}.examples.svelte-9pi8y1 img{pointer-events:none}.examples.svelte-9pi8y1{margin:auto;padding:var(--spacing-xxl);display:grid;grid-template-columns:repeat(auto-fit, minmax(240px, 1fr));gap:var(--spacing-xl);max-width:calc(min(4 * 240px + 5 * var(--spacing-xxl), 100%))}.example.svelte-9pi8y1{display:flex;flex-direction:column;align-items:flex-start;padding:var(--spacing-xxl);border:none;border-radius:var(--radius-lg);background-color:var(--block-background-fill);cursor:pointer;transition:all 150ms ease-in-out;width:100%;gap:var(--spacing-sm);border:var(--block-border-width) solid var(--block-border-color);transform:translateY(0px)}.example.svelte-9pi8y1:hover{transform:translateY(-2px);background-color:var(--color-accent-soft)}.example-content.svelte-9pi8y1{display:flex;flex-direction:column;align-items:flex-start;width:100%;height:100%}.example-text-content.svelte-9pi8y1{margin-top:auto;text-align:left}.example-text.svelte-9pi8y1{font-size:var(--text-md);text-align:left;overflow:hidden;text-overflow:ellipsis}.example-icons-grid.svelte-9pi8y1{display:flex;gap:var(--spacing-sm);margin-bottom:var(--spacing-lg);width:100%}.example-icon.svelte-9pi8y1{flex-shrink:0;width:var(--size-8);height:var(--size-8);display:flex;align-items:center;justify-content:center;border-radius:var(--radius-lg);border:var(--block-border-width) solid var(--block-border-color);background-color:var(--block-background-fill);position:relative}.example-icon.svelte-9pi8y1 svg{width:var(--size-4);height:var(--size-4);color:var(--color-text-secondary)}.text-icon-aa.svelte-9pi8y1{font-size:var(--text-sm);font-weight:var(--weight-semibold);color:var(--color-text-secondary);line-height:1}.example-image-container.svelte-9pi8y1{width:var(--size-8);height:var(--size-8);border-radius:var(--radius-lg);overflow:hidden;position:relative;margin-bottom:var(--spacing-lg)}.example-image-container.svelte-9pi8y1 img{width:100%;height:100%;object-fit:cover}.image-overlay.svelte-9pi8y1{position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(0, 0, 0, 0.6);color:white;display:flex;align-items:center;justify-content:center;font-size:var(--text-lg);font-weight:var(--weight-semibold);border-radius:var(--radius-lg)}.file-overlay.svelte-9pi8y1{position:absolute;inset:0;background:rgba(0, 0, 0, 0.6);color:white;display:flex;align-items:center;justify-content:center;font-size:var(--text-sm);font-weight:var(--weight-semibold);border-radius:var(--radius-lg)}\",map:'{\"version\":3,\"file\":\"Examples.svelte\",\"sources\":[\"Examples.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { Image } from \\\\\"@gradio/image/shared\\\\\";\\\\nimport { MarkdownCode as Markdown } from \\\\\"@gradio/markdown-code\\\\\";\\\\nimport { File, Music, Video } from \\\\\"@gradio/icons\\\\\";\\\\nimport { createEventDispatcher } from \\\\\"svelte\\\\\";\\\\nexport let examples = null;\\\\nexport let placeholder = null;\\\\nexport let latex_delimiters;\\\\nconst dispatch = createEventDispatcher();\\\\nfunction handle_example_select(i, example) {\\\\n    const example_obj = typeof example === \\\\\"string\\\\\" ? { text: example } : example;\\\\n    dispatch(\\\\\"example_select\\\\\", {\\\\n        index: i,\\\\n        value: { text: example_obj.text, files: example_obj.files }\\\\n    });\\\\n}\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"placeholder-content\\\\\" role=\\\\\"complementary\\\\\">\\\\n\\\\t{#if placeholder !== null}\\\\n\\\\t\\\\t<div class=\\\\\"placeholder\\\\\">\\\\n\\\\t\\\\t\\\\t<Markdown message={placeholder} {latex_delimiters} />\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n\\\\t{#if examples !== null}\\\\n\\\\t\\\\t<div class=\\\\\"examples\\\\\" role=\\\\\"list\\\\\">\\\\n\\\\t\\\\t\\\\t{#each examples as example, i}\\\\n\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() =>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\thandle_example_select(\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ti,\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttypeof example === \\\\\"string\\\\\" ? { text: example } : example\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\taria-label={`Select example ${i + 1}: ${example.display_text || example.text}`}\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"example-content\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if example?.icon?.url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"example-image-container\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Image\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example-image\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={example.icon.url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\talt=\\\\\"Example icon\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else if example?.icon?.mime_type === \\\\\"text\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"example-icon\\\\\" aria-hidden=\\\\\"true\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"text-icon-aa\\\\\">Aa</span>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else if example.files !== undefined && example.files.length > 0}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if example.files.length > 1}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example-icons-grid\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\trole=\\\\\"group\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-label=\\\\\"Example attachments\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#each example.files.slice(0, 4) as file, i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if file.mime_type?.includes(\\\\\"image\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"example-image-container\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Image\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example-image\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={file.url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\talt={file.orig_name || `Example image ${i + 1}`}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if i === 3 && example.files.length > 4}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"image-overlay\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\trole=\\\\\"status\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-label={`${example.files.length - 4} more files`}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t+{example.files.length - 4}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else if file.mime_type?.includes(\\\\\"video\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"example-image-container\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<video\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example-image\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={file.url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-hidden=\\\\\"true\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if i === 3 && example.files.length > 4}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"image-overlay\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\trole=\\\\\"status\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-label={`${example.files.length - 4} more files`}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t+{example.files.length - 4}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example-icon\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-label={`File: ${file.orig_name}`}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if file.mime_type?.includes(\\\\\"audio\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Music />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<File />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if example.files.length > 4}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"example-icon\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"file-overlay\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\trole=\\\\\"status\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-label={`${example.files.length - 4} more files`}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t+{example.files.length - 4}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else if example.files[0].mime_type?.includes(\\\\\"image\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"example-image-container\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Image\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example-image\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={example.files[0].url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\talt={example.files[0].orig_name || \\\\\"Example image\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else if example.files[0].mime_type?.includes(\\\\\"video\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"example-image-container\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<video\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example-image\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={example.files[0].url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-hidden=\\\\\"true\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else if example.files[0].mime_type?.includes(\\\\\"audio\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example-icon\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-label={`File: ${example.files[0].orig_name}`}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Music />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example-icon\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-label={`File: ${example.files[0].orig_name}`}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<File />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"example-text-content\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"example-text\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>{example.display_text || example.text}</span\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.placeholder-content {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.placeholder {\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tflex-grow: 1;\\\\n\\\\t}\\\\n\\\\n\\\\t.examples :global(img) {\\\\n\\\\t\\\\tpointer-events: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.examples {\\\\n\\\\t\\\\tmargin: auto;\\\\n\\\\t\\\\tpadding: var(--spacing-xxl);\\\\n\\\\t\\\\tdisplay: grid;\\\\n\\\\t\\\\tgrid-template-columns: repeat(auto-fit, minmax(240px, 1fr));\\\\n\\\\t\\\\tgap: var(--spacing-xl);\\\\n\\\\t\\\\tmax-width: calc(min(4 * 240px + 5 * var(--spacing-xxl), 100%));\\\\n\\\\t}\\\\n\\\\n\\\\t.example {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\talign-items: flex-start;\\\\n\\\\t\\\\tpadding: var(--spacing-xxl);\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\ttransition: all 150ms ease-in-out;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tgap: var(--spacing-sm);\\\\n\\\\t\\\\tborder: var(--block-border-width) solid var(--block-border-color);\\\\n\\\\t\\\\ttransform: translateY(0px);\\\\n\\\\t}\\\\n\\\\n\\\\t.example:hover {\\\\n\\\\t\\\\ttransform: translateY(-2px);\\\\n\\\\t\\\\tbackground-color: var(--color-accent-soft);\\\\n\\\\t}\\\\n\\\\n\\\\t.example-content {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\talign-items: flex-start;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.example-text-content {\\\\n\\\\t\\\\tmargin-top: auto;\\\\n\\\\t\\\\ttext-align: left;\\\\n\\\\t}\\\\n\\\\n\\\\t.example-text {\\\\n\\\\t\\\\tfont-size: var(--text-md);\\\\n\\\\t\\\\ttext-align: left;\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\ttext-overflow: ellipsis;\\\\n\\\\t}\\\\n\\\\n\\\\t.example-icons-grid {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tgap: var(--spacing-sm);\\\\n\\\\t\\\\tmargin-bottom: var(--spacing-lg);\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.example-icon {\\\\n\\\\t\\\\tflex-shrink: 0;\\\\n\\\\t\\\\twidth: var(--size-8);\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t\\\\tborder: var(--block-border-width) solid var(--block-border-color);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t}\\\\n\\\\n\\\\t.example-icon :global(svg) {\\\\n\\\\t\\\\twidth: var(--size-4);\\\\n\\\\t\\\\theight: var(--size-4);\\\\n\\\\t\\\\tcolor: var(--color-text-secondary);\\\\n\\\\t}\\\\n\\\\n\\\\t.text-icon-aa {\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t\\\\tfont-weight: var(--weight-semibold);\\\\n\\\\t\\\\tcolor: var(--color-text-secondary);\\\\n\\\\t\\\\tline-height: 1;\\\\n\\\\t}\\\\n\\\\n\\\\t.example-image-container {\\\\n\\\\t\\\\twidth: var(--size-8);\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tmargin-bottom: var(--spacing-lg);\\\\n\\\\t}\\\\n\\\\n\\\\t.example-image-container :global(img) {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t}\\\\n\\\\n\\\\t.image-overlay {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\ttop: 0;\\\\n\\\\t\\\\tleft: 0;\\\\n\\\\t\\\\tright: 0;\\\\n\\\\t\\\\tbottom: 0;\\\\n\\\\t\\\\tbackground: rgba(0, 0, 0, 0.6);\\\\n\\\\t\\\\tcolor: white;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\tfont-size: var(--text-lg);\\\\n\\\\t\\\\tfont-weight: var(--weight-semibold);\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t}\\\\n\\\\n\\\\t.file-overlay {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tinset: 0;\\\\n\\\\t\\\\tbackground: rgba(0, 0, 0, 0.6);\\\\n\\\\t\\\\tcolor: white;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t\\\\tfont-weight: var(--weight-semibold);\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAiKC,kCAAqB,CACpB,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,MAAM,CAAE,IACT,CAEA,0BAAa,CACZ,WAAW,CAAE,MAAM,CACnB,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,MAAM,CAAE,IAAI,CACZ,SAAS,CAAE,CACZ,CAEA,uBAAS,CAAS,GAAK,CACtB,cAAc,CAAE,IACjB,CAEA,uBAAU,CACT,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,IAAI,aAAa,CAAC,CAC3B,OAAO,CAAE,IAAI,CACb,qBAAqB,CAAE,OAAO,QAAQ,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAC3D,GAAG,CAAE,IAAI,YAAY,CAAC,CACtB,SAAS,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAC9D,CAEA,sBAAS,CACR,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,WAAW,CAAE,UAAU,CACvB,OAAO,CAAE,IAAI,aAAa,CAAC,CAC3B,MAAM,CAAE,IAAI,CACZ,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,MAAM,CAAE,OAAO,CACf,UAAU,CAAE,GAAG,CAAC,KAAK,CAAC,WAAW,CACjC,KAAK,CAAE,IAAI,CACX,GAAG,CAAE,IAAI,YAAY,CAAC,CACtB,MAAM,CAAE,IAAI,oBAAoB,CAAC,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CACjE,SAAS,CAAE,WAAW,GAAG,CAC1B,CAEA,sBAAQ,MAAO,CACd,SAAS,CAAE,WAAW,IAAI,CAAC,CAC3B,gBAAgB,CAAE,IAAI,mBAAmB,CAC1C,CAEA,8BAAiB,CAChB,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,WAAW,CAAE,UAAU,CACvB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CAEA,mCAAsB,CACrB,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,IACb,CAEA,2BAAc,CACb,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,UAAU,CAAE,IAAI,CAChB,QAAQ,CAAE,MAAM,CAChB,aAAa,CAAE,QAChB,CAEA,iCAAoB,CACnB,OAAO,CAAE,IAAI,CACb,GAAG,CAAE,IAAI,YAAY,CAAC,CACtB,aAAa,CAAE,IAAI,YAAY,CAAC,CAChC,KAAK,CAAE,IACR,CAEA,2BAAc,CACb,WAAW,CAAE,CAAC,CACd,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,MAAM,CAAE,IAAI,oBAAoB,CAAC,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CACjE,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,QAAQ,CAAE,QACX,CAEA,2BAAa,CAAS,GAAK,CAC1B,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,sBAAsB,CAClC,CAEA,2BAAc,CACb,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,WAAW,CAAE,IAAI,iBAAiB,CAAC,CACnC,KAAK,CAAE,IAAI,sBAAsB,CAAC,CAClC,WAAW,CAAE,CACd,CAEA,sCAAyB,CACxB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,QAAQ,CAClB,aAAa,CAAE,IAAI,YAAY,CAChC,CAEA,sCAAwB,CAAS,GAAK,CACrC,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,KACb,CAEA,4BAAe,CACd,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,CAAC,CACN,IAAI,CAAE,CAAC,CACP,KAAK,CAAE,CAAC,CACR,MAAM,CAAE,CAAC,CACT,UAAU,CAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAC9B,KAAK,CAAE,KAAK,CACZ,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,WAAW,CAAE,IAAI,iBAAiB,CAAC,CACnC,aAAa,CAAE,IAAI,WAAW,CAC/B,CAEA,2BAAc,CACb,QAAQ,CAAE,QAAQ,CAClB,KAAK,CAAE,CAAC,CACR,UAAU,CAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAC9B,KAAK,CAAE,KAAK,CACZ,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,WAAW,CAAE,IAAI,iBAAiB,CAAC,CACnC,aAAa,CAAE,IAAI,WAAW,CAC/B\"}'},Ee=M((A,t,e,s)=>{let{examples:a=null}=t,{placeholder:o=null}=t,{latex_delimiters:l}=t;return ut(),t.examples===void 0&&e.examples&&a!==void 0&&e.examples(a),t.placeholder===void 0&&e.placeholder&&o!==void 0&&e.placeholder(o),t.latex_delimiters===void 0&&e.latex_delimiters&&l!==void 0&&e.latex_delimiters(l),A.css.add(Ie),`<div class=\"placeholder-content svelte-9pi8y1\" role=\"complementary\">${o!==null?`<div class=\"placeholder svelte-9pi8y1\">${C(_t,\"Markdown\").$$render(A,{message:o,latex_delimiters:l},{},{})}</div>`:\"\"} ${a!==null?`<div class=\"examples svelte-9pi8y1\" role=\"list\">${ot(a,(n,r)=>`<button class=\"example svelte-9pi8y1\"${k(\"aria-label\",`Select example ${r+1}: ${n.display_text||n.text}`,0)}><div class=\"example-content svelte-9pi8y1\">${n?.icon?.url?`<div class=\"example-image-container svelte-9pi8y1\">${C(rt,\"Image\").$$render(A,{class:\"example-image\",src:n.icon.url,alt:\"Example icon\"},{},{})} </div>`:`${n?.icon?.mime_type===\"text\"?'<div class=\"example-icon svelte-9pi8y1\" aria-hidden=\"true\" data-svelte-h=\"svelte-15cq9iz\"><span class=\"text-icon-aa svelte-9pi8y1\">Aa</span> </div>':`${n.files!==void 0&&n.files.length>0?`${n.files.length>1?`<div class=\"example-icons-grid svelte-9pi8y1\" role=\"group\" aria-label=\"Example attachments\">${ot(n.files.slice(0,4),(i,d)=>`${i.mime_type?.includes(\"image\")?`<div class=\"example-image-container svelte-9pi8y1\">${C(rt,\"Image\").$$render(A,{class:\"example-image\",src:i.url,alt:i.orig_name||`Example image ${d+1}`},{},{})} ${d===3&&n.files.length>4?`<div class=\"image-overlay svelte-9pi8y1\" role=\"status\"${k(\"aria-label\",`${n.files.length-4} more files`,0)}>+${I(n.files.length-4)} </div>`:\"\"} </div>`:`${i.mime_type?.includes(\"video\")?`<div class=\"example-image-container svelte-9pi8y1\"><video class=\"example-image\"${k(\"src\",i.url,0)} aria-hidden=\"true\"></video> ${d===3&&n.files.length>4?`<div class=\"image-overlay svelte-9pi8y1\" role=\"status\"${k(\"aria-label\",`${n.files.length-4} more files`,0)}>+${I(n.files.length-4)} </div>`:\"\"} </div>`:`<div class=\"example-icon svelte-9pi8y1\"${k(\"aria-label\",`File: ${i.orig_name}`,0)}>${i.mime_type?.includes(\"audio\")?`${C(xt,\"Music\").$$render(A,{},{},{})}`:`${C(vt,\"File\").$$render(A,{},{},{})}`} </div>`}`}`)} ${n.files.length>4?`<div class=\"example-icon svelte-9pi8y1\"><div class=\"file-overlay svelte-9pi8y1\" role=\"status\"${k(\"aria-label\",`${n.files.length-4} more files`,0)}>+${I(n.files.length-4)}</div> </div>`:\"\"} </div>`:`${n.files[0].mime_type?.includes(\"image\")?`<div class=\"example-image-container svelte-9pi8y1\">${C(rt,\"Image\").$$render(A,{class:\"example-image\",src:n.files[0].url,alt:n.files[0].orig_name||\"Example image\"},{},{})} </div>`:`${n.files[0].mime_type?.includes(\"video\")?`<div class=\"example-image-container svelte-9pi8y1\"><video class=\"example-image\"${k(\"src\",n.files[0].url,0)} aria-hidden=\"true\"></video> </div>`:`${n.files[0].mime_type?.includes(\"audio\")?`<div class=\"example-icon svelte-9pi8y1\"${k(\"aria-label\",`File: ${n.files[0].orig_name}`,0)}>${C(xt,\"Music\").$$render(A,{},{},{})} </div>`:`<div class=\"example-icon svelte-9pi8y1\"${k(\"aria-label\",`File: ${n.files[0].orig_name}`,0)}>${C(vt,\"File\").$$render(A,{},{},{})} </div>`}`}`}`}`:\"\"}`}`} <div class=\"example-text-content svelte-9pi8y1\"><span class=\"example-text svelte-9pi8y1\">${I(n.display_text||n.text)}</span> </div></div> </button>`)}</div>`:\"\"} </div>`}),ye=M((A,t,e,s)=>{let{value:a}=t,{watermark:o=null}=t;return Mt(()=>{}),t.value===void 0&&e.value&&a!==void 0&&e.value(a),t.watermark===void 0&&e.watermark&&o!==void 0&&e.watermark(o),`${C(D,\"IconButton\").$$render(A,{Icon:yt,label:\"Copy conversation\"},{},{})}`}),ke={code:`.panel-wrap.svelte-gjtrl6.svelte-gjtrl6{width:100%;overflow-y:auto}.bubble-wrap.svelte-gjtrl6.svelte-gjtrl6{width:100%;overflow-y:auto;height:100%;padding-top:var(--spacing-xxl)}@media(prefers-color-scheme: dark){.bubble-wrap.svelte-gjtrl6.svelte-gjtrl6{background:var(--background-fill-secondary)}}.message-wrap.svelte-gjtrl6 .prose.chatbot.md{opacity:0.8;overflow-wrap:break-word}.message-wrap.svelte-gjtrl6 .message-row .md img{border-radius:var(--radius-xl);margin:var(--size-2);width:400px;max-width:30vw;max-height:30vw}.message-wrap.svelte-gjtrl6 .message a{color:var(--color-text-link);text-decoration:underline}.message-wrap.svelte-gjtrl6 .bot:not(:has(.table-wrap)) table,.message-wrap.svelte-gjtrl6 .bot:not(:has(.table-wrap)) tr,.message-wrap.svelte-gjtrl6 .bot:not(:has(.table-wrap)) td,.message-wrap.svelte-gjtrl6 .bot:not(:has(.table-wrap)) th{border:1px solid var(--border-color-primary)}.message-wrap.svelte-gjtrl6 .user table,.message-wrap.svelte-gjtrl6 .user tr,.message-wrap.svelte-gjtrl6 .user td,.message-wrap.svelte-gjtrl6 .user th{border:1px solid var(--border-color-accent)}.message-wrap.svelte-gjtrl6 span.katex{font-size:var(--text-lg);direction:ltr}.message-wrap.svelte-gjtrl6 span.katex-display{margin-top:0}.message-wrap.svelte-gjtrl6 pre{position:relative}.message-wrap.svelte-gjtrl6 .grid-wrap{max-height:80% !important;max-width:600px;object-fit:contain}.message-wrap.svelte-gjtrl6>div.svelte-gjtrl6 p:not(:first-child){margin-top:var(--spacing-xxl)}.message-wrap.svelte-gjtrl6.svelte-gjtrl6{display:flex;flex-direction:column;justify-content:space-between;margin-bottom:var(--spacing-xxl)}.panel-wrap.svelte-gjtrl6 .message-row:first-child{padding-top:calc(var(--spacing-xxl) * 2)}.scroll-down-button-container.svelte-gjtrl6.svelte-gjtrl6{position:absolute;bottom:10px;left:50%;transform:translateX(-50%);z-index:var(--layer-top)}.scroll-down-button-container.svelte-gjtrl6 button{border-radius:50%;box-shadow:var(--shadow-drop);transition:box-shadow 0.2s ease-in-out,\n\t\t\ttransform 0.2s ease-in-out}.scroll-down-button-container.svelte-gjtrl6 button:hover{box-shadow:var(--shadow-drop),\n\t\t\t0 2px 2px rgba(0, 0, 0, 0.05);transform:translateY(-2px)}.options.svelte-gjtrl6.svelte-gjtrl6{margin-left:auto;padding:var(--spacing-xxl);display:grid;grid-template-columns:repeat(auto-fit, minmax(200px, 1fr));gap:var(--spacing-xxl);max-width:calc(min(4 * 200px + 5 * var(--spacing-xxl), 100%));justify-content:end}.option.svelte-gjtrl6.svelte-gjtrl6{display:flex;flex-direction:column;align-items:center;padding:var(--spacing-xl);border:1px dashed var(--border-color-primary);border-radius:var(--radius-md);background-color:var(--background-fill-secondary);cursor:pointer;transition:var(--button-transition);max-width:var(--size-56);width:100%;justify-content:center}.option.svelte-gjtrl6.svelte-gjtrl6:hover{background-color:var(--color-accent-soft);border-color:var(--border-color-accent)}`,map:'{\"version\":3,\"file\":\"ChatBot.svelte\",\"sources\":[\"ChatBot.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { format_chat_for_sharing, is_last_bot_message, group_messages, load_components, get_components_from_messages } from \\\\\"./utils\\\\\";\\\\nimport { copy } from \\\\\"@gradio/utils\\\\\";\\\\nimport Message from \\\\\"./Message.svelte\\\\\";\\\\nimport { dequal } from \\\\\"dequal/lite\\\\\";\\\\nimport { createEventDispatcher, tick, onMount } from \\\\\"svelte\\\\\";\\\\nimport { Trash, Community, ScrollDownArrow } from \\\\\"@gradio/icons\\\\\";\\\\nimport { IconButtonWrapper, IconButton } from \\\\\"@gradio/atoms\\\\\";\\\\nimport Pending from \\\\\"./Pending.svelte\\\\\";\\\\nimport { ShareError } from \\\\\"@gradio/utils\\\\\";\\\\nimport { Gradio } from \\\\\"@gradio/utils\\\\\";\\\\nimport Examples from \\\\\"./Examples.svelte\\\\\";\\\\nexport let value = [];\\\\nlet old_value = null;\\\\nimport CopyAll from \\\\\"./CopyAll.svelte\\\\\";\\\\nexport let _fetch;\\\\nexport let load_component;\\\\nexport let allow_file_downloads;\\\\nexport let display_consecutive_in_same_bubble;\\\\nlet _components = {};\\\\nconst is_browser = typeof window !== \\\\\"undefined\\\\\";\\\\nasync function update_components() {\\\\n    _components = await load_components(get_components_from_messages(value), _components, load_component);\\\\n}\\\\n$: value, update_components();\\\\nexport let latex_delimiters;\\\\nexport let pending_message = false;\\\\nexport let generating = false;\\\\nexport let selectable = false;\\\\nexport let likeable = false;\\\\nexport let feedback_options;\\\\nexport let feedback_value = null;\\\\nexport let editable = null;\\\\nexport let show_share_button = false;\\\\nexport let show_copy_all_button = false;\\\\nexport let rtl = false;\\\\nexport let show_copy_button = false;\\\\nexport let avatar_images = [null, null];\\\\nexport let sanitize_html = true;\\\\nexport let render_markdown = true;\\\\nexport let line_breaks = true;\\\\nexport let autoscroll = true;\\\\nexport let theme_mode;\\\\nexport let i18n;\\\\nexport let layout = \\\\\"bubble\\\\\";\\\\nexport let placeholder = null;\\\\nexport let upload;\\\\nexport let msg_format = \\\\\"tuples\\\\\";\\\\nexport let examples = null;\\\\nexport let _retryable = false;\\\\nexport let _undoable = false;\\\\nexport let like_user_message = false;\\\\nexport let allow_tags = false;\\\\nexport let watermark = null;\\\\nexport let show_progress = \\\\\"full\\\\\";\\\\nlet target = null;\\\\nlet edit_index = null;\\\\nlet edit_messages = [];\\\\nonMount(() => {\\\\n    target = document.querySelector(\\\\\"div.gradio-container\\\\\");\\\\n});\\\\nlet div;\\\\nlet show_scroll_button = false;\\\\nconst dispatch = createEventDispatcher();\\\\nfunction is_at_bottom() {\\\\n    return div && div.offsetHeight + div.scrollTop > div.scrollHeight - 100;\\\\n}\\\\nfunction scroll_to_bottom() {\\\\n    if (!div)\\\\n        return;\\\\n    div.scrollTo(0, div.scrollHeight);\\\\n    show_scroll_button = false;\\\\n}\\\\nlet scroll_after_component_load = false;\\\\nasync function scroll_on_value_update() {\\\\n    if (!autoscroll)\\\\n        return;\\\\n    if (is_at_bottom()) {\\\\n        scroll_after_component_load = true;\\\\n        await tick();\\\\n        await new Promise((resolve) => setTimeout(resolve, 300));\\\\n        scroll_to_bottom();\\\\n    }\\\\n}\\\\nonMount(() => {\\\\n    if (autoscroll) {\\\\n        scroll_to_bottom();\\\\n    }\\\\n    scroll_on_value_update();\\\\n});\\\\n$: if (value || pending_message || _components) {\\\\n    scroll_on_value_update();\\\\n}\\\\nonMount(() => {\\\\n    function handle_scroll() {\\\\n        if (is_at_bottom()) {\\\\n            show_scroll_button = false;\\\\n        }\\\\n        else {\\\\n            scroll_after_component_load = false;\\\\n            show_scroll_button = true;\\\\n        }\\\\n    }\\\\n    div?.addEventListener(\\\\\"scroll\\\\\", handle_scroll);\\\\n    return () => {\\\\n        div?.removeEventListener(\\\\\"scroll\\\\\", handle_scroll);\\\\n    };\\\\n});\\\\n$: {\\\\n    if (!dequal(value, old_value)) {\\\\n        old_value = value;\\\\n        dispatch(\\\\\"change\\\\\");\\\\n    }\\\\n}\\\\n$: groupedMessages = value && group_messages(value, msg_format, display_consecutive_in_same_bubble);\\\\n$: options = value && get_last_bot_options();\\\\nfunction handle_action(i, message, selected) {\\\\n    if (selected === \\\\\"undo\\\\\" || selected === \\\\\"retry\\\\\") {\\\\n        const val_ = value;\\\\n        let last_index = val_.length - 1;\\\\n        while (val_[last_index].role === \\\\\"assistant\\\\\") {\\\\n            last_index--;\\\\n        }\\\\n        dispatch(selected, {\\\\n            index: val_[last_index].index,\\\\n            value: val_[last_index].content\\\\n        });\\\\n    }\\\\n    else if (selected == \\\\\"edit\\\\\") {\\\\n        edit_index = i;\\\\n        edit_messages.push(message.content);\\\\n    }\\\\n    else if (selected == \\\\\"edit_cancel\\\\\") {\\\\n        edit_index = null;\\\\n    }\\\\n    else if (selected == \\\\\"edit_submit\\\\\") {\\\\n        edit_index = null;\\\\n        dispatch(\\\\\"edit\\\\\", {\\\\n            index: message.index,\\\\n            value: edit_messages[i].slice(),\\\\n            previous_value: message.content\\\\n        });\\\\n    }\\\\n    else {\\\\n        let feedback = selected === \\\\\"Like\\\\\" ? true : selected === \\\\\"Dislike\\\\\" ? false : selected || \\\\\"\\\\\";\\\\n        if (msg_format === \\\\\"tuples\\\\\") {\\\\n            dispatch(\\\\\"like\\\\\", {\\\\n                index: message.index,\\\\n                value: message.content,\\\\n                liked: feedback\\\\n            });\\\\n        }\\\\n        else {\\\\n            if (!groupedMessages)\\\\n                return;\\\\n            const message_group = groupedMessages[i];\\\\n            const [first, last] = [\\\\n                message_group[0],\\\\n                message_group[message_group.length - 1]\\\\n            ];\\\\n            dispatch(\\\\\"like\\\\\", {\\\\n                index: first.index,\\\\n                value: message_group.map((m) => m.content),\\\\n                liked: feedback\\\\n            });\\\\n        }\\\\n    }\\\\n}\\\\nfunction get_last_bot_options() {\\\\n    if (!value || !groupedMessages || groupedMessages.length === 0)\\\\n        return void 0;\\\\n    const last_group = groupedMessages[groupedMessages.length - 1];\\\\n    if (last_group[0].role !== \\\\\"assistant\\\\\")\\\\n        return void 0;\\\\n    return last_group[last_group.length - 1].options;\\\\n}\\\\n<\\/script>\\\\n\\\\n{#if value !== null && value.length > 0}\\\\n\\\\t<IconButtonWrapper>\\\\n\\\\t\\\\t{#if show_share_button}\\\\n\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\tIcon={Community}\\\\n\\\\t\\\\t\\\\t\\\\ton:click={async () => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ttry {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t// @ts-ignore\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tconst formatted = await format_chat_for_sharing(value);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdispatch(\\\\\"share\\\\\", {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdescription: formatted\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t});\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t} catch (e) {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tconsole.error(e);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tlet message = e instanceof ShareError ? e.message : \\\\\"Share failed.\\\\\";\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdispatch(\\\\\"error\\\\\", message);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\tIcon={Trash}\\\\n\\\\t\\\\t\\\\ton:click={() => dispatch(\\\\\"clear\\\\\")}\\\\n\\\\t\\\\t\\\\tlabel={i18n(\\\\\"chatbot.clear\\\\\")}\\\\n\\\\t\\\\t></IconButton>\\\\n\\\\t\\\\t{#if show_copy_all_button}\\\\n\\\\t\\\\t\\\\t<CopyAll {value} {watermark} />\\\\n\\\\t\\\\t{/if}\\\\n\\\\t</IconButtonWrapper>\\\\n{/if}\\\\n\\\\n<div\\\\n\\\\tclass={layout === \\\\\"bubble\\\\\" ? \\\\\"bubble-wrap\\\\\" : \\\\\"panel-wrap\\\\\"}\\\\n\\\\tbind:this={div}\\\\n\\\\trole=\\\\\"log\\\\\"\\\\n\\\\taria-label=\\\\\"chatbot conversation\\\\\"\\\\n\\\\taria-live=\\\\\"polite\\\\\"\\\\n>\\\\n\\\\t{#if value !== null && value.length > 0 && groupedMessages !== null}\\\\n\\\\t\\\\t<div class=\\\\\"message-wrap\\\\\" use:copy>\\\\n\\\\t\\\\t\\\\t{#each groupedMessages as messages, i}\\\\n\\\\t\\\\t\\\\t\\\\t{@const role = messages[0].role === \\\\\"user\\\\\" ? \\\\\"user\\\\\" : \\\\\"bot\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t{@const avatar_img = avatar_images[role === \\\\\"user\\\\\" ? 0 : 1]}\\\\n\\\\t\\\\t\\\\t\\\\t{@const opposite_avatar_img = avatar_images[role === \\\\\"user\\\\\" ? 0 : 1]}\\\\n\\\\t\\\\t\\\\t\\\\t{@const feedback_index = groupedMessages\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t.slice(0, i)\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t.filter((m) => m[0].role === \\\\\"assistant\\\\\").length}\\\\n\\\\t\\\\t\\\\t\\\\t{@const current_feedback =\\\\n\\\\t\\\\t\\\\t\\\\t\\\\trole === \\\\\"bot\\\\\" && feedback_value && feedback_value[feedback_index]\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t? feedback_value[feedback_index]\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t: null}\\\\n\\\\t\\\\t\\\\t\\\\t<Message\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{messages}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{display_consecutive_in_same_bubble}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{opposite_avatar_img}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{avatar_img}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{role}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{layout}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{dispatch}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{_fetch}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{target}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{selectable}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{render_markdown}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{rtl}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{value}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{_components}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{generating}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{msg_format}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{feedback_options}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{current_feedback}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{allow_tags}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{watermark}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tshow_like={role === \\\\\"user\\\\\" ? likeable && like_user_message : likeable}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tshow_retry={_retryable && is_last_bot_message(messages, value)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tshow_undo={_undoable && is_last_bot_message(messages, value)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tshow_edit={editable === \\\\\"all\\\\\" ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t(editable == \\\\\"user\\\\\" &&\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\trole === \\\\\"user\\\\\" &&\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmessages.length > 0 &&\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmessages[messages.length - 1].type == \\\\\"text\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tin_edit_mode={edit_index === i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tbind:edit_messages\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{show_copy_button}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\thandle_action={(selected) => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tif (selected == \\\\\"edit\\\\\") {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tedit_messages.splice(0, edit_messages.length);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tif (selected === \\\\\"edit\\\\\" || selected === \\\\\"edit_submit\\\\\") {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmessages.forEach((msg, index) => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\thandle_action(selected === \\\\\"edit\\\\\" ? i : index, msg, selected);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t});\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t} else {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\thandle_action(i, messages[0], selected);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tscroll={is_browser ? scroll : () => {}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{allow_file_downloads}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:copy={(e) => dispatch(\\\\\"copy\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t{#if show_progress !== \\\\\"hidden\\\\\" && generating && messages[messages.length - 1].role === \\\\\"assistant\\\\\" && messages[messages.length - 1].metadata?.status === \\\\\"done\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<Pending {layout} {avatar_images} />\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t{#if show_progress !== \\\\\"hidden\\\\\" && pending_message}\\\\n\\\\t\\\\t\\\\t\\\\t<Pending {layout} {avatar_images} />\\\\n\\\\t\\\\t\\\\t{:else if options}\\\\n\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"options\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#each options as option, index}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"option\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() =>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdispatch(\\\\\"option_select\\\\\", {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tindex: index,\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tvalue: option.value\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t})}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{option.label || option.value}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t</div>\\\\n\\\\t{:else}\\\\n\\\\t\\\\t<Examples\\\\n\\\\t\\\\t\\\\t{examples}\\\\n\\\\t\\\\t\\\\t{placeholder}\\\\n\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\ton:example_select={(e) => dispatch(\\\\\"example_select\\\\\", e.detail)}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n{#if show_scroll_button}\\\\n\\\\t<div class=\\\\\"scroll-down-button-container\\\\\">\\\\n\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\tIcon={ScrollDownArrow}\\\\n\\\\t\\\\t\\\\tlabel=\\\\\"Scroll down\\\\\"\\\\n\\\\t\\\\t\\\\tsize=\\\\\"large\\\\\"\\\\n\\\\t\\\\t\\\\ton:click={scroll_to_bottom}\\\\n\\\\t\\\\t/>\\\\n\\\\t</div>\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.panel-wrap {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\toverflow-y: auto;\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble-wrap {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\toverflow-y: auto;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tpadding-top: var(--spacing-xxl);\\\\n\\\\t}\\\\n\\\\n\\\\t@media (prefers-color-scheme: dark) {\\\\n\\\\t\\\\t.bubble-wrap {\\\\n\\\\t\\\\t\\\\tbackground: var(--background-fill-secondary);\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap :global(.prose.chatbot.md) {\\\\n\\\\t\\\\topacity: 0.8;\\\\n\\\\t\\\\toverflow-wrap: break-word;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap :global(.message-row .md img) {\\\\n\\\\t\\\\tborder-radius: var(--radius-xl);\\\\n\\\\t\\\\tmargin: var(--size-2);\\\\n\\\\t\\\\twidth: 400px;\\\\n\\\\t\\\\tmax-width: 30vw;\\\\n\\\\t\\\\tmax-height: 30vw;\\\\n\\\\t}\\\\n\\\\n\\\\t/* link styles */\\\\n\\\\t.message-wrap :global(.message a) {\\\\n\\\\t\\\\tcolor: var(--color-text-link);\\\\n\\\\t\\\\ttext-decoration: underline;\\\\n\\\\t}\\\\n\\\\n\\\\t/* table styles */\\\\n\\\\t.message-wrap :global(.bot:not(:has(.table-wrap)) table),\\\\n\\\\t.message-wrap :global(.bot:not(:has(.table-wrap)) tr),\\\\n\\\\t.message-wrap :global(.bot:not(:has(.table-wrap)) td),\\\\n\\\\t.message-wrap :global(.bot:not(:has(.table-wrap)) th) {\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap :global(.user table),\\\\n\\\\t.message-wrap :global(.user tr),\\\\n\\\\t.message-wrap :global(.user td),\\\\n\\\\t.message-wrap :global(.user th) {\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-accent);\\\\n\\\\t}\\\\n\\\\n\\\\t/* KaTeX */\\\\n\\\\t.message-wrap :global(span.katex) {\\\\n\\\\t\\\\tfont-size: var(--text-lg);\\\\n\\\\t\\\\tdirection: ltr;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap :global(span.katex-display) {\\\\n\\\\t\\\\tmargin-top: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap :global(pre) {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap :global(.grid-wrap) {\\\\n\\\\t\\\\tmax-height: 80% !important;\\\\n\\\\t\\\\tmax-width: 600px;\\\\n\\\\t\\\\tobject-fit: contain;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap > div :global(p:not(:first-child)) {\\\\n\\\\t\\\\tmargin-top: var(--spacing-xxl);\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\tmargin-bottom: var(--spacing-xxl);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel-wrap :global(.message-row:first-child) {\\\\n\\\\t\\\\tpadding-top: calc(var(--spacing-xxl) * 2);\\\\n\\\\t}\\\\n\\\\n\\\\t.scroll-down-button-container {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tbottom: 10px;\\\\n\\\\t\\\\tleft: 50%;\\\\n\\\\t\\\\ttransform: translateX(-50%);\\\\n\\\\t\\\\tz-index: var(--layer-top);\\\\n\\\\t}\\\\n\\\\t.scroll-down-button-container :global(button) {\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop);\\\\n\\\\t\\\\ttransition:\\\\n\\\\t\\\\t\\\\tbox-shadow 0.2s ease-in-out,\\\\n\\\\t\\\\t\\\\ttransform 0.2s ease-in-out;\\\\n\\\\t}\\\\n\\\\t.scroll-down-button-container :global(button:hover) {\\\\n\\\\t\\\\tbox-shadow:\\\\n\\\\t\\\\t\\\\tvar(--shadow-drop),\\\\n\\\\t\\\\t\\\\t0 2px 2px rgba(0, 0, 0, 0.05);\\\\n\\\\t\\\\ttransform: translateY(-2px);\\\\n\\\\t}\\\\n\\\\n\\\\t.options {\\\\n\\\\t\\\\tmargin-left: auto;\\\\n\\\\t\\\\tpadding: var(--spacing-xxl);\\\\n\\\\t\\\\tdisplay: grid;\\\\n\\\\t\\\\tgrid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\\\n\\\\t\\\\tgap: var(--spacing-xxl);\\\\n\\\\t\\\\tmax-width: calc(min(4 * 200px + 5 * var(--spacing-xxl), 100%));\\\\n\\\\t\\\\tjustify-content: end;\\\\n\\\\t}\\\\n\\\\n\\\\t.option {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tpadding: var(--spacing-xl);\\\\n\\\\t\\\\tborder: 1px dashed var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t\\\\tbackground-color: var(--background-fill-secondary);\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\ttransition: var(--button-transition);\\\\n\\\\t\\\\tmax-width: var(--size-56);\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t}\\\\n\\\\n\\\\t.option:hover {\\\\n\\\\t\\\\tbackground-color: var(--color-accent-soft);\\\\n\\\\t\\\\tborder-color: var(--border-color-accent);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAwUC,uCAAY,CACX,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,IACb,CAEA,wCAAa,CACZ,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,IAAI,CAChB,MAAM,CAAE,IAAI,CACZ,WAAW,CAAE,IAAI,aAAa,CAC/B,CAEA,MAAO,uBAAuB,IAAI,CAAE,CACnC,wCAAa,CACZ,UAAU,CAAE,IAAI,2BAA2B,CAC5C,CACD,CAEA,2BAAa,CAAS,iBAAmB,CACxC,OAAO,CAAE,GAAG,CACZ,aAAa,CAAE,UAChB,CAEA,2BAAa,CAAS,oBAAsB,CAC3C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,KAAK,CACZ,SAAS,CAAE,IAAI,CACf,UAAU,CAAE,IACb,CAGA,2BAAa,CAAS,UAAY,CACjC,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,eAAe,CAAE,SAClB,CAGA,2BAAa,CAAS,iCAAkC,CACxD,2BAAa,CAAS,8BAA+B,CACrD,2BAAa,CAAS,8BAA+B,CACrD,2BAAa,CAAS,8BAAgC,CACrD,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAC7C,CAEA,2BAAa,CAAS,WAAY,CAClC,2BAAa,CAAS,QAAS,CAC/B,2BAAa,CAAS,QAAS,CAC/B,2BAAa,CAAS,QAAU,CAC/B,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,qBAAqB,CAC5C,CAGA,2BAAa,CAAS,UAAY,CACjC,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,SAAS,CAAE,GACZ,CAEA,2BAAa,CAAS,kBAAoB,CACzC,UAAU,CAAE,CACb,CAEA,2BAAa,CAAS,GAAK,CAC1B,QAAQ,CAAE,QACX,CAEA,2BAAa,CAAS,UAAY,CACjC,UAAU,CAAE,GAAG,CAAC,UAAU,CAC1B,SAAS,CAAE,KAAK,CAChB,UAAU,CAAE,OACb,CAEA,2BAAa,CAAG,iBAAG,CAAS,mBAAqB,CAChD,UAAU,CAAE,IAAI,aAAa,CAC9B,CAEA,yCAAc,CACb,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,aAAa,CAC9B,aAAa,CAAE,IAAI,aAAa,CACjC,CAEA,yBAAW,CAAS,wBAA0B,CAC7C,WAAW,CAAE,KAAK,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CACzC,CAEA,yDAA8B,CAC7B,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,IAAI,CACZ,IAAI,CAAE,GAAG,CACT,SAAS,CAAE,WAAW,IAAI,CAAC,CAC3B,OAAO,CAAE,IAAI,WAAW,CACzB,CACA,2CAA6B,CAAS,MAAQ,CAC7C,aAAa,CAAE,GAAG,CAClB,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,UAAU,CACT,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC;AAC/B,GAAG,SAAS,CAAC,IAAI,CAAC,WACjB,CACA,2CAA6B,CAAS,YAAc,CACnD,UAAU,CACT,IAAI,aAAa,CAAC,CAAC;AACtB,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAC9B,SAAS,CAAE,WAAW,IAAI,CAC3B,CAEA,oCAAS,CACR,WAAW,CAAE,IAAI,CACjB,OAAO,CAAE,IAAI,aAAa,CAAC,CAC3B,OAAO,CAAE,IAAI,CACb,qBAAqB,CAAE,OAAO,QAAQ,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAC3D,GAAG,CAAE,IAAI,aAAa,CAAC,CACvB,SAAS,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAC9D,eAAe,CAAE,GAClB,CAEA,mCAAQ,CACP,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,WAAW,CAAE,MAAM,CACnB,OAAO,CAAE,IAAI,YAAY,CAAC,CAC1B,MAAM,CAAE,GAAG,CAAC,MAAM,CAAC,IAAI,sBAAsB,CAAC,CAC9C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,gBAAgB,CAAE,IAAI,2BAA2B,CAAC,CAClD,MAAM,CAAE,OAAO,CACf,UAAU,CAAE,IAAI,mBAAmB,CAAC,CACpC,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,KAAK,CAAE,IAAI,CACX,eAAe,CAAE,MAClB,CAEA,mCAAO,MAAO,CACb,gBAAgB,CAAE,IAAI,mBAAmB,CAAC,CAC1C,YAAY,CAAE,IAAI,qBAAqB,CACxC\"}'},Me=M((A,t,e,s)=>{let a,o,{value:l=[]}=t,n=null,{_fetch:r}=t,{load_component:i}=t,{allow_file_downloads:d}=t,{display_consecutive_in_same_bubble:m}=t,u={};const f=typeof window<\"u\";async function g(){u=await ne(ae(l),u,i)}let{latex_delimiters:_}=t,{pending_message:x=!1}=t,{generating:h=!1}=t,{selectable:p=!1}=t,{likeable:c=!1}=t,{feedback_options:E}=t,{feedback_value:v=null}=t,{editable:B=null}=t,{show_share_button:O=!1}=t,{show_copy_all_button:z=!1}=t,{rtl:Y=!1}=t,{show_copy_button:T=!1}=t,{avatar_images:G=[null,null]}=t,{sanitize_html:R=!0}=t,{render_markdown:N=!0}=t,{line_breaks:J=!0}=t,{autoscroll:$=!0}=t,{theme_mode:U}=t,{i18n:Z}=t,{layout:W=\"bubble\"}=t,{placeholder:Q=null}=t,{upload:S}=t,{msg_format:K=\"tuples\"}=t,{examples:L=null}=t,{_retryable:V=!1}=t,{_undoable:H=!1}=t,{like_user_message:j=!1}=t,{allow_tags:At=!1}=t,{watermark:tt=null}=t,{show_progress:F=\"full\"}=t,y=null,b=null,nt=[];ct(()=>{y=document.querySelector(\"div.gradio-container\")});let Ot;const it=ut();async function ft(){}ct(()=>{ft()}),ct(()=>()=>{});function gt(w,X,q){if(q===\"undo\"||q===\"retry\"){const at=l;let et=at.length-1;for(;at[et].role===\"assistant\";)et--;it(q,{index:at[et].index,value:at[et].content})}else if(q==\"edit\")b=w,nt.push(X.content);else if(q==\"edit_cancel\")b=null;else if(q==\"edit_submit\")b=null,it(\"edit\",{index:X.index,value:nt[w].slice(),previous_value:X.content});else{let at=q===\"Like\"?!0:q===\"Dislike\"?!1:q||\"\";if(K===\"tuples\")it(\"like\",{index:X.index,value:X.content,liked:at});else{if(!a)return;const et=a[w],[st,ht]=[et[0],et[et.length-1]];it(\"like\",{index:st.index,value:et.map(P=>P.content),liked:at})}}}function Wt(){if(!l||!a||a.length===0)return;const w=a[a.length-1];if(w[0].role===\"assistant\")return w[w.length-1].options}t.value===void 0&&e.value&&l!==void 0&&e.value(l),t._fetch===void 0&&e._fetch&&r!==void 0&&e._fetch(r),t.load_component===void 0&&e.load_component&&i!==void 0&&e.load_component(i),t.allow_file_downloads===void 0&&e.allow_file_downloads&&d!==void 0&&e.allow_file_downloads(d),t.display_consecutive_in_same_bubble===void 0&&e.display_consecutive_in_same_bubble&&m!==void 0&&e.display_consecutive_in_same_bubble(m),t.latex_delimiters===void 0&&e.latex_delimiters&&_!==void 0&&e.latex_delimiters(_),t.pending_message===void 0&&e.pending_message&&x!==void 0&&e.pending_message(x),t.generating===void 0&&e.generating&&h!==void 0&&e.generating(h),t.selectable===void 0&&e.selectable&&p!==void 0&&e.selectable(p),t.likeable===void 0&&e.likeable&&c!==void 0&&e.likeable(c),t.feedback_options===void 0&&e.feedback_options&&E!==void 0&&e.feedback_options(E),t.feedback_value===void 0&&e.feedback_value&&v!==void 0&&e.feedback_value(v),t.editable===void 0&&e.editable&&B!==void 0&&e.editable(B),t.show_share_button===void 0&&e.show_share_button&&O!==void 0&&e.show_share_button(O),t.show_copy_all_button===void 0&&e.show_copy_all_button&&z!==void 0&&e.show_copy_all_button(z),t.rtl===void 0&&e.rtl&&Y!==void 0&&e.rtl(Y),t.show_copy_button===void 0&&e.show_copy_button&&T!==void 0&&e.show_copy_button(T),t.avatar_images===void 0&&e.avatar_images&&G!==void 0&&e.avatar_images(G),t.sanitize_html===void 0&&e.sanitize_html&&R!==void 0&&e.sanitize_html(R),t.render_markdown===void 0&&e.render_markdown&&N!==void 0&&e.render_markdown(N),t.line_breaks===void 0&&e.line_breaks&&J!==void 0&&e.line_breaks(J),t.autoscroll===void 0&&e.autoscroll&&$!==void 0&&e.autoscroll($),t.theme_mode===void 0&&e.theme_mode&&U!==void 0&&e.theme_mode(U),t.i18n===void 0&&e.i18n&&Z!==void 0&&e.i18n(Z),t.layout===void 0&&e.layout&&W!==void 0&&e.layout(W),t.placeholder===void 0&&e.placeholder&&Q!==void 0&&e.placeholder(Q),t.upload===void 0&&e.upload&&S!==void 0&&e.upload(S),t.msg_format===void 0&&e.msg_format&&K!==void 0&&e.msg_format(K),t.examples===void 0&&e.examples&&L!==void 0&&e.examples(L),t._retryable===void 0&&e._retryable&&V!==void 0&&e._retryable(V),t._undoable===void 0&&e._undoable&&H!==void 0&&e._undoable(H),t.like_user_message===void 0&&e.like_user_message&&j!==void 0&&e.like_user_message(j),t.allow_tags===void 0&&e.allow_tags&&At!==void 0&&e.allow_tags(At),t.watermark===void 0&&e.watermark&&tt!==void 0&&e.watermark(tt),t.show_progress===void 0&&e.show_progress&&F!==void 0&&e.show_progress(F),A.css.add(ke);let dt,pt,Gt=A.head;do dt=!0,A.head=Gt,g(),(l||x||u)&&ft(),Rt(l,n)||(n=l,it(\"change\")),a=l&&Ae(l,K,m),o=l&&Wt(),pt=`${l!==null&&l.length>0?`${C(kt,\"IconButtonWrapper\").$$render(A,{},{},{default:()=>`${O?`${C(D,\"IconButton\").$$render(A,{Icon:Vt},{},{})}`:\"\"} ${C(D,\"IconButton\").$$render(A,{Icon:Ht,label:Z(\"chatbot.clear\")},{},{})} ${z?`${C(ye,\"CopyAll\").$$render(A,{value:l,watermark:tt},{},{})}`:\"\"}`})}`:\"\"} <div class=\"${I(Et(W===\"bubble\"?\"bubble-wrap\":\"panel-wrap\"),!0)+\" svelte-gjtrl6\"}\" role=\"log\" aria-label=\"chatbot conversation\" aria-live=\"polite\"${k(\"this\",Ot,0)}>${l!==null&&l.length>0&&a!==null?`<div class=\"message-wrap svelte-gjtrl6\">${ot(a,(w,X)=>{let q=w[0].role===\"user\"?\"user\":\"bot\",at=G[q===\"user\"?0:1],et=G[q===\"user\"?0:1],st=a.slice(0,X).filter(P=>P[0].role===\"assistant\").length,ht=q===\"bot\"&&v&&v[st]?v[st]:null;return`     ${C(we,\"Message\").$$render(A,{messages:w,display_consecutive_in_same_bubble:m,opposite_avatar_img:et,avatar_img:at,role:q,layout:W,dispatch:it,i18n:Z,_fetch:r,line_breaks:J,theme_mode:U,target:y,upload:S,selectable:p,sanitize_html:R,render_markdown:N,rtl:Y,i:X,value:l,latex_delimiters:_,_components:u,generating:h,msg_format:K,feedback_options:E,current_feedback:ht,allow_tags:At,watermark:tt,show_like:q===\"user\"?c&&j:c,show_retry:V&&bt(w,l),show_undo:H&&bt(w,l),show_edit:B===\"all\"||B==\"user\"&&q===\"user\"&&w.length>0&&w[w.length-1].type==\"text\",in_edit_mode:b===X,show_copy_button:T,handle_action:P=>{P==\"edit\"&&nt.splice(0,nt.length),P===\"edit\"||P===\"edit_submit\"?w.forEach((zt,Yt)=>{gt(P===\"edit\"?X:Yt,zt,P)}):gt(X,w[0],P)},scroll:f?scroll:()=>{},allow_file_downloads:d,edit_messages:nt},{edit_messages:P=>{nt=P,dt=!1}},{})} ${F!==\"hidden\"&&h&&w[w.length-1].role===\"assistant\"&&w[w.length-1].metadata?.status===\"done\"?`${C(It,\"Pending\").$$render(A,{layout:W,avatar_images:G},{},{})}`:\"\"}`})} ${F!==\"hidden\"&&x?`${C(It,\"Pending\").$$render(A,{layout:W,avatar_images:G},{},{})}`:`${o?`<div class=\"options svelte-gjtrl6\">${ot(o,(w,X)=>`<button class=\"option svelte-gjtrl6\">${I(w.label||w.value)} </button>`)}</div>`:\"\"}`}</div>`:`${C(Ee,\"Examples\").$$render(A,{examples:L,placeholder:Q,latex_delimiters:_},{},{})}`}</div> `;while(!dt);return pt}),Ke=Me,Ue={code:\".wrapper.svelte-g3p8na{display:flex;position:relative;flex-direction:column;align-items:start;width:100%;height:100%;flex-grow:1}.progress-text{right:auto}\",map:'{\"version\":3,\"file\":\"Index.svelte\",\"sources\":[\"Index.svelte\"],\"sourcesContent\":[\"<script context=\\\\\"module\\\\\" lang=\\\\\"ts\\\\\">export { default as BaseChatBot } from \\\\\"./shared/ChatBot.svelte\\\\\";\\\\n<\\/script>\\\\n\\\\n<script lang=\\\\\"ts\\\\\">import ChatBot from \\\\\"./shared/ChatBot.svelte\\\\\";\\\\nimport { Block, BlockLabel } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { Chat } from \\\\\"@gradio/icons\\\\\";\\\\nimport { StatusTracker } from \\\\\"@gradio/statustracker\\\\\";\\\\nimport { normalise_tuples, normalise_messages } from \\\\\"./shared/utils\\\\\";\\\\nexport let elem_id = \\\\\"\\\\\";\\\\nexport let elem_classes = [];\\\\nexport let visible = true;\\\\nexport let value = [];\\\\nexport let scale = null;\\\\nexport let min_width = void 0;\\\\nexport let label;\\\\nexport let show_label = true;\\\\nexport let root;\\\\nexport let _selectable = false;\\\\nexport let likeable = false;\\\\nexport let feedback_options = [\\\\\"Like\\\\\", \\\\\"Dislike\\\\\"];\\\\nexport let feedback_value = null;\\\\nexport let show_share_button = false;\\\\nexport let rtl = false;\\\\nexport let show_copy_button = true;\\\\nexport let show_copy_all_button = false;\\\\nexport let sanitize_html = true;\\\\nexport let layout = \\\\\"bubble\\\\\";\\\\nexport let type = \\\\\"tuples\\\\\";\\\\nexport let render_markdown = true;\\\\nexport let line_breaks = true;\\\\nexport let autoscroll = true;\\\\nexport let _retryable = false;\\\\nexport let _undoable = false;\\\\nexport let group_consecutive_messages = true;\\\\nexport let allow_tags = false;\\\\nexport let latex_delimiters;\\\\nexport let gradio;\\\\nlet _value = [];\\\\n$: _value = type === \\\\\"tuples\\\\\" ? normalise_tuples(value, root) : normalise_messages(value, root);\\\\nexport let avatar_images = [null, null];\\\\nexport let like_user_message = false;\\\\nexport let loading_status = void 0;\\\\nexport let height;\\\\nexport let resizable;\\\\nexport let min_height;\\\\nexport let max_height;\\\\nexport let editable = null;\\\\nexport let placeholder = null;\\\\nexport let examples = null;\\\\nexport let theme_mode;\\\\nexport let allow_file_downloads = true;\\\\nexport let watermark = null;\\\\n<\\/script>\\\\n\\\\n<Block\\\\n\\\\t{elem_id}\\\\n\\\\t{elem_classes}\\\\n\\\\t{visible}\\\\n\\\\tpadding={false}\\\\n\\\\t{scale}\\\\n\\\\t{min_width}\\\\n\\\\t{height}\\\\n\\\\t{resizable}\\\\n\\\\t{min_height}\\\\n\\\\t{max_height}\\\\n\\\\tallow_overflow={true}\\\\n\\\\tflex={true}\\\\n\\\\toverflow_behavior=\\\\\"auto\\\\\"\\\\n>\\\\n\\\\t{#if loading_status}\\\\n\\\\t\\\\t<StatusTracker\\\\n\\\\t\\\\t\\\\tautoscroll={gradio.autoscroll}\\\\n\\\\t\\\\t\\\\ti18n={gradio.i18n}\\\\n\\\\t\\\\t\\\\t{...loading_status}\\\\n\\\\t\\\\t\\\\tshow_progress={loading_status.show_progress === \\\\\"hidden\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t? \\\\\"hidden\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t: \\\\\"minimal\\\\\"}\\\\n\\\\t\\\\t\\\\ton:clear_status={() => gradio.dispatch(\\\\\"clear_status\\\\\", loading_status)}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n\\\\t<div class=\\\\\"wrapper\\\\\">\\\\n\\\\t\\\\t{#if show_label}\\\\n\\\\t\\\\t\\\\t<BlockLabel\\\\n\\\\t\\\\t\\\\t\\\\t{show_label}\\\\n\\\\t\\\\t\\\\t\\\\tIcon={Chat}\\\\n\\\\t\\\\t\\\\t\\\\tfloat={true}\\\\n\\\\t\\\\t\\\\t\\\\tlabel={label || \\\\\"Chatbot\\\\\"}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t\\\\t<ChatBot\\\\n\\\\t\\\\t\\\\ti18n={gradio.i18n}\\\\n\\\\t\\\\t\\\\tselectable={_selectable}\\\\n\\\\t\\\\t\\\\t{likeable}\\\\n\\\\t\\\\t\\\\t{feedback_options}\\\\n\\\\t\\\\t\\\\t{feedback_value}\\\\n\\\\t\\\\t\\\\t{show_share_button}\\\\n\\\\t\\\\t\\\\t{show_copy_all_button}\\\\n\\\\t\\\\t\\\\tvalue={_value}\\\\n\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\tdisplay_consecutive_in_same_bubble={group_consecutive_messages}\\\\n\\\\t\\\\t\\\\t{render_markdown}\\\\n\\\\t\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\t\\\\t{editable}\\\\n\\\\t\\\\t\\\\tpending_message={loading_status?.status === \\\\\"pending\\\\\"}\\\\n\\\\t\\\\t\\\\tgenerating={loading_status?.status === \\\\\"generating\\\\\"}\\\\n\\\\t\\\\t\\\\t{rtl}\\\\n\\\\t\\\\t\\\\t{show_copy_button}\\\\n\\\\t\\\\t\\\\t{like_user_message}\\\\n\\\\t\\\\t\\\\tshow_progress={loading_status?.show_progress || \\\\\"full\\\\\"}\\\\n\\\\t\\\\t\\\\ton:change={() => gradio.dispatch(\\\\\"change\\\\\", value)}\\\\n\\\\t\\\\t\\\\ton:select={(e) => gradio.dispatch(\\\\\"select\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:like={(e) => gradio.dispatch(\\\\\"like\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:share={(e) => gradio.dispatch(\\\\\"share\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:error={(e) => gradio.dispatch(\\\\\"error\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:example_select={(e) => gradio.dispatch(\\\\\"example_select\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:option_select={(e) => gradio.dispatch(\\\\\"option_select\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:retry={(e) => gradio.dispatch(\\\\\"retry\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:undo={(e) => gradio.dispatch(\\\\\"undo\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:clear={() => {\\\\n\\\\t\\\\t\\\\t\\\\tvalue = [];\\\\n\\\\t\\\\t\\\\t\\\\tgradio.dispatch(\\\\\"clear\\\\\");\\\\n\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\ton:copy={(e) => gradio.dispatch(\\\\\"copy\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:edit={(e) => {\\\\n\\\\t\\\\t\\\\t\\\\tif (value === null || value.length === 0) return;\\\\n\\\\t\\\\t\\\\t\\\\tif (type === \\\\\"messages\\\\\") {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t//@ts-ignore\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tvalue[e.detail.index].content = e.detail.value;\\\\n\\\\t\\\\t\\\\t\\\\t} else {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t//@ts-ignore\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tvalue[e.detail.index[0]][e.detail.index[1]] = e.detail.value;\\\\n\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\tvalue = value;\\\\n\\\\t\\\\t\\\\t\\\\tgradio.dispatch(\\\\\"edit\\\\\", e.detail);\\\\n\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t{avatar_images}\\\\n\\\\t\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\t{autoscroll}\\\\n\\\\t\\\\t\\\\t{layout}\\\\n\\\\t\\\\t\\\\t{placeholder}\\\\n\\\\t\\\\t\\\\t{examples}\\\\n\\\\t\\\\t\\\\t{_retryable}\\\\n\\\\t\\\\t\\\\t{_undoable}\\\\n\\\\t\\\\t\\\\tupload={(...args) => gradio.client.upload(...args)}\\\\n\\\\t\\\\t\\\\t_fetch={(...args) => gradio.client.fetch(...args)}\\\\n\\\\t\\\\t\\\\tload_component={gradio.load_component}\\\\n\\\\t\\\\t\\\\tmsg_format={type}\\\\n\\\\t\\\\t\\\\t{allow_file_downloads}\\\\n\\\\t\\\\t\\\\t{allow_tags}\\\\n\\\\t\\\\t\\\\t{watermark}\\\\n\\\\t\\\\t/>\\\\n\\\\t</div>\\\\n</Block>\\\\n\\\\n<style>\\\\n\\\\t.wrapper {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\talign-items: start;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tflex-grow: 1;\\\\n\\\\t}\\\\n\\\\n\\\\t:global(.progress-text) {\\\\n\\\\t\\\\tright: auto;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA4JC,sBAAS,CACR,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,cAAc,CAAE,MAAM,CACtB,WAAW,CAAE,KAAK,CAClB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,SAAS,CAAE,CACZ,CAEQ,cAAgB,CACvB,KAAK,CAAE,IACR\"}'},Te=M((A,t,e,s)=>{let{elem_id:a=\"\"}=t,{elem_classes:o=[]}=t,{visible:l=!0}=t,{value:n=[]}=t,{scale:r=null}=t,{min_width:i=void 0}=t,{label:d}=t,{show_label:m=!0}=t,{root:u}=t,{_selectable:f=!1}=t,{likeable:g=!1}=t,{feedback_options:_=[\"Like\",\"Dislike\"]}=t,{feedback_value:x=null}=t,{show_share_button:h=!1}=t,{rtl:p=!1}=t,{show_copy_button:c=!0}=t,{show_copy_all_button:E=!1}=t,{sanitize_html:v=!0}=t,{layout:B=\"bubble\"}=t,{type:O=\"tuples\"}=t,{render_markdown:z=!0}=t,{line_breaks:Y=!0}=t,{autoscroll:T=!0}=t,{_retryable:G=!1}=t,{_undoable:R=!1}=t,{group_consecutive_messages:N=!0}=t,{allow_tags:J=!1}=t,{latex_delimiters:$}=t,{gradio:U}=t,Z=[],{avatar_images:W=[null,null]}=t,{like_user_message:Q=!1}=t,{loading_status:S=void 0}=t,{height:K}=t,{resizable:L}=t,{min_height:V}=t,{max_height:H}=t,{editable:j=null}=t,{placeholder:At=null}=t,{examples:tt=null}=t,{theme_mode:F}=t,{allow_file_downloads:y=!0}=t,{watermark:b=null}=t;return t.elem_id===void 0&&e.elem_id&&a!==void 0&&e.elem_id(a),t.elem_classes===void 0&&e.elem_classes&&o!==void 0&&e.elem_classes(o),t.visible===void 0&&e.visible&&l!==void 0&&e.visible(l),t.value===void 0&&e.value&&n!==void 0&&e.value(n),t.scale===void 0&&e.scale&&r!==void 0&&e.scale(r),t.min_width===void 0&&e.min_width&&i!==void 0&&e.min_width(i),t.label===void 0&&e.label&&d!==void 0&&e.label(d),t.show_label===void 0&&e.show_label&&m!==void 0&&e.show_label(m),t.root===void 0&&e.root&&u!==void 0&&e.root(u),t._selectable===void 0&&e._selectable&&f!==void 0&&e._selectable(f),t.likeable===void 0&&e.likeable&&g!==void 0&&e.likeable(g),t.feedback_options===void 0&&e.feedback_options&&_!==void 0&&e.feedback_options(_),t.feedback_value===void 0&&e.feedback_value&&x!==void 0&&e.feedback_value(x),t.show_share_button===void 0&&e.show_share_button&&h!==void 0&&e.show_share_button(h),t.rtl===void 0&&e.rtl&&p!==void 0&&e.rtl(p),t.show_copy_button===void 0&&e.show_copy_button&&c!==void 0&&e.show_copy_button(c),t.show_copy_all_button===void 0&&e.show_copy_all_button&&E!==void 0&&e.show_copy_all_button(E),t.sanitize_html===void 0&&e.sanitize_html&&v!==void 0&&e.sanitize_html(v),t.layout===void 0&&e.layout&&B!==void 0&&e.layout(B),t.type===void 0&&e.type&&O!==void 0&&e.type(O),t.render_markdown===void 0&&e.render_markdown&&z!==void 0&&e.render_markdown(z),t.line_breaks===void 0&&e.line_breaks&&Y!==void 0&&e.line_breaks(Y),t.autoscroll===void 0&&e.autoscroll&&T!==void 0&&e.autoscroll(T),t._retryable===void 0&&e._retryable&&G!==void 0&&e._retryable(G),t._undoable===void 0&&e._undoable&&R!==void 0&&e._undoable(R),t.group_consecutive_messages===void 0&&e.group_consecutive_messages&&N!==void 0&&e.group_consecutive_messages(N),t.allow_tags===void 0&&e.allow_tags&&J!==void 0&&e.allow_tags(J),t.latex_delimiters===void 0&&e.latex_delimiters&&$!==void 0&&e.latex_delimiters($),t.gradio===void 0&&e.gradio&&U!==void 0&&e.gradio(U),t.avatar_images===void 0&&e.avatar_images&&W!==void 0&&e.avatar_images(W),t.like_user_message===void 0&&e.like_user_message&&Q!==void 0&&e.like_user_message(Q),t.loading_status===void 0&&e.loading_status&&S!==void 0&&e.loading_status(S),t.height===void 0&&e.height&&K!==void 0&&e.height(K),t.resizable===void 0&&e.resizable&&L!==void 0&&e.resizable(L),t.min_height===void 0&&e.min_height&&V!==void 0&&e.min_height(V),t.max_height===void 0&&e.max_height&&H!==void 0&&e.max_height(H),t.editable===void 0&&e.editable&&j!==void 0&&e.editable(j),t.placeholder===void 0&&e.placeholder&&At!==void 0&&e.placeholder(At),t.examples===void 0&&e.examples&&tt!==void 0&&e.examples(tt),t.theme_mode===void 0&&e.theme_mode&&F!==void 0&&e.theme_mode(F),t.allow_file_downloads===void 0&&e.allow_file_downloads&&y!==void 0&&e.allow_file_downloads(y),t.watermark===void 0&&e.watermark&&b!==void 0&&e.watermark(b),A.css.add(Ue),Z=O===\"tuples\"?te(n,u):$t(n,u),`${C(Ft,\"Block\").$$render(A,{elem_id:a,elem_classes:o,visible:l,padding:!1,scale:r,min_width:i,height:K,resizable:L,min_height:V,max_height:H,allow_overflow:!0,flex:!0,overflow_behavior:\"auto\"},{},{default:()=>`${S?`${C(Nt,\"StatusTracker\").$$render(A,Object.assign({},{autoscroll:U.autoscroll},{i18n:U.i18n},S,{show_progress:S.show_progress===\"hidden\"?\"hidden\":\"minimal\"}),{},{})}`:\"\"} <div class=\"wrapper svelte-g3p8na\">${m?`${C(Xt,\"BlockLabel\").$$render(A,{show_label:m,Icon:Pt,float:!0,label:d||\"Chatbot\"},{},{})}`:\"\"} ${C(Ke,\"ChatBot\").$$render(A,{i18n:U.i18n,selectable:f,likeable:g,feedback_options:_,feedback_value:x,show_share_button:h,show_copy_all_button:E,value:Z,latex_delimiters:$,display_consecutive_in_same_bubble:N,render_markdown:z,theme_mode:F,editable:j,pending_message:S?.status===\"pending\",generating:S?.status===\"generating\",rtl:p,show_copy_button:c,like_user_message:Q,show_progress:S?.show_progress||\"full\",avatar_images:W,sanitize_html:v,line_breaks:Y,autoscroll:T,layout:B,placeholder:At,examples:tt,_retryable:G,_undoable:R,upload:(...nt)=>U.client.upload(...nt),_fetch:(...nt)=>U.client.fetch(...nt),load_component:U.load_component,msg_format:O,allow_file_downloads:y,allow_tags:J,watermark:b},{},{})}</div>`})}`});export{Ke as BaseChatBot,Te as default};\n//# sourceMappingURL=Index60.js.map\n"], "names": ["M", "C", "D", "ot", "Ct", "I", "ut", "Mt", "yt", "kt", "Qt", "jt", "Dt", "Tt", "Zt", "lt", "_t", "vt", "k", "Lt", "rt", "Et", "xt", "u", "j", "Rt", "Vt", "Ht", "Ft", "Nt", "Xt", "Pt"], "mappings": ";;;;;;;;;AAAgoB,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAM,SAAS,CAAC,OAAM,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,GAAG,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,WAAW,EAAE,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,MAAM,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAO,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAM,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,WAAW,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,EAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;AAC5lG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC;AACtD,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;AAChL,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,MAAM,EAAE,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,6oBAA6oB,CAAC,CAAC,EAAE,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,6+BAA6+B,CAAC,CAAC,EAAE,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,uoBAAuoB,CAAC,CAAC,EAAE,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,y+BAAy+B,CAAC,CAAC,EAAE,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,yLAAyL,CAAC,CAAC,EAAE,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,8JAA8J,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,4oBAA4oB,CAAC,GAAG,CAAC,ylIAAylI,CAAC,CAAC,EAAE,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAEC,kBAAC,CAACC,CAAC,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,SAAS,CAAC,qBAAqB,CAAC,+BAA+B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAED,kBAAC,CAACC,CAAC,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,qBAAqB,CAAC,+BAA+B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,qDAAqD,EAAED,kBAAC,CAACC,CAAC,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,+BAA+B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,oDAAoD,EAAEC,IAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,oDAAoD,EAAEC,UAAE,CAAC,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAEC,MAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACL,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACM,qBAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAOC,SAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEN,kBAAC,CAACC,CAAC,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAACM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,ihBAAihB,CAAC,GAAG,CAAC,CAAC,swIAAswI,CAAC,CAAC,CAAC,EAAE,CAACR,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,EAAE,kBAAkB,CAACK,MAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACA,MAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAACA,MAAC,CAAC,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,EAAEJ,kBAAC,CAACQ,IAAE,CAAC,mBAAmB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAER,kBAAC,CAACC,CAAC,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAACQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAET,kBAAC,CAACC,CAAC,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAACS,IAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEV,kBAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEA,kBAAC,CAACC,CAAC,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAACU,IAAE,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEX,kBAAC,CAACC,CAAC,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,IAAI,CAACW,IAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEZ,kBAAC,CAACC,CAAC,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,IAAI,CAACY,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEb,kBAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACD,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,sCAAsC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,sCAAsC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,sCAAsC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,sCAAsC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,EAAEC,kBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEc,iBAAE,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,sCAAsC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,CAAC,EAAEd,kBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEc,iBAAE,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,EAAEd,kBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEc,iBAAE,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,iCAAiC,EAAEd,kBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEc,iBAAE,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,sCAAsC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAEd,kBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEc,iBAAE,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,sCAAsC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAEd,kBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEc,iBAAE,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC,CAAC,sCAAsC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,EAAEd,kBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEc,iBAAE,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,EAAEd,kBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEc,iBAAE,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,KAAK,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,ixBAAixB,CAAC,GAAG,CAAC,4qJAA4qJ,CAAC,CAAC,EAAE,CAACf,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kCAAkC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kCAAkC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,6BAA6B,EAAEC,kBAAC,CAACe,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,WAAW,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,EAAEf,kBAAC,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,WAAW,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC,+EAA+E,EAAEA,kBAAC,CAACgB,EAAE,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,yGAAyG,EAAEC,aAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,EAAEA,aAAC,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,uCAAuC,EAAEb,MAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC,kDAAkD,EAAEA,MAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,krDAAkrD,CAAC,GAAG,CAAC,wySAAwyS,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,OAAM,UAAU,GAAG,CAAC,CAAC,MAAM,EAAE,CAACL,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kCAAkC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kCAAkC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,sDAAsD,EAAE,CAAC,sBAAsB,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAEkB,aAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,GAAG,EAAE,EAAE,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,8DAA8D,EAAEd,UAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAEH,kBAAC,CAACC,CAAC,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAACiB,IAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAElB,kBAAC,CAACe,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,GAAG,SAAS,CAAC,sDAAsD,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,sCAAsC,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAEX,MAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEA,MAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,EAAEA,MAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,MAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAEa,aAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEjB,kBAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,qCAAqC,EAAEE,IAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEF,kBAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,wqOAAwqO,CAAC,GAAG,CAAC,uwtBAAuwtB,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG,WAAW,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,wBAAwB,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,wBAAwB,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,oBAAoB,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAM,MAAC,EAAE,CAACD,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,mBAAmB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kCAAkC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kCAAkC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,YAAY,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,cAAc,CAACK,MAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACA,MAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,6CAA6C,EAAEJ,kBAAC,CAACmB,CAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,WAAW,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,cAAc,EAAE,CAACf,MAAC,CAACgB,aAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAElB,IAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,UAAU,CAACE,MAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC,CAAC,EAAE,CAAC,2BAA2B,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,GAAG,WAAW,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,yDAAyD,EAAED,UAAE,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEC,MAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,EAAEa,aAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,aAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEA,aAAC,CAAC,YAAY,CAAC,CAAC,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,2BAA2B,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAEd,UAAE,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAEc,aAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,EAAEjB,kBAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,kBAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,EAAEA,kBAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,mBAAmB,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAEA,kBAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,0zDAA0zD,CAAC,GAAG,CAAC,4gLAA4gL,CAAC,CAAC,EAAE,CAACD,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,sCAAsC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,6CAA6C,EAAEC,kBAAC,CAACmB,CAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,sBAAsB,CAACf,MAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,sWAAsW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,wkFAAwkF,CAAC,GAAG,CAAC,2pcAA2pc,CAAC,CAAC,EAAE,CAACL,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAOM,qBAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,oEAAoE,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,uCAAuC,EAAEL,kBAAC,CAACe,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,gDAAgD,EAAEb,IAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,qCAAqC,EAAEe,aAAC,CAAC,YAAY,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,4CAA4C,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,mDAAmD,EAAEjB,kBAAC,CAACmB,CAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,CAAC,qJAAqJ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,4FAA4F,EAAEjB,IAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,mDAAmD,EAAEF,kBAAC,CAACmB,CAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,sDAAsD,EAAEF,aAAC,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAEb,MAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,+EAA+E,EAAEa,aAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,6BAA6B,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,sDAAsD,EAAEA,aAAC,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAEb,MAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,uCAAuC,EAAEa,aAAC,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAEjB,kBAAC,CAACqB,IAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAErB,kBAAC,CAACgB,EAAE,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,6FAA6F,EAAEC,aAAC,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAEb,MAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,mDAAmD,EAAEJ,kBAAC,CAACmB,CAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,+EAA+E,EAAEF,aAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,mCAAmC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,uCAAuC,EAAEA,aAAC,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEjB,kBAAC,CAACqB,IAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,uCAAuC,EAAEJ,aAAC,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEjB,kBAAC,CAACgB,EAAE,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,0FAA0F,EAAEZ,MAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAACL,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAOO,SAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEN,kBAAC,CAACC,CAAC,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAACM,EAAE,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;AACnj2G;AACA,2xBAA2xB,CAAC,CAAC,GAAG,CAAC,gxjBAAgxjB,CAAC,CAAC,EAAE,CAACR,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACuB,GAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,OAAO,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,CAACA,GAAC,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAACA,GAAC,CAAC,CAAC,EAAC,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAACC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAA4D,IAAI,EAAE,CAAC,MAAM,EAAE,CAAClB,qBAAE,EAAE,CAAC,eAAe,EAAE,EAAE,EAAgC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,WAAW,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAC,CAAC,KAAK,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,KAAI,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,EAAC,CAAC,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kCAAkC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kCAAkC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,EAAEkB,GAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAED,GAAC,GAAG,EAAE,EAAE,CAACE,CAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAExB,kBAAC,CAACQ,IAAE,CAAC,mBAAmB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAER,kBAAC,CAACC,CAAC,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAACwB,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEzB,kBAAC,CAACC,CAAC,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAACyB,IAAE,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE1B,kBAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,EAAEI,MAAC,CAACgB,aAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,iEAAiE,EAAEH,aAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,wCAAwC,EAAEf,IAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,WAAW,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAM,CAAC,KAAK,EAAEF,kBAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,mBAAmB,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,WAAW,CAACsB,GAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,EAAEC,GAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC,EAAEvB,kBAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAEA,kBAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,mCAAmC,EAAEE,IAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,qCAAqC,EAAEE,MAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAEJ,kBAAC,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,6JAA6J,CAAC,GAAG,CAAC,k1LAAk1L,CAAC,CAAC,EAAE,CAACD,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,0BAA0B,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,0BAA0B,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEC,kBAAC,CAAC2B,IAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE3B,kBAAC,CAAC4B,EAAE,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,GAAG,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,oCAAoC,EAAE,CAAC,CAAC,CAAC,EAAE5B,kBAAC,CAAC6B,IAAE,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAACC,IAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE9B,kBAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,EAAE,aAAa,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;"}