{"version": 3, "file": "Index43-DW2cAoX9.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Index43.js"], "sourcesContent": ["import{create_ssr_component as F,add_attribute as r,validate_component as a,escape as V,each as J}from\"svelte/internal\";import{createEventDispatcher as N,beforeUpdate as $,onMount as R,afterUpdate as tt}from\"svelte\";import{d as et,h as nt,M as lt,V as At,j as ot,k as at,l as it,e as rt,f as st,b as dt}from\"./FullscreenButton.js\";import{U as Ct}from\"./ModifyUpload.js\";import{I as ut}from\"./Image.js\";import\"./ImagePreview.js\";import{I as ct}from\"./InteractiveAudio.js\";import{S as vt}from\"./StreamingBar.js\";import{default as Kt}from\"./Example20.js\";const ft={code:`.full-container.svelte-5gfv2q.svelte-5gfv2q{width:100%;position:relative;padding:var(--block-padding);border:1px solid transparent}.full-container.dragging.svelte-5gfv2q.svelte-5gfv2q{border:1px solid var(--color-accent);border-radius:calc(var(--radius-sm) - 1px)}.full-container.dragging.svelte-5gfv2q.svelte-5gfv2q::after{content:\"\";position:absolute;top:0;left:0;right:0;bottom:0;pointer-events:none}.input-container.svelte-5gfv2q.svelte-5gfv2q{display:flex;position:relative;align-items:flex-end}textarea.svelte-5gfv2q.svelte-5gfv2q{flex-grow:1;outline:none !important;background:var(--block-background-fill);padding:var(--input-padding);color:var(--body-text-color);font-weight:var(--input-text-weight);font-size:var(--input-text-size);line-height:var(--line-sm);border:none;margin-top:0px;margin-bottom:0px;resize:none;position:relative;z-index:1;text-align:left}textarea[dir=\"rtl\"].svelte-5gfv2q.svelte-5gfv2q{text-align:right}textarea[dir=\"rtl\"].svelte-5gfv2q~.submit-button.svelte-5gfv2q{order:-1;margin-left:0;margin-right:var(--spacing-sm)}textarea[dir=\"rtl\"].svelte-5gfv2q~.submit-button.svelte-5gfv2q svg{transform:scaleX(-1)}textarea.no-label.svelte-5gfv2q.svelte-5gfv2q{padding-top:5px;padding-bottom:5px}textarea.svelte-5gfv2q.svelte-5gfv2q:disabled{-webkit-opacity:1;opacity:1}textarea.svelte-5gfv2q.svelte-5gfv2q::placeholder{color:var(--input-placeholder-color)}.microphone-button.svelte-5gfv2q.svelte-5gfv2q,.upload-button.svelte-5gfv2q.svelte-5gfv2q,.submit-button.svelte-5gfv2q.svelte-5gfv2q,.stop-button.svelte-5gfv2q.svelte-5gfv2q{border:none;text-align:center;text-decoration:none;font-size:14px;cursor:pointer;border-radius:15px;min-width:30px;height:30px;flex-shrink:0;display:flex;justify-content:center;align-items:center;z-index:var(--layer-1);margin-left:var(--spacing-sm)}.padded-button.svelte-5gfv2q.svelte-5gfv2q{padding:0 10px}.microphone-button.svelte-5gfv2q.svelte-5gfv2q,.stop-button.svelte-5gfv2q.svelte-5gfv2q,.upload-button.svelte-5gfv2q.svelte-5gfv2q,.submit-button.svelte-5gfv2q.svelte-5gfv2q{background:var(--button-secondary-background-fill)}.microphone-button.svelte-5gfv2q.svelte-5gfv2q:hover:not(:disabled),.stop-button.svelte-5gfv2q.svelte-5gfv2q:hover:not(:disabled),.upload-button.svelte-5gfv2q.svelte-5gfv2q:hover:not(:disabled),.submit-button.svelte-5gfv2q.svelte-5gfv2q:hover:not(:disabled){background:var(--button-secondary-background-fill-hover)}.microphone-button.svelte-5gfv2q.svelte-5gfv2q:disabled,.stop-button.svelte-5gfv2q.svelte-5gfv2q:disabled,.upload-button.svelte-5gfv2q.svelte-5gfv2q:disabled,.submit-button.svelte-5gfv2q.svelte-5gfv2q:disabled{background:var(--button-secondary-background-fill);cursor:not-allowed}.microphone-button.svelte-5gfv2q.svelte-5gfv2q:active,.stop-button.svelte-5gfv2q.svelte-5gfv2q:active,.upload-button.svelte-5gfv2q.svelte-5gfv2q:active,.submit-button.svelte-5gfv2q.svelte-5gfv2q:active{box-shadow:var(--button-shadow-active)}.submit-button.svelte-5gfv2q svg{height:22px;width:22px}.microphone-button.svelte-5gfv2q svg,.upload-button.svelte-5gfv2q svg{height:17px;width:17px}.stop-button.svelte-5gfv2q svg{height:16px;width:16px}.loader.svelte-5gfv2q.svelte-5gfv2q{display:flex;justify-content:center;align-items:center;--ring-color:transparent;position:relative;border:5px solid #f3f3f3;border-top:5px solid var(--color-accent);border-radius:50%;width:25px;height:25px;animation:svelte-5gfv2q-spin 2s linear infinite}@keyframes svelte-5gfv2q-spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}.thumbnails.svelte-5gfv2q img{width:var(--size-full);height:var(--size-full);object-fit:cover;border-radius:var(--radius-lg)}.thumbnails.svelte-5gfv2q.svelte-5gfv2q{display:flex;align-items:center;gap:var(--spacing-lg);overflow-x:scroll;padding-top:var(--spacing-sm);margin-bottom:6px}.thumbnail-item.svelte-5gfv2q.svelte-5gfv2q{display:flex;justify-content:center;align-items:center;--ring-color:transparent;position:relative;box-shadow:0 0 0 2px var(--ring-color),\n\t\t\tvar(--shadow-drop);border:1px solid var(--border-color-primary);border-radius:var(--radius-lg);background:var(--background-fill-secondary);aspect-ratio:var(--ratio-square);width:var(--size-full);height:var(--size-full);cursor:default}.thumbnail-small.svelte-5gfv2q.svelte-5gfv2q{flex:none;transform:scale(0.9);transition:0.075s;width:var(--size-12);height:var(--size-12)}.thumbnail-item.svelte-5gfv2q svg{width:30px;height:30px}.delete-button.svelte-5gfv2q.svelte-5gfv2q{display:flex;justify-content:center;align-items:center;position:absolute;right:-7px;top:-7px;color:var(--button-secondary-text-color);background:var(--button-secondary-background-fill);border:none;text-align:center;text-decoration:none;font-size:10px;cursor:pointer;border-radius:50%;width:20px;height:20px}.disabled.svelte-5gfv2q.svelte-5gfv2q{display:none}.delete-button.svelte-5gfv2q svg{width:12px;height:12px}.delete-button.svelte-5gfv2q.svelte-5gfv2q:hover{filter:brightness(1.2);border:0.8px solid var(--color-grey-500)}`,map:'{\"version\":3,\"file\":\"MultimodalTextbox.svelte\",\"sources\":[\"MultimodalTextbox.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { onMount, beforeUpdate, afterUpdate, createEventDispatcher, tick } from \\\\\"svelte\\\\\";\\\\nimport { text_area_resize, resize } from \\\\\"../shared/utils\\\\\";\\\\nimport { BlockTitle } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { Upload } from \\\\\"@gradio/upload\\\\\";\\\\nimport { Image } from \\\\\"@gradio/image/shared\\\\\";\\\\nimport { Clear, File, Music, Paperclip, Video, Send, Square, Microphone } from \\\\\"@gradio/icons\\\\\";\\\\nimport InteractiveAudio from \\\\\"../../audio/interactive/InteractiveAudio.svelte\\\\\";\\\\nexport let value = {\\\\n    text: \\\\\"\\\\\",\\\\n    files: []\\\\n};\\\\nexport let value_is_output = false;\\\\nexport let lines = 1;\\\\nexport let i18n;\\\\nexport let placeholder = \\\\\"Type here...\\\\\";\\\\nexport let disabled = false;\\\\nexport let label;\\\\nexport let info = void 0;\\\\nexport let show_label = true;\\\\nexport let max_lines;\\\\nexport let submit_btn = null;\\\\nexport let stop_btn = null;\\\\nexport let rtl = false;\\\\nexport let autofocus = false;\\\\nexport let text_align = void 0;\\\\nexport let autoscroll = true;\\\\nexport let root;\\\\nexport let file_types = null;\\\\nexport let max_file_size = null;\\\\nexport let upload;\\\\nexport let stream_handler;\\\\nexport let file_count = \\\\\"multiple\\\\\";\\\\nexport let max_plain_text_length = 1e3;\\\\nexport let waveform_settings;\\\\nexport let waveform_options = {\\\\n    show_recording_waveform: true\\\\n};\\\\nexport let sources = [\\\\\"upload\\\\\"];\\\\nexport let active_source = null;\\\\nexport let html_attributes = null;\\\\nlet upload_component;\\\\nlet el;\\\\nlet can_scroll;\\\\nlet previous_scroll_top = 0;\\\\nlet user_has_scrolled_up = false;\\\\nexport let dragging = false;\\\\nlet uploading = false;\\\\nlet oldValue = value?.text ?? \\\\\"\\\\\";\\\\nlet recording = false;\\\\n$: dispatch(\\\\\"drag\\\\\", dragging);\\\\nlet mic_audio = null;\\\\nlet full_container;\\\\n$: if (oldValue !== value.text) {\\\\n    dispatch(\\\\\"change\\\\\", value);\\\\n    oldValue = value.text;\\\\n}\\\\n$: if (value === null)\\\\n    value = { text: \\\\\"\\\\\", files: [] };\\\\n$: value, el && lines !== max_lines && resize(el, lines, max_lines);\\\\nconst dispatch = createEventDispatcher();\\\\nbeforeUpdate(() => {\\\\n    can_scroll = el && el.offsetHeight + el.scrollTop > el.scrollHeight - 100;\\\\n});\\\\nconst scroll = () => {\\\\n    if (can_scroll && autoscroll && !user_has_scrolled_up) {\\\\n        el.scrollTo(0, el.scrollHeight);\\\\n    }\\\\n};\\\\nasync function handle_change() {\\\\n    dispatch(\\\\\"change\\\\\", value);\\\\n    if (!value_is_output) {\\\\n        dispatch(\\\\\"input\\\\\");\\\\n    }\\\\n}\\\\nonMount(() => {\\\\n    if (autofocus && el !== null) {\\\\n        el.focus();\\\\n    }\\\\n});\\\\nafterUpdate(() => {\\\\n    if (can_scroll && autoscroll) {\\\\n        scroll();\\\\n    }\\\\n    value_is_output = false;\\\\n});\\\\nfunction handle_select(event) {\\\\n    const target = event.target;\\\\n    const text = target.value;\\\\n    const index = [\\\\n        target.selectionStart,\\\\n        target.selectionEnd\\\\n    ];\\\\n    dispatch(\\\\\"select\\\\\", { value: text.substring(...index), index });\\\\n}\\\\nasync function handle_keypress(e) {\\\\n    await tick();\\\\n    if (e.key === \\\\\"Enter\\\\\" && e.shiftKey && lines > 1) {\\\\n        e.preventDefault();\\\\n        dispatch(\\\\\"submit\\\\\");\\\\n    }\\\\n    else if (e.key === \\\\\"Enter\\\\\" && !e.shiftKey && lines === 1 && max_lines >= 1) {\\\\n        e.preventDefault();\\\\n        dispatch(\\\\\"submit\\\\\");\\\\n        active_source = null;\\\\n        if (mic_audio) {\\\\n            value.files.push(mic_audio);\\\\n            value = value;\\\\n            mic_audio = null;\\\\n        }\\\\n    }\\\\n}\\\\nfunction handle_scroll(event) {\\\\n    const target = event.target;\\\\n    const current_scroll_top = target.scrollTop;\\\\n    if (current_scroll_top < previous_scroll_top) {\\\\n        user_has_scrolled_up = true;\\\\n    }\\\\n    previous_scroll_top = current_scroll_top;\\\\n    const max_scroll_top = target.scrollHeight - target.clientHeight;\\\\n    const user_has_scrolled_to_bottom = current_scroll_top >= max_scroll_top;\\\\n    if (user_has_scrolled_to_bottom) {\\\\n        user_has_scrolled_up = false;\\\\n    }\\\\n}\\\\nasync function handle_upload({ detail }) {\\\\n    handle_change();\\\\n    if (Array.isArray(detail)) {\\\\n        for (let file of detail) {\\\\n            value.files.push(file);\\\\n        }\\\\n        value = value;\\\\n    }\\\\n    else {\\\\n        value.files.push(detail);\\\\n        value = value;\\\\n    }\\\\n    await tick();\\\\n    dispatch(\\\\\"change\\\\\", value);\\\\n    dispatch(\\\\\"upload\\\\\", detail);\\\\n}\\\\nfunction remove_thumbnail(event, index) {\\\\n    handle_change();\\\\n    event.stopPropagation();\\\\n    value.files.splice(index, 1);\\\\n    value = value;\\\\n}\\\\nfunction handle_upload_click() {\\\\n    upload_component.open_upload();\\\\n}\\\\nfunction handle_stop() {\\\\n    dispatch(\\\\\"stop\\\\\");\\\\n}\\\\nfunction handle_submit() {\\\\n    dispatch(\\\\\"submit\\\\\");\\\\n    active_source = null;\\\\n    if (mic_audio) {\\\\n        value.files.push(mic_audio);\\\\n        value = value;\\\\n        mic_audio = null;\\\\n    }\\\\n}\\\\nasync function handle_paste(event) {\\\\n    if (!event.clipboardData)\\\\n        return;\\\\n    const items = event.clipboardData.items;\\\\n    const text = event.clipboardData.getData(\\\\\"text\\\\\");\\\\n    if (text && text.length > max_plain_text_length) {\\\\n        event.preventDefault();\\\\n        const file = new window.File([text], \\\\\"pasted_text.txt\\\\\", {\\\\n            type: \\\\\"text/plain\\\\\",\\\\n            lastModified: Date.now()\\\\n        });\\\\n        if (upload_component) {\\\\n            upload_component.load_files([file]);\\\\n        }\\\\n        return;\\\\n    }\\\\n    for (let index in items) {\\\\n        const item = items[index];\\\\n        if (item.kind === \\\\\"file\\\\\" && item.type.includes(\\\\\"image\\\\\")) {\\\\n            const blob = item.getAsFile();\\\\n            if (blob)\\\\n                upload_component.load_files([blob]);\\\\n        }\\\\n    }\\\\n}\\\\nfunction handle_dragenter(event) {\\\\n    event.preventDefault();\\\\n    dragging = true;\\\\n}\\\\nfunction handle_dragleave(event) {\\\\n    event.preventDefault();\\\\n    const rect = full_container.getBoundingClientRect();\\\\n    const { clientX, clientY } = event;\\\\n    if (clientX <= rect.left || clientX >= rect.right || clientY <= rect.top || clientY >= rect.bottom) {\\\\n        dragging = false;\\\\n    }\\\\n}\\\\nfunction handle_drop(event) {\\\\n    event.preventDefault();\\\\n    dragging = false;\\\\n    if (event.dataTransfer && event.dataTransfer.files) {\\\\n        const files = Array.from(event.dataTransfer.files);\\\\n        if (file_types) {\\\\n            const valid_files = files.filter((file) => {\\\\n                return file_types.some((type) => {\\\\n                    if (type.startsWith(\\\\\".\\\\\")) {\\\\n                        return file.name.toLowerCase().endsWith(type.toLowerCase());\\\\n                    }\\\\n                    return file.type.match(new RegExp(type.replace(\\\\\"*\\\\\", \\\\\".*\\\\\")));\\\\n                });\\\\n            });\\\\n            const invalid_files = files.length - valid_files.length;\\\\n            if (invalid_files > 0) {\\\\n                dispatch(\\\\\"error\\\\\", `${invalid_files} file(s) were rejected. Accepted formats: ${file_types.join(\\\\\", \\\\\")}`);\\\\n            }\\\\n            if (valid_files.length > 0) {\\\\n                upload_component.load_files(valid_files);\\\\n            }\\\\n        }\\\\n        else {\\\\n            upload_component.load_files(files);\\\\n        }\\\\n    }\\\\n}\\\\n<\\/script>\\\\n\\\\n<div\\\\n\\\\tclass=\\\\\"full-container\\\\\"\\\\n\\\\tclass:dragging\\\\n\\\\tbind:this={full_container}\\\\n\\\\ton:dragenter={handle_dragenter}\\\\n\\\\ton:dragleave={handle_dragleave}\\\\n\\\\ton:dragover|preventDefault\\\\n\\\\ton:drop={handle_drop}\\\\n\\\\trole=\\\\\"group\\\\\"\\\\n\\\\taria-label=\\\\\"Multimedia input field\\\\\"\\\\n>\\\\n\\\\t<BlockTitle {show_label} {info} {rtl}>{label}</BlockTitle>\\\\n\\\\t{#if value.files.length > 0 || uploading}\\\\n\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\tclass=\\\\\"thumbnails scroll-hide\\\\\"\\\\n\\\\t\\\\t\\\\taria-label=\\\\\"Uploaded files\\\\\"\\\\n\\\\t\\\\t\\\\tdata-testid=\\\\\"container_el\\\\\"\\\\n\\\\t\\\\t\\\\tstyle=\\\\\"display: {value.files.length > 0 || uploading ? \\'flex\\' : \\'none\\'};\\\\\"\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t{#each value.files as file, index}\\\\n\\\\t\\\\t\\\\t\\\\t<span role=\\\\\"listitem\\\\\" aria-label=\\\\\"File thumbnail\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<button class=\\\\\"thumbnail-item thumbnail-small\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:disabled\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"delete-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={(event) => remove_thumbnail(event, index)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t><Clear /></button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if file.mime_type && file.mime_type.includes(\\\\\"image\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Image\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={file.url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttitle={null}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\talt=\\\\\"\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tloading=\\\\\"lazy\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass={\\\\\"thumbnail-image\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else if file.mime_type && file.mime_type.includes(\\\\\"audio\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Music />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else if file.mime_type && file.mime_type.includes(\\\\\"video\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Video />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<File />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t{#if uploading}\\\\n\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"loader\\\\\" role=\\\\\"status\\\\\" aria-label=\\\\\"Uploading\\\\\"></div>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n\\\\t{#if sources && sources.includes(\\\\\"microphone\\\\\") && active_source === \\\\\"microphone\\\\\"}\\\\n\\\\t\\\\t<InteractiveAudio\\\\n\\\\t\\\\t\\\\ton:change={({ detail }) => {\\\\n\\\\t\\\\t\\\\t\\\\tif (detail !== null) {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tmic_audio = detail;\\\\n\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\ton:clear={() => {\\\\n\\\\t\\\\t\\\\t\\\\tactive_source = null;\\\\n\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\ton:start_recording={() => dispatch(\\\\\"start_recording\\\\\")}\\\\n\\\\t\\\\t\\\\ton:pause_recording={() => dispatch(\\\\\"pause_recording\\\\\")}\\\\n\\\\t\\\\t\\\\ton:stop_recording={() => dispatch(\\\\\"stop_recording\\\\\")}\\\\n\\\\t\\\\t\\\\tsources={[\\\\\"microphone\\\\\"]}\\\\n\\\\t\\\\t\\\\tclass_name=\\\\\"compact-audio\\\\\"\\\\n\\\\t\\\\t\\\\t{recording}\\\\n\\\\t\\\\t\\\\t{waveform_settings}\\\\n\\\\t\\\\t\\\\t{waveform_options}\\\\n\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t{active_source}\\\\n\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t{stream_handler}\\\\n\\\\t\\\\t\\\\tstream_every={1}\\\\n\\\\t\\\\t\\\\teditable={true}\\\\n\\\\t\\\\t\\\\t{label}\\\\n\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\tloop={false}\\\\n\\\\t\\\\t\\\\tshow_label={false}\\\\n\\\\t\\\\t\\\\tshow_download_button={false}\\\\n\\\\t\\\\t\\\\tdragging={false}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n\\\\t<div class=\\\\\"input-container\\\\\">\\\\n\\\\t\\\\t{#if sources && sources.includes(\\\\\"upload\\\\\") && !(file_count === \\\\\"single\\\\\" && value.files.length > 0)}\\\\n\\\\t\\\\t\\\\t<Upload\\\\n\\\\t\\\\t\\\\t\\\\tbind:this={upload_component}\\\\n\\\\t\\\\t\\\\t\\\\ton:load={handle_upload}\\\\n\\\\t\\\\t\\\\t\\\\t{file_count}\\\\n\\\\t\\\\t\\\\t\\\\tfiletype={file_types}\\\\n\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\t{max_file_size}\\\\n\\\\t\\\\t\\\\t\\\\tbind:dragging\\\\n\\\\t\\\\t\\\\t\\\\tbind:uploading\\\\n\\\\t\\\\t\\\\t\\\\tshow_progress={false}\\\\n\\\\t\\\\t\\\\t\\\\tdisable_click={true}\\\\n\\\\t\\\\t\\\\t\\\\ton:error\\\\n\\\\t\\\\t\\\\t\\\\thidden={true}\\\\n\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\t{stream_handler}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\tdata-testid=\\\\\"upload-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"upload-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t{disabled}\\\\n\\\\t\\\\t\\\\t\\\\ton:click={disabled ? undefined : handle_upload_click}\\\\n\\\\t\\\\t\\\\t\\\\t><Paperclip /></button\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t\\\\t{#if sources && sources.includes(\\\\\"microphone\\\\\")}\\\\n\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\tdata-testid=\\\\\"microphone-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"microphone-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass:recording\\\\n\\\\t\\\\t\\\\t\\\\t{disabled}\\\\n\\\\t\\\\t\\\\t\\\\ton:click={disabled\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t? undefined\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t: () => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tactive_source =\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tactive_source !== \\\\\"microphone\\\\\" ? \\\\\"microphone\\\\\" : null;\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<Microphone />\\\\n\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t\\\\t<!-- svelte-ignore a11y-autofocus -->\\\\n\\\\t\\\\t<textarea\\\\n\\\\t\\\\t\\\\tdata-testid=\\\\\"textbox\\\\\"\\\\n\\\\t\\\\t\\\\tuse:text_area_resize={{\\\\n\\\\t\\\\t\\\\t\\\\ttext: value.text,\\\\n\\\\t\\\\t\\\\t\\\\tlines: lines,\\\\n\\\\t\\\\t\\\\t\\\\tmax_lines: max_lines\\\\n\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\tclass=\\\\\"scroll-hide\\\\\"\\\\n\\\\t\\\\t\\\\tclass:no-label={!show_label}\\\\n\\\\t\\\\t\\\\tdir={rtl ? \\\\\"rtl\\\\\" : \\\\\"ltr\\\\\"}\\\\n\\\\t\\\\t\\\\tbind:value={value.text}\\\\n\\\\t\\\\t\\\\tbind:this={el}\\\\n\\\\t\\\\t\\\\t{placeholder}\\\\n\\\\t\\\\t\\\\trows={lines}\\\\n\\\\t\\\\t\\\\t{disabled}\\\\n\\\\t\\\\t\\\\t{autofocus}\\\\n\\\\t\\\\t\\\\ton:keypress={handle_keypress}\\\\n\\\\t\\\\t\\\\ton:blur\\\\n\\\\t\\\\t\\\\ton:select={handle_select}\\\\n\\\\t\\\\t\\\\ton:focus\\\\n\\\\t\\\\t\\\\ton:scroll={handle_scroll}\\\\n\\\\t\\\\t\\\\ton:paste={handle_paste}\\\\n\\\\t\\\\t\\\\tstyle={text_align ? \\\\\"text-align: \\\\\" + text_align : \\\\\"\\\\\"}\\\\n\\\\t\\\\t\\\\tautocapitalize={html_attributes?.autocapitalize}\\\\n\\\\t\\\\t\\\\tautocorrect={html_attributes?.autocorrect}\\\\n\\\\t\\\\t\\\\tspellcheck={html_attributes?.spellcheck}\\\\n\\\\t\\\\t\\\\tautocomplete={html_attributes?.autocomplete}\\\\n\\\\t\\\\t\\\\ttabindex={html_attributes?.tabindex}\\\\n\\\\t\\\\t\\\\tenterkeyhint={html_attributes?.enterkeyhint}\\\\n\\\\t\\\\t\\\\tlang={html_attributes?.lang}\\\\n\\\\t\\\\t/>\\\\n\\\\t\\\\t{#if submit_btn}\\\\n\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"submit-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass:padded-button={submit_btn !== true}\\\\n\\\\t\\\\t\\\\t\\\\t{disabled}\\\\n\\\\t\\\\t\\\\t\\\\ton:click={disabled ? undefined : handle_submit}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t{#if submit_btn === true}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<Send />\\\\n\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{submit_btn}\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t\\\\t{#if stop_btn}\\\\n\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"stop-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass:padded-button={stop_btn !== true}\\\\n\\\\t\\\\t\\\\t\\\\ton:click={handle_stop}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t{#if stop_btn === true}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<Square fill={\\\\\"none\\\\\"} stroke_width={2.5} />\\\\n\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{stop_btn}\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t</div>\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.full-container {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tpadding: var(--block-padding);\\\\n\\\\t\\\\tborder: 1px solid transparent;\\\\n\\\\t}\\\\n\\\\n\\\\t.full-container.dragging {\\\\n\\\\t\\\\tborder: 1px solid var(--color-accent);\\\\n\\\\t\\\\tborder-radius: calc(var(--radius-sm) - 1px);\\\\n\\\\t}\\\\n\\\\n\\\\t.full-container.dragging::after {\\\\n\\\\t\\\\tcontent: \\\\\"\\\\\";\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\ttop: 0;\\\\n\\\\t\\\\tleft: 0;\\\\n\\\\t\\\\tright: 0;\\\\n\\\\t\\\\tbottom: 0;\\\\n\\\\t\\\\tpointer-events: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.input-container {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\talign-items: flex-end;\\\\n\\\\t}\\\\n\\\\n\\\\ttextarea {\\\\n\\\\t\\\\tflex-grow: 1;\\\\n\\\\t\\\\toutline: none !important;\\\\n\\\\t\\\\tbackground: var(--block-background-fill);\\\\n\\\\t\\\\tpadding: var(--input-padding);\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\tfont-weight: var(--input-text-weight);\\\\n\\\\t\\\\tfont-size: var(--input-text-size);\\\\n\\\\t\\\\tline-height: var(--line-sm);\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tmargin-top: 0px;\\\\n\\\\t\\\\tmargin-bottom: 0px;\\\\n\\\\t\\\\tresize: none;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tz-index: 1;\\\\n\\\\t\\\\ttext-align: left;\\\\n\\\\t}\\\\n\\\\ttextarea[dir=\\\\\"rtl\\\\\"] {\\\\n\\\\t\\\\ttext-align: right;\\\\n\\\\t}\\\\n\\\\n\\\\ttextarea[dir=\\\\\"rtl\\\\\"] ~ .submit-button {\\\\n\\\\t\\\\torder: -1;\\\\n\\\\t\\\\tmargin-left: 0;\\\\n\\\\t\\\\tmargin-right: var(--spacing-sm);\\\\n\\\\t}\\\\n\\\\n\\\\ttextarea[dir=\\\\\"rtl\\\\\"] ~ .submit-button :global(svg) {\\\\n\\\\t\\\\ttransform: scaleX(-1);\\\\n\\\\t}\\\\n\\\\n\\\\ttextarea.no-label {\\\\n\\\\t\\\\tpadding-top: 5px;\\\\n\\\\t\\\\tpadding-bottom: 5px;\\\\n\\\\t}\\\\n\\\\n\\\\ttextarea:disabled {\\\\n\\\\t\\\\t-webkit-opacity: 1;\\\\n\\\\t\\\\topacity: 1;\\\\n\\\\t}\\\\n\\\\n\\\\ttextarea::placeholder {\\\\n\\\\t\\\\tcolor: var(--input-placeholder-color);\\\\n\\\\t}\\\\n\\\\n\\\\t.microphone-button,\\\\n\\\\t.upload-button,\\\\n\\\\t.submit-button,\\\\n\\\\t.stop-button {\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\ttext-align: center;\\\\n\\\\t\\\\ttext-decoration: none;\\\\n\\\\t\\\\tfont-size: 14px;\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\tborder-radius: 15px;\\\\n\\\\t\\\\tmin-width: 30px;\\\\n\\\\t\\\\theight: 30px;\\\\n\\\\t\\\\tflex-shrink: 0;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tz-index: var(--layer-1);\\\\n\\\\t\\\\tmargin-left: var(--spacing-sm);\\\\n\\\\t}\\\\n\\\\t.padded-button {\\\\n\\\\t\\\\tpadding: 0 10px;\\\\n\\\\t}\\\\n\\\\n\\\\t.microphone-button,\\\\n\\\\t.stop-button,\\\\n\\\\t.upload-button,\\\\n\\\\t.submit-button {\\\\n\\\\t\\\\tbackground: var(--button-secondary-background-fill);\\\\n\\\\t}\\\\n\\\\n\\\\t.microphone-button:hover:not(:disabled),\\\\n\\\\t.stop-button:hover:not(:disabled),\\\\n\\\\t.upload-button:hover:not(:disabled),\\\\n\\\\t.submit-button:hover:not(:disabled) {\\\\n\\\\t\\\\tbackground: var(--button-secondary-background-fill-hover);\\\\n\\\\t}\\\\n\\\\n\\\\t.microphone-button:disabled,\\\\n\\\\t.stop-button:disabled,\\\\n\\\\t.upload-button:disabled,\\\\n\\\\t.submit-button:disabled {\\\\n\\\\t\\\\tbackground: var(--button-secondary-background-fill);\\\\n\\\\t\\\\tcursor: not-allowed;\\\\n\\\\t}\\\\n\\\\t.microphone-button:active,\\\\n\\\\t.stop-button:active,\\\\n\\\\t.upload-button:active,\\\\n\\\\t.submit-button:active {\\\\n\\\\t\\\\tbox-shadow: var(--button-shadow-active);\\\\n\\\\t}\\\\n\\\\n\\\\t.submit-button :global(svg) {\\\\n\\\\t\\\\theight: 22px;\\\\n\\\\t\\\\twidth: 22px;\\\\n\\\\t}\\\\n\\\\t.microphone-button :global(svg),\\\\n\\\\t.upload-button :global(svg) {\\\\n\\\\t\\\\theight: 17px;\\\\n\\\\t\\\\twidth: 17px;\\\\n\\\\t}\\\\n\\\\n\\\\t.stop-button :global(svg) {\\\\n\\\\t\\\\theight: 16px;\\\\n\\\\t\\\\twidth: 16px;\\\\n\\\\t}\\\\n\\\\n\\\\t.loader {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\t--ring-color: transparent;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tborder: 5px solid #f3f3f3;\\\\n\\\\t\\\\tborder-top: 5px solid var(--color-accent);\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\twidth: 25px;\\\\n\\\\t\\\\theight: 25px;\\\\n\\\\t\\\\tanimation: spin 2s linear infinite;\\\\n\\\\t}\\\\n\\\\n\\\\t@keyframes spin {\\\\n\\\\t\\\\t0% {\\\\n\\\\t\\\\t\\\\ttransform: rotate(0deg);\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t100% {\\\\n\\\\t\\\\t\\\\ttransform: rotate(360deg);\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.thumbnails :global(img) {\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t}\\\\n\\\\n\\\\t.thumbnails {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tgap: var(--spacing-lg);\\\\n\\\\t\\\\toverflow-x: scroll;\\\\n\\\\t\\\\tpadding-top: var(--spacing-sm);\\\\n\\\\t\\\\tmargin-bottom: 6px;\\\\n\\\\t}\\\\n\\\\n\\\\t.thumbnail-item {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\t--ring-color: transparent;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tbox-shadow:\\\\n\\\\t\\\\t\\\\t0 0 0 2px var(--ring-color),\\\\n\\\\t\\\\t\\\\tvar(--shadow-drop);\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t\\\\tbackground: var(--background-fill-secondary);\\\\n\\\\t\\\\taspect-ratio: var(--ratio-square);\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tcursor: default;\\\\n\\\\t}\\\\n\\\\n\\\\t.thumbnail-small {\\\\n\\\\t\\\\tflex: none;\\\\n\\\\t\\\\ttransform: scale(0.9);\\\\n\\\\t\\\\ttransition: 0.075s;\\\\n\\\\t\\\\twidth: var(--size-12);\\\\n\\\\t\\\\theight: var(--size-12);\\\\n\\\\t}\\\\n\\\\n\\\\t.thumbnail-item :global(svg) {\\\\n\\\\t\\\\twidth: 30px;\\\\n\\\\t\\\\theight: 30px;\\\\n\\\\t}\\\\n\\\\n\\\\t.delete-button {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tright: -7px;\\\\n\\\\t\\\\ttop: -7px;\\\\n\\\\t\\\\tcolor: var(--button-secondary-text-color);\\\\n\\\\t\\\\tbackground: var(--button-secondary-background-fill);\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\ttext-align: center;\\\\n\\\\t\\\\ttext-decoration: none;\\\\n\\\\t\\\\tfont-size: 10px;\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\twidth: 20px;\\\\n\\\\t\\\\theight: 20px;\\\\n\\\\t}\\\\n\\\\n\\\\t.disabled {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.delete-button :global(svg) {\\\\n\\\\t\\\\twidth: 12px;\\\\n\\\\t\\\\theight: 12px;\\\\n\\\\t}\\\\n\\\\n\\\\t.delete-button:hover {\\\\n\\\\t\\\\tfilter: brightness(1.2);\\\\n\\\\t\\\\tborder: 0.8px solid var(--color-grey-500);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA+ZC,2CAAgB,CACf,KAAK,CAAE,IAAI,CACX,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,eAAe,CAAC,CAC7B,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,WACnB,CAEA,eAAe,qCAAU,CACxB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,cAAc,CAAC,CACrC,aAAa,CAAE,KAAK,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAC3C,CAEA,eAAe,qCAAS,OAAQ,CAC/B,OAAO,CAAE,EAAE,CACX,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,CAAC,CACN,IAAI,CAAE,CAAC,CACP,KAAK,CAAE,CAAC,CACR,MAAM,CAAE,CAAC,CACT,cAAc,CAAE,IACjB,CAEA,4CAAiB,CAChB,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,WAAW,CAAE,QACd,CAEA,oCAAS,CACR,SAAS,CAAE,CAAC,CACZ,OAAO,CAAE,IAAI,CAAC,UAAU,CACxB,UAAU,CAAE,IAAI,uBAAuB,CAAC,CACxC,OAAO,CAAE,IAAI,eAAe,CAAC,CAC7B,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,WAAW,CAAE,IAAI,mBAAmB,CAAC,CACrC,SAAS,CAAE,IAAI,iBAAiB,CAAC,CACjC,WAAW,CAAE,IAAI,SAAS,CAAC,CAC3B,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,GAAG,CACf,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,IAAI,CACZ,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,CAAC,CACV,UAAU,CAAE,IACb,CACA,QAAQ,CAAC,GAAG,CAAC,KAAK,6BAAE,CACnB,UAAU,CAAE,KACb,CAEA,QAAQ,CAAC,GAAG,CAAC,KAAK,eAAC,CAAG,4BAAe,CACpC,KAAK,CAAE,EAAE,CACT,WAAW,CAAE,CAAC,CACd,YAAY,CAAE,IAAI,YAAY,CAC/B,CAEA,QAAQ,CAAC,GAAG,CAAC,KAAK,eAAC,CAAG,4BAAc,CAAS,GAAK,CACjD,SAAS,CAAE,OAAO,EAAE,CACrB,CAEA,QAAQ,qCAAU,CACjB,WAAW,CAAE,GAAG,CAChB,cAAc,CAAE,GACjB,CAEA,oCAAQ,SAAU,CACjB,eAAe,CAAE,CAAC,CAClB,OAAO,CAAE,CACV,CAEA,oCAAQ,aAAc,CACrB,KAAK,CAAE,IAAI,yBAAyB,CACrC,CAEA,8CAAkB,CAClB,0CAAc,CACd,0CAAc,CACd,wCAAa,CACZ,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,MAAM,CAClB,eAAe,CAAE,IAAI,CACrB,SAAS,CAAE,IAAI,CACf,MAAM,CAAE,OAAO,CACf,aAAa,CAAE,IAAI,CACnB,SAAS,CAAE,IAAI,CACf,MAAM,CAAE,IAAI,CACZ,WAAW,CAAE,CAAC,CACd,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,OAAO,CAAE,IAAI,SAAS,CAAC,CACvB,WAAW,CAAE,IAAI,YAAY,CAC9B,CACA,0CAAe,CACd,OAAO,CAAE,CAAC,CAAC,IACZ,CAEA,8CAAkB,CAClB,wCAAY,CACZ,0CAAc,CACd,0CAAe,CACd,UAAU,CAAE,IAAI,kCAAkC,CACnD,CAEA,8CAAkB,MAAM,KAAK,SAAS,CAAC,CACvC,wCAAY,MAAM,KAAK,SAAS,CAAC,CACjC,0CAAc,MAAM,KAAK,SAAS,CAAC,CACnC,0CAAc,MAAM,KAAK,SAAS,CAAE,CACnC,UAAU,CAAE,IAAI,wCAAwC,CACzD,CAEA,8CAAkB,SAAS,CAC3B,wCAAY,SAAS,CACrB,0CAAc,SAAS,CACvB,0CAAc,SAAU,CACvB,UAAU,CAAE,IAAI,kCAAkC,CAAC,CACnD,MAAM,CAAE,WACT,CACA,8CAAkB,OAAO,CACzB,wCAAY,OAAO,CACnB,0CAAc,OAAO,CACrB,0CAAc,OAAQ,CACrB,UAAU,CAAE,IAAI,sBAAsB,CACvC,CAEA,4BAAc,CAAS,GAAK,CAC3B,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IACR,CACA,gCAAkB,CAAS,GAAI,CAC/B,4BAAc,CAAS,GAAK,CAC3B,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IACR,CAEA,0BAAY,CAAS,GAAK,CACzB,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IACR,CAEA,mCAAQ,CACP,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,YAAY,CAAE,WAAW,CACzB,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,UAAU,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,cAAc,CAAC,CACzC,aAAa,CAAE,GAAG,CAClB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,SAAS,CAAE,kBAAI,CAAC,EAAE,CAAC,MAAM,CAAC,QAC3B,CAEA,WAAW,kBAAK,CACf,EAAG,CACF,SAAS,CAAE,OAAO,IAAI,CACvB,CACA,IAAK,CACJ,SAAS,CAAE,OAAO,MAAM,CACzB,CACD,CAEA,yBAAW,CAAS,GAAK,CACxB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,UAAU,CAAE,KAAK,CACjB,aAAa,CAAE,IAAI,WAAW,CAC/B,CAEA,uCAAY,CACX,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,GAAG,CAAE,IAAI,YAAY,CAAC,CACtB,UAAU,CAAE,MAAM,CAClB,WAAW,CAAE,IAAI,YAAY,CAAC,CAC9B,aAAa,CAAE,GAChB,CAEA,2CAAgB,CACf,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,YAAY,CAAE,WAAW,CACzB,QAAQ,CAAE,QAAQ,CAClB,UAAU,CACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,CAAC;AAC/B,GAAG,IAAI,aAAa,CAAC,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,UAAU,CAAE,IAAI,2BAA2B,CAAC,CAC5C,YAAY,CAAE,IAAI,cAAc,CAAC,CACjC,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,MAAM,CAAE,OACT,CAEA,4CAAiB,CAChB,IAAI,CAAE,IAAI,CACV,SAAS,CAAE,MAAM,GAAG,CAAC,CACrB,UAAU,CAAE,MAAM,CAClB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,MAAM,CAAE,IAAI,SAAS,CACtB,CAEA,6BAAe,CAAS,GAAK,CAC5B,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CAEA,0CAAe,CACd,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,QAAQ,CAAE,QAAQ,CAClB,KAAK,CAAE,IAAI,CACX,GAAG,CAAE,IAAI,CACT,KAAK,CAAE,IAAI,6BAA6B,CAAC,CACzC,UAAU,CAAE,IAAI,kCAAkC,CAAC,CACnD,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,MAAM,CAClB,eAAe,CAAE,IAAI,CACrB,SAAS,CAAE,IAAI,CACf,MAAM,CAAE,OAAO,CACf,aAAa,CAAE,GAAG,CAClB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CAEA,qCAAU,CACT,OAAO,CAAE,IACV,CAEA,4BAAc,CAAS,GAAK,CAC3B,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CAEA,0CAAc,MAAO,CACpB,MAAM,CAAE,WAAW,GAAG,CAAC,CACvB,MAAM,CAAE,KAAK,CAAC,KAAK,CAAC,IAAI,gBAAgB,CACzC\"}'};let mt=!1;const _t=F((l,t,e,H)=>{let{value:n={text:\"\",files:[]}}=t,{value_is_output:k=!1}=t,{lines:K=1}=t,{i18n:S}=t,{placeholder:_=\"Type here...\"}=t,{disabled:d=!1}=t,{label:f}=t,{info:z=void 0}=t,{show_label:g=!0}=t,{max_lines:Q}=t,{submit_btn:u=null}=t,{stop_btn:C=null}=t,{rtl:h=!1}=t,{autofocus:p=!1}=t,{text_align:b=void 0}=t,{autoscroll:x=!0}=t,{root:m}=t,{file_types:E=null}=t,{max_file_size:G=null}=t,{upload:I}=t,{stream_handler:B}=t,{file_count:y=\"multiple\"}=t,{max_plain_text_length:T=1e3}=t,{waveform_settings:U}=t,{waveform_options:W={show_recording_waveform:!0}}=t,{sources:s=[\"upload\"]}=t,{active_source:w=null}=t,{html_attributes:A=null}=t,j,c,Y,M=!1,{dragging:v=!1}=t,O=!1,Z=n?.text??\"\",q;const D=N();$(()=>{Y=c});const X=()=>{Y&&x&&!M&&c.scrollTo(0,c.scrollHeight)};R(()=>{p&&c!==null&&c.focus()}),tt(()=>{Y&&x&&X(),k=!1}),t.value===void 0&&e.value&&n!==void 0&&e.value(n),t.value_is_output===void 0&&e.value_is_output&&k!==void 0&&e.value_is_output(k),t.lines===void 0&&e.lines&&K!==void 0&&e.lines(K),t.i18n===void 0&&e.i18n&&S!==void 0&&e.i18n(S),t.placeholder===void 0&&e.placeholder&&_!==void 0&&e.placeholder(_),t.disabled===void 0&&e.disabled&&d!==void 0&&e.disabled(d),t.label===void 0&&e.label&&f!==void 0&&e.label(f),t.info===void 0&&e.info&&z!==void 0&&e.info(z),t.show_label===void 0&&e.show_label&&g!==void 0&&e.show_label(g),t.max_lines===void 0&&e.max_lines&&Q!==void 0&&e.max_lines(Q),t.submit_btn===void 0&&e.submit_btn&&u!==void 0&&e.submit_btn(u),t.stop_btn===void 0&&e.stop_btn&&C!==void 0&&e.stop_btn(C),t.rtl===void 0&&e.rtl&&h!==void 0&&e.rtl(h),t.autofocus===void 0&&e.autofocus&&p!==void 0&&e.autofocus(p),t.text_align===void 0&&e.text_align&&b!==void 0&&e.text_align(b),t.autoscroll===void 0&&e.autoscroll&&x!==void 0&&e.autoscroll(x),t.root===void 0&&e.root&&m!==void 0&&e.root(m),t.file_types===void 0&&e.file_types&&E!==void 0&&e.file_types(E),t.max_file_size===void 0&&e.max_file_size&&G!==void 0&&e.max_file_size(G),t.upload===void 0&&e.upload&&I!==void 0&&e.upload(I),t.stream_handler===void 0&&e.stream_handler&&B!==void 0&&e.stream_handler(B),t.file_count===void 0&&e.file_count&&y!==void 0&&e.file_count(y),t.max_plain_text_length===void 0&&e.max_plain_text_length&&T!==void 0&&e.max_plain_text_length(T),t.waveform_settings===void 0&&e.waveform_settings&&U!==void 0&&e.waveform_settings(U),t.waveform_options===void 0&&e.waveform_options&&W!==void 0&&e.waveform_options(W),t.sources===void 0&&e.sources&&s!==void 0&&e.sources(s),t.active_source===void 0&&e.active_source&&w!==void 0&&e.active_source(w),t.html_attributes===void 0&&e.html_attributes&&A!==void 0&&e.html_attributes(A),t.dragging===void 0&&e.dragging&&v!==void 0&&e.dragging(v),l.css.add(ft);let o,P,L=l.head;do o=!0,l.head=L,D(\"drag\",v),n===null&&(n={text:\"\",files:[]}),Z!==n.text&&(D(\"change\",n),Z=n.text),P=`<div class=\"${[\"full-container svelte-5gfv2q\",v?\"dragging\":\"\"].join(\" \").trim()}\" role=\"group\" aria-label=\"Multimedia input field\"${r(\"this\",q,0)}>${a(et,\"BlockTitle\").$$render(l,{show_label:g,info:z,rtl:h},{},{default:()=>`${V(f)}`})} ${n.files.length>0||O?`<div class=\"thumbnails scroll-hide svelte-5gfv2q\" aria-label=\"Uploaded files\" data-testid=\"container_el\" style=\"${\"display: \"+V(n.files.length>0||O?\"flex\":\"none\",!0)+\";\"}\">${J(n.files,(i,ht)=>`<span role=\"listitem\" aria-label=\"File thumbnail\"><button class=\"thumbnail-item thumbnail-small svelte-5gfv2q\"><button class=\"${[\"delete-button svelte-5gfv2q\",d?\"disabled\":\"\"].join(\" \").trim()}\">${a(nt,\"Clear\").$$render(l,{},{},{})}</button> ${i.mime_type&&i.mime_type.includes(\"image\")?`${a(ut,\"Image\").$$render(l,{src:i.url,title:null,alt:\"\",loading:\"lazy\",class:\"thumbnail-image\"},{},{})}`:`${i.mime_type&&i.mime_type.includes(\"audio\")?`${a(lt,\"Music\").$$render(l,{},{},{})}`:`${i.mime_type&&i.mime_type.includes(\"video\")?`${a(At,\"Video\").$$render(l,{},{},{})}`:`${a(ot,\"File\").$$render(l,{},{},{})}`}`}`}</button> </span>`)} ${O?'<div class=\"loader svelte-5gfv2q\" role=\"status\" aria-label=\"Uploading\"></div>':\"\"}</div>`:\"\"} ${s&&s.includes(\"microphone\")&&w===\"microphone\"?`${a(ct,\"InteractiveAudio\").$$render(l,{sources:[\"microphone\"],class_name:\"compact-audio\",recording:mt,waveform_settings:U,waveform_options:W,i18n:S,active_source:w,upload:I,stream_handler:B,stream_every:1,editable:!0,label:f,root:m,loop:!1,show_label:!1,show_download_button:!1,dragging:!1},{},{})}`:\"\"} <div class=\"input-container svelte-5gfv2q\">${s&&s.includes(\"upload\")&&!(y===\"single\"&&n.files.length>0)?`${a(Ct,\"Upload\").$$render(l,{file_count:y,filetype:E,root:m,max_file_size:G,show_progress:!1,disable_click:!0,hidden:!0,upload:I,stream_handler:B,this:j,dragging:v,uploading:O},{this:i=>{j=i,o=!1},dragging:i=>{v=i,o=!1},uploading:i=>{O=i,o=!1}},{})} <button data-testid=\"upload-button\" class=\"upload-button svelte-5gfv2q\" ${d?\"disabled\":\"\"}>${a(at,\"Paperclip\").$$render(l,{},{},{})}</button>`:\"\"} ${s&&s.includes(\"microphone\")?`<button data-testid=\"microphone-button\" class=\"${[\"microphone-button svelte-5gfv2q\",\"\"].join(\" \").trim()}\" ${d?\"disabled\":\"\"}>${a(it,\"Microphone\").$$render(l,{},{},{})}</button>`:\"\"}  <textarea data-testid=\"textbox\" class=\"${[\"scroll-hide svelte-5gfv2q\",g?\"\":\"no-label\"].join(\" \").trim()}\"${r(\"dir\",h?\"rtl\":\"ltr\",0)}${r(\"placeholder\",_,0)}${r(\"rows\",K,0)} ${d?\"disabled\":\"\"} ${p?\"autofocus\":\"\"}${r(\"style\",b?\"text-align: \"+b:\"\",0)}${r(\"autocapitalize\",A?.autocapitalize,0)}${r(\"autocorrect\",A?.autocorrect,0)}${r(\"spellcheck\",A?.spellcheck,0)}${r(\"autocomplete\",A?.autocomplete,0)}${r(\"tabindex\",A?.tabindex,0)}${r(\"enterkeyhint\",A?.enterkeyhint,0)}${r(\"lang\",A?.lang,0)}${r(\"this\",c,0)}>${V(n.text||\"\")}</textarea> ${u?`<button class=\"${[\"submit-button svelte-5gfv2q\",u!==!0?\"padded-button\":\"\"].join(\" \").trim()}\" ${d?\"disabled\":\"\"}>${u===!0?`${a(rt,\"Send\").$$render(l,{},{},{})}`:`${V(u)}`}</button>`:\"\"} ${C?`<button class=\"${[\"stop-button svelte-5gfv2q\",C!==!0?\"padded-button\":\"\"].join(\" \").trim()}\">${C===!0?`${a(st,\"Square\").$$render(l,{fill:\"none\",stroke_width:2.5},{},{})}`:`${V(C)}`}</button>`:\"\"}</div> </div>`;while(!o);return P}),gt=_t,Mt=F((l,t,e,H)=>{let{gradio:n}=t,{elem_id:k=\"\"}=t,{elem_classes:K=[]}=t,{visible:S=!0}=t,{value:_={text:\"\",files:[]}}=t,{file_types:d=null}=t,{lines:f}=t,{placeholder:z=\"\"}=t,{label:g=\"MultimodalTextbox\"}=t,{info:Q=void 0}=t,{show_label:u}=t,{max_lines:C}=t,{scale:h=null}=t,{min_width:p=void 0}=t,{submit_btn:b=null}=t,{stop_btn:x=null}=t,{loading_status:m=void 0}=t,{value_is_output:E=!1}=t,{rtl:G=!1}=t,{text_align:I=void 0}=t,{autofocus:B=!1}=t,{autoscroll:y=!0}=t,{interactive:T}=t,{root:U}=t,{file_count:W}=t,{max_plain_text_length:s}=t,{sources:w=[\"upload\"]}=t,{waveform_options:A={}}=t,{html_attributes:j=null}=t,c,Y=null,M,v=\"darkorange\";R(()=>{v=getComputedStyle(document?.documentElement).getPropertyValue(\"--color-accent\"),Z(),M.waveColor=A.waveform_color||\"#9ca3af\",M.progressColor=A.waveform_progress_color||v,M.mediaControls=A.show_controls,M.sampleRate=A.sample_rate||44100});const O={color:A.trim_region_color,drag:!0,resize:!0};function Z(){document.documentElement.style.setProperty(\"--trim-region-color\",O.color||v)}t.gradio===void 0&&e.gradio&&n!==void 0&&e.gradio(n),t.elem_id===void 0&&e.elem_id&&k!==void 0&&e.elem_id(k),t.elem_classes===void 0&&e.elem_classes&&K!==void 0&&e.elem_classes(K),t.visible===void 0&&e.visible&&S!==void 0&&e.visible(S),t.value===void 0&&e.value&&_!==void 0&&e.value(_),t.file_types===void 0&&e.file_types&&d!==void 0&&e.file_types(d),t.lines===void 0&&e.lines&&f!==void 0&&e.lines(f),t.placeholder===void 0&&e.placeholder&&z!==void 0&&e.placeholder(z),t.label===void 0&&e.label&&g!==void 0&&e.label(g),t.info===void 0&&e.info&&Q!==void 0&&e.info(Q),t.show_label===void 0&&e.show_label&&u!==void 0&&e.show_label(u),t.max_lines===void 0&&e.max_lines&&C!==void 0&&e.max_lines(C),t.scale===void 0&&e.scale&&h!==void 0&&e.scale(h),t.min_width===void 0&&e.min_width&&p!==void 0&&e.min_width(p),t.submit_btn===void 0&&e.submit_btn&&b!==void 0&&e.submit_btn(b),t.stop_btn===void 0&&e.stop_btn&&x!==void 0&&e.stop_btn(x),t.loading_status===void 0&&e.loading_status&&m!==void 0&&e.loading_status(m),t.value_is_output===void 0&&e.value_is_output&&E!==void 0&&e.value_is_output(E),t.rtl===void 0&&e.rtl&&G!==void 0&&e.rtl(G),t.text_align===void 0&&e.text_align&&I!==void 0&&e.text_align(I),t.autofocus===void 0&&e.autofocus&&B!==void 0&&e.autofocus(B),t.autoscroll===void 0&&e.autoscroll&&y!==void 0&&e.autoscroll(y),t.interactive===void 0&&e.interactive&&T!==void 0&&e.interactive(T),t.root===void 0&&e.root&&U!==void 0&&e.root(U),t.file_count===void 0&&e.file_count&&W!==void 0&&e.file_count(W),t.max_plain_text_length===void 0&&e.max_plain_text_length&&s!==void 0&&e.max_plain_text_length(s),t.sources===void 0&&e.sources&&w!==void 0&&e.sources(w),t.waveform_options===void 0&&e.waveform_options&&A!==void 0&&e.waveform_options(A),t.html_attributes===void 0&&e.html_attributes&&j!==void 0&&e.html_attributes(j);let q,D,X=l.head;do q=!0,l.head=X,M={height:50,barWidth:2,barGap:3,cursorWidth:2,cursorColor:\"#ddd5e9\",autoplay:!1,barRadius:10,dragToSeek:!0,normalize:!0,minPxPerSec:20},D=`   ${a(dt,\"Block\").$$render(l,{visible:S,elem_id:k,elem_classes:[...K,\"multimodal-textbox\"],scale:h,min_width:p,allow_overflow:!1,padding:!1,border_mode:c?\"focus\":\"base\"},{},{default:()=>`${m?`${a(vt,\"StatusTracker\").$$render(l,Object.assign({},{autoscroll:n.autoscroll},{i18n:n.i18n},m),{},{})}`:\"\"} ${a(gt,\"MultimodalTextbox\").$$render(l,{file_types:d,root:U,label:g,info:Q,show_label:u,lines:f,rtl:G,text_align:I,waveform_settings:M,i18n:n.i18n,max_lines:C||f+1,placeholder:z,submit_btn:b,stop_btn:x,autofocus:B,autoscroll:y,file_count:W,sources:w,max_file_size:n.max_file_size,disabled:!T,upload:(...o)=>n.client.upload(...o),stream_handler:(...o)=>n.client.stream(...o),max_plain_text_length:s,html_attributes:j,value:_,value_is_output:E,dragging:c,active_source:Y},{value:o=>{_=o,q=!1},value_is_output:o=>{E=o,q=!1},dragging:o=>{c=o,q=!1},active_source:o=>{Y=o,q=!1}},{})}`})}`;while(!q);return D});export{Kt as BaseExample,gt as BaseMultimodalTextbox,Mt as default};\n//# sourceMappingURL=Index43.js.map\n"], "names": ["F", "b", "N", "r", "a", "et", "V", "J", "nt", "ut", "lt", "At", "ot", "ct", "Ct", "at", "it", "rt", "st", "dt", "vt"], "mappings": ";;;;;;;;;;;;;;AAAwiB,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;AACxjB,y+BAAy+B,CAAC,CAAC,GAAG,CAAC,4ivBAA4ivB,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAM,MAAC,EAAE,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAG,IAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAACC,GAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC,CAACC,qBAAC,EAAE,CAA4H,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAED,GAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,qBAAqB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,8BAA8B,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,kDAAkD,EAAEE,aAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEC,kBAAC,CAACC,EAAE,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAEC,MAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,gHAAgH,EAAE,WAAW,CAACA,MAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAEC,IAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,8HAA8H,EAAE,CAAC,6BAA6B,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAEH,kBAAC,CAACI,EAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAEJ,kBAAC,CAACK,CAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAEL,kBAAC,CAACM,EAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAEN,kBAAC,CAACO,EAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEP,kBAAC,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,+EAA+E,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,CAAC,EAAER,kBAAC,CAACS,EAAE,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,4CAA4C,EAAE,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAET,kBAAC,CAACU,EAAE,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,yEAAyE,EAAE,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,EAAEV,kBAAC,CAACW,EAAE,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,+CAA+C,EAAE,CAAC,iCAAiC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,EAAEX,kBAAC,CAACY,EAAE,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,yCAAyC,EAAE,CAAC,2BAA2B,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAEb,aAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEA,aAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,aAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,EAAEA,aAAC,CAAC,OAAO,CAACF,GAAC,CAAC,cAAc,CAACA,GAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAEE,aAAC,CAAC,gBAAgB,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,EAAEA,aAAC,CAAC,aAAa,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAEA,aAAC,CAAC,YAAY,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,EAAEA,aAAC,CAAC,cAAc,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,EAAEA,aAAC,CAAC,UAAU,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAEA,aAAC,CAAC,cAAc,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,EAAEA,aAAC,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEA,aAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEG,MAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,6BAA6B,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEF,kBAAC,CAACa,EAAE,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEX,MAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,2BAA2B,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEF,kBAAC,CAACc,EAAE,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEZ,MAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAACN,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAG,IAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAA6Q,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAA4F,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,qBAAqB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAEI,kBAAC,CAACe,IAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEf,kBAAC,CAACgB,EAAE,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEhB,kBAAC,CAAC,EAAE,CAAC,mBAAmB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;;;;"}