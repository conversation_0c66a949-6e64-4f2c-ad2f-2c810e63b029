import{SvelteComponent as o,init as u,safe_not_equal as d,element as h,text as m,claim_element as _,children as g,claim_text as v,detach as c,attr as y,toggle_class as r,insert_hydration as b,append_hydration as S,set_data as q,noop as f}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function E(n){let e,a=(n[0]!==null?n[0].toLocaleString():"")+"",i;return{c(){e=h("div"),i=m(a),this.h()},l(t){e=_(t,"DIV",{class:!0});var l=g(e);i=v(l,a),l.forEach(c),this.h()},h(){y(e,"class","svelte-1ayixqk"),r(e,"table",n[1]==="table"),r(e,"gallery",n[1]==="gallery"),r(e,"selected",n[2])},m(t,l){b(t,e,l),S(e,i)},p(t,[l]){l&1&&a!==(a=(t[0]!==null?t[0].toLocaleString():"")+"")&&q(i,a),l&2&&r(e,"table",t[1]==="table"),l&2&&r(e,"gallery",t[1]==="gallery"),l&4&&r(e,"selected",t[2])},i:f,o:f,d(t){t&&c(e)}}}function L(n,e,a){let{value:i}=e,{type:t}=e,{selected:l=!1}=e;return n.$$set=s=>{"value"in s&&a(0,i=s.value),"type"in s&&a(1,t=s.type),"selected"in s&&a(2,l=s.selected)},[i,t,l]}class D extends o{constructor(e){super(),u(this,e,L,E,d,{value:0,type:1,selected:2})}}export{D as default};
//# sourceMappingURL=Example.7rhdKtvr.js.map
