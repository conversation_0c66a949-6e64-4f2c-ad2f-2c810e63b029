"""
AI引擎
整合LLM、搜索、图像生成等功能
"""

import re
from typing import Generator, Optional, Dict, Any
from datetime import datetime

from core.llm.model_manager import model_manager
from core.utils.logger import get_logger
from core.utils.config import config_manager
from search.search_manager import search_manager
from engine_2d.api import engine_2d
from memory.context_engine import context_engine
from personas.persona_manager import persona_manager

logger = get_logger(__name__)

class AIEngine:
    """AI引擎"""
    
    def __init__(self):
        self.search_patterns = [
            r'搜索|查找|查询|search',
            r'最新|新闻|news|latest',
            r'什么是|what is|define',
            r'如何|怎么|how to',
            r'为什么|why',
            r'哪里|where',
            r'什么时候|when'
        ]
        
        self.image_patterns = [
            r'画|绘制|生成图片|generate image|draw',
            r'图片|图像|照片|picture|image|photo',
            r'创作|创建图片|create image'
        ]
    
    def generate_response(self, user_input: str, persona) -> Generator[str, None, None]:
        """生成AI回复"""
        try:
            # 检查是否需要搜索
            search_results = None
            if self._should_search(user_input):
                yield "SEARCH:" + user_input
                search_data = search_manager.search_with_context(user_input)
                if search_data and search_data.get('success'):
                    search_results = search_data.get('results', [])
                    yield f"SEARCH_COMPLETE:{len(search_results)}"
                else:
                    search_results = None
                    yield "SEARCH_COMPLETE:0"
            
            # 检查是否需要生成图像
            if self._should_generate_image(user_input):
                yield from self._handle_image_generation(user_input, persona)
                return
            
            # 构建上下文
            context = self._build_context(user_input, persona, search_results)
            
            # 生成文本回复
            yield from self._generate_text_response(context, persona)
            
        except Exception as e:
            # 安全处理异常信息，避免编码错误
            try:
                error_msg = str(e)
                # 确保错误信息可以安全编码
                error_msg.encode('utf-8')
            except UnicodeEncodeError:
                # 如果包含无法编码的字符，使用安全的错误信息
                error_msg = "生成回复时出现编码错误，请检查输入内容"
            except Exception:
                error_msg = "生成回复时出现未知错误"
            
            logger.error(f"AI回复生成失败: {error_msg}")
            yield f"抱歉，生成回复时出现错误: {error_msg}"
    
    def _should_search(self, user_input: str) -> bool:
        """判断是否需要搜索"""
        if not config_manager.get('search.auto_search', True):
            return False
        
        # 检查搜索模式
        for pattern in self.search_patterns:
            if re.search(pattern, user_input, re.IGNORECASE):
                return True
        
        # 检查是否包含时间相关词汇
        time_keywords = ['今天', '昨天', '明天', '最近', '现在', '当前', '2024', '2025']
        for keyword in time_keywords:
            if keyword in user_input:
                return True
        
        return False
    
    def _should_generate_image(self, user_input: str) -> bool:
        """判断是否需要生成图像"""
        for pattern in self.image_patterns:
            if re.search(pattern, user_input, re.IGNORECASE):
                return True
        return False
    
    def _handle_image_generation(self, user_input: str, persona) -> Generator[str, None, None]:
        """处理图像生成"""
        try:
            # 提取图像描述
            prompt = self._extract_image_prompt(user_input)
            
            yield f"好的，我来为你生成图像：{prompt}\n\n"
            yield "正在生成图像，请稍候..."
            
            # 生成图像
            def progress_callback(current: int, total: int, status: str):
                pass  # 在GUI中会有进度显示
            
            # 获取生成参数
            width = config_manager.get('generation.image.width', 1024)
            height = config_manager.get('generation.image.height', 1024)
            steps = config_manager.get('generation.image.steps', 20)
            guidance = config_manager.get('generation.image.guidance_scale', 7.5)
            
            # 生成图像
            image_paths = engine_2d.generate(
                prompt=prompt,
                width=width,
                height=height,
                steps=steps,
                guidance=guidance,
                progress_callback=progress_callback
            )
            
            if image_paths:
                yield f"\n\n✅ 图像生成完成！\n"
                for i, path in enumerate(image_paths, 1):
                    yield f"图像 {i}: {path}\n"
                
                # 根据人设添加个性化回复
                if persona and persona.id == 'wife':
                    yield "\n亲爱的，你觉得这张图片怎么样？我很用心画的哦～ (◕‿◕)💕"
                elif persona and persona.id == 'reasoning_expert':
                    yield f"\n图像已按照你的要求生成，使用了 {steps} 步推理，引导强度为 {guidance}。"
                else:
                    yield "\n希望你喜欢这张图片！"
            else:
                yield "\n❌ 抱歉，图像生成失败了。请检查模型是否正确加载。"
                
        except Exception as e:
            logger.error(f"图像生成失败: {e}")
            yield f"\n❌ 图像生成出现错误: {str(e)}"
    
    def _extract_image_prompt(self, user_input: str) -> str:
        """提取图像生成提示词"""
        # 移除生成指令词汇
        prompt = user_input
        remove_words = ['画', '绘制', '生成', '图片', '图像', '照片', '一张', '一幅', '给我', '帮我']
        
        for word in remove_words:
            prompt = prompt.replace(word, '')
        
        prompt = prompt.strip()
        
        # 如果提示词为空或太短，使用默认提示词
        if len(prompt) < 3:
            prompt = "beautiful landscape, masterpiece, high quality"
        
        return prompt
    
    def _build_context(self, user_input: str, persona, search_results=None) -> list[dict[str, str]]:
        """构建OpenAI格式的对话上下文"""
        messages = []

        # 1. 系统提示词 (System Prompt) - 确保每次都包含完整的人设信息
        system_prompt_parts = []
        
        # 基础人设提示词
        if persona:
            system_prompt_parts.append(f"# 角色设定\n{persona.system_prompt}")
            
            # 添加人设特定的行为指导
            system_prompt_parts.append(f"\n# 人设名称: {persona.name}")
            system_prompt_parts.append(f"# 人设描述: {persona.description}")
            
            # 如果有示例对话，添加到提示词中
            if hasattr(persona, 'example_conversations') and persona.example_conversations:
                system_prompt_parts.append("\n# 对话示例:")
                for example in persona.example_conversations[:2]:  # 只取前2个示例
                    system_prompt_parts.append(f"用户: {example.get('user', '')}")
                    system_prompt_parts.append(f"助手: {example.get('assistant', '')}")

        # 通用回复指导 - 确保每次都包含
        system_prompt_parts.append("\n## 重要回复要求:")
        system_prompt_parts.append("- 严格按照上述角色设定进行回复，保持人设一致性")
        system_prompt_parts.append("- 每次回复都要体现人设的性格特点和说话风格")
        system_prompt_parts.append("- 如果有搜索结果，请自然地结合最新信息回答")
        system_prompt_parts.append("- 保持对话的连贯性和情感连接")
        system_prompt_parts.append("- 回复要自然流畅，避免机械化的表达")
        
        # NSFW设置
        if persona and hasattr(persona, 'nsfw_enabled') and persona.nsfw_enabled:
            system_prompt_parts.append("- 可以讨论成人话题，根据对话内容和人设特点调整回复深度")
        else:
            system_prompt_parts.append("- 保持对话内容健康向上，避免不适当的内容")
        
        # 当前时间信息
        current_time = datetime.now().strftime("%Y年%m月%d日 %H:%M")
        system_prompt_parts.append(f"\n## 当前时间: {current_time}")
        
        messages.append({"role": "system", "content": "\n".join(system_prompt_parts)})

        # 2. 搜索结果 (作为系统信息提供)
        if search_results:
            search_context = ["## 最新搜索信息:"]
            for i, result in enumerate(search_results[:3], 1):
                search_context.append(f"{i}. {result['title']}: {result['snippet']}")
                if result.get('url'):
                    search_context.append(f"   来源: {result['url']}")
            search_context.append("\n请根据以上信息结合人设特点进行回复。")
            messages.append({"role": "system", "content": "\n".join(search_context)})

        # 3. 对话历史 - 保持上下文连贯性
        conversation_history = context_engine.get_context_for_generation()
        if conversation_history:
            # 取最近的对话历史，但确保不超过token限制
            recent_history = conversation_history[-8:]  # 取最近8条消息
            for item in recent_history:
                messages.append({"role": item['role'], "content": item['content']})

        # 4. 当前用户输入
        messages.append({"role": "user", "content": user_input})
        
        return messages

    def _generate_text_response(self, messages: list[dict[str, str]], persona) -> Generator[str, None, None]:
        """生成文本回复"""
        if not model_manager.client or not model_manager.current_config:
            yield "❌ LLM客户端未连接，请先启动llama.cpp服务器并选择模型。"
            return
        
        try:
            # 获取生成参数
            max_tokens = config_manager.get('generation.max_tokens', 2048)
            temperature = config_manager.get('generation.temperature', 0.7)
            top_p = config_manager.get('generation.top_p', 0.9)
            
            # 生成回复
            for chunk in model_manager.generate_text(
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p
            ):
                yield chunk
                
        except Exception as e:
            logger.error(f"文本生成失败: {e}")
            yield f"❌ 文本生成出现错误: {str(e)}"
    
    def get_model_status(self) -> Dict[str, Any]:
        """获取模型状态"""
        return {
            'llm_model': model_manager.get_current_model_info(),
            'image_model': engine_2d.get_current_model(),
            'available_llm_models': list(model_manager.get_available_models().keys()),
            'available_image_models': list(engine_2d.list_models().keys())
        }
    
    def load_llm_model(self, model_name: str) -> bool:
        """加载LLM模型"""
        return model_manager.load_model(model_name)
    
    def load_image_model(self, model_name: str) -> bool:
        """加载图像模型"""
        return engine_2d.load_model(model_name)
    
    def clear_context(self):
        """清空对话上下文"""
        context_engine.clear_current_session()
    
    def get_conversation_summary(self) -> str:
        """获取对话摘要"""
        context = context_engine.get_context_for_generation()
        if not context:
            return "暂无对话内容"

        if not model_manager.client or not model_manager.current_config:
            return "LLM客户端未连接，无法生成摘要。"

        # 构建摘要请求
        summary_prompt = "请将以下对话总结为一段简短的摘要，不超过100字。"
        messages = context[-10:] + [{"role": "user", "content": summary_prompt}]

        try:
            summary = model_manager.generate_text_complete(messages=messages, max_tokens=200)
            return summary
        except Exception as e:
            logger.error(f"生成对话摘要失败: {e}")
            return f"生成摘要失败: {e}"

# 全局AI引擎实例
ai_engine = AIEngine()
