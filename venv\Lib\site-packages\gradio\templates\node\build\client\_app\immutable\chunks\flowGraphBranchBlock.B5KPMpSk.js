import{c as e}from"./declarationMapper.UBCwU7BT.js";import{f as i}from"./KHR_interactivity.DEAVS2UW.js";import{R as o}from"./index.BoI39RQH.js";class a extends i{constructor(t){super(t),this.condition=this.registerDataInput("condition",e),this.onTrue=this._registerSignalOutput("onTrue"),this.onFalse=this._registerSignalOutput("onFalse")}_execute(t){this.condition.getValue(t)?this.onTrue._activateSignal(t):this.onFalse._activateSignal(t)}getClassName(){return"FlowGraphBranchBlock"}}o("FlowGraphBranchBlock",a);export{a as FlowGraphBranchBlock};
//# sourceMappingURL=flowGraphBranchBlock.B5KPMpSk.js.map
