import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qqmlbind_p.h"
        name: "QQmlBind"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus", "QQmlPropertyValueSource"]
        immediateNames: [
            "objectName",
            "target",
            "property",
            "value",
            "when",
            "delayed",
            "restoreMode"
        ]
        exports: [
            "QtQml/Binding 2.0",
            "QtQml/Binding 2.8",
            "QtQml/Binding 2.14",
            "QtQml/Binding 6.0"
        ]
        exportMetaObjectRevisions: [512, 520, 526, 1536]
        Enum {
            name: "RestorationMode"
            values: [
                "RestoreNone",
                "RestoreBinding",
                "RestoreValue",
                "RestoreBindingOrValue"
            ]
        }
        Property {
            name: "target"
            type: "QObject"
            isPointer: true
            read: "object"
            write: "setObject"
            index: 0
        }
        Property { name: "property"; type: "QString"; read: "property"; write: "setProperty"; index: 1 }
        Property { name: "value"; type: "QVariant"; read: "value"; write: "setValue"; index: 2 }
        Property { name: "when"; type: "bool"; read: "when"; write: "setWhen"; index: 3 }
        Property {
            name: "delayed"
            revision: 520
            type: "bool"
            read: "delayed"
            write: "setDelayed"
            index: 4
        }
        Property {
            name: "restoreMode"
            revision: 526
            type: "RestorationMode"
            read: "restoreMode"
            write: "setRestoreMode"
            notify: "restoreModeChanged"
            index: 5
        }
        Signal { name: "restoreModeChanged" }
        Method { name: "targetValueChanged" }
    }
    Component {
        file: "private/qqmlconnections_p.h"
        name: "QQmlConnections"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQml/Connections 2.0",
            "QtQml/Connections 2.3",
            "QtQml/Connections 6.0"
        ]
        hasCustomParser: true
        exportMetaObjectRevisions: [512, 515, 1536]
        Property {
            name: "target"
            type: "QObject"
            isPointer: true
            read: "target"
            write: "setTarget"
            notify: "targetChanged"
            index: 0
        }
        Property {
            name: "enabled"
            revision: 515
            type: "bool"
            read: "isEnabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 1
        }
        Property {
            name: "ignoreUnknownSignals"
            type: "bool"
            read: "ignoreUnknownSignals"
            write: "setIgnoreUnknownSignals"
            index: 2
        }
        Signal { name: "targetChanged" }
        Signal { name: "enabledChanged"; revision: 515 }
    }
    Component {
        file: "private/qqmllocaleenums_p.h"
        name: "QQmlLocaleEnums"
        accessSemantics: "none"
        prototype: "QQmlLocale"
        exports: ["QtQml/Locale 2.2", "QtQml/Locale 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [514, 1536]
    }
    Component {
        file: "private/qqmlloggingcategory_p.h"
        name: "QQmlLoggingCategory"
        accessSemantics: "reference"
        prototype: "QQmlLoggingCategoryBase"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQml/LoggingCategory 2.8",
            "QtQml/LoggingCategory 2.12",
            "QtQml/LoggingCategory 6.0"
        ]
        exportMetaObjectRevisions: [520, 524, 1536]
        Enum {
            name: "DefaultLogLevel"
            values: ["Debug", "Info", "Warning", "Critical", "Fatal"]
        }
        Property { name: "name"; type: "QString"; read: "name"; write: "setName"; index: 0 }
        Property {
            name: "defaultLogLevel"
            revision: 524
            type: "DefaultLogLevel"
            read: "defaultLogLevel"
            write: "setDefaultLogLevel"
            index: 1
        }
    }
    Component {
        file: "private/qqmltimer_p.h"
        name: "QQmlTimer"
        accessSemantics: "reference"
        parentProperty: "parent"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: ["QtQml/Timer 2.0", "QtQml/Timer 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "interval"
            type: "int"
            read: "interval"
            write: "setInterval"
            notify: "intervalChanged"
            index: 0
        }
        Property {
            name: "running"
            type: "bool"
            read: "isRunning"
            write: "setRunning"
            notify: "runningChanged"
            index: 1
        }
        Property {
            name: "repeat"
            type: "bool"
            read: "isRepeating"
            write: "setRepeating"
            notify: "repeatChanged"
            index: 2
        }
        Property {
            name: "triggeredOnStart"
            type: "bool"
            read: "triggeredOnStart"
            write: "setTriggeredOnStart"
            notify: "triggeredOnStartChanged"
            index: 3
        }
        Property {
            name: "parent"
            type: "QObject"
            isPointer: true
            read: "parent"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Signal { name: "triggered" }
        Signal { name: "runningChanged" }
        Signal { name: "intervalChanged" }
        Signal { name: "repeatChanged" }
        Signal { name: "triggeredOnStartChanged" }
        Method { name: "start" }
        Method { name: "stop" }
        Method { name: "restart" }
        Method { name: "ticked" }
    }
}
