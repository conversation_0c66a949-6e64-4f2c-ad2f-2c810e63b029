import{SvelteComponent as b,init as k,safe_not_equal as w,create_component as r,claim_component as z,mount_component as B,transition_in as q,transition_out as C,destroy_component as I,text as S,claim_text as j,insert_hydration as A,set_data as D,detach as E}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{o as F}from"./2.B2AoQPnG.js";function G(a){let i=(a[3]??"")+"",l;return{c(){l=S(i)},l(e){l=j(e,i)},m(e,t){A(e,l,t)},p(e,t){t&8&&i!==(i=(e[3]??"")+"")&&D(l,i)},d(e){e&&E(l)}}}function H(a){let i,l;return i=new F({props:{value:a[3],variant:a[4],elem_id:a[0],elem_classes:a[1],size:a[6],scale:a[7],link:a[9],icon:a[8],min_width:a[10],visible:a[2],disabled:!a[5],$$slots:{default:[G]},$$scope:{ctx:a}}}),i.$on("click",a[12]),{c(){r(i.$$.fragment)},l(e){z(i.$$.fragment,e)},m(e,t){B(i,e,t),l=!0},p(e,[t]){const f={};t&8&&(f.value=e[3]),t&16&&(f.variant=e[4]),t&1&&(f.elem_id=e[0]),t&2&&(f.elem_classes=e[1]),t&64&&(f.size=e[6]),t&128&&(f.scale=e[7]),t&512&&(f.link=e[9]),t&256&&(f.icon=e[8]),t&1024&&(f.min_width=e[10]),t&4&&(f.visible=e[2]),t&32&&(f.disabled=!e[5]),t&8200&&(f.$$scope={dirty:t,ctx:e}),i.$set(f)},i(e){l||(q(i.$$.fragment,e),l=!0)},o(e){C(i.$$.fragment,e),l=!1},d(e){I(i,e)}}}function J(a,i,l){let{elem_id:e=""}=i,{elem_classes:t=[]}=i,{visible:f=!0}=i,{value:s}=i,{variant:_="secondary"}=i,{interactive:u}=i,{size:c="lg"}=i,{scale:o=null}=i,{icon:v=null}=i,{link:d=null}=i,{min_width:h=void 0}=i,{gradio:m}=i;const g=()=>m.dispatch("click");return a.$$set=n=>{"elem_id"in n&&l(0,e=n.elem_id),"elem_classes"in n&&l(1,t=n.elem_classes),"visible"in n&&l(2,f=n.visible),"value"in n&&l(3,s=n.value),"variant"in n&&l(4,_=n.variant),"interactive"in n&&l(5,u=n.interactive),"size"in n&&l(6,c=n.size),"scale"in n&&l(7,o=n.scale),"icon"in n&&l(8,v=n.icon),"link"in n&&l(9,d=n.link),"min_width"in n&&l(10,h=n.min_width),"gradio"in n&&l(11,m=n.gradio)},[e,t,f,s,_,u,c,o,v,d,h,m,g]}class N extends b{constructor(i){super(),k(this,i,J,H,w,{elem_id:0,elem_classes:1,visible:2,value:3,variant:4,interactive:5,size:6,scale:7,icon:8,link:9,min_width:10,gradio:11})}}export{F as BaseButton,N as default};
//# sourceMappingURL=Index.CfFYKADH.js.map
