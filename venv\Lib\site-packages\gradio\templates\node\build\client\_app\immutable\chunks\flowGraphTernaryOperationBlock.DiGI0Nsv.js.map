{"version": 3, "file": "flowGraphTernaryOperationBlock.DiGI0Nsv.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphTernaryOperationBlock.js"], "sourcesContent": ["import { FlowGraphCachedOperationBlock } from \"./flowGraphCachedOperationBlock.js\";\n/**\n * @internal\n * The base block for all ternary operation blocks.\n */\nexport class FlowGraphTernaryOperationBlock extends FlowGraphCachedOperationBlock {\n    constructor(t1Type, t2Type, t3Type, resultRichType, _operation, _className, config) {\n        super(resultRichType, config);\n        this._operation = _operation;\n        this._className = _className;\n        this.a = this.registerDataInput(\"a\", t1Type);\n        this.b = this.registerDataInput(\"b\", t2Type);\n        this.c = this.registerDataInput(\"c\", t3Type);\n    }\n    /**\n     * the operation performed by this block\n     * @param context the graph context\n     * @returns the result of the operation\n     */\n    _doOperation(context) {\n        return this._operation(this.a.getValue(context), this.b.getValue(context), this.c.getValue(context));\n    }\n    /**\n     * Gets the class name of this block\n     * @returns the class name\n     */\n    getClassName() {\n        return this._className;\n    }\n}\n//# sourceMappingURL=flowGraphTernaryOperationBlock.js.map"], "names": ["FlowGraphTernaryOperationBlock", "FlowGraphCachedOperationBlock", "t1Type", "t2Type", "t3Type", "resultRichType", "_operation", "_className", "config", "context"], "mappings": "gEAKO,MAAMA,UAAuCC,CAA8B,CAC9E,YAAYC,EAAQC,EAAQC,EAAQC,EAAgBC,EAAYC,EAAYC,EAAQ,CAChF,MAAMH,EAAgBG,CAAM,EAC5B,KAAK,WAAaF,EAClB,KAAK,WAAaC,EAClB,KAAK,EAAI,KAAK,kBAAkB,IAAKL,CAAM,EAC3C,KAAK,EAAI,KAAK,kBAAkB,IAAKC,CAAM,EAC3C,KAAK,EAAI,KAAK,kBAAkB,IAAKC,CAAM,CAC9C,CAMD,aAAaK,EAAS,CAClB,OAAO,KAAK,WAAW,KAAK,EAAE,SAASA,CAAO,EAAG,KAAK,EAAE,SAASA,CAAO,EAAG,KAAK,EAAE,SAASA,CAAO,CAAC,CACtG,CAKD,cAAe,CACX,OAAO,KAAK,UACf,CACL", "x_google_ignoreList": [0]}