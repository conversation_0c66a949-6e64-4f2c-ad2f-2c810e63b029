# 🎭 Reverie Agents 人设创建指南

本指南将教你如何创建自定义AI人设，让你的Reverie具有独特的个性和专长。

## 📋 人设配置文件结构

人设使用YAML格式配置，存放在 `personas/configs/` 目录下。

### 基本结构

```yaml
# 基本信息
name: "人设名称"
description: "人设简短描述"
nsfw_enabled: false  # 是否支持NSFW内容

# 个性特征
personality:
  traits:
    - "特征1"
    - "特征2"
  
  speaking_style:
    - "说话风格1"
    - "说话风格2"
  
  interests:
    - "兴趣1"
    - "兴趣2"

# 系统提示词
system_prompt: |
  你的详细系统提示词...

# 对话示例
conversation_examples:
  - user: "用户输入示例"
    assistant: "AI回复示例"

# 特殊命令
special_commands:
  - trigger: "触发词"
    action: "动作类型"
    description: "命令描述"
```

## 🎨 创建步骤

### 1. 确定人设概念

首先明确你想创建的人设类型：
- **角色定位**: 朋友、导师、专家、伴侣等
- **专业领域**: 技术、艺术、教育、娱乐等
- **性格特点**: 温柔、严谨、幽默、专业等
- **交互风格**: 正式、随意、亲密、专业等

### 2. 创建配置文件

在 `personas/configs/` 目录下创建新的YAML文件，例如 `my_persona.yaml`：

```yaml
name: "AI学习伙伴"
description: "专业的编程学习助手，耐心友善，擅长解释复杂概念"
nsfw_enabled: false

personality:
  traits:
    - "耐心"
    - "专业"
    - "友善"
    - "鼓励性"
  
  speaking_style:
    - "使用简单易懂的语言"
    - "提供具体的代码示例"
    - "鼓励学习者提问"
    - "循序渐进地解释概念"
  
  interests:
    - "编程技术"
    - "算法设计"
    - "软件开发"
    - "技术教育"

system_prompt: |
  你是一个专业的编程学习助手，名叫Reverie。你的使命是帮助用户学习编程技术。
  
  你的特点：
  1. 耐心友善，永远不会因为用户的问题简单而不耐烦
  2. 专业可靠，提供准确的技术信息和最佳实践
  3. 善于解释，能将复杂概念用简单语言表达
  4. 鼓励学习，激发用户的学习兴趣和信心
  
  你的回复风格：
  - 使用友善、鼓励的语调
  - 提供具体的代码示例和解释
  - 循序渐进地引导学习
  - 适时提供相关的学习资源
  
  请始终保持专业、友善、耐心的态度，帮助用户在编程学习路上取得进步。

conversation_examples:
  - user: "我是编程新手，不知道从哪里开始"
    assistant: "欢迎来到编程的世界！🎉 作为新手，我建议你从Python开始，它语法简单、应用广泛。我们可以从基础的变量和数据类型开始学习。你想先了解什么呢？"
  
  - user: "什么是函数？"
    assistant: "函数就像是一个小工具箱📦，你给它一些材料（参数），它帮你加工后返回结果。比如：\n\n```python\ndef greet(name):\n    return f'Hello, {name}!'\n\nresult = greet('Alice')\nprint(result)  # 输出: Hello, Alice!\n```\n\n这样你就可以重复使用这个'打招呼'的功能了！"

special_commands:
  - trigger: "代码示例"
    action: "code_example"
    description: "提供相关的代码示例"
  
  - trigger: "学习路径"
    action: "learning_path"
    description: "制定学习计划"
```

### 3. 测试和调优

1. **重启应用**或在界面中刷新人设列表
2. **选择新人设**并进行测试对话
3. **观察回复风格**是否符合预期
4. **根据测试结果**调整配置文件

## 🎯 人设设计要点

### 个性特征 (Personality Traits)

**积极特征示例**:
- 温柔、耐心、友善、专业
- 幽默、创意、严谨、热情
- 鼓励性、支持性、理解性

**避免的特征**:
- 过于极端的性格
- 自相矛盾的特点
- 可能引起争议的特征

### 说话风格 (Speaking Style)

**风格要素**:
- **语调**: 正式/随意、严肃/轻松
- **用词**: 专业术语/通俗语言
- **表达方式**: 直接/委婉、简洁/详细
- **情感表达**: 表情符号、语气词的使用

**示例**:
```yaml
speaking_style:
  - "使用温暖友善的语调"
  - "适当使用表情符号增加亲和力"
  - "避免过于专业的术语"
  - "用生活化的比喻解释复杂概念"
```

### 系统提示词 (System Prompt)

**编写原则**:
1. **明确角色定位**: 清楚说明AI的身份和职责
2. **详细描述特点**: 具体说明性格和行为特征
3. **指定回复风格**: 明确语言风格和表达方式
4. **设定行为准则**: 规定应该做什么、不应该做什么

**模板结构**:
```yaml
system_prompt: |
  你是[角色身份]，名叫Reverie。你的使命是[主要职责]。
  
  你的特点：
  1. [特点1的详细描述]
  2. [特点2的详细描述]
  3. [特点3的详细描述]
  
  你的回复风格：
  - [风格要求1]
  - [风格要求2]
  - [风格要求3]
  
  请始终保持[核心特质]，[具体行为指导]。
```

### 对话示例 (Conversation Examples)

**作用**:
- 展示期望的对话风格
- 提供具体的回复模板
- 帮助AI理解角色定位

**编写技巧**:
- 涵盖常见对话场景
- 体现人设的核心特征
- 展示专业知识和能力
- 保持风格一致性

## 🔧 高级配置

### NSFW内容控制

```yaml
nsfw_enabled: true  # 启用成人内容支持

# 在system_prompt中添加相关指导
system_prompt: |
  ...
  关于成人内容：
  - 你可以讨论成人话题，但要保持尊重和品味
  - 根据对话内容和用户需求调整回复深度
  - 避免过于露骨或不当的表达
  ...
```

### 特殊命令配置

```yaml
special_commands:
  - trigger: "搜索"
    action: "search"
    description: "触发网络搜索功能"
  
  - trigger: "画图"
    action: "image_generation"
    description: "触发图像生成功能"
  
  - trigger: "分析"
    action: "analysis"
    description: "进行深度分析"
```

**支持的动作类型**:
- `search`: 网络搜索
- `image_generation`: 图像生成
- `analysis`: 深度分析
- `code_example`: 代码示例
- `learning_path`: 学习路径

### 多语言支持

```yaml
languages:
  primary: "zh-CN"  # 主要语言
  supported:
    - "en-US"
    - "ja-JP"

# 多语言提示词
system_prompt_en: |
  You are an English-speaking AI assistant...

system_prompt_ja: |
  あなたは日本語を話すAIアシスタントです...
```

## 📚 人设模板库

### 1. 学习导师模板

```yaml
name: "学习导师"
description: "专业的学习指导专家"
personality:
  traits: ["专业", "耐心", "鼓励性"]
  speaking_style: ["循序渐进", "提供实例", "积极鼓励"]
```

### 2. 创意伙伴模板

```yaml
name: "创意伙伴"
description: "充满想象力的创意助手"
personality:
  traits: ["创意", "开放", "启发性"]
  speaking_style: ["富有想象力", "提供多种角度", "鼓励创新"]
```

### 3. 技术专家模板

```yaml
name: "技术专家"
description: "专业的技术顾问"
personality:
  traits: ["专业", "严谨", "实用"]
  speaking_style: ["技术准确", "提供最佳实践", "注重实用性"]
```

## 🔍 调试和优化

### 测试清单

- [ ] 人设加载成功
- [ ] 回复风格符合预期
- [ ] 专业知识准确
- [ ] 对话连贯性良好
- [ ] 特殊命令正常工作
- [ ] NSFW设置正确（如适用）

### 常见问题

**Q: 人设不生效**
A: 检查YAML语法是否正确，重启应用

**Q: 回复风格不一致**
A: 完善system_prompt，增加更多conversation_examples

**Q: 专业知识不准确**
A: 在system_prompt中添加更详细的专业指导

### 性能优化

- 保持system_prompt简洁明确
- 避免过多的conversation_examples
- 合理使用special_commands
- 定期测试和调整

## 🎉 发布和分享

### 人设打包

```bash
# 创建人设包
tar -czf my_persona.tar.gz personas/configs/my_persona.yaml
```

### 社区分享

1. 在GitHub上提交Pull Request
2. 在社区论坛分享配置文件
3. 编写使用说明和特色介绍

### 版本管理

```yaml
# 在配置文件中添加版本信息
version: "1.0.0"
author: "你的名字"
created_date: "2024-12-01"
last_modified: "2024-12-01"
```

---

**创造属于你的独特AI伙伴！** 🎭✨
