{"version": 3, "file": "Example.qq1Un4eO.js", "sources": ["../../../../../../../paramviewer/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let value: string;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t<pre>{JSON.stringify(value, null, 2)}</pre>\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n"], "names": ["t_value", "ctx", "toggle_class", "div", "insert_hydration", "target", "anchor", "append_hydration", "pre", "dirty", "set_data", "t", "value", "$$props", "type", "selected"], "mappings": "iVAWOA,EAAA,KAAK,UAAUC,EAAO,CAAA,EAAA,KAAM,CAAC,EAAA,yMAJtBC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,EADJC,EAA0CJ,EAAAK,CAAA,mBAApCC,EAAA,GAAAT,KAAAA,EAAA,KAAK,UAAUC,EAAO,CAAA,EAAA,KAAM,CAAC,EAAA,KAAAS,EAAAC,EAAAX,CAAA,OAJtBE,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,sEAPtB,GAAA,CAAA,MAAAW,CAAA,EAAAC,EACA,CAAA,KAAAC,CAAA,EAAAD,GACA,SAAAE,EAAW,EAAA,EAAAF"}