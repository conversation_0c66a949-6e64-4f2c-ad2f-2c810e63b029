import{_ as a,j as g,l as m}from"./mermaid.core.CKP5SxPy.js";import{s as n}from"./select.BigU4G0v.js";var b=a((t,e)=>{let o;return e==="sandbox"&&(o=n("#i"+t)),(e==="sandbox"?n(o.nodes()[0].contentDocument.body):n("body")).select(`[id="${t}"]`)},"getDiagramElement"),B=a((t,e,o,i)=>{t.attr("class",o);const{width:r,height:s,x:h,y:x}=d(t,e);g(t,s,r,i);const c=l(h,x,r,s,e);t.attr("viewBox",c),m.debug(`viewBox configured: ${c} with padding: ${e}`)},"setupViewPortForSVG"),d=a((t,e)=>{var i;const o=((i=t.node())==null?void 0:i.getBBox())||{width:0,height:0,x:0,y:0};return{width:o.width+e*2,height:o.height+e*2,x:o.x,y:o.y}},"calculateDimensionsWithPadding"),l=a((t,e,o,i,r)=>`${t-r} ${e-r} ${o} ${i}`,"createViewBox");export{b as g,B as s};
//# sourceMappingURL=chunk-2O5F6CEG.B3wNlUn0.js.map
