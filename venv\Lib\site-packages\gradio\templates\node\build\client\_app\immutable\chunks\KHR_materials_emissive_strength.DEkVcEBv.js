import{ar as a,an as d,ao as h}from"./index.BoI39RQH.js";import{GLTFLoader as m}from"./glTFLoader.BetPWe9U.js";const t="KHR_materials_emissive_strength";class l{constructor(e){this.name=t,this.order=170,this._loader=e,this.enabled=this._loader.isExtensionUsed(t)}dispose(){this._loader=null}loadMaterialPropertiesAsync(e,s,i){return m.LoadExtensionAsync(e,s,this.name,(n,o)=>this._loader.loadMaterialPropertiesAsync(e,s,i).then(()=>{this._loadEmissiveProperties(n,o,i)}))}_loadEmissiveProperties(e,s,i){if(!(i instanceof a))throw new Error(`${e}: Material type not supported`);s.emissiveStrength!==void 0&&(i.emissiveIntensity=s.emissiveStrength)}}d(t);h(t,!0,r=>new l(r));export{l as KHR_materials_emissive_strength};
//# sourceMappingURL=KHR_materials_emissive_strength.DEkVcEBv.js.map
