import{setContext as sl,tick as rt,SvelteComponent as qe,init as He,safe_not_equal as Ve,element as P,svg_element as ne,claim_element as U,children as S,claim_svg_element as ie,detach as d,attr as h,null_to_empty as ol,insert_hydration as q,append_hydration as N,listen as ce,stop_propagation as xe,noop as Ce,binding_callbacks as Ee,bind as Ie,create_component as re,claim_component as _e,mount_component as se,add_flush_callback as Oe,transition_in as E,transition_out as A,destroy_component as oe,space as X,empty as ke,claim_space as G,group_outros as me,check_outros as ge,createEventDispatcher as pt,toggle_class as Y,set_input_value as al,action_destroyer as hi,run_all as Qe,bubble as it,prevent_default as _n,text as Ae,claim_text as Be,set_data as Me,HtmlTagHydration as di,claim_html_tag as mi,construct_svelte_component as ul,get_svelte_dataset as lt,is_function as Xe,set_style as De,create_slot as zt,add_render_callback as _l,add_iframe_resize_listener as fl,resize_observer_content_box as gi,update_slot_base as Pt,get_all_dirty_from_scope as Ut,get_slot_changes as Rt,onMount as _t,ensure_array_like as We,update_keyed_each as ft,outro_and_destroy_block as ct,ResizeObserverSingleton as bi,destroy_each as wi,onDestroy as ki,component_subscribe as vi,afterUpdate as pi,globals as yi,flush as pe,assign as Ei,get_spread_update as Ni,get_spread_object as Ti}from"../../../svelte/svelte.js";import{writable as Ci,get as ze}from"../../../svelte/svelte-submodules.js";import{M as Si,H as Jt,J as Ai,B as Bi,S as Mi}from"./2.B2AoQPnG.js";import{d as Vt}from"./index.tFQomdd2.js";import{d as Di}from"./dsv.DB8NKgIY.js";import{d as ut}from"./index.CnqicUFC.js";import{a as Ii}from"./Upload.yOHVlgUe.js";import{C as Oi}from"./Checkbox.BBJAZbOw.js";import{D as Li}from"./DropdownArrow.pfrcUdj1.js";import{F as ji}from"./FullscreenButton.g_8wwg6y.js";import qi from"./Index.BekbgQye.js";import{default as ao}from"./Example.Meu2o5J6.js";function wt(l,e,t){if(!e.length)return"none";const n=e.find(i=>{const s=i.col;return s<0||s>=t.length?!1:t[s]===l});return n?n.direction:"none"}function Hi(l,e){if(!l||!l.length||!l[0])return[];if(e.length>0){const t=[...Array(l.length)].map((n,i)=>i);return t.sort((n,i)=>{const s=l[n],r=l[i];for(const{col:o,direction:_}of e){if(!s||!r||o<0||o>=s.length||o>=r.length||!s[o]||!r[o])continue;const u=s[o].value,a=r[o].value,f=u<a?-1:u>a?1:0;if(f!==0)return _==="asc"?f:-f}return 0}),t}return[...Array(l.length)].map((t,n)=>n)}function Vi(l,e,t,n,i,s){let r=null;i&&i[0]in l&&i[1]in l[i[0]]&&(r=l[i[0]][i[1]].id),fn(l,e,t,n);let o=i;if(r){const[_,u]=s(r,l);o=[_,u]}return{data:l,selected:o}}function zi(l,e){if(!l||!l.length||!l[0])return[];let t=[...Array(l.length)].map((n,i)=>i);return e.length>0?(e.forEach(n=>{if(n.datatype==="string")switch(n.filter){case"Contains":t=t.filter(i=>{var s;return(s=l[i][n.col])==null?void 0:s.value.toString().includes(n.value)});break;case"Does not contain":t=t.filter(i=>{var s;return!((s=l[i][n.col])!=null&&s.value.toString().includes(n.value))});break;case"Starts with":t=t.filter(i=>{var s;return(s=l[i][n.col])==null?void 0:s.value.toString().startsWith(n.value)});break;case"Ends with":t=t.filter(i=>{var s;return(s=l[i][n.col])==null?void 0:s.value.toString().endsWith(n.value)});break;case"Is":t=t.filter(i=>{var s;return((s=l[i][n.col])==null?void 0:s.value.toString())===n.value});break;case"Is not":t=t.filter(i=>{var s;return((s=l[i][n.col])==null?void 0:s.value.toString())!==n.value});break;case"Is empty":t=t.filter(i=>{var s;return((s=l[i][n.col])==null?void 0:s.value.toString())===""});break;case"Is not empty":t=t.filter(i=>{var s;return((s=l[i][n.col])==null?void 0:s.value.toString())!==""});break}else if(n.datatype==="number")switch(n.filter){case"=":t=t.filter(i=>{var s,r;return!isNaN(Number((s=l[i][n.col])==null?void 0:s.value))&&!isNaN(Number(n.value))?Number((r=l[i][n.col])==null?void 0:r.value)===Number(n.value):!1});break;case"≠":t=t.filter(i=>{var s,r;return!isNaN(Number((s=l[i][n.col])==null?void 0:s.value))&&!isNaN(Number(n.value))?Number((r=l[i][n.col])==null?void 0:r.value)!==Number(n.value):!1});break;case">":t=t.filter(i=>{var s,r;return!isNaN(Number((s=l[i][n.col])==null?void 0:s.value))&&!isNaN(Number(n.value))?Number((r=l[i][n.col])==null?void 0:r.value)>Number(n.value):!1});break;case"<":t=t.filter(i=>{var s,r;return!isNaN(Number((s=l[i][n.col])==null?void 0:s.value))&&!isNaN(Number(n.value))?Number((r=l[i][n.col])==null?void 0:r.value)<Number(n.value):!1});break;case"≥":t=t.filter(i=>{var s,r;return!isNaN(Number((s=l[i][n.col])==null?void 0:s.value))&&!isNaN(Number(n.value))?Number((r=l[i][n.col])==null?void 0:r.value)>=Number(n.value):!1});break;case"≤":t=t.filter(i=>{var s,r;return!isNaN(Number((s=l[i][n.col])==null?void 0:s.value))&&!isNaN(Number(n.value))?Number((r=l[i][n.col])==null?void 0:r.value)<=Number(n.value):!1});break;case"Is empty":t=t.filter(i=>{var s;return((s=l[i][n.col])==null?void 0:s.value.toString())===""});break;case"Is not empty":t=t.filter(i=>{var s,r;return isNaN(Number((s=l[i][n.col])==null?void 0:s.value))?!1:((r=l[i][n.col])==null?void 0:r.value.toString())!==""});break}}),t):[...Array(l.length)].map((n,i)=>i)}function Pi(l,e,t,n,i,s,r,o,_){let u=null;i&&i[0]in l&&i[1]in l[i[0]]&&(u=l[i[0]][i[1]].id),Ri(l,e,t,n,r,o,_);let a=i;if(u){const[f,c]=s(u,l);a=[f,c]}return{data:l,selected:a}}function Ui(l){if(!l||!l.length)return[];let e=l[0].slice();for(let t=0;t<l.length;t++)for(let n=0;n<l[t].length;n++)`${e[n].value}`.length<`${l[t][n].value}`.length&&(e[n]=l[t][n]);return e}function fn(l,e,t,n){if(!n.length||!l||!l.length)return;const i=Hi(l,n),s=i.map(r=>l[r]);if(l.splice(0,l.length,...s),e){const r=i.map(o=>e[o]);e.splice(0,e.length,...r)}if(t){const r=i.map(o=>t[o]);t.splice(0,t.length,...r)}}function Ri(l,e,t,n,i,s,r){const o=i??l,_=s??e,u=r??t;if(!n.length){l.splice(0,l.length,...o.map(c=>[...c])),e&&_&&e.splice(0,e.length,..._.map(c=>[...c])),t&&u&&t.splice(0,t.length,...u.map(c=>[...c]));return}if(!l||!l.length)return;const a=zi(o,n),f=a.map(c=>o[c]);if(l.splice(0,l.length,...f),e&&_){const c=a.map(g=>_[g]);e.splice(0,e.length,...c)}if(t&&u){const c=a.map(g=>u[g]);t.splice(0,t.length,...c)}}async function cn(l,e){if(!l||!l.length)return;const n=(e||l.flatMap((o,_)=>o.map((u,a)=>[_,a]))).reduce((o,[_,u])=>{o[_]=o[_]||{};const a=String(l[_][u].value);return o[_][u]=a.includes(",")||a.includes('"')||a.includes(`
`)?`"${a.replace(/"/g,'""')}"`:a,o},{}),i=Object.keys(n).sort((o,_)=>+o-+_);if(!i.length)return;const s=Object.keys(n[i[0]]).sort((o,_)=>+o-+_),r=i.map(o=>s.map(_=>n[o][_]||"").join(",")).join(`
`);try{await navigator.clipboard.writeText(r)}catch(o){throw new Error("Failed to copy to clipboard: "+o.message)}}function Ji(l,e){return e.filter(t);function t(n){var i=-1;return l.split(`
`).every(s);function s(r){if(!r)return!0;var o=r.split(n).length;return i<0&&(i=o),i===o&&o>1}}}function Fi(l){const e=atob(l.split(",")[1]),t=l.split(",")[0].split(":")[1].split(";")[0],n=new ArrayBuffer(e.length),i=new Uint8Array(n);for(let s=0;s<e.length;s++)i[s]=e.charCodeAt(s);return new Blob([n],{type:t})}function Ki(l,e,t){const n=Fi(l),i=new FileReader;i.addEventListener("loadend",s=>{var u;if(!((u=s==null?void 0:s.target)!=null&&u.result)||typeof s.target.result!="string")return;const[r]=Ji(s.target.result,[",","	"]),[o,..._]=Di(r).parseRows(s.target.result);e(o),t(_)}),i.readAsText(n)}function Wi(l,e){const[t,n]=l;return e.some(([i,s])=>i===t&&s===n)}function Yi(l,e){const[t,n]=l;if(!e.some(([_,u])=>_===t&&u===n))return"";const i=e.some(([_,u])=>_===t-1&&u===n),s=e.some(([_,u])=>_===t+1&&u===n),r=e.some(([_,u])=>_===t&&u===n-1),o=e.some(([_,u])=>_===t&&u===n+1);return`cell-selected${i?" no-top":""}${s?" no-bottom":""}${r?" no-left":""}${o?" no-right":""}`}function Ft(l,e){const[t,n]=l,[i,s]=e,r=Math.min(t,i),o=Math.max(t,i),_=Math.min(n,s),u=Math.max(n,s),a=[];a.push(l);for(let f=r;f<=o;f++)for(let c=_;c<=u;c++)f===t&&c===n||a.push([f,c]);return a}function Xi(l,e,t){if(t.shiftKey&&e.length>0)return Ft(e[e.length-1],l);if(t.metaKey||t.ctrlKey){const n=([s,r])=>s===l[0]&&r===l[1],i=e.findIndex(n);return i===-1?[...e,l]:e.filter((s,r)=>r!==i)}return[l]}function Gi(l,e){const t=l.map(n=>[...n]);return e.forEach(([n,i])=>{t[n]&&t[n][i]&&(t[n][i]={...t[n][i],value:""})}),t}function Qi(l,e,t){const[n,i]=l;return t&&e.length===1&&e[0][0]===n&&e[0][1]===i}function Zi(l,e,t){var _,u,a;const[n,i]=l,s=t?-1:1;if((_=e[n])!=null&&_[i+s])return[n,i+s];const r=n+(s>0?1:0),o=n+(s<0?-1:0);return s>0&&((u=e[r])!=null&&u[0])?[r,0]:s<0&&((a=e[o])!=null&&a[e[0].length-1])?[o,e[0].length-1]:!1}function xi(l,e,t){var _;const n=l.key,i={ArrowRight:[0,1],ArrowLeft:[0,-1],ArrowDown:[1,0],ArrowUp:[-1,0]}[n];let s,r;if(l.metaKey||l.ctrlKey)if(n==="ArrowRight")s=e[0],r=t[0].length-1;else if(n==="ArrowLeft")s=e[0],r=0;else if(n==="ArrowDown")s=t.length-1,r=e[1];else if(n==="ArrowUp")s=0,r=e[1];else return!1;else s=e[0]+i[0],r=e[1]+i[1];return s<0&&r<=0?!1:((_=t[s])==null?void 0:_[r])?[s,r]:!1}function cl(l,e){return e.reduce((t,n,i)=>{const s=n.reduce((r,o,_)=>l===o.id?_:r,-1);return s===-1?t:[i,s]},[-1,-1])}function $i(l,e){const[t]=l.composedPath();return!e.contains(t)}function er(l,e,t,n,i){var g,M;const[s,r]=l;if(!((g=e[s])!=null&&g[r]))return{col_pos:"0px",row_pos:void 0};const o=e[s][r].id,_=(M=t[o])==null?void 0:M.cell;if(!_)return{col_pos:"0px",row_pos:void 0};const u=_.getBoundingClientRect(),a=i.getBoundingClientRect(),f=`${u.left-a.left+u.width/2}px`,c=`${u.top-a.top+u.height/2}px`;return{col_pos:f,row_pos:c}}const tr=Symbol("dataframe");function lr(l,e){const t=r=>l.update(o=>({...o,...r(o)})),n=(r,o,_)=>{var f;const u=(f=r[0])!=null&&f.length?Array(r[0].length).fill(null).map(()=>({value:"",id:o()})):[{value:"",id:o()}],a=[...r];return _!==void 0?a.splice(_,0,u):a.push(u),a},i=(r,o,_,u)=>{const a=e.headers?[...o.map(c=>e.headers[o.indexOf(c)].value)]:[...o,`Header ${o.length+1}`],f=r.map(c=>[...c,{value:"",id:_()}]);return u!==void 0&&(a.splice(u,0,a.pop()),f.forEach(c=>c.splice(u,0,c.pop()))),{data:f,headers:a}},s=(r,o)=>{r&&o&&o.splice(0,o.length,...JSON.parse(JSON.stringify(r)))};return{handle_search:r=>t(o=>({current_search_query:r})),handle_sort:(r,o)=>t(_=>{const u=_.sort_state.sort_columns.filter(f=>f.col!==r);_.sort_state.sort_columns.some(f=>f.col===r&&f.direction===o)||u.push({col:r,direction:o});const a=_.sort_state.initial_data||(e.data&&u.length>0?{data:JSON.parse(JSON.stringify(e.data)),display_value:e.display_value?JSON.parse(JSON.stringify(e.display_value)):null,styling:e.styling?JSON.parse(JSON.stringify(e.styling)):null}:null);return{sort_state:{..._.sort_state,sort_columns:u.slice(-3),initial_data:a}}}),handle_filter:(r,o,_,u)=>t(a=>{const f=a.filter_state.filter_columns.some(g=>g.col===r)?a.filter_state.filter_columns.filter(g=>g.col!==r):[...a.filter_state.filter_columns,{col:r,datatype:o,filter:_,value:u}],c=a.filter_state.initial_data||(e.data&&f.length>0?{data:JSON.parse(JSON.stringify(e.data)),display_value:e.display_value?JSON.parse(JSON.stringify(e.display_value)):null,styling:e.styling?JSON.parse(JSON.stringify(e.styling)):null}:null);return{filter_state:{...a.filter_state,filter_columns:f,initial_data:c}}}),get_sort_status:(r,o)=>{const u=ze(l).sort_state.sort_columns.find(a=>o[a.col]===r);return u?u.direction:"none"},sort_data:(r,o,_)=>{const{sort_state:{sort_columns:u}}=ze(l);u.length&&fn(r,o,_,u)},update_row_order:r=>t(o=>({sort_state:{...o.sort_state,row_order:o.sort_state.sort_columns.length&&r[0]?[...Array(r.length)].map((_,u)=>u).sort((_,u)=>{var a,f,c,g;for(const{col:M,direction:w}of o.sort_state.sort_columns){const C=(((f=(a=r[_])==null?void 0:a[M])==null?void 0:f.value)??"")<(((g=(c=r[u])==null?void 0:c[M])==null?void 0:g.value)??"")?-1:1;if(C)return w==="asc"?C:-C}return 0}):[...Array(r.length)].map((_,u)=>u)}})),filter_data:r=>{var _;const o=(_=ze(l).current_search_query)==null?void 0:_.toLowerCase();return o?r.filter(u=>u.some(a=>String(a==null?void 0:a.value).toLowerCase().includes(o))):r},add_row:n,add_col:i,add_row_at:(r,o,_,u)=>n(r,u,_==="above"?o:o+1),add_col_at:(r,o,_,u,a)=>i(r,o,a,u==="left"?_:_+1),delete_row:(r,o)=>r.length>1?r.filter((_,u)=>u!==o):r,delete_col:(r,o,_)=>o.length>1?{data:r.map(u=>u.filter((a,f)=>f!==_)),headers:o.filter((u,a)=>a!==_)}:{data:r,headers:o},delete_row_at:(r,o)=>r.length>1?[...r.slice(0,o),...r.slice(o+1)]:r,delete_col_at:(r,o,_)=>o.length>1?{data:r.map(u=>[...u.slice(0,_),...u.slice(_+1)]),headers:[...o.slice(0,_),...o.slice(_+1)]}:{data:r,headers:o},trigger_change:async(r,o,_,u,a,f)=>{if(ze(l).current_search_query)return;const g=o.map(w=>w.value),M=r.map(w=>w.map(C=>String(C.value)));(!Vt(M,_)||!Vt(g,u))&&(Vt(g,u)||t(w=>({sort_state:{sort_columns:[],row_order:[],initial_data:null},filter_state:{filter_columns:[],initial_data:null}})),f("change",{data:r.map(w=>w.map(C=>C.value)),headers:g,metadata:null}),a||f("input"))},reset_sort_state:()=>t(r=>{if(r.sort_state.initial_data&&e.data){const o=r.sort_state.initial_data;s(o.data,e.data),s(o.display_value,e.display_value),s(o.styling,e.styling)}return{sort_state:{sort_columns:[],row_order:[],initial_data:null}}}),reset_filter_state:()=>t(r=>{if(r.filter_state.initial_data&&e.data){const o=r.filter_state.initial_data;s(o.data,e.data),s(o.display_value,e.display_value),s(o.styling,e.styling)}return{filter_state:{filter_columns:[],initial_data:null}}}),set_active_cell_menu:r=>t(o=>({ui_state:{...o.ui_state,active_cell_menu:r}})),set_active_header_menu:r=>t(o=>({ui_state:{...o.ui_state,active_header_menu:r}})),set_selected_cells:r=>t(o=>({ui_state:{...o.ui_state,selected_cells:r}})),set_selected:r=>t(o=>({ui_state:{...o.ui_state,selected:r}})),set_editing:r=>t(o=>({ui_state:{...o.ui_state,editing:r}})),clear_ui_state:()=>t(r=>({ui_state:{active_cell_menu:null,active_header_menu:null,selected_cells:[],selected:!1,editing:!1,header_edit:!1,selected_header:!1,active_button:null,copy_flash:!1}})),set_header_edit:r=>t(o=>({ui_state:{...o.ui_state,selected_cells:[],selected_header:r,header_edit:r}})),set_selected_header:r=>t(o=>({ui_state:{...o.ui_state,selected_header:r,selected:!1,selected_cells:[]}})),handle_header_click:(r,o)=>t(_=>({ui_state:{..._.ui_state,active_cell_menu:null,active_header_menu:null,selected:!1,selected_cells:[],selected_header:r,header_edit:o?r:!1}})),end_header_edit:r=>{["Escape","Enter","Tab"].includes(r)&&t(o=>({ui_state:{...o.ui_state,selected:!1,header_edit:!1}}))},get_selected_cells:()=>ze(l).ui_state.selected_cells,get_active_cell_menu:()=>ze(l).ui_state.active_cell_menu,get_active_button:()=>ze(l).ui_state.active_button,set_active_button:r=>t(o=>({ui_state:{...o.ui_state,active_button:r}})),set_copy_flash:r=>t(o=>({ui_state:{...o.ui_state,copy_flash:r}})),handle_cell_click:(r,o,_)=>{var c;r.preventDefault(),r.stopPropagation();const u=ze(l);if(u.config.show_row_numbers&&_===-1)return;let a=o;if(u.current_search_query&&e.data){const g=[];e.data.forEach((M,w)=>{M.some(C=>{var p;return String(C==null?void 0:C.value).toLowerCase().includes(((p=u.current_search_query)==null?void 0:p.toLowerCase())||"")})&&g.push(w)}),a=g[o]??o}const f=Xi([a,_],u.ui_state.selected_cells,r);t(g=>({ui_state:{...g.ui_state,active_cell_menu:null,active_header_menu:null,selected_header:!1,header_edit:!1,selected_cells:f,selected:f[0]}})),u.config.editable&&f.length===1?(t(g=>({ui_state:{...g.ui_state,editing:[a,_]}})),rt().then(()=>{var g,M;return(M=(g=e.els[e.data[a][_].id])==null?void 0:g.input)==null?void 0:M.focus()})):rt().then(()=>{e.parent_element&&e.parent_element.focus()}),(c=e.dispatch)==null||c.call(e,"select",{index:[a,_],col_value:e.get_column(_),row_value:e.get_row(a),value:e.get_data_at(a,_)})},toggle_cell_menu:(r,o,_)=>{r.stopPropagation();const u=ze(l).ui_state.active_cell_menu;if((u==null?void 0:u.row)===o&&u.col===_)t(a=>({ui_state:{...a.ui_state,active_cell_menu:null}}));else{const a=r.target.closest("td");if(a){const f=a.getBoundingClientRect();t(c=>({ui_state:{...c.ui_state,active_cell_menu:{row:o,col:_,x:f.right,y:f.bottom}}}))}}},toggle_cell_button:(r,o)=>{const _=ze(l).ui_state.active_button,u=(_==null?void 0:_.type)==="cell"&&_.row===r&&_.col===o?null:{type:"cell",row:r,col:o};t(a=>({ui_state:{...a.ui_state,active_button:u}}))},handle_select_column:r=>{if(!e.data)return;const o=e.data.map((_,u)=>[u,r]);t(_=>({ui_state:{..._.ui_state,selected_cells:o,selected:o[0],editing:!1}})),setTimeout(()=>{var _;return(_=e.parent_element)==null?void 0:_.focus()},0)},handle_select_row:r=>{if(!e.data||!e.data[0])return;const o=e.data[0].map((_,u)=>[r,u]);t(_=>({ui_state:{..._.ui_state,selected_cells:o,selected:o[0],editing:!1}})),setTimeout(()=>{var _;return(_=e.parent_element)==null?void 0:_.focus()},0)},get_next_cell_coordinates:Zi,get_range_selection:Ft,move_cursor:xi}}function nr(l){const e=Ci({config:l,current_search_query:null,sort_state:{sort_columns:[],row_order:[],initial_data:null},filter_state:{filter_columns:[],initial_data:null},ui_state:{active_cell_menu:null,active_header_menu:null,selected_cells:[],selected:!1,editing:!1,header_edit:!1,selected_header:!1,active_button:null,copy_flash:!1}}),t={state:e,actions:null};t.actions=lr(e,t);const n=Symbol(`dataframe_${Math.random().toString(36).substring(2)}`);return sl(n,t),sl(tr,{instance_id:n,context:t}),t}function ir(l){let e,t,n,i,s,r,o,_,u;return{c(){e=P("button"),t=P("span"),n=ne("svg"),i=ne("path"),this.h()},l(a){e=U(a,"BUTTON",{class:!0,"aria-label":!0});var f=S(e);t=U(f,"SPAN",{class:!0});var c=S(t);n=ie(c,"svg",{xmlns:!0,viewBox:!0,class:!0});var g=S(n);i=ie(g,"path",{d:!0,"data-name":!0}),S(i).forEach(d),g.forEach(d),c.forEach(d),f.forEach(d),this.h()},h(){h(i,"d","m16.707 13.293-4-4a1 1 0 0 0-1.414 0l-4 4A1 1 0 0 0 8 15h8a1 1 0 0 0 .707-1.707z"),h(i,"data-name",l[3]),h(n,"xmlns","http://www.w3.org/2000/svg"),h(n,"viewBox","0 0 24 24"),h(n,"class","svelte-1mp8yw1"),h(t,"class",s=ol(l[3])+" svelte-1mp8yw1"),h(e,"class",r="selection-button selection-button-"+l[0]+" "+(l[2]?`move-${l[3]}`:"")+" svelte-1mp8yw1"),h(e,"aria-label",o=`Select ${l[0]}`)},m(a,f){q(a,e,f),N(e,t),N(t,n),N(n,i),_||(u=ce(e,"click",xe(l[5])),_=!0)},p(a,[f]){f&8&&h(i,"data-name",a[3]),f&8&&s!==(s=ol(a[3])+" svelte-1mp8yw1")&&h(t,"class",s),f&13&&r!==(r="selection-button selection-button-"+a[0]+" "+(a[2]?`move-${a[3]}`:"")+" svelte-1mp8yw1")&&h(e,"class",r),f&1&&o!==(o=`Select ${a[0]}`)&&h(e,"aria-label",o)},i:Ce,o:Ce,d(a){a&&d(e),_=!1,u()}}}function rr(l,e,t){let n,i,{position:s}=e,{coords:r}=e,{on_click:o=null}=e;const _=()=>o&&o();return l.$$set=u=>{"position"in u&&t(0,s=u.position),"coords"in u&&t(4,r=u.coords),"on_click"in u&&t(1,o=u.on_click)},l.$$.update=()=>{l.$$.dirty&17&&t(2,n=s==="column"?r[0]===0:r[1]===0),l.$$.dirty&5&&t(3,i=s==="column"?n?"down":"up":n?"right":"left")},[s,o,n,i,r,_]}class hl extends qe{constructor(e){super(),He(this,e,rr,ir,Ve,{position:0,coords:4,on_click:1})}}function sr(l){let e,t,n,i;function s(o){l[5](o)}let r={label:"",interactive:l[0]};return l[1]!==void 0&&(r.value=l[1]),t=new Oi({props:r}),Ee.push(()=>Ie(t,"value",s)),t.$on("change",l[2]),{c(){e=P("div"),re(t.$$.fragment),this.h()},l(o){e=U(o,"DIV",{class:!0,role:!0,tabindex:!0});var _=S(e);_e(t.$$.fragment,_),_.forEach(d),this.h()},h(){h(e,"class","bool-cell svelte-1svo4lb"),h(e,"role","button"),h(e,"tabindex","-1")},m(o,_){q(o,e,_),se(t,e,null),i=!0},p(o,[_]){const u={};_&1&&(u.interactive=o[0]),!n&&_&2&&(n=!0,u.value=o[1],Oe(()=>n=!1)),t.$set(u)},i(o){i||(E(t.$$.fragment,o),i=!0)},o(o){A(t.$$.fragment,o),i=!1},d(o){o&&d(e),oe(t)}}}function or(l,e,t){let n,{value:i=!1}=e,{editable:s=!0}=e,{on_change:r}=e;function o(u){s&&r(u.detail)}function _(u){n=u,t(1,n),t(3,i)}return l.$$set=u=>{"value"in u&&t(3,i=u.value),"editable"in u&&t(0,s=u.editable),"on_change"in u&&t(4,r=u.on_change)},l.$$.update=()=>{l.$$.dirty&8&&t(1,n=typeof i=="string"?i.toLowerCase()==="true":!!i)},[s,n,o,i,r,_]}class ar extends qe{constructor(e){super(),He(this,e,or,sr,Ve,{value:3,editable:0,on_change:4})}}function dl(l){let e,t,n,i;return{c(){e=P("textarea"),this.h()},l(s){e=U(s,"TEXTAREA",{"aria-readonly":!0,"aria-label":!0,tabindex:!0,class:!0}),S(e).forEach(d),this.h()},h(){e.readOnly=l[9],h(e,"aria-readonly",l[9]),h(e,"aria-label",t=l[9]?"Cell is read-only":"Edit cell"),h(e,"tabindex","-1"),h(e,"class","svelte-fvkwu"),Y(e,"header",l[4])},m(s,r){q(s,e,r),l[29](e),al(e,l[0]),n||(i=[ce(e,"input",l[30]),ce(e,"blur",l[21]),ce(e,"mousedown",xe(l[27])),ce(e,"click",xe(l[28])),hi(br.call(null,e)),ce(e,"keydown",l[22])],n=!0)},p(s,r){r[0]&512&&(e.readOnly=s[9]),r[0]&512&&h(e,"aria-readonly",s[9]),r[0]&512&&t!==(t=s[9]?"Cell is read-only":"Edit cell")&&h(e,"aria-label",t),r[0]&1&&al(e,s[0]),r[0]&16&&Y(e,"header",s[4])},d(s){s&&d(e),l[29](null),n=!1,Qe(i)}}}function ur(l){let e,t,n,i,s,r;const o=[dr,hr,cr,fr],_=[];function u(a,f){return a[5]==="image"&&a[11].image?0:a[5]==="html"?1:a[5]==="markdown"?2:3}return t=u(l),n=_[t]=o[t](l),{c(){e=P("span"),n.c(),this.h()},l(a){e=U(a,"SPAN",{tabindex:!0,role:!0,style:!0,"data-editable":!0,"data-max-chars":!0,"data-expanded":!0,placeholder:!0,class:!0});var f=S(e);n.l(f),f.forEach(d),this.h()},h(){h(e,"tabindex","0"),h(e,"role","button"),h(e,"style",l[3]),h(e,"data-editable",l[8]),h(e,"data-max-chars",l[10]),h(e,"data-expanded",l[2]),h(e,"placeholder"," "),h(e,"class","svelte-fvkwu"),Y(e,"dragging",l[13]),Y(e,"edit",l[2]),Y(e,"expanded",l[2]),Y(e,"multiline",l[4]),Y(e,"text",l[5]==="str"),Y(e,"wrap",l[14])},m(a,f){q(a,e,f),_[t].m(e,null),i=!0,s||(r=[ce(e,"keydown",l[22]),ce(e,"focus",_n(l[26]))],s=!0)},p(a,f){let c=t;t=u(a),t===c?_[t].p(a,f):(me(),A(_[c],1,1,()=>{_[c]=null}),ge(),n=_[t],n?n.p(a,f):(n=_[t]=o[t](a),n.c()),E(n,1),n.m(e,null)),(!i||f[0]&8)&&h(e,"style",a[3]),(!i||f[0]&256)&&h(e,"data-editable",a[8]),(!i||f[0]&1024)&&h(e,"data-max-chars",a[10]),(!i||f[0]&4)&&h(e,"data-expanded",a[2]),(!i||f[0]&8192)&&Y(e,"dragging",a[13]),(!i||f[0]&4)&&Y(e,"edit",a[2]),(!i||f[0]&4)&&Y(e,"expanded",a[2]),(!i||f[0]&16)&&Y(e,"multiline",a[4]),(!i||f[0]&32)&&Y(e,"text",a[5]==="str"),(!i||f[0]&16384)&&Y(e,"wrap",a[14])},i(a){i||(E(n),i=!0)},o(a){A(n),i=!1},d(a){a&&d(e),_[t].d(),s=!1,Qe(r)}}}function _r(l){let e,t;return e=new ar({props:{value:String(l[19]),editable:l[8],on_change:l[23]}}),{c(){re(e.$$.fragment)},l(n){_e(e.$$.fragment,n)},m(n,i){se(e,n,i),t=!0},p(n,i){const s={};i[0]&524288&&(s.value=String(n[19])),i[0]&256&&(s.editable=n[8]),e.$set(s)},i(n){t||(E(e.$$.fragment,n),t=!0)},o(n){A(e.$$.fragment,n),t=!1},d(n){oe(e,n)}}}function fr(l){let e;return{c(){e=Ae(l[20])},l(t){e=Be(t,l[20])},m(t,n){q(t,e,n)},p(t,n){n[0]&1048576&&Me(e,t[20])},i:Ce,o:Ce,d(t){t&&d(e)}}}function cr(l){let e,t;return e=new Si({props:{message:l[20].toLocaleString(),latex_delimiters:l[6],line_breaks:l[7],chatbot:!1}}),{c(){re(e.$$.fragment)},l(n){_e(e.$$.fragment,n)},m(n,i){se(e,n,i),t=!0},p(n,i){const s={};i[0]&1048576&&(s.message=n[20].toLocaleString()),i[0]&64&&(s.latex_delimiters=n[6]),i[0]&128&&(s.line_breaks=n[7]),e.$set(s)},i(n){t||(E(e.$$.fragment,n),t=!0)},o(n){A(e.$$.fragment,n),t=!1},d(n){oe(e,n)}}}function hr(l){let e,t;return{c(){e=new di(!1),t=ke(),this.h()},l(n){e=mi(n,!1),t=ke(),this.h()},h(){e.a=t},m(n,i){e.m(l[20],n,i),q(n,t,i)},p(n,i){i[0]&1048576&&e.p(n[20])},i:Ce,o:Ce,d(n){n&&(d(t),e.d())}}}function dr(l){let e,t,n;var i=l[11].image;function s(r,o){return{props:{value:{url:r[20]},show_label:!1,label:"cell-image",show_download_button:!1,i18n:r[12],gradio:{dispatch:wr}}}}return i&&(e=ul(i,s(l))),{c(){e&&re(e.$$.fragment),t=ke()},l(r){e&&_e(e.$$.fragment,r),t=ke()},m(r,o){e&&se(e,r,o),q(r,t,o),n=!0},p(r,o){if(o[0]&2048&&i!==(i=r[11].image)){if(e){me();const _=e;A(_.$$.fragment,1,0,()=>{oe(_,1)}),ge()}i?(e=ul(i,s(r)),re(e.$$.fragment),E(e.$$.fragment,1),se(e,t.parentNode,t)):e=null}else if(i){const _={};o[0]&1048576&&(_.value={url:r[20]}),o[0]&4096&&(_.i18n=r[12]),e.$set(_)}},i(r){n||(e&&E(e.$$.fragment,r),n=!0)},o(r){e&&A(e.$$.fragment,r),n=!1},d(r){r&&d(t),e&&oe(e,r)}}}function ml(l){let e,t,n,i;return e=new hl({props:{position:"column",coords:l[16],on_click:l[31]}}),n=new hl({props:{position:"row",coords:l[16],on_click:l[32]}}),{c(){re(e.$$.fragment),t=X(),re(n.$$.fragment)},l(s){_e(e.$$.fragment,s),t=G(s),_e(n.$$.fragment,s)},m(s,r){se(e,s,r),q(s,t,r),se(n,s,r),i=!0},p(s,r){const o={};r[0]&65536&&(o.coords=s[16]),r[0]&196608&&(o.on_click=s[31]),e.$set(o);const _={};r[0]&65536&&(_.coords=s[16]),r[0]&327680&&(_.on_click=s[32]),n.$set(_)},i(s){i||(E(e.$$.fragment,s),E(n.$$.fragment,s),i=!0)},o(s){A(e.$$.fragment,s),A(n.$$.fragment,s),i=!1},d(s){s&&d(t),oe(e,s),oe(n,s)}}}function mr(l){let e,t,n,i,s,r,o=l[2]&&l[5]!=="bool"&&dl(l);const _=[_r,ur],u=[];function a(c,g){return c[5]==="bool"?0:1}t=a(l),n=u[t]=_[t](l);let f=l[15]&&l[16]&&l[17]&&l[18]&&ml(l);return{c(){o&&o.c(),e=X(),n.c(),i=X(),f&&f.c(),s=ke()},l(c){o&&o.l(c),e=G(c),n.l(c),i=G(c),f&&f.l(c),s=ke()},m(c,g){o&&o.m(c,g),q(c,e,g),u[t].m(c,g),q(c,i,g),f&&f.m(c,g),q(c,s,g),r=!0},p(c,g){c[2]&&c[5]!=="bool"?o?o.p(c,g):(o=dl(c),o.c(),o.m(e.parentNode,e)):o&&(o.d(1),o=null);let M=t;t=a(c),t===M?u[t].p(c,g):(me(),A(u[M],1,1,()=>{u[M]=null}),ge(),n=u[t],n?n.p(c,g):(n=u[t]=_[t](c),n.c()),E(n,1),n.m(i.parentNode,i)),c[15]&&c[16]&&c[17]&&c[18]?f?(f.p(c,g),g[0]&491520&&E(f,1)):(f=ml(c),f.c(),E(f,1),f.m(s.parentNode,s)):f&&(me(),A(f,1,1,()=>{f=null}),ge())},i(c){r||(E(n),E(f),r=!0)},o(c){A(n),A(f),r=!1},d(c){c&&(d(e),d(i),d(s)),o&&o.d(c),u[t].d(c),f&&f.d(c)}}}function gr(l,e=null,t=!1){if(t)return String(l);const n=String(l);return!e||e<=0||n.length<=e?n:n.slice(0,e)+"..."}function br(l){return requestAnimationFrame(()=>{l.focus()}),{}}const wr=()=>{};function kr(l,e,t){let n,i,s,{edit:r}=e,{value:o=""}=e,{display_value:_=null}=e,{styling:u=""}=e,{header:a=!1}=e,{datatype:f="str"}=e,{latex_delimiters:c}=e,{line_breaks:g=!0}=e,{editable:M=!0}=e,{is_static:w=!1}=e,{max_chars:C=null}=e,{components:p={}}=e,{i18n:y}=e,{is_dragging:k=!1}=e,{wrap_text:T=!1}=e,{show_selection_buttons:O=!1}=e,{coords:L}=e,{on_select_column:B=null}=e,{on_select_row:z=null}=e,{el:F}=e;const ae=pt();function R(D){ae("blur",{blur_event:D,coords:L})}function Q(D){ae("keydown",D)}function K(D){t(0,o=D.toString()),ae("blur",{blur_event:{target:{type:"checkbox",checked:D,value:D.toString()}},coords:L})}function te(D){it.call(this,l,D)}function fe(D){it.call(this,l,D)}function $(D){it.call(this,l,D)}function ue(D){Ee[D?"unshift":"push"](()=>{F=D,t(1,F)})}function ye(){o=this.value,t(0,o)}const V=()=>B(L[1]),he=()=>z(L[0]);return l.$$set=D=>{"edit"in D&&t(2,r=D.edit),"value"in D&&t(0,o=D.value),"display_value"in D&&t(24,_=D.display_value),"styling"in D&&t(3,u=D.styling),"header"in D&&t(4,a=D.header),"datatype"in D&&t(5,f=D.datatype),"latex_delimiters"in D&&t(6,c=D.latex_delimiters),"line_breaks"in D&&t(7,g=D.line_breaks),"editable"in D&&t(8,M=D.editable),"is_static"in D&&t(9,w=D.is_static),"max_chars"in D&&t(10,C=D.max_chars),"components"in D&&t(11,p=D.components),"i18n"in D&&t(12,y=D.i18n),"is_dragging"in D&&t(13,k=D.is_dragging),"wrap_text"in D&&t(14,T=D.wrap_text),"show_selection_buttons"in D&&t(15,O=D.show_selection_buttons),"coords"in D&&t(16,L=D.coords),"on_select_column"in D&&t(17,B=D.on_select_column),"on_select_row"in D&&t(18,z=D.on_select_row),"el"in D&&t(1,F=D.el)},l.$$.update=()=>{l.$$.dirty[0]&1028&&t(25,n=!r&&C!==null&&C>0),l.$$.dirty[0]&16777473&&t(19,i=M?o:_!==null?_:o),l.$$.dirty[0]&34079776&&t(20,s=n?gr(i,C,f==="image"):i)},[o,F,r,u,a,f,c,g,M,w,C,p,y,k,T,O,L,B,z,i,s,R,Q,K,_,n,te,fe,$,ue,ye,V,he]}class Kt extends qe{constructor(e){super(),He(this,e,kr,mr,Ve,{edit:2,value:0,display_value:24,styling:3,header:4,datatype:5,latex_delimiters:6,line_breaks:7,editable:8,is_static:9,max_chars:10,components:11,i18n:12,is_dragging:13,wrap_text:14,show_selection_buttons:15,coords:16,on_select_column:17,on_select_row:18,el:1},null,[-1,-1])}}function vr(l){let e,t=(l[0]!==null?l[0]+1:"")+"",n;return{c(){e=P("td"),n=Ae(t),this.h()},l(i){e=U(i,"TD",{class:!0,tabindex:!0,"data-row":!0,"data-col":!0});var s=S(e);n=Be(s,t),s.forEach(d),this.h()},h(){h(e,"class","row-number svelte-ux4in1"),h(e,"tabindex","-1"),h(e,"data-row",l[0]),h(e,"data-col","row-number")},m(i,s){q(i,e,s),N(e,n)},p(i,s){s&1&&t!==(t=(i[0]!==null?i[0]+1:"")+"")&&Me(n,t),s&1&&h(e,"data-row",i[0])},d(i){i&&d(e)}}}function pr(l){let e,t='<div class="cell-wrap"><div class="header-content"><div class="header-text"></div></div></div>';return{c(){e=P("th"),e.innerHTML=t,this.h()},l(n){e=U(n,"TH",{tabindex:!0,class:!0,"data-svelte-h":!0}),lt(e)!=="svelte-1aj56zf"&&(e.innerHTML=t),this.h()},h(){h(e,"tabindex","-1"),h(e,"class","row-number svelte-ux4in1")},m(n,i){q(n,e,i)},p:Ce,d(n){n&&d(e)}}}function yr(l){let e;function t(s,r){return s[1]?pr:vr}let n=t(l),i=n(l);return{c(){i.c(),e=ke()},l(s){i.l(s),e=ke()},m(s,r){i.m(s,r),q(s,e,r)},p(s,[r]){n===(n=t(s))&&i?i.p(s,r):(i.d(1),i=n(s),i&&(i.c(),i.m(e.parentNode,e)))},i:Ce,o:Ce,d(s){s&&d(e),i.d(s)}}}function Er(l,e,t){let{index:n=null}=e,{is_header:i=!1}=e;return l.$$set=s=>{"index"in s&&t(0,n=s.index),"is_header"in s&&t(1,i=s.is_header)},[n,i]}class yt extends qe{constructor(e){super(),He(this,e,Er,yr,Ve,{index:0,is_header:1})}}function Nr(l){let e,t="⋮",n,i;return{c(){e=P("button"),e.textContent=t,this.h()},l(s){e=U(s,"BUTTON",{"aria-label":!0,class:!0,"aria-haspopup":!0,"data-svelte-h":!0}),lt(e)!=="svelte-qulk5p"&&(e.textContent=t),this.h()},h(){h(e,"aria-label","Open cell menu"),h(e,"class","cell-menu-button svelte-vt38nd"),h(e,"aria-haspopup","menu")},m(s,r){q(s,e,r),n||(i=[ce(e,"click",function(){Xe(l[0])&&l[0].apply(this,arguments)}),ce(e,"touchstart",l[1])],n=!0)},p(s,[r]){l=s},i:Ce,o:Ce,d(s){s&&d(e),n=!1,Qe(i)}}}function Tr(l,e,t){let{on_click:n}=e;const i=s=>{s.preventDefault();const r=s.touches[0],o=new MouseEvent("click",{clientX:r.clientX,clientY:r.clientY,bubbles:!0,cancelable:!0,view:window});n(o)};return l.$$set=s=>{"on_click"in s&&t(0,n=s.on_click)},[n,i]}class hn extends qe{constructor(e){super(),He(this,e,Tr,Nr,Ve,{on_click:0})}}function Cr(l){let e,t='<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg>';return{c(){e=P("div"),e.innerHTML=t,this.h()},l(n){e=U(n,"DIV",{class:!0,"aria-label":!0,"data-svelte-h":!0}),lt(e)!=="svelte-1ernod4"&&(e.innerHTML=t),this.h()},h(){h(e,"class","wrapper svelte-1skchaw"),h(e,"aria-label","Static column")},m(n,i){q(n,e,i)},p:Ce,i:Ce,o:Ce,d(n){n&&d(e)}}}class Sr extends qe{constructor(e){super(),He(this,e,null,Cr,Ve,{})}}function Ar(l){let e,t,n;return{c(){e=ne("svg"),t=ne("path"),n=ne("path"),this.h()},l(i){e=ie(i,"svg",{width:!0,height:!0,viewBox:!0,fill:!0,xmlns:!0});var s=S(e);t=ie(s,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0}),S(t).forEach(d),n=ie(s,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),S(n).forEach(d),s.forEach(d),this.h()},h(){h(t,"d","M4 8L8 4L12 8"),h(t,"stroke","currentColor"),h(t,"stroke-width","1.5"),h(t,"stroke-linecap","round"),h(t,"stroke-linejoin","round"),h(n,"d","M8 4V12"),h(n,"stroke","currentColor"),h(n,"stroke-width","1.5"),h(n,"stroke-linecap","round"),h(e,"width",l[0]),h(e,"height",l[0]),h(e,"viewBox","0 0 16 16"),h(e,"fill","none"),h(e,"xmlns","http://www.w3.org/2000/svg")},m(i,s){q(i,e,s),N(e,t),N(e,n)},p(i,[s]){s&1&&h(e,"width",i[0]),s&1&&h(e,"height",i[0])},i:Ce,o:Ce,d(i){i&&d(e)}}}function Br(l,e,t){let{size:n=16}=e;return l.$$set=i=>{"size"in i&&t(0,n=i.size)},[n]}class Mr extends qe{constructor(e){super(),He(this,e,Br,Ar,Ve,{size:0})}}function Dr(l){let e,t,n;return{c(){e=ne("svg"),t=ne("path"),n=ne("path"),this.h()},l(i){e=ie(i,"svg",{width:!0,height:!0,viewBox:!0,fill:!0,xmlns:!0});var s=S(e);t=ie(s,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0}),S(t).forEach(d),n=ie(s,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),S(n).forEach(d),s.forEach(d),this.h()},h(){h(t,"d","M4 8L8 12L12 8"),h(t,"stroke","currentColor"),h(t,"stroke-width","1.5"),h(t,"stroke-linecap","round"),h(t,"stroke-linejoin","round"),h(n,"d","M8 12V4"),h(n,"stroke","currentColor"),h(n,"stroke-width","1.5"),h(n,"stroke-linecap","round"),h(e,"width",l[0]),h(e,"height",l[0]),h(e,"viewBox","0 0 16 16"),h(e,"fill","none"),h(e,"xmlns","http://www.w3.org/2000/svg")},m(i,s){q(i,e,s),N(e,t),N(e,n)},p(i,[s]){s&1&&h(e,"width",i[0]),s&1&&h(e,"height",i[0])},i:Ce,o:Ce,d(i){i&&d(e)}}}function Ir(l,e,t){let{size:n=16}=e;return l.$$set=i=>{"size"in i&&t(0,n=i.size)},[n]}class Or extends qe{constructor(e){super(),He(this,e,Ir,Dr,Ve,{size:0})}}function Lr(l){let e,t,n,i,s;return{c(){e=ne("svg"),t=ne("path"),n=ne("path"),i=ne("path"),s=ne("path"),this.h()},l(r){e=ie(r,"svg",{viewBox:!0,width:!0,height:!0});var o=S(e);t=ie(o,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),S(t).forEach(d),n=ie(o,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),S(n).forEach(d),i=ie(o,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),S(i).forEach(d),s=ie(o,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),S(s).forEach(d),o.forEach(d),this.h()},h(){h(t,"d","M5 5H19"),h(t,"stroke","currentColor"),h(t,"stroke-width","2"),h(t,"stroke-linecap","round"),h(n,"d","M8 9H16"),h(n,"stroke","currentColor"),h(n,"stroke-width","2"),h(n,"stroke-linecap","round"),h(i,"d","M11 13H13"),h(i,"stroke","currentColor"),h(i,"stroke-width","2"),h(i,"stroke-linecap","round"),h(s,"d","M17 17L21 21M21 17L17 21"),h(s,"stroke","currentColor"),h(s,"stroke-width","2"),h(s,"stroke-linecap","round"),h(e,"viewBox","0 0 24 24"),h(e,"width","16"),h(e,"height","16")},m(r,o){q(r,e,o),N(e,t),N(e,n),N(e,i),N(e,s)},d(r){r&&d(e)}}}function jr(l){let e,t,n,i;return{c(){e=ne("svg"),t=ne("path"),n=ne("path"),i=ne("path"),this.h()},l(s){e=ie(s,"svg",{viewBox:!0,width:!0,height:!0});var r=S(e);t=ie(r,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),S(t).forEach(d),n=ie(r,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),S(n).forEach(d),i=ie(r,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),S(i).forEach(d),r.forEach(d),this.h()},h(){h(t,"d","M5 5H19"),h(t,"stroke","currentColor"),h(t,"stroke-width","2"),h(t,"stroke-linecap","round"),h(n,"d","M8 9H16"),h(n,"stroke","currentColor"),h(n,"stroke-width","2"),h(n,"stroke-linecap","round"),h(i,"d","M11 13H13"),h(i,"stroke","currentColor"),h(i,"stroke-width","2"),h(i,"stroke-linecap","round"),h(e,"viewBox","0 0 24 24"),h(e,"width","16"),h(e,"height","16")},m(s,r){q(s,e,r),N(e,t),N(e,n),N(e,i)},d(s){s&&d(e)}}}function qr(l){let e,t,n,i,s,r;return{c(){e=ne("svg"),t=ne("path"),n=ne("path"),i=ne("path"),s=ne("path"),r=ne("path"),this.h()},l(o){e=ie(o,"svg",{viewBox:!0,width:!0,height:!0});var _=S(e);t=ie(_,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),S(t).forEach(d),n=ie(_,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),S(n).forEach(d),i=ie(_,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),S(i).forEach(d),s=ie(_,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),S(s).forEach(d),r=ie(_,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),S(r).forEach(d),_.forEach(d),this.h()},h(){h(t,"d","M5 5H19"),h(t,"stroke","currentColor"),h(t,"stroke-width","2"),h(t,"stroke-linecap","round"),h(n,"d","M5 9H15"),h(n,"stroke","currentColor"),h(n,"stroke-width","2"),h(n,"stroke-linecap","round"),h(i,"d","M5 13H11"),h(i,"stroke","currentColor"),h(i,"stroke-width","2"),h(i,"stroke-linecap","round"),h(s,"d","M5 17H7"),h(s,"stroke","currentColor"),h(s,"stroke-width","2"),h(s,"stroke-linecap","round"),h(r,"d","M17 17L21 21M21 17L17 21"),h(r,"stroke","currentColor"),h(r,"stroke-width","2"),h(r,"stroke-linecap","round"),h(e,"viewBox","0 0 24 24"),h(e,"width","16"),h(e,"height","16")},m(o,_){q(o,e,_),N(e,t),N(e,n),N(e,i),N(e,s),N(e,r)},d(o){o&&d(e)}}}function Hr(l){let e,t,n,i;return{c(){e=ne("svg"),t=ne("path"),n=ne("path"),i=ne("path"),this.h()},l(s){e=ie(s,"svg",{viewBox:!0,width:!0,height:!0});var r=S(e);t=ie(r,"path",{d:!0,stroke:!0,"stroke-width":!0,fill:!0,"stroke-linecap":!0,"stroke-linejoin":!0}),S(t).forEach(d),n=ie(r,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),S(n).forEach(d),i=ie(r,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),S(i).forEach(d),r.forEach(d),this.h()},h(){h(t,"d","M8 12L12 16L16 12"),h(t,"stroke","currentColor"),h(t,"stroke-width","2"),h(t,"fill","none"),h(t,"stroke-linecap","round"),h(t,"stroke-linejoin","round"),h(n,"d","M12 16V9"),h(n,"stroke","currentColor"),h(n,"stroke-width","2"),h(n,"stroke-linecap","round"),h(i,"d","M5 5H19"),h(i,"stroke","currentColor"),h(i,"stroke-width","2"),h(i,"stroke-linecap","round"),h(e,"viewBox","0 0 24 24"),h(e,"width","16"),h(e,"height","16")},m(s,r){q(s,e,r),N(e,t),N(e,n),N(e,i)},d(s){s&&d(e)}}}function Vr(l){let e,t,n,i;return{c(){e=ne("svg"),t=ne("path"),n=ne("path"),i=ne("path"),this.h()},l(s){e=ie(s,"svg",{viewBox:!0,width:!0,height:!0});var r=S(e);t=ie(r,"path",{d:!0,stroke:!0,"stroke-width":!0,fill:!0,"stroke-linecap":!0,"stroke-linejoin":!0}),S(t).forEach(d),n=ie(r,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),S(n).forEach(d),i=ie(r,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),S(i).forEach(d),r.forEach(d),this.h()},h(){h(t,"d","M8 16L12 12L16 16"),h(t,"stroke","currentColor"),h(t,"stroke-width","2"),h(t,"fill","none"),h(t,"stroke-linecap","round"),h(t,"stroke-linejoin","round"),h(n,"d","M12 12V19"),h(n,"stroke","currentColor"),h(n,"stroke-width","2"),h(n,"stroke-linecap","round"),h(i,"d","M5 7H19"),h(i,"stroke","currentColor"),h(i,"stroke-width","2"),h(i,"stroke-linecap","round"),h(e,"viewBox","0 0 24 24"),h(e,"width","16"),h(e,"height","16")},m(s,r){q(s,e,r),N(e,t),N(e,n),N(e,i)},d(s){s&&d(e)}}}function zr(l){let e,t,n;return{c(){e=ne("svg"),t=ne("rect"),n=ne("path"),this.h()},l(i){e=ie(i,"svg",{viewBox:!0,width:!0,height:!0});var s=S(e);t=ie(s,"rect",{x:!0,y:!0,width:!0,height:!0,stroke:!0,"stroke-width":!0}),S(t).forEach(d),n=ie(s,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),S(n).forEach(d),s.forEach(d),this.h()},h(){h(t,"x","10"),h(t,"y","5"),h(t,"width","4"),h(t,"height","14"),h(t,"stroke","currentColor"),h(t,"stroke-width","2"),h(n,"d","M7 8L17 16M17 8L7 16"),h(n,"stroke","currentColor"),h(n,"stroke-width","2"),h(n,"stroke-linecap","round"),h(e,"viewBox","0 0 24 24"),h(e,"width","16"),h(e,"height","16")},m(i,s){q(i,e,s),N(e,t),N(e,n)},d(i){i&&d(e)}}}function Pr(l){let e,t,n;return{c(){e=ne("svg"),t=ne("rect"),n=ne("path"),this.h()},l(i){e=ie(i,"svg",{viewBox:!0,width:!0,height:!0});var s=S(e);t=ie(s,"rect",{x:!0,y:!0,width:!0,height:!0,stroke:!0,"stroke-width":!0}),S(t).forEach(d),n=ie(s,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0}),S(n).forEach(d),s.forEach(d),this.h()},h(){h(t,"x","5"),h(t,"y","10"),h(t,"width","14"),h(t,"height","4"),h(t,"stroke","currentColor"),h(t,"stroke-width","2"),h(n,"d","M8 7L16 17M16 7L8 17"),h(n,"stroke","currentColor"),h(n,"stroke-width","2"),h(n,"stroke-linecap","round"),h(e,"viewBox","0 0 24 24"),h(e,"width","16"),h(e,"height","16")},m(i,s){q(i,e,s),N(e,t),N(e,n)},d(i){i&&d(e)}}}function Ur(l){let e,t,n;return{c(){e=ne("svg"),t=ne("rect"),n=ne("path"),this.h()},l(i){e=ie(i,"svg",{viewBox:!0,width:!0,height:!0});var s=S(e);t=ie(s,"rect",{x:!0,y:!0,width:!0,height:!0,stroke:!0,"stroke-width":!0}),S(t).forEach(d),n=ie(s,"path",{d:!0,stroke:!0,"stroke-width":!0,fill:!0,"stroke-linecap":!0}),S(n).forEach(d),s.forEach(d),this.h()},h(){h(t,"x","6"),h(t,"y","4"),h(t,"width","12"),h(t,"height","4"),h(t,"stroke","currentColor"),h(t,"stroke-width","2"),h(n,"d","M12 12V19M8 16L12 19L16 16"),h(n,"stroke","currentColor"),h(n,"stroke-width","2"),h(n,"fill","none"),h(n,"stroke-linecap","round"),h(e,"viewBox","0 0 24 24"),h(e,"width","16"),h(e,"height","16")},m(i,s){q(i,e,s),N(e,t),N(e,n)},d(i){i&&d(e)}}}function Rr(l){let e,t,n;return{c(){e=ne("svg"),t=ne("rect"),n=ne("path"),this.h()},l(i){e=ie(i,"svg",{viewBox:!0,width:!0,height:!0});var s=S(e);t=ie(s,"rect",{x:!0,y:!0,width:!0,height:!0,stroke:!0,"stroke-width":!0}),S(t).forEach(d),n=ie(s,"path",{d:!0,stroke:!0,"stroke-width":!0,fill:!0,"stroke-linecap":!0}),S(n).forEach(d),s.forEach(d),this.h()},h(){h(t,"x","6"),h(t,"y","16"),h(t,"width","12"),h(t,"height","4"),h(t,"stroke","currentColor"),h(t,"stroke-width","2"),h(n,"d","M12 12V5M8 8L12 5L16 8"),h(n,"stroke","currentColor"),h(n,"stroke-width","2"),h(n,"fill","none"),h(n,"stroke-linecap","round"),h(e,"viewBox","0 0 24 24"),h(e,"width","16"),h(e,"height","16")},m(i,s){q(i,e,s),N(e,t),N(e,n)},d(i){i&&d(e)}}}function Jr(l){let e,t,n;return{c(){e=ne("svg"),t=ne("rect"),n=ne("path"),this.h()},l(i){e=ie(i,"svg",{viewBox:!0,width:!0,height:!0});var s=S(e);t=ie(s,"rect",{x:!0,y:!0,width:!0,height:!0,stroke:!0,"stroke-width":!0,fill:!0}),S(t).forEach(d),n=ie(s,"path",{d:!0,stroke:!0,"stroke-width":!0,fill:!0,"stroke-linecap":!0}),S(n).forEach(d),s.forEach(d),this.h()},h(){h(t,"x","16"),h(t,"y","6"),h(t,"width","4"),h(t,"height","12"),h(t,"stroke","currentColor"),h(t,"stroke-width","2"),h(t,"fill","none"),h(n,"d","M12 12H5M8 8L5 12L8 16"),h(n,"stroke","currentColor"),h(n,"stroke-width","2"),h(n,"fill","none"),h(n,"stroke-linecap","round"),h(e,"viewBox","0 0 24 24"),h(e,"width","16"),h(e,"height","16")},m(i,s){q(i,e,s),N(e,t),N(e,n)},d(i){i&&d(e)}}}function Fr(l){let e,t,n;return{c(){e=ne("svg"),t=ne("rect"),n=ne("path"),this.h()},l(i){e=ie(i,"svg",{viewBox:!0,width:!0,height:!0});var s=S(e);t=ie(s,"rect",{x:!0,y:!0,width:!0,height:!0,stroke:!0,"stroke-width":!0,fill:!0}),S(t).forEach(d),n=ie(s,"path",{d:!0,stroke:!0,"stroke-width":!0,fill:!0,"stroke-linecap":!0}),S(n).forEach(d),s.forEach(d),this.h()},h(){h(t,"x","4"),h(t,"y","6"),h(t,"width","4"),h(t,"height","12"),h(t,"stroke","currentColor"),h(t,"stroke-width","2"),h(t,"fill","none"),h(n,"d","M12 12H19M16 8L19 12L16 16"),h(n,"stroke","currentColor"),h(n,"stroke-width","2"),h(n,"fill","none"),h(n,"stroke-linecap","round"),h(e,"viewBox","0 0 24 24"),h(e,"width","16"),h(e,"height","16")},m(i,s){q(i,e,s),N(e,t),N(e,n)},d(i){i&&d(e)}}}function Kr(l){let e;function t(s,r){if(s[0]=="add-column-right")return Fr;if(s[0]=="add-column-left")return Jr;if(s[0]=="add-row-above")return Rr;if(s[0]=="add-row-below")return Ur;if(s[0]=="delete-row")return Pr;if(s[0]=="delete-column")return zr;if(s[0]=="sort-asc")return Vr;if(s[0]=="sort-desc")return Hr;if(s[0]=="clear-sort")return qr;if(s[0]=="filter")return jr;if(s[0]=="clear-filter")return Lr}let n=t(l),i=n&&n(l);return{c(){i&&i.c(),e=ke()},l(s){i&&i.l(s),e=ke()},m(s,r){i&&i.m(s,r),q(s,e,r)},p(s,[r]){n!==(n=t(s))&&(i&&i.d(1),i=n&&n(s),i&&(i.c(),i.m(e.parentNode,e)))},i:Ce,o:Ce,d(s){s&&d(e),i&&i.d(s)}}}function Wr(l,e,t){let{icon:n}=e;return l.$$set=i=>{"icon"in i&&t(0,n=i.icon)},[n]}class Ke extends qe{constructor(e){super(),He(this,e,Wr,Kr,Ve,{icon:0})}}function gl(l){let e,t,n,i,s,r;const o=[Xr,Yr],_=[];function u(f,c){return f[19]==="asc"?0:1}n=u(l),i=_[n]=o[n](l);let a=l[11].length>1&&bl(l);return{c(){e=P("div"),t=P("span"),i.c(),s=X(),a&&a.c(),this.h()},l(f){e=U(f,"DIV",{class:!0});var c=S(e);t=U(c,"SPAN",{class:!0});var g=S(t);i.l(g),g.forEach(d),s=G(c),a&&a.l(c),c.forEach(d),this.h()},h(){h(t,"class","sort-arrow svelte-41hbvn"),h(e,"class","sort-indicators svelte-41hbvn")},m(f,c){q(f,e,c),N(e,t),_[n].m(t,null),N(e,s),a&&a.m(e,null),r=!0},p(f,c){let g=n;n=u(f),n!==g&&(me(),A(_[g],1,1,()=>{_[g]=null}),ge(),i=_[n],i||(i=_[n]=o[n](f),i.c()),E(i,1),i.m(t,null)),f[11].length>1?a?a.p(f,c):(a=bl(f),a.c(),a.m(e,null)):a&&(a.d(1),a=null)},i(f){r||(E(i),r=!0)},o(f){A(i),r=!1},d(f){f&&d(e),_[n].d(),a&&a.d()}}}function Yr(l){let e,t;return e=new Or({props:{size:12}}),{c(){re(e.$$.fragment)},l(n){_e(e.$$.fragment,n)},m(n,i){se(e,n,i),t=!0},i(n){t||(E(e.$$.fragment,n),t=!0)},o(n){A(e.$$.fragment,n),t=!1},d(n){oe(e,n)}}}function Xr(l){let e,t;return e=new Mr({props:{size:12}}),{c(){re(e.$$.fragment)},l(n){_e(e.$$.fragment,n)},m(n,i){se(e,n,i),t=!0},i(n){t||(E(e.$$.fragment,n),t=!0)},o(n){A(e.$$.fragment,n),t=!1},d(n){oe(e,n)}}}function bl(l){let e,t;return{c(){e=P("span"),t=Ae(l[20]),this.h()},l(n){e=U(n,"SPAN",{class:!0});var i=S(e);t=Be(i,l[20]),i.forEach(d),this.h()},h(){h(e,"class","sort-priority svelte-41hbvn")},m(n,i){q(n,e,i),N(e,t)},p(n,i){i[0]&1048576&&Me(t,n[20])},d(n){n&&d(e)}}}function wl(l){let e,t,n,i;return n=new Ke({props:{icon:"filter"}}),{c(){e=P("div"),t=P("span"),re(n.$$.fragment),this.h()},l(s){e=U(s,"DIV",{class:!0});var r=S(e);t=U(r,"SPAN",{class:!0});var o=S(t);_e(n.$$.fragment,o),o.forEach(d),r.forEach(d),this.h()},h(){h(t,"class","filter-icon svelte-41hbvn"),h(e,"class","filter-indicators svelte-41hbvn")},m(s,r){q(s,e,r),N(e,t),se(n,t,null),i=!0},i(s){i||(E(n.$$.fragment,s),i=!0)},o(s){A(n.$$.fragment,s),i=!1},d(s){s&&d(e),oe(n)}}}function kl(l){let e,t;return e=new Sr({}),{c(){re(e.$$.fragment)},l(n){_e(e.$$.fragment,n)},m(n,i){se(e,n,i),t=!0},i(n){t||(E(e.$$.fragment,n),t=!0)},o(n){A(e.$$.fragment,n),t=!1},d(n){oe(e,n)}}}function vl(l){let e,t;return e=new hn({props:{on_click:l[30]}}),{c(){re(e.$$.fragment)},l(n){_e(e.$$.fragment,n)},m(n,i){se(e,n,i),t=!0},p(n,i){const s={};i[0]&516&&(s.on_click=n[30]),e.$set(s)},i(n){t||(E(e.$$.fragment,n),t=!0)},o(n){A(e.$$.fragment,n),t=!1},d(n){oe(e,n)}}}function Gr(l){let e,t,n,i,s,r,o,_,u,a,f,c,g,M,w;function C(B){l[26](B)}function p(B){l[27](B)}let y={max_chars:l[14],latex_delimiters:l[12],line_breaks:l[13],edit:l[4]===l[2],header:!0,editable:l[15],is_static:l[17],i18n:l[16],coords:[l[2],0]};l[0]!==void 0&&(y.value=l[0]),l[1]!==void 0&&(y.el=l[1]),s=new Kt({props:y}),Ee.push(()=>Ie(s,"value",C)),Ee.push(()=>Ie(s,"el",p)),s.$on("keydown",l[28]);let k=l[18]!==-1&&gl(l),T=l[21]!==-1&&wl(),O=l[17]&&kl(),L=l[22]&&vl(l);return{c(){e=P("th"),t=P("div"),n=P("div"),i=P("button"),re(s.$$.fragment),_=X(),k&&k.c(),u=X(),T&&T.c(),a=X(),O&&O.c(),f=X(),L&&L.c(),this.h()},l(B){e=U(B,"TH",{"aria-sort":!0,style:!0,title:!0,class:!0});var z=S(e);t=U(z,"DIV",{class:!0});var F=S(t);n=U(F,"DIV",{class:!0});var ae=S(n);i=U(ae,"BUTTON",{class:!0,title:!0});var R=S(i);_e(s.$$.fragment,R),_=G(R),k&&k.l(R),u=G(R),T&&T.l(R),R.forEach(d),a=G(ae),O&&O.l(ae),ae.forEach(d),f=G(F),L&&L.l(F),F.forEach(d),z.forEach(d),this.h()},h(){h(i,"class","header-button svelte-41hbvn"),h(i,"title",l[0]),h(n,"class","header-content svelte-41hbvn"),h(t,"class","cell-wrap svelte-41hbvn"),h(e,"aria-sort",c=wt(l[0],l[11],l[6])==="none"?"none":wt(l[0],l[11],l[6])==="asc"?"ascending":"descending"),De(e,"width",l[7](l[2])),De(e,"left",l[23](l[2])),h(e,"title",l[0]),h(e,"class","svelte-41hbvn"),Y(e,"pinned-column",l[2]<l[3]),Y(e,"last-pinned",l[2]===l[3]-1),Y(e,"focus",l[4]===l[2]||l[5]===l[2]),Y(e,"sorted",l[18]!==-1),Y(e,"filtered",l[21]!==-1)},m(B,z){q(B,e,z),N(e,t),N(t,n),N(n,i),se(s,i,null),N(i,_),k&&k.m(i,null),N(i,u),T&&T.m(i,null),N(n,a),O&&O.m(n,null),N(t,f),L&&L.m(t,null),g=!0,M||(w=[ce(i,"click",l[29]),ce(i,"mousedown",Qr),ce(e,"click",l[31]),ce(e,"mousedown",Zr)],M=!0)},p(B,z){const F={};z[0]&16384&&(F.max_chars=B[14]),z[0]&4096&&(F.latex_delimiters=B[12]),z[0]&8192&&(F.line_breaks=B[13]),z[0]&20&&(F.edit=B[4]===B[2]),z[0]&32768&&(F.editable=B[15]),z[0]&131072&&(F.is_static=B[17]),z[0]&65536&&(F.i18n=B[16]),z[0]&4&&(F.coords=[B[2],0]),!r&&z[0]&1&&(r=!0,F.value=B[0],Oe(()=>r=!1)),!o&&z[0]&2&&(o=!0,F.el=B[1],Oe(()=>o=!1)),s.$set(F),B[18]!==-1?k?(k.p(B,z),z[0]&262144&&E(k,1)):(k=gl(B),k.c(),E(k,1),k.m(i,u)):k&&(me(),A(k,1,1,()=>{k=null}),ge()),B[21]!==-1?T?z[0]&2097152&&E(T,1):(T=wl(),T.c(),E(T,1),T.m(i,null)):T&&(me(),A(T,1,1,()=>{T=null}),ge()),(!g||z[0]&1)&&h(i,"title",B[0]),B[17]?O?z[0]&131072&&E(O,1):(O=kl(),O.c(),E(O,1),O.m(n,null)):O&&(me(),A(O,1,1,()=>{O=null}),ge()),B[22]?L?(L.p(B,z),z[0]&4194304&&E(L,1)):(L=vl(B),L.c(),E(L,1),L.m(t,null)):L&&(me(),A(L,1,1,()=>{L=null}),ge()),(!g||z[0]&2113&&c!==(c=wt(B[0],B[11],B[6])==="none"?"none":wt(B[0],B[11],B[6])==="asc"?"ascending":"descending"))&&h(e,"aria-sort",c),(!g||z[0]&132)&&De(e,"width",B[7](B[2])),(!g||z[0]&4)&&De(e,"left",B[23](B[2])),(!g||z[0]&1)&&h(e,"title",B[0]),(!g||z[0]&12)&&Y(e,"pinned-column",B[2]<B[3]),(!g||z[0]&12)&&Y(e,"last-pinned",B[2]===B[3]-1),(!g||z[0]&52)&&Y(e,"focus",B[4]===B[2]||B[5]===B[2]),(!g||z[0]&262144)&&Y(e,"sorted",B[18]!==-1),(!g||z[0]&2097152)&&Y(e,"filtered",B[21]!==-1)},i(B){g||(E(s.$$.fragment,B),E(k),E(T),E(O),E(L),g=!0)},o(B){A(s.$$.fragment,B),A(k),A(T),A(O),A(L),g=!1},d(B){B&&d(e),oe(s),k&&k.d(),T&&T.d(),O&&O.d(),L&&L.d(),M=!1,Qe(w)}}}const Qr=l=>{l.preventDefault(),l.stopPropagation()},Zr=l=>{l.preventDefault(),l.stopPropagation()};function xr(l,e,t){let n,i,s,r,o,{value:_}=e,{i:u}=e,{actual_pinned_columns:a}=e,{header_edit:f}=e,{selected_header:c}=e,{headers:g}=e,{get_cell_width:M}=e,{handle_header_click:w}=e,{toggle_header_menu:C}=e,{end_header_edit:p}=e,{sort_columns:y=[]}=e,{filter_columns:k=[]}=e,{latex_delimiters:T}=e,{line_breaks:O}=e,{max_chars:L}=e,{editable:B}=e,{i18n:z}=e,{el:F}=e,{is_static:ae}=e,{col_count:R}=e;function Q(V){return V>=a?"auto":V===0?"0":`calc(${Array(V).fill(0).map((D,ee)=>M(ee)).join(" + ")})`}function K(V){_=V,t(0,_)}function te(V){F=V,t(1,F)}const fe=V=>{(V.detail.key==="Enter"||V.detail.key==="Escape"||V.detail.key==="Tab")&&p(V)},$=V=>w(V,u),ue=V=>C(V,u),ye=V=>w(V,u);return l.$$set=V=>{"value"in V&&t(0,_=V.value),"i"in V&&t(2,u=V.i),"actual_pinned_columns"in V&&t(3,a=V.actual_pinned_columns),"header_edit"in V&&t(4,f=V.header_edit),"selected_header"in V&&t(5,c=V.selected_header),"headers"in V&&t(6,g=V.headers),"get_cell_width"in V&&t(7,M=V.get_cell_width),"handle_header_click"in V&&t(8,w=V.handle_header_click),"toggle_header_menu"in V&&t(9,C=V.toggle_header_menu),"end_header_edit"in V&&t(10,p=V.end_header_edit),"sort_columns"in V&&t(11,y=V.sort_columns),"filter_columns"in V&&t(24,k=V.filter_columns),"latex_delimiters"in V&&t(12,T=V.latex_delimiters),"line_breaks"in V&&t(13,O=V.line_breaks),"max_chars"in V&&t(14,L=V.max_chars),"editable"in V&&t(15,B=V.editable),"i18n"in V&&t(16,z=V.i18n),"el"in V&&t(1,F=V.el),"is_static"in V&&t(17,ae=V.is_static),"col_count"in V&&t(25,R=V.col_count)},l.$$.update=()=>{l.$$.dirty[0]&33554432&&t(22,n=R&&R[1]==="dynamic"),l.$$.dirty[0]&2052&&t(18,i=y.findIndex(V=>V.col===u)),l.$$.dirty[0]&16777220&&t(21,s=k.findIndex(V=>V.col===u)),l.$$.dirty[0]&262144&&t(20,r=i!==-1?i+1:null),l.$$.dirty[0]&264192&&t(19,o=i!==-1?y[i].direction:null)},[_,F,u,a,f,c,g,M,w,C,p,y,T,O,L,B,z,ae,i,o,r,s,n,Q,k,R,K,te,fe,$,ue,ye]}class dn extends qe{constructor(e){super(),He(this,e,xr,Gr,Ve,{value:0,i:2,actual_pinned_columns:3,header_edit:4,selected_header:5,headers:6,get_cell_width:7,handle_header_click:8,toggle_header_menu:9,end_header_edit:10,sort_columns:11,filter_columns:24,latex_delimiters:12,line_breaks:13,max_chars:14,editable:15,i18n:16,el:1,is_static:17,col_count:25},null,[-1,-1])}}function pl(l){let e,t;return e=new hn({props:{on_click:l[39]}}),{c(){re(e.$$.fragment)},l(n){_e(e.$$.fragment,n)},m(n,i){se(e,n,i),t=!0},p(n,i){const s={};i[0]&536&&(s.on_click=n[39]),e.$set(s)},i(n){t||(E(e.$$.fragment,n),t=!0)},o(n){A(e.$$.fragment,n),t=!1},d(n){oe(e,n)}}}function $r(l){let e,t,n,i,s,r,o=l[19]&&l[10]([l[3],l[4]],l[0],l[19]),_,u,a,f,c,g;function M(y){l[36](y)}function w(y){l[37](y)}let C={display_value:l[26]!==void 0?l[26]:String(l[1]),latex_delimiters:l[14],line_breaks:l[15],editable:l[19],is_static:l[20],edit:l[17]&&l[17][0]===l[3]&&l[17][1]===l[4],datatype:l[16],max_chars:l[18],i18n:l[21],components:l[22],show_selection_buttons:l[0].length===1&&l[0][0][0]===l[3]&&l[0][0][1]===l[4],coords:[l[3],l[4]],on_select_column:l[23],on_select_row:l[24],is_dragging:l[25],wrap_text:l[27]};l[1]!==void 0&&(C.value=l[1]),l[2].input!==void 0&&(C.el=l[2].input),n=new Kt({props:C}),Ee.push(()=>Ie(n,"value",M)),Ee.push(()=>Ie(n,"el",w)),n.$on("focus",l[38]),n.$on("blur",function(){Xe(l[8])&&l[8].apply(this,arguments)});let p=o&&pl(l);return{c(){e=P("td"),t=P("div"),re(n.$$.fragment),r=X(),p&&p.c(),this.h()},l(y){e=U(y,"TD",{tabindex:!0,"data-row":!0,"data-col":!0,"data-testid":!0,style:!0,class:!0});var k=S(e);t=U(k,"DIV",{class:!0});var T=S(t);_e(n.$$.fragment,T),r=G(T),p&&p.l(T),T.forEach(d),k.forEach(d),this.h()},h(){h(t,"class","cell-wrap svelte-v1pjjd"),h(e,"tabindex",_=l[4]<l[5]?-1:0),h(e,"data-row",l[3]),h(e,"data-col",l[4]),h(e,"data-testid",u=`cell-${l[3]}-${l[4]}`),h(e,"style",a="width: "+l[6](l[4])+"; left: "+l[33](l[4])+"; "+(l[13]||"")),h(e,"class","svelte-v1pjjd"),Y(e,"pinned-column",l[4]<l[5]),Y(e,"last-pinned",l[4]===l[5]-1),Y(e,"flash",l[11]&&l[32]),Y(e,"cell-selected",l[32]),Y(e,"no-top",l[31]),Y(e,"no-bottom",l[30]),Y(e,"no-left",l[29]),Y(e,"no-right",l[28]),Y(e,"menu-active",l[12]&&l[12].row===l[3]&&l[12].col===l[4]),Y(e,"dragging",l[25])},m(y,k){q(y,e,k),N(e,t),se(n,t,null),N(t,r),p&&p.m(t,null),l[40](e),f=!0,c||(g=[ce(e,"mousedown",l[41]),ce(e,"contextmenu",_n(l[42]))],c=!0)},p(y,k){l=y;const T={};k[0]&67108866&&(T.display_value=l[26]!==void 0?l[26]:String(l[1])),k[0]&16384&&(T.latex_delimiters=l[14]),k[0]&32768&&(T.line_breaks=l[15]),k[0]&524288&&(T.editable=l[19]),k[0]&1048576&&(T.is_static=l[20]),k[0]&131096&&(T.edit=l[17]&&l[17][0]===l[3]&&l[17][1]===l[4]),k[0]&65536&&(T.datatype=l[16]),k[0]&262144&&(T.max_chars=l[18]),k[0]&2097152&&(T.i18n=l[21]),k[0]&4194304&&(T.components=l[22]),k[0]&25&&(T.show_selection_buttons=l[0].length===1&&l[0][0][0]===l[3]&&l[0][0][1]===l[4]),k[0]&24&&(T.coords=[l[3],l[4]]),k[0]&8388608&&(T.on_select_column=l[23]),k[0]&16777216&&(T.on_select_row=l[24]),k[0]&33554432&&(T.is_dragging=l[25]),k[0]&134217728&&(T.wrap_text=l[27]),!i&&k[0]&2&&(i=!0,T.value=l[1],Oe(()=>i=!1)),!s&&k[0]&4&&(s=!0,T.el=l[2].input,Oe(()=>s=!1)),n.$set(T),k[0]&525337&&(o=l[19]&&l[10]([l[3],l[4]],l[0],l[19])),o?p?(p.p(l,k),k[0]&525337&&E(p,1)):(p=pl(l),p.c(),E(p,1),p.m(t,null)):p&&(me(),A(p,1,1,()=>{p=null}),ge()),(!f||k[0]&48&&_!==(_=l[4]<l[5]?-1:0))&&h(e,"tabindex",_),(!f||k[0]&8)&&h(e,"data-row",l[3]),(!f||k[0]&16)&&h(e,"data-col",l[4]),(!f||k[0]&24&&u!==(u=`cell-${l[3]}-${l[4]}`))&&h(e,"data-testid",u),(!f||k[0]&8272&&a!==(a="width: "+l[6](l[4])+"; left: "+l[33](l[4])+"; "+(l[13]||"")))&&h(e,"style",a),(!f||k[0]&48)&&Y(e,"pinned-column",l[4]<l[5]),(!f||k[0]&48)&&Y(e,"last-pinned",l[4]===l[5]-1),(!f||k[0]&2048|k[1]&2)&&Y(e,"flash",l[11]&&l[32]),(!f||k[1]&2)&&Y(e,"cell-selected",l[32]),(!f||k[1]&1)&&Y(e,"no-top",l[31]),(!f||k[0]&1073741824)&&Y(e,"no-bottom",l[30]),(!f||k[0]&536870912)&&Y(e,"no-left",l[29]),(!f||k[0]&268435456)&&Y(e,"no-right",l[28]),(!f||k[0]&4120)&&Y(e,"menu-active",l[12]&&l[12].row===l[3]&&l[12].col===l[4]),(!f||k[0]&33554432)&&Y(e,"dragging",l[25])},i(y){f||(E(n.$$.fragment,y),E(p),f=!0)},o(y){A(n.$$.fragment,y),A(p),f=!1},d(y){y&&d(e),oe(n),p&&p.d(),l[40](null),c=!1,Qe(g)}}}function es(l,e,t){let n,i,s,r,o,_,{value:u}=e,{index:a}=e,{j:f}=e,{actual_pinned_columns:c}=e,{get_cell_width:g}=e,{handle_cell_click:M}=e,{handle_blur:w}=e,{toggle_cell_menu:C}=e,{is_cell_selected:p}=e,{should_show_cell_menu:y}=e,{selected_cells:k}=e,{copy_flash:T}=e,{active_cell_menu:O}=e,{styling:L}=e,{latex_delimiters:B}=e,{line_breaks:z}=e,{datatype:F}=e,{editing:ae}=e,{max_chars:R}=e,{editable:Q}=e,{is_static:K=!1}=e,{i18n:te}=e,{components:fe={}}=e,{el:$}=e,{handle_select_column:ue}=e,{handle_select_row:ye}=e,{is_dragging:V}=e,{display_value:he}=e,{wrap:D=!1}=e;function ee(j){return j>=c?"auto":j===0?"0":`calc(${Array(j).fill(0).map((Ue,we)=>g(we)).join(" + ")})`}function v(j){u=j,t(1,u)}function I(j){l.$$.not_equal($.input,j)&&($.input=j,t(2,$))}const b=()=>{const j=a,x=f;k.some(([Ue,we])=>Ue===j&&we===x)||t(0,k=[[j,x]])},Z=j=>C(j,a,f);function be(j){Ee[j?"unshift":"push"](()=>{$.cell=j,t(2,$)})}const Le=j=>M(j,a,f),de=j=>C(j,a,f);return l.$$set=j=>{"value"in j&&t(1,u=j.value),"index"in j&&t(3,a=j.index),"j"in j&&t(4,f=j.j),"actual_pinned_columns"in j&&t(5,c=j.actual_pinned_columns),"get_cell_width"in j&&t(6,g=j.get_cell_width),"handle_cell_click"in j&&t(7,M=j.handle_cell_click),"handle_blur"in j&&t(8,w=j.handle_blur),"toggle_cell_menu"in j&&t(9,C=j.toggle_cell_menu),"is_cell_selected"in j&&t(34,p=j.is_cell_selected),"should_show_cell_menu"in j&&t(10,y=j.should_show_cell_menu),"selected_cells"in j&&t(0,k=j.selected_cells),"copy_flash"in j&&t(11,T=j.copy_flash),"active_cell_menu"in j&&t(12,O=j.active_cell_menu),"styling"in j&&t(13,L=j.styling),"latex_delimiters"in j&&t(14,B=j.latex_delimiters),"line_breaks"in j&&t(15,z=j.line_breaks),"datatype"in j&&t(16,F=j.datatype),"editing"in j&&t(17,ae=j.editing),"max_chars"in j&&t(18,R=j.max_chars),"editable"in j&&t(19,Q=j.editable),"is_static"in j&&t(20,K=j.is_static),"i18n"in j&&t(21,te=j.i18n),"components"in j&&t(22,fe=j.components),"el"in j&&t(2,$=j.el),"handle_select_column"in j&&t(23,ue=j.handle_select_column),"handle_select_row"in j&&t(24,ye=j.handle_select_row),"is_dragging"in j&&t(25,V=j.is_dragging),"display_value"in j&&t(26,he=j.display_value),"wrap"in j&&t(27,D=j.wrap)},l.$$.update=()=>{l.$$.dirty[0]&25|l.$$.dirty[1]&8&&t(35,n=p([a,f],k||[])),l.$$.dirty[0]&25&&t(32,i=Wi([a,f],k)),l.$$.dirty[1]&16&&t(31,s=n.includes("no-top")),l.$$.dirty[1]&16&&t(30,r=n.includes("no-bottom")),l.$$.dirty[1]&16&&t(29,o=n.includes("no-left")),l.$$.dirty[1]&16&&t(28,_=n.includes("no-right"))},[k,u,$,a,f,c,g,M,w,C,y,T,O,L,B,z,F,ae,R,Q,K,te,fe,ue,ye,V,he,D,_,o,r,s,i,ee,p,n,v,I,b,Z,be,Le,de]}class ts extends qe{constructor(e){super(),He(this,e,es,$r,Ve,{value:1,index:3,j:4,actual_pinned_columns:5,get_cell_width:6,handle_cell_click:7,handle_blur:8,toggle_cell_menu:9,is_cell_selected:34,should_show_cell_menu:10,selected_cells:0,copy_flash:11,active_cell_menu:12,styling:13,latex_delimiters:14,line_breaks:15,datatype:16,editing:17,max_chars:18,editable:19,is_static:20,i18n:21,components:22,el:2,handle_select_column:23,handle_select_row:24,is_dragging:25,display_value:26,wrap:27},null,[-1,-1])}}function ls(l){let e,t="+",n,i;return{c(){e=P("button"),e.textContent=t,this.h()},l(s){e=U(s,"BUTTON",{class:!0,"aria-label":!0,"data-svelte-h":!0}),lt(e)!=="svelte-qq2si4"&&(e.textContent=t),this.h()},h(){h(e,"class","add-row-button svelte-jkwuz7"),h(e,"aria-label","Add row")},m(s,r){q(s,e,r),n||(i=ce(e,"click",function(){Xe(l[0])&&l[0].apply(this,arguments)}),n=!0)},p(s,[r]){l=s},i:Ce,o:Ce,d(s){s&&d(e),n=!1,i()}}}function ns(l,e,t){let{on_click:n}=e;return l.$$set=i=>{"on_click"in i&&t(0,n=i.on_click)},[n]}class is extends qe{constructor(e){super(),He(this,e,ns,ls,Ve,{on_click:0})}}const rs=l=>({}),yl=l=>({});function El(l,e,t){const n=l.slice();return n[37]=e[t],n}const ss=l=>({item:l[0]&1024,index:l[0]&1024}),Nl=l=>({item:l[37].data,index:l[37].index}),os=l=>({}),Tl=l=>({});function Cl(l){let e=[],t=new Map,n,i,s=We(l[10]);const r=o=>o[37].data[0].id;for(let o=0;o<s.length;o+=1){let _=El(l,s,o),u=r(_);t.set(u,e[o]=Sl(u,_))}return{c(){for(let o=0;o<e.length;o+=1)e[o].c();n=ke()},l(o){for(let _=0;_<e.length;_+=1)e[_].l(o);n=ke()},m(o,_){for(let u=0;u<e.length;u+=1)e[u]&&e[u].m(o,_);q(o,n,_),i=!0},p(o,_){_[0]&8389632&&(s=We(o[10]),me(),e=ft(e,_,r,1,o,s,t,n.parentNode,ct,Sl,n,El),ge())},i(o){if(!i){for(let _=0;_<s.length;_+=1)E(e[_]);i=!0}},o(o){for(let _=0;_<e.length;_+=1)A(e[_]);i=!1},d(o){o&&d(n);for(let _=0;_<e.length;_+=1)e[_].d(o)}}}function as(l){let e;return{c(){e=Ae(`Missing Table Row
						`)},l(t){e=Be(t,`Missing Table Row
						`)},m(t,n){q(t,e,n)},d(t){t&&d(e)}}}function Sl(l,e){let t,n;const i=e[24].tbody,s=zt(i,e,e[23],Nl),r=s||as();return{key:l,first:null,c(){t=ke(),r&&r.c(),this.h()},l(o){t=ke(),r&&r.l(o),this.h()},h(){this.first=t},m(o,_){q(o,t,_),r&&r.m(o,_),n=!0},p(o,_){e=o,s&&s.p&&(!n||_[0]&8389632)&&Pt(s,i,e,e[23],n?Rt(i,e[23],_,ss):Ut(e[23]),Nl)},i(o){n||(E(r,o),n=!0)},o(o){A(r,o),n=!1},d(o){o&&d(t),r&&r.d(o)}}}function us(l){let e,t,n,i,s,r,o,_,u,a,f,c,g,M;const w=l[24].thead,C=zt(w,l,l[23],Tl);let p=l[10].length&&l[10][0].data.length&&Cl(l);const y=l[24].tfoot,k=zt(y,l,l[23],yl);return{c(){e=P("svelte-virtual-table-viewport"),t=P("div"),n=P("table"),i=P("thead"),C&&C.c(),r=X(),o=P("tbody"),p&&p.c(),_=X(),u=P("tfoot"),k&&k.c(),this.h()},l(T){e=U(T,"SVELTE-VIRTUAL-TABLE-VIEWPORT",{});var O=S(e);t=U(O,"DIV",{});var L=S(t);n=U(L,"TABLE",{class:!0,style:!0});var B=S(n);i=U(B,"THEAD",{class:!0});var z=S(i);C&&C.l(z),z.forEach(d),r=G(B),o=U(B,"TBODY",{class:!0});var F=S(o);p&&p.l(F),F.forEach(d),_=G(B),u=U(B,"TFOOT",{class:!0});var ae=S(u);k&&k.l(ae),ae.forEach(d),B.forEach(d),L.forEach(d),O.forEach(d),this.h()},h(){h(i,"class","thead svelte-zsmsrz"),_l(()=>l[25].call(i)),h(o,"class","tbody svelte-zsmsrz"),h(u,"class","tfoot svelte-zsmsrz"),_l(()=>l[27].call(u)),h(n,"class","table svelte-zsmsrz"),De(n,"height",_s),De(n,"--bw-svt-p-top",l[9]+"px"),De(n,"--bw-svt-p-bottom",l[5]+"px"),De(n,"--bw-svt-head-height",l[7]+"px"),De(n,"--bw-svt-foot-height",l[8]+"px"),De(n,"--bw-svt-avg-row-height",l[3]+"px"),De(n,"--max-height",l[1]+"px"),Y(n,"disable-scroll",l[2])},m(T,O){q(T,e,O),N(e,t),N(t,n),N(n,i),C&&C.m(i,null),s=fl(i,l[25].bind(i)),N(n,r),N(n,o),p&&p.m(o,null),l[26](o),N(n,_),N(n,u),k&&k.m(u,null),a=fl(u,l[27].bind(u)),l[28](n),f=gi.observe(n,l[29].bind(n)),c=!0,g||(M=ce(n,"scroll",l[11]),g=!0)},p(T,O){C&&C.p&&(!c||O[0]&8388608)&&Pt(C,w,T,T[23],c?Rt(w,T[23],O,os):Ut(T[23]),Tl),T[10].length&&T[10][0].data.length?p?(p.p(T,O),O[0]&1024&&E(p,1)):(p=Cl(T),p.c(),E(p,1),p.m(o,null)):p&&(me(),A(p,1,1,()=>{p=null}),ge()),k&&k.p&&(!c||O[0]&8388608)&&Pt(k,y,T,T[23],c?Rt(y,T[23],O,rs):Ut(T[23]),yl),(!c||O[0]&512)&&De(n,"--bw-svt-p-top",T[9]+"px"),(!c||O[0]&32)&&De(n,"--bw-svt-p-bottom",T[5]+"px"),(!c||O[0]&128)&&De(n,"--bw-svt-head-height",T[7]+"px"),(!c||O[0]&256)&&De(n,"--bw-svt-foot-height",T[8]+"px"),(!c||O[0]&8)&&De(n,"--bw-svt-avg-row-height",T[3]+"px"),(!c||O[0]&2)&&De(n,"--max-height",T[1]+"px"),(!c||O[0]&4)&&Y(n,"disable-scroll",T[2])},i(T){c||(E(C,T),E(p),E(k,T),c=!0)},o(T){A(C,T),A(p),A(k,T),c=!1},d(T){T&&d(e),C&&C.d(T),s(),p&&p.d(),l[26](null),k&&k.d(T),a(),l[28](null),f(),g=!1,M()}}}let _s="100%";function fs(l,e,t){let n,{$$slots:i={},$$scope:s}=e,{items:r=[]}=e,{max_height:o}=e,{actual_height:_}=e,{table_scrollbar_width:u}=e,{start:a=0}=e,{end:f=20}=e,{selected:c}=e,{disable_scroll:g=!1}=e,{show_scroll_button:M=!1}=e,{viewport:w}=e;pt();let C=30,p=0,y,k=0,T=0,O=[],L,B,z=0,F=200,ae=[],R;const Q=typeof window<"u",K=Q?window.requestAnimationFrame:v=>v();async function te(){n.length<a&&await ue(n.length-1,{behavior:"auto"});const v=Math.max(0,w.scrollTop);t(16,M=v>100),t(15,u=w.offsetWidth-w.clientWidth);for(let j=0;j<B.length;j+=1)O[a+j]=B[j].getBoundingClientRect().height;let I=0,b=k;for(;I<n.length;){const j=O[I]||C;if(b+j>v-o){t(12,a=I),t(9,z=b-k);break}b+=j,I+=1}let Z=k;for(;I<n.length;){const j=O[I]||C;if(Z+=j,I+=1,Z-k>3*o)break}t(13,f=I);const be=n.length-f,Le=w.offsetHeight-w.clientHeight;Le>0&&(Z+=Le);let de=O.filter(j=>typeof j=="number");for(t(3,C=de.reduce((j,x)=>j+x,0)/de.length||30),t(5,p=be*C),isFinite(p)||t(5,p=2e5),O.length=n.length;I<n.length;)I+=1,O[I]=C;o&&Z>o?t(14,_=o):t(14,_=Z)}async function fe(v){K(async()=>{if(typeof v!="number")return;const I=typeof v!="number"?!1:$(v);I!==!0&&(I==="back"&&await ue(v,{behavior:"instant"}),I==="forwards"&&await ue(v,{behavior:"instant"},!0))})}function $(v){const I=B&&B[v-a];if(!I&&v<a)return"back";if(!I&&v>=f-1)return"forwards";const{top:b}=w.getBoundingClientRect(),{top:Z,bottom:be}=I.getBoundingClientRect();return Z-b<37?"back":be-b>F?"forwards":!0}async function ue(v,I,b=!1){await rt();const Z=C;let be=v*Z;b&&(be=be-F+Z+k);const Le=w.offsetHeight-w.clientHeight;Le>0&&(be+=Le);const de={top:be,behavior:"smooth",...I};w.scrollTo(de)}_t(()=>{B=y.children,t(20,L=!0)});function ye(){k=this.offsetHeight,t(7,k)}function V(v){Ee[v?"unshift":"push"](()=>{y=v,t(6,y)})}function he(){T=this.offsetHeight,t(8,T)}function D(v){Ee[v?"unshift":"push"](()=>{w=v,t(0,w)})}function ee(){var v;R=(v=bi.entries.get(this))==null?void 0:v.contentRect,t(4,R)}return l.$$set=v=>{"items"in v&&t(17,r=v.items),"max_height"in v&&t(1,o=v.max_height),"actual_height"in v&&t(14,_=v.actual_height),"table_scrollbar_width"in v&&t(15,u=v.table_scrollbar_width),"start"in v&&t(12,a=v.start),"end"in v&&t(13,f=v.end),"selected"in v&&t(18,c=v.selected),"disable_scroll"in v&&t(2,g=v.disable_scroll),"show_scroll_button"in v&&t(16,M=v.show_scroll_button),"viewport"in v&&t(0,w=v.viewport),"$$scope"in v&&t(23,s=v.$$scope)},l.$$.update=()=>{l.$$.dirty[0]&16&&t(21,F=(R==null?void 0:R.height)||200),l.$$.dirty[0]&131072&&t(22,n=r),l.$$.dirty[0]&7340033&&L&&F&&w.offsetParent&&K(te),l.$$.dirty[0]&262144&&fe(c),l.$$.dirty[0]&4206602&&t(10,ae=Q?n.slice(a,f).map((v,I)=>({index:I+a,data:v})):n.slice(0,o/n.length*C+1).map((v,I)=>({index:I+a,data:v})))},[w,o,g,C,R,p,y,k,T,z,ae,te,a,f,_,u,M,r,c,ue,L,F,n,s,i,ye,V,he,D,ee]}class cs extends qe{constructor(e){super(),He(this,e,fs,us,Ve,{items:17,max_height:1,actual_height:14,table_scrollbar_width:15,start:12,end:13,selected:18,disable_scroll:2,show_scroll_button:16,viewport:0,scroll_to_index:19},null,[-1,-1])}get scroll_to_index(){return this.$$.ctx[19]}}function Al(l,e,t){const n=l.slice();return n[15]=e[t],n}function Bl(l){let e,t=We(l[6][l[2]]),n=[];for(let i=0;i<t.length;i+=1)n[i]=Ml(Al(l,t,i));return{c(){e=P("div");for(let i=0;i<n.length;i+=1)n[i].c();this.h()},l(i){e=U(i,"DIV",{class:!0});var s=S(e);for(let r=0;r<n.length;r+=1)n[r].l(s);s.forEach(d),this.h()},h(){h(e,"class","dropdown-filter-options svelte-1nf5kyf")},m(i,s){q(i,e,s);for(let r=0;r<n.length;r+=1)n[r]&&n[r].m(e,null)},p(i,s){if(s&92){t=We(i[6][i[2]]);let r;for(r=0;r<t.length;r+=1){const o=Al(i,t,r);n[r]?n[r].p(o,s):(n[r]=Ml(o),n[r].c(),n[r].m(e,null))}for(;r<n.length;r+=1)n[r].d(1);n.length=t.length}},d(i){i&&d(e),wi(n,i)}}}function Ml(l){let e,t=l[15]+"",n,i,s,r;function o(){return l[11](l[15])}return{c(){e=P("button"),n=Ae(t),i=X(),this.h()},l(_){e=U(_,"BUTTON",{class:!0});var u=S(e);n=Be(u,t),i=G(u),u.forEach(d),this.h()},h(){h(e,"class","filter-option svelte-1nf5kyf")},m(_,u){q(_,e,u),N(e,n),N(e,i),s||(r=ce(e,"click",xe(o)),s=!0)},p(_,u){l=_,u&4&&t!==(t=l[15]+"")&&Me(n,t)},d(_){_&&d(e),s=!1,r()}}}function hs(l){let e,t,n,i,s,r,o="Filter as",_,u,a,f,c,g,M,w,C,p,y,k,T,O,L,B,z,F,ae,R,Q;y=new Li({});let K=l[4]&&Bl(l);return F=new Jt({}),{c(){e=P("div"),t=P("div"),n=X(),i=P("div"),s=P("div"),r=P("span"),r.textContent=o,_=X(),u=P("button"),a=Ae(l[2]),c=X(),g=P("div"),M=P("div"),w=P("button"),C=Ae(l[3]),p=X(),re(y.$$.fragment),T=X(),K&&K.c(),O=X(),L=P("input"),B=X(),z=P("button"),re(F.$$.fragment),this.h()},l(te){e=U(te,"DIV",{});var fe=S(e);t=U(fe,"DIV",{class:!0}),S(t).forEach(d),n=G(fe),i=U(fe,"DIV",{class:!0});var $=S(i);s=U($,"DIV",{class:!0});var ue=S(s);r=U(ue,"SPAN",{class:!0,"data-svelte-h":!0}),lt(r)!=="svelte-1ox9ptg"&&(r.textContent=o),_=G(ue),u=U(ue,"BUTTON",{"aria-label":!0,class:!0});var ye=S(u);a=Be(ye,l[2]),ye.forEach(d),ue.forEach(d),c=G($),g=U($,"DIV",{class:!0});var V=S(g);M=U(V,"DIV",{class:!0});var he=S(M);w=U(he,"BUTTON",{"aria-label":!0,class:!0});var D=S(w);C=Be(D,l[3]),p=G(D),_e(y.$$.fragment,D),D.forEach(d),T=G(he),K&&K.l(he),he.forEach(d),O=G(V),L=U(V,"INPUT",{type:!0,placeholder:!0,class:!0}),V.forEach(d),B=G($),z=U($,"BUTTON",{class:!0});var ee=S(z);_e(F.$$.fragment,ee),ee.forEach(d),$.forEach(d),fe.forEach(d),this.h()},h(){h(t,"class","background svelte-1nf5kyf"),h(r,"class","svelte-1nf5kyf"),h(u,"aria-label",f=`Change filter type. Filtering ${l[2]}s`),h(u,"class","svelte-1nf5kyf"),h(s,"class","filter-datatype-container svelte-1nf5kyf"),h(w,"aria-label",k=`Change filter. Using '${l[3]}'`),h(w,"class","svelte-1nf5kyf"),h(M,"class","filter-dropdown"),h(L,"type","text"),L.value=l[5],h(L,"placeholder","Type a value"),h(L,"class","filter-input svelte-1nf5kyf"),h(g,"class","input-container svelte-1nf5kyf"),h(z,"class","check-button svelte-1nf5kyf"),h(i,"class","filter-menu svelte-1nf5kyf")},m(te,fe){q(te,e,fe),N(e,t),N(e,n),N(e,i),N(i,s),N(s,r),N(s,_),N(s,u),N(u,a),N(i,c),N(i,g),N(g,M),N(M,w),N(w,C),N(w,p),se(y,w,null),N(M,T),K&&K.m(M,null),N(g,O),N(g,L),N(i,B),N(i,z),se(F,z,null),l[13](i),ae=!0,R||(Q=[ce(u,"click",xe(l[9])),ce(w,"click",xe(l[10])),ce(L,"click",xe(l[8])),ce(L,"input",l[7]),ce(z,"click",l[12])],R=!0)},p(te,[fe]){(!ae||fe&4)&&Me(a,te[2]),(!ae||fe&4&&f!==(f=`Change filter type. Filtering ${te[2]}s`))&&h(u,"aria-label",f),(!ae||fe&8)&&Me(C,te[3]),(!ae||fe&8&&k!==(k=`Change filter. Using '${te[3]}'`))&&h(w,"aria-label",k),te[4]?K?K.p(te,fe):(K=Bl(te),K.c(),K.m(M,null)):K&&(K.d(1),K=null),(!ae||fe&32&&L.value!==te[5])&&(L.value=te[5])},i(te){ae||(E(y.$$.fragment,te),E(F.$$.fragment,te),ae=!0)},o(te){A(y.$$.fragment,te),A(F.$$.fragment,te),ae=!1},d(te){te&&d(e),oe(y),K&&K.d(),oe(F),l[13](null),R=!1,Qe(Q)}}}function ds(l,e,t){let{on_filter:n=()=>{}}=e,i,s="string",r="Contains",o=!1,_="";const u={string:["Contains","Does not contain","Starts with","Ends with","Is","Is not","Is empty","Is not empty"],number:["=","≠",">","<","≥","≤","Is empty","Is not empty"]};_t(()=>{a()});function a(){if(!i)return;const y=window.innerWidth,k=window.innerHeight,T=i.getBoundingClientRect(),O=(y-T.width)/2,L=(k-T.height)/2;t(1,i.style.left=`${O}px`,i),t(1,i.style.top=`${L}px`,i)}function f(y){const k=y.target;t(5,_=k.value)}function c(y){it.call(this,l,y)}const g=()=>{t(2,s=s==="string"?"number":"string"),t(3,r=u[s][0])},M=()=>t(4,o=!o),w=y=>{t(3,r=y),t(4,o=!o)},C=()=>n(s,r,_);function p(y){Ee[y?"unshift":"push"](()=>{i=y,t(1,i)})}return l.$$set=y=>{"on_filter"in y&&t(0,n=y.on_filter)},[n,i,s,r,o,_,u,f,c,g,M,w,C,p]}class ms extends qe{constructor(e){super(),He(this,e,ds,hs,Ve,{on_filter:0})}}function Dl(l){let e,t,n,i=l[15]("dataframe.sort_ascending")+"",s,r,o,_,u,a,f=l[15]("dataframe.sort_descending")+"",c,g,M,w,C,p,y=l[15]("dataframe.clear_sort")+"",k,T,O,L,B,z=l[15]("dataframe.filter")+"",F,ae,R,Q,K,te,fe=l[15]("dataframe.clear_filter")+"",$,ue,ye,V;t=new Ke({props:{icon:"sort-asc"}});let he=l[10]==="asc"&&l[11]!==null&&Il(l);u=new Ke({props:{icon:"sort-desc"}});let D=l[10]==="desc"&&l[11]!==null&&Ol(l);C=new Ke({props:{icon:"clear-sort"}}),L=new Ke({props:{icon:"filter"}});let ee=l[14]&&Ll();return K=new Ke({props:{icon:"clear-filter"}}),{c(){e=P("button"),re(t.$$.fragment),n=X(),s=Ae(i),r=X(),he&&he.c(),o=X(),_=P("button"),re(u.$$.fragment),a=X(),c=Ae(f),g=X(),D&&D.c(),M=X(),w=P("button"),re(C.$$.fragment),p=X(),k=Ae(y),T=X(),O=P("button"),re(L.$$.fragment),B=X(),F=Ae(z),ae=X(),ee&&ee.c(),R=X(),Q=P("button"),re(K.$$.fragment),te=X(),$=Ae(fe),this.h()},l(v){e=U(v,"BUTTON",{role:!0,class:!0});var I=S(e);_e(t.$$.fragment,I),n=G(I),s=Be(I,i),r=G(I),he&&he.l(I),I.forEach(d),o=G(v),_=U(v,"BUTTON",{role:!0,class:!0});var b=S(_);_e(u.$$.fragment,b),a=G(b),c=Be(b,f),g=G(b),D&&D.l(b),b.forEach(d),M=G(v),w=U(v,"BUTTON",{role:!0,class:!0});var Z=S(w);_e(C.$$.fragment,Z),p=G(Z),k=Be(Z,y),Z.forEach(d),T=G(v),O=U(v,"BUTTON",{role:!0,class:!0});var be=S(O);_e(L.$$.fragment,be),B=G(be),F=Be(be,z),ae=G(be),ee&&ee.l(be),be.forEach(d),R=G(v),Q=U(v,"BUTTON",{role:!0,class:!0});var Le=S(Q);_e(K.$$.fragment,Le),te=G(Le),$=Be(Le,fe),Le.forEach(d),this.h()},h(){h(e,"role","menuitem"),h(e,"class","svelte-42thj4"),Y(e,"active",l[10]==="asc"),h(_,"role","menuitem"),h(_,"class","svelte-42thj4"),Y(_,"active",l[10]==="desc"),h(w,"role","menuitem"),h(w,"class","svelte-42thj4"),h(O,"role","menuitem"),h(O,"class","svelte-42thj4"),Y(O,"active",l[14]||l[17]),h(Q,"role","menuitem"),h(Q,"class","svelte-42thj4")},m(v,I){q(v,e,I),se(t,e,null),N(e,n),N(e,s),N(e,r),he&&he.m(e,null),q(v,o,I),q(v,_,I),se(u,_,null),N(_,a),N(_,c),N(_,g),D&&D.m(_,null),q(v,M,I),q(v,w,I),se(C,w,null),N(w,p),N(w,k),q(v,T,I),q(v,O,I),se(L,O,null),N(O,B),N(O,F),N(O,ae),ee&&ee.m(O,null),q(v,R,I),q(v,Q,I),se(K,Q,null),N(Q,te),N(Q,$),ue=!0,ye||(V=[ce(e,"click",l[28]),ce(_,"click",l[29]),ce(w,"click",function(){Xe(l[9])&&l[9].apply(this,arguments)}),ce(O,"click",xe(l[21])),ce(Q,"click",function(){Xe(l[13])&&l[13].apply(this,arguments)})],ye=!0)},p(v,I){l=v,(!ue||I[0]&32768)&&i!==(i=l[15]("dataframe.sort_ascending")+"")&&Me(s,i),l[10]==="asc"&&l[11]!==null?he?he.p(l,I):(he=Il(l),he.c(),he.m(e,null)):he&&(he.d(1),he=null),(!ue||I[0]&1024)&&Y(e,"active",l[10]==="asc"),(!ue||I[0]&32768)&&f!==(f=l[15]("dataframe.sort_descending")+"")&&Me(c,f),l[10]==="desc"&&l[11]!==null?D?D.p(l,I):(D=Ol(l),D.c(),D.m(_,null)):D&&(D.d(1),D=null),(!ue||I[0]&1024)&&Y(_,"active",l[10]==="desc"),(!ue||I[0]&32768)&&y!==(y=l[15]("dataframe.clear_sort")+"")&&Me(k,y),(!ue||I[0]&32768)&&z!==(z=l[15]("dataframe.filter")+"")&&Me(F,z),l[14]?ee||(ee=Ll(),ee.c(),ee.m(O,null)):ee&&(ee.d(1),ee=null),(!ue||I[0]&147456)&&Y(O,"active",l[14]||l[17]),(!ue||I[0]&32768)&&fe!==(fe=l[15]("dataframe.clear_filter")+"")&&Me($,fe)},i(v){ue||(E(t.$$.fragment,v),E(u.$$.fragment,v),E(C.$$.fragment,v),E(L.$$.fragment,v),E(K.$$.fragment,v),ue=!0)},o(v){A(t.$$.fragment,v),A(u.$$.fragment,v),A(C.$$.fragment,v),A(L.$$.fragment,v),A(K.$$.fragment,v),ue=!1},d(v){v&&(d(e),d(o),d(_),d(M),d(w),d(T),d(O),d(R),d(Q)),oe(t),he&&he.d(),oe(u),D&&D.d(),oe(C),oe(L),ee&&ee.d(),oe(K),ye=!1,Qe(V)}}}function Il(l){let e,t;return{c(){e=P("span"),t=Ae(l[11]),this.h()},l(n){e=U(n,"SPAN",{class:!0});var i=S(e);t=Be(i,l[11]),i.forEach(d),this.h()},h(){h(e,"class","priority svelte-42thj4")},m(n,i){q(n,e,i),N(e,t)},p(n,i){i[0]&2048&&Me(t,n[11])},d(n){n&&d(e)}}}function Ol(l){let e,t;return{c(){e=P("span"),t=Ae(l[11]),this.h()},l(n){e=U(n,"SPAN",{class:!0});var i=S(e);t=Be(i,l[11]),i.forEach(d),this.h()},h(){h(e,"class","priority svelte-42thj4")},m(n,i){q(n,e,i),N(e,t)},p(n,i){i[0]&2048&&Me(t,n[11])},d(n){n&&d(e)}}}function Ll(l){let e,t="1";return{c(){e=P("span"),e.textContent=t,this.h()},l(n){e=U(n,"SPAN",{class:!0,"data-svelte-h":!0}),lt(e)!=="svelte-1abh2by"&&(e.textContent=t),this.h()},h(){h(e,"class","priority svelte-42thj4")},m(n,i){q(n,e,i)},d(n){n&&d(e)}}}function jl(l){let e,t,n,i=l[15]("dataframe.add_row_above")+"",s,r,o,_,u,a=l[15]("dataframe.add_row_below")+"",f,c,g,M,w,C;t=new Ke({props:{icon:"add-row-above"}}),_=new Ke({props:{icon:"add-row-below"}});let p=l[6]&&ql(l);return{c(){e=P("button"),re(t.$$.fragment),n=X(),s=Ae(i),r=X(),o=P("button"),re(_.$$.fragment),u=X(),f=Ae(a),c=X(),p&&p.c(),g=ke(),this.h()},l(y){e=U(y,"BUTTON",{role:!0,"aria-label":!0,class:!0});var k=S(e);_e(t.$$.fragment,k),n=G(k),s=Be(k,i),k.forEach(d),r=G(y),o=U(y,"BUTTON",{role:!0,"aria-label":!0,class:!0});var T=S(o);_e(_.$$.fragment,T),u=G(T),f=Be(T,a),T.forEach(d),c=G(y),p&&p.l(y),g=ke(),this.h()},h(){h(e,"role","menuitem"),h(e,"aria-label","Add row above"),h(e,"class","svelte-42thj4"),h(o,"role","menuitem"),h(o,"aria-label","Add row below"),h(o,"class","svelte-42thj4")},m(y,k){q(y,e,k),se(t,e,null),N(e,n),N(e,s),q(y,r,k),q(y,o,k),se(_,o,null),N(o,u),N(o,f),q(y,c,k),p&&p.m(y,k),q(y,g,k),M=!0,w||(C=[ce(e,"click",l[30]),ce(o,"click",l[31])],w=!0)},p(y,k){(!M||k[0]&32768)&&i!==(i=y[15]("dataframe.add_row_above")+"")&&Me(s,i),(!M||k[0]&32768)&&a!==(a=y[15]("dataframe.add_row_below")+"")&&Me(f,a),y[6]?p?(p.p(y,k),k[0]&64&&E(p,1)):(p=ql(y),p.c(),E(p,1),p.m(g.parentNode,g)):p&&(me(),A(p,1,1,()=>{p=null}),ge())},i(y){M||(E(t.$$.fragment,y),E(_.$$.fragment,y),E(p),M=!0)},o(y){A(t.$$.fragment,y),A(_.$$.fragment,y),A(p),M=!1},d(y){y&&(d(e),d(r),d(o),d(c),d(g)),oe(t),oe(_),p&&p.d(y),w=!1,Qe(C)}}}function ql(l){let e,t,n,i=l[15]("dataframe.delete_row")+"",s,r,o,_;return t=new Ke({props:{icon:"delete-row"}}),{c(){e=P("button"),re(t.$$.fragment),n=X(),s=Ae(i),this.h()},l(u){e=U(u,"BUTTON",{role:!0,class:!0,"aria-label":!0});var a=S(e);_e(t.$$.fragment,a),n=G(a),s=Be(a,i),a.forEach(d),this.h()},h(){h(e,"role","menuitem"),h(e,"class","delete svelte-42thj4"),h(e,"aria-label","Delete row")},m(u,a){q(u,e,a),se(t,e,null),N(e,n),N(e,s),r=!0,o||(_=ce(e,"click",function(){Xe(l[4])&&l[4].apply(this,arguments)}),o=!0)},p(u,a){l=u,(!r||a[0]&32768)&&i!==(i=l[15]("dataframe.delete_row")+"")&&Me(s,i)},i(u){r||(E(t.$$.fragment,u),r=!0)},o(u){A(t.$$.fragment,u),r=!1},d(u){u&&d(e),oe(t),o=!1,_()}}}function Hl(l){let e,t,n,i=l[15]("dataframe.add_column_left")+"",s,r,o,_,u,a=l[15]("dataframe.add_column_right")+"",f,c,g,M,w,C;t=new Ke({props:{icon:"add-column-left"}}),_=new Ke({props:{icon:"add-column-right"}});let p=l[7]&&Vl(l);return{c(){e=P("button"),re(t.$$.fragment),n=X(),s=Ae(i),r=X(),o=P("button"),re(_.$$.fragment),u=X(),f=Ae(a),c=X(),p&&p.c(),g=ke(),this.h()},l(y){e=U(y,"BUTTON",{role:!0,"aria-label":!0,class:!0});var k=S(e);_e(t.$$.fragment,k),n=G(k),s=Be(k,i),k.forEach(d),r=G(y),o=U(y,"BUTTON",{role:!0,"aria-label":!0,class:!0});var T=S(o);_e(_.$$.fragment,T),u=G(T),f=Be(T,a),T.forEach(d),c=G(y),p&&p.l(y),g=ke(),this.h()},h(){h(e,"role","menuitem"),h(e,"aria-label","Add column to the left"),h(e,"class","svelte-42thj4"),h(o,"role","menuitem"),h(o,"aria-label","Add column to the right"),h(o,"class","svelte-42thj4")},m(y,k){q(y,e,k),se(t,e,null),N(e,n),N(e,s),q(y,r,k),q(y,o,k),se(_,o,null),N(o,u),N(o,f),q(y,c,k),p&&p.m(y,k),q(y,g,k),M=!0,w||(C=[ce(e,"click",l[32]),ce(o,"click",l[33])],w=!0)},p(y,k){(!M||k[0]&32768)&&i!==(i=y[15]("dataframe.add_column_left")+"")&&Me(s,i),(!M||k[0]&32768)&&a!==(a=y[15]("dataframe.add_column_right")+"")&&Me(f,a),y[7]?p?(p.p(y,k),k[0]&128&&E(p,1)):(p=Vl(y),p.c(),E(p,1),p.m(g.parentNode,g)):p&&(me(),A(p,1,1,()=>{p=null}),ge())},i(y){M||(E(t.$$.fragment,y),E(_.$$.fragment,y),E(p),M=!0)},o(y){A(t.$$.fragment,y),A(_.$$.fragment,y),A(p),M=!1},d(y){y&&(d(e),d(r),d(o),d(c),d(g)),oe(t),oe(_),p&&p.d(y),w=!1,Qe(C)}}}function Vl(l){let e,t,n,i=l[15]("dataframe.delete_column")+"",s,r,o,_;return t=new Ke({props:{icon:"delete-column"}}),{c(){e=P("button"),re(t.$$.fragment),n=X(),s=Ae(i),this.h()},l(u){e=U(u,"BUTTON",{role:!0,class:!0,"aria-label":!0});var a=S(e);_e(t.$$.fragment,a),n=G(a),s=Be(a,i),a.forEach(d),this.h()},h(){h(e,"role","menuitem"),h(e,"class","delete svelte-42thj4"),h(e,"aria-label","Delete column")},m(u,a){q(u,e,a),se(t,e,null),N(e,n),N(e,s),r=!0,o||(_=ce(e,"click",function(){Xe(l[5])&&l[5].apply(this,arguments)}),o=!0)},p(u,a){l=u,(!r||a[0]&32768)&&i!==(i=l[15]("dataframe.delete_column")+"")&&Me(s,i)},i(u){r||(E(t.$$.fragment,u),r=!0)},o(u){A(t.$$.fragment,u),r=!1},d(u){u&&d(e),oe(t),o=!1,_()}}}function zl(l){let e,t;return e=new ms({props:{on_filter:l[12]}}),{c(){re(e.$$.fragment)},l(n){_e(e.$$.fragment,n)},m(n,i){se(e,n,i),t=!0},p(n,i){const s={};i[0]&4096&&(s.on_filter=n[12]),e.$set(s)},i(n){t||(E(e.$$.fragment,n),t=!0)},o(n){A(e.$$.fragment,n),t=!1},d(n){oe(e,n)}}}function gs(l){let e,t,n,i,s,r,o=l[20]&&Dl(l),_=!l[20]&&l[19]&&jl(l),u=l[18]&&Hl(l),a=l[17]&&zl(l);return{c(){e=P("div"),o&&o.c(),t=X(),_&&_.c(),n=X(),u&&u.c(),i=X(),a&&a.c(),s=ke(),this.h()},l(f){e=U(f,"DIV",{class:!0,role:!0});var c=S(e);o&&o.l(c),t=G(c),_&&_.l(c),n=G(c),u&&u.l(c),c.forEach(d),i=G(f),a&&a.l(f),s=ke(),this.h()},h(){h(e,"class","cell-menu svelte-42thj4"),h(e,"role","menu")},m(f,c){q(f,e,c),o&&o.m(e,null),N(e,t),_&&_.m(e,null),N(e,n),u&&u.m(e,null),l[34](e),q(f,i,c),a&&a.m(f,c),q(f,s,c),r=!0},p(f,c){f[20]?o?(o.p(f,c),c[0]&1048576&&E(o,1)):(o=Dl(f),o.c(),E(o,1),o.m(e,t)):o&&(me(),A(o,1,1,()=>{o=null}),ge()),!f[20]&&f[19]?_?(_.p(f,c),c[0]&1572864&&E(_,1)):(_=jl(f),_.c(),E(_,1),_.m(e,n)):_&&(me(),A(_,1,1,()=>{_=null}),ge()),f[18]?u?(u.p(f,c),c[0]&262144&&E(u,1)):(u=Hl(f),u.c(),E(u,1),u.m(e,null)):u&&(me(),A(u,1,1,()=>{u=null}),ge()),f[17]?a?(a.p(f,c),c[0]&131072&&E(a,1)):(a=zl(f),a.c(),E(a,1),a.m(s.parentNode,s)):a&&(me(),A(a,1,1,()=>{a=null}),ge())},i(f){r||(E(o),E(_),E(u),E(a),r=!0)},o(f){A(o),A(_),A(u),A(a),r=!1},d(f){f&&(d(e),d(i),d(s)),o&&o.d(),_&&_.d(),u&&u.d(),l[34](null),a&&a.d(f)}}}function bs(l,e,t){let n,i,s,{x:r}=e,{y:o}=e,{on_add_row_above:_}=e,{on_add_row_below:u}=e,{on_add_column_left:a}=e,{on_add_column_right:f}=e,{row:c}=e,{col_count:g}=e,{row_count:M}=e,{on_delete_row:w}=e,{on_delete_col:C}=e,{can_delete_rows:p}=e,{can_delete_cols:y}=e,{on_sort:k=()=>{}}=e,{on_clear_sort:T=()=>{}}=e,{sort_direction:O=null}=e,{sort_priority:L=null}=e,{on_filter:B=()=>{}}=e,{on_clear_filter:z=()=>{}}=e,{filter_active:F=null}=e,{editable:ae=!0}=e,{i18n:R}=e,Q,K=null;_t(()=>{te()});function te(){if(!Q)return;const v=window.innerWidth,I=window.innerHeight,b=Q.getBoundingClientRect();let Z=r-30,be=o-20;Z+b.width>v&&(Z=r-b.width+10),be+b.height>I&&(be=o-b.height+10),t(16,Q.style.left=`${Z}px`,Q),t(16,Q.style.top=`${be}px`,Q)}function fe(){if(F){B("string","","");return}const v=Q.getBoundingClientRect();t(17,K={x:v.right,y:v.top+v.height/2})}const $=()=>k("asc"),ue=()=>k("desc"),ye=()=>_(),V=()=>u(),he=()=>a(),D=()=>f();function ee(v){Ee[v?"unshift":"push"](()=>{Q=v,t(16,Q)})}return l.$$set=v=>{"x"in v&&t(22,r=v.x),"y"in v&&t(23,o=v.y),"on_add_row_above"in v&&t(0,_=v.on_add_row_above),"on_add_row_below"in v&&t(1,u=v.on_add_row_below),"on_add_column_left"in v&&t(2,a=v.on_add_column_left),"on_add_column_right"in v&&t(3,f=v.on_add_column_right),"row"in v&&t(24,c=v.row),"col_count"in v&&t(25,g=v.col_count),"row_count"in v&&t(26,M=v.row_count),"on_delete_row"in v&&t(4,w=v.on_delete_row),"on_delete_col"in v&&t(5,C=v.on_delete_col),"can_delete_rows"in v&&t(6,p=v.can_delete_rows),"can_delete_cols"in v&&t(7,y=v.can_delete_cols),"on_sort"in v&&t(8,k=v.on_sort),"on_clear_sort"in v&&t(9,T=v.on_clear_sort),"sort_direction"in v&&t(10,O=v.sort_direction),"sort_priority"in v&&t(11,L=v.sort_priority),"on_filter"in v&&t(12,B=v.on_filter),"on_clear_filter"in v&&t(13,z=v.on_clear_filter),"filter_active"in v&&t(14,F=v.filter_active),"editable"in v&&t(27,ae=v.editable),"i18n"in v&&t(15,R=v.i18n)},l.$$.update=()=>{l.$$.dirty[0]&16777216&&t(20,n=c===-1),l.$$.dirty[0]&201326592&&t(19,i=ae&&M[1]==="dynamic"),l.$$.dirty[0]&167772160&&t(18,s=ae&&g[1]==="dynamic")},[_,u,a,f,w,C,p,y,k,T,O,L,B,z,F,R,Q,K,s,i,n,fe,r,o,c,g,M,ae,$,ue,ye,V,he,D,ee]}class ws extends qe{constructor(e){super(),He(this,e,bs,gs,Ve,{x:22,y:23,on_add_row_above:0,on_add_row_below:1,on_add_column_left:2,on_add_column_right:3,row:24,col_count:25,row_count:26,on_delete_row:4,on_delete_col:5,can_delete_rows:6,can_delete_cols:7,on_sort:8,on_clear_sort:9,sort_direction:10,sort_priority:11,on_filter:12,on_clear_filter:13,filter_active:14,editable:27,i18n:15},null,[-1,-1])}}function Pl(l){let e,t,n,i,s,r,o,_,u,a=l[0]&&l[3]==="filter"&&Ul(l);return{c(){e=P("div"),t=P("input"),r=X(),a&&a.c(),this.h()},l(f){e=U(f,"DIV",{class:!0});var c=S(e);t=U(c,"INPUT",{type:!0,placeholder:!0,class:!0,title:!0}),r=G(c),a&&a.l(c),c.forEach(d),this.h()},h(){h(t,"type","text"),t.value=n=l[0]||"",h(t,"placeholder",i=l[3]==="filter"?"Filter...":"Search..."),h(t,"class","search-input svelte-b1nr0g"),h(t,"title",s=`Enter text to ${l[3]} the table`),Y(t,"filter-mode",l[3]==="filter"),h(e,"class","search-container svelte-b1nr0g")},m(f,c){q(f,e,c),N(e,t),N(e,r),a&&a.m(e,null),o=!0,_||(u=ce(t,"input",l[7]),_=!0)},p(f,c){(!o||c&1&&n!==(n=f[0]||"")&&t.value!==n)&&(t.value=n),(!o||c&8&&i!==(i=f[3]==="filter"?"Filter...":"Search..."))&&h(t,"placeholder",i),(!o||c&8&&s!==(s=`Enter text to ${f[3]} the table`))&&h(t,"title",s),(!o||c&8)&&Y(t,"filter-mode",f[3]==="filter"),f[0]&&f[3]==="filter"?a?(a.p(f,c),c&9&&E(a,1)):(a=Ul(f),a.c(),E(a,1),a.m(e,null)):a&&(me(),A(a,1,1,()=>{a=null}),ge())},i(f){o||(E(a),o=!0)},o(f){A(a),o=!1},d(f){f&&d(e),a&&a.d(),_=!1,u()}}}function Ul(l){let e,t,n,i,s;return t=new Jt({}),{c(){e=P("button"),re(t.$$.fragment),this.h()},l(r){e=U(r,"BUTTON",{class:!0,"aria-label":!0,title:!0});var o=S(e);_e(t.$$.fragment,o),o.forEach(d),this.h()},h(){h(e,"class","toolbar-button check-button svelte-b1nr0g"),h(e,"aria-label","Apply filter and update dataframe values"),h(e,"title","Apply filter and update dataframe values")},m(r,o){q(r,e,o),se(t,e,null),n=!0,i||(s=ce(e,"click",function(){Xe(l[5])&&l[5].apply(this,arguments)}),i=!0)},p(r,o){l=r},i(r){n||(E(t.$$.fragment,r),n=!0)},o(r){A(t.$$.fragment,r),n=!1},d(r){r&&d(e),oe(t),i=!1,s()}}}function Rl(l){let e,t,n,i,s,r,o,_;const u=[vs,ks],a=[];function f(c,g){return c[6]?0:1}return t=f(l),n=a[t]=u[t](l),{c(){e=P("button"),n.c(),this.h()},l(c){e=U(c,"BUTTON",{class:!0,"aria-label":!0,title:!0});var g=S(e);n.l(g),g.forEach(d),this.h()},h(){h(e,"class","toolbar-button svelte-b1nr0g"),h(e,"aria-label",i=l[6]?"Copied to clipboard":"Copy table data"),h(e,"title",s=l[6]?"Copied to clipboard":"Copy table data")},m(c,g){q(c,e,g),a[t].m(e,null),r=!0,o||(_=ce(e,"click",l[8]),o=!0)},p(c,g){let M=t;t=f(c),t!==M&&(me(),A(a[M],1,1,()=>{a[M]=null}),ge(),n=a[t],n||(n=a[t]=u[t](c),n.c()),E(n,1),n.m(e,null)),(!r||g&64&&i!==(i=c[6]?"Copied to clipboard":"Copy table data"))&&h(e,"aria-label",i),(!r||g&64&&s!==(s=c[6]?"Copied to clipboard":"Copy table data"))&&h(e,"title",s)},i(c){r||(E(n),r=!0)},o(c){A(n),r=!1},d(c){c&&d(e),a[t].d(),o=!1,_()}}}function ks(l){let e,t;return e=new Ai({}),{c(){re(e.$$.fragment)},l(n){_e(e.$$.fragment,n)},m(n,i){se(e,n,i),t=!0},i(n){t||(E(e.$$.fragment,n),t=!0)},o(n){A(e.$$.fragment,n),t=!1},d(n){oe(e,n)}}}function vs(l){let e,t;return e=new Jt({}),{c(){re(e.$$.fragment)},l(n){_e(e.$$.fragment,n)},m(n,i){se(e,n,i),t=!0},i(n){t||(E(e.$$.fragment,n),t=!0)},o(n){A(e.$$.fragment,n),t=!1},d(n){oe(e,n)}}}function Jl(l){let e,t;return e=new ji({props:{fullscreen:l[4]}}),e.$on("fullscreen",l[10]),{c(){re(e.$$.fragment)},l(n){_e(e.$$.fragment,n)},m(n,i){se(e,n,i),t=!0},p(n,i){const s={};i&16&&(s.fullscreen=n[4]),e.$set(s)},i(n){t||(E(e.$$.fragment,n),t=!0)},o(n){A(e.$$.fragment,n),t=!1},d(n){oe(e,n)}}}function ps(l){let e,t,n,i,s,r=l[3]!=="none"&&Pl(l),o=l[2]&&Rl(l),_=l[1]&&Jl(l);return{c(){e=P("div"),t=P("div"),r&&r.c(),n=X(),o&&o.c(),i=X(),_&&_.c(),this.h()},l(u){e=U(u,"DIV",{class:!0,role:!0,"aria-label":!0});var a=S(e);t=U(a,"DIV",{class:!0});var f=S(t);r&&r.l(f),n=G(f),o&&o.l(f),i=G(f),_&&_.l(f),f.forEach(d),a.forEach(d),this.h()},h(){h(t,"class","toolbar-buttons svelte-b1nr0g"),h(e,"class","toolbar svelte-b1nr0g"),h(e,"role","toolbar"),h(e,"aria-label","Table actions")},m(u,a){q(u,e,a),N(e,t),r&&r.m(t,null),N(t,n),o&&o.m(t,null),N(t,i),_&&_.m(t,null),s=!0},p(u,[a]){u[3]!=="none"?r?(r.p(u,a),a&8&&E(r,1)):(r=Pl(u),r.c(),E(r,1),r.m(t,n)):r&&(me(),A(r,1,1,()=>{r=null}),ge()),u[2]?o?(o.p(u,a),a&4&&E(o,1)):(o=Rl(u),o.c(),E(o,1),o.m(t,i)):o&&(me(),A(o,1,1,()=>{o=null}),ge()),u[1]?_?(_.p(u,a),a&2&&E(_,1)):(_=Jl(u),_.c(),E(_,1),_.m(t,null)):_&&(me(),A(_,1,1,()=>{_=null}),ge())},i(u){s||(E(r),E(o),E(_),s=!0)},o(u){A(r),A(o),A(_),s=!1},d(u){u&&d(e),r&&r.d(),o&&o.d(),_&&_.d()}}}function ys(l,e,t){let{show_fullscreen_button:n=!1}=e,{show_copy_button:i=!1}=e,{show_search:s="none"}=e,{fullscreen:r=!1}=e,{on_copy:o}=e,{on_commit_filter:_}=e;const u=pt();let a=!1,f,{current_search_query:c=null}=e,g="";function M(y){g=y.target.value;const T=g||null;c!==T&&(t(0,c=T),u("search",c))}function w(){t(6,a=!0),f&&clearTimeout(f),f=setTimeout(()=>{t(6,a=!1)},2e3)}async function C(){await o(),w()}ki(()=>{f&&clearTimeout(f)});function p(y){it.call(this,l,y)}return l.$$set=y=>{"show_fullscreen_button"in y&&t(1,n=y.show_fullscreen_button),"show_copy_button"in y&&t(2,i=y.show_copy_button),"show_search"in y&&t(3,s=y.show_search),"fullscreen"in y&&t(4,r=y.fullscreen),"on_copy"in y&&t(9,o=y.on_copy),"on_commit_filter"in y&&t(5,_=y.on_commit_filter),"current_search_query"in y&&t(0,c=y.current_search_query)},[c,n,i,s,r,_,a,M,C,o,p]}class Es extends qe{constructor(e){super(),He(this,e,ys,ps,Ve,{show_fullscreen_button:1,show_copy_button:2,show_search:3,fullscreen:4,on_copy:9,on_commit_filter:5,current_search_query:0})}}function kt(l,e,t,n){let i=l||[];if(e[1]==="fixed"&&i.length<e[0]){const s=Array(e[0]-i.length).fill("").map((r,o)=>`${o+i.length}`);i=i.concat(s)}return!i||i.length===0?Array(e[0]).fill(0).map((s,r)=>{const o=n();return t[o]={cell:null,input:null},{id:o,value:JSON.stringify(r+1)}}):i.map((s,r)=>{const o=n();return t[o]={cell:null,input:null},{id:o,value:s??""}})}function Ns(l,e,t,n,i=null){return!l||l.length===0?[]:l.map((r,o)=>r.map((_,u)=>{var c;const a=n();e[a]={cell:null,input:null},t[a]=_;let f=(c=i==null?void 0:i[o])==null?void 0:c[u];return f===void 0&&(f=String(_)),{id:a,value:_,display_value:f}}))}function Ts(l,e){if(e==="number"){const t=Number(l);return isNaN(t)?l:t}if(e==="bool"){if(typeof l=="boolean")return l;if(typeof l=="number")return l!==0;const t=String(l).toLowerCase();return t==="true"||t==="1"?!0:t==="false"||t==="0"?!1:l}if(e==="date"){const t=new Date(l);return isNaN(t.getTime())?l:t.toISOString()}return l}async function mn(l,e,t,n){var s;if(!e.data||!e.data[t]||!e.data[t][n])return;const i=e.data[t][n].value;e.data[t][n].value=l,i!==l&&e.dispatch&&e.dispatch("change",{data:e.data.map(r=>r.map(o=>o.value)),headers:((s=e.headers)==null?void 0:s.map(r=>r.value))||[],metadata:null}),e.actions.set_selected([t,n])}async function Cs(l,e,t){if(!e.data||!e.headers||!e.els)return;const n=l.target;!n||n.value===void 0||await mn(n.type==="checkbox"?String(n.checked):n.value,e,t[0],t[1])}function Ss(l,e){const t=ze(e.state),n=t.ui_state.selected_header,i=t.ui_state.header_edit,s=e.headers||[];if(n===!1||i!==!1)return!1;switch(l.key){case"ArrowDown":return e.actions.set_selected_header(!1),e.actions.set_selected([0,n]),e.actions.set_selected_cells([[0,n]]),!0;case"ArrowLeft":return e.actions.set_selected_header(n>0?n-1:n),!0;case"ArrowRight":return e.actions.set_selected_header(n<s.length-1?n+1:n),!0;case"Escape":return l.preventDefault(),e.actions.set_selected_header(!1),!0;case"Enter":return l.preventDefault(),t.config.editable&&e.actions.set_header_edit(n),!0}return!1}function gn(l,e){var r;if(!e.data||!e.headers||!e.els||!e.dispatch)return!1;const t=ze(e.state);if(!t.config.editable||l.key!=="Delete"&&l.key!=="Backspace")return!1;const n=t.ui_state.editing,i=t.ui_state.selected_cells,s=t.config.static_columns||[];if(i.some(([o,_])=>s.includes(_)))return!1;if(n){const[o,_]=n,u=(r=e.els[e.data[o][_].id])==null?void 0:r.input;if(u&&u.selectionStart!==u.selectionEnd||l.key==="Delete"&&(u==null?void 0:u.selectionStart)!==(u==null?void 0:u.value.length)||l.key==="Backspace"&&(u==null?void 0:u.selectionStart)!==0)return!1}if(l.preventDefault(),i.length>0){const o=Gi(e.data,i);e.dispatch("change",{data:o.map(_=>_.map(u=>u.value)),headers:e.headers.map(_=>_.value),metadata:null})}return!0}function As(l,e,t,n){const i=ze(e.state),s=i.ui_state.editing,r=i.ui_state.selected_cells;if(s||!e.data)return!1;l.preventDefault();const o=e.actions.move_cursor(l,[t,n],e.data);return o?(l.shiftKey?(e.actions.set_selected_cells(e.actions.get_range_selection(r.length>0?r[0]:[t,n],o)),e.actions.set_editing(!1)):(e.actions.set_selected_cells([o]),e.actions.set_editing(!1)),e.actions.set_selected(o)):o===!1&&l.key==="ArrowUp"&&t===0&&(e.actions.set_selected_header(n),e.actions.set_selected(!1),e.actions.set_selected_cells([]),e.actions.set_editing(!1)),!0}async function Bs(l,e,t,n){var r,o;if(!e.data||!e.els)return!1;const i=ze(e.state);if(!i.config.editable)return!1;const s=i.ui_state.editing;if(s&&l.shiftKey)return!1;if(l.preventDefault(),s&&ut(s,[t,n])){const _=e.data[t][n].id,u=(r=e.els[_])==null?void 0:r.input;u&&await mn(u.value,e,t,n),e.actions.set_editing(!1),await rt(),(o=e.parent_element)==null||o.focus()}else e.actions.set_editing([t,n]);return!0}function Ms(l,e,t,n){if(!e.data)return!1;l.preventDefault(),e.actions.set_editing(!1);const i=e.actions.get_next_cell_coordinates([t,n],e.data,l.shiftKey);return i&&(e.actions.set_selected_cells([i]),e.actions.set_selected(i),ze(e.state).config.editable&&e.actions.set_editing(i)),!0}function Ds(l,e,t,n){const i=ze(e.state);if(!i.config.editable)return!1;const s=i.ui_state.editing;return(!s||s&&ut(s,[t,n]))&&l.key.length===1?(e.actions.set_editing([t,n]),!0):!1}async function Is(l,e){if(!e.data)return!1;const t=ze(e.state),n=t.ui_state.selected,i=t.ui_state.selected_cells;if(!n)return!1;if(l.key==="c"&&(l.metaKey||l.ctrlKey))return l.preventDefault(),i.length>0&&await cn(e.data,i),e.actions.set_copy_flash(!0),!0;const[s,r]=n;switch(l.key){case"ArrowRight":case"ArrowLeft":case"ArrowDown":case"ArrowUp":return As(l,e,s,r);case"Escape":return t.config.editable?(l.preventDefault(),e.actions.set_editing(!1),rt().then(()=>{e.parent_element&&e.parent_element.focus()}),!0):!1;case"Enter":return await Bs(l,e,s,r);case"Tab":return Ms(l,e,s,r);case"Delete":case"Backspace":return gn(l,e);default:return Ds(l,e,s,r)}}async function Os(l,e){Ss(l,e)||gn(l,e)||await Is(l,e)}function Ls(l,e,t,n,i,s,r){const o=(a,f,c)=>{const g=a.target,M=g.type==="checkbox"||g.closest('input[type="checkbox"]')||g.closest(".bool-cell");a.target instanceof HTMLAnchorElement||s&&c===-1||M||(a.preventDefault(),a.stopPropagation(),l.mouse_down_pos={x:a.clientX,y:a.clientY},l.drag_start=[f,c],!a.shiftKey&&!a.metaKey&&!a.ctrlKey&&(t([[f,c]]),n([f,c]),i(a,f,c)))},_=a=>{const f=a.target.closest("td");if(!f)return;const c=parseInt(f.getAttribute("data-row")||"0"),g=parseInt(f.getAttribute("data-col")||"0");if(isNaN(c)||isNaN(g))return;const M=Ft(l.drag_start,[c,g]);t(M),n([c,g])},u=a=>{!l.is_dragging&&l.drag_start?i(a,l.drag_start[0],l.drag_start[1]):l.is_dragging&&r&&r.focus(),l.is_dragging=!1,e(!1),l.drag_start=null,l.mouse_down_pos=null};return{handle_mouse_down:o,handle_mouse_move(a){if(!l.drag_start||!l.mouse_down_pos)return;if(!(a.buttons&1)){u(a);return}const f=Math.abs(a.clientX-l.mouse_down_pos.x),c=Math.abs(a.clientY-l.mouse_down_pos.y);!l.is_dragging&&(f>3||c>3)&&(l.is_dragging=!0,e(!0)),l.is_dragging&&_(a)},handle_mouse_up:u}}const{Map:vt,window:js}=yi;function Fl(l,e,t){const n=l.slice();return n[146]=e[t].value,n[147]=e[t].id,n[150]=e,n[151]=t,n}function Kl(l,e,t){const n=l.slice();return n[146]=e[t].value,n[147]=e[t].id,n[148]=e,n[149]=t,n}function Wl(l,e,t){const n=l.slice();return n[146]=e[t].value,n[147]=e[t].id,n[152]=e,n[149]=t,n}function Yl(l,e,t){const n=l.slice();return n[146]=e[t].value,n[147]=e[t].id,n[153]=e,n[151]=t,n}function Xl(l){let e,t,n,i,s=l[3]&&l[3].length!==0&&l[4]&&Gl(l);return n=new Es({props:{show_fullscreen_button:l[18],fullscreen:l[23],on_copy:l[89],show_copy_button:l[19],show_search:l[21],on_commit_filter:l[68],current_search_query:l[33].current_search_query}}),n.$on("search",l[90]),n.$on("fullscreen",l[91]),{c(){e=P("div"),s&&s.c(),t=X(),re(n.$$.fragment),this.h()},l(r){e=U(r,"DIV",{class:!0});var o=S(e);s&&s.l(o),t=G(o),_e(n.$$.fragment,o),o.forEach(d),this.h()},h(){h(e,"class","header-row svelte-1vwr9xf")},m(r,o){q(r,e,o),s&&s.m(e,null),N(e,t),se(n,e,null),i=!0},p(r,o){r[3]&&r[3].length!==0&&r[4]?s?s.p(r,o):(s=Gl(r),s.c(),s.m(e,t)):s&&(s.d(1),s=null);const _={};o[0]&262144&&(_.show_fullscreen_button=r[18]),o[0]&8388608&&(_.fullscreen=r[23]),o[0]&67108864&&(_.on_copy=r[89]),o[0]&524288&&(_.show_copy_button=r[19]),o[0]&2097152&&(_.show_search=r[21]),o[1]&4&&(_.current_search_query=r[33].current_search_query),n.$set(_)},i(r){i||(E(n.$$.fragment,r),i=!0)},o(r){A(n.$$.fragment,r),i=!1},d(r){r&&d(e),s&&s.d(),oe(n)}}}function Gl(l){let e,t,n;return{c(){e=P("div"),t=P("p"),n=Ae(l[3]),this.h()},l(i){e=U(i,"DIV",{class:!0});var s=S(e);t=U(s,"P",{class:!0});var r=S(t);n=Be(r,l[3]),r.forEach(d),s.forEach(d),this.h()},h(){h(t,"class","svelte-1vwr9xf"),h(e,"class","label svelte-1vwr9xf")},m(i,s){q(i,e,s),N(e,t),N(t,n)},p(i,s){s[0]&8&&Me(n,i[3])},d(i){i&&d(e)}}}function Ql(l){let e,t;return{c(){e=P("caption"),t=Ae(l[3]),this.h()},l(n){e=U(n,"CAPTION",{class:!0});var i=S(e);t=Be(i,l[3]),i.forEach(d),this.h()},h(){h(e,"class","sr-only svelte-1vwr9xf")},m(n,i){q(n,e,i),N(e,t)},p(n,i){i[0]&8&&Me(t,n[3])},d(n){n&&d(e)}}}function Zl(l){let e,t;return e=new yt({props:{is_header:!0}}),{c(){re(e.$$.fragment)},l(n){_e(e.$$.fragment,n)},m(n,i){se(e,n,i),t=!0},i(n){t||(E(e.$$.fragment,n),t=!0)},o(n){A(e.$$.fragment,n),t=!1},d(n){oe(e,n)}}}function xl(l,e){let t,n,i,s,r;function o(a){e[92](a,e[151])}function _(a){e[93](a,e[147])}let u={i:e[151],actual_pinned_columns:e[53],header_edit:e[52],selected_header:e[51],headers:e[0],get_cell_width:Wt,handle_header_click:e[60],toggle_header_menu:e[65],end_header_edit:e[61],sort_columns:e[33].sort_state.sort_columns,filter_columns:e[33].filter_state.filter_columns,latex_delimiters:e[7],line_breaks:e[14],max_chars:e[20],editable:e[9],is_static:e[22].includes(e[151]),i18n:e[12],col_count:e[5]};return e[25][e[151]].value!==void 0&&(u.value=e[25][e[151]].value),e[24][e[147]].input!==void 0&&(u.el=e[24][e[147]].input),n=new dn({props:u}),Ee.push(()=>Ie(n,"value",o)),Ee.push(()=>Ie(n,"el",_)),{key:l,first:null,c(){t=ke(),re(n.$$.fragment),this.h()},l(a){t=ke(),_e(n.$$.fragment,a),this.h()},h(){this.first=t},m(a,f){q(a,t,f),se(n,a,f),r=!0},p(a,f){e=a;const c={};f[0]&33554432&&(c.i=e[151]),f[1]&4194304&&(c.actual_pinned_columns=e[53]),f[1]&2097152&&(c.header_edit=e[52]),f[1]&1048576&&(c.selected_header=e[51]),f[0]&1&&(c.headers=e[0]),f[1]&4&&(c.sort_columns=e[33].sort_state.sort_columns),f[1]&4&&(c.filter_columns=e[33].filter_state.filter_columns),f[0]&128&&(c.latex_delimiters=e[7]),f[0]&16384&&(c.line_breaks=e[14]),f[0]&1048576&&(c.max_chars=e[20]),f[0]&512&&(c.editable=e[9]),f[0]&37748736&&(c.is_static=e[22].includes(e[151])),f[0]&4096&&(c.i18n=e[12]),f[0]&32&&(c.col_count=e[5]),!i&&f[0]&33554432&&(i=!0,c.value=e[25][e[151]].value,Oe(()=>i=!1)),!s&&f[0]&50331648&&(s=!0,c.el=e[24][e[147]].input,Oe(()=>s=!1)),n.$set(c)},i(a){r||(E(n.$$.fragment,a),r=!0)},o(a){A(n.$$.fragment,a),r=!1},d(a){a&&d(t),oe(n,a)}}}function $l(l){let e,t;return e=new yt({props:{index:0}}),{c(){re(e.$$.fragment)},l(n){_e(e.$$.fragment,n)},m(n,i){se(e,n,i),t=!0},i(n){t||(E(e.$$.fragment,n),t=!0)},o(n){A(e.$$.fragment,n),t=!1},d(n){oe(e,n)}}}function en(l,e){let t,n,i,s,r=e[149],o;i=new Kt({props:{value:e[146],latex_delimiters:e[7],line_breaks:e[14],datatype:Array.isArray(e[2])?e[2][e[149]]:e[2],edit:!1,el:null,editable:e[9],i18n:e[12],show_selection_buttons:e[31].length===1&&e[31][0][0]===0&&e[31][0][1]===e[149],coords:e[39],on_select_column:e[55].handle_select_column,on_select_row:e[55].handle_select_row,is_dragging:e[42]}}),i.$on("blur",e[64]);const _=()=>e[94](t,r),u=()=>e[94](null,r);return{key:l,first:null,c(){t=P("td"),n=P("div"),re(i.$$.fragment),s=X(),this.h()},l(a){t=U(a,"TD",{tabindex:!0,class:!0});var f=S(t);n=U(f,"DIV",{class:!0});var c=S(n);_e(i.$$.fragment,c),c.forEach(d),s=G(f),f.forEach(d),this.h()},h(){h(n,"class","cell-wrap svelte-1vwr9xf"),h(t,"tabindex","-1"),h(t,"class","svelte-1vwr9xf"),this.first=t},m(a,f){q(a,t,f),N(t,n),se(i,n,null),N(t,s),_(),o=!0},p(a,f){e=a;const c={};f[1]&524288&&(c.value=e[146]),f[0]&128&&(c.latex_delimiters=e[7]),f[0]&16384&&(c.line_breaks=e[14]),f[0]&4|f[1]&524288&&(c.datatype=Array.isArray(e[2])?e[2][e[149]]:e[2]),f[0]&512&&(c.editable=e[9]),f[0]&4096&&(c.i18n=e[12]),f[1]&524289&&(c.show_selection_buttons=e[31].length===1&&e[31][0][0]===0&&e[31][0][1]===e[149]),f[1]&256&&(c.coords=e[39]),f[1]&2048&&(c.is_dragging=e[42]),i.$set(c),r!==e[149]&&(u(),r=e[149],_())},i(a){o||(E(i.$$.fragment,a),o=!0)},o(a){A(i.$$.fragment,a),o=!1},d(a){a&&d(t),oe(i),u()}}}function tn(l){let e,t;return{c(){e=P("caption"),t=Ae(l[3]),this.h()},l(n){e=U(n,"CAPTION",{class:!0});var i=S(e);t=Be(i,l[3]),i.forEach(d),this.h()},h(){h(e,"class","sr-only svelte-1vwr9xf")},m(n,i){q(n,e,i),N(e,t)},p(n,i){i[0]&8&&Me(t,n[3])},d(n){n&&d(e)}}}function qs(l){let e,t=l[3]&&l[3].length!==0&&tn(l);return{c(){t&&t.c(),e=ke()},l(n){t&&t.l(n),e=ke()},m(n,i){t&&t.m(n,i),q(n,e,i)},p(n,i){n[3]&&n[3].length!==0?t?t.p(n,i):(t=tn(n),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null)},d(n){n&&d(e),t&&t.d(n)}}}function ln(l){let e,t;return e=new yt({props:{is_header:!0}}),{c(){re(e.$$.fragment)},l(n){_e(e.$$.fragment,n)},m(n,i){se(e,n,i),t=!0},i(n){t||(E(e.$$.fragment,n),t=!0)},o(n){A(e.$$.fragment,n),t=!1},d(n){oe(e,n)}}}function nn(l,e){let t,n,i,s,r;function o(a){e[99](a,e[151])}function _(a){e[100](a,e[147])}let u={i:e[151],actual_pinned_columns:e[53],header_edit:e[52],selected_header:e[51],headers:e[0],get_cell_width:Wt,handle_header_click:e[60],toggle_header_menu:e[65],end_header_edit:e[61],sort_columns:e[33].sort_state.sort_columns,filter_columns:e[33].filter_state.filter_columns,latex_delimiters:e[7],line_breaks:e[14],max_chars:e[20],editable:e[9],is_static:e[22].includes(e[151]),i18n:e[12],col_count:e[5]};return e[25][e[151]].value!==void 0&&(u.value=e[25][e[151]].value),e[24][e[147]].input!==void 0&&(u.el=e[24][e[147]].input),n=new dn({props:u}),Ee.push(()=>Ie(n,"value",o)),Ee.push(()=>Ie(n,"el",_)),{key:l,first:null,c(){t=ke(),re(n.$$.fragment),this.h()},l(a){t=ke(),_e(n.$$.fragment,a),this.h()},h(){this.first=t},m(a,f){q(a,t,f),se(n,a,f),r=!0},p(a,f){e=a;const c={};f[0]&33554432&&(c.i=e[151]),f[1]&4194304&&(c.actual_pinned_columns=e[53]),f[1]&2097152&&(c.header_edit=e[52]),f[1]&1048576&&(c.selected_header=e[51]),f[0]&1&&(c.headers=e[0]),f[1]&4&&(c.sort_columns=e[33].sort_state.sort_columns),f[1]&4&&(c.filter_columns=e[33].filter_state.filter_columns),f[0]&128&&(c.latex_delimiters=e[7]),f[0]&16384&&(c.line_breaks=e[14]),f[0]&1048576&&(c.max_chars=e[20]),f[0]&512&&(c.editable=e[9]),f[0]&37748736&&(c.is_static=e[22].includes(e[151])),f[0]&4096&&(c.i18n=e[12]),f[0]&32&&(c.col_count=e[5]),!i&&f[0]&33554432&&(i=!0,c.value=e[25][e[151]].value,Oe(()=>i=!1)),!s&&f[0]&50331648&&(s=!0,c.el=e[24][e[147]].input,Oe(()=>s=!1)),n.$set(c)},i(a){r||(E(n.$$.fragment,a),r=!0)},o(a){A(n.$$.fragment,a),r=!1},d(a){a&&d(t),oe(n,a)}}}function Hs(l){let e,t,n=[],i=new vt,s,r=l[15]&&ln(),o=We(l[25]);const _=u=>u[147];for(let u=0;u<o.length;u+=1){let a=Fl(l,o,u),f=_(a);i.set(f,n[u]=nn(f,a))}return{c(){e=P("tr"),r&&r.c(),t=X();for(let u=0;u<n.length;u+=1)n[u].c();this.h()},l(u){e=U(u,"TR",{slot:!0,class:!0});var a=S(e);r&&r.l(a),t=G(a);for(let f=0;f<n.length;f+=1)n[f].l(a);a.forEach(d),this.h()},h(){h(e,"slot","thead"),h(e,"class","svelte-1vwr9xf")},m(u,a){q(u,e,a),r&&r.m(e,null),N(e,t);for(let f=0;f<n.length;f+=1)n[f]&&n[f].m(e,null);s=!0},p(u,a){u[15]?r?a[0]&32768&&E(r,1):(r=ln(),r.c(),E(r,1),r.m(e,t)):r&&(me(),A(r,1,1,()=>{r=null}),ge()),a[0]&55595681|a[1]&1617952772|a[2]&8&&(o=We(u[25]),me(),n=ft(n,a,_,1,u,o,i,e,ct,nn,null,Fl),ge())},i(u){if(!s){E(r);for(let a=0;a<o.length;a+=1)E(n[a]);s=!0}},o(u){A(r);for(let a=0;a<n.length;a+=1)A(n[a]);s=!1},d(u){u&&d(e),r&&r.d();for(let a=0;a<n.length;a+=1)n[a].d()}}}function rn(l){let e,t;return e=new yt({props:{index:l[144]}}),{c(){re(e.$$.fragment)},l(n){_e(e.$$.fragment,n)},m(n,i){se(e,n,i),t=!0},p(n,i){const s={};i[4]&1048576&&(s.index=n[144]),e.$set(s)},i(n){t||(E(e.$$.fragment,n),t=!0)},o(n){A(e.$$.fragment,n),t=!1},d(n){oe(e,n)}}}function sn(l,e){let t,n,i,s,r;function o(a){e[97](a,e[144],e[149])}function _(a){e[98](a,e[147])}let u={display_value:e[72](e[144],e[149]),index:e[33].current_search_query!==void 0&&e[27][e[144]]!==void 0?e[27][e[144]]:e[144],j:e[149],actual_pinned_columns:e[53],get_cell_width:Wt,handle_cell_click:e[96],handle_blur:e[64],toggle_cell_menu:e[55].toggle_cell_menu,is_cell_selected:Yi,should_show_cell_menu:Qi,selected_cells:e[31],copy_flash:e[32],active_cell_menu:e[47],styling:e[35][e[144]][e[149]].styling,latex_delimiters:e[7],line_breaks:e[14],datatype:Array.isArray(e[2])?e[2][e[149]]:e[2],editing:e[48],max_chars:e[20],editable:e[9],is_static:e[22].includes(e[149]),i18n:e[12],components:e[8],handle_select_column:e[55].handle_select_column,handle_select_row:e[55].handle_select_row,is_dragging:e[42],wrap:e[10]};return e[35][e[144]][e[149]].value!==void 0&&(u.value=e[35][e[144]][e[149]].value),e[24][e[147]]!==void 0&&(u.el=e[24][e[147]]),n=new ts({props:u}),Ee.push(()=>Ie(n,"value",o)),Ee.push(()=>Ie(n,"el",_)),{key:l,first:null,c(){t=ke(),re(n.$$.fragment),this.h()},l(a){t=ke(),_e(n.$$.fragment,a),this.h()},h(){this.first=t},m(a,f){q(a,t,f),se(n,a,f),r=!0},p(a,f){e=a;const c={};f[4]&3145728&&(c.display_value=e[72](e[144],e[149])),f[0]&134217728|f[1]&4|f[4]&1048576&&(c.index=e[33].current_search_query!==void 0&&e[27][e[144]]!==void 0?e[27][e[144]]:e[144]),f[4]&2097152&&(c.j=e[149]),f[1]&4194304&&(c.actual_pinned_columns=e[53]),f[1]&16384&&(c.handle_cell_click=e[96]),f[1]&1&&(c.selected_cells=e[31]),f[1]&2&&(c.copy_flash=e[32]),f[1]&65536&&(c.active_cell_menu=e[47]),f[1]&16|f[4]&3145728&&(c.styling=e[35][e[144]][e[149]].styling),f[0]&128&&(c.latex_delimiters=e[7]),f[0]&16384&&(c.line_breaks=e[14]),f[0]&4|f[4]&2097152&&(c.datatype=Array.isArray(e[2])?e[2][e[149]]:e[2]),f[1]&131072&&(c.editing=e[48]),f[0]&1048576&&(c.max_chars=e[20]),f[0]&512&&(c.editable=e[9]),f[0]&4194304|f[4]&2097152&&(c.is_static=e[22].includes(e[149])),f[0]&4096&&(c.i18n=e[12]),f[0]&256&&(c.components=e[8]),f[1]&2048&&(c.is_dragging=e[42]),f[0]&1024&&(c.wrap=e[10]),!i&&f[1]&16|f[4]&3145728&&(i=!0,c.value=e[35][e[144]][e[149]].value,Oe(()=>i=!1)),!s&&f[0]&16777216|f[4]&2097152&&(s=!0,c.el=e[24][e[147]],Oe(()=>s=!1)),n.$set(c)},i(a){r||(E(n.$$.fragment,a),r=!0)},o(a){A(n.$$.fragment,a),r=!1},d(a){a&&d(t),oe(n,a)}}}function Vs(l){let e,t,n=[],i=new vt,s,r=l[15]&&rn(l),o=We(l[145]);const _=u=>u[147];for(let u=0;u<o.length;u+=1){let a=Kl(l,o,u),f=_(a);i.set(f,n[u]=sn(f,a))}return{c(){e=P("tr"),r&&r.c(),t=X();for(let u=0;u<n.length;u+=1)n[u].c();this.h()},l(u){e=U(u,"TR",{slot:!0,class:!0});var a=S(e);r&&r.l(a),t=G(a);for(let f=0;f<n.length;f+=1)n[f].l(a);a.forEach(d),this.h()},h(){h(e,"slot","tbody"),h(e,"class","svelte-1vwr9xf"),Y(e,"row-odd",l[144]%2===0)},m(u,a){q(u,e,a),r&&r.m(e,null),N(e,t);for(let f=0;f<n.length;f+=1)n[f]&&n[f].m(e,null);s=!0},p(u,a){u[15]?r?(r.p(u,a),a[0]&32768&&E(r,1)):(r=rn(u),r.c(),E(r,1),r.m(e,t)):r&&(me(),A(r,1,1,()=>{r=null}),ge()),a[0]&156260228|a[1]&21186583|a[2]&1028|a[4]&3145728&&(o=We(u[145]),me(),n=ft(n,a,_,1,u,o,i,e,ct,sn,null,Kl),ge()),(!s||a[4]&1048576)&&Y(e,"row-odd",u[144]%2===0)},i(u){if(!s){E(r);for(let a=0;a<o.length;a+=1)E(n[a]);s=!0}},o(u){A(r);for(let a=0;a<n.length;a+=1)A(n[a]);s=!1},d(u){u&&d(e),r&&r.d();for(let a=0;a<n.length;a+=1)n[a].d()}}}function zs(l){let e,t,n,i,s,r,o,_;function u(w){l[101](w)}function a(w){l[102](w)}function f(w){l[103](w)}function c(w){l[104](w)}function g(w){l[105](w)}let M={max_height:l[13],selected:l[49],disable_scroll:l[47]!==null||l[46]!==null,$$slots:{tbody:[Vs,({index:w,item:C})=>({144:w,145:C}),({index:w,item:C})=>[0,0,0,0,(w?1048576:0)|(C?2097152:0)]],thead:[Hs],default:[qs]},$$scope:{ctx:l}};return l[35]!==void 0&&(M.items=l[35]),l[37]!==void 0&&(M.actual_height=l[37]),l[38]!==void 0&&(M.table_scrollbar_width=l[38]),l[40]!==void 0&&(M.viewport=l[40]),l[41]!==void 0&&(M.show_scroll_button=l[41]),t=new cs({props:M}),Ee.push(()=>Ie(t,"items",u)),Ee.push(()=>Ie(t,"actual_height",a)),Ee.push(()=>Ie(t,"table_scrollbar_width",f)),Ee.push(()=>Ie(t,"viewport",c)),Ee.push(()=>Ie(t,"show_scroll_button",g)),t.$on("scroll_top",Us),{c(){e=P("div"),re(t.$$.fragment),this.h()},l(w){e=U(w,"DIV",{class:!0});var C=S(e);_e(t.$$.fragment,C),C.forEach(d),this.h()},h(){h(e,"class","table-wrap svelte-1vwr9xf")},m(w,C){q(w,e,C),se(t,e,null),_=!0},p(w,C){const p={};C[0]&8192&&(p.max_height=w[13]),C[1]&262144&&(p.selected=w[49]),C[1]&98304&&(p.disable_scroll=w[47]!==null||w[46]!==null),C[0]&189847469|C[1]&7555095|C[4]&1076887552&&(p.$$scope={dirty:C,ctx:w}),!n&&C[1]&16&&(n=!0,p.items=w[35],Oe(()=>n=!1)),!i&&C[1]&64&&(i=!0,p.actual_height=w[37],Oe(()=>i=!1)),!s&&C[1]&128&&(s=!0,p.table_scrollbar_width=w[38],Oe(()=>s=!1)),!r&&C[1]&512&&(r=!0,p.viewport=w[40],Oe(()=>r=!1)),!o&&C[1]&1024&&(o=!0,p.show_scroll_button=w[41],Oe(()=>o=!1)),t.$set(p)},i(w){_||(E(t.$$.fragment,w),_=!0)},o(w){A(t.$$.fragment,w),_=!1},d(w){w&&d(e),oe(t)}}}function on(l){let e,t="↑",n,i;return{c(){e=P("button"),e.textContent=t,this.h()},l(s){e=U(s,"BUTTON",{class:!0,"data-svelte-h":!0}),lt(e)!=="svelte-oaisf6"&&(e.textContent=t),this.h()},h(){h(e,"class","scroll-top-button svelte-1vwr9xf")},m(s,r){q(s,e,r),n||(i=ce(e,"click",l[69]),n=!0)},p:Ce,d(s){s&&d(e),n=!1,i()}}}function an(l){let e,t;return e=new is({props:{on_click:l[110]}}),{c(){re(e.$$.fragment)},l(n){_e(e.$$.fragment,n)},m(n,i){se(e,n,i),t=!0},p:Ce,i(n){t||(E(e.$$.fragment,n),t=!0)},o(n){A(e.$$.fragment,n),t=!1},d(n){oe(e,n)}}}function un(l){var n,i,s,r,o,_,u;let e,t;return e=new ws({props:{x:((n=l[47])==null?void 0:n.x)??((i=l[46])==null?void 0:i.x)??0,y:((s=l[47])==null?void 0:s.y)??((r=l[46])==null?void 0:r.y)??0,row:l[46]?-1:((o=l[47])==null?void 0:o.row)??0,col_count:l[5],row_count:l[6],on_add_row_above:l[111],on_add_row_below:l[112],on_add_column_left:l[113],on_add_column_right:l[114],on_delete_row:l[115],on_delete_col:l[116],editable:l[9],can_delete_rows:!l[46]&&l[26].length>1&&l[9],can_delete_cols:l[26].length>0&&((_=l[26][0])==null?void 0:_.length)>1&&l[9],i18n:l[12],on_sort:l[46]?l[117]:void 0,on_clear_sort:l[46]?l[118]:void 0,sort_direction:l[46]?((u=l[33].sort_state.sort_columns.find(l[119]))==null?void 0:u.direction)??null:null,sort_priority:l[46]&&l[33].sort_state.sort_columns.findIndex(l[120])+1||null,on_filter:l[46]?l[121]:void 0,on_clear_filter:l[46]?l[122]:void 0,filter_active:l[46]?l[33].filter_state.filter_columns.some(l[123]):null}}),{c(){re(e.$$.fragment)},l(a){_e(e.$$.fragment,a)},m(a,f){se(e,a,f),t=!0},p(a,f){var g,M,w,C,p,y,k;const c={};f[1]&98304&&(c.x=((g=a[47])==null?void 0:g.x)??((M=a[46])==null?void 0:M.x)??0),f[1]&98304&&(c.y=((w=a[47])==null?void 0:w.y)??((C=a[46])==null?void 0:C.y)??0),f[1]&98304&&(c.row=a[46]?-1:((p=a[47])==null?void 0:p.row)??0),f[0]&32&&(c.col_count=a[5]),f[0]&64&&(c.row_count=a[6]),f[1]&65536&&(c.on_add_row_above=a[111]),f[1]&65536&&(c.on_add_row_below=a[112]),f[1]&98304&&(c.on_add_column_left=a[113]),f[1]&98304&&(c.on_add_column_right=a[114]),f[1]&65536&&(c.on_delete_row=a[115]),f[1]&98304&&(c.on_delete_col=a[116]),f[0]&512&&(c.editable=a[9]),f[0]&67109376|f[1]&32768&&(c.can_delete_rows=!a[46]&&a[26].length>1&&a[9]),f[0]&67109376&&(c.can_delete_cols=a[26].length>0&&((y=a[26][0])==null?void 0:y.length)>1&&a[9]),f[0]&4096&&(c.i18n=a[12]),f[1]&32768&&(c.on_sort=a[46]?a[117]:void 0),f[1]&32768&&(c.on_clear_sort=a[46]?a[118]:void 0),f[1]&32772&&(c.sort_direction=a[46]?((k=a[33].sort_state.sort_columns.find(a[119]))==null?void 0:k.direction)??null:null),f[1]&32772&&(c.sort_priority=a[46]&&a[33].sort_state.sort_columns.findIndex(a[120])+1||null),f[1]&32768&&(c.on_filter=a[46]?a[121]:void 0),f[1]&32768&&(c.on_clear_filter=a[46]?a[122]:void 0),f[1]&32772&&(c.filter_active=a[46]?a[33].filter_state.filter_columns.some(a[123]):null),e.$set(c)},i(a){t||(E(e.$$.fragment,a),t=!0)},o(a){A(e.$$.fragment,a),t=!1},d(a){oe(e,a)}}}function Ps(l){let e,t,n,i,s,r,o,_,u=[],a=new vt,f,c,g,M,w=[],C=new vt,p,y,k,T,O,L,B,z,F,ae,R=(l[3]&&l[3].length!==0&&l[4]||l[18]||l[19]||l[21]!=="none")&&Xl(l),Q=l[3]&&l[3].length!==0&&Ql(l),K=l[15]&&Zl(),te=We(l[25]);const fe=I=>I[147];for(let I=0;I<te.length;I+=1){let b=Yl(l,te,I),Z=fe(b);a.set(Z,u[I]=xl(Z,b))}let $=l[15]&&$l(),ue=We(l[50]);const ye=I=>I[147];for(let I=0;I<ue.length;I+=1){let b=Wl(l,ue,I),Z=ye(b);C.set(Z,w[I]=en(Z,b))}function V(I){l[106](I)}let he={upload:l[16],stream_handler:l[17],flex:!1,center:!1,boundedheight:!1,disable_click:!0,root:l[11],aria_label:l[12]("dataframe.drop_to_upload"),$$slots:{default:[zs]},$$scope:{ctx:l}};l[36]!==void 0&&(he.dragging=l[36]),y=new Ii({props:he}),Ee.push(()=>Ie(y,"dragging",V)),y.$on("load",l[107]);let D=l[41]&&on(l),ee=l[26].length===0&&l[9]&&l[6][1]==="dynamic"&&an(l),v=(l[47]||l[46])&&un(l);return{c(){e=P("div"),R&&R.c(),t=X(),n=P("div"),i=P("table"),Q&&Q.c(),s=X(),r=P("thead"),o=P("tr"),K&&K.c(),_=X();for(let I=0;I<u.length;I+=1)u[I].c();f=X(),c=P("tbody"),g=P("tr"),$&&$.c(),M=X();for(let I=0;I<w.length;I+=1)w[I].c();p=X(),re(y.$$.fragment),T=X(),D&&D.c(),O=X(),ee&&ee.c(),L=X(),v&&v.c(),B=ke(),this.h()},l(I){e=U(I,"DIV",{class:!0});var b=S(e);R&&R.l(b),t=G(b),n=U(b,"DIV",{class:!0,style:!0,role:!0,tabindex:!0});var Z=S(n);i=U(Z,"TABLE",{"aria-hidden":!0,class:!0});var be=S(i);Q&&Q.l(be),s=G(be),r=U(be,"THEAD",{class:!0});var Le=S(r);o=U(Le,"TR",{class:!0});var de=S(o);K&&K.l(de),_=G(de);for(let Ue=0;Ue<u.length;Ue+=1)u[Ue].l(de);de.forEach(d),Le.forEach(d),f=G(be),c=U(be,"TBODY",{class:!0});var j=S(c);g=U(j,"TR",{class:!0});var x=S(g);$&&$.l(x),M=G(x);for(let Ue=0;Ue<w.length;Ue+=1)w[Ue].l(x);x.forEach(d),j.forEach(d),be.forEach(d),p=G(Z),_e(y.$$.fragment,Z),T=G(Z),D&&D.l(Z),Z.forEach(d),b.forEach(d),O=G(I),ee&&ee.l(I),L=G(I),v&&v.l(I),B=ke(),this.h()},h(){h(o,"class","svelte-1vwr9xf"),h(r,"class","svelte-1vwr9xf"),h(g,"class","svelte-1vwr9xf"),h(c,"class","svelte-1vwr9xf"),h(i,"aria-hidden","true"),h(i,"class","svelte-1vwr9xf"),h(n,"class","table-wrap svelte-1vwr9xf"),De(n,"height",l[37]+"px"),h(n,"role","grid"),h(n,"tabindex","0"),Y(n,"dragging",l[42]),Y(n,"no-wrap",!l[10]),Y(n,"menu-open",l[47]||l[46]),h(e,"class","table-container svelte-1vwr9xf")},m(I,b){q(I,e,b),R&&R.m(e,null),N(e,t),N(e,n),N(n,i),Q&&Q.m(i,null),N(i,s),N(i,r),N(r,o),K&&K.m(o,null),N(o,_);for(let Z=0;Z<u.length;Z+=1)u[Z]&&u[Z].m(o,null);N(i,f),N(i,c),N(c,g),$&&$.m(g,null),N(g,M);for(let Z=0;Z<w.length;Z+=1)w[Z]&&w[Z].m(g,null);l[95](i),N(n,p),se(y,n,null),N(n,T),D&&D.m(n,null),l[108](n),q(I,O,b),ee&&ee.m(I,b),q(I,L,b),v&&v.m(I,b),q(I,B,b),z=!0,F||(ae=[ce(js,"resize",l[88]),ce(n,"keydown",l[109]),ce(n,"mousemove",function(){Xe(l[44])&&l[44].apply(this,arguments)}),ce(n,"mouseup",function(){Xe(l[43])&&l[43].apply(this,arguments)}),ce(n,"mouseleave",function(){Xe(l[43])&&l[43].apply(this,arguments)})],F=!0)},p(I,b){l=I,l[3]&&l[3].length!==0&&l[4]||l[18]||l[19]||l[21]!=="none"?R?(R.p(l,b),b[0]&2883608&&E(R,1)):(R=Xl(l),R.c(),E(R,1),R.m(e,t)):R&&(me(),A(R,1,1,()=>{R=null}),ge()),l[3]&&l[3].length!==0?Q?Q.p(l,b):(Q=Ql(l),Q.c(),Q.m(i,s)):Q&&(Q.d(1),Q=null),l[15]?K?b[0]&32768&&E(K,1):(K=Zl(),K.c(),E(K,1),K.m(o,_)):K&&(me(),A(K,1,1,()=>{K=null}),ge()),b[0]&55595681|b[1]&1617952772|b[2]&8&&(te=We(l[25]),me(),u=ft(u,b,fe,1,l,te,a,o,ct,xl,null,Yl),ge()),l[15]?$?b[0]&32768&&E($,1):($=$l(),$.c(),E($,1),$.m(g,M)):$&&(me(),A($,1,1,()=>{$=null}),ge()),b[0]&268456580|b[1]&17303809|b[2]&4&&(ue=We(l[50]),me(),w=ft(w,b,ye,1,l,ue,C,g,ct,en,null,Wl),ge());const Z={};b[0]&65536&&(Z.upload=l[16]),b[0]&131072&&(Z.stream_handler=l[17]),b[0]&2048&&(Z.root=l[11]),b[0]&4096&&(Z.aria_label=l[12]("dataframe.drop_to_upload")),b[0]&189855661|b[1]&7851735|b[4]&1073741824&&(Z.$$scope={dirty:b,ctx:l}),!k&&b[1]&32&&(k=!0,Z.dragging=l[36],Oe(()=>k=!1)),y.$set(Z),l[41]?D?D.p(l,b):(D=on(l),D.c(),D.m(n,null)):D&&(D.d(1),D=null),(!z||b[1]&64)&&De(n,"height",l[37]+"px"),(!z||b[1]&2048)&&Y(n,"dragging",l[42]),(!z||b[0]&1024)&&Y(n,"no-wrap",!l[10]),(!z||b[1]&98304)&&Y(n,"menu-open",l[47]||l[46]),l[26].length===0&&l[9]&&l[6][1]==="dynamic"?ee?(ee.p(l,b),b[0]&67109440&&E(ee,1)):(ee=an(l),ee.c(),E(ee,1),ee.m(L.parentNode,L)):ee&&(me(),A(ee,1,1,()=>{ee=null}),ge()),l[47]||l[46]?v?(v.p(l,b),b[1]&98304&&E(v,1)):(v=un(l),v.c(),E(v,1),v.m(B.parentNode,B)):v&&(me(),A(v,1,1,()=>{v=null}),ge())},i(I){if(!z){E(R),E(K);for(let b=0;b<te.length;b+=1)E(u[b]);E($);for(let b=0;b<ue.length;b+=1)E(w[b]);E(y.$$.fragment,I),E(ee),E(v),z=!0}},o(I){A(R),A(K);for(let b=0;b<u.length;b+=1)A(u[b]);A($);for(let b=0;b<w.length;b+=1)A(w[b]);A(y.$$.fragment,I),A(ee),A(v),z=!1},d(I){I&&(d(e),d(O),d(L),d(B)),R&&R.d(),Q&&Q.d(),K&&K.d();for(let b=0;b<u.length;b+=1)u[b].d();$&&$.d();for(let b=0;b<w.length;b+=1)w[b].d();l[95](null),oe(y),D&&D.d(),l[108](null),ee&&ee.d(I),v&&v.d(I),F=!1,Qe(ae)}}}function tt(){return Math.random().toString(36).substring(2,15)}function Wt(l){return`var(--cell-width-${l})`}const Us=l=>{};function Rs(l,e,t){let n,i,s,r,o,_,u,a,f,c,g,M,w,C,p,{datatype:y}=e,{label:k=null}=e,{show_label:T=!0}=e,{headers:O=[]}=e,{values:L=[]}=e,{col_count:B}=e,{row_count:z}=e,{latex_delimiters:F}=e,{components:ae={}}=e,{editable:R=!0}=e,{wrap:Q=!1}=e,{root:K}=e,{i18n:te}=e,{max_height:fe=500}=e,{line_breaks:$=!0}=e,{column_widths:ue=[]}=e,{show_row_numbers:ye=!1}=e,{upload:V}=e,{stream_handler:he}=e,{show_fullscreen_button:D=!1}=e,{show_copy_button:ee=!1}=e,{value_is_output:v=!1}=e,{max_chars:I=void 0}=e,{show_search:b="none"}=e,{pinned_columns:Z=0}=e,{static_columns:be=[]}=e,{fullscreen:Le=!1}=e;const de=nr({show_fullscreen_button:D,show_copy_button:ee,show_search:b,show_row_numbers:ye,editable:R,pinned_columns:Z,show_label:T,line_breaks:$,wrap:Q,max_height:fe,column_widths:ue,max_chars:I,static_columns:be}),{state:j,actions:x}=de;vi(l,j,m=>t(33,p=m)),_t(()=>{t(34,de.parent_element=Ne,de),t(34,de.get_data_at=bn,de),t(34,de.get_column=wn,de),t(34,de.get_row=kn,de),t(34,de.dispatch=Ue,de),rl();const m=new IntersectionObserver(J=>{J.forEach(le=>{le.isIntersecting&&!$t&&t(84,$e=!1),$t=le.isIntersecting})});m.observe(Ne),document.addEventListener("click",xt),window.addEventListener("resize",nl);const H=J=>{(at||Ht)&&C(J)};return document.addEventListener("mouseup",H),()=>{m.disconnect(),document.removeEventListener("click",xt),window.removeEventListener("resize",nl),document.removeEventListener("mouseup",H)}});const Ue=pt();let we={},Et={},je=kt(O,B,we,tt),st=O,W=[[]],Fe,Re=[[]],Nt=!1,Yt,nt=[];_t(()=>{Yt=getComputedStyle(document.documentElement).getPropertyValue("--color-accent").trim()+"40",document.documentElement.style.setProperty("--color-accent-copied",Yt)});const bn=(m,H)=>{var J,le;return(le=(J=W==null?void 0:W[m])==null?void 0:J[H])==null?void 0:le.value},wn=m=>(W==null?void 0:W.map(H=>{var J;return(J=H[m])==null?void 0:J.value}))??[],kn=m=>{var H;return((H=W==null?void 0:W[m])==null?void 0:H.map(J=>J.value))??[]};let{display_value:Je=null}=e,{styling:Pe=null}=e,Tt=je.map(m=>m.value),Ct=W.map(m=>m.map(H=>String(H.value)));function Xt(m,H){x.handle_sort(m,H),dt(W,Je,Pe)}function Gt(){x.reset_sort_state(),dt(W,Je,Pe)}function Qt(m,H,J,le){x.handle_filter(m,H,J,le),mt(W,Je,Pe)}function Zt(){x.reset_filter_state(),mt(W,Je,Pe)}async function vn(m,H=!1){!R||r===m||B[1]!=="dynamic"||x.set_header_edit(m)}function pn(m,H){m.target instanceof HTMLAnchorElement||(m.preventDefault(),m.stopPropagation(),R&&(x.set_editing(!1),x.handle_header_click(H,R),Ne.focus()))}function yn(m){R&&(x.end_header_edit(m.detail.key),Ne.focus())}async function St(m){var J;if(Ne.focus(),z[1]!=="dynamic")return;const H=Array(((J=W[0])==null?void 0:J.length)||O.length).fill(0).map((le,Te)=>{const Se=tt();return t(24,we[Se]={cell:null,input:null},we),{id:Se,value:""}});W.length===0?t(26,W=[H]):m!==void 0&&m>=0&&m<=W.length?W.splice(m,0,H):W.push(H),t(87,i=[m!==void 0?m:W.length-1,0])}async function En(m){if(Ne.focus(),B[1]!=="dynamic")return;const H=x.add_col(W,O,tt,m);H.data.forEach(J=>{J.forEach(le=>{we[le.id]||t(24,we[le.id]={cell:null,input:null},we)})}),t(26,W=H.data),t(0,O=H.headers),await rt(),requestAnimationFrame(()=>{vn(m!==void 0?m:W[0].length-1,!0);const J=Ne.querySelectorAll("tbody")[1].offsetWidth;Ne.querySelectorAll("table")[1].scrollTo({left:J})})}function xt(m){$i(m,Ne)&&(x.clear_ui_state(),t(52,r=!1),t(51,o=!1))}let At,$e=!1,Ge=[],Ne,ht,Bt=0,Mt=0;function ot(){var J;const m=((J=W[0])==null?void 0:J.length)||0;if(p.filter_state.filter_columns.length>0||Bt===W.length&&Mt===m&&p.sort_state.sort_columns.length>0)return;Bt=W.length,Mt=m;const H=Ge.map(le=>(le==null?void 0:le.clientWidth)||0);if(H.length!==0){ye&&Ne.style.setProperty("--cell-width-row-number",`${H[0]}px`);for(let le=0;le<50;le++)if(!ue[le])Ne.style.removeProperty(`--cell-width-${le}`);else if(ue[le].endsWith("%")){const Te=parseFloat(ue[le]),Se=Math.floor(Te/100*Ne.clientWidth);Ne.style.setProperty(`--cell-width-${le}`,`${Se}px`)}else Ne.style.setProperty(`--cell-width-${le}`,ue[le]);H.forEach((le,Te)=>{if(!ue[Te]){const Se=`${Math.max(le,45)}px`;Ne.style.setProperty(`--cell-width-${Te}`,Se)}})}}let Dt=L.slice(0,fe/L.length*37).length*37+37,It=0;function dt(m,H,J){const le=Vi(m,H,J,p.sort_state.sort_columns,i,cl);t(26,W=le.data),t(87,i=le.selected)}function mt(m,H,J){var Te,Se,ve;const le=Pi(m,H,J,p.filter_state.filter_columns,i,cl,(Te=p.filter_state.initial_data)==null?void 0:Te.data,(Se=p.filter_state.initial_data)==null?void 0:Se.display_value,(ve=p.filter_state.initial_data)==null?void 0:ve.styling);t(26,W=le.data),t(87,i=le.selected)}let $t=!1;const Nn=m=>{x.set_copy_flash(m)};let Ot=[];function Tn(m){const{blur_event:H,coords:J}=m.detail;Cs(H,de,J)}function Cn(m,H){if(m.stopPropagation(),u&&u.col===H)x.set_active_header_menu(null);else{const J=m.target.closest("th");if(J){const le=J.getBoundingClientRect();x.set_active_header_menu({col:H,x:le.right,y:le.bottom})}}}pi(()=>{t(73,v=!1)});function el(m){if(B[1]!=="dynamic"||W[0].length<=1)return;const H=x.delete_col_at(W,O,m);t(26,W=H.data),t(0,O=H.headers),t(25,je=kt(O,B,we,tt)),x.set_active_cell_menu(null),x.set_active_header_menu(null),x.set_selected(!1),x.set_selected_cells([]),x.set_editing(!1)}function tl(m){t(26,W=x.delete_row_at(W,m)),x.set_active_cell_menu(null),x.set_active_header_menu(null)}let ll;function Sn(){if(p.current_search_query&&b==="filter"){const m=[],H=[],J=[];Re.forEach(Te=>{const Se=[],ve=[],et=[];Te.forEach(Ze=>{Se.push(Ze.value),ve.push(Ze.display_value!==void 0?Ze.display_value:String(Ze.value)),et.push(Ze.styling||"")}),m.push(Se),H.push(ve),J.push(et)});const le={data:m,headers:je.map(Te=>Te.value),metadata:{display_value:H,styling:J}};Ue("change",le),v||Ue("input"),x.handle_search(null)}}let gt,Lt=!1;function An(){gt.scrollTo({top:0})}function nl(){x.set_active_cell_menu(null),x.set_active_header_menu(null),t(31,n=[]),t(87,i=!1),t(48,s=!1),t(84,$e=!1),ot()}function jt(m,H){const J=H==="above"?m:m+1;St(J),t(47,_=null),t(46,u=null)}function qt(m,H){const J=H==="left"?m:m+1;En(J),t(47,_=null),t(46,u=null)}function Bn(){x.reset_sort_state()}let at=!1,Ht=null,il=null;const bt={is_dragging:at,drag_start:Ht,mouse_down_pos:il};let Ye;function rl(){t(86,Ye=Ls(bt,m=>t(42,at=m),m=>x.set_selected_cells(m),m=>x.set_selected(m),(m,H,J)=>x.handle_cell_click(m,H,J),ye,Ne))}function Mn(m,H){var le,Te;return p.current_search_query!==void 0&&((le=Re==null?void 0:Re[m])!=null&&le[H])?Re[m][H].display_value!==void 0?Re[m][H].display_value:String(Re[m][H].value):(Te=W==null?void 0:W[m])!=null&&Te[H]?W[m][H].display_value!==void 0?W[m][H].display_value:String(W[m][H].value):""}const Dn=()=>ot(),In=async()=>await cn(W,null),On=m=>x.handle_search(m.detail);function Ln(m){it.call(this,l,m)}function jn(m,H){l.$$.not_equal(je[H].value,m)&&(je[H].value=m,t(25,je),t(0,O),t(79,st),t(5,B),t(24,we))}function qn(m,H){l.$$.not_equal(we[H].input,m)&&(we[H].input=m,t(24,we))}function Hn(m,H){Ee[m?"unshift":"push"](()=>{Ge[H]=m,t(28,Ge)})}function Vn(m){Ee[m?"unshift":"push"](()=>{ht=m,t(30,ht)})}const zn=(m,H,J)=>M(m,H,J);function Pn(m,H,J){l.$$.not_equal(Re[H][J].value,m)&&(Re[H][J].value=m,t(35,Re),t(33,p),t(26,W),t(27,nt),t(77,Pe),t(1,L),t(80,Fe),t(29,Ne),t(24,we),t(131,Et),t(76,Je),t(28,Ge))}function Un(m,H){l.$$.not_equal(we[H],m)&&(we[H]=m,t(24,we))}function Rn(m,H){l.$$.not_equal(je[H].value,m)&&(je[H].value=m,t(25,je),t(0,O),t(79,st),t(5,B),t(24,we))}function Jn(m,H){l.$$.not_equal(we[H].input,m)&&(we[H].input=m,t(24,we))}function Fn(m){Re=m,t(35,Re),t(33,p),t(26,W),t(27,nt),t(77,Pe),t(1,L),t(80,Fe),t(29,Ne),t(24,we),t(131,Et),t(76,Je),t(28,Ge)}function Kn(m){Dt=m,t(37,Dt)}function Wn(m){It=m,t(38,It)}function Yn(m){gt=m,t(40,gt)}function Xn(m){Lt=m,t(41,Lt)}function Gn(m){Nt=m,t(36,Nt)}const Qn=({detail:m})=>Ki(m.data,H=>(t(25,je=kt(H.map(J=>J??""),B,we,tt)),je),H=>{t(1,L=H)});function Zn(m){Ee[m?"unshift":"push"](()=>{Ne=m,t(29,Ne)})}const xn=m=>Os(m,de),$n=()=>St(),ei=()=>jt((_==null?void 0:_.row)??-1,"above"),ti=()=>jt((_==null?void 0:_.row)??-1,"below"),li=()=>qt((_==null?void 0:_.col)??(u==null?void 0:u.col)??-1,"left"),ni=()=>qt((_==null?void 0:_.col)??(u==null?void 0:u.col)??-1,"right"),ii=()=>tl((_==null?void 0:_.row)??-1),ri=()=>el((_==null?void 0:_.col)??(u==null?void 0:u.col)??-1),si=m=>{u&&(Xt(u.col,m),x.set_active_header_menu(null))},oi=()=>{Gt(),x.set_active_header_menu(null)},ai=m=>m.col===((u==null?void 0:u.col)??-1),ui=m=>m.col===((u==null?void 0:u.col)??-1),_i=(m,H,J)=>{u&&(Qt(u.col,m,H,J),x.set_active_header_menu(null))},fi=()=>{Zt(),x.set_active_header_menu(null)},ci=m=>m.col===((u==null?void 0:u.col)??-1);return l.$$set=m=>{"datatype"in m&&t(2,y=m.datatype),"label"in m&&t(3,k=m.label),"show_label"in m&&t(4,T=m.show_label),"headers"in m&&t(0,O=m.headers),"values"in m&&t(1,L=m.values),"col_count"in m&&t(5,B=m.col_count),"row_count"in m&&t(6,z=m.row_count),"latex_delimiters"in m&&t(7,F=m.latex_delimiters),"components"in m&&t(8,ae=m.components),"editable"in m&&t(9,R=m.editable),"wrap"in m&&t(10,Q=m.wrap),"root"in m&&t(11,K=m.root),"i18n"in m&&t(12,te=m.i18n),"max_height"in m&&t(13,fe=m.max_height),"line_breaks"in m&&t(14,$=m.line_breaks),"column_widths"in m&&t(74,ue=m.column_widths),"show_row_numbers"in m&&t(15,ye=m.show_row_numbers),"upload"in m&&t(16,V=m.upload),"stream_handler"in m&&t(17,he=m.stream_handler),"show_fullscreen_button"in m&&t(18,D=m.show_fullscreen_button),"show_copy_button"in m&&t(19,ee=m.show_copy_button),"value_is_output"in m&&t(73,v=m.value_is_output),"max_chars"in m&&t(20,I=m.max_chars),"show_search"in m&&t(21,b=m.show_search),"pinned_columns"in m&&t(75,Z=m.pinned_columns),"static_columns"in m&&t(22,be=m.static_columns),"fullscreen"in m&&t(23,Le=m.fullscreen),"display_value"in m&&t(76,Je=m.display_value),"styling"in m&&t(77,Pe=m.styling)},l.$$.update=()=>{var m,H;if(l.$$.dirty[1]&4&&t(31,n=p.ui_state.selected_cells),l.$$.dirty[1]&4&&t(87,i=p.ui_state.selected),l.$$.dirty[1]&4&&t(48,s=p.ui_state.editing),l.$$.dirty[1]&4&&t(52,r=p.ui_state.header_edit),l.$$.dirty[1]&4&&t(51,o=p.ui_state.selected_header),l.$$.dirty[1]&4&&t(47,_=p.ui_state.active_cell_menu),l.$$.dirty[1]&4&&t(46,u=p.ui_state.active_header_menu),l.$$.dirty[1]&4&&t(32,a=p.ui_state.copy_flash),l.$$.dirty[0]&889192450|l.$$.dirty[1]&4|l.$$.dirty[2]&311296&&!ut(L,Fe)){if(Ne){const Te=L.length===0||L.length===1&&L[0].length===0,Se=Fe!==void 0&&(L.length!==Fe.length||L[0]&&Fe[0]&&L[0].length!==Fe[0].length);if(Te||Se){for(let ve=0;ve<50;ve++)Ne.style.removeProperty(`--cell-width-${ve}`);Bt=0,Mt=0,t(84,$e=!1)}}const J=L.length===0||L.length===1&&L[0].length===0,le=Fe!==void 0&&(L.length!==Fe.length||L[0]&&Fe[0]&&L[0].length!==Fe[0].length);t(26,W=Ns(L,we,Et,tt,Je)),t(80,Fe=JSON.parse(JSON.stringify(L))),J||le?x.reset_sort_state():p.sort_state.sort_columns.length>0?dt(W,Je,Pe):(x.handle_sort(-1,"asc"),x.reset_sort_state()),p.filter_state.filter_columns.length>0?mt(W,Je,Pe):x.reset_filter_state(),p.current_search_query&&x.handle_search(null),Ne&&Ge.length>0&&(J||le)&&t(84,$e=!1)}if(l.$$.dirty[0]&67108864|l.$$.dirty[2]&8192&&t(53,f=Z&&((m=W==null?void 0:W[0])!=null&&m.length)?Math.min(Z,W[0].length):0),l.$$.dirty[0]&16777249|l.$$.dirty[2]&131072&&(ut(O,st)||(t(25,je=kt(O,B,we,tt)),t(79,st=JSON.parse(JSON.stringify(O))))),l.$$.dirty[0]&117440512|l.$$.dirty[2]&49152&&(W||je||we)&&(t(34,de.data=W,de),t(34,de.headers=je,de),t(34,de.els=we,de),t(34,de.display_value=Je,de),t(34,de.styling=Pe,de)),l.$$.dirty[0]&201326592|l.$$.dirty[1]&4|l.$$.dirty[2]&32768)if(p.current_search_query!==void 0){const J=new Map;t(27,nt=[]),W.forEach((Te,Se)=>{Te.some(ve=>{var et;return String(ve==null?void 0:ve.value).toLowerCase().includes(((et=p.current_search_query)==null?void 0:et.toLowerCase())||"")})&&nt.push(Se),Te.forEach((ve,et)=>{var Ze;J.set(ve.id,{value:ve.value,display_value:ve.display_value!==void 0?ve.display_value:String(ve.value),styling:((Ze=Pe==null?void 0:Pe[Se])==null?void 0:Ze[et])||""})})});const le=x.filter_data(W);t(35,Re=le.map(Te=>Te.map(Se=>{const ve=J.get(Se.id);return{...Se,display_value:(ve==null?void 0:ve.display_value)!==void 0?ve.display_value:String(Se.value),styling:(ve==null?void 0:ve.styling)||""}})))}else t(27,nt=[]);if(l.$$.dirty[0]&100663300|l.$$.dirty[2]&1574912&&(W||je)&&(x.trigger_change(W.map((J,le)=>J.map((Te,Se)=>{const ve=Array.isArray(y)?y[Se]:y;return{...Te,value:Ts(Te.value,ve)}})),je,Ct,Tt,v,Ue),t(82,Ct=W.map(J=>J.map(le=>String(le.value)))),t(81,Tt=je.map(J=>J.value))),l.$$.dirty[0]&67108864|l.$$.dirty[1]&4|l.$$.dirty[2]&49152&&(p.filter_state.filter_columns.length>0&&mt(W,Je,Pe),p.sort_state.sort_columns.length>0&&(dt(W,Je,Pe),x.update_row_order(W))),l.$$.dirty[0]&67108864&&t(50,c=Ui(W)),l.$$.dirty[0]&268435456|l.$$.dirty[2]&2097152&&Ge[0]&&(H=Ge[0])!=null&&H.clientWidth&&(clearTimeout(At),t(83,At=setTimeout(()=>ot(),100))),l.$$.dirty[0]&268435456|l.$$.dirty[2]&4194304&&Ge[0]&&!$e&&(ot(),t(84,$e=!0)),l.$$.dirty[2]&33554432&&t(49,g=!!i&&i[0]),l.$$.dirty[1]&3|l.$$.dirty[2]&8388608&&(a&&!ut(n,Ot)&&Nn(!1),t(85,Ot=n)),l.$$.dirty[2]&33554432&&i!==!1&&t(39,ll=i),l.$$.dirty[0]&1694498816|l.$$.dirty[2]&33554432&&i!==!1){const J=er(i,W,we,Ne,ht);document.documentElement.style.setProperty("--selected-col-pos",J.col_pos),document.documentElement.style.setProperty("--selected-row-pos",J.row_pos||"0px")}l.$$.dirty[0]&536870912&&Ne&&rl(),l.$$.dirty[2]&16777216&&t(45,M=(Ye==null?void 0:Ye.handle_mouse_down)||(()=>{})),l.$$.dirty[2]&16777216&&t(44,w=(Ye==null?void 0:Ye.handle_mouse_move)||(()=>{})),l.$$.dirty[2]&16777216&&t(43,C=(Ye==null?void 0:Ye.handle_mouse_up)||(()=>{}))},t(42,at=bt.is_dragging),Ht=bt.drag_start,il=bt.mouse_down_pos,[O,L,y,k,T,B,z,F,ae,R,Q,K,te,fe,$,ye,V,he,D,ee,I,b,be,Le,we,je,W,nt,Ge,Ne,ht,n,a,p,de,Re,Nt,Dt,It,ll,gt,Lt,at,C,w,M,u,_,s,g,c,o,r,f,j,x,Xt,Gt,Qt,Zt,pn,yn,St,ot,Tn,Cn,el,tl,Sn,An,jt,qt,Mn,v,ue,Z,Je,Pe,Bn,st,Fe,Tt,Ct,At,$e,Ot,Ye,i,Dn,In,On,Ln,jn,qn,Hn,Vn,zn,Pn,Un,Rn,Jn,Fn,Kn,Wn,Yn,Xn,Gn,Qn,Zn,xn,$n,ei,ti,li,ni,ii,ri,si,oi,ai,ui,_i,fi,ci]}class Js extends qe{constructor(e){super(),He(this,e,Rs,Ps,Ve,{datatype:2,label:3,show_label:4,headers:0,values:1,col_count:5,row_count:6,latex_delimiters:7,components:8,editable:9,wrap:10,root:11,i18n:12,max_height:13,line_breaks:14,column_widths:74,show_row_numbers:15,upload:16,stream_handler:17,show_fullscreen_button:18,show_copy_button:19,value_is_output:73,max_chars:20,show_search:21,pinned_columns:75,static_columns:22,fullscreen:23,display_value:76,styling:77,reset_sort_state:78},null,[-1,-1,-1,-1,-1])}get reset_sort_state(){return this.$$.ctx[78]}}const Fs=Js;function Ks(l){var a,f;let e,t,n,i,s;const r=[{autoscroll:l[17].autoscroll},{i18n:l[17].i18n},l[20]];let o={};for(let c=0;c<r.length;c+=1)o=Ei(o,r[c]);e=new Mi({props:o}),e.$on("clear_status",l[29]);function _(c){l[32](c)}let u={root:l[14],label:l[8],show_label:l[9],row_count:l[7],col_count:l[6],values:l[0].data,display_value:(a=l[0].metadata)==null?void 0:a.display_value,styling:(f=l[0].metadata)==null?void 0:f.styling,headers:l[0].headers,fullscreen:l[2],wrap:l[10],datatype:l[11],latex_delimiters:l[18],editable:l[21],max_height:l[19],i18n:l[17].i18n,line_breaks:l[15],column_widths:l[16],upload:l[30],stream_handler:l[31],show_fullscreen_button:l[22],max_chars:l[23],show_copy_button:l[24],show_row_numbers:l[25],show_search:l[26],pinned_columns:l[27],components:{image:qi},static_columns:l[28]};return l[1]!==void 0&&(u.value_is_output=l[1]),n=new Fs({props:u}),Ee.push(()=>Ie(n,"value_is_output",_)),n.$on("change",l[33]),n.$on("input",l[34]),n.$on("select",l[35]),n.$on("fullscreen",l[36]),{c(){re(e.$$.fragment),t=X(),re(n.$$.fragment)},l(c){_e(e.$$.fragment,c),t=G(c),_e(n.$$.fragment,c)},m(c,g){se(e,c,g),q(c,t,g),se(n,c,g),s=!0},p(c,g){var C,p;const M=g[0]&1179648?Ni(r,[g[0]&131072&&{autoscroll:c[17].autoscroll},g[0]&131072&&{i18n:c[17].i18n},g[0]&1048576&&Ti(c[20])]):{};e.$set(M);const w={};g[0]&16384&&(w.root=c[14]),g[0]&256&&(w.label=c[8]),g[0]&512&&(w.show_label=c[9]),g[0]&128&&(w.row_count=c[7]),g[0]&64&&(w.col_count=c[6]),g[0]&1&&(w.values=c[0].data),g[0]&1&&(w.display_value=(C=c[0].metadata)==null?void 0:C.display_value),g[0]&1&&(w.styling=(p=c[0].metadata)==null?void 0:p.styling),g[0]&1&&(w.headers=c[0].headers),g[0]&4&&(w.fullscreen=c[2]),g[0]&1024&&(w.wrap=c[10]),g[0]&2048&&(w.datatype=c[11]),g[0]&262144&&(w.latex_delimiters=c[18]),g[0]&2097152&&(w.editable=c[21]),g[0]&524288&&(w.max_height=c[19]),g[0]&131072&&(w.i18n=c[17].i18n),g[0]&32768&&(w.line_breaks=c[15]),g[0]&65536&&(w.column_widths=c[16]),g[0]&131072&&(w.upload=c[30]),g[0]&131072&&(w.stream_handler=c[31]),g[0]&4194304&&(w.show_fullscreen_button=c[22]),g[0]&8388608&&(w.max_chars=c[23]),g[0]&16777216&&(w.show_copy_button=c[24]),g[0]&33554432&&(w.show_row_numbers=c[25]),g[0]&67108864&&(w.show_search=c[26]),g[0]&134217728&&(w.pinned_columns=c[27]),g[0]&268435456&&(w.static_columns=c[28]),!i&&g[0]&2&&(i=!0,w.value_is_output=c[1],Oe(()=>i=!1)),n.$set(w)},i(c){s||(E(e.$$.fragment,c),E(n.$$.fragment,c),s=!0)},o(c){A(e.$$.fragment,c),A(n.$$.fragment,c),s=!1},d(c){c&&d(t),oe(e,c),oe(n,c)}}}function Ws(l){let e,t,n;function i(r){l[37](r)}let s={visible:l[5],padding:!1,elem_id:l[3],elem_classes:l[4],container:!1,scale:l[12],min_width:l[13],overflow_behavior:"visible",$$slots:{default:[Ks]},$$scope:{ctx:l}};return l[2]!==void 0&&(s.fullscreen=l[2]),e=new Bi({props:s}),Ee.push(()=>Ie(e,"fullscreen",i)),{c(){re(e.$$.fragment)},l(r){_e(e.$$.fragment,r)},m(r,o){se(e,r,o),n=!0},p(r,o){const _={};o[0]&32&&(_.visible=r[5]),o[0]&8&&(_.elem_id=r[3]),o[0]&16&&(_.elem_classes=r[4]),o[0]&4096&&(_.scale=r[12]),o[0]&8192&&(_.min_width=r[13]),o[0]&536858567|o[1]&128&&(_.$$scope={dirty:o,ctx:r}),!t&&o[0]&4&&(t=!0,_.fullscreen=r[2],Oe(()=>t=!1)),e.$set(_)},i(r){n||(E(e.$$.fragment,r),n=!0)},o(r){A(e.$$.fragment,r),n=!1},d(r){oe(e,r)}}}function Ys(l,e,t){let{elem_id:n=""}=e,{elem_classes:i=[]}=e,{visible:s=!0}=e,{value:r={data:[["","",""]],headers:["1","2","3"],metadata:null}}=e,{value_is_output:o=!1}=e,{col_count:_}=e,{row_count:u}=e,{label:a=null}=e,{show_label:f=!0}=e,{wrap:c}=e,{datatype:g}=e,{scale:M=null}=e,{min_width:w=void 0}=e,{root:C}=e,{line_breaks:p=!0}=e,{column_widths:y=[]}=e,{gradio:k}=e,{latex_delimiters:T}=e,{max_height:O=void 0}=e,{loading_status:L}=e,{interactive:B}=e,{show_fullscreen_button:z=!1}=e,{max_chars:F=void 0}=e,{show_copy_button:ae=!1}=e,{show_row_numbers:R=!1}=e,{show_search:Q="none"}=e,{pinned_columns:K=0}=e,{static_columns:te=[]}=e,{fullscreen:fe=!1}=e;const $=()=>k.dispatch("clear_status",L),ue=(...b)=>k.client.upload(...b),ye=(...b)=>k.client.stream(...b);function V(b){o=b,t(1,o)}const he=b=>{t(0,r.data=b.detail.data,r),t(0,r.headers=b.detail.headers,r),k.dispatch("change")},D=b=>k.dispatch("input"),ee=b=>k.dispatch("select",b.detail),v=({detail:b})=>{t(2,fe=b)};function I(b){fe=b,t(2,fe)}return l.$$set=b=>{"elem_id"in b&&t(3,n=b.elem_id),"elem_classes"in b&&t(4,i=b.elem_classes),"visible"in b&&t(5,s=b.visible),"value"in b&&t(0,r=b.value),"value_is_output"in b&&t(1,o=b.value_is_output),"col_count"in b&&t(6,_=b.col_count),"row_count"in b&&t(7,u=b.row_count),"label"in b&&t(8,a=b.label),"show_label"in b&&t(9,f=b.show_label),"wrap"in b&&t(10,c=b.wrap),"datatype"in b&&t(11,g=b.datatype),"scale"in b&&t(12,M=b.scale),"min_width"in b&&t(13,w=b.min_width),"root"in b&&t(14,C=b.root),"line_breaks"in b&&t(15,p=b.line_breaks),"column_widths"in b&&t(16,y=b.column_widths),"gradio"in b&&t(17,k=b.gradio),"latex_delimiters"in b&&t(18,T=b.latex_delimiters),"max_height"in b&&t(19,O=b.max_height),"loading_status"in b&&t(20,L=b.loading_status),"interactive"in b&&t(21,B=b.interactive),"show_fullscreen_button"in b&&t(22,z=b.show_fullscreen_button),"max_chars"in b&&t(23,F=b.max_chars),"show_copy_button"in b&&t(24,ae=b.show_copy_button),"show_row_numbers"in b&&t(25,R=b.show_row_numbers),"show_search"in b&&t(26,Q=b.show_search),"pinned_columns"in b&&t(27,K=b.pinned_columns),"static_columns"in b&&t(28,te=b.static_columns),"fullscreen"in b&&t(2,fe=b.fullscreen)},[r,o,fe,n,i,s,_,u,a,f,c,g,M,w,C,p,y,k,T,O,L,B,z,F,ae,R,Q,K,te,$,ue,ye,V,he,D,ee,v,I]}class ro extends qe{constructor(e){super(),He(this,e,Ys,Ws,Ve,{elem_id:3,elem_classes:4,visible:5,value:0,value_is_output:1,col_count:6,row_count:7,label:8,show_label:9,wrap:10,datatype:11,scale:12,min_width:13,root:14,line_breaks:15,column_widths:16,gradio:17,latex_delimiters:18,max_height:19,loading_status:20,interactive:21,show_fullscreen_button:22,max_chars:23,show_copy_button:24,show_row_numbers:25,show_search:26,pinned_columns:27,static_columns:28,fullscreen:2},null,[-1,-1])}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),pe()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),pe()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),pe()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),pe()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),pe()}get col_count(){return this.$$.ctx[6]}set col_count(e){this.$$set({col_count:e}),pe()}get row_count(){return this.$$.ctx[7]}set row_count(e){this.$$set({row_count:e}),pe()}get label(){return this.$$.ctx[8]}set label(e){this.$$set({label:e}),pe()}get show_label(){return this.$$.ctx[9]}set show_label(e){this.$$set({show_label:e}),pe()}get wrap(){return this.$$.ctx[10]}set wrap(e){this.$$set({wrap:e}),pe()}get datatype(){return this.$$.ctx[11]}set datatype(e){this.$$set({datatype:e}),pe()}get scale(){return this.$$.ctx[12]}set scale(e){this.$$set({scale:e}),pe()}get min_width(){return this.$$.ctx[13]}set min_width(e){this.$$set({min_width:e}),pe()}get root(){return this.$$.ctx[14]}set root(e){this.$$set({root:e}),pe()}get line_breaks(){return this.$$.ctx[15]}set line_breaks(e){this.$$set({line_breaks:e}),pe()}get column_widths(){return this.$$.ctx[16]}set column_widths(e){this.$$set({column_widths:e}),pe()}get gradio(){return this.$$.ctx[17]}set gradio(e){this.$$set({gradio:e}),pe()}get latex_delimiters(){return this.$$.ctx[18]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),pe()}get max_height(){return this.$$.ctx[19]}set max_height(e){this.$$set({max_height:e}),pe()}get loading_status(){return this.$$.ctx[20]}set loading_status(e){this.$$set({loading_status:e}),pe()}get interactive(){return this.$$.ctx[21]}set interactive(e){this.$$set({interactive:e}),pe()}get show_fullscreen_button(){return this.$$.ctx[22]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),pe()}get max_chars(){return this.$$.ctx[23]}set max_chars(e){this.$$set({max_chars:e}),pe()}get show_copy_button(){return this.$$.ctx[24]}set show_copy_button(e){this.$$set({show_copy_button:e}),pe()}get show_row_numbers(){return this.$$.ctx[25]}set show_row_numbers(e){this.$$set({show_row_numbers:e}),pe()}get show_search(){return this.$$.ctx[26]}set show_search(e){this.$$set({show_search:e}),pe()}get pinned_columns(){return this.$$.ctx[27]}set pinned_columns(e){this.$$set({pinned_columns:e}),pe()}get static_columns(){return this.$$.ctx[28]}set static_columns(e){this.$$set({static_columns:e}),pe()}get fullscreen(){return this.$$.ctx[2]}set fullscreen(e){this.$$set({fullscreen:e}),pe()}}export{Fs as BaseDataFrame,ao as BaseExample,ro as default};
//# sourceMappingURL=Index.D6J2PVPn.js.map
