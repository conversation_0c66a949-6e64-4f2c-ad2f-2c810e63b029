{"version": 3, "file": "colorToUniform-KTpA7KSL.js", "sources": ["../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/high-shader/shader-bits/localUniformBit.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/sprite/BatchableSprite.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/graphics/gpu/colorToUniform.mjs"], "sourcesContent": ["\"use strict\";\nconst localUniformBit = {\n  name: \"local-uniform-bit\",\n  vertex: {\n    header: (\n      /* wgsl */\n      `\n\n            struct LocalUniforms {\n                uTransformMatrix:mat3x3<f32>,\n                uColor:vec4<f32>,\n                uRound:f32,\n            }\n\n            @group(1) @binding(0) var<uniform> localUniforms : LocalUniforms;\n        `\n    ),\n    main: (\n      /* wgsl */\n      `\n            vColor *= localUniforms.uColor;\n            modelMatrix *= localUniforms.uTransformMatrix;\n        `\n    ),\n    end: (\n      /* wgsl */\n      `\n            if(localUniforms.uRound == 1)\n            {\n                vPosition = vec4(roundPixels(vPosition.xy, globalUniforms.uResolution), vPosition.zw);\n            }\n        `\n    )\n  }\n};\nconst localUniformBitGroup2 = {\n  ...localUniformBit,\n  vertex: {\n    ...localUniformBit.vertex,\n    // replace the group!\n    header: localUniformBit.vertex.header.replace(\"group(1)\", \"group(2)\")\n  }\n};\nconst localUniformBitGl = {\n  name: \"local-uniform-bit\",\n  vertex: {\n    header: (\n      /* glsl */\n      `\n\n            uniform mat3 uTransformMatrix;\n            uniform vec4 uColor;\n            uniform float uRound;\n        `\n    ),\n    main: (\n      /* glsl */\n      `\n            vColor *= uColor;\n            modelMatrix = uTransformMatrix;\n        `\n    ),\n    end: (\n      /* glsl */\n      `\n            if(uRound == 1.)\n            {\n                gl_Position.xy = roundPixels(gl_Position.xy, uResolution);\n            }\n        `\n    )\n  }\n};\n\nexport { localUniformBit, localUniformBitGl, localUniformBitGroup2 };\n//# sourceMappingURL=localUniformBit.mjs.map\n", "\"use strict\";\nclass BatchableSprite {\n  constructor() {\n    this.batcherName = \"default\";\n    this.topology = \"triangle-list\";\n    // batch specific..\n    this.attributeSize = 4;\n    this.indexSize = 6;\n    this.packAsQuad = true;\n    this.roundPixels = 0;\n    this._attributeStart = 0;\n    // location in the buffer\n    this._batcher = null;\n    this._batch = null;\n  }\n  get blendMode() {\n    return this.renderable.groupBlendMode;\n  }\n  get color() {\n    return this.renderable.groupColorAlpha;\n  }\n  reset() {\n    this.renderable = null;\n    this.texture = null;\n    this._batcher = null;\n    this._batch = null;\n    this.bounds = null;\n  }\n}\n\nexport { BatchableSprite };\n//# sourceMappingURL=BatchableSprite.mjs.map\n", "\"use strict\";\nfunction colorToUniform(rgb, alpha, out, offset) {\n  out[offset++] = (rgb >> 16 & 255) / 255;\n  out[offset++] = (rgb >> 8 & 255) / 255;\n  out[offset++] = (rgb & 255) / 255;\n  out[offset++] = alpha;\n}\nfunction color32BitToUniform(abgr, out, offset) {\n  const alpha = (abgr >> 24 & 255) / 255;\n  out[offset++] = (abgr & 255) / 255 * alpha;\n  out[offset++] = (abgr >> 8 & 255) / 255 * alpha;\n  out[offset++] = (abgr >> 16 & 255) / 255 * alpha;\n  out[offset++] = alpha;\n}\n\nexport { color32BitToUniform, colorToUniform };\n//# sourceMappingURL=colorToUniform.mjs.map\n"], "names": ["localUniformBit", "localUniformBitGroup2", "localUniformBitGl", "BatchableSprite", "color32BitToUniform", "abgr", "out", "offset", "alpha"], "mappings": "AACK,MAACA,EAAkB,CACtB,KAAM,oBACN,OAAQ,CACN,OAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAWF,KAEE;AAAA;AAAA;AAAA,UAKF,IAEE;AAAA;AAAA;AAAA;AAAA;AAAA,SAOH,CACH,EACMC,EAAwB,CAC5B,GAAGD,EACH,OAAQ,CACN,GAAGA,EAAgB,OAEnB,OAAQA,EAAgB,OAAO,OAAO,QAAQ,WAAY,UAAU,CACrE,CACH,EACME,EAAoB,CACxB,KAAM,oBACN,OAAQ,CACN,OAEE;AAAA;AAAA;AAAA;AAAA;AAAA,UAOF,KAEE;AAAA;AAAA;AAAA,UAKF,IAEE;AAAA;AAAA;AAAA;AAAA;AAAA,SAOH,CACH,ECvEA,MAAMC,CAAgB,CACpB,aAAc,CACZ,KAAK,YAAc,UACnB,KAAK,SAAW,gBAEhB,KAAK,cAAgB,EACrB,KAAK,UAAY,EACjB,KAAK,WAAa,GAClB,KAAK,YAAc,EACnB,KAAK,gBAAkB,EAEvB,KAAK,SAAW,KAChB,KAAK,OAAS,IACf,CACD,IAAI,WAAY,CACd,OAAO,KAAK,WAAW,cACxB,CACD,IAAI,OAAQ,CACV,OAAO,KAAK,WAAW,eACxB,CACD,OAAQ,CACN,KAAK,WAAa,KAClB,KAAK,QAAU,KACf,KAAK,SAAW,KAChB,KAAK,OAAS,KACd,KAAK,OAAS,IACf,CACH,CCrBA,SAASC,EAAoBC,EAAMC,EAAKC,EAAQ,CAC9C,MAAMC,GAASH,GAAQ,GAAK,KAAO,IACnCC,EAAIC,GAAQ,GAAKF,EAAO,KAAO,IAAMG,EACrCF,EAAIC,GAAQ,GAAKF,GAAQ,EAAI,KAAO,IAAMG,EAC1CF,EAAIC,GAAQ,GAAKF,GAAQ,GAAK,KAAO,IAAMG,EAC3CF,EAAIC,GAAQ,EAAIC,CAClB", "x_google_ignoreList": [0, 1, 2]}