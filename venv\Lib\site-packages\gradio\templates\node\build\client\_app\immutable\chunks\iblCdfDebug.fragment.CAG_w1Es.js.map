{"version": 3, "file": "iblCdfDebug.fragment.CAG_w1Es.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Shaders/iblCdfDebug.fragment.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nconst name = \"iblCdfDebugPixelShader\";\nconst shader = `precision highp samplerCube;\n#define PI 3.1415927\nvarying vec2 vUV;uniform sampler2D cdfy;uniform sampler2D cdfx;uniform sampler2D icdf;uniform sampler2D pdf;\n#ifdef IBL_USE_CUBE_MAP\nuniform samplerCube iblSource;\n#else\nuniform sampler2D iblSource;\n#endif\nuniform sampler2D textureSampler;\n#define cdfyVSize (0.8/3.0)\n#define cdfxVSize 0.1\n#define cdfyHSize 0.5\nuniform vec4 sizeParams;\n#define offsetX sizeParams.x\n#define offsetY sizeParams.y\n#define widthScale sizeParams.z\n#define heightScale sizeParams.w\n#ifdef IBL_USE_CUBE_MAP\nvec3 equirectangularToCubemapDirection(vec2 uv) {float longitude=uv.x*2.0*PI-PI;float latitude=PI*0.5-uv.y*PI;vec3 direction;direction.x=cos(latitude)*sin(longitude);direction.y=sin(latitude);direction.z=cos(latitude)*cos(longitude);return direction;}\n#endif\nvoid main(void) {vec3 colour=vec3(0.0);vec2 uv =\nvec2((offsetX+vUV.x)*widthScale,(offsetY+vUV.y)*heightScale);vec3 backgroundColour=texture2D(textureSampler,vUV).rgb;int cdfxWidth=textureSize(cdfx,0).x;int cdfyHeight=textureSize(cdfy,0).y;const float iblStart=1.0-cdfyVSize;const float pdfStart=1.0-2.0*cdfyVSize;const float cdfyStart=1.0-3.0*cdfyVSize;const float cdfxStart=1.0-3.0*cdfyVSize-cdfxVSize;const float icdfxStart=1.0-3.0*cdfyVSize-2.0*cdfxVSize;\n#ifdef IBL_USE_CUBE_MAP\nvec3 direction=equirectangularToCubemapDirection(\n(uv-vec2(0.0,iblStart))*vec2(1.0,1.0/cdfyVSize));vec3 iblColour=textureCubeLodEXT(iblSource,direction,0.0).rgb;\n#else\nvec3 iblColour=texture2D(iblSource,(uv-vec2(0.0,iblStart)) *\nvec2(1.0,1.0/cdfyVSize))\n.rgb;\n#endif\nvec3 pdfColour=texture(icdf,(uv-vec2(0.0,pdfStart)) *\nvec2(1.0,1.0/cdfyVSize)).zzz;float cdfyColour =\ntexture2D(cdfy,(uv-vec2(0.0,cdfyStart))*vec2(2.0,1.0/cdfyVSize))\n.r;float icdfyColour =\ntexture2D(icdf,(uv-vec2(0.5,cdfyStart))*vec2(2.0,1.0/cdfyVSize))\n.g;float cdfxColour =\ntexture2D(cdfx,(uv-vec2(0.0,cdfxStart))*vec2(1.0,1.0/cdfxVSize))\n.r;float icdfxColour=texture2D(icdf,(uv-vec2(0.0,icdfxStart)) *\nvec2(1.0,1.0/cdfxVSize))\n.r;if (uv.x<0.0 || uv.x>1.0 || uv.y<0.0 || uv.y>1.0) {colour=backgroundColour;} else if (uv.y>iblStart) {colour+=iblColour;} else if (uv.y>pdfStart) {colour+=pdfColour;} else if (uv.y>cdfyStart && uv.x<0.5) {colour.r+=cdfyColour/float(cdfyHeight);} else if (uv.y>cdfyStart && uv.x>0.5) {colour.r+=icdfyColour;} else if (uv.y>cdfxStart) {colour.r+=cdfxColour/float(cdfxWidth);} else if (uv.y>icdfxStart) {colour.r+=icdfxColour;}\ngl_FragColor=vec4(colour,1.0);glFragColor.rgb=mix(gl_FragColor.rgb,backgroundColour,0.5);}\n`;\n// Sideeffect\nif (!ShaderStore.ShadersStore[name]) {\n    ShaderStore.ShadersStore[name] = shader;\n}\n/** @internal */\nexport const iblCdfDebugPixelShader = { name, shader };\n//# sourceMappingURL=iblCdfDebug.fragment.js.map"], "names": ["name", "shader", "ShaderStore", "iblCdfDebugPixelShader"], "mappings": "wCAEA,MAAMA,EAAO,yBACPC,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2CVC,EAAY,aAAaF,CAAI,IAC9BE,EAAY,aAAaF,CAAI,EAAIC,GAGzB,MAACE,EAAyB,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}