import{a as r,V as A,Q as T,M as F,an as b,ao as M}from"./index-Dpxo-yl_.js";import{GLTFLoader as V,ArrayItem as E}from"./glTFLoader-9Z3KGax5.js";import"./thinInstanceMesh-BkxjteXz.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./bone-kZWM5-u7.js";import"./rawTexture-DmvUfjqF.js";import"./assetContainer-BRzQBugc.js";import"./objectModelMapping-BR4RdEzn.js";const n="EXT_mesh_gpu_instancing";class g{constructor(i){this.name=n,this._loader=i,this.enabled=this._loader.isExtensionUsed(n)}dispose(){this._loader=null}loadNodeAsync(i,t,f){return V.LoadExtensionAsync(i,t,this.name,(u,h)=>{this._loader._disableInstancedMesh++;const p=this._loader.loadNodeAsync(`/nodes/${t.index}`,t,f);if(this._loader._disableInstancedMesh--,!t._primitiveBabylonMeshes)return p;const c=new Array;let s=0;const l=a=>{if(h.attributes[a]==null){c.push(Promise.resolve(null));return}const o=E.Get(`${u}/attributes/${a}`,this._loader.gltf.accessors,h.attributes[a]);if(c.push(this._loader._loadFloatAccessorAsync(`/accessors/${o.bufferView}`,o)),s===0)s=o.count;else if(s!==o.count)throw new Error(`${u}/attributes: Instance buffer accessors do not have the same count.`)};return l("TRANSLATION"),l("ROTATION"),l("SCALE"),p.then(a=>Promise.all(c).then(([o,d,_])=>{const y=new Float32Array(s*16);r.Vector3[0].copyFromFloats(0,0,0),r.Quaternion[0].copyFromFloats(0,0,0,1),r.Vector3[1].copyFromFloats(1,1,1);for(let e=0;e<s;++e)o&&A.FromArrayToRef(o,e*3,r.Vector3[0]),d&&T.FromArrayToRef(d,e*4,r.Quaternion[0]),_&&A.FromArrayToRef(_,e*3,r.Vector3[1]),F.ComposeToRef(r.Vector3[1],r.Quaternion[0],r.Vector3[0],r.Matrix[0]),r.Matrix[0].copyToArray(y,e*16);for(const e of t._primitiveBabylonMeshes)e.thinInstanceSetBuffer("matrix",y,16,!0);return a}))})}}b(n);M(n,!0,m=>new g(m));export{g as EXT_mesh_gpu_instancing};
//# sourceMappingURL=EXT_mesh_gpu_instancing-CUZKZAOM.js.map
