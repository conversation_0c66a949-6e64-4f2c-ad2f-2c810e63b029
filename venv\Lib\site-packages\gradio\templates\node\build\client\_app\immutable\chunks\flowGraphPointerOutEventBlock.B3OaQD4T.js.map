{"version": 3, "file": "flowGraphPointerOutEventBlock.B3OaQD4T.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphPointerOutEventBlock.js"], "sourcesContent": ["import { FlowGraphEventBlock } from \"../../flowGraphEventBlock.js\";\nimport { RichTypeAny, RichTypeNumber } from \"../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../Misc/typeStore.js\";\nimport { _isADescendantOf } from \"../../utils.js\";\n/**\n * A pointe out event block.\n * This block can be used as an entry pointer to when a pointer is out of a specific target mesh.\n */\nexport class FlowGraphPointerOutEventBlock extends FlowGraphEventBlock {\n    constructor(config) {\n        super(config);\n        this.type = \"PointerOut\" /* FlowGraphEventType.PointerOut */;\n        this.pointerId = this.registerDataOutput(\"pointerId\", RichTypeNumber);\n        this.targetMesh = this.registerDataInput(\"targetMesh\", RichTypeAny, config?.targetMesh);\n        this.meshOutOfPointer = this.registerDataOutput(\"meshOutOfPointer\", RichTypeAny);\n    }\n    _executeEvent(context, payload) {\n        const mesh = this.targetMesh.getValue(context);\n        this.meshOutOfPointer.setValue(payload.mesh, context);\n        this.pointerId.setValue(payload.pointerId, context);\n        const skipEvent = payload.over && _isADescendantOf(payload.mesh, mesh);\n        if (!skipEvent && (payload.mesh === mesh || _isADescendantOf(payload.mesh, mesh))) {\n            this._execute(context);\n            return !this.config?.stopPropagation;\n        }\n        return true;\n    }\n    _preparePendingTasks(_context) {\n        // no-op\n    }\n    _cancelPendingTasks(_context) {\n        // no-op\n    }\n    getClassName() {\n        return \"FlowGraphPointerOutEventBlock\" /* FlowGraphBlockNames.PointerOutEvent */;\n    }\n}\nRegisterClass(\"FlowGraphPointerOutEventBlock\" /* FlowGraphBlockNames.PointerOutEvent */, FlowGraphPointerOutEventBlock);\n//# sourceMappingURL=flowGraphPointerOutEventBlock.js.map"], "names": ["FlowGraphPointerOutEventBlock", "FlowGraphEventBlock", "config", "RichTypeNumber", "RichTypeAny", "context", "payload", "mesh", "_isADescendantOf", "_a", "_context", "RegisterClass"], "mappings": "8JAQO,MAAMA,UAAsCC,CAAoB,CACnE,YAAYC,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,KAAO,aACZ,KAAK,UAAY,KAAK,mBAAmB,YAAaC,CAAc,EACpE,KAAK,WAAa,KAAK,kBAAkB,aAAcC,EAAaF,GAAA,YAAAA,EAAQ,UAAU,EACtF,KAAK,iBAAmB,KAAK,mBAAmB,mBAAoBE,CAAW,CAClF,CACD,cAAcC,EAASC,EAAS,OAC5B,MAAMC,EAAO,KAAK,WAAW,SAASF,CAAO,EAI7C,OAHA,KAAK,iBAAiB,SAASC,EAAQ,KAAMD,CAAO,EACpD,KAAK,UAAU,SAASC,EAAQ,UAAWD,CAAO,EAE9C,EADcC,EAAQ,MAAQE,EAAiBF,EAAQ,KAAMC,CAAI,KAClDD,EAAQ,OAASC,GAAQC,EAAiBF,EAAQ,KAAMC,CAAI,IAC3E,KAAK,SAASF,CAAO,EACd,GAACI,EAAA,KAAK,SAAL,MAAAA,EAAa,kBAElB,EACV,CACD,qBAAqBC,EAAU,CAE9B,CACD,oBAAoBA,EAAU,CAE7B,CACD,cAAe,CACX,MAAO,+BACV,CACL,CACAC,EAAc,gCAA2EX,CAA6B", "x_google_ignoreList": [0]}