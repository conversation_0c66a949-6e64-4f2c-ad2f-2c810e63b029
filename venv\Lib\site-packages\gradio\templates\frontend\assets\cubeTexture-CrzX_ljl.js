import{B as z,M as g,O as C,V as y,T as M,a as S,b as P,S as R,G as E,_ as o,s as _,c as v,d as A,R as D}from"./index-Dpxo-yl_.js";import"./abstractEngine.cubeTexture-7uN6d2SV.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";const L=.8;class l extends z{set boundingBoxSize(t){if(this._boundingBoxSize&&this._boundingBoxSize.equals(t))return;this._boundingBoxSize=t;const i=this.getScene();i&&i.markAllMaterialsAsDirty(1)}get boundingBoxSize(){return this._boundingBoxSize}set rotationY(t){this._rotationY=t,this.setReflectionTextureMatrix(g.RotationY(this._rotationY))}get rotationY(){return this._rotationY}get noMipmap(){return this._noMipmap}get forcedExtension(){return this._forcedExtension}static CreateFromImages(t,i,e){let r="";return t.forEach(s=>r+=s),new l(r,i,null,e,t)}static CreateFromPrefilteredData(t,i,e=null,r=!0){const s=i.useDelayedTextureLoading;i.useDelayedTextureLoading=!1;const a=new l(t,i,null,!1,null,null,null,void 0,!0,e,r);return i.useDelayedTextureLoading=s,a}constructor(t,i,e=null,r=!1,s=null,a=null,n=null,u=5,c=!1,h=null,f=!1,m=L,x=0,b,d){super(i),this.onLoadObservable=new C,this.boundingBoxPosition=y.Zero(),this._rotationY=0,this._files=null,this._forcedExtension=null,this._extensions=null,this._textureMatrixRefraction=new g,this._buffer=null,this.name=t,this.url=t,this._noMipmap=r,this.hasAlpha=!1,this.isCube=!0,this._textureMatrix=g.Identity(),this.coordinatesMode=M.CUBIC_MODE;let p=null,B=null;e!==null&&!Array.isArray(e)?(p=e.extensions??null,this._noMipmap=e.noMipmap??!1,s=e.files??null,B=e.buffer??null,this._format=e.format??5,c=e.prefiltered??!1,h=e.forcedExtension??null,this._createPolynomials=e.createPolynomials??!1,this._lodScale=e.lodScale??L,this._lodOffset=e.lodOffset??0,this._loaderOptions=e.loaderOptions,this._useSRGBBuffer=e.useSRGBBuffer,a=e.onLoad??null,n=e.onError??null):(this._noMipmap=r,this._format=u,this._createPolynomials=f,p=e,this._loaderOptions=b,this._useSRGBBuffer=d,this._lodScale=m,this._lodOffset=x),!(!t&&!s)&&this.updateURL(t,h,a,c,n,p,this.getScene()?.useDelayedTextureLoading,s,B)}getClassName(){return"CubeTexture"}updateURL(t,i=null,e=null,r=!1,s=null,a=null,n=!1,u=null,c=null){(!this.name||this.name.startsWith("data:"))&&(this.name=t),this.url=t,i&&(this._forcedExtension=i);const h=t.lastIndexOf("."),f=i||(h>-1?t.substring(h).toLowerCase():""),m=f.indexOf(".dds")===0,x=f.indexOf(".env")===0,b=f.indexOf(".basis")===0;if(x?(this.gammaSpace=!1,this._prefiltered=!1,this.anisotropicFilteringLevel=1):(this._prefiltered=r,r&&(this.gammaSpace=!1,this.anisotropicFilteringLevel=1)),u)this._files=u;else if(!b&&!x&&!m&&!a&&(a=["_px.jpg","_py.jpg","_pz.jpg","_nx.jpg","_ny.jpg","_nz.jpg"]),this._files=this._files||[],this._files.length=0,a){for(let d=0;d<a.length;d++)this._files.push(t+a[d]);this._extensions=a}this._buffer=c,n?(this.delayLoadState=4,this._delayedOnLoad=e,this._delayedOnError=s):this._loadTexture(e,s)}delayLoad(t){this.delayLoadState===4&&(t&&(this._forcedExtension=t),this.delayLoadState=1,this._loadTexture(this._delayedOnLoad,this._delayedOnError))}getReflectionTextureMatrix(){return this._textureMatrix}setReflectionTextureMatrix(t){if(t.updateFlag===this._textureMatrix.updateFlag||(t.isIdentity()!==this._textureMatrix.isIdentity()&&this.getScene()?.markAllMaterialsAsDirty(1,s=>s.getActiveTextures().indexOf(this)!==-1),this._textureMatrix=t,!this.getScene()?.useRightHandedSystem))return;const i=S.Vector3[0],e=S.Quaternion[0],r=S.Vector3[1];this._textureMatrix.decompose(i,e,r),e.z*=-1,e.w*=-1,g.ComposeToRef(i,e,r,this._textureMatrixRefraction)}getRefractionTextureMatrix(){return this.getScene()?.useRightHandedSystem?this._textureMatrixRefraction:this._textureMatrix}_loadTexture(t=null,i=null){const e=this.getScene(),r=this._texture;this._texture=this._getFromCache(this.url,this._noMipmap,void 0,void 0,this._useSRGBBuffer,this.isCube);const s=()=>{this.onLoadObservable.notifyObservers(this),r&&(r.dispose(),this.getScene()?.markAllMaterialsAsDirty(1)),t&&t()},a=(n,u)=>{this._loadingError=!0,this._errorObject={message:n,exception:u},i&&i(n,u),M.OnTextureLoadErrorObservable.notifyObservers(this)};this._texture?this._texture.isReady?P.SetImmediate(()=>s()):this._texture.onLoadedObservable.add(()=>s()):(this._prefiltered?this._texture=this._getEngine().createPrefilteredCubeTexture(this.url,e,this._lodScale,this._lodOffset,t,a,this._format,this._forcedExtension,this._createPolynomials):this._texture=this._getEngine().createCubeTexture(this.url,e,this._files,this._noMipmap,t,a,this._format,this._forcedExtension,!1,this._lodScale,this._lodOffset,null,this._loaderOptions,!!this._useSRGBBuffer,this._buffer),this._texture?.onLoadedObservable.add(()=>this.onLoadObservable.notifyObservers(this)))}static Parse(t,i,e){const r=R.Parse(()=>{let s=!1;return t.prefiltered&&(s=t.prefiltered),new l(e+(t.url??t.name),i,t.extensions,!1,t.files||null,null,null,void 0,s,t.forcedExtension)},t,i);if(t.boundingBoxPosition&&(r.boundingBoxPosition=y.FromArray(t.boundingBoxPosition)),t.boundingBoxSize&&(r.boundingBoxSize=y.FromArray(t.boundingBoxSize)),t.animations)for(let s=0;s<t.animations.length;s++){const a=t.animations[s],n=E("BABYLON.Animation");n&&r.animations.push(n.Parse(a))}return r}clone(){let t=0;const i=R.Clone(()=>{const e=new l(this.url,this.getScene()||this._getEngine(),this._extensions,this._noMipmap,this._files);return t=e.uniqueId,e},this);return i.uniqueId=t,i}}o([_()],l.prototype,"url",void 0);o([v()],l.prototype,"boundingBoxPosition",void 0);o([v()],l.prototype,"boundingBoxSize",null);o([_("rotationY")],l.prototype,"rotationY",null);o([_("files")],l.prototype,"_files",void 0);o([_("forcedExtension")],l.prototype,"_forcedExtension",void 0);o([_("extensions")],l.prototype,"_extensions",void 0);o([A("textureMatrix")],l.prototype,"_textureMatrix",void 0);o([A("textureMatrixRefraction")],l.prototype,"_textureMatrixRefraction",void 0);M._CubeTextureParser=l.Parse;D("BABYLON.CubeTexture",l);export{l as CubeTexture};
//# sourceMappingURL=cubeTexture-CrzX_ljl.js.map
