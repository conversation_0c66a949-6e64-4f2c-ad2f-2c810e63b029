#pragma once

// @generated by torchgen/gen.py from NativeMetaFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/TensorIterator.h>
#include <ATen/TensorMeta.h>
#include <tuple>
#include <vector>

namespace at {
namespace meta {

struct TORCH_API structured_isin_Tensor_Tensor : public at::impl::MetaBase {
    
    
    void meta(const at::Tensor & elements, const at::Tensor & test_elements, bool assume_unique, bool invert);
};
struct TORCH_API structured_isin_Tensor_Scalar : public at::impl::MetaBase {
    
    
    void meta(const at::Tensor & elements, const at::Scalar & test_element, bool assume_unique, bool invert);
};
struct TORCH_API structured_isin_Scalar_Tensor : public at::impl::MetaBase {
    
    
    void meta(const at::Scalar & element, const at::Tensor & test_elements, bool assume_unique, bool invert);
};

} // namespace native
} // namespace at
