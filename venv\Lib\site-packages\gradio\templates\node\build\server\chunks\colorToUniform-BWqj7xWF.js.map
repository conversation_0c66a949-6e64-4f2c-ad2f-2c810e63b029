{"version": 3, "file": "colorToUniform-BWqj7xWF.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/colorToUniform.js"], "sourcesContent": ["const l={name:\"local-uniform-bit\",vertex:{header:`\n\n            struct LocalUniforms {\n                uTransformMatrix:mat3x3<f32>,\n                uColor:vec4<f32>,\n                uRound:f32,\n            }\n\n            @group(1) @binding(0) var<uniform> localUniforms : LocalUniforms;\n        `,main:`\n            vColor *= localUniforms.uColor;\n            modelMatrix *= localUniforms.uTransformMatrix;\n        `,end:`\n            if(localUniforms.uRound == 1)\n            {\n                vPosition = vec4(roundPixels(vPosition.xy, globalUniforms.uResolution), vPosition.zw);\n            }\n        `}},e={...l,vertex:{...l.vertex,header:l.vertex.header.replace(\"group(1)\",\"group(2)\")}},n={name:\"local-uniform-bit\",vertex:{header:`\n\n            uniform mat3 uTransformMatrix;\n            uniform vec4 uColor;\n            uniform float uRound;\n        `,main:`\n            vColor *= uColor;\n            modelMatrix = uTransformMatrix;\n        `,end:`\n            if(uRound == 1.)\n            {\n                gl_Position.xy = roundPixels(gl_Position.xy, uResolution);\n            }\n        `}};class a{constructor(){this.batcherName=\"default\",this.topology=\"triangle-list\",this.attributeSize=4,this.indexSize=6,this.packAsQuad=!0,this.roundPixels=0,this._attributeStart=0,this._batcher=null,this._batch=null}get blendMode(){return this.renderable.groupBlendMode}get color(){return this.renderable.groupColorAlpha}reset(){this.renderable=null,this.texture=null,this._batcher=null,this._batch=null,this.bounds=null}}function s(o,r,i){const t=(o>>24&255)/255;r[i++]=(o&255)/255*t,r[i++]=(o>>8&255)/255*t,r[i++]=(o>>16&255)/255*t,r[i++]=t}export{a as B,l as a,n as b,s as c,e as l};\n//# sourceMappingURL=colorToUniform.js.map\n"], "names": [], "mappings": "AAAK,MAAC,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC;AAChB;AACA;AACA,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;AACf;AACA;AACA;AACA;AACA,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC;AAC5I;AACA;AACA;AACA;AACA,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC;AAChB;AACA;AACA,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;AACf;AACA;AACA;AACA;AACA,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAI,CAAC,IAAI,SAAS,EAAE,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,KAAK,EAAE,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAC;;;;"}