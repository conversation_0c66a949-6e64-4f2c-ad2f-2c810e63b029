import { c as create_ssr_component, e as escape } from './ssr-C3HYbsxA.js';

const A={code:".gallery.svelte-1ayixqk{padding:var(--size-1) var(--size-2)}",map:'{"version":3,"file":"Example.svelte","sources":["Example.svelte"],"sourcesContent":["<script lang=\\"ts\\">export let value;\\nexport let type;\\nexport let selected = false;\\nexport let choices;\\nlet name_string;\\nif (value === null) {\\n    name_string = \\"\\";\\n}\\nelse {\\n    let name = choices.find((pair) => pair[1] === value);\\n    name_string = name ? name[0] : \\"\\";\\n}\\n<\/script>\\n\\n<div\\n\\tclass:table={type === \\"table\\"}\\n\\tclass:gallery={type === \\"gallery\\"}\\n\\tclass:selected\\n>\\n\\t{name_string}\\n</div>\\n\\n<style>\\n\\t.gallery {\\n\\t\\tpadding: var(--size-1) var(--size-2);\\n\\t}</style>\\n"],"names":[],"mappings":"AAuBC,uBAAS,CACR,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CACpC"}'},u=create_ssr_component((v,e,t,m)=>{let{value:l}=e,{type:a}=e,{selected:s=!1}=e,{choices:n}=e,c;if(l===null)c="";else {let i=n.find(r=>r[1]===l);c=i?i[0]:"";}return e.value===void 0&&t.value&&l!==void 0&&t.value(l),e.type===void 0&&t.type&&a!==void 0&&t.type(a),e.selected===void 0&&t.selected&&s!==void 0&&t.selected(s),e.choices===void 0&&t.choices&&n!==void 0&&t.choices(n),v.css.add(A),`<div class="${["svelte-1ayixqk",(a==="table"?"table":"")+" "+(a==="gallery"?"gallery":"")+" "+(s?"selected":"")].join(" ").trim()}">${escape(c)} </div>`});

export { u as default };
//# sourceMappingURL=Example23-DpqwuJHm.js.map
