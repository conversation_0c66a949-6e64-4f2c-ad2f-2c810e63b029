{"version": 3, "file": "index-ClteBeTX.js", "sources": ["../../../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/store/index.js"], "sourcesContent": ["import {\n\trun_all,\n\tsubscribe,\n\tnoop,\n\tsafe_not_equal,\n\tis_function,\n\tget_store_value\n} from '../internal/index.js';\n\nconst subscriber_queue = [];\n\n/**\n * Creates a `Readable` store that allows reading by subscription.\n *\n * https://svelte.dev/docs/svelte-store#readable\n * @template T\n * @param {T} [value] initial value\n * @param {import('./public.js').StartStopNotifier<T>} [start]\n * @returns {import('./public.js').Readable<T>}\n */\nexport function readable(value, start) {\n\treturn {\n\t\tsubscribe: writable(value, start).subscribe\n\t};\n}\n\n/**\n * Create a `Writable` store that allows both updating and reading by subscription.\n *\n * https://svelte.dev/docs/svelte-store#writable\n * @template T\n * @param {T} [value] initial value\n * @param {import('./public.js').StartStopNotifier<T>} [start]\n * @returns {import('./public.js').Writable<T>}\n */\nexport function writable(value, start = noop) {\n\t/** @type {import('./public.js').Unsubscriber} */\n\tlet stop;\n\t/** @type {Set<import('./private.js').SubscribeInvalidateTuple<T>>} */\n\tconst subscribers = new Set();\n\t/** @param {T} new_value\n\t * @returns {void}\n\t */\n\tfunction set(new_value) {\n\t\tif (safe_not_equal(value, new_value)) {\n\t\t\tvalue = new_value;\n\t\t\tif (stop) {\n\t\t\t\t// store is ready\n\t\t\t\tconst run_queue = !subscriber_queue.length;\n\t\t\t\tfor (const subscriber of subscribers) {\n\t\t\t\t\tsubscriber[1]();\n\t\t\t\t\tsubscriber_queue.push(subscriber, value);\n\t\t\t\t}\n\t\t\t\tif (run_queue) {\n\t\t\t\t\tfor (let i = 0; i < subscriber_queue.length; i += 2) {\n\t\t\t\t\t\tsubscriber_queue[i][0](subscriber_queue[i + 1]);\n\t\t\t\t\t}\n\t\t\t\t\tsubscriber_queue.length = 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @param {import('./public.js').Updater<T>} fn\n\t * @returns {void}\n\t */\n\tfunction update(fn) {\n\t\tset(fn(value));\n\t}\n\n\t/**\n\t * @param {import('./public.js').Subscriber<T>} run\n\t * @param {import('./private.js').Invalidator<T>} [invalidate]\n\t * @returns {import('./public.js').Unsubscriber}\n\t */\n\tfunction subscribe(run, invalidate = noop) {\n\t\t/** @type {import('./private.js').SubscribeInvalidateTuple<T>} */\n\t\tconst subscriber = [run, invalidate];\n\t\tsubscribers.add(subscriber);\n\t\tif (subscribers.size === 1) {\n\t\t\tstop = start(set, update) || noop;\n\t\t}\n\t\trun(value);\n\t\treturn () => {\n\t\t\tsubscribers.delete(subscriber);\n\t\t\tif (subscribers.size === 0 && stop) {\n\t\t\t\tstop();\n\t\t\t\tstop = null;\n\t\t\t}\n\t\t};\n\t}\n\treturn { set, update, subscribe };\n}\n\n/**\n * Derived value store by synchronizing one or more readable stores and\n * applying an aggregation function over its input values.\n *\n * https://svelte.dev/docs/svelte-store#derived\n * @template {import('./private.js').Stores} S\n * @template T\n * @overload\n * @param {S} stores - input stores\n * @param {(values: import('./private.js').StoresValues<S>, set: (value: T) => void, update: (fn: import('./public.js').Updater<T>) => void) => import('./public.js').Unsubscriber | void} fn - function callback that aggregates the values\n * @param {T} [initial_value] - initial value\n * @returns {import('./public.js').Readable<T>}\n */\n\n/**\n * Derived value store by synchronizing one or more readable stores and\n * applying an aggregation function over its input values.\n *\n * https://svelte.dev/docs/svelte-store#derived\n * @template {import('./private.js').Stores} S\n * @template T\n * @overload\n * @param {S} stores - input stores\n * @param {(values: import('./private.js').StoresValues<S>) => T} fn - function callback that aggregates the values\n * @param {T} [initial_value] - initial value\n * @returns {import('./public.js').Readable<T>}\n */\n\n/**\n * @template {import('./private.js').Stores} S\n * @template T\n * @param {S} stores\n * @param {Function} fn\n * @param {T} [initial_value]\n * @returns {import('./public.js').Readable<T>}\n */\nexport function derived(stores, fn, initial_value) {\n\tconst single = !Array.isArray(stores);\n\t/** @type {Array<import('./public.js').Readable<any>>} */\n\tconst stores_array = single ? [stores] : stores;\n\tif (!stores_array.every(Boolean)) {\n\t\tthrow new Error('derived() expects stores as input, got a falsy value');\n\t}\n\tconst auto = fn.length < 2;\n\treturn readable(initial_value, (set, update) => {\n\t\tlet started = false;\n\t\tconst values = [];\n\t\tlet pending = 0;\n\t\tlet cleanup = noop;\n\t\tconst sync = () => {\n\t\t\tif (pending) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tcleanup();\n\t\t\tconst result = fn(single ? values[0] : values, set, update);\n\t\t\tif (auto) {\n\t\t\t\tset(result);\n\t\t\t} else {\n\t\t\t\tcleanup = is_function(result) ? result : noop;\n\t\t\t}\n\t\t};\n\t\tconst unsubscribers = stores_array.map((store, i) =>\n\t\t\tsubscribe(\n\t\t\t\tstore,\n\t\t\t\t(value) => {\n\t\t\t\t\tvalues[i] = value;\n\t\t\t\t\tpending &= ~(1 << i);\n\t\t\t\t\tif (started) {\n\t\t\t\t\t\tsync();\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t() => {\n\t\t\t\t\tpending |= 1 << i;\n\t\t\t\t}\n\t\t\t)\n\t\t);\n\t\tstarted = true;\n\t\tsync();\n\t\treturn function stop() {\n\t\t\trun_all(unsubscribers);\n\t\t\tcleanup();\n\t\t\t// We need to set this to false because callbacks can still happen despite having unsubscribed:\n\t\t\t// Callbacks might already be placed in the queue which doesn't know it should no longer\n\t\t\t// invoke this derived store.\n\t\t\tstarted = false;\n\t\t};\n\t});\n}\n\n/**\n * Takes a store and returns a new one derived from the old one that is readable.\n *\n * https://svelte.dev/docs/svelte-store#readonly\n * @template T\n * @param {import('./public.js').Readable<T>} store  - store to make readonly\n * @returns {import('./public.js').Readable<T>}\n */\nexport function readonly(store) {\n\treturn {\n\t\tsubscribe: store.subscribe.bind(store)\n\t};\n}\n\nexport { get_store_value as get };\n"], "names": [], "mappings": ";;AASA,MAAM,gBAAgB,GAAG,EAAE,CAAC;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE;AACvC,CAAC,OAAO;AACR,EAAE,SAAS,EAAE,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,SAAS;AAC7C,EAAE,CAAC;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI,EAAE;AAC9C;AACA,CAAC,IAAI,IAAI,CAAC;AACV;AACA,CAAC,MAAM,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;AAC/B;AACA;AACA;AACA,CAAC,SAAS,GAAG,CAAC,SAAS,EAAE;AACzB,EAAE,IAAI,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE;AACxC,GAAG,KAAK,GAAG,SAAS,CAAC;AACrB,GAAG,IAAI,IAAI,EAAE;AACb;AACA,IAAI,MAAM,SAAS,GAAG,CAAC,gBAAgB,CAAC,MAAM,CAAC;AAC/C,IAAI,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;AAC1C,KAAK,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;AACrB,KAAK,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AAC9C,KAAK;AACL,IAAI,IAAI,SAAS,EAAE;AACnB,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AAC1D,MAAM,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACtD,MAAM;AACN,KAAK,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;AACjC,KAAK;AACL,IAAI;AACJ,GAAG;AACH,EAAE;AACF;AACA;AACA;AACA;AACA;AACA,CAAC,SAAS,MAAM,CAAC,EAAE,EAAE;AACrB,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AACjB,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,SAAS,SAAS,CAAC,GAAG,EAAE,UAAU,GAAG,IAAI,EAAE;AAC5C;AACA,EAAE,MAAM,UAAU,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;AACvC,EAAE,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AAC9B,EAAE,IAAI,WAAW,CAAC,IAAI,KAAK,CAAC,EAAE;AAC9B,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC;AACrC,GAAG;AACH,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;AACb,EAAE,OAAO,MAAM;AACf,GAAG,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AAClC,GAAG,IAAI,WAAW,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,EAAE;AACvC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,GAAG,IAAI,CAAC;AAChB,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE;AACF,CAAC,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;AACnC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,OAAO,CAAC,MAAM,EAAE,EAAE,EAAE,aAAa,EAAE;AACnD,CAAC,MAAM,MAAM,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACvC;AACA,CAAC,MAAM,YAAY,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;AACjD,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;AACnC,EAAE,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;AAC1E,EAAE;AACF,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;AAC5B,CAAC,OAAO,QAAQ,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK;AACjD,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC;AACtB,EAAE,MAAM,MAAM,GAAG,EAAE,CAAC;AACpB,EAAE,IAAI,OAAO,GAAG,CAAC,CAAC;AAClB,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC;AACrB,EAAE,MAAM,IAAI,GAAG,MAAM;AACrB,GAAG,IAAI,OAAO,EAAE;AAChB,IAAI,OAAO;AACX,IAAI;AACJ,GAAG,OAAO,EAAE,CAAC;AACb,GAAG,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;AAC/D,GAAG,IAAI,IAAI,EAAE;AACb,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC;AAChB,IAAI,MAAM;AACV,IAAI,OAAO,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC;AAClD,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;AAClD,GAAG,SAAS;AACZ,IAAI,KAAK;AACT,IAAI,CAAC,KAAK,KAAK;AACf,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AACvB,KAAK,OAAO,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1B,KAAK,IAAI,OAAO,EAAE;AAClB,MAAM,IAAI,EAAE,CAAC;AACb,MAAM;AACN,KAAK;AACL,IAAI,MAAM;AACV,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;AACvB,KAAK;AACL,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,OAAO,GAAG,IAAI,CAAC;AACjB,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,OAAO,SAAS,IAAI,GAAG;AACzB,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;AAC1B,GAAG,OAAO,EAAE,CAAC;AACb;AACA;AACA;AACA,GAAG,OAAO,GAAG,KAAK,CAAC;AACnB,GAAG,CAAC;AACJ,EAAE,CAAC,CAAC;AACJ;;;;", "x_google_ignoreList": [0]}