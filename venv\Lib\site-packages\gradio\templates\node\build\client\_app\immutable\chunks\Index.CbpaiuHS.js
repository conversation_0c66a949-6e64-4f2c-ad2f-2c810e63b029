import{SvelteComponent as X,init as x,safe_not_equal as $,element as b,space as S,claim_element as w,children as F,claim_space as D,detach as d,attr as m,insert_hydration as C,append_hydration as g,noop as H,onMount as te,text as O,claim_text as T,listen as ne,set_data as ee,ensure_array_like as R,empty as V,update_keyed_each as ae,destroy_block as ie,binding_callbacks as se,get_svelte_dataset as le,HtmlTagHydration as z,claim_html_tag as N,set_style as re,toggle_class as Z,create_component as oe,claim_component as fe,mount_component as ce,transition_in as ue,transition_out as he,destroy_component as de}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{R as j}from"./2.B2AoQPnG.js";(function(s){s.languages.typescript=s.languages.extend("javascript",{"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|type)\s+)(?!keyof\b)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?:\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,lookbehind:!0,greedy:!0,inside:null},builtin:/\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\b/}),s.languages.typescript.keyword.push(/\b(?:abstract|declare|is|keyof|readonly|require)\b/,/\b(?:asserts|infer|interface|module|namespace|type)\b(?=\s*(?:[{_$a-zA-Z\xA0-\uFFFF]|$))/,/\btype\b(?=\s*(?:[\{*]|$))/),delete s.languages.typescript.parameter,delete s.languages.typescript["literal-property"];var e=s.languages.extend("typescript",{});delete e["class-name"],s.languages.typescript["class-name"].inside=e,s.languages.insertBefore("typescript","function",{decorator:{pattern:/@[$\w\xA0-\uFFFF]+/,inside:{at:{pattern:/^@/,alias:"operator"},function:/^[\s\S]+/}},"generic-function":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\s*\()/,greedy:!0,inside:{function:/^#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*/,generic:{pattern:/<[\s\S]+/,alias:"class-name",inside:e}}}}),s.languages.ts=s.languages.typescript})(Prism);function B(s,e,l){const t=s.slice();return t[13]=e[l].type,t[14]=e[l].description,t[15]=e[l].default,t[16]=e[l].name,t}function U(s){let e,l,t,i,n,a,r,f,p;return{c(){e=b("div"),l=b("span"),t=O(s[1]),i=S(),n=b("button"),a=O("▼"),this.h()},l(c){e=w(c,"DIV",{class:!0});var _=F(e);l=w(_,"SPAN",{class:!0});var A=F(l);t=T(A,s[1]),A.forEach(d),i=D(_),n=w(_,"BUTTON",{class:!0,title:!0});var E=F(n);a=T(E,"▼"),E.forEach(d),_.forEach(d),this.h()},h(){m(l,"class","title svelte-1mlh4di"),m(n,"class","toggle-all svelte-1mlh4di"),m(n,"title",r=s[5]?"Close All":"Open All"),m(e,"class","header svelte-1mlh4di")},m(c,_){C(c,e,_),g(e,l),g(l,t),g(e,i),g(e,n),g(n,a),f||(p=ne(n,"click",s[6]),f=!0)},p(c,_){_&2&&ee(t,c[1]),_&32&&r!==(r=c[5]?"Close All":"Open All")&&m(n,"title",r)},d(c){c&&d(e),f=!1,p()}}}function Y(s){let e=[],l=new Map,t,i=R(s[4]);const n=a=>a[16];for(let a=0;a<i.length;a+=1){let r=B(s,i,a),f=n(r);l.set(f,e[a]=Q(f,r))}return{c(){for(let a=0;a<e.length;a+=1)e[a].c();t=V()},l(a){for(let r=0;r<e.length;r+=1)e[r].l(a);t=V()},m(a,r){for(let f=0;f<e.length;f+=1)e[f]&&e[f].m(a,r);C(a,t,r)},p(a,r){r&21&&(i=R(a[4]),e=ae(e,r,n,1,a,i,l,t.parentNode,ie,Q,t,B))},d(a){a&&d(t);for(let r=0;r<e.length;r+=1)e[r].d(a)}}}function G(s){let e,l,t="🔗",i;return{c(){e=b("a"),l=b("span"),l.textContent=t,this.h()},l(n){e=w(n,"A",{href:!0,class:!0});var a=F(e);l=w(a,"SPAN",{class:!0,"data-svelte-h":!0}),le(l)!=="svelte-1tjdumv"&&(l.textContent=t),a.forEach(d),this.h()},h(){m(l,"class","link-icon svelte-1mlh4di"),m(e,"href",i="#"+q(s[16]||"",s[2])),m(e,"class","param-link svelte-1mlh4di")},m(n,a){C(n,e,a),g(e,l)},p(n,a){a&20&&i!==(i="#"+q(n[16]||"",n[2]))&&m(e,"href",i)},d(n){n&&d(e)}}}function J(s){let e,l,t=s[13]+"",i;return{c(){e=O(": "),l=new z(!1),i=V(),this.h()},l(n){e=T(n,": "),l=N(n,!1),i=V(),this.h()},h(){l.a=i},m(n,a){C(n,e,a),l.m(t,n,a),C(n,i,a)},p(n,a){a&16&&t!==(t=n[13]+"")&&l.p(t)},d(n){n&&(d(e),d(i),l.d())}}}function K(s){let e,l,t="default",i,n,a,r,f=s[15]+"";return{c(){e=b("div"),l=b("span"),l.textContent=t,i=S(),n=b("code"),a=O("= "),r=new z(!1),this.h()},l(p){e=w(p,"DIV",{class:!0});var c=F(e);l=w(c,"SPAN",{class:!0,"data-svelte-h":!0}),le(l)!=="svelte-8y216r"&&(l.textContent=t),i=D(c),n=w(c,"CODE",{class:!0});var _=F(n);a=T(_,"= "),r=N(_,!1),_.forEach(d),c.forEach(d),this.h()},h(){m(l,"class","svelte-1mlh4di"),re(l,"padding-right","4px"),r.a=null,m(n,"class","svelte-1mlh4di"),m(e,"class","default svelte-1mlh4di"),Z(e,"last",!s[14])},m(p,c){C(p,e,c),g(e,l),g(e,i),g(e,n),g(n,a),r.m(f,n)},p(p,c){c&16&&f!==(f=p[15]+"")&&r.p(f),c&16&&Z(e,"last",!p[14])},d(p){p&&d(e)}}}function P(s){let e,l,t,i=W(s[14])+"";return{c(){e=b("div"),l=b("p"),t=new z(!1),this.h()},l(n){e=w(n,"DIV",{class:!0});var a=F(e);l=w(a,"P",{});var r=F(l);t=N(r,!1),r.forEach(d),a.forEach(d),this.h()},h(){t.a=null,m(e,"class","description svelte-1mlh4di")},m(n,a){C(n,e,a),g(e,l),t.m(i,l)},p(n,a){a&16&&i!==(i=W(n[14])+"")&&t.p(i)},d(n){n&&d(e)}}}function Q(s,e){let l,t,i,n,a,r=e[16]+"",f,p,c,_,A,E,v=e[2]&&G(e),k=e[13]&&J(e),o=e[15]&&K(e),u=e[14]&&P(e);return{key:s,first:null,c(){l=b("details"),t=b("summary"),v&&v.c(),i=S(),n=b("pre"),a=b("code"),f=O(r),k&&k.c(),c=S(),o&&o.c(),_=S(),u&&u.c(),A=S(),this.h()},l(y){l=w(y,"DETAILS",{class:!0,id:!0});var h=F(l);t=w(h,"SUMMARY",{class:!0});var I=F(t);v&&v.l(I),i=D(I),n=w(I,"PRE",{class:!0});var L=F(n);a=w(L,"CODE",{class:!0});var M=F(a);f=T(M,r),k&&k.l(M),M.forEach(d),L.forEach(d),I.forEach(d),c=D(h),o&&o.l(h),_=D(h),u&&u.l(h),A=D(h),h.forEach(d),this.h()},h(){m(a,"class","svelte-1mlh4di"),m(n,"class",p="language-"+e[0]+" svelte-1mlh4di"),m(t,"class","type svelte-1mlh4di"),m(l,"class","param md svelte-1mlh4di"),m(l,"id",E=e[2]?q(e[16]||"",e[2]):void 0),this.first=l},m(y,h){C(y,l,h),g(l,t),v&&v.m(t,null),g(t,i),g(t,n),g(n,a),g(a,f),k&&k.m(a,null),g(l,c),o&&o.m(l,null),g(l,_),u&&u.m(l,null),g(l,A)},p(y,h){e=y,e[2]?v?v.p(e,h):(v=G(e),v.c(),v.m(t,i)):v&&(v.d(1),v=null),h&16&&r!==(r=e[16]+"")&&ee(f,r),e[13]?k?k.p(e,h):(k=J(e),k.c(),k.m(a,null)):k&&(k.d(1),k=null),h&1&&p!==(p="language-"+e[0]+" svelte-1mlh4di")&&m(n,"class",p),e[15]?o?o.p(e,h):(o=K(e),o.c(),o.m(l,_)):o&&(o.d(1),o=null),e[14]?u?u.p(e,h):(u=P(e),u.c(),u.m(l,A)):u&&(u.d(1),u=null),h&20&&E!==(E=e[2]?q(e[16]||"",e[2]):void 0)&&m(l,"id",E)},d(y){y&&d(l),v&&v.d(),k&&k.d(),o&&o.d(),u&&u.d()}}}function _e(s){let e,l,t=s[1]!==null&&U(s),i=s[4]&&Y(s);return{c(){e=b("div"),t&&t.c(),l=S(),i&&i.c(),this.h()},l(n){e=w(n,"DIV",{class:!0});var a=F(e);t&&t.l(a),l=D(a),i&&i.l(a),a.forEach(d),this.h()},h(){m(e,"class","wrap svelte-1mlh4di")},m(n,a){C(n,e,a),t&&t.m(e,null),g(e,l),i&&i.m(e,null),s[9](e)},p(n,[a]){n[1]!==null?t?t.p(n,a):(t=U(n),t.c(),t.m(e,l)):t&&(t.d(1),t=null),n[4]?i?i.p(n,a):(i=Y(n),i.c(),i.m(e,null)):i&&(i.d(1),i=null)},i:H,o:H,d(n){n&&d(e),t&&t.d(),i&&i.d(),s[9](null)}}}function q(s,e){let l="param-";return typeof e=="string"&&(l+=e+"-"),l+s.toLowerCase().replace(/[^a-z0-9]+/g,"-")}function W(s){return s.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;").replace(/\[([^\]]+)\]\(([^)]+)\)/g,'<a href="$2" target="_blank">$1</a>')}function me(s,e,l){let{docs:t}=e,{lang:i="python"}=e,{linkify:n=[]}=e,{header:a}=e,{anchor_links:r=!1}=e,f,p,c=!1;function _(o,u){let y=j.highlight(o,j.languages[u],u);for(const h of n)y=y.replace(new RegExp(h,"g"),`<a href="#h-${h.toLocaleLowerCase()}">${h}</a>`);return y}function A(o,u){return o?Object.entries(o).map(([y,{type:h,description:I,default:L}])=>{let M=h?_(h,u):null;return{name:y,type:M,description:I,default:L?_(L,u):null}}):[]}function E(){l(5,c=!c),f.querySelectorAll(".param").forEach(u=>{u instanceof HTMLDetailsElement&&(u.open=c)})}te(()=>{window.location.hash&&v(window.location.hash),window.addEventListener("hashchange",o=>{v(window.location.hash)})});function v(o){if(!f)return;const u=o.slice(1),y=f.querySelector(`#${u}`);y instanceof HTMLDetailsElement&&(y.open=!0,y.scrollIntoView({behavior:"smooth"}))}function k(o){se[o?"unshift":"push"](()=>{f=o,l(3,f)})}return s.$$set=o=>{"docs"in o&&l(7,t=o.docs),"lang"in o&&l(0,i=o.lang),"linkify"in o&&l(8,n=o.linkify),"header"in o&&l(1,a=o.header),"anchor_links"in o&&l(2,r=o.anchor_links)},s.$$.update=()=>{s.$$.dirty&129&&l(4,p=A(t,i))},[i,a,r,f,p,c,E,t,n,k]}class ge extends X{constructor(e){super(),x(this,e,me,_e,$,{docs:7,lang:0,linkify:8,header:1,anchor_links:2})}}function pe(s){let e,l;return e=new ge({props:{docs:s[0],linkify:s[1],header:s[2],anchor_links:s[3]}}),{c(){oe(e.$$.fragment)},l(t){fe(e.$$.fragment,t)},m(t,i){ce(e,t,i),l=!0},p(t,[i]){const n={};i&1&&(n.docs=t[0]),i&2&&(n.linkify=t[1]),i&4&&(n.header=t[2]),i&8&&(n.anchor_links=t[3]),e.$set(n)},i(t){l||(ue(e.$$.fragment,t),l=!0)},o(t){he(e.$$.fragment,t),l=!1},d(t){de(e,t)}}}function ve(s,e,l){let{value:t}=e,{linkify:i=[]}=e,{header:n=null}=e,{anchor_links:a=!1}=e;return s.$$set=r=>{"value"in r&&l(0,t=r.value),"linkify"in r&&l(1,i=r.linkify),"header"in r&&l(2,n=r.header),"anchor_links"in r&&l(3,a=r.anchor_links)},[t,i,n,a]}class we extends X{constructor(e){super(),x(this,e,ve,pe,$,{value:0,linkify:1,header:2,anchor_links:3})}}export{we as default};
//# sourceMappingURL=Index.CbpaiuHS.js.map
