{"version": 3, "file": "StaticAudio-CKEuBlEq.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/StaticAudio.js"], "sourcesContent": ["import{create_ssr_component as y,validate_component as l}from\"svelte/internal\";import\"./client.js\";import{u as B}from\"./utils.js\";import{B as b,M as h,I as p,a as I,D,S as k,E as A}from\"./FullscreenButton.js\";import{A as E}from\"./AudioPlayer.js\";import{createEventDispatcher as L}from\"svelte\";import{D as S}from\"./DownloadLink.js\";const q=y((a,o,e,M)=>{let{value:t=null}=o,{label:_}=o,{show_label:d=!0}=o,{show_download_button:i=!0}=o,{show_share_button:u=!1}=o,{i18n:r}=o,{waveform_settings:n={}}=o,{waveform_options:f={show_recording_waveform:!0}}=o,{editable:c=!0}=o,{loop:v}=o,{display_icon_button_wrapper_top_corner:w=!1}=o;const s=L();return o.value===void 0&&e.value&&t!==void 0&&e.value(t),o.label===void 0&&e.label&&_!==void 0&&e.label(_),o.show_label===void 0&&e.show_label&&d!==void 0&&e.show_label(d),o.show_download_button===void 0&&e.show_download_button&&i!==void 0&&e.show_download_button(i),o.show_share_button===void 0&&e.show_share_button&&u!==void 0&&e.show_share_button(u),o.i18n===void 0&&e.i18n&&r!==void 0&&e.i18n(r),o.waveform_settings===void 0&&e.waveform_settings&&n!==void 0&&e.waveform_settings(n),o.waveform_options===void 0&&e.waveform_options&&f!==void 0&&e.waveform_options(f),o.editable===void 0&&e.editable&&c!==void 0&&e.editable(c),o.loop===void 0&&e.loop&&v!==void 0&&e.loop(v),o.display_icon_button_wrapper_top_corner===void 0&&e.display_icon_button_wrapper_top_corner&&w!==void 0&&e.display_icon_button_wrapper_top_corner(w),t&&s(\"change\",t),`${l(b,\"BlockLabel\").$$render(a,{show_label:d,Icon:h,float:!1,label:_||r(\"audio.audio\")},{},{})} ${t!==null?`${l(p,\"IconButtonWrapper\").$$render(a,{display_top_corner:w},{},{default:()=>`${i?`${l(S,\"DownloadLink\").$$render(a,{href:t.is_stream?t.url?.replace(\"playlist.m3u8\",\"playlist-file\"):t.url,download:t.orig_name||t.path},{},{default:()=>`${l(I,\"IconButton\").$$render(a,{Icon:D,label:r(\"common.download\")},{},{})}`})}`:\"\"} ${u?`${l(k,\"ShareButton\").$$render(a,{i18n:r,formatter:async m=>m?`<audio controls src=\"${await B(m.url)}\"></audio>`:\"\",value:t},{},{})}`:\"\"}`})} ${l(E,\"AudioPlayer\").$$render(a,{value:t,label:_,i18n:r,waveform_settings:n,waveform_options:f,editable:c,loop:v},{},{})}`:`${l(A,\"Empty\").$$render(a,{size:\"small\"},{},{default:()=>`${l(h,\"Music\").$$render(a,{},{},{})}`})}`}`});export{q as default};\n//# sourceMappingURL=StaticAudio.js.map\n"], "names": ["y", "_", "L", "l", "b", "h", "p", "S", "I", "D", "k", "B", "E", "A"], "mappings": ";;;;;;;;;;AAAgV,MAAC,CAAC,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAACC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,sCAAsC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAACC,qBAAC,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAED,GAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,sCAAsC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,sCAAsC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,sCAAsC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEE,kBAAC,CAACC,EAAC,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAACC,EAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAACJ,GAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,EAAEE,kBAAC,CAACG,EAAC,CAAC,mBAAmB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEH,kBAAC,CAACI,CAAC,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAEJ,kBAAC,CAACK,CAAC,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAACC,EAAC,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEN,kBAAC,CAACO,EAAC,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,qBAAqB,EAAE,MAAMC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAER,kBAAC,CAACS,EAAC,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAACX,GAAC,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEE,kBAAC,CAACU,EAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAEV,kBAAC,CAACE,EAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;"}