{"version": 3, "file": "WebGPURenderer-C6mpXoA_.js", "sources": ["../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/batcher/gpu/GpuBatchAdaptor.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/BindGroupSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/buffer/GpuBufferSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/buffer/UboBatch.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/GpuColorMaskSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/GpuDeviceSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/GpuEncoderSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/GpuStencilSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/shader/utils/createUboElementsWGSL.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/shader/utils/generateArraySyncWGSL.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/shader/utils/createUboSyncFunctionWGSL.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/GpuUboSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/GpuUniformBatchPipe.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/pipeline/PipelineSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/renderTarget/GpuRenderTarget.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/renderTarget/GpuRenderTargetAdaptor.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/renderTarget/GpuRenderTargetSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/shader/GpuShaderSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/state/GpuBlendModesToPixi.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/state/GpuStateSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/texture/uploaders/gpuUploadBufferImageResource.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/texture/uploaders/gpuUploadCompressedTextureResource.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/texture/uploaders/gpuUploadImageSource.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/texture/uploaders/gpuUploadVideoSource.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/texture/utils/GpuMipmapGenerator.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/texture/GpuTextureSystem.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/graphics/gpu/GpuGraphicsAdaptor.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/scene/mesh/gpu/GpuMeshAdapter.mjs", "../../../../node_modules/.pnpm/pixi.js@8.7.3/node_modules/pixi.js/lib/rendering/renderers/gpu/WebGPURenderer.mjs"], "sourcesContent": ["import { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { State } from '../../renderers/shared/state/State.mjs';\nimport { getTextureBatchBindGroup } from './getTextureBatchBindGroup.mjs';\n\n\"use strict\";\nconst tempState = State.for2d();\nclass GpuBatchAdaptor {\n  start(batchPipe, geometry, shader) {\n    const renderer = batchPipe.renderer;\n    const encoder = renderer.encoder;\n    const program = shader.gpuProgram;\n    this._shader = shader;\n    this._geometry = geometry;\n    encoder.setGeometry(geometry, program);\n    tempState.blendMode = \"normal\";\n    renderer.pipeline.getPipeline(\n      geometry,\n      program,\n      tempState\n    );\n    const globalUniformsBindGroup = renderer.globalUniforms.bindGroup;\n    encoder.resetBindGroup(1);\n    encoder.setBindGroup(0, globalUniformsBindGroup, program);\n  }\n  execute(batchPipe, batch) {\n    const program = this._shader.gpuProgram;\n    const renderer = batchPipe.renderer;\n    const encoder = renderer.encoder;\n    if (!batch.bindGroup) {\n      const textureBatch = batch.textures;\n      batch.bindGroup = getTextureBatchBindGroup(textureBatch.textures, textureBatch.count);\n    }\n    tempState.blendMode = batch.blendMode;\n    const gpuBindGroup = renderer.bindGroup.getBindGroup(\n      batch.bindGroup,\n      program,\n      1\n    );\n    const pipeline = renderer.pipeline.getPipeline(\n      this._geometry,\n      program,\n      tempState,\n      batch.topology\n    );\n    batch.bindGroup._touch(renderer.textureGC.count);\n    encoder.setPipeline(pipeline);\n    encoder.renderPassEncoder.setBindGroup(1, gpuBindGroup);\n    encoder.renderPassEncoder.drawIndexed(batch.size, 1, batch.start);\n  }\n}\n/** @ignore */\nGpuBatchAdaptor.extension = {\n  type: [\n    ExtensionType.WebGPUPipesAdaptor\n  ],\n  name: \"batch\"\n};\n\nexport { GpuBatchAdaptor };\n//# sourceMappingURL=GpuBatchAdaptor.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\n\n\"use strict\";\nclass BindGroupSystem {\n  constructor(renderer) {\n    this._hash = /* @__PURE__ */ Object.create(null);\n    this._renderer = renderer;\n    this._renderer.renderableGC.addManagedHash(this, \"_hash\");\n  }\n  contextChange(gpu) {\n    this._gpu = gpu;\n  }\n  getBindGroup(bindGroup, program, groupIndex) {\n    bindGroup._updateKey();\n    const gpuBindGroup = this._hash[bindGroup._key] || this._createBindGroup(bindGroup, program, groupIndex);\n    return gpuBindGroup;\n  }\n  _createBindGroup(group, program, groupIndex) {\n    const device = this._gpu.device;\n    const groupLayout = program.layout[groupIndex];\n    const entries = [];\n    const renderer = this._renderer;\n    for (const j in groupLayout) {\n      const resource = group.resources[j] ?? group.resources[groupLayout[j]];\n      let gpuResource;\n      if (resource._resourceType === \"uniformGroup\") {\n        const uniformGroup = resource;\n        renderer.ubo.updateUniformGroup(uniformGroup);\n        const buffer = uniformGroup.buffer;\n        gpuResource = {\n          buffer: renderer.buffer.getGPUBuffer(buffer),\n          offset: 0,\n          size: buffer.descriptor.size\n        };\n      } else if (resource._resourceType === \"buffer\") {\n        const buffer = resource;\n        gpuResource = {\n          buffer: renderer.buffer.getGPUBuffer(buffer),\n          offset: 0,\n          size: buffer.descriptor.size\n        };\n      } else if (resource._resourceType === \"bufferResource\") {\n        const bufferResource = resource;\n        gpuResource = {\n          buffer: renderer.buffer.getGPUBuffer(bufferResource.buffer),\n          offset: bufferResource.offset,\n          size: bufferResource.size\n        };\n      } else if (resource._resourceType === \"textureSampler\") {\n        const sampler = resource;\n        gpuResource = renderer.texture.getGpuSampler(sampler);\n      } else if (resource._resourceType === \"textureSource\") {\n        const texture = resource;\n        gpuResource = renderer.texture.getGpuSource(texture).createView({});\n      }\n      entries.push({\n        binding: groupLayout[j],\n        resource: gpuResource\n      });\n    }\n    const layout = renderer.shader.getProgramData(program).bindGroups[groupIndex];\n    const gpuBindGroup = device.createBindGroup({\n      layout,\n      entries\n    });\n    this._hash[group._key] = gpuBindGroup;\n    return gpuBindGroup;\n  }\n  destroy() {\n    for (const key of Object.keys(this._hash)) {\n      this._hash[key] = null;\n    }\n    this._hash = null;\n    this._renderer = null;\n  }\n}\n/** @ignore */\nBindGroupSystem.extension = {\n  type: [\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"bindGroup\"\n};\n\nexport { BindGroupSystem };\n//# sourceMappingURL=BindGroupSystem.mjs.map\n", "import { ExtensionType } from '../../../../extensions/Extensions.mjs';\nimport { fastCopy } from '../../shared/buffer/utils/fastCopy.mjs';\n\n\"use strict\";\nclass GpuBufferSystem {\n  constructor(renderer) {\n    this._gpuBuffers = /* @__PURE__ */ Object.create(null);\n    this._managedBuffers = [];\n    renderer.renderableGC.addManagedHash(this, \"_gpuBuffers\");\n  }\n  contextChange(gpu) {\n    this._gpu = gpu;\n  }\n  getGPUBuffer(buffer) {\n    return this._gpuBuffers[buffer.uid] || this.createGPUBuffer(buffer);\n  }\n  updateBuffer(buffer) {\n    const gpuBuffer = this._gpuBuffers[buffer.uid] || this.createGPUBuffer(buffer);\n    const data = buffer.data;\n    if (buffer._updateID && data) {\n      buffer._updateID = 0;\n      this._gpu.device.queue.writeBuffer(\n        gpuBuffer,\n        0,\n        data.buffer,\n        0,\n        // round to the nearest 4 bytes\n        (buffer._updateSize || data.byteLength) + 3 & ~3\n      );\n    }\n    return gpuBuffer;\n  }\n  /** dispose all WebGL resources of all managed buffers */\n  destroyAll() {\n    for (const id in this._gpuBuffers) {\n      this._gpuBuffers[id].destroy();\n    }\n    this._gpuBuffers = {};\n  }\n  createGPUBuffer(buffer) {\n    if (!this._gpuBuffers[buffer.uid]) {\n      buffer.on(\"update\", this.updateBuffer, this);\n      buffer.on(\"change\", this.onBufferChange, this);\n      buffer.on(\"destroy\", this.onBufferDestroy, this);\n      this._managedBuffers.push(buffer);\n    }\n    const gpuBuffer = this._gpu.device.createBuffer(buffer.descriptor);\n    buffer._updateID = 0;\n    if (buffer.data) {\n      fastCopy(buffer.data.buffer, gpuBuffer.getMappedRange());\n      gpuBuffer.unmap();\n    }\n    this._gpuBuffers[buffer.uid] = gpuBuffer;\n    return gpuBuffer;\n  }\n  onBufferChange(buffer) {\n    const gpuBuffer = this._gpuBuffers[buffer.uid];\n    gpuBuffer.destroy();\n    buffer._updateID = 0;\n    this._gpuBuffers[buffer.uid] = this.createGPUBuffer(buffer);\n  }\n  /**\n   * Disposes buffer\n   * @param buffer - buffer with data\n   */\n  onBufferDestroy(buffer) {\n    this._managedBuffers.splice(this._managedBuffers.indexOf(buffer), 1);\n    this._destroyBuffer(buffer);\n  }\n  destroy() {\n    this._managedBuffers.forEach((buffer) => this._destroyBuffer(buffer));\n    this._managedBuffers = null;\n    this._gpuBuffers = null;\n  }\n  _destroyBuffer(buffer) {\n    const gpuBuffer = this._gpuBuffers[buffer.uid];\n    gpuBuffer.destroy();\n    buffer.off(\"update\", this.updateBuffer, this);\n    buffer.off(\"change\", this.onBufferChange, this);\n    buffer.off(\"destroy\", this.onBufferDestroy, this);\n    this._gpuBuffers[buffer.uid] = null;\n  }\n}\n/** @ignore */\nGpuBufferSystem.extension = {\n  type: [\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"buffer\"\n};\n\nexport { GpuBufferSystem };\n//# sourceMappingURL=GpuBufferSystem.mjs.map\n", "\"use strict\";\nclass UboBatch {\n  constructor({ minUniformOffsetAlignment }) {\n    this._minUniformOffsetAlignment = 256;\n    this.byteIndex = 0;\n    this._minUniformOffsetAlignment = minUniformOffsetAlignment;\n    this.data = new Float32Array(65535);\n  }\n  clear() {\n    this.byteIndex = 0;\n  }\n  addEmptyGroup(size) {\n    if (size > this._minUniformOffsetAlignment / 4) {\n      throw new Error(`UniformBufferBatch: array is too large: ${size * 4}`);\n    }\n    const start = this.byteIndex;\n    let newSize = start + size * 4;\n    newSize = Math.ceil(newSize / this._minUniformOffsetAlignment) * this._minUniformOffsetAlignment;\n    if (newSize > this.data.length * 4) {\n      throw new Error(\"UniformBufferBatch: ubo batch got too big\");\n    }\n    this.byteIndex = newSize;\n    return start;\n  }\n  addGroup(array) {\n    const offset = this.addEmptyGroup(array.length);\n    for (let i = 0; i < array.length; i++) {\n      this.data[offset / 4 + i] = array[i];\n    }\n    return offset;\n  }\n  destroy() {\n    this.data = null;\n  }\n}\n\nexport { UboBatch };\n//# sourceMappingURL=UboBatch.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\n\n\"use strict\";\nclass GpuColorMaskSystem {\n  constructor(renderer) {\n    this._colorMaskCache = 15;\n    this._renderer = renderer;\n  }\n  setMask(colorMask) {\n    if (this._colorMaskCache === colorMask)\n      return;\n    this._colorMaskCache = colorMask;\n    this._renderer.pipeline.setColorMask(colorMask);\n  }\n  destroy() {\n    this._renderer = null;\n    this._colorMaskCache = null;\n  }\n}\n/** @ignore */\nGpuColorMaskSystem.extension = {\n  type: [\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"colorMask\"\n};\n\nexport { GpuColorMaskSystem };\n//# sourceMappingURL=GpuColorMaskSystem.mjs.map\n", "import { DOMAdapter } from '../../../environment/adapter.mjs';\nimport { ExtensionType } from '../../../extensions/Extensions.mjs';\n\n\"use strict\";\nclass GpuDeviceSystem {\n  /**\n   * @param {WebGPURenderer} renderer - The renderer this System works for.\n   */\n  constructor(renderer) {\n    this._renderer = renderer;\n  }\n  async init(options) {\n    if (this._initPromise)\n      return this._initPromise;\n    this._initPromise = this._createDeviceAndAdaptor(options).then((gpu) => {\n      this.gpu = gpu;\n      this._renderer.runners.contextChange.emit(this.gpu);\n    });\n    return this._initPromise;\n  }\n  /**\n   * Handle the context change event\n   * @param gpu\n   */\n  contextChange(gpu) {\n    this._renderer.gpu = gpu;\n  }\n  /**\n   * Helper class to create a WebGL Context\n   * @param {object} options - An options object that gets passed in to the canvas element containing the\n   *    context attributes\n   * @see https://developer.mozilla.org/en/docs/Web/API/HTMLCanvasElement/getContext\n   * @returns {WebGLRenderingContext} the WebGL context\n   */\n  async _createDeviceAndAdaptor(options) {\n    const adapter = await DOMAdapter.get().getNavigator().gpu.requestAdapter({\n      powerPreference: options.powerPreference,\n      forceFallbackAdapter: options.forceFallbackAdapter\n    });\n    const requiredFeatures = [\n      \"texture-compression-bc\",\n      \"texture-compression-astc\",\n      \"texture-compression-etc2\"\n    ].filter((feature) => adapter.features.has(feature));\n    const device = await adapter.requestDevice({\n      requiredFeatures\n    });\n    return { adapter, device };\n  }\n  destroy() {\n    this.gpu = null;\n    this._renderer = null;\n  }\n}\n/** @ignore */\nGpuDeviceSystem.extension = {\n  type: [\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"device\"\n};\n/** The default options for the GpuDeviceSystem. */\nGpuDeviceSystem.defaultOptions = {\n  /**\n   * {@link WebGPUOptions.powerPreference}\n   * @default default\n   */\n  powerPreference: void 0,\n  /**\n   * Force the use of the fallback adapter\n   * @default false\n   */\n  forceFallbackAdapter: false\n};\n\nexport { GpuDeviceSystem };\n//# sourceMappingURL=GpuDeviceSystem.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\n\n\"use strict\";\nclass GpuEncoderSystem {\n  constructor(renderer) {\n    this._boundBindGroup = /* @__PURE__ */ Object.create(null);\n    this._boundVertexBuffer = /* @__PURE__ */ Object.create(null);\n    this._renderer = renderer;\n  }\n  renderStart() {\n    this.commandFinished = new Promise((resolve) => {\n      this._resolveCommandFinished = resolve;\n    });\n    this.commandEncoder = this._renderer.gpu.device.createCommandEncoder();\n  }\n  beginRenderPass(gpuRenderTarget) {\n    this.endRenderPass();\n    this._clearCache();\n    this.renderPassEncoder = this.commandEncoder.beginRenderPass(gpuRenderTarget.descriptor);\n  }\n  endRenderPass() {\n    if (this.renderPassEncoder) {\n      this.renderPassEncoder.end();\n    }\n    this.renderPassEncoder = null;\n  }\n  setViewport(viewport) {\n    this.renderPassEncoder.setViewport(viewport.x, viewport.y, viewport.width, viewport.height, 0, 1);\n  }\n  setPipelineFromGeometryProgramAndState(geometry, program, state, topology) {\n    const pipeline = this._renderer.pipeline.getPipeline(geometry, program, state, topology);\n    this.setPipeline(pipeline);\n  }\n  setPipeline(pipeline) {\n    if (this._boundPipeline === pipeline)\n      return;\n    this._boundPipeline = pipeline;\n    this.renderPassEncoder.setPipeline(pipeline);\n  }\n  _setVertexBuffer(index, buffer) {\n    if (this._boundVertexBuffer[index] === buffer)\n      return;\n    this._boundVertexBuffer[index] = buffer;\n    this.renderPassEncoder.setVertexBuffer(index, this._renderer.buffer.updateBuffer(buffer));\n  }\n  _setIndexBuffer(buffer) {\n    if (this._boundIndexBuffer === buffer)\n      return;\n    this._boundIndexBuffer = buffer;\n    const indexFormat = buffer.data.BYTES_PER_ELEMENT === 2 ? \"uint16\" : \"uint32\";\n    this.renderPassEncoder.setIndexBuffer(this._renderer.buffer.updateBuffer(buffer), indexFormat);\n  }\n  resetBindGroup(index) {\n    this._boundBindGroup[index] = null;\n  }\n  setBindGroup(index, bindGroup, program) {\n    if (this._boundBindGroup[index] === bindGroup)\n      return;\n    this._boundBindGroup[index] = bindGroup;\n    bindGroup._touch(this._renderer.textureGC.count);\n    const gpuBindGroup = this._renderer.bindGroup.getBindGroup(bindGroup, program, index);\n    this.renderPassEncoder.setBindGroup(index, gpuBindGroup);\n  }\n  setGeometry(geometry, program) {\n    const buffersToBind = this._renderer.pipeline.getBufferNamesToBind(geometry, program);\n    for (const i in buffersToBind) {\n      this._setVertexBuffer(i, geometry.attributes[buffersToBind[i]].buffer);\n    }\n    if (geometry.indexBuffer) {\n      this._setIndexBuffer(geometry.indexBuffer);\n    }\n  }\n  _setShaderBindGroups(shader, skipSync) {\n    for (const i in shader.groups) {\n      const bindGroup = shader.groups[i];\n      if (!skipSync) {\n        this._syncBindGroup(bindGroup);\n      }\n      this.setBindGroup(i, bindGroup, shader.gpuProgram);\n    }\n  }\n  _syncBindGroup(bindGroup) {\n    for (const j in bindGroup.resources) {\n      const resource = bindGroup.resources[j];\n      if (resource.isUniformGroup) {\n        this._renderer.ubo.updateUniformGroup(resource);\n      }\n    }\n  }\n  draw(options) {\n    const { geometry, shader, state, topology, size, start, instanceCount, skipSync } = options;\n    this.setPipelineFromGeometryProgramAndState(geometry, shader.gpuProgram, state, topology);\n    this.setGeometry(geometry, shader.gpuProgram);\n    this._setShaderBindGroups(shader, skipSync);\n    if (geometry.indexBuffer) {\n      this.renderPassEncoder.drawIndexed(\n        size || geometry.indexBuffer.data.length,\n        instanceCount ?? geometry.instanceCount,\n        start || 0\n      );\n    } else {\n      this.renderPassEncoder.draw(size || geometry.getSize(), instanceCount ?? geometry.instanceCount, start || 0);\n    }\n  }\n  finishRenderPass() {\n    if (this.renderPassEncoder) {\n      this.renderPassEncoder.end();\n      this.renderPassEncoder = null;\n    }\n  }\n  postrender() {\n    this.finishRenderPass();\n    this._gpu.device.queue.submit([this.commandEncoder.finish()]);\n    this._resolveCommandFinished();\n    this.commandEncoder = null;\n  }\n  // restores a render pass if finishRenderPass was called\n  // not optimised as really used for debugging!\n  // used when we want to stop drawing and log a texture..\n  restoreRenderPass() {\n    const descriptor = this._renderer.renderTarget.adaptor.getDescriptor(\n      this._renderer.renderTarget.renderTarget,\n      false,\n      [0, 0, 0, 1]\n    );\n    this.renderPassEncoder = this.commandEncoder.beginRenderPass(descriptor);\n    const boundPipeline = this._boundPipeline;\n    const boundVertexBuffer = { ...this._boundVertexBuffer };\n    const boundIndexBuffer = this._boundIndexBuffer;\n    const boundBindGroup = { ...this._boundBindGroup };\n    this._clearCache();\n    const viewport = this._renderer.renderTarget.viewport;\n    this.renderPassEncoder.setViewport(viewport.x, viewport.y, viewport.width, viewport.height, 0, 1);\n    this.setPipeline(boundPipeline);\n    for (const i in boundVertexBuffer) {\n      this._setVertexBuffer(i, boundVertexBuffer[i]);\n    }\n    for (const i in boundBindGroup) {\n      this.setBindGroup(i, boundBindGroup[i], null);\n    }\n    this._setIndexBuffer(boundIndexBuffer);\n  }\n  _clearCache() {\n    for (let i = 0; i < 16; i++) {\n      this._boundBindGroup[i] = null;\n      this._boundVertexBuffer[i] = null;\n    }\n    this._boundIndexBuffer = null;\n    this._boundPipeline = null;\n  }\n  destroy() {\n    this._renderer = null;\n    this._gpu = null;\n    this._boundBindGroup = null;\n    this._boundVertexBuffer = null;\n    this._boundIndexBuffer = null;\n    this._boundPipeline = null;\n  }\n  contextChange(gpu) {\n    this._gpu = gpu;\n  }\n}\n/** @ignore */\nGpuEncoderSystem.extension = {\n  type: [ExtensionType.WebGPUSystem],\n  name: \"encoder\",\n  priority: 1\n};\n\nexport { GpuEncoderSystem };\n//# sourceMappingURL=GpuEncoderSystem.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { STENCIL_MODES } from '../shared/state/const.mjs';\n\n\"use strict\";\nclass GpuStencilSystem {\n  constructor(renderer) {\n    this._renderTargetStencilState = /* @__PURE__ */ Object.create(null);\n    this._renderer = renderer;\n    renderer.renderTarget.onRenderTargetChange.add(this);\n  }\n  onRenderTargetChange(renderTarget) {\n    let stencilState = this._renderTargetStencilState[renderTarget.uid];\n    if (!stencilState) {\n      stencilState = this._renderTargetStencilState[renderTarget.uid] = {\n        stencilMode: STENCIL_MODES.DISABLED,\n        stencilReference: 0\n      };\n    }\n    this._activeRenderTarget = renderTarget;\n    this.setStencilMode(stencilState.stencilMode, stencilState.stencilReference);\n  }\n  setStencilMode(stencilMode, stencilReference) {\n    const stencilState = this._renderTargetStencilState[this._activeRenderTarget.uid];\n    stencilState.stencilMode = stencilMode;\n    stencilState.stencilReference = stencilReference;\n    const renderer = this._renderer;\n    renderer.pipeline.setStencilMode(stencilMode);\n    renderer.encoder.renderPassEncoder.setStencilReference(stencilReference);\n  }\n  destroy() {\n    this._renderer.renderTarget.onRenderTargetChange.remove(this);\n    this._renderer = null;\n    this._activeRenderTarget = null;\n    this._renderTargetStencilState = null;\n  }\n}\n/** @ignore */\nGpuStencilSystem.extension = {\n  type: [\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"stencil\"\n};\n\nexport { GpuStencilSystem };\n//# sourceMappingURL=GpuStencilSystem.mjs.map\n", "\"use strict\";\nconst WGSL_ALIGN_SIZE_DATA = {\n  i32: { align: 4, size: 4 },\n  u32: { align: 4, size: 4 },\n  f32: { align: 4, size: 4 },\n  f16: { align: 2, size: 2 },\n  \"vec2<i32>\": { align: 8, size: 8 },\n  \"vec2<u32>\": { align: 8, size: 8 },\n  \"vec2<f32>\": { align: 8, size: 8 },\n  \"vec2<f16>\": { align: 4, size: 4 },\n  \"vec3<i32>\": { align: 16, size: 12 },\n  \"vec3<u32>\": { align: 16, size: 12 },\n  \"vec3<f32>\": { align: 16, size: 12 },\n  \"vec3<f16>\": { align: 8, size: 6 },\n  \"vec4<i32>\": { align: 16, size: 16 },\n  \"vec4<u32>\": { align: 16, size: 16 },\n  \"vec4<f32>\": { align: 16, size: 16 },\n  \"vec4<f16>\": { align: 8, size: 8 },\n  \"mat2x2<f32>\": { align: 8, size: 16 },\n  \"mat2x2<f16>\": { align: 4, size: 8 },\n  \"mat3x2<f32>\": { align: 8, size: 24 },\n  \"mat3x2<f16>\": { align: 4, size: 12 },\n  \"mat4x2<f32>\": { align: 8, size: 32 },\n  \"mat4x2<f16>\": { align: 4, size: 16 },\n  \"mat2x3<f32>\": { align: 16, size: 32 },\n  \"mat2x3<f16>\": { align: 8, size: 16 },\n  \"mat3x3<f32>\": { align: 16, size: 48 },\n  \"mat3x3<f16>\": { align: 8, size: 24 },\n  \"mat4x3<f32>\": { align: 16, size: 64 },\n  \"mat4x3<f16>\": { align: 8, size: 32 },\n  \"mat2x4<f32>\": { align: 16, size: 32 },\n  \"mat2x4<f16>\": { align: 8, size: 16 },\n  \"mat3x4<f32>\": { align: 16, size: 48 },\n  \"mat3x4<f16>\": { align: 8, size: 24 },\n  \"mat4x4<f32>\": { align: 16, size: 64 },\n  \"mat4x4<f16>\": { align: 8, size: 32 }\n};\nfunction createUboElementsWGSL(uniformData) {\n  const uboElements = uniformData.map((data) => ({\n    data,\n    offset: 0,\n    size: 0\n  }));\n  let offset = 0;\n  for (let i = 0; i < uboElements.length; i++) {\n    const uboElement = uboElements[i];\n    let size = WGSL_ALIGN_SIZE_DATA[uboElement.data.type].size;\n    const align = WGSL_ALIGN_SIZE_DATA[uboElement.data.type].align;\n    if (!WGSL_ALIGN_SIZE_DATA[uboElement.data.type]) {\n      throw new Error(`[Pixi.js] WebGPU UniformBuffer: Unknown type ${uboElement.data.type}`);\n    }\n    if (uboElement.data.size > 1) {\n      size = Math.max(size, align) * uboElement.data.size;\n    }\n    offset = Math.ceil(offset / align) * align;\n    uboElement.size = size;\n    uboElement.offset = offset;\n    offset += size;\n  }\n  offset = Math.ceil(offset / 16) * 16;\n  return { uboElements, size: offset };\n}\n\nexport { WGSL_ALIGN_SIZE_DATA, createUboElementsWGSL };\n//# sourceMappingURL=createUboElementsWGSL.mjs.map\n", "import { WGSL_ALIGN_SIZE_DATA } from './createUboElementsWGSL.mjs';\n\n\"use strict\";\nfunction generateArraySyncWGSL(uboElement, offsetToAdd) {\n  const { size, align } = WGSL_ALIGN_SIZE_DATA[uboElement.data.type];\n  const remainder = (align - size) / 4;\n  const data = uboElement.data.type.indexOf(\"i32\") >= 0 ? \"dataInt32\" : \"data\";\n  return `\n         v = uv.${uboElement.data.name};\n         ${offsetToAdd !== 0 ? `offset += ${offsetToAdd};` : \"\"}\n\n         arrayOffset = offset;\n\n         t = 0;\n\n         for(var i=0; i < ${uboElement.data.size * (size / 4)}; i++)\n         {\n             for(var j = 0; j < ${size / 4}; j++)\n             {\n                 ${data}[arrayOffset++] = v[t++];\n             }\n             ${remainder !== 0 ? `arrayOffset += ${remainder};` : \"\"}\n         }\n     `;\n}\n\nexport { generateArraySyncWGSL };\n//# sourceMappingURL=generateArraySyncWGSL.mjs.map\n", "import { createUboSyncFunction } from '../../../shared/shader/utils/createUboSyncFunction.mjs';\nimport { uboSyncFunctionsWGSL } from '../../../shared/shader/utils/uboSyncFunctions.mjs';\nimport { generateArraySyncWGSL } from './generateArraySyncWGSL.mjs';\n\n\"use strict\";\nfunction createUboSyncFunctionWGSL(uboElements) {\n  return createUboSyncFunction(\n    uboElements,\n    \"uboWgsl\",\n    generateArraySyncWGSL,\n    uboSyncFunctionsWGSL\n  );\n}\n\nexport { createUboSyncFunctionWGSL };\n//# sourceMappingURL=createUboSyncFunctionWGSL.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { UboSystem } from '../shared/shader/UboSystem.mjs';\nimport { createUboElementsWGSL } from './shader/utils/createUboElementsWGSL.mjs';\nimport { createUboSyncFunctionWGSL } from './shader/utils/createUboSyncFunctionWGSL.mjs';\n\n\"use strict\";\nclass GpuUboSystem extends UboSystem {\n  constructor() {\n    super({\n      createUboElements: createUboElementsWGSL,\n      generateUboSync: createUboSyncFunctionWGSL\n    });\n  }\n}\n/** @ignore */\nGpuUboSystem.extension = {\n  type: [ExtensionType.WebGPUSystem],\n  name: \"ubo\"\n};\n\nexport { GpuUboSystem };\n//# sourceMappingURL=GpuUboSystem.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { Buffer } from '../shared/buffer/Buffer.mjs';\nimport { BufferResource } from '../shared/buffer/BufferResource.mjs';\nimport { BufferUsage } from '../shared/buffer/const.mjs';\nimport { UboBatch } from './buffer/UboBatch.mjs';\nimport { BindGroup } from './shader/BindGroup.mjs';\n\n\"use strict\";\nconst minUniformOffsetAlignment = 128;\nclass GpuUniformBatchPipe {\n  constructor(renderer) {\n    this._bindGroupHash = /* @__PURE__ */ Object.create(null);\n    // number of buffers..\n    this._buffers = [];\n    this._bindGroups = [];\n    this._bufferResources = [];\n    this._renderer = renderer;\n    this._renderer.renderableGC.addManagedHash(this, \"_bindGroupHash\");\n    this._batchBuffer = new UboBatch({ minUniformOffsetAlignment });\n    const totalBuffers = 256 / minUniformOffsetAlignment;\n    for (let i = 0; i < totalBuffers; i++) {\n      let usage = BufferUsage.UNIFORM | BufferUsage.COPY_DST;\n      if (i === 0)\n        usage |= BufferUsage.COPY_SRC;\n      this._buffers.push(new Buffer({\n        data: this._batchBuffer.data,\n        usage\n      }));\n    }\n  }\n  renderEnd() {\n    this._uploadBindGroups();\n    this._resetBindGroups();\n  }\n  _resetBindGroups() {\n    for (const i in this._bindGroupHash) {\n      this._bindGroupHash[i] = null;\n    }\n    this._batchBuffer.clear();\n  }\n  // just works for single bind groups for now\n  getUniformBindGroup(group, duplicate) {\n    if (!duplicate && this._bindGroupHash[group.uid]) {\n      return this._bindGroupHash[group.uid];\n    }\n    this._renderer.ubo.ensureUniformGroup(group);\n    const data = group.buffer.data;\n    const offset = this._batchBuffer.addEmptyGroup(data.length);\n    this._renderer.ubo.syncUniformGroup(group, this._batchBuffer.data, offset / 4);\n    this._bindGroupHash[group.uid] = this._getBindGroup(offset / minUniformOffsetAlignment);\n    return this._bindGroupHash[group.uid];\n  }\n  getUboResource(group) {\n    this._renderer.ubo.updateUniformGroup(group);\n    const data = group.buffer.data;\n    const offset = this._batchBuffer.addGroup(data);\n    return this._getBufferResource(offset / minUniformOffsetAlignment);\n  }\n  getArrayBindGroup(data) {\n    const offset = this._batchBuffer.addGroup(data);\n    return this._getBindGroup(offset / minUniformOffsetAlignment);\n  }\n  getArrayBufferResource(data) {\n    const offset = this._batchBuffer.addGroup(data);\n    const index = offset / minUniformOffsetAlignment;\n    return this._getBufferResource(index);\n  }\n  _getBufferResource(index) {\n    if (!this._bufferResources[index]) {\n      const buffer = this._buffers[index % 2];\n      this._bufferResources[index] = new BufferResource({\n        buffer,\n        offset: (index / 2 | 0) * 256,\n        size: minUniformOffsetAlignment\n      });\n    }\n    return this._bufferResources[index];\n  }\n  _getBindGroup(index) {\n    if (!this._bindGroups[index]) {\n      const bindGroup = new BindGroup({\n        0: this._getBufferResource(index)\n      });\n      this._bindGroups[index] = bindGroup;\n    }\n    return this._bindGroups[index];\n  }\n  _uploadBindGroups() {\n    const bufferSystem = this._renderer.buffer;\n    const firstBuffer = this._buffers[0];\n    firstBuffer.update(this._batchBuffer.byteIndex);\n    bufferSystem.updateBuffer(firstBuffer);\n    const commandEncoder = this._renderer.gpu.device.createCommandEncoder();\n    for (let i = 1; i < this._buffers.length; i++) {\n      const buffer = this._buffers[i];\n      commandEncoder.copyBufferToBuffer(\n        bufferSystem.getGPUBuffer(firstBuffer),\n        minUniformOffsetAlignment,\n        bufferSystem.getGPUBuffer(buffer),\n        0,\n        this._batchBuffer.byteIndex\n      );\n    }\n    this._renderer.gpu.device.queue.submit([commandEncoder.finish()]);\n  }\n  destroy() {\n    for (let i = 0; i < this._bindGroups.length; i++) {\n      this._bindGroups[i].destroy();\n    }\n    this._bindGroups = null;\n    this._bindGroupHash = null;\n    for (let i = 0; i < this._buffers.length; i++) {\n      this._buffers[i].destroy();\n    }\n    this._buffers = null;\n    for (let i = 0; i < this._bufferResources.length; i++) {\n      this._bufferResources[i].destroy();\n    }\n    this._bufferResources = null;\n    this._batchBuffer.destroy();\n    this._bindGroupHash = null;\n    this._renderer = null;\n  }\n}\n/** @ignore */\nGpuUniformBatchPipe.extension = {\n  type: [\n    ExtensionType.WebGPUPipes\n  ],\n  name: \"uniformBatch\"\n};\n\nexport { GpuUniformBatchPipe };\n//# sourceMappingURL=GpuUniformBatchPipe.mjs.map\n", "import { ExtensionType } from '../../../../extensions/Extensions.mjs';\nimport { warn } from '../../../../utils/logging/warn.mjs';\nimport { ensureAttributes } from '../../gl/shader/program/ensureAttributes.mjs';\nimport { STENCIL_MODES } from '../../shared/state/const.mjs';\nimport { createIdFromString } from '../../shared/utils/createIdFromString.mjs';\nimport { GpuStencilModesToPixi } from '../state/GpuStencilModesToPixi.mjs';\n\n\"use strict\";\nconst topologyStringToId = {\n  \"point-list\": 0,\n  \"line-list\": 1,\n  \"line-strip\": 2,\n  \"triangle-list\": 3,\n  \"triangle-strip\": 4\n};\nfunction getGraphicsStateKey(geometryLayout, shaderKey, state, blendMode, topology) {\n  return geometryLayout << 24 | shaderKey << 16 | state << 10 | blendMode << 5 | topology;\n}\nfunction getGlobalStateKey(stencilStateId, multiSampleCount, colorMask, renderTarget) {\n  return colorMask << 6 | stencilStateId << 3 | renderTarget << 1 | multiSampleCount;\n}\nclass PipelineSystem {\n  constructor(renderer) {\n    this._moduleCache = /* @__PURE__ */ Object.create(null);\n    this._bufferLayoutsCache = /* @__PURE__ */ Object.create(null);\n    this._bindingNamesCache = /* @__PURE__ */ Object.create(null);\n    this._pipeCache = /* @__PURE__ */ Object.create(null);\n    this._pipeStateCaches = /* @__PURE__ */ Object.create(null);\n    this._colorMask = 15;\n    this._multisampleCount = 1;\n    this._renderer = renderer;\n  }\n  contextChange(gpu) {\n    this._gpu = gpu;\n    this.setStencilMode(STENCIL_MODES.DISABLED);\n    this._updatePipeHash();\n  }\n  setMultisampleCount(multisampleCount) {\n    if (this._multisampleCount === multisampleCount)\n      return;\n    this._multisampleCount = multisampleCount;\n    this._updatePipeHash();\n  }\n  setRenderTarget(renderTarget) {\n    this._multisampleCount = renderTarget.msaaSamples;\n    this._depthStencilAttachment = renderTarget.descriptor.depthStencilAttachment ? 1 : 0;\n    this._updatePipeHash();\n  }\n  setColorMask(colorMask) {\n    if (this._colorMask === colorMask)\n      return;\n    this._colorMask = colorMask;\n    this._updatePipeHash();\n  }\n  setStencilMode(stencilMode) {\n    if (this._stencilMode === stencilMode)\n      return;\n    this._stencilMode = stencilMode;\n    this._stencilState = GpuStencilModesToPixi[stencilMode];\n    this._updatePipeHash();\n  }\n  setPipeline(geometry, program, state, passEncoder) {\n    const pipeline = this.getPipeline(geometry, program, state);\n    passEncoder.setPipeline(pipeline);\n  }\n  getPipeline(geometry, program, state, topology) {\n    if (!geometry._layoutKey) {\n      ensureAttributes(geometry, program.attributeData);\n      this._generateBufferKey(geometry);\n    }\n    topology || (topology = geometry.topology);\n    const key = getGraphicsStateKey(\n      geometry._layoutKey,\n      program._layoutKey,\n      state.data,\n      state._blendModeId,\n      topologyStringToId[topology]\n    );\n    if (this._pipeCache[key])\n      return this._pipeCache[key];\n    this._pipeCache[key] = this._createPipeline(geometry, program, state, topology);\n    return this._pipeCache[key];\n  }\n  _createPipeline(geometry, program, state, topology) {\n    const device = this._gpu.device;\n    const buffers = this._createVertexBufferLayouts(geometry, program);\n    const blendModes = this._renderer.state.getColorTargets(state);\n    blendModes[0].writeMask = this._stencilMode === STENCIL_MODES.RENDERING_MASK_ADD ? 0 : this._colorMask;\n    const layout = this._renderer.shader.getProgramData(program).pipeline;\n    const descriptor = {\n      // TODO later check if its helpful to create..\n      // layout,\n      vertex: {\n        module: this._getModule(program.vertex.source),\n        entryPoint: program.vertex.entryPoint,\n        // geometry..\n        buffers\n      },\n      fragment: {\n        module: this._getModule(program.fragment.source),\n        entryPoint: program.fragment.entryPoint,\n        targets: blendModes\n      },\n      primitive: {\n        topology,\n        cullMode: state.cullMode\n      },\n      layout,\n      multisample: {\n        count: this._multisampleCount\n      },\n      // depthStencil,\n      label: `PIXI Pipeline`\n    };\n    if (this._depthStencilAttachment) {\n      descriptor.depthStencil = {\n        ...this._stencilState,\n        format: \"depth24plus-stencil8\",\n        depthWriteEnabled: state.depthTest,\n        depthCompare: state.depthTest ? \"less\" : \"always\"\n      };\n    }\n    const pipeline = device.createRenderPipeline(descriptor);\n    return pipeline;\n  }\n  _getModule(code) {\n    return this._moduleCache[code] || this._createModule(code);\n  }\n  _createModule(code) {\n    const device = this._gpu.device;\n    this._moduleCache[code] = device.createShaderModule({\n      code\n    });\n    return this._moduleCache[code];\n  }\n  _generateBufferKey(geometry) {\n    const keyGen = [];\n    let index = 0;\n    const attributeKeys = Object.keys(geometry.attributes).sort();\n    for (let i = 0; i < attributeKeys.length; i++) {\n      const attribute = geometry.attributes[attributeKeys[i]];\n      keyGen[index++] = attribute.offset;\n      keyGen[index++] = attribute.format;\n      keyGen[index++] = attribute.stride;\n      keyGen[index++] = attribute.instance;\n    }\n    const stringKey = keyGen.join(\"|\");\n    geometry._layoutKey = createIdFromString(stringKey, \"geometry\");\n    return geometry._layoutKey;\n  }\n  _generateAttributeLocationsKey(program) {\n    const keyGen = [];\n    let index = 0;\n    const attributeKeys = Object.keys(program.attributeData).sort();\n    for (let i = 0; i < attributeKeys.length; i++) {\n      const attribute = program.attributeData[attributeKeys[i]];\n      keyGen[index++] = attribute.location;\n    }\n    const stringKey = keyGen.join(\"|\");\n    program._attributeLocationsKey = createIdFromString(stringKey, \"programAttributes\");\n    return program._attributeLocationsKey;\n  }\n  /**\n   * Returns a hash of buffer names mapped to bind locations.\n   * This is used to bind the correct buffer to the correct location in the shader.\n   * @param geometry - The geometry where to get the buffer names\n   * @param program - The program where to get the buffer names\n   * @returns An object of buffer names mapped to the bind location.\n   */\n  getBufferNamesToBind(geometry, program) {\n    const key = geometry._layoutKey << 16 | program._attributeLocationsKey;\n    if (this._bindingNamesCache[key])\n      return this._bindingNamesCache[key];\n    const data = this._createVertexBufferLayouts(geometry, program);\n    const bufferNamesToBind = /* @__PURE__ */ Object.create(null);\n    const attributeData = program.attributeData;\n    for (let i = 0; i < data.length; i++) {\n      const attributes = Object.values(data[i].attributes);\n      const shaderLocation = attributes[0].shaderLocation;\n      for (const j in attributeData) {\n        if (attributeData[j].location === shaderLocation) {\n          bufferNamesToBind[i] = j;\n          break;\n        }\n      }\n    }\n    this._bindingNamesCache[key] = bufferNamesToBind;\n    return bufferNamesToBind;\n  }\n  _createVertexBufferLayouts(geometry, program) {\n    if (!program._attributeLocationsKey)\n      this._generateAttributeLocationsKey(program);\n    const key = geometry._layoutKey << 16 | program._attributeLocationsKey;\n    if (this._bufferLayoutsCache[key]) {\n      return this._bufferLayoutsCache[key];\n    }\n    const vertexBuffersLayout = [];\n    geometry.buffers.forEach((buffer) => {\n      const bufferEntry = {\n        arrayStride: 0,\n        stepMode: \"vertex\",\n        attributes: []\n      };\n      const bufferEntryAttributes = bufferEntry.attributes;\n      for (const i in program.attributeData) {\n        const attribute = geometry.attributes[i];\n        if ((attribute.divisor ?? 1) !== 1) {\n          warn(`Attribute ${i} has an invalid divisor value of '${attribute.divisor}'. WebGPU only supports a divisor value of 1`);\n        }\n        if (attribute.buffer === buffer) {\n          bufferEntry.arrayStride = attribute.stride;\n          bufferEntry.stepMode = attribute.instance ? \"instance\" : \"vertex\";\n          bufferEntryAttributes.push({\n            shaderLocation: program.attributeData[i].location,\n            offset: attribute.offset,\n            format: attribute.format\n          });\n        }\n      }\n      if (bufferEntryAttributes.length) {\n        vertexBuffersLayout.push(bufferEntry);\n      }\n    });\n    this._bufferLayoutsCache[key] = vertexBuffersLayout;\n    return vertexBuffersLayout;\n  }\n  _updatePipeHash() {\n    const key = getGlobalStateKey(\n      this._stencilMode,\n      this._multisampleCount,\n      this._colorMask,\n      this._depthStencilAttachment\n    );\n    if (!this._pipeStateCaches[key]) {\n      this._pipeStateCaches[key] = /* @__PURE__ */ Object.create(null);\n    }\n    this._pipeCache = this._pipeStateCaches[key];\n  }\n  destroy() {\n    this._renderer = null;\n    this._bufferLayoutsCache = null;\n  }\n}\n/** @ignore */\nPipelineSystem.extension = {\n  type: [ExtensionType.WebGPUSystem],\n  name: \"pipeline\"\n};\n\nexport { PipelineSystem };\n//# sourceMappingURL=PipelineSystem.mjs.map\n", "\"use strict\";\nclass GpuRenderTarget {\n  constructor() {\n    this.contexts = [];\n    this.msaaTextures = [];\n    this.msaaSamples = 1;\n  }\n}\n\nexport { GpuRenderTarget };\n//# sourceMappingURL=GpuRenderTarget.mjs.map\n", "import { CLEAR } from '../../gl/const.mjs';\nimport { CanvasSource } from '../../shared/texture/sources/CanvasSource.mjs';\nimport { TextureSource } from '../../shared/texture/sources/TextureSource.mjs';\nimport { GpuRenderTarget } from './GpuRenderTarget.mjs';\n\n\"use strict\";\nclass GpuRenderTargetAdaptor {\n  init(renderer, renderTargetSystem) {\n    this._renderer = renderer;\n    this._renderTargetSystem = renderTargetSystem;\n  }\n  copyToTexture(sourceRenderSurfaceTexture, destinationTexture, originSrc, size, originDest) {\n    const renderer = this._renderer;\n    const baseGpuTexture = this._getGpuColorTexture(\n      sourceRenderSurfaceTexture\n    );\n    const backGpuTexture = renderer.texture.getGpuSource(\n      destinationTexture.source\n    );\n    renderer.encoder.commandEncoder.copyTextureToTexture(\n      {\n        texture: baseGpuTexture,\n        origin: originSrc\n      },\n      {\n        texture: backGpuTexture,\n        origin: originDest\n      },\n      size\n    );\n    return destinationTexture;\n  }\n  startRenderPass(renderTarget, clear = true, clearColor, viewport) {\n    const renderTargetSystem = this._renderTargetSystem;\n    const gpuRenderTarget = renderTargetSystem.getGpuRenderTarget(renderTarget);\n    const descriptor = this.getDescriptor(renderTarget, clear, clearColor);\n    gpuRenderTarget.descriptor = descriptor;\n    this._renderer.pipeline.setRenderTarget(gpuRenderTarget);\n    this._renderer.encoder.beginRenderPass(gpuRenderTarget);\n    this._renderer.encoder.setViewport(viewport);\n  }\n  finishRenderPass() {\n    this._renderer.encoder.endRenderPass();\n  }\n  /**\n   * returns the gpu texture for the first color texture in the render target\n   * mainly used by the filter manager to get copy the texture for blending\n   * @param renderTarget\n   * @returns a gpu texture\n   */\n  _getGpuColorTexture(renderTarget) {\n    const gpuRenderTarget = this._renderTargetSystem.getGpuRenderTarget(renderTarget);\n    if (gpuRenderTarget.contexts[0]) {\n      return gpuRenderTarget.contexts[0].getCurrentTexture();\n    }\n    return this._renderer.texture.getGpuSource(\n      renderTarget.colorTextures[0].source\n    );\n  }\n  getDescriptor(renderTarget, clear, clearValue) {\n    if (typeof clear === \"boolean\") {\n      clear = clear ? CLEAR.ALL : CLEAR.NONE;\n    }\n    const renderTargetSystem = this._renderTargetSystem;\n    const gpuRenderTarget = renderTargetSystem.getGpuRenderTarget(renderTarget);\n    const colorAttachments = renderTarget.colorTextures.map(\n      (texture, i) => {\n        const context = gpuRenderTarget.contexts[i];\n        let view;\n        let resolveTarget;\n        if (context) {\n          const currentTexture = context.getCurrentTexture();\n          const canvasTextureView = currentTexture.createView();\n          view = canvasTextureView;\n        } else {\n          view = this._renderer.texture.getGpuSource(texture).createView({\n            mipLevelCount: 1\n          });\n        }\n        if (gpuRenderTarget.msaaTextures[i]) {\n          resolveTarget = view;\n          view = this._renderer.texture.getTextureView(\n            gpuRenderTarget.msaaTextures[i]\n          );\n        }\n        const loadOp = clear & CLEAR.COLOR ? \"clear\" : \"load\";\n        clearValue ?? (clearValue = renderTargetSystem.defaultClearColor);\n        return {\n          view,\n          resolveTarget,\n          clearValue,\n          storeOp: \"store\",\n          loadOp\n        };\n      }\n    );\n    let depthStencilAttachment;\n    if ((renderTarget.stencil || renderTarget.depth) && !renderTarget.depthStencilTexture) {\n      renderTarget.ensureDepthStencilTexture();\n      renderTarget.depthStencilTexture.source.sampleCount = gpuRenderTarget.msaa ? 4 : 1;\n    }\n    if (renderTarget.depthStencilTexture) {\n      const stencilLoadOp = clear & CLEAR.STENCIL ? \"clear\" : \"load\";\n      const depthLoadOp = clear & CLEAR.DEPTH ? \"clear\" : \"load\";\n      depthStencilAttachment = {\n        view: this._renderer.texture.getGpuSource(renderTarget.depthStencilTexture.source).createView(),\n        stencilStoreOp: \"store\",\n        stencilLoadOp,\n        depthClearValue: 1,\n        depthLoadOp,\n        depthStoreOp: \"store\"\n      };\n    }\n    const descriptor = {\n      colorAttachments,\n      depthStencilAttachment\n    };\n    return descriptor;\n  }\n  clear(renderTarget, clear = true, clearColor, viewport) {\n    if (!clear)\n      return;\n    const { gpu, encoder } = this._renderer;\n    const device = gpu.device;\n    const standAlone = encoder.commandEncoder === null;\n    if (standAlone) {\n      const commandEncoder = device.createCommandEncoder();\n      const renderPassDescriptor = this.getDescriptor(renderTarget, clear, clearColor);\n      const passEncoder = commandEncoder.beginRenderPass(renderPassDescriptor);\n      passEncoder.setViewport(viewport.x, viewport.y, viewport.width, viewport.height, 0, 1);\n      passEncoder.end();\n      const gpuCommands = commandEncoder.finish();\n      device.queue.submit([gpuCommands]);\n    } else {\n      this.startRenderPass(renderTarget, clear, clearColor, viewport);\n    }\n  }\n  initGpuRenderTarget(renderTarget) {\n    renderTarget.isRoot = true;\n    const gpuRenderTarget = new GpuRenderTarget();\n    renderTarget.colorTextures.forEach((colorTexture, i) => {\n      if (CanvasSource.test(colorTexture.resource)) {\n        const context = colorTexture.resource.getContext(\n          \"webgpu\"\n        );\n        const alphaMode = colorTexture.transparent ? \"premultiplied\" : \"opaque\";\n        try {\n          context.configure({\n            device: this._renderer.gpu.device,\n            usage: GPUTextureUsage.TEXTURE_BINDING | GPUTextureUsage.COPY_DST | GPUTextureUsage.RENDER_ATTACHMENT | GPUTextureUsage.COPY_SRC,\n            format: \"bgra8unorm\",\n            alphaMode\n          });\n        } catch (e) {\n          console.error(e);\n        }\n        gpuRenderTarget.contexts[i] = context;\n      }\n      gpuRenderTarget.msaa = colorTexture.source.antialias;\n      if (colorTexture.source.antialias) {\n        const msaaTexture = new TextureSource({\n          width: 0,\n          height: 0,\n          sampleCount: 4\n        });\n        gpuRenderTarget.msaaTextures[i] = msaaTexture;\n      }\n    });\n    if (gpuRenderTarget.msaa) {\n      gpuRenderTarget.msaaSamples = 4;\n      if (renderTarget.depthStencilTexture) {\n        renderTarget.depthStencilTexture.source.sampleCount = 4;\n      }\n    }\n    return gpuRenderTarget;\n  }\n  destroyGpuRenderTarget(gpuRenderTarget) {\n    gpuRenderTarget.contexts.forEach((context) => {\n      context.unconfigure();\n    });\n    gpuRenderTarget.msaaTextures.forEach((texture) => {\n      texture.destroy();\n    });\n    gpuRenderTarget.msaaTextures.length = 0;\n    gpuRenderTarget.contexts.length = 0;\n  }\n  ensureDepthStencilTexture(renderTarget) {\n    const gpuRenderTarget = this._renderTargetSystem.getGpuRenderTarget(renderTarget);\n    if (renderTarget.depthStencilTexture && gpuRenderTarget.msaa) {\n      renderTarget.depthStencilTexture.source.sampleCount = 4;\n    }\n  }\n  resizeGpuRenderTarget(renderTarget) {\n    const gpuRenderTarget = this._renderTargetSystem.getGpuRenderTarget(renderTarget);\n    gpuRenderTarget.width = renderTarget.width;\n    gpuRenderTarget.height = renderTarget.height;\n    if (gpuRenderTarget.msaa) {\n      renderTarget.colorTextures.forEach((colorTexture, i) => {\n        const msaaTexture = gpuRenderTarget.msaaTextures[i];\n        msaaTexture?.resize(\n          colorTexture.source.width,\n          colorTexture.source.height,\n          colorTexture.source._resolution\n        );\n      });\n    }\n  }\n}\n\nexport { GpuRenderTargetAdaptor };\n//# sourceMappingURL=GpuRenderTargetAdaptor.mjs.map\n", "import { ExtensionType } from '../../../../extensions/Extensions.mjs';\nimport { RenderTargetSystem } from '../../shared/renderTarget/RenderTargetSystem.mjs';\nimport { GpuRenderTargetAdaptor } from './GpuRenderTargetAdaptor.mjs';\n\n\"use strict\";\nclass GpuRenderTargetSystem extends RenderTargetSystem {\n  constructor(renderer) {\n    super(renderer);\n    this.adaptor = new GpuRenderTargetAdaptor();\n    this.adaptor.init(renderer, this);\n  }\n}\n/** @ignore */\nGpuRenderTargetSystem.extension = {\n  type: [ExtensionType.WebGPUSystem],\n  name: \"renderTarget\"\n};\n\nexport { GpuRenderTargetSystem };\n//# sourceMappingURL=GpuRenderTargetSystem.mjs.map\n", "import { ExtensionType } from '../../../../extensions/Extensions.mjs';\n\n\"use strict\";\nclass GpuShaderSystem {\n  constructor() {\n    this._gpuProgramData = /* @__PURE__ */ Object.create(null);\n  }\n  contextChange(gpu) {\n    this._gpu = gpu;\n    this.maxTextures = gpu.device.limits.maxSampledTexturesPerShaderStage;\n  }\n  getProgramData(program) {\n    return this._gpuProgramData[program._layoutKey] || this._createGPUProgramData(program);\n  }\n  _createGPUProgramData(program) {\n    const device = this._gpu.device;\n    const bindGroups = program.gpuLayout.map((group) => device.createBindGroupLayout({ entries: group }));\n    const pipelineLayoutDesc = { bindGroupLayouts: bindGroups };\n    this._gpuProgramData[program._layoutKey] = {\n      bindGroups,\n      pipeline: device.createPipelineLayout(pipelineLayoutDesc)\n    };\n    return this._gpuProgramData[program._layoutKey];\n  }\n  destroy() {\n    this._gpu = null;\n    this._gpuProgramData = null;\n  }\n}\n/** @ignore */\nGpuShaderSystem.extension = {\n  type: [\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"shader\"\n};\n\nexport { GpuShaderSystem };\n//# sourceMappingURL=GpuShaderSystem.mjs.map\n", "\"use strict\";\nconst GpuBlendModesToPixi = {};\nGpuBlendModesToPixi.normal = {\n  alpha: {\n    srcFactor: \"one\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  },\n  color: {\n    srcFactor: \"one\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  }\n};\nGpuBlendModesToPixi.add = {\n  alpha: {\n    srcFactor: \"src-alpha\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  },\n  color: {\n    srcFactor: \"one\",\n    dstFactor: \"one\",\n    operation: \"add\"\n  }\n};\nGpuBlendModesToPixi.multiply = {\n  alpha: {\n    srcFactor: \"one\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  },\n  color: {\n    srcFactor: \"dst\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  }\n};\nGpuBlendModesToPixi.screen = {\n  alpha: {\n    srcFactor: \"one\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  },\n  color: {\n    srcFactor: \"one\",\n    dstFactor: \"one-minus-src\",\n    operation: \"add\"\n  }\n};\nGpuBlendModesToPixi.overlay = {\n  alpha: {\n    srcFactor: \"one\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  },\n  color: {\n    srcFactor: \"one\",\n    dstFactor: \"one-minus-src\",\n    operation: \"add\"\n  }\n};\nGpuBlendModesToPixi.none = {\n  alpha: {\n    srcFactor: \"one\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  },\n  color: {\n    srcFactor: \"zero\",\n    dstFactor: \"zero\",\n    operation: \"add\"\n  }\n};\nGpuBlendModesToPixi[\"normal-npm\"] = {\n  alpha: {\n    srcFactor: \"one\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  },\n  color: {\n    srcFactor: \"src-alpha\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  }\n};\nGpuBlendModesToPixi[\"add-npm\"] = {\n  alpha: {\n    srcFactor: \"one\",\n    dstFactor: \"one\",\n    operation: \"add\"\n  },\n  color: {\n    srcFactor: \"src-alpha\",\n    dstFactor: \"one\",\n    operation: \"add\"\n  }\n};\nGpuBlendModesToPixi[\"screen-npm\"] = {\n  alpha: {\n    srcFactor: \"one\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  },\n  color: {\n    srcFactor: \"src-alpha\",\n    dstFactor: \"one-minus-src\",\n    operation: \"add\"\n  }\n};\nGpuBlendModesToPixi.erase = {\n  alpha: {\n    srcFactor: \"zero\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  },\n  color: {\n    srcFactor: \"zero\",\n    dstFactor: \"one-minus-src\",\n    operation: \"add\"\n  }\n};\nGpuBlendModesToPixi.min = {\n  alpha: {\n    srcFactor: \"one\",\n    dstFactor: \"one\",\n    operation: \"min\"\n  },\n  color: {\n    srcFactor: \"one\",\n    dstFactor: \"one\",\n    operation: \"min\"\n  }\n};\nGpuBlendModesToPixi.max = {\n  alpha: {\n    srcFactor: \"one\",\n    dstFactor: \"one\",\n    operation: \"max\"\n  },\n  color: {\n    srcFactor: \"one\",\n    dstFactor: \"one\",\n    operation: \"max\"\n  }\n};\n\nexport { GpuBlendModesToPixi };\n//# sourceMappingURL=GpuBlendModesToPixi.mjs.map\n", "import { ExtensionType } from '../../../../extensions/Extensions.mjs';\nimport { State } from '../../shared/state/State.mjs';\nimport { GpuBlendModesToPixi } from './GpuBlendModesToPixi.mjs';\n\n\"use strict\";\nclass GpuStateSystem {\n  constructor() {\n    this.defaultState = new State();\n    this.defaultState.blend = true;\n  }\n  contextChange(gpu) {\n    this.gpu = gpu;\n  }\n  /**\n   * Gets the blend mode data for the current state\n   * @param state - The state to get the blend mode from\n   */\n  getColorTargets(state) {\n    const blend = GpuBlendModesToPixi[state.blendMode] || GpuBlendModesToPixi.normal;\n    return [\n      {\n        format: \"bgra8unorm\",\n        writeMask: 0,\n        blend\n      }\n    ];\n  }\n  destroy() {\n    this.gpu = null;\n  }\n}\n/** @ignore */\nGpuStateSystem.extension = {\n  type: [\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"state\"\n};\n\nexport { GpuStateSystem };\n//# sourceMappingURL=GpuStateSystem.mjs.map\n", "\"use strict\";\nconst gpuUploadBufferImageResource = {\n  type: \"image\",\n  upload(source, gpuTexture, gpu) {\n    const resource = source.resource;\n    const total = (source.pixelWidth | 0) * (source.pixelHeight | 0);\n    const bytesPerPixel = resource.byteLength / total;\n    gpu.device.queue.writeTexture(\n      { texture: gpuTexture },\n      resource,\n      {\n        offset: 0,\n        rowsPerImage: source.pixelHeight,\n        bytesPerRow: source.pixelHeight * bytesPerPixel\n      },\n      {\n        width: source.pixelWidth,\n        height: source.pixelHeight,\n        depthOrArrayLayers: 1\n      }\n    );\n  }\n};\n\nexport { gpuUploadBufferImageResource };\n//# sourceMappingURL=gpuUploadBufferImageResource.mjs.map\n", "\"use strict\";\nconst blockDataMap = {\n  \"bc1-rgba-unorm\": { blockBytes: 8, blockWidth: 4, blockHeight: 4 },\n  \"bc2-rgba-unorm\": { blockBytes: 16, blockWidth: 4, blockHeight: 4 },\n  \"bc3-rgba-unorm\": { blockBytes: 16, blockWidth: 4, blockHeight: 4 },\n  \"bc7-rgba-unorm\": { blockBytes: 16, blockWidth: 4, blockHeight: 4 },\n  \"etc1-rgb-unorm\": { blockBytes: 8, blockWidth: 4, blockHeight: 4 },\n  \"etc2-rgba8unorm\": { blockBytes: 16, blockWidth: 4, blockHeight: 4 },\n  \"astc-4x4-unorm\": { blockBytes: 16, blockWidth: 4, blockHeight: 4 }\n};\nconst defaultBlockData = { blockBytes: 4, blockWidth: 1, blockHeight: 1 };\nconst gpuUploadCompressedTextureResource = {\n  type: \"compressed\",\n  upload(source, gpuTexture, gpu) {\n    let mipWidth = source.pixelWidth;\n    let mipHeight = source.pixelHeight;\n    const blockData = blockDataMap[source.format] || defaultBlockData;\n    for (let i = 0; i < source.resource.length; i++) {\n      const levelBuffer = source.resource[i];\n      const bytesPerRow = Math.ceil(mipWidth / blockData.blockWidth) * blockData.blockBytes;\n      gpu.device.queue.writeTexture(\n        {\n          texture: gpuTexture,\n          mipLevel: i\n        },\n        levelBuffer,\n        {\n          offset: 0,\n          bytesPerRow\n        },\n        {\n          width: Math.ceil(mipWidth / blockData.blockWidth) * blockData.blockWidth,\n          height: Math.ceil(mipHeight / blockData.blockHeight) * blockData.blockHeight,\n          depthOrArrayLayers: 1\n        }\n      );\n      mipWidth = Math.max(mipWidth >> 1, 1);\n      mipHeight = Math.max(mipHeight >> 1, 1);\n    }\n  }\n};\n\nexport { blockDataMap, gpuUploadCompressedTextureResource };\n//# sourceMappingURL=gpuUploadCompressedTextureResource.mjs.map\n", "\"use strict\";\nconst gpuUploadImageResource = {\n  type: \"image\",\n  upload(source, gpuTexture, gpu) {\n    const resource = source.resource;\n    if (!resource)\n      return;\n    const width = Math.min(gpuTexture.width, source.resourceWidth || source.pixelWidth);\n    const height = Math.min(gpuTexture.height, source.resourceHeight || source.pixelHeight);\n    const premultipliedAlpha = source.alphaMode === \"premultiply-alpha-on-upload\";\n    gpu.device.queue.copyExternalImageToTexture(\n      { source: resource },\n      { texture: gpuTexture, premultipliedAlpha },\n      {\n        width,\n        height\n      }\n    );\n  }\n};\n\nexport { gpuUploadImageResource };\n//# sourceMappingURL=gpuUploadImageSource.mjs.map\n", "import { gpuUploadImageResource } from './gpuUploadImageSource.mjs';\n\n\"use strict\";\nconst gpuUploadVideoResource = {\n  type: \"video\",\n  upload(source, gpuTexture, gpu) {\n    gpuUploadImageResource.upload(source, gpuTexture, gpu);\n  }\n};\n\nexport { gpuUploadVideoResource };\n//# sourceMappingURL=gpuUploadVideoSource.mjs.map\n", "\"use strict\";\nclass GpuMipmapGenerator {\n  constructor(device) {\n    this.device = device;\n    this.sampler = device.createSampler({ minFilter: \"linear\" });\n    this.pipelines = {};\n  }\n  _getMipmapPipeline(format) {\n    let pipeline = this.pipelines[format];\n    if (!pipeline) {\n      if (!this.mipmapShaderModule) {\n        this.mipmapShaderModule = this.device.createShaderModule({\n          code: (\n            /* wgsl */\n            `\n                        var<private> pos : array<vec2<f32>, 3> = array<vec2<f32>, 3>(\n                        vec2<f32>(-1.0, -1.0), vec2<f32>(-1.0, 3.0), vec2<f32>(3.0, -1.0));\n\n                        struct VertexOutput {\n                        @builtin(position) position : vec4<f32>,\n                        @location(0) texCoord : vec2<f32>,\n                        };\n\n                        @vertex\n                        fn vertexMain(@builtin(vertex_index) vertexIndex : u32) -> VertexOutput {\n                        var output : VertexOutput;\n                        output.texCoord = pos[vertexIndex] * vec2<f32>(0.5, -0.5) + vec2<f32>(0.5);\n                        output.position = vec4<f32>(pos[vertexIndex], 0.0, 1.0);\n                        return output;\n                        }\n\n                        @group(0) @binding(0) var imgSampler : sampler;\n                        @group(0) @binding(1) var img : texture_2d<f32>;\n\n                        @fragment\n                        fn fragmentMain(@location(0) texCoord : vec2<f32>) -> @location(0) vec4<f32> {\n                        return textureSample(img, imgSampler, texCoord);\n                        }\n                    `\n          )\n        });\n      }\n      pipeline = this.device.createRenderPipeline({\n        layout: \"auto\",\n        vertex: {\n          module: this.mipmapShaderModule,\n          entryPoint: \"vertexMain\"\n        },\n        fragment: {\n          module: this.mipmapShaderModule,\n          entryPoint: \"fragmentMain\",\n          targets: [{ format }]\n        }\n      });\n      this.pipelines[format] = pipeline;\n    }\n    return pipeline;\n  }\n  /**\n   * Generates mipmaps for the given GPUTexture from the data in level 0.\n   * @param {module:External.GPUTexture} texture - Texture to generate mipmaps for.\n   * @returns {module:External.GPUTexture} - The originally passed texture\n   */\n  generateMipmap(texture) {\n    const pipeline = this._getMipmapPipeline(texture.format);\n    if (texture.dimension === \"3d\" || texture.dimension === \"1d\") {\n      throw new Error(\"Generating mipmaps for non-2d textures is currently unsupported!\");\n    }\n    let mipTexture = texture;\n    const arrayLayerCount = texture.depthOrArrayLayers || 1;\n    const renderToSource = texture.usage & GPUTextureUsage.RENDER_ATTACHMENT;\n    if (!renderToSource) {\n      const mipTextureDescriptor = {\n        size: {\n          width: Math.ceil(texture.width / 2),\n          height: Math.ceil(texture.height / 2),\n          depthOrArrayLayers: arrayLayerCount\n        },\n        format: texture.format,\n        usage: GPUTextureUsage.TEXTURE_BINDING | GPUTextureUsage.COPY_SRC | GPUTextureUsage.RENDER_ATTACHMENT,\n        mipLevelCount: texture.mipLevelCount - 1\n      };\n      mipTexture = this.device.createTexture(mipTextureDescriptor);\n    }\n    const commandEncoder = this.device.createCommandEncoder({});\n    const bindGroupLayout = pipeline.getBindGroupLayout(0);\n    for (let arrayLayer = 0; arrayLayer < arrayLayerCount; ++arrayLayer) {\n      let srcView = texture.createView({\n        baseMipLevel: 0,\n        mipLevelCount: 1,\n        dimension: \"2d\",\n        baseArrayLayer: arrayLayer,\n        arrayLayerCount: 1\n      });\n      let dstMipLevel = renderToSource ? 1 : 0;\n      for (let i = 1; i < texture.mipLevelCount; ++i) {\n        const dstView = mipTexture.createView({\n          baseMipLevel: dstMipLevel++,\n          mipLevelCount: 1,\n          dimension: \"2d\",\n          baseArrayLayer: arrayLayer,\n          arrayLayerCount: 1\n        });\n        const passEncoder = commandEncoder.beginRenderPass({\n          colorAttachments: [{\n            view: dstView,\n            storeOp: \"store\",\n            loadOp: \"clear\",\n            clearValue: { r: 0, g: 0, b: 0, a: 0 }\n          }]\n        });\n        const bindGroup = this.device.createBindGroup({\n          layout: bindGroupLayout,\n          entries: [{\n            binding: 0,\n            resource: this.sampler\n          }, {\n            binding: 1,\n            resource: srcView\n          }]\n        });\n        passEncoder.setPipeline(pipeline);\n        passEncoder.setBindGroup(0, bindGroup);\n        passEncoder.draw(3, 1, 0, 0);\n        passEncoder.end();\n        srcView = dstView;\n      }\n    }\n    if (!renderToSource) {\n      const mipLevelSize = {\n        width: Math.ceil(texture.width / 2),\n        height: Math.ceil(texture.height / 2),\n        depthOrArrayLayers: arrayLayerCount\n      };\n      for (let i = 1; i < texture.mipLevelCount; ++i) {\n        commandEncoder.copyTextureToTexture({\n          texture: mipTexture,\n          mipLevel: i - 1\n        }, {\n          texture,\n          mipLevel: i\n        }, mipLevelSize);\n        mipLevelSize.width = Math.ceil(mipLevelSize.width / 2);\n        mipLevelSize.height = Math.ceil(mipLevelSize.height / 2);\n      }\n    }\n    this.device.queue.submit([commandEncoder.finish()]);\n    if (!renderToSource) {\n      mipTexture.destroy();\n    }\n    return texture;\n  }\n}\n\nexport { GpuMipmapGenerator };\n//# sourceMappingURL=GpuMipmapGenerator.mjs.map\n", "import { DOMAdapter } from '../../../../environment/adapter.mjs';\nimport { ExtensionType } from '../../../../extensions/Extensions.mjs';\nimport { UniformGroup } from '../../shared/shader/UniformGroup.mjs';\nimport { CanvasPool } from '../../shared/texture/CanvasPool.mjs';\nimport { BindGroup } from '../shader/BindGroup.mjs';\nimport { gpuUploadBufferImageResource } from './uploaders/gpuUploadBufferImageResource.mjs';\nimport { gpuUploadCompressedTextureResource, blockDataMap } from './uploaders/gpuUploadCompressedTextureResource.mjs';\nimport { gpuUploadImageResource } from './uploaders/gpuUploadImageSource.mjs';\nimport { gpuUploadVideoResource } from './uploaders/gpuUploadVideoSource.mjs';\nimport { GpuMipmapGenerator } from './utils/GpuMipmapGenerator.mjs';\n\n\"use strict\";\nclass GpuTextureSystem {\n  constructor(renderer) {\n    this.managedTextures = [];\n    this._gpuSources = /* @__PURE__ */ Object.create(null);\n    this._gpuSamplers = /* @__PURE__ */ Object.create(null);\n    this._bindGroupHash = /* @__PURE__ */ Object.create(null);\n    this._textureViewHash = /* @__PURE__ */ Object.create(null);\n    this._uploads = {\n      image: gpuUploadImageResource,\n      buffer: gpuUploadBufferImageResource,\n      video: gpuUploadVideoResource,\n      compressed: gpuUploadCompressedTextureResource\n    };\n    this._renderer = renderer;\n    renderer.renderableGC.addManagedHash(this, \"_gpuSources\");\n    renderer.renderableGC.addManagedHash(this, \"_gpuSamplers\");\n    renderer.renderableGC.addManagedHash(this, \"_bindGroupHash\");\n    renderer.renderableGC.addManagedHash(this, \"_textureViewHash\");\n  }\n  contextChange(gpu) {\n    this._gpu = gpu;\n  }\n  initSource(source) {\n    if (source.autoGenerateMipmaps) {\n      const biggestDimension = Math.max(source.pixelWidth, source.pixelHeight);\n      source.mipLevelCount = Math.floor(Math.log2(biggestDimension)) + 1;\n    }\n    let usage = GPUTextureUsage.TEXTURE_BINDING | GPUTextureUsage.COPY_DST;\n    if (source.uploadMethodId !== \"compressed\") {\n      usage |= GPUTextureUsage.RENDER_ATTACHMENT;\n      usage |= GPUTextureUsage.COPY_SRC;\n    }\n    const blockData = blockDataMap[source.format] || { blockBytes: 4, blockWidth: 1, blockHeight: 1 };\n    const width = Math.ceil(source.pixelWidth / blockData.blockWidth) * blockData.blockWidth;\n    const height = Math.ceil(source.pixelHeight / blockData.blockHeight) * blockData.blockHeight;\n    const textureDescriptor = {\n      label: source.label,\n      size: { width, height },\n      format: source.format,\n      sampleCount: source.sampleCount,\n      mipLevelCount: source.mipLevelCount,\n      dimension: source.dimension,\n      usage\n    };\n    const gpuTexture = this._gpu.device.createTexture(textureDescriptor);\n    this._gpuSources[source.uid] = gpuTexture;\n    if (!this.managedTextures.includes(source)) {\n      source.on(\"update\", this.onSourceUpdate, this);\n      source.on(\"resize\", this.onSourceResize, this);\n      source.on(\"destroy\", this.onSourceDestroy, this);\n      source.on(\"unload\", this.onSourceUnload, this);\n      source.on(\"updateMipmaps\", this.onUpdateMipmaps, this);\n      this.managedTextures.push(source);\n    }\n    this.onSourceUpdate(source);\n    return gpuTexture;\n  }\n  onSourceUpdate(source) {\n    const gpuTexture = this.getGpuSource(source);\n    if (!gpuTexture)\n      return;\n    if (this._uploads[source.uploadMethodId]) {\n      this._uploads[source.uploadMethodId].upload(source, gpuTexture, this._gpu);\n    }\n    if (source.autoGenerateMipmaps && source.mipLevelCount > 1) {\n      this.onUpdateMipmaps(source);\n    }\n  }\n  onSourceUnload(source) {\n    const gpuTexture = this._gpuSources[source.uid];\n    if (gpuTexture) {\n      this._gpuSources[source.uid] = null;\n      gpuTexture.destroy();\n    }\n  }\n  onUpdateMipmaps(source) {\n    if (!this._mipmapGenerator) {\n      this._mipmapGenerator = new GpuMipmapGenerator(this._gpu.device);\n    }\n    const gpuTexture = this.getGpuSource(source);\n    this._mipmapGenerator.generateMipmap(gpuTexture);\n  }\n  onSourceDestroy(source) {\n    source.off(\"update\", this.onSourceUpdate, this);\n    source.off(\"unload\", this.onSourceUnload, this);\n    source.off(\"destroy\", this.onSourceDestroy, this);\n    source.off(\"resize\", this.onSourceResize, this);\n    source.off(\"updateMipmaps\", this.onUpdateMipmaps, this);\n    this.managedTextures.splice(this.managedTextures.indexOf(source), 1);\n    this.onSourceUnload(source);\n  }\n  onSourceResize(source) {\n    const gpuTexture = this._gpuSources[source.uid];\n    if (!gpuTexture) {\n      this.initSource(source);\n    } else if (gpuTexture.width !== source.pixelWidth || gpuTexture.height !== source.pixelHeight) {\n      this._textureViewHash[source.uid] = null;\n      this._bindGroupHash[source.uid] = null;\n      this.onSourceUnload(source);\n      this.initSource(source);\n    }\n  }\n  _initSampler(sampler) {\n    this._gpuSamplers[sampler._resourceId] = this._gpu.device.createSampler(sampler);\n    return this._gpuSamplers[sampler._resourceId];\n  }\n  getGpuSampler(sampler) {\n    return this._gpuSamplers[sampler._resourceId] || this._initSampler(sampler);\n  }\n  getGpuSource(source) {\n    return this._gpuSources[source.uid] || this.initSource(source);\n  }\n  /**\n   * this returns s bind group for a specific texture, the bind group contains\n   * - the texture source\n   * - the texture style\n   * - the texture matrix\n   * This is cached so the bind group should only be created once per texture\n   * @param texture - the texture you want the bindgroup for\n   * @returns the bind group for the texture\n   */\n  getTextureBindGroup(texture) {\n    return this._bindGroupHash[texture.uid] ?? this._createTextureBindGroup(texture);\n  }\n  _createTextureBindGroup(texture) {\n    const source = texture.source;\n    this._bindGroupHash[texture.uid] = new BindGroup({\n      0: source,\n      1: source.style,\n      2: new UniformGroup({\n        uTextureMatrix: { type: \"mat3x3<f32>\", value: texture.textureMatrix.mapCoord }\n      })\n    });\n    return this._bindGroupHash[texture.uid];\n  }\n  getTextureView(texture) {\n    const source = texture.source;\n    return this._textureViewHash[source.uid] ?? this._createTextureView(source);\n  }\n  _createTextureView(texture) {\n    this._textureViewHash[texture.uid] = this.getGpuSource(texture).createView();\n    return this._textureViewHash[texture.uid];\n  }\n  generateCanvas(texture) {\n    const renderer = this._renderer;\n    const commandEncoder = renderer.gpu.device.createCommandEncoder();\n    const canvas = DOMAdapter.get().createCanvas();\n    canvas.width = texture.source.pixelWidth;\n    canvas.height = texture.source.pixelHeight;\n    const context = canvas.getContext(\"webgpu\");\n    context.configure({\n      device: renderer.gpu.device,\n      usage: GPUTextureUsage.COPY_DST | GPUTextureUsage.COPY_SRC,\n      format: DOMAdapter.get().getNavigator().gpu.getPreferredCanvasFormat(),\n      alphaMode: \"premultiplied\"\n    });\n    commandEncoder.copyTextureToTexture({\n      texture: renderer.texture.getGpuSource(texture.source),\n      origin: {\n        x: 0,\n        y: 0\n      }\n    }, {\n      texture: context.getCurrentTexture()\n    }, {\n      width: canvas.width,\n      height: canvas.height\n    });\n    renderer.gpu.device.queue.submit([commandEncoder.finish()]);\n    return canvas;\n  }\n  getPixels(texture) {\n    const webGPUCanvas = this.generateCanvas(texture);\n    const canvasAndContext = CanvasPool.getOptimalCanvasAndContext(webGPUCanvas.width, webGPUCanvas.height);\n    const context = canvasAndContext.context;\n    context.drawImage(webGPUCanvas, 0, 0);\n    const { width, height } = webGPUCanvas;\n    const imageData = context.getImageData(0, 0, width, height);\n    const pixels = new Uint8ClampedArray(imageData.data.buffer);\n    CanvasPool.returnCanvasAndContext(canvasAndContext);\n    return { pixels, width, height };\n  }\n  destroy() {\n    this.managedTextures.slice().forEach((source) => this.onSourceDestroy(source));\n    this.managedTextures = null;\n    for (const k of Object.keys(this._bindGroupHash)) {\n      const key = Number(k);\n      const bindGroup = this._bindGroupHash[key];\n      bindGroup?.destroy();\n      this._bindGroupHash[key] = null;\n    }\n    this._gpu = null;\n    this._mipmapGenerator = null;\n    this._gpuSources = null;\n    this._bindGroupHash = null;\n    this._textureViewHash = null;\n    this._gpuSamplers = null;\n  }\n}\n/** @ignore */\nGpuTextureSystem.extension = {\n  type: [\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"texture\"\n};\n\nexport { GpuTextureSystem };\n//# sourceMappingURL=GpuTextureSystem.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { Matrix } from '../../../maths/matrix/Matrix.mjs';\nimport { getMaxTexturesPerBatch } from '../../../rendering/batcher/gl/utils/maxRecommendedTextures.mjs';\nimport { getTextureBatchBindGroup } from '../../../rendering/batcher/gpu/getTextureBatchBindGroup.mjs';\nimport { compileHighShaderGpuProgram } from '../../../rendering/high-shader/compileHighShaderToProgram.mjs';\nimport { colorBit } from '../../../rendering/high-shader/shader-bits/colorBit.mjs';\nimport { generateTextureBatchBit } from '../../../rendering/high-shader/shader-bits/generateTextureBatchBit.mjs';\nimport { localUniformBitGroup2 } from '../../../rendering/high-shader/shader-bits/localUniformBit.mjs';\nimport { roundPixelsBit } from '../../../rendering/high-shader/shader-bits/roundPixelsBit.mjs';\nimport { Shader } from '../../../rendering/renderers/shared/shader/Shader.mjs';\nimport { UniformGroup } from '../../../rendering/renderers/shared/shader/UniformGroup.mjs';\n\n\"use strict\";\nclass GpuGraphicsAdaptor {\n  init() {\n    const localUniforms = new UniformGroup({\n      uTransformMatrix: { value: new Matrix(), type: \"mat3x3<f32>\" },\n      uColor: { value: new Float32Array([1, 1, 1, 1]), type: \"vec4<f32>\" },\n      uRound: { value: 0, type: \"f32\" }\n    });\n    const gpuProgram = compileHighShaderGpuProgram({\n      name: \"graphics\",\n      bits: [\n        colorBit,\n        generateTextureBatchBit(getMaxTexturesPerBatch()),\n        localUniformBitGroup2,\n        roundPixelsBit\n      ]\n    });\n    this.shader = new Shader({\n      gpuProgram,\n      resources: {\n        // added on the fly!\n        localUniforms\n      }\n    });\n  }\n  execute(graphicsPipe, renderable) {\n    const context = renderable.context;\n    const shader = context.customShader || this.shader;\n    const renderer = graphicsPipe.renderer;\n    const contextSystem = renderer.graphicsContext;\n    const {\n      batcher,\n      instructions\n    } = contextSystem.getContextRenderData(context);\n    const encoder = renderer.encoder;\n    encoder.setGeometry(batcher.geometry, shader.gpuProgram);\n    const globalUniformsBindGroup = renderer.globalUniforms.bindGroup;\n    encoder.setBindGroup(0, globalUniformsBindGroup, shader.gpuProgram);\n    const localBindGroup = renderer.renderPipes.uniformBatch.getUniformBindGroup(shader.resources.localUniforms, true);\n    encoder.setBindGroup(2, localBindGroup, shader.gpuProgram);\n    const batches = instructions.instructions;\n    let topology = null;\n    for (let i = 0; i < instructions.instructionSize; i++) {\n      const batch = batches[i];\n      if (batch.topology !== topology) {\n        topology = batch.topology;\n        encoder.setPipelineFromGeometryProgramAndState(\n          batcher.geometry,\n          shader.gpuProgram,\n          graphicsPipe.state,\n          batch.topology\n        );\n      }\n      shader.groups[1] = batch.bindGroup;\n      if (!batch.gpuBindGroup) {\n        const textureBatch = batch.textures;\n        batch.bindGroup = getTextureBatchBindGroup(textureBatch.textures, textureBatch.count);\n        batch.gpuBindGroup = renderer.bindGroup.getBindGroup(\n          batch.bindGroup,\n          shader.gpuProgram,\n          1\n        );\n      }\n      encoder.setBindGroup(1, batch.bindGroup, shader.gpuProgram);\n      encoder.renderPassEncoder.drawIndexed(batch.size, 1, batch.start);\n    }\n  }\n  destroy() {\n    this.shader.destroy(true);\n    this.shader = null;\n  }\n}\n/** @ignore */\nGpuGraphicsAdaptor.extension = {\n  type: [\n    ExtensionType.WebGPUPipesAdaptor\n  ],\n  name: \"graphics\"\n};\n\nexport { GpuGraphicsAdaptor };\n//# sourceMappingURL=GpuGraphicsAdaptor.mjs.map\n", "import { ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { Matrix } from '../../../maths/matrix/Matrix.mjs';\nimport { compileHighShaderGpuProgram } from '../../../rendering/high-shader/compileHighShaderToProgram.mjs';\nimport { localUniformBit } from '../../../rendering/high-shader/shader-bits/localUniformBit.mjs';\nimport { roundPixelsBit } from '../../../rendering/high-shader/shader-bits/roundPixelsBit.mjs';\nimport { textureBit } from '../../../rendering/high-shader/shader-bits/textureBit.mjs';\nimport { Shader } from '../../../rendering/renderers/shared/shader/Shader.mjs';\nimport { Texture } from '../../../rendering/renderers/shared/texture/Texture.mjs';\nimport { warn } from '../../../utils/logging/warn.mjs';\n\n\"use strict\";\nclass GpuMeshAdapter {\n  init() {\n    const gpuProgram = compileHighShaderGpuProgram({\n      name: \"mesh\",\n      bits: [\n        localUniformBit,\n        textureBit,\n        roundPixelsBit\n      ]\n    });\n    this._shader = new Shader({\n      gpuProgram,\n      resources: {\n        uTexture: Texture.EMPTY._source,\n        uSampler: Texture.EMPTY._source.style,\n        textureUniforms: {\n          uTextureMatrix: { type: \"mat3x3<f32>\", value: new Matrix() }\n        }\n      }\n    });\n  }\n  execute(meshPipe, mesh) {\n    const renderer = meshPipe.renderer;\n    let shader = mesh._shader;\n    if (!shader) {\n      shader = this._shader;\n      shader.groups[2] = renderer.texture.getTextureBindGroup(mesh.texture);\n    } else if (!shader.gpuProgram) {\n      warn(\"Mesh shader has no gpuProgram\", mesh.shader);\n      return;\n    }\n    const gpuProgram = shader.gpuProgram;\n    if (gpuProgram.autoAssignGlobalUniforms) {\n      shader.groups[0] = renderer.globalUniforms.bindGroup;\n    }\n    if (gpuProgram.autoAssignLocalUniforms) {\n      const localUniforms = meshPipe.localUniforms;\n      shader.groups[1] = renderer.renderPipes.uniformBatch.getUniformBindGroup(localUniforms, true);\n    }\n    renderer.encoder.draw({\n      geometry: mesh._geometry,\n      shader,\n      state: mesh.state\n    });\n  }\n  destroy() {\n    this._shader.destroy(true);\n    this._shader = null;\n  }\n}\n/** @ignore */\nGpuMeshAdapter.extension = {\n  type: [\n    ExtensionType.WebGPUPipesAdaptor\n  ],\n  name: \"mesh\"\n};\n\nexport { GpuMeshAdapter };\n//# sourceMappingURL=GpuMeshAdapter.mjs.map\n", "import { extensions, ExtensionType } from '../../../extensions/Extensions.mjs';\nimport { GpuGraphicsAdaptor } from '../../../scene/graphics/gpu/GpuGraphicsAdaptor.mjs';\nimport { GpuMeshAdapter } from '../../../scene/mesh/gpu/GpuMeshAdapter.mjs';\nimport { GpuBatchAdaptor } from '../../batcher/gpu/GpuBatchAdaptor.mjs';\nimport { AbstractRenderer } from '../shared/system/AbstractRenderer.mjs';\nimport { SharedSystems, SharedRenderPipes } from '../shared/system/SharedSystems.mjs';\nimport { RendererType } from '../types.mjs';\nimport { BindGroupSystem } from './BindGroupSystem.mjs';\nimport { GpuBufferSystem } from './buffer/GpuBufferSystem.mjs';\nimport { GpuColorMaskSystem } from './GpuColorMaskSystem.mjs';\nimport { GpuDeviceSystem } from './GpuDeviceSystem.mjs';\nimport { GpuEncoderSystem } from './GpuEncoderSystem.mjs';\nimport { GpuStencilSystem } from './GpuStencilSystem.mjs';\nimport { GpuUboSystem } from './GpuUboSystem.mjs';\nimport { GpuUniformBatchPipe } from './GpuUniformBatchPipe.mjs';\nimport { PipelineSystem } from './pipeline/PipelineSystem.mjs';\nimport { GpuRenderTargetSystem } from './renderTarget/GpuRenderTargetSystem.mjs';\nimport { GpuShaderSystem } from './shader/GpuShaderSystem.mjs';\nimport { GpuStateSystem } from './state/GpuStateSystem.mjs';\nimport { GpuTextureSystem } from './texture/GpuTextureSystem.mjs';\n\n\"use strict\";\nconst DefaultWebGPUSystems = [\n  ...SharedSystems,\n  GpuUboSystem,\n  GpuEncoderSystem,\n  GpuDeviceSystem,\n  GpuBufferSystem,\n  GpuTextureSystem,\n  GpuRenderTargetSystem,\n  GpuShaderSystem,\n  GpuStateSystem,\n  PipelineSystem,\n  GpuColorMaskSystem,\n  GpuStencilSystem,\n  BindGroupSystem\n];\nconst DefaultWebGPUPipes = [...SharedRenderPipes, GpuUniformBatchPipe];\nconst DefaultWebGPUAdapters = [GpuBatchAdaptor, GpuMeshAdapter, GpuGraphicsAdaptor];\nconst systems = [];\nconst renderPipes = [];\nconst renderPipeAdaptors = [];\nextensions.handleByNamedList(ExtensionType.WebGPUSystem, systems);\nextensions.handleByNamedList(ExtensionType.WebGPUPipes, renderPipes);\nextensions.handleByNamedList(ExtensionType.WebGPUPipesAdaptor, renderPipeAdaptors);\nextensions.add(...DefaultWebGPUSystems, ...DefaultWebGPUPipes, ...DefaultWebGPUAdapters);\nclass WebGPURenderer extends AbstractRenderer {\n  constructor() {\n    const systemConfig = {\n      name: \"webgpu\",\n      type: RendererType.WEBGPU,\n      systems,\n      renderPipes,\n      renderPipeAdaptors\n    };\n    super(systemConfig);\n  }\n}\n\nexport { WebGPURenderer };\n//# sourceMappingURL=WebGPURenderer.mjs.map\n"], "names": ["tempState", "State", "GpuBatchAdaptor", "batchPipe", "geometry", "shader", "renderer", "encoder", "program", "globalUniformsBindGroup", "batch", "textureBatch", "getTextureBatchBindGroup", "gpuBindGroup", "pipeline", "ExtensionType", "BindGroupSystem", "gpu", "bindGroup", "groupIndex", "group", "device", "groupLayout", "entries", "j", "resource", "gpuResource", "uniformGroup", "buffer", "bufferResource", "sampler", "texture", "layout", "key", "GpuBufferSystem", "gpuBuffer", "data", "id", "fastCopy", "UboBatch", "minUniformOffsetAlignment", "size", "start", "newSize", "array", "offset", "i", "GpuColorMaskSystem", "colorMask", "GpuDeviceSystem", "options", "adapter", "DOMAdapter", "requiredFeatures", "feature", "GpuEncoderSystem", "resolve", "gpuRenderTarget", "viewport", "state", "topology", "index", "indexFormat", "buffersToBind", "skipSync", "instanceCount", "descriptor", "boundPipeline", "boundVertexBuffer", "boundIndexBuffer", "boundBindGroup", "GpuStencilSystem", "renderTarget", "stencilState", "STENCIL_MODES", "stencilMode", "stencilReference", "WGSL_ALIGN_SIZE_DATA", "createUboElementsWGSL", "uniformData", "uboElements", "uboElement", "align", "generateArraySyncWGSL", "offsetToAdd", "remainder", "createUboSyncFunctionWGSL", "createUboSyncFunction", "uboSyncFunctionsWGSL", "GpuUboSystem", "UboSystem", "GpuUniformBatchPipe", "totalBuffers", "usage", "BufferUsage", "<PERSON><PERSON><PERSON>", "duplicate", "BufferResource", "BindGroup", "bufferSystem", "firstBuffer", "command<PERSON><PERSON><PERSON>", "topologyStringToId", "getGraphicsStateKey", "geometryLayout", "<PERSON><PERSON><PERSON><PERSON>", "blendMode", "getGlobalStateKey", "stencilStateId", "multiSampleCount", "PipelineSystem", "multisampleCount", "GpuStencilModesToPixi", "<PERSON><PERSON><PERSON><PERSON>", "ensureAttributes", "buffers", "blendModes", "code", "keyGen", "<PERSON><PERSON><PERSON><PERSON>", "attribute", "<PERSON><PERSON><PERSON>", "createIdFromString", "bufferNamesToBind", "attributeData", "shaderLocation", "vertexBuffersLayout", "bufferEntry", "bufferEntryAttributes", "warn", "GpuRenderTarget", "GpuRenderTargetAdaptor", "renderTargetSystem", "sourceRenderSurfaceTexture", "destinationTexture", "originSrc", "originDest", "baseGpuTexture", "backGpuTexture", "clear", "clearColor", "clearValue", "CLEAR", "colorAttachments", "context", "view", "<PERSON><PERSON><PERSON><PERSON>", "loadOp", "depthStencilAttachment", "stencilLoadOp", "depthLoadOp", "renderPassDescriptor", "gpuCommands", "colorTexture", "CanvasSource", "alphaMode", "e", "msaaTexture", "TextureSource", "GpuRenderTargetSystem", "RenderTargetSystem", "GpuShaderSystem", "bindGroups", "pipelineLayoutDesc", "GpuBlendModesToPixi", "GpuStateSystem", "gpuUploadBufferImageResource", "source", "gpuTexture", "total", "bytesPerPixel", "blockDataMap", "defaultBlockData", "gpuUploadCompressedTextureResource", "mip<PERSON><PERSON><PERSON>", "mipHeight", "blockData", "<PERSON><PERSON><PERSON><PERSON>", "bytesPerRow", "gpuUploadImageResource", "width", "height", "premultipliedAlpha", "gpuUploadVideoResource", "GpuMipmapGenerator", "format", "mipTexture", "arrayLayerCount", "renderToSource", "mipTextureDescriptor", "bindGroupLayout", "<PERSON><PERSON><PERSON><PERSON>", "srcView", "dstMipLevel", "dstView", "mipLevelSize", "GpuTextureSystem", "biggestDimension", "textureDescriptor", "UniformGroup", "canvas", "webGPUCanvas", "canvasAndContext", "CanvasPool", "imageData", "pixels", "k", "GpuGraphicsAdaptor", "localUniforms", "Matrix", "gpuProgram", "compileHighShaderGpuProgram", "colorBit", "generateTextureBatchBit", "getMaxTexturesPerBatch", "localUniformBitGroup2", "roundPixelsBit", "Shader", "graphicsPipe", "renderable", "contextSystem", "batcher", "instructions", "localBindGroup", "batches", "GpuMeshAdapter", "localUniformBit", "textureBit", "Texture", "meshPipe", "mesh", "DefaultWebGPUSystems", "SharedSystems", "DefaultWebGPUPipes", "SharedRenderPipes", "DefaultWebGPUAdapters", "systems", "renderPipes", "renderPipeAdaptors", "extensions", "WebGPUR<PERSON>er", "Abstract<PERSON><PERSON><PERSON>", "systemConfig", "RendererType"], "mappings": "m0DAKA,MAAMA,EAAYC,EAAM,QACxB,MAAMC,CAAgB,CACpB,MAAMC,EAAWC,EAAUC,EAAQ,CACjC,MAAMC,EAAWH,EAAU,SACrBI,EAAUD,EAAS,QACnBE,EAAUH,EAAO,WACvB,KAAK,QAAUA,EACf,KAAK,UAAYD,EACjBG,EAAQ,YAAYH,EAAUI,CAAO,EACrCR,EAAU,UAAY,SACtBM,EAAS,SAAS,YAChBF,EACAI,EACAR,CACN,EACI,MAAMS,EAA0BH,EAAS,eAAe,UACxDC,EAAQ,eAAe,CAAC,EACxBA,EAAQ,aAAa,EAAGE,EAAyBD,CAAO,CACzD,CACD,QAAQL,EAAWO,EAAO,CACxB,MAAMF,EAAU,KAAK,QAAQ,WACvBF,EAAWH,EAAU,SACrBI,EAAUD,EAAS,QACzB,GAAI,CAACI,EAAM,UAAW,CACpB,MAAMC,EAAeD,EAAM,SAC3BA,EAAM,UAAYE,EAAyBD,EAAa,SAAUA,EAAa,KAAK,CACrF,CACDX,EAAU,UAAYU,EAAM,UAC5B,MAAMG,EAAeP,EAAS,UAAU,aACtCI,EAAM,UACNF,EACA,CACN,EACUM,EAAWR,EAAS,SAAS,YACjC,KAAK,UACLE,EACAR,EACAU,EAAM,QACZ,EACIA,EAAM,UAAU,OAAOJ,EAAS,UAAU,KAAK,EAC/CC,EAAQ,YAAYO,CAAQ,EAC5BP,EAAQ,kBAAkB,aAAa,EAAGM,CAAY,EACtDN,EAAQ,kBAAkB,YAAYG,EAAM,KAAM,EAAGA,EAAM,KAAK,CACjE,CACH,CAEAR,EAAgB,UAAY,CAC1B,KAAM,CACJa,EAAc,kBACf,EACD,KAAM,OACR,ECrDA,MAAMC,CAAgB,CACpB,YAAYV,EAAU,CACpB,KAAK,MAAwB,OAAO,OAAO,IAAI,EAC/C,KAAK,UAAYA,EACjB,KAAK,UAAU,aAAa,eAAe,KAAM,OAAO,CACzD,CACD,cAAcW,EAAK,CACjB,KAAK,KAAOA,CACb,CACD,aAAaC,EAAWV,EAASW,EAAY,CAC3C,OAAAD,EAAU,WAAU,EACC,KAAK,MAAMA,EAAU,IAAI,GAAK,KAAK,iBAAiBA,EAAWV,EAASW,CAAU,CAExG,CACD,iBAAiBC,EAAOZ,EAASW,EAAY,CAC3C,MAAME,EAAS,KAAK,KAAK,OACnBC,EAAcd,EAAQ,OAAOW,CAAU,EACvCI,EAAU,CAAA,EACVjB,EAAW,KAAK,UACtB,UAAWkB,KAAKF,EAAa,CAC3B,MAAMG,EAAWL,EAAM,UAAUI,CAAC,GAAKJ,EAAM,UAAUE,EAAYE,CAAC,CAAC,EACrE,IAAIE,EACJ,GAAID,EAAS,gBAAkB,eAAgB,CAC7C,MAAME,EAAeF,EACrBnB,EAAS,IAAI,mBAAmBqB,CAAY,EAC5C,MAAMC,EAASD,EAAa,OAC5BD,EAAc,CACZ,OAAQpB,EAAS,OAAO,aAAasB,CAAM,EAC3C,OAAQ,EACR,KAAMA,EAAO,WAAW,IAClC,CACA,SAAiBH,EAAS,gBAAkB,SAAU,CAC9C,MAAMG,EAASH,EACfC,EAAc,CACZ,OAAQpB,EAAS,OAAO,aAAasB,CAAM,EAC3C,OAAQ,EACR,KAAMA,EAAO,WAAW,IAClC,CACA,SAAiBH,EAAS,gBAAkB,iBAAkB,CACtD,MAAMI,EAAiBJ,EACvBC,EAAc,CACZ,OAAQpB,EAAS,OAAO,aAAauB,EAAe,MAAM,EAC1D,OAAQA,EAAe,OACvB,KAAMA,EAAe,IAC/B,CACA,SAAiBJ,EAAS,gBAAkB,iBAAkB,CACtD,MAAMK,EAAUL,EAChBC,EAAcpB,EAAS,QAAQ,cAAcwB,CAAO,CAC5D,SAAiBL,EAAS,gBAAkB,gBAAiB,CACrD,MAAMM,EAAUN,EAChBC,EAAcpB,EAAS,QAAQ,aAAayB,CAAO,EAAE,WAAW,CAAA,CAAE,CACnE,CACDR,EAAQ,KAAK,CACX,QAASD,EAAYE,CAAC,EACtB,SAAUE,CAClB,CAAO,CACF,CACD,MAAMM,EAAS1B,EAAS,OAAO,eAAeE,CAAO,EAAE,WAAWW,CAAU,EACtEN,EAAeQ,EAAO,gBAAgB,CAC1C,OAAAW,EACA,QAAAT,CACN,CAAK,EACD,YAAK,MAAMH,EAAM,IAAI,EAAIP,EAClBA,CACR,CACD,SAAU,CACR,UAAWoB,KAAO,OAAO,KAAK,KAAK,KAAK,EACtC,KAAK,MAAMA,CAAG,EAAI,KAEpB,KAAK,MAAQ,KACb,KAAK,UAAY,IAClB,CACH,CAEAjB,EAAgB,UAAY,CAC1B,KAAM,CACJD,EAAc,YACf,EACD,KAAM,WACR,EC9EA,MAAMmB,CAAgB,CACpB,YAAY5B,EAAU,CACpB,KAAK,YAA8B,OAAO,OAAO,IAAI,EACrD,KAAK,gBAAkB,GACvBA,EAAS,aAAa,eAAe,KAAM,aAAa,CACzD,CACD,cAAcW,EAAK,CACjB,KAAK,KAAOA,CACb,CACD,aAAaW,EAAQ,CACnB,OAAO,KAAK,YAAYA,EAAO,GAAG,GAAK,KAAK,gBAAgBA,CAAM,CACnE,CACD,aAAaA,EAAQ,CACnB,MAAMO,EAAY,KAAK,YAAYP,EAAO,GAAG,GAAK,KAAK,gBAAgBA,CAAM,EACvEQ,EAAOR,EAAO,KACpB,OAAIA,EAAO,WAAaQ,IACtBR,EAAO,UAAY,EACnB,KAAK,KAAK,OAAO,MAAM,YACrBO,EACA,EACAC,EAAK,OACL,GAECR,EAAO,aAAeQ,EAAK,YAAc,EAAI,EACtD,GAEWD,CACR,CAED,YAAa,CACX,UAAWE,KAAM,KAAK,YACpB,KAAK,YAAYA,CAAE,EAAE,QAAO,EAE9B,KAAK,YAAc,EACpB,CACD,gBAAgBT,EAAQ,CACjB,KAAK,YAAYA,EAAO,GAAG,IAC9BA,EAAO,GAAG,SAAU,KAAK,aAAc,IAAI,EAC3CA,EAAO,GAAG,SAAU,KAAK,eAAgB,IAAI,EAC7CA,EAAO,GAAG,UAAW,KAAK,gBAAiB,IAAI,EAC/C,KAAK,gBAAgB,KAAKA,CAAM,GAElC,MAAMO,EAAY,KAAK,KAAK,OAAO,aAAaP,EAAO,UAAU,EACjE,OAAAA,EAAO,UAAY,EACfA,EAAO,OACTU,GAASV,EAAO,KAAK,OAAQO,EAAU,eAAc,CAAE,EACvDA,EAAU,MAAK,GAEjB,KAAK,YAAYP,EAAO,GAAG,EAAIO,EACxBA,CACR,CACD,eAAeP,EAAQ,CACH,KAAK,YAAYA,EAAO,GAAG,EACnC,QAAO,EACjBA,EAAO,UAAY,EACnB,KAAK,YAAYA,EAAO,GAAG,EAAI,KAAK,gBAAgBA,CAAM,CAC3D,CAKD,gBAAgBA,EAAQ,CACtB,KAAK,gBAAgB,OAAO,KAAK,gBAAgB,QAAQA,CAAM,EAAG,CAAC,EACnE,KAAK,eAAeA,CAAM,CAC3B,CACD,SAAU,CACR,KAAK,gBAAgB,QAASA,GAAW,KAAK,eAAeA,CAAM,CAAC,EACpE,KAAK,gBAAkB,KACvB,KAAK,YAAc,IACpB,CACD,eAAeA,EAAQ,CACH,KAAK,YAAYA,EAAO,GAAG,EACnC,QAAO,EACjBA,EAAO,IAAI,SAAU,KAAK,aAAc,IAAI,EAC5CA,EAAO,IAAI,SAAU,KAAK,eAAgB,IAAI,EAC9CA,EAAO,IAAI,UAAW,KAAK,gBAAiB,IAAI,EAChD,KAAK,YAAYA,EAAO,GAAG,EAAI,IAChC,CACH,CAEAM,EAAgB,UAAY,CAC1B,KAAM,CACJnB,EAAc,YACf,EACD,KAAM,QACR,ECxFA,MAAMwB,EAAS,CACb,YAAY,CAAE,0BAAAC,GAA6B,CACzC,KAAK,2BAA6B,IAClC,KAAK,UAAY,EACjB,KAAK,2BAA6BA,EAClC,KAAK,KAAO,IAAI,aAAa,KAAK,CACnC,CACD,OAAQ,CACN,KAAK,UAAY,CAClB,CACD,cAAcC,EAAM,CAClB,GAAIA,EAAO,KAAK,2BAA6B,EAC3C,MAAM,IAAI,MAAM,2CAA2CA,EAAO,CAAC,EAAE,EAEvE,MAAMC,EAAQ,KAAK,UACnB,IAAIC,EAAUD,EAAQD,EAAO,EAE7B,GADAE,EAAU,KAAK,KAAKA,EAAU,KAAK,0BAA0B,EAAI,KAAK,2BAClEA,EAAU,KAAK,KAAK,OAAS,EAC/B,MAAM,IAAI,MAAM,2CAA2C,EAE7D,YAAK,UAAYA,EACVD,CACR,CACD,SAASE,EAAO,CACd,MAAMC,EAAS,KAAK,cAAcD,EAAM,MAAM,EAC9C,QAASE,EAAI,EAAGA,EAAIF,EAAM,OAAQE,IAChC,KAAK,KAAKD,EAAS,EAAIC,CAAC,EAAIF,EAAME,CAAC,EAErC,OAAOD,CACR,CACD,SAAU,CACR,KAAK,KAAO,IACb,CACH,CC/BA,MAAME,CAAmB,CACvB,YAAYzC,EAAU,CACpB,KAAK,gBAAkB,GACvB,KAAK,UAAYA,CAClB,CACD,QAAQ0C,EAAW,CACb,KAAK,kBAAoBA,IAE7B,KAAK,gBAAkBA,EACvB,KAAK,UAAU,SAAS,aAAaA,CAAS,EAC/C,CACD,SAAU,CACR,KAAK,UAAY,KACjB,KAAK,gBAAkB,IACxB,CACH,CAEAD,EAAmB,UAAY,CAC7B,KAAM,CACJhC,EAAc,YACf,EACD,KAAM,WACR,ECrBA,MAAMkC,CAAgB,CAIpB,YAAY3C,EAAU,CACpB,KAAK,UAAYA,CAClB,CACD,MAAM,KAAK4C,EAAS,CAClB,OAAI,KAAK,aACA,KAAK,cACd,KAAK,aAAe,KAAK,wBAAwBA,CAAO,EAAE,KAAMjC,GAAQ,CACtE,KAAK,IAAMA,EACX,KAAK,UAAU,QAAQ,cAAc,KAAK,KAAK,GAAG,CACxD,CAAK,EACM,KAAK,aACb,CAKD,cAAcA,EAAK,CACjB,KAAK,UAAU,IAAMA,CACtB,CAQD,MAAM,wBAAwBiC,EAAS,CACrC,MAAMC,EAAU,MAAMC,EAAW,IAAK,EAAC,aAAc,EAAC,IAAI,eAAe,CACvE,gBAAiBF,EAAQ,gBACzB,qBAAsBA,EAAQ,oBACpC,CAAK,EACKG,EAAmB,CACvB,yBACA,2BACA,0BACN,EAAM,OAAQC,GAAYH,EAAQ,SAAS,IAAIG,CAAO,CAAC,EAC7CjC,EAAS,MAAM8B,EAAQ,cAAc,CACzC,iBAAAE,CACN,CAAK,EACD,MAAO,CAAE,QAAAF,EAAS,OAAA9B,EACnB,CACD,SAAU,CACR,KAAK,IAAM,KACX,KAAK,UAAY,IAClB,CACH,CAEA4B,EAAgB,UAAY,CAC1B,KAAM,CACJlC,EAAc,YACf,EACD,KAAM,QACR,EAEAkC,EAAgB,eAAiB,CAK/B,gBAAiB,OAKjB,qBAAsB,EACxB,ECtEA,MAAMM,CAAiB,CACrB,YAAYjD,EAAU,CACpB,KAAK,gBAAkC,OAAO,OAAO,IAAI,EACzD,KAAK,mBAAqC,OAAO,OAAO,IAAI,EAC5D,KAAK,UAAYA,CAClB,CACD,aAAc,CACZ,KAAK,gBAAkB,IAAI,QAASkD,GAAY,CAC9C,KAAK,wBAA0BA,CACrC,CAAK,EACD,KAAK,eAAiB,KAAK,UAAU,IAAI,OAAO,sBACjD,CACD,gBAAgBC,EAAiB,CAC/B,KAAK,cAAa,EAClB,KAAK,YAAW,EAChB,KAAK,kBAAoB,KAAK,eAAe,gBAAgBA,EAAgB,UAAU,CACxF,CACD,eAAgB,CACV,KAAK,mBACP,KAAK,kBAAkB,MAEzB,KAAK,kBAAoB,IAC1B,CACD,YAAYC,EAAU,CACpB,KAAK,kBAAkB,YAAYA,EAAS,EAAGA,EAAS,EAAGA,EAAS,MAAOA,EAAS,OAAQ,EAAG,CAAC,CACjG,CACD,uCAAuCtD,EAAUI,EAASmD,EAAOC,EAAU,CACzE,MAAM9C,EAAW,KAAK,UAAU,SAAS,YAAYV,EAAUI,EAASmD,EAAOC,CAAQ,EACvF,KAAK,YAAY9C,CAAQ,CAC1B,CACD,YAAYA,EAAU,CAChB,KAAK,iBAAmBA,IAE5B,KAAK,eAAiBA,EACtB,KAAK,kBAAkB,YAAYA,CAAQ,EAC5C,CACD,iBAAiB+C,EAAOjC,EAAQ,CAC1B,KAAK,mBAAmBiC,CAAK,IAAMjC,IAEvC,KAAK,mBAAmBiC,CAAK,EAAIjC,EACjC,KAAK,kBAAkB,gBAAgBiC,EAAO,KAAK,UAAU,OAAO,aAAajC,CAAM,CAAC,EACzF,CACD,gBAAgBA,EAAQ,CACtB,GAAI,KAAK,oBAAsBA,EAC7B,OACF,KAAK,kBAAoBA,EACzB,MAAMkC,EAAclC,EAAO,KAAK,oBAAsB,EAAI,SAAW,SACrE,KAAK,kBAAkB,eAAe,KAAK,UAAU,OAAO,aAAaA,CAAM,EAAGkC,CAAW,CAC9F,CACD,eAAeD,EAAO,CACpB,KAAK,gBAAgBA,CAAK,EAAI,IAC/B,CACD,aAAaA,EAAO3C,EAAWV,EAAS,CACtC,GAAI,KAAK,gBAAgBqD,CAAK,IAAM3C,EAClC,OACF,KAAK,gBAAgB2C,CAAK,EAAI3C,EAC9BA,EAAU,OAAO,KAAK,UAAU,UAAU,KAAK,EAC/C,MAAML,EAAe,KAAK,UAAU,UAAU,aAAaK,EAAWV,EAASqD,CAAK,EACpF,KAAK,kBAAkB,aAAaA,EAAOhD,CAAY,CACxD,CACD,YAAYT,EAAUI,EAAS,CAC7B,MAAMuD,EAAgB,KAAK,UAAU,SAAS,qBAAqB3D,EAAUI,CAAO,EACpF,UAAWsC,KAAKiB,EACd,KAAK,iBAAiBjB,EAAG1C,EAAS,WAAW2D,EAAcjB,CAAC,CAAC,EAAE,MAAM,EAEnE1C,EAAS,aACX,KAAK,gBAAgBA,EAAS,WAAW,CAE5C,CACD,qBAAqBC,EAAQ2D,EAAU,CACrC,UAAWlB,KAAKzC,EAAO,OAAQ,CAC7B,MAAMa,EAAYb,EAAO,OAAOyC,CAAC,EAC5BkB,GACH,KAAK,eAAe9C,CAAS,EAE/B,KAAK,aAAa4B,EAAG5B,EAAWb,EAAO,UAAU,CAClD,CACF,CACD,eAAea,EAAW,CACxB,UAAWM,KAAKN,EAAU,UAAW,CACnC,MAAMO,EAAWP,EAAU,UAAUM,CAAC,EAClCC,EAAS,gBACX,KAAK,UAAU,IAAI,mBAAmBA,CAAQ,CAEjD,CACF,CACD,KAAKyB,EAAS,CACZ,KAAM,CAAE,SAAA9C,EAAU,OAAAC,EAAQ,MAAAsD,EAAO,SAAAC,EAAU,KAAAnB,EAAM,MAAAC,EAAO,cAAAuB,EAAe,SAAAD,CAAU,EAAGd,EACpF,KAAK,uCAAuC9C,EAAUC,EAAO,WAAYsD,EAAOC,CAAQ,EACxF,KAAK,YAAYxD,EAAUC,EAAO,UAAU,EAC5C,KAAK,qBAAqBA,EAAQ2D,CAAQ,EACtC5D,EAAS,YACX,KAAK,kBAAkB,YACrBqC,GAAQrC,EAAS,YAAY,KAAK,OAClC6D,GAAiB7D,EAAS,cAC1BsC,GAAS,CACjB,EAEM,KAAK,kBAAkB,KAAKD,GAAQrC,EAAS,UAAW6D,GAAiB7D,EAAS,cAAesC,GAAS,CAAC,CAE9G,CACD,kBAAmB,CACb,KAAK,oBACP,KAAK,kBAAkB,MACvB,KAAK,kBAAoB,KAE5B,CACD,YAAa,CACX,KAAK,iBAAgB,EACrB,KAAK,KAAK,OAAO,MAAM,OAAO,CAAC,KAAK,eAAe,OAAQ,CAAA,CAAC,EAC5D,KAAK,wBAAuB,EAC5B,KAAK,eAAiB,IACvB,CAID,mBAAoB,CAClB,MAAMwB,EAAa,KAAK,UAAU,aAAa,QAAQ,cACrD,KAAK,UAAU,aAAa,aAC5B,GACA,CAAC,EAAG,EAAG,EAAG,CAAC,CACjB,EACI,KAAK,kBAAoB,KAAK,eAAe,gBAAgBA,CAAU,EACvE,MAAMC,EAAgB,KAAK,eACrBC,EAAoB,CAAE,GAAG,KAAK,kBAAkB,EAChDC,EAAmB,KAAK,kBACxBC,EAAiB,CAAE,GAAG,KAAK,eAAe,EAChD,KAAK,YAAW,EAChB,MAAMZ,EAAW,KAAK,UAAU,aAAa,SAC7C,KAAK,kBAAkB,YAAYA,EAAS,EAAGA,EAAS,EAAGA,EAAS,MAAOA,EAAS,OAAQ,EAAG,CAAC,EAChG,KAAK,YAAYS,CAAa,EAC9B,UAAWrB,KAAKsB,EACd,KAAK,iBAAiBtB,EAAGsB,EAAkBtB,CAAC,CAAC,EAE/C,UAAWA,KAAKwB,EACd,KAAK,aAAaxB,EAAGwB,EAAexB,CAAC,EAAG,IAAI,EAE9C,KAAK,gBAAgBuB,CAAgB,CACtC,CACD,aAAc,CACZ,QAASvB,EAAI,EAAGA,EAAI,GAAIA,IACtB,KAAK,gBAAgBA,CAAC,EAAI,KAC1B,KAAK,mBAAmBA,CAAC,EAAI,KAE/B,KAAK,kBAAoB,KACzB,KAAK,eAAiB,IACvB,CACD,SAAU,CACR,KAAK,UAAY,KACjB,KAAK,KAAO,KACZ,KAAK,gBAAkB,KACvB,KAAK,mBAAqB,KAC1B,KAAK,kBAAoB,KACzB,KAAK,eAAiB,IACvB,CACD,cAAc7B,EAAK,CACjB,KAAK,KAAOA,CACb,CACH,CAEAsC,EAAiB,UAAY,CAC3B,KAAM,CAACxC,EAAc,YAAY,EACjC,KAAM,UACN,SAAU,CACZ,ECnKA,MAAMwD,CAAiB,CACrB,YAAYjE,EAAU,CACpB,KAAK,0BAA4C,OAAO,OAAO,IAAI,EACnE,KAAK,UAAYA,EACjBA,EAAS,aAAa,qBAAqB,IAAI,IAAI,CACpD,CACD,qBAAqBkE,EAAc,CACjC,IAAIC,EAAe,KAAK,0BAA0BD,EAAa,GAAG,EAC7DC,IACHA,EAAe,KAAK,0BAA0BD,EAAa,GAAG,EAAI,CAChE,YAAaE,EAAc,SAC3B,iBAAkB,CAC1B,GAEI,KAAK,oBAAsBF,EAC3B,KAAK,eAAeC,EAAa,YAAaA,EAAa,gBAAgB,CAC5E,CACD,eAAeE,EAAaC,EAAkB,CAC5C,MAAMH,EAAe,KAAK,0BAA0B,KAAK,oBAAoB,GAAG,EAChFA,EAAa,YAAcE,EAC3BF,EAAa,iBAAmBG,EAChC,MAAMtE,EAAW,KAAK,UACtBA,EAAS,SAAS,eAAeqE,CAAW,EAC5CrE,EAAS,QAAQ,kBAAkB,oBAAoBsE,CAAgB,CACxE,CACD,SAAU,CACR,KAAK,UAAU,aAAa,qBAAqB,OAAO,IAAI,EAC5D,KAAK,UAAY,KACjB,KAAK,oBAAsB,KAC3B,KAAK,0BAA4B,IAClC,CACH,CAEAL,EAAiB,UAAY,CAC3B,KAAM,CACJxD,EAAc,YACf,EACD,KAAM,SACR,ECzCA,MAAM8D,EAAuB,CAC3B,IAAK,CAAE,MAAO,EAAG,KAAM,CAAG,EAC1B,IAAK,CAAE,MAAO,EAAG,KAAM,CAAG,EAC1B,IAAK,CAAE,MAAO,EAAG,KAAM,CAAG,EAC1B,IAAK,CAAE,MAAO,EAAG,KAAM,CAAG,EAC1B,YAAa,CAAE,MAAO,EAAG,KAAM,CAAG,EAClC,YAAa,CAAE,MAAO,EAAG,KAAM,CAAG,EAClC,YAAa,CAAE,MAAO,EAAG,KAAM,CAAG,EAClC,YAAa,CAAE,MAAO,EAAG,KAAM,CAAG,EAClC,YAAa,CAAE,MAAO,GAAI,KAAM,EAAI,EACpC,YAAa,CAAE,MAAO,GAAI,KAAM,EAAI,EACpC,YAAa,CAAE,MAAO,GAAI,KAAM,EAAI,EACpC,YAAa,CAAE,MAAO,EAAG,KAAM,CAAG,EAClC,YAAa,CAAE,MAAO,GAAI,KAAM,EAAI,EACpC,YAAa,CAAE,MAAO,GAAI,KAAM,EAAI,EACpC,YAAa,CAAE,MAAO,GAAI,KAAM,EAAI,EACpC,YAAa,CAAE,MAAO,EAAG,KAAM,CAAG,EAClC,cAAe,CAAE,MAAO,EAAG,KAAM,EAAI,EACrC,cAAe,CAAE,MAAO,EAAG,KAAM,CAAG,EACpC,cAAe,CAAE,MAAO,EAAG,KAAM,EAAI,EACrC,cAAe,CAAE,MAAO,EAAG,KAAM,EAAI,EACrC,cAAe,CAAE,MAAO,EAAG,KAAM,EAAI,EACrC,cAAe,CAAE,MAAO,EAAG,KAAM,EAAI,EACrC,cAAe,CAAE,MAAO,GAAI,KAAM,EAAI,EACtC,cAAe,CAAE,MAAO,EAAG,KAAM,EAAI,EACrC,cAAe,CAAE,MAAO,GAAI,KAAM,EAAI,EACtC,cAAe,CAAE,MAAO,EAAG,KAAM,EAAI,EACrC,cAAe,CAAE,MAAO,GAAI,KAAM,EAAI,EACtC,cAAe,CAAE,MAAO,EAAG,KAAM,EAAI,EACrC,cAAe,CAAE,MAAO,GAAI,KAAM,EAAI,EACtC,cAAe,CAAE,MAAO,EAAG,KAAM,EAAI,EACrC,cAAe,CAAE,MAAO,GAAI,KAAM,EAAI,EACtC,cAAe,CAAE,MAAO,EAAG,KAAM,EAAI,EACrC,cAAe,CAAE,MAAO,GAAI,KAAM,EAAI,EACtC,cAAe,CAAE,MAAO,EAAG,KAAM,EAAI,CACvC,EACA,SAASC,GAAsBC,EAAa,CAC1C,MAAMC,EAAcD,EAAY,IAAK3C,IAAU,CAC7C,KAAAA,EACA,OAAQ,EACR,KAAM,CACP,EAAC,EACF,IAAIS,EAAS,EACb,QAASC,EAAI,EAAGA,EAAIkC,EAAY,OAAQlC,IAAK,CAC3C,MAAMmC,EAAaD,EAAYlC,CAAC,EAChC,IAAIL,EAAOoC,EAAqBI,EAAW,KAAK,IAAI,EAAE,KACtD,MAAMC,EAAQL,EAAqBI,EAAW,KAAK,IAAI,EAAE,MACzD,GAAI,CAACJ,EAAqBI,EAAW,KAAK,IAAI,EAC5C,MAAM,IAAI,MAAM,gDAAgDA,EAAW,KAAK,IAAI,EAAE,EAEpFA,EAAW,KAAK,KAAO,IACzBxC,EAAO,KAAK,IAAIA,EAAMyC,CAAK,EAAID,EAAW,KAAK,MAEjDpC,EAAS,KAAK,KAAKA,EAASqC,CAAK,EAAIA,EACrCD,EAAW,KAAOxC,EAClBwC,EAAW,OAASpC,EACpBA,GAAUJ,CACX,CACD,OAAAI,EAAS,KAAK,KAAKA,EAAS,EAAE,EAAI,GAC3B,CAAE,YAAAmC,EAAa,KAAMnC,EAC9B,CC1DA,SAASsC,GAAsBF,EAAYG,EAAa,CACtD,KAAM,CAAE,KAAA3C,EAAM,MAAAyC,CAAO,EAAGL,EAAqBI,EAAW,KAAK,IAAI,EAC3DI,GAAaH,EAAQzC,GAAQ,EAC7BL,EAAO6C,EAAW,KAAK,KAAK,QAAQ,KAAK,GAAK,EAAI,YAAc,OACtE,MAAO;AAAA,kBACSA,EAAW,KAAK,IAAI;AAAA,WAC3BG,IAAgB,EAAI,aAAaA,CAAW,IAAM,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,4BAMnCH,EAAW,KAAK,MAAQxC,EAAO,EAAE;AAAA;AAAA,kCAE3BA,EAAO,CAAC;AAAA;AAAA,mBAEvBL,CAAI;AAAA;AAAA,eAERiD,IAAc,EAAI,kBAAkBA,CAAS,IAAM,EAAE;AAAA;AAAA,MAGpE,CCnBA,SAASC,GAA0BN,EAAa,CAC9C,OAAOO,GACLP,EACA,UACAG,GACAK,EACJ,CACA,CCNA,MAAMC,UAAqBC,EAAU,CACnC,aAAc,CACZ,MAAM,CACJ,kBAAmBZ,GACnB,gBAAiBQ,EACvB,CAAK,CACF,CACH,CAEAG,EAAa,UAAY,CACvB,KAAM,CAAC1E,EAAc,YAAY,EACjC,KAAM,KACR,ECVA,MAAMyB,EAA4B,IAClC,MAAMmD,CAAoB,CACxB,YAAYrF,EAAU,CACpB,KAAK,eAAiC,OAAO,OAAO,IAAI,EAExD,KAAK,SAAW,GAChB,KAAK,YAAc,GACnB,KAAK,iBAAmB,GACxB,KAAK,UAAYA,EACjB,KAAK,UAAU,aAAa,eAAe,KAAM,gBAAgB,EACjE,KAAK,aAAe,IAAIiC,GAAS,CAAE,0BAAAC,CAA2B,CAAA,EAC9D,MAAMoD,EAAe,IAAMpD,EAC3B,QAASM,EAAI,EAAGA,EAAI8C,EAAc9C,IAAK,CACrC,IAAI+C,EAAQC,EAAY,QAAUA,EAAY,SAC1ChD,IAAM,IACR+C,GAASC,EAAY,UACvB,KAAK,SAAS,KAAK,IAAIC,GAAO,CAC5B,KAAM,KAAK,aAAa,KACxB,MAAAF,CACD,CAAA,CAAC,CACH,CACF,CACD,WAAY,CACV,KAAK,kBAAiB,EACtB,KAAK,iBAAgB,CACtB,CACD,kBAAmB,CACjB,UAAW/C,KAAK,KAAK,eACnB,KAAK,eAAeA,CAAC,EAAI,KAE3B,KAAK,aAAa,OACnB,CAED,oBAAoB1B,EAAO4E,EAAW,CACpC,GAAI,CAACA,GAAa,KAAK,eAAe5E,EAAM,GAAG,EAC7C,OAAO,KAAK,eAAeA,EAAM,GAAG,EAEtC,KAAK,UAAU,IAAI,mBAAmBA,CAAK,EAC3C,MAAMgB,EAAOhB,EAAM,OAAO,KACpByB,EAAS,KAAK,aAAa,cAAcT,EAAK,MAAM,EAC1D,YAAK,UAAU,IAAI,iBAAiBhB,EAAO,KAAK,aAAa,KAAMyB,EAAS,CAAC,EAC7E,KAAK,eAAezB,EAAM,GAAG,EAAI,KAAK,cAAcyB,EAASL,CAAyB,EAC/E,KAAK,eAAepB,EAAM,GAAG,CACrC,CACD,eAAeA,EAAO,CACpB,KAAK,UAAU,IAAI,mBAAmBA,CAAK,EAC3C,MAAMgB,EAAOhB,EAAM,OAAO,KACpByB,EAAS,KAAK,aAAa,SAAST,CAAI,EAC9C,OAAO,KAAK,mBAAmBS,EAASL,CAAyB,CAClE,CACD,kBAAkBJ,EAAM,CACtB,MAAMS,EAAS,KAAK,aAAa,SAAST,CAAI,EAC9C,OAAO,KAAK,cAAcS,EAASL,CAAyB,CAC7D,CACD,uBAAuBJ,EAAM,CAE3B,MAAMyB,EADS,KAAK,aAAa,SAASzB,CAAI,EACvBI,EACvB,OAAO,KAAK,mBAAmBqB,CAAK,CACrC,CACD,mBAAmBA,EAAO,CACxB,GAAI,CAAC,KAAK,iBAAiBA,CAAK,EAAG,CACjC,MAAMjC,EAAS,KAAK,SAASiC,EAAQ,CAAC,EACtC,KAAK,iBAAiBA,CAAK,EAAI,IAAIoC,GAAe,CAChD,OAAArE,EACA,QAASiC,EAAQ,EAAI,GAAK,IAC1B,KAAMrB,CACd,CAAO,CACF,CACD,OAAO,KAAK,iBAAiBqB,CAAK,CACnC,CACD,cAAcA,EAAO,CACnB,GAAI,CAAC,KAAK,YAAYA,CAAK,EAAG,CAC5B,MAAM3C,EAAY,IAAIgF,EAAU,CAC9B,EAAG,KAAK,mBAAmBrC,CAAK,CACxC,CAAO,EACD,KAAK,YAAYA,CAAK,EAAI3C,CAC3B,CACD,OAAO,KAAK,YAAY2C,CAAK,CAC9B,CACD,mBAAoB,CAClB,MAAMsC,EAAe,KAAK,UAAU,OAC9BC,EAAc,KAAK,SAAS,CAAC,EACnCA,EAAY,OAAO,KAAK,aAAa,SAAS,EAC9CD,EAAa,aAAaC,CAAW,EACrC,MAAMC,EAAiB,KAAK,UAAU,IAAI,OAAO,uBACjD,QAASvD,EAAI,EAAGA,EAAI,KAAK,SAAS,OAAQA,IAAK,CAC7C,MAAMlB,EAAS,KAAK,SAASkB,CAAC,EAC9BuD,EAAe,mBACbF,EAAa,aAAaC,CAAW,EACrC5D,EACA2D,EAAa,aAAavE,CAAM,EAChC,EACA,KAAK,aAAa,SAC1B,CACK,CACD,KAAK,UAAU,IAAI,OAAO,MAAM,OAAO,CAACyE,EAAe,OAAQ,CAAA,CAAC,CACjE,CACD,SAAU,CACR,QAASvD,EAAI,EAAGA,EAAI,KAAK,YAAY,OAAQA,IAC3C,KAAK,YAAYA,CAAC,EAAE,QAAO,EAE7B,KAAK,YAAc,KACnB,KAAK,eAAiB,KACtB,QAASA,EAAI,EAAGA,EAAI,KAAK,SAAS,OAAQA,IACxC,KAAK,SAASA,CAAC,EAAE,QAAO,EAE1B,KAAK,SAAW,KAChB,QAASA,EAAI,EAAGA,EAAI,KAAK,iBAAiB,OAAQA,IAChD,KAAK,iBAAiBA,CAAC,EAAE,QAAO,EAElC,KAAK,iBAAmB,KACxB,KAAK,aAAa,UAClB,KAAK,eAAiB,KACtB,KAAK,UAAY,IAClB,CACH,CAEA6C,EAAoB,UAAY,CAC9B,KAAM,CACJ5E,EAAc,WACf,EACD,KAAM,cACR,EC1HA,MAAMuF,GAAqB,CACzB,aAAc,EACd,YAAa,EACb,aAAc,EACd,gBAAiB,EACjB,iBAAkB,CACpB,EACA,SAASC,GAAoBC,EAAgBC,EAAW9C,EAAO+C,EAAW9C,EAAU,CAClF,OAAO4C,GAAkB,GAAKC,GAAa,GAAK9C,GAAS,GAAK+C,GAAa,EAAI9C,CACjF,CACA,SAAS+C,GAAkBC,EAAgBC,EAAkB7D,EAAWwB,EAAc,CACpF,OAAOxB,GAAa,EAAI4D,GAAkB,EAAIpC,GAAgB,EAAIqC,CACpE,CACA,MAAMC,CAAe,CACnB,YAAYxG,EAAU,CACpB,KAAK,aAA+B,OAAO,OAAO,IAAI,EACtD,KAAK,oBAAsC,OAAO,OAAO,IAAI,EAC7D,KAAK,mBAAqC,OAAO,OAAO,IAAI,EAC5D,KAAK,WAA6B,OAAO,OAAO,IAAI,EACpD,KAAK,iBAAmC,OAAO,OAAO,IAAI,EAC1D,KAAK,WAAa,GAClB,KAAK,kBAAoB,EACzB,KAAK,UAAYA,CAClB,CACD,cAAcW,EAAK,CACjB,KAAK,KAAOA,EACZ,KAAK,eAAeyD,EAAc,QAAQ,EAC1C,KAAK,gBAAe,CACrB,CACD,oBAAoBqC,EAAkB,CAChC,KAAK,oBAAsBA,IAE/B,KAAK,kBAAoBA,EACzB,KAAK,gBAAe,EACrB,CACD,gBAAgBvC,EAAc,CAC5B,KAAK,kBAAoBA,EAAa,YACtC,KAAK,wBAA0BA,EAAa,WAAW,uBAAyB,EAAI,EACpF,KAAK,gBAAe,CACrB,CACD,aAAaxB,EAAW,CAClB,KAAK,aAAeA,IAExB,KAAK,WAAaA,EAClB,KAAK,gBAAe,EACrB,CACD,eAAe2B,EAAa,CACtB,KAAK,eAAiBA,IAE1B,KAAK,aAAeA,EACpB,KAAK,cAAgBqC,GAAsBrC,CAAW,EACtD,KAAK,gBAAe,EACrB,CACD,YAAYvE,EAAUI,EAASmD,EAAOsD,EAAa,CACjD,MAAMnG,EAAW,KAAK,YAAYV,EAAUI,EAASmD,CAAK,EAC1DsD,EAAY,YAAYnG,CAAQ,CACjC,CACD,YAAYV,EAAUI,EAASmD,EAAOC,EAAU,CACzCxD,EAAS,aACZ8G,GAAiB9G,EAAUI,EAAQ,aAAa,EAChD,KAAK,mBAAmBJ,CAAQ,GAElCwD,IAAaA,EAAWxD,EAAS,UACjC,MAAM6B,EAAMsE,GACVnG,EAAS,WACTI,EAAQ,WACRmD,EAAM,KACNA,EAAM,aACN2C,GAAmB1C,CAAQ,CACjC,EACI,OAAI,KAAK,WAAW3B,CAAG,EACd,KAAK,WAAWA,CAAG,GAC5B,KAAK,WAAWA,CAAG,EAAI,KAAK,gBAAgB7B,EAAUI,EAASmD,EAAOC,CAAQ,EACvE,KAAK,WAAW3B,CAAG,EAC3B,CACD,gBAAgB7B,EAAUI,EAASmD,EAAOC,EAAU,CAClD,MAAMvC,EAAS,KAAK,KAAK,OACnB8F,EAAU,KAAK,2BAA2B/G,EAAUI,CAAO,EAC3D4G,EAAa,KAAK,UAAU,MAAM,gBAAgBzD,CAAK,EAC7DyD,EAAW,CAAC,EAAE,UAAY,KAAK,eAAiB1C,EAAc,mBAAqB,EAAI,KAAK,WAC5F,MAAM1C,EAAS,KAAK,UAAU,OAAO,eAAexB,CAAO,EAAE,SACvD0D,EAAa,CAGjB,OAAQ,CACN,OAAQ,KAAK,WAAW1D,EAAQ,OAAO,MAAM,EAC7C,WAAYA,EAAQ,OAAO,WAE3B,QAAA2G,CACD,EACD,SAAU,CACR,OAAQ,KAAK,WAAW3G,EAAQ,SAAS,MAAM,EAC/C,WAAYA,EAAQ,SAAS,WAC7B,QAAS4G,CACV,EACD,UAAW,CACT,SAAAxD,EACA,SAAUD,EAAM,QACjB,EACD,OAAA3B,EACA,YAAa,CACX,MAAO,KAAK,iBACb,EAED,MAAO,eACb,EACI,OAAI,KAAK,0BACPkC,EAAW,aAAe,CACxB,GAAG,KAAK,cACR,OAAQ,uBACR,kBAAmBP,EAAM,UACzB,aAAcA,EAAM,UAAY,OAAS,QACjD,GAEqBtC,EAAO,qBAAqB6C,CAAU,CAExD,CACD,WAAWmD,EAAM,CACf,OAAO,KAAK,aAAaA,CAAI,GAAK,KAAK,cAAcA,CAAI,CAC1D,CACD,cAAcA,EAAM,CAClB,MAAMhG,EAAS,KAAK,KAAK,OACzB,YAAK,aAAagG,CAAI,EAAIhG,EAAO,mBAAmB,CAClD,KAAAgG,CACN,CAAK,EACM,KAAK,aAAaA,CAAI,CAC9B,CACD,mBAAmBjH,EAAU,CAC3B,MAAMkH,EAAS,CAAA,EACf,IAAIzD,EAAQ,EACZ,MAAM0D,EAAgB,OAAO,KAAKnH,EAAS,UAAU,EAAE,OACvD,QAAS0C,EAAI,EAAGA,EAAIyE,EAAc,OAAQzE,IAAK,CAC7C,MAAM0E,EAAYpH,EAAS,WAAWmH,EAAczE,CAAC,CAAC,EACtDwE,EAAOzD,GAAO,EAAI2D,EAAU,OAC5BF,EAAOzD,GAAO,EAAI2D,EAAU,OAC5BF,EAAOzD,GAAO,EAAI2D,EAAU,OAC5BF,EAAOzD,GAAO,EAAI2D,EAAU,QAC7B,CACD,MAAMC,EAAYH,EAAO,KAAK,GAAG,EACjC,OAAAlH,EAAS,WAAasH,EAAmBD,EAAW,UAAU,EACvDrH,EAAS,UACjB,CACD,+BAA+BI,EAAS,CACtC,MAAM8G,EAAS,CAAA,EACf,IAAIzD,EAAQ,EACZ,MAAM0D,EAAgB,OAAO,KAAK/G,EAAQ,aAAa,EAAE,OACzD,QAASsC,EAAI,EAAGA,EAAIyE,EAAc,OAAQzE,IAAK,CAC7C,MAAM0E,EAAYhH,EAAQ,cAAc+G,EAAczE,CAAC,CAAC,EACxDwE,EAAOzD,GAAO,EAAI2D,EAAU,QAC7B,CACD,MAAMC,EAAYH,EAAO,KAAK,GAAG,EACjC,OAAA9G,EAAQ,uBAAyBkH,EAAmBD,EAAW,mBAAmB,EAC3EjH,EAAQ,sBAChB,CAQD,qBAAqBJ,EAAUI,EAAS,CACtC,MAAMyB,EAAM7B,EAAS,YAAc,GAAKI,EAAQ,uBAChD,GAAI,KAAK,mBAAmByB,CAAG,EAC7B,OAAO,KAAK,mBAAmBA,CAAG,EACpC,MAAMG,EAAO,KAAK,2BAA2BhC,EAAUI,CAAO,EACxDmH,EAAoC,OAAO,OAAO,IAAI,EACtDC,EAAgBpH,EAAQ,cAC9B,QAASsC,EAAI,EAAGA,EAAIV,EAAK,OAAQU,IAAK,CAEpC,MAAM+E,EADa,OAAO,OAAOzF,EAAKU,CAAC,EAAE,UAAU,EACjB,CAAC,EAAE,eACrC,UAAWtB,KAAKoG,EACd,GAAIA,EAAcpG,CAAC,EAAE,WAAaqG,EAAgB,CAChDF,EAAkB7E,CAAC,EAAItB,EACvB,KACD,CAEJ,CACD,YAAK,mBAAmBS,CAAG,EAAI0F,EACxBA,CACR,CACD,2BAA2BvH,EAAUI,EAAS,CACvCA,EAAQ,wBACX,KAAK,+BAA+BA,CAAO,EAC7C,MAAMyB,EAAM7B,EAAS,YAAc,GAAKI,EAAQ,uBAChD,GAAI,KAAK,oBAAoByB,CAAG,EAC9B,OAAO,KAAK,oBAAoBA,CAAG,EAErC,MAAM6F,EAAsB,CAAA,EAC5B,OAAA1H,EAAS,QAAQ,QAASwB,GAAW,CACnC,MAAMmG,EAAc,CAClB,YAAa,EACb,SAAU,SACV,WAAY,CAAE,CACtB,EACYC,EAAwBD,EAAY,WAC1C,UAAWjF,KAAKtC,EAAQ,cAAe,CACrC,MAAMgH,EAAYpH,EAAS,WAAW0C,CAAC,GAClC0E,EAAU,SAAW,KAAO,GAC/BS,EAAK,aAAanF,CAAC,qCAAqC0E,EAAU,OAAO,8CAA8C,EAErHA,EAAU,SAAW5F,IACvBmG,EAAY,YAAcP,EAAU,OACpCO,EAAY,SAAWP,EAAU,SAAW,WAAa,SACzDQ,EAAsB,KAAK,CACzB,eAAgBxH,EAAQ,cAAcsC,CAAC,EAAE,SACzC,OAAQ0E,EAAU,OAClB,OAAQA,EAAU,MAC9B,CAAW,EAEJ,CACGQ,EAAsB,QACxBF,EAAoB,KAAKC,CAAW,CAE5C,CAAK,EACD,KAAK,oBAAoB9F,CAAG,EAAI6F,EACzBA,CACR,CACD,iBAAkB,CAChB,MAAM7F,EAAM0E,GACV,KAAK,aACL,KAAK,kBACL,KAAK,WACL,KAAK,uBACX,EACS,KAAK,iBAAiB1E,CAAG,IAC5B,KAAK,iBAAiBA,CAAG,EAAoB,OAAO,OAAO,IAAI,GAEjE,KAAK,WAAa,KAAK,iBAAiBA,CAAG,CAC5C,CACD,SAAU,CACR,KAAK,UAAY,KACjB,KAAK,oBAAsB,IAC5B,CACH,CAEA6E,EAAe,UAAY,CACzB,KAAM,CAAC/F,EAAc,YAAY,EACjC,KAAM,UACR,ECtPA,MAAMmH,EAAgB,CACpB,aAAc,CACZ,KAAK,SAAW,GAChB,KAAK,aAAe,GACpB,KAAK,YAAc,CACpB,CACH,CCDA,MAAMC,EAAuB,CAC3B,KAAK7H,EAAU8H,EAAoB,CACjC,KAAK,UAAY9H,EACjB,KAAK,oBAAsB8H,CAC5B,CACD,cAAcC,EAA4BC,EAAoBC,EAAW9F,EAAM+F,EAAY,CACzF,MAAMlI,EAAW,KAAK,UAChBmI,EAAiB,KAAK,oBAC1BJ,CACN,EACUK,EAAiBpI,EAAS,QAAQ,aACtCgI,EAAmB,MACzB,EACI,OAAAhI,EAAS,QAAQ,eAAe,qBAC9B,CACE,QAASmI,EACT,OAAQF,CACT,EACD,CACE,QAASG,EACT,OAAQF,CACT,EACD/F,CACN,EACW6F,CACR,CACD,gBAAgB9D,EAAcmE,EAAQ,GAAMC,EAAYlF,EAAU,CAEhE,MAAMD,EADqB,KAAK,oBACW,mBAAmBe,CAAY,EACpEN,EAAa,KAAK,cAAcM,EAAcmE,EAAOC,CAAU,EACrEnF,EAAgB,WAAaS,EAC7B,KAAK,UAAU,SAAS,gBAAgBT,CAAe,EACvD,KAAK,UAAU,QAAQ,gBAAgBA,CAAe,EACtD,KAAK,UAAU,QAAQ,YAAYC,CAAQ,CAC5C,CACD,kBAAmB,CACjB,KAAK,UAAU,QAAQ,eACxB,CAOD,oBAAoBc,EAAc,CAChC,MAAMf,EAAkB,KAAK,oBAAoB,mBAAmBe,CAAY,EAChF,OAAIf,EAAgB,SAAS,CAAC,EACrBA,EAAgB,SAAS,CAAC,EAAE,kBAAiB,EAE/C,KAAK,UAAU,QAAQ,aAC5Be,EAAa,cAAc,CAAC,EAAE,MACpC,CACG,CACD,cAAcA,EAAcmE,EAAOE,EAAY,CACzC,OAAOF,GAAU,YACnBA,EAAQA,EAAQG,EAAM,IAAMA,EAAM,MAEpC,MAAMV,EAAqB,KAAK,oBAC1B3E,EAAkB2E,EAAmB,mBAAmB5D,CAAY,EACpEuE,EAAmBvE,EAAa,cAAc,IAClD,CAACzC,EAASe,IAAM,CACd,MAAMkG,EAAUvF,EAAgB,SAASX,CAAC,EAC1C,IAAImG,EACAC,EACAF,EAGFC,EAFuBD,EAAQ,oBACU,aAGzCC,EAAO,KAAK,UAAU,QAAQ,aAAalH,CAAO,EAAE,WAAW,CAC7D,cAAe,CAC3B,CAAW,EAEC0B,EAAgB,aAAaX,CAAC,IAChCoG,EAAgBD,EAChBA,EAAO,KAAK,UAAU,QAAQ,eAC5BxF,EAAgB,aAAaX,CAAC,CAC1C,GAEQ,MAAMqG,EAASR,EAAQG,EAAM,MAAQ,QAAU,OAC/C,OAAAD,IAAeA,EAAaT,EAAmB,mBACxC,CACL,KAAAa,EACA,cAAAC,EACA,WAAAL,EACA,QAAS,QACT,OAAAM,CACV,CACO,CACP,EACI,IAAIC,EAKJ,IAJK5E,EAAa,SAAWA,EAAa,QAAU,CAACA,EAAa,sBAChEA,EAAa,0BAAyB,EACtCA,EAAa,oBAAoB,OAAO,YAAcf,EAAgB,KAAO,EAAI,GAE/Ee,EAAa,oBAAqB,CACpC,MAAM6E,EAAgBV,EAAQG,EAAM,QAAU,QAAU,OAClDQ,EAAcX,EAAQG,EAAM,MAAQ,QAAU,OACpDM,EAAyB,CACvB,KAAM,KAAK,UAAU,QAAQ,aAAa5E,EAAa,oBAAoB,MAAM,EAAE,WAAY,EAC/F,eAAgB,QAChB,cAAA6E,EACA,gBAAiB,EACjB,YAAAC,EACA,aAAc,OACtB,CACK,CAKD,MAJmB,CACjB,iBAAAP,EACA,uBAAAK,CACN,CAEG,CACD,MAAM5E,EAAcmE,EAAQ,GAAMC,EAAYlF,EAAU,CACtD,GAAI,CAACiF,EACH,OACF,KAAM,CAAE,IAAA1H,EAAK,QAAAV,GAAY,KAAK,UACxBc,EAASJ,EAAI,OAEnB,GADmBV,EAAQ,iBAAmB,KAC9B,CACd,MAAM8F,EAAiBhF,EAAO,uBACxBkI,EAAuB,KAAK,cAAc/E,EAAcmE,EAAOC,CAAU,EACzE3B,EAAcZ,EAAe,gBAAgBkD,CAAoB,EACvEtC,EAAY,YAAYvD,EAAS,EAAGA,EAAS,EAAGA,EAAS,MAAOA,EAAS,OAAQ,EAAG,CAAC,EACrFuD,EAAY,IAAG,EACf,MAAMuC,EAAcnD,EAAe,SACnChF,EAAO,MAAM,OAAO,CAACmI,CAAW,CAAC,CACvC,MACM,KAAK,gBAAgBhF,EAAcmE,EAAOC,EAAYlF,CAAQ,CAEjE,CACD,oBAAoBc,EAAc,CAChCA,EAAa,OAAS,GACtB,MAAMf,EAAkB,IAAIyE,GAC5B,OAAA1D,EAAa,cAAc,QAAQ,CAACiF,EAAc3G,IAAM,CACtD,GAAI4G,GAAa,KAAKD,EAAa,QAAQ,EAAG,CAC5C,MAAMT,EAAUS,EAAa,SAAS,WACpC,QACV,EACcE,EAAYF,EAAa,YAAc,gBAAkB,SAC/D,GAAI,CACFT,EAAQ,UAAU,CAChB,OAAQ,KAAK,UAAU,IAAI,OAC3B,MAAO,gBAAgB,gBAAkB,gBAAgB,SAAW,gBAAgB,kBAAoB,gBAAgB,SACxH,OAAQ,aACR,UAAAW,CACZ,CAAW,CACF,OAAQC,EAAG,CACV,QAAQ,MAAMA,CAAC,CAChB,CACDnG,EAAgB,SAASX,CAAC,EAAIkG,CAC/B,CAED,GADAvF,EAAgB,KAAOgG,EAAa,OAAO,UACvCA,EAAa,OAAO,UAAW,CACjC,MAAMI,EAAc,IAAIC,GAAc,CACpC,MAAO,EACP,OAAQ,EACR,YAAa,CACvB,CAAS,EACDrG,EAAgB,aAAaX,CAAC,EAAI+G,CACnC,CACP,CAAK,EACGpG,EAAgB,OAClBA,EAAgB,YAAc,EAC1Be,EAAa,sBACfA,EAAa,oBAAoB,OAAO,YAAc,IAGnDf,CACR,CACD,uBAAuBA,EAAiB,CACtCA,EAAgB,SAAS,QAASuF,GAAY,CAC5CA,EAAQ,YAAW,CACzB,CAAK,EACDvF,EAAgB,aAAa,QAAS1B,GAAY,CAChDA,EAAQ,QAAO,CACrB,CAAK,EACD0B,EAAgB,aAAa,OAAS,EACtCA,EAAgB,SAAS,OAAS,CACnC,CACD,0BAA0Be,EAAc,CACtC,MAAMf,EAAkB,KAAK,oBAAoB,mBAAmBe,CAAY,EAC5EA,EAAa,qBAAuBf,EAAgB,OACtDe,EAAa,oBAAoB,OAAO,YAAc,EAEzD,CACD,sBAAsBA,EAAc,CAClC,MAAMf,EAAkB,KAAK,oBAAoB,mBAAmBe,CAAY,EAChFf,EAAgB,MAAQe,EAAa,MACrCf,EAAgB,OAASe,EAAa,OAClCf,EAAgB,MAClBe,EAAa,cAAc,QAAQ,CAACiF,EAAc3G,IAAM,CAClCW,EAAgB,aAAaX,CAAC,GACrC,OACX2G,EAAa,OAAO,MACpBA,EAAa,OAAO,OACpBA,EAAa,OAAO,WAC9B,CACA,CAAO,CAEJ,CACH,CC1MA,MAAMM,UAA8BC,EAAmB,CACrD,YAAY1J,EAAU,CACpB,MAAMA,CAAQ,EACd,KAAK,QAAU,IAAI6H,GACnB,KAAK,QAAQ,KAAK7H,EAAU,IAAI,CACjC,CACH,CAEAyJ,EAAsB,UAAY,CAChC,KAAM,CAAChJ,EAAc,YAAY,EACjC,KAAM,cACR,ECbA,MAAMkJ,CAAgB,CACpB,aAAc,CACZ,KAAK,gBAAkC,OAAO,OAAO,IAAI,CAC1D,CACD,cAAchJ,EAAK,CACjB,KAAK,KAAOA,EACZ,KAAK,YAAcA,EAAI,OAAO,OAAO,gCACtC,CACD,eAAeT,EAAS,CACtB,OAAO,KAAK,gBAAgBA,EAAQ,UAAU,GAAK,KAAK,sBAAsBA,CAAO,CACtF,CACD,sBAAsBA,EAAS,CAC7B,MAAMa,EAAS,KAAK,KAAK,OACnB6I,EAAa1J,EAAQ,UAAU,IAAKY,GAAUC,EAAO,sBAAsB,CAAE,QAASD,CAAK,CAAE,CAAC,EAC9F+I,EAAqB,CAAE,iBAAkBD,GAC/C,YAAK,gBAAgB1J,EAAQ,UAAU,EAAI,CACzC,WAAA0J,EACA,SAAU7I,EAAO,qBAAqB8I,CAAkB,CAC9D,EACW,KAAK,gBAAgB3J,EAAQ,UAAU,CAC/C,CACD,SAAU,CACR,KAAK,KAAO,KACZ,KAAK,gBAAkB,IACxB,CACH,CAEAyJ,EAAgB,UAAY,CAC1B,KAAM,CACJlJ,EAAc,YACf,EACD,KAAM,QACR,EClCA,MAAMqJ,EAAsB,CAAA,EAC5BA,EAAoB,OAAS,CAC3B,MAAO,CACL,UAAW,MACX,UAAW,sBACX,UAAW,KACZ,EACD,MAAO,CACL,UAAW,MACX,UAAW,sBACX,UAAW,KACZ,CACH,EACAA,EAAoB,IAAM,CACxB,MAAO,CACL,UAAW,YACX,UAAW,sBACX,UAAW,KACZ,EACD,MAAO,CACL,UAAW,MACX,UAAW,MACX,UAAW,KACZ,CACH,EACAA,EAAoB,SAAW,CAC7B,MAAO,CACL,UAAW,MACX,UAAW,sBACX,UAAW,KACZ,EACD,MAAO,CACL,UAAW,MACX,UAAW,sBACX,UAAW,KACZ,CACH,EACAA,EAAoB,OAAS,CAC3B,MAAO,CACL,UAAW,MACX,UAAW,sBACX,UAAW,KACZ,EACD,MAAO,CACL,UAAW,MACX,UAAW,gBACX,UAAW,KACZ,CACH,EACAA,EAAoB,QAAU,CAC5B,MAAO,CACL,UAAW,MACX,UAAW,sBACX,UAAW,KACZ,EACD,MAAO,CACL,UAAW,MACX,UAAW,gBACX,UAAW,KACZ,CACH,EACAA,EAAoB,KAAO,CACzB,MAAO,CACL,UAAW,MACX,UAAW,sBACX,UAAW,KACZ,EACD,MAAO,CACL,UAAW,OACX,UAAW,OACX,UAAW,KACZ,CACH,EACAA,EAAoB,YAAY,EAAI,CAClC,MAAO,CACL,UAAW,MACX,UAAW,sBACX,UAAW,KACZ,EACD,MAAO,CACL,UAAW,YACX,UAAW,sBACX,UAAW,KACZ,CACH,EACAA,EAAoB,SAAS,EAAI,CAC/B,MAAO,CACL,UAAW,MACX,UAAW,MACX,UAAW,KACZ,EACD,MAAO,CACL,UAAW,YACX,UAAW,MACX,UAAW,KACZ,CACH,EACAA,EAAoB,YAAY,EAAI,CAClC,MAAO,CACL,UAAW,MACX,UAAW,sBACX,UAAW,KACZ,EACD,MAAO,CACL,UAAW,YACX,UAAW,gBACX,UAAW,KACZ,CACH,EACAA,EAAoB,MAAQ,CAC1B,MAAO,CACL,UAAW,OACX,UAAW,sBACX,UAAW,KACZ,EACD,MAAO,CACL,UAAW,OACX,UAAW,gBACX,UAAW,KACZ,CACH,EACAA,EAAoB,IAAM,CACxB,MAAO,CACL,UAAW,MACX,UAAW,MACX,UAAW,KACZ,EACD,MAAO,CACL,UAAW,MACX,UAAW,MACX,UAAW,KACZ,CACH,EACAA,EAAoB,IAAM,CACxB,MAAO,CACL,UAAW,MACX,UAAW,MACX,UAAW,KACZ,EACD,MAAO,CACL,UAAW,MACX,UAAW,MACX,UAAW,KACZ,CACH,EC5IA,MAAMC,CAAe,CACnB,aAAc,CACZ,KAAK,aAAe,IAAIpK,EACxB,KAAK,aAAa,MAAQ,EAC3B,CACD,cAAcgB,EAAK,CACjB,KAAK,IAAMA,CACZ,CAKD,gBAAgB0C,EAAO,CAErB,MAAO,CACL,CACE,OAAQ,aACR,UAAW,EACX,MALUyG,EAAoBzG,EAAM,SAAS,GAAKyG,EAAoB,MAMvE,CACP,CACG,CACD,SAAU,CACR,KAAK,IAAM,IACZ,CACH,CAEAC,EAAe,UAAY,CACzB,KAAM,CACJtJ,EAAc,YACf,EACD,KAAM,OACR,ECpCA,MAAMuJ,GAA+B,CACnC,KAAM,QACN,OAAOC,EAAQC,EAAYvJ,EAAK,CAC9B,MAAMQ,EAAW8I,EAAO,SAClBE,GAASF,EAAO,WAAa,IAAMA,EAAO,YAAc,GACxDG,EAAgBjJ,EAAS,WAAagJ,EAC5CxJ,EAAI,OAAO,MAAM,aACf,CAAE,QAASuJ,CAAY,EACvB/I,EACA,CACE,OAAQ,EACR,aAAc8I,EAAO,YACrB,YAAaA,EAAO,YAAcG,CACnC,EACD,CACE,MAAOH,EAAO,WACd,OAAQA,EAAO,YACf,mBAAoB,CACrB,CACP,CACG,CACH,ECrBMI,EAAe,CACnB,iBAAkB,CAAE,WAAY,EAAG,WAAY,EAAG,YAAa,CAAG,EAClE,iBAAkB,CAAE,WAAY,GAAI,WAAY,EAAG,YAAa,CAAG,EACnE,iBAAkB,CAAE,WAAY,GAAI,WAAY,EAAG,YAAa,CAAG,EACnE,iBAAkB,CAAE,WAAY,GAAI,WAAY,EAAG,YAAa,CAAG,EACnE,iBAAkB,CAAE,WAAY,EAAG,WAAY,EAAG,YAAa,CAAG,EAClE,kBAAmB,CAAE,WAAY,GAAI,WAAY,EAAG,YAAa,CAAG,EACpE,iBAAkB,CAAE,WAAY,GAAI,WAAY,EAAG,YAAa,CAAG,CACrE,EACMC,GAAmB,CAAE,WAAY,EAAG,WAAY,EAAG,YAAa,GAChEC,GAAqC,CACzC,KAAM,aACN,OAAON,EAAQC,EAAYvJ,EAAK,CAC9B,IAAI6J,EAAWP,EAAO,WAClBQ,EAAYR,EAAO,YACvB,MAAMS,EAAYL,EAAaJ,EAAO,MAAM,GAAKK,GACjD,QAAS9H,EAAI,EAAGA,EAAIyH,EAAO,SAAS,OAAQzH,IAAK,CAC/C,MAAMmI,EAAcV,EAAO,SAASzH,CAAC,EAC/BoI,EAAc,KAAK,KAAKJ,EAAWE,EAAU,UAAU,EAAIA,EAAU,WAC3E/J,EAAI,OAAO,MAAM,aACf,CACE,QAASuJ,EACT,SAAU1H,CACX,EACDmI,EACA,CACE,OAAQ,EACR,YAAAC,CACD,EACD,CACE,MAAO,KAAK,KAAKJ,EAAWE,EAAU,UAAU,EAAIA,EAAU,WAC9D,OAAQ,KAAK,KAAKD,EAAYC,EAAU,WAAW,EAAIA,EAAU,YACjE,mBAAoB,CACrB,CACT,EACMF,EAAW,KAAK,IAAIA,GAAY,EAAG,CAAC,EACpCC,EAAY,KAAK,IAAIA,GAAa,EAAG,CAAC,CACvC,CACF,CACH,ECvCMI,EAAyB,CAC7B,KAAM,QACN,OAAOZ,EAAQC,EAAYvJ,EAAK,CAC9B,MAAMQ,EAAW8I,EAAO,SACxB,GAAI,CAAC9I,EACH,OACF,MAAM2J,EAAQ,KAAK,IAAIZ,EAAW,MAAOD,EAAO,eAAiBA,EAAO,UAAU,EAC5Ec,EAAS,KAAK,IAAIb,EAAW,OAAQD,EAAO,gBAAkBA,EAAO,WAAW,EAChFe,EAAqBf,EAAO,YAAc,8BAChDtJ,EAAI,OAAO,MAAM,2BACf,CAAE,OAAQQ,CAAU,EACpB,CAAE,QAAS+I,EAAY,mBAAAc,CAAoB,EAC3C,CACE,MAAAF,EACA,OAAAC,CACD,CACP,CACG,CACH,EChBME,GAAyB,CAC7B,KAAM,QACN,OAAOhB,EAAQC,EAAYvJ,EAAK,CAC9BkK,EAAuB,OAAOZ,EAAQC,EAAYvJ,CAAG,CACtD,CACH,ECPA,MAAMuK,EAAmB,CACvB,YAAYnK,EAAQ,CAClB,KAAK,OAASA,EACd,KAAK,QAAUA,EAAO,cAAc,CAAE,UAAW,QAAQ,CAAE,EAC3D,KAAK,UAAY,EAClB,CACD,mBAAmBoK,EAAQ,CACzB,IAAI3K,EAAW,KAAK,UAAU2K,CAAM,EACpC,OAAK3K,IACE,KAAK,qBACR,KAAK,mBAAqB,KAAK,OAAO,mBAAmB,CACvD,KAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBA0BZ,CAAS,GAEHA,EAAW,KAAK,OAAO,qBAAqB,CAC1C,OAAQ,OACR,OAAQ,CACN,OAAQ,KAAK,mBACb,WAAY,YACb,EACD,SAAU,CACR,OAAQ,KAAK,mBACb,WAAY,eACZ,QAAS,CAAC,CAAE,OAAA2K,EAAQ,CACrB,CACT,CAAO,EACD,KAAK,UAAUA,CAAM,EAAI3K,GAEpBA,CACR,CAMD,eAAeiB,EAAS,CACtB,MAAMjB,EAAW,KAAK,mBAAmBiB,EAAQ,MAAM,EACvD,GAAIA,EAAQ,YAAc,MAAQA,EAAQ,YAAc,KACtD,MAAM,IAAI,MAAM,kEAAkE,EAEpF,IAAI2J,EAAa3J,EACjB,MAAM4J,EAAkB5J,EAAQ,oBAAsB,EAChD6J,EAAiB7J,EAAQ,MAAQ,gBAAgB,kBACvD,GAAI,CAAC6J,EAAgB,CACnB,MAAMC,EAAuB,CAC3B,KAAM,CACJ,MAAO,KAAK,KAAK9J,EAAQ,MAAQ,CAAC,EAClC,OAAQ,KAAK,KAAKA,EAAQ,OAAS,CAAC,EACpC,mBAAoB4J,CACrB,EACD,OAAQ5J,EAAQ,OAChB,MAAO,gBAAgB,gBAAkB,gBAAgB,SAAW,gBAAgB,kBACpF,cAAeA,EAAQ,cAAgB,CAC/C,EACM2J,EAAa,KAAK,OAAO,cAAcG,CAAoB,CAC5D,CACD,MAAMxF,EAAiB,KAAK,OAAO,qBAAqB,CAAE,CAAA,EACpDyF,EAAkBhL,EAAS,mBAAmB,CAAC,EACrD,QAASiL,EAAa,EAAGA,EAAaJ,EAAiB,EAAEI,EAAY,CACnE,IAAIC,EAAUjK,EAAQ,WAAW,CAC/B,aAAc,EACd,cAAe,EACf,UAAW,KACX,eAAgBgK,EAChB,gBAAiB,CACzB,CAAO,EACGE,EAAcL,EAAiB,EAAI,EACvC,QAAS9I,EAAI,EAAGA,EAAIf,EAAQ,cAAe,EAAEe,EAAG,CAC9C,MAAMoJ,EAAUR,EAAW,WAAW,CACpC,aAAcO,IACd,cAAe,EACf,UAAW,KACX,eAAgBF,EAChB,gBAAiB,CAC3B,CAAS,EACK9E,EAAcZ,EAAe,gBAAgB,CACjD,iBAAkB,CAAC,CACjB,KAAM6F,EACN,QAAS,QACT,OAAQ,QACR,WAAY,CAAE,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,CAAG,CAClD,CAAW,CACX,CAAS,EACKhL,EAAY,KAAK,OAAO,gBAAgB,CAC5C,OAAQ4K,EACR,QAAS,CAAC,CACR,QAAS,EACT,SAAU,KAAK,OAC3B,EAAa,CACD,QAAS,EACT,SAAUE,CACtB,CAAW,CACX,CAAS,EACD/E,EAAY,YAAYnG,CAAQ,EAChCmG,EAAY,aAAa,EAAG/F,CAAS,EACrC+F,EAAY,KAAK,EAAG,EAAG,EAAG,CAAC,EAC3BA,EAAY,IAAG,EACf+E,EAAUE,CACX,CACF,CACD,GAAI,CAACN,EAAgB,CACnB,MAAMO,EAAe,CACnB,MAAO,KAAK,KAAKpK,EAAQ,MAAQ,CAAC,EAClC,OAAQ,KAAK,KAAKA,EAAQ,OAAS,CAAC,EACpC,mBAAoB4J,CAC5B,EACM,QAAS7I,EAAI,EAAGA,EAAIf,EAAQ,cAAe,EAAEe,EAC3CuD,EAAe,qBAAqB,CAClC,QAASqF,EACT,SAAU5I,EAAI,CACxB,EAAW,CACD,QAAAf,EACA,SAAUe,CACX,EAAEqJ,CAAY,EACfA,EAAa,MAAQ,KAAK,KAAKA,EAAa,MAAQ,CAAC,EACrDA,EAAa,OAAS,KAAK,KAAKA,EAAa,OAAS,CAAC,CAE1D,CACD,YAAK,OAAO,MAAM,OAAO,CAAC9F,EAAe,OAAQ,CAAA,CAAC,EAC7CuF,GACHF,EAAW,QAAO,EAEb3J,CACR,CACH,CC5IA,MAAMqK,EAAiB,CACrB,YAAY9L,EAAU,CACpB,KAAK,gBAAkB,GACvB,KAAK,YAA8B,OAAO,OAAO,IAAI,EACrD,KAAK,aAA+B,OAAO,OAAO,IAAI,EACtD,KAAK,eAAiC,OAAO,OAAO,IAAI,EACxD,KAAK,iBAAmC,OAAO,OAAO,IAAI,EAC1D,KAAK,SAAW,CACd,MAAO6K,EACP,OAAQb,GACR,MAAOiB,GACP,WAAYV,EAClB,EACI,KAAK,UAAYvK,EACjBA,EAAS,aAAa,eAAe,KAAM,aAAa,EACxDA,EAAS,aAAa,eAAe,KAAM,cAAc,EACzDA,EAAS,aAAa,eAAe,KAAM,gBAAgB,EAC3DA,EAAS,aAAa,eAAe,KAAM,kBAAkB,CAC9D,CACD,cAAcW,EAAK,CACjB,KAAK,KAAOA,CACb,CACD,WAAWsJ,EAAQ,CACjB,GAAIA,EAAO,oBAAqB,CAC9B,MAAM8B,EAAmB,KAAK,IAAI9B,EAAO,WAAYA,EAAO,WAAW,EACvEA,EAAO,cAAgB,KAAK,MAAM,KAAK,KAAK8B,CAAgB,CAAC,EAAI,CAClE,CACD,IAAIxG,EAAQ,gBAAgB,gBAAkB,gBAAgB,SAC1D0E,EAAO,iBAAmB,eAC5B1E,GAAS,gBAAgB,kBACzBA,GAAS,gBAAgB,UAE3B,MAAMmF,EAAYL,EAAaJ,EAAO,MAAM,GAAK,CAAE,WAAY,EAAG,WAAY,EAAG,YAAa,CAAC,EACzFa,EAAQ,KAAK,KAAKb,EAAO,WAAaS,EAAU,UAAU,EAAIA,EAAU,WACxEK,EAAS,KAAK,KAAKd,EAAO,YAAcS,EAAU,WAAW,EAAIA,EAAU,YAC3EsB,EAAoB,CACxB,MAAO/B,EAAO,MACd,KAAM,CAAE,MAAAa,EAAO,OAAAC,CAAQ,EACvB,OAAQd,EAAO,OACf,YAAaA,EAAO,YACpB,cAAeA,EAAO,cACtB,UAAWA,EAAO,UAClB,MAAA1E,CACN,EACU2E,EAAa,KAAK,KAAK,OAAO,cAAc8B,CAAiB,EACnE,YAAK,YAAY/B,EAAO,GAAG,EAAIC,EAC1B,KAAK,gBAAgB,SAASD,CAAM,IACvCA,EAAO,GAAG,SAAU,KAAK,eAAgB,IAAI,EAC7CA,EAAO,GAAG,SAAU,KAAK,eAAgB,IAAI,EAC7CA,EAAO,GAAG,UAAW,KAAK,gBAAiB,IAAI,EAC/CA,EAAO,GAAG,SAAU,KAAK,eAAgB,IAAI,EAC7CA,EAAO,GAAG,gBAAiB,KAAK,gBAAiB,IAAI,EACrD,KAAK,gBAAgB,KAAKA,CAAM,GAElC,KAAK,eAAeA,CAAM,EACnBC,CACR,CACD,eAAeD,EAAQ,CACrB,MAAMC,EAAa,KAAK,aAAaD,CAAM,EACtCC,IAED,KAAK,SAASD,EAAO,cAAc,GACrC,KAAK,SAASA,EAAO,cAAc,EAAE,OAAOA,EAAQC,EAAY,KAAK,IAAI,EAEvED,EAAO,qBAAuBA,EAAO,cAAgB,GACvD,KAAK,gBAAgBA,CAAM,EAE9B,CACD,eAAeA,EAAQ,CACrB,MAAMC,EAAa,KAAK,YAAYD,EAAO,GAAG,EAC1CC,IACF,KAAK,YAAYD,EAAO,GAAG,EAAI,KAC/BC,EAAW,QAAO,EAErB,CACD,gBAAgBD,EAAQ,CACjB,KAAK,mBACR,KAAK,iBAAmB,IAAIiB,GAAmB,KAAK,KAAK,MAAM,GAEjE,MAAMhB,EAAa,KAAK,aAAaD,CAAM,EAC3C,KAAK,iBAAiB,eAAeC,CAAU,CAChD,CACD,gBAAgBD,EAAQ,CACtBA,EAAO,IAAI,SAAU,KAAK,eAAgB,IAAI,EAC9CA,EAAO,IAAI,SAAU,KAAK,eAAgB,IAAI,EAC9CA,EAAO,IAAI,UAAW,KAAK,gBAAiB,IAAI,EAChDA,EAAO,IAAI,SAAU,KAAK,eAAgB,IAAI,EAC9CA,EAAO,IAAI,gBAAiB,KAAK,gBAAiB,IAAI,EACtD,KAAK,gBAAgB,OAAO,KAAK,gBAAgB,QAAQA,CAAM,EAAG,CAAC,EACnE,KAAK,eAAeA,CAAM,CAC3B,CACD,eAAeA,EAAQ,CACrB,MAAMC,EAAa,KAAK,YAAYD,EAAO,GAAG,EACzCC,GAEMA,EAAW,QAAUD,EAAO,YAAcC,EAAW,SAAWD,EAAO,eAChF,KAAK,iBAAiBA,EAAO,GAAG,EAAI,KACpC,KAAK,eAAeA,EAAO,GAAG,EAAI,KAClC,KAAK,eAAeA,CAAM,EAC1B,KAAK,WAAWA,CAAM,GALtB,KAAK,WAAWA,CAAM,CAOzB,CACD,aAAazI,EAAS,CACpB,YAAK,aAAaA,EAAQ,WAAW,EAAI,KAAK,KAAK,OAAO,cAAcA,CAAO,EACxE,KAAK,aAAaA,EAAQ,WAAW,CAC7C,CACD,cAAcA,EAAS,CACrB,OAAO,KAAK,aAAaA,EAAQ,WAAW,GAAK,KAAK,aAAaA,CAAO,CAC3E,CACD,aAAayI,EAAQ,CACnB,OAAO,KAAK,YAAYA,EAAO,GAAG,GAAK,KAAK,WAAWA,CAAM,CAC9D,CAUD,oBAAoBxI,EAAS,CAC3B,OAAO,KAAK,eAAeA,EAAQ,GAAG,GAAK,KAAK,wBAAwBA,CAAO,CAChF,CACD,wBAAwBA,EAAS,CAC/B,MAAMwI,EAASxI,EAAQ,OACvB,YAAK,eAAeA,EAAQ,GAAG,EAAI,IAAImE,EAAU,CAC/C,EAAGqE,EACH,EAAGA,EAAO,MACV,EAAG,IAAIgC,EAAa,CAClB,eAAgB,CAAE,KAAM,cAAe,MAAOxK,EAAQ,cAAc,QAAU,CACtF,CAAO,CACP,CAAK,EACM,KAAK,eAAeA,EAAQ,GAAG,CACvC,CACD,eAAeA,EAAS,CACtB,MAAMwI,EAASxI,EAAQ,OACvB,OAAO,KAAK,iBAAiBwI,EAAO,GAAG,GAAK,KAAK,mBAAmBA,CAAM,CAC3E,CACD,mBAAmBxI,EAAS,CAC1B,YAAK,iBAAiBA,EAAQ,GAAG,EAAI,KAAK,aAAaA,CAAO,EAAE,aACzD,KAAK,iBAAiBA,EAAQ,GAAG,CACzC,CACD,eAAeA,EAAS,CACtB,MAAMzB,EAAW,KAAK,UAChB+F,EAAiB/F,EAAS,IAAI,OAAO,qBAAoB,EACzDkM,EAASpJ,EAAW,IAAK,EAAC,aAAY,EAC5CoJ,EAAO,MAAQzK,EAAQ,OAAO,WAC9ByK,EAAO,OAASzK,EAAQ,OAAO,YAC/B,MAAMiH,EAAUwD,EAAO,WAAW,QAAQ,EAC1C,OAAAxD,EAAQ,UAAU,CAChB,OAAQ1I,EAAS,IAAI,OACrB,MAAO,gBAAgB,SAAW,gBAAgB,SAClD,OAAQ8C,EAAW,IAAG,EAAG,aAAc,EAAC,IAAI,yBAA0B,EACtE,UAAW,eACjB,CAAK,EACDiD,EAAe,qBAAqB,CAClC,QAAS/F,EAAS,QAAQ,aAAayB,EAAQ,MAAM,EACrD,OAAQ,CACN,EAAG,EACH,EAAG,CACJ,CACP,EAAO,CACD,QAASiH,EAAQ,kBAAmB,CAC1C,EAAO,CACD,MAAOwD,EAAO,MACd,OAAQA,EAAO,MACrB,CAAK,EACDlM,EAAS,IAAI,OAAO,MAAM,OAAO,CAAC+F,EAAe,OAAQ,CAAA,CAAC,EACnDmG,CACR,CACD,UAAUzK,EAAS,CACjB,MAAM0K,EAAe,KAAK,eAAe1K,CAAO,EAC1C2K,EAAmBC,EAAW,2BAA2BF,EAAa,MAAOA,EAAa,MAAM,EAChGzD,EAAU0D,EAAiB,QACjC1D,EAAQ,UAAUyD,EAAc,EAAG,CAAC,EACpC,KAAM,CAAE,MAAArB,EAAO,OAAAC,CAAQ,EAAGoB,EACpBG,EAAY5D,EAAQ,aAAa,EAAG,EAAGoC,EAAOC,CAAM,EACpDwB,EAAS,IAAI,kBAAkBD,EAAU,KAAK,MAAM,EAC1D,OAAAD,EAAW,uBAAuBD,CAAgB,EAC3C,CAAE,OAAAG,EAAQ,MAAAzB,EAAO,OAAAC,EACzB,CACD,SAAU,CACR,KAAK,gBAAgB,MAAK,EAAG,QAASd,GAAW,KAAK,gBAAgBA,CAAM,CAAC,EAC7E,KAAK,gBAAkB,KACvB,UAAWuC,KAAK,OAAO,KAAK,KAAK,cAAc,EAAG,CAChD,MAAM7K,EAAM,OAAO6K,CAAC,EACF,KAAK,eAAe7K,CAAG,GAC9B,QAAO,EAClB,KAAK,eAAeA,CAAG,EAAI,IAC5B,CACD,KAAK,KAAO,KACZ,KAAK,iBAAmB,KACxB,KAAK,YAAc,KACnB,KAAK,eAAiB,KACtB,KAAK,iBAAmB,KACxB,KAAK,aAAe,IACrB,CACH,CAEAmK,GAAiB,UAAY,CAC3B,KAAM,CACJrL,EAAc,YACf,EACD,KAAM,SACR,EC5MA,MAAMgM,EAAmB,CACvB,MAAO,CACL,MAAMC,EAAgB,IAAIT,EAAa,CACrC,iBAAkB,CAAE,MAAO,IAAIU,EAAU,KAAM,aAAe,EAC9D,OAAQ,CAAE,MAAO,IAAI,aAAa,CAAC,EAAG,EAAG,EAAG,CAAC,CAAC,EAAG,KAAM,WAAa,EACpE,OAAQ,CAAE,MAAO,EAAG,KAAM,KAAO,CACvC,CAAK,EACKC,EAAaC,EAA4B,CAC7C,KAAM,WACN,KAAM,CACJC,GACAC,GAAwBC,GAAsB,CAAE,EAChDC,GACAC,CACD,CACP,CAAK,EACD,KAAK,OAAS,IAAIC,EAAO,CACvB,WAAAP,EACA,UAAW,CAET,cAAAF,CACD,CACP,CAAK,CACF,CACD,QAAQU,EAAcC,EAAY,CAChC,MAAM3E,EAAU2E,EAAW,QACrBtN,EAAS2I,EAAQ,cAAgB,KAAK,OACtC1I,EAAWoN,EAAa,SACxBE,EAAgBtN,EAAS,gBACzB,CACJ,QAAAuN,EACA,aAAAC,CACN,EAAQF,EAAc,qBAAqB5E,CAAO,EACxCzI,EAAUD,EAAS,QACzBC,EAAQ,YAAYsN,EAAQ,SAAUxN,EAAO,UAAU,EACvD,MAAMI,EAA0BH,EAAS,eAAe,UACxDC,EAAQ,aAAa,EAAGE,EAAyBJ,EAAO,UAAU,EAClE,MAAM0N,EAAiBzN,EAAS,YAAY,aAAa,oBAAoBD,EAAO,UAAU,cAAe,EAAI,EACjHE,EAAQ,aAAa,EAAGwN,EAAgB1N,EAAO,UAAU,EACzD,MAAM2N,EAAUF,EAAa,aAC7B,IAAIlK,EAAW,KACf,QAASd,EAAI,EAAGA,EAAIgL,EAAa,gBAAiBhL,IAAK,CACrD,MAAMpC,EAAQsN,EAAQlL,CAAC,EAWvB,GAVIpC,EAAM,WAAakD,IACrBA,EAAWlD,EAAM,SACjBH,EAAQ,uCACNsN,EAAQ,SACRxN,EAAO,WACPqN,EAAa,MACbhN,EAAM,QAChB,GAEML,EAAO,OAAO,CAAC,EAAIK,EAAM,UACrB,CAACA,EAAM,aAAc,CACvB,MAAMC,EAAeD,EAAM,SAC3BA,EAAM,UAAYE,EAAyBD,EAAa,SAAUA,EAAa,KAAK,EACpFD,EAAM,aAAeJ,EAAS,UAAU,aACtCI,EAAM,UACNL,EAAO,WACP,CACV,CACO,CACDE,EAAQ,aAAa,EAAGG,EAAM,UAAWL,EAAO,UAAU,EAC1DE,EAAQ,kBAAkB,YAAYG,EAAM,KAAM,EAAGA,EAAM,KAAK,CACjE,CACF,CACD,SAAU,CACR,KAAK,OAAO,QAAQ,EAAI,EACxB,KAAK,OAAS,IACf,CACH,CAEAqM,GAAmB,UAAY,CAC7B,KAAM,CACJhM,EAAc,kBACf,EACD,KAAM,UACR,EC/EA,MAAMkN,EAAe,CACnB,MAAO,CACL,MAAMf,EAAaC,EAA4B,CAC7C,KAAM,OACN,KAAM,CACJe,GACAC,GACAX,CACD,CACP,CAAK,EACD,KAAK,QAAU,IAAIC,EAAO,CACxB,WAAAP,EACA,UAAW,CACT,SAAUkB,EAAQ,MAAM,QACxB,SAAUA,EAAQ,MAAM,QAAQ,MAChC,gBAAiB,CACf,eAAgB,CAAE,KAAM,cAAe,MAAO,IAAInB,CAAU,CAC7D,CACF,CACP,CAAK,CACF,CACD,QAAQoB,EAAUC,EAAM,CACtB,MAAMhO,EAAW+N,EAAS,SAC1B,IAAIhO,EAASiO,EAAK,QAClB,GAAI,CAACjO,EACHA,EAAS,KAAK,QACdA,EAAO,OAAO,CAAC,EAAIC,EAAS,QAAQ,oBAAoBgO,EAAK,OAAO,UAC3D,CAACjO,EAAO,WAAY,CAC7B4H,EAAK,gCAAiCqG,EAAK,MAAM,EACjD,MACD,CACD,MAAMpB,EAAa7M,EAAO,WAI1B,GAHI6M,EAAW,2BACb7M,EAAO,OAAO,CAAC,EAAIC,EAAS,eAAe,WAEzC4M,EAAW,wBAAyB,CACtC,MAAMF,EAAgBqB,EAAS,cAC/BhO,EAAO,OAAO,CAAC,EAAIC,EAAS,YAAY,aAAa,oBAAoB0M,EAAe,EAAI,CAC7F,CACD1M,EAAS,QAAQ,KAAK,CACpB,SAAUgO,EAAK,UACf,OAAAjO,EACA,MAAOiO,EAAK,KAClB,CAAK,CACF,CACD,SAAU,CACR,KAAK,QAAQ,QAAQ,EAAI,EACzB,KAAK,QAAU,IAChB,CACH,CAEAL,GAAe,UAAY,CACzB,KAAM,CACJlN,EAAc,kBACf,EACD,KAAM,MACR,EC7CA,MAAMwN,GAAuB,CAC3B,GAAGC,GACH/I,EACAlC,EACAN,EACAf,EACAkK,GACArC,EACAE,EACAI,EACAvD,EACA/D,EACAwB,EACAvD,CACF,EACMyN,GAAqB,CAAC,GAAGC,GAAmB/I,CAAmB,EAC/DgJ,GAAwB,CAACzO,EAAiB+N,GAAgBlB,EAAkB,EAC5E6B,GAAU,CAAA,EACVC,GAAc,CAAA,EACdC,GAAqB,CAAA,EAC3BC,EAAW,kBAAkBhO,EAAc,aAAc6N,EAAO,EAChEG,EAAW,kBAAkBhO,EAAc,YAAa8N,EAAW,EACnEE,EAAW,kBAAkBhO,EAAc,mBAAoB+N,EAAkB,EACjFC,EAAW,IAAI,GAAGR,GAAsB,GAAGE,GAAoB,GAAGE,EAAqB,EACvF,MAAMK,WAAuBC,EAAiB,CAC5C,aAAc,CACZ,MAAMC,EAAe,CACnB,KAAM,SACN,KAAMC,GAAa,OACnB,QAAAP,GACA,YAAAC,GACA,mBAAAC,EACN,EACI,MAAMI,CAAY,CACnB,CACH", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28]}