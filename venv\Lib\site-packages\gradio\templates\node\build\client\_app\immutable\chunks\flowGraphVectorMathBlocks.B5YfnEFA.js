import{R as c,b as p,e as s,f as w,g as h,h as B}from"./declarationMapper.UBCwU7BT.js";import{R as a,V as i,ay as G,M as u,az as k}from"./index.BoI39RQH.js";import{F as l}from"./flowGraphBinaryOperationBlock.CI0NxKdM.js";import{F}from"./flowGraphUnaryOperationBlock.ZI1fHq8c.js";import{F as f}from"./flowGraphTernaryOperationBlock.DiGI0Nsv.js";import{e as m}from"./KHR_interactivity.DEAVS2UW.js";class y extends F{constructor(o){super(c,p,t=>this._polymorphicLength(t),"FlowGraphLengthBlock",o)}_polymorphicLength(o){switch(m(o)){case"Vector2":case"Vector3":case"Vector4":case"Quaternion":return o.length();default:throw new Error(`Cannot compute length of value ${o}`)}}}a("FlowGraphLengthBlock",y);class V extends F{constructor(o){super(c,c,t=>this._polymorphicNormalize(t),"FlowGraphNormalizeBlock",o)}_polymorphicNormalize(o){var n;const t=m(o);let e;switch(t){case"Vector2":case"Vector3":case"Vector4":case"Quaternion":return e=o.normalizeToNew(),(n=this.config)!=null&&n.nanOnZeroLength&&o.length()===0&&e.setAll(NaN),e;default:throw new Error(`Cannot normalize value ${o}`)}}}a("FlowGraphNormalizeBlock",V);class d extends l{constructor(o){super(c,c,p,(t,e)=>this._polymorphicDot(t,e),"FlowGraphDotBlock",o)}_polymorphicDot(o,t){switch(m(o)){case"Vector2":case"Vector3":case"Vector4":case"Quaternion":return o.dot(t);default:throw new Error(`Cannot get dot product of ${o} and ${t}`)}}}a("FlowGraphDotBlock",d);class T extends l{constructor(o){super(s,s,s,(t,e)=>i.Cross(t,e),"FlowGraphCrossBlock",o)}}a("FlowGraphCrossBlock",T);class x extends l{constructor(o){super(w,p,w,(t,e)=>G.Transform(t,u.RotationZ(e)),"FlowGraphRotate2DBlock",o)}}a("FlowGraphRotate2DBlock",x);class C extends f{constructor(o){super(s,s,p,s,(t,e,n)=>i.TransformCoordinates(t,u.RotationAxis(e,n)),"FlowGraphRotate3DBlock",o)}}a("FlowGraphRotate3DBlock",C);function N(r,o){switch(m(r)){case"Vector2":return o.transformVector(r);case"Vector3":return o.transformVector(r);case"Vector4":return r=r,new k(r.x*o.m[0]+r.y*o.m[1]+r.z*o.m[2]+r.w*o.m[3],r.x*o.m[4]+r.y*o.m[5]+r.z*o.m[6]+r.w*o.m[7],r.x*o.m[8]+r.y*o.m[9]+r.z*o.m[10]+r.w*o.m[11],r.x*o.m[12]+r.y*o.m[13]+r.z*o.m[14]+r.w*o.m[15]);default:throw new Error(`Cannot transform value ${r}`)}}class R extends l{constructor(o){const t=(o==null?void 0:o.vectorType)||"Vector3",e=t==="Vector2"?"Matrix2D":t==="Vector3"?"Matrix3D":"Matrix";super(h(t),h(e),h(t),N,"FlowGraphTransformVectorBlock",o)}}a("FlowGraphTransformVectorBlock",R);class z extends l{constructor(o){super(s,B,s,(t,e)=>i.TransformCoordinates(t,e),"FlowGraphTransformCoordinatesBlock",o)}}a("FlowGraphTransformCoordinatesBlock",z);export{T as FlowGraphCrossBlock,d as FlowGraphDotBlock,y as FlowGraphLengthBlock,V as FlowGraphNormalizeBlock,x as FlowGraphRotate2DBlock,C as FlowGraphRotate3DBlock,R as FlowGraphTransformBlock,z as FlowGraphTransformCoordinatesBlock};
//# sourceMappingURL=flowGraphVectorMathBlocks.B5YfnEFA.js.map
