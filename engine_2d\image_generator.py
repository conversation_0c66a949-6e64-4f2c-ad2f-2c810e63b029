"""
2D Engine - 文生图引擎
支持SD、SDXL、Flux、illustrious等模型的图像生成
"""

import os
import torch
import numpy as np
from PIL import Image
from typing import Optional, Dict, Any, Callable, List
from pathlib import Path
import threading
from dataclasses import dataclass
from datetime import datetime

try:
    from diffusers import (
        StableDiffusionPipeline,
        StableDiffusionXLPipeline,
        FluxPipeline,
        DiffusionPipeline,
        AutoencoderKL,
        UNet2DConditionModel,
        DDIMScheduler,
        EulerDiscreteScheduler
    )
    DIFFUSERS_AVAILABLE = True
except ImportError:
    DIFFUSERS_AVAILABLE = False

from core.utils.logger import get_logger
from core.utils.config import config_manager

logger = get_logger(__name__)

@dataclass
class GenerationParams:
    """图像生成参数"""
    prompt: str
    negative_prompt: str = ""
    width: int = 1024
    height: int = 1024
    steps: int = 20
    guidance_scale: float = 7.5
    seed: Optional[int] = None
    batch_size: int = 1
    model_name: str = "flux-dev"
    sampler: str = "euler"  # 采样器

@dataclass
class GenerationResult:
    """生成结果"""
    images: List[Image.Image]
    params: GenerationParams
    generation_time: float
    seed_used: int
    model_used: str

class ImageGenerator:
    """图像生成器"""
    
    def __init__(self):
        self.model_path = config_manager.get('models.t2i.model_path', 'models/t2i')
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.current_pipeline = None
        self.current_model = None
        self.generation_lock = threading.Lock()
        
        # 支持的模型类型
        if DIFFUSERS_AVAILABLE:
            self.model_types = {
                'sd': StableDiffusionPipeline,
                'sd2': StableDiffusionPipeline,
                'sdxl': StableDiffusionXLPipeline,
                'sd3': StableDiffusionPipeline,  # SD3需要特殊处理
                'flux': FluxPipeline,
                'illustrious': StableDiffusionXLPipeline,  # illustrious基于SDXL
                'kandinsky': StableDiffusionPipeline,  # 需要特殊pipeline
                'deepfloyd': StableDiffusionPipeline,  # 需要特殊pipeline
                'wuerstchen': StableDiffusionPipeline,  # 需要特殊pipeline
            }
        else:
            self.model_types = {}

        # 模型特征识别关键词
        self.model_keywords = {
            'flux': ['flux', 'flux.1', 'flux-dev', 'flux-schnell'],
            'illustrious': ['illustrious', 'noob', 'pony', 'animagine'],
            'sdxl': ['xl', 'sdxl', 'stable-diffusion-xl', 'sd_xl', 'base_xl'],
            'sd3': ['sd3', 'stable-diffusion-3'],
            'sd2': ['sd2', 'stable-diffusion-2', 'v2-', '768-v-ema'],
            'kandinsky': ['kandinsky', 'kand'],
            'deepfloyd': ['deepfloyd', 'if-'],
            'wuerstchen': ['wuerstchen', 'wurstchen'],
        }
        
        # 支持的采样器
        self.available_samplers = {
            'euler': 'Euler',
            'euler_a': 'Euler Ancestral',
            'heun': 'Heun',
            'dpm_2': 'DPM 2',
            'dpm_2_a': 'DPM 2 Ancestral',
            'lms': 'LMS',
            'dpm_fast': 'DPM Fast',
            'dpm_adaptive': 'DPM Adaptive',
            'dpmpp_2s_a': 'DPM++ 2S Ancestral',
            'dpmpp_2m': 'DPM++ 2M',
            'dpmpp_sde': 'DPM++ SDE',
            'dpmpp_2m_sde': 'DPM++ 2M SDE',
            'ddim': 'DDIM',
            'plms': 'PLMS'
        }

        # 扫描可用模型
        self.available_models = self._scan_models()

        logger.info(f"2D Engine初始化完成，设备: {self.device}")
        logger.info(f"发现 {len(self.available_models)} 个可用模型")
        logger.info(f"支持 {len(self.available_samplers)} 种采样器")
    
    def _scan_models(self) -> Dict[str, Dict[str, Any]]:
        """扫描可用模型"""
        models = {}
        
        if not os.path.exists(self.model_path):
            logger.warning(f"模型目录不存在: {self.model_path}")
            return models
        
        for item in os.listdir(self.model_path):
            item_path = os.path.join(self.model_path, item)
            
            # 检查是否是模型目录或文件
            if os.path.isdir(item_path):
                # 检查是否包含模型文件
                if self._is_valid_model_dir(item_path):
                    model_type = self._detect_model_type(item_path)
                    models[item] = {
                        'path': item_path,
                        'type': model_type,
                        'format': 'directory'
                    }
            elif item.endswith('.safetensors') or item.endswith('.ckpt'):
                # 单文件模型
                model_type = self._detect_model_type_from_name(item)
                models[item.replace('.safetensors', '').replace('.ckpt', '')] = {
                    'path': item_path,
                    'type': model_type,
                    'format': 'single_file'
                }
        
        return models
    
    def _is_valid_model_dir(self, path: str) -> bool:
        """检查是否是有效的模型目录"""
        required_files = ['model_index.json']
        for file in required_files:
            if not os.path.exists(os.path.join(path, file)):
                return False
        return True
    
    def _detect_model_type(self, path: str) -> str:
        """检测模型类型"""
        # 读取model_index.json
        try:
            import json
            with open(os.path.join(path, 'model_index.json'), 'r') as f:
                model_index = json.load(f)
            
            # 根据配置判断模型类型
            if 'flux' in path.lower():
                return 'flux'
            elif 'xl' in path.lower() or 'sdxl' in path.lower():
                return 'sdxl'
            elif 'illustrious' in path.lower():
                return 'illustrious'
            else:
                return 'sd'
        except:
            return 'sd'  # 默认为SD
    
    def _detect_model_type_from_name(self, filename: str) -> str:
        """从文件名检测模型类型"""
        filename_lower = filename.lower()

        # 使用关键词字典进行精确识别
        for model_type, keywords in self.model_keywords.items():
            if any(keyword in filename_lower for keyword in keywords):
                logger.info(f"检测到模型类型: {model_type} (文件: {filename})")
                return model_type

        # 默认为SD 1.x
        logger.info(f"使用默认模型类型: sd (文件: {filename})")
        return 'sd'

    def _detect_model_type_from_config(self, model_path: str) -> str:
        """从模型配置文件检测模型类型"""
        try:
            import json
            config_path = os.path.join(model_path, 'model_index.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 检查配置中的关键信息
                if 'unet' in config:
                    unet_config = config.get('unet', [])
                    if isinstance(unet_config, list) and len(unet_config) > 1:
                        # 检查是否为SDXL (通常有两个UNet)
                        return 'sdxl'

                # 检查其他特征
                if 'flux' in str(config).lower():
                    return 'flux'
                elif 'xl' in str(config).lower():
                    return 'sdxl'

        except Exception as e:
            logger.debug(f"无法从配置检测模型类型: {e}")

        return 'sd'  # 默认类型

    def _get_sampler_class_name(self, sampler: str) -> Optional[str]:
        """获取采样器类名"""
        sampler_mapping = {
            'euler': 'EulerDiscreteScheduler',
            'euler_a': 'EulerAncestralDiscreteScheduler',
            'heun': 'HeunDiscreteScheduler',
            'dpm_2': 'KDPM2DiscreteScheduler',
            'dpm_2_a': 'KDPM2AncestralDiscreteScheduler',
            'lms': 'LMSDiscreteScheduler',
            'dpm_fast': 'DPMSolverSinglestepScheduler',
            'dpm_adaptive': 'DPMSolverMultistepScheduler',
            'dpmpp_2s_a': 'DPMSolverSinglestepScheduler',
            'dpmpp_2m': 'DPMSolverMultistepScheduler',
            'dpmpp_sde': 'DPMSolverSDEScheduler',
            'dpmpp_2m_sde': 'DPMSolverMultistepScheduler',
            'ddim': 'DDIMScheduler',
            'plms': 'PNDMScheduler'
        }
        return sampler_mapping.get(sampler)

    def get_available_samplers(self) -> Dict[str, str]:
        """获取可用采样器列表"""
        return self.available_samplers.copy()

    def list_models(self) -> Dict[str, Dict[str, Any]]:
        """获取可用模型列表"""
        return self.available_models.copy()

    def get_current_model(self) -> Optional[str]:
        """获取当前加载的模型名称"""
        return self.current_model
    
    def load_model(self, model_name: str) -> bool:
        """加载模型"""
        if not DIFFUSERS_AVAILABLE:
            logger.error("diffusers库未安装，无法加载模型")
            return False
        
        if model_name not in self.available_models:
            logger.error(f"模型不存在: {model_name}")
            return False
        
        model_info = self.available_models[model_name]
        model_type = model_info['type']
        model_path = model_info['path']
        
        try:
            with self.generation_lock:
                # 释放当前模型
                if self.current_pipeline:
                    del self.current_pipeline
                    torch.cuda.empty_cache()
                
                logger.info(f"正在加载模型: {model_name} (类型: {model_type})")
                
                # 根据模型类型选择pipeline
                pipeline_class = self.model_types.get(model_type, StableDiffusionPipeline)
                
                if model_info['format'] == 'single_file':
                    # 单文件模型
                    self.current_pipeline = pipeline_class.from_single_file(
                        model_path,
                        torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                        use_safetensors=True
                    )
                else:
                    # 目录模型
                    self.current_pipeline = pipeline_class.from_pretrained(
                        model_path,
                        torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                        use_safetensors=True
                    )
                
                # 移动到设备
                self.current_pipeline = self.current_pipeline.to(self.device)
                
                # 优化设置
                if self.device == "cuda":
                    self.current_pipeline.enable_model_cpu_offload()
                    self.current_pipeline.enable_xformers_memory_efficient_attention()
                
                self.current_model = model_name
                logger.info(f"模型加载成功: {model_name}")
                return True
                
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            return False
    
    def generate_image(self, params: GenerationParams, 
                      progress_callback: Optional[Callable[[int, int, str], None]] = None) -> Optional[GenerationResult]:
        """生成图像"""
        if not self.current_pipeline:
            logger.error("没有加载模型")
            return None
        
        try:
            start_time = datetime.now()
            
            # 设置随机种子
            if params.seed is None:
                params.seed = torch.randint(0, 2**32, (1,)).item()
            
            generator = torch.Generator(device=self.device).manual_seed(params.seed)
            
            if progress_callback:
                progress_callback(0, params.steps, "开始生成图像...")
            
            # 生成参数
            generation_kwargs = {
                'prompt': params.prompt,
                'negative_prompt': params.negative_prompt,
                'width': params.width,
                'height': params.height,
                'num_inference_steps': params.steps,
                'guidance_scale': params.guidance_scale,
                'generator': generator,
                'num_images_per_prompt': params.batch_size
            }

            # 设置采样器
            if hasattr(self.current_pipeline, 'scheduler') and params.sampler in self.available_samplers:
                try:
                    # 动态导入采样器
                    sampler_name = self._get_sampler_class_name(params.sampler)
                    if sampler_name:
                        from diffusers import schedulers
                        sampler_class = getattr(schedulers, sampler_name, None)
                        if sampler_class:
                            self.current_pipeline.scheduler = sampler_class.from_config(
                                self.current_pipeline.scheduler.config
                            )
                            logger.info(f"使用采样器: {params.sampler}")
                except Exception as e:
                    logger.warning(f"设置采样器失败，使用默认采样器: {e}")
            
            # 执行生成
            with torch.no_grad():
                result = self.current_pipeline(**generation_kwargs)
            
            generation_time = (datetime.now() - start_time).total_seconds()
            
            if progress_callback:
                progress_callback(params.steps, params.steps, "生成完成!")
            
            return GenerationResult(
                images=result.images,
                params=params,
                generation_time=generation_time,
                seed_used=params.seed,
                model_used=self.current_model
            )
            
        except Exception as e:
            logger.error(f"图像生成失败: {e}")
            if progress_callback:
                progress_callback(0, params.steps, f"生成失败: {str(e)}")
            return None
    
    def get_available_models(self) -> Dict[str, Dict[str, Any]]:
        """获取可用模型列表"""
        return self.available_models.copy()
    
    def get_current_model(self) -> Optional[str]:
        """获取当前模型"""
        return self.current_model
    
    def unload_model(self):
        """卸载当前模型"""
        with self.generation_lock:
            if self.current_pipeline:
                del self.current_pipeline
                self.current_pipeline = None
                self.current_model = None
                torch.cuda.empty_cache()
                logger.info("模型已卸载")
    
    def reload_models(self):
        """重新扫描模型"""
        self.available_models = self._scan_models()
        logger.info(f"模型列表已刷新，发现 {len(self.available_models)} 个模型")

# 全局图像生成器实例
image_generator = ImageGenerator()
