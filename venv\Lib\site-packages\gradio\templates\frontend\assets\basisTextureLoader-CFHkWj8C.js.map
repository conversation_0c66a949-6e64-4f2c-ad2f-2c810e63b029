{"version": 3, "file": "basisTextureLoader-CFHkWj8C.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Misc/basisWorker.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Misc/basis.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/Loaders/basisTextureLoader.js"], "sourcesContent": ["import { Tools } from \"./tools.js\";\n/**\n * The worker function that gets converted to a blob url to pass into a worker.\n * To be used if a developer wants to create their own worker instance and inject it instead of using the default worker.\n */\nexport function workerFunction() {\n    const _BASIS_FORMAT = {\n        cTFETC1: 0,\n        cTFETC2: 1,\n        cTFBC1: 2,\n        cTFBC3: 3,\n        cTFBC4: 4,\n        cTFBC5: 5,\n        cTFBC7: 6,\n        cTFPVRTC1_4_RGB: 8,\n        cTFPVRTC1_4_RGBA: 9,\n        cTFASTC_4x4: 10,\n        cTFATC_RGB: 11,\n        cTFATC_RGBA_INTERPOLATED_ALPHA: 12,\n        cTFRGBA32: 13,\n        cTFRGB565: 14,\n        cTFBGR565: 15,\n        cTFRGBA4444: 16,\n        cTFFXT1_RGB: 17,\n        cTFPVRTC2_4_RGB: 18,\n        cTFPVRTC2_4_RGBA: 19,\n        cTFETC2_EAC_R11: 20,\n        cTFETC2_EAC_RG11: 21,\n    };\n    let transcoderModulePromise = null;\n    onmessage = (event) => {\n        if (event.data.action === \"init\") {\n            // Load the transcoder if it hasn't been yet\n            if (event.data.url) {\n                // make sure we loaded the script correctly\n                try {\n                    importScripts(event.data.url);\n                }\n                catch (e) {\n                    postMessage({ action: \"error\", error: e });\n                }\n            }\n            if (!transcoderModulePromise) {\n                transcoderModulePromise = BASIS({\n                    // Override wasm binary\n                    wasmBinary: event.data.wasmBinary,\n                });\n            }\n            if (transcoderModulePromise !== null) {\n                transcoderModulePromise.then((m) => {\n                    BASIS = m;\n                    m.initializeBasis();\n                    postMessage({ action: \"init\" });\n                });\n            }\n        }\n        else if (event.data.action === \"transcode\") {\n            // Transcode the basis image and return the resulting pixels\n            const config = event.data.config;\n            const imgData = event.data.imageData;\n            const loadedFile = new BASIS.BasisFile(imgData);\n            const fileInfo = GetFileInfo(loadedFile);\n            let format = event.data.ignoreSupportedFormats ? null : GetSupportedTranscodeFormat(event.data.config, fileInfo);\n            let needsConversion = false;\n            if (format === null) {\n                needsConversion = true;\n                format = fileInfo.hasAlpha ? _BASIS_FORMAT.cTFBC3 : _BASIS_FORMAT.cTFBC1;\n            }\n            // Begin transcode\n            let success = true;\n            if (!loadedFile.startTranscoding()) {\n                success = false;\n            }\n            const buffers = [];\n            for (let imageIndex = 0; imageIndex < fileInfo.images.length; imageIndex++) {\n                if (!success) {\n                    break;\n                }\n                const image = fileInfo.images[imageIndex];\n                if (config.loadSingleImage === undefined || config.loadSingleImage === imageIndex) {\n                    let mipCount = image.levels.length;\n                    if (config.loadMipmapLevels === false) {\n                        mipCount = 1;\n                    }\n                    for (let levelIndex = 0; levelIndex < mipCount; levelIndex++) {\n                        const levelInfo = image.levels[levelIndex];\n                        const pixels = TranscodeLevel(loadedFile, imageIndex, levelIndex, format, needsConversion);\n                        if (!pixels) {\n                            success = false;\n                            break;\n                        }\n                        levelInfo.transcodedPixels = pixels;\n                        buffers.push(levelInfo.transcodedPixels.buffer);\n                    }\n                }\n            }\n            // Close file\n            loadedFile.close();\n            loadedFile.delete();\n            if (needsConversion) {\n                format = -1;\n            }\n            if (!success) {\n                postMessage({ action: \"transcode\", success: success, id: event.data.id });\n            }\n            else {\n                postMessage({ action: \"transcode\", success: success, id: event.data.id, fileInfo: fileInfo, format: format }, buffers);\n            }\n        }\n    };\n    /**\n     * Detects the supported transcode format for the file\n     * @param config transcode config\n     * @param fileInfo info about the file\n     * @returns the chosed format or null if none are supported\n     */\n    function GetSupportedTranscodeFormat(config, fileInfo) {\n        let format = null;\n        if (config.supportedCompressionFormats) {\n            if (config.supportedCompressionFormats.astc) {\n                format = _BASIS_FORMAT.cTFASTC_4x4;\n            }\n            else if (config.supportedCompressionFormats.bc7) {\n                format = _BASIS_FORMAT.cTFBC7;\n            }\n            else if (config.supportedCompressionFormats.s3tc) {\n                format = fileInfo.hasAlpha ? _BASIS_FORMAT.cTFBC3 : _BASIS_FORMAT.cTFBC1;\n            }\n            else if (config.supportedCompressionFormats.pvrtc) {\n                format = fileInfo.hasAlpha ? _BASIS_FORMAT.cTFPVRTC1_4_RGBA : _BASIS_FORMAT.cTFPVRTC1_4_RGB;\n            }\n            else if (config.supportedCompressionFormats.etc2) {\n                format = _BASIS_FORMAT.cTFETC2;\n            }\n            else if (config.supportedCompressionFormats.etc1) {\n                format = _BASIS_FORMAT.cTFETC1;\n            }\n            else {\n                format = _BASIS_FORMAT.cTFRGB565;\n            }\n        }\n        return format;\n    }\n    /**\n     * Retrieves information about the basis file eg. dimensions\n     * @param basisFile the basis file to get the info from\n     * @returns information about the basis file\n     */\n    function GetFileInfo(basisFile) {\n        const hasAlpha = basisFile.getHasAlpha();\n        const imageCount = basisFile.getNumImages();\n        const images = [];\n        for (let i = 0; i < imageCount; i++) {\n            const imageInfo = {\n                levels: [],\n            };\n            const levelCount = basisFile.getNumLevels(i);\n            for (let level = 0; level < levelCount; level++) {\n                const levelInfo = {\n                    width: basisFile.getImageWidth(i, level),\n                    height: basisFile.getImageHeight(i, level),\n                };\n                imageInfo.levels.push(levelInfo);\n            }\n            images.push(imageInfo);\n        }\n        const info = { hasAlpha, images };\n        return info;\n    }\n    function TranscodeLevel(loadedFile, imageIndex, levelIndex, format, convertToRgb565) {\n        const dstSize = loadedFile.getImageTranscodedSizeInBytes(imageIndex, levelIndex, format);\n        let dst = new Uint8Array(dstSize);\n        if (!loadedFile.transcodeImage(dst, imageIndex, levelIndex, format, 1, 0)) {\n            return null;\n        }\n        // If no supported format is found, load as dxt and convert to rgb565\n        if (convertToRgb565) {\n            const alignedWidth = (loadedFile.getImageWidth(imageIndex, levelIndex) + 3) & ~3;\n            const alignedHeight = (loadedFile.getImageHeight(imageIndex, levelIndex) + 3) & ~3;\n            dst = ConvertDxtToRgb565(dst, 0, alignedWidth, alignedHeight);\n        }\n        return dst;\n    }\n    /**\n     * From https://github.com/BinomialLLC/basis_universal/blob/master/webgl/texture/dxt-to-rgb565.js\n     * An unoptimized version of dxtToRgb565.  Also, the floating\n     * point math used to compute the colors actually results in\n     * slightly different colors compared to hardware DXT decoders.\n     * @param src dxt src pixels\n     * @param srcByteOffset offset for the start of src\n     * @param  width aligned width of the image\n     * @param  height aligned height of the image\n     * @returns the converted pixels\n     */\n    function ConvertDxtToRgb565(src, srcByteOffset, width, height) {\n        const c = new Uint16Array(4);\n        const dst = new Uint16Array(width * height);\n        const blockWidth = width / 4;\n        const blockHeight = height / 4;\n        for (let blockY = 0; blockY < blockHeight; blockY++) {\n            for (let blockX = 0; blockX < blockWidth; blockX++) {\n                const i = srcByteOffset + 8 * (blockY * blockWidth + blockX);\n                c[0] = src[i] | (src[i + 1] << 8);\n                c[1] = src[i + 2] | (src[i + 3] << 8);\n                c[2] =\n                    ((2 * (c[0] & 0x1f) + 1 * (c[1] & 0x1f)) / 3) |\n                        (((2 * (c[0] & 0x7e0) + 1 * (c[1] & 0x7e0)) / 3) & 0x7e0) |\n                        (((2 * (c[0] & 0xf800) + 1 * (c[1] & 0xf800)) / 3) & 0xf800);\n                c[3] =\n                    ((2 * (c[1] & 0x1f) + 1 * (c[0] & 0x1f)) / 3) |\n                        (((2 * (c[1] & 0x7e0) + 1 * (c[0] & 0x7e0)) / 3) & 0x7e0) |\n                        (((2 * (c[1] & 0xf800) + 1 * (c[0] & 0xf800)) / 3) & 0xf800);\n                for (let row = 0; row < 4; row++) {\n                    const m = src[i + 4 + row];\n                    let dstI = (blockY * 4 + row) * width + blockX * 4;\n                    dst[dstI++] = c[m & 0x3];\n                    dst[dstI++] = c[(m >> 2) & 0x3];\n                    dst[dstI++] = c[(m >> 4) & 0x3];\n                    dst[dstI++] = c[(m >> 6) & 0x3];\n                }\n            }\n        }\n        return dst;\n    }\n}\n/**\n * Initialize a web worker with the basis transcoder\n * @param worker the worker to initialize\n * @param wasmBinary the wasm binary to load into the worker\n * @param moduleUrl the url to the basis transcoder module\n * @returns a promise that resolves when the worker is initialized\n */\nexport function initializeWebWorker(worker, wasmBinary, moduleUrl) {\n    return new Promise((res, reject) => {\n        const initHandler = (msg) => {\n            if (msg.data.action === \"init\") {\n                worker.removeEventListener(\"message\", initHandler);\n                res(worker);\n            }\n            else if (msg.data.action === \"error\") {\n                reject(msg.data.error || \"error initializing worker\");\n            }\n        };\n        worker.addEventListener(\"message\", initHandler);\n        // we can use transferable objects here because the worker will own the ArrayBuffer\n        worker.postMessage({ action: \"init\", url: moduleUrl ? Tools.GetBabylonScriptURL(moduleUrl) : undefined, wasmBinary }, [wasmBinary]);\n    });\n}\n//# sourceMappingURL=basisWorker.js.map", "import { Tools } from \"./tools.js\";\nimport { Texture } from \"../Materials/Textures/texture.js\";\nimport { InternalTexture } from \"../Materials/Textures/internalTexture.js\";\n\nimport { initializeWebWorker, workerFunction } from \"./basisWorker.js\";\n/**\n * Info about the .basis files\n */\nexport class BasisFileInfo {\n}\n/**\n * Result of transcoding a basis file\n */\nclass TranscodeResult {\n}\n/**\n * Configuration options for the Basis transcoder\n */\nexport class BasisTranscodeConfiguration {\n}\n/**\n * @internal\n * Enum of basis transcoder formats\n */\nvar BASIS_FORMATS;\n(function (BASIS_FORMATS) {\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFETC1\"] = 0] = \"cTFETC1\";\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFETC2\"] = 1] = \"cTFETC2\";\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFBC1\"] = 2] = \"cTFBC1\";\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFBC3\"] = 3] = \"cTFBC3\";\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFBC4\"] = 4] = \"cTFBC4\";\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFBC5\"] = 5] = \"cTFBC5\";\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFBC7\"] = 6] = \"cTFBC7\";\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFPVRTC1_4_RGB\"] = 8] = \"cTFPVRTC1_4_RGB\";\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFPVRTC1_4_RGBA\"] = 9] = \"cTFPVRTC1_4_RGBA\";\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFASTC_4x4\"] = 10] = \"cTFASTC_4x4\";\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFATC_RGB\"] = 11] = \"cTFATC_RGB\";\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFATC_RGBA_INTERPOLATED_ALPHA\"] = 12] = \"cTFATC_RGBA_INTERPOLATED_ALPHA\";\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFRGBA32\"] = 13] = \"cTFRGBA32\";\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFRGB565\"] = 14] = \"cTFRGB565\";\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFBGR565\"] = 15] = \"cTFBGR565\";\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFRGBA4444\"] = 16] = \"cTFRGBA4444\";\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFFXT1_RGB\"] = 17] = \"cTFFXT1_RGB\";\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFPVRTC2_4_RGB\"] = 18] = \"cTFPVRTC2_4_RGB\";\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFPVRTC2_4_RGBA\"] = 19] = \"cTFPVRTC2_4_RGBA\";\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFETC2_EAC_R11\"] = 20] = \"cTFETC2_EAC_R11\";\n    BASIS_FORMATS[BASIS_FORMATS[\"cTFETC2_EAC_RG11\"] = 21] = \"cTFETC2_EAC_RG11\";\n})(BASIS_FORMATS || (BASIS_FORMATS = {}));\n/**\n * Used to load .Basis files\n * See https://github.com/BinomialLLC/basis_universal/tree/master/webgl\n */\nexport const BasisToolsOptions = {\n    /**\n     * URL to use when loading the basis transcoder\n     */\n    JSModuleURL: `${Tools._DefaultCdnUrl}/basisTranscoder/1/basis_transcoder.js`,\n    /**\n     * URL to use when loading the wasm module for the transcoder\n     */\n    WasmModuleURL: `${Tools._DefaultCdnUrl}/basisTranscoder/1/basis_transcoder.wasm`,\n};\n/**\n * Get the internal format to be passed to texImage2D corresponding to the .basis format value\n * @param basisFormat format chosen from GetSupportedTranscodeFormat\n * @param engine\n * @returns internal format corresponding to the Basis format\n */\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport const GetInternalFormatFromBasisFormat = (basisFormat, engine) => {\n    let format;\n    switch (basisFormat) {\n        case BASIS_FORMATS.cTFETC1:\n            format = 36196;\n            break;\n        case BASIS_FORMATS.cTFBC1:\n            format = 33776;\n            break;\n        case BASIS_FORMATS.cTFBC4:\n            format = 33779;\n            break;\n        case BASIS_FORMATS.cTFASTC_4x4:\n            format = 37808;\n            break;\n        case BASIS_FORMATS.cTFETC2:\n            format = 37496;\n            break;\n        case BASIS_FORMATS.cTFBC7:\n            format = 36492;\n            break;\n    }\n    if (format === undefined) {\n        // eslint-disable-next-line no-throw-literal\n        throw \"The chosen Basis transcoder format is not currently supported\";\n    }\n    return format;\n};\nlet _WorkerPromise = null;\nlet _Worker = null;\nlet _actionId = 0;\nconst _IgnoreSupportedFormats = false;\nconst _CreateWorkerAsync = () => {\n    if (!_WorkerPromise) {\n        _WorkerPromise = new Promise((res, reject) => {\n            if (_Worker) {\n                res(_Worker);\n            }\n            else {\n                Tools.LoadFileAsync(Tools.GetBabylonScriptURL(BasisToolsOptions.WasmModuleURL))\n                    .then((wasmBinary) => {\n                    if (typeof URL !== \"function\") {\n                        return reject(\"Basis transcoder requires an environment with a URL constructor\");\n                    }\n                    const workerBlobUrl = URL.createObjectURL(new Blob([`(${workerFunction})()`], { type: \"application/javascript\" }));\n                    _Worker = new Worker(workerBlobUrl);\n                    initializeWebWorker(_Worker, wasmBinary, BasisToolsOptions.JSModuleURL).then(res, reject);\n                })\n                    .catch(reject);\n            }\n        });\n    }\n    return _WorkerPromise;\n};\n/**\n * Set the worker to use for transcoding\n * @param worker The worker that will be used for transcoding\n */\nexport const SetBasisTranscoderWorker = (worker) => {\n    _Worker = worker;\n};\n/**\n * Transcodes a loaded image file to compressed pixel data\n * @param data image data to transcode\n * @param config configuration options for the transcoding\n * @returns a promise resulting in the transcoded image\n */\nexport const TranscodeAsync = (data, config) => {\n    const dataView = data instanceof ArrayBuffer ? new Uint8Array(data) : data;\n    return new Promise((res, rej) => {\n        _CreateWorkerAsync().then(() => {\n            const actionId = _actionId++;\n            const messageHandler = (msg) => {\n                if (msg.data.action === \"transcode\" && msg.data.id === actionId) {\n                    _Worker.removeEventListener(\"message\", messageHandler);\n                    if (!msg.data.success) {\n                        rej(\"Transcode is not supported on this device\");\n                    }\n                    else {\n                        res(msg.data);\n                    }\n                }\n            };\n            _Worker.addEventListener(\"message\", messageHandler);\n            const dataViewCopy = new Uint8Array(dataView.byteLength);\n            dataViewCopy.set(new Uint8Array(dataView.buffer, dataView.byteOffset, dataView.byteLength));\n            _Worker.postMessage({ action: \"transcode\", id: actionId, imageData: dataViewCopy, config: config, ignoreSupportedFormats: _IgnoreSupportedFormats }, [\n                dataViewCopy.buffer,\n            ]);\n        }, (error) => {\n            rej(error);\n        });\n    });\n};\n/**\n * Binds a texture according to its underlying target.\n * @param texture texture to bind\n * @param engine the engine to bind the texture in\n */\nconst BindTexture = (texture, engine) => {\n    let target = engine._gl?.TEXTURE_2D;\n    if (texture.isCube) {\n        target = engine._gl?.TEXTURE_CUBE_MAP;\n    }\n    engine._bindTextureDirectly(target, texture, true);\n};\n/**\n * Loads a texture from the transcode result\n * @param texture texture load to\n * @param transcodeResult the result of transcoding the basis file to load from\n */\nexport const LoadTextureFromTranscodeResult = (texture, transcodeResult) => {\n    const engine = texture.getEngine();\n    for (let i = 0; i < transcodeResult.fileInfo.images.length; i++) {\n        const rootImage = transcodeResult.fileInfo.images[i].levels[0];\n        texture._invertVScale = texture.invertY;\n        if (transcodeResult.format === -1 || transcodeResult.format === BASIS_FORMATS.cTFRGB565) {\n            // No compatable compressed format found, fallback to RGB\n            texture.type = 10;\n            texture.format = 4;\n            if (engine._features.basisNeedsPOT && (Math.log2(rootImage.width) % 1 !== 0 || Math.log2(rootImage.height) % 1 !== 0)) {\n                // Create non power of two texture\n                const source = new InternalTexture(engine, 2 /* InternalTextureSource.Temp */);\n                texture._invertVScale = texture.invertY;\n                source.type = 10;\n                source.format = 4;\n                // Fallback requires aligned width/height\n                source.width = (rootImage.width + 3) & ~3;\n                source.height = (rootImage.height + 3) & ~3;\n                BindTexture(source, engine);\n                engine._uploadDataToTextureDirectly(source, new Uint16Array(rootImage.transcodedPixels.buffer), i, 0, 4, true);\n                // Resize to power of two\n                engine._rescaleTexture(source, texture, engine.scenes[0], engine._getInternalFormat(4), () => {\n                    engine._releaseTexture(source);\n                    BindTexture(texture, engine);\n                });\n            }\n            else {\n                // Fallback is already inverted\n                texture._invertVScale = !texture.invertY;\n                // Upload directly\n                texture.width = (rootImage.width + 3) & ~3;\n                texture.height = (rootImage.height + 3) & ~3;\n                texture.samplingMode = 2;\n                BindTexture(texture, engine);\n                engine._uploadDataToTextureDirectly(texture, new Uint16Array(rootImage.transcodedPixels.buffer), i, 0, 4, true);\n            }\n        }\n        else {\n            texture.width = rootImage.width;\n            texture.height = rootImage.height;\n            texture.generateMipMaps = transcodeResult.fileInfo.images[i].levels.length > 1;\n            const format = BasisTools.GetInternalFormatFromBasisFormat(transcodeResult.format, engine);\n            texture.format = format;\n            BindTexture(texture, engine);\n            // Upload all mip levels in the file\n            transcodeResult.fileInfo.images[i].levels.forEach((level, index) => {\n                engine._uploadCompressedDataToTextureDirectly(texture, format, level.width, level.height, level.transcodedPixels, i, index);\n            });\n            if (engine._features.basisNeedsPOT && (Math.log2(texture.width) % 1 !== 0 || Math.log2(texture.height) % 1 !== 0)) {\n                Tools.Warn(\"Loaded .basis texture width and height are not a power of two. Texture wrapping will be set to Texture.CLAMP_ADDRESSMODE as other modes are not supported with non power of two dimensions in webGL 1.\");\n                texture._cachedWrapU = Texture.CLAMP_ADDRESSMODE;\n                texture._cachedWrapV = Texture.CLAMP_ADDRESSMODE;\n            }\n        }\n    }\n};\n/**\n * Used to load .Basis files\n * See https://github.com/BinomialLLC/basis_universal/tree/master/webgl\n */\nexport const BasisTools = {\n    /**\n     * URL to use when loading the basis transcoder\n     */\n    JSModuleURL: BasisToolsOptions.JSModuleURL,\n    /**\n     * URL to use when loading the wasm module for the transcoder\n     */\n    WasmModuleURL: BasisToolsOptions.WasmModuleURL,\n    /**\n     * Get the internal format to be passed to texImage2D corresponding to the .basis format value\n     * @param basisFormat format chosen from GetSupportedTranscodeFormat\n     * @returns internal format corresponding to the Basis format\n     */\n    GetInternalFormatFromBasisFormat,\n    /**\n     * Transcodes a loaded image file to compressed pixel data\n     * @param data image data to transcode\n     * @param config configuration options for the transcoding\n     * @returns a promise resulting in the transcoded image\n     */\n    TranscodeAsync,\n    /**\n     * Loads a texture from the transcode result\n     * @param texture texture load to\n     * @param transcodeResult the result of transcoding the basis file to load from\n     */\n    LoadTextureFromTranscodeResult,\n};\nObject.defineProperty(BasisTools, \"JSModuleURL\", {\n    get: function () {\n        return BasisToolsOptions.JSModuleURL;\n    },\n    set: function (value) {\n        BasisToolsOptions.JSModuleURL = value;\n    },\n});\nObject.defineProperty(BasisTools, \"WasmModuleURL\", {\n    get: function () {\n        return BasisToolsOptions.WasmModuleURL;\n    },\n    set: function (value) {\n        BasisToolsOptions.WasmModuleURL = value;\n    },\n});\n//# sourceMappingURL=basis.js.map", "import { LoadTextureFromTranscodeResult, TranscodeAsync } from \"../../../Misc/basis.js\";\nimport { Tools } from \"../../../Misc/tools.js\";\n/**\n * Loader for .basis file format\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class _BasisTextureLoader {\n    constructor() {\n        /**\n         * Defines whether the loader supports cascade loading the different faces.\n         */\n        this.supportCascades = false;\n    }\n    /**\n     * Uploads the cube texture data to the WebGL texture. It has already been bound.\n     * @param data contains the texture data\n     * @param texture defines the BabylonJS internal texture\n     * @param createPolynomials will be true if polynomials have been requested\n     * @param onLoad defines the callback to trigger once the texture is ready\n     * @param onError defines the callback to trigger in case of error\n     */\n    loadCubeData(data, texture, createPolynomials, onLoad, onError) {\n        if (Array.isArray(data)) {\n            return;\n        }\n        const caps = texture.getEngine().getCaps();\n        const transcodeConfig = {\n            supportedCompressionFormats: {\n                etc1: caps.etc1 ? true : false,\n                s3tc: caps.s3tc ? true : false,\n                pvrtc: caps.pvrtc ? true : false,\n                etc2: caps.etc2 ? true : false,\n                astc: caps.astc ? true : false,\n                bc7: caps.bptc ? true : false,\n            },\n        };\n        TranscodeAsync(data, transcodeConfig)\n            .then((result) => {\n            const hasMipmap = result.fileInfo.images[0].levels.length > 1 && texture.generateMipMaps;\n            LoadTextureFromTranscodeResult(texture, result);\n            texture.getEngine()._setCubeMapTextureParams(texture, hasMipmap);\n            texture.isReady = true;\n            texture.onLoadedObservable.notifyObservers(texture);\n            texture.onLoadedObservable.clear();\n            if (onLoad) {\n                onLoad();\n            }\n        })\n            .catch((err) => {\n            const errorMessage = \"Failed to transcode Basis file, transcoding may not be supported on this device\";\n            Tools.Warn(errorMessage);\n            texture.isReady = true;\n            if (onError) {\n                onError(err);\n            }\n        });\n    }\n    /**\n     * Uploads the 2D texture data to the WebGL texture. It has already been bound once in the callback.\n     * @param data contains the texture data\n     * @param texture defines the BabylonJS internal texture\n     * @param callback defines the method to call once ready to upload\n     */\n    loadData(data, texture, callback) {\n        const caps = texture.getEngine().getCaps();\n        const transcodeConfig = {\n            supportedCompressionFormats: {\n                etc1: caps.etc1 ? true : false,\n                s3tc: caps.s3tc ? true : false,\n                pvrtc: caps.pvrtc ? true : false,\n                etc2: caps.etc2 ? true : false,\n                astc: caps.astc ? true : false,\n                bc7: caps.bptc ? true : false,\n            },\n        };\n        TranscodeAsync(data, transcodeConfig)\n            .then((result) => {\n            const rootImage = result.fileInfo.images[0].levels[0];\n            const hasMipmap = result.fileInfo.images[0].levels.length > 1 && texture.generateMipMaps;\n            callback(rootImage.width, rootImage.height, hasMipmap, result.format !== -1, () => {\n                LoadTextureFromTranscodeResult(texture, result);\n            });\n        })\n            .catch((err) => {\n            Tools.Warn(\"Failed to transcode Basis file, transcoding may not be supported on this device\");\n            Tools.Warn(`Failed to transcode Basis file: ${err}`);\n            callback(0, 0, false, false, () => { }, true);\n        });\n    }\n}\n//# sourceMappingURL=basisTextureLoader.js.map"], "names": ["workerFunction", "_BASIS_FORMAT", "transcoderModulePromise", "event", "e", "m", "config", "imgData", "loadedFile", "fileInfo", "GetFileInfo", "format", "GetSupportedTranscodeFormat", "needsConversion", "success", "buffers", "imageIndex", "image", "mipCount", "levelIndex", "levelInfo", "pixels", "TranscodeLevel", "basisFile", "has<PERSON><PERSON><PERSON>", "imageCount", "images", "i", "imageInfo", "levelCount", "level", "convertToRgb565", "dstSize", "dst", "alignedWidth", "alignedHeight", "ConvertDxtToRgb565", "src", "srcByteOffset", "width", "height", "c", "blockWidth", "blockHeight", "blockY", "blockX", "row", "dstI", "initializeWebWorker", "worker", "wasmBinary", "moduleUrl", "res", "reject", "initHandler", "msg", "Tools", "BASIS_FORMATS", "BasisToolsOptions", "GetInternalFormatFromBasisFormat", "basisFormat", "engine", "_WorkerPromise", "_Worker", "_actionId", "_IgnoreSupportedFormats", "_CreateWorkerAsync", "workerBlobUrl", "TranscodeAsync", "data", "dataView", "rej", "actionId", "messageHandler", "dataViewCopy", "error", "BindTexture", "texture", "target", "LoadTextureFromTranscodeResult", "transcodeResult", "rootImage", "source", "InternalTexture", "BasisTools", "index", "Texture", "value", "_BasisTexture<PERSON>oader", "createPolynomials", "onLoad", "onError", "caps", "transcodeConfig", "result", "hasMipmap", "err", "callback"], "mappings": "6GAKO,SAASA,GAAiB,CAC7B,MAAMC,EAAgB,CAClB,QAAS,EACT,QAAS,EACT,OAAQ,EACR,OAAQ,EACR,OAAQ,EACR,OAAQ,EACR,OAAQ,EACR,gBAAiB,EACjB,iBAAkB,EAClB,YAAa,GACb,WAAY,GACZ,+BAAgC,GAChC,UAAW,GACX,UAAW,GACX,UAAW,GACX,YAAa,GACb,YAAa,GACb,gBAAiB,GACjB,iBAAkB,GAClB,gBAAiB,GACjB,iBAAkB,EAC1B,EACI,IAAIC,EAA0B,KAC9B,UAAaC,GAAU,CACnB,GAAIA,EAAM,KAAK,SAAW,OAAQ,CAE9B,GAAIA,EAAM,KAAK,IAEX,GAAI,CACA,cAAcA,EAAM,KAAK,GAAG,CAC/B,OACMC,EAAG,CACN,YAAY,CAAE,OAAQ,QAAS,MAAOA,CAAG,CAAA,CAC5C,CAEAF,IACDA,EAA0B,MAAM,CAE5B,WAAYC,EAAM,KAAK,UAC3C,CAAiB,GAEDD,IAA4B,MAC5BA,EAAwB,KAAMG,GAAM,CAChC,MAAQA,EACRA,EAAE,gBAAe,EACjB,YAAY,CAAE,OAAQ,MAAM,CAAE,CAClD,CAAiB,CAER,SACQF,EAAM,KAAK,SAAW,YAAa,CAExC,MAAMG,EAASH,EAAM,KAAK,OACpBI,EAAUJ,EAAM,KAAK,UACrBK,EAAa,IAAI,MAAM,UAAUD,CAAO,EACxCE,EAAWC,EAAYF,CAAU,EACvC,IAAIG,EAASR,EAAM,KAAK,uBAAyB,KAAOS,EAA4BT,EAAM,KAAK,OAAQM,CAAQ,EAC3GI,EAAkB,GAClBF,IAAW,OACXE,EAAkB,GAClBF,EAASF,EAAS,SAAWR,EAAc,OAASA,EAAc,QAGtE,IAAIa,EAAU,GACTN,EAAW,qBACZM,EAAU,IAEd,MAAMC,EAAU,CAAA,EAChB,QAASC,EAAa,EAAGA,EAAaP,EAAS,OAAO,QAC7CK,EADqDE,IAAc,CAIxE,MAAMC,EAAQR,EAAS,OAAOO,CAAU,EACxC,GAAIV,EAAO,kBAAoB,QAAaA,EAAO,kBAAoBU,EAAY,CAC/E,IAAIE,EAAWD,EAAM,OAAO,OACxBX,EAAO,mBAAqB,KAC5BY,EAAW,GAEf,QAASC,EAAa,EAAGA,EAAaD,EAAUC,IAAc,CAC1D,MAAMC,EAAYH,EAAM,OAAOE,CAAU,EACnCE,EAASC,EAAed,EAAYQ,EAAYG,EAAYR,EAAQE,CAAe,EACzF,GAAI,CAACQ,EAAQ,CACTP,EAAU,GACV,KACH,CACDM,EAAU,iBAAmBC,EAC7BN,EAAQ,KAAKK,EAAU,iBAAiB,MAAM,CACjD,CACJ,CACJ,CAEDZ,EAAW,MAAK,EAChBA,EAAW,OAAM,EACbK,IACAF,EAAS,IAERG,EAID,YAAY,CAAE,OAAQ,YAAa,QAASA,EAAS,GAAIX,EAAM,KAAK,GAAI,SAAUM,EAAU,OAAQE,CAAM,EAAII,CAAO,EAHrH,YAAY,CAAE,OAAQ,YAAa,QAASD,EAAS,GAAIX,EAAM,KAAK,EAAE,CAAE,CAK/E,CACT,EAOI,SAASS,EAA4BN,EAAQG,EAAU,CACnD,IAAIE,EAAS,KACb,OAAIL,EAAO,8BACHA,EAAO,4BAA4B,KACnCK,EAASV,EAAc,YAElBK,EAAO,4BAA4B,IACxCK,EAASV,EAAc,OAElBK,EAAO,4BAA4B,KACxCK,EAASF,EAAS,SAAWR,EAAc,OAASA,EAAc,OAE7DK,EAAO,4BAA4B,MACxCK,EAASF,EAAS,SAAWR,EAAc,iBAAmBA,EAAc,gBAEvEK,EAAO,4BAA4B,KACxCK,EAASV,EAAc,QAElBK,EAAO,4BAA4B,KACxCK,EAASV,EAAc,QAGvBU,EAASV,EAAc,WAGxBU,CACV,CAMD,SAASD,EAAYa,EAAW,CAC5B,MAAMC,EAAWD,EAAU,cACrBE,EAAaF,EAAU,eACvBG,EAAS,CAAA,EACf,QAASC,EAAI,EAAGA,EAAIF,EAAYE,IAAK,CACjC,MAAMC,EAAY,CACd,OAAQ,CAAE,CAC1B,EACkBC,EAAaN,EAAU,aAAaI,CAAC,EAC3C,QAASG,EAAQ,EAAGA,EAAQD,EAAYC,IAAS,CAC7C,MAAMV,EAAY,CACd,MAAOG,EAAU,cAAcI,EAAGG,CAAK,EACvC,OAAQP,EAAU,eAAeI,EAAGG,CAAK,CAC7D,EACgBF,EAAU,OAAO,KAAKR,CAAS,CAClC,CACDM,EAAO,KAAKE,CAAS,CACxB,CAED,MADa,CAAE,SAAAJ,EAAU,OAAAE,EAE5B,CACD,SAASJ,EAAed,EAAYQ,EAAYG,EAAYR,EAAQoB,EAAiB,CACjF,MAAMC,EAAUxB,EAAW,8BAA8BQ,EAAYG,EAAYR,CAAM,EACvF,IAAIsB,EAAM,IAAI,WAAWD,CAAO,EAChC,GAAI,CAACxB,EAAW,eAAeyB,EAAKjB,EAAYG,EAAYR,EAAQ,EAAG,CAAC,EACpE,OAAO,KAGX,GAAIoB,EAAiB,CACjB,MAAMG,EAAgB1B,EAAW,cAAcQ,EAAYG,CAAU,EAAI,EAAK,GACxEgB,EAAiB3B,EAAW,eAAeQ,EAAYG,CAAU,EAAI,EAAK,GAChFc,EAAMG,EAAmBH,EAAK,EAAGC,EAAcC,CAAa,CAC/D,CACD,OAAOF,CACV,CAYD,SAASG,EAAmBC,EAAKC,EAAeC,EAAOC,EAAQ,CAC3D,MAAMC,EAAI,IAAI,YAAY,CAAC,EACrBR,EAAM,IAAI,YAAYM,EAAQC,CAAM,EACpCE,EAAaH,EAAQ,EACrBI,EAAcH,EAAS,EAC7B,QAASI,EAAS,EAAGA,EAASD,EAAaC,IACvC,QAASC,EAAS,EAAGA,EAASH,EAAYG,IAAU,CAChD,MAAMlB,EAAIW,EAAgB,GAAKM,EAASF,EAAaG,GACrDJ,EAAE,CAAC,EAAIJ,EAAIV,CAAC,EAAKU,EAAIV,EAAI,CAAC,GAAK,EAC/Bc,EAAE,CAAC,EAAIJ,EAAIV,EAAI,CAAC,EAAKU,EAAIV,EAAI,CAAC,GAAK,EACnCc,EAAE,CAAC,GACG,GAAKA,EAAE,CAAC,EAAI,IAAQ,GAAKA,EAAE,CAAC,EAAI,KAAS,GACpC,GAAKA,EAAE,CAAC,EAAI,MAAS,GAAKA,EAAE,CAAC,EAAI,OAAU,EAAK,MAChD,GAAKA,EAAE,CAAC,EAAI,OAAU,GAAKA,EAAE,CAAC,EAAI,QAAW,EAAK,MAC7DA,EAAE,CAAC,GACG,GAAKA,EAAE,CAAC,EAAI,IAAQ,GAAKA,EAAE,CAAC,EAAI,KAAS,GACpC,GAAKA,EAAE,CAAC,EAAI,MAAS,GAAKA,EAAE,CAAC,EAAI,OAAU,EAAK,MAChD,GAAKA,EAAE,CAAC,EAAI,OAAU,GAAKA,EAAE,CAAC,EAAI,QAAW,EAAK,MAC7D,QAASK,EAAM,EAAGA,EAAM,EAAGA,IAAO,CAC9B,MAAMzC,EAAIgC,EAAIV,EAAI,EAAImB,CAAG,EACzB,IAAIC,GAAQH,EAAS,EAAIE,GAAOP,EAAQM,EAAS,EACjDZ,EAAIc,GAAM,EAAIN,EAAEpC,EAAI,CAAG,EACvB4B,EAAIc,GAAM,EAAIN,EAAGpC,GAAK,EAAK,CAAG,EAC9B4B,EAAIc,GAAM,EAAIN,EAAGpC,GAAK,EAAK,CAAG,EAC9B4B,EAAIc,GAAM,EAAIN,EAAGpC,GAAK,EAAK,CAAG,CACjC,CACJ,CAEL,OAAO4B,CACV,CACL,CAQO,SAASe,EAAoBC,EAAQC,EAAYC,EAAW,CAC/D,OAAO,IAAI,QAAQ,CAACC,EAAKC,IAAW,CAChC,MAAMC,EAAeC,GAAQ,CACrBA,EAAI,KAAK,SAAW,QACpBN,EAAO,oBAAoB,UAAWK,CAAW,EACjDF,EAAIH,CAAM,GAELM,EAAI,KAAK,SAAW,SACzBF,EAAOE,EAAI,KAAK,OAAS,2BAA2B,CAEpE,EACQN,EAAO,iBAAiB,UAAWK,CAAW,EAE9CL,EAAO,YAAY,CAAE,OAAQ,OAAQ,IAAKE,EAAYK,EAAM,oBAAoBL,CAAS,EAAI,OAAW,WAAAD,CAAU,EAAI,CAACA,CAAU,CAAC,CAC1I,CAAK,CACL,CC/NA,IAAIO,GACH,SAAUA,EAAe,CACtBA,EAAcA,EAAc,QAAa,CAAC,EAAI,UAC9CA,EAAcA,EAAc,QAAa,CAAC,EAAI,UAC9CA,EAAcA,EAAc,OAAY,CAAC,EAAI,SAC7CA,EAAcA,EAAc,OAAY,CAAC,EAAI,SAC7CA,EAAcA,EAAc,OAAY,CAAC,EAAI,SAC7CA,EAAcA,EAAc,OAAY,CAAC,EAAI,SAC7CA,EAAcA,EAAc,OAAY,CAAC,EAAI,SAC7CA,EAAcA,EAAc,gBAAqB,CAAC,EAAI,kBACtDA,EAAcA,EAAc,iBAAsB,CAAC,EAAI,mBACvDA,EAAcA,EAAc,YAAiB,EAAE,EAAI,cACnDA,EAAcA,EAAc,WAAgB,EAAE,EAAI,aAClDA,EAAcA,EAAc,+BAAoC,EAAE,EAAI,iCACtEA,EAAcA,EAAc,UAAe,EAAE,EAAI,YACjDA,EAAcA,EAAc,UAAe,EAAE,EAAI,YACjDA,EAAcA,EAAc,UAAe,EAAE,EAAI,YACjDA,EAAcA,EAAc,YAAiB,EAAE,EAAI,cACnDA,EAAcA,EAAc,YAAiB,EAAE,EAAI,cACnDA,EAAcA,EAAc,gBAAqB,EAAE,EAAI,kBACvDA,EAAcA,EAAc,iBAAsB,EAAE,EAAI,mBACxDA,EAAcA,EAAc,gBAAqB,EAAE,EAAI,kBACvDA,EAAcA,EAAc,iBAAsB,EAAE,EAAI,kBAC5D,GAAGA,IAAkBA,EAAgB,CAAE,EAAC,EAKjC,MAAMC,EAAoB,CAI7B,YAAa,GAAGF,EAAM,cAAc,yCAIpC,cAAe,GAAGA,EAAM,cAAc,0CAC1C,EAQaG,EAAmC,CAACC,EAAaC,IAAW,CACrE,IAAIlD,EACJ,OAAQiD,EAAW,CACf,KAAKH,EAAc,QACf9C,EAAS,MACT,MACJ,KAAK8C,EAAc,OACf9C,EAAS,MACT,MACJ,KAAK8C,EAAc,OACf9C,EAAS,MACT,MACJ,KAAK8C,EAAc,YACf9C,EAAS,MACT,MACJ,KAAK8C,EAAc,QACf9C,EAAS,MACT,MACJ,KAAK8C,EAAc,OACf9C,EAAS,MACT,KACP,CACD,GAAIA,IAAW,OAEX,KAAM,gEAEV,OAAOA,CACX,EACA,IAAImD,EAAiB,KACjBC,EAAU,KACVC,EAAY,EAChB,MAAMC,EAA0B,GAC1BC,EAAqB,KAClBJ,IACDA,EAAiB,IAAI,QAAQ,CAACV,EAAKC,IAAW,CACtCU,EACAX,EAAIW,CAAO,EAGXP,EAAM,cAAcA,EAAM,oBAAoBE,EAAkB,aAAa,CAAC,EACzE,KAAMR,GAAe,CACtB,GAAI,OAAO,KAAQ,WACf,OAAOG,EAAO,iEAAiE,EAEnF,MAAMc,EAAgB,IAAI,gBAAgB,IAAI,KAAK,CAAC,IAAInE,CAAc,KAAK,EAAG,CAAE,KAAM,wBAAwB,CAAE,CAAC,EACjH+D,EAAU,IAAI,OAAOI,CAAa,EAClCnB,EAAoBe,EAASb,EAAYQ,EAAkB,WAAW,EAAE,KAAKN,EAAKC,CAAM,CAC5G,CAAiB,EACI,MAAMA,CAAM,CAEjC,CAAS,GAEES,GAeEM,EAAiB,CAACC,EAAM/D,IAAW,CAC5C,MAAMgE,EAAWD,aAAgB,YAAc,IAAI,WAAWA,CAAI,EAAIA,EACtE,OAAO,IAAI,QAAQ,CAACjB,EAAKmB,IAAQ,CAC7BL,EAAkB,EAAG,KAAK,IAAM,CAC5B,MAAMM,EAAWR,IACXS,EAAkBlB,GAAQ,CACxBA,EAAI,KAAK,SAAW,aAAeA,EAAI,KAAK,KAAOiB,IACnDT,EAAQ,oBAAoB,UAAWU,CAAc,EAChDlB,EAAI,KAAK,QAIVH,EAAIG,EAAI,IAAI,EAHZgB,EAAI,2CAA2C,EAMvE,EACYR,EAAQ,iBAAiB,UAAWU,CAAc,EAClD,MAAMC,EAAe,IAAI,WAAWJ,EAAS,UAAU,EACvDI,EAAa,IAAI,IAAI,WAAWJ,EAAS,OAAQA,EAAS,WAAYA,EAAS,UAAU,CAAC,EAC1FP,EAAQ,YAAY,CAAE,OAAQ,YAAa,GAAIS,EAAU,UAAWE,EAAc,OAAQpE,EAAQ,uBAAwB2D,CAAuB,EAAI,CACjJS,EAAa,MAC7B,CAAa,CACJ,EAAGC,GAAU,CACVJ,EAAII,CAAK,CACrB,CAAS,CACT,CAAK,CACL,EAMMC,EAAc,CAACC,EAAShB,IAAW,CACrC,IAAIiB,EAASjB,EAAO,KAAK,WACrBgB,EAAQ,SACRC,EAASjB,EAAO,KAAK,kBAEzBA,EAAO,qBAAqBiB,EAAQD,EAAS,EAAI,CACrD,EAMaE,EAAiC,CAACF,EAASG,IAAoB,CACxE,MAAMnB,EAASgB,EAAQ,YACvB,QAASlD,EAAI,EAAGA,EAAIqD,EAAgB,SAAS,OAAO,OAAQrD,IAAK,CAC7D,MAAMsD,EAAYD,EAAgB,SAAS,OAAOrD,CAAC,EAAE,OAAO,CAAC,EAE7D,GADAkD,EAAQ,cAAgBA,EAAQ,QAC5BG,EAAgB,SAAW,IAAMA,EAAgB,SAAWvB,EAAc,UAI1E,GAFAoB,EAAQ,KAAO,GACfA,EAAQ,OAAS,EACbhB,EAAO,UAAU,gBAAkB,KAAK,KAAKoB,EAAU,KAAK,EAAI,IAAM,GAAK,KAAK,KAAKA,EAAU,MAAM,EAAI,IAAM,GAAI,CAEnH,MAAMC,EAAS,IAAIC,EAAgBtB,EAAQ,CAAC,EAC5CgB,EAAQ,cAAgBA,EAAQ,QAChCK,EAAO,KAAO,GACdA,EAAO,OAAS,EAEhBA,EAAO,MAASD,EAAU,MAAQ,EAAK,GACvCC,EAAO,OAAUD,EAAU,OAAS,EAAK,GACzCL,EAAYM,EAAQrB,CAAM,EAC1BA,EAAO,6BAA6BqB,EAAQ,IAAI,YAAYD,EAAU,iBAAiB,MAAM,EAAGtD,EAAG,EAAG,EAAG,EAAI,EAE7GkC,EAAO,gBAAgBqB,EAAQL,EAAShB,EAAO,OAAO,CAAC,EAAGA,EAAO,mBAAmB,CAAC,EAAG,IAAM,CAC1FA,EAAO,gBAAgBqB,CAAM,EAC7BN,EAAYC,EAAShB,CAAM,CAC/C,CAAiB,CACJ,MAGGgB,EAAQ,cAAgB,CAACA,EAAQ,QAEjCA,EAAQ,MAASI,EAAU,MAAQ,EAAK,GACxCJ,EAAQ,OAAUI,EAAU,OAAS,EAAK,GAC1CJ,EAAQ,aAAe,EACvBD,EAAYC,EAAShB,CAAM,EAC3BA,EAAO,6BAA6BgB,EAAS,IAAI,YAAYI,EAAU,iBAAiB,MAAM,EAAGtD,EAAG,EAAG,EAAG,EAAI,MAGjH,CACDkD,EAAQ,MAAQI,EAAU,MAC1BJ,EAAQ,OAASI,EAAU,OAC3BJ,EAAQ,gBAAkBG,EAAgB,SAAS,OAAOrD,CAAC,EAAE,OAAO,OAAS,EAC7E,MAAMhB,EAASyE,EAAW,iCAAiCJ,EAAgB,OAAQnB,CAAM,EACzFgB,EAAQ,OAASlE,EACjBiE,EAAYC,EAAShB,CAAM,EAE3BmB,EAAgB,SAAS,OAAOrD,CAAC,EAAE,OAAO,QAAQ,CAACG,EAAOuD,IAAU,CAChExB,EAAO,uCAAuCgB,EAASlE,EAAQmB,EAAM,MAAOA,EAAM,OAAQA,EAAM,iBAAkBH,EAAG0D,CAAK,CAC1I,CAAa,EACGxB,EAAO,UAAU,gBAAkB,KAAK,KAAKgB,EAAQ,KAAK,EAAI,IAAM,GAAK,KAAK,KAAKA,EAAQ,MAAM,EAAI,IAAM,KAC3GrB,EAAM,KAAK,wMAAwM,EACnNqB,EAAQ,aAAeS,EAAQ,kBAC/BT,EAAQ,aAAeS,EAAQ,kBAEtC,CACJ,CACL,EAKaF,EAAa,CAItB,YAAa1B,EAAkB,YAI/B,cAAeA,EAAkB,cAMjC,iCAAAC,EAOA,eAAAS,EAMA,+BAAAW,CACJ,EACA,OAAO,eAAeK,EAAY,cAAe,CAC7C,IAAK,UAAY,CACb,OAAO1B,EAAkB,WAC5B,EACD,IAAK,SAAU6B,EAAO,CAClB7B,EAAkB,YAAc6B,CACnC,CACL,CAAC,EACD,OAAO,eAAeH,EAAY,gBAAiB,CAC/C,IAAK,UAAY,CACb,OAAO1B,EAAkB,aAC5B,EACD,IAAK,SAAU6B,EAAO,CAClB7B,EAAkB,cAAgB6B,CACrC,CACL,CAAC,ECrRM,MAAMC,CAAoB,CAC7B,aAAc,CAIV,KAAK,gBAAkB,EAC1B,CASD,aAAanB,EAAMQ,EAASY,EAAmBC,EAAQC,EAAS,CAC5D,GAAI,MAAM,QAAQtB,CAAI,EAClB,OAEJ,MAAMuB,EAAOf,EAAQ,UAAW,EAAC,QAAO,EAClCgB,EAAkB,CACpB,4BAA6B,CACzB,KAAM,EAAAD,EAAK,KACX,KAAM,EAAAA,EAAK,KACX,MAAO,EAAAA,EAAK,MACZ,KAAM,EAAAA,EAAK,KACX,KAAM,EAAAA,EAAK,KACX,IAAK,EAAAA,EAAK,IACb,CACb,EACQxB,EAAeC,EAAMwB,CAAe,EAC/B,KAAMC,GAAW,CAClB,MAAMC,EAAYD,EAAO,SAAS,OAAO,CAAC,EAAE,OAAO,OAAS,GAAKjB,EAAQ,gBACzEE,EAA+BF,EAASiB,CAAM,EAC9CjB,EAAQ,UAAW,EAAC,yBAAyBA,EAASkB,CAAS,EAC/DlB,EAAQ,QAAU,GAClBA,EAAQ,mBAAmB,gBAAgBA,CAAO,EAClDA,EAAQ,mBAAmB,QACvBa,GACAA,GAEhB,CAAS,EACI,MAAOM,GAAQ,CAEhBxC,EAAM,KADe,iFACE,EACvBqB,EAAQ,QAAU,GACdc,GACAA,EAAQK,CAAG,CAE3B,CAAS,CACJ,CAOD,SAAS3B,EAAMQ,EAASoB,EAAU,CAC9B,MAAML,EAAOf,EAAQ,UAAW,EAAC,QAAO,EAClCgB,EAAkB,CACpB,4BAA6B,CACzB,KAAM,EAAAD,EAAK,KACX,KAAM,EAAAA,EAAK,KACX,MAAO,EAAAA,EAAK,MACZ,KAAM,EAAAA,EAAK,KACX,KAAM,EAAAA,EAAK,KACX,IAAK,EAAAA,EAAK,IACb,CACb,EACQxB,EAAeC,EAAMwB,CAAe,EAC/B,KAAMC,GAAW,CAClB,MAAMb,EAAYa,EAAO,SAAS,OAAO,CAAC,EAAE,OAAO,CAAC,EAC9CC,EAAYD,EAAO,SAAS,OAAO,CAAC,EAAE,OAAO,OAAS,GAAKjB,EAAQ,gBACzEoB,EAAShB,EAAU,MAAOA,EAAU,OAAQc,EAAWD,EAAO,SAAW,GAAI,IAAM,CAC/Ef,EAA+BF,EAASiB,CAAM,CAC9D,CAAa,CACb,CAAS,EACI,MAAOE,GAAQ,CAChBxC,EAAM,KAAK,iFAAiF,EAC5FA,EAAM,KAAK,mCAAmCwC,CAAG,EAAE,EACnDC,EAAS,EAAG,EAAG,GAAO,GAAO,IAAM,CAAA,EAAK,EAAI,CACxD,CAAS,CACJ,CACL", "x_google_ignoreList": [0, 1, 2]}