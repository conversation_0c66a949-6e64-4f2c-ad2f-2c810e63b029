import{F as n}from"./KHR_interactivity-DTxiAnOo.js";import{R as r}from"./declarationMapper-BZjsjg7g.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./index-Dpxo-yl_.js";import"./objectModelMapping-BR4RdEzn.js";class d extends n{constructor(a){super();const t=a.glTF,s=t.animations?.map(o=>o._babylonAnimationGroup)||[];this.animationGroups=this.registerDataOutput("animationGroups",r,s);const i=t.nodes?.map(o=>o._babylonTransformNode)||[];this.nodes=this.registerDataOutput("nodes",r,i)}getClassName(){return"FlowGraphGLTFDataProvider"}}export{d as FlowGraphGLTFDataProvider};
//# sourceMappingURL=flowGraphGLTFDataProvider-O0ZVKuJg.js.map
