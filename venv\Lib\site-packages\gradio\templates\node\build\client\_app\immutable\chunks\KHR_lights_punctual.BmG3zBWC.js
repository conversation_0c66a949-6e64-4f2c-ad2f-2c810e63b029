import{N as T,V as n,aq as x,M as S,_ as f,s as u,R as Z,C as b,an as v,ao as C}from"./index.BoI39RQH.js";import{b as E,a as R}from"./objectModelMapping.ha_8hIyl.js";import{ArrayItem as M,GLTFLoader as D}from"./glTFLoader.BetPWe9U.js";T.AddNodeConstructor("Light_Type_1",(c,t)=>()=>new a(c,n.Zero(),t));class a extends E{get shadowFrustumSize(){return this._shadowFrustumSize}set shadowFrustumSize(t){this._shadowFrustumSize=t,this.forceProjectionMatrixCompute()}get shadowOrthoScale(){return this._shadowOrthoScale}set shadowOrthoScale(t){this._shadowOrthoScale=t,this.forceProjectionMatrixCompute()}get orthoLeft(){return this._orthoLeft}set orthoLeft(t){this._orthoLeft=t}get orthoRight(){return this._orthoRight}set orthoRight(t){this._orthoRight=t}get orthoTop(){return this._orthoTop}set orthoTop(t){this._orthoTop=t}get orthoBottom(){return this._orthoBottom}set orthoBottom(t){this._orthoBottom=t}constructor(t,e,h){super(t,h),this._shadowFrustumSize=0,this._shadowOrthoScale=.1,this.autoUpdateExtends=!0,this.autoCalcShadowZBounds=!1,this._orthoLeft=Number.MAX_VALUE,this._orthoRight=Number.MIN_VALUE,this._orthoTop=Number.MIN_VALUE,this._orthoBottom=Number.MAX_VALUE,this.position=e.scale(-1),this.direction=e}getClassName(){return"DirectionalLight"}getTypeID(){return x.LIGHTTYPEID_DIRECTIONALLIGHT}_setDefaultShadowProjectionMatrix(t,e,h){this.shadowFrustumSize>0?this._setDefaultFixedFrustumShadowProjectionMatrix(t):this._setDefaultAutoExtendShadowProjectionMatrix(t,e,h)}_setDefaultFixedFrustumShadowProjectionMatrix(t){const e=this.getScene().activeCamera;e&&S.OrthoLHToRef(this.shadowFrustumSize,this.shadowFrustumSize,this.shadowMinZ!==void 0?this.shadowMinZ:e.minZ,this.shadowMaxZ!==void 0?this.shadowMaxZ:e.maxZ,t,this.getScene().getEngine().isNDCHalfZRange)}_setDefaultAutoExtendShadowProjectionMatrix(t,e,h){const s=this.getScene().activeCamera;if(this.autoUpdateExtends||this._orthoLeft===Number.MAX_VALUE){const o=n.Zero();this._orthoLeft=Number.MAX_VALUE,this._orthoRight=-Number.MAX_VALUE,this._orthoTop=-Number.MAX_VALUE,this._orthoBottom=Number.MAX_VALUE;let p=Number.MAX_VALUE,w=-Number.MAX_VALUE;for(let L=0;L<h.length;L++){const A=h[L];if(!A)continue;const B=A.getBoundingInfo().boundingBox;for(let y=0;y<B.vectorsWorld.length;y++)n.TransformCoordinatesToRef(B.vectorsWorld[y],e,o),o.x<this._orthoLeft&&(this._orthoLeft=o.x),o.y<this._orthoBottom&&(this._orthoBottom=o.y),o.x>this._orthoRight&&(this._orthoRight=o.x),o.y>this._orthoTop&&(this._orthoTop=o.y),this.autoCalcShadowZBounds&&(o.z<p&&(p=o.z),o.z>w&&(w=o.z))}this.autoCalcShadowZBounds&&(this._shadowMinZ=p,this._shadowMaxZ=w)}const l=this._orthoRight-this._orthoLeft,d=this._orthoTop-this._orthoBottom,i=this.shadowMinZ!==void 0?this.shadowMinZ:(s==null?void 0:s.minZ)||0,r=this.shadowMaxZ!==void 0?this.shadowMaxZ:(s==null?void 0:s.maxZ)||1e4,_=this.getScene().getEngine().useReverseDepthBuffer;S.OrthoOffCenterLHToRef(this._orthoLeft-l*this.shadowOrthoScale,this._orthoRight+l*this.shadowOrthoScale,this._orthoBottom-d*this.shadowOrthoScale,this._orthoTop+d*this.shadowOrthoScale,_?r:i,_?i:r,t,this.getScene().getEngine().isNDCHalfZRange)}_buildUniformLayout(){this._uniformBuffer.addUniform("vLightData",4),this._uniformBuffer.addUniform("vLightDiffuse",4),this._uniformBuffer.addUniform("vLightSpecular",4),this._uniformBuffer.addUniform("shadowsInfo",3),this._uniformBuffer.addUniform("depthValues",2),this._uniformBuffer.create()}transferToEffect(t,e){return this.computeTransformedInformation()?(this._uniformBuffer.updateFloat4("vLightData",this.transformedDirection.x,this.transformedDirection.y,this.transformedDirection.z,1,e),this):(this._uniformBuffer.updateFloat4("vLightData",this.direction.x,this.direction.y,this.direction.z,1,e),this)}transferToNodeMaterialEffect(t,e){return this.computeTransformedInformation()?(t.setFloat3(e,this.transformedDirection.x,this.transformedDirection.y,this.transformedDirection.z),this):(t.setFloat3(e,this.direction.x,this.direction.y,this.direction.z),this)}getDepthMinZ(t){const e=this._scene.getEngine();return!e.useReverseDepthBuffer&&e.isNDCHalfZRange?0:1}getDepthMaxZ(t){const e=this._scene.getEngine();return e.useReverseDepthBuffer&&e.isNDCHalfZRange?0:1}prepareLightSpecificDefines(t,e){t["DIRLIGHT"+e]=!0}}f([u()],a.prototype,"shadowFrustumSize",null);f([u()],a.prototype,"shadowOrthoScale",null);f([u()],a.prototype,"autoUpdateExtends",void 0);f([u()],a.prototype,"autoCalcShadowZBounds",void 0);f([u("orthoLeft")],a.prototype,"_orthoLeft",void 0);f([u("orthoRight")],a.prototype,"_orthoRight",void 0);f([u("orthoTop")],a.prototype,"_orthoTop",void 0);f([u("orthoBottom")],a.prototype,"_orthoBottom",void 0);Z("BABYLON.DirectionalLight",a);T.AddNodeConstructor("Light_Type_0",(c,t)=>()=>new g(c,n.Zero(),t));class g extends E{get shadowAngle(){return this._shadowAngle}set shadowAngle(t){this._shadowAngle=t,this.forceProjectionMatrixCompute()}get direction(){return this._direction}set direction(t){const e=this.needCube();if(this._direction=t,this.needCube()!==e&&this._shadowGenerators){const h=this._shadowGenerators.values();for(let s=h.next();s.done!==!0;s=h.next())s.value.recreateShadowMap()}}constructor(t,e,h){super(t,h),this._shadowAngle=Math.PI/2,this.position=e}getClassName(){return"PointLight"}getTypeID(){return x.LIGHTTYPEID_POINTLIGHT}needCube(){return!this.direction}getShadowDirection(t){if(this.direction)return super.getShadowDirection(t);switch(t){case 0:return new n(1,0,0);case 1:return new n(-1,0,0);case 2:return new n(0,-1,0);case 3:return new n(0,1,0);case 4:return new n(0,0,1);case 5:return new n(0,0,-1)}return n.Zero()}_setDefaultShadowProjectionMatrix(t,e,h){const s=this.getScene().activeCamera;if(!s)return;const l=this.shadowMinZ!==void 0?this.shadowMinZ:s.minZ,d=this.shadowMaxZ!==void 0?this.shadowMaxZ:s.maxZ,i=this.getScene().getEngine().useReverseDepthBuffer;S.PerspectiveFovLHToRef(this.shadowAngle,1,i?d:l,i?l:d,t,!0,this._scene.getEngine().isNDCHalfZRange,void 0,i)}_buildUniformLayout(){this._uniformBuffer.addUniform("vLightData",4),this._uniformBuffer.addUniform("vLightDiffuse",4),this._uniformBuffer.addUniform("vLightSpecular",4),this._uniformBuffer.addUniform("vLightFalloff",4),this._uniformBuffer.addUniform("shadowsInfo",3),this._uniformBuffer.addUniform("depthValues",2),this._uniformBuffer.create()}transferToEffect(t,e){return this.computeTransformedInformation()?this._uniformBuffer.updateFloat4("vLightData",this.transformedPosition.x,this.transformedPosition.y,this.transformedPosition.z,0,e):this._uniformBuffer.updateFloat4("vLightData",this.position.x,this.position.y,this.position.z,0,e),this._uniformBuffer.updateFloat4("vLightFalloff",this.range,this._inverseSquaredRange,0,0,e),this}transferToNodeMaterialEffect(t,e){return this.computeTransformedInformation()?t.setFloat3(e,this.transformedPosition.x,this.transformedPosition.y,this.transformedPosition.z):t.setFloat3(e,this.position.x,this.position.y,this.position.z),this}prepareLightSpecificDefines(t,e){t["POINTLIGHT"+e]=!0}}f([u()],g.prototype,"shadowAngle",null);Z("BABYLON.PointLight",g);const m="KHR_lights_punctual";class N{constructor(t){this.name=m,this._loader=t,this.enabled=this._loader.isExtensionUsed(m)}dispose(){this._loader=null,delete this._lights}onLoading(){const t=this._loader.gltf.extensions;if(t&&t[this.name]){const e=t[this.name];this._lights=e.lights,M.Assign(this._lights)}}loadNodeAsync(t,e,h){return D.LoadExtensionAsync(t,e,this.name,(s,l)=>(this._loader._allMaterialsDirtyRequired=!0,this._loader.loadNodeAsync(t,e,d=>{let i;const r=M.Get(s,this._lights,l.light),_=r.name||d.name;switch(this._loader.babylonScene._blockEntityCollection=!!this._loader._assetContainer,r.type){case"directional":{const o=new a(_,n.Backward(),this._loader.babylonScene);o.position.setAll(0),i=o;break}case"point":{i=new g(_,n.Zero(),this._loader.babylonScene);break}case"spot":{const o=new R(_,n.Zero(),n.Backward(),0,1,this._loader.babylonScene);o.angle=(r.spot&&r.spot.outerConeAngle||Math.PI/4)*2,o.innerAngle=(r.spot&&r.spot.innerConeAngle||0)*2,i=o;break}default:throw this._loader.babylonScene._blockEntityCollection=!1,new Error(`${s}: Invalid light type (${r.type})`)}i._parentContainer=this._loader._assetContainer,this._loader.babylonScene._blockEntityCollection=!1,r._babylonLight=i,i.falloffType=x.FALLOFF_GLTF,i.diffuse=r.color?b.FromArray(r.color):b.White(),i.intensity=r.intensity==null?1:r.intensity,i.range=r.range==null?Number.MAX_VALUE:r.range,i.parent=d,this._loader._babylonLights.push(i),D.AddPointerMetadata(i,s),h(d)})))}}v(m);C(m,!0,c=>new N(c));export{N as KHR_lights};
//# sourceMappingURL=KHR_lights_punctual.BmG3zBWC.js.map
