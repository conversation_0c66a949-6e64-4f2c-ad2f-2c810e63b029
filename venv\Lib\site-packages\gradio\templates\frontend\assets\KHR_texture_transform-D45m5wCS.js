import{T as d,an as m,ao as c}from"./index-Dpxo-yl_.js";import{GLTFLoader as p}from"./glTFLoader-9Z3KGax5.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./bone-kZWM5-u7.js";import"./rawTexture-DmvUfjqF.js";import"./assetContainer-BRzQBugc.js";import"./objectModelMapping-BR4RdEzn.js";const s="KHR_texture_transform";class n{constructor(o){this.name=s,this._loader=o,this.enabled=this._loader.isExtensionUsed(s)}dispose(){this._loader=null}loadTextureInfoAsync(o,f,a){return p.LoadExtensionAsync(o,f,this.name,(e,r)=>this._loader.loadTextureInfoAsync(o,f,t=>{if(!(t instanceof d))throw new Error(`${e}: Texture type not supported`);r.offset&&(t.uOffset=r.offset[0],t.vOffset=r.offset[1]),t.uRotationCenter=0,t.vRotationCenter=0,r.rotation&&(t.wAng=-r.rotation),r.scale&&(t.uScale=r.scale[0],t.vScale=r.scale[1]),r.texCoord!=null&&(t.coordinatesIndex=r.texCoord),a(t)}))}}m(s);c(s,!0,i=>new n(i));export{n as KHR_texture_transform};
//# sourceMappingURL=KHR_texture_transform-D45m5wCS.js.map
