import{SvelteComponent as d,init as p,safe_not_equal as u,svg_element as n,claim_svg_element as a,children as s,detach as o,attr as l,insert_hydration as m,append_hydration as v,noop as h}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function f(c){let t,e;return{c(){t=n("svg"),e=n("path"),this.h()},l(r){t=a(r,"svg",{width:!0,height:!0,viewBox:!0});var i=s(t);e=a(i,"path",{fill:!0,d:!0}),s(e).forEach(o),i.forEach(o),this.h()},h(){l(e,"fill","currentColor"),l(e,"d","m31 16l-7 7l-1.41-1.41L28.17 16l-5.58-5.59L24 9l7 7zM1 16l7-7l1.41 1.41L3.83 16l5.58 5.59L8 23l-7-7zm11.42 9.484L17.64 6l1.932.517L14.352 26z"),l(t,"width","100%"),l(t,"height","100%"),l(t,"viewBox","0 0 32 32")},m(r,i){m(r,t,i),v(t,e)},p:h,i:h,o:h,d(r){r&&o(t)}}}class L extends d{constructor(t){super(),p(this,t,null,f,u,{})}}export{L as C};
//# sourceMappingURL=Code.DWo5KduI.js.map
