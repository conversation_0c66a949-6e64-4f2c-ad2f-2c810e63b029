{"version": 3, "file": "Index25-yqg76A6k.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Index25.js"], "sourcesContent": ["import{create_ssr_component as s,add_attribute as i,escape as n,add_styles as a}from\"svelte/internal\";const C={code:\"div.svelte-1nguped{border:var(--block-border-width) solid var(--border-color-primary);background:var(--block-border-color);border-radius:var(--block-radius);display:flex;flex-direction:column;gap:var(--form-gap-width);overflow:hidden}div.svelte-1nguped>*:not(.absolute){border:none;border-radius:0}.hide.svelte-1nguped{display:none}\",map:`{\"version\":3,\"file\":\"Index.svelte\",\"sources\":[\"Index.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">export let elem_id = \\\\\"\\\\\";\\\\nexport let elem_classes = [];\\\\nexport let visible = true;\\\\n<\\/script>\\\\n\\\\n<div\\\\n\\\\tid={elem_id}\\\\n\\\\tclass=\\\\\"gr-group {elem_classes.join(' ')}\\\\\"\\\\n\\\\tclass:hide={!visible}\\\\n>\\\\n\\\\t<div\\\\n\\\\t\\\\tclass=\\\\\"styler\\\\\"\\\\n\\\\t\\\\tstyle:--block-radius=\\\\\"0px\\\\\"\\\\n\\\\t\\\\tstyle:--block-border-width=\\\\\"0px\\\\\"\\\\n\\\\t\\\\tstyle:--layout-gap=\\\\\"1px\\\\\"\\\\n\\\\t\\\\tstyle:--form-gap-width=\\\\\"1px\\\\\"\\\\n\\\\t\\\\tstyle:--button-border-width=\\\\\"0px\\\\\"\\\\n\\\\t\\\\tstyle:--button-large-radius=\\\\\"0px\\\\\"\\\\n\\\\t\\\\tstyle:--button-small-radius=\\\\\"0px\\\\\"\\\\n\\\\t>\\\\n\\\\t\\\\t<slot />\\\\n\\\\t</div>\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\tdiv {\\\\n\\\\t\\\\tborder: var(--block-border-width) solid var(--border-color-primary);\\\\n\\\\t\\\\tbackground: var(--block-border-color);\\\\n\\\\t\\\\tborder-radius: var(--block-radius);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tgap: var(--form-gap-width);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}\\\\n\\\\tdiv > :global(*:not(.absolute)) {\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tborder-radius: 0;\\\\n\\\\t}\\\\n\\\\t.hide {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAyBC,kBAAI,CACH,MAAM,CAAE,IAAI,oBAAoB,CAAC,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CACnE,UAAU,CAAE,IAAI,oBAAoB,CAAC,CACrC,aAAa,CAAE,IAAI,cAAc,CAAC,CAClC,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,GAAG,CAAE,IAAI,gBAAgB,CAAC,CAC1B,QAAQ,CAAE,MACX,CACA,kBAAG,CAAW,gBAAkB,CAC/B,MAAM,CAAE,IAAI,CACZ,aAAa,CAAE,CAChB,CACA,oBAAM,CACL,OAAO,CAAE,IACV\"}`},v=s((o,e,t,d)=>{let{elem_id:A=\"\"}=e,{elem_classes:l=[]}=e,{visible:r=!0}=e;return e.elem_id===void 0&&t.elem_id&&A!==void 0&&t.elem_id(A),e.elem_classes===void 0&&t.elem_classes&&l!==void 0&&t.elem_classes(l),e.visible===void 0&&t.visible&&r!==void 0&&t.visible(r),o.css.add(C),`<div${i(\"id\",A,0)} class=\"${[\"gr-group \"+n(l.join(\" \"),!0)+\" svelte-1nguped\",r?\"\":\"hide\"].join(\" \").trim()}\"><div class=\"styler svelte-1nguped\"${a({\"--block-radius\":\"0px\",\"--block-border-width\":\"0px\",\"--layout-gap\":\"1px\",\"--form-gap-width\":\"1px\",\"--button-border-width\":\"0px\",\"--button-large-radius\":\"0px\",\"--button-small-radius\":\"0px\"})}>${d.default?d.default({}):\"\"}</div> </div>`});export{v as default};\n//# sourceMappingURL=Index25.js.map\n"], "names": ["s", "i", "n", "a"], "mappings": ";;AAA2G,MAAC,CAAC,CAAC,CAAC,IAAI,CAAC,8UAA8U,CAAC,GAAG,CAAC,CAAC,++CAA++C,CAAC,CAAC,CAAC,CAAC,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,KAA<PERSON>,CAAC,EAAE,CAAC,CAAC,YAAY,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAEC,aAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,WAAW,CAACC,MAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,oCAAoC,EAAEC,UAAC,CAAC,CAAC,gBAAgB,CAAC,KAAK,CAAC,sBAAsB,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,kBAAkB,CAAC,KAAK,CAAC,uBAAuB,CAAC,KAAK,CAAC,uBAAuB,CAAC,KAAK,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;;;;"}