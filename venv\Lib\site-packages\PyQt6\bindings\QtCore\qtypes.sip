// qtypes.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>EN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_5_0 -)
typedef unsigned char uchar;
%End
%If (Qt_6_5_0 -)
typedef unsigned short ushort;
%End
%If (Qt_6_5_0 -)
typedef unsigned int uint;
%End
%If (Qt_6_5_0 -)
typedef unsigned long ulong;
%End
%If (Qt_6_5_0 -)
typedef signed char qint8 /PyInt/;
%End
%If (Qt_6_5_0 -)
typedef unsigned char quint8 /PyInt/;
%End
%If (Qt_6_5_0 -)
typedef short qint16;
%End
%If (Qt_6_5_0 -)
typedef unsigned short quint16;
%End
%If (Qt_6_5_0 -)
typedef int qint32;
%End
%If (Qt_6_5_0 -)
typedef unsigned int quint32;
%End
%If (Qt_6_5_0 -)
typedef long long qint64;
%End
%If (Qt_6_5_0 -)
typedef unsigned long long quint64;
%End
%If (Qt_6_5_0 -)
typedef qint64 qlonglong;
%End
%If (Qt_6_5_0 -)
typedef quint64 qulonglong;
%End
%If (Qt_6_5_0 -)
%If (PyQt_qreal_double)
typedef double qreal;
%End
%End
%If (Qt_6_5_0 -)
%If (!PyQt_qreal_double)
typedef float qreal;
%End
%End
%If (Qt_6_5_0 -)
typedef long long qsizetype;
%End
