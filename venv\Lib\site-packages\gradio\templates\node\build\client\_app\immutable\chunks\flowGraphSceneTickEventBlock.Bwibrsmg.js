import{c as i}from"./KHR_interactivity.DEAVS2UW.js";import{R as s}from"./index.BoI39RQH.js";import{b as r}from"./declarationMapper.UBCwU7BT.js";class a extends i{constructor(){super(),this.type="SceneBeforeRender",this.timeSinceStart=this.registerDataOutput("timeSinceStart",r),this.deltaTime=this.registerDataOutput("deltaTime",r)}_preparePendingTasks(e){}_executeEvent(e,t){return this.timeSinceStart.setValue(t.timeSinceStart,e),this.deltaTime.setValue(t.deltaTime,e),this._execute(e),!0}_cancelPendingTasks(e){}getClassName(){return"FlowGraphSceneTickEventBlock"}}s("FlowGraphSceneTickEventBlock",a);export{a as FlowGraphSceneTickEventBlock};
//# sourceMappingURL=flowGraphSceneTickEventBlock.Bwibrsmg.js.map
