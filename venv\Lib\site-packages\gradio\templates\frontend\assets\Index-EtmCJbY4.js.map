{"version": 3, "file": "Index-EtmCJbY4.js", "sources": ["../../../../js/row/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport type { Gradio } from \"@gradio/utils\";\n\n\texport let equal_height = true;\n\texport let elem_id: string;\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let variant: \"default\" | \"panel\" | \"compact\" = \"default\";\n\texport let loading_status: LoadingStatus | undefined = undefined;\n\texport let gradio: Gradio | undefined = undefined;\n\texport let show_progress = false;\n\texport let height: number | string | undefined;\n\texport let min_height: number | string | undefined;\n\texport let max_height: number | string | undefined;\n\texport let scale: number | null = null;\n\n\tconst get_dimension = (\n\t\tdimension_value: string | number | undefined\n\t): string | undefined => {\n\t\tif (dimension_value === undefined) {\n\t\t\treturn undefined;\n\t\t}\n\t\tif (typeof dimension_value === \"number\") {\n\t\t\treturn dimension_value + \"px\";\n\t\t} else if (typeof dimension_value === \"string\") {\n\t\t\treturn dimension_value;\n\t\t}\n\t};\n</script>\n\n<div\n\tclass:compact={variant === \"compact\"}\n\tclass:panel={variant === \"panel\"}\n\tclass:unequal-height={equal_height === false}\n\tclass:stretch={equal_height}\n\tclass:hide={!visible}\n\tclass:grow-children={scale && scale >= 1}\n\tstyle:height={get_dimension(height)}\n\tstyle:max-height={get_dimension(max_height)}\n\tstyle:min-height={get_dimension(min_height)}\n\tstyle:flex-grow={scale}\n\tid={elem_id}\n\tclass=\"row {elem_classes.join(' ')}\"\n>\n\t{#if loading_status && show_progress && gradio}\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\tstatus={loading_status\n\t\t\t\t? loading_status.status == \"pending\"\n\t\t\t\t\t? \"generating\"\n\t\t\t\t\t: loading_status.status\n\t\t\t\t: null}\n\t\t/>\n\t{/if}\n\t<slot />\n</div>\n\n<style>\n\tdiv {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: var(--layout-gap);\n\t\twidth: var(--size-full);\n\t\tposition: relative;\n\t}\n\n\t.hide {\n\t\tdisplay: none;\n\t}\n\t.compact > :global(*),\n\t.compact :global(.box) {\n\t\tborder-radius: 0;\n\t}\n\t.compact,\n\t.panel {\n\t\tborder-radius: var(--container-radius);\n\t\tbackground: var(--background-fill-secondary);\n\t\tpadding: var(--size-2);\n\t}\n\t.unequal-height {\n\t\talign-items: flex-start;\n\t}\n\n\t.stretch {\n\t\talign-items: stretch;\n\t}\n\n\t.stretch > :global(.column > *),\n\t.stretch > :global(.column > .form > *) {\n\t\tflex-grow: 1;\n\t\tflex-shrink: 0;\n\t}\n\n\tdiv > :global(*),\n\tdiv > :global(.form > *) {\n\t\tflex: 1 1 0%;\n\t\tflex-wrap: wrap;\n\t\tmin-width: min(160px, 100%);\n\t}\n\n\t.grow-children > :global(.column) {\n\t\talign-self: stretch;\n\t}\n</style>\n"], "names": ["ctx", "dirty", "create_if_block", "toggle_class", "div", "set_style", "insert", "target", "anchor", "equal_height", "$$props", "elem_id", "elem_classes", "visible", "variant", "loading_status", "gradio", "show_progress", "height", "min_height", "max_height", "scale", "get_dimension", "dimension_value"], "mappings": "w0BAgDe,CAAA,WAAAA,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,CAAA,GACV,OAAAA,EAAA,CAAA,EACLA,EAAc,CAAA,EAAC,QAAU,UACxB,aACAA,EAAe,CAAA,EAAA,OAChB,wJAPSC,EAAA,IAAA,CAAA,WAAAD,KAAO,UAAU,EACvBC,EAAA,IAAA,CAAA,KAAAD,KAAO,IAAI,UACbA,EAAc,CAAA,CAAA,SACV,OAAAA,EAAA,CAAA,EACLA,EAAc,CAAA,EAAC,QAAU,UACxB,aACAA,EAAe,CAAA,EAAA,OAChB,iIATAA,EAAc,CAAA,GAAIA,EAAa,CAAA,GAAIA,EAAM,CAAA,GAAAE,EAAAF,CAAA,mGAH1CA,EAAO,CAAA,CAAA,uBACCA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,iBAAA,EAXlBG,EAAAC,EAAA,UAAAJ,OAAY,SAAS,EACvBG,EAAAC,EAAA,QAAAJ,OAAY,OAAO,EACVG,EAAAC,EAAA,iBAAAJ,OAAiB,EAAK,gBAC7BA,EAAY,CAAA,CAAA,cACdA,EAAO,CAAA,CAAA,sBACCA,EAAK,EAAA,GAAIA,EAAK,EAAA,GAAI,CAAC,EAC1BK,EAAAD,EAAA,SAAAJ,MAAcA,EAAM,CAAA,CAAA,CAAA,EAChBK,EAAAD,EAAA,aAAAJ,MAAcA,EAAU,EAAA,CAAA,CAAA,EACxBK,EAAAD,EAAA,aAAAJ,MAAcA,EAAU,CAAA,CAAA,CAAA,kBACzBA,EAAK,EAAA,CAAA,UAVvBM,EA2BKC,EAAAH,EAAAI,CAAA,sDAbCR,EAAc,CAAA,GAAIA,EAAa,CAAA,GAAIA,EAAM,CAAA,0LAH1CA,EAAO,CAAA,CAAA,0BACCA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,gDAXlBG,EAAAC,EAAA,UAAAJ,OAAY,SAAS,cACvBG,EAAAC,EAAA,QAAAJ,OAAY,OAAO,aACVG,EAAAC,EAAA,iBAAAJ,OAAiB,EAAK,2BAC7BA,EAAY,CAAA,CAAA,0BACdA,EAAO,CAAA,CAAA,oCACCA,EAAK,EAAA,GAAIA,EAAK,EAAA,GAAI,CAAC,SAC1BK,EAAAD,EAAA,SAAAJ,MAAcA,EAAM,CAAA,CAAA,CAAA,UAChBK,EAAAD,EAAA,aAAAJ,MAAcA,EAAU,EAAA,CAAA,CAAA,SACxBK,EAAAD,EAAA,aAAAJ,MAAcA,EAAU,CAAA,CAAA,CAAA,0BACzBA,EAAK,EAAA,CAAA,yIArCX,aAAAS,EAAe,EAAA,EAAAC,EACf,CAAA,QAAAC,CAAA,EAAAD,EACA,CAAA,aAAAE,EAAA,EAAA,EAAAF,GACA,QAAAG,EAAU,EAAA,EAAAH,GACV,QAAAI,EAA2C,SAAA,EAAAJ,GAC3C,eAAAK,EAA4C,MAAA,EAAAL,GAC5C,OAAAM,EAA6B,MAAA,EAAAN,GAC7B,cAAAO,EAAgB,EAAA,EAAAP,EAChB,CAAA,OAAAQ,CAAA,EAAAR,EACA,CAAA,WAAAS,CAAA,EAAAT,EACA,CAAA,WAAAU,CAAA,EAAAV,GACA,MAAAW,EAAuB,IAAA,EAAAX,QAE5BY,EACLC,GAAA,IAEIA,IAAoB,kBAGbA,GAAoB,gBACvBA,EAAkB,eACRA,GAAoB,SAC9B,OAAAA"}