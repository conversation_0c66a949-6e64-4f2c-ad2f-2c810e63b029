import{ab as i,h as B,M as c,aa as s,ac as l,ad as D,a as u,V as x}from"./index.BoI39RQH.js";i.prototype.thinInstanceAdd=function(t,e=!0){if(!this.getScene().getEngine().getCaps().instancedArrays)return <PERSON><PERSON>r("Thin Instances are not supported on this device as Instanced Array extension not supported"),-1;this._thinInstanceUpdateBufferSize("matrix",Array.isArray(t)?t.length:1);const n=this._thinInstanceDataStorage.instancesCount;if(Array.isArray(t))for(let a=0;a<t.length;++a)this.thinInstanceSetMatrixAt(this._thinInstanceDataStorage.instancesCount++,t[a],a===t.length-1&&e);else this.thinInstanceSetMatrixAt(this._thinInstanceDataStorage.instancesCount++,t,e);return n};i.prototype.thinInstanceAddSelf=function(t=!0){return this.thinInstanceAdd(c.IdentityReadOnly,t)};i.prototype.thinInstanceRegisterAttribute=function(t,e){t===s.ColorKind&&(t=s.ColorInstanceKind),this.removeVerticesData(t),this._thinInstanceInitializeUserStorage(),this._userThinInstanceBuffersStorage.strides[t]=e,this._userThinInstanceBuffersStorage.sizes[t]=e*Math.max(32,this._thinInstanceDataStorage.instancesCount),this._userThinInstanceBuffersStorage.data[t]=new Float32Array(this._userThinInstanceBuffersStorage.sizes[t]),this._userThinInstanceBuffersStorage.vertexBuffers[t]=new s(this.getEngine(),this._userThinInstanceBuffersStorage.data[t],t,!0,!1,e,!0),this.setVerticesBuffer(this._userThinInstanceBuffersStorage.vertexBuffers[t])};i.prototype.thinInstanceSetMatrixAt=function(t,e,n=!0){if(!this._thinInstanceDataStorage.matrixData||t>=this._thinInstanceDataStorage.instancesCount)return!1;const a=this._thinInstanceDataStorage.matrixData;return e.copyToArray(a,t*16),this._thinInstanceDataStorage.worldMatrices&&(this._thinInstanceDataStorage.worldMatrices[t]=e),n&&(this.thinInstanceBufferUpdated("matrix"),this.doNotSyncBoundingInfo||this.thinInstanceRefreshBoundingInfo(!1)),!0};i.prototype.thinInstanceSetAttributeAt=function(t,e,n,a=!0){return t===s.ColorKind&&(t=s.ColorInstanceKind),!this._userThinInstanceBuffersStorage||!this._userThinInstanceBuffersStorage.data[t]||e>=this._thinInstanceDataStorage.instancesCount?!1:(this._thinInstanceUpdateBufferSize(t,0),this._userThinInstanceBuffersStorage.data[t].set(n,e*this._userThinInstanceBuffersStorage.strides[t]),a&&this.thinInstanceBufferUpdated(t),!0)};Object.defineProperty(i.prototype,"thinInstanceCount",{get:function(){return this._thinInstanceDataStorage.instancesCount},set:function(t){var a;const e=this._thinInstanceDataStorage.matrixData??((a=this.source)==null?void 0:a._thinInstanceDataStorage.matrixData),n=e?e.length/16:0;t<=n&&(this._thinInstanceDataStorage.instancesCount=t)},enumerable:!0,configurable:!0});i.prototype._thinInstanceCreateMatrixBuffer=function(t,e,n=!0){const a=new l(this.getEngine(),e,!n,16,!1,!0);for(let r=0;r<4;r++)this.setVerticesBuffer(a.createVertexBuffer(t+r,r*4,4));return a};i.prototype.thinInstanceSetBuffer=function(t,e,n=0,a=!0){var r,o,h;n=n||16,t==="matrix"?((r=this._thinInstanceDataStorage.matrixBuffer)==null||r.dispose(),this._thinInstanceDataStorage.matrixBuffer=null,this._thinInstanceDataStorage.matrixBufferSize=e?e.length:32*n,this._thinInstanceDataStorage.matrixData=e,this._thinInstanceDataStorage.worldMatrices=null,e!==null?(this._thinInstanceDataStorage.instancesCount=e.length/n,this._thinInstanceDataStorage.matrixBuffer=this._thinInstanceCreateMatrixBuffer("world",e,a),this.doNotSyncBoundingInfo||this.thinInstanceRefreshBoundingInfo(!1)):(this._thinInstanceDataStorage.instancesCount=0,this.doNotSyncBoundingInfo||this.refreshBoundingInfo())):t==="previousMatrix"?((o=this._thinInstanceDataStorage.previousMatrixBuffer)==null||o.dispose(),this._thinInstanceDataStorage.previousMatrixBuffer=null,this._thinInstanceDataStorage.previousMatrixData=e,e!==null&&(this._thinInstanceDataStorage.previousMatrixBuffer=this._thinInstanceCreateMatrixBuffer("previousWorld",e,a))):(t===s.ColorKind&&(t=s.ColorInstanceKind),e===null?(h=this._userThinInstanceBuffersStorage)!=null&&h.data[t]&&(this.removeVerticesData(t),delete this._userThinInstanceBuffersStorage.data[t],delete this._userThinInstanceBuffersStorage.strides[t],delete this._userThinInstanceBuffersStorage.sizes[t],delete this._userThinInstanceBuffersStorage.vertexBuffers[t]):(this._thinInstanceInitializeUserStorage(),this._userThinInstanceBuffersStorage.data[t]=e,this._userThinInstanceBuffersStorage.strides[t]=n,this._userThinInstanceBuffersStorage.sizes[t]=e.length,this._userThinInstanceBuffersStorage.vertexBuffers[t]=new s(this.getEngine(),e,t,!a,!1,n,!0),this.setVerticesBuffer(this._userThinInstanceBuffersStorage.vertexBuffers[t])))};i.prototype.thinInstanceBufferUpdated=function(t){var e,n,a;t==="matrix"?(this.thinInstanceAllowAutomaticStaticBufferRecreation&&this._thinInstanceDataStorage.matrixBuffer&&!this._thinInstanceDataStorage.matrixBuffer.isUpdatable()&&this._thinInstanceRecreateBuffer(t),(e=this._thinInstanceDataStorage.matrixBuffer)==null||e.updateDirectly(this._thinInstanceDataStorage.matrixData,0,this._thinInstanceDataStorage.instancesCount)):t==="previousMatrix"?(this.thinInstanceAllowAutomaticStaticBufferRecreation&&this._thinInstanceDataStorage.previousMatrixBuffer&&!this._thinInstanceDataStorage.previousMatrixBuffer.isUpdatable()&&this._thinInstanceRecreateBuffer(t),(n=this._thinInstanceDataStorage.previousMatrixBuffer)==null||n.updateDirectly(this._thinInstanceDataStorage.previousMatrixData,0,this._thinInstanceDataStorage.instancesCount)):(t===s.ColorKind&&(t=s.ColorInstanceKind),(a=this._userThinInstanceBuffersStorage)!=null&&a.vertexBuffers[t]&&(this.thinInstanceAllowAutomaticStaticBufferRecreation&&!this._userThinInstanceBuffersStorage.vertexBuffers[t].isUpdatable()&&this._thinInstanceRecreateBuffer(t),this._userThinInstanceBuffersStorage.vertexBuffers[t].updateDirectly(this._userThinInstanceBuffersStorage.data[t],0)))};i.prototype.thinInstancePartialBufferUpdate=function(t,e,n){var a;t==="matrix"?this._thinInstanceDataStorage.matrixBuffer&&this._thinInstanceDataStorage.matrixBuffer.updateDirectly(e,n):(t===s.ColorKind&&(t=s.ColorInstanceKind),(a=this._userThinInstanceBuffersStorage)!=null&&a.vertexBuffers[t]&&this._userThinInstanceBuffersStorage.vertexBuffers[t].updateDirectly(e,n))};i.prototype.thinInstanceGetWorldMatrices=function(){if(!this._thinInstanceDataStorage.matrixData||!this._thinInstanceDataStorage.matrixBuffer)return[];const t=this._thinInstanceDataStorage.matrixData;if(!this._thinInstanceDataStorage.worldMatrices){this._thinInstanceDataStorage.worldMatrices=[];for(let e=0;e<this._thinInstanceDataStorage.instancesCount;++e)this._thinInstanceDataStorage.worldMatrices[e]=c.FromArray(t,e*16)}return this._thinInstanceDataStorage.worldMatrices};i.prototype.thinInstanceRefreshBoundingInfo=function(t=!1,e=!1,n=!1){if(!this._thinInstanceDataStorage.matrixData||!this._thinInstanceDataStorage.matrixBuffer)return;const a=this._thinInstanceDataStorage.boundingVectors;if(t||!this.rawBoundingInfo){a.length=0,this.refreshBoundingInfo(e,n);const h=this.getBoundingInfo();this.rawBoundingInfo=new D(h.minimum,h.maximum)}const r=this.getBoundingInfo(),o=this._thinInstanceDataStorage.matrixData;if(a.length===0)for(let h=0;h<r.boundingBox.vectors.length;++h)a.push(r.boundingBox.vectors[h].clone());u.Vector3[0].setAll(Number.POSITIVE_INFINITY),u.Vector3[1].setAll(Number.NEGATIVE_INFINITY);for(let h=0;h<this._thinInstanceDataStorage.instancesCount;++h){c.FromArrayToRef(o,h*16,u.Matrix[0]);for(let f=0;f<a.length;++f)x.TransformCoordinatesToRef(a[f],u.Matrix[0],u.Vector3[2]),u.Vector3[0].minimizeInPlace(u.Vector3[2]),u.Vector3[1].maximizeInPlace(u.Vector3[2])}r.reConstruct(u.Vector3[0],u.Vector3[1]),this._updateBoundingInfo()};i.prototype._thinInstanceRecreateBuffer=function(t,e=!0){var n,a,r;t==="matrix"?((n=this._thinInstanceDataStorage.matrixBuffer)==null||n.dispose(),this._thinInstanceDataStorage.matrixBuffer=this._thinInstanceCreateMatrixBuffer("world",this._thinInstanceDataStorage.matrixData,e)):t==="previousMatrix"?this._scene.needsPreviousWorldMatrices&&((a=this._thinInstanceDataStorage.previousMatrixBuffer)==null||a.dispose(),this._thinInstanceDataStorage.previousMatrixBuffer=this._thinInstanceCreateMatrixBuffer("previousWorld",this._thinInstanceDataStorage.previousMatrixData??this._thinInstanceDataStorage.matrixData,e)):(t===s.ColorKind&&(t=s.ColorInstanceKind),(r=this._userThinInstanceBuffersStorage.vertexBuffers[t])==null||r.dispose(),this._userThinInstanceBuffersStorage.vertexBuffers[t]=new s(this.getEngine(),this._userThinInstanceBuffersStorage.data[t],t,!e,!1,this._userThinInstanceBuffersStorage.strides[t],!0),this.setVerticesBuffer(this._userThinInstanceBuffersStorage.vertexBuffers[t]))};i.prototype._thinInstanceUpdateBufferSize=function(t,e=1){var I,g,S;t===s.ColorKind&&(t=s.ColorInstanceKind);const n=t==="matrix";if(!n&&(!this._userThinInstanceBuffersStorage||!this._userThinInstanceBuffersStorage.strides[t]))return;const a=n?16:this._userThinInstanceBuffersStorage.strides[t],r=n?this._thinInstanceDataStorage.matrixBufferSize:this._userThinInstanceBuffersStorage.sizes[t];let o=n?this._thinInstanceDataStorage.matrixData:this._userThinInstanceBuffersStorage.data[t];const h=(this._thinInstanceDataStorage.instancesCount+e)*a;let f=r;for(;f<h;)f*=2;if(!o||r!=f){if(!o)o=new Float32Array(f);else{const _=new Float32Array(f);_.set(o,0),o=_}n?((I=this._thinInstanceDataStorage.matrixBuffer)==null||I.dispose(),this._thinInstanceDataStorage.matrixBuffer=this._thinInstanceCreateMatrixBuffer("world",o,!1),this._thinInstanceDataStorage.matrixData=o,this._thinInstanceDataStorage.matrixBufferSize=f,this._scene.needsPreviousWorldMatrices&&!this._thinInstanceDataStorage.previousMatrixData&&((g=this._thinInstanceDataStorage.previousMatrixBuffer)==null||g.dispose(),this._thinInstanceDataStorage.previousMatrixBuffer=this._thinInstanceCreateMatrixBuffer("previousWorld",o,!1))):((S=this._userThinInstanceBuffersStorage.vertexBuffers[t])==null||S.dispose(),this._userThinInstanceBuffersStorage.data[t]=o,this._userThinInstanceBuffersStorage.sizes[t]=f,this._userThinInstanceBuffersStorage.vertexBuffers[t]=new s(this.getEngine(),o,t,!0,!1,a,!0),this.setVerticesBuffer(this._userThinInstanceBuffersStorage.vertexBuffers[t]))}};i.prototype._thinInstanceInitializeUserStorage=function(){this._userThinInstanceBuffersStorage||(this._userThinInstanceBuffersStorage={data:{},sizes:{},vertexBuffers:{},strides:{}})};i.prototype._disposeThinInstanceSpecificData=function(){var t;(t=this._thinInstanceDataStorage)!=null&&t.matrixBuffer&&(this._thinInstanceDataStorage.matrixBuffer.dispose(),this._thinInstanceDataStorage.matrixBuffer=null)};
//# sourceMappingURL=thinInstanceMesh.BW3MxSeP.js.map
