import{ar as c,an as h,ao as p}from"./index.BoI39RQH.js";import{GLTFLoader as u}from"./glTFLoader.BetPWe9U.js";const s="MSFT_minecraftMesh";class a{constructor(r){this.name=s,this._loader=r,this.enabled=this._loader.isExtensionUsed(s)}dispose(){this._loader=null}loadMaterialPropertiesAsync(r,i,e){return u.LoadExtraAsync(r,i,this.name,(n,o)=>{if(o){if(!(e instanceof c))throw new Error(`${n}: Material type not supported`);const d=this._loader.loadMaterialPropertiesAsync(r,i,e);return e.needAlphaBlending()&&(e.forceDepthWrite=!0,e.separateCullingPass=!0),e.backFaceCulling=e.forceDepthWrite,e.twoSidedLighting=!0,d}return null})}}h(s);p(s,!0,t=>new a(t));export{a as MSFT_minecraftMesh};
//# sourceMappingURL=MSFT_minecraftMesh.gCTYVD9T.js.map
