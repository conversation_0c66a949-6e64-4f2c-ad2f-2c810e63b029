{"version": 3, "file": "animationGroup.CYmPDV4x.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Animations/runtimeAnimation.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Animations/animatable.core.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Animations/animatable.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Animations/animationGroup.js"], "sourcesContent": ["import { Matrix } from \"../Maths/math.vector.js\";\nimport { Animation, _staticOffsetValueColor3, _staticOffsetValueColor4, _staticOffsetValueQuaternion, _staticOffsetValueSize, _staticOffsetValueVector2, _staticOffsetValueVector3, } from \"./animation.js\";\n/**\n * Defines a runtime animation\n */\nexport class RuntimeAnimation {\n    /**\n     * Gets the current frame of the runtime animation\n     */\n    get currentFrame() {\n        return this._currentFrame;\n    }\n    /**\n     * Gets the weight of the runtime animation\n     */\n    get weight() {\n        return this._weight;\n    }\n    /**\n     * Gets the current value of the runtime animation\n     */\n    get currentValue() {\n        return this._currentValue;\n    }\n    /**\n     * Gets or sets the target path of the runtime animation\n     */\n    get targetPath() {\n        return this._targetPath;\n    }\n    /**\n     * Gets the actual target of the runtime animation\n     */\n    get target() {\n        return this._currentActiveTarget;\n    }\n    /**\n     * Gets the additive state of the runtime animation\n     */\n    get isAdditive() {\n        return this._host && this._host.isAdditive;\n    }\n    /**\n     * Create a new RuntimeAnimation object\n     * @param target defines the target of the animation\n     * @param animation defines the source animation object\n     * @param scene defines the hosting scene\n     * @param host defines the initiating Animatable\n     */\n    constructor(target, animation, scene, host) {\n        this._events = new Array();\n        /**\n         * The current frame of the runtime animation\n         */\n        this._currentFrame = 0;\n        /**\n         * The original value of the runtime animation\n         */\n        this._originalValue = new Array();\n        /**\n         * The original blend value of the runtime animation\n         */\n        this._originalBlendValue = null;\n        /**\n         * The offsets cache of the runtime animation\n         */\n        this._offsetsCache = {};\n        /**\n         * The high limits cache of the runtime animation\n         */\n        this._highLimitsCache = {};\n        /**\n         * Specifies if the runtime animation has been stopped\n         */\n        this._stopped = false;\n        /**\n         * The blending factor of the runtime animation\n         */\n        this._blendingFactor = 0;\n        /**\n         * The current value of the runtime animation\n         */\n        this._currentValue = null;\n        this._currentActiveTarget = null;\n        this._directTarget = null;\n        /**\n         * The target path of the runtime animation\n         */\n        this._targetPath = \"\";\n        /**\n         * The weight of the runtime animation\n         */\n        this._weight = 1.0;\n        /**\n         * The absolute frame offset of the runtime animation\n         */\n        this._absoluteFrameOffset = 0;\n        /**\n         * The previous elapsed time (since start of animation) of the runtime animation\n         */\n        this._previousElapsedTime = 0;\n        this._yoyoDirection = 1;\n        /**\n         * The previous absolute frame of the runtime animation (meaning, without taking into account the from/to values, only the elapsed time and the fps)\n         */\n        this._previousAbsoluteFrame = 0;\n        this._targetIsArray = false;\n        this._animation = animation;\n        this._target = target;\n        this._scene = scene;\n        this._host = host;\n        this._activeTargets = [];\n        animation._runtimeAnimations.push(this);\n        // State\n        this._animationState = {\n            key: 0,\n            repeatCount: 0,\n            loopMode: this._getCorrectLoopMode(),\n        };\n        if (this._animation.dataType === Animation.ANIMATIONTYPE_MATRIX) {\n            this._animationState.workValue = Matrix.Zero();\n        }\n        // Limits\n        this._keys = this._animation.getKeys();\n        this._minFrame = this._keys[0].frame;\n        this._maxFrame = this._keys[this._keys.length - 1].frame;\n        this._minValue = this._keys[0].value;\n        this._maxValue = this._keys[this._keys.length - 1].value;\n        // Add a start key at frame 0 if missing\n        if (this._minFrame !== 0) {\n            const newKey = { frame: 0, value: this._minValue };\n            this._keys.splice(0, 0, newKey);\n        }\n        // Check data\n        if (this._target instanceof Array) {\n            let index = 0;\n            for (const target of this._target) {\n                this._preparePath(target, index);\n                this._getOriginalValues(index);\n                index++;\n            }\n            this._targetIsArray = true;\n        }\n        else {\n            this._preparePath(this._target);\n            this._getOriginalValues();\n            this._targetIsArray = false;\n            this._directTarget = this._activeTargets[0];\n        }\n        // Cloning events locally\n        const events = animation.getEvents();\n        if (events && events.length > 0) {\n            events.forEach((e) => {\n                this._events.push(e._clone());\n            });\n        }\n        this._enableBlending = target && target.animationPropertiesOverride ? target.animationPropertiesOverride.enableBlending : this._animation.enableBlending;\n    }\n    _preparePath(target, targetIndex = 0) {\n        const targetPropertyPath = this._animation.targetPropertyPath;\n        if (targetPropertyPath.length > 1) {\n            let property = target;\n            for (let index = 0; index < targetPropertyPath.length - 1; index++) {\n                const name = targetPropertyPath[index];\n                property = property[name];\n                if (property === undefined) {\n                    throw new Error(`Invalid property (${name}) in property path (${targetPropertyPath.join(\".\")})`);\n                }\n            }\n            this._targetPath = targetPropertyPath[targetPropertyPath.length - 1];\n            this._activeTargets[targetIndex] = property;\n        }\n        else {\n            this._targetPath = targetPropertyPath[0];\n            this._activeTargets[targetIndex] = target;\n        }\n        if (this._activeTargets[targetIndex][this._targetPath] === undefined) {\n            throw new Error(`Invalid property (${this._targetPath}) in property path (${targetPropertyPath.join(\".\")})`);\n        }\n    }\n    /**\n     * Gets the animation from the runtime animation\n     */\n    get animation() {\n        return this._animation;\n    }\n    /**\n     * Resets the runtime animation to the beginning\n     * @param restoreOriginal defines whether to restore the target property to the original value\n     */\n    reset(restoreOriginal = false) {\n        if (restoreOriginal) {\n            if (this._target instanceof Array) {\n                let index = 0;\n                for (const target of this._target) {\n                    if (this._originalValue[index] !== undefined) {\n                        this._setValue(target, this._activeTargets[index], this._originalValue[index], -1, index);\n                    }\n                    index++;\n                }\n            }\n            else {\n                if (this._originalValue[0] !== undefined) {\n                    this._setValue(this._target, this._directTarget, this._originalValue[0], -1, 0);\n                }\n            }\n        }\n        this._offsetsCache = {};\n        this._highLimitsCache = {};\n        this._currentFrame = 0;\n        this._blendingFactor = 0;\n        // Events\n        for (let index = 0; index < this._events.length; index++) {\n            this._events[index].isDone = false;\n        }\n    }\n    /**\n     * Specifies if the runtime animation is stopped\n     * @returns Boolean specifying if the runtime animation is stopped\n     */\n    isStopped() {\n        return this._stopped;\n    }\n    /**\n     * Disposes of the runtime animation\n     */\n    dispose() {\n        const index = this._animation.runtimeAnimations.indexOf(this);\n        if (index > -1) {\n            this._animation.runtimeAnimations.splice(index, 1);\n        }\n    }\n    /**\n     * Apply the interpolated value to the target\n     * @param currentValue defines the value computed by the animation\n     * @param weight defines the weight to apply to this value (Defaults to 1.0)\n     */\n    setValue(currentValue, weight) {\n        if (this._targetIsArray) {\n            for (let index = 0; index < this._target.length; index++) {\n                const target = this._target[index];\n                this._setValue(target, this._activeTargets[index], currentValue, weight, index);\n            }\n            return;\n        }\n        this._setValue(this._target, this._directTarget, currentValue, weight, 0);\n    }\n    _getOriginalValues(targetIndex = 0) {\n        let originalValue;\n        const target = this._activeTargets[targetIndex];\n        if (target.getLocalMatrix && this._targetPath === \"_matrix\") {\n            // For bones\n            originalValue = target.getLocalMatrix();\n        }\n        else {\n            originalValue = target[this._targetPath];\n        }\n        if (originalValue && originalValue.clone) {\n            this._originalValue[targetIndex] = originalValue.clone();\n        }\n        else {\n            this._originalValue[targetIndex] = originalValue;\n        }\n    }\n    _registerTargetForLateAnimationBinding(runtimeAnimation, originalValue) {\n        const target = runtimeAnimation.target;\n        this._scene._registeredForLateAnimationBindings.pushNoDuplicate(target);\n        if (!target._lateAnimationHolders) {\n            target._lateAnimationHolders = {};\n        }\n        if (!target._lateAnimationHolders[runtimeAnimation.targetPath]) {\n            target._lateAnimationHolders[runtimeAnimation.targetPath] = {\n                totalWeight: 0,\n                totalAdditiveWeight: 0,\n                animations: [],\n                additiveAnimations: [],\n                originalValue: originalValue,\n            };\n        }\n        if (runtimeAnimation.isAdditive) {\n            target._lateAnimationHolders[runtimeAnimation.targetPath].additiveAnimations.push(runtimeAnimation);\n            target._lateAnimationHolders[runtimeAnimation.targetPath].totalAdditiveWeight += runtimeAnimation.weight;\n        }\n        else {\n            target._lateAnimationHolders[runtimeAnimation.targetPath].animations.push(runtimeAnimation);\n            target._lateAnimationHolders[runtimeAnimation.targetPath].totalWeight += runtimeAnimation.weight;\n        }\n    }\n    _setValue(target, destination, currentValue, weight, targetIndex) {\n        // Set value\n        this._currentActiveTarget = destination;\n        this._weight = weight;\n        if (this._enableBlending && this._blendingFactor <= 1.0) {\n            if (!this._originalBlendValue) {\n                const originalValue = destination[this._targetPath];\n                if (originalValue.clone) {\n                    this._originalBlendValue = originalValue.clone();\n                }\n                else {\n                    this._originalBlendValue = originalValue;\n                }\n            }\n            if (this._originalBlendValue.m) {\n                // Matrix\n                if (Animation.AllowMatrixDecomposeForInterpolation) {\n                    if (this._currentValue) {\n                        Matrix.DecomposeLerpToRef(this._originalBlendValue, currentValue, this._blendingFactor, this._currentValue);\n                    }\n                    else {\n                        this._currentValue = Matrix.DecomposeLerp(this._originalBlendValue, currentValue, this._blendingFactor);\n                    }\n                }\n                else {\n                    if (this._currentValue) {\n                        Matrix.LerpToRef(this._originalBlendValue, currentValue, this._blendingFactor, this._currentValue);\n                    }\n                    else {\n                        this._currentValue = Matrix.Lerp(this._originalBlendValue, currentValue, this._blendingFactor);\n                    }\n                }\n            }\n            else {\n                this._currentValue = Animation._UniversalLerp(this._originalBlendValue, currentValue, this._blendingFactor);\n            }\n            const blendingSpeed = target && target.animationPropertiesOverride ? target.animationPropertiesOverride.blendingSpeed : this._animation.blendingSpeed;\n            this._blendingFactor += blendingSpeed;\n        }\n        else {\n            if (!this._currentValue) {\n                if (currentValue?.clone) {\n                    this._currentValue = currentValue.clone();\n                }\n                else {\n                    this._currentValue = currentValue;\n                }\n            }\n            else if (this._currentValue.copyFrom) {\n                this._currentValue.copyFrom(currentValue);\n            }\n            else {\n                this._currentValue = currentValue;\n            }\n        }\n        if (weight !== -1.0) {\n            this._registerTargetForLateAnimationBinding(this, this._originalValue[targetIndex]);\n        }\n        else {\n            if (this._animationState.loopMode === Animation.ANIMATIONLOOPMODE_RELATIVE_FROM_CURRENT) {\n                if (this._currentValue.addToRef) {\n                    this._currentValue.addToRef(this._originalValue[targetIndex], destination[this._targetPath]);\n                }\n                else {\n                    destination[this._targetPath] = this._originalValue[targetIndex] + this._currentValue;\n                }\n            }\n            else {\n                destination[this._targetPath] = this._currentValue;\n            }\n        }\n        if (target.markAsDirty) {\n            target.markAsDirty(this._animation.targetProperty);\n        }\n    }\n    /**\n     * Gets the loop pmode of the runtime animation\n     * @returns Loop Mode\n     */\n    _getCorrectLoopMode() {\n        if (this._target && this._target.animationPropertiesOverride) {\n            return this._target.animationPropertiesOverride.loopMode;\n        }\n        return this._animation.loopMode;\n    }\n    /**\n     * Move the current animation to a given frame\n     * @param frame defines the frame to move to\n     * @param weight defines the weight to apply to the animation (-1.0 by default)\n     */\n    goToFrame(frame, weight = -1) {\n        const keys = this._animation.getKeys();\n        if (frame < keys[0].frame) {\n            frame = keys[0].frame;\n        }\n        else if (frame > keys[keys.length - 1].frame) {\n            frame = keys[keys.length - 1].frame;\n        }\n        // Need to reset animation events\n        const events = this._events;\n        if (events.length) {\n            for (let index = 0; index < events.length; index++) {\n                if (!events[index].onlyOnce) {\n                    // reset events in the future\n                    events[index].isDone = events[index].frame < frame;\n                }\n            }\n        }\n        this._currentFrame = frame;\n        const currentValue = this._animation._interpolate(frame, this._animationState);\n        this.setValue(currentValue, weight);\n    }\n    /**\n     * @internal Internal use only\n     */\n    _prepareForSpeedRatioChange(newSpeedRatio) {\n        const newAbsoluteFrame = (this._previousElapsedTime * (this._animation.framePerSecond * newSpeedRatio)) / 1000.0;\n        this._absoluteFrameOffset = this._previousAbsoluteFrame - newAbsoluteFrame;\n    }\n    /**\n     * Execute the current animation\n     * @param elapsedTimeSinceAnimationStart defines the elapsed time (in milliseconds) since the animation was started\n     * @param from defines the lower frame of the animation range\n     * @param to defines the upper frame of the animation range\n     * @param loop defines if the current animation must loop\n     * @param speedRatio defines the current speed ratio\n     * @param weight defines the weight of the animation (default is -1 so no weight)\n     * @returns a boolean indicating if the animation is running\n     */\n    animate(elapsedTimeSinceAnimationStart, from, to, loop, speedRatio, weight = -1.0) {\n        const animation = this._animation;\n        const targetPropertyPath = animation.targetPropertyPath;\n        if (!targetPropertyPath || targetPropertyPath.length < 1) {\n            this._stopped = true;\n            return false;\n        }\n        let returnValue = true;\n        // Check limits\n        if (from < this._minFrame || from > this._maxFrame) {\n            from = this._minFrame;\n        }\n        if (to < this._minFrame || to > this._maxFrame) {\n            to = this._maxFrame;\n        }\n        const frameRange = to - from;\n        let offsetValue;\n        // Compute the frame according to the elapsed time and the fps of the animation (\"from\" and \"to\" are not factored in!)\n        let absoluteFrame = (elapsedTimeSinceAnimationStart * (animation.framePerSecond * speedRatio)) / 1000.0 + this._absoluteFrameOffset;\n        let highLimitValue = 0;\n        // Apply the yoyo function if required\n        let yoyoLoop = false;\n        const yoyoMode = loop && this._animationState.loopMode === Animation.ANIMATIONLOOPMODE_YOYO;\n        if (yoyoMode) {\n            const position = (absoluteFrame - from) / frameRange;\n            // Apply the yoyo curve\n            const sin = Math.sin(position * Math.PI);\n            const yoyoPosition = Math.abs(sin);\n            // Map the yoyo position back to the range\n            absoluteFrame = yoyoPosition * frameRange + from;\n            const direction = sin >= 0 ? 1 : -1;\n            if (this._yoyoDirection !== direction) {\n                yoyoLoop = true;\n            }\n            this._yoyoDirection = direction;\n        }\n        this._previousElapsedTime = elapsedTimeSinceAnimationStart;\n        this._previousAbsoluteFrame = absoluteFrame;\n        if (!loop && to >= from && ((absoluteFrame >= frameRange && speedRatio > 0) || (absoluteFrame <= 0 && speedRatio < 0))) {\n            // If we are out of range and not looping get back to caller\n            returnValue = false;\n            highLimitValue = animation._getKeyValue(this._maxValue);\n        }\n        else if (!loop && from >= to && ((absoluteFrame <= frameRange && speedRatio < 0) || (absoluteFrame >= 0 && speedRatio > 0))) {\n            returnValue = false;\n            highLimitValue = animation._getKeyValue(this._minValue);\n        }\n        else if (this._animationState.loopMode !== Animation.ANIMATIONLOOPMODE_CYCLE) {\n            const keyOffset = to.toString() + from.toString();\n            if (!this._offsetsCache[keyOffset]) {\n                this._animationState.repeatCount = 0;\n                this._animationState.loopMode = Animation.ANIMATIONLOOPMODE_CYCLE; // force a specific codepath in animation._interpolate()!\n                const fromValue = animation._interpolate(from, this._animationState);\n                const toValue = animation._interpolate(to, this._animationState);\n                this._animationState.loopMode = this._getCorrectLoopMode();\n                switch (animation.dataType) {\n                    // Float\n                    case Animation.ANIMATIONTYPE_FLOAT:\n                        this._offsetsCache[keyOffset] = toValue - fromValue;\n                        break;\n                    // Quaternion\n                    case Animation.ANIMATIONTYPE_QUATERNION:\n                        this._offsetsCache[keyOffset] = toValue.subtract(fromValue);\n                        break;\n                    // Vector3\n                    case Animation.ANIMATIONTYPE_VECTOR3:\n                        this._offsetsCache[keyOffset] = toValue.subtract(fromValue);\n                        break;\n                    // Vector2\n                    case Animation.ANIMATIONTYPE_VECTOR2:\n                        this._offsetsCache[keyOffset] = toValue.subtract(fromValue);\n                        break;\n                    // Size\n                    case Animation.ANIMATIONTYPE_SIZE:\n                        this._offsetsCache[keyOffset] = toValue.subtract(fromValue);\n                        break;\n                    // Color3\n                    case Animation.ANIMATIONTYPE_COLOR3:\n                        this._offsetsCache[keyOffset] = toValue.subtract(fromValue);\n                        break;\n                    default:\n                        break;\n                }\n                this._highLimitsCache[keyOffset] = toValue;\n            }\n            highLimitValue = this._highLimitsCache[keyOffset];\n            offsetValue = this._offsetsCache[keyOffset];\n        }\n        if (offsetValue === undefined) {\n            switch (animation.dataType) {\n                // Float\n                case Animation.ANIMATIONTYPE_FLOAT:\n                    offsetValue = 0;\n                    break;\n                // Quaternion\n                case Animation.ANIMATIONTYPE_QUATERNION:\n                    offsetValue = _staticOffsetValueQuaternion;\n                    break;\n                // Vector3\n                case Animation.ANIMATIONTYPE_VECTOR3:\n                    offsetValue = _staticOffsetValueVector3;\n                    break;\n                // Vector2\n                case Animation.ANIMATIONTYPE_VECTOR2:\n                    offsetValue = _staticOffsetValueVector2;\n                    break;\n                // Size\n                case Animation.ANIMATIONTYPE_SIZE:\n                    offsetValue = _staticOffsetValueSize;\n                    break;\n                // Color3\n                case Animation.ANIMATIONTYPE_COLOR3:\n                    offsetValue = _staticOffsetValueColor3;\n                    break;\n                case Animation.ANIMATIONTYPE_COLOR4:\n                    offsetValue = _staticOffsetValueColor4;\n                    break;\n            }\n        }\n        // Compute value\n        let currentFrame;\n        if (this._host && this._host.syncRoot) {\n            // If we must sync with an animatable, calculate the current frame based on the frame of the root animatable\n            const syncRoot = this._host.syncRoot;\n            const hostNormalizedFrame = (syncRoot.masterFrame - syncRoot.fromFrame) / (syncRoot.toFrame - syncRoot.fromFrame);\n            currentFrame = from + frameRange * hostNormalizedFrame;\n        }\n        else {\n            if ((absoluteFrame > 0 && from > to) || (absoluteFrame < 0 && from < to)) {\n                currentFrame = returnValue && frameRange !== 0 ? to + (absoluteFrame % frameRange) : from;\n            }\n            else {\n                currentFrame = returnValue && frameRange !== 0 ? from + (absoluteFrame % frameRange) : to;\n            }\n        }\n        const events = this._events;\n        // Reset event/state if looping\n        if ((!yoyoMode && ((speedRatio > 0 && this.currentFrame > currentFrame) || (speedRatio < 0 && this.currentFrame < currentFrame))) || (yoyoMode && yoyoLoop)) {\n            this._onLoop();\n            // Need to reset animation events\n            for (let index = 0; index < events.length; index++) {\n                if (!events[index].onlyOnce) {\n                    // reset event, the animation is looping\n                    events[index].isDone = false;\n                }\n            }\n            this._animationState.key = speedRatio > 0 ? 0 : animation.getKeys().length - 1;\n        }\n        this._currentFrame = currentFrame;\n        this._animationState.repeatCount = frameRange === 0 ? 0 : (absoluteFrame / frameRange) >> 0;\n        this._animationState.highLimitValue = highLimitValue;\n        this._animationState.offsetValue = offsetValue;\n        const currentValue = animation._interpolate(currentFrame, this._animationState);\n        // Set value\n        this.setValue(currentValue, weight);\n        // Check events\n        if (events.length) {\n            for (let index = 0; index < events.length; index++) {\n                // Make sure current frame has passed event frame and that event frame is within the current range\n                // Also, handle both forward and reverse animations\n                if ((frameRange >= 0 && currentFrame >= events[index].frame && events[index].frame >= from) ||\n                    (frameRange < 0 && currentFrame <= events[index].frame && events[index].frame <= from)) {\n                    const event = events[index];\n                    if (!event.isDone) {\n                        // If event should be done only once, remove it.\n                        if (event.onlyOnce) {\n                            events.splice(index, 1);\n                            index--;\n                        }\n                        event.isDone = true;\n                        event.action(currentFrame);\n                    } // Don't do anything if the event has already been done.\n                }\n            }\n        }\n        if (!returnValue) {\n            this._stopped = true;\n        }\n        return returnValue;\n    }\n}\n//# sourceMappingURL=runtimeAnimation.js.map", "import { Observable } from \"../Misc/observable.js\";\nimport { RuntimeAnimation } from \"./runtimeAnimation.js\";\nimport { Animation } from \"./animation.js\";\nimport { PrecisionDate } from \"../Misc/precisionDate.js\";\nimport { Matrix, Quaternion, TmpVectors, Vector3 } from \"../Maths/math.vector.js\";\n/**\n * Class used to store an actual running animation\n */\nexport class Animatable {\n    /**\n     * Gets the root Animatable used to synchronize and normalize animations\n     */\n    get syncRoot() {\n        return this._syncRoot;\n    }\n    /**\n     * Gets the current frame of the first RuntimeAnimation\n     * Used to synchronize Animatables\n     */\n    get masterFrame() {\n        if (this._runtimeAnimations.length === 0) {\n            return 0;\n        }\n        return this._runtimeAnimations[0].currentFrame;\n    }\n    /**\n     * Gets or sets the animatable weight (-1.0 by default meaning not weighted)\n     */\n    get weight() {\n        return this._weight;\n    }\n    set weight(value) {\n        if (value === -1) {\n            // -1 is ok and means no weight\n            this._weight = -1;\n            return;\n        }\n        // Else weight must be in [0, 1] range\n        this._weight = Math.min(Math.max(value, 0), 1.0);\n    }\n    /**\n     * Gets or sets the speed ratio to apply to the animatable (1.0 by default)\n     */\n    get speedRatio() {\n        return this._speedRatio;\n    }\n    set speedRatio(value) {\n        for (let index = 0; index < this._runtimeAnimations.length; index++) {\n            const animation = this._runtimeAnimations[index];\n            animation._prepareForSpeedRatioChange(value);\n        }\n        this._speedRatio = value;\n        // Resync _manualJumpDelay in case goToFrame was called before speedRatio was set.\n        if (this._goToFrame !== null) {\n            this.goToFrame(this._goToFrame);\n        }\n    }\n    /**\n     * Gets the elapsed time since the animatable started in milliseconds\n     */\n    get elapsedTime() {\n        return this._localDelayOffset === null ? 0 : this._scene._animationTime - this._localDelayOffset;\n    }\n    /**\n     * Creates a new Animatable\n     * @param scene defines the hosting scene\n     * @param target defines the target object\n     * @param fromFrame defines the starting frame number (default is 0)\n     * @param toFrame defines the ending frame number (default is 100)\n     * @param loopAnimation defines if the animation must loop (default is false)\n     * @param speedRatio defines the factor to apply to animation speed (default is 1)\n     * @param onAnimationEnd defines a callback to call when animation ends if it is not looping\n     * @param animations defines a group of animation to add to the new Animatable\n     * @param onAnimationLoop defines a callback to call when animation loops\n     * @param isAdditive defines whether the animation should be evaluated additively\n     * @param playOrder defines the order in which this animatable should be processed in the list of active animatables (default: 0)\n     */\n    constructor(scene, \n    /** defines the target object */\n    target, \n    /** [0] defines the starting frame number (default is 0) */\n    fromFrame = 0, \n    /** [100] defines the ending frame number (default is 100) */\n    toFrame = 100, \n    /** [false] defines if the animation must loop (default is false)  */\n    loopAnimation = false, speedRatio = 1.0, \n    /** defines a callback to call when animation ends if it is not looping */\n    onAnimationEnd, animations, \n    /** defines a callback to call when animation loops */\n    onAnimationLoop, \n    /** [false] defines whether the animation should be evaluated additively */\n    isAdditive = false, \n    /** [0] defines the order in which this animatable should be processed in the list of active animatables (default: 0) */\n    playOrder = 0) {\n        this.target = target;\n        this.fromFrame = fromFrame;\n        this.toFrame = toFrame;\n        this.loopAnimation = loopAnimation;\n        this.onAnimationEnd = onAnimationEnd;\n        this.onAnimationLoop = onAnimationLoop;\n        this.isAdditive = isAdditive;\n        this.playOrder = playOrder;\n        this._localDelayOffset = null;\n        this._pausedDelay = null;\n        this._manualJumpDelay = null;\n        /** @hidden */\n        this._runtimeAnimations = new Array();\n        this._paused = false;\n        this._speedRatio = 1;\n        this._weight = -1.0;\n        this._previousWeight = -1.0;\n        this._syncRoot = null;\n        this._frameToSyncFromJump = null;\n        this._goToFrame = null;\n        /**\n         * Gets or sets a boolean indicating if the animatable must be disposed and removed at the end of the animation.\n         * This will only apply for non looping animation (default is true)\n         */\n        this.disposeOnEnd = true;\n        /**\n         * Gets a boolean indicating if the animation has started\n         */\n        this.animationStarted = false;\n        /**\n         * Observer raised when the animation ends\n         */\n        this.onAnimationEndObservable = new Observable();\n        /**\n         * Observer raised when the animation loops\n         */\n        this.onAnimationLoopObservable = new Observable();\n        this._scene = scene;\n        if (animations) {\n            this.appendAnimations(target, animations);\n        }\n        this._speedRatio = speedRatio;\n        scene._activeAnimatables.push(this);\n    }\n    // Methods\n    /**\n     * Synchronize and normalize current Animatable with a source Animatable\n     * This is useful when using animation weights and when animations are not of the same length\n     * @param root defines the root Animatable to synchronize with (null to stop synchronizing)\n     * @returns the current Animatable\n     */\n    syncWith(root) {\n        this._syncRoot = root;\n        if (root) {\n            // Make sure this animatable will animate after the root\n            const index = this._scene._activeAnimatables.indexOf(this);\n            if (index > -1) {\n                this._scene._activeAnimatables.splice(index, 1);\n                this._scene._activeAnimatables.push(this);\n            }\n        }\n        return this;\n    }\n    /**\n     * Gets the list of runtime animations\n     * @returns an array of RuntimeAnimation\n     */\n    getAnimations() {\n        return this._runtimeAnimations;\n    }\n    /**\n     * Adds more animations to the current animatable\n     * @param target defines the target of the animations\n     * @param animations defines the new animations to add\n     */\n    appendAnimations(target, animations) {\n        for (let index = 0; index < animations.length; index++) {\n            const animation = animations[index];\n            const newRuntimeAnimation = new RuntimeAnimation(target, animation, this._scene, this);\n            newRuntimeAnimation._onLoop = () => {\n                this.onAnimationLoopObservable.notifyObservers(this);\n                if (this.onAnimationLoop) {\n                    this.onAnimationLoop();\n                }\n            };\n            this._runtimeAnimations.push(newRuntimeAnimation);\n        }\n    }\n    /**\n     * Gets the source animation for a specific property\n     * @param property defines the property to look for\n     * @returns null or the source animation for the given property\n     */\n    getAnimationByTargetProperty(property) {\n        const runtimeAnimations = this._runtimeAnimations;\n        for (let index = 0; index < runtimeAnimations.length; index++) {\n            if (runtimeAnimations[index].animation.targetProperty === property) {\n                return runtimeAnimations[index].animation;\n            }\n        }\n        return null;\n    }\n    /**\n     * Gets the runtime animation for a specific property\n     * @param property defines the property to look for\n     * @returns null or the runtime animation for the given property\n     */\n    getRuntimeAnimationByTargetProperty(property) {\n        const runtimeAnimations = this._runtimeAnimations;\n        for (let index = 0; index < runtimeAnimations.length; index++) {\n            if (runtimeAnimations[index].animation.targetProperty === property) {\n                return runtimeAnimations[index];\n            }\n        }\n        return null;\n    }\n    /**\n     * Resets the animatable to its original state\n     */\n    reset() {\n        const runtimeAnimations = this._runtimeAnimations;\n        for (let index = 0; index < runtimeAnimations.length; index++) {\n            runtimeAnimations[index].reset(true);\n        }\n        this._localDelayOffset = null;\n        this._pausedDelay = null;\n    }\n    /**\n     * Allows the animatable to blend with current running animations\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-blending\n     * @param blendingSpeed defines the blending speed to use\n     */\n    enableBlending(blendingSpeed) {\n        const runtimeAnimations = this._runtimeAnimations;\n        for (let index = 0; index < runtimeAnimations.length; index++) {\n            runtimeAnimations[index].animation.enableBlending = true;\n            runtimeAnimations[index].animation.blendingSpeed = blendingSpeed;\n        }\n    }\n    /**\n     * Disable animation blending\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-blending\n     */\n    disableBlending() {\n        const runtimeAnimations = this._runtimeAnimations;\n        for (let index = 0; index < runtimeAnimations.length; index++) {\n            runtimeAnimations[index].animation.enableBlending = false;\n        }\n    }\n    /**\n     * Jump directly to a given frame\n     * @param frame defines the frame to jump to\n     * @param useWeight defines whether the animation weight should be applied to the image to be jumped to (false by default)\n     */\n    goToFrame(frame, useWeight = false) {\n        const runtimeAnimations = this._runtimeAnimations;\n        if (runtimeAnimations[0]) {\n            const fps = runtimeAnimations[0].animation.framePerSecond;\n            this._frameToSyncFromJump = this._frameToSyncFromJump ?? runtimeAnimations[0].currentFrame;\n            const delay = this.speedRatio === 0 ? 0 : (((frame - this._frameToSyncFromJump) / fps) * 1000) / this.speedRatio;\n            this._manualJumpDelay = -delay;\n        }\n        for (let index = 0; index < runtimeAnimations.length; index++) {\n            runtimeAnimations[index].goToFrame(frame, useWeight ? this._weight : -1);\n        }\n        this._goToFrame = frame;\n    }\n    /**\n     * Returns true if the animations for this animatable are paused\n     */\n    get paused() {\n        return this._paused;\n    }\n    /**\n     * Pause the animation\n     */\n    pause() {\n        if (this._paused) {\n            return;\n        }\n        this._paused = true;\n    }\n    /**\n     * Restart the animation\n     */\n    restart() {\n        this._paused = false;\n    }\n    _raiseOnAnimationEnd() {\n        if (this.onAnimationEnd) {\n            this.onAnimationEnd();\n        }\n        this.onAnimationEndObservable.notifyObservers(this);\n    }\n    /**\n     * Stop and delete the current animation\n     * @param animationName defines a string used to only stop some of the runtime animations instead of all\n     * @param targetMask a function that determines if the animation should be stopped based on its target (all animations will be stopped if both this and animationName are empty)\n     * @param useGlobalSplice if true, the animatables will be removed by the caller of this function (false by default)\n     * @param skipOnAnimationEnd defines if the system should not raise onAnimationEnd. Default is false\n     */\n    stop(animationName, targetMask, useGlobalSplice = false, skipOnAnimationEnd = false) {\n        if (animationName || targetMask) {\n            const idx = this._scene._activeAnimatables.indexOf(this);\n            if (idx > -1) {\n                const runtimeAnimations = this._runtimeAnimations;\n                for (let index = runtimeAnimations.length - 1; index >= 0; index--) {\n                    const runtimeAnimation = runtimeAnimations[index];\n                    if (animationName && runtimeAnimation.animation.name != animationName) {\n                        continue;\n                    }\n                    if (targetMask && !targetMask(runtimeAnimation.target)) {\n                        continue;\n                    }\n                    runtimeAnimation.dispose();\n                    runtimeAnimations.splice(index, 1);\n                }\n                if (runtimeAnimations.length == 0) {\n                    if (!useGlobalSplice) {\n                        this._scene._activeAnimatables.splice(idx, 1);\n                    }\n                    if (!skipOnAnimationEnd) {\n                        this._raiseOnAnimationEnd();\n                    }\n                }\n            }\n        }\n        else {\n            const index = this._scene._activeAnimatables.indexOf(this);\n            if (index > -1) {\n                if (!useGlobalSplice) {\n                    this._scene._activeAnimatables.splice(index, 1);\n                }\n                const runtimeAnimations = this._runtimeAnimations;\n                for (let index = 0; index < runtimeAnimations.length; index++) {\n                    runtimeAnimations[index].dispose();\n                }\n                this._runtimeAnimations.length = 0;\n                if (!skipOnAnimationEnd) {\n                    this._raiseOnAnimationEnd();\n                }\n            }\n        }\n    }\n    /**\n     * Wait asynchronously for the animation to end\n     * @returns a promise which will be fulfilled when the animation ends\n     */\n    waitAsync() {\n        return new Promise((resolve) => {\n            this.onAnimationEndObservable.add(() => {\n                resolve(this);\n            }, undefined, undefined, this, true);\n        });\n    }\n    /**\n     * @internal\n     */\n    _animate(delay) {\n        if (this._paused) {\n            this.animationStarted = false;\n            if (this._pausedDelay === null) {\n                this._pausedDelay = delay;\n            }\n            return true;\n        }\n        if (this._localDelayOffset === null) {\n            this._localDelayOffset = delay;\n            this._pausedDelay = null;\n        }\n        else if (this._pausedDelay !== null) {\n            this._localDelayOffset += delay - this._pausedDelay;\n            this._pausedDelay = null;\n        }\n        if (this._manualJumpDelay !== null) {\n            this._localDelayOffset += this._manualJumpDelay;\n            this._manualJumpDelay = null;\n            this._frameToSyncFromJump = null;\n        }\n        this._goToFrame = null;\n        if (this._weight === 0 && this._previousWeight === 0) {\n            // We consider that an animatable with a weight === 0 is \"actively\" paused\n            return true;\n        }\n        this._previousWeight = this._weight;\n        // Animating\n        let running = false;\n        const runtimeAnimations = this._runtimeAnimations;\n        let index;\n        for (index = 0; index < runtimeAnimations.length; index++) {\n            const animation = runtimeAnimations[index];\n            const isRunning = animation.animate(delay - this._localDelayOffset, this.fromFrame, this.toFrame, this.loopAnimation, this._speedRatio, this._weight);\n            running = running || isRunning;\n        }\n        this.animationStarted = running;\n        if (!running) {\n            if (this.disposeOnEnd) {\n                // Remove from active animatables\n                index = this._scene._activeAnimatables.indexOf(this);\n                this._scene._activeAnimatables.splice(index, 1);\n                // Dispose all runtime animations\n                for (index = 0; index < runtimeAnimations.length; index++) {\n                    runtimeAnimations[index].dispose();\n                }\n            }\n            this._raiseOnAnimationEnd();\n            if (this.disposeOnEnd) {\n                this.onAnimationEnd = null;\n                this.onAnimationLoop = null;\n                this.onAnimationLoopObservable.clear();\n                this.onAnimationEndObservable.clear();\n            }\n        }\n        return running;\n    }\n}\n/** @internal */\nfunction ProcessLateAnimationBindingsForMatrices(holder) {\n    if (holder.totalWeight === 0 && holder.totalAdditiveWeight === 0) {\n        return holder.originalValue;\n    }\n    let normalizer = 1.0;\n    const finalPosition = TmpVectors.Vector3[0];\n    const finalScaling = TmpVectors.Vector3[1];\n    const finalQuaternion = TmpVectors.Quaternion[0];\n    let startIndex = 0;\n    const originalAnimation = holder.animations[0];\n    const originalValue = holder.originalValue;\n    let scale = 1;\n    let skipOverride = false;\n    if (holder.totalWeight < 1.0) {\n        // We need to mix the original value in\n        scale = 1.0 - holder.totalWeight;\n        originalValue.decompose(finalScaling, finalQuaternion, finalPosition);\n    }\n    else {\n        startIndex = 1;\n        // We need to normalize the weights\n        normalizer = holder.totalWeight;\n        scale = originalAnimation.weight / normalizer;\n        if (scale == 1) {\n            if (holder.totalAdditiveWeight) {\n                skipOverride = true;\n            }\n            else {\n                return originalAnimation.currentValue;\n            }\n        }\n        originalAnimation.currentValue.decompose(finalScaling, finalQuaternion, finalPosition);\n    }\n    // Add up the override animations\n    if (!skipOverride) {\n        finalScaling.scaleInPlace(scale);\n        finalPosition.scaleInPlace(scale);\n        finalQuaternion.scaleInPlace(scale);\n        for (let animIndex = startIndex; animIndex < holder.animations.length; animIndex++) {\n            const runtimeAnimation = holder.animations[animIndex];\n            if (runtimeAnimation.weight === 0) {\n                continue;\n            }\n            scale = runtimeAnimation.weight / normalizer;\n            const currentPosition = TmpVectors.Vector3[2];\n            const currentScaling = TmpVectors.Vector3[3];\n            const currentQuaternion = TmpVectors.Quaternion[1];\n            runtimeAnimation.currentValue.decompose(currentScaling, currentQuaternion, currentPosition);\n            currentScaling.scaleAndAddToRef(scale, finalScaling);\n            currentQuaternion.scaleAndAddToRef(Quaternion.Dot(finalQuaternion, currentQuaternion) > 0 ? scale : -scale, finalQuaternion);\n            currentPosition.scaleAndAddToRef(scale, finalPosition);\n        }\n        finalQuaternion.normalize();\n    }\n    // Add up the additive animations\n    for (let animIndex = 0; animIndex < holder.additiveAnimations.length; animIndex++) {\n        const runtimeAnimation = holder.additiveAnimations[animIndex];\n        if (runtimeAnimation.weight === 0) {\n            continue;\n        }\n        const currentPosition = TmpVectors.Vector3[2];\n        const currentScaling = TmpVectors.Vector3[3];\n        const currentQuaternion = TmpVectors.Quaternion[1];\n        runtimeAnimation.currentValue.decompose(currentScaling, currentQuaternion, currentPosition);\n        currentScaling.multiplyToRef(finalScaling, currentScaling);\n        Vector3.LerpToRef(finalScaling, currentScaling, runtimeAnimation.weight, finalScaling);\n        finalQuaternion.multiplyToRef(currentQuaternion, currentQuaternion);\n        Quaternion.SlerpToRef(finalQuaternion, currentQuaternion, runtimeAnimation.weight, finalQuaternion);\n        currentPosition.scaleAndAddToRef(runtimeAnimation.weight, finalPosition);\n    }\n    const workValue = originalAnimation ? originalAnimation._animationState.workValue : TmpVectors.Matrix[0].clone();\n    Matrix.ComposeToRef(finalScaling, finalQuaternion, finalPosition, workValue);\n    return workValue;\n}\n/** @internal */\nfunction ProcessLateAnimationBindingsForQuaternions(holder, refQuaternion) {\n    if (holder.totalWeight === 0 && holder.totalAdditiveWeight === 0) {\n        return refQuaternion;\n    }\n    const originalAnimation = holder.animations[0];\n    const originalValue = holder.originalValue;\n    let cumulativeQuaternion = refQuaternion;\n    if (holder.totalWeight === 0 && holder.totalAdditiveWeight > 0) {\n        cumulativeQuaternion.copyFrom(originalValue);\n    }\n    else if (holder.animations.length === 1) {\n        Quaternion.SlerpToRef(originalValue, originalAnimation.currentValue, Math.min(1.0, holder.totalWeight), cumulativeQuaternion);\n        if (holder.totalAdditiveWeight === 0) {\n            return cumulativeQuaternion;\n        }\n    }\n    else if (holder.animations.length > 1) {\n        // Add up the override animations\n        let normalizer = 1.0;\n        let quaternions;\n        let weights;\n        if (holder.totalWeight < 1.0) {\n            const scale = 1.0 - holder.totalWeight;\n            quaternions = [];\n            weights = [];\n            quaternions.push(originalValue);\n            weights.push(scale);\n        }\n        else {\n            if (holder.animations.length === 2) {\n                // Slerp as soon as we can\n                Quaternion.SlerpToRef(holder.animations[0].currentValue, holder.animations[1].currentValue, holder.animations[1].weight / holder.totalWeight, refQuaternion);\n                if (holder.totalAdditiveWeight === 0) {\n                    return refQuaternion;\n                }\n            }\n            quaternions = [];\n            weights = [];\n            normalizer = holder.totalWeight;\n        }\n        for (let animIndex = 0; animIndex < holder.animations.length; animIndex++) {\n            const runtimeAnimation = holder.animations[animIndex];\n            quaternions.push(runtimeAnimation.currentValue);\n            weights.push(runtimeAnimation.weight / normalizer);\n        }\n        // https://gamedev.stackexchange.com/questions/62354/method-for-interpolation-between-3-quaternions\n        let cumulativeAmount = 0;\n        for (let index = 0; index < quaternions.length;) {\n            if (!index) {\n                Quaternion.SlerpToRef(quaternions[index], quaternions[index + 1], weights[index + 1] / (weights[index] + weights[index + 1]), refQuaternion);\n                cumulativeQuaternion = refQuaternion;\n                cumulativeAmount = weights[index] + weights[index + 1];\n                index += 2;\n                continue;\n            }\n            cumulativeAmount += weights[index];\n            Quaternion.SlerpToRef(cumulativeQuaternion, quaternions[index], weights[index] / cumulativeAmount, cumulativeQuaternion);\n            index++;\n        }\n    }\n    // Add up the additive animations\n    for (let animIndex = 0; animIndex < holder.additiveAnimations.length; animIndex++) {\n        const runtimeAnimation = holder.additiveAnimations[animIndex];\n        if (runtimeAnimation.weight === 0) {\n            continue;\n        }\n        cumulativeQuaternion.multiplyToRef(runtimeAnimation.currentValue, TmpVectors.Quaternion[0]);\n        Quaternion.SlerpToRef(cumulativeQuaternion, TmpVectors.Quaternion[0], runtimeAnimation.weight, cumulativeQuaternion);\n    }\n    return cumulativeQuaternion;\n}\n/** @internal */\nfunction ProcessLateAnimationBindings(scene) {\n    if (!scene._registeredForLateAnimationBindings.length) {\n        return;\n    }\n    for (let index = 0; index < scene._registeredForLateAnimationBindings.length; index++) {\n        const target = scene._registeredForLateAnimationBindings.data[index];\n        for (const path in target._lateAnimationHolders) {\n            const holder = target._lateAnimationHolders[path];\n            const originalAnimation = holder.animations[0];\n            const originalValue = holder.originalValue;\n            if (originalValue === undefined || originalValue === null) {\n                continue;\n            }\n            const matrixDecomposeMode = Animation.AllowMatrixDecomposeForInterpolation && originalValue.m; // ie. data is matrix\n            let finalValue = target[path];\n            if (matrixDecomposeMode) {\n                finalValue = ProcessLateAnimationBindingsForMatrices(holder);\n            }\n            else {\n                const quaternionMode = originalValue.w !== undefined;\n                if (quaternionMode) {\n                    finalValue = ProcessLateAnimationBindingsForQuaternions(holder, finalValue || Quaternion.Identity());\n                }\n                else {\n                    let startIndex = 0;\n                    let normalizer = 1.0;\n                    const originalAnimationIsLoopRelativeFromCurrent = originalAnimation && originalAnimation._animationState.loopMode === Animation.ANIMATIONLOOPMODE_RELATIVE_FROM_CURRENT;\n                    if (holder.totalWeight < 1.0) {\n                        // We need to mix the original value in\n                        if (originalAnimationIsLoopRelativeFromCurrent) {\n                            finalValue = originalValue.clone ? originalValue.clone() : originalValue;\n                        }\n                        else if (originalAnimation && originalValue.scale) {\n                            finalValue = originalValue.scale(1.0 - holder.totalWeight);\n                        }\n                        else if (originalAnimation) {\n                            finalValue = originalValue * (1.0 - holder.totalWeight);\n                        }\n                        else if (originalValue.clone) {\n                            finalValue = originalValue.clone();\n                        }\n                        else {\n                            finalValue = originalValue;\n                        }\n                    }\n                    else if (originalAnimation) {\n                        // We need to normalize the weights\n                        normalizer = holder.totalWeight;\n                        const scale = originalAnimation.weight / normalizer;\n                        if (scale !== 1) {\n                            if (originalAnimation.currentValue.scale) {\n                                finalValue = originalAnimation.currentValue.scale(scale);\n                            }\n                            else {\n                                finalValue = originalAnimation.currentValue * scale;\n                            }\n                        }\n                        else {\n                            finalValue = originalAnimation.currentValue;\n                        }\n                        if (originalAnimationIsLoopRelativeFromCurrent) {\n                            if (finalValue.addToRef) {\n                                finalValue.addToRef(originalValue, finalValue);\n                            }\n                            else {\n                                finalValue += originalValue;\n                            }\n                        }\n                        startIndex = 1;\n                    }\n                    // Add up the override animations\n                    for (let animIndex = startIndex; animIndex < holder.animations.length; animIndex++) {\n                        const runtimeAnimation = holder.animations[animIndex];\n                        const scale = runtimeAnimation.weight / normalizer;\n                        if (!scale) {\n                            continue;\n                        }\n                        else if (runtimeAnimation.currentValue.scaleAndAddToRef) {\n                            runtimeAnimation.currentValue.scaleAndAddToRef(scale, finalValue);\n                        }\n                        else {\n                            finalValue += runtimeAnimation.currentValue * scale;\n                        }\n                    }\n                    // Add up the additive animations\n                    for (let animIndex = 0; animIndex < holder.additiveAnimations.length; animIndex++) {\n                        const runtimeAnimation = holder.additiveAnimations[animIndex];\n                        const scale = runtimeAnimation.weight;\n                        if (!scale) {\n                            continue;\n                        }\n                        else if (runtimeAnimation.currentValue.scaleAndAddToRef) {\n                            runtimeAnimation.currentValue.scaleAndAddToRef(scale, finalValue);\n                        }\n                        else {\n                            finalValue += runtimeAnimation.currentValue * scale;\n                        }\n                    }\n                }\n            }\n            target[path] = finalValue;\n        }\n        target._lateAnimationHolders = {};\n    }\n    scene._registeredForLateAnimationBindings.reset();\n}\n/** @internal */\nexport function RegisterTargetForLateAnimationBinding(scene, runtimeAnimation, originalValue) {\n    const target = runtimeAnimation.target;\n    scene._registeredForLateAnimationBindings.pushNoDuplicate(target);\n    if (!target._lateAnimationHolders) {\n        target._lateAnimationHolders = {};\n    }\n    if (!target._lateAnimationHolders[runtimeAnimation.targetPath]) {\n        target._lateAnimationHolders[runtimeAnimation.targetPath] = {\n            totalWeight: 0,\n            totalAdditiveWeight: 0,\n            animations: [],\n            additiveAnimations: [],\n            originalValue: originalValue,\n        };\n    }\n    if (runtimeAnimation.isAdditive) {\n        target._lateAnimationHolders[runtimeAnimation.targetPath].additiveAnimations.push(runtimeAnimation);\n        target._lateAnimationHolders[runtimeAnimation.targetPath].totalAdditiveWeight += runtimeAnimation.weight;\n    }\n    else {\n        target._lateAnimationHolders[runtimeAnimation.targetPath].animations.push(runtimeAnimation);\n        target._lateAnimationHolders[runtimeAnimation.targetPath].totalWeight += runtimeAnimation.weight;\n    }\n}\n/**\n * Initialize all the inter dependecies between the animations and Scene and Bone\n * @param sceneClass defines the scene prototype to use\n * @param boneClass defines the bone prototype to use\n */\nexport function AddAnimationExtensions(sceneClass, boneClass) {\n    if (boneClass) {\n        boneClass.prototype.copyAnimationRange = function (source, rangeName, frameOffset, rescaleAsRequired = false, skelDimensionsRatio = null) {\n            // all animation may be coming from a library skeleton, so may need to create animation\n            if (this.animations.length === 0) {\n                this.animations.push(new Animation(this.name, \"_matrix\", source.animations[0].framePerSecond, Animation.ANIMATIONTYPE_MATRIX, 0));\n                this.animations[0].setKeys([]);\n            }\n            // get animation info / verify there is such a range from the source bone\n            const sourceRange = source.animations[0].getRange(rangeName);\n            if (!sourceRange) {\n                return false;\n            }\n            const from = sourceRange.from;\n            const to = sourceRange.to;\n            const sourceKeys = source.animations[0].getKeys();\n            // rescaling prep\n            const sourceBoneLength = source.length;\n            const sourceParent = source.getParent();\n            const parent = this.getParent();\n            const parentScalingReqd = rescaleAsRequired && sourceParent && sourceBoneLength && this.length && sourceBoneLength !== this.length;\n            const parentRatio = parentScalingReqd && parent && sourceParent ? parent.length / sourceParent.length : 1;\n            const dimensionsScalingReqd = rescaleAsRequired && !parent && skelDimensionsRatio && (skelDimensionsRatio.x !== 1 || skelDimensionsRatio.y !== 1 || skelDimensionsRatio.z !== 1);\n            const destKeys = this.animations[0].getKeys();\n            // loop vars declaration\n            let orig;\n            let origTranslation;\n            let mat;\n            for (let key = 0, nKeys = sourceKeys.length; key < nKeys; key++) {\n                orig = sourceKeys[key];\n                if (orig.frame >= from && orig.frame <= to) {\n                    if (rescaleAsRequired) {\n                        mat = orig.value.clone();\n                        // scale based on parent ratio, when bone has parent\n                        if (parentScalingReqd) {\n                            origTranslation = mat.getTranslation();\n                            mat.setTranslation(origTranslation.scaleInPlace(parentRatio));\n                            // scale based on skeleton dimension ratio when root bone, and value is passed\n                        }\n                        else if (dimensionsScalingReqd && skelDimensionsRatio) {\n                            origTranslation = mat.getTranslation();\n                            mat.setTranslation(origTranslation.multiplyInPlace(skelDimensionsRatio));\n                            // use original when root bone, and no data for skelDimensionsRatio\n                        }\n                        else {\n                            mat = orig.value;\n                        }\n                    }\n                    else {\n                        mat = orig.value;\n                    }\n                    destKeys.push({ frame: orig.frame + frameOffset, value: mat });\n                }\n            }\n            this.animations[0].createRange(rangeName, from + frameOffset, to + frameOffset);\n            return true;\n        };\n    }\n    if (!sceneClass) {\n        return;\n    }\n    sceneClass.prototype._animate = function (customDeltaTime) {\n        if (!this.animationsEnabled) {\n            return;\n        }\n        // Getting time\n        const now = PrecisionDate.Now;\n        if (!this._animationTimeLast) {\n            if (this._pendingData.length > 0) {\n                return;\n            }\n            this._animationTimeLast = now;\n        }\n        this.deltaTime = customDeltaTime !== undefined ? customDeltaTime : this.useConstantAnimationDeltaTime ? 16.0 : (now - this._animationTimeLast) * this.animationTimeScale;\n        this._animationTimeLast = now;\n        const animatables = this._activeAnimatables;\n        if (animatables.length === 0) {\n            return;\n        }\n        this._animationTime += this.deltaTime;\n        const animationTime = this._animationTime;\n        for (let index = 0; index < animatables.length; index++) {\n            const animatable = animatables[index];\n            if (!animatable._animate(animationTime) && animatable.disposeOnEnd) {\n                index--; // Array was updated\n            }\n        }\n        // Late animation bindings\n        ProcessLateAnimationBindings(this);\n    };\n    sceneClass.prototype.sortActiveAnimatables = function () {\n        this._activeAnimatables.sort((a, b) => {\n            return a.playOrder - b.playOrder;\n        });\n    };\n    sceneClass.prototype.beginWeightedAnimation = function (target, from, to, weight = 1.0, loop, speedRatio = 1.0, onAnimationEnd, animatable, targetMask, onAnimationLoop, isAdditive = false) {\n        const returnedAnimatable = this.beginAnimation(target, from, to, loop, speedRatio, onAnimationEnd, animatable, false, targetMask, onAnimationLoop, isAdditive);\n        returnedAnimatable.weight = weight;\n        return returnedAnimatable;\n    };\n    sceneClass.prototype.beginAnimation = function (target, from, to, loop, speedRatio = 1.0, onAnimationEnd, animatable, stopCurrent = true, targetMask, onAnimationLoop, isAdditive = false) {\n        // get speed speedRatio, to and from, based on the sign and value(s)\n        if (speedRatio < 0) {\n            const tmp = from;\n            from = to;\n            to = tmp;\n            speedRatio = -speedRatio;\n        }\n        // if from > to switch speed ratio\n        if (from > to) {\n            speedRatio = -speedRatio;\n        }\n        if (stopCurrent) {\n            this.stopAnimation(target, undefined, targetMask);\n        }\n        if (!animatable) {\n            animatable = new Animatable(this, target, from, to, loop, speedRatio, onAnimationEnd, undefined, onAnimationLoop, isAdditive);\n        }\n        const shouldRunTargetAnimations = targetMask ? targetMask(target) : true;\n        // Local animations\n        if (target.animations && shouldRunTargetAnimations) {\n            animatable.appendAnimations(target, target.animations);\n        }\n        // Children animations\n        if (target.getAnimatables) {\n            const animatables = target.getAnimatables();\n            for (let index = 0; index < animatables.length; index++) {\n                this.beginAnimation(animatables[index], from, to, loop, speedRatio, onAnimationEnd, animatable, stopCurrent, targetMask, onAnimationLoop);\n            }\n        }\n        animatable.reset();\n        return animatable;\n    };\n    sceneClass.prototype.beginHierarchyAnimation = function (target, directDescendantsOnly, from, to, loop, speedRatio = 1.0, onAnimationEnd, animatable, stopCurrent = true, targetMask, onAnimationLoop, isAdditive = false) {\n        const children = target.getDescendants(directDescendantsOnly);\n        const result = [];\n        result.push(this.beginAnimation(target, from, to, loop, speedRatio, onAnimationEnd, animatable, stopCurrent, targetMask, undefined, isAdditive));\n        for (const child of children) {\n            result.push(this.beginAnimation(child, from, to, loop, speedRatio, onAnimationEnd, animatable, stopCurrent, targetMask, undefined, isAdditive));\n        }\n        return result;\n    };\n    sceneClass.prototype.beginDirectAnimation = function (target, animations, from, to, loop, speedRatio = 1.0, onAnimationEnd, onAnimationLoop, isAdditive = false) {\n        // get speed speedRatio, to and from, based on the sign and value(s)\n        if (speedRatio < 0) {\n            const tmp = from;\n            from = to;\n            to = tmp;\n            speedRatio = -speedRatio;\n        }\n        // if from > to switch speed ratio\n        if (from > to) {\n            speedRatio = -speedRatio;\n        }\n        const animatable = new Animatable(this, target, from, to, loop, speedRatio, onAnimationEnd, animations, onAnimationLoop, isAdditive);\n        return animatable;\n    };\n    sceneClass.prototype.beginDirectHierarchyAnimation = function (target, directDescendantsOnly, animations, from, to, loop, speedRatio, onAnimationEnd, onAnimationLoop, isAdditive = false) {\n        const children = target.getDescendants(directDescendantsOnly);\n        const result = [];\n        result.push(this.beginDirectAnimation(target, animations, from, to, loop, speedRatio, onAnimationEnd, onAnimationLoop, isAdditive));\n        for (const child of children) {\n            result.push(this.beginDirectAnimation(child, animations, from, to, loop, speedRatio, onAnimationEnd, onAnimationLoop, isAdditive));\n        }\n        return result;\n    };\n    sceneClass.prototype.getAnimatableByTarget = function (target) {\n        for (let index = 0; index < this._activeAnimatables.length; index++) {\n            if (this._activeAnimatables[index].target === target) {\n                return this._activeAnimatables[index];\n            }\n        }\n        return null;\n    };\n    sceneClass.prototype.getAllAnimatablesByTarget = function (target) {\n        const result = [];\n        for (let index = 0; index < this._activeAnimatables.length; index++) {\n            if (this._activeAnimatables[index].target === target) {\n                result.push(this._activeAnimatables[index]);\n            }\n        }\n        return result;\n    };\n    sceneClass.prototype.stopAnimation = function (target, animationName, targetMask) {\n        const animatables = this.getAllAnimatablesByTarget(target);\n        for (const animatable of animatables) {\n            animatable.stop(animationName, targetMask);\n        }\n    };\n    sceneClass.prototype.stopAllAnimations = function () {\n        if (this._activeAnimatables) {\n            for (let i = 0; i < this._activeAnimatables.length; i++) {\n                this._activeAnimatables[i].stop(undefined, undefined, true);\n            }\n            this._activeAnimatables.length = 0;\n        }\n        for (const group of this.animationGroups) {\n            group.stop();\n        }\n    };\n}\n//# sourceMappingURL=animatable.core.js.map", "import { Bone } from \"../Bones/bone.js\";\nimport { AddAnimationExtensions } from \"./animatable.core.js\";\nimport { Scene } from \"../scene.js\";\nexport * from \"./animatable.core.js\";\n// Connect everything!\nAddAnimationExtensions(Scene, Bone);\n//# sourceMappingURL=animatable.js.map", "import { Animation } from \"./animation.js\";\nimport { Observable } from \"../Misc/observable.js\";\nimport { EngineStore } from \"../Engines/engineStore.js\";\nimport { Tags } from \"../Misc/tags.js\";\nimport \"./animatable.js\";\n/**\n * This class defines the direct association between an animation and a target\n */\nexport class TargetedAnimation {\n    /**\n     * Returns the string \"TargetedAnimation\"\n     * @returns \"TargetedAnimation\"\n     */\n    getClassName() {\n        return \"TargetedAnimation\";\n    }\n    /**\n     * Serialize the object\n     * @returns the JSON object representing the current entity\n     */\n    serialize() {\n        const serializationObject = {};\n        serializationObject.animation = this.animation.serialize();\n        serializationObject.targetId = this.target.id;\n        return serializationObject;\n    }\n}\n/**\n * Use this class to create coordinated animations on multiple targets\n */\nexport class AnimationGroup {\n    /**\n     * Gets or sets the mask associated with this animation group. This mask is used to filter which objects should be animated.\n     */\n    get mask() {\n        return this._mask;\n    }\n    set mask(value) {\n        if (this._mask === value) {\n            return;\n        }\n        this._mask = value;\n        this.syncWithMask(true);\n    }\n    /**\n     * Makes sure that the animations are either played or stopped according to the animation group mask.\n     * Note however that the call won't have any effect if the animation group has not been started yet.\n     * @param forceUpdate If true, forces to loop over the animatables even if no mask is defined (used internally, you shouldn't need to use it). Default: false.\n     */\n    syncWithMask(forceUpdate = false) {\n        if (!this.mask && !forceUpdate) {\n            this._numActiveAnimatables = this._targetedAnimations.length;\n            return;\n        }\n        this._numActiveAnimatables = 0;\n        for (let i = 0; i < this._animatables.length; ++i) {\n            const animatable = this._animatables[i];\n            if (!this.mask || this.mask.disabled || this.mask.retainsTarget(animatable.target.name)) {\n                this._numActiveAnimatables++;\n                if (animatable.paused) {\n                    animatable.restart();\n                }\n            }\n            else {\n                if (!animatable.paused) {\n                    animatable.pause();\n                }\n            }\n        }\n    }\n    /**\n     * Removes all animations for the targets not retained by the animation group mask.\n     * Use this function if you know you won't need those animations anymore and if you want to free memory.\n     */\n    removeUnmaskedAnimations() {\n        if (!this.mask || this.mask.disabled) {\n            return;\n        }\n        // Removes all animatables (in case the animation group has already been started)\n        for (let i = 0; i < this._animatables.length; ++i) {\n            const animatable = this._animatables[i];\n            if (!this.mask.retainsTarget(animatable.target.name)) {\n                animatable.stop();\n                this._animatables.splice(i, 1);\n                --i;\n            }\n        }\n        // Removes the targeted animations\n        for (let index = 0; index < this._targetedAnimations.length; index++) {\n            const targetedAnimation = this._targetedAnimations[index];\n            if (!this.mask.retainsTarget(targetedAnimation.target.name)) {\n                this._targetedAnimations.splice(index, 1);\n                --index;\n            }\n        }\n    }\n    /**\n     * Gets or sets the first frame\n     */\n    get from() {\n        return this._from;\n    }\n    set from(value) {\n        if (this._from === value) {\n            return;\n        }\n        this._from = value;\n        for (let index = 0; index < this._animatables.length; index++) {\n            const animatable = this._animatables[index];\n            animatable.fromFrame = this._from;\n        }\n    }\n    /**\n     * Gets or sets the last frame\n     */\n    get to() {\n        return this._to;\n    }\n    set to(value) {\n        if (this._to === value) {\n            return;\n        }\n        this._to = value;\n        for (let index = 0; index < this._animatables.length; index++) {\n            const animatable = this._animatables[index];\n            animatable.toFrame = this._to;\n        }\n    }\n    /**\n     * Define if the animations are started\n     */\n    get isStarted() {\n        return this._isStarted;\n    }\n    /**\n     * Gets a value indicating that the current group is playing\n     */\n    get isPlaying() {\n        return this._isStarted && !this._isPaused;\n    }\n    /**\n     * Gets or sets the speed ratio to use for all animations\n     */\n    get speedRatio() {\n        return this._speedRatio;\n    }\n    /**\n     * Gets or sets the speed ratio to use for all animations\n     */\n    set speedRatio(value) {\n        if (this._speedRatio === value) {\n            return;\n        }\n        this._speedRatio = value;\n        for (let index = 0; index < this._animatables.length; index++) {\n            const animatable = this._animatables[index];\n            animatable.speedRatio = this._speedRatio;\n        }\n    }\n    /**\n     * Gets or sets if all animations should loop or not\n     */\n    get loopAnimation() {\n        return this._loopAnimation;\n    }\n    set loopAnimation(value) {\n        if (this._loopAnimation === value) {\n            return;\n        }\n        this._loopAnimation = value;\n        for (let index = 0; index < this._animatables.length; index++) {\n            const animatable = this._animatables[index];\n            animatable.loopAnimation = this._loopAnimation;\n        }\n    }\n    /**\n     * Gets or sets if all animations should be evaluated additively\n     */\n    get isAdditive() {\n        return this._isAdditive;\n    }\n    set isAdditive(value) {\n        if (this._isAdditive === value) {\n            return;\n        }\n        this._isAdditive = value;\n        for (let index = 0; index < this._animatables.length; index++) {\n            const animatable = this._animatables[index];\n            animatable.isAdditive = this._isAdditive;\n        }\n    }\n    /**\n     * Gets or sets the weight to apply to all animations of the group\n     */\n    get weight() {\n        return this._weight;\n    }\n    set weight(value) {\n        if (this._weight === value) {\n            return;\n        }\n        this._weight = value;\n        this.setWeightForAllAnimatables(this._weight);\n    }\n    /**\n     * Gets the targeted animations for this animation group\n     */\n    get targetedAnimations() {\n        return this._targetedAnimations;\n    }\n    /**\n     * returning the list of animatables controlled by this animation group.\n     */\n    get animatables() {\n        return this._animatables;\n    }\n    /**\n     * Gets the list of target animations\n     */\n    get children() {\n        return this._targetedAnimations;\n    }\n    /**\n     * Gets or sets the order of play of the animation group (default: 0)\n     */\n    get playOrder() {\n        return this._playOrder;\n    }\n    set playOrder(value) {\n        if (this._playOrder === value) {\n            return;\n        }\n        this._playOrder = value;\n        if (this._animatables.length > 0) {\n            for (let i = 0; i < this._animatables.length; i++) {\n                this._animatables[i].playOrder = this._playOrder;\n            }\n            this._scene.sortActiveAnimatables();\n        }\n    }\n    /**\n     * Allows the animations of the animation group to blend with current running animations\n     * Note that a null value means that each animation will use their own existing blending configuration (Animation.enableBlending)\n     */\n    get enableBlending() {\n        return this._enableBlending;\n    }\n    set enableBlending(value) {\n        if (this._enableBlending === value) {\n            return;\n        }\n        this._enableBlending = value;\n        if (value !== null) {\n            for (let i = 0; i < this._targetedAnimations.length; ++i) {\n                this._targetedAnimations[i].animation.enableBlending = value;\n            }\n        }\n    }\n    /**\n     * Gets or sets the animation blending speed\n     * Note that a null value means that each animation will use their own existing blending configuration (Animation.blendingSpeed)\n     */\n    get blendingSpeed() {\n        return this._blendingSpeed;\n    }\n    set blendingSpeed(value) {\n        if (this._blendingSpeed === value) {\n            return;\n        }\n        this._blendingSpeed = value;\n        if (value !== null) {\n            for (let i = 0; i < this._targetedAnimations.length; ++i) {\n                this._targetedAnimations[i].animation.blendingSpeed = value;\n            }\n        }\n    }\n    /**\n     * Gets the length (in seconds) of the animation group\n     * This function assumes that all animations are played at the same framePerSecond speed!\n     * Note: you can only call this method after you've added at least one targeted animation!\n     * @param from Starting frame range (default is AnimationGroup.from)\n     * @param to Ending frame range (default is AnimationGroup.to)\n     * @returns The length in seconds\n     */\n    getLength(from, to) {\n        from = from ?? this._from;\n        to = to ?? this._to;\n        const fps = this.targetedAnimations[0].animation.framePerSecond * this._speedRatio;\n        return (to - from) / fps;\n    }\n    /**\n     * Merge the array of animation groups into a new animation group\n     * @param animationGroups List of animation groups to merge\n     * @param disposeSource If true, animation groups will be disposed after being merged (default: true)\n     * @param normalize If true, animation groups will be normalized before being merged, so that all animations have the same \"from\" and \"to\" frame (default: false)\n     * @param weight Weight for the new animation group. If not provided, it will inherit the weight from the first animation group of the array\n     * @returns The new animation group or null if no animation groups were passed\n     */\n    static MergeAnimationGroups(animationGroups, disposeSource = true, normalize = false, weight) {\n        if (animationGroups.length === 0) {\n            return null;\n        }\n        weight = weight ?? animationGroups[0].weight;\n        let beginFrame = Number.MAX_VALUE;\n        let endFrame = -Number.MAX_VALUE;\n        if (normalize) {\n            for (const animationGroup of animationGroups) {\n                if (animationGroup.from < beginFrame) {\n                    beginFrame = animationGroup.from;\n                }\n                if (animationGroup.to > endFrame) {\n                    endFrame = animationGroup.to;\n                }\n            }\n        }\n        const mergedAnimationGroup = new AnimationGroup(animationGroups[0].name + \"_merged\", animationGroups[0]._scene, weight);\n        for (const animationGroup of animationGroups) {\n            if (normalize) {\n                animationGroup.normalize(beginFrame, endFrame);\n            }\n            for (const targetedAnimation of animationGroup.targetedAnimations) {\n                mergedAnimationGroup.addTargetedAnimation(targetedAnimation.animation, targetedAnimation.target);\n            }\n            if (disposeSource) {\n                animationGroup.dispose();\n            }\n        }\n        return mergedAnimationGroup;\n    }\n    /**\n     * Instantiates a new Animation Group.\n     * This helps managing several animations at once.\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/groupAnimations\n     * @param name Defines the name of the group\n     * @param scene Defines the scene the group belongs to\n     * @param weight Defines the weight to use for animations in the group (-1.0 by default, meaning \"no weight\")\n     * @param playOrder Defines the order of play of the animation group (default is 0)\n     */\n    constructor(\n    /** The name of the animation group */\n    name, scene = null, weight = -1, playOrder = 0) {\n        this.name = name;\n        this._targetedAnimations = new Array();\n        this._animatables = new Array();\n        this._from = Number.MAX_VALUE;\n        this._to = -Number.MAX_VALUE;\n        this._speedRatio = 1;\n        this._loopAnimation = false;\n        this._isAdditive = false;\n        this._weight = -1;\n        this._playOrder = 0;\n        this._enableBlending = null;\n        this._blendingSpeed = null;\n        this._numActiveAnimatables = 0;\n        this._shouldStart = true;\n        /** @internal */\n        this._parentContainer = null;\n        /**\n         * This observable will notify when one animation have ended\n         */\n        this.onAnimationEndObservable = new Observable();\n        /**\n         * Observer raised when one animation loops\n         */\n        this.onAnimationLoopObservable = new Observable();\n        /**\n         * Observer raised when all animations have looped\n         */\n        this.onAnimationGroupLoopObservable = new Observable();\n        /**\n         * This observable will notify when all animations have ended.\n         */\n        this.onAnimationGroupEndObservable = new Observable();\n        /**\n         * This observable will notify when all animations have paused.\n         */\n        this.onAnimationGroupPauseObservable = new Observable();\n        /**\n         * This observable will notify when all animations are playing.\n         */\n        this.onAnimationGroupPlayObservable = new Observable();\n        /**\n         * Gets or sets an object used to store user defined information for the node\n         */\n        this.metadata = null;\n        this._mask = null;\n        this._animationLoopFlags = [];\n        this._scene = scene || EngineStore.LastCreatedScene;\n        this._weight = weight;\n        this._playOrder = playOrder;\n        this.uniqueId = this._scene.getUniqueId();\n        this._scene.addAnimationGroup(this);\n    }\n    /**\n     * Add an animation (with its target) in the group\n     * @param animation defines the animation we want to add\n     * @param target defines the target of the animation\n     * @returns the TargetedAnimation object\n     */\n    addTargetedAnimation(animation, target) {\n        const targetedAnimation = new TargetedAnimation();\n        targetedAnimation.animation = animation;\n        targetedAnimation.target = target;\n        const keys = animation.getKeys();\n        if (this._from > keys[0].frame) {\n            this._from = keys[0].frame;\n        }\n        if (this._to < keys[keys.length - 1].frame) {\n            this._to = keys[keys.length - 1].frame;\n        }\n        if (this._enableBlending !== null) {\n            animation.enableBlending = this._enableBlending;\n        }\n        if (this._blendingSpeed !== null) {\n            animation.blendingSpeed = this._blendingSpeed;\n        }\n        this._targetedAnimations.push(targetedAnimation);\n        this._shouldStart = true;\n        return targetedAnimation;\n    }\n    /**\n     * Remove an animation from the group\n     * @param animation defines the animation we want to remove\n     */\n    removeTargetedAnimation(animation) {\n        for (let index = this._targetedAnimations.length - 1; index > -1; index--) {\n            const targetedAnimation = this._targetedAnimations[index];\n            if (targetedAnimation.animation === animation) {\n                this._targetedAnimations.splice(index, 1);\n            }\n        }\n    }\n    /**\n     * This function will normalize every animation in the group to make sure they all go from beginFrame to endFrame\n     * It can add constant keys at begin or end\n     * @param beginFrame defines the new begin frame for all animations or the smallest begin frame of all animations if null (defaults to null)\n     * @param endFrame defines the new end frame for all animations or the largest end frame of all animations if null (defaults to null)\n     * @returns the animation group\n     */\n    normalize(beginFrame = null, endFrame = null) {\n        if (beginFrame == null) {\n            beginFrame = this._from;\n        }\n        if (endFrame == null) {\n            endFrame = this._to;\n        }\n        for (let index = 0; index < this._targetedAnimations.length; index++) {\n            const targetedAnimation = this._targetedAnimations[index];\n            const keys = targetedAnimation.animation.getKeys();\n            const startKey = keys[0];\n            const endKey = keys[keys.length - 1];\n            if (startKey.frame > beginFrame) {\n                const newKey = {\n                    frame: beginFrame,\n                    value: startKey.value,\n                    inTangent: startKey.inTangent,\n                    outTangent: startKey.outTangent,\n                    interpolation: startKey.interpolation,\n                };\n                keys.splice(0, 0, newKey);\n            }\n            if (endKey.frame < endFrame) {\n                const newKey = {\n                    frame: endFrame,\n                    value: endKey.value,\n                    inTangent: endKey.inTangent,\n                    outTangent: endKey.outTangent,\n                    interpolation: endKey.interpolation,\n                };\n                keys.push(newKey);\n            }\n        }\n        this._from = beginFrame;\n        this._to = endFrame;\n        return this;\n    }\n    _processLoop(animatable, targetedAnimation, index) {\n        animatable.onAnimationLoop = () => {\n            this.onAnimationLoopObservable.notifyObservers(targetedAnimation);\n            if (this._animationLoopFlags[index]) {\n                return;\n            }\n            this._animationLoopFlags[index] = true;\n            this._animationLoopCount++;\n            if (this._animationLoopCount === this._numActiveAnimatables) {\n                this.onAnimationGroupLoopObservable.notifyObservers(this);\n                this._animationLoopCount = 0;\n                this._animationLoopFlags.length = 0;\n            }\n        };\n    }\n    /**\n     * Start all animations on given targets\n     * @param loop defines if animations must loop\n     * @param speedRatio defines the ratio to apply to animation speed (1 by default)\n     * @param from defines the from key (optional)\n     * @param to defines the to key (optional)\n     * @param isAdditive defines the additive state for the resulting animatables (optional)\n     * @returns the current animation group\n     */\n    start(loop = false, speedRatio = 1, from, to, isAdditive) {\n        if (this._isStarted || this._targetedAnimations.length === 0) {\n            return this;\n        }\n        this._loopAnimation = loop;\n        this._shouldStart = false;\n        this._animationLoopCount = 0;\n        this._animationLoopFlags.length = 0;\n        for (let index = 0; index < this._targetedAnimations.length; index++) {\n            const targetedAnimation = this._targetedAnimations[index];\n            const animatable = this._scene.beginDirectAnimation(targetedAnimation.target, [targetedAnimation.animation], from !== undefined ? from : this._from, to !== undefined ? to : this._to, loop, speedRatio, undefined, undefined, isAdditive !== undefined ? isAdditive : this._isAdditive);\n            animatable.weight = this._weight;\n            animatable.playOrder = this._playOrder;\n            animatable.onAnimationEnd = () => {\n                this.onAnimationEndObservable.notifyObservers(targetedAnimation);\n                this._checkAnimationGroupEnded(animatable);\n            };\n            this._processLoop(animatable, targetedAnimation, index);\n            this._animatables.push(animatable);\n        }\n        this.syncWithMask();\n        this._scene.sortActiveAnimatables();\n        this._speedRatio = speedRatio;\n        this._isStarted = true;\n        this._isPaused = false;\n        this.onAnimationGroupPlayObservable.notifyObservers(this);\n        return this;\n    }\n    /**\n     * Pause all animations\n     * @returns the animation group\n     */\n    pause() {\n        if (!this._isStarted) {\n            return this;\n        }\n        this._isPaused = true;\n        for (let index = 0; index < this._animatables.length; index++) {\n            const animatable = this._animatables[index];\n            animatable.pause();\n        }\n        this.onAnimationGroupPauseObservable.notifyObservers(this);\n        return this;\n    }\n    /**\n     * Play all animations to initial state\n     * This function will start() the animations if they were not started or will restart() them if they were paused\n     * @param loop defines if animations must loop\n     * @returns the animation group\n     */\n    play(loop) {\n        // only if there are animatable available\n        if (this.isStarted && this._animatables.length && !this._shouldStart) {\n            if (loop !== undefined) {\n                this.loopAnimation = loop;\n            }\n            this.restart();\n        }\n        else {\n            this.stop();\n            this.start(loop, this._speedRatio);\n        }\n        return this;\n    }\n    /**\n     * Reset all animations to initial state\n     * @returns the animation group\n     */\n    reset() {\n        if (!this._isStarted) {\n            this.play();\n            this.goToFrame(0);\n            this.stop(true);\n            return this;\n        }\n        for (let index = 0; index < this._animatables.length; index++) {\n            const animatable = this._animatables[index];\n            animatable.reset();\n        }\n        return this;\n    }\n    /**\n     * Restart animations from after pausing it\n     * @returns the animation group\n     */\n    restart() {\n        if (!this._isStarted) {\n            return this;\n        }\n        for (let index = 0; index < this._animatables.length; index++) {\n            const animatable = this._animatables[index];\n            animatable.restart();\n        }\n        this.syncWithMask();\n        this._isPaused = false;\n        this.onAnimationGroupPlayObservable.notifyObservers(this);\n        return this;\n    }\n    /**\n     * Stop all animations\n     * @param skipOnAnimationEnd defines if the system should not raise onAnimationEnd. Default is false\n     * @returns the animation group\n     */\n    stop(skipOnAnimationEnd = false) {\n        if (!this._isStarted) {\n            return this;\n        }\n        const list = this._animatables.slice();\n        for (let index = 0; index < list.length; index++) {\n            list[index].stop(undefined, undefined, true, skipOnAnimationEnd);\n        }\n        // We will take care of removing all stopped animatables\n        let curIndex = 0;\n        for (let index = 0; index < this._scene._activeAnimatables.length; index++) {\n            const animatable = this._scene._activeAnimatables[index];\n            if (animatable._runtimeAnimations.length > 0) {\n                this._scene._activeAnimatables[curIndex++] = animatable;\n            }\n            else if (skipOnAnimationEnd) {\n                // We normally rely on the onAnimationEnd callback (assigned in the start function) to be notified when an animatable\n                // ends and should be removed from the active animatables array. However, if the animatable is stopped with the skipOnAnimationEnd\n                // flag set to true, then we need to explicitly remove it from the active animatables array.\n                this._checkAnimationGroupEnded(animatable, skipOnAnimationEnd);\n            }\n        }\n        this._scene._activeAnimatables.length = curIndex;\n        this._isStarted = false;\n        return this;\n    }\n    /**\n     * Set animation weight for all animatables\n     *\n     * @since 6.12.4\n     *  You can pass the weight to the AnimationGroup constructor, or use the weight property to set it after the group has been created,\n     *  making it easier to define the overall animation weight than calling setWeightForAllAnimatables() after the animation group has been started\n     * @param weight defines the weight to use\n     * @returns the animationGroup\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-weights\n     */\n    setWeightForAllAnimatables(weight) {\n        for (let index = 0; index < this._animatables.length; index++) {\n            const animatable = this._animatables[index];\n            animatable.weight = weight;\n        }\n        return this;\n    }\n    /**\n     * Synchronize and normalize all animatables with a source animatable\n     * @param root defines the root animatable to synchronize with (null to stop synchronizing)\n     * @returns the animationGroup\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-weights\n     */\n    syncAllAnimationsWith(root) {\n        for (let index = 0; index < this._animatables.length; index++) {\n            const animatable = this._animatables[index];\n            animatable.syncWith(root);\n        }\n        return this;\n    }\n    /**\n     * Goes to a specific frame in this animation group. Note that the animation group must be in playing or paused status\n     * @param frame the frame number to go to\n     * @param useWeight defines whether the animation weight should be applied to the image to be jumped to (false by default)\n     * @returns the animationGroup\n     */\n    goToFrame(frame, useWeight = false) {\n        if (!this._isStarted) {\n            return this;\n        }\n        for (let index = 0; index < this._animatables.length; index++) {\n            const animatable = this._animatables[index];\n            animatable.goToFrame(frame, useWeight);\n        }\n        return this;\n    }\n    /**\n     * Helper to get the current frame. This will return 0 if the AnimationGroup is not running, and it might return wrong results if multiple animations are running in different frames.\n     * @returns current animation frame.\n     */\n    getCurrentFrame() {\n        return this.animatables[0]?.masterFrame || 0;\n    }\n    /**\n     * Dispose all associated resources\n     */\n    dispose() {\n        if (this.isStarted) {\n            this.stop();\n        }\n        this._targetedAnimations.length = 0;\n        this._animatables.length = 0;\n        // Remove from scene\n        const index = this._scene.animationGroups.indexOf(this);\n        if (index > -1) {\n            this._scene.animationGroups.splice(index, 1);\n        }\n        if (this._parentContainer) {\n            const index = this._parentContainer.animationGroups.indexOf(this);\n            if (index > -1) {\n                this._parentContainer.animationGroups.splice(index, 1);\n            }\n            this._parentContainer = null;\n        }\n        this.onAnimationEndObservable.clear();\n        this.onAnimationGroupEndObservable.clear();\n        this.onAnimationGroupPauseObservable.clear();\n        this.onAnimationGroupPlayObservable.clear();\n        this.onAnimationLoopObservable.clear();\n        this.onAnimationGroupLoopObservable.clear();\n    }\n    _checkAnimationGroupEnded(animatable, skipOnAnimationEnd = false) {\n        // animatable should be taken out of the array\n        const idx = this._animatables.indexOf(animatable);\n        if (idx > -1) {\n            this._animatables.splice(idx, 1);\n        }\n        // all animatables were removed? animation group ended!\n        if (this._animatables.length === this._targetedAnimations.length - this._numActiveAnimatables) {\n            this._isStarted = false;\n            if (!skipOnAnimationEnd) {\n                this.onAnimationGroupEndObservable.notifyObservers(this);\n            }\n            this._animatables.length = 0;\n        }\n    }\n    /**\n     * Clone the current animation group and returns a copy\n     * @param newName defines the name of the new group\n     * @param targetConverter defines an optional function used to convert current animation targets to new ones\n     * @param cloneAnimations defines if the animations should be cloned or referenced\n     * @returns the new animation group\n     */\n    clone(newName, targetConverter, cloneAnimations = false) {\n        const newGroup = new AnimationGroup(newName || this.name, this._scene, this._weight, this._playOrder);\n        newGroup._from = this.from;\n        newGroup._to = this.to;\n        newGroup._speedRatio = this.speedRatio;\n        newGroup._loopAnimation = this.loopAnimation;\n        newGroup._isAdditive = this.isAdditive;\n        newGroup._enableBlending = this.enableBlending;\n        newGroup._blendingSpeed = this.blendingSpeed;\n        newGroup.metadata = this.metadata;\n        newGroup.mask = this.mask;\n        for (const targetAnimation of this._targetedAnimations) {\n            newGroup.addTargetedAnimation(cloneAnimations ? targetAnimation.animation.clone() : targetAnimation.animation, targetConverter ? targetConverter(targetAnimation.target) : targetAnimation.target);\n        }\n        return newGroup;\n    }\n    /**\n     * Serializes the animationGroup to an object\n     * @returns Serialized object\n     */\n    serialize() {\n        const serializationObject = {};\n        serializationObject.name = this.name;\n        serializationObject.from = this.from;\n        serializationObject.to = this.to;\n        serializationObject.speedRatio = this.speedRatio;\n        serializationObject.loopAnimation = this.loopAnimation;\n        serializationObject.isAdditive = this.isAdditive;\n        serializationObject.weight = this.weight;\n        serializationObject.playOrder = this.playOrder;\n        serializationObject.enableBlending = this.enableBlending;\n        serializationObject.blendingSpeed = this.blendingSpeed;\n        serializationObject.targetedAnimations = [];\n        for (let targetedAnimationIndex = 0; targetedAnimationIndex < this.targetedAnimations.length; targetedAnimationIndex++) {\n            const targetedAnimation = this.targetedAnimations[targetedAnimationIndex];\n            serializationObject.targetedAnimations[targetedAnimationIndex] = targetedAnimation.serialize();\n        }\n        if (Tags && Tags.HasTags(this)) {\n            serializationObject.tags = Tags.GetTags(this);\n        }\n        // Metadata\n        if (this.metadata) {\n            serializationObject.metadata = this.metadata;\n        }\n        return serializationObject;\n    }\n    // Statics\n    /**\n     * Returns a new AnimationGroup object parsed from the source provided.\n     * @param parsedAnimationGroup defines the source\n     * @param scene defines the scene that will receive the animationGroup\n     * @returns a new AnimationGroup\n     */\n    static Parse(parsedAnimationGroup, scene) {\n        const animationGroup = new AnimationGroup(parsedAnimationGroup.name, scene, parsedAnimationGroup.weight, parsedAnimationGroup.playOrder);\n        for (let i = 0; i < parsedAnimationGroup.targetedAnimations.length; i++) {\n            const targetedAnimation = parsedAnimationGroup.targetedAnimations[i];\n            const animation = Animation.Parse(targetedAnimation.animation);\n            const id = targetedAnimation.targetId;\n            if (targetedAnimation.animation.property === \"influence\") {\n                // morph target animation\n                const morphTarget = scene.getMorphTargetById(id);\n                if (morphTarget) {\n                    animationGroup.addTargetedAnimation(animation, morphTarget);\n                }\n            }\n            else {\n                const targetNode = scene.getNodeById(id);\n                if (targetNode != null) {\n                    animationGroup.addTargetedAnimation(animation, targetNode);\n                }\n            }\n        }\n        if (Tags) {\n            Tags.AddTagsTo(animationGroup, parsedAnimationGroup.tags);\n        }\n        if (parsedAnimationGroup.from !== null && parsedAnimationGroup.to !== null) {\n            animationGroup.normalize(parsedAnimationGroup.from, parsedAnimationGroup.to);\n        }\n        if (parsedAnimationGroup.speedRatio !== undefined) {\n            animationGroup._speedRatio = parsedAnimationGroup.speedRatio;\n        }\n        if (parsedAnimationGroup.loopAnimation !== undefined) {\n            animationGroup._loopAnimation = parsedAnimationGroup.loopAnimation;\n        }\n        if (parsedAnimationGroup.isAdditive !== undefined) {\n            animationGroup._isAdditive = parsedAnimationGroup.isAdditive;\n        }\n        if (parsedAnimationGroup.weight !== undefined) {\n            animationGroup._weight = parsedAnimationGroup.weight;\n        }\n        if (parsedAnimationGroup.playOrder !== undefined) {\n            animationGroup._playOrder = parsedAnimationGroup.playOrder;\n        }\n        if (parsedAnimationGroup.enableBlending !== undefined) {\n            animationGroup._enableBlending = parsedAnimationGroup.enableBlending;\n        }\n        if (parsedAnimationGroup.blendingSpeed !== undefined) {\n            animationGroup._blendingSpeed = parsedAnimationGroup.blendingSpeed;\n        }\n        if (parsedAnimationGroup.metadata !== undefined) {\n            animationGroup.metadata = parsedAnimationGroup.metadata;\n        }\n        return animationGroup;\n    }\n    /** @internal */\n    static MakeAnimationAdditive(sourceAnimationGroup, referenceFrameOrOptions, range, cloneOriginal = false, clonedName) {\n        let options;\n        if (typeof referenceFrameOrOptions === \"object\") {\n            options = referenceFrameOrOptions;\n        }\n        else {\n            options = {\n                referenceFrame: referenceFrameOrOptions,\n                range: range,\n                cloneOriginalAnimationGroup: cloneOriginal,\n                clonedAnimationName: clonedName,\n            };\n        }\n        let animationGroup = sourceAnimationGroup;\n        if (options.cloneOriginalAnimationGroup) {\n            animationGroup = sourceAnimationGroup.clone(options.clonedAnimationGroupName || animationGroup.name);\n        }\n        const targetedAnimations = animationGroup.targetedAnimations;\n        for (let index = 0; index < targetedAnimations.length; index++) {\n            const targetedAnimation = targetedAnimations[index];\n            targetedAnimation.animation = Animation.MakeAnimationAdditive(targetedAnimation.animation, options);\n        }\n        animationGroup.isAdditive = true;\n        if (options.clipKeys) {\n            // We need to recalculate the from/to frames for the animation group because some keys may have been removed\n            let from = Number.MAX_VALUE;\n            let to = -Number.MAX_VALUE;\n            const targetedAnimations = animationGroup.targetedAnimations;\n            for (let index = 0; index < targetedAnimations.length; index++) {\n                const targetedAnimation = targetedAnimations[index];\n                const animation = targetedAnimation.animation;\n                const keys = animation.getKeys();\n                if (from > keys[0].frame) {\n                    from = keys[0].frame;\n                }\n                if (to < keys[keys.length - 1].frame) {\n                    to = keys[keys.length - 1].frame;\n                }\n            }\n            animationGroup._from = from;\n            animationGroup._to = to;\n        }\n        return animationGroup;\n    }\n    /**\n     * Creates a new animation, keeping only the keys that are inside a given key range\n     * @param sourceAnimationGroup defines the animation group on which to operate\n     * @param fromKey defines the lower bound of the range\n     * @param toKey defines the upper bound of the range\n     * @param name defines the name of the new animation group. If not provided, use the same name as animationGroup\n     * @param dontCloneAnimations defines whether or not the animations should be cloned before clipping the keys. Default is false, so animations will be cloned\n     * @returns a new animation group stripped from all the keys outside the given range\n     */\n    static ClipKeys(sourceAnimationGroup, fromKey, toKey, name, dontCloneAnimations) {\n        const animationGroup = sourceAnimationGroup.clone(name || sourceAnimationGroup.name);\n        return AnimationGroup.ClipKeysInPlace(animationGroup, fromKey, toKey, dontCloneAnimations);\n    }\n    /**\n     * Updates an existing animation, keeping only the keys that are inside a given key range\n     * @param animationGroup defines the animation group on which to operate\n     * @param fromKey defines the lower bound of the range\n     * @param toKey defines the upper bound of the range\n     * @param dontCloneAnimations defines whether or not the animations should be cloned before clipping the keys. Default is false, so animations will be cloned\n     * @returns the animationGroup stripped from all the keys outside the given range\n     */\n    static ClipKeysInPlace(animationGroup, fromKey, toKey, dontCloneAnimations) {\n        return AnimationGroup.ClipInPlace(animationGroup, fromKey, toKey, dontCloneAnimations, false);\n    }\n    /**\n     * Creates a new animation, keeping only the frames that are inside a given frame range\n     * @param sourceAnimationGroup defines the animation group on which to operate\n     * @param fromFrame defines the lower bound of the range\n     * @param toFrame defines the upper bound of the range\n     * @param name defines the name of the new animation group. If not provided, use the same name as animationGroup\n     * @param dontCloneAnimations defines whether or not the animations should be cloned before clipping the frames. Default is false, so animations will be cloned\n     * @returns a new animation group stripped from all the frames outside the given range\n     */\n    static ClipFrames(sourceAnimationGroup, fromFrame, toFrame, name, dontCloneAnimations) {\n        const animationGroup = sourceAnimationGroup.clone(name || sourceAnimationGroup.name);\n        return AnimationGroup.ClipFramesInPlace(animationGroup, fromFrame, toFrame, dontCloneAnimations);\n    }\n    /**\n     * Updates an existing animation, keeping only the frames that are inside a given frame range\n     * @param animationGroup defines the animation group on which to operate\n     * @param fromFrame defines the lower bound of the range\n     * @param toFrame defines the upper bound of the range\n     * @param dontCloneAnimations defines whether or not the animations should be cloned before clipping the frames. Default is false, so animations will be cloned\n     * @returns the animationGroup stripped from all the frames outside the given range\n     */\n    static ClipFramesInPlace(animationGroup, fromFrame, toFrame, dontCloneAnimations) {\n        return AnimationGroup.ClipInPlace(animationGroup, fromFrame, toFrame, dontCloneAnimations, true);\n    }\n    /**\n     * Updates an existing animation, keeping only the keys that are inside a given key or frame range\n     * @param animationGroup defines the animation group on which to operate\n     * @param start defines the lower bound of the range\n     * @param end defines the upper bound of the range\n     * @param dontCloneAnimations defines whether or not the animations should be cloned before clipping the keys. Default is false, so animations will be cloned\n     * @param useFrame defines if the range is defined by frame numbers or key indices (default is false which means use key indices)\n     * @returns the animationGroup stripped from all the keys outside the given range\n     */\n    static ClipInPlace(animationGroup, start, end, dontCloneAnimations, useFrame = false) {\n        let from = Number.MAX_VALUE;\n        let to = -Number.MAX_VALUE;\n        const targetedAnimations = animationGroup.targetedAnimations;\n        for (let index = 0; index < targetedAnimations.length; index++) {\n            const targetedAnimation = targetedAnimations[index];\n            const animation = dontCloneAnimations ? targetedAnimation.animation : targetedAnimation.animation.clone();\n            if (useFrame) {\n                // Make sure we have keys corresponding to the bounds of the frame range\n                animation.createKeyForFrame(start);\n                animation.createKeyForFrame(end);\n            }\n            const keys = animation.getKeys();\n            const newKeys = [];\n            let startFrame = Number.MAX_VALUE;\n            for (let k = 0; k < keys.length; k++) {\n                const key = keys[k];\n                if ((!useFrame && k >= start && k <= end) || (useFrame && key.frame >= start && key.frame <= end)) {\n                    const newKey = {\n                        frame: key.frame,\n                        value: key.value.clone ? key.value.clone() : key.value,\n                        inTangent: key.inTangent,\n                        outTangent: key.outTangent,\n                        interpolation: key.interpolation,\n                        lockedTangent: key.lockedTangent,\n                    };\n                    if (startFrame === Number.MAX_VALUE) {\n                        startFrame = newKey.frame;\n                    }\n                    newKey.frame -= startFrame;\n                    newKeys.push(newKey);\n                }\n            }\n            if (newKeys.length === 0) {\n                targetedAnimations.splice(index, 1);\n                index--;\n                continue;\n            }\n            if (from > newKeys[0].frame) {\n                from = newKeys[0].frame;\n            }\n            if (to < newKeys[newKeys.length - 1].frame) {\n                to = newKeys[newKeys.length - 1].frame;\n            }\n            animation.setKeys(newKeys, true);\n            targetedAnimation.animation = animation; // in case the animation has been cloned\n        }\n        animationGroup._from = from;\n        animationGroup._to = to;\n        return animationGroup;\n    }\n    /**\n     * Returns the string \"AnimationGroup\"\n     * @returns \"AnimationGroup\"\n     */\n    getClassName() {\n        return \"AnimationGroup\";\n    }\n    /**\n     * Creates a detailed string about the object\n     * @param fullDetails defines if the output string will support multiple levels of logging within scene loading\n     * @returns a string representing the object\n     */\n    toString(fullDetails) {\n        let ret = \"Name: \" + this.name;\n        ret += \", type: \" + this.getClassName();\n        if (fullDetails) {\n            ret += \", from: \" + this._from;\n            ret += \", to: \" + this._to;\n            ret += \", isStarted: \" + this._isStarted;\n            ret += \", speedRatio: \" + this._speedRatio;\n            ret += \", targetedAnimations length: \" + this._targetedAnimations.length;\n            ret += \", animatables length: \" + this._animatables;\n        }\n        return ret;\n    }\n}\n//# sourceMappingURL=animationGroup.js.map"], "names": ["RuntimeAnimation", "target", "animation", "scene", "host", "Animation", "Matrix", "new<PERSON>ey", "index", "events", "e", "targetIndex", "targetPropertyPath", "property", "name", "restoreOriginal", "currentValue", "weight", "originalValue", "runtimeAnimation", "destination", "blendingSpeed", "frame", "keys", "newSpeedRatio", "newAbsoluteFrame", "elapsedTimeSinceAnimationStart", "from", "to", "loop", "speedRatio", "returnValue", "frameRange", "offsetValue", "absoluteFrame", "highLimitValue", "yoyo<PERSON><PERSON>", "yoyoMode", "position", "sin", "direction", "keyOffset", "fromValue", "toValue", "_staticOffsetValueQuaternion", "_staticOffsetValueVector3", "_staticOffsetValueVector2", "_staticOffsetValueSize", "_staticOffsetValueColor3", "_staticOffsetValueColor4", "currentFrame", "syncRoot", "hostNormalizedFrame", "event", "Animatable", "value", "fromFrame", "to<PERSON><PERSON><PERSON>", "loopAnimation", "onAnimationEnd", "animations", "onAnimationLoop", "isAdditive", "playOrder", "Observable", "root", "newRuntimeAnimation", "runtimeAnimations", "useWeight", "fps", "delay", "animationName", "targetMask", "useGlobalSplice", "skipOnAnimationEnd", "idx", "resolve", "running", "isRunning", "ProcessLateAnimationBindingsForMatrices", "holder", "normalizer", "finalPosition", "TmpVectors", "finalScaling", "finalQuaternion", "startIndex", "originalAnimation", "scale", "skipOverride", "animIndex", "currentPosition", "currentScaling", "currentQuaternion", "Quaternion", "Vector3", "workValue", "ProcessLateAnimationBindingsForQuaternions", "refQuaternion", "cumulativeQuaternion", "quaternions", "weights", "cumulativeAmount", "ProcessLateAnimationBindings", "path", "matrixDecomposeMode", "finalValue", "originalAnimationIsLoopRelativeFromCurrent", "AddAnimationExtensions", "sceneClass", "boneClass", "source", "rangeName", "frameOffset", "rescaleAsRequired", "skelDimensionsRatio", "sourceRange", "sourceKeys", "sourceBoneLength", "sourceParent", "parent", "parentScalingReqd", "parentRatio", "dimensionsScalingReqd", "dest<PERSON><PERSON>s", "orig", "origTranslation", "mat", "key", "nKeys", "customDeltaTime", "now", "PrecisionDate", "animatables", "animationTime", "animatable", "a", "b", "returnedAnimatable", "stopCurrent", "tmp", "shouldRunTargetAnimations", "directDescendantsOnly", "children", "result", "child", "i", "group", "Scene", "Bone", "TargetedAnimation", "serializationObject", "AnimationGroup", "forceUpdate", "targetedAnimation", "animationGroups", "disposeSource", "normalize", "beginFrame", "endFrame", "animationGroup", "mergedAnimationGroup", "EngineStore", "startKey", "<PERSON><PERSON><PERSON>", "list", "curIndex", "_a", "newName", "targetConverter", "cloneAnimations", "newGroup", "targetAnimation", "targetedAnimationIndex", "Tags", "parsedAnimationGroup", "id", "morph<PERSON>arget", "targetNode", "sourceAnimationGroup", "referenceFrameOrOptions", "range", "cloneOriginal", "clonedName", "options", "targetedAnimations", "fromKey", "to<PERSON><PERSON>", "dontCloneAnimations", "start", "end", "useFrame", "newKeys", "startFrame", "k", "fullDetails", "ret"], "mappings": "kMAKO,MAAMA,CAAiB,CAI1B,IAAI,cAAe,CACf,OAAO,KAAK,aACf,CAID,IAAI,QAAS,CACT,OAAO,KAAK,OACf,CAID,IAAI,cAAe,CACf,OAAO,KAAK,aACf,CAID,IAAI,YAAa,CACb,OAAO,KAAK,WACf,CAID,IAAI,QAAS,CACT,OAAO,KAAK,oBACf,CAID,IAAI,YAAa,CACb,OAAO,KAAK,OAAS,KAAK,MAAM,UACnC,CAQD,YAAYC,EAAQC,EAAWC,EAAOC,EAAM,CAgFxC,GA/EA,KAAK,QAAU,IAAI,MAInB,KAAK,cAAgB,EAIrB,KAAK,eAAiB,IAAI,MAI1B,KAAK,oBAAsB,KAI3B,KAAK,cAAgB,GAIrB,KAAK,iBAAmB,GAIxB,KAAK,SAAW,GAIhB,KAAK,gBAAkB,EAIvB,KAAK,cAAgB,KACrB,KAAK,qBAAuB,KAC5B,KAAK,cAAgB,KAIrB,KAAK,YAAc,GAInB,KAAK,QAAU,EAIf,KAAK,qBAAuB,EAI5B,KAAK,qBAAuB,EAC5B,KAAK,eAAiB,EAItB,KAAK,uBAAyB,EAC9B,KAAK,eAAiB,GACtB,KAAK,WAAaF,EAClB,KAAK,QAAUD,EACf,KAAK,OAASE,EACd,KAAK,MAAQC,EACb,KAAK,eAAiB,GACtBF,EAAU,mBAAmB,KAAK,IAAI,EAEtC,KAAK,gBAAkB,CACnB,IAAK,EACL,YAAa,EACb,SAAU,KAAK,oBAAqB,CAChD,EACY,KAAK,WAAW,WAAaG,EAAU,uBACvC,KAAK,gBAAgB,UAAYC,EAAO,KAAI,GAGhD,KAAK,MAAQ,KAAK,WAAW,QAAO,EACpC,KAAK,UAAY,KAAK,MAAM,CAAC,EAAE,MAC/B,KAAK,UAAY,KAAK,MAAM,KAAK,MAAM,OAAS,CAAC,EAAE,MACnD,KAAK,UAAY,KAAK,MAAM,CAAC,EAAE,MAC/B,KAAK,UAAY,KAAK,MAAM,KAAK,MAAM,OAAS,CAAC,EAAE,MAE/C,KAAK,YAAc,EAAG,CACtB,MAAMC,EAAS,CAAE,MAAO,EAAG,MAAO,KAAK,WACvC,KAAK,MAAM,OAAO,EAAG,EAAGA,CAAM,CACjC,CAED,GAAI,KAAK,mBAAmB,MAAO,CAC/B,IAAIC,EAAQ,EACZ,UAAWP,KAAU,KAAK,QACtB,KAAK,aAAaA,EAAQO,CAAK,EAC/B,KAAK,mBAAmBA,CAAK,EAC7BA,IAEJ,KAAK,eAAiB,EACzB,MAEG,KAAK,aAAa,KAAK,OAAO,EAC9B,KAAK,mBAAkB,EACvB,KAAK,eAAiB,GACtB,KAAK,cAAgB,KAAK,eAAe,CAAC,EAG9C,MAAMC,EAASP,EAAU,YACrBO,GAAUA,EAAO,OAAS,GAC1BA,EAAO,QAASC,GAAM,CAClB,KAAK,QAAQ,KAAKA,EAAE,OAAQ,CAAA,CAC5C,CAAa,EAEL,KAAK,gBAAkBT,GAAUA,EAAO,4BAA8BA,EAAO,4BAA4B,eAAiB,KAAK,WAAW,cAC7I,CACD,aAAaA,EAAQU,EAAc,EAAG,CAClC,MAAMC,EAAqB,KAAK,WAAW,mBAC3C,GAAIA,EAAmB,OAAS,EAAG,CAC/B,IAAIC,EAAWZ,EACf,QAASO,EAAQ,EAAGA,EAAQI,EAAmB,OAAS,EAAGJ,IAAS,CAChE,MAAMM,EAAOF,EAAmBJ,CAAK,EAErC,GADAK,EAAWA,EAASC,CAAI,EACpBD,IAAa,OACb,MAAM,IAAI,MAAM,qBAAqBC,CAAI,uBAAuBF,EAAmB,KAAK,GAAG,CAAC,GAAG,CAEtG,CACD,KAAK,YAAcA,EAAmBA,EAAmB,OAAS,CAAC,EACnE,KAAK,eAAeD,CAAW,EAAIE,CACtC,MAEG,KAAK,YAAcD,EAAmB,CAAC,EACvC,KAAK,eAAeD,CAAW,EAAIV,EAEvC,GAAI,KAAK,eAAeU,CAAW,EAAE,KAAK,WAAW,IAAM,OACvD,MAAM,IAAI,MAAM,qBAAqB,KAAK,WAAW,uBAAuBC,EAAmB,KAAK,GAAG,CAAC,GAAG,CAElH,CAID,IAAI,WAAY,CACZ,OAAO,KAAK,UACf,CAKD,MAAMG,EAAkB,GAAO,CAC3B,GAAIA,EACA,GAAI,KAAK,mBAAmB,MAAO,CAC/B,IAAIP,EAAQ,EACZ,UAAWP,KAAU,KAAK,QAClB,KAAK,eAAeO,CAAK,IAAM,QAC/B,KAAK,UAAUP,EAAQ,KAAK,eAAeO,CAAK,EAAG,KAAK,eAAeA,CAAK,EAAG,GAAIA,CAAK,EAE5FA,GAEP,MAEO,KAAK,eAAe,CAAC,IAAM,QAC3B,KAAK,UAAU,KAAK,QAAS,KAAK,cAAe,KAAK,eAAe,CAAC,EAAG,GAAI,CAAC,EAI1F,KAAK,cAAgB,GACrB,KAAK,iBAAmB,GACxB,KAAK,cAAgB,EACrB,KAAK,gBAAkB,EAEvB,QAASA,EAAQ,EAAGA,EAAQ,KAAK,QAAQ,OAAQA,IAC7C,KAAK,QAAQA,CAAK,EAAE,OAAS,EAEpC,CAKD,WAAY,CACR,OAAO,KAAK,QACf,CAID,SAAU,CACN,MAAMA,EAAQ,KAAK,WAAW,kBAAkB,QAAQ,IAAI,EACxDA,EAAQ,IACR,KAAK,WAAW,kBAAkB,OAAOA,EAAO,CAAC,CAExD,CAMD,SAASQ,EAAcC,EAAQ,CAC3B,GAAI,KAAK,eAAgB,CACrB,QAAST,EAAQ,EAAGA,EAAQ,KAAK,QAAQ,OAAQA,IAAS,CACtD,MAAMP,EAAS,KAAK,QAAQO,CAAK,EACjC,KAAK,UAAUP,EAAQ,KAAK,eAAeO,CAAK,EAAGQ,EAAcC,EAAQT,CAAK,CACjF,CACD,MACH,CACD,KAAK,UAAU,KAAK,QAAS,KAAK,cAAeQ,EAAcC,EAAQ,CAAC,CAC3E,CACD,mBAAmBN,EAAc,EAAG,CAChC,IAAIO,EACJ,MAAMjB,EAAS,KAAK,eAAeU,CAAW,EAC1CV,EAAO,gBAAkB,KAAK,cAAgB,UAE9CiB,EAAgBjB,EAAO,iBAGvBiB,EAAgBjB,EAAO,KAAK,WAAW,EAEvCiB,GAAiBA,EAAc,MAC/B,KAAK,eAAeP,CAAW,EAAIO,EAAc,MAAK,EAGtD,KAAK,eAAeP,CAAW,EAAIO,CAE1C,CACD,uCAAuCC,EAAkBD,EAAe,CACpE,MAAMjB,EAASkB,EAAiB,OAChC,KAAK,OAAO,oCAAoC,gBAAgBlB,CAAM,EACjEA,EAAO,wBACRA,EAAO,sBAAwB,IAE9BA,EAAO,sBAAsBkB,EAAiB,UAAU,IACzDlB,EAAO,sBAAsBkB,EAAiB,UAAU,EAAI,CACxD,YAAa,EACb,oBAAqB,EACrB,WAAY,CAAE,EACd,mBAAoB,CAAE,EACtB,cAAeD,CAC/B,GAEYC,EAAiB,YACjBlB,EAAO,sBAAsBkB,EAAiB,UAAU,EAAE,mBAAmB,KAAKA,CAAgB,EAClGlB,EAAO,sBAAsBkB,EAAiB,UAAU,EAAE,qBAAuBA,EAAiB,SAGlGlB,EAAO,sBAAsBkB,EAAiB,UAAU,EAAE,WAAW,KAAKA,CAAgB,EAC1FlB,EAAO,sBAAsBkB,EAAiB,UAAU,EAAE,aAAeA,EAAiB,OAEjG,CACD,UAAUlB,EAAQmB,EAAaJ,EAAcC,EAAQN,EAAa,CAI9D,GAFA,KAAK,qBAAuBS,EAC5B,KAAK,QAAUH,EACX,KAAK,iBAAmB,KAAK,iBAAmB,EAAK,CACrD,GAAI,CAAC,KAAK,oBAAqB,CAC3B,MAAMC,EAAgBE,EAAY,KAAK,WAAW,EAC9CF,EAAc,MACd,KAAK,oBAAsBA,EAAc,QAGzC,KAAK,oBAAsBA,CAElC,CACG,KAAK,oBAAoB,EAErBb,EAAU,qCACN,KAAK,cACLC,EAAO,mBAAmB,KAAK,oBAAqBU,EAAc,KAAK,gBAAiB,KAAK,aAAa,EAG1G,KAAK,cAAgBV,EAAO,cAAc,KAAK,oBAAqBU,EAAc,KAAK,eAAe,EAItG,KAAK,cACLV,EAAO,UAAU,KAAK,oBAAqBU,EAAc,KAAK,gBAAiB,KAAK,aAAa,EAGjG,KAAK,cAAgBV,EAAO,KAAK,KAAK,oBAAqBU,EAAc,KAAK,eAAe,EAKrG,KAAK,cAAgBX,EAAU,eAAe,KAAK,oBAAqBW,EAAc,KAAK,eAAe,EAE9G,MAAMK,EAAgBpB,GAAUA,EAAO,4BAA8BA,EAAO,4BAA4B,cAAgB,KAAK,WAAW,cACxI,KAAK,iBAAmBoB,CAC3B,MAEQ,KAAK,cAQD,KAAK,cAAc,SACxB,KAAK,cAAc,SAASL,CAAY,EAGxC,KAAK,cAAgBA,EAXjBA,GAAA,MAAAA,EAAc,MACd,KAAK,cAAgBA,EAAa,QAGlC,KAAK,cAAgBA,EAU7BC,IAAW,GACX,KAAK,uCAAuC,KAAM,KAAK,eAAeN,CAAW,CAAC,EAG9E,KAAK,gBAAgB,WAAaN,EAAU,wCACxC,KAAK,cAAc,SACnB,KAAK,cAAc,SAAS,KAAK,eAAeM,CAAW,EAAGS,EAAY,KAAK,WAAW,CAAC,EAG3FA,EAAY,KAAK,WAAW,EAAI,KAAK,eAAeT,CAAW,EAAI,KAAK,cAI5ES,EAAY,KAAK,WAAW,EAAI,KAAK,cAGzCnB,EAAO,aACPA,EAAO,YAAY,KAAK,WAAW,cAAc,CAExD,CAKD,qBAAsB,CAClB,OAAI,KAAK,SAAW,KAAK,QAAQ,4BACtB,KAAK,QAAQ,4BAA4B,SAE7C,KAAK,WAAW,QAC1B,CAMD,UAAUqB,EAAOL,EAAS,GAAI,CAC1B,MAAMM,EAAO,KAAK,WAAW,QAAO,EAChCD,EAAQC,EAAK,CAAC,EAAE,MAChBD,EAAQC,EAAK,CAAC,EAAE,MAEXD,EAAQC,EAAKA,EAAK,OAAS,CAAC,EAAE,QACnCD,EAAQC,EAAKA,EAAK,OAAS,CAAC,EAAE,OAGlC,MAAMd,EAAS,KAAK,QACpB,GAAIA,EAAO,OACP,QAASD,EAAQ,EAAGA,EAAQC,EAAO,OAAQD,IAClCC,EAAOD,CAAK,EAAE,WAEfC,EAAOD,CAAK,EAAE,OAASC,EAAOD,CAAK,EAAE,MAAQc,GAIzD,KAAK,cAAgBA,EACrB,MAAMN,EAAe,KAAK,WAAW,aAAaM,EAAO,KAAK,eAAe,EAC7E,KAAK,SAASN,EAAcC,CAAM,CACrC,CAID,4BAA4BO,EAAe,CACvC,MAAMC,EAAoB,KAAK,sBAAwB,KAAK,WAAW,eAAiBD,GAAkB,IAC1G,KAAK,qBAAuB,KAAK,uBAAyBC,CAC7D,CAWD,QAAQC,EAAgCC,EAAMC,EAAIC,EAAMC,EAAYb,EAAS,GAAM,CAC/E,MAAMf,EAAY,KAAK,WACjBU,EAAqBV,EAAU,mBACrC,GAAI,CAACU,GAAsBA,EAAmB,OAAS,EACnD,YAAK,SAAW,GACT,GAEX,IAAImB,EAAc,IAEdJ,EAAO,KAAK,WAAaA,EAAO,KAAK,aACrCA,EAAO,KAAK,YAEZC,EAAK,KAAK,WAAaA,EAAK,KAAK,aACjCA,EAAK,KAAK,WAEd,MAAMI,EAAaJ,EAAKD,EACxB,IAAIM,EAEAC,EAAiBR,GAAkCxB,EAAU,eAAiB4B,GAAe,IAAS,KAAK,qBAC3GK,EAAiB,EAEjBC,EAAW,GACf,MAAMC,EAAWR,GAAQ,KAAK,gBAAgB,WAAaxB,EAAU,uBACrE,GAAIgC,EAAU,CACV,MAAMC,GAAYJ,EAAgBP,GAAQK,EAEpCO,EAAM,KAAK,IAAID,EAAW,KAAK,EAAE,EAGvCJ,EAFqB,KAAK,IAAIK,CAAG,EAEFP,EAAaL,EAC5C,MAAMa,EAAYD,GAAO,EAAI,EAAI,GAC7B,KAAK,iBAAmBC,IACxBJ,EAAW,IAEf,KAAK,eAAiBI,CACzB,CAGD,GAFA,KAAK,qBAAuBd,EAC5B,KAAK,uBAAyBQ,EAC1B,CAACL,GAAQD,GAAMD,IAAUO,GAAiBF,GAAcF,EAAa,GAAOI,GAAiB,GAAKJ,EAAa,GAE/GC,EAAc,GACdI,EAAiBjC,EAAU,aAAa,KAAK,SAAS,UAEjD,CAAC2B,GAAQF,GAAQC,IAAQM,GAAiBF,GAAcF,EAAa,GAAOI,GAAiB,GAAKJ,EAAa,GACpHC,EAAc,GACdI,EAAiBjC,EAAU,aAAa,KAAK,SAAS,UAEjD,KAAK,gBAAgB,WAAaG,EAAU,wBAAyB,CAC1E,MAAMoC,EAAYb,EAAG,SAAU,EAAGD,EAAK,SAAQ,EAC/C,GAAI,CAAC,KAAK,cAAcc,CAAS,EAAG,CAChC,KAAK,gBAAgB,YAAc,EACnC,KAAK,gBAAgB,SAAWpC,EAAU,wBAC1C,MAAMqC,EAAYxC,EAAU,aAAayB,EAAM,KAAK,eAAe,EAC7DgB,EAAUzC,EAAU,aAAa0B,EAAI,KAAK,eAAe,EAE/D,OADA,KAAK,gBAAgB,SAAW,KAAK,oBAAmB,EAChD1B,EAAU,SAAQ,CAEtB,KAAKG,EAAU,oBACX,KAAK,cAAcoC,CAAS,EAAIE,EAAUD,EAC1C,MAEJ,KAAKrC,EAAU,yBACX,KAAK,cAAcoC,CAAS,EAAIE,EAAQ,SAASD,CAAS,EAC1D,MAEJ,KAAKrC,EAAU,sBACX,KAAK,cAAcoC,CAAS,EAAIE,EAAQ,SAASD,CAAS,EAC1D,MAEJ,KAAKrC,EAAU,sBACX,KAAK,cAAcoC,CAAS,EAAIE,EAAQ,SAASD,CAAS,EAC1D,MAEJ,KAAKrC,EAAU,mBACX,KAAK,cAAcoC,CAAS,EAAIE,EAAQ,SAASD,CAAS,EAC1D,MAEJ,KAAKrC,EAAU,qBACX,KAAK,cAAcoC,CAAS,EAAIE,EAAQ,SAASD,CAAS,EAC1D,KAGP,CACD,KAAK,iBAAiBD,CAAS,EAAIE,CACtC,CACDR,EAAiB,KAAK,iBAAiBM,CAAS,EAChDR,EAAc,KAAK,cAAcQ,CAAS,CAC7C,CACD,GAAIR,IAAgB,OAChB,OAAQ/B,EAAU,SAAQ,CAEtB,KAAKG,EAAU,oBACX4B,EAAc,EACd,MAEJ,KAAK5B,EAAU,yBACX4B,EAAcW,EACd,MAEJ,KAAKvC,EAAU,sBACX4B,EAAcY,EACd,MAEJ,KAAKxC,EAAU,sBACX4B,EAAca,EACd,MAEJ,KAAKzC,EAAU,mBACX4B,EAAcc,EACd,MAEJ,KAAK1C,EAAU,qBACX4B,EAAce,EACd,MACJ,KAAK3C,EAAU,qBACX4B,EAAcgB,EACd,KACP,CAGL,IAAIC,EACJ,GAAI,KAAK,OAAS,KAAK,MAAM,SAAU,CAEnC,MAAMC,EAAW,KAAK,MAAM,SACtBC,GAAuBD,EAAS,YAAcA,EAAS,YAAcA,EAAS,QAAUA,EAAS,WACvGD,EAAevB,EAAOK,EAAaoB,CACtC,MAEQlB,EAAgB,GAAKP,EAAOC,GAAQM,EAAgB,GAAKP,EAAOC,EACjEsB,EAAenB,GAAeC,IAAe,EAAIJ,EAAMM,EAAgBF,EAAcL,EAGrFuB,EAAenB,GAAeC,IAAe,EAAIL,EAAQO,EAAgBF,EAAcJ,EAG/F,MAAMnB,EAAS,KAAK,QAEpB,GAAK,CAAC4B,IAAcP,EAAa,GAAK,KAAK,aAAeoB,GAAkBpB,EAAa,GAAK,KAAK,aAAeoB,IAAoBb,GAAYD,EAAW,CACzJ,KAAK,QAAO,EAEZ,QAAS5B,EAAQ,EAAGA,EAAQC,EAAO,OAAQD,IAClCC,EAAOD,CAAK,EAAE,WAEfC,EAAOD,CAAK,EAAE,OAAS,IAG/B,KAAK,gBAAgB,IAAMsB,EAAa,EAAI,EAAI5B,EAAU,QAAO,EAAG,OAAS,CAChF,CACD,KAAK,cAAgBgD,EACrB,KAAK,gBAAgB,YAAclB,IAAe,EAAI,EAAKE,EAAgBF,GAAe,EAC1F,KAAK,gBAAgB,eAAiBG,EACtC,KAAK,gBAAgB,YAAcF,EACnC,MAAMjB,EAAed,EAAU,aAAagD,EAAc,KAAK,eAAe,EAI9E,GAFA,KAAK,SAASlC,EAAcC,CAAM,EAE9BR,EAAO,QACP,QAASD,EAAQ,EAAGA,EAAQC,EAAO,OAAQD,IAGvC,GAAKwB,GAAc,GAAKkB,GAAgBzC,EAAOD,CAAK,EAAE,OAASC,EAAOD,CAAK,EAAE,OAASmB,GACjFK,EAAa,GAAKkB,GAAgBzC,EAAOD,CAAK,EAAE,OAASC,EAAOD,CAAK,EAAE,OAASmB,EAAO,CACxF,MAAM0B,EAAQ5C,EAAOD,CAAK,EACrB6C,EAAM,SAEHA,EAAM,WACN5C,EAAO,OAAOD,EAAO,CAAC,EACtBA,KAEJ6C,EAAM,OAAS,GACfA,EAAM,OAAOH,CAAY,EAEhC,EAGT,OAAKnB,IACD,KAAK,SAAW,IAEbA,CACV,CACL,CC7kBO,MAAMuB,CAAW,CAIpB,IAAI,UAAW,CACX,OAAO,KAAK,SACf,CAKD,IAAI,aAAc,CACd,OAAI,KAAK,mBAAmB,SAAW,EAC5B,EAEJ,KAAK,mBAAmB,CAAC,EAAE,YACrC,CAID,IAAI,QAAS,CACT,OAAO,KAAK,OACf,CACD,IAAI,OAAOC,EAAO,CACd,GAAIA,IAAU,GAAI,CAEd,KAAK,QAAU,GACf,MACH,CAED,KAAK,QAAU,KAAK,IAAI,KAAK,IAAIA,EAAO,CAAC,EAAG,CAAG,CAClD,CAID,IAAI,YAAa,CACb,OAAO,KAAK,WACf,CACD,IAAI,WAAWA,EAAO,CAClB,QAAS/C,EAAQ,EAAGA,EAAQ,KAAK,mBAAmB,OAAQA,IACtC,KAAK,mBAAmBA,CAAK,EACrC,4BAA4B+C,CAAK,EAE/C,KAAK,YAAcA,EAEf,KAAK,aAAe,MACpB,KAAK,UAAU,KAAK,UAAU,CAErC,CAID,IAAI,aAAc,CACd,OAAO,KAAK,oBAAsB,KAAO,EAAI,KAAK,OAAO,eAAiB,KAAK,iBAClF,CAeD,YAAYpD,EAEZF,EAEAuD,EAAY,EAEZC,EAAU,IAEVC,EAAgB,GAAO5B,EAAa,EAEpC6B,EAAgBC,EAEhBC,EAEAC,EAAa,GAEbC,EAAY,EAAG,CACX,KAAK,OAAS9D,EACd,KAAK,UAAYuD,EACjB,KAAK,QAAUC,EACf,KAAK,cAAgBC,EACrB,KAAK,eAAiBC,EACtB,KAAK,gBAAkBE,EACvB,KAAK,WAAaC,EAClB,KAAK,UAAYC,EACjB,KAAK,kBAAoB,KACzB,KAAK,aAAe,KACpB,KAAK,iBAAmB,KAExB,KAAK,mBAAqB,IAAI,MAC9B,KAAK,QAAU,GACf,KAAK,YAAc,EACnB,KAAK,QAAU,GACf,KAAK,gBAAkB,GACvB,KAAK,UAAY,KACjB,KAAK,qBAAuB,KAC5B,KAAK,WAAa,KAKlB,KAAK,aAAe,GAIpB,KAAK,iBAAmB,GAIxB,KAAK,yBAA2B,IAAIC,EAIpC,KAAK,0BAA4B,IAAIA,EACrC,KAAK,OAAS7D,EACVyD,GACA,KAAK,iBAAiB3D,EAAQ2D,CAAU,EAE5C,KAAK,YAAc9B,EACnB3B,EAAM,mBAAmB,KAAK,IAAI,CACrC,CAQD,SAAS8D,EAAM,CAEX,GADA,KAAK,UAAYA,EACbA,EAAM,CAEN,MAAMzD,EAAQ,KAAK,OAAO,mBAAmB,QAAQ,IAAI,EACrDA,EAAQ,KACR,KAAK,OAAO,mBAAmB,OAAOA,EAAO,CAAC,EAC9C,KAAK,OAAO,mBAAmB,KAAK,IAAI,EAE/C,CACD,OAAO,IACV,CAKD,eAAgB,CACZ,OAAO,KAAK,kBACf,CAMD,iBAAiBP,EAAQ2D,EAAY,CACjC,QAASpD,EAAQ,EAAGA,EAAQoD,EAAW,OAAQpD,IAAS,CACpD,MAAMN,EAAY0D,EAAWpD,CAAK,EAC5B0D,EAAsB,IAAIlE,EAAiBC,EAAQC,EAAW,KAAK,OAAQ,IAAI,EACrFgE,EAAoB,QAAU,IAAM,CAChC,KAAK,0BAA0B,gBAAgB,IAAI,EAC/C,KAAK,iBACL,KAAK,gBAAe,CAExC,EACY,KAAK,mBAAmB,KAAKA,CAAmB,CACnD,CACJ,CAMD,6BAA6BrD,EAAU,CACnC,MAAMsD,EAAoB,KAAK,mBAC/B,QAAS3D,EAAQ,EAAGA,EAAQ2D,EAAkB,OAAQ3D,IAClD,GAAI2D,EAAkB3D,CAAK,EAAE,UAAU,iBAAmBK,EACtD,OAAOsD,EAAkB3D,CAAK,EAAE,UAGxC,OAAO,IACV,CAMD,oCAAoCK,EAAU,CAC1C,MAAMsD,EAAoB,KAAK,mBAC/B,QAAS3D,EAAQ,EAAGA,EAAQ2D,EAAkB,OAAQ3D,IAClD,GAAI2D,EAAkB3D,CAAK,EAAE,UAAU,iBAAmBK,EACtD,OAAOsD,EAAkB3D,CAAK,EAGtC,OAAO,IACV,CAID,OAAQ,CACJ,MAAM2D,EAAoB,KAAK,mBAC/B,QAAS3D,EAAQ,EAAGA,EAAQ2D,EAAkB,OAAQ3D,IAClD2D,EAAkB3D,CAAK,EAAE,MAAM,EAAI,EAEvC,KAAK,kBAAoB,KACzB,KAAK,aAAe,IACvB,CAMD,eAAea,EAAe,CAC1B,MAAM8C,EAAoB,KAAK,mBAC/B,QAAS3D,EAAQ,EAAGA,EAAQ2D,EAAkB,OAAQ3D,IAClD2D,EAAkB3D,CAAK,EAAE,UAAU,eAAiB,GACpD2D,EAAkB3D,CAAK,EAAE,UAAU,cAAgBa,CAE1D,CAKD,iBAAkB,CACd,MAAM8C,EAAoB,KAAK,mBAC/B,QAAS3D,EAAQ,EAAGA,EAAQ2D,EAAkB,OAAQ3D,IAClD2D,EAAkB3D,CAAK,EAAE,UAAU,eAAiB,EAE3D,CAMD,UAAUc,EAAO8C,EAAY,GAAO,CAChC,MAAMD,EAAoB,KAAK,mBAC/B,GAAIA,EAAkB,CAAC,EAAG,CACtB,MAAME,EAAMF,EAAkB,CAAC,EAAE,UAAU,eAC3C,KAAK,qBAAuB,KAAK,sBAAwBA,EAAkB,CAAC,EAAE,aAC9E,MAAMG,EAAQ,KAAK,aAAe,EAAI,GAAOhD,EAAQ,KAAK,sBAAwB+C,EAAO,IAAQ,KAAK,WACtG,KAAK,iBAAmB,CAACC,CAC5B,CACD,QAAS9D,EAAQ,EAAGA,EAAQ2D,EAAkB,OAAQ3D,IAClD2D,EAAkB3D,CAAK,EAAE,UAAUc,EAAO8C,EAAY,KAAK,QAAU,EAAE,EAE3E,KAAK,WAAa9C,CACrB,CAID,IAAI,QAAS,CACT,OAAO,KAAK,OACf,CAID,OAAQ,CACA,KAAK,UAGT,KAAK,QAAU,GAClB,CAID,SAAU,CACN,KAAK,QAAU,EAClB,CACD,sBAAuB,CACf,KAAK,gBACL,KAAK,eAAc,EAEvB,KAAK,yBAAyB,gBAAgB,IAAI,CACrD,CAQD,KAAKiD,EAAeC,EAAYC,EAAkB,GAAOC,EAAqB,GAAO,CACjF,GAAIH,GAAiBC,EAAY,CAC7B,MAAMG,EAAM,KAAK,OAAO,mBAAmB,QAAQ,IAAI,EACvD,GAAIA,EAAM,GAAI,CACV,MAAMR,EAAoB,KAAK,mBAC/B,QAAS3D,EAAQ2D,EAAkB,OAAS,EAAG3D,GAAS,EAAGA,IAAS,CAChE,MAAMW,EAAmBgD,EAAkB3D,CAAK,EAC5C+D,GAAiBpD,EAAiB,UAAU,MAAQoD,GAGpDC,GAAc,CAACA,EAAWrD,EAAiB,MAAM,IAGrDA,EAAiB,QAAO,EACxBgD,EAAkB,OAAO3D,EAAO,CAAC,EACpC,CACG2D,EAAkB,QAAU,IACvBM,GACD,KAAK,OAAO,mBAAmB,OAAOE,EAAK,CAAC,EAE3CD,GACD,KAAK,qBAAoB,EAGpC,CACJ,KACI,CACD,MAAMlE,EAAQ,KAAK,OAAO,mBAAmB,QAAQ,IAAI,EACzD,GAAIA,EAAQ,GAAI,CACPiE,GACD,KAAK,OAAO,mBAAmB,OAAOjE,EAAO,CAAC,EAElD,MAAM2D,EAAoB,KAAK,mBAC/B,QAAS3D,EAAQ,EAAGA,EAAQ2D,EAAkB,OAAQ3D,IAClD2D,EAAkB3D,CAAK,EAAE,UAE7B,KAAK,mBAAmB,OAAS,EAC5BkE,GACD,KAAK,qBAAoB,CAEhC,CACJ,CACJ,CAKD,WAAY,CACR,OAAO,IAAI,QAASE,GAAY,CAC5B,KAAK,yBAAyB,IAAI,IAAM,CACpCA,EAAQ,IAAI,CACf,EAAE,OAAW,OAAW,KAAM,EAAI,CAC/C,CAAS,CACJ,CAID,SAASN,EAAO,CACZ,GAAI,KAAK,QACL,YAAK,iBAAmB,GACpB,KAAK,eAAiB,OACtB,KAAK,aAAeA,GAEjB,GAgBX,GAdI,KAAK,oBAAsB,MAC3B,KAAK,kBAAoBA,EACzB,KAAK,aAAe,MAEf,KAAK,eAAiB,OAC3B,KAAK,mBAAqBA,EAAQ,KAAK,aACvC,KAAK,aAAe,MAEpB,KAAK,mBAAqB,OAC1B,KAAK,mBAAqB,KAAK,iBAC/B,KAAK,iBAAmB,KACxB,KAAK,qBAAuB,MAEhC,KAAK,WAAa,KACd,KAAK,UAAY,GAAK,KAAK,kBAAoB,EAE/C,MAAO,GAEX,KAAK,gBAAkB,KAAK,QAE5B,IAAIO,EAAU,GACd,MAAMV,EAAoB,KAAK,mBAC/B,IAAI3D,EACJ,IAAKA,EAAQ,EAAGA,EAAQ2D,EAAkB,OAAQ3D,IAAS,CAEvD,MAAMsE,EADYX,EAAkB3D,CAAK,EACb,QAAQ8D,EAAQ,KAAK,kBAAmB,KAAK,UAAW,KAAK,QAAS,KAAK,cAAe,KAAK,YAAa,KAAK,OAAO,EACpJO,EAAUA,GAAWC,CACxB,CAED,GADA,KAAK,iBAAmBD,EACpB,CAACA,EAAS,CACV,GAAI,KAAK,aAKL,IAHArE,EAAQ,KAAK,OAAO,mBAAmB,QAAQ,IAAI,EACnD,KAAK,OAAO,mBAAmB,OAAOA,EAAO,CAAC,EAEzCA,EAAQ,EAAGA,EAAQ2D,EAAkB,OAAQ3D,IAC9C2D,EAAkB3D,CAAK,EAAE,UAGjC,KAAK,qBAAoB,EACrB,KAAK,eACL,KAAK,eAAiB,KACtB,KAAK,gBAAkB,KACvB,KAAK,0BAA0B,QAC/B,KAAK,yBAAyB,QAErC,CACD,OAAOqE,CACV,CACL,CAEA,SAASE,EAAwCC,EAAQ,CACrD,GAAIA,EAAO,cAAgB,GAAKA,EAAO,sBAAwB,EAC3D,OAAOA,EAAO,cAElB,IAAIC,EAAa,EACjB,MAAMC,EAAgBC,EAAW,QAAQ,CAAC,EACpCC,EAAeD,EAAW,QAAQ,CAAC,EACnCE,EAAkBF,EAAW,WAAW,CAAC,EAC/C,IAAIG,EAAa,EACjB,MAAMC,EAAoBP,EAAO,WAAW,CAAC,EACvC9D,EAAgB8D,EAAO,cAC7B,IAAIQ,EAAQ,EACRC,EAAe,GACnB,GAAIT,EAAO,YAAc,EAErBQ,EAAQ,EAAMR,EAAO,YACrB9D,EAAc,UAAUkE,EAAcC,EAAiBH,CAAa,MAEnE,CAKD,GAJAI,EAAa,EAEbL,EAAaD,EAAO,YACpBQ,EAAQD,EAAkB,OAASN,EAC/BO,GAAS,EACT,GAAIR,EAAO,oBACPS,EAAe,OAGf,QAAOF,EAAkB,aAGjCA,EAAkB,aAAa,UAAUH,EAAcC,EAAiBH,CAAa,CACxF,CAED,GAAI,CAACO,EAAc,CACfL,EAAa,aAAaI,CAAK,EAC/BN,EAAc,aAAaM,CAAK,EAChCH,EAAgB,aAAaG,CAAK,EAClC,QAASE,EAAYJ,EAAYI,EAAYV,EAAO,WAAW,OAAQU,IAAa,CAChF,MAAMvE,EAAmB6D,EAAO,WAAWU,CAAS,EACpD,GAAIvE,EAAiB,SAAW,EAC5B,SAEJqE,EAAQrE,EAAiB,OAAS8D,EAClC,MAAMU,EAAkBR,EAAW,QAAQ,CAAC,EACtCS,EAAiBT,EAAW,QAAQ,CAAC,EACrCU,EAAoBV,EAAW,WAAW,CAAC,EACjDhE,EAAiB,aAAa,UAAUyE,EAAgBC,EAAmBF,CAAe,EAC1FC,EAAe,iBAAiBJ,EAAOJ,CAAY,EACnDS,EAAkB,iBAAiBC,EAAW,IAAIT,EAAiBQ,CAAiB,EAAI,EAAIL,EAAQ,CAACA,EAAOH,CAAe,EAC3HM,EAAgB,iBAAiBH,EAAON,CAAa,CACxD,CACDG,EAAgB,UAAS,CAC5B,CAED,QAASK,EAAY,EAAGA,EAAYV,EAAO,mBAAmB,OAAQU,IAAa,CAC/E,MAAMvE,EAAmB6D,EAAO,mBAAmBU,CAAS,EAC5D,GAAIvE,EAAiB,SAAW,EAC5B,SAEJ,MAAMwE,EAAkBR,EAAW,QAAQ,CAAC,EACtCS,EAAiBT,EAAW,QAAQ,CAAC,EACrCU,EAAoBV,EAAW,WAAW,CAAC,EACjDhE,EAAiB,aAAa,UAAUyE,EAAgBC,EAAmBF,CAAe,EAC1FC,EAAe,cAAcR,EAAcQ,CAAc,EACzDG,EAAQ,UAAUX,EAAcQ,EAAgBzE,EAAiB,OAAQiE,CAAY,EACrFC,EAAgB,cAAcQ,EAAmBA,CAAiB,EAClEC,EAAW,WAAWT,EAAiBQ,EAAmB1E,EAAiB,OAAQkE,CAAe,EAClGM,EAAgB,iBAAiBxE,EAAiB,OAAQ+D,CAAa,CAC1E,CACD,MAAMc,EAAYT,EAAoBA,EAAkB,gBAAgB,UAAYJ,EAAW,OAAO,CAAC,EAAE,QACzG,OAAA7E,EAAO,aAAa8E,EAAcC,EAAiBH,EAAec,CAAS,EACpEA,CACX,CAEA,SAASC,EAA2CjB,EAAQkB,EAAe,CACvE,GAAIlB,EAAO,cAAgB,GAAKA,EAAO,sBAAwB,EAC3D,OAAOkB,EAEX,MAAMX,EAAoBP,EAAO,WAAW,CAAC,EACvC9D,EAAgB8D,EAAO,cAC7B,IAAImB,EAAuBD,EAC3B,GAAIlB,EAAO,cAAgB,GAAKA,EAAO,oBAAsB,EACzDmB,EAAqB,SAASjF,CAAa,UAEtC8D,EAAO,WAAW,SAAW,GAElC,GADAc,EAAW,WAAW5E,EAAeqE,EAAkB,aAAc,KAAK,IAAI,EAAKP,EAAO,WAAW,EAAGmB,CAAoB,EACxHnB,EAAO,sBAAwB,EAC/B,OAAOmB,UAGNnB,EAAO,WAAW,OAAS,EAAG,CAEnC,IAAIC,EAAa,EACbmB,EACAC,EACJ,GAAIrB,EAAO,YAAc,EAAK,CAC1B,MAAMQ,EAAQ,EAAMR,EAAO,YAC3BoB,EAAc,CAAA,EACdC,EAAU,CAAA,EACVD,EAAY,KAAKlF,CAAa,EAC9BmF,EAAQ,KAAKb,CAAK,CACrB,KACI,CACD,GAAIR,EAAO,WAAW,SAAW,IAE7Bc,EAAW,WAAWd,EAAO,WAAW,CAAC,EAAE,aAAcA,EAAO,WAAW,CAAC,EAAE,aAAcA,EAAO,WAAW,CAAC,EAAE,OAASA,EAAO,YAAakB,CAAa,EACvJlB,EAAO,sBAAwB,GAC/B,OAAOkB,EAGfE,EAAc,CAAA,EACdC,EAAU,CAAA,EACVpB,EAAaD,EAAO,WACvB,CACD,QAASU,EAAY,EAAGA,EAAYV,EAAO,WAAW,OAAQU,IAAa,CACvE,MAAMvE,EAAmB6D,EAAO,WAAWU,CAAS,EACpDU,EAAY,KAAKjF,EAAiB,YAAY,EAC9CkF,EAAQ,KAAKlF,EAAiB,OAAS8D,CAAU,CACpD,CAED,IAAIqB,EAAmB,EACvB,QAAS9F,EAAQ,EAAGA,EAAQ4F,EAAY,QAAS,CAC7C,GAAI,CAAC5F,EAAO,CACRsF,EAAW,WAAWM,EAAY5F,CAAK,EAAG4F,EAAY5F,EAAQ,CAAC,EAAG6F,EAAQ7F,EAAQ,CAAC,GAAK6F,EAAQ7F,CAAK,EAAI6F,EAAQ7F,EAAQ,CAAC,GAAI0F,CAAa,EAC3IC,EAAuBD,EACvBI,EAAmBD,EAAQ7F,CAAK,EAAI6F,EAAQ7F,EAAQ,CAAC,EACrDA,GAAS,EACT,QACH,CACD8F,GAAoBD,EAAQ7F,CAAK,EACjCsF,EAAW,WAAWK,EAAsBC,EAAY5F,CAAK,EAAG6F,EAAQ7F,CAAK,EAAI8F,EAAkBH,CAAoB,EACvH3F,GACH,CACJ,CAED,QAASkF,EAAY,EAAGA,EAAYV,EAAO,mBAAmB,OAAQU,IAAa,CAC/E,MAAMvE,EAAmB6D,EAAO,mBAAmBU,CAAS,EACxDvE,EAAiB,SAAW,IAGhCgF,EAAqB,cAAchF,EAAiB,aAAcgE,EAAW,WAAW,CAAC,CAAC,EAC1FW,EAAW,WAAWK,EAAsBhB,EAAW,WAAW,CAAC,EAAGhE,EAAiB,OAAQgF,CAAoB,EACtH,CACD,OAAOA,CACX,CAEA,SAASI,EAA6BpG,EAAO,CACzC,GAAKA,EAAM,oCAAoC,OAG/C,SAASK,EAAQ,EAAGA,EAAQL,EAAM,oCAAoC,OAAQK,IAAS,CACnF,MAAMP,EAASE,EAAM,oCAAoC,KAAKK,CAAK,EACnE,UAAWgG,KAAQvG,EAAO,sBAAuB,CAC7C,MAAM+E,EAAS/E,EAAO,sBAAsBuG,CAAI,EAC1CjB,EAAoBP,EAAO,WAAW,CAAC,EACvC9D,EAAgB8D,EAAO,cAC7B,GAAmC9D,GAAkB,KACjD,SAEJ,MAAMuF,EAAsBpG,EAAU,sCAAwCa,EAAc,EAC5F,IAAIwF,EAAazG,EAAOuG,CAAI,EAC5B,GAAIC,EACAC,EAAa3B,EAAwCC,CAAM,UAGpC9D,EAAc,IAAM,OAEvCwF,EAAaT,EAA2CjB,EAAQ0B,GAAcZ,EAAW,SAAQ,CAAE,MAElG,CACD,IAAIR,EAAa,EACbL,EAAa,EACjB,MAAM0B,EAA6CpB,GAAqBA,EAAkB,gBAAgB,WAAalF,EAAU,wCACjI,GAAI2E,EAAO,YAAc,EAEjB2B,EACAD,EAAaxF,EAAc,MAAQA,EAAc,MAAK,EAAKA,EAEtDqE,GAAqBrE,EAAc,MACxCwF,EAAaxF,EAAc,MAAM,EAAM8D,EAAO,WAAW,EAEpDO,EACLmB,EAAaxF,GAAiB,EAAM8D,EAAO,aAEtC9D,EAAc,MACnBwF,EAAaxF,EAAc,QAG3BwF,EAAaxF,UAGZqE,EAAmB,CAExBN,EAAaD,EAAO,YACpB,MAAMQ,EAAQD,EAAkB,OAASN,EACrCO,IAAU,EACND,EAAkB,aAAa,MAC/BmB,EAAanB,EAAkB,aAAa,MAAMC,CAAK,EAGvDkB,EAAanB,EAAkB,aAAeC,EAIlDkB,EAAanB,EAAkB,aAE/BoB,IACID,EAAW,SACXA,EAAW,SAASxF,EAAewF,CAAU,EAG7CA,GAAcxF,GAGtBoE,EAAa,CAChB,CAED,QAASI,EAAYJ,EAAYI,EAAYV,EAAO,WAAW,OAAQU,IAAa,CAChF,MAAMvE,EAAmB6D,EAAO,WAAWU,CAAS,EAC9CF,EAAQrE,EAAiB,OAAS8D,EACxC,GAAKO,EAGIrE,EAAiB,aAAa,iBACnCA,EAAiB,aAAa,iBAAiBqE,EAAOkB,CAAU,EAGhEA,GAAcvF,EAAiB,aAAeqE,MAN9C,SAQP,CAED,QAASE,EAAY,EAAGA,EAAYV,EAAO,mBAAmB,OAAQU,IAAa,CAC/E,MAAMvE,EAAmB6D,EAAO,mBAAmBU,CAAS,EACtDF,EAAQrE,EAAiB,OAC/B,GAAKqE,EAGIrE,EAAiB,aAAa,iBACnCA,EAAiB,aAAa,iBAAiBqE,EAAOkB,CAAU,EAGhEA,GAAcvF,EAAiB,aAAeqE,MAN9C,SAQP,CACJ,CAELvF,EAAOuG,CAAI,EAAIE,CAClB,CACDzG,EAAO,sBAAwB,EAClC,CACDE,EAAM,oCAAoC,QAC9C,CA+BO,SAASyG,EAAuBC,EAAYC,EAAW,CACtDA,IACAA,EAAU,UAAU,mBAAqB,SAAUC,EAAQC,EAAWC,EAAaC,EAAoB,GAAOC,EAAsB,KAAM,CAElI,KAAK,WAAW,SAAW,IAC3B,KAAK,WAAW,KAAK,IAAI9G,EAAU,KAAK,KAAM,UAAW0G,EAAO,WAAW,CAAC,EAAE,eAAgB1G,EAAU,qBAAsB,CAAC,CAAC,EAChI,KAAK,WAAW,CAAC,EAAE,QAAQ,CAAE,CAAA,GAGjC,MAAM+G,EAAcL,EAAO,WAAW,CAAC,EAAE,SAASC,CAAS,EAC3D,GAAI,CAACI,EACD,MAAO,GAEX,MAAMzF,EAAOyF,EAAY,KACnBxF,EAAKwF,EAAY,GACjBC,EAAaN,EAAO,WAAW,CAAC,EAAE,QAAO,EAEzCO,EAAmBP,EAAO,OAC1BQ,EAAeR,EAAO,YACtBS,EAAS,KAAK,YACdC,EAAoBP,GAAqBK,GAAgBD,GAAoB,KAAK,QAAUA,IAAqB,KAAK,OACtHI,EAAcD,GAAqBD,GAAUD,EAAeC,EAAO,OAASD,EAAa,OAAS,EAClGI,EAAwBT,GAAqB,CAACM,GAAUL,IAAwBA,EAAoB,IAAM,GAAKA,EAAoB,IAAM,GAAKA,EAAoB,IAAM,GACxKS,EAAW,KAAK,WAAW,CAAC,EAAE,QAAO,EAE3C,IAAIC,EACAC,EACAC,EACJ,QAASC,EAAM,EAAGC,EAAQZ,EAAW,OAAQW,EAAMC,EAAOD,IACtDH,EAAOR,EAAWW,CAAG,EACjBH,EAAK,OAASlG,GAAQkG,EAAK,OAASjG,IAChCsF,GACAa,EAAMF,EAAK,MAAM,QAEbJ,GACAK,EAAkBC,EAAI,iBACtBA,EAAI,eAAeD,EAAgB,aAAaJ,CAAW,CAAC,GAGvDC,GAAyBR,GAC9BW,EAAkBC,EAAI,iBACtBA,EAAI,eAAeD,EAAgB,gBAAgBX,CAAmB,CAAC,GAIvEY,EAAMF,EAAK,OAIfE,EAAMF,EAAK,MAEfD,EAAS,KAAK,CAAE,MAAOC,EAAK,MAAQZ,EAAa,MAAOc,CAAG,CAAE,GAGrE,YAAK,WAAW,CAAC,EAAE,YAAYf,EAAWrF,EAAOsF,EAAarF,EAAKqF,CAAW,EACvE,EACnB,GAESJ,IAGLA,EAAW,UAAU,SAAW,SAAUqB,EAAiB,CACvD,GAAI,CAAC,KAAK,kBACN,OAGJ,MAAMC,EAAMC,EAAc,IAC1B,GAAI,CAAC,KAAK,mBAAoB,CAC1B,GAAI,KAAK,aAAa,OAAS,EAC3B,OAEJ,KAAK,mBAAqBD,CAC7B,CACD,KAAK,UAAYD,IAAoB,OAAYA,EAAkB,KAAK,8BAAgC,IAAQC,EAAM,KAAK,oBAAsB,KAAK,mBACtJ,KAAK,mBAAqBA,EAC1B,MAAME,EAAc,KAAK,mBACzB,GAAIA,EAAY,SAAW,EACvB,OAEJ,KAAK,gBAAkB,KAAK,UAC5B,MAAMC,EAAgB,KAAK,eAC3B,QAAS9H,EAAQ,EAAGA,EAAQ6H,EAAY,OAAQ7H,IAAS,CACrD,MAAM+H,EAAaF,EAAY7H,CAAK,EAChC,CAAC+H,EAAW,SAASD,CAAa,GAAKC,EAAW,cAClD/H,GAEP,CAED+F,EAA6B,IAAI,CACzC,EACIM,EAAW,UAAU,sBAAwB,UAAY,CACrD,KAAK,mBAAmB,KAAK,CAAC2B,EAAGC,IACtBD,EAAE,UAAYC,EAAE,SAC1B,CACT,EACI5B,EAAW,UAAU,uBAAyB,SAAU5G,EAAQ0B,EAAMC,EAAIX,EAAS,EAAKY,EAAMC,EAAa,EAAK6B,EAAgB4E,EAAY/D,EAAYX,EAAiBC,EAAa,GAAO,CACzL,MAAM4E,EAAqB,KAAK,eAAezI,EAAQ0B,EAAMC,EAAIC,EAAMC,EAAY6B,EAAgB4E,EAAY,GAAO/D,EAAYX,EAAiBC,CAAU,EAC7J,OAAA4E,EAAmB,OAASzH,EACrByH,CACf,EACI7B,EAAW,UAAU,eAAiB,SAAU5G,EAAQ0B,EAAMC,EAAIC,EAAMC,EAAa,EAAK6B,EAAgB4E,EAAYI,EAAc,GAAMnE,EAAYX,EAAiBC,EAAa,GAAO,CAEvL,GAAIhC,EAAa,EAAG,CAChB,MAAM8G,EAAMjH,EACZA,EAAOC,EACPA,EAAKgH,EACL9G,EAAa,CAACA,CACjB,CAEGH,EAAOC,IACPE,EAAa,CAACA,GAEd6G,GACA,KAAK,cAAc1I,EAAQ,OAAWuE,CAAU,EAE/C+D,IACDA,EAAa,IAAIjF,EAAW,KAAMrD,EAAQ0B,EAAMC,EAAIC,EAAMC,EAAY6B,EAAgB,OAAWE,EAAiBC,CAAU,GAEhI,MAAM+E,EAA4BrE,EAAaA,EAAWvE,CAAM,EAAI,GAMpE,GAJIA,EAAO,YAAc4I,GACrBN,EAAW,iBAAiBtI,EAAQA,EAAO,UAAU,EAGrDA,EAAO,eAAgB,CACvB,MAAMoI,EAAcpI,EAAO,iBAC3B,QAASO,EAAQ,EAAGA,EAAQ6H,EAAY,OAAQ7H,IAC5C,KAAK,eAAe6H,EAAY7H,CAAK,EAAGmB,EAAMC,EAAIC,EAAMC,EAAY6B,EAAgB4E,EAAYI,EAAanE,EAAYX,CAAe,CAE/I,CACD,OAAA0E,EAAW,MAAK,EACTA,CACf,EACI1B,EAAW,UAAU,wBAA0B,SAAU5G,EAAQ6I,EAAuBnH,EAAMC,EAAIC,EAAMC,EAAa,EAAK6B,EAAgB4E,EAAYI,EAAc,GAAMnE,EAAYX,EAAiBC,EAAa,GAAO,CACvN,MAAMiF,EAAW9I,EAAO,eAAe6I,CAAqB,EACtDE,EAAS,CAAA,EACfA,EAAO,KAAK,KAAK,eAAe/I,EAAQ0B,EAAMC,EAAIC,EAAMC,EAAY6B,EAAgB4E,EAAYI,EAAanE,EAAY,OAAWV,CAAU,CAAC,EAC/I,UAAWmF,KAASF,EAChBC,EAAO,KAAK,KAAK,eAAeC,EAAOtH,EAAMC,EAAIC,EAAMC,EAAY6B,EAAgB4E,EAAYI,EAAanE,EAAY,OAAWV,CAAU,CAAC,EAElJ,OAAOkF,CACf,EACInC,EAAW,UAAU,qBAAuB,SAAU5G,EAAQ2D,EAAYjC,EAAMC,EAAIC,EAAMC,EAAa,EAAK6B,EAAgBE,EAAiBC,EAAa,GAAO,CAE7J,GAAIhC,EAAa,EAAG,CAChB,MAAM8G,EAAMjH,EACZA,EAAOC,EACPA,EAAKgH,EACL9G,EAAa,CAACA,CACjB,CAED,OAAIH,EAAOC,IACPE,EAAa,CAACA,GAEC,IAAIwB,EAAW,KAAMrD,EAAQ0B,EAAMC,EAAIC,EAAMC,EAAY6B,EAAgBC,EAAYC,EAAiBC,CAAU,CAE3I,EACI+C,EAAW,UAAU,8BAAgC,SAAU5G,EAAQ6I,EAAuBlF,EAAYjC,EAAMC,EAAIC,EAAMC,EAAY6B,EAAgBE,EAAiBC,EAAa,GAAO,CACvL,MAAMiF,EAAW9I,EAAO,eAAe6I,CAAqB,EACtDE,EAAS,CAAA,EACfA,EAAO,KAAK,KAAK,qBAAqB/I,EAAQ2D,EAAYjC,EAAMC,EAAIC,EAAMC,EAAY6B,EAAgBE,EAAiBC,CAAU,CAAC,EAClI,UAAWmF,KAASF,EAChBC,EAAO,KAAK,KAAK,qBAAqBC,EAAOrF,EAAYjC,EAAMC,EAAIC,EAAMC,EAAY6B,EAAgBE,EAAiBC,CAAU,CAAC,EAErI,OAAOkF,CACf,EACInC,EAAW,UAAU,sBAAwB,SAAU5G,EAAQ,CAC3D,QAASO,EAAQ,EAAGA,EAAQ,KAAK,mBAAmB,OAAQA,IACxD,GAAI,KAAK,mBAAmBA,CAAK,EAAE,SAAWP,EAC1C,OAAO,KAAK,mBAAmBO,CAAK,EAG5C,OAAO,IACf,EACIqG,EAAW,UAAU,0BAA4B,SAAU5G,EAAQ,CAC/D,MAAM+I,EAAS,CAAA,EACf,QAASxI,EAAQ,EAAGA,EAAQ,KAAK,mBAAmB,OAAQA,IACpD,KAAK,mBAAmBA,CAAK,EAAE,SAAWP,GAC1C+I,EAAO,KAAK,KAAK,mBAAmBxI,CAAK,CAAC,EAGlD,OAAOwI,CACf,EACInC,EAAW,UAAU,cAAgB,SAAU5G,EAAQsE,EAAeC,EAAY,CAC9E,MAAM6D,EAAc,KAAK,0BAA0BpI,CAAM,EACzD,UAAWsI,KAAcF,EACrBE,EAAW,KAAKhE,EAAeC,CAAU,CAErD,EACIqC,EAAW,UAAU,kBAAoB,UAAY,CACjD,GAAI,KAAK,mBAAoB,CACzB,QAASqC,EAAI,EAAGA,EAAI,KAAK,mBAAmB,OAAQA,IAChD,KAAK,mBAAmBA,CAAC,EAAE,KAAK,OAAW,OAAW,EAAI,EAE9D,KAAK,mBAAmB,OAAS,CACpC,CACD,UAAWC,KAAS,KAAK,gBACrBA,EAAM,KAAI,CAEtB,EACA,CCz3BAvC,EAAuBwC,EAAOC,CAAI,ECG3B,MAAMC,CAAkB,CAK3B,cAAe,CACX,MAAO,mBACV,CAKD,WAAY,CACR,MAAMC,EAAsB,CAAA,EAC5B,OAAAA,EAAoB,UAAY,KAAK,UAAU,UAAS,EACxDA,EAAoB,SAAW,KAAK,OAAO,GACpCA,CACV,CACL,CAIO,MAAMC,CAAe,CAIxB,IAAI,MAAO,CACP,OAAO,KAAK,KACf,CACD,IAAI,KAAKjG,EAAO,CACR,KAAK,QAAUA,IAGnB,KAAK,MAAQA,EACb,KAAK,aAAa,EAAI,EACzB,CAMD,aAAakG,EAAc,GAAO,CAC9B,GAAI,CAAC,KAAK,MAAQ,CAACA,EAAa,CAC5B,KAAK,sBAAwB,KAAK,oBAAoB,OACtD,MACH,CACD,KAAK,sBAAwB,EAC7B,QAASP,EAAI,EAAGA,EAAI,KAAK,aAAa,OAAQ,EAAEA,EAAG,CAC/C,MAAMX,EAAa,KAAK,aAAaW,CAAC,EAClC,CAAC,KAAK,MAAQ,KAAK,KAAK,UAAY,KAAK,KAAK,cAAcX,EAAW,OAAO,IAAI,GAClF,KAAK,wBACDA,EAAW,QACXA,EAAW,QAAO,GAIjBA,EAAW,QACZA,EAAW,MAAK,CAG3B,CACJ,CAKD,0BAA2B,CACvB,GAAI,GAAC,KAAK,MAAQ,KAAK,KAAK,UAI5B,SAASW,EAAI,EAAGA,EAAI,KAAK,aAAa,OAAQ,EAAEA,EAAG,CAC/C,MAAMX,EAAa,KAAK,aAAaW,CAAC,EACjC,KAAK,KAAK,cAAcX,EAAW,OAAO,IAAI,IAC/CA,EAAW,KAAI,EACf,KAAK,aAAa,OAAOW,EAAG,CAAC,EAC7B,EAAEA,EAET,CAED,QAAS1I,EAAQ,EAAGA,EAAQ,KAAK,oBAAoB,OAAQA,IAAS,CAClE,MAAMkJ,EAAoB,KAAK,oBAAoBlJ,CAAK,EACnD,KAAK,KAAK,cAAckJ,EAAkB,OAAO,IAAI,IACtD,KAAK,oBAAoB,OAAOlJ,EAAO,CAAC,EACxC,EAAEA,EAET,EACJ,CAID,IAAI,MAAO,CACP,OAAO,KAAK,KACf,CACD,IAAI,KAAK+C,EAAO,CACZ,GAAI,KAAK,QAAUA,EAGnB,MAAK,MAAQA,EACb,QAAS/C,EAAQ,EAAGA,EAAQ,KAAK,aAAa,OAAQA,IAAS,CAC3D,MAAM+H,EAAa,KAAK,aAAa/H,CAAK,EAC1C+H,EAAW,UAAY,KAAK,KAC/B,EACJ,CAID,IAAI,IAAK,CACL,OAAO,KAAK,GACf,CACD,IAAI,GAAGhF,EAAO,CACV,GAAI,KAAK,MAAQA,EAGjB,MAAK,IAAMA,EACX,QAAS/C,EAAQ,EAAGA,EAAQ,KAAK,aAAa,OAAQA,IAAS,CAC3D,MAAM+H,EAAa,KAAK,aAAa/H,CAAK,EAC1C+H,EAAW,QAAU,KAAK,GAC7B,EACJ,CAID,IAAI,WAAY,CACZ,OAAO,KAAK,UACf,CAID,IAAI,WAAY,CACZ,OAAO,KAAK,YAAc,CAAC,KAAK,SACnC,CAID,IAAI,YAAa,CACb,OAAO,KAAK,WACf,CAID,IAAI,WAAWhF,EAAO,CAClB,GAAI,KAAK,cAAgBA,EAGzB,MAAK,YAAcA,EACnB,QAAS/C,EAAQ,EAAGA,EAAQ,KAAK,aAAa,OAAQA,IAAS,CAC3D,MAAM+H,EAAa,KAAK,aAAa/H,CAAK,EAC1C+H,EAAW,WAAa,KAAK,WAChC,EACJ,CAID,IAAI,eAAgB,CAChB,OAAO,KAAK,cACf,CACD,IAAI,cAAchF,EAAO,CACrB,GAAI,KAAK,iBAAmBA,EAG5B,MAAK,eAAiBA,EACtB,QAAS/C,EAAQ,EAAGA,EAAQ,KAAK,aAAa,OAAQA,IAAS,CAC3D,MAAM+H,EAAa,KAAK,aAAa/H,CAAK,EAC1C+H,EAAW,cAAgB,KAAK,cACnC,EACJ,CAID,IAAI,YAAa,CACb,OAAO,KAAK,WACf,CACD,IAAI,WAAWhF,EAAO,CAClB,GAAI,KAAK,cAAgBA,EAGzB,MAAK,YAAcA,EACnB,QAAS/C,EAAQ,EAAGA,EAAQ,KAAK,aAAa,OAAQA,IAAS,CAC3D,MAAM+H,EAAa,KAAK,aAAa/H,CAAK,EAC1C+H,EAAW,WAAa,KAAK,WAChC,EACJ,CAID,IAAI,QAAS,CACT,OAAO,KAAK,OACf,CACD,IAAI,OAAOhF,EAAO,CACV,KAAK,UAAYA,IAGrB,KAAK,QAAUA,EACf,KAAK,2BAA2B,KAAK,OAAO,EAC/C,CAID,IAAI,oBAAqB,CACrB,OAAO,KAAK,mBACf,CAID,IAAI,aAAc,CACd,OAAO,KAAK,YACf,CAID,IAAI,UAAW,CACX,OAAO,KAAK,mBACf,CAID,IAAI,WAAY,CACZ,OAAO,KAAK,UACf,CACD,IAAI,UAAUA,EAAO,CACjB,GAAI,KAAK,aAAeA,IAGxB,KAAK,WAAaA,EACd,KAAK,aAAa,OAAS,GAAG,CAC9B,QAAS2F,EAAI,EAAGA,EAAI,KAAK,aAAa,OAAQA,IAC1C,KAAK,aAAaA,CAAC,EAAE,UAAY,KAAK,WAE1C,KAAK,OAAO,uBACf,CACJ,CAKD,IAAI,gBAAiB,CACjB,OAAO,KAAK,eACf,CACD,IAAI,eAAe3F,EAAO,CACtB,GAAI,KAAK,kBAAoBA,IAG7B,KAAK,gBAAkBA,EACnBA,IAAU,MACV,QAAS2F,EAAI,EAAGA,EAAI,KAAK,oBAAoB,OAAQ,EAAEA,EACnD,KAAK,oBAAoBA,CAAC,EAAE,UAAU,eAAiB3F,CAGlE,CAKD,IAAI,eAAgB,CAChB,OAAO,KAAK,cACf,CACD,IAAI,cAAcA,EAAO,CACrB,GAAI,KAAK,iBAAmBA,IAG5B,KAAK,eAAiBA,EAClBA,IAAU,MACV,QAAS2F,EAAI,EAAGA,EAAI,KAAK,oBAAoB,OAAQ,EAAEA,EACnD,KAAK,oBAAoBA,CAAC,EAAE,UAAU,cAAgB3F,CAGjE,CASD,UAAU5B,EAAMC,EAAI,CAChBD,EAAOA,GAAQ,KAAK,MACpBC,EAAKA,GAAM,KAAK,IAChB,MAAMyC,EAAM,KAAK,mBAAmB,CAAC,EAAE,UAAU,eAAiB,KAAK,YACvE,OAAQzC,EAAKD,GAAQ0C,CACxB,CASD,OAAO,qBAAqBsF,EAAiBC,EAAgB,GAAMC,EAAY,GAAO5I,EAAQ,CAC1F,GAAI0I,EAAgB,SAAW,EAC3B,OAAO,KAEX1I,EAASA,GAAU0I,EAAgB,CAAC,EAAE,OACtC,IAAIG,EAAa,OAAO,UACpBC,EAAW,CAAC,OAAO,UACvB,GAAIF,EACA,UAAWG,KAAkBL,EACrBK,EAAe,KAAOF,IACtBA,EAAaE,EAAe,MAE5BA,EAAe,GAAKD,IACpBA,EAAWC,EAAe,IAItC,MAAMC,EAAuB,IAAIT,EAAeG,EAAgB,CAAC,EAAE,KAAO,UAAWA,EAAgB,CAAC,EAAE,OAAQ1I,CAAM,EACtH,UAAW+I,KAAkBL,EAAiB,CACtCE,GACAG,EAAe,UAAUF,EAAYC,CAAQ,EAEjD,UAAWL,KAAqBM,EAAe,mBAC3CC,EAAqB,qBAAqBP,EAAkB,UAAWA,EAAkB,MAAM,EAE/FE,GACAI,EAAe,QAAO,CAE7B,CACD,OAAOC,CACV,CAUD,YAEAnJ,EAAMX,EAAQ,KAAMc,EAAS,GAAI8C,EAAY,EAAG,CAC5C,KAAK,KAAOjD,EACZ,KAAK,oBAAsB,IAAI,MAC/B,KAAK,aAAe,IAAI,MACxB,KAAK,MAAQ,OAAO,UACpB,KAAK,IAAM,CAAC,OAAO,UACnB,KAAK,YAAc,EACnB,KAAK,eAAiB,GACtB,KAAK,YAAc,GACnB,KAAK,QAAU,GACf,KAAK,WAAa,EAClB,KAAK,gBAAkB,KACvB,KAAK,eAAiB,KACtB,KAAK,sBAAwB,EAC7B,KAAK,aAAe,GAEpB,KAAK,iBAAmB,KAIxB,KAAK,yBAA2B,IAAIkD,EAIpC,KAAK,0BAA4B,IAAIA,EAIrC,KAAK,+BAAiC,IAAIA,EAI1C,KAAK,8BAAgC,IAAIA,EAIzC,KAAK,gCAAkC,IAAIA,EAI3C,KAAK,+BAAiC,IAAIA,EAI1C,KAAK,SAAW,KAChB,KAAK,MAAQ,KACb,KAAK,oBAAsB,GAC3B,KAAK,OAAS7D,GAAS+J,EAAY,iBACnC,KAAK,QAAUjJ,EACf,KAAK,WAAa8C,EAClB,KAAK,SAAW,KAAK,OAAO,YAAW,EACvC,KAAK,OAAO,kBAAkB,IAAI,CACrC,CAOD,qBAAqB7D,EAAWD,EAAQ,CACpC,MAAMyJ,EAAoB,IAAIJ,EAC9BI,EAAkB,UAAYxJ,EAC9BwJ,EAAkB,OAASzJ,EAC3B,MAAMsB,EAAOrB,EAAU,UACvB,OAAI,KAAK,MAAQqB,EAAK,CAAC,EAAE,QACrB,KAAK,MAAQA,EAAK,CAAC,EAAE,OAErB,KAAK,IAAMA,EAAKA,EAAK,OAAS,CAAC,EAAE,QACjC,KAAK,IAAMA,EAAKA,EAAK,OAAS,CAAC,EAAE,OAEjC,KAAK,kBAAoB,OACzBrB,EAAU,eAAiB,KAAK,iBAEhC,KAAK,iBAAmB,OACxBA,EAAU,cAAgB,KAAK,gBAEnC,KAAK,oBAAoB,KAAKwJ,CAAiB,EAC/C,KAAK,aAAe,GACbA,CACV,CAKD,wBAAwBxJ,EAAW,CAC/B,QAASM,EAAQ,KAAK,oBAAoB,OAAS,EAAGA,EAAQ,GAAIA,IACpC,KAAK,oBAAoBA,CAAK,EAClC,YAAcN,GAChC,KAAK,oBAAoB,OAAOM,EAAO,CAAC,CAGnD,CAQD,UAAUsJ,EAAa,KAAMC,EAAW,KAAM,CACtCD,GAAc,OACdA,EAAa,KAAK,OAElBC,GAAY,OACZA,EAAW,KAAK,KAEpB,QAASvJ,EAAQ,EAAGA,EAAQ,KAAK,oBAAoB,OAAQA,IAAS,CAElE,MAAMe,EADoB,KAAK,oBAAoBf,CAAK,EACzB,UAAU,QAAO,EAC1C2J,EAAW5I,EAAK,CAAC,EACjB6I,EAAS7I,EAAKA,EAAK,OAAS,CAAC,EACnC,GAAI4I,EAAS,MAAQL,EAAY,CAC7B,MAAMvJ,EAAS,CACX,MAAOuJ,EACP,MAAOK,EAAS,MAChB,UAAWA,EAAS,UACpB,WAAYA,EAAS,WACrB,cAAeA,EAAS,aAC5C,EACgB5I,EAAK,OAAO,EAAG,EAAGhB,CAAM,CAC3B,CACD,GAAI6J,EAAO,MAAQL,EAAU,CACzB,MAAMxJ,EAAS,CACX,MAAOwJ,EACP,MAAOK,EAAO,MACd,UAAWA,EAAO,UAClB,WAAYA,EAAO,WACnB,cAAeA,EAAO,aAC1C,EACgB7I,EAAK,KAAKhB,CAAM,CACnB,CACJ,CACD,YAAK,MAAQuJ,EACb,KAAK,IAAMC,EACJ,IACV,CACD,aAAaxB,EAAYmB,EAAmBlJ,EAAO,CAC/C+H,EAAW,gBAAkB,IAAM,CAC/B,KAAK,0BAA0B,gBAAgBmB,CAAiB,EAC5D,MAAK,oBAAoBlJ,CAAK,IAGlC,KAAK,oBAAoBA,CAAK,EAAI,GAClC,KAAK,sBACD,KAAK,sBAAwB,KAAK,wBAClC,KAAK,+BAA+B,gBAAgB,IAAI,EACxD,KAAK,oBAAsB,EAC3B,KAAK,oBAAoB,OAAS,GAElD,CACK,CAUD,MAAMqB,EAAO,GAAOC,EAAa,EAAGH,EAAMC,EAAIkC,EAAY,CACtD,GAAI,KAAK,YAAc,KAAK,oBAAoB,SAAW,EACvD,OAAO,KAEX,KAAK,eAAiBjC,EACtB,KAAK,aAAe,GACpB,KAAK,oBAAsB,EAC3B,KAAK,oBAAoB,OAAS,EAClC,QAASrB,EAAQ,EAAGA,EAAQ,KAAK,oBAAoB,OAAQA,IAAS,CAClE,MAAMkJ,EAAoB,KAAK,oBAAoBlJ,CAAK,EAClD+H,EAAa,KAAK,OAAO,qBAAqBmB,EAAkB,OAAQ,CAACA,EAAkB,SAAS,EAAG/H,IAAS,OAAYA,EAAO,KAAK,MAAOC,IAAO,OAAYA,EAAK,KAAK,IAAKC,EAAMC,EAAY,OAAW,OAAWgC,IAAe,OAAYA,EAAa,KAAK,WAAW,EACvRyE,EAAW,OAAS,KAAK,QACzBA,EAAW,UAAY,KAAK,WAC5BA,EAAW,eAAiB,IAAM,CAC9B,KAAK,yBAAyB,gBAAgBmB,CAAiB,EAC/D,KAAK,0BAA0BnB,CAAU,CACzD,EACY,KAAK,aAAaA,EAAYmB,EAAmBlJ,CAAK,EACtD,KAAK,aAAa,KAAK+H,CAAU,CACpC,CACD,YAAK,aAAY,EACjB,KAAK,OAAO,wBACZ,KAAK,YAAczG,EACnB,KAAK,WAAa,GAClB,KAAK,UAAY,GACjB,KAAK,+BAA+B,gBAAgB,IAAI,EACjD,IACV,CAKD,OAAQ,CACJ,GAAI,CAAC,KAAK,WACN,OAAO,KAEX,KAAK,UAAY,GACjB,QAAStB,EAAQ,EAAGA,EAAQ,KAAK,aAAa,OAAQA,IAC/B,KAAK,aAAaA,CAAK,EAC/B,MAAK,EAEpB,YAAK,gCAAgC,gBAAgB,IAAI,EAClD,IACV,CAOD,KAAKqB,EAAM,CAEP,OAAI,KAAK,WAAa,KAAK,aAAa,QAAU,CAAC,KAAK,cAChDA,IAAS,SACT,KAAK,cAAgBA,GAEzB,KAAK,QAAO,IAGZ,KAAK,KAAI,EACT,KAAK,MAAMA,EAAM,KAAK,WAAW,GAE9B,IACV,CAKD,OAAQ,CACJ,GAAI,CAAC,KAAK,WACN,YAAK,KAAI,EACT,KAAK,UAAU,CAAC,EAChB,KAAK,KAAK,EAAI,EACP,KAEX,QAASrB,EAAQ,EAAGA,EAAQ,KAAK,aAAa,OAAQA,IAC/B,KAAK,aAAaA,CAAK,EAC/B,MAAK,EAEpB,OAAO,IACV,CAKD,SAAU,CACN,GAAI,CAAC,KAAK,WACN,OAAO,KAEX,QAASA,EAAQ,EAAGA,EAAQ,KAAK,aAAa,OAAQA,IAC/B,KAAK,aAAaA,CAAK,EAC/B,QAAO,EAEtB,YAAK,aAAY,EACjB,KAAK,UAAY,GACjB,KAAK,+BAA+B,gBAAgB,IAAI,EACjD,IACV,CAMD,KAAKkE,EAAqB,GAAO,CAC7B,GAAI,CAAC,KAAK,WACN,OAAO,KAEX,MAAM2F,EAAO,KAAK,aAAa,MAAK,EACpC,QAAS7J,EAAQ,EAAGA,EAAQ6J,EAAK,OAAQ7J,IACrC6J,EAAK7J,CAAK,EAAE,KAAK,OAAW,OAAW,GAAMkE,CAAkB,EAGnE,IAAI4F,EAAW,EACf,QAAS9J,EAAQ,EAAGA,EAAQ,KAAK,OAAO,mBAAmB,OAAQA,IAAS,CACxE,MAAM+H,EAAa,KAAK,OAAO,mBAAmB/H,CAAK,EACnD+H,EAAW,mBAAmB,OAAS,EACvC,KAAK,OAAO,mBAAmB+B,GAAU,EAAI/B,EAExC7D,GAIL,KAAK,0BAA0B6D,EAAY7D,CAAkB,CAEpE,CACD,YAAK,OAAO,mBAAmB,OAAS4F,EACxC,KAAK,WAAa,GACX,IACV,CAWD,2BAA2BrJ,EAAQ,CAC/B,QAAST,EAAQ,EAAGA,EAAQ,KAAK,aAAa,OAAQA,IAAS,CAC3D,MAAM+H,EAAa,KAAK,aAAa/H,CAAK,EAC1C+H,EAAW,OAAStH,CACvB,CACD,OAAO,IACV,CAOD,sBAAsBgD,EAAM,CACxB,QAASzD,EAAQ,EAAGA,EAAQ,KAAK,aAAa,OAAQA,IAC/B,KAAK,aAAaA,CAAK,EAC/B,SAASyD,CAAI,EAE5B,OAAO,IACV,CAOD,UAAU3C,EAAO8C,EAAY,GAAO,CAChC,GAAI,CAAC,KAAK,WACN,OAAO,KAEX,QAAS5D,EAAQ,EAAGA,EAAQ,KAAK,aAAa,OAAQA,IAC/B,KAAK,aAAaA,CAAK,EAC/B,UAAUc,EAAO8C,CAAS,EAEzC,OAAO,IACV,CAKD,iBAAkB,OACd,QAAOmG,EAAA,KAAK,YAAY,CAAC,IAAlB,YAAAA,EAAqB,cAAe,CAC9C,CAID,SAAU,CACF,KAAK,WACL,KAAK,KAAI,EAEb,KAAK,oBAAoB,OAAS,EAClC,KAAK,aAAa,OAAS,EAE3B,MAAM/J,EAAQ,KAAK,OAAO,gBAAgB,QAAQ,IAAI,EAItD,GAHIA,EAAQ,IACR,KAAK,OAAO,gBAAgB,OAAOA,EAAO,CAAC,EAE3C,KAAK,iBAAkB,CACvB,MAAMA,EAAQ,KAAK,iBAAiB,gBAAgB,QAAQ,IAAI,EAC5DA,EAAQ,IACR,KAAK,iBAAiB,gBAAgB,OAAOA,EAAO,CAAC,EAEzD,KAAK,iBAAmB,IAC3B,CACD,KAAK,yBAAyB,QAC9B,KAAK,8BAA8B,QACnC,KAAK,gCAAgC,QACrC,KAAK,+BAA+B,QACpC,KAAK,0BAA0B,QAC/B,KAAK,+BAA+B,OACvC,CACD,0BAA0B+H,EAAY7D,EAAqB,GAAO,CAE9D,MAAMC,EAAM,KAAK,aAAa,QAAQ4D,CAAU,EAC5C5D,EAAM,IACN,KAAK,aAAa,OAAOA,EAAK,CAAC,EAG/B,KAAK,aAAa,SAAW,KAAK,oBAAoB,OAAS,KAAK,wBACpE,KAAK,WAAa,GACbD,GACD,KAAK,8BAA8B,gBAAgB,IAAI,EAE3D,KAAK,aAAa,OAAS,EAElC,CAQD,MAAM8F,EAASC,EAAiBC,EAAkB,GAAO,CACrD,MAAMC,EAAW,IAAInB,EAAegB,GAAW,KAAK,KAAM,KAAK,OAAQ,KAAK,QAAS,KAAK,UAAU,EACpGG,EAAS,MAAQ,KAAK,KACtBA,EAAS,IAAM,KAAK,GACpBA,EAAS,YAAc,KAAK,WAC5BA,EAAS,eAAiB,KAAK,cAC/BA,EAAS,YAAc,KAAK,WAC5BA,EAAS,gBAAkB,KAAK,eAChCA,EAAS,eAAiB,KAAK,cAC/BA,EAAS,SAAW,KAAK,SACzBA,EAAS,KAAO,KAAK,KACrB,UAAWC,KAAmB,KAAK,oBAC/BD,EAAS,qBAAqBD,EAAkBE,EAAgB,UAAU,MAAK,EAAKA,EAAgB,UAAWH,EAAkBA,EAAgBG,EAAgB,MAAM,EAAIA,EAAgB,MAAM,EAErM,OAAOD,CACV,CAKD,WAAY,CACR,MAAMpB,EAAsB,CAAA,EAC5BA,EAAoB,KAAO,KAAK,KAChCA,EAAoB,KAAO,KAAK,KAChCA,EAAoB,GAAK,KAAK,GAC9BA,EAAoB,WAAa,KAAK,WACtCA,EAAoB,cAAgB,KAAK,cACzCA,EAAoB,WAAa,KAAK,WACtCA,EAAoB,OAAS,KAAK,OAClCA,EAAoB,UAAY,KAAK,UACrCA,EAAoB,eAAiB,KAAK,eAC1CA,EAAoB,cAAgB,KAAK,cACzCA,EAAoB,mBAAqB,GACzC,QAASsB,EAAyB,EAAGA,EAAyB,KAAK,mBAAmB,OAAQA,IAA0B,CACpH,MAAMnB,EAAoB,KAAK,mBAAmBmB,CAAsB,EACxEtB,EAAoB,mBAAmBsB,CAAsB,EAAInB,EAAkB,UAAS,CAC/F,CACD,OAAIoB,GAAQA,EAAK,QAAQ,IAAI,IACzBvB,EAAoB,KAAOuB,EAAK,QAAQ,IAAI,GAG5C,KAAK,WACLvB,EAAoB,SAAW,KAAK,UAEjCA,CACV,CAQD,OAAO,MAAMwB,EAAsB5K,EAAO,CACtC,MAAM6J,EAAiB,IAAIR,EAAeuB,EAAqB,KAAM5K,EAAO4K,EAAqB,OAAQA,EAAqB,SAAS,EACvI,QAAS7B,EAAI,EAAGA,EAAI6B,EAAqB,mBAAmB,OAAQ7B,IAAK,CACrE,MAAMQ,EAAoBqB,EAAqB,mBAAmB7B,CAAC,EAC7DhJ,EAAYG,EAAU,MAAMqJ,EAAkB,SAAS,EACvDsB,EAAKtB,EAAkB,SAC7B,GAAIA,EAAkB,UAAU,WAAa,YAAa,CAEtD,MAAMuB,EAAc9K,EAAM,mBAAmB6K,CAAE,EAC3CC,GACAjB,EAAe,qBAAqB9J,EAAW+K,CAAW,CAEjE,KACI,CACD,MAAMC,EAAa/K,EAAM,YAAY6K,CAAE,EACnCE,GAAc,MACdlB,EAAe,qBAAqB9J,EAAWgL,CAAU,CAEhE,CACJ,CACD,OAAIJ,GACAA,EAAK,UAAUd,EAAgBe,EAAqB,IAAI,EAExDA,EAAqB,OAAS,MAAQA,EAAqB,KAAO,MAClEf,EAAe,UAAUe,EAAqB,KAAMA,EAAqB,EAAE,EAE3EA,EAAqB,aAAe,SACpCf,EAAe,YAAce,EAAqB,YAElDA,EAAqB,gBAAkB,SACvCf,EAAe,eAAiBe,EAAqB,eAErDA,EAAqB,aAAe,SACpCf,EAAe,YAAce,EAAqB,YAElDA,EAAqB,SAAW,SAChCf,EAAe,QAAUe,EAAqB,QAE9CA,EAAqB,YAAc,SACnCf,EAAe,WAAae,EAAqB,WAEjDA,EAAqB,iBAAmB,SACxCf,EAAe,gBAAkBe,EAAqB,gBAEtDA,EAAqB,gBAAkB,SACvCf,EAAe,eAAiBe,EAAqB,eAErDA,EAAqB,WAAa,SAClCf,EAAe,SAAWe,EAAqB,UAE5Cf,CACV,CAED,OAAO,sBAAsBmB,EAAsBC,EAAyBC,EAAOC,EAAgB,GAAOC,EAAY,CAClH,IAAIC,EACA,OAAOJ,GAA4B,SACnCI,EAAUJ,EAGVI,EAAU,CACN,eAAgBJ,EAChB,MAAOC,EACP,4BAA6BC,EAC7B,oBAAqBC,CACrC,EAEQ,IAAIvB,EAAiBmB,EACjBK,EAAQ,8BACRxB,EAAiBmB,EAAqB,MAAMK,EAAQ,0BAA4BxB,EAAe,IAAI,GAEvG,MAAMyB,EAAqBzB,EAAe,mBAC1C,QAASxJ,EAAQ,EAAGA,EAAQiL,EAAmB,OAAQjL,IAAS,CAC5D,MAAMkJ,EAAoB+B,EAAmBjL,CAAK,EAClDkJ,EAAkB,UAAYrJ,EAAU,sBAAsBqJ,EAAkB,UAAW8B,CAAO,CACrG,CAED,GADAxB,EAAe,WAAa,GACxBwB,EAAQ,SAAU,CAElB,IAAI7J,EAAO,OAAO,UACdC,EAAK,CAAC,OAAO,UACjB,MAAM6J,EAAqBzB,EAAe,mBAC1C,QAASxJ,EAAQ,EAAGA,EAAQiL,EAAmB,OAAQjL,IAAS,CAG5D,MAAMe,EAFoBkK,EAAmBjL,CAAK,EACd,UACb,UACnBmB,EAAOJ,EAAK,CAAC,EAAE,QACfI,EAAOJ,EAAK,CAAC,EAAE,OAEfK,EAAKL,EAAKA,EAAK,OAAS,CAAC,EAAE,QAC3BK,EAAKL,EAAKA,EAAK,OAAS,CAAC,EAAE,MAElC,CACDyI,EAAe,MAAQrI,EACvBqI,EAAe,IAAMpI,CACxB,CACD,OAAOoI,CACV,CAUD,OAAO,SAASmB,EAAsBO,EAASC,EAAO7K,EAAM8K,EAAqB,CAC7E,MAAM5B,EAAiBmB,EAAqB,MAAMrK,GAAQqK,EAAqB,IAAI,EACnF,OAAO3B,EAAe,gBAAgBQ,EAAgB0B,EAASC,EAAOC,CAAmB,CAC5F,CASD,OAAO,gBAAgB5B,EAAgB0B,EAASC,EAAOC,EAAqB,CACxE,OAAOpC,EAAe,YAAYQ,EAAgB0B,EAASC,EAAOC,EAAqB,EAAK,CAC/F,CAUD,OAAO,WAAWT,EAAsB3H,EAAWC,EAAS3C,EAAM8K,EAAqB,CACnF,MAAM5B,EAAiBmB,EAAqB,MAAMrK,GAAQqK,EAAqB,IAAI,EACnF,OAAO3B,EAAe,kBAAkBQ,EAAgBxG,EAAWC,EAASmI,CAAmB,CAClG,CASD,OAAO,kBAAkB5B,EAAgBxG,EAAWC,EAASmI,EAAqB,CAC9E,OAAOpC,EAAe,YAAYQ,EAAgBxG,EAAWC,EAASmI,EAAqB,EAAI,CAClG,CAUD,OAAO,YAAY5B,EAAgB6B,EAAOC,EAAKF,EAAqBG,EAAW,GAAO,CAClF,IAAIpK,EAAO,OAAO,UACdC,EAAK,CAAC,OAAO,UACjB,MAAM6J,EAAqBzB,EAAe,mBAC1C,QAASxJ,EAAQ,EAAGA,EAAQiL,EAAmB,OAAQjL,IAAS,CAC5D,MAAMkJ,EAAoB+B,EAAmBjL,CAAK,EAC5CN,EAAY0L,EAAsBlC,EAAkB,UAAYA,EAAkB,UAAU,QAC9FqC,IAEA7L,EAAU,kBAAkB2L,CAAK,EACjC3L,EAAU,kBAAkB4L,CAAG,GAEnC,MAAMvK,EAAOrB,EAAU,UACjB8L,EAAU,CAAA,EAChB,IAAIC,EAAa,OAAO,UACxB,QAASC,EAAI,EAAGA,EAAI3K,EAAK,OAAQ2K,IAAK,CAClC,MAAMlE,EAAMzG,EAAK2K,CAAC,EAClB,GAAK,CAACH,GAAYG,GAAKL,GAASK,GAAKJ,GAASC,GAAY/D,EAAI,OAAS6D,GAAS7D,EAAI,OAAS8D,EAAM,CAC/F,MAAMvL,EAAS,CACX,MAAOyH,EAAI,MACX,MAAOA,EAAI,MAAM,MAAQA,EAAI,MAAM,QAAUA,EAAI,MACjD,UAAWA,EAAI,UACf,WAAYA,EAAI,WAChB,cAAeA,EAAI,cACnB,cAAeA,EAAI,aAC3C,EACwBiE,IAAe,OAAO,YACtBA,EAAa1L,EAAO,OAExBA,EAAO,OAAS0L,EAChBD,EAAQ,KAAKzL,CAAM,CACtB,CACJ,CACD,GAAIyL,EAAQ,SAAW,EAAG,CACtBP,EAAmB,OAAOjL,EAAO,CAAC,EAClCA,IACA,QACH,CACGmB,EAAOqK,EAAQ,CAAC,EAAE,QAClBrK,EAAOqK,EAAQ,CAAC,EAAE,OAElBpK,EAAKoK,EAAQA,EAAQ,OAAS,CAAC,EAAE,QACjCpK,EAAKoK,EAAQA,EAAQ,OAAS,CAAC,EAAE,OAErC9L,EAAU,QAAQ8L,EAAS,EAAI,EAC/BtC,EAAkB,UAAYxJ,CACjC,CACD,OAAA8J,EAAe,MAAQrI,EACvBqI,EAAe,IAAMpI,EACdoI,CACV,CAKD,cAAe,CACX,MAAO,gBACV,CAMD,SAASmC,EAAa,CAClB,IAAIC,EAAM,SAAW,KAAK,KAC1B,OAAAA,GAAO,WAAa,KAAK,eACrBD,IACAC,GAAO,WAAa,KAAK,MACzBA,GAAO,SAAW,KAAK,IACvBA,GAAO,gBAAkB,KAAK,WAC9BA,GAAO,iBAAmB,KAAK,YAC/BA,GAAO,gCAAkC,KAAK,oBAAoB,OAClEA,GAAO,yBAA2B,KAAK,cAEpCA,CACV,CACL", "x_google_ignoreList": [0, 1, 2, 3]}