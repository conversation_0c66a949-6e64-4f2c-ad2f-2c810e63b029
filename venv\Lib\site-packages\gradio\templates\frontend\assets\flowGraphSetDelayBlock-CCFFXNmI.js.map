{"version": 3, "file": "flowGraphSetDelayBlock-CCFFXNmI.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Misc/timer.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphSetDelayBlock.js"], "sourcesContent": ["import { Observable } from \"../Misc/observable.js\";\n/**\n * The current state of the timer\n */\nexport var TimerState;\n(function (TimerState) {\n    /**\n     * Timer initialized, not yet started\n     */\n    TimerState[TimerState[\"INIT\"] = 0] = \"INIT\";\n    /**\n     * Timer started and counting\n     */\n    TimerState[TimerState[\"STARTED\"] = 1] = \"STARTED\";\n    /**\n     * Timer ended (whether aborted or time reached)\n     */\n    TimerState[TimerState[\"ENDED\"] = 2] = \"ENDED\";\n})(TimerState || (TimerState = {}));\n/**\n * A simple version of the timer. Will take options and start the timer immediately after calling it\n *\n * @param options options with which to initialize this timer\n * @returns an observer that can be used to stop the timer\n */\nexport function setAndStartTimer(options) {\n    let timer = 0;\n    const startTime = Date.now();\n    options.observableParameters = options.observableParameters ?? {};\n    const observer = options.contextObservable.add((payload) => {\n        const now = Date.now();\n        timer = now - startTime;\n        const data = {\n            startTime,\n            currentTime: now,\n            deltaTime: timer,\n            completeRate: timer / options.timeout,\n            payload,\n        };\n        options.onTick && options.onTick(data);\n        if (options.breakCondition && options.breakCondition()) {\n            options.contextObservable.remove(observer);\n            options.onAborted && options.onAborted(data);\n        }\n        if (timer >= options.timeout) {\n            options.contextObservable.remove(observer);\n            options.onEnded && options.onEnded(data);\n        }\n    }, options.observableParameters.mask, options.observableParameters.insertFirst, options.observableParameters.scope);\n    return observer;\n}\n/**\n * An advanced implementation of a timer class\n */\nexport class AdvancedTimer {\n    /**\n     * Will construct a new advanced timer based on the options provided. Timer will not start until start() is called.\n     * @param options construction options for this advanced timer\n     */\n    constructor(options) {\n        /**\n         * Will notify each time the timer calculates the remaining time\n         */\n        this.onEachCountObservable = new Observable();\n        /**\n         * Will trigger when the timer was aborted due to the break condition\n         */\n        this.onTimerAbortedObservable = new Observable();\n        /**\n         * Will trigger when the timer ended successfully\n         */\n        this.onTimerEndedObservable = new Observable();\n        /**\n         * Will trigger when the timer state has changed\n         */\n        this.onStateChangedObservable = new Observable();\n        this._observer = null;\n        this._breakOnNextTick = false;\n        this._tick = (payload) => {\n            const now = Date.now();\n            this._timer = now - this._startTime;\n            const data = {\n                startTime: this._startTime,\n                currentTime: now,\n                deltaTime: this._timer,\n                completeRate: this._timer / this._timeToEnd,\n                payload,\n            };\n            const shouldBreak = this._breakOnNextTick || this._breakCondition(data);\n            if (shouldBreak || this._timer >= this._timeToEnd) {\n                this._stop(data, shouldBreak);\n            }\n            else {\n                this.onEachCountObservable.notifyObservers(data);\n            }\n        };\n        this._setState(0 /* TimerState.INIT */);\n        this._contextObservable = options.contextObservable;\n        this._observableParameters = options.observableParameters ?? {};\n        this._breakCondition = options.breakCondition ?? (() => false);\n        this._timeToEnd = options.timeout;\n        if (options.onEnded) {\n            this.onTimerEndedObservable.add(options.onEnded);\n        }\n        if (options.onTick) {\n            this.onEachCountObservable.add(options.onTick);\n        }\n        if (options.onAborted) {\n            this.onTimerAbortedObservable.add(options.onAborted);\n        }\n    }\n    /**\n     * set a breaking condition for this timer. Default is to never break during count\n     * @param predicate the new break condition. Returns true to break, false otherwise\n     */\n    set breakCondition(predicate) {\n        this._breakCondition = predicate;\n    }\n    /**\n     * Reset ALL associated observables in this advanced timer\n     */\n    clearObservables() {\n        this.onEachCountObservable.clear();\n        this.onTimerAbortedObservable.clear();\n        this.onTimerEndedObservable.clear();\n        this.onStateChangedObservable.clear();\n    }\n    /**\n     * Will start a new iteration of this timer. Only one instance of this timer can run at a time.\n     *\n     * @param timeToEnd how much time to measure until timer ended\n     */\n    start(timeToEnd = this._timeToEnd) {\n        if (this._state === 1 /* TimerState.STARTED */) {\n            throw new Error(\"Timer already started. Please stop it before starting again\");\n        }\n        this._timeToEnd = timeToEnd;\n        this._startTime = Date.now();\n        this._timer = 0;\n        this._observer = this._contextObservable.add(this._tick, this._observableParameters.mask, this._observableParameters.insertFirst, this._observableParameters.scope);\n        this._setState(1 /* TimerState.STARTED */);\n    }\n    /**\n     * Will force a stop on the next tick.\n     */\n    stop() {\n        if (this._state !== 1 /* TimerState.STARTED */) {\n            return;\n        }\n        this._breakOnNextTick = true;\n    }\n    /**\n     * Dispose this timer, clearing all resources\n     */\n    dispose() {\n        if (this._observer) {\n            this._contextObservable.remove(this._observer);\n        }\n        this.clearObservables();\n    }\n    _setState(newState) {\n        this._state = newState;\n        this.onStateChangedObservable.notifyObservers(this._state);\n    }\n    _stop(data, aborted = false) {\n        this._contextObservable.remove(this._observer);\n        this._setState(2 /* TimerState.ENDED */);\n        if (aborted) {\n            this.onTimerAbortedObservable.notifyObservers(data);\n        }\n        else {\n            this.onTimerEndedObservable.notifyObservers(data);\n        }\n    }\n}\n//# sourceMappingURL=timer.js.map", "import { FlowGraphAsyncExecutionBlock } from \"../../../flowGraphAsyncExecutionBlock.js\";\nimport { RichTypeNumber } from \"../../../flowGraphRichTypes.js\";\nimport { AdvancedTimer } from \"../../../../Misc/timer.js\";\nimport { Logger } from \"../../../../Misc/logger.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\n/**\n * Block that sets a delay in seconds before activating the output signal.\n */\nexport class FlowGraphSetDelayBlock extends FlowGraphAsyncExecutionBlock {\n    constructor(config) {\n        super(config);\n        this.cancel = this._registerSignalInput(\"cancel\");\n        this.duration = this.registerDataInput(\"duration\", RichTypeNumber);\n        this.lastDelayIndex = this.registerDataOutput(\"lastDelayIndex\", RichTypeNumber, -1);\n    }\n    _preparePendingTasks(context) {\n        const duration = this.duration.getValue(context);\n        if (duration < 0 || isNaN(duration) || !isFinite(duration)) {\n            return this._reportError(context, \"Invalid duration in SetDelay block\");\n        }\n        // active delays are global to the context\n        const activeDelays = context._getGlobalContextVariable(\"activeDelays\", 0);\n        if (activeDelays >= FlowGraphSetDelayBlock.MaxParallelDelayCount) {\n            return this._reportError(context, \"Max parallel delays reached\");\n        }\n        // get the last global delay index\n        const lastDelayIndex = context._getGlobalContextVariable(\"lastDelayIndex\", -1);\n        // these are block-specific and not global\n        const timers = context._getExecutionVariable(this, \"pendingDelays\", []);\n        const scene = context.configuration.scene;\n        const timer = new AdvancedTimer({\n            timeout: duration * 1000, // duration is in seconds\n            contextObservable: scene.onBeforeRenderObservable,\n            onEnded: () => this._onEnded(timer, context),\n        });\n        timer.start();\n        const newIndex = lastDelayIndex + 1;\n        this.lastDelayIndex.setValue(newIndex, context);\n        context._setGlobalContextVariable(\"lastDelayIndex\", newIndex);\n        timers[newIndex] = timer;\n        context._setExecutionVariable(this, \"pendingDelays\", timers);\n    }\n    _cancelPendingTasks(context) {\n        const timers = context._getExecutionVariable(this, \"pendingDelays\", []);\n        for (const timer of timers) {\n            timer?.dispose();\n        }\n        context._deleteExecutionVariable(this, \"pendingDelays\");\n        this.lastDelayIndex.setValue(-1, context);\n    }\n    _execute(context, callingSignal) {\n        if (callingSignal === this.cancel) {\n            this._cancelPendingTasks(context);\n            return;\n        }\n        else {\n            this._preparePendingTasks(context);\n            this.out._activateSignal(context);\n        }\n    }\n    getClassName() {\n        return \"FlowGraphSetDelayBlock\" /* FlowGraphBlockNames.SetDelay */;\n    }\n    _onEnded(timer, context) {\n        const timers = context._getExecutionVariable(this, \"pendingDelays\", []);\n        const index = timers.indexOf(timer);\n        if (index !== -1) {\n            timers.splice(index, 1);\n        }\n        else {\n            Logger.Warn(\"FlowGraphTimerBlock: Timer ended but was not found in the running timers list\");\n        }\n        context._removePendingBlock(this);\n        this.done._activateSignal(context);\n    }\n}\n/**\n * The maximum number of parallel delays that can be set per node.\n */\nFlowGraphSetDelayBlock.MaxParallelDelayCount = 100;\nRegisterClass(\"FlowGraphSetDelayBlock\" /* FlowGraphBlockNames.SetDelay */, FlowGraphSetDelayBlock);\n//# sourceMappingURL=flowGraphSetDelayBlock.js.map"], "names": ["TimerState", "AdvancedTimer", "options", "Observable", "payload", "now", "data", "shouldBreak", "predicate", "timeToEnd", "newState", "aborted", "FlowGraphSetDelayBlock", "FlowGraphAsyncExecutionBlock", "config", "RichTypeNumber", "context", "duration", "lastDelayIndex", "timers", "scene", "timer", "newIndex", "callingSignal", "index", "<PERSON><PERSON>", "RegisterClass"], "mappings": "8PAIO,IAAIA,GACV,SAAUA,EAAY,CAInBA,EAAWA,EAAW,KAAU,CAAC,EAAI,OAIrCA,EAAWA,EAAW,QAAa,CAAC,EAAI,UAIxCA,EAAWA,EAAW,MAAW,CAAC,EAAI,OAC1C,GAAGA,IAAeA,EAAa,CAAE,EAAC,EAoC3B,MAAMC,CAAc,CAKvB,YAAYC,EAAS,CAIjB,KAAK,sBAAwB,IAAIC,EAIjC,KAAK,yBAA2B,IAAIA,EAIpC,KAAK,uBAAyB,IAAIA,EAIlC,KAAK,yBAA2B,IAAIA,EACpC,KAAK,UAAY,KACjB,KAAK,iBAAmB,GACxB,KAAK,MAASC,GAAY,CACtB,MAAMC,EAAM,KAAK,MACjB,KAAK,OAASA,EAAM,KAAK,WACzB,MAAMC,EAAO,CACT,UAAW,KAAK,WAChB,YAAaD,EACb,UAAW,KAAK,OAChB,aAAc,KAAK,OAAS,KAAK,WACjC,QAAAD,CAChB,EACkBG,EAAc,KAAK,kBAAoB,KAAK,gBAAgBD,CAAI,EAClEC,GAAe,KAAK,QAAU,KAAK,WACnC,KAAK,MAAMD,EAAMC,CAAW,EAG5B,KAAK,sBAAsB,gBAAgBD,CAAI,CAE/D,EACQ,KAAK,UAAU,GACf,KAAK,mBAAqBJ,EAAQ,kBAClC,KAAK,sBAAwBA,EAAQ,sBAAwB,CAAA,EAC7D,KAAK,gBAAkBA,EAAQ,iBAAmB,IAAM,IACxD,KAAK,WAAaA,EAAQ,QACtBA,EAAQ,SACR,KAAK,uBAAuB,IAAIA,EAAQ,OAAO,EAE/CA,EAAQ,QACR,KAAK,sBAAsB,IAAIA,EAAQ,MAAM,EAE7CA,EAAQ,WACR,KAAK,yBAAyB,IAAIA,EAAQ,SAAS,CAE1D,CAKD,IAAI,eAAeM,EAAW,CAC1B,KAAK,gBAAkBA,CAC1B,CAID,kBAAmB,CACf,KAAK,sBAAsB,QAC3B,KAAK,yBAAyB,QAC9B,KAAK,uBAAuB,QAC5B,KAAK,yBAAyB,OACjC,CAMD,MAAMC,EAAY,KAAK,WAAY,CAC/B,GAAI,KAAK,SAAW,EAChB,MAAM,IAAI,MAAM,6DAA6D,EAEjF,KAAK,WAAaA,EAClB,KAAK,WAAa,KAAK,MACvB,KAAK,OAAS,EACd,KAAK,UAAY,KAAK,mBAAmB,IAAI,KAAK,MAAO,KAAK,sBAAsB,KAAM,KAAK,sBAAsB,YAAa,KAAK,sBAAsB,KAAK,EAClK,KAAK,UAAU,EAClB,CAID,MAAO,CACC,KAAK,SAAW,IAGpB,KAAK,iBAAmB,GAC3B,CAID,SAAU,CACF,KAAK,WACL,KAAK,mBAAmB,OAAO,KAAK,SAAS,EAEjD,KAAK,iBAAgB,CACxB,CACD,UAAUC,EAAU,CAChB,KAAK,OAASA,EACd,KAAK,yBAAyB,gBAAgB,KAAK,MAAM,CAC5D,CACD,MAAMJ,EAAMK,EAAU,GAAO,CACzB,KAAK,mBAAmB,OAAO,KAAK,SAAS,EAC7C,KAAK,UAAU,GACXA,EACA,KAAK,yBAAyB,gBAAgBL,CAAI,EAGlD,KAAK,uBAAuB,gBAAgBA,CAAI,CAEvD,CACL,CCtKO,MAAMM,UAA+BC,CAA6B,CACrE,YAAYC,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,OAAS,KAAK,qBAAqB,QAAQ,EAChD,KAAK,SAAW,KAAK,kBAAkB,WAAYC,CAAc,EACjE,KAAK,eAAiB,KAAK,mBAAmB,iBAAkBA,EAAgB,EAAE,CACrF,CACD,qBAAqBC,EAAS,CAC1B,MAAMC,EAAW,KAAK,SAAS,SAASD,CAAO,EAC/C,GAAIC,EAAW,GAAK,MAAMA,CAAQ,GAAK,CAAC,SAASA,CAAQ,EACrD,OAAO,KAAK,aAAaD,EAAS,oCAAoC,EAI1E,GADqBA,EAAQ,0BAA0B,eAAgB,CAAC,GACpDJ,EAAuB,sBACvC,OAAO,KAAK,aAAaI,EAAS,6BAA6B,EAGnE,MAAME,EAAiBF,EAAQ,0BAA0B,iBAAkB,EAAE,EAEvEG,EAASH,EAAQ,sBAAsB,KAAM,gBAAiB,CAAA,CAAE,EAChEI,EAAQJ,EAAQ,cAAc,MAC9BK,EAAQ,IAAIpB,EAAc,CAC5B,QAASgB,EAAW,IACpB,kBAAmBG,EAAM,yBACzB,QAAS,IAAM,KAAK,SAASC,EAAOL,CAAO,CACvD,CAAS,EACDK,EAAM,MAAK,EACX,MAAMC,EAAWJ,EAAiB,EAClC,KAAK,eAAe,SAASI,EAAUN,CAAO,EAC9CA,EAAQ,0BAA0B,iBAAkBM,CAAQ,EAC5DH,EAAOG,CAAQ,EAAID,EACnBL,EAAQ,sBAAsB,KAAM,gBAAiBG,CAAM,CAC9D,CACD,oBAAoBH,EAAS,CACzB,MAAMG,EAASH,EAAQ,sBAAsB,KAAM,gBAAiB,CAAA,CAAE,EACtE,UAAWK,KAASF,EAChBE,GAAO,QAAO,EAElBL,EAAQ,yBAAyB,KAAM,eAAe,EACtD,KAAK,eAAe,SAAS,GAAIA,CAAO,CAC3C,CACD,SAASA,EAASO,EAAe,CAC7B,GAAIA,IAAkB,KAAK,OAAQ,CAC/B,KAAK,oBAAoBP,CAAO,EAChC,MACH,MAEG,KAAK,qBAAqBA,CAAO,EACjC,KAAK,IAAI,gBAAgBA,CAAO,CAEvC,CACD,cAAe,CACX,MAAO,wBACV,CACD,SAASK,EAAOL,EAAS,CACrB,MAAMG,EAASH,EAAQ,sBAAsB,KAAM,gBAAiB,CAAA,CAAE,EAChEQ,EAAQL,EAAO,QAAQE,CAAK,EAC9BG,IAAU,GACVL,EAAO,OAAOK,EAAO,CAAC,EAGtBC,EAAO,KAAK,+EAA+E,EAE/FT,EAAQ,oBAAoB,IAAI,EAChC,KAAK,KAAK,gBAAgBA,CAAO,CACpC,CACL,CAIAJ,EAAuB,sBAAwB,IAC/Cc,EAAc,yBAA6Dd,CAAsB", "x_google_ignoreList": [0, 1]}