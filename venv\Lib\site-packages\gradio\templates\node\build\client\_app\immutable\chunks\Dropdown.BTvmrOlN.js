import{SvelteComponent as ae,init as de,safe_not_equal as me,add_render_callback as be,element as q,space as G,empty as oe,claim_element as U,children as P,detach as O,claim_space as K,attr as k,insert_hydration as R,listen as z,transition_in as B,group_outros as we,transition_out as j,check_outros as ge,run_all as ne,createEventDispatcher as pe,ensure_array_like as te,set_style as V,prevent_default as Ie,create_bidirectional_transition as se,destroy_each as Te,text as ke,get_svelte_dataset as Ce,claim_text as ye,toggle_class as A,append_hydration as S,set_data as De,binding_callbacks as v,create_component as x,claim_component as $,mount_component as ee,set_input_value as fe,destroy_component as le,afterUpdate as Ee}from"../../../svelte/svelte.js";import{fly as ie}from"../../../svelte/svelte-submodules.js";import{y as Ne}from"./2.B2AoQPnG.js";import{D as Se}from"./DropdownArrow.pfrcUdj1.js";function ue(n,l,e){const _=n.slice();return _[28]=l[e],_}function re(n){let l,e,_,c,s,f=te(n[1]),o=[];for(let t=0;t<f.length;t+=1)o[t]=_e(ue(n,f,t));return{c(){l=q("ul");for(let t=0;t<o.length;t+=1)o[t].c();this.h()},l(t){l=U(t,"UL",{class:!0,role:!0});var g=P(l);for(let h=0;h<o.length;h+=1)o[h].l(g);g.forEach(O),this.h()},h(){k(l,"class","options svelte-y6qw75"),k(l,"role","listbox"),V(l,"top",n[9]),V(l,"bottom",n[10]),V(l,"max-height",`calc(${n[11]}px - var(--window-padding))`),V(l,"width",n[8]+"px")},m(t,g){R(t,l,g);for(let h=0;h<o.length;h+=1)o[h]&&o[h].m(l,null);n[24](l),_=!0,c||(s=[z(l,"mousedown",Ie(n[22])),z(l,"scroll",n[23])],c=!0)},p(t,g){if(g&307){f=te(t[1]);let h;for(h=0;h<f.length;h+=1){const u=ue(t,f,h);o[h]?o[h].p(u,g):(o[h]=_e(u),o[h].c(),o[h].m(l,null))}for(;h<o.length;h+=1)o[h].d(1);o.length=f.length}g&512&&V(l,"top",t[9]),g&1024&&V(l,"bottom",t[10]),g&2048&&V(l,"max-height",`calc(${t[11]}px - var(--window-padding))`),g&256&&V(l,"width",t[8]+"px")},i(t){_||(t&&be(()=>{_&&(e||(e=se(l,ie,{duration:200,y:5},!0)),e.run(1))}),_=!0)},o(t){t&&(e||(e=se(l,ie,{duration:200,y:5},!1)),e.run(0)),_=!1},d(t){t&&O(l),Te(o,t),n[24](null),t&&e&&e.end(),c=!1,ne(s)}}}function _e(n){let l,e,_="✓",c,s=n[0][n[28]][0]+"",f,o,t,g,h;return{c(){l=q("li"),e=q("span"),e.textContent=_,c=G(),f=ke(s),o=G(),this.h()},l(u){l=U(u,"LI",{class:!0,"data-index":!0,"aria-label":!0,"data-testid":!0,role:!0,"aria-selected":!0});var a=P(l);e=U(a,"SPAN",{class:!0,"data-svelte-h":!0}),Ce(e)!=="svelte-1id9b8g"&&(e.textContent=_),c=K(a),f=ye(a,s),o=K(a),a.forEach(O),this.h()},h(){k(e,"class","inner-item svelte-y6qw75"),A(e,"hide",!n[4].includes(n[28])),k(l,"class","item svelte-y6qw75"),k(l,"data-index",t=n[28]),k(l,"aria-label",g=n[0][n[28]][0]),k(l,"data-testid","dropdown-option"),k(l,"role","option"),k(l,"aria-selected",h=n[4].includes(n[28])),A(l,"selected",n[4].includes(n[28])),A(l,"active",n[28]===n[5]),A(l,"bg-gray-100",n[28]===n[5]),A(l,"dark:bg-gray-600",n[28]===n[5]),V(l,"width",n[8]+"px")},m(u,a){R(u,l,a),S(l,e),S(l,c),S(l,f),S(l,o)},p(u,a){a&18&&A(e,"hide",!u[4].includes(u[28])),a&3&&s!==(s=u[0][u[28]][0]+"")&&De(f,s),a&2&&t!==(t=u[28])&&k(l,"data-index",t),a&3&&g!==(g=u[0][u[28]][0])&&k(l,"aria-label",g),a&18&&h!==(h=u[4].includes(u[28]))&&k(l,"aria-selected",h),a&18&&A(l,"selected",u[4].includes(u[28])),a&34&&A(l,"active",u[28]===u[5]),a&34&&A(l,"bg-gray-100",u[28]===u[5]),a&34&&A(l,"dark:bg-gray-600",u[28]===u[5]),a&256&&V(l,"width",u[8]+"px")},d(u){u&&O(l)}}}function Ve(n){let l,e,_,c,s;be(n[20]);let f=n[2]&&!n[3]&&re(n);return{c(){l=q("div"),e=G(),f&&f.c(),_=oe(),this.h()},l(o){l=U(o,"DIV",{class:!0}),P(l).forEach(O),e=K(o),f&&f.l(o),_=oe(),this.h()},h(){k(l,"class","reference")},m(o,t){R(o,l,t),n[21](l),R(o,e,t),f&&f.m(o,t),R(o,_,t),c||(s=[z(window,"scroll",n[14]),z(window,"resize",n[20])],c=!0)},p(o,[t]){o[2]&&!o[3]?f?(f.p(o,t),t&12&&B(f,1)):(f=re(o),f.c(),B(f,1),f.m(_.parentNode,_)):f&&(we(),j(f,1,1,()=>{f=null}),ge())},i(o){B(f)},o(o){j(f)},d(o){o&&(O(l),O(e),O(_)),n[21](null),f&&f.d(o),c=!1,ne(s)}}}function qe(n,l,e){let{choices:_}=l,{filtered_indices:c}=l,{show_options:s=!1}=l,{disabled:f=!1}=l,{selected_indices:o=[]}=l,{active_index:t=null}=l,{remember_scroll:g=!1}=l,h,u,a,D,E,r,i,d,y,m,C=0;function L(){const{top:w,bottom:F}=E.getBoundingClientRect();e(17,h=w),e(18,u=m-F)}let I=null;function T(){s&&(I!==null&&clearTimeout(I),I=setTimeout(()=>{L(),I=null},10))}function p(){var w;(w=r==null?void 0:r.scrollTo)==null||w.call(r,0,C)}const N=pe();function H(){e(12,m=window.innerHeight)}function Q(w){v[w?"unshift":"push"](()=>{E=w,e(6,E)})}const X=w=>N("change",w),M=w=>e(13,C=w.currentTarget.scrollTop);function Y(w){v[w?"unshift":"push"](()=>{r=w,e(7,r)})}return n.$$set=w=>{"choices"in w&&e(0,_=w.choices),"filtered_indices"in w&&e(1,c=w.filtered_indices),"show_options"in w&&e(2,s=w.show_options),"disabled"in w&&e(3,f=w.disabled),"selected_indices"in w&&e(4,o=w.selected_indices),"active_index"in w&&e(5,t=w.active_index),"remember_scroll"in w&&e(16,g=w.remember_scroll)},n.$$.update=()=>{var w,F;if(n.$$.dirty&983252){if(s&&E){if(g)p();else if(r&&o.length>0){let Z=r.querySelectorAll("li");for(const W of Array.from(Z))if(W.getAttribute("data-index")===o[0].toString()){(w=r==null?void 0:r.scrollTo)==null||w.call(r,0,W.offsetTop);break}}L();const J=(F=E.parentElement)==null?void 0:F.getBoundingClientRect();e(19,a=(J==null?void 0:J.height)||0),e(8,D=(J==null?void 0:J.width)||0)}u>h?(e(9,i=`${h}px`),e(11,y=u),e(10,d=null)):(e(10,d=`${u+a}px`),e(11,y=h-a),e(9,i=null))}},[_,c,s,f,o,t,E,r,D,i,d,y,m,C,T,N,g,h,u,a,H,Q,X,M,Y]}class Ue extends ae{constructor(l){super(),de(this,l,qe,Ve,me,{choices:0,filtered_indices:1,show_options:2,disabled:3,selected_indices:4,active_index:5,remember_scroll:16})}}function Le(n,l){return(n%l+l)%l}function ce(n,l){return n.reduce((e,_,c)=>((!l||_[0].toLowerCase().includes(l.toLowerCase()))&&e.push(c),e),[])}function ze(n,l,e){n("change",l),e||n("input")}function Be(n,l,e){if(n.key==="Escape")return[!1,l];if((n.key==="ArrowDown"||n.key==="ArrowUp")&&e.length>0)if(l===null)l=n.key==="ArrowDown"?e[0]:e[e.length-1];else{const _=e.indexOf(l),c=n.key==="ArrowUp"?-1:1;l=e[Le(_+c,e.length)]}return[!0,l]}function He(n){let l;return{c(){l=ke(n[0])},l(e){l=ye(e,n[0])},m(e,_){R(e,l,_)},p(e,_){_[0]&1&&De(l,e[0])},d(e){e&&O(l)}}}function he(n){let l,e,_;return e=new Se({}),{c(){l=q("div"),x(e.$$.fragment),this.h()},l(c){l=U(c,"DIV",{class:!0});var s=P(l);$(e.$$.fragment,s),s.forEach(O),this.h()},h(){k(l,"class","icon-wrap svelte-1hfxrpf")},m(c,s){R(c,l,s),ee(e,l,null),_=!0},i(c){_||(B(e.$$.fragment,c),_=!0)},o(c){j(e.$$.fragment,c),_=!1},d(c){c&&O(l),le(e)}}}function Je(n){let l,e,_,c,s,f,o,t,g,h,u,a,D,E;e=new Ne({props:{show_label:n[4],info:n[1],$$slots:{default:[He]},$$scope:{ctx:n}}});let r=!n[3]&&he();return u=new Ue({props:{show_options:n[12],choices:n[2],filtered_indices:n[10],disabled:n[3],selected_indices:n[11]===null?[]:[n[11]],active_index:n[14]}}),u.$on("change",n[16]),{c(){l=q("div"),x(e.$$.fragment),_=G(),c=q("div"),s=q("div"),f=q("div"),o=q("input"),g=G(),r&&r.c(),h=G(),x(u.$$.fragment),this.h()},l(i){l=U(i,"DIV",{class:!0});var d=P(l);$(e.$$.fragment,d),_=K(d),c=U(d,"DIV",{class:!0});var y=P(c);s=U(y,"DIV",{class:!0});var m=P(s);f=U(m,"DIV",{class:!0});var C=P(f);o=U(C,"INPUT",{role:!0,"aria-controls":!0,"aria-expanded":!0,"aria-label":!0,class:!0,autocomplete:!0}),g=K(C),r&&r.l(C),C.forEach(O),m.forEach(O),h=K(y),$(u.$$.fragment,y),y.forEach(O),d.forEach(O),this.h()},h(){k(o,"role","listbox"),k(o,"aria-controls","dropdown-options"),k(o,"aria-expanded",n[12]),k(o,"aria-label",n[0]),k(o,"class","border-none svelte-1hfxrpf"),o.disabled=n[3],k(o,"autocomplete","off"),o.readOnly=t=!n[7],A(o,"subdued",!n[13].includes(n[9])&&!n[6]),k(f,"class","secondary-wrap svelte-1hfxrpf"),k(s,"class","wrap-inner svelte-1hfxrpf"),A(s,"show_options",n[12]),k(c,"class","wrap svelte-1hfxrpf"),k(l,"class","svelte-1hfxrpf"),A(l,"container",n[5])},m(i,d){R(i,l,d),ee(e,l,null),S(l,_),S(l,c),S(c,s),S(s,f),S(f,o),fe(o,n[9]),n[29](o),S(f,g),r&&r.m(f,null),S(c,h),ee(u,c,null),a=!0,D||(E=[z(o,"input",n[28]),z(o,"keydown",n[19]),z(o,"keyup",n[30]),z(o,"blur",n[18]),z(o,"focus",n[17])],D=!0)},p(i,d){const y={};d[0]&16&&(y.show_label=i[4]),d[0]&2&&(y.info=i[1]),d[0]&1|d[1]&8&&(y.$$scope={dirty:d,ctx:i}),e.$set(y),(!a||d[0]&4096)&&k(o,"aria-expanded",i[12]),(!a||d[0]&1)&&k(o,"aria-label",i[0]),(!a||d[0]&8)&&(o.disabled=i[3]),(!a||d[0]&128&&t!==(t=!i[7]))&&(o.readOnly=t),d[0]&512&&o.value!==i[9]&&fe(o,i[9]),(!a||d[0]&8768)&&A(o,"subdued",!i[13].includes(i[9])&&!i[6]),i[3]?r&&(we(),j(r,1,1,()=>{r=null}),ge()):r?d[0]&8&&B(r,1):(r=he(),r.c(),B(r,1),r.m(f,null)),(!a||d[0]&4096)&&A(s,"show_options",i[12]);const m={};d[0]&4096&&(m.show_options=i[12]),d[0]&4&&(m.choices=i[2]),d[0]&1024&&(m.filtered_indices=i[10]),d[0]&8&&(m.disabled=i[3]),d[0]&2048&&(m.selected_indices=i[11]===null?[]:[i[11]]),d[0]&16384&&(m.active_index=i[14]),u.$set(m),(!a||d[0]&32)&&A(l,"container",i[5])},i(i){a||(B(e.$$.fragment,i),B(r),B(u.$$.fragment,i),a=!0)},o(i){j(e.$$.fragment,i),j(r),j(u.$$.fragment,i),a=!1},d(i){i&&O(l),le(e),n[29](null),r&&r.d(),le(u),D=!1,ne(E)}}}function Pe(n,l,e){let{label:_}=l,{info:c=void 0}=l,{value:s=void 0}=l,f,{value_is_output:o=!1}=l,{choices:t}=l,g,{disabled:h=!1}=l,{show_label:u}=l,{container:a=!0}=l,{allow_custom_value:D=!1}=l,{filterable:E=!0}=l,r,i=!1,d,y,m="",C="",L=!1,I=[],T=null,p=null,N;const H=pe();s&&(N=t.map(b=>b[1]).indexOf(s),p=N,p===-1?(f=s,p=null):([m,f]=t[p],C=m),M());function Q(){e(13,d=t.map(b=>b[0])),e(24,y=t.map(b=>b[1]))}const X=typeof window<"u";function M(){Q(),s===void 0||Array.isArray(s)&&s.length===0?(e(9,m=""),e(11,p=null)):y.includes(s)?(e(9,m=d[y.indexOf(s)]),e(11,p=y.indexOf(s))):D?(e(9,m=s),e(11,p=null)):(e(9,m=""),e(11,p=null)),e(27,N=p)}function Y(b){if(e(11,p=parseInt(b.detail.target.dataset.index)),isNaN(p)){e(11,p=null);return}e(12,i=!1),e(14,T=null),r.blur()}function w(b){e(10,I=t.map((Re,Oe)=>Oe)),e(12,i=!0),H("focus")}function F(){D?e(20,s=m):e(9,m=d[y.indexOf(s)]),e(12,i=!1),e(14,T=null),H("blur")}function J(b){e(12,[i,T]=Be(b,T,I),i,(e(14,T),e(2,t),e(23,g),e(6,D),e(9,m),e(10,I),e(8,r),e(25,C),e(11,p),e(27,N),e(26,L),e(24,y))),b.key==="Enter"&&(T!==null?(e(11,p=T),e(12,i=!1),r.blur(),e(14,T=null)):d.includes(m)?(e(11,p=d.indexOf(m)),e(12,i=!1),e(14,T=null),r.blur()):D&&(e(20,s=m),e(11,p=null),e(12,i=!1),e(14,T=null),r.blur()))}Ee(()=>{e(21,o=!1),e(26,L=!0)});function Z(){m=this.value,e(9,m),e(11,p),e(27,N),e(26,L),e(2,t),e(24,y)}function W(b){v[b?"unshift":"push"](()=>{r=b,e(8,r)})}const Ae=b=>H("key_up",{key:b.key,input_value:m});return n.$$set=b=>{"label"in b&&e(0,_=b.label),"info"in b&&e(1,c=b.info),"value"in b&&e(20,s=b.value),"value_is_output"in b&&e(21,o=b.value_is_output),"choices"in b&&e(2,t=b.choices),"disabled"in b&&e(3,h=b.disabled),"show_label"in b&&e(4,u=b.show_label),"container"in b&&e(5,a=b.container),"allow_custom_value"in b&&e(6,D=b.allow_custom_value),"filterable"in b&&e(7,E=b.filterable)},n.$$.update=()=>{n.$$.dirty[0]&218105860&&p!==N&&p!==null&&L&&(e(9,[m,s]=t[p],m,(e(20,s),e(11,p),e(27,N),e(26,L),e(2,t),e(24,y))),e(27,N=p),H("select",{index:p,value:y[p],selected:!0})),n.$$.dirty[0]&7340032&&JSON.stringify(f)!==JSON.stringify(s)&&(M(),ze(H,s,o),e(22,f=s)),n.$$.dirty[0]&4&&Q(),n.$$.dirty[0]&8390468&&t!==g&&(D||M(),e(23,g=t),e(10,I=ce(t,m)),!D&&I.length>0&&e(14,T=I[0]),X&&r===document.activeElement&&e(12,i=!0)),n.$$.dirty[0]&33556036&&m!==C&&(e(10,I=ce(t,m)),e(25,C=m),!D&&I.length>0&&e(14,T=I[0]))},[_,c,t,h,u,a,D,E,r,m,I,p,i,d,T,H,Y,w,F,J,s,o,f,g,y,C,L,N,Z,W,Ae]}class Me extends ae{constructor(l){super(),de(this,l,Pe,Je,me,{label:0,info:1,value:20,value_is_output:21,choices:2,disabled:3,show_label:4,container:5,allow_custom_value:6,filterable:7},null,[-1,-1])}}export{Ue as D,ze as a,Be as b,Me as c,ce as h};
//# sourceMappingURL=Dropdown.BTvmrOlN.js.map
