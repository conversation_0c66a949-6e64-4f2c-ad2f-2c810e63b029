import{_ as c,c as L,s as l,aq as P,M as R,V as a,b1 as O,a as m,N as S,T as M,l as y,R as d,ay as f,aS as I,Q as x}from"./index-Dpxo-yl_.js";class r{}r.AUTOSAMPLERSUFFIX="Sampler";r.DIS<PERSON>LEUA="#define DISABLE_UNIFORMITY_ANALYSIS";r.ALPHA_DISABLE=0;r.ALPHA_ADD=1;r.ALPHA_COMBINE=2;r.ALPHA_SUBTRACT=3;r.ALPHA_MULTIPLY=4;r.AL<PERSON>HA_MAXIMIZED=5;r.ALPHA_ONEONE=6;r.AL<PERSON>HA_PREMULTIPLIED=7;r.ALPHA_PREMULTIPLIED_PORTERDUFF=8;r.ALPHA_INTERPOLATE=9;r.ALPHA_SCREENMODE=10;r.ALPHA_ONEONE_ONEONE=11;r.AL<PERSON><PERSON>_ALPHATOCOLOR=12;r.AL<PERSON><PERSON>_REVERSEONEMINUS=13;r.<PERSON><PERSON>_SRC_DSTONEMINUSSRCALPHA=14;r.<PERSON><PERSON>HA_ONEONE_ONEZERO=15;r.ALPHA_EXCLUSION=16;r.ALPHA_LAYER_ACCUMULATE=17;r.ALPHA_EQUATION_ADD=0;r.ALPHA_EQUATION_SUBSTRACT=1;r.ALPHA_EQUATION_REVERSE_SUBTRACT=2;r.ALPHA_EQUATION_MAX=3;r.ALPHA_EQUATION_MIN=4;r.ALPHA_EQUATION_DARKEN=5;r.DELAYLOADSTATE_NONE=0;r.DELAYLOADSTATE_LOADED=1;r.DELAYLOADSTATE_LOADING=2;r.DELAYLOADSTATE_NOTLOADED=4;r.NEVER=512;r.ALWAYS=519;r.LESS=513;r.EQUAL=514;r.LEQUAL=515;r.GREATER=516;r.GEQUAL=518;r.NOTEQUAL=517;r.KEEP=7680;r.ZERO=0;r.REPLACE=7681;r.INCR=7682;r.DECR=7683;r.INVERT=5386;r.INCR_WRAP=34055;r.DECR_WRAP=34056;r.TEXTURE_CLAMP_ADDRESSMODE=0;r.TEXTURE_WRAP_ADDRESSMODE=1;r.TEXTURE_MIRROR_ADDRESSMODE=2;r.TEXTURE_CREATIONFLAG_STORAGE=1;r.TEXTUREFORMAT_ALPHA=0;r.TEXTUREFORMAT_LUMINANCE=1;r.TEXTUREFORMAT_LUMINANCE_ALPHA=2;r.TEXTUREFORMAT_RGB=4;r.TEXTUREFORMAT_RGBA=5;r.TEXTUREFORMAT_RED=6;r.TEXTUREFORMAT_R=6;r.TEXTUREFORMAT_R16_UNORM=33322;r.TEXTUREFORMAT_RG16_UNORM=33324;r.TEXTUREFORMAT_RGB16_UNORM=32852;r.TEXTUREFORMAT_RGBA16_UNORM=32859;r.TEXTUREFORMAT_R16_SNORM=36760;r.TEXTUREFORMAT_RG16_SNORM=36761;r.TEXTUREFORMAT_RGB16_SNORM=36762;r.TEXTUREFORMAT_RGBA16_SNORM=36763;r.TEXTUREFORMAT_RG=7;r.TEXTUREFORMAT_RED_INTEGER=8;r.TEXTUREFORMAT_R_INTEGER=8;r.TEXTUREFORMAT_RG_INTEGER=9;r.TEXTUREFORMAT_RGB_INTEGER=10;r.TEXTUREFORMAT_RGBA_INTEGER=11;r.TEXTUREFORMAT_BGRA=12;r.TEXTUREFORMAT_DEPTH24_STENCIL8=13;r.TEXTUREFORMAT_DEPTH32_FLOAT=14;r.TEXTUREFORMAT_DEPTH16=15;r.TEXTUREFORMAT_DEPTH24=16;r.TEXTUREFORMAT_DEPTH24UNORM_STENCIL8=17;r.TEXTUREFORMAT_DEPTH32FLOAT_STENCIL8=18;r.TEXTUREFORMAT_STENCIL8=19;r.TEXTUREFORMAT_UNDEFINED=4294967295;r.TEXTUREFORMAT_COMPRESSED_RGBA_BPTC_UNORM=36492;r.TEXTUREFORMAT_COMPRESSED_SRGB_ALPHA_BPTC_UNORM=36493;r.TEXTUREFORMAT_COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT=36495;r.TEXTUREFORMAT_COMPRESSED_RGB_BPTC_SIGNED_FLOAT=36494;r.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT5=33779;r.TEXTUREFORMAT_COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT=35919;r.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT3=33778;r.TEXTUREFORMAT_COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT=35918;r.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT1=33777;r.TEXTUREFORMAT_COMPRESSED_RGB_S3TC_DXT1=33776;r.TEXTUREFORMAT_COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT=35917;r.TEXTUREFORMAT_COMPRESSED_SRGB_S3TC_DXT1_EXT=35916;r.TEXTUREFORMAT_COMPRESSED_RGBA_ASTC_4x4=37808;r.TEXTUREFORMAT_COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR=37840;r.TEXTUREFORMAT_COMPRESSED_RGB_ETC1_WEBGL=36196;r.TEXTUREFORMAT_COMPRESSED_RGB8_ETC2=37492;r.TEXTUREFORMAT_COMPRESSED_SRGB8_ETC2=37493;r.TEXTUREFORMAT_COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2=37494;r.TEXTUREFORMAT_COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2=37495;r.TEXTUREFORMAT_COMPRESSED_RGBA8_ETC2_EAC=37496;r.TEXTUREFORMAT_COMPRESSED_SRGB8_ALPHA8_ETC2_EAC=37497;r.TEXTURETYPE_UNSIGNED_BYTE=0;r.TEXTURETYPE_UNSIGNED_INT=0;r.TEXTURETYPE_FLOAT=1;r.TEXTURETYPE_HALF_FLOAT=2;r.TEXTURETYPE_BYTE=3;r.TEXTURETYPE_SHORT=4;r.TEXTURETYPE_UNSIGNED_SHORT=5;r.TEXTURETYPE_INT=6;r.TEXTURETYPE_UNSIGNED_INTEGER=7;r.TEXTURETYPE_UNSIGNED_SHORT_4_4_4_4=8;r.TEXTURETYPE_UNSIGNED_SHORT_5_5_5_1=9;r.TEXTURETYPE_UNSIGNED_SHORT_5_6_5=10;r.TEXTURETYPE_UNSIGNED_INT_2_10_10_10_REV=11;r.TEXTURETYPE_UNSIGNED_INT_24_8=12;r.TEXTURETYPE_UNSIGNED_INT_10F_11F_11F_REV=13;r.TEXTURETYPE_UNSIGNED_INT_5_9_9_9_REV=14;r.TEXTURETYPE_FLOAT_32_UNSIGNED_INT_24_8_REV=15;r.TEXTURETYPE_UNDEFINED=16;r.TEXTURE_2D=3553;r.TEXTURE_2D_ARRAY=35866;r.TEXTURE_CUBE_MAP=34067;r.TEXTURE_CUBE_MAP_ARRAY=3735928559;r.TEXTURE_3D=32879;r.TEXTURE_NEAREST_SAMPLINGMODE=1;r.TEXTURE_NEAREST_NEAREST=1;r.TEXTURE_BILINEAR_SAMPLINGMODE=2;r.TEXTURE_LINEAR_LINEAR=2;r.TEXTURE_TRILINEAR_SAMPLINGMODE=3;r.TEXTURE_LINEAR_LINEAR_MIPLINEAR=3;r.TEXTURE_NEAREST_NEAREST_MIPNEAREST=4;r.TEXTURE_NEAREST_LINEAR_MIPNEAREST=5;r.TEXTURE_NEAREST_LINEAR_MIPLINEAR=6;r.TEXTURE_NEAREST_LINEAR=7;r.TEXTURE_NEAREST_NEAREST_MIPLINEAR=8;r.TEXTURE_LINEAR_NEAREST_MIPNEAREST=9;r.TEXTURE_LINEAR_NEAREST_MIPLINEAR=10;r.TEXTURE_LINEAR_LINEAR_MIPNEAREST=11;r.TEXTURE_LINEAR_NEAREST=12;r.TEXTURE_EXPLICIT_MODE=0;r.TEXTURE_SPHERICAL_MODE=1;r.TEXTURE_PLANAR_MODE=2;r.TEXTURE_CUBIC_MODE=3;r.TEXTURE_PROJECTION_MODE=4;r.TEXTURE_SKYBOX_MODE=5;r.TEXTURE_INVCUBIC_MODE=6;r.TEXTURE_EQUIRECTANGULAR_MODE=7;r.TEXTURE_FIXED_EQUIRECTANGULAR_MODE=8;r.TEXTURE_FIXED_EQUIRECTANGULAR_MIRRORED_MODE=9;r.TEXTURE_FILTERING_QUALITY_OFFLINE=4096;r.TEXTURE_FILTERING_QUALITY_HIGH=64;r.TEXTURE_FILTERING_QUALITY_MEDIUM=16;r.TEXTURE_FILTERING_QUALITY_LOW=8;r.SCALEMODE_FLOOR=1;r.SCALEMODE_NEAREST=2;r.SCALEMODE_CEILING=3;r.MATERIAL_TextureDirtyFlag=1;r.MATERIAL_LightDirtyFlag=2;r.MATERIAL_FresnelDirtyFlag=4;r.MATERIAL_AttributesDirtyFlag=8;r.MATERIAL_MiscDirtyFlag=16;r.MATERIAL_PrePassDirtyFlag=32;r.MATERIAL_ImageProcessingDirtyFlag=64;r.MATERIAL_AllDirtyFlag=127;r.MATERIAL_TriangleFillMode=0;r.MATERIAL_WireFrameFillMode=1;r.MATERIAL_PointFillMode=2;r.MATERIAL_PointListDrawMode=3;r.MATERIAL_LineListDrawMode=4;r.MATERIAL_LineLoopDrawMode=5;r.MATERIAL_LineStripDrawMode=6;r.MATERIAL_TriangleStripDrawMode=7;r.MATERIAL_TriangleFanDrawMode=8;r.MATERIAL_ClockWiseSideOrientation=0;r.MATERIAL_CounterClockWiseSideOrientation=1;r.ACTION_NothingTrigger=0;r.ACTION_OnPickTrigger=1;r.ACTION_OnLeftPickTrigger=2;r.ACTION_OnRightPickTrigger=3;r.ACTION_OnCenterPickTrigger=4;r.ACTION_OnPickDownTrigger=5;r.ACTION_OnDoublePickTrigger=6;r.ACTION_OnPickUpTrigger=7;r.ACTION_OnPickOutTrigger=16;r.ACTION_OnLongPressTrigger=8;r.ACTION_OnPointerOverTrigger=9;r.ACTION_OnPointerOutTrigger=10;r.ACTION_OnEveryFrameTrigger=11;r.ACTION_OnIntersectionEnterTrigger=12;r.ACTION_OnIntersectionExitTrigger=13;r.ACTION_OnKeyDownTrigger=14;r.ACTION_OnKeyUpTrigger=15;r.PARTICLES_BILLBOARDMODE_Y=2;r.PARTICLES_BILLBOARDMODE_ALL=7;r.PARTICLES_BILLBOARDMODE_STRETCHED=8;r.PARTICLES_BILLBOARDMODE_STRETCHED_LOCAL=9;r.MESHES_CULLINGSTRATEGY_STANDARD=0;r.MESHES_CULLINGSTRATEGY_BOUNDINGSPHERE_ONLY=1;r.MESHES_CULLINGSTRATEGY_OPTIMISTIC_INCLUSION=2;r.MESHES_CULLINGSTRATEGY_OPTIMISTIC_INCLUSION_THEN_BSPHERE_ONLY=3;r.SCENELOADER_NO_LOGGING=0;r.SCENELOADER_MINIMAL_LOGGING=1;r.SCENELOADER_SUMMARY_LOGGING=2;r.SCENELOADER_DETAILED_LOGGING=3;r.PREPASS_IRRADIANCE_TEXTURE_TYPE=0;r.PREPASS_POSITION_TEXTURE_TYPE=1;r.PREPASS_VELOCITY_TEXTURE_TYPE=2;r.PREPASS_REFLECTIVITY_TEXTURE_TYPE=3;r.PREPASS_COLOR_TEXTURE_TYPE=4;r.PREPASS_DEPTH_TEXTURE_TYPE=5;r.PREPASS_NORMAL_TEXTURE_TYPE=6;r.PREPASS_ALBEDO_SQRT_TEXTURE_TYPE=7;r.PREPASS_WORLD_NORMAL_TEXTURE_TYPE=8;r.PREPASS_LOCAL_POSITION_TEXTURE_TYPE=9;r.PREPASS_SCREENSPACE_DEPTH_TEXTURE_TYPE=10;r.PREPASS_VELOCITY_LINEAR_TEXTURE_TYPE=11;r.PREPASS_ALBEDO_TEXTURE_TYPE=12;r.BUFFER_CREATIONFLAG_READ=1;r.BUFFER_CREATIONFLAG_WRITE=2;r.BUFFER_CREATIONFLAG_READWRITE=3;r.BUFFER_CREATIONFLAG_UNIFORM=4;r.BUFFER_CREATIONFLAG_VERTEX=8;r.BUFFER_CREATIONFLAG_INDEX=16;r.BUFFER_CREATIONFLAG_STORAGE=32;r.BUFFER_CREATIONFLAG_INDIRECT=64;r.RENDERPASS_MAIN=0;r.INPUT_ALT_KEY=18;r.INPUT_CTRL_KEY=17;r.INPUT_META_KEY1=91;r.INPUT_META_KEY2=92;r.INPUT_META_KEY3=93;r.INPUT_SHIFT_KEY=16;r.SNAPSHOTRENDERING_STANDARD=0;r.SNAPSHOTRENDERING_FAST=1;r.PERSPECTIVE_CAMERA=0;r.ORTHOGRAPHIC_CAMERA=1;r.FOVMODE_VERTICAL_FIXED=0;r.FOVMODE_HORIZONTAL_FIXED=1;r.RIG_MODE_NONE=0;r.RIG_MODE_STEREOSCOPIC_ANAGLYPH=10;r.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_PARALLEL=11;r.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_CROSSEYED=12;r.RIG_MODE_STEREOSCOPIC_OVERUNDER=13;r.RIG_MODE_STEREOSCOPIC_INTERLACED=14;r.RIG_MODE_VR=20;r.RIG_MODE_CUSTOM=22;r.MAX_SUPPORTED_UV_SETS=6;r.GL_ALPHA_EQUATION_ADD=32774;r.GL_ALPHA_EQUATION_MIN=32775;r.GL_ALPHA_EQUATION_MAX=32776;r.GL_ALPHA_EQUATION_SUBTRACT=32778;r.GL_ALPHA_EQUATION_REVERSE_SUBTRACT=32779;r.GL_ALPHA_FUNCTION_SRC=768;r.GL_ALPHA_FUNCTION_ONE_MINUS_SRC_COLOR=769;r.GL_ALPHA_FUNCTION_SRC_ALPHA=770;r.GL_ALPHA_FUNCTION_ONE_MINUS_SRC_ALPHA=771;r.GL_ALPHA_FUNCTION_DST_ALPHA=772;r.GL_ALPHA_FUNCTION_ONE_MINUS_DST_ALPHA=773;r.GL_ALPHA_FUNCTION_DST_COLOR=774;r.GL_ALPHA_FUNCTION_ONE_MINUS_DST_COLOR=775;r.GL_ALPHA_FUNCTION_SRC_ALPHA_SATURATED=776;r.GL_ALPHA_FUNCTION_CONSTANT_COLOR=32769;r.GL_ALPHA_FUNCTION_ONE_MINUS_CONSTANT_COLOR=32770;r.GL_ALPHA_FUNCTION_CONSTANT_ALPHA=32771;r.GL_ALPHA_FUNCTION_ONE_MINUS_CONSTANT_ALPHA=32772;r.GL_ALPHA_FUNCTION_SRC1_COLOR=35065;r.GL_ALPHA_FUNCTION_ONE_MINUS_SRC1_COLOR=35066;r.GL_ALPHA_FUNCTION_SRC1_ALPHA=34185;r.GL_ALPHA_FUNCTION_ONE_MINUS_SRC1_ALPHA=35067;r.SnippetUrl="https://snippet.babylonjs.com";r.FOGMODE_NONE=0;r.FOGMODE_EXP=1;r.FOGMODE_EXP2=2;r.FOGMODE_LINEAR=3;r.BYTE=5120;r.UNSIGNED_BYTE=5121;r.SHORT=5122;r.UNSIGNED_SHORT=5123;r.INT=5124;r.UNSIGNED_INT=5125;r.FLOAT=5126;r.PositionKind="position";r.NormalKind="normal";r.TangentKind="tangent";r.UVKind="uv";r.UV2Kind="uv2";r.UV3Kind="uv3";r.UV4Kind="uv4";r.UV5Kind="uv5";r.UV6Kind="uv6";r.ColorKind="color";r.ColorInstanceKind="instanceColor";r.MatricesIndicesKind="matricesIndices";r.MatricesWeightsKind="matricesWeights";r.MatricesIndicesExtraKind="matricesIndicesExtra";r.MatricesWeightsExtraKind="matricesWeightsExtra";r.ANIMATIONTYPE_FLOAT=0;r.ANIMATIONTYPE_VECTOR3=1;r.ANIMATIONTYPE_QUATERNION=2;r.ANIMATIONTYPE_MATRIX=3;r.ANIMATIONTYPE_COLOR3=4;r.ANIMATIONTYPE_COLOR4=7;r.ANIMATIONTYPE_VECTOR2=5;r.ANIMATIONTYPE_SIZE=6;r.ShadowMinZ=0;r.ShadowMaxZ=1e4;class p extends P{constructor(){super(...arguments),this._needProjectionMatrixCompute=!0,this._viewMatrix=R.Identity(),this._projectionMatrix=R.Identity()}_setPosition(t){this._position=t}get position(){return this._position}set position(t){this._setPosition(t)}_setDirection(t){this._direction=t}get direction(){return this._direction}set direction(t){this._setDirection(t)}get shadowMinZ(){return this._shadowMinZ}set shadowMinZ(t){this._shadowMinZ=t,this.forceProjectionMatrixCompute()}get shadowMaxZ(){return this._shadowMaxZ}set shadowMaxZ(t){this._shadowMaxZ=t,this.forceProjectionMatrixCompute()}computeTransformedInformation(){return this.parent&&this.parent.getWorldMatrix?(this.transformedPosition||(this.transformedPosition=a.Zero()),a.TransformCoordinatesToRef(this.position,this.parent.getWorldMatrix(),this.transformedPosition),this.direction&&(this.transformedDirection||(this.transformedDirection=a.Zero()),a.TransformNormalToRef(this.direction,this.parent.getWorldMatrix(),this.transformedDirection)),!0):!1}getDepthScale(){return 50}getShadowDirection(t){return this.transformedDirection?this.transformedDirection:this.direction}getAbsolutePosition(){return this.transformedPosition?this.transformedPosition:this.position}setDirectionToTarget(t){return this.direction=a.Normalize(t.subtract(this.position)),this.direction}getRotation(){this.direction.normalize();const t=a.Cross(this.direction,O.Y),i=a.Cross(t,this.direction);return a.RotationFromAxis(t,i,this.direction)}needCube(){return!1}needProjectionMatrixCompute(){return this._needProjectionMatrixCompute}forceProjectionMatrixCompute(){this._needProjectionMatrixCompute=!0}_initCache(){super._initCache(),this._cache.position=a.Zero()}_isSynchronized(){return!!this._cache.position.equals(this.position)}computeWorldMatrix(t){return!t&&this.isSynchronized()?(this._currentRenderId=this.getScene().getRenderId(),this._worldMatrix):(this._updateCache(),this._cache.position.copyFrom(this.position),this._worldMatrix||(this._worldMatrix=R.Identity()),R.TranslationToRef(this.position.x,this.position.y,this.position.z,this._worldMatrix),this.parent&&this.parent.getWorldMatrix&&(this._worldMatrix.multiplyToRef(this.parent.getWorldMatrix(),this._worldMatrix),this._markSyncedWithParent()),this._worldMatrixDeterminantIsDirty=!0,this._worldMatrix)}getDepthMinZ(t){return this.shadowMinZ!==void 0?this.shadowMinZ:t?.minZ||0}getDepthMaxZ(t){return this.shadowMaxZ!==void 0?this.shadowMaxZ:t?.maxZ||1e4}setShadowProjectionMatrix(t,i,o){return this.customProjectionMatrixBuilder?this.customProjectionMatrixBuilder(i,o,t):this._setDefaultShadowProjectionMatrix(t,i,o),this}_syncParentEnabledState(){super._syncParentEnabledState(),(!this.parent||!this.parent.getWorldMatrix)&&(this.transformedPosition=null,this.transformedDirection=null)}getViewMatrix(t){const i=m.Vector3[0];let o=this.position;this.computeTransformedInformation()&&(o=this.transformedPosition),a.NormalizeToRef(this.getShadowDirection(t),i),Math.abs(a.Dot(i,a.Up()))===1&&(i.z=1e-13);const s=m.Vector3[1];return o.addToRef(i,s),R.LookAtLHToRef(o,s,a.Up(),this._viewMatrix),this._viewMatrix}getProjectionMatrix(t,i){return this.setShadowProjectionMatrix(this._projectionMatrix,t??this._viewMatrix,i??[]),this._projectionMatrix}}c([L()],p.prototype,"position",null);c([L()],p.prototype,"direction",null);c([l()],p.prototype,"shadowMinZ",null);c([l()],p.prototype,"shadowMaxZ",null);S.AddNodeConstructor("Light_Type_2",(e,t)=>()=>new T(e,a.Zero(),a.Zero(),0,0,t));class T extends p{get iesProfileTexture(){return this._iesProfileTexture}set iesProfileTexture(t){this._iesProfileTexture!==t&&(this._iesProfileTexture=t,this._iesProfileTexture&&T._IsTexture(this._iesProfileTexture)&&this._iesProfileTexture.onLoadObservable.addOnce(()=>{this._markMeshesAsLightDirty()}))}get angle(){return this._angle}set angle(t){this._angle=t,this._cosHalfAngle=Math.cos(t*.5),this._projectionTextureProjectionLightDirty=!0,this.forceProjectionMatrixCompute(),this._computeAngleValues()}get innerAngle(){return this._innerAngle}set innerAngle(t){this._innerAngle=t,this._computeAngleValues()}get shadowAngleScale(){return this._shadowAngleScale}set shadowAngleScale(t){this._shadowAngleScale=t,this.forceProjectionMatrixCompute()}get projectionTextureMatrix(){return this._projectionTextureMatrix}get projectionTextureLightNear(){return this._projectionTextureLightNear}set projectionTextureLightNear(t){this._projectionTextureLightNear=t,this._projectionTextureProjectionLightDirty=!0}get projectionTextureLightFar(){return this._projectionTextureLightFar}set projectionTextureLightFar(t){this._projectionTextureLightFar=t,this._projectionTextureProjectionLightDirty=!0}get projectionTextureUpDirection(){return this._projectionTextureUpDirection}set projectionTextureUpDirection(t){this._projectionTextureUpDirection=t,this._projectionTextureProjectionLightDirty=!0}get projectionTexture(){return this._projectionTexture}set projectionTexture(t){this._projectionTexture!==t&&(this._projectionTexture=t,this._projectionTextureDirty=!0,this._projectionTexture&&!this._projectionTexture.isReady()&&(T._IsProceduralTexture(this._projectionTexture)?this._projectionTexture.getEffect().executeWhenCompiled(()=>{this._markMeshesAsLightDirty()}):T._IsTexture(this._projectionTexture)&&this._projectionTexture.onLoadObservable.addOnce(()=>{this._markMeshesAsLightDirty()})))}static _IsProceduralTexture(t){return t.onGeneratedObservable!==void 0}static _IsTexture(t){return t.onLoadObservable!==void 0}get projectionTextureProjectionLightMatrix(){return this._projectionTextureProjectionLightMatrix}set projectionTextureProjectionLightMatrix(t){this._projectionTextureProjectionLightMatrix=t,this._projectionTextureProjectionLightDirty=!1,this._projectionTextureDirty=!0}constructor(t,i,o,s,_,u){super(t,u),this._innerAngle=0,this._iesProfileTexture=null,this._projectionTextureMatrix=R.Zero(),this._projectionTextureLightNear=1e-6,this._projectionTextureLightFar=1e3,this._projectionTextureUpDirection=a.Up(),this._projectionTextureViewLightDirty=!0,this._projectionTextureProjectionLightDirty=!0,this._projectionTextureDirty=!0,this._projectionTextureViewTargetVector=a.Zero(),this._projectionTextureViewLightMatrix=R.Zero(),this._projectionTextureProjectionLightMatrix=R.Zero(),this._projectionTextureScalingMatrix=R.FromValues(.5,0,0,0,0,.5,0,0,0,0,.5,0,.5,.5,.5,1),this.position=i,this.direction=o,this.angle=s,this.exponent=_}getClassName(){return"SpotLight"}getTypeID(){return P.LIGHTTYPEID_SPOTLIGHT}_setDirection(t){super._setDirection(t),this._projectionTextureViewLightDirty=!0}_setPosition(t){super._setPosition(t),this._projectionTextureViewLightDirty=!0}_setDefaultShadowProjectionMatrix(t,i,o){const s=this.getScene().activeCamera;if(!s)return;this._shadowAngleScale=this._shadowAngleScale||1;const _=this._shadowAngleScale*this._angle,u=this.shadowMinZ!==void 0?this.shadowMinZ:s.minZ,h=this.shadowMaxZ!==void 0?this.shadowMaxZ:s.maxZ,g=this.getScene().getEngine().useReverseDepthBuffer;R.PerspectiveFovLHToRef(_,1,g?h:u,g?u:h,t,!0,this._scene.getEngine().isNDCHalfZRange,void 0,g)}_computeProjectionTextureViewLightMatrix(){this._projectionTextureViewLightDirty=!1,this._projectionTextureDirty=!0,this.getAbsolutePosition().addToRef(this.getShadowDirection(),this._projectionTextureViewTargetVector),R.LookAtLHToRef(this.getAbsolutePosition(),this._projectionTextureViewTargetVector,this._projectionTextureUpDirection,this._projectionTextureViewLightMatrix)}_computeProjectionTextureProjectionLightMatrix(){this._projectionTextureProjectionLightDirty=!1,this._projectionTextureDirty=!0;const t=this.projectionTextureLightFar,i=this.projectionTextureLightNear,o=t/(t-i),s=-o*i,_=1/Math.tan(this._angle/2);R.FromValuesToRef(_/1,0,0,0,0,_,0,0,0,0,o,1,0,0,s,0,this._projectionTextureProjectionLightMatrix)}_computeProjectionTextureMatrix(){if(this._projectionTextureDirty=!1,this._projectionTextureViewLightMatrix.multiplyToRef(this._projectionTextureProjectionLightMatrix,this._projectionTextureMatrix),this._projectionTexture instanceof M){const t=this._projectionTexture.uScale/2,i=this._projectionTexture.vScale/2;R.FromValuesToRef(t,0,0,0,0,i,0,0,0,0,.5,0,.5,.5,.5,1,this._projectionTextureScalingMatrix)}this._projectionTextureMatrix.multiplyToRef(this._projectionTextureScalingMatrix,this._projectionTextureMatrix)}_buildUniformLayout(){this._uniformBuffer.addUniform("vLightData",4),this._uniformBuffer.addUniform("vLightDiffuse",4),this._uniformBuffer.addUniform("vLightSpecular",4),this._uniformBuffer.addUniform("vLightDirection",3),this._uniformBuffer.addUniform("vLightFalloff",4),this._uniformBuffer.addUniform("shadowsInfo",3),this._uniformBuffer.addUniform("depthValues",2),this._uniformBuffer.create()}_computeAngleValues(){this._lightAngleScale=1/Math.max(.001,Math.cos(this._innerAngle*.5)-this._cosHalfAngle),this._lightAngleOffset=-this._cosHalfAngle*this._lightAngleScale}transferTexturesToEffect(t,i){return this.projectionTexture&&this.projectionTexture.isReady()&&(this._projectionTextureViewLightDirty&&this._computeProjectionTextureViewLightMatrix(),this._projectionTextureProjectionLightDirty&&this._computeProjectionTextureProjectionLightMatrix(),this._projectionTextureDirty&&this._computeProjectionTextureMatrix(),t.setMatrix("textureProjectionMatrix"+i,this._projectionTextureMatrix),t.setTexture("projectionLightTexture"+i,this.projectionTexture)),this._iesProfileTexture&&this._iesProfileTexture.isReady()&&t.setTexture("iesLightTexture"+i,this._iesProfileTexture),this}transferToEffect(t,i){let o;return this.computeTransformedInformation()?(this._uniformBuffer.updateFloat4("vLightData",this.transformedPosition.x,this.transformedPosition.y,this.transformedPosition.z,this.exponent,i),o=a.Normalize(this.transformedDirection)):(this._uniformBuffer.updateFloat4("vLightData",this.position.x,this.position.y,this.position.z,this.exponent,i),o=a.Normalize(this.direction)),this._uniformBuffer.updateFloat4("vLightDirection",o.x,o.y,o.z,this._cosHalfAngle,i),this._uniformBuffer.updateFloat4("vLightFalloff",this.range,this._inverseSquaredRange,this._lightAngleScale,this._lightAngleOffset,i),this}transferToNodeMaterialEffect(t,i){let o;return this.computeTransformedInformation()?o=a.Normalize(this.transformedDirection):o=a.Normalize(this.direction),this.getScene().useRightHandedSystem?t.setFloat3(i,-o.x,-o.y,-o.z):t.setFloat3(i,o.x,o.y,o.z),this}dispose(){super.dispose(),this._projectionTexture&&this._projectionTexture.dispose(),this._iesProfileTexture&&(this._iesProfileTexture.dispose(),this._iesProfileTexture=null)}getDepthMinZ(t){const i=this._scene.getEngine(),o=this.shadowMinZ!==void 0?this.shadowMinZ:t?.minZ??0;return i.useReverseDepthBuffer&&i.isNDCHalfZRange?o:this._scene.getEngine().isNDCHalfZRange?0:o}getDepthMaxZ(t){const i=this._scene.getEngine(),o=this.shadowMaxZ!==void 0?this.shadowMaxZ:t?.maxZ??1e4;return i.useReverseDepthBuffer&&i.isNDCHalfZRange?0:o}prepareLightSpecificDefines(t,i){t["SPOTLIGHT"+i]=!0,t["PROJECTEDLIGHTTEXTURE"+i]=!!(this.projectionTexture&&this.projectionTexture.isReady()),t["IESLIGHTTEXTURE"+i]=!!(this._iesProfileTexture&&this._iesProfileTexture.isReady())}}c([l()],T.prototype,"angle",null);c([l()],T.prototype,"innerAngle",null);c([l()],T.prototype,"shadowAngleScale",null);c([l()],T.prototype,"exponent",void 0);c([l()],T.prototype,"projectionTextureLightNear",null);c([l()],T.prototype,"projectionTextureLightFar",null);c([l()],T.prototype,"projectionTextureUpDirection",null);c([y("projectedLightTexture")],T.prototype,"_projectionTexture",void 0);d("BABYLON.SpotLight",T);class C{constructor(t,i){this._gltf=t,this._infoTree=i}convert(t){let i=this._gltf,o=this._infoTree,s;if(!t.startsWith("/"))throw new Error("Path must start with a /");const _=t.split("/");if(_.shift(),_[_.length-1].includes(".length")){const g=_[_.length-1].split(".");_.pop(),_.push(...g)}let u=!1;for(const h of _){const g=h==="length";if(g&&!o.__array__)throw new Error(`Path ${t} is invalid`);if(o.__ignoreObjectTree__&&(u=!0),o.__array__&&!g)o=o.__array__;else if(o=o[h],!o)throw new Error(`Path ${t} is invalid`);if(!u){if(i===void 0)throw new Error(`Path ${t} is invalid`);g||(i=i?.[h])}(o.__target__||g)&&(s=i)}return{object:s,info:o}}}const b={length:{type:"number",get:e=>e.length,getTarget:e=>e.map(t=>t._babylonTransformNode),getPropertyName:[()=>"length"]},__array__:{__target__:!0,translation:{type:"Vector3",get:e=>e._babylonTransformNode?.position,set:(e,t)=>t._babylonTransformNode?.position.copyFrom(e),getTarget:e=>e._babylonTransformNode,getPropertyName:[()=>"position"]},rotation:{type:"Quaternion",get:e=>e._babylonTransformNode?.rotationQuaternion,set:(e,t)=>t._babylonTransformNode?.rotationQuaternion?.copyFrom(e),getTarget:e=>e._babylonTransformNode,getPropertyName:[()=>"rotationQuaternion"]},scale:{type:"Vector3",get:e=>e._babylonTransformNode?.scaling,set:(e,t)=>t._babylonTransformNode?.scaling.copyFrom(e),getTarget:e=>e._babylonTransformNode,getPropertyName:[()=>"scaling"]},weights:{length:{type:"number",get:e=>e._numMorphTargets,getTarget:e=>e._babylonTransformNode,getPropertyName:[()=>"influence"]},__array__:{__target__:!0,type:"number",get:(e,t)=>t!==void 0?e._primitiveBabylonMeshes?.[0].morphTargetManager?.getTarget(t).influence:void 0,getTarget:e=>e._babylonTransformNode,getPropertyName:[()=>"influence"]},type:"number[]",get:(e,t)=>[0],getTarget:e=>e._babylonTransformNode,getPropertyName:[()=>"influence"]},matrix:{type:"Matrix",get:e=>R.Compose(e._babylonTransformNode?.scaling,e._babylonTransformNode?.rotationQuaternion,e._babylonTransformNode?.position),getTarget:e=>e._babylonTransformNode,isReadOnly:!0},globalMatrix:{type:"Matrix",get:e=>{const t=R.Identity();let i=e.parent;for(;i&&i.parent;)i=i.parent;const o=e._babylonTransformNode?.position._isDirty||e._babylonTransformNode?.rotationQuaternion?._isDirty||e._babylonTransformNode?.scaling._isDirty;if(i){const s=i._babylonTransformNode?.computeWorldMatrix(!0).invert();s&&e._babylonTransformNode?.computeWorldMatrix(o)?.multiplyToRef(s,t)}else e._babylonTransformNode&&t.copyFrom(e._babylonTransformNode.computeWorldMatrix(o));return t},getTarget:e=>e._babylonTransformNode,isReadOnly:!0},extensions:{EXT_lights_ies:{multiplier:{type:"number",get:e=>e._babylonTransformNode?.getChildren(t=>t instanceof T,!0)[0]?.intensity,getTarget:e=>e._babylonTransformNode?.getChildren(t=>t instanceof T,!0)[0],set:(e,t)=>{if(t._babylonTransformNode){const i=t._babylonTransformNode.getChildren(o=>o instanceof T,!0)[0];i&&(i.intensity=e)}}},color:{type:"Color3",get:e=>e._babylonTransformNode?.getChildren(t=>t instanceof T,!0)[0]?.diffuse,getTarget:e=>e._babylonTransformNode?.getChildren(t=>t instanceof T,!0)[0],set:(e,t)=>{if(t._babylonTransformNode){const i=t._babylonTransformNode.getChildren(o=>o instanceof T,!0)[0];i&&(i.diffuse=e)}}}}}}},D={length:{type:"number",get:e=>e.length,getTarget:e=>e.map(t=>t._babylonAnimationGroup),getPropertyName:[()=>"length"]},__array__:{}},U={length:{type:"number",get:e=>e.length,getTarget:e=>e.map(t=>t.primitives[0]._instanceData?.babylonSourceMesh),getPropertyName:[()=>"length"]},__array__:{}},F={__array__:{__target__:!0,orthographic:{xmag:{componentsCount:2,type:"Vector2",get:e=>new f(e._babylonCamera?.orthoLeft??0,e._babylonCamera?.orthoRight??0),set:(e,t)=>{t._babylonCamera&&(t._babylonCamera.orthoLeft=e.x,t._babylonCamera.orthoRight=e.y)},getTarget:e=>e,getPropertyName:[()=>"orthoLeft",()=>"orthoRight"]},ymag:{componentsCount:2,type:"Vector2",get:e=>new f(e._babylonCamera?.orthoBottom??0,e._babylonCamera?.orthoTop??0),set:(e,t)=>{t._babylonCamera&&(t._babylonCamera.orthoBottom=e.x,t._babylonCamera.orthoTop=e.y)},getTarget:e=>e,getPropertyName:[()=>"orthoBottom",()=>"orthoTop"]},zfar:{type:"number",get:e=>e._babylonCamera?.maxZ,set:(e,t)=>{t._babylonCamera&&(t._babylonCamera.maxZ=e)},getTarget:e=>e,getPropertyName:[()=>"maxZ"]},znear:{type:"number",get:e=>e._babylonCamera?.minZ,set:(e,t)=>{t._babylonCamera&&(t._babylonCamera.minZ=e)},getTarget:e=>e,getPropertyName:[()=>"minZ"]}},perspective:{aspectRatio:{type:"number",get:e=>e._babylonCamera?.getEngine().getAspectRatio(e._babylonCamera),getTarget:e=>e,getPropertyName:[()=>"aspectRatio"],isReadOnly:!0},yfov:{type:"number",get:e=>e._babylonCamera?.fov,set:(e,t)=>{t._babylonCamera&&(t._babylonCamera.fov=e)},getTarget:e=>e,getPropertyName:[()=>"fov"]},zfar:{type:"number",get:e=>e._babylonCamera?.maxZ,set:(e,t)=>{t._babylonCamera&&(t._babylonCamera.maxZ=e)},getTarget:e=>e,getPropertyName:[()=>"maxZ"]},znear:{type:"number",get:e=>e._babylonCamera?.minZ,set:(e,t)=>{t._babylonCamera&&(t._babylonCamera.minZ=e)},getTarget:e=>e,getPropertyName:[()=>"minZ"]}}}},G={__array__:{__target__:!0,emissiveFactor:{type:"Color3",get:(e,t,i)=>n(e,t,i).emissiveColor,set:(e,t,i,o)=>n(t,i,o).emissiveColor.copyFrom(e),getTarget:(e,t,i)=>n(e,t,i),getPropertyName:[()=>"emissiveColor"]},emissiveTexture:{extensions:{KHR_texture_transform:E("emissiveTexture")}},normalTexture:{scale:{type:"number",get:(e,t,i)=>A(e,i,"bumpTexture")?.level,set:(e,t,i,o)=>{const s=A(t,o,"bumpTexture");s&&(s.level=e)},getTarget:(e,t,i)=>n(e,t,i),getPropertyName:[()=>"level"]},extensions:{KHR_texture_transform:E("bumpTexture")}},occlusionTexture:{strength:{type:"number",get:(e,t,i)=>n(e,t,i).ambientTextureStrength,set:(e,t,i,o)=>{const s=n(t,i,o);s&&(s.ambientTextureStrength=e)},getTarget:(e,t,i)=>n(e,t,i),getPropertyName:[()=>"ambientTextureStrength"]},extensions:{KHR_texture_transform:E("ambientTexture")}},pbrMetallicRoughness:{baseColorFactor:{type:"Color4",get:(e,t,i)=>{const o=n(e,t,i);return I.FromColor3(o.albedoColor,o.alpha)},set:(e,t,i,o)=>{const s=n(t,i,o);s.albedoColor.set(e.r,e.g,e.b),s.alpha=e.a},getTarget:(e,t,i)=>n(e,t,i),getPropertyName:[()=>"albedoColor",()=>"alpha"]},baseColorTexture:{extensions:{KHR_texture_transform:E("albedoTexture")}},metallicFactor:{type:"number",get:(e,t,i)=>n(e,t,i).metallic,set:(e,t,i,o)=>{const s=n(t,i,o);s&&(s.metallic=e)},getTarget:(e,t,i)=>n(e,t,i),getPropertyName:[()=>"metallic"]},roughnessFactor:{type:"number",get:(e,t,i)=>n(e,t,i).roughness,set:(e,t,i,o)=>{const s=n(t,i,o);s&&(s.roughness=e)},getTarget:(e,t,i)=>n(e,t,i),getPropertyName:[()=>"roughness"]},metallicRoughnessTexture:{extensions:{KHR_texture_transform:E("metallicTexture")}}},extensions:{KHR_materials_anisotropy:{anisotropyStrength:{type:"number",get:(e,t,i)=>n(e,t,i).anisotropy.intensity,set:(e,t,i,o)=>{n(t,i,o).anisotropy.intensity=e},getTarget:(e,t,i)=>n(e,t,i),getPropertyName:[()=>"anisotropy.intensity"]},anisotropyRotation:{type:"number",get:(e,t,i)=>n(e,t,i).anisotropy.angle,set:(e,t,i,o)=>{n(t,i,o).anisotropy.angle=e},getTarget:(e,t,i)=>n(e,t,i),getPropertyName:[()=>"anisotropy.angle"]},anisotropyTexture:{extensions:{KHR_texture_transform:E("anisotropy","texture")}}},KHR_materials_clearcoat:{clearcoatFactor:{type:"number",get:(e,t,i)=>n(e,t,i).clearCoat.intensity,set:(e,t,i,o)=>{n(t,i,o).clearCoat.intensity=e},getTarget:(e,t,i)=>n(e,t,i),getPropertyName:[()=>"clearCoat.intensity"]},clearcoatRoughnessFactor:{type:"number",get:(e,t,i)=>n(e,t,i).clearCoat.roughness,set:(e,t,i,o)=>{n(t,i,o).clearCoat.roughness=e},getTarget:(e,t,i)=>n(e,t,i),getPropertyName:[()=>"clearCoat.roughness"]},clearcoatTexture:{extensions:{KHR_texture_transform:E("clearCoat","texture")}},clearcoatNormalTexture:{scale:{type:"number",get:(e,t,i)=>n(e,t,i).clearCoat.bumpTexture?.level,getTarget:n,set:(e,t,i,o)=>n(t,i,o).clearCoat.bumpTexture.level=e},extensions:{KHR_texture_transform:E("clearCoat","bumpTexture")}},clearcoatRoughnessTexture:{extensions:{KHR_texture_transform:E("clearCoat","textureRoughness")}}},KHR_materials_dispersion:{dispersion:{type:"number",get:(e,t,i)=>n(e,t,i).subSurface.dispersion,getTarget:n,set:(e,t,i,o)=>n(t,i,o).subSurface.dispersion=e}},KHR_materials_emissive_strength:{emissiveStrength:{type:"number",get:(e,t,i)=>n(e,t,i).emissiveIntensity,getTarget:n,set:(e,t,i,o)=>n(t,i,o).emissiveIntensity=e}},KHR_materials_ior:{ior:{type:"number",get:(e,t,i)=>n(e,t,i).indexOfRefraction,getTarget:n,set:(e,t,i,o)=>n(t,i,o).indexOfRefraction=e}},KHR_materials_iridescence:{iridescenceFactor:{type:"number",get:(e,t,i)=>n(e,t,i).iridescence.intensity,getTarget:n,set:(e,t,i,o)=>n(t,i,o).iridescence.intensity=e},iridescenceIor:{type:"number",get:(e,t,i)=>n(e,t,i).iridescence.indexOfRefraction,getTarget:n,set:(e,t,i,o)=>n(t,i,o).iridescence.indexOfRefraction=e},iridescenceTexture:{extensions:{KHR_texture_transform:E("iridescence","texture")}},iridescenceThicknessMaximum:{type:"number",get:(e,t,i)=>n(e,t,i).iridescence.maximumThickness,getTarget:n,set:(e,t,i,o)=>n(t,i,o).iridescence.maximumThickness=e},iridescenceThicknessMinimum:{type:"number",get:(e,t,i)=>n(e,t,i).iridescence.minimumThickness,getTarget:n,set:(e,t,i,o)=>n(t,i,o).iridescence.minimumThickness=e},iridescenceThicknessTexture:{extensions:{KHR_texture_transform:E("iridescence","thicknessTexture")}}},KHR_materials_sheen:{sheenColorFactor:{type:"Color3",get:(e,t,i)=>n(e,t,i).sheen.color,getTarget:n,set:(e,t,i,o)=>n(t,i,o).sheen.color.copyFrom(e)},sheenColorTexture:{extensions:{KHR_texture_transform:E("sheen","texture")}},sheenRoughnessFactor:{type:"number",get:(e,t,i)=>n(e,t,i).sheen.intensity,getTarget:n,set:(e,t,i,o)=>n(t,i,o).sheen.intensity=e},sheenRoughnessTexture:{extensions:{KHR_texture_transform:E("sheen","thicknessTexture")}}},KHR_materials_specular:{specularFactor:{type:"number",get:(e,t,i)=>n(e,t,i).metallicF0Factor,getTarget:n,set:(e,t,i,o)=>n(t,i,o).metallicF0Factor=e,getPropertyName:[()=>"metallicF0Factor"]},specularColorFactor:{type:"Color3",get:(e,t,i)=>n(e,t,i).metallicReflectanceColor,getTarget:n,set:(e,t,i,o)=>n(t,i,o).metallicReflectanceColor.copyFrom(e),getPropertyName:[()=>"metallicReflectanceColor"]},specularTexture:{extensions:{KHR_texture_transform:E("metallicReflectanceTexture")}},specularColorTexture:{extensions:{KHR_texture_transform:E("reflectanceTexture")}}},KHR_materials_transmission:{transmissionFactor:{type:"number",get:(e,t,i)=>n(e,t,i).subSurface.refractionIntensity,getTarget:n,set:(e,t,i,o)=>n(t,i,o).subSurface.refractionIntensity=e,getPropertyName:[()=>"subSurface.refractionIntensity"]},transmissionTexture:{extensions:{KHR_texture_transform:E("subSurface","refractionIntensityTexture")}}},KHR_materials_diffuse_transmission:{diffuseTransmissionFactor:{type:"number",get:(e,t,i)=>n(e,t,i).subSurface.translucencyIntensity,getTarget:n,set:(e,t,i,o)=>n(t,i,o).subSurface.translucencyIntensity=e},diffuseTransmissionTexture:{extensions:{KHR_texture_transform:E("subSurface","translucencyIntensityTexture")}},diffuseTransmissionColorFactor:{type:"Color3",get:(e,t,i)=>n(e,t,i).subSurface.translucencyColor,getTarget:n,set:(e,t,i,o)=>e&&n(t,i,o).subSurface.translucencyColor?.copyFrom(e)},diffuseTransmissionColorTexture:{extensions:{KHR_texture_transform:E("subSurface","translucencyColorTexture")}}},KHR_materials_volume:{attenuationColor:{type:"Color3",get:(e,t,i)=>n(e,t,i).subSurface.tintColor,getTarget:n,set:(e,t,i,o)=>n(t,i,o).subSurface.tintColor.copyFrom(e)},attenuationDistance:{type:"number",get:(e,t,i)=>n(e,t,i).subSurface.tintColorAtDistance,getTarget:n,set:(e,t,i,o)=>n(t,i,o).subSurface.tintColorAtDistance=e},thicknessFactor:{type:"number",get:(e,t,i)=>n(e,t,i).subSurface.maximumThickness,getTarget:n,set:(e,t,i,o)=>n(t,i,o).subSurface.maximumThickness=e},thicknessTexture:{extensions:{KHR_texture_transform:E("subSurface","thicknessTexture")}}}}}},H={KHR_lights_punctual:{lights:{length:{type:"number",get:e=>e.length,getTarget:e=>e.map(t=>t._babylonLight),getPropertyName:[e=>"length"]},__array__:{__target__:!0,color:{type:"Color3",get:e=>e._babylonLight?.diffuse,set:(e,t)=>t._babylonLight?.diffuse.copyFrom(e),getTarget:e=>e._babylonLight,getPropertyName:[e=>"diffuse"]},intensity:{type:"number",get:e=>e._babylonLight?.intensity,set:(e,t)=>t._babylonLight?t._babylonLight.intensity=e:void 0,getTarget:e=>e._babylonLight,getPropertyName:[e=>"intensity"]},range:{type:"number",get:e=>e._babylonLight?.range,set:(e,t)=>t._babylonLight?t._babylonLight.range=e:void 0,getTarget:e=>e._babylonLight,getPropertyName:[e=>"range"]},spot:{innerConeAngle:{type:"number",get:e=>e._babylonLight?.innerAngle,set:(e,t)=>t._babylonLight?t._babylonLight.innerAngle=e:void 0,getTarget:e=>e._babylonLight,getPropertyName:[e=>"innerConeAngle"]},outerConeAngle:{type:"number",get:e=>e._babylonLight?.angle,set:(e,t)=>t._babylonLight?t._babylonLight.angle=e:void 0,getTarget:e=>e._babylonLight,getPropertyName:[e=>"outerConeAngle"]}}}}},EXT_lights_ies:{lights:{length:{type:"number",get:e=>e.length,getTarget:e=>e.map(t=>t._babylonLight),getPropertyName:[e=>"length"]}}},EXT_lights_image_based:{lights:{length:{type:"number",get:e=>e.length,getTarget:e=>e.map(t=>t._babylonTexture),getPropertyName:[e=>"length"]},__array__:{__target__:!0,intensity:{type:"number",get:e=>e._babylonTexture?.level,set:(e,t)=>{t._babylonTexture&&(t._babylonTexture.level=e)},getTarget:e=>e._babylonTexture},rotation:{type:"Quaternion",get:e=>e._babylonTexture&&x.FromRotationMatrix(e._babylonTexture?.getReflectionTextureMatrix()),set:(e,t)=>{t._babylonTexture&&(t._babylonTexture.getScene()?.useRightHandedSystem||(e=x.Inverse(e)),R.FromQuaternionToRef(e,t._babylonTexture.getReflectionTextureMatrix()))},getTarget:e=>e._babylonTexture}}}}};function A(e,t,i,o){const s=n(e);return o?s[i][o]:s[i]}function n(e,t,i){return e._data?.[i?.fillMode??r.MATERIAL_TriangleFillMode]?.babylonMaterial}function E(e,t){return{offset:{componentsCount:2,type:"Vector2",get:(i,o,s)=>{const _=A(i,s,e,t);return new f(_?.uOffset,_?.vOffset)},getTarget:n,set:(i,o,s,_)=>{const u=A(o,_,e,t);u.uOffset=i.x,u.vOffset=i.y},getPropertyName:[()=>`${e}${t?"."+t:""}.uOffset`,()=>`${e}${t?"."+t:""}.vOffset`]},rotation:{type:"number",get:(i,o,s)=>A(i,s,e,t)?.wAng,getTarget:n,set:(i,o,s,_)=>A(o,_,e,t).wAng=i,getPropertyName:[()=>`${e}${t?"."+t:""}.wAng`]},scale:{componentsCount:2,type:"Vector2",get:(i,o,s)=>{const _=A(i,s,e,t);return new f(_?.uScale,_?.vScale)},getTarget:n,set:(i,o,s,_)=>{const u=A(o,_,e,t);u.uScale=i.x,u.vScale=i.y},getPropertyName:[()=>`${e}${t?"."+t:""}.uScale`,()=>`${e}${t?"."+t:""}.vScale`]}}}const N={cameras:F,nodes:b,materials:G,extensions:H,animations:D,meshes:U};function j(e){return new C(e,N)}function B(e){const t=e.split("/").map(o=>o.replace(/{}/g,"__array__"));let i=N;for(const o of t)o&&(i=i[o]);if(i&&i.type&&i.get)return i}function w(e,t){const i=e.split("/").map(s=>s.replace(/{}/g,"__array__"));let o=N;for(const s of i)s&&(o=o[s]);o&&o.type&&o.get&&(o.interpolation=t)}function Y(e,t){const i=e.split("/").map(s=>s.replace(/{}/g,"__array__"));let o=N;for(const s of i)if(s){if(!o[s]){if(s==="?"){o.__ignoreObjectTree__=!0;continue}o[s]={},s==="__array__"&&(o[s].__target__=!0)}o=o[s]}Object.assign(o,t)}export{Y as A,r as C,j as G,w as S,T as a,p as b,B as c};
//# sourceMappingURL=objectModelMapping-BR4RdEzn.js.map
