{"version": 3, "file": "assetContainer.w1CRG79i.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Meshes/instancedMesh.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/assetContainer.js"], "sourcesContent": ["import { Matrix, TmpVectors } from \"../Maths/math.vector.js\";\nimport { <PERSON>gger } from \"../Misc/logger.js\";\nimport { AbstractMesh } from \"../Meshes/abstractMesh.js\";\nimport { Mesh } from \"../Meshes/mesh.js\";\nimport { DeepCopier } from \"../Misc/deepCopier.js\";\nimport { TransformNode } from \"./transformNode.js\";\nimport { VertexBuffer } from \"../Buffers/buffer.js\";\nimport { Tools } from \"../Misc/tools.js\";\nimport { RegisterClass } from \"../Misc/typeStore.js\";\nMesh._instancedMeshFactory = (name, mesh) => {\n    const instance = new InstancedMesh(name, mesh);\n    if (mesh.instancedBuffers) {\n        instance.instancedBuffers = {};\n        for (const key in mesh.instancedBuffers) {\n            instance.instancedBuffers[key] = mesh.instancedBuffers[key];\n        }\n    }\n    return instance;\n};\n/**\n * Creates an instance based on a source mesh.\n */\nexport class InstancedMesh extends AbstractMesh {\n    /**\n     * Creates a new InstancedMesh object from the mesh source.\n     * @param name defines the name of the instance\n     * @param source the mesh to create the instance from\n     */\n    constructor(name, source) {\n        super(name, source.getScene());\n        /** @internal */\n        this._indexInSourceMeshInstanceArray = -1;\n        /** @internal */\n        this._distanceToCamera = 0;\n        source.addInstance(this);\n        this._sourceMesh = source;\n        this._unIndexed = source._unIndexed;\n        this.position.copyFrom(source.position);\n        this.rotation.copyFrom(source.rotation);\n        this.scaling.copyFrom(source.scaling);\n        if (source.rotationQuaternion) {\n            this.rotationQuaternion = source.rotationQuaternion.clone();\n        }\n        this.animations = source.animations.slice();\n        for (const range of source.getAnimationRanges()) {\n            if (range != null) {\n                this.createAnimationRange(range.name, range.from, range.to);\n            }\n        }\n        this.infiniteDistance = source.infiniteDistance;\n        this.setPivotMatrix(source.getPivotMatrix());\n        this.refreshBoundingInfo(true, true);\n        this._syncSubMeshes();\n    }\n    /**\n     * @returns the string \"InstancedMesh\".\n     */\n    getClassName() {\n        return \"InstancedMesh\";\n    }\n    /** Gets the list of lights affecting that mesh */\n    get lightSources() {\n        return this._sourceMesh._lightSources;\n    }\n    _resyncLightSources() {\n        // Do nothing as all the work will be done by source mesh\n    }\n    _resyncLightSource() {\n        // Do nothing as all the work will be done by source mesh\n    }\n    _removeLightSource() {\n        // Do nothing as all the work will be done by source mesh\n    }\n    // Methods\n    /**\n     * If the source mesh receives shadows\n     */\n    get receiveShadows() {\n        return this._sourceMesh.receiveShadows;\n    }\n    set receiveShadows(_value) {\n        if (this._sourceMesh?.receiveShadows !== _value) {\n            Tools.Warn(\"Setting receiveShadows on an instanced mesh has no effect\");\n        }\n    }\n    /**\n     * The material of the source mesh\n     */\n    get material() {\n        return this._sourceMesh.material;\n    }\n    set material(_value) {\n        if (this._sourceMesh?.material !== _value) {\n            Tools.Warn(\"Setting material on an instanced mesh has no effect\");\n        }\n    }\n    /**\n     * Visibility of the source mesh\n     */\n    get visibility() {\n        return this._sourceMesh.visibility;\n    }\n    set visibility(_value) {\n        if (this._sourceMesh?.visibility !== _value) {\n            Tools.Warn(\"Setting visibility on an instanced mesh has no effect\");\n        }\n    }\n    /**\n     * Skeleton of the source mesh\n     */\n    get skeleton() {\n        return this._sourceMesh.skeleton;\n    }\n    set skeleton(_value) {\n        if (this._sourceMesh?.skeleton !== _value) {\n            Tools.Warn(\"Setting skeleton on an instanced mesh has no effect\");\n        }\n    }\n    /**\n     * Rendering ground id of the source mesh\n     */\n    get renderingGroupId() {\n        return this._sourceMesh.renderingGroupId;\n    }\n    set renderingGroupId(value) {\n        if (!this._sourceMesh || value === this._sourceMesh.renderingGroupId) {\n            return;\n        }\n        //no-op with warning\n        Logger.Warn(\"Note - setting renderingGroupId of an instanced mesh has no effect on the scene\");\n    }\n    /**\n     * @returns the total number of vertices (integer).\n     */\n    getTotalVertices() {\n        return this._sourceMesh ? this._sourceMesh.getTotalVertices() : 0;\n    }\n    /**\n     * Returns a positive integer : the total number of indices in this mesh geometry.\n     * @returns the number of indices or zero if the mesh has no geometry.\n     */\n    getTotalIndices() {\n        return this._sourceMesh.getTotalIndices();\n    }\n    /**\n     * The source mesh of the instance\n     */\n    get sourceMesh() {\n        return this._sourceMesh;\n    }\n    /**\n     * Gets the mesh internal Geometry object\n     */\n    get geometry() {\n        return this._sourceMesh._geometry;\n    }\n    /**\n     * Creates a new InstancedMesh object from the mesh model.\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/copies/instances\n     * @param name defines the name of the new instance\n     * @returns a new InstancedMesh\n     */\n    createInstance(name) {\n        return this._sourceMesh.createInstance(name);\n    }\n    /**\n     * Is this node ready to be used/rendered\n     * @param completeCheck defines if a complete check (including materials and lights) has to be done (false by default)\n     * @returns {boolean} is it ready\n     */\n    isReady(completeCheck = false) {\n        return this._sourceMesh.isReady(completeCheck, true);\n    }\n    /**\n     * Returns an array of integers or a typed array (Int32Array, Uint32Array, Uint16Array) populated with the mesh indices.\n     * @param kind kind of verticies to retrieve (eg. positions, normals, uvs, etc.)\n     * @param copyWhenShared If true (default false) and and if the mesh geometry is shared among some other meshes, the returned array is a copy of the internal one.\n     * @param forceCopy defines a boolean forcing the copy of the buffer no matter what the value of copyWhenShared is\n     * @returns a float array or a Float32Array of the requested kind of data : positions, normals, uvs, etc.\n     */\n    getVerticesData(kind, copyWhenShared, forceCopy) {\n        return this._sourceMesh.getVerticesData(kind, copyWhenShared, forceCopy);\n    }\n    copyVerticesData(kind, vertexData) {\n        this._sourceMesh.copyVerticesData(kind, vertexData);\n    }\n    /**\n     * Sets the vertex data of the mesh geometry for the requested `kind`.\n     * If the mesh has no geometry, a new Geometry object is set to the mesh and then passed this vertex data.\n     * The `data` are either a numeric array either a Float32Array.\n     * The parameter `updatable` is passed as is to the underlying Geometry object constructor (if initially none) or updater.\n     * The parameter `stride` is an optional positive integer, it is usually automatically deducted from the `kind` (3 for positions or normals, 2 for UV, etc).\n     * Note that a new underlying VertexBuffer object is created each call.\n     * If the `kind` is the `PositionKind`, the mesh BoundingInfo is renewed, so the bounding box and sphere, and the mesh World Matrix is recomputed.\n     *\n     * Possible `kind` values :\n     * - VertexBuffer.PositionKind\n     * - VertexBuffer.UVKind\n     * - VertexBuffer.UV2Kind\n     * - VertexBuffer.UV3Kind\n     * - VertexBuffer.UV4Kind\n     * - VertexBuffer.UV5Kind\n     * - VertexBuffer.UV6Kind\n     * - VertexBuffer.ColorKind\n     * - VertexBuffer.MatricesIndicesKind\n     * - VertexBuffer.MatricesIndicesExtraKind\n     * - VertexBuffer.MatricesWeightsKind\n     * - VertexBuffer.MatricesWeightsExtraKind\n     *\n     * Returns the Mesh.\n     * @param kind defines vertex data kind\n     * @param data defines the data source\n     * @param updatable defines if the data must be flagged as updatable (false as default)\n     * @param stride defines the vertex stride (optional)\n     * @returns the current mesh\n     */\n    setVerticesData(kind, data, updatable, stride) {\n        if (this.sourceMesh) {\n            this.sourceMesh.setVerticesData(kind, data, updatable, stride);\n        }\n        return this.sourceMesh;\n    }\n    /**\n     * Updates the existing vertex data of the mesh geometry for the requested `kind`.\n     * If the mesh has no geometry, it is simply returned as it is.\n     * The `data` are either a numeric array either a Float32Array.\n     * No new underlying VertexBuffer object is created.\n     * If the `kind` is the `PositionKind` and if `updateExtends` is true, the mesh BoundingInfo is renewed, so the bounding box and sphere, and the mesh World Matrix is recomputed.\n     * If the parameter `makeItUnique` is true, a new global geometry is created from this positions and is set to the mesh.\n     *\n     * Possible `kind` values :\n     * - VertexBuffer.PositionKind\n     * - VertexBuffer.UVKind\n     * - VertexBuffer.UV2Kind\n     * - VertexBuffer.UV3Kind\n     * - VertexBuffer.UV4Kind\n     * - VertexBuffer.UV5Kind\n     * - VertexBuffer.UV6Kind\n     * - VertexBuffer.ColorKind\n     * - VertexBuffer.MatricesIndicesKind\n     * - VertexBuffer.MatricesIndicesExtraKind\n     * - VertexBuffer.MatricesWeightsKind\n     * - VertexBuffer.MatricesWeightsExtraKind\n     *\n     * Returns the Mesh.\n     * @param kind defines vertex data kind\n     * @param data defines the data source\n     * @param updateExtends defines if extends info of the mesh must be updated (can be null). This is mostly useful for \"position\" kind\n     * @param makeItUnique defines it the updated vertex buffer must be flagged as unique (false by default)\n     * @returns the source mesh\n     */\n    updateVerticesData(kind, data, updateExtends, makeItUnique) {\n        if (this.sourceMesh) {\n            this.sourceMesh.updateVerticesData(kind, data, updateExtends, makeItUnique);\n        }\n        return this.sourceMesh;\n    }\n    /**\n     * Sets the mesh indices.\n     * Expects an array populated with integers or a typed array (Int32Array, Uint32Array, Uint16Array).\n     * If the mesh has no geometry, a new Geometry object is created and set to the mesh.\n     * This method creates a new index buffer each call.\n     * Returns the Mesh.\n     * @param indices the source data\n     * @param totalVertices defines the total number of vertices referenced by indices (could be null)\n     * @returns source mesh\n     */\n    setIndices(indices, totalVertices = null) {\n        if (this.sourceMesh) {\n            this.sourceMesh.setIndices(indices, totalVertices);\n        }\n        return this.sourceMesh;\n    }\n    /**\n     * Boolean : True if the mesh owns the requested kind of data.\n     * @param kind defines which buffer to check (positions, indices, normals, etc). Possible `kind` values :\n     * - VertexBuffer.PositionKind\n     * - VertexBuffer.UVKind\n     * - VertexBuffer.UV2Kind\n     * - VertexBuffer.UV3Kind\n     * - VertexBuffer.UV4Kind\n     * - VertexBuffer.UV5Kind\n     * - VertexBuffer.UV6Kind\n     * - VertexBuffer.ColorKind\n     * - VertexBuffer.MatricesIndicesKind\n     * - VertexBuffer.MatricesIndicesExtraKind\n     * - VertexBuffer.MatricesWeightsKind\n     * - VertexBuffer.MatricesWeightsExtraKind\n     * @returns true if data kind is present\n     */\n    isVerticesDataPresent(kind) {\n        return this._sourceMesh.isVerticesDataPresent(kind);\n    }\n    /**\n     * @returns an array of indices (IndicesArray).\n     */\n    getIndices() {\n        return this._sourceMesh.getIndices();\n    }\n    get _positions() {\n        return this._sourceMesh._positions;\n    }\n    refreshBoundingInfo(applySkeletonOrOptions = false, applyMorph = false) {\n        if (this.hasBoundingInfo && this.getBoundingInfo().isLocked) {\n            return this;\n        }\n        let options;\n        if (typeof applySkeletonOrOptions === \"object\") {\n            options = applySkeletonOrOptions;\n        }\n        else {\n            options = {\n                applySkeleton: applySkeletonOrOptions,\n                applyMorph: applyMorph,\n            };\n        }\n        const bias = this._sourceMesh.geometry ? this._sourceMesh.geometry.boundingBias : null;\n        this._refreshBoundingInfo(this._sourceMesh._getData(options, null, VertexBuffer.PositionKind), bias);\n        return this;\n    }\n    /** @internal */\n    _preActivate() {\n        if (this._currentLOD) {\n            this._currentLOD._preActivate();\n        }\n        return this;\n    }\n    /**\n     * @internal\n     */\n    _activate(renderId, intermediateRendering) {\n        super._activate(renderId, intermediateRendering);\n        if (!this._sourceMesh.subMeshes) {\n            Logger.Warn(\"Instances should only be created for meshes with geometry.\");\n        }\n        if (this._currentLOD) {\n            const differentSign = this._currentLOD._getWorldMatrixDeterminant() >= 0 !== this._getWorldMatrixDeterminant() >= 0;\n            if (differentSign) {\n                this._internalAbstractMeshDataInfo._actAsRegularMesh = true;\n                return true;\n            }\n            this._internalAbstractMeshDataInfo._actAsRegularMesh = false;\n            this._currentLOD._registerInstanceForRenderId(this, renderId);\n            if (intermediateRendering) {\n                if (!this._currentLOD._internalAbstractMeshDataInfo._isActiveIntermediate) {\n                    this._currentLOD._internalAbstractMeshDataInfo._onlyForInstancesIntermediate = true;\n                    return true;\n                }\n            }\n            else {\n                if (!this._currentLOD._internalAbstractMeshDataInfo._isActive) {\n                    this._currentLOD._internalAbstractMeshDataInfo._onlyForInstances = true;\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n    /** @internal */\n    _postActivate() {\n        if (this._sourceMesh.edgesShareWithInstances && this._sourceMesh._edgesRenderer && this._sourceMesh._edgesRenderer.isEnabled && this._sourceMesh._renderingGroup) {\n            // we are using the edge renderer of the source mesh\n            this._sourceMesh._renderingGroup._edgesRenderers.pushNoDuplicate(this._sourceMesh._edgesRenderer);\n            this._sourceMesh._edgesRenderer.customInstances.push(this.getWorldMatrix());\n        }\n        else if (this._edgesRenderer && this._edgesRenderer.isEnabled && this._sourceMesh._renderingGroup) {\n            // we are using the edge renderer defined for this instance\n            this._sourceMesh._renderingGroup._edgesRenderers.push(this._edgesRenderer);\n        }\n    }\n    getWorldMatrix() {\n        if (this._currentLOD && this._currentLOD.billboardMode !== TransformNode.BILLBOARDMODE_NONE && this._currentLOD._masterMesh !== this) {\n            if (!this._billboardWorldMatrix) {\n                this._billboardWorldMatrix = new Matrix();\n            }\n            const tempMaster = this._currentLOD._masterMesh;\n            this._currentLOD._masterMesh = this;\n            TmpVectors.Vector3[7].copyFrom(this._currentLOD.position);\n            this._currentLOD.position.set(0, 0, 0);\n            this._billboardWorldMatrix.copyFrom(this._currentLOD.computeWorldMatrix(true));\n            this._currentLOD.position.copyFrom(TmpVectors.Vector3[7]);\n            this._currentLOD._masterMesh = tempMaster;\n            return this._billboardWorldMatrix;\n        }\n        return super.getWorldMatrix();\n    }\n    get isAnInstance() {\n        return true;\n    }\n    /**\n     * Returns the current associated LOD AbstractMesh.\n     * @param camera defines the camera to use to pick the LOD level\n     * @returns a Mesh or `null` if no LOD is associated with the AbstractMesh\n     */\n    getLOD(camera) {\n        if (!camera) {\n            return this;\n        }\n        const sourceMeshLODLevels = this.sourceMesh.getLODLevels();\n        if (!sourceMeshLODLevels || sourceMeshLODLevels.length === 0) {\n            this._currentLOD = this.sourceMesh;\n        }\n        else {\n            const boundingInfo = this.getBoundingInfo();\n            this._currentLOD = this.sourceMesh.getLOD(camera, boundingInfo.boundingSphere);\n        }\n        return this._currentLOD;\n    }\n    /**\n     * @internal\n     */\n    _preActivateForIntermediateRendering(renderId) {\n        return this.sourceMesh._preActivateForIntermediateRendering(renderId);\n    }\n    /** @internal */\n    _syncSubMeshes() {\n        this.releaseSubMeshes();\n        if (this._sourceMesh.subMeshes) {\n            for (let index = 0; index < this._sourceMesh.subMeshes.length; index++) {\n                this._sourceMesh.subMeshes[index].clone(this, this._sourceMesh);\n            }\n        }\n        return this;\n    }\n    /** @internal */\n    _generatePointsArray() {\n        return this._sourceMesh._generatePointsArray();\n    }\n    /** @internal */\n    _updateBoundingInfo() {\n        if (this.hasBoundingInfo) {\n            this.getBoundingInfo().update(this.worldMatrixFromCache);\n        }\n        else {\n            this.buildBoundingInfo(this.absolutePosition, this.absolutePosition, this.worldMatrixFromCache);\n        }\n        this._updateSubMeshesBoundingInfo(this.worldMatrixFromCache);\n        return this;\n    }\n    /**\n     * Creates a new InstancedMesh from the current mesh.\n     *\n     * Returns the clone.\n     * @param name the cloned mesh name\n     * @param newParent the optional Node to parent the clone to.\n     * @param doNotCloneChildren if `true` the model children aren't cloned.\n     * @param newSourceMesh if set this mesh will be used as the source mesh instead of ths instance's one\n     * @returns the clone\n     */\n    clone(name, newParent = null, doNotCloneChildren, newSourceMesh) {\n        const result = (newSourceMesh || this._sourceMesh).createInstance(name);\n        // Deep copy\n        DeepCopier.DeepCopy(this, result, [\n            \"name\",\n            \"subMeshes\",\n            \"uniqueId\",\n            \"parent\",\n            \"lightSources\",\n            \"receiveShadows\",\n            \"material\",\n            \"visibility\",\n            \"skeleton\",\n            \"sourceMesh\",\n            \"isAnInstance\",\n            \"facetNb\",\n            \"isFacetDataEnabled\",\n            \"isBlocked\",\n            \"useBones\",\n            \"hasInstances\",\n            \"collider\",\n            \"edgesRenderer\",\n            \"forward\",\n            \"up\",\n            \"right\",\n            \"absolutePosition\",\n            \"absoluteScaling\",\n            \"absoluteRotationQuaternion\",\n            \"isWorldMatrixFrozen\",\n            \"nonUniformScaling\",\n            \"behaviors\",\n            \"worldMatrixFromCache\",\n            \"hasThinInstances\",\n            \"hasBoundingInfo\",\n            \"geometry\",\n        ], []);\n        // Bounding info\n        this.refreshBoundingInfo();\n        // Parent\n        if (newParent) {\n            result.parent = newParent;\n        }\n        if (!doNotCloneChildren) {\n            // Children\n            for (let index = 0; index < this.getScene().meshes.length; index++) {\n                const mesh = this.getScene().meshes[index];\n                if (mesh.parent === this) {\n                    mesh.clone(mesh.name, result);\n                }\n            }\n        }\n        result.computeWorldMatrix(true);\n        this.onClonedObservable.notifyObservers(result);\n        return result;\n    }\n    /**\n     * Disposes the InstancedMesh.\n     * Returns nothing.\n     * @param doNotRecurse Set to true to not recurse into each children (recurse into each children by default)\n     * @param disposeMaterialAndTextures Set to true to also dispose referenced materials and textures (false by default)\n     */\n    dispose(doNotRecurse, disposeMaterialAndTextures = false) {\n        // Remove from mesh\n        this._sourceMesh.removeInstance(this);\n        super.dispose(doNotRecurse, disposeMaterialAndTextures);\n    }\n    /**\n     * @internal\n     */\n    _serializeAsParent(serializationObject) {\n        super._serializeAsParent(serializationObject);\n        serializationObject.parentId = this._sourceMesh.uniqueId;\n        serializationObject.parentInstanceIndex = this._indexInSourceMeshInstanceArray;\n    }\n    /**\n     * Instantiate (when possible) or clone that node with its hierarchy\n     * @param newParent defines the new parent to use for the instance (or clone)\n     * @param options defines options to configure how copy is done\n     * @param options.doNotInstantiate defines if the model must be instantiated or just cloned\n     * @param options.newSourcedMesh newSourcedMesh the new source mesh for the instance (or clone)\n     * @param onNewNodeCreated defines an option callback to call when a clone or an instance is created\n     * @returns an instance (or a clone) of the current node with its hierarchy\n     */\n    instantiateHierarchy(newParent = null, options, onNewNodeCreated) {\n        const clone = this.clone(\"Clone of \" + (this.name || this.id), newParent || this.parent, true, options && options.newSourcedMesh);\n        if (clone) {\n            if (onNewNodeCreated) {\n                onNewNodeCreated(this, clone);\n            }\n        }\n        for (const child of this.getChildTransformNodes(true)) {\n            child.instantiateHierarchy(clone, options, onNewNodeCreated);\n        }\n        return clone;\n    }\n}\nMesh.prototype.registerInstancedBuffer = function (kind, stride) {\n    // Remove existing one\n    this._userInstancedBuffersStorage?.vertexBuffers[kind]?.dispose();\n    // Creates the instancedBuffer field if not present\n    if (!this.instancedBuffers) {\n        this.instancedBuffers = {};\n        for (const instance of this.instances) {\n            instance.instancedBuffers = {};\n        }\n    }\n    if (!this._userInstancedBuffersStorage) {\n        this._userInstancedBuffersStorage = {\n            data: {},\n            vertexBuffers: {},\n            strides: {},\n            sizes: {},\n            vertexArrayObjects: this.getEngine().getCaps().vertexArrayObject ? {} : undefined,\n        };\n    }\n    // Creates an empty property for this kind\n    this.instancedBuffers[kind] = null;\n    this._userInstancedBuffersStorage.strides[kind] = stride;\n    this._userInstancedBuffersStorage.sizes[kind] = stride * 32; // Initial size\n    this._userInstancedBuffersStorage.data[kind] = new Float32Array(this._userInstancedBuffersStorage.sizes[kind]);\n    this._userInstancedBuffersStorage.vertexBuffers[kind] = new VertexBuffer(this.getEngine(), this._userInstancedBuffersStorage.data[kind], kind, true, false, stride, true);\n    for (const instance of this.instances) {\n        instance.instancedBuffers[kind] = null;\n    }\n    this._invalidateInstanceVertexArrayObject();\n    this._markSubMeshesAsAttributesDirty();\n};\nMesh.prototype._processInstancedBuffers = function (visibleInstances, renderSelf) {\n    const instanceCount = visibleInstances ? visibleInstances.length : 0;\n    for (const kind in this.instancedBuffers) {\n        let size = this._userInstancedBuffersStorage.sizes[kind];\n        const stride = this._userInstancedBuffersStorage.strides[kind];\n        // Resize if required\n        const expectedSize = (instanceCount + 1) * stride;\n        while (size < expectedSize) {\n            size *= 2;\n        }\n        if (this._userInstancedBuffersStorage.data[kind].length != size) {\n            this._userInstancedBuffersStorage.data[kind] = new Float32Array(size);\n            this._userInstancedBuffersStorage.sizes[kind] = size;\n            if (this._userInstancedBuffersStorage.vertexBuffers[kind]) {\n                this._userInstancedBuffersStorage.vertexBuffers[kind].dispose();\n                this._userInstancedBuffersStorage.vertexBuffers[kind] = null;\n            }\n        }\n        const data = this._userInstancedBuffersStorage.data[kind];\n        // Update data buffer\n        let offset = 0;\n        if (renderSelf) {\n            const value = this.instancedBuffers[kind];\n            if (value.toArray) {\n                value.toArray(data, offset);\n            }\n            else if (value.copyToArray) {\n                value.copyToArray(data, offset);\n            }\n            else {\n                data[offset] = value;\n            }\n            offset += stride;\n        }\n        for (let instanceIndex = 0; instanceIndex < instanceCount; instanceIndex++) {\n            const instance = visibleInstances[instanceIndex];\n            const value = instance.instancedBuffers[kind];\n            if (value.toArray) {\n                value.toArray(data, offset);\n            }\n            else if (value.copyToArray) {\n                value.copyToArray(data, offset);\n            }\n            else {\n                data[offset] = value;\n            }\n            offset += stride;\n        }\n        // Update vertex buffer\n        if (!this._userInstancedBuffersStorage.vertexBuffers[kind]) {\n            this._userInstancedBuffersStorage.vertexBuffers[kind] = new VertexBuffer(this.getEngine(), this._userInstancedBuffersStorage.data[kind], kind, true, false, stride, true);\n            this._invalidateInstanceVertexArrayObject();\n        }\n        else {\n            this._userInstancedBuffersStorage.vertexBuffers[kind].updateDirectly(data, 0);\n        }\n    }\n};\nMesh.prototype._invalidateInstanceVertexArrayObject = function () {\n    if (!this._userInstancedBuffersStorage || this._userInstancedBuffersStorage.vertexArrayObjects === undefined) {\n        return;\n    }\n    for (const kind in this._userInstancedBuffersStorage.vertexArrayObjects) {\n        this.getEngine().releaseVertexArrayObject(this._userInstancedBuffersStorage.vertexArrayObjects[kind]);\n    }\n    this._userInstancedBuffersStorage.vertexArrayObjects = {};\n};\nMesh.prototype._disposeInstanceSpecificData = function () {\n    if (this._instanceDataStorage.instancesBuffer) {\n        this._instanceDataStorage.instancesBuffer.dispose();\n        this._instanceDataStorage.instancesBuffer = null;\n    }\n    while (this.instances.length) {\n        this.instances[0].dispose();\n    }\n    for (const kind in this.instancedBuffers) {\n        if (this._userInstancedBuffersStorage.vertexBuffers[kind]) {\n            this._userInstancedBuffersStorage.vertexBuffers[kind].dispose();\n        }\n    }\n    this._invalidateInstanceVertexArrayObject();\n    this.instancedBuffers = {};\n};\n// Register Class Name\nRegisterClass(\"BABYLON.InstancedMesh\", InstancedMesh);\n//# sourceMappingURL=instancedMesh.js.map", "import { Mesh } from \"./Meshes/mesh.js\";\nimport { TransformNode } from \"./Meshes/transformNode.js\";\nimport { AbstractMesh } from \"./Meshes/abstractMesh.js\";\nimport { Logger } from \"./Misc/logger.js\";\nimport { EngineStore } from \"./Engines/engineStore.js\";\nimport { InstancedMesh } from \"./Meshes/instancedMesh.js\";\nimport { Light } from \"./Lights/light.js\";\nimport { Camera } from \"./Cameras/camera.js\";\nimport { Tools } from \"./Misc/tools.js\";\nimport { Tags } from \"./Misc/tags.js\";\n/**\n * Root class for AssetContainer and KeepAssets\n */\nexport class AbstractAssetContainer {\n    constructor() {\n        /**\n         * Gets the list of root nodes (ie. nodes with no parent)\n         */\n        this.rootNodes = [];\n        /** All of the cameras added to this scene\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras\n         */\n        this.cameras = [];\n        /**\n         * All of the lights added to this scene\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\n         */\n        this.lights = [];\n        /**\n         * All of the (abstract) meshes added to this scene\n         */\n        this.meshes = [];\n        /**\n         * The list of skeletons added to the scene\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/bonesSkeletons\n         */\n        this.skeletons = [];\n        /**\n         * All of the particle systems added to this scene\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/particles/particle_system/particle_system_intro\n         */\n        this.particleSystems = [];\n        /**\n         * Gets a list of Animations associated with the scene\n         */\n        this.animations = [];\n        /**\n         * All of the animation groups added to this scene\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/groupAnimations\n         */\n        this.animationGroups = [];\n        /**\n         * All of the multi-materials added to this scene\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/multiMaterials\n         */\n        this.multiMaterials = [];\n        /**\n         * All of the materials added to this scene\n         * In the context of a Scene, it is not supposed to be modified manually.\n         * Any addition or removal should be done using the addMaterial and removeMaterial Scene methods.\n         * Note also that the order of the Material within the array is not significant and might change.\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/materials_introduction\n         */\n        this.materials = [];\n        /**\n         * The list of morph target managers added to the scene\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/dynamicMeshMorph\n         */\n        this.morphTargetManagers = [];\n        /**\n         * The list of geometries used in the scene.\n         */\n        this.geometries = [];\n        /**\n         * All of the transform nodes added to this scene\n         * In the context of a Scene, it is not supposed to be modified manually.\n         * Any addition or removal should be done using the addTransformNode and removeTransformNode Scene methods.\n         * Note also that the order of the TransformNode within the array is not significant and might change.\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/transforms/parent_pivot/transform_node\n         */\n        this.transformNodes = [];\n        /**\n         * ActionManagers available on the scene.\n         * @deprecated\n         */\n        this.actionManagers = [];\n        /**\n         * Textures to keep.\n         */\n        this.textures = [];\n        /** @internal */\n        this._environmentTexture = null;\n        /**\n         * The list of postprocesses added to the scene\n         */\n        this.postProcesses = [];\n        /**\n         * The list of sounds\n         */\n        this.sounds = null;\n        /**\n         * The list of effect layers added to the scene\n         */\n        this.effectLayers = [];\n        /**\n         * The list of layers added to the scene\n         */\n        this.layers = [];\n        /**\n         * The list of reflection probes added to the scene\n         */\n        this.reflectionProbes = [];\n    }\n    /**\n     * Texture used in all pbr material as the reflection texture.\n     * As in the majority of the scene they are the same (exception for multi room and so on),\n     * this is easier to reference from here than from all the materials.\n     */\n    get environmentTexture() {\n        return this._environmentTexture;\n    }\n    set environmentTexture(value) {\n        this._environmentTexture = value;\n    }\n    /**\n     * @returns all meshes, lights, cameras, transformNodes and bones\n     */\n    getNodes() {\n        let nodes = [];\n        nodes = nodes.concat(this.meshes);\n        nodes = nodes.concat(this.lights);\n        nodes = nodes.concat(this.cameras);\n        nodes = nodes.concat(this.transformNodes); // dummies\n        this.skeletons.forEach((skeleton) => (nodes = nodes.concat(skeleton.bones)));\n        return nodes;\n    }\n}\n/**\n * Set of assets to keep when moving a scene into an asset container.\n */\nexport class KeepAssets extends AbstractAssetContainer {\n}\n/**\n * Class used to store the output of the AssetContainer.instantiateAllMeshesToScene function\n */\nexport class InstantiatedEntries {\n    constructor() {\n        /**\n         * List of new root nodes (eg. nodes with no parent)\n         */\n        this.rootNodes = [];\n        /**\n         * List of new skeletons\n         */\n        this.skeletons = [];\n        /**\n         * List of new animation groups\n         */\n        this.animationGroups = [];\n    }\n    /**\n     * Disposes the instantiated entries from the scene\n     */\n    dispose() {\n        this.rootNodes.slice(0).forEach((o) => {\n            o.dispose();\n        });\n        this.rootNodes.length = 0;\n        this.skeletons.slice(0).forEach((o) => {\n            o.dispose();\n        });\n        this.skeletons.length = 0;\n        this.animationGroups.slice(0).forEach((o) => {\n            o.dispose();\n        });\n        this.animationGroups.length = 0;\n    }\n}\n/**\n * Container with a set of assets that can be added or removed from a scene.\n */\nexport class AssetContainer extends AbstractAssetContainer {\n    /**\n     * Instantiates an AssetContainer.\n     * @param scene The scene the AssetContainer belongs to.\n     */\n    constructor(scene) {\n        super();\n        this._wasAddedToScene = false;\n        scene = scene || EngineStore.LastCreatedScene;\n        if (!scene) {\n            return;\n        }\n        this.scene = scene;\n        this[\"proceduralTextures\"] = [];\n        scene.onDisposeObservable.add(() => {\n            if (!this._wasAddedToScene) {\n                this.dispose();\n            }\n        });\n        this._onContextRestoredObserver = scene.getEngine().onContextRestoredObservable.add(() => {\n            for (const geometry of this.geometries) {\n                geometry._rebuild();\n            }\n            for (const mesh of this.meshes) {\n                mesh._rebuild();\n            }\n            for (const system of this.particleSystems) {\n                system.rebuild();\n            }\n            for (const texture of this.textures) {\n                texture._rebuild();\n            }\n        });\n    }\n    /**\n     * Given a list of nodes, return a topological sorting of them.\n     * @param nodes\n     * @returns a sorted array of nodes\n     */\n    _topologicalSort(nodes) {\n        const nodesUidMap = new Map();\n        for (const node of nodes) {\n            nodesUidMap.set(node.uniqueId, node);\n        }\n        const dependencyGraph = {\n            dependsOn: new Map(), // given a node id, what are the ids of the nodes it depends on\n            dependedBy: new Map(), // given a node id, what are the ids of the nodes that depend on it\n        };\n        // Build the dependency graph given the list of nodes\n        // First pass: Initialize the empty dependency graph\n        for (const node of nodes) {\n            const nodeId = node.uniqueId;\n            dependencyGraph.dependsOn.set(nodeId, new Set());\n            dependencyGraph.dependedBy.set(nodeId, new Set());\n        }\n        // Second pass: Populate the dependency graph. We assume that we\n        // don't need to check for cycles here, as the scene graph cannot\n        // contain cycles. Our graph also already contains all transitive\n        // dependencies because getDescendants returns the transitive\n        // dependencies by default.\n        for (const node of nodes) {\n            const nodeId = node.uniqueId;\n            const dependsOn = dependencyGraph.dependsOn.get(nodeId);\n            if (node instanceof InstancedMesh) {\n                const masterMesh = node.sourceMesh;\n                if (nodesUidMap.has(masterMesh.uniqueId)) {\n                    dependsOn.add(masterMesh.uniqueId);\n                    dependencyGraph.dependedBy.get(masterMesh.uniqueId).add(nodeId);\n                }\n            }\n            const dependedBy = dependencyGraph.dependedBy.get(nodeId);\n            for (const child of node.getDescendants()) {\n                const childId = child.uniqueId;\n                if (nodesUidMap.has(childId)) {\n                    dependedBy.add(childId);\n                    const childDependsOn = dependencyGraph.dependsOn.get(childId);\n                    childDependsOn.add(nodeId);\n                }\n            }\n        }\n        // Third pass: Topological sort\n        const sortedNodes = [];\n        // First: Find all nodes that have no dependencies\n        const leaves = [];\n        for (const node of nodes) {\n            const nodeId = node.uniqueId;\n            if (dependencyGraph.dependsOn.get(nodeId).size === 0) {\n                leaves.push(node);\n                nodesUidMap.delete(nodeId);\n            }\n        }\n        const visitList = leaves;\n        while (visitList.length > 0) {\n            const nodeToVisit = visitList.shift();\n            sortedNodes.push(nodeToVisit);\n            // Remove the node from the dependency graph\n            // When a node is visited, we know that dependsOn is empty.\n            // So we only need to remove the node from dependedBy.\n            const dependedByVisitedNode = dependencyGraph.dependedBy.get(nodeToVisit.uniqueId);\n            // Array.from(x.values()) is to make the TS compiler happy\n            for (const dependedByVisitedNodeId of Array.from(dependedByVisitedNode.values())) {\n                const dependsOnDependedByVisitedNode = dependencyGraph.dependsOn.get(dependedByVisitedNodeId);\n                dependsOnDependedByVisitedNode.delete(nodeToVisit.uniqueId);\n                if (dependsOnDependedByVisitedNode.size === 0 && nodesUidMap.get(dependedByVisitedNodeId)) {\n                    visitList.push(nodesUidMap.get(dependedByVisitedNodeId));\n                    nodesUidMap.delete(dependedByVisitedNodeId);\n                }\n            }\n        }\n        if (nodesUidMap.size > 0) {\n            Logger.Error(\"SceneSerializer._topologicalSort: There were unvisited nodes:\");\n            nodesUidMap.forEach((node) => Logger.Error(node.name));\n        }\n        return sortedNodes;\n    }\n    _addNodeAndDescendantsToList(list, addedIds, rootNode, predicate) {\n        if (!rootNode || (predicate && !predicate(rootNode)) || addedIds.has(rootNode.uniqueId)) {\n            return;\n        }\n        list.push(rootNode);\n        addedIds.add(rootNode.uniqueId);\n        for (const child of rootNode.getDescendants(true)) {\n            this._addNodeAndDescendantsToList(list, addedIds, child, predicate);\n        }\n    }\n    /**\n     * Check if a specific node is contained in this asset container.\n     * @param node the node to check\n     * @returns true if the node is contained in this container, otherwise false.\n     */\n    _isNodeInContainer(node) {\n        if (node instanceof AbstractMesh && this.meshes.indexOf(node) !== -1) {\n            return true;\n        }\n        if (node instanceof TransformNode && this.transformNodes.indexOf(node) !== -1) {\n            return true;\n        }\n        if (node instanceof Light && this.lights.indexOf(node) !== -1) {\n            return true;\n        }\n        if (node instanceof Camera && this.cameras.indexOf(node) !== -1) {\n            return true;\n        }\n        return false;\n    }\n    /**\n     * For every node in the scene, check if its parent node is also in the scene.\n     * @returns true if every node's parent is also in the scene, otherwise false.\n     */\n    _isValidHierarchy() {\n        for (const node of this.meshes) {\n            if (node.parent && !this._isNodeInContainer(node.parent)) {\n                Logger.Warn(`Node ${node.name} has a parent that is not in the container.`);\n                return false;\n            }\n        }\n        for (const node of this.transformNodes) {\n            if (node.parent && !this._isNodeInContainer(node.parent)) {\n                Logger.Warn(`Node ${node.name} has a parent that is not in the container.`);\n                return false;\n            }\n        }\n        for (const node of this.lights) {\n            if (node.parent && !this._isNodeInContainer(node.parent)) {\n                Logger.Warn(`Node ${node.name} has a parent that is not in the container.`);\n                return false;\n            }\n        }\n        for (const node of this.cameras) {\n            if (node.parent && !this._isNodeInContainer(node.parent)) {\n                Logger.Warn(`Node ${node.name} has a parent that is not in the container.`);\n                return false;\n            }\n        }\n        return true;\n    }\n    /**\n     * Instantiate or clone all meshes and add the new ones to the scene.\n     * Skeletons and animation groups will all be cloned\n     * @param nameFunction defines an optional function used to get new names for clones\n     * @param cloneMaterials defines an optional boolean that defines if materials must be cloned as well (false by default)\n     * @param options defines an optional list of options to control how to instantiate / clone models\n     * @param options.doNotInstantiate defines if the model must be instantiated or just cloned\n     * @param options.predicate defines a predicate used to filter whih mesh to instantiate/clone\n     * @returns a list of rootNodes, skeletons and animation groups that were duplicated\n     */\n    instantiateModelsToScene(nameFunction, cloneMaterials = false, options) {\n        if (!this._isValidHierarchy()) {\n            Tools.Warn(\"SceneSerializer.InstantiateModelsToScene: The Asset Container hierarchy is not valid.\");\n        }\n        const conversionMap = {};\n        const storeMap = {};\n        const result = new InstantiatedEntries();\n        const alreadySwappedSkeletons = [];\n        const alreadySwappedMaterials = [];\n        const localOptions = {\n            doNotInstantiate: true,\n            ...options,\n        };\n        const onClone = (source, clone) => {\n            conversionMap[source.uniqueId] = clone.uniqueId;\n            storeMap[clone.uniqueId] = clone;\n            if (nameFunction) {\n                clone.name = nameFunction(source.name);\n            }\n            if (clone instanceof Mesh) {\n                const clonedMesh = clone;\n                if (clonedMesh.morphTargetManager) {\n                    const oldMorphTargetManager = source.morphTargetManager;\n                    clonedMesh.morphTargetManager = oldMorphTargetManager.clone();\n                    for (let index = 0; index < oldMorphTargetManager.numTargets; index++) {\n                        const oldTarget = oldMorphTargetManager.getTarget(index);\n                        const newTarget = clonedMesh.morphTargetManager.getTarget(index);\n                        conversionMap[oldTarget.uniqueId] = newTarget.uniqueId;\n                        storeMap[newTarget.uniqueId] = newTarget;\n                    }\n                }\n            }\n        };\n        const nodesToSort = [];\n        const idsOnSortList = new Set();\n        for (const transformNode of this.transformNodes) {\n            if (transformNode.parent === null) {\n                this._addNodeAndDescendantsToList(nodesToSort, idsOnSortList, transformNode, localOptions.predicate);\n            }\n        }\n        for (const mesh of this.meshes) {\n            if (mesh.parent === null) {\n                this._addNodeAndDescendantsToList(nodesToSort, idsOnSortList, mesh, localOptions.predicate);\n            }\n        }\n        // Topologically sort nodes by parenting/instancing relationships so that all resources are in place\n        // when a given node is instantiated.\n        const sortedNodes = this._topologicalSort(nodesToSort);\n        const onNewCreated = (source, clone) => {\n            onClone(source, clone);\n            if (source.parent) {\n                const replicatedParentId = conversionMap[source.parent.uniqueId];\n                const replicatedParent = storeMap[replicatedParentId];\n                if (replicatedParent) {\n                    clone.parent = replicatedParent;\n                }\n                else {\n                    clone.parent = source.parent;\n                }\n            }\n            if (clone.position && source.position) {\n                clone.position.copyFrom(source.position);\n            }\n            if (clone.rotationQuaternion && source.rotationQuaternion) {\n                clone.rotationQuaternion.copyFrom(source.rotationQuaternion);\n            }\n            if (clone.rotation && source.rotation) {\n                clone.rotation.copyFrom(source.rotation);\n            }\n            if (clone.scaling && source.scaling) {\n                clone.scaling.copyFrom(source.scaling);\n            }\n            if (clone.material) {\n                const mesh = clone;\n                if (mesh.material) {\n                    if (cloneMaterials) {\n                        const sourceMaterial = source.material;\n                        if (alreadySwappedMaterials.indexOf(sourceMaterial) === -1) {\n                            let swap = sourceMaterial.clone(nameFunction ? nameFunction(sourceMaterial.name) : \"Clone of \" + sourceMaterial.name);\n                            alreadySwappedMaterials.push(sourceMaterial);\n                            conversionMap[sourceMaterial.uniqueId] = swap.uniqueId;\n                            storeMap[swap.uniqueId] = swap;\n                            if (sourceMaterial.getClassName() === \"MultiMaterial\") {\n                                const multi = sourceMaterial;\n                                for (const material of multi.subMaterials) {\n                                    if (!material) {\n                                        continue;\n                                    }\n                                    swap = material.clone(nameFunction ? nameFunction(material.name) : \"Clone of \" + material.name);\n                                    alreadySwappedMaterials.push(material);\n                                    conversionMap[material.uniqueId] = swap.uniqueId;\n                                    storeMap[swap.uniqueId] = swap;\n                                }\n                                multi.subMaterials = multi.subMaterials.map((m) => m && storeMap[conversionMap[m.uniqueId]]);\n                            }\n                        }\n                        if (mesh.getClassName() !== \"InstancedMesh\") {\n                            mesh.material = storeMap[conversionMap[sourceMaterial.uniqueId]];\n                        }\n                    }\n                    else {\n                        if (mesh.material.getClassName() === \"MultiMaterial\") {\n                            if (this.scene.multiMaterials.indexOf(mesh.material) === -1) {\n                                this.scene.addMultiMaterial(mesh.material);\n                            }\n                        }\n                        else {\n                            if (this.scene.materials.indexOf(mesh.material) === -1) {\n                                this.scene.addMaterial(mesh.material);\n                            }\n                        }\n                    }\n                }\n            }\n            if (clone.parent === null) {\n                result.rootNodes.push(clone);\n            }\n        };\n        sortedNodes.forEach((node) => {\n            if (node.getClassName() === \"InstancedMesh\") {\n                const instancedNode = node;\n                const sourceMesh = instancedNode.sourceMesh;\n                const replicatedSourceId = conversionMap[sourceMesh.uniqueId];\n                const replicatedSource = typeof replicatedSourceId === \"number\" ? storeMap[replicatedSourceId] : sourceMesh;\n                const replicatedInstancedNode = replicatedSource.createInstance(instancedNode.name);\n                onNewCreated(instancedNode, replicatedInstancedNode);\n            }\n            else {\n                // Mesh or TransformNode\n                let canInstance = true;\n                if (node.getClassName() === \"TransformNode\" ||\n                    node.getClassName() === \"Node\" ||\n                    node.skeleton ||\n                    !node.getTotalVertices ||\n                    node.getTotalVertices() === 0) {\n                    // Transform nodes, skinned meshes, and meshes with no vertices can never be instanced!\n                    canInstance = false;\n                }\n                else if (localOptions.doNotInstantiate) {\n                    if (typeof localOptions.doNotInstantiate === \"function\") {\n                        canInstance = !localOptions.doNotInstantiate(node);\n                    }\n                    else {\n                        canInstance = !localOptions.doNotInstantiate;\n                    }\n                }\n                const replicatedNode = canInstance ? node.createInstance(`instance of ${node.name}`) : node.clone(`Clone of ${node.name}`, null, true);\n                if (!replicatedNode) {\n                    throw new Error(`Could not clone or instantiate node on Asset Container ${node.name}`);\n                }\n                onNewCreated(node, replicatedNode);\n            }\n        });\n        this.skeletons.forEach((s) => {\n            if (localOptions.predicate && !localOptions.predicate(s)) {\n                return;\n            }\n            const clone = s.clone(nameFunction ? nameFunction(s.name) : \"Clone of \" + s.name);\n            for (const m of this.meshes) {\n                if (m.skeleton === s && !m.isAnInstance) {\n                    const copy = storeMap[conversionMap[m.uniqueId]];\n                    if (!copy || copy.isAnInstance) {\n                        continue;\n                    }\n                    copy.skeleton = clone;\n                    if (alreadySwappedSkeletons.indexOf(clone) !== -1) {\n                        continue;\n                    }\n                    alreadySwappedSkeletons.push(clone);\n                    // Check if bones are mesh linked\n                    for (const bone of clone.bones) {\n                        if (bone._linkedTransformNode) {\n                            bone._linkedTransformNode = storeMap[conversionMap[bone._linkedTransformNode.uniqueId]];\n                        }\n                    }\n                }\n            }\n            result.skeletons.push(clone);\n        });\n        this.animationGroups.forEach((o) => {\n            if (localOptions.predicate && !localOptions.predicate(o)) {\n                return;\n            }\n            const clone = o.clone(nameFunction ? nameFunction(o.name) : \"Clone of \" + o.name, (oldTarget) => {\n                const newTarget = storeMap[conversionMap[oldTarget.uniqueId]];\n                return newTarget || oldTarget;\n            });\n            result.animationGroups.push(clone);\n        });\n        return result;\n    }\n    /**\n     * Adds all the assets from the container to the scene.\n     */\n    addAllToScene() {\n        if (this._wasAddedToScene) {\n            return;\n        }\n        if (!this._isValidHierarchy()) {\n            Tools.Warn(\"SceneSerializer.addAllToScene: The Asset Container hierarchy is not valid.\");\n        }\n        this._wasAddedToScene = true;\n        this.addToScene(null);\n        if (this.environmentTexture) {\n            this.scene.environmentTexture = this.environmentTexture;\n        }\n        for (const component of this.scene._serializableComponents) {\n            component.addFromContainer(this);\n        }\n        this.scene.getEngine().onContextRestoredObservable.remove(this._onContextRestoredObserver);\n        this._onContextRestoredObserver = null;\n    }\n    /**\n     * Adds assets from the container to the scene.\n     * @param predicate defines a predicate used to select which entity will be added (can be null)\n     */\n    addToScene(predicate = null) {\n        const addedNodes = [];\n        this.cameras.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.addCamera(o);\n            addedNodes.push(o);\n        });\n        this.lights.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.addLight(o);\n            addedNodes.push(o);\n        });\n        this.meshes.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.addMesh(o);\n            addedNodes.push(o);\n        });\n        this.skeletons.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.addSkeleton(o);\n        });\n        this.animations.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.addAnimation(o);\n        });\n        this.animationGroups.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.addAnimationGroup(o);\n        });\n        this.multiMaterials.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.addMultiMaterial(o);\n        });\n        this.materials.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.addMaterial(o);\n        });\n        this.morphTargetManagers.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.addMorphTargetManager(o);\n        });\n        this.geometries.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.addGeometry(o);\n        });\n        this.transformNodes.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.addTransformNode(o);\n            addedNodes.push(o);\n        });\n        this.actionManagers.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.addActionManager(o);\n        });\n        this.textures.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.addTexture(o);\n        });\n        this.reflectionProbes.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.addReflectionProbe(o);\n        });\n        for (const addedNode of addedNodes) {\n            // If node was added to the scene, but parent is not in the scene, break the relationship\n            if (addedNode.parent && this.scene.getNodes().indexOf(addedNode.parent) === -1) {\n                // Use setParent to keep transform if possible\n                if (addedNode.setParent) {\n                    addedNode.setParent(null);\n                }\n                else {\n                    addedNode.parent = null;\n                }\n            }\n        }\n    }\n    /**\n     * Removes all the assets in the container from the scene\n     */\n    removeAllFromScene() {\n        if (!this._isValidHierarchy()) {\n            Tools.Warn(\"SceneSerializer.removeAllFromScene: The Asset Container hierarchy is not valid.\");\n        }\n        this._wasAddedToScene = false;\n        this.removeFromScene(null);\n        if (this.environmentTexture === this.scene.environmentTexture) {\n            this.scene.environmentTexture = null;\n        }\n        for (const component of this.scene._serializableComponents) {\n            component.removeFromContainer(this);\n        }\n    }\n    /**\n     * Removes assets in the container from the scene\n     * @param predicate defines a predicate used to select which entity will be added (can be null)\n     */\n    removeFromScene(predicate = null) {\n        this.cameras.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.removeCamera(o);\n        });\n        this.lights.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.removeLight(o);\n        });\n        this.meshes.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.removeMesh(o, true);\n        });\n        this.skeletons.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.removeSkeleton(o);\n        });\n        this.animations.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.removeAnimation(o);\n        });\n        this.animationGroups.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.removeAnimationGroup(o);\n        });\n        this.multiMaterials.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.removeMultiMaterial(o);\n        });\n        this.materials.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.removeMaterial(o);\n        });\n        this.morphTargetManagers.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.removeMorphTargetManager(o);\n        });\n        this.geometries.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.removeGeometry(o);\n        });\n        this.transformNodes.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.removeTransformNode(o);\n        });\n        this.actionManagers.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.removeActionManager(o);\n        });\n        this.textures.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.removeTexture(o);\n        });\n        this.reflectionProbes.forEach((o) => {\n            if (predicate && !predicate(o)) {\n                return;\n            }\n            this.scene.removeReflectionProbe(o);\n        });\n    }\n    /**\n     * Disposes all the assets in the container\n     */\n    dispose() {\n        this.cameras.slice(0).forEach((o) => {\n            o.dispose();\n        });\n        this.cameras.length = 0;\n        this.lights.slice(0).forEach((o) => {\n            o.dispose();\n        });\n        this.lights.length = 0;\n        this.meshes.slice(0).forEach((o) => {\n            o.dispose();\n        });\n        this.meshes.length = 0;\n        this.skeletons.slice(0).forEach((o) => {\n            o.dispose();\n        });\n        this.skeletons.length = 0;\n        this.animationGroups.slice(0).forEach((o) => {\n            o.dispose();\n        });\n        this.animationGroups.length = 0;\n        this.multiMaterials.slice(0).forEach((o) => {\n            o.dispose();\n        });\n        this.multiMaterials.length = 0;\n        this.materials.slice(0).forEach((o) => {\n            o.dispose();\n        });\n        this.materials.length = 0;\n        this.geometries.slice(0).forEach((o) => {\n            o.dispose();\n        });\n        this.geometries.length = 0;\n        this.transformNodes.slice(0).forEach((o) => {\n            o.dispose();\n        });\n        this.transformNodes.length = 0;\n        this.actionManagers.slice(0).forEach((o) => {\n            o.dispose();\n        });\n        this.actionManagers.length = 0;\n        this.textures.slice(0).forEach((o) => {\n            o.dispose();\n        });\n        this.textures.length = 0;\n        this.reflectionProbes.slice(0).forEach((o) => {\n            o.dispose();\n        });\n        this.reflectionProbes.length = 0;\n        this.morphTargetManagers.slice(0).forEach((o) => {\n            o.dispose();\n        });\n        this.morphTargetManagers.length = 0;\n        if (this.environmentTexture) {\n            this.environmentTexture.dispose();\n            this.environmentTexture = null;\n        }\n        for (const component of this.scene._serializableComponents) {\n            component.removeFromContainer(this, true);\n        }\n        if (this._onContextRestoredObserver) {\n            this.scene.getEngine().onContextRestoredObservable.remove(this._onContextRestoredObserver);\n            this._onContextRestoredObserver = null;\n        }\n    }\n    _moveAssets(sourceAssets, targetAssets, keepAssets) {\n        if (!sourceAssets || !targetAssets) {\n            return;\n        }\n        for (const asset of sourceAssets) {\n            let move = true;\n            if (keepAssets) {\n                for (const keepAsset of keepAssets) {\n                    if (asset === keepAsset) {\n                        move = false;\n                        break;\n                    }\n                }\n            }\n            if (move) {\n                targetAssets.push(asset);\n                asset._parentContainer = this;\n            }\n        }\n    }\n    /**\n     * Removes all the assets contained in the scene and adds them to the container.\n     * @param keepAssets Set of assets to keep in the scene. (default: empty)\n     */\n    moveAllFromScene(keepAssets) {\n        this._wasAddedToScene = false;\n        if (keepAssets === undefined) {\n            keepAssets = new KeepAssets();\n        }\n        for (const key in this) {\n            if (Object.prototype.hasOwnProperty.call(this, key)) {\n                this[key] = this[key] || (key === \"_environmentTexture\" ? null : []);\n                this._moveAssets(this.scene[key], this[key], keepAssets[key]);\n            }\n        }\n        this.environmentTexture = this.scene.environmentTexture;\n        this.removeAllFromScene();\n    }\n    /**\n     * Adds all meshes in the asset container to a root mesh that can be used to position all the contained meshes. The root mesh is then added to the front of the meshes in the assetContainer.\n     * @returns the root mesh\n     */\n    createRootMesh() {\n        const rootMesh = new Mesh(\"assetContainerRootMesh\", this.scene);\n        this.meshes.forEach((m) => {\n            if (!m.parent) {\n                rootMesh.addChild(m);\n            }\n        });\n        this.meshes.unshift(rootMesh);\n        return rootMesh;\n    }\n    /**\n     * Merge animations (direct and animation groups) from this asset container into a scene\n     * @param scene is the instance of BABYLON.Scene to append to (default: last created scene)\n     * @param animatables set of animatables to retarget to a node from the scene\n     * @param targetConverter defines a function used to convert animation targets from the asset container to the scene (default: search node by name)\n     * @returns an array of the new AnimationGroup added to the scene (empty array if none)\n     */\n    mergeAnimationsTo(scene = EngineStore.LastCreatedScene, animatables, targetConverter = null) {\n        if (!scene) {\n            Logger.Error(\"No scene available to merge animations to\");\n            return [];\n        }\n        const _targetConverter = targetConverter\n            ? targetConverter\n            : (target) => {\n                let node = null;\n                const targetProperty = target.animations.length ? target.animations[0].targetProperty : \"\";\n                /*\n            BabylonJS adds special naming to targets that are children of nodes.\n            This name attempts to remove that special naming to get the parent nodes name in case the target\n            can't be found in the node tree\n\n            Ex: Torso_primitive0 likely points to a Mesh primitive. We take away primitive0 and are left with \"Torso\" which is the name\n            of the primitive's parent.\n        */\n                const name = target.name.split(\".\").join(\"\").split(\"_primitive\")[0];\n                switch (targetProperty) {\n                    case \"position\":\n                    case \"rotationQuaternion\":\n                        node = scene.getTransformNodeByName(target.name) || scene.getTransformNodeByName(name);\n                        break;\n                    case \"influence\":\n                        node = scene.getMorphTargetByName(target.name) || scene.getMorphTargetByName(name);\n                        break;\n                    default:\n                        node = scene.getNodeByName(target.name) || scene.getNodeByName(name);\n                }\n                return node;\n            };\n        // Copy new node animations\n        const nodesInAC = this.getNodes();\n        nodesInAC.forEach((nodeInAC) => {\n            const nodeInScene = _targetConverter(nodeInAC);\n            if (nodeInScene !== null) {\n                // Remove old animations with same target property as a new one\n                for (const animationInAC of nodeInAC.animations) {\n                    // Doing treatment on an array for safety measure\n                    const animationsWithSameProperty = nodeInScene.animations.filter((animationInScene) => {\n                        return animationInScene.targetProperty === animationInAC.targetProperty;\n                    });\n                    for (const animationWithSameProperty of animationsWithSameProperty) {\n                        const index = nodeInScene.animations.indexOf(animationWithSameProperty, 0);\n                        if (index > -1) {\n                            nodeInScene.animations.splice(index, 1);\n                        }\n                    }\n                }\n                // Append new animations\n                nodeInScene.animations = nodeInScene.animations.concat(nodeInAC.animations);\n            }\n        });\n        const newAnimationGroups = [];\n        // Copy new animation groups\n        this.animationGroups.slice().forEach((animationGroupInAC) => {\n            // Clone the animation group and all its animatables\n            newAnimationGroups.push(animationGroupInAC.clone(animationGroupInAC.name, _targetConverter));\n            // Remove animatables related to the asset container\n            animationGroupInAC.animatables.forEach((animatable) => {\n                animatable.stop();\n            });\n        });\n        // Retarget animatables\n        animatables.forEach((animatable) => {\n            const target = _targetConverter(animatable.target);\n            if (target) {\n                // Clone the animatable and retarget it\n                scene.beginAnimation(target, animatable.fromFrame, animatable.toFrame, animatable.loopAnimation, animatable.speedRatio, animatable.onAnimationEnd ? animatable.onAnimationEnd : undefined, undefined, true, undefined, animatable.onAnimationLoop ? animatable.onAnimationLoop : undefined);\n                // Stop animation for the target in the asset container\n                scene.stopAnimation(animatable.target);\n            }\n        });\n        return newAnimationGroups;\n    }\n    /**\n     * @since 6.15.0\n     * This method checks for any node that has no parent\n     * and is not in the rootNodes array, and adds the node\n     * there, if so.\n     */\n    populateRootNodes() {\n        this.rootNodes.length = 0;\n        this.meshes.forEach((m) => {\n            if (!m.parent && this.rootNodes.indexOf(m) === -1) {\n                this.rootNodes.push(m);\n            }\n        });\n        this.transformNodes.forEach((t) => {\n            if (!t.parent && this.rootNodes.indexOf(t) === -1) {\n                this.rootNodes.push(t);\n            }\n        });\n        this.lights.forEach((l) => {\n            if (!l.parent && this.rootNodes.indexOf(l) === -1) {\n                this.rootNodes.push(l);\n            }\n        });\n        this.cameras.forEach((c) => {\n            if (!c.parent && this.rootNodes.indexOf(c) === -1) {\n                this.rootNodes.push(c);\n            }\n        });\n    }\n    /**\n     * @since 6.26.0\n     * Given a root asset, this method will traverse its hierarchy and add it, its children and any materials/skeletons/animation groups to the container.\n     * @param root root node\n     */\n    addAllAssetsToContainer(root) {\n        if (!root) {\n            return;\n        }\n        const nodesToVisit = [];\n        const visitedNodes = new Set();\n        nodesToVisit.push(root);\n        while (nodesToVisit.length > 0) {\n            const nodeToVisit = nodesToVisit.pop();\n            if (nodeToVisit instanceof Mesh) {\n                if (nodeToVisit.geometry && this.geometries.indexOf(nodeToVisit.geometry) === -1) {\n                    this.geometries.push(nodeToVisit.geometry);\n                }\n                this.meshes.push(nodeToVisit);\n            }\n            else if (nodeToVisit instanceof TransformNode) {\n                this.transformNodes.push(nodeToVisit);\n            }\n            else if (nodeToVisit instanceof Light) {\n                this.lights.push(nodeToVisit);\n            }\n            else if (nodeToVisit instanceof Camera) {\n                this.cameras.push(nodeToVisit);\n            }\n            if (nodeToVisit instanceof AbstractMesh) {\n                if (nodeToVisit.material && this.materials.indexOf(nodeToVisit.material) === -1) {\n                    this.materials.push(nodeToVisit.material);\n                    for (const texture of nodeToVisit.material.getActiveTextures()) {\n                        if (this.textures.indexOf(texture) === -1) {\n                            this.textures.push(texture);\n                        }\n                    }\n                }\n                if (nodeToVisit.skeleton && this.skeletons.indexOf(nodeToVisit.skeleton) === -1) {\n                    this.skeletons.push(nodeToVisit.skeleton);\n                }\n                if (nodeToVisit.morphTargetManager && this.morphTargetManagers.indexOf(nodeToVisit.morphTargetManager) === -1) {\n                    this.morphTargetManagers.push(nodeToVisit.morphTargetManager);\n                }\n            }\n            for (const child of nodeToVisit.getChildren()) {\n                if (!visitedNodes.has(child)) {\n                    nodesToVisit.push(child);\n                }\n            }\n            visitedNodes.add(nodeToVisit);\n        }\n        this.populateRootNodes();\n    }\n    /**\n     * Get from a list of objects by tags\n     * @param list the list of objects to use\n     * @param tagsQuery the query to use\n     * @param filter a predicate to filter for tags\n     * @returns\n     */\n    _getByTags(list, tagsQuery, filter) {\n        if (tagsQuery === undefined) {\n            // returns the complete list (could be done with Tags.MatchesQuery but no need to have a for-loop here)\n            return list;\n        }\n        const listByTags = [];\n        for (const i in list) {\n            const item = list[i];\n            if (Tags && Tags.MatchesQuery(item, tagsQuery) && (!filter || filter(item))) {\n                listByTags.push(item);\n            }\n        }\n        return listByTags;\n    }\n    /**\n     * Get a list of meshes by tags\n     * @param tagsQuery defines the tags query to use\n     * @param filter defines a predicate used to filter results\n     * @returns an array of Mesh\n     */\n    getMeshesByTags(tagsQuery, filter) {\n        return this._getByTags(this.meshes, tagsQuery, filter);\n    }\n    /**\n     * Get a list of cameras by tags\n     * @param tagsQuery defines the tags query to use\n     * @param filter defines a predicate used to filter results\n     * @returns an array of Camera\n     */\n    getCamerasByTags(tagsQuery, filter) {\n        return this._getByTags(this.cameras, tagsQuery, filter);\n    }\n    /**\n     * Get a list of lights by tags\n     * @param tagsQuery defines the tags query to use\n     * @param filter defines a predicate used to filter results\n     * @returns an array of Light\n     */\n    getLightsByTags(tagsQuery, filter) {\n        return this._getByTags(this.lights, tagsQuery, filter);\n    }\n    /**\n     * Get a list of materials by tags\n     * @param tagsQuery defines the tags query to use\n     * @param filter defines a predicate used to filter results\n     * @returns an array of Material\n     */\n    getMaterialsByTags(tagsQuery, filter) {\n        return this._getByTags(this.materials, tagsQuery, filter).concat(this._getByTags(this.multiMaterials, tagsQuery, filter));\n    }\n    /**\n     * Get a list of transform nodes by tags\n     * @param tagsQuery defines the tags query to use\n     * @param filter defines a predicate used to filter results\n     * @returns an array of TransformNode\n     */\n    getTransformNodesByTags(tagsQuery, filter) {\n        return this._getByTags(this.transformNodes, tagsQuery, filter);\n    }\n}\n//# sourceMappingURL=assetContainer.js.map"], "names": ["<PERSON><PERSON>", "name", "mesh", "instance", "In<PERSON>d<PERSON>esh", "key", "AbstractMesh", "source", "range", "_value", "_a", "Tools", "value", "<PERSON><PERSON>", "completeCheck", "kind", "copyWhenShared", "forceCopy", "vertexData", "data", "updatable", "stride", "updateExtends", "makeItUnique", "indices", "totalVertices", "applySkeletonOrOptions", "applyMorph", "options", "bias", "VertexBuffer", "renderId", "intermediateRendering", "TransformNode", "Matrix", "tempMaster", "TmpVectors", "camera", "sourceMeshLODLevels", "boundingInfo", "index", "newParent", "doNotCloneChildren", "newSourceMesh", "result", "DeepCopier", "doNotRecurse", "disposeMaterialAndTextures", "serializationObject", "onNewNodeCreated", "clone", "child", "_b", "visibleInstances", "renderSelf", "instanceCount", "size", "expectedSize", "offset", "instanceIndex", "RegisterClass", "AbstractAssetContainer", "nodes", "skeleton", "KeepAssets", "InstantiatedEntries", "o", "<PERSON>setC<PERSON><PERSON>", "scene", "EngineStore", "geometry", "system", "texture", "nodesUidMap", "node", "dependencyGraph", "nodeId", "dependsOn", "<PERSON><PERSON><PERSON>", "dependedBy", "childId", "sortedNodes", "leaves", "visitList", "nodeToVisit", "dependedByVisitedNode", "dependedByVisitedNodeId", "dependsOnDependedByVisitedNode", "list", "addedIds", "rootNode", "predicate", "Light", "Camera", "nameFunction", "cloneMaterials", "conversionMap", "storeMap", "alreadySwappedSkeletons", "alreadySwappedMaterials", "localOptions", "onClone", "cloned<PERSON><PERSON>", "oldMorphTargetManager", "old<PERSON><PERSON>get", "newTarget", "nodesToSort", "idsOnSortList", "transformNode", "onNewCreated", "replicatedParentId", "replicatedParent", "sourceMaterial", "swap", "multi", "material", "m", "instancedNode", "sourceMesh", "replicatedSourceId", "replicatedInstancedNode", "canInstance", "replicatedNode", "s", "copy", "bone", "component", "addedNodes", "addedNode", "sourceAssets", "targetAssets", "keepAssets", "asset", "move", "keepAsset", "<PERSON><PERSON><PERSON>", "animatables", "targetConverter", "_targetConverter", "target", "targetProperty", "nodeInAC", "nodeInScene", "animationInAC", "animationsWithSameProperty", "animationInScene", "animationWithSameProperty", "newAnimationGroups", "animationGroupInAC", "animatable", "t", "l", "c", "root", "nodesToVisit", "visitedNodes", "<PERSON><PERSON><PERSON><PERSON>", "filter", "listByTags", "i", "item", "Tags"], "mappings": "4IASAA,EAAK,sBAAwB,CAACC,EAAMC,IAAS,CACzC,MAAMC,EAAW,IAAIC,EAAcH,EAAMC,CAAI,EAC7C,GAAIA,EAAK,iBAAkB,CACvBC,EAAS,iBAAmB,GAC5B,UAAWE,KAAOH,EAAK,iBACnBC,EAAS,iBAAiBE,CAAG,EAAIH,EAAK,iBAAiBG,CAAG,CAEjE,CACD,OAAOF,CACX,EAIO,MAAMC,UAAsBE,CAAa,CAM5C,YAAYL,EAAMM,EAAQ,CACtB,MAAMN,EAAMM,EAAO,SAAU,CAAA,EAE7B,KAAK,gCAAkC,GAEvC,KAAK,kBAAoB,EACzBA,EAAO,YAAY,IAAI,EACvB,KAAK,YAAcA,EACnB,KAAK,WAAaA,EAAO,WACzB,KAAK,SAAS,SAASA,EAAO,QAAQ,EACtC,KAAK,SAAS,SAASA,EAAO,QAAQ,EACtC,KAAK,QAAQ,SAASA,EAAO,OAAO,EAChCA,EAAO,qBACP,KAAK,mBAAqBA,EAAO,mBAAmB,MAAK,GAE7D,KAAK,WAAaA,EAAO,WAAW,MAAK,EACzC,UAAWC,KAASD,EAAO,qBACnBC,GAAS,MACT,KAAK,qBAAqBA,EAAM,KAAMA,EAAM,KAAMA,EAAM,EAAE,EAGlE,KAAK,iBAAmBD,EAAO,iBAC/B,KAAK,eAAeA,EAAO,eAAgB,CAAA,EAC3C,KAAK,oBAAoB,GAAM,EAAI,EACnC,KAAK,eAAc,CACtB,CAID,cAAe,CACX,MAAO,eACV,CAED,IAAI,cAAe,CACf,OAAO,KAAK,YAAY,aAC3B,CACD,qBAAsB,CAErB,CACD,oBAAqB,CAEpB,CACD,oBAAqB,CAEpB,CAKD,IAAI,gBAAiB,CACjB,OAAO,KAAK,YAAY,cAC3B,CACD,IAAI,eAAeE,EAAQ,SACnBC,EAAA,KAAK,cAAL,YAAAA,EAAkB,kBAAmBD,GACrCE,EAAM,KAAK,2DAA2D,CAE7E,CAID,IAAI,UAAW,CACX,OAAO,KAAK,YAAY,QAC3B,CACD,IAAI,SAASF,EAAQ,SACbC,EAAA,KAAK,cAAL,YAAAA,EAAkB,YAAaD,GAC/BE,EAAM,KAAK,qDAAqD,CAEvE,CAID,IAAI,YAAa,CACb,OAAO,KAAK,YAAY,UAC3B,CACD,IAAI,WAAWF,EAAQ,SACfC,EAAA,KAAK,cAAL,YAAAA,EAAkB,cAAeD,GACjCE,EAAM,KAAK,uDAAuD,CAEzE,CAID,IAAI,UAAW,CACX,OAAO,KAAK,YAAY,QAC3B,CACD,IAAI,SAASF,EAAQ,SACbC,EAAA,KAAK,cAAL,YAAAA,EAAkB,YAAaD,GAC/BE,EAAM,KAAK,qDAAqD,CAEvE,CAID,IAAI,kBAAmB,CACnB,OAAO,KAAK,YAAY,gBAC3B,CACD,IAAI,iBAAiBC,EAAO,CACpB,CAAC,KAAK,aAAeA,IAAU,KAAK,YAAY,kBAIpDC,EAAO,KAAK,iFAAiF,CAChG,CAID,kBAAmB,CACf,OAAO,KAAK,YAAc,KAAK,YAAY,iBAAkB,EAAG,CACnE,CAKD,iBAAkB,CACd,OAAO,KAAK,YAAY,iBAC3B,CAID,IAAI,YAAa,CACb,OAAO,KAAK,WACf,CAID,IAAI,UAAW,CACX,OAAO,KAAK,YAAY,SAC3B,CAOD,eAAeZ,EAAM,CACjB,OAAO,KAAK,YAAY,eAAeA,CAAI,CAC9C,CAMD,QAAQa,EAAgB,GAAO,CAC3B,OAAO,KAAK,YAAY,QAAQA,EAAe,EAAI,CACtD,CAQD,gBAAgBC,EAAMC,EAAgBC,EAAW,CAC7C,OAAO,KAAK,YAAY,gBAAgBF,EAAMC,EAAgBC,CAAS,CAC1E,CACD,iBAAiBF,EAAMG,EAAY,CAC/B,KAAK,YAAY,iBAAiBH,EAAMG,CAAU,CACrD,CA+BD,gBAAgBH,EAAMI,EAAMC,EAAWC,EAAQ,CAC3C,OAAI,KAAK,YACL,KAAK,WAAW,gBAAgBN,EAAMI,EAAMC,EAAWC,CAAM,EAE1D,KAAK,UACf,CA8BD,mBAAmBN,EAAMI,EAAMG,EAAeC,EAAc,CACxD,OAAI,KAAK,YACL,KAAK,WAAW,mBAAmBR,EAAMI,EAAMG,EAAeC,CAAY,EAEvE,KAAK,UACf,CAWD,WAAWC,EAASC,EAAgB,KAAM,CACtC,OAAI,KAAK,YACL,KAAK,WAAW,WAAWD,EAASC,CAAa,EAE9C,KAAK,UACf,CAkBD,sBAAsBV,EAAM,CACxB,OAAO,KAAK,YAAY,sBAAsBA,CAAI,CACrD,CAID,YAAa,CACT,OAAO,KAAK,YAAY,YAC3B,CACD,IAAI,YAAa,CACb,OAAO,KAAK,YAAY,UAC3B,CACD,oBAAoBW,EAAyB,GAAOC,EAAa,GAAO,CACpE,GAAI,KAAK,iBAAmB,KAAK,gBAAe,EAAG,SAC/C,OAAO,KAEX,IAAIC,EACA,OAAOF,GAA2B,SAClCE,EAAUF,EAGVE,EAAU,CACN,cAAeF,EACf,WAAYC,CAC5B,EAEQ,MAAME,EAAO,KAAK,YAAY,SAAW,KAAK,YAAY,SAAS,aAAe,KAClF,YAAK,qBAAqB,KAAK,YAAY,SAASD,EAAS,KAAME,EAAa,YAAY,EAAGD,CAAI,EAC5F,IACV,CAED,cAAe,CACX,OAAI,KAAK,aACL,KAAK,YAAY,eAEd,IACV,CAID,UAAUE,EAAUC,EAAuB,CAKvC,GAJA,MAAM,UAAUD,EAAUC,CAAqB,EAC1C,KAAK,YAAY,WAClBnB,EAAO,KAAK,4DAA4D,EAExE,KAAK,YAAa,CAElB,GADsB,KAAK,YAAY,2BAA0B,GAAM,GAAM,KAAK,2BAA4B,GAAI,EAE9G,YAAK,8BAA8B,kBAAoB,GAChD,GAIX,GAFA,KAAK,8BAA8B,kBAAoB,GACvD,KAAK,YAAY,6BAA6B,KAAMkB,CAAQ,EACxDC,GACA,GAAI,CAAC,KAAK,YAAY,8BAA8B,sBAChD,YAAK,YAAY,8BAA8B,8BAAgC,GACxE,WAIP,CAAC,KAAK,YAAY,8BAA8B,UAChD,YAAK,YAAY,8BAA8B,kBAAoB,GAC5D,EAGlB,CACD,MAAO,EACV,CAED,eAAgB,CACR,KAAK,YAAY,yBAA2B,KAAK,YAAY,gBAAkB,KAAK,YAAY,eAAe,WAAa,KAAK,YAAY,iBAE7I,KAAK,YAAY,gBAAgB,gBAAgB,gBAAgB,KAAK,YAAY,cAAc,EAChG,KAAK,YAAY,eAAe,gBAAgB,KAAK,KAAK,eAAc,CAAE,GAErE,KAAK,gBAAkB,KAAK,eAAe,WAAa,KAAK,YAAY,iBAE9E,KAAK,YAAY,gBAAgB,gBAAgB,KAAK,KAAK,cAAc,CAEhF,CACD,gBAAiB,CACb,GAAI,KAAK,aAAe,KAAK,YAAY,gBAAkBC,EAAc,oBAAsB,KAAK,YAAY,cAAgB,KAAM,CAC7H,KAAK,wBACN,KAAK,sBAAwB,IAAIC,GAErC,MAAMC,EAAa,KAAK,YAAY,YACpC,YAAK,YAAY,YAAc,KAC/BC,EAAW,QAAQ,CAAC,EAAE,SAAS,KAAK,YAAY,QAAQ,EACxD,KAAK,YAAY,SAAS,IAAI,EAAG,EAAG,CAAC,EACrC,KAAK,sBAAsB,SAAS,KAAK,YAAY,mBAAmB,EAAI,CAAC,EAC7E,KAAK,YAAY,SAAS,SAASA,EAAW,QAAQ,CAAC,CAAC,EACxD,KAAK,YAAY,YAAcD,EACxB,KAAK,qBACf,CACD,OAAO,MAAM,gBAChB,CACD,IAAI,cAAe,CACf,MAAO,EACV,CAMD,OAAOE,EAAQ,CACX,GAAI,CAACA,EACD,OAAO,KAEX,MAAMC,EAAsB,KAAK,WAAW,aAAY,EACxD,GAAI,CAACA,GAAuBA,EAAoB,SAAW,EACvD,KAAK,YAAc,KAAK,eAEvB,CACD,MAAMC,EAAe,KAAK,kBAC1B,KAAK,YAAc,KAAK,WAAW,OAAOF,EAAQE,EAAa,cAAc,CAChF,CACD,OAAO,KAAK,WACf,CAID,qCAAqCR,EAAU,CAC3C,OAAO,KAAK,WAAW,qCAAqCA,CAAQ,CACvE,CAED,gBAAiB,CAEb,GADA,KAAK,iBAAgB,EACjB,KAAK,YAAY,UACjB,QAASS,EAAQ,EAAGA,EAAQ,KAAK,YAAY,UAAU,OAAQA,IAC3D,KAAK,YAAY,UAAUA,CAAK,EAAE,MAAM,KAAM,KAAK,WAAW,EAGtE,OAAO,IACV,CAED,sBAAuB,CACnB,OAAO,KAAK,YAAY,sBAC3B,CAED,qBAAsB,CAClB,OAAI,KAAK,gBACL,KAAK,gBAAiB,EAAC,OAAO,KAAK,oBAAoB,EAGvD,KAAK,kBAAkB,KAAK,iBAAkB,KAAK,iBAAkB,KAAK,oBAAoB,EAElG,KAAK,6BAA6B,KAAK,oBAAoB,EACpD,IACV,CAWD,MAAMvC,EAAMwC,EAAY,KAAMC,EAAoBC,EAAe,CAC7D,MAAMC,GAAUD,GAAiB,KAAK,aAAa,eAAe1C,CAAI,EAyCtE,GAvCA4C,EAAW,SAAS,KAAMD,EAAQ,CAC9B,OACA,YACA,WACA,SACA,eACA,iBACA,WACA,aACA,WACA,aACA,eACA,UACA,qBACA,YACA,WACA,eACA,WACA,gBACA,UACA,KACA,QACA,mBACA,kBACA,6BACA,sBACA,oBACA,YACA,uBACA,mBACA,kBACA,UACH,EAAE,CAAE,CAAA,EAEL,KAAK,oBAAmB,EAEpBH,IACAG,EAAO,OAASH,GAEhB,CAACC,EAED,QAASF,EAAQ,EAAGA,EAAQ,KAAK,SAAQ,EAAG,OAAO,OAAQA,IAAS,CAChE,MAAMtC,EAAO,KAAK,SAAU,EAAC,OAAOsC,CAAK,EACrCtC,EAAK,SAAW,MAChBA,EAAK,MAAMA,EAAK,KAAM0C,CAAM,CAEnC,CAEL,OAAAA,EAAO,mBAAmB,EAAI,EAC9B,KAAK,mBAAmB,gBAAgBA,CAAM,EACvCA,CACV,CAOD,QAAQE,EAAcC,EAA6B,GAAO,CAEtD,KAAK,YAAY,eAAe,IAAI,EACpC,MAAM,QAAQD,EAAcC,CAA0B,CACzD,CAID,mBAAmBC,EAAqB,CACpC,MAAM,mBAAmBA,CAAmB,EAC5CA,EAAoB,SAAW,KAAK,YAAY,SAChDA,EAAoB,oBAAsB,KAAK,+BAClD,CAUD,qBAAqBP,EAAY,KAAMb,EAASqB,EAAkB,CAC9D,MAAMC,EAAQ,KAAK,MAAM,aAAe,KAAK,MAAQ,KAAK,IAAKT,GAAa,KAAK,OAAQ,GAAMb,GAAWA,EAAQ,cAAc,EAC5HsB,GACID,GACAA,EAAiB,KAAMC,CAAK,EAGpC,UAAWC,KAAS,KAAK,uBAAuB,EAAI,EAChDA,EAAM,qBAAqBD,EAAOtB,EAASqB,CAAgB,EAE/D,OAAOC,CACV,CACL,CACAlD,EAAK,UAAU,wBAA0B,SAAUe,EAAMM,EAAQ,SAI7D,IAFA+B,GAAA1C,EAAA,KAAK,+BAAL,YAAAA,EAAmC,cAAcK,KAAjD,MAAAqC,EAAwD,UAEpD,CAAC,KAAK,iBAAkB,CACxB,KAAK,iBAAmB,GACxB,UAAWjD,KAAY,KAAK,UACxBA,EAAS,iBAAmB,EAEnC,CACI,KAAK,+BACN,KAAK,6BAA+B,CAChC,KAAM,CAAE,EACR,cAAe,CAAE,EACjB,QAAS,CAAE,EACX,MAAO,CAAE,EACT,mBAAoB,KAAK,UAAW,EAAC,QAAO,EAAG,kBAAoB,CAAA,EAAK,MACpF,GAGI,KAAK,iBAAiBY,CAAI,EAAI,KAC9B,KAAK,6BAA6B,QAAQA,CAAI,EAAIM,EAClD,KAAK,6BAA6B,MAAMN,CAAI,EAAIM,EAAS,GACzD,KAAK,6BAA6B,KAAKN,CAAI,EAAI,IAAI,aAAa,KAAK,6BAA6B,MAAMA,CAAI,CAAC,EAC7G,KAAK,6BAA6B,cAAcA,CAAI,EAAI,IAAIe,EAAa,KAAK,YAAa,KAAK,6BAA6B,KAAKf,CAAI,EAAGA,EAAM,GAAM,GAAOM,EAAQ,EAAI,EACxK,UAAWlB,KAAY,KAAK,UACxBA,EAAS,iBAAiBY,CAAI,EAAI,KAEtC,KAAK,qCAAoC,EACzC,KAAK,gCAA+B,CACxC,EACAf,EAAK,UAAU,yBAA2B,SAAUqD,EAAkBC,EAAY,CAC9E,MAAMC,EAAgBF,EAAmBA,EAAiB,OAAS,EACnE,UAAWtC,KAAQ,KAAK,iBAAkB,CACtC,IAAIyC,EAAO,KAAK,6BAA6B,MAAMzC,CAAI,EACvD,MAAMM,EAAS,KAAK,6BAA6B,QAAQN,CAAI,EAEvD0C,GAAgBF,EAAgB,GAAKlC,EAC3C,KAAOmC,EAAOC,GACVD,GAAQ,EAER,KAAK,6BAA6B,KAAKzC,CAAI,EAAE,QAAUyC,IACvD,KAAK,6BAA6B,KAAKzC,CAAI,EAAI,IAAI,aAAayC,CAAI,EACpE,KAAK,6BAA6B,MAAMzC,CAAI,EAAIyC,EAC5C,KAAK,6BAA6B,cAAczC,CAAI,IACpD,KAAK,6BAA6B,cAAcA,CAAI,EAAE,QAAO,EAC7D,KAAK,6BAA6B,cAAcA,CAAI,EAAI,OAGhE,MAAMI,EAAO,KAAK,6BAA6B,KAAKJ,CAAI,EAExD,IAAI2C,EAAS,EACb,GAAIJ,EAAY,CACZ,MAAM1C,EAAQ,KAAK,iBAAiBG,CAAI,EACpCH,EAAM,QACNA,EAAM,QAAQO,EAAMuC,CAAM,EAErB9C,EAAM,YACXA,EAAM,YAAYO,EAAMuC,CAAM,EAG9BvC,EAAKuC,CAAM,EAAI9C,EAEnB8C,GAAUrC,CACb,CACD,QAASsC,EAAgB,EAAGA,EAAgBJ,EAAeI,IAAiB,CAExE,MAAM/C,EADWyC,EAAiBM,CAAa,EACxB,iBAAiB5C,CAAI,EACxCH,EAAM,QACNA,EAAM,QAAQO,EAAMuC,CAAM,EAErB9C,EAAM,YACXA,EAAM,YAAYO,EAAMuC,CAAM,EAG9BvC,EAAKuC,CAAM,EAAI9C,EAEnB8C,GAAUrC,CACb,CAEI,KAAK,6BAA6B,cAAcN,CAAI,EAKrD,KAAK,6BAA6B,cAAcA,CAAI,EAAE,eAAeI,EAAM,CAAC,GAJ5E,KAAK,6BAA6B,cAAcJ,CAAI,EAAI,IAAIe,EAAa,KAAK,YAAa,KAAK,6BAA6B,KAAKf,CAAI,EAAGA,EAAM,GAAM,GAAOM,EAAQ,EAAI,EACxK,KAAK,qCAAoC,EAKhD,CACL,EACArB,EAAK,UAAU,qCAAuC,UAAY,CAC9D,GAAI,GAAC,KAAK,8BAAgC,KAAK,6BAA6B,qBAAuB,QAGnG,WAAWe,KAAQ,KAAK,6BAA6B,mBACjD,KAAK,UAAW,EAAC,yBAAyB,KAAK,6BAA6B,mBAAmBA,CAAI,CAAC,EAExG,KAAK,6BAA6B,mBAAqB,GAC3D,EACAf,EAAK,UAAU,6BAA+B,UAAY,CAKtD,IAJI,KAAK,qBAAqB,kBAC1B,KAAK,qBAAqB,gBAAgB,UAC1C,KAAK,qBAAqB,gBAAkB,MAEzC,KAAK,UAAU,QAClB,KAAK,UAAU,CAAC,EAAE,QAAO,EAE7B,UAAWe,KAAQ,KAAK,iBAChB,KAAK,6BAA6B,cAAcA,CAAI,GACpD,KAAK,6BAA6B,cAAcA,CAAI,EAAE,QAAO,EAGrE,KAAK,qCAAoC,EACzC,KAAK,iBAAmB,EAC5B,EAEA6C,EAAc,wBAAyBxD,CAAa,ECvoB7C,MAAMyD,CAAuB,CAChC,aAAc,CAIV,KAAK,UAAY,GAIjB,KAAK,QAAU,GAKf,KAAK,OAAS,GAId,KAAK,OAAS,GAKd,KAAK,UAAY,GAKjB,KAAK,gBAAkB,GAIvB,KAAK,WAAa,GAKlB,KAAK,gBAAkB,GAKvB,KAAK,eAAiB,GAQtB,KAAK,UAAY,GAKjB,KAAK,oBAAsB,GAI3B,KAAK,WAAa,GAQlB,KAAK,eAAiB,GAKtB,KAAK,eAAiB,GAItB,KAAK,SAAW,GAEhB,KAAK,oBAAsB,KAI3B,KAAK,cAAgB,GAIrB,KAAK,OAAS,KAId,KAAK,aAAe,GAIpB,KAAK,OAAS,GAId,KAAK,iBAAmB,EAC3B,CAMD,IAAI,oBAAqB,CACrB,OAAO,KAAK,mBACf,CACD,IAAI,mBAAmBjD,EAAO,CAC1B,KAAK,oBAAsBA,CAC9B,CAID,UAAW,CACP,IAAIkD,EAAQ,CAAA,EACZ,OAAAA,EAAQA,EAAM,OAAO,KAAK,MAAM,EAChCA,EAAQA,EAAM,OAAO,KAAK,MAAM,EAChCA,EAAQA,EAAM,OAAO,KAAK,OAAO,EACjCA,EAAQA,EAAM,OAAO,KAAK,cAAc,EACxC,KAAK,UAAU,QAASC,GAAcD,EAAQA,EAAM,OAAOC,EAAS,KAAK,CAAE,EACpED,CACV,CACL,CAIO,MAAME,UAAmBH,CAAuB,CACvD,CAIO,MAAMI,CAAoB,CAC7B,aAAc,CAIV,KAAK,UAAY,GAIjB,KAAK,UAAY,GAIjB,KAAK,gBAAkB,EAC1B,CAID,SAAU,CACN,KAAK,UAAU,MAAM,CAAC,EAAE,QAASC,GAAM,CACnCA,EAAE,QAAO,CACrB,CAAS,EACD,KAAK,UAAU,OAAS,EACxB,KAAK,UAAU,MAAM,CAAC,EAAE,QAASA,GAAM,CACnCA,EAAE,QAAO,CACrB,CAAS,EACD,KAAK,UAAU,OAAS,EACxB,KAAK,gBAAgB,MAAM,CAAC,EAAE,QAASA,GAAM,CACzCA,EAAE,QAAO,CACrB,CAAS,EACD,KAAK,gBAAgB,OAAS,CACjC,CACL,CAIO,MAAMC,UAAuBN,CAAuB,CAKvD,YAAYO,EAAO,CACf,QACA,KAAK,iBAAmB,GACxBA,EAAQA,GAASC,EAAY,iBACxBD,IAGL,KAAK,MAAQA,EACb,KAAK,mBAAwB,GAC7BA,EAAM,oBAAoB,IAAI,IAAM,CAC3B,KAAK,kBACN,KAAK,QAAO,CAE5B,CAAS,EACD,KAAK,2BAA6BA,EAAM,UAAS,EAAG,4BAA4B,IAAI,IAAM,CACtF,UAAWE,KAAY,KAAK,WACxBA,EAAS,SAAQ,EAErB,UAAWpE,KAAQ,KAAK,OACpBA,EAAK,SAAQ,EAEjB,UAAWqE,KAAU,KAAK,gBACtBA,EAAO,QAAO,EAElB,UAAWC,KAAW,KAAK,SACvBA,EAAQ,SAAQ,CAEhC,CAAS,EACJ,CAMD,iBAAiBV,EAAO,CACpB,MAAMW,EAAc,IAAI,IACxB,UAAWC,KAAQZ,EACfW,EAAY,IAAIC,EAAK,SAAUA,CAAI,EAEvC,MAAMC,EAAkB,CACpB,UAAW,IAAI,IACf,WAAY,IAAI,GAC5B,EAGQ,UAAWD,KAAQZ,EAAO,CACtB,MAAMc,EAASF,EAAK,SACpBC,EAAgB,UAAU,IAAIC,EAAQ,IAAI,GAAK,EAC/CD,EAAgB,WAAW,IAAIC,EAAQ,IAAI,GAAK,CACnD,CAMD,UAAWF,KAAQZ,EAAO,CACtB,MAAMc,EAASF,EAAK,SACdG,EAAYF,EAAgB,UAAU,IAAIC,CAAM,EACtD,GAAIF,aAAgBtE,EAAe,CAC/B,MAAM0E,EAAaJ,EAAK,WACpBD,EAAY,IAAIK,EAAW,QAAQ,IACnCD,EAAU,IAAIC,EAAW,QAAQ,EACjCH,EAAgB,WAAW,IAAIG,EAAW,QAAQ,EAAE,IAAIF,CAAM,EAErE,CACD,MAAMG,EAAaJ,EAAgB,WAAW,IAAIC,CAAM,EACxD,UAAWzB,KAASuB,EAAK,iBAAkB,CACvC,MAAMM,EAAU7B,EAAM,SAClBsB,EAAY,IAAIO,CAAO,IACvBD,EAAW,IAAIC,CAAO,EACCL,EAAgB,UAAU,IAAIK,CAAO,EAC7C,IAAIJ,CAAM,EAEhC,CACJ,CAED,MAAMK,EAAc,CAAA,EAEdC,EAAS,CAAA,EACf,UAAWR,KAAQZ,EAAO,CACtB,MAAMc,EAASF,EAAK,SAChBC,EAAgB,UAAU,IAAIC,CAAM,EAAE,OAAS,IAC/CM,EAAO,KAAKR,CAAI,EAChBD,EAAY,OAAOG,CAAM,EAEhC,CACD,MAAMO,EAAYD,EAClB,KAAOC,EAAU,OAAS,GAAG,CACzB,MAAMC,EAAcD,EAAU,QAC9BF,EAAY,KAAKG,CAAW,EAI5B,MAAMC,EAAwBV,EAAgB,WAAW,IAAIS,EAAY,QAAQ,EAEjF,UAAWE,KAA2B,MAAM,KAAKD,EAAsB,OAAM,CAAE,EAAG,CAC9E,MAAME,EAAiCZ,EAAgB,UAAU,IAAIW,CAAuB,EAC5FC,EAA+B,OAAOH,EAAY,QAAQ,EACtDG,EAA+B,OAAS,GAAKd,EAAY,IAAIa,CAAuB,IACpFH,EAAU,KAAKV,EAAY,IAAIa,CAAuB,CAAC,EACvDb,EAAY,OAAOa,CAAuB,EAEjD,CACJ,CACD,OAAIb,EAAY,KAAO,IACnB5D,EAAO,MAAM,+DAA+D,EAC5E4D,EAAY,QAASC,GAAS7D,EAAO,MAAM6D,EAAK,IAAI,CAAC,GAElDO,CACV,CACD,6BAA6BO,EAAMC,EAAUC,EAAUC,EAAW,CAC9D,GAAI,GAACD,GAAaC,GAAa,CAACA,EAAUD,CAAQ,GAAMD,EAAS,IAAIC,EAAS,QAAQ,GAGtF,CAAAF,EAAK,KAAKE,CAAQ,EAClBD,EAAS,IAAIC,EAAS,QAAQ,EAC9B,UAAWvC,KAASuC,EAAS,eAAe,EAAI,EAC5C,KAAK,6BAA6BF,EAAMC,EAAUtC,EAAOwC,CAAS,EAEzE,CAMD,mBAAmBjB,EAAM,CAUrB,OATIA,aAAgBpE,GAAgB,KAAK,OAAO,QAAQoE,CAAI,IAAM,IAG9DA,aAAgBzC,GAAiB,KAAK,eAAe,QAAQyC,CAAI,IAAM,IAGvEA,aAAgBkB,GAAS,KAAK,OAAO,QAAQlB,CAAI,IAAM,IAGvDA,aAAgBmB,GAAU,KAAK,QAAQ,QAAQnB,CAAI,IAAM,EAIhE,CAKD,mBAAoB,CAChB,UAAWA,KAAQ,KAAK,OACpB,GAAIA,EAAK,QAAU,CAAC,KAAK,mBAAmBA,EAAK,MAAM,EACnD,OAAA7D,EAAO,KAAK,QAAQ6D,EAAK,IAAI,6CAA6C,EACnE,GAGf,UAAWA,KAAQ,KAAK,eACpB,GAAIA,EAAK,QAAU,CAAC,KAAK,mBAAmBA,EAAK,MAAM,EACnD,OAAA7D,EAAO,KAAK,QAAQ6D,EAAK,IAAI,6CAA6C,EACnE,GAGf,UAAWA,KAAQ,KAAK,OACpB,GAAIA,EAAK,QAAU,CAAC,KAAK,mBAAmBA,EAAK,MAAM,EACnD,OAAA7D,EAAO,KAAK,QAAQ6D,EAAK,IAAI,6CAA6C,EACnE,GAGf,UAAWA,KAAQ,KAAK,QACpB,GAAIA,EAAK,QAAU,CAAC,KAAK,mBAAmBA,EAAK,MAAM,EACnD,OAAA7D,EAAO,KAAK,QAAQ6D,EAAK,IAAI,6CAA6C,EACnE,GAGf,MAAO,EACV,CAWD,yBAAyBoB,EAAcC,EAAiB,GAAOnE,EAAS,CAC/D,KAAK,qBACNjB,EAAM,KAAK,uFAAuF,EAEtG,MAAMqF,EAAgB,CAAA,EAChBC,EAAW,CAAA,EACXrD,EAAS,IAAIqB,EACbiC,EAA0B,CAAA,EAC1BC,EAA0B,CAAA,EAC1BC,EAAe,CACjB,iBAAkB,GAClB,GAAGxE,CACf,EACcyE,EAAU,CAAC9F,EAAQ2C,IAAU,CAM/B,GALA8C,EAAczF,EAAO,QAAQ,EAAI2C,EAAM,SACvC+C,EAAS/C,EAAM,QAAQ,EAAIA,EACvB4C,IACA5C,EAAM,KAAO4C,EAAavF,EAAO,IAAI,GAErC2C,aAAiBlD,EAAM,CACvB,MAAMsG,EAAapD,EACnB,GAAIoD,EAAW,mBAAoB,CAC/B,MAAMC,EAAwBhG,EAAO,mBACrC+F,EAAW,mBAAqBC,EAAsB,QACtD,QAAS/D,EAAQ,EAAGA,EAAQ+D,EAAsB,WAAY/D,IAAS,CACnE,MAAMgE,EAAYD,EAAsB,UAAU/D,CAAK,EACjDiE,EAAYH,EAAW,mBAAmB,UAAU9D,CAAK,EAC/DwD,EAAcQ,EAAU,QAAQ,EAAIC,EAAU,SAC9CR,EAASQ,EAAU,QAAQ,EAAIA,CAClC,CACJ,CACJ,CACb,EACcC,EAAc,CAAA,EACdC,EAAgB,IAAI,IAC1B,UAAWC,KAAiB,KAAK,eACzBA,EAAc,SAAW,MACzB,KAAK,6BAA6BF,EAAaC,EAAeC,EAAeR,EAAa,SAAS,EAG3G,UAAWlG,KAAQ,KAAK,OAChBA,EAAK,SAAW,MAChB,KAAK,6BAA6BwG,EAAaC,EAAezG,EAAMkG,EAAa,SAAS,EAKlG,MAAMnB,EAAc,KAAK,iBAAiByB,CAAW,EAC/CG,EAAe,CAACtG,EAAQ2C,IAAU,CAEpC,GADAmD,EAAQ9F,EAAQ2C,CAAK,EACjB3C,EAAO,OAAQ,CACf,MAAMuG,EAAqBd,EAAczF,EAAO,OAAO,QAAQ,EACzDwG,EAAmBd,EAASa,CAAkB,EAChDC,EACA7D,EAAM,OAAS6D,EAGf7D,EAAM,OAAS3C,EAAO,MAE7B,CAaD,GAZI2C,EAAM,UAAY3C,EAAO,UACzB2C,EAAM,SAAS,SAAS3C,EAAO,QAAQ,EAEvC2C,EAAM,oBAAsB3C,EAAO,oBACnC2C,EAAM,mBAAmB,SAAS3C,EAAO,kBAAkB,EAE3D2C,EAAM,UAAY3C,EAAO,UACzB2C,EAAM,SAAS,SAAS3C,EAAO,QAAQ,EAEvC2C,EAAM,SAAW3C,EAAO,SACxB2C,EAAM,QAAQ,SAAS3C,EAAO,OAAO,EAErC2C,EAAM,SAAU,CAChB,MAAMhD,EAAOgD,EACb,GAAIhD,EAAK,SACL,GAAI6F,EAAgB,CAChB,MAAMiB,EAAiBzG,EAAO,SAC9B,GAAI4F,EAAwB,QAAQa,CAAc,IAAM,GAAI,CACxD,IAAIC,EAAOD,EAAe,MAAMlB,EAAeA,EAAakB,EAAe,IAAI,EAAI,YAAcA,EAAe,IAAI,EAIpH,GAHAb,EAAwB,KAAKa,CAAc,EAC3ChB,EAAcgB,EAAe,QAAQ,EAAIC,EAAK,SAC9ChB,EAASgB,EAAK,QAAQ,EAAIA,EACtBD,EAAe,aAAc,IAAK,gBAAiB,CACnD,MAAME,EAAQF,EACd,UAAWG,KAAYD,EAAM,aACpBC,IAGLF,EAAOE,EAAS,MAAMrB,EAAeA,EAAaqB,EAAS,IAAI,EAAI,YAAcA,EAAS,IAAI,EAC9FhB,EAAwB,KAAKgB,CAAQ,EACrCnB,EAAcmB,EAAS,QAAQ,EAAIF,EAAK,SACxChB,EAASgB,EAAK,QAAQ,EAAIA,GAE9BC,EAAM,aAAeA,EAAM,aAAa,IAAKE,GAAMA,GAAKnB,EAASD,EAAcoB,EAAE,QAAQ,CAAC,CAAC,CAC9F,CACJ,CACGlH,EAAK,aAAc,IAAK,kBACxBA,EAAK,SAAW+F,EAASD,EAAcgB,EAAe,QAAQ,CAAC,EAEtE,MAEO9G,EAAK,SAAS,aAAY,IAAO,gBAC7B,KAAK,MAAM,eAAe,QAAQA,EAAK,QAAQ,IAAM,IACrD,KAAK,MAAM,iBAAiBA,EAAK,QAAQ,EAIzC,KAAK,MAAM,UAAU,QAAQA,EAAK,QAAQ,IAAM,IAChD,KAAK,MAAM,YAAYA,EAAK,QAAQ,CAKvD,CACGgD,EAAM,SAAW,MACjBN,EAAO,UAAU,KAAKM,CAAK,CAE3C,EACQ,OAAA+B,EAAY,QAASP,GAAS,CAC1B,GAAIA,EAAK,aAAc,IAAK,gBAAiB,CACzC,MAAM2C,EAAgB3C,EAChB4C,EAAaD,EAAc,WAC3BE,EAAqBvB,EAAcsB,EAAW,QAAQ,EAEtDE,GADmB,OAAOD,GAAuB,SAAWtB,EAASsB,CAAkB,EAAID,GAChD,eAAeD,EAAc,IAAI,EAClFR,EAAaQ,EAAeG,CAAuB,CACtD,KACI,CAED,IAAIC,EAAc,GACd/C,EAAK,aAAY,IAAO,iBACxBA,EAAK,aAAY,IAAO,QACxBA,EAAK,UACL,CAACA,EAAK,kBACNA,EAAK,iBAAkB,IAAK,EAE5B+C,EAAc,GAETrB,EAAa,mBACd,OAAOA,EAAa,kBAAqB,WACzCqB,EAAc,CAACrB,EAAa,iBAAiB1B,CAAI,EAGjD+C,EAAc,CAACrB,EAAa,kBAGpC,MAAMsB,EAAiBD,EAAc/C,EAAK,eAAe,eAAeA,EAAK,IAAI,EAAE,EAAIA,EAAK,MAAM,YAAYA,EAAK,IAAI,GAAI,KAAM,EAAI,EACrI,GAAI,CAACgD,EACD,MAAM,IAAI,MAAM,0DAA0DhD,EAAK,IAAI,EAAE,EAEzFmC,EAAanC,EAAMgD,CAAc,CACpC,CACb,CAAS,EACD,KAAK,UAAU,QAASC,GAAM,CAC1B,GAAIvB,EAAa,WAAa,CAACA,EAAa,UAAUuB,CAAC,EACnD,OAEJ,MAAMzE,EAAQyE,EAAE,MAAM7B,EAAeA,EAAa6B,EAAE,IAAI,EAAI,YAAcA,EAAE,IAAI,EAChF,UAAWP,KAAK,KAAK,OACjB,GAAIA,EAAE,WAAaO,GAAK,CAACP,EAAE,aAAc,CACrC,MAAMQ,EAAO3B,EAASD,EAAcoB,EAAE,QAAQ,CAAC,EAK/C,GAJI,CAACQ,GAAQA,EAAK,eAGlBA,EAAK,SAAW1E,EACZgD,EAAwB,QAAQhD,CAAK,IAAM,IAC3C,SAEJgD,EAAwB,KAAKhD,CAAK,EAElC,UAAW2E,KAAQ3E,EAAM,MACjB2E,EAAK,uBACLA,EAAK,qBAAuB5B,EAASD,EAAc6B,EAAK,qBAAqB,QAAQ,CAAC,EAGjG,CAELjF,EAAO,UAAU,KAAKM,CAAK,CACvC,CAAS,EACD,KAAK,gBAAgB,QAASgB,GAAM,CAChC,GAAIkC,EAAa,WAAa,CAACA,EAAa,UAAUlC,CAAC,EACnD,OAEJ,MAAMhB,EAAQgB,EAAE,MAAM4B,EAAeA,EAAa5B,EAAE,IAAI,EAAI,YAAcA,EAAE,KAAOsC,GAC7DP,EAASD,EAAcQ,EAAU,QAAQ,CAAC,GACxCA,CACvB,EACD5D,EAAO,gBAAgB,KAAKM,CAAK,CAC7C,CAAS,EACMN,CACV,CAID,eAAgB,CACZ,GAAI,MAAK,iBAGT,CAAK,KAAK,qBACNjC,EAAM,KAAK,4EAA4E,EAE3F,KAAK,iBAAmB,GACxB,KAAK,WAAW,IAAI,EAChB,KAAK,qBACL,KAAK,MAAM,mBAAqB,KAAK,oBAEzC,UAAWmH,KAAa,KAAK,MAAM,wBAC/BA,EAAU,iBAAiB,IAAI,EAEnC,KAAK,MAAM,UAAW,EAAC,4BAA4B,OAAO,KAAK,0BAA0B,EACzF,KAAK,2BAA6B,KACrC,CAKD,WAAWnC,EAAY,KAAM,CACzB,MAAMoC,EAAa,CAAA,EACnB,KAAK,QAAQ,QAAS7D,GAAM,CACpByB,GAAa,CAACA,EAAUzB,CAAC,IAG7B,KAAK,MAAM,UAAUA,CAAC,EACtB6D,EAAW,KAAK7D,CAAC,EAC7B,CAAS,EACD,KAAK,OAAO,QAASA,GAAM,CACnByB,GAAa,CAACA,EAAUzB,CAAC,IAG7B,KAAK,MAAM,SAASA,CAAC,EACrB6D,EAAW,KAAK7D,CAAC,EAC7B,CAAS,EACD,KAAK,OAAO,QAASA,GAAM,CACnByB,GAAa,CAACA,EAAUzB,CAAC,IAG7B,KAAK,MAAM,QAAQA,CAAC,EACpB6D,EAAW,KAAK7D,CAAC,EAC7B,CAAS,EACD,KAAK,UAAU,QAASA,GAAM,CACtByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,YAAYA,CAAC,CACpC,CAAS,EACD,KAAK,WAAW,QAASA,GAAM,CACvByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,aAAaA,CAAC,CACrC,CAAS,EACD,KAAK,gBAAgB,QAASA,GAAM,CAC5ByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,kBAAkBA,CAAC,CAC1C,CAAS,EACD,KAAK,eAAe,QAASA,GAAM,CAC3ByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,iBAAiBA,CAAC,CACzC,CAAS,EACD,KAAK,UAAU,QAASA,GAAM,CACtByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,YAAYA,CAAC,CACpC,CAAS,EACD,KAAK,oBAAoB,QAASA,GAAM,CAChCyB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,sBAAsBA,CAAC,CAC9C,CAAS,EACD,KAAK,WAAW,QAASA,GAAM,CACvByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,YAAYA,CAAC,CACpC,CAAS,EACD,KAAK,eAAe,QAASA,GAAM,CAC3ByB,GAAa,CAACA,EAAUzB,CAAC,IAG7B,KAAK,MAAM,iBAAiBA,CAAC,EAC7B6D,EAAW,KAAK7D,CAAC,EAC7B,CAAS,EACD,KAAK,eAAe,QAASA,GAAM,CAC3ByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,iBAAiBA,CAAC,CACzC,CAAS,EACD,KAAK,SAAS,QAASA,GAAM,CACrByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,WAAWA,CAAC,CACnC,CAAS,EACD,KAAK,iBAAiB,QAASA,GAAM,CAC7ByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,mBAAmBA,CAAC,CAC3C,CAAS,EACD,UAAW8D,KAAaD,EAEhBC,EAAU,QAAU,KAAK,MAAM,WAAW,QAAQA,EAAU,MAAM,IAAM,KAEpEA,EAAU,UACVA,EAAU,UAAU,IAAI,EAGxBA,EAAU,OAAS,KAIlC,CAID,oBAAqB,CACZ,KAAK,qBACNrH,EAAM,KAAK,iFAAiF,EAEhG,KAAK,iBAAmB,GACxB,KAAK,gBAAgB,IAAI,EACrB,KAAK,qBAAuB,KAAK,MAAM,qBACvC,KAAK,MAAM,mBAAqB,MAEpC,UAAWmH,KAAa,KAAK,MAAM,wBAC/BA,EAAU,oBAAoB,IAAI,CAEzC,CAKD,gBAAgBnC,EAAY,KAAM,CAC9B,KAAK,QAAQ,QAASzB,GAAM,CACpByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,aAAaA,CAAC,CACrC,CAAS,EACD,KAAK,OAAO,QAASA,GAAM,CACnByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,YAAYA,CAAC,CACpC,CAAS,EACD,KAAK,OAAO,QAASA,GAAM,CACnByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,WAAWA,EAAG,EAAI,CACzC,CAAS,EACD,KAAK,UAAU,QAASA,GAAM,CACtByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,eAAeA,CAAC,CACvC,CAAS,EACD,KAAK,WAAW,QAASA,GAAM,CACvByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,gBAAgBA,CAAC,CACxC,CAAS,EACD,KAAK,gBAAgB,QAASA,GAAM,CAC5ByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,qBAAqBA,CAAC,CAC7C,CAAS,EACD,KAAK,eAAe,QAASA,GAAM,CAC3ByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,oBAAoBA,CAAC,CAC5C,CAAS,EACD,KAAK,UAAU,QAASA,GAAM,CACtByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,eAAeA,CAAC,CACvC,CAAS,EACD,KAAK,oBAAoB,QAASA,GAAM,CAChCyB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,yBAAyBA,CAAC,CACjD,CAAS,EACD,KAAK,WAAW,QAASA,GAAM,CACvByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,eAAeA,CAAC,CACvC,CAAS,EACD,KAAK,eAAe,QAASA,GAAM,CAC3ByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,oBAAoBA,CAAC,CAC5C,CAAS,EACD,KAAK,eAAe,QAASA,GAAM,CAC3ByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,oBAAoBA,CAAC,CAC5C,CAAS,EACD,KAAK,SAAS,QAASA,GAAM,CACrByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,cAAcA,CAAC,CACtC,CAAS,EACD,KAAK,iBAAiB,QAASA,GAAM,CAC7ByB,GAAa,CAACA,EAAUzB,CAAC,GAG7B,KAAK,MAAM,sBAAsBA,CAAC,CAC9C,CAAS,CACJ,CAID,SAAU,CACN,KAAK,QAAQ,MAAM,CAAC,EAAE,QAASA,GAAM,CACjCA,EAAE,QAAO,CACrB,CAAS,EACD,KAAK,QAAQ,OAAS,EACtB,KAAK,OAAO,MAAM,CAAC,EAAE,QAASA,GAAM,CAChCA,EAAE,QAAO,CACrB,CAAS,EACD,KAAK,OAAO,OAAS,EACrB,KAAK,OAAO,MAAM,CAAC,EAAE,QAASA,GAAM,CAChCA,EAAE,QAAO,CACrB,CAAS,EACD,KAAK,OAAO,OAAS,EACrB,KAAK,UAAU,MAAM,CAAC,EAAE,QAASA,GAAM,CACnCA,EAAE,QAAO,CACrB,CAAS,EACD,KAAK,UAAU,OAAS,EACxB,KAAK,gBAAgB,MAAM,CAAC,EAAE,QAASA,GAAM,CACzCA,EAAE,QAAO,CACrB,CAAS,EACD,KAAK,gBAAgB,OAAS,EAC9B,KAAK,eAAe,MAAM,CAAC,EAAE,QAASA,GAAM,CACxCA,EAAE,QAAO,CACrB,CAAS,EACD,KAAK,eAAe,OAAS,EAC7B,KAAK,UAAU,MAAM,CAAC,EAAE,QAASA,GAAM,CACnCA,EAAE,QAAO,CACrB,CAAS,EACD,KAAK,UAAU,OAAS,EACxB,KAAK,WAAW,MAAM,CAAC,EAAE,QAASA,GAAM,CACpCA,EAAE,QAAO,CACrB,CAAS,EACD,KAAK,WAAW,OAAS,EACzB,KAAK,eAAe,MAAM,CAAC,EAAE,QAASA,GAAM,CACxCA,EAAE,QAAO,CACrB,CAAS,EACD,KAAK,eAAe,OAAS,EAC7B,KAAK,eAAe,MAAM,CAAC,EAAE,QAASA,GAAM,CACxCA,EAAE,QAAO,CACrB,CAAS,EACD,KAAK,eAAe,OAAS,EAC7B,KAAK,SAAS,MAAM,CAAC,EAAE,QAASA,GAAM,CAClCA,EAAE,QAAO,CACrB,CAAS,EACD,KAAK,SAAS,OAAS,EACvB,KAAK,iBAAiB,MAAM,CAAC,EAAE,QAASA,GAAM,CAC1CA,EAAE,QAAO,CACrB,CAAS,EACD,KAAK,iBAAiB,OAAS,EAC/B,KAAK,oBAAoB,MAAM,CAAC,EAAE,QAASA,GAAM,CAC7CA,EAAE,QAAO,CACrB,CAAS,EACD,KAAK,oBAAoB,OAAS,EAC9B,KAAK,qBACL,KAAK,mBAAmB,UACxB,KAAK,mBAAqB,MAE9B,UAAW4D,KAAa,KAAK,MAAM,wBAC/BA,EAAU,oBAAoB,KAAM,EAAI,EAExC,KAAK,6BACL,KAAK,MAAM,UAAW,EAAC,4BAA4B,OAAO,KAAK,0BAA0B,EACzF,KAAK,2BAA6B,KAEzC,CACD,YAAYG,EAAcC,EAAcC,EAAY,CAChD,GAAI,GAACF,GAAgB,CAACC,GAGtB,UAAWE,KAASH,EAAc,CAC9B,IAAII,EAAO,GACX,GAAIF,GACA,UAAWG,KAAaH,EACpB,GAAIC,IAAUE,EAAW,CACrBD,EAAO,GACP,KACH,EAGLA,IACAH,EAAa,KAAKE,CAAK,EACvBA,EAAM,iBAAmB,KAEhC,CACJ,CAKD,iBAAiBD,EAAY,CACzB,KAAK,iBAAmB,GACpBA,IAAe,SACfA,EAAa,IAAInE,GAErB,UAAW3D,KAAO,KACV,OAAO,UAAU,eAAe,KAAK,KAAMA,CAAG,IAC9C,KAAKA,CAAG,EAAI,KAAKA,CAAG,IAAMA,IAAQ,sBAAwB,KAAO,CAAA,GACjE,KAAK,YAAY,KAAK,MAAMA,CAAG,EAAG,KAAKA,CAAG,EAAG8H,EAAW9H,CAAG,CAAC,GAGpE,KAAK,mBAAqB,KAAK,MAAM,mBACrC,KAAK,mBAAkB,CAC1B,CAKD,gBAAiB,CACb,MAAMkI,EAAW,IAAIvI,EAAK,yBAA0B,KAAK,KAAK,EAC9D,YAAK,OAAO,QAASoH,GAAM,CAClBA,EAAE,QACHmB,EAAS,SAASnB,CAAC,CAEnC,CAAS,EACD,KAAK,OAAO,QAAQmB,CAAQ,EACrBA,CACV,CAQD,kBAAkBnE,EAAQC,EAAY,iBAAkBmE,EAAaC,EAAkB,KAAM,CACzF,GAAI,CAACrE,EACD,OAAAvD,EAAO,MAAM,2CAA2C,EACjD,GAEX,MAAM6H,EAAmBD,IAElBE,GAAW,CACV,IAAIjE,EAAO,KACX,MAAMkE,EAAiBD,EAAO,WAAW,OAASA,EAAO,WAAW,CAAC,EAAE,eAAiB,GASlF1I,EAAO0I,EAAO,KAAK,MAAM,GAAG,EAAE,KAAK,EAAE,EAAE,MAAM,YAAY,EAAE,CAAC,EAClE,OAAQC,EAAc,CAClB,IAAK,WACL,IAAK,qBACDlE,EAAON,EAAM,uBAAuBuE,EAAO,IAAI,GAAKvE,EAAM,uBAAuBnE,CAAI,EACrF,MACJ,IAAK,YACDyE,EAAON,EAAM,qBAAqBuE,EAAO,IAAI,GAAKvE,EAAM,qBAAqBnE,CAAI,EACjF,MACJ,QACIyE,EAAON,EAAM,cAAcuE,EAAO,IAAI,GAAKvE,EAAM,cAAcnE,CAAI,CAC1E,CACD,OAAOyE,CACvB,GAE0B,KAAK,WACb,QAASmE,GAAa,CAC5B,MAAMC,EAAcJ,EAAiBG,CAAQ,EAC7C,GAAIC,IAAgB,KAAM,CAEtB,UAAWC,KAAiBF,EAAS,WAAY,CAE7C,MAAMG,EAA6BF,EAAY,WAAW,OAAQG,GACvDA,EAAiB,iBAAmBF,EAAc,cAC5D,EACD,UAAWG,KAA6BF,EAA4B,CAChE,MAAMxG,EAAQsG,EAAY,WAAW,QAAQI,EAA2B,CAAC,EACrE1G,EAAQ,IACRsG,EAAY,WAAW,OAAOtG,EAAO,CAAC,CAE7C,CACJ,CAEDsG,EAAY,WAAaA,EAAY,WAAW,OAAOD,EAAS,UAAU,CAC7E,CACb,CAAS,EACD,MAAMM,EAAqB,CAAA,EAE3B,YAAK,gBAAgB,MAAO,EAAC,QAASC,GAAuB,CAEzDD,EAAmB,KAAKC,EAAmB,MAAMA,EAAmB,KAAMV,CAAgB,CAAC,EAE3FU,EAAmB,YAAY,QAASC,GAAe,CACnDA,EAAW,KAAI,CAC/B,CAAa,CACb,CAAS,EAEDb,EAAY,QAASa,GAAe,CAChC,MAAMV,EAASD,EAAiBW,EAAW,MAAM,EAC7CV,IAEAvE,EAAM,eAAeuE,EAAQU,EAAW,UAAWA,EAAW,QAASA,EAAW,cAAeA,EAAW,WAAYA,EAAW,eAAiBA,EAAW,eAAiB,OAAW,OAAW,GAAM,OAAWA,EAAW,gBAAkBA,EAAW,gBAAkB,MAAS,EAE1RjF,EAAM,cAAciF,EAAW,MAAM,EAErD,CAAS,EACMF,CACV,CAOD,mBAAoB,CAChB,KAAK,UAAU,OAAS,EACxB,KAAK,OAAO,QAAS/B,GAAM,CACnB,CAACA,EAAE,QAAU,KAAK,UAAU,QAAQA,CAAC,IAAM,IAC3C,KAAK,UAAU,KAAKA,CAAC,CAErC,CAAS,EACD,KAAK,eAAe,QAASkC,GAAM,CAC3B,CAACA,EAAE,QAAU,KAAK,UAAU,QAAQA,CAAC,IAAM,IAC3C,KAAK,UAAU,KAAKA,CAAC,CAErC,CAAS,EACD,KAAK,OAAO,QAASC,GAAM,CACnB,CAACA,EAAE,QAAU,KAAK,UAAU,QAAQA,CAAC,IAAM,IAC3C,KAAK,UAAU,KAAKA,CAAC,CAErC,CAAS,EACD,KAAK,QAAQ,QAASC,GAAM,CACpB,CAACA,EAAE,QAAU,KAAK,UAAU,QAAQA,CAAC,IAAM,IAC3C,KAAK,UAAU,KAAKA,CAAC,CAErC,CAAS,CACJ,CAMD,wBAAwBC,EAAM,CAC1B,GAAI,CAACA,EACD,OAEJ,MAAMC,EAAe,CAAA,EACfC,EAAe,IAAI,IAEzB,IADAD,EAAa,KAAKD,CAAI,EACfC,EAAa,OAAS,GAAG,CAC5B,MAAMtE,EAAcsE,EAAa,MAgBjC,GAfItE,aAAuBpF,GACnBoF,EAAY,UAAY,KAAK,WAAW,QAAQA,EAAY,QAAQ,IAAM,IAC1E,KAAK,WAAW,KAAKA,EAAY,QAAQ,EAE7C,KAAK,OAAO,KAAKA,CAAW,GAEvBA,aAAuBnD,EAC5B,KAAK,eAAe,KAAKmD,CAAW,EAE/BA,aAAuBQ,EAC5B,KAAK,OAAO,KAAKR,CAAW,EAEvBA,aAAuBS,GAC5B,KAAK,QAAQ,KAAKT,CAAW,EAE7BA,aAAuB9E,EAAc,CACrC,GAAI8E,EAAY,UAAY,KAAK,UAAU,QAAQA,EAAY,QAAQ,IAAM,GAAI,CAC7E,KAAK,UAAU,KAAKA,EAAY,QAAQ,EACxC,UAAWZ,KAAWY,EAAY,SAAS,kBAAiB,EACpD,KAAK,SAAS,QAAQZ,CAAO,IAAM,IACnC,KAAK,SAAS,KAAKA,CAAO,CAGrC,CACGY,EAAY,UAAY,KAAK,UAAU,QAAQA,EAAY,QAAQ,IAAM,IACzE,KAAK,UAAU,KAAKA,EAAY,QAAQ,EAExCA,EAAY,oBAAsB,KAAK,oBAAoB,QAAQA,EAAY,kBAAkB,IAAM,IACvG,KAAK,oBAAoB,KAAKA,EAAY,kBAAkB,CAEnE,CACD,UAAWjC,KAASiC,EAAY,cACvBuE,EAAa,IAAIxG,CAAK,GACvBuG,EAAa,KAAKvG,CAAK,EAG/BwG,EAAa,IAAIvE,CAAW,CAC/B,CACD,KAAK,kBAAiB,CACzB,CAQD,WAAWI,EAAMoE,EAAWC,EAAQ,CAChC,GAAID,IAAc,OAEd,OAAOpE,EAEX,MAAMsE,EAAa,CAAA,EACnB,UAAWC,KAAKvE,EAAM,CAClB,MAAMwE,EAAOxE,EAAKuE,CAAC,EACfE,GAAQA,EAAK,aAAaD,EAAMJ,CAAS,IAAM,CAACC,GAAUA,EAAOG,CAAI,IACrEF,EAAW,KAAKE,CAAI,CAE3B,CACD,OAAOF,CACV,CAOD,gBAAgBF,EAAWC,EAAQ,CAC/B,OAAO,KAAK,WAAW,KAAK,OAAQD,EAAWC,CAAM,CACxD,CAOD,iBAAiBD,EAAWC,EAAQ,CAChC,OAAO,KAAK,WAAW,KAAK,QAASD,EAAWC,CAAM,CACzD,CAOD,gBAAgBD,EAAWC,EAAQ,CAC/B,OAAO,KAAK,WAAW,KAAK,OAAQD,EAAWC,CAAM,CACxD,CAOD,mBAAmBD,EAAWC,EAAQ,CAClC,OAAO,KAAK,WAAW,KAAK,UAAWD,EAAWC,CAAM,EAAE,OAAO,KAAK,WAAW,KAAK,eAAgBD,EAAWC,CAAM,CAAC,CAC3H,CAOD,wBAAwBD,EAAWC,EAAQ,CACvC,OAAO,KAAK,WAAW,KAAK,eAAgBD,EAAWC,CAAM,CAChE,CACL", "x_google_ignoreList": [0, 1]}