"""
美化的通知组件
提供各种类型的通知消息
"""

from PyQt6.QtWidgets import (QWidget, QLabel, QHBoxLayout, QVBoxLayout, 
                            QPushButton, QGraphicsOpacityEffect, QApplication)
from PyQt6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal
from PyQt6.QtGui import QFont, QPainter, QLinearGradient, QColor, QPen

class NotificationWidget(QWidget):
    """通知组件"""
    
    closed = pyqtSignal()
    
    def __init__(self, title, message, notification_type="info", duration=3000, parent=None):
        super().__init__(parent)
        self.notification_type = notification_type
        self.duration = duration
        
        self.init_ui(title, message)
        self.setup_animations()
        
        # 自动关闭定时器
        if duration > 0:
            QTimer.singleShot(duration, self.close_notification)
            
    def init_ui(self, title, message):
        """初始化UI"""
        self.setFixedSize(350, 100)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # 主布局
        layout = QHBoxLayout()
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(12)
        
        # 图标
        icon_label = QLabel()
        icon_label.setFont(QFont("Segoe UI Emoji", 20))
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setFixedSize(40, 40)
        
        # 根据类型设置图标
        if self.notification_type == "success":
            icon_label.setText("✅")
        elif self.notification_type == "error":
            icon_label.setText("❌")
        elif self.notification_type == "warning":
            icon_label.setText("⚠️")
        else:  # info
            icon_label.setText("ℹ️")
            
        # 内容区域
        content_layout = QVBoxLayout()
        content_layout.setSpacing(5)
        
        # 标题
        title_label = QLabel(title)
        title_label.setFont(QFont("Microsoft YaHei UI", 12, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #f0f6fc;")
        
        # 消息
        message_label = QLabel(message)
        message_label.setFont(QFont("Microsoft YaHei UI", 10))
        message_label.setStyleSheet("color: #8b949e;")
        message_label.setWordWrap(True)
        
        content_layout.addWidget(title_label)
        content_layout.addWidget(message_label)
        content_layout.addStretch()
        
        # 关闭按钮
        close_button = QPushButton("×")
        close_button.setFixedSize(20, 20)
        close_button.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        close_button.setStyleSheet("""
            QPushButton {
                background: transparent;
                color: #8b949e;
                border: none;
                border-radius: 10px;
            }
            QPushButton:hover {
                background: #30363d;
                color: #f0f6fc;
            }
        """)
        close_button.clicked.connect(self.close_notification)
        
        layout.addWidget(icon_label)
        layout.addLayout(content_layout)
        layout.addWidget(close_button, alignment=Qt.AlignmentFlag.AlignTop)
        
        self.setLayout(layout)
        
    def setup_animations(self):
        """设置动画"""
        # 透明度效果
        self.opacity_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.opacity_effect)
        
        # 淡入动画
        self.fade_in_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_in_animation.setDuration(300)
        self.fade_in_animation.setStartValue(0.0)
        self.fade_in_animation.setEndValue(1.0)
        self.fade_in_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # 淡出动画
        self.fade_out_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_out_animation.setDuration(300)
        self.fade_out_animation.setStartValue(1.0)
        self.fade_out_animation.setEndValue(0.0)
        self.fade_out_animation.setEasingCurve(QEasingCurve.Type.InCubic)
        self.fade_out_animation.finished.connect(self.hide)
        self.fade_out_animation.finished.connect(self.closed.emit)
        
    def paintEvent(self, event):
        """绘制背景"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 根据类型设置颜色
        if self.notification_type == "success":
            start_color = QColor("#39d353")
            end_color = QColor("#2ea043")
        elif self.notification_type == "error":
            start_color = QColor("#f85149")
            end_color = QColor("#da3633")
        elif self.notification_type == "warning":
            start_color = QColor("#d29922")
            end_color = QColor("#bf8700")
        else:  # info
            start_color = QColor("#58a6ff")
            end_color = QColor("#1f6feb")
            
        # 创建渐变
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, start_color)
        gradient.setColorAt(1, end_color)
        
        # 绘制背景
        painter.setBrush(gradient)
        painter.setPen(QPen(QColor("#30363d"), 2))
        painter.drawRoundedRect(self.rect().adjusted(1, 1, -1, -1), 12, 12)
        
    def show_notification(self):
        """显示通知"""
        self.show()
        self.fade_in_animation.start()
        
    def close_notification(self):
        """关闭通知"""
        self.fade_out_animation.start()

class NotificationManager:
    """通知管理器"""
    
    def __init__(self):
        self.notifications = []
        self.max_notifications = 5
        self.spacing = 10
        
    def show_notification(self, title, message, notification_type="info", duration=3000):
        """显示通知"""
        # 创建通知
        notification = NotificationWidget(title, message, notification_type, duration)
        notification.closed.connect(lambda: self.remove_notification(notification))
        
        # 添加到列表
        self.notifications.append(notification)
        
        # 限制通知数量
        if len(self.notifications) > self.max_notifications:
            oldest = self.notifications.pop(0)
            oldest.close_notification()
            
        # 重新排列通知位置
        self.arrange_notifications()
        
        # 显示通知
        notification.show_notification()
        
    def remove_notification(self, notification):
        """移除通知"""
        if notification in self.notifications:
            self.notifications.remove(notification)
            self.arrange_notifications()
            
    def arrange_notifications(self):
        """排列通知位置"""
        screen = QApplication.primaryScreen().geometry()
        
        for i, notification in enumerate(self.notifications):
            x = screen.width() - notification.width() - 20
            y = 20 + i * (notification.height() + self.spacing)
            notification.move(x, y)
            
    def show_success(self, title, message, duration=3000):
        """显示成功通知"""
        self.show_notification(title, message, "success", duration)
        
    def show_error(self, title, message, duration=5000):
        """显示错误通知"""
        self.show_notification(title, message, "error", duration)
        
    def show_warning(self, title, message, duration=4000):
        """显示警告通知"""
        self.show_notification(title, message, "warning", duration)
        
    def show_info(self, title, message, duration=3000):
        """显示信息通知"""
        self.show_notification(title, message, "info", duration)

# 全局通知管理器实例
notification_manager = NotificationManager()

# 便捷函数
def show_success(title, message, duration=3000):
    """显示成功通知"""
    notification_manager.show_success(title, message, duration)

def show_error(title, message, duration=5000):
    """显示错误通知"""
    notification_manager.show_error(title, message, duration)

def show_warning(title, message, duration=4000):
    """显示警告通知"""
    notification_manager.show_warning(title, message, duration)

def show_info(title, message, duration=3000):
    """显示信息通知"""
    notification_manager.show_info(title, message, duration)
