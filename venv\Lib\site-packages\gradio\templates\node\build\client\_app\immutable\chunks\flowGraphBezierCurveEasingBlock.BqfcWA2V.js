import{aG as a,R as u}from"./index.BoI39RQH.js";import{F as c}from"./KHR_interactivity.DEAVS2UW.js";import{b as h,f as r,R as g}from"./declarationMapper.UBCwU7BT.js";class l extends c{constructor(t){super(t),this.config=t,this._easingFunctions={},this.mode=this.registerDataInput("mode",h,0),this.controlPoint1=this.registerDataInput("controlPoint1",r),this.controlPoint2=this.registerDataInput("controlPoint2",r),this.easingFunction=this.registerDataOutput("easingFunction",g)}_updateOutputs(t){const e=this.mode.getValue(t),i=this.controlPoint1.getValue(t),s=this.controlPoint2.getValue(t);if(e===void 0)return;const n=`${e}-${i.x}-${i.y}-${s.x}-${s.y}`;if(!this._easingFunctions[n]){const o=new a(i.x,i.y,s.x,s.y);o.setEasingMode(e),this._easingFunctions[n]=o}this.easingFunction.setValue(this._easingFunctions[n],t)}getClassName(){return"FlowGraphBezierCurveEasing"}}u("FlowGraphBezierCurveEasing",l);export{l as FlowGraphBezierCurveEasingBlock};
//# sourceMappingURL=flowGraphBezierCurveEasingBlock.BqfcWA2V.js.map
