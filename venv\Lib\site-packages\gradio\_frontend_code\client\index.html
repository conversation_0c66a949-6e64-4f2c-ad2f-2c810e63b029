<!doctype html>

<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Client</title>
		<script type="module">
			import { Client } from "./dist/index.js";
			console.log(Client);

			const client = await Client.connect("pngwn/chatinterface_streaming_echo");
			async function run(message, n) {
				// console.log(client);
				const req = client.submit("/chat", {
					message
				});
				console.log("start");
				for await (const c of req) {
					if (c.type === "data") {
						console.log(`${n}: ${c.data[0]}`);
					}
				}

				console.log("end");

				return "hi";
			}

			run("My name is frank", 1);
			run("Hello there", 2);

			console.log("boo");
		</script>
	</head>
	<body>
		<div id="app"></div>
	</body>
</html>
