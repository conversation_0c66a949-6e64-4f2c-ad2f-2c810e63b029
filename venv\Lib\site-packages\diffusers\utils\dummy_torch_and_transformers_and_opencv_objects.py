# This file is autogenerated by the command `make fix-copies`, do not edit.
from ..utils import DummyObject, requires_backends


class ConsisIDPipeline(metaclass=DummyObject):
    _backends = ["torch", "transformers", "opencv"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["torch", "transformers", "opencv"])

    @classmethod
    def from_config(cls, *args, **kwargs):
        requires_backends(cls, ["torch", "transformers", "opencv"])

    @classmethod
    def from_pretrained(cls, *args, **kwargs):
        requires_backends(cls, ["torch", "transformers", "opencv"])
