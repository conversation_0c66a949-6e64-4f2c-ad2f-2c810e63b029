import{F as t,g as i}from"./KHR_interactivity.DEAVS2UW.js";import{R as s,F as l}from"./declarationMapper.UBCwU7BT.js";import{R as u}from"./index.BoI39RQH.js";class h extends t{constructor(e){super(e),this.config=e,this.array=this.registerDataInput("array",s),this.index=this.registerDataInput("index",s,new l(-1)),this.value=this.registerDataOutput("value",s)}_updateOutputs(e){const a=this.array.getValue(e),r=i(this.index.getValue(e));a&&r>=0&&r<a.length?this.value.setValue(a[r],e):this.value.setValue(null,e)}serialize(e){super.serialize(e)}getClassName(){return"FlowGraphArrayIndexBlock"}}u("FlowGraphArrayIndexBlock",h);export{h as FlowGraphArrayIndexBlock};
//# sourceMappingURL=flowGraphArrayIndexBlock.B03sfcvL.js.map
