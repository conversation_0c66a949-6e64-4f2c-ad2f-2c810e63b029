import{F as e,j as i}from"./declarationMapper-BZjsjg7g.js";import{b as r}from"./KHR_interactivity-DTxiAnOo.js";import{R as a}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class u extends r{constructor(t={}){super(t),this.config=t,this.config.startIndex=t.startIndex??new e(0),this.reset=this._registerSignalInput("reset"),this.maxExecutions=this.registerDataInput("maxExecutions",i),this.executionCount=this.registerDataOutput("executionCount",i,new e(0))}_execute(t,o){if(o===this.reset)this.executionCount.setValue(this.config.startIndex,t);else{const s=this.executionCount.getValue(t);s.value<this.maxExecutions.getValue(t).value&&(this.executionCount.setValue(new e(s.value+1),t),this.out._activateSignal(t))}}getClassName(){return"FlowGraphDoNBlock"}}a("FlowGraphDoNBlock",u);export{u as FlowGraphDoNBlock};
//# sourceMappingURL=flowGraphDoNBlock-C_MpXPTz.js.map
