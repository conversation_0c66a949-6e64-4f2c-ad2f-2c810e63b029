import{ar as d,an as c,ao as h}from"./index-Dpxo-yl_.js";import{GLTFLoader as m}from"./glTFLoader-9Z3KGax5.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./bone-kZWM5-u7.js";import"./rawTexture-DmvUfjqF.js";import"./assetContainer-BRzQBugc.js";import"./objectModelMapping-BR4RdEzn.js";const t="MSFT_minecraftMesh";class u{constructor(r){this.name=t,this._loader=r,this.enabled=this._loader.isExtensionUsed(t)}dispose(){this._loader=null}loadMaterialPropertiesAsync(r,i,e){return m.LoadExtraAsync(r,i,this.name,(o,n)=>{if(n){if(!(e instanceof d))throw new Error(`${o}: Material type not supported`);const p=this._loader.loadMaterialPropertiesAsync(r,i,e);return e.needAlphaBlending()&&(e.forceDepthWrite=!0,e.separateCullingPass=!0),e.backFaceCulling=e.forceDepthWrite,e.twoSidedLighting=!0,p}return null})}}c(t);h(t,!0,s=>new u(s));export{u as MSFT_minecraftMesh};
//# sourceMappingURL=MSFT_minecraftMesh-B615P8cV.js.map
