const _=new URL("sveltekit-internal://");function S(e,r){if(r[0]==="/"&&r[1]==="/")return r;let t=new URL(e,_);return t=new URL(r,t),t.protocol===_.protocol?t.pathname+t.search+t.hash:t.href}function b(e,r){return e==="/"||r==="ignore"?e:r==="never"?e.endsWith("/")?e.slice(0,-1):e:r==="always"&&!e.endsWith("/")?e+"/":e}function m(e){return e.split("%25").map(decodeURI).join("%25")}function P(e){for(const r in e)e[r]=decodeURIComponent(e[r]);return e}const w=["href","pathname","search","toString","toJSON"];function j(e,r,t){const n=new URL(e);Object.defineProperty(n,"searchParams",{value:new Proxy(n.searchParams,{get(s,o){if(o==="get"||o==="getAll"||o==="has")return f=>(t(f),s[o](f));r();const a=Reflect.get(s,o);return typeof a=="function"?a.bind(s):a}}),enumerable:!0,configurable:!0});for(const s of w)Object.defineProperty(n,s,{get(){return r(),e[s]},enumerable:!0,configurable:!0});return n[Symbol.for("nodejs.util.inspect.custom")]=(s,o,a)=>a(e,o),$(n),n}function $(e){h(e),Object.defineProperty(e,"hash",{get(){throw new Error("Cannot access event.url.hash. Consider using `$page.url.hash` inside a component instead")}});}function y(e){h(e);for(const r of ["search","searchParams"])Object.defineProperty(e,r,{get(){throw new Error(`Cannot access url.${r} on a page with prerendering enabled`)}});}function h(e){e[Symbol.for("nodejs.util.inspect.custom")]=(r,t,n)=>n(new URL(e),t);}const l="/__data.json",c=".html__data.json";function U(e){return e.endsWith(l)||e.endsWith(c)}function O(e){return e.endsWith(".html")?e.replace(/\.html$/,c):e.replace(/\/$/,"")+l}function T(e){return e.endsWith(c)?e.slice(0,-c.length)+".html":e.slice(0,-l.length)}const u=new Set(["load","prerender","csr","ssr","trailingSlash","config"]);new Set([...u,"entries"]);const d=new Set([...u]);new Set([...d,"actions","entries"]);

export { O, P, S, T, U, b, j, m, y };
//# sourceMappingURL=exports-Cu4i9J_Z.js.map
