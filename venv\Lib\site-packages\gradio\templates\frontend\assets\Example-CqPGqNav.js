const{SvelteComponent:z,append:k,attr:g,destroy_each:H,detach:o,element:d,empty:B,ensure_array_like:b,flush:m,init:D,insert:a,listen:y,noop:v,run_all:F,safe_not_equal:G,set_data:L,space:M,text:S,toggle_class:u}=window.__gradio__svelte__internal;function p(c,e,t){const l=c.slice();return l[9]=e[t],l[11]=t,l}function w(c,e,t){const l=c.slice();return l[12]=e[t],l[14]=t,l}function I(c){let e,t,l;function i(r,s){return typeof r[0]=="string"?N:r[6]?K:J}let f=i(c),n=f(c);return{c(){e=d("div"),n.c(),g(e,"class","svelte-hn96gn"),u(e,"table",c[1]==="table"),u(e,"gallery",c[1]==="gallery"),u(e,"selected",c[2])},m(r,s){a(r,e,s),n.m(e,null),t||(l=[y(e,"mouseenter",c[7]),y(e,"mouseleave",c[8])],t=!0)},p(r,s){f===(f=i(r))&&n?n.p(r,s):(n.d(1),n=f(r),n&&(n.c(),n.m(e,null))),s&2&&u(e,"table",r[1]==="table"),s&2&&u(e,"gallery",r[1]==="gallery"),s&4&&u(e,"selected",r[2])},d(r){r&&o(e),n.d(),t=!1,F(l)}}}function J(c){let e,t,l=b(c[0].slice(0,3)),i=[];for(let n=0;n<l.length;n+=1)i[n]=E(p(c,l,n));let f=c[0].length>3&&q(c);return{c(){e=d("table");for(let n=0;n<i.length;n+=1)i[n].c();t=M(),f&&f.c(),g(e,"class"," svelte-hn96gn")},m(n,r){a(n,e,r);for(let s=0;s<i.length;s+=1)i[s]&&i[s].m(e,null);k(e,t),f&&f.m(e,null)},p(n,r){if(r&1){l=b(n[0].slice(0,3));let s;for(s=0;s<l.length;s+=1){const h=p(n,l,s);i[s]?i[s].p(h,r):(i[s]=E(h),i[s].c(),i[s].m(e,t))}for(;s<i.length;s+=1)i[s].d(1);i.length=l.length}n[0].length>3?f?f.p(n,r):(f=q(n),f.c(),f.m(e,null)):f&&(f.d(1),f=null)},d(n){n&&o(e),H(i,n),f&&f.d()}}}function K(c){let e;return{c(){e=d("table"),e.innerHTML='<tr><td class="svelte-hn96gn">Empty</td></tr>',g(e,"class"," svelte-hn96gn")},m(t,l){a(t,e,l)},p:v,d(t){t&&o(e)}}}function N(c){let e;return{c(){e=S(c[0])},m(t,l){a(t,e,l)},p(t,l){l&1&&L(e,t[0])},d(t){t&&o(e)}}}function A(c){let e,t=c[12]+"",l;return{c(){e=d("td"),l=S(t),g(e,"class","svelte-hn96gn")},m(i,f){a(i,e,f),k(e,l)},p(i,f){f&1&&t!==(t=i[12]+"")&&L(l,t)},d(i){i&&o(e)}}}function C(c){let e;return{c(){e=d("td"),e.textContent="…",g(e,"class","svelte-hn96gn")},m(t,l){a(t,e,l)},d(t){t&&o(e)}}}function E(c){let e,t,l=b(c[9].slice(0,3)),i=[];for(let n=0;n<l.length;n+=1)i[n]=A(w(c,l,n));let f=c[9].length>3&&C();return{c(){e=d("tr");for(let n=0;n<i.length;n+=1)i[n].c();t=M(),f&&f.c()},m(n,r){a(n,e,r);for(let s=0;s<i.length;s+=1)i[s]&&i[s].m(e,null);k(e,t),f&&f.m(e,null)},p(n,r){if(r&1){l=b(n[9].slice(0,3));let s;for(s=0;s<l.length;s+=1){const h=w(n,l,s);i[s]?i[s].p(h,r):(i[s]=A(h),i[s].c(),i[s].m(e,t))}for(;s<i.length;s+=1)i[s].d(1);i.length=l.length}n[9].length>3?f||(f=C(),f.c(),f.m(e,null)):f&&(f.d(1),f=null)},d(n){n&&o(e),H(i,n),f&&f.d()}}}function q(c){let e;return{c(){e=d("div"),g(e,"class","overlay svelte-hn96gn"),u(e,"odd",c[3]%2!=0),u(e,"even",c[3]%2==0),u(e,"button",c[1]==="gallery")},m(t,l){a(t,e,l)},p(t,l){l&8&&u(e,"odd",t[3]%2!=0),l&8&&u(e,"even",t[3]%2==0),l&2&&u(e,"button",t[1]==="gallery")},d(t){t&&o(e)}}}function O(c){let e,t=c[5]&&I(c);return{c(){t&&t.c(),e=B()},m(l,i){t&&t.m(l,i),a(l,e,i)},p(l,[i]){l[5]&&t.p(l,i)},i:v,o:v,d(l){l&&o(e),t&&t.d(l)}}}function P(c,e,t){let{value:l}=e,{type:i}=e,{selected:f=!1}=e,{index:n}=e,r=!1,s=Array.isArray(l),h=s&&(l.length===0||l[0].length===0);const T=()=>t(4,r=!0),j=()=>t(4,r=!1);return c.$$set=_=>{"value"in _&&t(0,l=_.value),"type"in _&&t(1,i=_.type),"selected"in _&&t(2,f=_.selected),"index"in _&&t(3,n=_.index)},[l,i,f,n,r,s,h,T,j]}class Q extends z{constructor(e){super(),D(this,e,P,O,G,{value:0,type:1,selected:2,index:3})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),m()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),m()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),m()}get index(){return this.$$.ctx[3]}set index(e){this.$$set({index:e}),m()}}export{Q as default};
//# sourceMappingURL=Example-CqPGqNav.js.map
