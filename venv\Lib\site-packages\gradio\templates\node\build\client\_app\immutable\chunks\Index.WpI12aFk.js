import{SvelteComponent as M,init as Q,safe_not_equal as R,create_slot as W,element as T,space as y,claim_element as N,children as q,detach as k,claim_space as E,attr as p,toggle_class as O,insert_hydration as C,append_hydration as g,listen as U,is_function as K,update_slot_base as X,get_all_dirty_from_scope as Y,get_slot_changes as Z,transition_in as x,transition_out as $,text as D,claim_text as V,set_data as P,empty as z,get_svelte_dataset as I,run_all as S,noop as ee,ensure_array_like as j,destroy_each as te}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function A(t,e,l){const s=t.slice();return s[16]=e[l],s}function F(t){let e,l,s,n,d,m;return{c(){e=T("div"),l=T("span"),s=D(t[2]),n=D(":"),d=D(" "),m=D(t[3]),this.h()},l(u){e=N(u,"DIV",{class:!0});var i=q(e);l=N(i,"SPAN",{class:!0});var b=q(l);s=V(b,t[2]),n=V(b,":"),b.forEach(k),d=V(i," "),m=V(i,t[3]),i.forEach(k),this.h()},h(){p(l,"class","svelte-1ccw3kh"),p(e,"class","component-name svelte-1ccw3kh")},m(u,i){C(u,e,i),g(e,l),g(l,s),g(l,n),g(e,d),g(e,m)},p(u,i){i&4&&P(s,u[2]),i&8&&P(m,u[3])},d(u){u&&k(e)}}}function le(t){let e,l="+",s,n,d="+",m,u,i="+",b,w,f="+",r,v,c,B,a=!t[1]&&G(t);return{c(){e=T("button"),e.textContent=l,s=y(),n=T("button"),n.textContent=d,m=y(),u=T("button"),u.textContent=i,b=y(),w=T("button"),w.textContent=f,r=y(),a&&a.c(),v=z(),this.h()},l(o){e=N(o,"BUTTON",{class:!0,"data-svelte-h":!0}),I(e)!=="svelte-9vgc4e"&&(e.textContent=l),s=E(o),n=N(o,"BUTTON",{class:!0,"data-svelte-h":!0}),I(n)!=="svelte-pewocm"&&(n.textContent=d),m=E(o),u=N(o,"BUTTON",{class:!0,"data-svelte-h":!0}),I(u)!=="svelte-4vsur8"&&(u.textContent=i),b=E(o),w=N(o,"BUTTON",{class:!0,"data-svelte-h":!0}),I(w)!=="svelte-c945mq"&&(w.textContent=f),r=E(o),a&&a.l(o),v=z(),this.h()},h(){p(e,"class","add up svelte-1ccw3kh"),p(n,"class","add left svelte-1ccw3kh"),p(u,"class","add right svelte-1ccw3kh"),p(w,"class","add down svelte-1ccw3kh")},m(o,_){C(o,e,_),C(o,s,_),C(o,n,_),C(o,m,_),C(o,u,_),C(o,b,_),C(o,w,_),C(o,r,_),a&&a.m(o,_),C(o,v,_),c||(B=[U(e,"click",t[11]("up")),U(n,"click",t[11]("left")),U(u,"click",t[11]("right")),U(w,"click",t[11]("down"))],c=!0)},p(o,_){o[1]?a&&(a.d(1),a=null):a?a.p(o,_):(a=G(o),a.c(),a.m(v.parentNode,v))},d(o){o&&(k(e),k(s),k(n),k(m),k(u),k(b),k(w),k(r),k(v)),a&&a.d(o),c=!1,S(B)}}}function ne(t){let e,l=!t[10]&&!t[1]&&H(t);return{c(){l&&l.c(),e=z()},l(s){l&&l.l(s),e=z()},m(s,n){l&&l.m(s,n),C(s,e,n)},p(s,n){!s[10]&&!s[1]?l?l.p(s,n):(l=H(s),l.c(),l.m(e.parentNode,e)):l&&(l.d(1),l=null)},d(s){s&&k(e),l&&l.d(s)}}}function G(t){let e,l="✎",s,n,d="✗",m,u;return{c(){e=T("button"),e.textContent=l,s=y(),n=T("button"),n.textContent=d,this.h()},l(i){e=N(i,"BUTTON",{class:!0,"data-svelte-h":!0}),I(e)!=="svelte-xeup1i"&&(e.textContent=l),s=E(i),n=N(i,"BUTTON",{class:!0,"data-svelte-h":!0}),I(n)!=="svelte-ko6iaj"&&(n.textContent=d),this.h()},h(){p(e,"class","action modify svelte-1ccw3kh"),p(n,"class","action delete svelte-1ccw3kh")},m(i,b){C(i,e,b),C(i,s,b),C(i,n,b),m||(u=[U(e,"click",t[11]("modify")),U(n,"click",t[11]("delete"))],m=!0)},p:ee,d(i){i&&(k(e),k(s),k(n)),m=!1,S(u)}}}function H(t){let e,l,s="input",n,d,m="output",u,i,b,w=j(t[6]),f=[];for(let r=0;r<w.length;r+=1)f[r]=J(A(t,w,r));return{c(){e=T("div"),l=T("button"),l.textContent=s,n=y(),d=T("button"),d.textContent=m,u=D(`
					| `);for(let r=0;r<f.length;r+=1)f[r].c();this.h()},l(r){e=N(r,"DIV",{class:!0});var v=q(e);l=N(v,"BUTTON",{class:!0,"data-svelte-h":!0}),I(l)!=="svelte-g0b3ia"&&(l.textContent=s),n=E(v),d=N(v,"BUTTON",{class:!0,"data-svelte-h":!0}),I(d)!=="svelte-zz2f36"&&(d.textContent=m),u=V(v,`
					| `);for(let c=0;c<f.length;c+=1)f[c].l(v);v.forEach(k),this.h()},h(){p(l,"class","function input svelte-1ccw3kh"),O(l,"selected",t[7]),p(d,"class","function output svelte-1ccw3kh"),O(d,"selected",t[8]),p(e,"class","button-set svelte-1ccw3kh")},m(r,v){C(r,e,v),g(e,l),g(e,n),g(e,d),g(e,u);for(let c=0;c<f.length;c+=1)f[c]&&f[c].m(e,null);i||(b=[U(l,"click",t[11]("input")),U(d,"click",t[11]("output"))],i=!0)},p(r,v){if(v&128&&O(l,"selected",r[7]),v&256&&O(d,"selected",r[8]),v&2624){w=j(r[6]);let c;for(c=0;c<w.length;c+=1){const B=A(r,w,c);f[c]?f[c].p(B,v):(f[c]=J(B),f[c].c(),f[c].m(e,null))}for(;c<f.length;c+=1)f[c].d(1);f.length=w.length}},d(r){r&&k(e),te(f,r),i=!1,S(b)}}}function J(t){let e,l,s=t[16]+"",n,d,m;return{c(){e=T("button"),l=D("on:"),n=D(s),this.h()},l(u){e=N(u,"BUTTON",{class:!0});var i=q(e);l=V(i,"on:"),n=V(i,s),i.forEach(k),this.h()},h(){p(e,"class","function event svelte-1ccw3kh"),O(e,"selected",t[9].includes(t[16]))},m(u,i){C(u,e,i),g(e,l),g(e,n),d||(m=U(e,"click",function(){K(t[11]("on:"+t[16]))&&t[11]("on:"+t[16]).apply(this,arguments)}),d=!0)},p(u,i){t=u,i&64&&s!==(s=t[16]+"")&&P(n,s),i&576&&O(e,"selected",t[9].includes(t[16]))},d(u){u&&k(e),d=!1,m()}}}function ie(t){let e,l,s,n,d=t[12].includes(t[2]),m,u,i,b,w,f=d&&F(t);function r(o,_){return o[5]?ne:le}let v=r(t),c=v(t);const B=t[15].default,a=W(B,t,t[14],null);return{c(){e=T("div"),l=T("div"),s=y(),n=T("div"),f&&f.c(),m=y(),c.c(),u=y(),a&&a.c(),this.h()},l(o){e=N(o,"DIV",{class:!0});var _=q(e);l=N(_,"DIV",{class:!0}),q(l).forEach(k),s=E(_),n=N(_,"DIV",{class:!0});var h=q(n);f&&f.l(h),m=E(h),c.l(h),h.forEach(k),u=E(_),a&&a.l(_),_.forEach(k),this.h()},h(){p(l,"class","cover svelte-1ccw3kh"),p(n,"class","interaction svelte-1ccw3kh"),p(e,"class","sketchbox svelte-1ccw3kh"),O(e,"function_mode",t[5]),O(e,"row",t[0]),O(e,"active",t[4])},m(o,_){C(o,e,_),g(e,l),g(e,s),g(e,n),f&&f.m(n,null),g(n,m),c.m(n,null),g(e,u),a&&a.m(e,null),i=!0,b||(w=U(n,"click",function(){K(t[1]?void 0:t[11]("modify"))&&(t[1]?void 0:t[11]("modify")).apply(this,arguments)}),b=!0)},p(o,[_]){t=o,_&4&&(d=t[12].includes(t[2])),d?f?f.p(t,_):(f=F(t),f.c(),f.m(n,m)):f&&(f.d(1),f=null),v===(v=r(t))&&c?c.p(t,_):(c.d(1),c=v(t),c&&(c.c(),c.m(n,null))),a&&a.p&&(!i||_&16384)&&X(a,B,t,t[14],i?Z(B,t[14],_,null):Y(t[14]),null),(!i||_&32)&&O(e,"function_mode",t[5]),(!i||_&1)&&O(e,"row",t[0]),(!i||_&16)&&O(e,"active",t[4])},i(o){i||(x(a,o),i=!0)},o(o){$(a,o),i=!1},d(o){o&&k(e),f&&f.d(),c.d(),a&&a.d(o),b=!1,w()}}}function se(t,e,l){let s,{$$slots:n={},$$scope:d}=e,{row:m}=e,{is_container:u}=e,{component_type:i}=e,{var_name:b}=e,{active:w=!1}=e,{function_mode:f=!1}=e,{event_list:r}=e,{is_input:v=!1}=e,{is_output:c=!1}=e,{triggers:B=[]}=e,{gradio:a}=e;const o=h=>L=>{L.stopPropagation(),a.dispatch("select",{index:0,value:h})},_=["state","browserstate","function"];return t.$$set=h=>{"row"in h&&l(0,m=h.row),"is_container"in h&&l(1,u=h.is_container),"component_type"in h&&l(2,i=h.component_type),"var_name"in h&&l(3,b=h.var_name),"active"in h&&l(4,w=h.active),"function_mode"in h&&l(5,f=h.function_mode),"event_list"in h&&l(6,r=h.event_list),"is_input"in h&&l(7,v=h.is_input),"is_output"in h&&l(8,c=h.is_output),"triggers"in h&&l(9,B=h.triggers),"gradio"in h&&l(13,a=h.gradio),"$$scope"in h&&l(14,d=h.$$scope)},t.$$.update=()=>{t.$$.dirty&4&&l(10,s=i==="function")},[m,u,i,b,w,f,r,v,c,B,s,o,_,a,d,n]}class fe extends M{constructor(e){super(),Q(this,e,se,ie,R,{row:0,is_container:1,component_type:2,var_name:3,active:4,function_mode:5,event_list:6,is_input:7,is_output:8,triggers:9,gradio:13})}}export{fe as default};
//# sourceMappingURL=Index.WpI12aFk.js.map
