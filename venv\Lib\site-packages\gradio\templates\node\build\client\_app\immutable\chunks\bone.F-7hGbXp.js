import{k as f,V as c,Q as u,M as h,N as _,a as d}from"./index.BoI39RQH.js";class r extends _{get _matrix(){return this._compose(),this._localMatrix}set _matrix(t){t.updateFlag===this._localMatrix.updateFlag&&!this._needToCompose||(this._needToCompose=!1,this._localMatrix.copyFrom(t),this._markAsDirtyAndDecompose())}constructor(t,i,e=null,s=null,o=null,a=null,n=null){super(t,i.getScene(),!1),this.name=t,this.children=[],this.animations=[],this._index=null,this._scalingDeterminant=1,this._needToDecompose=!0,this._needToCompose=!1,this._linkedTransformNode=null,this._waitingTransformNodeId=null,this._skeleton=i,this._localMatrix=(s==null?void 0:s.clone())??h.Identity(),this._restMatrix=o??this._localMatrix.clone(),this._bindMatrix=a??this._localMatrix.clone(),this._index=n,this._absoluteMatrix=new h,this._absoluteBindMatrix=new h,this._absoluteInverseBindMatrix=new h,this._finalMatrix=new h,i.bones.push(this),this.setParent(e,!1),this._updateAbsoluteBindMatrices()}getClassName(){return"Bone"}getSkeleton(){return this._skeleton}get parent(){return this._parentNode}getParent(){return this.parent}getChildren(){return this.children}getIndex(){return this._index===null?this.getSkeleton().bones.indexOf(this):this._index}set parent(t){this.setParent(t)}setParent(t,i=!0){if(this.parent!==t){if(this.parent){const e=this.parent.children.indexOf(this);e!==-1&&this.parent.children.splice(e,1)}this._parentNode=t,this.parent&&this.parent.children.push(this),i&&this._updateAbsoluteBindMatrices(),this.markAsDirty()}}getLocalMatrix(){return this._compose(),this._localMatrix}getBindMatrix(){return this._bindMatrix}getBaseMatrix(){return this.getBindMatrix()}getRestMatrix(){return this._restMatrix}getRestPose(){return this.getRestMatrix()}setRestMatrix(t){this._restMatrix.copyFrom(t)}setRestPose(t){this.setRestMatrix(t)}getBindPose(){return this.getBindMatrix()}setBindMatrix(t){this.updateMatrix(t)}setBindPose(t){this.setBindMatrix(t)}getFinalMatrix(){return this._finalMatrix}getWorldMatrix(){return this.getFinalMatrix()}returnToRest(){if(this._linkedTransformNode){const t=d.Vector3[0],i=d.Quaternion[0],e=d.Vector3[1];this.getRestMatrix().decompose(t,i,e),this._linkedTransformNode.position.copyFrom(e),this._linkedTransformNode.rotationQuaternion=this._linkedTransformNode.rotationQuaternion??u.Identity(),this._linkedTransformNode.rotationQuaternion.copyFrom(i),this._linkedTransformNode.scaling.copyFrom(t)}else this._matrix=this._restMatrix}getAbsoluteInverseBindMatrix(){return this._absoluteInverseBindMatrix}getInvertedAbsoluteTransform(){return this.getAbsoluteInverseBindMatrix()}getAbsoluteMatrix(){return this._absoluteMatrix}getAbsoluteTransform(){return this._absoluteMatrix}linkTransformNode(t){this._linkedTransformNode&&this._skeleton._numBonesWithLinkedTransformNode--,this._linkedTransformNode=t,this._linkedTransformNode&&this._skeleton._numBonesWithLinkedTransformNode++}getTransformNode(){return this._linkedTransformNode}get position(){return this._decompose(),this._localPosition}set position(t){this._decompose(),this._localPosition.copyFrom(t),this._markAsDirtyAndCompose()}get rotation(){return this.getRotation()}set rotation(t){this.setRotation(t)}get rotationQuaternion(){return this._decompose(),this._localRotation}set rotationQuaternion(t){this.setRotationQuaternion(t)}get scaling(){return this.getScale()}set scaling(t){this.setScale(t)}get animationPropertiesOverride(){return this._skeleton.animationPropertiesOverride}_decompose(){this._needToDecompose&&(this._needToDecompose=!1,this._localScaling||(this._localScaling=c.Zero(),this._localRotation=u.Zero(),this._localPosition=c.Zero()),this._localMatrix.decompose(this._localScaling,this._localRotation,this._localPosition))}_compose(){if(this._needToCompose){if(!this._localScaling){this._needToCompose=!1;return}this._needToCompose=!1,h.ComposeToRef(this._localScaling,this._localRotation,this._localPosition,this._localMatrix)}}updateMatrix(t,i=!0,e=!0){this._bindMatrix.copyFrom(t),i&&this._updateAbsoluteBindMatrices(),e?this._matrix=t:this.markAsDirty()}_updateAbsoluteBindMatrices(t,i=!0){if(t||(t=this._bindMatrix),this.parent?t.multiplyToRef(this.parent._absoluteBindMatrix,this._absoluteBindMatrix):this._absoluteBindMatrix.copyFrom(t),this._absoluteBindMatrix.invertToRef(this._absoluteInverseBindMatrix),i)for(let e=0;e<this.children.length;e++)this.children[e]._updateAbsoluteBindMatrices();this._scalingDeterminant=this._absoluteBindMatrix.determinant()<0?-1:1}markAsDirty(){return this._currentRenderId++,this._childUpdateId++,this._skeleton._markAsDirty(),this}_markAsDirtyAndCompose(){this.markAsDirty(),this._needToCompose=!0}_markAsDirtyAndDecompose(){this.markAsDirty(),this._needToDecompose=!0}_updatePosition(t,i=0,e,s=!0){const o=this.getLocalMatrix();if(i==0)s?(o.addAtIndex(12,t.x),o.addAtIndex(13,t.y),o.addAtIndex(14,t.z)):o.setTranslationFromFloats(t.x,t.y,t.z);else{let a=null;e&&(a=e.getWorldMatrix()),this._skeleton.computeAbsoluteMatrices();const n=r._TmpMats[0],l=r._TmpVecs[0];this.parent?e&&a?(n.copyFrom(this.parent.getAbsoluteMatrix()),n.multiplyToRef(a,n)):n.copyFrom(this.parent.getAbsoluteMatrix()):h.IdentityToRef(n),s&&n.setTranslationFromFloats(0,0,0),n.invert(),c.TransformCoordinatesToRef(t,n,l),s?(o.addAtIndex(12,l.x),o.addAtIndex(13,l.y),o.addAtIndex(14,l.z)):o.setTranslationFromFloats(l.x,l.y,l.z)}this._markAsDirtyAndDecompose()}translate(t,i=0,e){this._updatePosition(t,i,e,!0)}setPosition(t,i=0,e){this._updatePosition(t,i,e,!1)}setAbsolutePosition(t,i){this.setPosition(t,1,i)}scale(t,i,e,s=!1){const o=this.getLocalMatrix(),a=r._TmpMats[0];h.ScalingToRef(t,i,e,a),a.multiplyToRef(o,o),a.invert();for(const n of this.children){const l=n.getLocalMatrix();l.multiplyToRef(a,l),l.multiplyAtIndex(12,t),l.multiplyAtIndex(13,i),l.multiplyAtIndex(14,e),n._markAsDirtyAndDecompose()}if(this._markAsDirtyAndDecompose(),s)for(const n of this.children)n.scale(t,i,e,s)}setScale(t){this._decompose(),this._localScaling.copyFrom(t),this._markAsDirtyAndCompose()}getScale(){return this._decompose(),this._localScaling}getScaleToRef(t){this._decompose(),t.copyFrom(this._localScaling)}setYawPitchRoll(t,i,e,s=0,o){if(s===0){const l=r._TmpQuat;u.RotationYawPitchRollToRef(t,i,e,l),this.setRotationQuaternion(l,s,o);return}const a=r._TmpMats[0];if(!this._getAbsoluteInverseMatrixUnscaledToRef(a,o))return;const n=r._TmpMats[1];h.RotationYawPitchRollToRef(t,i,e,n),a.multiplyToRef(n,n),this._rotateWithMatrix(n,s,o)}rotate(t,i,e=0,s){const o=r._TmpMats[0];o.setTranslationFromFloats(0,0,0),h.RotationAxisToRef(t,i,o),this._rotateWithMatrix(o,e,s)}setAxisAngle(t,i,e=0,s){if(e===0){const n=r._TmpQuat;u.RotationAxisToRef(t,i,n),this.setRotationQuaternion(n,e,s);return}const o=r._TmpMats[0];if(!this._getAbsoluteInverseMatrixUnscaledToRef(o,s))return;const a=r._TmpMats[1];h.RotationAxisToRef(t,i,a),o.multiplyToRef(a,a),this._rotateWithMatrix(a,e,s)}setRotation(t,i=0,e){this.setYawPitchRoll(t.y,t.x,t.z,i,e)}setRotationQuaternion(t,i=0,e){if(i===0){this._decompose(),this._localRotation.copyFrom(t),this._markAsDirtyAndCompose();return}const s=r._TmpMats[0];if(!this._getAbsoluteInverseMatrixUnscaledToRef(s,e))return;const o=r._TmpMats[1];h.FromQuaternionToRef(t,o),s.multiplyToRef(o,o),this._rotateWithMatrix(o,i,e)}setRotationMatrix(t,i=0,e){if(i===0){const a=r._TmpQuat;u.FromRotationMatrixToRef(t,a),this.setRotationQuaternion(a,i,e);return}const s=r._TmpMats[0];if(!this._getAbsoluteInverseMatrixUnscaledToRef(s,e))return;const o=r._TmpMats[1];o.copyFrom(t),s.multiplyToRef(t,o),this._rotateWithMatrix(o,i,e)}_rotateWithMatrix(t,i=0,e){const s=this.getLocalMatrix(),o=s.m[12],a=s.m[13],n=s.m[14],l=this.getParent(),m=r._TmpMats[3],p=r._TmpMats[4];l&&i==1?(e?(m.copyFrom(e.getWorldMatrix()),l.getAbsoluteMatrix().multiplyToRef(m,m)):m.copyFrom(l.getAbsoluteMatrix()),p.copyFrom(m),p.invert(),s.multiplyToRef(m,s),s.multiplyToRef(t,s),s.multiplyToRef(p,s)):i==1&&e?(m.copyFrom(e.getWorldMatrix()),p.copyFrom(m),p.invert(),s.multiplyToRef(m,s),s.multiplyToRef(t,s),s.multiplyToRef(p,s)):s.multiplyToRef(t,s),s.setTranslationFromFloats(o,a,n),this.computeAbsoluteMatrices(),this._markAsDirtyAndDecompose()}_getAbsoluteInverseMatrixUnscaledToRef(t,i){const e=r._TmpMats[2];return t.copyFrom(this.getAbsoluteMatrix()),i?(t.multiplyToRef(i.getWorldMatrix(),t),h.ScalingToRef(i.scaling.x,i.scaling.y,i.scaling.z,e)):h.IdentityToRef(e),t.invert(),isNaN(t.m[0])?!1:(e.multiplyAtIndex(0,this._scalingDeterminant),t.multiplyToRef(e,t),!0)}getPosition(t=0,i=null){const e=c.Zero();return this.getPositionToRef(t,i,e),e}getPositionToRef(t=0,i,e){if(t==0){const s=this.getLocalMatrix();e.x=s.m[12],e.y=s.m[13],e.z=s.m[14]}else{let s=null;i&&(s=i.getWorldMatrix()),this._skeleton.computeAbsoluteMatrices();let o=r._TmpMats[0];i&&s?(o.copyFrom(this.getAbsoluteMatrix()),o.multiplyToRef(s,o)):o=this.getAbsoluteMatrix(),e.x=o.m[12],e.y=o.m[13],e.z=o.m[14]}}getAbsolutePosition(t=null){const i=c.Zero();return this.getPositionToRef(1,t,i),i}getAbsolutePositionToRef(t,i){this.getPositionToRef(1,t,i)}computeAbsoluteMatrices(){if(this._compose(),this.parent)this._localMatrix.multiplyToRef(this.parent._absoluteMatrix,this._absoluteMatrix);else{this._absoluteMatrix.copyFrom(this._localMatrix);const e=this._skeleton.getPoseMatrix();e&&this._absoluteMatrix.multiplyToRef(e,this._absoluteMatrix)}const t=this.children,i=t.length;for(let e=0;e<i;e++)t[e].computeAbsoluteMatrices()}computeAbsoluteTransforms(){this.computeAbsoluteMatrices()}getDirection(t,i=null){const e=c.Zero();return this.getDirectionToRef(t,i,e),e}getDirectionToRef(t,i=null,e){let s=null;i&&(s=i.getWorldMatrix()),this._skeleton.computeAbsoluteMatrices();const o=r._TmpMats[0];o.copyFrom(this.getAbsoluteMatrix()),i&&s&&o.multiplyToRef(s,o),c.TransformNormalToRef(t,o,e),e.normalize()}getRotation(t=0,i=null){const e=c.Zero();return this.getRotationToRef(t,i,e),e}getRotationToRef(t=0,i=null,e){const s=r._TmpQuat;this.getRotationQuaternionToRef(t,i,s),s.toEulerAnglesToRef(e)}getRotationQuaternion(t=0,i=null){const e=u.Identity();return this.getRotationQuaternionToRef(t,i,e),e}getRotationQuaternionToRef(t=0,i=null,e){if(t==0)this._decompose(),e.copyFrom(this._localRotation);else{const s=r._TmpMats[0],o=this.getAbsoluteMatrix();i?o.multiplyToRef(i.getWorldMatrix(),s):s.copyFrom(o),s.multiplyAtIndex(0,this._scalingDeterminant),s.multiplyAtIndex(1,this._scalingDeterminant),s.multiplyAtIndex(2,this._scalingDeterminant),s.decompose(void 0,e,void 0)}}getRotationMatrix(t=0,i){const e=h.Identity();return this.getRotationMatrixToRef(t,i,e),e}getRotationMatrixToRef(t=0,i,e){if(t==0)this.getLocalMatrix().getRotationMatrixToRef(e);else{const s=r._TmpMats[0],o=this.getAbsoluteMatrix();i?o.multiplyToRef(i.getWorldMatrix(),s):s.copyFrom(o),s.multiplyAtIndex(0,this._scalingDeterminant),s.multiplyAtIndex(1,this._scalingDeterminant),s.multiplyAtIndex(2,this._scalingDeterminant),s.getRotationMatrixToRef(e)}}getAbsolutePositionFromLocal(t,i=null){const e=c.Zero();return this.getAbsolutePositionFromLocalToRef(t,i,e),e}getAbsolutePositionFromLocalToRef(t,i=null,e){let s=null;i&&(s=i.getWorldMatrix()),this._skeleton.computeAbsoluteMatrices();const o=r._TmpMats[0];o.copyFrom(this.getAbsoluteMatrix()),i&&s&&o.multiplyToRef(s,o),c.TransformCoordinatesToRef(t,o,e)}getLocalPositionFromAbsolute(t,i=null){const e=c.Zero();return this.getLocalPositionFromAbsoluteToRef(t,i,e),e}getLocalPositionFromAbsoluteToRef(t,i=null,e){let s=null;i&&(s=i.getWorldMatrix()),this._skeleton.computeAbsoluteMatrices();const o=r._TmpMats[0];o.copyFrom(this.getAbsoluteMatrix()),i&&s&&o.multiplyToRef(s,o),o.invert(),c.TransformCoordinatesToRef(t,o,e)}setCurrentPoseAsRest(){this.setRestMatrix(this.getLocalMatrix())}dispose(){this._linkedTransformNode=null;const t=this._skeleton.bones.indexOf(this);if(t!==-1&&this._skeleton.bones.splice(t,1),this._parentNode&&this._parentNode.children){const i=this._parentNode.children,e=i.indexOf(this);e!==-1&&i.splice(e,1)}super.dispose()}}r._TmpVecs=f(2,c.Zero);r._TmpQuat=u.Identity();r._TmpMats=f(5,h.Identity);export{r as B};
//# sourceMappingURL=bone.F-7hGbXp.js.map
