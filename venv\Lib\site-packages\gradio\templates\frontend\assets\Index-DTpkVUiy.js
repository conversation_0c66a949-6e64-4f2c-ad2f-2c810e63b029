const __vite__fileDeps=["./Canvas3D-ZmsoPOFA.js","./index-B7J2Z2jS.js","./index-CJsBH6a-.css","./file-url-DoxvUUVV.js","./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js","./DownloadLink-CqD3Uu0l.css","./Canvas3DGS-Btr1ASr1.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{q as Re,_ as ie}from"./index-B7J2Z2jS.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import{B as ge}from"./BlockLabel-3KxTaaiM.js";import{I as Ie}from"./IconButton-C_HS7fTi.js";import{D as Ve}from"./Download-DVtk-Jv3.js";import{F as ae}from"./File-BQ_9P3Ye.js";import{U as Fe}from"./Undo-DCjBnnSO.js";import{I as He}from"./IconButtonWrapper--EIOWuEM.js";import{a as Je}from"./Upload-D4uXt6Nz.js";import{M as Ke}from"./ModifyUpload-5lgGIpMc.js";import{B as Ee}from"./Block-CJdXVpa7.js";import{E as Qe}from"./Empty-ZqppqzTN.js";import{U as Xe}from"./UploadText-DtO3Yo94.js";import{S as Me}from"./index-B1FJGuzG.js";import"./StreamingBar.svelte_svelte_type_style_lang-DyfAPhwM.js";import{default as vn}from"./Example-uQ8MuYg6.js";import"./svelte/svelte.js";import"./prism-python-MMh3z1bK.js";/* empty css                                             */import"./Clear-By3xiIwg.js";import"./Edit-BpRIf5rU.js";import"./DownloadLink-QIttOhoR.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import"./file-url-DoxvUUVV.js";const{SvelteComponent:Ye,add_flush_callback:Se,append:Ze,attr:V,bind:le,binding_callbacks:Y,check_outros:te,construct_svelte_component:oe,create_component:T,destroy_component:W,detach:F,element:Ue,empty:pe,flush:U,group_outros:ne,init:ye,insert:H,mount_component:A,safe_not_equal:xe,space:be,transition_in:w,transition_out:v}=window.__gradio__svelte__internal;function $e(s){let e,n,t,l,i,_;n=new He({props:{$$slots:{default:[et]},$$scope:{ctx:s}}});const r=[nt,tt],o=[];function u(a,f){return a[10]?0:1}return l=u(s),i=o[l]=r[l](s),{c(){e=Ue("div"),T(n.$$.fragment),t=be(),i.c(),V(e,"class","model3D svelte-1mxwah3"),V(e,"data-testid","model3d")},m(a,f){H(a,e,f),A(n,e,null),Ze(e,t),o[l].m(e,null),_=!0},p(a,f){const d={};f&2115105&&(d.$$scope={dirty:f,ctx:a}),n.$set(d);let m=l;l=u(a),l===m?o[l].p(a,f):(ne(),v(o[m],1,1,()=>{o[m]=null}),te(),i=o[l],i?i.p(a,f):(i=o[l]=r[l](a),i.c()),w(i,1),i.m(e,null))},i(a){_||(w(n.$$.fragment,a),w(i),_=!0)},o(a){v(n.$$.fragment,a),v(i),_=!1},d(a){a&&F(e),W(n),o[l].d()}}}function ve(s){let e,n;return e=new Ie({props:{Icon:Fe,label:"Undo",disabled:!s[9]}}),e.$on("click",s[17]),{c(){T(e.$$.fragment)},m(t,l){A(e,t,l),n=!0},p(t,l){const i={};l&512&&(i.disabled=!t[9]),e.$set(i)},i(t){n||(w(e.$$.fragment,t),n=!0)},o(t){v(e.$$.fragment,t),n=!1},d(t){W(e,t)}}}function et(s){let e,n,t,l,i,_=!s[10]&&ve(s);return t=new Ie({props:{Icon:Ve,label:s[5]("common.download")}}),{c(){_&&_.c(),e=be(),n=Ue("a"),T(t.$$.fragment),V(n,"href",s[14]),V(n,"target",window.__is_colab__?"_blank":null),V(n,"download",l=window.__is_colab__?null:s[0].orig_name||s[0].path)},m(r,o){_&&_.m(r,o),H(r,e,o),H(r,n,o),A(t,n,null),i=!0},p(r,o){r[10]?_&&(ne(),v(_,1,1,()=>{_=null}),te()):_?(_.p(r,o),o&1024&&w(_,1)):(_=ve(r),_.c(),w(_,1),_.m(e.parentNode,e));const u={};o&32&&(u.label=r[5]("common.download")),t.$set(u),(!i||o&16384)&&V(n,"href",r[14]),(!i||o&1&&l!==(l=window.__is_colab__?null:r[0].orig_name||r[0].path))&&V(n,"download",l)},i(r){i||(w(_),w(t.$$.fragment,r),i=!0)},o(r){v(_),v(t.$$.fragment,r),i=!1},d(r){r&&(F(e),F(n)),_&&_.d(r),W(t)}}}function tt(s){let e,n,t,l;function i(o){s[20](o)}var _=s[13];function r(o,u){let a={value:o[0],display_mode:o[1],clear_color:o[2],camera_position:o[8],zoom_speed:o[6],pan_speed:o[7]};return o[14]!==void 0&&(a.resolved_url=o[14]),{props:a}}return _&&(e=oe(_,r(s)),s[19](e),Y.push(()=>le(e,"resolved_url",i))),{c(){e&&T(e.$$.fragment),t=pe()},m(o,u){e&&A(e,o,u),H(o,t,u),l=!0},p(o,u){if(u&8192&&_!==(_=o[13])){if(e){ne();const a=e;v(a.$$.fragment,1,0,()=>{W(a,1)}),te()}_?(e=oe(_,r(o)),o[19](e),Y.push(()=>le(e,"resolved_url",i)),T(e.$$.fragment),w(e.$$.fragment,1),A(e,t.parentNode,t)):e=null}else if(_){const a={};u&1&&(a.value=o[0]),u&2&&(a.display_mode=o[1]),u&4&&(a.clear_color=o[2]),u&256&&(a.camera_position=o[8]),u&64&&(a.zoom_speed=o[6]),u&128&&(a.pan_speed=o[7]),!n&&u&16384&&(n=!0,a.resolved_url=o[14],Se(()=>n=!1)),e.$set(a)}},i(o){l||(e&&w(e.$$.fragment,o),l=!0)},o(o){e&&v(e.$$.fragment,o),l=!1},d(o){o&&F(t),s[19](null),e&&W(e,o)}}}function nt(s){let e,n,t,l;function i(o){s[18](o)}var _=s[12];function r(o,u){let a={value:o[0],zoom_speed:o[6],pan_speed:o[7]};return o[14]!==void 0&&(a.resolved_url=o[14]),{props:a}}return _&&(e=oe(_,r(s)),Y.push(()=>le(e,"resolved_url",i))),{c(){e&&T(e.$$.fragment),t=pe()},m(o,u){e&&A(e,o,u),H(o,t,u),l=!0},p(o,u){if(u&4096&&_!==(_=o[12])){if(e){ne();const a=e;v(a.$$.fragment,1,0,()=>{W(a,1)}),te()}_?(e=oe(_,r(o)),Y.push(()=>le(e,"resolved_url",i)),T(e.$$.fragment),w(e.$$.fragment,1),A(e,t.parentNode,t)):e=null}else if(_){const a={};u&1&&(a.value=o[0]),u&64&&(a.zoom_speed=o[6]),u&128&&(a.pan_speed=o[7]),!n&&u&16384&&(n=!0,a.resolved_url=o[14],Se(()=>n=!1)),e.$set(a)}},i(o){l||(e&&w(e.$$.fragment,o),l=!0)},o(o){e&&v(e.$$.fragment,o),l=!1},d(o){o&&F(t),e&&W(e,o)}}}function lt(s){let e,n,t,l;e=new ge({props:{show_label:s[4],Icon:ae,label:s[3]||s[5]("3D_model.3d_model")}});let i=s[0]&&$e(s);return{c(){T(e.$$.fragment),n=be(),i&&i.c(),t=pe()},m(_,r){A(e,_,r),H(_,n,r),i&&i.m(_,r),H(_,t,r),l=!0},p(_,[r]){const o={};r&16&&(o.show_label=_[4]),r&40&&(o.label=_[3]||_[5]("3D_model.3d_model")),e.$set(o),_[0]?i?(i.p(_,r),r&1&&w(i,1)):(i=$e(_),i.c(),w(i,1),i.m(t.parentNode,t)):i&&(ne(),v(i,1,1,()=>{i=null}),te())},i(_){l||(w(e.$$.fragment,_),w(i),l=!0)},o(_){v(e.$$.fragment,_),v(i),l=!1},d(_){_&&(F(n),F(t)),W(e,_),i&&i.d(_)}}}async function ot(){return(await ie(()=>import("./Canvas3D-ZmsoPOFA.js"),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url)).default}async function st(){return(await ie(()=>import("./Canvas3DGS-Btr1ASr1.js"),__vite__mapDeps([6,3,4,5]),import.meta.url)).default}function it(s,e,n){let{value:t}=e,{display_mode:l="solid"}=e,{clear_color:i=[0,0,0,0]}=e,{label:_=""}=e,{show_label:r}=e,{i18n:o}=e,{zoom_speed:u=1}=e,{pan_speed:a=1}=e,{camera_position:f=[null,null,null]}=e,{has_change_history:d=!1}=e,m={camera_position:f,zoom_speed:u,pan_speed:a},E=!1,k,b,M;function L(){M?.reset_camera_position()}let S;const P=()=>L();function j(g){S=g,n(14,S)}function G(g){Y[g?"unshift":"push"](()=>{M=g,n(11,M)})}function O(g){S=g,n(14,S)}return s.$$set=g=>{"value"in g&&n(0,t=g.value),"display_mode"in g&&n(1,l=g.display_mode),"clear_color"in g&&n(2,i=g.clear_color),"label"in g&&n(3,_=g.label),"show_label"in g&&n(4,r=g.show_label),"i18n"in g&&n(5,o=g.i18n),"zoom_speed"in g&&n(6,u=g.zoom_speed),"pan_speed"in g&&n(7,a=g.pan_speed),"camera_position"in g&&n(8,f=g.camera_position),"has_change_history"in g&&n(9,d=g.has_change_history)},s.$$.update=()=>{s.$$.dirty&1025&&t&&(n(10,E=t.path.endsWith(".splat")||t.path.endsWith(".ply")),E?st().then(g=>{n(12,k=g)}):ot().then(g=>{n(13,b=g)})),s.$$.dirty&68032&&(!Re(m.camera_position,f)||m.zoom_speed!==u||m.pan_speed!==a)&&(M?.update_camera(f,u,a),n(16,m={camera_position:f,zoom_speed:u,pan_speed:a}))},[t,l,i,_,r,o,u,a,f,d,E,M,k,b,S,L,m,P,j,G,O]}class at extends Ye{constructor(e){super(),ye(this,e,it,lt,xe,{value:0,display_mode:1,clear_color:2,label:3,show_label:4,i18n:5,zoom_speed:6,pan_speed:7,camera_position:8,has_change_history:9})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),U()}get display_mode(){return this.$$.ctx[1]}set display_mode(e){this.$$set({display_mode:e}),U()}get clear_color(){return this.$$.ctx[2]}set clear_color(e){this.$$set({clear_color:e}),U()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),U()}get show_label(){return this.$$.ctx[4]}set show_label(e){this.$$set({show_label:e}),U()}get i18n(){return this.$$.ctx[5]}set i18n(e){this.$$set({i18n:e}),U()}get zoom_speed(){return this.$$.ctx[6]}set zoom_speed(e){this.$$set({zoom_speed:e}),U()}get pan_speed(){return this.$$.ctx[7]}set pan_speed(e){this.$$set({pan_speed:e}),U()}get camera_position(){return this.$$.ctx[8]}set camera_position(e){this.$$set({camera_position:e}),U()}get has_change_history(){return this.$$.ctx[9]}set has_change_history(e){this.$$set({has_change_history:e}),U()}}const _t=at,{SvelteComponent:rt,add_flush_callback:ze,append:ut,attr:ft,bind:De,binding_callbacks:he,bubble:ct,check_outros:_e,construct_svelte_component:se,create_component:J,create_slot:mt,destroy_component:K,detach:Z,element:dt,empty:we,flush:$,get_all_dirty_from_scope:ht,get_slot_changes:gt,group_outros:re,init:pt,insert:y,mount_component:Q,safe_not_equal:bt,space:Be,transition_in:C,transition_out:I,update_slot_base:wt}=window.__gradio__svelte__internal,{createEventDispatcher:kt,tick:Ce}=window.__gradio__svelte__internal;function $t(s){let e,n,t,l,i,_;n=new Ke({props:{undoable:!s[14],i18n:s[7]}}),n.$on("clear",s[20]),n.$on("undo",s[21]);const r=[Dt,zt],o=[];function u(a,f){return a[14]?0:1}return l=u(s),i=o[l]=r[l](s),{c(){e=dt("div"),J(n.$$.fragment),t=Be(),i.c(),ft(e,"class","input-model svelte-jub4pj")},m(a,f){y(a,e,f),Q(n,e,null),ut(e,t),o[l].m(e,null),_=!0},p(a,f){const d={};f&16384&&(d.undoable=!a[14]),f&128&&(d.i18n=a[7]),n.$set(d);let m=l;l=u(a),l===m?o[l].p(a,f):(re(),I(o[m],1,1,()=>{o[m]=null}),_e(),i=o[l],i?i.p(a,f):(i=o[l]=r[l](a),i.c()),C(i,1),i.m(e,null))},i(a){_||(C(n.$$.fragment,a),C(i),_=!0)},o(a){I(n.$$.fragment,a),I(i),_=!1},d(a){a&&Z(e),K(n),o[l].d()}}}function vt(s){let e,n,t,l;function i(o){s[23](o)}function _(o){s[24](o)}let r={upload:s[12],stream_handler:s[13],root:s[6],max_file_size:s[10],filetype:[".stl",".obj",".gltf",".glb","model/obj",".splat",".ply"],aria_label:s[7]("model3d.drop_to_upload"),$$slots:{default:[Ct]},$$scope:{ctx:s}};return s[15]!==void 0&&(r.dragging=s[15]),s[1]!==void 0&&(r.uploading=s[1]),e=new Je({props:r}),he.push(()=>De(e,"dragging",i)),he.push(()=>De(e,"uploading",_)),e.$on("load",s[19]),e.$on("error",s[25]),{c(){J(e.$$.fragment)},m(o,u){Q(e,o,u),l=!0},p(o,u){const a={};u&4096&&(a.upload=o[12]),u&8192&&(a.stream_handler=o[13]),u&64&&(a.root=o[6]),u&1024&&(a.max_file_size=o[10]),u&128&&(a.aria_label=o[7]("model3d.drop_to_upload")),u&134217728&&(a.$$scope={dirty:u,ctx:o}),!n&&u&32768&&(n=!0,a.dragging=o[15],ze(()=>n=!1)),!t&&u&2&&(t=!0,a.uploading=o[1],ze(()=>t=!1)),e.$set(a)},i(o){l||(C(e.$$.fragment,o),l=!0)},o(o){I(e.$$.fragment,o),l=!1},d(o){K(e,o)}}}function zt(s){let e,n,t;var l=s[17];function i(_,r){return{props:{value:_[0],display_mode:_[2],clear_color:_[3],camera_position:_[11],zoom_speed:_[8],pan_speed:_[9]}}}return l&&(e=se(l,i(s)),s[26](e)),{c(){e&&J(e.$$.fragment),n=we()},m(_,r){e&&Q(e,_,r),y(_,n,r),t=!0},p(_,r){if(r&131072&&l!==(l=_[17])){if(e){re();const o=e;I(o.$$.fragment,1,0,()=>{K(o,1)}),_e()}l?(e=se(l,i(_)),_[26](e),J(e.$$.fragment),C(e.$$.fragment,1),Q(e,n.parentNode,n)):e=null}else if(l){const o={};r&1&&(o.value=_[0]),r&4&&(o.display_mode=_[2]),r&8&&(o.clear_color=_[3]),r&2048&&(o.camera_position=_[11]),r&256&&(o.zoom_speed=_[8]),r&512&&(o.pan_speed=_[9]),e.$set(o)}},i(_){t||(e&&C(e.$$.fragment,_),t=!0)},o(_){e&&I(e.$$.fragment,_),t=!1},d(_){_&&Z(n),s[26](null),e&&K(e,_)}}}function Dt(s){let e,n,t;var l=s[16];function i(_,r){return{props:{value:_[0],zoom_speed:_[8],pan_speed:_[9]}}}return l&&(e=se(l,i(s))),{c(){e&&J(e.$$.fragment),n=we()},m(_,r){e&&Q(e,_,r),y(_,n,r),t=!0},p(_,r){if(r&65536&&l!==(l=_[16])){if(e){re();const o=e;I(o.$$.fragment,1,0,()=>{K(o,1)}),_e()}l?(e=se(l,i(_)),J(e.$$.fragment),C(e.$$.fragment,1),Q(e,n.parentNode,n)):e=null}else if(l){const o={};r&1&&(o.value=_[0]),r&256&&(o.zoom_speed=_[8]),r&512&&(o.pan_speed=_[9]),e.$set(o)}},i(_){t||(e&&C(e.$$.fragment,_),t=!0)},o(_){e&&I(e.$$.fragment,_),t=!1},d(_){_&&Z(n),e&&K(e,_)}}}function Ct(s){let e;const n=s[22].default,t=mt(n,s,s[27],null);return{c(){t&&t.c()},m(l,i){t&&t.m(l,i),e=!0},p(l,i){t&&t.p&&(!e||i&134217728)&&wt(t,n,l,l[27],e?gt(n,l[27],i,null):ht(l[27]),null)},i(l){e||(C(t,l),e=!0)},o(l){I(t,l),e=!1},d(l){t&&t.d(l)}}}function It(s){let e,n,t,l,i,_;e=new ge({props:{show_label:s[5],Icon:ae,label:s[4]||"3D Model"}});const r=[vt,$t],o=[];function u(a,f){return a[0]===null?0:1}return t=u(s),l=o[t]=r[t](s),{c(){J(e.$$.fragment),n=Be(),l.c(),i=we()},m(a,f){Q(e,a,f),y(a,n,f),o[t].m(a,f),y(a,i,f),_=!0},p(a,[f]){const d={};f&32&&(d.show_label=a[5]),f&16&&(d.label=a[4]||"3D Model"),e.$set(d);let m=t;t=u(a),t===m?o[t].p(a,f):(re(),I(o[m],1,1,()=>{o[m]=null}),_e(),l=o[t],l?l.p(a,f):(l=o[t]=r[t](a),l.c()),C(l,1),l.m(i.parentNode,i))},i(a){_||(C(e.$$.fragment,a),C(l),_=!0)},o(a){I(e.$$.fragment,a),I(l),_=!1},d(a){a&&(Z(n),Z(i)),K(e,a),o[t].d(a)}}}async function Et(){return(await ie(()=>import("./Canvas3D-ZmsoPOFA.js"),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url)).default}async function Mt(){return(await ie(()=>import("./Canvas3DGS-Btr1ASr1.js"),__vite__mapDeps([6,3,4,5]),import.meta.url)).default}function St(s,e,n){let{$$slots:t={},$$scope:l}=e,{value:i}=e,{display_mode:_="solid"}=e,{clear_color:r=[0,0,0,0]}=e,{label:o=""}=e,{show_label:u}=e,{root:a}=e,{i18n:f}=e,{zoom_speed:d=1}=e,{pan_speed:m=1}=e,{max_file_size:E=null}=e,{uploading:k=!1}=e,{camera_position:b=[null,null,null]}=e,{upload:M}=e,{stream_handler:L}=e;async function S({detail:h}){n(0,i=h),await Ce(),R("change",i),R("load",i)}async function P(){n(0,i=null),await Ce(),R("clear"),R("change")}let j=!1,G,O,g;async function ue(){g?.reset_camera_position()}const R=kt();let X=!1;function fe(h){X=h,n(15,X)}function ce(h){k=h,n(1,k)}function me(h){ct.call(this,s,h)}function de(h){he[h?"unshift":"push"](()=>{g=h,n(18,g)})}return s.$$set=h=>{"value"in h&&n(0,i=h.value),"display_mode"in h&&n(2,_=h.display_mode),"clear_color"in h&&n(3,r=h.clear_color),"label"in h&&n(4,o=h.label),"show_label"in h&&n(5,u=h.show_label),"root"in h&&n(6,a=h.root),"i18n"in h&&n(7,f=h.i18n),"zoom_speed"in h&&n(8,d=h.zoom_speed),"pan_speed"in h&&n(9,m=h.pan_speed),"max_file_size"in h&&n(10,E=h.max_file_size),"uploading"in h&&n(1,k=h.uploading),"camera_position"in h&&n(11,b=h.camera_position),"upload"in h&&n(12,M=h.upload),"stream_handler"in h&&n(13,L=h.stream_handler),"$$scope"in h&&n(27,l=h.$$scope)},s.$$.update=()=>{s.$$.dirty&16385&&i&&(n(14,j=i.path.endsWith(".splat")||i.path.endsWith(".ply")),j?Mt().then(h=>{n(16,G=h)}):Et().then(h=>{n(17,O=h)})),s.$$.dirty&32768&&R("drag",X)},[i,k,_,r,o,u,a,f,d,m,E,b,M,L,j,X,G,O,g,S,P,ue,t,fe,ce,me,de,l]}class Ut extends rt{constructor(e){super(),pt(this,e,St,It,bt,{value:0,display_mode:2,clear_color:3,label:4,show_label:5,root:6,i18n:7,zoom_speed:8,pan_speed:9,max_file_size:10,uploading:1,camera_position:11,upload:12,stream_handler:13})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),$()}get display_mode(){return this.$$.ctx[2]}set display_mode(e){this.$$set({display_mode:e}),$()}get clear_color(){return this.$$.ctx[3]}set clear_color(e){this.$$set({clear_color:e}),$()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),$()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),$()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),$()}get i18n(){return this.$$.ctx[7]}set i18n(e){this.$$set({i18n:e}),$()}get zoom_speed(){return this.$$.ctx[8]}set zoom_speed(e){this.$$set({zoom_speed:e}),$()}get pan_speed(){return this.$$.ctx[9]}set pan_speed(e){this.$$set({pan_speed:e}),$()}get max_file_size(){return this.$$.ctx[10]}set max_file_size(e){this.$$set({max_file_size:e}),$()}get uploading(){return this.$$.ctx[1]}set uploading(e){this.$$set({uploading:e}),$()}get camera_position(){return this.$$.ctx[11]}set camera_position(e){this.$$set({camera_position:e}),$()}get upload(){return this.$$.ctx[12]}set upload(e){this.$$set({upload:e}),$()}get stream_handler(){return this.$$.ctx[13]}set stream_handler(e){this.$$set({stream_handler:e}),$()}}const Bt=Ut,{SvelteComponent:Nt,add_flush_callback:qt,assign:Ne,bind:jt,binding_callbacks:Lt,check_outros:qe,create_component:B,destroy_component:N,detach:x,empty:je,flush:p,get_spread_object:Le,get_spread_update:Pe,group_outros:Te,init:Pt,insert:ee,mount_component:q,safe_not_equal:Tt,space:ke,transition_in:z,transition_out:D}=window.__gradio__svelte__internal;function Wt(s){let e,n;return e=new Ee({props:{visible:s[5],variant:s[0]===null?"dashed":"solid",border_mode:s[20]?"focus":"base",padding:!1,elem_id:s[3],elem_classes:s[4],container:s[11],scale:s[12],min_width:s[13],height:s[15],$$slots:{default:[Ot]},$$scope:{ctx:s}}}),{c(){B(e.$$.fragment)},m(t,l){q(e,t,l),n=!0},p(t,l){const i={};l[0]&32&&(i.visible=t[5]),l[0]&1&&(i.variant=t[0]===null?"dashed":"solid"),l[0]&1048576&&(i.border_mode=t[20]?"focus":"base"),l[0]&8&&(i.elem_id=t[3]),l[0]&16&&(i.elem_classes=t[4]),l[0]&2048&&(i.container=t[11]),l[0]&4096&&(i.scale=t[12]),l[0]&8192&&(i.min_width=t[13]),l[0]&32768&&(i.height=t[15]),l[0]&1787847|l[1]&8&&(i.$$scope={dirty:l,ctx:t}),e.$set(i)},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){N(e,t)}}}function At(s){let e,n;return e=new Ee({props:{visible:s[5],variant:s[0]===null?"dashed":"solid",border_mode:s[20]?"focus":"base",padding:!1,elem_id:s[3],elem_classes:s[4],container:s[11],scale:s[12],min_width:s[13],height:s[15],$$slots:{default:[Ht]},$$scope:{ctx:s}}}),{c(){B(e.$$.fragment)},m(t,l){q(e,t,l),n=!0},p(t,l){const i={};l[0]&32&&(i.visible=t[5]),l[0]&1&&(i.variant=t[0]===null?"dashed":"solid"),l[0]&1048576&&(i.border_mode=t[20]?"focus":"base"),l[0]&8&&(i.elem_id=t[3]),l[0]&16&&(i.elem_classes=t[4]),l[0]&2048&&(i.container=t[11]),l[0]&4096&&(i.scale=t[12]),l[0]&8192&&(i.min_width=t[13]),l[0]&32768&&(i.height=t[15]),l[0]&214919|l[1]&8&&(i.$$scope={dirty:l,ctx:t}),e.$set(i)},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){N(e,t)}}}function Gt(s){let e,n;return e=new Xe({props:{i18n:s[14].i18n,type:"file"}}),{c(){B(e.$$.fragment)},m(t,l){q(e,t,l),n=!0},p(t,l){const i={};l[0]&16384&&(i.i18n=t[14].i18n),e.$set(i)},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){N(e,t)}}}function Ot(s){let e,n,t,l,i;const _=[{autoscroll:s[14].autoscroll},{i18n:s[14].i18n},s[1]];let r={};for(let a=0;a<_.length;a+=1)r=Ne(r,_[a]);e=new Me({props:r}),e.$on("clear_status",s[24]);function o(a){s[27](a)}let u={label:s[9],show_label:s[10],root:s[6],display_mode:s[7],clear_color:s[8],value:s[0],camera_position:s[17],zoom_speed:s[16],i18n:s[14].i18n,max_file_size:s[14].max_file_size,upload:s[25],stream_handler:s[26],$$slots:{default:[Gt]},$$scope:{ctx:s}};return s[19]!==void 0&&(u.uploading=s[19]),t=new Bt({props:u}),Lt.push(()=>jt(t,"uploading",o)),t.$on("change",s[28]),t.$on("drag",s[29]),t.$on("change",s[30]),t.$on("clear",s[31]),t.$on("load",s[32]),t.$on("error",s[33]),{c(){B(e.$$.fragment),n=ke(),B(t.$$.fragment)},m(a,f){q(e,a,f),ee(a,n,f),q(t,a,f),i=!0},p(a,f){const d=f[0]&16386?Pe(_,[f[0]&16384&&{autoscroll:a[14].autoscroll},f[0]&16384&&{i18n:a[14].i18n},f[0]&2&&Le(a[1])]):{};e.$set(d);const m={};f[0]&512&&(m.label=a[9]),f[0]&1024&&(m.show_label=a[10]),f[0]&64&&(m.root=a[6]),f[0]&128&&(m.display_mode=a[7]),f[0]&256&&(m.clear_color=a[8]),f[0]&1&&(m.value=a[0]),f[0]&131072&&(m.camera_position=a[17]),f[0]&65536&&(m.zoom_speed=a[16]),f[0]&16384&&(m.i18n=a[14].i18n),f[0]&16384&&(m.max_file_size=a[14].max_file_size),f[0]&16384&&(m.upload=a[25]),f[0]&16384&&(m.stream_handler=a[26]),f[0]&16384|f[1]&8&&(m.$$scope={dirty:f,ctx:a}),!l&&f[0]&524288&&(l=!0,m.uploading=a[19],qt(()=>l=!1)),t.$set(m)},i(a){i||(z(e.$$.fragment,a),z(t.$$.fragment,a),i=!0)},o(a){D(e.$$.fragment,a),D(t.$$.fragment,a),i=!1},d(a){a&&x(n),N(e,a),N(t,a)}}}function Rt(s){let e,n,t,l;return e=new ge({props:{show_label:s[10],Icon:ae,label:s[9]||"3D Model"}}),t=new Qe({props:{unpadded_box:!0,size:"large",$$slots:{default:[Ft]},$$scope:{ctx:s}}}),{c(){B(e.$$.fragment),n=ke(),B(t.$$.fragment)},m(i,_){q(e,i,_),ee(i,n,_),q(t,i,_),l=!0},p(i,_){const r={};_[0]&1024&&(r.show_label=i[10]),_[0]&512&&(r.label=i[9]||"3D Model"),e.$set(r);const o={};_[1]&8&&(o.$$scope={dirty:_,ctx:i}),t.$set(o)},i(i){l||(z(e.$$.fragment,i),z(t.$$.fragment,i),l=!0)},o(i){D(e.$$.fragment,i),D(t.$$.fragment,i),l=!1},d(i){i&&x(n),N(e,i),N(t,i)}}}function Vt(s){let e,n;return e=new _t({props:{value:s[0],i18n:s[14].i18n,display_mode:s[7],clear_color:s[8],label:s[9],show_label:s[10],camera_position:s[17],zoom_speed:s[16],has_change_history:s[2]}}),{c(){B(e.$$.fragment)},m(t,l){q(e,t,l),n=!0},p(t,l){const i={};l[0]&1&&(i.value=t[0]),l[0]&16384&&(i.i18n=t[14].i18n),l[0]&128&&(i.display_mode=t[7]),l[0]&256&&(i.clear_color=t[8]),l[0]&512&&(i.label=t[9]),l[0]&1024&&(i.show_label=t[10]),l[0]&131072&&(i.camera_position=t[17]),l[0]&65536&&(i.zoom_speed=t[16]),l[0]&4&&(i.has_change_history=t[2]),e.$set(i)},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){N(e,t)}}}function Ft(s){let e,n;return e=new ae({}),{c(){B(e.$$.fragment)},m(t,l){q(e,t,l),n=!0},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){D(e.$$.fragment,t),n=!1},d(t){N(e,t)}}}function Ht(s){let e,n,t,l,i,_;const r=[{autoscroll:s[14].autoscroll},{i18n:s[14].i18n},s[1]];let o={};for(let d=0;d<r.length;d+=1)o=Ne(o,r[d]);e=new Me({props:o}),e.$on("clear_status",s[23]);const u=[Vt,Rt],a=[];function f(d,m){return d[0]&&d[21]?0:1}return t=f(s),l=a[t]=u[t](s),{c(){B(e.$$.fragment),n=ke(),l.c(),i=je()},m(d,m){q(e,d,m),ee(d,n,m),a[t].m(d,m),ee(d,i,m),_=!0},p(d,m){const E=m[0]&16386?Pe(r,[m[0]&16384&&{autoscroll:d[14].autoscroll},m[0]&16384&&{i18n:d[14].i18n},m[0]&2&&Le(d[1])]):{};e.$set(E);let k=t;t=f(d),t===k?a[t].p(d,m):(Te(),D(a[k],1,1,()=>{a[k]=null}),qe(),l=a[t],l?l.p(d,m):(l=a[t]=u[t](d),l.c()),z(l,1),l.m(i.parentNode,i))},i(d){_||(z(e.$$.fragment,d),z(l),_=!0)},o(d){D(e.$$.fragment,d),D(l),_=!1},d(d){d&&(x(n),x(i)),N(e,d),a[t].d(d)}}}function Jt(s){let e,n,t,l;const i=[At,Wt],_=[];function r(o,u){return o[18]?1:0}return e=r(s),n=_[e]=i[e](s),{c(){n.c(),t=je()},m(o,u){_[e].m(o,u),ee(o,t,u),l=!0},p(o,u){let a=e;e=r(o),e===a?_[e].p(o,u):(Te(),D(_[a],1,1,()=>{_[a]=null}),qe(),n=_[e],n?n.p(o,u):(n=_[e]=i[e](o),n.c()),z(n,1),n.m(t.parentNode,t))},i(o){l||(z(n),l=!0)},o(o){D(n),l=!1},d(o){o&&x(t),_[e].d(o)}}}function Kt(s,e,n){let{elem_id:t=""}=e,{elem_classes:l=[]}=e,{visible:i=!0}=e,{value:_=null}=e,{root:r}=e,{display_mode:o="solid"}=e,{clear_color:u}=e,{loading_status:a}=e,{label:f}=e,{show_label:d}=e,{container:m=!0}=e,{scale:E=null}=e,{min_width:k=void 0}=e,{gradio:b}=e,{height:M=void 0}=e,{zoom_speed:L=1}=e,{input_ready:S}=e,P=!1,{has_change_history:j=!1}=e,{camera_position:G=[null,null,null]}=e,{interactive:O}=e,g=!1;const ue=typeof window<"u",R=()=>b.dispatch("clear_status",a),X=()=>b.dispatch("clear_status",a),fe=(...c)=>b.client.upload(...c),ce=(...c)=>b.client.stream(...c);function me(c){P=c,n(19,P)}const de=({detail:c})=>n(0,_=c),h=({detail:c})=>n(20,g=c),We=({detail:c})=>{b.dispatch("change",c),n(2,j=!0)},Ae=()=>{n(0,_=null),b.dispatch("clear")},Ge=({detail:c})=>{n(0,_=c),b.dispatch("upload")},Oe=({detail:c})=>{n(1,a=a||{}),n(1,a.status="error",a),b.dispatch("error",c)};return s.$$set=c=>{"elem_id"in c&&n(3,t=c.elem_id),"elem_classes"in c&&n(4,l=c.elem_classes),"visible"in c&&n(5,i=c.visible),"value"in c&&n(0,_=c.value),"root"in c&&n(6,r=c.root),"display_mode"in c&&n(7,o=c.display_mode),"clear_color"in c&&n(8,u=c.clear_color),"loading_status"in c&&n(1,a=c.loading_status),"label"in c&&n(9,f=c.label),"show_label"in c&&n(10,d=c.show_label),"container"in c&&n(11,m=c.container),"scale"in c&&n(12,E=c.scale),"min_width"in c&&n(13,k=c.min_width),"gradio"in c&&n(14,b=c.gradio),"height"in c&&n(15,M=c.height),"zoom_speed"in c&&n(16,L=c.zoom_speed),"input_ready"in c&&n(22,S=c.input_ready),"has_change_history"in c&&n(2,j=c.has_change_history),"camera_position"in c&&n(17,G=c.camera_position),"interactive"in c&&n(18,O=c.interactive)},s.$$.update=()=>{s.$$.dirty[0]&524288&&n(22,S=!P)},[_,a,j,t,l,i,r,o,u,f,d,m,E,k,b,M,L,G,O,P,g,ue,S,R,X,fe,ce,me,de,h,We,Ae,Ge,Oe]}class wn extends Nt{constructor(e){super(),Pt(this,e,Kt,Jt,Tt,{elem_id:3,elem_classes:4,visible:5,value:0,root:6,display_mode:7,clear_color:8,loading_status:1,label:9,show_label:10,container:11,scale:12,min_width:13,gradio:14,height:15,zoom_speed:16,input_ready:22,has_change_history:2,camera_position:17,interactive:18},null,[-1,-1])}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),p()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),p()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),p()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),p()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),p()}get display_mode(){return this.$$.ctx[7]}set display_mode(e){this.$$set({display_mode:e}),p()}get clear_color(){return this.$$.ctx[8]}set clear_color(e){this.$$set({clear_color:e}),p()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),p()}get label(){return this.$$.ctx[9]}set label(e){this.$$set({label:e}),p()}get show_label(){return this.$$.ctx[10]}set show_label(e){this.$$set({show_label:e}),p()}get container(){return this.$$.ctx[11]}set container(e){this.$$set({container:e}),p()}get scale(){return this.$$.ctx[12]}set scale(e){this.$$set({scale:e}),p()}get min_width(){return this.$$.ctx[13]}set min_width(e){this.$$set({min_width:e}),p()}get gradio(){return this.$$.ctx[14]}set gradio(e){this.$$set({gradio:e}),p()}get height(){return this.$$.ctx[15]}set height(e){this.$$set({height:e}),p()}get zoom_speed(){return this.$$.ctx[16]}set zoom_speed(e){this.$$set({zoom_speed:e}),p()}get input_ready(){return this.$$.ctx[22]}set input_ready(e){this.$$set({input_ready:e}),p()}get has_change_history(){return this.$$.ctx[2]}set has_change_history(e){this.$$set({has_change_history:e}),p()}get camera_position(){return this.$$.ctx[17]}set camera_position(e){this.$$set({camera_position:e}),p()}get interactive(){return this.$$.ctx[18]}set interactive(e){this.$$set({interactive:e}),p()}}export{vn as BaseExample,_t as BaseModel3D,Bt as BaseModel3DUpload,wn as default};
//# sourceMappingURL=Index-DTpkVUiy.js.map
