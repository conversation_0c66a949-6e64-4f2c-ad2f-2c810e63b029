import{c as n,d as i}from"./KHR_interactivity-DTxiAnOo.js";import{b as a,R as v}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./declarationMapper-BZjsjg7g.js";import"./objectModelMapping-BR4RdEzn.js";class c extends n{constructor(e){super(e),this.config=e,this.initPriority=1;for(const t in this.config.eventData)this.registerDataOutput(t,this.config.eventData[t].type)}_preparePendingTasks(e){const t=e.configuration.coordinator.getCustomEventObservable(this.config.eventId);if(t&&t.hasObservers()&&t.observers.length>i.MaxEventsPerType){this._reportError(e,`FlowGraphReceiveCustomEventBlock: Too many observers for event ${this.config.eventId}. Max is ${i.MaxEventsPerType}.`);return}const r=t.add(o=>{Object.keys(o).forEach(s=>{this.getDataOutput(s)?.setValue(o[s],e)}),this._execute(e)});e._setExecutionVariable(this,"_eventObserver",r)}_cancelPendingTasks(e){const t=e.configuration.coordinator.getCustomEventObservable(this.config.eventId);if(t){const r=e._getExecutionVariable(this,"_eventObserver",null);t.remove(r)}else a.Warn(`FlowGraphReceiveCustomEventBlock: Missing observable for event ${this.config.eventId}`)}_executeEvent(e,t){return!0}getClassName(){return"FlowGraphReceiveCustomEventBlock"}}v("FlowGraphReceiveCustomEventBlock",c);export{c as FlowGraphReceiveCustomEventBlock};
//# sourceMappingURL=flowGraphReceiveCustomEventBlock-DnQSLCSP.js.map
