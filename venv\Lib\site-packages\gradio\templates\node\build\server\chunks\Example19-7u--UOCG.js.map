{"version": 3, "file": "Example19-7u--UOCG.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Example19.js"], "sourcesContent": ["import{create_ssr_component as c,escape as A}from\"svelte/internal\";const n={code:\".gallery.svelte-1ayixqk{padding:var(--size-1) var(--size-2)}\",map:'{\"version\":3,\"file\":\"Example.svelte\",\"sources\":[\"Example.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">export let value;\\\\nexport let type;\\\\nexport let selected = false;\\\\n<\\/script>\\\\n\\\\n<div\\\\n\\\\tclass:table={type === \\\\\"table\\\\\"}\\\\n\\\\tclass:gallery={type === \\\\\"gallery\\\\\"}\\\\n\\\\tclass:selected\\\\n>\\\\n\\\\t{value ? value : \\\\\"\\\\\"}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.gallery {\\\\n\\\\t\\\\tpadding: var(--size-1) var(--size-2);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAcC,uBAAS,CACR,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CACpC\"}'},i=c((v,e,t,r)=>{let{value:l}=e,{type:a}=e,{selected:s=!1}=e;return e.value===void 0&&t.value&&l!==void 0&&t.value(l),e.type===void 0&&t.type&&a!==void 0&&t.type(a),e.selected===void 0&&t.selected&&s!==void 0&&t.selected(s),v.css.add(n),`<div class=\"${[\"svelte-1ayixqk\",(a===\"table\"?\"table\":\"\")+\" \"+(a===\"gallery\"?\"gallery\":\"\")+\" \"+(s?\"selected\":\"\")].join(\" \").trim()}\">${A(l||\"\")} </div>`});export{i as default};\n//# sourceMappingURL=Example19.js.map\n"], "names": ["c", "A"], "mappings": ";;AAAwE,MAAC,CAAC,CAAC,CAAC,IAAI,CAAC,8DAA8D,CAAC,GAAG,CAAC,ghBAAghB,CAAC,CAAC,CAAC,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAEC,MAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;;;;"}