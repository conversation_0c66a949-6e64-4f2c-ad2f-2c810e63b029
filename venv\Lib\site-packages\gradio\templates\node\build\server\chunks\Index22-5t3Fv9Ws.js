import { c as create_ssr_component, v as validate_component, e as escape, f as each, d as add_attribute } from './ssr-C3HYbsxA.js';
import { m as mt, z as zA, N as xt } from './2-DJbI4FWc.js';
import './index-ClteBeTX.js';
import './Component-NmRBwSfF.js';
import 'path';
import 'url';
import 'fs';

const D={code:`.wrap.svelte-1e02hys.svelte-1e02hys.svelte-1e02hys{display:flex;flex-wrap:wrap;gap:var(--checkbox-label-gap)}label.svelte-1e02hys.svelte-1e02hys.svelte-1e02hys{display:flex;align-items:center;transition:var(--button-transition);cursor:pointer;box-shadow:var(--checkbox-label-shadow);border:var(--checkbox-label-border-width) solid
			var(--checkbox-label-border-color);border-radius:var(--checkbox-border-radius);background:var(--checkbox-label-background-fill);padding:var(--checkbox-label-padding);color:var(--checkbox-label-text-color);font-weight:var(--checkbox-label-text-weight);font-size:var(--checkbox-label-text-size);line-height:var(--line-md)}label.svelte-1e02hys.svelte-1e02hys.svelte-1e02hys:hover{background:var(--checkbox-label-background-fill-hover)}label.svelte-1e02hys.svelte-1e02hys.svelte-1e02hys:focus{background:var(--checkbox-label-background-fill-focus)}label.selected.svelte-1e02hys.svelte-1e02hys.svelte-1e02hys{background:var(--checkbox-label-background-fill-selected);color:var(--checkbox-label-text-color-selected);border-color:var(--checkbox-label-border-color-selected)}label.svelte-1e02hys>.svelte-1e02hys+.svelte-1e02hys{margin-left:var(--size-2)}input.svelte-1e02hys.svelte-1e02hys.svelte-1e02hys{--ring-color:transparent;position:relative;box-shadow:var(--checkbox-shadow);border:var(--checkbox-border-width) solid var(--checkbox-border-color);border-radius:var(--checkbox-border-radius);background-color:var(--checkbox-background-color);line-height:var(--line-sm)}input.svelte-1e02hys.svelte-1e02hys.svelte-1e02hys:checked,input.svelte-1e02hys.svelte-1e02hys.svelte-1e02hys:checked:hover,input.svelte-1e02hys.svelte-1e02hys.svelte-1e02hys:checked:focus{border-color:var(--checkbox-border-color-selected);background-image:var(--checkbox-check);background-color:var(--checkbox-background-color-selected)}input.svelte-1e02hys.svelte-1e02hys.svelte-1e02hys:checked:focus{border-color:var(--checkbox-border-color-focus);background-image:var(--checkbox-check);background-color:var(--checkbox-background-color-selected)}input.svelte-1e02hys.svelte-1e02hys.svelte-1e02hys:hover{border-color:var(--checkbox-border-color-hover);background-color:var(--checkbox-background-color-hover)}input.svelte-1e02hys.svelte-1e02hys.svelte-1e02hys:not(:checked):focus{border-color:var(--checkbox-border-color-focus)}input[disabled].svelte-1e02hys.svelte-1e02hys.svelte-1e02hys,.disabled.svelte-1e02hys.svelte-1e02hys.svelte-1e02hys{cursor:not-allowed}input.svelte-1e02hys.svelte-1e02hys.svelte-1e02hys:hover{cursor:pointer}`,map:'{"version":3,"file":"Index.svelte","sources":["Index.svelte"],"sourcesContent":["<svelte:options immutable={true} />\\n\\n<script lang=\\"ts\\">import { Block, BlockTitle } from \\"@gradio/atoms\\";\\nimport { StatusTracker } from \\"@gradio/statustracker\\";\\nexport let gradio;\\nexport let elem_id = \\"\\";\\nexport let elem_classes = [];\\nexport let visible = true;\\nexport let value = [];\\nexport let choices;\\nexport let container = true;\\nexport let scale = null;\\nexport let min_width = void 0;\\nexport let label = gradio.i18n(\\"checkbox.checkbox_group\\");\\nexport let info = void 0;\\nexport let show_label = true;\\nexport let loading_status;\\nexport let interactive = true;\\nexport let old_value = value.slice();\\nfunction toggle_choice(choice) {\\n    if (value.includes(choice)) {\\n        value = value.filter((v) => v !== choice);\\n    }\\n    else {\\n        value = [...value, choice];\\n    }\\n    gradio.dispatch(\\"input\\");\\n}\\n$: disabled = !interactive;\\n$: if (JSON.stringify(old_value) !== JSON.stringify(value)) {\\n    old_value = value;\\n    gradio.dispatch(\\"change\\");\\n}\\n<\/script>\\n\\n<Block\\n\\t{visible}\\n\\t{elem_id}\\n\\t{elem_classes}\\n\\ttype=\\"fieldset\\"\\n\\t{container}\\n\\t{scale}\\n\\t{min_width}\\n>\\n\\t<StatusTracker\\n\\t\\tautoscroll={gradio.autoscroll}\\n\\t\\ti18n={gradio.i18n}\\n\\t\\t{...loading_status}\\n\\t\\ton:clear_status={() => gradio.dispatch(\\"clear_status\\", loading_status)}\\n\\t/>\\n\\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\\n\\n\\t<div class=\\"wrap\\" data-testid=\\"checkbox-group\\">\\n\\t\\t{#each choices as [display_value, internal_value], i}\\n\\t\\t\\t<label class:disabled class:selected={value.includes(internal_value)}>\\n\\t\\t\\t\\t<input\\n\\t\\t\\t\\t\\t{disabled}\\n\\t\\t\\t\\t\\ton:change={() => toggle_choice(internal_value)}\\n\\t\\t\\t\\t\\ton:input={(evt) =>\\n\\t\\t\\t\\t\\t\\tgradio.dispatch(\\"select\\", {\\n\\t\\t\\t\\t\\t\\t\\tindex: i,\\n\\t\\t\\t\\t\\t\\t\\tvalue: internal_value,\\n\\t\\t\\t\\t\\t\\t\\tselected: evt.currentTarget.checked\\n\\t\\t\\t\\t\\t\\t})}\\n\\t\\t\\t\\t\\ton:keydown={(event) => {\\n\\t\\t\\t\\t\\t\\tif (event.key === \\"Enter\\") {\\n\\t\\t\\t\\t\\t\\t\\ttoggle_choice(internal_value);\\n\\t\\t\\t\\t\\t\\t\\tgradio.dispatch(\\"select\\", {\\n\\t\\t\\t\\t\\t\\t\\t\\tindex: i,\\n\\t\\t\\t\\t\\t\\t\\t\\tvalue: internal_value,\\n\\t\\t\\t\\t\\t\\t\\t\\tselected: !value.includes(internal_value)\\n\\t\\t\\t\\t\\t\\t\\t});\\n\\t\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\t}}\\n\\t\\t\\t\\t\\tchecked={value.includes(internal_value)}\\n\\t\\t\\t\\t\\ttype=\\"checkbox\\"\\n\\t\\t\\t\\t\\tname={internal_value?.toString()}\\n\\t\\t\\t\\t\\ttitle={internal_value?.toString()}\\n\\t\\t\\t\\t/>\\n\\t\\t\\t\\t<span class=\\"ml-2\\">{display_value}</span>\\n\\t\\t\\t</label>\\n\\t\\t{/each}\\n\\t</div>\\n</Block>\\n\\n<style>\\n\\t.wrap {\\n\\t\\tdisplay: flex;\\n\\t\\tflex-wrap: wrap;\\n\\t\\tgap: var(--checkbox-label-gap);\\n\\t}\\n\\tlabel {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\ttransition: var(--button-transition);\\n\\t\\tcursor: pointer;\\n\\t\\tbox-shadow: var(--checkbox-label-shadow);\\n\\t\\tborder: var(--checkbox-label-border-width) solid\\n\\t\\t\\tvar(--checkbox-label-border-color);\\n\\t\\tborder-radius: var(--checkbox-border-radius);\\n\\t\\tbackground: var(--checkbox-label-background-fill);\\n\\t\\tpadding: var(--checkbox-label-padding);\\n\\t\\tcolor: var(--checkbox-label-text-color);\\n\\t\\tfont-weight: var(--checkbox-label-text-weight);\\n\\t\\tfont-size: var(--checkbox-label-text-size);\\n\\t\\tline-height: var(--line-md);\\n\\t}\\n\\n\\tlabel:hover {\\n\\t\\tbackground: var(--checkbox-label-background-fill-hover);\\n\\t}\\n\\tlabel:focus {\\n\\t\\tbackground: var(--checkbox-label-background-fill-focus);\\n\\t}\\n\\tlabel.selected {\\n\\t\\tbackground: var(--checkbox-label-background-fill-selected);\\n\\t\\tcolor: var(--checkbox-label-text-color-selected);\\n\\t\\tborder-color: var(--checkbox-label-border-color-selected);\\n\\t}\\n\\n\\tlabel > * + * {\\n\\t\\tmargin-left: var(--size-2);\\n\\t}\\n\\n\\tinput {\\n\\t\\t--ring-color: transparent;\\n\\t\\tposition: relative;\\n\\t\\tbox-shadow: var(--checkbox-shadow);\\n\\t\\tborder: var(--checkbox-border-width) solid var(--checkbox-border-color);\\n\\t\\tborder-radius: var(--checkbox-border-radius);\\n\\t\\tbackground-color: var(--checkbox-background-color);\\n\\t\\tline-height: var(--line-sm);\\n\\t}\\n\\n\\tinput:checked,\\n\\tinput:checked:hover,\\n\\tinput:checked:focus {\\n\\t\\tborder-color: var(--checkbox-border-color-selected);\\n\\t\\tbackground-image: var(--checkbox-check);\\n\\t\\tbackground-color: var(--checkbox-background-color-selected);\\n\\t}\\n\\n\\tinput:checked:focus {\\n\\t\\tborder-color: var(--checkbox-border-color-focus);\\n\\t\\tbackground-image: var(--checkbox-check);\\n\\t\\tbackground-color: var(--checkbox-background-color-selected);\\n\\t}\\n\\n\\tinput:hover {\\n\\t\\tborder-color: var(--checkbox-border-color-hover);\\n\\t\\tbackground-color: var(--checkbox-background-color-hover);\\n\\t}\\n\\n\\tinput:not(:checked):focus {\\n\\t\\tborder-color: var(--checkbox-border-color-focus);\\n\\t}\\n\\n\\tinput[disabled],\\n\\t.disabled {\\n\\t\\tcursor: not-allowed;\\n\\t}\\n\\n\\tinput:hover {\\n\\t\\tcursor: pointer;\\n\\t}</style>\\n"],"names":[],"mappings":"AAsFC,kDAAM,CACL,OAAO,CAAE,IAAI,CACb,SAAS,CAAE,IAAI,CACf,GAAG,CAAE,IAAI,oBAAoB,CAC9B,CACA,kDAAM,CACL,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,UAAU,CAAE,IAAI,mBAAmB,CAAC,CACpC,MAAM,CAAE,OAAO,CACf,UAAU,CAAE,IAAI,uBAAuB,CAAC,CACxC,MAAM,CAAE,IAAI,6BAA6B,CAAC,CAAC,KAAK;AAClD,GAAG,IAAI,6BAA6B,CAAC,CACnC,aAAa,CAAE,IAAI,wBAAwB,CAAC,CAC5C,UAAU,CAAE,IAAI,gCAAgC,CAAC,CACjD,OAAO,CAAE,IAAI,wBAAwB,CAAC,CACtC,KAAK,CAAE,IAAI,2BAA2B,CAAC,CACvC,WAAW,CAAE,IAAI,4BAA4B,CAAC,CAC9C,SAAS,CAAE,IAAI,0BAA0B,CAAC,CAC1C,WAAW,CAAE,IAAI,SAAS,CAC3B,CAEA,kDAAK,MAAO,CACX,UAAU,CAAE,IAAI,sCAAsC,CACvD,CACA,kDAAK,MAAO,CACX,UAAU,CAAE,IAAI,sCAAsC,CACvD,CACA,KAAK,sDAAU,CACd,UAAU,CAAE,IAAI,yCAAyC,CAAC,CAC1D,KAAK,CAAE,IAAI,oCAAoC,CAAC,CAChD,YAAY,CAAE,IAAI,sCAAsC,CACzD,CAEA,oBAAK,CAAG,eAAC,CAAG,eAAE,CACb,WAAW,CAAE,IAAI,QAAQ,CAC1B,CAEA,kDAAM,CACL,YAAY,CAAE,WAAW,CACzB,QAAQ,CAAE,QAAQ,CAClB,UAAU,CAAE,IAAI,iBAAiB,CAAC,CAClC,MAAM,CAAE,IAAI,uBAAuB,CAAC,CAAC,KAAK,CAAC,IAAI,uBAAuB,CAAC,CACvE,aAAa,CAAE,IAAI,wBAAwB,CAAC,CAC5C,gBAAgB,CAAE,IAAI,2BAA2B,CAAC,CAClD,WAAW,CAAE,IAAI,SAAS,CAC3B,CAEA,kDAAK,QAAQ,CACb,kDAAK,QAAQ,MAAM,CACnB,kDAAK,QAAQ,MAAO,CACnB,YAAY,CAAE,IAAI,gCAAgC,CAAC,CACnD,gBAAgB,CAAE,IAAI,gBAAgB,CAAC,CACvC,gBAAgB,CAAE,IAAI,oCAAoC,CAC3D,CAEA,kDAAK,QAAQ,MAAO,CACnB,YAAY,CAAE,IAAI,6BAA6B,CAAC,CAChD,gBAAgB,CAAE,IAAI,gBAAgB,CAAC,CACvC,gBAAgB,CAAE,IAAI,oCAAoC,CAC3D,CAEA,kDAAK,MAAO,CACX,YAAY,CAAE,IAAI,6BAA6B,CAAC,CAChD,gBAAgB,CAAE,IAAI,iCAAiC,CACxD,CAEA,kDAAK,KAAK,QAAQ,CAAC,MAAO,CACzB,YAAY,CAAE,IAAI,6BAA6B,CAChD,CAEA,KAAK,CAAC,QAAQ,8CAAC,CACf,sDAAU,CACT,MAAM,CAAE,WACT,CAEA,kDAAK,MAAO,CACX,MAAM,CAAE,OACT"}'},U=create_ssr_component((o,e,t,K)=>{let r,{gradio:l}=e,{elem_id:n=""}=e,{elem_classes:s=[]}=e,{visible:i=!0}=e,{value:A=[]}=e,{choices:C}=e,{container:d=!0}=e,{scale:v=null}=e,{min_width:h=void 0}=e,{label:b=l.i18n("checkbox.checkbox_group")}=e,{info:u=void 0}=e,{show_label:k=!0}=e,{loading_status:x}=e,{interactive:g=!0}=e,{old_value:c=A.slice()}=e;return e.gradio===void 0&&t.gradio&&l!==void 0&&t.gradio(l),e.elem_id===void 0&&t.elem_id&&n!==void 0&&t.elem_id(n),e.elem_classes===void 0&&t.elem_classes&&s!==void 0&&t.elem_classes(s),e.visible===void 0&&t.visible&&i!==void 0&&t.visible(i),e.value===void 0&&t.value&&A!==void 0&&t.value(A),e.choices===void 0&&t.choices&&C!==void 0&&t.choices(C),e.container===void 0&&t.container&&d!==void 0&&t.container(d),e.scale===void 0&&t.scale&&v!==void 0&&t.scale(v),e.min_width===void 0&&t.min_width&&h!==void 0&&t.min_width(h),e.label===void 0&&t.label&&b!==void 0&&t.label(b),e.info===void 0&&t.info&&u!==void 0&&t.info(u),e.show_label===void 0&&t.show_label&&k!==void 0&&t.show_label(k),e.loading_status===void 0&&t.loading_status&&x!==void 0&&t.loading_status(x),e.interactive===void 0&&t.interactive&&g!==void 0&&t.interactive(g),e.old_value===void 0&&t.old_value&&c!==void 0&&t.old_value(c),o.css.add(D),r=!g,JSON.stringify(c)!==JSON.stringify(A)&&(c=A,l.dispatch("change")),`  ${validate_component(mt,"Block").$$render(o,{visible:i,elem_id:n,elem_classes:s,type:"fieldset",container:d,scale:v,min_width:h},{},{default:()=>`${validate_component(zA,"StatusTracker").$$render(o,Object.assign({},{autoscroll:l.autoscroll},{i18n:l.i18n},x),{},{})} ${validate_component(xt,"BlockTitle").$$render(o,{show_label:k,info:u},{},{default:()=>`${escape(b)}`})} <div class="wrap svelte-1e02hys" data-testid="checkbox-group">${each(C,([y,a],M)=>`<label class="${["svelte-1e02hys",(r?"disabled":"")+" "+(A.includes(a)?"selected":"")].join(" ").trim()}"><input ${r?"disabled":""} ${A.includes(a)?"checked":""} type="checkbox"${add_attribute("name",a?.toString(),0)}${add_attribute("title",a?.toString(),0)} class="svelte-1e02hys"> <span class="ml-2 svelte-1e02hys">${escape(y)}</span> </label>`)}</div>`})}`});

export { U as default };
//# sourceMappingURL=Index22-5t3Fv9Ws.js.map
