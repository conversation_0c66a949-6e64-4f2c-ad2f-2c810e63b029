import { c as create_ssr_component, v as validate_component, e as escape, f as each, b as createEventDispatcher, d as add_attribute } from './ssr-C3HYbsxA.js';
import { m as mt, z as zA, N as xt } from './2-DJbI4FWc.js';
export { default as BaseExample } from './Example23-DpqwuJHm.js';
import './index-ClteBeTX.js';
import './Component-NmRBwSfF.js';
import 'path';
import 'url';
import 'fs';

const W={code:`label.svelte-1bx8sav.svelte-1bx8sav.svelte-1bx8sav{display:flex;align-items:center;transition:var(--button-transition);cursor:pointer;box-shadow:var(--checkbox-label-shadow);border:var(--checkbox-label-border-width) solid
			var(--checkbox-label-border-color);border-radius:var(--checkbox-border-radius);background:var(--checkbox-label-background-fill);padding:var(--checkbox-label-padding);color:var(--checkbox-label-text-color);font-weight:var(--checkbox-label-text-weight);font-size:var(--checkbox-label-text-size);line-height:var(--line-md)}label.svelte-1bx8sav.svelte-1bx8sav.svelte-1bx8sav:hover{background:var(--checkbox-label-background-fill-hover)}label.svelte-1bx8sav.svelte-1bx8sav.svelte-1bx8sav:focus{background:var(--checkbox-label-background-fill-focus)}label.selected.svelte-1bx8sav.svelte-1bx8sav.svelte-1bx8sav{background:var(--checkbox-label-background-fill-selected);color:var(--checkbox-label-text-color-selected);border-color:var(--checkbox-label-border-color-selected)}label.svelte-1bx8sav>.svelte-1bx8sav+.svelte-1bx8sav{margin-left:var(--size-2)}label.rtl.svelte-1bx8sav>.svelte-1bx8sav+.svelte-1bx8sav{margin-left:0;margin-right:var(--size-2)}input.svelte-1bx8sav.svelte-1bx8sav.svelte-1bx8sav{--ring-color:transparent;position:relative;box-shadow:var(--checkbox-shadow);border:var(--checkbox-border-width) solid var(--checkbox-border-color);border-radius:var(--radius-full);background-color:var(--checkbox-background-color);line-height:var(--line-sm)}input.svelte-1bx8sav.svelte-1bx8sav.svelte-1bx8sav:checked,input.svelte-1bx8sav.svelte-1bx8sav.svelte-1bx8sav:checked:hover{border-color:var(--checkbox-border-color-selected);background-image:var(--radio-circle);background-color:var(--checkbox-background-color-selected)}input.svelte-1bx8sav.svelte-1bx8sav.svelte-1bx8sav:checked::after{content:"";position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);border-radius:50%;background-color:white}input.svelte-1bx8sav.svelte-1bx8sav.svelte-1bx8sav:hover{border-color:var(--checkbox-border-color-hover);background-color:var(--checkbox-background-color-hover)}input.svelte-1bx8sav.svelte-1bx8sav.svelte-1bx8sav:focus{border-color:var(--checkbox-border-color-focus);background-color:var(--checkbox-background-color-focus)}input.svelte-1bx8sav.svelte-1bx8sav.svelte-1bx8sav:checked:focus{border-color:var(--checkbox-border-color-focus);background-image:var(--radio-circle);background-color:var(--checkbox-background-color-selected)}input[disabled].svelte-1bx8sav.svelte-1bx8sav.svelte-1bx8sav,.disabled.svelte-1bx8sav.svelte-1bx8sav.svelte-1bx8sav{cursor:not-allowed}`,map:'{"version":3,"file":"Radio.svelte","sources":["Radio.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\tlet id = 0;\\n<\/script>\\n\\n<script lang=\\"ts\\">import { createEventDispatcher } from \\"svelte\\";\\nexport let display_value;\\nexport let internal_value;\\nexport let disabled = false;\\nexport let selected = null;\\nexport let rtl = false;\\nconst dispatch = createEventDispatcher();\\nlet is_selected = false;\\nasync function handle_input(selected2, internal_value2) {\\n    is_selected = selected2 === internal_value2;\\n    if (is_selected) {\\n        dispatch(\\"input\\", internal_value2);\\n    }\\n}\\n$: handle_input(selected, internal_value);\\n<\/script>\\n\\n<label\\n\\tclass:disabled\\n\\tclass:selected={is_selected}\\n\\tdata-testid=\\"{display_value}-radio-label\\"\\n\\tclass:rtl\\n>\\n\\t<input\\n\\t\\t{disabled}\\n\\t\\ttype=\\"radio\\"\\n\\t\\tname=\\"radio-{++id}\\"\\n\\t\\tvalue={internal_value}\\n\\t\\taria-checked={is_selected}\\n\\t\\tbind:group={selected}\\n\\t/>\\n\\t<span>{display_value}</span>\\n</label>\\n\\n<style>\\n\\tlabel {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\ttransition: var(--button-transition);\\n\\t\\tcursor: pointer;\\n\\t\\tbox-shadow: var(--checkbox-label-shadow);\\n\\t\\tborder: var(--checkbox-label-border-width) solid\\n\\t\\t\\tvar(--checkbox-label-border-color);\\n\\t\\tborder-radius: var(--checkbox-border-radius);\\n\\t\\tbackground: var(--checkbox-label-background-fill);\\n\\t\\tpadding: var(--checkbox-label-padding);\\n\\t\\tcolor: var(--checkbox-label-text-color);\\n\\t\\tfont-weight: var(--checkbox-label-text-weight);\\n\\t\\tfont-size: var(--checkbox-label-text-size);\\n\\t\\tline-height: var(--line-md);\\n\\t}\\n\\n\\tlabel:hover {\\n\\t\\tbackground: var(--checkbox-label-background-fill-hover);\\n\\t}\\n\\tlabel:focus {\\n\\t\\tbackground: var(--checkbox-label-background-fill-focus);\\n\\t}\\n\\n\\tlabel.selected {\\n\\t\\tbackground: var(--checkbox-label-background-fill-selected);\\n\\t\\tcolor: var(--checkbox-label-text-color-selected);\\n\\t\\tborder-color: var(--checkbox-label-border-color-selected);\\n\\t}\\n\\n\\tlabel > * + * {\\n\\t\\tmargin-left: var(--size-2);\\n\\t}\\n\\n\\tlabel.rtl > * + * {\\n\\t\\tmargin-left: 0;\\n\\t\\tmargin-right: var(--size-2);\\n\\t}\\n\\n\\tinput {\\n\\t\\t--ring-color: transparent;\\n\\t\\tposition: relative;\\n\\t\\tbox-shadow: var(--checkbox-shadow);\\n\\t\\tborder: var(--checkbox-border-width) solid var(--checkbox-border-color);\\n\\t\\tborder-radius: var(--radius-full);\\n\\t\\tbackground-color: var(--checkbox-background-color);\\n\\t\\tline-height: var(--line-sm);\\n\\t}\\n\\n\\tinput:checked,\\n\\tinput:checked:hover {\\n\\t\\tborder-color: var(--checkbox-border-color-selected);\\n\\t\\tbackground-image: var(--radio-circle);\\n\\t\\tbackground-color: var(--checkbox-background-color-selected);\\n\\t}\\n\\n\\tinput:checked::after {\\n\\t\\tcontent: \\"\\";\\n\\t\\tposition: absolute;\\n\\t\\ttop: 50%;\\n\\t\\tleft: 50%;\\n\\t\\ttransform: translate(-50%, -50%);\\n\\t\\tborder-radius: 50%;\\n\\t\\tbackground-color: white;\\n\\t}\\n\\n\\tinput:hover {\\n\\t\\tborder-color: var(--checkbox-border-color-hover);\\n\\t\\tbackground-color: var(--checkbox-background-color-hover);\\n\\t}\\n\\n\\tinput:focus {\\n\\t\\tborder-color: var(--checkbox-border-color-focus);\\n\\t\\tbackground-color: var(--checkbox-background-color-focus);\\n\\t}\\n\\n\\tinput:checked:focus {\\n\\t\\tborder-color: var(--checkbox-border-color-focus);\\n\\t\\tbackground-image: var(--radio-circle);\\n\\t\\tbackground-color: var(--checkbox-background-color-selected);\\n\\t}\\n\\n\\tinput[disabled],\\n\\t.disabled {\\n\\t\\tcursor: not-allowed;\\n\\t}</style>\\n"],"names":[],"mappings":"AAuCC,kDAAM,CACL,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,UAAU,CAAE,IAAI,mBAAmB,CAAC,CACpC,MAAM,CAAE,OAAO,CACf,UAAU,CAAE,IAAI,uBAAuB,CAAC,CACxC,MAAM,CAAE,IAAI,6BAA6B,CAAC,CAAC,KAAK;AAClD,GAAG,IAAI,6BAA6B,CAAC,CACnC,aAAa,CAAE,IAAI,wBAAwB,CAAC,CAC5C,UAAU,CAAE,IAAI,gCAAgC,CAAC,CACjD,OAAO,CAAE,IAAI,wBAAwB,CAAC,CACtC,KAAK,CAAE,IAAI,2BAA2B,CAAC,CACvC,WAAW,CAAE,IAAI,4BAA4B,CAAC,CAC9C,SAAS,CAAE,IAAI,0BAA0B,CAAC,CAC1C,WAAW,CAAE,IAAI,SAAS,CAC3B,CAEA,kDAAK,MAAO,CACX,UAAU,CAAE,IAAI,sCAAsC,CACvD,CACA,kDAAK,MAAO,CACX,UAAU,CAAE,IAAI,sCAAsC,CACvD,CAEA,KAAK,sDAAU,CACd,UAAU,CAAE,IAAI,yCAAyC,CAAC,CAC1D,KAAK,CAAE,IAAI,oCAAoC,CAAC,CAChD,YAAY,CAAE,IAAI,sCAAsC,CACzD,CAEA,oBAAK,CAAG,eAAC,CAAG,eAAE,CACb,WAAW,CAAE,IAAI,QAAQ,CAC1B,CAEA,KAAK,mBAAI,CAAG,eAAC,CAAG,eAAE,CACjB,WAAW,CAAE,CAAC,CACd,YAAY,CAAE,IAAI,QAAQ,CAC3B,CAEA,kDAAM,CACL,YAAY,CAAE,WAAW,CACzB,QAAQ,CAAE,QAAQ,CAClB,UAAU,CAAE,IAAI,iBAAiB,CAAC,CAClC,MAAM,CAAE,IAAI,uBAAuB,CAAC,CAAC,KAAK,CAAC,IAAI,uBAAuB,CAAC,CACvE,aAAa,CAAE,IAAI,aAAa,CAAC,CACjC,gBAAgB,CAAE,IAAI,2BAA2B,CAAC,CAClD,WAAW,CAAE,IAAI,SAAS,CAC3B,CAEA,kDAAK,QAAQ,CACb,kDAAK,QAAQ,MAAO,CACnB,YAAY,CAAE,IAAI,gCAAgC,CAAC,CACnD,gBAAgB,CAAE,IAAI,cAAc,CAAC,CACrC,gBAAgB,CAAE,IAAI,oCAAoC,CAC3D,CAEA,kDAAK,QAAQ,OAAQ,CACpB,OAAO,CAAE,EAAE,CACX,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,GAAG,CACR,IAAI,CAAE,GAAG,CACT,SAAS,CAAE,UAAU,IAAI,CAAC,CAAC,IAAI,CAAC,CAChC,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,KACnB,CAEA,kDAAK,MAAO,CACX,YAAY,CAAE,IAAI,6BAA6B,CAAC,CAChD,gBAAgB,CAAE,IAAI,iCAAiC,CACxD,CAEA,kDAAK,MAAO,CACX,YAAY,CAAE,IAAI,6BAA6B,CAAC,CAChD,gBAAgB,CAAE,IAAI,iCAAiC,CACxD,CAEA,kDAAK,QAAQ,MAAO,CACnB,YAAY,CAAE,IAAI,6BAA6B,CAAC,CAChD,gBAAgB,CAAE,IAAI,cAAc,CAAC,CACrC,gBAAgB,CAAE,IAAI,oCAAoC,CAC3D,CAEA,KAAK,CAAC,QAAQ,8CAAC,CACf,sDAAU,CACT,MAAM,CAAE,WACT"}'};let Y=0;const z=create_ssr_component((l,e,t,p)=>{let{display_value:n}=e,{internal_value:A}=e,{disabled:o=!1}=e,{selected:r=null}=e,{rtl:s=!1}=e;const d=createEventDispatcher();let c=!1;async function a(C,i){c=C===i,c&&d("input",i);}return e.display_value===void 0&&t.display_value&&n!==void 0&&t.display_value(n),e.internal_value===void 0&&t.internal_value&&A!==void 0&&t.internal_value(A),e.disabled===void 0&&t.disabled&&o!==void 0&&t.disabled(o),e.selected===void 0&&t.selected&&r!==void 0&&t.selected(r),e.rtl===void 0&&t.rtl&&s!==void 0&&t.rtl(s),l.css.add(W),a(r,A),`<label data-testid="${escape(n,!0)+"-radio-label"}" class="${["svelte-1bx8sav",(o?"disabled":"")+" "+(c?"selected":"")+" "+(s?"rtl":"")].join(" ").trim()}"><input ${o?"disabled":""} type="radio" name="${"radio-"+escape(++Y,!0)}"${add_attribute("value",A,0)}${add_attribute("aria-checked",c,0)} class="svelte-1bx8sav"${A===r?add_attribute("checked",!0,1):""}> <span class="svelte-1bx8sav">${escape(n)}</span> </label>`}),R=z,T={code:".wrap.svelte-1kzox3m{display:flex;flex-wrap:wrap;gap:var(--checkbox-label-gap)}",map:'{"version":3,"file":"Index.svelte","sources":["Index.svelte"],"sourcesContent":["<script context=\\"module\\" lang=\\"ts\\">export { default as BaseRadio } from \\"./shared/Radio.svelte\\";\\nexport { default as BaseExample } from \\"./Example.svelte\\";\\n<\/script>\\n\\n<script lang=\\"ts\\">import { Block, BlockTitle } from \\"@gradio/atoms\\";\\nimport { StatusTracker } from \\"@gradio/statustracker\\";\\nimport BaseRadio from \\"./shared/Radio.svelte\\";\\nexport let gradio;\\nexport let label = gradio.i18n(\\"radio.radio\\");\\nexport let info = void 0;\\nexport let elem_id = \\"\\";\\nexport let elem_classes = [];\\nexport let visible = true;\\nexport let value = null;\\nexport let choices = [];\\nexport let show_label = true;\\nexport let container = false;\\nexport let scale = null;\\nexport let min_width = void 0;\\nexport let loading_status;\\nexport let interactive = true;\\nexport let rtl = false;\\nfunction handle_change() {\\n    gradio.dispatch(\\"change\\");\\n}\\nlet old_value = value;\\n$: {\\n    if (value !== old_value) {\\n        old_value = value;\\n        handle_change();\\n    }\\n}\\n$: disabled = !interactive;\\n<\/script>\\n\\n<Block\\n\\t{visible}\\n\\ttype=\\"fieldset\\"\\n\\t{elem_id}\\n\\t{elem_classes}\\n\\t{container}\\n\\t{scale}\\n\\t{min_width}\\n\\t{rtl}\\n>\\n\\t<StatusTracker\\n\\t\\tautoscroll={gradio.autoscroll}\\n\\t\\ti18n={gradio.i18n}\\n\\t\\t{...loading_status}\\n\\t\\ton:clear_status={() => gradio.dispatch(\\"clear_status\\", loading_status)}\\n\\t/>\\n\\n\\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\\n\\n\\t<div class=\\"wrap\\">\\n\\t\\t{#each choices as [display_value, internal_value], i (i)}\\n\\t\\t\\t<BaseRadio\\n\\t\\t\\t\\t{display_value}\\n\\t\\t\\t\\t{internal_value}\\n\\t\\t\\t\\tbind:selected={value}\\n\\t\\t\\t\\t{disabled}\\n\\t\\t\\t\\t{rtl}\\n\\t\\t\\t\\ton:input={() => {\\n\\t\\t\\t\\t\\tgradio.dispatch(\\"select\\", { value: internal_value, index: i });\\n\\t\\t\\t\\t\\tgradio.dispatch(\\"input\\");\\n\\t\\t\\t\\t}}\\n\\t\\t\\t/>\\n\\t\\t{/each}\\n\\t</div>\\n</Block>\\n\\n<style>\\n\\t.wrap {\\n\\t\\tdisplay: flex;\\n\\t\\tflex-wrap: wrap;\\n\\t\\tgap: var(--checkbox-label-gap);\\n\\t}</style>\\n"],"names":[],"mappings":"AAwEC,oBAAM,CACL,OAAO,CAAE,IAAI,CACb,SAAS,CAAE,IAAI,CACf,GAAG,CAAE,IAAI,oBAAoB,CAC9B"}'},H=create_ssr_component((l,e,t,p)=>{let n,{gradio:A}=e,{label:o=A.i18n("radio.radio")}=e,{info:r=void 0}=e,{elem_id:s=""}=e,{elem_classes:d=[]}=e,{visible:c=!0}=e,{value:a=null}=e,{choices:C=[]}=e,{show_label:i=!0}=e,{container:h=!1}=e,{scale:x=null}=e,{min_width:k=void 0}=e,{loading_status:f}=e,{interactive:g=!0}=e,{rtl:v=!1}=e;function w(){A.dispatch("change");}let m=a;e.gradio===void 0&&t.gradio&&A!==void 0&&t.gradio(A),e.label===void 0&&t.label&&o!==void 0&&t.label(o),e.info===void 0&&t.info&&r!==void 0&&t.info(r),e.elem_id===void 0&&t.elem_id&&s!==void 0&&t.elem_id(s),e.elem_classes===void 0&&t.elem_classes&&d!==void 0&&t.elem_classes(d),e.visible===void 0&&t.visible&&c!==void 0&&t.visible(c),e.value===void 0&&t.value&&a!==void 0&&t.value(a),e.choices===void 0&&t.choices&&C!==void 0&&t.choices(C),e.show_label===void 0&&t.show_label&&i!==void 0&&t.show_label(i),e.container===void 0&&t.container&&h!==void 0&&t.container(h),e.scale===void 0&&t.scale&&x!==void 0&&t.scale(x),e.min_width===void 0&&t.min_width&&k!==void 0&&t.min_width(k),e.loading_status===void 0&&t.loading_status&&f!==void 0&&t.loading_status(f),e.interactive===void 0&&t.interactive&&g!==void 0&&t.interactive(g),e.rtl===void 0&&t.rtl&&v!==void 0&&t.rtl(v),l.css.add(T);let I,_,D=l.head;do I=!0,l.head=D,a!==m&&(m=a,w()),n=!g,_=`${validate_component(mt,"Block").$$render(l,{visible:c,type:"fieldset",elem_id:s,elem_classes:d,container:h,scale:x,min_width:k,rtl:v},{},{default:()=>`${validate_component(zA,"StatusTracker").$$render(l,Object.assign({},{autoscroll:A.autoscroll},{i18n:A.i18n},f),{},{})} ${validate_component(xt,"BlockTitle").$$render(l,{show_label:i,info:r},{},{default:()=>`${escape(o)}`})} <div class="wrap svelte-1kzox3m">${each(C,([y,K],j)=>`${validate_component(R,"BaseRadio").$$render(l,{display_value:y,internal_value:K,disabled:n,rtl:v,selected:a},{selected:Q=>{a=Q,I=!1;}},{})}`)}</div>`})}`;while(!I);return _});

export { R as BaseRadio, H as default };
//# sourceMappingURL=Index52-BfOslKij.js.map
