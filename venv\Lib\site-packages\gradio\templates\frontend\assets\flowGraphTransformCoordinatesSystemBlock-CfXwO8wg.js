import{F as d}from"./KHR_interactivity-DTxiAnOo.js";import{R as e,e as r}from"./declarationMapper-BZjsjg7g.js";import{a as i,V as c,R as h}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class y extends d{constructor(t){super(t),this.sourceSystem=this.registerDataInput("sourceSystem",e),this.destinationSystem=this.registerDataInput("destinationSystem",e),this.inputCoordinates=this.registerDataInput("inputCoordinates",r),this.outputCoordinates=this.registerDataOutput("outputCoordinates",r)}_updateOutputs(t){const a=this.sourceSystem.getValue(t),n=this.destinationSystem.getValue(t),u=this.inputCoordinates.getValue(t),p=a.getWorldMatrix(),m=n.getWorldMatrix(),o=i.Matrix[0].copyFrom(m);o.invert();const s=i.Matrix[1];o.multiplyToRef(p,s);const l=this.outputCoordinates.getValue(t);c.TransformCoordinatesToRef(u,s,l)}getClassName(){return"FlowGraphTransformCoordinatesSystemBlock"}}h("FlowGraphTransformCoordinatesSystemBlock",y);export{y as FlowGraphTransformCoordinatesSystemBlock};
//# sourceMappingURL=flowGraphTransformCoordinatesSystemBlock-CfXwO8wg.js.map
