import{g as Ze,s as $e}from"./chunk-2O5F6CEG-DK8iCJt_.js";import{_ as p,i as et,d as D,Q as M,s as tt,g as st,c as it,b as at,n as nt,o as rt,e as x,t as ut,l as ve,u as Ie,r as lt,y as ct,z as ot}from"./mermaid.core-D-K3Awe9.js";import{s as q}from"./select-BigU4G0v.js";var Oe=function(){var e=p(function(v,l,c,d){for(c=c||{},d=v.length;d--;c[v[d]]=l);return c},"o"),i=[1,18],r=[1,19],u=[1,20],a=[1,41],o=[1,42],A=[1,26],f=[1,24],T=[1,25],_=[1,32],he=[1,33],de=[1,34],C=[1,45],pe=[1,35],Ae=[1,36],fe=[1,37],ge=[1,38],Ce=[1,27],me=[1,28],be=[1,29],Ee=[1,30],ye=[1,31],m=[1,44],b=[1,46],E=[1,43],k=[1,47],Te=[1,9],h=[1,8,9],J=[1,58],Z=[1,59],$=[1,60],ee=[1,61],te=[1,62],ke=[1,63],De=[1,64],se=[1,8,9,41],we=[1,76],V=[1,8,9,12,13,22,39,41,44,66,67,68,69,70,71,72,77,79],ie=[1,8,9,12,13,17,20,22,39,41,44,48,58,66,67,68,69,70,71,72,77,79,84,99,101,102],ae=[13,58,84,99,101,102],R=[13,58,71,72,84,99,101,102],Ve=[13,58,66,67,68,69,70,84,99,101,102],Fe=[1,98],G=[1,115],U=[1,107],z=[1,113],K=[1,108],Y=[1,109],Q=[1,110],W=[1,111],j=[1,112],X=[1,114],Pe=[22,58,59,80,84,85,86,87,88,89],Be=[1,8,9,39,41,44],ne=[1,8,9,22],Me=[1,143],Re=[1,8,9,59],N=[1,8,9,22,58,59,80,84,85,86,87,88,89],_e={trace:p(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mermaidDoc:4,statements:5,graphConfig:6,CLASS_DIAGRAM:7,NEWLINE:8,EOF:9,statement:10,classLabel:11,SQS:12,STR:13,SQE:14,namespaceName:15,alphaNumToken:16,DOT:17,className:18,classLiteralName:19,GENERICTYPE:20,relationStatement:21,LABEL:22,namespaceStatement:23,classStatement:24,memberStatement:25,annotationStatement:26,clickStatement:27,styleStatement:28,cssClassStatement:29,noteStatement:30,classDefStatement:31,direction:32,acc_title:33,acc_title_value:34,acc_descr:35,acc_descr_value:36,acc_descr_multiline_value:37,namespaceIdentifier:38,STRUCT_START:39,classStatements:40,STRUCT_STOP:41,NAMESPACE:42,classIdentifier:43,STYLE_SEPARATOR:44,members:45,CLASS:46,ANNOTATION_START:47,ANNOTATION_END:48,MEMBER:49,SEPARATOR:50,relation:51,NOTE_FOR:52,noteText:53,NOTE:54,CLASSDEF:55,classList:56,stylesOpt:57,ALPHA:58,COMMA:59,direction_tb:60,direction_bt:61,direction_rl:62,direction_lr:63,relationType:64,lineType:65,AGGREGATION:66,EXTENSION:67,COMPOSITION:68,DEPENDENCY:69,LOLLIPOP:70,LINE:71,DOTTED_LINE:72,CALLBACK:73,LINK:74,LINK_TARGET:75,CLICK:76,CALLBACK_NAME:77,CALLBACK_ARGS:78,HREF:79,STYLE:80,CSSCLASS:81,style:82,styleComponent:83,NUM:84,COLON:85,UNIT:86,SPACE:87,BRKT:88,PCT:89,commentToken:90,textToken:91,graphCodeTokens:92,textNoTagsToken:93,TAGSTART:94,TAGEND:95,"==":96,"--":97,DEFAULT:98,MINUS:99,keywords:100,UNICODE_TEXT:101,BQUOTE_STR:102,$accept:0,$end:1},terminals_:{2:"error",7:"CLASS_DIAGRAM",8:"NEWLINE",9:"EOF",12:"SQS",13:"STR",14:"SQE",17:"DOT",20:"GENERICTYPE",22:"LABEL",33:"acc_title",34:"acc_title_value",35:"acc_descr",36:"acc_descr_value",37:"acc_descr_multiline_value",39:"STRUCT_START",41:"STRUCT_STOP",42:"NAMESPACE",44:"STYLE_SEPARATOR",46:"CLASS",47:"ANNOTATION_START",48:"ANNOTATION_END",49:"MEMBER",50:"SEPARATOR",52:"NOTE_FOR",54:"NOTE",55:"CLASSDEF",58:"ALPHA",59:"COMMA",60:"direction_tb",61:"direction_bt",62:"direction_rl",63:"direction_lr",66:"AGGREGATION",67:"EXTENSION",68:"COMPOSITION",69:"DEPENDENCY",70:"LOLLIPOP",71:"LINE",72:"DOTTED_LINE",73:"CALLBACK",74:"LINK",75:"LINK_TARGET",76:"CLICK",77:"CALLBACK_NAME",78:"CALLBACK_ARGS",79:"HREF",80:"STYLE",81:"CSSCLASS",84:"NUM",85:"COLON",86:"UNIT",87:"SPACE",88:"BRKT",89:"PCT",92:"graphCodeTokens",94:"TAGSTART",95:"TAGEND",96:"==",97:"--",98:"DEFAULT",99:"MINUS",100:"keywords",101:"UNICODE_TEXT",102:"BQUOTE_STR"},productions_:[0,[3,1],[3,1],[4,1],[6,4],[5,1],[5,2],[5,3],[11,3],[15,1],[15,3],[15,2],[18,1],[18,3],[18,1],[18,2],[18,2],[18,2],[10,1],[10,2],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,2],[10,2],[10,1],[23,4],[23,5],[38,2],[40,1],[40,2],[40,3],[24,1],[24,3],[24,4],[24,6],[43,2],[43,3],[26,4],[45,1],[45,2],[25,1],[25,2],[25,1],[25,1],[21,3],[21,4],[21,4],[21,5],[30,3],[30,2],[31,3],[56,1],[56,3],[32,1],[32,1],[32,1],[32,1],[51,3],[51,2],[51,2],[51,1],[64,1],[64,1],[64,1],[64,1],[64,1],[65,1],[65,1],[27,3],[27,4],[27,3],[27,4],[27,4],[27,5],[27,3],[27,4],[27,4],[27,5],[27,4],[27,5],[27,5],[27,6],[28,3],[29,3],[57,1],[57,3],[82,1],[82,2],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[90,1],[90,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[93,1],[93,1],[93,1],[93,1],[16,1],[16,1],[16,1],[16,1],[19,1],[53,1]],performAction:p(function(l,c,d,n,g,t,H){var s=t.length-1;switch(g){case 8:this.$=t[s-1];break;case 9:case 12:case 14:this.$=t[s];break;case 10:case 13:this.$=t[s-2]+"."+t[s];break;case 11:case 15:this.$=t[s-1]+t[s];break;case 16:case 17:this.$=t[s-1]+"~"+t[s]+"~";break;case 18:n.addRelation(t[s]);break;case 19:t[s-1].title=n.cleanupLabel(t[s]),n.addRelation(t[s-1]);break;case 30:this.$=t[s].trim(),n.setAccTitle(this.$);break;case 31:case 32:this.$=t[s].trim(),n.setAccDescription(this.$);break;case 33:n.addClassesToNamespace(t[s-3],t[s-1]);break;case 34:n.addClassesToNamespace(t[s-4],t[s-1]);break;case 35:this.$=t[s],n.addNamespace(t[s]);break;case 36:this.$=[t[s]];break;case 37:this.$=[t[s-1]];break;case 38:t[s].unshift(t[s-2]),this.$=t[s];break;case 40:n.setCssClass(t[s-2],t[s]);break;case 41:n.addMembers(t[s-3],t[s-1]);break;case 42:n.setCssClass(t[s-5],t[s-3]),n.addMembers(t[s-5],t[s-1]);break;case 43:this.$=t[s],n.addClass(t[s]);break;case 44:this.$=t[s-1],n.addClass(t[s-1]),n.setClassLabel(t[s-1],t[s]);break;case 45:n.addAnnotation(t[s],t[s-2]);break;case 46:case 59:this.$=[t[s]];break;case 47:t[s].push(t[s-1]),this.$=t[s];break;case 48:break;case 49:n.addMember(t[s-1],n.cleanupLabel(t[s]));break;case 50:break;case 51:break;case 52:this.$={id1:t[s-2],id2:t[s],relation:t[s-1],relationTitle1:"none",relationTitle2:"none"};break;case 53:this.$={id1:t[s-3],id2:t[s],relation:t[s-1],relationTitle1:t[s-2],relationTitle2:"none"};break;case 54:this.$={id1:t[s-3],id2:t[s],relation:t[s-2],relationTitle1:"none",relationTitle2:t[s-1]};break;case 55:this.$={id1:t[s-4],id2:t[s],relation:t[s-2],relationTitle1:t[s-3],relationTitle2:t[s-1]};break;case 56:n.addNote(t[s],t[s-1]);break;case 57:n.addNote(t[s]);break;case 58:this.$=t[s-2],n.defineClass(t[s-1],t[s]);break;case 60:this.$=t[s-2].concat([t[s]]);break;case 61:n.setDirection("TB");break;case 62:n.setDirection("BT");break;case 63:n.setDirection("RL");break;case 64:n.setDirection("LR");break;case 65:this.$={type1:t[s-2],type2:t[s],lineType:t[s-1]};break;case 66:this.$={type1:"none",type2:t[s],lineType:t[s-1]};break;case 67:this.$={type1:t[s-1],type2:"none",lineType:t[s]};break;case 68:this.$={type1:"none",type2:"none",lineType:t[s]};break;case 69:this.$=n.relationType.AGGREGATION;break;case 70:this.$=n.relationType.EXTENSION;break;case 71:this.$=n.relationType.COMPOSITION;break;case 72:this.$=n.relationType.DEPENDENCY;break;case 73:this.$=n.relationType.LOLLIPOP;break;case 74:this.$=n.lineType.LINE;break;case 75:this.$=n.lineType.DOTTED_LINE;break;case 76:case 82:this.$=t[s-2],n.setClickEvent(t[s-1],t[s]);break;case 77:case 83:this.$=t[s-3],n.setClickEvent(t[s-2],t[s-1]),n.setTooltip(t[s-2],t[s]);break;case 78:this.$=t[s-2],n.setLink(t[s-1],t[s]);break;case 79:this.$=t[s-3],n.setLink(t[s-2],t[s-1],t[s]);break;case 80:this.$=t[s-3],n.setLink(t[s-2],t[s-1]),n.setTooltip(t[s-2],t[s]);break;case 81:this.$=t[s-4],n.setLink(t[s-3],t[s-2],t[s]),n.setTooltip(t[s-3],t[s-1]);break;case 84:this.$=t[s-3],n.setClickEvent(t[s-2],t[s-1],t[s]);break;case 85:this.$=t[s-4],n.setClickEvent(t[s-3],t[s-2],t[s-1]),n.setTooltip(t[s-3],t[s]);break;case 86:this.$=t[s-3],n.setLink(t[s-2],t[s]);break;case 87:this.$=t[s-4],n.setLink(t[s-3],t[s-1],t[s]);break;case 88:this.$=t[s-4],n.setLink(t[s-3],t[s-1]),n.setTooltip(t[s-3],t[s]);break;case 89:this.$=t[s-5],n.setLink(t[s-4],t[s-2],t[s]),n.setTooltip(t[s-4],t[s-1]);break;case 90:this.$=t[s-2],n.setCssStyle(t[s-1],t[s]);break;case 91:n.setCssClass(t[s-1],t[s]);break;case 92:this.$=[t[s]];break;case 93:t[s-2].push(t[s]),this.$=t[s-2];break;case 95:this.$=t[s-1]+t[s];break}},"anonymous"),table:[{3:1,4:2,5:3,6:4,7:[1,6],10:5,16:39,18:21,19:40,21:7,23:8,24:9,25:10,26:11,27:12,28:13,29:14,30:15,31:16,32:17,33:i,35:r,37:u,38:22,42:a,43:23,46:o,47:A,49:f,50:T,52:_,54:he,55:de,58:C,60:pe,61:Ae,62:fe,63:ge,73:Ce,74:me,76:be,80:Ee,81:ye,84:m,99:b,101:E,102:k},{1:[3]},{1:[2,1]},{1:[2,2]},{1:[2,3]},e(Te,[2,5],{8:[1,48]}),{8:[1,49]},e(h,[2,18],{22:[1,50]}),e(h,[2,20]),e(h,[2,21]),e(h,[2,22]),e(h,[2,23]),e(h,[2,24]),e(h,[2,25]),e(h,[2,26]),e(h,[2,27]),e(h,[2,28]),e(h,[2,29]),{34:[1,51]},{36:[1,52]},e(h,[2,32]),e(h,[2,48],{51:53,64:56,65:57,13:[1,54],22:[1,55],66:J,67:Z,68:$,69:ee,70:te,71:ke,72:De}),{39:[1,65]},e(se,[2,39],{39:[1,67],44:[1,66]}),e(h,[2,50]),e(h,[2,51]),{16:68,58:C,84:m,99:b,101:E},{16:39,18:69,19:40,58:C,84:m,99:b,101:E,102:k},{16:39,18:70,19:40,58:C,84:m,99:b,101:E,102:k},{16:39,18:71,19:40,58:C,84:m,99:b,101:E,102:k},{58:[1,72]},{13:[1,73]},{16:39,18:74,19:40,58:C,84:m,99:b,101:E,102:k},{13:we,53:75},{56:77,58:[1,78]},e(h,[2,61]),e(h,[2,62]),e(h,[2,63]),e(h,[2,64]),e(V,[2,12],{16:39,19:40,18:80,17:[1,79],20:[1,81],58:C,84:m,99:b,101:E,102:k}),e(V,[2,14],{20:[1,82]}),{15:83,16:84,58:C,84:m,99:b,101:E},{16:39,18:85,19:40,58:C,84:m,99:b,101:E,102:k},e(ie,[2,118]),e(ie,[2,119]),e(ie,[2,120]),e(ie,[2,121]),e([1,8,9,12,13,20,22,39,41,44,66,67,68,69,70,71,72,77,79],[2,122]),e(Te,[2,6],{10:5,21:7,23:8,24:9,25:10,26:11,27:12,28:13,29:14,30:15,31:16,32:17,18:21,38:22,43:23,16:39,19:40,5:86,33:i,35:r,37:u,42:a,46:o,47:A,49:f,50:T,52:_,54:he,55:de,58:C,60:pe,61:Ae,62:fe,63:ge,73:Ce,74:me,76:be,80:Ee,81:ye,84:m,99:b,101:E,102:k}),{5:87,10:5,16:39,18:21,19:40,21:7,23:8,24:9,25:10,26:11,27:12,28:13,29:14,30:15,31:16,32:17,33:i,35:r,37:u,38:22,42:a,43:23,46:o,47:A,49:f,50:T,52:_,54:he,55:de,58:C,60:pe,61:Ae,62:fe,63:ge,73:Ce,74:me,76:be,80:Ee,81:ye,84:m,99:b,101:E,102:k},e(h,[2,19]),e(h,[2,30]),e(h,[2,31]),{13:[1,89],16:39,18:88,19:40,58:C,84:m,99:b,101:E,102:k},{51:90,64:56,65:57,66:J,67:Z,68:$,69:ee,70:te,71:ke,72:De},e(h,[2,49]),{65:91,71:ke,72:De},e(ae,[2,68],{64:92,66:J,67:Z,68:$,69:ee,70:te}),e(R,[2,69]),e(R,[2,70]),e(R,[2,71]),e(R,[2,72]),e(R,[2,73]),e(Ve,[2,74]),e(Ve,[2,75]),{8:[1,94],24:95,40:93,43:23,46:o},{16:96,58:C,84:m,99:b,101:E},{45:97,49:Fe},{48:[1,99]},{13:[1,100]},{13:[1,101]},{77:[1,102],79:[1,103]},{22:G,57:104,58:U,80:z,82:105,83:106,84:K,85:Y,86:Q,87:W,88:j,89:X},{58:[1,116]},{13:we,53:117},e(h,[2,57]),e(h,[2,123]),{22:G,57:118,58:U,59:[1,119],80:z,82:105,83:106,84:K,85:Y,86:Q,87:W,88:j,89:X},e(Pe,[2,59]),{16:39,18:120,19:40,58:C,84:m,99:b,101:E,102:k},e(V,[2,15]),e(V,[2,16]),e(V,[2,17]),{39:[2,35]},{15:122,16:84,17:[1,121],39:[2,9],58:C,84:m,99:b,101:E},e(Be,[2,43],{11:123,12:[1,124]}),e(Te,[2,7]),{9:[1,125]},e(ne,[2,52]),{16:39,18:126,19:40,58:C,84:m,99:b,101:E,102:k},{13:[1,128],16:39,18:127,19:40,58:C,84:m,99:b,101:E,102:k},e(ae,[2,67],{64:129,66:J,67:Z,68:$,69:ee,70:te}),e(ae,[2,66]),{41:[1,130]},{24:95,40:131,43:23,46:o},{8:[1,132],41:[2,36]},e(se,[2,40],{39:[1,133]}),{41:[1,134]},{41:[2,46],45:135,49:Fe},{16:39,18:136,19:40,58:C,84:m,99:b,101:E,102:k},e(h,[2,76],{13:[1,137]}),e(h,[2,78],{13:[1,139],75:[1,138]}),e(h,[2,82],{13:[1,140],78:[1,141]}),{13:[1,142]},e(h,[2,90],{59:Me}),e(Re,[2,92],{83:144,22:G,58:U,80:z,84:K,85:Y,86:Q,87:W,88:j,89:X}),e(N,[2,94]),e(N,[2,96]),e(N,[2,97]),e(N,[2,98]),e(N,[2,99]),e(N,[2,100]),e(N,[2,101]),e(N,[2,102]),e(N,[2,103]),e(N,[2,104]),e(h,[2,91]),e(h,[2,56]),e(h,[2,58],{59:Me}),{58:[1,145]},e(V,[2,13]),{15:146,16:84,58:C,84:m,99:b,101:E},{39:[2,11]},e(Be,[2,44]),{13:[1,147]},{1:[2,4]},e(ne,[2,54]),e(ne,[2,53]),{16:39,18:148,19:40,58:C,84:m,99:b,101:E,102:k},e(ae,[2,65]),e(h,[2,33]),{41:[1,149]},{24:95,40:150,41:[2,37],43:23,46:o},{45:151,49:Fe},e(se,[2,41]),{41:[2,47]},e(h,[2,45]),e(h,[2,77]),e(h,[2,79]),e(h,[2,80],{75:[1,152]}),e(h,[2,83]),e(h,[2,84],{13:[1,153]}),e(h,[2,86],{13:[1,155],75:[1,154]}),{22:G,58:U,80:z,82:156,83:106,84:K,85:Y,86:Q,87:W,88:j,89:X},e(N,[2,95]),e(Pe,[2,60]),{39:[2,10]},{14:[1,157]},e(ne,[2,55]),e(h,[2,34]),{41:[2,38]},{41:[1,158]},e(h,[2,81]),e(h,[2,85]),e(h,[2,87]),e(h,[2,88],{75:[1,159]}),e(Re,[2,93],{83:144,22:G,58:U,80:z,84:K,85:Y,86:Q,87:W,88:j,89:X}),e(Be,[2,8]),e(se,[2,42]),e(h,[2,89])],defaultActions:{2:[2,1],3:[2,2],4:[2,3],83:[2,35],122:[2,11],125:[2,4],135:[2,47],146:[2,10],150:[2,38]},parseError:p(function(l,c){if(c.recoverable)this.trace(l);else{var d=new Error(l);throw d.hash=c,d}},"parseError"),parse:p(function(l){var c=this,d=[0],n=[],g=[null],t=[],H=this.table,s="",ue=0,Ge=0,Xe=2,Ue=1,He=t.slice.call(arguments,1),y=Object.create(this.lexer),I={yy:{}};for(var Se in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Se)&&(I.yy[Se]=this.yy[Se]);y.setInput(l,I.yy),I.yy.lexer=y,I.yy.parser=this,typeof y.yylloc>"u"&&(y.yylloc={});var Ne=y.yylloc;t.push(Ne);var qe=y.options&&y.options.ranges;typeof I.yy.parseError=="function"?this.parseError=I.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Je(B){d.length=d.length-2*B,g.length=g.length-B,t.length=t.length-B}p(Je,"popStack");function ze(){var B;return B=n.pop()||y.lex()||Ue,typeof B!="number"&&(B instanceof Array&&(n=B,B=n.pop()),B=c.symbols_[B]||B),B}p(ze,"lex");for(var F,O,S,Le,P={},le,L,Ke,ce;;){if(O=d[d.length-1],this.defaultActions[O]?S=this.defaultActions[O]:((F===null||typeof F>"u")&&(F=ze()),S=H[O]&&H[O][F]),typeof S>"u"||!S.length||!S[0]){var xe="";ce=[];for(le in H[O])this.terminals_[le]&&le>Xe&&ce.push("'"+this.terminals_[le]+"'");y.showPosition?xe="Parse error on line "+(ue+1)+`:
`+y.showPosition()+`
Expecting `+ce.join(", ")+", got '"+(this.terminals_[F]||F)+"'":xe="Parse error on line "+(ue+1)+": Unexpected "+(F==Ue?"end of input":"'"+(this.terminals_[F]||F)+"'"),this.parseError(xe,{text:y.match,token:this.terminals_[F]||F,line:y.yylineno,loc:Ne,expected:ce})}if(S[0]instanceof Array&&S.length>1)throw new Error("Parse Error: multiple actions possible at state: "+O+", token: "+F);switch(S[0]){case 1:d.push(F),g.push(y.yytext),t.push(y.yylloc),d.push(S[1]),F=null,Ge=y.yyleng,s=y.yytext,ue=y.yylineno,Ne=y.yylloc;break;case 2:if(L=this.productions_[S[1]][1],P.$=g[g.length-L],P._$={first_line:t[t.length-(L||1)].first_line,last_line:t[t.length-1].last_line,first_column:t[t.length-(L||1)].first_column,last_column:t[t.length-1].last_column},qe&&(P._$.range=[t[t.length-(L||1)].range[0],t[t.length-1].range[1]]),Le=this.performAction.apply(P,[s,Ge,ue,I.yy,S[1],g,t].concat(He)),typeof Le<"u")return Le;L&&(d=d.slice(0,-1*L*2),g=g.slice(0,-1*L),t=t.slice(0,-1*L)),d.push(this.productions_[S[1]][0]),g.push(P.$),t.push(P._$),Ke=H[d[d.length-2]][d[d.length-1]],d.push(Ke);break;case 3:return!0}}return!0},"parse")},je=function(){var v={EOF:1,parseError:p(function(c,d){if(this.yy.parser)this.yy.parser.parseError(c,d);else throw new Error(c)},"parseError"),setInput:p(function(l,c){return this.yy=c||this.yy||{},this._input=l,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:p(function(){var l=this._input[0];this.yytext+=l,this.yyleng++,this.offset++,this.match+=l,this.matched+=l;var c=l.match(/(?:\r\n?|\n).*/g);return c?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),l},"input"),unput:p(function(l){var c=l.length,d=l.split(/(?:\r\n?|\n)/g);this._input=l+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-c),this.offset-=c;var n=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),d.length-1&&(this.yylineno-=d.length-1);var g=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:d?(d.length===n.length?this.yylloc.first_column:0)+n[n.length-d.length].length-d[0].length:this.yylloc.first_column-c},this.options.ranges&&(this.yylloc.range=[g[0],g[0]+this.yyleng-c]),this.yyleng=this.yytext.length,this},"unput"),more:p(function(){return this._more=!0,this},"more"),reject:p(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:p(function(l){this.unput(this.match.slice(l))},"less"),pastInput:p(function(){var l=this.matched.substr(0,this.matched.length-this.match.length);return(l.length>20?"...":"")+l.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:p(function(){var l=this.match;return l.length<20&&(l+=this._input.substr(0,20-l.length)),(l.substr(0,20)+(l.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:p(function(){var l=this.pastInput(),c=new Array(l.length+1).join("-");return l+this.upcomingInput()+`
`+c+"^"},"showPosition"),test_match:p(function(l,c){var d,n,g;if(this.options.backtrack_lexer&&(g={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(g.yylloc.range=this.yylloc.range.slice(0))),n=l[0].match(/(?:\r\n?|\n).*/g),n&&(this.yylineno+=n.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:n?n[n.length-1].length-n[n.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+l[0].length},this.yytext+=l[0],this.match+=l[0],this.matches=l,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(l[0].length),this.matched+=l[0],d=this.performAction.call(this,this.yy,this,c,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),d)return d;if(this._backtrack){for(var t in g)this[t]=g[t];return!1}return!1},"test_match"),next:p(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var l,c,d,n;this._more||(this.yytext="",this.match="");for(var g=this._currentRules(),t=0;t<g.length;t++)if(d=this._input.match(this.rules[g[t]]),d&&(!c||d[0].length>c[0].length)){if(c=d,n=t,this.options.backtrack_lexer){if(l=this.test_match(d,g[t]),l!==!1)return l;if(this._backtrack){c=!1;continue}else return!1}else if(!this.options.flex)break}return c?(l=this.test_match(c,g[n]),l!==!1?l:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:p(function(){var c=this.next();return c||this.lex()},"lex"),begin:p(function(c){this.conditionStack.push(c)},"begin"),popState:p(function(){var c=this.conditionStack.length-1;return c>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:p(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:p(function(c){return c=this.conditionStack.length-1-Math.abs(c||0),c>=0?this.conditionStack[c]:"INITIAL"},"topState"),pushState:p(function(c){this.begin(c)},"pushState"),stateStackSize:p(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:p(function(c,d,n,g){switch(n){case 0:return 60;case 1:return 61;case 2:return 62;case 3:return 63;case 4:break;case 5:break;case 6:return this.begin("acc_title"),33;case 7:return this.popState(),"acc_title_value";case 8:return this.begin("acc_descr"),35;case 9:return this.popState(),"acc_descr_value";case 10:this.begin("acc_descr_multiline");break;case 11:this.popState();break;case 12:return"acc_descr_multiline_value";case 13:return 8;case 14:break;case 15:return 7;case 16:return 7;case 17:return"EDGE_STATE";case 18:this.begin("callback_name");break;case 19:this.popState();break;case 20:this.popState(),this.begin("callback_args");break;case 21:return 77;case 22:this.popState();break;case 23:return 78;case 24:this.popState();break;case 25:return"STR";case 26:this.begin("string");break;case 27:return 80;case 28:return 55;case 29:return this.begin("namespace"),42;case 30:return this.popState(),8;case 31:break;case 32:return this.begin("namespace-body"),39;case 33:return this.popState(),41;case 34:return"EOF_IN_STRUCT";case 35:return 8;case 36:break;case 37:return"EDGE_STATE";case 38:return this.begin("class"),46;case 39:return this.popState(),8;case 40:break;case 41:return this.popState(),this.popState(),41;case 42:return this.begin("class-body"),39;case 43:return this.popState(),41;case 44:return"EOF_IN_STRUCT";case 45:return"EDGE_STATE";case 46:return"OPEN_IN_STRUCT";case 47:break;case 48:return"MEMBER";case 49:return 81;case 50:return 73;case 51:return 74;case 52:return 76;case 53:return 52;case 54:return 54;case 55:return 47;case 56:return 48;case 57:return 79;case 58:this.popState();break;case 59:return"GENERICTYPE";case 60:this.begin("generic");break;case 61:this.popState();break;case 62:return"BQUOTE_STR";case 63:this.begin("bqstring");break;case 64:return 75;case 65:return 75;case 66:return 75;case 67:return 75;case 68:return 67;case 69:return 67;case 70:return 69;case 71:return 69;case 72:return 68;case 73:return 66;case 74:return 70;case 75:return 71;case 76:return 72;case 77:return 22;case 78:return 44;case 79:return 99;case 80:return 17;case 81:return"PLUS";case 82:return 85;case 83:return 59;case 84:return 88;case 85:return 88;case 86:return 89;case 87:return"EQUALS";case 88:return"EQUALS";case 89:return 58;case 90:return 12;case 91:return 14;case 92:return"PUNCTUATION";case 93:return 84;case 94:return 101;case 95:return 87;case 96:return 87;case 97:return 9}},"anonymous"),rules:[/^(?:.*direction\s+TB[^\n]*)/,/^(?:.*direction\s+BT[^\n]*)/,/^(?:.*direction\s+RL[^\n]*)/,/^(?:.*direction\s+LR[^\n]*)/,/^(?:%%(?!\{)*[^\n]*(\r?\n?)+)/,/^(?:%%[^\n]*(\r?\n)*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:classDiagram-v2\b)/,/^(?:classDiagram\b)/,/^(?:\[\*\])/,/^(?:call[\s]+)/,/^(?:\([\s]*\))/,/^(?:\()/,/^(?:[^(]*)/,/^(?:\))/,/^(?:[^)]*)/,/^(?:["])/,/^(?:[^"]*)/,/^(?:["])/,/^(?:style\b)/,/^(?:classDef\b)/,/^(?:namespace\b)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:[{])/,/^(?:[}])/,/^(?:$)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:\[\*\])/,/^(?:class\b)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:[}])/,/^(?:[{])/,/^(?:[}])/,/^(?:$)/,/^(?:\[\*\])/,/^(?:[{])/,/^(?:[\n])/,/^(?:[^{}\n]*)/,/^(?:cssClass\b)/,/^(?:callback\b)/,/^(?:link\b)/,/^(?:click\b)/,/^(?:note for\b)/,/^(?:note\b)/,/^(?:<<)/,/^(?:>>)/,/^(?:href\b)/,/^(?:[~])/,/^(?:[^~]*)/,/^(?:~)/,/^(?:[`])/,/^(?:[^`]+)/,/^(?:[`])/,/^(?:_self\b)/,/^(?:_blank\b)/,/^(?:_parent\b)/,/^(?:_top\b)/,/^(?:\s*<\|)/,/^(?:\s*\|>)/,/^(?:\s*>)/,/^(?:\s*<)/,/^(?:\s*\*)/,/^(?:\s*o\b)/,/^(?:\s*\(\))/,/^(?:--)/,/^(?:\.\.)/,/^(?::{1}[^:\n;]+)/,/^(?::{3})/,/^(?:-)/,/^(?:\.)/,/^(?:\+)/,/^(?::)/,/^(?:,)/,/^(?:#)/,/^(?:#)/,/^(?:%)/,/^(?:=)/,/^(?:=)/,/^(?:\w+)/,/^(?:\[)/,/^(?:\])/,/^(?:[!"#$%&'*+,-.`?\\/])/,/^(?:[0-9]+)/,/^(?:[\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6]|[\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377]|[\u037A-\u037D\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5]|[\u03F7-\u0481\u048A-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA]|[\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE]|[\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA]|[\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0]|[\u08A2-\u08AC\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0977]|[\u0979-\u097F\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2]|[\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A]|[\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39]|[\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8]|[\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0B05-\u0B0C]|[\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C]|[\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99]|[\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0]|[\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C33\u0C35-\u0C39\u0C3D]|[\u0C58\u0C59\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3]|[\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10]|[\u0D12-\u0D3A\u0D3D\u0D4E\u0D60\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1]|[\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81]|[\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3]|[\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6]|[\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A]|[\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081]|[\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D]|[\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0]|[\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310]|[\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F4\u1401-\u166C]|[\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u1700-\u170C\u170E-\u1711]|[\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7]|[\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191C]|[\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19C1-\u19C7\u1A00-\u1A16]|[\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF]|[\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC]|[\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D]|[\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D]|[\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3]|[\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F]|[\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128]|[\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184]|[\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3]|[\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6]|[\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE]|[\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C]|[\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D]|[\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FCC]|[\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B]|[\uA640-\uA66E\uA67F-\uA697\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788]|[\uA78B-\uA78E\uA790-\uA793\uA7A0-\uA7AA\uA7F8-\uA801\uA803-\uA805]|[\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB]|[\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uAA00-\uAA28]|[\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA80-\uAAAF\uAAB1\uAAB5]|[\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4]|[\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E]|[\uABC0-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D]|[\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36]|[\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D]|[\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC]|[\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF]|[\uFFD2-\uFFD7\uFFDA-\uFFDC])/,/^(?:\s)/,/^(?:\s)/,/^(?:$)/],conditions:{"namespace-body":{rules:[26,33,34,35,36,37,38,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},namespace:{rules:[26,29,30,31,32,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},"class-body":{rules:[26,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},class:{rules:[26,39,40,41,42,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},acc_descr_multiline:{rules:[11,12,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},acc_descr:{rules:[9,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},acc_title:{rules:[7,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},callback_args:{rules:[22,23,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},callback_name:{rules:[19,20,21,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},href:{rules:[26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},struct:{rules:[26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},generic:{rules:[26,49,50,51,52,53,54,55,56,57,58,59,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},bqstring:{rules:[26,49,50,51,52,53,54,55,56,57,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},string:{rules:[24,25,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,8,10,13,14,15,16,17,18,26,27,28,29,38,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97],inclusive:!0}}};return v}();_e.lexer=je;function re(){this.yy={}}return p(re,"Parser"),re.prototype=_e,_e.Parser=re,new re}();Oe.parser=Oe;var mt=Oe,Ye=["#","+","~","-",""],Qe=class{static{p(this,"ClassMember")}constructor(e,i){this.memberType=i,this.visibility="",this.classifier="",this.text="";const r=et(e,D());this.parseMember(r)}getDisplayDetails(){let e=this.visibility+M(this.id);this.memberType==="method"&&(e+=`(${M(this.parameters.trim())})`,this.returnType&&(e+=" : "+M(this.returnType))),e=e.trim();const i=this.parseClassifier();return{displayText:e,cssStyle:i}}parseMember(e){let i="";if(this.memberType==="method"){const a=/([#+~-])?(.+)\((.*)\)([\s$*])?(.*)([$*])?/.exec(e);if(a){const o=a[1]?a[1].trim():"";if(Ye.includes(o)&&(this.visibility=o),this.id=a[2],this.parameters=a[3]?a[3].trim():"",i=a[4]?a[4].trim():"",this.returnType=a[5]?a[5].trim():"",i===""){const A=this.returnType.substring(this.returnType.length-1);/[$*]/.exec(A)&&(i=A,this.returnType=this.returnType.substring(0,this.returnType.length-1))}}}else{const u=e.length,a=e.substring(0,1),o=e.substring(u-1);Ye.includes(a)&&(this.visibility=a),/[$*]/.exec(o)&&(i=o),this.id=e.substring(this.visibility===""?0:1,i===""?u:u-1)}this.classifier=i,this.id=this.id.startsWith(" ")?" "+this.id.trim():this.id.trim();const r=`${this.visibility?"\\"+this.visibility:""}${M(this.id)}${this.memberType==="method"?`(${M(this.parameters)})${this.returnType?" : "+M(this.returnType):""}`:""}`;this.text=r.replaceAll("<","&lt;").replaceAll(">","&gt;"),this.text.startsWith("\\&lt;")&&(this.text=this.text.replace("\\&lt;","~"))}parseClassifier(){switch(this.classifier){case"*":return"font-style:italic;";case"$":return"text-decoration:underline;";default:return""}}},oe="classId-",We=0,w=p(e=>x.sanitizeText(e,D()),"sanitizeText"),bt=class{constructor(){this.relations=[],this.classes=new Map,this.styleClasses=new Map,this.notes=[],this.interfaces=[],this.namespaces=new Map,this.namespaceCounter=0,this.functions=[],this.lineType={LINE:0,DOTTED_LINE:1},this.relationType={AGGREGATION:0,EXTENSION:1,COMPOSITION:2,DEPENDENCY:3,LOLLIPOP:4},this.setupToolTips=p(e=>{let i=q(".mermaidTooltip");(i._groups||i)[0][0]===null&&(i=q("body").append("div").attr("class","mermaidTooltip").style("opacity",0)),q(e).select("svg").selectAll("g.node").on("mouseover",a=>{const o=q(a.currentTarget);if(o.attr("title")===null)return;const f=this.getBoundingClientRect();i.transition().duration(200).style("opacity",".9"),i.text(o.attr("title")).style("left",window.scrollX+f.left+(f.right-f.left)/2+"px").style("top",window.scrollY+f.top-14+document.body.scrollTop+"px"),i.html(i.html().replace(/&lt;br\/&gt;/g,"<br/>")),o.classed("hover",!0)}).on("mouseout",a=>{i.transition().duration(500).style("opacity",0),q(a.currentTarget).classed("hover",!1)})},"setupToolTips"),this.direction="TB",this.setAccTitle=tt,this.getAccTitle=st,this.setAccDescription=it,this.getAccDescription=at,this.setDiagramTitle=nt,this.getDiagramTitle=rt,this.getConfig=p(()=>D().class,"getConfig"),this.functions.push(this.setupToolTips.bind(this)),this.clear(),this.addRelation=this.addRelation.bind(this),this.addClassesToNamespace=this.addClassesToNamespace.bind(this),this.addNamespace=this.addNamespace.bind(this),this.setCssClass=this.setCssClass.bind(this),this.addMembers=this.addMembers.bind(this),this.addClass=this.addClass.bind(this),this.setClassLabel=this.setClassLabel.bind(this),this.addAnnotation=this.addAnnotation.bind(this),this.addMember=this.addMember.bind(this),this.cleanupLabel=this.cleanupLabel.bind(this),this.addNote=this.addNote.bind(this),this.defineClass=this.defineClass.bind(this),this.setDirection=this.setDirection.bind(this),this.setLink=this.setLink.bind(this),this.bindFunctions=this.bindFunctions.bind(this),this.clear=this.clear.bind(this),this.setTooltip=this.setTooltip.bind(this),this.setClickEvent=this.setClickEvent.bind(this),this.setCssStyle=this.setCssStyle.bind(this)}static{p(this,"ClassDB")}splitClassNameAndType(e){const i=x.sanitizeText(e,D());let r="",u=i;if(i.indexOf("~")>0){const a=i.split("~");u=w(a[0]),r=w(a[1])}return{className:u,type:r}}setClassLabel(e,i){const r=x.sanitizeText(e,D());i&&(i=w(i));const{className:u}=this.splitClassNameAndType(r);this.classes.get(u).label=i,this.classes.get(u).text=`${i}${this.classes.get(u).type?`<${this.classes.get(u).type}>`:""}`}addClass(e){const i=x.sanitizeText(e,D()),{className:r,type:u}=this.splitClassNameAndType(i);if(this.classes.has(r))return;const a=x.sanitizeText(r,D());this.classes.set(a,{id:a,type:u,label:a,text:`${a}${u?`&lt;${u}&gt;`:""}`,shape:"classBox",cssClasses:"default",methods:[],members:[],annotations:[],styles:[],domId:oe+a+"-"+We}),We++}addInterface(e,i){const r={id:`interface${this.interfaces.length}`,label:e,classId:i};this.interfaces.push(r)}lookUpDomId(e){const i=x.sanitizeText(e,D());if(this.classes.has(i))return this.classes.get(i).domId;throw new Error("Class not found: "+i)}clear(){this.relations=[],this.classes=new Map,this.notes=[],this.interfaces=[],this.functions=[],this.functions.push(this.setupToolTips.bind(this)),this.namespaces=new Map,this.namespaceCounter=0,this.direction="TB",ut()}getClass(e){return this.classes.get(e)}getClasses(){return this.classes}getRelations(){return this.relations}getNotes(){return this.notes}addRelation(e){ve.debug("Adding relation: "+JSON.stringify(e));const i=[this.relationType.LOLLIPOP,this.relationType.AGGREGATION,this.relationType.COMPOSITION,this.relationType.DEPENDENCY,this.relationType.EXTENSION];e.relation.type1===this.relationType.LOLLIPOP&&!i.includes(e.relation.type2)?(this.addClass(e.id2),this.addInterface(e.id1,e.id2),e.id1=`interface${this.interfaces.length-1}`):e.relation.type2===this.relationType.LOLLIPOP&&!i.includes(e.relation.type1)?(this.addClass(e.id1),this.addInterface(e.id2,e.id1),e.id2=`interface${this.interfaces.length-1}`):(this.addClass(e.id1),this.addClass(e.id2)),e.id1=this.splitClassNameAndType(e.id1).className,e.id2=this.splitClassNameAndType(e.id2).className,e.relationTitle1=x.sanitizeText(e.relationTitle1.trim(),D()),e.relationTitle2=x.sanitizeText(e.relationTitle2.trim(),D()),this.relations.push(e)}addAnnotation(e,i){const r=this.splitClassNameAndType(e).className;this.classes.get(r).annotations.push(i)}addMember(e,i){this.addClass(e);const r=this.splitClassNameAndType(e).className,u=this.classes.get(r);if(typeof i=="string"){const a=i.trim();a.startsWith("<<")&&a.endsWith(">>")?u.annotations.push(w(a.substring(2,a.length-2))):a.indexOf(")")>0?u.methods.push(new Qe(a,"method")):a&&u.members.push(new Qe(a,"attribute"))}}addMembers(e,i){Array.isArray(i)&&(i.reverse(),i.forEach(r=>this.addMember(e,r)))}addNote(e,i){const r={id:`note${this.notes.length}`,class:i,text:e};this.notes.push(r)}cleanupLabel(e){return e.startsWith(":")&&(e=e.substring(1)),w(e.trim())}setCssClass(e,i){e.split(",").forEach(r=>{let u=r;/\d/.exec(r[0])&&(u=oe+u);const a=this.classes.get(u);a&&(a.cssClasses+=" "+i)})}defineClass(e,i){for(const r of e){let u=this.styleClasses.get(r);u===void 0&&(u={id:r,styles:[],textStyles:[]},this.styleClasses.set(r,u)),i&&i.forEach(a=>{if(/color/.exec(a)){const o=a.replace("fill","bgFill");u.textStyles.push(o)}u.styles.push(a)}),this.classes.forEach(a=>{a.cssClasses.includes(r)&&a.styles.push(...i.flatMap(o=>o.split(",")))})}}setTooltip(e,i){e.split(",").forEach(r=>{i!==void 0&&(this.classes.get(r).tooltip=w(i))})}getTooltip(e,i){return i&&this.namespaces.has(i)?this.namespaces.get(i).classes.get(e).tooltip:this.classes.get(e).tooltip}setLink(e,i,r){const u=D();e.split(",").forEach(a=>{let o=a;/\d/.exec(a[0])&&(o=oe+o);const A=this.classes.get(o);A&&(A.link=Ie.formatUrl(i,u),u.securityLevel==="sandbox"?A.linkTarget="_top":typeof r=="string"?A.linkTarget=w(r):A.linkTarget="_blank")}),this.setCssClass(e,"clickable")}setClickEvent(e,i,r){e.split(",").forEach(u=>{this.setClickFunc(u,i,r),this.classes.get(u).haveCallback=!0}),this.setCssClass(e,"clickable")}setClickFunc(e,i,r){const u=x.sanitizeText(e,D());if(D().securityLevel!=="loose"||i===void 0)return;const o=u;if(this.classes.has(o)){const A=this.lookUpDomId(o);let f=[];if(typeof r=="string"){f=r.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let T=0;T<f.length;T++){let _=f[T].trim();_.startsWith('"')&&_.endsWith('"')&&(_=_.substr(1,_.length-2)),f[T]=_}}f.length===0&&f.push(A),this.functions.push(()=>{const T=document.querySelector(`[id="${A}"]`);T!==null&&T.addEventListener("click",()=>{Ie.runFunc(i,...f)},!1)})}}bindFunctions(e){this.functions.forEach(i=>{i(e)})}getDirection(){return this.direction}setDirection(e){this.direction=e}addNamespace(e){this.namespaces.has(e)||(this.namespaces.set(e,{id:e,classes:new Map,children:{},domId:oe+e+"-"+this.namespaceCounter}),this.namespaceCounter++)}getNamespace(e){return this.namespaces.get(e)}getNamespaces(){return this.namespaces}addClassesToNamespace(e,i){if(this.namespaces.has(e))for(const r of i){const{className:u}=this.splitClassNameAndType(r);this.classes.get(u).parent=e,this.namespaces.get(e).classes.set(u,this.classes.get(u))}}setCssStyle(e,i){const r=this.classes.get(e);if(!(!i||!r))for(const u of i)u.includes(",")?r.styles.push(...u.split(",")):r.styles.push(u)}getArrowMarker(e){let i;switch(e){case 0:i="aggregation";break;case 1:i="extension";break;case 2:i="composition";break;case 3:i="dependency";break;case 4:i="lollipop";break;default:i="none"}return i}getData(){const e=[],i=[],r=D();for(const a of this.namespaces.keys()){const o=this.namespaces.get(a);if(o){const A={id:o.id,label:o.id,isGroup:!0,padding:r.class.padding??16,shape:"rect",cssStyles:["fill: none","stroke: black"],look:r.look};e.push(A)}}for(const a of this.classes.keys()){const o=this.classes.get(a);if(o){const A=o;A.parentId=o.parent,A.look=r.look,e.push(A)}}let u=0;for(const a of this.notes){u++;const o={id:a.id,label:a.text,isGroup:!1,shape:"note",padding:r.class.padding??6,cssStyles:["text-align: left","white-space: nowrap",`fill: ${r.themeVariables.noteBkgColor}`,`stroke: ${r.themeVariables.noteBorderColor}`],look:r.look};e.push(o);const A=this.classes.get(a.class)?.id??"";if(A){const f={id:`edgeNote${u}`,start:a.id,end:A,type:"normal",thickness:"normal",classes:"relation",arrowTypeStart:"none",arrowTypeEnd:"none",arrowheadStyle:"",labelStyle:[""],style:["fill: none"],pattern:"dotted",look:r.look};i.push(f)}}for(const a of this.interfaces){const o={id:a.id,label:a.label,isGroup:!1,shape:"rect",cssStyles:["opacity: 0;"],look:r.look};e.push(o)}u=0;for(const a of this.relations){u++;const o={id:lt(a.id1,a.id2,{prefix:"id",counter:u}),start:a.id1,end:a.id2,type:"normal",label:a.title,labelpos:"c",thickness:"normal",classes:"relation",arrowTypeStart:this.getArrowMarker(a.relation.type1),arrowTypeEnd:this.getArrowMarker(a.relation.type2),startLabelRight:a.relationTitle1==="none"?"":a.relationTitle1,endLabelLeft:a.relationTitle2==="none"?"":a.relationTitle2,arrowheadStyle:"",labelStyle:["display: inline-block"],style:a.style||"",pattern:a.relation.lineType==1?"dashed":"solid",look:r.look};i.push(o)}return{nodes:e,edges:i,other:{},config:r,direction:this.getDirection()}}},ht=p(e=>`g.classGroup text {
  fill: ${e.nodeBorder||e.classText};
  stroke: none;
  font-family: ${e.fontFamily};
  font-size: 10px;

  .title {
    font-weight: bolder;
  }

}

.nodeLabel, .edgeLabel {
  color: ${e.classText};
}
.edgeLabel .label rect {
  fill: ${e.mainBkg};
}
.label text {
  fill: ${e.classText};
}

.labelBkg {
  background: ${e.mainBkg};
}
.edgeLabel .label span {
  background: ${e.mainBkg};
}

.classTitle {
  font-weight: bolder;
}
.node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${e.mainBkg};
    stroke: ${e.nodeBorder};
    stroke-width: 1px;
  }


.divider {
  stroke: ${e.nodeBorder};
  stroke-width: 1;
}

g.clickable {
  cursor: pointer;
}

g.classGroup rect {
  fill: ${e.mainBkg};
  stroke: ${e.nodeBorder};
}

g.classGroup line {
  stroke: ${e.nodeBorder};
  stroke-width: 1;
}

.classLabel .box {
  stroke: none;
  stroke-width: 0;
  fill: ${e.mainBkg};
  opacity: 0.5;
}

.classLabel .label {
  fill: ${e.nodeBorder};
  font-size: 10px;
}

.relation {
  stroke: ${e.lineColor};
  stroke-width: 1;
  fill: none;
}

.dashed-line{
  stroke-dasharray: 3;
}

.dotted-line{
  stroke-dasharray: 1 2;
}

#compositionStart, .composition {
  fill: ${e.lineColor} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#compositionEnd, .composition {
  fill: ${e.lineColor} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#dependencyStart, .dependency {
  fill: ${e.lineColor} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#dependencyStart, .dependency {
  fill: ${e.lineColor} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#extensionStart, .extension {
  fill: transparent !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#extensionEnd, .extension {
  fill: transparent !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#aggregationStart, .aggregation {
  fill: transparent !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#aggregationEnd, .aggregation {
  fill: transparent !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#lollipopStart, .lollipop {
  fill: ${e.mainBkg} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#lollipopEnd, .lollipop {
  fill: ${e.mainBkg} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

.edgeTerminals {
  font-size: 11px;
  line-height: initial;
}

.classTitleText {
  text-anchor: middle;
  font-size: 18px;
  fill: ${e.textColor};
}
`,"getStyles"),Et=ht,dt=p((e,i="TB")=>{if(!e.doc)return i;let r=i;for(const u of e.doc)u.stmt==="dir"&&(r=u.value);return r},"getDir"),pt=p(function(e,i){return i.db.getClasses()},"getClasses"),At=p(async function(e,i,r,u){ve.info("REF0:"),ve.info("Drawing class diagram (v3)",i);const{securityLevel:a,state:o,layout:A}=D(),f=u.db.getData(),T=Ze(i,a);f.type=u.type,f.layoutAlgorithm=ct(A),f.nodeSpacing=o?.nodeSpacing||50,f.rankSpacing=o?.rankSpacing||50,f.markers=["aggregation","extension","composition","dependency","lollipop"],f.diagramId=i,await ot(f,T);const _=8;Ie.insertTitle(T,"classDiagramTitleText",o?.titleTopMargin??25,u.db.getDiagramTitle()),$e(T,_,"classDiagram",o?.useMaxWidth??!0)},"draw"),yt={getClasses:pt,draw:At,getDir:dt};export{bt as C,yt as a,mt as c,Et as s};
//# sourceMappingURL=chunk-5V4FS25O-BR8Rz7ud.js.map
