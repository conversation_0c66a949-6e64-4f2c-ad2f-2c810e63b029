{"version": 3, "file": "clone.CpN9xanj.js", "sources": ["../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/clone.js"], "sourcesContent": ["import baseClone from './_baseClone.js';\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_SYMBOLS_FLAG = 4;\n\n/**\n * Creates a shallow clone of `value`.\n *\n * **Note:** This method is loosely based on the\n * [structured clone algorithm](https://mdn.io/Structured_clone_algorithm)\n * and supports cloning arrays, array buffers, booleans, date objects, maps,\n * numbers, `Object` objects, regexes, sets, strings, symbols, and typed\n * arrays. The own enumerable properties of `arguments` objects are cloned\n * as plain objects. An empty object is returned for uncloneable values such\n * as error objects, functions, DOM nodes, and WeakMaps.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to clone.\n * @returns {*} Returns the cloned value.\n * @see _.cloneDeep\n * @example\n *\n * var objects = [{ 'a': 1 }, { 'b': 2 }];\n *\n * var shallow = _.clone(objects);\n * console.log(shallow[0] === objects[0]);\n * // => true\n */\nfunction clone(value) {\n  return baseClone(value, CLONE_SYMBOLS_FLAG);\n}\n\nexport default clone;\n"], "names": ["CLONE_SYMBOLS_FLAG", "clone", "value", "baseClone"], "mappings": "4CAGA,IAAIA,EAAqB,EA4BzB,SAASC,EAAMC,EAAO,CACpB,OAAOC,EAAUD,EAAOF,CAAkB,CAC5C", "x_google_ignoreList": [0]}