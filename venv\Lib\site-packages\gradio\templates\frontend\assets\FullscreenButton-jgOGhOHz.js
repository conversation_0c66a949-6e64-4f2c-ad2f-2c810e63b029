/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import{I as g}from"./IconButton-C_HS7fTi.js";import"./index-B7J2Z2jS.js";const{SvelteComponent:C,append:M,attr:i,detach:q,init:S,insert:y,noop:d,safe_not_equal:z,svg_element:$}=window.__gradio__svelte__internal;function B(l){let e,n;return{c(){e=$("svg"),n=$("path"),i(n,"d","M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"),i(e,"xmlns","http://www.w3.org/2000/svg"),i(e,"viewBox","0 0 24 24"),i(e,"fill","none"),i(e,"stroke","currentColor"),i(e,"stroke-width","2"),i(e,"stroke-linecap","round"),i(e,"stroke-linejoin","round"),i(e,"class","feather feather-maximize"),i(e,"width","100%"),i(e,"height","100%")},m(t,r){y(t,e,r),M(e,n)},p:d,i:d,o:d,d(t){t&&q(e)}}}class I extends C{constructor(e){super(),S(this,e,null,B,z,{})}}const{SvelteComponent:F,append:j,attr:s,detach:E,init:H,insert:V,noop:h,safe_not_equal:D,svg_element:v}=window.__gradio__svelte__internal;function N(l){let e,n;return{c(){e=v("svg"),n=v("path"),s(n,"d","M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"),s(e,"xmlns","http://www.w3.org/2000/svg"),s(e,"viewBox","0 0 24 24"),s(e,"fill","none"),s(e,"stroke","currentColor"),s(e,"stroke-width","2"),s(e,"stroke-linecap","round"),s(e,"stroke-linejoin","round"),s(e,"class","feather feather-minimize"),s(e,"width","100%"),s(e,"height","100%")},m(t,r){V(t,e,r),j(e,n)},p:h,i:h,o:h,d(t){t&&E(e)}}}class A extends F{constructor(e){super(),H(this,e,null,N,D,{})}}const{SvelteComponent:G,check_outros:J,create_component:w,destroy_component:k,detach:K,empty:L,flush:O,group_outros:P,init:Q,insert:R,mount_component:b,noop:x,safe_not_equal:T,transition_in:f,transition_out:p}=window.__gradio__svelte__internal,{createEventDispatcher:U}=window.__gradio__svelte__internal;function W(l){let e,n;return e=new g({props:{Icon:I,label:"Fullscreen"}}),e.$on("click",l[3]),{c(){w(e.$$.fragment)},m(t,r){b(e,t,r),n=!0},p:x,i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function X(l){let e,n;return e=new g({props:{Icon:A,label:"Exit fullscreen mode"}}),e.$on("click",l[2]),{c(){w(e.$$.fragment)},m(t,r){b(e,t,r),n=!0},p:x,i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Y(l){let e,n,t,r;const _=[X,W],c=[];function u(o,a){return o[0]?0:1}return e=u(l),n=c[e]=_[e](l),{c(){n.c(),t=L()},m(o,a){c[e].m(o,a),R(o,t,a),r=!0},p(o,[a]){let m=e;e=u(o),e===m?c[e].p(o,a):(P(),p(c[m],1,1,()=>{c[m]=null}),J(),n=c[e],n?n.p(o,a):(n=c[e]=_[e](o),n.c()),f(n,1),n.m(t.parentNode,t))},i(o){r||(f(n),r=!0)},o(o){p(n),r=!1},d(o){o&&K(t),c[e].d(o)}}}function Z(l,e,n){const t=U();let{fullscreen:r}=e;const _=()=>t("fullscreen",!1),c=()=>t("fullscreen",!0);return l.$$set=u=>{"fullscreen"in u&&n(0,r=u.fullscreen)},[r,t,_,c]}class oe extends G{constructor(e){super(),Q(this,e,Z,Y,T,{fullscreen:0})}get fullscreen(){return this.$$.ctx[0]}set fullscreen(e){this.$$set({fullscreen:e}),O()}}export{oe as F};
//# sourceMappingURL=FullscreenButton-jgOGhOHz.js.map
