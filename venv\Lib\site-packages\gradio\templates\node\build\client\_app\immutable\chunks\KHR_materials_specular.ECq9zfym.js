import{ar as u,C as n,an as i,ao as d}from"./index.BoI39RQH.js";import{GLTFLoader as m}from"./glTFLoader.BetPWe9U.js";const l="KHR_materials_specular";class h{constructor(s){this.name=l,this.order=190,this._loader=s,this.enabled=this._loader.isExtensionUsed(l)}dispose(){this._loader=null}loadMaterialPropertiesAsync(s,e,r){return m.LoadExtensionAsync(s,e,this.name,(o,a)=>{const t=new Array;return t.push(this._loader.loadMaterialPropertiesAsync(s,e,r)),t.push(this._loadSpecularPropertiesAsync(o,a,r)),Promise.all(t).then(()=>{})})}_loadSpecularPropertiesAsync(s,e,r){if(!(r instanceof u))throw new Error(`${s}: Material type not supported`);const o=new Array;return e.specularFactor!==void 0&&(r.metallicF0Factor=e.specularFactor),e.specularColorFactor!==void 0&&(r.metallicReflectanceColor=n.FromArray(e.specularColorFactor)),e.specularTexture&&(e.specularTexture.nonColorData=!0,o.push(this._loader.loadTextureInfoAsync(`${s}/specularTexture`,e.specularTexture,a=>{a.name=`${r.name} (Specular)`,r.metallicReflectanceTexture=a,r.useOnlyMetallicFromMetallicReflectanceTexture=!0}))),e.specularColorTexture&&o.push(this._loader.loadTextureInfoAsync(`${s}/specularColorTexture`,e.specularColorTexture,a=>{a.name=`${r.name} (Specular Color)`,r.reflectanceTexture=a})),Promise.all(o).then(()=>{})}}i(l);d(l,!0,c=>new h(c));export{h as KHR_materials_specular};
//# sourceMappingURL=KHR_materials_specular.ECq9zfym.js.map
