../../Scripts/f2py.exe,sha256=wUf6iJILS4mM817TjA1Q3urmw2FH5cY1bcHAdnlrtd8,108380
../../Scripts/numpy-config.exe,sha256=O-Pufih03sCAUxEwfXtFXRengzc1lKB0_n6Ip5GXZbg,108380
numpy-2.1.2-cp310-cp310-win_amd64.whl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy-2.1.2.dist-info/DELVEWHEEL,sha256=2jofbrest6RDY7cOoGyPLkp__7LFstdwvu-LO_akYwY,445
numpy-2.1.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
numpy-2.1.2.dist-info/LICENSE.txt,sha256=whSmXuKn3M0JXJ-7TSGeYX3UL526w1mxrEnmcCCwMDY,47582
numpy-2.1.2.dist-info/METADATA,sha256=eeIpC3-CAUXqu8oAiwLJ7E8pUh6xPFBok-HmiuotvP0,59743
numpy-2.1.2.dist-info/RECORD,,
numpy-2.1.2.dist-info/WHEEL,sha256=1nIT8bOU3dBEtO1OHNUw1PB7s17JH9tAQ93SLqU9JNM,85
numpy-2.1.2.dist-info/entry_points.txt,sha256=4mXDNhJDQ9GHqMBeRJ8B3PlixTFmkXGqU3RVuac20q0,172
numpy.libs/libscipy_openblas64_-c16e4918366c6bc1f1cd71e28ca36fc0.dll,sha256=emJiC1VvSkhconPjTw4iTzRdpFMNFQKcdLpupd6HiTQ,20269568
numpy.libs/msvcp140-23ebcc0b37c8e3d074511f362feac48b.dll,sha256=MubIEAvWLnqR9QmWwqWWktx5a28UCi36TeMTykPUx0g,618728
numpy/__config__.py,sha256=1W2wvtk2jhjAlq1zAlyn11ZdG2obpkIhS87rzlsIT9E,5536
numpy/__init__.cython-30.pxd,sha256=IcK9m-YYaER0GrKBPe1L_isKtSkEJPIoWLv1WkZkpUs,47009
numpy/__init__.pxd,sha256=mLZr42w7VxrMkIV8bMm8lnq-aQ8X6D-G2bSroH5ew1o,43558
numpy/__init__.py,sha256=kp-SKxlEDyNXlWZcziPe0ouohT9ahmPZhtpd7WTDovk,22881
numpy/__init__.pyi,sha256=sgjw6lPKykrjZ53cJA7IHOYW_3pquPLG1d_kNg80qWk,154550
numpy/__pycache__/__config__.cpython-310.pyc,,
numpy/__pycache__/__init__.cpython-310.pyc,,
numpy/__pycache__/_array_api_info.cpython-310.pyc,,
numpy/__pycache__/_configtool.cpython-310.pyc,,
numpy/__pycache__/_distributor_init.cpython-310.pyc,,
numpy/__pycache__/_expired_attrs_2_0.cpython-310.pyc,,
numpy/__pycache__/_globals.cpython-310.pyc,,
numpy/__pycache__/_pytesttester.cpython-310.pyc,,
numpy/__pycache__/conftest.cpython-310.pyc,,
numpy/__pycache__/ctypeslib.cpython-310.pyc,,
numpy/__pycache__/dtypes.cpython-310.pyc,,
numpy/__pycache__/exceptions.cpython-310.pyc,,
numpy/__pycache__/matlib.cpython-310.pyc,,
numpy/__pycache__/version.cpython-310.pyc,,
numpy/_array_api_info.py,sha256=Qd_2x_pUQLdBtnPKodEZy2Zds-R5i2DKQacMmMVRaRk,10727
numpy/_array_api_info.pyi,sha256=uYyKfIJ2C6P1j7W5D1Nj3EZH_WHWli6c5CRlF9TiQxQ,5238
numpy/_configtool.py,sha256=CgdDWSv9AX6XNKIibBXBisvuCu0aUkVVKbNudJfERIw,1046
numpy/_core/__init__.py,sha256=ziVwv-eSrrG6jAQYH3eQcPtNsdRZaWBnvzKCj4MrtbA,5792
numpy/_core/__init__.pyi,sha256=C5NQDIktXlR1OosGgyvY87pyotkyJr3Ci2dMWTLpSi4,88
numpy/_core/__pycache__/__init__.cpython-310.pyc,,
numpy/_core/__pycache__/_add_newdocs.cpython-310.pyc,,
numpy/_core/__pycache__/_add_newdocs_scalars.cpython-310.pyc,,
numpy/_core/__pycache__/_asarray.cpython-310.pyc,,
numpy/_core/__pycache__/_dtype.cpython-310.pyc,,
numpy/_core/__pycache__/_dtype_ctypes.cpython-310.pyc,,
numpy/_core/__pycache__/_exceptions.cpython-310.pyc,,
numpy/_core/__pycache__/_internal.cpython-310.pyc,,
numpy/_core/__pycache__/_machar.cpython-310.pyc,,
numpy/_core/__pycache__/_methods.cpython-310.pyc,,
numpy/_core/__pycache__/_string_helpers.cpython-310.pyc,,
numpy/_core/__pycache__/_type_aliases.cpython-310.pyc,,
numpy/_core/__pycache__/_ufunc_config.cpython-310.pyc,,
numpy/_core/__pycache__/arrayprint.cpython-310.pyc,,
numpy/_core/__pycache__/cversions.cpython-310.pyc,,
numpy/_core/__pycache__/defchararray.cpython-310.pyc,,
numpy/_core/__pycache__/einsumfunc.cpython-310.pyc,,
numpy/_core/__pycache__/fromnumeric.cpython-310.pyc,,
numpy/_core/__pycache__/function_base.cpython-310.pyc,,
numpy/_core/__pycache__/getlimits.cpython-310.pyc,,
numpy/_core/__pycache__/memmap.cpython-310.pyc,,
numpy/_core/__pycache__/multiarray.cpython-310.pyc,,
numpy/_core/__pycache__/numeric.cpython-310.pyc,,
numpy/_core/__pycache__/numerictypes.cpython-310.pyc,,
numpy/_core/__pycache__/overrides.cpython-310.pyc,,
numpy/_core/__pycache__/printoptions.cpython-310.pyc,,
numpy/_core/__pycache__/records.cpython-310.pyc,,
numpy/_core/__pycache__/shape_base.cpython-310.pyc,,
numpy/_core/__pycache__/strings.cpython-310.pyc,,
numpy/_core/__pycache__/umath.cpython-310.pyc,,
numpy/_core/_add_newdocs.py,sha256=hXofj43PzOxOs6isfGQna_hpMwHha0_8ZZxu-roMQOs,218469
numpy/_core/_add_newdocs_scalars.py,sha256=z0hbQcX8082OS8n4zsac211btWOPhBLL_S40v4WNvX4,13002
numpy/_core/_asarray.py,sha256=xXi-F0JvEJMpUkfhAe637kja17_hYY_QGnrTY6XBTSM,4045
numpy/_core/_asarray.pyi,sha256=D69zNnqdPvk1HVuXJ5RsawoUfvaJhXssNM6KYrlbTmA,1082
numpy/_core/_dtype.py,sha256=itXloCOgln5qr5mMFvGA54AEpC0ueUA3qiEH6Z798O0,11108
numpy/_core/_dtype_ctypes.py,sha256=ebN9U_QbymSP-ombYBYc4F7HtgC3ViucNW91MqpNhrM,3838
numpy/_core/_exceptions.py,sha256=35d-to48ERMggcjK60hKzHYhZJUUAxWY1GcJWh9bPJE,5551
numpy/_core/_internal.py,sha256=f7PNtQIywHQYg7rGnL7Wgo27Wwswcwl1i5tlRKnjgmw,30127
numpy/_core/_internal.pyi,sha256=ag2YKEK9nFPm8J2S6czuptXR1lElSRhpuutPVQInuGI,1052
numpy/_core/_machar.py,sha256=_6CjQfUG-Xk0M8_9KBT3vhoGKxvjl2JKG__ZCS19Mdk,11922
numpy/_core/_methods.py,sha256=bpTZVaHA0FzUc9ncesRdyhSOWCnmNj1_tau9a2w8Or0,9915
numpy/_core/_multiarray_tests.cp310-win_amd64.lib,sha256=DszGysPsWCviapEQa2KSCNn8jXJE9iDtztVPfGuxdaU,2418
numpy/_core/_multiarray_tests.cp310-win_amd64.pyd,sha256=AhyCiN8OYl1GDhFKaYU5LEkLcjnxM1OPczuMQA_2TIQ,62976
numpy/_core/_multiarray_umath.cp310-win_amd64.lib,sha256=udQMSEgaQTT4HBjuclg6lqBlpjDloUAyPaA5fLUFkAg,2192
numpy/_core/_multiarray_umath.cp310-win_amd64.pyd,sha256=3DxFnwF56MXNmoY1ZHB_ssA7lGxtOZnTY1VIdc2R9lU,4147712
numpy/_core/_operand_flag_tests.cp310-win_amd64.lib,sha256=BxqSqaW-U1mz2gPm1KB-iw7rfqNA8m57tZkBpzLIMaI,2228
numpy/_core/_operand_flag_tests.cp310-win_amd64.pyd,sha256=M-_ZswwWtaWEqwapN1Jlb1yKQs1gyNgNn5Sq5dOIIHs,11776
numpy/_core/_rational_tests.cp310-win_amd64.lib,sha256=Bq-NJ0yZbxue2eWg6I8LyEK0SX9Vmyys1W-EkZTE9Jk,2156
numpy/_core/_rational_tests.cp310-win_amd64.pyd,sha256=OfQs18mJrpl9jJdDnjQhHg-lpvN5GlXfysyy_ns-8d0,40448
numpy/_core/_simd.cp310-win_amd64.lib,sha256=-LyG9Tu3JbA5o3Vlxx5X7PychoGPd5bXnTM6iBbjMBk,1976
numpy/_core/_simd.cp310-win_amd64.pyd,sha256=Yd_XM1Q8hjsmn9moEXYNPoKIiGdP0unIErI1PrEOnnw,2238464
numpy/_core/_string_helpers.py,sha256=yqhYXnS3SgnP_4PvP7NUYvYJ7c5GeFJz8a8zI_uU0DI,2937
numpy/_core/_struct_ufunc_tests.cp310-win_amd64.lib,sha256=0qvkfQHC0aaGUj8E6rY9MiymMnTqppR2k4bXUX3QERE,2228
numpy/_core/_struct_ufunc_tests.cp310-win_amd64.pyd,sha256=kwmTC1CazNXrz169ABEFEtpSqk1D8PlfptHvukCthG0,13824
numpy/_core/_type_aliases.py,sha256=FS7GRVJtjLnqF_CX8oM4r7RKZ7EEGssh4V-V6eKBGQw,3612
numpy/_core/_type_aliases.pyi,sha256=8FLtTrjAwDYKPUBKuV8ZQ-WRShKWaifzfcDJPII76K0,73
numpy/_core/_ufunc_config.py,sha256=Rz3wllM1Gbq7FF1vAcfFOSHeWJZC6Iwh3KYPNDx8I-g,16080
numpy/_core/_ufunc_config.pyi,sha256=KgouzlKtzHKm3UquSj9kbLI9WAQqpL3bFMmAW6-V4yw,1103
numpy/_core/_umath_tests.cp310-win_amd64.lib,sha256=mqPbrJkWtRQJaXZ9po_H22x0pRRIDaw2wT26QuDU2Zk,2104
numpy/_core/_umath_tests.cp310-win_amd64.pyd,sha256=Tgdgf3d3a6aaB6t5REA2BhrB0bfXeYBwxEGew8vtiH0,33792
numpy/_core/arrayprint.py,sha256=xmQ1-nDnFYgpYNyO91U5d_xv5-5_IqdZszHi6i_VB10,66032
numpy/_core/arrayprint.pyi,sha256=JUnk8QSHGrFPw9HGe7pXEnDZMLXqvUJOaxL0oXmkVyM,4433
numpy/_core/cversions.py,sha256=FISv1d4R917Bi5xJjKKy8Lo6AlFkV00WvSoB7l3acA4,360
numpy/_core/defchararray.py,sha256=FCx6UPLQvzL-YKSPhQMQIpcFkOilV5iCxQO9ctRaOjs,38910
numpy/_core/defchararray.pyi,sha256=YidqJRaCkGmdL2CYEHA1z9VPL_xBeZVyaz-MCZOh_wE,20655
numpy/_core/einsumfunc.py,sha256=BDsOGZ16sr8R9VEZ_YeRpQd99ADMXAUcAHwewCKidr0,54426
numpy/_core/einsumfunc.pyi,sha256=UCvLNwDI8AvfgzoCL0az2f5QNS-Q5Jw-wsVy1lxuP2E,5004
numpy/_core/fromnumeric.py,sha256=4JNHEh073WHRrKswehL2osRnl54ONOpW4vDZe9IvYvw,148545
numpy/_core/fromnumeric.pyi,sha256=j-HECBBcli0bPzeLFFZt6FXBTZ84forasOy77TaMJYc,29607
numpy/_core/function_base.py,sha256=IxkRUEXTRlLS4EY9lWxN6fQaIaI0clX75B_jRu-WG0U,20588
numpy/_core/function_base.pyi,sha256=ru7yHdg1jQMVWlov1ubbcDqIoGv3oKyk_x4p5SleXCA,5223
numpy/_core/getlimits.py,sha256=BFfOM-yusCiZ97jUpFhTjwFGAfbXC1_e4z3lBF2n__g,26727
numpy/_core/getlimits.pyi,sha256=rRrU4RZYBrsczGZq6_VRCBNwUKyrqPAHARXXLQuw950,88
numpy/_core/include/numpy/__multiarray_api.c,sha256=Vc65MKuXE5761vVI9qdZkPyg3C5_k_ickum0Q04EOOA,13045
numpy/_core/include/numpy/__multiarray_api.h,sha256=SzcxgIDQ8m4Ds1fvlM9fQ8RuINJpcPLRKzpb9HFDtpw,62996
numpy/_core/include/numpy/__ufunc_api.c,sha256=NoTcyLqrAF8F3AE0TDvlDFS7DXuFJRpoINEaDnZWhys,1809
numpy/_core/include/numpy/__ufunc_api.h,sha256=Q36B7NKN8E6GLytefgBOGLfgRnt8ayO1Conr2QWlqkA,13506
numpy/_core/include/numpy/_neighborhood_iterator_imp.h,sha256=s5TK2aPpClbw4CbVJCij__hzoh5IgHIIZK0k6FKtqfc,1947
numpy/_core/include/numpy/_numpyconfig.h,sha256=mqDMFv5Vhk2nHXNf6TIWzz7ozrtc9aNaN8_LJZBYjX0,902
numpy/_core/include/numpy/_public_dtype_api_table.h,sha256=4ylG8s52kZEx__QODt_7Do8QitmhDSvTeZ7Lar0fOgo,4660
numpy/_core/include/numpy/arrayobject.h,sha256=ghWzloPUkSaVkcsAnBnpbrxtXeXL-mkzVGJQEHFxjnk,211
numpy/_core/include/numpy/arrayscalars.h,sha256=4TrsilxaUiH4mVCkElEPTM_C_8c67O9R4Whx-3QzDE4,4439
numpy/_core/include/numpy/dtype_api.h,sha256=C-eCJHKjKAO73LavKj7cgDfLKz-aSmBIgAa2fOGwm1A,19671
numpy/_core/include/numpy/halffloat.h,sha256=qYgX5iQfNzXICsnd0MCRq5ELhhfFjlRGm1xXGimQm44,2029
numpy/_core/include/numpy/ndarrayobject.h,sha256=V5Zkf5a9vWyV8ZInBgAceBn7c9GK4aquhzeGTW_Sgls,12361
numpy/_core/include/numpy/ndarraytypes.h,sha256=1_cmZjFmB2alFk4lCEw1H008ctP8k5__R2MJfdm-hbo,66876
numpy/_core/include/numpy/npy_1_7_deprecated_api.h,sha256=eYbQlqb6mzJnUKuVfl2mmrMpvB3GN2rFgHazFO9CKT8,3858
numpy/_core/include/numpy/npy_2_compat.h,sha256=VxsRXAtDfLlXkvH-ErZRSuH49k9EjcFwcSUSfTPRzAU,8795
numpy/_core/include/numpy/npy_2_complexcompat.h,sha256=uW0iF-qMwQNn4PvIfWCrYce6b4OrYUO4BWu-VYYAZag,885
numpy/_core/include/numpy/npy_3kcompat.h,sha256=dV01ltbxntPY8cN7WAL4MX3KHeyCLeSBDQreDxs09aQ,10022
numpy/_core/include/numpy/npy_common.h,sha256=3njI4LhBxMZvkkdG3nLq0NZI7lNqx9dnvcTfCgEW0rI,37621
numpy/_core/include/numpy/npy_cpu.h,sha256=6CVIqBgYWa75CDXif9WrKbsrz0Rw1P5_SqH8ltgerLA,4758
numpy/_core/include/numpy/npy_endian.h,sha256=G3x4fuvRgY6_Y0AWiJaQ5ZbtmMRRt_QUnYCwkqrHhPE,2863
numpy/_core/include/numpy/npy_math.h,sha256=ItgOGoKdQan93epl_EPF9Rm6M5Mis6xW__PbPIZsENA,19492
numpy/_core/include/numpy/npy_no_deprecated_api.h,sha256=jIcjEP2AbovDTfgE-qtvdP51_dVGjVnEGBX86rlGSKE,698
numpy/_core/include/numpy/npy_os.h,sha256=j044vd1C1oCcW52r3htiVNhUaJSEqCjKrODwMHq3TU0,1298
numpy/_core/include/numpy/numpyconfig.h,sha256=kiVGqpuyjCuokEZs1AANGg6kNsRrhOSPZ6xSn0E2poI,7013
numpy/_core/include/numpy/random/LICENSE.txt,sha256=1UR2FVi1EIZsIffootVxb8p24LmBF-O2uGMU23JE0VA,1039
numpy/_core/include/numpy/random/bitgen.h,sha256=_H0uXqmnub4PxnJWdMWaNqfpyFDu2KB0skf2wc5vjUc,508
numpy/_core/include/numpy/random/distributions.h,sha256=GLURa3sFESZE0_0RK-3Gqmfa96itBHw8LlsNyy9EPt4,10070
numpy/_core/include/numpy/random/libdivide.h,sha256=F9PLx6TcOk-sd0dObe0nWLyz4HhbHv2K7voR_kolpGU,82217
numpy/_core/include/numpy/ufuncobject.h,sha256=DZ6lZMDfxmKKZzdiIofdg2bAhZLWjEpuntLyL9U0r9Q,12256
numpy/_core/include/numpy/utils.h,sha256=vzJAbatJYfxHmX2yL_xBirmB4mEGLOhJ92JlV9s8yPs,1222
numpy/_core/lib/npy-pkg-config/mlib.ini,sha256=hYWFyoBxE036dh19si8UPka01H2cv64qlc4ZtgoA_7A,156
numpy/_core/lib/npy-pkg-config/npymath.ini,sha256=e0rdsb00Y93VuammuvIIFlzZtnUAXwsS1XNKlCU8mFQ,381
numpy/_core/lib/npymath.lib,sha256=RmPhD8K-OXrYENNdfsdjAaOL0OJetaT2gU6kXAFr2_4,154174
numpy/_core/lib/pkgconfig/numpy.pc,sha256=Ie3tP35Y3d7vpImNVppMMcBtGXpNsBNWetCssO07ZXs,198
numpy/_core/memmap.py,sha256=_ko5Q2bdmdGYIroIN_x6gsfYzV9k_O8QjNZmAzuhdfU,12563
numpy/_core/memmap.pyi,sha256=pwOLGTEk20Z2Tao7hqxLDqfCHrO10otgozEsUO-bPeo,58
numpy/_core/multiarray.py,sha256=P6WyKUMa5enzYens62bdUl1JZsWEzY0IO6KN0WqpZnE,59418
numpy/_core/multiarray.pyi,sha256=bNpidX0YsseFEMighN8rq_JIx9lGkL2pIp4CFi8hNEs,27591
numpy/_core/numeric.py,sha256=OjnrhDFDH2eFzocDFvdaQc9irWlY5nrCTVyCLMkt-q8,84554
numpy/_core/numeric.pyi,sha256=pKG5emD7NQJX2rElS6UNqyLKQqoWGApsOhqHpfXGpDk,16020
numpy/_core/numerictypes.py,sha256=ITU0GBClQSEau264NDq2y1xjvLcq-I3JQTW73bGfEP8,16742
numpy/_core/numerictypes.pyi,sha256=EsyKgQy6hE9E5YE7uSsbATlfwAy_A6T5KklR-6AxZCo,1779
numpy/_core/overrides.py,sha256=w4p_e55w6lp_vC34tFnH3GfN7EE-ZESrvmwtHEUxohA,7275
numpy/_core/printoptions.py,sha256=btxyfisjJ_7DB5JtZKAtaLYL9qmcmTnnJ8pHFcwn2Wc,1095
numpy/_core/records.py,sha256=BLYyUsbaoLp72pvj_ruumE34G7OczLj9ByK2C2O-TTc,37958
numpy/_core/records.pyi,sha256=omVK2D0mg75BELrgC7g4sik2Q6soN7i7Qlq199J5f1g,9103
numpy/_core/shape_base.py,sha256=vDd6Q7oCU48u0sCU2WR1e5Fs2g6IWYIYnaCNZdhqGQE,33433
numpy/_core/shape_base.pyi,sha256=pUhkWJLIaQDWtiI4Sj5OsmCgDbKMAhW5BtyxwPZzl8A,3169
numpy/_core/strings.py,sha256=JNffSDEObKROr8B1j7iAZzpZ5L6YMW3TExYikW97Pvc,44739
numpy/_core/strings.pyi,sha256=SNbskTazF10ec69Z6yzVby2Hdreuh7bz2VKGPPZ06UA,7851
numpy/_core/tests/__pycache__/_locales.cpython-310.pyc,,
numpy/_core/tests/__pycache__/_natype.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test__exceptions.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_abc.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_api.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_argparse.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_array_api_info.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_array_coercion.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_array_interface.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_arraymethod.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_arrayobject.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_arrayprint.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_casting_floatingpoint_errors.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_casting_unittests.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_conversion_utils.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_cpu_dispatcher.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_cpu_features.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_custom_dtypes.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_cython.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_datetime.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_defchararray.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_deprecations.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_dlpack.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_dtype.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_einsum.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_errstate.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_extint128.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_function_base.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_getlimits.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_half.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_hashtable.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_indexerrors.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_indexing.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_item_selection.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_limited_api.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_longdouble.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_machar.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_mem_overlap.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_mem_policy.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_memmap.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_multiarray.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_multithreading.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_nditer.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_nep50_promotions.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_numeric.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_numerictypes.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_overrides.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_print.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_protocols.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_records.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_regression.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_scalar_ctors.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_scalar_methods.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_scalarbuffer.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_scalarinherit.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_scalarmath.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_scalarprint.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_shape_base.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_simd.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_simd_module.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_stringdtype.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_strings.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_ufunc.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_umath.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_umath_accuracy.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_umath_complex.cpython-310.pyc,,
numpy/_core/tests/__pycache__/test_unicode.cpython-310.pyc,,
numpy/_core/tests/_locales.py,sha256=esGp_wCqPpxFxy3eUF-r_Wk-yjFjrQEwkgSzolRzUr0,2280
numpy/_core/tests/_natype.py,sha256=uVXHCahmyDbZZAaQ-OKqaWnOgJRIYRETU06drssSSP0,6457
numpy/_core/tests/data/astype_copy.pkl,sha256=lWSzCcvzRB_wpuRGj92spGIw-rNPFcd9hwJaRVvfWdk,716
numpy/_core/tests/data/generate_umath_validation_data.cpp,sha256=9TBdxpPo0djv1CKxQ6_DbGKRxIZVawitAm7AMmWKroI,6012
numpy/_core/tests/data/recarray_from_file.fits,sha256=NA0kliz31FlLnYxv3ppzeruONqNYkuEvts5wzXEeIc4,8640
numpy/_core/tests/data/umath-validation-set-README.txt,sha256=GfrkmU_wTjpLkOftWDuGayEDdV3RPpN2GRVQX61VgWI,982
numpy/_core/tests/data/umath-validation-set-arccos.csv,sha256=VUdQdKBFrpXHLlPtX2WYIK_uwkaXgky85CZ4aNuvmD4,62794
numpy/_core/tests/data/umath-validation-set-arccosh.csv,sha256=tbuOQkvnYxSyJf_alGk3Zw3Vyv0HO5dMC1hUle2hWwQ,62794
numpy/_core/tests/data/umath-validation-set-arcsin.csv,sha256=JPEWWMxgPKdNprDq0pH5QhJ2oiVCzuDbK-3WhTKny8o,62768
numpy/_core/tests/data/umath-validation-set-arcsinh.csv,sha256=fwuq25xeS57kBExBuSNfewgHb-mgoR9wUGVqcOXbfoI,61718
numpy/_core/tests/data/umath-validation-set-arctan.csv,sha256=nu33YyL-ALXSSF5cupCTaf_jTPLK_QyUfciNQGpffkY,61734
numpy/_core/tests/data/umath-validation-set-arctanh.csv,sha256=wHSKFY2Yvbv3fnmmfLqPYpjhkEM88YHkFVpZQioyBDw,62768
numpy/_core/tests/data/umath-validation-set-cbrt.csv,sha256=FFi_XxEnGrfJd7OxtjVFT6WFC2tUqKhVV8fmQfb0z8o,62275
numpy/_core/tests/data/umath-validation-set-cos.csv,sha256=ccDri5_jQ84D_kAmSwZ_ztNUPIhzhgycDtNsPB7m8dc,60497
numpy/_core/tests/data/umath-validation-set-cosh.csv,sha256=DnN6RGvKQHAWIofchmhGH7kkJej2VtNwGGMRZGzBkTQ,62298
numpy/_core/tests/data/umath-validation-set-exp.csv,sha256=mPhjF4KLe0bdwx38SJiNipD24ntLI_5aWc8h-V0UMgM,17903
numpy/_core/tests/data/umath-validation-set-exp2.csv,sha256=sD94pK2EAZAyD2fDEocfw1oXNw1qTlW1TBwRlcpbcsI,60053
numpy/_core/tests/data/umath-validation-set-expm1.csv,sha256=tyfZN5D8tlm7APgxCIPyuy774AZHytMOB59H9KewxEs,61728
numpy/_core/tests/data/umath-validation-set-log.csv,sha256=CDPky64PjaURWhqkHxkLElmMiI21v5ugGGyzhdfUbnI,11963
numpy/_core/tests/data/umath-validation-set-log10.csv,sha256=dW6FPEBlRx2pcS-7eui_GtqTpXzOy147il55qdP-8Ak,70551
numpy/_core/tests/data/umath-validation-set-log1p.csv,sha256=2aEsHVcvRym-4535CkvJTsmHywkt01ZMfmjl-d4fvVI,61732
numpy/_core/tests/data/umath-validation-set-log2.csv,sha256=aVZ7VMQ5urGOx5MMMOUmMKBhFLFE-U7y6DVCTeXQfo0,70546
numpy/_core/tests/data/umath-validation-set-sin.csv,sha256=GvPrQUEYMX1iB2zjbfK26JUJOxtqbfiRUgXuAO1QcP0,59981
numpy/_core/tests/data/umath-validation-set-sinh.csv,sha256=lc7OYcYWWpkxbMuRAWmogQ5cKi7EwsQ2ibiMdpJWYbw,61722
numpy/_core/tests/data/umath-validation-set-tan.csv,sha256=fn7Dr9s6rcqGUzsmyJxve_Z18J4AUaSm-uo2N3N_hfk,61728
numpy/_core/tests/data/umath-validation-set-tanh.csv,sha256=xSY5fgfeBXN6fal4XDed-VUcgFIy9qKOosa7vQ5v1-U,61728
numpy/_core/tests/examples/cython/__pycache__/setup.cpython-310.pyc,,
numpy/_core/tests/examples/cython/checks.pyx,sha256=dTrtc8ccMi3cnKIaYxt1GwKai1QcOBmgJe2PYH0q8_c,7612
numpy/_core/tests/examples/cython/meson.build,sha256=EaUdTgpleUBROExDaFVMnWIYW4XDxFLFGK9ej_pTtQg,1311
numpy/_core/tests/examples/cython/setup.py,sha256=tPQ9m6dr48JSvLpgmV-aVnMWMV0islzlSrynB5yGYDY,894
numpy/_core/tests/examples/limited_api/__pycache__/setup.cpython-310.pyc,,
numpy/_core/tests/examples/limited_api/limited_api1.c,sha256=RcHe_nyyjv86gjF9E53cexQiGW-YNs8OGGqjrxCFhBc,363
numpy/_core/tests/examples/limited_api/limited_api2.pyx,sha256=4P5-yu0yr8NBa-TFtw4v30LGjccRroRAQFFLaztEK9I,214
numpy/_core/tests/examples/limited_api/limited_api_latest.c,sha256=drvrNSyOeF0Or0trDmayJWllTP7c4Nzpp9T0ydwPAGo,471
numpy/_core/tests/examples/limited_api/meson.build,sha256=yitMzLuGDhWCjyavpm5UEBrhwKnfXOVAxA3ZL7PlB0Q,1686
numpy/_core/tests/examples/limited_api/setup.py,sha256=N7kqsVp4iIE20IebigEJUW3nW2F0l6Vthb5qNvKHBmM,457
numpy/_core/tests/test__exceptions.py,sha256=gy7-mZq7XS5z_w-us4gRIzC0H7XqC_62xaQQmWqLzSw,2970
numpy/_core/tests/test_abc.py,sha256=u82wrSKXJ2V7AmNrh4klHxYiqOx0BYWJ4j7hqTMH--A,2275
numpy/_core/tests/test_api.py,sha256=FCyb7MJv3-5_RVsICrdK93xFIo3wpvpPeX2xHfIG7F0,23548
numpy/_core/tests/test_argparse.py,sha256=vPctuxToPkZMlbgjnzE924XkxXYUdBxlR6LsP2_-aQM,2914
numpy/_core/tests/test_array_api_info.py,sha256=g3uxqnNihXU5rOt8es_AvWcMguY647FJbBrFGYJAXa8,3174
numpy/_core/tests/test_array_coercion.py,sha256=oyuHheN_aWaRVHpxv8_zzLHBeLwniZ_xjbuYDUYQ1TQ,35763
numpy/_core/tests/test_array_interface.py,sha256=E6QR-DJYTJX_F-i70PakQMmvxzfSBD-W1rFve70MFTg,7986
numpy/_core/tests/test_arraymethod.py,sha256=zePjXm333bIKCIUkqwka0SUa27vdYwxrUIKUi6lJqvA,3351
numpy/_core/tests/test_arrayobject.py,sha256=wRr-JK19ky86QqRwI-O85c87LiakIU1E3uPL8fp3LMw,823
numpy/_core/tests/test_arrayprint.py,sha256=WHAcaINL6l0LOGZ_-3ZG6uSYvtaUa9W3g7uSKoGrP4I,49198
numpy/_core/tests/test_casting_floatingpoint_errors.py,sha256=FRRWJBppa5v1axij6L14ENmzoZS8R_SyJKgHiAFI2KQ,5228
numpy/_core/tests/test_casting_unittests.py,sha256=uieTDY3O_2q2mvI_D4t0IiLPvqic6mEuYoKFc2yPqAY,35126
numpy/_core/tests/test_conversion_utils.py,sha256=cz2WEiCYSEP9m_7RHa2pS8WW0PcWO0E-LvpLTO72PkE,6814
numpy/_core/tests/test_cpu_dispatcher.py,sha256=Bpb_ep7kT3OfNypV1pSOWCNlk8oT46kjZBEGS32qfCI,1597
numpy/_core/tests/test_cpu_features.py,sha256=J59IeoCuBt941Zk8LXPG3XVFJA7QHnCvUBVbp84m3Ps,15723
numpy/_core/tests/test_custom_dtypes.py,sha256=0RFD2vUFn3iUKV60wdifX0iY8egechVcGEk5JOLOIMg,11945
numpy/_core/tests/test_cython.py,sha256=Hq11TQcXbVNj3JNbRlCFLId2lJLjI20a6SGw1VOCXS4,8800
numpy/_core/tests/test_datetime.py,sha256=ZTB_HXJvpB3ym5ZVPIcWBppy3O93FardP6Gz34_cqA8,119982
numpy/_core/tests/test_defchararray.py,sha256=iOO8AUBOwhadTI8UUlOw-tI0Dd0l4k1rLY9gWFuQLbw,31423
numpy/_core/tests/test_deprecations.py,sha256=TZGSxzYL5hlSRt_ohF4cljyJtbHSy9revoFpe-j-DTQ,29424
numpy/_core/tests/test_dlpack.py,sha256=GlNhsoO5Pf2jcvGeFaA1jE-bzj1yvTgmfJcWua_ie3M,5653
numpy/_core/tests/test_dtype.py,sha256=50OxBU0jQJISx_yZjEk4XMDBE9oUsRTUhavUh1fJ0n8,80221
numpy/_core/tests/test_einsum.py,sha256=vCCmEIxx5ijwrOeP6-t-fGpRfdveeYrDjjlkxpgLwZs,54176
numpy/_core/tests/test_errstate.py,sha256=VWq6zrKdCWlhSzuHPDJGEvNt4UudT_ZupA_wfGe_AR4,4775
numpy/_core/tests/test_extint128.py,sha256=YKIX0q9ENW0qehJtdaAAB2sFG0me42U2yJmq0kK6xGQ,5863
numpy/_core/tests/test_function_base.py,sha256=BTNE1NHVdOCZAcXPUlhM8i9_w_L16fSSyJOigK_P3gU,17601
numpy/_core/tests/test_getlimits.py,sha256=4fyvmHUyNbkJXWBs8AEaGoQV23o4HJJIp9nrub_-_EU,6932
numpy/_core/tests/test_half.py,sha256=Y31VvCfWPLDZU8yy3Riy9ocWAMQF2tjb3QGSAyy1gW0,25165
numpy/_core/tests/test_hashtable.py,sha256=-Zl-uppJbc9kwPN_ZlxJMA76yAQKsgGmQQWI8Y-sxaM,1182
numpy/_core/tests/test_indexerrors.py,sha256=keWclNvFu3ukhVSXc97w3bJM8BvkOpul6kjNudf1F2Q,4858
numpy/_core/tests/test_indexing.py,sha256=LC6M2E6ZaJkaVmWkY8Kin2zuI1_CMyWdcHToCCUBHc8,56504
numpy/_core/tests/test_item_selection.py,sha256=zaGuMcTDsbCpQO1k9c9xuc4jUWhbArfn_1INfilf9hk,6623
numpy/_core/tests/test_limited_api.py,sha256=p7jhKhN6ScAeUXf2bZK71kwTKWts_fqkCfqKfmy8psI,2915
numpy/_core/tests/test_longdouble.py,sha256=kcu2DpPuw-j0on0INw-LNMOjw4wuXI_fPbvn-9n-Oks,14285
numpy/_core/tests/test_machar.py,sha256=z0mwyf6ASFI-gtMemFAag-8eEXKjb12mZ1BSpLYA52Q,1099
numpy/_core/tests/test_mem_overlap.py,sha256=jNg_XE2glkVlYEo0a4RepIaRN-xvoshQwFl0Urtfwt8,30073
numpy/_core/tests/test_mem_policy.py,sha256=Q7WeHyWxqZkR0WgmL5MzJ7xrOoyqysgONlqsKGUfeWY,17126
numpy/_core/tests/test_memmap.py,sha256=l5dwyfYK6Z7vODg6RR7YWPLNyoDYIKJZEFCwFRCygok,7964
numpy/_core/tests/test_multiarray.py,sha256=AOA3AUyXqLZZ6L28jtwVOw5EnmgtfIGGNZz7AojYt2Y,400979
numpy/_core/tests/test_multithreading.py,sha256=o0PZ0kDADeW_Lzw8JIQKSohrMl6W1WMulEk2uf7xNBM,3674
numpy/_core/tests/test_nditer.py,sha256=kJ7O4Oq6y0r1xpLv6KPr8DKhgIV4Yr2TlRgmCjGwpQc,134633
numpy/_core/tests/test_nep50_promotions.py,sha256=j8yGWBl_OjqXmJA7Q3hQQzhppV-rGx0y0h3lbODCZec,13208
numpy/_core/tests/test_numeric.py,sha256=QnRR7Prc4skHD-9GwNiJEKn6TMNurU3EMrIzVmI-OY4,161605
numpy/_core/tests/test_numerictypes.py,sha256=hQ1YqasQ6mq--7fnKO08koqVrnsK2IwloWcdElKB7U4,23912
numpy/_core/tests/test_overrides.py,sha256=vuK9oBmlnEi4aKGhw0UlcAAgLfbG8BUm5-RiIGbrGTA,26433
numpy/_core/tests/test_print.py,sha256=HhOMC4roNrnXdncgpXPmFgsJWwcRpCc9u3KOIMSRxDw,7038
numpy/_core/tests/test_protocols.py,sha256=19yXLJKXmy1-ArAclZ-F7PtgN6LmIHbiPvg6Vi2PkJk,1234
numpy/_core/tests/test_records.py,sha256=QJtFytxMTfurTwXtIeNxu-on4mAuXWSZzPVE9Ypr1CA,21075
numpy/_core/tests/test_regression.py,sha256=OLTLFybjenG_0gXbZzPWYp_YCyR7KsEHKt8a8-wNA3c,97515
numpy/_core/tests/test_scalar_ctors.py,sha256=CrPYj6xo5A52VVqecc9S8Q0JQWPPyU2pND5KUNX_-pw,6923
numpy/_core/tests/test_scalar_methods.py,sha256=16yB3b9hka7WuGwOy4yKwzwiDZVlQSsoKaFWRJ-e6Mc,8407
numpy/_core/tests/test_scalarbuffer.py,sha256=0d8LgyIclgmi4wJM218frNDXa4V408McDFDkieBpJFo,5735
numpy/_core/tests/test_scalarinherit.py,sha256=PbTAgUejbabwm8MGub6xqlzgiTe5WK6LAEJiUnsxPsc,2697
numpy/_core/tests/test_scalarmath.py,sha256=oTDTXLYN6VO85WPxQI8ROi44b-dr_FkRlFMI4JEE4iw,47738
numpy/_core/tests/test_scalarprint.py,sha256=tmqMvApqJSypj7HvIkwpAX0WrC53moxrEWt3PVRWBlA,19170
numpy/_core/tests/test_shape_base.py,sha256=Ja5uITUZHbopamx-WUDf28uhBSi4iWJqm7LYae3gZm8,31869
numpy/_core/tests/test_simd.py,sha256=RBOfCkMnz9nTusabir-w7k3AJlD2KKi88srolG_0178,50031
numpy/_core/tests/test_simd_module.py,sha256=s22tqYtgN0x-5B3HTXiGfIV2aTXyQQH18c1fYj6VRhg,4004
numpy/_core/tests/test_stringdtype.py,sha256=YJaoqQAZA4nlOYsji9gcrWnVPjlhBibsssN8caBUyY0,54907
numpy/_core/tests/test_strings.py,sha256=kYObxy1CQrYgMDXZfe_xLdUfhTDZ4f-xZUFRpubXxLw,49872
numpy/_core/tests/test_ufunc.py,sha256=uu4-VuiNfQr5GDz-sxPKjAdL7A9okp59zPhtG-klA9w,134797
numpy/_core/tests/test_umath.py,sha256=ikimpSP98mg9hf1GLwSRSkmbeqIbC9pXxpiwCVpXBjU,197188
numpy/_core/tests/test_umath_accuracy.py,sha256=_-WUbhiymgBy5M5engPoJ_AdOdAMhgmw9lF7bRiC3Qs,5569
numpy/_core/tests/test_umath_complex.py,sha256=ZRnJuFo6DQPz5tdUUZyHSamtaI2BFlLXzz6AtlILVIw,23912
numpy/_core/tests/test_unicode.py,sha256=u2ddCDJAY7NylPEBobucIvIhWlzOq_IzZImyfbq7Seo,13236
numpy/_core/umath.py,sha256=mefzi1NWmVkB8yrjG_yK0mewvfvKiqbKBB-UJO6niKo,2103
numpy/_distributor_init.py,sha256=ahBbZPz-mGZrmwx35FHQ26AiinST78FxvupiBBKGFp4,422
numpy/_expired_attrs_2_0.py,sha256=V3_qiNdBMeU90sMHAWx0HZIJ_30yfnv2eoTQ2MDKxzM,3993
numpy/_globals.py,sha256=FWUxIto9hQ5Mi2NoxP6DeGpI3bgS8H9xq7jfzaVLtG0,3185
numpy/_pyinstaller/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/_pyinstaller/__pycache__/__init__.cpython-310.pyc,,
numpy/_pyinstaller/__pycache__/hook-numpy.cpython-310.pyc,,
numpy/_pyinstaller/hook-numpy.py,sha256=GFGizYFjd9HsYMOtby7gew94CkvTrRW77ECGPNUgGGc,1429
numpy/_pyinstaller/tests/__init__.py,sha256=ZKqNjqlKw1pYiv57onbjDJnJdVrLawbZAcl-mPZzcSw,345
numpy/_pyinstaller/tests/__pycache__/__init__.cpython-310.pyc,,
numpy/_pyinstaller/tests/__pycache__/pyinstaller-smoke.cpython-310.pyc,,
numpy/_pyinstaller/tests/__pycache__/test_pyinstaller.cpython-310.pyc,,
numpy/_pyinstaller/tests/pyinstaller-smoke.py,sha256=xt3dl_DjxuzVTPrqmVmMOZm5-24wBG2TxldQl78Xt1g,1175
numpy/_pyinstaller/tests/test_pyinstaller.py,sha256=31zWlvlAC2sfhdew97x8aDvcYUaV3Tc_0CwFk8pgKaM,1170
numpy/_pytesttester.py,sha256=5UoibbUqV5hcjEUuXKXDWiOzecc5kiBz0MpEKNVm1R0,6486
numpy/_pytesttester.pyi,sha256=naZg3QsbkOp4kcjmCJzs3A5vmkLp1WynIgD5hUKHLZI,507
numpy/_typing/__init__.py,sha256=2_xckFOgzHzgYwEzbvB7Ws1BFdIxmfwF3nX8RyqTXRs,7317
numpy/_typing/__pycache__/__init__.cpython-310.pyc,,
numpy/_typing/__pycache__/_add_docstring.cpython-310.pyc,,
numpy/_typing/__pycache__/_array_like.cpython-310.pyc,,
numpy/_typing/__pycache__/_char_codes.cpython-310.pyc,,
numpy/_typing/__pycache__/_dtype_like.cpython-310.pyc,,
numpy/_typing/__pycache__/_extended_precision.cpython-310.pyc,,
numpy/_typing/__pycache__/_nbit.cpython-310.pyc,,
numpy/_typing/__pycache__/_nested_sequence.cpython-310.pyc,,
numpy/_typing/__pycache__/_scalars.cpython-310.pyc,,
numpy/_typing/__pycache__/_shape.cpython-310.pyc,,
numpy/_typing/_add_docstring.py,sha256=lDztWZ6UOFTDo3MwoxB71P5nDwAIiT49vOioAtTiy1U,4122
numpy/_typing/_array_like.py,sha256=9EKaSSiCGWQqtjmRRKbt6D5aeRpXK_6olUf114oXV5o,4863
numpy/_typing/_callable.pyi,sha256=j1zzxUa4yPxZg3sb75_mInEXSXqfXHqaxLSFuAuDKhc,12936
numpy/_typing/_char_codes.py,sha256=ggRrevkEeA5Q8z4VOQH7rsJ_X7meJS0bjp-b3O14UvI,7121
numpy/_typing/_dtype_like.py,sha256=FfV0MwNRDULdukQrzKJulSQO82hb3SdYYZSn80QQjkI,6138
numpy/_typing/_extended_precision.py,sha256=5PhjET4NkRp-LSgffJqfcZ1C5Cp-xERB14FNXfUvRkU,804
numpy/_typing/_nbit.py,sha256=LteTa09AlIKphaJa-TcpyYv9TzYI3u4xjBaC5ZHUkpw,378
numpy/_typing/_nested_sequence.py,sha256=NNECI_Lo3vAKF4GnuGsrGufoayUBG5sJv3RhM7yYAOk,2652
numpy/_typing/_scalars.py,sha256=sKaaEEZqAQtiEijeuH4U5KPNpG7FYsBtGO73l9dti9Q,1058
numpy/_typing/_shape.py,sha256=3g0rNpZHxM7rPInBJMSGpbVD9Y0Lw1QtkFEN_yrWEeo,238
numpy/_typing/_ufunc.pyi,sha256=wtJVbrY5U-ZZvtgrXfX70UR-4a_qPI1UBxBIC1L71_M,12539
numpy/_utils/__init__.py,sha256=DKnuUUKF1rWvDlbfCO43rq61My2EC1RHnBm7gD5zNBA,3311
numpy/_utils/__pycache__/__init__.cpython-310.pyc,,
numpy/_utils/__pycache__/_convertions.cpython-310.pyc,,
numpy/_utils/__pycache__/_inspect.cpython-310.pyc,,
numpy/_utils/__pycache__/_pep440.cpython-310.pyc,,
numpy/_utils/_convertions.py,sha256=vetZFqC1qB-Z9jvc7RKuU_5ETOaSbjhbKa-sVwYV8TU,347
numpy/_utils/_inspect.py,sha256=4PWDVD-iE3lZGrBCWdiLMn2oSytssuFszubUkC0oruA,7638
numpy/_utils/_pep440.py,sha256=y5Oppq3Kxn2dH3EWBYSENv_j8XjGUXWvNAiNCEJ-euI,14556
numpy/char/__init__.py,sha256=oQZSAOs7rHme6CxfdL9nraYRNI3NU18MjzQ4kQmK2kA,95
numpy/char/__init__.pyi,sha256=35gZ99TqbCpNzRbyLly_EiY0pZVvEifRILEj59d7wdU,1389
numpy/char/__pycache__/__init__.cpython-310.pyc,,
numpy/compat/__init__.py,sha256=oqsQeYKpQuJpuTLqMkZX6ssqQfSXs0Joj_S8Ms9KSNU,756
numpy/compat/__pycache__/__init__.cpython-310.pyc,,
numpy/compat/__pycache__/py3k.cpython-310.pyc,,
numpy/compat/py3k.py,sha256=w5IMIIE6YlP3sQzhe-rCPC92moA6gsezl37nHq_Aq1E,3978
numpy/compat/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/compat/tests/__pycache__/__init__.cpython-310.pyc,,
numpy/conftest.py,sha256=u6dpRU3Ic9fneMShPGzKAYY_XfibNZIFz4LvhP60XPI,8649
numpy/core/__init__.py,sha256=_lpcaIqNg3TH53JE0JKVKD4X0DOTki2dSvQgjHj6Eek,1307
numpy/core/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/core/__pycache__/__init__.cpython-310.pyc,,
numpy/core/__pycache__/_dtype.cpython-310.pyc,,
numpy/core/__pycache__/_dtype_ctypes.cpython-310.pyc,,
numpy/core/__pycache__/_internal.cpython-310.pyc,,
numpy/core/__pycache__/_multiarray_umath.cpython-310.pyc,,
numpy/core/__pycache__/_utils.cpython-310.pyc,,
numpy/core/__pycache__/arrayprint.cpython-310.pyc,,
numpy/core/__pycache__/defchararray.cpython-310.pyc,,
numpy/core/__pycache__/einsumfunc.cpython-310.pyc,,
numpy/core/__pycache__/fromnumeric.cpython-310.pyc,,
numpy/core/__pycache__/function_base.cpython-310.pyc,,
numpy/core/__pycache__/getlimits.cpython-310.pyc,,
numpy/core/__pycache__/multiarray.cpython-310.pyc,,
numpy/core/__pycache__/numeric.cpython-310.pyc,,
numpy/core/__pycache__/numerictypes.cpython-310.pyc,,
numpy/core/__pycache__/overrides.cpython-310.pyc,,
numpy/core/__pycache__/records.cpython-310.pyc,,
numpy/core/__pycache__/shape_base.cpython-310.pyc,,
numpy/core/__pycache__/umath.cpython-310.pyc,,
numpy/core/_dtype.py,sha256=PcSCn7DCpgrvBjm-k4eCMcEiTnH-jPzQmh8FyzLVw9I,331
numpy/core/_dtype_ctypes.py,sha256=eiZNKCJbzZ1Ei9Tkd7Fffx8vWUsAKnFSK-5vza3vmEQ,359
numpy/core/_internal.py,sha256=HC1NrqDEgK-6M1M6-8ZTZSZF7xnIYPh_G_4j2BFBNLM,972
numpy/core/_multiarray_umath.py,sha256=vO49_4x5SYg-BST541l73RmBm7pkqbwlssmwsRSdU80,2151
numpy/core/_utils.py,sha256=P8VAr5WvU7HBBoDG7rnLk4Vr5_hFIrCLbZHZF47z4uU,938
numpy/core/arrayprint.py,sha256=qo9GIfdEmW9foxvP0vtFLRaAlSbOoOGJU-hBlQ5hIlA,347
numpy/core/defchararray.py,sha256=-gCjc9ciILhSzAxtVXgiTwdpuNMD3R6p9tXHe_MLx9A,355
numpy/core/einsumfunc.py,sha256=LkCSjRQ3HIF4fdRz7uEgl-1TyeT0gtGV5y8x9cQYsZ0,347
numpy/core/fromnumeric.py,sha256=iQsih718r6QW80auPJbva99qeWfT5IK2S02sv4AFMUs,351
numpy/core/function_base.py,sha256=V_-tUGZfgjYzjZxvhLNRtVXV2_v12rJsvAGpDXbfq8w,359
numpy/core/getlimits.py,sha256=SQsTlDpDVz9AvFC-xvAJbhcm5svBD02qpE-HLgt17RA,343
numpy/core/multiarray.py,sha256=2K7g3jXbH7wqupSsyr5wP0YoQSpXlZab9uDDbJtz2Bk,816
numpy/core/numeric.py,sha256=nTvwcwAqkzCnYmqEt4J3dvqUodzXUlaI8H5YF5x65xg,370
numpy/core/numerictypes.py,sha256=jmQ9c1WrWxlx8ODDZKOAqrixUu3Gx_NJD1SzT3wtb50,355
numpy/core/overrides.py,sha256=Dq-lTb829gvg-HfRtY0BE6GE2UbI6iXkMIh8Gvkzt1g,343
numpy/core/records.py,sha256=5jPtgEtHaJ642Ct-G9uEwnF9y_TZnZAUXm_EUJEF8J8,335
numpy/core/shape_base.py,sha256=itirz4hN3M8Ndgij4_ZVcra4qtRkK42Owp8qr9fFe5w,347
numpy/core/umath.py,sha256=09uNybUqfWxdqkoYHzv6jrTDCXq6DDI-EdwaOKdijn4,327
numpy/ctypeslib.py,sha256=PtyT1GJXzd3RGDbXZmNB0yheHC8cFVhxUAnIWMn4wrE,18187
numpy/ctypeslib.pyi,sha256=gk5KMbxCI0mPh-4aXWKatmZ2VYbQIhLf5g91B6nrUlU,8305
numpy/distutils/__init__.py,sha256=sh1TV9_aW0YWvmHfBPtbZKCRcZTN6BnxKV-mIAG2vuY,2138
numpy/distutils/__init__.pyi,sha256=6KiQIH85pUXaIlow3KW06e1_ZJBocVY6lIGghNaW33A,123
numpy/distutils/__pycache__/__init__.cpython-310.pyc,,
numpy/distutils/__pycache__/_shell_utils.cpython-310.pyc,,
numpy/distutils/__pycache__/armccompiler.cpython-310.pyc,,
numpy/distutils/__pycache__/ccompiler.cpython-310.pyc,,
numpy/distutils/__pycache__/ccompiler_opt.cpython-310.pyc,,
numpy/distutils/__pycache__/conv_template.cpython-310.pyc,,
numpy/distutils/__pycache__/conv_template.cpython-310.pyc,sha256=KadnbFYi4tqsTRp9_-twGNQs7guXounEqJfzzFjeOrY,8278
numpy/distutils/__pycache__/core.cpython-310.pyc,,
numpy/distutils/__pycache__/cpuinfo.cpython-310.pyc,,
numpy/distutils/__pycache__/exec_command.cpython-310.pyc,,
numpy/distutils/__pycache__/extension.cpython-310.pyc,,
numpy/distutils/__pycache__/from_template.cpython-310.pyc,,
numpy/distutils/__pycache__/fujitsuccompiler.cpython-310.pyc,,
numpy/distutils/__pycache__/intelccompiler.cpython-310.pyc,,
numpy/distutils/__pycache__/lib2def.cpython-310.pyc,,
numpy/distutils/__pycache__/line_endings.cpython-310.pyc,,
numpy/distutils/__pycache__/log.cpython-310.pyc,,
numpy/distutils/__pycache__/mingw32ccompiler.cpython-310.pyc,,
numpy/distutils/__pycache__/misc_util.cpython-310.pyc,,
numpy/distutils/__pycache__/msvc9compiler.cpython-310.pyc,,
numpy/distutils/__pycache__/msvccompiler.cpython-310.pyc,,
numpy/distutils/__pycache__/npy_pkg_config.cpython-310.pyc,,
numpy/distutils/__pycache__/numpy_distribution.cpython-310.pyc,,
numpy/distutils/__pycache__/pathccompiler.cpython-310.pyc,,
numpy/distutils/__pycache__/system_info.cpython-310.pyc,,
numpy/distutils/__pycache__/unixccompiler.cpython-310.pyc,,
numpy/distutils/_shell_utils.py,sha256=TDc8sp986sdmW06JwOaIaN5XVqG2t4HEfs8SdCpwU50,2625
numpy/distutils/armccompiler.py,sha256=6sKNp543q_4NafErHoFOPKz8R3YJR9soDCr1WeFr5Xk,988
numpy/distutils/ccompiler.py,sha256=TFzGS6MmE2JSChohLSvJ955mtV1339u7gfFar1O4seI,29516
numpy/distutils/ccompiler_opt.py,sha256=6lKyYwOGGBNYjzSznBwnTyW4fBAfwlFw2nSkSvPPozI,103064
numpy/distutils/checks/cpu_asimd.c,sha256=Nit4NvYvo3XWtBKeV6rmIszdNLu9AY81sqMFCTkKXBE,845
numpy/distutils/checks/cpu_asimddp.c,sha256=bQP32IzQZANu9aFu3qkovLYJXKCm0bJ6srsO5Ho2GKI,448
numpy/distutils/checks/cpu_asimdfhm.c,sha256=xJjmEakgtmK9zlx2fIT6UZ4eZreLzdCoOVkkGPyzXFA,548
numpy/distutils/checks/cpu_asimdhp.c,sha256=0eTZ2E1Gyk3G5XfkpSN32yI9AC3SUwwFetyAOtEp5u4,394
numpy/distutils/checks/cpu_avx.c,sha256=69aCE28EArV-BmdFKhCA5djgNZAZtQg2zdea3VQD-co,799
numpy/distutils/checks/cpu_avx2.c,sha256=207hFoh4ojzMAPQ53ug_Y5qCFIgZ1e8SdI1-o2jzdB4,769
numpy/distutils/checks/cpu_avx512_clx.c,sha256=CfPjudkRZ9_xygLVOySSEjoAfkjjfu4ipkWK4uCahbU,864
numpy/distutils/checks/cpu_avx512_cnl.c,sha256=eKCPRk6p1B0bPAyOY0oWRKZMfa-c5g-skvJGGlG5I4Y,972
numpy/distutils/checks/cpu_avx512_icl.c,sha256=Zt8XOXZL85Ds5HvZlAwUVilT6mGbPU44Iir44ul6y2Y,1030
numpy/distutils/checks/cpu_avx512_knl.c,sha256=0itGNg9s9gFjsj79qQvsZR-xceTTcpw4qa0OOAmq_Sg,984
numpy/distutils/checks/cpu_avx512_knm.c,sha256=iVdJnZ5HY59XhUv4GzwqYRwz2E_jWJnk1uSz97MvxY0,1162
numpy/distutils/checks/cpu_avx512_skx.c,sha256=aOHpYdGPEx2FcnC7TKe9Nr7wQ0QWW20Uq3xRVSb4U90,1036
numpy/distutils/checks/cpu_avx512_spr.c,sha256=ziSmzNQZ_k3j5FrAWSKfAAW_g3l8tq8t6InVPWEUx9Y,930
numpy/distutils/checks/cpu_avx512cd.c,sha256=zIl7AJXfxqnquZyHQvUAGr9M-vt62TIlylhdlrg-qkE,779
numpy/distutils/checks/cpu_avx512f.c,sha256=ibW0zon6XGYkdfnYETuPfREmE5OtO0HfuLTqXMsoqNA,775
numpy/distutils/checks/cpu_f16c.c,sha256=QxxI3vimUAkJ4eJ83va2mZzTJOk3yROI05fVY07H5To,890
numpy/distutils/checks/cpu_fma3.c,sha256=Cq0F_UpVJ4SYHcxXfaYoqHSYvWRJzZsB8IkOVl8K2ro,839
numpy/distutils/checks/cpu_fma4.c,sha256=Xy0YfVpQDCiFOOrCWH-RMkv7ms5ZAbSauwm2xEOT94o,314
numpy/distutils/checks/cpu_neon.c,sha256=I-R8DHE6JfzqmPpaF4NTdWxq5hEW-lJZPjSjW8ynFgo,619
numpy/distutils/checks/cpu_neon_fp16.c,sha256=6hdykX7cRL3ruejgK3bf_IXGQWol8OUITPEjvbz_1Hc,262
numpy/distutils/checks/cpu_neon_vfpv4.c,sha256=IY4cT03GTrzEZKLd7UInKtYC0DlgugFGGrkSTfwwvmU,630
numpy/distutils/checks/cpu_popcnt.c,sha256=Jkslm5DiuxbI-fBcCIgJjxjidm-Ps_yfAb_jJIZonE8,1081
numpy/distutils/checks/cpu_rvv.c,sha256=hXM8c3JEjDRSf1vn3IWG0VSuno7QLrUlTegVvXXpYG4,313
numpy/distutils/checks/cpu_sse.c,sha256=XitLZu_qxXDINNpbfcUAL7iduT1I63HjNgtyE72SCEo,706
numpy/distutils/checks/cpu_sse2.c,sha256=OJpQzshqCS6Cp9X1I1yqh2ZPa0b2AoSmJn6HdApOzYk,717
numpy/distutils/checks/cpu_sse3.c,sha256=AmZkvTpXcoCAfVckXgvwloutI5CTHkwHJD86pYsntgk,709
numpy/distutils/checks/cpu_sse41.c,sha256=5GvpgxPcDL39iydUjKyS6WczOiXTs14KeXvlWVOr6LQ,695
numpy/distutils/checks/cpu_sse42.c,sha256=8eYzhquuXjRRGp3isTX0cNUV3pXATEPc-J-CDYTgTaU,712
numpy/distutils/checks/cpu_ssse3.c,sha256=QXWKRz5fGQv5bn282bJL4h_92-yqHFG_Gp5uLKvcA34,725
numpy/distutils/checks/cpu_sve.c,sha256=QgBJTJ_cTDz85ZLSMU7cQbpaiv8Bwb6Ma1HfCoX3l5c,301
numpy/distutils/checks/cpu_vsx.c,sha256=gxWpdnkMeoaBCzlU_j56brB38KFo4ItFsjyiyo3YrKk,499
numpy/distutils/checks/cpu_vsx2.c,sha256=ycKoKXszrZkECYmonzKd7TgflpZyVc1Xq-gtJqyPKxs,276
numpy/distutils/checks/cpu_vsx3.c,sha256=pNA4w2odwo-mUfSnKnXl5SVY1z2nOxPZZcNC-L2YX1w,263
numpy/distutils/checks/cpu_vsx4.c,sha256=SROYYjVVc8gPlM4ERO--9Dk2MzvAecZzJxGKO_RTvPM,319
numpy/distutils/checks/cpu_vx.c,sha256=v1UZMj78POCN7sbFmW6N0GM_qQSUwHxiF15LQYADIUs,477
numpy/distutils/checks/cpu_vxe.c,sha256=1w8AvS6x8s_zTgcrDEGMKQmSqpJRX2NLprdSu_ibyjk,813
numpy/distutils/checks/cpu_vxe2.c,sha256=fY9P2fWo-b08dy4dmuNNc_xX3E0ruPRU9zLPzzgD-Z8,645
numpy/distutils/checks/cpu_xop.c,sha256=sPhOvyT-mdlbf6RlbZvMrslRwHnTFgP-HXLjueS7nwU,246
numpy/distutils/checks/extra_avx512bw_mask.c,sha256=7IRO24mpcuXRhm3refGWP91sy0e6RmSkmUQCWyxy__0,654
numpy/distutils/checks/extra_avx512dq_mask.c,sha256=jFtOKEtZl3iTpfbmFNB-u4DQNXXBST2toKCpxFIjEa0,520
numpy/distutils/checks/extra_avx512f_reduce.c,sha256=hIcCLMm_aXPfrhzCsoFdQiryIrntPqfDxz0tNOR985w,1636
numpy/distutils/checks/extra_vsx3_half_double.c,sha256=GU-E6yQLdzmOdvO06D0KCkvU4YHyuwFvyydirU_1Clk,366
numpy/distutils/checks/extra_vsx4_mma.c,sha256=-Pz_qQ55WfWmTWGTH0hvKrFTU2S2kjsVBfIK3w5sciE,520
numpy/distutils/checks/extra_vsx_asm.c,sha256=anSZskhKZImNk0lsSJJY_8GJQ0h3dDrkrmrGitlS7Fw,981
numpy/distutils/checks/test_flags.c,sha256=7rgVefVOKOBaefG_6riau_tT2IqI4MFrbSMGNFnqUBQ,17
numpy/distutils/command/__init__.py,sha256=DCxnKqTLrauOD3Fc8b7qg9U3gV2k9SADevE_Q3H78ng,1073
numpy/distutils/command/__pycache__/__init__.cpython-310.pyc,,
numpy/distutils/command/__pycache__/autodist.cpython-310.pyc,,
numpy/distutils/command/__pycache__/bdist_rpm.cpython-310.pyc,,
numpy/distutils/command/__pycache__/build.cpython-310.pyc,,
numpy/distutils/command/__pycache__/build_clib.cpython-310.pyc,,
numpy/distutils/command/__pycache__/build_ext.cpython-310.pyc,,
numpy/distutils/command/__pycache__/build_py.cpython-310.pyc,,
numpy/distutils/command/__pycache__/build_scripts.cpython-310.pyc,,
numpy/distutils/command/__pycache__/build_src.cpython-310.pyc,,
numpy/distutils/command/__pycache__/config.cpython-310.pyc,,
numpy/distutils/command/__pycache__/config_compiler.cpython-310.pyc,,
numpy/distutils/command/__pycache__/develop.cpython-310.pyc,,
numpy/distutils/command/__pycache__/egg_info.cpython-310.pyc,,
numpy/distutils/command/__pycache__/install.cpython-310.pyc,,
numpy/distutils/command/__pycache__/install_clib.cpython-310.pyc,,
numpy/distutils/command/__pycache__/install_data.cpython-310.pyc,,
numpy/distutils/command/__pycache__/install_headers.cpython-310.pyc,,
numpy/distutils/command/__pycache__/sdist.cpython-310.pyc,,
numpy/distutils/command/autodist.py,sha256=i2ip0Zru8_AFx3lNQhlZfj6o_vg-RQ8yu1WNstcIYhE,3866
numpy/distutils/command/bdist_rpm.py,sha256=9uZfOzdHV0_PRUD8exNNwafc0qUqUjHuTDxQcZXLIbg,731
numpy/distutils/command/build.py,sha256=6IbYgycGcCRrrWENUBqzAEhgtUhCGLnXNVnTCu3hxWc,2675
numpy/distutils/command/build_clib.py,sha256=x8CjWbraTjai7wdSwq16VBWMQw5w20BmCj_iHdzDc14,19786
numpy/distutils/command/build_ext.py,sha256=XfbdWZdqQKwqibpb8VT2ODlrcftrigfFVneLl97P3Zk,33735
numpy/distutils/command/build_py.py,sha256=xBHZCtx91GqucanjIBETPeXmR-gyUKPDyr1iMx1ARWE,1175
numpy/distutils/command/build_scripts.py,sha256=AEQLNmO2v5N-GXl4lwd8v_nHlrauBx9Y-UudDcdCs_A,1714
numpy/distutils/command/build_src.py,sha256=njEPAEftbBAQ8K6uARjA1N_CkbCDwlB59p3wue5IfZg,31951
numpy/distutils/command/config.py,sha256=IBU66VZXvuPfEYxMXImJpG8b0HW1UDlNBoLVrLyKLDA,21186
numpy/distutils/command/config_compiler.py,sha256=I-xAL3JxaGFfpR4lg7g0tDdA_t7zCt-D4JtOACCP_Ak,4495
numpy/distutils/command/develop.py,sha256=5ro-Sudt8l58JpKvH9FauH6vIfYRv2ohHLz-9eHytbc,590
numpy/distutils/command/egg_info.py,sha256=n6trbjRfD1qWc_hRtMFkOJsg82BCiLvdl-NeXyuceGc,946
numpy/distutils/command/install.py,sha256=iK5ls63o6WqVOreU-mG5HZSkx90qYhMQvlo2FaaQWWg,3152
numpy/distutils/command/install_clib.py,sha256=q3yrfJY9EBaxOIYUQoiu2-juNKLKAKKfXC0nrd4t6z0,1439
numpy/distutils/command/install_data.py,sha256=r8EVbIaXyN3aOmRugT3kp_F4Z03PsVX2l_x4RjTOWU4,872
numpy/distutils/command/install_headers.py,sha256=HZo3To_7tpls2ZomDnaxdP32oSUVQsFeCjbD8jDZXFY,945
numpy/distutils/command/sdist.py,sha256=XQM39b-MMO08bfE3SJrrtDWwX0XVnzCZqfAoVuuaFuE,760
numpy/distutils/conv_template.py,sha256=hL0DDy7tMJ-5I-63BmkWkoLNX2c5GiQdQhj-XNG3Tm8,9865
numpy/distutils/core.py,sha256=4vvNzpLy_9AfakXgzC6OITRThJd4OdfSmrzxhYu49Fc,8388
numpy/distutils/cpuinfo.py,sha256=l5G7myXNwEOTynBIEitH-ghaF8Zw5pHQAjaYpPKNtTQ,23322
numpy/distutils/exec_command.py,sha256=ZnPon3CxIP1kCznPhTyPnCSOLS7sXAot4TeTPcqVQdw,10598
numpy/distutils/extension.py,sha256=U4vHJeem4kWsK_5KUnmp1qCG0qO6PI5yQjchUvHnwlw,3561
numpy/distutils/fcompiler/__init__.py,sha256=UncOSqwlhHdNNSViIibqy51Prrkd589e1C06sTtnYww,41660
numpy/distutils/fcompiler/__pycache__/__init__.cpython-310.pyc,,
numpy/distutils/fcompiler/__pycache__/absoft.cpython-310.pyc,,
numpy/distutils/fcompiler/__pycache__/arm.cpython-310.pyc,,
numpy/distutils/fcompiler/__pycache__/compaq.cpython-310.pyc,,
numpy/distutils/fcompiler/__pycache__/environment.cpython-310.pyc,,
numpy/distutils/fcompiler/__pycache__/fujitsu.cpython-310.pyc,,
numpy/distutils/fcompiler/__pycache__/g95.cpython-310.pyc,,
numpy/distutils/fcompiler/__pycache__/gnu.cpython-310.pyc,,
numpy/distutils/fcompiler/__pycache__/hpux.cpython-310.pyc,,
numpy/distutils/fcompiler/__pycache__/ibm.cpython-310.pyc,,
numpy/distutils/fcompiler/__pycache__/intel.cpython-310.pyc,,
numpy/distutils/fcompiler/__pycache__/lahey.cpython-310.pyc,,
numpy/distutils/fcompiler/__pycache__/mips.cpython-310.pyc,,
numpy/distutils/fcompiler/__pycache__/nag.cpython-310.pyc,,
numpy/distutils/fcompiler/__pycache__/none.cpython-310.pyc,,
numpy/distutils/fcompiler/__pycache__/nv.cpython-310.pyc,,
numpy/distutils/fcompiler/__pycache__/pathf95.cpython-310.pyc,,
numpy/distutils/fcompiler/__pycache__/pg.cpython-310.pyc,,
numpy/distutils/fcompiler/__pycache__/sun.cpython-310.pyc,,
numpy/distutils/fcompiler/__pycache__/vast.cpython-310.pyc,,
numpy/distutils/fcompiler/absoft.py,sha256=J5Nn8PXD0VNUjCI5Vj6PD8JRS6Dxi5Zz5LCa8fkPZIY,5672
numpy/distutils/fcompiler/arm.py,sha256=Bpftt3HnmJc3Iyt8-nwsNv86JqdFYK0JMwh3CC8nP_k,2161
numpy/distutils/fcompiler/compaq.py,sha256=yyReqFAq42dy1zscMAV0GqVaYW7Iao1HtAUpnv5XTec,4023
numpy/distutils/fcompiler/environment.py,sha256=PVS1al3wahDNnneNVSl1sQhMPfz2dUXaIDVJfy0wZBU,3168
numpy/distutils/fcompiler/fujitsu.py,sha256=g4dTLDFfLRAzhYayIwyHGBw1Y36DKtPOCYfA823ldNA,1379
numpy/distutils/fcompiler/g95.py,sha256=1TJe4IynWYqqYBy8gJ-nz8WQ_TaSbv8k2UzUIY5Erqc,1372
numpy/distutils/fcompiler/gnu.py,sha256=6V_Ly_lwEEsfUDSz0vCDg86EhWlajHuyBy_ioLqKCdM,21057
numpy/distutils/fcompiler/hpux.py,sha256=SLbDOPYgiixqE32GgUrAJjpDLFy9g7E01vGNZCGv6Pc,1394
numpy/distutils/fcompiler/ibm.py,sha256=P8NMedMGxlCvVRoVIj4GKF65IP1TUe7jmlt-1KscVYo,3631
numpy/distutils/fcompiler/intel.py,sha256=rlm017cVcyjIy1_s8a4lNHJ8ilo6TiYcIA_tuPojapY,6781
numpy/distutils/fcompiler/lahey.py,sha256=EV3Zhwq-iowWAu4BFBPv_UGJ-IB-qxlxmi6WU1qHDOs,1372
numpy/distutils/fcompiler/mips.py,sha256=mlUNgGrRSLnNhtxQXWVfC9l4_OP2GMvOkgbZQwBon0A,1768
numpy/distutils/fcompiler/nag.py,sha256=FpoDQWW_Y3Anm9-Psml-eNySCGzCp9_jP2Ej4_AwDy8,2864
numpy/distutils/fcompiler/none.py,sha256=auMK2ou1WtJ20LeMbwCZJ3XofpT9A0YYbMVd-62Mi_E,786
numpy/distutils/fcompiler/nv.py,sha256=40IYfxm5ppkYtSaX8seMg9NGynvXrZFkcLDonxbKfW4,1594
numpy/distutils/fcompiler/pathf95.py,sha256=ipbaZIO8sqPJ1lUppOurnboiTwRzIasWNAJvKmktvv4,1094
numpy/distutils/fcompiler/pg.py,sha256=cVcSFM9oR0KmO5AIb4Odw9OGslW6zvDGP88n-uEwxvQ,3696
numpy/distutils/fcompiler/sun.py,sha256=JMdFfKldTYlfW1DxV7nR09k5PZypKLWpP7wmQzmlnH0,1628
numpy/distutils/fcompiler/vast.py,sha256=JUGP68JGOUOBS9WbXftE-qCVUD13fpLyPnhpHfTL5y0,1719
numpy/distutils/from_template.py,sha256=BL-vypfI0GNJrTo-nKs445liTW2Qdfvrsu8RMjATL5A,8174
numpy/distutils/fujitsuccompiler.py,sha256=JWVPhI1oH4v2iKzDP8VjcnJIKYXZFYcYCwdpDxhURvw,862
numpy/distutils/intelccompiler.py,sha256=77BDCj7_6Nnf92ZDeFQgA6aDKJGkzDQ7u0nuQGw1v8g,4345
numpy/distutils/lib2def.py,sha256=KnWZJaOsxmx57MEJxrsdPAlZbQBgu-27bSCjwO8cI6k,3746
numpy/distutils/line_endings.py,sha256=hlI71r840mhfu8lmzdHPVZ4NFm-kJDDUMV3lETblVTY,2109
numpy/distutils/log.py,sha256=a5-sPwcZei7kSP0ZQZH4tTrlRWHnL8jtzLCeUSPA_04,2990
numpy/distutils/mingw/gfortran_vs2003_hack.c,sha256=FDTA53KYTIhil9ytvZlocOqghQVp9LacLHn1IurV0wI,83
numpy/distutils/mingw32ccompiler.py,sha256=9zhqZ8mJU0j0j4nciS3tKp2-ESsYe05SknfnadX8rwQ,22749
numpy/distutils/misc_util.py,sha256=re8nNlckODPh9fFvDJflT-UeOV594mgRSX-IANr1I_o,91723
numpy/distutils/msvc9compiler.py,sha256=bCtCVJmGrBHPm9sOoxa3oSrdrEVCNQFEM5O5hdqX8Hc,2255
numpy/distutils/msvccompiler.py,sha256=gqQySO-P6Egk3qgrNlyCF3ze_U47lIO9SrbFJrCQCO8,2723
numpy/distutils/npy_pkg_config.py,sha256=t2-OG_QrnZEeQsagpJF4sLN9C7RMlnWGOW4K88wEvx0,13459
numpy/distutils/numpy_distribution.py,sha256=nrdp8rlyjEBBV1tzzi5cE-aYeXB5U3X8T5-G0akXSoY,651
numpy/distutils/pathccompiler.py,sha256=a5CYDXilCaIC85v0fVh-wrb0fClv0A7mPS87aF1inUc,734
numpy/distutils/system_info.py,sha256=xBGUYCCgaXykWWsTsOZNgi7vluEw9fTBq1L19JN6OwY,117195
numpy/distutils/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/distutils/tests/__pycache__/__init__.cpython-310.pyc,,
numpy/distutils/tests/__pycache__/test_build_ext.cpython-310.pyc,,
numpy/distutils/tests/__pycache__/test_ccompiler_opt.cpython-310.pyc,,
numpy/distutils/tests/__pycache__/test_ccompiler_opt_conf.cpython-310.pyc,,
numpy/distutils/tests/__pycache__/test_exec_command.cpython-310.pyc,,
numpy/distutils/tests/__pycache__/test_fcompiler.cpython-310.pyc,,
numpy/distutils/tests/__pycache__/test_fcompiler_gnu.cpython-310.pyc,,
numpy/distutils/tests/__pycache__/test_fcompiler_intel.cpython-310.pyc,,
numpy/distutils/tests/__pycache__/test_fcompiler_nagfor.cpython-310.pyc,,
numpy/distutils/tests/__pycache__/test_from_template.cpython-310.pyc,,
numpy/distutils/tests/__pycache__/test_log.cpython-310.pyc,,
numpy/distutils/tests/__pycache__/test_mingw32ccompiler.cpython-310.pyc,,
numpy/distutils/tests/__pycache__/test_misc_util.cpython-310.pyc,,
numpy/distutils/tests/__pycache__/test_npy_pkg_config.cpython-310.pyc,,
numpy/distutils/tests/__pycache__/test_shell_utils.cpython-310.pyc,,
numpy/distutils/tests/__pycache__/test_system_info.cpython-310.pyc,,
numpy/distutils/tests/__pycache__/utilities.cpython-310.pyc,,
numpy/distutils/tests/test_build_ext.py,sha256=-hdco5_La1AucFJgjYnvk_zFP1eKUs_xz0X8iwuW1RA,2853
numpy/distutils/tests/test_ccompiler_opt.py,sha256=YAR76iKLsRIpRfS2XmKunsyHaiDzyGK-T47oNI7WmyE,29586
numpy/distutils/tests/test_ccompiler_opt_conf.py,sha256=3KyqLepj3nC2C1UYm8nv1Ne5O6KtufD-7DlvAYJuvOo,6523
numpy/distutils/tests/test_exec_command.py,sha256=EVipBhoXEJjlSwtQRptWJC1LNJc6wfYzu_81V2jdAL8,7612
numpy/distutils/tests/test_fcompiler.py,sha256=SS5HOLIg0eqkmZTRKeWq9_ahW2tmV9c9piwYfzcBPmc,1320
numpy/distutils/tests/test_fcompiler_gnu.py,sha256=RlRHZbyazgKGY17NmdYSF3ehO0M0xXN4UkbsJzJz4i8,2191
numpy/distutils/tests/test_fcompiler_intel.py,sha256=4cppjLugoa8P4bjzYdiPxmyCywmP9plXOkfsklhnYsQ,1088
numpy/distutils/tests/test_fcompiler_nagfor.py,sha256=ntyr8f-67dNI0OF_l6-aeTwu9wW-vnxpheqrc4cXAUI,1124
numpy/distutils/tests/test_from_template.py,sha256=ZzUSEPyZIG4Zak3-TFqmRGXHMp58aKTuLKb0t-5XpDg,1147
numpy/distutils/tests/test_log.py,sha256=ylfdL0kBkbjj_Tgqx47UGykAtpE_mJkLndL40p11AYc,902
numpy/distutils/tests/test_mingw32ccompiler.py,sha256=7X8V4hLMtsNj1pYoLkSSla04gJu66e87E_k-6ce3PrA,1651
numpy/distutils/tests/test_misc_util.py,sha256=91koMzWbDZktEvkfdCByVHzViHHULmn0WRcA_D-YSjQ,3452
numpy/distutils/tests/test_npy_pkg_config.py,sha256=1pQh-mApHjj0y9Ba2tqns79U8dsfDpJ9zcPdsa2qbps,2641
numpy/distutils/tests/test_shell_utils.py,sha256=aKtyXpHEYARNsAq9q5SeVC0qqMfm1gzvlN6-nXOVlac,2193
numpy/distutils/tests/test_system_info.py,sha256=-j438GufVq6Vicimybm1XxndwwiXGKuYTEb78gfY5Ws,11739
numpy/distutils/tests/utilities.py,sha256=d49suMzR_1sAXU0OO5kD7msJfBtmvv7yZZCCWIxXKY4,2377
numpy/distutils/unixccompiler.py,sha256=ED_e7yHVNj4oXMze6KY8TbPxjyvHDC6o4VNGAkFA5ZQ,5567
numpy/doc/__pycache__/ufuncs.cpython-310.pyc,,
numpy/doc/ufuncs.py,sha256=jMnfQhRknVIhgFVS9z2l5oYM8N1tuQtf5bXMBL449oI,5552
numpy/dtypes.py,sha256=cPkS6BLRvpfsUzhd7Vk1L7_VcenWb1nuHuCxc9fYC4I,1353
numpy/dtypes.pyi,sha256=yWy0Pmb1GfngJEZo0bCrG8XamKncC-I6Qmhxvz6fw30,14647
numpy/exceptions.py,sha256=GRKePX6DhwV3I0XCFxWgTmB8vzPpRMgPQDIczZ-lbpw,8123
numpy/exceptions.pyi,sha256=9eJ_k4qPTUG1uJftx6D2Gofp4f9glsxuviHTuh_3LBA,658
numpy/f2py/__init__.py,sha256=HgdKKkkvXrSZaqTEA-np107u91Ofa2UKusd9RsK1czs,2614
numpy/f2py/__init__.pyi,sha256=Hca1LGxioFoPddmOcqwkexbH4E1TSw_UTzA4Xs8bxXY,1129
numpy/f2py/__main__.py,sha256=TDesy_2fDX-g27uJt4yXIXWzSor138R2t2V7HFHwqAk,135
numpy/f2py/__pycache__/__init__.cpython-310.pyc,,
numpy/f2py/__pycache__/__main__.cpython-310.pyc,,
numpy/f2py/__pycache__/__version__.cpython-310.pyc,,
numpy/f2py/__pycache__/_isocbind.cpython-310.pyc,,
numpy/f2py/__pycache__/_src_pyf.cpython-310.pyc,,
numpy/f2py/__pycache__/auxfuncs.cpython-310.pyc,,
numpy/f2py/__pycache__/capi_maps.cpython-310.pyc,,
numpy/f2py/__pycache__/cb_rules.cpython-310.pyc,,
numpy/f2py/__pycache__/cfuncs.cpython-310.pyc,,
numpy/f2py/__pycache__/common_rules.cpython-310.pyc,,
numpy/f2py/__pycache__/crackfortran.cpython-310.pyc,,
numpy/f2py/__pycache__/diagnose.cpython-310.pyc,,
numpy/f2py/__pycache__/f2py2e.cpython-310.pyc,,
numpy/f2py/__pycache__/f90mod_rules.cpython-310.pyc,,
numpy/f2py/__pycache__/func2subr.cpython-310.pyc,,
numpy/f2py/__pycache__/rules.cpython-310.pyc,,
numpy/f2py/__pycache__/symbolic.cpython-310.pyc,,
numpy/f2py/__pycache__/use_rules.cpython-310.pyc,,
numpy/f2py/__version__.py,sha256=TisKvgcg4vh5Fptw2GS1JB_3bAQsWZIKhclEX6ZcAho,35
numpy/f2py/_backends/__init__.py,sha256=xIVHiF-velkBDPKwFS20PSg-XkFW5kLAVj5CSqNLddM,308
numpy/f2py/_backends/__pycache__/__init__.cpython-310.pyc,,
numpy/f2py/_backends/__pycache__/_backend.cpython-310.pyc,,
numpy/f2py/_backends/__pycache__/_distutils.cpython-310.pyc,,
numpy/f2py/_backends/__pycache__/_meson.cpython-310.pyc,,
numpy/f2py/_backends/_backend.py,sha256=9RZDu4FCwCM7G39EX2YEt-Vnaz0U2WSp-QSAfz11BGE,1233
numpy/f2py/_backends/_distutils.py,sha256=CN_xltCz7-cIwBf6X6298EM_0m30TAKLBIfpDPSs8WA,2463
numpy/f2py/_backends/_meson.py,sha256=IK2wpu-6oUWKpgC4-PQXOTFUxaehG-bp8gpmtdMJZ1w,8350
numpy/f2py/_backends/meson.build.template,sha256=6XD3j-K5pc1P_icgUWkrgEsyludQWsqS5rb6UB29tH0,1654
numpy/f2py/_isocbind.py,sha256=QVoR_pD_bY9IgTaSHHUw_8EBg0mkaf3JZfwhLfHbz1Q,2422
numpy/f2py/_src_pyf.py,sha256=dmgZsLgl8vbN8C-VYCXxzamkjDJ4TDQbeL8--NJMeqQ,7893
numpy/f2py/auxfuncs.py,sha256=9gN2eHzsBnpWRb8M4CqUWyd1nLYRagvE1pyWNH00N0Y,27821
numpy/f2py/capi_maps.py,sha256=oHcPRrHKrz36n6vqznFH1w1lT4_jOOmk7AsHz_e8Knc,31448
numpy/f2py/cb_rules.py,sha256=hALemKsqa1qkTD2KqBcdGmRDhSTAuq1Z5ZsPlJjWdXw,25648
numpy/f2py/cfuncs.py,sha256=gdbRGV5xYjHYSmP-vaOkljUAT0P2zXeEvdXZHRM1Ih4,53670
numpy/f2py/common_rules.py,sha256=19VDEPQ9-Pzzknv03U23gWYesmDAzJrGxwdXqn7CxhQ,5277
numpy/f2py/crackfortran.py,sha256=qburIiF488pikSZu1nU1SezeMpoHQIxuP3C7fiRiiMc,151778
numpy/f2py/diagnose.py,sha256=-t3VpQqke6qEjxpIrV1OA3VFuQRANimc0irdjGyO8RA,5351
numpy/f2py/f2py2e.py,sha256=wtRVdzeeR5wtUSfryoAtyJ4AP1XY8hMExAb4aq3VNjI,29728
numpy/f2py/f90mod_rules.py,sha256=88bpV4FFu1l-mNhoY-b6q3MsQUbzMaZMQpci0X2KwmE,10264
numpy/f2py/func2subr.py,sha256=Wro0C3NGSO-1g2zxBI8qg_Tl6KyczrCtCTJvhN4KtUQ,10621
numpy/f2py/rules.py,sha256=AE9mZxYZfJT90m94Z-mNnx768Reugkb06LChQKdGS-Y,64447
numpy/f2py/setup.cfg,sha256=828sy3JvJmMzVxLkC-y0lxcEMaDTnMc3l9dWqP4jYng,50
numpy/f2py/src/fortranobject.c,sha256=X1nYkFfbN73vjvWBoDnXj43g_TT7CU1PIET9VdfFwCM,47471
numpy/f2py/src/fortranobject.h,sha256=uCcHO8mjuANlKb3c7YAZwM4pgT0CTaXWLYqgE27Mnt0,5996
numpy/f2py/symbolic.py,sha256=KfSdRlEIq6Ihou51oGp5khpJH_YUdwLu8PaxFkKKQv4,54785
numpy/f2py/tests/__init__.py,sha256=hiQX1lvI7rIYRNecVpg5D_0N6E0w94BSmexhEErutmI,343
numpy/f2py/tests/__pycache__/__init__.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_abstract_interface.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_array_from_pyobj.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_assumed_shape.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_block_docstring.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_callback.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_character.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_common.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_crackfortran.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_data.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_docs.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_f2cmap.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_f2py2e.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_isoc.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_kind.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_mixed.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_modules.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_parameter.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_pyf_src.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_quoted_character.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_regression.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_return_character.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_return_complex.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_return_integer.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_return_logical.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_return_real.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_semicolon_split.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_size.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_string.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_symbolic.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/test_value_attrspec.cpython-310.pyc,,
numpy/f2py/tests/__pycache__/util.cpython-310.pyc,,
numpy/f2py/tests/src/abstract_interface/foo.f90,sha256=aCaFEqfXp79pVXnTFtjZBWUY_5pu8wsehp1dEauOkSE,692
numpy/f2py/tests/src/abstract_interface/gh18403_mod.f90,sha256=y3R2dDn0BUz-0bMggfT1jwXbhz_gniz7ONMpureEQew,111
numpy/f2py/tests/src/array_from_pyobj/wrapmodule.c,sha256=0UkctY5oeFs9B9qnX8qhe3wTFZA_mF-FBBkJoy_iuQg,7713
numpy/f2py/tests/src/assumed_shape/.f2py_f2cmap,sha256=zfuOShmuotzcLIQDnVFaARwvM66iLrOYzpquIGDbiKU,30
numpy/f2py/tests/src/assumed_shape/foo_free.f90,sha256=fqbSr7VlKfVrBulFgQtQA9fQf0mQvVbLi94e4FTST3k,494
numpy/f2py/tests/src/assumed_shape/foo_mod.f90,sha256=9pbi88-uSNP5IwS49Kim982jDAuopo3tpEhg2SOU7no,540
numpy/f2py/tests/src/assumed_shape/foo_use.f90,sha256=9Cl1sdrihB8cCSsjoQGmOO8VRv9ni8Fjr0Aku1UdEWM,288
numpy/f2py/tests/src/assumed_shape/precision.f90,sha256=3L_F7n5ju9F0nxw95uBUaPeuiDOw6uHvB580eIj7bqI,134
numpy/f2py/tests/src/block_docstring/foo.f,sha256=KVTeqSFpI94ibYIVvUW6lOQ9T2Bx5UzZEayP8Maf2H0,103
numpy/f2py/tests/src/callback/foo.f,sha256=rLqaaaUpWFTaGVxNoGERtDKGCa5dLCTW5DglsFIx-wU,1316
numpy/f2py/tests/src/callback/gh17797.f90,sha256=-_NvQK0MzlSR72PSuUE1FeUzzsMBUcPKsbraHIF7O24,155
numpy/f2py/tests/src/callback/gh18335.f90,sha256=n_Rr99cI7iHBEPV3KGLEt0QKZtItEUKDdQkBt0GKKy4,523
numpy/f2py/tests/src/callback/gh25211.f,sha256=ejY_ssadbZQfD5_-Xnx_ayzWXWLjkdy7DGp6C_uCUCY,189
numpy/f2py/tests/src/callback/gh25211.pyf,sha256=nrzvt2QHZRCcugg0R-4FDMMl1MJmWCOAjR7Ta-pXz7Y,465
numpy/f2py/tests/src/cli/gh_22819.pyf,sha256=e3zYjFmiOxzdXoxzgkaQ-CV6sZ1t4aKugyhqRXmBNdQ,148
numpy/f2py/tests/src/cli/hi77.f,sha256=bgBERF4EYxHlzJCvZCJOlEmUE1FIvipdmj4LjdmL_dE,74
numpy/f2py/tests/src/cli/hiworld.f90,sha256=RncaEqGWmsH9Z8BMV-UmOTUyo3-e9xOQGAmNgDv6SfY,54
numpy/f2py/tests/src/common/block.f,sha256=tcGKa42S-6bfA6fybpM0Su_xjysEVustkEJoF51o_pE,235
numpy/f2py/tests/src/common/gh19161.f90,sha256=Vpb34lRVC96STWaJerqkDQeZf7mDOwWbud6pW62Tvm4,203
numpy/f2py/tests/src/crackfortran/accesstype.f90,sha256=3ONHb4ZNx0XISvp8fArnUwR1W9rzetLFILTiETPUd80,221
numpy/f2py/tests/src/crackfortran/data_common.f,sha256=rP3avnulWqJbGCFLWayjoFKSspGDHZMidPTurjz33Tc,201
numpy/f2py/tests/src/crackfortran/data_multiplier.f,sha256=LaPXVuo5lX0gFZVh76Hc7LM1sMk9EBPALuXBnHAGdOA,202
numpy/f2py/tests/src/crackfortran/data_stmts.f90,sha256=MAZ3gstsPqECk3nWQ5Ql-C5udrIv3sAciW1ZGTtHLts,713
numpy/f2py/tests/src/crackfortran/data_with_comments.f,sha256=FUPluNth5uHgyKqjQW7HKmyWg4wDXj3XPJCIC9ZZuOs,183
numpy/f2py/tests/src/crackfortran/foo_deps.f90,sha256=D9FT8Rx-mK2p8R6r4bWxxqgYhkXR6lNmPj2RXOseMpw,134
numpy/f2py/tests/src/crackfortran/gh15035.f,sha256=0G9bmfVafpuux4-ZgktYZ6ormwrWDTOhKMK4wmiSZlQ,391
numpy/f2py/tests/src/crackfortran/gh17859.f,sha256=acknjwoWYdA038oliYLjB4T1PHhXkKRLeJobIgB_Lbo,352
numpy/f2py/tests/src/crackfortran/gh22648.pyf,sha256=xPnKx4RcT1568q-q_O83DYpCgVYJ8z4WQ-yLmHPchJA,248
numpy/f2py/tests/src/crackfortran/gh23533.f,sha256=k2xjRpRaajMYpi5O-cldYPTZGFGB12PUGcj5Fm9joyk,131
numpy/f2py/tests/src/crackfortran/gh23598.f90,sha256=20ukdZXq-qU0Zxzt4W6cO8tRxlNlQ456zgD09zdozCE,105
numpy/f2py/tests/src/crackfortran/gh23598Warn.f90,sha256=FvnIxy5fEOvzNb5WSkWzPk7yZ9yIv0yPZk9vNnS-83w,216
numpy/f2py/tests/src/crackfortran/gh23879.f90,sha256=jELVfEGEF66z_Pv_iBHp3yGsGhadB0dnKCDtPcaz_CM,352
numpy/f2py/tests/src/crackfortran/gh2848.f90,sha256=-IpkeTz0j9_lkQeN9mT7w3U1cAJjQxSMdAmyHdF8oVg,295
numpy/f2py/tests/src/crackfortran/operators.f90,sha256=cb1JO2hIMCQejZO_UJWluBCP8LdXQbBJw2XN6YHB3JA,1233
numpy/f2py/tests/src/crackfortran/privatemod.f90,sha256=9O2oWEquIUcbDB1wIzNeae3hx4gvXAoYW5tGfBt3KWk,185
numpy/f2py/tests/src/crackfortran/publicmod.f90,sha256=nU_VXCKiniiUq_78KAWkXiN6oiMQh39emMxbgOVf9cg,177
numpy/f2py/tests/src/crackfortran/pubprivmod.f90,sha256=-uz75kquU4wobaAPZ1DLKXJg6ySCZoDME1ce6YZ2q5Y,175
numpy/f2py/tests/src/crackfortran/unicode_comment.f90,sha256=wDMoF7F7VFYdeocfTyWIh7noniEwExVb364HrhUSbSg,102
numpy/f2py/tests/src/f2cmap/.f2py_f2cmap,sha256=fwszymaWhcWO296u5ThHW5yMAkFhB6EtHWqqpc9FAVI,83
numpy/f2py/tests/src/f2cmap/isoFortranEnvMap.f90,sha256=rphN_mmzjCCCkdPM0HjsiJV7rmxpo4GoCNp5qmBzv8U,307
numpy/f2py/tests/src/isocintrin/isoCtests.f90,sha256=Oir0PfE3mErnUQ42aFxiqAkcYn3B6b1FHIPGipDdekg,1032
numpy/f2py/tests/src/kind/foo.f90,sha256=6_zq3OAWsuNJ5ftGTQAEynkHy-MnuLgBXmMIgbvL7yU,367
numpy/f2py/tests/src/mixed/foo.f,sha256=Zgn0xDhhzfas3HrzgVSxIL1lGEF2mFRVohrvXN1thU0,90
numpy/f2py/tests/src/mixed/foo_fixed.f90,sha256=6eEEYCH71gPp6lZ6e2afLrfS6F_fdP7GZDbgGJJ_6ns,187
numpy/f2py/tests/src/mixed/foo_free.f90,sha256=UC6iVRcm0-aVXAILE5jZhivoGQbKU-prqv59HTbxUJA,147
numpy/f2py/tests/src/modules/gh25337/data.f90,sha256=EqMEuEV0_sx4XbFzftbU_6VfGtOw9Tbs0pm0eVEp2cA,188
numpy/f2py/tests/src/modules/gh25337/use_data.f90,sha256=DChVLgD7qTOpbYNmfGjPjfOx5YsphMIYwdwnF12X4xM,185
numpy/f2py/tests/src/modules/gh26920/two_mods_with_no_public_entities.f90,sha256=MMLPSzBwuGS4UwCXws9djH11F5tG5xFLc80CDb4U9Mk,423
numpy/f2py/tests/src/modules/gh26920/two_mods_with_one_public_routine.f90,sha256=1dJD1kDC_wwn7v_zF49D3n62T1x9wFxGKanQQz_VI7k,424
numpy/f2py/tests/src/modules/module_data_docstring.f90,sha256=-asnMH7vZMwVIeMU2YiLWgYCUUUxZgPTpbAomgWByHs,236
numpy/f2py/tests/src/modules/use_modules.f90,sha256=bveSAqXIZtd4NMlDfFei1ZlesFAa9An5LjkD-gDk2ms,418
numpy/f2py/tests/src/negative_bounds/issue_20853.f90,sha256=IxBGWem-uv9eHgDhysEdGTmNKHR1gAiU7YJPo20eveM,164
numpy/f2py/tests/src/parameter/constant_array.f90,sha256=fkYemwIBKsP63-FGKBW8mzOAp6k13eZOin8sQe1pyno,1513
numpy/f2py/tests/src/parameter/constant_both.f90,sha256=L0rG6-ClvHx7Qsch46BUXRi_oIEL0uw5dpRHdOUQuv0,1996
numpy/f2py/tests/src/parameter/constant_compound.f90,sha256=lAT76HcXGMgr1NfKof-RIX3W2P_ik1PPqkRdJ6EyBmM,484
numpy/f2py/tests/src/parameter/constant_integer.f90,sha256=42jROArrG7vIag9wFa_Rr5DBnnNvGsrEUgpPU14vfIo,634
numpy/f2py/tests/src/parameter/constant_non_compound.f90,sha256=u9MRf894Cw0MVlSOUbMSnFSHP4Icz7RBO21QfMkIl-Q,632
numpy/f2py/tests/src/parameter/constant_real.f90,sha256=QoPgKiHWrwI7w5ctYZugXWzaQsqSfGMO7Jskbg4CLTc,633
numpy/f2py/tests/src/quoted_character/foo.f,sha256=0zXQbdaqB9nB8R4LF07KDMFDbxlNdiJjVdR8Nb3nzIM,496
numpy/f2py/tests/src/regression/AB.inc,sha256=ydjTVb6QEw1iYw2tRiziqqzWcDHrJsNWr3m51-rqFXQ,17
numpy/f2py/tests/src/regression/f77comments.f,sha256=FjP-07suTBdqgtwiENT04P-47UB4g9J5-20IQdXAHhM,652
numpy/f2py/tests/src/regression/f77fixedform.f95,sha256=KdKFcAc3ZrID-h4nTOJDdEYfQzR2kkn9VqQCorfJGpM,144
numpy/f2py/tests/src/regression/f90continuation.f90,sha256=VweFIi5-xxZhtgSOh8i_FjMPXu_od9qjrDHq6ma5X5k,285
numpy/f2py/tests/src/regression/incfile.f90,sha256=gq87H2CtCZUON9V5UzcK6x_fthnWDVuPFQLa0fece1M,97
numpy/f2py/tests/src/regression/inout.f90,sha256=TlMxJjhjjiuLI--Tg2LshLnbfZpiKz37EpR_tPKKSx8,286
numpy/f2py/tests/src/return_character/foo77.f,sha256=tRyQSu9vNWtMRi7gjmMN-IZnS7ogr5YS0n38uax_Eo0,1025
numpy/f2py/tests/src/return_character/foo90.f90,sha256=WPQZC6CjXLbUYpzy5LItEoHmRDFxW0ABB3emRACsjZU,1296
numpy/f2py/tests/src/return_complex/foo77.f,sha256=7-iKoamJ-VObPFR-Tslhiw9E-ItIvankWMyxU5HqxII,1018
numpy/f2py/tests/src/return_complex/foo90.f90,sha256=_GOKOZeooWp3pEaTBrZNmPmkgGodj33pJnJmySnp7aE,1286
numpy/f2py/tests/src/return_integer/foo77.f,sha256=EKs1KeAOQBkIO99tMCx0H7_lpqvqpjie8zWZ6T_bAR4,1234
numpy/f2py/tests/src/return_integer/foo90.f90,sha256=0aYWcaAVs7Lw3Qbf8hupfLC8YavRuPZVIwjHecIlMOo,1590
numpy/f2py/tests/src/return_logical/foo77.f,sha256=Ax3tBVNAlxFtHhV8fziFcsTnoa8YJdapecMr6Qj7fLk,1244
numpy/f2py/tests/src/return_logical/foo90.f90,sha256=IZXCerFecYT24zTQ_spIoPr6n-fRncaM0tkTs8JqO1E,1590
numpy/f2py/tests/src/return_real/foo77.f,sha256=3nAY1YtzGk4osR2jZkHMVIUHxFoOtF1OLfWswpcV7kA,978
numpy/f2py/tests/src/return_real/foo90.f90,sha256=38ZCnBGWb9arlJdnVWvZjVk8uesrQN8wG2GrXGcSIJs,1242
numpy/f2py/tests/src/size/foo.f90,sha256=nK_767f1TtqVr-dMalNkXmcKbSbLCiabhRkxSDCzLz0,859
numpy/f2py/tests/src/string/char.f90,sha256=X_soOEV8cKsVZefi3iLT7ilHljjvJJ_i9VEHWOt0T9Y,647
numpy/f2py/tests/src/string/fixed_string.f90,sha256=tCN5sA6e7M1ViZtBNvTnO7_efk7BHIjyhFKBoLC3US0,729
numpy/f2py/tests/src/string/gh24008.f,sha256=Z6cq8SFGvmaA72qeH9tu1rP8pYjqm0ONpHn7nGbhoLA,225
numpy/f2py/tests/src/string/gh24662.f90,sha256=xJkiYvrMT9Ipb9Cq7OXl1Ev6TISl8pq1MGemySzfGd0,204
numpy/f2py/tests/src/string/gh25286.f90,sha256=lqEl81Iu9GIDTAbOfkkNGcGgDyyGnPB44mJw2iK1kng,318
numpy/f2py/tests/src/string/gh25286.pyf,sha256=wYkkr5gEN9_RtGjpqh28X1k8KCgh0-Ds9XAt8IC9j4A,393
numpy/f2py/tests/src/string/gh25286_bc.pyf,sha256=ZRvgSzRlaPEx8GyNt97FrRhtCg-r4ZTEDsHNBfit4m8,396
numpy/f2py/tests/src/string/scalar_string.f90,sha256=U1QqVgbF1DbxdFekRjchyDlFRPnXwzG72kuE8A44Za8,185
numpy/f2py/tests/src/string/string.f,sha256=JCwLuH21Ltag5cw_9geIQQJ4Hv_39NqG8Dzbqj1eDKE,260
numpy/f2py/tests/src/value_attrspec/gh21665.f90,sha256=MbbSUQI5Enzq46KWFHRzQbY7q6ZHJH_9NRL-C9i13Wg,199
numpy/f2py/tests/test_abstract_interface.py,sha256=HPd3mOIhLcqbyIf0_xT4yqxoLoxxQp2SbZoyYzGoXL4,876
numpy/f2py/tests/test_array_from_pyobj.py,sha256=lMTKks8SFB-_SNcYSmHNlsxz_-yqPsuQQhhlAyx1of4,24462
numpy/f2py/tests/test_assumed_shape.py,sha256=IyqJPGpGVv_RaRCwrko_793jLxJC1495tR9gAbmTlR8,1515
numpy/f2py/tests/test_block_docstring.py,sha256=0Dh1GXlaCg33DmlbhC08MOBMXdpMbk983MQB2hB7XhA,600
numpy/f2py/tests/test_callback.py,sha256=7GUGk7r7NPaxp-FJ80gI4u5oFwBuFxOFJN9-CZQI46c,6806
numpy/f2py/tests/test_character.py,sha256=nT9ax7A1ixbfloqQ34Z6MlacJHwevFcMy2_HOesJKpo,22565
numpy/f2py/tests/test_common.py,sha256=z1qoOm6HFvLal_cOCPuNn7NVohWjWBcO2v1maVFfRhQ,661
numpy/f2py/tests/test_crackfortran.py,sha256=QTFsKOtJk907-MOIhzUrm7UB3W7tXqOkgJsls0O5G98,16295
numpy/f2py/tests/test_data.py,sha256=0gIPLYE187Gf362yskXprc_ze8fzDFtU9pbD3MWmAio,2969
numpy/f2py/tests/test_docs.py,sha256=zYqI3MMTcytHP1-KsywMp8eBvpYurf7wkHccDyuCbyc,1933
numpy/f2py/tests/test_f2cmap.py,sha256=2Yy4zuFrkn0QvCkiRjGiHqirp9bXe8ODSnM_LYNAUsM,400
numpy/f2py/tests/test_f2py2e.py,sha256=UPJUFf7CGjUYceN3VjfLc4lw_7Vgs2nHBBF0-VqlAyc,28803
numpy/f2py/tests/test_isoc.py,sha256=KGUijaN2Qum_qQD1Rc7m7B3dMTx47oRud8ZWNfc5M0Y,1481
numpy/f2py/tests/test_kind.py,sha256=oM-s-rCSQDO3XerECjSeOlSi0fi4hgO9xBbzMXoAPc4,1843
numpy/f2py/tests/test_mixed.py,sha256=CVw7cerVhNwviuRDlLlhokbYSXpAtI_-GgVVibcrRoU,904
numpy/f2py/tests/test_modules.py,sha256=mMLzcjENVJ3on--z9qmbUthruWz02T49XiY_A0xbzkw,2380
numpy/f2py/tests/test_parameter.py,sha256=laQi-MQDlqMy4XfOBFnixhxNkcdE-wfYze8mcWCIsro,4764
numpy/f2py/tests/test_pyf_src.py,sha256=RLm95aANGakQYCzk_UJjUcq0mOQH0LtD6HoZYkEiIrU,1179
numpy/f2py/tests/test_quoted_character.py,sha256=cLPRMhNiCO0v-_A5jPkTg-Zv38U-bbJteuLOL9VSZik,493
numpy/f2py/tests/test_regression.py,sha256=iku13JEKU85lWxUqrFlmil2fme89e9MSlXAhh7I3Fcg,4878
numpy/f2py/tests/test_return_character.py,sha256=9hAUrTWmHkSnRQM4pz43cLFBSEIU5sN8g2M8xaqBqBE,1557
numpy/f2py/tests/test_return_complex.py,sha256=ynSaaMSxiBTApp-tIGwXHLe5gCjqm4qJCq_QNwihGWk,2481
numpy/f2py/tests/test_return_integer.py,sha256=PNeeeykh0Q9oPxUCcuLC3Q1XFbRrk7jhQwK6erjau0M,1830
numpy/f2py/tests/test_return_logical.py,sha256=gPBO6zxmwek0fUIvCDgybiltiNqiMwaIqqsY2o0PXtg,2081
numpy/f2py/tests/test_return_real.py,sha256=AB0L__3Qoi1oHb4mXtfY6I8pE2F1Z1qRtEzPNVJVONw,3361
numpy/f2py/tests/test_semicolon_split.py,sha256=FFm5GdeDYwQ348uo6_3oFPGyC0wJH_uWGjDRw2I5tto,1728
numpy/f2py/tests/test_size.py,sha256=Qy8KH9Co1IL6GbnDJ5IDGRPD1HKQ3HL6zXCkN2wpuUY,1209
numpy/f2py/tests/test_string.py,sha256=vSMQKo1SK4Y1xpgVw8iquHHH2kmelFsmphMMKYhnAaM,3062
numpy/f2py/tests/test_symbolic.py,sha256=Zk4h3WC2etMrIEyMrayPpGthpWfuS35Yz-4XzzGFcY4,18835
numpy/f2py/tests/test_value_attrspec.py,sha256=rzUhPldFKJXkCAxzRgZlLFeAJarfJCAEV4fFg--YF60,354
numpy/f2py/tests/util.py,sha256=r5ScKHYr_ZBb4awsxsJKscdJ9mQYFLQWyPzMWK-yVQo,12661
numpy/f2py/use_rules.py,sha256=xSi4D11ZN6_O7kQZ_v_dD-043gTeD1y7YvqKBq58FYg,3633
numpy/fft/__init__.py,sha256=MwVEjIo3wDxMAbKERmmX3cHh8EK9nIw9vlUNTpOgNyo,8541
numpy/fft/__init__.pyi,sha256=tdoAmh_tKqlcHHDqCN1bUlJJVVSSIFyPLNx0w5B_uiE,559
numpy/fft/__pycache__/__init__.cpython-310.pyc,,
numpy/fft/__pycache__/_helper.cpython-310.pyc,,
numpy/fft/__pycache__/_pocketfft.cpython-310.pyc,,
numpy/fft/__pycache__/helper.cpython-310.pyc,,
numpy/fft/_helper.py,sha256=nAtQQ7eHZrQhws3IEIBtpnCWA4emPricOmNnXrm_bng,7010
numpy/fft/_helper.pyi,sha256=RxjRN0odazLpWYw7B6T5Q4LqukBLJaaBQeAVbXbj7LI,1381
numpy/fft/_pocketfft.py,sha256=1Ycffk7VQQ33n4FOCom7CoLRBWPncQRwsLQSQnZ-oww,64883
numpy/fft/_pocketfft.pyi,sha256=aB_E4DdUY2V6aKDBEWXHzvtF_F25obzakXzF9FRIJ0M,3083
numpy/fft/_pocketfft_umath.cp310-win_amd64.lib,sha256=DPXtnyFmecouNQ-O9QbCb4Pquc3qUQRoya4CmwufSJQ,2176
numpy/fft/_pocketfft_umath.cp310-win_amd64.pyd,sha256=xGaTbN3_aT2RlHY3LiU_CwGrXPa1X4wTQAk5gQ6nPvE,279040
numpy/fft/helper.py,sha256=Dvf6DS9pHTCmugMQy5IBwk5LlSt5PjdShv1IRsUySIY,626
numpy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/fft/tests/__pycache__/__init__.cpython-310.pyc,,
numpy/fft/tests/__pycache__/test_helper.cpython-310.pyc,,
numpy/fft/tests/__pycache__/test_pocketfft.cpython-310.pyc,,
numpy/fft/tests/test_helper.py,sha256=-CrZvGxoD1xhFNVsHJS3oNTw6yYoNq06CKHmWO_0fSk,6316
numpy/fft/tests/test_pocketfft.py,sha256=i6xc69w-shY8rfPk4MghiIRPlB2Bltzu37mYp7v3ct0,25001
numpy/lib/__init__.py,sha256=0Rxfcecr3YjBhTAHdVNvDPN1Qu-mQkEkJ_8KG9g8lUo,3279
numpy/lib/__init__.pyi,sha256=0Up8FAI3BJUbyF1SjHc4CTmIajyk18rsbcYIlTut1Ng,811
numpy/lib/__pycache__/__init__.cpython-310.pyc,,
numpy/lib/__pycache__/_array_utils_impl.cpython-310.pyc,,
numpy/lib/__pycache__/_arraypad_impl.cpython-310.pyc,,
numpy/lib/__pycache__/_arraysetops_impl.cpython-310.pyc,,
numpy/lib/__pycache__/_arrayterator_impl.cpython-310.pyc,,
numpy/lib/__pycache__/_datasource.cpython-310.pyc,,
numpy/lib/__pycache__/_function_base_impl.cpython-310.pyc,,
numpy/lib/__pycache__/_histograms_impl.cpython-310.pyc,,
numpy/lib/__pycache__/_index_tricks_impl.cpython-310.pyc,,
numpy/lib/__pycache__/_iotools.cpython-310.pyc,,
numpy/lib/__pycache__/_nanfunctions_impl.cpython-310.pyc,,
numpy/lib/__pycache__/_npyio_impl.cpython-310.pyc,,
numpy/lib/__pycache__/_polynomial_impl.cpython-310.pyc,,
numpy/lib/__pycache__/_scimath_impl.cpython-310.pyc,,
numpy/lib/__pycache__/_shape_base_impl.cpython-310.pyc,,
numpy/lib/__pycache__/_stride_tricks_impl.cpython-310.pyc,,
numpy/lib/__pycache__/_twodim_base_impl.cpython-310.pyc,,
numpy/lib/__pycache__/_type_check_impl.cpython-310.pyc,,
numpy/lib/__pycache__/_ufunclike_impl.cpython-310.pyc,,
numpy/lib/__pycache__/_user_array_impl.cpython-310.pyc,,
numpy/lib/__pycache__/_utils_impl.cpython-310.pyc,,
numpy/lib/__pycache__/_version.cpython-310.pyc,,
numpy/lib/__pycache__/array_utils.cpython-310.pyc,,
numpy/lib/__pycache__/format.cpython-310.pyc,,
numpy/lib/__pycache__/introspect.cpython-310.pyc,,
numpy/lib/__pycache__/mixins.cpython-310.pyc,,
numpy/lib/__pycache__/npyio.cpython-310.pyc,,
numpy/lib/__pycache__/recfunctions.cpython-310.pyc,,
numpy/lib/__pycache__/scimath.cpython-310.pyc,,
numpy/lib/__pycache__/stride_tricks.cpython-310.pyc,,
numpy/lib/__pycache__/user_array.cpython-310.pyc,,
numpy/lib/_array_utils_impl.py,sha256=8V5hh2JYzL0LKy2KBrRPh-FZHjfKrn7nyS_VNrvRSO0,1751
numpy/lib/_array_utils_impl.pyi,sha256=TE7pig1ciZY__EWnxJFi-VdMJDN7LpPR0UHfveZ_iEE,773
numpy/lib/_arraypad_impl.py,sha256=siEjIJ9xPnR2nGqjSYDzoR4ojTOKOEig2hc2aGVh3eQ,33303
numpy/lib/_arraypad_impl.pyi,sha256=8z4FY3lJ1Xde2FHkoxmpz94aYDk5MDFoMvto8KALxvM,1813
numpy/lib/_arraysetops_impl.py,sha256=5J74lXcWnQEt61VzanHhGCBcXljfr-qr6aghgYo0hQM,40761
numpy/lib/_arraysetops_impl.pyi,sha256=C7JBzpRkmR018tDcVWw5F0acjriCjkPhUH9YBJ_nVBY,9714
numpy/lib/_arrayterator_impl.py,sha256=rNIpG8baocc-xu_rWeyNMhxtqTiDWXgIOCQwoR7bCPM,7390
numpy/lib/_arrayterator_impl.pyi,sha256=ggcL_Hf5WDtTFeX63ZzmXx7x-tk_nbRDaIrYh8S8AO8,1562
numpy/lib/_datasource.py,sha256=H7HKFHCye9r2mLDup6KYnuKcjUrOd3Gc2wFMn78rVGY,23429
numpy/lib/_function_base_impl.py,sha256=AbdIe0ud2rot2v9CkmKxfJxDwWw27Ofw4kgBN_Sy2pI,200017
numpy/lib/_function_base_impl.pyi,sha256=vostJLyQP7SmNtE0hZiOT0JHnTFs3ltnPPoYl2V5NmA,19538
numpy/lib/_histograms_impl.py,sha256=1gKq0l-GuSwYhHosqI6JvTOZtnCIs6nkVL4AQiN7sDc,39749
numpy/lib/_histograms_impl.pyi,sha256=Durb7tQoOK4VlqpqJks7p3r2aHPXGmIV4orMEv98FM8,1048
numpy/lib/_index_tricks_impl.py,sha256=W5TqSnlZ-JSfp0lxdTArvUo4y5mtDTq5pv26JOOez-w,33384
numpy/lib/_index_tricks_impl.pyi,sha256=GVhpSur2uqslztysh-5vPC__W5u8EVMDNz0A7kiWQ7Q,4317
numpy/lib/_iotools.py,sha256=gfw4LwMszW5bDH78mM1Y_VWWCZ_u24uW-tuoZAVBM2k,31840
numpy/lib/_nanfunctions_impl.py,sha256=SXToFwYq0DkKHU-jBhm0iXLivZvOGND2hLFPSVkfMUg,74705
numpy/lib/_nanfunctions_impl.pyi,sha256=7HlF-CjExRJbLHTbAkCi99rt04Ph7J3ESvifINe0QBc,651
numpy/lib/_npyio_impl.py,sha256=TV3hteAox3mNdjSxfFVyQA0viyp518vSlO9YT4vqyU4,101589
numpy/lib/_npyio_impl.pyi,sha256=ER6tWXr8Dzrr9p0kQH8OIP6UQNd_fnIYMd893dVN9wo,10607
numpy/lib/_polynomial_impl.py,sha256=o5Qd-VSe36gsgEuJBrjJrW4j5ZMMfYuq6howHR3TazE,45752
numpy/lib/_polynomial_impl.pyi,sha256=bgY0jagX9iHPBbVm33E3OQFOOtPbGtREAZx8buxlukM,7238
numpy/lib/_scimath_impl.py,sha256=_Hojiycv0uM8OsS42BXCnmaj8Hqv1NiVJ-SC7-pGWUI,16176
numpy/lib/_scimath_impl.pyi,sha256=bcW3wCbYG_cQpWyMAQ9dRY5JenhnGt8RiBjCTewaxag,2977
numpy/lib/_shape_base_impl.py,sha256=NM2IOKzAknKEPAFHhBf1VDzP1Hc3iF3Jsc2152cCPc8,40953
numpy/lib/_shape_base_impl.pyi,sha256=hQmb3EfxMt71RpcfaDx0XoCUT4nOjjvFdVcdVyhw9cc,4927
numpy/lib/_stride_tricks_impl.py,sha256=cJNVXC8z73azEJS67qCJslXDBueUnRKOdXSVyXkq0Cg,18765
numpy/lib/_stride_tricks_impl.pyi,sha256=zVUU06MgM4mH5Atr89IKE629nvNFj8jOyqIjDjiZvig,1833
numpy/lib/_twodim_base_impl.py,sha256=hBwZcp3seSN0WwQNQWvYtSu936zdsogH76e7tG_2CAQ,34906
numpy/lib/_twodim_base_impl.pyi,sha256=EKUQI-5h08dXMGIMIEWtov6uLfx3TNwsU4uMmoZxlRM,11267
numpy/lib/_type_check_impl.py,sha256=3AThkHiCCyP-WFLOEKDwCoMz14M0ouTkKVjPJWdQjaY,20059
numpy/lib/_type_check_impl.pyi,sha256=LMwN0g_Y3nd_AIN0y2-hh5vzMRnI1pRTuulXzmOY_d8,5411
numpy/lib/_ufunclike_impl.py,sha256=AzIIaheWBPlSAYvtUHbc1F5Ys39kBgU1hbY3rbGAPZw,6551
numpy/lib/_ufunclike_impl.pyi,sha256=tb1bwznH6h9JmVeOAgw0gAOZN0kLkf7V9ZVQQC6agPE,1366
numpy/lib/_user_array_impl.py,sha256=YLMKRghWoh5GhF3xplNl46zgxOaPDtqwf2SFbvZlP10,8179
numpy/lib/_utils_impl.py,sha256=ct-wOdmNPaG3J21yJ9Kw1Ds3xpTZGzjPULLL5Ze9RP0,24180
numpy/lib/_utils_impl.pyi,sha256=FVTnFf50fl2gOS7n5O8yKvr1moPiPJhWjmpcYZ3X-x4,677
numpy/lib/_version.py,sha256=yLDQc7F065F67tTSOa1Ed6h1pAMUARAhaLn9V-UKczY,5008
numpy/lib/_version.pyi,sha256=rw_2q4KJ-dfpqJuj1e3PtVqK4Yh2FdJa5AHdE5IRaWM,650
numpy/lib/array_utils.py,sha256=SyMHXlsOJMKwxkjQxjsxx3J2cgx_3J2N0qqmLZTQgMc,137
numpy/lib/array_utils.pyi,sha256=YYnx_V4CMdSbJTCnYboN1swcswmlOD2e4ZvQj5WsSak,197
numpy/lib/format.py,sha256=j2LPlWMhM05Om_j8RsvwV_9g9M71KyEZztAx7EH7Yo8,37325
numpy/lib/format.pyi,sha256=dAlF-kNz-H-Vtn9H7Cs8J1l1xUkc3A2e7oWK1Qy17bs,770
numpy/lib/introspect.py,sha256=9T8sL7NDxKWNdDv29ECbKq7BRfsZ2jkGCaLw4dRZZf8,2833
numpy/lib/mixins.py,sha256=3e7kyw_9VTgXAGAZpJSgp5Zhv-GsC7h7alVzF8sf1Q4,7548
numpy/lib/mixins.pyi,sha256=PsN6nPTdC6ZT74B4nPUlI5Qmkq6pT2lv6z6OZ_U9Bgg,3188
numpy/lib/npyio.py,sha256=nZadg1IKRXTLZX_52TpjU-YutNH5QA_UU457rHfn6oc,65
numpy/lib/npyio.pyi,sha256=XTIBYZ2RaZB_SweXtKkzpVsM9kYtrUlxCzCpFKFJ-ZM,96
numpy/lib/recfunctions.py,sha256=qw43LnMHy7VtDFj6yzVGYY2PviNwflYjnoOun0Q744Y,61598
numpy/lib/scimath.py,sha256=HgFt3iWrgcxgV4Y6U-xyZZBM_MMewX62uP8HhOxhveY,122
numpy/lib/scimath.pyi,sha256=ptqs29LhXC1rKTxduPeHZns2dVEB_wwNuqkZiqDH1Eg,253
numpy/lib/stride_tricks.py,sha256=BDqFklWQ4eVAoAvtdb_3nT0YxXeMZOtPp6nBr7gKG64,85
numpy/lib/stride_tricks.pyi,sha256=6-K3R7XBw_fcpHaAIs9y4LEc5i4r5gZUG-tg4EOR-ew,128
numpy/lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/lib/tests/__pycache__/__init__.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test__datasource.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test__iotools.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test__version.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_array_utils.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_arraypad.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_arraysetops.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_arrayterator.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_format.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_function_base.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_histograms.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_index_tricks.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_io.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_loadtxt.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_mixins.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_nanfunctions.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_packbits.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_polynomial.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_recfunctions.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_regression.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_shape_base.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_stride_tricks.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_twodim_base.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_type_check.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_ufunclike.cpython-310.pyc,,
numpy/lib/tests/__pycache__/test_utils.cpython-310.pyc,,
numpy/lib/tests/data/py2-np0-objarr.npy,sha256=ZLoI7K3iQpXDkuoDF1Ymyc6Jbw4JngbQKC9grauVRsk,258
numpy/lib/tests/data/py2-objarr.npy,sha256=F4cyUC-_TB9QSFLAo2c7c44rC6NUYIgrfGx9PqWPSKk,258
numpy/lib/tests/data/py2-objarr.npz,sha256=xo13HBT0FbFZ2qvZz0LWGDb3SuQASSaXh7rKfVcJjx4,366
numpy/lib/tests/data/py3-objarr.npy,sha256=7mtikKlHXp4unZhM8eBot8Cknlx1BofJdd73Np2PW8o,325
numpy/lib/tests/data/py3-objarr.npz,sha256=vVRl9_NZ7_q-hjduUr8YWnzRy8ESNlmvMPlaSSC69fk,453
numpy/lib/tests/data/python3.npy,sha256=X0ad3hAaLGXig9LtSHAo-BgOvLlFfPYMnZuVIxRmj-0,96
numpy/lib/tests/data/win64python2.npy,sha256=agOcgHVYFJrV-nrRJDbGnUnF4ZTPYXuSeF-Mtg7GMpc,96
numpy/lib/tests/test__datasource.py,sha256=H6PZKQ0tY6r1bhrcLRKMjWdWop5P4Rj_SYvrU9ukDzc,10921
numpy/lib/tests/test__iotools.py,sha256=ejbG7SVvTm55Lq5LdUza8-nIvF2mt-XYvfpzn13q038,14097
numpy/lib/tests/test__version.py,sha256=v2TOlH4f1Pmzxn1HWby3eBgLO9tGnhwH2LvBXlXtHP4,2063
numpy/lib/tests/test_array_utils.py,sha256=Fy8_PR6GHed-mStqcbfjTe8Q5zMZnJ9WzFzX6DjoRR0,1152
numpy/lib/tests/test_arraypad.py,sha256=ZwFgHuHGTsEJ5A2FZzM-DTZ9J1gHJWkLrGaepzXvzRw,57503
numpy/lib/tests/test_arraysetops.py,sha256=9w0deU2nmu2hIOZlphdzWREZ9UmwknmbSdxCsgBEoi0,39012
numpy/lib/tests/test_arrayterator.py,sha256=IRVmzxbr9idboJjOHKuX_8NQhMAKs7pD1xWqmU3ZERw,1337
numpy/lib/tests/test_format.py,sha256=WKvXRP9Ql5y4qp-nqMAguHaMtjjLbEu9rAna-VkQaVw,41978
numpy/lib/tests/test_function_base.py,sha256=6fn2qjT7X5kdzFcTnvFrl0IlDAKW5-PLQNs7CbVdK_w,171261
numpy/lib/tests/test_histograms.py,sha256=62kvbJaW5cfLQCmSkd8CFzLR6rPVFq7iK71yhGUsrCE,34391
numpy/lib/tests/test_index_tricks.py,sha256=tgXpLGpT9XpO_djXCTKpM0-WF-AVE5GF8lbvIyUz9X4,20921
numpy/lib/tests/test_io.py,sha256=Rh21OHkd6IqzOyk3uhyioR3AaSAo36Nz-0AnOE9hRh4,112192
numpy/lib/tests/test_loadtxt.py,sha256=0Ylr-uk81-69Z3KEZH0Rp4cgP9yBAqL6g6uesLuLaME,40574
numpy/lib/tests/test_mixins.py,sha256=nIec_DZIDx7ONnlpq_Y2TLkIULAPvQ7LPqtMwEHuV4U,7246
numpy/lib/tests/test_nanfunctions.py,sha256=a6uEdfPjMixC7gSrw7ptYJq--GOl1_r9rWh6s2R5hxM,54769
numpy/lib/tests/test_packbits.py,sha256=yN8rYbPDteOPJf7dEeZkgSnyzIUKe_ituLYdsqxcqbQ,17920
numpy/lib/tests/test_polynomial.py,sha256=ISb6Qkl0uFSpE8163jAhwyVCpDx2E4XErzIvfryv3rE,11703
numpy/lib/tests/test_recfunctions.py,sha256=OBrCGHSH3wAPVj0hVjMX05ev97LVqeAUm1bTTZZoEMU,45044
numpy/lib/tests/test_regression.py,sha256=05NyviW3yIMWCqV7zg5P6HIZCBil31eziBY2GG2aD5I,7942
numpy/lib/tests/test_shape_base.py,sha256=fYKyGdLTM-l2rlTHAzDJbObc_SQWXXF8QoKt266F7K4,28296
numpy/lib/tests/test_stride_tricks.py,sha256=EKHYiPoawG_vu_tFmKi5Lmvfs0VEDcUW7feiWybUGXA,23644
numpy/lib/tests/test_twodim_base.py,sha256=mNNXsDKT3hPpz-HB_1k8YTWpwdx7dnvmrWWS_Lkew30,19382
numpy/lib/tests/test_type_check.py,sha256=hIX902yujOzj62j6qtuoGdBV_j4L_zxHm7gZ1o-UFvg,15167
numpy/lib/tests/test_ufunclike.py,sha256=9C9LV3XZLaHNQoyRVZl-C4w9HcOTEJMDw2uXYXhf1u4,3123
numpy/lib/tests/test_utils.py,sha256=KN1q-eFLmckYbOMTxPKTwFMPtzBHdAPb0j9ntfea_yM,2454
numpy/lib/user_array.py,sha256=v3dCCNs-PZ7tHZ1vqGqdeV5FLHRiLLWrMZhdzQTSRAM,50
numpy/linalg/__init__.py,sha256=AZnH2FnMk_bDy8VuOsihmoS-nICrpKIRMPNa5Puyk30,2201
numpy/linalg/__init__.pyi,sha256=sSoAjq4LtJrWARF5wGU6nZwqrIJNKh445-RjY_hrmTw,1004
numpy/linalg/__pycache__/__init__.cpython-310.pyc,,
numpy/linalg/__pycache__/_linalg.cpython-310.pyc,,
numpy/linalg/__pycache__/linalg.cpython-310.pyc,,
numpy/linalg/_linalg.py,sha256=1waA9GCzw2vihiep5S68hGGshY6sR7736MOAN6qgStI,119135
numpy/linalg/_linalg.pyi,sha256=5Yc_wS3XiDGGiWg0jhQre9mX3LRFANVz-t_01LYUHFU,11152
numpy/linalg/_umath_linalg.cp310-win_amd64.lib,sha256=Ca0b3VNthxj47L_wxcn0Ltp93sf0Ivs2vcaIjKI5Hfo,2120
numpy/linalg/_umath_linalg.cp310-win_amd64.pyd,sha256=tUGYVmKVMiQ3_v8zBGc4kfFV-tW_1RW2QKpOHctOocI,108032
numpy/linalg/lapack_lite.cp310-win_amd64.lib,sha256=T26nmNBuY-07uxY9wRQ01fUmvcnezwsYJMt3CkAer1w,2084
numpy/linalg/lapack_lite.cp310-win_amd64.pyd,sha256=gZjiH6XHFKchNnF6WAyBVi-SKyz8jxoLz6Q46bWxGb4,17920
numpy/linalg/linalg.py,sha256=1CC9jc-u61GePC5AuieDiyMyrVvgLD8ZJbTPvLfKjHc,600
numpy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/linalg/tests/__pycache__/__init__.cpython-310.pyc,,
numpy/linalg/tests/__pycache__/test_deprecations.cpython-310.pyc,,
numpy/linalg/tests/__pycache__/test_linalg.cpython-310.pyc,,
numpy/linalg/tests/__pycache__/test_regression.cpython-310.pyc,,
numpy/linalg/tests/test_deprecations.py,sha256=GaeE3JnQlJLoAfbY93LmgCFUlV5M8IFmQ7EhF4WbqwU,660
numpy/linalg/tests/test_linalg.py,sha256=kt3BGE9QP1JIyUliO1IEjVxJt5VsossA1H7RQEdcyvQ,85741
numpy/linalg/tests/test_regression.py,sha256=EI-TxNoazBhutV813qp4k4Ix6jKGIBspon3puHZGpp8,6883
numpy/ma/API_CHANGES.txt,sha256=U39zA87aM_OIJhEKvHgL1RY1lhMJZc1Yj3DGLwbPbF0,3540
numpy/ma/LICENSE,sha256=1427IIuA2StNMz5BpLquUNEkRPRuUxmfp3Jqkd5uLac,1616
numpy/ma/README.rst,sha256=_MHrqHTE8L4wiJJqvaOh1l-xTxidwdilc_SZkFbgubM,10110
numpy/ma/__init__.py,sha256=EFe3qk5iN_7Z__BwlkEW6xo2Zc6NnI8F7G2b1UVW4uY,1473
numpy/ma/__init__.pyi,sha256=qLQuZN0tRMKVIm-Agz5wEcdaxZ_79A_kcqDsK-E3hYQ,6274
numpy/ma/__pycache__/__init__.cpython-310.pyc,,
numpy/ma/__pycache__/core.cpython-310.pyc,,
numpy/ma/__pycache__/extras.cpython-310.pyc,,
numpy/ma/__pycache__/mrecords.cpython-310.pyc,,
numpy/ma/__pycache__/testutils.cpython-310.pyc,,
numpy/ma/__pycache__/timer_comparison.cpython-310.pyc,,
numpy/ma/core.py,sha256=hh6hU2cgrKaxeoKdlZ6S_uqZSN1wk7AFKaddgIJxLWk,296559
numpy/ma/core.pyi,sha256=KtHZFMnmH3oYc1WNlWL9y69uyEZoJQNy7ljLptGuAvc,14768
numpy/ma/extras.py,sha256=m0oBIr2nQmXDlgLikyBv6UE2uXMUQuRfxexKjkxBvkY,73367
numpy/ma/extras.pyi,sha256=gIwx3qeof7ShV6_7app98Rj6tmYvuDTYMJv9pCET8g8,2739
numpy/ma/mrecords.py,sha256=ywqcZdFlKtR6WAM1R39Jgn0abDCe1ptfQUefWzJNWo8,27976
numpy/ma/mrecords.pyi,sha256=t4gt79zg6z-NN1kCR54F1LQdt9IwsaDlNchBU_3oRDs,1972
numpy/ma/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/ma/tests/__pycache__/__init__.cpython-310.pyc,,
numpy/ma/tests/__pycache__/test_arrayobject.cpython-310.pyc,,
numpy/ma/tests/__pycache__/test_core.cpython-310.pyc,,
numpy/ma/tests/__pycache__/test_deprecations.cpython-310.pyc,,
numpy/ma/tests/__pycache__/test_extras.cpython-310.pyc,,
numpy/ma/tests/__pycache__/test_mrecords.cpython-310.pyc,,
numpy/ma/tests/__pycache__/test_old_ma.cpython-310.pyc,,
numpy/ma/tests/__pycache__/test_regression.cpython-310.pyc,,
numpy/ma/tests/__pycache__/test_subclassing.cpython-310.pyc,,
numpy/ma/tests/test_arrayobject.py,sha256=ap06C0a0dGWcOknpctbhLbzHSNd2M9p_JL2jESqBBGk,1139
numpy/ma/tests/test_core.py,sha256=BdRX5eA2f3Xuhu8VLu9r5xMKeDbYExo03fDT4YhFIDs,221592
numpy/ma/tests/test_deprecations.py,sha256=WurKSuN6hsXmWxRoxstdVBXcKCTvYxlYz-ntSkW6qKc,2650
numpy/ma/tests/test_extras.py,sha256=C_auxUGRJ38o-7LZGNTN5IdAi48c1QIY8bzM2NozB6g,80274
numpy/ma/tests/test_mrecords.py,sha256=TzQwlvY1iJnKH7ARsOI9nNaNeTt1sGgZAj8NEjP7jY0,20348
numpy/ma/tests/test_old_ma.py,sha256=tQ-IqKZ1NMHq5_8qkOaZWg_rZkWBpRaPnlodBRd_ABA,33629
numpy/ma/tests/test_regression.py,sha256=J1ftHDKfIF3SUIgQlxJplCsYTrPpAyN4rf5K1Uw5T8w,3384
numpy/ma/tests/test_subclassing.py,sha256=YK5WYq4zSMj6gmTNZfI2ElaFpT9fb31XpOZxN_3MCnM,17486
numpy/ma/testutils.py,sha256=86e8bckl-C24JBICXzVMI_s4RqtbgZqDLD0L5tZPTgc,10564
numpy/ma/timer_comparison.py,sha256=xru-X3qO_cG98SFYHXN0Hg-PO8Ig6cyX5pTZCskHBmA,16136
numpy/matlib.py,sha256=TL7bhRdtlMOmaxua0P7_Q0z4Za8OXr4AtEvQliLvz0k,11072
numpy/matrixlib/__init__.py,sha256=9-DMlmdLxOk5HSGJ20AuTjKkGZ3MUPHCFjhE6sb4NMo,253
numpy/matrixlib/__init__.pyi,sha256=ly_PIgEdVlSHNMlDWDIRrRk0Sc_6PQWSD8-BK094xBQ,246
numpy/matrixlib/__pycache__/__init__.cpython-310.pyc,,
numpy/matrixlib/__pycache__/defmatrix.cpython-310.pyc,,
numpy/matrixlib/defmatrix.py,sha256=4B5rB_-8rtVwuBMswLCz6V8gK-v5UCr0RRZvulQQc9Y,31871
numpy/matrixlib/defmatrix.pyi,sha256=i7medmOD8aL6_PMJSiGSnWmld_YOxsoP67Kh-SR_QLo,467
numpy/matrixlib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/matrixlib/tests/__pycache__/__init__.cpython-310.pyc,,
numpy/matrixlib/tests/__pycache__/test_defmatrix.cpython-310.pyc,,
numpy/matrixlib/tests/__pycache__/test_interaction.cpython-310.pyc,,
numpy/matrixlib/tests/__pycache__/test_masked_matrix.cpython-310.pyc,,
numpy/matrixlib/tests/__pycache__/test_matrix_linalg.cpython-310.pyc,,
numpy/matrixlib/tests/__pycache__/test_multiarray.cpython-310.pyc,,
numpy/matrixlib/tests/__pycache__/test_numeric.cpython-310.pyc,,
numpy/matrixlib/tests/__pycache__/test_regression.cpython-310.pyc,,
numpy/matrixlib/tests/test_defmatrix.py,sha256=3cSTjFilFZVq2fMgfoUlx6hf9N4MSvBMhHcemoiUzLA,15488
numpy/matrixlib/tests/test_interaction.py,sha256=9loMwSKXBOu09Z6aZ6_RG7ojbEfn19A8N39h12F5668,12249
numpy/matrixlib/tests/test_masked_matrix.py,sha256=SjuUs4IhE3x2y8oM9uoWhKX4K1sX2JNkLQMlhMlvzD0,9146
numpy/matrixlib/tests/test_matrix_linalg.py,sha256=9S9Zrk8PMLfEEo9wBx5LyrV_TbXhI6r-Hc5t594lQFY,2152
numpy/matrixlib/tests/test_multiarray.py,sha256=E5jvWX9ypWYNHH7iqAW3xz3tMrEV-oNgjN3_oPzZzws,570
numpy/matrixlib/tests/test_numeric.py,sha256=l-LFBKPoP3_O1iea23MmaACBLx_tSSdPcUBBRTiTbzk,458
numpy/matrixlib/tests/test_regression.py,sha256=wpWVjM4pHRaiVX_Y5_zc6yNr4I5zWdmJfHTwbmBUhew,963
numpy/polynomial/__init__.py,sha256=JAnPIGbR7QJilyIhHjVvA7SsWGSO1Sm0PCse-XWk3dY,6947
numpy/polynomial/__init__.pyi,sha256=UZaSx2BmyHWFz5_TdaI9pD4Xi05-hCUhMloFtKrsm_s,634
numpy/polynomial/__pycache__/__init__.cpython-310.pyc,,
numpy/polynomial/__pycache__/_polybase.cpython-310.pyc,,
numpy/polynomial/__pycache__/chebyshev.cpython-310.pyc,,
numpy/polynomial/__pycache__/hermite.cpython-310.pyc,,
numpy/polynomial/__pycache__/hermite_e.cpython-310.pyc,,
numpy/polynomial/__pycache__/laguerre.cpython-310.pyc,,
numpy/polynomial/__pycache__/legendre.cpython-310.pyc,,
numpy/polynomial/__pycache__/polynomial.cpython-310.pyc,,
numpy/polynomial/__pycache__/polyutils.cpython-310.pyc,,
numpy/polynomial/_polybase.py,sha256=UgHa6POPh6nz5JGjAE3uqFrmEhjS3aLDANct1mpC71Y,41115
numpy/polynomial/_polybase.pyi,sha256=JeAsu9hrHzkVZWb7yLU-Nv9T_TaX0yTp8R6VHe-VX_I,9057
numpy/polynomial/_polytypes.pyi,sha256=DnCb5_ddY9IZnrO-uMU83kDvoCAjj8_wvgVOPDlf4qw,23803
numpy/polynomial/chebyshev.py,sha256=rz2Cn3Zgf6pfd-iKTIKq330N6k5hJJ4XU20D7_hXasw,65025
numpy/polynomial/chebyshev.pyi,sha256=AnJkNZoHyIUQvFbQfexdey-GJwN3fMjZs2pDZT6YzvQ,4917
numpy/polynomial/hermite.py,sha256=zrFPELLkTnmlA_PhPXMKAUrxmlSVlot5tpeljBK5ap0,56851
numpy/polynomial/hermite.pyi,sha256=xggYYL_74IGVlqmK9NXXIiSpGKELIcoqaOOJ0enXvPU,2551
numpy/polynomial/hermite_e.py,sha256=aartMVr46ZzrEPpNZIu-pY91uMH1FjIHwYRLvujAtA8,54511
numpy/polynomial/hermite_e.pyi,sha256=CGq8MpTXOonV1JzfLdWuN_-pXOYEJG4qvNd977s11ho,2643
numpy/polynomial/laguerre.py,sha256=BeT8pBbAUGJSstf62JKa7X3x_Zj4ywsCXCwleLYOz4w,54667
numpy/polynomial/laguerre.pyi,sha256=ftBF2ZU4CFriNY6xy8lGP-gNxRB4udAI4HVW7nkv2R0,2274
numpy/polynomial/legendre.py,sha256=EDHL8_BTFGG4Erl4POaPUs7oh-2oZGxbkL7pZViGNtM,53263
numpy/polynomial/legendre.pyi,sha256=590XJNm9Yl_ShYBZdcrlB65qs9DEh7OOAmeC_IXu5to,2272
numpy/polynomial/polynomial.py,sha256=iNOs_xb9er6U8i-5uVjAGUB2sekJ6HnT4zog325eq0Q,54363
numpy/polynomial/polynomial.pyi,sha256=0KSIDRCJg1EnrZCuyQVCEKP07IiHTFHyaKPC4Po3jJI,2089
numpy/polynomial/polyutils.py,sha256=L6f5kUEjttU8_THuwGRuccqwokiQvPxz-o8d_zj0LhI,23243
numpy/polynomial/polyutils.pyi,sha256=zA5UdU71NWqiKv3nAYAt5MAcJgAywHOj9lwjX8sbEro,10857
numpy/polynomial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/polynomial/tests/__pycache__/__init__.cpython-310.pyc,,
numpy/polynomial/tests/__pycache__/test_chebyshev.cpython-310.pyc,,
numpy/polynomial/tests/__pycache__/test_classes.cpython-310.pyc,,
numpy/polynomial/tests/__pycache__/test_hermite.cpython-310.pyc,,
numpy/polynomial/tests/__pycache__/test_hermite_e.cpython-310.pyc,,
numpy/polynomial/tests/__pycache__/test_laguerre.cpython-310.pyc,,
numpy/polynomial/tests/__pycache__/test_legendre.cpython-310.pyc,,
numpy/polynomial/tests/__pycache__/test_polynomial.cpython-310.pyc,,
numpy/polynomial/tests/__pycache__/test_polyutils.cpython-310.pyc,,
numpy/polynomial/tests/__pycache__/test_printing.cpython-310.pyc,,
numpy/polynomial/tests/__pycache__/test_symbol.cpython-310.pyc,,
numpy/polynomial/tests/test_chebyshev.py,sha256=PI2XwvGGqQKEB1RxbsYRgeTG0cunB_8Otd9SBJozq-8,21141
numpy/polynomial/tests/test_classes.py,sha256=VCcG2ICOteBolQHyfzYzMUhyqHlbAJxV8LdQm9NO50U,19057
numpy/polynomial/tests/test_hermite.py,sha256=zGYN24ia2xx4IH16D6sfAxIipnZrGrIe7D8QMJZPw4Y,19132
numpy/polynomial/tests/test_hermite_e.py,sha256=5ZBtGi2gkeldYVSh8xlQOLUDW6fcT4YdZiTrB6AaGJU,19467
numpy/polynomial/tests/test_laguerre.py,sha256=hBgo8w_3iEQosX2CqjTkUstTiuTPLZmfQNQtyKudZLo,18048
numpy/polynomial/tests/test_legendre.py,sha256=v3ajjp0sg1o7njoLhbPftxaIWaxpY0pBp1suImZqJMw,19241
numpy/polynomial/tests/test_polynomial.py,sha256=CVy9ABfF8c-ZUsekP_bAkv7qQldHIAbzIQSLiPCHKXk,22669
numpy/polynomial/tests/test_polyutils.py,sha256=b3vdtJVjC34AmEv96sw2IvIABNDqmYhCnMYZCvhtWzU,3897
numpy/polynomial/tests/test_printing.py,sha256=PWo6ijsIMYXo372gk6Y7GDeq1I3XqUUjOdh2_mS47Yc,21885
numpy/polynomial/tests/test_symbol.py,sha256=GZnqB4PLjZDWalREVOAI3qus9kjUDhCW-WZ_87jRmPY,5588
numpy/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/LICENSE.md,sha256=tLwvT6HJV3jx7T3Y8UcGvs45lHW5ePnzS1081yUhtIo,3582
numpy/random/__init__.pxd,sha256=g3EaMi3yfmnqT-KEWj0cp6SWIxVN9ChFjEYXGOfOifE,445
numpy/random/__init__.py,sha256=W_hFzGsKVQfdh3-U15gzsOKKAk8uZgioDkxKyuou4WA,7721
numpy/random/__init__.pyi,sha256=eQomej0oB0FQL4kSM_DQhLuLcaFRkjyhpJy7E6EFgZ8,2194
numpy/random/__pycache__/__init__.cpython-310.pyc,,
numpy/random/__pycache__/_pickle.cpython-310.pyc,,
numpy/random/_bounded_integers.cp310-win_amd64.lib,sha256=IDGLivUmn_bygKOZoDHms77zQILrnXXjos7AIibcQsE,18000
numpy/random/_bounded_integers.cp310-win_amd64.pyd,sha256=Qfkm_ZLmr7-mNprUa-oVYlzf1EJgI7DAj0xjQ1jPmy8,252928
numpy/random/_bounded_integers.pxd,sha256=EOKKUlF9bh0CLNEP8TzXzX4w_xV5kivr1Putfdf6yvU,1763
numpy/random/_common.cp310-win_amd64.lib,sha256=1xwQHVlnmE5DWaXcp0JthdISxrDv5m096sjUuxYegd8,2012
numpy/random/_common.cp310-win_amd64.pyd,sha256=SxPbRwa5kG_J__p0-1X-vjxJ6s_qFrQK2p3Z_57Ze7Y,176128
numpy/random/_common.pxd,sha256=2_9NLWFSnLG4iDd-KeYUBRa47QM8qceUsPiAkyWZ74I,5089
numpy/random/_examples/cffi/__pycache__/extending.cpython-310.pyc,,
numpy/random/_examples/cffi/__pycache__/parse.cpython-310.pyc,,
numpy/random/_examples/cffi/extending.py,sha256=BgydYEYBb6hDghMF-KQFVc8ssUU1F5Dg-3GyeilT3Vg,920
numpy/random/_examples/cffi/parse.py,sha256=rbi3NF6bhyk35yhgK1j8fFeqlfO9Om8vjS84Jg8GX20,1825
numpy/random/_examples/cython/extending.pyx,sha256=RmpxvFfGsAGZwCY78LWrfpa307NG7vrE64TIiIpKEA4,2368
numpy/random/_examples/cython/extending_distributions.pyx,sha256=1zrMvPbKi0RinyZ93Syyy4OXGEOzAAKHSzTmDtN09ZY,3987
numpy/random/_examples/cython/meson.build,sha256=q_IFcVs_qzERJD_-8uaDnjps3QdaW49okZMbFtwkAPo,1747
numpy/random/_examples/numba/__pycache__/extending.cpython-310.pyc,,
numpy/random/_examples/numba/__pycache__/extending_distributions.cpython-310.pyc,,
numpy/random/_examples/numba/extending.py,sha256=vnqUqQRvlAI-3VYDzIxSQDlb-smBAyj8fA1-M2IrOQw,2041
numpy/random/_examples/numba/extending_distributions.py,sha256=-aTxLIqnXW0XPtmEp0yJfaBTBcjEo9Q9SebKG_dOLvw,2103
numpy/random/_generator.cp310-win_amd64.lib,sha256=Qd_YhzkaMq0j0Svn092f5Uvmub_3ppeh-l7Ug05CceM,18400
numpy/random/_generator.cp310-win_amd64.pyd,sha256=IXCHvAVzeGoA3fEMKZ3JCSeyHvNO9AVNVvlwv2_Ai5o,754176
numpy/random/_generator.pyi,sha256=ux5YhIqp2qcCijUx3QopSYJwkSQftP1xKrcLMUN76MY,25392
numpy/random/_mt19937.cp310-win_amd64.lib,sha256=Y8OcvzbXYjj69KNlP3Q7yX2u7QAwaNsGvzKDCzaIFGY,2032
numpy/random/_mt19937.cp310-win_amd64.pyd,sha256=JDsOs4POn5Rczjc0xE5EwBDNTBvyLyygG9jhAJjyjew,88576
numpy/random/_mt19937.pyi,sha256=s3qqDpQ5p6G3TS7135oBK3MtEt0uZZteQDVNeeGlbIk,747
numpy/random/_pcg64.cp310-win_amd64.lib,sha256=zQtrEu4Fq252LKPxg1SIZ9w6VlmPN3TkFISDzwHeU74,1996
numpy/random/_pcg64.cp310-win_amd64.pyd,sha256=IfQkDJUxjFnB2mnGsTyFep4i5k8Q3Nqy0RXZjQZ8pr4,97792
numpy/random/_pcg64.pyi,sha256=Q-QetvAEmjzguUzTFe9WyNjouYT4AdB3t4TP7Rv_h9A,1133
numpy/random/_philox.cp310-win_amd64.lib,sha256=35n2j1ddlGsACD9gjByF65MRYWxbn3DxXSOERzat5as,2012
numpy/random/_philox.cp310-win_amd64.pyd,sha256=CyIaGZdgohbMX-KtXE2I5ZipcBBjhdLuqDhm6NpE4lc,81920
numpy/random/_philox.pyi,sha256=mxF1_5G3eniJ6MrmidXQJM9VZCA_RWM9SOpQmbuJN1U,991
numpy/random/_pickle.py,sha256=D5MrszR_oDD4yss3bt94MPw54FNIyH6f4MtOWBYRDvk,2832
numpy/random/_sfc64.cp310-win_amd64.lib,sha256=HBxGn0olKDEg8hoUhYE3DDTpaXIFISIYpwrp6QMia7E,1996
numpy/random/_sfc64.cp310-win_amd64.pyd,sha256=HYyFukJwQTWEdwRTn4t6JnWPtWhiWLluJjxJRYQaNhU,61952
numpy/random/_sfc64.pyi,sha256=-SVBWvTRAKnWTBNYuvNI_tcwUReb1XPhuHoSopP-hOQ,657
numpy/random/bit_generator.cp310-win_amd64.lib,sha256=lzt6E81_MHxVQxzcIcWsvj4JDMKzZb2swXd5fGTOv_w,2120
numpy/random/bit_generator.cp310-win_amd64.pyd,sha256=LHWYtqvglLV6ZHOiTEKWjNwGqbZJ3vSp2RfamTO87IM,174592
numpy/random/bit_generator.pxd,sha256=LJpeB-EKeVV8_JO69sS33XJLZQ3DAhrUCNzs_ei7AoI,1042
numpy/random/bit_generator.pyi,sha256=oAB6QdmtR5qNSHVRUccZFA9bYfUlRuAwi8rKjLBKd5k,3719
numpy/random/c_distributions.pxd,sha256=VnYwdkMQmLp2rU-fT0Dvj0AhLirSpE5EirMe7iNcTTQ,6464
numpy/random/lib/npyrandom.lib,sha256=hgXGCY81ahUOj2RNt4adoSb4UTNmpC8VKUub07ilGXM,148178
numpy/random/mtrand.cp310-win_amd64.lib,sha256=Qp2sqvZLa4D1VY9qOLGmsaMZSsOYpZy9tEG6LVUchLo,17122
numpy/random/mtrand.cp310-win_amd64.pyd,sha256=Vu-gqs6OaQn7moJ4Kwr250aCByK5iS2WE4on9ttEj78,643072
numpy/random/mtrand.pyi,sha256=sxRVq8FvvUN3U-ylFLFZ_a_c3Mv2gzTOjMfWqqq_rj0,23122
numpy/random/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/__pycache__/__init__.cpython-310.pyc,,
numpy/random/tests/__pycache__/test_direct.cpython-310.pyc,,
numpy/random/tests/__pycache__/test_extending.cpython-310.pyc,,
numpy/random/tests/__pycache__/test_generator_mt19937.cpython-310.pyc,,
numpy/random/tests/__pycache__/test_generator_mt19937_regressions.cpython-310.pyc,,
numpy/random/tests/__pycache__/test_random.cpython-310.pyc,,
numpy/random/tests/__pycache__/test_randomstate.cpython-310.pyc,,
numpy/random/tests/__pycache__/test_randomstate_regression.cpython-310.pyc,,
numpy/random/tests/__pycache__/test_regression.cpython-310.pyc,,
numpy/random/tests/__pycache__/test_seed_sequence.cpython-310.pyc,,
numpy/random/tests/__pycache__/test_smoke.cpython-310.pyc,,
numpy/random/tests/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/data/__pycache__/__init__.cpython-310.pyc,,
numpy/random/tests/data/generator_pcg64_np121.pkl.gz,sha256=EfQ-X70KkHgBAFX2pIPcCUl4MNP1ZNROaXOU75vdiqM,203
numpy/random/tests/data/generator_pcg64_np126.pkl.gz,sha256=fN8deNVxX-HELA1eIZ32kdtYvc4hwKya6wv00GJeH0Y,208
numpy/random/tests/data/mt19937-testset-1.csv,sha256=bA5uuOXgLpkAwJjfV8oUePg3-eyaH4-gKe8AMcl2Xn0,16845
numpy/random/tests/data/mt19937-testset-2.csv,sha256=SnOL1nyRbblYlC254PBUSc37NguV5xN-0W_B32IxDGE,16826
numpy/random/tests/data/pcg64-testset-1.csv,sha256=wHoS7fIR3hMEdta7MtJ8EpIWX-Bw1yfSaVxiC15vxVs,24840
numpy/random/tests/data/pcg64-testset-2.csv,sha256=6vlnVuW_4i6LEsVn6b40HjcBWWjoX5lboSCBDpDrzFs,24846
numpy/random/tests/data/pcg64dxsm-testset-1.csv,sha256=Fhha5-jrCmRk__rsvx6CbDFZ7EPc8BOPDTh-myZLkhM,24834
numpy/random/tests/data/pcg64dxsm-testset-2.csv,sha256=mNYzkCh0NMt1VvTrN08BbkpAbfkFxztNcsofgeW_0ns,24840
numpy/random/tests/data/philox-testset-1.csv,sha256=QvpTynWHQjqTz3P2MPvtMLdg2VnM6TGTpXgp-_LeJ5g,24853
numpy/random/tests/data/philox-testset-2.csv,sha256=-BNO1OCYtDIjnN5Q-AsQezBCGmVJUIs3qAMyj8SNtsA,24839
numpy/random/tests/data/sfc64-testset-1.csv,sha256=sgkemW0lbKJ2wh1sBj6CfmXwFYTqfAk152P0r8emO38,24841
numpy/random/tests/data/sfc64-testset-2.csv,sha256=mkp21SG8eCqsfNyQZdmiV41-xKcsV8eutT7rVnVEG50,24834
numpy/random/tests/data/sfc64_np126.pkl.gz,sha256=MVa1ylFy7DUPgUBK-oIeKSdVl4UYEiN3AZ7G3sdzzaw,290
numpy/random/tests/test_direct.py,sha256=9LzX-v2YlylwqU2FRD7ANRlVIs1MTDflds0nlHZ2zgw,19812
numpy/random/tests/test_extending.py,sha256=Lt20n7fkccQX-8U2uTf4dexw2DGozBMzhUvAH-1GLUI,4107
numpy/random/tests/test_generator_mt19937.py,sha256=Wga14H0GZM0WbttQI6TItLXpCTLWaJzr7fub3jmmp2I,120091
numpy/random/tests/test_generator_mt19937_regressions.py,sha256=5wlQqn6jdLwPbGNZrF3RPwLn_xRj2CCA6DY167dHN7c,8300
numpy/random/tests/test_random.py,sha256=TW-ikZicDVgTi9WeZOQwLCCCZ_Q_gWAom6PoztXSZ5k,71901
numpy/random/tests/test_randomstate.py,sha256=RrgFeK2r5JcD4K8paWObS8nKufdGumLN2fdnvp974kI,87399
numpy/random/tests/test_randomstate_regression.py,sha256=8FL4sxX1D1oMVX_F9u5vR8Zazo5V0Yj4bL7zsh57V-Y,8215
numpy/random/tests/test_regression.py,sha256=_eoEa-QIYh33tESahMHsVZtCy9W_s5T5RPzI6QYS7LY,5611
numpy/random/tests/test_seed_sequence.py,sha256=zWUvhWDxBmTN2WteSFQeJ29W0-2k3ZUze_3YtL4Kgms,3391
numpy/random/tests/test_smoke.py,sha256=StTxeemamKeE_H_UHQWyDxIXJSbLQI4Yr5sDp3y6ZH4,28992
numpy/rec/__init__.py,sha256=SMM69A-UzX5LD6JxSYXO-M9t4grwzRcqSAXXuMU5PSY,85
numpy/rec/__init__.pyi,sha256=yNYXrAepUnWmdsniXDPYVxly2MZSL8KALoXoqKAlAkc,310
numpy/rec/__pycache__/__init__.cpython-310.pyc,,
numpy/strings/__init__.py,sha256=NLFxhadn513TAXf8kgVguCvmyzXnP1JpVnNJtqfErX4,85
numpy/strings/__init__.pyi,sha256=O39lhLmQsn4x8hpDK-W4Ue-ErWICVcQ_KShY20VVc0Y,1263
numpy/strings/__pycache__/__init__.cpython-310.pyc,,
numpy/testing/__init__.py,sha256=ENc09IN_D74xNvH33Z65Q2dkaSEvljHF_tz-BV-g_dU,617
numpy/testing/__init__.pyi,sha256=SqxF3NYTPzIpKCuUFJRibBgTnZ8zYHyQbIl7jC1JLL4,1703
numpy/testing/__pycache__/__init__.cpython-310.pyc,,
numpy/testing/__pycache__/overrides.cpython-310.pyc,,
numpy/testing/__pycache__/print_coercion_tables.cpython-310.pyc,,
numpy/testing/_private/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/_private/__pycache__/__init__.cpython-310.pyc,,
numpy/testing/_private/__pycache__/extbuild.cpython-310.pyc,,
numpy/testing/_private/__pycache__/utils.cpython-310.pyc,,
numpy/testing/_private/extbuild.py,sha256=DVrgyVcss6fqiEJXu89wqWXswLscv48phZRUVCAPogg,8367
numpy/testing/_private/utils.py,sha256=TkFcIVvNqoBYzhJ8PPMrlREp1UdJ13eO2BmtrbNE4-c,96287
numpy/testing/_private/utils.pyi,sha256=DbxZ_5oUDnqPxeXI2392krjqCQdkZTlbtwn7oeOo3IY,10642
numpy/testing/overrides.py,sha256=9EH3_ISI5y_oqwhO8hS0EWieAd5QQM2KsLl5i0bviXo,2208
numpy/testing/print_coercion_tables.py,sha256=BGTgZxvxnUNYqOwsceMR9xQ1LD6QUePsKLBsq8c8Vyo,6424
numpy/testing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/tests/__pycache__/__init__.cpython-310.pyc,,
numpy/testing/tests/__pycache__/test_utils.cpython-310.pyc,,
numpy/testing/tests/test_utils.py,sha256=fEV3151npMqb1cXYRHPQfEtj8MfBACVPyo9qhNZBWtQ,72390
numpy/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/tests/__pycache__/__init__.cpython-310.pyc,,
numpy/tests/__pycache__/test__all__.cpython-310.pyc,,
numpy/tests/__pycache__/test_configtool.cpython-310.pyc,,
numpy/tests/__pycache__/test_ctypeslib.cpython-310.pyc,,
numpy/tests/__pycache__/test_lazyloading.cpython-310.pyc,,
numpy/tests/__pycache__/test_matlib.cpython-310.pyc,,
numpy/tests/__pycache__/test_numpy_config.cpython-310.pyc,,
numpy/tests/__pycache__/test_numpy_version.cpython-310.pyc,,
numpy/tests/__pycache__/test_public_api.cpython-310.pyc,,
numpy/tests/__pycache__/test_reloading.cpython-310.pyc,,
numpy/tests/__pycache__/test_scripts.cpython-310.pyc,,
numpy/tests/__pycache__/test_warnings.cpython-310.pyc,,
numpy/tests/test__all__.py,sha256=JziA96KUyXwWCPExbQcJBqe_RU1xQVrVwi1xhO8tzqM,230
numpy/tests/test_configtool.py,sha256=goqOIpRq8Hrig_d6vxZGu8zluQManELhkGGDl3g9qto,1598
numpy/tests/test_ctypeslib.py,sha256=PSiQsEpT3CoLFp56zntAEkaJJ1VMHkvE0pr8-infzKM,12728
numpy/tests/test_lazyloading.py,sha256=Z01x6jxk94e2HPoHdlHBgmgHjm9tNDE09kuJmT-DYFo,1200
numpy/tests/test_matlib.py,sha256=TUaQmGoz9fvQQ8FrooTq-g9BFiViGWjoTIGQSUUF6-Y,1910
numpy/tests/test_numpy_config.py,sha256=xp036ZX3-R20FjGn4-okdkFPjkThQhYdFtGt5MO65sI,1285
numpy/tests/test_numpy_version.py,sha256=n4cggUNnM9okmtxwyhYBWBFwJvKpY7NzYxMgrNwRU40,1808
numpy/tests/test_public_api.py,sha256=TVcKD5Bwu5ZHuuZMEc-HUZKu5_YYGqfp9-iwo3wDCSo,23511
numpy/tests/test_reloading.py,sha256=spEldUm_nmV0tBoUG53a2ORCOjwfltimpKfGGTqa7pI,2441
numpy/tests/test_scripts.py,sha256=6rZN5bnGpeR4vEjLBiKEUMXJiE2NVnbY1Q8xKPlOqA8,1692
numpy/tests/test_warnings.py,sha256=iAipwlsADKIY0BdRHd6oRv4RzOI0p0nxcqSr9DoqeLI,2422
numpy/typing/__init__.py,sha256=rGl883L4FnRPSzNe1Zyz7_KrHvxIMobSMoLuGPPhKNI,5442
numpy/typing/__pycache__/__init__.cpython-310.pyc,,
numpy/typing/__pycache__/mypy_plugin.cpython-310.pyc,,
numpy/typing/mypy_plugin.py,sha256=8RP5idJlHB1aSj86jatiqWxAmFcNQgKvkNVuJqpU2nY,6606
numpy/typing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/typing/tests/__pycache__/__init__.cpython-310.pyc,,
numpy/typing/tests/__pycache__/test_isfile.cpython-310.pyc,,
numpy/typing/tests/__pycache__/test_runtime.cpython-310.pyc,,
numpy/typing/tests/__pycache__/test_typing.cpython-310.pyc,,
numpy/typing/tests/data/fail/arithmetic.pyi,sha256=1u4oSD5crYgB2TSyua5YqxDs1DuY1ZZrOwEwJRnVZUQ,3901
numpy/typing/tests/data/fail/array_constructors.pyi,sha256=mrcArR9EVNE4-9yKg-SgVv_Yp-4DpZ1Q_0cHiRwXRtI,1163
numpy/typing/tests/data/fail/array_like.pyi,sha256=MUIx6Oc5bJeebr-TC4FhZFXnX9pJ5gQDv8moHmPek10,471
numpy/typing/tests/data/fail/array_pad.pyi,sha256=JGCMd_sRBYlsPQ2d7EfLaNooTsg1P0jBuD5Ds2MeXAg,138
numpy/typing/tests/data/fail/arrayprint.pyi,sha256=sj4gU-mBy7yOa9l-D4ARxGc__U0P880TtcATh_AJOCo,606
numpy/typing/tests/data/fail/arrayterator.pyi,sha256=tRPWjCh1-sg5FXAyYeTbHSR983JUFlecRNcustDLt4E,484
numpy/typing/tests/data/fail/bitwise_ops.pyi,sha256=QglRyKkdf96Z-klBfGQ1JSmtOFk3yeSDFz0MqKS-rj0,604
numpy/typing/tests/data/fail/char.pyi,sha256=m8SxJUaMSj2SWFHhjtJHj0b1KMPg7f1tXBjpPG_pEso,2781
numpy/typing/tests/data/fail/chararray.pyi,sha256=2cRv7u1puCq1dhooEVik_gQDoocnoZENt2J7FRwE_Cs,2368
numpy/typing/tests/data/fail/comparisons.pyi,sha256=xrNXGulq1kVRufLUB7nG95g_YNr_wR5hbIdhy0tkRMc,849
numpy/typing/tests/data/fail/constants.pyi,sha256=3IZ6T9p4n61qIXngrHB8VqEaqloxcNmbUz3YcSqNSXI,88
numpy/typing/tests/data/fail/datasource.pyi,sha256=mX9ucsgNXNekVFuRVzBjleA-p8GpuwpbsHqiG6a9CpA,420
numpy/typing/tests/data/fail/dtype.pyi,sha256=ltT4BFaX_KTVdRLw2dMg3_OiSNYjDSNrXsxby6eeLTw,354
numpy/typing/tests/data/fail/einsumfunc.pyi,sha256=dYOaJ0J4EUzdyUBikKHie99K8SMaYrlqN3R9aDcMeJ4,499
numpy/typing/tests/data/fail/false_positives.pyi,sha256=TKmRWDjlfVP2rgZczUMXcm9l0maPLDf7bSBon4Xfakw,377
numpy/typing/tests/data/fail/flatiter.pyi,sha256=u4-JnRsydg5BW3OcA9we8MXLJ6F5cuaxxw0BrHVA9kY,891
numpy/typing/tests/data/fail/fromnumeric.pyi,sha256=1kB7P_OXW0Ob63tf4iYEtgSLWDPQcWKSMrLxNrczVb4,5752
numpy/typing/tests/data/fail/histograms.pyi,sha256=JteTXgK_kXD8UPdihMZ_T2VcM3rTBj6t-MMRP8UHvhw,379
numpy/typing/tests/data/fail/index_tricks.pyi,sha256=63ADYRCVtf0Dapc2dJpYJZDSIXK3MhhW_1lG30d3-RY,523
numpy/typing/tests/data/fail/lib_function_base.pyi,sha256=wOI2CEvJZEGDysR8Oas2QZN8INVdys1FPN4QstLyE8I,2001
numpy/typing/tests/data/fail/lib_polynomial.pyi,sha256=PM1TD9h4tFNeMp4y6HlXHKuAHDW0bfNHw0UWLUHnLVk,928
numpy/typing/tests/data/fail/lib_utils.pyi,sha256=chR5zMEM5KI2Aw0LPIlIC8CnEcPIHwyKMLzbPhXNYXU,99
numpy/typing/tests/data/fail/lib_version.pyi,sha256=JWtuTLcjkZpGfXshlFpJO5vINxawn9S-mxLGH0-7kcw,164
numpy/typing/tests/data/fail/linalg.pyi,sha256=j6GGpOENz0nuZsza0Dyfy6MtjfRltqrbY8K_7g5H92I,1370
numpy/typing/tests/data/fail/memmap.pyi,sha256=eAX-nEKtOb06mL8EPECukmL8MwrehSVRu5TBlHiSBaQ,164
numpy/typing/tests/data/fail/modules.pyi,sha256=xkoJ-zYQtGdeeUTjlOuBmLRkxFgGk_YGD74skrXWQtI,688
numpy/typing/tests/data/fail/multiarray.pyi,sha256=AMsYk58-B30xQTHirBGAC6vykmauw-S7H_YiHSLOAQA,1696
numpy/typing/tests/data/fail/ndarray.pyi,sha256=5A83TCpAmaUC0rtOU0NVG0vsNfKo_-1SF5qtVT7eqoc,415
numpy/typing/tests/data/fail/ndarray_misc.pyi,sha256=lKMJRh-0D84J1VaeeF12RbhloshyA19GWiGKpQbBPlg,1376
numpy/typing/tests/data/fail/nditer.pyi,sha256=We6p5_nmfUdd_4CtwYZc5O7MTSMyM-Xw7mEUzdKPcP4,333
numpy/typing/tests/data/fail/nested_sequence.pyi,sha256=7E1zJ2SZIF0ldbEmjtA_Bp6cV4Q-cS4Op0BJN3Vi3rc,444
numpy/typing/tests/data/fail/npyio.pyi,sha256=XPFPjSKKBX5FRc_8FyO_6-X9LGzqQdgUKdKhxqe7RIk,541
numpy/typing/tests/data/fail/numerictypes.pyi,sha256=wPJaHwMdiX1tJLdnYAgZ5z42tEhX-8EtGfWKU81czf4,125
numpy/typing/tests/data/fail/random.pyi,sha256=v_Y-EfhC7PC8E3AH-v-AfiZVlJDSShL77WQ3yXWx5iE,2883
numpy/typing/tests/data/fail/rec.pyi,sha256=BxH41lR1wLvLrlash9mzkPFngDAXSPQQXvuHxYylHAI,721
numpy/typing/tests/data/fail/scalars.pyi,sha256=75iAXKKdouG6Qkq1d-iMTgZh0Eb-751Zk5fpGFqdm-E,3043
numpy/typing/tests/data/fail/shape.pyi,sha256=-SzfxgevV7APDLlq-Sh8KzsKdCjHUb5GXEeJ9H6tacQ,143
numpy/typing/tests/data/fail/shape_base.pyi,sha256=ZU1KSP0k-i-npwIMUhp42-EMzrdZhOqPEnV8ah-ZJ6U,160
numpy/typing/tests/data/fail/stride_tricks.pyi,sha256=L0fJGun6CDq24yNdw2zeNVGGcIpEOyP2dmWj1pEbMz8,324
numpy/typing/tests/data/fail/strings.pyi,sha256=nFnnRCF8j6jTjptkRCNs-BNAQLBAcNaTaqEq-N-Sh5Q,2911
numpy/typing/tests/data/fail/testing.pyi,sha256=O1nk5xnSvKn7aAHNi3mMLYIr75ym5WIT-BvZemEnayQ,1398
numpy/typing/tests/data/fail/twodim_base.pyi,sha256=wzd-h1ye2BhMdIHlQ0ZcHfgYRBHVX2GJ3WGfMk5euPg,935
numpy/typing/tests/data/fail/type_check.pyi,sha256=0KG0c2LNUbUFChTYtbJ38eJUmfvUJl4Cn5G0vh1Bkrw,392
numpy/typing/tests/data/fail/ufunc_config.pyi,sha256=WzZzWJ-cC39qAzak3Cf--XIZX11MqwsEa3bYYyzqsvY,755
numpy/typing/tests/data/fail/ufunclike.pyi,sha256=89Fjsr7vmurRl90mVbC5L0xOwRIk0jg4mJrgkTDn4eM,648
numpy/typing/tests/data/fail/ufuncs.pyi,sha256=2ATU0I4ZF8DB3vyodRDJIuXnXb-CcQpt-l4Kn00kJxA,493
numpy/typing/tests/data/fail/warnings_and_errors.pyi,sha256=4sTfiur0rV5CpjlYJC_1WV3KPnovteiImffvpYh19eU,190
numpy/typing/tests/data/misc/extended_precision.pyi,sha256=RTsXUAM9iKX_L-iviwFVuUwKcqX9N8sRW5ZHAXjYtjc,909
numpy/typing/tests/data/mypy.ini,sha256=-DlViGsyN4XPZs-IXa4x5G-0oEIyokEVTt0FxHvr5zU,175
numpy/typing/tests/data/pass/__pycache__/arithmetic.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/array_constructors.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/array_like.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/arrayprint.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/arrayterator.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/bitwise_ops.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/comparisons.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/dtype.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/einsumfunc.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/flatiter.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/fromnumeric.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/index_tricks.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/lib_utils.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/lib_version.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/literal.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/ma.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/mod.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/modules.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/multiarray.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_conversion.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_misc.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_shape_manipulation.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/numeric.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/numerictypes.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/random.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/scalars.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/shape.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/simple.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/simple_py3.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufunc_config.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufunclike.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufuncs.cpython-310.pyc,,
numpy/typing/tests/data/pass/__pycache__/warnings_and_errors.cpython-310.pyc,,
numpy/typing/tests/data/pass/arithmetic.py,sha256=URqU-4kfIIAo-PLaUUAerXr4HuFzQEZzJe0Er0uYaP0,8127
numpy/typing/tests/data/pass/array_constructors.py,sha256=MGzgCt7uTeC_b7wU2aPlvTuDzXfgOujx_lR0Vqfpny8,2584
numpy/typing/tests/data/pass/array_like.py,sha256=h7P3QdJ_eTAmYImblwJxhe8bVGzi5RWjMNyLTkUDdNk,1061
numpy/typing/tests/data/pass/arrayprint.py,sha256=NTw1gJ9v3TDVwRov4zsg_27rI-ndKuG4mDidBWEKVyc,803
numpy/typing/tests/data/pass/arrayterator.py,sha256=z4o0H08T7tbzzMWhu5ZXdVqbivjBicuFgRHBk_lpOck,420
numpy/typing/tests/data/pass/bitwise_ops.py,sha256=8lfjgayfTDDcWi1O-rnxLu4FZqvskvGHvFXJpMQWQgc,1095
numpy/typing/tests/data/pass/comparisons.py,sha256=TWpd4WRFJ6KwVsrN_U0RUYcQpTSSccw8uJKBUwAe8DA,3294
numpy/typing/tests/data/pass/dtype.py,sha256=YRsTwKEQ5iJtdKCEQIybU_nL8z8Wq9hU-BZmEO7HjQE,1127
numpy/typing/tests/data/pass/einsumfunc.py,sha256=CXdLvQsU2iDqQc7d2TRRCSwguQzJ0SJDFn23SDeOOuY,1406
numpy/typing/tests/data/pass/flatiter.py,sha256=2xtMPvDgfhgjZIqiN3B3Wvy6Q9oBeo9uh4UkCAQNmwg,190
numpy/typing/tests/data/pass/fromnumeric.py,sha256=bP0hEQYYQJOn7-ce0rAf8cvuxZX3Ja6GSSlCtNhEBUM,4263
numpy/typing/tests/data/pass/index_tricks.py,sha256=Vn5iEuWlNdbr03zMEwAHvjBgI25-uCqRAJfUvRVWSp0,1556
numpy/typing/tests/data/pass/lib_utils.py,sha256=XEc0v7bwES-C5D4GkSJQSSTSAl5ng7tq6tCWj3jxbCM,336
numpy/typing/tests/data/pass/lib_version.py,sha256=TlLZK8sekCMm__WWo22FZfZc40zpczENf6y_TNjBpCw,317
numpy/typing/tests/data/pass/literal.py,sha256=EAz1BK2ikHufoUCMJvJRtHwViQqpVrRW5AgDZvnARWE,1408
numpy/typing/tests/data/pass/ma.py,sha256=LfK4LXCWLLK5q0c1Me8STWbhGj9b_46LYvZwXGpaEjQ,179
numpy/typing/tests/data/pass/mod.py,sha256=L1qLwjdrRo9Tx7mxWpf_ugdKdUprDYhPRbCvQd5QjXY,1725
numpy/typing/tests/data/pass/modules.py,sha256=buzLurat4TIGmJuW3mGsGk7dKNmpBDfQOWWQXFfb9Uc,670
numpy/typing/tests/data/pass/multiarray.py,sha256=i6VU-VN96Q16mRGzVoY3oTE2W1z16GOGTOVFxWGRacM,1407
numpy/typing/tests/data/pass/ndarray_conversion.py,sha256=6TnvucV8Vtte7dGWihx7YmrHlNOanqmLJIH1W8Wok0E,1612
numpy/typing/tests/data/pass/ndarray_misc.py,sha256=-1HK6vqTlKl5P7rU1zEM3UGHHWndl8F4lajIvKIQPaI,2795
numpy/typing/tests/data/pass/ndarray_shape_manipulation.py,sha256=yaBK3hW5fe2VpvARkn_NMeF-JX-OajI8JiRWOA_Uk7Y,687
numpy/typing/tests/data/pass/numeric.py,sha256=D-QAh75OtWLwYE_qkPiOGYR2M4GxJWbby-UIuwFRkAQ,1622
numpy/typing/tests/data/pass/numerictypes.py,sha256=JaCjk4zQPOI67XzqGyi3dI-GUMFM2AvDuniwzSQ7_Rk,348
numpy/typing/tests/data/pass/random.py,sha256=3Uw-6pRXWR_O4Q0BsSHQUh5ZigQ1E7fIP2AFCI9bLMg,63307
numpy/typing/tests/data/pass/scalars.py,sha256=9rfIxadM9yVyOYVgkgVPnRgIkARJ0g6vf1LKq5NIlaM,3633
numpy/typing/tests/data/pass/shape.py,sha256=pBfK9Lc5lKUQ81WYE5N4KDIG9czMp5Vw7YdyZwM8jMw,387
numpy/typing/tests/data/pass/simple.py,sha256=M0nNgtpPOIJMRDy0rqgiCVEpbQJOh2iYX8zTiX6lL5U,2901
numpy/typing/tests/data/pass/simple_py3.py,sha256=OBpoDmf5u4bRblugokiOZzufESsEmoU03MqipERrjLg,102
numpy/typing/tests/data/pass/ufunc_config.py,sha256=gmMTPrq8gLXJZSBQoOpJcgzIzWgMx-k_etKPV4KSTJk,1269
numpy/typing/tests/data/pass/ufunclike.py,sha256=jxTR61d0bmFg7JHZmw992ccRua00u4XWJYtcQRJwFS0,1172
numpy/typing/tests/data/pass/ufuncs.py,sha256=gvdcCNoGUfN0CnQmn6k1j6ghdt8zGkJdcRcgctmU48A,438
numpy/typing/tests/data/pass/warnings_and_errors.py,sha256=q3c1SmMwhyYLYQsLjK02AXphk3-96YltSTdTfrElJzQ,167
numpy/typing/tests/data/reveal/arithmetic.pyi,sha256=xVHIfSRxs7Dy3VJkBdfxIkUppDzedOBns1pYxDGQ18g,20278
numpy/typing/tests/data/reveal/array_api_info.pyi,sha256=oJ_zwKiJZhBmkg4P3v7udjnpVbcWlUkCDbDdebPdhQQ,3218
numpy/typing/tests/data/reveal/array_constructors.pyi,sha256=bsBDUrkNzO7kU52UMKHC9P0G9hVHHIyg4z9pzny9r9g,11232
numpy/typing/tests/data/reveal/arraypad.pyi,sha256=jNYVO_bhcfiS4qD2yjdh9zHHfYwTXlpbS0S4monXg3A,804
numpy/typing/tests/data/reveal/arrayprint.pyi,sha256=CWK-exqf0iJsgG0dy0aeYPy58YCpKt6v4wPILUthWGQ,936
numpy/typing/tests/data/reveal/arraysetops.pyi,sha256=WwNy5tZCasuchQumB3HnI_bOea_PAsVStn2GhKeUhDU,4584
numpy/typing/tests/data/reveal/arrayterator.pyi,sha256=kO3jaArSCHX8qfhWWwfSzpVGCar92Mncispc5DJHB_Q,1130
numpy/typing/tests/data/reveal/bitwise_ops.pyi,sha256=csNMKNCTTSzSiwUZUQ7SnIDWwcvbSXI4sc2vhp5NueI,4046
numpy/typing/tests/data/reveal/char.pyi,sha256=wSOObFlhK3KoMc5lJMS0tOef6y5oZPHxS8cUAknGCBA,7349
numpy/typing/tests/data/reveal/chararray.pyi,sha256=N5AtV6PN-DM_kb2dseZ1Hy3p2YiqkN7pd8kUpnuB1lY,6399
numpy/typing/tests/data/reveal/comparisons.pyi,sha256=Zne45zznKBLYTrf-a86CuOOytzL4edUb75ypwNMEniY,7583
numpy/typing/tests/data/reveal/constants.pyi,sha256=GYr4FEAuCiQi7AP4iSxAkCwkumdLQf_jNBaqi83jWFg,412
numpy/typing/tests/data/reveal/ctypeslib.pyi,sha256=Rk4vlh_5eZDrG2zhGrc2w4YtF85XdioEZEBvjUHCNC8,4910
numpy/typing/tests/data/reveal/datasource.pyi,sha256=Nc4eLV8-qg5wCfhmDCjEogQVMocfiTZgSSn-KyUwPQU,730
numpy/typing/tests/data/reveal/dtype.pyi,sha256=q_XtArthE1O1qfx6HXq2x_zSii01_OM5O3w0Ztm4ZNc,2959
numpy/typing/tests/data/reveal/einsumfunc.pyi,sha256=Hiz6uEzKhrdLZ5mLQouwwtzHSlqKq37UowcC_wxCgQY,2089
numpy/typing/tests/data/reveal/emath.pyi,sha256=cwFfmjIs6QoK7OrsaNUxWbshIRJCramjX4BM37n0LsM,2483
numpy/typing/tests/data/reveal/false_positives.pyi,sha256=BvCw4kn5DV8oDobFzgkpSJEUow1Hdh-FsAQZz0m29-A,500
numpy/typing/tests/data/reveal/fft.pyi,sha256=f5y4zbqvnby1QfKPxp-sVz4CEULTLwbxcBys_nToPJ0,1792
numpy/typing/tests/data/reveal/flatiter.pyi,sha256=yv6PNf5YWi1ENymuxSBAzGi3Gs4bUWu2XDR5-kL05t4,1523
numpy/typing/tests/data/reveal/fromnumeric.pyi,sha256=FbuTRwuO7wo3ZGXQihFoF2_HxPaA7JA_JMmsK53O16k,13590
numpy/typing/tests/data/reveal/getlimits.pyi,sha256=JVKbkYkD1XjBByQhV1808ZbpapPzwWZ5xfNDqo4O2OY,1698
numpy/typing/tests/data/reveal/histograms.pyi,sha256=NwulSnCG3PvG2lG4Twj1hFnHGBea1k9uJI2g1OkxaF8,1406
numpy/typing/tests/data/reveal/index_tricks.pyi,sha256=9qY9E5hidnC2sXVO5FUH-5kvZ3Twp5j1uZrA3NPAyc8,3647
numpy/typing/tests/data/reveal/lib_function_base.pyi,sha256=fZRwtAP7b43LdMEEC7pQ1FEZaaO8CCdSCktZ72Gsw5Q,9129
numpy/typing/tests/data/reveal/lib_polynomial.pyi,sha256=XYoK0oyRZ-wsf0OEZkQVv37EnXzEOF_Rb0BgDDpaF_s,6133
numpy/typing/tests/data/reveal/lib_utils.pyi,sha256=KjfK20MZyKC8vcfL2vLoQpSwbKt9hX3-QPcEZ65ZD3I,558
numpy/typing/tests/data/reveal/lib_version.pyi,sha256=GEI8Jy3t3L3C3nmh5NARPK_Duxpn53RmBs59MXkLmOc,697
numpy/typing/tests/data/reveal/linalg.pyi,sha256=TV4PRA4KOPB0sVNTuaRW-1b5miup5AMusQ-01s9n8G4,6458
numpy/typing/tests/data/reveal/matrix.pyi,sha256=DvXVYdI5Xi10UYGm-D1Ze0nBedsJFt8krmlGHfQ7xZ0,2994
numpy/typing/tests/data/reveal/memmap.pyi,sha256=6yhQzVryBCRhJkwsZsqcQNZZYfN-HHZ0F97EOa9SjrM,867
numpy/typing/tests/data/reveal/mod.pyi,sha256=mMmw_fxMky6HBQZvxS72Ndt1XCA9fxfvBkY_m-eceq8,5812
numpy/typing/tests/data/reveal/modules.pyi,sha256=Q4486mAOwGhMcsV8qSm1eNNsHC-4egMYODqLMpXrEa8,2014
numpy/typing/tests/data/reveal/multiarray.pyi,sha256=Gct30VyJCpySOMcVXhRlAA1zliOVcs0Z-raWJ1ejkGg,5292
numpy/typing/tests/data/reveal/nbit_base_example.pyi,sha256=XsPLKvd6Xg0EfoeT9AP8ZLc24Ml14TOpEA3hdq4nFJY,684
numpy/typing/tests/data/reveal/ndarray_conversion.pyi,sha256=oMFhNd0lPp1-dWWLIeihDsEN4o-FjBJsGXKMl9DYRjg,1917
numpy/typing/tests/data/reveal/ndarray_misc.pyi,sha256=9NHlmlT-oHFS4rpSqbX2d43u7215RQ16nEP8Xa-RHdQ,7587
numpy/typing/tests/data/reveal/ndarray_shape_manipulation.pyi,sha256=_hTO6zBAPMK-y1N9UU1CHweJhCUPcNqjXux_X8u0xig,1277
numpy/typing/tests/data/reveal/nditer.pyi,sha256=MNWZpoW7tuZmBKml94OIFB3vNAWDPgfSvsWrtlYmCcU,2076
numpy/typing/tests/data/reveal/nested_sequence.pyi,sha256=8eO-nOkd1p2u3KS3TvLAU5V53TBeFNIQzqJJt_x4UyM,766
numpy/typing/tests/data/reveal/npyio.pyi,sha256=Ynivm_--YlyomYBiMchSgBgyRPwVLSjJc2jgq5ZWkIk,3700
numpy/typing/tests/data/reveal/numeric.pyi,sha256=YXRmW12-nV0SYbSWGZphKGk5QvrxiqHNniqWRgp-qIg,6293
numpy/typing/tests/data/reveal/numerictypes.pyi,sha256=ypmfcunWMkicVEsEJGD8QdA6pmCQJFbHpqIR9ev7g-c,1445
numpy/typing/tests/data/reveal/polynomial_polybase.pyi,sha256=9UgUbpx5XfLZEOaI9UYAwR0DCIMC_Dah1KOawaxItm4,8327
numpy/typing/tests/data/reveal/polynomial_polyutils.pyi,sha256=xjCpd_WrRH4qCgmjw1umhwVMelGi3YXnRpXpI5Qq2nw,11129
numpy/typing/tests/data/reveal/polynomial_series.pyi,sha256=H9JotZXCh3FgspUlZNZiKO9AQg8geByHdftk5HqZMfM,7360
numpy/typing/tests/data/reveal/random.pyi,sha256=_pn-82DFJpjOgYmojqulWUb3uSy-jRPdPDBOpqgtnJ4,105948
numpy/typing/tests/data/reveal/rec.pyi,sha256=TT4ayH4Tw686cFGsa_IHyn0FwsVS7mXKLTiT52kAedo,4025
numpy/typing/tests/data/reveal/scalars.pyi,sha256=AU6fCqR9hwBnrdlukNxkvTkoD28aCoK_yQko_E8lITU,4749
numpy/typing/tests/data/reveal/shape.pyi,sha256=M0joDPodElAHLjI9FmofIgS45uSidjXOnfpbKwyBaZ8,307
numpy/typing/tests/data/reveal/shape_base.pyi,sha256=myZcRg9Y0pyx_spTFw45vB0-7EVhFoxu9R2SFpVq1nY,2192
numpy/typing/tests/data/reveal/stride_tricks.pyi,sha256=8NAfQ-N0cTc4Qj50NQQXWrJZjSl9PkMuyn22QroFhVM,1466
numpy/typing/tests/data/reveal/strings.pyi,sha256=6GpNLJGS5i7mP-Bz3sb2BjBMQdkS-EJyvx5vCjf8mA0,6510
numpy/typing/tests/data/reveal/testing.pyi,sha256=TtO2Zi6M8GKh4pYGTrvUbgKYWI_HsddGB9aM_DdRnXE,8813
numpy/typing/tests/data/reveal/twodim_base.pyi,sha256=iW8WgBMI298pHYtZ30aIJ0yNTjuVb422weoVOk871os,4543
numpy/typing/tests/data/reveal/type_check.pyi,sha256=kDl2RCWN7EJLd3ndgjHlflZlLgAvqSPFLOJ_iUr6Cdg,2838
numpy/typing/tests/data/reveal/ufunc_config.pyi,sha256=WPf6cIYrrLX-WqDezqXNmnB0M5N1LWXvNEGG3VLcCzk,1353
numpy/typing/tests/data/reveal/ufunclike.pyi,sha256=zXdhP4jFgruPbhb-rmT9DFm3XHdndwos6iRG_KLux00,1358
numpy/typing/tests/data/reveal/ufuncs.pyi,sha256=lwyhF0lJrE2ALDBpUh92iCCpGiybhOOASRy2lKzmcVs,4634
numpy/typing/tests/data/reveal/warnings_and_errors.pyi,sha256=KjmoWgviXUYyD7iBIvrkkImVELfWrP-ttbxql-tB4nE,565
numpy/typing/tests/test_isfile.py,sha256=slpVB1kHtrG5unlgYxl94Q_kOzDBPnDtFZQhLZdq9JM,897
numpy/typing/tests/test_runtime.py,sha256=p-Ydvt0Rt6mPHmAKYOOAGxxXQnjoARJSVZmViKMAX0A,3384
numpy/typing/tests/test_typing.py,sha256=GtYxVeorDdVa0VfjZU4C4POJN069lsu4NfcHsHfBR_4,8592
numpy/version.py,sha256=z5UxkYz6wlwRBTYw1cfZdbz7GVIk6X7kyybZHepIBns,304
numpy/version.pyi,sha256=lBxFpW0Ragtr6EJ-Nd3oZ5-ZN82qxli6waDgZbZ6otM,500
