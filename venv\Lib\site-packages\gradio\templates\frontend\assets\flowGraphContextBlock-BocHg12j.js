import{F as r}from"./KHR_interactivity-DTxiAnOo.js";import{R as s,b as t}from"./declarationMapper-BZjsjg7g.js";import{R as a}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class i extends r{constructor(e){super(e),this.userVariables=this.registerDataOutput("userVariables",s),this.executionId=this.registerDataOutput("executionId",t)}_updateOutputs(e){this.userVariables.setValue(e.userVariables,e),this.executionId.setValue(e.executionId,e)}serialize(e){super.serialize(e)}getClassName(){return"FlowGraphContextBlock"}}a("FlowGraphContextBlock",i);export{i as FlowGraphContextBlock};
//# sourceMappingURL=flowGraphContextBlock-BocHg12j.js.map
