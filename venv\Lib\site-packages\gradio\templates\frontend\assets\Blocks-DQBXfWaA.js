const __vite__fileDeps=["./ApiDocs-tDCQqm4K.js","./clear-CvR6Cdu7.js","./index-B7J2Z2jS.js","./index-CJsBH6a-.css","./Button-B3gqVEq2.js","./Image-CnqB5dbD.js","./file-url-DoxvUUVV.js","./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js","./DownloadLink-CqD3Uu0l.css","./Image-B8dFOee4.css","./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js","./prism-python-MMh3z1bK.js","./MarkdownCode-GC7vPJ5f.css","./Button-DTh9AgeE.css","./IconButtonWrapper-BqcF4N5S.css","./ImagePreview-C_qhEOxI.css","./index-B1FJGuzG.js","./StreamingBar.svelte_svelte_type_style_lang-DyfAPhwM.js","./StreamingBar-pGVW9eBG.css","./IconButton-C_HS7fTi.js","./Clear-By3xiIwg.js","./Block-CJdXVpa7.js","./Toast-BMPuxKCO.js","./index-CEGzm7H5.js","./utils-BsGrhMNe.js","./ApiDocs-C1ssMq67.css","./ApiRecorder-Bj4fHidL.js","./ApiRecorder-7UmvbTxs.css","./Settings-DWdlBuRz.js","./Dropdown-vf9-p-La.js","./BlockTitle-Ct-h8ev5.js","./Info-IGMCDo7y.js","./MarkdownCode-CkSMBRHJ.js","./DropdownArrow-DYWFcSFn.js","./Dropdown-CWxB-qJp.css","./Checkbox-CjOIpf6b.js","./Checkbox-COx9d1js.css","./Settings-BVvHX4gg.css","./Example-D7K5RtQ2.css"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{t as Ut,y as mi,z as di,$ as gi,c as hi,p as qt,s as pi,_ as tt,A as Wt,w as bi}from"./index-B7J2Z2jS.js";import"./StreamingBar.svelte_svelte_type_style_lang-DyfAPhwM.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import{T as wi}from"./Toast-BMPuxKCO.js";import{G as vi}from"./utils-BsGrhMNe.js";const{SvelteComponent:ki,add_flush_callback:Ei,assign:_t,bind:Ht,binding_callbacks:Nt,bubble:Si,check_outros:Sn,compute_rest_props:Vt,construct_svelte_component:Zt,create_component:Jt,create_slot:Ci,destroy_component:Qt,detach:Cn,empty:zn,exclude_internal_props:zi,flush:se,get_all_dirty_from_scope:Mi,get_slot_changes:yi,get_spread_object:Kt,get_spread_update:Yt,group_outros:Mn,init:Li,insert:yn,mount_component:Xt,not_equal:Ni,transition_in:Te,transition_out:Ve,update_slot_base:Oi}=window.__gradio__svelte__internal,{bind:Ri,binding_callbacks:Ai}=window.__gradio__svelte__internal;function xt(i){let e,t,n,s;const o=[{elem_id:i[5]},{elem_classes:i[6]},{target:i[3]},{visible:i[7]},i[9],{theme_mode:i[4]},{root:i[2]}];function p(r){i[14](r)}var c=i[8];function a(r,f){let u={$$slots:{default:[Ti]},$$scope:{ctx:r}};for(let _=0;_<o.length;_+=1)u=_t(u,o[_]);return f!==void 0&&f&764&&(u=_t(u,Yt(o,[f&32&&{elem_id:r[5]},f&64&&{elem_classes:r[6]},f&8&&{target:r[3]},f&128&&{visible:r[7]},f&512&&Kt(r[9]),f&16&&{theme_mode:r[4]},f&4&&{root:r[2]}]))),r[0]!==void 0&&(u.value=r[0]),{props:u}}return c&&(e=Zt(c,a(i)),i[13](e),Nt.push(()=>Ht(e,"value",p)),e.$on("prop_change",i[15])),{c(){e&&Jt(e.$$.fragment),n=zn()},m(r,f){e&&Xt(e,r,f),yn(r,n,f),s=!0},p(r,f){if(c!==(c=r[8])){if(e){Mn();const u=e;Ve(u.$$.fragment,1,0,()=>{Qt(u,1)}),Sn()}c?(e=Zt(c,a(r,f)),r[13](e),Nt.push(()=>Ht(e,"value",p)),e.$on("prop_change",r[15]),Jt(e.$$.fragment),Te(e.$$.fragment,1),Xt(e,n.parentNode,n)):e=null}else if(c){const u=f&764?Yt(o,[f&32&&{elem_id:r[5]},f&64&&{elem_classes:r[6]},f&8&&{target:r[3]},f&128&&{visible:r[7]},f&512&&Kt(r[9]),f&16&&{theme_mode:r[4]},f&4&&{root:r[2]}]):{};f&65536&&(u.$$scope={dirty:f,ctx:r}),!t&&f&1&&(t=!0,u.value=r[0],Ei(()=>t=!1)),e.$set(u)}},i(r){s||(e&&Te(e.$$.fragment,r),s=!0)},o(r){e&&Ve(e.$$.fragment,r),s=!1},d(r){r&&Cn(n),i[13](null),e&&Qt(e,r)}}}function Ti(i){let e;const t=i[12].default,n=Ci(t,i,i[16],null);return{c(){n&&n.c()},m(s,o){n&&n.m(s,o),e=!0},p(s,o){n&&n.p&&(!e||o&65536)&&Oi(n,t,s,s[16],e?yi(t,s[16],o,null):Mi(s[16]),null)},i(s){e||(Te(n,s),e=!0)},o(s){Ve(n,s),e=!1},d(s){n&&n.d(s)}}}function Pi(i){let e,t,n=i[7]&&xt(i);return{c(){n&&n.c(),e=zn()},m(s,o){n&&n.m(s,o),yn(s,e,o),t=!0},p(s,[o]){s[7]?n?(n.p(s,o),o&128&&Te(n,1)):(n=xt(s),n.c(),Te(n,1),n.m(e.parentNode,e)):n&&(Mn(),Ve(n,1,1,()=>{n=null}),Sn())},i(s){t||(Te(n),t=!0)},o(s){Ve(n),t=!1},d(s){s&&Cn(e),n&&n.d(s)}}}function Di(i,e,t){const n=["root","component","target","theme_mode","instance","value","elem_id","elem_classes","_id","visible"];let s=Vt(e,n),{$$slots:o={},$$scope:p}=e,{root:c}=e,{component:a}=e,{target:r}=e,{theme_mode:f}=e,{instance:u}=e,{value:_}=e,{elem_id:h}=e,{elem_classes:k}=e,{_id:g}=e,{visible:E}=e;const w=(S,$,X)=>new CustomEvent("prop_change",{detail:{id:S,prop:$,value:X}});function N(S){return new Proxy(S,{construct(X,re){const ne=new X(...re),le=Object.keys(ne.$$.props);function Se(D){return function(H){if(!r)return;const Y=w(g,D,H);r.dispatchEvent(Y)}}return le.forEach(D=>{Ai.push(()=>Ri(ne,D,Se(D)))}),ne}})}let d=N(a);const L=["description","info","title","placeholder","value","label"];function m(S){for(const $ in S)L.includes($)&&(S[$]=Ut(S[$]))}function M(S){Nt[S?"unshift":"push"](()=>{u=S,t(1,u)})}function x(S){_=S,t(0,_)}function te(S){Si.call(this,i,S)}return i.$$set=S=>{e=_t(_t({},e),zi(S)),t(9,s=Vt(e,n)),"root"in S&&t(2,c=S.root),"component"in S&&t(10,a=S.component),"target"in S&&t(3,r=S.target),"theme_mode"in S&&t(4,f=S.theme_mode),"instance"in S&&t(1,u=S.instance),"value"in S&&t(0,_=S.value),"elem_id"in S&&t(5,h=S.elem_id),"elem_classes"in S&&t(6,k=S.elem_classes),"_id"in S&&t(11,g=S._id),"visible"in S&&t(7,E=S.visible),"$$scope"in S&&t(16,p=S.$$scope)},i.$$.update=()=>{m(s),i.$$.dirty&1&&t(0,_=Ut(_))},[_,u,c,r,f,h,k,E,d,s,a,g,o,M,x,te,p]}class Bi extends ki{constructor(e){super(),Li(this,e,Di,Pi,Ni,{root:2,component:10,target:3,theme_mode:4,instance:1,value:0,elem_id:5,elem_classes:6,_id:11,visible:7})}get root(){return this.$$.ctx[2]}set root(e){this.$$set({root:e}),se()}get component(){return this.$$.ctx[10]}set component(e){this.$$set({component:e}),se()}get target(){return this.$$.ctx[3]}set target(e){this.$$set({target:e}),se()}get theme_mode(){return this.$$.ctx[4]}set theme_mode(e){this.$$set({theme_mode:e}),se()}get instance(){return this.$$.ctx[1]}set instance(e){this.$$set({instance:e}),se()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),se()}get elem_id(){return this.$$.ctx[5]}set elem_id(e){this.$$set({elem_id:e}),se()}get elem_classes(){return this.$$.ctx[6]}set elem_classes(e){this.$$set({elem_classes:e}),se()}get _id(){return this.$$.ctx[11]}set _id(e){this.$$set({_id:e}),se()}get visible(){return this.$$.ctx[7]}set visible(e){this.$$set({visible:e}),se()}}const{SvelteComponent:ji,add_flush_callback:$t,assign:Ii,bind:en,binding_callbacks:tn,bubble:nn,check_outros:Rt,component_subscribe:Fi,create_component:Ln,destroy_component:Nn,detach:ut,empty:mt,ensure_array_like:on,flush:_e,get_spread_object:Gi,get_spread_update:Ui,group_outros:At,init:qi,insert:dt,mount_component:On,outro_and_destroy_block:Wi,safe_not_equal:Hi,transition_in:fe,transition_out:Le,update_keyed_each:Vi}=window.__gradio__svelte__internal,{onMount:Zi,createEventDispatcher:Ji,setContext:Qi}=window.__gradio__svelte__internal;function sn(i,e,t){const n=i.slice();return n[16]=e[t],n}function rn(i){let e,t,n,s;const o=[{_id:i[0]?.id},{component:i[0].component},{elem_id:"elem_id"in i[0].props&&i[0].props.elem_id||`component-${i[0].id}`},{elem_classes:"elem_classes"in i[0].props&&i[0].props.elem_classes||[]},{target:i[2]},i[0].props,{theme_mode:i[3]},{root:i[1]},{visible:typeof i[0].props.visible=="boolean"?i[0].props.visible:!0}];function p(r){i[12](r)}function c(r){i[13](r)}let a={$$slots:{default:[Ki]},$$scope:{ctx:i}};for(let r=0;r<o.length;r+=1)a=Ii(a,o[r]);return i[0].instance!==void 0&&(a.instance=i[0].instance),i[0].props.value!==void 0&&(a.value=i[0].props.value),e=new Bi({props:a}),tn.push(()=>en(e,"instance",p)),tn.push(()=>en(e,"value",c)),{c(){Ln(e.$$.fragment)},m(r,f){On(e,r,f),s=!0},p(r,f){const u=f&15?Ui(o,[f&1&&{_id:r[0]?.id},f&1&&{component:r[0].component},f&1&&{elem_id:"elem_id"in r[0].props&&r[0].props.elem_id||`component-${r[0].id}`},f&1&&{elem_classes:"elem_classes"in r[0].props&&r[0].props.elem_classes||[]},f&4&&{target:r[2]},f&1&&Gi(r[0].props),f&8&&{theme_mode:r[3]},f&2&&{root:r[1]},f&1&&{visible:typeof r[0].props.visible=="boolean"?r[0].props.visible:!0}]):{};f&524351&&(u.$$scope={dirty:f,ctx:r}),!t&&f&1&&(t=!0,u.instance=r[0].instance,$t(()=>t=!1)),!n&&f&1&&(n=!0,u.value=r[0].props.value,$t(()=>n=!1)),e.$set(u)},i(r){s||(fe(e.$$.fragment,r),s=!0)},o(r){Le(e.$$.fragment,r),s=!1},d(r){Nn(e,r)}}}function ln(i){let e=[],t=new Map,n,s,o=on(i[0].children);const p=c=>c[16].id;for(let c=0;c<o.length;c+=1){let a=sn(i,o,c),r=p(a);t.set(r,e[c]=an(r,a))}return{c(){for(let c=0;c<e.length;c+=1)e[c].c();n=mt()},m(c,a){for(let r=0;r<e.length;r+=1)e[r]&&e[r].m(c,a);dt(c,n,a),s=!0},p(c,a){a&63&&(o=on(c[0].children),At(),e=Vi(e,a,p,1,c,o,t,n.parentNode,Wi,an,n,sn),Rt())},i(c){if(!s){for(let a=0;a<o.length;a+=1)fe(e[a]);s=!0}},o(c){for(let a=0;a<e.length;a+=1)Le(e[a]);s=!1},d(c){c&&ut(n);for(let a=0;a<e.length;a+=1)e[a].d(c)}}}function an(i,e){let t,n,s;return n=new Rn({props:{node:e[16],component:e[16].component,target:e[2],id:e[16].id,root:e[1],theme_mode:e[3],max_file_size:e[4],client:e[5]}}),n.$on("destroy",e[10]),n.$on("mount",e[11]),{key:i,first:null,c(){t=mt(),Ln(n.$$.fragment),this.first=t},m(o,p){dt(o,t,p),On(n,o,p),s=!0},p(o,p){e=o;const c={};p&1&&(c.node=e[16]),p&1&&(c.component=e[16].component),p&4&&(c.target=e[2]),p&1&&(c.id=e[16].id),p&2&&(c.root=e[1]),p&8&&(c.theme_mode=e[3]),p&16&&(c.max_file_size=e[4]),p&32&&(c.client=e[5]),n.$set(c)},i(o){s||(fe(n.$$.fragment,o),s=!0)},o(o){Le(n.$$.fragment,o),s=!1},d(o){o&&ut(t),Nn(n,o)}}}function Ki(i){let e,t,n=i[0].children&&i[0].children.length&&ln(i);return{c(){n&&n.c(),e=mt()},m(s,o){n&&n.m(s,o),dt(s,e,o),t=!0},p(s,o){s[0].children&&s[0].children.length?n?(n.p(s,o),o&1&&fe(n,1)):(n=ln(s),n.c(),fe(n,1),n.m(e.parentNode,e)):n&&(At(),Le(n,1,1,()=>{n=null}),Rt())},i(s){t||(fe(n),t=!0)},o(s){Le(n),t=!1},d(s){s&&ut(e),n&&n.d(s)}}}function Yi(i){let e,t,n=i[0].component&&rn(i);return{c(){n&&n.c(),e=mt()},m(s,o){n&&n.m(s,o),dt(s,e,o),t=!0},p(s,[o]){s[0].component?n?(n.p(s,o),o&1&&fe(n,1)):(n=rn(s),n.c(),fe(n,1),n.m(e.parentNode,e)):n&&(At(),Le(n,1,1,()=>{n=null}),Rt())},i(s){t||(fe(n),t=!0)},o(s){Le(n),t=!1},d(s){s&&ut(e),n&&n.d(s)}}}function Xi(i,e,t){let n;Fi(i,mi,d=>t(9,n=d));let{root:s}=e,{node:o}=e,{parent:p=null}=e,{target:c}=e,{theme_mode:a}=e,{version:r}=e,{autoscroll:f}=e,{max_file_size:u}=e,{client:_}=e;const h=Ji();let k=[];Zi(()=>{h("mount",o.id);for(const d of k)h("mount",d.id);return()=>{h("destroy",o.id);for(const d of k)h("mount",d.id)}}),Qi("BLOCK_KEY",p);function g(d){nn.call(this,i,d)}function E(d){nn.call(this,i,d)}function w(d){i.$$.not_equal(o.instance,d)&&(o.instance=d,t(0,o),t(15,k),t(2,c),t(3,a),t(7,r),t(1,s),t(8,f),t(4,u),t(9,n),t(5,_))}function N(d){i.$$.not_equal(o.props.value,d)&&(o.props.value=d,t(0,o),t(15,k),t(2,c),t(3,a),t(7,r),t(1,s),t(8,f),t(4,u),t(9,n),t(5,_))}return i.$$set=d=>{"root"in d&&t(1,s=d.root),"node"in d&&t(0,o=d.node),"parent"in d&&t(6,p=d.parent),"target"in d&&t(2,c=d.target),"theme_mode"in d&&t(3,a=d.theme_mode),"version"in d&&t(7,r=d.version),"autoscroll"in d&&t(8,f=d.autoscroll),"max_file_size"in d&&t(4,u=d.max_file_size),"client"in d&&t(5,_=d.client)},i.$$.update=()=>{i.$$.dirty&1&&o&&t(0,o.children=o.children&&o.children.filter(d=>{const L=o.type!=="statustracker";return L||k.push(d),L}),o),i.$$.dirty&1&&o&&o.type==="form"&&(o.children?.every(d=>typeof d.props.visible=="boolean"&&!d.props.visible)?t(0,o.props.visible=!1,o):t(0,o.props.visible=!0,o)),i.$$.dirty&959&&t(0,o.props.gradio=new vi(o.id,c,a,r,s,f,u,n,_,di),o)},[o,s,c,a,u,_,p,r,f,n,g,E,w,N]}class Rn extends ji{constructor(e){super(),qi(this,e,Xi,Yi,Hi,{root:1,node:0,parent:6,target:2,theme_mode:3,version:7,autoscroll:8,max_file_size:4,client:5})}get root(){return this.$$.ctx[1]}set root(e){this.$$set({root:e}),_e()}get node(){return this.$$.ctx[0]}set node(e){this.$$set({node:e}),_e()}get parent(){return this.$$.ctx[6]}set parent(e){this.$$set({parent:e}),_e()}get target(){return this.$$.ctx[2]}set target(e){this.$$set({target:e}),_e()}get theme_mode(){return this.$$.ctx[3]}set theme_mode(e){this.$$set({theme_mode:e}),_e()}get version(){return this.$$.ctx[7]}set version(e){this.$$set({version:e}),_e()}get autoscroll(){return this.$$.ctx[8]}set autoscroll(e){this.$$set({autoscroll:e}),_e()}get max_file_size(){return this.$$.ctx[4]}set max_file_size(e){this.$$set({max_file_size:e}),_e()}get client(){return this.$$.ctx[5]}set client(e){this.$$set({client:e}),_e()}}const{SvelteComponent:xi,check_outros:$i,create_component:eo,destroy_component:to,detach:no,empty:io,flush:ge,group_outros:oo,init:so,insert:ro,mount_component:lo,safe_not_equal:ao,transition_in:lt,transition_out:Ot}=window.__gradio__svelte__internal,{onMount:co,createEventDispatcher:_o}=window.__gradio__svelte__internal;function cn(i){let e,t;return e=new Rn({props:{node:i[0],root:i[1],target:i[2],theme_mode:i[3],version:i[4],autoscroll:i[5],max_file_size:i[6],client:i[7]}}),{c(){eo(e.$$.fragment)},m(n,s){lo(e,n,s),t=!0},p(n,s){const o={};s&1&&(o.node=n[0]),s&2&&(o.root=n[1]),s&4&&(o.target=n[2]),s&8&&(o.theme_mode=n[3]),s&16&&(o.version=n[4]),s&32&&(o.autoscroll=n[5]),s&64&&(o.max_file_size=n[6]),s&128&&(o.client=n[7]),e.$set(o)},i(n){t||(lt(e.$$.fragment,n),t=!0)},o(n){Ot(e.$$.fragment,n),t=!1},d(n){to(e,n)}}}function fo(i){let e,t,n=i[0]&&cn(i);return{c(){n&&n.c(),e=io()},m(s,o){n&&n.m(s,o),ro(s,e,o),t=!0},p(s,[o]){s[0]?n?(n.p(s,o),o&1&&lt(n,1)):(n=cn(s),n.c(),lt(n,1),n.m(e.parentNode,e)):n&&(oo(),Ot(n,1,1,()=>{n=null}),$i())},i(s){t||(lt(n),t=!0)},o(s){Ot(n),t=!1},d(s){s&&no(e),n&&n.d(s)}}}function uo(i,e,t){let{rootNode:n}=e,{root:s}=e,{target:o}=e,{theme_mode:p}=e,{version:c}=e,{autoscroll:a}=e,{max_file_size:r=null}=e,{client:f}=e;const u=_o();return co(()=>{u("mount")}),i.$$set=_=>{"rootNode"in _&&t(0,n=_.rootNode),"root"in _&&t(1,s=_.root),"target"in _&&t(2,o=_.target),"theme_mode"in _&&t(3,p=_.theme_mode),"version"in _&&t(4,c=_.version),"autoscroll"in _&&t(5,a=_.autoscroll),"max_file_size"in _&&t(6,r=_.max_file_size),"client"in _&&t(7,f=_.client)},[n,s,o,p,c,a,r,f]}class mo extends xi{constructor(e){super(),so(this,e,uo,fo,ao,{rootNode:0,root:1,target:2,theme_mode:3,version:4,autoscroll:5,max_file_size:6,client:7})}get rootNode(){return this.$$.ctx[0]}set rootNode(e){this.$$set({rootNode:e}),ge()}get root(){return this.$$.ctx[1]}set root(e){this.$$set({root:e}),ge()}get target(){return this.$$.ctx[2]}set target(e){this.$$set({target:e}),ge()}get theme_mode(){return this.$$.ctx[3]}set theme_mode(e){this.$$set({theme_mode:e}),ge()}get version(){return this.$$.ctx[4]}set version(e){this.$$set({version:e}),ge()}get autoscroll(){return this.$$.ctx[5]}set autoscroll(e){this.$$set({autoscroll:e}),ge()}get max_file_size(){return this.$$.ctx[6]}set max_file_size(e){this.$$set({max_file_size:e}),ge()}get client(){return this.$$.ctx[7]}set client(e){this.$$set({client:e}),ge()}}const go="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='576'%20height='576'%20viewBox='0%200%20576%20576'%20fill='none'%3e%3cpath%20d='M287.5%20229L86%20344.5L287.5%20460L489%20344.5L287.5%20229Z'%20stroke='url(%23paint0_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='round'/%3e%3cpath%20d='M287.5%20116L86%20231.5L287.5%20347L489%20231.5L287.5%20116Z'%20stroke='url(%23paint1_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='round'/%3e%3cpath%20d='M86%20344L288%20229'%20stroke='url(%23paint2_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='bevel'/%3e%3cdefs%3e%3clinearGradient%20id='paint0_linear_102_7'%20x1='60'%20y1='341'%20x2='429.5'%20y2='344'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint1_linear_102_7'%20x1='513.5'%20y1='231'%20x2='143.5'%20y2='231'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint2_linear_102_7'%20x1='60'%20y1='344'%20x2='428.987'%20y2='341.811'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e",ho="data:image/svg+xml,%3csvg%20width='28'%20height='28'%20viewBox='0%200%2028%2028'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M26.9425%202.94265C27.4632%202.42195%2027.4632%201.57773%2026.9425%201.05703C26.4218%200.536329%2025.5776%200.536329%2025.0569%201.05703L22.5713%203.54256C21.1213%202.59333%2019.5367%202.43378%2018.1753%202.64006C16.5495%202.88638%2015.1127%203.66838%2014.3905%204.39053L12.3905%206.39053C12.1405%206.64058%2012%206.97972%2012%207.33334C12%207.68697%2012.1405%208.0261%2012.3905%208.27615L19.7239%2015.6095C20.2446%2016.1302%2021.0888%2016.1302%2021.6095%2015.6095L23.6095%2013.6095C24.3316%2012.8873%2025.1136%2011.4505%2025.36%209.82475C25.5663%208.46312%2025.4066%206.87827%2024.4571%205.42807L26.9425%202.94265Z'%20fill='%233c4555'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M12.276%2012.9426C12.7967%2012.4219%2012.7967%2011.5777%2012.276%2011.057C11.7553%2010.5363%2010.9111%2010.5363%2010.3904%2011.057L8.66651%2012.7809L8.27615%2012.3905C8.0261%2012.1405%207.68697%2012%207.33334%2012C6.97972%2012%206.64058%2012.1405%206.39053%2012.3905L4.39053%2014.3905C3.66838%2015.1127%202.88638%2016.5495%202.64006%2018.1753C2.43377%2019.5367%202.59333%2021.1214%203.54262%2022.5714L1.05703%2025.057C0.536329%2025.5777%200.536329%2026.4219%201.05703%2026.9426C1.57773%2027.4633%202.42195%2027.4633%202.94265%2026.9426L5.42817%2024.4571C6.87835%2025.4066%208.46315%2025.5663%209.82475%2025.36C11.4505%2025.1136%2012.8873%2024.3316%2013.6095%2023.6095L15.6095%2021.6095C16.1302%2021.0888%2016.1302%2020.2446%2015.6095%2019.7239L15.2188%2019.3332L16.9426%2017.6093C17.4633%2017.0886%2017.4633%2016.2444%2016.9426%2015.7237C16.4219%2015.203%2015.5777%2015.203%2015.057%2015.7237L13.3332%2017.4475L10.5521%2014.6665L12.276%2012.9426Z'%20fill='%23FF7C00'/%3e%3c/svg%3e",po="data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20xmlns='http://www.w3.org/2000/svg'%3e%3c!--%20Outer%20gear%20teeth%20(gray)%20--%3e%3cpath%20d='M19.14%2012.94c.04-.3.06-.61.06-.94%200-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24%200-.43.17-.47.41l-.36%202.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47%200-.59.22L2.74%208.87c-.12.21-.08.47.12.61l2.03%201.58c-.05.3-.07.62-.07.94s.02.64.07.94l-2.03%201.58c-.18.14-.23.41-.12.61l1.92%203.32c.12.22.37.29.59.22l2.39-.96c.5.38%201.03.7%201.62.94l.36%202.54c.05.24.24.41.48.41h3.84c.24%200%20.44-.17.47-.41l.36-2.54c.59-.24%201.13-.56%201.62-.94l2.39.96c.22.08.47%200%20.59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12%2015.6c-1.98%200-3.6-1.62-3.6-3.6s1.62-3.6%203.6-3.6%203.6%201.62%203.6%203.6-1.62%203.6-3.6%203.6z'%20fill='%23808080'/%3e%3c!--%20Inner%20circle%20(now%20gray)%20--%3e%3ccircle%20cx='12'%20cy='12'%20r='2.5'%20fill='%23808080'/%3e%3c/svg%3e",bo="data:image/svg+xml,%3csvg%20viewBox='0%200%2020%2020'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='%23000000'%3e%3cg%20id='SVGRepo_bgCarrier'%20stroke-width='0'%3e%3c/g%3e%3cg%20id='SVGRepo_tracerCarrier'%20stroke-linecap='round'%20stroke-linejoin='round'%3e%3c/g%3e%3cg%20id='SVGRepo_iconCarrier'%3e%3ctitle%3erecord%20[%23982]%3c/title%3e%3cdesc%3eCreated%20with%20Sketch.%3c/desc%3e%3cdefs%3e%3c/defs%3e%3cg%20id='Page-1'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='Dribbble-Light-Preview'%20transform='translate(-380.000000,%20-3839.000000)'%20fill='%23FF0000'%3e%3cg%20id='icons'%20transform='translate(56.000000,%20160.000000)'%3e%3cpath%20d='M338,3689%20C338,3691.209%20336.209,3693%20334,3693%20C331.791,3693%20330,3691.209%20330,3689%20C330,3686.791%20331.791,3685%20334,3685%20C336.209,3685%20338,3686.791%20338,3689%20M334,3697%20C329.589,3697%20326,3693.411%20326,3689%20C326,3684.589%20329.589,3681%20334,3681%20C338.411,3681%20342,3684.589%20342,3689%20C342,3693.411%20338.411,3697%20334,3697%20M334,3679%20C328.477,3679%20324,3683.477%20324,3689%20C324,3694.523%20328.477,3699%20334,3699%20C339.523,3699%20344,3694.523%20344,3689%20C344,3683.477%20339.523,3679%20334,3679'%20id='record-[%23982]'%3e%3c/path%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e";let ue=!1,ye=null,Tt=[],gt=0,be={},An,ft,Ne=null,qe=[];function wo(i,e,t){An=i,ft=e,t&&(Ne=t)}async function vo(){if(!ue)try{const i=document.title;document.title="[Sharing] Gradio Tab";const e=await navigator.mediaDevices.getDisplayMedia({video:{width:{ideal:1920},height:{ideal:1080},frameRate:{ideal:30}},audio:!0,selfBrowserSurface:"include"});document.title=i;const t={videoBitsPerSecond:5e6};ye=new MediaRecorder(e,t),Tt=[],be={},ye.ondataavailable=zo,ye.onstop=Mo,ye.start(1e3),ue=!0,Ne&&Ne(!0),gt=Date.now()}catch(i){ft("Recording Error","Failed to start recording: "+i.message,"error")}}function ko(){!ue||!ye||(ye.stop(),ue=!1,Ne&&Ne(!1))}function Eo(){if(!ue)return;const i=(Date.now()-gt)/1e3;be.start=i}function So(){if(!ue||be.start===void 0)return;const i=(Date.now()-gt)/1e3;be.end=i}function Co(i,e){if(!ue)return;const t=30,n=(Date.now()-gt)/1e3,s=Math.floor(i?(n-2)*t:n*t);if(e.boundingBox&&e.boundingBox.topLeft&&e.boundingBox.bottomRight&&e.boundingBox.topLeft.length===2&&e.boundingBox.bottomRight.length===2){const o=e.duration||2,p=s+Math.floor(o*t);qe.some(a=>{const r=a.start_frame+Math.floor((a.duration||2)*t);return s>=a.start_frame&&s<=r||p>=a.start_frame&&p<=r||s<=a.start_frame&&p>=r})||qe.push({boundingBox:e.boundingBox,start_frame:s,duration:o})}}function _n(i,e,t=2){if(ue)try{setTimeout(()=>{if(!e||e.length===0)return;let n=1/0,s=1/0,o=0,p=0,c=!1;for(const w of e){const N=`#component-${w}`,d=document.querySelector(N);if(d){c=!0;const L=d.getBoundingClientRect();n=Math.min(n,L.left),s=Math.min(s,L.top),o=Math.max(o,L.right),p=Math.max(p,L.bottom)}}if(!c)return;const a=window.innerWidth,r=window.innerHeight,f=Math.min(o,a)-Math.max(0,n),u=Math.min(p,r)-Math.max(0,s),_=f/a,h=u/r;if(_>=.8||h>=.8)return;const k=/^((?!chrome|android).)*safari/i.test(navigator.userAgent);let g=[Math.max(0,n)/a,Math.max(0,s)/r],E=[Math.min(o,a)/a,Math.min(p,r)/r];if(k){g[0]=Math.max(0,g[0]*.9),E[0]=Math.min(1,E[0]*.9);const w=E[0]-g[0],d=(g[0]+E[0])/2*.9;g[0]=Math.max(0,d-w/2),E[0]=Math.min(1,d+w/2)}g[0]=Math.max(0,g[0]),g[1]=Math.max(0,g[1]),E[0]=Math.min(1,E[0]),E[1]=Math.min(1,E[1]),Co(i,{boundingBox:{topLeft:g,bottomRight:E},duration:t})},300)}catch{}}function zo(i){i.data.size>0&&Tt.push(i.data)}function Mo(){ue=!1,Ne&&Ne(!1);const i=new Blob(Tt,{type:"video/mp4"});yo(i),(ye?.stream?.getTracks()||[]).forEach(t=>t.stop())}async function yo(i){try{ft("Processing video","This may take a few seconds...","info");const e=new FormData;e.append("video",i,"recording.mp4"),be.start!==void 0&&be.end!==void 0&&(e.append("remove_segment_start",be.start.toString()),e.append("remove_segment_end",be.end.toString())),qe.length>0&&e.append("zoom_effects",JSON.stringify(qe));const t=await fetch(An+"/gradio_api/process_recording",{method:"POST",body:e});if(!t.ok)throw new Error(`Server returned ${t.status}: ${t.statusText}`);const n=await t.blob(),s=`gradio-screen-recording-${new Date().toISOString().replace(/:/g,"-").replace(/\..+/,"")}.mp4`;fn(n,s),qe=[]}catch{ft("Processing Error","Failed to process recording. Saving original version.","warning");const t=`gradio-screen-recording-${new Date().toISOString().replace(/:/g,"-").replace(/\..+/,"")}.mp4`;fn(i,t)}}function fn(i,e){const t=URL.createObjectURL(i),n=document.createElement("a");n.style.display="none",n.href=t,n.download=e,document.body.appendChild(n),n.click(),setTimeout(()=>{document.body.removeChild(n),URL.revokeObjectURL(t)},100)}const{HtmlTag:Lo,SvelteComponent:No,add_flush_callback:un,append:A,attr:y,bind:nt,binding_callbacks:it,check_outros:he,component_subscribe:Ae,construct_svelte_component:Pe,create_component:ve,destroy_component:ke,detach:V,element:W,empty:at,flush:B,group_outros:pe,init:Oo,insert:K,listen:we,mount_component:Ee,noop:yt,run_all:Tn,safe_not_equal:Ro,set_data:We,set_store_value:Ao,set_style:ot,space:Q,src_url_equal:ct,text:He,toggle_class:st,transition_in:I,transition_out:J}=window.__gradio__svelte__internal,{tick:rt,onMount:To}=window.__gradio__svelte__internal;function mn(i){return document.title=i[2],{c:yt,m:yt,d:yt}}function dn(i){let e,t=`<style>${qt(i[15],i[12])}</style>`,n;return{c(){e=new Lo(!1),n=at(),e.a=n},m(s,o){e.m(t,s,o),K(s,n,o)},p(s,o){o[0]&36864&&t!==(t=`<style>${qt(s[15],s[12])}</style>`)&&e.p(t)},d(s){s&&(V(n),e.d())}}}function gn(i){let e,t;return e=new mo({props:{rootNode:i[16],root:i[0],target:i[3],theme_mode:i[9],version:i[12],autoscroll:i[4],max_file_size:i[14],client:i[10]}}),e.$on("mount",i[39]),{c(){ve(e.$$.fragment)},m(n,s){Ee(e,n,s),t=!0},p(n,s){const o={};s[0]&65536&&(o.rootNode=n[16]),s[0]&1&&(o.root=n[0]),s[0]&8&&(o.target=n[3]),s[0]&512&&(o.theme_mode=n[9]),s[0]&4096&&(o.version=n[12]),s[0]&16&&(o.autoscroll=n[4]),s[0]&16384&&(o.max_file_size=n[14]),s[0]&1024&&(o.client=n[10]),e.$set(o)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){J(e.$$.fragment,n),t=!1},d(n){ke(e,n)}}}function hn(i){let e,t,n,s=i[28]("common.built_with_gradio")+"",o,p,c,a,r,f,u,_,h,k=i[28]("common.stop_recording")+"",g,E,w,N,d,L,m,M,x,te=i[28]("common.settings")+"",S,$,X,re,ne,le,Se,D=i[5]&&pn(i);return{c(){e=W("footer"),D&&D.c(),t=Q(),n=W("a"),o=He(s),p=Q(),c=W("img"),f=Q(),u=W("div"),u.textContent="·",_=Q(),h=W("button"),g=He(k),E=Q(),w=W("img"),L=Q(),m=W("div"),m.textContent="·",M=Q(),x=W("button"),S=He(te),$=Q(),X=W("img"),ct(c.src,a=go)||y(c,"src",a),y(c,"alt",r=i[28]("common.logo")),y(c,"class","svelte-czcr5b"),y(n,"href","https://gradio.app"),y(n,"class","built-with svelte-czcr5b"),y(n,"target","_blank"),y(n,"rel","noreferrer"),y(u,"class","divider svelte-czcr5b"),st(u,"hidden",!i[27]),ct(w.src,N=bo)||y(w,"src",N),y(w,"alt",d=i[28]("common.stop_recording")),y(w,"class","svelte-czcr5b"),y(h,"class","record svelte-czcr5b"),st(h,"hidden",!i[27]),y(m,"class","divider svelte-czcr5b"),ct(X.src,re=po)||y(X,"src",re),y(X,"alt",ne=i[28]("common.settings")),y(X,"class","svelte-czcr5b"),y(x,"class","settings svelte-czcr5b"),y(e,"class","svelte-czcr5b")},m(H,Y){K(H,e,Y),D&&D.m(e,null),A(e,t),A(e,n),A(n,o),A(n,p),A(n,c),A(e,f),A(e,u),A(e,_),A(e,h),A(h,g),A(h,E),A(h,w),A(e,L),A(e,m),A(e,M),A(e,x),A(x,S),A(x,$),A(x,X),le||(Se=[we(h,"click",i[57]),we(x,"click",i[58]),we(x,"mouseenter",i[59])],le=!0)},p(H,Y){H[5]?D?D.p(H,Y):(D=pn(H),D.c(),D.m(e,t)):D&&(D.d(1),D=null),Y[0]&268435456&&s!==(s=H[28]("common.built_with_gradio")+"")&&We(o,s),Y[0]&268435456&&r!==(r=H[28]("common.logo"))&&y(c,"alt",r),Y[0]&134217728&&st(u,"hidden",!H[27]),Y[0]&268435456&&k!==(k=H[28]("common.stop_recording")+"")&&We(g,k),Y[0]&268435456&&d!==(d=H[28]("common.stop_recording"))&&y(w,"alt",d),Y[0]&134217728&&st(h,"hidden",!H[27]),Y[0]&268435456&&te!==(te=H[28]("common.settings")+"")&&We(S,te),Y[0]&268435456&&ne!==(ne=H[28]("common.settings"))&&y(X,"alt",ne)},d(H){H&&V(e),D&&D.d(),le=!1,Tn(Se)}}}function pn(i){let e,t,n,s,o,p,c,a,r;function f(h,k){return h[10].config?.mcp_server?Do:Po}let u=f(i),_=u(i);return{c(){e=W("button"),_.c(),t=Q(),n=W("img"),p=Q(),c=W("div"),c.textContent="·",ct(n.src,s=ho)||y(n,"src",s),y(n,"alt",o=i[28]("common.logo")),y(n,"class","svelte-czcr5b"),y(e,"class","show-api svelte-czcr5b"),y(c,"class","divider show-api-divider svelte-czcr5b")},m(h,k){K(h,e,k),_.m(e,null),A(e,t),A(e,n),K(h,p,k),K(h,c,k),a||(r=[we(e,"click",i[55]),we(e,"mouseenter",i[56])],a=!0)},p(h,k){u===(u=f(h))&&_?_.p(h,k):(_.d(1),_=u(h),_&&(_.c(),_.m(e,t))),k[0]&268435456&&o!==(o=h[28]("common.logo"))&&y(n,"alt",o)},d(h){h&&(V(e),V(p),V(c)),_.d(),a=!1,Tn(r)}}}function Po(i){let e=i[28]("errors.use_via_api")+"",t;return{c(){t=He(e)},m(n,s){K(n,t,s)},p(n,s){s[0]&268435456&&e!==(e=n[28]("errors.use_via_api")+"")&&We(t,e)},d(n){n&&V(t)}}}function Do(i){let e=i[28]("errors.use_via_api_or_mcp")+"",t;return{c(){t=He(e)},m(n,s){K(n,t,s)},p(n,s){s[0]&268435456&&e!==(e=n[28]("errors.use_via_api_or_mcp")+"")&&We(t,e)},d(n){n&&V(t)}}}function bn(i){let e,t,n,s,o;var p=i[23];function c(a,r){return{props:{api_calls:a[25],dependencies:a[1]}}}return p&&(t=Pe(p,c(i))),{c(){e=W("div"),t&&ve(t.$$.fragment),y(e,"id","api-recorder-container"),y(e,"class","svelte-czcr5b")},m(a,r){K(a,e,r),t&&Ee(t,e,null),n=!0,s||(o=we(e,"click",i[60]),s=!0)},p(a,r){if(r[0]&8388608&&p!==(p=a[23])){if(t){pe();const f=t;J(f.$$.fragment,1,0,()=>{ke(f,1)}),he()}p?(t=Pe(p,c(a)),ve(t.$$.fragment),I(t.$$.fragment,1),Ee(t,e,null)):t=null}else if(p){const f={};r[0]&33554432&&(f.api_calls=a[25]),r[0]&2&&(f.dependencies=a[1]),t.$set(f)}},i(a){n||(t&&I(t.$$.fragment,a),n=!0)},o(a){t&&J(t.$$.fragment,a),n=!1},d(a){a&&V(e),t&&ke(t),s=!1,o()}}}function wn(i){let e,t,n,s,o,p,c,a;var r=i[22];function f(u,_){return{props:{root_node:u[16],dependencies:u[1],root:u[0],app:u[10],space_id:u[11],api_calls:u[25],username:u[13]}}}return r&&(o=Pe(r,f(i)),o.$on("close",i[62])),{c(){e=W("div"),t=W("div"),n=Q(),s=W("div"),o&&ve(o.$$.fragment),y(t,"class","backdrop svelte-czcr5b"),y(s,"class","api-docs-wrap svelte-czcr5b"),y(e,"class","api-docs svelte-czcr5b")},m(u,_){K(u,e,_),A(e,t),A(e,n),A(e,s),o&&Ee(o,s,null),p=!0,c||(a=we(t,"click",i[61]),c=!0)},p(u,_){if(_[0]&4194304&&r!==(r=u[22])){if(o){pe();const h=o;J(h.$$.fragment,1,0,()=>{ke(h,1)}),he()}r?(o=Pe(r,f(u)),o.$on("close",u[62]),ve(o.$$.fragment),I(o.$$.fragment,1),Ee(o,s,null)):o=null}else if(r){const h={};_[0]&65536&&(h.root_node=u[16]),_[0]&2&&(h.dependencies=u[1]),_[0]&1&&(h.root=u[0]),_[0]&1024&&(h.app=u[10]),_[0]&2048&&(h.space_id=u[11]),_[0]&33554432&&(h.api_calls=u[25]),_[0]&8192&&(h.username=u[13]),o.$set(h)}},i(u){p||(o&&I(o.$$.fragment,u),p=!0)},o(u){o&&J(o.$$.fragment,u),p=!1},d(u){u&&V(e),o&&ke(o),c=!1,a()}}}function vn(i){let e,t,n,s,o,p,c,a,r,f;function u(g){i[64](g)}function _(g){i[65](g)}var h=i[24];function k(g,E){let w={pwa_enabled:g[10].config.pwa,root:g[0],space_id:g[11]};return g[20]!==void 0&&(w.allow_zoom=g[20]),g[21]!==void 0&&(w.allow_video_trim=g[21]),{props:w}}return h&&(o=Pe(h,k(i)),it.push(()=>nt(o,"allow_zoom",u)),it.push(()=>nt(o,"allow_video_trim",_)),o.$on("close",i[66]),o.$on("start_recording",i[67])),{c(){e=W("div"),t=W("div"),n=Q(),s=W("div"),o&&ve(o.$$.fragment),y(t,"class","backdrop svelte-czcr5b"),y(s,"class","api-docs-wrap svelte-czcr5b"),y(e,"class","api-docs svelte-czcr5b")},m(g,E){K(g,e,E),A(e,t),A(e,n),A(e,s),o&&Ee(o,s,null),a=!0,r||(f=we(t,"click",i[63]),r=!0)},p(g,E){if(E[0]&16777216&&h!==(h=g[24])){if(o){pe();const w=o;J(w.$$.fragment,1,0,()=>{ke(w,1)}),he()}h?(o=Pe(h,k(g)),it.push(()=>nt(o,"allow_zoom",u)),it.push(()=>nt(o,"allow_video_trim",_)),o.$on("close",g[66]),o.$on("start_recording",g[67]),ve(o.$$.fragment),I(o.$$.fragment,1),Ee(o,s,null)):o=null}else if(h){const w={};E[0]&1024&&(w.pwa_enabled=g[10].config.pwa),E[0]&1&&(w.root=g[0]),E[0]&2048&&(w.space_id=g[11]),!p&&E[0]&1048576&&(p=!0,w.allow_zoom=g[20],un(()=>p=!1)),!c&&E[0]&2097152&&(c=!0,w.allow_video_trim=g[21],un(()=>c=!1)),o.$set(w)}},i(g){a||(o&&I(o.$$.fragment,g),a=!0)},o(g){o&&J(o.$$.fragment,g),a=!1},d(g){g&&V(e),o&&ke(o),r=!1,f()}}}function kn(i){let e,t;return e=new wi({props:{messages:i[26]}}),e.$on("close",i[38]),{c(){ve(e.$$.fragment)},m(n,s){Ee(e,n,s),t=!0},p(n,s){const o={};s[0]&67108864&&(o.messages=n[26]),e.$set(o)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){J(e.$$.fragment,n),t=!1},d(n){ke(e,n)}}}function Bo(i){let e,t,n,s,o,p,c,a,r,f,u,_,h=i[7]&&mn(i),k=i[15]&&dn(i),g=i[16]&&i[10].config&&gn(i),E=i[6]&&hn(i),w=i[19]&&i[23]&&bn(i),N=i[17]&&i[16]&&i[22]&&wn(i),d=i[18]&&i[16]&&i[10].config&&i[24]&&vn(i),L=i[26]&&kn(i);return{c(){h&&h.c(),e=at(),k&&k.c(),t=at(),n=Q(),s=W("div"),o=W("div"),g&&g.c(),p=Q(),E&&E.c(),c=Q(),w&&w.c(),a=Q(),N&&N.c(),r=Q(),d&&d.c(),f=Q(),L&&L.c(),u=at(),y(o,"class","contain svelte-czcr5b"),ot(o,"flex-grow",i[8]?"1":"auto"),y(s,"class","wrap svelte-czcr5b"),ot(s,"min-height",i[8]?"100%":"auto")},m(m,M){h&&h.m(document.head,null),A(document.head,e),k&&k.m(document.head,null),A(document.head,t),K(m,n,M),K(m,s,M),A(s,o),g&&g.m(o,null),A(s,p),E&&E.m(s,null),K(m,c,M),w&&w.m(m,M),K(m,a,M),N&&N.m(m,M),K(m,r,M),d&&d.m(m,M),K(m,f,M),L&&L.m(m,M),K(m,u,M),_=!0},p(m,M){m[7]?h||(h=mn(m),h.c(),h.m(e.parentNode,e)):h&&(h.d(1),h=null),m[15]?k?k.p(m,M):(k=dn(m),k.c(),k.m(t.parentNode,t)):k&&(k.d(1),k=null),m[16]&&m[10].config?g?(g.p(m,M),M[0]&66560&&I(g,1)):(g=gn(m),g.c(),I(g,1),g.m(o,null)):g&&(pe(),J(g,1,1,()=>{g=null}),he()),M[0]&256&&ot(o,"flex-grow",m[8]?"1":"auto"),m[6]?E?E.p(m,M):(E=hn(m),E.c(),E.m(s,null)):E&&(E.d(1),E=null),M[0]&256&&ot(s,"min-height",m[8]?"100%":"auto"),m[19]&&m[23]?w?(w.p(m,M),M[0]&8912896&&I(w,1)):(w=bn(m),w.c(),I(w,1),w.m(a.parentNode,a)):w&&(pe(),J(w,1,1,()=>{w=null}),he()),m[17]&&m[16]&&m[22]?N?(N.p(m,M),M[0]&4390912&&I(N,1)):(N=wn(m),N.c(),I(N,1),N.m(r.parentNode,r)):N&&(pe(),J(N,1,1,()=>{N=null}),he()),m[18]&&m[16]&&m[10].config&&m[24]?d?(d.p(m,M),M[0]&17105920&&I(d,1)):(d=vn(m),d.c(),I(d,1),d.m(f.parentNode,f)):d&&(pe(),J(d,1,1,()=>{d=null}),he()),m[26]?L?(L.p(m,M),M[0]&67108864&&I(L,1)):(L=kn(m),L.c(),I(L,1),L.m(u.parentNode,u)):L&&(pe(),J(L,1,1,()=>{L=null}),he())},i(m){_||(I(g),I(w),I(N),I(d),I(L),_=!0)},o(m){J(g),J(w),J(N),J(d),J(L),_=!1},d(m){m&&(V(n),V(s),V(c),V(a),V(r),V(f),V(u)),h&&h.d(m),V(e),k&&k.d(m),V(t),g&&g.d(),E&&E.d(),w&&w.d(m),N&&N.d(m),d&&d.d(m),L&&L.d(m)}}}const jo=/^'([^]+)'$/,Lt="Connection to the server was lost. Attempting reconnection...",Io="Reconnected to server, but the server has changed. You may need to <a href=''>refresh the page</a>.",Fo="Connection re-established.",Go="Session not found - this is likely because the machine you were connected to has changed. <a href=''>Refresh the page</a> to continue.",Uo=15,qo=10;function En(i){return"detail"in i}function Wo(i,e,t){let n,s,o,p,c,a;Ae(i,gi,l=>t(28,c=l));let{root:r}=e,{components:f}=e,{layout:u}=e,{dependencies:_}=e,{title:h="Gradio"}=e,{target:k}=e,{autoscroll:g}=e,{show_api:E=!0}=e,{show_footer:w=!0}=e,{control_page_title:N=!1}=e,{app_mode:d}=e,{theme_mode:L}=e,{app:m}=e,{space_id:M}=e,{version:x}=e,{js:te}=e,{fill_height:S=!1}=e,{ready:$}=e,{username:X}=e,{api_prefix:re=""}=e,{max_file_size:ne=void 0}=e,{initial_layout:le=void 0}=e,{css:Se=null}=e,D=!1,{layout:H,targets:Y,update_value:De,get_data:Pt,modify_stream:ht,get_stream_state:Dt,set_time_limit:Pn,loading_status:Oe,scheduled_updates:pt,create_layout:Dn,rerender_layout:Bn,value_change:jn}=hi({initial_layout:le});Ae(i,H,l=>t(16,a=l)),Ae(i,Y,l=>t(74,o=l)),Ae(i,Oe,l=>t(54,s=l)),Ae(i,pt,l=>t(75,p=l));let bt=_;async function In(){await pi(m.config?.i18n_translations||void 0),t(53,$e=!0),await Dn({components:f,layout:u,dependencies:_,root:r+re,app:m,options:{fill_height:S}}),t(53,$e=!1)}let{search_params:Be}=e,Ze=Be.get("view")==="api"&&E,je=Be.get("view")==="settings",Ce=Be.get("view")==="api-recorder"&&E,Je=!0,Ie=!0,Qe=null,Re=null,wt=null;async function Ke(){if(!Qe||!Re){const l=await tt(()=>import("./ApiDocs-tDCQqm4K.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25]),import.meta.url),C=await tt(()=>import("./ApiRecorder-Bj4fHidL.js"),__vite__mapDeps([26,2,3,4,5,6,7,8,9,10,11,12,13,14,15,27]),import.meta.url);Qe||t(22,Qe=l.default),Re||t(23,Re=C.default)}}async function vt(){if(!Re){const l=await tt(()=>import("./ApiRecorder-Bj4fHidL.js"),__vite__mapDeps([26,2,3,4,5,6,7,8,9,10,11,12,13,14,15,27]),import.meta.url);t(23,Re=l.default)}}async function Ye(){if(!wt){const l=await tt(()=>import("./Settings-DWdlBuRz.js"),__vite__mapDeps([28,2,3,1,10,11,12,29,23,30,31,32,14,33,34,17,18,35,36,22,24,37,38]),import.meta.url);t(24,wt=l.default)}}async function Fe(l){t(19,Ce=!1),l&&await Ke(),t(17,Ze=l);let C=new URLSearchParams(window.location.search);l?C.set("view","api"):C.delete("view"),history.replaceState(null,"","?"+C.toString())}async function Xe(l){l&&await Ye();let C=new URLSearchParams(window.location.search);l?C.set("view","settings"):C.delete("view"),history.replaceState(null,"","?"+C.toString()),t(18,je=!je)}let xe=[],$e=!1,{render_complete:Ge=!1}=e;async function kt(l,C){const v=_.find(F=>F.id===C),j=f.find(F=>F.id===v?.inputs[0])?.type;if(Je&&v&&j!=="dataset"&&(v&&v.inputs&&v.inputs.length>0&&n&&_n(!0,v.inputs,1),v&&v.outputs&&v.outputs.length>0&&n&&_n(!1,v.outputs,2)),!v)return;const b=v.outputs,P=l?.map((F,ie)=>({id:b[ie],prop:"value_is_output",value:!0}));De(P),await rt();const G=[];l?.forEach((F,ie)=>{if(typeof F=="object"&&F!==null&&F.__type__==="update")for(const[Me,z]of Object.entries(F))Me!=="__type__"&&G.push({id:b[ie],prop:Me,value:z});else G.push({id:b[ie],prop:"value",value:F})}),De(G),await rt()}let ae=new Map,O=[];function ee(l,C,v,j,b=10,P=!0){return{title:l,message:C,fn_index:v,type:j,id:++Fn,duration:b,visible:P}}function Et(l,C,v){t(26,O=[ee(l,C,-1,v),...O])}let Fn=-1;const Gn=c("blocks.long_requests_queue"),Un=c("blocks.connection_can_break"),qn=c("blocks.waiting_for_inputs");let Bt=!1,jt=!1,It=!1,Ue=[];function ze(l,C=null,v=null){let j=()=>{};function b(){j()}p?j=pt.subscribe(P=>{P||rt().then(()=>{Ft(l,C,v),b()})}):Ft(l,C,v)}async function Wn(l,C,v){return l===C&&v&&v.is_value_data===!0?v.value:Pt(l)}async function Ft(l,C=null,v=null){const j=_.find(z=>z.id===l);if(j===void 0)return;const b=j;if(Ue.length>0){for(const z of Ue)if(b.inputs.includes(z)){Et("Warning",qn,"warning");return}}const P=Oe.get_status_for_fn(l);t(26,O=O.filter(({fn_index:z})=>z!==l)),(P==="pending"||P==="generating")&&(b.pending_request=!0);let G={fn_index:l,data:await Promise.all(b.inputs.map(z=>Wn(z,C,v))),event_data:b.collects_event_data?v:null,trigger_id:C};b.frontend_fn&&typeof b.frontend_fn!="boolean"?b.frontend_fn(G.data.concat(await Promise.all(b.outputs.map(z=>Pt(z))))).then(z=>{b.backend_fn?(G.data=z,F(b,G)):kt(z,l)}):b.types.cancel&&b.cancels?await Promise.all(b.cancels.map(async z=>{const U=ae.get(z);return U?.cancel(),U})):b.backend_fn&&(b.js_implementation&&new Wt(`let result = await (${b.js_implementation})(...arguments);
						return (!Array.isArray(result)) ? [result] : result;`)(...G.data).then(U=>{kt(U,l),G.js_implementation=!0}).catch(U=>{console.error(U),G.js_implementation=!1}),F(b,G));function F(z,U){z.trigger_mode==="once"?z.pending_request||Me(U,z.connection=="stream"):z.trigger_mode==="multiple"?Me(U,z.connection=="stream"):z.trigger_mode==="always_last"&&(z.pending_request?z.final_event=U:Me(U,z.connection=="stream"))}async function ie(){const z=await m.reconnect();z==="broken"?setTimeout(ie,1e3):z==="changed"?(D=!1,t(26,O=[ee("Changed Connection",Io,-1,"info",3,!0),...O.map(U=>U.message===Lt?{...U,visible:!1}:U)])):z==="connected"&&(D=!1,t(26,O=[ee("Reconnected",Fo,-1,"success",null,!0),...O.map(U=>U.message===Lt?{...U,visible:!1}:U)]))}async function Me(z,U=!1){Ie&&Eo(),Ce&&t(25,xe=[...xe,JSON.parse(JSON.stringify(z))]);let zt;if(m.set_current_payload(z),U)if(!ae.has(l))b.inputs.forEach(T=>ht(T,"waiting"));else{if(ae.has(l)&&b.inputs.some(T=>Dt(T)==="waiting"))return;if(ae.has(l)&&b.inputs.some(T=>Dt(T)==="open")){await m.send_ws_message(`${m.config.root+m.config.api_prefix}/stream/${ae.get(l).event_id()}`,{...z,session_hash:m.session_hash});return}}try{zt=m.submit(z.fn_index,z.data,z.event_data,z.trigger_id)}catch(T){if(m.closed)return;t(26,O=[ee("Error",String(T),0,"error"),...O]),Oe.update({status:"error",fn_index:0,eta:0,queue:!1,queue_position:null}),et(s);return}ae.set(l,zt);for await(const T of zt){if(z.js_implementation)return;T.type==="data"?ai(T):T.type==="render"?ci(T):T.type==="status"?ui(T):T.type==="log"&&_i(T)}function ai(T){const{data:Z,fn_index:R}=T;b.pending_request&&b.final_event&&(b.pending_request=!1,Me(b.final_event,b.connection=="stream")),b.pending_request=!1,kt(Z,R),et(s)}function ci(T){const{data:Z}=T;let R=Z.components,q=Z.layout,oe=Z.dependencies,me=Z.render_id,de=[];_.forEach((ce,Mt)=>{ce.rendered_in===b.render_id&&de.push(Mt)}),de.reverse().forEach(ce=>{_.splice(ce,1)}),oe.forEach(ce=>{_.push(ce)}),Bn({components:R,layout:q,root:r+re,dependencies:_,render_id:me}),oe.forEach(ce=>{ce.targets.some(Mt=>Mt[1]==="load")&&ze(ce.id)})}function _i(T){const{title:Z,log:R,fn_index:q,level:oe,duration:me,visible:de}=T;t(26,O=[ee(Z,R,q,oe,me,de),...O])}function fi(T,Z,R){T.original_msg==="process_starts"&&R.connection==="stream"&&ht(Z,"open")}function ui(T){T.broken&&!D&&(t(26,O=[ee("Broken Connection",Lt,-1,"error",null,!0),...O]),D=!0,setTimeout(ie,1e3)),T.session_not_found&&t(26,O=[ee("Session Not Found",Go,-1,"error",null,!0),...O]);const{fn_index:Z,...R}=T;if(R.stage==="streaming"&&R.time_limit&&b.inputs.forEach(q=>{Pn(q,R.time_limit)}),b.inputs.forEach(q=>{fi(T,q,b)}),Oe.update({...R,time_limit:R.time_limit,status:R.stage,progress:R.progress_data,fn_index:Z}),et(s),!jt&&M!==null&&R.position!==void 0&&R.position>=2&&R.eta!==void 0&&R.eta>Uo&&(jt=!0,t(26,O=[ee("Warning",Gn,Z,"warning"),...O])),!It&&Bt&&R.eta!==void 0&&R.eta>qo&&(It=!0,t(26,O=[ee("Warning",Un,Z,"warning"),...O])),R.stage==="complete"||R.stage==="generating"){const q=new Set;R.changed_state_ids?.forEach(oe=>{_.filter(me=>me.targets.some(([de,ce])=>de===oe)).forEach(me=>{q.add(me)})}),q.forEach(oe=>{ze(oe.id,z.trigger_id)})}if(R.stage==="complete"&&(_.forEach(async q=>{q.trigger_after===Z&&ze(q.id,z.trigger_id)}),b.inputs.forEach(q=>{ht(q,"closed")}),ae.delete(l)),R.stage==="error"&&!D&&!T.session_not_found){if(R.message){const q=R.message.replace(jo,(me,de)=>de),oe=R.title??"Error";t(26,O=[ee(oe,q,Z,"error",R.duration,R.visible),...O])}_.map(async q=>{q.trigger_after===Z&&!q.trigger_only_on_success&&ze(q.id,z.trigger_id)})}}Ie&&So()}}function Hn(l,C){if(M===null)return;const v=new URL(`https://huggingface.co/spaces/${M}/discussions/new`);l!==void 0&&l.length>0&&v.searchParams.set("title",l),v.searchParams.set("description",C),window.open(v.toString(),"_blank")}function Vn(l){const C=l.detail;t(26,O=O.filter(v=>v.id!==C))}const Zn=l=>!!(l&&new URL(l,location.href).origin!==location.origin);async function Jn(){te&&await new Wt(`let result = await (${te})();
					return (!Array.isArray(result)) ? [result] : result;`)(),await rt();for(var l=k.getElementsByTagName("a"),C=0;C<l.length;C++){const v=l[C].getAttribute("target"),j=l[C].getAttribute("href");Zn(j)&&v!=="_blank"&&l[C].setAttribute("target","_blank")}Gt(),!(!k||Ge)&&(k.addEventListener("prop_change",v=>{if(!En(v))throw new Error("not a custom event");const{id:j,prop:b,value:P}=v.detail;De([{id:j,prop:b,value:P}]),b==="input_ready"&&P===!1&&Ue.push(j),b==="input_ready"&&P===!0&&(Ue=Ue.filter(G=>G!==j))}),k.addEventListener("gradio",v=>{if(!En(v))throw new Error("not a custom event");const{id:j,event:b,data:P}=v.detail;if(b==="share"){const{title:G,description:F}=P;Hn(G,F)}else b==="error"?t(26,O=[ee("Error",P,-1,b),...O]):b==="warning"?t(26,O=[ee("Warning",P,-1,b),...O]):b==="info"?t(26,O=[ee("Info",P,-1,b),...O]):b=="clear_status"?Qn(j,"complete",P):b=="close_stream"?o[j]?.[P]?.forEach(F=>{if(ae.has(F)){const ie=`${m.config.root+m.config.api_prefix}/stream/${ae.get(F).event_id()}`;m.post_data(`${ie}/close`,{}),m.close_ws(ie)}}):o[j]?.[b]?.forEach(F=>{requestAnimationFrame(()=>{ze(F,j,P)})})}),t(42,Ge=!0))}jn((l,C)=>{o[l]?.change?.forEach(j=>{requestAnimationFrame(()=>{ze(j,l,C)})})});const Gt=()=>{_.forEach(l=>{l.targets.some(C=>C[1]==="load")&&ze(l.id)})};function Qn(l,C,v){v.status=C,De([{id:l,prop:"loading_status",value:v}])}function et(l){let C=[];Object.entries(l).forEach(([b,P])=>{if(m.closed&&P.status==="error")return;let G=_.find(F=>F.id==P.fn_index);G!==void 0&&(P.scroll_to_output=G.scroll_to_output,P.show_progress=G.show_progress,C.push({id:parseInt(b),prop:"loading_status",value:P}))});const v=Oe.get_inputs_to_update(),j=Array.from(v).map(([b,P])=>({id:b,prop:"pending",value:P==="pending"}));De([...C,...j])}let St=bi(!1);Ae(i,St,l=>t(27,n=l)),To(()=>{Bt=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),wo(r,(l,C,v)=>{Et(l,C,v)},l=>{Ao(St,n=l,n)}),Ze&&Ke(),Ce&&vt(),je&&Ye()});function Ct(){n?ko():vo()}const Kn=()=>{Fe(!Ze)},Yn=()=>{Ke(),vt()},Xn=()=>{Ct()},xn=()=>{Xe(!je)},$n=()=>{Ye()},ei=()=>{Fe(!0),t(19,Ce=!1)},ti=()=>{Fe(!1)},ni=l=>{Fe(!1),t(25,xe=[]),t(19,Ce=t(19,Ce=l.detail?.api_recorder_visible))},ii=()=>{Xe(!1)};function oi(l){Je=l,t(20,Je)}function si(l){Ie=l,t(21,Ie)}const ri=()=>{Xe(!1)},li=()=>{Ct()};return i.$$set=l=>{"root"in l&&t(0,r=l.root),"components"in l&&t(44,f=l.components),"layout"in l&&t(45,u=l.layout),"dependencies"in l&&t(1,_=l.dependencies),"title"in l&&t(2,h=l.title),"target"in l&&t(3,k=l.target),"autoscroll"in l&&t(4,g=l.autoscroll),"show_api"in l&&t(5,E=l.show_api),"show_footer"in l&&t(6,w=l.show_footer),"control_page_title"in l&&t(7,N=l.control_page_title),"app_mode"in l&&t(8,d=l.app_mode),"theme_mode"in l&&t(9,L=l.theme_mode),"app"in l&&t(10,m=l.app),"space_id"in l&&t(11,M=l.space_id),"version"in l&&t(12,x=l.version),"js"in l&&t(46,te=l.js),"fill_height"in l&&t(47,S=l.fill_height),"ready"in l&&t(43,$=l.ready),"username"in l&&t(13,X=l.username),"api_prefix"in l&&t(48,re=l.api_prefix),"max_file_size"in l&&t(14,ne=l.max_file_size),"initial_layout"in l&&t(49,le=l.initial_layout),"css"in l&&t(15,Se=l.css),"search_params"in l&&t(50,Be=l.search_params),"render_complete"in l&&t(42,Ge=l.render_complete)},i.$$.update=()=>{i.$$.dirty[0]&1035|i.$$.dirty[1]&90112&&In(),i.$$.dirty[0]&65536&&t(43,$=!!a),i.$$.dirty[0]&2|i.$$.dirty[1]&6293504&&_!==bt&&Ge&&!$e&&(Gt(),t(52,bt=_)),i.$$.dirty[1]&8388608&&et(s)},[r,_,h,k,g,E,w,N,d,L,m,M,x,X,ne,Se,a,Ze,je,Ce,Je,Ie,Qe,Re,wt,xe,O,n,c,H,Y,Oe,pt,Ke,vt,Ye,Fe,Xe,Vn,Jn,St,Ct,Ge,$,f,u,te,S,re,le,Be,Et,bt,$e,s,Kn,Yn,Xn,xn,$n,ei,ti,ni,ii,oi,si,ri,li]}class Ho extends No{constructor(e){super(),Oo(this,e,Wo,Bo,Ro,{root:0,components:44,layout:45,dependencies:1,title:2,target:3,autoscroll:4,show_api:5,show_footer:6,control_page_title:7,app_mode:8,theme_mode:9,app:10,space_id:11,version:12,js:46,fill_height:47,ready:43,username:13,api_prefix:48,max_file_size:14,initial_layout:49,css:15,search_params:50,render_complete:42,add_new_message:51},null,[-1,-1,-1,-1])}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),B()}get components(){return this.$$.ctx[44]}set components(e){this.$$set({components:e}),B()}get layout(){return this.$$.ctx[45]}set layout(e){this.$$set({layout:e}),B()}get dependencies(){return this.$$.ctx[1]}set dependencies(e){this.$$set({dependencies:e}),B()}get title(){return this.$$.ctx[2]}set title(e){this.$$set({title:e}),B()}get target(){return this.$$.ctx[3]}set target(e){this.$$set({target:e}),B()}get autoscroll(){return this.$$.ctx[4]}set autoscroll(e){this.$$set({autoscroll:e}),B()}get show_api(){return this.$$.ctx[5]}set show_api(e){this.$$set({show_api:e}),B()}get show_footer(){return this.$$.ctx[6]}set show_footer(e){this.$$set({show_footer:e}),B()}get control_page_title(){return this.$$.ctx[7]}set control_page_title(e){this.$$set({control_page_title:e}),B()}get app_mode(){return this.$$.ctx[8]}set app_mode(e){this.$$set({app_mode:e}),B()}get theme_mode(){return this.$$.ctx[9]}set theme_mode(e){this.$$set({theme_mode:e}),B()}get app(){return this.$$.ctx[10]}set app(e){this.$$set({app:e}),B()}get space_id(){return this.$$.ctx[11]}set space_id(e){this.$$set({space_id:e}),B()}get version(){return this.$$.ctx[12]}set version(e){this.$$set({version:e}),B()}get js(){return this.$$.ctx[46]}set js(e){this.$$set({js:e}),B()}get fill_height(){return this.$$.ctx[47]}set fill_height(e){this.$$set({fill_height:e}),B()}get ready(){return this.$$.ctx[43]}set ready(e){this.$$set({ready:e}),B()}get username(){return this.$$.ctx[13]}set username(e){this.$$set({username:e}),B()}get api_prefix(){return this.$$.ctx[48]}set api_prefix(e){this.$$set({api_prefix:e}),B()}get max_file_size(){return this.$$.ctx[14]}set max_file_size(e){this.$$set({max_file_size:e}),B()}get initial_layout(){return this.$$.ctx[49]}set initial_layout(e){this.$$set({initial_layout:e}),B()}get css(){return this.$$.ctx[15]}set css(e){this.$$set({css:e}),B()}get search_params(){return this.$$.ctx[50]}set search_params(e){this.$$set({search_params:e}),B()}get render_complete(){return this.$$.ctx[42]}set render_complete(e){this.$$set({render_complete:e}),B()}get add_new_message(){return this.$$.ctx[51]}}const Xo=Object.freeze(Object.defineProperty({__proto__:null,default:Ho},Symbol.toStringTag,{value:"Module"}));export{Xo as B,ho as a,po as s};
//# sourceMappingURL=Blocks-DQBXfWaA.js.map
