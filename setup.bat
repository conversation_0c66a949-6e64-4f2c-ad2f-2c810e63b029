@echo off
echo ========================================
echo    Reverie Agents 环境配置脚本
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Python未安装或未添加到PATH
    echo 请先安装Python 3.9+
    pause
    exit /b 1
)

echo [信息] 检测到Python版本:
python --version

:: 创建虚拟环境
echo.
echo [步骤1] 创建虚拟环境...
if exist "venv" (
    echo [信息] 虚拟环境已存在，跳过创建
) else (
    python -m venv venv
    if %errorlevel% neq 0 (
        echo [错误] 虚拟环境创建失败
        pause
        exit /b 1
    )
    echo [成功] 虚拟环境创建完成
)

:: 激活虚拟环境
echo.
echo [步骤2] 激活虚拟环境...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo [错误] 虚拟环境激活失败
    pause
    exit /b 1
)

:: 升级pip
echo.
echo [步骤3] 升级pip...
python -m pip install --upgrade pip

:: 安装PyTorch (CUDA 12.8)
echo.
echo [步骤4] 安装PyTorch (CUDA 12.8)...
pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128

:: 安装其他依赖
echo.
echo [步骤5] 安装项目依赖...
pip install -r requirements.txt

:: 创建必要的目录
echo.
echo [步骤6] 创建必要的目录...
if not exist "logs" mkdir logs
if not exist "models\llm" mkdir models\llm
if not exist "models\t2i" mkdir models\t2i
if not exist "memory\storage" mkdir memory\storage
if not exist "config\settings" mkdir config\settings

:: 创建默认配置文件
echo.
echo [步骤7] 创建默认配置文件...
if not exist "config\settings\app_config.yaml" (
    echo # Reverie Agents 配置文件 > config\settings\app_config.yaml
    echo app: >> config\settings\app_config.yaml
    echo   name: "Reverie Agents" >> config\settings\app_config.yaml
    echo   version: "1.0.0" >> config\settings\app_config.yaml
    echo   debug: false >> config\settings\app_config.yaml
)

echo.
echo ========================================
echo    环境配置完成！
echo ========================================
echo.
echo 使用说明:
echo   - 启动桌面应用: run_app.bat
echo   - 启动Web界面: run_web.bat
echo   - 查看文档: docs\README.md
echo.
pause
