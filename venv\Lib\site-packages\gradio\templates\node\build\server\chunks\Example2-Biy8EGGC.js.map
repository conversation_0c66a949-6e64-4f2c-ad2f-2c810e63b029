{"version": 3, "file": "Example2-Biy8EGGC.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Example2.js"], "sourcesContent": ["import{create_ssr_component as a,escape as v}from\"svelte/internal\";const m=a((f,t,e,c)=>{let{title:l}=t,{x:o}=t,{y:x}=t;return t.title===void 0&&e.title&&l!==void 0&&e.title(l),t.x===void 0&&e.x&&o!==void 0&&e.x(o),t.y===void 0&&e.y&&x!==void 0&&e.y(x),`${l?`${v(l)}`:`${v(o)} x ${v(x)}`}`});export{m as default};\n//# sourceMappingURL=Example2.js.map\n"], "names": ["a", "v"], "mappings": ";;AAAwE,MAAC,CAAC,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEC,MAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,MAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAEA,MAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;"}