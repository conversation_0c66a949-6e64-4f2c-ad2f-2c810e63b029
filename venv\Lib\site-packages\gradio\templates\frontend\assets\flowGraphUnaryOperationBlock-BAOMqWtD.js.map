{"version": 3, "file": "flowGraphUnaryOperationBlock-BAOMqWtD.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphUnaryOperationBlock.js"], "sourcesContent": ["import { FlowGraphCachedOperationBlock } from \"./flowGraphCachedOperationBlock.js\";\n/**\n * @internal\n * The base block for all unary operation blocks. Receives an input of type InputT, and outputs a value of type ResultT.\n */\nexport class FlowGraphUnaryOperationBlock extends FlowGraphCachedOperationBlock {\n    constructor(inputRichType, resultRichType, _operation, _className, config) {\n        super(resultRichType, config);\n        this._operation = _operation;\n        this._className = _className;\n        this.a = this.registerDataInput(\"a\", inputRichType);\n    }\n    /**\n     * the operation performed by this block\n     * @param context the graph context\n     * @returns the result of the operation\n     */\n    _doOperation(context) {\n        return this._operation(this.a.getValue(context));\n    }\n    /**\n     * Gets the class name of this block\n     * @returns the class name\n     */\n    getClassName() {\n        return this._className;\n    }\n}\n//# sourceMappingURL=flowGraphUnaryOperationBlock.js.map"], "names": ["FlowGraphUnaryOperationBlock", "FlowGraphCachedOperationBlock", "inputRichType", "resultRichType", "_operation", "_className", "config", "context"], "mappings": "gEAKO,MAAMA,UAAqCC,CAA8B,CAC5E,YAAYC,EAAeC,EAAgBC,EAAYC,EAAYC,EAAQ,CACvE,MAAMH,EAAgBG,CAAM,EAC5B,KAAK,WAAaF,EAClB,KAAK,WAAaC,EAClB,KAAK,EAAI,KAAK,kBAAkB,IAAKH,CAAa,CACrD,CAMD,aAAaK,EAAS,CAClB,OAAO,KAAK,WAAW,KAAK,EAAE,SAASA,CAAO,CAAC,CAClD,CAKD,cAAe,CACX,OAAO,KAAK,UACf,CACL", "x_google_ignoreList": [0]}