import{R as i}from"./index.BoI39RQH.js";import{f as s}from"./KHR_interactivity.DEAVS2UW.js";class n extends s{constructor(e){super(e),this.config=e,this.executionSignals=[],this.setNumberOfOutputSignals(this.config.outputSignalCount)}_execute(e){for(let t=0;t<this.executionSignals.length;t++)this.executionSignals[t]._activateSignal(e)}setNumberOfOutputSignals(e=1){for(;this.executionSignals.length>e;){const t=this.executionSignals.pop();t&&(t.disconnectFromAll(),this._unregisterSignalOutput(t.name))}for(;this.executionSignals.length<e;)this.executionSignals.push(this._registerSignalOutput(`out_${this.executionSignals.length}`))}getClassName(){return"FlowGraphSequenceBlock"}}i("FlowGraphSequenceBlock",n);export{n as FlowGraphSequenceBlock};
//# sourceMappingURL=flowGraphSequenceBlock.CEbTwj2T.js.map
