/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import{I as D}from"./IconButton-C_HS7fTi.js";import{C as q}from"./Clear-By3xiIwg.js";import{D as L}from"./Download-DVtk-Jv3.js";import{E as P}from"./Edit-BpRIf5rU.js";import{U as S}from"./Undo-DCjBnnSO.js";import"./index-B7J2Z2jS.js";import{I as W}from"./IconButtonWrapper--EIOWuEM.js";import{D as j}from"./DownloadLink-QIttOhoR.js";const{SvelteComponent:v,check_outros:C,create_component:$,create_slot:z,destroy_component:p,detach:g,flush:k,get_all_dirty_from_scope:A,get_slot_changes:F,group_outros:E,init:G,insert:h,mount_component:b,safe_not_equal:H,space:I,transition_in:c,transition_out:_,update_slot_base:J}=window.__gradio__svelte__internal,{createEventDispatcher:K}=window.__gradio__svelte__internal;function U(r){let e,o;return e=new D({props:{Icon:P,label:r[3]("common.edit")}}),e.$on("click",r[6]),{c(){$(e.$$.fragment)},m(t,i){b(e,t,i),o=!0},p(t,i){const f={};i&8&&(f.label=t[3]("common.edit")),e.$set(f)},i(t){o||(c(e.$$.fragment,t),o=!0)},o(t){_(e.$$.fragment,t),o=!1},d(t){p(e,t)}}}function B(r){let e,o;return e=new D({props:{Icon:S,label:r[3]("common.undo")}}),e.$on("click",r[7]),{c(){$(e.$$.fragment)},m(t,i){b(e,t,i),o=!0},p(t,i){const f={};i&8&&(f.label=t[3]("common.undo")),e.$set(f)},i(t){o||(c(e.$$.fragment,t),o=!0)},o(t){_(e.$$.fragment,t),o=!1},d(t){p(e,t)}}}function M(r){let e,o;return e=new j({props:{href:r[2],download:!0,$$slots:{default:[O]},$$scope:{ctx:r}}}),{c(){$(e.$$.fragment)},m(t,i){b(e,t,i),o=!0},p(t,i){const f={};i&4&&(f.href=t[2]),i&520&&(f.$$scope={dirty:i,ctx:t}),e.$set(f)},i(t){o||(c(e.$$.fragment,t),o=!0)},o(t){_(e.$$.fragment,t),o=!1},d(t){p(e,t)}}}function O(r){let e,o;return e=new D({props:{Icon:L,label:r[3]("common.download")}}),{c(){$(e.$$.fragment)},m(t,i){b(e,t,i),o=!0},p(t,i){const f={};i&8&&(f.label=t[3]("common.download")),e.$set(f)},i(t){o||(c(e.$$.fragment,t),o=!0)},o(t){_(e.$$.fragment,t),o=!1},d(t){p(e,t)}}}function Q(r){let e,o,t,i,f,d,a=r[0]&&U(r),u=r[1]&&B(r),s=r[2]&&M(r);const w=r[5].default,m=z(w,r,r[9],null);return f=new D({props:{Icon:q,label:r[3]("common.clear")}}),f.$on("click",r[8]),{c(){a&&a.c(),e=I(),u&&u.c(),o=I(),s&&s.c(),t=I(),m&&m.c(),i=I(),$(f.$$.fragment)},m(n,l){a&&a.m(n,l),h(n,e,l),u&&u.m(n,l),h(n,o,l),s&&s.m(n,l),h(n,t,l),m&&m.m(n,l),h(n,i,l),b(f,n,l),d=!0},p(n,l){n[0]?a?(a.p(n,l),l&1&&c(a,1)):(a=U(n),a.c(),c(a,1),a.m(e.parentNode,e)):a&&(E(),_(a,1,1,()=>{a=null}),C()),n[1]?u?(u.p(n,l),l&2&&c(u,1)):(u=B(n),u.c(),c(u,1),u.m(o.parentNode,o)):u&&(E(),_(u,1,1,()=>{u=null}),C()),n[2]?s?(s.p(n,l),l&4&&c(s,1)):(s=M(n),s.c(),c(s,1),s.m(t.parentNode,t)):s&&(E(),_(s,1,1,()=>{s=null}),C()),m&&m.p&&(!d||l&512)&&J(m,w,n,n[9],d?F(w,n[9],l,null):A(n[9]),null);const N={};l&8&&(N.label=n[3]("common.clear")),f.$set(N)},i(n){d||(c(a),c(u),c(s),c(m,n),c(f.$$.fragment,n),d=!0)},o(n){_(a),_(u),_(s),_(m,n),_(f.$$.fragment,n),d=!1},d(n){n&&(g(e),g(o),g(t),g(i)),a&&a.d(n),u&&u.d(n),s&&s.d(n),m&&m.d(n),p(f,n)}}}function R(r){let e,o;return e=new W({props:{$$slots:{default:[Q]},$$scope:{ctx:r}}}),{c(){$(e.$$.fragment)},m(t,i){b(e,t,i),o=!0},p(t,[i]){const f={};i&527&&(f.$$scope={dirty:i,ctx:t}),e.$set(f)},i(t){o||(c(e.$$.fragment,t),o=!0)},o(t){_(e.$$.fragment,t),o=!1},d(t){p(e,t)}}}function T(r,e,o){let{$$slots:t={},$$scope:i}=e,{editable:f=!1}=e,{undoable:d=!1}=e,{download:a=null}=e,{i18n:u}=e;const s=K(),w=()=>s("edit"),m=()=>s("undo"),n=l=>{s("clear"),l.stopPropagation()};return r.$$set=l=>{"editable"in l&&o(0,f=l.editable),"undoable"in l&&o(1,d=l.undoable),"download"in l&&o(2,a=l.download),"i18n"in l&&o(3,u=l.i18n),"$$scope"in l&&o(9,i=l.$$scope)},[f,d,a,u,s,t,w,m,n,i]}class le extends v{constructor(e){super(),G(this,e,T,R,H,{editable:0,undoable:1,download:2,i18n:3})}get editable(){return this.$$.ctx[0]}set editable(e){this.$$set({editable:e}),k()}get undoable(){return this.$$.ctx[1]}set undoable(e){this.$$set({undoable:e}),k()}get download(){return this.$$.ctx[2]}set download(e){this.$$set({download:e}),k()}get i18n(){return this.$$.ctx[3]}set i18n(e){this.$$set({i18n:e}),k()}}export{le as M};
//# sourceMappingURL=ModifyUpload-5lgGIpMc.js.map
