import"./index-B7J2Z2jS.js";import{B as I}from"./Button-B3gqVEq2.js";import"./svelte/svelte.js";import"./Image-CnqB5dbD.js";import"./file-url-DoxvUUVV.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import"./prism-python-MMh3z1bK.js";/* empty css                                                   */const{SvelteComponent:N,attr:z,create_component:A,create_slot:F,destroy_component:G,detach:D,element:H,flush:o,get_all_dirty_from_scope:J,get_slot_changes:K,init:L,insert:S,mount_component:M,safe_not_equal:O,space:P,src_url_equal:C,transition_in:E,transition_out:j,update_slot_base:Q}=window.__gradio__svelte__internal,{createEventDispatcher:R}=window.__gradio__svelte__internal;function q(l){let e,i,t;return{c(){e=H("img"),z(e,"class","button-icon svelte-yjn27e"),C(e.src,i=l[6].url)||z(e,"src",i),z(e,"alt",t=`${l[5]} icon`)},m(s,n){S(s,e,n)},p(s,n){n&64&&!C(e.src,i=s[6].url)&&z(e,"src",i),n&32&&t!==(t=`${s[5]} icon`)&&z(e,"alt",t)},d(s){s&&D(e)}}}function T(l){let e,i,t=l[6]&&q(l);const s=l[11].default,n=F(s,l,l[12],null);return{c(){t&&t.c(),e=P(),n&&n.c()},m(_,u){t&&t.m(_,u),S(_,e,u),n&&n.m(_,u),i=!0},p(_,u){_[6]?t?t.p(_,u):(t=q(_),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null),n&&n.p&&(!i||u&4096)&&Q(n,s,_,_[12],i?K(s,_[12],u,null):J(_[12]),null)},i(_){i||(E(n,_),i=!0)},o(_){j(n,_),i=!1},d(_){_&&D(e),t&&t.d(_),n&&n.d(_)}}}function U(l){let e,i;return e=new I({props:{size:l[4],variant:l[3],elem_id:l[0],elem_classes:l[1],visible:l[2],scale:l[8],min_width:l[9],disabled:l[7],$$slots:{default:[T]},$$scope:{ctx:l}}}),e.$on("click",l[10]),{c(){A(e.$$.fragment)},m(t,s){M(e,t,s),i=!0},p(t,[s]){const n={};s&16&&(n.size=t[4]),s&8&&(n.variant=t[3]),s&1&&(n.elem_id=t[0]),s&2&&(n.elem_classes=t[1]),s&4&&(n.visible=t[2]),s&256&&(n.scale=t[8]),s&512&&(n.min_width=t[9]),s&128&&(n.disabled=t[7]),s&4192&&(n.$$scope={dirty:s,ctx:t}),e.$set(n)},i(t){i||(E(e.$$.fragment,t),i=!0)},o(t){j(e.$$.fragment,t),i=!1},d(t){G(e,t)}}}function V(l,e,i){let{$$slots:t={},$$scope:s}=e,{elem_id:n=""}=e,{elem_classes:_=[]}=e,{visible:u=!0}=e,{variant:h="secondary"}=e,{size:d="lg"}=e,{value:m}=e,{icon:g}=e,{disabled:v=!1}=e,{scale:b=null}=e,{min_width:r=void 0}=e;const k=R();function c(){if(k("click"),!m?.url)return;let a;if(!m.orig_name&&m.url){const B=m.url.split("/");a=B[B.length-1],a=a.split("?")[0].split("#")[0]}else a=m.orig_name;const w=document.createElement("a");w.href=m.url,w.download=a||"file",document.body.appendChild(w),w.click(),document.body.removeChild(w)}return l.$$set=a=>{"elem_id"in a&&i(0,n=a.elem_id),"elem_classes"in a&&i(1,_=a.elem_classes),"visible"in a&&i(2,u=a.visible),"variant"in a&&i(3,h=a.variant),"size"in a&&i(4,d=a.size),"value"in a&&i(5,m=a.value),"icon"in a&&i(6,g=a.icon),"disabled"in a&&i(7,v=a.disabled),"scale"in a&&i(8,b=a.scale),"min_width"in a&&i(9,r=a.min_width),"$$scope"in a&&i(12,s=a.$$scope)},[n,_,u,h,d,m,g,v,b,r,c,t,s]}class W extends N{constructor(e){super(),L(this,e,V,U,O,{elem_id:0,elem_classes:1,visible:2,variant:3,size:4,value:5,icon:6,disabled:7,scale:8,min_width:9})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),o()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),o()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),o()}get variant(){return this.$$.ctx[3]}set variant(e){this.$$set({variant:e}),o()}get size(){return this.$$.ctx[4]}set size(e){this.$$set({size:e}),o()}get value(){return this.$$.ctx[5]}set value(e){this.$$set({value:e}),o()}get icon(){return this.$$.ctx[6]}set icon(e){this.$$set({icon:e}),o()}get disabled(){return this.$$.ctx[7]}set disabled(e){this.$$set({disabled:e}),o()}get scale(){return this.$$.ctx[8]}set scale(e){this.$$set({scale:e}),o()}get min_width(){return this.$$.ctx[9]}set min_width(e){this.$$set({min_width:e}),o()}}const X=W,{SvelteComponent:Y,create_component:Z,destroy_component:y,detach:x,flush:f,init:$,insert:p,mount_component:ee,safe_not_equal:te,set_data:ie,text:se,transition_in:ne,transition_out:le}=window.__gradio__svelte__internal;function ae(l){let e=(l[10]??"")+"",i;return{c(){i=se(e)},m(t,s){p(t,i,s)},p(t,s){s&1024&&e!==(e=(t[10]??"")+"")&&ie(i,e)},d(t){t&&x(i)}}}function ce(l){let e,i;return e=new X({props:{value:l[3],variant:l[4],elem_id:l[0],elem_classes:l[1],size:l[6],scale:l[7],icon:l[8],min_width:l[9],visible:l[2],disabled:!l[5],$$slots:{default:[ae]},$$scope:{ctx:l}}}),e.$on("click",l[12]),{c(){Z(e.$$.fragment)},m(t,s){ee(e,t,s),i=!0},p(t,[s]){const n={};s&8&&(n.value=t[3]),s&16&&(n.variant=t[4]),s&1&&(n.elem_id=t[0]),s&2&&(n.elem_classes=t[1]),s&64&&(n.size=t[6]),s&128&&(n.scale=t[7]),s&256&&(n.icon=t[8]),s&512&&(n.min_width=t[9]),s&4&&(n.visible=t[2]),s&32&&(n.disabled=!t[5]),s&9216&&(n.$$scope={dirty:s,ctx:t}),e.$set(n)},i(t){i||(ne(e.$$.fragment,t),i=!0)},o(t){le(e.$$.fragment,t),i=!1},d(t){y(e,t)}}}function _e(l,e,i){let{elem_id:t=""}=e,{elem_classes:s=[]}=e,{visible:n=!0}=e,{value:_}=e,{variant:u="secondary"}=e,{interactive:h}=e,{size:d="lg"}=e,{scale:m=null}=e,{icon:g=null}=e,{min_width:v=void 0}=e,{label:b=null}=e,{gradio:r}=e;const k=()=>r.dispatch("click");return l.$$set=c=>{"elem_id"in c&&i(0,t=c.elem_id),"elem_classes"in c&&i(1,s=c.elem_classes),"visible"in c&&i(2,n=c.visible),"value"in c&&i(3,_=c.value),"variant"in c&&i(4,u=c.variant),"interactive"in c&&i(5,h=c.interactive),"size"in c&&i(6,d=c.size),"scale"in c&&i(7,m=c.scale),"icon"in c&&i(8,g=c.icon),"min_width"in c&&i(9,v=c.min_width),"label"in c&&i(10,b=c.label),"gradio"in c&&i(11,r=c.gradio)},[t,s,n,_,u,h,d,m,g,v,b,r,k]}class we extends Y{constructor(e){super(),$(this,e,_e,ce,te,{elem_id:0,elem_classes:1,visible:2,value:3,variant:4,interactive:5,size:6,scale:7,icon:8,min_width:9,label:10,gradio:11})}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),f()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),f()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),f()}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),f()}get variant(){return this.$$.ctx[4]}set variant(e){this.$$set({variant:e}),f()}get interactive(){return this.$$.ctx[5]}set interactive(e){this.$$set({interactive:e}),f()}get size(){return this.$$.ctx[6]}set size(e){this.$$set({size:e}),f()}get scale(){return this.$$.ctx[7]}set scale(e){this.$$set({scale:e}),f()}get icon(){return this.$$.ctx[8]}set icon(e){this.$$set({icon:e}),f()}get min_width(){return this.$$.ctx[9]}set min_width(e){this.$$set({min_width:e}),f()}get label(){return this.$$.ctx[10]}set label(e){this.$$set({label:e}),f()}get gradio(){return this.$$.ctx[11]}set gradio(e){this.$$set({gradio:e}),f()}}export{X as BaseButton,we as default};
//# sourceMappingURL=Index-DRJTgarg.js.map
