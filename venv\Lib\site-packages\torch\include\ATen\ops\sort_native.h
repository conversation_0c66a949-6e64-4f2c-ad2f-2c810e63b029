#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>
#include <ATen/ops/sort_meta.h>

namespace at {
namespace native {
TORCH_API ::std::tuple<at::Tensor,at::Tensor> sort(const at::Tensor & self, int64_t dim=-1, bool descending=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> sort_out(const at::Tensor & self, int64_t dim, bool descending, at::Tensor & values, at::Tensor & indices);
struct TORCH_API structured_sort_stable_out : public at::meta::structured_sort_stable {
void impl(const at::Tensor & self, ::std::optional<bool> stable, int64_t dim, bool descending, const at::Tensor & values, const at::Tensor & indices);
};
TORCH_API ::std::tuple<at::Tensor,at::Tensor> sort_quantized_cpu_stable(const at::Tensor & self, ::std::optional<bool> stable, int64_t dim=-1, bool descending=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> sort(const at::Tensor & self, at::Dimname dim, bool descending=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> sort_out(const at::Tensor & self, at::Dimname dim, bool descending, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> sort(const at::Tensor & self, ::std::optional<bool> stable, at::Dimname dim, bool descending=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> sort_out(const at::Tensor & self, ::std::optional<bool> stable, at::Dimname dim, bool descending, at::Tensor & values, at::Tensor & indices);
} // namespace native
} // namespace at
