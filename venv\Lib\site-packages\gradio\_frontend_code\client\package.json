{"name": "@gradio/client", "version": "1.15.7", "description": "Gradio API client", "type": "module", "main": "dist/index.js", "author": "", "license": "ISC", "exports": {".": {"gradio": "./src/index.ts", "browser": "./dist/browser.js", "import": "./dist/index.js", "default": "./dist/index.js"}, "./package.json": "./package.json", "./browser.js": "./dist/browser.js"}, "dependencies": {"@types/eventsource": "^1.1.15", "bufferutil": "^4.0.7", "eventsource": "^2.0.2", "fetch-event-stream": "^0.1.5", "msw": "^2.2.1", "semiver": "^1.1.0", "textlinestream": "^1.1.1", "typescript": "^5.0.0", "ws": "^8.13.0"}, "devDependencies": {"@types/ws": "^8.5.10", "esbuild": "^0.21.0"}, "scripts": {"bundle": "vite build --ssr", "bundle:browser": "BROWSER_BUILD=true vite build", "generate_types": "tsc", "clean": "rm -rf dist", "build": "pnpm clean && pnpm bundle && pnpm bundle:browser && pnpm generate_types", "test": "pnpm test:client && pnpm test:client:node", "test:client": "vitest run -c vite.config.js", "test:client:node": "TEST_MODE=node vitest run -c vite.config.js", "preview:browser": "vite dev --mode=preview"}, "engines": {"node": ">=18.0.0"}, "main_changeset": true, "repository": {"type": "git", "url": "git+https://github.com/gradio-app/gradio.git", "directory": "client/js"}}