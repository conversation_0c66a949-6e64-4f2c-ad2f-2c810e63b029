import{s as n,t as a,f as i,c as p,p as s,S as l}from"./Index-XeBgsyZk.js";import{yaml as f}from"./yaml-DsCXHVTH.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import"./Check-CEkiXcyC.js";import"./Copy-CxQ9EyK2.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import"./prism-python-MMh3z1bK.js";import"./IconButton-C_HS7fTi.js";import"./Download-DVtk-Jv3.js";import"./DownloadLink-QIttOhoR.js";import"./file-url-DoxvUUVV.js";import"./IconButtonWrapper--EIOWuEM.js";import"./index-B1FJGuzG.js";import"./StreamingBar.svelte_svelte_type_style_lang-DyfAPhwM.js";import"./Clear-By3xiIwg.js";import"./Code-DGNrTu_I.js";import"./Block-CJdXVpa7.js";import"./BlockLabel-3KxTaaiM.js";import"./Empty-ZqppqzTN.js";import"./Example-DQvZIwBt.js";const m=/^---\s*$/m,T={defineNodes:[{name:"Frontmatter",block:!0},"FrontmatterMark"],props:[n({Frontmatter:[a.documentMeta,a.monospace],FrontmatterMark:a.processingInstruction}),i.add({Frontmatter:p,FrontmatterMark:()=>null})],wrap:s(t=>{const{parser:e}=l.define(f);return t.type.name==="Frontmatter"?{parser:e,overlay:[{from:t.from+4,to:t.to-4}]}:null}),parseBlock:[{name:"Frontmatter",before:"HorizontalRule",parse:(t,e)=>{let r;const o=new Array;if(t.lineStart===0&&m.test(e.text)){for(o.push(t.elt("FrontmatterMark",0,4));t.nextLine();)if(m.test(e.text)){r=t.lineStart+4;break}return r!==void 0&&(o.push(t.elt("FrontmatterMark",r-4,r)),t.addElement(t.elt("Frontmatter",0,r,o))),!0}return!1}}]};export{T as frontmatter};
//# sourceMappingURL=frontmatter-ByG-wJQO.js.map
