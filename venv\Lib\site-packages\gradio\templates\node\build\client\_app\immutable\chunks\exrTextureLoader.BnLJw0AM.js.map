{"version": 3, "file": "exrTextureLoader.BnLJw0AM.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/Loaders/EXR/exrLoader.interfaces.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/Loaders/EXR/exrLoader.core.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/Loaders/EXR/exrLoader.header.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/Loaders/EXR/exrLoader.compression.huf.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/Loaders/EXR/exrLoader.compression.rle.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/Loaders/EXR/exrLoader.compression.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/Loaders/EXR/exrLoader.configuration.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/Loaders/EXR/exrLoader.decoder.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/Loaders/exrTextureLoader.js"], "sourcesContent": ["export const INT32_SIZE = 4;\nexport const FLOAT32_SIZE = 4;\nexport const INT8_SIZE = 1;\nexport const INT16_SIZE = 2;\nexport const ULONG_SIZE = 8;\nexport const USHORT_RANGE = 1 << 16;\nexport const BITMAP_SIZE = USHORT_RANGE >> 3;\nexport const HUF_ENCBITS = 16;\nexport const HUF_DECBITS = 14;\nexport const HUF_ENCSIZE = (1 << HUF_ENCBITS) + 1;\nexport const HUF_DECSIZE = 1 << HUF_DECBITS;\nexport const HUF_DECMASK = HUF_DECSIZE - 1;\nexport const SHORT_ZEROCODE_RUN = 59;\nexport const LONG_ZEROCODE_RUN = 63;\nexport const SHORTEST_LONG_RUN = 2 + LONG_ZEROCODE_RUN - SHORT_ZEROCODE_RUN;\n//# sourceMappingURL=exrLoader.interfaces.js.map", "import { Clamp } from \"../../../../Maths/math.scalar.functions.js\";\nimport { FLOAT32_SIZE, INT16_SIZE, INT32_SIZE, INT8_SIZE, ULONG_SIZE } from \"./exrLoader.interfaces.js\";\n/**\n * Inspired by https://github.com/sciecode/three.js/blob/dev/examples/jsm/loaders/EXRLoader.js\n * Referred to the original Industrial Light & Magic OpenEXR implementation and the TinyEXR / Syoyo Fujita\n * implementation.\n */\n// /*\n// Copyright (c) 2014 - 2017, Syoyo Fujita\n// All rights reserved.\n// Redistribution and use in source and binary forms, with or without\n// modification, are permitted provided that the following conditions are met:\n//     * Redistributions of source code must retain the above copyright\n//       notice, this list of conditions and the following disclaimer.\n//     * Redistributions in binary form must reproduce the above copyright\n//       notice, this list of conditions and the following disclaimer in the\n//       documentation and/or other materials provided with the distribution.\n//     * Neither the name of the Syoyo Fujita nor the\n//       names of its contributors may be used to endorse or promote products\n//       derived from this software without specific prior written permission.\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n// DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY\n// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n// */\n// // TinyEXR contains some OpenEXR code, which is licensed under ------------\n// ///////////////////////////////////////////////////////////////////////////\n// //\n// // Copyright (c) 2002, Industrial Light & Magic, a division of Lucas\n// // Digital Ltd. LLC\n// //\n// // All rights reserved.\n// //\n// // Redistribution and use in source and binary forms, with or without\n// // modification, are permitted provided that the following conditions are\n// // met:\n// // *       Redistributions of source code must retain the above copyright\n// // notice, this list of conditions and the following disclaimer.\n// // *       Redistributions in binary form must reproduce the above\n// // copyright notice, this list of conditions and the following disclaimer\n// // in the documentation and/or other materials provided with the\n// // distribution.\n// // *       Neither the name of Industrial Light & Magic nor the names of\n// // its contributors may be used to endorse or promote products derived\n// // from this software without specific prior written permission.\n// //\n// // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n// // \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n// // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n// // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n// // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n// // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n// // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n// // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n// // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n// // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n// //\n// ///////////////////////////////////////////////////////////////////////////\n// // End of OpenEXR license -------------------------------------------------\nexport var CompressionCodes;\n(function (CompressionCodes) {\n    CompressionCodes[CompressionCodes[\"NO_COMPRESSION\"] = 0] = \"NO_COMPRESSION\";\n    CompressionCodes[CompressionCodes[\"RLE_COMPRESSION\"] = 1] = \"RLE_COMPRESSION\";\n    CompressionCodes[CompressionCodes[\"ZIPS_COMPRESSION\"] = 2] = \"ZIPS_COMPRESSION\";\n    CompressionCodes[CompressionCodes[\"ZIP_COMPRESSION\"] = 3] = \"ZIP_COMPRESSION\";\n    CompressionCodes[CompressionCodes[\"PIZ_COMPRESSION\"] = 4] = \"PIZ_COMPRESSION\";\n    CompressionCodes[CompressionCodes[\"PXR24_COMPRESSION\"] = 5] = \"PXR24_COMPRESSION\";\n})(CompressionCodes || (CompressionCodes = {}));\nvar LineOrders;\n(function (LineOrders) {\n    LineOrders[LineOrders[\"INCREASING_Y\"] = 0] = \"INCREASING_Y\";\n    LineOrders[LineOrders[\"DECREASING_Y\"] = 1] = \"DECREASING_Y\";\n})(LineOrders || (LineOrders = {}));\nconst _tables = _GenerateTables();\n// Fast Half Float Conversions, http://www.fox-toolkit.org/ftp/fasthalffloatconversion.pdf\nfunction _GenerateTables() {\n    // float32 to float16 helpers\n    const buffer = new ArrayBuffer(4);\n    const floatView = new Float32Array(buffer);\n    const uint32View = new Uint32Array(buffer);\n    const baseTable = new Uint32Array(512);\n    const shiftTable = new Uint32Array(512);\n    for (let i = 0; i < 256; ++i) {\n        const e = i - 127;\n        // very small number (0, -0)\n        if (e < -27) {\n            baseTable[i] = 0x0000;\n            baseTable[i | 0x100] = 0x8000;\n            shiftTable[i] = 24;\n            shiftTable[i | 0x100] = 24;\n            // small number (denorm)\n        }\n        else if (e < -14) {\n            baseTable[i] = 0x0400 >> (-e - 14);\n            baseTable[i | 0x100] = (0x0400 >> (-e - 14)) | 0x8000;\n            shiftTable[i] = -e - 1;\n            shiftTable[i | 0x100] = -e - 1;\n            // normal number\n        }\n        else if (e <= 15) {\n            baseTable[i] = (e + 15) << 10;\n            baseTable[i | 0x100] = ((e + 15) << 10) | 0x8000;\n            shiftTable[i] = 13;\n            shiftTable[i | 0x100] = 13;\n            // large number (Infinity, -Infinity)\n        }\n        else if (e < 128) {\n            baseTable[i] = 0x7c00;\n            baseTable[i | 0x100] = 0xfc00;\n            shiftTable[i] = 24;\n            shiftTable[i | 0x100] = 24;\n            // stay (NaN, Infinity, -Infinity)\n        }\n        else {\n            baseTable[i] = 0x7c00;\n            baseTable[i | 0x100] = 0xfc00;\n            shiftTable[i] = 13;\n            shiftTable[i | 0x100] = 13;\n        }\n    }\n    // float16 to float32 helpers\n    const mantissaTable = new Uint32Array(2048);\n    const exponentTable = new Uint32Array(64);\n    const offsetTable = new Uint32Array(64);\n    for (let i = 1; i < 1024; ++i) {\n        let m = i << 13; // zero pad mantissa bits\n        let e = 0; // zero exponent\n        // normalized\n        while ((m & 0x00800000) === 0) {\n            m <<= 1;\n            e -= 0x00800000; // decrement exponent\n        }\n        m &= ~0x00800000; // clear leading 1 bit\n        e += 0x38800000; // adjust bias\n        mantissaTable[i] = m | e;\n    }\n    for (let i = 1024; i < 2048; ++i) {\n        mantissaTable[i] = 0x38000000 + ((i - 1024) << 13);\n    }\n    for (let i = 1; i < 31; ++i) {\n        exponentTable[i] = i << 23;\n    }\n    exponentTable[31] = 0x47800000;\n    exponentTable[32] = 0x80000000;\n    for (let i = 33; i < 63; ++i) {\n        exponentTable[i] = 0x80000000 + ((i - 32) << 23);\n    }\n    exponentTable[63] = 0xc7800000;\n    for (let i = 1; i < 64; ++i) {\n        if (i !== 32) {\n            offsetTable[i] = 1024;\n        }\n    }\n    return {\n        floatView: floatView,\n        uint32View: uint32View,\n        baseTable: baseTable,\n        shiftTable: shiftTable,\n        mantissaTable: mantissaTable,\n        exponentTable: exponentTable,\n        offsetTable: offsetTable,\n    };\n}\n/**\n * Parse a null terminated string from the buffer\n * @param buffer buffer to read from\n * @param offset current offset in the buffer\n * @returns a string\n */\nexport function ParseNullTerminatedString(buffer, offset) {\n    const uintBuffer = new Uint8Array(buffer);\n    let endOffset = 0;\n    while (uintBuffer[offset.value + endOffset] != 0) {\n        endOffset += 1;\n    }\n    const stringValue = new TextDecoder().decode(uintBuffer.slice(offset.value, offset.value + endOffset));\n    offset.value = offset.value + endOffset + 1;\n    return stringValue;\n}\n/**\n * Parse an int32 from the buffer\n * @param dataView dataview on the data\n * @param offset current offset in the data view\n * @returns an int32\n */\nexport function ParseInt32(dataView, offset) {\n    const value = dataView.getInt32(offset.value, true);\n    offset.value += INT32_SIZE;\n    return value;\n}\n/**\n * Parse an uint32 from the buffer\n * @param dataView data view to read from\n * @param offset offset in the data view\n * @returns an uint32\n */\nexport function ParseUint32(dataView, offset) {\n    const value = dataView.getUint32(offset.value, true);\n    offset.value += INT32_SIZE;\n    return value;\n}\n/**\n * Parse an uint8 from the buffer\n * @param dataView dataview on the data\n * @param offset current offset in the data view\n * @returns an uint8\n */\nexport function ParseUint8(dataView, offset) {\n    const value = dataView.getUint8(offset.value);\n    offset.value += INT8_SIZE;\n    return value;\n}\n/**\n * Parse an uint16 from the buffer\n * @param dataView dataview on the data\n * @param offset current offset in the data view\n * @returns an uint16\n */\nexport function ParseUint16(dataView, offset) {\n    const value = dataView.getUint16(offset.value, true);\n    offset.value += INT16_SIZE;\n    return value;\n}\n/**\n * Parse an uint8 from an array buffer\n * @param array array buffer\n * @param offset current offset in the data view\n * @returns an uint16\n */\nexport function ParseUint8Array(array, offset) {\n    const value = array[offset.value];\n    offset.value += INT8_SIZE;\n    return value;\n}\n/**\n * Parse an int64 from the buffer\n * @param dataView dataview on the data\n * @param offset current offset in the data view\n * @returns an int64\n */\nexport function ParseInt64(dataView, offset) {\n    let int;\n    if (\"getBigInt64\" in DataView.prototype) {\n        int = Number(dataView.getBigInt64(offset.value, true));\n    }\n    else {\n        int = dataView.getUint32(offset.value + 4, true) + Number(dataView.getUint32(offset.value, true) << 32);\n    }\n    offset.value += ULONG_SIZE;\n    return int;\n}\n/**\n * Parse a float32 from the buffer\n * @param dataView dataview on the data\n * @param offset current offset in the data view\n * @returns a float32\n */\nexport function ParseFloat32(dataView, offset) {\n    const value = dataView.getFloat32(offset.value, true);\n    offset.value += FLOAT32_SIZE;\n    return value;\n}\n/**\n * Parse a float16 from the buffer\n * @param dataView dataview on the data\n * @param offset current offset in the data view\n * @returns a float16\n */\nexport function ParseFloat16(dataView, offset) {\n    return DecodeFloat16(ParseUint16(dataView, offset));\n}\nfunction DecodeFloat16(binary) {\n    const exponent = (binary & 0x7c00) >> 10;\n    const fraction = binary & 0x03ff;\n    return ((binary >> 15 ? -1 : 1) *\n        (exponent ? (exponent === 0x1f ? (fraction ? NaN : Infinity) : Math.pow(2, exponent - 15) * (1 + fraction / 0x400)) : 6.103515625e-5 * (fraction / 0x400)));\n}\nfunction ToHalfFloat(value) {\n    if (Math.abs(value) > 65504) {\n        throw new Error(\"Value out of range.Consider using float instead of half-float.\");\n    }\n    value = Clamp(value, -65504, 65504);\n    _tables.floatView[0] = value;\n    const f = _tables.uint32View[0];\n    const e = (f >> 23) & 0x1ff;\n    return _tables.baseTable[e] + ((f & 0x007fffff) >> _tables.shiftTable[e]);\n}\n/**\n * Decode a float32 from the buffer\n * @param dataView dataview on the data\n * @param offset current offset in the data view\n * @returns a float32\n */\nexport function DecodeFloat32(dataView, offset) {\n    return ToHalfFloat(ParseFloat32(dataView, offset));\n}\nfunction ParseFixedLengthString(buffer, offset, size) {\n    const stringValue = new TextDecoder().decode(new Uint8Array(buffer).slice(offset.value, offset.value + size));\n    offset.value = offset.value + size;\n    return stringValue;\n}\nfunction ParseRational(dataView, offset) {\n    const x = ParseInt32(dataView, offset);\n    const y = ParseUint32(dataView, offset);\n    return [x, y];\n}\nfunction ParseTimecode(dataView, offset) {\n    const x = ParseUint32(dataView, offset);\n    const y = ParseUint32(dataView, offset);\n    return [x, y];\n}\nfunction ParseV2f(dataView, offset) {\n    const x = ParseFloat32(dataView, offset);\n    const y = ParseFloat32(dataView, offset);\n    return [x, y];\n}\nfunction ParseV3f(dataView, offset) {\n    const x = ParseFloat32(dataView, offset);\n    const y = ParseFloat32(dataView, offset);\n    const z = ParseFloat32(dataView, offset);\n    return [x, y, z];\n}\nfunction ParseChlist(dataView, offset, size) {\n    const startOffset = offset.value;\n    const channels = [];\n    while (offset.value < startOffset + size - 1) {\n        const name = ParseNullTerminatedString(dataView.buffer, offset);\n        const pixelType = ParseInt32(dataView, offset);\n        const pLinear = ParseUint8(dataView, offset);\n        offset.value += 3; // reserved, three chars\n        const xSampling = ParseInt32(dataView, offset);\n        const ySampling = ParseInt32(dataView, offset);\n        channels.push({\n            name: name,\n            pixelType: pixelType,\n            pLinear: pLinear,\n            xSampling: xSampling,\n            ySampling: ySampling,\n        });\n    }\n    offset.value += 1;\n    return channels;\n}\nfunction ParseChromaticities(dataView, offset) {\n    const redX = ParseFloat32(dataView, offset);\n    const redY = ParseFloat32(dataView, offset);\n    const greenX = ParseFloat32(dataView, offset);\n    const greenY = ParseFloat32(dataView, offset);\n    const blueX = ParseFloat32(dataView, offset);\n    const blueY = ParseFloat32(dataView, offset);\n    const whiteX = ParseFloat32(dataView, offset);\n    const whiteY = ParseFloat32(dataView, offset);\n    return { redX: redX, redY: redY, greenX: greenX, greenY: greenY, blueX: blueX, blueY: blueY, whiteX: whiteX, whiteY: whiteY };\n}\nfunction ParseCompression(dataView, offset) {\n    return ParseUint8(dataView, offset);\n}\nfunction ParseBox2i(dataView, offset) {\n    const xMin = ParseInt32(dataView, offset);\n    const yMin = ParseInt32(dataView, offset);\n    const xMax = ParseInt32(dataView, offset);\n    const yMax = ParseInt32(dataView, offset);\n    return { xMin: xMin, yMin: yMin, xMax: xMax, yMax: yMax };\n}\nfunction ParseLineOrder(dataView, offset) {\n    const lineOrder = ParseUint8(dataView, offset);\n    return LineOrders[lineOrder];\n}\n/**\n * Parse a value from the data view\n * @param dataView defines the data view to read from\n * @param offset defines the current offset in the data view\n * @param type defines the type of the value to read\n * @param size defines the size of the value to read\n * @returns the parsed value\n */\nexport function ParseValue(dataView, offset, type, size) {\n    switch (type) {\n        case \"string\":\n        case \"stringvector\":\n        case \"iccProfile\":\n            return ParseFixedLengthString(dataView.buffer, offset, size);\n        case \"chlist\":\n            return ParseChlist(dataView, offset, size);\n        case \"chromaticities\":\n            return ParseChromaticities(dataView, offset);\n        case \"compression\":\n            return ParseCompression(dataView, offset);\n        case \"box2i\":\n            return ParseBox2i(dataView, offset);\n        case \"lineOrder\":\n            return ParseLineOrder(dataView, offset);\n        case \"float\":\n            return ParseFloat32(dataView, offset);\n        case \"v2f\":\n            return ParseV2f(dataView, offset);\n        case \"v3f\":\n            return ParseV3f(dataView, offset);\n        case \"int\":\n            return ParseInt32(dataView, offset);\n        case \"rational\":\n            return ParseRational(dataView, offset);\n        case \"timecode\":\n            return ParseTimecode(dataView, offset);\n        case \"preview\":\n            offset.value += size;\n            return \"skipped\";\n        default:\n            offset.value += size;\n            return undefined;\n    }\n}\n/**\n * Revert the endianness of the data\n * @param source defines the source\n */\nexport function Predictor(source) {\n    for (let t = 1; t < source.length; t++) {\n        const d = source[t - 1] + source[t] - 128;\n        source[t] = d;\n    }\n}\n/**\n * Interleave pixels\n * @param source defines the data source\n * @param out defines the output\n */\nexport function InterleaveScalar(source, out) {\n    let t1 = 0;\n    let t2 = Math.floor((source.length + 1) / 2);\n    let s = 0;\n    const stop = source.length - 1;\n    // eslint-disable-next-line no-constant-condition\n    while (true) {\n        if (s > stop) {\n            break;\n        }\n        out[s++] = source[t1++];\n        if (s > stop) {\n            break;\n        }\n        out[s++] = source[t2++];\n    }\n}\n//# sourceMappingURL=exrLoader.core.js.map", "/* eslint-disable @typescript-eslint/naming-convention */\nimport { Logger } from \"../../../../Misc/logger.js\";\nimport { ParseNullTerminatedString, ParseUint32, ParseValue } from \"./exrLoader.core.js\";\n/**\n * Inspired by https://github.com/sciecode/three.js/blob/dev/examples/jsm/loaders/EXRLoader.js\n * Referred to the original Industrial Light & Magic OpenEXR implementation and the TinyEXR / Syoyo Fujita\n * implementation.\n */\n// /*\n// Copyright (c) 2014 - 2017, Syoyo Fujita\n// All rights reserved.\n// Redistribution and use in source and binary forms, with or without\n// modification, are permitted provided that the following conditions are met:\n//     * Redistributions of source code must retain the above copyright\n//       notice, this list of conditions and the following disclaimer.\n//     * Redistributions in binary form must reproduce the above copyright\n//       notice, this list of conditions and the following disclaimer in the\n//       documentation and/or other materials provided with the distribution.\n//     * Neither the name of the Syoyo Fujita nor the\n//       names of its contributors may be used to endorse or promote products\n//       derived from this software without specific prior written permission.\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n// DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY\n// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n// */\n// // TinyEXR contains some OpenEXR code, which is licensed under ------------\n// ///////////////////////////////////////////////////////////////////////////\n// //\n// // Copyright (c) 2002, Industrial Light & Magic, a division of Lucas\n// // Digital Ltd. LLC\n// //\n// // All rights reserved.\n// //\n// // Redistribution and use in source and binary forms, with or without\n// // modification, are permitted provided that the following conditions are\n// // met:\n// // *       Redistributions of source code must retain the above copyright\n// // notice, this list of conditions and the following disclaimer.\n// // *       Redistributions in binary form must reproduce the above\n// // copyright notice, this list of conditions and the following disclaimer\n// // in the documentation and/or other materials provided with the\n// // distribution.\n// // *       Neither the name of Industrial Light & Magic nor the names of\n// // its contributors may be used to endorse or promote products derived\n// // from this software without specific prior written permission.\n// //\n// // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n// // \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n// // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n// // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n// // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n// // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n// // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n// // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n// // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n// // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n// //\n// ///////////////////////////////////////////////////////////////////////////\n// // End of OpenEXR license -------------------------------------------------\nconst EXR_MAGIC = 20000630;\n/**\n * Gets the EXR header\n * @param dataView defines the data view to read from\n * @param offset defines the offset to start reading from\n * @returns the header\n */\nexport function GetExrHeader(dataView, offset) {\n    if (dataView.getUint32(0, true) != EXR_MAGIC) {\n        throw new Error(\"Incorrect OpenEXR format\");\n    }\n    const version = dataView.getUint8(4);\n    const specData = dataView.getUint8(5); // fullMask\n    const spec = {\n        singleTile: !!(specData & 2),\n        longName: !!(specData & 4),\n        deepFormat: !!(specData & 8),\n        multiPart: !!(specData & 16),\n    };\n    offset.value = 8;\n    const headerData = {};\n    let keepReading = true;\n    while (keepReading) {\n        const attributeName = ParseNullTerminatedString(dataView.buffer, offset);\n        if (!attributeName) {\n            keepReading = false;\n        }\n        else {\n            const attributeType = ParseNullTerminatedString(dataView.buffer, offset);\n            const attributeSize = ParseUint32(dataView, offset);\n            const attributeValue = ParseValue(dataView, offset, attributeType, attributeSize);\n            if (attributeValue === undefined) {\n                Logger.Warn(`Unknown header attribute type ${attributeType}'.`);\n            }\n            else {\n                headerData[attributeName] = attributeValue;\n            }\n        }\n    }\n    if ((specData & ~0x04) != 0) {\n        throw new Error(\"Unsupported file format\");\n    }\n    return { version: version, spec: spec, ...headerData };\n}\n//# sourceMappingURL=exrLoader.header.js.map", "import { ParseUint32, ParseUint8Array } from \"./exrLoader.core.js\";\nimport { HUF_DECBITS, HUF_DECMASK, HUF_DECSIZE, HUF_ENCSIZE, LONG_ZEROCODE_RUN, SHORT_ZEROCODE_RUN, SHORTEST_LONG_RUN, USHORT_RANGE } from \"./exrLoader.interfaces.js\";\n/**\n * Inspired by https://github.com/sciecode/three.js/blob/dev/examples/jsm/loaders/EXRLoader.js\n * Referred to the original Industrial Light & Magic OpenEXR implementation and the TinyEXR / Syoyo Fujita\n * implementation.\n */\n// /*\n// Copyright (c) 2014 - 2017, Syoyo <PERSON>ta\n// All rights reserved.\n// Redistribution and use in source and binary forms, with or without\n// modification, are permitted provided that the following conditions are met:\n//     * Redistributions of source code must retain the above copyright\n//       notice, this list of conditions and the following disclaimer.\n//     * Redistributions in binary form must reproduce the above copyright\n//       notice, this list of conditions and the following disclaimer in the\n//       documentation and/or other materials provided with the distribution.\n//     * Neither the name of the Syoyo Fujita nor the\n//       names of its contributors may be used to endorse or promote products\n//       derived from this software without specific prior written permission.\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n// DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY\n// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n// */\n// // TinyEXR contains some OpenEXR code, which is licensed under ------------\n// ///////////////////////////////////////////////////////////////////////////\n// //\n// // Copyright (c) 2002, Industrial Light & Magic, a division of Lucas\n// // Digital Ltd. LLC\n// //\n// // All rights reserved.\n// //\n// // Redistribution and use in source and binary forms, with or without\n// // modification, are permitted provided that the following conditions are\n// // met:\n// // *       Redistributions of source code must retain the above copyright\n// // notice, this list of conditions and the following disclaimer.\n// // *       Redistributions in binary form must reproduce the above\n// // copyright notice, this list of conditions and the following disclaimer\n// // in the documentation and/or other materials provided with the\n// // distribution.\n// // *       Neither the name of Industrial Light & Magic nor the names of\n// // its contributors may be used to endorse or promote products derived\n// // from this software without specific prior written permission.\n// //\n// // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n// // \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n// // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n// // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n// // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n// // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n// // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n// // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n// // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n// // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n// //\n// ///////////////////////////////////////////////////////////////////////////\n// // End of OpenEXR license -------------------------------------------------\nconst NBITS = 16;\nconst A_OFFSET = 1 << (NBITS - 1);\nconst MOD_MASK = (1 << NBITS) - 1;\n/** @internal */\nexport function ReverseLutFromBitmap(bitmap, lut) {\n    let k = 0;\n    for (let i = 0; i < USHORT_RANGE; ++i) {\n        if (i == 0 || bitmap[i >> 3] & (1 << (i & 7))) {\n            lut[k++] = i;\n        }\n    }\n    const n = k - 1;\n    while (k < USHORT_RANGE)\n        lut[k++] = 0;\n    return n;\n}\nfunction HufClearDecTable(hdec) {\n    for (let i = 0; i < HUF_DECSIZE; i++) {\n        hdec[i] = {};\n        hdec[i].len = 0;\n        hdec[i].lit = 0;\n        hdec[i].p = null;\n    }\n}\nfunction GetBits(nBits, c, lc, array, offset) {\n    while (lc < nBits) {\n        c = (c << 8) | ParseUint8Array(array, offset);\n        lc += 8;\n    }\n    lc -= nBits;\n    return {\n        l: (c >> lc) & ((1 << nBits) - 1),\n        c,\n        lc,\n    };\n}\nfunction GetChar(c, lc, array, offset) {\n    c = (c << 8) | ParseUint8Array(array, offset);\n    lc += 8;\n    return {\n        c,\n        lc,\n    };\n}\nfunction GetCode(po, rlc, c, lc, array, offset, outBuffer, outBufferOffset, outBufferEndOffset) {\n    if (po == rlc) {\n        if (lc < 8) {\n            const gc = GetChar(c, lc, array, offset);\n            c = gc.c;\n            lc = gc.lc;\n        }\n        lc -= 8;\n        let cs = c >> lc;\n        cs = new Uint8Array([cs])[0];\n        if (outBufferOffset.value + cs > outBufferEndOffset) {\n            return null;\n        }\n        const s = outBuffer[outBufferOffset.value - 1];\n        while (cs-- > 0) {\n            outBuffer[outBufferOffset.value++] = s;\n        }\n    }\n    else if (outBufferOffset.value < outBufferEndOffset) {\n        outBuffer[outBufferOffset.value++] = po;\n    }\n    else {\n        return null;\n    }\n    return { c, lc };\n}\nconst HufTableBuffer = new Array(59);\nfunction HufCanonicalCodeTable(hcode) {\n    for (let i = 0; i <= 58; ++i)\n        HufTableBuffer[i] = 0;\n    for (let i = 0; i < HUF_ENCSIZE; ++i)\n        HufTableBuffer[hcode[i]] += 1;\n    let c = 0;\n    for (let i = 58; i > 0; --i) {\n        const nc = (c + HufTableBuffer[i]) >> 1;\n        HufTableBuffer[i] = c;\n        c = nc;\n    }\n    for (let i = 0; i < HUF_ENCSIZE; ++i) {\n        const l = hcode[i];\n        if (l > 0)\n            hcode[i] = l | (HufTableBuffer[l]++ << 6);\n    }\n}\nfunction HufUnpackEncTable(array, offset, ni, im, iM, hcode) {\n    const p = offset;\n    let c = 0;\n    let lc = 0;\n    for (; im <= iM; im++) {\n        if (p.value - offset.value > ni) {\n            return;\n        }\n        let gb = GetBits(6, c, lc, array, p);\n        const l = gb.l;\n        c = gb.c;\n        lc = gb.lc;\n        hcode[im] = l;\n        if (l == LONG_ZEROCODE_RUN) {\n            if (p.value - offset.value > ni) {\n                throw new Error(\"Error in HufUnpackEncTable\");\n            }\n            gb = GetBits(8, c, lc, array, p);\n            let zerun = gb.l + SHORTEST_LONG_RUN;\n            c = gb.c;\n            lc = gb.lc;\n            if (im + zerun > iM + 1) {\n                throw new Error(\"Error in HufUnpackEncTable\");\n            }\n            while (zerun--)\n                hcode[im++] = 0;\n            im--;\n        }\n        else if (l >= SHORT_ZEROCODE_RUN) {\n            let zerun = l - SHORT_ZEROCODE_RUN + 2;\n            if (im + zerun > iM + 1) {\n                throw new Error(\"Error in HufUnpackEncTable\");\n            }\n            while (zerun--)\n                hcode[im++] = 0;\n            im--;\n        }\n    }\n    HufCanonicalCodeTable(hcode);\n}\nfunction HufLength(code) {\n    return code & 63;\n}\nfunction HufCode(code) {\n    return code >> 6;\n}\nfunction HufBuildDecTable(hcode, im, iM, hdecod) {\n    for (; im <= iM; im++) {\n        const c = HufCode(hcode[im]);\n        const l = HufLength(hcode[im]);\n        if (c >> l) {\n            throw new Error(\"Invalid table entry\");\n        }\n        if (l > HUF_DECBITS) {\n            const pl = hdecod[c >> (l - HUF_DECBITS)];\n            if (pl.len) {\n                throw new Error(\"Invalid table entry\");\n            }\n            pl.lit++;\n            if (pl.p) {\n                const p = pl.p;\n                pl.p = new Array(pl.lit);\n                for (let i = 0; i < pl.lit - 1; ++i) {\n                    pl.p[i] = p[i];\n                }\n            }\n            else {\n                pl.p = new Array(1);\n            }\n            pl.p[pl.lit - 1] = im;\n        }\n        else if (l) {\n            let plOffset = 0;\n            for (let i = 1 << (HUF_DECBITS - l); i > 0; i--) {\n                const pl = hdecod[(c << (HUF_DECBITS - l)) + plOffset];\n                if (pl.len || pl.p) {\n                    throw new Error(\"Invalid table entry\");\n                }\n                pl.len = l;\n                pl.lit = im;\n                plOffset++;\n            }\n        }\n    }\n    return true;\n}\nfunction HufDecode(encodingTable, decodingTable, array, offset, ni, rlc, no, outBuffer, outOffset) {\n    let c = 0;\n    let lc = 0;\n    const outBufferEndOffset = no;\n    const inOffsetEnd = Math.trunc(offset.value + (ni + 7) / 8);\n    while (offset.value < inOffsetEnd) {\n        let gc = GetChar(c, lc, array, offset);\n        c = gc.c;\n        lc = gc.lc;\n        while (lc >= HUF_DECBITS) {\n            const index = (c >> (lc - HUF_DECBITS)) & HUF_DECMASK;\n            const pl = decodingTable[index];\n            if (pl.len) {\n                lc -= pl.len;\n                const gCode = GetCode(pl.lit, rlc, c, lc, array, offset, outBuffer, outOffset, outBufferEndOffset);\n                if (gCode) {\n                    c = gCode.c;\n                    lc = gCode.lc;\n                }\n            }\n            else {\n                if (!pl.p) {\n                    throw new Error(\"hufDecode issues\");\n                }\n                let j;\n                for (j = 0; j < pl.lit; j++) {\n                    const l = HufLength(encodingTable[pl.p[j]]);\n                    while (lc < l && offset.value < inOffsetEnd) {\n                        gc = GetChar(c, lc, array, offset);\n                        c = gc.c;\n                        lc = gc.lc;\n                    }\n                    if (lc >= l) {\n                        if (HufCode(encodingTable[pl.p[j]]) == ((c >> (lc - l)) & ((1 << l) - 1))) {\n                            lc -= l;\n                            const gCode = GetCode(pl.p[j], rlc, c, lc, array, offset, outBuffer, outOffset, outBufferEndOffset);\n                            if (gCode) {\n                                c = gCode.c;\n                                lc = gCode.lc;\n                            }\n                            break;\n                        }\n                    }\n                }\n                if (j == pl.lit) {\n                    throw new Error(\"HufDecode issues\");\n                }\n            }\n        }\n    }\n    const i = (8 - ni) & 7;\n    c >>= i;\n    lc -= i;\n    while (lc > 0) {\n        const pl = decodingTable[(c << (HUF_DECBITS - lc)) & HUF_DECMASK];\n        if (pl.len) {\n            lc -= pl.len;\n            const gCode = GetCode(pl.lit, rlc, c, lc, array, offset, outBuffer, outOffset, outBufferEndOffset);\n            if (gCode) {\n                c = gCode.c;\n                lc = gCode.lc;\n            }\n        }\n        else {\n            throw new Error(\"HufDecode issues\");\n        }\n    }\n    return true;\n}\n/** @internal */\nexport function HufUncompress(array, dataView, offset, nCompressed, outBuffer, nRaw) {\n    const outOffset = { value: 0 };\n    const initialInOffset = offset.value;\n    const im = ParseUint32(dataView, offset);\n    const iM = ParseUint32(dataView, offset);\n    offset.value += 4;\n    const nBits = ParseUint32(dataView, offset);\n    offset.value += 4;\n    if (im < 0 || im >= HUF_ENCSIZE || iM < 0 || iM >= HUF_ENCSIZE) {\n        throw new Error(\"Wrong HUF_ENCSIZE\");\n    }\n    const freq = new Array(HUF_ENCSIZE);\n    const hdec = new Array(HUF_DECSIZE);\n    HufClearDecTable(hdec);\n    const ni = nCompressed - (offset.value - initialInOffset);\n    HufUnpackEncTable(array, offset, ni, im, iM, freq);\n    if (nBits > 8 * (nCompressed - (offset.value - initialInOffset))) {\n        throw new Error(\"Wrong hufUncompress\");\n    }\n    HufBuildDecTable(freq, im, iM, hdec);\n    HufDecode(freq, hdec, array, offset, nBits, iM, nRaw, outBuffer, outOffset);\n}\nfunction UInt16(value) {\n    return value & 0xffff;\n}\nfunction Int16(value) {\n    const ref = UInt16(value);\n    return ref > 0x7fff ? ref - 0x10000 : ref;\n}\nfunction Wdec14(l, h) {\n    const ls = Int16(l);\n    const hs = Int16(h);\n    const hi = hs;\n    const ai = ls + (hi & 1) + (hi >> 1);\n    const as = ai;\n    const bs = ai - hi;\n    return { a: as, b: bs };\n}\nfunction Wdec16(l, h) {\n    const m = UInt16(l);\n    const d = UInt16(h);\n    const bb = (m - (d >> 1)) & MOD_MASK;\n    const aa = (d + bb - A_OFFSET) & MOD_MASK;\n    return { a: aa, b: bb };\n}\n/** @internal */\nexport function Wav2Decode(buffer, j, nx, ox, ny, oy, mx) {\n    const w14 = mx < 1 << 14;\n    const n = nx > ny ? ny : nx;\n    let p = 1;\n    let p2;\n    let py;\n    while (p <= n)\n        p <<= 1;\n    p >>= 1;\n    p2 = p;\n    p >>= 1;\n    while (p >= 1) {\n        py = 0;\n        const ey = py + oy * (ny - p2);\n        const oy1 = oy * p;\n        const oy2 = oy * p2;\n        const ox1 = ox * p;\n        const ox2 = ox * p2;\n        let i00, i01, i10, i11;\n        for (; py <= ey; py += oy2) {\n            let px = py;\n            const ex = py + ox * (nx - p2);\n            for (; px <= ex; px += ox2) {\n                const p01 = px + ox1;\n                const p10 = px + oy1;\n                const p11 = p10 + ox1;\n                if (w14) {\n                    let result = Wdec14(buffer[px + j], buffer[p10 + j]);\n                    i00 = result.a;\n                    i10 = result.b;\n                    result = Wdec14(buffer[p01 + j], buffer[p11 + j]);\n                    i01 = result.a;\n                    i11 = result.b;\n                    result = Wdec14(i00, i01);\n                    buffer[px + j] = result.a;\n                    buffer[p01 + j] = result.b;\n                    result = Wdec14(i10, i11);\n                    buffer[p10 + j] = result.a;\n                    buffer[p11 + j] = result.b;\n                }\n                else {\n                    let result = Wdec16(buffer[px + j], buffer[p10 + j]);\n                    i00 = result.a;\n                    i10 = result.b;\n                    result = Wdec16(buffer[p01 + j], buffer[p11 + j]);\n                    i01 = result.a;\n                    i11 = result.b;\n                    result = Wdec16(i00, i01);\n                    buffer[px + j] = result.a;\n                    buffer[p01 + j] = result.b;\n                    result = Wdec16(i10, i11);\n                    buffer[p10 + j] = result.a;\n                    buffer[p11 + j] = result.b;\n                }\n            }\n            if (nx & p) {\n                const p10 = px + oy1;\n                let result;\n                if (w14) {\n                    result = Wdec14(buffer[px + j], buffer[p10 + j]);\n                }\n                else {\n                    result = Wdec16(buffer[px + j], buffer[p10 + j]);\n                }\n                i00 = result.a;\n                buffer[p10 + j] = result.b;\n                buffer[px + j] = i00;\n            }\n        }\n        if (ny & p) {\n            let px = py;\n            const ex = py + ox * (nx - p2);\n            for (; px <= ex; px += ox2) {\n                const p01 = px + ox1;\n                let result;\n                if (w14) {\n                    result = Wdec14(buffer[px + j], buffer[p01 + j]);\n                }\n                else {\n                    result = Wdec16(buffer[px + j], buffer[p01 + j]);\n                }\n                i00 = result.a;\n                buffer[p01 + j] = result.b;\n                buffer[px + j] = i00;\n            }\n        }\n        p2 = p;\n        p >>= 1;\n    }\n    return py;\n}\n/** @internal */\nexport function ApplyLut(lut, data, nData) {\n    for (let i = 0; i < nData; ++i) {\n        data[i] = lut[data[i]];\n    }\n}\n//# sourceMappingURL=exrLoader.compression.huf.js.map", "/**\n * Inspired by https://github.com/sciecode/three.js/blob/dev/examples/jsm/loaders/EXRLoader.js\n * Referred to the original Industrial Light & Magic OpenEXR implementation and the TinyEXR / Syoyo <PERSON>\n * implementation.\n */\n// /*\n// Copyright (c) 2014 - 2017, S<PERSON><PERSON>\n// All rights reserved.\n// Redistribution and use in source and binary forms, with or without\n// modification, are permitted provided that the following conditions are met:\n//     * Redistributions of source code must retain the above copyright\n//       notice, this list of conditions and the following disclaimer.\n//     * Redistributions in binary form must reproduce the above copyright\n//       notice, this list of conditions and the following disclaimer in the\n//       documentation and/or other materials provided with the distribution.\n//     * Neither the name of the Syoyo <PERSON> nor the\n//       names of its contributors may be used to endorse or promote products\n//       derived from this software without specific prior written permission.\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n// DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY\n// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n// */\n// // TinyEXR contains some OpenEXR code, which is licensed under ------------\n// ///////////////////////////////////////////////////////////////////////////\n// //\n// // Copyright (c) 2002, Industrial Light & Magic, a division of Lucas\n// // Digital Ltd. LLC\n// //\n// // All rights reserved.\n// //\n// // Redistribution and use in source and binary forms, with or without\n// // modification, are permitted provided that the following conditions are\n// // met:\n// // *       Redistributions of source code must retain the above copyright\n// // notice, this list of conditions and the following disclaimer.\n// // *       Redistributions in binary form must reproduce the above\n// // copyright notice, this list of conditions and the following disclaimer\n// // in the documentation and/or other materials provided with the\n// // distribution.\n// // *       Neither the name of Industrial Light & Magic nor the names of\n// // its contributors may be used to endorse or promote products derived\n// // from this software without specific prior written permission.\n// //\n// // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n// // \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n// // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n// // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n// // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n// // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n// // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n// // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n// // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n// // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n// //\n// ///////////////////////////////////////////////////////////////////////////\n// // End of OpenEXR license -------------------------------------------------\n/** @internal */\nexport function DecodeRunLength(source) {\n    let size = source.byteLength;\n    const out = new Array();\n    let p = 0;\n    const reader = new DataView(source);\n    while (size > 0) {\n        const l = reader.getInt8(p++);\n        if (l < 0) {\n            const count = -l;\n            size -= count + 1;\n            for (let i = 0; i < count; i++) {\n                out.push(reader.getUint8(p++));\n            }\n        }\n        else {\n            const count = l;\n            size -= 2;\n            const value = reader.getUint8(p++);\n            for (let i = 0; i < count + 1; i++) {\n                out.push(value);\n            }\n        }\n    }\n    return out;\n}\n//# sourceMappingURL=exrLoader.compression.rle.js.map", "import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ncomp<PERSON>, ReverseLutFromBitmap, Wav2Decode } from \"./exrLoader.compression.huf.js\";\nimport { DecodeRunLength } from \"./exrLoader.compression.rle.js\";\nimport { InterleaveScalar, ParseUint16, ParseUint32, ParseUint8, Predictor } from \"./exrLoader.core.js\";\nimport { BITMAP_SIZE, INT16_SIZE, USHORT_RANGE } from \"./exrLoader.interfaces.js\";\n/**\n * No compression\n * @param decoder defines the decoder to use\n * @returns a decompressed data view\n */\nexport function UncompressRAW(decoder) {\n    return new DataView(decoder.array.buffer, decoder.offset.value, decoder.size);\n}\n/**\n * RLE compression\n * @param decoder defines the decoder to use\n * @returns a decompressed data view\n */\nexport function UncompressRLE(decoder) {\n    const compressed = decoder.viewer.buffer.slice(decoder.offset.value, decoder.offset.value + decoder.size);\n    const rawBuffer = new Uint8Array(DecodeRunLength(compressed));\n    const tmpBuffer = new Uint8Array(rawBuffer.length);\n    Predictor(rawBuffer);\n    InterleaveScalar(rawBuffer, tmpBuffer);\n    return new DataView(tmpBuffer.buffer);\n}\n/**\n * Zip compression\n * @param decoder defines the decoder to use\n * @returns a decompressed data view\n */\nexport function UncompressZIP(decoder) {\n    const compressed = decoder.array.slice(decoder.offset.value, decoder.offset.value + decoder.size);\n    const rawBuffer = fflate.unzlibSync(compressed);\n    const tmpBuffer = new Uint8Array(rawBuffer.length);\n    Predictor(rawBuffer);\n    InterleaveScalar(rawBuffer, tmpBuffer);\n    return new DataView(tmpBuffer.buffer);\n}\n/**\n * PXR compression\n * @param decoder defines the decoder to use\n * @returns a decompressed data view\n */\nexport function UncompressPXR(decoder) {\n    const compressed = decoder.array.slice(decoder.offset.value, decoder.offset.value + decoder.size);\n    const rawBuffer = fflate.unzlibSync(compressed);\n    const sz = decoder.lines * decoder.channels * decoder.width;\n    const tmpBuffer = decoder.type == 1 ? new Uint16Array(sz) : new Uint32Array(sz);\n    let tmpBufferEnd = 0;\n    let writePtr = 0;\n    const ptr = new Array(4);\n    for (let y = 0; y < decoder.lines; y++) {\n        for (let c = 0; c < decoder.channels; c++) {\n            let pixel = 0;\n            switch (decoder.type) {\n                case 1:\n                    ptr[0] = tmpBufferEnd;\n                    ptr[1] = ptr[0] + decoder.width;\n                    tmpBufferEnd = ptr[1] + decoder.width;\n                    for (let j = 0; j < decoder.width; ++j) {\n                        const diff = (rawBuffer[ptr[0]++] << 8) | rawBuffer[ptr[1]++];\n                        pixel += diff;\n                        tmpBuffer[writePtr] = pixel;\n                        writePtr++;\n                    }\n                    break;\n                case 2:\n                    ptr[0] = tmpBufferEnd;\n                    ptr[1] = ptr[0] + decoder.width;\n                    ptr[2] = ptr[1] + decoder.width;\n                    tmpBufferEnd = ptr[2] + decoder.width;\n                    for (let j = 0; j < decoder.width; ++j) {\n                        const diff = (rawBuffer[ptr[0]++] << 24) | (rawBuffer[ptr[1]++] << 16) | (rawBuffer[ptr[2]++] << 8);\n                        pixel += diff;\n                        tmpBuffer[writePtr] = pixel;\n                        writePtr++;\n                    }\n                    break;\n            }\n        }\n    }\n    return new DataView(tmpBuffer.buffer);\n}\n/**\n * PIZ compression\n * @param decoder defines the decoder to use\n * @returns a decompressed data view\n */\nexport function UncompressPIZ(decoder) {\n    const inDataView = decoder.viewer;\n    const inOffset = { value: decoder.offset.value };\n    const outBuffer = new Uint16Array(decoder.width * decoder.scanlineBlockSize * (decoder.channels * decoder.type));\n    const bitmap = new Uint8Array(BITMAP_SIZE);\n    // Setup channel info\n    let outBufferEnd = 0;\n    const pizChannelData = new Array(decoder.channels);\n    for (let i = 0; i < decoder.channels; i++) {\n        pizChannelData[i] = {};\n        pizChannelData[i][\"start\"] = outBufferEnd;\n        pizChannelData[i][\"end\"] = pizChannelData[i][\"start\"];\n        pizChannelData[i][\"nx\"] = decoder.width;\n        pizChannelData[i][\"ny\"] = decoder.lines;\n        pizChannelData[i][\"size\"] = decoder.type;\n        outBufferEnd += pizChannelData[i].nx * pizChannelData[i].ny * pizChannelData[i].size;\n    }\n    // Read range compression data\n    const minNonZero = ParseUint16(inDataView, inOffset);\n    const maxNonZero = ParseUint16(inDataView, inOffset);\n    if (maxNonZero >= BITMAP_SIZE) {\n        throw new Error(\"Wrong PIZ_COMPRESSION BITMAP_SIZE\");\n    }\n    if (minNonZero <= maxNonZero) {\n        for (let i = 0; i < maxNonZero - minNonZero + 1; i++) {\n            bitmap[i + minNonZero] = ParseUint8(inDataView, inOffset);\n        }\n    }\n    // Reverse LUT\n    const lut = new Uint16Array(USHORT_RANGE);\n    const maxValue = ReverseLutFromBitmap(bitmap, lut);\n    const length = ParseUint32(inDataView, inOffset);\n    // Huffman decoding\n    HufUncompress(decoder.array, inDataView, inOffset, length, outBuffer, outBufferEnd);\n    // Wavelet decoding\n    for (let i = 0; i < decoder.channels; ++i) {\n        const cd = pizChannelData[i];\n        for (let j = 0; j < pizChannelData[i].size; ++j) {\n            Wav2Decode(outBuffer, cd.start + j, cd.nx, cd.size, cd.ny, cd.nx * cd.size, maxValue);\n        }\n    }\n    // Expand the pixel data to their original range\n    ApplyLut(lut, outBuffer, outBufferEnd);\n    // Rearrange the pixel data into the format expected by the caller.\n    let tmpOffset = 0;\n    const tmpBuffer = new Uint8Array(outBuffer.buffer.byteLength);\n    for (let y = 0; y < decoder.lines; y++) {\n        for (let c = 0; c < decoder.channels; c++) {\n            const cd = pizChannelData[c];\n            const n = cd.nx * cd.size;\n            const cp = new Uint8Array(outBuffer.buffer, cd.end * INT16_SIZE, n * INT16_SIZE);\n            tmpBuffer.set(cp, tmpOffset);\n            tmpOffset += n * INT16_SIZE;\n            cd.end += n;\n        }\n    }\n    return new DataView(tmpBuffer.buffer);\n}\n//# sourceMappingURL=exrLoader.compression.js.map", "export var EXROutputType;\n(function (EXROutputType) {\n    EXROutputType[EXROutputType[\"Float\"] = 0] = \"Float\";\n    EXROutputType[EXROutputType[\"HalfFloat\"] = 1] = \"HalfFloat\";\n})(EXROutputType || (EXROutputType = {}));\n/**\n * Class used to store configuration of the exr loader\n */\nexport class ExrLoaderGlobalConfiguration {\n}\n/**\n * Defines the default output type to use (Half float by default)\n */\nExrLoaderGlobalConfiguration.DefaultOutputType = EXROutputType.HalfFloat;\n/**\n * Url to use to load the fflate library (for zip decompression)\n */\nExrLoaderGlobalConfiguration.FFLATEUrl = \"https://unpkg.com/fflate@0.8.2\";\n//# sourceMappingURL=exrLoader.configuration.js.map", "import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>32, Parse<PERSON><PERSON>16, Parse<PERSON><PERSON>32, Parse<PERSON>nt32, Parse<PERSON>nt64, <PERSON>rse<PERSON>int16, ParseUint32 } from \"./exrLoader.core.js\";\nimport { UncompressPIZ, UncompressPXR, UncompressRAW, UncompressRLE, UncompressZIP } from \"./exrLoader.compression.js\";\nimport { FLOAT32_SIZE, INT16_SIZE } from \"./exrLoader.interfaces.js\";\n\nimport { Tools } from \"../../../../Misc/tools.js\";\nimport { ExrLoaderGlobalConfiguration, EXROutputType } from \"./exrLoader.configuration.js\";\n/**\n * Inspired by https://github.com/sciecode/three.js/blob/dev/examples/jsm/loaders/EXRLoader.js\n * Referred to the original Industrial Light & Magic OpenEXR implementation and the TinyEXR / Syoyo Fujita\n * implementation.\n */\n// /*\n// Copyright (c) 2014 - 2017, Syoyo <PERSON>\n// All rights reserved.\n// Redistribution and use in source and binary forms, with or without\n// modification, are permitted provided that the following conditions are met:\n//     * Redistributions of source code must retain the above copyright\n//       notice, this list of conditions and the following disclaimer.\n//     * Redistributions in binary form must reproduce the above copyright\n//       notice, this list of conditions and the following disclaimer in the\n//       documentation and/or other materials provided with the distribution.\n//     * Neither the name of the Syoyo Fujita nor the\n//       names of its contributors may be used to endorse or promote products\n//       derived from this software without specific prior written permission.\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n// DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY\n// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n// */\n// // TinyEXR contains some OpenEXR code, which is licensed under ------------\n// ///////////////////////////////////////////////////////////////////////////\n// //\n// // Copyright (c) 2002, Industrial Light & Magic, a division of Lucas\n// // Digital Ltd. LLC\n// //\n// // All rights reserved.\n// //\n// // Redistribution and use in source and binary forms, with or without\n// // modification, are permitted provided that the following conditions are\n// // met:\n// // *       Redistributions of source code must retain the above copyright\n// // notice, this list of conditions and the following disclaimer.\n// // *       Redistributions in binary form must reproduce the above\n// // copyright notice, this list of conditions and the following disclaimer\n// // in the documentation and/or other materials provided with the\n// // distribution.\n// // *       Neither the name of Industrial Light & Magic nor the names of\n// // its contributors may be used to endorse or promote products derived\n// // from this software without specific prior written permission.\n// //\n// // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n// // \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n// // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n// // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n// // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n// // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n// // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n// // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n// // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n// // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n// //\n// ///////////////////////////////////////////////////////////////////////////\n// // End of OpenEXR license -------------------------------------------------\n/**\n * Create a decoder for the exr file\n * @param header header of the exr file\n * @param dataView dataview of the exr file\n * @param offset current offset\n * @param outputType expected output type (float or half float)\n * @returns a promise that resolves with the decoder\n */\nexport async function CreateDecoderAsync(header, dataView, offset, outputType) {\n    const decoder = {\n        size: 0,\n        viewer: dataView,\n        array: new Uint8Array(dataView.buffer),\n        offset: offset,\n        width: header.dataWindow.xMax - header.dataWindow.xMin + 1,\n        height: header.dataWindow.yMax - header.dataWindow.yMin + 1,\n        channels: header.channels.length,\n        channelLineOffsets: {},\n        scanOrder: () => 0,\n        bytesPerLine: 0,\n        outLineWidth: 0,\n        lines: 0,\n        scanlineBlockSize: 0,\n        inputSize: null,\n        type: 0,\n        uncompress: null,\n        getter: () => 0,\n        format: 5,\n        outputChannels: 0,\n        decodeChannels: {},\n        blockCount: null,\n        byteArray: null,\n        linearSpace: false,\n        textureType: 0,\n    };\n    switch (header.compression) {\n        case CompressionCodes.NO_COMPRESSION:\n            decoder.lines = 1;\n            decoder.uncompress = UncompressRAW;\n            break;\n        case CompressionCodes.RLE_COMPRESSION:\n            decoder.lines = 1;\n            decoder.uncompress = UncompressRLE;\n            break;\n        case CompressionCodes.ZIPS_COMPRESSION:\n            decoder.lines = 1;\n            decoder.uncompress = UncompressZIP;\n            await Tools.LoadScriptAsync(ExrLoaderGlobalConfiguration.FFLATEUrl);\n            break;\n        case CompressionCodes.ZIP_COMPRESSION:\n            decoder.lines = 16;\n            decoder.uncompress = UncompressZIP;\n            await Tools.LoadScriptAsync(ExrLoaderGlobalConfiguration.FFLATEUrl);\n            break;\n        case CompressionCodes.PIZ_COMPRESSION:\n            decoder.lines = 32;\n            decoder.uncompress = UncompressPIZ;\n            break;\n        case CompressionCodes.PXR24_COMPRESSION:\n            decoder.lines = 16;\n            decoder.uncompress = UncompressPXR;\n            await Tools.LoadScriptAsync(ExrLoaderGlobalConfiguration.FFLATEUrl);\n            break;\n        default:\n            throw new Error(CompressionCodes[header.compression] + \" is unsupported\");\n    }\n    decoder.scanlineBlockSize = decoder.lines;\n    const channels = {};\n    for (const channel of header.channels) {\n        switch (channel.name) {\n            case \"Y\":\n            case \"R\":\n            case \"G\":\n            case \"B\":\n            case \"A\":\n                channels[channel.name] = true;\n                decoder.type = channel.pixelType;\n        }\n    }\n    // RGB images will be converted to RGBA format, preventing software emulation in select devices.\n    let fillAlpha = false;\n    if (channels.R && channels.G && channels.B) {\n        fillAlpha = !channels.A;\n        decoder.outputChannels = 4;\n        decoder.decodeChannels = { R: 0, G: 1, B: 2, A: 3 };\n    }\n    else if (channels.Y) {\n        decoder.outputChannels = 1;\n        decoder.decodeChannels = { Y: 0 };\n    }\n    else {\n        throw new Error(\"EXRLoader.parse: file contains unsupported data channels.\");\n    }\n    if (decoder.type === 1) {\n        // half\n        switch (outputType) {\n            case EXROutputType.Float:\n                decoder.getter = ParseFloat16;\n                decoder.inputSize = INT16_SIZE;\n                break;\n            case EXROutputType.HalfFloat:\n                decoder.getter = ParseUint16;\n                decoder.inputSize = INT16_SIZE;\n                break;\n        }\n    }\n    else if (decoder.type === 2) {\n        // float\n        switch (outputType) {\n            case EXROutputType.Float:\n                decoder.getter = ParseFloat32;\n                decoder.inputSize = FLOAT32_SIZE;\n                break;\n            case EXROutputType.HalfFloat:\n                decoder.getter = DecodeFloat32;\n                decoder.inputSize = FLOAT32_SIZE;\n        }\n    }\n    else {\n        throw new Error(\"Unsupported pixelType \" + decoder.type + \" for \" + header.compression);\n    }\n    decoder.blockCount = decoder.height / decoder.scanlineBlockSize;\n    for (let i = 0; i < decoder.blockCount; i++) {\n        ParseInt64(dataView, offset); // scanlineOffset\n    }\n    // we should be passed the scanline offset table, ready to start reading pixel data.\n    const size = decoder.width * decoder.height * decoder.outputChannels;\n    switch (outputType) {\n        case EXROutputType.Float:\n            decoder.byteArray = new Float32Array(size);\n            decoder.textureType = 1;\n            // Fill initially with 1s for the alpha value if the texture is not RGBA, RGB values will be overwritten\n            if (fillAlpha) {\n                decoder.byteArray.fill(1, 0, size);\n            }\n            break;\n        case EXROutputType.HalfFloat:\n            decoder.byteArray = new Uint16Array(size);\n            decoder.textureType = 2;\n            if (fillAlpha) {\n                decoder.byteArray.fill(0x3c00, 0, size); // Uint16Array holds half float data, 0x3C00 is 1\n            }\n            break;\n        default:\n            throw new Error(\"Unsupported type: \" + outputType);\n    }\n    let byteOffset = 0;\n    for (const channel of header.channels) {\n        if (decoder.decodeChannels[channel.name] !== undefined) {\n            decoder.channelLineOffsets[channel.name] = byteOffset * decoder.width;\n        }\n        byteOffset += channel.pixelType * 2;\n    }\n    decoder.bytesPerLine = decoder.width * byteOffset;\n    decoder.outLineWidth = decoder.width * decoder.outputChannels;\n    if (header.lineOrder === \"INCREASING_Y\") {\n        decoder.scanOrder = (y) => y;\n    }\n    else {\n        decoder.scanOrder = (y) => decoder.height - 1 - y;\n    }\n    if (decoder.outputChannels == 4) {\n        decoder.format = 5;\n        decoder.linearSpace = true;\n    }\n    else {\n        decoder.format = 6;\n        decoder.linearSpace = false;\n    }\n    return decoder;\n}\n/**\n * Scan the data of the exr file\n * @param decoder decoder to use\n * @param header header of the exr file\n * @param dataView dataview of the exr file\n * @param offset current offset\n */\nexport function ScanData(decoder, header, dataView, offset) {\n    const tmpOffset = { value: 0 };\n    for (let scanlineBlockIdx = 0; scanlineBlockIdx < decoder.height / decoder.scanlineBlockSize; scanlineBlockIdx++) {\n        const line = ParseInt32(dataView, offset) - header.dataWindow.yMin; // line_no\n        decoder.size = ParseUint32(dataView, offset); // data_len\n        decoder.lines = line + decoder.scanlineBlockSize > decoder.height ? decoder.height - line : decoder.scanlineBlockSize;\n        const isCompressed = decoder.size < decoder.lines * decoder.bytesPerLine;\n        const viewer = isCompressed && decoder.uncompress ? decoder.uncompress(decoder) : UncompressRAW(decoder);\n        offset.value += decoder.size;\n        for (let line_y = 0; line_y < decoder.scanlineBlockSize; line_y++) {\n            const scan_y = scanlineBlockIdx * decoder.scanlineBlockSize;\n            const true_y = line_y + decoder.scanOrder(scan_y);\n            if (true_y >= decoder.height) {\n                continue;\n            }\n            const lineOffset = line_y * decoder.bytesPerLine;\n            const outLineOffset = (decoder.height - 1 - true_y) * decoder.outLineWidth;\n            for (let channelID = 0; channelID < decoder.channels; channelID++) {\n                const name = header.channels[channelID].name;\n                const lOff = decoder.channelLineOffsets[name];\n                const cOff = decoder.decodeChannels[name];\n                if (cOff === undefined) {\n                    continue;\n                }\n                tmpOffset.value = lineOffset + lOff;\n                for (let x = 0; x < decoder.width; x++) {\n                    const outIndex = outLineOffset + x * decoder.outputChannels + cOff;\n                    if (decoder.byteArray) {\n                        decoder.byteArray[outIndex] = decoder.getter(viewer, tmpOffset);\n                    }\n                }\n            }\n        }\n    }\n}\n//# sourceMappingURL=exrLoader.decoder.js.map", "import { GetExrHeader } from \"./EXR/exrLoader.header.js\";\nimport { CreateDecoder<PERSON><PERSON>, ScanData } from \"./EXR/exrLoader.decoder.js\";\nimport { ExrLoaderGlobalConfiguration } from \"./EXR/exrLoader.configuration.js\";\n/**\n * Inspired by https://github.com/sciecode/three.js/blob/dev/examples/jsm/loaders/EXRLoader.js\n * Referred to the original Industrial Light & Magic OpenEXR implementation and the TinyEXR / Syoyo Fujita\n * implementation.\n */\n// /*\n// Copyright (c) 2014 - 2017, Syoyo Fujita\n// All rights reserved.\n// Redistribution and use in source and binary forms, with or without\n// modification, are permitted provided that the following conditions are met:\n//     * Redistributions of source code must retain the above copyright\n//       notice, this list of conditions and the following disclaimer.\n//     * Redistributions in binary form must reproduce the above copyright\n//       notice, this list of conditions and the following disclaimer in the\n//       documentation and/or other materials provided with the distribution.\n//     * Neither the name of the Syoyo Fuji<PERSON> nor the\n//       names of its contributors may be used to endorse or promote products\n//       derived from this software without specific prior written permission.\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n// DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY\n// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n// */\n// // TinyEXR contains some OpenEXR code, which is licensed under ------------\n// ///////////////////////////////////////////////////////////////////////////\n// //\n// // Copyright (c) 2002, Industrial Light & Magic, a division of Lucas\n// // Digital Ltd. LLC\n// //\n// // All rights reserved.\n// //\n// // Redistribution and use in source and binary forms, with or without\n// // modification, are permitted provided that the following conditions are\n// // met:\n// // *       Redistributions of source code must retain the above copyright\n// // notice, this list of conditions and the following disclaimer.\n// // *       Redistributions in binary form must reproduce the above\n// // copyright notice, this list of conditions and the following disclaimer\n// // in the documentation and/or other materials provided with the\n// // distribution.\n// // *       Neither the name of Industrial Light & Magic nor the names of\n// // its contributors may be used to endorse or promote products derived\n// // from this software without specific prior written permission.\n// //\n// // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n// // \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n// // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n// // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n// // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n// // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n// // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n// // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n// // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n// // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n// //\n// ///////////////////////////////////////////////////////////////////////////\n// // End of OpenEXR license -------------------------------------------------\n/**\n * Loader for .exr file format\n * @see [PIZ compression](https://playground.babylonjs.com/#4RN0VF#151)\n * @see [ZIP compression](https://playground.babylonjs.com/#4RN0VF#146)\n * @see [RLE compression](https://playground.babylonjs.com/#4RN0VF#149)\n * @see [PXR24 compression](https://playground.babylonjs.com/#4RN0VF#150)\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class _ExrTextureLoader {\n    constructor() {\n        /**\n         * Defines whether the loader supports cascade loading the different faces.\n         */\n        this.supportCascades = false;\n    }\n    /**\n     * Uploads the cube texture data to the WebGL texture. It has already been bound.\n     * @param _data contains the texture data\n     * @param _texture defines the BabylonJS internal texture\n     * @param _createPolynomials will be true if polynomials have been requested\n     * @param _onLoad defines the callback to trigger once the texture is ready\n     * @param _onError defines the callback to trigger in case of error\n     * Cube texture are not supported by .exr files\n     */\n    loadCubeData(_data, _texture, _createPolynomials, _onLoad, _onError) {\n        // eslint-disable-next-line no-throw-literal\n        throw \".exr not supported in Cube.\";\n    }\n    /**\n     * Uploads the 2D texture data to the WebGL texture. It has already been bound once in the callback.\n     * @param data contains the texture data\n     * @param texture defines the BabylonJS internal texture\n     * @param callback defines the method to call once ready to upload\n     */\n    async loadData(data, texture, callback) {\n        const dataView = new DataView(data.buffer);\n        const offset = { value: 0 };\n        const header = GetExrHeader(dataView, offset);\n        const decoder = await CreateDecoderAsync(header, dataView, offset, ExrLoaderGlobalConfiguration.DefaultOutputType);\n        ScanData(decoder, header, dataView, offset);\n        // Updating texture\n        const width = header.dataWindow.xMax - header.dataWindow.xMin + 1;\n        const height = header.dataWindow.yMax - header.dataWindow.yMin + 1;\n        callback(width, height, texture.generateMipMaps, false, () => {\n            const engine = texture.getEngine();\n            texture.format = header.format;\n            texture.type = decoder.textureType;\n            texture.invertY = false;\n            texture._gammaSpace = !header.linearSpace;\n            if (decoder.byteArray) {\n                engine._uploadDataToTextureDirectly(texture, decoder.byteArray, 0, 0, undefined, true);\n            }\n        });\n    }\n}\n//# sourceMappingURL=exrTextureLoader.js.map"], "names": ["INT32_SIZE", "FLOAT32_SIZE", "INT8_SIZE", "INT16_SIZE", "ULONG_SIZE", "USHORT_RANGE", "BITMAP_SIZE", "HUF_ENCBITS", "HUF_DECBITS", "HUF_ENCSIZE", "HUF_DECSIZE", "HUF_DECMASK", "SHORT_ZEROCODE_RUN", "LONG_ZEROCODE_RUN", "SHORTEST_LONG_RUN", "CompressionCodes", "LineOrders", "_tables", "_GenerateTables", "buffer", "floatView", "uint32View", "baseTable", "shiftTable", "e", "mantissaTable", "exponentTable", "offsetTable", "m", "ParseNullTerminatedString", "offset", "uint<PERSON><PERSON><PERSON>", "endOffset", "stringValue", "ParseInt32", "dataView", "value", "ParseUint32", "ParseUint8", "ParseUint16", "ParseUint8Array", "array", "ParseInt64", "int", "ParseFloat32", "ParseFloat16", "DecodeFloat16", "binary", "exponent", "fraction", "ToHalfFloat", "C<PERSON>", "f", "DecodeFloat32", "ParseFixedLengthString", "size", "ParseRational", "x", "y", "ParseTimecode", "ParseV2f", "ParseV3f", "z", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "startOffset", "channels", "name", "pixelType", "pLinear", "xSampling", "ySampling", "ParseChromaticities", "redX", "redY", "greenX", "greenY", "blueX", "blueY", "whiteX", "whiteY", "ParseCompression", "ParseBox2i", "xMin", "yMin", "xMax", "yMax", "ParseLineOrder", "lineOrder", "ParseValue", "type", "Predictor", "source", "d", "InterleaveScalar", "out", "t1", "t2", "s", "stop", "EXR_MAGIC", "GetExrHeader", "version", "specData", "spec", "headerData", "keepReading", "attributeName", "attributeType", "attributeSize", "attributeValue", "<PERSON><PERSON>", "NBITS", "A_OFFSET", "MOD_MASK", "ReverseLutFromBitmap", "bitmap", "lut", "k", "i", "n", "HufClearDecTable", "hdec", "GetBits", "nBits", "c", "lc", "GetChar", "GetCode", "po", "rlc", "outBuffer", "outBufferOffset", "outBufferEndOffset", "gc", "cs", "HufTableBuffer", "HufCanonicalCodeTable", "hcode", "nc", "HufUnpackEncTable", "ni", "im", "iM", "p", "gb", "l", "zerun", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code", "HufCode", "HufBuildDecTable", "hdecod", "pl", "plOffset", "HufDecode", "encodingTable", "decodingTable", "no", "outOffset", "inOffsetEnd", "index", "gCode", "j", "<PERSON><PERSON>Uncompress", "nCompressed", "nRaw", "initialInOffset", "freq", "UInt16", "Int16", "ref", "Wdec14", "h", "ls", "hi", "ai", "as", "bs", "Wdec16", "bb", "Wav2Decode", "nx", "ox", "ny", "oy", "mx", "w14", "p2", "py", "ey", "oy1", "oy2", "ox1", "ox2", "i00", "i01", "i10", "i11", "px", "ex", "p01", "p10", "p11", "result", "ApplyLut", "data", "nData", "Decode<PERSON>un<PERSON><PERSON><PERSON>", "reader", "count", "UncompressRAW", "decoder", "UncompressRLE", "compressed", "<PERSON><PERSON><PERSON><PERSON>", "tmp<PERSON><PERSON><PERSON>", "UncompressZIP", "UncompressPXR", "sz", "tmpBufferEnd", "writePtr", "ptr", "pixel", "diff", "UncompressPIZ", "inDataView", "inOffset", "outBufferEnd", "pizChannelData", "minNonZero", "maxNonZero", "maxValue", "length", "cd", "tmpOffset", "cp", "EXROutputType", "ExrLoaderGlobalConfiguration", "CreateDecoderAsync", "header", "outputType", "Tools", "channel", "fill<PERSON>l<PERSON>", "byteOffset", "ScanData", "scanlineBlockIdx", "line", "viewer", "line_y", "scan_y", "true_y", "lineOffset", "outLineOffset", "channelID", "lOff", "cOff", "outIndex", "_ExrTextureLoader", "_data", "_texture", "_createPolynomials", "_onLoad", "_onError", "texture", "callback", "width", "height", "engine"], "mappings": "yDAAO,MAAMA,GAAa,EACbC,EAAe,EACfC,GAAY,EACZC,EAAa,EACbC,GAAa,EACbC,EAAe,MACfC,EAAcD,GAAgB,EAC9BE,GAAc,GACdC,EAAc,GACdC,GAAe,GAAKF,IAAe,EACnCG,EAAc,GAAKF,EACnBG,EAAcD,EAAc,EAC5BE,EAAqB,GACrBC,GAAoB,GACpBC,GAAoB,EAAID,GAAoBD,ECoDlD,IAAIG,GACV,SAAUA,EAAkB,CACzBA,EAAiBA,EAAiB,eAAoB,CAAC,EAAI,iBAC3DA,EAAiBA,EAAiB,gBAAqB,CAAC,EAAI,kBAC5DA,EAAiBA,EAAiB,iBAAsB,CAAC,EAAI,mBAC7DA,EAAiBA,EAAiB,gBAAqB,CAAC,EAAI,kBAC5DA,EAAiBA,EAAiB,gBAAqB,CAAC,EAAI,kBAC5DA,EAAiBA,EAAiB,kBAAuB,CAAC,EAAI,mBAClE,GAAGA,IAAqBA,EAAmB,CAAE,EAAC,EAC9C,IAAIC,GACH,SAAUA,EAAY,CACnBA,EAAWA,EAAW,aAAkB,CAAC,EAAI,eAC7CA,EAAWA,EAAW,aAAkB,CAAC,EAAI,cACjD,GAAGA,IAAeA,EAAa,CAAE,EAAC,EAClC,MAAMC,EAAUC,GAAe,EAE/B,SAASA,IAAkB,CAEvB,MAAMC,EAAS,IAAI,YAAY,CAAC,EAC1BC,EAAY,IAAI,aAAaD,CAAM,EACnCE,EAAa,IAAI,YAAYF,CAAM,EACnCG,EAAY,IAAI,YAAY,GAAG,EAC/BC,EAAa,IAAI,YAAY,GAAG,EACtC,QAAS,EAAI,EAAG,EAAI,IAAK,EAAE,EAAG,CAC1B,MAAMC,EAAI,EAAI,IAEVA,EAAI,KACJF,EAAU,CAAC,EAAI,EACfA,EAAU,EAAI,GAAK,EAAI,MACvBC,EAAW,CAAC,EAAI,GAChBA,EAAW,EAAI,GAAK,EAAI,IAGnBC,EAAI,KACTF,EAAU,CAAC,EAAI,MAAW,CAACE,EAAI,GAC/BF,EAAU,EAAI,GAAK,EAAK,MAAW,CAACE,EAAI,GAAO,MAC/CD,EAAW,CAAC,EAAI,CAACC,EAAI,EACrBD,EAAW,EAAI,GAAK,EAAI,CAACC,EAAI,GAGxBA,GAAK,IACVF,EAAU,CAAC,EAAKE,EAAI,IAAO,GAC3BF,EAAU,EAAI,GAAK,EAAME,EAAI,IAAO,GAAM,MAC1CD,EAAW,CAAC,EAAI,GAChBA,EAAW,EAAI,GAAK,EAAI,IAGnBC,EAAI,KACTF,EAAU,CAAC,EAAI,MACfA,EAAU,EAAI,GAAK,EAAI,MACvBC,EAAW,CAAC,EAAI,GAChBA,EAAW,EAAI,GAAK,EAAI,KAIxBD,EAAU,CAAC,EAAI,MACfA,EAAU,EAAI,GAAK,EAAI,MACvBC,EAAW,CAAC,EAAI,GAChBA,EAAW,EAAI,GAAK,EAAI,GAE/B,CAED,MAAME,EAAgB,IAAI,YAAY,IAAI,EACpCC,EAAgB,IAAI,YAAY,EAAE,EAClCC,EAAc,IAAI,YAAY,EAAE,EACtC,QAAS,EAAI,EAAG,EAAI,KAAM,EAAE,EAAG,CAC3B,IAAIC,EAAI,GAAK,GACTJ,EAAI,EAER,KAAQ,EAAAI,EAAI,UACRA,IAAM,EACNJ,GAAK,QAETI,GAAK,SACLJ,GAAK,UACLC,EAAc,CAAC,EAAIG,EAAIJ,CAC1B,CACD,QAAS,EAAI,KAAM,EAAI,KAAM,EAAE,EAC3BC,EAAc,CAAC,EAAI,WAAe,EAAI,MAAS,IAEnD,QAAS,EAAI,EAAG,EAAI,GAAI,EAAE,EACtBC,EAAc,CAAC,EAAI,GAAK,GAE5BA,EAAc,EAAE,EAAI,WACpBA,EAAc,EAAE,EAAI,WACpB,QAAS,EAAI,GAAI,EAAI,GAAI,EAAE,EACvBA,EAAc,CAAC,EAAI,YAAe,EAAI,IAAO,IAEjDA,EAAc,EAAE,EAAI,WACpB,QAAS,EAAI,EAAG,EAAI,GAAI,EAAE,EAClB,IAAM,KACNC,EAAY,CAAC,EAAI,MAGzB,MAAO,CACH,UAAWP,EACX,WAAYC,EACZ,UAAWC,EACX,WAAYC,EACZ,cAAeE,EACf,cAAeC,EACf,YAAaC,CACrB,CACA,CAOO,SAASE,EAA0BV,EAAQW,EAAQ,CACtD,MAAMC,EAAa,IAAI,WAAWZ,CAAM,EACxC,IAAIa,EAAY,EAChB,KAAOD,EAAWD,EAAO,MAAQE,CAAS,GAAK,GAC3CA,GAAa,EAEjB,MAAMC,EAAc,IAAI,cAAc,OAAOF,EAAW,MAAMD,EAAO,MAAOA,EAAO,MAAQE,CAAS,CAAC,EACrG,OAAAF,EAAO,MAAQA,EAAO,MAAQE,EAAY,EACnCC,CACX,CAOO,SAASC,EAAWC,EAAUL,EAAQ,CACzC,MAAMM,EAAQD,EAAS,SAASL,EAAO,MAAO,EAAI,EAClD,OAAAA,EAAO,OAAS9B,GACToC,CACX,CAOO,SAASC,EAAYF,EAAUL,EAAQ,CAC1C,MAAMM,EAAQD,EAAS,UAAUL,EAAO,MAAO,EAAI,EACnD,OAAAA,EAAO,OAAS9B,GACToC,CACX,CAOO,SAASE,EAAWH,EAAUL,EAAQ,CACzC,MAAMM,EAAQD,EAAS,SAASL,EAAO,KAAK,EAC5C,OAAAA,EAAO,OAAS5B,GACTkC,CACX,CAOO,SAASG,EAAYJ,EAAUL,EAAQ,CAC1C,MAAMM,EAAQD,EAAS,UAAUL,EAAO,MAAO,EAAI,EACnD,OAAAA,EAAO,OAAS3B,EACTiC,CACX,CAOO,SAASI,GAAgBC,EAAOX,EAAQ,CAC3C,MAAMM,EAAQK,EAAMX,EAAO,KAAK,EAChC,OAAAA,EAAO,OAAS5B,GACTkC,CACX,CAOO,SAASM,GAAWP,EAAUL,EAAQ,CACzC,IAAIa,EACJ,MAAI,gBAAiB,SAAS,UAC1BA,EAAM,OAAOR,EAAS,YAAYL,EAAO,MAAO,EAAI,CAAC,EAGrDa,EAAMR,EAAS,UAAUL,EAAO,MAAQ,EAAG,EAAI,EAAI,OAAOK,EAAS,UAAUL,EAAO,MAAO,EAAI,GAAK,EAAE,EAE1GA,EAAO,OAAS1B,GACTuC,CACX,CAOO,SAASC,EAAaT,EAAUL,EAAQ,CAC3C,MAAMM,EAAQD,EAAS,WAAWL,EAAO,MAAO,EAAI,EACpD,OAAAA,EAAO,OAAS7B,EACTmC,CACX,CAOO,SAASS,GAAaV,EAAUL,EAAQ,CAC3C,OAAOgB,GAAcP,EAAYJ,EAAUL,CAAM,CAAC,CACtD,CACA,SAASgB,GAAcC,EAAQ,CAC3B,MAAMC,GAAYD,EAAS,QAAW,GAChCE,EAAWF,EAAS,KAC1B,OAASA,GAAU,GAAK,GAAK,IACxBC,EAAYA,IAAa,GAAQC,EAAW,IAAM,IAAY,KAAK,IAAI,EAAGD,EAAW,EAAE,GAAK,EAAIC,EAAW,MAAU,gBAAkBA,EAAW,MAC3J,CACA,SAASC,GAAYd,EAAO,CACxB,GAAI,KAAK,IAAIA,CAAK,EAAI,MAClB,MAAM,IAAI,MAAM,gEAAgE,EAEpFA,EAAQe,GAAMf,EAAO,OAAQ,KAAK,EAClCnB,EAAQ,UAAU,CAAC,EAAImB,EACvB,MAAMgB,EAAInC,EAAQ,WAAW,CAAC,EACxB,EAAKmC,GAAK,GAAM,IACtB,OAAOnC,EAAQ,UAAU,CAAC,IAAMmC,EAAI,UAAenC,EAAQ,WAAW,CAAC,EAC3E,CAOO,SAASoC,GAAclB,EAAUL,EAAQ,CAC5C,OAAOoB,GAAYN,EAAaT,EAAUL,CAAM,CAAC,CACrD,CACA,SAASwB,GAAuBnC,EAAQW,EAAQyB,EAAM,CAClD,MAAMtB,EAAc,IAAI,YAAW,EAAG,OAAO,IAAI,WAAWd,CAAM,EAAE,MAAMW,EAAO,MAAOA,EAAO,MAAQyB,CAAI,CAAC,EAC5G,OAAAzB,EAAO,MAAQA,EAAO,MAAQyB,EACvBtB,CACX,CACA,SAASuB,GAAcrB,EAAUL,EAAQ,CACrC,MAAM2B,EAAIvB,EAAWC,EAAUL,CAAM,EAC/B4B,EAAIrB,EAAYF,EAAUL,CAAM,EACtC,MAAO,CAAC2B,EAAGC,CAAC,CAChB,CACA,SAASC,GAAcxB,EAAUL,EAAQ,CACrC,MAAM2B,EAAIpB,EAAYF,EAAUL,CAAM,EAChC4B,EAAIrB,EAAYF,EAAUL,CAAM,EACtC,MAAO,CAAC2B,EAAGC,CAAC,CAChB,CACA,SAASE,GAASzB,EAAUL,EAAQ,CAChC,MAAM2B,EAAIb,EAAaT,EAAUL,CAAM,EACjC4B,EAAId,EAAaT,EAAUL,CAAM,EACvC,MAAO,CAAC2B,EAAGC,CAAC,CAChB,CACA,SAASG,GAAS1B,EAAUL,EAAQ,CAChC,MAAM2B,EAAIb,EAAaT,EAAUL,CAAM,EACjC4B,EAAId,EAAaT,EAAUL,CAAM,EACjCgC,EAAIlB,EAAaT,EAAUL,CAAM,EACvC,MAAO,CAAC2B,EAAGC,EAAGI,CAAC,CACnB,CACA,SAASC,GAAY5B,EAAUL,EAAQyB,EAAM,CACzC,MAAMS,EAAclC,EAAO,MACrBmC,EAAW,CAAA,EACjB,KAAOnC,EAAO,MAAQkC,EAAcT,EAAO,GAAG,CAC1C,MAAMW,EAAOrC,EAA0BM,EAAS,OAAQL,CAAM,EACxDqC,EAAYjC,EAAWC,EAAUL,CAAM,EACvCsC,EAAU9B,EAAWH,EAAUL,CAAM,EAC3CA,EAAO,OAAS,EAChB,MAAMuC,EAAYnC,EAAWC,EAAUL,CAAM,EACvCwC,EAAYpC,EAAWC,EAAUL,CAAM,EAC7CmC,EAAS,KAAK,CACV,KAAMC,EACN,UAAWC,EACX,QAASC,EACT,UAAWC,EACX,UAAWC,CACvB,CAAS,CACJ,CACD,OAAAxC,EAAO,OAAS,EACTmC,CACX,CACA,SAASM,GAAoBpC,EAAUL,EAAQ,CAC3C,MAAM0C,EAAO5B,EAAaT,EAAUL,CAAM,EACpC2C,EAAO7B,EAAaT,EAAUL,CAAM,EACpC4C,EAAS9B,EAAaT,EAAUL,CAAM,EACtC6C,EAAS/B,EAAaT,EAAUL,CAAM,EACtC8C,EAAQhC,EAAaT,EAAUL,CAAM,EACrC+C,EAAQjC,EAAaT,EAAUL,CAAM,EACrCgD,EAASlC,EAAaT,EAAUL,CAAM,EACtCiD,EAASnC,EAAaT,EAAUL,CAAM,EAC5C,MAAO,CAAE,KAAM0C,EAAM,KAAMC,EAAM,OAAQC,EAAQ,OAAQC,EAAQ,MAAOC,EAAO,MAAOC,EAAO,OAAQC,EAAQ,OAAQC,EACzH,CACA,SAASC,GAAiB7C,EAAUL,EAAQ,CACxC,OAAOQ,EAAWH,EAAUL,CAAM,CACtC,CACA,SAASmD,GAAW9C,EAAUL,EAAQ,CAClC,MAAMoD,EAAOhD,EAAWC,EAAUL,CAAM,EAClCqD,EAAOjD,EAAWC,EAAUL,CAAM,EAClCsD,EAAOlD,EAAWC,EAAUL,CAAM,EAClCuD,EAAOnD,EAAWC,EAAUL,CAAM,EACxC,MAAO,CAAE,KAAMoD,EAAM,KAAMC,EAAM,KAAMC,EAAM,KAAMC,EACvD,CACA,SAASC,GAAenD,EAAUL,EAAQ,CACtC,MAAMyD,EAAYjD,EAAWH,EAAUL,CAAM,EAC7C,OAAOd,EAAWuE,CAAS,CAC/B,CASO,SAASC,GAAWrD,EAAUL,EAAQ2D,EAAMlC,EAAM,CACrD,OAAQkC,EAAI,CACR,IAAK,SACL,IAAK,eACL,IAAK,aACD,OAAOnC,GAAuBnB,EAAS,OAAQL,EAAQyB,CAAI,EAC/D,IAAK,SACD,OAAOQ,GAAY5B,EAAUL,EAAQyB,CAAI,EAC7C,IAAK,iBACD,OAAOgB,GAAoBpC,EAAUL,CAAM,EAC/C,IAAK,cACD,OAAOkD,GAAiB7C,EAAUL,CAAM,EAC5C,IAAK,QACD,OAAOmD,GAAW9C,EAAUL,CAAM,EACtC,IAAK,YACD,OAAOwD,GAAenD,EAAUL,CAAM,EAC1C,IAAK,QACD,OAAOc,EAAaT,EAAUL,CAAM,EACxC,IAAK,MACD,OAAO8B,GAASzB,EAAUL,CAAM,EACpC,IAAK,MACD,OAAO+B,GAAS1B,EAAUL,CAAM,EACpC,IAAK,MACD,OAAOI,EAAWC,EAAUL,CAAM,EACtC,IAAK,WACD,OAAO0B,GAAcrB,EAAUL,CAAM,EACzC,IAAK,WACD,OAAO6B,GAAcxB,EAAUL,CAAM,EACzC,IAAK,UACD,OAAAA,EAAO,OAASyB,EACT,UACX,QACIzB,EAAO,OAASyB,EAChB,MACP,CACL,CAKO,SAASmC,GAAUC,EAAQ,CAC9B,QAAS,EAAI,EAAG,EAAIA,EAAO,OAAQ,IAAK,CACpC,MAAMC,EAAID,EAAO,EAAI,CAAC,EAAIA,EAAO,CAAC,EAAI,IACtCA,EAAO,CAAC,EAAIC,CACf,CACL,CAMO,SAASC,GAAiBF,EAAQG,EAAK,CAC1C,IAAIC,EAAK,EACLC,EAAK,KAAK,OAAOL,EAAO,OAAS,GAAK,CAAC,EACvCM,EAAI,EACR,MAAMC,EAAOP,EAAO,OAAS,EAE7B,KACQ,EAAAM,EAAIC,IAGRJ,EAAIG,GAAG,EAAIN,EAAOI,GAAI,EAClBE,EAAIC,KAGRJ,EAAIG,GAAG,EAAIN,EAAOK,GAAI,CAE9B,CC/XA,MAAMG,GAAY,SAOX,SAASC,GAAajE,EAAUL,EAAQ,CAC3C,GAAIK,EAAS,UAAU,EAAG,EAAI,GAAKgE,GAC/B,MAAM,IAAI,MAAM,0BAA0B,EAE9C,MAAME,EAAUlE,EAAS,SAAS,CAAC,EAC7BmE,EAAWnE,EAAS,SAAS,CAAC,EAC9BoE,EAAO,CACT,WAAY,CAAC,EAAED,EAAW,GAC1B,SAAU,CAAC,EAAEA,EAAW,GACxB,WAAY,CAAC,EAAEA,EAAW,GAC1B,UAAW,CAAC,EAAEA,EAAW,GACjC,EACIxE,EAAO,MAAQ,EACf,MAAM0E,EAAa,CAAA,EACnB,IAAIC,EAAc,GAClB,KAAOA,GAAa,CAChB,MAAMC,EAAgB7E,EAA0BM,EAAS,OAAQL,CAAM,EACvE,GAAI,CAAC4E,EACDD,EAAc,OAEb,CACD,MAAME,EAAgB9E,EAA0BM,EAAS,OAAQL,CAAM,EACjE8E,EAAgBvE,EAAYF,EAAUL,CAAM,EAC5C+E,EAAiBrB,GAAWrD,EAAUL,EAAQ6E,EAAeC,CAAa,EAC5EC,IAAmB,OACnBC,GAAO,KAAK,iCAAiCH,CAAa,IAAI,EAG9DH,EAAWE,CAAa,EAAIG,CAEnC,CACJ,CACD,GAAKP,EAAW,GACZ,MAAM,IAAI,MAAM,yBAAyB,EAE7C,MAAO,CAAE,QAASD,EAAS,KAAME,EAAM,GAAGC,EAC9C,CC5CA,MAAMO,GAAQ,GACRC,GAAW,GAAMD,GAAQ,EACzBE,IAAY,GAAKF,IAAS,EAEzB,SAASG,GAAqBC,EAAQC,EAAK,CAC9C,IAAIC,EAAI,EACR,QAASC,EAAI,EAAGA,EAAIjH,EAAc,EAAEiH,GAC5BA,GAAK,GAAKH,EAAOG,GAAK,CAAC,EAAK,IAAMA,EAAI,MACtCF,EAAIC,GAAG,EAAIC,GAGnB,MAAMC,EAAIF,EAAI,EACd,KAAOA,EAAIhH,GACP+G,EAAIC,GAAG,EAAI,EACf,OAAOE,CACX,CACA,SAASC,GAAiBC,EAAM,CAC5B,QAASH,EAAI,EAAGA,EAAI5G,EAAa4G,IAC7BG,EAAKH,CAAC,EAAI,GACVG,EAAKH,CAAC,EAAE,IAAM,EACdG,EAAKH,CAAC,EAAE,IAAM,EACdG,EAAKH,CAAC,EAAE,EAAI,IAEpB,CACA,SAASI,GAAQC,EAAOC,EAAGC,EAAIpF,EAAOX,EAAQ,CAC1C,KAAO+F,EAAKF,GACRC,EAAKA,GAAK,EAAKpF,GAAgBC,EAAOX,CAAM,EAC5C+F,GAAM,EAEV,OAAAA,GAAMF,EACC,CACH,EAAIC,GAAKC,GAAQ,GAAKF,GAAS,EAC/B,EAAAC,EACA,GAAAC,CACR,CACA,CACA,SAASC,EAAQF,EAAGC,EAAIpF,EAAOX,EAAQ,CACnC,OAAA8F,EAAKA,GAAK,EAAKpF,GAAgBC,EAAOX,CAAM,EAC5C+F,GAAM,EACC,CACH,EAAAD,EACA,GAAAC,CACR,CACA,CACA,SAASE,EAAQC,EAAIC,EAAKL,EAAGC,EAAIpF,EAAOX,EAAQoG,EAAWC,EAAiBC,EAAoB,CAC5F,GAAIJ,GAAMC,EAAK,CACX,GAAIJ,EAAK,EAAG,CACR,MAAMQ,EAAKP,EAAQF,EAAGC,EAAIpF,EAAOX,CAAM,EACvC8F,EAAIS,EAAG,EACPR,EAAKQ,EAAG,EACX,CACDR,GAAM,EACN,IAAIS,EAAKV,GAAKC,EAEd,GADAS,EAAK,IAAI,WAAW,CAACA,CAAE,CAAC,EAAE,CAAC,EACvBH,EAAgB,MAAQG,EAAKF,EAC7B,OAAO,KAEX,MAAMnC,EAAIiC,EAAUC,EAAgB,MAAQ,CAAC,EAC7C,KAAOG,KAAO,GACVJ,EAAUC,EAAgB,OAAO,EAAIlC,CAE5C,SACQkC,EAAgB,MAAQC,EAC7BF,EAAUC,EAAgB,OAAO,EAAIH,MAGrC,QAAO,KAEX,MAAO,CAAE,EAAAJ,EAAG,GAAAC,EAChB,CACA,MAAMU,EAAiB,IAAI,MAAM,EAAE,EACnC,SAASC,GAAsBC,EAAO,CAClC,QAASnB,EAAI,EAAGA,GAAK,GAAI,EAAEA,EACvBiB,EAAejB,CAAC,EAAI,EACxB,QAASA,EAAI,EAAGA,EAAI7G,EAAa,EAAE6G,EAC/BiB,EAAeE,EAAMnB,CAAC,CAAC,GAAK,EAChC,IAAIM,EAAI,EACR,QAASN,EAAI,GAAIA,EAAI,EAAG,EAAEA,EAAG,CACzB,MAAMoB,EAAMd,EAAIW,EAAejB,CAAC,GAAM,EACtCiB,EAAejB,CAAC,EAAIM,EACpBA,EAAIc,CACP,CACD,QAASpB,EAAI,EAAGA,EAAI7G,EAAa,EAAE6G,EAAG,CAClC,MAAM,EAAImB,EAAMnB,CAAC,EACb,EAAI,IACJmB,EAAMnB,CAAC,EAAI,EAAKiB,EAAe,CAAC,KAAO,EAC9C,CACL,CACA,SAASI,GAAkBlG,EAAOX,EAAQ8G,EAAIC,EAAIC,EAAIL,EAAO,CACzD,MAAMM,EAAIjH,EACV,IAAI8F,EAAI,EACJC,EAAK,EACT,KAAOgB,GAAMC,EAAID,IAAM,CACnB,GAAIE,EAAE,MAAQjH,EAAO,MAAQ8G,EACzB,OAEJ,IAAII,EAAKtB,GAAQ,EAAGE,EAAGC,EAAIpF,EAAOsG,CAAC,EACnC,MAAME,EAAID,EAAG,EAIb,GAHApB,EAAIoB,EAAG,EACPnB,EAAKmB,EAAG,GACRP,EAAMI,CAAE,EAAII,EACRA,GAAKpI,GAAmB,CACxB,GAAIkI,EAAE,MAAQjH,EAAO,MAAQ8G,EACzB,MAAM,IAAI,MAAM,4BAA4B,EAEhDI,EAAKtB,GAAQ,EAAGE,EAAGC,EAAIpF,EAAOsG,CAAC,EAC/B,IAAIG,EAAQF,EAAG,EAAIlI,GAGnB,GAFA8G,EAAIoB,EAAG,EACPnB,EAAKmB,EAAG,GACJH,EAAKK,EAAQJ,EAAK,EAClB,MAAM,IAAI,MAAM,4BAA4B,EAEhD,KAAOI,KACHT,EAAMI,GAAI,EAAI,EAClBA,GACH,SACQI,GAAKrI,EAAoB,CAC9B,IAAIsI,EAAQD,EAAIrI,EAAqB,EACrC,GAAIiI,EAAKK,EAAQJ,EAAK,EAClB,MAAM,IAAI,MAAM,4BAA4B,EAEhD,KAAOI,KACHT,EAAMI,GAAI,EAAI,EAClBA,GACH,CACJ,CACDL,GAAsBC,CAAK,CAC/B,CACA,SAASU,GAAUC,EAAM,CACrB,OAAOA,EAAO,EAClB,CACA,SAASC,GAAQD,EAAM,CACnB,OAAOA,GAAQ,CACnB,CACA,SAASE,GAAiBb,EAAOI,EAAIC,EAAIS,EAAQ,CAC7C,KAAOV,GAAMC,EAAID,IAAM,CACnB,MAAMjB,EAAIyB,GAAQZ,EAAMI,CAAE,CAAC,EACrBI,EAAIE,GAAUV,EAAMI,CAAE,CAAC,EAC7B,GAAIjB,GAAKqB,EACL,MAAM,IAAI,MAAM,qBAAqB,EAEzC,GAAIA,EAAIzI,EAAa,CACjB,MAAMgJ,EAAKD,EAAO3B,GAAMqB,EAAIzI,CAAY,EACxC,GAAIgJ,EAAG,IACH,MAAM,IAAI,MAAM,qBAAqB,EAGzC,GADAA,EAAG,MACCA,EAAG,EAAG,CACN,MAAMT,EAAIS,EAAG,EACbA,EAAG,EAAI,IAAI,MAAMA,EAAG,GAAG,EACvB,QAAS,EAAI,EAAG,EAAIA,EAAG,IAAM,EAAG,EAAE,EAC9BA,EAAG,EAAE,CAAC,EAAIT,EAAE,CAAC,CAEpB,MAEGS,EAAG,EAAI,IAAI,MAAM,CAAC,EAEtBA,EAAG,EAAEA,EAAG,IAAM,CAAC,EAAIX,CACtB,SACQI,EAAG,CACR,IAAIQ,EAAW,EACf,QAASnC,EAAI,GAAM9G,EAAcyI,EAAI3B,EAAI,EAAGA,IAAK,CAC7C,MAAMkC,EAAKD,GAAQ3B,GAAMpH,EAAcyI,GAAMQ,CAAQ,EACrD,GAAID,EAAG,KAAOA,EAAG,EACb,MAAM,IAAI,MAAM,qBAAqB,EAEzCA,EAAG,IAAMP,EACTO,EAAG,IAAMX,EACTY,GACH,CACJ,CACJ,CACD,MAAO,EACX,CACA,SAASC,GAAUC,EAAeC,EAAenH,EAAOX,EAAQ8G,EAAIX,EAAK4B,EAAI3B,EAAW4B,EAAW,CAC/F,IAAIlC,EAAI,EACJC,EAAK,EACT,MAAMO,EAAqByB,EACrBE,EAAc,KAAK,MAAMjI,EAAO,OAAS8G,EAAK,GAAK,CAAC,EAC1D,KAAO9G,EAAO,MAAQiI,GAAa,CAC/B,IAAI1B,EAAKP,EAAQF,EAAGC,EAAIpF,EAAOX,CAAM,EAGrC,IAFA8F,EAAIS,EAAG,EACPR,EAAKQ,EAAG,GACDR,GAAMrH,GAAa,CACtB,MAAMwJ,EAASpC,GAAMC,EAAKrH,EAAgBG,EACpC6I,EAAKI,EAAcI,CAAK,EAC9B,GAAIR,EAAG,IAAK,CACR3B,GAAM2B,EAAG,IACT,MAAMS,EAAQlC,EAAQyB,EAAG,IAAKvB,EAAKL,EAAGC,EAAIpF,EAAOX,EAAQoG,EAAW4B,EAAW1B,CAAkB,EAC7F6B,IACArC,EAAIqC,EAAM,EACVpC,EAAKoC,EAAM,GAElB,KACI,CACD,GAAI,CAACT,EAAG,EACJ,MAAM,IAAI,MAAM,kBAAkB,EAEtC,IAAIU,EACJ,IAAKA,EAAI,EAAGA,EAAIV,EAAG,IAAKU,IAAK,CACzB,MAAMjB,EAAIE,GAAUQ,EAAcH,EAAG,EAAEU,CAAC,CAAC,CAAC,EAC1C,KAAOrC,EAAKoB,GAAKnH,EAAO,MAAQiI,GAC5B1B,EAAKP,EAAQF,EAAGC,EAAIpF,EAAOX,CAAM,EACjC8F,EAAIS,EAAG,EACPR,EAAKQ,EAAG,GAEZ,GAAIR,GAAMoB,GACFI,GAAQM,EAAcH,EAAG,EAAEU,CAAC,CAAC,CAAC,IAAOtC,GAAMC,EAAKoB,GAAQ,GAAKA,GAAK,GAAK,CACvEpB,GAAMoB,EACN,MAAMgB,EAAQlC,EAAQyB,EAAG,EAAEU,CAAC,EAAGjC,EAAKL,EAAGC,EAAIpF,EAAOX,EAAQoG,EAAW4B,EAAW1B,CAAkB,EAC9F6B,IACArC,EAAIqC,EAAM,EACVpC,EAAKoC,EAAM,IAEf,KACH,CAER,CACD,GAAIC,GAAKV,EAAG,IACR,MAAM,IAAI,MAAM,kBAAkB,CAEzC,CACJ,CACJ,CACD,MAAMlC,EAAK,EAAIsB,EAAM,EAGrB,IAFAhB,IAAMN,EACNO,GAAMP,EACCO,EAAK,GAAG,CACX,MAAM2B,EAAKI,EAAehC,GAAMpH,EAAcqH,EAAOlH,CAAW,EAChE,GAAI6I,EAAG,IAAK,CACR3B,GAAM2B,EAAG,IACT,MAAMS,EAAQlC,EAAQyB,EAAG,IAAKvB,EAAKL,EAAGC,EAAIpF,EAAOX,EAAQoG,EAAW4B,EAAW1B,CAAkB,EAC7F6B,IACArC,EAAIqC,EAAM,EACVpC,EAAKoC,EAAM,GAElB,KAEG,OAAM,IAAI,MAAM,kBAAkB,CAEzC,CACD,MAAO,EACX,CAEO,SAASE,GAAc1H,EAAON,EAAUL,EAAQsI,EAAalC,EAAWmC,EAAM,CACjF,MAAMP,EAAY,CAAE,MAAO,GACrBQ,EAAkBxI,EAAO,MACzB+G,EAAKxG,EAAYF,EAAUL,CAAM,EACjCgH,EAAKzG,EAAYF,EAAUL,CAAM,EACvCA,EAAO,OAAS,EAChB,MAAM6F,EAAQtF,EAAYF,EAAUL,CAAM,EAE1C,GADAA,EAAO,OAAS,EACZ+G,EAAK,GAAKA,GAAMpI,GAAeqI,EAAK,GAAKA,GAAMrI,EAC/C,MAAM,IAAI,MAAM,mBAAmB,EAEvC,MAAM8J,EAAO,IAAI,MAAM9J,CAAW,EAC5BgH,EAAO,IAAI,MAAM/G,CAAW,EAClC8G,GAAiBC,CAAI,EACrB,MAAMmB,EAAKwB,GAAetI,EAAO,MAAQwI,GAEzC,GADA3B,GAAkBlG,EAAOX,EAAQ8G,EAAIC,EAAIC,EAAIyB,CAAI,EAC7C5C,EAAQ,GAAKyC,GAAetI,EAAO,MAAQwI,IAC3C,MAAM,IAAI,MAAM,qBAAqB,EAEzChB,GAAiBiB,EAAM1B,EAAIC,EAAIrB,CAAI,EACnCiC,GAAUa,EAAM9C,EAAMhF,EAAOX,EAAQ6F,EAAOmB,EAAIuB,EAAMnC,EAAW4B,CAAS,CAC9E,CACA,SAASU,EAAOpI,EAAO,CACnB,OAAOA,EAAQ,KACnB,CACA,SAASqI,GAAMrI,EAAO,CAClB,MAAMsI,EAAMF,EAAOpI,CAAK,EACxB,OAAOsI,EAAM,MAASA,EAAM,MAAUA,CAC1C,CACA,SAASC,EAAO1B,EAAG2B,EAAG,CAClB,MAAMC,EAAKJ,GAAMxB,CAAC,EAEZ6B,EADKL,GAAMG,CAAC,EAEZG,EAAKF,GAAMC,EAAK,IAAMA,GAAM,GAC5BE,EAAKD,EACLE,EAAKF,EAAKD,EAChB,MAAO,CAAE,EAAGE,EAAI,EAAGC,CAAE,CACzB,CACA,SAASC,EAAOjC,EAAG2B,EAAG,CAClB,MAAMhJ,EAAI4I,EAAOvB,CAAC,EACZrD,EAAI4E,EAAOI,CAAC,EACZO,EAAMvJ,GAAKgE,GAAK,GAAMqB,GAE5B,MAAO,CAAE,EADGrB,EAAIuF,EAAKnE,GAAYC,GACjB,EAAGkE,CAAE,CACzB,CAEO,SAASC,GAAWjK,EAAQ+I,EAAGmB,EAAIC,EAAIC,EAAIC,EAAIC,EAAI,CACtD,MAAMC,EAAMD,EAAK,MACXlE,EAAI8D,EAAKE,EAAKA,EAAKF,EACzB,IAAItC,EAAI,EACJ4C,EACAC,EACJ,KAAO7C,GAAKxB,GACRwB,IAAM,EAIV,IAHAA,IAAM,EACN4C,EAAK5C,EACLA,IAAM,EACCA,GAAK,GAAG,CACX6C,EAAK,EACL,MAAMC,EAAKD,EAAKJ,GAAMD,EAAKI,GACrBG,EAAMN,EAAKzC,EACXgD,EAAMP,EAAKG,EACXK,EAAMV,EAAKvC,EACXkD,EAAMX,EAAKK,EACjB,IAAIO,EAAKC,EAAKC,EAAKC,EACnB,KAAOT,GAAMC,EAAID,GAAMG,EAAK,CACxB,IAAIO,EAAKV,EACT,MAAMW,EAAKX,EAAKN,GAAMD,EAAKM,GAC3B,KAAOW,GAAMC,EAAID,GAAML,EAAK,CACxB,MAAMO,EAAMF,EAAKN,EACXS,EAAMH,EAAKR,EACXY,EAAMD,EAAMT,EAClB,GAAIN,EAAK,CACL,IAAIiB,EAAShC,EAAOxJ,EAAOmL,EAAKpC,CAAC,EAAG/I,EAAOsL,EAAMvC,CAAC,CAAC,EACnDgC,EAAMS,EAAO,EACbP,EAAMO,EAAO,EACbA,EAAShC,EAAOxJ,EAAOqL,EAAMtC,CAAC,EAAG/I,EAAOuL,EAAMxC,CAAC,CAAC,EAChDiC,EAAMQ,EAAO,EACbN,EAAMM,EAAO,EACbA,EAAShC,EAAOuB,EAAKC,CAAG,EACxBhL,EAAOmL,EAAKpC,CAAC,EAAIyC,EAAO,EACxBxL,EAAOqL,EAAMtC,CAAC,EAAIyC,EAAO,EACzBA,EAAShC,EAAOyB,EAAKC,CAAG,EACxBlL,EAAOsL,EAAMvC,CAAC,EAAIyC,EAAO,EACzBxL,EAAOuL,EAAMxC,CAAC,EAAIyC,EAAO,CAC5B,KACI,CACD,IAAIA,EAASzB,EAAO/J,EAAOmL,EAAKpC,CAAC,EAAG/I,EAAOsL,EAAMvC,CAAC,CAAC,EACnDgC,EAAMS,EAAO,EACbP,EAAMO,EAAO,EACbA,EAASzB,EAAO/J,EAAOqL,EAAMtC,CAAC,EAAG/I,EAAOuL,EAAMxC,CAAC,CAAC,EAChDiC,EAAMQ,EAAO,EACbN,EAAMM,EAAO,EACbA,EAASzB,EAAOgB,EAAKC,CAAG,EACxBhL,EAAOmL,EAAKpC,CAAC,EAAIyC,EAAO,EACxBxL,EAAOqL,EAAMtC,CAAC,EAAIyC,EAAO,EACzBA,EAASzB,EAAOkB,EAAKC,CAAG,EACxBlL,EAAOsL,EAAMvC,CAAC,EAAIyC,EAAO,EACzBxL,EAAOuL,EAAMxC,CAAC,EAAIyC,EAAO,CAC5B,CACJ,CACD,GAAItB,EAAKtC,EAAG,CACR,MAAM0D,EAAMH,EAAKR,EACjB,IAAIa,EACAjB,EACAiB,EAAShC,EAAOxJ,EAAOmL,EAAKpC,CAAC,EAAG/I,EAAOsL,EAAMvC,CAAC,CAAC,EAG/CyC,EAASzB,EAAO/J,EAAOmL,EAAKpC,CAAC,EAAG/I,EAAOsL,EAAMvC,CAAC,CAAC,EAEnDgC,EAAMS,EAAO,EACbxL,EAAOsL,EAAMvC,CAAC,EAAIyC,EAAO,EACzBxL,EAAOmL,EAAKpC,CAAC,EAAIgC,CACpB,CACJ,CACD,GAAIX,EAAKxC,EAAG,CACR,IAAIuD,EAAKV,EACT,MAAMW,EAAKX,EAAKN,GAAMD,EAAKM,GAC3B,KAAOW,GAAMC,EAAID,GAAML,EAAK,CACxB,MAAMO,EAAMF,EAAKN,EACjB,IAAIW,EACAjB,EACAiB,EAAShC,EAAOxJ,EAAOmL,EAAKpC,CAAC,EAAG/I,EAAOqL,EAAMtC,CAAC,CAAC,EAG/CyC,EAASzB,EAAO/J,EAAOmL,EAAKpC,CAAC,EAAG/I,EAAOqL,EAAMtC,CAAC,CAAC,EAEnDgC,EAAMS,EAAO,EACbxL,EAAOqL,EAAMtC,CAAC,EAAIyC,EAAO,EACzBxL,EAAOmL,EAAKpC,CAAC,EAAIgC,CACpB,CACJ,CACDP,EAAK5C,EACLA,IAAM,CACT,CACD,OAAO6C,CACX,CAEO,SAASgB,GAASxF,EAAKyF,EAAMC,EAAO,CACvC,QAASxF,EAAI,EAAGA,EAAIwF,EAAO,EAAExF,EACzBuF,EAAKvF,CAAC,EAAIF,EAAIyF,EAAKvF,CAAC,CAAC,CAE7B,CCnYO,SAASyF,GAAgBpH,EAAQ,CACpC,IAAIpC,EAAOoC,EAAO,WAClB,MAAMG,EAAM,IAAI,MAChB,IAAIiD,EAAI,EACR,MAAMiE,EAAS,IAAI,SAASrH,CAAM,EAClC,KAAOpC,EAAO,GAAG,CACb,MAAM0F,EAAI+D,EAAO,QAAQjE,GAAG,EAC5B,GAAIE,EAAI,EAAG,CACP,MAAMgE,EAAQ,CAAChE,EACf1F,GAAQ0J,EAAQ,EAChB,QAAS3F,EAAI,EAAGA,EAAI2F,EAAO3F,IACvBxB,EAAI,KAAKkH,EAAO,SAASjE,GAAG,CAAC,CAEpC,KACI,CACD,MAAMkE,EAAQhE,EACd1F,GAAQ,EACR,MAAMnB,EAAQ4K,EAAO,SAASjE,GAAG,EACjC,QAAS,EAAI,EAAG,EAAIkE,EAAQ,EAAG,IAC3BnH,EAAI,KAAK1D,CAAK,CAErB,CACJ,CACD,OAAO0D,CACX,CChFO,SAASoH,GAAcC,EAAS,CACnC,OAAO,IAAI,SAASA,EAAQ,MAAM,OAAQA,EAAQ,OAAO,MAAOA,EAAQ,IAAI,CAChF,CAMO,SAASC,GAAcD,EAAS,CACnC,MAAME,EAAaF,EAAQ,OAAO,OAAO,MAAMA,EAAQ,OAAO,MAAOA,EAAQ,OAAO,MAAQA,EAAQ,IAAI,EAClGG,EAAY,IAAI,WAAWP,GAAgBM,CAAU,CAAC,EACtDE,EAAY,IAAI,WAAWD,EAAU,MAAM,EACjD,OAAA5H,GAAU4H,CAAS,EACnBzH,GAAiByH,EAAWC,CAAS,EAC9B,IAAI,SAASA,EAAU,MAAM,CACxC,CAMO,SAASC,GAAcL,EAAS,CACnC,MAAME,EAAaF,EAAQ,MAAM,MAAMA,EAAQ,OAAO,MAAOA,EAAQ,OAAO,MAAQA,EAAQ,IAAI,EAC1FG,EAAY,OAAO,WAAWD,CAAU,EACxCE,EAAY,IAAI,WAAWD,EAAU,MAAM,EACjD,OAAA5H,GAAU4H,CAAS,EACnBzH,GAAiByH,EAAWC,CAAS,EAC9B,IAAI,SAASA,EAAU,MAAM,CACxC,CAMO,SAASE,GAAcN,EAAS,CACnC,MAAME,EAAaF,EAAQ,MAAM,MAAMA,EAAQ,OAAO,MAAOA,EAAQ,OAAO,MAAQA,EAAQ,IAAI,EAC1FG,EAAY,OAAO,WAAWD,CAAU,EACxCK,EAAKP,EAAQ,MAAQA,EAAQ,SAAWA,EAAQ,MAChDI,EAAYJ,EAAQ,MAAQ,EAAI,IAAI,YAAYO,CAAE,EAAI,IAAI,YAAYA,CAAE,EAC9E,IAAIC,EAAe,EACfC,EAAW,EACf,MAAMC,EAAM,IAAI,MAAM,CAAC,EACvB,QAASnK,EAAI,EAAGA,EAAIyJ,EAAQ,MAAOzJ,IAC/B,QAASkE,EAAI,EAAGA,EAAIuF,EAAQ,SAAUvF,IAAK,CACvC,IAAIkG,EAAQ,EACZ,OAAQX,EAAQ,KAAI,CAChB,IAAK,GACDU,EAAI,CAAC,EAAIF,EACTE,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAIV,EAAQ,MAC1BQ,EAAeE,EAAI,CAAC,EAAIV,EAAQ,MAChC,QAASjD,EAAI,EAAGA,EAAIiD,EAAQ,MAAO,EAAEjD,EAAG,CACpC,MAAM6D,EAAQT,EAAUO,EAAI,CAAC,GAAG,GAAK,EAAKP,EAAUO,EAAI,CAAC,GAAG,EAC5DC,GAASC,EACTR,EAAUK,CAAQ,EAAIE,EACtBF,GACH,CACD,MACJ,IAAK,GACDC,EAAI,CAAC,EAAIF,EACTE,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAIV,EAAQ,MAC1BU,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAIV,EAAQ,MAC1BQ,EAAeE,EAAI,CAAC,EAAIV,EAAQ,MAChC,QAASjD,EAAI,EAAGA,EAAIiD,EAAQ,MAAO,EAAEjD,EAAG,CACpC,MAAM6D,EAAQT,EAAUO,EAAI,CAAC,GAAG,GAAK,GAAOP,EAAUO,EAAI,CAAC,GAAG,GAAK,GAAOP,EAAUO,EAAI,CAAC,GAAG,GAAK,EACjGC,GAASC,EACTR,EAAUK,CAAQ,EAAIE,EACtBF,GACH,CACD,KACP,CACJ,CAEL,OAAO,IAAI,SAASL,EAAU,MAAM,CACxC,CAMO,SAASS,GAAcb,EAAS,CACnC,MAAMc,EAAad,EAAQ,OACrBe,EAAW,CAAE,MAAOf,EAAQ,OAAO,KAAK,EACxCjF,EAAY,IAAI,YAAYiF,EAAQ,MAAQA,EAAQ,mBAAqBA,EAAQ,SAAWA,EAAQ,KAAK,EACzGhG,EAAS,IAAI,WAAW7G,CAAW,EAEzC,IAAI6N,EAAe,EACnB,MAAMC,EAAiB,IAAI,MAAMjB,EAAQ,QAAQ,EACjD,QAAS7F,EAAI,EAAGA,EAAI6F,EAAQ,SAAU7F,IAClC8G,EAAe9G,CAAC,EAAI,GACpB8G,EAAe9G,CAAC,EAAE,MAAW6G,EAC7BC,EAAe9G,CAAC,EAAE,IAAS8G,EAAe9G,CAAC,EAAE,MAC7C8G,EAAe9G,CAAC,EAAE,GAAQ6F,EAAQ,MAClCiB,EAAe9G,CAAC,EAAE,GAAQ6F,EAAQ,MAClCiB,EAAe9G,CAAC,EAAE,KAAU6F,EAAQ,KACpCgB,GAAgBC,EAAe9G,CAAC,EAAE,GAAK8G,EAAe9G,CAAC,EAAE,GAAK8G,EAAe9G,CAAC,EAAE,KAGpF,MAAM+G,EAAa9L,EAAY0L,EAAYC,CAAQ,EAC7CI,EAAa/L,EAAY0L,EAAYC,CAAQ,EACnD,GAAII,GAAchO,EACd,MAAM,IAAI,MAAM,mCAAmC,EAEvD,GAAI+N,GAAcC,EACd,QAAShH,EAAI,EAAGA,EAAIgH,EAAaD,EAAa,EAAG/G,IAC7CH,EAAOG,EAAI+G,CAAU,EAAI/L,EAAW2L,EAAYC,CAAQ,EAIhE,MAAM9G,EAAM,IAAI,YAAY/G,CAAY,EAClCkO,EAAWrH,GAAqBC,EAAQC,CAAG,EAC3CoH,EAASnM,EAAY4L,EAAYC,CAAQ,EAE/C/D,GAAcgD,EAAQ,MAAOc,EAAYC,EAAUM,EAAQtG,EAAWiG,CAAY,EAElF,QAAS7G,EAAI,EAAGA,EAAI6F,EAAQ,SAAU,EAAE7F,EAAG,CACvC,MAAMmH,EAAKL,EAAe9G,CAAC,EAC3B,QAAS4C,EAAI,EAAGA,EAAIkE,EAAe9G,CAAC,EAAE,KAAM,EAAE4C,EAC1CkB,GAAWlD,EAAWuG,EAAG,MAAQvE,EAAGuE,EAAG,GAAIA,EAAG,KAAMA,EAAG,GAAIA,EAAG,GAAKA,EAAG,KAAMF,CAAQ,CAE3F,CAED3B,GAASxF,EAAKc,EAAWiG,CAAY,EAErC,IAAIO,EAAY,EAChB,MAAMnB,EAAY,IAAI,WAAWrF,EAAU,OAAO,UAAU,EAC5D,QAASxE,EAAI,EAAGA,EAAIyJ,EAAQ,MAAOzJ,IAC/B,QAASkE,EAAI,EAAGA,EAAIuF,EAAQ,SAAUvF,IAAK,CACvC,MAAM6G,EAAKL,EAAexG,CAAC,EACrBL,EAAIkH,EAAG,GAAKA,EAAG,KACfE,EAAK,IAAI,WAAWzG,EAAU,OAAQuG,EAAG,IAAMtO,EAAYoH,EAAIpH,CAAU,EAC/EoN,EAAU,IAAIoB,EAAID,CAAS,EAC3BA,GAAanH,EAAIpH,EACjBsO,EAAG,KAAOlH,CACb,CAEL,OAAO,IAAI,SAASgG,EAAU,MAAM,CACxC,CCjJO,IAAIqB,GACV,SAAUA,EAAe,CACtBA,EAAcA,EAAc,MAAW,CAAC,EAAI,QAC5CA,EAAcA,EAAc,UAAe,CAAC,EAAI,WACpD,GAAGA,IAAkBA,EAAgB,CAAE,EAAC,EAIjC,MAAMC,CAA6B,CAC1C,CAIAA,EAA6B,kBAAoBD,EAAc,UAI/DC,EAA6B,UAAY,iCC6DlC,eAAeC,GAAmBC,EAAQ5M,EAAUL,EAAQkN,EAAY,CAC3E,MAAM7B,EAAU,CACZ,KAAM,EACN,OAAQhL,EACR,MAAO,IAAI,WAAWA,EAAS,MAAM,EACrC,OAAQL,EACR,MAAOiN,EAAO,WAAW,KAAOA,EAAO,WAAW,KAAO,EACzD,OAAQA,EAAO,WAAW,KAAOA,EAAO,WAAW,KAAO,EAC1D,SAAUA,EAAO,SAAS,OAC1B,mBAAoB,CAAE,EACtB,UAAW,IAAM,EACjB,aAAc,EACd,aAAc,EACd,MAAO,EACP,kBAAmB,EACnB,UAAW,KACX,KAAM,EACN,WAAY,KACZ,OAAQ,IAAM,EACd,OAAQ,EACR,eAAgB,EAChB,eAAgB,CAAE,EAClB,WAAY,KACZ,UAAW,KACX,YAAa,GACb,YAAa,CACrB,EACI,OAAQA,EAAO,YAAW,CACtB,KAAKhO,EAAiB,eAClBoM,EAAQ,MAAQ,EAChBA,EAAQ,WAAaD,GACrB,MACJ,KAAKnM,EAAiB,gBAClBoM,EAAQ,MAAQ,EAChBA,EAAQ,WAAaC,GACrB,MACJ,KAAKrM,EAAiB,iBAClBoM,EAAQ,MAAQ,EAChBA,EAAQ,WAAaK,GACrB,MAAMyB,EAAM,gBAAgBJ,EAA6B,SAAS,EAClE,MACJ,KAAK9N,EAAiB,gBAClBoM,EAAQ,MAAQ,GAChBA,EAAQ,WAAaK,GACrB,MAAMyB,EAAM,gBAAgBJ,EAA6B,SAAS,EAClE,MACJ,KAAK9N,EAAiB,gBAClBoM,EAAQ,MAAQ,GAChBA,EAAQ,WAAaa,GACrB,MACJ,KAAKjN,EAAiB,kBAClBoM,EAAQ,MAAQ,GAChBA,EAAQ,WAAaM,GACrB,MAAMwB,EAAM,gBAAgBJ,EAA6B,SAAS,EAClE,MACJ,QACI,MAAM,IAAI,MAAM9N,EAAiBgO,EAAO,WAAW,EAAI,iBAAiB,CAC/E,CACD5B,EAAQ,kBAAoBA,EAAQ,MACpC,MAAMlJ,EAAW,CAAA,EACjB,UAAWiL,KAAWH,EAAO,SACzB,OAAQG,EAAQ,KAAI,CAChB,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACDjL,EAASiL,EAAQ,IAAI,EAAI,GACzB/B,EAAQ,KAAO+B,EAAQ,SAC9B,CAGL,IAAIC,EAAY,GAChB,GAAIlL,EAAS,GAAKA,EAAS,GAAKA,EAAS,EACrCkL,EAAY,CAAClL,EAAS,EACtBkJ,EAAQ,eAAiB,EACzBA,EAAQ,eAAiB,CAAE,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,UAE5ClJ,EAAS,EACdkJ,EAAQ,eAAiB,EACzBA,EAAQ,eAAiB,CAAE,EAAG,CAAC,MAG/B,OAAM,IAAI,MAAM,2DAA2D,EAE/E,GAAIA,EAAQ,OAAS,EAEjB,OAAQ6B,EAAU,CACd,KAAKJ,EAAc,MACfzB,EAAQ,OAAStK,GACjBsK,EAAQ,UAAYhN,EACpB,MACJ,KAAKyO,EAAc,UACfzB,EAAQ,OAAS5K,EACjB4K,EAAQ,UAAYhN,EACpB,KACP,SAEIgN,EAAQ,OAAS,EAEtB,OAAQ6B,EAAU,CACd,KAAKJ,EAAc,MACfzB,EAAQ,OAASvK,EACjBuK,EAAQ,UAAYlN,EACpB,MACJ,KAAK2O,EAAc,UACfzB,EAAQ,OAAS9J,GACjB8J,EAAQ,UAAYlN,CAC3B,KAGD,OAAM,IAAI,MAAM,yBAA2BkN,EAAQ,KAAO,QAAU4B,EAAO,WAAW,EAE1F5B,EAAQ,WAAaA,EAAQ,OAASA,EAAQ,kBAC9C,QAAS7F,EAAI,EAAGA,EAAI6F,EAAQ,WAAY7F,IACpC5E,GAAWP,EAAUL,CAAM,EAG/B,MAAMyB,EAAO4J,EAAQ,MAAQA,EAAQ,OAASA,EAAQ,eACtD,OAAQ6B,EAAU,CACd,KAAKJ,EAAc,MACfzB,EAAQ,UAAY,IAAI,aAAa5J,CAAI,EACzC4J,EAAQ,YAAc,EAElBgC,GACAhC,EAAQ,UAAU,KAAK,EAAG,EAAG5J,CAAI,EAErC,MACJ,KAAKqL,EAAc,UACfzB,EAAQ,UAAY,IAAI,YAAY5J,CAAI,EACxC4J,EAAQ,YAAc,EAClBgC,GACAhC,EAAQ,UAAU,KAAK,MAAQ,EAAG5J,CAAI,EAE1C,MACJ,QACI,MAAM,IAAI,MAAM,qBAAuByL,CAAU,CACxD,CACD,IAAII,EAAa,EACjB,UAAWF,KAAWH,EAAO,SACrB5B,EAAQ,eAAe+B,EAAQ,IAAI,IAAM,SACzC/B,EAAQ,mBAAmB+B,EAAQ,IAAI,EAAIE,EAAajC,EAAQ,OAEpEiC,GAAcF,EAAQ,UAAY,EAEtC,OAAA/B,EAAQ,aAAeA,EAAQ,MAAQiC,EACvCjC,EAAQ,aAAeA,EAAQ,MAAQA,EAAQ,eAC3C4B,EAAO,YAAc,eACrB5B,EAAQ,UAAazJ,GAAMA,EAG3ByJ,EAAQ,UAAazJ,GAAMyJ,EAAQ,OAAS,EAAIzJ,EAEhDyJ,EAAQ,gBAAkB,GAC1BA,EAAQ,OAAS,EACjBA,EAAQ,YAAc,KAGtBA,EAAQ,OAAS,EACjBA,EAAQ,YAAc,IAEnBA,CACX,CAQO,SAASkC,GAASlC,EAAS4B,EAAQ5M,EAAUL,EAAQ,CACxD,MAAM4M,EAAY,CAAE,MAAO,GAC3B,QAASY,EAAmB,EAAGA,EAAmBnC,EAAQ,OAASA,EAAQ,kBAAmBmC,IAAoB,CAC9G,MAAMC,EAAOrN,EAAWC,EAAUL,CAAM,EAAIiN,EAAO,WAAW,KAC9D5B,EAAQ,KAAO9K,EAAYF,EAAUL,CAAM,EAC3CqL,EAAQ,MAAQoC,EAAOpC,EAAQ,kBAAoBA,EAAQ,OAASA,EAAQ,OAASoC,EAAOpC,EAAQ,kBAEpG,MAAMqC,EADerC,EAAQ,KAAOA,EAAQ,MAAQA,EAAQ,cAC7BA,EAAQ,WAAaA,EAAQ,WAAWA,CAAO,EAAID,GAAcC,CAAO,EACvGrL,EAAO,OAASqL,EAAQ,KACxB,QAASsC,EAAS,EAAGA,EAAStC,EAAQ,kBAAmBsC,IAAU,CAC/D,MAAMC,EAASJ,EAAmBnC,EAAQ,kBACpCwC,EAASF,EAAStC,EAAQ,UAAUuC,CAAM,EAChD,GAAIC,GAAUxC,EAAQ,OAClB,SAEJ,MAAMyC,EAAaH,EAAStC,EAAQ,aAC9B0C,GAAiB1C,EAAQ,OAAS,EAAIwC,GAAUxC,EAAQ,aAC9D,QAAS2C,EAAY,EAAGA,EAAY3C,EAAQ,SAAU2C,IAAa,CAC/D,MAAM5L,EAAO6K,EAAO,SAASe,CAAS,EAAE,KAClCC,EAAO5C,EAAQ,mBAAmBjJ,CAAI,EACtC8L,EAAO7C,EAAQ,eAAejJ,CAAI,EACxC,GAAI8L,IAAS,OAGb,CAAAtB,EAAU,MAAQkB,EAAaG,EAC/B,QAAStM,EAAI,EAAGA,EAAI0J,EAAQ,MAAO1J,IAAK,CACpC,MAAMwM,EAAWJ,EAAgBpM,EAAI0J,EAAQ,eAAiB6C,EAC1D7C,EAAQ,YACRA,EAAQ,UAAU8C,CAAQ,EAAI9C,EAAQ,OAAOqC,EAAQd,CAAS,EAErE,EACJ,CACJ,CACJ,CACL,CC9MO,MAAMwB,EAAkB,CAC3B,aAAc,CAIV,KAAK,gBAAkB,EAC1B,CAUD,aAAaC,EAAOC,EAAUC,EAAoBC,EAASC,EAAU,CAEjE,KAAM,6BACT,CAOD,MAAM,SAAS1D,EAAM2D,EAASC,EAAU,CACpC,MAAMtO,EAAW,IAAI,SAAS0K,EAAK,MAAM,EACnC/K,EAAS,CAAE,MAAO,GAClBiN,EAAS3I,GAAajE,EAAUL,CAAM,EACtCqL,EAAU,MAAM2B,GAAmBC,EAAQ5M,EAAUL,EAAQ+M,EAA6B,iBAAiB,EACjHQ,GAASlC,EAAS4B,EAAQ5M,EAAUL,CAAM,EAE1C,MAAM4O,EAAQ3B,EAAO,WAAW,KAAOA,EAAO,WAAW,KAAO,EAC1D4B,EAAS5B,EAAO,WAAW,KAAOA,EAAO,WAAW,KAAO,EACjE0B,EAASC,EAAOC,EAAQH,EAAQ,gBAAiB,GAAO,IAAM,CAC1D,MAAMI,EAASJ,EAAQ,YACvBA,EAAQ,OAASzB,EAAO,OACxByB,EAAQ,KAAOrD,EAAQ,YACvBqD,EAAQ,QAAU,GAClBA,EAAQ,YAAc,CAACzB,EAAO,YAC1B5B,EAAQ,WACRyD,EAAO,6BAA6BJ,EAASrD,EAAQ,UAAW,EAAG,EAAG,OAAW,EAAI,CAErG,CAAS,CACJ,CACL", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8]}