{"version": 3, "file": "Index.C3gJvtqi.js", "sources": ["../../../../../../../tabitem/shared/TabItem.svelte", "../../../../../../../tabitem/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { getContext, onMount, createEventDispatcher, tick } from \"svelte\";\n\timport { TABS } from \"@gradio/tabs\";\n\timport Column from \"@gradio/column\";\n\timport type { SelectData } from \"@gradio/utils\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let label: string;\n\texport let id: string | number | object = {};\n\texport let visible: boolean;\n\texport let interactive: boolean;\n\texport let order: number;\n\texport let scale: number;\n\n\tconst dispatch = createEventDispatcher<{ select: SelectData }>();\n\n\tconst { register_tab, unregister_tab, selected_tab, selected_tab_index } =\n\t\tgetContext(TABS) as any;\n\n\tlet tab_index: number;\n\n\t$: tab_index = register_tab(\n\t\t{ label, id, elem_id, visible, interactive, scale },\n\t\torder\n\t);\n\n\tonMount(() => {\n\t\treturn (): void => unregister_tab({ label, id, elem_id }, order);\n\t});\n\n\t$: $selected_tab_index === tab_index &&\n\t\ttick().then(() => dispatch(\"select\", { value: label, index: tab_index }));\n</script>\n\n{#if $selected_tab === id && visible}\n\t<div\n\t\tid={elem_id}\n\t\tclass=\"tabitem {elem_classes.join(' ')}\"\n\t\tclass:grow-children={scale >= 1}\n\t\tstyle:display={$selected_tab === id && visible ? \"flex\" : \"none\"}\n\t\tstyle:flex-grow={scale}\n\t\trole=\"tabpanel\"\n\t>\n\t\t<Column scale={scale >= 1 ? scale : null}>\n\t\t\t<slot />\n\t\t</Column>\n\t</div>\n{/if}\n\n<style>\n\tdiv {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tposition: relative;\n\t\tborder: none;\n\t\tborder-radius: var(--radius-sm);\n\t\tpadding: var(--block-padding);\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t}\n\t.grow-children > :global(.column > .column) {\n\t\tflex-grow: 1;\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseTabItem } from \"./shared/TabItem.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport TabItem from \"./shared/TabItem.svelte\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let label: string;\n\texport let id: string | number;\n\texport let gradio:\n\t\t| Gradio<{\n\t\t\t\tselect: SelectData;\n\t\t  }>\n\t\t| undefined;\n\texport let visible = true;\n\texport let interactive = true;\n\texport let order: number;\n\texport let scale: number;\n</script>\n\n<TabItem\n\t{elem_id}\n\t{elem_classes}\n\t{label}\n\t{visible}\n\t{interactive}\n\t{id}\n\t{order}\n\t{scale}\n\ton:select={({ detail }) => gradio?.dispatch(\"select\", detail)}\n>\n\t{#if visible}\n\t\t<slot />\n\t{/if}\n</TabItem>\n"], "names": ["ctx", "toggle_class", "div", "insert_hydration", "target", "anchor", "dirty", "column_changes", "create_if_block", "elem_id", "$$props", "elem_classes", "label", "id", "visible", "interactive", "order", "scale", "dispatch", "createEventDispatcher", "register_tab", "unregister_tab", "selected_tab", "selected_tab_index", "getContext", "TABS", "tab_index", "onMount", "$$invalidate", "$selected_tab_index", "tick", "gradio", "select_handler", "detail"], "mappings": "muBA4CiB,MAAAA,EAAS,CAAA,GAAA,EAAIA,KAAQ,uMAPhCA,EAAO,CAAA,CAAA,2BACKA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,gBAAA,yBAChBC,EAAAC,EAAA,gBAAAF,MAAS,CAAC,gBAChBA,EAAa,CAAA,IAAKA,EAAE,CAAA,GAAIA,EAAO,CAAA,EAAG,OAAS,MAAM,kBAC/CA,EAAK,CAAA,CAAA,UALvBG,EAWKC,EAAAF,EAAAG,CAAA,sCAHWC,EAAA,KAAAC,EAAA,MAAAP,EAAS,CAAA,GAAA,EAAIA,KAAQ,yEAPhCA,EAAO,CAAA,CAAA,8BACKA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,+CAChBC,EAAAC,EAAA,gBAAAF,MAAS,CAAC,sBAChBA,EAAa,CAAA,IAAKA,EAAE,CAAA,GAAIA,EAAO,CAAA,EAAG,OAAS,MAAM,wBAC/CA,EAAK,CAAA,CAAA,wXANnBA,EAAa,CAAA,IAAKA,EAAE,CAAA,GAAIA,EAAO,CAAA,GAAAQ,EAAAR,CAAA,8FAA/BA,EAAa,CAAA,IAAKA,EAAE,CAAA,GAAIA,EAAO,CAAA,+NA7BxB,QAAAS,EAAU,EAAA,EAAAC,EACV,CAAA,aAAAC,EAAA,EAAA,EAAAD,EACA,CAAA,MAAAE,CAAA,EAAAF,EACA,CAAA,GAAAG,EAAA,EAAA,EAAAH,EACA,CAAA,QAAAI,CAAA,EAAAJ,EACA,CAAA,YAAAK,CAAA,EAAAL,EACA,CAAA,MAAAM,CAAA,EAAAN,EACA,CAAA,MAAAO,CAAA,EAAAP,QAELQ,EAAWC,IAET,CAAA,aAAAC,EAAc,eAAAC,EAAgB,aAAAC,EAAc,mBAAAC,GACnDC,EAAWC,CAAI,yCAEZ,IAAAC,EAOJ,OAAAC,EAAA,QACoBN,EAAiB,CAAA,MAAAT,EAAO,GAAAC,EAAI,QAAAJ,CAAA,EAAWO,CAAK,gVANhEY,EAAA,GAAGF,EAAYN,GACZ,MAAAR,EAAO,GAAAC,EAAI,QAAAJ,EAAS,QAAAK,EAAS,YAAAC,EAAa,MAAAE,GAC5CD,qBAOEa,IAAwBH,GAC1BI,IAAO,KAAA,IAAWZ,EAAS,SAAY,CAAA,MAAON,EAAO,MAAOc,CAAA,CAAA,CAAA,0dCExD1B,EAAO,CAAA,GAAAQ,EAAAR,CAAA,4FAAPA,EAAO,CAAA,2zBA1BD,QAAAS,EAAU,EAAA,EAAAC,EACV,CAAA,aAAAC,EAAA,EAAA,EAAAD,EACA,CAAA,MAAAE,CAAA,EAAAF,EACA,CAAA,GAAAG,CAAA,EAAAH,EACA,CAAA,OAAAqB,CAAA,EAAArB,GAKA,QAAAI,EAAU,EAAA,EAAAJ,GACV,YAAAK,EAAc,EAAA,EAAAL,EACd,CAAA,MAAAM,CAAA,EAAAN,EACA,CAAA,MAAAO,CAAA,EAAAP,EAYG,MAAAsB,EAAA,CAAA,CAAA,OAAAC,KAAaF,GAAA,YAAAA,EAAQ,SAAS,SAAUE"}