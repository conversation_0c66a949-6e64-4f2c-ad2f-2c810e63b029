// qsslconfiguration.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_SSL)

class QSslConfiguration
{
%TypeHeaderCode
#include <qsslconfiguration.h>
%End

public:
    QSslConfiguration();
    QSslConfiguration(const QSslConfiguration &other);
    ~QSslConfiguration();
    bool isNull() const;
    QSsl::SslProtocol protocol() const;
    void setProtocol(QSsl::SslProtocol protocol);
    QSslSocket::PeerVerifyMode peerVerifyMode() const;
    void setPeerVerifyMode(QSslSocket::PeerVerifyMode mode);
    int peerVerifyDepth() const;
    void setPeerVerifyDepth(int depth);
    QSslCertificate localCertificate() const;
    void setLocalCertificate(const QSslCertificate &certificate);
    QSslCertificate peerCertificate() const;
    QList<QSslCertificate> peerCertificateChain() const;
    QSslCipher sessionCipher() const;
    QSslKey privateKey() const;
    void setPrivateKey(const QSslKey &key);
    QList<QSslCipher> ciphers() const;
    void setCiphers(const QString &ciphers);
    void setCiphers(const QList<QSslCipher> &ciphers);
    QList<QSslCertificate> caCertificates() const;
    void setCaCertificates(const QList<QSslCertificate> &certificates);
    static QSslConfiguration defaultConfiguration();
    static void setDefaultConfiguration(const QSslConfiguration &configuration);
    bool operator==(const QSslConfiguration &other) const;
    bool operator!=(const QSslConfiguration &other) const;
    void setSslOption(QSsl::SslOption option, bool on);
    bool testSslOption(QSsl::SslOption option) const;
    void swap(QSslConfiguration &other /Constrained/);
    QList<QSslCertificate> localCertificateChain() const;
    void setLocalCertificateChain(const QList<QSslCertificate> &localChain);
    QByteArray sessionTicket() const;
    void setSessionTicket(const QByteArray &sessionTicket);
    int sessionTicketLifeTimeHint() const;

    enum NextProtocolNegotiationStatus
    {
        NextProtocolNegotiationNone,
        NextProtocolNegotiationNegotiated,
        NextProtocolNegotiationUnsupported,
    };

    void setAllowedNextProtocols(const QList<QByteArray> &protocols);
    QList<QByteArray> allowedNextProtocols() const;
    QByteArray nextNegotiatedProtocol() const;
    QSslConfiguration::NextProtocolNegotiationStatus nextProtocolNegotiationStatus() const;
    QSsl::SslProtocol sessionProtocol() const;
    static QList<QSslCipher> supportedCiphers();
    static QList<QSslCertificate> systemCaCertificates();
    QList<QSslEllipticCurve> ellipticCurves() const;
    void setEllipticCurves(const QList<QSslEllipticCurve> &curves);
    static QList<QSslEllipticCurve> supportedEllipticCurves();
    QSslKey ephemeralServerKey() const;
    QByteArray preSharedKeyIdentityHint() const;
    void setPreSharedKeyIdentityHint(const QByteArray &hint);
    QSslDiffieHellmanParameters diffieHellmanParameters() const;
    void setDiffieHellmanParameters(const QSslDiffieHellmanParameters &dhparams);
    QMap<QByteArray, QVariant> backendConfiguration() const;
    void setBackendConfigurationOption(const QByteArray &name, const QVariant &value);
    void setBackendConfiguration(const QMap<QByteArray, QVariant> &backendConfiguration = QMap<QByteArray, QVariant>());
    void setOcspStaplingEnabled(bool enable);
    bool ocspStaplingEnabled() const;
    void addCaCertificate(const QSslCertificate &certificate);
    bool addCaCertificates(const QString &path, QSsl::EncodingFormat format = QSsl::Pem, QSslCertificate::PatternSyntax syntax = QSslCertificate::PatternSyntax::FixedString);
    void addCaCertificates(const QList<QSslCertificate> &certificates);
    bool handshakeMustInterruptOnError() const;
    void setHandshakeMustInterruptOnError(bool interrupt);
    bool missingCertificateIsFatal() const;
    void setMissingCertificateIsFatal(bool cannotRecover);
    static const char *NextProtocolHttp1_1 /Encoding="None",NoSetter/;
%If (Qt_6_3_0 -)
%If (PyQt_DTLS)
    bool dtlsCookieVerificationEnabled() const;
%End
%End
%If (Qt_6_3_0 -)
%If (PyQt_DTLS)
    void setDtlsCookieVerificationEnabled(bool enable);
%End
%End
%If (Qt_6_3_0 -)
%If (PyQt_DTLS)
    static QSslConfiguration defaultDtlsConfiguration();
%End
%End
%If (Qt_6_3_0 -)
%If (PyQt_DTLS)
    static void setDefaultDtlsConfiguration(const QSslConfiguration &configuration);
%End
%End
};

%End
