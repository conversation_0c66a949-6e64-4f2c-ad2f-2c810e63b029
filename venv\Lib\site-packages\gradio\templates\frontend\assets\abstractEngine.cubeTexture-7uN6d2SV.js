import{A as h,e as P,L as v,I as A,f as D,g as R,h as y}from"./index-Dpxo-yl_.js";h.prototype._partialLoadFile=function(a,u,t,e,o=null){const n=s=>{t[u]=s,t._internalCount++,t._internalCount===6&&e(t)},l=(s,r)=>{o&&s&&o(s.status+" "+s.statusText,r)};this._loadFile(a,n,void 0,void 0,!0,l)};h.prototype._cascadeLoadFiles=function(a,u,t,e=null){const o=[];o._internalCount=0;for(let n=0;n<6;n++)this._partialLoadFile(t[n],n,o,u,e)};h.prototype._cascadeLoadImgs=function(a,u,t,e,o=null,n){const l=[];l._internalCount=0;for(let s=0;s<6;s++)this._partialLoadImg(e[s],s,l,a,u,t,o,n)};h.prototype._partialLoadImg=function(a,u,t,e,o,n,l=null,s){const r=P();v(a,d=>{t[u]=d,t._internalCount++,e&&e.removePendingData(r),t._internalCount===6&&n&&n(o,t)},(d,c)=>{e&&e.removePendingData(r),l&&l(d,c)},e?e.offlineProvider:null,s),e&&e.addPendingData(r)};h.prototype.createCubeTextureBase=function(a,u,t,e,o=null,n=null,l,s=null,r=!1,g=0,T=0,d=null,c=null,m=null,C=!1,x=null){const i=d||new A(this,7);i.isCube=!0,i.url=a,i.generateMipMaps=!e,i._lodGenerationScale=g,i._lodGenerationOffset=T,i._useSRGBBuffer=!!C&&this._caps.supportSRGBBuffers&&(this.version>1||this.isWebGPU||!!e),i!==d&&(i.label=a.substring(0,60)),this._doNotHandleContextLost||(i._extension=s,i._files=t,i._buffer=x);const L=a;this._transformTextureUrl&&!d&&(a=this._transformTextureUrl(a));const G=s??D(a),w=R(G),I=(_,f)=>{a===L?n&&_&&n(_.status+" "+_.statusText,f):(y.Warn(`Failed to load ${a}, falling back to the ${L}`),this.createCubeTextureBase(L,u,t,!!e,o,n,l,s,r,g,T,i,c,m,C,x))};if(w)w.then(_=>{const f=p=>{c&&c(i,p),_.loadCubeData(p,i,r,o,n)};x?f(x):t&&t.length===6?_.supportCascades?this._cascadeLoadFiles(u,p=>f(p.map(F=>new Uint8Array(F))),t,n):n?n("Textures type does not support cascades."):y.Warn("Texture loader does not support cascades."):this._loadFile(a,p=>f(new Uint8Array(p)),void 0,void 0,!0,I)});else{if(!t||t.length===0)throw new Error("Cannot load cubemap because files were not defined, or the correct loader was not found.");this._cascadeLoadImgs(u,i,(_,f)=>{m&&m(_,f)},t,n)}return this._internalTexturesCache.push(i),i};
//# sourceMappingURL=abstractEngine.cubeTexture-7uN6d2SV.js.map
