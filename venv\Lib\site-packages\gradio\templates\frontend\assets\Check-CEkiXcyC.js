const{SvelteComponent:a,append:c,attr:e,detach:d,init:h,insert:p,noop:r,safe_not_equal:u,svg_element:s}=window.__gradio__svelte__internal;function w(i){let t,o;return{c(){t=s("svg"),o=s("path"),e(o,"d","M5 13L9 17L19 7"),e(o,"stroke","currentColor"),e(o,"stroke-width","1.5"),e(o,"stroke-linecap","round"),e(o,"stroke-linejoin","round"),e(t,"width","100%"),e(t,"height","100%"),e(t,"stroke-width","1.5"),e(t,"viewBox","0 0 24 24"),e(t,"fill","none"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"color","currentColor")},m(n,l){p(n,t,l),c(t,o)},p:r,i:r,o:r,d(n){n&&d(t)}}}class _ extends a{constructor(t){super(),h(this,t,null,w,u,{})}}export{_ as C};
//# sourceMappingURL=Check-CEkiXcyC.js.map
