{"version": 3, "file": "EXT_lights_ies.TfXTlsqD.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/EXT_lights_ies.js"], "sourcesContent": ["import { Vector3 } from \"@babylonjs/core/Maths/math.vector.js\";\nimport { Color3 } from \"@babylonjs/core/Maths/math.color.js\";\nimport { SpotLight } from \"@babylonjs/core/Lights/spotLight.js\";\nimport { Light } from \"@babylonjs/core/Lights/light.js\";\nimport { GLTFLoader, ArrayItem } from \"../glTFLoader.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nimport { Texture } from \"@babylonjs/core/Materials/Textures/texture.js\";\nconst NAME = \"EXT_lights_ies\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Vendor/EXT_lights_ies)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class EXT_lights_ies {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n        delete this._lights;\n    }\n    /** @internal */\n    onLoading() {\n        const extensions = this._loader.gltf.extensions;\n        if (extensions && extensions[this.name]) {\n            const extension = extensions[this.name];\n            this._lights = extension.lights;\n            ArrayItem.Assign(this._lights);\n        }\n    }\n    /**\n     * @internal\n     */\n    loadNodeAsync(context, node, assign) {\n        return GLTFLoader.LoadExtensionAsync(context, node, this.name, async (extensionContext, extension) => {\n            this._loader._allMaterialsDirtyRequired = true;\n            let babylonSpotLight;\n            let light;\n            const transformNode = await this._loader.loadNodeAsync(context, node, (babylonMesh) => {\n                light = ArrayItem.Get(extensionContext, this._lights, extension.light);\n                const name = light.name || babylonMesh.name;\n                this._loader.babylonScene._blockEntityCollection = !!this._loader._assetContainer;\n                babylonSpotLight = new SpotLight(name, Vector3.Zero(), Vector3.Backward(), 0, 1, this._loader.babylonScene);\n                babylonSpotLight.angle = Math.PI / 2;\n                babylonSpotLight.innerAngle = 0;\n                babylonSpotLight._parentContainer = this._loader._assetContainer;\n                this._loader.babylonScene._blockEntityCollection = false;\n                light._babylonLight = babylonSpotLight;\n                babylonSpotLight.falloffType = Light.FALLOFF_GLTF;\n                babylonSpotLight.diffuse = extension.color ? Color3.FromArray(extension.color) : Color3.White();\n                babylonSpotLight.intensity = extension.multiplier || 1;\n                babylonSpotLight.range = Number.MAX_VALUE;\n                babylonSpotLight.parent = babylonMesh;\n                this._loader._babylonLights.push(babylonSpotLight);\n                GLTFLoader.AddPointerMetadata(babylonSpotLight, extensionContext);\n                assign(babylonMesh);\n            });\n            // Load the profile\n            let bufferData;\n            if (light.uri) {\n                bufferData = await this._loader.loadUriAsync(context, light, light.uri);\n            }\n            else {\n                const bufferView = ArrayItem.Get(`${context}/bufferView`, this._loader.gltf.bufferViews, light.bufferView);\n                bufferData = await this._loader.loadBufferViewAsync(`/bufferViews/${bufferView.index}`, bufferView);\n            }\n            babylonSpotLight.iesProfileTexture = new Texture(name + \"_iesProfile\", this._loader.babylonScene, true, false, undefined, null, null, bufferData, true, undefined, undefined, undefined, undefined, \".ies\");\n            return transformNode;\n        });\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new EXT_lights_ies(loader));\n//# sourceMappingURL=EXT_lights_ies.js.map"], "names": ["NAME", "EXT_lights_ies", "loader", "extensions", "extension", "ArrayItem", "context", "node", "assign", "GLTFLoader", "extensionContext", "babylonSpotLight", "light", "transformNode", "<PERSON><PERSON><PERSON><PERSON>", "name", "SpotLight", "Vector3", "Light", "Color3", "bufferData", "bufferView", "Texture", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "wMAOA,MAAMA,EAAO,iBAKN,MAAMC,CAAe,CAIxB,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EACZ,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,CACnD,CAED,SAAU,CACN,KAAK,QAAU,KACf,OAAO,KAAK,OACf,CAED,WAAY,CACR,MAAMG,EAAa,KAAK,QAAQ,KAAK,WACrC,GAAIA,GAAcA,EAAW,KAAK,IAAI,EAAG,CACrC,MAAMC,EAAYD,EAAW,KAAK,IAAI,EACtC,KAAK,QAAUC,EAAU,OACzBC,EAAU,OAAO,KAAK,OAAO,CAChC,CACJ,CAID,cAAcC,EAASC,EAAMC,EAAQ,CACjC,OAAOC,EAAW,mBAAmBH,EAASC,EAAM,KAAK,KAAM,MAAOG,EAAkBN,IAAc,CAClG,KAAK,QAAQ,2BAA6B,GAC1C,IAAIO,EACAC,EACJ,MAAMC,EAAgB,MAAM,KAAK,QAAQ,cAAcP,EAASC,EAAOO,GAAgB,CACnFF,EAAQP,EAAU,IAAIK,EAAkB,KAAK,QAASN,EAAU,KAAK,EACrE,MAAMW,EAAOH,EAAM,MAAQE,EAAY,KACvC,KAAK,QAAQ,aAAa,uBAAyB,CAAC,CAAC,KAAK,QAAQ,gBAClEH,EAAmB,IAAIK,EAAUD,EAAME,EAAQ,KAAI,EAAIA,EAAQ,SAAU,EAAE,EAAG,EAAG,KAAK,QAAQ,YAAY,EAC1GN,EAAiB,MAAQ,KAAK,GAAK,EACnCA,EAAiB,WAAa,EAC9BA,EAAiB,iBAAmB,KAAK,QAAQ,gBACjD,KAAK,QAAQ,aAAa,uBAAyB,GACnDC,EAAM,cAAgBD,EACtBA,EAAiB,YAAcO,EAAM,aACrCP,EAAiB,QAAUP,EAAU,MAAQe,EAAO,UAAUf,EAAU,KAAK,EAAIe,EAAO,MAAK,EAC7FR,EAAiB,UAAYP,EAAU,YAAc,EACrDO,EAAiB,MAAQ,OAAO,UAChCA,EAAiB,OAASG,EAC1B,KAAK,QAAQ,eAAe,KAAKH,CAAgB,EACjDF,EAAW,mBAAmBE,EAAkBD,CAAgB,EAChEF,EAAOM,CAAW,CAClC,CAAa,EAED,IAAIM,EACJ,GAAIR,EAAM,IACNQ,EAAa,MAAM,KAAK,QAAQ,aAAad,EAASM,EAAOA,EAAM,GAAG,MAErE,CACD,MAAMS,EAAahB,EAAU,IAAI,GAAGC,CAAO,cAAe,KAAK,QAAQ,KAAK,YAAaM,EAAM,UAAU,EACzGQ,EAAa,MAAM,KAAK,QAAQ,oBAAoB,gBAAgBC,EAAW,KAAK,GAAIA,CAAU,CACrG,CACD,OAAAV,EAAiB,kBAAoB,IAAIW,EAAQ,KAAO,cAAe,KAAK,QAAQ,aAAc,GAAM,GAAO,OAAW,KAAM,KAAMF,EAAY,GAAM,OAAW,OAAW,OAAW,OAAW,MAAM,EACnMP,CACnB,CAAS,CACJ,CACL,CACAU,EAAwBvB,CAAI,EAC5BwB,EAAsBxB,EAAM,GAAOE,GAAW,IAAID,EAAeC,CAAM,CAAC", "x_google_ignoreList": [0]}