{"version": 3, "file": "Checkbox.BBJAZbOw.js", "sources": ["../../../../../../../checkbox/shared/Checkbox.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\texport let value = false;\n\texport let label = \"Checkbox\";\n\texport let interactive: boolean;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: boolean;\n\t\tselect: SelectData;\n\t}>();\n\n\t// When the value changes, dispatch the change event via handle_change()\n\t// See the docs for an explanation: https://svelte.dev/docs/svelte-components#script-3-$-marks-a-statement-as-reactive\n\t$: value, dispatch(\"change\", value);\n\t$: disabled = !interactive;\n\n\tasync function handle_enter(\n\t\tevent: KeyboardEvent & { currentTarget: EventTarget & HTMLInputElement }\n\t): Promise<void> {\n\t\tif (event.key === \"Enter\") {\n\t\t\tvalue = !value;\n\t\t\tdispatch(\"select\", {\n\t\t\t\tindex: 0,\n\t\t\t\tvalue: event.currentTarget.checked,\n\t\t\t\tselected: event.currentTarget.checked\n\t\t\t});\n\t\t}\n\t}\n\n\tasync function handle_input(\n\t\tevent: Event & { currentTarget: EventTarget & HTMLInputElement }\n\t): Promise<void> {\n\t\tvalue = event.currentTarget.checked;\n\t\tdispatch(\"select\", {\n\t\t\tindex: 0,\n\t\t\tvalue: event.currentTarget.checked,\n\t\t\tselected: event.currentTarget.checked\n\t\t});\n\t}\n</script>\n\n<label class:disabled>\n\t<input\n\t\tbind:checked={value}\n\t\ton:keydown={handle_enter}\n\t\ton:input={handle_input}\n\t\t{disabled}\n\t\ttype=\"checkbox\"\n\t\tname=\"test\"\n\t\tdata-testid=\"checkbox\"\n\t/>\n\t<span>{label}</span>\n</label>\n\n<style>\n\tlabel {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\ttransition: var(--button-transition);\n\t\tcursor: pointer;\n\t\tcolor: var(--checkbox-label-text-color);\n\t\tfont-weight: var(--checkbox-label-text-weight);\n\t\tfont-size: var(--checkbox-label-text-size);\n\t\tline-height: var(--line-md);\n\t}\n\n\tlabel > * + * {\n\t\tmargin-left: var(--size-2);\n\t}\n\n\tinput {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\tbox-shadow: var(--checkbox-shadow);\n\t\tborder: 1px solid var(--checkbox-border-color);\n\t\tborder-radius: var(--checkbox-border-radius);\n\t\tbackground-color: var(--checkbox-background-color);\n\t\tline-height: var(--line-sm);\n\t}\n\n\tinput:checked,\n\tinput:checked:hover,\n\tinput:checked:focus {\n\t\tbackground-image: var(--checkbox-check);\n\t\tbackground-color: var(--checkbox-background-color-selected);\n\t\tborder-color: var(--checkbox-border-color-focus);\n\t}\n\n\tinput:checked:focus {\n\t\tbackground-image: var(--checkbox-check);\n\t\tbackground-color: var(--checkbox-background-color-selected);\n\t\tborder-color: var(--checkbox-border-color-focus);\n\t}\n\n\tinput:hover {\n\t\tborder-color: var(--checkbox-border-color-hover);\n\t\tbackground-color: var(--checkbox-background-color-hover);\n\t}\n\n\tinput:focus {\n\t\tborder-color: var(--checkbox-border-color-focus);\n\t\tbackground-color: var(--checkbox-background-color-focus);\n\t}\n\n\tinput[disabled],\n\t.disabled {\n\t\tcursor: not-allowed !important;\n\t}\n\n\tinput:hover {\n\t\tcursor: pointer;\n\t}\n</style>\n"], "names": ["ctx", "insert_hydration", "target", "label_1", "anchor", "append_hydration", "input", "span", "value", "$$props", "label", "interactive", "dispatch", "createEventDispatcher", "handle_enter", "event", "handle_input", "$$invalidate", "disabled"], "mappings": "seAqDQA,EAAK,CAAA,CAAA,kKAALA,EAAK,CAAA,CAAA,yPAVbC,EAWOC,EAAAC,EAAAC,CAAA,EAVNC,EAQCF,EAAAG,CAAA,YAPcN,EAAK,CAAA,SAQpBK,EAAmBF,EAAAI,CAAA,iDAPNP,EAAY,CAAA,CAAA,cACdA,EAAY,CAAA,CAAA,0DAFRA,EAAK,CAAA,YAQbA,EAAK,CAAA,CAAA,uFAjDD,MAAAQ,EAAQ,EAAA,EAAAC,GACR,MAAAC,EAAQ,UAAA,EAAAD,EACR,CAAA,YAAAE,CAAA,EAAAF,QAELG,EAAWC,mBAUFC,EACdC,EAAA,CAEIA,EAAM,MAAQ,cACjBP,EAAS,CAAAA,CAAA,EACTI,EAAS,SAAA,CACR,MAAO,EACP,MAAOG,EAAM,cAAc,QAC3B,SAAUA,EAAM,cAAc,0BAKlBC,EACdD,EAAA,CAEAE,EAAA,EAAAT,EAAQO,EAAM,cAAc,OAAA,EAC5BH,EAAS,SAAA,CACR,MAAO,EACP,MAAOG,EAAM,cAAc,QAC3B,SAAUA,EAAM,cAAc,uBAOjBP,EAAK,KAAA,kKA9BVI,EAAS,SAAUJ,CAAK,iBAClCS,EAAA,EAAGC,EAAY,CAAAP,CAAA"}