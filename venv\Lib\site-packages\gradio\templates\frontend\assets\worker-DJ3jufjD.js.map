{"version": 3, "file": "worker-DJ3jufjD.js", "sources": ["../../../node_modules/.pnpm/@ffmpeg+ffmpeg@0.12.7/node_modules/@ffmpeg/ffmpeg/dist/esm/const.js", "../../../node_modules/.pnpm/@ffmpeg+ffmpeg@0.12.7/node_modules/@ffmpeg/ffmpeg/dist/esm/errors.js", "../../../node_modules/.pnpm/@ffmpeg+ffmpeg@0.12.7/node_modules/@ffmpeg/ffmpeg/dist/esm/worker.js"], "sourcesContent": ["export const MIME_TYPE_JAVASCRIPT = \"text/javascript\";\nexport const MIME_TYPE_WASM = \"application/wasm\";\nexport const CORE_VERSION = \"0.12.1\";\nexport const CORE_URL = `https://unpkg.com/@ffmpeg/core@${CORE_VERSION}/dist/umd/ffmpeg-core.js`;\nexport var FFMessageType;\n(function (FFMessageType) {\n    FFMessageType[\"LOAD\"] = \"LOAD\";\n    FFMessageType[\"EXEC\"] = \"EXEC\";\n    FFMessageType[\"WRITE_FILE\"] = \"WRITE_FILE\";\n    FFMessageType[\"READ_FILE\"] = \"READ_FILE\";\n    FFMessageType[\"DELETE_FILE\"] = \"DELETE_FILE\";\n    FFMessageType[\"RENAME\"] = \"RENAME\";\n    FFMessageType[\"CREATE_DIR\"] = \"CREATE_DIR\";\n    FFMessageType[\"LIST_DIR\"] = \"LIST_DIR\";\n    FFMessageType[\"DELETE_DIR\"] = \"DELETE_DIR\";\n    FFMessageType[\"ERROR\"] = \"ERROR\";\n    FFMessageType[\"DOWNLOAD\"] = \"DOWNLOAD\";\n    FFMessageType[\"PROGRESS\"] = \"PROGRESS\";\n    FFMessageType[\"LOG\"] = \"LOG\";\n    FFMessageType[\"MOUNT\"] = \"MOUNT\";\n    FFMessageType[\"UNMOUNT\"] = \"UNMOUNT\";\n})(FFMessageType || (FFMessageType = {}));\n", "export const ERROR_UNKNOWN_MESSAGE_TYPE = new Error(\"unknown message type\");\nexport const ERROR_NOT_LOADED = new Error(\"ffmpeg is not loaded, call `await ffmpeg.load()` first\");\nexport const ERROR_TERMINATED = new Error(\"called FFmpeg.terminate()\");\nexport const ERROR_IMPORT_FAILURE = new Error(\"failed to import ffmpeg-core.js\");\n", "/// <reference no-default-lib=\"true\" />\n/// <reference lib=\"esnext\" />\n/// <reference lib=\"webworker\" />\nimport { CORE_URL, FFMessageType } from \"./const.js\";\nimport { ERROR_UNKNOWN_MESSAGE_TYPE, ERROR_NOT_LOADED, ERROR_IMPORT_FAILURE, } from \"./errors.js\";\nlet ffmpeg;\nconst load = async ({ coreURL: _coreURL = CORE_URL, wasmURL: _wasmURL, workerURL: _workerURL, }) => {\n    const first = !ffmpeg;\n    const coreURL = _coreURL;\n    const wasmURL = _wasmURL ? _wasmURL : _coreURL.replace(/.js$/g, \".wasm\");\n    const workerURL = _workerURL\n        ? _workerURL\n        : _coreURL.replace(/.js$/g, \".worker.js\");\n    try {\n        // when web worker type is `classic`.\n        importScripts(coreURL);\n    }\n    catch {\n        // when web worker type is `module`.\n        self.createFFmpegCore = (await import(\n        /* @vite-ignore */ coreURL)).default;\n        if (!self.createFFmpegCore) {\n            throw ERROR_IMPORT_FAILURE;\n        }\n    }\n    ffmpeg = await self.createFFmpegCore({\n        // Fix `Overload resolution failed.` when using multi-threaded ffmpeg-core.\n        // Encoded wasmURL and workerURL in the URL as a hack to fix locateFile issue.\n        mainScriptUrlOrBlob: `${coreURL}#${btoa(JSON.stringify({ wasmURL, workerURL }))}`,\n    });\n    ffmpeg.setLogger((data) => self.postMessage({ type: FFMessageType.LOG, data }));\n    ffmpeg.setProgress((data) => self.postMessage({\n        type: FFMessageType.PROGRESS,\n        data,\n    }));\n    return first;\n};\nconst exec = ({ args, timeout = -1 }) => {\n    ffmpeg.setTimeout(timeout);\n    ffmpeg.exec(...args);\n    const ret = ffmpeg.ret;\n    ffmpeg.reset();\n    return ret;\n};\nconst writeFile = ({ path, data }) => {\n    ffmpeg.FS.writeFile(path, data);\n    return true;\n};\nconst readFile = ({ path, encoding }) => ffmpeg.FS.readFile(path, { encoding });\n// TODO: check if deletion works.\nconst deleteFile = ({ path }) => {\n    ffmpeg.FS.unlink(path);\n    return true;\n};\nconst rename = ({ oldPath, newPath }) => {\n    ffmpeg.FS.rename(oldPath, newPath);\n    return true;\n};\n// TODO: check if creation works.\nconst createDir = ({ path }) => {\n    ffmpeg.FS.mkdir(path);\n    return true;\n};\nconst listDir = ({ path }) => {\n    const names = ffmpeg.FS.readdir(path);\n    const nodes = [];\n    for (const name of names) {\n        const stat = ffmpeg.FS.stat(`${path}/${name}`);\n        const isDir = ffmpeg.FS.isDir(stat.mode);\n        nodes.push({ name, isDir });\n    }\n    return nodes;\n};\n// TODO: check if deletion works.\nconst deleteDir = ({ path }) => {\n    ffmpeg.FS.rmdir(path);\n    return true;\n};\nconst mount = ({ fsType, options, mountPoint }) => {\n    let str = fsType;\n    let fs = ffmpeg.FS.filesystems[str];\n    if (!fs)\n        return false;\n    ffmpeg.FS.mount(fs, options, mountPoint);\n    return true;\n};\nconst unmount = ({ mountPoint }) => {\n    ffmpeg.FS.unmount(mountPoint);\n    return true;\n};\nself.onmessage = async ({ data: { id, type, data: _data }, }) => {\n    const trans = [];\n    let data;\n    try {\n        if (type !== FFMessageType.LOAD && !ffmpeg)\n            throw ERROR_NOT_LOADED;\n        switch (type) {\n            case FFMessageType.LOAD:\n                data = await load(_data);\n                break;\n            case FFMessageType.EXEC:\n                data = exec(_data);\n                break;\n            case FFMessageType.WRITE_FILE:\n                data = writeFile(_data);\n                break;\n            case FFMessageType.READ_FILE:\n                data = readFile(_data);\n                break;\n            case FFMessageType.DELETE_FILE:\n                data = deleteFile(_data);\n                break;\n            case FFMessageType.RENAME:\n                data = rename(_data);\n                break;\n            case FFMessageType.CREATE_DIR:\n                data = createDir(_data);\n                break;\n            case FFMessageType.LIST_DIR:\n                data = listDir(_data);\n                break;\n            case FFMessageType.DELETE_DIR:\n                data = deleteDir(_data);\n                break;\n            case FFMessageType.MOUNT:\n                data = mount(_data);\n                break;\n            case FFMessageType.UNMOUNT:\n                data = unmount(_data);\n                break;\n            default:\n                throw ERROR_UNKNOWN_MESSAGE_TYPE;\n        }\n    }\n    catch (e) {\n        self.postMessage({\n            id,\n            type: FFMessageType.ERROR,\n            data: e.toString(),\n        });\n        return;\n    }\n    if (data instanceof Uint8Array) {\n        trans.push(data.buffer);\n    }\n    self.postMessage({ id, type, data }, trans);\n};\n"], "names": ["CORE_URL", "FFMessageType", "ERROR_UNKNOWN_MESSAGE_TYPE", "ERROR_NOT_LOADED", "ERROR_IMPORT_FAILURE", "ffmpeg", "load", "_coreURL", "_wasmURL", "_workerURL", "first", "coreURL", "wasmURL", "workerURL", "data", "exec", "args", "timeout", "ret", "writeFile", "path", "readFile", "encoding", "deleteFile", "rename", "old<PERSON><PERSON>", "newPath", "createDir", "listDir", "names", "nodes", "name", "stat", "isDir", "deleteDir", "mount", "fsType", "options", "mountPoint", "str", "fs", "unmount", "id", "type", "_data", "trans", "e"], "mappings": "yBAGO,MAAMA,EAAW,gEACjB,IAAIC,GACV,SAAUA,EAAe,CACtBA,EAAc,KAAU,OACxBA,EAAc,KAAU,OACxBA,EAAc,WAAgB,aAC9BA,EAAc,UAAe,YAC7BA,EAAc,YAAiB,cAC/BA,EAAc,OAAY,SAC1BA,EAAc,WAAgB,aAC9BA,EAAc,SAAc,WAC5BA,EAAc,WAAgB,aAC9BA,EAAc,MAAW,QACzBA,EAAc,SAAc,WAC5BA,EAAc,SAAc,WAC5BA,EAAc,IAAS,MACvBA,EAAc,MAAW,QACzBA,EAAc,QAAa,SAC/B,GAAGA,IAAkBA,EAAgB,CAAA,EAAG,ECrBjC,MAAMC,EAA6B,IAAI,MAAM,sBAAsB,EAC7DC,EAAmB,IAAI,MAAM,wDAAwD,EAErFC,EAAuB,IAAI,MAAM,iCAAiC,ECE/E,IAAIC,EACJ,MAAMC,EAAO,MAAO,CAAE,QAASC,EAAWP,EAAU,QAASQ,EAAU,UAAWC,KAAkB,CAChG,MAAMC,EAAQ,CAACL,EACTM,EAAUJ,EACVK,EAAUJ,GAAsBD,EAAS,QAAQ,QAAS,OAAO,EACjEM,EAAYJ,GAEZF,EAAS,QAAQ,QAAS,YAAY,EAC5C,GAAI,CAEA,cAAcI,CAAO,CACxB,MACK,CAIF,GAFA,KAAK,kBAAoB,MAAM,OACZA,IAAU,QACzB,CAAC,KAAK,iBACN,MAAMP,CAEb,CACD,OAAAC,EAAS,MAAM,KAAK,iBAAiB,CAGjC,oBAAqB,GAAGM,CAAO,IAAI,KAAK,KAAK,UAAU,CAAE,QAAAC,EAAS,UAAAC,CAAW,CAAA,CAAC,CAAC,EACvF,CAAK,EACDR,EAAO,UAAWS,GAAS,KAAK,YAAY,CAAE,KAAMb,EAAc,IAAK,KAAAa,CAAI,CAAE,CAAC,EAC9ET,EAAO,YAAaS,GAAS,KAAK,YAAY,CAC1C,KAAMb,EAAc,SACpB,KAAAa,CACH,CAAA,CAAC,EACKJ,CACX,EACMK,EAAO,CAAC,CAAE,KAAAC,EAAM,QAAAC,EAAU,EAAE,IAAO,CACrCZ,EAAO,WAAWY,CAAO,EACzBZ,EAAO,KAAK,GAAGW,CAAI,EACnB,MAAME,EAAMb,EAAO,IACnB,OAAAA,EAAO,MAAK,EACLa,CACX,EACMC,EAAY,CAAC,CAAE,KAAAC,EAAM,KAAAN,MACvBT,EAAO,GAAG,UAAUe,EAAMN,CAAI,EACvB,IAELO,EAAW,CAAC,CAAE,KAAAD,EAAM,SAAAE,KAAejB,EAAO,GAAG,SAASe,EAAM,CAAE,SAAAE,CAAU,CAAA,EAExEC,EAAa,CAAC,CAAE,KAAAH,MAClBf,EAAO,GAAG,OAAOe,CAAI,EACd,IAELI,EAAS,CAAC,CAAE,QAAAC,EAAS,QAAAC,MACvBrB,EAAO,GAAG,OAAOoB,EAASC,CAAO,EAC1B,IAGLC,EAAY,CAAC,CAAE,KAAAP,MACjBf,EAAO,GAAG,MAAMe,CAAI,EACb,IAELQ,EAAU,CAAC,CAAE,KAAAR,KAAW,CAC1B,MAAMS,EAAQxB,EAAO,GAAG,QAAQe,CAAI,EAC9BU,EAAQ,CAAA,EACd,UAAWC,KAAQF,EAAO,CACtB,MAAMG,EAAO3B,EAAO,GAAG,KAAK,GAAGe,CAAI,IAAIW,CAAI,EAAE,EACvCE,EAAQ5B,EAAO,GAAG,MAAM2B,EAAK,IAAI,EACvCF,EAAM,KAAK,CAAE,KAAAC,EAAM,MAAAE,CAAO,CAAA,CAC7B,CACD,OAAOH,CACX,EAEMI,EAAY,CAAC,CAAE,KAAAd,MACjBf,EAAO,GAAG,MAAMe,CAAI,EACb,IAELe,EAAQ,CAAC,CAAE,OAAAC,EAAQ,QAAAC,EAAS,WAAAC,CAAU,IAAO,CAC/C,IAAIC,EAAMH,EACNI,EAAKnC,EAAO,GAAG,YAAYkC,CAAG,EAClC,OAAKC,GAELnC,EAAO,GAAG,MAAMmC,EAAIH,EAASC,CAAU,EAChC,IAFI,EAGf,EACMG,EAAU,CAAC,CAAE,WAAAH,MACfjC,EAAO,GAAG,QAAQiC,CAAU,EACrB,IAEX,KAAK,UAAY,MAAO,CAAE,KAAM,CAAE,GAAAI,EAAI,KAAAC,EAAM,KAAMC,CAAK,KAAU,CAC7D,MAAMC,EAAQ,CAAA,EACd,IAAI/B,EACJ,GAAI,CACA,GAAI6B,IAAS1C,EAAc,MAAQ,CAACI,EAChC,MAAMF,EACV,OAAQwC,EAAI,CACR,KAAK1C,EAAc,KACfa,EAAO,MAAMR,EAAKsC,CAAK,EACvB,MACJ,KAAK3C,EAAc,KACfa,EAAOC,EAAK6B,CAAK,EACjB,MACJ,KAAK3C,EAAc,WACfa,EAAOK,EAAUyB,CAAK,EACtB,MACJ,KAAK3C,EAAc,UACfa,EAAOO,EAASuB,CAAK,EACrB,MACJ,KAAK3C,EAAc,YACfa,EAAOS,EAAWqB,CAAK,EACvB,MACJ,KAAK3C,EAAc,OACfa,EAAOU,EAAOoB,CAAK,EACnB,MACJ,KAAK3C,EAAc,WACfa,EAAOa,EAAUiB,CAAK,EACtB,MACJ,KAAK3C,EAAc,SACfa,EAAOc,EAAQgB,CAAK,EACpB,MACJ,KAAK3C,EAAc,WACfa,EAAOoB,EAAUU,CAAK,EACtB,MACJ,KAAK3C,EAAc,MACfa,EAAOqB,EAAMS,CAAK,EAClB,MACJ,KAAK3C,EAAc,QACfa,EAAO2B,EAAQG,CAAK,EACpB,MACJ,QACI,MAAM1C,CACb,CACJ,OACM4C,EAAG,CACN,KAAK,YAAY,CACb,GAAAJ,EACA,KAAMzC,EAAc,MACpB,KAAM6C,EAAE,SAAU,CAC9B,CAAS,EACD,MACH,CACGhC,aAAgB,YAChB+B,EAAM,KAAK/B,EAAK,MAAM,EAE1B,KAAK,YAAY,CAAE,GAAA4B,EAAI,KAAAC,EAAM,KAAA7B,CAAI,EAAI+B,CAAK,CAC9C", "x_google_ignoreList": [0, 1, 2]}