"""
对话历史管理器
负责将对话记录保存到JSON文件并从文件中加载历史记录
"""

import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
from dataclasses import dataclass, asdict

from core.utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class ChatMessage:
    """聊天消息"""
    role: str  # user, assistant, system
    content: str
    timestamp: str
    persona: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class ChatSession:
    """聊天会话"""
    session_id: str
    persona: str
    title: str
    created_at: str
    last_updated: str
    messages: List[ChatMessage]
    total_messages: int
    metadata: Optional[Dict[str, Any]] = None

class HistoryManager:
    """历史记录管理器"""
    
    def __init__(self, history_dir: str = "history"):
        self.history_dir = Path(history_dir)
        self.history_dir.mkdir(exist_ok=True)
        logger.info(f"历史记录目录: {self.history_dir.absolute()}")
    
    def save_session(self, session: ChatSession) -> bool:
        """保存会话到JSON文件"""
        try:
            filename = f"{session.session_id}.json"
            filepath = self.history_dir / filename
            
            # 转换为字典格式
            session_data = {
                "session_id": session.session_id,
                "persona": session.persona,
                "title": session.title,
                "created_at": session.created_at,
                "last_updated": session.last_updated,
                "total_messages": session.total_messages,
                "metadata": session.metadata or {},
                "messages": [
                    {
                        "role": msg.role,
                        "content": msg.content,
                        "timestamp": msg.timestamp,
                        "persona": msg.persona,
                        "metadata": msg.metadata or {}
                    }
                    for msg in session.messages
                ]
            }
            
            # 保存到文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"会话已保存: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"保存会话失败: {e}")
            return False
    
    def load_session(self, session_id: str) -> Optional[ChatSession]:
        """从JSON文件加载会话"""
        try:
            filename = f"{session_id}.json"
            filepath = self.history_dir / filename
            
            if not filepath.exists():
                logger.warning(f"会话文件不存在: {filename}")
                return None
            
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 转换消息
            messages = []
            for msg_data in data.get("messages", []):
                message = ChatMessage(
                    role=msg_data["role"],
                    content=msg_data["content"],
                    timestamp=msg_data["timestamp"],
                    persona=msg_data.get("persona"),
                    metadata=msg_data.get("metadata", {})
                )
                messages.append(message)
            
            # 创建会话对象
            session = ChatSession(
                session_id=data["session_id"],
                persona=data["persona"],
                title=data["title"],
                created_at=data["created_at"],
                last_updated=data["last_updated"],
                messages=messages,
                total_messages=data["total_messages"],
                metadata=data.get("metadata", {})
            )
            
            logger.info(f"会话已加载: {filename} ({len(messages)} 条消息)")
            return session
            
        except Exception as e:
            logger.error(f"加载会话失败: {e}")
            return None
    
    def get_session_list(self) -> List[Dict[str, Any]]:
        """获取所有会话列表"""
        sessions = []
        
        try:
            # 扫描history目录中的JSON文件
            for filepath in self.history_dir.glob("*.json"):
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 提取会话信息
                    session_info = {
                        "session_id": data["session_id"],
                        "persona": data["persona"],
                        "title": data["title"],
                        "created_at": data["created_at"],
                        "last_updated": data["last_updated"],
                        "total_messages": data["total_messages"],
                        "file_path": str(filepath)
                    }
                    sessions.append(session_info)
                    
                except Exception as e:
                    logger.error(f"读取会话文件失败 {filepath}: {e}")
                    continue
            
            # 按最后更新时间排序
            sessions.sort(key=lambda x: x["last_updated"], reverse=True)
            
            logger.info(f"找到 {len(sessions)} 个历史会话")
            return sessions
            
        except Exception as e:
            logger.error(f"获取会话列表失败: {e}")
            return []
    
    def add_message_to_session(self, session_id: str, role: str, content: str, persona: str = None) -> bool:
        """向会话添加消息"""
        try:
            # 加载现有会话或创建新会话
            session = self.load_session(session_id)
            
            if session is None:
                # 创建新会话
                now = datetime.now().isoformat()
                session = ChatSession(
                    session_id=session_id,
                    persona=persona or "default",
                    title=self._generate_title(content),
                    created_at=now,
                    last_updated=now,
                    messages=[],
                    total_messages=0
                )
            
            # 添加新消息
            message = ChatMessage(
                role=role,
                content=content,
                timestamp=datetime.now().isoformat(),
                persona=persona
            )
            
            session.messages.append(message)
            session.total_messages = len(session.messages)
            session.last_updated = datetime.now().isoformat()
            
            # 更新标题（如果是第一条用户消息）
            if role == "user" and session.total_messages == 1:
                session.title = self._generate_title(content)
            
            # 保存会话
            return self.save_session(session)
            
        except Exception as e:
            logger.error(f"添加消息失败: {e}")
            return False
    
    def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        try:
            filename = f"{session_id}.json"
            filepath = self.history_dir / filename
            
            if filepath.exists():
                filepath.unlink()
                logger.info(f"会话已删除: {filename}")
                return True
            else:
                logger.warning(f"会话文件不存在: {filename}")
                return False
                
        except Exception as e:
            logger.error(f"删除会话失败: {e}")
            return False
    
    def _generate_title(self, content: str, max_length: int = 30) -> str:
        """生成会话标题"""
        if not content:
            return "新对话"
        
        try:
            # 清理内容并处理编码
            title = content.strip().replace('\n', ' ').replace('\r', '')
            
            # 确保字符串可以正确编码
            title = title.encode('utf-8', errors='ignore').decode('utf-8')
            
            # 截断长度
            if len(title) > max_length:
                title = title[:max_length] + "..."
            
            return title or "新对话"
        except Exception as e:
            logger.error(f"生成标题时编码错误: {e}")
            return "新对话"
    
    def generate_conversation_title(self, session_id: str) -> str:
        """使用AI生成对话标题"""
        try:
            session = self.load_session(session_id)
            if not session or len(session.messages) < 2:
                return "新对话"
            
            # 获取前几条消息作为上下文
            context_messages = session.messages[:4]  # 取前4条消息
            context_text = ""
            
            for msg in context_messages:
                role_name = "用户" if msg.role == "user" else "助手"
                context_text += f"{role_name}: {msg.content}\n"
            
            # 构建标题生成提示词
            title_prompt = f"""请根据以下对话内容，生成一个简洁的对话标题（不超过15个字）：

{context_text}

要求：
1. 标题要简洁明了，能概括对话主题
2. 不超过15个字
3. 不要包含"对话"、"聊天"等词汇
4. 直接输出标题，不要其他内容

标题："""

            # 使用LLM生成标题
            from core.llm.model_manager import model_manager
            
            if model_manager.client and model_manager.current_config:
                messages = [
                    {"role": "system", "content": "你是一个专业的标题生成助手，能够根据对话内容生成简洁准确的标题。"},
                    {"role": "user", "content": title_prompt}
                ]
                
                title = model_manager.generate_text_complete(
                    messages=messages,
                    max_tokens=50,
                    temperature=0.3
                )
                
                # 清理生成的标题
                title = title.strip().replace("标题：", "").replace("标题:", "")
                title = title.replace('"', '').replace("'", "")
                
                if title and len(title) <= 20:
                    # 更新会话标题
                    session.title = title
                    self.save_session(session)
                    logger.info(f"AI生成标题: {title}")
                    return title
            
            # 如果AI生成失败，使用简单方法
            return self._generate_title(context_messages[0].content if context_messages else "新对话")
            
        except Exception as e:
            error_msg = str(e)
            try:
                error_msg = error_msg.encode('utf-8', errors='ignore').decode('utf-8')
            except:
                error_msg = "生成标题时出现未知错误"
            
            logger.error(f"生成对话标题失败: {error_msg}")
            return "新对话"
    
    def get_session_summary(self, session_id: str) -> Dict[str, Any]:
        """获取会话摘要信息"""
        session = self.load_session(session_id)
        if not session:
            return {}
        
        return {
            "session_id": session.session_id,
            "persona": session.persona,
            "title": session.title,
            "created_at": session.created_at,
            "last_updated": session.last_updated,
            "total_messages": session.total_messages,
            "last_message": session.messages[-1].content if session.messages else ""
        }

# 全局历史管理器实例
history_manager = HistoryManager()
