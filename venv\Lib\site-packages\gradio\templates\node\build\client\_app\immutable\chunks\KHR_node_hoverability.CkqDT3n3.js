import{an as b,ao as k}from"./index.BoI39RQH.js";import{a as _}from"./declarationMapper.UBCwU7BT.js";import{A as B}from"./objectModelMapping.ha_8hIyl.js";const d="KHR_node_hoverability",m="targetMeshPointerOver_";_("event/onHoverIn",d,{blocks:["FlowGraphPointerOverEventBlock","FlowGraphGetVariableBlock","FlowGraphIndexOfBlock","KHR_interactivity/FlowGraphGLTFDataProvider"],configuration:{stopPropagation:{name:"stopPropagation"},nodeIndex:{name:"variable",toBlock:"FlowGraphGetVariableBlock",dataTransformer(n){return[m+n[0]]}}},outputs:{values:{hoverNodeIndex:{name:"index",toBlock:"FlowGraphIndexOfBlock"},controllerIndex:{name:"pointerId"}},flows:{out:{name:"done"}}},interBlockConnectors:[{input:"targetMesh",output:"value",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"array",output:"nodes",inputBlockIndex:2,outputBlockIndex:3,isVariable:!0},{input:"object",output:"meshUnderPointer",inputBlockIndex:2,outputBlockIndex:0,isVariable:!0}],extraProcessor(n,o,i,s,t,u,e){var p,l,h,f,v,x;const a=t[t.length-1];a.config=a.config||{},a.config.glTF=e;const r=(l=(p=n.configuration)==null?void 0:p.nodeIndex)==null?void 0:l.value[0];if(r===void 0||typeof r!="number")throw new Error("nodeIndex not found in configuration");const c=m+r;return t[1].config.variable=c,u._userVariables[c]={className:"Mesh",id:(f=(h=e==null?void 0:e.nodes)==null?void 0:h[r]._babylonTransformNode)==null?void 0:f.id,uniqueId:(x=(v=e==null?void 0:e.nodes)==null?void 0:v[r]._babylonTransformNode)==null?void 0:x.uniqueId},t}});const I="targetMeshPointerOut_";_("event/onHoverOut",d,{blocks:["FlowGraphPointerOutEventBlock","FlowGraphGetVariableBlock","FlowGraphIndexOfBlock","KHR_interactivity/FlowGraphGLTFDataProvider"],configuration:{stopPropagation:{name:"stopPropagation"},nodeIndex:{name:"variable",toBlock:"FlowGraphGetVariableBlock",dataTransformer(n){return[I+n[0]]}}},outputs:{values:{hoverNodeIndex:{name:"index",toBlock:"FlowGraphIndexOfBlock"},controllerIndex:{name:"pointerId"}},flows:{out:{name:"done"}}},interBlockConnectors:[{input:"targetMesh",output:"value",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"array",output:"nodes",inputBlockIndex:2,outputBlockIndex:3,isVariable:!0},{input:"object",output:"meshOutOfPointer",inputBlockIndex:2,outputBlockIndex:0,isVariable:!0}],extraProcessor(n,o,i,s,t,u,e){var p,l,h,f,v,x;const a=t[t.length-1];a.config=a.config||{},a.config.glTF=e;const r=(l=(p=n.configuration)==null?void 0:p.nodeIndex)==null?void 0:l.value[0];if(r===void 0||typeof r!="number")throw new Error("nodeIndex not found in configuration");const c=I+r;return t[1].config.variable=c,u._userVariables[c]={className:"Mesh",id:(f=(h=e==null?void 0:e.nodes)==null?void 0:h[r]._babylonTransformNode)==null?void 0:f.id,uniqueId:(x=(v=e==null?void 0:e.nodes)==null?void 0:v[r]._babylonTransformNode)==null?void 0:x.uniqueId},t}});B("/nodes/{}/extensions/KHR_node_hoverability/hoverable",{get:n=>{const o=n._babylonTransformNode;return o&&o.pointerOverDisableMeshTesting!==void 0?o.pointerOverDisableMeshTesting:!0},set:(n,o)=>{var i;(i=o._primitiveBabylonMeshes)==null||i.forEach(s=>{s.pointerOverDisableMeshTesting=!n})},getTarget:n=>n._babylonTransformNode,getPropertyName:[()=>"pointerOverDisableMeshTesting"],type:"boolean"});class y{constructor(o){this.name=d,this._loader=o,this.enabled=o.isExtensionUsed(d)}async onReady(){var o;(o=this._loader.gltf.nodes)==null||o.forEach(i=>{var s,t,u;(s=i.extensions)!=null&&s.KHR_node_hoverability&&((t=i.extensions)==null?void 0:t.KHR_node_hoverability.hoverable)===!1&&((u=i._babylonTransformNode)==null||u.getChildMeshes().forEach(e=>{e.pointerOverDisableMeshTesting=!0}))})}dispose(){this._loader=null}}b(d);k(d,!0,n=>new y(n));export{y as KHR_node_hoverability};
//# sourceMappingURL=KHR_node_hoverability.CkqDT3n3.js.map
