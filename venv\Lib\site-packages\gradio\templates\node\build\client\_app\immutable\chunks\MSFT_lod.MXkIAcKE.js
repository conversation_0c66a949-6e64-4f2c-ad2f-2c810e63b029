import{O as u,at as f,an as g,ao as b}from"./index.BoI39RQH.js";import{GLTFLoader as c,ArrayItem as p}from"./glTFLoader.BetPWe9U.js";const O="MSFT_lod";class x{constructor(e){var s;this.name=O,this.order=100,this.maxLODsToLoad=10,this.onNodeLODsLoadedObservable=new u,this.onMaterialLODsLoadedObservable=new u,this._bufferLODs=new Array,this._nodeIndexLOD=null,this._nodeSignalLODs=new Array,this._nodePromiseLODs=new Array,this._nodeBufferLODs=new Array,this._materialIndexLOD=null,this._materialSignalLODs=new Array,this._materialPromiseLODs=new Array,this._materialBufferLODs=new Array,this._loader=e,this.maxLODsToLoad=((s=this._loader.parent.extensionOptions[O])==null?void 0:s.maxLODsToLoad)??this.maxLODsToLoad,this.enabled=this._loader.isExtensionUsed(O)}dispose(){this._loader=null,this._nodeIndexLOD=null,this._nodeSignalLODs.length=0,this._nodePromiseLODs.length=0,this._nodeBufferLODs.length=0,this._materialIndexLOD=null,this._materialSignalLODs.length=0,this._materialPromiseLODs.length=0,this._materialBufferLODs.length=0,this.onMaterialLODsLoadedObservable.clear(),this.onNodeLODsLoadedObservable.clear()}onReady(){for(let e=0;e<this._nodePromiseLODs.length;e++){const s=Promise.all(this._nodePromiseLODs[e]).then(()=>{e!==0&&(this._loader.endPerformanceCounter(`Node LOD ${e}`),this._loader.log(`Loaded node LOD ${e}`)),this.onNodeLODsLoadedObservable.notifyObservers(e),e!==this._nodePromiseLODs.length-1&&(this._loader.startPerformanceCounter(`Node LOD ${e+1}`),this._loadBufferLOD(this._nodeBufferLODs,e+1),this._nodeSignalLODs[e]&&this._nodeSignalLODs[e].resolve())});this._loader._completePromises.push(s)}for(let e=0;e<this._materialPromiseLODs.length;e++){const s=Promise.all(this._materialPromiseLODs[e]).then(()=>{e!==0&&(this._loader.endPerformanceCounter(`Material LOD ${e}`),this._loader.log(`Loaded material LOD ${e}`)),this.onMaterialLODsLoadedObservable.notifyObservers(e),e!==this._materialPromiseLODs.length-1&&(this._loader.startPerformanceCounter(`Material LOD ${e+1}`),this._loadBufferLOD(this._materialBufferLODs,e+1),this._materialSignalLODs[e]&&this._materialSignalLODs[e].resolve())});this._loader._completePromises.push(s)}}loadSceneAsync(e,s){const t=this._loader.loadSceneAsync(e,s);return this._loadBufferLOD(this._bufferLODs,0),t}loadNodeAsync(e,s,t){return c.LoadExtensionAsync(e,s,this.name,(i,n)=>{let a;const d=this._getLODs(i,s,this._loader.gltf.nodes,n.ids);this._loader.logOpen(`${i}`);for(let o=0;o<d.length;o++){const l=d[o];o!==0&&(this._nodeIndexLOD=o,this._nodeSignalLODs[o]=this._nodeSignalLODs[o]||new f);const r=L=>{t(L),L.setEnabled(!1)},h=this._loader.loadNodeAsync(`/nodes/${l.index}`,l,r).then(L=>{if(o!==0){const _=d[o-1];_._babylonTransformNode&&(this._disposeTransformNode(_._babylonTransformNode),delete _._babylonTransformNode)}return L.setEnabled(!0),L});this._nodePromiseLODs[o]=this._nodePromiseLODs[o]||[],o===0?a=h:(this._nodeIndexLOD=null,this._nodePromiseLODs[o].push(h))}return this._loader.logClose(),a})}_loadMaterialAsync(e,s,t,i,n){return this._nodeIndexLOD?null:c.LoadExtensionAsync(e,s,this.name,(a,d)=>{let o;const l=this._getLODs(a,s,this._loader.gltf.materials,d.ids);this._loader.logOpen(`${a}`);for(let r=0;r<l.length;r++){const h=l[r];r!==0&&(this._materialIndexLOD=r);const L=this._loader._loadMaterialAsync(`/materials/${h.index}`,h,t,i,_=>{r===0&&n(_)}).then(_=>{if(r!==0){n(_);const D=l[r-1]._data;D[i]&&(this._disposeMaterials([D[i].babylonMaterial]),delete D[i])}return _});this._materialPromiseLODs[r]=this._materialPromiseLODs[r]||[],r===0?o=L:(this._materialIndexLOD=null,this._materialPromiseLODs[r].push(L))}return this._loader.logClose(),o})}_loadUriAsync(e,s,t){if(this._nodeIndexLOD!==null){this._loader.log("deferred");const i=this._nodeIndexLOD-1;return this._nodeSignalLODs[i]=this._nodeSignalLODs[i]||new f,this._nodeSignalLODs[this._nodeIndexLOD-1].promise.then(()=>this._loader.loadUriAsync(e,s,t))}else if(this._materialIndexLOD!==null){this._loader.log("deferred");const i=this._materialIndexLOD-1;return this._materialSignalLODs[i]=this._materialSignalLODs[i]||new f,this._materialSignalLODs[i].promise.then(()=>this._loader.loadUriAsync(e,s,t))}return null}loadBufferAsync(e,s,t,i){if(this._loader.parent.useRangeRequests&&!s.uri){if(!this._loader.bin)throw new Error(`${e}: Uri is missing or the binary glTF is missing its binary chunk`);const n=(a,d)=>{const o=t,l=o+i-1;let r=a[d];return r?(r.start=Math.min(r.start,o),r.end=Math.max(r.end,l)):(r={start:o,end:l,loaded:new f},a[d]=r),r.loaded.promise.then(h=>new Uint8Array(h.buffer,h.byteOffset+t-r.start,i))};return this._loader.log("deferred"),this._nodeIndexLOD!==null?n(this._nodeBufferLODs,this._nodeIndexLOD):this._materialIndexLOD!==null?n(this._materialBufferLODs,this._materialIndexLOD):n(this._bufferLODs,0)}return null}_loadBufferLOD(e,s){const t=e[s];t&&(this._loader.log(`Loading buffer range [${t.start}-${t.end}]`),this._loader.bin.readAsync(t.start,t.end-t.start+1).then(i=>{t.loaded.resolve(i)},i=>{t.loaded.reject(i)}))}_getLODs(e,s,t,i){if(this.maxLODsToLoad<=0)throw new Error("maxLODsToLoad must be greater than zero");const n=[];for(let a=i.length-1;a>=0;a--)if(n.push(p.Get(`${e}/ids/${i[a]}`,t,i[a])),n.length===this.maxLODsToLoad)return n;return n.push(s),n}_disposeTransformNode(e){const s=[],t=e.material;t&&s.push(t);for(const n of e.getChildMeshes())n.material&&s.push(n.material);e.dispose();const i=s.filter(n=>this._loader.babylonScene.meshes.every(a=>a.material!=n));this._disposeMaterials(i)}_disposeMaterials(e){const s={};for(const t of e){for(const i of t.getActiveTextures())s[i.uniqueId]=i;t.dispose()}for(const t in s)for(const i of this._loader.babylonScene.materials)i.hasTexture(s[t])&&delete s[t];for(const t in s)s[t].dispose()}}g(O);b(O,!0,m=>new x(m));export{x as MSFT_lod};
//# sourceMappingURL=MSFT_lod.MXkIAcKE.js.map
