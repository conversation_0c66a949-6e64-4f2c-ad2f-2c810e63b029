import{SvelteComponent as S,init as T,safe_not_equal as V,empty as p,insert_hydration as v,group_outros as A,transition_out as d,check_outros as C,transition_in as h,detach as m,compute_rest_props as D,createEventDispatcher as z,assign as b,exclude_internal_props as I,create_slot as E,element as N,claim_element as O,children as P,set_attributes as k,set_style as W,toggle_class as w,listen as B,update_slot_base as L,get_all_dirty_from_scope as R,get_slot_changes as U,get_spread_update as F,prevent_default as J}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{j as K,k as M,n as Q}from"./2.B2AoQPnG.js";function X(f){let e,r,o,n,u;const i=f[8].default,s=E(i,f,f[7],null);let a=[{class:"download-link"},{href:f[0]},{target:r=typeof window<"u"&&window.__is_colab__?"_blank":null},{rel:"noopener noreferrer"},{download:f[1]},f[6]],l={};for(let t=0;t<a.length;t+=1)l=b(l,a[t]);return{c(){e=N("a"),s&&s.c(),this.h()},l(t){e=O(t,"A",{class:!0,href:!0,target:!0,rel:!0,download:!0});var _=P(e);s&&s.l(_),_.forEach(m),this.h()},h(){k(e,l),W(e,"position","relative"),w(e,"svelte-1s8vnbx",!0)},m(t,_){v(t,e,_),s&&s.m(e,null),o=!0,n||(u=B(e,"click",f[3].bind(null,"click")),n=!0)},p(t,_){s&&s.p&&(!o||_&128)&&L(s,i,t,t[7],o?U(i,t[7],_,null):R(t[7]),null),k(e,l=F(a,[{class:"download-link"},(!o||_&1)&&{href:t[0]},{target:r},{rel:"noopener noreferrer"},(!o||_&2)&&{download:t[1]},_&64&&t[6]])),W(e,"position","relative"),w(e,"svelte-1s8vnbx",!0)},i(t){o||(h(s,t),o=!0)},o(t){d(s,t),o=!1},d(t){t&&m(e),s&&s.d(t),n=!1,u()}}}function Y(f){let e,r,o,n;const u=[x,Z],i=[];function s(a,l){return a[2]?0:1}return e=s(f),r=i[e]=u[e](f),{c(){r.c(),o=p()},l(a){r.l(a),o=p()},m(a,l){i[e].m(a,l),v(a,o,l),n=!0},p(a,l){let t=e;e=s(a),e===t?i[e].p(a,l):(A(),d(i[t],1,1,()=>{i[t]=null}),C(),r=i[e],r?r.p(a,l):(r=i[e]=u[e](a),r.c()),h(r,1),r.m(o.parentNode,o))},i(a){n||(h(r),n=!0)},o(a){d(r),n=!1},d(a){a&&m(o),i[e].d(a)}}}function Z(f){let e,r,o,n;const u=f[8].default,i=E(u,f,f[7],null);let s=[f[6],{href:f[0]}],a={};for(let l=0;l<s.length;l+=1)a=b(a,s[l]);return{c(){e=N("a"),i&&i.c(),this.h()},l(l){e=O(l,"A",{href:!0});var t=P(e);i&&i.l(t),t.forEach(m),this.h()},h(){k(e,a),w(e,"svelte-1s8vnbx",!0)},m(l,t){v(l,e,t),i&&i.m(e,null),r=!0,o||(n=B(e,"click",J(f[5])),o=!0)},p(l,t){i&&i.p&&(!r||t&128)&&L(i,u,l,l[7],r?U(u,l[7],t,null):R(l[7]),null),k(e,a=F(s,[t&64&&l[6],(!r||t&1)&&{href:l[0]}])),w(e,"svelte-1s8vnbx",!0)},i(l){r||(h(i,l),r=!0)},o(l){d(i,l),r=!1},d(l){l&&m(e),i&&i.d(l),o=!1,n()}}}function x(f){let e;const r=f[8].default,o=E(r,f,f[7],null);return{c(){o&&o.c()},l(n){o&&o.l(n)},m(n,u){o&&o.m(n,u),e=!0},p(n,u){o&&o.p&&(!e||u&128)&&L(o,r,n,n[7],e?U(r,n[7],u,null):R(n[7]),null)},i(n){e||(h(o,n),e=!0)},o(n){d(o,n),e=!1},d(n){o&&o.d(n)}}}function $(f){let e,r,o,n,u;const i=[Y,X],s=[];function a(l,t){return t&1&&(e=null),e==null&&(e=!!(l[4]&&M(l[0]))),e?0:1}return r=a(f,-1),o=s[r]=i[r](f),{c(){o.c(),n=p()},l(l){o.l(l),n=p()},m(l,t){s[r].m(l,t),v(l,n,t),u=!0},p(l,[t]){let _=r;r=a(l,t),r===_?s[r].p(l,t):(A(),d(s[_],1,1,()=>{s[_]=null}),C(),o=s[r],o?o.p(l,t):(o=s[r]=i[r](l),o.c()),h(o,1),o.m(n.parentNode,n))},i(l){u||(h(o),u=!0)},o(l){d(o),u=!1},d(l){l&&m(n),s[r].d(l)}}}function ee(f,e,r){const o=["href","download"];let n=D(e,o),{$$slots:u={},$$scope:i}=e,{href:s=void 0}=e,{download:a}=e;const l=z();let t=!1;const _=K();async function G(){if(t)return;if(l("click"),s==null)throw new Error("href is not defined.");if(_==null)throw new Error("Wasm worker proxy is not available.");const j=new URL(s,window.location.href).pathname;r(2,t=!0),_.httpRequest({method:"GET",path:j,headers:{},query_string:""}).then(g=>{if(g.status!==200)throw new Error(`Failed to get file ${j} from the Wasm worker.`);const H=new Blob([g.body],{type:Q(g.headers,"content-type")}),q=URL.createObjectURL(H),y=document.createElement("a");y.href=q,y.download=a,y.click(),URL.revokeObjectURL(q)}).finally(()=>{r(2,t=!1)})}return f.$$set=c=>{e=b(b({},e),I(c)),r(6,n=D(e,o)),"href"in c&&r(0,s=c.href),"download"in c&&r(1,a=c.download),"$$scope"in c&&r(7,i=c.$$scope)},[s,a,t,l,_,G,n,i,u]}class re extends S{constructor(e){super(),T(this,e,ee,$,V,{href:0,download:1})}}export{re as D};
//# sourceMappingURL=DownloadLink.D1g3Q1HV.js.map
