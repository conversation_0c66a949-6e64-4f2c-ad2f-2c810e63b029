const __vite__fileDeps=["./Index.CU-sllhp.js","../assets/Index.C5NYahSl.css","./Index.ZlUMhLz3.js","./BlockLabel.BTSz9r5s.js","./Empty.DwQ6nkN6.js","./Image.CTVzPhL7.js","./IconButtonWrapper.D5aGR59h.js","./FullscreenButton.g_8wwg6y.js","../assets/Index.E3yBBMTH.css","./StaticAudio.CEB3a_-s.js","./ShareButton.Be7APJkJ.js","./Community.i_uzCNAp.js","./Download.CpfEFmFf.js","./Music.BVFRDHso.js","./AudioPlayer.CnAk5fND.js","./Trim.CWFkmJwA.js","./Play.DJ4h2PVY.js","./Undo.LhwFM5M8.js","./hls.C_AVPmGC.js","../assets/AudioPlayer.D5BS3Cgh.css","./DownloadLink.D1g3Q1HV.js","./Example.BioRcyBn.js","../assets/Example.D7K5RtQ2.css","./index.-LvVKGMO.js","./InteractiveAudio.BOik-Knm.js","./preload-helper.D6kgxu3v.js","./Upload.yOHVlgUe.js","../assets/Upload.L7mprsyN.css","./ModifyUpload.uW4g0eE0.js","./Edit.jvyxqbov.js","./SelectSource.CTC8Kkgx.js","./StreamingBar.BOfzkLQo.js","../assets/InteractiveAudio.B76TQFG-.css","./UploadText.CJcy9n89.js","./Index.DQB3Bzkd.js","./__vite-browser-external.D7Ct-6yo.js","./Index.Bv2l-SRz.js","./File.Dl9hvYLG.js","./index.CnqicUFC.js","./Trash.D4IfxcH_.js","../assets/Index.CSHIljo_.css","./Example.7rhdKtvr.js","./Index.DVcrV1NY.js","./Checkbox.BBJAZbOw.js","../assets/Checkbox.COx9d1js.css","./Example.CO-COJlt.js","./Index.DbcE8rmG.js","../assets/Index.DMKGW8pW.css","./Example.HHpueynA.js","../assets/Example.oomIF0ca.css","./Index.B7Jp9MPG.js","./Code.DWo5KduI.js","../assets/Index.DloLYeAi.css","./Example.BtYdbMfl.js","../assets/Example.Bw8Q_3wB.css","./Index.C4qVdpCH.js","./tinycolor.CowIdatr.js","../assets/Index.DwWu86Nh.css","./Example.Meu2o5J6.js","../assets/Example.1kVNej19.css","./Index.D6J2PVPn.js","./index.tFQomdd2.js","./dsv.DB8NKgIY.js","./DropdownArrow.pfrcUdj1.js","./Index.BekbgQye.js","./ImagePreview.B6xPgKdu.js","./utils.Gtzs_Zla.js","./ImageUploader.Dvb2Mtrn.js","../assets/ImageUploader.DMdYP1a9.css","./Example.BnEs1fCL.js","../assets/Example.DikqVAPo.css","../assets/Index.Ckk68opA.css","./Index.DHWTYgOZ.js","./Example.DtHlUNEL.js","../assets/Index.D3f6Hf9S.css","./Index.OQ2NBVln.js","./Example.p5DpXI6u.js","../assets/Index.Bk5ZTHOI.css","./Index.Dzsrn0Y_.js","../assets/Index.tcNSQSor.css","./Example.AaUiXLZ_.js","./Index.B10FuoQa.js","./Dropdown.BTvmrOlN.js","../assets/Dropdown.CWxB-qJp.css","./Example.CZOpiQG0.js","../assets/Example.DpWs9cEC.css","./Index.CZNYq4H7.js","./FileUpload.DUgXd8x6.js","../assets/FileUpload.b2Zdge9M.css","./Example.DbEDtHpU.js","../assets/Example.DfhEULNF.css","./Index.B70BNVP7.js","../assets/Index.BKaa_GXG.css","./Gallery.BgdW0y-K.js","./Video.SijWdeHX.js","../assets/Video.DJw86Ppo.css","../assets/Gallery.CUVAWrOv.css","./Index.BJYxUfB0.js","./Index.BgwHXxYk.js","../assets/Index.Cgj6KPvj.css","./Index.D56cqZJI.js","./color.6b7WW5Kp.js","../assets/Index.KsvjhaTn.css","./Index.vSoPi4Pe.js","../assets/Index.Csm0OGa9.css","./Example.BBdSzwmg.js","../assets/Example.CSw4pLi5.css","./Example.DU87hEH5.js","../assets/Example.6rv12T44.css","./Index.Bd_GXR6d.js","../assets/Index.CCAzyDPy.css","./Example.C3O8QWdg.js","../assets/Example.fMB4cHw6.css","./Index.Cm0Yofoi.js","./select.BigU4G0v.js","./dispatch.kxCwF96_.js","../assets/Index.xkmfPIkL.css","./Example.VAz9a1G1.js","./JSON.CA9rn_1b.js","../assets/JSON.AVFPpFex.css","../assets/Example.CG7uBGLE.css","./Index.n4nGh_FC.js","./Index.Bi4RUrG3.js","./LineChart.C8bDl53w.js","../assets/Index.D3BKJl5I.css","./Example.4x8pwdna.js","./Index.3nCFs0Nn.js","../assets/Index.BTXaMQgd.css","./Example.V12cx6oG.js","./Index.innrePmL.js","../assets/Index.Be3F7oKw.css","./Example.CLHP3nyK.js","../assets/Example.CCTTJ5R1.css","./Index.CnBkVKz_.js","./Video.CE2Y9LYL.js","../assets/Index.D6bhueJw.css","./Index.HDGqOq9a.js","../assets/Index.WdTVQ0oj.css","./Example.Du75eb0D.js","./Index.B1U3d4hY.js","../assets/Index.Dclo02rM.css","./Example.qq1Un4eO.js","./Index.CbpaiuHS.js","../assets/Index.BuaZ3fzZ.css","./Plot.BOh0qlQz.js","./Index.DVGOl5fo.js","./Example.Dl9poLS6.js","./Index.DP0ejsHH.js","../assets/Index.-UpFQsHg.css","./Index.MLf-ptpy.js","../assets/Index.CfowPFmo.css","./Index.B_t3vyeP.js","../assets/Index.w1Ra_uQ1.css","./Index.WpI12aFk.js","../assets/Index.Cwr2RydD.css","./Example.FcPgDijm.js","./Index.DXH7H1Cc.js","../assets/Index.C0mowOU5.css","./index.eE3WRTCe.js","./Index.C3gJvtqi.js","./Tabs.DkGgrUWn.js","../assets/Tabs.C0qLuAtA.css","../assets/Index.Gmwqb-vD.css","./Index.CXpQskGr.js","./Index.BICvNBdW.js","./Index.BfN4FUC5.js","../assets/Index.DYDmCduo.css","./VideoPreview.BI_25RC8.js","../assets/VideoPreview.BeECudjn.css","./Example.Dzb6Yct_.js","../assets/Example.B5CSTz0f.css","./index.CMpgkpBY.js","../assets/index.CFBZQE_H.css","./Example.cOvhHY2p.js","./Index.BW3mQ8xb.js","../assets/Index.CgDrEMlk.css","../assets/katex.s5L0WRdg.css","./auto-render.530FrPZJ.js","./katex.rPiVaalG.js","./mermaid.core.CKP5SxPy.js","./step.Ce-xBr2D.js","./ApiDocs.DrqKfr4o.js","./clear.D_TEXRKD.js","../assets/ApiDocs.CSqxRPus.css","./ApiRecorder.r4QAwWei.js","../assets/ApiRecorder.7UmvbTxs.css","./Settings.BXvpy3uo.js","../assets/Settings.D01KD214.css"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
var xu=Object.defineProperty;var Hu=(t,e,n)=>e in t?xu(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var K=(t,e,n)=>(Hu(t,typeof e!="symbol"?e+"":e,n),n),dr=(t,e,n)=>{if(!e.has(t))throw TypeError("Cannot "+n)};var Kt=(t,e,n)=>(dr(t,e,"read from private field"),n?n.call(t):e.get(t)),Ln=(t,e,n)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,n)},Di=(t,e,n,i)=>(dr(t,e,"write to private field"),i?i.call(t,n):e.set(t,n),n);var ki=(t,e,n)=>(dr(t,e,"access private method"),n);import{SvelteComponent as qe,init as We,safe_not_equal as Xe,create_slot as Ut,element as Z,space as oe,claim_element as X,children as U,claim_space as ae,detach as v,attr as _,toggle_class as G,set_style as Y,insert_hydration as R,append_hydration as M,update_slot_base as jt,get_all_dirty_from_scope as Gt,get_slot_changes as Vt,transition_in as B,transition_out as j,component_subscribe as Rt,getContext as Pa,ensure_array_like as Ot,destroy_each as Qi,empty as fe,text as Ee,claim_text as ve,get_svelte_dataset as Pn,set_data as je,binding_callbacks as Je,listen as Te,svg_element as Se,claim_svg_element as Le,noop as pe,onMount as gn,assign as It,set_dynamic_element_data as Es,get_spread_update as un,HtmlTagHydration as Ki,claim_html_tag as $i,afterUpdate as Ba,tick as on,create_component as ge,claim_component as Re,mount_component as be,destroy_component as we,group_outros as Be,check_outros as Ne,construct_svelte_component as Dt,bubble as _t,createEventDispatcher as zn,onDestroy as Na,stop_propagation as vs,add_render_callback as Ma,create_in_transition as xa,create_out_transition as zu,run_all as En,update_keyed_each as Ha,fix_and_outro_and_destroy_block as Uu,fix_position as ju,create_animation as Gu,not_equal as Vu,compute_rest_props as zi,exclude_internal_props as za,bind as mt,get_spread_object as Bn,add_flush_callback as xt,setContext as qu,outro_and_destroy_block as Wu,head_selector as Ua,src_url_equal as Jn,set_store_value as Zu,beforeUpdate as Xu,set_input_value as cn,action_destroyer as Yu,is_function as Ju,set_attributes as ys,globals as Qu}from"../../../svelte/svelte.js";import{writable as Zt,derived as vn,get as $r,spring as Ds,fade as Br,flip as Ku}from"../../../svelte/svelte-submodules.js";import{_ as P}from"./preload-helper.D6kgxu3v.js";import{p as $u}from"./stores.z8sZTwoA.js";const ec=!0,tc=ec,nc="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='10'%20height='10'%20fill='none'%3e%3cpath%20fill='%23FF3270'%20d='M1.93%206.03v2.04h2.04V6.03H1.93Z'/%3e%3cpath%20fill='%23861FFF'%20d='M6.03%206.03v2.04h2.04V6.03H6.03Z'/%3e%3cpath%20fill='%23097EFF'%20d='M1.93%201.93v2.04h2.04V1.93H1.93Z'/%3e%3cpath%20fill='%23000'%20fill-rule='evenodd'%20d='M.5%201.4c0-.5.4-.9.9-.9h3.1a.9.9%200%200%201%20.87.67A2.44%202.44%200%200%201%209.5%202.95c0%20.65-.25%201.24-.67%***********.67.46.67.88v3.08c0%20.5-.4.91-.9.91H1.4a.9.9%200%200%201-.9-.9V1.4Zm1.43.53v2.04h2.04V1.93H1.93Zm0%206.14V6.03h2.04v2.04H1.93Zm4.1%200V6.03h2.04v2.04H6.03Zm0-5.12a1.02%201.02%200%201%201%202.04%200%201.02%201.02%200%200%201-2.04%200Z'%20clip-rule='evenodd'/%3e%3cpath%20fill='%23FFD702'%20d='M7.05%201.93a1.02%201.02%200%201%200%200%202.04%201.02%201.02%200%200%200%200-2.04Z'/%3e%3c/svg%3e";var ks=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function ja(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function Fg(t){if(t.__esModule)return t;var e=t.default;if(typeof e=="function"){var n=function i(){return this instanceof i?Reflect.construct(e,arguments,this.constructor):e.apply(this,arguments)};n.prototype=e.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(t).forEach(function(i){var r=Object.getOwnPropertyDescriptor(t,i);Object.defineProperty(n,i,r.get?r:{enumerable:!0,get:function(){return t[i]}})}),n}var ic=function(e){return rc(e)&&!sc(e)};function rc(t){return!!t&&typeof t=="object"}function sc(t){var e=Object.prototype.toString.call(t);return e==="[object RegExp]"||e==="[object Date]"||lc(t)}var oc=typeof Symbol=="function"&&Symbol.for,ac=oc?Symbol.for("react.element"):60103;function lc(t){return t.$$typeof===ac}function uc(t){return Array.isArray(t)?[]:{}}function ri(t,e){return e.clone!==!1&&e.isMergeableObject(t)?Nn(uc(t),t,e):t}function cc(t,e,n){return t.concat(e).map(function(i){return ri(i,n)})}function fc(t,e){if(!e.customMerge)return Nn;var n=e.customMerge(t);return typeof n=="function"?n:Nn}function hc(t){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t).filter(function(e){return Object.propertyIsEnumerable.call(t,e)}):[]}function As(t){return Object.keys(t).concat(hc(t))}function Ga(t,e){try{return e in t}catch{return!1}}function dc(t,e){return Ga(t,e)&&!(Object.hasOwnProperty.call(t,e)&&Object.propertyIsEnumerable.call(t,e))}function pc(t,e,n){var i={};return n.isMergeableObject(t)&&As(t).forEach(function(r){i[r]=ri(t[r],n)}),As(e).forEach(function(r){dc(t,r)||(Ga(t,r)&&n.isMergeableObject(e[r])?i[r]=fc(r,n)(t[r],e[r],n):i[r]=ri(e[r],n))}),i}function Nn(t,e,n){n=n||{},n.arrayMerge=n.arrayMerge||cc,n.isMergeableObject=n.isMergeableObject||ic,n.cloneUnlessOtherwiseSpecified=ri;var i=Array.isArray(e),r=Array.isArray(t),s=i===r;return s?i?n.arrayMerge(t,e,n):pc(t,e,n):ri(e,n)}Nn.all=function(e,n){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce(function(i,r){return Nn(i,r,n)},{})};var mc=Nn,_c=mc;const gc=ja(_c);var Nr=function(t,e){return Nr=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(n[r]=i[r])},Nr(t,e)};function er(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");Nr(t,e);function n(){this.constructor=t}t.prototype=e===null?Object.create(e):(n.prototype=e.prototype,new n)}var ze=function(){return ze=Object.assign||function(e){for(var n,i=1,r=arguments.length;i<r;i++){n=arguments[i];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(e[s]=n[s])}return e},ze.apply(this,arguments)};function bc(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,i=Object.getOwnPropertySymbols(t);r<i.length;r++)e.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(t,i[r])&&(n[i[r]]=t[i[r]]);return n}function pr(t,e,n){if(n||arguments.length===2)for(var i=0,r=e.length,s;i<r;i++)(s||!(i in e))&&(s||(s=Array.prototype.slice.call(e,0,i)),s[i]=e[i]);return t.concat(s||Array.prototype.slice.call(e))}var Oe;(function(t){t[t.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",t[t.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",t[t.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",t[t.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",t[t.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",t[t.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",t[t.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",t[t.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",t[t.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",t[t.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",t[t.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",t[t.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",t[t.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",t[t.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",t[t.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",t[t.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",t[t.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",t[t.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",t[t.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",t[t.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",t[t.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",t[t.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",t[t.INVALID_TAG=23]="INVALID_TAG",t[t.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",t[t.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",t[t.UNCLOSED_TAG=27]="UNCLOSED_TAG"})(Oe||(Oe={}));var rt;(function(t){t[t.literal=0]="literal",t[t.argument=1]="argument",t[t.number=2]="number",t[t.date=3]="date",t[t.time=4]="time",t[t.select=5]="select",t[t.plural=6]="plural",t[t.pound=7]="pound",t[t.tag=8]="tag"})(rt||(rt={}));var Mn;(function(t){t[t.number=0]="number",t[t.dateTime=1]="dateTime"})(Mn||(Mn={}));function Fs(t){return t.type===rt.literal}function wc(t){return t.type===rt.argument}function Va(t){return t.type===rt.number}function qa(t){return t.type===rt.date}function Wa(t){return t.type===rt.time}function Za(t){return t.type===rt.select}function Xa(t){return t.type===rt.plural}function Ec(t){return t.type===rt.pound}function Ya(t){return t.type===rt.tag}function Ja(t){return!!(t&&typeof t=="object"&&t.type===Mn.number)}function Mr(t){return!!(t&&typeof t=="object"&&t.type===Mn.dateTime)}var Qa=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,vc=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;function yc(t){var e={};return t.replace(vc,function(n){var i=n.length;switch(n[0]){case"G":e.era=i===4?"long":i===5?"narrow":"short";break;case"y":e.year=i===2?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw new RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw new RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":e.month=["numeric","2-digit","short","long","narrow"][i-1];break;case"w":case"W":throw new RangeError("`w/W` (week) patterns are not supported");case"d":e.day=["numeric","2-digit"][i-1];break;case"D":case"F":case"g":throw new RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":e.weekday=i===4?"long":i===5?"narrow":"short";break;case"e":if(i<4)throw new RangeError("`e..eee` (weekday) patterns are not supported");e.weekday=["short","long","narrow","short"][i-4];break;case"c":if(i<4)throw new RangeError("`c..ccc` (weekday) patterns are not supported");e.weekday=["short","long","narrow","short"][i-4];break;case"a":e.hour12=!0;break;case"b":case"B":throw new RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":e.hourCycle="h12",e.hour=["numeric","2-digit"][i-1];break;case"H":e.hourCycle="h23",e.hour=["numeric","2-digit"][i-1];break;case"K":e.hourCycle="h11",e.hour=["numeric","2-digit"][i-1];break;case"k":e.hourCycle="h24",e.hour=["numeric","2-digit"][i-1];break;case"j":case"J":case"C":throw new RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":e.minute=["numeric","2-digit"][i-1];break;case"s":e.second=["numeric","2-digit"][i-1];break;case"S":case"A":throw new RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":e.timeZoneName=i<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw new RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),e}var Dc=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i;function kc(t){if(t.length===0)throw new Error("Number skeleton cannot be empty");for(var e=t.split(Dc).filter(function(f){return f.length>0}),n=[],i=0,r=e;i<r.length;i++){var s=r[i],o=s.split("/");if(o.length===0)throw new Error("Invalid number skeleton");for(var a=o[0],l=o.slice(1),u=0,c=l;u<c.length;u++){var h=c[u];if(h.length===0)throw new Error("Invalid number skeleton")}n.push({stem:a,options:l})}return n}function Ac(t){return t.replace(/^(.*?)-/,"")}var Ts=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,Ka=/^(@+)?(\+|#+)?[rs]?$/g,Fc=/(\*)(0+)|(#+)(0+)|(0+)/g,$a=/^(0+)$/;function Cs(t){var e={};return t[t.length-1]==="r"?e.roundingPriority="morePrecision":t[t.length-1]==="s"&&(e.roundingPriority="lessPrecision"),t.replace(Ka,function(n,i,r){return typeof r!="string"?(e.minimumSignificantDigits=i.length,e.maximumSignificantDigits=i.length):r==="+"?e.minimumSignificantDigits=i.length:i[0]==="#"?e.maximumSignificantDigits=i.length:(e.minimumSignificantDigits=i.length,e.maximumSignificantDigits=i.length+(typeof r=="string"?r.length:0)),""}),e}function el(t){switch(t){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function Tc(t){var e;if(t[0]==="E"&&t[1]==="E"?(e={notation:"engineering"},t=t.slice(2)):t[0]==="E"&&(e={notation:"scientific"},t=t.slice(1)),e){var n=t.slice(0,2);if(n==="+!"?(e.signDisplay="always",t=t.slice(2)):n==="+?"&&(e.signDisplay="exceptZero",t=t.slice(2)),!$a.test(t))throw new Error("Malformed concise eng/scientific notation");e.minimumIntegerDigits=t.length}return e}function Ss(t){var e={},n=el(t);return n||e}function Cc(t){for(var e={},n=0,i=t;n<i.length;n++){var r=i[n];switch(r.stem){case"percent":case"%":e.style="percent";continue;case"%x100":e.style="percent",e.scale=100;continue;case"currency":e.style="currency",e.currency=r.options[0];continue;case"group-off":case",_":e.useGrouping=!1;continue;case"precision-integer":case".":e.maximumFractionDigits=0;continue;case"measure-unit":case"unit":e.style="unit",e.unit=Ac(r.options[0]);continue;case"compact-short":case"K":e.notation="compact",e.compactDisplay="short";continue;case"compact-long":case"KK":e.notation="compact",e.compactDisplay="long";continue;case"scientific":e=ze(ze(ze({},e),{notation:"scientific"}),r.options.reduce(function(l,u){return ze(ze({},l),Ss(u))},{}));continue;case"engineering":e=ze(ze(ze({},e),{notation:"engineering"}),r.options.reduce(function(l,u){return ze(ze({},l),Ss(u))},{}));continue;case"notation-simple":e.notation="standard";continue;case"unit-width-narrow":e.currencyDisplay="narrowSymbol",e.unitDisplay="narrow";continue;case"unit-width-short":e.currencyDisplay="code",e.unitDisplay="short";continue;case"unit-width-full-name":e.currencyDisplay="name",e.unitDisplay="long";continue;case"unit-width-iso-code":e.currencyDisplay="symbol";continue;case"scale":e.scale=parseFloat(r.options[0]);continue;case"rounding-mode-floor":e.roundingMode="floor";continue;case"rounding-mode-ceiling":e.roundingMode="ceil";continue;case"rounding-mode-down":e.roundingMode="trunc";continue;case"rounding-mode-up":e.roundingMode="expand";continue;case"rounding-mode-half-even":e.roundingMode="halfEven";continue;case"rounding-mode-half-down":e.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":e.roundingMode="halfExpand";continue;case"integer-width":if(r.options.length>1)throw new RangeError("integer-width stems only accept a single optional option");r.options[0].replace(Fc,function(l,u,c,h,f,d){if(u)e.minimumIntegerDigits=c.length;else{if(h&&f)throw new Error("We currently do not support maximum integer digits");if(d)throw new Error("We currently do not support exact integer digits")}return""});continue}if($a.test(r.stem)){e.minimumIntegerDigits=r.stem.length;continue}if(Ts.test(r.stem)){if(r.options.length>1)throw new RangeError("Fraction-precision stems only accept a single optional option");r.stem.replace(Ts,function(l,u,c,h,f,d){return c==="*"?e.minimumFractionDigits=u.length:h&&h[0]==="#"?e.maximumFractionDigits=h.length:f&&d?(e.minimumFractionDigits=f.length,e.maximumFractionDigits=f.length+d.length):(e.minimumFractionDigits=u.length,e.maximumFractionDigits=u.length),""});var s=r.options[0];s==="w"?e=ze(ze({},e),{trailingZeroDisplay:"stripIfInteger"}):s&&(e=ze(ze({},e),Cs(s)));continue}if(Ka.test(r.stem)){e=ze(ze({},e),Cs(r.stem));continue}var o=el(r.stem);o&&(e=ze(ze({},e),o));var a=Tc(r.stem);a&&(e=ze(ze({},e),a))}return e}var Ai={"001":["H","h"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["H","h","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["H","hB","h","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["H","h","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["H","h","hB","hb"],CU:["H","h","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["H","hB","h","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["H","h","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["H","h","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["H","h","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["H","h","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["H","hB","h","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["H","h","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["H","h","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["H","h","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"es-BO":["H","h","hB","hb"],"es-BR":["H","h","hB","hb"],"es-EC":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"es-PE":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]};function Sc(t,e){for(var n="",i=0;i<t.length;i++){var r=t.charAt(i);if(r==="j"){for(var s=0;i+1<t.length&&t.charAt(i+1)===r;)s++,i++;var o=1+(s&1),a=s<2?1:3+(s>>1),l="a",u=Lc(e);for((u=="H"||u=="k")&&(a=0);a-- >0;)n+=l;for(;o-- >0;)n=u+n}else r==="J"?n+="H":n+=r}return n}function Lc(t){var e=t.hourCycle;if(e===void 0&&t.hourCycles&&t.hourCycles.length&&(e=t.hourCycles[0]),e)switch(e){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw new Error("Invalid hourCycle")}var n=t.language,i;n!=="root"&&(i=t.maximize().region);var r=Ai[i||""]||Ai[n||""]||Ai["".concat(n,"-001")]||Ai["001"];return r[0]}var mr,Rc=new RegExp("^".concat(Qa.source,"*")),Oc=new RegExp("".concat(Qa.source,"*$"));function Pe(t,e){return{start:t,end:e}}var Ic=!!String.prototype.startsWith&&"_a".startsWith("a",1),Pc=!!String.fromCodePoint,Bc=!!Object.fromEntries,Nc=!!String.prototype.codePointAt,Mc=!!String.prototype.trimStart,xc=!!String.prototype.trimEnd,Hc=!!Number.isSafeInteger,zc=Hc?Number.isSafeInteger:function(t){return typeof t=="number"&&isFinite(t)&&Math.floor(t)===t&&Math.abs(t)<=9007199254740991},xr=!0;try{var Uc=nl("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");xr=((mr=Uc.exec("a"))===null||mr===void 0?void 0:mr[0])==="a"}catch{xr=!1}var Ls=Ic?function(e,n,i){return e.startsWith(n,i)}:function(e,n,i){return e.slice(i,i+n.length)===n},Hr=Pc?String.fromCodePoint:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];for(var i="",r=e.length,s=0,o;r>s;){if(o=e[s++],o>1114111)throw RangeError(o+" is not a valid code point");i+=o<65536?String.fromCharCode(o):String.fromCharCode(((o-=65536)>>10)+55296,o%1024+56320)}return i},Rs=Bc?Object.fromEntries:function(e){for(var n={},i=0,r=e;i<r.length;i++){var s=r[i],o=s[0],a=s[1];n[o]=a}return n},tl=Nc?function(e,n){return e.codePointAt(n)}:function(e,n){var i=e.length;if(!(n<0||n>=i)){var r=e.charCodeAt(n),s;return r<55296||r>56319||n+1===i||(s=e.charCodeAt(n+1))<56320||s>57343?r:(r-55296<<10)+(s-56320)+65536}},jc=Mc?function(e){return e.trimStart()}:function(e){return e.replace(Rc,"")},Gc=xc?function(e){return e.trimEnd()}:function(e){return e.replace(Oc,"")};function nl(t,e){return new RegExp(t,e)}var zr;if(xr){var Os=nl("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");zr=function(e,n){var i;Os.lastIndex=n;var r=Os.exec(e);return(i=r[1])!==null&&i!==void 0?i:""}}else zr=function(e,n){for(var i=[];;){var r=tl(e,n);if(r===void 0||il(r)||Zc(r))break;i.push(r),n+=r>=65536?2:1}return Hr.apply(void 0,i)};var Vc=function(){function t(e,n){n===void 0&&(n={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!n.ignoreTag,this.locale=n.locale,this.requiresOtherClause=!!n.requiresOtherClause,this.shouldParseSkeletons=!!n.shouldParseSkeletons}return t.prototype.parse=function(){if(this.offset()!==0)throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},t.prototype.parseMessage=function(e,n,i){for(var r=[];!this.isEOF();){var s=this.char();if(s===123){var o=this.parseArgument(e,i);if(o.err)return o;r.push(o.val)}else{if(s===125&&e>0)break;if(s===35&&(n==="plural"||n==="selectordinal")){var a=this.clonePosition();this.bump(),r.push({type:rt.pound,location:Pe(a,this.clonePosition())})}else if(s===60&&!this.ignoreTag&&this.peek()===47){if(i)break;return this.error(Oe.UNMATCHED_CLOSING_TAG,Pe(this.clonePosition(),this.clonePosition()))}else if(s===60&&!this.ignoreTag&&Ur(this.peek()||0)){var o=this.parseTag(e,n);if(o.err)return o;r.push(o.val)}else{var o=this.parseLiteral(e,n);if(o.err)return o;r.push(o.val)}}}return{val:r,err:null}},t.prototype.parseTag=function(e,n){var i=this.clonePosition();this.bump();var r=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:rt.literal,value:"<".concat(r,"/>"),location:Pe(i,this.clonePosition())},err:null};if(this.bumpIf(">")){var s=this.parseMessage(e+1,n,!0);if(s.err)return s;var o=s.val,a=this.clonePosition();if(this.bumpIf("</")){if(this.isEOF()||!Ur(this.char()))return this.error(Oe.INVALID_TAG,Pe(a,this.clonePosition()));var l=this.clonePosition(),u=this.parseTagName();return r!==u?this.error(Oe.UNMATCHED_CLOSING_TAG,Pe(l,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">")?{val:{type:rt.tag,value:r,children:o,location:Pe(i,this.clonePosition())},err:null}:this.error(Oe.INVALID_TAG,Pe(a,this.clonePosition())))}else return this.error(Oe.UNCLOSED_TAG,Pe(i,this.clonePosition()))}else return this.error(Oe.INVALID_TAG,Pe(i,this.clonePosition()))},t.prototype.parseTagName=function(){var e=this.offset();for(this.bump();!this.isEOF()&&Wc(this.char());)this.bump();return this.message.slice(e,this.offset())},t.prototype.parseLiteral=function(e,n){for(var i=this.clonePosition(),r="";;){var s=this.tryParseQuote(n);if(s){r+=s;continue}var o=this.tryParseUnquoted(e,n);if(o){r+=o;continue}var a=this.tryParseLeftAngleBracket();if(a){r+=a;continue}break}var l=Pe(i,this.clonePosition());return{val:{type:rt.literal,value:r,location:l},err:null}},t.prototype.tryParseLeftAngleBracket=function(){return!this.isEOF()&&this.char()===60&&(this.ignoreTag||!qc(this.peek()||0))?(this.bump(),"<"):null},t.prototype.tryParseQuote=function(e){if(this.isEOF()||this.char()!==39)return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if(e==="plural"||e==="selectordinal")break;return null;default:return null}this.bump();var n=[this.char()];for(this.bump();!this.isEOF();){var i=this.char();if(i===39)if(this.peek()===39)n.push(39),this.bump();else{this.bump();break}else n.push(i);this.bump()}return Hr.apply(void 0,n)},t.prototype.tryParseUnquoted=function(e,n){if(this.isEOF())return null;var i=this.char();return i===60||i===123||i===35&&(n==="plural"||n==="selectordinal")||i===125&&e>0?null:(this.bump(),Hr(i))},t.prototype.parseArgument=function(e,n){var i=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(Oe.EXPECT_ARGUMENT_CLOSING_BRACE,Pe(i,this.clonePosition()));if(this.char()===125)return this.bump(),this.error(Oe.EMPTY_ARGUMENT,Pe(i,this.clonePosition()));var r=this.parseIdentifierIfPossible().value;if(!r)return this.error(Oe.MALFORMED_ARGUMENT,Pe(i,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(Oe.EXPECT_ARGUMENT_CLOSING_BRACE,Pe(i,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:rt.argument,value:r,location:Pe(i,this.clonePosition())},err:null};case 44:return this.bump(),this.bumpSpace(),this.isEOF()?this.error(Oe.EXPECT_ARGUMENT_CLOSING_BRACE,Pe(i,this.clonePosition())):this.parseArgumentOptions(e,n,r,i);default:return this.error(Oe.MALFORMED_ARGUMENT,Pe(i,this.clonePosition()))}},t.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),n=this.offset(),i=zr(this.message,n),r=n+i.length;this.bumpTo(r);var s=this.clonePosition(),o=Pe(e,s);return{value:i,location:o}},t.prototype.parseArgumentOptions=function(e,n,i,r){var s,o=this.clonePosition(),a=this.parseIdentifierIfPossible().value,l=this.clonePosition();switch(a){case"":return this.error(Oe.EXPECT_ARGUMENT_TYPE,Pe(o,l));case"number":case"date":case"time":{this.bumpSpace();var u=null;if(this.bumpIf(",")){this.bumpSpace();var c=this.clonePosition(),h=this.parseSimpleArgStyleIfPossible();if(h.err)return h;var f=Gc(h.val);if(f.length===0)return this.error(Oe.EXPECT_ARGUMENT_STYLE,Pe(this.clonePosition(),this.clonePosition()));var d=Pe(c,this.clonePosition());u={style:f,styleLocation:d}}var m=this.tryParseArgumentClose(r);if(m.err)return m;var p=Pe(r,this.clonePosition());if(u&&Ls(u==null?void 0:u.style,"::",0)){var b=jc(u.style.slice(2));if(a==="number"){var h=this.parseNumberSkeletonFromString(b,u.styleLocation);return h.err?h:{val:{type:rt.number,value:i,location:p,style:h.val},err:null}}else{if(b.length===0)return this.error(Oe.EXPECT_DATE_TIME_SKELETON,p);var D=b;this.locale&&(D=Sc(b,this.locale));var f={type:Mn.dateTime,pattern:D,location:u.styleLocation,parsedOptions:this.shouldParseSkeletons?yc(D):{}},y=a==="date"?rt.date:rt.time;return{val:{type:y,value:i,location:p,style:f},err:null}}}return{val:{type:a==="number"?rt.number:a==="date"?rt.date:rt.time,value:i,location:p,style:(s=u==null?void 0:u.style)!==null&&s!==void 0?s:null},err:null}}case"plural":case"selectordinal":case"select":{var g=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(Oe.EXPECT_SELECT_ARGUMENT_OPTIONS,Pe(g,ze({},g)));this.bumpSpace();var A=this.parseIdentifierIfPossible(),w=0;if(a!=="select"&&A.value==="offset"){if(!this.bumpIf(":"))return this.error(Oe.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,Pe(this.clonePosition(),this.clonePosition()));this.bumpSpace();var h=this.tryParseDecimalInteger(Oe.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,Oe.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(h.err)return h;this.bumpSpace(),A=this.parseIdentifierIfPossible(),w=h.val}var E=this.tryParsePluralOrSelectOptions(e,a,n,A);if(E.err)return E;var m=this.tryParseArgumentClose(r);if(m.err)return m;var S=Pe(r,this.clonePosition());return a==="select"?{val:{type:rt.select,value:i,options:Rs(E.val),location:S},err:null}:{val:{type:rt.plural,value:i,options:Rs(E.val),offset:w,pluralType:a==="plural"?"cardinal":"ordinal",location:S},err:null}}default:return this.error(Oe.INVALID_ARGUMENT_TYPE,Pe(o,l))}},t.prototype.tryParseArgumentClose=function(e){return this.isEOF()||this.char()!==125?this.error(Oe.EXPECT_ARGUMENT_CLOSING_BRACE,Pe(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},t.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,n=this.clonePosition();!this.isEOF();){var i=this.char();switch(i){case 39:{this.bump();var r=this.clonePosition();if(!this.bumpUntil("'"))return this.error(Oe.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,Pe(r,this.clonePosition()));this.bump();break}case 123:{e+=1,this.bump();break}case 125:{if(e>0)e-=1;else return{val:this.message.slice(n.offset,this.offset()),err:null};break}default:this.bump();break}}return{val:this.message.slice(n.offset,this.offset()),err:null}},t.prototype.parseNumberSkeletonFromString=function(e,n){var i=[];try{i=kc(e)}catch{return this.error(Oe.INVALID_NUMBER_SKELETON,n)}return{val:{type:Mn.number,tokens:i,location:n,parsedOptions:this.shouldParseSkeletons?Cc(i):{}},err:null}},t.prototype.tryParsePluralOrSelectOptions=function(e,n,i,r){for(var s,o=!1,a=[],l=new Set,u=r.value,c=r.location;;){if(u.length===0){var h=this.clonePosition();if(n!=="select"&&this.bumpIf("=")){var f=this.tryParseDecimalInteger(Oe.EXPECT_PLURAL_ARGUMENT_SELECTOR,Oe.INVALID_PLURAL_ARGUMENT_SELECTOR);if(f.err)return f;c=Pe(h,this.clonePosition()),u=this.message.slice(h.offset,this.offset())}else break}if(l.has(u))return this.error(n==="select"?Oe.DUPLICATE_SELECT_ARGUMENT_SELECTOR:Oe.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,c);u==="other"&&(o=!0),this.bumpSpace();var d=this.clonePosition();if(!this.bumpIf("{"))return this.error(n==="select"?Oe.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:Oe.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,Pe(this.clonePosition(),this.clonePosition()));var m=this.parseMessage(e+1,n,i);if(m.err)return m;var p=this.tryParseArgumentClose(d);if(p.err)return p;a.push([u,{value:m.val,location:Pe(d,this.clonePosition())}]),l.add(u),this.bumpSpace(),s=this.parseIdentifierIfPossible(),u=s.value,c=s.location}return a.length===0?this.error(n==="select"?Oe.EXPECT_SELECT_ARGUMENT_SELECTOR:Oe.EXPECT_PLURAL_ARGUMENT_SELECTOR,Pe(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!o?this.error(Oe.MISSING_OTHER_CLAUSE,Pe(this.clonePosition(),this.clonePosition())):{val:a,err:null}},t.prototype.tryParseDecimalInteger=function(e,n){var i=1,r=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(i=-1);for(var s=!1,o=0;!this.isEOF();){var a=this.char();if(a>=48&&a<=57)s=!0,o=o*10+(a-48),this.bump();else break}var l=Pe(r,this.clonePosition());return s?(o*=i,zc(o)?{val:o,err:null}:this.error(n,l)):this.error(e,l)},t.prototype.offset=function(){return this.position.offset},t.prototype.isEOF=function(){return this.offset()===this.message.length},t.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},t.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var n=tl(this.message,e);if(n===void 0)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return n},t.prototype.error=function(e,n){return{val:null,err:{kind:e,message:this.message,location:n}}},t.prototype.bump=function(){if(!this.isEOF()){var e=this.char();e===10?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},t.prototype.bumpIf=function(e){if(Ls(this.message,e,this.offset())){for(var n=0;n<e.length;n++)this.bump();return!0}return!1},t.prototype.bumpUntil=function(e){var n=this.offset(),i=this.message.indexOf(e,n);return i>=0?(this.bumpTo(i),!0):(this.bumpTo(this.message.length),!1)},t.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var n=this.offset();if(n===e)break;if(n>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},t.prototype.bumpSpace=function(){for(;!this.isEOF()&&il(this.char());)this.bump()},t.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),n=this.offset(),i=this.message.charCodeAt(n+(e>=65536?2:1));return i??null},t}();function Ur(t){return t>=97&&t<=122||t>=65&&t<=90}function qc(t){return Ur(t)||t===47}function Wc(t){return t===45||t===46||t>=48&&t<=57||t===95||t>=97&&t<=122||t>=65&&t<=90||t==183||t>=192&&t<=214||t>=216&&t<=246||t>=248&&t<=893||t>=895&&t<=8191||t>=8204&&t<=8205||t>=8255&&t<=8256||t>=8304&&t<=8591||t>=11264&&t<=12271||t>=12289&&t<=55295||t>=63744&&t<=64975||t>=65008&&t<=65533||t>=65536&&t<=983039}function il(t){return t>=9&&t<=13||t===32||t===133||t>=8206&&t<=8207||t===8232||t===8233}function Zc(t){return t>=33&&t<=35||t===36||t>=37&&t<=39||t===40||t===41||t===42||t===43||t===44||t===45||t>=46&&t<=47||t>=58&&t<=59||t>=60&&t<=62||t>=63&&t<=64||t===91||t===92||t===93||t===94||t===96||t===123||t===124||t===125||t===126||t===161||t>=162&&t<=165||t===166||t===167||t===169||t===171||t===172||t===174||t===176||t===177||t===182||t===187||t===191||t===215||t===247||t>=8208&&t<=8213||t>=8214&&t<=8215||t===8216||t===8217||t===8218||t>=8219&&t<=8220||t===8221||t===8222||t===8223||t>=8224&&t<=8231||t>=8240&&t<=8248||t===8249||t===8250||t>=8251&&t<=8254||t>=8257&&t<=8259||t===8260||t===8261||t===8262||t>=8263&&t<=8273||t===8274||t===8275||t>=8277&&t<=8286||t>=8592&&t<=8596||t>=8597&&t<=8601||t>=8602&&t<=8603||t>=8604&&t<=8607||t===8608||t>=8609&&t<=8610||t===8611||t>=8612&&t<=8613||t===8614||t>=8615&&t<=8621||t===8622||t>=8623&&t<=8653||t>=8654&&t<=8655||t>=8656&&t<=8657||t===8658||t===8659||t===8660||t>=8661&&t<=8691||t>=8692&&t<=8959||t>=8960&&t<=8967||t===8968||t===8969||t===8970||t===8971||t>=8972&&t<=8991||t>=8992&&t<=8993||t>=8994&&t<=9e3||t===9001||t===9002||t>=9003&&t<=9083||t===9084||t>=9085&&t<=9114||t>=9115&&t<=9139||t>=9140&&t<=9179||t>=9180&&t<=9185||t>=9186&&t<=9254||t>=9255&&t<=9279||t>=9280&&t<=9290||t>=9291&&t<=9311||t>=9472&&t<=9654||t===9655||t>=9656&&t<=9664||t===9665||t>=9666&&t<=9719||t>=9720&&t<=9727||t>=9728&&t<=9838||t===9839||t>=9840&&t<=10087||t===10088||t===10089||t===10090||t===10091||t===10092||t===10093||t===10094||t===10095||t===10096||t===10097||t===10098||t===10099||t===10100||t===10101||t>=10132&&t<=10175||t>=10176&&t<=10180||t===10181||t===10182||t>=10183&&t<=10213||t===10214||t===10215||t===10216||t===10217||t===10218||t===10219||t===10220||t===10221||t===10222||t===10223||t>=10224&&t<=10239||t>=10240&&t<=10495||t>=10496&&t<=10626||t===10627||t===10628||t===10629||t===10630||t===10631||t===10632||t===10633||t===10634||t===10635||t===10636||t===10637||t===10638||t===10639||t===10640||t===10641||t===10642||t===10643||t===10644||t===10645||t===10646||t===10647||t===10648||t>=10649&&t<=10711||t===10712||t===10713||t===10714||t===10715||t>=10716&&t<=10747||t===10748||t===10749||t>=10750&&t<=11007||t>=11008&&t<=11055||t>=11056&&t<=11076||t>=11077&&t<=11078||t>=11079&&t<=11084||t>=11085&&t<=11123||t>=11124&&t<=11125||t>=11126&&t<=11157||t===11158||t>=11159&&t<=11263||t>=11776&&t<=11777||t===11778||t===11779||t===11780||t===11781||t>=11782&&t<=11784||t===11785||t===11786||t===11787||t===11788||t===11789||t>=11790&&t<=11798||t===11799||t>=11800&&t<=11801||t===11802||t===11803||t===11804||t===11805||t>=11806&&t<=11807||t===11808||t===11809||t===11810||t===11811||t===11812||t===11813||t===11814||t===11815||t===11816||t===11817||t>=11818&&t<=11822||t===11823||t>=11824&&t<=11833||t>=11834&&t<=11835||t>=11836&&t<=11839||t===11840||t===11841||t===11842||t>=11843&&t<=11855||t>=11856&&t<=11857||t===11858||t>=11859&&t<=11903||t>=12289&&t<=12291||t===12296||t===12297||t===12298||t===12299||t===12300||t===12301||t===12302||t===12303||t===12304||t===12305||t>=12306&&t<=12307||t===12308||t===12309||t===12310||t===12311||t===12312||t===12313||t===12314||t===12315||t===12316||t===12317||t>=12318&&t<=12319||t===12320||t===12336||t===64830||t===64831||t>=65093&&t<=65094}function jr(t){t.forEach(function(e){if(delete e.location,Za(e)||Xa(e))for(var n in e.options)delete e.options[n].location,jr(e.options[n].value);else Va(e)&&Ja(e.style)||(qa(e)||Wa(e))&&Mr(e.style)?delete e.style.location:Ya(e)&&jr(e.children)})}function Xc(t,e){e===void 0&&(e={}),e=ze({shouldParseSkeletons:!0,requiresOtherClause:!0},e);var n=new Vc(t,e).parse();if(n.err){var i=SyntaxError(Oe[n.err.kind]);throw i.location=n.err.location,i.originalMessage=n.err.message,i}return e!=null&&e.captureLocation||jr(n.val),n.val}function _r(t,e){var n=e&&e.cache?e.cache:ef,i=e&&e.serializer?e.serializer:$c,r=e&&e.strategy?e.strategy:Jc;return r(t,{cache:n,serializer:i})}function Yc(t){return t==null||typeof t=="number"||typeof t=="boolean"}function rl(t,e,n,i){var r=Yc(i)?i:n(i),s=e.get(r);return typeof s>"u"&&(s=t.call(this,i),e.set(r,s)),s}function sl(t,e,n){var i=Array.prototype.slice.call(arguments,3),r=n(i),s=e.get(r);return typeof s>"u"&&(s=t.apply(this,i),e.set(r,s)),s}function es(t,e,n,i,r){return n.bind(e,t,i,r)}function Jc(t,e){var n=t.length===1?rl:sl;return es(t,this,n,e.cache.create(),e.serializer)}function Qc(t,e){return es(t,this,sl,e.cache.create(),e.serializer)}function Kc(t,e){return es(t,this,rl,e.cache.create(),e.serializer)}var $c=function(){return JSON.stringify(arguments)};function ts(){this.cache=Object.create(null)}ts.prototype.get=function(t){return this.cache[t]};ts.prototype.set=function(t,e){this.cache[t]=e};var ef={create:function(){return new ts}},gr={variadic:Qc,monadic:Kc},xn;(function(t){t.MISSING_VALUE="MISSING_VALUE",t.INVALID_VALUE="INVALID_VALUE",t.MISSING_INTL_API="MISSING_INTL_API"})(xn||(xn={}));var tr=function(t){er(e,t);function e(n,i,r){var s=t.call(this,n)||this;return s.code=i,s.originalMessage=r,s}return e.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},e}(Error),Is=function(t){er(e,t);function e(n,i,r,s){return t.call(this,'Invalid values for "'.concat(n,'": "').concat(i,'". Options are "').concat(Object.keys(r).join('", "'),'"'),xn.INVALID_VALUE,s)||this}return e}(tr),tf=function(t){er(e,t);function e(n,i,r){return t.call(this,'Value for "'.concat(n,'" must be of type ').concat(i),xn.INVALID_VALUE,r)||this}return e}(tr),nf=function(t){er(e,t);function e(n,i){return t.call(this,'The intl string context variable "'.concat(n,'" was not provided to the string "').concat(i,'"'),xn.MISSING_VALUE,i)||this}return e}(tr),Et;(function(t){t[t.literal=0]="literal",t[t.object=1]="object"})(Et||(Et={}));function rf(t){return t.length<2?t:t.reduce(function(e,n){var i=e[e.length-1];return!i||i.type!==Et.literal||n.type!==Et.literal?e.push(n):i.value+=n.value,e},[])}function sf(t){return typeof t=="function"}function Pi(t,e,n,i,r,s,o){if(t.length===1&&Fs(t[0]))return[{type:Et.literal,value:t[0].value}];for(var a=[],l=0,u=t;l<u.length;l++){var c=u[l];if(Fs(c)){a.push({type:Et.literal,value:c.value});continue}if(Ec(c)){typeof s=="number"&&a.push({type:Et.literal,value:n.getNumberFormat(e).format(s)});continue}var h=c.value;if(!(r&&h in r))throw new nf(h,o);var f=r[h];if(wc(c)){(!f||typeof f=="string"||typeof f=="number")&&(f=typeof f=="string"||typeof f=="number"?String(f):""),a.push({type:typeof f=="string"?Et.literal:Et.object,value:f});continue}if(qa(c)){var d=typeof c.style=="string"?i.date[c.style]:Mr(c.style)?c.style.parsedOptions:void 0;a.push({type:Et.literal,value:n.getDateTimeFormat(e,d).format(f)});continue}if(Wa(c)){var d=typeof c.style=="string"?i.time[c.style]:Mr(c.style)?c.style.parsedOptions:i.time.medium;a.push({type:Et.literal,value:n.getDateTimeFormat(e,d).format(f)});continue}if(Va(c)){var d=typeof c.style=="string"?i.number[c.style]:Ja(c.style)?c.style.parsedOptions:void 0;d&&d.scale&&(f=f*(d.scale||1)),a.push({type:Et.literal,value:n.getNumberFormat(e,d).format(f)});continue}if(Ya(c)){var m=c.children,p=c.value,b=r[p];if(!sf(b))throw new tf(p,"function",o);var D=Pi(m,e,n,i,r,s),y=b(D.map(function(w){return w.value}));Array.isArray(y)||(y=[y]),a.push.apply(a,y.map(function(w){return{type:typeof w=="string"?Et.literal:Et.object,value:w}}))}if(Za(c)){var g=c.options[f]||c.options.other;if(!g)throw new Is(c.value,f,Object.keys(c.options),o);a.push.apply(a,Pi(g.value,e,n,i,r));continue}if(Xa(c)){var g=c.options["=".concat(f)];if(!g){if(!Intl.PluralRules)throw new tr(`Intl.PluralRules is not available in this environment.
Try polyfilling it using "@formatjs/intl-pluralrules"
`,xn.MISSING_INTL_API,o);var A=n.getPluralRules(e,{type:c.pluralType}).select(f-(c.offset||0));g=c.options[A]||c.options.other}if(!g)throw new Is(c.value,f,Object.keys(c.options),o);a.push.apply(a,Pi(g.value,e,n,i,r,f-(c.offset||0)));continue}}return rf(a)}function of(t,e){return e?ze(ze(ze({},t||{}),e||{}),Object.keys(t).reduce(function(n,i){return n[i]=ze(ze({},t[i]),e[i]||{}),n},{})):t}function af(t,e){return e?Object.keys(t).reduce(function(n,i){return n[i]=of(t[i],e[i]),n},ze({},t)):t}function br(t){return{create:function(){return{get:function(e){return t[e]},set:function(e,n){t[e]=n}}}}}function lf(t){return t===void 0&&(t={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:_r(function(){for(var e,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];return new((e=Intl.NumberFormat).bind.apply(e,pr([void 0],n,!1)))},{cache:br(t.number),strategy:gr.variadic}),getDateTimeFormat:_r(function(){for(var e,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];return new((e=Intl.DateTimeFormat).bind.apply(e,pr([void 0],n,!1)))},{cache:br(t.dateTime),strategy:gr.variadic}),getPluralRules:_r(function(){for(var e,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];return new((e=Intl.PluralRules).bind.apply(e,pr([void 0],n,!1)))},{cache:br(t.pluralRules),strategy:gr.variadic})}}var ol=function(){function t(e,n,i,r){var s=this;if(n===void 0&&(n=t.defaultLocale),this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(l){var u=s.formatToParts(l);if(u.length===1)return u[0].value;var c=u.reduce(function(h,f){return!h.length||f.type!==Et.literal||typeof h[h.length-1]!="string"?h.push(f.value):h[h.length-1]+=f.value,h},[]);return c.length<=1?c[0]||"":c},this.formatToParts=function(l){return Pi(s.ast,s.locales,s.formatters,s.formats,l,void 0,s.message)},this.resolvedOptions=function(){var l;return{locale:((l=s.resolvedLocale)===null||l===void 0?void 0:l.toString())||Intl.NumberFormat.supportedLocalesOf(s.locales)[0]}},this.getAst=function(){return s.ast},this.locales=n,this.resolvedLocale=t.resolveLocale(n),typeof e=="string"){if(this.message=e,!t.__parse)throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var o=r||{};o.formatters;var a=bc(o,["formatters"]);this.ast=t.__parse(e,ze(ze({},a),{locale:this.resolvedLocale}))}else this.ast=e;if(!Array.isArray(this.ast))throw new TypeError("A message must be provided as a String or AST.");this.formats=af(t.formats,i),this.formatters=r&&r.formatters||lf(this.formatterCache)}return Object.defineProperty(t,"defaultLocale",{get:function(){return t.memoizedDefaultLocale||(t.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),t.memoizedDefaultLocale},enumerable:!1,configurable:!0}),t.memoizedDefaultLocale=null,t.resolveLocale=function(e){if(!(typeof Intl.Locale>"u")){var n=Intl.NumberFormat.supportedLocalesOf(e);return n.length>0?new Intl.Locale(n[0]):new Intl.Locale(typeof e=="string"?e:e[0])}},t.__parse=Xc,t.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},t}();function uf(t,e){if(e==null)return;if(e in t)return t[e];const n=e.split(".");let i=t;for(let r=0;r<n.length;r++)if(typeof i=="object"){if(r>0){const s=n.slice(r,n.length).join(".");if(s in i){i=i[s];break}}i=i[n[r]]}else i=void 0;return i}const an={},cf=(t,e,n)=>n&&(e in an||(an[e]={}),t in an[e]||(an[e][t]=n),n),al=(t,e)=>{if(e==null)return;if(e in an&&t in an[e])return an[e][t];const n=li(e);for(let i=0;i<n.length;i++){const r=n[i],s=hf(r,t);if(s)return cf(t,e,s)}};let ns;const Un=Zt({});function ff(t){return ns[t]||null}function is(t){return t in ns}function hf(t,e){if(!is(t))return null;const n=ff(t);return uf(n,e)}function df(t){if(t==null)return;const e=li(t);for(let n=0;n<e.length;n++){const i=e[n];if(is(i))return i}}function Bi(t,...e){delete an[t],Un.update(n=>(n[t]=gc.all([n[t]||{},...e]),n))}vn([Un],([t])=>Object.keys(t));Un.subscribe(t=>ns=t);const Qn={};function pf(t){Qn[t]=new Set}function mf(t,e){Qn[t].delete(e),Qn[t].size===0&&delete Qn[t]}function Kn(t){return Qn[t]}function _f(t){return li(t).map(e=>{const n=Kn(e);return[e,n?[...n]:[]]}).filter(([,e])=>e.length>0)}function Ui(t){return t==null?!1:li(t).some(e=>{var n;return(n=Kn(e))==null?void 0:n.size})}function gf(t,e){return Promise.all(e.map(i=>(mf(t,i),i().then(r=>r.default||r)))).then(i=>Bi(t,...i))}const Vn={};function ll(t){if(!Ui(t))return t in Vn?Vn[t]:Promise.resolve();const e=_f(t);return Vn[t]=Promise.all(e.map(([n,i])=>gf(n,i))).then(()=>{if(Ui(t))return ll(t);delete Vn[t]}),Vn[t]}function bf(t,e){Kn(t)||pf(t);const n=Kn(t);Kn(t).has(e)||(is(t)||Un.update(i=>(i[t]={},i)),n.add(e))}var Ps=Object.getOwnPropertySymbols,wf=Object.prototype.hasOwnProperty,Ef=Object.prototype.propertyIsEnumerable,vf=(t,e)=>{var n={};for(var i in t)wf.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(t!=null&&Ps)for(var i of Ps(t))e.indexOf(i)<0&&Ef.call(t,i)&&(n[i]=t[i]);return n};const yf={number:{scientific:{notation:"scientific"},engineering:{notation:"engineering"},compactLong:{notation:"compact",compactDisplay:"long"},compactShort:{notation:"compact",compactDisplay:"short"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}};function Df({locale:t,id:e}){console.warn(`[svelte-i18n] The message "${e}" was not found in "${li(t).join('", "')}".${Ui(fn())?`

Note: there are at least one loader still registered to this locale that wasn't executed.`:""}`)}const kf={fallbackLocale:null,loadingDelay:200,formats:yf,warnOnMissingMessages:!0,handleMissingMessage:void 0,ignoreTag:!0},Yn=kf;function Hn(){return Yn}function Af(t){const e=t,{formats:n}=e,i=vf(e,["formats"]);let r=t.fallbackLocale;if(t.initialLocale)try{ol.resolveLocale(t.initialLocale)&&(r=t.initialLocale)}catch{console.warn(`[svelte-i18n] The initial locale "${t.initialLocale}" is not a valid locale.`)}return i.warnOnMissingMessages&&(delete i.warnOnMissingMessages,i.handleMissingMessage==null?i.handleMissingMessage=Df:console.warn('[svelte-i18n] The "warnOnMissingMessages" option is deprecated. Please use the "handleMissingMessage" option instead.')),Object.assign(Yn,i,{initialLocale:r}),n&&("number"in n&&Object.assign(Yn.formats.number,n.number),"date"in n&&Object.assign(Yn.formats.date,n.date),"time"in n&&Object.assign(Yn.formats.time,n.time)),yn.set(r)}const wr=Zt(!1);var Ff=Object.defineProperty,Tf=Object.defineProperties,Cf=Object.getOwnPropertyDescriptors,Bs=Object.getOwnPropertySymbols,Sf=Object.prototype.hasOwnProperty,Lf=Object.prototype.propertyIsEnumerable,Ns=(t,e,n)=>e in t?Ff(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,Rf=(t,e)=>{for(var n in e||(e={}))Sf.call(e,n)&&Ns(t,n,e[n]);if(Bs)for(var n of Bs(e))Lf.call(e,n)&&Ns(t,n,e[n]);return t},Of=(t,e)=>Tf(t,Cf(e));let Gr;const ji=Zt(null);function Ms(t){return t.split("-").map((e,n,i)=>i.slice(0,n+1).join("-")).reverse()}function li(t,e=Hn().fallbackLocale){const n=Ms(t);return e?[...new Set([...n,...Ms(e)])]:n}function fn(){return Gr??void 0}ji.subscribe(t=>{Gr=t??void 0,typeof window<"u"&&t!=null&&document.documentElement.setAttribute("lang",t)});const If=t=>{if(t&&df(t)&&Ui(t)){const{loadingDelay:e}=Hn();let n;return typeof window<"u"&&fn()!=null&&e?n=window.setTimeout(()=>wr.set(!0),e):wr.set(!0),ll(t).then(()=>{ji.set(t)}).finally(()=>{clearTimeout(n),wr.set(!1)})}return ji.set(t)},yn=Of(Rf({},ji),{set:If}),Pf=()=>typeof window>"u"?null:window.navigator.language||window.navigator.languages[0],nr=t=>{const e=Object.create(null);return i=>{const r=JSON.stringify(i);return r in e?e[r]:e[r]=t(i)}};var Bf=Object.defineProperty,Gi=Object.getOwnPropertySymbols,ul=Object.prototype.hasOwnProperty,cl=Object.prototype.propertyIsEnumerable,xs=(t,e,n)=>e in t?Bf(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,rs=(t,e)=>{for(var n in e||(e={}))ul.call(e,n)&&xs(t,n,e[n]);if(Gi)for(var n of Gi(e))cl.call(e,n)&&xs(t,n,e[n]);return t},jn=(t,e)=>{var n={};for(var i in t)ul.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(t!=null&&Gi)for(var i of Gi(t))e.indexOf(i)<0&&cl.call(t,i)&&(n[i]=t[i]);return n};const si=(t,e)=>{const{formats:n}=Hn();if(t in n&&e in n[t])return n[t][e];throw new Error(`[svelte-i18n] Unknown "${e}" ${t} format.`)},Nf=nr(t=>{var e=t,{locale:n,format:i}=e,r=jn(e,["locale","format"]);if(n==null)throw new Error('[svelte-i18n] A "locale" must be set to format numbers');return i&&(r=si("number",i)),new Intl.NumberFormat(n,r)}),Mf=nr(t=>{var e=t,{locale:n,format:i}=e,r=jn(e,["locale","format"]);if(n==null)throw new Error('[svelte-i18n] A "locale" must be set to format dates');return i?r=si("date",i):Object.keys(r).length===0&&(r=si("date","short")),new Intl.DateTimeFormat(n,r)}),xf=nr(t=>{var e=t,{locale:n,format:i}=e,r=jn(e,["locale","format"]);if(n==null)throw new Error('[svelte-i18n] A "locale" must be set to format time values');return i?r=si("time",i):Object.keys(r).length===0&&(r=si("time","short")),new Intl.DateTimeFormat(n,r)}),Hf=(t={})=>{var e=t,{locale:n=fn()}=e,i=jn(e,["locale"]);return Nf(rs({locale:n},i))},zf=(t={})=>{var e=t,{locale:n=fn()}=e,i=jn(e,["locale"]);return Mf(rs({locale:n},i))},Uf=(t={})=>{var e=t,{locale:n=fn()}=e,i=jn(e,["locale"]);return xf(rs({locale:n},i))},jf=nr((t,e=fn())=>new ol(t,e,Hn().formats,{ignoreTag:Hn().ignoreTag})),Gf=(t,e={})=>{var n,i,r,s;let o=e;typeof t=="object"&&(o=t,t=o.id);const{values:a,locale:l=fn(),default:u}=o;if(l==null)throw new Error("[svelte-i18n] Cannot format a message without first setting the initial locale.");let c=al(t,l);if(!c)c=(s=(r=(i=(n=Hn()).handleMissingMessage)==null?void 0:i.call(n,{locale:l,id:t,defaultValue:u}))!=null?r:u)!=null?s:t;else if(typeof c!="string")return console.warn(`[svelte-i18n] Message with id "${t}" must be of type "string", found: "${typeof c}". Gettin its value through the "$format" method is deprecated; use the "json" method instead.`),c;if(!a)return c;let h=c;try{h=jf(c,l).format(a)}catch(f){f instanceof Error&&console.warn(`[svelte-i18n] Message "${t}" has syntax error:`,f.message)}return h},Vf=(t,e)=>Uf(e).format(t),qf=(t,e)=>zf(e).format(t),Wf=(t,e)=>Hf(e).format(t),Zf=(t,e=fn())=>al(t,e),ui=vn([yn,Un],()=>Gf);vn([yn],()=>Vf);vn([yn],()=>qf);vn([yn],()=>Wf);vn([yn,Un],()=>Zf);function Hs(t,e,n){const i=t.slice();return i[19]=e[n][0],i[20]=e[n][1],i[22]=n,i}function zs(t){let e,n,i=Ot(t[10]),r=[];for(let s=0;s<i.length;s+=1)r[s]=Us(Hs(t,i,s));return{c(){e=Z("div"),n=Z("nav");for(let s=0;s<r.length;s+=1)r[s].c();this.h()},l(s){e=X(s,"DIV",{class:!0});var o=U(e);n=X(o,"NAV",{class:!0});var a=U(n);for(let l=0;l<r.length;l+=1)r[l].l(a);a.forEach(v),o.forEach(v),this.h()},h(){_(n,"class","fillable svelte-1anp163"),G(n,"fill_width",t[3]),_(e,"class","nav-holder svelte-1anp163")},m(s,o){R(s,e,o),M(e,n);for(let a=0;a<r.length;a+=1)r[a]&&r[a].m(n,null)},p(s,o){if(o&23584){i=Ot(s[10]);let a;for(a=0;a<i.length;a+=1){const l=Hs(s,i,a);r[a]?r[a].p(l,o):(r[a]=Us(l),r[a].c(),r[a].m(n,null))}for(;a<r.length;a+=1)r[a].d(1);r.length=i.length}o&8&&G(n,"fill_width",s[3])},d(s){s&&v(e),Qi(r,s)}}}function Xf(t){let e,n=t[20]+"",i,r,s;return{c(){e=Z("a"),i=Ee(n),r=oe(),this.h()},l(o){e=X(o,"A",{href:!0,"data-sveltekit-reload":!0,class:!0});var a=U(e);i=ve(a,n),r=ae(a),a.forEach(v),this.h()},h(){_(e,"href",s=`${t[12]}/${t[19]}`),_(e,"data-sveltekit-reload",""),_(e,"class","svelte-1anp163"),G(e,"active",t[19]===t[11])},m(o,a){R(o,e,a),M(e,i),M(e,r)},p(o,a){a&1024&&n!==(n=o[20]+"")&&je(i,n),a&5120&&s!==(s=`${o[12]}/${o[19]}`)&&_(e,"href",s),a&3072&&G(e,"active",o[19]===o[11])},d(o){o&&v(e)}}}function Yf(t){let e,n=t[20]+"",i,r,s,o;function a(...l){return t[17](t[19],...l)}return{c(){e=Z("button"),i=Ee(n),r=oe(),this.h()},l(l){e=X(l,"BUTTON",{class:!0});var u=U(e);i=ve(u,n),r=ae(u),u.forEach(v),this.h()},h(){_(e,"class","svelte-1anp163"),G(e,"active",t[19]===t[11])},m(l,u){R(l,e,u),M(e,i),M(e,r),s||(o=Te(e,"click",a),s=!0)},p(l,u){t=l,u&1024&&n!==(n=t[20]+"")&&je(i,n),u&3072&&G(e,"active",t[19]===t[11])},d(l){l&&v(e),s=!1,o()}}}function Us(t){let e;function n(s,o){return s[5]?Yf:Xf}let i=n(t),r=i(t);return{c(){r.c(),e=fe()},l(s){r.l(s),e=fe()},m(s,o){r.m(s,o),R(s,e,o)},p(s,o){i===(i=n(s))&&r?r.p(s,o):(r.d(1),r=i(s),r&&(r.c(),r.m(e.parentNode,e)))},d(s){s&&v(e),r.d(s)}}}function js(t){let e,n,i,r,s,o,a,l=t[13]("common.built_with")+"",u,c,h,f="Gradio",d,m,p,b=t[13]("common.hosted_on")+"",D,y,g,A=`<span class="space-logo svelte-1anp163"><img src="${nc}" alt="Hugging Face Space" class="svelte-1anp163"/></span> Spaces`;return{c(){e=Z("div"),n=Z("span"),i=Z("a"),r=Ee(t[6]),o=oe(),a=Z("span"),u=Ee(l),c=oe(),h=Z("a"),h.textContent=f,d=Ee("."),m=oe(),p=Z("span"),D=Ee(b),y=oe(),g=Z("a"),g.innerHTML=A,this.h()},l(w){e=X(w,"DIV",{class:!0});var E=U(e);n=X(E,"SPAN",{class:!0});var S=U(n);i=X(S,"A",{href:!0,class:!0});var z=U(i);r=ve(z,t[6]),z.forEach(v),S.forEach(v),o=ae(E),a=X(E,"SPAN",{class:!0});var C=U(a);u=ve(C,l),c=ae(C),h=X(C,"A",{class:!0,href:!0,"data-svelte-h":!0}),Pn(h)!=="svelte-jht9bl"&&(h.textContent=f),d=ve(C,"."),C.forEach(v),m=ae(E),p=X(E,"SPAN",{class:!0});var N=U(p);D=ve(N,b),y=ae(N),g=X(N,"A",{class:!0,href:!0,"data-svelte-h":!0}),Pn(g)!=="svelte-d5sowi"&&(g.innerHTML=A),N.forEach(v),E.forEach(v),this.h()},h(){_(i,"href",s="https://huggingface.co/spaces/"+t[6]),_(i,"class","title svelte-1anp163"),_(n,"class","svelte-1anp163"),_(h,"class","gradio svelte-1anp163"),_(h,"href","https://gradio.app"),_(a,"class","svelte-1anp163"),_(g,"class","hf svelte-1anp163"),_(g,"href","https://huggingface.co/spaces"),_(p,"class","svelte-1anp163"),_(e,"class","info svelte-1anp163")},m(w,E){R(w,e,E),M(e,n),M(n,i),M(i,r),M(e,o),M(e,a),M(a,u),M(a,c),M(a,h),M(a,d),M(e,m),M(e,p),M(p,D),M(p,y),M(p,g)},p(w,E){E&64&&je(r,w[6]),E&64&&s!==(s="https://huggingface.co/spaces/"+w[6])&&_(i,"href",s),E&8192&&l!==(l=w[13]("common.built_with")+"")&&je(u,l),E&8192&&b!==(b=w[13]("common.hosted_on")+"")&&je(D,b)},d(w){w&&v(e)}}}function Jf(t){let e,n,i,r,s,o,a,l=t[10].length>1&&zs(t);const u=t[16].default,c=Ut(u,t,t[15],null);let h=t[7]&&t[6]&&t[8]&&js(t);return{c(){e=Z("div"),l&&l.c(),n=oe(),i=Z("main"),c&&c.c(),r=oe(),s=Z("div"),h&&h.c(),this.h()},l(f){e=X(f,"DIV",{class:!0,"data-iframe-height":!0});var d=U(e);l&&l.l(d),n=ae(d),i=X(d,"MAIN",{class:!0});var m=U(i);c&&c.l(m),r=ae(m),s=X(m,"DIV",{});var p=U(s);h&&h.l(p),p.forEach(v),m.forEach(v),d.forEach(v),this.h()},h(){_(i,"class","fillable svelte-1anp163"),G(i,"fill_width",t[3]),G(i,"app",!t[7]&&!t[4]),_(e,"class",o="gradio-container gradio-container-"+t[1]+" svelte-1anp163"),_(e,"data-iframe-height",""),G(e,"fill_width",t[3]),G(e,"embed-container",t[7]),G(e,"with-info",t[8]),Y(e,"min-height",t[9]?"initial":t[2]),Y(e,"flex-grow",t[7]?"auto":"1")},m(f,d){R(f,e,d),l&&l.m(e,null),M(e,n),M(e,i),c&&c.m(i,null),M(i,r),M(i,s),h&&h.m(s,null),t[18](e),a=!0},p(f,[d]){f[10].length>1?l?l.p(f,d):(l=zs(f),l.c(),l.m(e,n)):l&&(l.d(1),l=null),c&&c.p&&(!a||d&32768)&&jt(c,u,f,f[15],a?Vt(u,f[15],d,null):Gt(f[15]),null),f[7]&&f[6]&&f[8]?h?h.p(f,d):(h=js(f),h.c(),h.m(s,null)):h&&(h.d(1),h=null),(!a||d&8)&&G(i,"fill_width",f[3]),(!a||d&144)&&G(i,"app",!f[7]&&!f[4]),(!a||d&2&&o!==(o="gradio-container gradio-container-"+f[1]+" svelte-1anp163"))&&_(e,"class",o),(!a||d&10)&&G(e,"fill_width",f[3]),(!a||d&130)&&G(e,"embed-container",f[7]),(!a||d&258)&&G(e,"with-info",f[8]),d&516&&Y(e,"min-height",f[9]?"initial":f[2]),d&128&&Y(e,"flex-grow",f[7]?"auto":"1")},i(f){a||(B(c,f),a=!0)},o(f){j(c,f),a=!1},d(f){f&&v(e),l&&l.d(),c&&c.d(f),h&&h.d(),t[18](null)}}}function Qf(t,e,n){let i;Rt(t,ui,E=>n(13,i=E));let{$$slots:r={},$$scope:s}=e,{wrapper:o}=e,{version:a}=e,{initial_height:l}=e,{fill_width:u}=e,{is_embed:c}=e,{is_lite:h}=e,{space:f}=e,{display:d}=e,{info:m}=e,{loaded:p}=e,{pages:b=[]}=e,{current_page:D=""}=e,{root:y}=e;const g=Pa("set_lite_page"),A=(E,S)=>{S.preventDefault(),g==null||g(E)};function w(E){Je[E?"unshift":"push"](()=>{o=E,n(0,o)})}return t.$$set=E=>{"wrapper"in E&&n(0,o=E.wrapper),"version"in E&&n(1,a=E.version),"initial_height"in E&&n(2,l=E.initial_height),"fill_width"in E&&n(3,u=E.fill_width),"is_embed"in E&&n(4,c=E.is_embed),"is_lite"in E&&n(5,h=E.is_lite),"space"in E&&n(6,f=E.space),"display"in E&&n(7,d=E.display),"info"in E&&n(8,m=E.info),"loaded"in E&&n(9,p=E.loaded),"pages"in E&&n(10,b=E.pages),"current_page"in E&&n(11,D=E.current_page),"root"in E&&n(12,y=E.root),"$$scope"in E&&n(15,s=E.$$scope)},[o,a,l,u,c,h,f,d,m,p,b,D,y,i,g,s,r,A,w]}class Kf extends qe{constructor(e){super(),We(this,e,Qf,Jf,Xe,{wrapper:0,version:1,initial_height:2,fill_width:3,is_embed:4,is_lite:5,space:6,display:7,info:8,loaded:9,pages:10,current_page:11,root:12})}}let fl=!1;typeof window<"u"&&"attachShadow"in Element.prototype&&"adoptedStyleSheets"in Document.prototype&&(fl="adoptedStyleSheets"in document.createElement("div").attachShadow({mode:"open"}));function Tg(t,e){const n=new URL(import.meta.url).origin;var i=t;if(window.location.origin!==n&&(i=new URL(t,n).href),document.querySelector(`link[href='${i}']`))return Promise.resolve();const s=document.createElement("link");return s.rel="stylesheet",s.href=i,new Promise((o,a)=>{s.addEventListener("load",()=>o()),s.addEventListener("error",()=>{console.error(`Unable to preload CSS for ${i}`),o()}),e.appendChild(s)})}function Gs(t,e,n){if(!fl)return t;n||(n=document.createElement("style")),n.remove();const i=new CSSStyleSheet;i.replaceSync(t);let r="";t=t.replace(/@import\s+url\((.*?)\);\s*/g,(l,u)=>(r+=`@import url(${u});
`,""));const s=i.cssRules;let o="",a=`.gradio-container.gradio-container-${e} .contain `;for(let l=0;l<s.length;l++){const u=s[l];let c=u.cssText.includes(".dark");if(u instanceof CSSStyleRule){const h=u.selectorText;if(h){const f=h.replace(".dark","").split(",").map(d=>`${c?".dark":""} ${a} ${d.trim()} `).join(",");o+=u.cssText,o+=u.cssText.replace(h,f)}}else if(u instanceof CSSMediaRule){let h=`@media ${u.media.mediaText} {`;for(let f=0;f<u.cssRules.length;f++){const d=u.cssRules[f];if(d instanceof CSSStyleRule){let m=d.cssText.includes(".dark ");const p=d.selectorText,b=p.replace(".dark","").split(",").map(D=>`${m?".dark":""} ${a} ${D.trim()} `).join(",");h+=d.cssText.replace(p,b)}}h+="}",o+=h}else if(u instanceof CSSKeyframesRule){o+=`@keyframes ${u.name} {`;for(let h=0;h<u.cssRules.length;h++){const f=u.cssRules[h];f instanceof CSSKeyframeRule&&(o+=`${f.keyText} { ${f.style.cssText} }`)}o+="}"}else u instanceof CSSFontFaceRule&&(o+=`@font-face { ${u.style.cssText} }`)}return r+o}var Vs=Object.prototype.hasOwnProperty;function qs(t,e,n){for(n of t.keys())if($n(n,e))return n}function $n(t,e){var n,i,r;if(t===e)return!0;if(t&&e&&(n=t.constructor)===e.constructor){if(n===Date)return t.getTime()===e.getTime();if(n===RegExp)return t.toString()===e.toString();if(n===Array){if((i=t.length)===e.length)for(;i--&&$n(t[i],e[i]););return i===-1}if(n===Set){if(t.size!==e.size)return!1;for(i of t)if(r=i,r&&typeof r=="object"&&(r=qs(e,r),!r)||!e.has(r))return!1;return!0}if(n===Map){if(t.size!==e.size)return!1;for(i of t)if(r=i[0],r&&typeof r=="object"&&(r=qs(e,r),!r)||!$n(i[1],e.get(r)))return!1;return!0}if(n===ArrayBuffer)t=new Uint8Array(t),e=new Uint8Array(e);else if(n===DataView){if((i=t.byteLength)===e.byteLength)for(;i--&&t.getInt8(i)===e.getInt8(i););return i===-1}if(ArrayBuffer.isView(t)){if((i=t.byteLength)===e.byteLength)for(;i--&&t[i]===e[i];);return i===-1}if(!n||typeof t=="object"){i=0;for(n in t)if(Vs.call(t,n)&&++i&&!Vs.call(e,n)||!(n in e)||!$n(t[n],e[n]))return!1;return Object.keys(e).length===i}}return t!==t&&e!==e}const $f={accordion:{component:()=>P(()=>import("./Index.CU-sllhp.js"),__vite__mapDeps([0,1]),import.meta.url)},annotatedimage:{component:()=>P(()=>import("./Index.ZlUMhLz3.js"),__vite__mapDeps([2,3,4,5,6,7,8]),import.meta.url)},audio:{base:()=>P(()=>import("./StaticAudio.CEB3a_-s.js"),__vite__mapDeps([9,3,4,10,11,12,13,6,14,15,16,17,18,19,20]),import.meta.url),example:()=>P(()=>import("./Example.BioRcyBn.js"),__vite__mapDeps([21,22]),import.meta.url),component:()=>P(()=>import("./index.-LvVKGMO.js"),__vite__mapDeps([23,9,3,4,10,11,12,13,6,14,15,16,17,18,19,20,24,25,26,27,28,29,30,31,32,33,21,22]),import.meta.url)},box:{component:()=>P(()=>import("./Index.CHyVMB6E.js"),[],import.meta.url)},browserstate:{component:()=>P(()=>import("./Index.DQB3Bzkd.js"),__vite__mapDeps([34,35]),import.meta.url)},button:{component:()=>P(()=>import("./Index.CfFYKADH.js"),[],import.meta.url)},chatbot:{component:()=>P(()=>import("./Index.Bv2l-SRz.js"),__vite__mapDeps([36,29,17,6,37,38,11,39,13,3,40]),import.meta.url)},checkbox:{example:()=>P(()=>import("./Example.7rhdKtvr.js"),__vite__mapDeps([41,22]),import.meta.url),component:()=>P(()=>import("./Index.DVcrV1NY.js"),__vite__mapDeps([42,43,44]),import.meta.url)},checkboxgroup:{example:()=>P(()=>import("./Example.CO-COJlt.js"),__vite__mapDeps([45,22]),import.meta.url),component:()=>P(()=>import("./Index.DbcE8rmG.js"),__vite__mapDeps([46,47]),import.meta.url)},code:{example:()=>P(()=>import("./Example.HHpueynA.js"),__vite__mapDeps([48,49]),import.meta.url),component:()=>P(()=>import("./Index.B7Jp9MPG.js").then(t=>t.K),__vite__mapDeps([50,25,12,20,6,51,3,4,48,49,52]),import.meta.url)},colorpicker:{example:()=>P(()=>import("./Example.BtYdbMfl.js"),__vite__mapDeps([53,54]),import.meta.url),component:()=>P(()=>import("./Index.C4qVdpCH.js"),__vite__mapDeps([55,56,53,54,57]),import.meta.url)},column:{component:()=>P(()=>Promise.resolve().then(()=>z_),void 0,import.meta.url)},core:{component:()=>P(()=>import("./index.CoUW42Ur.js"),[],import.meta.url)},dataframe:{example:()=>P(()=>import("./Example.Meu2o5J6.js"),__vite__mapDeps([58,59]),import.meta.url),component:()=>P(()=>import("./Index.D6J2PVPn.js"),__vite__mapDeps([60,61,62,38,26,27,43,44,63,7,64,65,3,4,10,11,12,5,6,66,20,67,30,31,68,33,69,70,58,59,71]),import.meta.url)},dataset:{component:()=>P(()=>import("./Index.DHWTYgOZ.js"),__vite__mapDeps([72,73,74]),import.meta.url)},datetime:{example:()=>P(()=>import("./Example.p5DpXI6u.js"),[],import.meta.url),component:()=>P(()=>import("./Index.OQ2NBVln.js"),__vite__mapDeps([75,76,77]),import.meta.url)},downloadbutton:{component:()=>P(()=>import("./Index.Dzsrn0Y_.js"),__vite__mapDeps([78,79]),import.meta.url)},dropdown:{example:()=>P(()=>import("./Example.AaUiXLZ_.js"),__vite__mapDeps([80,22]),import.meta.url),component:()=>P(()=>import("./Index.B10FuoQa.js"),__vite__mapDeps([81,63,82,83,80,22]),import.meta.url)},file:{example:()=>P(()=>import("./Example.CZOpiQG0.js"),__vite__mapDeps([84,85]),import.meta.url),component:()=>P(()=>import("./Index.CZNYq4H7.js"),__vite__mapDeps([86,87,3,4,37,26,27,6,20,88,33,84,85]),import.meta.url)},fileexplorer:{example:()=>P(()=>import("./Example.DbEDtHpU.js"),__vite__mapDeps([89,90]),import.meta.url),component:()=>P(()=>import("./Index.B70BNVP7.js"),__vite__mapDeps([91,37,3,92]),import.meta.url)},form:{component:()=>P(()=>Promise.resolve().then(()=>l_),void 0,import.meta.url)},gallery:{base:()=>P(()=>import("./Gallery.BgdW0y-K.js"),__vite__mapDeps([93,3,4,10,11,12,5,16,6,7,28,29,17,20,94,18,95,61,96,27]),import.meta.url),component:()=>P(()=>import("./Index.BJYxUfB0.js"),__vite__mapDeps([97,33,26,27,93,3,4,10,11,12,5,16,6,7,28,29,17,20,94,18,95,61,96,87,37,88,85]),import.meta.url)},group:{component:()=>P(()=>import("./Index.BgwHXxYk.js"),__vite__mapDeps([98,99]),import.meta.url)},highlightedtext:{component:()=>P(()=>import("./Index.D56cqZJI.js"),__vite__mapDeps([100,101,3,4,102]),import.meta.url)},html:{base:()=>P(()=>import("./Index.vSoPi4Pe.js"),__vite__mapDeps([103,51,3,104]),import.meta.url),example:()=>P(()=>import("./Example.BBdSzwmg.js"),__vite__mapDeps([105,106]),import.meta.url),component:()=>P(()=>import("./Index.vSoPi4Pe.js"),__vite__mapDeps([103,51,3,104]),import.meta.url)},image:{base:()=>P(()=>import("./ImagePreview.B6xPgKdu.js"),__vite__mapDeps([65,3,4,10,11,12,5,6,7,66,20]),import.meta.url),example:()=>P(()=>import("./Example.BnEs1fCL.js"),__vite__mapDeps([69,70]),import.meta.url),component:()=>P(()=>import("./Index.BekbgQye.js"),__vite__mapDeps([64,65,3,4,10,11,12,5,6,7,66,20,67,30,26,27,63,31,68,33,69,70]),import.meta.url)},imageeditor:{example:()=>P(()=>import("./Example.DU87hEH5.js"),__vite__mapDeps([107,67,3,5,30,26,27,6,7,66,63,31,68,108,70]),import.meta.url),component:()=>P(()=>import("./Index.Bd_GXR6d.js").then(t=>t.as),__vite__mapDeps([109,65,3,4,10,11,12,5,6,7,66,20,67,30,26,27,63,31,68,56,25,39,17,110,70]),import.meta.url)},imageslider:{example:()=>P(()=>import("./Example.C3O8QWdg.js"),__vite__mapDeps([111,112]),import.meta.url),component:()=>P(()=>import("./Index.Cm0Yofoi.js"),__vite__mapDeps([113,114,115,3,4,12,5,17,6,7,20,26,27,33,116]),import.meta.url)},json:{example:()=>P(()=>import("./Example.VAz9a1G1.js"),__vite__mapDeps([117,118,4,6,119,120]),import.meta.url),component:()=>P(()=>import("./Index.n4nGh_FC.js"),__vite__mapDeps([121,118,4,6,119,3]),import.meta.url)},label:{component:()=>P(()=>import("./Index.Bi4RUrG3.js"),__vite__mapDeps([122,123,3,4,124]),import.meta.url)},markdown:{example:()=>P(()=>import("./Example.4x8pwdna.js"),__vite__mapDeps([125,22]),import.meta.url),component:()=>P(()=>import("./Index.3nCFs0Nn.js"),__vite__mapDeps([126,6,125,22,127]),import.meta.url)},model3d:{example:()=>P(()=>import("./Example.V12cx6oG.js"),__vite__mapDeps([128,22]),import.meta.url),component:()=>P(()=>import("./Index.innrePmL.js"),__vite__mapDeps([129,25,3,12,37,17,6,26,27,28,29,20,4,33,128,22,130]),import.meta.url)},multimodaltextbox:{example:()=>P(()=>import("./Example.CLHP3nyK.js"),__vite__mapDeps([131,94,18,95,132]),import.meta.url),component:()=>P(()=>import("./Index.CnBkVKz_.js"),__vite__mapDeps([133,37,30,26,27,13,134,24,25,28,12,29,17,6,20,3,31,14,15,16,4,18,19,32,131,94,95,132,135]),import.meta.url)},nativeplot:{example:()=>P(()=>import("./Example.B-yMNQmV.js"),[],import.meta.url),component:()=>P(()=>import("./Index.HDGqOq9a.js"),__vite__mapDeps([136,25,4,123,6,7,137]),import.meta.url)},number:{example:()=>P(()=>import("./Example.Du75eb0D.js"),__vite__mapDeps([138,22]),import.meta.url),component:()=>P(()=>import("./Index.B1U3d4hY.js"),__vite__mapDeps([139,140]),import.meta.url)},paramviewer:{example:()=>P(()=>import("./Example.qq1Un4eO.js"),__vite__mapDeps([141,22]),import.meta.url),component:()=>P(()=>import("./Index.CbpaiuHS.js"),__vite__mapDeps([142,143]),import.meta.url)},plot:{base:()=>P(()=>import("./Plot.BOh0qlQz.js").then(t=>t.b),__vite__mapDeps([144,25,4]),import.meta.url),component:()=>P(()=>import("./Index.DVGOl5fo.js"),__vite__mapDeps([145,144,25,4,3,6,7]),import.meta.url)},radio:{example:()=>P(()=>import("./Example.Dl9poLS6.js"),__vite__mapDeps([146,22]),import.meta.url),component:()=>P(()=>import("./Index.DP0ejsHH.js"),__vite__mapDeps([147,146,22,148]),import.meta.url)},row:{component:()=>P(()=>import("./Index.MLf-ptpy.js"),__vite__mapDeps([149,150]),import.meta.url)},sidebar:{component:()=>P(()=>import("./Index.B_t3vyeP.js"),__vite__mapDeps([151,152]),import.meta.url)},sketchbox:{component:()=>P(()=>import("./Index.WpI12aFk.js"),__vite__mapDeps([153,154]),import.meta.url)},slider:{example:()=>P(()=>import("./Example.FcPgDijm.js"),__vite__mapDeps([155,22]),import.meta.url),component:()=>P(()=>import("./Index.DXH7H1Cc.js"),__vite__mapDeps([156,157]),import.meta.url)},state:{component:()=>P(()=>import("./Index.CIqK-Zw-.js"),[],import.meta.url)},statustracker:{component:()=>P(()=>import("./index.eE3WRTCe.js"),__vite__mapDeps([158,31]),import.meta.url)},tabitem:{component:()=>P(()=>import("./Index.C3gJvtqi.js"),__vite__mapDeps([159,160,161,162]),import.meta.url)},tabs:{component:()=>P(()=>import("./Index.CXpQskGr.js"),__vite__mapDeps([163,160,161]),import.meta.url)},textbox:{example:()=>P(()=>import("./Example.DtHlUNEL.js"),[],import.meta.url),component:()=>P(()=>import("./Index.BICvNBdW.js"),__vite__mapDeps([164,73]),import.meta.url)},timer:{component:()=>P(()=>import("./Index.B85Go98U.js"),[],import.meta.url)},uploadbutton:{component:()=>P(()=>import("./Index.BfN4FUC5.js"),__vite__mapDeps([165,166]),import.meta.url)},video:{base:()=>P(()=>import("./VideoPreview.BI_25RC8.js").then(t=>t.a),__vite__mapDeps([167,3,4,10,11,12,134,6,20,15,16,17,94,18,95,28,29,168,27]),import.meta.url),example:()=>P(()=>import("./Example.Dzb6Yct_.js"),__vite__mapDeps([169,94,18,95,170]),import.meta.url),component:()=>P(()=>import("./index.CMpgkpBY.js"),__vite__mapDeps([171,26,27,3,134,30,67,5,6,7,66,63,31,68,94,18,95,167,4,10,11,12,20,15,16,17,28,29,168,169,170,33,172,70]),import.meta.url)}},nn={},ss=typeof window<"u";function Vr({api_url:t,name:e,id:n,variant:i}){var a,l,u,c;const r=ss&&window.__GRADIO__CC__,s={...$f,...r||{}};let o=n||e;if(nn[`${o}-${i}`])return{component:nn[`${o}-${i}`],name:e};try{if(!((a=s==null?void 0:s[o])!=null&&a[i])&&!((l=s==null?void 0:s[e])!=null&&l[i]))throw new Error;return nn[`${o}-${i}`]=(((u=s==null?void 0:s[o])==null?void 0:u[i])||((c=s==null?void 0:s[e])==null?void 0:c[i]))(),{name:e,component:nn[`${o}-${i}`]}}catch{if(!o)throw new Error(`Component not found: ${e}`);try{return nn[`${o}-${i}`]=eh(t,o,i),{name:e,component:nn[`${o}-${i}`]}}catch(f){if(i==="example")return nn[`${o}-${i}`]=P(()=>import("./Example.cOvhHY2p.js"),__vite__mapDeps([173,22]),import.meta.url),{name:e,component:nn[`${o}-${i}`]};throw console.error(`failed to load: ${e}`),console.error(f),f}}}function Ws(t){return ss?new Promise((e,n)=>{const i=document.createElement("link");i.rel="stylesheet",i.href=t,document.head.appendChild(i),i.onload=()=>e(),i.onerror=()=>n()}):Promise.resolve()}function eh(t,e,n){const i=ss?"client":"server";let r;return i==="server"?Promise.all([Ws(`${t}/custom_component/${e}/${n}/style.css`),P(()=>import("./Index.BW3mQ8xb.js"),__vite__mapDeps([174,175]),import.meta.url)]).then(([s,o])=>o):(r=`${t}/custom_component/${e}/${i}/${n}/index.js`,Promise.all([Ws(`${t}/custom_component/${e}/${i}/${n}/style.css`),import(r)]).then(([s,o])=>o))}function th(){const t=Zt({}),e={},n={},i=new Map,r=new Map,s=new Map,o={};function a({fn_index:u,status:c,queue:h=!0,size:f,position:d=null,eta:m=null,message:p=null,progress:b,time_limit:D=null}){const y=n[u],g=e[u],A=o[u],w=y.map(E=>{let S;const z=i.get(E)||0;if(A==="pending"&&c!=="pending"){let C=z-1;i.set(E,C<0?0:C),S=C>0?"pending":c}else A==="pending"&&c==="pending"?S="pending":A!=="pending"&&c==="pending"?(S="pending",i.set(E,z+1)):S=c;return{id:E,queue_position:d,queue_size:f,eta:m,status:S,message:p,progress:b}});g.forEach(E=>{const S=r.get(E)||0;if(A==="pending"&&c!=="pending"){let z=S-1;r.set(E,z<0?0:z),s.set(E,c)}else A!=="pending"&&c==="pending"?(r.set(E,S+1),s.set(E,c)):s.delete(E)}),t.update(E=>(w.forEach(({id:S,queue_position:z,queue_size:C,eta:N,status:re,message:x,progress:le})=>{E[S]={queue:h,queue_size:C,queue_position:z,eta:N,message:x,progress:le,status:re,fn_index:u}}),E)),o[u]=c}function l(u,c,h){e[u]=c,n[u]=h}return{update:a,register:l,subscribe:t.subscribe,get_status_for_fn(u){return o[u]},get_inputs_to_update(){return s}}}let pn=[];const nh=typeof window<"u",Zs=nh?requestAnimationFrame:async t=>await t();function hl({initial_layout:t=void 0}={initial_layout:void 0}){let e,n=Zt({}),i={},r,s,o,a,l=th();const u=Zt(t);let c=[],h,f={},d,m=null;function p(Q){m=Q}let b,D;function y(Q){Q.forEach(O=>{O.targets.forEach(I=>{var k,J;const L=a[I[0]];L&&((k=O.event_specific_args)==null?void 0:k.length)>0&&((J=O.event_specific_args)==null||J.forEach(V=>{L.props[V]=O[V]}))})})}async function g({app:Q,components:O,layout:I,dependencies:L,root:k,options:J}){N(),h=Q,a&&O.forEach(V=>{if(V.props.value==null&&V.key){const $=Object.values(a).find(q=>q.key===V.key);$&&(V.props.value=$.props.value)}}),c=O,r=new Set,s=new Set,pn=[],o=new Map,e=new Map,a={},b=I,D=k,d={id:I.id,type:"column",props:{interactive:!1,scale:J.fill_height?1:null},has_modes:!1,instance:null,component:null,component_class_id:"",key:null},O.push(d),L.forEach(V=>{l.register(V.id,V.inputs,V.show_progress_on||V.outputs),V.frontend_fn=Xs(V.js,!!V.backend_fn,V.inputs.length,V.outputs.length),Ys(V.targets,V.id,i),Js(V,r,s)}),n.set(i),o=Qs(O,I,k),a=O.reduce((V,$)=>(V[$.id]=$,V),{}),await w(I,k,c),u.set(d),y(L)}function A({render_id:Q,components:O,layout:I,root:L,dependencies:k}){b=I,D=L,O.forEach(he=>{for(const Me in he.props)he.props[Me]===null&&(he.props[Me]=void 0)});let J=[],V=[];O.forEach(he=>{var Me;he.key==null||!((Me=f[Q])!=null&&Me.includes(he.key))?V.push(he):J.push(he)}),Qs(V,I,L).forEach((he,Me)=>{o.set(Me,he)}),i={},k.forEach(he=>{l.register(he.id,he.inputs,he.outputs),he.frontend_fn=Xs(he.js,!!he.backend_fn,he.inputs.length,he.outputs.length),Ys(he.targets,he.id,i),Js(he,r,s)}),n.set(i);let q=a[I.id];const Ce=he=>{he.children&&he.children.forEach(Me=>{Ce(Me)})};Ce(q),Object.entries(a).forEach(([he,Me])=>{var $e;let it=Number(he);if(Me.rendered_in===Q){let ct=J.find(et=>et.key===Me.key);if(Me.key!=null&&ct!==void 0){const et=a[Me.id];for(const st in ct.props)($e=ct.props.preserved_by_key)!=null&&$e.includes(st)||(et.props[st]=ct.props[st])}else delete a[it],e.has(it)&&e.delete(it)}}),V.concat(J.filter(he=>!a[he.id])).forEach(he=>{a[he.id]=he,e.set(he.id,he)}),q.parent&&(q.parent.children[q.parent.children.indexOf(q)]=a[I.id]),w(I,L,c.concat(O),q.parent).then(()=>{u.set(d),f[Q]=O.map(he=>he.key).filter(he=>he!=null)}),y(k)}async function w(Q,O,I,L){var J,V,$;const k=a[Q.id];if(!k.component){const q=k.component_class_id||k.type;let Ce=o.get(q);Ce&&(k.component=(J=await Ce)==null?void 0:J.default)}if(k.parent=L,k.type==="dataset"&&(k.props.component_map=Wr(k.type,k.component_class_id,O,I,k.props.components).example_components),i[k.id]&&(k.props.attached_events=Object.keys(i[k.id])),k.props.interactive=rh(k.id,k.props.interactive,k.props.value,r,s),k.props.server=sh(k.id,k.props.server_fns,h),e.set(k.id,k),Q.children&&(k.children=await Promise.all(Q.children.map(q=>w(q,O,I,k)))),k.type==="tabs"&&!k.props.initial_tabs){const Ce=(((V=Q.children)==null?void 0:V.map((Ze,he)=>{var it;const Me=a[Ze.id];return(it=Me.props).id??(it.id=Ze.id),{type:Me.type,props:{...Me.props,id:Me.props.id,order:he}}}))||[]).filter(Ze=>Ze.type==="tabitem");k.props.initial_tabs=Ce==null?void 0:Ce.map(Ze=>({label:Ze.props.label,id:Ze.props.id,visible:typeof Ze.props.visible=="boolean"?Ze.props.visible:!0,interactive:Ze.props.interactive,order:Ze.props.order}))}return k.type==="tabs"&&(($=Q.children)==null||$.forEach((q,Ce)=>{const Ze=a[q.id];Ze.props.order=Ce})),k}let E=!1,S=Zt(!1);async function z(Q){var I,L;if(Q.size===0)return;const O=c.filter(k=>Q.has(k.id));for(const k of O){const J=k.component_class_id||k.type;if(o.has(J))k.component=((L=await o.get(J))==null?void 0:L.default)??k.component;else{const{component:V,example_components:$}=Wr(k.type,k.component_class_id,D,c);if(o.set(J,V),$)for(const[q,Ce]of $)o.set(q,Ce);k.component||(k.component=(I=await V)==null?void 0:I.default)}}}function C(Q){return Q.some(O=>O.some(I=>{const L=a[I.id];return L?I.prop==="visible"||I.prop==="selected"&&L.type==="tabs":!1}))}function N(){const Q=C(pn);let O;Q&&b&&(O=Vi(b,c)),u.update(I=>{for(let L=0;L<pn.length;L++)for(let k=0;k<pn[L].length;k++){const J=pn[L][k];if(!J)continue;const V=a[J.id];if(!V)continue;let $;const q=V.props[J.prop];J.value instanceof Map?$=new Map(J.value):J.value instanceof Set?$=new Set(J.value):Array.isArray(J.value)?$=[...J.value]:J.value==null?$=null:typeof J.value=="object"?$={...J.value}:$=J.value,V.props[J.prop]=$,J.prop==="value"&&!dl(V)&&!$n(q,$)&&(m==null||m(J.id,$))}return I}),Q&&b&&O&&Zs(async()=>{const I=Vi(b,c),L=new Set;for(const k of I)O.has(k)||L.add(k);await z(L),L.size>0&&u.update(k=>k)}),pn=[],E=!1,S.set(!1)}function re(Q){Q&&(pn.push(Q),E||(E=!0,S.set(!0),Zs(N)))}function x(Q){var I;let O=e.get(Q);if(!O){const L=$r(u);O=le(L,Q)}return O?(I=O.instance)!=null&&I.get_value?O.instance.get_value():O.props.value:null}function le(Q,O){if(Q.id===O)return Q;if(Q.children)for(const I of Q.children){const L=le(I,O);if(L)return L}}function me(Q,O){var L;const I=e.get(Q);I&&((L=I.instance)!=null&&L.modify_stream_state)&&I.instance.modify_stream_state(O)}function ke(Q){var I;const O=e.get(Q);return(I=O==null?void 0:O.instance)!=null&&I.get_stream_state?O.instance.get_stream_state():"not_set"}function ue(Q,O){var L;const I=e.get(Q);(L=I==null?void 0:I.instance)!=null&&L.set_time_limit&&I.instance.set_time_limit(O)}return{layout:u,targets:n,update_value:re,get_data:x,modify_stream:me,get_stream_state:ke,set_time_limit:ue,loading_status:l,scheduled_updates:S,create_layout:g,rerender_layout:A,value_change:p}}const qr=Object.getPrototypeOf(async function(){}).constructor;function Xs(t,e,n,i){if(!t||t===!0)return null;const r=e?n===1:i===1;try{return new qr("__fn_args",`  let result = await (${t})(...__fn_args);
  if (typeof result === "undefined") return [];
  return (${r} && !Array.isArray(result)) ? [result] : result;`)}catch(s){return console.error("Could not parse custom js method."),console.error(s),null}}function Ys(t,e,n){return t.forEach(([i,r])=>{var s,o;n[i]||(n[i]={}),(s=n[i])!=null&&s[r]&&!((o=n[i])!=null&&o[r].includes(e))?n[i][r].push(e):n[i][r]=[e]}),n}function Js(t,e,n){return t.inputs.forEach(i=>e.add(i)),t.outputs.forEach(i=>n.add(i)),[e,n]}function ih(t){return Array.isArray(t)&&t.length===0||t===""||t===0||!t}function rh(t,e,n,i,r){return e===!1?!1:e===!0?!0:!!(i.has(t)||!r.has(t)&&ih(n))}function sh(t,e,n){return e?e.reduce((i,r)=>(i[r]=async(...s)=>(s.length===1&&(s=s[0]),await n.component_server(t,r,s)),i),{}):{}}function Wr(t,e,n,i,r){let s=new Map;t==="api"&&(t="state"),t==="dataset"&&r&&r.forEach(a=>{if(s.has(a))return;let l;const u=i.find(c=>c.type===a);u&&(l=Vr({api_url:n,name:a,id:u.component_class_id,variant:"example"}),s.set(a,l.component))});const o=Vr({api_url:n,name:t,id:e,variant:"component"});return{component:o.component,name:o.name,example_components:s.size>0?s:void 0}}function oh(t,e,n){const i=(n==null?void 0:n.selected_tab_id)===t.id||(n==null?void 0:n.selected_tab_id)===t.props.id;return e&&i}function ah(t,e,n){const i=t.props.selected;if(typeof i=="string"||typeof i=="number")return i;if(e.children)for(const r of e.children){const s=n.find(o=>o.id===r.id);if((s==null?void 0:s.type)==="tabitem"&&s.props.visible!==!1&&s.props.interactive!==!1)return s.id||s.props.id}}function Er(t,e,n){const i=new Set;if(t.children)for(const r of t.children)Vi(r,e,!0,n).forEach(o=>i.add(o));return i}function Vi(t,e,n=!0,i){const r=new Set,s=e.find(a=>a.id===t.id);if(!s)return r;const o=n&&(typeof s.props.visible=="boolean"?s.props.visible:!0);if(s.type==="tabitem")return oh(s,o,i)&&(r.add(s.id),Er(t,e,i).forEach(l=>r.add(l))),r;if(s.type==="tabs"){if(o){r.add(s.id);const a=ah(s,t,e);Er(t,e,{selected_tab_id:a}).forEach(u=>r.add(u))}return r}return o&&(r.add(s.id),Er(t,e,i).forEach(l=>r.add(l))),r}function Qs(t,e,n){let i=new Map;const r=Vi(e,t);return t.forEach(s=>{if(r.has(s.id)){const{component:o,example_components:a}=Wr(s.type,s.component_class_id,n,t);if(i.set(s.component_class_id||s.type,o),a)for(const[l,u]of a)i.set(l,u)}}),i}function dl(t){return typeof t.props.visible=="boolean"&&t.props.visible===!1?!1:t.parent?dl(t.parent):!0}const Cg=["red","green","blue","yellow","purple","teal","orange","cyan","lime","pink"],lh=[{color:"red",primary:600,secondary:100},{color:"green",primary:600,secondary:100},{color:"blue",primary:600,secondary:100},{color:"yellow",primary:500,secondary:100},{color:"purple",primary:600,secondary:100},{color:"teal",primary:600,secondary:100},{color:"orange",primary:600,secondary:100},{color:"cyan",primary:600,secondary:100},{color:"lime",primary:500,secondary:100},{color:"pink",primary:600,secondary:100}],Ks={inherit:"inherit",current:"currentColor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"#f8fafc",100:"#f1f5f9",200:"#e2e8f0",300:"#cbd5e1",400:"#94a3b8",500:"#64748b",600:"#475569",700:"#334155",800:"#1e293b",900:"#0f172a",950:"#020617"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827",950:"#030712"},zinc:{50:"#fafafa",100:"#f4f4f5",200:"#e4e4e7",300:"#d4d4d8",400:"#a1a1aa",500:"#71717a",600:"#52525b",700:"#3f3f46",800:"#27272a",900:"#18181b",950:"#09090b"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},stone:{50:"#fafaf9",100:"#f5f5f4",200:"#e7e5e4",300:"#d6d3d1",400:"#a8a29e",500:"#78716c",600:"#57534e",700:"#44403c",800:"#292524",900:"#1c1917",950:"#0c0a09"},red:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d",950:"#450a0a"},orange:{50:"#fff7ed",100:"#ffedd5",200:"#fed7aa",300:"#fdba74",400:"#fb923c",500:"#f97316",600:"#ea580c",700:"#c2410c",800:"#9a3412",900:"#7c2d12",950:"#431407"},amber:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f",950:"#451a03"},yellow:{50:"#fefce8",100:"#fef9c3",200:"#fef08a",300:"#fde047",400:"#facc15",500:"#eab308",600:"#ca8a04",700:"#a16207",800:"#854d0e",900:"#713f12",950:"#422006"},lime:{50:"#f7fee7",100:"#ecfccb",200:"#d9f99d",300:"#bef264",400:"#a3e635",500:"#84cc16",600:"#65a30d",700:"#4d7c0f",800:"#3f6212",900:"#365314",950:"#1a2e05"},green:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d",950:"#052e16"},emerald:{50:"#ecfdf5",100:"#d1fae5",200:"#a7f3d0",300:"#6ee7b7",400:"#34d399",500:"#10b981",600:"#059669",700:"#047857",800:"#065f46",900:"#064e3b",950:"#022c22"},teal:{50:"#f0fdfa",100:"#ccfbf1",200:"#99f6e4",300:"#5eead4",400:"#2dd4bf",500:"#14b8a6",600:"#0d9488",700:"#0f766e",800:"#115e59",900:"#134e4a",950:"#042f2e"},cyan:{50:"#ecfeff",100:"#cffafe",200:"#a5f3fc",300:"#67e8f9",400:"#22d3ee",500:"#06b6d4",600:"#0891b2",700:"#0e7490",800:"#155e75",900:"#164e63",950:"#083344"},sky:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e",950:"#082f49"},blue:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a",950:"#172554"},indigo:{50:"#eef2ff",100:"#e0e7ff",200:"#c7d2fe",300:"#a5b4fc",400:"#818cf8",500:"#6366f1",600:"#4f46e5",700:"#4338ca",800:"#3730a3",900:"#312e81",950:"#1e1b4b"},violet:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95",950:"#2e1065"},purple:{50:"#faf5ff",100:"#f3e8ff",200:"#e9d5ff",300:"#d8b4fe",400:"#c084fc",500:"#a855f7",600:"#9333ea",700:"#7e22ce",800:"#6b21a8",900:"#581c87",950:"#3b0764"},fuchsia:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75",950:"#4a044e"},pink:{50:"#fdf2f8",100:"#fce7f3",200:"#fbcfe8",300:"#f9a8d4",400:"#f472b6",500:"#ec4899",600:"#db2777",700:"#be185d",800:"#9d174d",900:"#831843",950:"#500724"},rose:{50:"#fff1f2",100:"#ffe4e6",200:"#fecdd3",300:"#fda4af",400:"#fb7185",500:"#f43f5e",600:"#e11d48",700:"#be123c",800:"#9f1239",900:"#881337",950:"#4c0519"}},Sg=lh.reduce((t,{color:e,primary:n,secondary:i})=>({...t,[e]:{primary:Ks[e][n],secondary:Ks[e][i]}}),{});class vr extends Error{constructor(e){super(e),this.name="ShareError"}}async function Lg(t,e){var l;if(window.__gradio_space__==null)throw new vr("Must be on Spaces to share.");let n,i,r;{let u;if(typeof t=="object"&&t.url)u=t.url;else if(typeof t=="string")u=t;else throw new Error("Invalid data format for URL type");const c=await fetch(u);n=await c.blob(),i=c.headers.get("content-type")||"",r=c.headers.get("content-disposition")||""}const s=new File([n],r,{type:i}),o=await fetch("https://huggingface.co/uploads",{method:"POST",body:s,headers:{"Content-Type":s.type,"X-Requested-With":"XMLHttpRequest"}});if(!o.ok){if((l=o.headers.get("content-type"))!=null&&l.includes("application/json")){const u=await o.json();throw new vr(`Upload failed: ${u.error}`)}throw new vr("Upload failed.")}return await o.text()}function Rg(t){t.addEventListener("click",e);async function e(n){const i=n.composedPath(),[r]=i.filter(s=>(s==null?void 0:s.tagName)==="BUTTON"&&s.classList.contains("copy_code_button"));if(r){let s=function(u){u.style.opacity="1",setTimeout(()=>{u.style.opacity="0"},2e3)};n.stopImmediatePropagation();const o=r.parentElement.innerText.trim(),a=Array.from(r.children)[1];await uh(o)&&s(a)}}return{destroy(){t.removeEventListener("click",e)}}}async function uh(t){let e=!1;if("clipboard"in navigator)await navigator.clipboard.writeText(t),e=!0;else{const n=document.createElement("textarea");n.value=t,n.style.position="absolute",n.style.left="-999999px",document.body.prepend(n),n.select();try{document.execCommand("copy"),e=!0}catch(i){console.error(i),e=!1}finally{n.remove()}}return e}const Og=t=>{const e=Math.floor(t/3600),n=Math.floor(t%3600/60),i=Math.round(t)%60,r=`${n<10?"0":""}${n}`,s=`${i<10?"0":""}${i}`;return e>0?`${e}:${r}:${s}`:`${n}:${s}`},ch=typeof window<"u";var oi,In;class fh{constructor(e,n,i,r,s,o,a,l=h=>h,u,c){Ln(this,oi,void 0);K(this,"theme");K(this,"version");K(this,"i18n");Ln(this,In,void 0);K(this,"root");K(this,"autoscroll");K(this,"max_file_size");K(this,"client");K(this,"_load_component");K(this,"load_component",hh.bind(this));Di(this,oi,e),this.theme=i,this.version=r,Di(this,In,n),this.max_file_size=a,this.i18n=l,this.root=s,this.autoscroll=o,this.client=u,this._load_component=c}dispatch(e,n){if(!ch||!Kt(this,In))return;const i=new CustomEvent("gradio",{bubbles:!0,detail:{data:n,id:Kt(this,oi),event:e}});Kt(this,In).dispatchEvent(i)}}oi=new WeakMap,In=new WeakMap;function hh(t,e="component"){var n;return this._load_component({name:t,api_url:(n=this.client.config)==null?void 0:n.root,variant:e})}const Ig=t=>typeof t=="number"?t+"px":t;function pl(t){if(t==null)return"";const e=String(t),n=$r(ui);let i=n(e);if(i!==e)return i;const r=e.toLowerCase();for(const s of bh){const o=s.substring(s.indexOf(".")+1);if(r===o){const a=n(s);if(a!==s)return a;break}}return e}const dh=vn(ui,()=>pl),$s={ar:"جاري التحميل",ca:"S'està carregant",ckb:"بارکردن",de:"Laden",en:"Loading",es:"Cargando",eu:"Kargatzen",fa:"در حال بارگذاری",fi:"Ladataan",fr:"Chargement",he:"טוען",hi:"लोड हो रहा है",ja:"読み込み中",ko:"로딩 중",lt:"Kraunama",nb:"Laster",nl:"Laden",pl:"Ładowanie","pt-BR":"Carregando",pt:"A carregar",ro:"Se încarcă",ru:"Загрузка",sv:"Laddar",ta:"ஏற்றுகிறது",th:"กำลังโหลด",tr:"Yükleniyor",uk:"Завантаження",ur:"لوڈ ہو رہا ہے",uz:"Yuklanmoqda","zh-CN":"加载中","zh-TW":"載入中"},ml="English",_l={annotated_image:"Annotated Image"},gl={allow_recording_access:"Please allow access to the microphone for recording.",audio:"Audio",drop_to_upload:"Drop an audio file here to upload",record_from_microphone:"Record from microphone",stop_recording:"Stop recording",no_device_support:"Media devices could not be accessed. Check that you are running on a secure origin (https) or localhost (or you have passed a valid SSL certificate to ssl_verify), and you have allowed browser access to your device.",stop:"Stop",resume:"Resume",record:"Record",no_microphone:"No microphone found",pause:"Pause",play:"Play",waiting:"Waiting"},bl={connection_can_break:"On mobile, the connection can break if this tab is unfocused or the device sleeps, losing your position in queue.",long_requests_queue:"There is a long queue of requests pending. Duplicate this Space to skip.",lost_connection:"Lost connection due to leaving page. Rejoining queue...",waiting_for_inputs:"Waiting for file(s) to finish uploading, please retry."},wl={edit:"Edit",retry:"Retry",undo:"Undo",submit:"Submit",cancel:"Cancel",like:"Like",dislike:"Dislike",clear:"Clear"},El={checkbox:"Checkbox",checkbox_group:"Checkbox Group"},vl={code:"Code"},yl={color_picker:"Color Picker"},Dl={built_with:"built with",built_with_gradio:"Built with Gradio",clear:"Clear",download:"Download",edit:"Edit",empty:"Empty",error:"Error",hosted_on:"Hosted on",loading:"Loading",logo:"logo",or:"or",remove:"Remove",settings:"Settings",share:"Share",submit:"Submit",undo:"Undo",no_devices:"No devices found",language:"Language",display_theme:"Display Theme",pwa:"Progressive Web App",record:"Record",stop_recording:"Stop Recording",screen_studio:"Screen Studio",share_gradio_tab:"[Sharing] Gradio Tab",run:"Run"},kl={incorrect_format:"Incorrect format, only CSV and TSV files are supported",new_column:"Add column",new_row:"New row",add_row_above:"Add row above",add_row_below:"Add row below",delete_row:"Delete row",delete_column:"Delete column",add_column_left:"Add column to the left",add_column_right:"Add column to the right",sort_column:"Sort column",sort_ascending:"Sort ascending",sort_descending:"Sort descending",drop_to_upload:"Drop CSV or TSV files here to import data into dataframe",clear_sort:"Clear sort",filter:"Filter",clear_filter:"Clear filters"},Al={dropdown:"Dropdown"},Fl={build_error:"there is a build error",config_error:"there is a config error",contact_page_author:"Please contact the author of the page to let them know.",no_app_file:"there is no app file",runtime_error:"there is a runtime error",space_not_working:`"Space isn't working because" {0}`,space_paused:"the space is paused",use_via_api:"Use via API",use_via_api_or_mcp:"Use via API or MCP"},Tl={uploading:"Uploading..."},Cl={highlighted_text:"Highlighted Text"},Sl={allow_webcam_access:"Please allow access to the webcam for recording.",brush_color:"Brush color",brush_radius:"Brush radius",image:"Image",remove_image:"Remove Image",select_brush_color:"Select brush color",start_drawing:"Start drawing",use_brush:"Use brush",drop_to_upload:"Drop an image file here to upload"},Ll={label:"Label"},Rl={enable_cookies:"If you are visiting a HuggingFace Space in Incognito mode, you must enable third party cookies.",incorrect_credentials:"Incorrect Credentials",username:"username",password:"password",login:"Login"},Ol={number:"Number"},Il={plot:"Plot"},Pl={radio:"Radio"},Bl={slider:"Slider"},Nl={click_to_upload:"Click to Upload",drop_audio:"Drop Audio Here",drop_csv:"Drop CSV Here",drop_file:"Drop File Here",drop_image:"Drop Image Here",drop_video:"Drop Video Here",drop_gallery:"Drop Media Here",paste_clipboard:"Paste from Clipboard"},Ml={drop_to_upload:"Drop a video file here to upload"},os={_name:ml,"3D_model":{"3d_model":"3D Model",drop_to_upload:"Drop a 3D model (.obj, .glb, .stl, .gltf, .splat, or .ply) file here to upload"},annotated_image:_l,audio:gl,blocks:bl,chatbot:wl,checkbox:El,code:vl,color_picker:yl,common:Dl,dataframe:kl,dropdown:Al,errors:Fl,file:Tl,highlighted_text:Cl,image:Sl,label:Ll,login:Rl,number:Ol,plot:Il,radio:Pl,slider:Bl,upload_text:Nl,video:Ml},ph=Object.freeze(Object.defineProperty({__proto__:null,_name:ml,annotated_image:_l,audio:gl,blocks:bl,chatbot:wl,checkbox:El,code:vl,color_picker:yl,common:Dl,dataframe:kl,default:os,dropdown:Al,errors:Fl,file:Tl,highlighted_text:Cl,image:Sl,label:Ll,login:Rl,number:Ol,plot:Il,radio:Pl,slider:Bl,upload_text:Nl,video:Ml},Symbol.toStringTag,{value:"Module"})),mh={ar:"العربية",ca:"Català",ckb:"کوردی",de:"Deutsch",en:"English",es:"Español",eu:"Euskara",fa:"فارسی",fi:"Suomi",fr:"Français",he:"עברית",hi:"हिंदी",ja:"日本語",ko:"한국어",lt:"Lietuvių",nb:"Norsk bokmål",nl:"Nederlands",pl:"Polski","pt-BR":"Português do Brasil",pt:"Português",ro:"Română",ru:"Русский",sv:"Svenska",ta:"தமிழ்",th:"ภาษาไทย",tr:"Türkçe",uk:"Українська",ur:"اردو",uz:"O'zbek","zh-CN":"简体中文","zh-TW":"繁體中文"},_h=Object.assign({"./lang/ar.json":()=>P(()=>import("./ar.C14WhJAs.js"),[],import.meta.url),"./lang/ca.json":()=>P(()=>import("./ca.BawhR5IW.js"),[],import.meta.url),"./lang/ckb.json":()=>P(()=>import("./ckb.DJglGH6y.js"),[],import.meta.url),"./lang/de.json":()=>P(()=>import("./de.1aCSmaRd.js"),[],import.meta.url),"./lang/en.json":()=>P(()=>Promise.resolve().then(()=>ph),void 0,import.meta.url),"./lang/es.json":()=>P(()=>import("./es.DnL3K9UL.js"),[],import.meta.url),"./lang/eu.json":()=>P(()=>import("./eu.CVD4vzPg.js"),[],import.meta.url),"./lang/fa.json":()=>P(()=>import("./fa.EPplD9mS.js"),[],import.meta.url),"./lang/fi.json":()=>P(()=>import("./fi.B30SrT2N.js"),[],import.meta.url),"./lang/fr.json":()=>P(()=>import("./fr.BtMjEOuI.js"),[],import.meta.url),"./lang/he.json":()=>P(()=>import("./he.C03Xfbr8.js"),[],import.meta.url),"./lang/hi.json":()=>P(()=>import("./hi.DaWfWT5u.js"),[],import.meta.url),"./lang/ja.json":()=>P(()=>import("./ja.BTBiRAkN.js"),[],import.meta.url),"./lang/ko.json":()=>P(()=>import("./ko.C9Hiv2KW.js"),[],import.meta.url),"./lang/lt.json":()=>P(()=>import("./lt.Z_MxAT26.js"),[],import.meta.url),"./lang/nb.json":()=>P(()=>import("./nb.CGxYPyHM.js"),[],import.meta.url),"./lang/nl.json":()=>P(()=>import("./nl.Dw1IiFHs.js"),[],import.meta.url),"./lang/pl.json":()=>P(()=>import("./pl.CewrFAug.js"),[],import.meta.url),"./lang/pt-BR.json":()=>P(()=>import("./pt-BR.DZjK3tkX.js"),[],import.meta.url),"./lang/pt.json":()=>P(()=>import("./pt.DH1R3DI2.js"),[],import.meta.url),"./lang/ro.json":()=>P(()=>import("./ro.boSZHd3M.js"),[],import.meta.url),"./lang/ru.json":()=>P(()=>import("./ru.BgQGKwpu.js"),[],import.meta.url),"./lang/sv.json":()=>P(()=>import("./sv.D1nuPeJf.js"),[],import.meta.url),"./lang/ta.json":()=>P(()=>import("./ta.Ex4E4CrJ.js"),[],import.meta.url),"./lang/th.json":()=>P(()=>import("./th.DGNowuDO.js"),[],import.meta.url),"./lang/tr.json":()=>P(()=>import("./tr.Cez09yqM.js"),[],import.meta.url),"./lang/uk.json":()=>P(()=>import("./uk.C_6kyB1E.js"),[],import.meta.url),"./lang/ur.json":()=>P(()=>import("./ur.1gg1Zkwj.js"),[],import.meta.url),"./lang/uz.json":()=>P(()=>import("./uz.CVKNjEjc.js"),[],import.meta.url),"./lang/zh-CN.json":()=>P(()=>import("./zh-CN.C4t77Jdq.js"),[],import.meta.url),"./lang/zh-TW.json":()=>P(()=>import("./zh-TW.DnQkjK12.js"),[],import.meta.url)});function Pg(t){return t&&typeof t=="object"&&t.__type__==="translation_metadata"&&typeof t.key=="string"}function eo(t){if(typeof t!="string")return t;const e="__i18n__",n=t.indexOf(e);if(n===-1)return t;try{const i=n>0?t.substring(0,n):"",r=n+e.length,s=t.indexOf("{",r);let o=-1,a=0;for(let c=s;c<t.length;c++)if(t[c]==="{"&&a++,t[c]==="}"&&a--,a===0){o=c+1;break}if(o===-1)return console.error("Could not find end of JSON in i18n string"),t;const l=t.substring(s,o),u=o<t.length?t.substring(o):"";try{const c=JSON.parse(l);if(c&&c.key){const h=pl(c.key);return i+h+u}}catch(c){console.error("Error parsing i18n JSON:",c)}return t}catch(i){return console.error("Error processing translation:",i),t}}function gh(){return{...Object.fromEntries(Object.entries(_h).map(([e,n])=>[e.split("/").pop().split(".")[0],{type:"lazy",data:n}])),en:{type:"static",data:os}}}const as=gh(),to=Object.keys(as),Bg=Object.entries(as).map(([t])=>[mh[t]||t,t]);let bh=new Set,yr=!1,no;async function xl(t){if(yr&&!(yr&&t!==no))return;no=t,wh({processed_langs:as,custom_translations:t??{}});const n=Pf();let i=n&&to.includes(n)?n:null;if(!i){const r=n==null?void 0:n.split("-")[0];i=r&&to.includes(r)?r:"en"}await Af({fallbackLocale:"en",initialLocale:i}),yr=!0}function Ng(t){yn.set(t)}function Mg(t,e,n="en"){return t&&e.includes(t)?t:n}function wh(t){if(t){try{for(const e in t.custom_translations)Bi(e,t.custom_translations[e]);for(const e in t.processed_langs)e==="en"&&t.processed_langs[e].type==="static"?Bi(e,os):t.processed_langs[e].type==="lazy"&&bf(e,t.processed_langs[e].data)}catch(e){console.error("Error loading translations:",e)}for(const e in $s)Bi(e,{common:{loading:$s[e]}})}}var Dr=new Intl.Collator(0,{numeric:1}).compare;function Hl(t,e,n){return t=t.split("."),e=e.split("."),Dr(t[0],e[0])||Dr(t[1],e[1])||(e[2]=e.slice(2).join("."),n=/[.-]/.test(t[2]=t.slice(2).join(".")),n==/[.-]/.test(e[2])?Dr(t[2],e[2]):n?-1:1)}const Eh="host",zl="queue/data",vh="queue/join",io="upload",yh="login",ro="config",Dh="info",kh="runtime",Ah="sleeptime",Fh="heartbeat",Th="component_server",Ch="reset",Sh="cancel",Lh="app_id",Rh="https://gradio-space-api-fetcher-v2.hf.space/api",Ul="This application is currently busy. Please try again. ",_n="Connection errored out. ",sn="Could not resolve app config. ",Oh="Could not get space status. ",Ih="Could not get API info. ",ls="Space metadata could not be loaded. ",Ph="Invalid URL. A full URL path is required.",Bh="Not authorized to access this space. ",jl="Invalid credentials. Could not login. ",Gl="Login credentials are required to access this space.",Nh="File system access is only available in Node.js environments",Vl="Root URL not found in client config",Mh="Error uploading file";function xh(t,e,n){return e.startsWith("http://")||e.startsWith("https://")?n?t:e:t+e}async function so(t,e,n){try{return(await(await fetch(`https://huggingface.co/api/spaces/${t}/jwt`,{headers:{Authorization:`Bearer ${e}`,...n?{Cookie:n}:{}}})).json()).token||!1}catch{return!1}}function Hh(t){let e={};return t.forEach(({api_name:n,id:i})=>{n&&(e[n]=i)}),e}async function zh(t){const e=this.options.hf_token?{Authorization:`Bearer ${this.options.hf_token}`}:{};if(e["Content-Type"]="application/json",typeof window<"u"&&window.gradio_config&&location.origin!=="http://localhost:9876"&&!window.gradio_config.dev_mode)return window.gradio_config.current_page&&(t=t.substring(0,t.lastIndexOf("/"))),window.gradio_config.root=t,{...window.gradio_config};if(t){let n=Zl(t,this.deep_link?ro+"?deep_link="+this.deep_link:ro);const i=await this.fetch(n,{headers:e,credentials:"include"});return Uh(i,t,!!this.options.auth)}throw new Error(sn)}async function Uh(t,e,n){var i,r;if((t==null?void 0:t.status)===401&&!n){const s=await t.json(),o=(i=s==null?void 0:s.detail)==null?void 0:i.auth_message;throw new Error(o||Gl)}else if((t==null?void 0:t.status)===401&&n)throw new Error(jl);if((t==null?void 0:t.status)===200){let s=await t.json();return s.root=e,(r=s.dependencies)==null||r.forEach((o,a)=>{o.id===void 0&&(o.id=a)}),s}else if((t==null?void 0:t.status)===401)throw new Error(Bh);throw new Error(sn)}async function jh(){const{http_protocol:t,host:e}=await ir(this.app_reference,this.options.hf_token);try{if(this.options.auth){const n=await ql(t,e,this.options.auth,this.fetch,this.options.hf_token);n&&this.set_cookies(n)}}catch(n){throw Error(n.message)}}async function ql(t,e,n,i,r){const s=new FormData;s.append("username",n==null?void 0:n[0]),s.append("password",n==null?void 0:n[1]);let o={};r&&(o.Authorization=`Bearer ${r}`);const a=await i(`${t}//${e}/${yh}`,{headers:o,method:"POST",body:s,credentials:"include"});if(a.status===200)return a.headers.get("set-cookie");throw a.status===401?new Error(jl):new Error(ls)}function kr(t){if(t.startsWith("http")){const{protocol:e,host:n,pathname:i}=new URL(t);return{ws_protocol:e==="https:"?"wss":"ws",http_protocol:e,host:n+(i!=="/"?i:"")}}else if(t.startsWith("file:"))return{ws_protocol:"ws",http_protocol:"http:",host:"lite.local"};return{ws_protocol:"wss",http_protocol:"https:",host:new URL(t).host}}const Wl=t=>{let e=[];return t.split(/,(?=\s*[^\s=;]+=[^\s=;]+)/).forEach(i=>{const[r,s]=i.split(";")[0].split("=");r&&s&&e.push(`${r.trim()}=${s.trim()}`)}),e},us=/^[a-zA-Z0-9_\-\.]+\/[a-zA-Z0-9_\-\.]+$/,Gh=/.*hf\.space\/{0,1}.*$/;async function ir(t,e){const n={};e&&(n.Authorization=`Bearer ${e}`);const i=t.trim().replace(/\/$/,"");if(us.test(i))try{const s=(await(await fetch(`https://huggingface.co/api/spaces/${i}/${Eh}`,{headers:n})).json()).host;return{space_id:t,...kr(s)}}catch{throw new Error(ls)}if(Gh.test(i)){const{ws_protocol:r,http_protocol:s,host:o}=kr(i);return{space_id:o.split("/")[0].replace(".hf.space",""),ws_protocol:r,http_protocol:s,host:o}}return{space_id:!1,...kr(i)}}const Zl=(...t)=>{try{return t.reduce((e,n)=>(e=e.replace(/\/+$/,""),n=n.replace(/^\/+/,""),new URL(n,e+"/").toString()))}catch{throw new Error(Ph)}};function Vh(t,e,n){const i={named_endpoints:{},unnamed_endpoints:{}};return Object.keys(t).forEach(r=>{(r==="named_endpoints"||r==="unnamed_endpoints")&&(i[r]={},Object.entries(t[r]).forEach(([s,{parameters:o,returns:a}])=>{var h,f,d,m;const l=((h=e.dependencies.find(p=>p.api_name===s||p.api_name===s.replace("/","")))==null?void 0:h.id)||n[s.replace("/","")]||-1,u=l!==-1?(f=e.dependencies.find(p=>p.id==l))==null?void 0:f.types:{generator:!1,cancel:!1};if(l!==-1&&((m=(d=e.dependencies.find(p=>p.id==l))==null?void 0:d.inputs)==null?void 0:m.length)!==o.length){const p=e.dependencies.find(b=>b.id==l).inputs.map(b=>{var D;return(D=e.components.find(y=>y.id===b))==null?void 0:D.type});try{p.forEach((b,D)=>{if(b==="state"){const y={component:"state",example:null,parameter_default:null,parameter_has_default:!0,parameter_name:null,hidden:!0};o.splice(D,0,y)}})}catch(b){console.error(b)}}const c=(p,b,D,y)=>({...p,description:Wh(p==null?void 0:p.type,D),type:qh(p==null?void 0:p.type,b,D,y)||""});i[r][s]={parameters:o.map(p=>c(p,p==null?void 0:p.component,p==null?void 0:p.serializer,"parameter")),returns:a.map(p=>c(p,p==null?void 0:p.component,p==null?void 0:p.serializer,"return")),type:u}}))}),i}function qh(t,e,n,i){if(e==="Api")return t.type;switch(t==null?void 0:t.type){case"string":return"string";case"boolean":return"boolean";case"number":return"number"}if(n==="JSONSerializable"||n==="StringSerializable")return"any";if(n==="ListStringSerializable")return"string[]";if(e==="Image")return i==="parameter"?"Blob | File | Buffer":"string";if(n==="FileSerializable")return(t==null?void 0:t.type)==="array"?i==="parameter"?"(Blob | File | Buffer)[]":"{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}[]":i==="parameter"?"Blob | File | Buffer":"{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}";if(n==="GallerySerializable")return i==="parameter"?"[(Blob | File | Buffer), (string | null)][]":"[{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}, (string | null))][]"}function Wh(t,e){return e==="GallerySerializable"?"array of [file, label] tuples":e==="ListStringSerializable"?"array of strings":e==="FileSerializable"?"array of files or single file":t==null?void 0:t.description}function Ar(t,e){switch(t.msg){case"send_data":return{type:"data"};case"send_hash":return{type:"hash"};case"queue_full":return{type:"update",status:{queue:!0,message:Ul,stage:"error",code:t.code,success:t.success}};case"heartbeat":return{type:"heartbeat"};case"unexpected_error":return{type:"unexpected_error",status:{queue:!0,message:t.message,session_not_found:t.session_not_found,stage:"error",success:!1}};case"broken_connection":return{type:"broken_connection",status:{queue:!0,message:t.message,stage:"error",success:!1}};case"estimation":return{type:"update",status:{queue:!0,stage:e||"pending",code:t.code,size:t.queue_size,position:t.rank,eta:t.rank_eta,success:t.success}};case"progress":return{type:"update",status:{queue:!0,stage:"pending",code:t.code,progress_data:t.progress_data,success:t.success}};case"log":return{type:"log",data:t};case"process_generating":return{type:"generating",status:{queue:!0,message:t.success?null:t.output.error,stage:t.success?"generating":"error",code:t.code,progress_data:t.progress_data,eta:t.average_duration,changed_state_ids:t.success?t.output.changed_state_ids:void 0},data:t.success?t.output:null};case"process_streaming":return{type:"streaming",status:{queue:!0,message:t.output.error,stage:"streaming",time_limit:t.time_limit,code:t.code,progress_data:t.progress_data,eta:t.eta},data:t.output};case"process_completed":return"error"in t.output?{type:"update",status:{queue:!0,title:t.output.title,message:t.output.error,visible:t.output.visible,duration:t.output.duration,stage:"error",code:t.code,success:t.success}}:{type:"complete",status:{queue:!0,message:t.success?void 0:t.output.error,stage:t.success?"complete":"error",code:t.code,progress_data:t.progress_data,changed_state_ids:t.success?t.output.changed_state_ids:void 0},data:t.success?t.output:null};case"process_starts":return{type:"update",status:{queue:!0,stage:"pending",code:t.code,size:t.rank,position:0,success:t.success,eta:t.eta},original_msg:"process_starts"}}return{type:"none",status:{stage:"error",queue:!0}}}const Zh=(t=[],e)=>{const n=e?e.parameters:[];if(Array.isArray(t))return e&&n.length>0&&t.length>n.length&&console.warn("Too many arguments provided for the endpoint."),t;const i=[],r=Object.keys(t);return n.forEach((s,o)=>{if(t.hasOwnProperty(s.parameter_name))i[o]=t[s.parameter_name];else if(s.parameter_has_default)i[o]=s.parameter_default;else throw new Error(`No value provided for required parameter: ${s.parameter_name}`)}),r.forEach(s=>{if(!n.some(o=>o.parameter_name===s))throw new Error(`Parameter \`${s}\` is not a valid keyword argument. Please refer to the API for usage.`)}),i.forEach((s,o)=>{if(s===void 0&&!n[o].parameter_has_default)throw new Error(`No value provided for required parameter: ${n[o].parameter_name}`)}),i};async function Xh(){if(this.api_info)return this.api_info;const{hf_token:t}=this.options,{config:e}=this,n={"Content-Type":"application/json"};if(t&&(n.Authorization=`Bearer ${t}`),!!e)try{let i,r;if(typeof window<"u"&&window.gradio_api_info)r=window.gradio_api_info;else{if(Hl((e==null?void 0:e.version)||"2.0.0","3.30")<0)i=await this.fetch(Rh,{method:"POST",body:JSON.stringify({serialize:!1,config:JSON.stringify(e)}),headers:n,credentials:"include"});else{const s=Zl(e.root,this.api_prefix,Dh);i=await this.fetch(s,{headers:n,credentials:"include"})}if(!i.ok)throw new Error(_n);r=await i.json()}return"api"in r&&(r=r.api),r.named_endpoints["/predict"]&&!r.unnamed_endpoints[0]&&(r.unnamed_endpoints[0]=r.named_endpoints["/predict"]),Vh(r,e,this.api_map)}catch(i){throw new Error("Could not get API info. "+i.message)}}async function Yh(t,e,n){var a;const i={};(a=this==null?void 0:this.options)!=null&&a.hf_token&&(i.Authorization=`Bearer ${this.options.hf_token}`);const r=1e3,s=[];let o;for(let l=0;l<e.length;l+=r){const u=e.slice(l,l+r),c=new FormData;u.forEach(f=>{c.append("files",f)});try{const f=n?`${t}${this.api_prefix}/${io}?upload_id=${n}`:`${t}${this.api_prefix}/${io}`;o=await this.fetch(f,{method:"POST",body:c,headers:i,credentials:"include"})}catch(f){throw new Error(_n+f.message)}if(!o.ok){const f=await o.text();return{error:`HTTP ${o.status}: ${f}`}}const h=await o.json();h&&s.push(...h)}return{files:s}}async function Jh(t,e,n,i){let r=(Array.isArray(t)?t:[t]).map(o=>o.blob);const s=r.filter(o=>o.size>(i??1/0));if(s.length)throw new Error(`File size exceeds the maximum allowed size of ${i} bytes: ${s.map(o=>o.name).join(", ")}`);return await Promise.all(await this.upload_files(e,r,n).then(async o=>{if(o.error)throw new Error(o.error);return o.files?o.files.map((a,l)=>new rr({...t[l],path:a,url:`${e}${this.api_prefix}/file=${a}`})):[]}))}async function xg(t,e){return t.map(n=>new rr({path:n.name,orig_name:n.name,blob:n,size:n.size,mime_type:n.type,is_stream:e}))}class rr{constructor({path:e,url:n,orig_name:i,size:r,blob:s,is_stream:o,mime_type:a,alt_text:l,b64:u}){K(this,"path");K(this,"url");K(this,"orig_name");K(this,"size");K(this,"blob");K(this,"is_stream");K(this,"mime_type");K(this,"alt_text");K(this,"b64");K(this,"meta",{_type:"gradio.FileData"});this.path=e,this.url=n,this.orig_name=i,this.size=r,this.blob=n?void 0:s,this.is_stream=o,this.mime_type=a,this.alt_text=l,this.b64=u}}class Qh{constructor(e,n){K(this,"type");K(this,"command");K(this,"meta");K(this,"fileData");this.type="command",this.command=e,this.meta=n}}typeof process<"u"&&process.versions&&process.versions.node;function oo(t,e,n){for(;n.length>1;){const r=n.shift();if(typeof r=="string"||typeof r=="number")t=t[r];else throw new Error("Invalid key type")}const i=n.shift();if(typeof i=="string"||typeof i=="number")t[i]=e;else throw new Error("Invalid key type")}async function Zr(t,e=void 0,n=[],i=!1,r=void 0){if(Array.isArray(t)){let s=[];return await Promise.all(t.map(async(o,a)=>{var c;let l=n.slice();l.push(String(a));const u=await Zr(t[a],i?((c=r==null?void 0:r.parameters[a])==null?void 0:c.component)||void 0:e,l,!1,r);s=s.concat(u)})),s}else{if(globalThis.Buffer&&t instanceof globalThis.Buffer||t instanceof Blob)return[{path:n,blob:new Blob([t]),type:e}];if(typeof t=="object"&&t!==null){let s=[];for(const o of Object.keys(t)){const a=[...n,o],l=t[o];s=s.concat(await Zr(l,void 0,a,!1,r))}return s}}return[]}function Kh(t,e){var i,r;let n=(r=(i=e==null?void 0:e.dependencies)==null?void 0:i.find(s=>s.id==t))==null?void 0:r.queue;return n!=null?!n:!e.enable_queue}function $h(t,e){return new Promise((n,i)=>{const r=new MessageChannel;r.port1.onmessage=({data:s})=>{r.port1.close(),n(s)},window.parent.postMessage(t,e,[r.port2])})}function qn(t,e,n,i,r=!1){if(i==="input"&&!r)throw new Error("Invalid code path. Cannot skip state inputs for input.");if(i==="output"&&r)return t;let s=[],o=0;const a=i==="input"?e.inputs:e.outputs;for(let l=0;l<a.length;l++){const u=a[l],c=n.find(h=>h.id===u);if((c==null?void 0:c.type)==="state"){if(r)if(t.length===a.length){const h=t[o];s.push(h),o++}else s.push(null);else{o++;continue}continue}else{const h=t[o];s.push(h),o++}}return s}async function ed(t,e,n){const i=this;await td(i,e);const r=await Zr(e,void 0,[],!0,n);return(await Promise.all(r.map(async({path:o,blob:a,type:l})=>{if(!a)return{path:o,type:l};const u=await i.upload_files(t,[a]),c=u.files&&u.files[0];return{path:o,file_url:c,type:l,name:typeof File<"u"&&a instanceof File?a==null?void 0:a.name:void 0}}))).forEach(({path:o,file_url:a,type:l,name:u})=>{if(l==="Gallery")oo(e,a,o);else if(a){const c=new rr({path:a,orig_name:u});oo(e,c,o)}}),e}async function td(t,e){var i,r;if(!(((i=t.config)==null?void 0:i.root)||((r=t.config)==null?void 0:r.root_url)))throw new Error(Vl);await Xl(t,e)}async function Xl(t,e,n=[]){for(const i in e)e[i]instanceof Qh?await nd(t,e,i):typeof e[i]=="object"&&e[i]!==null&&await Xl(t,e[i],[...n,i])}async function nd(t,e,n){var s,o;let i=e[n];const r=((s=t.config)==null?void 0:s.root)||((o=t.config)==null?void 0:o.root_url);if(!r)throw new Error(Vl);try{let a,l;if(typeof process<"u"&&process.versions&&process.versions.node){const f=await P(()=>import("./__vite-browser-external.D7Ct-6yo.js").then(m=>m._),[],import.meta.url);l=(await P(()=>import("./__vite-browser-external.D7Ct-6yo.js").then(m=>m._),[],import.meta.url)).resolve(process.cwd(),i.meta.path),a=await f.readFile(l)}else throw new Error(Nh);const u=new Blob([a],{type:"application/octet-stream"}),c=await t.upload_files(r,[u]),h=c.files&&c.files[0];if(h){const f=new rr({path:h,orig_name:i.meta.name||""});e[n]=f}}catch(a){console.error(Mh,a)}}async function id(t,e,n){const i={"Content-Type":"application/json"};this.options.hf_token&&(i.Authorization=`Bearer ${this.options.hf_token}`);try{var r=await this.fetch(t,{method:"POST",body:JSON.stringify(e),headers:{...i,...n},credentials:"include"})}catch{return[{error:_n},500]}let s,o;try{s=await r.json(),o=r.status}catch(a){s={error:`Could not parse server response: ${a}`},o=500}return[s,o]}async function rd(t,e={}){let n=!1,i=!1;if(!this.config)throw new Error("Could not resolve app config");if(typeof t=="number")this.config.dependencies.find(r=>r.id==t);else{const r=t.replace(/^\//,"");this.config.dependencies.find(s=>s.id==this.api_map[r])}return new Promise(async(r,s)=>{const o=this.submit(t,e,null,null,!0);let a;for await(const l of o)l.type==="data"&&(i&&r(a),n=!0,a=l),l.type==="status"&&(l.stage==="error"&&s(l),l.stage==="complete"&&(i=!0,n&&r(a)))})}async function ei(t,e,n){let i=e==="subdomain"?`https://huggingface.co/api/spaces/by-subdomain/${t}`:`https://huggingface.co/api/spaces/${t}`,r,s;try{if(r=await fetch(i),s=r.status,s!==200)throw new Error;r=await r.json()}catch{n({status:"error",load_status:"error",message:Oh,detail:"NOT_FOUND"});return}if(!r||s!==200)return;const{runtime:{stage:o},id:a}=r;switch(o){case"STOPPED":case"SLEEPING":n({status:"sleeping",load_status:"pending",message:"Space is asleep. Waking it up...",detail:o}),setTimeout(()=>{ei(t,e,n)},1e3);break;case"PAUSED":n({status:"paused",load_status:"error",message:"This space has been paused by the author. If you would like to try this demo, consider duplicating the space.",detail:o,discussions_enabled:await ao(a)});break;case"RUNNING":case"RUNNING_BUILDING":n({status:"running",load_status:"complete",message:"Space is running.",detail:o});break;case"BUILDING":n({status:"building",load_status:"pending",message:"Space is building...",detail:o}),setTimeout(()=>{ei(t,e,n)},1e3);break;case"APP_STARTING":n({status:"starting",load_status:"pending",message:"Space is starting...",detail:o}),setTimeout(()=>{ei(t,e,n)},1e3);break;default:n({status:"space_error",load_status:"error",message:"This space is experiencing an issue.",detail:o,discussions_enabled:await ao(a)});break}}const Yl=async(t,e)=>{let n=0;const i=12,r=5e3;return new Promise(s=>{ei(t,us.test(t)?"space_name":"subdomain",o=>{e(o),o.status==="running"||o.status==="error"||o.status==="paused"||o.status==="space_error"?s():(o.status==="sleeping"||o.status==="building")&&(n<i?(n++,setTimeout(()=>{Yl(t,e).then(s)},r)):s())})})},sd=/^(?=[^]*\b[dD]iscussions{0,1}\b)(?=[^]*\b[dD]isabled\b)[^]*$/;async function ao(t){try{const e=await fetch(`https://huggingface.co/api/spaces/${t}/discussions`,{method:"HEAD"}),n=e.headers.get("x-error-message");return!(!e.ok||n&&sd.test(n))}catch{return!1}}async function od(t,e){const n={};e&&(n.Authorization=`Bearer ${e}`);try{const i=await fetch(`https://huggingface.co/api/spaces/${t}/${kh}`,{headers:n});if(i.status!==200)throw new Error("Space hardware could not be obtained.");const{hardware:r}=await i.json();return r.current}catch(i){throw new Error(i.message)}}async function ad(t,e,n){const i={};n&&(i.Authorization=`Bearer ${n}`);const r={seconds:e};try{const s=await fetch(`https://huggingface.co/api/spaces/${t}/${Ah}`,{method:"POST",headers:{"Content-Type":"application/json",...i},body:JSON.stringify(r)});if(s.status!==200)throw new Error("Could not set sleep timeout on duplicated Space. Please visit *ADD HF LINK TO SETTINGS* to set a timeout manually to reduce billing charges.");return await s.json()}catch(s){throw new Error(s.message)}}const lo=["cpu-basic","cpu-upgrade","cpu-xl","t4-small","t4-medium","a10g-small","a10g-large","a10g-largex2","a10g-largex4","a100-large","zero-a10g","h100","h100x8"];async function ld(t,e){const{hf_token:n,private:i,hardware:r,timeout:s,auth:o}=e;if(r&&!lo.includes(r))throw new Error(`Invalid hardware type provided. Valid types are: ${lo.map(b=>`"${b}"`).join(",")}.`);const{http_protocol:a,host:l}=await ir(t,n);let u=null;if(o){const b=await ql(a,l,o,fetch);b&&(u=Wl(b))}const c={Authorization:`Bearer ${n}`,"Content-Type":"application/json",...u?{Cookie:u.join("; ")}:{}},h=(await(await fetch("https://huggingface.co/api/whoami-v2",{headers:c})).json()).name,f=t.split("/")[1],d={repository:`${h}/${f}`};i&&(d.private=!0);let m;try{r||(m=await od(t,n))}catch(b){throw Error(ls+b.message)}const p=r||m||"cpu-basic";d.hardware=p;try{const b=await fetch(`https://huggingface.co/api/spaces/${t}/duplicate`,{method:"POST",headers:c,body:JSON.stringify(d)});if(b.status===409)try{return await qi.connect(`${h}/${f}`,e)}catch(y){throw console.error("Failed to connect Client instance:",y),y}else if(b.status!==200)throw new Error(b.statusText);const D=await b.json();return await ad(`${h}/${f}`,s||300,n),await qi.connect(ud(D.url),e)}catch(b){throw new Error(b)}}function ud(t){const e=/https:\/\/huggingface.co\/spaces\/([^/]+\/[^/]+)/,n=t.match(e);if(n)return n[1]}var $t;class cd extends TransformStream{constructor(n={allowCR:!1}){super({transform:(i,r)=>{for(i=Kt(this,$t)+i;;){const s=i.indexOf(`
`),o=n.allowCR?i.indexOf("\r"):-1;if(o!==-1&&o!==i.length-1&&(s===-1||s-1>o)){r.enqueue(i.slice(0,o)),i=i.slice(o+1);continue}if(s===-1)break;const a=i[s-1]==="\r"?s-1:s;r.enqueue(i.slice(0,a)),i=i.slice(s+1)}Di(this,$t,i)},flush:i=>{if(Kt(this,$t)==="")return;const r=n.allowCR&&Kt(this,$t).endsWith("\r")?Kt(this,$t).slice(0,-1):Kt(this,$t);i.enqueue(r)}});Ln(this,$t,"")}}$t=new WeakMap;function fd(t){let e=new TextDecoderStream,n=new cd({allowCR:!0});return t.pipeThrough(e).pipeThrough(n)}function hd(t){let n=/[:]\s*/.exec(t),i=n&&n.index;if(i)return[t.substring(0,i),t.substring(i+n[0].length)]}function uo(t,e,n){t.get(e)||t.set(e,n)}async function*dd(t,e){if(!t.body)return;let n=fd(t.body),i,r=n.getReader(),s;for(;;){if(e&&e.aborted)return r.cancel();if(i=await r.read(),i.done)return;if(!i.value){s&&(yield s),s=void 0;continue}let[o,a]=hd(i.value)||[];o&&(o==="data"?(s||(s={}),s[o]=s[o]?s[o]+`
`+a:a):o==="event"?(s||(s={}),s[o]=a):o==="id"?(s||(s={}),s[o]=+a||a):o==="retry"&&(s||(s={}),s[o]=+a||void 0))}}async function pd(t,e){let n=new Request(t,e);uo(n.headers,"Accept","text/event-stream"),uo(n.headers,"Content-Type","application/json");let i=await fetch(n);if(!i.ok)throw i;return dd(i,n.signal)}async function md(){let{event_callbacks:t,unclosed_events:e,pending_stream_messages:n,stream_status:i,config:r,jwt:s}=this;const o=this;if(!r)throw new Error("Could not resolve app config");i.open=!0;let a=null,l=new URLSearchParams({session_hash:this.session_hash}).toString(),u=new URL(`${r.root}${this.api_prefix}/${zl}?${l}`);if(s&&u.searchParams.set("__sign",s),a=this.stream(u),!a){console.warn("Cannot connect to SSE endpoint: "+u.toString());return}a.onmessage=async function(c){let h=JSON.parse(c.data);if(h.msg==="close_stream"){cs(i,o.abort_controller);return}const f=h.event_id;if(!f)await Promise.all(Object.keys(t).map(d=>t[d](h)));else if(t[f]&&r){h.msg==="process_completed"&&["sse","sse_v1","sse_v2","sse_v2.1","sse_v3"].includes(r.protocol)&&e.delete(f);let d=t[f];typeof window<"u"&&typeof document<"u"?setTimeout(d,0,h):d(h)}else n[f]||(n[f]=[]),n[f].push(h)},a.onerror=async function(c){console.error(c),await Promise.all(Object.keys(t).map(h=>t[h]({msg:"broken_connection",message:_n})))}}function cs(t,e){t&&(t.open=!1,e==null||e.abort())}function _d(t,e,n){!t[e]?(t[e]=[],n.data.forEach((r,s)=>{t[e][s]=r})):n.data.forEach((r,s)=>{let o=gd(t[e][s],r);t[e][s]=o,n.data[s]=o})}function gd(t,e){return e.forEach(([n,i,r])=>{t=bd(t,i,n,r)}),t}function bd(t,e,n,i){if(e.length===0){if(n==="replace")return i;if(n==="append")return t+i;throw new Error(`Unsupported action: ${n}`)}let r=t;for(let o=0;o<e.length-1;o++)r=r[e[o]];const s=e[e.length-1];switch(n){case"replace":r[s]=i;break;case"append":r[s]+=i;break;case"add":Array.isArray(r)?r.splice(Number(s),0,i):r[s]=i;break;case"delete":Array.isArray(r)?r.splice(Number(s),1):delete r[s];break;default:throw new Error(`Unknown action: ${n}`)}return t}function wd(t,e={}){const n={close:()=>{console.warn("Method not implemented.")},onerror:null,onmessage:null,onopen:null,readyState:0,url:t.toString(),withCredentials:!1,CONNECTING:0,OPEN:1,CLOSED:2,addEventListener:()=>{throw new Error("Method not implemented.")},dispatchEvent:()=>{throw new Error("Method not implemented.")},removeEventListener:()=>{throw new Error("Method not implemented.")}};return pd(t,e).then(async i=>{n.readyState=n.OPEN;try{for await(const r of i)n.onmessage&&n.onmessage(r);n.readyState=n.CLOSED}catch(r){n.onerror&&n.onerror(r),n.readyState=n.CLOSED}}).catch(i=>{console.error(i),n.onerror&&n.onerror(i),n.readyState=n.CLOSED}),n}function Ed(t,e={},n,i,r){var s;try{let o=function(ye){(r||Ze[ye.type])&&c(ye)},a=function(){for($e=!0;et.length>0;)et.shift()({value:void 0,done:!0})},l=function(ye){et.length>0?et.shift()(ye):ct.push(ye)},u=function(ye){l(vd(ye)),a()},c=function(ye){l({value:ye,done:!1})},h=function(){return ct.length>0?Promise.resolve(ct.shift()):new Promise(ye=>et.push(ye))};const{hf_token:f}=this.options,{fetch:d,app_reference:m,config:p,session_hash:b,api_info:D,api_map:y,stream_status:g,pending_stream_messages:A,pending_diff_streams:w,event_callbacks:E,unclosed_events:S,post_data:z,options:C,api_prefix:N}=this,re=this;if(!D)throw new Error("No API found");if(!p)throw new Error("Could not resolve app config");let{fn_index:x,endpoint_info:le,dependency:me}=yd(D,t,y,p),ke=Zh(e,le),ue,Q,O=p.protocol??"ws",I="",L=()=>I;const k=typeof t=="number"?"/predict":t;let J,V=null,$=!1,q={},Ce=typeof window<"u"&&typeof document<"u"?new URLSearchParams(window.location.search).toString():"";const Ze=((s=C==null?void 0:C.events)==null?void 0:s.reduce((ye,lt)=>(ye[lt]=!0,ye),{}))||{};async function he(){let ye={},lt={};O==="ws"?(ue&&ue.readyState===0?ue.addEventListener("open",()=>{ue.close()}):ue.close(),ye={fn_index:x,session_hash:b}):(ye={event_id:V},lt={event_id:V,session_hash:b,fn_index:x});try{if(!p)throw new Error("Could not resolve app config");"event_id"in lt&&await d(`${p.root}${N}/${Sh}`,{headers:{"Content-Type":"application/json"},method:"POST",body:JSON.stringify(lt)}),await d(`${p.root}${N}/${Ch}`,{headers:{"Content-Type":"application/json"},method:"POST",body:JSON.stringify(ye)})}catch{console.warn("The `/reset` endpoint could not be called. Subsequent endpoint results may be unreliable.")}}const Me=async ye=>{await this._resolve_heartbeat(ye)};async function it(ye){if(!p)return;let lt=ye.render_id;p.components=[...p.components.filter(xe=>xe.props.rendered_in!==lt),...ye.components],p.dependencies=[...p.dependencies.filter(xe=>xe.rendered_in!==lt),...ye.dependencies];const yt=p.components.some(xe=>xe.type==="state"),gt=p.dependencies.some(xe=>xe.targets.some(dt=>dt[1]==="unload"));p.connect_heartbeat=yt||gt,await Me(p),o({type:"render",data:ye,endpoint:k,fn_index:x})}this.handle_blob(p.root,ke,le).then(async ye=>{var gt;if(J={data:qn(ye,me,p.components,"input",!0)||[],event_data:n,fn_index:x,trigger_id:i},Kh(x,p))o({type:"status",endpoint:k,stage:"pending",queue:!1,fn_index:x,time:new Date}),z(`${p.root}${N}/run${k.startsWith("/")?k:`/${k}`}${Ce?"?"+Ce:""}`,{...J,session_hash:b}).then(([xe,dt])=>{const ht=xe.data;dt==200?(o({type:"data",endpoint:k,fn_index:x,data:qn(ht,me,p.components,"output",C.with_null_state),time:new Date,event_data:n,trigger_id:i}),xe.render_config&&it(xe.render_config),o({type:"status",endpoint:k,fn_index:x,stage:"complete",eta:xe.average_duration,queue:!1,time:new Date})):o({type:"status",stage:"error",endpoint:k,fn_index:x,message:xe.error,queue:!1,time:new Date})}).catch(xe=>{o({type:"status",stage:"error",message:xe.message,endpoint:k,fn_index:x,queue:!1,time:new Date})});else if(O=="ws"){const{ws_protocol:xe,host:dt}=await ir(m,f);o({type:"status",stage:"pending",queue:!0,endpoint:k,fn_index:x,time:new Date});let ht=new URL(`${xe}://${xh(dt,p.root,!0)}/queue/join${Ce?"?"+Ce:""}`);this.jwt&&ht.searchParams.set("__sign",this.jwt),ue=new WebSocket(ht),ue.onclose=tt=>{tt.wasClean||o({type:"status",stage:"error",broken:!0,message:_n,queue:!0,endpoint:k,fn_index:x,time:new Date})},ue.onmessage=function(tt){const H=JSON.parse(tt.data),{type:De,status:Ie,data:Ye}=Ar(H,q[x]);if(De==="update"&&Ie&&!$)o({type:"status",endpoint:k,fn_index:x,time:new Date,...Ie}),Ie.stage==="error"&&ue.close();else if(De==="hash"){ue.send(JSON.stringify({fn_index:x,session_hash:b}));return}else De==="data"?ue.send(JSON.stringify({...J,session_hash:b})):De==="complete"?$=Ie:De==="log"?o({type:"log",title:Ye.title,log:Ye.log,level:Ye.level,endpoint:k,duration:Ye.duration,visible:Ye.visible,fn_index:x}):De==="generating"&&o({type:"status",time:new Date,...Ie,stage:Ie==null?void 0:Ie.stage,queue:!0,endpoint:k,fn_index:x});Ye&&(o({type:"data",time:new Date,data:qn(Ye.data,me,p.components,"output",C.with_null_state),endpoint:k,fn_index:x,event_data:n,trigger_id:i}),$&&(o({type:"status",time:new Date,...$,stage:Ie==null?void 0:Ie.stage,queue:!0,endpoint:k,fn_index:x}),ue.close()))},Hl(p.version||"2.0.0","3.6")<0&&addEventListener("open",()=>ue.send(JSON.stringify({hash:b})))}else if(O=="sse"){o({type:"status",stage:"pending",queue:!0,endpoint:k,fn_index:x,time:new Date});var yt=new URLSearchParams({fn_index:x.toString(),session_hash:b}).toString();let xe=new URL(`${p.root}${N}/${zl}?${Ce?Ce+"&":""}${yt}`);if(this.jwt&&xe.searchParams.set("__sign",this.jwt),Q=this.stream(xe),!Q)return Promise.reject(new Error("Cannot connect to SSE endpoint: "+xe.toString()));Q.onmessage=async function(dt){const ht=JSON.parse(dt.data),{type:tt,status:H,data:De}=Ar(ht,q[x]);if(tt==="update"&&H&&!$)o({type:"status",endpoint:k,fn_index:x,time:new Date,...H}),H.stage==="error"&&(Q==null||Q.close(),a());else if(tt==="data"){let[Ie,Ye]=await z(`${p.root}${N}/queue/data`,{...J,session_hash:b,event_id:V});Ye!==200&&(o({type:"status",stage:"error",message:_n,queue:!0,endpoint:k,fn_index:x,time:new Date}),Q==null||Q.close(),a())}else tt==="complete"?$=H:tt==="log"?o({type:"log",title:De.title,log:De.log,level:De.level,endpoint:k,duration:De.duration,visible:De.visible,fn_index:x}):(tt==="generating"||tt==="streaming")&&o({type:"status",time:new Date,...H,stage:H==null?void 0:H.stage,queue:!0,endpoint:k,fn_index:x});De&&(o({type:"data",time:new Date,data:qn(De.data,me,p.components,"output",C.with_null_state),endpoint:k,fn_index:x,event_data:n,trigger_id:i}),$&&(o({type:"status",time:new Date,...$,stage:H==null?void 0:H.stage,queue:!0,endpoint:k,fn_index:x}),Q==null||Q.close(),a()))}}else if(O=="sse_v1"||O=="sse_v2"||O=="sse_v2.1"||O=="sse_v3"){o({type:"status",stage:"pending",queue:!0,endpoint:k,fn_index:x,time:new Date});let xe="";typeof window<"u"&&typeof document<"u"&&(xe=(gt=window==null?void 0:window.location)==null?void 0:gt.hostname);const ht=xe.includes(".dev.")?`https://moon-${xe.split(".")[1]}.dev.spaces.huggingface.tech`:"https://huggingface.co";(typeof window<"u"&&typeof document<"u"&&window.parent!=window&&window.supports_zerogpu_headers?$h("zerogpu-headers",ht):Promise.resolve(null)).then(Ie=>z(`${p.root}${N}/${vh}?${Ce}`,{...J,session_hash:b},Ie)).then(async([Ie,Ye])=>{if(Ye===503)o({type:"status",stage:"error",message:Ul,queue:!0,endpoint:k,fn_index:x,time:new Date});else if(Ye!==200)o({type:"status",stage:"error",broken:!0,message:_n,queue:!0,endpoint:k,fn_index:x,time:new Date});else{V=Ie.event_id,I=V;let bt=async function(kt){try{const{type:Ge,status:se,data:Ae,original_msg:Tt}=Ar(kt,q[x]);if(Ge=="heartbeat")return;if(Ge==="update"&&se&&!$)o({type:"status",endpoint:k,fn_index:x,time:new Date,original_msg:Tt,...se});else if(Ge==="complete")$=se;else if(Ge=="unexpected_error"||Ge=="broken_connection"){console.error("Unexpected error",se==null?void 0:se.message);const hn=Ge==="broken_connection";o({type:"status",stage:"error",message:(se==null?void 0:se.message)||"An Unexpected Error Occurred!",queue:!0,endpoint:k,broken:hn,session_not_found:se==null?void 0:se.session_not_found,fn_index:x,time:new Date})}else if(Ge==="log"){o({type:"log",title:Ae.title,log:Ae.log,level:Ae.level,endpoint:k,duration:Ae.duration,visible:Ae.visible,fn_index:x});return}else(Ge==="generating"||Ge==="streaming")&&(o({type:"status",time:new Date,...se,stage:se==null?void 0:se.stage,queue:!0,endpoint:k,fn_index:x}),Ae&&me.connection!=="stream"&&["sse_v2","sse_v2.1","sse_v3"].includes(O)&&_d(w,V,Ae));Ae&&(o({type:"data",time:new Date,data:qn(Ae.data,me,p.components,"output",C.with_null_state),endpoint:k,fn_index:x}),Ae.render_config&&await it(Ae.render_config),$&&(o({type:"status",time:new Date,...$,stage:se==null?void 0:se.stage,queue:!0,endpoint:k,fn_index:x}),a())),((se==null?void 0:se.stage)==="complete"||(se==null?void 0:se.stage)==="error")&&(E[V]&&delete E[V],V in w&&delete w[V])}catch(Ge){console.error("Unexpected client exception",Ge),o({type:"status",stage:"error",message:"An Unexpected Error Occurred!",queue:!0,endpoint:k,fn_index:x,time:new Date}),["sse_v2","sse_v2.1","sse_v3"].includes(O)&&(cs(g,re.abort_controller),g.open=!1,a())}};V in A&&(A[V].forEach(kt=>bt(kt)),delete A[V]),E[V]=bt,S.add(V),g.open||await this.open_stream()}})}});let $e=!1;const ct=[],et=[],st={[Symbol.asyncIterator]:()=>st,next:h,throw:async ye=>(u(ye),h()),return:async()=>(a(),h()),cancel:he,event_id:L};return st}catch(o){throw console.error("Submit function encountered an error:",o),o}}function vd(t){return{then:(e,n)=>n(t)}}function yd(t,e,n,i){let r,s,o;if(typeof e=="number")r=e,s=t.unnamed_endpoints[r],o=i.dependencies.find(a=>a.id==e);else{const a=e.replace(/^\//,"");r=n[a],s=t.named_endpoints[e.trim()],o=i.dependencies.find(l=>l.id==n[a])}if(typeof r!="number")throw new Error("There is no endpoint matching that name of fn_index matching that number.");return{fn_index:r,endpoint_info:s,dependency:o}}class qi{constructor(e,n={events:["data"]}){K(this,"app_reference");K(this,"options");K(this,"deep_link",null);K(this,"config");K(this,"api_prefix","");K(this,"api_info");K(this,"api_map",{});K(this,"session_hash",Math.random().toString(36).substring(2));K(this,"jwt",!1);K(this,"last_status",{});K(this,"cookies",null);K(this,"stream_status",{open:!1});K(this,"closed",!1);K(this,"pending_stream_messages",{});K(this,"pending_diff_streams",{});K(this,"event_callbacks",{});K(this,"unclosed_events",new Set);K(this,"heartbeat_event",null);K(this,"abort_controller",null);K(this,"stream_instance",null);K(this,"current_payload");K(this,"ws_map",{});K(this,"view_api");K(this,"upload_files");K(this,"upload");K(this,"handle_blob");K(this,"post_data");K(this,"submit");K(this,"predict");K(this,"open_stream");K(this,"resolve_config");K(this,"resolve_cookies");var i;this.app_reference=e,this.deep_link=((i=n.query_params)==null?void 0:i.deep_link)||null,n.events||(n.events=["data"]),this.options=n,this.current_payload={},this.view_api=Xh.bind(this),this.upload_files=Yh.bind(this),this.handle_blob=ed.bind(this),this.post_data=id.bind(this),this.submit=Ed.bind(this),this.predict=rd.bind(this),this.open_stream=md.bind(this),this.resolve_config=zh.bind(this),this.resolve_cookies=jh.bind(this),this.upload=Jh.bind(this),this.fetch=this.fetch.bind(this),this.handle_space_success=this.handle_space_success.bind(this),this.stream=this.stream.bind(this)}get_url_config(e=null){if(!this.config)throw new Error(sn);e===null&&(e=window.location.href);const n=o=>o.replace(/^\/+|\/+$/g,"");let i=n(new URL(this.config.root).pathname),r=n(new URL(e).pathname),s;return r.startsWith(i)?s=n(r.substring(i.length)):s="",this.get_page_config(s)}get_page_config(e){if(!this.config)throw new Error(sn);let n=this.config;return e in n.page||(e=""),{...n,current_page:e,layout:n.page[e].layout,components:n.components.filter(i=>n.page[e].components.includes(i.id)),dependencies:this.config.dependencies.filter(i=>n.page[e].dependencies.includes(i.id))}}fetch(e,n){const i=new Headers((n==null?void 0:n.headers)||{});if(this&&this.cookies&&i.append("Cookie",this.cookies),this&&this.options.headers)for(const r in this.options.headers)i.append(r,this.options.headers[r]);return fetch(e,{...n,headers:i})}stream(e){const n=new Headers;if(this&&this.cookies&&n.append("Cookie",this.cookies),this&&this.options.headers)for(const i in this.options.headers)n.append(i,this.options.headers[i]);return this&&this.options.hf_token&&n.append("Authorization",`Bearer ${this.options.hf_token}`),this.abort_controller=new AbortController,this.stream_instance=wd(e.toString(),{credentials:"include",headers:n,signal:this.abort_controller.signal}),this.stream_instance}async init(){var e;if((typeof window>"u"||!("WebSocket"in window))&&!global.WebSocket){const n=await P(()=>import("./browser.D44bRDhO.js").then(i=>i.b),[],import.meta.url);global.WebSocket=n.WebSocket}this.options.auth&&await this.resolve_cookies(),await this._resolve_config().then(({config:n})=>this._resolve_heartbeat(n)),this.api_info=await this.view_api(),this.api_map=Hh(((e=this.config)==null?void 0:e.dependencies)||[])}async _resolve_heartbeat(e){if(e&&(this.config=e,this.api_prefix=e.api_prefix||"",this.config&&this.config.connect_heartbeat&&this.config.space_id&&this.options.hf_token&&(this.jwt=await so(this.config.space_id,this.options.hf_token,this.cookies))),e.space_id&&this.options.hf_token&&(this.jwt=await so(e.space_id,this.options.hf_token)),this.config&&this.config.connect_heartbeat){const n=new URL(`${this.config.root}${this.api_prefix}/${Fh}/${this.session_hash}`);this.jwt&&n.searchParams.set("__sign",this.jwt),this.heartbeat_event||(this.heartbeat_event=this.stream(n))}}static async connect(e,n={events:["data"]}){const i=new this(e,n);return n.session_hash&&(i.session_hash=n.session_hash),await i.init(),i}async reconnect(){const e=new URL(`${this.config.root}${this.api_prefix}/${Lh}`);let n;try{const i=await this.fetch(e);if(!i.ok)throw new Error;n=(await i.json()).app_id}catch{return"broken"}return n!==this.config.app_id?"changed":"connected"}close(){this.closed=!0,cs(this.stream_status,this.abort_controller)}set_current_payload(e){this.current_payload=e}static async duplicate(e,n={events:["data"]}){return ld(e,n)}async _resolve_config(){const{http_protocol:e,host:n,space_id:i}=await ir(this.app_reference,this.options.hf_token),{status_callback:r}=this.options;i&&r&&await Yl(i,r);let s;try{let o=`${e}//${n}`;if(s=await this.resolve_config(o),!s)throw new Error(sn);return this.config_success(s)}catch(o){if(i&&r)ei(i,us.test(i)?"space_name":"subdomain",this.handle_space_success);else throw r&&r({status:"error",message:"Could not load this space.",load_status:"error",detail:"NOT_FOUND"}),Error(o)}}async config_success(e){if(this.config=e,this.api_prefix=e.api_prefix||"",this.config.auth_required)return this.prepare_return_obj();try{this.api_info=await this.view_api()}catch(n){console.error(Ih+n.message)}return this.prepare_return_obj()}async handle_space_success(e){var i;if(!this)throw new Error(sn);const{status_callback:n}=this.options;if(n&&n(e),e.status==="running")try{if(this.config=await this._resolve_config(),this.api_prefix=((i=this==null?void 0:this.config)==null?void 0:i.api_prefix)||"",!this.config)throw new Error(sn);return await this.config_success(this.config)}catch(r){throw n&&n({status:"error",message:"Could not load this space.",load_status:"error",detail:"NOT_FOUND"}),r}}async component_server(e,n,i){var c;if(!this.config)throw new Error(sn);const r={},{hf_token:s}=this.options,{session_hash:o}=this;s&&(r.Authorization=`Bearer ${this.options.hf_token}`);let a,l=this.config.components.find(h=>h.id===e);(c=l==null?void 0:l.props)!=null&&c.root_url?a=l.props.root_url:a=this.config.root;let u;if("binary"in i){u=new FormData;for(const h in i.data)h!=="binary"&&u.append(h,i.data[h]);u.set("component_id",e.toString()),u.set("fn_name",n),u.set("session_hash",o)}else u=JSON.stringify({data:i,component_id:e,fn_name:n,session_hash:o}),r["Content-Type"]="application/json";s&&(r.Authorization=`Bearer ${s}`);try{const h=await this.fetch(`${a}${this.api_prefix}/${Th}/`,{method:"POST",body:u,headers:r,credentials:"include"});if(!h.ok)throw new Error("Could not connect to component server: "+h.statusText);return await h.json()}catch(h){console.warn(h)}}set_cookies(e){this.cookies=Wl(e).join("; ")}prepare_return_obj(){return{config:this.config,predict:this.predict,submit:this.submit,view_api:this.view_api,component_server:this.component_server}}async connect_ws(e){return new Promise((n,i)=>{let r;try{r=new WebSocket(e)}catch{this.ws_map[e]="failed";return}this.ws_map[e]="pending",r.onopen=()=>{this.ws_map[e]=r,n()},r.onerror=s=>{console.error("WebSocket error:",s),this.close_ws(e),this.ws_map[e]="failed",n()},r.onclose=()=>{this.ws_map[e]="closed"},r.onmessage=s=>{}})}async send_ws_message(e,n){if(!(e in this.ws_map))await this.connect_ws(e);else if(this.ws_map[e]==="pending"||this.ws_map[e]==="closed"||this.ws_map[e]==="failed")return;const i=this.ws_map[e];i instanceof WebSocket?i.send(JSON.stringify(n)):this.post_data(e,n)}async close_ws(e){if(e in this.ws_map){const n=this.ws_map[e];n instanceof WebSocket&&(n.close(),delete this.ws_map[e])}}}function On(t){let e=["","k","M","G","T","P","E","Z"],n=0;for(;t>1e3&&n<e.length-1;)t/=1e3,n++;let i=e[n];return(Number.isInteger(t)?t:t.toFixed(1))+i}function Dd(t){let e,n,i,r,s,o,a,l,u,c,h,f;return{c(){e=Z("div"),n=Se("svg"),i=Se("g"),r=Se("path"),s=Se("path"),o=Se("path"),a=Se("path"),l=Se("g"),u=Se("path"),c=Se("path"),h=Se("path"),f=Se("path"),this.h()},l(d){e=X(d,"DIV",{class:!0});var m=U(e);n=Le(m,"svg",{viewBox:!0,fill:!0,xmlns:!0,class:!0});var p=U(n);i=Le(p,"g",{style:!0});var b=U(i);r=Le(b,"path",{d:!0,fill:!0,"fill-opacity":!0,class:!0}),U(r).forEach(v),s=Le(b,"path",{d:!0,fill:!0,class:!0}),U(s).forEach(v),o=Le(b,"path",{d:!0,fill:!0,"fill-opacity":!0,class:!0}),U(o).forEach(v),a=Le(b,"path",{d:!0,fill:!0,class:!0}),U(a).forEach(v),b.forEach(v),l=Le(p,"g",{style:!0});var D=U(l);u=Le(D,"path",{d:!0,fill:!0,"fill-opacity":!0,class:!0}),U(u).forEach(v),c=Le(D,"path",{d:!0,fill:!0,class:!0}),U(c).forEach(v),h=Le(D,"path",{d:!0,fill:!0,"fill-opacity":!0,class:!0}),U(h).forEach(v),f=Le(D,"path",{d:!0,fill:!0,class:!0}),U(f).forEach(v),D.forEach(v),p.forEach(v),m.forEach(v),this.h()},h(){_(r,"d","M255.926 0.754768L509.702 139.936V221.027L255.926 81.8465V0.754768Z"),_(r,"fill","#FF7C00"),_(r,"fill-opacity","0.4"),_(r,"class","svelte-zyxd38"),_(s,"d","M509.69 139.936L254.981 279.641V361.255L509.69 221.55V139.936Z"),_(s,"fill","#FF7C00"),_(s,"class","svelte-zyxd38"),_(o,"d","M0.250138 139.937L254.981 279.641V361.255L0.250138 221.55V139.937Z"),_(o,"fill","#FF7C00"),_(o,"fill-opacity","0.4"),_(o,"class","svelte-zyxd38"),_(a,"d","M255.923 0.232622L0.236328 139.936V221.55L255.923 81.8469V0.232622Z"),_(a,"fill","#FF7C00"),_(a,"class","svelte-zyxd38"),Y(i,"transform","translate("+t[1][0]+"px, "+t[1][1]+"px)"),_(u,"d","M255.926 141.5L509.702 280.681V361.773L255.926 222.592V141.5Z"),_(u,"fill","#FF7C00"),_(u,"fill-opacity","0.4"),_(u,"class","svelte-zyxd38"),_(c,"d","M509.69 280.679L254.981 420.384V501.998L509.69 362.293V280.679Z"),_(c,"fill","#FF7C00"),_(c,"class","svelte-zyxd38"),_(h,"d","M0.250138 280.681L254.981 420.386V502L0.250138 362.295V280.681Z"),_(h,"fill","#FF7C00"),_(h,"fill-opacity","0.4"),_(h,"class","svelte-zyxd38"),_(f,"d","M255.923 140.977L0.236328 280.68V362.294L255.923 222.591V140.977Z"),_(f,"fill","#FF7C00"),_(f,"class","svelte-zyxd38"),Y(l,"transform","translate("+t[2][0]+"px, "+t[2][1]+"px)"),_(n,"viewBox","-1200 -1200 3000 3000"),_(n,"fill","none"),_(n,"xmlns","http://www.w3.org/2000/svg"),_(n,"class","svelte-zyxd38"),_(e,"class","svelte-zyxd38"),G(e,"margin",t[0])},m(d,m){R(d,e,m),M(e,n),M(n,i),M(i,r),M(i,s),M(i,o),M(i,a),M(n,l),M(l,u),M(l,c),M(l,h),M(l,f)},p(d,[m]){m&2&&Y(i,"transform","translate("+d[1][0]+"px, "+d[1][1]+"px)"),m&4&&Y(l,"transform","translate("+d[2][0]+"px, "+d[2][1]+"px)"),m&1&&G(e,"margin",d[0])},i:pe,o:pe,d(d){d&&v(e)}}}function kd(t,e,n){let i,r,{margin:s=!0}=e;const o=Ds([0,0]);Rt(t,o,f=>n(1,i=f));const a=Ds([0,0]);Rt(t,a,f=>n(2,r=f));let l;async function u(){await Promise.all([o.set([125,140]),a.set([-125,-140])]),await Promise.all([o.set([-125,140]),a.set([125,-140])]),await Promise.all([o.set([-125,0]),a.set([125,-0])]),await Promise.all([o.set([125,0]),a.set([-125,0])])}async function c(){await u(),l||c()}async function h(){await Promise.all([o.set([125,0]),a.set([-125,0])]),c()}return gn(()=>(h(),()=>l=!0)),t.$$set=f=>{"margin"in f&&n(0,s=f.margin)},[s,i,r,o,a]}class Ad extends qe{constructor(e){super(),We(this,e,kd,Dd,Xe,{margin:0})}}function co(t){let e,n,i,r,s;return{c(){e=Se("svg"),n=Se("line"),i=Se("line"),this.h()},l(o){e=Le(o,"svg",{class:!0,xmlns:!0,viewBox:!0});var a=U(e);n=Le(a,"line",{x1:!0,y1:!0,x2:!0,y2:!0,stroke:!0,"stroke-width":!0}),U(n).forEach(v),i=Le(a,"line",{x1:!0,y1:!0,x2:!0,y2:!0,stroke:!0,"stroke-width":!0}),U(i).forEach(v),a.forEach(v),this.h()},h(){_(n,"x1","1"),_(n,"y1","9"),_(n,"x2","9"),_(n,"y2","1"),_(n,"stroke","gray"),_(n,"stroke-width","0.5"),_(i,"x1","5"),_(i,"y1","9"),_(i,"x2","9"),_(i,"y2","5"),_(i,"stroke","gray"),_(i,"stroke-width","0.5"),_(e,"class","resize-handle svelte-1svsvh2"),_(e,"xmlns","http://www.w3.org/2000/svg"),_(e,"viewBox","0 0 10 10")},m(o,a){R(o,e,a),M(e,n),M(e,i),r||(s=Te(e,"mousedown",t[27]),r=!0)},p:pe,d(o){o&&v(e),r=!1,s()}}}function Fd(t){var h;let e,n,i,r,s;const o=t[31].default,a=Ut(o,t,t[30],null);let l=t[19]&&co(t),u=[{"data-testid":t[11]},{id:t[6]},{class:i="block "+(((h=t[7])==null?void 0:h.join(" "))||"")+" svelte-1svsvh2"},{dir:r=t[20]?"rtl":"ltr"}],c={};for(let f=0;f<u.length;f+=1)c=It(c,u[f]);return{c(){e=Z(t[25]),a&&a.c(),n=oe(),l&&l.c(),this.h()},l(f){e=X(f,(t[25]||"null").toUpperCase(),{"data-testid":!0,id:!0,class:!0,dir:!0});var d=U(e);a&&a.l(d),n=ae(d),l&&l.l(d),d.forEach(v),this.h()},h(){Es(t[25])(e,c),G(e,"hidden",t[14]===!1),G(e,"padded",t[10]),G(e,"flex",t[1]),G(e,"border_focus",t[9]==="focus"),G(e,"border_contrast",t[9]==="contrast"),G(e,"hide-container",!t[12]&&!t[13]),G(e,"fullscreen",t[0]),G(e,"animating",t[0]&&t[24]!==null),G(e,"auto-margin",t[17]===null),Y(e,"height",t[0]?void 0:t[26](t[2])),Y(e,"min-height",t[0]?void 0:t[26](t[3])),Y(e,"max-height",t[0]?void 0:t[26](t[4])),Y(e,"--start-top",t[24]?`${t[24].top}px`:"0px"),Y(e,"--start-left",t[24]?`${t[24].left}px`:"0px"),Y(e,"--start-width",t[24]?`${t[24].width}px`:"0px"),Y(e,"--start-height",t[24]?`${t[24].height}px`:"0px"),Y(e,"width",t[0]?void 0:typeof t[5]=="number"?`calc(min(${t[5]}px, 100%))`:t[26](t[5])),Y(e,"border-style",t[8]),Y(e,"overflow",t[15]?t[16]:"hidden"),Y(e,"flex-grow",t[17]),Y(e,"min-width",`calc(min(${t[18]}px, 100%))`),Y(e,"border-width","var(--block-border-width)")},m(f,d){R(f,e,d),a&&a.m(e,null),M(e,n),l&&l.m(e,null),t[32](e),s=!0},p(f,d){var m;a&&a.p&&(!s||d[0]&1073741824)&&jt(a,o,f,f[30],s?Vt(o,f[30],d,null):Gt(f[30]),null),f[19]?l?l.p(f,d):(l=co(f),l.c(),l.m(e,null)):l&&(l.d(1),l=null),Es(f[25])(e,c=un(u,[(!s||d[0]&2048)&&{"data-testid":f[11]},(!s||d[0]&64)&&{id:f[6]},(!s||d[0]&128&&i!==(i="block "+(((m=f[7])==null?void 0:m.join(" "))||"")+" svelte-1svsvh2"))&&{class:i},(!s||d[0]&1048576&&r!==(r=f[20]?"rtl":"ltr"))&&{dir:r}])),G(e,"hidden",f[14]===!1),G(e,"padded",f[10]),G(e,"flex",f[1]),G(e,"border_focus",f[9]==="focus"),G(e,"border_contrast",f[9]==="contrast"),G(e,"hide-container",!f[12]&&!f[13]),G(e,"fullscreen",f[0]),G(e,"animating",f[0]&&f[24]!==null),G(e,"auto-margin",f[17]===null),d[0]&5&&Y(e,"height",f[0]?void 0:f[26](f[2])),d[0]&9&&Y(e,"min-height",f[0]?void 0:f[26](f[3])),d[0]&17&&Y(e,"max-height",f[0]?void 0:f[26](f[4])),d[0]&16777216&&Y(e,"--start-top",f[24]?`${f[24].top}px`:"0px"),d[0]&16777216&&Y(e,"--start-left",f[24]?`${f[24].left}px`:"0px"),d[0]&16777216&&Y(e,"--start-width",f[24]?`${f[24].width}px`:"0px"),d[0]&16777216&&Y(e,"--start-height",f[24]?`${f[24].height}px`:"0px"),d[0]&33&&Y(e,"width",f[0]?void 0:typeof f[5]=="number"?`calc(min(${f[5]}px, 100%))`:f[26](f[5])),d[0]&256&&Y(e,"border-style",f[8]),d[0]&98304&&Y(e,"overflow",f[15]?f[16]:"hidden"),d[0]&131072&&Y(e,"flex-grow",f[17]),d[0]&262144&&Y(e,"min-width",`calc(min(${f[18]}px, 100%))`)},i(f){s||(B(a,f),s=!0)},o(f){j(a,f),s=!1},d(f){f&&v(e),a&&a.d(f),l&&l.d(),t[32](null)}}}function fo(t){let e;return{c(){e=Z("div"),this.h()},l(n){e=X(n,"DIV",{class:!0}),U(e).forEach(v),this.h()},h(){_(e,"class","placeholder svelte-1svsvh2"),Y(e,"height",t[22]+"px"),Y(e,"width",t[23]+"px")},m(n,i){R(n,e,i)},p(n,i){i[0]&4194304&&Y(e,"height",n[22]+"px"),i[0]&8388608&&Y(e,"width",n[23]+"px")},d(n){n&&v(e)}}}function Td(t){let e,n,i,r=t[25]&&Fd(t),s=t[0]&&fo(t);return{c(){r&&r.c(),e=oe(),s&&s.c(),n=fe()},l(o){r&&r.l(o),e=ae(o),s&&s.l(o),n=fe()},m(o,a){r&&r.m(o,a),R(o,e,a),s&&s.m(o,a),R(o,n,a),i=!0},p(o,a){o[25]&&r.p(o,a),o[0]?s?s.p(o,a):(s=fo(o),s.c(),s.m(n.parentNode,n)):s&&(s.d(1),s=null)},i(o){i||(B(r,o),i=!0)},o(o){j(r,o),i=!1},d(o){o&&(v(e),v(n)),r&&r.d(o),s&&s.d(o)}}}function Cd(t,e,n){let{$$slots:i={},$$scope:r}=e,{height:s=void 0}=e,{min_height:o=void 0}=e,{max_height:a=void 0}=e,{width:l=void 0}=e,{elem_id:u=""}=e,{elem_classes:c=[]}=e,{variant:h="solid"}=e,{border_mode:f="base"}=e,{padding:d=!0}=e,{type:m="normal"}=e,{test_id:p=void 0}=e,{explicit_call:b=!1}=e,{container:D=!0}=e,{visible:y=!0}=e,{allow_overflow:g=!0}=e,{overflow_behavior:A="auto"}=e,{scale:w=null}=e,{min_width:E=0}=e,{flex:S=!1}=e,{resizable:z=!1}=e,{rtl:C=!1}=e,{fullscreen:N=!1}=e,re=N,x,le=m==="fieldset"?"fieldset":"div",me=0,ke=0,ue=null;function Q(k){N&&k.key==="Escape"&&n(0,N=!1)}const O=k=>{if(k!==void 0){if(typeof k=="number")return k+"px";if(typeof k=="string")return k}},I=k=>{let J=k.clientY;const V=q=>{const Ce=q.clientY-J;J=q.clientY,n(21,x.style.height=`${x.offsetHeight+Ce}px`,x)},$=()=>{window.removeEventListener("mousemove",V),window.removeEventListener("mouseup",$)};window.addEventListener("mousemove",V),window.addEventListener("mouseup",$)};function L(k){Je[k?"unshift":"push"](()=>{x=k,n(21,x)})}return t.$$set=k=>{"height"in k&&n(2,s=k.height),"min_height"in k&&n(3,o=k.min_height),"max_height"in k&&n(4,a=k.max_height),"width"in k&&n(5,l=k.width),"elem_id"in k&&n(6,u=k.elem_id),"elem_classes"in k&&n(7,c=k.elem_classes),"variant"in k&&n(8,h=k.variant),"border_mode"in k&&n(9,f=k.border_mode),"padding"in k&&n(10,d=k.padding),"type"in k&&n(28,m=k.type),"test_id"in k&&n(11,p=k.test_id),"explicit_call"in k&&n(12,b=k.explicit_call),"container"in k&&n(13,D=k.container),"visible"in k&&n(14,y=k.visible),"allow_overflow"in k&&n(15,g=k.allow_overflow),"overflow_behavior"in k&&n(16,A=k.overflow_behavior),"scale"in k&&n(17,w=k.scale),"min_width"in k&&n(18,E=k.min_width),"flex"in k&&n(1,S=k.flex),"resizable"in k&&n(19,z=k.resizable),"rtl"in k&&n(20,C=k.rtl),"fullscreen"in k&&n(0,N=k.fullscreen),"$$scope"in k&&n(30,r=k.$$scope)},t.$$.update=()=>{t.$$.dirty[0]&538968065&&N!==re&&(n(29,re=N),N?(n(24,ue=x.getBoundingClientRect()),n(22,me=x.offsetHeight),n(23,ke=x.offsetWidth),window.addEventListener("keydown",Q)):(n(24,ue=null),window.removeEventListener("keydown",Q))),t.$$.dirty[0]&16384&&(y||n(1,S=!1))},[N,S,s,o,a,l,u,c,h,f,d,p,b,D,y,g,A,w,E,z,C,x,me,ke,ue,le,O,I,m,re,r,i,L]}class ho extends qe{constructor(e){super(),We(this,e,Cd,Td,Xe,{height:2,min_height:3,max_height:4,width:5,elem_id:6,elem_classes:7,variant:8,border_mode:9,padding:10,type:28,test_id:11,explicit_call:12,container:13,visible:14,allow_overflow:15,overflow_behavior:16,scale:17,min_width:18,flex:1,resizable:19,rtl:20,fullscreen:0},null,[-1,-1])}}function fs(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let Dn=fs();function Jl(t){Dn=t}const Ql=/[&<>"']/,Sd=new RegExp(Ql.source,"g"),Kl=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,Ld=new RegExp(Kl.source,"g"),Rd={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},po=t=>Rd[t];function Ft(t,e){if(e){if(Ql.test(t))return t.replace(Sd,po)}else if(Kl.test(t))return t.replace(Ld,po);return t}const Od=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig;function Id(t){return t.replace(Od,(e,n)=>(n=n.toLowerCase(),n==="colon"?":":n.charAt(0)==="#"?n.charAt(1)==="x"?String.fromCharCode(parseInt(n.substring(2),16)):String.fromCharCode(+n.substring(1)):""))}const Pd=/(^|[^\[])\^/g;function Ke(t,e){let n=typeof t=="string"?t:t.source;e=e||"";const i={replace:(r,s)=>{let o=typeof s=="string"?s:s.source;return o=o.replace(Pd,"$1"),n=n.replace(r,o),i},getRegex:()=>new RegExp(n,e)};return i}function mo(t){try{t=encodeURI(t).replace(/%25/g,"%")}catch{return null}return t}const ti={exec:()=>null};function _o(t,e){const n=t.replace(/\|/g,(s,o,a)=>{let l=!1,u=o;for(;--u>=0&&a[u]==="\\";)l=!l;return l?"|":" |"}),i=n.split(/ \|/);let r=0;if(i[0].trim()||i.shift(),i.length>0&&!i[i.length-1].trim()&&i.pop(),e)if(i.length>e)i.splice(e);else for(;i.length<e;)i.push("");for(;r<i.length;r++)i[r]=i[r].trim().replace(/\\\|/g,"|");return i}function Fi(t,e,n){const i=t.length;if(i===0)return"";let r=0;for(;r<i;){const s=t.charAt(i-r-1);if(s===e&&!n)r++;else if(s!==e&&n)r++;else break}return t.slice(0,i-r)}function Bd(t,e){if(t.indexOf(e[1])===-1)return-1;let n=0;for(let i=0;i<t.length;i++)if(t[i]==="\\")i++;else if(t[i]===e[0])n++;else if(t[i]===e[1]&&(n--,n<0))return i;return-1}function go(t,e,n,i){const r=e.href,s=e.title?Ft(e.title):null,o=t[1].replace(/\\([\[\]])/g,"$1");if(t[0].charAt(0)!=="!"){i.state.inLink=!0;const a={type:"link",raw:n,href:r,title:s,text:o,tokens:i.inlineTokens(o)};return i.state.inLink=!1,a}return{type:"image",raw:n,href:r,title:s,text:Ft(o)}}function Nd(t,e){const n=t.match(/^(\s+)(?:```)/);if(n===null)return e;const i=n[1];return e.split(`
`).map(r=>{const s=r.match(/^\s+/);if(s===null)return r;const[o]=s;return o.length>=i.length?r.slice(i.length):r}).join(`
`)}class Wi{constructor(e){K(this,"options");K(this,"rules");K(this,"lexer");this.options=e||Dn}space(e){const n=this.rules.block.newline.exec(e);if(n&&n[0].length>0)return{type:"space",raw:n[0]}}code(e){const n=this.rules.block.code.exec(e);if(n){const i=n[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:n[0],codeBlockStyle:"indented",text:this.options.pedantic?i:Fi(i,`
`)}}}fences(e){const n=this.rules.block.fences.exec(e);if(n){const i=n[0],r=Nd(i,n[3]||"");return{type:"code",raw:i,lang:n[2]?n[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):n[2],text:r}}}heading(e){const n=this.rules.block.heading.exec(e);if(n){let i=n[2].trim();if(/#$/.test(i)){const r=Fi(i,"#");(this.options.pedantic||!r||/ $/.test(r))&&(i=r.trim())}return{type:"heading",raw:n[0],depth:n[1].length,text:i,tokens:this.lexer.inline(i)}}}hr(e){const n=this.rules.block.hr.exec(e);if(n)return{type:"hr",raw:n[0]}}blockquote(e){const n=this.rules.block.blockquote.exec(e);if(n){const i=Fi(n[0].replace(/^ *>[ \t]?/gm,""),`
`),r=this.lexer.state.top;this.lexer.state.top=!0;const s=this.lexer.blockTokens(i);return this.lexer.state.top=r,{type:"blockquote",raw:n[0],tokens:s,text:i}}}list(e){let n=this.rules.block.list.exec(e);if(n){let i=n[1].trim();const r=i.length>1,s={type:"list",raw:"",ordered:r,start:r?+i.slice(0,-1):"",loose:!1,items:[]};i=r?`\\d{1,9}\\${i.slice(-1)}`:`\\${i}`,this.options.pedantic&&(i=r?i:"[*+-]");const o=new RegExp(`^( {0,3}${i})((?:[	 ][^\\n]*)?(?:\\n|$))`);let a="",l="",u=!1;for(;e;){let c=!1;if(!(n=o.exec(e))||this.rules.block.hr.test(e))break;a=n[0],e=e.substring(a.length);let h=n[2].split(`
`,1)[0].replace(/^\t+/,D=>" ".repeat(3*D.length)),f=e.split(`
`,1)[0],d=0;this.options.pedantic?(d=2,l=h.trimStart()):(d=n[2].search(/[^ ]/),d=d>4?1:d,l=h.slice(d),d+=n[1].length);let m=!1;if(!h&&/^ *$/.test(f)&&(a+=f+`
`,e=e.substring(f.length+1),c=!0),!c){const D=new RegExp(`^ {0,${Math.min(3,d-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),y=new RegExp(`^ {0,${Math.min(3,d-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),g=new RegExp(`^ {0,${Math.min(3,d-1)}}(?:\`\`\`|~~~)`),A=new RegExp(`^ {0,${Math.min(3,d-1)}}#`);for(;e;){const w=e.split(`
`,1)[0];if(f=w,this.options.pedantic&&(f=f.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),g.test(f)||A.test(f)||D.test(f)||y.test(e))break;if(f.search(/[^ ]/)>=d||!f.trim())l+=`
`+f.slice(d);else{if(m||h.search(/[^ ]/)>=4||g.test(h)||A.test(h)||y.test(h))break;l+=`
`+f}!m&&!f.trim()&&(m=!0),a+=w+`
`,e=e.substring(w.length+1),h=f.slice(d)}}s.loose||(u?s.loose=!0:/\n *\n *$/.test(a)&&(u=!0));let p=null,b;this.options.gfm&&(p=/^\[[ xX]\] /.exec(l),p&&(b=p[0]!=="[ ] ",l=l.replace(/^\[[ xX]\] +/,""))),s.items.push({type:"list_item",raw:a,task:!!p,checked:b,loose:!1,text:l,tokens:[]}),s.raw+=a}s.items[s.items.length-1].raw=a.trimEnd(),s.items[s.items.length-1].text=l.trimEnd(),s.raw=s.raw.trimEnd();for(let c=0;c<s.items.length;c++)if(this.lexer.state.top=!1,s.items[c].tokens=this.lexer.blockTokens(s.items[c].text,[]),!s.loose){const h=s.items[c].tokens.filter(d=>d.type==="space"),f=h.length>0&&h.some(d=>/\n.*\n/.test(d.raw));s.loose=f}if(s.loose)for(let c=0;c<s.items.length;c++)s.items[c].loose=!0;return s}}html(e){const n=this.rules.block.html.exec(e);if(n)return{type:"html",block:!0,raw:n[0],pre:n[1]==="pre"||n[1]==="script"||n[1]==="style",text:n[0]}}def(e){const n=this.rules.block.def.exec(e);if(n){const i=n[1].toLowerCase().replace(/\s+/g," "),r=n[2]?n[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",s=n[3]?n[3].substring(1,n[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):n[3];return{type:"def",tag:i,raw:n[0],href:r,title:s}}}table(e){const n=this.rules.block.table.exec(e);if(!n||!/[:|]/.test(n[2]))return;const i=_o(n[1]),r=n[2].replace(/^\||\| *$/g,"").split("|"),s=n[3]&&n[3].trim()?n[3].replace(/\n[ \t]*$/,"").split(`
`):[],o={type:"table",raw:n[0],header:[],align:[],rows:[]};if(i.length===r.length){for(const a of r)/^ *-+: *$/.test(a)?o.align.push("right"):/^ *:-+: *$/.test(a)?o.align.push("center"):/^ *:-+ *$/.test(a)?o.align.push("left"):o.align.push(null);for(const a of i)o.header.push({text:a,tokens:this.lexer.inline(a)});for(const a of s)o.rows.push(_o(a,o.header.length).map(l=>({text:l,tokens:this.lexer.inline(l)})));return o}}lheading(e){const n=this.rules.block.lheading.exec(e);if(n)return{type:"heading",raw:n[0],depth:n[2].charAt(0)==="="?1:2,text:n[1],tokens:this.lexer.inline(n[1])}}paragraph(e){const n=this.rules.block.paragraph.exec(e);if(n){const i=n[1].charAt(n[1].length-1)===`
`?n[1].slice(0,-1):n[1];return{type:"paragraph",raw:n[0],text:i,tokens:this.lexer.inline(i)}}}text(e){const n=this.rules.block.text.exec(e);if(n)return{type:"text",raw:n[0],text:n[0],tokens:this.lexer.inline(n[0])}}escape(e){const n=this.rules.inline.escape.exec(e);if(n)return{type:"escape",raw:n[0],text:Ft(n[1])}}tag(e){const n=this.rules.inline.tag.exec(e);if(n)return!this.lexer.state.inLink&&/^<a /i.test(n[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(n[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(n[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(n[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:n[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:n[0]}}link(e){const n=this.rules.inline.link.exec(e);if(n){const i=n[2].trim();if(!this.options.pedantic&&/^</.test(i)){if(!/>$/.test(i))return;const o=Fi(i.slice(0,-1),"\\");if((i.length-o.length)%2===0)return}else{const o=Bd(n[2],"()");if(o>-1){const l=(n[0].indexOf("!")===0?5:4)+n[1].length+o;n[2]=n[2].substring(0,o),n[0]=n[0].substring(0,l).trim(),n[3]=""}}let r=n[2],s="";if(this.options.pedantic){const o=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(r);o&&(r=o[1],s=o[3])}else s=n[3]?n[3].slice(1,-1):"";return r=r.trim(),/^</.test(r)&&(this.options.pedantic&&!/>$/.test(i)?r=r.slice(1):r=r.slice(1,-1)),go(n,{href:r&&r.replace(this.rules.inline.anyPunctuation,"$1"),title:s&&s.replace(this.rules.inline.anyPunctuation,"$1")},n[0],this.lexer)}}reflink(e,n){let i;if((i=this.rules.inline.reflink.exec(e))||(i=this.rules.inline.nolink.exec(e))){const r=(i[2]||i[1]).replace(/\s+/g," "),s=n[r.toLowerCase()];if(!s){const o=i[0].charAt(0);return{type:"text",raw:o,text:o}}return go(i,s,i[0],this.lexer)}}emStrong(e,n,i=""){let r=this.rules.inline.emStrongLDelim.exec(e);if(!r||r[3]&&i.match(/[\p{L}\p{N}]/u))return;if(!(r[1]||r[2]||"")||!i||this.rules.inline.punctuation.exec(i)){const o=[...r[0]].length-1;let a,l,u=o,c=0;const h=r[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(h.lastIndex=0,n=n.slice(-1*e.length+o);(r=h.exec(n))!=null;){if(a=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!a)continue;if(l=[...a].length,r[3]||r[4]){u+=l;continue}else if((r[5]||r[6])&&o%3&&!((o+l)%3)){c+=l;continue}if(u-=l,u>0)continue;l=Math.min(l,l+u+c);const f=[...r[0]][0].length,d=e.slice(0,o+r.index+f+l);if(Math.min(o,l)%2){const p=d.slice(1,-1);return{type:"em",raw:d,text:p,tokens:this.lexer.inlineTokens(p)}}const m=d.slice(2,-2);return{type:"strong",raw:d,text:m,tokens:this.lexer.inlineTokens(m)}}}}codespan(e){const n=this.rules.inline.code.exec(e);if(n){let i=n[2].replace(/\n/g," ");const r=/[^ ]/.test(i),s=/^ /.test(i)&&/ $/.test(i);return r&&s&&(i=i.substring(1,i.length-1)),i=Ft(i,!0),{type:"codespan",raw:n[0],text:i}}}br(e){const n=this.rules.inline.br.exec(e);if(n)return{type:"br",raw:n[0]}}del(e){const n=this.rules.inline.del.exec(e);if(n)return{type:"del",raw:n[0],text:n[2],tokens:this.lexer.inlineTokens(n[2])}}autolink(e){const n=this.rules.inline.autolink.exec(e);if(n){let i,r;return n[2]==="@"?(i=Ft(n[1]),r="mailto:"+i):(i=Ft(n[1]),r=i),{type:"link",raw:n[0],text:i,href:r,tokens:[{type:"text",raw:i,text:i}]}}}url(e){var i;let n;if(n=this.rules.inline.url.exec(e)){let r,s;if(n[2]==="@")r=Ft(n[0]),s="mailto:"+r;else{let o;do o=n[0],n[0]=((i=this.rules.inline._backpedal.exec(n[0]))==null?void 0:i[0])??"";while(o!==n[0]);r=Ft(n[0]),n[1]==="www."?s="http://"+n[0]:s=n[0]}return{type:"link",raw:n[0],text:r,href:s,tokens:[{type:"text",raw:r,text:r}]}}}inlineText(e){const n=this.rules.inline.text.exec(e);if(n){let i;return this.lexer.state.inRawBlock?i=n[0]:i=Ft(n[0]),{type:"text",raw:n[0],text:i}}}}const Md=/^(?: *(?:\n|$))+/,xd=/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,Hd=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,ci=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,zd=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,$l=/(?:[*+-]|\d{1,9}[.)])/,eu=Ke(/^(?!bull )((?:.|\n(?!\s*?\n|bull ))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,$l).getRegex(),hs=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Ud=/^[^\n]+/,ds=/(?!\s*\])(?:\\.|[^\[\]\\])+/,jd=Ke(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",ds).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Gd=Ke(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,$l).getRegex(),sr="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",ps=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,Vd=Ke("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",ps).replace("tag",sr).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),tu=Ke(hs).replace("hr",ci).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",sr).getRegex(),qd=Ke(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",tu).getRegex(),ms={blockquote:qd,code:xd,def:jd,fences:Hd,heading:zd,hr:ci,html:Vd,lheading:eu,list:Gd,newline:Md,paragraph:tu,table:ti,text:Ud},bo=Ke("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",ci).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",sr).getRegex(),Wd={...ms,table:bo,paragraph:Ke(hs).replace("hr",ci).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",bo).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",sr).getRegex()},Zd={...ms,html:Ke(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",ps).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:ti,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:Ke(hs).replace("hr",ci).replace("heading",` *#{1,6} *[^
]`).replace("lheading",eu).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},nu=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,Xd=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,iu=/^( {2,}|\\)\n(?!\s*$)/,Yd=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,fi="\\p{P}\\p{S}",Jd=Ke(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,fi).getRegex(),Qd=/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,Kd=Ke(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,fi).getRegex(),$d=Ke("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,fi).getRegex(),e0=Ke("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,fi).getRegex(),t0=Ke(/\\([punct])/,"gu").replace(/punct/g,fi).getRegex(),n0=Ke(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),i0=Ke(ps).replace("(?:-->|$)","-->").getRegex(),r0=Ke("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",i0).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Zi=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,s0=Ke(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",Zi).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),ru=Ke(/^!?\[(label)\]\[(ref)\]/).replace("label",Zi).replace("ref",ds).getRegex(),su=Ke(/^!?\[(ref)\](?:\[\])?/).replace("ref",ds).getRegex(),o0=Ke("reflink|nolink(?!\\()","g").replace("reflink",ru).replace("nolink",su).getRegex(),_s={_backpedal:ti,anyPunctuation:t0,autolink:n0,blockSkip:Qd,br:iu,code:Xd,del:ti,emStrongLDelim:Kd,emStrongRDelimAst:$d,emStrongRDelimUnd:e0,escape:nu,link:s0,nolink:su,punctuation:Jd,reflink:ru,reflinkSearch:o0,tag:r0,text:Yd,url:ti},a0={..._s,link:Ke(/^!?\[(label)\]\((.*?)\)/).replace("label",Zi).getRegex(),reflink:Ke(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Zi).getRegex()},Xr={..._s,escape:Ke(nu).replace("])","~|])").getRegex(),url:Ke(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},l0={...Xr,br:Ke(iu).replace("{2,}","*").getRegex(),text:Ke(Xr.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},Ti={normal:ms,gfm:Wd,pedantic:Zd},Wn={normal:_s,gfm:Xr,breaks:l0,pedantic:a0};class qt{constructor(e){K(this,"tokens");K(this,"options");K(this,"state");K(this,"tokenizer");K(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||Dn,this.options.tokenizer=this.options.tokenizer||new Wi,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const n={block:Ti.normal,inline:Wn.normal};this.options.pedantic?(n.block=Ti.pedantic,n.inline=Wn.pedantic):this.options.gfm&&(n.block=Ti.gfm,this.options.breaks?n.inline=Wn.breaks:n.inline=Wn.gfm),this.tokenizer.rules=n}static get rules(){return{block:Ti,inline:Wn}}static lex(e,n){return new qt(n).lex(e)}static lexInline(e,n){return new qt(n).inlineTokens(e)}lex(e){e=e.replace(/\r\n|\r/g,`
`),this.blockTokens(e,this.tokens);for(let n=0;n<this.inlineQueue.length;n++){const i=this.inlineQueue[n];this.inlineTokens(i.src,i.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,n=[]){this.options.pedantic?e=e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e=e.replace(/^( *)(\t+)/gm,(a,l,u)=>l+"    ".repeat(u.length));let i,r,s,o;for(;e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(a=>(i=a.call({lexer:this},e,n))?(e=e.substring(i.raw.length),n.push(i),!0):!1))){if(i=this.tokenizer.space(e)){e=e.substring(i.raw.length),i.raw.length===1&&n.length>0?n[n.length-1].raw+=`
`:n.push(i);continue}if(i=this.tokenizer.code(e)){e=e.substring(i.raw.length),r=n[n.length-1],r&&(r.type==="paragraph"||r.type==="text")?(r.raw+=`
`+i.raw,r.text+=`
`+i.text,this.inlineQueue[this.inlineQueue.length-1].src=r.text):n.push(i);continue}if(i=this.tokenizer.fences(e)){e=e.substring(i.raw.length),n.push(i);continue}if(i=this.tokenizer.heading(e)){e=e.substring(i.raw.length),n.push(i);continue}if(i=this.tokenizer.hr(e)){e=e.substring(i.raw.length),n.push(i);continue}if(i=this.tokenizer.blockquote(e)){e=e.substring(i.raw.length),n.push(i);continue}if(i=this.tokenizer.list(e)){e=e.substring(i.raw.length),n.push(i);continue}if(i=this.tokenizer.html(e)){e=e.substring(i.raw.length),n.push(i);continue}if(i=this.tokenizer.def(e)){e=e.substring(i.raw.length),r=n[n.length-1],r&&(r.type==="paragraph"||r.type==="text")?(r.raw+=`
`+i.raw,r.text+=`
`+i.raw,this.inlineQueue[this.inlineQueue.length-1].src=r.text):this.tokens.links[i.tag]||(this.tokens.links[i.tag]={href:i.href,title:i.title});continue}if(i=this.tokenizer.table(e)){e=e.substring(i.raw.length),n.push(i);continue}if(i=this.tokenizer.lheading(e)){e=e.substring(i.raw.length),n.push(i);continue}if(s=e,this.options.extensions&&this.options.extensions.startBlock){let a=1/0;const l=e.slice(1);let u;this.options.extensions.startBlock.forEach(c=>{u=c.call({lexer:this},l),typeof u=="number"&&u>=0&&(a=Math.min(a,u))}),a<1/0&&a>=0&&(s=e.substring(0,a+1))}if(this.state.top&&(i=this.tokenizer.paragraph(s))){r=n[n.length-1],o&&r.type==="paragraph"?(r.raw+=`
`+i.raw,r.text+=`
`+i.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=r.text):n.push(i),o=s.length!==e.length,e=e.substring(i.raw.length);continue}if(i=this.tokenizer.text(e)){e=e.substring(i.raw.length),r=n[n.length-1],r&&r.type==="text"?(r.raw+=`
`+i.raw,r.text+=`
`+i.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=r.text):n.push(i);continue}if(e){const a="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(a);break}else throw new Error(a)}}return this.state.top=!0,n}inline(e,n=[]){return this.inlineQueue.push({src:e,tokens:n}),n}inlineTokens(e,n=[]){let i,r,s,o=e,a,l,u;if(this.tokens.links){const c=Object.keys(this.tokens.links);if(c.length>0)for(;(a=this.tokenizer.rules.inline.reflinkSearch.exec(o))!=null;)c.includes(a[0].slice(a[0].lastIndexOf("[")+1,-1))&&(o=o.slice(0,a.index)+"["+"a".repeat(a[0].length-2)+"]"+o.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(a=this.tokenizer.rules.inline.blockSkip.exec(o))!=null;)o=o.slice(0,a.index)+"["+"a".repeat(a[0].length-2)+"]"+o.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(a=this.tokenizer.rules.inline.anyPunctuation.exec(o))!=null;)o=o.slice(0,a.index)+"++"+o.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;e;)if(l||(u=""),l=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(c=>(i=c.call({lexer:this},e,n))?(e=e.substring(i.raw.length),n.push(i),!0):!1))){if(i=this.tokenizer.escape(e)){e=e.substring(i.raw.length),n.push(i);continue}if(i=this.tokenizer.tag(e)){e=e.substring(i.raw.length),r=n[n.length-1],r&&i.type==="text"&&r.type==="text"?(r.raw+=i.raw,r.text+=i.text):n.push(i);continue}if(i=this.tokenizer.link(e)){e=e.substring(i.raw.length),n.push(i);continue}if(i=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(i.raw.length),r=n[n.length-1],r&&i.type==="text"&&r.type==="text"?(r.raw+=i.raw,r.text+=i.text):n.push(i);continue}if(i=this.tokenizer.emStrong(e,o,u)){e=e.substring(i.raw.length),n.push(i);continue}if(i=this.tokenizer.codespan(e)){e=e.substring(i.raw.length),n.push(i);continue}if(i=this.tokenizer.br(e)){e=e.substring(i.raw.length),n.push(i);continue}if(i=this.tokenizer.del(e)){e=e.substring(i.raw.length),n.push(i);continue}if(i=this.tokenizer.autolink(e)){e=e.substring(i.raw.length),n.push(i);continue}if(!this.state.inLink&&(i=this.tokenizer.url(e))){e=e.substring(i.raw.length),n.push(i);continue}if(s=e,this.options.extensions&&this.options.extensions.startInline){let c=1/0;const h=e.slice(1);let f;this.options.extensions.startInline.forEach(d=>{f=d.call({lexer:this},h),typeof f=="number"&&f>=0&&(c=Math.min(c,f))}),c<1/0&&c>=0&&(s=e.substring(0,c+1))}if(i=this.tokenizer.inlineText(s)){e=e.substring(i.raw.length),i.raw.slice(-1)!=="_"&&(u=i.raw.slice(-1)),l=!0,r=n[n.length-1],r&&r.type==="text"?(r.raw+=i.raw,r.text+=i.text):n.push(i);continue}if(e){const c="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(c);break}else throw new Error(c)}}return n}}class Xi{constructor(e){K(this,"options");this.options=e||Dn}code(e,n,i){var s;const r=(s=(n||"").match(/^\S*/))==null?void 0:s[0];return e=e.replace(/\n$/,"")+`
`,r?'<pre><code class="language-'+Ft(r)+'">'+(i?e:Ft(e,!0))+`</code></pre>
`:"<pre><code>"+(i?e:Ft(e,!0))+`</code></pre>
`}blockquote(e){return`<blockquote>
${e}</blockquote>
`}html(e,n){return e}heading(e,n,i){return`<h${n}>${e}</h${n}>
`}hr(){return`<hr>
`}list(e,n,i){const r=n?"ol":"ul",s=n&&i!==1?' start="'+i+'"':"";return"<"+r+s+`>
`+e+"</"+r+`>
`}listitem(e,n,i){return`<li>${e}</li>
`}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(e){return`<p>${e}</p>
`}table(e,n){return n&&(n=`<tbody>${n}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+n+`</table>
`}tablerow(e){return`<tr>
${e}</tr>
`}tablecell(e,n){const i=n.header?"th":"td";return(n.align?`<${i} align="${n.align}">`:`<${i}>`)+e+`</${i}>
`}strong(e){return`<strong>${e}</strong>`}em(e){return`<em>${e}</em>`}codespan(e){return`<code>${e}</code>`}br(){return"<br>"}del(e){return`<del>${e}</del>`}link(e,n,i){const r=mo(e);if(r===null)return i;e=r;let s='<a href="'+e+'"';return n&&(s+=' title="'+n+'"'),s+=">"+i+"</a>",s}image(e,n,i){const r=mo(e);if(r===null)return i;e=r;let s=`<img src="${e}" alt="${i}"`;return n&&(s+=` title="${n}"`),s+=">",s}text(e){return e}}class gs{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,n,i){return""+i}image(e,n,i){return""+i}br(){return""}}class Wt{constructor(e){K(this,"options");K(this,"renderer");K(this,"textRenderer");this.options=e||Dn,this.options.renderer=this.options.renderer||new Xi,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new gs}static parse(e,n){return new Wt(n).parse(e)}static parseInline(e,n){return new Wt(n).parseInline(e)}parse(e,n=!0){let i="";for(let r=0;r<e.length;r++){const s=e[r];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[s.type]){const o=s,a=this.options.extensions.renderers[o.type].call({parser:this},o);if(a!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(o.type)){i+=a||"";continue}}switch(s.type){case"space":continue;case"hr":{i+=this.renderer.hr();continue}case"heading":{const o=s;i+=this.renderer.heading(this.parseInline(o.tokens),o.depth,Id(this.parseInline(o.tokens,this.textRenderer)));continue}case"code":{const o=s;i+=this.renderer.code(o.text,o.lang,!!o.escaped);continue}case"table":{const o=s;let a="",l="";for(let c=0;c<o.header.length;c++)l+=this.renderer.tablecell(this.parseInline(o.header[c].tokens),{header:!0,align:o.align[c]});a+=this.renderer.tablerow(l);let u="";for(let c=0;c<o.rows.length;c++){const h=o.rows[c];l="";for(let f=0;f<h.length;f++)l+=this.renderer.tablecell(this.parseInline(h[f].tokens),{header:!1,align:o.align[f]});u+=this.renderer.tablerow(l)}i+=this.renderer.table(a,u);continue}case"blockquote":{const o=s,a=this.parse(o.tokens);i+=this.renderer.blockquote(a);continue}case"list":{const o=s,a=o.ordered,l=o.start,u=o.loose;let c="";for(let h=0;h<o.items.length;h++){const f=o.items[h],d=f.checked,m=f.task;let p="";if(f.task){const b=this.renderer.checkbox(!!d);u?f.tokens.length>0&&f.tokens[0].type==="paragraph"?(f.tokens[0].text=b+" "+f.tokens[0].text,f.tokens[0].tokens&&f.tokens[0].tokens.length>0&&f.tokens[0].tokens[0].type==="text"&&(f.tokens[0].tokens[0].text=b+" "+f.tokens[0].tokens[0].text)):f.tokens.unshift({type:"text",text:b+" "}):p+=b+" "}p+=this.parse(f.tokens,u),c+=this.renderer.listitem(p,m,!!d)}i+=this.renderer.list(c,a,l);continue}case"html":{const o=s;i+=this.renderer.html(o.text,o.block);continue}case"paragraph":{const o=s;i+=this.renderer.paragraph(this.parseInline(o.tokens));continue}case"text":{let o=s,a=o.tokens?this.parseInline(o.tokens):o.text;for(;r+1<e.length&&e[r+1].type==="text";)o=e[++r],a+=`
`+(o.tokens?this.parseInline(o.tokens):o.text);i+=n?this.renderer.paragraph(a):a;continue}default:{const o='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return i}parseInline(e,n){n=n||this.renderer;let i="";for(let r=0;r<e.length;r++){const s=e[r];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[s.type]){const o=this.options.extensions.renderers[s.type].call({parser:this},s);if(o!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(s.type)){i+=o||"";continue}}switch(s.type){case"escape":{const o=s;i+=n.text(o.text);break}case"html":{const o=s;i+=n.html(o.text);break}case"link":{const o=s;i+=n.link(o.href,o.title,this.parseInline(o.tokens,n));break}case"image":{const o=s;i+=n.image(o.href,o.title,o.text);break}case"strong":{const o=s;i+=n.strong(this.parseInline(o.tokens,n));break}case"em":{const o=s;i+=n.em(this.parseInline(o.tokens,n));break}case"codespan":{const o=s;i+=n.codespan(o.text);break}case"br":{i+=n.br();break}case"del":{const o=s;i+=n.del(this.parseInline(o.tokens,n));break}case"text":{const o=s;i+=n.text(o.text);break}default:{const o='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return i}}class ni{constructor(e){K(this,"options");this.options=e||Dn}preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}}K(ni,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var ai,Yr,Ji,au;class ou{constructor(...e){Ln(this,ai);Ln(this,Ji);K(this,"defaults",fs());K(this,"options",this.setOptions);K(this,"parse",ki(this,ai,Yr).call(this,qt.lex,Wt.parse));K(this,"parseInline",ki(this,ai,Yr).call(this,qt.lexInline,Wt.parseInline));K(this,"Parser",Wt);K(this,"Renderer",Xi);K(this,"TextRenderer",gs);K(this,"Lexer",qt);K(this,"Tokenizer",Wi);K(this,"Hooks",ni);this.use(...e)}walkTokens(e,n){var r,s;let i=[];for(const o of e)switch(i=i.concat(n.call(this,o)),o.type){case"table":{const a=o;for(const l of a.header)i=i.concat(this.walkTokens(l.tokens,n));for(const l of a.rows)for(const u of l)i=i.concat(this.walkTokens(u.tokens,n));break}case"list":{const a=o;i=i.concat(this.walkTokens(a.items,n));break}default:{const a=o;(s=(r=this.defaults.extensions)==null?void 0:r.childTokens)!=null&&s[a.type]?this.defaults.extensions.childTokens[a.type].forEach(l=>{const u=a[l].flat(1/0);i=i.concat(this.walkTokens(u,n))}):a.tokens&&(i=i.concat(this.walkTokens(a.tokens,n)))}}return i}use(...e){const n=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach(i=>{const r={...i};if(r.async=this.defaults.async||r.async||!1,i.extensions&&(i.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if("renderer"in s){const o=n.renderers[s.name];o?n.renderers[s.name]=function(...a){let l=s.renderer.apply(this,a);return l===!1&&(l=o.apply(this,a)),l}:n.renderers[s.name]=s.renderer}if("tokenizer"in s){if(!s.level||s.level!=="block"&&s.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const o=n[s.level];o?o.unshift(s.tokenizer):n[s.level]=[s.tokenizer],s.start&&(s.level==="block"?n.startBlock?n.startBlock.push(s.start):n.startBlock=[s.start]:s.level==="inline"&&(n.startInline?n.startInline.push(s.start):n.startInline=[s.start]))}"childTokens"in s&&s.childTokens&&(n.childTokens[s.name]=s.childTokens)}),r.extensions=n),i.renderer){const s=this.defaults.renderer||new Xi(this.defaults);for(const o in i.renderer){if(!(o in s))throw new Error(`renderer '${o}' does not exist`);if(o==="options")continue;const a=o,l=i.renderer[a],u=s[a];s[a]=(...c)=>{let h=l.apply(s,c);return h===!1&&(h=u.apply(s,c)),h||""}}r.renderer=s}if(i.tokenizer){const s=this.defaults.tokenizer||new Wi(this.defaults);for(const o in i.tokenizer){if(!(o in s))throw new Error(`tokenizer '${o}' does not exist`);if(["options","rules","lexer"].includes(o))continue;const a=o,l=i.tokenizer[a],u=s[a];s[a]=(...c)=>{let h=l.apply(s,c);return h===!1&&(h=u.apply(s,c)),h}}r.tokenizer=s}if(i.hooks){const s=this.defaults.hooks||new ni;for(const o in i.hooks){if(!(o in s))throw new Error(`hook '${o}' does not exist`);if(o==="options")continue;const a=o,l=i.hooks[a],u=s[a];ni.passThroughHooks.has(o)?s[a]=c=>{if(this.defaults.async)return Promise.resolve(l.call(s,c)).then(f=>u.call(s,f));const h=l.call(s,c);return u.call(s,h)}:s[a]=(...c)=>{let h=l.apply(s,c);return h===!1&&(h=u.apply(s,c)),h}}r.hooks=s}if(i.walkTokens){const s=this.defaults.walkTokens,o=i.walkTokens;r.walkTokens=function(a){let l=[];return l.push(o.call(this,a)),s&&(l=l.concat(s.call(this,a))),l}}this.defaults={...this.defaults,...r}}),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,n){return qt.lex(e,n??this.defaults)}parser(e,n){return Wt.parse(e,n??this.defaults)}}ai=new WeakSet,Yr=function(e,n){return(i,r)=>{const s={...r},o={...this.defaults,...s};this.defaults.async===!0&&s.async===!1&&(o.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),o.async=!0);const a=ki(this,Ji,au).call(this,!!o.silent,!!o.async);if(typeof i>"u"||i===null)return a(new Error("marked(): input parameter is undefined or null"));if(typeof i!="string")return a(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(i)+", string expected"));if(o.hooks&&(o.hooks.options=o),o.async)return Promise.resolve(o.hooks?o.hooks.preprocess(i):i).then(l=>e(l,o)).then(l=>o.hooks?o.hooks.processAllTokens(l):l).then(l=>o.walkTokens?Promise.all(this.walkTokens(l,o.walkTokens)).then(()=>l):l).then(l=>n(l,o)).then(l=>o.hooks?o.hooks.postprocess(l):l).catch(a);try{o.hooks&&(i=o.hooks.preprocess(i));let l=e(i,o);o.hooks&&(l=o.hooks.processAllTokens(l)),o.walkTokens&&this.walkTokens(l,o.walkTokens);let u=n(l,o);return o.hooks&&(u=o.hooks.postprocess(u)),u}catch(l){return a(l)}}},Ji=new WeakSet,au=function(e,n){return i=>{if(i.message+=`
Please report this to https://github.com/markedjs/marked.`,e){const r="<p>An error occurred:</p><pre>"+Ft(i.message+"",!0)+"</pre>";return n?Promise.resolve(r):r}if(n)return Promise.reject(i);throw i}};const bn=new ou;function Qe(t,e){return bn.parse(t,e)}Qe.options=Qe.setOptions=function(t){return bn.setOptions(t),Qe.defaults=bn.defaults,Jl(Qe.defaults),Qe};Qe.getDefaults=fs;Qe.defaults=Dn;Qe.use=function(...t){return bn.use(...t),Qe.defaults=bn.defaults,Jl(Qe.defaults),Qe};Qe.walkTokens=function(t,e){return bn.walkTokens(t,e)};Qe.parseInline=bn.parseInline;Qe.Parser=Wt;Qe.parser=Wt.parse;Qe.Renderer=Xi;Qe.TextRenderer=gs;Qe.Lexer=qt;Qe.lexer=qt.lex;Qe.Tokenizer=Wi;Qe.Hooks=ni;Qe.parse=Qe;Qe.options;Qe.setOptions;Qe.use;Qe.walkTokens;Qe.parseInline;Wt.parse;qt.lex;function u0(t){if(typeof t=="function"&&(t={highlight:t}),!t||typeof t.highlight!="function")throw new Error("Must provide highlight function");return typeof t.langPrefix!="string"&&(t.langPrefix="language-"),typeof t.emptyLangClass!="string"&&(t.emptyLangClass=""),{async:!!t.async,walkTokens(e){if(e.type!=="code")return;const n=wo(e.lang);if(t.async)return Promise.resolve(t.highlight(e.text,n,e.lang||"")).then(Eo(e));const i=t.highlight(e.text,n,e.lang||"");if(i instanceof Promise)throw new Error("markedHighlight is not set to async but the highlight function is async. Set the async option to true on markedHighlight to await the async highlight function.");Eo(e)(i)},useNewRenderer:!0,renderer:{code(e,n,i){typeof e=="object"&&(i=e.escaped,n=e.lang,e=e.text);const r=wo(n),s=r?t.langPrefix+yo(r):t.emptyLangClass,o=s?` class="${s}"`:"";return e=e.replace(/\n$/,""),`<pre><code${o}>${i?e:yo(e,!0)}
</code></pre>`}}}}function wo(t){return(t||"").match(/\S*/)[0]}function Eo(t){return e=>{typeof e=="string"&&e!==t.text&&(t.escaped=!0,t.text=e)}}const lu=/[&<>"']/,c0=new RegExp(lu.source,"g"),uu=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,f0=new RegExp(uu.source,"g"),h0={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},vo=t=>h0[t];function yo(t,e){if(e){if(lu.test(t))return t.replace(c0,vo)}else if(uu.test(t))return t.replace(f0,vo);return t}const d0=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g,p0=Object.hasOwnProperty;class bs{constructor(){this.occurrences,this.reset()}slug(e,n){const i=this;let r=m0(e,n===!0);const s=r;for(;p0.call(i.occurrences,r);)i.occurrences[s]++,r=s+"-"+i.occurrences[s];return i.occurrences[r]=0,r}reset(){this.occurrences=Object.create(null)}}function m0(t,e){return typeof t!="string"?"":(e||(t=t.toLowerCase()),t.replace(d0,"").replace(/ /g,"-"))}let cu=new bs,fu=[];function _0({prefix:t="",globalSlugs:e=!1}={}){return{headerIds:!1,hooks:{preprocess(n){return e||g0(),n}},renderer:{heading(n,i,r){r=r.toLowerCase().trim().replace(/<[!\/a-z].*?>/gi,"");const s=`${t}${cu.slug(r)}`,o={level:i,text:n,id:s};return fu.push(o),`<h${i} id="${s}">${n}</h${i}>
`}}}}function g0(){fu=[],cu=new bs}var hu={exports:{}};(function(t){var e=typeof window<"u"?window:typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope?self:{};/**
 * Prism: Lightweight, robust, elegant syntax highlighting
 *
 * @license MIT <https://opensource.org/licenses/MIT>
 * <AUTHOR> Verou <https://lea.verou.me>
 * @namespace
 * @public
 */var n=function(i){var r=/(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i,s=0,o={},a={manual:i.Prism&&i.Prism.manual,disableWorkerMessageHandler:i.Prism&&i.Prism.disableWorkerMessageHandler,util:{encode:function y(g){return g instanceof l?new l(g.type,y(g.content),g.alias):Array.isArray(g)?g.map(y):g.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(y){return Object.prototype.toString.call(y).slice(8,-1)},objId:function(y){return y.__id||Object.defineProperty(y,"__id",{value:++s}),y.__id},clone:function y(g,A){A=A||{};var w,E;switch(a.util.type(g)){case"Object":if(E=a.util.objId(g),A[E])return A[E];w={},A[E]=w;for(var S in g)g.hasOwnProperty(S)&&(w[S]=y(g[S],A));return w;case"Array":return E=a.util.objId(g),A[E]?A[E]:(w=[],A[E]=w,g.forEach(function(z,C){w[C]=y(z,A)}),w);default:return g}},getLanguage:function(y){for(;y;){var g=r.exec(y.className);if(g)return g[1].toLowerCase();y=y.parentElement}return"none"},setLanguage:function(y,g){y.className=y.className.replace(RegExp(r,"gi"),""),y.classList.add("language-"+g)},currentScript:function(){if(typeof document>"u")return null;if("currentScript"in document)return document.currentScript;try{throw new Error}catch(w){var y=(/at [^(\r\n]*\((.*):[^:]+:[^:]+\)$/i.exec(w.stack)||[])[1];if(y){var g=document.getElementsByTagName("script");for(var A in g)if(g[A].src==y)return g[A]}return null}},isActive:function(y,g,A){for(var w="no-"+g;y;){var E=y.classList;if(E.contains(g))return!0;if(E.contains(w))return!1;y=y.parentElement}return!!A}},languages:{plain:o,plaintext:o,text:o,txt:o,extend:function(y,g){var A=a.util.clone(a.languages[y]);for(var w in g)A[w]=g[w];return A},insertBefore:function(y,g,A,w){w=w||a.languages;var E=w[y],S={};for(var z in E)if(E.hasOwnProperty(z)){if(z==g)for(var C in A)A.hasOwnProperty(C)&&(S[C]=A[C]);A.hasOwnProperty(z)||(S[z]=E[z])}var N=w[y];return w[y]=S,a.languages.DFS(a.languages,function(re,x){x===N&&re!=y&&(this[re]=S)}),S},DFS:function y(g,A,w,E){E=E||{};var S=a.util.objId;for(var z in g)if(g.hasOwnProperty(z)){A.call(g,z,g[z],w||z);var C=g[z],N=a.util.type(C);N==="Object"&&!E[S(C)]?(E[S(C)]=!0,y(C,A,null,E)):N==="Array"&&!E[S(C)]&&(E[S(C)]=!0,y(C,A,z,E))}}},plugins:{},highlightAll:function(y,g){a.highlightAllUnder(document,y,g)},highlightAllUnder:function(y,g,A){var w={callback:A,container:y,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};a.hooks.run("before-highlightall",w),w.elements=Array.prototype.slice.apply(w.container.querySelectorAll(w.selector)),a.hooks.run("before-all-elements-highlight",w);for(var E=0,S;S=w.elements[E++];)a.highlightElement(S,g===!0,w.callback)},highlightElement:function(y,g,A){var w=a.util.getLanguage(y),E=a.languages[w];a.util.setLanguage(y,w);var S=y.parentElement;S&&S.nodeName.toLowerCase()==="pre"&&a.util.setLanguage(S,w);var z=y.textContent,C={element:y,language:w,grammar:E,code:z};function N(x){C.highlightedCode=x,a.hooks.run("before-insert",C),C.element.innerHTML=C.highlightedCode,a.hooks.run("after-highlight",C),a.hooks.run("complete",C),A&&A.call(C.element)}if(a.hooks.run("before-sanity-check",C),S=C.element.parentElement,S&&S.nodeName.toLowerCase()==="pre"&&!S.hasAttribute("tabindex")&&S.setAttribute("tabindex","0"),!C.code){a.hooks.run("complete",C),A&&A.call(C.element);return}if(a.hooks.run("before-highlight",C),!C.grammar){N(a.util.encode(C.code));return}if(g&&i.Worker){var re=new Worker(a.filename);re.onmessage=function(x){N(x.data)},re.postMessage(JSON.stringify({language:C.language,code:C.code,immediateClose:!0}))}else N(a.highlight(C.code,C.grammar,C.language))},highlight:function(y,g,A){var w={code:y,grammar:g,language:A};if(a.hooks.run("before-tokenize",w),!w.grammar)throw new Error('The language "'+w.language+'" has no grammar.');return w.tokens=a.tokenize(w.code,w.grammar),a.hooks.run("after-tokenize",w),l.stringify(a.util.encode(w.tokens),w.language)},tokenize:function(y,g){var A=g.rest;if(A){for(var w in A)g[w]=A[w];delete g.rest}var E=new h;return f(E,E.head,y),c(y,E,g,E.head,0),m(E)},hooks:{all:{},add:function(y,g){var A=a.hooks.all;A[y]=A[y]||[],A[y].push(g)},run:function(y,g){var A=a.hooks.all[y];if(!(!A||!A.length))for(var w=0,E;E=A[w++];)E(g)}},Token:l};i.Prism=a;function l(y,g,A,w){this.type=y,this.content=g,this.alias=A,this.length=(w||"").length|0}l.stringify=function y(g,A){if(typeof g=="string")return g;if(Array.isArray(g)){var w="";return g.forEach(function(N){w+=y(N,A)}),w}var E={type:g.type,content:y(g.content,A),tag:"span",classes:["token",g.type],attributes:{},language:A},S=g.alias;S&&(Array.isArray(S)?Array.prototype.push.apply(E.classes,S):E.classes.push(S)),a.hooks.run("wrap",E);var z="";for(var C in E.attributes)z+=" "+C+'="'+(E.attributes[C]||"").replace(/"/g,"&quot;")+'"';return"<"+E.tag+' class="'+E.classes.join(" ")+'"'+z+">"+E.content+"</"+E.tag+">"};function u(y,g,A,w){y.lastIndex=g;var E=y.exec(A);if(E&&w&&E[1]){var S=E[1].length;E.index+=S,E[0]=E[0].slice(S)}return E}function c(y,g,A,w,E,S){for(var z in A)if(!(!A.hasOwnProperty(z)||!A[z])){var C=A[z];C=Array.isArray(C)?C:[C];for(var N=0;N<C.length;++N){if(S&&S.cause==z+","+N)return;var re=C[N],x=re.inside,le=!!re.lookbehind,me=!!re.greedy,ke=re.alias;if(me&&!re.pattern.global){var ue=re.pattern.toString().match(/[imsuy]*$/)[0];re.pattern=RegExp(re.pattern.source,ue+"g")}for(var Q=re.pattern||re,O=w.next,I=E;O!==g.tail&&!(S&&I>=S.reach);I+=O.value.length,O=O.next){var L=O.value;if(g.length>y.length)return;if(!(L instanceof l)){var k=1,J;if(me){if(J=u(Q,I,y,le),!J||J.index>=y.length)break;var Ce=J.index,V=J.index+J[0].length,$=I;for($+=O.value.length;Ce>=$;)O=O.next,$+=O.value.length;if($-=O.value.length,I=$,O.value instanceof l)continue;for(var q=O;q!==g.tail&&($<V||typeof q.value=="string");q=q.next)k++,$+=q.value.length;k--,L=y.slice(I,$),J.index-=I}else if(J=u(Q,0,L,le),!J)continue;var Ce=J.index,Ze=J[0],he=L.slice(0,Ce),Me=L.slice(Ce+Ze.length),it=I+L.length;S&&it>S.reach&&(S.reach=it);var $e=O.prev;he&&($e=f(g,$e,he),I+=he.length),d(g,$e,k);var ct=new l(z,x?a.tokenize(Ze,x):Ze,ke,Ze);if(O=f(g,$e,ct),Me&&f(g,O,Me),k>1){var et={cause:z+","+N,reach:it};c(y,g,A,O.prev,I,et),S&&et.reach>S.reach&&(S.reach=et.reach)}}}}}}function h(){var y={value:null,prev:null,next:null},g={value:null,prev:y,next:null};y.next=g,this.head=y,this.tail=g,this.length=0}function f(y,g,A){var w=g.next,E={value:A,prev:g,next:w};return g.next=E,w.prev=E,y.length++,E}function d(y,g,A){for(var w=g.next,E=0;E<A&&w!==y.tail;E++)w=w.next;g.next=w,w.prev=g,y.length-=E}function m(y){for(var g=[],A=y.head.next;A!==y.tail;)g.push(A.value),A=A.next;return g}if(!i.document)return i.addEventListener&&(a.disableWorkerMessageHandler||i.addEventListener("message",function(y){var g=JSON.parse(y.data),A=g.language,w=g.code,E=g.immediateClose;i.postMessage(a.highlight(w,a.languages[A],A)),E&&i.close()},!1)),a;var p=a.util.currentScript();p&&(a.filename=p.src,p.hasAttribute("data-manual")&&(a.manual=!0));function b(){a.manual||a.highlightAll()}if(!a.manual){var D=document.readyState;D==="loading"||D==="interactive"&&p&&p.defer?document.addEventListener("DOMContentLoaded",b):window.requestAnimationFrame?window.requestAnimationFrame(b):window.setTimeout(b,16)}return a}(e);t.exports&&(t.exports=n),typeof ks<"u"&&(ks.Prism=n),n.languages.markup={comment:{pattern:/<!--(?:(?!<!--)[\s\S])*?-->/,greedy:!0},prolog:{pattern:/<\?[\s\S]+?\?>/,greedy:!0},doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(^[^\[]*\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/i,name:/[^\s<>'"]+/}},cdata:{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,greedy:!0},tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"special-attr":[],"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},{pattern:/^(\s*)["']|["']$/,lookbehind:!0}]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},n.languages.markup.tag.inside["attr-value"].inside.entity=n.languages.markup.entity,n.languages.markup.doctype.inside["internal-subset"].inside=n.languages.markup,n.hooks.add("wrap",function(i){i.type==="entity"&&(i.attributes.title=i.content.replace(/&amp;/,"&"))}),Object.defineProperty(n.languages.markup.tag,"addInlined",{value:function(r,s){var o={};o["language-"+s]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:n.languages[s]},o.cdata=/^<!\[CDATA\[|\]\]>$/i;var a={"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:o}};a["language-"+s]={pattern:/[\s\S]+/,inside:n.languages[s]};var l={};l[r]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,function(){return r}),"i"),lookbehind:!0,greedy:!0,inside:a},n.languages.insertBefore("markup","cdata",l)}}),Object.defineProperty(n.languages.markup.tag,"addAttribute",{value:function(i,r){n.languages.markup.tag.inside["special-attr"].push({pattern:RegExp(/(^|["'\s])/.source+"(?:"+i+")"+/\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))/.source,"i"),lookbehind:!0,inside:{"attr-name":/^[^\s=]+/,"attr-value":{pattern:/=[\s\S]+/,inside:{value:{pattern:/(^=\s*(["']|(?!["'])))\S[\s\S]*(?=\2$)/,lookbehind:!0,alias:[r,"language-"+r],inside:n.languages[r]},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}}}})}}),n.languages.html=n.languages.markup,n.languages.mathml=n.languages.markup,n.languages.svg=n.languages.markup,n.languages.xml=n.languages.extend("markup",{}),n.languages.ssml=n.languages.xml,n.languages.atom=n.languages.xml,n.languages.rss=n.languages.xml,function(i){var r=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/;i.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:RegExp("@[\\w-](?:"+/[^;{\s"']|\s+(?!\s)/.source+"|"+r.source+")*?"+/(?:;|(?=\s*\{))/.source),inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+r.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+r.source+"$"),alias:"url"}}},selector:{pattern:RegExp(`(^|[{}\\s])[^{}\\s](?:[^{};"'\\s]|\\s+(?![\\s{])|`+r.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:r,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},i.languages.css.atrule.inside.rest=i.languages.css;var s=i.languages.markup;s&&(s.tag.addInlined("style","css"),s.tag.addAttribute("style","css"))}(n),n.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|trait)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\b/,boolean:/\b(?:false|true)\b/,function:/\b\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/},n.languages.javascript=n.languages.extend("clike",{"class-name":[n.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:{pattern:RegExp(/(^|[^\w$])/.source+"(?:"+(/NaN|Infinity/.source+"|"+/0[bB][01]+(?:_[01]+)*n?/.source+"|"+/0[oO][0-7]+(?:_[0-7]+)*n?/.source+"|"+/0[xX][\dA-Fa-f]+(?:_[\dA-Fa-f]+)*n?/.source+"|"+/\d+(?:_\d+)*n/.source+"|"+/(?:\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\.\d+(?:_\d+)*)(?:[Ee][+-]?\d+(?:_\d+)*)?/.source)+")"+/(?![\w$])/.source),lookbehind:!0},operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),n.languages.javascript["class-name"][0].pattern=/(\b(?:class|extends|implements|instanceof|interface|new)\s+)[\w.\\]+/,n.languages.insertBefore("javascript","keyword",{regex:{pattern:RegExp(/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)/.source+/\//.source+"(?:"+/(?:\[(?:[^\]\\\r\n]|\\.)*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}/.source+"|"+/(?:\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.)*\])*\])*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source+")"+/(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/.source),lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:n.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:n.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:n.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:n.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:n.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),n.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:n.languages.javascript}},string:/[\s\S]+/}},"string-property":{pattern:/((?:^|[,{])[ \t]*)(["'])(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2(?=\s*:)/m,lookbehind:!0,greedy:!0,alias:"property"}}),n.languages.insertBefore("javascript","operator",{"literal-property":{pattern:/((?:^|[,{])[ \t]*)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/m,lookbehind:!0,alias:"property"}}),n.languages.markup&&(n.languages.markup.tag.addInlined("script","javascript"),n.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,"javascript")),n.languages.js=n.languages.javascript,function(){if(typeof n>"u"||typeof document>"u")return;Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector);var i="Loading…",r=function(p,b){return"✖ Error "+p+" while fetching file: "+b},s="✖ Error: File does not exist or is empty",o={js:"javascript",py:"python",rb:"ruby",ps1:"powershell",psm1:"powershell",sh:"bash",bat:"batch",h:"c",tex:"latex"},a="data-src-status",l="loading",u="loaded",c="failed",h="pre[data-src]:not(["+a+'="'+u+'"]):not(['+a+'="'+l+'"])';function f(p,b,D){var y=new XMLHttpRequest;y.open("GET",p,!0),y.onreadystatechange=function(){y.readyState==4&&(y.status<400&&y.responseText?b(y.responseText):y.status>=400?D(r(y.status,y.statusText)):D(s))},y.send(null)}function d(p){var b=/^\s*(\d+)\s*(?:(,)\s*(?:(\d+)\s*)?)?$/.exec(p||"");if(b){var D=Number(b[1]),y=b[2],g=b[3];return y?g?[D,Number(g)]:[D,void 0]:[D,D]}}n.hooks.add("before-highlightall",function(p){p.selector+=", "+h}),n.hooks.add("before-sanity-check",function(p){var b=p.element;if(b.matches(h)){p.code="",b.setAttribute(a,l);var D=b.appendChild(document.createElement("CODE"));D.textContent=i;var y=b.getAttribute("data-src"),g=p.language;if(g==="none"){var A=(/\.(\w+)$/.exec(y)||[,"none"])[1];g=o[A]||A}n.util.setLanguage(D,g),n.util.setLanguage(b,g);var w=n.plugins.autoloader;w&&w.loadLanguages(g),f(y,function(E){b.setAttribute(a,u);var S=d(b.getAttribute("data-range"));if(S){var z=E.split(/\r\n?|\n/g),C=S[0],N=S[1]==null?z.length:S[1];C<0&&(C+=z.length),C=Math.max(0,Math.min(C-1,z.length)),N<0&&(N+=z.length),N=Math.max(0,Math.min(N,z.length)),E=z.slice(C,N).join(`
`),b.hasAttribute("data-start")||b.setAttribute("data-start",String(C+1))}D.textContent=E,n.highlightElement(D)},function(E){b.setAttribute(a,c),D.textContent=E})}}),n.plugins.fileHighlight={highlight:function(b){for(var D=(b||document).querySelectorAll(h),y=0,g;g=D[y++];)n.highlightElement(g)}};var m=!1;n.fileHighlight=function(){m||(console.warn("Prism.fileHighlight is deprecated. Use `Prism.plugins.fileHighlight.highlight` instead."),m=!0),n.plugins.fileHighlight.highlight.apply(this,arguments)}}()})(hu);var Ni=hu.exports;const Hg=ja(Ni);Prism.languages.python={comment:{pattern:/(^|[^\\])#.*/,lookbehind:!0,greedy:!0},"string-interpolation":{pattern:/(?:f|fr|rf)(?:("""|''')[\s\S]*?\1|("|')(?:\\.|(?!\2)[^\\\r\n])*\2)/i,greedy:!0,inside:{interpolation:{pattern:/((?:^|[^{])(?:\{\{)*)\{(?!\{)(?:[^{}]|\{(?!\{)(?:[^{}]|\{(?!\{)(?:[^{}])+\})+\})+\}/,lookbehind:!0,inside:{"format-spec":{pattern:/(:)[^:(){}]+(?=\}$)/,lookbehind:!0},"conversion-option":{pattern:/![sra](?=[:}]$)/,alias:"punctuation"},rest:null}},string:/[\s\S]+/}},"triple-quoted-string":{pattern:/(?:[rub]|br|rb)?("""|''')[\s\S]*?\1/i,greedy:!0,alias:"string"},string:{pattern:/(?:[rub]|br|rb)?("|')(?:\\.|(?!\1)[^\\\r\n])*\1/i,greedy:!0},function:{pattern:/((?:^|\s)def[ \t]+)[a-zA-Z_]\w*(?=\s*\()/g,lookbehind:!0},"class-name":{pattern:/(\bclass\s+)\w+/i,lookbehind:!0},decorator:{pattern:/(^[\t ]*)@\w+(?:\.\w+)*/m,lookbehind:!0,alias:["annotation","punctuation"],inside:{punctuation:/\./}},keyword:/\b(?:_(?=\s*:)|and|as|assert|async|await|break|case|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|match|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\b/,builtin:/\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\b/,boolean:/\b(?:False|None|True)\b/,number:/\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\b|(?:\b\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\B\.\d+(?:_\d+)*)(?:e[+-]?\d+(?:_\d+)*)?j?(?!\w)/i,operator:/[-+%=]=?|!=|:=|\*\*?=?|\/\/?=?|<[<=>]?|>[=>]?|[&|^~]/,punctuation:/[{}[\];(),.:]/};Prism.languages.python["string-interpolation"].inside.interpolation.inside.rest=Prism.languages.python;Prism.languages.py=Prism.languages.python;(function(t){var e=/\\(?:[^a-z()[\]]|[a-z*]+)/i,n={"equation-command":{pattern:e,alias:"regex"}};t.languages.latex={comment:/%.*/,cdata:{pattern:/(\\begin\{((?:lstlisting|verbatim)\*?)\})[\s\S]*?(?=\\end\{\2\})/,lookbehind:!0},equation:[{pattern:/\$\$(?:\\[\s\S]|[^\\$])+\$\$|\$(?:\\[\s\S]|[^\\$])+\$|\\\([\s\S]*?\\\)|\\\[[\s\S]*?\\\]/,inside:n,alias:"string"},{pattern:/(\\begin\{((?:align|eqnarray|equation|gather|math|multline)\*?)\})[\s\S]*?(?=\\end\{\2\})/,lookbehind:!0,inside:n,alias:"string"}],keyword:{pattern:/(\\(?:begin|cite|documentclass|end|label|ref|usepackage)(?:\[[^\]]+\])?\{)[^}]+(?=\})/,lookbehind:!0},url:{pattern:/(\\url\{)[^}]+(?=\})/,lookbehind:!0},headline:{pattern:/(\\(?:chapter|frametitle|paragraph|part|section|subparagraph|subsection|subsubparagraph|subsubsection|subsubsubparagraph)\*?(?:\[[^\]]+\])?\{)[^}]+(?=\})/,lookbehind:!0,alias:"class-name"},function:{pattern:e,alias:"selector"},punctuation:/[[\]{}&]/},t.languages.tex=t.languages.latex,t.languages.context=t.languages.latex})(Prism);(function(t){var e="\\b(?:BASH|BASHOPTS|BASH_ALIASES|BASH_ARGC|BASH_ARGV|BASH_CMDS|BASH_COMPLETION_COMPAT_DIR|BASH_LINENO|BASH_REMATCH|BASH_SOURCE|BASH_VERSINFO|BASH_VERSION|COLORTERM|COLUMNS|COMP_WORDBREAKS|DBUS_SESSION_BUS_ADDRESS|DEFAULTS_PATH|DESKTOP_SESSION|DIRSTACK|DISPLAY|EUID|GDMSESSION|GDM_LANG|GNOME_KEYRING_CONTROL|GNOME_KEYRING_PID|GPG_AGENT_INFO|GROUPS|HISTCONTROL|HISTFILE|HISTFILESIZE|HISTSIZE|HOME|HOSTNAME|HOSTTYPE|IFS|INSTANCE|JOB|LANG|LANGUAGE|LC_ADDRESS|LC_ALL|LC_IDENTIFICATION|LC_MEASUREMENT|LC_MONETARY|LC_NAME|LC_NUMERIC|LC_PAPER|LC_TELEPHONE|LC_TIME|LESSCLOSE|LESSOPEN|LINES|LOGNAME|LS_COLORS|MACHTYPE|MAILCHECK|MANDATORY_PATH|NO_AT_BRIDGE|OLDPWD|OPTERR|OPTIND|ORBIT_SOCKETDIR|OSTYPE|PAPERSIZE|PATH|PIPESTATUS|PPID|PS1|PS2|PS3|PS4|PWD|RANDOM|REPLY|SECONDS|SELINUX_INIT|SESSION|SESSIONTYPE|SESSION_MANAGER|SHELL|SHELLOPTS|SHLVL|SSH_AUTH_SOCK|TERM|UID|UPSTART_EVENTS|UPSTART_INSTANCE|UPSTART_JOB|UPSTART_SESSION|USER|WINDOWID|XAUTHORITY|XDG_CONFIG_DIRS|XDG_CURRENT_DESKTOP|XDG_DATA_DIRS|XDG_GREETER_DATA_DIR|XDG_MENU_PREFIX|XDG_RUNTIME_DIR|XDG_SEAT|XDG_SEAT_PATH|XDG_SESSION_DESKTOP|XDG_SESSION_ID|XDG_SESSION_PATH|XDG_SESSION_TYPE|XDG_VTNR|XMODIFIERS)\\b",n={pattern:/(^(["']?)\w+\2)[ \t]+\S.*/,lookbehind:!0,alias:"punctuation",inside:null},i={bash:n,environment:{pattern:RegExp("\\$"+e),alias:"constant"},variable:[{pattern:/\$?\(\([\s\S]+?\)\)/,greedy:!0,inside:{variable:[{pattern:/(^\$\(\([\s\S]+)\)\)/,lookbehind:!0},/^\$\(\(/],number:/\b0x[\dA-Fa-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:[Ee]-?\d+)?/,operator:/--|\+\+|\*\*=?|<<=?|>>=?|&&|\|\||[=!+\-*/%<>^&|]=?|[?~:]/,punctuation:/\(\(?|\)\)?|,|;/}},{pattern:/\$\((?:\([^)]+\)|[^()])+\)|`[^`]+`/,greedy:!0,inside:{variable:/^\$\(|^`|\)$|`$/}},{pattern:/\$\{[^}]+\}/,greedy:!0,inside:{operator:/:[-=?+]?|[!\/]|##?|%%?|\^\^?|,,?/,punctuation:/[\[\]]/,environment:{pattern:RegExp("(\\{)"+e),lookbehind:!0,alias:"constant"}}},/\$(?:\w+|[#?*!@$])/],entity:/\\(?:[abceEfnrtv\\"]|O?[0-7]{1,3}|U[0-9a-fA-F]{8}|u[0-9a-fA-F]{4}|x[0-9a-fA-F]{1,2})/};t.languages.bash={shebang:{pattern:/^#!\s*\/.*/,alias:"important"},comment:{pattern:/(^|[^"{\\$])#.*/,lookbehind:!0},"function-name":[{pattern:/(\bfunction\s+)[\w-]+(?=(?:\s*\(?:\s*\))?\s*\{)/,lookbehind:!0,alias:"function"},{pattern:/\b[\w-]+(?=\s*\(\s*\)\s*\{)/,alias:"function"}],"for-or-select":{pattern:/(\b(?:for|select)\s+)\w+(?=\s+in\s)/,alias:"variable",lookbehind:!0},"assign-left":{pattern:/(^|[\s;|&]|[<>]\()\w+(?:\.\w+)*(?=\+?=)/,inside:{environment:{pattern:RegExp("(^|[\\s;|&]|[<>]\\()"+e),lookbehind:!0,alias:"constant"}},alias:"variable",lookbehind:!0},parameter:{pattern:/(^|\s)-{1,2}(?:\w+:[+-]?)?\w+(?:\.\w+)*(?=[=\s]|$)/,alias:"variable",lookbehind:!0},string:[{pattern:/((?:^|[^<])<<-?\s*)(\w+)\s[\s\S]*?(?:\r?\n|\r)\2/,lookbehind:!0,greedy:!0,inside:i},{pattern:/((?:^|[^<])<<-?\s*)(["'])(\w+)\2\s[\s\S]*?(?:\r?\n|\r)\3/,lookbehind:!0,greedy:!0,inside:{bash:n}},{pattern:/(^|[^\\](?:\\\\)*)"(?:\\[\s\S]|\$\([^)]+\)|\$(?!\()|`[^`]+`|[^"\\`$])*"/,lookbehind:!0,greedy:!0,inside:i},{pattern:/(^|[^$\\])'[^']*'/,lookbehind:!0,greedy:!0},{pattern:/\$'(?:[^'\\]|\\[\s\S])*'/,greedy:!0,inside:{entity:i.entity}}],environment:{pattern:RegExp("\\$?"+e),alias:"constant"},variable:i.variable,function:{pattern:/(^|[\s;|&]|[<>]\()(?:add|apropos|apt|apt-cache|apt-get|aptitude|aspell|automysqlbackup|awk|basename|bash|bc|bconsole|bg|bzip2|cal|cargo|cat|cfdisk|chgrp|chkconfig|chmod|chown|chroot|cksum|clear|cmp|column|comm|composer|cp|cron|crontab|csplit|curl|cut|date|dc|dd|ddrescue|debootstrap|df|diff|diff3|dig|dir|dircolors|dirname|dirs|dmesg|docker|docker-compose|du|egrep|eject|env|ethtool|expand|expect|expr|fdformat|fdisk|fg|fgrep|file|find|fmt|fold|format|free|fsck|ftp|fuser|gawk|git|gparted|grep|groupadd|groupdel|groupmod|groups|grub-mkconfig|gzip|halt|head|hg|history|host|hostname|htop|iconv|id|ifconfig|ifdown|ifup|import|install|ip|java|jobs|join|kill|killall|less|link|ln|locate|logname|logrotate|look|lpc|lpr|lprint|lprintd|lprintq|lprm|ls|lsof|lynx|make|man|mc|mdadm|mkconfig|mkdir|mke2fs|mkfifo|mkfs|mkisofs|mknod|mkswap|mmv|more|most|mount|mtools|mtr|mutt|mv|nano|nc|netstat|nice|nl|node|nohup|notify-send|npm|nslookup|op|open|parted|passwd|paste|pathchk|ping|pkill|pnpm|podman|podman-compose|popd|pr|printcap|printenv|ps|pushd|pv|quota|quotacheck|quotactl|ram|rar|rcp|reboot|remsync|rename|renice|rev|rm|rmdir|rpm|rsync|scp|screen|sdiff|sed|sendmail|seq|service|sftp|sh|shellcheck|shuf|shutdown|sleep|slocate|sort|split|ssh|stat|strace|su|sudo|sum|suspend|swapon|sync|sysctl|tac|tail|tar|tee|time|timeout|top|touch|tr|traceroute|tsort|tty|umount|uname|unexpand|uniq|units|unrar|unshar|unzip|update-grub|uptime|useradd|userdel|usermod|users|uudecode|uuencode|v|vcpkg|vdir|vi|vim|virsh|vmstat|wait|watch|wc|wget|whereis|which|who|whoami|write|xargs|xdg-open|yarn|yes|zenity|zip|zsh|zypper)(?=$|[)\s;|&])/,lookbehind:!0},keyword:{pattern:/(^|[\s;|&]|[<>]\()(?:case|do|done|elif|else|esac|fi|for|function|if|in|select|then|until|while)(?=$|[)\s;|&])/,lookbehind:!0},builtin:{pattern:/(^|[\s;|&]|[<>]\()(?:\.|:|alias|bind|break|builtin|caller|cd|command|continue|declare|echo|enable|eval|exec|exit|export|getopts|hash|help|let|local|logout|mapfile|printf|pwd|read|readarray|readonly|return|set|shift|shopt|source|test|times|trap|type|typeset|ulimit|umask|unalias|unset)(?=$|[)\s;|&])/,lookbehind:!0,alias:"class-name"},boolean:{pattern:/(^|[\s;|&]|[<>]\()(?:false|true)(?=$|[)\s;|&])/,lookbehind:!0},"file-descriptor":{pattern:/\B&\d\b/,alias:"important"},operator:{pattern:/\d?<>|>\||\+=|=[=~]?|!=?|<<[<-]?|[&\d]?>>|\d[<>]&?|[<>][&=]?|&[>&]?|\|[&|]?/,inside:{"file-descriptor":{pattern:/^\d/,alias:"important"}}},punctuation:/\$?\(\(?|\)\)?|\.\.|[{}[\];\\]/,number:{pattern:/(^|\s)(?:[1-9]\d*|0)(?:[.,]\d+)?\b/,lookbehind:!0}},n.inside=t.languages.bash;for(var r=["comment","function-name","for-or-select","assign-left","parameter","string","environment","function","keyword","builtin","boolean","file-descriptor","operator","punctuation","number"],s=i.variable[1].inside,o=0;o<r.length;o++)s[r[o]]=t.languages.bash[r[o]];t.languages.sh=t.languages.bash,t.languages.shell=t.languages.bash})(Prism);const b0='<svg class="md-link-icon" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true" fill="currentColor"><path d="m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z"></path></svg>',w0=`
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15" color="currentColor" aria-hidden="true" aria-label="Copy" stroke-width="1.3" width="15" height="15">
  <path fill="currentColor" d="M12.728 4.545v8.182H4.545V4.545zm0 -0.909H4.545a0.909 0.909 0 0 0 -0.909 0.909v8.182a0.909 0.909 0 0 0 0.909 0.909h8.182a0.909 0.909 0 0 0 0.909 -0.909V4.545a0.909 0.909 0 0 0 -0.909 -0.909"/>
  <path fill="currentColor" d="M1.818 8.182H0.909V1.818a0.909 0.909 0 0 1 0.909 -0.909h6.364v0.909H1.818Z"/>
</svg>

`,E0=`<svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" aria-hidden="true" aria-label="Copied" fill="none" stroke="currentColor" stroke-width="1.3">
  <path d="m13.813 4.781 -7.438 7.438 -3.188 -3.188"/>
</svg>
`,Do=`<button title="copy" class="copy_code_button">
  <span class="copy-text">${w0}</span>
  <span class="check">${E0}</span>
</button>`,du=/[&<>"']/,v0=new RegExp(du.source,"g"),pu=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,y0=new RegExp(pu.source,"g"),D0={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ko=t=>D0[t]||"";function Fr(t,e){if(e){if(du.test(t))return t.replace(v0,ko)}else if(pu.test(t))return t.replace(y0,ko);return t}function k0(t){const e=t.map(n=>({start:new RegExp(n.left.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")),end:new RegExp(n.right.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"))}));return{name:"latex",level:"block",start(n){for(const i of e){const r=n.match(i.start);if(r)return r.index}return-1},tokenizer(n,i){for(const r of e){const s=new RegExp(`${r.start.source}([\\s\\S]+?)${r.end.source}`).exec(n);if(s)return{type:"latex",raw:s[0],text:s[1].trim()}}},renderer(n){return`<div class="latex-block">${n.text}</div>`}}}function A0(){return{name:"mermaid",level:"block",start(t){var e;return(e=t.match(/^```mermaid\s*\n/))==null?void 0:e.index},tokenizer(t){const e=/^```mermaid\s*\n([\s\S]*?)```\s*(?:\n|$)/.exec(t);if(e)return{type:"mermaid",raw:e[0],text:e[1].trim()}},renderer(t){return`<div class="mermaid">${t.text}</div>
`}}}const F0={code(t,e,n){var r;const i=((r=(e??"").match(/\S*/))==null?void 0:r[0])??"";return t=t.replace(/\n$/,"")+`
`,!i||i==="mermaid"?'<div class="code_wrap">'+Do+"<pre><code>"+(n?t:Fr(t,!0))+`</code></pre></div>
`:'<div class="code_wrap">'+Do+'<pre><code class="language-'+Fr(i)+'">'+(n?t:Fr(t,!0))+`</code></pre></div>
`}},T0=new bs;function C0({header_links:t,line_breaks:e,latex_delimiters:n}){const i=new ou;i.use({gfm:!0,pedantic:!1,breaks:e},u0({highlight:(o,a)=>{var l;return(l=Ni.languages)!=null&&l[a]?Ni.highlight(o,Ni.languages[a],a):o}}),{renderer:F0}),t&&(i.use(_0()),i.use({extensions:[{name:"heading",level:"block",renderer(o){const a=o.raw.toLowerCase().trim().replace(/<[!\/a-z].*?>/gi,""),l="h"+T0.slug(a),u=o.depth,c=this.parser.parseInline(o.tokens);return`<h${u} id="${l}"><a class="md-header-anchor" href="#${l}">${b0}</a>${c}</h${u}>
`}}]}));const r=A0(),s=k0(n);return i.use({extensions:[r,s]}),i}const Jr=t=>JSON.parse(JSON.stringify(t)),S0=t=>t.nodeType===1,L0=t=>Q0.has(t.tagName),R0=t=>"action"in t,O0=t=>t.tagName==="IFRAME",I0=t=>"formAction"in t,P0=t=>"protocol"in t,Ci=(()=>{const t=/^(?:\w+script|data):/i;return e=>t.test(e)})(),B0=(()=>{const t=/(?:script|data):/i;return e=>t.test(e)})(),N0=t=>{const e={};for(let n=0,i=t.length;n<i;n++){const r=t[n];for(const s in r)e[s]?e[s]=e[s].concat(r[s]):e[s]=r[s]}return e},mu=(t,e)=>{let n=t.firstChild;for(;n;){const i=n.nextSibling;S0(n)&&(e(n,t),n.parentNode&&mu(n,e)),n=i}},M0=(t,e)=>{const n=document.createNodeIterator(t,NodeFilter.SHOW_ELEMENT);let i;for(;i=n.nextNode();){const r=i.parentNode;r&&e(i,r)}},x0=(t,e)=>!!globalThis.document&&!!globalThis.document.createNodeIterator?M0(t,e):mu(t,e),_u=["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","bgsound","big","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","datalist","dd","del","details","dfn","dialog","dir","div","dl","dt","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","keygen","label","layer","legend","li","link","listing","main","map","mark","marquee","menu","meta","meter","nav","nobr","ol","optgroup","option","output","p","picture","popup","pre","progress","q","rb","rp","rt","rtc","ruby","s","samp","section","select","selectmenu","small","source","span","strike","strong","style","sub","summary","sup","table","tbody","td","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"],H0=["basefont","command","data","iframe","image","plaintext","portal","slot","textarea","title","xmp"],z0=new Set([..._u,...H0]),gu=["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"],U0=["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"],j0=new Set([...gu,...U0]),bu=["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"],G0=["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"],V0=new Set([...bu,...G0]),q0=["abbr","accept","accept-charset","accesskey","action","align","alink","allow","allowfullscreen","alt","anchor","archive","as","async","autocapitalize","autocomplete","autocorrect","autofocus","autopictureinpicture","autoplay","axis","background","behavior","bgcolor","border","bordercolor","capture","cellpadding","cellspacing","challenge","char","charoff","charset","checked","cite","class","classid","clear","code","codebase","codetype","color","cols","colspan","compact","content","contenteditable","controls","controlslist","conversiondestination","coords","crossorigin","csp","data","datetime","declare","decoding","default","defer","dir","direction","dirname","disabled","disablepictureinpicture","disableremoteplayback","disallowdocumentaccess","download","draggable","elementtiming","enctype","end","enterkeyhint","event","exportparts","face","for","form","formaction","formenctype","formmethod","formnovalidate","formtarget","frame","frameborder","headers","height","hidden","high","href","hreflang","hreftranslate","hspace","http-equiv","id","imagesizes","imagesrcset","importance","impressiondata","impressionexpiry","incremental","inert","inputmode","integrity","invisible","ismap","keytype","kind","label","lang","language","latencyhint","leftmargin","link","list","loading","longdesc","loop","low","lowsrc","manifest","marginheight","marginwidth","max","maxlength","mayscript","media","method","min","minlength","multiple","muted","name","nohref","nomodule","nonce","noresize","noshade","novalidate","nowrap","object","open","optimum","part","pattern","ping","placeholder","playsinline","policy","poster","preload","pseudo","readonly","referrerpolicy","rel","reportingorigin","required","resources","rev","reversed","role","rows","rowspan","rules","sandbox","scheme","scope","scopes","scrollamount","scrolldelay","scrolling","select","selected","shadowroot","shadowrootdelegatesfocus","shape","size","sizes","slot","span","spellcheck","src","srclang","srcset","standby","start","step","style","summary","tabindex","target","text","title","topmargin","translate","truespeed","trusttoken","type","usemap","valign","value","valuetype","version","virtualkeyboardpolicy","vlink","vspace","webkitdirectory","width","wrap"],W0=["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dominant-baseline","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"],Z0=["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"],Ht={HTML:"http://www.w3.org/1999/xhtml",SVG:"http://www.w3.org/2000/svg",MATH:"http://www.w3.org/1998/Math/MathML"},X0={[Ht.HTML]:z0,[Ht.SVG]:j0,[Ht.MATH]:V0},Y0={[Ht.HTML]:"html",[Ht.SVG]:"svg",[Ht.MATH]:"math"},J0={[Ht.HTML]:"",[Ht.SVG]:"svg:",[Ht.MATH]:"math:"},Q0=new Set(["A","AREA","BUTTON","FORM","IFRAME","INPUT"]),wu={allowComments:!0,allowCustomElements:!1,allowUnknownMarkup:!1,allowElements:[..._u,...gu.map(t=>`svg:${t}`),...bu.map(t=>`math:${t}`)],allowAttributes:N0([Object.fromEntries(q0.map(t=>[t,["*"]])),Object.fromEntries(W0.map(t=>[t,["svg:*"]])),Object.fromEntries(Z0.map(t=>[t,["math:*"]]))])};var Tr=function(t,e,n,i,r){if(i==="m")throw new TypeError("Private method is not writable");if(i==="a"&&!r)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?t!==e||!r:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return i==="a"?r.call(t,n):r?r.value=n:e.set(t,n),n},mn=function(t,e,n,i){if(n==="a"&&!i)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?t!==e||!i:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?i:n==="a"?i.call(t):i?i.value:e.get(t)},rn,Mi,xi;class Eu{constructor(e={}){rn.set(this,void 0),Mi.set(this,void 0),xi.set(this,void 0),this.getConfiguration=()=>Jr(mn(this,rn,"f")),this.sanitize=c=>{const h=mn(this,Mi,"f"),f=mn(this,xi,"f");return x0(c,(d,m)=>{const p=d.namespaceURI||Ht.HTML,b=m.namespaceURI||Ht.HTML,D=X0[p],y=Y0[p],g=J0[p],A=d.tagName.toLowerCase(),w=`${g}${A}`,S=`${g}*`;if(!D.has(A)||!h.has(w)||p!==b&&A!==y)m.removeChild(d);else{const z=d.getAttributeNames(),C=z.length;if(C){for(let N=0;N<C;N++){const re=z[N],x=f[re];(!x||!x.has(S)&&!x.has(w))&&d.removeAttribute(re)}if(L0(d))if(P0(d)){const N=d.getAttribute("href");N&&B0(N)&&Ci(d.protocol)&&d.removeAttribute("href")}else R0(d)?Ci(d.action)&&d.removeAttribute("action"):I0(d)?Ci(d.formAction)&&d.removeAttribute("formaction"):O0(d)&&(Ci(d.src)&&d.removeAttribute("formaction"),d.setAttribute("sandbox","allow-scripts"))}}}),c},this.sanitizeFor=(c,h)=>{throw new Error('"sanitizeFor" is not implemented yet')};const{allowComments:n,allowCustomElements:i,allowUnknownMarkup:r,blockElements:s,dropElements:o,dropAttributes:a}=e;if(n===!1)throw new Error('A false "allowComments" is not supported yet');if(i)throw new Error('A true "allowCustomElements" is not supported yet');if(r)throw new Error('A true "allowUnknownMarkup" is not supported yet');if(s)throw new Error('"blockElements" is not supported yet, use "allowElements" instead');if(o)throw new Error('"dropElements" is not supported yet, use "allowElements" instead');if(a)throw new Error('"dropAttributes" is not supported yet, use "allowAttributes" instead');Tr(this,rn,Jr(wu),"f");const{allowElements:l,allowAttributes:u}=e;l&&(mn(this,rn,"f").allowElements=e.allowElements),u&&(mn(this,rn,"f").allowAttributes=e.allowAttributes),Tr(this,Mi,new Set(mn(this,rn,"f").allowElements),"f"),Tr(this,xi,Object.fromEntries(Object.entries(mn(this,rn,"f").allowAttributes||{}).map(([c,h])=>[c,new Set(h)])),"f")}}rn=new WeakMap,Mi=new WeakMap,xi=new WeakMap;Eu.getDefaultConfiguration=()=>Jr(wu);const K0=(t,e=location.href)=>{try{return!!t&&new URL(t).origin!==new URL(e).origin}catch{return!1}};function Ao(t){const e=new Eu,n=new DOMParser().parseFromString(t,"text/html");return vu(n.body,"A",i=>{i instanceof HTMLElement&&"target"in i&&K0(i.getAttribute("href"),location.href)&&(i.setAttribute("target","_blank"),i.setAttribute("rel","noopener noreferrer"))}),e.sanitize(n).body.innerHTML}function vu(t,e,n){t&&(t.nodeName===e||typeof e=="function")&&n(t);const i=(t==null?void 0:t.childNodes)||[];for(let r=0;r<i.length;r++)vu(i[r],e,n)}const Fo=["!--","!doctype","a","abbr","acronym","address","applet","area","article","aside","audio","b","base","basefont","bdi","bdo","big","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","dir","div","dl","dt","em","embed","fieldset","figcaption","figure","font","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","label","legend","li","link","main","map","mark","menu","meta","meter","nav","noframes","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","search","section","select","small","source","span","strike","strong","style","sub","summary","sup","svg","table","tbody","td","template","textarea","tfoot","th","thead","time","title","tr","track","tt","u","ul","var","video","wbr"],$0=["g","defs","use","symbol","rect","circle","ellipse","line","polyline","polygon","path","image","text","tspan","textPath","linearGradient","radialGradient","stop","pattern","clipPath","mask","filter","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feGaussianBlur","feMerge","feMorphology","feOffset","feSpecularLighting","feTurbulence","feMergeNode","feFuncR","feFuncG","feFuncB","feFuncA","feDistantLight","fePointLight","feSpotLight","feFlood","feTile","animate","animateTransform","animateMotion","mpath","set","view","cursor","foreignObject","desc","title","metadata","switch"],ep=[...Fo,...$0.filter(t=>!Fo.includes(t))];function tp(t){let e,n;return{c(){e=Z("span"),n=new Ki(!1),this.h()},l(i){e=X(i,"SPAN",{class:!0});var r=U(e);n=$i(r,!1),r.forEach(v),this.h()},h(){n.a=null,_(e,"class","md svelte-7ddecg"),G(e,"chatbot",t[0]),G(e,"prose",t[1])},m(i,r){R(i,e,r),n.m(t[3],e),t[11](e)},p(i,[r]){r&8&&n.p(i[3]),r&1&&G(e,"chatbot",i[0]),r&2&&G(e,"prose",i[1])},i:pe,o:pe,d(i){i&&v(e),t[11](null)}}}function To(t){return t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function np(t,e,n){let{chatbot:i=!0}=e,{message:r}=e,{sanitize_html:s=!0}=e,{latex_delimiters:o=[]}=e,{render_markdown:a=!0}=e,{line_breaks:l=!0}=e,{header_links:u=!1}=e,{allow_tags:c=!1}=e,{theme_mode:h="system"}=e,f,d,m=!1;const p=C0({header_links:u,line_breaks:l,latex_delimiters:o||[]});function b(w){return!o||o.length===0?!1:o.some(E=>w.includes(E.left)&&w.includes(E.right))}function D(w,E){if(E===!0){const S=/<\/?([a-zA-Z][a-zA-Z0-9-]*)([\s>])/g;return w.replace(S,(z,C,N)=>ep.includes(C.toLowerCase())?z:z.replace(/</g,"&lt;").replace(/>/g,"&gt;"))}if(Array.isArray(E)){const S=E.map(C=>({open:new RegExp(`<(${C})(\\s+[^>]*)?>`,"gi"),close:new RegExp(`</(${C})>`,"gi")}));let z=w;return S.forEach(C=>{z=z.replace(C.open,N=>N.replace(/</g,"&lt;").replace(/>/g,"&gt;")),z=z.replace(C.close,N=>N.replace(/</g,"&lt;").replace(/>/g,"&gt;"))}),z}return w}function y(w){let E=w;if(a){const S=[];o.forEach((z,C)=>{const N=To(z.left),re=To(z.right),x=new RegExp(`${N}([\\s\\S]+?)${re}`,"g");E=E.replace(x,(le,me)=>(S.push(le),`%%%LATEX_BLOCK_${S.length-1}%%%`))}),E=p.parse(E),E=E.replace(/%%%LATEX_BLOCK_(\d+)%%%/g,(z,C)=>S[parseInt(C,10)])}return c&&(E=D(E,c)),s&&Ao&&(E=Ao(E)),E}async function g(w){if(o.length>0&&w&&b(w))if(!m)await Promise.all([P(()=>Promise.resolve({}),__vite__mapDeps([176]),import.meta.url),P(()=>import("./auto-render.530FrPZJ.js"),__vite__mapDeps([177,178]),import.meta.url)]).then(([,{default:E}])=>{m=!0,E(f,{delimiters:o,throwOnError:!1})});else{const{default:E}=await P(()=>import("./auto-render.530FrPZJ.js"),__vite__mapDeps([177,178]),import.meta.url);E(f,{delimiters:o,throwOnError:!1})}if(f){const E=f.querySelectorAll(".mermaid");if(E.length>0){await on();const{default:S}=await P(()=>import("./mermaid.core.CKP5SxPy.js").then(z=>z.b3),__vite__mapDeps([179,25,115,180,114]),import.meta.url);S.initialize({startOnLoad:!1,theme:h==="dark"?"dark":"default",securityLevel:"antiscript"}),await S.run({nodes:Array.from(E).map(z=>z)})}}}Ba(async()=>{f&&document.body.contains(f)?await g(r):console.error("Element is not in the DOM")});function A(w){Je[w?"unshift":"push"](()=>{f=w,n(2,f)})}return t.$$set=w=>{"chatbot"in w&&n(0,i=w.chatbot),"message"in w&&n(4,r=w.message),"sanitize_html"in w&&n(5,s=w.sanitize_html),"latex_delimiters"in w&&n(6,o=w.latex_delimiters),"render_markdown"in w&&n(1,a=w.render_markdown),"line_breaks"in w&&n(7,l=w.line_breaks),"header_links"in w&&n(8,u=w.header_links),"allow_tags"in w&&n(9,c=w.allow_tags),"theme_mode"in w&&n(10,h=w.theme_mode)},t.$$.update=()=>{t.$$.dirty&16&&(r&&r.trim()?n(3,d=y(r)):n(3,d=""))},[i,a,f,d,r,s,o,l,u,c,h,A]}class ip extends qe{constructor(e){super(),We(this,e,np,tp,Xe,{chatbot:0,message:4,sanitize_html:5,latex_delimiters:6,render_markdown:1,line_breaks:7,header_links:8,allow_tags:9,theme_mode:10})}}function rp(t){let e,n,i;return n=new ip({props:{message:t[0],sanitize_html:!0}}),{c(){e=Z("div"),ge(n.$$.fragment),this.h()},l(r){e=X(r,"DIV",{class:!0});var s=U(e);Re(n.$$.fragment,s),s.forEach(v),this.h()},h(){_(e,"class","svelte-j9uq24")},m(r,s){R(r,e,s),be(n,e,null),i=!0},p(r,[s]){const o={};s&1&&(o.message=r[0]),n.$set(o)},i(r){i||(B(n.$$.fragment,r),i=!0)},o(r){j(n.$$.fragment,r),i=!1},d(r){r&&v(e),we(n)}}}function sp(t,e,n){let{info:i}=e;return t.$$set=r=>{"info"in r&&n(0,i=r.info)},[i]}let op=class extends qe{constructor(e){super(),We(this,e,sp,rp,Xe,{info:0})}};function Co(t){let e,n;return e=new op({props:{info:t[1]}}),{c(){ge(e.$$.fragment)},l(i){Re(e.$$.fragment,i)},m(i,r){be(e,i,r),n=!0},p(i,r){const s={};r&2&&(s.info=i[1]),e.$set(s)},i(i){n||(B(e.$$.fragment,i),n=!0)},o(i){j(e.$$.fragment,i),n=!1},d(i){we(e,i)}}}function ap(t){let e,n,i,r,s;const o=t[4].default,a=Ut(o,t,t[3],null);let l=t[1]&&Co(t);return{c(){e=Z("span"),a&&a.c(),i=oe(),l&&l.c(),r=fe(),this.h()},l(u){e=X(u,"SPAN",{"data-testid":!0,dir:!0,class:!0});var c=U(e);a&&a.l(c),c.forEach(v),i=ae(u),l&&l.l(u),r=fe(),this.h()},h(){_(e,"data-testid","block-info"),_(e,"dir",n=t[2]?"rtl":"ltr"),_(e,"class","svelte-g2oxp3"),G(e,"sr-only",!t[0]),G(e,"hide",!t[0]),G(e,"has-info",t[1]!=null)},m(u,c){R(u,e,c),a&&a.m(e,null),R(u,i,c),l&&l.m(u,c),R(u,r,c),s=!0},p(u,[c]){a&&a.p&&(!s||c&8)&&jt(a,o,u,u[3],s?Vt(o,u[3],c,null):Gt(u[3]),null),(!s||c&4&&n!==(n=u[2]?"rtl":"ltr"))&&_(e,"dir",n),(!s||c&1)&&G(e,"sr-only",!u[0]),(!s||c&1)&&G(e,"hide",!u[0]),(!s||c&2)&&G(e,"has-info",u[1]!=null),u[1]?l?(l.p(u,c),c&2&&B(l,1)):(l=Co(u),l.c(),B(l,1),l.m(r.parentNode,r)):l&&(Be(),j(l,1,1,()=>{l=null}),Ne())},i(u){s||(B(a,u),B(l),s=!0)},o(u){j(a,u),j(l),s=!1},d(u){u&&(v(e),v(i),v(r)),a&&a.d(u),l&&l.d(u)}}}function lp(t,e,n){let{$$slots:i={},$$scope:r}=e,{show_label:s=!0}=e,{info:o=void 0}=e,{rtl:a=!1}=e;return t.$$set=l=>{"show_label"in l&&n(0,s=l.show_label),"info"in l&&n(1,o=l.info),"rtl"in l&&n(2,a=l.rtl),"$$scope"in l&&n(3,r=l.$$scope)},[s,o,a,r,i]}class up extends qe{constructor(e){super(),We(this,e,lp,ap,Xe,{show_label:0,info:1,rtl:2})}}function So(t){let e,n;return{c(){e=Z("span"),n=Ee(t[1]),this.h()},l(i){e=X(i,"SPAN",{class:!0});var r=U(e);n=ve(r,t[1]),r.forEach(v),this.h()},h(){_(e,"class","svelte-vzs2gq")},m(i,r){R(i,e,r),M(e,n)},p(i,r){r&2&&je(n,i[1])},d(i){i&&v(e)}}}function cp(t){let e,n,i,r,s,o,a,l,u=t[2]&&So(t);var c=t[0];function h(m,p){return{}}c&&(r=Dt(c,h()));const f=t[14].default,d=Ut(f,t,t[13],null);return{c(){e=Z("button"),u&&u.c(),n=oe(),i=Z("div"),r&&ge(r.$$.fragment),s=oe(),d&&d.c(),this.h()},l(m){e=X(m,"BUTTON",{"aria-label":!0,"aria-haspopup":!0,title:!0,class:!0});var p=U(e);u&&u.l(p),n=ae(p),i=X(p,"DIV",{class:!0});var b=U(i);r&&Re(r.$$.fragment,b),s=ae(b),d&&d.l(b),b.forEach(v),p.forEach(v),this.h()},h(){_(i,"class","svelte-vzs2gq"),G(i,"x-small",t[4]==="x-small"),G(i,"small",t[4]==="small"),G(i,"large",t[4]==="large"),G(i,"medium",t[4]==="medium"),e.disabled=t[7],_(e,"aria-label",t[1]),_(e,"aria-haspopup",t[8]),_(e,"title",t[1]),_(e,"class","svelte-vzs2gq"),G(e,"pending",t[3]),G(e,"padded",t[5]),G(e,"highlight",t[6]),G(e,"transparent",t[9]),Y(e,"color",!t[7]&&t[11]?t[11]:"var(--block-label-text-color)"),Y(e,"--bg-color",t[7]?"auto":t[10])},m(m,p){R(m,e,p),u&&u.m(e,null),M(e,n),M(e,i),r&&be(r,i,null),M(i,s),d&&d.m(i,null),o=!0,a||(l=Te(e,"click",t[15]),a=!0)},p(m,[p]){if(m[2]?u?u.p(m,p):(u=So(m),u.c(),u.m(e,n)):u&&(u.d(1),u=null),p&1&&c!==(c=m[0])){if(r){Be();const b=r;j(b.$$.fragment,1,0,()=>{we(b,1)}),Ne()}c?(r=Dt(c,h()),ge(r.$$.fragment),B(r.$$.fragment,1),be(r,i,s)):r=null}d&&d.p&&(!o||p&8192)&&jt(d,f,m,m[13],o?Vt(f,m[13],p,null):Gt(m[13]),null),(!o||p&16)&&G(i,"x-small",m[4]==="x-small"),(!o||p&16)&&G(i,"small",m[4]==="small"),(!o||p&16)&&G(i,"large",m[4]==="large"),(!o||p&16)&&G(i,"medium",m[4]==="medium"),(!o||p&128)&&(e.disabled=m[7]),(!o||p&2)&&_(e,"aria-label",m[1]),(!o||p&256)&&_(e,"aria-haspopup",m[8]),(!o||p&2)&&_(e,"title",m[1]),(!o||p&8)&&G(e,"pending",m[3]),(!o||p&32)&&G(e,"padded",m[5]),(!o||p&64)&&G(e,"highlight",m[6]),(!o||p&512)&&G(e,"transparent",m[9]),p&2176&&Y(e,"color",!m[7]&&m[11]?m[11]:"var(--block-label-text-color)"),p&1152&&Y(e,"--bg-color",m[7]?"auto":m[10])},i(m){o||(r&&B(r.$$.fragment,m),B(d,m),o=!0)},o(m){r&&j(r.$$.fragment,m),j(d,m),o=!1},d(m){m&&v(e),u&&u.d(),r&&we(r),d&&d.d(m),a=!1,l()}}}function fp(t,e,n){let i,{$$slots:r={},$$scope:s}=e,{Icon:o}=e,{label:a=""}=e,{show_label:l=!1}=e,{pending:u=!1}=e,{size:c="small"}=e,{padded:h=!0}=e,{highlight:f=!1}=e,{disabled:d=!1}=e,{hasPopup:m=!1}=e,{color:p="var(--block-label-text-color)"}=e,{transparent:b=!1}=e,{background:D="var(--block-background-fill)"}=e;function y(g){_t.call(this,t,g)}return t.$$set=g=>{"Icon"in g&&n(0,o=g.Icon),"label"in g&&n(1,a=g.label),"show_label"in g&&n(2,l=g.show_label),"pending"in g&&n(3,u=g.pending),"size"in g&&n(4,c=g.size),"padded"in g&&n(5,h=g.padded),"highlight"in g&&n(6,f=g.highlight),"disabled"in g&&n(7,d=g.disabled),"hasPopup"in g&&n(8,m=g.hasPopup),"color"in g&&n(12,p=g.color),"transparent"in g&&n(9,b=g.transparent),"background"in g&&n(10,D=g.background),"$$scope"in g&&n(13,s=g.$$scope)},t.$$.update=()=>{t.$$.dirty&4160&&n(11,i=f?"var(--color-accent)":p)},[o,a,l,u,c,h,f,d,m,b,D,i,p,s,r,y]}class hp extends qe{constructor(e){super(),We(this,e,fp,cp,Xe,{Icon:0,label:1,show_label:2,pending:3,size:4,padded:5,highlight:6,disabled:7,hasPopup:8,color:12,transparent:9,background:10})}}function dp(t){let e,n;return{c(){e=Se("svg"),n=Se("path"),this.h()},l(i){e=Le(i,"svg",{width:!0,height:!0,"stroke-width":!0,viewBox:!0,fill:!0,xmlns:!0,color:!0});var r=U(e);n=Le(r,"path",{d:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0}),U(n).forEach(v),r.forEach(v),this.h()},h(){_(n,"d","M5 13L9 17L19 7"),_(n,"stroke","currentColor"),_(n,"stroke-width","1.5"),_(n,"stroke-linecap","round"),_(n,"stroke-linejoin","round"),_(e,"width","100%"),_(e,"height","100%"),_(e,"stroke-width","1.5"),_(e,"viewBox","0 0 24 24"),_(e,"fill","none"),_(e,"xmlns","http://www.w3.org/2000/svg"),_(e,"color","currentColor")},m(i,r){R(i,e,r),M(e,n)},p:pe,i:pe,o:pe,d(i){i&&v(e)}}}class pp extends qe{constructor(e){super(),We(this,e,null,dp,Xe,{})}}function mp(t){let e,n,i,r;return{c(){e=Se("svg"),n=Se("g"),i=Se("path"),r=Se("path"),this.h()},l(s){e=Le(s,"svg",{width:!0,height:!0,viewBox:!0,version:!0,xmlns:!0,"xmlns:xlink":!0,"xml:space":!0,stroke:!0,style:!0});var o=U(e);n=Le(o,"g",{transform:!0});var a=U(n);i=Le(a,"path",{d:!0,style:!0}),U(i).forEach(v),a.forEach(v),r=Le(o,"path",{d:!0,style:!0}),U(r).forEach(v),o.forEach(v),this.h()},h(){_(i,"d","M18,6L6.087,17.913"),Y(i,"fill","none"),Y(i,"fill-rule","nonzero"),Y(i,"stroke-width","2px"),_(n,"transform","matrix(1.14096,-0.140958,-0.140958,1.14096,-0.0559523,0.0559523)"),_(r,"d","M4.364,4.364L19.636,19.636"),Y(r,"fill","none"),Y(r,"fill-rule","nonzero"),Y(r,"stroke-width","2px"),_(e,"width","100%"),_(e,"height","100%"),_(e,"viewBox","0 0 24 24"),_(e,"version","1.1"),_(e,"xmlns","http://www.w3.org/2000/svg"),_(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),_(e,"xml:space","preserve"),_(e,"stroke","currentColor"),Y(e,"fill-rule","evenodd"),Y(e,"clip-rule","evenodd"),Y(e,"stroke-linecap","round"),Y(e,"stroke-linejoin","round")},m(s,o){R(s,e,o),M(e,n),M(n,i),M(e,r)},p:pe,i:pe,o:pe,d(s){s&&v(e)}}}class _p extends qe{constructor(e){super(),We(this,e,null,mp,Xe,{})}}function gp(t){let e,n,i;return{c(){e=Se("svg"),n=Se("path"),i=Se("path"),this.h()},l(r){e=Le(r,"svg",{xmlns:!0,viewBox:!0,color:!0,"aria-hidden":!0,width:!0,height:!0});var s=U(e);n=Le(s,"path",{fill:!0,d:!0}),U(n).forEach(v),i=Le(s,"path",{fill:!0,d:!0}),U(i).forEach(v),s.forEach(v),this.h()},h(){_(n,"fill","currentColor"),_(n,"d","M28 10v18H10V10h18m0-2H10a2 2 0 0 0-2 2v18a2 2 0 0 0 2 2h18a2 2 0 0 0 2-2V10a2 2 0 0 0-2-2Z"),_(i,"fill","currentColor"),_(i,"d","M4 18H2V4a2 2 0 0 1 2-2h14v2H4Z"),_(e,"xmlns","http://www.w3.org/2000/svg"),_(e,"viewBox","0 0 33 33"),_(e,"color","currentColor"),_(e,"aria-hidden","true"),_(e,"width","100%"),_(e,"height","100%")},m(r,s){R(r,e,s),M(e,n),M(e,i)},p:pe,i:pe,o:pe,d(r){r&&v(e)}}}class bp extends qe{constructor(e){super(),We(this,e,null,gp,Xe,{})}}function wp(t){let e,n;return{c(){e=Se("svg"),n=Se("path"),this.h()},l(i){e=Le(i,"svg",{fill:!0,stroke:!0,viewBox:!0,width:!0,height:!0,xmlns:!0,"aria-hidden":!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0});var r=U(e);n=Le(r,"path",{"stroke-linecap":!0,"stroke-linejoin":!0,d:!0}),U(n).forEach(v),r.forEach(v),this.h()},h(){_(n,"stroke-linecap","round"),_(n,"stroke-linejoin","round"),_(n,"d","M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"),_(e,"fill","none"),_(e,"stroke","currentColor"),_(e,"viewBox","0 0 24 24"),_(e,"width","100%"),_(e,"height","100%"),_(e,"xmlns","http://www.w3.org/2000/svg"),_(e,"aria-hidden","true"),_(e,"stroke-width","2"),_(e,"stroke-linecap","round"),_(e,"stroke-linejoin","round")},m(i,r){R(i,e,r),M(e,n)},p:pe,i:pe,o:pe,d(i){i&&v(e)}}}let Ep=class extends qe{constructor(e){super(),We(this,e,null,wp,Xe,{})}};function vp(t){let e,n;return{c(){e=Se("svg"),n=Se("path"),this.h()},l(i){e=Le(i,"svg",{fill:!0,stroke:!0,viewBox:!0,width:!0,height:!0,xmlns:!0,"aria-hidden":!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0});var r=U(e);n=Le(r,"path",{"stroke-linecap":!0,"stroke-linejoin":!0,d:!0}),U(n).forEach(v),r.forEach(v),this.h()},h(){_(n,"stroke-linecap","round"),_(n,"stroke-linejoin","round"),_(n,"d","M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"),_(e,"fill","none"),_(e,"stroke","currentColor"),_(e,"viewBox","0 0 24 24"),_(e,"width","100%"),_(e,"height","100%"),_(e,"xmlns","http://www.w3.org/2000/svg"),_(e,"aria-hidden","true"),_(e,"stroke-width","2"),_(e,"stroke-linecap","round"),_(e,"stroke-linejoin","round")},m(i,r){R(i,e,r),M(e,n)},p:pe,i:pe,o:pe,d(i){i&&v(e)}}}class yp extends qe{constructor(e){super(),We(this,e,null,vp,Xe,{})}}function Dp(t){let e,n;return{c(){e=Se("svg"),n=Se("path"),this.h()},l(i){e=Le(i,"svg",{fill:!0,stroke:!0,viewBox:!0,width:!0,height:!0,xmlns:!0,"aria-hidden":!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0});var r=U(e);n=Le(r,"path",{"stroke-linecap":!0,"stroke-linejoin":!0,d:!0}),U(n).forEach(v),r.forEach(v),this.h()},h(){_(n,"stroke-linecap","round"),_(n,"stroke-linejoin","round"),_(n,"d","M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"),_(e,"fill","none"),_(e,"stroke","currentColor"),_(e,"viewBox","0 0 24 24"),_(e,"width","100%"),_(e,"height","100%"),_(e,"xmlns","http://www.w3.org/2000/svg"),_(e,"aria-hidden","true"),_(e,"stroke-width","2"),_(e,"stroke-linecap","round"),_(e,"stroke-linejoin","round")},m(i,r){R(i,e,r),M(e,n)},p:pe,i:pe,o:pe,d(i){i&&v(e)}}}class kp extends qe{constructor(e){super(),We(this,e,null,Dp,Xe,{})}}function Ap(t){let e,n,i,r,s;return{c(){e=Se("svg"),n=Se("g"),i=Se("g"),r=Se("g"),s=Se("path"),this.h()},l(o){e=Le(o,"svg",{viewBox:!0,width:!0,height:!0,fill:!0,xmlns:!0});var a=U(e);n=Le(a,"g",{id:!0,"stroke-width":!0}),U(n).forEach(v),i=Le(a,"g",{id:!0,"stroke-linecap":!0,"stroke-linejoin":!0}),U(i).forEach(v),r=Le(a,"g",{id:!0});var l=U(r);s=Le(l,"path",{d:!0,fill:!0}),U(s).forEach(v),l.forEach(v),a.forEach(v),this.h()},h(){_(n,"id","SVGRepo_bgCarrier"),_(n,"stroke-width","0"),_(i,"id","SVGRepo_tracerCarrier"),_(i,"stroke-linecap","round"),_(i,"stroke-linejoin","round"),_(s,"d","M19.1168 12.1484C19.474 12.3581 19.9336 12.2384 20.1432 11.8811C20.3528 11.5238 20.2331 11.0643 19.8758 10.8547L19.1168 12.1484ZM6.94331 4.13656L6.55624 4.77902L6.56378 4.78344L6.94331 4.13656ZM5.92408 4.1598L5.50816 3.5357L5.50816 3.5357L5.92408 4.1598ZM5.51031 5.09156L4.76841 5.20151C4.77575 5.25101 4.78802 5.29965 4.80505 5.34671L5.51031 5.09156ZM7.12405 11.7567C7.26496 12.1462 7.69495 12.3477 8.08446 12.2068C8.47397 12.0659 8.67549 11.6359 8.53458 11.2464L7.12405 11.7567ZM19.8758 12.1484C20.2331 11.9388 20.3528 11.4793 20.1432 11.122C19.9336 10.7648 19.474 10.6451 19.1168 10.8547L19.8758 12.1484ZM6.94331 18.8666L6.56375 18.2196L6.55627 18.2241L6.94331 18.8666ZM5.92408 18.8433L5.50815 19.4674H5.50815L5.92408 18.8433ZM5.51031 17.9116L4.80505 17.6564C4.78802 17.7035 4.77575 17.7521 4.76841 17.8016L5.51031 17.9116ZM8.53458 11.7567C8.67549 11.3672 8.47397 10.9372 8.08446 10.7963C7.69495 10.6554 7.26496 10.8569 7.12405 11.2464L8.53458 11.7567ZM19.4963 12.2516C19.9105 12.2516 20.2463 11.9158 20.2463 11.5016C20.2463 11.0873 19.9105 10.7516 19.4963 10.7516V12.2516ZM7.82931 10.7516C7.4151 10.7516 7.07931 11.0873 7.07931 11.5016C7.07931 11.9158 7.4151 12.2516 7.82931 12.2516V10.7516ZM19.8758 10.8547L7.32284 3.48968L6.56378 4.78344L19.1168 12.1484L19.8758 10.8547ZM7.33035 3.49414C6.76609 3.15419 6.05633 3.17038 5.50816 3.5357L6.34 4.78391C6.40506 4.74055 6.4893 4.73863 6.55627 4.77898L7.33035 3.49414ZM5.50816 3.5357C4.95998 3.90102 4.67184 4.54987 4.76841 5.20151L6.25221 4.98161C6.24075 4.90427 6.27494 4.82727 6.34 4.78391L5.50816 3.5357ZM4.80505 5.34671L7.12405 11.7567L8.53458 11.2464L6.21558 4.83641L4.80505 5.34671ZM19.1168 10.8547L6.56378 18.2197L7.32284 19.5134L19.8758 12.1484L19.1168 10.8547ZM6.55627 18.2241C6.4893 18.2645 6.40506 18.2626 6.34 18.2192L5.50815 19.4674C6.05633 19.8327 6.76609 19.8489 7.33035 19.509L6.55627 18.2241ZM6.34 18.2192C6.27494 18.1759 6.24075 18.0988 6.25221 18.0215L4.76841 17.8016C4.67184 18.4532 4.95998 19.1021 5.50815 19.4674L6.34 18.2192ZM6.21558 18.1667L8.53458 11.7567L7.12405 11.2464L4.80505 17.6564L6.21558 18.1667ZM19.4963 10.7516H7.82931V12.2516H19.4963V10.7516Z"),_(s,"fill","currentColor"),_(r,"id","SVGRepo_iconCarrier"),_(e,"viewBox","0 0 22 24"),_(e,"width","100%"),_(e,"height","100%"),_(e,"fill","none"),_(e,"xmlns","http://www.w3.org/2000/svg")},m(o,a){R(o,e,a),M(e,n),M(e,i),M(e,r),M(r,s)},p:pe,i:pe,o:pe,d(o){o&&v(e)}}}class Fp extends qe{constructor(e){super(),We(this,e,null,Ap,Xe,{})}}function Tp(t){let e,n,i;return{c(){e=Se("svg"),n=Se("rect"),this.h()},l(r){e=Le(r,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0,class:!0});var s=U(e);n=Le(s,"rect",{x:!0,y:!0,width:!0,height:!0,rx:!0,ry:!0}),U(n).forEach(v),s.forEach(v),this.h()},h(){_(n,"x","3"),_(n,"y","3"),_(n,"width","18"),_(n,"height","18"),_(n,"rx","2"),_(n,"ry","2"),_(e,"xmlns","http://www.w3.org/2000/svg"),_(e,"width","100%"),_(e,"height","100%"),_(e,"viewBox","0 0 24 24"),_(e,"fill",t[0]),_(e,"stroke","currentColor"),_(e,"stroke-width",i=`${t[1]}`),_(e,"stroke-linecap","round"),_(e,"stroke-linejoin","round"),_(e,"class","feather feather-square")},m(r,s){R(r,e,s),M(e,n)},p(r,[s]){s&1&&_(e,"fill",r[0]),s&2&&i!==(i=`${r[1]}`)&&_(e,"stroke-width",i)},i:pe,o:pe,d(r){r&&v(e)}}}function Cp(t,e,n){let{fill:i="currentColor"}=e,{stroke_width:r=1.5}=e;return t.$$set=s=>{"fill"in s&&n(0,i=s.fill),"stroke_width"in s&&n(1,r=s.stroke_width)},[i,r]}class Sp extends qe{constructor(e){super(),We(this,e,Cp,Tp,Xe,{fill:0,stroke_width:1})}}function Lp(t){let e,n;return{c(){e=Se("svg"),n=Se("path"),this.h()},l(i){e=Le(i,"svg",{fill:!0,stroke:!0,"stroke-width":!0,viewBox:!0,width:!0,height:!0,xmlns:!0,"aria-hidden":!0,"stroke-linecap":!0,"stroke-linejoin":!0});var r=U(e);n=Le(r,"path",{"stroke-linecap":!0,"stroke-linejoin":!0,d:!0}),U(n).forEach(v),r.forEach(v),this.h()},h(){_(n,"stroke-linecap","round"),_(n,"stroke-linejoin","round"),_(n,"d","M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"),_(e,"fill","none"),_(e,"stroke","currentColor"),_(e,"stroke-width","2"),_(e,"viewBox","0 0 24 24"),_(e,"width","100%"),_(e,"height","100%"),_(e,"xmlns","http://www.w3.org/2000/svg"),_(e,"aria-hidden","true"),_(e,"stroke-linecap","round"),_(e,"stroke-linejoin","round")},m(i,r){R(i,e,r),M(e,n)},p:pe,i:pe,o:pe,d(i){i&&v(e)}}}class Rp extends qe{constructor(e){super(),We(this,e,null,Lp,Xe,{})}}const Op=t=>({}),Lo=t=>({}),Ip=t=>({}),Ro=t=>({});function Oo(t,e,n){const i=t.slice();return i[40]=e[n],i[42]=n,i}function Io(t,e,n){const i=t.slice();return i[40]=e[n],i}function Pp(t){let e,n,i,r,s=t[1]("common.error")+"",o,a,l;n=new hp({props:{Icon:_p,label:t[1]("common.clear"),disabled:!1}}),n.$on("click",t[32]);const u=t[30].error,c=Ut(u,t,t[29],Lo);return{c(){e=Z("div"),ge(n.$$.fragment),i=oe(),r=Z("span"),o=Ee(s),a=oe(),c&&c.c(),this.h()},l(h){e=X(h,"DIV",{class:!0});var f=U(e);Re(n.$$.fragment,f),f.forEach(v),i=ae(h),r=X(h,"SPAN",{class:!0});var d=U(r);o=ve(d,s),d.forEach(v),a=ae(h),c&&c.l(h),this.h()},h(){_(e,"class","clear-status svelte-ls20lj"),_(r,"class","error svelte-ls20lj")},m(h,f){R(h,e,f),be(n,e,null),R(h,i,f),R(h,r,f),M(r,o),R(h,a,f),c&&c.m(h,f),l=!0},p(h,f){const d={};f[0]&2&&(d.label=h[1]("common.clear")),n.$set(d),(!l||f[0]&2)&&s!==(s=h[1]("common.error")+"")&&je(o,s),c&&c.p&&(!l||f[0]&536870912)&&jt(c,u,h,h[29],l?Vt(u,h[29],f,Op):Gt(h[29]),Lo)},i(h){l||(B(n.$$.fragment,h),B(c,h),l=!0)},o(h){j(n.$$.fragment,h),j(c,h),l=!1},d(h){h&&(v(e),v(i),v(r),v(a)),we(n),c&&c.d(h)}}}function Bp(t){let e,n,i,r,s,o,a,l,u,c=t[8]==="default"&&t[18]&&t[6]==="full"&&Po(t);function h(g,A){if(g[7])return xp;if(g[2]!==null&&g[3]!==void 0&&g[2]>=0)return Mp;if(g[2]===0)return Np}let f=h(t),d=f&&f(t),m=t[5]&&Mo(t);const p=[jp,Up],b=[];function D(g,A){return g[15]!=null?0:g[6]==="full"?1:-1}~(s=D(t))&&(o=b[s]=p[s](t));let y=!t[5]&&Vo(t);return{c(){c&&c.c(),e=oe(),n=Z("div"),d&&d.c(),i=oe(),m&&m.c(),r=oe(),o&&o.c(),a=oe(),y&&y.c(),l=fe(),this.h()},l(g){c&&c.l(g),e=ae(g),n=X(g,"DIV",{class:!0});var A=U(n);d&&d.l(A),i=ae(A),m&&m.l(A),A.forEach(v),r=ae(g),o&&o.l(g),a=ae(g),y&&y.l(g),l=fe(),this.h()},h(){_(n,"class","progress-text svelte-ls20lj"),G(n,"meta-text-center",t[8]==="center"),G(n,"meta-text",t[8]==="default")},m(g,A){c&&c.m(g,A),R(g,e,A),R(g,n,A),d&&d.m(n,null),M(n,i),m&&m.m(n,null),R(g,r,A),~s&&b[s].m(g,A),R(g,a,A),y&&y.m(g,A),R(g,l,A),u=!0},p(g,A){g[8]==="default"&&g[18]&&g[6]==="full"?c?c.p(g,A):(c=Po(g),c.c(),c.m(e.parentNode,e)):c&&(c.d(1),c=null),f===(f=h(g))&&d?d.p(g,A):(d&&d.d(1),d=f&&f(g),d&&(d.c(),d.m(n,i))),g[5]?m?m.p(g,A):(m=Mo(g),m.c(),m.m(n,null)):m&&(m.d(1),m=null),(!u||A[0]&256)&&G(n,"meta-text-center",g[8]==="center"),(!u||A[0]&256)&&G(n,"meta-text",g[8]==="default");let w=s;s=D(g),s===w?~s&&b[s].p(g,A):(o&&(Be(),j(b[w],1,1,()=>{b[w]=null}),Ne()),~s?(o=b[s],o?o.p(g,A):(o=b[s]=p[s](g),o.c()),B(o,1),o.m(a.parentNode,a)):o=null),g[5]?y&&(Be(),j(y,1,1,()=>{y=null}),Ne()):y?(y.p(g,A),A[0]&32&&B(y,1)):(y=Vo(g),y.c(),B(y,1),y.m(l.parentNode,l))},i(g){u||(B(o),B(y),u=!0)},o(g){j(o),j(y),u=!1},d(g){g&&(v(e),v(n),v(r),v(a),v(l)),c&&c.d(g),d&&d.d(),m&&m.d(),~s&&b[s].d(g),y&&y.d(g)}}}function Po(t){let e,n=`translateX(${(t[17]||0)*100-100}%)`;return{c(){e=Z("div"),this.h()},l(i){e=X(i,"DIV",{class:!0}),U(e).forEach(v),this.h()},h(){_(e,"class","eta-bar svelte-ls20lj"),Y(e,"transform",n)},m(i,r){R(i,e,r)},p(i,r){r[0]&131072&&n!==(n=`translateX(${(i[17]||0)*100-100}%)`)&&Y(e,"transform",n)},d(i){i&&v(e)}}}function Np(t){let e;return{c(){e=Ee("processing |")},l(n){e=ve(n,"processing |")},m(n,i){R(n,e,i)},p:pe,d(n){n&&v(e)}}}function Mp(t){let e,n=t[2]+1+"",i,r,s,o;return{c(){e=Ee("queue: "),i=Ee(n),r=Ee("/"),s=Ee(t[3]),o=Ee(" |")},l(a){e=ve(a,"queue: "),i=ve(a,n),r=ve(a,"/"),s=ve(a,t[3]),o=ve(a," |")},m(a,l){R(a,e,l),R(a,i,l),R(a,r,l),R(a,s,l),R(a,o,l)},p(a,l){l[0]&4&&n!==(n=a[2]+1+"")&&je(i,n),l[0]&8&&je(s,a[3])},d(a){a&&(v(e),v(i),v(r),v(s),v(o))}}}function xp(t){let e,n=Ot(t[7]),i=[];for(let r=0;r<n.length;r+=1)i[r]=No(Io(t,n,r));return{c(){for(let r=0;r<i.length;r+=1)i[r].c();e=fe()},l(r){for(let s=0;s<i.length;s+=1)i[s].l(r);e=fe()},m(r,s){for(let o=0;o<i.length;o+=1)i[o]&&i[o].m(r,s);R(r,e,s)},p(r,s){if(s[0]&128){n=Ot(r[7]);let o;for(o=0;o<n.length;o+=1){const a=Io(r,n,o);i[o]?i[o].p(a,s):(i[o]=No(a),i[o].c(),i[o].m(e.parentNode,e))}for(;o<i.length;o+=1)i[o].d(1);i.length=n.length}},d(r){r&&v(e),Qi(i,r)}}}function Bo(t){let e,n=t[40].unit+"",i,r,s=" ",o;function a(c,h){return c[40].length!=null?zp:Hp}let l=a(t),u=l(t);return{c(){u.c(),e=oe(),i=Ee(n),r=Ee(" | "),o=Ee(s)},l(c){u.l(c),e=ae(c),i=ve(c,n),r=ve(c," | "),o=ve(c,s)},m(c,h){u.m(c,h),R(c,e,h),R(c,i,h),R(c,r,h),R(c,o,h)},p(c,h){l===(l=a(c))&&u?u.p(c,h):(u.d(1),u=l(c),u&&(u.c(),u.m(e.parentNode,e))),h[0]&128&&n!==(n=c[40].unit+"")&&je(i,n)},d(c){c&&(v(e),v(i),v(r),v(o)),u.d(c)}}}function Hp(t){let e=On(t[40].index||0)+"",n;return{c(){n=Ee(e)},l(i){n=ve(i,e)},m(i,r){R(i,n,r)},p(i,r){r[0]&128&&e!==(e=On(i[40].index||0)+"")&&je(n,e)},d(i){i&&v(n)}}}function zp(t){let e=On(t[40].index||0)+"",n,i,r=On(t[40].length)+"",s;return{c(){n=Ee(e),i=Ee("/"),s=Ee(r)},l(o){n=ve(o,e),i=ve(o,"/"),s=ve(o,r)},m(o,a){R(o,n,a),R(o,i,a),R(o,s,a)},p(o,a){a[0]&128&&e!==(e=On(o[40].index||0)+"")&&je(n,e),a[0]&128&&r!==(r=On(o[40].length)+"")&&je(s,r)},d(o){o&&(v(n),v(i),v(s))}}}function No(t){let e,n=t[40].index!=null&&Bo(t);return{c(){n&&n.c(),e=fe()},l(i){n&&n.l(i),e=fe()},m(i,r){n&&n.m(i,r),R(i,e,r)},p(i,r){i[40].index!=null?n?n.p(i,r):(n=Bo(i),n.c(),n.m(e.parentNode,e)):n&&(n.d(1),n=null)},d(i){i&&v(e),n&&n.d(i)}}}function Mo(t){let e,n=t[0]?`/${t[19]}`:"",i,r;return{c(){e=Ee(t[20]),i=Ee(n),r=Ee("s")},l(s){e=ve(s,t[20]),i=ve(s,n),r=ve(s,"s")},m(s,o){R(s,e,o),R(s,i,o),R(s,r,o)},p(s,o){o[0]&1048576&&je(e,s[20]),o[0]&524289&&n!==(n=s[0]?`/${s[19]}`:"")&&je(i,n)},d(s){s&&(v(e),v(i),v(r))}}}function Up(t){let e,n;return e=new Ad({props:{margin:t[8]==="default"}}),{c(){ge(e.$$.fragment)},l(i){Re(e.$$.fragment,i)},m(i,r){be(e,i,r),n=!0},p(i,r){const s={};r[0]&256&&(s.margin=i[8]==="default"),e.$set(s)},i(i){n||(B(e.$$.fragment,i),n=!0)},o(i){j(e.$$.fragment,i),n=!1},d(i){we(e,i)}}}function jp(t){let e,n,i,r,s,o=`${t[15]*100}%`,a=t[7]!=null&&xo(t);return{c(){e=Z("div"),n=Z("div"),a&&a.c(),i=oe(),r=Z("div"),s=Z("div"),this.h()},l(l){e=X(l,"DIV",{class:!0});var u=U(e);n=X(u,"DIV",{class:!0});var c=U(n);a&&a.l(c),c.forEach(v),i=ae(u),r=X(u,"DIV",{class:!0});var h=U(r);s=X(h,"DIV",{class:!0}),U(s).forEach(v),h.forEach(v),u.forEach(v),this.h()},h(){_(n,"class","progress-level-inner svelte-ls20lj"),_(s,"class","progress-bar svelte-ls20lj"),Y(s,"width",o),_(r,"class","progress-bar-wrap svelte-ls20lj"),_(e,"class","progress-level svelte-ls20lj")},m(l,u){R(l,e,u),M(e,n),a&&a.m(n,null),M(e,i),M(e,r),M(r,s),t[31](s)},p(l,u){l[7]!=null?a?a.p(l,u):(a=xo(l),a.c(),a.m(n,null)):a&&(a.d(1),a=null),u[0]&32768&&o!==(o=`${l[15]*100}%`)&&Y(s,"width",o)},i:pe,o:pe,d(l){l&&v(e),a&&a.d(),t[31](null)}}}function xo(t){let e,n=Ot(t[7]),i=[];for(let r=0;r<n.length;r+=1)i[r]=Go(Oo(t,n,r));return{c(){for(let r=0;r<i.length;r+=1)i[r].c();e=fe()},l(r){for(let s=0;s<i.length;s+=1)i[s].l(r);e=fe()},m(r,s){for(let o=0;o<i.length;o+=1)i[o]&&i[o].m(r,s);R(r,e,s)},p(r,s){if(s[0]&16512){n=Ot(r[7]);let o;for(o=0;o<n.length;o+=1){const a=Oo(r,n,o);i[o]?i[o].p(a,s):(i[o]=Go(a),i[o].c(),i[o].m(e.parentNode,e))}for(;o<i.length;o+=1)i[o].d(1);i.length=n.length}},d(r){r&&v(e),Qi(i,r)}}}function Ho(t){let e,n,i,r,s=t[42]!==0&&Gp(),o=t[40].desc!=null&&zo(t),a=t[40].desc!=null&&t[14]&&t[14][t[42]]!=null&&Uo(),l=t[14]!=null&&jo(t);return{c(){s&&s.c(),e=oe(),o&&o.c(),n=oe(),a&&a.c(),i=oe(),l&&l.c(),r=fe()},l(u){s&&s.l(u),e=ae(u),o&&o.l(u),n=ae(u),a&&a.l(u),i=ae(u),l&&l.l(u),r=fe()},m(u,c){s&&s.m(u,c),R(u,e,c),o&&o.m(u,c),R(u,n,c),a&&a.m(u,c),R(u,i,c),l&&l.m(u,c),R(u,r,c)},p(u,c){u[40].desc!=null?o?o.p(u,c):(o=zo(u),o.c(),o.m(n.parentNode,n)):o&&(o.d(1),o=null),u[40].desc!=null&&u[14]&&u[14][u[42]]!=null?a||(a=Uo(),a.c(),a.m(i.parentNode,i)):a&&(a.d(1),a=null),u[14]!=null?l?l.p(u,c):(l=jo(u),l.c(),l.m(r.parentNode,r)):l&&(l.d(1),l=null)},d(u){u&&(v(e),v(n),v(i),v(r)),s&&s.d(u),o&&o.d(u),a&&a.d(u),l&&l.d(u)}}}function Gp(t){let e;return{c(){e=Ee(" /")},l(n){e=ve(n," /")},m(n,i){R(n,e,i)},d(n){n&&v(e)}}}function zo(t){let e=t[40].desc+"",n;return{c(){n=Ee(e)},l(i){n=ve(i,e)},m(i,r){R(i,n,r)},p(i,r){r[0]&128&&e!==(e=i[40].desc+"")&&je(n,e)},d(i){i&&v(n)}}}function Uo(t){let e;return{c(){e=Ee("-")},l(n){e=ve(n,"-")},m(n,i){R(n,e,i)},d(n){n&&v(e)}}}function jo(t){let e=(100*(t[14][t[42]]||0)).toFixed(1)+"",n,i;return{c(){n=Ee(e),i=Ee("%")},l(r){n=ve(r,e),i=ve(r,"%")},m(r,s){R(r,n,s),R(r,i,s)},p(r,s){s[0]&16384&&e!==(e=(100*(r[14][r[42]]||0)).toFixed(1)+"")&&je(n,e)},d(r){r&&(v(n),v(i))}}}function Go(t){let e,n=(t[40].desc!=null||t[14]&&t[14][t[42]]!=null)&&Ho(t);return{c(){n&&n.c(),e=fe()},l(i){n&&n.l(i),e=fe()},m(i,r){n&&n.m(i,r),R(i,e,r)},p(i,r){i[40].desc!=null||i[14]&&i[14][i[42]]!=null?n?n.p(i,r):(n=Ho(i),n.c(),n.m(e.parentNode,e)):n&&(n.d(1),n=null)},d(i){i&&v(e),n&&n.d(i)}}}function Vo(t){let e,n,i,r;const s=t[30]["additional-loading-text"],o=Ut(s,t,t[29],Ro);return{c(){e=Z("p"),n=Ee(t[9]),i=oe(),o&&o.c(),this.h()},l(a){e=X(a,"P",{class:!0});var l=U(e);n=ve(l,t[9]),l.forEach(v),i=ae(a),o&&o.l(a),this.h()},h(){_(e,"class","loading svelte-ls20lj")},m(a,l){R(a,e,l),M(e,n),R(a,i,l),o&&o.m(a,l),r=!0},p(a,l){(!r||l[0]&512)&&je(n,a[9]),o&&o.p&&(!r||l[0]&536870912)&&jt(o,s,a,a[29],r?Vt(s,a[29],l,Ip):Gt(a[29]),Ro)},i(a){r||(B(o,a),r=!0)},o(a){j(o,a),r=!1},d(a){a&&(v(e),v(i)),o&&o.d(a)}}}function Vp(t){let e,n,i,r,s;const o=[Bp,Pp],a=[];function l(u,c){return u[4]==="pending"?0:u[4]==="error"?1:-1}return~(n=l(t))&&(i=a[n]=o[n](t)),{c(){e=Z("div"),i&&i.c(),this.h()},l(u){e=X(u,"DIV",{class:!0});var c=U(e);i&&i.l(c),c.forEach(v),this.h()},h(){_(e,"class",r="wrap "+t[8]+" "+t[6]+" svelte-ls20lj"),G(e,"hide",!t[4]||t[4]==="complete"||t[6]==="hidden"||t[4]=="streaming"),G(e,"translucent",t[8]==="center"&&(t[4]==="pending"||t[4]==="error")||t[11]||t[6]==="minimal"),G(e,"generating",t[4]==="generating"&&t[6]==="full"),G(e,"border",t[12]),Y(e,"position",t[10]?"absolute":"static"),Y(e,"padding",t[10]?"0":"var(--size-8) 0")},m(u,c){R(u,e,c),~n&&a[n].m(e,null),t[33](e),s=!0},p(u,c){let h=n;n=l(u),n===h?~n&&a[n].p(u,c):(i&&(Be(),j(a[h],1,1,()=>{a[h]=null}),Ne()),~n?(i=a[n],i?i.p(u,c):(i=a[n]=o[n](u),i.c()),B(i,1),i.m(e,null)):i=null),(!s||c[0]&320&&r!==(r="wrap "+u[8]+" "+u[6]+" svelte-ls20lj"))&&_(e,"class",r),(!s||c[0]&336)&&G(e,"hide",!u[4]||u[4]==="complete"||u[6]==="hidden"||u[4]=="streaming"),(!s||c[0]&2384)&&G(e,"translucent",u[8]==="center"&&(u[4]==="pending"||u[4]==="error")||u[11]||u[6]==="minimal"),(!s||c[0]&336)&&G(e,"generating",u[4]==="generating"&&u[6]==="full"),(!s||c[0]&4416)&&G(e,"border",u[12]),c[0]&1024&&Y(e,"position",u[10]?"absolute":"static"),c[0]&1024&&Y(e,"padding",u[10]?"0":"var(--size-8) 0")},i(u){s||(B(i),s=!0)},o(u){j(i),s=!1},d(u){u&&v(e),~n&&a[n].d(),t[33](null)}}}let Si=[],Cr=!1;const qp=typeof window<"u",yu=qp?window.requestAnimationFrame:t=>{};async function Wp(t,e=!0){if(!(window.__gradio_mode__==="website"||window.__gradio_mode__!=="app"&&e!==!0)){if(Si.push(t),!Cr)Cr=!0;else return;await on(),yu(()=>{let n=[0,0];for(let i=0;i<Si.length;i++){const s=Si[i].getBoundingClientRect();(i===0||s.top+window.scrollY<=n[0])&&(n[0]=s.top+window.scrollY,n[1]=i)}window.scrollTo({top:n[0]-20,behavior:"smooth"}),Cr=!1,Si=[]})}}function Zp(t,e,n){let i,{$$slots:r={},$$scope:s}=e;const o=zn();let{i18n:a}=e,{eta:l=null}=e,{queue_position:u}=e,{queue_size:c}=e,{status:h}=e,{scroll_to_output:f=!1}=e,{timer:d=!0}=e,{show_progress:m="full"}=e,{message:p=null}=e,{progress:b=null}=e,{variant:D="default"}=e,{loading_text:y="Loading..."}=e,{absolute:g=!0}=e,{translucent:A=!1}=e,{border:w=!1}=e,{autoscroll:E}=e,S,z=!1,C=0,N=0,re=null,x=null,le=0,me=null,ke,ue=null,Q=!0;const O=()=>{n(0,l=n(27,re=n(19,k=null))),n(25,C=performance.now()),n(26,N=0),z=!0,I()};function I(){yu(()=>{n(26,N=(performance.now()-C)/1e3),z&&I()})}function L(){n(26,N=0),n(0,l=n(27,re=n(19,k=null))),z&&(z=!1)}Na(()=>{z&&L()});let k=null;function J(q){Je[q?"unshift":"push"](()=>{ue=q,n(16,ue),n(7,b),n(14,me),n(15,ke)})}const V=()=>{o("clear_status")};function $(q){Je[q?"unshift":"push"](()=>{S=q,n(13,S)})}return t.$$set=q=>{"i18n"in q&&n(1,a=q.i18n),"eta"in q&&n(0,l=q.eta),"queue_position"in q&&n(2,u=q.queue_position),"queue_size"in q&&n(3,c=q.queue_size),"status"in q&&n(4,h=q.status),"scroll_to_output"in q&&n(22,f=q.scroll_to_output),"timer"in q&&n(5,d=q.timer),"show_progress"in q&&n(6,m=q.show_progress),"message"in q&&n(23,p=q.message),"progress"in q&&n(7,b=q.progress),"variant"in q&&n(8,D=q.variant),"loading_text"in q&&n(9,y=q.loading_text),"absolute"in q&&n(10,g=q.absolute),"translucent"in q&&n(11,A=q.translucent),"border"in q&&n(12,w=q.border),"autoscroll"in q&&n(24,E=q.autoscroll),"$$scope"in q&&n(29,s=q.$$scope)},t.$$.update=()=>{t.$$.dirty[0]&436207617&&(l===null&&n(0,l=re),l!=null&&re!==l&&(n(28,x=(performance.now()-C)/1e3+l),n(19,k=x.toFixed(1)),n(27,re=l))),t.$$.dirty[0]&335544320&&n(17,le=x===null||x<=0||!N?null:Math.min(N/x,1)),t.$$.dirty[0]&128&&b!=null&&n(18,Q=!1),t.$$.dirty[0]&114816&&(b!=null?n(14,me=b.map(q=>{if(q.index!=null&&q.length!=null)return q.index/q.length;if(q.progress!=null)return q.progress})):n(14,me=null),me?(n(15,ke=me[me.length-1]),ue&&(ke===0?n(16,ue.style.transition="0",ue):n(16,ue.style.transition="150ms",ue))):n(15,ke=void 0)),t.$$.dirty[0]&16&&(h==="pending"?O():L()),t.$$.dirty[0]&20979728&&S&&f&&(h==="pending"||h==="complete")&&Wp(S,E),t.$$.dirty[0]&8388624,t.$$.dirty[0]&67108864&&n(20,i=N.toFixed(1))},[l,a,u,c,h,d,m,b,D,y,g,A,w,S,me,ke,ue,le,Q,k,i,o,f,p,E,C,N,re,x,s,r,J,V,$]}class Xp extends qe{constructor(e){super(),We(this,e,Zp,Vp,Xe,{i18n:1,eta:0,queue_position:2,queue_size:3,status:4,scroll_to_output:22,timer:5,show_progress:6,message:23,progress:7,variant:8,loading_text:9,absolute:10,translucent:11,border:12,autoscroll:24},null,[-1,-1])}}/*! @license DOMPurify 3.0.3 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.3/LICENSE */const{entries:Du,setPrototypeOf:qo,isFrozen:Yp,getPrototypeOf:Jp,getOwnPropertyDescriptor:Qp}=Object;let{freeze:vt,seal:zt,create:Kp}=Object,{apply:Qr,construct:Kr}=typeof Reflect<"u"&&Reflect;Qr||(Qr=function(e,n,i){return e.apply(n,i)});vt||(vt=function(e){return e});zt||(zt=function(e){return e});Kr||(Kr=function(e,n){return new e(...n)});const $p=Pt(Array.prototype.forEach),Wo=Pt(Array.prototype.pop),Zn=Pt(Array.prototype.push),Hi=Pt(String.prototype.toLowerCase),Sr=Pt(String.prototype.toString),em=Pt(String.prototype.match),Mt=Pt(String.prototype.replace),tm=Pt(String.prototype.indexOf),nm=Pt(String.prototype.trim),At=Pt(RegExp.prototype.test),Xn=im(TypeError);function Pt(t){return function(e){for(var n=arguments.length,i=new Array(n>1?n-1:0),r=1;r<n;r++)i[r-1]=arguments[r];return Qr(t,e,i)}}function im(t){return function(){for(var e=arguments.length,n=new Array(e),i=0;i<e;i++)n[i]=arguments[i];return Kr(t,n)}}function Fe(t,e,n){var i;n=(i=n)!==null&&i!==void 0?i:Hi,qo&&qo(t,null);let r=e.length;for(;r--;){let s=e[r];if(typeof s=="string"){const o=n(s);o!==s&&(Yp(e)||(e[r]=o),s=o)}t[s]=!0}return t}function Rn(t){const e=Kp(null);for(const[n,i]of Du(t))e[n]=i;return e}function Li(t,e){for(;t!==null;){const i=Qp(t,e);if(i){if(i.get)return Pt(i.get);if(typeof i.value=="function")return Pt(i.value)}t=Jp(t)}function n(i){return console.warn("fallback value for",i),null}return n}const Zo=vt(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Lr=vt(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Rr=vt(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),rm=vt(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Or=vt(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),sm=vt(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Xo=vt(["#text"]),Yo=vt(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),Ir=vt(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Jo=vt(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Ri=vt(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),om=zt(/\{\{[\w\W]*|[\w\W]*\}\}/gm),am=zt(/<%[\w\W]*|[\w\W]*%>/gm),lm=zt(/\${[\w\W]*}/gm),um=zt(/^data-[\-\w.\u00B7-\uFFFF]/),cm=zt(/^aria-[\-\w]+$/),ku=zt(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),fm=zt(/^(?:\w+script|data):/i),hm=zt(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Au=zt(/^html$/i);var Qo=Object.freeze({__proto__:null,MUSTACHE_EXPR:om,ERB_EXPR:am,TMPLIT_EXPR:lm,DATA_ATTR:um,ARIA_ATTR:cm,IS_ALLOWED_URI:ku,IS_SCRIPT_OR_DATA:fm,ATTR_WHITESPACE:hm,DOCTYPE_NAME:Au});const dm=()=>typeof window>"u"?null:window,pm=function(e,n){if(typeof e!="object"||typeof e.createPolicy!="function")return null;let i=null;const r="data-tt-policy-suffix";n&&n.hasAttribute(r)&&(i=n.getAttribute(r));const s="dompurify"+(i?"#"+i:"");try{return e.createPolicy(s,{createHTML(o){return o},createScriptURL(o){return o}})}catch{return console.warn("TrustedTypes policy "+s+" could not be created."),null}};function Fu(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:dm();const e=ce=>Fu(ce);if(e.version="3.0.3",e.removed=[],!t||!t.document||t.document.nodeType!==9)return e.isSupported=!1,e;const n=t.document,i=n.currentScript;let{document:r}=t;const{DocumentFragment:s,HTMLTemplateElement:o,Node:a,Element:l,NodeFilter:u,NamedNodeMap:c=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:h,DOMParser:f,trustedTypes:d}=t,m=l.prototype,p=Li(m,"cloneNode"),b=Li(m,"nextSibling"),D=Li(m,"childNodes"),y=Li(m,"parentNode");if(typeof o=="function"){const ce=r.createElement("template");ce.content&&ce.content.ownerDocument&&(r=ce.content.ownerDocument)}let g,A="";const{implementation:w,createNodeIterator:E,createDocumentFragment:S,getElementsByTagName:z}=r,{importNode:C}=n;let N={};e.isSupported=typeof Du=="function"&&typeof y=="function"&&w&&w.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:re,ERB_EXPR:x,TMPLIT_EXPR:le,DATA_ATTR:me,ARIA_ATTR:ke,IS_SCRIPT_OR_DATA:ue,ATTR_WHITESPACE:Q}=Qo;let{IS_ALLOWED_URI:O}=Qo,I=null;const L=Fe({},[...Zo,...Lr,...Rr,...Or,...Xo]);let k=null;const J=Fe({},[...Yo,...Ir,...Jo,...Ri]);let V=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),$=null,q=null,Ce=!0,Ze=!0,he=!1,Me=!0,it=!1,$e=!1,ct=!1,et=!1,st=!1,ye=!1,lt=!1,yt=!0,gt=!1;const xe="user-content-";let dt=!0,ht=!1,tt={},H=null;const De=Fe({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Ie=null;const Ye=Fe({},["audio","video","img","source","image","track"]);let bt=null;const kt=Fe({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Ge="http://www.w3.org/1998/Math/MathML",se="http://www.w3.org/2000/svg",Ae="http://www.w3.org/1999/xhtml";let Tt=Ae,hn=!1,Gn=null;const ar=Fe({},[Ge,se,Ae],Sr);let Xt;const hi=["application/xhtml+xml","text/html"],di="text/html";let ft,Ct=null;const Yt=r.createElement("form"),pi=function(F){return F instanceof RegExp||F instanceof Function},kn=function(F){if(!(Ct&&Ct===F)){if((!F||typeof F!="object")&&(F={}),F=Rn(F),Xt=hi.indexOf(F.PARSER_MEDIA_TYPE)===-1?Xt=di:Xt=F.PARSER_MEDIA_TYPE,ft=Xt==="application/xhtml+xml"?Sr:Hi,I="ALLOWED_TAGS"in F?Fe({},F.ALLOWED_TAGS,ft):L,k="ALLOWED_ATTR"in F?Fe({},F.ALLOWED_ATTR,ft):J,Gn="ALLOWED_NAMESPACES"in F?Fe({},F.ALLOWED_NAMESPACES,Sr):ar,bt="ADD_URI_SAFE_ATTR"in F?Fe(Rn(kt),F.ADD_URI_SAFE_ATTR,ft):kt,Ie="ADD_DATA_URI_TAGS"in F?Fe(Rn(Ye),F.ADD_DATA_URI_TAGS,ft):Ye,H="FORBID_CONTENTS"in F?Fe({},F.FORBID_CONTENTS,ft):De,$="FORBID_TAGS"in F?Fe({},F.FORBID_TAGS,ft):{},q="FORBID_ATTR"in F?Fe({},F.FORBID_ATTR,ft):{},tt="USE_PROFILES"in F?F.USE_PROFILES:!1,Ce=F.ALLOW_ARIA_ATTR!==!1,Ze=F.ALLOW_DATA_ATTR!==!1,he=F.ALLOW_UNKNOWN_PROTOCOLS||!1,Me=F.ALLOW_SELF_CLOSE_IN_ATTR!==!1,it=F.SAFE_FOR_TEMPLATES||!1,$e=F.WHOLE_DOCUMENT||!1,st=F.RETURN_DOM||!1,ye=F.RETURN_DOM_FRAGMENT||!1,lt=F.RETURN_TRUSTED_TYPE||!1,et=F.FORCE_BODY||!1,yt=F.SANITIZE_DOM!==!1,gt=F.SANITIZE_NAMED_PROPS||!1,dt=F.KEEP_CONTENT!==!1,ht=F.IN_PLACE||!1,O=F.ALLOWED_URI_REGEXP||ku,Tt=F.NAMESPACE||Ae,V=F.CUSTOM_ELEMENT_HANDLING||{},F.CUSTOM_ELEMENT_HANDLING&&pi(F.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(V.tagNameCheck=F.CUSTOM_ELEMENT_HANDLING.tagNameCheck),F.CUSTOM_ELEMENT_HANDLING&&pi(F.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(V.attributeNameCheck=F.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),F.CUSTOM_ELEMENT_HANDLING&&typeof F.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(V.allowCustomizedBuiltInElements=F.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),it&&(Ze=!1),ye&&(st=!0),tt&&(I=Fe({},[...Xo]),k=[],tt.html===!0&&(Fe(I,Zo),Fe(k,Yo)),tt.svg===!0&&(Fe(I,Lr),Fe(k,Ir),Fe(k,Ri)),tt.svgFilters===!0&&(Fe(I,Rr),Fe(k,Ir),Fe(k,Ri)),tt.mathMl===!0&&(Fe(I,Or),Fe(k,Jo),Fe(k,Ri))),F.ADD_TAGS&&(I===L&&(I=Rn(I)),Fe(I,F.ADD_TAGS,ft)),F.ADD_ATTR&&(k===J&&(k=Rn(k)),Fe(k,F.ADD_ATTR,ft)),F.ADD_URI_SAFE_ATTR&&Fe(bt,F.ADD_URI_SAFE_ATTR,ft),F.FORBID_CONTENTS&&(H===De&&(H=Rn(H)),Fe(H,F.FORBID_CONTENTS,ft)),dt&&(I["#text"]=!0),$e&&Fe(I,["html","head","body"]),I.table&&(Fe(I,["tbody"]),delete $.tbody),F.TRUSTED_TYPES_POLICY){if(typeof F.TRUSTED_TYPES_POLICY.createHTML!="function")throw Xn('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof F.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw Xn('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');g=F.TRUSTED_TYPES_POLICY,A=g.createHTML("")}else g===void 0&&(g=pm(d,i)),g!==null&&typeof A=="string"&&(A=g.createHTML(""));vt&&vt(F),Ct=F}},mi=Fe({},["mi","mo","mn","ms","mtext"]),_i=Fe({},["foreignobject","desc","title","annotation-xml"]),lr=Fe({},["title","style","font","a","script"]),An=Fe({},Lr);Fe(An,Rr),Fe(An,rm);const Fn=Fe({},Or);Fe(Fn,sm);const ur=function(F){let W=y(F);(!W||!W.tagName)&&(W={namespaceURI:Tt,tagName:"template"});const ee=Hi(F.tagName),T=Hi(W.tagName);return Gn[F.namespaceURI]?F.namespaceURI===se?W.namespaceURI===Ae?ee==="svg":W.namespaceURI===Ge?ee==="svg"&&(T==="annotation-xml"||mi[T]):!!An[ee]:F.namespaceURI===Ge?W.namespaceURI===Ae?ee==="math":W.namespaceURI===se?ee==="math"&&_i[T]:!!Fn[ee]:F.namespaceURI===Ae?W.namespaceURI===se&&!_i[T]||W.namespaceURI===Ge&&!mi[T]?!1:!Fn[ee]&&(lr[ee]||!An[ee]):!!(Xt==="application/xhtml+xml"&&Gn[F.namespaceURI]):!1},St=function(F){Zn(e.removed,{element:F});try{F.parentNode.removeChild(F)}catch{F.remove()}},dn=function(F,W){try{Zn(e.removed,{attribute:W.getAttributeNode(F),from:W})}catch{Zn(e.removed,{attribute:null,from:W})}if(W.removeAttribute(F),F==="is"&&!k[F])if(st||ye)try{St(W)}catch{}else try{W.setAttribute(F,"")}catch{}},Tn=function(F){let W,ee;if(et)F="<remove></remove>"+F;else{const ne=em(F,/^[\r\n\t ]+/);ee=ne&&ne[0]}Xt==="application/xhtml+xml"&&Tt===Ae&&(F='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+F+"</body></html>");const T=g?g.createHTML(F):F;if(Tt===Ae)try{W=new f().parseFromString(T,Xt)}catch{}if(!W||!W.documentElement){W=w.createDocument(Tt,"template",null);try{W.documentElement.innerHTML=hn?A:T}catch{}}const ie=W.body||W.documentElement;return F&&ee&&ie.insertBefore(r.createTextNode(ee),ie.childNodes[0]||null),Tt===Ae?z.call(W,$e?"html":"body")[0]:$e?W.documentElement:ie},gi=function(F){return E.call(F.ownerDocument||F,F,u.SHOW_ELEMENT|u.SHOW_COMMENT|u.SHOW_TEXT,null,!1)},cr=function(F){return F instanceof h&&(typeof F.nodeName!="string"||typeof F.textContent!="string"||typeof F.removeChild!="function"||!(F.attributes instanceof c)||typeof F.removeAttribute!="function"||typeof F.setAttribute!="function"||typeof F.namespaceURI!="string"||typeof F.insertBefore!="function"||typeof F.hasChildNodes!="function")},Cn=function(F){return typeof a=="object"?F instanceof a:F&&typeof F=="object"&&typeof F.nodeType=="number"&&typeof F.nodeName=="string"},Bt=function(F,W,ee){N[F]&&$p(N[F],T=>{T.call(e,W,ee,Ct)})},bi=function(F){let W;if(Bt("beforeSanitizeElements",F,null),cr(F))return St(F),!0;const ee=ft(F.nodeName);if(Bt("uponSanitizeElement",F,{tagName:ee,allowedTags:I}),F.hasChildNodes()&&!Cn(F.firstElementChild)&&(!Cn(F.content)||!Cn(F.content.firstElementChild))&&At(/<[/\w]/g,F.innerHTML)&&At(/<[/\w]/g,F.textContent))return St(F),!0;if(!I[ee]||$[ee]){if(!$[ee]&&Ei(ee)&&(V.tagNameCheck instanceof RegExp&&At(V.tagNameCheck,ee)||V.tagNameCheck instanceof Function&&V.tagNameCheck(ee)))return!1;if(dt&&!H[ee]){const T=y(F)||F.parentNode,ie=D(F)||F.childNodes;if(ie&&T){const ne=ie.length;for(let de=ne-1;de>=0;--de)T.insertBefore(p(ie[de],!0),b(F))}}return St(F),!0}return F instanceof l&&!ur(F)||(ee==="noscript"||ee==="noembed")&&At(/<\/no(script|embed)/i,F.innerHTML)?(St(F),!0):(it&&F.nodeType===3&&(W=F.textContent,W=Mt(W,re," "),W=Mt(W,x," "),W=Mt(W,le," "),F.textContent!==W&&(Zn(e.removed,{element:F.cloneNode()}),F.textContent=W)),Bt("afterSanitizeElements",F,null),!1)},wi=function(F,W,ee){if(yt&&(W==="id"||W==="name")&&(ee in r||ee in Yt))return!1;if(!(Ze&&!q[W]&&At(me,W))){if(!(Ce&&At(ke,W))){if(!k[W]||q[W]){if(!(Ei(F)&&(V.tagNameCheck instanceof RegExp&&At(V.tagNameCheck,F)||V.tagNameCheck instanceof Function&&V.tagNameCheck(F))&&(V.attributeNameCheck instanceof RegExp&&At(V.attributeNameCheck,W)||V.attributeNameCheck instanceof Function&&V.attributeNameCheck(W))||W==="is"&&V.allowCustomizedBuiltInElements&&(V.tagNameCheck instanceof RegExp&&At(V.tagNameCheck,ee)||V.tagNameCheck instanceof Function&&V.tagNameCheck(ee))))return!1}else if(!bt[W]){if(!At(O,Mt(ee,Q,""))){if(!((W==="src"||W==="xlink:href"||W==="href")&&F!=="script"&&tm(ee,"data:")===0&&Ie[F])){if(!(he&&!At(ue,Mt(ee,Q,"")))){if(ee)return!1}}}}}}return!0},Ei=function(F){return F.indexOf("-")>0},vi=function(F){let W,ee,T,ie;Bt("beforeSanitizeAttributes",F,null);const{attributes:ne}=F;if(!ne)return;const de={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:k};for(ie=ne.length;ie--;){W=ne[ie];const{name:te,namespaceURI:He}=W;if(ee=te==="value"?W.value:nm(W.value),T=ft(te),de.attrName=T,de.attrValue=ee,de.keepAttr=!0,de.forceKeepAttr=void 0,Bt("uponSanitizeAttribute",F,de),ee=de.attrValue,de.forceKeepAttr||(dn(te,F),!de.keepAttr))continue;if(!Me&&At(/\/>/i,ee)){dn(te,F);continue}it&&(ee=Mt(ee,re," "),ee=Mt(ee,x," "),ee=Mt(ee,le," "));const ot=ft(F.nodeName);if(wi(ot,T,ee)){if(gt&&(T==="id"||T==="name")&&(dn(te,F),ee=xe+ee),g&&typeof d=="object"&&typeof d.getAttributeType=="function"&&!He)switch(d.getAttributeType(ot,T)){case"TrustedHTML":{ee=g.createHTML(ee);break}case"TrustedScriptURL":{ee=g.createScriptURL(ee);break}}try{He?F.setAttributeNS(He,te,ee):F.setAttribute(te,ee),Wo(e.removed)}catch{}}}Bt("afterSanitizeAttributes",F,null)},fr=function ce(F){let W;const ee=gi(F);for(Bt("beforeSanitizeShadowDOM",F,null);W=ee.nextNode();)Bt("uponSanitizeShadowNode",W,null),!bi(W)&&(W.content instanceof s&&ce(W.content),vi(W));Bt("afterSanitizeShadowDOM",F,null)};return e.sanitize=function(ce){let F=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},W,ee,T,ie;if(hn=!ce,hn&&(ce="<!-->"),typeof ce!="string"&&!Cn(ce))if(typeof ce.toString=="function"){if(ce=ce.toString(),typeof ce!="string")throw Xn("dirty is not a string, aborting")}else throw Xn("toString is not a function");if(!e.isSupported)return ce;if(ct||kn(F),e.removed=[],typeof ce=="string"&&(ht=!1),ht){if(ce.nodeName){const te=ft(ce.nodeName);if(!I[te]||$[te])throw Xn("root node is forbidden and cannot be sanitized in-place")}}else if(ce instanceof a)W=Tn("<!---->"),ee=W.ownerDocument.importNode(ce,!0),ee.nodeType===1&&ee.nodeName==="BODY"||ee.nodeName==="HTML"?W=ee:W.appendChild(ee);else{if(!st&&!it&&!$e&&ce.indexOf("<")===-1)return g&&lt?g.createHTML(ce):ce;if(W=Tn(ce),!W)return st?null:lt?A:""}W&&et&&St(W.firstChild);const ne=gi(ht?ce:W);for(;T=ne.nextNode();)bi(T)||(T.content instanceof s&&fr(T.content),vi(T));if(ht)return ce;if(st){if(ye)for(ie=S.call(W.ownerDocument);W.firstChild;)ie.appendChild(W.firstChild);else ie=W;return(k.shadowroot||k.shadowrootmod)&&(ie=C.call(n,ie,!0)),ie}let de=$e?W.outerHTML:W.innerHTML;return $e&&I["!doctype"]&&W.ownerDocument&&W.ownerDocument.doctype&&W.ownerDocument.doctype.name&&At(Au,W.ownerDocument.doctype.name)&&(de="<!DOCTYPE "+W.ownerDocument.doctype.name+`>
`+de),it&&(de=Mt(de,re," "),de=Mt(de,x," "),de=Mt(de,le," ")),g&&lt?g.createHTML(de):de},e.setConfig=function(ce){kn(ce),ct=!0},e.clearConfig=function(){Ct=null,ct=!1},e.isValidAttribute=function(ce,F,W){Ct||kn({});const ee=ft(ce),T=ft(F);return wi(ee,T,W)},e.addHook=function(ce,F){typeof F=="function"&&(N[ce]=N[ce]||[],Zn(N[ce],F))},e.removeHook=function(ce){if(N[ce])return Wo(N[ce])},e.removeHooks=function(ce){N[ce]&&(N[ce]=[])},e.removeAllHooks=function(){N={}},e}var Ko=Fu();function mm(t){let e,n;return e=new Ep({}),{c(){ge(e.$$.fragment)},l(i){Re(e.$$.fragment,i)},m(i,r){be(e,i,r),n=!0},i(i){n||(B(e.$$.fragment,i),n=!0)},o(i){j(e.$$.fragment,i),n=!1},d(i){we(e,i)}}}function _m(t){let e,n;return e=new kp({}),{c(){ge(e.$$.fragment)},l(i){Re(e.$$.fragment,i)},m(i,r){be(e,i,r),n=!0},i(i){n||(B(e.$$.fragment,i),n=!0)},o(i){j(e.$$.fragment,i),n=!1},d(i){we(e,i)}}}function gm(t){let e,n;return e=new yp({}),{c(){ge(e.$$.fragment)},l(i){Re(e.$$.fragment,i)},m(i,r){be(e,i,r),n=!0},i(i){n||(B(e.$$.fragment,i),n=!0)},o(i){j(e.$$.fragment,i),n=!1},d(i){we(e,i)}}}function bm(t){let e,n;return e=new Rp({}),{c(){ge(e.$$.fragment)},l(i){Re(e.$$.fragment,i)},m(i,r){be(e,i,r),n=!0},i(i){n||(B(e.$$.fragment,i),n=!0)},o(i){j(e.$$.fragment,i),n=!1},d(i){we(e,i)}}}function wm(t){let e,n,i,r,s,o,a,l,u,c,h,f,d,m,p,b,D,y,g="×",A,w,E,S,z,C,N,re,x,le,me;const ke=[bm,gm,_m,mm],ue=[];function Q(O,I){return O[2]==="warning"?0:O[2]==="info"?1:O[2]==="success"?2:O[2]==="error"?3:-1}return~(i=Q(t))&&(r=ue[i]=ke[i](t)),{c(){e=Z("div"),n=Z("div"),r&&r.c(),o=oe(),a=Z("div"),l=Z("div"),u=Ee(t[1]),h=oe(),f=Z("div"),d=new Ki(!1),b=oe(),D=Z("button"),y=Z("span"),y.textContent=g,w=oe(),E=Z("div"),this.h()},l(O){e=X(O,"DIV",{class:!0,role:!0,"data-testid":!0});var I=U(e);n=X(I,"DIV",{class:!0});var L=U(n);r&&r.l(L),L.forEach(v),o=ae(I),a=X(I,"DIV",{class:!0});var k=U(a);l=X(k,"DIV",{class:!0});var J=U(l);u=ve(J,t[1]),J.forEach(v),h=ae(k),f=X(k,"DIV",{class:!0});var V=U(f);d=$i(V,!1),V.forEach(v),k.forEach(v),b=ae(I),D=X(I,"BUTTON",{class:!0,type:!0,"aria-label":!0,"data-testid":!0});var $=U(D);y=X($,"SPAN",{"aria-hidden":!0,"data-svelte-h":!0}),Pn(y)!=="svelte-zpkfw7"&&(y.textContent=g),$.forEach(v),w=ae(I),E=X(I,"DIV",{class:!0,style:!0}),U(E).forEach(v),I.forEach(v),this.h()},h(){_(n,"class",s="toast-icon "+t[2]+" svelte-fee5uc"),_(l,"class",c="toast-title "+t[2]+" svelte-fee5uc"),d.a=null,_(f,"class",m="toast-text "+t[2]+" svelte-fee5uc"),_(a,"class",p="toast-details "+t[2]+" svelte-fee5uc"),_(y,"aria-hidden","true"),_(D,"class",A="toast-close "+t[2]+" svelte-fee5uc"),_(D,"type","button"),_(D,"aria-label","Close"),_(D,"data-testid","toast-close"),_(E,"class",S="timer "+t[2]+" svelte-fee5uc"),_(E,"style",z=`animation-duration: ${t[3]};`),_(e,"class",C="toast-body "+t[2]+" svelte-fee5uc"),_(e,"role","alert"),_(e,"data-testid","toast-body"),G(e,"hidden",!t[4])},m(O,I){R(O,e,I),M(e,n),~i&&ue[i].m(n,null),M(e,o),M(e,a),M(a,l),M(l,u),M(a,h),M(a,f),d.m(t[0],f),M(e,b),M(e,D),M(D,y),M(e,w),M(e,E),x=!0,le||(me=[Te(D,"click",t[5]),Te(e,"click",vs(t[9])),Te(e,"keydown",vs(t[10]))],le=!0)},p(O,[I]){let L=i;i=Q(O),i!==L&&(r&&(Be(),j(ue[L],1,1,()=>{ue[L]=null}),Ne()),~i?(r=ue[i],r||(r=ue[i]=ke[i](O),r.c()),B(r,1),r.m(n,null)):r=null),(!x||I&4&&s!==(s="toast-icon "+O[2]+" svelte-fee5uc"))&&_(n,"class",s),(!x||I&2)&&je(u,O[1]),(!x||I&4&&c!==(c="toast-title "+O[2]+" svelte-fee5uc"))&&_(l,"class",c),(!x||I&1)&&d.p(O[0]),(!x||I&4&&m!==(m="toast-text "+O[2]+" svelte-fee5uc"))&&_(f,"class",m),(!x||I&4&&p!==(p="toast-details "+O[2]+" svelte-fee5uc"))&&_(a,"class",p),(!x||I&4&&A!==(A="toast-close "+O[2]+" svelte-fee5uc"))&&_(D,"class",A),(!x||I&4&&S!==(S="timer "+O[2]+" svelte-fee5uc"))&&_(E,"class",S),(!x||I&8&&z!==(z=`animation-duration: ${O[3]};`))&&_(E,"style",z),(!x||I&4&&C!==(C="toast-body "+O[2]+" svelte-fee5uc"))&&_(e,"class",C),(!x||I&20)&&G(e,"hidden",!O[4])},i(O){x||(B(r),O&&Ma(()=>{x&&(re&&re.end(1),N=xa(e,Br,{duration:200,delay:100}),N.start())}),x=!0)},o(O){j(r),N&&N.invalidate(),O&&(re=zu(e,Br,{duration:200})),x=!1},d(O){O&&v(e),~i&&ue[i].d(),O&&re&&re.end(),le=!1,En(me)}}}function Em(t,e,n){let i,r,{title:s=""}=e,{message:o=""}=e,{type:a}=e,{id:l}=e,{duration:u=10}=e,{visible:c=!0}=e;const h=b=>{try{return!!b&&new URL(b,location.href).origin!==location.origin}catch{return!1}};Ko.addHook("afterSanitizeAttributes",function(b){"target"in b&&h(b.getAttribute("href"))&&(b.setAttribute("target","_blank"),b.setAttribute("rel","noopener noreferrer"))});const f=zn();function d(){f("close",l)}gn(()=>{u!==null&&setTimeout(()=>{d()},u*1e3)});function m(b){_t.call(this,t,b)}function p(b){_t.call(this,t,b)}return t.$$set=b=>{"title"in b&&n(1,s=b.title),"message"in b&&n(0,o=b.message),"type"in b&&n(2,a=b.type),"id"in b&&n(7,l=b.id),"duration"in b&&n(6,u=b.duration),"visible"in b&&n(8,c=b.visible)},t.$$.update=()=>{t.$$.dirty&1&&n(0,o=Ko.sanitize(o)),t.$$.dirty&256&&n(4,i=c),t.$$.dirty&64&&n(6,u=u||null),t.$$.dirty&64&&n(3,r=`${u||0}s`)},[o,s,a,r,i,d,u,l,c,m,p]}class vm extends qe{constructor(e){super(),We(this,e,Em,wm,Xe,{title:1,message:0,type:2,id:7,duration:6,visible:8})}}function $o(t,e,n){const i=t.slice();return i[2]=e[n].type,i[3]=e[n].title,i[4]=e[n].message,i[5]=e[n].id,i[6]=e[n].duration,i[7]=e[n].visible,i}function ea(t,e){let n,i,r,s,o=pe,a;return i=new vm({props:{type:e[2],title:e[3],message:e[4],duration:e[6],visible:e[7],id:e[5]}}),i.$on("close",e[1]),{key:t,first:null,c(){n=Z("div"),ge(i.$$.fragment),r=oe(),this.h()},l(l){n=X(l,"DIV",{});var u=U(n);Re(i.$$.fragment,u),r=ae(u),u.forEach(v),this.h()},h(){Y(n,"width","100%"),this.first=n},m(l,u){R(l,n,u),be(i,n,null),M(n,r),a=!0},p(l,u){e=l;const c={};u&1&&(c.type=e[2]),u&1&&(c.title=e[3]),u&1&&(c.message=e[4]),u&1&&(c.duration=e[6]),u&1&&(c.visible=e[7]),u&1&&(c.id=e[5]),i.$set(c)},r(){s=n.getBoundingClientRect()},f(){ju(n),o()},a(){o(),o=Gu(n,s,Ku,{duration:300})},i(l){a||(B(i.$$.fragment,l),a=!0)},o(l){j(i.$$.fragment,l),a=!1},d(l){l&&v(n),we(i)}}}function ym(t){let e,n=[],i=new Map,r,s=Ot(t[0]);const o=a=>a[5];for(let a=0;a<s.length;a+=1){let l=$o(t,s,a),u=o(l);i.set(u,n[a]=ea(u,l))}return{c(){e=Z("div");for(let a=0;a<n.length;a+=1)n[a].c();this.h()},l(a){e=X(a,"DIV",{class:!0});var l=U(e);for(let u=0;u<n.length;u+=1)n[u].l(l);l.forEach(v),this.h()},h(){_(e,"class","toast-wrap svelte-pu0yf1")},m(a,l){R(a,e,l);for(let u=0;u<n.length;u+=1)n[u]&&n[u].m(e,null);r=!0},p(a,[l]){if(l&1){s=Ot(a[0]),Be();for(let u=0;u<n.length;u+=1)n[u].r();n=Ha(n,l,o,1,a,s,i,e,Uu,ea,null,$o);for(let u=0;u<n.length;u+=1)n[u].a();Ne()}},i(a){if(!r){for(let l=0;l<s.length;l+=1)B(n[l]);r=!0}},o(a){for(let l=0;l<n.length;l+=1)j(n[l]);r=!1},d(a){a&&v(e);for(let l=0;l<n.length;l+=1)n[l].d()}}}function Dm(t){var e;t.length>0&&"parentIFrame"in window&&((e=window.parentIFrame)==null||e.scrollTo(0,0))}function km(t,e,n){let{messages:i=[]}=e;function r(s){_t.call(this,t,s)}return t.$$set=s=>{"messages"in s&&n(0,i=s.messages)},t.$$.update=()=>{t.$$.dirty&1&&Dm(i)},[i,r]}class Am extends qe{constructor(e){super(),We(this,e,km,ym,Xe,{messages:0})}}function ta(t){let e,n,i,r;const s=[{elem_id:t[5]},{elem_classes:t[6]},{target:t[3]},{visible:t[7]},t[9],{theme_mode:t[4]},{root:t[2]}];function o(u){t[14](u)}var a=t[8];function l(u,c){let h={$$slots:{default:[Fm]},$$scope:{ctx:u}};for(let f=0;f<s.length;f+=1)h=It(h,s[f]);return c!==void 0&&c&764&&(h=It(h,un(s,[c&32&&{elem_id:u[5]},c&64&&{elem_classes:u[6]},c&8&&{target:u[3]},c&128&&{visible:u[7]},c&512&&Bn(u[9]),c&16&&{theme_mode:u[4]},c&4&&{root:u[2]}]))),u[0]!==void 0&&(h.value=u[0]),{props:h}}return a&&(e=Dt(a,l(t)),t[13](e),Je.push(()=>mt(e,"value",o)),e.$on("prop_change",t[15])),{c(){e&&ge(e.$$.fragment),i=fe()},l(u){e&&Re(e.$$.fragment,u),i=fe()},m(u,c){e&&be(e,u,c),R(u,i,c),r=!0},p(u,c){if(a!==(a=u[8])){if(e){Be();const h=e;j(h.$$.fragment,1,0,()=>{we(h,1)}),Ne()}a?(e=Dt(a,l(u,c)),u[13](e),Je.push(()=>mt(e,"value",o)),e.$on("prop_change",u[15]),ge(e.$$.fragment),B(e.$$.fragment,1),be(e,i.parentNode,i)):e=null}else if(a){const h=c&764?un(s,[c&32&&{elem_id:u[5]},c&64&&{elem_classes:u[6]},c&8&&{target:u[3]},c&128&&{visible:u[7]},c&512&&Bn(u[9]),c&16&&{theme_mode:u[4]},c&4&&{root:u[2]}]):{};c&65536&&(h.$$scope={dirty:c,ctx:u}),!n&&c&1&&(n=!0,h.value=u[0],xt(()=>n=!1)),e.$set(h)}},i(u){r||(e&&B(e.$$.fragment,u),r=!0)},o(u){e&&j(e.$$.fragment,u),r=!1},d(u){u&&v(i),t[13](null),e&&we(e,u)}}}function Fm(t){let e;const n=t[12].default,i=Ut(n,t,t[16],null);return{c(){i&&i.c()},l(r){i&&i.l(r)},m(r,s){i&&i.m(r,s),e=!0},p(r,s){i&&i.p&&(!e||s&65536)&&jt(i,n,r,r[16],e?Vt(n,r[16],s,null):Gt(r[16]),null)},i(r){e||(B(i,r),e=!0)},o(r){j(i,r),e=!1},d(r){i&&i.d(r)}}}function Tm(t){let e,n,i=t[7]&&ta(t);return{c(){i&&i.c(),e=fe()},l(r){i&&i.l(r),e=fe()},m(r,s){i&&i.m(r,s),R(r,e,s),n=!0},p(r,[s]){r[7]?i?(i.p(r,s),s&128&&B(i,1)):(i=ta(r),i.c(),B(i,1),i.m(e.parentNode,e)):i&&(Be(),j(i,1,1,()=>{i=null}),Ne())},i(r){n||(B(i),n=!0)},o(r){j(i),n=!1},d(r){r&&v(e),i&&i.d(r)}}}function Cm(t,e,n){const i=["root","component","target","theme_mode","instance","value","elem_id","elem_classes","_id","visible"];let r=zi(e,i),{$$slots:s={},$$scope:o}=e,{root:a}=e,{component:l}=e,{target:u}=e,{theme_mode:c}=e,{instance:h}=e,{value:f}=e,{elem_id:d}=e,{elem_classes:m}=e,{_id:p}=e,{visible:b}=e;const D=(C,N,re)=>new CustomEvent("prop_change",{detail:{id:C,prop:N,value:re}});function y(C){return new Proxy(C,{construct(re,x){const le=new re(...x),me=Object.keys(le.$$.props);function ke(ue){return function(Q){if(!u)return;const O=D(p,ue,Q);u.dispatchEvent(O)}}return me.forEach(ue=>{Je.push(()=>mt(le,ue,ke(ue)))}),le}})}let g=y(l);const A=["description","info","title","placeholder","value","label"];function w(C){for(const N in C)A.includes(N)&&(C[N]=eo(C[N]))}function E(C){Je[C?"unshift":"push"](()=>{h=C,n(1,h)})}function S(C){f=C,n(0,f)}function z(C){_t.call(this,t,C)}return t.$$set=C=>{e=It(It({},e),za(C)),n(9,r=zi(e,i)),"root"in C&&n(2,a=C.root),"component"in C&&n(10,l=C.component),"target"in C&&n(3,u=C.target),"theme_mode"in C&&n(4,c=C.theme_mode),"instance"in C&&n(1,h=C.instance),"value"in C&&n(0,f=C.value),"elem_id"in C&&n(5,d=C.elem_id),"elem_classes"in C&&n(6,m=C.elem_classes),"_id"in C&&n(11,p=C._id),"visible"in C&&n(7,b=C.visible),"$$scope"in C&&n(16,o=C.$$scope)},t.$$.update=()=>{w(r),t.$$.dirty&1&&n(0,f=eo(f))},[f,h,a,u,c,d,m,b,g,r,l,p,s,E,S,z,o]}class Sm extends qe{constructor(e){super(),We(this,e,Cm,Tm,Vu,{root:2,component:10,target:3,theme_mode:4,instance:1,value:0,elem_id:5,elem_classes:6,_id:11,visible:7})}}function na(t,e,n){const i=t.slice();return i[16]=e[n],i}function ia(t){var u;let e,n,i,r;const s=[{_id:(u=t[0])==null?void 0:u.id},{component:t[0].component},{elem_id:"elem_id"in t[0].props&&t[0].props.elem_id||`component-${t[0].id}`},{elem_classes:"elem_classes"in t[0].props&&t[0].props.elem_classes||[]},{target:t[2]},t[0].props,{theme_mode:t[3]},{root:t[1]},{visible:typeof t[0].props.visible=="boolean"?t[0].props.visible:!0}];function o(c){t[12](c)}function a(c){t[13](c)}let l={$$slots:{default:[Lm]},$$scope:{ctx:t}};for(let c=0;c<s.length;c+=1)l=It(l,s[c]);return t[0].instance!==void 0&&(l.instance=t[0].instance),t[0].props.value!==void 0&&(l.value=t[0].props.value),e=new Sm({props:l}),Je.push(()=>mt(e,"instance",o)),Je.push(()=>mt(e,"value",a)),{c(){ge(e.$$.fragment)},l(c){Re(e.$$.fragment,c)},m(c,h){be(e,c,h),r=!0},p(c,h){var d;const f=h&15?un(s,[h&1&&{_id:(d=c[0])==null?void 0:d.id},h&1&&{component:c[0].component},h&1&&{elem_id:"elem_id"in c[0].props&&c[0].props.elem_id||`component-${c[0].id}`},h&1&&{elem_classes:"elem_classes"in c[0].props&&c[0].props.elem_classes||[]},h&4&&{target:c[2]},h&1&&Bn(c[0].props),h&8&&{theme_mode:c[3]},h&2&&{root:c[1]},h&1&&{visible:typeof c[0].props.visible=="boolean"?c[0].props.visible:!0}]):{};h&524351&&(f.$$scope={dirty:h,ctx:c}),!n&&h&1&&(n=!0,f.instance=c[0].instance,xt(()=>n=!1)),!i&&h&1&&(i=!0,f.value=c[0].props.value,xt(()=>i=!1)),e.$set(f)},i(c){r||(B(e.$$.fragment,c),r=!0)},o(c){j(e.$$.fragment,c),r=!1},d(c){we(e,c)}}}function ra(t){let e=[],n=new Map,i,r,s=Ot(t[0].children);const o=a=>a[16].id;for(let a=0;a<s.length;a+=1){let l=na(t,s,a),u=o(l);n.set(u,e[a]=sa(u,l))}return{c(){for(let a=0;a<e.length;a+=1)e[a].c();i=fe()},l(a){for(let l=0;l<e.length;l+=1)e[l].l(a);i=fe()},m(a,l){for(let u=0;u<e.length;u+=1)e[u]&&e[u].m(a,l);R(a,i,l),r=!0},p(a,l){l&63&&(s=Ot(a[0].children),Be(),e=Ha(e,l,o,1,a,s,n,i.parentNode,Wu,sa,i,na),Ne())},i(a){if(!r){for(let l=0;l<s.length;l+=1)B(e[l]);r=!0}},o(a){for(let l=0;l<e.length;l+=1)j(e[l]);r=!1},d(a){a&&v(i);for(let l=0;l<e.length;l+=1)e[l].d(a)}}}function sa(t,e){let n,i,r;return i=new Tu({props:{node:e[16],component:e[16].component,target:e[2],id:e[16].id,root:e[1],theme_mode:e[3],max_file_size:e[4],client:e[5]}}),i.$on("destroy",e[10]),i.$on("mount",e[11]),{key:t,first:null,c(){n=fe(),ge(i.$$.fragment),this.h()},l(s){n=fe(),Re(i.$$.fragment,s),this.h()},h(){this.first=n},m(s,o){R(s,n,o),be(i,s,o),r=!0},p(s,o){e=s;const a={};o&1&&(a.node=e[16]),o&1&&(a.component=e[16].component),o&4&&(a.target=e[2]),o&1&&(a.id=e[16].id),o&2&&(a.root=e[1]),o&8&&(a.theme_mode=e[3]),o&16&&(a.max_file_size=e[4]),o&32&&(a.client=e[5]),i.$set(a)},i(s){r||(B(i.$$.fragment,s),r=!0)},o(s){j(i.$$.fragment,s),r=!1},d(s){s&&v(n),we(i,s)}}}function Lm(t){let e,n,i=t[0].children&&t[0].children.length&&ra(t);return{c(){i&&i.c(),e=fe()},l(r){i&&i.l(r),e=fe()},m(r,s){i&&i.m(r,s),R(r,e,s),n=!0},p(r,s){r[0].children&&r[0].children.length?i?(i.p(r,s),s&1&&B(i,1)):(i=ra(r),i.c(),B(i,1),i.m(e.parentNode,e)):i&&(Be(),j(i,1,1,()=>{i=null}),Ne())},i(r){n||(B(i),n=!0)},o(r){j(i),n=!1},d(r){r&&v(e),i&&i.d(r)}}}function Rm(t){let e,n,i=t[0].component&&ia(t);return{c(){i&&i.c(),e=fe()},l(r){i&&i.l(r),e=fe()},m(r,s){i&&i.m(r,s),R(r,e,s),n=!0},p(r,[s]){r[0].component?i?(i.p(r,s),s&1&&B(i,1)):(i=ia(r),i.c(),B(i,1),i.m(e.parentNode,e)):i&&(Be(),j(i,1,1,()=>{i=null}),Ne())},i(r){n||(B(i),n=!0)},o(r){j(i),n=!1},d(r){r&&v(e),i&&i.d(r)}}}function Om(t,e,n){let i;Rt(t,dh,g=>n(9,i=g));let{root:r}=e,{node:s}=e,{parent:o=null}=e,{target:a}=e,{theme_mode:l}=e,{version:u}=e,{autoscroll:c}=e,{max_file_size:h}=e,{client:f}=e;const d=zn();let m=[];gn(()=>{d("mount",s.id);for(const g of m)d("mount",g.id);return()=>{d("destroy",s.id);for(const g of m)d("mount",g.id)}}),qu("BLOCK_KEY",o);function p(g){_t.call(this,t,g)}function b(g){_t.call(this,t,g)}function D(g){t.$$.not_equal(s.instance,g)&&(s.instance=g,n(0,s),n(15,m),n(2,a),n(3,l),n(7,u),n(1,r),n(8,c),n(4,h),n(9,i),n(5,f))}function y(g){t.$$.not_equal(s.props.value,g)&&(s.props.value=g,n(0,s),n(15,m),n(2,a),n(3,l),n(7,u),n(1,r),n(8,c),n(4,h),n(9,i),n(5,f))}return t.$$set=g=>{"root"in g&&n(1,r=g.root),"node"in g&&n(0,s=g.node),"parent"in g&&n(6,o=g.parent),"target"in g&&n(2,a=g.target),"theme_mode"in g&&n(3,l=g.theme_mode),"version"in g&&n(7,u=g.version),"autoscroll"in g&&n(8,c=g.autoscroll),"max_file_size"in g&&n(4,h=g.max_file_size),"client"in g&&n(5,f=g.client)},t.$$.update=()=>{var g;t.$$.dirty&1&&s&&n(0,s.children=s.children&&s.children.filter(A=>{const w=s.type!=="statustracker";return w||m.push(A),w}),s),t.$$.dirty&1&&s&&s.type==="form"&&((g=s.children)!=null&&g.every(A=>typeof A.props.visible=="boolean"&&!A.props.visible)?n(0,s.props.visible=!1,s):n(0,s.props.visible=!0,s)),t.$$.dirty&959&&n(0,s.props.gradio=new fh(s.id,a,l,u,r,c,h,i,f,Vr),s)},[s,r,a,l,h,f,o,u,c,i,p,b,D,y]}class Tu extends qe{constructor(e){super(),We(this,e,Om,Rm,Xe,{root:1,node:0,parent:6,target:2,theme_mode:3,version:7,autoscroll:8,max_file_size:4,client:5})}}function oa(t){let e,n;return e=new Tu({props:{node:t[0],root:t[1],target:t[2],theme_mode:t[3],version:t[4],autoscroll:t[5],max_file_size:t[6],client:t[7]}}),{c(){ge(e.$$.fragment)},l(i){Re(e.$$.fragment,i)},m(i,r){be(e,i,r),n=!0},p(i,r){const s={};r&1&&(s.node=i[0]),r&2&&(s.root=i[1]),r&4&&(s.target=i[2]),r&8&&(s.theme_mode=i[3]),r&16&&(s.version=i[4]),r&32&&(s.autoscroll=i[5]),r&64&&(s.max_file_size=i[6]),r&128&&(s.client=i[7]),e.$set(s)},i(i){n||(B(e.$$.fragment,i),n=!0)},o(i){j(e.$$.fragment,i),n=!1},d(i){we(e,i)}}}function Im(t){let e,n,i=t[0]&&oa(t);return{c(){i&&i.c(),e=fe()},l(r){i&&i.l(r),e=fe()},m(r,s){i&&i.m(r,s),R(r,e,s),n=!0},p(r,[s]){r[0]?i?(i.p(r,s),s&1&&B(i,1)):(i=oa(r),i.c(),B(i,1),i.m(e.parentNode,e)):i&&(Be(),j(i,1,1,()=>{i=null}),Ne())},i(r){n||(B(i),n=!0)},o(r){j(i),n=!1},d(r){r&&v(e),i&&i.d(r)}}}function Pm(t,e,n){let{rootNode:i}=e,{root:r}=e,{target:s}=e,{theme_mode:o}=e,{version:a}=e,{autoscroll:l}=e,{max_file_size:u=null}=e,{client:c}=e;const h=zn();return gn(()=>{h("mount")}),t.$$set=f=>{"rootNode"in f&&n(0,i=f.rootNode),"root"in f&&n(1,r=f.root),"target"in f&&n(2,s=f.target),"theme_mode"in f&&n(3,o=f.theme_mode),"version"in f&&n(4,a=f.version),"autoscroll"in f&&n(5,l=f.autoscroll),"max_file_size"in f&&n(6,u=f.max_file_size),"client"in f&&n(7,c=f.client)},[i,r,s,o,a,l,u,c]}class Bm extends qe{constructor(e){super(),We(this,e,Pm,Im,Xe,{rootNode:0,root:1,target:2,theme_mode:3,version:4,autoscroll:5,max_file_size:6,client:7})}}const Nm="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='576'%20height='576'%20viewBox='0%200%20576%20576'%20fill='none'%3e%3cpath%20d='M287.5%20229L86%20344.5L287.5%20460L489%20344.5L287.5%20229Z'%20stroke='url(%23paint0_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='round'/%3e%3cpath%20d='M287.5%20116L86%20231.5L287.5%20347L489%20231.5L287.5%20116Z'%20stroke='url(%23paint1_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='round'/%3e%3cpath%20d='M86%20344L288%20229'%20stroke='url(%23paint2_linear_102_7)'%20stroke-width='59'%20stroke-linejoin='bevel'/%3e%3cdefs%3e%3clinearGradient%20id='paint0_linear_102_7'%20x1='60'%20y1='341'%20x2='429.5'%20y2='344'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint1_linear_102_7'%20x1='513.5'%20y1='231'%20x2='143.5'%20y2='231'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint2_linear_102_7'%20x1='60'%20y1='344'%20x2='428.987'%20y2='341.811'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23F9D100'/%3e%3cstop%20offset='1'%20stop-color='%23F97700'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e",Mm="data:image/svg+xml,%3csvg%20width='28'%20height='28'%20viewBox='0%200%2028%2028'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M26.9425%202.94265C27.4632%202.42195%2027.4632%201.57773%2026.9425%201.05703C26.4218%200.536329%2025.5776%200.536329%2025.0569%201.05703L22.5713%203.54256C21.1213%202.59333%2019.5367%202.43378%2018.1753%202.64006C16.5495%202.88638%2015.1127%203.66838%2014.3905%204.39053L12.3905%206.39053C12.1405%206.64058%2012%206.97972%2012%207.33334C12%207.68697%2012.1405%208.0261%2012.3905%208.27615L19.7239%2015.6095C20.2446%2016.1302%2021.0888%2016.1302%2021.6095%2015.6095L23.6095%2013.6095C24.3316%2012.8873%2025.1136%2011.4505%2025.36%209.82475C25.5663%208.46312%2025.4066%206.87827%2024.4571%205.42807L26.9425%202.94265Z'%20fill='%233c4555'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M12.276%2012.9426C12.7967%2012.4219%2012.7967%2011.5777%2012.276%2011.057C11.7553%2010.5363%2010.9111%2010.5363%2010.3904%2011.057L8.66651%2012.7809L8.27615%2012.3905C8.0261%2012.1405%207.68697%2012%207.33334%2012C6.97972%2012%206.64058%2012.1405%206.39053%2012.3905L4.39053%2014.3905C3.66838%2015.1127%202.88638%2016.5495%202.64006%2018.1753C2.43377%2019.5367%202.59333%2021.1214%203.54262%2022.5714L1.05703%2025.057C0.536329%2025.5777%200.536329%2026.4219%201.05703%2026.9426C1.57773%2027.4633%202.42195%2027.4633%202.94265%2026.9426L5.42817%2024.4571C6.87835%2025.4066%208.46315%2025.5663%209.82475%2025.36C11.4505%2025.1136%2012.8873%2024.3316%2013.6095%2023.6095L15.6095%2021.6095C16.1302%2021.0888%2016.1302%2020.2446%2015.6095%2019.7239L15.2188%2019.3332L16.9426%2017.6093C17.4633%2017.0886%2017.4633%2016.2444%2016.9426%2015.7237C16.4219%2015.203%2015.5777%2015.203%2015.057%2015.7237L13.3332%2017.4475L10.5521%2014.6665L12.276%2012.9426Z'%20fill='%23FF7C00'/%3e%3c/svg%3e",xm="data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20xmlns='http://www.w3.org/2000/svg'%3e%3c!--%20Outer%20gear%20teeth%20(gray)%20--%3e%3cpath%20d='M19.14%2012.94c.04-.3.06-.61.06-.94%200-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24%200-.43.17-.47.41l-.36%202.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47%200-.59.22L2.74%208.87c-.12.21-.08.47.12.61l2.03%201.58c-.05.3-.07.62-.07.94s.02.64.07.94l-2.03%201.58c-.18.14-.23.41-.12.61l1.92%203.32c.12.22.37.29.59.22l2.39-.96c.5.38%201.03.7%201.62.94l.36%202.54c.05.24.24.41.48.41h3.84c.24%200%20.44-.17.47-.41l.36-2.54c.59-.24%201.13-.56%201.62-.94l2.39.96c.22.08.47%200%20.59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12%2015.6c-1.98%200-3.6-1.62-3.6-3.6s1.62-3.6%203.6-3.6%203.6%201.62%203.6%203.6-1.62%203.6-3.6%203.6z'%20fill='%23808080'/%3e%3c!--%20Inner%20circle%20(now%20gray)%20--%3e%3ccircle%20cx='12'%20cy='12'%20r='2.5'%20fill='%23808080'/%3e%3c/svg%3e",Hm="data:image/svg+xml,%3csvg%20viewBox='0%200%2020%2020'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='%23000000'%3e%3cg%20id='SVGRepo_bgCarrier'%20stroke-width='0'%3e%3c/g%3e%3cg%20id='SVGRepo_tracerCarrier'%20stroke-linecap='round'%20stroke-linejoin='round'%3e%3c/g%3e%3cg%20id='SVGRepo_iconCarrier'%3e%3ctitle%3erecord%20[%23982]%3c/title%3e%3cdesc%3eCreated%20with%20Sketch.%3c/desc%3e%3cdefs%3e%3c/defs%3e%3cg%20id='Page-1'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='Dribbble-Light-Preview'%20transform='translate(-380.000000,%20-3839.000000)'%20fill='%23FF0000'%3e%3cg%20id='icons'%20transform='translate(56.000000,%20160.000000)'%3e%3cpath%20d='M338,3689%20C338,3691.209%20336.209,3693%20334,3693%20C331.791,3693%20330,3691.209%20330,3689%20C330,3686.791%20331.791,3685%20334,3685%20C336.209,3685%20338,3686.791%20338,3689%20M334,3697%20C329.589,3697%20326,3693.411%20326,3689%20C326,3684.589%20329.589,3681%20334,3681%20C338.411,3681%20342,3684.589%20342,3689%20C342,3693.411%20338.411,3697%20334,3697%20M334,3679%20C328.477,3679%20324,3683.477%20324,3689%20C324,3694.523%20328.477,3699%20334,3699%20C339.523,3699%20344,3694.523%20344,3689%20C344,3683.477%20339.523,3679%20334,3679'%20id='record-[%23982]'%3e%3c/path%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e";let tn=!1,en=null,ws=[],or=0,ln={},Cu,Yi,wn=null,ii=[];function zm(t,e,n){Cu=t,Yi=e,n&&(wn=n)}async function Um(){if(!tn)try{const t=document.title;document.title="[Sharing] Gradio Tab";const e=await navigator.mediaDevices.getDisplayMedia({video:{width:{ideal:1920},height:{ideal:1080},frameRate:{ideal:30}},audio:!0,selfBrowserSurface:"include"});document.title=t;const n={videoBitsPerSecond:5e6};en=new MediaRecorder(e,n),ws=[],ln={},en.ondataavailable=Wm,en.onstop=Zm,en.start(1e3),tn=!0,wn&&wn(!0),or=Date.now()}catch(t){Yi("Recording Error","Failed to start recording: "+t.message,"error")}}function jm(){!tn||!en||(en.stop(),tn=!1,wn&&wn(!1))}function Gm(){if(!tn)return;const t=(Date.now()-or)/1e3;ln.start=t}function Vm(){if(!tn||ln.start===void 0)return;const t=(Date.now()-or)/1e3;ln.end=t}function qm(t,e){if(!tn)return;const n=30,i=(Date.now()-or)/1e3,r=Math.floor(t?(i-2)*n:i*n);if(e.boundingBox&&e.boundingBox.topLeft&&e.boundingBox.bottomRight&&e.boundingBox.topLeft.length===2&&e.boundingBox.bottomRight.length===2){const s=e.duration||2,o=r+Math.floor(s*n);ii.some(l=>{const u=l.start_frame+Math.floor((l.duration||2)*n);return r>=l.start_frame&&r<=u||o>=l.start_frame&&o<=u||r<=l.start_frame&&o>=u})||ii.push({boundingBox:e.boundingBox,start_frame:r,duration:s})}}function aa(t,e,n=2){if(tn)try{setTimeout(()=>{if(!e||e.length===0)return;let i=1/0,r=1/0,s=0,o=0,a=!1;for(const D of e){const y=`#component-${D}`,g=document.querySelector(y);if(g){a=!0;const A=g.getBoundingClientRect();i=Math.min(i,A.left),r=Math.min(r,A.top),s=Math.max(s,A.right),o=Math.max(o,A.bottom)}}if(!a)return;const l=window.innerWidth,u=window.innerHeight,c=Math.min(s,l)-Math.max(0,i),h=Math.min(o,u)-Math.max(0,r),f=c/l,d=h/u;if(f>=.8||d>=.8)return;const m=/^((?!chrome|android).)*safari/i.test(navigator.userAgent);let p=[Math.max(0,i)/l,Math.max(0,r)/u],b=[Math.min(s,l)/l,Math.min(o,u)/u];if(m){p[0]=Math.max(0,p[0]*.9),b[0]=Math.min(1,b[0]*.9);const D=b[0]-p[0],g=(p[0]+b[0])/2*.9;p[0]=Math.max(0,g-D/2),b[0]=Math.min(1,g+D/2)}p[0]=Math.max(0,p[0]),p[1]=Math.max(0,p[1]),b[0]=Math.min(1,b[0]),b[1]=Math.min(1,b[1]),qm(t,{boundingBox:{topLeft:p,bottomRight:b},duration:n})},300)}catch{}}function Wm(t){t.data.size>0&&ws.push(t.data)}function Zm(){var n;tn=!1,wn&&wn(!1);const t=new Blob(ws,{type:"video/mp4"});Xm(t),(((n=en==null?void 0:en.stream)==null?void 0:n.getTracks())||[]).forEach(i=>i.stop())}async function Xm(t){try{Yi("Processing video","This may take a few seconds...","info");const e=new FormData;e.append("video",t,"recording.mp4"),ln.start!==void 0&&ln.end!==void 0&&(e.append("remove_segment_start",ln.start.toString()),e.append("remove_segment_end",ln.end.toString())),ii.length>0&&e.append("zoom_effects",JSON.stringify(ii));const n=await fetch(Cu+"/gradio_api/process_recording",{method:"POST",body:e});if(!n.ok)throw new Error(`Server returned ${n.status}: ${n.statusText}`);const i=await n.blob(),r=`gradio-screen-recording-${new Date().toISOString().replace(/:/g,"-").replace(/\..+/,"")}.mp4`;la(i,r),ii=[]}catch{Yi("Processing Error","Failed to process recording. Saving original version.","warning");const n=`gradio-screen-recording-${new Date().toISOString().replace(/:/g,"-").replace(/\..+/,"")}.mp4`;la(t,n)}}function la(t,e){const n=URL.createObjectURL(t),i=document.createElement("a");i.style.display="none",i.href=n,i.download=e,document.body.appendChild(i),i.click(),setTimeout(()=>{document.body.removeChild(i),URL.revokeObjectURL(n)},100)}function ua(t){return document.title=t[2],{c:pe,l:pe,m:pe,d:pe}}function ca(t){let e,n=`<style>${Gs(t[15],t[12])}</style>`,i;return{c(){e=new Ki(!1),i=fe(),this.h()},l(r){e=$i(r,!1),i=fe(),this.h()},h(){e.a=i},m(r,s){e.m(n,r,s),R(r,i,s)},p(r,s){s[0]&36864&&n!==(n=`<style>${Gs(r[15],r[12])}</style>`)&&e.p(n)},d(r){r&&(v(i),e.d())}}}function fa(t){let e,n;return e=new Bm({props:{rootNode:t[16],root:t[0],target:t[3],theme_mode:t[9],version:t[12],autoscroll:t[4],max_file_size:t[14],client:t[10]}}),e.$on("mount",t[39]),{c(){ge(e.$$.fragment)},l(i){Re(e.$$.fragment,i)},m(i,r){be(e,i,r),n=!0},p(i,r){const s={};r[0]&65536&&(s.rootNode=i[16]),r[0]&1&&(s.root=i[0]),r[0]&8&&(s.target=i[3]),r[0]&512&&(s.theme_mode=i[9]),r[0]&4096&&(s.version=i[12]),r[0]&16&&(s.autoscroll=i[4]),r[0]&16384&&(s.max_file_size=i[14]),r[0]&1024&&(s.client=i[10]),e.$set(s)},i(i){n||(B(e.$$.fragment,i),n=!0)},o(i){j(e.$$.fragment,i),n=!1},d(i){we(e,i)}}}function ha(t){let e,n,i,r=t[28]("common.built_with_gradio")+"",s,o,a,l,u,c,h,f="·",d,m,p=t[28]("common.stop_recording")+"",b,D,y,g,A,w,E,S="·",z,C,N=t[28]("common.settings")+"",re,x,le,me,ke,ue,Q,O=t[5]&&da(t);return{c(){e=Z("footer"),O&&O.c(),n=oe(),i=Z("a"),s=Ee(r),o=oe(),a=Z("img"),c=oe(),h=Z("div"),h.textContent=f,d=oe(),m=Z("button"),b=Ee(p),D=oe(),y=Z("img"),w=oe(),E=Z("div"),E.textContent=S,z=oe(),C=Z("button"),re=Ee(N),x=oe(),le=Z("img"),this.h()},l(I){e=X(I,"FOOTER",{class:!0});var L=U(e);O&&O.l(L),n=ae(L),i=X(L,"A",{href:!0,class:!0,target:!0,rel:!0});var k=U(i);s=ve(k,r),o=ae(k),a=X(k,"IMG",{src:!0,alt:!0,class:!0}),k.forEach(v),c=ae(L),h=X(L,"DIV",{class:!0,"data-svelte-h":!0}),Pn(h)!=="svelte-8g02jg"&&(h.textContent=f),d=ae(L),m=X(L,"BUTTON",{class:!0});var J=U(m);b=ve(J,p),D=ae(J),y=X(J,"IMG",{src:!0,alt:!0,class:!0}),J.forEach(v),w=ae(L),E=X(L,"DIV",{class:!0,"data-svelte-h":!0}),Pn(E)!=="svelte-1r8d4nl"&&(E.textContent=S),z=ae(L),C=X(L,"BUTTON",{class:!0});var V=U(C);re=ve(V,N),x=ae(V),le=X(V,"IMG",{src:!0,alt:!0,class:!0}),V.forEach(v),L.forEach(v),this.h()},h(){Jn(a.src,l=Nm)||_(a,"src",l),_(a,"alt",u=t[28]("common.logo")),_(a,"class","svelte-czcr5b"),_(i,"href","https://gradio.app"),_(i,"class","built-with svelte-czcr5b"),_(i,"target","_blank"),_(i,"rel","noreferrer"),_(h,"class","divider svelte-czcr5b"),G(h,"hidden",!t[27]),Jn(y.src,g=Hm)||_(y,"src",g),_(y,"alt",A=t[28]("common.stop_recording")),_(y,"class","svelte-czcr5b"),_(m,"class","record svelte-czcr5b"),G(m,"hidden",!t[27]),_(E,"class","divider svelte-czcr5b"),Jn(le.src,me=xm)||_(le,"src",me),_(le,"alt",ke=t[28]("common.settings")),_(le,"class","svelte-czcr5b"),_(C,"class","settings svelte-czcr5b"),_(e,"class","svelte-czcr5b")},m(I,L){R(I,e,L),O&&O.m(e,null),M(e,n),M(e,i),M(i,s),M(i,o),M(i,a),M(e,c),M(e,h),M(e,d),M(e,m),M(m,b),M(m,D),M(m,y),M(e,w),M(e,E),M(e,z),M(e,C),M(C,re),M(C,x),M(C,le),ue||(Q=[Te(m,"click",t[57]),Te(C,"click",t[58]),Te(C,"mouseenter",t[59])],ue=!0)},p(I,L){I[5]?O?O.p(I,L):(O=da(I),O.c(),O.m(e,n)):O&&(O.d(1),O=null),L[0]&268435456&&r!==(r=I[28]("common.built_with_gradio")+"")&&je(s,r),L[0]&268435456&&u!==(u=I[28]("common.logo"))&&_(a,"alt",u),L[0]&134217728&&G(h,"hidden",!I[27]),L[0]&268435456&&p!==(p=I[28]("common.stop_recording")+"")&&je(b,p),L[0]&268435456&&A!==(A=I[28]("common.stop_recording"))&&_(y,"alt",A),L[0]&134217728&&G(m,"hidden",!I[27]),L[0]&268435456&&N!==(N=I[28]("common.settings")+"")&&je(re,N),L[0]&268435456&&ke!==(ke=I[28]("common.settings"))&&_(le,"alt",ke)},d(I){I&&v(e),O&&O.d(),ue=!1,En(Q)}}}function da(t){let e,n,i,r,s,o,a,l="·",u,c;function h(m,p){var b;return(b=m[10].config)!=null&&b.mcp_server?Jm:Ym}let f=h(t),d=f(t);return{c(){e=Z("button"),d.c(),n=oe(),i=Z("img"),o=oe(),a=Z("div"),a.textContent=l,this.h()},l(m){e=X(m,"BUTTON",{class:!0});var p=U(e);d.l(p),n=ae(p),i=X(p,"IMG",{src:!0,alt:!0,class:!0}),p.forEach(v),o=ae(m),a=X(m,"DIV",{class:!0,"data-svelte-h":!0}),Pn(a)!=="svelte-1b2w0qd"&&(a.textContent=l),this.h()},h(){Jn(i.src,r=Mm)||_(i,"src",r),_(i,"alt",s=t[28]("common.logo")),_(i,"class","svelte-czcr5b"),_(e,"class","show-api svelte-czcr5b"),_(a,"class","divider show-api-divider svelte-czcr5b")},m(m,p){R(m,e,p),d.m(e,null),M(e,n),M(e,i),R(m,o,p),R(m,a,p),u||(c=[Te(e,"click",t[55]),Te(e,"mouseenter",t[56])],u=!0)},p(m,p){f===(f=h(m))&&d?d.p(m,p):(d.d(1),d=f(m),d&&(d.c(),d.m(e,n))),p[0]&268435456&&s!==(s=m[28]("common.logo"))&&_(i,"alt",s)},d(m){m&&(v(e),v(o),v(a)),d.d(),u=!1,En(c)}}}function Ym(t){let e=t[28]("errors.use_via_api")+"",n;return{c(){n=Ee(e)},l(i){n=ve(i,e)},m(i,r){R(i,n,r)},p(i,r){r[0]&268435456&&e!==(e=i[28]("errors.use_via_api")+"")&&je(n,e)},d(i){i&&v(n)}}}function Jm(t){let e=t[28]("errors.use_via_api_or_mcp")+"",n;return{c(){n=Ee(e)},l(i){n=ve(i,e)},m(i,r){R(i,n,r)},p(i,r){r[0]&268435456&&e!==(e=i[28]("errors.use_via_api_or_mcp")+"")&&je(n,e)},d(i){i&&v(n)}}}function pa(t){let e,n,i,r,s;var o=t[23];function a(l,u){return{props:{api_calls:l[25],dependencies:l[1]}}}return o&&(n=Dt(o,a(t))),{c(){e=Z("div"),n&&ge(n.$$.fragment),this.h()},l(l){e=X(l,"DIV",{id:!0,class:!0});var u=U(e);n&&Re(n.$$.fragment,u),u.forEach(v),this.h()},h(){_(e,"id","api-recorder-container"),_(e,"class","svelte-czcr5b")},m(l,u){R(l,e,u),n&&be(n,e,null),i=!0,r||(s=Te(e,"click",t[60]),r=!0)},p(l,u){if(u[0]&8388608&&o!==(o=l[23])){if(n){Be();const c=n;j(c.$$.fragment,1,0,()=>{we(c,1)}),Ne()}o?(n=Dt(o,a(l)),ge(n.$$.fragment),B(n.$$.fragment,1),be(n,e,null)):n=null}else if(o){const c={};u[0]&33554432&&(c.api_calls=l[25]),u[0]&2&&(c.dependencies=l[1]),n.$set(c)}},i(l){i||(n&&B(n.$$.fragment,l),i=!0)},o(l){n&&j(n.$$.fragment,l),i=!1},d(l){l&&v(e),n&&we(n),r=!1,s()}}}function ma(t){let e,n,i,r,s,o,a,l;var u=t[22];function c(h,f){return{props:{root_node:h[16],dependencies:h[1],root:h[0],app:h[10],space_id:h[11],api_calls:h[25],username:h[13]}}}return u&&(s=Dt(u,c(t)),s.$on("close",t[62])),{c(){e=Z("div"),n=Z("div"),i=oe(),r=Z("div"),s&&ge(s.$$.fragment),this.h()},l(h){e=X(h,"DIV",{class:!0});var f=U(e);n=X(f,"DIV",{class:!0}),U(n).forEach(v),i=ae(f),r=X(f,"DIV",{class:!0});var d=U(r);s&&Re(s.$$.fragment,d),d.forEach(v),f.forEach(v),this.h()},h(){_(n,"class","backdrop svelte-czcr5b"),_(r,"class","api-docs-wrap svelte-czcr5b"),_(e,"class","api-docs svelte-czcr5b")},m(h,f){R(h,e,f),M(e,n),M(e,i),M(e,r),s&&be(s,r,null),o=!0,a||(l=Te(n,"click",t[61]),a=!0)},p(h,f){if(f[0]&4194304&&u!==(u=h[22])){if(s){Be();const d=s;j(d.$$.fragment,1,0,()=>{we(d,1)}),Ne()}u?(s=Dt(u,c(h)),s.$on("close",h[62]),ge(s.$$.fragment),B(s.$$.fragment,1),be(s,r,null)):s=null}else if(u){const d={};f[0]&65536&&(d.root_node=h[16]),f[0]&2&&(d.dependencies=h[1]),f[0]&1&&(d.root=h[0]),f[0]&1024&&(d.app=h[10]),f[0]&2048&&(d.space_id=h[11]),f[0]&33554432&&(d.api_calls=h[25]),f[0]&8192&&(d.username=h[13]),s.$set(d)}},i(h){o||(s&&B(s.$$.fragment,h),o=!0)},o(h){s&&j(s.$$.fragment,h),o=!1},d(h){h&&v(e),s&&we(s),a=!1,l()}}}function _a(t){let e,n,i,r,s,o,a,l,u,c;function h(p){t[64](p)}function f(p){t[65](p)}var d=t[24];function m(p,b){let D={pwa_enabled:p[10].config.pwa,root:p[0],space_id:p[11]};return p[20]!==void 0&&(D.allow_zoom=p[20]),p[21]!==void 0&&(D.allow_video_trim=p[21]),{props:D}}return d&&(s=Dt(d,m(t)),Je.push(()=>mt(s,"allow_zoom",h)),Je.push(()=>mt(s,"allow_video_trim",f)),s.$on("close",t[66]),s.$on("start_recording",t[67])),{c(){e=Z("div"),n=Z("div"),i=oe(),r=Z("div"),s&&ge(s.$$.fragment),this.h()},l(p){e=X(p,"DIV",{class:!0});var b=U(e);n=X(b,"DIV",{class:!0}),U(n).forEach(v),i=ae(b),r=X(b,"DIV",{class:!0});var D=U(r);s&&Re(s.$$.fragment,D),D.forEach(v),b.forEach(v),this.h()},h(){_(n,"class","backdrop svelte-czcr5b"),_(r,"class","api-docs-wrap svelte-czcr5b"),_(e,"class","api-docs svelte-czcr5b")},m(p,b){R(p,e,b),M(e,n),M(e,i),M(e,r),s&&be(s,r,null),l=!0,u||(c=Te(n,"click",t[63]),u=!0)},p(p,b){if(b[0]&16777216&&d!==(d=p[24])){if(s){Be();const D=s;j(D.$$.fragment,1,0,()=>{we(D,1)}),Ne()}d?(s=Dt(d,m(p)),Je.push(()=>mt(s,"allow_zoom",h)),Je.push(()=>mt(s,"allow_video_trim",f)),s.$on("close",p[66]),s.$on("start_recording",p[67]),ge(s.$$.fragment),B(s.$$.fragment,1),be(s,r,null)):s=null}else if(d){const D={};b[0]&1024&&(D.pwa_enabled=p[10].config.pwa),b[0]&1&&(D.root=p[0]),b[0]&2048&&(D.space_id=p[11]),!o&&b[0]&1048576&&(o=!0,D.allow_zoom=p[20],xt(()=>o=!1)),!a&&b[0]&2097152&&(a=!0,D.allow_video_trim=p[21],xt(()=>a=!1)),s.$set(D)}},i(p){l||(s&&B(s.$$.fragment,p),l=!0)},o(p){s&&j(s.$$.fragment,p),l=!1},d(p){p&&v(e),s&&we(s),u=!1,c()}}}function ga(t){let e,n;return e=new Am({props:{messages:t[26]}}),e.$on("close",t[38]),{c(){ge(e.$$.fragment)},l(i){Re(e.$$.fragment,i)},m(i,r){be(e,i,r),n=!0},p(i,r){const s={};r[0]&67108864&&(s.messages=i[26]),e.$set(s)},i(i){n||(B(e.$$.fragment,i),n=!0)},o(i){j(e.$$.fragment,i),n=!1},d(i){we(e,i)}}}function Qm(t){let e,n,i,r,s,o,a,l,u,c,h,f,d=t[7]&&ua(t),m=t[15]&&ca(t),p=t[16]&&t[10].config&&fa(t),b=t[6]&&ha(t),D=t[19]&&t[23]&&pa(t),y=t[17]&&t[16]&&t[22]&&ma(t),g=t[18]&&t[16]&&t[10].config&&t[24]&&_a(t),A=t[26]&&ga(t);return{c(){d&&d.c(),e=fe(),m&&m.c(),n=fe(),i=oe(),r=Z("div"),s=Z("div"),p&&p.c(),o=oe(),b&&b.c(),a=oe(),D&&D.c(),l=oe(),y&&y.c(),u=oe(),g&&g.c(),c=oe(),A&&A.c(),h=fe(),this.h()},l(w){const E=Ua("svelte-eiigye",document.head);d&&d.l(E),e=fe(),m&&m.l(E),n=fe(),E.forEach(v),i=ae(w),r=X(w,"DIV",{class:!0});var S=U(r);s=X(S,"DIV",{class:!0});var z=U(s);p&&p.l(z),z.forEach(v),o=ae(S),b&&b.l(S),S.forEach(v),a=ae(w),D&&D.l(w),l=ae(w),y&&y.l(w),u=ae(w),g&&g.l(w),c=ae(w),A&&A.l(w),h=fe(),this.h()},h(){_(s,"class","contain svelte-czcr5b"),Y(s,"flex-grow",t[8]?"1":"auto"),_(r,"class","wrap svelte-czcr5b"),Y(r,"min-height",t[8]?"100%":"auto")},m(w,E){d&&d.m(document.head,null),M(document.head,e),m&&m.m(document.head,null),M(document.head,n),R(w,i,E),R(w,r,E),M(r,s),p&&p.m(s,null),M(r,o),b&&b.m(r,null),R(w,a,E),D&&D.m(w,E),R(w,l,E),y&&y.m(w,E),R(w,u,E),g&&g.m(w,E),R(w,c,E),A&&A.m(w,E),R(w,h,E),f=!0},p(w,E){w[7]?d||(d=ua(w),d.c(),d.m(e.parentNode,e)):d&&(d.d(1),d=null),w[15]?m?m.p(w,E):(m=ca(w),m.c(),m.m(n.parentNode,n)):m&&(m.d(1),m=null),w[16]&&w[10].config?p?(p.p(w,E),E[0]&66560&&B(p,1)):(p=fa(w),p.c(),B(p,1),p.m(s,null)):p&&(Be(),j(p,1,1,()=>{p=null}),Ne()),E[0]&256&&Y(s,"flex-grow",w[8]?"1":"auto"),w[6]?b?b.p(w,E):(b=ha(w),b.c(),b.m(r,null)):b&&(b.d(1),b=null),E[0]&256&&Y(r,"min-height",w[8]?"100%":"auto"),w[19]&&w[23]?D?(D.p(w,E),E[0]&8912896&&B(D,1)):(D=pa(w),D.c(),B(D,1),D.m(l.parentNode,l)):D&&(Be(),j(D,1,1,()=>{D=null}),Ne()),w[17]&&w[16]&&w[22]?y?(y.p(w,E),E[0]&4390912&&B(y,1)):(y=ma(w),y.c(),B(y,1),y.m(u.parentNode,u)):y&&(Be(),j(y,1,1,()=>{y=null}),Ne()),w[18]&&w[16]&&w[10].config&&w[24]?g?(g.p(w,E),E[0]&17105920&&B(g,1)):(g=_a(w),g.c(),B(g,1),g.m(c.parentNode,c)):g&&(Be(),j(g,1,1,()=>{g=null}),Ne()),w[26]?A?(A.p(w,E),E[0]&67108864&&B(A,1)):(A=ga(w),A.c(),B(A,1),A.m(h.parentNode,h)):A&&(Be(),j(A,1,1,()=>{A=null}),Ne())},i(w){f||(B(p),B(D),B(y),B(g),B(A),f=!0)},o(w){j(p),j(D),j(y),j(g),j(A),f=!1},d(w){w&&(v(i),v(r),v(a),v(l),v(u),v(c),v(h)),d&&d.d(w),v(e),m&&m.d(w),v(n),p&&p.d(),b&&b.d(),D&&D.d(w),y&&y.d(w),g&&g.d(w),A&&A.d(w)}}}const Km=/^'([^]+)'$/,Pr="Connection to the server was lost. Attempting reconnection...",$m="Reconnected to server, but the server has changed. You may need to <a href=''>refresh the page</a>.",e_="Connection re-established.",t_="Session not found - this is likely because the machine you were connected to has changed. <a href=''>Refresh the page</a> to continue.",n_=15,i_=10;function ba(t){return"detail"in t}function r_(t,e,n){let i,r,s,o,a,l;Rt(t,ui,T=>n(28,a=T));let{root:u}=e,{components:c}=e,{layout:h}=e,{dependencies:f}=e,{title:d="Gradio"}=e,{target:m}=e,{autoscroll:p}=e,{show_api:b=!0}=e,{show_footer:D=!0}=e,{control_page_title:y=!1}=e,{app_mode:g}=e,{theme_mode:A}=e,{app:w}=e,{space_id:E}=e,{version:S}=e,{js:z}=e,{fill_height:C=!1}=e,{ready:N}=e,{username:re}=e,{api_prefix:x=""}=e,{max_file_size:le=void 0}=e,{initial_layout:me=void 0}=e,{css:ke=null}=e,ue=!1,{layout:Q,targets:O,update_value:I,get_data:L,modify_stream:k,get_stream_state:J,set_time_limit:V,loading_status:$,scheduled_updates:q,create_layout:Ce,rerender_layout:Ze,value_change:he}=hl({initial_layout:me});Rt(t,Q,T=>n(16,l=T)),Rt(t,O,T=>n(74,s=T)),Rt(t,$,T=>n(54,r=T)),Rt(t,q,T=>n(75,o=T));let Me=f;async function it(){var T;await xl(((T=w.config)==null?void 0:T.i18n_translations)||void 0),n(53,Ye=!0),await Ce({components:c,layout:h,dependencies:f,root:u+x,app:w,options:{fill_height:C}}),n(53,Ye=!1)}let{search_params:$e}=e,ct=$e.get("view")==="api"&&b,et=$e.get("view")==="settings",st=$e.get("view")==="api-recorder"&&b,ye=!0,lt=!0,yt=null,gt=null,xe=null;async function dt(){if(!yt||!gt){const T=await P(()=>import("./ApiDocs.DrqKfr4o.js"),__vite__mapDeps([181,182,183]),import.meta.url),ie=await P(()=>import("./ApiRecorder.r4QAwWei.js"),__vite__mapDeps([184,185]),import.meta.url);yt||n(22,yt=T.default),gt||n(23,gt=ie.default)}}async function ht(){if(!gt){const T=await P(()=>import("./ApiRecorder.r4QAwWei.js"),__vite__mapDeps([184,185]),import.meta.url);n(23,gt=T.default)}}async function tt(){if(!xe){const T=await P(()=>import("./Settings.BXvpy3uo.js"),__vite__mapDeps([186,182,82,63,83,43,44,187,22]),import.meta.url);n(24,xe=T.default)}}async function H(T){n(19,st=!1),T&&await dt(),n(17,ct=T);let ie=new URLSearchParams(window.location.search);T?ie.set("view","api"):ie.delete("view"),history.replaceState(null,"","?"+ie.toString())}async function De(T){T&&await tt();let ie=new URLSearchParams(window.location.search);T?ie.set("view","settings"):ie.delete("view"),history.replaceState(null,"","?"+ie.toString()),n(18,et=!et)}let Ie=[],Ye=!1,{render_complete:bt=!1}=e;async function kt(T,ie){var Nt;const ne=f.find(at=>at.id===ie),de=(Nt=c.find(at=>at.id===(ne==null?void 0:ne.inputs[0])))==null?void 0:Nt.type;if(ye&&ne&&de!=="dataset"&&(ne&&ne.inputs&&ne.inputs.length>0&&i&&aa(!0,ne.inputs,1),ne&&ne.outputs&&ne.outputs.length>0&&i&&aa(!1,ne.outputs,2)),!ne)return;const te=ne.outputs,He=T==null?void 0:T.map((at,wt)=>({id:te[wt],prop:"value_is_output",value:!0}));I(He),await on();const ot=[];T==null||T.forEach((at,wt)=>{if(typeof at=="object"&&at!==null&&at.__type__==="update")for(const[_e,nt]of Object.entries(at))_e!=="__type__"&&ot.push({id:te[wt],prop:_e,value:nt});else ot.push({id:te[wt],prop:"value",value:at})}),I(ot),await on()}let Ge=new Map,se=[];function Ae(T,ie,ne,de,te=10,He=!0){return{title:T,message:ie,fn_index:ne,type:de,id:++hn,duration:te,visible:He}}function Tt(T,ie,ne){n(26,se=[Ae(T,ie,-1,ne),...se])}let hn=-1;const Gn=a("blocks.long_requests_queue"),ar=a("blocks.connection_can_break"),Xt=a("blocks.waiting_for_inputs");let hi=!1,di=!1,ft=!1,Ct=[];function Yt(T,ie=null,ne=null){let de=()=>{};function te(){de()}o?de=q.subscribe(He=>{He||on().then(()=>{kn(T,ie,ne),te()})}):kn(T,ie,ne)}async function pi(T,ie,ne){return T===ie&&ne&&ne.is_value_data===!0?ne.value:L(T)}async function kn(T,ie=null,ne=null){const de=f.find(_e=>_e.id===T);if(de===void 0)return;const te=de;if(Ct.length>0){for(const _e of Ct)if(te.inputs.includes(_e)){Tt("Warning",Xt,"warning");return}}const He=$.get_status_for_fn(T);n(26,se=se.filter(({fn_index:_e})=>_e!==T)),(He==="pending"||He==="generating")&&(te.pending_request=!0);let ot={fn_index:T,data:await Promise.all(te.inputs.map(_e=>pi(_e,ie,ne))),event_data:te.collects_event_data?ne:null,trigger_id:ie};te.frontend_fn&&typeof te.frontend_fn!="boolean"?te.frontend_fn(ot.data.concat(await Promise.all(te.outputs.map(_e=>L(_e))))).then(_e=>{te.backend_fn?(ot.data=_e,Nt(te,ot)):kt(_e,T)}):te.types.cancel&&te.cancels?await Promise.all(te.cancels.map(async _e=>{const nt=Ge.get(_e);return nt==null||nt.cancel(),nt})):te.backend_fn&&(te.js_implementation&&new qr(`let result = await (${te.js_implementation})(...arguments);
						return (!Array.isArray(result)) ? [result] : result;`)(...ot.data).then(nt=>{kt(nt,T),ot.js_implementation=!0}).catch(nt=>{console.error(nt),ot.js_implementation=!1}),Nt(te,ot));function Nt(_e,nt){_e.trigger_mode==="once"?_e.pending_request||wt(nt,_e.connection=="stream"):_e.trigger_mode==="multiple"?wt(nt,_e.connection=="stream"):_e.trigger_mode==="always_last"&&(_e.pending_request?_e.final_event=nt:wt(nt,_e.connection=="stream"))}async function at(){const _e=await w.reconnect();_e==="broken"?setTimeout(at,1e3):_e==="changed"?(ue=!1,n(26,se=[Ae("Changed Connection",$m,-1,"info",3,!0),...se.map(nt=>nt.message===Pr?{...nt,visible:!1}:nt)])):_e==="connected"&&(ue=!1,n(26,se=[Ae("Reconnected",e_,-1,"success",null,!0),...se.map(nt=>nt.message===Pr?{...nt,visible:!1}:nt)]))}async function wt(_e,nt=!1){lt&&Gm(),st&&n(25,Ie=[...Ie,JSON.parse(JSON.stringify(_e))]);let hr;if(w.set_current_payload(_e),nt)if(!Ge.has(T))te.inputs.forEach(Ve=>k(Ve,"waiting"));else{if(Ge.has(T)&&te.inputs.some(Ve=>J(Ve)==="waiting"))return;if(Ge.has(T)&&te.inputs.some(Ve=>J(Ve)==="open")){await w.send_ws_message(`${w.config.root+w.config.api_prefix}/stream/${Ge.get(T).event_id()}`,{..._e,session_hash:w.session_hash});return}}try{hr=w.submit(_e.fn_index,_e.data,_e.event_data,_e.trigger_id)}catch(Ve){if(w.closed)return;n(26,se=[Ae("Error",String(Ve),0,"error"),...se]),$.update({status:"error",fn_index:0,eta:0,queue:!1,queue_position:null}),St(r);return}Ge.set(T,hr);for await(const Ve of hr){if(_e.js_implementation)return;Ve.type==="data"?Iu(Ve):Ve.type==="render"?Pu(Ve):Ve.type==="status"?Mu(Ve):Ve.type==="log"&&Bu(Ve)}function Iu(Ve){const{data:pt,fn_index:Ue}=Ve;te.pending_request&&te.final_event&&(te.pending_request=!1,wt(te.final_event,te.connection=="stream")),te.pending_request=!1,kt(pt,Ue),St(r)}function Pu(Ve){const{data:pt}=Ve;let Ue=pt.components,Sn=pt.layout,ut=pt.dependencies,Jt=pt.render_id,Qt=[];f.forEach((Lt,yi)=>{Lt.rendered_in===te.render_id&&Qt.push(yi)}),Qt.reverse().forEach(Lt=>{f.splice(Lt,1)}),ut.forEach(Lt=>{f.push(Lt)}),Ze({components:Ue,layout:Sn,root:u+x,dependencies:f,render_id:Jt}),ut.forEach(Lt=>{Lt.targets.some(yi=>yi[1]==="load")&&Yt(Lt.id)})}function Bu(Ve){const{title:pt,log:Ue,fn_index:Sn,level:ut,duration:Jt,visible:Qt}=Ve;n(26,se=[Ae(pt,Ue,Sn,ut,Jt,Qt),...se])}function Nu(Ve,pt,Ue){Ve.original_msg==="process_starts"&&Ue.connection==="stream"&&k(pt,"open")}function Mu(Ve){var Sn;Ve.broken&&!ue&&(n(26,se=[Ae("Broken Connection",Pr,-1,"error",null,!0),...se]),ue=!0,setTimeout(at,1e3)),Ve.session_not_found&&n(26,se=[Ae("Session Not Found",t_,-1,"error",null,!0),...se]);const{fn_index:pt,...Ue}=Ve;if(Ue.stage==="streaming"&&Ue.time_limit&&te.inputs.forEach(ut=>{V(ut,Ue.time_limit)}),te.inputs.forEach(ut=>{Nu(Ve,ut,te)}),$.update({...Ue,time_limit:Ue.time_limit,status:Ue.stage,progress:Ue.progress_data,fn_index:pt}),St(r),!di&&E!==null&&Ue.position!==void 0&&Ue.position>=2&&Ue.eta!==void 0&&Ue.eta>n_&&(di=!0,n(26,se=[Ae("Warning",Gn,pt,"warning"),...se])),!ft&&hi&&Ue.eta!==void 0&&Ue.eta>i_&&(ft=!0,n(26,se=[Ae("Warning",ar,pt,"warning"),...se])),Ue.stage==="complete"||Ue.stage==="generating"){const ut=new Set;(Sn=Ue.changed_state_ids)==null||Sn.forEach(Jt=>{f.filter(Qt=>Qt.targets.some(([Lt,yi])=>Lt===Jt)).forEach(Qt=>{ut.add(Qt)})}),ut.forEach(Jt=>{Yt(Jt.id,_e.trigger_id)})}if(Ue.stage==="complete"&&(f.forEach(async ut=>{ut.trigger_after===pt&&Yt(ut.id,_e.trigger_id)}),te.inputs.forEach(ut=>{k(ut,"closed")}),Ge.delete(T)),Ue.stage==="error"&&!ue&&!Ve.session_not_found){if(Ue.message){const ut=Ue.message.replace(Km,(Qt,Lt)=>Lt),Jt=Ue.title??"Error";n(26,se=[Ae(Jt,ut,pt,"error",Ue.duration,Ue.visible),...se])}f.map(async ut=>{ut.trigger_after===pt&&!ut.trigger_only_on_success&&Yt(ut.id,_e.trigger_id)})}}lt&&Vm()}}function mi(T,ie){if(E===null)return;const ne=new URL(`https://huggingface.co/spaces/${E}/discussions/new`);T!==void 0&&T.length>0&&ne.searchParams.set("title",T),ne.searchParams.set("description",ie),window.open(ne.toString(),"_blank")}function _i(T){const ie=T.detail;n(26,se=se.filter(ne=>ne.id!==ie))}const lr=T=>!!(T&&new URL(T,location.href).origin!==location.origin);async function An(){z&&await new qr(`let result = await (${z})();
					return (!Array.isArray(result)) ? [result] : result;`)(),await on();for(var T=m.getElementsByTagName("a"),ie=0;ie<T.length;ie++){const ne=T[ie].getAttribute("target"),de=T[ie].getAttribute("href");lr(de)&&ne!=="_blank"&&T[ie].setAttribute("target","_blank")}Fn(),!(!m||bt)&&(m.addEventListener("prop_change",ne=>{if(!ba(ne))throw new Error("not a custom event");const{id:de,prop:te,value:He}=ne.detail;I([{id:de,prop:te,value:He}]),te==="input_ready"&&He===!1&&Ct.push(de),te==="input_ready"&&He===!0&&(Ct=Ct.filter(ot=>ot!==de))}),m.addEventListener("gradio",ne=>{var ot,Nt;if(!ba(ne))throw new Error("not a custom event");const{id:de,event:te,data:He}=ne.detail;if(te==="share"){const{title:at,description:wt}=He;mi(at,wt)}else if(te==="error")n(26,se=[Ae("Error",He,-1,te),...se]);else if(te==="warning")n(26,se=[Ae("Warning",He,-1,te),...se]);else if(te==="info")n(26,se=[Ae("Info",He,-1,te),...se]);else if(te=="clear_status")ur(de,"complete",He);else if(te=="close_stream"){const at=(ot=s[de])==null?void 0:ot[He];at==null||at.forEach(wt=>{if(Ge.has(wt)){const _e=`${w.config.root+w.config.api_prefix}/stream/${Ge.get(wt).event_id()}`;w.post_data(`${_e}/close`,{}),w.close_ws(_e)}})}else{const at=(Nt=s[de])==null?void 0:Nt[te];at==null||at.forEach(wt=>{requestAnimationFrame(()=>{Yt(wt,de,He)})})}}),n(42,bt=!0))}he((T,ie)=>{var de;const ne=(de=s[T])==null?void 0:de.change;ne==null||ne.forEach(te=>{requestAnimationFrame(()=>{Yt(te,T,ie)})})});const Fn=()=>{f.forEach(T=>{T.targets.some(ie=>ie[1]==="load")&&Yt(T.id)})};function ur(T,ie,ne){ne.status=ie,I([{id:T,prop:"loading_status",value:ne}])}function St(T){let ie=[];Object.entries(T).forEach(([te,He])=>{if(w.closed&&He.status==="error")return;let ot=f.find(Nt=>Nt.id==He.fn_index);ot!==void 0&&(He.scroll_to_output=ot.scroll_to_output,He.show_progress=ot.show_progress,ie.push({id:parseInt(te),prop:"loading_status",value:He}))});const ne=$.get_inputs_to_update(),de=Array.from(ne).map(([te,He])=>({id:te,prop:"pending",value:He==="pending"}));I([...ie,...de])}let dn=Zt(!1);Rt(t,dn,T=>n(27,i=T)),gn(()=>{hi=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),zm(u,(T,ie,ne)=>{Tt(T,ie,ne)},T=>{Zu(dn,i=T,i)}),ct&&dt(),st&&ht(),et&&tt()});function Tn(){i?jm():Um()}const gi=()=>{H(!ct)},cr=()=>{dt(),ht()},Cn=()=>{Tn()},Bt=()=>{De(!et)},bi=()=>{tt()},wi=()=>{H(!0),n(19,st=!1)},Ei=()=>{H(!1)},vi=T=>{var ie;H(!1),n(25,Ie=[]),n(19,st=n(19,st=(ie=T.detail)==null?void 0:ie.api_recorder_visible))},fr=()=>{De(!1)};function ce(T){ye=T,n(20,ye)}function F(T){lt=T,n(21,lt)}const W=()=>{De(!1)},ee=()=>{Tn()};return t.$$set=T=>{"root"in T&&n(0,u=T.root),"components"in T&&n(44,c=T.components),"layout"in T&&n(45,h=T.layout),"dependencies"in T&&n(1,f=T.dependencies),"title"in T&&n(2,d=T.title),"target"in T&&n(3,m=T.target),"autoscroll"in T&&n(4,p=T.autoscroll),"show_api"in T&&n(5,b=T.show_api),"show_footer"in T&&n(6,D=T.show_footer),"control_page_title"in T&&n(7,y=T.control_page_title),"app_mode"in T&&n(8,g=T.app_mode),"theme_mode"in T&&n(9,A=T.theme_mode),"app"in T&&n(10,w=T.app),"space_id"in T&&n(11,E=T.space_id),"version"in T&&n(12,S=T.version),"js"in T&&n(46,z=T.js),"fill_height"in T&&n(47,C=T.fill_height),"ready"in T&&n(43,N=T.ready),"username"in T&&n(13,re=T.username),"api_prefix"in T&&n(48,x=T.api_prefix),"max_file_size"in T&&n(14,le=T.max_file_size),"initial_layout"in T&&n(49,me=T.initial_layout),"css"in T&&n(15,ke=T.css),"search_params"in T&&n(50,$e=T.search_params),"render_complete"in T&&n(42,bt=T.render_complete)},t.$$.update=()=>{t.$$.dirty[0]&1035|t.$$.dirty[1]&90112&&it(),t.$$.dirty[0]&65536&&n(43,N=!!l),t.$$.dirty[0]&2|t.$$.dirty[1]&6293504&&f!==Me&&bt&&!Ye&&(Fn(),n(52,Me=f)),t.$$.dirty[1]&8388608&&St(r)},[u,f,d,m,p,b,D,y,g,A,w,E,S,re,le,ke,l,ct,et,st,ye,lt,yt,gt,xe,Ie,se,i,a,Q,O,$,q,dt,ht,tt,H,De,_i,An,dn,Tn,bt,N,c,h,z,C,x,me,$e,Tt,Me,Ye,r,gi,cr,Cn,Bt,bi,wi,Ei,vi,fr,ce,F,W,ee]}class s_ extends qe{constructor(e){super(),We(this,e,r_,Qm,Xe,{root:0,components:44,layout:45,dependencies:1,title:2,target:3,autoscroll:4,show_api:5,show_footer:6,control_page_title:7,app_mode:8,theme_mode:9,app:10,space_id:11,version:12,js:46,fill_height:47,ready:43,username:13,api_prefix:48,max_file_size:14,initial_layout:49,css:15,search_params:50,render_complete:42,add_new_message:51},null,[-1,-1,-1,-1])}get add_new_message(){return this.$$.ctx[51]}}function o_(t){let e,n;const i=t[4].default,r=Ut(i,t,t[3],null);return{c(){e=Z("div"),r&&r.c(),this.h()},l(s){e=X(s,"DIV",{class:!0});var o=U(e);r&&r.l(o),o.forEach(v),this.h()},h(){_(e,"class","form svelte-633qhp"),G(e,"hidden",!t[0]),Y(e,"flex-grow",t[1]),Y(e,"min-width",`calc(min(${t[2]}px, 100%))`)},m(s,o){R(s,e,o),r&&r.m(e,null),n=!0},p(s,[o]){r&&r.p&&(!n||o&8)&&jt(r,i,s,s[3],n?Vt(i,s[3],o,null):Gt(s[3]),null),(!n||o&1)&&G(e,"hidden",!s[0]),o&2&&Y(e,"flex-grow",s[1]),o&4&&Y(e,"min-width",`calc(min(${s[2]}px, 100%))`)},i(s){n||(B(r,s),n=!0)},o(s){j(r,s),n=!1},d(s){s&&v(e),r&&r.d(s)}}}function a_(t,e,n){let{$$slots:i={},$$scope:r}=e,{visible:s=!0}=e,{scale:o=null}=e,{min_width:a=0}=e;return t.$$set=l=>{"visible"in l&&n(0,s=l.visible),"scale"in l&&n(1,o=l.scale),"min_width"in l&&n(2,a=l.min_width),"$$scope"in l&&n(3,r=l.$$scope)},[s,o,a,r,i]}let Su=class extends qe{constructor(e){super(),We(this,e,a_,o_,Xe,{visible:0,scale:1,min_width:2})}};const l_=Object.freeze(Object.defineProperty({__proto__:null,default:Su},Symbol.toStringTag,{value:"Module"}));function wa(t){let e,n,i,r;const s=[c_,u_],o=[];function a(l,u){return l[19]?0:1}return e=a(t),n=o[e]=s[e](t),{c(){n.c(),i=fe()},l(l){n.l(l),i=fe()},m(l,u){o[e].m(l,u),R(l,i,u),r=!0},p(l,u){let c=e;e=a(l),e===c?o[e].p(l,u):(Be(),j(o[c],1,1,()=>{o[c]=null}),Ne(),n=o[e],n?n.p(l,u):(n=o[e]=s[e](l),n.c()),B(n,1),n.m(i.parentNode,i))},i(l){r||(B(n),r=!0)},o(l){j(n),r=!1},d(l){l&&v(i),o[e].d(l)}}}function u_(t){let e,n,i,r,s;return n=new bp({}),{c(){e=Z("button"),ge(n.$$.fragment),this.h()},l(o){e=X(o,"BUTTON",{class:!0,"aria-label":!0,"aria-roledescription":!0});var a=U(e);Re(n.$$.fragment,a),a.forEach(v),this.h()},h(){_(e,"class","copy-button svelte-173056l"),_(e,"aria-label","Copy"),_(e,"aria-roledescription","Copy text")},m(o,a){R(o,e,a),be(n,e,null),i=!0,r||(s=Te(e,"click",t[21]),r=!0)},p:pe,i(o){i||(B(n.$$.fragment,o),i=!0)},o(o){j(n.$$.fragment,o),i=!1},d(o){o&&v(e),we(n),r=!1,s()}}}function c_(t){let e,n,i,r;return n=new pp({}),{c(){e=Z("button"),ge(n.$$.fragment),this.h()},l(s){e=X(s,"BUTTON",{class:!0,"aria-label":!0,"aria-roledescription":!0});var o=U(e);Re(n.$$.fragment,o),o.forEach(v),this.h()},h(){_(e,"class","copy-button svelte-173056l"),_(e,"aria-label","Copied"),_(e,"aria-roledescription","Text copied")},m(s,o){R(s,e,o),be(n,e,null),r=!0},p:pe,i(s){r||(B(n.$$.fragment,s),s&&(i||Ma(()=>{i=xa(e,Br,{duration:300}),i.start()})),r=!0)},o(s){j(n.$$.fragment,s),r=!1},d(s){s&&v(e),we(n)}}}function f_(t){let e;return{c(){e=Ee(t[3])},l(n){e=ve(n,t[3])},m(n,i){R(n,e,i)},p(n,i){i[0]&8&&je(e,n[3])},d(n){n&&v(e)}}}function h_(t){let e,n,i,r,s,o,a,l,u,c,h,f,d;return{c(){e=Z("textarea"),this.h()},l(m){e=X(m,"TEXTAREA",{"data-testid":!0,dir:!0,placeholder:!0,rows:!0,maxlength:!0,style:!0,autocapitalize:!0,autocorrect:!0,spellcheck:!0,autocomplete:!0,tabindex:!0,enterkeyhint:!0,lang:!0,class:!0}),U(e).forEach(v),this.h()},h(){var m,p,b,D,y,g,A;_(e,"data-testid","textbox"),_(e,"dir",n=t[12]?"rtl":"ltr"),_(e,"placeholder",t[2]),_(e,"rows",t[1]),e.disabled=t[5],e.autofocus=t[13],_(e,"maxlength",t[15]),_(e,"style",i=t[14]?"text-align: "+t[14]:""),_(e,"autocapitalize",r=(m=t[16])==null?void 0:m.autocapitalize),_(e,"autocorrect",s=(p=t[16])==null?void 0:p.autocorrect),_(e,"spellcheck",o=(b=t[16])==null?void 0:b.spellcheck),_(e,"autocomplete",a=(D=t[16])==null?void 0:D.autocomplete),_(e,"tabindex",l=(y=t[16])==null?void 0:y.tabindex),_(e,"enterkeyhint",u=(g=t[16])==null?void 0:g.enterkeyhint),_(e,"lang",c=(A=t[16])==null?void 0:A.lang),_(e,"class","svelte-173056l"),G(e,"no-label",!t[6]&&(t[10]||t[11]))},m(m,p){R(m,e,p),cn(e,t[0]),t[46](e),t[13]&&e.focus(),f||(d=[Yu(h=t[27].call(null,e,t[0])),Te(e,"input",t[45]),Te(e,"keypress",t[23]),Te(e,"blur",t[37]),Te(e,"select",t[22]),Te(e,"focus",t[38]),Te(e,"scroll",t[24])],f=!0)},p(m,p){var b,D,y,g,A,w,E;p[0]&4096&&n!==(n=m[12]?"rtl":"ltr")&&_(e,"dir",n),p[0]&4&&_(e,"placeholder",m[2]),p[0]&2&&_(e,"rows",m[1]),p[0]&32&&(e.disabled=m[5]),p[0]&8192&&(e.autofocus=m[13]),p[0]&32768&&_(e,"maxlength",m[15]),p[0]&16384&&i!==(i=m[14]?"text-align: "+m[14]:"")&&_(e,"style",i),p[0]&65536&&r!==(r=(b=m[16])==null?void 0:b.autocapitalize)&&_(e,"autocapitalize",r),p[0]&65536&&s!==(s=(D=m[16])==null?void 0:D.autocorrect)&&_(e,"autocorrect",s),p[0]&65536&&o!==(o=(y=m[16])==null?void 0:y.spellcheck)&&_(e,"spellcheck",o),p[0]&65536&&a!==(a=(g=m[16])==null?void 0:g.autocomplete)&&_(e,"autocomplete",a),p[0]&65536&&l!==(l=(A=m[16])==null?void 0:A.tabindex)&&_(e,"tabindex",l),p[0]&65536&&u!==(u=(w=m[16])==null?void 0:w.enterkeyhint)&&_(e,"enterkeyhint",u),p[0]&65536&&c!==(c=(E=m[16])==null?void 0:E.lang)&&_(e,"lang",c),h&&Ju(h.update)&&p[0]&1&&h.update.call(null,m[0]),p[0]&1&&cn(e,m[0]),p[0]&3136&&G(e,"no-label",!m[6]&&(m[10]||m[11]))},d(m){m&&v(e),t[46](null),f=!1,En(d)}}}function d_(t){let e;function n(s,o){if(s[8]==="text")return __;if(s[8]==="password")return m_;if(s[8]==="email")return p_}let i=n(t),r=i&&i(t);return{c(){r&&r.c(),e=fe()},l(s){r&&r.l(s),e=fe()},m(s,o){r&&r.m(s,o),R(s,e,o)},p(s,o){i===(i=n(s))&&r?r.p(s,o):(r&&r.d(1),r=i&&i(s),r&&(r.c(),r.m(e.parentNode,e)))},d(s){s&&v(e),r&&r.d(s)}}}function p_(t){let e,n,i,r,s,o,a,l,u;return{c(){e=Z("input"),this.h()},l(c){e=X(c,"INPUT",{"data-testid":!0,type:!0,class:!0,placeholder:!0,maxlength:!0,autocomplete:!0,autocapitalize:!0,autocorrect:!0,spellcheck:!0,tabindex:!0,enterkeyhint:!0,lang:!0}),this.h()},h(){var c,h,f,d,m,p;_(e,"data-testid","textbox"),_(e,"type","email"),_(e,"class","scroll-hide svelte-173056l"),_(e,"placeholder",t[2]),e.disabled=t[5],e.autofocus=t[13],_(e,"maxlength",t[15]),_(e,"autocomplete","email"),_(e,"autocapitalize",n=(c=t[16])==null?void 0:c.autocapitalize),_(e,"autocorrect",i=(h=t[16])==null?void 0:h.autocorrect),_(e,"spellcheck",r=(f=t[16])==null?void 0:f.spellcheck),_(e,"tabindex",s=(d=t[16])==null?void 0:d.tabindex),_(e,"enterkeyhint",o=(m=t[16])==null?void 0:m.enterkeyhint),_(e,"lang",a=(p=t[16])==null?void 0:p.lang)},m(c,h){R(c,e,h),cn(e,t[0]),t[44](e),t[13]&&e.focus(),l||(u=[Te(e,"input",t[43]),Te(e,"keypress",t[23]),Te(e,"blur",t[35]),Te(e,"select",t[22]),Te(e,"focus",t[36])],l=!0)},p(c,h){var f,d,m,p,b,D;h[0]&4&&_(e,"placeholder",c[2]),h[0]&32&&(e.disabled=c[5]),h[0]&8192&&(e.autofocus=c[13]),h[0]&32768&&_(e,"maxlength",c[15]),h[0]&65536&&n!==(n=(f=c[16])==null?void 0:f.autocapitalize)&&_(e,"autocapitalize",n),h[0]&65536&&i!==(i=(d=c[16])==null?void 0:d.autocorrect)&&_(e,"autocorrect",i),h[0]&65536&&r!==(r=(m=c[16])==null?void 0:m.spellcheck)&&_(e,"spellcheck",r),h[0]&65536&&s!==(s=(p=c[16])==null?void 0:p.tabindex)&&_(e,"tabindex",s),h[0]&65536&&o!==(o=(b=c[16])==null?void 0:b.enterkeyhint)&&_(e,"enterkeyhint",o),h[0]&65536&&a!==(a=(D=c[16])==null?void 0:D.lang)&&_(e,"lang",a),h[0]&1&&e.value!==c[0]&&cn(e,c[0])},d(c){c&&v(e),t[44](null),l=!1,En(u)}}}function m_(t){let e,n,i,r,s,o,a,l,u;return{c(){e=Z("input"),this.h()},l(c){e=X(c,"INPUT",{"data-testid":!0,type:!0,class:!0,placeholder:!0,maxlength:!0,autocomplete:!0,autocapitalize:!0,autocorrect:!0,spellcheck:!0,tabindex:!0,enterkeyhint:!0,lang:!0}),this.h()},h(){var c,h,f,d,m,p;_(e,"data-testid","password"),_(e,"type","password"),_(e,"class","scroll-hide svelte-173056l"),_(e,"placeholder",t[2]),e.disabled=t[5],e.autofocus=t[13],_(e,"maxlength",t[15]),_(e,"autocomplete",""),_(e,"autocapitalize",n=(c=t[16])==null?void 0:c.autocapitalize),_(e,"autocorrect",i=(h=t[16])==null?void 0:h.autocorrect),_(e,"spellcheck",r=(f=t[16])==null?void 0:f.spellcheck),_(e,"tabindex",s=(d=t[16])==null?void 0:d.tabindex),_(e,"enterkeyhint",o=(m=t[16])==null?void 0:m.enterkeyhint),_(e,"lang",a=(p=t[16])==null?void 0:p.lang)},m(c,h){R(c,e,h),cn(e,t[0]),t[42](e),t[13]&&e.focus(),l||(u=[Te(e,"input",t[41]),Te(e,"keypress",t[23]),Te(e,"blur",t[33]),Te(e,"select",t[22]),Te(e,"focus",t[34])],l=!0)},p(c,h){var f,d,m,p,b,D;h[0]&4&&_(e,"placeholder",c[2]),h[0]&32&&(e.disabled=c[5]),h[0]&8192&&(e.autofocus=c[13]),h[0]&32768&&_(e,"maxlength",c[15]),h[0]&65536&&n!==(n=(f=c[16])==null?void 0:f.autocapitalize)&&_(e,"autocapitalize",n),h[0]&65536&&i!==(i=(d=c[16])==null?void 0:d.autocorrect)&&_(e,"autocorrect",i),h[0]&65536&&r!==(r=(m=c[16])==null?void 0:m.spellcheck)&&_(e,"spellcheck",r),h[0]&65536&&s!==(s=(p=c[16])==null?void 0:p.tabindex)&&_(e,"tabindex",s),h[0]&65536&&o!==(o=(b=c[16])==null?void 0:b.enterkeyhint)&&_(e,"enterkeyhint",o),h[0]&65536&&a!==(a=(D=c[16])==null?void 0:D.lang)&&_(e,"lang",a),h[0]&1&&e.value!==c[0]&&cn(e,c[0])},d(c){c&&v(e),t[42](null),l=!1,En(u)}}}function __(t){let e,n,i,r,s,o,a,l,u,c,h,f;return{c(){e=Z("input"),this.h()},l(d){e=X(d,"INPUT",{"data-testid":!0,type:!0,class:!0,dir:!0,placeholder:!0,maxlength:!0,style:!0,autocapitalize:!0,autocorrect:!0,spellcheck:!0,autocomplete:!0,tabindex:!0,enterkeyhint:!0,lang:!0}),this.h()},h(){var d,m,p,b,D,y,g;_(e,"data-testid","textbox"),_(e,"type","text"),_(e,"class","scroll-hide svelte-173056l"),_(e,"dir",n=t[12]?"rtl":"ltr"),_(e,"placeholder",t[2]),e.disabled=t[5],e.autofocus=t[13],_(e,"maxlength",t[15]),_(e,"style",i=t[14]?"text-align: "+t[14]:""),_(e,"autocapitalize",r=(d=t[16])==null?void 0:d.autocapitalize),_(e,"autocorrect",s=(m=t[16])==null?void 0:m.autocorrect),_(e,"spellcheck",o=(p=t[16])==null?void 0:p.spellcheck),_(e,"autocomplete",a=(b=t[16])==null?void 0:b.autocomplete),_(e,"tabindex",l=(D=t[16])==null?void 0:D.tabindex),_(e,"enterkeyhint",u=(y=t[16])==null?void 0:y.enterkeyhint),_(e,"lang",c=(g=t[16])==null?void 0:g.lang)},m(d,m){R(d,e,m),cn(e,t[0]),t[40](e),t[13]&&e.focus(),h||(f=[Te(e,"input",t[39]),Te(e,"keypress",t[23]),Te(e,"blur",t[31]),Te(e,"select",t[22]),Te(e,"focus",t[32])],h=!0)},p(d,m){var p,b,D,y,g,A,w;m[0]&4096&&n!==(n=d[12]?"rtl":"ltr")&&_(e,"dir",n),m[0]&4&&_(e,"placeholder",d[2]),m[0]&32&&(e.disabled=d[5]),m[0]&8192&&(e.autofocus=d[13]),m[0]&32768&&_(e,"maxlength",d[15]),m[0]&16384&&i!==(i=d[14]?"text-align: "+d[14]:"")&&_(e,"style",i),m[0]&65536&&r!==(r=(p=d[16])==null?void 0:p.autocapitalize)&&_(e,"autocapitalize",r),m[0]&65536&&s!==(s=(b=d[16])==null?void 0:b.autocorrect)&&_(e,"autocorrect",s),m[0]&65536&&o!==(o=(D=d[16])==null?void 0:D.spellcheck)&&_(e,"spellcheck",o),m[0]&65536&&a!==(a=(y=d[16])==null?void 0:y.autocomplete)&&_(e,"autocomplete",a),m[0]&65536&&l!==(l=(g=d[16])==null?void 0:g.tabindex)&&_(e,"tabindex",l),m[0]&65536&&u!==(u=(A=d[16])==null?void 0:A.enterkeyhint)&&_(e,"enterkeyhint",u),m[0]&65536&&c!==(c=(w=d[16])==null?void 0:w.lang)&&_(e,"lang",c),m[0]&1&&e.value!==d[0]&&cn(e,d[0])},d(d){d&&v(e),t[40](null),h=!1,En(f)}}}function Ea(t){let e,n,i,r,s,o;const a=[b_,g_],l=[];function u(c,h){return c[10]===!0?0:1}return n=u(t),i=l[n]=a[n](t),{c(){e=Z("button"),i.c(),this.h()},l(c){e=X(c,"BUTTON",{class:!0});var h=U(e);i.l(h),h.forEach(v),this.h()},h(){_(e,"class","submit-button svelte-173056l"),G(e,"padded-button",t[10]!==!0)},m(c,h){R(c,e,h),l[n].m(e,null),r=!0,s||(o=Te(e,"click",t[26]),s=!0)},p(c,h){let f=n;n=u(c),n===f?l[n].p(c,h):(Be(),j(l[f],1,1,()=>{l[f]=null}),Ne(),i=l[n],i?i.p(c,h):(i=l[n]=a[n](c),i.c()),B(i,1),i.m(e,null)),(!r||h[0]&1024)&&G(e,"padded-button",c[10]!==!0)},i(c){r||(B(i),r=!0)},o(c){j(i),r=!1},d(c){c&&v(e),l[n].d(),s=!1,o()}}}function g_(t){let e;return{c(){e=Ee(t[10])},l(n){e=ve(n,t[10])},m(n,i){R(n,e,i)},p(n,i){i[0]&1024&&je(e,n[10])},i:pe,o:pe,d(n){n&&v(e)}}}function b_(t){let e,n;return e=new Fp({}),{c(){ge(e.$$.fragment)},l(i){Re(e.$$.fragment,i)},m(i,r){be(e,i,r),n=!0},p:pe,i(i){n||(B(e.$$.fragment,i),n=!0)},o(i){j(e.$$.fragment,i),n=!1},d(i){we(e,i)}}}function va(t){let e,n,i,r,s,o;const a=[E_,w_],l=[];function u(c,h){return c[11]===!0?0:1}return n=u(t),i=l[n]=a[n](t),{c(){e=Z("button"),i.c(),this.h()},l(c){e=X(c,"BUTTON",{class:!0});var h=U(e);i.l(h),h.forEach(v),this.h()},h(){_(e,"class","stop-button svelte-173056l"),G(e,"padded-button",t[11]!==!0)},m(c,h){R(c,e,h),l[n].m(e,null),r=!0,s||(o=Te(e,"click",t[25]),s=!0)},p(c,h){let f=n;n=u(c),n===f?l[n].p(c,h):(Be(),j(l[f],1,1,()=>{l[f]=null}),Ne(),i=l[n],i?i.p(c,h):(i=l[n]=a[n](c),i.c()),B(i,1),i.m(e,null)),(!r||h[0]&2048)&&G(e,"padded-button",c[11]!==!0)},i(c){r||(B(i),r=!0)},o(c){j(i),r=!1},d(c){c&&v(e),l[n].d(),s=!1,o()}}}function w_(t){let e;return{c(){e=Ee(t[11])},l(n){e=ve(n,t[11])},m(n,i){R(n,e,i)},p(n,i){i[0]&2048&&je(e,n[11])},i:pe,o:pe,d(n){n&&v(e)}}}function E_(t){let e,n;return e=new Sp({props:{fill:"none",stroke_width:2.5}}),{c(){ge(e.$$.fragment)},l(i){Re(e.$$.fragment,i)},m(i,r){be(e,i,r),n=!0},p:pe,i(i){n||(B(e.$$.fragment,i),n=!0)},o(i){j(e.$$.fragment,i),n=!1},d(i){we(e,i)}}}function v_(t){let e,n,i,r,s,o,a,l,u=t[6]&&t[9]&&wa(t);i=new up({props:{show_label:t[6],info:t[4],$$slots:{default:[f_]},$$scope:{ctx:t}}});function c(p,b){return p[1]===1&&p[18]===1?d_:h_}let h=c(t),f=h(t),d=t[10]&&Ea(t),m=t[11]&&va(t);return{c(){e=Z("label"),u&&u.c(),n=oe(),ge(i.$$.fragment),r=oe(),s=Z("div"),f.c(),o=oe(),d&&d.c(),a=oe(),m&&m.c(),this.h()},l(p){e=X(p,"LABEL",{class:!0});var b=U(e);u&&u.l(b),n=ae(b),Re(i.$$.fragment,b),r=ae(b),s=X(b,"DIV",{class:!0});var D=U(s);f.l(D),o=ae(D),d&&d.l(D),a=ae(D),m&&m.l(D),D.forEach(v),b.forEach(v),this.h()},h(){_(s,"class","input-container svelte-173056l"),_(e,"class","svelte-173056l"),G(e,"container",t[7]),G(e,"show_textbox_border",t[20])},m(p,b){R(p,e,b),u&&u.m(e,null),M(e,n),be(i,e,null),M(e,r),M(e,s),f.m(s,null),M(s,o),d&&d.m(s,null),M(s,a),m&&m.m(s,null),l=!0},p(p,b){p[6]&&p[9]?u?(u.p(p,b),b[0]&576&&B(u,1)):(u=wa(p),u.c(),B(u,1),u.m(e,n)):u&&(Be(),j(u,1,1,()=>{u=null}),Ne());const D={};b[0]&64&&(D.show_label=p[6]),b[0]&16&&(D.info=p[4]),b[0]&8|b[1]&33554432&&(D.$$scope={dirty:b,ctx:p}),i.$set(D),h===(h=c(p))&&f?f.p(p,b):(f.d(1),f=h(p),f&&(f.c(),f.m(s,o))),p[10]?d?(d.p(p,b),b[0]&1024&&B(d,1)):(d=Ea(p),d.c(),B(d,1),d.m(s,a)):d&&(Be(),j(d,1,1,()=>{d=null}),Ne()),p[11]?m?(m.p(p,b),b[0]&2048&&B(m,1)):(m=va(p),m.c(),B(m,1),m.m(s,null)):m&&(Be(),j(m,1,1,()=>{m=null}),Ne()),(!l||b[0]&128)&&G(e,"container",p[7])},i(p){l||(B(u),B(i.$$.fragment,p),B(d),B(m),l=!0)},o(p){j(u),j(i.$$.fragment,p),j(d),j(m),l=!1},d(p){p&&v(e),u&&u.d(),we(i),f.d(),d&&d.d(),m&&m.d()}}}function y_(t){const e=t.scrollHeight,n=t.clientHeight,i=parseFloat(window.getComputedStyle(t).lineHeight);e>n+i?t.style.overflowY="scroll":t.style.overflowY="hidden"}function D_(t,e,n){let{value:i=""}=e,{value_is_output:r=!1}=e,{lines:s=1}=e,{placeholder:o="Type here..."}=e,{label:a}=e,{info:l=void 0}=e,{disabled:u=!1}=e,{show_label:c=!0}=e,{container:h=!0}=e,{max_lines:f=void 0}=e,{type:d="text"}=e,{show_copy_button:m=!1}=e,{submit_btn:p=null}=e,{stop_btn:b=null}=e,{rtl:D=!1}=e,{autofocus:y=!1}=e,{text_align:g=void 0}=e,{autoscroll:A=!0}=e,{max_length:w=void 0}=e,{html_attributes:E=null}=e,S,z=!1,C,N,re=0,x=!1,le;const me=!p,ke=zn();Xu(()=>{!x&&S&&S.offsetHeight+S.scrollTop>S.scrollHeight-100&&(N=!0)});const ue=()=>{N&&A&&!x&&S.scrollTo(0,S.scrollHeight)};function Q(){ke("change",i),r||ke("input")}Ba(()=>{y&&S.focus(),N&&A&&ue(),n(28,r=!1)});async function O(){"clipboard"in navigator&&(await navigator.clipboard.writeText(i),ke("copy",{value:i}),I())}function I(){n(19,z=!0),C&&clearTimeout(C),C=setTimeout(()=>{n(19,z=!1)},1e3)}function L(H){const De=H.target,Ie=De.value,Ye=[De.selectionStart,De.selectionEnd];ke("select",{value:Ie.substring(...Ye),index:Ye})}async function k(H){await on(),(H.key==="Enter"&&H.shiftKey&&s>1||H.key==="Enter"&&!H.shiftKey&&s===1&&le>=1)&&(H.preventDefault(),ke("submit"))}function J(H){const De=H.target,Ie=De.scrollTop;Ie<re&&(x=!0),re=Ie;const Ye=De.scrollHeight-De.clientHeight;Ie>=Ye&&(x=!1)}function V(){ke("stop")}function $(){ke("submit")}async function q(H){if(await on(),s===le)return;const De=H.target,Ie=window.getComputedStyle(De),Ye=parseFloat(Ie.paddingTop),bt=parseFloat(Ie.paddingBottom),kt=parseFloat(Ie.lineHeight);let Ge=le===void 0?!1:Ye+bt+kt*le,se=Ye+bt+s*kt;De.style.height="1px";let Ae;Ge&&De.scrollHeight>Ge?Ae=Ge:De.scrollHeight<se?Ae=se:Ae=De.scrollHeight,De.style.height=`${Ae}px`,y_(De)}function Ce(H,De){if(s!==le&&(H.style.overflowY="scroll",H.addEventListener("input",q),!!De.trim()))return q({target:H}),{destroy:()=>H.removeEventListener("input",q)}}function Ze(H){_t.call(this,t,H)}function he(H){_t.call(this,t,H)}function Me(H){_t.call(this,t,H)}function it(H){_t.call(this,t,H)}function $e(H){_t.call(this,t,H)}function ct(H){_t.call(this,t,H)}function et(H){_t.call(this,t,H)}function st(H){_t.call(this,t,H)}function ye(){i=this.value,n(0,i)}function lt(H){Je[H?"unshift":"push"](()=>{S=H,n(17,S)})}function yt(){i=this.value,n(0,i)}function gt(H){Je[H?"unshift":"push"](()=>{S=H,n(17,S)})}function xe(){i=this.value,n(0,i)}function dt(H){Je[H?"unshift":"push"](()=>{S=H,n(17,S)})}function ht(){i=this.value,n(0,i)}function tt(H){Je[H?"unshift":"push"](()=>{S=H,n(17,S)})}return t.$$set=H=>{"value"in H&&n(0,i=H.value),"value_is_output"in H&&n(28,r=H.value_is_output),"lines"in H&&n(1,s=H.lines),"placeholder"in H&&n(2,o=H.placeholder),"label"in H&&n(3,a=H.label),"info"in H&&n(4,l=H.info),"disabled"in H&&n(5,u=H.disabled),"show_label"in H&&n(6,c=H.show_label),"container"in H&&n(7,h=H.container),"max_lines"in H&&n(29,f=H.max_lines),"type"in H&&n(8,d=H.type),"show_copy_button"in H&&n(9,m=H.show_copy_button),"submit_btn"in H&&n(10,p=H.submit_btn),"stop_btn"in H&&n(11,b=H.stop_btn),"rtl"in H&&n(12,D=H.rtl),"autofocus"in H&&n(13,y=H.autofocus),"text_align"in H&&n(14,g=H.text_align),"autoscroll"in H&&n(30,A=H.autoscroll),"max_length"in H&&n(15,w=H.max_length),"html_attributes"in H&&n(16,E=H.html_attributes)},t.$$.update=()=>{t.$$.dirty[0]&536871170&&(f==null?d==="text"?n(18,le=Math.max(s,20)):n(18,le=1):n(18,le=Math.max(f,s))),t.$$.dirty[0]&1&&i===null&&n(0,i=""),t.$$.dirty[0]&393219&&S&&s!==le&&q({target:S}),t.$$.dirty[0]&1&&Q()},[i,s,o,a,l,u,c,h,d,m,p,b,D,y,g,w,E,S,le,z,me,O,L,k,J,V,$,Ce,r,f,A,Ze,he,Me,it,$e,ct,et,st,ye,lt,yt,gt,xe,dt,ht,tt]}class Lu extends qe{constructor(e){super(),We(this,e,D_,v_,Xe,{value:0,value_is_output:28,lines:1,placeholder:2,label:3,info:4,disabled:5,show_label:6,container:7,max_lines:29,type:8,show_copy_button:9,submit_btn:10,stop_btn:11,rtl:12,autofocus:13,text_align:14,autoscroll:30,max_length:15,html_attributes:16},null,[-1,-1])}}const k_="WORKER_PROXY_CONTEXT_KEY";function A_(){return Pa(k_)}const F_="lite.local";function T_(t){return t.host===window.location.host||t.host==="localhost:7860"||t.host==="127.0.0.1:7860"||t.host===F_}function C_(t,e){const n=e.toLowerCase();for(const[i,r]of Object.entries(t))if(i.toLowerCase()===n)return r}function S_(t){const e=typeof window<"u";if(t==null||!e)return!1;const n=new URL(t,window.location.href);return!(!T_(n)||n.protocol!=="http:"&&n.protocol!=="https:")}let Oi;async function L_(t){const e=typeof window<"u";if(t==null||!e||!S_(t))return t;if(Oi==null)try{Oi=A_()}catch{return t}if(Oi==null)return t;const i=new URL(t,window.location.href).pathname;return Oi.httpRequest({method:"GET",path:i,headers:{},query_string:""}).then(r=>{if(r.status!==200)throw new Error(`Failed to get file ${i} from the Wasm worker.`);const s=new Blob([r.body],{type:C_(r.headers,"content-type")});return URL.createObjectURL(s)})}function R_(t){let e,n,i,r,s=[{src:n=t[0]},t[1]],o={};for(let a=0;a<s.length;a+=1)o=It(o,s[a]);return{c(){e=Z("img"),this.h()},l(a){e=X(a,"IMG",{src:!0}),this.h()},h(){ys(e,o),G(e,"svelte-1pijsyv",!0)},m(a,l){R(a,e,l),i||(r=Te(e,"load",t[4]),i=!0)},p(a,[l]){ys(e,o=un(s,[l&1&&!Jn(e.src,n=a[0])&&{src:n},l&2&&a[1]])),G(e,"svelte-1pijsyv",!0)},i:pe,o:pe,d(a){a&&v(e),i=!1,r()}}}function O_(t,e,n){const i=["src"];let r=zi(e,i),{src:s=void 0}=e,o,a;function l(u){_t.call(this,t,u)}return t.$$set=u=>{e=It(It({},e),za(u)),n(1,r=zi(e,i)),"src"in u&&n(2,s=u.src)},t.$$.update=()=>{if(t.$$.dirty&12){n(0,o=s),n(3,a=s);const u=s;L_(u).then(c=>{a===u&&n(0,o=c)})}},[o,r,s,a,l]}class Ru extends qe{constructor(e){super(),We(this,e,O_,R_,Xe,{src:2})}}function I_(t){let e,n,i,r,s,o,a=t[7]&&ya(t);const l=t[12].default,u=Ut(l,t,t[11],null);return{c(){e=Z("button"),a&&a.c(),n=oe(),u&&u.c(),this.h()},l(c){e=X(c,"BUTTON",{class:!0,id:!0});var h=U(e);a&&a.l(h),n=ae(h),u&&u.l(h),h.forEach(v),this.h()},h(){_(e,"class",i=t[4]+" "+t[3]+" "+t[1].join(" ")+" svelte-1ixn6qd"),_(e,"id",t[0]),e.disabled=t[8],G(e,"hidden",!t[2]),Y(e,"flex-grow",t[9]),Y(e,"width",t[9]===0?"fit-content":null),Y(e,"min-width",typeof t[10]=="number"?`calc(min(${t[10]}px, 100%))`:null)},m(c,h){R(c,e,h),a&&a.m(e,null),M(e,n),u&&u.m(e,null),r=!0,s||(o=Te(e,"click",t[13]),s=!0)},p(c,h){c[7]?a?(a.p(c,h),h&128&&B(a,1)):(a=ya(c),a.c(),B(a,1),a.m(e,n)):a&&(Be(),j(a,1,1,()=>{a=null}),Ne()),u&&u.p&&(!r||h&2048)&&jt(u,l,c,c[11],r?Vt(l,c[11],h,null):Gt(c[11]),null),(!r||h&26&&i!==(i=c[4]+" "+c[3]+" "+c[1].join(" ")+" svelte-1ixn6qd"))&&_(e,"class",i),(!r||h&1)&&_(e,"id",c[0]),(!r||h&256)&&(e.disabled=c[8]),(!r||h&30)&&G(e,"hidden",!c[2]),h&512&&Y(e,"flex-grow",c[9]),h&512&&Y(e,"width",c[9]===0?"fit-content":null),h&1024&&Y(e,"min-width",typeof c[10]=="number"?`calc(min(${c[10]}px, 100%))`:null)},i(c){r||(B(a),B(u,c),r=!0)},o(c){j(a),j(u,c),r=!1},d(c){c&&v(e),a&&a.d(),u&&u.d(c),s=!1,o()}}}function P_(t){let e,n,i,r,s=t[7]&&Da(t);const o=t[12].default,a=Ut(o,t,t[11],null);return{c(){e=Z("a"),s&&s.c(),n=oe(),a&&a.c(),this.h()},l(l){e=X(l,"A",{href:!0,rel:!0,"aria-disabled":!0,class:!0,id:!0});var u=U(e);s&&s.l(u),n=ae(u),a&&a.l(u),u.forEach(v),this.h()},h(){_(e,"href",t[6]),_(e,"rel","noopener noreferrer"),_(e,"aria-disabled",t[8]),_(e,"class",i=t[4]+" "+t[3]+" "+t[1].join(" ")+" svelte-1ixn6qd"),_(e,"id",t[0]),G(e,"hidden",!t[2]),G(e,"disabled",t[8]),Y(e,"flex-grow",t[9]),Y(e,"pointer-events",t[8]?"none":null),Y(e,"width",t[9]===0?"fit-content":null),Y(e,"min-width",typeof t[10]=="number"?`calc(min(${t[10]}px, 100%))`:null)},m(l,u){R(l,e,u),s&&s.m(e,null),M(e,n),a&&a.m(e,null),r=!0},p(l,u){l[7]?s?(s.p(l,u),u&128&&B(s,1)):(s=Da(l),s.c(),B(s,1),s.m(e,n)):s&&(Be(),j(s,1,1,()=>{s=null}),Ne()),a&&a.p&&(!r||u&2048)&&jt(a,o,l,l[11],r?Vt(o,l[11],u,null):Gt(l[11]),null),(!r||u&64)&&_(e,"href",l[6]),(!r||u&256)&&_(e,"aria-disabled",l[8]),(!r||u&26&&i!==(i=l[4]+" "+l[3]+" "+l[1].join(" ")+" svelte-1ixn6qd"))&&_(e,"class",i),(!r||u&1)&&_(e,"id",l[0]),(!r||u&30)&&G(e,"hidden",!l[2]),(!r||u&282)&&G(e,"disabled",l[8]),u&512&&Y(e,"flex-grow",l[9]),u&256&&Y(e,"pointer-events",l[8]?"none":null),u&512&&Y(e,"width",l[9]===0?"fit-content":null),u&1024&&Y(e,"min-width",typeof l[10]=="number"?`calc(min(${l[10]}px, 100%))`:null)},i(l){r||(B(s),B(a,l),r=!0)},o(l){j(s),j(a,l),r=!1},d(l){l&&v(e),s&&s.d(),a&&a.d(l)}}}function ya(t){let e,n;return e=new Ru({props:{class:`button-icon ${t[5]?"right-padded":""}`,src:t[7].url,alt:`${t[5]} icon`}}),{c(){ge(e.$$.fragment)},l(i){Re(e.$$.fragment,i)},m(i,r){be(e,i,r),n=!0},p(i,r){const s={};r&32&&(s.class=`button-icon ${i[5]?"right-padded":""}`),r&128&&(s.src=i[7].url),r&32&&(s.alt=`${i[5]} icon`),e.$set(s)},i(i){n||(B(e.$$.fragment,i),n=!0)},o(i){j(e.$$.fragment,i),n=!1},d(i){we(e,i)}}}function Da(t){let e,n;return e=new Ru({props:{class:"button-icon",src:t[7].url,alt:`${t[5]} icon`}}),{c(){ge(e.$$.fragment)},l(i){Re(e.$$.fragment,i)},m(i,r){be(e,i,r),n=!0},p(i,r){const s={};r&128&&(s.src=i[7].url),r&32&&(s.alt=`${i[5]} icon`),e.$set(s)},i(i){n||(B(e.$$.fragment,i),n=!0)},o(i){j(e.$$.fragment,i),n=!1},d(i){we(e,i)}}}function B_(t){let e,n,i,r;const s=[P_,I_],o=[];function a(l,u){return l[6]&&l[6].length>0?0:1}return e=a(t),n=o[e]=s[e](t),{c(){n.c(),i=fe()},l(l){n.l(l),i=fe()},m(l,u){o[e].m(l,u),R(l,i,u),r=!0},p(l,[u]){let c=e;e=a(l),e===c?o[e].p(l,u):(Be(),j(o[c],1,1,()=>{o[c]=null}),Ne(),n=o[e],n?n.p(l,u):(n=o[e]=s[e](l),n.c()),B(n,1),n.m(i.parentNode,i))},i(l){r||(B(n),r=!0)},o(l){j(n),r=!1},d(l){l&&v(i),o[e].d(l)}}}function N_(t,e,n){let{$$slots:i={},$$scope:r}=e,{elem_id:s=""}=e,{elem_classes:o=[]}=e,{visible:a=!0}=e,{variant:l="secondary"}=e,{size:u="lg"}=e,{value:c=null}=e,{link:h=null}=e,{icon:f=null}=e,{disabled:d=!1}=e,{scale:m=null}=e,{min_width:p=void 0}=e;function b(D){_t.call(this,t,D)}return t.$$set=D=>{"elem_id"in D&&n(0,s=D.elem_id),"elem_classes"in D&&n(1,o=D.elem_classes),"visible"in D&&n(2,a=D.visible),"variant"in D&&n(3,l=D.variant),"size"in D&&n(4,u=D.size),"value"in D&&n(5,c=D.value),"link"in D&&n(6,h=D.link),"icon"in D&&n(7,f=D.icon),"disabled"in D&&n(8,d=D.disabled),"scale"in D&&n(9,m=D.scale),"min_width"in D&&n(10,p=D.min_width),"$$scope"in D&&n(11,r=D.$$scope)},[s,o,a,l,u,c,h,f,d,m,p,r,i,b]}class M_ extends qe{constructor(e){super(),We(this,e,N_,B_,Xe,{elem_id:0,elem_classes:1,visible:2,variant:3,size:4,value:5,link:6,icon:7,disabled:8,scale:9,min_width:10})}}function ka(t){let e,n;const i=[{autoscroll:t[7].autoscroll},{i18n:t[7].i18n},t[6],{status:t[6]?t[6].status=="pending"?"generating":t[6].status:null}];let r={};for(let s=0;s<i.length;s+=1)r=It(r,i[s]);return e=new Xp({props:r}),{c(){ge(e.$$.fragment)},l(s){Re(e.$$.fragment,s)},m(s,o){be(e,s,o),n=!0},p(s,o){const a=o&192?un(i,[o&128&&{autoscroll:s[7].autoscroll},o&128&&{i18n:s[7].i18n},o&64&&Bn(s[6]),o&64&&{status:s[6]?s[6].status=="pending"?"generating":s[6].status:null}]):{};e.$set(a)},i(s){n||(B(e.$$.fragment,s),n=!0)},o(s){j(e.$$.fragment,s),n=!1},d(s){we(e,s)}}}function x_(t){let e,n,i,r=`calc(min(${t[1]}px, 100%))`,s,o=t[6]&&t[8]&&t[7]&&ka(t);const a=t[10].default,l=Ut(a,t,t[9],null);return{c(){e=Z("div"),o&&o.c(),n=oe(),l&&l.c(),this.h()},l(u){e=X(u,"DIV",{id:!0,class:!0});var c=U(e);o&&o.l(c),n=ae(c),l&&l.l(c),c.forEach(v),this.h()},h(){_(e,"id",t[2]),_(e,"class",i="column "+t[3].join(" ")+" svelte-bnzux8"),G(e,"compact",t[5]==="compact"),G(e,"panel",t[5]==="panel"),G(e,"hide",!t[4]),Y(e,"flex-grow",t[0]),Y(e,"min-width",r)},m(u,c){R(u,e,c),o&&o.m(e,null),M(e,n),l&&l.m(e,null),s=!0},p(u,[c]){u[6]&&u[8]&&u[7]?o?(o.p(u,c),c&448&&B(o,1)):(o=ka(u),o.c(),B(o,1),o.m(e,n)):o&&(Be(),j(o,1,1,()=>{o=null}),Ne()),l&&l.p&&(!s||c&512)&&jt(l,a,u,u[9],s?Vt(a,u[9],c,null):Gt(u[9]),null),(!s||c&4)&&_(e,"id",u[2]),(!s||c&8&&i!==(i="column "+u[3].join(" ")+" svelte-bnzux8"))&&_(e,"class",i),(!s||c&40)&&G(e,"compact",u[5]==="compact"),(!s||c&40)&&G(e,"panel",u[5]==="panel"),(!s||c&24)&&G(e,"hide",!u[4]),c&1&&Y(e,"flex-grow",u[0]),c&2&&r!==(r=`calc(min(${u[1]}px, 100%))`)&&Y(e,"min-width",r)},i(u){s||(B(o),B(l,u),s=!0)},o(u){j(o),j(l,u),s=!1},d(u){u&&v(e),o&&o.d(),l&&l.d(u)}}}function H_(t,e,n){let{$$slots:i={},$$scope:r}=e,{scale:s=null}=e,{min_width:o=0}=e,{elem_id:a=""}=e,{elem_classes:l=[]}=e,{visible:u=!0}=e,{variant:c="default"}=e,{loading_status:h=void 0}=e,{gradio:f=void 0}=e,{show_progress:d=!1}=e;return t.$$set=m=>{"scale"in m&&n(0,s=m.scale),"min_width"in m&&n(1,o=m.min_width),"elem_id"in m&&n(2,a=m.elem_id),"elem_classes"in m&&n(3,l=m.elem_classes),"visible"in m&&n(4,u=m.visible),"variant"in m&&n(5,c=m.variant),"loading_status"in m&&n(6,h=m.loading_status),"gradio"in m&&n(7,f=m.gradio),"show_progress"in m&&n(8,d=m.show_progress),"$$scope"in m&&n(9,r=m.$$scope)},[s,o,a,l,u,c,h,f,d,r,i]}class Ou extends qe{constructor(e){super(),We(this,e,H_,x_,Xe,{scale:0,min_width:1,elem_id:2,elem_classes:3,visible:4,variant:5,loading_status:6,gradio:7,show_progress:8})}}const z_=Object.freeze(Object.defineProperty({__proto__:null,default:Ou},Symbol.toStringTag,{value:"Module"}));function Aa(t){let e,n;return{c(){e=Z("p"),n=new Ki(!1),this.h()},l(i){e=X(i,"P",{class:!0});var r=U(e);n=$i(r,!1),r.forEach(v),this.h()},h(){n.a=null,_(e,"class","auth svelte-1ogxbi0")},m(i,r){R(i,e,r),n.m(t[0],e)},p(i,r){r&1&&n.p(i[0])},d(i){i&&v(e)}}}function Fa(t){let e,n=t[6]("login.enable_cookies")+"",i;return{c(){e=Z("p"),i=Ee(n),this.h()},l(r){e=X(r,"P",{class:!0});var s=U(e);i=ve(s,n),s.forEach(v),this.h()},h(){_(e,"class","auth svelte-1ogxbi0")},m(r,s){R(r,e,s),M(e,i)},p(r,s){s&64&&n!==(n=r[6]("login.enable_cookies")+"")&&je(i,n)},d(r){r&&v(e)}}}function Ta(t){let e,n=t[6]("login.incorrect_credentials")+"",i;return{c(){e=Z("p"),i=Ee(n),this.h()},l(r){e=X(r,"P",{class:!0});var s=U(e);i=ve(s,n),s.forEach(v),this.h()},h(){_(e,"class","creds svelte-1ogxbi0")},m(r,s){R(r,e,s),M(e,i)},p(r,s){s&64&&n!==(n=r[6]("login.incorrect_credentials")+"")&&je(i,n)},d(r){r&&v(e)}}}function U_(t){let e,n,i;function r(o){t[9](o)}let s={label:t[6]("login.username"),lines:1,show_label:!0,max_lines:1};return t[3]!==void 0&&(s.value=t[3]),e=new Lu({props:s}),Je.push(()=>mt(e,"value",r)),e.$on("submit",t[7]),{c(){ge(e.$$.fragment)},l(o){Re(e.$$.fragment,o)},m(o,a){be(e,o,a),i=!0},p(o,a){const l={};a&64&&(l.label=o[6]("login.username")),!n&&a&8&&(n=!0,l.value=o[3],xt(()=>n=!1)),e.$set(l)},i(o){i||(B(e.$$.fragment,o),i=!0)},o(o){j(e.$$.fragment,o),i=!1},d(o){we(e,o)}}}function j_(t){let e,n,i;function r(o){t[10](o)}let s={label:t[6]("login.password"),lines:1,show_label:!0,max_lines:1,type:"password"};return t[4]!==void 0&&(s.value=t[4]),e=new Lu({props:s}),Je.push(()=>mt(e,"value",r)),e.$on("submit",t[7]),{c(){ge(e.$$.fragment)},l(o){Re(e.$$.fragment,o)},m(o,a){be(e,o,a),i=!0},p(o,a){const l={};a&64&&(l.label=o[6]("login.password")),!n&&a&16&&(n=!0,l.value=o[4],xt(()=>n=!1)),e.$set(l)},i(o){i||(B(e.$$.fragment,o),i=!0)},o(o){j(e.$$.fragment,o),i=!1},d(o){we(e,o)}}}function G_(t){let e,n,i,r;return e=new ho({props:{$$slots:{default:[U_]},$$scope:{ctx:t}}}),i=new ho({props:{$$slots:{default:[j_]},$$scope:{ctx:t}}}),{c(){ge(e.$$.fragment),n=oe(),ge(i.$$.fragment)},l(s){Re(e.$$.fragment,s),n=ae(s),Re(i.$$.fragment,s)},m(s,o){be(e,s,o),R(s,n,o),be(i,s,o),r=!0},p(s,o){const a={};o&2120&&(a.$$scope={dirty:o,ctx:s}),e.$set(a);const l={};o&2128&&(l.$$scope={dirty:o,ctx:s}),i.$set(l)},i(s){r||(B(e.$$.fragment,s),B(i.$$.fragment,s),r=!0)},o(s){j(e.$$.fragment,s),j(i.$$.fragment,s),r=!1},d(s){s&&v(n),we(e,s),we(i,s)}}}function V_(t){let e=t[6]("login.login")+"",n;return{c(){n=Ee(e)},l(i){n=ve(i,e)},m(i,r){R(i,n,r)},p(i,r){r&64&&e!==(e=i[6]("login.login")+"")&&je(n,e)},d(i){i&&v(n)}}}function q_(t){let e,n=t[6]("login.login")+"",i,r,s,o,a,l,u,c,h,f=t[0]&&Aa(t),d=t[2]&&Fa(t),m=t[5]&&Ta(t);return l=new Su({props:{$$slots:{default:[G_]},$$scope:{ctx:t}}}),c=new M_({props:{size:"lg",variant:"primary",$$slots:{default:[V_]},$$scope:{ctx:t}}}),c.$on("click",t[7]),{c(){e=Z("h2"),i=Ee(n),r=oe(),f&&f.c(),s=oe(),d&&d.c(),o=oe(),m&&m.c(),a=oe(),ge(l.$$.fragment),u=oe(),ge(c.$$.fragment),this.h()},l(p){e=X(p,"H2",{class:!0});var b=U(e);i=ve(b,n),b.forEach(v),r=ae(p),f&&f.l(p),s=ae(p),d&&d.l(p),o=ae(p),m&&m.l(p),a=ae(p),Re(l.$$.fragment,p),u=ae(p),Re(c.$$.fragment,p),this.h()},h(){_(e,"class","svelte-1ogxbi0")},m(p,b){R(p,e,b),M(e,i),R(p,r,b),f&&f.m(p,b),R(p,s,b),d&&d.m(p,b),R(p,o,b),m&&m.m(p,b),R(p,a,b),be(l,p,b),R(p,u,b),be(c,p,b),h=!0},p(p,b){(!h||b&64)&&n!==(n=p[6]("login.login")+"")&&je(i,n),p[0]?f?f.p(p,b):(f=Aa(p),f.c(),f.m(s.parentNode,s)):f&&(f.d(1),f=null),p[2]?d?d.p(p,b):(d=Fa(p),d.c(),d.m(o.parentNode,o)):d&&(d.d(1),d=null),p[5]?m?m.p(p,b):(m=Ta(p),m.c(),m.m(a.parentNode,a)):m&&(m.d(1),m=null);const D={};b&2136&&(D.$$scope={dirty:b,ctx:p}),l.$set(D);const y={};b&2112&&(y.$$scope={dirty:b,ctx:p}),c.$set(y)},i(p){h||(B(l.$$.fragment,p),B(c.$$.fragment,p),h=!0)},o(p){j(l.$$.fragment,p),j(c.$$.fragment,p),h=!1},d(p){p&&(v(e),v(r),v(s),v(o),v(a),v(u)),f&&f.d(p),d&&d.d(p),m&&m.d(p),we(l,p),we(c,p)}}}function W_(t){let e,n,i;return n=new Ou({props:{variant:"panel",min_width:480,$$slots:{default:[q_]},$$scope:{ctx:t}}}),{c(){e=Z("div"),ge(n.$$.fragment),this.h()},l(r){e=X(r,"DIV",{class:!0});var s=U(e);Re(n.$$.fragment,s),s.forEach(v),this.h()},h(){_(e,"class","wrap svelte-1ogxbi0"),G(e,"min-h-screen",t[1])},m(r,s){R(r,e,s),be(n,e,null),i=!0},p(r,[s]){const o={};s&2173&&(o.$$scope={dirty:s,ctx:r}),n.$set(o),(!i||s&2)&&G(e,"min-h-screen",r[1])},i(r){i||(B(n.$$.fragment,r),i=!0)},o(r){j(n.$$.fragment,r),i=!1},d(r){r&&v(e),we(n)}}}function Z_(t,e,n){let i;Rt(t,ui,m=>n(6,i=m));let{root:r}=e,{auth_message:s}=e,{app_mode:o}=e,{space_id:a}=e,l="",u="",c=!1;const h=async()=>{const m=new FormData;m.append("username",l),m.append("password",u);let p=await fetch(r+"/login",{method:"POST",body:m});p.status===400?(n(5,c=!0),n(3,l=""),n(4,u="")):p.status==200&&location.reload()};function f(m){l=m,n(3,l)}function d(m){u=m,n(4,u)}return t.$$set=m=>{"root"in m&&n(8,r=m.root),"auth_message"in m&&n(0,s=m.auth_message),"app_mode"in m&&n(1,o=m.app_mode),"space_id"in m&&n(2,a=m.space_id)},[s,o,a,l,u,c,i,h,r,f,d]}class Ca extends qe{constructor(e){super(),We(this,e,Z_,W_,Xe,{root:8,auth_message:0,app_mode:1,space_id:2})}}async function X_({url:t,data:{server:e,port:n,local_dev_mode:i}}){var h;let r;const s=i?e:new URL(".",location.href).href,o=t.searchParams.get("deep_link");try{r=await qi.connect(s,{with_null_state:!0,events:["data","log","status","render"],query_params:o?{deep_link:o}:void 0})}catch(f){const d=f.message||"";let m="";return d.includes(Gl)||(m=d.replace(/^Error:?\s*/,"")),{Render:Ca,config:{auth_message:m,auth_required:!0,components:[],current_page:"",dependencies:[],layout:{},pages:[],page:{},root:t.origin,space_id:null,analytics_enabled:!1,connect_heartbeat:!1,css:"",js:"",theme_hash:0,head:"",dev_mode:!1,enable_queue:!1,show_error:!1,fill_height:!1,fill_width:!1,mode:"blocks",theme:"default",title:"",version:"",api_prefix:"",is_space:!1,is_colab:!1,show_api:!1,stylesheets:[],protocol:"sse_v3",username:""},api_url:s,layout:{},app:null}}if(!r.config)throw new Error("No config found");let a=r.get_url_config(t.toString());const{create_layout:l,layout:u}=hl();await l({app:r,components:a.components,dependencies:a.dependencies,layout:a.layout,root:r.config.root+r.config.api_prefix,options:{fill_height:r.config.fill_height??!1}});const c=$r(u);return await xl(),{Render:(h=r.config)!=null&&h.auth_required?Ca:s_,config:a,api_url:s,layout:c,app:r}}const Gg=Object.freeze(Object.defineProperty({__proto__:null,load:X_},Symbol.toStringTag,{value:"Module"}));var Y_=()=>{const t=document.createElement("link");t.href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700;1,900&display=swap",t.rel="stylesheet";const e=document.createElement("link");e.href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@400;600;700&display=swap",e.rel="stylesheet",document.head.appendChild(t),document.head.appendChild(e)},J_=()=>{const t=document.createElement("div");return t.style.backgroundImage="linear-gradient(to top, #f9fafb, white)",t.style.border="1px solid #e5e7eb",t.style.borderRadius="0.75rem",t.style.boxShadow="0 0 10px rgba(0, 0, 0, 0.1)",t.style.color="#374151",t.style.display="flex",t.style.flexDirection="row",t.style.alignItems="center",t.style.height="40px",t.style.justifyContent="space-between",t.style.overflow="hidden",t.style.position="fixed",t.style.right=".75rem",t.style.top=".75rem",t.style.width="auto",t.style.zIndex="20",t.style.paddingLeft="1rem",t.setAttribute("id","huggingface-space-header"),window.matchMedia("(max-width: 768px)").addEventListener("change",e=>{e.matches?t.style.display="none":t.style.display="flex"}),t},Q_=()=>{const t=document.createElementNS("http://www.w3.org/2000/svg","svg");t.setAttribute("xmlns","http://www.w3.org/2000/svg"),t.setAttribute("xmlns:link","http://www.w3.org/1999/xlink"),t.setAttribute("aria-hidden","true"),t.setAttribute("focusable","false"),t.setAttribute("role","img"),t.setAttribute("width","1em"),t.setAttribute("height","1em"),t.setAttribute("preserveAspectRatio","xMidYMid meet"),t.setAttribute("viewBox","0 0 12 12"),t.setAttribute("fill","currentColor");const e=document.createElementNS("http://www.w3.org/2000/svg","path");return e.setAttribute("d","M0.375001 10.3828L0.375 1.61719C0.375 1.104 0.816001 0.687501 1.35938 0.687501L10.6406 0.6875C10.9017 0.6875 11.1521 0.785449 11.3367 0.959797C11.5213 1.13415 11.625 1.37062 11.625 1.61719V10.3828C11.625 10.6294 11.5213 10.8659 11.3367 11.0402C11.1521 11.2145 10.9017 11.3125 10.6406 11.3125H1.35938C0.816001 11.3125 0.375001 10.896 0.375001 10.3828ZM1.35938 10.5156H10.6406C10.7183 10.5156 10.7813 10.4561 10.7813 10.3828V4.40625H1.21875V10.3828C1.21875 10.418 1.23356 10.4518 1.25994 10.4767C1.28631 10.5017 1.32208 10.5156 1.35938 10.5156ZM4.61052 6.38251L5.9999 7.69472L7.38927 6.38251C7.44083 6.33007 7.50645 6.29173 7.57913 6.27153C7.6518 6.25134 7.72898 6.25003 7.8024 6.26776C7.87583 6.28549 7.9428 6.3216 7.99628 6.37227C8.04983 6.42295 8.08785 6.48631 8.10645 6.5557C8.12528 6.62497 8.12393 6.69773 8.10263 6.76635C8.0814 6.83497 8.0409 6.8969 7.98555 6.94564L6.29802 8.53936C6.21892 8.61399 6.11169 8.65592 5.9999 8.65592C5.8881 8.65592 5.78087 8.61399 5.70177 8.53936L4.01427 6.94564C3.95874 6.89694 3.91814 6.835 3.89676 6.76633C3.87538 6.69766 3.874 6.62483 3.89277 6.55549C3.91154 6.48615 3.94977 6.42287 4.00343 6.37233C4.05708 6.32179 4.12418 6.28585 4.19765 6.2683C4.27098 6.25054 4.34803 6.25178 4.42068 6.27188C4.49334 6.29198 4.55891 6.3302 4.61052 6.38251Z"),t.appendChild(e),t},K_=(t,e)=>{const n=document.createElement("div");return n.setAttribute("id","space-header__collapse"),n.style.display="flex",n.style.flexDirection="row",n.style.alignItems="center",n.style.justifyContent="center",n.style.fontSize="16px",n.style.paddingLeft="10px",n.style.paddingRight="10px",n.style.height="40px",n.style.cursor="pointer",n.style.color="#40546e",n.style.transitionDuration="0.1s",n.style.transitionProperty="all",n.style.transitionTimingFunction="ease-in-out",n.appendChild(Q_()),n.addEventListener("click",i=>{i.preventDefault(),i.stopPropagation(),e()}),n.addEventListener("mouseenter",()=>{n.style.color="#213551"}),n.addEventListener("mouseleave",()=>{n.style.color="#40546e"}),n},$_=t=>{const e=document.createElement("p");return e.style.margin="0",e.style.padding="0",e.style.color="#9ca3af",e.style.fontSize="14px",e.style.fontFamily="Source Sans Pro, sans-serif",e.style.padding="0px 6px",e.style.borderLeft="1px solid #e5e7eb",e.style.marginLeft="4px",e.textContent=(t??0).toString(),e},eg=()=>{const t=document.createElementNS("http://www.w3.org/2000/svg","svg");t.setAttribute("xmlns","http://www.w3.org/2000/svg"),t.setAttribute("xmlns:link","http://www.w3.org/1999/xlink"),t.setAttribute("aria-hidden","true"),t.setAttribute("focusable","false"),t.setAttribute("role","img"),t.setAttribute("width","1em"),t.setAttribute("height","1em"),t.setAttribute("preserveAspectRatio","xMidYMid meet"),t.setAttribute("viewBox","0 0 32 32"),t.setAttribute("fill","#6b7280");const e=document.createElementNS("http://www.w3.org/2000/svg","path");return e.setAttribute("d","M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z"),t.appendChild(e),t},tg=t=>{const e=document.createElement("a");return e.setAttribute("href",`https://huggingface.co/spaces/${t.id}`),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e.style.border="1px solid #e5e7eb",e.style.borderRadius="6px",e.style.display="flex",e.style.flexDirection="row",e.style.alignItems="center",e.style.margin="0 0 0 12px",e.style.fontSize="14px",e.style.paddingLeft="4px",e.style.textDecoration="none",e.appendChild(eg()),e.appendChild($_(t.likes)),e},ng=(t,e="user")=>{const n=e==="user"?"users":"organizations",i=document.createElement("img");return i.src=`https://huggingface.co/api/${n}/${t}/avatar`,i.style.width="0.875rem",i.style.height="0.875rem",i.style.borderRadius="50%",i.style.flex="none",i.style.marginRight="0.375rem",i},ig=t=>{const[e,n]=t.split("/"),i=document.createElement("a");return i.setAttribute("href",`https://huggingface.co/spaces/${t}`),i.setAttribute("rel","noopener noreferrer"),i.setAttribute("target","_blank"),i.style.color="#1f2937",i.style.textDecoration="none",i.style.fontWeight="600",i.style.fontSize="15px",i.style.lineHeight="24px",i.style.flex="none",i.style.fontFamily="IBM Plex Mono, sans-serif",i.addEventListener("mouseover",()=>{i.style.color="#2563eb"}),i.addEventListener("mouseout",()=>{i.style.color="#1f2937"}),i.textContent=n,i},rg=()=>{const t=document.createElement("div");return t.style.marginLeft=".125rem",t.style.marginRight=".125rem",t.style.color="#d1d5db",t.textContent="/",t},sg=t=>{const e=document.createElement("a");return e.setAttribute("href",`https://huggingface.co/${t}`),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e.style.color="rgb(107, 114, 128)",e.style.textDecoration="none",e.style.fontWeight="400",e.style.fontSize="16px",e.style.lineHeight="24px",e.style.flex="none",e.style.fontFamily="Source Sans Pro, sans-serif",e.addEventListener("mouseover",()=>{e.style.color="#2563eb"}),e.addEventListener("mouseout",()=>{e.style.color="rgb(107, 114, 128)"}),e.textContent=t,e},og=t=>{const e=document.createElement("div");return e.style.display="flex",e.style.flexDirection="row",e.style.alignItems="center",e.style.justifyContent="center",e.style.borderRight="1px solid #e5e7eb",e.style.paddingRight="12px",e.style.height="40px",t.type!=="unknown"&&e.appendChild(ng(t.author,t.type)),e.appendChild(sg(t.author)),e.appendChild(rg()),e.appendChild(ig(t.id)),e.appendChild(tg(t)),e},ag=t=>{const e=J_(),n=()=>e.style.display="none";return e.appendChild(og(t)),e.appendChild(K_(t,n)),e},Sa=async(t,e="user")=>{const n=e==="user"?"users":"organizations";try{return(await fetch(`https://huggingface.co/api/${n}/${t}/avatar`)).ok}catch{return!1}},lg=async t=>{try{return await(await fetch(`https://huggingface.co/api/spaces/${t}`)).json()}catch{return null}},ug=(t,e)=>{if(document.body===null)return console.error("document.body is null");document.body.appendChild(t)};async function cg(t,e){var n,i;if(window===void 0)return console.error("Please run this script in a browser environment");if(Object.values((i=(n=window.location)==null?void 0:n.ancestorOrigins)!=null?i:{0:window.document.referrer}).some(u=>{var c;return((c=new URL(u))==null?void 0:c.origin)==="https://huggingface.co"}))return;Y_();let s;if(typeof t=="string"){if(s=await lg(t),s===null)return console.error("Space not found")}else s=t;const[o,a]=await Promise.all([Sa(s.author,"user"),Sa(s.author,"org")]);s.type=o?"user":a?"org":"unknown";const l=ag(s);return ug(l),{element:l}}var fg=(t,e)=>cg(t);const{document:Ii}=Qu;function La(t,e,n){const i=t.slice();return i[38]=e[n],i}function Ra(t){let e,n=Ot(t[13].stylesheets),i=[];for(let r=0;r<n.length;r+=1)i[r]=Ia(La(t,n,r));return{c(){for(let r=0;r<i.length;r+=1)i[r].c();e=fe()},l(r){for(let s=0;s<i.length;s+=1)i[s].l(r);e=fe()},m(r,s){for(let o=0;o<i.length;o+=1)i[o]&&i[o].m(r,s);R(r,e,s)},p(r,s){if(s[0]&8192){n=Ot(r[13].stylesheets);let o;for(o=0;o<n.length;o+=1){const a=La(r,n,o);i[o]?i[o].p(a,s):(i[o]=Ia(a),i[o].c(),i[o].m(e.parentNode,e))}for(;o<i.length;o+=1)i[o].d(1);i.length=n.length}},d(r){r&&v(e),Qi(i,r)}}}function Oa(t){let e,n;return{c(){e=Z("link"),this.h()},l(i){e=X(i,"LINK",{rel:!0,href:!0}),this.h()},h(){_(e,"rel","stylesheet"),_(e,"href",n=t[38])},m(i,r){R(i,e,r)},p(i,r){r[0]&8192&&n!==(n=i[38])&&_(e,"href",n)},d(i){i&&v(e)}}}function Ia(t){let e=t[38].startsWith("http:")||t[38].startsWith("https:"),n,i=e&&Oa(t);return{c(){i&&i.c(),n=fe()},l(r){i&&i.l(r),n=fe()},m(r,s){i&&i.m(r,s),R(r,n,s)},p(r,s){s[0]&8192&&(e=r[38].startsWith("http:")||r[38].startsWith("https:")),e?i?i.p(r,s):(i=Oa(r),i.c(),i.m(n.parentNode,n)):i&&(i.d(1),i=null)},d(r){r&&v(n),i&&i.d(r)}}}function hg(t){let e,n,i,r,s,o;const a=[{app:t[11]},t[13],{fill_height:!t[5]&&t[13].fill_height},{theme_mode:t[14]},{control_page_title:t[6]},{target:t[9]},{autoscroll:t[1]},{show_footer:!t[5]},{app_mode:t[4]},{version:t[2]},{search_params:t[16].url.searchParams},{initial_layout:t[0].layout}];function l(d){t[19](d)}function u(d){t[20](d)}function c(d){t[21](d)}var h=t[0].Render;function f(d,m){let p={};for(let b=0;b<a.length;b+=1)p=It(p,a[b]);return m!==void 0&&m[0]&92791&&(p=It(p,un(a,[m[0]&2048&&{app:d[11]},m[0]&8192&&Bn(d[13]),m[0]&8224&&{fill_height:!d[5]&&d[13].fill_height},m[0]&16384&&{theme_mode:d[14]},m[0]&64&&{control_page_title:d[6]},m[0]&512&&{target:d[9]},m[0]&2&&{autoscroll:d[1]},m[0]&32&&{show_footer:!d[5]},m[0]&16&&{app_mode:d[4]},m[0]&4&&{version:d[2]},m[0]&65536&&{search_params:d[16].url.searchParams},m[0]&1&&{initial_layout:d[0].layout}]))),d[15]!==void 0&&(p.ready=d[15]),d[10]!==void 0&&(p.render_complete=d[10]),d[12]!==void 0&&(p.add_new_message=d[12]),{props:p}}return h&&(e=Dt(h,f(t)),Je.push(()=>mt(e,"ready",l)),Je.push(()=>mt(e,"render_complete",u)),Je.push(()=>mt(e,"add_new_message",c))),{c(){e&&ge(e.$$.fragment),s=fe()},l(d){e&&Re(e.$$.fragment,d),s=fe()},m(d,m){e&&be(e,d,m),R(d,s,m),o=!0},p(d,m){if(m[0]&1&&h!==(h=d[0].Render)){if(e){Be();const p=e;j(p.$$.fragment,1,0,()=>{we(p,1)}),Ne()}h?(e=Dt(h,f(d,m)),Je.push(()=>mt(e,"ready",l)),Je.push(()=>mt(e,"render_complete",u)),Je.push(()=>mt(e,"add_new_message",c)),ge(e.$$.fragment),B(e.$$.fragment,1),be(e,s.parentNode,s)):e=null}else if(h){const p=m[0]&92791?un(a,[m[0]&2048&&{app:d[11]},m[0]&8192&&Bn(d[13]),m[0]&8224&&{fill_height:!d[5]&&d[13].fill_height},m[0]&16384&&{theme_mode:d[14]},m[0]&64&&{control_page_title:d[6]},m[0]&512&&{target:d[9]},m[0]&2&&{autoscroll:d[1]},m[0]&32&&{show_footer:!d[5]},m[0]&16&&{app_mode:d[4]},m[0]&4&&{version:d[2]},m[0]&65536&&{search_params:d[16].url.searchParams},m[0]&1&&{initial_layout:d[0].layout}]):{};!n&&m[0]&32768&&(n=!0,p.ready=d[15],xt(()=>n=!1)),!i&&m[0]&1024&&(i=!0,p.render_complete=d[10],xt(()=>i=!1)),!r&&m[0]&4096&&(r=!0,p.add_new_message=d[12],xt(()=>r=!1)),e.$set(p)}},i(d){o||(e&&B(e.$$.fragment,d),o=!0)},o(d){e&&j(e.$$.fragment,d),o=!1},d(d){d&&v(s),e&&we(e,d)}}}function dg(t){let e,n,i;var r=t[0].Render;function s(o,a){return{props:{auth_message:o[13].auth_message,root:o[13].root,space_id:o[8],app_mode:o[4]}}}return r&&(e=Dt(r,s(t))),{c(){e&&ge(e.$$.fragment),n=fe()},l(o){e&&Re(e.$$.fragment,o),n=fe()},m(o,a){e&&be(e,o,a),R(o,n,a),i=!0},p(o,a){if(a[0]&1&&r!==(r=o[0].Render)){if(e){Be();const l=e;j(l.$$.fragment,1,0,()=>{we(l,1)}),Ne()}r?(e=Dt(r,s(o)),ge(e.$$.fragment),B(e.$$.fragment,1),be(e,n.parentNode,n)):e=null}else if(r){const l={};a[0]&8192&&(l.auth_message=o[13].auth_message),a[0]&8192&&(l.root=o[13].root),a[0]&256&&(l.space_id=o[8]),a[0]&16&&(l.app_mode=o[4]),e.$set(l)}},i(o){i||(e&&B(e.$$.fragment,o),i=!0)},o(o){e&&j(e.$$.fragment,o),i=!1},d(o){o&&v(n),e&&we(e,o)}}}function pg(t){let e,n,i,r;const s=[dg,hg],o=[];function a(l,u){var c;return(c=l[13])!=null&&c.auth_required?0:l[13]&&l[11]?1:-1}return~(e=a(t))&&(n=o[e]=s[e](t)),{c(){n&&n.c(),i=fe()},l(l){n&&n.l(l),i=fe()},m(l,u){~e&&o[e].m(l,u),R(l,i,u),r=!0},p(l,u){let c=e;e=a(l),e===c?~e&&o[e].p(l,u):(n&&(Be(),j(o[c],1,1,()=>{o[c]=null}),Ne()),~e?(n=o[e],n?n.p(l,u):(n=o[e]=s[e](l),n.c()),B(n,1),n.m(i.parentNode,i)):n=null)},i(l){r||(B(n),r=!0)},o(l){j(n),r=!1},d(l){l&&v(i),~e&&o[e].d(l)}}}function mg(t){var h,f;let e,n,i,r,s,o,a,l=((h=t[13])==null?void 0:h.stylesheets)&&Ra(t);function u(d){t[22](d)}let c={display:t[7]&&t[5],is_embed:t[5],info:!1,version:t[2],initial_height:t[3],space:t[8],pages:t[13].pages,current_page:t[13].current_page,root:t[13].root,loaded:bg==="complete",fill_width:((f=t[13])==null?void 0:f.fill_width)||!1,$$slots:{default:[pg]},$$scope:{ctx:t}};return t[9]!==void 0&&(c.wrapper=t[9]),s=new Kf({props:c}),Je.push(()=>mt(s,"wrapper",u)),{c(){e=Z("link"),l&&l.c(),i=Z("link"),r=oe(),ge(s.$$.fragment),this.h()},l(d){const m=Ua("svelte-ky6cqz",Ii.head);e=X(m,"LINK",{rel:!0,href:!0}),l&&l.l(m),i=X(m,"LINK",{rel:!0,href:!0}),m.forEach(v),r=ae(d),Re(s.$$.fragment,d),this.h()},h(){var d;_(e,"rel","stylesheet"),_(e,"href",n="./theme.css?v="+((d=t[13])==null?void 0:d.theme_hash)),_(i,"rel","manifest"),_(i,"href","/manifest.json")},m(d,m){M(Ii.head,e),l&&l.m(Ii.head,null),M(Ii.head,i),R(d,r,m),be(s,d,m),a=!0},p(d,m){var b,D,y;(!a||m[0]&8192&&n!==(n="./theme.css?v="+((b=d[13])==null?void 0:b.theme_hash)))&&_(e,"href",n),(D=d[13])!=null&&D.stylesheets?l?l.p(d,m):(l=Ra(d),l.c(),l.m(i.parentNode,i)):l&&(l.d(1),l=null);const p={};m[0]&160&&(p.display=d[7]&&d[5]),m[0]&32&&(p.is_embed=d[5]),m[0]&4&&(p.version=d[2]),m[0]&8&&(p.initial_height=d[3]),m[0]&256&&(p.space=d[8]),m[0]&8192&&(p.pages=d[13].pages),m[0]&8192&&(p.current_page=d[13].current_page),m[0]&8192&&(p.root=d[13].root),m[0]&8192&&(p.fill_width=((y=d[13])==null?void 0:y.fill_width)||!1),m[0]&130935|m[1]&1024&&(p.$$scope={dirty:m,ctx:d}),!o&&m[0]&512&&(o=!0,p.wrapper=d[9],xt(()=>o=!1)),s.$set(p)},i(d){a||(B(s.$$.fragment,d),a=!0)},o(d){j(s.$$.fragment,d),a=!1},d(d){d&&v(r),v(e),l&&l.d(d),v(i),we(s,d)}}}let _g=-1;function gg(){const t=Zt({}),e=new Map,n=new IntersectionObserver(r=>{r.forEach(s=>{if(s.isIntersecting){let o=e.get(s.target);o!==void 0&&t.update(a=>({...a,[o]:!0}))}})});function i(r,s){e.set(s,r),n.observe(s)}return{register:i,subscribe:t.subscribe}}let bg="complete";async function wg(t){if(t){const e=new DOMParser,n=Array.from(e.parseFromString(t,"text/html").head.children);if(n)for(let i of n){let r=document.createElement(i.tagName);if(Array.from(i.attributes).forEach(s=>{r.setAttribute(s.name,s.value)}),r.textContent=i.textContent,r.tagName=="META"){const s=r.getAttribute("property"),o=r.getAttribute("name");if(s||o){const l=Array.from(document.head.getElementsByTagName("meta")??[]).find(u=>s&&u.getAttribute("property")===s||o&&u.getAttribute("name")===o?!u.isEqualNode(r):!1);if(l){document.head.replaceChild(r,l);continue}}}document.head.appendChild(r)}}}function Eg(t,e,n){let i,r;Rt(t,$u,L=>n(16,r=L));const s=zn();let{data:o}=e,{autoscroll:a=!1}=e,{version:l="5-39-0"}=e,{initial_height:u}=e,{app_mode:c=!0}=e,{is_embed:h=!1}=e,{theme_mode:f=null}=e,{control_page_title:d=!0}=e,{container:m}=e,p;function b(L){let k;const V=new URL(window.location.toString()).searchParams.get("__theme");return k=f||V||"system",k==="dark"||k==="light"?y(L,k):k=D(L),k}function D(L){var V;const k=J();(V=window==null?void 0:window.matchMedia("(prefers-color-scheme: dark)"))==null||V.addEventListener("change",J);function J(){var q;let $=(q=window==null?void 0:window.matchMedia)!=null&&q.call(window,"(prefers-color-scheme: dark)").matches?"dark":"light";return y(L,$),$}return k}function y(L,k){const J=h?L.parentElement:document.body,V=h?L:L.parentElement;V.style.background="var(--body-background-fill)",J.classList.add("theme-loaded"),k==="dark"?J.classList.add("dark"):J.classList.remove("dark")}let g;g=b(document.body);let{space:A}=e,w=_g++,E,S=!1,z=!1,C={register:()=>{},subscribe:Zt({}).subscribe},N=o.app;function re(L){}let x=!1;gn(async()=>{if(n(13,i=o.config),window.gradio_config=i,window.gradio_config=o.config,n(13,i=o.config),i.deep_link_state==="invalid"&&n(18,x=!0),!N.config)throw new Error("Could not resolve app config");window.__gradio_space__=i.space_id,window.__gradio_session_hash__=N.session_hash,window.__is_colab__=i.is_colab,await wg(i.head);const L="supports-zerogpu-headers";window.addEventListener("message",V=>{V.data===L&&(window.supports_zerogpu_headers=!0)});const k=window.location.hostname,J=k.includes(".dev.")?`https://moon-${k.split(".")[1]}.dev.spaces.huggingface.tech`:"https://huggingface.co";window.parent.postMessage(L,J),s("loaded"),i.dev_mode&&setTimeout(()=>{const{host:V}=new URL(o.api_url);let $=new URL(`http://${V}${N.api_prefix}/dev/reload`);p=new EventSource($),p.addEventListener("error",async q=>{let Ce=q.data;Ce&&(le("Error","Error reloading app","error"),console.error(JSON.parse(Ce)))}),p.addEventListener("reload",async q=>{if(N.close(),n(11,N=await qi.connect(o.api_url,{status_callback:re,with_null_state:!0,events:["data","log","status","render"],session_hash:N.session_hash})),!N.config)throw new Error("Could not resolve app config");n(13,i=N.config),window.__gradio_space__=i.space_id})},200)});let le;gn(async()=>{C=gg(),C.register(w,E)});let me;async function ke(L,k){if(L&&!k&&window.self===window.top){me&&(me.remove(),me=void 0);const J=await fg(L);J&&(me=J.element)}}Na(()=>{me==null||me.remove()});function ue(L){S=L,n(15,S)}function Q(L){z=L,n(10,z)}function O(L){le=L,n(12,le)}function I(L){E=L,n(9,E)}return t.$$set=L=>{"data"in L&&n(0,o=L.data),"autoscroll"in L&&n(1,a=L.autoscroll),"version"in L&&n(2,l=L.version),"initial_height"in L&&n(3,u=L.initial_height),"app_mode"in L&&n(4,c=L.app_mode),"is_embed"in L&&n(5,h=L.is_embed),"theme_mode"in L&&n(17,f=L.theme_mode),"control_page_title"in L&&n(6,d=L.control_page_title),"container"in L&&n(7,m=L.container),"space"in L&&n(8,A=L.space)},t.$$.update=()=>{var L;t.$$.dirty[0]&1&&n(13,i=o.config),t.$$.dirty[0]&8192&&i!=null&&i.app_id&&i.app_id,t.$$.dirty[0]&266240&&le&&x&&(le("Error","Deep link was not valid","error"),n(18,x=!1)),t.$$.dirty[0]&1536&&z&&E.dispatchEvent(new CustomEvent("render",{bubbles:!0,cancelable:!1,composed:!0})),t.$$.dirty[0]&2080&&N!=null&&N.config&&tc&&ke((L=N==null?void 0:N.config)==null?void 0:L.space_id,h)},[o,a,l,u,c,h,d,m,A,E,z,N,le,i,g,S,r,f,x,ue,Q,O,I]}class Vg extends qe{constructor(e){super(),We(this,e,Eg,mg,Xe,{data:0,autoscroll:1,version:2,initial_height:3,app_mode:4,is_embed:5,theme_mode:17,control_page_title:6,container:7,space:8},null,[-1,-1])}}export{ui as $,xg as A,ho as B,_p as C,ks as D,Kf as E,Ig as F,Rg as G,pp as H,hp as I,bp as J,xm as K,Ad as L,ip as M,yn as N,$n as O,Fp as P,Sp as Q,Hg as R,Xp as S,Am as T,Og as U,Sg as V,Mm as W,Fg as X,rr as Y,Vg as Z,Gg as _,gh as a,bh as b,hl as c,Ng as d,wh as e,ja as f,Mg as g,vr as h,Pg as i,A_ as j,S_ as k,Bg as l,Tg as m,C_ as n,M_ as o,Gs as p,op as q,Ru as r,xl as s,eo as t,Lg as u,Cg as v,Lu as w,L_ as x,up as y,Ou as z};
//# sourceMappingURL=2.B2AoQPnG.js.map
