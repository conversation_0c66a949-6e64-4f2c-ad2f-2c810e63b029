import{b as l,g as i}from"./KHR_interactivity-DTxiAnOo.js";import{R as r,b as u,j as h,F as o}from"./declarationMapper-BZjsjg7g.js";import{R as d}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class s extends l{constructor(e){super(e),this.startIndex=this.registerDataInput("startIndex",r,0),this.endIndex=this.registerDataInput("endIndex",r),this.step=this.registerDataInput("step",u,1),this.index=this.registerDataOutput("index",h,new o(i(e?.initialIndex??0))),this.executionFlow=this._registerSignalOutput("executionFlow"),this.completed=this._registerSignalOutput("completed"),this._unregisterSignalOutput("out")}_execute(e){const n=i(this.startIndex.getValue(e)),p=this.step.getValue(e);let a=i(this.endIndex.getValue(e));for(let t=n;t<a&&(this.index.setValue(new o(t),e),this.executionFlow._activateSignal(e),a=i(this.endIndex.getValue(e)),!(t>s.MaxLoopIterations));t+=p);this.completed._activateSignal(e)}getClassName(){return"FlowGraphForLoopBlock"}}s.MaxLoopIterations=1e3;d("FlowGraphForLoopBlock",s);export{s as FlowGraphForLoopBlock};
//# sourceMappingURL=flowGraphForLoopBlock-B8jYUBDQ.js.map
