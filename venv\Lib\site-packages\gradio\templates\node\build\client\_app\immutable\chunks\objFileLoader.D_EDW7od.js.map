{"version": 3, "file": "objFileLoader.D_EDW7od.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/OBJ/mtlFileLoader.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/OBJ/solidParser.js", "../../../../../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/OBJ/objFileLoader.js"], "sourcesContent": ["import { Color3 } from \"@babylonjs/core/Maths/math.color.js\";\nimport { Texture } from \"@babylonjs/core/Materials/Textures/texture.js\";\nimport { StandardMaterial } from \"@babylonjs/core/Materials/standardMaterial.js\";\n/**\n * Class reading and parsing the MTL file bundled with the obj file.\n */\nexport class MTLFileLoader {\n    constructor() {\n        /**\n         * All material loaded from the mtl will be set here\n         */\n        this.materials = [];\n    }\n    /**\n     * This function will read the mtl file and create each material described inside\n     * This function could be improve by adding :\n     * -some component missing (Ni, Tf...)\n     * -including the specific options available\n     *\n     * @param scene defines the scene the material will be created in\n     * @param data defines the mtl data to parse\n     * @param rootUrl defines the rooturl to use in order to load relative dependencies\n     * @param assetContainer defines the asset container to store the material in (can be null)\n     */\n    parseMTL(scene, data, rootUrl, assetContainer) {\n        if (data instanceof ArrayBuffer) {\n            return;\n        }\n        //Split the lines from the file\n        const lines = data.split(\"\\n\");\n        // whitespace char ie: [ \\t\\r\\n\\f]\n        const delimiter_pattern = /\\s+/;\n        //Array with RGB colors\n        let color;\n        //New material\n        let material = null;\n        //Look at each line\n        for (let i = 0; i < lines.length; i++) {\n            const line = lines[i].trim();\n            // Blank line or comment\n            if (line.length === 0 || line.charAt(0) === \"#\") {\n                continue;\n            }\n            //Get the first parameter (keyword)\n            const pos = line.indexOf(\" \");\n            let key = pos >= 0 ? line.substring(0, pos) : line;\n            key = key.toLowerCase();\n            //Get the data following the key\n            const value = pos >= 0 ? line.substring(pos + 1).trim() : \"\";\n            //This mtl keyword will create the new material\n            if (key === \"newmtl\") {\n                //Check if it is the first material.\n                // Materials specifications are described after this keyword.\n                if (material) {\n                    //Add the previous material in the material array.\n                    this.materials.push(material);\n                }\n                //Create a new material.\n                // value is the name of the material read in the mtl file\n                scene._blockEntityCollection = !!assetContainer;\n                material = new StandardMaterial(value, scene);\n                material._parentContainer = assetContainer;\n                scene._blockEntityCollection = false;\n            }\n            else if (key === \"kd\" && material) {\n                // Diffuse color (color under white light) using RGB values\n                //value  = \"r g b\"\n                color = value.split(delimiter_pattern, 3).map(parseFloat);\n                //color = [r,g,b]\n                //Set tghe color into the material\n                material.diffuseColor = Color3.FromArray(color);\n            }\n            else if (key === \"ka\" && material) {\n                // Ambient color (color under shadow) using RGB values\n                //value = \"r g b\"\n                color = value.split(delimiter_pattern, 3).map(parseFloat);\n                //color = [r,g,b]\n                //Set tghe color into the material\n                material.ambientColor = Color3.FromArray(color);\n            }\n            else if (key === \"ks\" && material) {\n                // Specular color (color when light is reflected from shiny surface) using RGB values\n                //value = \"r g b\"\n                color = value.split(delimiter_pattern, 3).map(parseFloat);\n                //color = [r,g,b]\n                //Set the color into the material\n                material.specularColor = Color3.FromArray(color);\n            }\n            else if (key === \"ke\" && material) {\n                // Emissive color using RGB values\n                color = value.split(delimiter_pattern, 3).map(parseFloat);\n                material.emissiveColor = Color3.FromArray(color);\n            }\n            else if (key === \"ns\" && material) {\n                //value = \"Integer\"\n                material.specularPower = parseFloat(value);\n            }\n            else if (key === \"d\" && material) {\n                //d is dissolve for current material. It mean alpha for BABYLON\n                material.alpha = parseFloat(value);\n                //Texture\n                //This part can be improved by adding the possible options of texture\n            }\n            else if (key === \"map_ka\" && material) {\n                // ambient texture map with a loaded image\n                //We must first get the folder of the image\n                material.ambientTexture = MTLFileLoader._GetTexture(rootUrl, value, scene);\n            }\n            else if (key === \"map_kd\" && material) {\n                // Diffuse texture map with a loaded image\n                material.diffuseTexture = MTLFileLoader._GetTexture(rootUrl, value, scene);\n            }\n            else if (key === \"map_ks\" && material) {\n                // Specular texture map with a loaded image\n                //We must first get the folder of the image\n                material.specularTexture = MTLFileLoader._GetTexture(rootUrl, value, scene);\n            }\n            else if (key === \"map_ns\") {\n                //Specular\n                //Specular highlight component\n                //We must first get the folder of the image\n                //\n                //Not supported by BABYLON\n                //\n                //    continue;\n            }\n            else if (key === \"map_bump\" && material) {\n                //The bump texture\n                const values = value.split(delimiter_pattern);\n                const bumpMultiplierIndex = values.indexOf(\"-bm\");\n                let bumpMultiplier = null;\n                if (bumpMultiplierIndex >= 0) {\n                    bumpMultiplier = values[bumpMultiplierIndex + 1];\n                    values.splice(bumpMultiplierIndex, 2); // remove\n                }\n                material.bumpTexture = MTLFileLoader._GetTexture(rootUrl, values.join(\" \"), scene);\n                if (material.bumpTexture && bumpMultiplier !== null) {\n                    material.bumpTexture.level = parseFloat(bumpMultiplier);\n                }\n            }\n            else if (key === \"map_d\" && material) {\n                // The dissolve of the material\n                material.opacityTexture = MTLFileLoader._GetTexture(rootUrl, value, scene);\n                //Options for illumination\n            }\n            else if (key === \"illum\") {\n                //Illumination\n                if (value === \"0\") {\n                    //That mean Kd == Kd\n                }\n                else if (value === \"1\") {\n                    //Color on and Ambient on\n                }\n                else if (value === \"2\") {\n                    //Highlight on\n                }\n                else if (value === \"3\") {\n                    //Reflection on and Ray trace on\n                }\n                else if (value === \"4\") {\n                    //Transparency: Glass on, Reflection: Ray trace on\n                }\n                else if (value === \"5\") {\n                    //Reflection: Fresnel on and Ray trace on\n                }\n                else if (value === \"6\") {\n                    //Transparency: Refraction on, Reflection: Fresnel off and Ray trace on\n                }\n                else if (value === \"7\") {\n                    //Transparency: Refraction on, Reflection: Fresnel on and Ray trace on\n                }\n                else if (value === \"8\") {\n                    //Reflection on and Ray trace off\n                }\n                else if (value === \"9\") {\n                    //Transparency: Glass on, Reflection: Ray trace off\n                }\n                else if (value === \"10\") {\n                    //Casts shadows onto invisible surfaces\n                }\n            }\n            else {\n                // console.log(\"Unhandled expression at line : \" + i +'\\n' + \"with value : \" + line);\n            }\n        }\n        //At the end of the file, add the last material\n        if (material) {\n            this.materials.push(material);\n        }\n    }\n    /**\n     * Gets the texture for the material.\n     *\n     * If the material is imported from input file,\n     * We sanitize the url to ensure it takes the texture from aside the material.\n     *\n     * @param rootUrl The root url to load from\n     * @param value The value stored in the mtl\n     * @param scene\n     * @returns The Texture\n     */\n    static _GetTexture(rootUrl, value, scene) {\n        if (!value) {\n            return null;\n        }\n        let url = rootUrl;\n        // Load from input file.\n        if (rootUrl === \"file:\") {\n            let lastDelimiter = value.lastIndexOf(\"\\\\\");\n            if (lastDelimiter === -1) {\n                lastDelimiter = value.lastIndexOf(\"/\");\n            }\n            if (lastDelimiter > -1) {\n                url += value.substring(lastDelimiter + 1);\n            }\n            else {\n                url += value;\n            }\n        }\n        // Not from input file.\n        else {\n            url += value;\n        }\n        return new Texture(url, scene, false, MTLFileLoader.INVERT_TEXTURE_Y);\n    }\n}\n/**\n * Invert Y-Axis of referenced textures on load\n */\nMTLFileLoader.INVERT_TEXTURE_Y = true;\n//# sourceMappingURL=mtlFileLoader.js.map", "import { VertexBuffer } from \"@babylonjs/core/Buffers/buffer.js\";\nimport { StandardMaterial } from \"@babylonjs/core/Materials/standardMaterial.js\";\nimport { Color3, Color4 } from \"@babylonjs/core/Maths/math.color.js\";\nimport { Vector2, Vector3 } from \"@babylonjs/core/Maths/math.vector.js\";\nimport { Geometry } from \"@babylonjs/core/Meshes/geometry.js\";\nimport { Mesh } from \"@babylonjs/core/Meshes/mesh.js\";\nimport { VertexData } from \"@babylonjs/core/Meshes/mesh.vertexData.js\";\nimport { Logger } from \"@babylonjs/core/Misc/logger.js\";\n/**\n * Class used to load mesh data from OBJ content\n */\nexport class SolidParser {\n    /**\n     * Creates a new SolidParser\n     * @param materialToUse defines the array to fill with the list of materials to use (it will be filled by the parse function)\n     * @param babylonMeshesArray defines the array to fill with the list of loaded meshes (it will be filled by the parse function)\n     * @param loadingOptions defines the loading options to use\n     */\n    constructor(materialToUse, babylonMeshesArray, loadingOptions) {\n        this._positions = []; //values for the positions of vertices\n        this._normals = []; //Values for the normals\n        this._uvs = []; //Values for the textures\n        this._colors = [];\n        this._extColors = []; //Extension color\n        this._meshesFromObj = []; //[mesh] Contains all the obj meshes\n        this._indicesForBabylon = []; //The list of indices for VertexData\n        this._wrappedPositionForBabylon = []; //The list of position in vectors\n        this._wrappedUvsForBabylon = []; //Array with all value of uvs to match with the indices\n        this._wrappedColorsForBabylon = []; // Array with all color values to match with the indices\n        this._wrappedNormalsForBabylon = []; //Array with all value of normals to match with the indices\n        this._tuplePosNorm = []; //Create a tuple with indice of Position, Normal, UV  [pos, norm, uvs]\n        this._curPositionInIndices = 0;\n        this._hasMeshes = false; //Meshes are defined in the file\n        this._unwrappedPositionsForBabylon = []; //Value of positionForBabylon w/o Vector3() [x,y,z]\n        this._unwrappedColorsForBabylon = []; // Value of colorForBabylon w/o Color4() [r,g,b,a]\n        this._unwrappedNormalsForBabylon = []; //Value of normalsForBabylon w/o Vector3()  [x,y,z]\n        this._unwrappedUVForBabylon = []; //Value of uvsForBabylon w/o Vector3()      [x,y,z]\n        this._triangles = []; //Indices from new triangles coming from polygons\n        this._materialNameFromObj = \"\"; //The name of the current material\n        this._objMeshName = \"\"; //The name of the current obj mesh\n        this._increment = 1; //Id for meshes created by the multimaterial\n        this._isFirstMaterial = true;\n        this._grayColor = new Color4(0.5, 0.5, 0.5, 1);\n        this._hasLineData = false; //If this mesh has line segment(l) data\n        this._materialToUse = materialToUse;\n        this._babylonMeshesArray = babylonMeshesArray;\n        this._loadingOptions = loadingOptions;\n    }\n    /**\n     * Search for obj in the given array.\n     * This function is called to check if a couple of data already exists in an array.\n     *\n     * If found, returns the index of the founded tuple index. Returns -1 if not found\n     * @param arr Array<{ normals: Array<number>, idx: Array<number> }>\n     * @param obj Array<number>\n     * @returns {boolean}\n     */\n    _isInArray(arr, obj) {\n        if (!arr[obj[0]]) {\n            arr[obj[0]] = { normals: [], idx: [] };\n        }\n        const idx = arr[obj[0]].normals.indexOf(obj[1]);\n        return idx === -1 ? -1 : arr[obj[0]].idx[idx];\n    }\n    _isInArrayUV(arr, obj) {\n        if (!arr[obj[0]]) {\n            arr[obj[0]] = { normals: [], idx: [], uv: [] };\n        }\n        const idx = arr[obj[0]].normals.indexOf(obj[1]);\n        if (idx != 1 && obj[2] === arr[obj[0]].uv[idx]) {\n            return arr[obj[0]].idx[idx];\n        }\n        return -1;\n    }\n    /**\n     * This function set the data for each triangle.\n     * Data are position, normals and uvs\n     * If a tuple of (position, normal) is not set, add the data into the corresponding array\n     * If the tuple already exist, add only their indice\n     *\n     * @param indicePositionFromObj Integer The index in positions array\n     * @param indiceUvsFromObj Integer The index in uvs array\n     * @param indiceNormalFromObj Integer The index in normals array\n     * @param positionVectorFromOBJ Vector3 The value of position at index objIndice\n     * @param textureVectorFromOBJ Vector3 The value of uvs\n     * @param normalsVectorFromOBJ Vector3 The value of normals at index objNormale\n     * @param positionColorsFromOBJ\n     */\n    _setData(indicePositionFromObj, indiceUvsFromObj, indiceNormalFromObj, positionVectorFromOBJ, textureVectorFromOBJ, normalsVectorFromOBJ, positionColorsFromOBJ) {\n        //Check if this tuple already exists in the list of tuples\n        let _index;\n        if (this._loadingOptions.optimizeWithUV) {\n            _index = this._isInArrayUV(this._tuplePosNorm, [indicePositionFromObj, indiceNormalFromObj, indiceUvsFromObj]);\n        }\n        else {\n            _index = this._isInArray(this._tuplePosNorm, [indicePositionFromObj, indiceNormalFromObj]);\n        }\n        //If it not exists\n        if (_index === -1) {\n            //Add an new indice.\n            //The array of indices is only an array with his length equal to the number of triangles - 1.\n            //We add vertices data in this order\n            this._indicesForBabylon.push(this._wrappedPositionForBabylon.length);\n            //Push the position of vertice for Babylon\n            //Each element is a Vector3(x,y,z)\n            this._wrappedPositionForBabylon.push(positionVectorFromOBJ);\n            //Push the uvs for Babylon\n            //Each element is a Vector2(u,v)\n            //If the UVs are missing, set (u,v)=(0,0)\n            textureVectorFromOBJ = textureVectorFromOBJ ?? new Vector2(0, 0);\n            this._wrappedUvsForBabylon.push(textureVectorFromOBJ);\n            //Push the normals for Babylon\n            //Each element is a Vector3(x,y,z)\n            this._wrappedNormalsForBabylon.push(normalsVectorFromOBJ);\n            if (positionColorsFromOBJ !== undefined) {\n                //Push the colors for Babylon\n                //Each element is a BABYLON.Color4(r,g,b,a)\n                this._wrappedColorsForBabylon.push(positionColorsFromOBJ);\n            }\n            //Add the tuple in the comparison list\n            this._tuplePosNorm[indicePositionFromObj].normals.push(indiceNormalFromObj);\n            this._tuplePosNorm[indicePositionFromObj].idx.push(this._curPositionInIndices++);\n            if (this._loadingOptions.optimizeWithUV) {\n                this._tuplePosNorm[indicePositionFromObj].uv.push(indiceUvsFromObj);\n            }\n        }\n        else {\n            //The tuple already exists\n            //Add the index of the already existing tuple\n            //At this index we can get the value of position, normal, color and uvs of vertex\n            this._indicesForBabylon.push(_index);\n        }\n    }\n    /**\n     * Transform Vector() and BABYLON.Color() objects into numbers in an array\n     */\n    _unwrapData() {\n        try {\n            //Every array has the same length\n            for (let l = 0; l < this._wrappedPositionForBabylon.length; l++) {\n                //Push the x, y, z values of each element in the unwrapped array\n                this._unwrappedPositionsForBabylon.push(this._wrappedPositionForBabylon[l].x * this._handednessSign, this._wrappedPositionForBabylon[l].y, this._wrappedPositionForBabylon[l].z);\n                this._unwrappedNormalsForBabylon.push(this._wrappedNormalsForBabylon[l].x * this._handednessSign, this._wrappedNormalsForBabylon[l].y, this._wrappedNormalsForBabylon[l].z);\n                this._unwrappedUVForBabylon.push(this._wrappedUvsForBabylon[l].x, this._wrappedUvsForBabylon[l].y); //z is an optional value not supported by BABYLON\n                if (this._loadingOptions.importVertexColors) {\n                    //Push the r, g, b, a values of each element in the unwrapped array\n                    this._unwrappedColorsForBabylon.push(this._wrappedColorsForBabylon[l].r, this._wrappedColorsForBabylon[l].g, this._wrappedColorsForBabylon[l].b, this._wrappedColorsForBabylon[l].a);\n                }\n            }\n            // Reset arrays for the next new meshes\n            this._wrappedPositionForBabylon.length = 0;\n            this._wrappedNormalsForBabylon.length = 0;\n            this._wrappedUvsForBabylon.length = 0;\n            this._wrappedColorsForBabylon.length = 0;\n            this._tuplePosNorm.length = 0;\n            this._curPositionInIndices = 0;\n        }\n        catch (e) {\n            throw new Error(\"Unable to unwrap data while parsing OBJ data.\");\n        }\n    }\n    /**\n     * Create triangles from polygons\n     * It is important to notice that a triangle is a polygon\n     * We get 5 patterns of face defined in OBJ File :\n     * facePattern1 = [\"1\",\"2\",\"3\",\"4\",\"5\",\"6\"]\n     * facePattern2 = [\"1/1\",\"2/2\",\"3/3\",\"4/4\",\"5/5\",\"6/6\"]\n     * facePattern3 = [\"1/1/1\",\"2/2/2\",\"3/3/3\",\"4/4/4\",\"5/5/5\",\"6/6/6\"]\n     * facePattern4 = [\"1//1\",\"2//2\",\"3//3\",\"4//4\",\"5//5\",\"6//6\"]\n     * facePattern5 = [\"-1/-1/-1\",\"-2/-2/-2\",\"-3/-3/-3\",\"-4/-4/-4\",\"-5/-5/-5\",\"-6/-6/-6\"]\n     * Each pattern is divided by the same method\n     * @param faces Array[String] The indices of elements\n     * @param v Integer The variable to increment\n     */\n    _getTriangles(faces, v) {\n        //Work for each element of the array\n        for (let faceIndex = v; faceIndex < faces.length - 1; faceIndex++) {\n            //Add on the triangle variable the indexes to obtain triangles\n            this._pushTriangle(faces, faceIndex);\n        }\n        //Result obtained after 2 iterations:\n        //Pattern1 => triangle = [\"1\",\"2\",\"3\",\"1\",\"3\",\"4\"];\n        //Pattern2 => triangle = [\"1/1\",\"2/2\",\"3/3\",\"1/1\",\"3/3\",\"4/4\"];\n        //Pattern3 => triangle = [\"1/1/1\",\"2/2/2\",\"3/3/3\",\"1/1/1\",\"3/3/3\",\"4/4/4\"];\n        //Pattern4 => triangle = [\"1//1\",\"2//2\",\"3//3\",\"1//1\",\"3//3\",\"4//4\"];\n        //Pattern5 => triangle = [\"-1/-1/-1\",\"-2/-2/-2\",\"-3/-3/-3\",\"-1/-1/-1\",\"-3/-3/-3\",\"-4/-4/-4\"];\n    }\n    /**\n     * To get color between color and extension color\n     * @param index Integer The index of the element in the array\n     * @returns value of target color\n     */\n    _getColor(index) {\n        if (this._loadingOptions.importVertexColors) {\n            return this._extColors[index] ?? this._colors[index];\n        }\n        else {\n            return undefined;\n        }\n    }\n    /**\n     * Create triangles and push the data for each polygon for the pattern 1\n     * In this pattern we get vertice positions\n     * @param face\n     * @param v\n     */\n    _setDataForCurrentFaceWithPattern1(face, v) {\n        //Get the indices of triangles for each polygon\n        this._getTriangles(face, v);\n        //For each element in the triangles array.\n        //This var could contains 1 to an infinity of triangles\n        for (let k = 0; k < this._triangles.length; k++) {\n            // Set position indice\n            const indicePositionFromObj = parseInt(this._triangles[k]) - 1;\n            this._setData(indicePositionFromObj, 0, 0, // In the pattern 1, normals and uvs are not defined\n            this._positions[indicePositionFromObj], // Get the vectors data\n            Vector2.Zero(), Vector3.Up(), // Create default vectors\n            this._getColor(indicePositionFromObj));\n        }\n        //Reset variable for the next line\n        this._triangles.length = 0;\n    }\n    /**\n     * Create triangles and push the data for each polygon for the pattern 2\n     * In this pattern we get vertice positions and uvs\n     * @param face\n     * @param v\n     */\n    _setDataForCurrentFaceWithPattern2(face, v) {\n        //Get the indices of triangles for each polygon\n        this._getTriangles(face, v);\n        for (let k = 0; k < this._triangles.length; k++) {\n            //triangle[k] = \"1/1\"\n            //Split the data for getting position and uv\n            const point = this._triangles[k].split(\"/\"); // [\"1\", \"1\"]\n            //Set position indice\n            const indicePositionFromObj = parseInt(point[0]) - 1;\n            //Set uv indice\n            const indiceUvsFromObj = parseInt(point[1]) - 1;\n            this._setData(indicePositionFromObj, indiceUvsFromObj, 0, //Default value for normals\n            this._positions[indicePositionFromObj], //Get the values for each element\n            this._uvs[indiceUvsFromObj] ?? Vector2.Zero(), Vector3.Up(), //Default value for normals\n            this._getColor(indicePositionFromObj));\n        }\n        //Reset variable for the next line\n        this._triangles.length = 0;\n    }\n    /**\n     * Create triangles and push the data for each polygon for the pattern 3\n     * In this pattern we get vertice positions, uvs and normals\n     * @param face\n     * @param v\n     */\n    _setDataForCurrentFaceWithPattern3(face, v) {\n        //Get the indices of triangles for each polygon\n        this._getTriangles(face, v);\n        for (let k = 0; k < this._triangles.length; k++) {\n            //triangle[k] = \"1/1/1\"\n            //Split the data for getting position, uv, and normals\n            const point = this._triangles[k].split(\"/\"); // [\"1\", \"1\", \"1\"]\n            // Set position indice\n            const indicePositionFromObj = parseInt(point[0]) - 1;\n            // Set uv indice\n            const indiceUvsFromObj = parseInt(point[1]) - 1;\n            // Set normal indice\n            const indiceNormalFromObj = parseInt(point[2]) - 1;\n            this._setData(indicePositionFromObj, indiceUvsFromObj, indiceNormalFromObj, this._positions[indicePositionFromObj], this._uvs[indiceUvsFromObj] ?? Vector2.Zero(), this._normals[indiceNormalFromObj] ?? Vector3.Up() //Set the vector for each component\n            );\n        }\n        //Reset variable for the next line\n        this._triangles.length = 0;\n    }\n    /**\n     * Create triangles and push the data for each polygon for the pattern 4\n     * In this pattern we get vertice positions and normals\n     * @param face\n     * @param v\n     */\n    _setDataForCurrentFaceWithPattern4(face, v) {\n        this._getTriangles(face, v);\n        for (let k = 0; k < this._triangles.length; k++) {\n            //triangle[k] = \"1//1\"\n            //Split the data for getting position and normals\n            const point = this._triangles[k].split(\"//\"); // [\"1\", \"1\"]\n            // We check indices, and normals\n            const indicePositionFromObj = parseInt(point[0]) - 1;\n            const indiceNormalFromObj = parseInt(point[1]) - 1;\n            this._setData(indicePositionFromObj, 1, //Default value for uv\n            indiceNormalFromObj, this._positions[indicePositionFromObj], //Get each vector of data\n            Vector2.Zero(), this._normals[indiceNormalFromObj], this._getColor(indicePositionFromObj));\n        }\n        //Reset variable for the next line\n        this._triangles.length = 0;\n    }\n    /*\n     * Create triangles and push the data for each polygon for the pattern 3\n     * In this pattern we get vertice positions, uvs and normals\n     * @param face\n     * @param v\n     */\n    _setDataForCurrentFaceWithPattern5(face, v) {\n        //Get the indices of triangles for each polygon\n        this._getTriangles(face, v);\n        for (let k = 0; k < this._triangles.length; k++) {\n            //triangle[k] = \"-1/-1/-1\"\n            //Split the data for getting position, uv, and normals\n            const point = this._triangles[k].split(\"/\"); // [\"-1\", \"-1\", \"-1\"]\n            // Set position indice\n            const indicePositionFromObj = this._positions.length + parseInt(point[0]);\n            // Set uv indice\n            const indiceUvsFromObj = this._uvs.length + parseInt(point[1]);\n            // Set normal indice\n            const indiceNormalFromObj = this._normals.length + parseInt(point[2]);\n            this._setData(indicePositionFromObj, indiceUvsFromObj, indiceNormalFromObj, this._positions[indicePositionFromObj], this._uvs[indiceUvsFromObj], this._normals[indiceNormalFromObj], //Set the vector for each component\n            this._getColor(indicePositionFromObj));\n        }\n        //Reset variable for the next line\n        this._triangles.length = 0;\n    }\n    _addPreviousObjMesh() {\n        //Check if it is not the first mesh. Otherwise we don't have data.\n        if (this._meshesFromObj.length > 0) {\n            //Get the previous mesh for applying the data about the faces\n            //=> in obj file, faces definition append after the name of the mesh\n            this._handledMesh = this._meshesFromObj[this._meshesFromObj.length - 1];\n            //Set the data into Array for the mesh\n            this._unwrapData();\n            if (this._loadingOptions.useLegacyBehavior) {\n                // Reverse tab. Otherwise face are displayed in the wrong sens\n                this._indicesForBabylon.reverse();\n            }\n            //Set the information for the mesh\n            //Slice the array to avoid rewriting because of the fact this is the same var which be rewrited\n            this._handledMesh.indices = this._indicesForBabylon.slice();\n            this._handledMesh.positions = this._unwrappedPositionsForBabylon.slice();\n            this._handledMesh.normals = this._unwrappedNormalsForBabylon.slice();\n            this._handledMesh.uvs = this._unwrappedUVForBabylon.slice();\n            this._handledMesh.hasLines = this._hasLineData;\n            if (this._loadingOptions.importVertexColors) {\n                this._handledMesh.colors = this._unwrappedColorsForBabylon.slice();\n            }\n            //Reset the array for the next mesh\n            this._indicesForBabylon.length = 0;\n            this._unwrappedPositionsForBabylon.length = 0;\n            this._unwrappedColorsForBabylon.length = 0;\n            this._unwrappedNormalsForBabylon.length = 0;\n            this._unwrappedUVForBabylon.length = 0;\n            this._hasLineData = false;\n        }\n    }\n    _optimizeNormals(mesh) {\n        const positions = mesh.getVerticesData(VertexBuffer.PositionKind);\n        const normals = mesh.getVerticesData(VertexBuffer.NormalKind);\n        const mapVertices = {};\n        if (!positions || !normals) {\n            return;\n        }\n        for (let i = 0; i < positions.length / 3; i++) {\n            const x = positions[i * 3 + 0];\n            const y = positions[i * 3 + 1];\n            const z = positions[i * 3 + 2];\n            const key = x + \"_\" + y + \"_\" + z;\n            let lst = mapVertices[key];\n            if (!lst) {\n                lst = [];\n                mapVertices[key] = lst;\n            }\n            lst.push(i);\n        }\n        const normal = new Vector3();\n        for (const key in mapVertices) {\n            const lst = mapVertices[key];\n            if (lst.length < 2) {\n                continue;\n            }\n            const v0Idx = lst[0];\n            for (let i = 1; i < lst.length; ++i) {\n                const vIdx = lst[i];\n                normals[v0Idx * 3 + 0] += normals[vIdx * 3 + 0];\n                normals[v0Idx * 3 + 1] += normals[vIdx * 3 + 1];\n                normals[v0Idx * 3 + 2] += normals[vIdx * 3 + 2];\n            }\n            normal.copyFromFloats(normals[v0Idx * 3 + 0], normals[v0Idx * 3 + 1], normals[v0Idx * 3 + 2]);\n            normal.normalize();\n            for (let i = 0; i < lst.length; ++i) {\n                const vIdx = lst[i];\n                normals[vIdx * 3 + 0] = normal.x;\n                normals[vIdx * 3 + 1] = normal.y;\n                normals[vIdx * 3 + 2] = normal.z;\n            }\n        }\n        mesh.setVerticesData(VertexBuffer.NormalKind, normals);\n    }\n    static _IsLineElement(line) {\n        return line.startsWith(\"l\");\n    }\n    static _IsObjectElement(line) {\n        return line.startsWith(\"o\");\n    }\n    static _IsGroupElement(line) {\n        return line.startsWith(\"g\");\n    }\n    static _GetZbrushMRGB(line, notParse) {\n        if (!line.startsWith(\"mrgb\"))\n            return null;\n        line = line.replace(\"mrgb\", \"\").trim();\n        // if include vertex color , not load mrgb anymore\n        if (notParse)\n            return [];\n        const regex = /[a-z0-9]/g;\n        const regArray = line.match(regex);\n        if (!regArray || regArray.length % 8 !== 0) {\n            return [];\n        }\n        const array = [];\n        for (let regIndex = 0; regIndex < regArray.length / 8; regIndex++) {\n            //each item is MMRRGGBB, m is material index\n            // const m = regArray[regIndex * 8 + 0] + regArray[regIndex * 8 + 1];\n            const r = regArray[regIndex * 8 + 2] + regArray[regIndex * 8 + 3];\n            const g = regArray[regIndex * 8 + 4] + regArray[regIndex * 8 + 5];\n            const b = regArray[regIndex * 8 + 6] + regArray[regIndex * 8 + 7];\n            array.push(new Color4(parseInt(r, 16) / 255, parseInt(g, 16) / 255, parseInt(b, 16) / 255, 1));\n        }\n        return array;\n    }\n    /**\n     * Function used to parse an OBJ string\n     * @param meshesNames defines the list of meshes to load (all if not defined)\n     * @param data defines the OBJ string\n     * @param scene defines the hosting scene\n     * @param assetContainer defines the asset container to load data in\n     * @param onFileToLoadFound defines a callback that will be called if a MTL file is found\n     */\n    parse(meshesNames, data, scene, assetContainer, onFileToLoadFound) {\n        //Move Santitize here to forbid delete zbrush data\n        // Sanitize data\n        data = data.replace(/#MRGB/g, \"mrgb\");\n        data = data.replace(/#.*$/gm, \"\").trim();\n        if (this._loadingOptions.useLegacyBehavior) {\n            this._pushTriangle = (faces, faceIndex) => this._triangles.push(faces[0], faces[faceIndex], faces[faceIndex + 1]);\n            this._handednessSign = 1;\n        }\n        else if (scene.useRightHandedSystem) {\n            this._pushTriangle = (faces, faceIndex) => this._triangles.push(faces[0], faces[faceIndex + 1], faces[faceIndex]);\n            this._handednessSign = 1;\n        }\n        else {\n            this._pushTriangle = (faces, faceIndex) => this._triangles.push(faces[0], faces[faceIndex], faces[faceIndex + 1]);\n            this._handednessSign = -1;\n        }\n        // Split the file into lines\n        // Preprocess line data\n        const linesOBJ = data.split(\"\\n\");\n        const lineLines = [];\n        let currentGroup = [];\n        lineLines.push(currentGroup);\n        for (let i = 0; i < linesOBJ.length; i++) {\n            const line = linesOBJ[i].trim().replace(/\\s\\s/g, \" \");\n            // Comment or newLine\n            if (line.length === 0 || line.charAt(0) === \"#\") {\n                continue;\n            }\n            if (SolidParser._IsGroupElement(line) || SolidParser._IsObjectElement(line)) {\n                currentGroup = [];\n                lineLines.push(currentGroup);\n            }\n            if (SolidParser._IsLineElement(line)) {\n                const lineValues = line.split(\" \");\n                // create line elements with two vertices only\n                for (let i = 1; i < lineValues.length - 1; i++) {\n                    currentGroup.push(`l ${lineValues[i]} ${lineValues[i + 1]}`);\n                }\n            }\n            else {\n                currentGroup.push(line);\n            }\n        }\n        const lines = lineLines.flat();\n        // Look at each line\n        for (let i = 0; i < lines.length; i++) {\n            const line = lines[i].trim().replace(/\\s\\s/g, \" \");\n            let result;\n            // Comment or newLine\n            if (line.length === 0 || line.charAt(0) === \"#\") {\n                continue;\n            }\n            else if (SolidParser.VertexPattern.test(line)) {\n                //Get information about one position possible for the vertices\n                result = line.match(/[^ ]+/g); // match will return non-null due to passing regex pattern\n                // Value of result with line: \"v 1.0 2.0 3.0\"\n                // [\"v\", \"1.0\", \"2.0\", \"3.0\"]\n                // Create a Vector3 with the position x, y, z\n                this._positions.push(new Vector3(parseFloat(result[1]), parseFloat(result[2]), parseFloat(result[3])));\n                if (this._loadingOptions.importVertexColors) {\n                    if (result.length >= 7) {\n                        const r = parseFloat(result[4]);\n                        const g = parseFloat(result[5]);\n                        const b = parseFloat(result[6]);\n                        this._colors.push(new Color4(r > 1 ? r / 255 : r, g > 1 ? g / 255 : g, b > 1 ? b / 255 : b, result.length === 7 || result[7] === undefined ? 1 : parseFloat(result[7])));\n                    }\n                    else {\n                        // TODO: maybe push NULL and if all are NULL to skip (and remove grayColor var).\n                        this._colors.push(this._grayColor);\n                    }\n                }\n            }\n            else if ((result = SolidParser.NormalPattern.exec(line)) !== null) {\n                //Create a Vector3 with the normals x, y, z\n                //Value of result\n                // [\"vn 1.0 2.0 3.0\", \"1.0\", \"2.0\", \"3.0\"]\n                //Add the Vector in the list of normals\n                this._normals.push(new Vector3(parseFloat(result[1]), parseFloat(result[2]), parseFloat(result[3])));\n            }\n            else if ((result = SolidParser.UVPattern.exec(line)) !== null) {\n                //Create a Vector2 with the normals u, v\n                //Value of result\n                // [\"vt 0.1 0.2 0.3\", \"0.1\", \"0.2\"]\n                //Add the Vector in the list of uvs\n                this._uvs.push(new Vector2(parseFloat(result[1]) * this._loadingOptions.UVScaling.x, parseFloat(result[2]) * this._loadingOptions.UVScaling.y));\n                //Identify patterns of faces\n                //Face could be defined in different type of pattern\n            }\n            else if ((result = SolidParser.FacePattern3.exec(line)) !== null) {\n                //Value of result:\n                //[\"f 1/1/1 2/2/2 3/3/3\", \"1/1/1 2/2/2 3/3/3\"...]\n                //Set the data for this face\n                this._setDataForCurrentFaceWithPattern3(result[1].trim().split(\" \"), // [\"1/1/1\", \"2/2/2\", \"3/3/3\"]\n                1);\n            }\n            else if ((result = SolidParser.FacePattern4.exec(line)) !== null) {\n                //Value of result:\n                //[\"f 1//1 2//2 3//3\", \"1//1 2//2 3//3\"...]\n                //Set the data for this face\n                this._setDataForCurrentFaceWithPattern4(result[1].trim().split(\" \"), // [\"1//1\", \"2//2\", \"3//3\"]\n                1);\n            }\n            else if ((result = SolidParser.FacePattern5.exec(line)) !== null) {\n                //Value of result:\n                //[\"f -1/-1/-1 -2/-2/-2 -3/-3/-3\", \"-1/-1/-1 -2/-2/-2 -3/-3/-3\"...]\n                //Set the data for this face\n                this._setDataForCurrentFaceWithPattern5(result[1].trim().split(\" \"), // [\"-1/-1/-1\", \"-2/-2/-2\", \"-3/-3/-3\"]\n                1);\n            }\n            else if ((result = SolidParser.FacePattern2.exec(line)) !== null) {\n                //Value of result:\n                //[\"f 1/1 2/2 3/3\", \"1/1 2/2 3/3\"...]\n                //Set the data for this face\n                this._setDataForCurrentFaceWithPattern2(result[1].trim().split(\" \"), // [\"1/1\", \"2/2\", \"3/3\"]\n                1);\n            }\n            else if ((result = SolidParser.FacePattern1.exec(line)) !== null) {\n                //Value of result\n                //[\"f 1 2 3\", \"1 2 3\"...]\n                //Set the data for this face\n                this._setDataForCurrentFaceWithPattern1(result[1].trim().split(\" \"), // [\"1\", \"2\", \"3\"]\n                1);\n                // Define a mesh or an object\n                // Each time this keyword is analyzed, create a new Object with all data for creating a babylonMesh\n            }\n            else if ((result = SolidParser.LinePattern1.exec(line)) !== null) {\n                //Value of result\n                //[\"l 1 2\"]\n                //Set the data for this face\n                this._setDataForCurrentFaceWithPattern1(result[1].trim().split(\" \"), // [\"1\", \"2\"]\n                0);\n                this._hasLineData = true;\n                // Define a mesh or an object\n                // Each time this keyword is analyzed, create a new Object with all data for creating a babylonMesh\n            }\n            else if ((result = SolidParser.LinePattern2.exec(line)) !== null) {\n                //Value of result\n                //[\"l 1/1 2/2\"]\n                //Set the data for this face\n                this._setDataForCurrentFaceWithPattern2(result[1].trim().split(\" \"), // [\"1/1\", \"2/2\"]\n                0);\n                this._hasLineData = true;\n                // Define a mesh or an object\n                // Each time this keyword is analyzed, create a new Object with all data for creating a babylonMesh\n            }\n            else if ((result = SolidParser._GetZbrushMRGB(line, !this._loadingOptions.importVertexColors))) {\n                result.forEach((element) => {\n                    this._extColors.push(element);\n                });\n            }\n            else if ((result = SolidParser.LinePattern3.exec(line)) !== null) {\n                //Value of result\n                //[\"l 1/1/1 2/2/2\"]\n                //Set the data for this face\n                this._setDataForCurrentFaceWithPattern3(result[1].trim().split(\" \"), // [\"1/1/1\", \"2/2/2\"]\n                0);\n                this._hasLineData = true;\n                // Define a mesh or an object\n                // Each time this keyword is analyzed, create a new Object with all data for creating a babylonMesh\n            }\n            else if (SolidParser.GroupDescriptor.test(line) || SolidParser.ObjectDescriptor.test(line)) {\n                // Create a new mesh corresponding to the name of the group.\n                // Definition of the mesh\n                const objMesh = {\n                    name: line.substring(2).trim(), //Set the name of the current obj mesh\n                    indices: null,\n                    positions: null,\n                    normals: null,\n                    uvs: null,\n                    colors: null,\n                    materialName: this._materialNameFromObj,\n                    isObject: SolidParser.ObjectDescriptor.test(line),\n                };\n                this._addPreviousObjMesh();\n                //Push the last mesh created with only the name\n                this._meshesFromObj.push(objMesh);\n                //Set this variable to indicate that now meshesFromObj has objects defined inside\n                this._hasMeshes = true;\n                this._isFirstMaterial = true;\n                this._increment = 1;\n                //Keyword for applying a material\n            }\n            else if (SolidParser.UseMtlDescriptor.test(line)) {\n                //Get the name of the material\n                this._materialNameFromObj = line.substring(7).trim();\n                //If this new material is in the same mesh\n                if (!this._isFirstMaterial || !this._hasMeshes) {\n                    //Set the data for the previous mesh\n                    this._addPreviousObjMesh();\n                    //Create a new mesh\n                    const objMesh = \n                    //Set the name of the current obj mesh\n                    {\n                        name: (this._objMeshName || \"mesh\") + \"_mm\" + this._increment.toString(), //Set the name of the current obj mesh\n                        indices: null,\n                        positions: null,\n                        normals: null,\n                        uvs: null,\n                        colors: null,\n                        materialName: this._materialNameFromObj,\n                        isObject: false,\n                    };\n                    this._increment++;\n                    //If meshes are already defined\n                    this._meshesFromObj.push(objMesh);\n                    this._hasMeshes = true;\n                }\n                //Set the material name if the previous line define a mesh\n                if (this._hasMeshes && this._isFirstMaterial) {\n                    //Set the material name to the previous mesh (1 material per mesh)\n                    this._meshesFromObj[this._meshesFromObj.length - 1].materialName = this._materialNameFromObj;\n                    this._isFirstMaterial = false;\n                }\n                // Keyword for loading the mtl file\n            }\n            else if (SolidParser.MtlLibGroupDescriptor.test(line)) {\n                // Get the name of mtl file\n                onFileToLoadFound(line.substring(7).trim());\n                // Apply smoothing\n            }\n            else if (SolidParser.SmoothDescriptor.test(line)) {\n                // smooth shading => apply smoothing\n                // Today I don't know it work with babylon and with obj.\n                // With the obj file  an integer is set\n            }\n            else {\n                //If there is another possibility\n                Logger.Log(\"Unhandled expression at line : \" + line);\n            }\n        }\n        // At the end of the file, add the last mesh into the meshesFromObj array\n        if (this._hasMeshes) {\n            // Set the data for the last mesh\n            this._handledMesh = this._meshesFromObj[this._meshesFromObj.length - 1];\n            if (this._loadingOptions.useLegacyBehavior) {\n                //Reverse indices for displaying faces in the good sense\n                this._indicesForBabylon.reverse();\n            }\n            //Get the good array\n            this._unwrapData();\n            //Set array\n            this._handledMesh.indices = this._indicesForBabylon;\n            this._handledMesh.positions = this._unwrappedPositionsForBabylon;\n            this._handledMesh.normals = this._unwrappedNormalsForBabylon;\n            this._handledMesh.uvs = this._unwrappedUVForBabylon;\n            this._handledMesh.hasLines = this._hasLineData;\n            if (this._loadingOptions.importVertexColors) {\n                this._handledMesh.colors = this._unwrappedColorsForBabylon;\n            }\n        }\n        // If any o or g keyword not found, create a mesh with a random id\n        if (!this._hasMeshes) {\n            let newMaterial = null;\n            if (this._indicesForBabylon.length) {\n                if (this._loadingOptions.useLegacyBehavior) {\n                    // reverse tab of indices\n                    this._indicesForBabylon.reverse();\n                }\n                //Get positions normals uvs\n                this._unwrapData();\n            }\n            else {\n                // There is no indices in the file. We will have to switch to point cloud rendering\n                for (const pos of this._positions) {\n                    this._unwrappedPositionsForBabylon.push(pos.x, pos.y, pos.z);\n                }\n                if (this._normals.length) {\n                    for (const normal of this._normals) {\n                        this._unwrappedNormalsForBabylon.push(normal.x, normal.y, normal.z);\n                    }\n                }\n                if (this._uvs.length) {\n                    for (const uv of this._uvs) {\n                        this._unwrappedUVForBabylon.push(uv.x, uv.y);\n                    }\n                }\n                if (this._extColors.length) {\n                    for (const color of this._extColors) {\n                        this._unwrappedColorsForBabylon.push(color.r, color.g, color.b, color.a);\n                    }\n                }\n                else {\n                    if (this._colors.length) {\n                        for (const color of this._colors) {\n                            this._unwrappedColorsForBabylon.push(color.r, color.g, color.b, color.a);\n                        }\n                    }\n                }\n                if (!this._materialNameFromObj) {\n                    // Create a material with point cloud on\n                    newMaterial = new StandardMaterial(Geometry.RandomId(), scene);\n                    newMaterial.pointsCloud = true;\n                    this._materialNameFromObj = newMaterial.name;\n                    if (!this._normals.length) {\n                        newMaterial.disableLighting = true;\n                        newMaterial.emissiveColor = Color3.White();\n                    }\n                }\n            }\n            //Set data for one mesh\n            this._meshesFromObj.push({\n                name: Geometry.RandomId(),\n                indices: this._indicesForBabylon,\n                positions: this._unwrappedPositionsForBabylon,\n                colors: this._unwrappedColorsForBabylon,\n                normals: this._unwrappedNormalsForBabylon,\n                uvs: this._unwrappedUVForBabylon,\n                materialName: this._materialNameFromObj,\n                directMaterial: newMaterial,\n                isObject: true,\n                hasLines: this._hasLineData,\n            });\n        }\n        //Set data for each mesh\n        for (let j = 0; j < this._meshesFromObj.length; j++) {\n            //check meshesNames (stlFileLoader)\n            if (meshesNames && this._meshesFromObj[j].name) {\n                if (meshesNames instanceof Array) {\n                    if (meshesNames.indexOf(this._meshesFromObj[j].name) === -1) {\n                        continue;\n                    }\n                }\n                else {\n                    if (this._meshesFromObj[j].name !== meshesNames) {\n                        continue;\n                    }\n                }\n            }\n            //Get the current mesh\n            //Set the data with VertexBuffer for each mesh\n            this._handledMesh = this._meshesFromObj[j];\n            //Create a Mesh with the name of the obj mesh\n            scene._blockEntityCollection = !!assetContainer;\n            const babylonMesh = new Mesh(this._meshesFromObj[j].name, scene);\n            babylonMesh._parentContainer = assetContainer;\n            scene._blockEntityCollection = false;\n            this._handledMesh._babylonMesh = babylonMesh;\n            // If this is a group mesh, it should have an object mesh as a parent. So look for the first object mesh that appears before it.\n            if (!this._handledMesh.isObject) {\n                for (let k = j - 1; k >= 0; --k) {\n                    if (this._meshesFromObj[k].isObject && this._meshesFromObj[k]._babylonMesh) {\n                        babylonMesh.parent = this._meshesFromObj[k]._babylonMesh;\n                        break;\n                    }\n                }\n            }\n            //Push the name of the material to an array\n            //This is indispensable for the importMesh function\n            this._materialToUse.push(this._meshesFromObj[j].materialName);\n            //If the mesh is a line mesh\n            if (this._handledMesh.hasLines) {\n                babylonMesh._internalMetadata ?? (babylonMesh._internalMetadata = {});\n                babylonMesh._internalMetadata[\"_isLine\"] = true; //this is a line mesh\n            }\n            if (this._handledMesh.positions?.length === 0) {\n                //Push the mesh into an array\n                this._babylonMeshesArray.push(babylonMesh);\n                continue;\n            }\n            const vertexData = new VertexData(); //The container for the values\n            //Set the data for the babylonMesh\n            vertexData.uvs = this._handledMesh.uvs;\n            vertexData.indices = this._handledMesh.indices;\n            vertexData.positions = this._handledMesh.positions;\n            if (this._loadingOptions.computeNormals) {\n                const normals = new Array();\n                VertexData.ComputeNormals(this._handledMesh.positions, this._handledMesh.indices, normals);\n                vertexData.normals = normals;\n            }\n            else {\n                vertexData.normals = this._handledMesh.normals;\n            }\n            if (this._loadingOptions.importVertexColors) {\n                vertexData.colors = this._handledMesh.colors;\n            }\n            //Set the data from the VertexBuffer to the current Mesh\n            vertexData.applyToMesh(babylonMesh);\n            if (this._loadingOptions.invertY) {\n                babylonMesh.scaling.y *= -1;\n            }\n            if (this._loadingOptions.optimizeNormals) {\n                this._optimizeNormals(babylonMesh);\n            }\n            //Push the mesh into an array\n            this._babylonMeshesArray.push(babylonMesh);\n            if (this._handledMesh.directMaterial) {\n                babylonMesh.material = this._handledMesh.directMaterial;\n            }\n        }\n    }\n}\n// Descriptor\n/** Object descriptor */\nSolidParser.ObjectDescriptor = /^o/;\n/** Group descriptor */\nSolidParser.GroupDescriptor = /^g/;\n/** Material lib descriptor */\nSolidParser.MtlLibGroupDescriptor = /^mtllib /;\n/** Use a material descriptor */\nSolidParser.UseMtlDescriptor = /^usemtl /;\n/** Smooth descriptor */\nSolidParser.SmoothDescriptor = /^s /;\n// Patterns\n/** Pattern used to detect a vertex */\nSolidParser.VertexPattern = /^v(\\s+[\\d|.|+|\\-|e|E]+){3,7}/;\n/** Pattern used to detect a normal */\nSolidParser.NormalPattern = /^vn(\\s+[\\d|.|+|\\-|e|E]+)( +[\\d|.|+|\\-|e|E]+)( +[\\d|.|+|\\-|e|E]+)/;\n/** Pattern used to detect a UV set */\nSolidParser.UVPattern = /^vt(\\s+[\\d|.|+|\\-|e|E]+)( +[\\d|.|+|\\-|e|E]+)/;\n/** Pattern used to detect a first kind of face (f vertex vertex vertex) */\nSolidParser.FacePattern1 = /^f\\s+(([\\d]{1,}[\\s]?){3,})+/;\n/** Pattern used to detect a second kind of face (f vertex/uvs vertex/uvs vertex/uvs) */\nSolidParser.FacePattern2 = /^f\\s+((([\\d]{1,}\\/[\\d]{1,}[\\s]?){3,})+)/;\n/** Pattern used to detect a third kind of face (f vertex/uvs/normal vertex/uvs/normal vertex/uvs/normal) */\nSolidParser.FacePattern3 = /^f\\s+((([\\d]{1,}\\/[\\d]{1,}\\/[\\d]{1,}[\\s]?){3,})+)/;\n/** Pattern used to detect a fourth kind of face (f vertex//normal vertex//normal vertex//normal)*/\nSolidParser.FacePattern4 = /^f\\s+((([\\d]{1,}\\/\\/[\\d]{1,}[\\s]?){3,})+)/;\n/** Pattern used to detect a fifth kind of face (f -vertex/-uvs/-normal -vertex/-uvs/-normal -vertex/-uvs/-normal) */\nSolidParser.FacePattern5 = /^f\\s+(((-[\\d]{1,}\\/-[\\d]{1,}\\/-[\\d]{1,}[\\s]?){3,})+)/;\n/** Pattern used to detect a line(l vertex vertex) */\nSolidParser.LinePattern1 = /^l\\s+(([\\d]{1,}[\\s]?){2,})+/;\n/** Pattern used to detect a second kind of line (l vertex/uvs vertex/uvs) */\nSolidParser.LinePattern2 = /^l\\s+((([\\d]{1,}\\/[\\d]{1,}[\\s]?){2,})+)/;\n/** Pattern used to detect a third kind of line (l vertex/uvs/normal vertex/uvs/normal) */\nSolidParser.LinePattern3 = /^l\\s+((([\\d]{1,}\\/[\\d]{1,}\\/[\\d]{1,}[\\s]?){2,})+)/;\n//# sourceMappingURL=solidParser.js.map", "import { Vector2 } from \"@babylonjs/core/Maths/math.vector.js\";\nimport { Tools } from \"@babylonjs/core/Misc/tools.js\";\nimport { RegisterSceneLoaderPlugin } from \"@babylonjs/core/Loading/sceneLoader.js\";\nimport { AssetContainer } from \"@babylonjs/core/assetContainer.js\";\nimport { OBJFileLoaderMetadata } from \"./objFileLoader.metadata.js\";\nimport { MTLFileLoader } from \"./mtlFileLoader.js\";\nimport { SolidParser } from \"./solidParser.js\";\nimport { StandardMaterial } from \"@babylonjs/core/Materials/standardMaterial.js\";\n/**\n * OBJ file type loader.\n * This is a babylon scene loader plugin.\n */\nexport class OBJFileLoader {\n    /**\n     * Invert Y-Axis of referenced textures on load\n     */\n    static get INVERT_TEXTURE_Y() {\n        return MTLFileLoader.INVERT_TEXTURE_Y;\n    }\n    static set INVERT_TEXTURE_Y(value) {\n        MTLFileLoader.INVERT_TEXTURE_Y = value;\n    }\n    /**\n     * Creates loader for .OBJ files\n     *\n     * @param loadingOptions options for loading and parsing OBJ/MTL files.\n     */\n    constructor(loadingOptions) {\n        /**\n         * Defines the name of the plugin.\n         */\n        this.name = OBJFileLoaderMetadata.name;\n        /**\n         * Defines the extension the plugin is able to load.\n         */\n        this.extensions = OBJFileLoaderMetadata.extensions;\n        this._assetContainer = null;\n        this._loadingOptions = { ...OBJFileLoader._DefaultLoadingOptions, ...(loadingOptions ?? {}) };\n    }\n    static get _DefaultLoadingOptions() {\n        return {\n            computeNormals: OBJFileLoader.COMPUTE_NORMALS,\n            optimizeNormals: OBJFileLoader.OPTIMIZE_NORMALS,\n            importVertexColors: OBJFileLoader.IMPORT_VERTEX_COLORS,\n            invertY: OBJFileLoader.INVERT_Y,\n            invertTextureY: OBJFileLoader.INVERT_TEXTURE_Y,\n            // eslint-disable-next-line @typescript-eslint/naming-convention\n            UVScaling: OBJFileLoader.UV_SCALING,\n            materialLoadingFailsSilently: OBJFileLoader.MATERIAL_LOADING_FAILS_SILENTLY,\n            optimizeWithUV: OBJFileLoader.OPTIMIZE_WITH_UV,\n            skipMaterials: OBJFileLoader.SKIP_MATERIALS,\n            useLegacyBehavior: OBJFileLoader.USE_LEGACY_BEHAVIOR,\n        };\n    }\n    /**\n     * Calls synchronously the MTL file attached to this obj.\n     * Load function or importMesh function don't enable to load 2 files in the same time asynchronously.\n     * Without this function materials are not displayed in the first frame (but displayed after).\n     * In consequence it is impossible to get material information in your HTML file\n     *\n     * @param url The URL of the MTL file\n     * @param rootUrl defines where to load data from\n     * @param onSuccess Callback function to be called when the MTL file is loaded\n     * @param onFailure\n     */\n    _loadMTL(url, rootUrl, onSuccess, onFailure) {\n        //The complete path to the mtl file\n        const pathOfFile = rootUrl + url;\n        // Loads through the babylon tools to allow fileInput search.\n        Tools.LoadFile(pathOfFile, onSuccess, undefined, undefined, false, (request, exception) => {\n            onFailure(pathOfFile, exception);\n        });\n    }\n    /** @internal */\n    createPlugin(options) {\n        return new OBJFileLoader(options[OBJFileLoaderMetadata.name]);\n    }\n    /**\n     * If the data string can be loaded directly.\n     * @returns if the data can be loaded directly\n     */\n    canDirectLoad() {\n        return false;\n    }\n    /**\n     * Imports one or more meshes from the loaded OBJ data and adds them to the scene\n     * @param meshesNames a string or array of strings of the mesh names that should be loaded from the file\n     * @param scene the scene the meshes should be added to\n     * @param data the OBJ data to load\n     * @param rootUrl root url to load from\n     * @returns a promise containing the loaded meshes, particles, skeletons and animations\n     */\n    importMeshAsync(meshesNames, scene, data, rootUrl) {\n        //get the meshes from OBJ file\n        return this._parseSolid(meshesNames, scene, data, rootUrl).then((meshes) => {\n            return {\n                meshes: meshes,\n                particleSystems: [],\n                skeletons: [],\n                animationGroups: [],\n                transformNodes: [],\n                geometries: [],\n                lights: [],\n                spriteManagers: [],\n            };\n        });\n    }\n    /**\n     * Imports all objects from the loaded OBJ data and adds them to the scene\n     * @param scene the scene the objects should be added to\n     * @param data the OBJ data to load\n     * @param rootUrl root url to load from\n     * @returns a promise which completes when objects have been loaded to the scene\n     */\n    loadAsync(scene, data, rootUrl) {\n        //Get the 3D model\n        return this.importMeshAsync(null, scene, data, rootUrl).then(() => {\n            // return void\n        });\n    }\n    /**\n     * Load into an asset container.\n     * @param scene The scene to load into\n     * @param data The data to import\n     * @param rootUrl The root url for scene and resources\n     * @returns The loaded asset container\n     */\n    loadAssetContainerAsync(scene, data, rootUrl) {\n        const container = new AssetContainer(scene);\n        this._assetContainer = container;\n        return this.importMeshAsync(null, scene, data, rootUrl)\n            .then((result) => {\n            result.meshes.forEach((mesh) => container.meshes.push(mesh));\n            result.meshes.forEach((mesh) => {\n                const material = mesh.material;\n                if (material) {\n                    // Materials\n                    if (container.materials.indexOf(material) == -1) {\n                        container.materials.push(material);\n                        // Textures\n                        const textures = material.getActiveTextures();\n                        textures.forEach((t) => {\n                            if (container.textures.indexOf(t) == -1) {\n                                container.textures.push(t);\n                            }\n                        });\n                    }\n                }\n            });\n            this._assetContainer = null;\n            return container;\n        })\n            .catch((ex) => {\n            this._assetContainer = null;\n            throw ex;\n        });\n    }\n    /**\n     * Read the OBJ file and create an Array of meshes.\n     * Each mesh contains all information given by the OBJ and the MTL file.\n     * i.e. vertices positions and indices, optional normals values, optional UV values, optional material\n     * @param meshesNames defines a string or array of strings of the mesh names that should be loaded from the file\n     * @param scene defines the scene where are displayed the data\n     * @param data defines the content of the obj file\n     * @param rootUrl defines the path to the folder\n     * @returns the list of loaded meshes\n     */\n    _parseSolid(meshesNames, scene, data, rootUrl) {\n        let fileToLoad = \"\"; //The name of the mtlFile to load\n        const materialsFromMTLFile = new MTLFileLoader();\n        const materialToUse = [];\n        const babylonMeshesArray = []; //The mesh for babylon\n        // Sanitize data\n        data = data.replace(/#.*$/gm, \"\").trim();\n        // Main function\n        const solidParser = new SolidParser(materialToUse, babylonMeshesArray, this._loadingOptions);\n        solidParser.parse(meshesNames, data, scene, this._assetContainer, (fileName) => {\n            fileToLoad = fileName;\n        });\n        // load the materials\n        const mtlPromises = [];\n        // Check if we have a file to load\n        if (fileToLoad !== \"\" && !this._loadingOptions.skipMaterials) {\n            //Load the file synchronously\n            mtlPromises.push(new Promise((resolve, reject) => {\n                this._loadMTL(fileToLoad, rootUrl, (dataLoaded) => {\n                    try {\n                        //Create materials thanks MTLLoader function\n                        materialsFromMTLFile.parseMTL(scene, dataLoaded, rootUrl, this._assetContainer);\n                        //Look at each material loaded in the mtl file\n                        for (let n = 0; n < materialsFromMTLFile.materials.length; n++) {\n                            //Three variables to get all meshes with the same material\n                            let startIndex = 0;\n                            const _indices = [];\n                            let _index;\n                            //The material from MTL file is used in the meshes loaded\n                            //Push the indice in an array\n                            //Check if the material is not used for another mesh\n                            while ((_index = materialToUse.indexOf(materialsFromMTLFile.materials[n].name, startIndex)) > -1) {\n                                _indices.push(_index);\n                                startIndex = _index + 1;\n                            }\n                            //If the material is not used dispose it\n                            if (_index === -1 && _indices.length === 0) {\n                                //If the material is not needed, remove it\n                                materialsFromMTLFile.materials[n].dispose();\n                            }\n                            else {\n                                for (let o = 0; o < _indices.length; o++) {\n                                    //Apply the material to the Mesh for each mesh with the material\n                                    const mesh = babylonMeshesArray[_indices[o]];\n                                    const material = materialsFromMTLFile.materials[n];\n                                    mesh.material = material;\n                                    if (!mesh.getTotalIndices()) {\n                                        // No indices, we need to turn on point cloud\n                                        material.pointsCloud = true;\n                                    }\n                                }\n                            }\n                        }\n                        resolve();\n                    }\n                    catch (e) {\n                        Tools.Warn(`Error processing MTL file: '${fileToLoad}'`);\n                        if (this._loadingOptions.materialLoadingFailsSilently) {\n                            resolve();\n                        }\n                        else {\n                            reject(e);\n                        }\n                    }\n                }, (pathOfFile, exception) => {\n                    Tools.Warn(`Error downloading MTL file: '${fileToLoad}'`);\n                    if (this._loadingOptions.materialLoadingFailsSilently) {\n                        resolve();\n                    }\n                    else {\n                        reject(exception);\n                    }\n                });\n            }));\n        }\n        //Return an array with all Mesh\n        return Promise.all(mtlPromises).then(() => {\n            const isLine = (mesh) => Boolean(mesh._internalMetadata?.[\"_isLine\"] ?? false);\n            // Iterate over the mesh, determine if it is a line mesh, clone or modify the material to line rendering.\n            babylonMeshesArray.forEach((mesh) => {\n                if (isLine(mesh)) {\n                    let mat = mesh.material ?? new StandardMaterial(mesh.name + \"_line\", scene);\n                    // If another mesh is using this material and it is not a line then we need to clone it.\n                    const needClone = mat.getBindedMeshes().filter((e) => !isLine(e)).length > 0;\n                    if (needClone) {\n                        mat = mat.clone(mat.name + \"_line\") ?? mat;\n                    }\n                    mat.wireframe = true;\n                    mesh.material = mat;\n                    if (mesh._internalMetadata) {\n                        mesh._internalMetadata[\"_isLine\"] = undefined;\n                    }\n                }\n            });\n            return babylonMeshesArray;\n        });\n    }\n}\n/**\n * Defines if UVs are optimized by default during load.\n */\nOBJFileLoader.OPTIMIZE_WITH_UV = true;\n/**\n * Invert model on y-axis (does a model scaling inversion)\n */\nOBJFileLoader.INVERT_Y = false;\n/**\n * Include in meshes the vertex colors available in some OBJ files.  This is not part of OBJ standard.\n */\nOBJFileLoader.IMPORT_VERTEX_COLORS = false;\n/**\n * Compute the normals for the model, even if normals are present in the file.\n */\nOBJFileLoader.COMPUTE_NORMALS = false;\n/**\n * Optimize the normals for the model. Lighting can be uneven if you use OptimizeWithUV = true because new vertices can be created for the same location if they pertain to different faces.\n * Using OptimizehNormals = true will help smoothing the lighting by averaging the normals of those vertices.\n */\nOBJFileLoader.OPTIMIZE_NORMALS = false;\n/**\n * Defines custom scaling of UV coordinates of loaded meshes.\n */\nOBJFileLoader.UV_SCALING = new Vector2(1, 1);\n/**\n * Skip loading the materials even if defined in the OBJ file (materials are ignored).\n */\nOBJFileLoader.SKIP_MATERIALS = false;\n/**\n * When a material fails to load OBJ loader will silently fail and onSuccess() callback will be triggered.\n *\n * Defaults to true for backwards compatibility.\n */\nOBJFileLoader.MATERIAL_LOADING_FAILS_SILENTLY = true;\n/**\n * Loads assets without handedness conversions. This flag is for compatibility. Use it only if absolutely required. Defaults to false.\n */\nOBJFileLoader.USE_LEGACY_BEHAVIOR = false;\n//Add this loader into the register plugin\nRegisterSceneLoaderPlugin(new OBJFileLoader());\n//# sourceMappingURL=objFileLoader.js.map"], "names": ["MTLFileLoader", "scene", "data", "rootUrl", "assetContainer", "lines", "delimiter_pattern", "color", "material", "i", "line", "pos", "key", "value", "StandardMaterial", "Color3", "values", "bumpMultiplierIndex", "bumpMultiplier", "url", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Texture", "SolidParser", "materialToUse", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadingOptions", "Color4", "arr", "obj", "idx", "indicePositionFromObj", "indiceUvsFromObj", "indiceNormalFromObj", "positionVectorFromOBJ", "textureVectorFromOBJ", "normalsVectorFromOBJ", "positionColorsFromOBJ", "_index", "Vector2", "l", "faces", "v", "faceIndex", "index", "face", "k", "Vector3", "point", "mesh", "positions", "VertexBuffer", "normals", "mapVertices", "x", "y", "z", "lst", "normal", "v0Idx", "vIdx", "notParse", "regex", "reg<PERSON><PERSON><PERSON>", "array", "regIndex", "r", "g", "b", "meshesNames", "onFileToLoadFound", "linesOBJ", "lineLines", "currentGroup", "lineValues", "result", "element", "obj<PERSON><PERSON>", "<PERSON><PERSON>", "newMaterial", "uv", "Geometry", "j", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "_a", "vertexData", "VertexData", "OBJFileLoader", "OBJFileLoaderMetadata", "onSuccess", "onFailure", "pathOfFile", "Tools", "request", "exception", "options", "meshes", "container", "<PERSON>setC<PERSON><PERSON>", "t", "ex", "fileToLoad", "materialsFromMTLFile", "fileName", "mtlPromises", "resolve", "reject", "dataLoaded", "n", "startIndex", "_indices", "o", "e", "isLine", "mat", "RegisterSceneLoaderPlugin"], "mappings": "wOAMO,MAAMA,CAAc,CACvB,aAAc,CAIV,KAAK,UAAY,EACpB,CAYD,SAASC,EAAOC,EAAMC,EAASC,EAAgB,CAC3C,GAAIF,aAAgB,YAChB,OAGJ,MAAMG,EAAQH,EAAK,MAAM;AAAA,CAAI,EAEvBI,EAAoB,MAE1B,IAAIC,EAEAC,EAAW,KAEf,QAASC,EAAI,EAAGA,EAAIJ,EAAM,OAAQI,IAAK,CACnC,MAAMC,EAAOL,EAAMI,CAAC,EAAE,KAAI,EAE1B,GAAIC,EAAK,SAAW,GAAKA,EAAK,OAAO,CAAC,IAAM,IACxC,SAGJ,MAAMC,EAAMD,EAAK,QAAQ,GAAG,EAC5B,IAAIE,EAAMD,GAAO,EAAID,EAAK,UAAU,EAAGC,CAAG,EAAID,EAC9CE,EAAMA,EAAI,cAEV,MAAMC,EAAQF,GAAO,EAAID,EAAK,UAAUC,EAAM,CAAC,EAAE,KAAM,EAAG,GAE1D,GAAIC,IAAQ,SAGJJ,GAEA,KAAK,UAAU,KAAKA,CAAQ,EAIhCP,EAAM,uBAAyB,CAAC,CAACG,EACjCI,EAAW,IAAIM,EAAiBD,EAAOZ,CAAK,EAC5CO,EAAS,iBAAmBJ,EAC5BH,EAAM,uBAAyB,WAE1BW,IAAQ,MAAQJ,EAGrBD,EAAQM,EAAM,MAAMP,EAAmB,CAAC,EAAE,IAAI,UAAU,EAGxDE,EAAS,aAAeO,EAAO,UAAUR,CAAK,UAEzCK,IAAQ,MAAQJ,EAGrBD,EAAQM,EAAM,MAAMP,EAAmB,CAAC,EAAE,IAAI,UAAU,EAGxDE,EAAS,aAAeO,EAAO,UAAUR,CAAK,UAEzCK,IAAQ,MAAQJ,EAGrBD,EAAQM,EAAM,MAAMP,EAAmB,CAAC,EAAE,IAAI,UAAU,EAGxDE,EAAS,cAAgBO,EAAO,UAAUR,CAAK,UAE1CK,IAAQ,MAAQJ,EAErBD,EAAQM,EAAM,MAAMP,EAAmB,CAAC,EAAE,IAAI,UAAU,EACxDE,EAAS,cAAgBO,EAAO,UAAUR,CAAK,UAE1CK,IAAQ,MAAQJ,EAErBA,EAAS,cAAgB,WAAWK,CAAK,UAEpCD,IAAQ,KAAOJ,EAEpBA,EAAS,MAAQ,WAAWK,CAAK,UAI5BD,IAAQ,UAAYJ,EAGzBA,EAAS,eAAiBR,EAAc,YAAYG,EAASU,EAAOZ,CAAK,UAEpEW,IAAQ,UAAYJ,EAEzBA,EAAS,eAAiBR,EAAc,YAAYG,EAASU,EAAOZ,CAAK,UAEpEW,IAAQ,UAAYJ,EAGzBA,EAAS,gBAAkBR,EAAc,YAAYG,EAASU,EAAOZ,CAAK,UAErEW,IAAQ,SASZ,GAAIA,IAAQ,YAAcJ,EAAU,CAErC,MAAMQ,EAASH,EAAM,MAAMP,CAAiB,EACtCW,EAAsBD,EAAO,QAAQ,KAAK,EAChD,IAAIE,EAAiB,KACjBD,GAAuB,IACvBC,EAAiBF,EAAOC,EAAsB,CAAC,EAC/CD,EAAO,OAAOC,EAAqB,CAAC,GAExCT,EAAS,YAAcR,EAAc,YAAYG,EAASa,EAAO,KAAK,GAAG,EAAGf,CAAK,EAC7EO,EAAS,aAAeU,IAAmB,OAC3CV,EAAS,YAAY,MAAQ,WAAWU,CAAc,EAE7D,MACQN,IAAQ,SAAWJ,IAExBA,EAAS,eAAiBR,EAAc,YAAYG,EAASU,EAAOZ,CAAK,EA0ChF,CAEGO,GACA,KAAK,UAAU,KAAKA,CAAQ,CAEnC,CAYD,OAAO,YAAYL,EAASU,EAAOZ,EAAO,CACtC,GAAI,CAACY,EACD,OAAO,KAEX,IAAIM,EAAMhB,EAEV,GAAIA,IAAY,QAAS,CACrB,IAAIiB,EAAgBP,EAAM,YAAY,IAAI,EACtCO,IAAkB,KAClBA,EAAgBP,EAAM,YAAY,GAAG,GAErCO,EAAgB,GAChBD,GAAON,EAAM,UAAUO,EAAgB,CAAC,EAGxCD,GAAON,CAEd,MAGGM,GAAON,EAEX,OAAO,IAAIQ,EAAQF,EAAKlB,EAAO,GAAOD,EAAc,gBAAgB,CACvE,CACL,CAIAA,EAAc,iBAAmB,GC1N1B,MAAMsB,CAAY,CAOrB,YAAYC,EAAeC,EAAoBC,EAAgB,CAC3D,KAAK,WAAa,GAClB,KAAK,SAAW,GAChB,KAAK,KAAO,GACZ,KAAK,QAAU,GACf,KAAK,WAAa,GAClB,KAAK,eAAiB,GACtB,KAAK,mBAAqB,GAC1B,KAAK,2BAA6B,GAClC,KAAK,sBAAwB,GAC7B,KAAK,yBAA2B,GAChC,KAAK,0BAA4B,GACjC,KAAK,cAAgB,GACrB,KAAK,sBAAwB,EAC7B,KAAK,WAAa,GAClB,KAAK,8BAAgC,GACrC,KAAK,2BAA6B,GAClC,KAAK,4BAA8B,GACnC,KAAK,uBAAyB,GAC9B,KAAK,WAAa,GAClB,KAAK,qBAAuB,GAC5B,KAAK,aAAe,GACpB,KAAK,WAAa,EAClB,KAAK,iBAAmB,GACxB,KAAK,WAAa,IAAIC,EAAO,GAAK,GAAK,GAAK,CAAC,EAC7C,KAAK,aAAe,GACpB,KAAK,eAAiBH,EACtB,KAAK,oBAAsBC,EAC3B,KAAK,gBAAkBC,CAC1B,CAUD,WAAWE,EAAKC,EAAK,CACZD,EAAIC,EAAI,CAAC,CAAC,IACXD,EAAIC,EAAI,CAAC,CAAC,EAAI,CAAE,QAAS,CAAE,EAAE,IAAK,CAAA,IAEtC,MAAMC,EAAMF,EAAIC,EAAI,CAAC,CAAC,EAAE,QAAQ,QAAQA,EAAI,CAAC,CAAC,EAC9C,OAAOC,IAAQ,GAAK,GAAKF,EAAIC,EAAI,CAAC,CAAC,EAAE,IAAIC,CAAG,CAC/C,CACD,aAAaF,EAAKC,EAAK,CACdD,EAAIC,EAAI,CAAC,CAAC,IACXD,EAAIC,EAAI,CAAC,CAAC,EAAI,CAAE,QAAS,CAAE,EAAE,IAAK,CAAA,EAAI,GAAI,CAAE,CAAA,GAEhD,MAAMC,EAAMF,EAAIC,EAAI,CAAC,CAAC,EAAE,QAAQ,QAAQA,EAAI,CAAC,CAAC,EAC9C,OAAIC,GAAO,GAAKD,EAAI,CAAC,IAAMD,EAAIC,EAAI,CAAC,CAAC,EAAE,GAAGC,CAAG,EAClCF,EAAIC,EAAI,CAAC,CAAC,EAAE,IAAIC,CAAG,EAEvB,EACV,CAeD,SAASC,EAAuBC,EAAkBC,EAAqBC,EAAuBC,EAAsBC,EAAsBC,EAAuB,CAE7J,IAAIC,EACA,KAAK,gBAAgB,eACrBA,EAAS,KAAK,aAAa,KAAK,cAAe,CAACP,EAAuBE,EAAqBD,CAAgB,CAAC,EAG7GM,EAAS,KAAK,WAAW,KAAK,cAAe,CAACP,EAAuBE,CAAmB,CAAC,EAGzFK,IAAW,IAIX,KAAK,mBAAmB,KAAK,KAAK,2BAA2B,MAAM,EAGnE,KAAK,2BAA2B,KAAKJ,CAAqB,EAI1DC,EAAuBA,GAAwB,IAAII,EAAQ,EAAG,CAAC,EAC/D,KAAK,sBAAsB,KAAKJ,CAAoB,EAGpD,KAAK,0BAA0B,KAAKC,CAAoB,EACpDC,IAA0B,QAG1B,KAAK,yBAAyB,KAAKA,CAAqB,EAG5D,KAAK,cAAcN,CAAqB,EAAE,QAAQ,KAAKE,CAAmB,EAC1E,KAAK,cAAcF,CAAqB,EAAE,IAAI,KAAK,KAAK,uBAAuB,EAC3E,KAAK,gBAAgB,gBACrB,KAAK,cAAcA,CAAqB,EAAE,GAAG,KAAKC,CAAgB,GAOtE,KAAK,mBAAmB,KAAKM,CAAM,CAE1C,CAID,aAAc,CACV,GAAI,CAEA,QAASE,EAAI,EAAGA,EAAI,KAAK,2BAA2B,OAAQA,IAExD,KAAK,8BAA8B,KAAK,KAAK,2BAA2BA,CAAC,EAAE,EAAI,KAAK,gBAAiB,KAAK,2BAA2BA,CAAC,EAAE,EAAG,KAAK,2BAA2BA,CAAC,EAAE,CAAC,EAC/K,KAAK,4BAA4B,KAAK,KAAK,0BAA0BA,CAAC,EAAE,EAAI,KAAK,gBAAiB,KAAK,0BAA0BA,CAAC,EAAE,EAAG,KAAK,0BAA0BA,CAAC,EAAE,CAAC,EAC1K,KAAK,uBAAuB,KAAK,KAAK,sBAAsBA,CAAC,EAAE,EAAG,KAAK,sBAAsBA,CAAC,EAAE,CAAC,EAC7F,KAAK,gBAAgB,oBAErB,KAAK,2BAA2B,KAAK,KAAK,yBAAyBA,CAAC,EAAE,EAAG,KAAK,yBAAyBA,CAAC,EAAE,EAAG,KAAK,yBAAyBA,CAAC,EAAE,EAAG,KAAK,yBAAyBA,CAAC,EAAE,CAAC,EAI3L,KAAK,2BAA2B,OAAS,EACzC,KAAK,0BAA0B,OAAS,EACxC,KAAK,sBAAsB,OAAS,EACpC,KAAK,yBAAyB,OAAS,EACvC,KAAK,cAAc,OAAS,EAC5B,KAAK,sBAAwB,CAChC,MACS,CACN,MAAM,IAAI,MAAM,+CAA+C,CAClE,CACJ,CAcD,cAAcC,EAAOC,EAAG,CAEpB,QAASC,EAAYD,EAAGC,EAAYF,EAAM,OAAS,EAAGE,IAElD,KAAK,cAAcF,EAAOE,CAAS,CAQ1C,CAMD,UAAUC,EAAO,CACb,GAAI,KAAK,gBAAgB,mBACrB,OAAO,KAAK,WAAWA,CAAK,GAAK,KAAK,QAAQA,CAAK,CAK1D,CAOD,mCAAmCC,EAAMH,EAAG,CAExC,KAAK,cAAcG,EAAMH,CAAC,EAG1B,QAASI,EAAI,EAAGA,EAAI,KAAK,WAAW,OAAQA,IAAK,CAE7C,MAAMf,EAAwB,SAAS,KAAK,WAAWe,CAAC,CAAC,EAAI,EAC7D,KAAK,SAASf,EAAuB,EAAG,EACxC,KAAK,WAAWA,CAAqB,EACrCQ,EAAQ,KAAI,EAAIQ,EAAQ,GAAI,EAC5B,KAAK,UAAUhB,CAAqB,CAAC,CACxC,CAED,KAAK,WAAW,OAAS,CAC5B,CAOD,mCAAmCc,EAAMH,EAAG,CAExC,KAAK,cAAcG,EAAMH,CAAC,EAC1B,QAASI,EAAI,EAAGA,EAAI,KAAK,WAAW,OAAQA,IAAK,CAG7C,MAAME,EAAQ,KAAK,WAAWF,CAAC,EAAE,MAAM,GAAG,EAEpCf,EAAwB,SAASiB,EAAM,CAAC,CAAC,EAAI,EAE7ChB,EAAmB,SAASgB,EAAM,CAAC,CAAC,EAAI,EAC9C,KAAK,SAASjB,EAAuBC,EAAkB,EACvD,KAAK,WAAWD,CAAqB,EACrC,KAAK,KAAKC,CAAgB,GAAKO,EAAQ,KAAM,EAAEQ,EAAQ,GAAI,EAC3D,KAAK,UAAUhB,CAAqB,CAAC,CACxC,CAED,KAAK,WAAW,OAAS,CAC5B,CAOD,mCAAmCc,EAAMH,EAAG,CAExC,KAAK,cAAcG,EAAMH,CAAC,EAC1B,QAASI,EAAI,EAAGA,EAAI,KAAK,WAAW,OAAQA,IAAK,CAG7C,MAAME,EAAQ,KAAK,WAAWF,CAAC,EAAE,MAAM,GAAG,EAEpCf,EAAwB,SAASiB,EAAM,CAAC,CAAC,EAAI,EAE7ChB,EAAmB,SAASgB,EAAM,CAAC,CAAC,EAAI,EAExCf,EAAsB,SAASe,EAAM,CAAC,CAAC,EAAI,EACjD,KAAK,SAASjB,EAAuBC,EAAkBC,EAAqB,KAAK,WAAWF,CAAqB,EAAG,KAAK,KAAKC,CAAgB,GAAKO,EAAQ,OAAQ,KAAK,SAASN,CAAmB,GAAKc,EAAQ,GAAI,CACjO,CACS,CAED,KAAK,WAAW,OAAS,CAC5B,CAOD,mCAAmCF,EAAMH,EAAG,CACxC,KAAK,cAAcG,EAAMH,CAAC,EAC1B,QAASI,EAAI,EAAGA,EAAI,KAAK,WAAW,OAAQA,IAAK,CAG7C,MAAME,EAAQ,KAAK,WAAWF,CAAC,EAAE,MAAM,IAAI,EAErCf,EAAwB,SAASiB,EAAM,CAAC,CAAC,EAAI,EAC7Cf,EAAsB,SAASe,EAAM,CAAC,CAAC,EAAI,EACjD,KAAK,SAASjB,EAAuB,EACrCE,EAAqB,KAAK,WAAWF,CAAqB,EAC1DQ,EAAQ,OAAQ,KAAK,SAASN,CAAmB,EAAG,KAAK,UAAUF,CAAqB,CAAC,CAC5F,CAED,KAAK,WAAW,OAAS,CAC5B,CAOD,mCAAmCc,EAAMH,EAAG,CAExC,KAAK,cAAcG,EAAMH,CAAC,EAC1B,QAASI,EAAI,EAAGA,EAAI,KAAK,WAAW,OAAQA,IAAK,CAG7C,MAAME,EAAQ,KAAK,WAAWF,CAAC,EAAE,MAAM,GAAG,EAEpCf,EAAwB,KAAK,WAAW,OAAS,SAASiB,EAAM,CAAC,CAAC,EAElEhB,EAAmB,KAAK,KAAK,OAAS,SAASgB,EAAM,CAAC,CAAC,EAEvDf,EAAsB,KAAK,SAAS,OAAS,SAASe,EAAM,CAAC,CAAC,EACpE,KAAK,SAASjB,EAAuBC,EAAkBC,EAAqB,KAAK,WAAWF,CAAqB,EAAG,KAAK,KAAKC,CAAgB,EAAG,KAAK,SAASC,CAAmB,EAClL,KAAK,UAAUF,CAAqB,CAAC,CACxC,CAED,KAAK,WAAW,OAAS,CAC5B,CACD,qBAAsB,CAEd,KAAK,eAAe,OAAS,IAG7B,KAAK,aAAe,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,EAEtE,KAAK,YAAW,EACZ,KAAK,gBAAgB,mBAErB,KAAK,mBAAmB,UAI5B,KAAK,aAAa,QAAU,KAAK,mBAAmB,MAAK,EACzD,KAAK,aAAa,UAAY,KAAK,8BAA8B,MAAK,EACtE,KAAK,aAAa,QAAU,KAAK,4BAA4B,MAAK,EAClE,KAAK,aAAa,IAAM,KAAK,uBAAuB,MAAK,EACzD,KAAK,aAAa,SAAW,KAAK,aAC9B,KAAK,gBAAgB,qBACrB,KAAK,aAAa,OAAS,KAAK,2BAA2B,MAAK,GAGpE,KAAK,mBAAmB,OAAS,EACjC,KAAK,8BAA8B,OAAS,EAC5C,KAAK,2BAA2B,OAAS,EACzC,KAAK,4BAA4B,OAAS,EAC1C,KAAK,uBAAuB,OAAS,EACrC,KAAK,aAAe,GAE3B,CACD,iBAAiBkB,EAAM,CACnB,MAAMC,EAAYD,EAAK,gBAAgBE,EAAa,YAAY,EAC1DC,EAAUH,EAAK,gBAAgBE,EAAa,UAAU,EACtDE,EAAc,CAAA,EACpB,GAAI,CAACH,GAAa,CAACE,EACf,OAEJ,QAAS1C,EAAI,EAAGA,EAAIwC,EAAU,OAAS,EAAGxC,IAAK,CAC3C,MAAM4C,EAAIJ,EAAUxC,EAAI,EAAI,CAAC,EACvB6C,EAAIL,EAAUxC,EAAI,EAAI,CAAC,EACvB8C,EAAIN,EAAUxC,EAAI,EAAI,CAAC,EACvBG,EAAMyC,EAAI,IAAMC,EAAI,IAAMC,EAChC,IAAIC,EAAMJ,EAAYxC,CAAG,EACpB4C,IACDA,EAAM,CAAA,EACNJ,EAAYxC,CAAG,EAAI4C,GAEvBA,EAAI,KAAK/C,CAAC,CACb,CACD,MAAMgD,EAAS,IAAIX,EACnB,UAAWlC,KAAOwC,EAAa,CAC3B,MAAMI,EAAMJ,EAAYxC,CAAG,EAC3B,GAAI4C,EAAI,OAAS,EACb,SAEJ,MAAME,EAAQF,EAAI,CAAC,EACnB,QAAS/C,EAAI,EAAGA,EAAI+C,EAAI,OAAQ,EAAE/C,EAAG,CACjC,MAAMkD,EAAOH,EAAI/C,CAAC,EAClB0C,EAAQO,EAAQ,EAAI,CAAC,GAAKP,EAAQQ,EAAO,EAAI,CAAC,EAC9CR,EAAQO,EAAQ,EAAI,CAAC,GAAKP,EAAQQ,EAAO,EAAI,CAAC,EAC9CR,EAAQO,EAAQ,EAAI,CAAC,GAAKP,EAAQQ,EAAO,EAAI,CAAC,CACjD,CACDF,EAAO,eAAeN,EAAQO,EAAQ,EAAI,CAAC,EAAGP,EAAQO,EAAQ,EAAI,CAAC,EAAGP,EAAQO,EAAQ,EAAI,CAAC,CAAC,EAC5FD,EAAO,UAAS,EAChB,QAAShD,EAAI,EAAGA,EAAI+C,EAAI,OAAQ,EAAE/C,EAAG,CACjC,MAAMkD,EAAOH,EAAI/C,CAAC,EAClB0C,EAAQQ,EAAO,EAAI,CAAC,EAAIF,EAAO,EAC/BN,EAAQQ,EAAO,EAAI,CAAC,EAAIF,EAAO,EAC/BN,EAAQQ,EAAO,EAAI,CAAC,EAAIF,EAAO,CAClC,CACJ,CACDT,EAAK,gBAAgBE,EAAa,WAAYC,CAAO,CACxD,CACD,OAAO,eAAezC,EAAM,CACxB,OAAOA,EAAK,WAAW,GAAG,CAC7B,CACD,OAAO,iBAAiBA,EAAM,CAC1B,OAAOA,EAAK,WAAW,GAAG,CAC7B,CACD,OAAO,gBAAgBA,EAAM,CACzB,OAAOA,EAAK,WAAW,GAAG,CAC7B,CACD,OAAO,eAAeA,EAAMkD,EAAU,CAClC,GAAI,CAAClD,EAAK,WAAW,MAAM,EACvB,OAAO,KAGX,GAFAA,EAAOA,EAAK,QAAQ,OAAQ,EAAE,EAAE,OAE5BkD,EACA,MAAO,GACX,MAAMC,EAAQ,YACRC,EAAWpD,EAAK,MAAMmD,CAAK,EACjC,GAAI,CAACC,GAAYA,EAAS,OAAS,IAAM,EACrC,MAAO,GAEX,MAAMC,EAAQ,CAAA,EACd,QAASC,EAAW,EAAGA,EAAWF,EAAS,OAAS,EAAGE,IAAY,CAG/D,MAAMC,EAAIH,EAASE,EAAW,EAAI,CAAC,EAAIF,EAASE,EAAW,EAAI,CAAC,EAC1DE,EAAIJ,EAASE,EAAW,EAAI,CAAC,EAAIF,EAASE,EAAW,EAAI,CAAC,EAC1DG,EAAIL,EAASE,EAAW,EAAI,CAAC,EAAIF,EAASE,EAAW,EAAI,CAAC,EAChED,EAAM,KAAK,IAAIrC,EAAO,SAASuC,EAAG,EAAE,EAAI,IAAK,SAASC,EAAG,EAAE,EAAI,IAAK,SAASC,EAAG,EAAE,EAAI,IAAK,CAAC,CAAC,CAChG,CACD,OAAOJ,CACV,CASD,MAAMK,EAAalE,EAAMD,EAAOG,EAAgBiE,EAAmB,OAG/DnE,EAAOA,EAAK,QAAQ,SAAU,MAAM,EACpCA,EAAOA,EAAK,QAAQ,SAAU,EAAE,EAAE,OAC9B,KAAK,gBAAgB,mBACrB,KAAK,cAAgB,CAACsC,EAAOE,IAAc,KAAK,WAAW,KAAKF,EAAM,CAAC,EAAGA,EAAME,CAAS,EAAGF,EAAME,EAAY,CAAC,CAAC,EAChH,KAAK,gBAAkB,GAElBzC,EAAM,sBACX,KAAK,cAAgB,CAACuC,EAAOE,IAAc,KAAK,WAAW,KAAKF,EAAM,CAAC,EAAGA,EAAME,EAAY,CAAC,EAAGF,EAAME,CAAS,CAAC,EAChH,KAAK,gBAAkB,IAGvB,KAAK,cAAgB,CAACF,EAAOE,IAAc,KAAK,WAAW,KAAKF,EAAM,CAAC,EAAGA,EAAME,CAAS,EAAGF,EAAME,EAAY,CAAC,CAAC,EAChH,KAAK,gBAAkB,IAI3B,MAAM4B,EAAWpE,EAAK,MAAM;AAAA,CAAI,EAC1BqE,EAAY,CAAA,EAClB,IAAIC,EAAe,CAAA,EACnBD,EAAU,KAAKC,CAAY,EAC3B,QAAS/D,EAAI,EAAGA,EAAI6D,EAAS,OAAQ7D,IAAK,CACtC,MAAMC,EAAO4D,EAAS7D,CAAC,EAAE,KAAI,EAAG,QAAQ,QAAS,GAAG,EAEpD,GAAI,EAAAC,EAAK,SAAW,GAAKA,EAAK,OAAO,CAAC,IAAM,KAO5C,IAJIY,EAAY,gBAAgBZ,CAAI,GAAKY,EAAY,iBAAiBZ,CAAI,KACtE8D,EAAe,CAAA,EACfD,EAAU,KAAKC,CAAY,GAE3BlD,EAAY,eAAeZ,CAAI,EAAG,CAClC,MAAM+D,EAAa/D,EAAK,MAAM,GAAG,EAEjC,QAASD,EAAI,EAAGA,EAAIgE,EAAW,OAAS,EAAGhE,IACvC+D,EAAa,KAAK,KAAKC,EAAWhE,CAAC,CAAC,IAAIgE,EAAWhE,EAAI,CAAC,CAAC,EAAE,CAElE,MAEG+D,EAAa,KAAK9D,CAAI,CAE7B,CACD,MAAML,EAAQkE,EAAU,OAExB,QAAS9D,EAAI,EAAGA,EAAIJ,EAAM,OAAQI,IAAK,CACnC,MAAMC,EAAOL,EAAMI,CAAC,EAAE,KAAI,EAAG,QAAQ,QAAS,GAAG,EACjD,IAAIiE,EAEJ,GAAI,EAAAhE,EAAK,SAAW,GAAKA,EAAK,OAAO,CAAC,IAAM,KAGvC,GAAIY,EAAY,cAAc,KAAKZ,CAAI,GAOxC,GALAgE,EAAShE,EAAK,MAAM,QAAQ,EAI5B,KAAK,WAAW,KAAK,IAAIoC,EAAQ,WAAW4B,EAAO,CAAC,CAAC,EAAG,WAAWA,EAAO,CAAC,CAAC,EAAG,WAAWA,EAAO,CAAC,CAAC,CAAC,CAAC,EACjG,KAAK,gBAAgB,mBACrB,GAAIA,EAAO,QAAU,EAAG,CACpB,MAAMT,EAAI,WAAWS,EAAO,CAAC,CAAC,EACxBR,EAAI,WAAWQ,EAAO,CAAC,CAAC,EACxBP,EAAI,WAAWO,EAAO,CAAC,CAAC,EAC9B,KAAK,QAAQ,KAAK,IAAIhD,EAAOuC,EAAI,EAAIA,EAAI,IAAMA,EAAGC,EAAI,EAAIA,EAAI,IAAMA,EAAGC,EAAI,EAAIA,EAAI,IAAMA,EAAGO,EAAO,SAAW,GAAKA,EAAO,CAAC,IAAM,OAAY,EAAI,WAAWA,EAAO,CAAC,CAAC,CAAC,CAAC,CAC1K,MAGG,KAAK,QAAQ,KAAK,KAAK,UAAU,WAInCA,EAASpD,EAAY,cAAc,KAAKZ,CAAI,KAAO,KAKzD,KAAK,SAAS,KAAK,IAAIoC,EAAQ,WAAW4B,EAAO,CAAC,CAAC,EAAG,WAAWA,EAAO,CAAC,CAAC,EAAG,WAAWA,EAAO,CAAC,CAAC,CAAC,CAAC,WAE7FA,EAASpD,EAAY,UAAU,KAAKZ,CAAI,KAAO,KAKrD,KAAK,KAAK,KAAK,IAAI4B,EAAQ,WAAWoC,EAAO,CAAC,CAAC,EAAI,KAAK,gBAAgB,UAAU,EAAG,WAAWA,EAAO,CAAC,CAAC,EAAI,KAAK,gBAAgB,UAAU,CAAC,CAAC,WAIxIA,EAASpD,EAAY,aAAa,KAAKZ,CAAI,KAAO,KAIxD,KAAK,mCAAmCgE,EAAO,CAAC,EAAE,KAAM,EAAC,MAAM,GAAG,EAClE,CAAC,WAEKA,EAASpD,EAAY,aAAa,KAAKZ,CAAI,KAAO,KAIxD,KAAK,mCAAmCgE,EAAO,CAAC,EAAE,KAAM,EAAC,MAAM,GAAG,EAClE,CAAC,WAEKA,EAASpD,EAAY,aAAa,KAAKZ,CAAI,KAAO,KAIxD,KAAK,mCAAmCgE,EAAO,CAAC,EAAE,KAAM,EAAC,MAAM,GAAG,EAClE,CAAC,WAEKA,EAASpD,EAAY,aAAa,KAAKZ,CAAI,KAAO,KAIxD,KAAK,mCAAmCgE,EAAO,CAAC,EAAE,KAAM,EAAC,MAAM,GAAG,EAClE,CAAC,WAEKA,EAASpD,EAAY,aAAa,KAAKZ,CAAI,KAAO,KAIxD,KAAK,mCAAmCgE,EAAO,CAAC,EAAE,KAAM,EAAC,MAAM,GAAG,EAClE,CAAC,WAIKA,EAASpD,EAAY,aAAa,KAAKZ,CAAI,KAAO,KAIxD,KAAK,mCAAmCgE,EAAO,CAAC,EAAE,KAAM,EAAC,MAAM,GAAG,EAClE,CAAC,EACD,KAAK,aAAe,YAIdA,EAASpD,EAAY,aAAa,KAAKZ,CAAI,KAAO,KAIxD,KAAK,mCAAmCgE,EAAO,CAAC,EAAE,KAAM,EAAC,MAAM,GAAG,EAClE,CAAC,EACD,KAAK,aAAe,WAIdA,EAASpD,EAAY,eAAeZ,EAAM,CAAC,KAAK,gBAAgB,kBAAkB,EACxFgE,EAAO,QAASC,GAAY,CACxB,KAAK,WAAW,KAAKA,CAAO,CAChD,CAAiB,WAEKD,EAASpD,EAAY,aAAa,KAAKZ,CAAI,KAAO,KAIxD,KAAK,mCAAmCgE,EAAO,CAAC,EAAE,KAAM,EAAC,MAAM,GAAG,EAClE,CAAC,EACD,KAAK,aAAe,WAIfpD,EAAY,gBAAgB,KAAKZ,CAAI,GAAKY,EAAY,iBAAiB,KAAKZ,CAAI,EAAG,CAGxF,MAAMkE,EAAU,CACZ,KAAMlE,EAAK,UAAU,CAAC,EAAE,KAAM,EAC9B,QAAS,KACT,UAAW,KACX,QAAS,KACT,IAAK,KACL,OAAQ,KACR,aAAc,KAAK,qBACnB,SAAUY,EAAY,iBAAiB,KAAKZ,CAAI,CACpE,EACgB,KAAK,oBAAmB,EAExB,KAAK,eAAe,KAAKkE,CAAO,EAEhC,KAAK,WAAa,GAClB,KAAK,iBAAmB,GACxB,KAAK,WAAa,CAErB,SACQtD,EAAY,iBAAiB,KAAKZ,CAAI,EAAG,CAI9C,GAFA,KAAK,qBAAuBA,EAAK,UAAU,CAAC,EAAE,OAE1C,CAAC,KAAK,kBAAoB,CAAC,KAAK,WAAY,CAE5C,KAAK,oBAAmB,EAExB,MAAMkE,EAEN,CACI,MAAO,KAAK,cAAgB,QAAU,MAAQ,KAAK,WAAW,SAAU,EACxE,QAAS,KACT,UAAW,KACX,QAAS,KACT,IAAK,KACL,OAAQ,KACR,aAAc,KAAK,qBACnB,SAAU,EAClC,EACoB,KAAK,aAEL,KAAK,eAAe,KAAKA,CAAO,EAChC,KAAK,WAAa,EACrB,CAEG,KAAK,YAAc,KAAK,mBAExB,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,EAAE,aAAe,KAAK,qBACxE,KAAK,iBAAmB,GAG/B,MACQtD,EAAY,sBAAsB,KAAKZ,CAAI,EAEhD2D,EAAkB3D,EAAK,UAAU,CAAC,EAAE,KAAM,CAAA,EAGrCY,EAAY,iBAAiB,KAAKZ,CAAI,GAO3CmE,EAAO,IAAI,kCAAoCnE,CAAI,CAE1D,CAsBD,GApBI,KAAK,aAEL,KAAK,aAAe,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,EAClE,KAAK,gBAAgB,mBAErB,KAAK,mBAAmB,UAG5B,KAAK,YAAW,EAEhB,KAAK,aAAa,QAAU,KAAK,mBACjC,KAAK,aAAa,UAAY,KAAK,8BACnC,KAAK,aAAa,QAAU,KAAK,4BACjC,KAAK,aAAa,IAAM,KAAK,uBAC7B,KAAK,aAAa,SAAW,KAAK,aAC9B,KAAK,gBAAgB,qBACrB,KAAK,aAAa,OAAS,KAAK,6BAIpC,CAAC,KAAK,WAAY,CAClB,IAAIoE,EAAc,KAClB,GAAI,KAAK,mBAAmB,OACpB,KAAK,gBAAgB,mBAErB,KAAK,mBAAmB,UAG5B,KAAK,YAAW,MAEf,CAED,UAAWnE,KAAO,KAAK,WACnB,KAAK,8BAA8B,KAAKA,EAAI,EAAGA,EAAI,EAAGA,EAAI,CAAC,EAE/D,GAAI,KAAK,SAAS,OACd,UAAW8C,KAAU,KAAK,SACtB,KAAK,4BAA4B,KAAKA,EAAO,EAAGA,EAAO,EAAGA,EAAO,CAAC,EAG1E,GAAI,KAAK,KAAK,OACV,UAAWsB,KAAM,KAAK,KAClB,KAAK,uBAAuB,KAAKA,EAAG,EAAGA,EAAG,CAAC,EAGnD,GAAI,KAAK,WAAW,OAChB,UAAWxE,KAAS,KAAK,WACrB,KAAK,2BAA2B,KAAKA,EAAM,EAAGA,EAAM,EAAGA,EAAM,EAAGA,EAAM,CAAC,UAIvE,KAAK,QAAQ,OACb,UAAWA,KAAS,KAAK,QACrB,KAAK,2BAA2B,KAAKA,EAAM,EAAGA,EAAM,EAAGA,EAAM,EAAGA,EAAM,CAAC,EAI9E,KAAK,uBAENuE,EAAc,IAAIhE,EAAiBkE,EAAS,SAAU,EAAE/E,CAAK,EAC7D6E,EAAY,YAAc,GAC1B,KAAK,qBAAuBA,EAAY,KACnC,KAAK,SAAS,SACfA,EAAY,gBAAkB,GAC9BA,EAAY,cAAgB/D,EAAO,SAG9C,CAED,KAAK,eAAe,KAAK,CACrB,KAAMiE,EAAS,SAAU,EACzB,QAAS,KAAK,mBACd,UAAW,KAAK,8BAChB,OAAQ,KAAK,2BACb,QAAS,KAAK,4BACd,IAAK,KAAK,uBACV,aAAc,KAAK,qBACnB,eAAgBF,EAChB,SAAU,GACV,SAAU,KAAK,YAC/B,CAAa,CACJ,CAED,QAASG,EAAI,EAAGA,EAAI,KAAK,eAAe,OAAQA,IAAK,CAEjD,GAAIb,GAAe,KAAK,eAAea,CAAC,EAAE,MACtC,GAAIb,aAAuB,OACvB,GAAIA,EAAY,QAAQ,KAAK,eAAea,CAAC,EAAE,IAAI,IAAM,GACrD,iBAIA,KAAK,eAAeA,CAAC,EAAE,OAASb,EAChC,SAMZ,KAAK,aAAe,KAAK,eAAea,CAAC,EAEzChF,EAAM,uBAAyB,CAAC,CAACG,EACjC,MAAM8E,EAAc,IAAIC,EAAK,KAAK,eAAeF,CAAC,EAAE,KAAMhF,CAAK,EAK/D,GAJAiF,EAAY,iBAAmB9E,EAC/BH,EAAM,uBAAyB,GAC/B,KAAK,aAAa,aAAeiF,EAE7B,CAAC,KAAK,aAAa,UACnB,QAASrC,EAAIoC,EAAI,EAAGpC,GAAK,EAAG,EAAEA,EAC1B,GAAI,KAAK,eAAeA,CAAC,EAAE,UAAY,KAAK,eAAeA,CAAC,EAAE,aAAc,CACxEqC,EAAY,OAAS,KAAK,eAAerC,CAAC,EAAE,aAC5C,KACH,EAWT,GANA,KAAK,eAAe,KAAK,KAAK,eAAeoC,CAAC,EAAE,YAAY,EAExD,KAAK,aAAa,WAClBC,EAAY,oBAAsBA,EAAY,kBAAoB,CAAE,GACpEA,EAAY,kBAAkB,QAAa,MAE3CE,EAAA,KAAK,aAAa,YAAlB,YAAAA,EAA6B,UAAW,EAAG,CAE3C,KAAK,oBAAoB,KAAKF,CAAW,EACzC,QACH,CACD,MAAMG,EAAa,IAAIC,EAKvB,GAHAD,EAAW,IAAM,KAAK,aAAa,IACnCA,EAAW,QAAU,KAAK,aAAa,QACvCA,EAAW,UAAY,KAAK,aAAa,UACrC,KAAK,gBAAgB,eAAgB,CACrC,MAAMlC,EAAU,IAAI,MACpBmC,EAAW,eAAe,KAAK,aAAa,UAAW,KAAK,aAAa,QAASnC,CAAO,EACzFkC,EAAW,QAAUlC,CACxB,MAEGkC,EAAW,QAAU,KAAK,aAAa,QAEvC,KAAK,gBAAgB,qBACrBA,EAAW,OAAS,KAAK,aAAa,QAG1CA,EAAW,YAAYH,CAAW,EAC9B,KAAK,gBAAgB,UACrBA,EAAY,QAAQ,GAAK,IAEzB,KAAK,gBAAgB,iBACrB,KAAK,iBAAiBA,CAAW,EAGrC,KAAK,oBAAoB,KAAKA,CAAW,EACrC,KAAK,aAAa,iBAClBA,EAAY,SAAW,KAAK,aAAa,eAEhD,CACJ,CACL,CAGA5D,EAAY,iBAAmB,KAE/BA,EAAY,gBAAkB,KAE9BA,EAAY,sBAAwB,WAEpCA,EAAY,iBAAmB,WAE/BA,EAAY,iBAAmB,MAG/BA,EAAY,cAAgB,+BAE5BA,EAAY,cAAgB,mEAE5BA,EAAY,UAAY,+CAExBA,EAAY,aAAe,8BAE3BA,EAAY,aAAe,0CAE3BA,EAAY,aAAe,oDAE3BA,EAAY,aAAe,4CAE3BA,EAAY,aAAe,uDAE3BA,EAAY,aAAe,8BAE3BA,EAAY,aAAe,0CAE3BA,EAAY,aAAe,oDC90BpB,MAAMiE,CAAc,CAIvB,WAAW,kBAAmB,CAC1B,OAAOvF,EAAc,gBACxB,CACD,WAAW,iBAAiBa,EAAO,CAC/Bb,EAAc,iBAAmBa,CACpC,CAMD,YAAYY,EAAgB,CAIxB,KAAK,KAAO+D,EAAsB,KAIlC,KAAK,WAAaA,EAAsB,WACxC,KAAK,gBAAkB,KACvB,KAAK,gBAAkB,CAAE,GAAGD,EAAc,uBAAwB,GAAI9D,GAAkB,CAAA,EAC3F,CACD,WAAW,wBAAyB,CAChC,MAAO,CACH,eAAgB8D,EAAc,gBAC9B,gBAAiBA,EAAc,iBAC/B,mBAAoBA,EAAc,qBAClC,QAASA,EAAc,SACvB,eAAgBA,EAAc,iBAE9B,UAAWA,EAAc,WACzB,6BAA8BA,EAAc,gCAC5C,eAAgBA,EAAc,iBAC9B,cAAeA,EAAc,eAC7B,kBAAmBA,EAAc,mBAC7C,CACK,CAYD,SAASpE,EAAKhB,EAASsF,EAAWC,EAAW,CAEzC,MAAMC,EAAaxF,EAAUgB,EAE7ByE,EAAM,SAASD,EAAYF,EAAW,OAAW,OAAW,GAAO,CAACI,EAASC,IAAc,CACvFJ,EAAUC,EAAYG,CAAS,CAC3C,CAAS,CACJ,CAED,aAAaC,EAAS,CAClB,OAAO,IAAIR,EAAcQ,EAAQP,EAAsB,IAAI,CAAC,CAC/D,CAKD,eAAgB,CACZ,MAAO,EACV,CASD,gBAAgBpB,EAAanE,EAAOC,EAAMC,EAAS,CAE/C,OAAO,KAAK,YAAYiE,EAAanE,EAAOC,EAAMC,CAAO,EAAE,KAAM6F,IACtD,CACH,OAAQA,EACR,gBAAiB,CAAE,EACnB,UAAW,CAAE,EACb,gBAAiB,CAAE,EACnB,eAAgB,CAAE,EAClB,WAAY,CAAE,EACd,OAAQ,CAAE,EACV,eAAgB,CAAE,CAClC,EACS,CACJ,CAQD,UAAU/F,EAAOC,EAAMC,EAAS,CAE5B,OAAO,KAAK,gBAAgB,KAAMF,EAAOC,EAAMC,CAAO,EAAE,KAAK,IAAM,CAE3E,CAAS,CACJ,CAQD,wBAAwBF,EAAOC,EAAMC,EAAS,CAC1C,MAAM8F,EAAY,IAAIC,EAAejG,CAAK,EAC1C,YAAK,gBAAkBgG,EAChB,KAAK,gBAAgB,KAAMhG,EAAOC,EAAMC,CAAO,EACjD,KAAMuE,IACPA,EAAO,OAAO,QAAS1B,GAASiD,EAAU,OAAO,KAAKjD,CAAI,CAAC,EAC3D0B,EAAO,OAAO,QAAS1B,GAAS,CAC5B,MAAMxC,EAAWwC,EAAK,SAClBxC,GAEIyF,EAAU,UAAU,QAAQzF,CAAQ,GAAK,KACzCyF,EAAU,UAAU,KAAKzF,CAAQ,EAEhBA,EAAS,oBACjB,QAAS2F,GAAM,CAChBF,EAAU,SAAS,QAAQE,CAAC,GAAK,IACjCF,EAAU,SAAS,KAAKE,CAAC,CAEzD,CAAyB,EAGzB,CAAa,EACD,KAAK,gBAAkB,KAChBF,EACV,EACI,MAAOG,GAAO,CACf,WAAK,gBAAkB,KACjBA,CAClB,CAAS,CACJ,CAWD,YAAYhC,EAAanE,EAAOC,EAAMC,EAAS,CAC3C,IAAIkG,EAAa,GACjB,MAAMC,EAAuB,IAAItG,EAC3BuB,EAAgB,CAAA,EAChBC,EAAqB,CAAA,EAE3BtB,EAAOA,EAAK,QAAQ,SAAU,EAAE,EAAE,OAEd,IAAIoB,EAAYC,EAAeC,EAAoB,KAAK,eAAe,EAC/E,MAAM4C,EAAalE,EAAMD,EAAO,KAAK,gBAAkBsG,GAAa,CAC5EF,EAAaE,CACzB,CAAS,EAED,MAAMC,EAAc,CAAA,EAEpB,OAAIH,IAAe,IAAM,CAAC,KAAK,gBAAgB,eAE3CG,EAAY,KAAK,IAAI,QAAQ,CAACC,EAASC,IAAW,CAC9C,KAAK,SAASL,EAAYlG,EAAUwG,GAAe,CAC/C,GAAI,CAEAL,EAAqB,SAASrG,EAAO0G,EAAYxG,EAAS,KAAK,eAAe,EAE9E,QAASyG,EAAI,EAAGA,EAAIN,EAAqB,UAAU,OAAQM,IAAK,CAE5D,IAAIC,EAAa,EACjB,MAAMC,EAAW,CAAA,EACjB,IAAIzE,EAIJ,MAAQA,EAASd,EAAc,QAAQ+E,EAAqB,UAAUM,CAAC,EAAE,KAAMC,CAAU,GAAK,IAC1FC,EAAS,KAAKzE,CAAM,EACpBwE,EAAaxE,EAAS,EAG1B,GAAIA,IAAW,IAAMyE,EAAS,SAAW,EAErCR,EAAqB,UAAUM,CAAC,EAAE,QAAO,MAGzC,SAASG,EAAI,EAAGA,EAAID,EAAS,OAAQC,IAAK,CAEtC,MAAM/D,EAAOxB,EAAmBsF,EAASC,CAAC,CAAC,EACrCvG,EAAW8F,EAAqB,UAAUM,CAAC,EACjD5D,EAAK,SAAWxC,EACXwC,EAAK,oBAENxC,EAAS,YAAc,GAE9B,CAER,CACDiG,GACH,OACMO,EAAG,CACNpB,EAAM,KAAK,+BAA+BS,CAAU,GAAG,EACnD,KAAK,gBAAgB,6BACrBI,IAGAC,EAAOM,CAAC,CAEf,CACrB,EAAmB,CAACrB,EAAYG,IAAc,CAC1BF,EAAM,KAAK,gCAAgCS,CAAU,GAAG,EACpD,KAAK,gBAAgB,6BACrBI,IAGAC,EAAOZ,CAAS,CAExC,CAAiB,CACJ,CAAA,CAAC,EAGC,QAAQ,IAAIU,CAAW,EAAE,KAAK,IAAM,CACvC,MAAMS,EAAUjE,UAAS,WAAQoC,EAAApC,EAAK,oBAAL,YAAAoC,EAAyB,UAAc,KAExE,OAAA5D,EAAmB,QAASwB,GAAS,CACjC,GAAIiE,EAAOjE,CAAI,EAAG,CACd,IAAIkE,EAAMlE,EAAK,UAAY,IAAIlC,EAAiBkC,EAAK,KAAO,QAAS/C,CAAK,EAExDiH,EAAI,gBAAe,EAAG,OAAQF,GAAM,CAACC,EAAOD,CAAC,CAAC,EAAE,OAAS,IAEvEE,EAAMA,EAAI,MAAMA,EAAI,KAAO,OAAO,GAAKA,GAE3CA,EAAI,UAAY,GAChBlE,EAAK,SAAWkE,EACZlE,EAAK,oBACLA,EAAK,kBAAkB,QAAa,OAE3C,CACjB,CAAa,EACMxB,CACnB,CAAS,CACJ,CACL,CAIA+D,EAAc,iBAAmB,GAIjCA,EAAc,SAAW,GAIzBA,EAAc,qBAAuB,GAIrCA,EAAc,gBAAkB,GAKhCA,EAAc,iBAAmB,GAIjCA,EAAc,WAAa,IAAIjD,EAAQ,EAAG,CAAC,EAI3CiD,EAAc,eAAiB,GAM/BA,EAAc,gCAAkC,GAIhDA,EAAc,oBAAsB,GAEpC4B,EAA0B,IAAI5B,CAAe", "x_google_ignoreList": [0, 1, 2]}