import{SvelteComponent as c,init as u,safe_not_equal as d,svg_element as a,claim_svg_element as h,children as l,detach as o,attr as t,insert_hydration as p,append_hydration as m,noop as s}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function g(v){let e,r;return{c(){e=a("svg"),r=a("path"),this.h()},l(i){e=h(i,"svg",{xmlns:!0,"xmlns:xlink":!0,"aria-hidden":!0,role:!0,class:!0,width:!0,height:!0,preserveAspectRatio:!0,viewBox:!0});var n=l(e);r=h(n,"path",{fill:!0,d:!0}),l(r).forEach(o),n.forEach(o),this.h()},h(){t(r,"fill","currentColor"),t(r,"d","M4 2H2v26a2 2 0 0 0 2 2h26v-2H4v-3h22v-8H4v-4h14V5H4Zm20 17v4H4v-4ZM16 7v4H4V7Z"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),t(e,"aria-hidden","true"),t(e,"role","img"),t(e,"class","iconify iconify--carbon"),t(e,"width","100%"),t(e,"height","100%"),t(e,"preserveAspectRatio","xMidYMid meet"),t(e,"viewBox","0 0 32 32")},m(i,n){p(i,e,n),m(e,r)},p:s,i:s,o:s,d(i){i&&o(e)}}}class f extends c{constructor(e){super(),u(this,e,null,g,d,{})}}export{f as L};
//# sourceMappingURL=LineChart.C8bDl53w.js.map
