const{SvelteComponent:m,append:_,attr:d,detach:w,element:o,flush:f,init:p,insert:T,listen:b,noop:k,run_all:y,safe_not_equal:C,set_data:x,space:E,text:q,toggle_class:g}=window.__gradio__svelte__internal,{createEventDispatcher:D}=window.__gradio__svelte__internal;function S(a){let e,t,h,l,r,s,u;return{c(){e=o("label"),t=o("input"),h=E(),l=o("span"),r=q(a[1]),t.disabled=a[2],d(t,"type","checkbox"),d(t,"name","test"),d(t,"data-testid","checkbox"),d(t,"class","svelte-1a15wmk"),d(l,"class","svelte-1a15wmk"),d(e,"class","svelte-1a15wmk"),g(e,"disabled",a[2])},m(c,i){T(c,e,i),_(e,t),t.checked=a[0],_(e,h),_(e,l),_(l,r),s||(u=[b(t,"change",a[6]),b(t,"keydown",a[3]),b(t,"input",a[4])],s=!0)},p(c,[i]){i&4&&(t.disabled=c[2]),i&1&&(t.checked=c[0]),i&2&&x(r,c[1]),i&4&&g(e,"disabled",c[2])},i:k,o:k,d(c){c&&w(e),s=!1,y(u)}}}function j(a,e,t){let h,{value:l=!1}=e,{label:r="Checkbox"}=e,{interactive:s}=e;const u=D();async function c(n){n.key==="Enter"&&(t(0,l=!l),u("select",{index:0,value:n.currentTarget.checked,selected:n.currentTarget.checked}))}async function i(n){t(0,l=n.currentTarget.checked),u("select",{index:0,value:n.currentTarget.checked,selected:n.currentTarget.checked})}function v(){l=this.checked,t(0,l)}return a.$$set=n=>{"value"in n&&t(0,l=n.value),"label"in n&&t(1,r=n.label),"interactive"in n&&t(5,s=n.interactive)},a.$$.update=()=>{a.$$.dirty&1&&u("change",l),a.$$.dirty&32&&t(2,h=!s)},[l,r,h,c,i,s,v]}class z extends m{constructor(e){super(),p(this,e,j,S,C,{value:0,label:1,interactive:5})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),f()}get interactive(){return this.$$.ctx[5]}set interactive(e){this.$$set({interactive:e}),f()}}export{z as C};
//# sourceMappingURL=Checkbox-CjOIpf6b.js.map
