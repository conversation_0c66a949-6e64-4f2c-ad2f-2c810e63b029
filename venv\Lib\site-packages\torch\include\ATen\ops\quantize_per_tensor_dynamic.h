#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/quantize_per_tensor_dynamic_ops.h>

namespace at {


// aten::quantize_per_tensor_dynamic(Tensor self, ScalarType dtype, bool reduce_range) -> Tensor
inline at::Tensor quantize_per_tensor_dynamic(const at::Tensor & self, at::ScalarType dtype, bool reduce_range) {
    return at::_ops::quantize_per_tensor_dynamic::call(self, dtype, reduce_range);
}

// aten::quantize_per_tensor_dynamic.out(Tensor self, <PERSON>alarType dtype, bool reduce_range, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & quantize_per_tensor_dynamic_out(at::Tensor & out, const at::Tensor & self, at::ScalarType dtype, bool reduce_range) {
    return at::_ops::quantize_per_tensor_dynamic_out::call(self, dtype, reduce_range, out);
}
// aten::quantize_per_tensor_dynamic.out(Tensor self, ScalarType dtype, bool reduce_range, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & quantize_per_tensor_dynamic_outf(const at::Tensor & self, at::ScalarType dtype, bool reduce_range, at::Tensor & out) {
    return at::_ops::quantize_per_tensor_dynamic_out::call(self, dtype, reduce_range, out);
}

}
