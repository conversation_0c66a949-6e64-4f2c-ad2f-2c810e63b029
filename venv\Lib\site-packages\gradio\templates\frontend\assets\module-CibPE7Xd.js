import{c as Jn,a as Qn,g as er}from"./module-C-VadMaF.js";import{k as Zt}from"./index-B7J2Z2jS.js";import"./svelte/svelte.js";const Ot=new Set,tr=Jn({encode:({call:e})=>async(t,n)=>{const r=await e("encode",{encoderId:t,timeslice:n});return Ot.delete(t),r},instantiate:({call:e})=>async(t,n)=>{const r=Qn(Ot),o=await e("instantiate",{encoderId:r,mimeType:t,sampleRate:n});return{encoderId:r,port:o}},register:({call:e})=>t=>e("register",{port:t},[t])}),nr=e=>{const t=new Worker(e);return tr(t)},rr=`(()=>{var e={775:function(e,t,r){!function(e,t,r,n){"use strict";var o=function(e,t){return void 0===t?e:t.reduce((function(e,t){if("capitalize"===t){var o=e.charAt(0).toUpperCase(),s=e.slice(1);return"".concat(o).concat(s)}return"dashify"===t?r(e):"prependIndefiniteArticle"===t?"".concat(n(e)," ").concat(e):e}),e)},s=function(e){var t=e.name+e.modifiers.map((function(e){return"\\\\.".concat(e,"\\\\(\\\\)")})).join("");return new RegExp("\\\\$\\\\{".concat(t,"}"),"g")},a=function(e,r){for(var n=/\\\${([^.}]+)((\\.[^(]+\\(\\))*)}/g,a=[],i=n.exec(e);null!==i;){var c={modifiers:[],name:i[1]};if(void 0!==i[3])for(var u=/\\.[^(]+\\(\\)/g,l=u.exec(i[2]);null!==l;)c.modifiers.push(l[0].slice(1,-2)),l=u.exec(i[2]);a.push(c),i=n.exec(e)}var d=a.reduce((function(e,n){return e.map((function(e){return"string"==typeof e?e.split(s(n)).reduce((function(e,s,a){return 0===a?[s]:n.name in r?[].concat(t(e),[o(r[n.name],n.modifiers),s]):[].concat(t(e),[function(e){return o(e[n.name],n.modifiers)},s])}),[]):[e]})).reduce((function(e,r){return[].concat(t(e),t(r))}),[])}),[e]);return function(e){return d.reduce((function(r,n){return[].concat(t(r),"string"==typeof n?[n]:[n(e)])}),[]).join("")}},i=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=void 0===e.code?void 0:a(e.code,t),n=void 0===e.message?void 0:a(e.message,t);function o(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=arguments.length>1?arguments[1]:void 0,s=void 0===o&&(t instanceof Error||void 0!==t.code&&"Exception"===t.code.slice(-9))?{cause:t,missingParameters:{}}:{cause:o,missingParameters:t},a=s.cause,i=s.missingParameters,c=void 0===n?new Error:new Error(n(i));return null!==a&&(c.cause=a),void 0!==r&&(c.code=r(i)),void 0!==e.status&&(c.status=e.status),c}return o};e.compile=i}(t,r(106),r(881),r(507))},881:e=>{"use strict";e.exports=(e,t)=>{if("string"!=typeof e)throw new TypeError("expected a string");return e.trim().replace(/([a-z])([A-Z])/g,"$1-$2").replace(/\\W/g,(e=>/[À-ž]/.test(e)?e:"-")).replace(/^-+|-+$/g,"").replace(/-{2,}/g,(e=>t&&t.condense?"-":e)).toLowerCase()}},107:function(e,t){!function(e){"use strict";var t=function(e){return function(t){var r=e(t);return t.add(r),r}},r=function(e){return function(t,r){return e.set(t,r),r}},n=void 0===Number.MAX_SAFE_INTEGER?9007199254740991:Number.MAX_SAFE_INTEGER,o=536870912,s=2*o,a=function(e,t){return function(r){var a=t.get(r),i=void 0===a?r.size:a<s?a+1:0;if(!r.has(i))return e(r,i);if(r.size<o){for(;r.has(i);)i=Math.floor(Math.random()*s);return e(r,i)}if(r.size>n)throw new Error("Congratulations, you created a collection of unique numbers which uses all available integers!");for(;r.has(i);)i=Math.floor(Math.random()*n);return e(r,i)}},i=new WeakMap,c=r(i),u=a(c,i),l=t(u);e.addUniqueNumber=l,e.generateUniqueNumber=u}(t)},507:e=>{var t=function(e){var t,r,n=/\\w+/.exec(e);if(!n)return"an";var o=(r=n[0]).toLowerCase(),s=["honest","hour","hono"];for(t in s)if(0==o.indexOf(s[t]))return"an";if(1==o.length)return"aedhilmnorsx".indexOf(o)>=0?"an":"a";if(r.match(/(?!FJO|[HLMNS]Y.|RY[EO]|SQU|(F[LR]?|[HL]|MN?|N|RH?|S[CHKLMNPTVW]?|X(YL)?)[AEIOU])[FHLMNRSX][A-Z]/))return"an";var a=[/^e[uw]/,/^onc?e\\b/,/^uni([^nmd]|mo)/,/^u[bcfhjkqrst][aeiou]/];for(t=0;t<a.length;t++)if(o.match(a[t]))return"a";return r.match(/^U[NK][AIEO]/)?"a":r==r.toUpperCase()?"aedhilmnorsx".indexOf(o[0])>=0?"an":"a":"aeiou".indexOf(o[0])>=0||o.match(/^y(b[lor]|cl[ea]|fere|gg|p[ios]|rou|tt)/)?"an":"a"};void 0!==e.exports?e.exports=t:window.indefiniteArticle=t},768:e=>{e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n},e.exports.__esModule=!0,e.exports.default=e.exports},907:(e,t,r)=>{var n=r(768);e.exports=function(e){if(Array.isArray(e))return n(e)},e.exports.__esModule=!0,e.exports.default=e.exports},642:e=>{e.exports=function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports},344:e=>{e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},106:(e,t,r)=>{var n=r(907),o=r(642),s=r(906),a=r(344);e.exports=function(e){return n(e)||o(e)||s(e)||a()},e.exports.__esModule=!0,e.exports.default=e.exports},906:(e,t,r)=>{var n=r(768);e.exports=function(e,t){if(e){if("string"==typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var s=t[n]={exports:{}};return e[n].call(s.exports,s,s.exports,r),s.exports}(()=>{"use strict";var e=r(775);const t=-32603,n=-32602,o=-32601,s=(0,e.compile)({message:'The requested method called "\${method}" is not supported.',status:o}),a=(0,e.compile)({message:'The handler of the method called "\${method}" returned no required result.',status:t}),i=(0,e.compile)({message:'The handler of the method called "\${method}" returned an unexpected result.',status:t}),c=(0,e.compile)({message:'The specified parameter called "portId" with the given value "\${portId}" does not identify a port connected to this worker.',status:n});var u=r(107);const l=new Map,d=(e,t,r)=>({...t,connect:r=>{let{port:n}=r;n.start();const o=e(n,t),s=(0,u.generateUniqueNumber)(l);return l.set(s,(()=>{o(),n.close(),l.delete(s)})),{result:s}},disconnect:e=>{let{portId:t}=e;const r=l.get(t);if(void 0===r)throw c({portId:t.toString()});return r(),{result:null}},isSupported:async()=>{if(await new Promise((e=>{const t=new ArrayBuffer(0),{port1:r,port2:n}=new MessageChannel;r.onmessage=t=>{let{data:r}=t;return e(null!==r)},n.postMessage(t,[t])}))){const e=r();return{result:e instanceof Promise?await e:e}}return{result:!1}}}),f=function(e,t){const r=d(f,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>!0),n=((e,t)=>async r=>{let{data:{id:n,method:o,params:c}}=r;const u=t[o];try{if(void 0===u)throw s({method:o});const t=void 0===c?u():u(c);if(void 0===t)throw a({method:o});const r=t instanceof Promise?await t:t;if(null===n){if(void 0!==r.result)throw i({method:o})}else{if(void 0===r.result)throw i({method:o});const{result:t,transferables:s=[]}=r;e.postMessage({id:n,result:t},s)}}catch(t){const{message:r,status:o=-32603}=t;e.postMessage({error:{code:o,message:r},id:n})}})(e,r);return e.addEventListener("message",n),()=>e.removeEventListener("message",n)},p=e=>{e.onmessage=null,e.close()},m=new WeakMap,h=new WeakMap,g=(e=>{const t=(r=e,{...r,connect:e=>{let{call:t}=e;return async()=>{const{port1:e,port2:r}=new MessageChannel,n=await t("connect",{port:e},[e]);return m.set(r,n),r}},disconnect:e=>{let{call:t}=e;return async e=>{const r=m.get(e);if(void 0===r)throw new Error("The given port is not connected.");await t("disconnect",{portId:r})}},isSupported:e=>{let{call:t}=e;return()=>t("isSupported")}});var r;return e=>{const r=(e=>{if(h.has(e))return h.get(e);const t=new Map;return h.set(e,t),t})(e);e.addEventListener("message",(e=>{let{data:t}=e;const{id:n}=t;if(null!==n&&r.has(n)){const{reject:e,resolve:o}=r.get(n);r.delete(n),void 0===t.error?o(t.result):e(new Error(t.error.message))}})),(e=>"function"==typeof e.start)(e)&&e.start();const n=function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return new Promise(((s,a)=>{const i=(0,u.generateUniqueNumber)(r);r.set(i,{reject:a,resolve:s}),null===n?e.postMessage({id:i,method:t},o):e.postMessage({id:i,method:t,params:n},o)}))},o=function(t,r){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];e.postMessage({id:null,method:t,params:r},n)};let s={};for(const[e,r]of Object.entries(t))s={...s,[e]:r({call:n,notify:o})};return{...s}}})({characterize:e=>{let{call:t}=e;return()=>t("characterize")},encode:e=>{let{call:t}=e;return(e,r)=>t("encode",{recordingId:e,timeslice:r})},record:e=>{let{call:t}=e;return async(e,r,n)=>{await t("record",{recordingId:e,sampleRate:r,typedArrays:n},n.map((e=>{let{buffer:t}=e;return t})))}}}),v=async(e,t)=>{const r=g(t),n=await r.characterize(),o=n.toString();if(e.has(o))throw new Error("There is already an encoder stored which handles exactly the same mime types.");return e.set(o,[n,r]),n},w=new Map,x=(e=>t=>{const r=e.get(t);if(void 0===r)throw new Error("There was no instance of an encoder stored with the given id.");return r})(w),y=((e,t)=>r=>{const n=t(r);return e.delete(r),n})(w,x),M=new Map,b=((e,t)=>r=>{const[n,o,s,a]=t(r);return s?new Promise((t=>{o.onmessage=s=>{let{data:i}=s;0===i.length?(e(o),t(n.encode(r,null))):n.record(r,a,i)}})):n.encode(r,null)})(p,y),E=(e=>t=>{for(const[r,n]of Array.from(e.values()))if(r.test(t))return n;throw new Error("There is no encoder registered which could handle the given mimeType.")})(M),A=((e,t,r)=>(n,o,s)=>{if(t.has(n))throw new Error('There is already an encoder registered with an id called "'.concat(n,'".'));const a=r(o),{port1:i,port2:c}=new MessageChannel,u=[a,i,!0,s];return t.set(n,u),i.onmessage=t=>{let{data:r}=t;0===r.length?(e(i),u[2]=!1):a.record(n,s,r.map((e=>"number"==typeof e?new Float32Array(e):e)))},c})(p,w,E),I=(e=>(t,r)=>{const[n]=e(t);return n.encode(t,r)})(x);f(self,{encode:async e=>{let{encoderId:t,timeslice:r}=e;const n=null===r?await b(t):await I(t,r);return{result:n,transferables:n}},instantiate:e=>{let{encoderId:t,mimeType:r,sampleRate:n}=e;const o=A(t,r,n);return{result:o,transferables:[o]}},register:async e=>{let{port:t}=e;return{result:await v(M,t)}}})})()})();`,or=new Blob([rr],{type:"application/javascript; charset=utf-8"}),Kt=URL.createObjectURL(or),lt=nr(Kt),Se=lt.encode,Jt=lt.instantiate,sr=lt.register;URL.revokeObjectURL(Kt);const ir=e=>(t,n)=>{if(e===null)throw new Error("A native BlobEvent could not be created.");return new e(t,n)},ar=(e,t)=>(n,r,o)=>{const s=[];let i=r,a=0;for(;a<n.byteLength;)if(i===null){const c=t(n,a);if(c===null)break;const{length:u,type:d}=c;i=d,a+=u}else{const c=e(n,a,i,o);if(c===null)break;const{content:u,length:d}=c;i=null,a+=d,u!==null&&s.push(u)}return{contents:s,currentElementType:i,offset:a}},cr=(e,t)=>class{constructor(r=null){this._listeners=new WeakMap,this._nativeEventTarget=r===null?e():r}addEventListener(r,o,s){if(o!==null){let i=this._listeners.get(o);i===void 0&&(i=t(this,o),typeof o=="function"&&this._listeners.set(o,i)),this._nativeEventTarget.addEventListener(r,i,s)}}dispatchEvent(r){return this._nativeEventTarget.dispatchEvent(r)}removeEventListener(r,o,s){const i=o===null?void 0:this._listeners.get(o);this._nativeEventTarget.removeEventListener(r,i===void 0?null:i,s)}},ur=e=>()=>{if(e===null)throw new Error("A native EventTarget could not be created.");return e.document.createElement("p")},lr=(e="")=>{try{return new DOMException(e,"InvalidModificationError")}catch(t){return t.code=13,t.message=e,t.name="InvalidModificationError",t}},dr=()=>{try{return new DOMException("","InvalidStateError")}catch(e){return e.code=11,e.name="InvalidStateError",e}},fr=e=>{if(e!==null&&e.BlobEvent!==void 0&&e.MediaStream!==void 0&&(e.MediaRecorder===void 0||e.MediaRecorder.isTypeSupported!==void 0)){if(e.MediaRecorder===void 0)return Promise.resolve(!0);const t=e.document.createElement("canvas"),n=t.getContext("2d");if(n===null||typeof t.captureStream!="function")return Promise.resolve(!1);const r=t.captureStream();return Promise.all([new Promise(o=>{const s="audio/webm";try{const i=new e.MediaRecorder(r,{mimeType:s});i.addEventListener("dataavailable",({data:a})=>o(a.type===s)),i.start(),setTimeout(()=>i.stop(),10)}catch(i){o(i.name==="NotSupportedError")}}),new Promise(o=>{const s=new e.MediaRecorder(r);let i=!1,a=!1;s.addEventListener("dataavailable",()=>i=!0),s.addEventListener("error",c=>{o(!i&&!a&&"error"in c&&c.error!==null&&typeof c.error=="object"&&"name"in c.error&&c.error.name!=="UnknownError")}),s.addEventListener("stop",()=>a=!0),s.start(),n.fillRect(0,0,1,1),r.removeTrack(r.getVideoTracks()[0])})]).then(o=>o.every(s=>s))}return Promise.resolve(!1)},hr=(e,t,n,r,o,s,i)=>class extends s{constructor(c,u={}){const{mimeType:d}=u;if(i!==null&&(d===void 0||i.isTypeSupported!==void 0&&i.isTypeSupported(d))){const l=e(i,c,u);super(l),this._internalMediaRecorder=l}else if(d!==void 0&&o.some(l=>l.test(d)))super(),i!==null&&i.isTypeSupported!==void 0&&i.isTypeSupported("audio/webm;codecs=pcm")?this._internalMediaRecorder=r(this,i,c,d):this._internalMediaRecorder=n(this,c,d);else throw i!==null&&e(i,c,u),t();this._ondataavailable=null,this._onerror=null,this._onpause=null,this._onresume=null,this._onstart=null,this._onstop=null}get mimeType(){return this._internalMediaRecorder.mimeType}get ondataavailable(){return this._ondataavailable===null?this._ondataavailable:this._ondataavailable[0]}set ondataavailable(c){if(this._ondataavailable!==null&&this.removeEventListener("dataavailable",this._ondataavailable[1]),typeof c=="function"){const u=c.bind(this);this.addEventListener("dataavailable",u),this._ondataavailable=[c,u]}else this._ondataavailable=null}get onerror(){return this._onerror===null?this._onerror:this._onerror[0]}set onerror(c){if(this._onerror!==null&&this.removeEventListener("error",this._onerror[1]),typeof c=="function"){const u=c.bind(this);this.addEventListener("error",u),this._onerror=[c,u]}else this._onerror=null}get onpause(){return this._onpause===null?this._onpause:this._onpause[0]}set onpause(c){if(this._onpause!==null&&this.removeEventListener("pause",this._onpause[1]),typeof c=="function"){const u=c.bind(this);this.addEventListener("pause",u),this._onpause=[c,u]}else this._onpause=null}get onresume(){return this._onresume===null?this._onresume:this._onresume[0]}set onresume(c){if(this._onresume!==null&&this.removeEventListener("resume",this._onresume[1]),typeof c=="function"){const u=c.bind(this);this.addEventListener("resume",u),this._onresume=[c,u]}else this._onresume=null}get onstart(){return this._onstart===null?this._onstart:this._onstart[0]}set onstart(c){if(this._onstart!==null&&this.removeEventListener("start",this._onstart[1]),typeof c=="function"){const u=c.bind(this);this.addEventListener("start",u),this._onstart=[c,u]}else this._onstart=null}get onstop(){return this._onstop===null?this._onstop:this._onstop[0]}set onstop(c){if(this._onstop!==null&&this.removeEventListener("stop",this._onstop[1]),typeof c=="function"){const u=c.bind(this);this.addEventListener("stop",u),this._onstop=[c,u]}else this._onstop=null}get state(){return this._internalMediaRecorder.state}pause(){return this._internalMediaRecorder.pause()}resume(){return this._internalMediaRecorder.resume()}start(c){return this._internalMediaRecorder.start(c)}stop(){return this._internalMediaRecorder.stop()}static isTypeSupported(c){return i!==null&&i.isTypeSupported!==void 0&&i.isTypeSupported(c)||o.some(u=>u.test(c))}},pr=e=>e!==null&&e.BlobEvent!==void 0?e.BlobEvent:null,mr=(e,t,n)=>{const r=new Map,o=new WeakMap,s=new WeakMap,i=new e(t,n),a=new WeakMap;let c=!1;return i.addEventListener=(u=>(d,l,w)=>{let g=l;if(typeof l=="function")if(d==="dataavailable"){const h=[];g=f=>{c&&i.state==="inactive"?h.push(f):l.call(i,f)},r.set(l,h),o.set(l,g)}else d==="error"?(g=h=>{h instanceof ErrorEvent?l.call(i,h):l.call(i,new ErrorEvent("error",{error:h.error}))},s.set(l,g)):d==="stop"&&(g=h=>{for(const[f,m]of r.entries())if(m.length>0){const[p]=m;m.length>1&&Object.defineProperty(p,"data",{value:new Blob(m.map(({data:b})=>b),{type:p.data.type})}),m.length=0,f.call(i,p)}c=!1,l.call(i,h)},a.set(l,g));return u.call(i,d,g,w)})(i.addEventListener),i.removeEventListener=(u=>(d,l,w)=>{let g=l;if(typeof l=="function"){if(d==="dataavailable"){r.delete(l);const h=o.get(l);h!==void 0&&(g=h)}else if(d==="error"){const h=s.get(l);h!==void 0&&(g=h)}else if(d==="stop"){const h=a.get(l);h!==void 0&&(g=h)}}return u.call(i,d,g,w)})(i.removeEventListener),i.start=(u=>d=>(c=d!==void 0,d===void 0?u.call(i):u.call(i,d)))(i.start),i},gr=e=>e===null||e.MediaRecorder===void 0?null:e.MediaRecorder,Qt=()=>{try{return new DOMException("","NotSupportedError")}catch(e){return e.code=9,e.name="NotSupportedError",e}},wr=e=>(t,n,r,o=2)=>{const s=e(t,n);if(s===null)return s;const{length:i,value:a}=s;if(r==="master")return{content:null,length:i};if(n+i+a>t.byteLength)return null;if(r==="binary"){const c=(a/Float32Array.BYTES_PER_ELEMENT-1)/o,u=Array.from({length:o},()=>new Float32Array(c));for(let d=0;d<c;d+=1){const l=d*o+1;for(let w=0;w<o;w+=1)u[w][d]=t.getFloat32(n+i+(l+w)*Float32Array.BYTES_PER_ELEMENT,!0)}return{content:u,length:i+a}}return{content:null,length:i+a}},vr=e=>(t,n)=>{const r=e(t,n);if(r===null)return r;const{length:o,value:s}=r;return s===35?{length:o,type:"binary"}:s===46||s===97||s===88713574||s===106212971||s===139690087||s===172351395||s===256095861?{length:o,type:"master"}:{length:o,type:"unknown"}},_r=e=>(t,n)=>{const r=e(t,n);if(r===null)return r;const o=n+Math.floor((r-1)/8);if(o+r>t.byteLength)return null;let i=t.getUint8(o)&(1<<8-r%8)-1;for(let a=1;a<r;a+=1)i=(i<<8)+t.getUint8(o+a);return{length:r,value:i}},Rt=Symbol.observable||"@@observable";function yr(e){return Symbol.observable||(typeof e=="function"&&e.prototype&&e.prototype[Symbol.observable]?(e.prototype[Rt]=e.prototype[Symbol.observable],delete e.prototype[Symbol.observable]):(e[Rt]=e[Symbol.observable],delete e[Symbol.observable])),e}const Ne=()=>{},It=e=>{throw e};function Er(e){return e?e.next&&e.error&&e.complete?e:{complete:(e.complete??Ne).bind(e),error:(e.error??It).bind(e),next:(e.next??Ne).bind(e)}:{complete:Ne,error:It,next:Ne}}const Ar=e=>(t,n,r)=>e(o=>{const s=i=>o.next(i);return t.addEventListener(n,s,r),()=>t.removeEventListener(n,s,r)}),br=(e,t)=>{const n=()=>{},r=o=>typeof o[0]=="function";return o=>{const s=(...i)=>{const a=o(r(i)?t({next:i[0]}):t(...i));return a!==void 0?a:n};return s[Symbol.observable]=()=>({subscribe:(...i)=>({unsubscribe:s(...i)})}),e(s)}},Cr=br(yr,Er),en=Ar(Cr);/*!
 * dashify <https://github.com/jonschlinkert/dashify>
 *
 * Copyright (c) 2015-2017, Jon Schlinkert.
 * Released under the MIT License.
 */var Tr=(e,t)=>{if(typeof e!="string")throw new TypeError("expected a string");return e.trim().replace(/([a-z])([A-Z])/g,"$1-$2").replace(/\W/g,n=>/[À-ž]/.test(n)?n:"-").replace(/^-+|-+$/g,"").replace(/-{2,}/g,n=>t&&t.condense?"-":n).toLowerCase()};const Mr=Zt(Tr);var tn={exports:{}};(function(e){var t=function(n){var r,o,s=/\w+/.exec(n);if(s)o=s[0];else return"an";var i=o.toLowerCase(),a=["honest","hour","hono"];for(r in a)if(i.indexOf(a[r])==0)return"an";if(i.length==1)return"aedhilmnorsx".indexOf(i)>=0?"an":"a";if(o.match(/(?!FJO|[HLMNS]Y.|RY[EO]|SQU|(F[LR]?|[HL]|MN?|N|RH?|S[CHKLMNPTVW]?|X(YL)?)[AEIOU])[FHLMNRSX][A-Z]/))return"an";var c=[/^e[uw]/,/^onc?e\b/,/^uni([^nmd]|mo)/,/^u[bcfhjkqrst][aeiou]/];for(r=0;r<c.length;r++)if(i.match(c[r]))return"a";return o.match(/^U[NK][AIEO]/)?"a":o==o.toUpperCase()?"aedhilmnorsx".indexOf(i[0])>=0?"an":"a":"aeiou".indexOf(i[0])>=0||i.match(/^y(b[lor]|cl[ea]|fere|gg|p[ios]|rou|tt)/)?"an":"a"};e.exports=t})(tn);var Nr=tn.exports;const Or=Zt(Nr),St=(e,t)=>t===void 0?e:t.reduce((n,r)=>{if(r==="capitalize"){const o=n.charAt(0).toUpperCase(),s=n.slice(1);return`${o}${s}`}return r==="dashify"?Mr(n):r==="prependIndefiniteArticle"?`${Or(n)} ${n}`:n},e),Rr=e=>{const t=e.name+e.modifiers.map(n=>`\\.${n}\\(\\)`).join("");return new RegExp(`\\$\\{${t}}`,"g")},kt=(e,t)=>{const n=/\${([^.}]+)((\.[^(]+\(\))*)}/g,r=[];let o=n.exec(e);for(;o!==null;){const i={modifiers:[],name:o[1]};if(o[3]!==void 0){const a=/\.[^(]+\(\)/g;let c=a.exec(o[2]);for(;c!==null;)i.modifiers.push(c[0].slice(1,-2)),c=a.exec(o[2])}r.push(i),o=n.exec(e)}const s=r.reduce((i,a)=>i.map(c=>typeof c=="string"?c.split(Rr(a)).reduce((u,d,l)=>l===0?[d]:a.name in t?[...u,St(t[a.name],a.modifiers),d]:[...u,w=>St(w[a.name],a.modifiers),d],[]):[c]).reduce((c,u)=>[...c,...u],[]),[e]);return i=>s.reduce((a,c)=>typeof c=="string"?[...a,c]:[...a,c(i)],[]).join("")},We=(e,t={})=>{const n=e.code===void 0?void 0:kt(e.code,t),r=e.message===void 0?void 0:kt(e.message,t);function o(s={},i){const a=i===void 0&&(s instanceof Error||s.code!==void 0&&s.code.slice(-9)==="Exception"),{cause:c,missingParameters:u}=a?{cause:s,missingParameters:{}}:{cause:i,missingParameters:s},d=r===void 0?new Error:new Error(r(u));return c!==null&&(d.cause=c),n!==void 0&&(d.code=n(u)),e.status!==void 0&&(d.status=e.status),d}return o},Ve={INTERNAL_ERROR:-32603,INVALID_PARAMS:-32602,METHOD_NOT_FOUND:-32601};We({message:'The requested method called "${method}" is not supported.',status:Ve.METHOD_NOT_FOUND});We({message:'The handler of the method called "${method}" returned no required result.',status:Ve.INTERNAL_ERROR});We({message:'The handler of the method called "${method}" returned an unexpected result.',status:Ve.INTERNAL_ERROR});We({message:'The specified parameter called "portId" with the given value "${portId}" does not identify a port connected to this worker.',status:Ve.INVALID_PARAMS});const Ir=(e,t,n)=>async r=>{const o=new e([n],{type:"application/javascript; charset=utf-8"}),s=t.createObjectURL(o);try{await r(s)}finally{t.revokeObjectURL(s)}},Sr=e=>({data:t})=>{const{id:n}=t;if(n!==null){const r=e.get(n);if(r!==void 0){const{reject:o,resolve:s}=r;e.delete(n),t.error===void 0?s(t.result):o(new Error(t.error.message))}}},kr=e=>(t,n)=>(r,o=[])=>new Promise((s,i)=>{const a=e(t);t.set(a,{reject:i,resolve:s}),n.postMessage({id:a,...r},o)}),Lr=(e,t,n,r)=>(o,s,i={})=>{const a=new o(s,"recorder-audio-worklet-processor",{...i,channelCountMode:"explicit",numberOfInputs:1,numberOfOutputs:0}),c=new Map,u=t(c,a.port),d=n(a.port,"message")(e(c));a.port.start();let l="inactive";return Object.defineProperties(a,{pause:{get(){return async()=>(r(["recording"],l),l="paused",u({method:"pause"}))}},port:{get(){throw new Error("The port of a RecorderAudioWorkletNode can't be accessed.")}},record:{get(){return async w=>(r(["inactive"],l),l="recording",u({method:"record",params:{encoderPort:w}},[w]))}},resume:{get(){return async()=>(r(["paused"],l),l="recording",u({method:"resume"}))}},stop:{get(){return async()=>{r(["paused","recording"],l),l="stopped";try{await u({method:"stop"})}finally{d()}}}}}),a},xr=(e,t)=>{if(!e.includes(t))throw new Error(`Expected the state to be ${e.map(n=>`"${n}"`).join(" or ")} but it was "${t}".`)},Pr='(()=>{"use strict";class e extends AudioWorkletProcessor{constructor(){super(),this._encoderPort=null,this._numberOfChannels=0,this._state="inactive",this.port.onmessage=e=>{let{data:t}=e;"pause"===t.method?"active"===this._state||"recording"===this._state?(this._state="paused",this._sendAcknowledgement(t.id)):this._sendUnexpectedStateError(t.id):"record"===t.method?"inactive"===this._state?(this._encoderPort=t.params.encoderPort,this._state="active",this._sendAcknowledgement(t.id)):this._sendUnexpectedStateError(t.id):"resume"===t.method?"paused"===this._state?(this._state="active",this._sendAcknowledgement(t.id)):this._sendUnexpectedStateError(t.id):"stop"===t.method?"active"!==this._state&&"paused"!==this._state&&"recording"!==this._state||null===this._encoderPort?this._sendUnexpectedStateError(t.id):(this._stop(this._encoderPort),this._sendAcknowledgement(t.id)):"number"==typeof t.id&&this.port.postMessage({error:{code:-32601,message:"The requested method is not supported."},id:t.id})}}process(e){let[t]=e;if("inactive"===this._state||"paused"===this._state)return!0;if("active"===this._state){if(void 0===t)throw new Error("No channelData was received for the first input.");if(0===t.length)return!0;this._state="recording"}if("recording"===this._state&&null!==this._encoderPort){if(void 0===t)throw new Error("No channelData was received for the first input.");return 0===t.length?this._encoderPort.postMessage(Array.from({length:this._numberOfChannels},(()=>128))):(this._encoderPort.postMessage(t,t.map((e=>{let{buffer:t}=e;return t}))),this._numberOfChannels=t.length),!0}return!1}_sendAcknowledgement(e){this.port.postMessage({id:e,result:null})}_sendUnexpectedStateError(e){this.port.postMessage({error:{code:-32603,message:"The internal state does not allow to process the given message."},id:e})}_stop(e){e.postMessage([]),e.close(),this._encoderPort=null,this._state="stopped"}}e.parameterDescriptors=[],registerProcessor("recorder-audio-worklet-processor",e)})();',Ur=Ir(Blob,URL,Pr),Br=Lr(Sr,kr(er),en,xr),Lt=(e,t,n)=>({endTime:t,insertTime:n,type:"exponentialRampToValue",value:e}),xt=(e,t,n)=>({endTime:t,insertTime:n,type:"linearRampToValue",value:e}),et=(e,t)=>({startTime:t,type:"setValue",value:e}),nn=(e,t,n)=>({duration:n,startTime:t,type:"setValueCurve",values:e}),rn=(e,t,{startTime:n,target:r,timeConstant:o})=>r+(t-r)*Math.exp((n-e)/o),me=e=>e.type==="exponentialRampToValue",ke=e=>e.type==="linearRampToValue",re=e=>me(e)||ke(e),dt=e=>e.type==="setValue",Q=e=>e.type==="setValueCurve",Le=(e,t,n,r)=>{const o=e[t];return o===void 0?r:re(o)||dt(o)?o.value:Q(o)?o.values[o.values.length-1]:rn(n,Le(e,t-1,o.startTime,r),o)},Pt=(e,t,n,r,o)=>n===void 0?[r.insertTime,o]:re(n)?[n.endTime,n.value]:dt(n)?[n.startTime,n.value]:Q(n)?[n.startTime+n.duration,n.values[n.values.length-1]]:[n.startTime,Le(e,t-1,n.startTime,o)],tt=e=>e.type==="cancelAndHold",nt=e=>e.type==="cancelScheduledValues",ne=e=>tt(e)||nt(e)?e.cancelTime:me(e)||ke(e)?e.endTime:e.startTime,Ut=(e,t,n,{endTime:r,value:o})=>n===o?o:0<n&&0<o||n<0&&o<0?n*(o/n)**((e-t)/(r-t)):0,Bt=(e,t,n,{endTime:r,value:o})=>n+(e-t)/(r-t)*(o-n),Dr=(e,t)=>{const n=Math.floor(t),r=Math.ceil(t);return n===r?e[n]:(1-(t-n))*e[n]+(1-(r-t))*e[r]},Wr=(e,{duration:t,startTime:n,values:r})=>{const o=(e-n)/t*(r.length-1);return Dr(r,o)},Oe=e=>e.type==="setTarget";class Vr{constructor(t){this._automationEvents=[],this._currenTime=0,this._defaultValue=t}[Symbol.iterator](){return this._automationEvents[Symbol.iterator]()}add(t){const n=ne(t);if(tt(t)||nt(t)){const r=this._automationEvents.findIndex(s=>nt(t)&&Q(s)?s.startTime+s.duration>=n:ne(s)>=n),o=this._automationEvents[r];if(r!==-1&&(this._automationEvents=this._automationEvents.slice(0,r)),tt(t)){const s=this._automationEvents[this._automationEvents.length-1];if(o!==void 0&&re(o)){if(s!==void 0&&Oe(s))throw new Error("The internal list is malformed.");const i=s===void 0?o.insertTime:Q(s)?s.startTime+s.duration:ne(s),a=s===void 0?this._defaultValue:Q(s)?s.values[s.values.length-1]:s.value,c=me(o)?Ut(n,i,a,o):Bt(n,i,a,o),u=me(o)?Lt(c,n,this._currenTime):xt(c,n,this._currenTime);this._automationEvents.push(u)}if(s!==void 0&&Oe(s)&&this._automationEvents.push(et(this.getValue(n),n)),s!==void 0&&Q(s)&&s.startTime+s.duration>n){const i=n-s.startTime,a=(s.values.length-1)/s.duration,c=Math.max(2,1+Math.ceil(i*a)),u=i/(c-1)*a,d=s.values.slice(0,c);if(u<1)for(let l=1;l<c;l+=1){const w=u*l%1;d[l]=s.values[l-1]*(1-w)+s.values[l]*w}this._automationEvents[this._automationEvents.length-1]=nn(d,s.startTime,i)}}}else{const r=this._automationEvents.findIndex(i=>ne(i)>n),o=r===-1?this._automationEvents[this._automationEvents.length-1]:this._automationEvents[r-1];if(o!==void 0&&Q(o)&&ne(o)+o.duration>n)return!1;const s=me(t)?Lt(t.value,t.endTime,this._currenTime):ke(t)?xt(t.value,n,this._currenTime):t;if(r===-1)this._automationEvents.push(s);else{if(Q(t)&&n+t.duration>ne(this._automationEvents[r]))return!1;this._automationEvents.splice(r,0,s)}}return!0}flush(t){const n=this._automationEvents.findIndex(r=>ne(r)>t);if(n>1){const r=this._automationEvents.slice(n-1),o=r[0];Oe(o)&&r.unshift(et(Le(this._automationEvents,n-2,o.startTime,this._defaultValue),o.startTime)),this._automationEvents=r}}getValue(t){if(this._automationEvents.length===0)return this._defaultValue;const n=this._automationEvents.findIndex(i=>ne(i)>t),r=this._automationEvents[n],o=(n===-1?this._automationEvents.length:n)-1,s=this._automationEvents[o];if(s!==void 0&&Oe(s)&&(r===void 0||!re(r)||r.insertTime>t))return rn(t,Le(this._automationEvents,o-1,s.startTime,this._defaultValue),s);if(s!==void 0&&dt(s)&&(r===void 0||!re(r)))return s.value;if(s!==void 0&&Q(s)&&(r===void 0||!re(r)||s.startTime+s.duration>t))return t<s.startTime+s.duration?Wr(t,s):s.values[s.values.length-1];if(s!==void 0&&re(s)&&(r===void 0||!re(r)))return s.value;if(r!==void 0&&me(r)){const[i,a]=Pt(this._automationEvents,o,s,r,this._defaultValue);return Ut(t,i,a,r)}if(r!==void 0&&ke(r)){const[i,a]=Pt(this._automationEvents,o,s,r,this._defaultValue);return Bt(t,i,a,r)}return this._defaultValue}}const Fr=e=>({cancelTime:e,type:"cancelAndHold"}),jr=e=>({cancelTime:e,type:"cancelScheduledValues"}),$r=(e,t)=>({endTime:t,type:"exponentialRampToValue",value:e}),Gr=(e,t)=>({endTime:t,type:"linearRampToValue",value:e}),qr=(e,t,n)=>({startTime:t,target:e,timeConstant:n,type:"setTarget"}),zr=()=>new DOMException("","AbortError"),Hr=e=>(t,n,[r,o,s],i)=>{e(t[o],[n,r,s],a=>a[0]===n&&a[1]===r,i)},Yr=e=>(t,n,r)=>{const o=[];for(let s=0;s<r.numberOfInputs;s+=1)o.push(new Set);e.set(t,{activeInputs:o,outputs:new Set,passiveInputs:new WeakMap,renderer:n})},Xr=e=>(t,n)=>{e.set(t,{activeInputs:new Set,passiveInputs:new WeakMap,renderer:n})},ge=new WeakSet,on=new WeakMap,sn=new WeakMap,an=new WeakMap,cn=new WeakMap,un=new WeakMap,ln=new WeakMap,rt=new WeakMap,ot=new WeakMap,st=new WeakMap,dn={construct(){return dn}},Zr=e=>{try{const t=new Proxy(e,dn);new t}catch{return!1}return!0},Dt=/^import(?:(?:[\s]+[\w]+|(?:[\s]+[\w]+[\s]*,)?[\s]*\{[\s]*[\w]+(?:[\s]+as[\s]+[\w]+)?(?:[\s]*,[\s]*[\w]+(?:[\s]+as[\s]+[\w]+)?)*[\s]*}|(?:[\s]+[\w]+[\s]*,)?[\s]*\*[\s]+as[\s]+[\w]+)[\s]+from)?(?:[\s]*)("([^"\\]|\\.)+"|'([^'\\]|\\.)+')(?:[\s]*);?/,Wt=(e,t)=>{const n=[];let r=e.replace(/^[\s]+/,""),o=r.match(Dt);for(;o!==null;){const s=o[1].slice(1,-1),i=o[0].replace(/([\s]+)?;?$/,"").replace(s,new URL(s,t).toString());n.push(i),r=r.slice(o[0].length).replace(/^[\s]+/,""),o=r.match(Dt)}return[n.join(";"),r]},Vt=e=>{if(e!==void 0&&!Array.isArray(e))throw new TypeError("The parameterDescriptors property of given value for processorCtor is not an array.")},Ft=e=>{if(!Zr(e))throw new TypeError("The given value for processorCtor should be a constructor.");if(e.prototype===null||typeof e.prototype!="object")throw new TypeError("The given value for processorCtor should have a prototype.")},Kr=(e,t,n,r,o,s,i,a,c,u,d,l,w)=>{let g=0;return(h,f,m={credentials:"omit"})=>{const p=d.get(h);if(p!==void 0&&p.has(f))return Promise.resolve();const b=u.get(h);if(b!==void 0){const E=b.get(f);if(E!==void 0)return E}const _=s(h),T=_.audioWorklet===void 0?o(f).then(([E,A])=>{const[y,v]=Wt(E,A),N=`${y};((a,b)=>{(a[b]=a[b]||[]).push((AudioWorkletProcessor,global,registerProcessor,sampleRate,self,window)=>{${v}
})})(window,'_AWGS')`;return n(N)}).then(()=>{const E=w._AWGS.pop();if(E===void 0)throw new SyntaxError;r(_.currentTime,_.sampleRate,()=>E(class{},void 0,(A,y)=>{if(A.trim()==="")throw t();const v=ot.get(_);if(v!==void 0){if(v.has(A))throw t();Ft(y),Vt(y.parameterDescriptors),v.set(A,y)}else Ft(y),Vt(y.parameterDescriptors),ot.set(_,new Map([[A,y]]))},_.sampleRate,void 0,void 0))}):Promise.all([o(f),Promise.resolve(e(l,l))]).then(([[E,A],y])=>{const v=g+1;g=v;const[N,I]=Wt(E,A),D=`${N};((AudioWorkletProcessor,registerProcessor)=>{${I}
})(${y?"AudioWorkletProcessor":"class extends AudioWorkletProcessor {__b=new WeakSet();constructor(){super();(p=>p.postMessage=(q=>(m,t)=>q.call(p,m,t?t.filter(u=>!this.__b.has(u)):t))(p.postMessage))(this.port)}}"},(n,p)=>registerProcessor(n,class extends p{${y?"":"__c = (a) => a.forEach(e=>this.__b.add(e.buffer));"}process(i,o,p){${y?"":"i.forEach(this.__c);o.forEach(this.__c);this.__c(Object.values(p));"}return super.process(i.map(j=>j.some(k=>k.length===0)?[]:j),o,p)}}));registerProcessor('__sac${v}',class extends AudioWorkletProcessor{process(){return !1}})`,P=new Blob([D],{type:"application/javascript; charset=utf-8"}),S=URL.createObjectURL(P);return _.audioWorklet.addModule(S,m).then(()=>{if(a(_))return _;const L=i(_);return L.audioWorklet.addModule(S,m).then(()=>L)}).then(L=>{if(c===null)throw new SyntaxError;try{new c(L,`__sac${v}`)}catch{throw new SyntaxError}}).finally(()=>URL.revokeObjectURL(S))});return b===void 0?u.set(h,new Map([[f,T]])):b.set(f,T),T.then(()=>{const E=d.get(h);E===void 0?d.set(h,new Set([f])):E.add(f)}).finally(()=>{const E=u.get(h);E!==void 0&&E.delete(f)}),T}},K=(e,t)=>{const n=e.get(t);if(n===void 0)throw new Error("A value with the given key could not be found.");return n},Fe=(e,t)=>{const n=Array.from(e).filter(t);if(n.length>1)throw Error("More than one element was found.");if(n.length===0)throw Error("No element was found.");const[r]=n;return e.delete(r),r},fn=(e,t,n,r)=>{const o=K(e,t),s=Fe(o,i=>i[0]===n&&i[1]===r);return o.size===0&&e.delete(t),s},Ae=e=>K(ln,e),xe=e=>{if(ge.has(e))throw new Error("The AudioNode is already stored.");ge.add(e),Ae(e).forEach(t=>t(!0))},hn=e=>"port"in e,ft=e=>{if(!ge.has(e))throw new Error("The AudioNode is not stored.");ge.delete(e),Ae(e).forEach(t=>t(!1))},it=(e,t)=>{!hn(e)&&t.every(n=>n.size===0)&&ft(e)},Jr=(e,t,n,r,o,s,i,a,c,u,d,l,w)=>{const g=new WeakMap;return(h,f,m,p,b)=>{const{activeInputs:_,passiveInputs:T}=s(f),{outputs:E}=s(h),A=a(h),y=v=>{const N=c(f),I=c(h);if(v){const M=fn(T,h,m,p);e(_,h,M,!1),!b&&!l(h)&&n(I,N,m,p),w(f)&&xe(f)}else{const M=r(_,h,m,p);t(T,p,M,!1),!b&&!l(h)&&o(I,N,m,p);const U=i(f);if(U===0)d(f)&&it(f,_);else{const k=g.get(f);k!==void 0&&clearTimeout(k),g.set(f,setTimeout(()=>{d(f)&&it(f,_)},U*1e3))}}};return u(E,[f,m,p],v=>v[0]===f&&v[1]===m&&v[2]===p,!0)?(A.add(y),d(h)?e(_,h,[m,p,y],!0):t(T,p,[h,m,y],!0),!0):!1}},Qr=e=>(t,n,[r,o,s],i)=>{const a=t.get(r);a===void 0?t.set(r,new Set([[o,n,s]])):e(a,[o,n,s],c=>c[0]===o&&c[1]===n,i)},eo=e=>(t,n)=>{const r=e(t,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});n.connect(r).connect(t.destination);const o=()=>{n.removeEventListener("ended",o),n.disconnect(r),r.disconnect()};n.addEventListener("ended",o)},to=e=>(t,n)=>{e(t).add(n)},pn=(e,t)=>e.context===t,jt=e=>{try{e.copyToChannel(new Float32Array(1),0,-1)}catch{return!1}return!0},ue=()=>new DOMException("","IndexSizeError"),no=e=>{e.getChannelData=(t=>n=>{try{return t.call(e,n)}catch(r){throw r.code===12?ue():r}})(e.getChannelData)},ro={numberOfChannels:1},oo=(e,t,n,r,o,s,i,a)=>{let c=null;return class mn{constructor(d){if(o===null)throw new Error("Missing the native OfflineAudioContext constructor.");const{length:l,numberOfChannels:w,sampleRate:g}={...ro,...d};c===null&&(c=new o(1,1,44100));const h=r!==null&&t(s,s)?new r({length:l,numberOfChannels:w,sampleRate:g}):c.createBuffer(w,l,g);if(h.numberOfChannels===0)throw n();return typeof h.copyFromChannel!="function"?(i(h),no(h)):t(jt,()=>jt(h))||a(h),e.add(h),h}static[Symbol.hasInstance](d){return d!==null&&typeof d=="object"&&Object.getPrototypeOf(d)===mn.prototype||e.has(d)}}},je=-34028234663852886e22,ht=-je,ie=e=>ge.has(e),so={buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1},io=(e,t,n,r,o,s,i,a)=>class extends e{constructor(u,d){const l=s(u),w={...so,...d},g=o(l,w),h=i(l),f=h?t():null;super(u,!1,g,f),this._audioBufferSourceNodeRenderer=f,this._isBufferNullified=!1,this._isBufferSet=w.buffer!==null,this._nativeAudioBufferSourceNode=g,this._onended=null,this._playbackRate=n(this,h,g.playbackRate,ht,je)}get buffer(){return this._isBufferNullified?null:this._nativeAudioBufferSourceNode.buffer}set buffer(u){if(this._nativeAudioBufferSourceNode.buffer=u,u!==null){if(this._isBufferSet)throw r();this._isBufferSet=!0}}get loop(){return this._nativeAudioBufferSourceNode.loop}set loop(u){this._nativeAudioBufferSourceNode.loop=u}get loopEnd(){return this._nativeAudioBufferSourceNode.loopEnd}set loopEnd(u){this._nativeAudioBufferSourceNode.loopEnd=u}get loopStart(){return this._nativeAudioBufferSourceNode.loopStart}set loopStart(u){this._nativeAudioBufferSourceNode.loopStart=u}get onended(){return this._onended}set onended(u){const d=typeof u=="function"?a(this,u):null;this._nativeAudioBufferSourceNode.onended=d;const l=this._nativeAudioBufferSourceNode.onended;this._onended=l!==null&&l===d?u:l}get playbackRate(){return this._playbackRate}start(u=0,d=0,l){if(this._nativeAudioBufferSourceNode.start(u,d,l),this._audioBufferSourceNodeRenderer!==null&&(this._audioBufferSourceNodeRenderer.start=l===void 0?[u,d]:[u,d,l]),this.context.state!=="closed"){xe(this);const w=()=>{this._nativeAudioBufferSourceNode.removeEventListener("ended",w),ie(this)&&ft(this)};this._nativeAudioBufferSourceNode.addEventListener("ended",w)}}stop(u=0){this._nativeAudioBufferSourceNode.stop(u),this._audioBufferSourceNodeRenderer!==null&&(this._audioBufferSourceNodeRenderer.stop=u)}},ao=(e,t,n,r,o)=>()=>{const s=new WeakMap;let i=null,a=null;const c=async(u,d)=>{let l=n(u);const w=pn(l,d);if(!w){const g={buffer:l.buffer,channelCount:l.channelCount,channelCountMode:l.channelCountMode,channelInterpretation:l.channelInterpretation,loop:l.loop,loopEnd:l.loopEnd,loopStart:l.loopStart,playbackRate:l.playbackRate.value};l=t(d,g),i!==null&&l.start(...i),a!==null&&l.stop(a)}return s.set(d,l),w?await e(d,u.playbackRate,l.playbackRate):await r(d,u.playbackRate,l.playbackRate),await o(u,d,l),l};return{set start(u){i=u},set stop(u){a=u},render(u,d){const l=s.get(d);return l!==void 0?Promise.resolve(l):c(u,d)}}},co=e=>"playbackRate"in e,uo=e=>"frequency"in e&&"gain"in e,lo=e=>"offset"in e,fo=e=>!("frequency"in e)&&"gain"in e,ho=e=>"detune"in e&&"frequency"in e,po=e=>"pan"in e,z=e=>K(on,e),be=e=>K(an,e),at=(e,t)=>{const{activeInputs:n}=z(e);n.forEach(o=>o.forEach(([s])=>{t.includes(e)||at(s,[...t,e])}));const r=co(e)?[e.playbackRate]:hn(e)?Array.from(e.parameters.values()):uo(e)?[e.Q,e.detune,e.frequency,e.gain]:lo(e)?[e.offset]:fo(e)?[e.gain]:ho(e)?[e.detune,e.frequency]:po(e)?[e.pan]:[];for(const o of r){const s=be(o);s!==void 0&&s.activeInputs.forEach(([i])=>at(i,t))}ie(e)&&ft(e)},mo=e=>{at(e.destination,[])},go=e=>e===void 0||typeof e=="number"||typeof e=="string"&&(e==="balanced"||e==="interactive"||e==="playback"),wo=(e,t,n,r,o,s,i,a)=>class extends e{constructor(u,d){const l=s(u),w=i(l),g=o(l,d,w),h=w?t(a):null;super(u,!1,g,h),this._isNodeOfNativeOfflineAudioContext=w,this._nativeAudioDestinationNode=g}get channelCount(){return this._nativeAudioDestinationNode.channelCount}set channelCount(u){if(this._isNodeOfNativeOfflineAudioContext)throw r();if(u>this._nativeAudioDestinationNode.maxChannelCount)throw n();this._nativeAudioDestinationNode.channelCount=u}get channelCountMode(){return this._nativeAudioDestinationNode.channelCountMode}set channelCountMode(u){if(this._isNodeOfNativeOfflineAudioContext)throw r();this._nativeAudioDestinationNode.channelCountMode=u}get maxChannelCount(){return this._nativeAudioDestinationNode.maxChannelCount}},vo=e=>{const t=new WeakMap,n=async(r,o)=>{const s=o.destination;return t.set(o,s),await e(r,o,s),s};return{render(r,o){const s=t.get(o);return s!==void 0?Promise.resolve(s):n(r,o)}}},_o=(e,t,n,r,o,s,i,a)=>(c,u)=>{const d=u.listener,l=()=>{const E=new Float32Array(1),A=t(u,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:9}),y=i(u);let v=!1,N=[0,0,-1,0,1,0],I=[0,0,0];const M=()=>{if(v)return;v=!0;const P=r(u,256,9,0);P.onaudioprocess=({inputBuffer:S})=>{const L=[s(S,E,0),s(S,E,1),s(S,E,2),s(S,E,3),s(S,E,4),s(S,E,5)];L.some((O,x)=>O!==N[x])&&(d.setOrientation(...L),N=L);const B=[s(S,E,6),s(S,E,7),s(S,E,8)];B.some((O,x)=>O!==I[x])&&(d.setPosition(...B),I=B)},A.connect(P)},U=P=>S=>{S!==N[P]&&(N[P]=S,d.setOrientation(...N))},k=P=>S=>{S!==I[P]&&(I[P]=S,d.setPosition(...I))},D=(P,S,L)=>{const B=n(u,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",offset:S});B.connect(A,0,P),B.start(),Object.defineProperty(B.offset,"defaultValue",{get(){return S}});const O=e({context:c},y,B.offset,ht,je);return a(O,"value",x=>()=>x.call(O),x=>V=>{try{x.call(O,V)}catch(G){if(G.code!==9)throw G}M(),y&&L(V)}),O.cancelAndHoldAtTime=(x=>y?()=>{throw o()}:(...V)=>{const G=x.apply(O,V);return M(),G})(O.cancelAndHoldAtTime),O.cancelScheduledValues=(x=>y?()=>{throw o()}:(...V)=>{const G=x.apply(O,V);return M(),G})(O.cancelScheduledValues),O.exponentialRampToValueAtTime=(x=>y?()=>{throw o()}:(...V)=>{const G=x.apply(O,V);return M(),G})(O.exponentialRampToValueAtTime),O.linearRampToValueAtTime=(x=>y?()=>{throw o()}:(...V)=>{const G=x.apply(O,V);return M(),G})(O.linearRampToValueAtTime),O.setTargetAtTime=(x=>y?()=>{throw o()}:(...V)=>{const G=x.apply(O,V);return M(),G})(O.setTargetAtTime),O.setValueAtTime=(x=>y?()=>{throw o()}:(...V)=>{const G=x.apply(O,V);return M(),G})(O.setValueAtTime),O.setValueCurveAtTime=(x=>y?()=>{throw o()}:(...V)=>{const G=x.apply(O,V);return M(),G})(O.setValueCurveAtTime),O};return{forwardX:D(0,0,U(0)),forwardY:D(1,0,U(1)),forwardZ:D(2,-1,U(2)),positionX:D(6,0,k(0)),positionY:D(7,0,k(1)),positionZ:D(8,0,k(2)),upX:D(3,0,U(3)),upY:D(4,1,U(4)),upZ:D(5,0,U(5))}},{forwardX:w,forwardY:g,forwardZ:h,positionX:f,positionY:m,positionZ:p,upX:b,upY:_,upZ:T}=d.forwardX===void 0?l():d;return{get forwardX(){return w},get forwardY(){return g},get forwardZ(){return h},get positionX(){return f},get positionY(){return m},get positionZ(){return p},get upX(){return b},get upY(){return _},get upZ(){return T}}},Pe=e=>"context"in e,Ce=e=>Pe(e[0]),le=(e,t,n,r)=>{for(const o of e)if(n(o)){if(r)return!1;throw Error("The set contains at least one similar element.")}return e.add(t),!0},$t=(e,t,[n,r],o)=>{le(e,[t,n,r],s=>s[0]===t&&s[1]===n,o)},Gt=(e,[t,n,r],o)=>{const s=e.get(t);s===void 0?e.set(t,new Set([[n,r]])):le(s,[n,r],i=>i[0]===n,o)},gn=e=>"inputs"in e,ct=(e,t,n,r)=>{if(gn(t)){const o=t.inputs[r];return e.connect(o,n,0),[o,n,0]}return e.connect(t,n,r),[t,n,r]},wn=(e,t,n)=>{for(const r of e)if(r[0]===t&&r[1]===n)return e.delete(r),r;return null},yo=(e,t,n)=>Fe(e,r=>r[0]===t&&r[1]===n),vn=(e,t)=>{if(!Ae(e).delete(t))throw new Error("Missing the expected event listener.")},_n=(e,t,n)=>{const r=K(e,t),o=Fe(r,s=>s[0]===n);return r.size===0&&e.delete(t),o},ut=(e,t,n,r)=>{gn(t)?e.disconnect(t.inputs[r],n,0):e.disconnect(t,n,r)},X=e=>K(sn,e),ye=e=>K(cn,e),ae=e=>rt.has(e),Ie=e=>!ge.has(e),qt=(e,t)=>new Promise(n=>{if(t!==null)n(!0);else{const r=e.createScriptProcessor(256,1,1),o=e.createGain(),s=e.createBuffer(1,2,44100),i=s.getChannelData(0);i[0]=1,i[1]=1;const a=e.createBufferSource();a.buffer=s,a.loop=!0,a.connect(r).connect(e.destination),a.connect(o),a.disconnect(o),r.onaudioprocess=c=>{const u=c.inputBuffer.getChannelData(0);Array.prototype.some.call(u,d=>d===1)?n(!0):n(!1),a.stop(),r.onaudioprocess=null,a.disconnect(r),r.disconnect(e.destination)},a.start()}}),Je=(e,t)=>{const n=new Map;for(const r of e)for(const o of r){const s=n.get(o);n.set(o,s===void 0?1:s+1)}n.forEach((r,o)=>t(o,r))},Ue=e=>"context"in e,Eo=e=>{const t=new Map;e.connect=(n=>(r,o=0,s=0)=>{const i=Ue(r)?n(r,o,s):n(r,o),a=t.get(r);return a===void 0?t.set(r,[{input:s,output:o}]):a.every(c=>c.input!==s||c.output!==o)&&a.push({input:s,output:o}),i})(e.connect.bind(e)),e.disconnect=(n=>(r,o,s)=>{if(n.apply(e),r===void 0)t.clear();else if(typeof r=="number")for(const[i,a]of t){const c=a.filter(u=>u.output!==r);c.length===0?t.delete(i):t.set(i,c)}else if(t.has(r))if(o===void 0)t.delete(r);else{const i=t.get(r);if(i!==void 0){const a=i.filter(c=>c.output!==o&&(c.input!==s||s===void 0));a.length===0?t.delete(r):t.set(r,a)}}for(const[i,a]of t)a.forEach(c=>{Ue(i)?e.connect(i,c.output,c.input):e.connect(i,c.output)})})(e.disconnect)},Ao=(e,t,n,r)=>{const{activeInputs:o,passiveInputs:s}=be(t),{outputs:i}=z(e),a=Ae(e),c=u=>{const d=X(e),l=ye(t);if(u){const w=_n(s,e,n);$t(o,e,w,!1),!r&&!ae(e)&&d.connect(l,n)}else{const w=yo(o,e,n);Gt(s,w,!1),!r&&!ae(e)&&d.disconnect(l,n)}};return le(i,[t,n],u=>u[0]===t&&u[1]===n,!0)?(a.add(c),ie(e)?$t(o,e,[n,c],!0):Gt(s,[e,n,c],!0),!0):!1},bo=(e,t,n,r)=>{const{activeInputs:o,passiveInputs:s}=z(t),i=wn(o[r],e,n);return i===null?[fn(s,e,n,r)[2],!1]:[i[2],!0]},Co=(e,t,n)=>{const{activeInputs:r,passiveInputs:o}=be(t),s=wn(r,e,n);return s===null?[_n(o,e,n)[1],!1]:[s[2],!0]},pt=(e,t,n,r,o)=>{const[s,i]=bo(e,n,r,o);if(s!==null&&(vn(e,s),i&&!t&&!ae(e)&&ut(X(e),X(n),r,o)),ie(n)){const{activeInputs:a}=z(n);it(n,a)}},mt=(e,t,n,r)=>{const[o,s]=Co(e,n,r);o!==null&&(vn(e,o),s&&!t&&!ae(e)&&X(e).disconnect(ye(n),r))},To=(e,t)=>{const n=z(e),r=[];for(const o of n.outputs)Ce(o)?pt(e,t,...o):mt(e,t,...o),r.push(o[0]);return n.outputs.clear(),r},Mo=(e,t,n)=>{const r=z(e),o=[];for(const s of r.outputs)s[1]===n&&(Ce(s)?pt(e,t,...s):mt(e,t,...s),o.push(s[0]),r.outputs.delete(s));return o},No=(e,t,n,r,o)=>{const s=z(e);return Array.from(s.outputs).filter(i=>i[0]===n&&(r===void 0||i[1]===r)&&(o===void 0||i[2]===o)).map(i=>(Ce(i)?pt(e,t,...i):mt(e,t,...i),s.outputs.delete(i),i[0]))},Oo=(e,t,n,r,o,s,i,a,c,u,d,l,w,g,h,f)=>class extends u{constructor(p,b,_,T){super(_),this._context=p,this._nativeAudioNode=_;const E=d(p);l(E)&&n(qt,()=>qt(E,f))!==!0&&Eo(_),sn.set(this,_),ln.set(this,new Set),p.state!=="closed"&&b&&xe(this),e(this,T,_)}get channelCount(){return this._nativeAudioNode.channelCount}set channelCount(p){this._nativeAudioNode.channelCount=p}get channelCountMode(){return this._nativeAudioNode.channelCountMode}set channelCountMode(p){this._nativeAudioNode.channelCountMode=p}get channelInterpretation(){return this._nativeAudioNode.channelInterpretation}set channelInterpretation(p){this._nativeAudioNode.channelInterpretation=p}get context(){return this._context}get numberOfInputs(){return this._nativeAudioNode.numberOfInputs}get numberOfOutputs(){return this._nativeAudioNode.numberOfOutputs}connect(p,b=0,_=0){if(b<0||b>=this._nativeAudioNode.numberOfOutputs)throw o();const T=d(this._context),E=h(T);if(w(p)||g(p))throw s();if(Pe(p)){const v=X(p);try{const I=ct(this._nativeAudioNode,v,b,_),M=Ie(this);(E||M)&&this._nativeAudioNode.disconnect(...I),this.context.state!=="closed"&&!M&&Ie(p)&&xe(p)}catch(I){throw I.code===12?s():I}if(t(this,p,b,_,E)){const I=c([this],p);Je(I,r(E))}return p}const A=ye(p);if(A.name==="playbackRate"&&A.maxValue===1024)throw i();try{this._nativeAudioNode.connect(A,b),(E||Ie(this))&&this._nativeAudioNode.disconnect(A,b)}catch(v){throw v.code===12?s():v}if(Ao(this,p,b,E)){const v=c([this],p);Je(v,r(E))}}disconnect(p,b,_){let T;const E=d(this._context),A=h(E);if(p===void 0)T=To(this,A);else if(typeof p=="number"){if(p<0||p>=this.numberOfOutputs)throw o();T=Mo(this,A,p)}else{if(b!==void 0&&(b<0||b>=this.numberOfOutputs)||Pe(p)&&_!==void 0&&(_<0||_>=p.numberOfInputs))throw o();if(T=No(this,A,p,b,_),T.length===0)throw s()}for(const y of T){const v=c([this],y);Je(v,a)}}},Ro=(e,t,n,r,o,s,i,a,c,u,d,l,w)=>(g,h,f,m=null,p=null)=>{const b=f.value,_=new Vr(b),T=h?r(_):null,E={get defaultValue(){return b},get maxValue(){return m===null?f.maxValue:m},get minValue(){return p===null?f.minValue:p},get value(){return f.value},set value(A){f.value=A,E.setValueAtTime(A,g.context.currentTime)},cancelAndHoldAtTime(A){if(typeof f.cancelAndHoldAtTime=="function")T===null&&_.flush(g.context.currentTime),_.add(o(A)),f.cancelAndHoldAtTime(A);else{const y=Array.from(_).pop();T===null&&_.flush(g.context.currentTime),_.add(o(A));const v=Array.from(_).pop();f.cancelScheduledValues(A),y!==v&&v!==void 0&&(v.type==="exponentialRampToValue"?f.exponentialRampToValueAtTime(v.value,v.endTime):v.type==="linearRampToValue"?f.linearRampToValueAtTime(v.value,v.endTime):v.type==="setValue"?f.setValueAtTime(v.value,v.startTime):v.type==="setValueCurve"&&f.setValueCurveAtTime(v.values,v.startTime,v.duration))}return E},cancelScheduledValues(A){return T===null&&_.flush(g.context.currentTime),_.add(s(A)),f.cancelScheduledValues(A),E},exponentialRampToValueAtTime(A,y){if(A===0)throw new RangeError;if(!Number.isFinite(y)||y<0)throw new RangeError;const v=g.context.currentTime;return T===null&&_.flush(v),Array.from(_).length===0&&(_.add(u(b,v)),f.setValueAtTime(b,v)),_.add(i(A,y)),f.exponentialRampToValueAtTime(A,y),E},linearRampToValueAtTime(A,y){const v=g.context.currentTime;return T===null&&_.flush(v),Array.from(_).length===0&&(_.add(u(b,v)),f.setValueAtTime(b,v)),_.add(a(A,y)),f.linearRampToValueAtTime(A,y),E},setTargetAtTime(A,y,v){return T===null&&_.flush(g.context.currentTime),_.add(c(A,y,v)),f.setTargetAtTime(A,y,v),E},setValueAtTime(A,y){return T===null&&_.flush(g.context.currentTime),_.add(u(A,y)),f.setValueAtTime(A,y),E},setValueCurveAtTime(A,y,v){const N=A instanceof Float32Array?A:new Float32Array(A);if(l!==null&&l.name==="webkitAudioContext"){const I=y+v,M=g.context.sampleRate,U=Math.ceil(y*M),k=Math.floor(I*M),D=k-U,P=new Float32Array(D);for(let L=0;L<D;L+=1){const B=(N.length-1)/v*((U+L)/M-y),O=Math.floor(B),x=Math.ceil(B);P[L]=O===x?N[O]:(1-(B-O))*N[O]+(1-(x-B))*N[x]}T===null&&_.flush(g.context.currentTime),_.add(d(P,y,v)),f.setValueCurveAtTime(P,y,v);const S=k/M;S<I&&w(E,P[P.length-1],S),w(E,N[N.length-1],I)}else T===null&&_.flush(g.context.currentTime),_.add(d(N,y,v)),f.setValueCurveAtTime(N,y,v);return E}};return n.set(E,f),t.set(E,g),e(E,T),E},Io=e=>({replay(t){for(const n of e)if(n.type==="exponentialRampToValue"){const{endTime:r,value:o}=n;t.exponentialRampToValueAtTime(o,r)}else if(n.type==="linearRampToValue"){const{endTime:r,value:o}=n;t.linearRampToValueAtTime(o,r)}else if(n.type==="setTarget"){const{startTime:r,target:o,timeConstant:s}=n;t.setTargetAtTime(o,r,s)}else if(n.type==="setValue"){const{startTime:r,value:o}=n;t.setValueAtTime(o,r)}else if(n.type==="setValueCurve"){const{duration:r,startTime:o,values:s}=n;t.setValueCurveAtTime(s,o,r)}else throw new Error("Can't apply an unknown automation.")}});class yn{constructor(t){this._map=new Map(t)}get size(){return this._map.size}entries(){return this._map.entries()}forEach(t,n=null){return this._map.forEach((r,o)=>t.call(n,r,o,this))}get(t){return this._map.get(t)}has(t){return this._map.has(t)}keys(){return this._map.keys()}values(){return this._map.values()}}const So={channelCount:2,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:1,numberOfOutputs:1,parameterData:{},processorOptions:{}},ko=(e,t,n,r,o,s,i,a,c,u,d,l,w,g)=>class extends t{constructor(f,m,p){var b;const _=a(f),T=c(_),E=d({...So,...p});w(E);const A=ot.get(_),y=A?.get(m),v=T||_.state!=="closed"?_:(b=i(_))!==null&&b!==void 0?b:_,N=o(v,T?null:f.baseLatency,u,m,y,E),I=T?r(m,E,y):null;super(f,!0,N,I);const M=[];N.parameters.forEach((k,D)=>{const P=n(this,T,k);M.push([D,P])}),this._nativeAudioWorkletNode=N,this._onprocessorerror=null,this._parameters=new yn(M),T&&e(_,this);const{activeInputs:U}=s(this);l(N,U)}get onprocessorerror(){return this._onprocessorerror}set onprocessorerror(f){const m=typeof f=="function"?g(this,f):null;this._nativeAudioWorkletNode.onprocessorerror=m;const p=this._nativeAudioWorkletNode.onprocessorerror;this._onprocessorerror=p!==null&&p===m?f:p}get parameters(){return this._parameters===null?this._nativeAudioWorkletNode.parameters:this._parameters}get port(){return this._nativeAudioWorkletNode.port}};function Be(e,t,n,r,o){if(typeof e.copyFromChannel=="function")t[n].byteLength===0&&(t[n]=new Float32Array(128)),e.copyFromChannel(t[n],r,o);else{const s=e.getChannelData(r);if(t[n].byteLength===0)t[n]=s.slice(o,o+128);else{const i=new Float32Array(s.buffer,o*Float32Array.BYTES_PER_ELEMENT,128);t[n].set(i)}}}const En=(e,t,n,r,o)=>{typeof e.copyToChannel=="function"?t[n].byteLength!==0&&e.copyToChannel(t[n],r,o):t[n].byteLength!==0&&e.getChannelData(r).set(t[n],o)},De=(e,t)=>{const n=[];for(let r=0;r<e;r+=1){const o=[],s=typeof t=="number"?t:t[r];for(let i=0;i<s;i+=1)o.push(new Float32Array(128));n.push(o)}return n},Lo=(e,t)=>{const n=K(st,e),r=X(t);return K(n,r)},xo=async(e,t,n,r,o,s,i)=>{const a=t===null?Math.ceil(e.context.length/128)*128:t.length,c=r.channelCount*r.numberOfInputs,u=o.reduce((m,p)=>m+p,0),d=u===0?null:n.createBuffer(u,a,n.sampleRate);if(s===void 0)throw new Error("Missing the processor constructor.");const l=z(e),w=await Lo(n,e),g=De(r.numberOfInputs,r.channelCount),h=De(r.numberOfOutputs,o),f=Array.from(e.parameters.keys()).reduce((m,p)=>({...m,[p]:new Float32Array(128)}),{});for(let m=0;m<a;m+=128){if(r.numberOfInputs>0&&t!==null)for(let p=0;p<r.numberOfInputs;p+=1)for(let b=0;b<r.channelCount;b+=1)Be(t,g[p],b,b,m);s.parameterDescriptors!==void 0&&t!==null&&s.parameterDescriptors.forEach(({name:p},b)=>{Be(t,f,p,c+b,m)});for(let p=0;p<r.numberOfInputs;p+=1)for(let b=0;b<o[p];b+=1)h[p][b].byteLength===0&&(h[p][b]=new Float32Array(128));try{const p=g.map((_,T)=>l.activeInputs[T].size===0?[]:_),b=i(m/n.sampleRate,n.sampleRate,()=>w.process(p,h,f));if(d!==null)for(let _=0,T=0;_<r.numberOfOutputs;_+=1){for(let E=0;E<o[_];E+=1)En(d,h[_],E,T+E,m);T+=o[_]}if(!b)break}catch(p){e.dispatchEvent(new ErrorEvent("processorerror",{colno:p.colno,filename:p.filename,lineno:p.lineno,message:p.message}));break}}return d},Po=(e,t,n,r,o,s,i,a,c,u,d,l,w,g,h,f)=>(m,p,b)=>{const _=new WeakMap;let T=null;const E=async(A,y)=>{let v=d(A),N=null;const I=pn(v,y),M=Array.isArray(p.outputChannelCount)?p.outputChannelCount:Array.from(p.outputChannelCount);if(l===null){const U=M.reduce((S,L)=>S+L,0),k=o(y,{channelCount:Math.max(1,U),channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:Math.max(1,U)}),D=[];for(let S=0;S<A.numberOfOutputs;S+=1)D.push(r(y,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:M[S]}));const P=i(y,{channelCount:p.channelCount,channelCountMode:p.channelCountMode,channelInterpretation:p.channelInterpretation,gain:1});P.connect=t.bind(null,D),P.disconnect=c.bind(null,D),N=[k,D,P]}else I||(v=new l(y,m));if(_.set(y,N===null?v:N[2]),N!==null){if(T===null){if(b===void 0)throw new Error("Missing the processor constructor.");if(w===null)throw new Error("Missing the native OfflineAudioContext constructor.");const L=A.channelCount*A.numberOfInputs,B=b.parameterDescriptors===void 0?0:b.parameterDescriptors.length,O=L+B;T=xo(A,O===0?null:await(async()=>{const V=new w(O,Math.ceil(A.context.length/128)*128,y.sampleRate),G=[],fe=[];for(let j=0;j<p.numberOfInputs;j+=1)G.push(i(V,{channelCount:p.channelCount,channelCountMode:p.channelCountMode,channelInterpretation:p.channelInterpretation,gain:1})),fe.push(o(V,{channelCount:p.channelCount,channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:p.channelCount}));const he=await Promise.all(Array.from(A.parameters.values()).map(async j=>{const H=s(V,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",offset:j.value});return await g(V,j,H.offset),H})),pe=r(V,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:Math.max(1,L+B)});for(let j=0;j<p.numberOfInputs;j+=1){G[j].connect(fe[j]);for(let H=0;H<p.channelCount;H+=1)fe[j].connect(pe,H,j*p.channelCount+H)}for(const[j,H]of he.entries())H.connect(pe,0,L+j),H.start(0);return pe.connect(V.destination),await Promise.all(G.map(j=>h(A,V,j))),f(V)})(),y,p,M,b,u)}const U=await T,k=n(y,{buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1}),[D,P,S]=N;U!==null&&(k.buffer=U,k.start(0)),k.connect(D);for(let L=0,B=0;L<A.numberOfOutputs;L+=1){const O=P[L];for(let x=0;x<M[L];x+=1)D.connect(O,B+x,x);B+=M[L]}return S}if(I)for(const[U,k]of A.parameters.entries())await e(y,k,v.parameters.get(U));else for(const[U,k]of A.parameters.entries())await g(y,k,v.parameters.get(U));return await h(A,y,v),v};return{render(A,y){a(y,A);const v=_.get(y);return v!==void 0?Promise.resolve(v):E(A,y)}}},Uo=(e,t)=>(n,r)=>{const o=t.get(n);if(o!==void 0)return o;const s=e.get(n);if(s!==void 0)return s;try{const i=r();return i instanceof Promise?(e.set(n,i),i.catch(()=>!1).then(a=>(e.delete(n),t.set(n,a),a))):(t.set(n,i),i)}catch{return t.set(n,!1),!1}},Bo=e=>(t,n,r)=>e(n,t,r),Do=e=>(t,n,r=0,o=0)=>{const s=t[r];if(s===void 0)throw e();return Ue(n)?s.connect(n,0,o):s.connect(n,0)},Wo=e=>t=>(e[0]=t,e[0]),Vo=(e,t,n,r,o,s,i,a)=>(c,u)=>{const d=t.get(c);if(d===void 0)throw new Error("Missing the expected cycle count.");const l=s(c.context),w=a(l);if(d===u){if(t.delete(c),!w&&i(c)){const g=r(c),{outputs:h}=n(c);for(const f of h)if(Ce(f)){const m=r(f[0]);e(g,m,f[1],f[2])}else{const m=o(f[0]);g.connect(m,f[1])}}}else t.set(c,d-u)},Fo=e=>(t,n,r,o)=>e(t[o],s=>s[0]===n&&s[1]===r),jo=e=>(t,n)=>{e(t).delete(n)},$o=e=>"delayTime"in e,Go=(e,t,n)=>function r(o,s){const i=Pe(s)?s:n(e,s);if($o(i))return[];if(o[0]===i)return[o];if(o.includes(i))return[];const{outputs:a}=t(i);return Array.from(a).map(c=>r([...o,i],c[0])).reduce((c,u)=>c.concat(u),[])},Re=(e,t,n)=>{const r=t[n];if(r===void 0)throw e();return r},qo=e=>(t,n=void 0,r=void 0,o=0)=>n===void 0?t.forEach(s=>s.disconnect()):typeof n=="number"?Re(e,t,n).disconnect():Ue(n)?r===void 0?t.forEach(s=>s.disconnect(n)):o===void 0?Re(e,t,r).disconnect(n,0):Re(e,t,r).disconnect(n,0,o):r===void 0?t.forEach(s=>s.disconnect(n)):Re(e,t,r).disconnect(n,0),zo=e=>t=>new Promise((n,r)=>{if(e===null){r(new SyntaxError);return}const o=e.document.head;if(o===null)r(new SyntaxError);else{const s=e.document.createElement("script"),i=new Blob([t],{type:"application/javascript"}),a=URL.createObjectURL(i),c=e.onerror,u=()=>{e.onerror=c,URL.revokeObjectURL(a)};e.onerror=(d,l,w,g,h)=>{if(l===a||l===e.location.href&&w===1&&g===1)return u(),r(h),!1;if(c!==null)return c(d,l,w,g,h)},s.onerror=()=>{u(),r(new SyntaxError)},s.onload=()=>{u(),n()},s.src=a,s.type="module",o.appendChild(s)}}),Ho=e=>class{constructor(n){this._nativeEventTarget=n,this._listeners=new WeakMap}addEventListener(n,r,o){if(r!==null){let s=this._listeners.get(r);s===void 0&&(s=e(this,r),typeof r=="function"&&this._listeners.set(r,s)),this._nativeEventTarget.addEventListener(n,s,o)}}dispatchEvent(n){return this._nativeEventTarget.dispatchEvent(n)}removeEventListener(n,r,o){const s=r===null?void 0:this._listeners.get(r);this._nativeEventTarget.removeEventListener(n,s===void 0?null:s,o)}},Yo=e=>(t,n,r)=>{Object.defineProperties(e,{currentFrame:{configurable:!0,get(){return Math.round(t*n)}},currentTime:{configurable:!0,get(){return t}}});try{return r()}finally{e!==null&&(delete e.currentFrame,delete e.currentTime)}},Xo=e=>async t=>{try{const n=await fetch(t);if(n.ok)return[await n.text(),n.url]}catch{}throw e()},Zo=(e,t)=>n=>t(e,n),Ko=e=>t=>{const n=e(t);if(n.renderer===null)throw new Error("Missing the renderer of the given AudioNode in the audio graph.");return n.renderer},Jo=e=>t=>{var n;return(n=e.get(t))!==null&&n!==void 0?n:0},Qo=e=>t=>{const n=e(t);if(n.renderer===null)throw new Error("Missing the renderer of the given AudioParam in the audio graph.");return n.renderer},es=e=>t=>e.get(t),Z=()=>new DOMException("","InvalidStateError"),ts=e=>t=>{const n=e.get(t);if(n===void 0)throw Z();return n},ns=(e,t)=>n=>{let r=e.get(n);if(r!==void 0)return r;if(t===null)throw new Error("Missing the native OfflineAudioContext constructor.");return r=new t(1,1,44100),e.set(n,r),r},rs=e=>t=>{const n=e.get(t);if(n===void 0)throw new Error("The context has no set of AudioWorkletNodes.");return n},os=()=>new DOMException("","InvalidAccessError"),ss=(e,t,n,r,o,s)=>i=>(a,c)=>{const u=e.get(a);if(u===void 0){if(!i&&s(a)){const d=r(a),{outputs:l}=n(a);for(const w of l)if(Ce(w)){const g=r(w[0]);t(d,g,w[1],w[2])}else{const g=o(w[0]);d.disconnect(g,w[1])}}e.set(a,c)}else e.set(a,u+c)},is=e=>t=>e!==null&&t instanceof e,as=e=>t=>e!==null&&typeof e.AudioNode=="function"&&t instanceof e.AudioNode,cs=e=>t=>e!==null&&typeof e.AudioParam=="function"&&t instanceof e.AudioParam,us=e=>t=>e!==null&&t instanceof e,ls=e=>e!==null&&e.isSecureContext,ds=(e,t,n,r)=>class extends e{constructor(s,i){const a=n(s),c=t(a,i);if(r(a))throw new TypeError;super(s,!0,c,null),this._nativeMediaStreamAudioSourceNode=c}get mediaStream(){return this._nativeMediaStreamAudioSourceNode.mediaStream}},fs=(e,t,n,r,o)=>class extends r{constructor(i={}){if(o===null)throw new Error("Missing the native AudioContext constructor.");let a;try{a=new o(i)}catch(d){throw d.code===12&&d.message==="sampleRate is not in range"?t():d}if(a===null)throw n();if(!go(i.latencyHint))throw new TypeError(`The provided value '${i.latencyHint}' is not a valid enum value of type AudioContextLatencyCategory.`);if(i.sampleRate!==void 0&&a.sampleRate!==i.sampleRate)throw t();super(a,2);const{latencyHint:c}=i,{sampleRate:u}=a;if(this._baseLatency=typeof a.baseLatency=="number"?a.baseLatency:c==="balanced"?512/u:c==="interactive"||c===void 0?256/u:c==="playback"?1024/u:Math.max(2,Math.min(128,Math.round(c*u/128)))*128/u,this._nativeAudioContext=a,o.name==="webkitAudioContext"?(this._nativeGainNode=a.createGain(),this._nativeOscillatorNode=a.createOscillator(),this._nativeGainNode.gain.value=1e-37,this._nativeOscillatorNode.connect(this._nativeGainNode).connect(a.destination),this._nativeOscillatorNode.start()):(this._nativeGainNode=null,this._nativeOscillatorNode=null),this._state=null,a.state==="running"){this._state="suspended";const d=()=>{this._state==="suspended"&&(this._state=null),a.removeEventListener("statechange",d)};a.addEventListener("statechange",d)}}get baseLatency(){return this._baseLatency}get state(){return this._state!==null?this._state:this._nativeAudioContext.state}close(){return this.state==="closed"?this._nativeAudioContext.close().then(()=>{throw e()}):(this._state==="suspended"&&(this._state=null),this._nativeAudioContext.close().then(()=>{this._nativeGainNode!==null&&this._nativeOscillatorNode!==null&&(this._nativeOscillatorNode.stop(),this._nativeGainNode.disconnect(),this._nativeOscillatorNode.disconnect()),mo(this)}))}resume(){return this._state==="suspended"?new Promise((i,a)=>{const c=()=>{this._nativeAudioContext.removeEventListener("statechange",c),this._nativeAudioContext.state==="running"?i():this.resume().then(i,a)};this._nativeAudioContext.addEventListener("statechange",c)}):this._nativeAudioContext.resume().catch(i=>{throw i===void 0||i.code===15?e():i})}suspend(){return this._nativeAudioContext.suspend().catch(i=>{throw i===void 0?e():i})}},hs=(e,t,n,r,o,s)=>class extends n{constructor(a,c){super(a),this._nativeContext=a,un.set(this,a),r(a)&&o.set(a,new Set),this._destination=new e(this,c),this._listener=t(this,a),this._onstatechange=null}get currentTime(){return this._nativeContext.currentTime}get destination(){return this._destination}get listener(){return this._listener}get onstatechange(){return this._onstatechange}set onstatechange(a){const c=typeof a=="function"?s(this,a):null;this._nativeContext.onstatechange=c;const u=this._nativeContext.onstatechange;this._onstatechange=u!==null&&u===c?a:u}get sampleRate(){return this._nativeContext.sampleRate}get state(){return this._nativeContext.state}},zt=e=>{const t=new Uint32Array([1179011410,40,1163280727,544501094,16,131073,44100,176400,1048580,1635017060,4,0]);try{const n=e.decodeAudioData(t.buffer,()=>{});return n===void 0?!1:(n.catch(()=>{}),!0)}catch{}return!1},ps=(e,t)=>(n,r,o)=>{const s=new Set;return n.connect=(i=>(a,c=0,u=0)=>{const d=s.size===0;if(t(a))return i.call(n,a,c,u),e(s,[a,c,u],l=>l[0]===a&&l[1]===c&&l[2]===u,!0),d&&r(),a;i.call(n,a,c),e(s,[a,c],l=>l[0]===a&&l[1]===c,!0),d&&r()})(n.connect),n.disconnect=(i=>(a,c,u)=>{const d=s.size>0;if(a===void 0)i.apply(n),s.clear();else if(typeof a=="number"){i.call(n,a);for(const w of s)w[1]===a&&s.delete(w)}else{t(a)?i.call(n,a,c,u):i.call(n,a,c);for(const w of s)w[0]===a&&(c===void 0||w[1]===c)&&(u===void 0||w[2]===u)&&s.delete(w)}const l=s.size===0;d&&l&&o()})(n.disconnect),n},se=(e,t,n)=>{const r=t[n];r!==void 0&&r!==e[n]&&(e[n]=r)},Te=(e,t)=>{se(e,t,"channelCount"),se(e,t,"channelCountMode"),se(e,t,"channelInterpretation")},ms=e=>e===null?null:e.hasOwnProperty("AudioBuffer")?e.AudioBuffer:null,gt=(e,t,n)=>{const r=t[n];r!==void 0&&r!==e[n].value&&(e[n].value=r)},gs=e=>{e.start=(t=>{let n=!1;return(r=0,o=0,s)=>{if(n)throw Z();t.call(e,r,o,s),n=!0}})(e.start)},An=e=>{e.start=(t=>(n=0,r=0,o)=>{if(typeof o=="number"&&o<0||r<0||n<0)throw new RangeError("The parameters can't be negative.");t.call(e,n,r,o)})(e.start)},bn=e=>{e.stop=(t=>(n=0)=>{if(n<0)throw new RangeError("The parameter can't be negative.");t.call(e,n)})(e.stop)},ws=(e,t,n,r,o,s,i,a,c,u,d)=>(l,w)=>{const g=l.createBufferSource();return Te(g,w),gt(g,w,"playbackRate"),se(g,w,"buffer"),se(g,w,"loop"),se(g,w,"loopEnd"),se(g,w,"loopStart"),t(n,()=>n(l))||gs(g),t(r,()=>r(l))||c(g),t(o,()=>o(l))||u(g,l),t(s,()=>s(l))||An(g),t(i,()=>i(l))||d(g,l),t(a,()=>a(l))||bn(g),e(l,g),g},vs=e=>e===null?null:e.hasOwnProperty("AudioContext")?e.AudioContext:e.hasOwnProperty("webkitAudioContext")?e.webkitAudioContext:null,_s=(e,t)=>(n,r,o)=>{const s=n.destination;if(s.channelCount!==r)try{s.channelCount=r}catch{}o&&s.channelCountMode!=="explicit"&&(s.channelCountMode="explicit"),s.maxChannelCount===0&&Object.defineProperty(s,"maxChannelCount",{value:r});const i=e(n,{channelCount:r,channelCountMode:s.channelCountMode,channelInterpretation:s.channelInterpretation,gain:1});return t(i,"channelCount",a=>()=>a.call(i),a=>c=>{a.call(i,c);try{s.channelCount=c}catch(u){if(c>s.maxChannelCount)throw u}}),t(i,"channelCountMode",a=>()=>a.call(i),a=>c=>{a.call(i,c),s.channelCountMode=c}),t(i,"channelInterpretation",a=>()=>a.call(i),a=>c=>{a.call(i,c),s.channelInterpretation=c}),Object.defineProperty(i,"maxChannelCount",{get:()=>s.maxChannelCount}),i.connect(s),i},ys=e=>e===null?null:e.hasOwnProperty("AudioWorkletNode")?e.AudioWorkletNode:null,Es=e=>{const{port1:t}=new MessageChannel;try{t.postMessage(e)}finally{t.close()}},As=(e,t,n,r,o)=>(s,i,a,c,u,d)=>{if(a!==null)try{const l=new a(s,c,d),w=new Map;let g=null;if(Object.defineProperties(l,{channelCount:{get:()=>d.channelCount,set:()=>{throw e()}},channelCountMode:{get:()=>"explicit",set:()=>{throw e()}},onprocessorerror:{get:()=>g,set:h=>{typeof g=="function"&&l.removeEventListener("processorerror",g),g=typeof h=="function"?h:null,typeof g=="function"&&l.addEventListener("processorerror",g)}}}),l.addEventListener=(h=>(...f)=>{if(f[0]==="processorerror"){const m=typeof f[1]=="function"?f[1]:typeof f[1]=="object"&&f[1]!==null&&typeof f[1].handleEvent=="function"?f[1].handleEvent:null;if(m!==null){const p=w.get(f[1]);p!==void 0?f[1]=p:(f[1]=b=>{b.type==="error"?(Object.defineProperties(b,{type:{value:"processorerror"}}),m(b)):m(new ErrorEvent(f[0],{...b}))},w.set(m,f[1]))}}return h.call(l,"error",f[1],f[2]),h.call(l,...f)})(l.addEventListener),l.removeEventListener=(h=>(...f)=>{if(f[0]==="processorerror"){const m=w.get(f[1]);m!==void 0&&(w.delete(f[1]),f[1]=m)}return h.call(l,"error",f[1],f[2]),h.call(l,f[0],f[1],f[2])})(l.removeEventListener),d.numberOfOutputs!==0){const h=n(s,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});return l.connect(h).connect(s.destination),o(l,()=>h.disconnect(),()=>h.connect(s.destination))}return l}catch(l){throw l.code===11?r():l}if(u===void 0)throw r();return Es(d),t(s,i,u,d)},bs=(e,t)=>e===null?512:Math.max(512,Math.min(16384,Math.pow(2,Math.round(Math.log2(e*t))))),Cs=e=>new Promise((t,n)=>{const{port1:r,port2:o}=new MessageChannel;r.onmessage=({data:s})=>{r.close(),o.close(),t(s)},r.onmessageerror=({data:s})=>{r.close(),o.close(),n(s)},o.postMessage(e)}),Ts=async(e,t)=>{const n=await Cs(t);return new e(n)},Ms=(e,t,n,r)=>{let o=st.get(e);o===void 0&&(o=new WeakMap,st.set(e,o));const s=Ts(n,r);return o.set(t,s),s},Ns=(e,t,n,r,o,s,i,a,c,u,d,l,w)=>(g,h,f,m)=>{if(m.numberOfInputs===0&&m.numberOfOutputs===0)throw c();const p=Array.isArray(m.outputChannelCount)?m.outputChannelCount:Array.from(m.outputChannelCount);if(p.some(C=>C<1))throw c();if(p.length!==m.numberOfOutputs)throw t();if(m.channelCountMode!=="explicit")throw c();const b=m.channelCount*m.numberOfInputs,_=p.reduce((C,R)=>C+R,0),T=f.parameterDescriptors===void 0?0:f.parameterDescriptors.length;if(b+T>6||_>6)throw c();const E=new MessageChannel,A=[],y=[];for(let C=0;C<m.numberOfInputs;C+=1)A.push(i(g,{channelCount:m.channelCount,channelCountMode:m.channelCountMode,channelInterpretation:m.channelInterpretation,gain:1})),y.push(o(g,{channelCount:m.channelCount,channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:m.channelCount}));const v=[];if(f.parameterDescriptors!==void 0)for(const{defaultValue:C,maxValue:R,minValue:q,name:F}of f.parameterDescriptors){const W=s(g,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",offset:m.parameterData[F]!==void 0?m.parameterData[F]:C===void 0?0:C});Object.defineProperties(W.offset,{defaultValue:{get:()=>C===void 0?0:C},maxValue:{get:()=>R===void 0?ht:R},minValue:{get:()=>q===void 0?je:q}}),v.push(W)}const N=r(g,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:Math.max(1,b+T)}),I=bs(h,g.sampleRate),M=a(g,I,b+T,Math.max(1,_)),U=o(g,{channelCount:Math.max(1,_),channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:Math.max(1,_)}),k=[];for(let C=0;C<m.numberOfOutputs;C+=1)k.push(r(g,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:p[C]}));for(let C=0;C<m.numberOfInputs;C+=1){A[C].connect(y[C]);for(let R=0;R<m.channelCount;R+=1)y[C].connect(N,R,C*m.channelCount+R)}const D=new yn(f.parameterDescriptors===void 0?[]:f.parameterDescriptors.map(({name:C},R)=>{const q=v[R];return q.connect(N,0,b+R),q.start(0),[C,q.offset]}));N.connect(M);let P=m.channelInterpretation,S=null;const L=m.numberOfOutputs===0?[M]:k,B={get bufferSize(){return I},get channelCount(){return m.channelCount},set channelCount(C){throw n()},get channelCountMode(){return m.channelCountMode},set channelCountMode(C){throw n()},get channelInterpretation(){return P},set channelInterpretation(C){for(const R of A)R.channelInterpretation=C;P=C},get context(){return M.context},get inputs(){return A},get numberOfInputs(){return m.numberOfInputs},get numberOfOutputs(){return m.numberOfOutputs},get onprocessorerror(){return S},set onprocessorerror(C){typeof S=="function"&&B.removeEventListener("processorerror",S),S=typeof C=="function"?C:null,typeof S=="function"&&B.addEventListener("processorerror",S)},get parameters(){return D},get port(){return E.port2},addEventListener(...C){return M.addEventListener(C[0],C[1],C[2])},connect:e.bind(null,L),disconnect:u.bind(null,L),dispatchEvent(...C){return M.dispatchEvent(C[0])},removeEventListener(...C){return M.removeEventListener(C[0],C[1],C[2])}},O=new Map;E.port1.addEventListener=(C=>(...R)=>{if(R[0]==="message"){const q=typeof R[1]=="function"?R[1]:typeof R[1]=="object"&&R[1]!==null&&typeof R[1].handleEvent=="function"?R[1].handleEvent:null;if(q!==null){const F=O.get(R[1]);F!==void 0?R[1]=F:(R[1]=W=>{d(g.currentTime,g.sampleRate,()=>q(W))},O.set(q,R[1]))}}return C.call(E.port1,R[0],R[1],R[2])})(E.port1.addEventListener),E.port1.removeEventListener=(C=>(...R)=>{if(R[0]==="message"){const q=O.get(R[1]);q!==void 0&&(O.delete(R[1]),R[1]=q)}return C.call(E.port1,R[0],R[1],R[2])})(E.port1.removeEventListener);let x=null;Object.defineProperty(E.port1,"onmessage",{get:()=>x,set:C=>{typeof x=="function"&&E.port1.removeEventListener("message",x),x=typeof C=="function"?C:null,typeof x=="function"&&(E.port1.addEventListener("message",x),E.port1.start())}}),f.prototype.port=E.port1;let V=null;Ms(g,B,f,m).then(C=>V=C);const fe=De(m.numberOfInputs,m.channelCount),he=De(m.numberOfOutputs,p),pe=f.parameterDescriptors===void 0?[]:f.parameterDescriptors.reduce((C,{name:R})=>({...C,[R]:new Float32Array(128)}),{});let j=!0;const H=()=>{m.numberOfOutputs>0&&M.disconnect(U);for(let C=0,R=0;C<m.numberOfOutputs;C+=1){const q=k[C];for(let F=0;F<p[C];F+=1)U.disconnect(q,R+F,F);R+=p[C]}},Me=new Map;M.onaudioprocess=({inputBuffer:C,outputBuffer:R})=>{if(V!==null){const q=l(B);for(let F=0;F<I;F+=128){for(let W=0;W<m.numberOfInputs;W+=1)for(let $=0;$<m.channelCount;$+=1)Be(C,fe[W],$,$,F);f.parameterDescriptors!==void 0&&f.parameterDescriptors.forEach(({name:W},$)=>{Be(C,pe,W,b+$,F)});for(let W=0;W<m.numberOfInputs;W+=1)for(let $=0;$<p[W];$+=1)he[W][$].byteLength===0&&(he[W][$]=new Float32Array(128));try{const W=fe.map((Y,te)=>{if(q[te].size>0)return Me.set(te,I/128),Y;const Ke=Me.get(te);return Ke===void 0?[]:(Y.every(Zn=>Zn.every(Kn=>Kn===0))&&(Ke===1?Me.delete(te):Me.set(te,Ke-1)),Y)});j=d(g.currentTime+F/g.sampleRate,g.sampleRate,()=>V.process(W,he,pe));for(let Y=0,te=0;Y<m.numberOfOutputs;Y+=1){for(let _e=0;_e<p[Y];_e+=1)En(R,he[Y],_e,te+_e,F);te+=p[Y]}}catch(W){j=!1,B.dispatchEvent(new ErrorEvent("processorerror",{colno:W.colno,filename:W.filename,lineno:W.lineno,message:W.message}))}if(!j){for(let W=0;W<m.numberOfInputs;W+=1){A[W].disconnect(y[W]);for(let $=0;$<m.channelCount;$+=1)y[F].disconnect(N,$,W*m.channelCount+$)}if(f.parameterDescriptors!==void 0){const W=f.parameterDescriptors.length;for(let $=0;$<W;$+=1){const Y=v[$];Y.disconnect(N,0,b+$),Y.stop()}}N.disconnect(M),M.onaudioprocess=null,Xe?H():Nt();break}}}};let Xe=!1;const Ze=i(g,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0}),Mt=()=>M.connect(Ze).connect(g.destination),Nt=()=>{M.disconnect(Ze),Ze.disconnect()},Yn=()=>{if(j){Nt(),m.numberOfOutputs>0&&M.connect(U);for(let C=0,R=0;C<m.numberOfOutputs;C+=1){const q=k[C];for(let F=0;F<p[C];F+=1)U.connect(q,R+F,F);R+=p[C]}}Xe=!0},Xn=()=>{j&&(Mt(),H()),Xe=!1};return Mt(),w(B,Yn,Xn)},Os=(e,t)=>(n,r)=>{const o=n.createChannelMerger(r.numberOfInputs);return e!==null&&e.name==="webkitAudioContext"&&t(n,o),Te(o,r),o},Rs=e=>{const t=e.numberOfOutputs;Object.defineProperty(e,"channelCount",{get:()=>t,set:n=>{if(n!==t)throw Z()}}),Object.defineProperty(e,"channelCountMode",{get:()=>"explicit",set:n=>{if(n!=="explicit")throw Z()}}),Object.defineProperty(e,"channelInterpretation",{get:()=>"discrete",set:n=>{if(n!=="discrete")throw Z()}})},Cn=(e,t)=>{const n=e.createChannelSplitter(t.numberOfOutputs);return Te(n,t),Rs(n),n},Is=(e,t,n,r,o)=>(s,i)=>{if(s.createConstantSource===void 0)return n(s,i);const a=s.createConstantSource();return Te(a,i),gt(a,i,"offset"),t(r,()=>r(s))||An(a),t(o,()=>o(s))||bn(a),e(s,a),a},Tn=(e,t)=>(e.connect=t.connect.bind(t),e.disconnect=t.disconnect.bind(t),e),Ss=(e,t,n,r)=>(o,{offset:s,...i})=>{const a=o.createBuffer(1,2,44100),c=t(o,{buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1}),u=n(o,{...i,gain:s}),d=a.getChannelData(0);d[0]=1,d[1]=1,c.buffer=a,c.loop=!0;const l={get bufferSize(){},get channelCount(){return u.channelCount},set channelCount(h){u.channelCount=h},get channelCountMode(){return u.channelCountMode},set channelCountMode(h){u.channelCountMode=h},get channelInterpretation(){return u.channelInterpretation},set channelInterpretation(h){u.channelInterpretation=h},get context(){return u.context},get inputs(){return[]},get numberOfInputs(){return c.numberOfInputs},get numberOfOutputs(){return u.numberOfOutputs},get offset(){return u.gain},get onended(){return c.onended},set onended(h){c.onended=h},addEventListener(...h){return c.addEventListener(h[0],h[1],h[2])},dispatchEvent(...h){return c.dispatchEvent(h[0])},removeEventListener(...h){return c.removeEventListener(h[0],h[1],h[2])},start(h=0){c.start.call(c,h)},stop(h=0){c.stop.call(c,h)}},w=()=>c.connect(u),g=()=>c.disconnect(u);return e(o,c),r(Tn(l,u),w,g)},oe=(e,t)=>{const n=e.createGain();return Te(n,t),gt(n,t,"gain"),n},ks=(e,{mediaStream:t})=>{const n=t.getAudioTracks();n.sort((s,i)=>s.id<i.id?-1:s.id>i.id?1:0);const r=n.slice(0,1),o=e.createMediaStreamSource(new MediaStream(r));return Object.defineProperty(o,"mediaStream",{value:t}),o},Ls=e=>e===null?null:e.hasOwnProperty("OfflineAudioContext")?e.OfflineAudioContext:e.hasOwnProperty("webkitOfflineAudioContext")?e.webkitOfflineAudioContext:null,wt=(e,t,n,r)=>e.createScriptProcessor(t,n,r),de=()=>new DOMException("","NotSupportedError"),xs=(e,t)=>(n,r,o)=>(e(r).replay(o),t(r,n,o)),Ps=(e,t,n)=>async(r,o,s)=>{const i=e(r);await Promise.all(i.activeInputs.map((a,c)=>Array.from(a).map(async([u,d])=>{const w=await t(u).render(u,o),g=r.context.destination;!n(u)&&(r!==g||!n(r))&&w.connect(s,d,c)})).reduce((a,c)=>[...a,...c],[]))},Us=(e,t,n)=>async(r,o,s)=>{const i=t(r);await Promise.all(Array.from(i.activeInputs).map(async([a,c])=>{const d=await e(a).render(a,o);n(a)||d.connect(s,c)}))},Bs=(e,t,n,r)=>o=>e(zt,()=>zt(o))?Promise.resolve(e(r,r)).then(s=>{if(!s){const i=n(o,512,0,1);o.oncomplete=()=>{i.onaudioprocess=null,i.disconnect()},i.onaudioprocess=()=>o.currentTime,i.connect(o.destination)}return o.startRendering()}):new Promise(s=>{const i=t(o,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});o.oncomplete=a=>{i.disconnect(),s(a.renderedBuffer)},i.connect(o.destination),o.startRendering()}),Ds=e=>(t,n)=>{e.set(t,n)},Ws=e=>()=>{if(e===null)return!1;try{new e({length:1,sampleRate:44100})}catch{return!1}return!0},Vs=(e,t)=>async()=>{if(e===null)return!0;if(t===null)return!1;const n=new Blob(['class A extends AudioWorkletProcessor{process(i){this.port.postMessage(i,[i[0][0].buffer])}}registerProcessor("a",A)'],{type:"application/javascript; charset=utf-8"}),r=new t(1,128,44100),o=URL.createObjectURL(n);let s=!1,i=!1;try{await r.audioWorklet.addModule(o);const a=new e(r,"a",{numberOfOutputs:0}),c=r.createOscillator();a.port.onmessage=()=>s=!0,a.onprocessorerror=()=>i=!0,c.connect(a),c.start(0),await r.startRendering(),await new Promise(u=>setTimeout(u))}catch{}finally{URL.revokeObjectURL(o)}return s&&!i},Fs=(e,t)=>()=>{if(t===null)return Promise.resolve(!1);const n=new t(1,1,44100),r=e(n,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});return new Promise(o=>{n.oncomplete=()=>{r.disconnect(),o(n.currentTime!==0)},n.startRendering()})},js=()=>new DOMException("","UnknownError"),$s=()=>typeof window>"u"?null:window,Gs=(e,t)=>n=>{n.copyFromChannel=(r,o,s=0)=>{const i=e(s),a=e(o);if(a>=n.numberOfChannels)throw t();const c=n.length,u=n.getChannelData(a),d=r.length;for(let l=i<0?-i:0;l+i<c&&l<d;l+=1)r[l]=u[l+i]},n.copyToChannel=(r,o,s=0)=>{const i=e(s),a=e(o);if(a>=n.numberOfChannels)throw t();const c=n.length,u=n.getChannelData(a),d=r.length;for(let l=i<0?-i:0;l+i<c&&l<d;l+=1)u[l+i]=r[l]}},qs=e=>t=>{t.copyFromChannel=(n=>(r,o,s=0)=>{const i=e(s),a=e(o);if(i<t.length)return n.call(t,r,a,i)})(t.copyFromChannel),t.copyToChannel=(n=>(r,o,s=0)=>{const i=e(s),a=e(o);if(i<t.length)return n.call(t,r,a,i)})(t.copyToChannel)},zs=e=>(t,n)=>{const r=n.createBuffer(1,1,44100);t.buffer===null&&(t.buffer=r),e(t,"buffer",o=>()=>{const s=o.call(t);return s===r?null:s},o=>s=>o.call(t,s===null?r:s))},Hs=(e,t)=>(n,r)=>{r.channelCount=1,r.channelCountMode="explicit",Object.defineProperty(r,"channelCount",{get:()=>1,set:()=>{throw e()}}),Object.defineProperty(r,"channelCountMode",{get:()=>"explicit",set:()=>{throw e()}});const o=n.createBufferSource();t(r,()=>{const a=r.numberOfInputs;for(let c=0;c<a;c+=1)o.connect(r,0,c)},()=>o.disconnect(r))},Ys=(e,t,n)=>e.copyFromChannel===void 0?e.getChannelData(n)[0]:(e.copyFromChannel(t,n),t[0]),vt=(e,t,n,r)=>{let o=e;for(;!o.hasOwnProperty(t);)o=Object.getPrototypeOf(o);const{get:s,set:i}=Object.getOwnPropertyDescriptor(o,t);Object.defineProperty(e,t,{get:n(s),set:r(i)})},Xs=e=>({...e,outputChannelCount:e.outputChannelCount!==void 0?e.outputChannelCount:e.numberOfInputs===1&&e.numberOfOutputs===1?[e.channelCount]:Array.from({length:e.numberOfOutputs},()=>1)}),Mn=(e,t,n)=>{try{e.setValueAtTime(t,n)}catch(r){if(r.code!==9)throw r;Mn(e,t,n+1e-7)}},Zs=e=>{const t=e.createBufferSource();t.start();try{t.start()}catch{return!0}return!1},Ks=e=>{const t=e.createBufferSource(),n=e.createBuffer(1,1,44100);t.buffer=n;try{t.start(0,1)}catch{return!1}return!0},Js=e=>{const t=e.createBufferSource();t.start();try{t.stop()}catch{return!1}return!0},Nn=e=>{const t=e.createOscillator();try{t.start(-1)}catch(n){return n instanceof RangeError}return!1},Qs=e=>{const t=e.createBuffer(1,1,44100),n=e.createBufferSource();n.buffer=t,n.start(),n.stop();try{return n.stop(),!0}catch{return!1}},On=e=>{const t=e.createOscillator();try{t.stop(-1)}catch(n){return n instanceof RangeError}return!1},ei=e=>{const{port1:t,port2:n}=new MessageChannel;try{t.postMessage(e)}finally{t.close(),n.close()}},ti=e=>{e.start=(t=>(n=0,r=0,o)=>{const s=e.buffer,i=s===null?r:Math.min(s.duration,r);s!==null&&i>s.duration-.5/e.context.sampleRate?t.call(e,n,0,0):t.call(e,n,i,o)})(e.start)},ni=(e,t)=>{const n=t.createGain();e.connect(n);const r=(o=>()=>{o.call(e,n),e.removeEventListener("ended",r)})(e.disconnect);e.addEventListener("ended",r),Tn(e,n),e.stop=(o=>{let s=!1;return(i=0)=>{if(s)try{o.call(e,i)}catch{n.gain.setValueAtTime(0,i)}else o.call(e,i),s=!0}})(e.stop)},$e=(e,t)=>n=>{const r={value:e};return Object.defineProperties(n,{currentTarget:r,target:r}),typeof t=="function"?t.call(e,n):t.handleEvent.call(e,n)},ri=Hr(le),oi=Qr(le),si=Fo(Fe),ii=new WeakMap,ai=Jo(ii),we=Uo(new Map,new WeakMap),J=$s(),Rn=Ko(z),_t=Ps(z,Rn,ae),ce=ts(un),ve=Ls(J),ee=us(ve),In=new WeakMap,Sn=Ho($e),Ge=vs(J),ci=is(Ge),kn=as(J),ui=cs(J),Ee=ys(J),qe=Oo(Yr(on),Jr(ri,oi,ct,si,ut,z,ai,Ae,X,le,ie,ae,Ie),we,ss(rt,ut,z,X,ye,ie),ue,os,de,Vo(ct,rt,z,X,ye,ce,ie,ee),Go(In,z,K),Sn,ce,ci,kn,ui,ee,Ee),li=new WeakSet,Ht=ms(J),Ln=Wo(new Uint32Array(1)),di=Gs(Ln,ue),fi=qs(Ln),hi=oo(li,we,de,Ht,ve,Ws(Ht),di,fi),yt=eo(oe),xn=Us(Rn,be,ae),Pn=Bo(xn),ze=ws(yt,we,Zs,Ks,Js,Nn,Qs,On,ti,zs(vt),ni),Un=xs(Qo(be),xn),pi=ao(Pn,ze,X,Un,_t),Et=Ro(Xr(an),In,cn,Io,Fr,jr,$r,Gr,qr,et,nn,Ge,Mn),mi=io(qe,pi,Et,Z,ze,ce,ee,$e),gi=wo(qe,vo,ue,Z,_s(oe,vt),ce,ee,_t),He=ps(le,kn),wi=Hs(Z,He),At=Os(Ge,wi),vi=Ss(yt,ze,oe,He),bt=Is(yt,we,vi,Nn,On),_i=Bs(we,oe,wt,Fs(oe,ve)),yi=_o(Et,At,bt,wt,de,Ys,ee,vt),Bn=new WeakMap,Ei=hs(gi,yi,Sn,ee,Bn,$e),Dn=ls(J),Ct=Yo(J),Wn=new WeakMap,Ai=ns(Wn,ve),Yt=Dn?Kr(we,de,zo(J),Ct,Xo(zr),ce,Ai,ee,Ee,new WeakMap,new WeakMap,Vs(Ee,ve),J):void 0,bi=ds(qe,ks,ce,ee),Vn=rs(Bn),Ci=to(Vn),Fn=Do(ue),Ti=jo(Vn),jn=qo(ue),$n=new WeakMap,Mi=Zo($n,K),Ni=Ns(Fn,ue,Z,At,Cn,bt,oe,wt,de,jn,Ct,Mi,He),Oi=As(Z,Ni,oe,de,He),Ri=Po(Pn,Fn,ze,At,Cn,bt,oe,Ti,jn,Ct,X,Ee,ve,Un,_t,_i),Ii=es(Wn),Si=Ds($n),Xt=Dn?ko(Ci,qe,Et,Ri,Oi,z,Ii,ce,ee,Ee,Xs,Si,ei,$e):void 0,ki=fs(Z,de,js,Ei,Ge),Gn="Missing AudioWorklet support. Maybe this is not running in a secure context.",Li=async(e,t,n,r,o)=>{const{encoderId:s,port:i}=await Jt(o,t.sampleRate);if(Xt===void 0)throw new Error(Gn);const a=new mi(t,{buffer:e}),c=new bi(t,{mediaStream:r}),u=Br(Xt,t,{channelCount:n});return{audioBufferSourceNode:a,encoderId:s,mediaStreamAudioSourceNode:c,port:i,recorderAudioWorkletNode:u}},xi=(e,t,n,r)=>(o,s,i)=>{var a;const c=(a=s.getAudioTracks()[0])===null||a===void 0?void 0:a.getSettings().sampleRate,u=new ki({latencyHint:"playback",sampleRate:c}),d=Math.max(1024,Math.ceil(u.baseLatency*u.sampleRate)),l=new hi({length:d,sampleRate:u.sampleRate}),w=[],g=Ur(v=>{if(Yt===void 0)throw new Error(Gn);return Yt(u,v)});let h=null,f=null,m=null,p=null,b=!0;const _=v=>{o.dispatchEvent(e("dataavailable",{data:new Blob(v,{type:i})}))},T=async(v,N)=>{const I=await Se(v,N);m===null?w.push(...I):(_(I),p=T(v,N))},E=()=>(b=!0,u.resume()),A=()=>{m!==null&&(h!==null&&(s.removeEventListener("addtrack",h),s.removeEventListener("removetrack",h)),f!==null&&clearTimeout(f),m.then(async({encoderId:v,mediaStreamAudioSourceNode:N,recorderAudioWorkletNode:I})=>{p!==null&&(p.catch(()=>{}),p=null),await I.stop(),N.disconnect(I);const M=await Se(v,null);m===null&&await y(),_([...w,...M]),w.length=0,o.dispatchEvent(new Event("stop"))}),m=null)},y=()=>(b=!1,u.suspend());return y(),{get mimeType(){return i},get state(){return m===null?"inactive":b?"recording":"paused"},pause(){if(m===null)throw n();b&&(y(),o.dispatchEvent(new Event("pause")))},resume(){if(m===null)throw n();b||(E(),o.dispatchEvent(new Event("resume")))},start(v){var N;if(m!==null)throw n();if(s.getVideoTracks().length>0)throw r();o.dispatchEvent(new Event("start"));const I=s.getAudioTracks(),M=I.length===0?2:(N=I[0].getSettings().channelCount)!==null&&N!==void 0?N:2;m=Promise.all([E(),g.then(()=>Li(l,u,M,s,i))]).then(async([,{audioBufferSourceNode:k,encoderId:D,mediaStreamAudioSourceNode:P,port:S,recorderAudioWorkletNode:L}])=>(P.connect(L),await new Promise(B=>{k.onended=B,k.connect(L),k.start(u.currentTime+d/u.sampleRate)}),k.disconnect(L),await L.record(S),v!==void 0&&(p=T(D,v)),{encoderId:D,mediaStreamAudioSourceNode:P,recorderAudioWorkletNode:L}));const U=s.getTracks();h=()=>{A(),o.dispatchEvent(new ErrorEvent("error",{error:t()}))},s.addEventListener("addtrack",h),s.addEventListener("removetrack",h),f=setInterval(()=>{const k=s.getTracks();(k.length!==U.length||k.some((D,P)=>D!==U[P]))&&h!==null&&h()},1e3)},stop:A}};class Qe{constructor(t,n=0,r){if(n<0||r!==void 0&&r<0)throw new RangeError;const o=t.reduce((d,l)=>d+l.byteLength,0);if(n>o||r!==void 0&&n+r>o)throw new RangeError;const s=[],i=r===void 0?o-n:r,a=[];let c=0,u=n;for(const d of t)if(a.length===0)if(d.byteLength>u){c=d.byteLength-u;const l=c>i?i:c;s.push(new DataView(d,u,l)),a.push(d)}else u-=d.byteLength;else if(c<i){c+=d.byteLength;const l=c>i?d.byteLength-c+i:d.byteLength;s.push(new DataView(d,0,l)),a.push(d)}this._buffers=a,this._byteLength=i,this._byteOffset=u,this._dataViews=s,this._internalBuffer=new DataView(new ArrayBuffer(8))}get buffers(){return this._buffers}get byteLength(){return this._byteLength}get byteOffset(){return this._byteOffset}getFloat32(t,n){return this._internalBuffer.setUint8(0,this.getUint8(t+0)),this._internalBuffer.setUint8(1,this.getUint8(t+1)),this._internalBuffer.setUint8(2,this.getUint8(t+2)),this._internalBuffer.setUint8(3,this.getUint8(t+3)),this._internalBuffer.getFloat32(0,n)}getFloat64(t,n){return this._internalBuffer.setUint8(0,this.getUint8(t+0)),this._internalBuffer.setUint8(1,this.getUint8(t+1)),this._internalBuffer.setUint8(2,this.getUint8(t+2)),this._internalBuffer.setUint8(3,this.getUint8(t+3)),this._internalBuffer.setUint8(4,this.getUint8(t+4)),this._internalBuffer.setUint8(5,this.getUint8(t+5)),this._internalBuffer.setUint8(6,this.getUint8(t+6)),this._internalBuffer.setUint8(7,this.getUint8(t+7)),this._internalBuffer.getFloat64(0,n)}getInt16(t,n){return this._internalBuffer.setUint8(0,this.getUint8(t+0)),this._internalBuffer.setUint8(1,this.getUint8(t+1)),this._internalBuffer.getInt16(0,n)}getInt32(t,n){return this._internalBuffer.setUint8(0,this.getUint8(t+0)),this._internalBuffer.setUint8(1,this.getUint8(t+1)),this._internalBuffer.setUint8(2,this.getUint8(t+2)),this._internalBuffer.setUint8(3,this.getUint8(t+3)),this._internalBuffer.getInt32(0,n)}getInt8(t){const[n,r]=this._findDataViewWithOffset(t);return n.getInt8(t-r)}getUint16(t,n){return this._internalBuffer.setUint8(0,this.getUint8(t+0)),this._internalBuffer.setUint8(1,this.getUint8(t+1)),this._internalBuffer.getUint16(0,n)}getUint32(t,n){return this._internalBuffer.setUint8(0,this.getUint8(t+0)),this._internalBuffer.setUint8(1,this.getUint8(t+1)),this._internalBuffer.setUint8(2,this.getUint8(t+2)),this._internalBuffer.setUint8(3,this.getUint8(t+3)),this._internalBuffer.getUint32(0,n)}getUint8(t){const[n,r]=this._findDataViewWithOffset(t);return n.getUint8(t-r)}setFloat32(t,n,r){this._internalBuffer.setFloat32(0,n,r),this.setUint8(t,this._internalBuffer.getUint8(0)),this.setUint8(t+1,this._internalBuffer.getUint8(1)),this.setUint8(t+2,this._internalBuffer.getUint8(2)),this.setUint8(t+3,this._internalBuffer.getUint8(3))}setFloat64(t,n,r){this._internalBuffer.setFloat64(0,n,r),this.setUint8(t,this._internalBuffer.getUint8(0)),this.setUint8(t+1,this._internalBuffer.getUint8(1)),this.setUint8(t+2,this._internalBuffer.getUint8(2)),this.setUint8(t+3,this._internalBuffer.getUint8(3)),this.setUint8(t+4,this._internalBuffer.getUint8(4)),this.setUint8(t+5,this._internalBuffer.getUint8(5)),this.setUint8(t+6,this._internalBuffer.getUint8(6)),this.setUint8(t+7,this._internalBuffer.getUint8(7))}setInt16(t,n,r){this._internalBuffer.setInt16(0,n,r),this.setUint8(t,this._internalBuffer.getUint8(0)),this.setUint8(t+1,this._internalBuffer.getUint8(1))}setInt32(t,n,r){this._internalBuffer.setInt32(0,n,r),this.setUint8(t,this._internalBuffer.getUint8(0)),this.setUint8(t+1,this._internalBuffer.getUint8(1)),this.setUint8(t+2,this._internalBuffer.getUint8(2)),this.setUint8(t+3,this._internalBuffer.getUint8(3))}setInt8(t,n){const[r,o]=this._findDataViewWithOffset(t);r.setInt8(t-o,n)}setUint16(t,n,r){this._internalBuffer.setUint16(0,n,r),this.setUint8(t,this._internalBuffer.getUint8(0)),this.setUint8(t+1,this._internalBuffer.getUint8(1))}setUint32(t,n,r){this._internalBuffer.setUint32(0,n,r),this.setUint8(t,this._internalBuffer.getUint8(0)),this.setUint8(t+1,this._internalBuffer.getUint8(1)),this.setUint8(t+2,this._internalBuffer.getUint8(2)),this.setUint8(t+3,this._internalBuffer.getUint8(3))}setUint8(t,n){const[r,o]=this._findDataViewWithOffset(t);r.setUint8(t-o,n)}_findDataViewWithOffset(t){let n=0;for(const r of this._dataViews){const o=n+r.byteLength;if(t>=n&&t<o)return[r,n];n=o}throw new RangeError}}const Pi=(e,t,n)=>(r,o,s,i)=>{const a=[],c=new o(s,{mimeType:"audio/webm;codecs=pcm"});let u=null,d=()=>{};const l=h=>{r.dispatchEvent(e("dataavailable",{data:new Blob(h,{type:i})}))},w=async(h,f)=>{const m=await Se(h,f);c.state==="inactive"?a.push(...m):(l(m),u=w(h,f))},g=()=>{c.state!=="inactive"&&(u!==null&&(u.catch(()=>{}),u=null),d(),d=()=>{},c.stop())};return c.addEventListener("error",h=>{g(),r.dispatchEvent(new ErrorEvent("error",{error:h.error}))}),c.addEventListener("pause",()=>r.dispatchEvent(new Event("pause"))),c.addEventListener("resume",()=>r.dispatchEvent(new Event("resume"))),c.addEventListener("start",()=>r.dispatchEvent(new Event("start"))),{get mimeType(){return i},get state(){return c.state},pause(){return c.pause()},resume(){return c.resume()},start(h){const[f]=s.getAudioTracks();if(f!==void 0&&c.state==="inactive"){const{channelCount:m,sampleRate:p}=f.getSettings();if(m===void 0)throw new Error("The channelCount is not defined.");if(p===void 0)throw new Error("The sampleRate is not defined.");let b=!1,_=!1,T=0,E=Jt(i,p);d=()=>{_=!0};const A=en(c,"dataavailable")(({data:y})=>{T+=1,E=E.then(async({dataView:v=null,elementType:N=null,encoderId:I,port:M})=>{const U=await y.arrayBuffer();T-=1;const k=v===null?new Qe([U]):new Qe([...v.buffers,U],v.byteOffset);if(!b&&c.state==="recording"&&!_){const B=n(k,0);if(B===null)return{dataView:k,elementType:N,encoderId:I,port:M};const{value:O}=B;if(O!==172351395)return{dataView:v,elementType:N,encoderId:I,port:M};b=!0}const{currentElementType:D,offset:P,contents:S}=t(k,N,m),L=P<k.byteLength?new Qe(k.buffers,k.byteOffset+P):null;return S.forEach(B=>M.postMessage(B,B.map(({buffer:O})=>O))),T===0&&(c.state==="inactive"||_)&&(Se(I,null).then(B=>{l([...a,...B]),a.length=0,r.dispatchEvent(new Event("stop"))}),M.postMessage([]),M.close(),A()),{dataView:L,elementType:D,encoderId:I,port:M}})});h!==void 0&&E.then(({encoderId:y})=>u=w(y,h))}c.start(100)},stop:g}},Ui=()=>typeof window>"u"?null:window,qn=(e,t)=>{if(t>=e.byteLength)return null;const n=e.getUint8(t);if(n>127)return 1;if(n>63)return 2;if(n>31)return 3;if(n>15)return 4;if(n>7)return 5;if(n>3)return 6;if(n>1)return 7;if(n>0)return 8;const r=qn(e,t+1);return r===null?null:r+8},Bi=(e,t)=>n=>{const r={value:e};return Object.defineProperties(n,{currentTarget:r,target:r}),typeof t=="function"?t.call(e,n):t.handleEvent.call(e,n)},zn=[],Ye=Ui(),Di=pr(Ye),Hn=ir(Di),Wi=xi(Hn,lr,dr,Qt),Tt=_r(qn),Vi=wr(Tt),Fi=vr(Tt),ji=ar(Vi,Fi),$i=Pi(Hn,ji,Tt),Gi=ur(Ye),qi=gr(Ye),ia=hr(mr,Qt,Wi,$i,zn,cr(Gi,Bi),qi),aa=()=>fr(Ye),ca=async e=>{zn.push(await sr(e))};export{ia as MediaRecorder,aa as isSupported,ca as register};
//# sourceMappingURL=module-CibPE7Xd.js.map
