import { c as create_ssr_component, v as validate_component, b as createEventDispatcher, h as add_styles, f as each, d as add_attribute, e as escape } from './ssr-C3HYbsxA.js';
import { m as mt, z as zA, _ as _e, C, e as bt, R as Rt, g as kt, j as Ke, q as j, aj as H$1, H as qt } from './2-DJbI4FWc.js';
import { g as ge } from './ModifyUpload-wYzp15o5.js';
export { default as BaseExample } from './Example12-QUrFCQ6n.js';
import './index-ClteBeTX.js';
import './Component-NmRBwSfF.js';
import 'path';
import 'url';
import 'fs';

const H=l=>{let t=["B","KB","MB","GB","PB"],e=0;for(;l>1024;)l/=1024,e++;let b=t[e];return l.toFixed(1)+"&nbsp;"+b},J={code:'.label-clear-button.svelte-1rvzbk6.svelte-1rvzbk6{color:var(--body-text-color-subdued);position:relative;left:-3px}.label-clear-button.svelte-1rvzbk6.svelte-1rvzbk6:hover{color:var(--body-text-color)}.file-preview.svelte-1rvzbk6.svelte-1rvzbk6{table-layout:fixed;width:var(--size-full);max-height:var(--size-60);overflow-y:auto;margin-top:var(--size-1);color:var(--body-text-color)}.file-preview-holder.svelte-1rvzbk6.svelte-1rvzbk6{overflow:auto}.file.svelte-1rvzbk6.svelte-1rvzbk6{display:flex;width:var(--size-full)}.file.svelte-1rvzbk6>.svelte-1rvzbk6{padding:var(--size-1) var(--size-2-5)}.filename.svelte-1rvzbk6.svelte-1rvzbk6{flex-grow:1;display:flex;overflow:hidden}.filename.svelte-1rvzbk6 .stem.svelte-1rvzbk6{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.filename.svelte-1rvzbk6 .ext.svelte-1rvzbk6{white-space:nowrap}.download.svelte-1rvzbk6.svelte-1rvzbk6{min-width:8rem;width:10%;white-space:nowrap;text-align:right}.download.svelte-1rvzbk6.svelte-1rvzbk6:hover{text-decoration:underline}.download.svelte-1rvzbk6>a{color:var(--link-text-color)}.download.svelte-1rvzbk6>a:hover{color:var(--link-text-color-hover)}.download.svelte-1rvzbk6>a:visited{color:var(--link-text-color-visited)}.download.svelte-1rvzbk6>a:active{color:var(--link-text-color-active)}.selectable.svelte-1rvzbk6.svelte-1rvzbk6{cursor:pointer}tbody.svelte-1rvzbk6>tr.svelte-1rvzbk6:nth-child(even){background:var(--block-background-fill)}tbody.svelte-1rvzbk6>tr.svelte-1rvzbk6:nth-child(odd){background:var(--table-odd-background-fill)}.drag-handle.svelte-1rvzbk6.svelte-1rvzbk6{cursor:grab;color:var(--body-text-color-subdued);padding-right:var(--size-2);user-select:none}.dragging.svelte-1rvzbk6.svelte-1rvzbk6{opacity:0.5;cursor:grabbing}.drop-target.svelte-1rvzbk6.svelte-1rvzbk6{border-top:2px solid var(--color-accent)}tr:last-child.drop-target[data-drop-target="before"].svelte-1rvzbk6.svelte-1rvzbk6{border-top:2px solid var(--color-accent);border-bottom:none}tr:last-child.drop-target[data-drop-target="after"].svelte-1rvzbk6.svelte-1rvzbk6{border-top:none;border-bottom:2px solid var(--color-accent)}',map:'{"version":3,"file":"FilePreview.svelte","sources":["FilePreview.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { prettyBytes } from \\"./utils\\";\\nimport { createEventDispatcher } from \\"svelte\\";\\nimport { DownloadLink } from \\"@gradio/wasm/svelte\\";\\nconst dispatch = createEventDispatcher();\\nexport let value;\\nexport let selectable = false;\\nexport let height = void 0;\\nexport let i18n;\\nexport let allow_reordering = false;\\nlet dragging_index = null;\\nlet drop_target_index = null;\\nfunction handle_drag_start(event, index) {\\n    dragging_index = index;\\n    if (event.dataTransfer) {\\n        event.dataTransfer.effectAllowed = \\"move\\";\\n        event.dataTransfer.setData(\\"text/plain\\", index.toString());\\n    }\\n}\\nfunction handle_drag_over(event, index) {\\n    event.preventDefault();\\n    if (index === normalized_files.length - 1) {\\n        const rect = event.currentTarget.getBoundingClientRect();\\n        const midY = rect.top + rect.height / 2;\\n        drop_target_index = event.clientY > midY ? normalized_files.length : index;\\n    }\\n    else {\\n        drop_target_index = index;\\n    }\\n    if (event.dataTransfer) {\\n        event.dataTransfer.dropEffect = \\"move\\";\\n    }\\n}\\nfunction handle_drag_end(event) {\\n    if (!event.dataTransfer?.dropEffect || event.dataTransfer.dropEffect === \\"none\\") {\\n        dragging_index = null;\\n        drop_target_index = null;\\n    }\\n}\\nfunction handle_drop(event, index) {\\n    event.preventDefault();\\n    if (dragging_index === null || dragging_index === index)\\n        return;\\n    const files = Array.isArray(value) ? [...value] : [value];\\n    const [removed] = files.splice(dragging_index, 1);\\n    files.splice(drop_target_index === normalized_files.length ? normalized_files.length : index, 0, removed);\\n    const new_value = Array.isArray(value) ? files : files[0];\\n    dispatch(\\"change\\", new_value);\\n    dragging_index = null;\\n    drop_target_index = null;\\n}\\nfunction split_filename(filename) {\\n    const last_dot = filename.lastIndexOf(\\".\\");\\n    if (last_dot === -1) {\\n        return [filename, \\"\\"];\\n    }\\n    return [filename.slice(0, last_dot), filename.slice(last_dot)];\\n}\\n$: normalized_files = (Array.isArray(value) ? value : [value]).map((file) => {\\n    const [filename_stem, filename_ext] = split_filename(file.orig_name ?? \\"\\");\\n    return {\\n        ...file,\\n        filename_stem,\\n        filename_ext\\n    };\\n});\\nfunction handle_row_click(event, index) {\\n    const tr = event.currentTarget;\\n    const should_select = event.target === tr || // Only select if the click is on the row itself\\n        tr && tr.firstElementChild && event.composedPath().includes(tr.firstElementChild);\\n    if (should_select) {\\n        dispatch(\\"select\\", { value: normalized_files[index].orig_name, index });\\n    }\\n}\\nfunction remove_file(index) {\\n    const removed = normalized_files.splice(index, 1);\\n    normalized_files = [...normalized_files];\\n    value = normalized_files;\\n    dispatch(\\"delete\\", removed[0]);\\n    dispatch(\\"change\\", normalized_files);\\n}\\nfunction handle_download(file) {\\n    dispatch(\\"download\\", file);\\n}\\nconst is_browser = typeof window !== \\"undefined\\";\\n<\/script>\\n\\n<div\\n\\tclass=\\"file-preview-holder\\"\\n\\tstyle:max-height={height\\n\\t\\t? typeof height === \\"number\\"\\n\\t\\t\\t? height + \\"px\\"\\n\\t\\t\\t: height\\n\\t\\t: \\"auto\\"}\\n>\\n\\t<table class=\\"file-preview\\">\\n\\t\\t<tbody>\\n\\t\\t\\t{#each normalized_files as file, i (file.url)}\\n\\t\\t\\t\\t<tr\\n\\t\\t\\t\\t\\tclass=\\"file\\"\\n\\t\\t\\t\\t\\tclass:selectable\\n\\t\\t\\t\\t\\tclass:dragging={dragging_index === i}\\n\\t\\t\\t\\t\\tclass:drop-target={drop_target_index === i ||\\n\\t\\t\\t\\t\\t\\t(i === normalized_files.length - 1 &&\\n\\t\\t\\t\\t\\t\\t\\tdrop_target_index === normalized_files.length)}\\n\\t\\t\\t\\t\\tdata-drop-target={drop_target_index === normalized_files.length &&\\n\\t\\t\\t\\t\\ti === normalized_files.length - 1\\n\\t\\t\\t\\t\\t\\t? \\"after\\"\\n\\t\\t\\t\\t\\t\\t: drop_target_index === i + 1\\n\\t\\t\\t\\t\\t\\t\\t? \\"after\\"\\n\\t\\t\\t\\t\\t\\t\\t: \\"before\\"}\\n\\t\\t\\t\\t\\tdraggable={allow_reordering && normalized_files.length > 1}\\n\\t\\t\\t\\t\\ton:click={(event) => {\\n\\t\\t\\t\\t\\t\\thandle_row_click(event, i);\\n\\t\\t\\t\\t\\t}}\\n\\t\\t\\t\\t\\ton:dragstart={(event) => handle_drag_start(event, i)}\\n\\t\\t\\t\\t\\ton:dragenter|preventDefault\\n\\t\\t\\t\\t\\ton:dragover={(event) => handle_drag_over(event, i)}\\n\\t\\t\\t\\t\\ton:drop={(event) => handle_drop(event, i)}\\n\\t\\t\\t\\t\\ton:dragend={handle_drag_end}\\n\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t<td class=\\"filename\\" aria-label={file.orig_name}>\\n\\t\\t\\t\\t\\t\\t{#if allow_reordering && normalized_files.length > 1}\\n\\t\\t\\t\\t\\t\\t\\t<span class=\\"drag-handle\\">⋮⋮</span>\\n\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t\\t<span class=\\"stem\\">{file.filename_stem}</span>\\n\\t\\t\\t\\t\\t\\t<span class=\\"ext\\">{file.filename_ext}</span>\\n\\t\\t\\t\\t\\t</td>\\n\\n\\t\\t\\t\\t\\t<td class=\\"download\\">\\n\\t\\t\\t\\t\\t\\t{#if file.url}\\n\\t\\t\\t\\t\\t\\t\\t<DownloadLink\\n\\t\\t\\t\\t\\t\\t\\t\\thref={file.url}\\n\\t\\t\\t\\t\\t\\t\\t\\ton:click={() => handle_download(file)}\\n\\t\\t\\t\\t\\t\\t\\t\\tdownload={is_browser && window.__is_colab__\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t? null\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t: file.orig_name}\\n\\t\\t\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t\\t\\t{@html file.size != null\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t? prettyBytes(file.size)\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t: \\"(size unknown)\\"}&nbsp;&#8675;\\n\\t\\t\\t\\t\\t\\t\\t</DownloadLink>\\n\\t\\t\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t\\t\\t{i18n(\\"file.uploading\\")}\\n\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t</td>\\n\\n\\t\\t\\t\\t\\t{#if normalized_files.length > 1}\\n\\t\\t\\t\\t\\t\\t<td>\\n\\t\\t\\t\\t\\t\\t\\t<button\\n\\t\\t\\t\\t\\t\\t\\t\\tclass=\\"label-clear-button\\"\\n\\t\\t\\t\\t\\t\\t\\t\\taria-label=\\"Remove this file\\"\\n\\t\\t\\t\\t\\t\\t\\t\\ton:click={() => {\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tremove_file(i);\\n\\t\\t\\t\\t\\t\\t\\t\\t}}\\n\\t\\t\\t\\t\\t\\t\\t\\ton:keydown={(event) => {\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tif (event.key === \\"Enter\\") {\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tremove_file(i);\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\t\\t\\t\\t}}\\n\\t\\t\\t\\t\\t\\t\\t\\t>×\\n\\t\\t\\t\\t\\t\\t\\t</button>\\n\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t</tr>\\n\\t\\t\\t{/each}\\n\\t\\t</tbody>\\n\\t</table>\\n</div>\\n\\n<style>\\n\\t.label-clear-button {\\n\\t\\tcolor: var(--body-text-color-subdued);\\n\\t\\tposition: relative;\\n\\t\\tleft: -3px;\\n\\t}\\n\\n\\t.label-clear-button:hover {\\n\\t\\tcolor: var(--body-text-color);\\n\\t}\\n\\n\\t.file-preview {\\n\\t\\ttable-layout: fixed;\\n\\t\\twidth: var(--size-full);\\n\\t\\tmax-height: var(--size-60);\\n\\t\\toverflow-y: auto;\\n\\t\\tmargin-top: var(--size-1);\\n\\t\\tcolor: var(--body-text-color);\\n\\t}\\n\\n\\t.file-preview-holder {\\n\\t\\toverflow: auto;\\n\\t}\\n\\n\\t.file {\\n\\t\\tdisplay: flex;\\n\\t\\twidth: var(--size-full);\\n\\t}\\n\\n\\t.file > * {\\n\\t\\tpadding: var(--size-1) var(--size-2-5);\\n\\t}\\n\\n\\t.filename {\\n\\t\\tflex-grow: 1;\\n\\t\\tdisplay: flex;\\n\\t\\toverflow: hidden;\\n\\t}\\n\\t.filename .stem {\\n\\t\\toverflow: hidden;\\n\\t\\ttext-overflow: ellipsis;\\n\\t\\twhite-space: nowrap;\\n\\t}\\n\\t.filename .ext {\\n\\t\\twhite-space: nowrap;\\n\\t}\\n\\n\\t.download {\\n\\t\\tmin-width: 8rem;\\n\\t\\twidth: 10%;\\n\\t\\twhite-space: nowrap;\\n\\t\\ttext-align: right;\\n\\t}\\n\\t.download:hover {\\n\\t\\ttext-decoration: underline;\\n\\t}\\n\\t.download > :global(a) {\\n\\t\\tcolor: var(--link-text-color);\\n\\t}\\n\\n\\t.download > :global(a:hover) {\\n\\t\\tcolor: var(--link-text-color-hover);\\n\\t}\\n\\t.download > :global(a:visited) {\\n\\t\\tcolor: var(--link-text-color-visited);\\n\\t}\\n\\t.download > :global(a:active) {\\n\\t\\tcolor: var(--link-text-color-active);\\n\\t}\\n\\t.selectable {\\n\\t\\tcursor: pointer;\\n\\t}\\n\\n\\ttbody > tr:nth-child(even) {\\n\\t\\tbackground: var(--block-background-fill);\\n\\t}\\n\\n\\ttbody > tr:nth-child(odd) {\\n\\t\\tbackground: var(--table-odd-background-fill);\\n\\t}\\n\\n\\t.drag-handle {\\n\\t\\tcursor: grab;\\n\\t\\tcolor: var(--body-text-color-subdued);\\n\\t\\tpadding-right: var(--size-2);\\n\\t\\tuser-select: none;\\n\\t}\\n\\n\\t.dragging {\\n\\t\\topacity: 0.5;\\n\\t\\tcursor: grabbing;\\n\\t}\\n\\n\\t.drop-target {\\n\\t\\tborder-top: 2px solid var(--color-accent);\\n\\t}\\n\\n\\ttr:last-child.drop-target[data-drop-target=\\"before\\"] {\\n\\t\\tborder-top: 2px solid var(--color-accent);\\n\\t\\tborder-bottom: none;\\n\\t}\\n\\n\\ttr:last-child.drop-target[data-drop-target=\\"after\\"] {\\n\\t\\tborder-top: none;\\n\\t\\tborder-bottom: 2px solid var(--color-accent);\\n\\t}</style>\\n"],"names":[],"mappings":"AA0KC,iDAAoB,CACnB,KAAK,CAAE,IAAI,yBAAyB,CAAC,CACrC,QAAQ,CAAE,QAAQ,CAClB,IAAI,CAAE,IACP,CAEA,iDAAmB,MAAO,CACzB,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CAEA,2CAAc,CACb,YAAY,CAAE,KAAK,CACnB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,UAAU,CAAE,IAAI,SAAS,CAAC,CAC1B,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,IAAI,QAAQ,CAAC,CACzB,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CAEA,kDAAqB,CACpB,QAAQ,CAAE,IACX,CAEA,mCAAM,CACL,OAAO,CAAE,IAAI,CACb,KAAK,CAAE,IAAI,WAAW,CACvB,CAEA,oBAAK,CAAG,eAAE,CACT,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,UAAU,CACtC,CAEA,uCAAU,CACT,SAAS,CAAE,CAAC,CACZ,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,MACX,CACA,wBAAS,CAAC,oBAAM,CACf,QAAQ,CAAE,MAAM,CAChB,aAAa,CAAE,QAAQ,CACvB,WAAW,CAAE,MACd,CACA,wBAAS,CAAC,mBAAK,CACd,WAAW,CAAE,MACd,CAEA,uCAAU,CACT,SAAS,CAAE,IAAI,CACf,KAAK,CAAE,GAAG,CACV,WAAW,CAAE,MAAM,CACnB,UAAU,CAAE,KACb,CACA,uCAAS,MAAO,CACf,eAAe,CAAE,SAClB,CACA,wBAAS,CAAW,CAAG,CACtB,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CAEA,wBAAS,CAAW,OAAS,CAC5B,KAAK,CAAE,IAAI,uBAAuB,CACnC,CACA,wBAAS,CAAW,SAAW,CAC9B,KAAK,CAAE,IAAI,yBAAyB,CACrC,CACA,wBAAS,CAAW,QAAU,CAC7B,KAAK,CAAE,IAAI,wBAAwB,CACpC,CACA,yCAAY,CACX,MAAM,CAAE,OACT,CAEA,oBAAK,CAAG,iBAAE,WAAW,IAAI,CAAE,CAC1B,UAAU,CAAE,IAAI,uBAAuB,CACxC,CAEA,oBAAK,CAAG,iBAAE,WAAW,GAAG,CAAE,CACzB,UAAU,CAAE,IAAI,2BAA2B,CAC5C,CAEA,0CAAa,CACZ,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IAAI,yBAAyB,CAAC,CACrC,aAAa,CAAE,IAAI,QAAQ,CAAC,CAC5B,WAAW,CAAE,IACd,CAEA,uCAAU,CACT,OAAO,CAAE,GAAG,CACZ,MAAM,CAAE,QACT,CAEA,0CAAa,CACZ,UAAU,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,cAAc,CACzC,CAEA,EAAE,WAAW,YAAY,CAAC,gBAAgB,CAAC,QAAQ,+BAAE,CACpD,UAAU,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,cAAc,CAAC,CACzC,aAAa,CAAE,IAChB,CAEA,EAAE,WAAW,YAAY,CAAC,gBAAgB,CAAC,OAAO,+BAAE,CACnD,UAAU,CAAE,IAAI,CAChB,aAAa,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,cAAc,CAC5C"}'};function q(l){const t=l.lastIndexOf(".");return t===-1?[l,""]:[l.slice(0,t),l.slice(t)]}const V=create_ssr_component((l,t,e,b)=>{let a;createEventDispatcher();let{value:r}=t,{selectable:c=!1}=t,{height:n=void 0}=t,{i18n:d}=t,{allow_reordering:v=!1}=t,_=null,s=null;const C$1=typeof window<"u";return t.value===void 0&&e.value&&r!==void 0&&e.value(r),t.selectable===void 0&&e.selectable&&c!==void 0&&e.selectable(c),t.height===void 0&&e.height&&n!==void 0&&e.height(n),t.i18n===void 0&&e.i18n&&d!==void 0&&e.i18n(d),t.allow_reordering===void 0&&e.allow_reordering&&v!==void 0&&e.allow_reordering(v),l.css.add(J),a=(Array.isArray(r)?r:[r]).map(o=>{const[i,h]=q(o.orig_name??"");return {...o,filename_stem:i,filename_ext:h}}),`<div class="file-preview-holder svelte-1rvzbk6"${add_styles({"max-height":n?typeof n=="number"?n+"px":n:"auto"})}><table class="file-preview svelte-1rvzbk6"><tbody class="svelte-1rvzbk6">${each(a,(o,i)=>`<tr class="${["file svelte-1rvzbk6",(c?"selectable":"")+" "+(_===i?"dragging":"")+" "+(s===i||i===a.length-1&&s===a.length?"drop-target":"")].join(" ").trim()}"${add_attribute("data-drop-target",s===a.length&&i===a.length-1||s===i+1?"after":"before",0)}${add_attribute("draggable",v&&a.length>1,0)}><td class="filename svelte-1rvzbk6"${add_attribute("aria-label",o.orig_name,0)}>${v&&a.length>1?'<span class="drag-handle svelte-1rvzbk6" data-svelte-h="svelte-1u4up0a">⋮⋮</span>':""} <span class="stem svelte-1rvzbk6">${escape(o.filename_stem)}</span> <span class="ext svelte-1rvzbk6">${escape(o.filename_ext)}</span></td> <td class="download svelte-1rvzbk6">${o.url?`${validate_component(C,"DownloadLink").$$render(l,{href:o.url,download:C$1&&window.__is_colab__?null:o.orig_name},{},{default:()=>`<!-- HTML_TAG_START -->${o.size!=null?H(o.size):"(size unknown)"}<!-- HTML_TAG_END --> ⇣
							`})}`:`${escape(d("file.uploading"))}`}</td> ${a.length>1?'<td class="svelte-1rvzbk6"><button class="label-clear-button svelte-1rvzbk6" aria-label="Remove this file" data-svelte-h="svelte-nhtord">×</button> </td>':""} </tr>`)}</tbody></table> </div>`}),O=V,$=create_ssr_component((l,t,e,b)=>{let{value:a=null}=t,{label:r}=t,{show_label:c=!0}=t,{selectable:n=!1}=t,{height:d=void 0}=t,{i18n:v}=t;return t.value===void 0&&e.value&&a!==void 0&&e.value(a),t.label===void 0&&e.label&&r!==void 0&&e.label(r),t.show_label===void 0&&e.show_label&&c!==void 0&&e.show_label(c),t.selectable===void 0&&e.selectable&&n!==void 0&&e.selectable(n),t.height===void 0&&e.height&&d!==void 0&&e.height(d),t.i18n===void 0&&e.i18n&&v!==void 0&&e.i18n(v),`${validate_component(bt,"BlockLabel").$$render(l,{show_label:c,float:a===null,Icon:Rt,label:r||"File"},{},{})} ${a&&(!Array.isArray(a)||a.length>0)?`${validate_component(O,"FilePreview").$$render(l,{i18n:v,selectable:n,value:a,height:d},{},{})}`:`${validate_component(kt,"Empty").$$render(l,{unpadded_box:!0,size:"large"},{},{default:()=>`${validate_component(Rt,"File").$$render(l,{},{},{})}`})}`}`}),tt=$,et=create_ssr_component((l,t,e,b)=>{let{value:a}=t,{label:r}=t,{show_label:c=!0}=t,{file_count:n="single"}=t,{file_types:d=null}=t,{selectable:v=!1}=t,{root:_}=t,{height:s=void 0}=t,{i18n:C}=t,{max_file_size:o=null}=t,{upload:i}=t,{stream_handler:h}=t,{uploading:m=!1}=t,{allow_reordering:x=!1}=t;const f=createEventDispatcher();let w=!1;t.value===void 0&&e.value&&a!==void 0&&e.value(a),t.label===void 0&&e.label&&r!==void 0&&e.label(r),t.show_label===void 0&&e.show_label&&c!==void 0&&e.show_label(c),t.file_count===void 0&&e.file_count&&n!==void 0&&e.file_count(n),t.file_types===void 0&&e.file_types&&d!==void 0&&e.file_types(d),t.selectable===void 0&&e.selectable&&v!==void 0&&e.selectable(v),t.root===void 0&&e.root&&_!==void 0&&e.root(_),t.height===void 0&&e.height&&s!==void 0&&e.height(s),t.i18n===void 0&&e.i18n&&C!==void 0&&e.i18n(C),t.max_file_size===void 0&&e.max_file_size&&o!==void 0&&e.max_file_size(o),t.upload===void 0&&e.upload&&i!==void 0&&e.upload(i),t.stream_handler===void 0&&e.stream_handler&&h!==void 0&&e.stream_handler(h),t.uploading===void 0&&e.uploading&&m!==void 0&&e.uploading(m),t.allow_reordering===void 0&&e.allow_reordering&&x!==void 0&&e.allow_reordering(x);let g,z,k=l.head;do g=!0,l.head=k,f("drag",w),z=`${validate_component(bt,"BlockLabel").$$render(l,{show_label:c,Icon:Rt,float:!a,label:r||"File"},{},{})} ${a&&(!Array.isArray(a)||a.length>0)?`${validate_component(Ke,"IconButtonWrapper").$$render(l,{},{},{default:()=>`${n==="single"&&(Array.isArray(a)?a.length>0:a!==null)?"":`${validate_component(j,"IconButton").$$render(l,{Icon:H$1,label:C("common.upload")},{},{default:()=>`${validate_component(ge,"Upload").$$render(l,{icon_upload:!0,filetype:d,file_count:n,max_file_size:o,root:_,stream_handler:h,upload:i,dragging:w,uploading:m},{dragging:u=>{w=u,g=!1;},uploading:u=>{m=u,g=!1;}},{})}`})}`} ${validate_component(j,"IconButton").$$render(l,{Icon:qt,label:C("common.clear")},{},{})}`})} ${validate_component(O,"FilePreview").$$render(l,{i18n:C,selectable:v,value:a,height:s,allow_reordering:x},{},{})}`:`${validate_component(ge,"Upload").$$render(l,{filetype:d,file_count:n,max_file_size:o,root:_,stream_handler:h,upload:i,height:s,dragging:w,uploading:m},{dragging:u=>{w=u,g=!1;},uploading:u=>{m=u,g=!1;}},{default:()=>`${b.default?b.default({}):""}`})}`}`;while(!g);return z}),lt=et,dt=create_ssr_component((l,t,e,b)=>{let{elem_id:a=""}=t,{elem_classes:r=[]}=t,{visible:c=!0}=t,{value:n}=t,{interactive:d}=t,{root:v}=t,{label:_}=t,{show_label:s}=t,{height:C=void 0}=t,{_selectable:o=!1}=t,{loading_status:i}=t,{container:h=!0}=t,{scale:m=null}=t,{min_width:x=void 0}=t,{gradio:f}=t,{file_count:w}=t,{file_types:g=["file"]}=t,{input_ready:z}=t,{allow_reordering:k=!1}=t,u=!1,U=n;t.elem_id===void 0&&e.elem_id&&a!==void 0&&e.elem_id(a),t.elem_classes===void 0&&e.elem_classes&&r!==void 0&&e.elem_classes(r),t.visible===void 0&&e.visible&&c!==void 0&&e.visible(c),t.value===void 0&&e.value&&n!==void 0&&e.value(n),t.interactive===void 0&&e.interactive&&d!==void 0&&e.interactive(d),t.root===void 0&&e.root&&v!==void 0&&e.root(v),t.label===void 0&&e.label&&_!==void 0&&e.label(_),t.show_label===void 0&&e.show_label&&s!==void 0&&e.show_label(s),t.height===void 0&&e.height&&C!==void 0&&e.height(C),t._selectable===void 0&&e._selectable&&o!==void 0&&e._selectable(o),t.loading_status===void 0&&e.loading_status&&i!==void 0&&e.loading_status(i),t.container===void 0&&e.container&&h!==void 0&&e.container(h),t.scale===void 0&&e.scale&&m!==void 0&&e.scale(m),t.min_width===void 0&&e.min_width&&x!==void 0&&e.min_width(x),t.gradio===void 0&&e.gradio&&f!==void 0&&e.gradio(f),t.file_count===void 0&&e.file_count&&w!==void 0&&e.file_count(w),t.file_types===void 0&&e.file_types&&g!==void 0&&e.file_types(g),t.input_ready===void 0&&e.input_ready&&z!==void 0&&e.input_ready(z),t.allow_reordering===void 0&&e.allow_reordering&&k!==void 0&&e.allow_reordering(k);let y,W,D=l.head;do y=!0,l.head=D,z=!u,JSON.stringify(U)!==JSON.stringify(n)&&(f.dispatch("change"),U=n),W=`   ${validate_component(mt,"Block").$$render(l,{visible:c,variant:n?"solid":"dashed",border_mode:"base",padding:!1,elem_id:a,elem_classes:r,container:h,scale:m,min_width:x,allow_overflow:!1},{},{default:()=>`${validate_component(zA,"StatusTracker").$$render(l,Object.assign({},{autoscroll:f.autoscroll},{i18n:f.i18n},i,{status:i?.status||"complete"}),{},{})} ${d?`${validate_component(lt,"FileUpload").$$render(l,{upload:(...B)=>f.client.upload(...B),stream_handler:(...B)=>f.client.stream(...B),label:_,show_label:s,value:n,file_count:w,file_types:g,selectable:o,root:v,height:C,allow_reordering:k,max_file_size:f.max_file_size,i18n:f.i18n,uploading:u},{uploading:B=>{u=B,y=!1;}},{default:()=>`${validate_component(_e,"UploadText").$$render(l,{i18n:f.i18n,type:"file"},{},{})}`})}`:`${validate_component(tt,"File").$$render(l,{selectable:o,value:n,label:_,show_label:s,height:C,i18n:f.i18n},{},{})}`}`})}`;while(!y);return W});

export { tt as BaseFile, lt as BaseFileUpload, O as FilePreview, dt as default };
//# sourceMappingURL=Index10-CbfXzI2l.js.map
