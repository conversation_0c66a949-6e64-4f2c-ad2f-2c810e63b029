import{R as t}from"./index.BoI39RQH.js";import{b as l}from"./KHR_interactivity.DEAVS2UW.js";import{b as r}from"./declarationMapper.UBCwU7BT.js";class s extends l{constructor(e){super(e),this.delayIndex=this.registerDataInput("delayIndex",r)}_execute(e,o){const a=this.delayIndex.getValue(e);if(a<=0||isNaN(a)||!isFinite(a))return this._reportError(e,"Invalid delay index");const i=e._getExecutionVariable(this,"pendingDelays",[])[a];i&&i.dispose(),this.out._activateSignal(e)}getClassName(){return"FlowGraphCancelDelayBlock"}}t("FlowGraphCancelDelayBlock",s);export{s as FlowGraphCancelDelayBlock};
//# sourceMappingURL=flowGraphCancelDelayBlock.Bo445-z6.js.map
