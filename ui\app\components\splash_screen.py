"""
美化的启动画面
显示加载进度和系统信息
"""

import sys
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QProgressBar, QApplication)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QFont, QPainter, QLinearGradient, QColor, QPen, QPixmap

class SplashScreen(QWidget):
    """美化的启动画面"""
    
    finished = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.progress = 0
        self.init_ui()
        self.setup_animations()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setFixedSize(500, 300)
        
        # 居中显示
        screen = QApplication.primaryScreen().geometry()
        self.move(
            (screen.width() - self.width()) // 2,
            (screen.height() - self.height()) // 2
        )
        
        # 主布局
        layout = QVBoxLayout()
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(20)
        
        # 标题
        self.title_label = QLabel("🌟 Reverie Agents")
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.title_label.setFont(QFont("Microsoft YaHei UI", 24, QFont.Weight.Bold))
        self.title_label.setStyleSheet("""
            QLabel {
                color: #58a6ff;
                background: transparent;
                padding: 10px;
            }
        """)
        
        # 副标题
        self.subtitle_label = QLabel("AI陪伴对话系统")
        self.subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.subtitle_label.setFont(QFont("Microsoft YaHei UI", 14))
        self.subtitle_label.setStyleSheet("""
            QLabel {
                color: #8b949e;
                background: transparent;
            }
        """)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setFixedHeight(6)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                background-color: #21262d;
                border-radius: 3px;
                border: none;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #58a6ff, stop:1 #1f6feb);
                border-radius: 3px;
            }
        """)
        
        # 状态标签
        self.status_label = QLabel("正在初始化...")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setFont(QFont("Microsoft YaHei UI", 12))
        self.status_label.setStyleSheet("""
            QLabel {
                color: #f0f6fc;
                background: transparent;
            }
        """)
        
        # 版本信息
        self.version_label = QLabel("v1.0.0")
        self.version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.version_label.setFont(QFont("Microsoft YaHei UI", 10))
        self.version_label.setStyleSheet("""
            QLabel {
                color: #484f58;
                background: transparent;
            }
        """)
        
        layout.addWidget(self.title_label)
        layout.addWidget(self.subtitle_label)
        layout.addStretch()
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.status_label)
        layout.addStretch()
        layout.addWidget(self.version_label)
        
        self.setLayout(layout)

        # 设置现代化背景样式
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0a0e1a, stop:0.3 #1a1f2e, stop:0.7 #2d3548, stop:1 #667eea);
                border-radius: 25px;
                border: 2px solid rgba(102, 126, 234, 0.3);
            }
        """)
        
    def setup_animations(self):
        """设置动画"""
        # 标题淡入动画
        self.title_animation = QPropertyAnimation(self.title_label, b"windowOpacity")
        self.title_animation.setDuration(1000)
        self.title_animation.setStartValue(0.0)
        self.title_animation.setEndValue(1.0)
        self.title_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
    def paintEvent(self, event):
        """绘制背景"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 创建渐变背景
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor("#0d1117"))
        gradient.setColorAt(1, QColor("#161b22"))
        
        # 绘制圆角矩形背景
        painter.setBrush(gradient)
        painter.setPen(QPen(QColor("#30363d"), 2))
        painter.drawRoundedRect(self.rect().adjusted(1, 1, -1, -1), 15, 15)
        
    def start_loading(self):
        """开始加载动画"""
        self.show()
        self.title_animation.start()
        
        # 模拟加载过程
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_progress)
        self.timer.start(50)  # 每50ms更新一次
        
    def update_progress(self):
        """更新进度"""
        self.progress += 2
        self.progress_bar.setValue(self.progress)
        
        # 更新状态文本
        if self.progress < 20:
            self.status_label.setText("正在加载配置...")
        elif self.progress < 40:
            self.status_label.setText("正在初始化AI引擎...")
        elif self.progress < 60:
            self.status_label.setText("正在加载人设系统...")
        elif self.progress < 80:
            self.status_label.setText("正在连接记忆系统...")
        elif self.progress < 100:
            self.status_label.setText("正在启动界面...")
        else:
            self.status_label.setText("启动完成！")
            self.timer.stop()
            QTimer.singleShot(500, self.finish_loading)
            
    def finish_loading(self):
        """完成加载"""
        self.finished.emit()
        self.close()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    splash = SplashScreen()
    splash.start_loading()
    sys.exit(app.exec())
