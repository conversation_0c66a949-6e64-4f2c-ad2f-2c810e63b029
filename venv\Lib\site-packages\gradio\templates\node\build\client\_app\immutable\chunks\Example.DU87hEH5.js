import{SvelteComponent as g,init as d,safe_not_equal as _,element as h,create_component as p,claim_element as v,children as b,claim_component as y,detach as f,attr as k,toggle_class as i,insert_hydration as E,mount_component as I,transition_in as j,transition_out as q,destroy_component as w}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{r as C}from"./2.B2AoQPnG.js";import"./ImageUploader.Dvb2Mtrn.js";/* empty css                                              */function D(r){var o,s;let t,l,n;return l=new C({props:{src:((o=r[0].composite)==null?void 0:o.url)||((s=r[0].background)==null?void 0:s.url),alt:""}}),{c(){t=h("div"),p(l.$$.fragment),this.h()},l(e){t=v(e,"DIV",{class:!0});var a=b(t);y(l.$$.fragment,a),a.forEach(f),this.h()},h(){k(t,"class","container svelte-jhlhb0"),i(t,"table",r[1]==="table"),i(t,"gallery",r[1]==="gallery"),i(t,"selected",r[2])},m(e,a){E(e,t,a),I(l,t,null),n=!0},p(e,[a]){var m,u;const c={};a&1&&(c.src=((m=e[0].composite)==null?void 0:m.url)||((u=e[0].background)==null?void 0:u.url)),l.$set(c),(!n||a&2)&&i(t,"table",e[1]==="table"),(!n||a&2)&&i(t,"gallery",e[1]==="gallery"),(!n||a&4)&&i(t,"selected",e[2])},i(e){n||(j(l.$$.fragment,e),n=!0)},o(e){q(l.$$.fragment,e),n=!1},d(e){e&&f(t),w(l)}}}function S(r,t,l){let{value:n}=t,{type:o}=t,{selected:s=!1}=t;return r.$$set=e=>{"value"in e&&l(0,n=e.value),"type"in e&&l(1,o=e.type),"selected"in e&&l(2,s=e.selected)},[n,o,s]}class G extends g{constructor(t){super(),d(this,t,S,D,_,{value:0,type:1,selected:2})}}export{G as default};
//# sourceMappingURL=Example.DU87hEH5.js.map
