import{B as Se}from"./Block-CJdXVpa7.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";/* empty css                                                        */import"./index-B7J2Z2jS.js";/* empty css                                              */import"./StreamingBar.svelte_svelte_type_style_lang-DyfAPhwM.js";import Ye from"./Example-BdAjEacD.js";import"./prism-python-MMh3z1bK.js";import"./svelte/svelte.js";/* empty css                                              */const{SvelteComponent:De,append:z,assign:Q,attr:d,check_outros:E,construct_svelte_component:T,create_component:Y,destroy_component:D,destroy_each:J,detach:q,element:B,empty:K,ensure_array_like:M,flush:v,get_spread_object:U,get_spread_update:V,group_outros:P,init:Fe,insert:N,listen:R,mount_component:F,noop:Ge,null_to_empty:ie,run_all:Ne,safe_not_equal:Je,set_data:le,set_style:oe,space:I,svg_element:re,text:W,toggle_class:fe,transition_in:b,transition_out:k}=window.__gradio__svelte__internal;function _e(s,e,l){const t=s.slice();return t[39]=e[l],t}function ce(s,e,l){const t=s.slice();return t[42]=e[l],t[44]=l,t}function ue(s,e,l){const t=s.slice();t[0]=e[l].value,t[46]=e[l].component,t[49]=l;const n=t[1][t[49]];return t[47]=n,t}function ae(s,e,l){const t=s.slice();return t[50]=e[l],t}function me(s,e,l){const t=s.slice();return t[42]=e[l],t[44]=l,t}function he(s){let e,l,t,n,o;return{c(){e=B("div"),l=re("svg"),t=re("path"),n=I(),o=W(s[4]),d(t,"fill","currentColor"),d(t,"d","M10 6h18v2H10zm0 18h18v2H10zm0-9h18v2H10zm-6 0h2v2H4zm0-9h2v2H4zm0 18h2v2H4z"),d(l,"xmlns","http://www.w3.org/2000/svg"),d(l,"xmlns:xlink","http://www.w3.org/1999/xlink"),d(l,"aria-hidden","true"),d(l,"role","img"),d(l,"width","1em"),d(l,"height","1em"),d(l,"preserveAspectRatio","xMidYMid meet"),d(l,"viewBox","0 0 32 32"),d(l,"class","svelte-p5q82i"),d(e,"class","label svelte-p5q82i")},m(f,i){N(f,e,i),z(e,l),z(l,t),z(e,n),z(e,o)},p(f,i){i[0]&16&&le(o,f[4])},d(f){f&&q(e)}}}function Ke(s){let e,l,t,n,o,f,i,r=M(s[6]),_=[];for(let c=0;c<r.length;c+=1)_[c]=ge(ae(s,r,c));let m=M(s[21]),u=[];for(let c=0;c<m.length;c+=1)u[c]=be(ce(s,m,c));const h=c=>k(u[c],1,1,()=>{u[c]=null});return{c(){e=B("div"),l=B("table"),t=B("thead"),n=B("tr");for(let c=0;c<_.length;c+=1)_[c].c();o=I(),f=B("tbody");for(let c=0;c<u.length;c+=1)u[c].c();d(n,"class","tr-head svelte-p5q82i"),d(l,"tabindex","0"),d(l,"role","grid"),d(l,"class","svelte-p5q82i"),d(e,"class","table-wrap svelte-p5q82i")},m(c,p){N(c,e,p),z(e,l),z(l,t),z(t,n);for(let g=0;g<_.length;g+=1)_[g]&&_[g].m(n,null);z(l,o),z(l,f);for(let g=0;g<u.length;g+=1)u[g]&&u[g].m(f,null);i=!0},p(c,p){if(p[0]&64){r=M(c[6]);let g;for(g=0;g<r.length;g+=1){const w=ae(c,r,g);_[g]?_[g].p(w,p):(_[g]=ge(w),_[g].c(),_[g].m(n,null))}for(;g<_.length;g+=1)_[g].d(1);_.length=r.length}if(p[0]&62232591){m=M(c[21]);let g;for(g=0;g<m.length;g+=1){const w=ce(c,m,g);u[g]?(u[g].p(w,p),b(u[g],1)):(u[g]=be(w),u[g].c(),b(u[g],1),u[g].m(f,null))}for(P(),g=m.length;g<u.length;g+=1)h(g);E()}},i(c){if(!i){for(let p=0;p<m.length;p+=1)b(u[p]);i=!0}},o(c){u=u.filter(Boolean);for(let p=0;p<u.length;p+=1)k(u[p]);i=!1},d(c){c&&q(e),J(_,c),J(u,c)}}}function Le(s){let e,l,t=M(s[18]),n=[];for(let f=0;f<t.length;f+=1)n[f]=ve(me(s,t,f));const o=f=>k(n[f],1,1,()=>{n[f]=null});return{c(){e=B("div");for(let f=0;f<n.length;f+=1)n[f].c();d(e,"class","gallery svelte-p5q82i")},m(f,i){N(f,e,i);for(let r=0;r<n.length;r+=1)n[r]&&n[r].m(e,null);l=!0},p(f,i){if(i[0]&62232719){t=M(f[18]);let r;for(r=0;r<t.length;r+=1){const _=me(f,t,r);n[r]?(n[r].p(_,i),b(n[r],1)):(n[r]=ve(_),n[r].c(),b(n[r],1),n[r].m(e,null))}for(P(),r=t.length;r<n.length;r+=1)o(r);E()}},i(f){if(!l){for(let i=0;i<t.length;i+=1)b(n[i]);l=!0}},o(f){n=n.filter(Boolean);for(let i=0;i<n.length;i+=1)k(n[i]);l=!1},d(f){f&&q(e),J(n,f)}}}function ge(s){let e,l=s[50]+"",t,n;return{c(){e=B("th"),t=W(l),n=I(),d(e,"class","svelte-p5q82i")},m(o,f){N(o,e,f),z(e,t),z(e,n)},p(o,f){f[0]&64&&l!==(l=o[50]+"")&&le(t,l)},d(o){o&&q(e)}}}function pe(s){let e,l,t,n;const o=[s[2][s[49]],{value:s[0]},{samples_dir:s[23]},{type:"table"},{selected:s[20]===s[44]},{index:s[44]},{root:s[11]}];var f=s[46];function i(r,_){let m={};for(let u=0;u<o.length;u+=1)m=Q(m,o[u]);return _!==void 0&&_[0]&11536388&&(m=Q(m,V(o,[_[0]&4&&U(r[2][r[49]]),_[0]&2097152&&{value:r[0]},_[0]&8388608&&{samples_dir:r[23]},o[3],_[0]&1048576&&{selected:r[20]===r[44]},o[5],_[0]&2048&&{root:r[11]}]))),{props:m}}return f&&(l=T(f,i(s))),{c(){e=B("td"),l&&Y(l.$$.fragment),oe(e,"max-width",s[47]==="textbox"?"35ch":"auto"),d(e,"class",t=ie(s[47])+" svelte-p5q82i")},m(r,_){N(r,e,_),l&&F(l,e,null),n=!0},p(r,_){if(_[0]&2097152&&f!==(f=r[46])){if(l){P();const m=l;k(m.$$.fragment,1,0,()=>{D(m,1)}),E()}f?(l=T(f,i(r,_)),Y(l.$$.fragment),b(l.$$.fragment,1),F(l,e,null)):l=null}else if(f){const m=_[0]&11536388?V(o,[_[0]&4&&U(r[2][r[49]]),_[0]&2097152&&{value:r[0]},_[0]&8388608&&{samples_dir:r[23]},o[3],_[0]&1048576&&{selected:r[20]===r[44]},o[5],_[0]&2048&&{root:r[11]}]):{};l.$set(m)}(!n||_[0]&2)&&oe(e,"max-width",r[47]==="textbox"?"35ch":"auto"),(!n||_[0]&2&&t!==(t=ie(r[47])+" svelte-p5q82i"))&&d(e,"class",t)},i(r){n||(l&&b(l.$$.fragment,r),n=!0)},o(r){l&&k(l.$$.fragment,r),n=!1},d(r){r&&q(e),l&&D(l)}}}function de(s){let e=s[47]!==void 0&&s[3].get(s[47])!==void 0,l,t,n=e&&pe(s);return{c(){n&&n.c(),l=K()},m(o,f){n&&n.m(o,f),N(o,l,f),t=!0},p(o,f){f[0]&10&&(e=o[47]!==void 0&&o[3].get(o[47])!==void 0),e?n?(n.p(o,f),f[0]&10&&b(n,1)):(n=pe(o),n.c(),b(n,1),n.m(l.parentNode,l)):n&&(P(),k(n,1,1,()=>{n=null}),E())},i(o){t||(b(n),t=!0)},o(o){k(n),t=!1},d(o){o&&q(l),n&&n.d(o)}}}function be(s){let e,l,t,n,o,f=M(s[42]),i=[];for(let u=0;u<f.length;u+=1)i[u]=de(ue(s,f,u));const r=u=>k(i[u],1,1,()=>{i[u]=null});function _(){return s[34](s[44])}function m(){return s[35](s[44])}return{c(){e=B("tr");for(let u=0;u<i.length;u+=1)i[u].c();l=I(),d(e,"class","tr-body svelte-p5q82i")},m(u,h){N(u,e,h);for(let c=0;c<i.length;c+=1)i[c]&&i[c].m(e,null);z(e,l),t=!0,n||(o=[R(e,"click",_),R(e,"mouseenter",m),R(e,"mouseleave",s[36])],n=!0)},p(u,h){if(s=u,h[0]&11536398){f=M(s[42]);let c;for(c=0;c<f.length;c+=1){const p=ue(s,f,c);i[c]?(i[c].p(p,h),b(i[c],1)):(i[c]=de(p),i[c].c(),b(i[c],1),i[c].m(e,l))}for(P(),c=f.length;c<i.length;c+=1)r(c);E()}},i(u){if(!t){for(let h=0;h<f.length;h+=1)b(i[h]);t=!0}},o(u){i=i.filter(Boolean);for(let h=0;h<i.length;h+=1)k(i[h]);t=!1},d(u){u&&q(e),J(i,u),n=!1,Ne(o)}}}function ke(s){let e,l,t,n,o,f,i,r;const _=[Qe,Oe],m=[];function u(p,g){return g[0]&2097162&&(l=null),p[7]?0:(l==null&&(l=!!(p[21].length&&p[3].get(p[1][0]))),l?1:-1)}~(t=u(s,[-1,-1]))&&(n=m[t]=_[t](s));function h(){return s[31](s[44],s[42])}function c(){return s[32](s[44])}return{c(){e=B("button"),n&&n.c(),o=I(),d(e,"class","gallery-item svelte-p5q82i")},m(p,g){N(p,e,g),~t&&m[t].m(e,null),z(e,o),f=!0,i||(r=[R(e,"click",h),R(e,"mouseenter",c),R(e,"mouseleave",s[33])],i=!0)},p(p,g){s=p;let w=t;t=u(s,g),t===w?~t&&m[t].p(s,g):(n&&(P(),k(m[w],1,1,()=>{m[w]=null}),E()),~t?(n=m[t],n?n.p(s,g):(n=m[t]=_[t](s),n.c()),b(n,1),n.m(e,o)):n=null)},i(p){f||(b(n),f=!0)},o(p){k(n),f=!1},d(p){p&&q(e),~t&&m[t].d(),i=!1,Ne(r)}}}function Oe(s){let e,l,t;const n=[s[2][0],{value:s[42][0]},{samples_dir:s[23]},{type:"gallery"},{selected:s[20]===s[44]},{index:s[44]},{root:s[11]}];var o=s[21][0][0].component;function f(i,r){let _={};for(let m=0;m<n.length;m+=1)_=Q(_,n[m]);return r!==void 0&&r[0]&9701380&&(_=Q(_,V(n,[r[0]&4&&U(i[2][0]),r[0]&262144&&{value:i[42][0]},r[0]&8388608&&{samples_dir:i[23]},n[3],r[0]&1048576&&{selected:i[20]===i[44]},n[5],r[0]&2048&&{root:i[11]}]))),{props:_}}return o&&(e=T(o,f(s))),{c(){e&&Y(e.$$.fragment),l=K()},m(i,r){e&&F(e,i,r),N(i,l,r),t=!0},p(i,r){if(r[0]&2097152&&o!==(o=i[21][0][0].component)){if(e){P();const _=e;k(_.$$.fragment,1,0,()=>{D(_,1)}),E()}o?(e=T(o,f(i,r)),Y(e.$$.fragment),b(e.$$.fragment,1),F(e,l.parentNode,l)):e=null}else if(o){const _=r[0]&9701380?V(n,[r[0]&4&&U(i[2][0]),r[0]&262144&&{value:i[42][0]},r[0]&8388608&&{samples_dir:i[23]},n[3],r[0]&1048576&&{selected:i[20]===i[44]},n[5],r[0]&2048&&{root:i[11]}]):{};e.$set(_)}},i(i){t||(e&&b(e.$$.fragment,i),t=!0)},o(i){e&&k(e.$$.fragment,i),t=!1},d(i){i&&q(l),e&&D(e,i)}}}function Qe(s){let e,l;return e=new Ye({props:{value:s[42][0],selected:s[20]===s[44],type:"gallery"}}),{c(){Y(e.$$.fragment)},m(t,n){F(e,t,n),l=!0},p(t,n){const o={};n[0]&262144&&(o.value=t[42][0]),n[0]&1048576&&(o.selected=t[20]===t[44]),e.$set(o)},i(t){l||(b(e.$$.fragment,t),l=!0)},o(t){k(e.$$.fragment,t),l=!1},d(t){D(e,t)}}}function ve(s){let e,l,t=s[42][0]!=null&&ke(s);return{c(){t&&t.c(),e=K()},m(n,o){t&&t.m(n,o),N(n,e,o),l=!0},p(n,o){n[42][0]!=null?t?(t.p(n,o),o[0]&262144&&b(t,1)):(t=ke(n),t.c(),b(t,1),t.m(e.parentNode,e)):t&&(P(),k(t,1,1,()=>{t=null}),E())},i(n){l||(b(t),l=!0)},o(n){k(t),l=!1},d(n){n&&q(e),t&&t.d(n)}}}function we(s){let e,l,t=M(s[19]),n=[];for(let o=0;o<t.length;o+=1)n[o]=qe(_e(s,t,o));return{c(){e=B("div"),l=W(`Pages:
			`);for(let o=0;o<n.length;o+=1)n[o].c();d(e,"class","paginate svelte-p5q82i")},m(o,f){N(o,e,f),z(e,l);for(let i=0;i<n.length;i+=1)n[i]&&n[i].m(e,null)},p(o,f){if(f[0]&589824){t=M(o[19]);let i;for(i=0;i<t.length;i+=1){const r=_e(o,t,i);n[i]?n[i].p(r,f):(n[i]=qe(r),n[i].c(),n[i].m(e,null))}for(;i<n.length;i+=1)n[i].d(1);n.length=t.length}},d(o){o&&q(e),J(n,o)}}}function Te(s){let e,l=s[39]+1+"",t,n,o,f;function i(){return s[37](s[39])}return{c(){e=B("button"),t=W(l),n=I(),d(e,"class","svelte-p5q82i"),fe(e,"current-page",s[16]===s[39])},m(r,_){N(r,e,_),z(e,t),z(e,n),o||(f=R(e,"click",i),o=!0)},p(r,_){s=r,_[0]&524288&&l!==(l=s[39]+1+"")&&le(t,l),_[0]&589824&&fe(e,"current-page",s[16]===s[39])},d(r){r&&q(e),o=!1,f()}}}function Ue(s){let e;return{c(){e=B("div"),e.textContent="..."},m(l,t){N(l,e,t)},p:Ge,d(l){l&&q(e)}}}function qe(s){let e;function l(o,f){return o[39]===-1?Ue:Te}let t=l(s),n=t(s);return{c(){n.c(),e=K()},m(o,f){n.m(o,f),N(o,e,f)},p(o,f){t===(t=l(o))&&n?n.p(o,f):(n.d(1),n=t(o),n&&(n.c(),n.m(e.parentNode,e)))},d(o){o&&q(e),n.d(o)}}}function Ve(s){let e,l,t,n,o,f,i=s[5]&&he(s);const r=[Le,Ke],_=[];function m(h,c){return h[22]?0:h[18].length>0?1:-1}~(l=m(s))&&(t=_[l]=r[l](s));let u=s[17]&&we(s);return{c(){i&&i.c(),e=I(),t&&t.c(),n=I(),u&&u.c(),o=K()},m(h,c){i&&i.m(h,c),N(h,e,c),~l&&_[l].m(h,c),N(h,n,c),u&&u.m(h,c),N(h,o,c),f=!0},p(h,c){h[5]?i?i.p(h,c):(i=he(h),i.c(),i.m(e.parentNode,e)):i&&(i.d(1),i=null);let p=l;l=m(h),l===p?~l&&_[l].p(h,c):(t&&(P(),k(_[p],1,1,()=>{_[p]=null}),E()),~l?(t=_[l],t?t.p(h,c):(t=_[l]=r[l](h),t.c()),b(t,1),t.m(n.parentNode,n)):t=null),h[17]?u?u.p(h,c):(u=we(h),u.c(),u.m(o.parentNode,o)):u&&(u.d(1),u=null)},i(h){f||(b(t),f=!0)},o(h){k(t),f=!1},d(h){h&&(q(e),q(n),q(o)),i&&i.d(h),~l&&_[l].d(h),u&&u.d(h)}}}function We(s){let e,l;return e=new Se({props:{visible:s[10],padding:!1,elem_id:s[8],elem_classes:s[9],scale:s[13],min_width:s[14],allow_overflow:!1,container:!1,$$slots:{default:[Ve]},$$scope:{ctx:s}}}),{c(){Y(e.$$.fragment)},m(t,n){F(e,t,n),l=!0},p(t,n){const o={};n[0]&1024&&(o.visible=t[10]),n[0]&256&&(o.elem_id=t[8]),n[0]&512&&(o.elem_classes=t[9]),n[0]&8192&&(o.scale=t[13]),n[0]&16384&&(o.min_width=t[14]),n[0]&8362239|n[1]&4194304&&(o.$$scope={dirty:n,ctx:t}),e.$set(o)},i(t){l||(b(e.$$.fragment,t),l=!0)},o(t){k(e.$$.fragment,t),l=!1},d(t){D(e,t)}}}function Xe(s,e,l){let t,{components:n}=e,{component_props:o}=e,{component_map:f}=e,{label:i="Examples"}=e,{show_label:r=!0}=e,{headers:_}=e,{samples:m=null}=e,u=null,{sample_labels:h=null}=e,{elem_id:c=""}=e,{elem_classes:p=[]}=e,{visible:g=!0}=e,{value:w=null}=e,{root:X}=e,{proxy_url:L}=e,{samples_per_page:C=10}=e,{scale:te=null}=e,{min_width:ne=void 0}=e,{gradio:S}=e,{layout:Z=null}=e,ze=L?`/proxy=${L}file=`:`${X}/file=`,j=0,y=m?m.length>C:!1,G,O,A=[],$=-1;function x(a){l(20,$=a)}function ee(){l(20,$=-1)}let se=[];async function Be(a){l(21,se=await Promise.all(a&&a.map(async H=>await Promise.all(H.map(async(Ie,Re)=>({value:Ie,component:(await f.get(n[Re]))?.default}))))))}const He=(a,H)=>{l(0,w=a+j*C),S.dispatch("click",w),S.dispatch("select",{index:w,value:H})},Me=a=>x(a),Ce=()=>ee(),Ee=a=>{l(0,w=a+j*C),S.dispatch("click",w),S.dispatch("select",{index:w,value:G[a]})},Pe=a=>x(a),je=()=>ee(),Ae=a=>l(16,j=a);return s.$$set=a=>{"components"in a&&l(1,n=a.components),"component_props"in a&&l(2,o=a.component_props),"component_map"in a&&l(3,f=a.component_map),"label"in a&&l(4,i=a.label),"show_label"in a&&l(5,r=a.show_label),"headers"in a&&l(6,_=a.headers),"samples"in a&&l(26,m=a.samples),"sample_labels"in a&&l(7,h=a.sample_labels),"elem_id"in a&&l(8,c=a.elem_id),"elem_classes"in a&&l(9,p=a.elem_classes),"visible"in a&&l(10,g=a.visible),"value"in a&&l(0,w=a.value),"root"in a&&l(11,X=a.root),"proxy_url"in a&&l(27,L=a.proxy_url),"samples_per_page"in a&&l(12,C=a.samples_per_page),"scale"in a&&l(13,te=a.scale),"min_width"in a&&l(14,ne=a.min_width),"gradio"in a&&l(15,S=a.gradio),"layout"in a&&l(28,Z=a.layout)},s.$$.update=()=>{s.$$.dirty[0]&268435586&&l(22,t=(n.length<2||h!==null)&&Z!=="table"),s.$$.dirty[0]&1678446720&&(h?l(26,m=h.map(a=>[a])):m||l(26,m=[]),m!==u&&(l(16,j=0),l(29,u=m)),l(17,y=m.length>C),y?(l(19,A=[]),l(18,G=m.slice(j*C,(j+1)*C)),l(30,O=Math.ceil(m.length/C)),[0,j,O-1].forEach(a=>{for(let H=a-2;H<=a+2;H++)H>=0&&H<O&&!A.includes(H)&&(A.length>0&&H-A[A.length-1]>1&&A.push(-1),A.push(H))})):l(18,G=m.slice())),s.$$.dirty[0]&262152&&Be(G)},[w,n,o,f,i,r,_,h,c,p,g,X,C,te,ne,S,j,y,G,A,$,se,t,ze,x,ee,m,L,Z,u,O,He,Me,Ce,Ee,Pe,je,Ae]}class ol extends De{constructor(e){super(),Fe(this,e,Xe,We,Je,{components:1,component_props:2,component_map:3,label:4,show_label:5,headers:6,samples:26,sample_labels:7,elem_id:8,elem_classes:9,visible:10,value:0,root:11,proxy_url:27,samples_per_page:12,scale:13,min_width:14,gradio:15,layout:28},null,[-1,-1])}get components(){return this.$$.ctx[1]}set components(e){this.$$set({components:e}),v()}get component_props(){return this.$$.ctx[2]}set component_props(e){this.$$set({component_props:e}),v()}get component_map(){return this.$$.ctx[3]}set component_map(e){this.$$set({component_map:e}),v()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),v()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),v()}get headers(){return this.$$.ctx[6]}set headers(e){this.$$set({headers:e}),v()}get samples(){return this.$$.ctx[26]}set samples(e){this.$$set({samples:e}),v()}get sample_labels(){return this.$$.ctx[7]}set sample_labels(e){this.$$set({sample_labels:e}),v()}get elem_id(){return this.$$.ctx[8]}set elem_id(e){this.$$set({elem_id:e}),v()}get elem_classes(){return this.$$.ctx[9]}set elem_classes(e){this.$$set({elem_classes:e}),v()}get visible(){return this.$$.ctx[10]}set visible(e){this.$$set({visible:e}),v()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),v()}get root(){return this.$$.ctx[11]}set root(e){this.$$set({root:e}),v()}get proxy_url(){return this.$$.ctx[27]}set proxy_url(e){this.$$set({proxy_url:e}),v()}get samples_per_page(){return this.$$.ctx[12]}set samples_per_page(e){this.$$set({samples_per_page:e}),v()}get scale(){return this.$$.ctx[13]}set scale(e){this.$$set({scale:e}),v()}get min_width(){return this.$$.ctx[14]}set min_width(e){this.$$set({min_width:e}),v()}get gradio(){return this.$$.ctx[15]}set gradio(e){this.$$set({gradio:e}),v()}get layout(){return this.$$.ctx[28]}set layout(e){this.$$set({layout:e}),v()}}export{ol as default};
//# sourceMappingURL=Index-loH_ztvM.js.map
