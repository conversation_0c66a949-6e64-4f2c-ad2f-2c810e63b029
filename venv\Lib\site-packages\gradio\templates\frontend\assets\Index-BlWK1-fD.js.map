{"version": 3, "file": "Index-BlWK1-fD.js", "sources": ["../../../../js/sketchbox/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\n\texport let row: boolean;\n\texport let is_container: boolean;\n\texport let component_type: string;\n\texport let var_name: string;\n\texport let active = false;\n\texport let function_mode = false;\n\texport let event_list: string[];\n\texport let is_input = false;\n\texport let is_output = false;\n\texport let triggers: string[] = [];\n\t$: is_function = component_type === \"function\";\n\n\texport let gradio: Gradio<{\n\t\tselect: SelectData;\n\t}>;\n\n\tconst dispatch = (type: string) => {\n\t\treturn (event: MouseEvent) => {\n\t\t\tevent.stopPropagation();\n\t\t\tgradio.dispatch(\"select\", { index: 0, value: type });\n\t\t};\n\t};\n\n\tconst invisible_components = [\"state\", \"browserstate\", \"function\"];\n</script>\n\n<div class=\"sketchbox\" class:function_mode class:row class:active>\n\t<div class=\"cover\"></div>\n\t<!-- svelte-ignore a11y-click-events-have-key-events -->\n\t<!-- svelte-ignore a11y-no-static-element-interactions -->\n\t<div\n\t\tclass=\"interaction\"\n\t\ton:click={is_container ? undefined : dispatch(\"modify\")}\n\t>\n\t\t{#if invisible_components.includes(component_type)}\n\t\t\t<div class=\"component-name\">\n\t\t\t\t<span>{component_type}:</span>&nbsp;{var_name}\n\t\t\t</div>\n\t\t{/if}\n\t\t{#if function_mode}\n\t\t\t{#if !is_function && !is_container}\n\t\t\t\t<div class=\"button-set\">\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass=\"function input\"\n\t\t\t\t\t\ton:click={dispatch(\"input\")}\n\t\t\t\t\t\tclass:selected={is_input}>input</button\n\t\t\t\t\t>\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass=\"function output\"\n\t\t\t\t\t\ton:click={dispatch(\"output\")}\n\t\t\t\t\t\tclass:selected={is_output}>output</button\n\t\t\t\t\t>\n\t\t\t\t\t| {#each event_list as event}\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"function event\"\n\t\t\t\t\t\t\ton:click={dispatch(\"on:\" + event)}\n\t\t\t\t\t\t\tclass:selected={triggers.includes(event)}>on:{event}</button\n\t\t\t\t\t\t>\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t{:else}\n\t\t\t<button class=\"add up\" on:click={dispatch(\"up\")}>+</button>\n\t\t\t<button class=\"add left\" on:click={dispatch(\"left\")}>+</button>\n\t\t\t<button class=\"add right\" on:click={dispatch(\"right\")}>+</button>\n\t\t\t<button class=\"add down\" on:click={dispatch(\"down\")}>+</button>\n\t\t\t{#if !is_container}\n\t\t\t\t<button class=\"action modify\" on:click={dispatch(\"modify\")}>✎</button>\n\t\t\t\t<button class=\"action delete\" on:click={dispatch(\"delete\")}>✗</button>\n\t\t\t{/if}\n\t\t{/if}\n\t</div>\n\t<slot />\n</div>\n\n<style>\n\t.sketchbox {\n\t\tposition: inherit;\n\t\tdisplay: flex;\n\t\tflex-direction: inherit;\n\t\talign-items: inherit;\n\t\tmin-height: 32px;\n\t}\n\t.sketchbox:not(.function_mode) {\n\t\tcursor: pointer;\n\t}\n\t.function_mode .cover {\n\t\tbackground-color: color-mix(\n\t\t\tin srgb,\n\t\t\tvar(--background-fill-primary),\n\t\t\ttransparent 80%\n\t\t);\n\t}\n\n\t.row > :global(*),\n\t.row > :global(.form),\n\t.row > :global(.form > *) {\n\t\tflex-grow: 1 !important;\n\t}\n\t.interaction {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tz-index: 100;\n\t}\n\t.cover {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tz-index: 99;\n\t}\n\t.interaction:hover,\n\t.active .interaction {\n\t\tborder-color: var(--body-text-color);\n\t\tborder-width: 1px;\n\t\tborder-radius: var(--block-radius);\n\t}\n\t.active.function_mode .interaction {\n\t\tborder-color: var(--color-accent);\n\t\tborder-width: 2px;\n\t}\n\t.interaction:hover .add,\n\t.interaction:hover .action {\n\t\tdisplay: flex;\n\t}\n\tbutton {\n\t\tborder-color: var(--body-text-color);\n\t\tborder-width: 1px;\n\t\tbackground-color: var(--button-secondary-background-fill);\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tfont-weight: bold;\n\t}\n\t.add,\n\t.action {\n\t\tdisplay: none;\n\t\tposition: absolute;\n\t}\n\t.action {\n\t\tborder-radius: 15px;\n\t\twidth: 30px;\n\t\theight: 30px;\n\t}\n\t.add {\n\t\tborder-radius: 10px;\n\t\twidth: 20px;\n\t\theight: 20px;\n\t}\n\t.function {\n\t\tborder-radius: 15px;\n\t\tpadding: 4px 8px;\n\t\tfont-size: 10px;\n\t}\n\t.function.selected {\n\t\tcolor: white;\n\t}\n\t.input.selected {\n\t\tbackground-color: oklch(0.707 0.165 254.624);\n\t\tborder-color: oklch(0.707 0.165 254.624);\n\t}\n\t.output.selected {\n\t\tbackground-color: oklch(0.712 0.194 13.428);\n\t\tborder-color: oklch(0.712 0.194 13.428);\n\t}\n\t.event.selected {\n\t\tbackground-color: oklch(0.702 0.183 293.541);\n\t\tborder-color: oklch(0.702 0.183 293.541);\n\t}\n\tbutton:hover {\n\t\tbackground-color: var(--button-secondary-background-fill-hover);\n\t}\n\t.up {\n\t\ttop: -10px;\n\t\tleft: 50%;\n\t\ttransform: translate(-50%, 0);\n\t\twidth: 80%;\n\t}\n\t.left {\n\t\ttop: 50%;\n\t\tleft: -10px;\n\t\ttransform: translate(0, -50%);\n\t\theight: 80%;\n\t}\n\t.right {\n\t\ttop: 50%;\n\t\tright: -10px;\n\t\ttransform: translate(0, -50%);\n\t\theight: 80%;\n\t}\n\t.down {\n\t\tbottom: -10px;\n\t\tleft: 50%;\n\t\ttransform: translate(-50%, 0);\n\t\twidth: 80%;\n\t}\n\t.modify {\n\t\ttop: 50%;\n\t\tleft: 50%;\n\t\ttransform: translate(calc(-50% - 20px), -50%);\n\t}\n\t.delete {\n\t\ttop: 50%;\n\t\tleft: 50%;\n\t\ttransform: translate(calc(-50% + 20px), -50%);\n\t}\n\t.button-set {\n\t\tdisplay: flex;\n\t\tgap: 8px;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\theight: 100%;\n\t\tflex-wrap: wrap;\n\t\tpadding: 0 30px;\n\t}\n\t.component-name {\n\t\tbackground: var(--block-background-fill);\n\t\tborder: var(--block-border-color) var(--block-border-width) solid;\n\t\tborder-radius: var(--block-radius);\n\t\theight: 100%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\t.component-name span {\n\t\tcolor: var(--body-text-color-subdued);\n\t\tfont-style: italic;\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "div", "anchor", "append", "span", "create_if_block_2", "button0", "button1", "button2", "button3", "listen", "if_block", "create_if_block_1", "i", "button", "set_data", "t1", "t1_value", "create_if_block", "div2", "div0", "div1", "row", "$$props", "is_container", "component_type", "var_name", "active", "function_mode", "event_list", "is_input", "is_output", "triggers", "gradio", "dispatch", "type", "event", "invisible_components", "$$invalidate", "is_function"], "mappings": "+eAuCWA,EAAc,CAAA,CAAA,MAAC,GAAC,MAAO,GAAM,MAACA,EAAQ,CAAA,CAAA,qFAD9CC,EAEKC,EAAAC,EAAAC,CAAA,EADJC,EAA8BF,EAAAG,CAAA,+CAAvBN,EAAc,CAAA,CAAA,WAAgBA,EAAQ,CAAA,CAAA,6DA8BxCA,EAAY,CAAA,GAAAO,EAAAP,CAAA,uVAJlBC,EAA0DC,EAAAM,EAAAJ,CAAA,WAC1DH,EAA8DC,EAAAO,EAAAL,CAAA,WAC9DH,EAAgEC,EAAAQ,EAAAN,CAAA,WAChEH,EAA8DC,EAAAS,EAAAP,CAAA,uCAH7BQ,EAAAJ,EAAA,QAAAR,MAAS,IAAI,CAAA,EACXY,EAAAH,EAAA,QAAAT,MAAS,MAAM,CAAA,EACdY,EAAAF,EAAA,QAAAV,MAAS,OAAO,CAAA,EACjBY,EAAAD,EAAA,QAAAX,MAAS,MAAM,CAAA,iBAC5CA,EAAY,CAAA,qKA1BZa,EAAA,CAAAb,QAAgBA,EAAY,CAAA,GAAAc,EAAAd,CAAA,iEAA5B,CAAAA,QAAgBA,EAAY,CAAA,0SA2BjCC,EAAqEC,EAAAM,EAAAJ,CAAA,WACrEH,EAAqEC,EAAAO,EAAAL,CAAA,SAD7BQ,EAAAJ,EAAA,QAAAR,MAAS,QAAQ,CAAA,EACjBY,EAAAH,EAAA,QAAAT,MAAS,QAAQ,CAAA,uFAhB/CA,EAAU,CAAA,CAAA,uBAAf,OAAIe,GAAA,4HADR;AAAA,QACE,mGAPef,EAAQ,CAAA,CAAA,+DAKRA,EAAS,CAAA,CAAA,mDAT3BC,EAkBKC,EAAAC,EAAAC,CAAA,EAjBJC,EAIAF,EAAAK,CAAA,SACAH,EAIAF,EAAAM,CAAA,iEAPWG,EAAAJ,EAAA,QAAAR,MAAS,OAAO,CAAA,EAKhBY,EAAAH,EAAA,QAAAT,MAAS,QAAQ,CAAA,0CAJXA,EAAQ,CAAA,CAAA,wBAKRA,EAAS,CAAA,CAAA,cAEjBA,EAAU,CAAA,CAAA,oBAAf,OAAIe,GAAA,EAAA,iHAAJ,iEAI4Cf,EAAK,EAAA,EAAA,sCAAT,KAAG,qEAA7BA,EAAQ,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,UAHxCC,EAIAC,EAAAc,EAAAZ,CAAA,+CAFWJ,EAAQ,EAAA,EAAC,MAAQA,SAAjBA,EAAQ,EAAA,EAAC,MAAQA,EAAK,EAAA,CAAA,EAAA,MAAA,KAAA,SAAA,mCACcA,EAAK,EAAA,EAAA,KAAAiB,EAAAC,EAAAC,CAAA,wBAAnCnB,EAAQ,CAAA,EAAC,SAASA,EAAK,EAAA,CAAA,CAAA,wDAtBvCA,EAAoB,EAAA,EAAC,SAASA,EAAc,CAAA,CAAA,6CAK5CA,EAAa,CAAA,EAAAoB,iVAbpBnB,EA+CKC,EAAAmB,EAAAjB,CAAA,EA9CJC,EAAwBgB,EAAAC,CAAA,SAGxBjB,EAyCKgB,EAAAE,CAAA,8FAvCMvB,EAAY,CAAA,EAAG,OAAYA,EAAQ,EAAA,EAAC,QAAQ,CAAA,IAA5CA,EAAY,CAAA,EAAG,OAAYA,MAAS,QAAQ,GAAA,MAAA,KAAA,SAAA,gCAEjDA,EAAoB,EAAA,EAAC,SAASA,EAAc,CAAA,CAAA,kbAlCvC,CAAA,IAAAwB,CAAA,EAAAC,EACA,CAAA,aAAAC,CAAA,EAAAD,EACA,CAAA,eAAAE,CAAA,EAAAF,EACA,CAAA,SAAAG,CAAA,EAAAH,GACA,OAAAI,EAAS,EAAA,EAAAJ,GACT,cAAAK,EAAgB,EAAA,EAAAL,EAChB,CAAA,WAAAM,CAAA,EAAAN,GACA,SAAAO,EAAW,EAAA,EAAAP,GACX,UAAAQ,EAAY,EAAA,EAAAR,EACZ,CAAA,SAAAS,EAAA,EAAA,EAAAT,EAGA,CAAA,OAAAU,CAAA,EAAAV,QAILW,EAAYC,GACTC,GAAA,CACPA,EAAM,gBAAA,EACNH,EAAO,SAAS,SAAA,CAAY,MAAO,EAAG,MAAOE,CAAA,CAAA,GAIzCE,EAAwB,CAAA,QAAS,eAAgB,UAAU,+dAbjEC,EAAA,GAAGC,EAAcd,IAAmB,UAAA"}