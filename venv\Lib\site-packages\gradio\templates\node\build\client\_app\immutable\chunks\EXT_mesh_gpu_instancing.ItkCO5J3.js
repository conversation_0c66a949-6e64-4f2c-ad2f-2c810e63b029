import{a as e,V as A,Q as T,M as F,an as b,ao as M}from"./index.BoI39RQH.js";import{GLTFLoader as V,ArrayItem as E}from"./glTFLoader.BetPWe9U.js";import"./thinInstanceMesh.BW3MxSeP.js";const n="EXT_mesh_gpu_instancing";class g{constructor(i){this.name=n,this._loader=i,this.enabled=this._loader.isExtensionUsed(n)}dispose(){this._loader=null}loadNodeAsync(i,o,f){return V.LoadExtensionAsync(i,o,this.name,(m,h)=>{this._loader._disableInstancedMesh++;const d=this._loader.loadNodeAsync(`/nodes/${o.index}`,o,f);if(this._loader._disableInstancedMesh--,!o._primitiveBabylonMeshes)return d;const c=new Array;let t=0;const l=a=>{if(h.attributes[a]==null){c.push(Promise.resolve(null));return}const s=E.Get(`${m}/attributes/${a}`,this._loader.gltf.accessors,h.attributes[a]);if(c.push(this._loader._loadFloatAccessorAsync(`/accessors/${s.bufferView}`,s)),t===0)t=s.count;else if(t!==s.count)throw new Error(`${m}/attributes: Instance buffer accessors do not have the same count.`)};return l("TRANSLATION"),l("ROTATION"),l("SCALE"),d.then(a=>Promise.all(c).then(([s,p,_])=>{const y=new Float32Array(t*16);e.Vector3[0].copyFromFloats(0,0,0),e.Quaternion[0].copyFromFloats(0,0,0,1),e.Vector3[1].copyFromFloats(1,1,1);for(let r=0;r<t;++r)s&&A.FromArrayToRef(s,r*3,e.Vector3[0]),p&&T.FromArrayToRef(p,r*4,e.Quaternion[0]),_&&A.FromArrayToRef(_,r*3,e.Vector3[1]),F.ComposeToRef(e.Vector3[1],e.Quaternion[0],e.Vector3[0],e.Matrix[0]),e.Matrix[0].copyToArray(y,r*16);for(const r of o._primitiveBabylonMeshes)r.thinInstanceSetBuffer("matrix",y,16,!0);return a}))})}}b(n);M(n,!0,u=>new g(u));export{g as EXT_mesh_gpu_instancing};
//# sourceMappingURL=EXT_mesh_gpu_instancing.ItkCO5J3.js.map
