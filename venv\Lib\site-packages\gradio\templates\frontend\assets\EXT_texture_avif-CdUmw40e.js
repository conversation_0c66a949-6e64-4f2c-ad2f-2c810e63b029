import{GLTFLoader as o,ArrayItem as a}from"./glTFLoader-9Z3KGax5.js";import{an as u,ao as _}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./bone-kZWM5-u7.js";import"./rawTexture-DmvUfjqF.js";import"./assetContainer-BRzQBugc.js";import"./objectModelMapping-BR4RdEzn.js";const s="EXT_texture_avif";class c{constructor(e){this.name=s,this._loader=e,this.enabled=e.isExtensionUsed(s)}dispose(){this._loader=null}_loadTextureAsync(e,r,i){return o.LoadExtensionAsync(e,r,this.name,(n,m)=>{const l=r.sampler==null?o.DefaultSampler:a.Get(`${e}/sampler`,this._loader.gltf.samplers,r.sampler),p=a.Get(`${n}/source`,this._loader.gltf.images,m.source);return this._loader._createTextureAsync(e,l,p,d=>{i(d)},void 0,!r._textureInfo.nonColorData)})}}u(s);_(s,!0,t=>new c(t));export{c as EXT_texture_avif};
//# sourceMappingURL=EXT_texture_avif-CdUmw40e.js.map
