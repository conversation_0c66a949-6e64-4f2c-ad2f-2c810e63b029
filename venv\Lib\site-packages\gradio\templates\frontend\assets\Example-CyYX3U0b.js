import{M as o}from"./MarkdownCode-CkSMBRHJ.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import"./prism-python-MMh3z1bK.js";const{SvelteComponent:h,attr:g,create_component:d,destroy_component:b,detach:k,element:z,flush:_,init:v,insert:w,mount_component:y,safe_not_equal:x,toggle_class:m,transition_in:q,transition_out:C}=window.__gradio__svelte__internal;function M(l){let e,t,a;return t=new o({props:{message:c(l[0]),latex_delimiters:l[5],sanitize_html:l[3],line_breaks:l[4],chatbot:!1}}),{c(){e=z("div"),d(t.$$.fragment),g(e,"class","prose svelte-1ayixqk"),m(e,"table",l[1]==="table"),m(e,"gallery",l[1]==="gallery"),m(e,"selected",l[2])},m(i,s){w(i,e,s),y(t,e,null),a=!0},p(i,[s]){const r={};s&1&&(r.message=c(i[0])),s&32&&(r.latex_delimiters=i[5]),s&8&&(r.sanitize_html=i[3]),s&16&&(r.line_breaks=i[4]),t.$set(r),(!a||s&2)&&m(e,"table",i[1]==="table"),(!a||s&2)&&m(e,"gallery",i[1]==="gallery"),(!a||s&4)&&m(e,"selected",i[2])},i(i){a||(q(t.$$.fragment,i),a=!0)},o(i){C(t.$$.fragment,i),a=!1},d(i){i&&k(e),b(t)}}}function c(l,e=60){if(!l)return"";const t=String(l);return t.length<=e?t:t.slice(0,e)+"..."}function S(l,e,t){let{value:a}=e,{type:i}=e,{selected:s=!1}=e,{sanitize_html:r}=e,{line_breaks:u}=e,{latex_delimiters:f}=e;return l.$$set=n=>{"value"in n&&t(0,a=n.value),"type"in n&&t(1,i=n.type),"selected"in n&&t(2,s=n.selected),"sanitize_html"in n&&t(3,r=n.sanitize_html),"line_breaks"in n&&t(4,u=n.line_breaks),"latex_delimiters"in n&&t(5,f=n.latex_delimiters)},[a,i,s,r,u,f]}class F extends h{constructor(e){super(),v(this,e,S,M,x,{value:0,type:1,selected:2,sanitize_html:3,line_breaks:4,latex_delimiters:5})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),_()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),_()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),_()}get sanitize_html(){return this.$$.ctx[3]}set sanitize_html(e){this.$$set({sanitize_html:e}),_()}get line_breaks(){return this.$$.ctx[4]}set line_breaks(e){this.$$set({line_breaks:e}),_()}get latex_delimiters(){return this.$$.ctx[5]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),_()}}export{F as default};
//# sourceMappingURL=Example-CyYX3U0b.js.map
