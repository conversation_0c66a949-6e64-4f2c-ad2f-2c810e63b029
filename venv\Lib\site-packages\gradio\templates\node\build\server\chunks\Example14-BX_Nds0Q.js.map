{"version": 3, "file": "Example14-BX_Nds0Q.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Example14.js"], "sourcesContent": ["import{create_ssr_component as n}from\"svelte/internal\";const c={code:\".gallery.svelte-zvfedn{padding:var(--size-2)}\",map:'{\"version\":3,\"file\":\"Example.svelte\",\"sources\":[\"Example.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">export let value;\\\\nexport let type;\\\\nexport let selected = false;\\\\n<\\/script>\\\\n\\\\n<div\\\\n\\\\tclass:table={type === \\\\\"table\\\\\"}\\\\n\\\\tclass:gallery={type === \\\\\"gallery\\\\\"}\\\\n\\\\tclass:selected\\\\n\\\\tclass=\\\\\"prose\\\\\"\\\\n>\\\\n\\\\t{@html value}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.gallery {\\\\n\\\\t\\\\tpadding: var(--size-2);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAeC,sBAAS,CACR,OAAO,CAAE,IAAI,QAAQ,CACtB\"}'},o=n((v,e,t,d)=>{let{value:s}=e,{type:l}=e,{selected:a=!1}=e;return e.value===void 0&&t.value&&s!==void 0&&t.value(s),e.type===void 0&&t.type&&l!==void 0&&t.type(l),e.selected===void 0&&t.selected&&a!==void 0&&t.selected(a),v.css.add(c),`<div class=\"${[\"prose svelte-zvfedn\",(l===\"table\"?\"table\":\"\")+\" \"+(l===\"gallery\"?\"gallery\":\"\")+\" \"+(a?\"selected\":\"\")].join(\" \").trim()}\"><!-- HTML_TAG_START -->${s}<!-- HTML_TAG_END --> </div>`});export{o as default};\n//# sourceMappingURL=Example14.js.map\n"], "names": ["n"], "mappings": ";;AAA4D,MAAC,CAAC,CAAC,CAAC,IAAI,CAAC,+CAA+C,CAAC,GAAG,CAAC,0fAA0f,CAAC,CAAC,CAAC,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,yBAAyB,EAAE,CAAC,CAAC,4BAA4B,CAAC,CAAC;;;;"}