/* empty css                                                        */const{SvelteComponent:K,append:P,attr:c,bubble:L,check_outros:M,construct_svelte_component:S,create_component:j,create_slot:N,destroy_component:A,detach:G,element:B,flush:g,get_all_dirty_from_scope:O,get_slot_changes:Q,group_outros:R,init:T,insert:H,listen:U,mount_component:D,safe_not_equal:V,set_data:W,set_style:I,space:E,text:X,toggle_class:h,transition_in:v,transition_out:q,update_slot_base:Y}=window.__gradio__svelte__internal;function F(l){let e,o;return{c(){e=B("span"),o=X(l[1]),c(e,"class","svelte-vzs2gq")},m(i,a){H(i,e,a),P(e,o)},p(i,a){a&2&&W(o,i[1])},d(i){i&&G(e)}}}function Z(l){let e,o,i,a,b,f,m,k,u=l[2]&&F(l);var d=l[0];function w(t,s){return{}}d&&(a=S(d,w()));const _=l[14].default,r=N(_,l,l[13],null);return{c(){e=B("button"),u&&u.c(),o=E(),i=B("div"),a&&j(a.$$.fragment),b=E(),r&&r.c(),c(i,"class","svelte-vzs2gq"),h(i,"x-small",l[4]==="x-small"),h(i,"small",l[4]==="small"),h(i,"large",l[4]==="large"),h(i,"medium",l[4]==="medium"),e.disabled=l[7],c(e,"aria-label",l[1]),c(e,"aria-haspopup",l[8]),c(e,"title",l[1]),c(e,"class","svelte-vzs2gq"),h(e,"pending",l[3]),h(e,"padded",l[5]),h(e,"highlight",l[6]),h(e,"transparent",l[9]),I(e,"color",!l[7]&&l[11]?l[11]:"var(--block-label-text-color)"),I(e,"--bg-color",l[7]?"auto":l[10])},m(t,s){H(t,e,s),u&&u.m(e,null),P(e,o),P(e,i),a&&D(a,i,null),P(i,b),r&&r.m(i,null),f=!0,m||(k=U(e,"click",l[15]),m=!0)},p(t,[s]){if(t[2]?u?u.p(t,s):(u=F(t),u.c(),u.m(e,o)):u&&(u.d(1),u=null),s&1&&d!==(d=t[0])){if(a){R();const z=a;q(z.$$.fragment,1,0,()=>{A(z,1)}),M()}d?(a=S(d,w()),j(a.$$.fragment),v(a.$$.fragment,1),D(a,i,b)):a=null}r&&r.p&&(!f||s&8192)&&Y(r,_,t,t[13],f?Q(_,t[13],s,null):O(t[13]),null),(!f||s&16)&&h(i,"x-small",t[4]==="x-small"),(!f||s&16)&&h(i,"small",t[4]==="small"),(!f||s&16)&&h(i,"large",t[4]==="large"),(!f||s&16)&&h(i,"medium",t[4]==="medium"),(!f||s&128)&&(e.disabled=t[7]),(!f||s&2)&&c(e,"aria-label",t[1]),(!f||s&256)&&c(e,"aria-haspopup",t[8]),(!f||s&2)&&c(e,"title",t[1]),(!f||s&8)&&h(e,"pending",t[3]),(!f||s&32)&&h(e,"padded",t[5]),(!f||s&64)&&h(e,"highlight",t[6]),(!f||s&512)&&h(e,"transparent",t[9]),s&2176&&I(e,"color",!t[7]&&t[11]?t[11]:"var(--block-label-text-color)"),s&1152&&I(e,"--bg-color",t[7]?"auto":t[10])},i(t){f||(a&&v(a.$$.fragment,t),v(r,t),f=!0)},o(t){a&&q(a.$$.fragment,t),q(r,t),f=!1},d(t){t&&G(e),u&&u.d(),a&&A(a),r&&r.d(t),m=!1,k()}}}function p(l,e,o){let i,{$$slots:a={},$$scope:b}=e,{Icon:f}=e,{label:m=""}=e,{show_label:k=!1}=e,{pending:u=!1}=e,{size:d="small"}=e,{padded:w=!0}=e,{highlight:_=!1}=e,{disabled:r=!1}=e,{hasPopup:t=!1}=e,{color:s="var(--block-label-text-color)"}=e,{transparent:z=!1}=e,{background:C="var(--block-background-fill)"}=e;function J(n){L.call(this,l,n)}return l.$$set=n=>{"Icon"in n&&o(0,f=n.Icon),"label"in n&&o(1,m=n.label),"show_label"in n&&o(2,k=n.show_label),"pending"in n&&o(3,u=n.pending),"size"in n&&o(4,d=n.size),"padded"in n&&o(5,w=n.padded),"highlight"in n&&o(6,_=n.highlight),"disabled"in n&&o(7,r=n.disabled),"hasPopup"in n&&o(8,t=n.hasPopup),"color"in n&&o(12,s=n.color),"transparent"in n&&o(9,z=n.transparent),"background"in n&&o(10,C=n.background),"$$scope"in n&&o(13,b=n.$$scope)},l.$$.update=()=>{l.$$.dirty&4160&&o(11,i=_?"var(--color-accent)":s)},[f,m,k,u,d,w,_,r,t,z,C,i,s,b,a,J]}class x extends K{constructor(e){super(),T(this,e,p,Z,V,{Icon:0,label:1,show_label:2,pending:3,size:4,padded:5,highlight:6,disabled:7,hasPopup:8,color:12,transparent:9,background:10})}get Icon(){return this.$$.ctx[0]}set Icon(e){this.$$set({Icon:e}),g()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),g()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),g()}get pending(){return this.$$.ctx[3]}set pending(e){this.$$set({pending:e}),g()}get size(){return this.$$.ctx[4]}set size(e){this.$$set({size:e}),g()}get padded(){return this.$$.ctx[5]}set padded(e){this.$$set({padded:e}),g()}get highlight(){return this.$$.ctx[6]}set highlight(e){this.$$set({highlight:e}),g()}get disabled(){return this.$$.ctx[7]}set disabled(e){this.$$set({disabled:e}),g()}get hasPopup(){return this.$$.ctx[8]}set hasPopup(e){this.$$set({hasPopup:e}),g()}get color(){return this.$$.ctx[12]}set color(e){this.$$set({color:e}),g()}get transparent(){return this.$$.ctx[9]}set transparent(e){this.$$set({transparent:e}),g()}get background(){return this.$$.ctx[10]}set background(e){this.$$set({background:e}),g()}}export{x as I};
//# sourceMappingURL=IconButton-C_HS7fTi.js.map
