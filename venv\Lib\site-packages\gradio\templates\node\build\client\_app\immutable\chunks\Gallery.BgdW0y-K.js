import{SvelteComponent as ll,init as tl,safe_not_equal as nl,add_render_callback as il,space as O,empty as oe,claim_space as R,insert_hydration as U,listen as te,transition_in as g,group_outros as P,transition_out as w,check_outros as W,detach as E,createEventDispatcher as rl,onMount as ol,globals as fl,create_component as j,claim_component as z,mount_component as N,destroy_component as L,tick as al,bubble as se,binding_callbacks as ce,ensure_array_like as fe,element as G,claim_element as S,children as q,attr as D,set_style as F,toggle_class as C,append_hydration as M,destroy_each as Ue,is_function as ul,run_all as sl,text as Ve,claim_text as Ce,set_data as Me,noop as cl}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{u as _l,I as Oe,C as ml,r as _e}from"./2.B2AoQPnG.js";import{B as hl}from"./BlockLabel.BTSz9r5s.js";import{E as gl}from"./Empty.DwQ6nkN6.js";import{S as bl}from"./ShareButton.Be7APJkJ.js";import{D as dl}from"./Download.CpfEFmFf.js";import{I as Re}from"./Image.CTVzPhL7.js";import{P as Pe}from"./Play.DJ4h2PVY.js";import{I as pl}from"./IconButtonWrapper.D5aGR59h.js";import{F as wl}from"./FullscreenButton.g_8wwg6y.js";/* empty css                                             */import{M as kl}from"./ModifyUpload.uW4g0eE0.js";import{V as me}from"./Video.SijWdeHX.js";import{d as vl}from"./index.tFQomdd2.js";async function yl(n){return n?`<div style="display: flex; flex-wrap: wrap; gap: 16px">${(await Promise.all(n.map(async([t,e])=>t===null||!t.url?"":await _l(t.url)))).map(t=>`<img src="${t}" style="height: 400px" />`).join("")}</div>`:""}const{window:We}=fl;function we(n,l,t){const e=n.slice();return e[51]=l[t],e[53]=t,e}function ke(n,l,t){const e=n.slice();return e[54]=l[t],e[55]=l,e[53]=t,e}function ve(n){let l,t;return l=new hl({props:{show_label:n[2],Icon:Re,label:n[3]||"Gallery"}}),{c(){j(l.$$.fragment)},l(e){z(l.$$.fragment,e)},m(e,r){N(l,e,r),t=!0},p(e,r){const i={};r[0]&4&&(i.show_label=e[2]),r[0]&8&&(i.label=e[3]||"Gallery"),l.$set(i)},i(e){t||(g(l.$$.fragment,e),t=!0)},o(e){w(l.$$.fragment,e),t=!1},d(e){L(l,e)}}}function El(n){let l,t,e,r,i,s,c=n[24]&&n[7]&&ye(n),o=n[12]&&n[1]===null&&ze(n),h=fe(n[17]),f=[];for(let u=0;u<h.length;u+=1)f[u]=Le(we(n,h,u));const d=u=>w(f[u],1,1,()=>{f[u]=null});return{c(){l=G("div"),c&&c.c(),t=O(),e=G("div"),o&&o.c(),r=O(),i=G("div");for(let u=0;u<f.length;u+=1)f[u].c();this.h()},l(u){l=S(u,"DIV",{class:!0});var _=q(l);c&&c.l(_),t=R(_),e=S(_,"DIV",{class:!0});var p=q(e);o&&o.l(p),r=R(p),i=S(p,"DIV",{class:!0,style:!0});var I=q(i);for(let b=0;b<f.length;b+=1)f[b].l(I);I.forEach(E),p.forEach(E),_.forEach(E),this.h()},h(){D(i,"class","grid-container svelte-1atirkn"),F(i,"--grid-cols",n[4]),F(i,"--grid-rows",n[5]),F(i,"--object-fit",n[8]),F(i,"height",n[6]),C(i,"pt-6",n[2]),D(e,"class","grid-wrap svelte-1atirkn"),C(e,"minimal",n[13]==="minimal"),C(e,"fixed-height",n[13]!=="minimal"&&(!n[6]||n[6]=="auto")),C(e,"hidden",n[19]),D(l,"class","gallery-container")},m(u,_){U(u,l,_),c&&c.m(l,null),M(l,t),M(l,e),o&&o.m(e,null),M(e,r),M(e,i);for(let p=0;p<f.length;p+=1)f[p]&&f[p].m(i,null);n[46](l),s=!0},p(u,_){if(u[24]&&u[7]?c?(c.p(u,_),_[0]&16777344&&g(c,1)):(c=ye(u),c.c(),g(c,1),c.m(l,t)):c&&(P(),w(c,1,1,()=>{c=null}),W()),u[12]&&u[1]===null?o?(o.p(u,_),_[0]&4098&&g(o,1)):(o=ze(u),o.c(),g(o,1),o.m(e,r)):o&&(P(),w(o,1,1,()=>{o=null}),W()),_[0]&33685634){h=fe(u[17]);let p;for(p=0;p<h.length;p+=1){const I=we(u,h,p);f[p]?(f[p].p(I,_),g(f[p],1)):(f[p]=Le(I),f[p].c(),g(f[p],1),f[p].m(i,null))}for(P(),p=h.length;p<f.length;p+=1)d(p);W()}(!s||_[0]&16)&&F(i,"--grid-cols",u[4]),(!s||_[0]&32)&&F(i,"--grid-rows",u[5]),(!s||_[0]&256)&&F(i,"--object-fit",u[8]),(!s||_[0]&64)&&F(i,"height",u[6]),(!s||_[0]&4)&&C(i,"pt-6",u[2]),(!s||_[0]&8192)&&C(e,"minimal",u[13]==="minimal"),(!s||_[0]&8256)&&C(e,"fixed-height",u[13]!=="minimal"&&(!u[6]||u[6]=="auto")),(!s||_[0]&524288)&&C(e,"hidden",u[19])},i(u){if(!s){g(c),g(o);for(let _=0;_<h.length;_+=1)g(f[_]);s=!0}},o(u){w(c),w(o),f=f.filter(Boolean);for(let _=0;_<f.length;_+=1)w(f[_]);s=!1},d(u){u&&E(l),c&&c.d(),o&&o.d(),Ue(f,u),n[46](null)}}}function Il(n){let l,t;return l=new gl({props:{unpadded_box:!0,size:"large",$$slots:{default:[Ul]},$$scope:{ctx:n}}}),{c(){j(l.$$.fragment)},l(e){z(l.$$.fragment,e)},m(e,r){N(l,e,r),t=!0},p(e,r){const i={};r[1]&33554432&&(i.$$scope={dirty:r,ctx:e}),l.$set(i)},i(e){t||(g(l.$$.fragment,e),t=!0)},o(e){w(l.$$.fragment,e),t=!1},d(e){L(l,e)}}}function ye(n){var Y;let l,t,e,r,i,s,c,o,h,f,d,u;t=new pl({props:{display_top_corner:n[15],$$slots:{default:[Tl]},$$scope:{ctx:n}}});const _=[Dl,Bl],p=[];function I(v,k){return"image"in v[24]?0:1}i=I(n),s=p[i]=_[i](n);let b=((Y=n[24])==null?void 0:Y.caption)&&De(n),y=fe(n[17]),m=[];for(let v=0;v<y.length;v+=1)m[v]=je(ke(n,y,v));const ne=v=>w(m[v],1,1,()=>{m[v]=null});return{c(){l=G("button"),j(t.$$.fragment),e=O(),r=G("button"),s.c(),c=O(),b&&b.c(),o=O(),h=G("div");for(let v=0;v<m.length;v+=1)m[v].c();this.h()},l(v){l=S(v,"BUTTON",{class:!0});var k=q(l);z(t.$$.fragment,k),e=R(k),r=S(k,"BUTTON",{class:!0,style:!0,"aria-label":!0});var V=q(r);s.l(V),V.forEach(E),c=R(k),b&&b.l(k),o=R(k),h=S(k,"DIV",{class:!0,"data-testid":!0,style:!0});var X=q(h);for(let J=0;J<m.length;J+=1)m[J].l(X);X.forEach(E),k.forEach(E),this.h()},h(){D(r,"class","media-button svelte-1atirkn"),F(r,"height","calc(100% - "+(n[24].caption?"80px":"60px")+")"),D(r,"aria-label","detailed view of selected image"),D(h,"class","thumbnails scroll-hide svelte-1atirkn"),D(h,"data-testid","container_el"),F(h,"justify-content",n[23]?"flex-start":"center"),D(l,"class","preview svelte-1atirkn"),C(l,"minimal",n[13]==="minimal")},m(v,k){U(v,l,k),N(t,l,null),M(l,e),M(l,r),p[i].m(r,null),M(l,c),b&&b.m(l,null),M(l,o),M(l,h);for(let V=0;V<m.length;V+=1)m[V]&&m[V].m(h,null);n[43](h),f=!0,d||(u=[te(r,"click",function(){ul("image"in n[24]?n[40]:null)&&("image"in n[24]?n[40]:null).apply(this,arguments)}),te(l,"keydown",n[27])],d=!0)},p(v,k){var J;n=v;const V={};k[0]&32768&&(V.display_top_corner=n[15]),k[0]&17518082|k[1]&33554432&&(V.$$scope={dirty:k,ctx:n}),t.$set(V);let X=i;if(i=I(n),i===X?p[i].p(n,k):(P(),w(p[X],1,1,()=>{p[X]=null}),W(),s=p[i],s?s.p(n,k):(s=p[i]=_[i](n),s.c()),g(s,1),s.m(r,null)),(!f||k[0]&16777216)&&F(r,"height","calc(100% - "+(n[24].caption?"80px":"60px")+")"),(J=n[24])!=null&&J.caption?b?b.p(n,k):(b=De(n),b.c(),b.m(l,o)):b&&(b.d(1),b=null),k[0]&2236418){y=fe(n[17]);let T;for(T=0;T<y.length;T+=1){const K=ke(n,y,T);m[T]?(m[T].p(K,k),g(m[T],1)):(m[T]=je(K),m[T].c(),g(m[T],1),m[T].m(h,null))}for(P(),T=y.length;T<m.length;T+=1)ne(T);W()}(!f||k[0]&8388608)&&F(h,"justify-content",n[23]?"flex-start":"center"),(!f||k[0]&8192)&&C(l,"minimal",n[13]==="minimal")},i(v){if(!f){g(t.$$.fragment,v),g(s);for(let k=0;k<y.length;k+=1)g(m[k]);f=!0}},o(v){w(t.$$.fragment,v),w(s),m=m.filter(Boolean);for(let k=0;k<m.length;k+=1)w(m[k]);f=!1},d(v){v&&E(l),L(t),p[i].d(),b&&b.d(),Ue(m,v),n[43](null),d=!1,sl(u)}}}function Ee(n){let l,t;return l=new Oe({props:{Icon:dl,label:n[11]("common.download")}}),l.$on("click",n[35]),{c(){j(l.$$.fragment)},l(e){z(l.$$.fragment,e)},m(e,r){N(l,e,r),t=!0},p(e,r){const i={};r[0]&2048&&(i.label=e[11]("common.download")),l.$set(i)},i(e){t||(g(l.$$.fragment,e),t=!0)},o(e){w(l.$$.fragment,e),t=!1},d(e){L(l,e)}}}function Ie(n){let l,t;return l=new wl({props:{fullscreen:n[16]}}),l.$on("fullscreen",n[36]),{c(){j(l.$$.fragment)},l(e){z(l.$$.fragment,e)},m(e,r){N(l,e,r),t=!0},p(e,r){const i={};r[0]&65536&&(i.fullscreen=e[16]),l.$set(i)},i(e){t||(g(l.$$.fragment,e),t=!0)},o(e){w(l.$$.fragment,e),t=!1},d(e){L(l,e)}}}function Te(n){let l,t,e;return t=new bl({props:{i18n:n[11],value:n[17],formatter:yl}}),t.$on("share",n[37]),t.$on("error",n[38]),{c(){l=G("div"),j(t.$$.fragment),this.h()},l(r){l=S(r,"DIV",{class:!0});var i=q(l);z(t.$$.fragment,i),i.forEach(E),this.h()},h(){D(l,"class","icon-button")},m(r,i){U(r,l,i),N(t,l,null),e=!0},p(r,i){const s={};i[0]&2048&&(s.i18n=r[11]),i[0]&131072&&(s.value=r[17]),t.$set(s)},i(r){e||(g(t.$$.fragment,r),e=!0)},o(r){w(t.$$.fragment,r),e=!1},d(r){r&&E(l),L(t)}}}function Be(n){let l,t;return l=new Oe({props:{Icon:ml,label:"Close"}}),l.$on("click",n[39]),{c(){j(l.$$.fragment)},l(e){z(l.$$.fragment,e)},m(e,r){N(l,e,r),t=!0},p:cl,i(e){t||(g(l.$$.fragment,e),t=!0)},o(e){w(l.$$.fragment,e),t=!1},d(e){L(l,e)}}}function Tl(n){let l,t,e,r,i,s=n[10]&&Ee(n),c=n[14]&&Ie(n),o=n[9]&&Te(n),h=!n[19]&&Be(n);return{c(){s&&s.c(),l=O(),c&&c.c(),t=O(),o&&o.c(),e=O(),h&&h.c(),r=oe()},l(f){s&&s.l(f),l=R(f),c&&c.l(f),t=R(f),o&&o.l(f),e=R(f),h&&h.l(f),r=oe()},m(f,d){s&&s.m(f,d),U(f,l,d),c&&c.m(f,d),U(f,t,d),o&&o.m(f,d),U(f,e,d),h&&h.m(f,d),U(f,r,d),i=!0},p(f,d){f[10]?s?(s.p(f,d),d[0]&1024&&g(s,1)):(s=Ee(f),s.c(),g(s,1),s.m(l.parentNode,l)):s&&(P(),w(s,1,1,()=>{s=null}),W()),f[14]?c?(c.p(f,d),d[0]&16384&&g(c,1)):(c=Ie(f),c.c(),g(c,1),c.m(t.parentNode,t)):c&&(P(),w(c,1,1,()=>{c=null}),W()),f[9]?o?(o.p(f,d),d[0]&512&&g(o,1)):(o=Te(f),o.c(),g(o,1),o.m(e.parentNode,e)):o&&(P(),w(o,1,1,()=>{o=null}),W()),f[19]?h&&(P(),w(h,1,1,()=>{h=null}),W()):h?(h.p(f,d),d[0]&524288&&g(h,1)):(h=Be(f),h.c(),g(h,1),h.m(r.parentNode,r))},i(f){i||(g(s),g(c),g(o),g(h),i=!0)},o(f){w(s),w(c),w(o),w(h),i=!1},d(f){f&&(E(l),E(t),E(e),E(r)),s&&s.d(f),c&&c.d(f),o&&o.d(f),h&&h.d(f)}}}function Bl(n){let l,t;return l=new me({props:{src:n[24].video.url,"data-testid":"detailed-video",alt:n[24].caption||"",loading:"lazy",loop:!1,is_stream:!1,muted:!1,controls:!0}}),{c(){j(l.$$.fragment)},l(e){z(l.$$.fragment,e)},m(e,r){N(l,e,r),t=!0},p(e,r){const i={};r[0]&16777216&&(i.src=e[24].video.url),r[0]&16777216&&(i.alt=e[24].caption||""),l.$set(i)},i(e){t||(g(l.$$.fragment,e),t=!0)},o(e){w(l.$$.fragment,e),t=!1},d(e){L(l,e)}}}function Dl(n){let l,t;return l=new _e({props:{"data-testid":"detailed-image",src:n[24].image.url,alt:n[24].caption||"",title:n[24].caption||null,class:n[24].caption&&"with-caption",loading:"lazy"}}),{c(){j(l.$$.fragment)},l(e){z(l.$$.fragment,e)},m(e,r){N(l,e,r),t=!0},p(e,r){const i={};r[0]&16777216&&(i.src=e[24].image.url),r[0]&16777216&&(i.alt=e[24].caption||""),r[0]&16777216&&(i.title=e[24].caption||null),r[0]&16777216&&(i.class=e[24].caption&&"with-caption"),l.$set(i)},i(e){t||(g(l.$$.fragment,e),t=!0)},o(e){w(l.$$.fragment,e),t=!1},d(e){L(l,e)}}}function De(n){let l,t=n[24].caption+"",e;return{c(){l=G("caption"),e=Ve(t),this.h()},l(r){l=S(r,"CAPTION",{class:!0});var i=q(l);e=Ce(i,t),i.forEach(E),this.h()},h(){D(l,"class","caption svelte-1atirkn")},m(r,i){U(r,l,i),M(l,e)},p(r,i){i[0]&16777216&&t!==(t=r[24].caption+"")&&Me(e,t)},d(r){r&&E(l)}}}function jl(n){let l,t,e,r;return l=new Pe({}),e=new me({props:{src:n[54].video.url,title:n[54].caption||null,is_stream:!1,"data-testid":"thumbnail "+(n[53]+1),alt:"",loading:"lazy",loop:!1}}),{c(){j(l.$$.fragment),t=O(),j(e.$$.fragment)},l(i){z(l.$$.fragment,i),t=R(i),z(e.$$.fragment,i)},m(i,s){N(l,i,s),U(i,t,s),N(e,i,s),r=!0},p(i,s){const c={};s[0]&131072&&(c.src=i[54].video.url),s[0]&131072&&(c.title=i[54].caption||null),e.$set(c)},i(i){r||(g(l.$$.fragment,i),g(e.$$.fragment,i),r=!0)},o(i){w(l.$$.fragment,i),w(e.$$.fragment,i),r=!1},d(i){i&&E(t),L(l,i),L(e,i)}}}function zl(n){let l,t;return l=new _e({props:{src:n[54].image.url,title:n[54].caption||null,"data-testid":"thumbnail "+(n[53]+1),alt:"",loading:"lazy"}}),{c(){j(l.$$.fragment)},l(e){z(l.$$.fragment,e)},m(e,r){N(l,e,r),t=!0},p(e,r){const i={};r[0]&131072&&(i.src=e[54].image.url),r[0]&131072&&(i.title=e[54].caption||null),l.$set(i)},i(e){t||(g(l.$$.fragment,e),t=!0)},o(e){w(l.$$.fragment,e),t=!1},d(e){L(l,e)}}}function je(n){let l,t,e,r,i,s=n[53],c,o,h;const f=[zl,jl],d=[];function u(b,y){return"image"in b[54]?0:1}t=u(n),e=d[t]=f[t](n);const _=()=>n[41](l,s),p=()=>n[41](null,s);function I(){return n[42](n[53])}return{c(){l=G("button"),e.c(),r=O(),this.h()},l(b){l=S(b,"BUTTON",{class:!0,"aria-label":!0});var y=q(l);e.l(y),r=R(y),y.forEach(E),this.h()},h(){D(l,"class","thumbnail-item thumbnail-small svelte-1atirkn"),D(l,"aria-label",i="Thumbnail "+(n[53]+1)+" of "+n[17].length),C(l,"selected",n[1]===n[53]&&n[13]!=="minimal")},m(b,y){U(b,l,y),d[t].m(l,null),M(l,r),_(),c=!0,o||(h=te(l,"click",I),o=!0)},p(b,y){n=b;let m=t;t=u(n),t===m?d[t].p(n,y):(P(),w(d[m],1,1,()=>{d[m]=null}),W(),e=d[t],e?e.p(n,y):(e=d[t]=f[t](n),e.c()),g(e,1),e.m(l,r)),(!c||y[0]&131072&&i!==(i="Thumbnail "+(n[53]+1)+" of "+n[17].length))&&D(l,"aria-label",i),s!==n[53]&&(p(),s=n[53],_()),(!c||y[0]&8194)&&C(l,"selected",n[1]===n[53]&&n[13]!=="minimal")},i(b){c||(g(e),c=!0)},o(b){w(e),c=!1},d(b){b&&E(l),d[t].d(),p(),o=!1,h()}}}function ze(n){let l,t;return l=new kl({props:{i18n:n[11]}}),l.$on("clear",n[44]),{c(){j(l.$$.fragment)},l(e){z(l.$$.fragment,e)},m(e,r){N(l,e,r),t=!0},p(e,r){const i={};r[0]&2048&&(i.i18n=e[11]),l.$set(i)},i(e){t||(g(l.$$.fragment,e),t=!0)},o(e){w(l.$$.fragment,e),t=!1},d(e){L(l,e)}}}function Nl(n){let l,t,e,r;return l=new Pe({}),e=new me({props:{src:n[51].video.url,title:n[51].caption||null,is_stream:!1,"data-testid":"thumbnail "+(n[53]+1),alt:"",loading:"lazy",loop:!1}}),{c(){j(l.$$.fragment),t=O(),j(e.$$.fragment)},l(i){z(l.$$.fragment,i),t=R(i),z(e.$$.fragment,i)},m(i,s){N(l,i,s),U(i,t,s),N(e,i,s),r=!0},p(i,s){const c={};s[0]&131072&&(c.src=i[51].video.url),s[0]&131072&&(c.title=i[51].caption||null),e.$set(c)},i(i){r||(g(l.$$.fragment,i),g(e.$$.fragment,i),r=!0)},o(i){w(l.$$.fragment,i),w(e.$$.fragment,i),r=!1},d(i){i&&E(t),L(l,i),L(e,i)}}}function Ll(n){let l,t;return l=new _e({props:{alt:n[51].caption||"",src:typeof n[51].image=="string"?n[51].image:n[51].image.url,loading:"lazy"}}),{c(){j(l.$$.fragment)},l(e){z(l.$$.fragment,e)},m(e,r){N(l,e,r),t=!0},p(e,r){const i={};r[0]&131072&&(i.alt=e[51].caption||""),r[0]&131072&&(i.src=typeof e[51].image=="string"?e[51].image:e[51].image.url),l.$set(i)},i(e){t||(g(l.$$.fragment,e),t=!0)},o(e){w(l.$$.fragment,e),t=!1},d(e){L(l,e)}}}function Ne(n){let l,t=n[51].caption+"",e;return{c(){l=G("div"),e=Ve(t),this.h()},l(r){l=S(r,"DIV",{class:!0});var i=q(l);e=Ce(i,t),i.forEach(E),this.h()},h(){D(l,"class","caption-label svelte-1atirkn")},m(r,i){U(r,l,i),M(l,e)},p(r,i){i[0]&131072&&t!==(t=r[51].caption+"")&&Me(e,t)},d(r){r&&E(l)}}}function Le(n){let l,t,e,r,i,s,c,o,h;const f=[Ll,Nl],d=[];function u(I,b){return"image"in I[51]?0:1}t=u(n),e=d[t]=f[t](n);let _=n[51].caption&&Ne(n);function p(){return n[45](n[53])}return{c(){l=G("button"),e.c(),r=O(),_&&_.c(),i=O(),this.h()},l(I){l=S(I,"BUTTON",{class:!0,"aria-label":!0});var b=q(l);e.l(b),r=R(b),_&&_.l(b),i=R(b),b.forEach(E),this.h()},h(){D(l,"class","thumbnail-item thumbnail-lg svelte-1atirkn"),D(l,"aria-label",s="Thumbnail "+(n[53]+1)+" of "+n[17].length),C(l,"selected",n[1]===n[53])},m(I,b){U(I,l,b),d[t].m(l,null),M(l,r),_&&_.m(l,null),M(l,i),c=!0,o||(h=te(l,"click",p),o=!0)},p(I,b){n=I;let y=t;t=u(n),t===y?d[t].p(n,b):(P(),w(d[y],1,1,()=>{d[y]=null}),W(),e=d[t],e?e.p(n,b):(e=d[t]=f[t](n),e.c()),g(e,1),e.m(l,r)),n[51].caption?_?_.p(n,b):(_=Ne(n),_.c(),_.m(l,i)):_&&(_.d(1),_=null),(!c||b[0]&131072&&s!==(s="Thumbnail "+(n[53]+1)+" of "+n[17].length))&&D(l,"aria-label",s),(!c||b[0]&2)&&C(l,"selected",n[1]===n[53])},i(I){c||(g(e),c=!0)},o(I){w(e),c=!1},d(I){I&&E(l),d[t].d(),_&&_.d(),o=!1,h()}}}function Ul(n){let l,t;return l=new Re({}),{c(){j(l.$$.fragment)},l(e){z(l.$$.fragment,e)},m(e,r){N(l,e,r),t=!0},i(e){t||(g(l.$$.fragment,e),t=!0)},o(e){w(l.$$.fragment,e),t=!1},d(e){L(l,e)}}}function Vl(n){let l,t,e,r,i,s,c;il(n[34]);let o=n[2]&&ve(n);const h=[Il,El],f=[];function d(u,_){return u[0]==null||u[17]==null||u[17].length===0?0:1}return t=d(n),e=f[t]=h[t](n),{c(){o&&o.c(),l=O(),e.c(),r=oe()},l(u){o&&o.l(u),l=R(u),e.l(u),r=oe()},m(u,_){o&&o.m(u,_),U(u,l,_),f[t].m(u,_),U(u,r,_),i=!0,s||(c=te(We,"resize",n[34]),s=!0)},p(u,_){u[2]?o?(o.p(u,_),_[0]&4&&g(o,1)):(o=ve(u),o.c(),g(o,1),o.m(l.parentNode,l)):o&&(P(),w(o,1,1,()=>{o=null}),W());let p=t;t=d(u),t===p?f[t].p(u,_):(P(),w(f[p],1,1,()=>{f[p]=null}),W(),e=f[t],e?e.p(u,_):(e=f[t]=h[t](u),e.c()),g(e,1),e.m(r.parentNode,r))},i(u){i||(g(o),g(e),i=!0)},o(u){w(o),w(e),i=!1},d(u){u&&(E(l),E(r)),o&&o.d(u),f[t].d(u),s=!1,c()}}}function Cl(n,l,t){let e,r,i,{show_label:s=!0}=l,{label:c}=l,{value:o=null}=l,{columns:h=[2]}=l,{rows:f=void 0}=l,{height:d="auto"}=l,{preview:u}=l,{allow_preview:_=!0}=l,{object_fit:p="cover"}=l,{show_share_button:I=!1}=l,{show_download_button:b=!1}=l,{i18n:y}=l,{selected_index:m=null}=l,{interactive:ne}=l,{_fetch:Y}=l,{mode:v="normal"}=l,{show_fullscreen_button:k=!0}=l,{display_icon_button_wrapper_top_corner:V=!1}=l,{fullscreen:X=!1}=l,J=!1,T;const K=rl();let x=!0,B=null,ae=o;m==null&&u&&(o!=null&&o.length)&&(m=0);let ue=m;function he(a){const H=a.target,Q=a.offsetX,$=H.offsetWidth/2;Q<$?t(1,m=e):t(1,m=r)}function Ae(a){switch(a.code){case"Escape":a.preventDefault(),t(1,m=null);break;case"ArrowLeft":a.preventDefault(),t(1,m=e);break;case"ArrowRight":a.preventDefault(),t(1,m=r);break}}let Z=[],A;async function Fe(a){var pe;if(typeof a!="number"||(await al(),Z[a]===void 0))return;(pe=Z[a])==null||pe.focus();const{left:H,width:Q}=A.getBoundingClientRect(),{left:ie,width:$}=Z[a].getBoundingClientRect(),le=ie-H+$/2-Q/2+A.scrollLeft;A&&typeof A.scrollTo=="function"&&A.scrollTo({left:le<0?0:le,behavior:"smooth"})}let ge=0;async function be(a,H){let Q;try{Q=await Y(a)}catch(le){if(le instanceof TypeError){window.open(a,"_blank","noreferrer");return}throw le}const ie=await Q.blob(),$=URL.createObjectURL(ie),re=document.createElement("a");re.href=$,re.download=H,re.click(),URL.revokeObjectURL($)}let de=!1;function ee(){A&&t(23,de=A.scrollWidth>A.clientWidth)}ol(()=>(ee(),document.addEventListener("fullscreenchange",()=>{t(19,J=!!document.fullscreenElement)}),window.addEventListener("resize",ee),()=>window.removeEventListener("resize",ee)));function Ge(){t(22,ge=We.innerHeight)}const Se=()=>{const a="image"in i?i==null?void 0:i.image:i==null?void 0:i.video;if(a==null)return;const{url:H,orig_name:Q}=a;H&&be(H,Q??"image")};function qe(a){se.call(this,n,a)}function He(a){se.call(this,n,a)}function Xe(a){se.call(this,n,a)}const Je=()=>{t(1,m=null),K("preview_close")},Ke=a=>he(a);function Qe(a,H){ce[a?"unshift":"push"](()=>{Z[H]=a,t(21,Z)})}const Ye=a=>t(1,m=a);function Ze(a){ce[a?"unshift":"push"](()=>{A=a,t(18,A)})}const $e=()=>t(0,o=[]),xe=a=>{m===null&&_&&K("preview_open"),t(1,m=a)};function el(a){ce[a?"unshift":"push"](()=>{T=a,t(20,T)})}return n.$$set=a=>{"show_label"in a&&t(2,s=a.show_label),"label"in a&&t(3,c=a.label),"value"in a&&t(0,o=a.value),"columns"in a&&t(4,h=a.columns),"rows"in a&&t(5,f=a.rows),"height"in a&&t(6,d=a.height),"preview"in a&&t(29,u=a.preview),"allow_preview"in a&&t(7,_=a.allow_preview),"object_fit"in a&&t(8,p=a.object_fit),"show_share_button"in a&&t(9,I=a.show_share_button),"show_download_button"in a&&t(10,b=a.show_download_button),"i18n"in a&&t(11,y=a.i18n),"selected_index"in a&&t(1,m=a.selected_index),"interactive"in a&&t(12,ne=a.interactive),"_fetch"in a&&t(30,Y=a._fetch),"mode"in a&&t(13,v=a.mode),"show_fullscreen_button"in a&&t(14,k=a.show_fullscreen_button),"display_icon_button_wrapper_top_corner"in a&&t(15,V=a.display_icon_button_wrapper_top_corner),"fullscreen"in a&&t(16,X=a.fullscreen)},n.$$.update=()=>{n.$$.dirty[0]&1|n.$$.dirty[1]&1&&t(31,x=o==null||o.length===0?!0:x),n.$$.dirty[0]&1&&t(17,B=o==null?null:o.map(a=>"video"in a?{video:a.video,caption:a.caption}:"image"in a?{image:a.image,caption:a.caption}:{})),n.$$.dirty[0]&536870915|n.$$.dirty[1]&3&&(vl(ae,o)||(x?(t(1,m=u&&(o!=null&&o.length)?0:null),t(31,x=!1)):m!==null&&o!==null?t(1,m=Math.max(0,Math.min(m,o.length-1))):t(1,m=null),K("change"),t(32,ae=o))),n.$$.dirty[0]&131074|n.$$.dirty[1]&4&&m!==ue&&(t(33,ue=m),m!==null&&(B!=null&&t(1,m=Math.max(0,Math.min(m,B.length-1))),K("select",{index:m,value:B==null?void 0:B[m]}))),n.$$.dirty[0]&131074&&(e=((m??0)+((B==null?void 0:B.length)??0)-1)%((B==null?void 0:B.length)??0)),n.$$.dirty[0]&131074&&(r=((m??0)+1)%((B==null?void 0:B.length)??0)),n.$$.dirty[0]&130&&_&&Fe(m),n.$$.dirty[0]&131074&&t(24,i=m!=null&&B!=null?B[m]:null),n.$$.dirty[0]&131072&&ee(),n.$$.dirty[0]&262144&&A&&ee()},[o,m,s,c,h,f,d,_,p,I,b,y,ne,v,k,V,X,B,A,J,T,Z,ge,de,i,K,he,Ae,be,u,Y,x,ae,ue,Ge,Se,qe,He,Xe,Je,Ke,Qe,Ye,Ze,$e,xe,el]}class Yl extends ll{constructor(l){super(),tl(this,l,Cl,Vl,nl,{show_label:2,label:3,value:0,columns:4,rows:5,height:6,preview:29,allow_preview:7,object_fit:8,show_share_button:9,show_download_button:10,i18n:11,selected_index:1,interactive:12,_fetch:30,mode:13,show_fullscreen_button:14,display_icon_button_wrapper_top_corner:15,fullscreen:16},null,[-1,-1])}}export{Yl as default};
//# sourceMappingURL=Gallery.BgdW0y-K.js.map
