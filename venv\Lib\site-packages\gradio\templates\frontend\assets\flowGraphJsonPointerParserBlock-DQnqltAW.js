import{j as b,F as d,R as p}from"./declarationMapper-BZjsjg7g.js";import{C as P,aS as A,R as F}from"./index-Dpxo-yl_.js";import{F as j}from"./flowGraphCachedOperationBlock-DbEh2T66.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./KHR_interactivity-DTxiAnOo.js";import"./objectModelMapping-BR4RdEzn.js";const f=new RegExp(/\/\{(\w+)\}\//g);class _{constructor(t,n){this.path=t,this.ownerBlock=n,this.templatedInputs=[];let r=f.exec(t);const e=new Set;for(;r;){const[,o]=r;if(e.has(o))throw new Error("Duplicate template variable detected.");e.add(o),this.templatedInputs.push(n.registerDataInput(o,b,new d(0))),r=f.exec(t)}}getAccessor(t,n){let r=this.path;for(const e of this.templatedInputs){const o=e.getValue(n).value;if(typeof o!="number"||o<0)throw new Error("Invalid value for templated input.");r=r.replace(`{${e.name}}`,o.toString())}return t.convert(r)}}class I extends j{constructor(t){super(p,t),this.config=t,this.object=this.registerDataOutput("object",p),this.propertyName=this.registerDataOutput("propertyName",p),this.setterFunction=this.registerDataOutput("setFunction",p,this._setPropertyValue.bind(this)),this.getterFunction=this.registerDataOutput("getFunction",p,this._getPropertyValue.bind(this)),this.generateAnimationsFunction=this.registerDataOutput("generateAnimationsFunction",p,this._getInterpolationAnimationPropertyInfo.bind(this)),this.templateComponent=new _(t.jsonPointer,this)}_doOperation(t){const n=this.templateComponent.getAccessor(this.config.pathConverter,t),r=n.info.get(n.object),e=n.info.getTarget?.(n.object),o=n.info.getPropertyName?.[0](n.object);if(e)this.object.setValue(e,t),o&&this.propertyName.setValue(o,t);else throw new Error("Object is undefined");return r}_setPropertyValue(t,n,r,e){const o=this.templateComponent.getAccessor(this.config.pathConverter,e),c=o.info.type;c.startsWith("Color")&&(r=C(r,c)),o.info.set?.(r,o.object)}_getPropertyValue(t,n,r){const e=this.templateComponent.getAccessor(this.config.pathConverter,r);return e.info.get(e.object)}_getInterpolationAnimationPropertyInfo(t,n,r){const e=this.templateComponent.getAccessor(this.config.pathConverter,r);return(o,c,y,h)=>{const l=[],m=e.info.type;return m.startsWith("Color")&&(o=o.map(i=>({frame:i.frame,value:C(i.value,m)}))),e.info.interpolation?.forEach((i,u)=>{const w=e.info.getPropertyName?.[u](e.object)||"Animation-interpolation-"+u;let g=o;y!==i.type&&(g=o.map(s=>({frame:s.frame,value:i.getValue(void 0,s.value.asArray?s.value.asArray():[s.value],0,1)}))),i.buildAnimations(e.object,w,60,g).forEach(s=>{h&&s.babylonAnimation.setEasingFunction(h),l.push(s.babylonAnimation)})}),l}}getClassName(){return"FlowGraphJsonPointerParserBlock"}}function C(a,t){return a.getClassName().startsWith("Color")?a:t==="Color3"?new P(a.x,a.y,a.z):t==="Color4"?new A(a.x,a.y,a.z,a.w):a}F("FlowGraphJsonPointerParserBlock",I);export{I as FlowGraphJsonPointerParserBlock};
//# sourceMappingURL=flowGraphJsonPointerParserBlock-DQnqltAW.js.map
