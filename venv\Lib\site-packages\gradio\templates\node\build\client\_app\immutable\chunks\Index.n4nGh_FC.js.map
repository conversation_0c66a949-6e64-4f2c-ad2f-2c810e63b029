{"version": 3, "file": "Index.n4nGh_FC.js", "sources": ["../../../../../../../json/Index.svelte"], "sourcesContent": ["<script lang=\"ts\" context=\"module\">\n\texport { default as BaseJSON } from \"./shared/JSON.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport JSO<PERSON> from \"./shared/JSON.svelte\";\n\timport { Block, BlockLabel } from \"@gradio/atoms\";\n\timport { JSON as JSONIcon } from \"@gradio/icons\";\n\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: any;\n\tlet old_value: any;\n\texport let loading_status: LoadingStatus;\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tclear_status: LoadingStatus;\n\t}>;\n\texport let open = false;\n\texport let theme_mode: \"system\" | \"light\" | \"dark\";\n\texport let show_indices: boolean;\n\texport let height: number | string | undefined;\n\texport let min_height: number | string | undefined;\n\texport let max_height: number | string | undefined;\n\n\t$: {\n\t\tif (value !== old_value) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t}\n\t}\n\n\tlet label_height = 0;\n</script>\n\n<Block\n\t{visible}\n\ttest_id=\"json\"\n\t{elem_id}\n\t{elem_classes}\n\t{container}\n\t{scale}\n\t{min_width}\n\tpadding={false}\n\tallow_overflow={true}\n\toverflow_behavior=\"auto\"\n\t{height}\n\t{min_height}\n\t{max_height}\n>\n\t<div bind:clientHeight={label_height}>\n\t\t{#if label}\n\t\t\t<BlockLabel\n\t\t\t\tIcon={JSONIcon}\n\t\t\t\t{show_label}\n\t\t\t\t{label}\n\t\t\t\tfloat={false}\n\t\t\t\tdisable={container === false}\n\t\t\t/>\n\t\t{/if}\n\t</div>\n\n\t<StatusTracker\n\t\tautoscroll={gradio.autoscroll}\n\t\ti18n={gradio.i18n}\n\t\t{...loading_status}\n\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t/>\n\n\t<JSON {value} {open} {theme_mode} {show_indices} {label_height} />\n</Block>\n"], "names": ["JSONIcon", "ctx", "dirty", "blocklabel_changes", "create_if_block", "insert_hydration", "target", "div", "anchor", "elem_id", "$$props", "elem_classes", "visible", "value", "old_value", "loading_status", "label", "show_label", "container", "scale", "min_width", "gradio", "open", "theme_mode", "show_indices", "height", "min_height", "max_height", "label_height", "clear_status_handler"], "mappings": "gsBA+DUA,mCAGC,GACE,QAAAC,OAAc,6IAAdC,EAAA,MAAAC,EAAA,QAAAF,OAAc,iIANpBA,EAAK,CAAA,GAAAG,EAAAH,CAAA,YAYE,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,ibAfnBI,EAUKC,EAAAC,EAAAC,CAAA,uFATCP,EAAK,CAAA,8HAYE,WAAAA,MAAO,YACbC,EAAA,MAAA,CAAA,KAAAD,MAAO,IAAI,UACbA,EAAc,CAAA,CAAA,gfAtBV,kBACO,8kBAzCL,QAAAQ,EAAU,EAAA,EAAAC,EACV,CAAA,aAAAC,EAAA,EAAA,EAAAD,GACA,QAAAE,EAAU,EAAA,EAAAF,EACV,CAAA,MAAAG,CAAA,EAAAH,EACPI,EACO,CAAA,eAAAC,CAAA,EAAAL,EACA,CAAA,MAAAM,CAAA,EAAAN,EACA,CAAA,WAAAO,CAAA,EAAAP,GACA,UAAAQ,EAAY,EAAA,EAAAR,GACZ,MAAAS,EAAuB,IAAA,EAAAT,GACvB,UAAAU,EAAgC,MAAA,EAAAV,EAChC,CAAA,OAAAW,CAAA,EAAAX,GAIA,KAAAY,EAAO,EAAA,EAAAZ,EACP,CAAA,WAAAa,CAAA,EAAAb,EACA,CAAA,aAAAc,CAAA,EAAAd,EACA,CAAA,OAAAe,CAAA,EAAAf,EACA,CAAA,WAAAgB,CAAA,EAAAhB,EACA,CAAA,WAAAiB,CAAA,EAAAjB,EASPkB,EAAe,eAkBKA,EAAY,KAAA,qBAgBZ,MAAAC,EAAA,IAAAR,EAAO,SAAS,eAAgBN,CAAc,6oBAxCjEF,IAAUC,SACbA,EAAYD,CAAA,EACZQ,EAAO,SAAS,QAAQ"}