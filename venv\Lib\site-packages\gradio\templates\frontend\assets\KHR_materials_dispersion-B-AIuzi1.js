import{ar as a,an as d,ao as u}from"./index-Dpxo-yl_.js";import{GLTFLoader as m}from"./glTFLoader-9Z3KGax5.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./bone-kZWM5-u7.js";import"./rawTexture-DmvUfjqF.js";import"./assetContainer-BRzQBugc.js";import"./objectModelMapping-BR4RdEzn.js";const o="KHR_materials_dispersion";class l{constructor(s){this.name=o,this.order=174,this._loader=s,this.enabled=this._loader.isExtensionUsed(o)}dispose(){this._loader=null}loadMaterialPropertiesAsync(s,e,r){return m.LoadExtensionAsync(s,e,this.name,(i,p)=>{const t=new Array;return t.push(this._loader.loadMaterialPropertiesAsync(s,e,r)),t.push(this._loadDispersionPropertiesAsync(i,e,r,p)),Promise.all(t).then(()=>{})})}_loadDispersionPropertiesAsync(s,e,r,i){if(!(r instanceof a))throw new Error(`${s}: Material type not supported`);return!r.subSurface.isRefractionEnabled||!i.dispersion||(r.subSurface.isDispersionEnabled=!0,r.subSurface.dispersion=i.dispersion),Promise.resolve()}}d(o);u(o,!0,n=>new l(n));export{l as KHR_materials_dispersion};
//# sourceMappingURL=KHR_materials_dispersion-B-AIuzi1.js.map
