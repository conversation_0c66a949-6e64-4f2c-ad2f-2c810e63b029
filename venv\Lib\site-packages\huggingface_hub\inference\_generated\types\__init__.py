# This file is auto-generated by `utils/generate_inference_types.py`.
# Do not modify it manually.
#
# ruff: noqa: F401

from .audio_classification import (
    AudioClassificationInput,
    AudioClassificationOutputElement,
    AudioClassificationOutputTransform,
    AudioClassificationParameters,
)
from .audio_to_audio import AudioToAudioInput, AudioToAudioOutputElement
from .automatic_speech_recognition import (
    AutomaticSpeechRecognitionEarlyStoppingEnum,
    AutomaticSpeechRecognitionGenerationParameters,
    AutomaticSpeechRecognitionInput,
    AutomaticSpeechRecognitionOutput,
    AutomaticSpeechRecognitionOutputChunk,
    AutomaticSpeechRecognitionParameters,
)
from .base import BaseInferenceType
from .chat_completion import (
    ChatCompletionInput,
    ChatCompletionInputFunctionDefinition,
    ChatCompletionInputFunctionName,
    ChatCompletionInputGrammarType,
    ChatCompletionInputJSONSchema,
    ChatCompletionInputMessage,
    ChatCompletionInputMessageChunk,
    ChatCompletionInputMessageChunkType,
    ChatCompletionInputResponseFormatJSONObject,
    ChatCompletionInputResponseFormatJSONSchema,
    ChatCompletionInputResponseFormatText,
    ChatCompletionInputStreamOptions,
    ChatCompletionInputTool,
    ChatCompletionInputToolCall,
    ChatCompletionInputToolChoiceClass,
    ChatCompletionInputToolChoiceEnum,
    ChatCompletionInputURL,
    ChatCompletionOutput,
    ChatCompletionOutputComplete,
    ChatCompletionOutputFunctionDefinition,
    ChatCompletionOutputLogprob,
    ChatCompletionOutputLogprobs,
    ChatCompletionOutputMessage,
    ChatCompletionOutputToolCall,
    ChatCompletionOutputTopLogprob,
    ChatCompletionOutputUsage,
    ChatCompletionStreamOutput,
    ChatCompletionStreamOutputChoice,
    ChatCompletionStreamOutputDelta,
    ChatCompletionStreamOutputDeltaToolCall,
    ChatCompletionStreamOutputFunction,
    ChatCompletionStreamOutputLogprob,
    ChatCompletionStreamOutputLogprobs,
    ChatCompletionStreamOutputTopLogprob,
    ChatCompletionStreamOutputUsage,
)
from .depth_estimation import DepthEstimationInput, DepthEstimationOutput
from .document_question_answering import (
    DocumentQuestionAnsweringInput,
    DocumentQuestionAnsweringInputData,
    DocumentQuestionAnsweringOutputElement,
    DocumentQuestionAnsweringParameters,
)
from .feature_extraction import FeatureExtractionInput, FeatureExtractionInputTruncationDirection
from .fill_mask import FillMaskInput, FillMaskOutputElement, FillMaskParameters
from .image_classification import (
    ImageClassificationInput,
    ImageClassificationOutputElement,
    ImageClassificationOutputTransform,
    ImageClassificationParameters,
)
from .image_segmentation import (
    ImageSegmentationInput,
    ImageSegmentationOutputElement,
    ImageSegmentationParameters,
    ImageSegmentationSubtask,
)
from .image_to_image import ImageToImageInput, ImageToImageOutput, ImageToImageParameters, ImageToImageTargetSize
from .image_to_text import (
    ImageToTextEarlyStoppingEnum,
    ImageToTextGenerationParameters,
    ImageToTextInput,
    ImageToTextOutput,
    ImageToTextParameters,
)
from .image_to_video import ImageToVideoInput, ImageToVideoOutput, ImageToVideoParameters, ImageToVideoTargetSize
from .object_detection import (
    ObjectDetectionBoundingBox,
    ObjectDetectionInput,
    ObjectDetectionOutputElement,
    ObjectDetectionParameters,
)
from .question_answering import (
    QuestionAnsweringInput,
    QuestionAnsweringInputData,
    QuestionAnsweringOutputElement,
    QuestionAnsweringParameters,
)
from .sentence_similarity import SentenceSimilarityInput, SentenceSimilarityInputData
from .summarization import (
    SummarizationInput,
    SummarizationOutput,
    SummarizationParameters,
    SummarizationTruncationStrategy,
)
from .table_question_answering import (
    Padding,
    TableQuestionAnsweringInput,
    TableQuestionAnsweringInputData,
    TableQuestionAnsweringOutputElement,
    TableQuestionAnsweringParameters,
)
from .text2text_generation import (
    Text2TextGenerationInput,
    Text2TextGenerationOutput,
    Text2TextGenerationParameters,
    Text2TextGenerationTruncationStrategy,
)
from .text_classification import (
    TextClassificationInput,
    TextClassificationOutputElement,
    TextClassificationOutputTransform,
    TextClassificationParameters,
)
from .text_generation import (
    TextGenerationInput,
    TextGenerationInputGenerateParameters,
    TextGenerationInputGrammarType,
    TextGenerationOutput,
    TextGenerationOutputBestOfSequence,
    TextGenerationOutputDetails,
    TextGenerationOutputFinishReason,
    TextGenerationOutputPrefillToken,
    TextGenerationOutputToken,
    TextGenerationStreamOutput,
    TextGenerationStreamOutputStreamDetails,
    TextGenerationStreamOutputToken,
    TypeEnum,
)
from .text_to_audio import (
    TextToAudioEarlyStoppingEnum,
    TextToAudioGenerationParameters,
    TextToAudioInput,
    TextToAudioOutput,
    TextToAudioParameters,
)
from .text_to_image import TextToImageInput, TextToImageOutput, TextToImageParameters
from .text_to_speech import (
    TextToSpeechEarlyStoppingEnum,
    TextToSpeechGenerationParameters,
    TextToSpeechInput,
    TextToSpeechOutput,
    TextToSpeechParameters,
)
from .text_to_video import TextToVideoInput, TextToVideoOutput, TextToVideoParameters
from .token_classification import (
    TokenClassificationAggregationStrategy,
    TokenClassificationInput,
    TokenClassificationOutputElement,
    TokenClassificationParameters,
)
from .translation import TranslationInput, TranslationOutput, TranslationParameters, TranslationTruncationStrategy
from .video_classification import (
    VideoClassificationInput,
    VideoClassificationOutputElement,
    VideoClassificationOutputTransform,
    VideoClassificationParameters,
)
from .visual_question_answering import (
    VisualQuestionAnsweringInput,
    VisualQuestionAnsweringInputData,
    VisualQuestionAnsweringOutputElement,
    VisualQuestionAnsweringParameters,
)
from .zero_shot_classification import (
    ZeroShotClassificationInput,
    ZeroShotClassificationOutputElement,
    ZeroShotClassificationParameters,
)
from .zero_shot_image_classification import (
    ZeroShotImageClassificationInput,
    ZeroShotImageClassificationOutputElement,
    ZeroShotImageClassificationParameters,
)
from .zero_shot_object_detection import (
    ZeroShotObjectDetectionBoundingBox,
    ZeroShotObjectDetectionInput,
    ZeroShotObjectDetectionOutputElement,
    ZeroShotObjectDetectionParameters,
)
