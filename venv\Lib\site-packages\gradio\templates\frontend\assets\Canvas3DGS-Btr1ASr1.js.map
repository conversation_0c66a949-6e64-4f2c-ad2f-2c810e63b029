{"version": 3, "file": "Canvas3DGS-Btr1ASr1.js", "sources": ["../../../../node_modules/.pnpm/gsplat@1.2.9/node_modules/gsplat/dist/index.es.js", "../../../../js/model3D/shared/Canvas3DGS.svelte"], "sourcesContent": ["class R {\n  constructor(t = 0, n = 0, i = 0) {\n    this.x = t, this.y = n, this.z = i;\n  }\n  equals(t) {\n    return !(this.x !== t.x || this.y !== t.y || this.z !== t.z);\n  }\n  add(t) {\n    return typeof t == \"number\" ? new R(this.x + t, this.y + t, this.z + t) : new R(this.x + t.x, this.y + t.y, this.z + t.z);\n  }\n  subtract(t) {\n    return typeof t == \"number\" ? new R(this.x - t, this.y - t, this.z - t) : new R(this.x - t.x, this.y - t.y, this.z - t.z);\n  }\n  multiply(t) {\n    return typeof t == \"number\" ? new R(this.x * t, this.y * t, this.z * t) : t instanceof R ? new R(this.x * t.x, this.y * t.y, this.z * t.z) : new R(\n      this.x * t.buffer[0] + this.y * t.buffer[4] + this.z * t.buffer[8] + t.buffer[12],\n      this.x * t.buffer[1] + this.y * t.buffer[5] + this.z * t.buffer[9] + t.buffer[13],\n      this.x * t.buffer[2] + this.y * t.buffer[6] + this.z * t.buffer[10] + t.buffer[14]\n    );\n  }\n  divide(t) {\n    return typeof t == \"number\" ? new R(this.x / t, this.y / t, this.z / t) : new R(this.x / t.x, this.y / t.y, this.z / t.z);\n  }\n  cross(t) {\n    const n = this.y * t.z - this.z * t.y, i = this.z * t.x - this.x * t.z, e = this.x * t.y - this.y * t.x;\n    return new R(n, i, e);\n  }\n  dot(t) {\n    return this.x * t.x + this.y * t.y + this.z * t.z;\n  }\n  lerp(t, n) {\n    return new R(this.x + (t.x - this.x) * n, this.y + (t.y - this.y) * n, this.z + (t.z - this.z) * n);\n  }\n  min(t) {\n    return new R(Math.min(this.x, t.x), Math.min(this.y, t.y), Math.min(this.z, t.z));\n  }\n  max(t) {\n    return new R(Math.max(this.x, t.x), Math.max(this.y, t.y), Math.max(this.z, t.z));\n  }\n  getComponent(t) {\n    switch (t) {\n      case 0:\n        return this.x;\n      case 1:\n        return this.y;\n      case 2:\n        return this.z;\n      default:\n        throw new Error(`Invalid component index: ${t}`);\n    }\n  }\n  minComponent() {\n    return this.x < this.y && this.x < this.z ? 0 : this.y < this.z ? 1 : 2;\n  }\n  maxComponent() {\n    return this.x > this.y && this.x > this.z ? 0 : this.y > this.z ? 1 : 2;\n  }\n  magnitude() {\n    return Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);\n  }\n  distanceTo(t) {\n    return Math.sqrt((this.x - t.x) ** 2 + (this.y - t.y) ** 2 + (this.z - t.z) ** 2);\n  }\n  normalize() {\n    const t = this.magnitude();\n    return new R(this.x / t, this.y / t, this.z / t);\n  }\n  flat() {\n    return [this.x, this.y, this.z];\n  }\n  clone() {\n    return new R(this.x, this.y, this.z);\n  }\n  toString() {\n    return `[${this.flat().join(\", \")}]`;\n  }\n  static One(t = 1) {\n    return new R(t, t, t);\n  }\n}\nclass y {\n  constructor(t = 0, n = 0, i = 0, e = 1) {\n    this.x = t, this.y = n, this.z = i, this.w = e;\n  }\n  equals(t) {\n    return !(this.x !== t.x || this.y !== t.y || this.z !== t.z || this.w !== t.w);\n  }\n  normalize() {\n    const t = Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z + this.w * this.w);\n    return new y(this.x / t, this.y / t, this.z / t, this.w / t);\n  }\n  multiply(t) {\n    const n = this.w, i = this.x, e = this.y, A = this.z, o = t.w, s = t.x, r = t.y, Q = t.z;\n    return new y(\n      n * s + i * o + e * Q - A * r,\n      n * r - i * Q + e * o + A * s,\n      n * Q + i * r - e * s + A * o,\n      n * o - i * s - e * r - A * Q\n    );\n  }\n  inverse() {\n    const t = this.x * this.x + this.y * this.y + this.z * this.z + this.w * this.w;\n    return new y(-this.x / t, -this.y / t, -this.z / t, this.w / t);\n  }\n  apply(t) {\n    const n = new y(t.x, t.y, t.z, 0), i = new y(-this.x, -this.y, -this.z, this.w), e = this.multiply(n).multiply(i);\n    return new R(e.x, e.y, e.z);\n  }\n  flat() {\n    return [this.x, this.y, this.z, this.w];\n  }\n  clone() {\n    return new y(this.x, this.y, this.z, this.w);\n  }\n  static FromEuler(t) {\n    const n = t.x / 2, i = t.y / 2, e = t.z / 2, A = Math.cos(i), o = Math.sin(i), s = Math.cos(n), r = Math.sin(n), Q = Math.cos(e), I = Math.sin(e);\n    return new y(\n      A * r * Q + o * s * I,\n      o * s * Q - A * r * I,\n      A * s * I - o * r * Q,\n      A * s * Q + o * r * I\n    );\n  }\n  toEuler() {\n    const t = 2 * (this.w * this.x + this.y * this.z), n = 1 - 2 * (this.x * this.x + this.y * this.y), i = Math.atan2(t, n);\n    let e;\n    const A = 2 * (this.w * this.y - this.z * this.x);\n    Math.abs(A) >= 1 ? e = Math.sign(A) * Math.PI / 2 : e = Math.asin(A);\n    const o = 2 * (this.w * this.z + this.x * this.y), s = 1 - 2 * (this.y * this.y + this.z * this.z), r = Math.atan2(o, s);\n    return new R(i, e, r);\n  }\n  static FromMatrix3(t) {\n    const n = t.buffer, i = n[0] + n[4] + n[8];\n    let e, A, o, s;\n    if (i > 0) {\n      const r = 0.5 / Math.sqrt(i + 1);\n      s = 0.25 / r, e = (n[7] - n[5]) * r, A = (n[2] - n[6]) * r, o = (n[3] - n[1]) * r;\n    } else if (n[0] > n[4] && n[0] > n[8]) {\n      const r = 2 * Math.sqrt(1 + n[0] - n[4] - n[8]);\n      s = (n[7] - n[5]) / r, e = 0.25 * r, A = (n[1] + n[3]) / r, o = (n[2] + n[6]) / r;\n    } else if (n[4] > n[8]) {\n      const r = 2 * Math.sqrt(1 + n[4] - n[0] - n[8]);\n      s = (n[2] - n[6]) / r, e = (n[1] + n[3]) / r, A = 0.25 * r, o = (n[5] + n[7]) / r;\n    } else {\n      const r = 2 * Math.sqrt(1 + n[8] - n[0] - n[4]);\n      s = (n[3] - n[1]) / r, e = (n[2] + n[6]) / r, A = (n[5] + n[7]) / r, o = 0.25 * r;\n    }\n    return new y(e, A, o, s);\n  }\n  static FromAxisAngle(t, n) {\n    const i = n / 2, e = Math.sin(i), A = Math.cos(i);\n    return new y(t.x * e, t.y * e, t.z * e, A);\n  }\n  static LookRotation(t) {\n    const n = new R(0, 0, 1), i = n.dot(t);\n    if (Math.abs(i - -1) < 1e-6)\n      return new y(0, 1, 0, Math.PI);\n    if (Math.abs(i - 1) < 1e-6)\n      return new y();\n    const e = Math.acos(i), A = n.cross(t).normalize();\n    return y.FromAxisAngle(A, e);\n  }\n  toString() {\n    return `[${this.flat().join(\", \")}]`;\n  }\n}\nclass Ft {\n  constructor() {\n    const t = /* @__PURE__ */ new Map();\n    this.addEventListener = (n, i) => {\n      t.has(n) || t.set(n, /* @__PURE__ */ new Set()), t.get(n).add(i);\n    }, this.removeEventListener = (n, i) => {\n      t.has(n) && t.get(n).delete(i);\n    }, this.hasEventListener = (n, i) => t.has(n) ? t.get(n).has(i) : !1, this.dispatchEvent = (n) => {\n      if (t.has(n.type))\n        for (const i of t.get(n.type))\n          i(n);\n    };\n  }\n}\nclass K {\n  // prettier-ignore\n  constructor(t = 1, n = 0, i = 0, e = 0, A = 0, o = 1, s = 0, r = 0, Q = 0, I = 0, d = 1, a = 0, U = 0, F = 0, g = 0, B = 1) {\n    this.buffer = [\n      t,\n      n,\n      i,\n      e,\n      A,\n      o,\n      s,\n      r,\n      Q,\n      I,\n      d,\n      a,\n      U,\n      F,\n      g,\n      B\n    ];\n  }\n  equals(t) {\n    if (this.buffer.length !== t.buffer.length)\n      return !1;\n    if (this.buffer === t.buffer)\n      return !0;\n    for (let n = 0; n < this.buffer.length; n++)\n      if (this.buffer[n] !== t.buffer[n])\n        return !1;\n    return !0;\n  }\n  multiply(t) {\n    const n = this.buffer, i = t.buffer;\n    return new K(\n      i[0] * n[0] + i[1] * n[4] + i[2] * n[8] + i[3] * n[12],\n      i[0] * n[1] + i[1] * n[5] + i[2] * n[9] + i[3] * n[13],\n      i[0] * n[2] + i[1] * n[6] + i[2] * n[10] + i[3] * n[14],\n      i[0] * n[3] + i[1] * n[7] + i[2] * n[11] + i[3] * n[15],\n      i[4] * n[0] + i[5] * n[4] + i[6] * n[8] + i[7] * n[12],\n      i[4] * n[1] + i[5] * n[5] + i[6] * n[9] + i[7] * n[13],\n      i[4] * n[2] + i[5] * n[6] + i[6] * n[10] + i[7] * n[14],\n      i[4] * n[3] + i[5] * n[7] + i[6] * n[11] + i[7] * n[15],\n      i[8] * n[0] + i[9] * n[4] + i[10] * n[8] + i[11] * n[12],\n      i[8] * n[1] + i[9] * n[5] + i[10] * n[9] + i[11] * n[13],\n      i[8] * n[2] + i[9] * n[6] + i[10] * n[10] + i[11] * n[14],\n      i[8] * n[3] + i[9] * n[7] + i[10] * n[11] + i[11] * n[15],\n      i[12] * n[0] + i[13] * n[4] + i[14] * n[8] + i[15] * n[12],\n      i[12] * n[1] + i[13] * n[5] + i[14] * n[9] + i[15] * n[13],\n      i[12] * n[2] + i[13] * n[6] + i[14] * n[10] + i[15] * n[14],\n      i[12] * n[3] + i[13] * n[7] + i[14] * n[11] + i[15] * n[15]\n    );\n  }\n  clone() {\n    const t = this.buffer;\n    return new K(\n      t[0],\n      t[1],\n      t[2],\n      t[3],\n      t[4],\n      t[5],\n      t[6],\n      t[7],\n      t[8],\n      t[9],\n      t[10],\n      t[11],\n      t[12],\n      t[13],\n      t[14],\n      t[15]\n    );\n  }\n  determinant() {\n    const t = this.buffer;\n    return t[12] * t[9] * t[6] * t[3] - t[8] * t[13] * t[6] * t[3] - t[12] * t[5] * t[10] * t[3] + t[4] * t[13] * t[10] * t[3] + t[8] * t[5] * t[14] * t[3] - t[4] * t[9] * t[14] * t[3] - t[12] * t[9] * t[2] * t[7] + t[8] * t[13] * t[2] * t[7] + t[12] * t[1] * t[10] * t[7] - t[0] * t[13] * t[10] * t[7] - t[8] * t[1] * t[14] * t[7] + t[0] * t[9] * t[14] * t[7] + t[12] * t[5] * t[2] * t[11] - t[4] * t[13] * t[2] * t[11] - t[12] * t[1] * t[6] * t[11] + t[0] * t[13] * t[6] * t[11] + t[4] * t[1] * t[14] * t[11] - t[0] * t[5] * t[14] * t[11] - t[8] * t[5] * t[2] * t[15] + t[4] * t[9] * t[2] * t[15] + t[8] * t[1] * t[6] * t[15] - t[0] * t[9] * t[6] * t[15] - t[4] * t[1] * t[10] * t[15] + t[0] * t[5] * t[10] * t[15];\n  }\n  invert() {\n    const t = this.buffer, n = this.determinant();\n    if (n === 0)\n      throw new Error(\"Matrix is not invertible.\");\n    const i = 1 / n;\n    return new K(\n      i * (t[5] * t[10] * t[15] - t[5] * t[11] * t[14] - t[9] * t[6] * t[15] + t[9] * t[7] * t[14] + t[13] * t[6] * t[11] - t[13] * t[7] * t[10]),\n      i * (-t[1] * t[10] * t[15] + t[1] * t[11] * t[14] + t[9] * t[2] * t[15] - t[9] * t[3] * t[14] - t[13] * t[2] * t[11] + t[13] * t[3] * t[10]),\n      i * (t[1] * t[6] * t[15] - t[1] * t[7] * t[14] - t[5] * t[2] * t[15] + t[5] * t[3] * t[14] + t[13] * t[2] * t[7] - t[13] * t[3] * t[6]),\n      i * (-t[1] * t[6] * t[11] + t[1] * t[7] * t[10] + t[5] * t[2] * t[11] - t[5] * t[3] * t[10] - t[9] * t[2] * t[7] + t[9] * t[3] * t[6]),\n      i * (-t[4] * t[10] * t[15] + t[4] * t[11] * t[14] + t[8] * t[6] * t[15] - t[8] * t[7] * t[14] - t[12] * t[6] * t[11] + t[12] * t[7] * t[10]),\n      i * (t[0] * t[10] * t[15] - t[0] * t[11] * t[14] - t[8] * t[2] * t[15] + t[8] * t[3] * t[14] + t[12] * t[2] * t[11] - t[12] * t[3] * t[10]),\n      i * (-t[0] * t[6] * t[15] + t[0] * t[7] * t[14] + t[4] * t[2] * t[15] - t[4] * t[3] * t[14] - t[12] * t[2] * t[7] + t[12] * t[3] * t[6]),\n      i * (t[0] * t[6] * t[11] - t[0] * t[7] * t[10] - t[4] * t[2] * t[11] + t[4] * t[3] * t[10] + t[8] * t[2] * t[7] - t[8] * t[3] * t[6]),\n      i * (t[4] * t[9] * t[15] - t[4] * t[11] * t[13] - t[8] * t[5] * t[15] + t[8] * t[7] * t[13] + t[12] * t[5] * t[11] - t[12] * t[7] * t[9]),\n      i * (-t[0] * t[9] * t[15] + t[0] * t[11] * t[13] + t[8] * t[1] * t[15] - t[8] * t[3] * t[13] - t[12] * t[1] * t[11] + t[12] * t[3] * t[9]),\n      i * (t[0] * t[5] * t[15] - t[0] * t[7] * t[13] - t[4] * t[1] * t[15] + t[4] * t[3] * t[13] + t[12] * t[1] * t[7] - t[12] * t[3] * t[5]),\n      i * (-t[0] * t[5] * t[11] + t[0] * t[7] * t[9] + t[4] * t[1] * t[11] - t[4] * t[3] * t[9] - t[8] * t[1] * t[7] + t[8] * t[3] * t[5]),\n      i * (-t[4] * t[9] * t[14] + t[4] * t[10] * t[13] + t[8] * t[5] * t[14] - t[8] * t[6] * t[13] - t[12] * t[5] * t[10] + t[12] * t[6] * t[9]),\n      i * (t[0] * t[9] * t[14] - t[0] * t[10] * t[13] - t[8] * t[1] * t[14] + t[8] * t[2] * t[13] + t[12] * t[1] * t[10] - t[12] * t[2] * t[9]),\n      i * (-t[0] * t[5] * t[14] + t[0] * t[6] * t[13] + t[4] * t[1] * t[14] - t[4] * t[2] * t[13] - t[12] * t[1] * t[6] + t[12] * t[2] * t[5]),\n      i * (t[0] * t[5] * t[10] - t[0] * t[6] * t[9] - t[4] * t[1] * t[10] + t[4] * t[2] * t[9] + t[8] * t[1] * t[6] - t[8] * t[2] * t[5])\n    );\n  }\n  static Compose(t, n, i) {\n    const e = n.x, A = n.y, o = n.z, s = n.w, r = e + e, Q = A + A, I = o + o, d = e * r, a = e * Q, U = e * I, F = A * Q, g = A * I, B = o * I, C = s * r, c = s * Q, p = s * I, u = i.x, S = i.y, W = i.z;\n    return new K(\n      (1 - (F + B)) * u,\n      (a + p) * u,\n      (U - c) * u,\n      0,\n      (a - p) * S,\n      (1 - (d + B)) * S,\n      (g + C) * S,\n      0,\n      (U + c) * W,\n      (g - C) * W,\n      (1 - (d + F)) * W,\n      0,\n      t.x,\n      t.y,\n      t.z,\n      1\n    );\n  }\n  toString() {\n    return `[${this.buffer.join(\", \")}]`;\n  }\n}\nclass It extends Event {\n  constructor(t) {\n    super(\"objectAdded\"), this.object = t;\n  }\n}\nclass ht extends Event {\n  constructor(t) {\n    super(\"objectRemoved\"), this.object = t;\n  }\n}\nclass ct extends Event {\n  constructor(t) {\n    super(\"objectChanged\"), this.object = t;\n  }\n}\nclass st extends Ft {\n  constructor() {\n    super(), this.positionChanged = !1, this.rotationChanged = !1, this.scaleChanged = !1, this._position = new R(), this._rotation = new y(), this._scale = new R(1, 1, 1), this._transform = new K(), this._changeEvent = new ct(this), this.update = () => {\n    }, this.applyPosition = () => {\n      this.position = new R();\n    }, this.applyRotation = () => {\n      this.rotation = new y();\n    }, this.applyScale = () => {\n      this.scale = new R(1, 1, 1);\n    }, this.raiseChangeEvent = () => {\n      this.dispatchEvent(this._changeEvent);\n    };\n  }\n  _updateMatrix() {\n    this._transform = K.Compose(this._position, this._rotation, this._scale);\n  }\n  get position() {\n    return this._position;\n  }\n  set position(t) {\n    this._position.equals(t) || (this._position = t, this.positionChanged = !0, this._updateMatrix(), this.dispatchEvent(this._changeEvent));\n  }\n  get rotation() {\n    return this._rotation;\n  }\n  set rotation(t) {\n    this._rotation.equals(t) || (this._rotation = t, this.rotationChanged = !0, this._updateMatrix(), this.dispatchEvent(this._changeEvent));\n  }\n  get scale() {\n    return this._scale;\n  }\n  set scale(t) {\n    this._scale.equals(t) || (this._scale = t, this.scaleChanged = !0, this._updateMatrix(), this.dispatchEvent(this._changeEvent));\n  }\n  get forward() {\n    let t = new R(0, 0, 1);\n    return t = this.rotation.apply(t), t;\n  }\n  get transform() {\n    return this._transform;\n  }\n}\nclass _ {\n  // prettier-ignore\n  constructor(t = 1, n = 0, i = 0, e = 0, A = 1, o = 0, s = 0, r = 0, Q = 1) {\n    this.buffer = [\n      t,\n      n,\n      i,\n      e,\n      A,\n      o,\n      s,\n      r,\n      Q\n    ];\n  }\n  equals(t) {\n    if (this.buffer.length !== t.buffer.length)\n      return !1;\n    if (this.buffer === t.buffer)\n      return !0;\n    for (let n = 0; n < this.buffer.length; n++)\n      if (this.buffer[n] !== t.buffer[n])\n        return !1;\n    return !0;\n  }\n  multiply(t) {\n    const n = this.buffer, i = t.buffer;\n    return new _(\n      i[0] * n[0] + i[3] * n[1] + i[6] * n[2],\n      i[1] * n[0] + i[4] * n[1] + i[7] * n[2],\n      i[2] * n[0] + i[5] * n[1] + i[8] * n[2],\n      i[0] * n[3] + i[3] * n[4] + i[6] * n[5],\n      i[1] * n[3] + i[4] * n[4] + i[7] * n[5],\n      i[2] * n[3] + i[5] * n[4] + i[8] * n[5],\n      i[0] * n[6] + i[3] * n[7] + i[6] * n[8],\n      i[1] * n[6] + i[4] * n[7] + i[7] * n[8],\n      i[2] * n[6] + i[5] * n[7] + i[8] * n[8]\n    );\n  }\n  clone() {\n    const t = this.buffer;\n    return new _(\n      t[0],\n      t[1],\n      t[2],\n      t[3],\n      t[4],\n      t[5],\n      t[6],\n      t[7],\n      t[8]\n    );\n  }\n  static Eye(t = 1) {\n    return new _(t, 0, 0, 0, t, 0, 0, 0, t);\n  }\n  static Diagonal(t) {\n    return new _(t.x, 0, 0, 0, t.y, 0, 0, 0, t.z);\n  }\n  static RotationFromQuaternion(t) {\n    return new _(\n      1 - 2 * t.y * t.y - 2 * t.z * t.z,\n      2 * t.x * t.y - 2 * t.z * t.w,\n      2 * t.x * t.z + 2 * t.y * t.w,\n      2 * t.x * t.y + 2 * t.z * t.w,\n      1 - 2 * t.x * t.x - 2 * t.z * t.z,\n      2 * t.y * t.z - 2 * t.x * t.w,\n      2 * t.x * t.z - 2 * t.y * t.w,\n      2 * t.y * t.z + 2 * t.x * t.w,\n      1 - 2 * t.x * t.x - 2 * t.y * t.y\n    );\n  }\n  static RotationFromEuler(t) {\n    const n = Math.cos(t.x), i = Math.sin(t.x), e = Math.cos(t.y), A = Math.sin(t.y), o = Math.cos(t.z), s = Math.sin(t.z), r = [\n      e * o + A * i * s,\n      -e * s + A * i * o,\n      A * n,\n      n * s,\n      n * o,\n      -i,\n      -A * o + e * i * s,\n      A * s + e * i * o,\n      e * n\n    ];\n    return new _(...r);\n  }\n  toString() {\n    return `[${this.buffer.join(\", \")}]`;\n  }\n}\nclass Y {\n  constructor(t = 0, n = null, i = null, e = null, A = null) {\n    this.changed = !1, this.detached = !1, this._vertexCount = t, this._positions = n || new Float32Array(0), this._rotations = i || new Float32Array(0), this._scales = e || new Float32Array(0), this._colors = A || new Uint8Array(0), this._selection = new Uint8Array(this.vertexCount), this.translate = (o) => {\n      for (let s = 0; s < this.vertexCount; s++)\n        this.positions[3 * s + 0] += o.x, this.positions[3 * s + 1] += o.y, this.positions[3 * s + 2] += o.z;\n      this.changed = !0;\n    }, this.rotate = (o) => {\n      const s = _.RotationFromQuaternion(o).buffer;\n      for (let r = 0; r < this.vertexCount; r++) {\n        const Q = this.positions[3 * r + 0], I = this.positions[3 * r + 1], d = this.positions[3 * r + 2];\n        this.positions[3 * r + 0] = s[0] * Q + s[1] * I + s[2] * d, this.positions[3 * r + 1] = s[3] * Q + s[4] * I + s[5] * d, this.positions[3 * r + 2] = s[6] * Q + s[7] * I + s[8] * d;\n        const a = new y(\n          this.rotations[4 * r + 1],\n          this.rotations[4 * r + 2],\n          this.rotations[4 * r + 3],\n          this.rotations[4 * r + 0]\n        ), U = o.multiply(a);\n        this.rotations[4 * r + 1] = U.x, this.rotations[4 * r + 2] = U.y, this.rotations[4 * r + 3] = U.z, this.rotations[4 * r + 0] = U.w;\n      }\n      this.changed = !0;\n    }, this.scale = (o) => {\n      for (let s = 0; s < this.vertexCount; s++)\n        this.positions[3 * s + 0] *= o.x, this.positions[3 * s + 1] *= o.y, this.positions[3 * s + 2] *= o.z, this.scales[3 * s + 0] *= o.x, this.scales[3 * s + 1] *= o.y, this.scales[3 * s + 2] *= o.z;\n      this.changed = !0;\n    }, this.serialize = () => {\n      const o = new Uint8Array(this.vertexCount * Y.RowLength), s = new Float32Array(o.buffer), r = new Uint8Array(o.buffer);\n      for (let Q = 0; Q < this.vertexCount; Q++)\n        s[8 * Q + 0] = this.positions[3 * Q + 0], s[8 * Q + 1] = this.positions[3 * Q + 1], s[8 * Q + 2] = this.positions[3 * Q + 2], r[32 * Q + 24 + 0] = this.colors[4 * Q + 0], r[32 * Q + 24 + 1] = this.colors[4 * Q + 1], r[32 * Q + 24 + 2] = this.colors[4 * Q + 2], r[32 * Q + 24 + 3] = this.colors[4 * Q + 3], s[8 * Q + 3 + 0] = this.scales[3 * Q + 0], s[8 * Q + 3 + 1] = this.scales[3 * Q + 1], s[8 * Q + 3 + 2] = this.scales[3 * Q + 2], r[32 * Q + 28 + 0] = this.rotations[4 * Q + 0] * 128 + 128 & 255, r[32 * Q + 28 + 1] = this.rotations[4 * Q + 1] * 128 + 128 & 255, r[32 * Q + 28 + 2] = this.rotations[4 * Q + 2] * 128 + 128 & 255, r[32 * Q + 28 + 3] = this.rotations[4 * Q + 3] * 128 + 128 & 255;\n      return o;\n    }, this.reattach = (o, s, r, Q, I) => {\n      console.assert(\n        o.byteLength === this.vertexCount * 3 * 4,\n        `Expected ${this.vertexCount * 3 * 4} bytes, got ${o.byteLength} bytes`\n      ), this._positions = new Float32Array(o), this._rotations = new Float32Array(s), this._scales = new Float32Array(r), this._colors = new Uint8Array(Q), this._selection = new Uint8Array(I), this.detached = !1;\n    };\n  }\n  static {\n    this.RowLength = 3 * 4 + 3 * 4 + 4 + 4;\n  }\n  static Deserialize(t) {\n    const n = t.length / Y.RowLength, i = new Float32Array(3 * n), e = new Float32Array(4 * n), A = new Float32Array(3 * n), o = new Uint8Array(4 * n), s = new Float32Array(t.buffer), r = new Uint8Array(t.buffer);\n    for (let Q = 0; Q < n; Q++)\n      i[3 * Q + 0] = s[8 * Q + 0], i[3 * Q + 1] = s[8 * Q + 1], i[3 * Q + 2] = s[8 * Q + 2], e[4 * Q + 0] = (r[32 * Q + 28 + 0] - 128) / 128, e[4 * Q + 1] = (r[32 * Q + 28 + 1] - 128) / 128, e[4 * Q + 2] = (r[32 * Q + 28 + 2] - 128) / 128, e[4 * Q + 3] = (r[32 * Q + 28 + 3] - 128) / 128, A[3 * Q + 0] = s[8 * Q + 3 + 0], A[3 * Q + 1] = s[8 * Q + 3 + 1], A[3 * Q + 2] = s[8 * Q + 3 + 2], o[4 * Q + 0] = r[32 * Q + 24 + 0], o[4 * Q + 1] = r[32 * Q + 24 + 1], o[4 * Q + 2] = r[32 * Q + 24 + 2], o[4 * Q + 3] = r[32 * Q + 24 + 3];\n    return new Y(n, i, e, A, o);\n  }\n  get vertexCount() {\n    return this._vertexCount;\n  }\n  get positions() {\n    return this._positions;\n  }\n  get rotations() {\n    return this._rotations;\n  }\n  get scales() {\n    return this._scales;\n  }\n  get colors() {\n    return this._colors;\n  }\n  get selection() {\n    return this._selection;\n  }\n  clone() {\n    return new Y(\n      this.vertexCount,\n      new Float32Array(this.positions),\n      new Float32Array(this.rotations),\n      new Float32Array(this.scales),\n      new Uint8Array(this.colors)\n    );\n  }\n}\nclass ot {\n  static {\n    this.RowLength = 64;\n  }\n  constructor(t, n, i, e, A) {\n    this._vertexCount = t, this._positions = n, this._data = i, this._width = e, this._height = A, this.serialize = () => new Uint8Array(this._data.buffer);\n  }\n  static Deserialize(t, n, i) {\n    const e = new Uint32Array(t.buffer), A = new Float32Array(t.buffer), o = Math.floor(A.byteLength / this.RowLength), s = new Float32Array(o * 3);\n    for (let r = 0; r < o; r++)\n      s[3 * r + 0] = A[16 * r + 0], s[3 * r + 1] = A[16 * r + 1], s[3 * r + 2] = A[16 * r + 2], s[3 * r + 0] = A[16 * r + 3];\n    return new ot(o, s, e, n, i);\n  }\n  get vertexCount() {\n    return this._vertexCount;\n  }\n  get positions() {\n    return this._positions;\n  }\n  get data() {\n    return this._data;\n  }\n  get width() {\n    return this._width;\n  }\n  get height() {\n    return this._height;\n  }\n}\nclass et {\n  static {\n    this.SH_C0 = 0.28209479177387814;\n  }\n  static SplatToPLY(t, n) {\n    let i = `ply\nformat binary_little_endian 1.0\n`;\n    i += `element vertex ${n}\n`;\n    const e = [\"x\", \"y\", \"z\", \"nx\", \"ny\", \"nz\", \"f_dc_0\", \"f_dc_1\", \"f_dc_2\"];\n    for (let B = 0; B < 45; B++)\n      e.push(`f_rest_${B}`);\n    e.push(\"opacity\"), e.push(\"scale_0\"), e.push(\"scale_1\"), e.push(\"scale_2\"), e.push(\"rot_0\"), e.push(\"rot_1\"), e.push(\"rot_2\"), e.push(\"rot_3\");\n    for (const B of e)\n      i += `property float ${B}\n`;\n    i += `end_header\n`;\n    const A = new TextEncoder().encode(i), o = 4 * 3 + 4 * 3 + 4 * 3 + 4 * 45 + 4 + 4 * 3 + 4 * 4, s = n * o, r = new DataView(new ArrayBuffer(A.length + s));\n    new Uint8Array(r.buffer).set(A, 0);\n    const Q = new Float32Array(t), I = new Uint8Array(t), d = A.length, a = 4 * 3 + 4 * 3, U = a + 4 * 3 + 4 * 45, F = U + 4, g = F + 4 * 3;\n    for (let B = 0; B < n; B++) {\n      const C = Q[8 * B + 0], c = Q[8 * B + 1], p = Q[8 * B + 2], u = (I[32 * B + 24 + 0] / 255 - 0.5) / this.SH_C0, S = (I[32 * B + 24 + 1] / 255 - 0.5) / this.SH_C0, W = (I[32 * B + 24 + 2] / 255 - 0.5) / this.SH_C0, Z = I[32 * B + 24 + 3] / 255, k = Math.log(Z / (1 - Z)), f = Math.log(Q[8 * B + 3 + 0]), H = Math.log(Q[8 * B + 3 + 1]), L = Math.log(Q[8 * B + 3 + 2]);\n      let M = new y(\n        (I[32 * B + 28 + 1] - 128) / 128,\n        (I[32 * B + 28 + 2] - 128) / 128,\n        (I[32 * B + 28 + 3] - 128) / 128,\n        (I[32 * B + 28 + 0] - 128) / 128\n      );\n      M = M.normalize();\n      const V = M.w, w = M.x, P = M.y, T = M.z;\n      r.setFloat32(d + o * B + 0, C, !0), r.setFloat32(d + o * B + 4, c, !0), r.setFloat32(d + o * B + 8, p, !0), r.setFloat32(d + o * B + a + 0, u, !0), r.setFloat32(d + o * B + a + 4, S, !0), r.setFloat32(d + o * B + a + 8, W, !0), r.setFloat32(d + o * B + U, k, !0), r.setFloat32(d + o * B + F + 0, f, !0), r.setFloat32(d + o * B + F + 4, H, !0), r.setFloat32(d + o * B + F + 8, L, !0), r.setFloat32(d + o * B + g + 0, V, !0), r.setFloat32(d + o * B + g + 4, w, !0), r.setFloat32(d + o * B + g + 8, P, !0), r.setFloat32(d + o * B + g + 12, T, !0);\n    }\n    return r.buffer;\n  }\n}\nclass nt {\n  constructor(t, n) {\n    this.min = t, this.max = n;\n  }\n  contains(t) {\n    return t.x >= this.min.x && t.x <= this.max.x && t.y >= this.min.y && t.y <= this.max.y && t.z >= this.min.z && t.z <= this.max.z;\n  }\n  intersects(t) {\n    return this.max.x >= t.min.x && this.min.x <= t.max.x && this.max.y >= t.min.y && this.min.y <= t.max.y && this.max.z >= t.min.z && this.min.z <= t.max.z;\n  }\n  size() {\n    return this.max.subtract(this.min);\n  }\n  center() {\n    return this.min.add(this.max).divide(2);\n  }\n  expand(t) {\n    this.min = this.min.min(t), this.max = this.max.max(t);\n  }\n  permute() {\n    const t = this.min, n = this.max;\n    this.min = new R(Math.min(t.x, n.x), Math.min(t.y, n.y), Math.min(t.z, n.z)), this.max = new R(Math.max(t.x, n.x), Math.max(t.y, n.y), Math.max(t.z, n.z));\n  }\n}\nclass X extends st {\n  constructor(t = void 0) {\n    super(), this.selectedChanged = !1, this.colorTransformChanged = !1, this._selected = !1, this._colorTransforms = [], this._colorTransformsMap = /* @__PURE__ */ new Map(), this._data = t || new Y(), this._bounds = new nt(\n      new R(1 / 0, 1 / 0, 1 / 0),\n      new R(-1 / 0, -1 / 0, -1 / 0)\n    ), this.recalculateBounds = () => {\n      this._bounds = new nt(\n        new R(1 / 0, 1 / 0, 1 / 0),\n        new R(-1 / 0, -1 / 0, -1 / 0)\n      );\n      for (let n = 0; n < this._data.vertexCount; n++)\n        this._bounds.expand(\n          new R(\n            this._data.positions[3 * n],\n            this._data.positions[3 * n + 1],\n            this._data.positions[3 * n + 2]\n          )\n        );\n    }, this.applyPosition = () => {\n      this.data.translate(this.position), this.position = new R();\n    }, this.applyRotation = () => {\n      this.data.rotate(this.rotation), this.rotation = new y();\n    }, this.applyScale = () => {\n      this.data.scale(this.scale), this.scale = new R(1, 1, 1);\n    }, this.recalculateBounds();\n  }\n  saveToFile(t = null, n = \"splat\") {\n    if (!document) return;\n    if (!t) {\n      const s = /* @__PURE__ */ new Date();\n      t = `splat-${s.getFullYear()}-${s.getMonth() + 1}-${s.getDate()}.${n}`;\n    }\n    const i = this.clone();\n    i.applyRotation(), i.applyScale(), i.applyPosition();\n    const e = i.data.serialize();\n    let A;\n    if (n === \"ply\") {\n      const s = et.SplatToPLY(e.buffer, i.data.vertexCount);\n      A = new Blob([s], { type: \"application/octet-stream\" });\n    } else\n      A = new Blob([e.buffer], { type: \"application/octet-stream\" });\n    const o = document.createElement(\"a\");\n    o.download = t, o.href = URL.createObjectURL(A), o.click();\n  }\n  get data() {\n    return this._data;\n  }\n  get selected() {\n    return this._selected;\n  }\n  set selected(t) {\n    this._selected !== t && (this._selected = t, this.selectedChanged = !0, this.dispatchEvent(this._changeEvent));\n  }\n  get colorTransforms() {\n    return this._colorTransforms;\n  }\n  get colorTransformsMap() {\n    return this._colorTransformsMap;\n  }\n  get bounds() {\n    let t = this._bounds.center();\n    t = t.add(this.position);\n    let n = this._bounds.size();\n    return n = n.multiply(this.scale), new nt(t.subtract(n.divide(2)), t.add(n.divide(2)));\n  }\n  clone() {\n    const t = new X(this.data.clone());\n    return t.position = this.position.clone(), t.rotation = this.rotation.clone(), t.scale = this.scale.clone(), t;\n  }\n}\nclass tt extends st {\n  constructor(t) {\n    super(), this._data = t;\n  }\n  get data() {\n    return this._data;\n  }\n}\nclass Rt {\n  constructor() {\n    this._fx = 1132, this._fy = 1132, this._near = 0.1, this._far = 100, this._width = 512, this._height = 512, this._projectionMatrix = new K(), this._viewMatrix = new K(), this._viewProj = new K(), this._updateProjectionMatrix = () => {\n      this._projectionMatrix = new K(\n        2 * this.fx / this.width,\n        0,\n        0,\n        0,\n        0,\n        -2 * this.fy / this.height,\n        0,\n        0,\n        0,\n        0,\n        this.far / (this.far - this.near),\n        1,\n        0,\n        0,\n        -(this.far * this.near) / (this.far - this.near),\n        0\n      ), this._viewProj = this.projectionMatrix.multiply(this.viewMatrix);\n    }, this.update = (t, n) => {\n      const i = _.RotationFromQuaternion(n).buffer, e = t.flat();\n      this._viewMatrix = new K(\n        i[0],\n        i[1],\n        i[2],\n        0,\n        i[3],\n        i[4],\n        i[5],\n        0,\n        i[6],\n        i[7],\n        i[8],\n        0,\n        -e[0] * i[0] - e[1] * i[3] - e[2] * i[6],\n        -e[0] * i[1] - e[1] * i[4] - e[2] * i[7],\n        -e[0] * i[2] - e[1] * i[5] - e[2] * i[8],\n        1\n      ), this._viewProj = this.projectionMatrix.multiply(this.viewMatrix);\n    }, this.setSize = (t, n) => {\n      this._width = t, this._height = n, this._updateProjectionMatrix();\n    };\n  }\n  get fx() {\n    return this._fx;\n  }\n  set fx(t) {\n    this._fx !== t && (this._fx = t, this._updateProjectionMatrix());\n  }\n  get fy() {\n    return this._fy;\n  }\n  set fy(t) {\n    this._fy !== t && (this._fy = t, this._updateProjectionMatrix());\n  }\n  get near() {\n    return this._near;\n  }\n  set near(t) {\n    this._near !== t && (this._near = t, this._updateProjectionMatrix());\n  }\n  get far() {\n    return this._far;\n  }\n  set far(t) {\n    this._far !== t && (this._far = t, this._updateProjectionMatrix());\n  }\n  get width() {\n    return this._width;\n  }\n  get height() {\n    return this._height;\n  }\n  get projectionMatrix() {\n    return this._projectionMatrix;\n  }\n  get viewMatrix() {\n    return this._viewMatrix;\n  }\n  get viewProj() {\n    return this._viewProj;\n  }\n}\nclass z {\n  constructor(t = 0, n = 0, i = 0, e = 0) {\n    this.x = t, this.y = n, this.z = i, this.w = e;\n  }\n  equals(t) {\n    return !(this.x !== t.x || this.y !== t.y || this.z !== t.z || this.w !== t.w);\n  }\n  add(t) {\n    return typeof t == \"number\" ? new z(this.x + t, this.y + t, this.z + t, this.w + t) : new z(this.x + t.x, this.y + t.y, this.z + t.z, this.w + t.w);\n  }\n  subtract(t) {\n    return typeof t == \"number\" ? new z(this.x - t, this.y - t, this.z - t, this.w - t) : new z(this.x - t.x, this.y - t.y, this.z - t.z, this.w - t.w);\n  }\n  multiply(t) {\n    return typeof t == \"number\" ? new z(this.x * t, this.y * t, this.z * t, this.w * t) : t instanceof z ? new z(this.x * t.x, this.y * t.y, this.z * t.z, this.w * t.w) : new z(\n      this.x * t.buffer[0] + this.y * t.buffer[4] + this.z * t.buffer[8] + this.w * t.buffer[12],\n      this.x * t.buffer[1] + this.y * t.buffer[5] + this.z * t.buffer[9] + this.w * t.buffer[13],\n      this.x * t.buffer[2] + this.y * t.buffer[6] + this.z * t.buffer[10] + this.w * t.buffer[14],\n      this.x * t.buffer[3] + this.y * t.buffer[7] + this.z * t.buffer[11] + this.w * t.buffer[15]\n    );\n  }\n  dot(t) {\n    return this.x * t.x + this.y * t.y + this.z * t.z + this.w * t.w;\n  }\n  lerp(t, n) {\n    return new z(\n      this.x + (t.x - this.x) * n,\n      this.y + (t.y - this.y) * n,\n      this.z + (t.z - this.z) * n,\n      this.w + (t.w - this.w) * n\n    );\n  }\n  magnitude() {\n    return Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z + this.w * this.w);\n  }\n  distanceTo(t) {\n    return Math.sqrt((this.x - t.x) ** 2 + (this.y - t.y) ** 2 + (this.z - t.z) ** 2 + (this.w - t.w) ** 2);\n  }\n  normalize() {\n    const t = this.magnitude();\n    return new z(this.x / t, this.y / t, this.z / t, this.w / t);\n  }\n  flat() {\n    return [this.x, this.y, this.z, this.w];\n  }\n  clone() {\n    return new z(this.x, this.y, this.z, this.w);\n  }\n  toString() {\n    return `[${this.flat().join(\", \")}]`;\n  }\n}\nclass wt extends st {\n  constructor(t = void 0) {\n    super(), this._data = t || new Rt(), this._position = new R(0, 0, -5), this.update = () => {\n      this.data.update(this.position, this.rotation);\n    }, this.screenPointToRay = (n, i) => {\n      const e = new z(n, i, -1, 1), A = this._data.projectionMatrix.invert(), o = e.multiply(A), s = this._data.viewMatrix.invert(), r = o.multiply(s);\n      return new R(\n        r.x / r.w,\n        r.y / r.w,\n        r.z / r.w\n      ).subtract(this.position).normalize();\n    };\n  }\n  get data() {\n    return this._data;\n  }\n}\nclass kt extends Ft {\n  constructor() {\n    super(), this._objects = [], this.addObject = (t) => {\n      this.objects.push(t), this.dispatchEvent(new It(t));\n    }, this.removeObject = (t) => {\n      const n = this.objects.indexOf(t);\n      if (n < 0)\n        throw new Error(\"Object not found in scene\");\n      this.objects.splice(n, 1), this.dispatchEvent(new ht(t));\n    }, this.findObject = (t) => {\n      for (const n of this.objects)\n        if (t(n))\n          return n;\n    }, this.findObjectOfType = (t) => {\n      for (const n of this.objects)\n        if (n instanceof t)\n          return n;\n    }, this.reset = () => {\n      const t = this.objects.slice();\n      for (const n of t)\n        this.removeObject(n);\n    }, this.reset();\n  }\n  getMergedSceneDataBuffer(t = \"splat\") {\n    const n = [];\n    let i = 0;\n    for (const o of this.objects)\n      if (o instanceof X) {\n        const s = o.clone();\n        s.applyRotation(), s.applyScale(), s.applyPosition();\n        const r = s.data.serialize();\n        n.push(r), i += s.data.vertexCount;\n      }\n    const e = new Uint8Array(i * Y.RowLength);\n    let A = 0;\n    for (const o of n)\n      e.set(o, A), A += o.length;\n    return t === \"ply\" ? et.SplatToPLY(e.buffer, i) : e.buffer;\n  }\n  saveToFile(t = null, n = \"splat\") {\n    if (!document) return;\n    if (!t) {\n      const o = /* @__PURE__ */ new Date();\n      t = `scene-${o.getFullYear()}-${o.getMonth() + 1}-${o.getDate()}.${n}`;\n    }\n    const i = this.getMergedSceneDataBuffer(n), e = new Blob([i], { type: \"application/octet-stream\" }), A = document.createElement(\"a\");\n    A.download = t, A.href = URL.createObjectURL(e), A.click();\n  }\n  get objects() {\n    return this._objects;\n  }\n}\nasync function rt(E, t) {\n  const n = await fetch(E, {\n    mode: \"cors\",\n    credentials: \"omit\",\n    cache: t ? \"force-cache\" : \"default\"\n  });\n  if (n.status != 200)\n    throw new Error(n.status + \" Unable to load \" + n.url);\n  return n;\n}\nasync function At(E, t) {\n  const n = E.body.getReader(), i = E.headers.get(\"content-length\"), e = i && !isNaN(parseInt(i)) ? parseInt(i) : void 0, A = [];\n  let o = 0;\n  for (; ; ) {\n    const { done: Q, value: I } = await n.read();\n    if (Q) break;\n    if (A.push(I), o += I.length, t && e) {\n      const d = o / e, a = Math.min(d * 0.95, 0.95);\n      t(a);\n    }\n  }\n  const s = new Uint8Array(o);\n  let r = 0;\n  for (const Q of A)\n    s.set(Q, r), r += Q.length;\n  return t && t(1), s;\n}\nclass Tt {\n  static async LoadAsync(t, n, i, e = !1) {\n    const A = await rt(t, e), o = await At(A, i);\n    return this.LoadFromArrayBuffer(o.buffer, n);\n  }\n  static async LoadFromFileAsync(t, n, i) {\n    const e = new FileReader();\n    let A = new X();\n    return e.onload = (o) => {\n      A = this.LoadFromArrayBuffer(o.target.result, n);\n    }, e.onprogress = (o) => {\n      i?.(o.loaded / o.total);\n    }, e.readAsArrayBuffer(t), await new Promise((o) => {\n      e.onloadend = () => {\n        o();\n      };\n    }), A;\n  }\n  static LoadFromArrayBuffer(t, n) {\n    const i = new Uint8Array(t), e = Y.Deserialize(i), A = new X(e);\n    return n.addObject(A), A;\n  }\n}\nclass bt {\n  static async LoadAsync(t, n, i, e = \"\", A = !1) {\n    const o = await rt(t, A), s = await At(o, i);\n    if (s[0] !== 112 || s[1] !== 108 || s[2] !== 121 || s[3] !== 10)\n      throw new Error(\"Invalid PLY file\");\n    return this.LoadFromArrayBuffer(s.buffer, n, e);\n  }\n  static async LoadFromFileAsync(t, n, i, e = \"\") {\n    const A = new FileReader();\n    let o = new X();\n    return A.onload = (s) => {\n      o = this.LoadFromArrayBuffer(s.target.result, n, e);\n    }, A.onprogress = (s) => {\n      i?.(s.loaded / s.total);\n    }, A.readAsArrayBuffer(t), await new Promise((s) => {\n      A.onloadend = () => {\n        s();\n      };\n    }), o;\n  }\n  static LoadFromArrayBuffer(t, n, i = \"\") {\n    const e = new Uint8Array(this._ParsePLYBuffer(t, i)), A = Y.Deserialize(e), o = new X(A);\n    return n.addObject(o), o;\n  }\n  static _ParsePLYBuffer(t, n) {\n    const i = new Uint8Array(t), e = new TextDecoder().decode(i.slice(0, 1024 * 10)), A = `end_header\n`, o = e.indexOf(A);\n    if (o < 0) throw new Error(\"Unable to read .ply file header\");\n    const s = parseInt(/element vertex (\\d+)\\n/.exec(e)[1]);\n    let r = 0;\n    const Q = {\n      double: 8,\n      int: 4,\n      uint: 4,\n      float: 4,\n      short: 2,\n      ushort: 2,\n      uchar: 1\n    }, I = [];\n    for (const F of e.slice(0, o).split(`\n`).filter((g) => g.startsWith(\"property \"))) {\n      const [g, B, C] = F.split(\" \");\n      if (I.push({ name: C, type: B, offset: r }), !Q[B]) throw new Error(`Unsupported property type: ${B}`);\n      r += Q[B];\n    }\n    const d = new DataView(t, o + A.length), a = new ArrayBuffer(Y.RowLength * s), U = y.FromEuler(new R(Math.PI / 2, 0, 0));\n    for (let F = 0; F < s; F++) {\n      const g = new Float32Array(a, F * Y.RowLength, 3), B = new Float32Array(a, F * Y.RowLength + 12, 3), C = new Uint8ClampedArray(a, F * Y.RowLength + 24, 4), c = new Uint8ClampedArray(a, F * Y.RowLength + 28, 4);\n      let p = 255, u = 0, S = 0, W = 0;\n      I.forEach((k) => {\n        let f;\n        switch (k.type) {\n          case \"float\":\n            f = d.getFloat32(k.offset + F * r, !0);\n            break;\n          case \"int\":\n            f = d.getInt32(k.offset + F * r, !0);\n            break;\n          default:\n            throw new Error(`Unsupported property type: ${k.type}`);\n        }\n        switch (k.name) {\n          case \"x\":\n            g[0] = f;\n            break;\n          case \"y\":\n            g[1] = f;\n            break;\n          case \"z\":\n            g[2] = f;\n            break;\n          case \"scale_0\":\n          case \"scaling_0\":\n            B[0] = Math.exp(f);\n            break;\n          case \"scale_1\":\n          case \"scaling_1\":\n            B[1] = Math.exp(f);\n            break;\n          case \"scale_2\":\n          case \"scaling_2\":\n            B[2] = Math.exp(f);\n            break;\n          case \"red\":\n            C[0] = f;\n            break;\n          case \"green\":\n            C[1] = f;\n            break;\n          case \"blue\":\n            C[2] = f;\n            break;\n          case \"f_dc_0\":\n          case \"features_0\":\n            C[0] = (0.5 + et.SH_C0 * f) * 255;\n            break;\n          case \"f_dc_1\":\n          case \"features_1\":\n            C[1] = (0.5 + et.SH_C0 * f) * 255;\n            break;\n          case \"f_dc_2\":\n          case \"features_2\":\n            C[2] = (0.5 + et.SH_C0 * f) * 255;\n            break;\n          case \"f_dc_3\":\n            C[3] = (0.5 + et.SH_C0 * f) * 255;\n            break;\n          case \"opacity\":\n          case \"opacity_0\":\n            C[3] = 1 / (1 + Math.exp(-f)) * 255;\n            break;\n          case \"rot_0\":\n          case \"rotation_0\":\n            p = f;\n            break;\n          case \"rot_1\":\n          case \"rotation_1\":\n            u = f;\n            break;\n          case \"rot_2\":\n          case \"rotation_2\":\n            S = f;\n            break;\n          case \"rot_3\":\n          case \"rotation_3\":\n            W = f;\n            break;\n        }\n      });\n      let Z = new y(u, S, W, p);\n      switch (n) {\n        case \"polycam\": {\n          const k = g[1];\n          g[1] = -g[2], g[2] = k, Z = U.multiply(Z);\n          break;\n        }\n        case \"\":\n          break;\n        default:\n          throw new Error(`Unsupported format: ${n}`);\n      }\n      Z = Z.normalize(), c[0] = Z.w * 128 + 128, c[1] = Z.x * 128 + 128, c[2] = Z.y * 128 + 128, c[3] = Z.z * 128 + 128;\n    }\n    return a;\n  }\n}\nclass xt {\n  static async LoadAsync(t, n, i, e, A = !1) {\n    const o = await rt(t, A), s = await At(o, e);\n    return this._ParseSplatvBuffer(s.buffer, n, i);\n  }\n  static async LoadFromFileAsync(t, n, i, e) {\n    const A = new FileReader();\n    let o = null;\n    if (A.onload = (s) => {\n      o = this._ParseSplatvBuffer(s.target.result, n, i);\n    }, A.onprogress = (s) => {\n      e?.(s.loaded / s.total);\n    }, A.readAsArrayBuffer(t), await new Promise((s) => {\n      A.onloadend = () => {\n        s();\n      };\n    }), !o)\n      throw new Error(\"Failed to load splatv file\");\n    return o;\n  }\n  static _ParseSplatvBuffer(t, n, i) {\n    let e = null;\n    const A = (a, U, F) => {\n      if (a.type === \"magic\") {\n        const g = new Int32Array(U.buffer);\n        if (g[0] !== 26443)\n          throw new Error(\"Invalid splatv file\");\n        F.push({ size: g[1], type: \"chunks\" });\n      } else if (a.type === \"chunks\") {\n        const g = JSON.parse(new TextDecoder(\"utf-8\").decode(U));\n        if (g.length == 0)\n          throw new Error(\"Invalid splatv file\");\n        g.length > 1 && console.warn(\"Splatv file contains more than one chunk, only the first one will be loaded\");\n        const B = g[0], C = B.cameras;\n        if (i && C && C.length) {\n          const c = C[0], p = new R(\n            c.position[0],\n            c.position[1],\n            c.position[2]\n          ), u = y.FromMatrix3(\n            new _(\n              c.rotation[0][0],\n              c.rotation[0][1],\n              c.rotation[0][2],\n              c.rotation[1][0],\n              c.rotation[1][1],\n              c.rotation[1][2],\n              c.rotation[2][0],\n              c.rotation[2][1],\n              c.rotation[2][2]\n            )\n          );\n          i.position = p, i.rotation = u;\n        }\n        F.push(B);\n      } else if (a.type === \"splat\") {\n        const g = ot.Deserialize(U, a.texwidth, a.texheight), B = new tt(g);\n        n.addObject(B), e = B;\n      }\n    }, o = new Uint8Array(t), s = [\n      { size: 8, type: \"magic\", texwidth: 0, texheight: 0 }\n    ];\n    let r = s.shift(), Q = new Uint8Array(r.size), I = 0, d = 0;\n    for (; r; ) {\n      for (; I < r.size; ) {\n        const a = Math.min(r.size - I, o.length - d);\n        Q.set(o.subarray(d, d + a), I), I += a, d += a;\n      }\n      if (A(r, Q, s), e)\n        return e;\n      r = s.shift(), r && (Q = new Uint8Array(r.size), I = 0);\n    }\n    throw new Error(\"Invalid splatv file\");\n  }\n}\nconst Bt = \"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\", Et = (E) => Uint8Array.from(atob(E), (t) => t.charCodeAt(0)), Qt = typeof self < \"u\" && self.Blob && new Blob([\"URL.revokeObjectURL(import.meta.url);\", Et(Bt)], { type: \"text/javascript;charset=utf-8\" });\nfunction St(E) {\n  let t;\n  try {\n    if (t = Qt && (self.URL || self.webkitURL).createObjectURL(Qt), !t) throw \"\";\n    const n = new Worker(t, {\n      type: \"module\",\n      name: E?.name\n    });\n    return n.addEventListener(\"error\", () => {\n      (self.URL || self.webkitURL).revokeObjectURL(t);\n    }), n;\n  } catch {\n    return new Worker(\n      \"data:text/javascript;base64,\" + Bt,\n      {\n        type: \"module\",\n        name: E?.name\n      }\n    );\n  }\n}\nclass lt {\n  constructor(t, n) {\n    this._scene = null, this._camera = null, this._started = !1, this._initialized = !1, this._renderer = t;\n    const i = t.gl;\n    this._program = i.createProgram(), this._passes = n || [];\n    const e = i.createShader(i.VERTEX_SHADER);\n    i.shaderSource(e, this._getVertexSource()), i.compileShader(e), i.getShaderParameter(e, i.COMPILE_STATUS) || console.error(i.getShaderInfoLog(e));\n    const A = i.createShader(i.FRAGMENT_SHADER);\n    i.shaderSource(A, this._getFragmentSource()), i.compileShader(A), i.getShaderParameter(A, i.COMPILE_STATUS) || console.error(i.getShaderInfoLog(A)), i.attachShader(this.program, e), i.attachShader(this.program, A), i.linkProgram(this.program), i.getProgramParameter(this.program, i.LINK_STATUS) || console.error(i.getProgramInfoLog(this.program)), this.resize = () => {\n      i.useProgram(this._program), this._resize();\n    }, this.initialize = () => {\n      console.assert(!this._initialized, \"ShaderProgram already initialized\"), i.useProgram(this._program), this._initialize();\n      for (const o of this.passes)\n        o.initialize(this);\n      this._initialized = !0, this._started = !0;\n    }, this.render = (o, s) => {\n      i.useProgram(this._program), (this._scene !== o || this._camera !== s) && (this.dispose(), this._scene = o, this._camera = s, this.initialize());\n      for (const r of this.passes)\n        r.render();\n      this._render();\n    }, this.dispose = () => {\n      if (this._initialized) {\n        i.useProgram(this._program);\n        for (const o of this.passes)\n          o.dispose();\n        this._dispose(), this._scene = null, this._camera = null, this._initialized = !1;\n      }\n    };\n  }\n  get renderer() {\n    return this._renderer;\n  }\n  get scene() {\n    return this._scene;\n  }\n  get camera() {\n    return this._camera;\n  }\n  get program() {\n    return this._program;\n  }\n  get passes() {\n    return this._passes;\n  }\n  get started() {\n    return this._started;\n  }\n}\nconst gt = \"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\", ut = (E) => Uint8Array.from(atob(E), (t) => t.charCodeAt(0)), at = typeof self < \"u\" && self.Blob && new Blob([\"URL.revokeObjectURL(import.meta.url);\", ut(gt)], { type: \"text/javascript;charset=utf-8\" });\nfunction Jt(E) {\n  let t;\n  try {\n    if (t = at && (self.URL || self.webkitURL).createObjectURL(at), !t) throw \"\";\n    const n = new Worker(t, {\n      type: \"module\",\n      name: E?.name\n    });\n    return n.addEventListener(\"error\", () => {\n      (self.URL || self.webkitURL).revokeObjectURL(t);\n    }), n;\n  } catch {\n    return new Worker(\n      \"data:text/javascript;base64,\" + gt,\n      {\n        type: \"module\",\n        name: E?.name\n      }\n    );\n  }\n}\nvar mt = function(E = {}) {\n  var t, n = E, i = import.meta.url, e = \"\", A;\n  {\n    try {\n      e = new URL(\".\", i).href;\n    } catch {\n    }\n    A = (h) => {\n      var m = new XMLHttpRequest();\n      return m.open(\"GET\", h, !1), m.responseType = \"arraybuffer\", m.send(null), new Uint8Array(m.response);\n    };\n  }\n  console.log.bind(console), console.error.bind(console);\n  var o, s, r;\n  function Q() {\n    var h = s.buffer;\n    n.HEAPU8 = r = new Uint8Array(h), n.HEAPU32 = new Uint32Array(h), n.HEAPF32 = new Float32Array(h), new BigInt64Array(h), new BigUint64Array(h);\n  }\n  function I() {\n    if (n.preRun)\n      for (typeof n.preRun == \"function\" && (n.preRun = [n.preRun]); n.preRun.length; )\n        L(n.preRun.shift());\n    Z(H);\n  }\n  function d() {\n    j.c();\n  }\n  function a() {\n    if (n.postRun)\n      for (typeof n.postRun == \"function\" && (n.postRun = [n.postRun]); n.postRun.length; )\n        f(n.postRun.shift());\n    Z(k);\n  }\n  var U = 0, F = null;\n  function g(h) {\n    U++, n.monitorRunDependencies?.(U);\n  }\n  function B(h) {\n    if (U--, n.monitorRunDependencies?.(U), U == 0 && F) {\n      var m = F;\n      F = null, m();\n    }\n  }\n  var C;\n  function c() {\n    return M(\"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\");\n  }\n  function p(h) {\n    if (ArrayBuffer.isView(h))\n      return h;\n    if (h == C && o)\n      return new Uint8Array(o);\n    if (A)\n      return A(h);\n    throw 'sync fetching of the wasm failed: you can preload it to Module[\"wasmBinary\"] manually, or emcc.py will do that for you when generating HTML (but not JS)';\n  }\n  function u(h, m) {\n    var x, N = p(h);\n    x = new WebAssembly.Module(N);\n    var v = new WebAssembly.Instance(x, m);\n    return [v, x];\n  }\n  function S() {\n    return { a: b };\n  }\n  function W() {\n    function h(N, v) {\n      return j = N.exports, s = j.b, Q(), D(j), B(), j;\n    }\n    g();\n    var m = S();\n    if (n.instantiateWasm)\n      return new Promise((N, v) => {\n        n.instantiateWasm(m, (O, $) => {\n          N(h(O));\n        });\n      });\n    C ??= c();\n    var x = u(C, m);\n    return h(x[0]);\n  }\n  for (var Z = (h) => {\n    for (; h.length > 0; )\n      h.shift()(n);\n  }, k = [], f = (h) => k.push(h), H = [], L = (h) => H.push(h), M = (h) => {\n    for (var m, x, N = 0, v = 0, O = h.length, $ = new Uint8Array((O * 3 >> 2) - (h[O - 2] == \"=\") - (h[O - 1] == \"=\")); N < O; N += 4, v += 3)\n      m = l[h.charCodeAt(N + 1)], x = l[h.charCodeAt(N + 2)], $[v] = l[h.charCodeAt(N)] << 2 | m >> 4, $[v + 1] = m << 4 | x >> 2, $[v + 2] = x << 6 | l[h.charCodeAt(N + 3)];\n    return $;\n  }, V = () => 2147483648, w = (h, m) => Math.ceil(h / m) * m, P = (h) => {\n    var m = s.buffer, x = (h - m.byteLength + 65535) / 65536 | 0;\n    try {\n      return s.grow(x), Q(), 1;\n    } catch {\n    }\n  }, T = (h) => {\n    var m = r.length;\n    h >>>= 0;\n    var x = V();\n    if (h > x)\n      return !1;\n    for (var N = 1; N <= 4; N *= 2) {\n      var v = m * (1 + 0.2 / N);\n      v = Math.min(v, h + 100663296);\n      var O = Math.min(x, w(Math.max(h, v), 65536)), $ = P(O);\n      if ($)\n        return !0;\n    }\n    return !1;\n  }, l = new Uint8Array(123), J = 25; J >= 0; --J)\n    l[48 + J] = 52 + J, l[65 + J] = J, l[97 + J] = 26 + J;\n  l[43] = 62, l[47] = 63, n.noExitRuntime && n.noExitRuntime, n.print && n.print, n.printErr && n.printErr, n.wasmBinary && (o = n.wasmBinary), n.arguments && n.arguments, n.thisProgram && n.thisProgram;\n  function D(h) {\n    n._pack = h.d, n._malloc = h.e, n._free = h.f;\n  }\n  var b = { a: T }, j = W();\n  function q() {\n    if (U > 0) {\n      F = q;\n      return;\n    }\n    if (I(), U > 0) {\n      F = q;\n      return;\n    }\n    function h() {\n      n.calledRun = !0, d(), n.onRuntimeInitialized?.(), a();\n    }\n    n.setStatus ? (n.setStatus(\"Running...\"), setTimeout(() => {\n      setTimeout(() => n.setStatus(\"\"), 1), h();\n    }, 1)) : h();\n  }\n  function G() {\n    if (n.preInit)\n      for (typeof n.preInit == \"function\" && (n.preInit = [n.preInit]); n.preInit.length > 0; )\n        n.preInit.shift()();\n  }\n  return G(), q(), t = n, t;\n};\nconst ft = () => new Jt();\nclass Ut {\n  constructor(t) {\n    this.dataChanged = !1, this.transformsChanged = !1, this.colorTransformsChanged = !1, this._updating = /* @__PURE__ */ new Set(), this._dirty = /* @__PURE__ */ new Set();\n    let n = 0, i = 0;\n    this._splatIndices = /* @__PURE__ */ new Map(), this._offsets = /* @__PURE__ */ new Map();\n    const e = /* @__PURE__ */ new Map();\n    for (const a of t.objects)\n      a instanceof X && (this._splatIndices.set(a, i), this._offsets.set(a, n), e.set(n, a), n += a.data.vertexCount, i++);\n    this._vertexCount = n, this._width = 2048, this._height = Math.ceil(2 * this.vertexCount / this.width), this._data = new Uint32Array(this.width * this.height * 4), this._transformsWidth = 5, this._transformsHeight = e.size, this._transforms = new Float32Array(this._transformsWidth * this._transformsHeight * 4), this._transformIndicesWidth = 1024, this._transformIndicesHeight = Math.ceil(this.vertexCount / this._transformIndicesWidth), this._transformIndices = new Uint32Array(this._transformIndicesWidth * this._transformIndicesHeight), this._colorTransformsWidth = 4, this._colorTransformsHeight = 64, this._colorTransforms = new Float32Array(this._colorTransformsWidth * this._colorTransformsHeight * 4), this._colorTransforms.fill(0), this._colorTransforms[0] = 1, this._colorTransforms[5] = 1, this._colorTransforms[10] = 1, this._colorTransforms[15] = 1, this._colorTransformIndicesWidth = 1024, this._colorTransformIndicesHeight = Math.ceil(this.vertexCount / this._colorTransformIndicesWidth), this._colorTransformIndices = new Uint32Array(\n      this._colorTransformIndicesWidth * this._colorTransformIndicesHeight\n    ), this.colorTransformIndices.fill(0), this._positions = new Float32Array(this.vertexCount * 3), this._rotations = new Float32Array(this.vertexCount * 4), this._scales = new Float32Array(this.vertexCount * 3), this._worker = ft();\n    const A = (a) => {\n      const U = this._splatIndices.get(a);\n      this._transforms.set(a.transform.buffer, U * 20), this._transforms[U * 20 + 16] = a.selected ? 1 : 0, a.positionChanged = !1, a.rotationChanged = !1, a.scaleChanged = !1, a.selectedChanged = !1, this.transformsChanged = !0;\n    }, o = () => {\n      let a = !1;\n      for (const g of this._splatIndices.keys())\n        if (g.colorTransformChanged) {\n          a = !0;\n          break;\n        }\n      if (!a)\n        return;\n      const U = [new K()];\n      this._colorTransformIndices.fill(0);\n      let F = 1;\n      for (const g of this._splatIndices.keys()) {\n        const B = this._offsets.get(g);\n        for (const C of g.colorTransforms)\n          U.includes(C) || (U.push(C), F++);\n        for (const C of g.colorTransformsMap.keys()) {\n          const c = g.colorTransformsMap.get(C);\n          this._colorTransformIndices[C + B] = c + F - 1;\n        }\n        g.colorTransformChanged = !1;\n      }\n      for (let g = 0; g < U.length; g++) {\n        const B = U[g];\n        this._colorTransforms.set(B.buffer, g * 16);\n      }\n      this.colorTransformsChanged = !0;\n    };\n    this._worker.onmessage = (a) => {\n      if (a.data.response) {\n        const U = a.data.response, F = e.get(U.offset);\n        A(F), o();\n        const g = this._splatIndices.get(F);\n        for (let B = 0; B < F.data.vertexCount; B++)\n          this._transformIndices[U.offset + B] = g;\n        this._data.set(U.data, U.offset * 8), F.data.reattach(\n          U.positions,\n          U.rotations,\n          U.scales,\n          U.colors,\n          U.selection\n        ), this._positions.set(U.worldPositions, U.offset * 3), this._rotations.set(U.worldRotations, U.offset * 4), this._scales.set(U.worldScales, U.offset * 3), this._updating.delete(F), F.selectedChanged = !1, this.dataChanged = !0;\n      }\n    };\n    let s;\n    async function r() {\n      s = await mt();\n    }\n    r();\n    async function Q() {\n      for (; !s; )\n        await new Promise((a) => setTimeout(a, 0));\n    }\n    const I = (a) => {\n      if (!s) {\n        Q().then(() => {\n          I(a);\n        });\n        return;\n      }\n      A(a);\n      const U = s._malloc(3 * a.data.vertexCount * 4), F = s._malloc(4 * a.data.vertexCount * 4), g = s._malloc(3 * a.data.vertexCount * 4), B = s._malloc(4 * a.data.vertexCount), C = s._malloc(a.data.vertexCount), c = s._malloc(8 * a.data.vertexCount * 4), p = s._malloc(3 * a.data.vertexCount * 4), u = s._malloc(4 * a.data.vertexCount * 4), S = s._malloc(3 * a.data.vertexCount * 4);\n      s.HEAPF32.set(a.data.positions, U / 4), s.HEAPF32.set(a.data.rotations, F / 4), s.HEAPF32.set(a.data.scales, g / 4), s.HEAPU8.set(a.data.colors, B), s.HEAPU8.set(a.data.selection, C), s._pack(\n        a.selected,\n        a.data.vertexCount,\n        U,\n        F,\n        g,\n        B,\n        C,\n        c,\n        p,\n        u,\n        S\n      );\n      const W = new Uint32Array(s.HEAPU32.buffer, c, a.data.vertexCount * 8), Z = new Float32Array(\n        s.HEAPF32.buffer,\n        p,\n        a.data.vertexCount * 3\n      ), k = new Float32Array(\n        s.HEAPF32.buffer,\n        u,\n        a.data.vertexCount * 4\n      ), f = new Float32Array(s.HEAPF32.buffer, S, a.data.vertexCount * 3), H = this._splatIndices.get(a), L = this._offsets.get(a);\n      for (let M = 0; M < a.data.vertexCount; M++)\n        this._transformIndices[L + M] = H;\n      this._data.set(W, L * 8), this._positions.set(Z, L * 3), this._rotations.set(k, L * 4), this._scales.set(f, L * 3), s._free(U), s._free(F), s._free(g), s._free(B), s._free(C), s._free(c), s._free(p), s._free(u), s._free(S), this.dataChanged = !0, this.colorTransformsChanged = !0;\n    }, d = (a) => {\n      if ((a.positionChanged || a.rotationChanged || a.scaleChanged || a.selectedChanged) && A(a), a.colorTransformChanged && o(), !a.data.changed || a.data.detached) return;\n      const U = {\n        position: new Float32Array(a.position.flat()),\n        rotation: new Float32Array(a.rotation.flat()),\n        scale: new Float32Array(a.scale.flat()),\n        selected: a.selected,\n        vertexCount: a.data.vertexCount,\n        positions: a.data.positions,\n        rotations: a.data.rotations,\n        scales: a.data.scales,\n        colors: a.data.colors,\n        selection: a.data.selection,\n        offset: this._offsets.get(a)\n      };\n      this._worker.postMessage(\n        {\n          splat: U\n        },\n        [\n          U.position.buffer,\n          U.rotation.buffer,\n          U.scale.buffer,\n          U.positions.buffer,\n          U.rotations.buffer,\n          U.scales.buffer,\n          U.colors.buffer,\n          U.selection.buffer\n        ]\n      ), this._updating.add(a), a.data.detached = !0;\n    };\n    this.getSplat = (a) => {\n      let U = null;\n      for (const [F, g] of this._offsets)\n        if (a >= g)\n          U = F;\n        else\n          break;\n      return U;\n    }, this.getLocalIndex = (a, U) => {\n      const F = this._offsets.get(a);\n      return U - F;\n    }, this.markDirty = (a) => {\n      this._dirty.add(a);\n    }, this.rebuild = () => {\n      for (const a of this._dirty)\n        d(a);\n      this._dirty.clear();\n    }, this.dispose = () => {\n      this._worker.terminate();\n    };\n    for (const a of this._splatIndices.keys())\n      I(a);\n    o();\n  }\n  get offsets() {\n    return this._offsets;\n  }\n  get data() {\n    return this._data;\n  }\n  get width() {\n    return this._width;\n  }\n  get height() {\n    return this._height;\n  }\n  get transforms() {\n    return this._transforms;\n  }\n  get transformsWidth() {\n    return this._transformsWidth;\n  }\n  get transformsHeight() {\n    return this._transformsHeight;\n  }\n  get transformIndices() {\n    return this._transformIndices;\n  }\n  get transformIndicesWidth() {\n    return this._transformIndicesWidth;\n  }\n  get transformIndicesHeight() {\n    return this._transformIndicesHeight;\n  }\n  get colorTransforms() {\n    return this._colorTransforms;\n  }\n  get colorTransformsWidth() {\n    return this._colorTransformsWidth;\n  }\n  get colorTransformsHeight() {\n    return this._colorTransformsHeight;\n  }\n  get colorTransformIndices() {\n    return this._colorTransformIndices;\n  }\n  get colorTransformIndicesWidth() {\n    return this._colorTransformIndicesWidth;\n  }\n  get colorTransformIndicesHeight() {\n    return this._colorTransformIndicesHeight;\n  }\n  get positions() {\n    return this._positions;\n  }\n  get rotations() {\n    return this._rotations;\n  }\n  get scales() {\n    return this._scales;\n  }\n  get vertexCount() {\n    return this._vertexCount;\n  }\n  get needsRebuild() {\n    return this._dirty.size > 0;\n  }\n  get updating() {\n    return this._updating.size > 0;\n  }\n}\nclass dt {\n  constructor(t = 0, n = 0, i = 0, e = 255) {\n    this.r = t, this.g = n, this.b = i, this.a = e;\n  }\n  flat() {\n    return [this.r, this.g, this.b, this.a];\n  }\n  flatNorm() {\n    return [this.r / 255, this.g / 255, this.b / 255, this.a / 255];\n  }\n  toHexString() {\n    return \"#\" + this.flat().map((t) => t.toString(16).padStart(2, \"0\")).join(\"\");\n  }\n  toString() {\n    return `[${this.flat().join(\", \")}]`;\n  }\n}\nconst Vt = () => new St(), Zt = (\n  /* glsl */\n  `#version 300 es\nprecision highp float;\nprecision highp int;\n\nuniform highp usampler2D u_texture;\nuniform highp sampler2D u_transforms;\nuniform highp usampler2D u_transformIndices;\nuniform highp sampler2D u_colorTransforms;\nuniform highp usampler2D u_colorTransformIndices;\nuniform mat4 projection, view;\nuniform vec2 focal;\nuniform vec2 viewport;\n\nuniform bool useDepthFade;\nuniform float depthFade;\n\nin vec2 position;\nin int index;\n\nout vec4 vColor;\nout vec2 vPosition;\nout float vSize;\nout float vSelected;\n\nvoid main () {\n    uvec4 cen = texelFetch(u_texture, ivec2((uint(index) & 0x3ffu) << 1, uint(index) >> 10), 0);\n    float selected = float((cen.w >> 24) & 0xffu);\n\n    uint transformIndex = texelFetch(u_transformIndices, ivec2(uint(index) & 0x3ffu, uint(index) >> 10), 0).x;\n    mat4 transform = mat4(\n        texelFetch(u_transforms, ivec2(0, transformIndex), 0),\n        texelFetch(u_transforms, ivec2(1, transformIndex), 0),\n        texelFetch(u_transforms, ivec2(2, transformIndex), 0),\n        texelFetch(u_transforms, ivec2(3, transformIndex), 0)\n    );\n\n    if (selected < 0.5) {\n        selected = texelFetch(u_transforms, ivec2(4, transformIndex), 0).x;\n    }\n\n    mat4 viewTransform = view * transform;\n\n    vec4 cam = viewTransform * vec4(uintBitsToFloat(cen.xyz), 1);\n    vec4 pos2d = projection * cam;\n\n    float clip = 1.2 * pos2d.w;\n    if (pos2d.z < -pos2d.w || pos2d.z > pos2d.w || pos2d.x < -clip || pos2d.x > clip || pos2d.y < -clip || pos2d.y > clip) {\n        gl_Position = vec4(0.0, 0.0, 2.0, 1.0);\n        return;\n    }\n\n    uvec4 cov = texelFetch(u_texture, ivec2(((uint(index) & 0x3ffu) << 1) | 1u, uint(index) >> 10), 0);\n    vec2 u1 = unpackHalf2x16(cov.x), u2 = unpackHalf2x16(cov.y), u3 = unpackHalf2x16(cov.z);\n    mat3 Vrk = mat3(u1.x, u1.y, u2.x, u1.y, u2.y, u3.x, u2.x, u3.x, u3.y);\n\n    mat3 J = mat3(\n        focal.x / cam.z, 0., -(focal.x * cam.x) / (cam.z * cam.z), \n        0., -focal.y / cam.z, (focal.y * cam.y) / (cam.z * cam.z), \n        0., 0., 0.\n    );\n\n    mat3 T = transpose(mat3(viewTransform)) * J;\n    mat3 cov2d = transpose(T) * Vrk * T;\n\n    //ref: https://github.com/graphdeco-inria/diff-gaussian-rasterization/blob/main/cuda_rasterizer/forward.cu#L110-L111\n    cov2d[0][0] += 0.3;\n    cov2d[1][1] += 0.3;\n\n    float mid = (cov2d[0][0] + cov2d[1][1]) / 2.0;\n    float radius = length(vec2((cov2d[0][0] - cov2d[1][1]) / 2.0, cov2d[0][1]));\n    float lambda1 = mid + radius, lambda2 = mid - radius;\n\n    if (lambda2 < 0.0) return;\n    vec2 diagonalVector = normalize(vec2(cov2d[0][1], lambda1 - cov2d[0][0]));\n    vec2 majorAxis = min(sqrt(2.0 * lambda1), 1024.0) * diagonalVector;\n    vec2 minorAxis = min(sqrt(2.0 * lambda2), 1024.0) * vec2(diagonalVector.y, -diagonalVector.x);\n\n    uint colorTransformIndex = texelFetch(u_colorTransformIndices, ivec2(uint(index) & 0x3ffu, uint(index) >> 10), 0).x;\n    mat4 colorTransform = mat4(\n        texelFetch(u_colorTransforms, ivec2(0, colorTransformIndex), 0),\n        texelFetch(u_colorTransforms, ivec2(1, colorTransformIndex), 0),\n        texelFetch(u_colorTransforms, ivec2(2, colorTransformIndex), 0),\n        texelFetch(u_colorTransforms, ivec2(3, colorTransformIndex), 0)\n    );\n\n    vec4 color = vec4((cov.w) & 0xffu, (cov.w >> 8) & 0xffu, (cov.w >> 16) & 0xffu, (cov.w >> 24) & 0xffu) / 255.0;\n    vColor = colorTransform * color;\n\n    vPosition = position;\n    vSize = length(majorAxis);\n    vSelected = selected;\n\n    float scalingFactor = 1.0;\n\n    if (useDepthFade) {\n        float depthNorm = (pos2d.z / pos2d.w + 1.0) / 2.0;\n        float near = 0.1; float far = 100.0;\n        float normalizedDepth = (2.0 * near) / (far + near - depthNorm * (far - near));\n        float start = max(normalizedDepth - 0.1, 0.0);\n        float end = min(normalizedDepth + 0.1, 1.0);\n        scalingFactor = clamp((depthFade - start) / (end - start), 0.0, 1.0);\n    }\n\n    vec2 vCenter = vec2(pos2d) / pos2d.w;\n    gl_Position = vec4(\n        vCenter \n        + position.x * majorAxis * scalingFactor / viewport\n        + position.y * minorAxis * scalingFactor / viewport, 0.0, 1.0);\n}\n`\n), pt = (\n  /* glsl */\n  `#version 300 es\nprecision highp float;\n\nuniform float outlineThickness;\nuniform vec4 outlineColor;\n\nin vec4 vColor;\nin vec2 vPosition;\nin float vSize;\nin float vSelected;\n\nout vec4 fragColor;\n\nvoid main () {\n    float A = -dot(vPosition, vPosition);\n\n    if (A < -4.0) discard;\n\n    if (vSelected < 0.5) {\n        float B = exp(A) * vColor.a;\n        fragColor = vec4(B * vColor.rgb, B);\n        return;\n    }\n\n    float outlineThreshold = -4.0 + (outlineThickness / vSize);\n\n    if (A < outlineThreshold) {\n        fragColor = outlineColor;\n    } \n    else {\n        float B = exp(A) * vColor.a;\n        fragColor = vec4(B * vColor.rgb, B);\n    }\n}\n`\n);\nclass Ct extends lt {\n  constructor(t, n) {\n    super(t, n), this._outlineThickness = 10, this._outlineColor = new dt(255, 165, 0, 255), this._renderData = null, this._depthIndex = new Uint32Array(), this._splatTexture = null, this._worker = null;\n    const i = t.canvas, e = t.gl;\n    let A, o, s, r, Q, I, d, a, U, F, g, B, C, c, p, u, S, W, Z;\n    this._resize = () => {\n      this._camera && (this._camera.data.setSize(i.width, i.height), this._camera.update(), A = e.getUniformLocation(this.program, \"projection\"), e.uniformMatrix4fv(A, !1, this._camera.data.projectionMatrix.buffer), o = e.getUniformLocation(this.program, \"viewport\"), e.uniform2fv(o, new Float32Array([i.width, i.height])));\n    };\n    const k = () => {\n      this._worker = Vt(), this._worker.onmessage = (V) => {\n        if (V.data.depthIndex) {\n          const { depthIndex: w } = V.data;\n          this._depthIndex = w, e.bindBuffer(e.ARRAY_BUFFER, Z), e.bufferData(e.ARRAY_BUFFER, w, e.STATIC_DRAW);\n        }\n      };\n    };\n    this._initialize = () => {\n      if (!this._scene || !this._camera) {\n        console.error(\"Cannot render without scene and camera\");\n        return;\n      }\n      this._resize(), this._scene.addEventListener(\"objectAdded\", f), this._scene.addEventListener(\"objectRemoved\", H);\n      for (const V of this._scene.objects)\n        V instanceof X && V.addEventListener(\"objectChanged\", L);\n      this._renderData = new Ut(this._scene), s = e.getUniformLocation(this.program, \"focal\"), e.uniform2fv(s, new Float32Array([this._camera.data.fx, this._camera.data.fy])), r = e.getUniformLocation(this.program, \"view\"), e.uniformMatrix4fv(r, !1, this._camera.data.viewMatrix.buffer), F = e.getUniformLocation(this.program, \"outlineThickness\"), e.uniform1f(F, this.outlineThickness), g = e.getUniformLocation(this.program, \"outlineColor\"), e.uniform4fv(g, new Float32Array(this.outlineColor.flatNorm())), this._splatTexture = e.createTexture(), Q = e.getUniformLocation(this.program, \"u_texture\"), e.uniform1i(Q, 0), c = e.createTexture(), I = e.getUniformLocation(this.program, \"u_transforms\"), e.uniform1i(I, 1), p = e.createTexture(), d = e.getUniformLocation(this.program, \"u_transformIndices\"), e.uniform1i(d, 2), u = e.createTexture(), a = e.getUniformLocation(this.program, \"u_colorTransforms\"), e.uniform1i(a, 3), S = e.createTexture(), U = e.getUniformLocation(\n        this.program,\n        \"u_colorTransformIndices\"\n      ), e.uniform1i(U, 4), W = e.createBuffer(), e.bindBuffer(e.ARRAY_BUFFER, W), e.bufferData(e.ARRAY_BUFFER, new Float32Array([-2, -2, 2, -2, 2, 2, -2, 2]), e.STATIC_DRAW), B = e.getAttribLocation(this.program, \"position\"), e.enableVertexAttribArray(B), e.vertexAttribPointer(B, 2, e.FLOAT, !1, 0, 0), Z = e.createBuffer(), C = e.getAttribLocation(this.program, \"index\"), e.enableVertexAttribArray(C), e.bindBuffer(e.ARRAY_BUFFER, Z), k();\n    };\n    const f = (V) => {\n      const w = V;\n      w.object instanceof X && w.object.addEventListener(\"objectChanged\", L), M();\n    }, H = (V) => {\n      const w = V;\n      w.object instanceof X && w.object.removeEventListener(\"objectChanged\", L), M();\n    }, L = (V) => {\n      const w = V;\n      w.object instanceof X && this._renderData && this._renderData.markDirty(w.object);\n    }, M = () => {\n      this._renderData?.dispose(), this._renderData = new Ut(this._scene), this._worker?.terminate(), k();\n    };\n    this._render = () => {\n      if (!this._scene || !this._camera || !this.renderData) {\n        console.error(\"Cannot render without scene and camera\");\n        return;\n      }\n      if (this.renderData.needsRebuild && this.renderData.rebuild(), this.renderData.dataChanged || this.renderData.transformsChanged || this.renderData.colorTransformsChanged) {\n        this.renderData.dataChanged && (e.activeTexture(e.TEXTURE0), e.bindTexture(e.TEXTURE_2D, this.splatTexture), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_WRAP_S, e.CLAMP_TO_EDGE), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_WRAP_T, e.CLAMP_TO_EDGE), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_MIN_FILTER, e.NEAREST), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_MAG_FILTER, e.NEAREST), e.texImage2D(\n          e.TEXTURE_2D,\n          0,\n          e.RGBA32UI,\n          this.renderData.width,\n          this.renderData.height,\n          0,\n          e.RGBA_INTEGER,\n          e.UNSIGNED_INT,\n          this.renderData.data\n        )), this.renderData.transformsChanged && (e.activeTexture(e.TEXTURE1), e.bindTexture(e.TEXTURE_2D, c), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_WRAP_S, e.CLAMP_TO_EDGE), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_WRAP_T, e.CLAMP_TO_EDGE), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_MIN_FILTER, e.NEAREST), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_MAG_FILTER, e.NEAREST), e.texImage2D(\n          e.TEXTURE_2D,\n          0,\n          e.RGBA32F,\n          this.renderData.transformsWidth,\n          this.renderData.transformsHeight,\n          0,\n          e.RGBA,\n          e.FLOAT,\n          this.renderData.transforms\n        ), e.activeTexture(e.TEXTURE2), e.bindTexture(e.TEXTURE_2D, p), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_WRAP_S, e.CLAMP_TO_EDGE), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_WRAP_T, e.CLAMP_TO_EDGE), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_MIN_FILTER, e.NEAREST), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_MAG_FILTER, e.NEAREST), e.texImage2D(\n          e.TEXTURE_2D,\n          0,\n          e.R32UI,\n          this.renderData.transformIndicesWidth,\n          this.renderData.transformIndicesHeight,\n          0,\n          e.RED_INTEGER,\n          e.UNSIGNED_INT,\n          this.renderData.transformIndices\n        )), this.renderData.colorTransformsChanged && (e.activeTexture(e.TEXTURE3), e.bindTexture(e.TEXTURE_2D, u), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_WRAP_S, e.CLAMP_TO_EDGE), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_WRAP_T, e.CLAMP_TO_EDGE), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_MIN_FILTER, e.NEAREST), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_MAG_FILTER, e.NEAREST), e.texImage2D(\n          e.TEXTURE_2D,\n          0,\n          e.RGBA32F,\n          this.renderData.colorTransformsWidth,\n          this.renderData.colorTransformsHeight,\n          0,\n          e.RGBA,\n          e.FLOAT,\n          this.renderData.colorTransforms\n        ), e.activeTexture(e.TEXTURE4), e.bindTexture(e.TEXTURE_2D, S), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_WRAP_S, e.CLAMP_TO_EDGE), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_WRAP_T, e.CLAMP_TO_EDGE), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_MIN_FILTER, e.NEAREST), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_MAG_FILTER, e.NEAREST), e.texImage2D(\n          e.TEXTURE_2D,\n          0,\n          e.R32UI,\n          this.renderData.colorTransformIndicesWidth,\n          this.renderData.colorTransformIndicesHeight,\n          0,\n          e.RED_INTEGER,\n          e.UNSIGNED_INT,\n          this.renderData.colorTransformIndices\n        ));\n        const V = new Float32Array(this.renderData.positions.slice().buffer), w = new Float32Array(this.renderData.transforms.slice().buffer), P = new Uint32Array(this.renderData.transformIndices.slice().buffer);\n        this._worker?.postMessage(\n          {\n            sortData: {\n              positions: V,\n              transforms: w,\n              transformIndices: P,\n              vertexCount: this.renderData.vertexCount\n            }\n          },\n          [V.buffer, w.buffer, P.buffer]\n        ), this.renderData.dataChanged = !1, this.renderData.transformsChanged = !1, this.renderData.colorTransformsChanged = !1;\n      }\n      this._camera.update(), this._worker?.postMessage({ viewProj: this._camera.data.viewProj.buffer }), e.viewport(0, 0, i.width, i.height), e.clearColor(0, 0, 0, 0), e.clear(e.COLOR_BUFFER_BIT), e.disable(e.DEPTH_TEST), e.enable(e.BLEND), e.blendFuncSeparate(e.ONE_MINUS_DST_ALPHA, e.ONE, e.ONE_MINUS_DST_ALPHA, e.ONE), e.blendEquationSeparate(e.FUNC_ADD, e.FUNC_ADD), e.uniformMatrix4fv(A, !1, this._camera.data.projectionMatrix.buffer), e.uniformMatrix4fv(r, !1, this._camera.data.viewMatrix.buffer), e.bindBuffer(e.ARRAY_BUFFER, W), e.vertexAttribPointer(B, 2, e.FLOAT, !1, 0, 0), e.bindBuffer(e.ARRAY_BUFFER, Z), e.bufferData(e.ARRAY_BUFFER, this.depthIndex, e.STATIC_DRAW), e.vertexAttribIPointer(C, 1, e.INT, 0, 0), e.vertexAttribDivisor(C, 1), e.drawArraysInstanced(e.TRIANGLE_FAN, 0, 4, this.depthIndex.length);\n    }, this._dispose = () => {\n      if (!this._scene || !this._camera || !this.renderData) {\n        console.error(\"Cannot dispose without scene and camera\");\n        return;\n      }\n      this._scene.removeEventListener(\"objectAdded\", f), this._scene.removeEventListener(\"objectRemoved\", H);\n      for (const V of this._scene.objects)\n        V instanceof X && V.removeEventListener(\"objectChanged\", L);\n      this._worker?.terminate(), this.renderData.dispose(), e.deleteTexture(this.splatTexture), e.deleteTexture(c), e.deleteTexture(p), e.deleteBuffer(Z), e.deleteBuffer(W);\n    }, this._setOutlineThickness = (V) => {\n      this._outlineThickness = V, this._initialized && e.uniform1f(F, V);\n    }, this._setOutlineColor = (V) => {\n      this._outlineColor = V, this._initialized && e.uniform4fv(g, new Float32Array(V.flatNorm()));\n    };\n  }\n  get renderData() {\n    return this._renderData;\n  }\n  get depthIndex() {\n    return this._depthIndex;\n  }\n  get splatTexture() {\n    return this._splatTexture;\n  }\n  get outlineThickness() {\n    return this._outlineThickness;\n  }\n  set outlineThickness(t) {\n    this._setOutlineThickness(t);\n  }\n  get outlineColor() {\n    return this._outlineColor;\n  }\n  set outlineColor(t) {\n    this._setOutlineColor(t);\n  }\n  get worker() {\n    return this._worker;\n  }\n  _getVertexSource() {\n    return Zt;\n  }\n  _getFragmentSource() {\n    return pt;\n  }\n}\nclass Nt {\n  constructor(t = 1) {\n    let n = 0, i = !1, e, A, o, s;\n    this.initialize = (r) => {\n      if (!(r instanceof Ct))\n        throw new Error(\"FadeInPass requires a RenderProgram\");\n      n = r.started ? 1 : 0, i = !0, e = r, A = r.renderer.gl, o = A.getUniformLocation(e.program, \"useDepthFade\"), A.uniform1i(o, 1), s = A.getUniformLocation(e.program, \"depthFade\"), A.uniform1f(s, n);\n    }, this.render = () => {\n      !i || e.renderData?.updating || (A.useProgram(e.program), n = Math.min(n + t * 0.01, 1), n >= 1 && (i = !1, A.uniform1i(o, 0)), A.uniform1f(s, n));\n    };\n  }\n  dispose() {\n  }\n}\nclass Mt {\n  constructor(t = null, n = null) {\n    this._backgroundColor = new dt();\n    const i = t || document.createElement(\"canvas\");\n    t || (i.style.display = \"block\", i.style.boxSizing = \"border-box\", i.style.width = \"100%\", i.style.height = \"100%\", i.style.margin = \"0\", i.style.padding = \"0\", document.body.appendChild(i)), i.style.background = this._backgroundColor.toHexString(), this._canvas = i, this._gl = i.getContext(\"webgl2\", { antialias: !1 });\n    const e = n || [];\n    n || e.push(new Nt()), this._renderProgram = new Ct(this, e);\n    const A = [this._renderProgram];\n    this.resize = () => {\n      const o = i.clientWidth, s = i.clientHeight;\n      (i.width !== o || i.height !== s) && this.setSize(o, s);\n    }, this.setSize = (o, s) => {\n      i.width = o, i.height = s, this._gl.viewport(0, 0, i.width, i.height);\n      for (const r of A)\n        r.resize();\n    }, this.render = (o, s) => {\n      for (const r of A)\n        r.render(o, s);\n    }, this.dispose = () => {\n      for (const o of A)\n        o.dispose();\n    }, this.addProgram = (o) => {\n      A.push(o);\n    }, this.removeProgram = (o) => {\n      const s = A.indexOf(o);\n      if (s < 0)\n        throw new Error(\"Program not found\");\n      A.splice(s, 1);\n    }, this.resize();\n  }\n  get canvas() {\n    return this._canvas;\n  }\n  get gl() {\n    return this._gl;\n  }\n  get renderProgram() {\n    return this._renderProgram;\n  }\n  get backgroundColor() {\n    return this._backgroundColor;\n  }\n  set backgroundColor(t) {\n    this._backgroundColor = t, this._canvas.style.background = t.toHexString();\n  }\n}\nclass Lt {\n  constructor(t, n, i = 0.5, e = 0.5, A = 5, o = !0, s = new R()) {\n    this.minAngle = -90, this.maxAngle = 90, this.minZoom = 0.1, this.maxZoom = 30, this.orbitSpeed = 1, this.panSpeed = 1, this.zoomSpeed = 1, this.dampening = 0.12, this.setCameraTarget = () => {\n    };\n    let r = s.clone(), Q = r.clone(), I = i, d = e, a = A, U = !1, F = !1, g = 0, B = 0, C = 0;\n    const c = {};\n    let p = !1;\n    const u = () => {\n      if (p) return;\n      const l = t.rotation.toEuler();\n      I = -l.y, d = -l.x;\n      const J = t.position.x - a * Math.sin(I) * Math.cos(d), D = t.position.y + a * Math.sin(d), b = t.position.z + a * Math.cos(I) * Math.cos(d);\n      Q = new R(J, D, b);\n    };\n    t.addEventListener(\"objectChanged\", u), this.setCameraTarget = (l) => {\n      const J = l.x - t.position.x, D = l.y - t.position.y, b = l.z - t.position.z;\n      a = Math.sqrt(J * J + D * D + b * b), d = Math.atan2(D, Math.sqrt(J * J + b * b)), I = -Math.atan2(J, b), Q = new R(l.x, l.y, l.z);\n    };\n    const S = () => 0.1 + 0.9 * (a - this.minZoom) / (this.maxZoom - this.minZoom), W = (l) => {\n      c[l.code] = !0, l.code === \"ArrowUp\" && (c.KeyW = !0), l.code === \"ArrowDown\" && (c.KeyS = !0), l.code === \"ArrowLeft\" && (c.KeyA = !0), l.code === \"ArrowRight\" && (c.KeyD = !0);\n    }, Z = (l) => {\n      c[l.code] = !1, l.code === \"ArrowUp\" && (c.KeyW = !1), l.code === \"ArrowDown\" && (c.KeyS = !1), l.code === \"ArrowLeft\" && (c.KeyA = !1), l.code === \"ArrowRight\" && (c.KeyD = !1);\n    }, k = (l) => {\n      T(l), U = !0, F = l.button === 2, B = l.clientX, C = l.clientY, window.addEventListener(\"mouseup\", f);\n    }, f = (l) => {\n      T(l), U = !1, F = !1, window.removeEventListener(\"mouseup\", f);\n    }, H = (l) => {\n      if (T(l), !U || !t) return;\n      const J = l.clientX - B, D = l.clientY - C;\n      if (F) {\n        const b = S(), j = -J * this.panSpeed * 0.01 * b, q = -D * this.panSpeed * 0.01 * b, G = _.RotationFromQuaternion(t.rotation).buffer, h = new R(G[0], G[3], G[6]), m = new R(G[1], G[4], G[7]);\n        Q = Q.add(h.multiply(j)), Q = Q.add(m.multiply(q));\n      } else\n        I -= J * this.orbitSpeed * 3e-3, d += D * this.orbitSpeed * 3e-3, d = Math.min(\n          Math.max(d, this.minAngle * Math.PI / 180),\n          this.maxAngle * Math.PI / 180\n        );\n      B = l.clientX, C = l.clientY;\n    }, L = (l) => {\n      T(l);\n      const J = S();\n      a += l.deltaY * this.zoomSpeed * 0.025 * J, a = Math.min(Math.max(a, this.minZoom), this.maxZoom);\n    }, M = (l) => {\n      if (T(l), l.touches.length === 1)\n        U = !0, F = !1, B = l.touches[0].clientX, C = l.touches[0].clientY, g = 0;\n      else if (l.touches.length === 2) {\n        U = !0, F = !0, B = (l.touches[0].clientX + l.touches[1].clientX) / 2, C = (l.touches[0].clientY + l.touches[1].clientY) / 2;\n        const J = l.touches[0].clientX - l.touches[1].clientX, D = l.touches[0].clientY - l.touches[1].clientY;\n        g = Math.sqrt(J * J + D * D);\n      }\n    }, V = (l) => {\n      T(l), U = !1, F = !1;\n    }, w = (l) => {\n      if (T(l), !(!U || !t))\n        if (F) {\n          const J = S(), D = l.touches[0].clientX - l.touches[1].clientX, b = l.touches[0].clientY - l.touches[1].clientY, j = Math.sqrt(D * D + b * b), q = g - j;\n          a += q * this.zoomSpeed * 0.1 * J, a = Math.min(Math.max(a, this.minZoom), this.maxZoom), g = j;\n          const G = (l.touches[0].clientX + l.touches[1].clientX) / 2, h = (l.touches[0].clientY + l.touches[1].clientY) / 2, m = G - B, x = h - C, N = _.RotationFromQuaternion(t.rotation).buffer, v = new R(N[0], N[3], N[6]), O = new R(N[1], N[4], N[7]);\n          Q = Q.add(v.multiply(-m * this.panSpeed * 0.025 * J)), Q = Q.add(O.multiply(-x * this.panSpeed * 0.025 * J)), B = G, C = h;\n        } else {\n          const J = l.touches[0].clientX - B, D = l.touches[0].clientY - C;\n          I -= J * this.orbitSpeed * 3e-3, d += D * this.orbitSpeed * 3e-3, d = Math.min(\n            Math.max(d, this.minAngle * Math.PI / 180),\n            this.maxAngle * Math.PI / 180\n          ), B = l.touches[0].clientX, C = l.touches[0].clientY;\n        }\n    }, P = (l, J, D) => (1 - D) * l + D * J;\n    this.update = () => {\n      p = !0, i = P(i, I, this.dampening), e = P(e, d, this.dampening), A = P(A, a, this.dampening), r = r.lerp(Q, this.dampening);\n      const l = r.x + A * Math.sin(i) * Math.cos(e), J = r.y - A * Math.sin(e), D = r.z - A * Math.cos(i) * Math.cos(e);\n      t.position = new R(l, J, D);\n      const b = r.subtract(t.position).normalize(), j = Math.asin(-b.y), q = Math.atan2(b.x, b.z);\n      t.rotation = y.FromEuler(new R(j, q, 0));\n      const G = 0.025, h = 0.01, m = _.RotationFromQuaternion(t.rotation).buffer, x = new R(-m[2], -m[5], -m[8]), N = new R(m[0], m[3], m[6]);\n      c.KeyS && (Q = Q.add(x.multiply(G))), c.KeyW && (Q = Q.subtract(x.multiply(G))), c.KeyA && (Q = Q.subtract(N.multiply(G))), c.KeyD && (Q = Q.add(N.multiply(G))), c.KeyE && (I += h), c.KeyQ && (I -= h), c.KeyR && (d += h), c.KeyF && (d -= h), p = !1;\n    };\n    const T = (l) => {\n      l.preventDefault(), l.stopPropagation();\n    };\n    this.dispose = () => {\n      n.removeEventListener(\"dragenter\", T), n.removeEventListener(\"dragover\", T), n.removeEventListener(\"dragleave\", T), n.removeEventListener(\"contextmenu\", T), n.removeEventListener(\"mousedown\", k), n.removeEventListener(\"mousemove\", H), n.removeEventListener(\"wheel\", L), n.removeEventListener(\"touchstart\", M), n.removeEventListener(\"touchend\", V), n.removeEventListener(\"touchmove\", w), o && (window.removeEventListener(\"keydown\", W), window.removeEventListener(\"keyup\", Z));\n    }, o && (window.addEventListener(\"keydown\", W), window.addEventListener(\"keyup\", Z)), n.addEventListener(\"dragenter\", T), n.addEventListener(\"dragover\", T), n.addEventListener(\"dragleave\", T), n.addEventListener(\"contextmenu\", T), n.addEventListener(\"mousedown\", k), n.addEventListener(\"mousemove\", H), n.addEventListener(\"wheel\", L), n.addEventListener(\"touchstart\", M), n.addEventListener(\"touchend\", V), n.addEventListener(\"touchmove\", w), this.update();\n  }\n}\nclass Gt {\n  constructor(t, n) {\n    this.moveSpeed = 1.5, this.lookSpeed = 0.7, this.dampening = 0.5;\n    const i = {};\n    let e = t.rotation.toEuler().x, A = t.rotation.toEuler().y, o = t.position, s = !1;\n    const r = () => {\n      n.requestPointerLock();\n    }, Q = () => {\n      s = document.pointerLockElement === n, s ? n.addEventListener(\"mousemove\", I) : n.removeEventListener(\"mousemove\", I);\n    }, I = (F) => {\n      const g = F.movementX, B = F.movementY;\n      A += g * this.lookSpeed * 1e-3, e -= B * this.lookSpeed * 1e-3, e = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, e));\n    }, d = (F) => {\n      i[F.code] = !0, F.code === \"ArrowUp\" && (i.KeyW = !0), F.code === \"ArrowDown\" && (i.KeyS = !0), F.code === \"ArrowLeft\" && (i.KeyA = !0), F.code === \"ArrowRight\" && (i.KeyD = !0);\n    }, a = (F) => {\n      i[F.code] = !1, F.code === \"ArrowUp\" && (i.KeyW = !1), F.code === \"ArrowDown\" && (i.KeyS = !1), F.code === \"ArrowLeft\" && (i.KeyA = !1), F.code === \"ArrowRight\" && (i.KeyD = !1), F.code === \"Escape\" && document.exitPointerLock();\n    };\n    this.update = () => {\n      const F = _.RotationFromQuaternion(t.rotation).buffer, g = new R(-F[2], -F[5], -F[8]), B = new R(F[0], F[3], F[6]);\n      let C = new R(0, 0, 0);\n      i.KeyS && (C = C.add(g)), i.KeyW && (C = C.subtract(g)), i.KeyA && (C = C.subtract(B)), i.KeyD && (C = C.add(B)), C = new R(C.x, 0, C.z), C.magnitude() > 0 && (C = C.normalize()), o = o.add(C.multiply(this.moveSpeed * 0.01)), t.position = t.position.add(o.subtract(t.position).multiply(this.dampening)), t.rotation = y.FromEuler(new R(e, A, 0));\n    };\n    const U = (F) => {\n      F.preventDefault(), F.stopPropagation();\n    };\n    this.dispose = () => {\n      n.removeEventListener(\"dragenter\", U), n.removeEventListener(\"dragover\", U), n.removeEventListener(\"dragleave\", U), n.removeEventListener(\"contextmenu\", U), n.removeEventListener(\"mousedown\", r), document.removeEventListener(\"pointerlockchange\", Q), window.removeEventListener(\"keydown\", d), window.removeEventListener(\"keyup\", a);\n    }, window.addEventListener(\"keydown\", d), window.addEventListener(\"keyup\", a), n.addEventListener(\"dragenter\", U), n.addEventListener(\"dragover\", U), n.addEventListener(\"dragleave\", U), n.addEventListener(\"contextmenu\", U), n.addEventListener(\"mousedown\", r), document.addEventListener(\"pointerlockchange\", Q), this.update();\n  }\n}\nclass vt {\n  constructor(t, n) {\n    this.normal = t, this.point = n;\n  }\n  intersect(t, n) {\n    const i = this.normal.dot(n);\n    if (Math.abs(i) < 1e-4)\n      return null;\n    const e = this.normal.dot(this.point.subtract(t)) / i;\n    return e < 0 ? null : t.add(n.multiply(e));\n  }\n}\nclass _t {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  initialize(t) {\n  }\n  render() {\n  }\n  dispose() {\n  }\n}\nconst Wt = (\n  /* glsl */\n  `#version 300 es\nprecision highp float;\nprecision highp int;\n  \nuniform highp usampler2D u_texture;\nuniform mat4 projection, view;\nuniform vec2 focal;\nuniform vec2 viewport;\nuniform float time;\n  \nin vec2 position;\nin int index;\n  \nout vec4 vColor;\nout vec2 vPosition;\n  \nvoid main () {\n    gl_Position = vec4(0.0, 0.0, 2.0, 1.0);\n\n    uvec4 motion1 = texelFetch(u_texture, ivec2(((uint(index) & 0x3ffu) << 2) | 3u, uint(index) >> 10), 0);\n    vec2 trbf = unpackHalf2x16(motion1.w);\n    float dt = time - trbf.x;\n\n    float topacity = exp(-1.0 * pow(dt / trbf.y, 2.0));\n    if(topacity < 0.02) return;\n\n    uvec4 motion0 = texelFetch(u_texture, ivec2(((uint(index) & 0x3ffu) << 2) | 2u, uint(index) >> 10), 0);\n    uvec4 static0 = texelFetch(u_texture, ivec2(((uint(index) & 0x3ffu) << 2), uint(index) >> 10), 0);\n\n    vec2 m0 = unpackHalf2x16(motion0.x), m1 = unpackHalf2x16(motion0.y), m2 = unpackHalf2x16(motion0.z), \n         m3 = unpackHalf2x16(motion0.w), m4 = unpackHalf2x16(motion1.x); \n      \n    vec4 trot = vec4(unpackHalf2x16(motion1.y).xy, unpackHalf2x16(motion1.z).xy) * dt;\n    vec3 tpos = (vec3(m0.xy, m1.x) * dt + vec3(m1.y, m2.xy) * dt*dt + vec3(m3.xy, m4.x) * dt*dt*dt);\n      \n    vec4 cam = view * vec4(uintBitsToFloat(static0.xyz) + tpos, 1);\n    vec4 pos = projection * cam;\n  \n    float clip = 1.2 * pos.w;\n    if (pos.z < -clip || pos.x < -clip || pos.x > clip || pos.y < -clip || pos.y > clip) return;\n    uvec4 static1 = texelFetch(u_texture, ivec2(((uint(index) & 0x3ffu) << 2) | 1u, uint(index) >> 10), 0);\n\n    vec4 rot = vec4(unpackHalf2x16(static0.w).xy, unpackHalf2x16(static1.x).xy) + trot;\n    vec3 scale = vec3(unpackHalf2x16(static1.y).xy, unpackHalf2x16(static1.z).x);\n    rot /= sqrt(dot(rot, rot));\n  \n    mat3 S = mat3(scale.x, 0.0, 0.0, 0.0, scale.y, 0.0, 0.0, 0.0, scale.z);\n    mat3 R = mat3(\n        1.0 - 2.0 * (rot.z * rot.z + rot.w * rot.w), 2.0 * (rot.y * rot.z - rot.x * rot.w), 2.0 * (rot.y * rot.w + rot.x * rot.z),\n        2.0 * (rot.y * rot.z + rot.x * rot.w), 1.0 - 2.0 * (rot.y * rot.y + rot.w * rot.w), 2.0 * (rot.z * rot.w - rot.x * rot.y),\n        2.0 * (rot.y * rot.w - rot.x * rot.z), 2.0 * (rot.z * rot.w + rot.x * rot.y), 1.0 - 2.0 * (rot.y * rot.y + rot.z * rot.z));\n    mat3 M = S * R;\n    mat3 Vrk = 4.0 * transpose(M) * M;\n    mat3 J = mat3(\n        focal.x / cam.z, 0., -(focal.x * cam.x) / (cam.z * cam.z), \n        0., -focal.y / cam.z, (focal.y * cam.y) / (cam.z * cam.z), \n        0., 0., 0.\n    );\n  \n    mat3 T = transpose(mat3(view)) * J;\n    mat3 cov2d = transpose(T) * Vrk * T;\n  \n    float mid = (cov2d[0][0] + cov2d[1][1]) / 2.0;\n    float radius = length(vec2((cov2d[0][0] - cov2d[1][1]) / 2.0, cov2d[0][1]));\n    float lambda1 = mid + radius, lambda2 = mid - radius;\n  \n    if(lambda2 < 0.0) return;\n    vec2 diagonalVector = normalize(vec2(cov2d[0][1], lambda1 - cov2d[0][0]));\n    vec2 majorAxis = min(sqrt(2.0 * lambda1), 1024.0) * diagonalVector;\n    vec2 minorAxis = min(sqrt(2.0 * lambda2), 1024.0) * vec2(diagonalVector.y, -diagonalVector.x);\n      \n    uint rgba = static1.w;\n    vColor = \n        clamp(pos.z/pos.w+1.0, 0.0, 1.0) * \n        vec4(1.0, 1.0, 1.0, topacity) *\n        vec4(\n            (rgba) & 0xffu, \n            (rgba >> 8) & 0xffu, \n            (rgba >> 16) & 0xffu, \n            (rgba >> 24) & 0xffu) / 255.0;\n\n    vec2 vCenter = vec2(pos) / pos.w;\n    gl_Position = vec4(\n        vCenter \n        + position.x * majorAxis / viewport \n        + position.y * minorAxis / viewport, 0.0, 1.0);\n\n    vPosition = position;\n}\n`\n), Dt = (\n  /* glsl */\n  `#version 300 es\nprecision highp float;\n  \nin vec4 vColor;\nin vec2 vPosition;\n\nout vec4 fragColor;\n\nvoid main () {\n    float A = -dot(vPosition, vPosition);\n    if (A < -4.0) discard;\n    float B = exp(A) * vColor.a;\n    fragColor = vec4(B * vColor.rgb, B);\n}\n`\n);\nclass Yt extends lt {\n  constructor(t, n = []) {\n    super(t, n), this._renderData = null, this._depthIndex = new Uint32Array(), this._splatTexture = null;\n    const i = t.canvas, e = t.gl;\n    let A, o, s, r, Q, I, d, a, U, F, g;\n    this._resize = () => {\n      this._camera && (this._camera.data.setSize(i.width, i.height), this._camera.update(), o = e.getUniformLocation(this.program, \"projection\"), e.uniformMatrix4fv(o, !1, this._camera.data.projectionMatrix.buffer), s = e.getUniformLocation(this.program, \"viewport\"), e.uniform2fv(s, new Float32Array([i.width, i.height])));\n    };\n    const B = () => {\n      if (t.renderProgram.worker === null) {\n        console.error(\"Render program is not initialized. Cannot render without worker\");\n        return;\n      }\n      A = t.renderProgram.worker, A.onmessage = (u) => {\n        if (u.data.depthIndex) {\n          const { depthIndex: S } = u.data;\n          this._depthIndex = S, e.bindBuffer(e.ARRAY_BUFFER, g), e.bufferData(e.ARRAY_BUFFER, S, e.STATIC_DRAW);\n        }\n      };\n    };\n    this._initialize = () => {\n      if (!this._scene || !this._camera) {\n        console.error(\"Cannot render without scene and camera\");\n        return;\n      }\n      this._resize(), this._scene.addEventListener(\"objectAdded\", C), this._scene.addEventListener(\"objectRemoved\", c);\n      for (const Z of this._scene.objects)\n        Z instanceof tt && (this._renderData === null ? (this._renderData = Z.data, Z.addEventListener(\"objectChanged\", p)) : console.warn(\"Multiple Splatv objects are not currently supported\"));\n      if (this._renderData === null) {\n        console.error(\"Cannot render without Splatv object\");\n        return;\n      }\n      r = e.getUniformLocation(this.program, \"focal\"), e.uniform2fv(r, new Float32Array([this._camera.data.fx, this._camera.data.fy])), Q = e.getUniformLocation(this.program, \"view\"), e.uniformMatrix4fv(Q, !1, this._camera.data.viewMatrix.buffer), this._splatTexture = e.createTexture(), I = e.getUniformLocation(this.program, \"u_texture\"), e.uniform1i(I, 0), d = e.getUniformLocation(this.program, \"time\"), e.uniform1f(d, Math.sin(Date.now() / 1e3) / 2 + 1 / 2), F = e.createBuffer(), e.bindBuffer(e.ARRAY_BUFFER, F), e.bufferData(e.ARRAY_BUFFER, new Float32Array([-2, -2, 2, -2, 2, 2, -2, 2]), e.STATIC_DRAW), a = e.getAttribLocation(this.program, \"position\"), e.enableVertexAttribArray(a), e.vertexAttribPointer(a, 2, e.FLOAT, !1, 0, 0), g = e.createBuffer(), U = e.getAttribLocation(this.program, \"index\"), e.enableVertexAttribArray(U), e.bindBuffer(e.ARRAY_BUFFER, g), B(), e.activeTexture(e.TEXTURE0), e.bindTexture(e.TEXTURE_2D, this._splatTexture), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_WRAP_S, e.CLAMP_TO_EDGE), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_WRAP_T, e.CLAMP_TO_EDGE), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_MIN_FILTER, e.NEAREST), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_MAG_FILTER, e.NEAREST), e.texImage2D(\n        e.TEXTURE_2D,\n        0,\n        e.RGBA32UI,\n        this._renderData.width,\n        this._renderData.height,\n        0,\n        e.RGBA_INTEGER,\n        e.UNSIGNED_INT,\n        this._renderData.data\n      );\n      const u = this._renderData.positions, S = new Float32Array(new K().buffer), W = new Uint32Array(this._renderData.vertexCount);\n      W.fill(0), A.postMessage(\n        {\n          sortData: {\n            positions: u,\n            transforms: S,\n            transformIndices: W,\n            vertexCount: this._renderData.vertexCount\n          }\n        },\n        [u.buffer, S.buffer, W.buffer]\n      );\n    };\n    const C = (u) => {\n      const S = u;\n      S.object instanceof tt && (this._renderData === null ? (this._renderData = S.object.data, S.object.addEventListener(\"objectChanged\", p)) : console.warn(\"Splatv not supported by default RenderProgram. Use VideoRenderProgram instead.\")), this.dispose();\n    }, c = (u) => {\n      const S = u;\n      S.object instanceof tt && this._renderData === S.object.data && (this._renderData = null, S.object.removeEventListener(\"objectChanged\", p)), this.dispose();\n    }, p = (u) => {\n      const S = u;\n      S.object instanceof tt && this._renderData === S.object.data && this.dispose();\n    };\n    this._render = () => {\n      if (!this._scene || !this._camera) {\n        console.error(\"Cannot render without scene and camera\");\n        return;\n      }\n      if (!this._renderData) {\n        console.warn(\"Cannot render without Splatv object\");\n        return;\n      }\n      this._camera.update(), A.postMessage({ viewProj: this._camera.data.viewProj.buffer }), e.viewport(0, 0, i.width, i.height), e.clearColor(0, 0, 0, 0), e.clear(e.COLOR_BUFFER_BIT), e.disable(e.DEPTH_TEST), e.enable(e.BLEND), e.blendFuncSeparate(e.ONE_MINUS_DST_ALPHA, e.ONE, e.ONE_MINUS_DST_ALPHA, e.ONE), e.blendEquationSeparate(e.FUNC_ADD, e.FUNC_ADD), e.uniformMatrix4fv(o, !1, this._camera.data.projectionMatrix.buffer), e.uniformMatrix4fv(Q, !1, this._camera.data.viewMatrix.buffer), e.uniform1f(d, Math.sin(Date.now() / 1e3) / 2 + 1 / 2), e.bindBuffer(e.ARRAY_BUFFER, F), e.vertexAttribPointer(a, 2, e.FLOAT, !1, 0, 0), e.bindBuffer(e.ARRAY_BUFFER, g), e.bufferData(e.ARRAY_BUFFER, this._depthIndex, e.STATIC_DRAW), e.vertexAttribIPointer(U, 1, e.INT, 0, 0), e.vertexAttribDivisor(U, 1), e.drawArraysInstanced(e.TRIANGLE_FAN, 0, 4, this._renderData.vertexCount);\n    }, this._dispose = () => {\n      if (!this._scene || !this._camera) {\n        console.error(\"Cannot dispose without scene and camera\");\n        return;\n      }\n      this._scene.removeEventListener(\"objectAdded\", C), this._scene.removeEventListener(\"objectRemoved\", c);\n      for (const u of this._scene.objects)\n        u instanceof tt && this._renderData === u.data && (this._renderData = null, u.removeEventListener(\"objectChanged\", p));\n      A?.terminate(), e.deleteTexture(this._splatTexture), e.deleteBuffer(g), e.deleteBuffer(F);\n    };\n  }\n  get renderData() {\n    return this._renderData;\n  }\n  _getVertexSource() {\n    return Wt;\n  }\n  _getFragmentSource() {\n    return Dt;\n  }\n}\nclass it {\n  constructor(t, n, i) {\n    this.bounds = t, this.boxes = n, this.left = null, this.right = null, this.pointIndices = [], i.length > 1 ? this.split(t, n, i) : i.length > 0 && (this.pointIndices = i);\n  }\n  split(t, n, i) {\n    const e = t.size().maxComponent();\n    i.sort((r, Q) => n[r].center().getComponent(e) - n[Q].center().getComponent(e));\n    const A = Math.floor(i.length / 2), o = i.slice(0, A), s = i.slice(A);\n    this.left = new it(t, n, o), this.right = new it(t, n, s);\n  }\n  queryRange(t) {\n    return this.bounds.intersects(t) ? this.left !== null && this.right !== null ? this.left.queryRange(t).concat(this.right.queryRange(t)) : this.pointIndices.filter((n) => t.intersects(this.boxes[n])) : [];\n  }\n}\nclass yt {\n  constructor(t, n) {\n    const i = n.map((e, A) => A);\n    this.root = new it(t, n, i);\n  }\n  queryRange(t) {\n    return this.root.queryRange(t);\n  }\n}\nclass Kt {\n  constructor(t, n = 100, i = 1) {\n    let e = 0, A = null, o = [];\n    const s = () => {\n      if (t.renderData === null) {\n        console.error(\"IntersectionTester cannot be called before renderProgram has been initialized\");\n        return;\n      }\n      o = [];\n      const r = t.renderData, Q = new Array(r.offsets.size);\n      let I = 0;\n      const d = new nt(\n        new R(1 / 0, 1 / 0, 1 / 0),\n        new R(-1 / 0, -1 / 0, -1 / 0)\n      );\n      for (const a of r.offsets.keys()) {\n        const U = a.bounds;\n        Q[I++] = U, d.expand(U.min), d.expand(U.max), o.push(a);\n      }\n      d.permute(), A = new yt(d, Q), e = r.vertexCount;\n    };\n    this.testPoint = (r, Q) => {\n      if (t.renderData === null || t.camera === null)\n        return console.error(\"IntersectionTester cannot be called before renderProgram has been initialized\"), null;\n      if (s(), A === null)\n        return console.error(\"Failed to build octree for IntersectionTester\"), null;\n      const I = t.renderData, d = t.camera;\n      e !== I.vertexCount && console.warn(\"IntersectionTester has not been rebuilt since the last render\");\n      const a = d.screenPointToRay(r, Q);\n      for (let U = 0; U < n; U += i) {\n        const F = d.position.add(a.multiply(U)), g = new R(\n          F.x - i / 2,\n          F.y - i / 2,\n          F.z - i / 2\n        ), B = new R(\n          F.x + i / 2,\n          F.y + i / 2,\n          F.z + i / 2\n        ), C = new nt(g, B), c = A.queryRange(C);\n        if (c.length > 0)\n          return o[c[0]];\n      }\n      return null;\n    };\n  }\n}\nexport {\n  wt as Camera,\n  Rt as CameraData,\n  dt as Color32,\n  Gt as FPSControls,\n  Nt as FadeInPass,\n  Kt as IntersectionTester,\n  Tt as Loader,\n  _ as Matrix3,\n  K as Matrix4,\n  st as Object3D,\n  Lt as OrbitControls,\n  bt as PLYLoader,\n  vt as Plane,\n  y as Quaternion,\n  Ut as RenderData,\n  Ct as RenderProgram,\n  kt as Scene,\n  _t as ShaderPass,\n  lt as ShaderProgram,\n  X as Splat,\n  Y as SplatData,\n  tt as Splatv,\n  ot as SplatvData,\n  xt as SplatvLoader,\n  R as Vector3,\n  z as Vector4,\n  Yt as VideoRenderProgram,\n  Mt as WebGLRenderer\n};\n//# sourceMappingURL=index.es.js.map\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport * as SPLAT from \"gsplat\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { resolve_wasm_src } from \"@gradio/wasm/svelte\";\n\n\texport let value: FileData;\n\texport let zoom_speed: number;\n\texport let pan_speed: number;\n\n\t$: url = value.url;\n\n\t/* URL resolution for the Wasm mode. */\n\texport let resolved_url: typeof url = undefined; // Exposed to be bound to the download link in the parent component.\n\t// The prop can be updated before the Promise from `resolve_wasm_src` is resolved.\n\t// In such a case, the resolved url for the old `url` has to be discarded,\n\t// This variable `latest_url` is used to pick up only the value resolved for the latest `url`.\n\tlet latest_url: typeof url;\n\t$: {\n\t\t// In normal (non-Wasm) Gradio, the original `url` should be used immediately\n\t\t// without waiting for `resolve_wasm_src()` to resolve.\n\t\t// If it waits, a blank element is displayed until the async task finishes\n\t\t// and it leads to undesirable flickering.\n\t\t// So set `resolved_url` immediately above, and update it with the resolved values below later.\n\t\tresolved_url = url;\n\n\t\tif (url) {\n\t\t\tlatest_url = url;\n\t\t\tconst resolving_url = url;\n\t\t\tresolve_wasm_src(url).then((resolved) => {\n\t\t\t\tif (latest_url === resolving_url) {\n\t\t\t\t\tresolved_url = resolved ?? undefined;\n\t\t\t\t} else {\n\t\t\t\t\tresolved && URL.revokeObjectURL(resolved);\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t}\n\n\tlet canvas: HTMLCanvasElement;\n\tlet scene: SPLAT.Scene;\n\tlet camera: SPLAT.Camera;\n\tlet renderer: SPLAT.WebGLRenderer | null = null;\n\tlet controls: SPLAT.OrbitControls;\n\tlet mounted = false;\n\tlet frameId: number | null = null;\n\n\tfunction reset_scene(): void {\n\t\tif (frameId !== null) {\n\t\t\tcancelAnimationFrame(frameId);\n\t\t\tframeId = null;\n\t\t}\n\n\t\tif (renderer !== null) {\n\t\t\trenderer.dispose();\n\t\t\trenderer = null;\n\t\t}\n\n\t\tscene = new SPLAT.Scene();\n\t\tcamera = new SPLAT.Camera();\n\t\trenderer = new SPLAT.WebGLRenderer(canvas);\n\t\tcontrols = new SPLAT.OrbitControls(camera, canvas);\n\t\tcontrols.zoomSpeed = zoom_speed;\n\t\tcontrols.panSpeed = pan_speed;\n\n\t\tif (!value) {\n\t\t\treturn;\n\t\t}\n\n\t\tlet loading = false;\n\t\tconst load = async (): Promise<void> => {\n\t\t\tif (loading) {\n\t\t\t\tconsole.error(\"Already loading\");\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (!resolved_url) {\n\t\t\t\tthrow new Error(\"No resolved URL\");\n\t\t\t}\n\t\t\tloading = true;\n\t\t\tif (resolved_url.endsWith(\".ply\")) {\n\t\t\t\tawait SPLAT.PLYLoader.LoadAsync(resolved_url, scene, undefined);\n\t\t\t} else if (resolved_url.endsWith(\".splat\")) {\n\t\t\t\tawait SPLAT.Loader.LoadAsync(resolved_url, scene, undefined);\n\t\t\t} else {\n\t\t\t\tthrow new Error(\"Unsupported file type\");\n\t\t\t}\n\t\t\tloading = false;\n\t\t};\n\n\t\tconst frame = (): void => {\n\t\t\tif (!renderer) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (loading) {\n\t\t\t\tframeId = requestAnimationFrame(frame);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tcontrols.update();\n\t\t\trenderer.render(scene, camera);\n\n\t\t\tframeId = requestAnimationFrame(frame);\n\t\t};\n\n\t\tload();\n\t\tframeId = requestAnimationFrame(frame);\n\t}\n\n\tonMount(() => {\n\t\tif (value != null) {\n\t\t\treset_scene();\n\t\t}\n\t\tmounted = true;\n\n\t\treturn () => {\n\t\t\tif (renderer) {\n\t\t\t\trenderer.dispose();\n\t\t\t}\n\t\t};\n\t});\n\n\t$: ({ path } = value || {\n\t\tpath: undefined\n\t});\n\n\t$: canvas && mounted && path && reset_scene();\n</script>\n\n<canvas bind:this={canvas}></canvas>\n"], "names": ["R", "n", "i", "e", "A", "o", "s", "r", "Q", "K", "a", "U", "F", "g", "B", "C", "c", "p", "u", "S", "W", "It", "ht", "ct", "_", "Y", "et", "Z", "k", "f", "L", "M", "V", "w", "nt", "z", "wt", "kt", "rt", "E", "At", "Tt", "bt", "Bt", "Et", "Qt", "St", "lt", "at", "Jt", "mt", "h", "m", "j", "N", "v", "D", "l", "G", "ft", "Ut", "dt", "Vt", "Zt", "pt", "Nt", "Mt", "Lt", "insert", "target", "canvas_1", "anchor", "value", "$$props", "zoom_speed", "pan_speed", "resolved_url", "latest_url", "canvas", "scene", "camera", "renderer", "controls", "mounted", "frameId", "reset_scene", "SPLAT.Scene", "SPLAT.Camera", "SPLAT.WebGLRenderer", "SPLAT.OrbitControls", "loading", "load", "SPLAT.PLYLoader", "SPLAT.Loader", "frame", "onMount", "$$value", "$$invalidate", "url", "resolving_url", "resolve_wasm_src", "resolved", "path"], "mappings": "6GAAA,MAAMA,CAAE,CACN,YAAY,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAG,CAC/B,KAAK,EAAI,EAAG,KAAK,EAAID,EAAG,KAAK,EAAIC,CAClC,CACD,OAAO,EAAG,CACR,MAAO,EAAE,KAAK,IAAM,EAAE,GAAK,KAAK,IAAM,EAAE,GAAK,KAAK,IAAM,EAAE,EAC3D,CACD,IAAI,EAAG,CACL,OAAO,OAAO,GAAK,SAAW,IAAIF,EAAE,KAAK,EAAI,EAAG,KAAK,EAAI,EAAG,KAAK,EAAI,CAAC,EAAI,IAAIA,EAAE,KAAK,EAAI,EAAE,EAAG,KAAK,EAAI,EAAE,EAAG,KAAK,EAAI,EAAE,CAAC,CACzH,CACD,SAAS,EAAG,CACV,OAAO,OAAO,GAAK,SAAW,IAAIA,EAAE,KAAK,EAAI,EAAG,KAAK,EAAI,EAAG,KAAK,EAAI,CAAC,EAAI,IAAIA,EAAE,KAAK,EAAI,EAAE,EAAG,KAAK,EAAI,EAAE,EAAG,KAAK,EAAI,EAAE,CAAC,CACzH,CACD,SAAS,EAAG,CACV,OAAO,OAAO,GAAK,SAAW,IAAIA,EAAE,KAAK,EAAI,EAAG,KAAK,EAAI,EAAG,KAAK,EAAI,CAAC,EAAI,aAAaA,EAAI,IAAIA,EAAE,KAAK,EAAI,EAAE,EAAG,KAAK,EAAI,EAAE,EAAG,KAAK,EAAI,EAAE,CAAC,EAAI,IAAIA,EAC/I,KAAK,EAAI,EAAE,OAAO,CAAC,EAAI,KAAK,EAAI,EAAE,OAAO,CAAC,EAAI,KAAK,EAAI,EAAE,OAAO,CAAC,EAAI,EAAE,OAAO,EAAE,EAChF,KAAK,EAAI,EAAE,OAAO,CAAC,EAAI,KAAK,EAAI,EAAE,OAAO,CAAC,EAAI,KAAK,EAAI,EAAE,OAAO,CAAC,EAAI,EAAE,OAAO,EAAE,EAChF,KAAK,EAAI,EAAE,OAAO,CAAC,EAAI,KAAK,EAAI,EAAE,OAAO,CAAC,EAAI,KAAK,EAAI,EAAE,OAAO,EAAE,EAAI,EAAE,OAAO,EAAE,CACvF,CACG,CACD,OAAO,EAAG,CACR,OAAO,OAAO,GAAK,SAAW,IAAIA,EAAE,KAAK,EAAI,EAAG,KAAK,EAAI,EAAG,KAAK,EAAI,CAAC,EAAI,IAAIA,EAAE,KAAK,EAAI,EAAE,EAAG,KAAK,EAAI,EAAE,EAAG,KAAK,EAAI,EAAE,CAAC,CACzH,CACD,MAAM,EAAG,CACP,MAAMC,EAAI,KAAK,EAAI,EAAE,EAAI,KAAK,EAAI,EAAE,EAAGC,EAAI,KAAK,EAAI,EAAE,EAAI,KAAK,EAAI,EAAE,EAAGC,EAAI,KAAK,EAAI,EAAE,EAAI,KAAK,EAAI,EAAE,EACtG,OAAO,IAAIH,EAAEC,EAAGC,EAAGC,CAAC,CACrB,CACD,IAAI,EAAG,CACL,OAAO,KAAK,EAAI,EAAE,EAAI,KAAK,EAAI,EAAE,EAAI,KAAK,EAAI,EAAE,CACjD,CACD,KAAK,EAAGF,EAAG,CACT,OAAO,IAAID,EAAE,KAAK,GAAK,EAAE,EAAI,KAAK,GAAKC,EAAG,KAAK,GAAK,EAAE,EAAI,KAAK,GAAKA,EAAG,KAAK,GAAK,EAAE,EAAI,KAAK,GAAKA,CAAC,CACnG,CACD,IAAI,EAAG,CACL,OAAO,IAAID,EAAE,KAAK,IAAI,KAAK,EAAG,EAAE,CAAC,EAAG,KAAK,IAAI,KAAK,EAAG,EAAE,CAAC,EAAG,KAAK,IAAI,KAAK,EAAG,EAAE,CAAC,CAAC,CACjF,CACD,IAAI,EAAG,CACL,OAAO,IAAIA,EAAE,KAAK,IAAI,KAAK,EAAG,EAAE,CAAC,EAAG,KAAK,IAAI,KAAK,EAAG,EAAE,CAAC,EAAG,KAAK,IAAI,KAAK,EAAG,EAAE,CAAC,CAAC,CACjF,CACD,aAAa,EAAG,CACd,OAAQ,EAAC,CACP,IAAK,GACH,OAAO,KAAK,EACd,IAAK,GACH,OAAO,KAAK,EACd,IAAK,GACH,OAAO,KAAK,EACd,QACE,MAAM,IAAI,MAAM,4BAA4B,CAAC,EAAE,CAClD,CACF,CACD,cAAe,CACb,OAAO,KAAK,EAAI,KAAK,GAAK,KAAK,EAAI,KAAK,EAAI,EAAI,KAAK,EAAI,KAAK,EAAI,EAAI,CACvE,CACD,cAAe,CACb,OAAO,KAAK,EAAI,KAAK,GAAK,KAAK,EAAI,KAAK,EAAI,EAAI,KAAK,EAAI,KAAK,EAAI,EAAI,CACvE,CACD,WAAY,CACV,OAAO,KAAK,KAAK,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,CAAC,CACrE,CACD,WAAW,EAAG,CACZ,OAAO,KAAK,MAAM,KAAK,EAAI,EAAE,IAAM,GAAK,KAAK,EAAI,EAAE,IAAM,GAAK,KAAK,EAAI,EAAE,IAAM,CAAC,CACjF,CACD,WAAY,CACV,MAAM,EAAI,KAAK,YACf,OAAO,IAAIA,EAAE,KAAK,EAAI,EAAG,KAAK,EAAI,EAAG,KAAK,EAAI,CAAC,CAChD,CACD,MAAO,CACL,MAAO,CAAC,KAAK,EAAG,KAAK,EAAG,KAAK,CAAC,CAC/B,CACD,OAAQ,CACN,OAAO,IAAIA,EAAE,KAAK,EAAG,KAAK,EAAG,KAAK,CAAC,CACpC,CACD,UAAW,CACT,MAAO,IAAI,KAAK,KAAI,EAAG,KAAK,IAAI,CAAC,GAClC,CACD,OAAO,IAAI,EAAI,EAAG,CAChB,OAAO,IAAIA,EAAE,EAAG,EAAG,CAAC,CACrB,CACH,CACA,MAAM,CAAE,CACN,YAAY,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAG,CACtC,KAAK,EAAI,EAAG,KAAK,EAAIF,EAAG,KAAK,EAAIC,EAAG,KAAK,EAAIC,CAC9C,CACD,OAAO,EAAG,CACR,MAAO,EAAE,KAAK,IAAM,EAAE,GAAK,KAAK,IAAM,EAAE,GAAK,KAAK,IAAM,EAAE,GAAK,KAAK,IAAM,EAAE,EAC7E,CACD,WAAY,CACV,MAAM,EAAI,KAAK,KAAK,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,CAAC,EACzF,OAAO,IAAI,EAAE,KAAK,EAAI,EAAG,KAAK,EAAI,EAAG,KAAK,EAAI,EAAG,KAAK,EAAI,CAAC,CAC5D,CACD,SAAS,EAAG,CACV,MAAMF,EAAI,KAAK,EAAGC,EAAI,KAAK,EAAGC,EAAI,KAAK,EAAGC,EAAI,KAAK,EAAGC,EAAI,EAAE,EAAGC,EAAI,EAAE,EAAGC,EAAI,EAAE,EAAGC,EAAI,EAAE,EACvF,OAAO,IAAI,EACTP,EAAIK,EAAIJ,EAAIG,EAAIF,EAAIK,EAAIJ,EAAIG,EAC5BN,EAAIM,EAAIL,EAAIM,EAAIL,EAAIE,EAAID,EAAIE,EAC5BL,EAAIO,EAAIN,EAAIK,EAAIJ,EAAIG,EAAIF,EAAIC,EAC5BJ,EAAII,EAAIH,EAAII,EAAIH,EAAII,EAAIH,EAAII,CAClC,CACG,CACD,SAAU,CACR,MAAM,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,EAC9E,OAAO,IAAI,EAAE,CAAC,KAAK,EAAI,EAAG,CAAC,KAAK,EAAI,EAAG,CAAC,KAAK,EAAI,EAAG,KAAK,EAAI,CAAC,CAC/D,CACD,MAAM,EAAG,CACP,MAAMP,EAAI,IAAI,EAAE,EAAE,EAAG,EAAE,EAAG,EAAE,EAAG,CAAC,EAAGC,EAAI,IAAI,EAAE,CAAC,KAAK,EAAG,CAAC,KAAK,EAAG,CAAC,KAAK,EAAG,KAAK,CAAC,EAAGC,EAAI,KAAK,SAASF,CAAC,EAAE,SAASC,CAAC,EAChH,OAAO,IAAIF,EAAEG,EAAE,EAAGA,EAAE,EAAGA,EAAE,CAAC,CAC3B,CACD,MAAO,CACL,MAAO,CAAC,KAAK,EAAG,KAAK,EAAG,KAAK,EAAG,KAAK,CAAC,CACvC,CACD,OAAQ,CACN,OAAO,IAAI,EAAE,KAAK,EAAG,KAAK,EAAG,KAAK,EAAG,KAAK,CAAC,CAC5C,CACD,OAAO,UAAU,EAAG,CAClB,MAAMF,EAAI,EAAE,EAAI,EAAGC,EAAI,EAAE,EAAI,EAAGC,EAAI,EAAE,EAAI,EAAGC,EAAI,KAAK,IAAIF,CAAC,EAAGG,EAAI,KAAK,IAAIH,CAAC,EAAGI,EAAI,KAAK,IAAIL,CAAC,EAAGM,EAAI,KAAK,IAAIN,CAAC,EAAGO,EAAI,KAAK,IAAIL,CAAC,EAAG,EAAI,KAAK,IAAIA,CAAC,EAChJ,OAAO,IAAI,EACTC,EAAIG,EAAIC,EAAIH,EAAIC,EAAI,EACpBD,EAAIC,EAAIE,EAAIJ,EAAIG,EAAI,EACpBH,EAAIE,EAAI,EAAID,EAAIE,EAAIC,EACpBJ,EAAIE,EAAIE,EAAIH,EAAIE,EAAI,CAC1B,CACG,CACD,SAAU,CACR,MAAM,EAAI,GAAK,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,GAAIN,EAAI,EAAI,GAAK,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,GAAIC,EAAI,KAAK,MAAM,EAAGD,CAAC,EACvH,IAAIE,EACJ,MAAMC,EAAI,GAAK,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,GAC/C,KAAK,IAAIA,CAAC,GAAK,EAAID,EAAI,KAAK,KAAKC,CAAC,EAAI,KAAK,GAAK,EAAID,EAAI,KAAK,KAAKC,CAAC,EACnE,MAAMC,EAAI,GAAK,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,GAAIC,EAAI,EAAI,GAAK,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,GAAIC,EAAI,KAAK,MAAMF,EAAGC,CAAC,EACvH,OAAO,IAAIN,EAAEE,EAAGC,EAAGI,CAAC,CACrB,CACD,OAAO,YAAY,EAAG,CACpB,MAAMN,EAAI,EAAE,OAAQC,EAAID,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EACzC,IAAIE,EAAGC,EAAGC,EAAGC,EACb,GAAIJ,EAAI,EAAG,CACT,MAAMK,EAAI,GAAM,KAAK,KAAKL,EAAI,CAAC,EAC/BI,EAAI,IAAOC,EAAGJ,GAAKF,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKM,EAAGH,GAAKH,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKM,EAAGF,GAAKJ,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKM,CACjF,SAAUN,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAG,CACrC,MAAMM,EAAI,EAAI,KAAK,KAAK,EAAIN,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,CAAC,EAC9CK,GAAKL,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKM,EAAGJ,EAAI,IAAOI,EAAGH,GAAKH,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKM,EAAGF,GAAKJ,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKM,CACjF,SAAUN,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAG,CACtB,MAAMM,EAAI,EAAI,KAAK,KAAK,EAAIN,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,CAAC,EAC9CK,GAAKL,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKM,EAAGJ,GAAKF,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKM,EAAGH,EAAI,IAAOG,EAAGF,GAAKJ,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKM,CACtF,KAAW,CACL,MAAMA,EAAI,EAAI,KAAK,KAAK,EAAIN,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,CAAC,EAC9CK,GAAKL,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKM,EAAGJ,GAAKF,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKM,EAAGH,GAAKH,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAKM,EAAGF,EAAI,IAAOE,CACjF,CACD,OAAO,IAAI,EAAEJ,EAAGC,EAAGC,EAAGC,CAAC,CACxB,CACD,OAAO,cAAc,EAAGL,EAAG,CACzB,MAAMC,EAAID,EAAI,EAAGE,EAAI,KAAK,IAAID,CAAC,EAAGE,EAAI,KAAK,IAAIF,CAAC,EAChD,OAAO,IAAI,EAAE,EAAE,EAAIC,EAAG,EAAE,EAAIA,EAAG,EAAE,EAAIA,EAAGC,CAAC,CAC1C,CACD,OAAO,aAAa,EAAG,CACrB,MAAMH,EAAI,IAAID,EAAE,EAAG,EAAG,CAAC,EAAGE,EAAID,EAAE,IAAI,CAAC,EACrC,GAAI,KAAK,IAAIC,EAAI,GAAE,EAAI,KACrB,OAAO,IAAI,EAAE,EAAG,EAAG,EAAG,KAAK,EAAE,EAC/B,GAAI,KAAK,IAAIA,EAAI,CAAC,EAAI,KACpB,OAAO,IAAI,EACb,MAAMC,EAAI,KAAK,KAAKD,CAAC,EAAGE,EAAIH,EAAE,MAAM,CAAC,EAAE,UAAS,EAChD,OAAO,EAAE,cAAcG,EAAGD,CAAC,CAC5B,CACD,UAAW,CACT,MAAO,IAAI,KAAK,KAAI,EAAG,KAAK,IAAI,CAAC,GAClC,CACH,CACA,MAAM,EAAG,CACP,aAAc,CACZ,MAAM,EAAoB,IAAI,IAC9B,KAAK,iBAAmB,CAACF,EAAGC,IAAM,CAChC,EAAE,IAAID,CAAC,GAAK,EAAE,IAAIA,EAAmB,IAAI,GAAK,EAAG,EAAE,IAAIA,CAAC,EAAE,IAAIC,CAAC,CAChE,EAAE,KAAK,oBAAsB,CAACD,EAAGC,IAAM,CACtC,EAAE,IAAID,CAAC,GAAK,EAAE,IAAIA,CAAC,EAAE,OAAOC,CAAC,CACnC,EAAO,KAAK,iBAAmB,CAACD,EAAGC,IAAM,EAAE,IAAID,CAAC,EAAI,EAAE,IAAIA,CAAC,EAAE,IAAIC,CAAC,EAAI,GAAI,KAAK,cAAiBD,GAAM,CAChG,GAAI,EAAE,IAAIA,EAAE,IAAI,EACd,UAAWC,KAAK,EAAE,IAAID,EAAE,IAAI,EAC1BC,EAAED,CAAC,CACb,CACG,CACH,CACA,MAAMQ,CAAE,CAEN,YAAY,EAAI,EAAGR,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAG,EAAI,EAAG,EAAI,EAAGE,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAG,CAC1H,KAAK,OAAS,CACZ,EACAb,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACA,EACA,EACAE,EACAC,EACAC,EACAC,EACAC,CACN,CACG,CACD,OAAO,EAAG,CACR,GAAI,KAAK,OAAO,SAAW,EAAE,OAAO,OAClC,MAAO,GACT,GAAI,KAAK,SAAW,EAAE,OACpB,MAAO,GACT,QAASb,EAAI,EAAGA,EAAI,KAAK,OAAO,OAAQA,IACtC,GAAI,KAAK,OAAOA,CAAC,IAAM,EAAE,OAAOA,CAAC,EAC/B,MAAO,GACX,MAAO,EACR,CACD,SAAS,EAAG,CACV,MAAMA,EAAI,KAAK,OAAQC,EAAI,EAAE,OAC7B,OAAO,IAAIO,EACTP,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,EAAE,EACrDC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,EAAE,EACrDC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,EAAE,EAAIC,EAAE,CAAC,EAAID,EAAE,EAAE,EACtDC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,EAAE,EAAIC,EAAE,CAAC,EAAID,EAAE,EAAE,EACtDC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,EAAE,EACrDC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,EAAE,EACrDC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,EAAE,EAAIC,EAAE,CAAC,EAAID,EAAE,EAAE,EACtDC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,EAAE,EAAIC,EAAE,CAAC,EAAID,EAAE,EAAE,EACtDC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAE,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAE,EAAID,EAAE,EAAE,EACvDC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAE,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAE,EAAID,EAAE,EAAE,EACvDC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAE,EAAID,EAAE,EAAE,EAAIC,EAAE,EAAE,EAAID,EAAE,EAAE,EACxDC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAE,EAAID,EAAE,EAAE,EAAIC,EAAE,EAAE,EAAID,EAAE,EAAE,EACxDC,EAAE,EAAE,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAE,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAE,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAE,EAAID,EAAE,EAAE,EACzDC,EAAE,EAAE,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAE,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAE,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAE,EAAID,EAAE,EAAE,EACzDC,EAAE,EAAE,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAE,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAE,EAAID,EAAE,EAAE,EAAIC,EAAE,EAAE,EAAID,EAAE,EAAE,EAC1DC,EAAE,EAAE,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAE,EAAID,EAAE,CAAC,EAAIC,EAAE,EAAE,EAAID,EAAE,EAAE,EAAIC,EAAE,EAAE,EAAID,EAAE,EAAE,CAChE,CACG,CACD,OAAQ,CACN,MAAM,EAAI,KAAK,OACf,OAAO,IAAIQ,EACT,EAAE,CAAC,EACH,EAAE,CAAC,EACH,EAAE,CAAC,EACH,EAAE,CAAC,EACH,EAAE,CAAC,EACH,EAAE,CAAC,EACH,EAAE,CAAC,EACH,EAAE,CAAC,EACH,EAAE,CAAC,EACH,EAAE,CAAC,EACH,EAAE,EAAE,EACJ,EAAE,EAAE,EACJ,EAAE,EAAE,EACJ,EAAE,EAAE,EACJ,EAAE,EAAE,EACJ,EAAE,EAAE,CACV,CACG,CACD,aAAc,CACZ,MAAM,EAAI,KAAK,OACf,OAAO,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,CACxsB,CACD,QAAS,CACP,MAAM,EAAI,KAAK,OAAQR,EAAI,KAAK,cAChC,GAAIA,IAAM,EACR,MAAM,IAAI,MAAM,2BAA2B,EAC7C,MAAMC,EAAI,EAAID,EACd,OAAO,IAAIQ,EACTP,GAAK,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,GACzIA,GAAK,CAAC,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,GAC1IA,GAAK,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,GACrIA,GAAK,CAAC,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,GACpIA,GAAK,CAAC,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,GAC1IA,GAAK,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,GACzIA,GAAK,CAAC,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,GACtIA,GAAK,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,GACnIA,GAAK,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,GACvIA,GAAK,CAAC,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,GACxIA,GAAK,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,GACrIA,GAAK,CAAC,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,GAClIA,GAAK,CAAC,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,GACxIA,GAAK,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,GACvIA,GAAK,CAAC,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,GACtIA,GAAK,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,EAAE,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EAAI,EAAE,CAAC,EACvI,CACG,CACD,OAAO,QAAQ,EAAGD,EAAGC,EAAG,CACtB,MAAMC,EAAIF,EAAE,EAAGG,EAAIH,EAAE,EAAGI,EAAIJ,EAAE,EAAGK,EAAIL,EAAE,EAAGM,EAAIJ,EAAIA,EAAGK,EAAIJ,EAAIA,EAAG,EAAIC,EAAIA,EAAG,EAAIF,EAAII,EAAGG,EAAIP,EAAIK,EAAGG,EAAIR,EAAI,EAAGS,EAAIR,EAAII,EAAGK,EAAIT,EAAI,EAAGU,EAAIT,EAAI,EAAGU,EAAIT,EAAIC,EAAGS,EAAIV,EAAIE,EAAGS,EAAIX,EAAI,EAAGY,EAAIhB,EAAE,EAAGiB,EAAIjB,EAAE,EAAGkB,EAAIlB,EAAE,EACtM,OAAO,IAAIO,GACR,GAAKG,EAAIE,IAAMI,GACfR,EAAIO,GAAKC,GACTP,EAAIK,GAAKE,EACV,GACCR,EAAIO,GAAKE,GACT,GAAK,EAAIL,IAAMK,GACfN,EAAIE,GAAKI,EACV,GACCR,EAAIK,GAAKI,GACTP,EAAIE,GAAKK,GACT,GAAK,EAAIR,IAAMQ,EAChB,EACA,EAAE,EACF,EAAE,EACF,EAAE,EACF,CACN,CACG,CACD,UAAW,CACT,MAAO,IAAI,KAAK,OAAO,KAAK,IAAI,CAAC,GAClC,CACH,CACA,MAAMC,WAAW,KAAM,CACrB,YAAY,EAAG,CACb,MAAM,aAAa,EAAG,KAAK,OAAS,CACrC,CACH,CACA,MAAMC,WAAW,KAAM,CACrB,YAAY,EAAG,CACb,MAAM,eAAe,EAAG,KAAK,OAAS,CACvC,CACH,CACA,MAAMC,WAAW,KAAM,CACrB,YAAY,EAAG,CACb,MAAM,eAAe,EAAG,KAAK,OAAS,CACvC,CACH,CACA,MAAM,WAAW,EAAG,CAClB,aAAc,CACZ,MAAO,EAAE,KAAK,gBAAkB,GAAI,KAAK,gBAAkB,GAAI,KAAK,aAAe,GAAI,KAAK,UAAY,IAAIvB,EAAK,KAAK,UAAY,IAAI,EAAK,KAAK,OAAS,IAAIA,EAAE,EAAG,EAAG,CAAC,EAAG,KAAK,WAAa,IAAIS,EAAK,KAAK,aAAe,IAAIc,GAAG,IAAI,EAAG,KAAK,OAAS,IAAM,CAC9P,EAAO,KAAK,cAAgB,IAAM,CAC5B,KAAK,SAAW,IAAIvB,CAC1B,EAAO,KAAK,cAAgB,IAAM,CAC5B,KAAK,SAAW,IAAI,CAC1B,EAAO,KAAK,WAAa,IAAM,CACzB,KAAK,MAAQ,IAAIA,EAAE,EAAG,EAAG,CAAC,CAChC,EAAO,KAAK,iBAAmB,IAAM,CAC/B,KAAK,cAAc,KAAK,YAAY,CAC1C,CACG,CACD,eAAgB,CACd,KAAK,WAAaS,EAAE,QAAQ,KAAK,UAAW,KAAK,UAAW,KAAK,MAAM,CACxE,CACD,IAAI,UAAW,CACb,OAAO,KAAK,SACb,CACD,IAAI,SAAS,EAAG,CACd,KAAK,UAAU,OAAO,CAAC,IAAM,KAAK,UAAY,EAAG,KAAK,gBAAkB,GAAI,KAAK,gBAAiB,KAAK,cAAc,KAAK,YAAY,EACvI,CACD,IAAI,UAAW,CACb,OAAO,KAAK,SACb,CACD,IAAI,SAAS,EAAG,CACd,KAAK,UAAU,OAAO,CAAC,IAAM,KAAK,UAAY,EAAG,KAAK,gBAAkB,GAAI,KAAK,gBAAiB,KAAK,cAAc,KAAK,YAAY,EACvI,CACD,IAAI,OAAQ,CACV,OAAO,KAAK,MACb,CACD,IAAI,MAAM,EAAG,CACX,KAAK,OAAO,OAAO,CAAC,IAAM,KAAK,OAAS,EAAG,KAAK,aAAe,GAAI,KAAK,gBAAiB,KAAK,cAAc,KAAK,YAAY,EAC9H,CACD,IAAI,SAAU,CACZ,IAAI,EAAI,IAAIT,EAAE,EAAG,EAAG,CAAC,EACrB,OAAO,EAAI,KAAK,SAAS,MAAM,CAAC,EAAG,CACpC,CACD,IAAI,WAAY,CACd,OAAO,KAAK,UACb,CACH,CACA,MAAMwB,CAAE,CAEN,YAAY,EAAI,EAAGvB,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAG,CACzE,KAAK,OAAS,CACZ,EACAP,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,CACN,CACG,CACD,OAAO,EAAG,CACR,GAAI,KAAK,OAAO,SAAW,EAAE,OAAO,OAClC,MAAO,GACT,GAAI,KAAK,SAAW,EAAE,OACpB,MAAO,GACT,QAASP,EAAI,EAAGA,EAAI,KAAK,OAAO,OAAQA,IACtC,GAAI,KAAK,OAAOA,CAAC,IAAM,EAAE,OAAOA,CAAC,EAC/B,MAAO,GACX,MAAO,EACR,CACD,SAAS,EAAG,CACV,MAAMA,EAAI,KAAK,OAAQC,EAAI,EAAE,OAC7B,OAAO,IAAIsB,EACTtB,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EACtCC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EACtCC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EACtCC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EACtCC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EACtCC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EACtCC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EACtCC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EACtCC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,CAC5C,CACG,CACD,OAAQ,CACN,MAAM,EAAI,KAAK,OACf,OAAO,IAAIuB,EACT,EAAE,CAAC,EACH,EAAE,CAAC,EACH,EAAE,CAAC,EACH,EAAE,CAAC,EACH,EAAE,CAAC,EACH,EAAE,CAAC,EACH,EAAE,CAAC,EACH,EAAE,CAAC,EACH,EAAE,CAAC,CACT,CACG,CACD,OAAO,IAAI,EAAI,EAAG,CAChB,OAAO,IAAIA,EAAE,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,CACvC,CACD,OAAO,SAAS,EAAG,CACjB,OAAO,IAAIA,EAAE,EAAE,EAAG,EAAG,EAAG,EAAG,EAAE,EAAG,EAAG,EAAG,EAAG,EAAE,CAAC,CAC7C,CACD,OAAO,uBAAuB,EAAG,CAC/B,OAAO,IAAIA,EACT,EAAI,EAAI,EAAE,EAAI,EAAE,EAAI,EAAI,EAAE,EAAI,EAAE,EAChC,EAAI,EAAE,EAAI,EAAE,EAAI,EAAI,EAAE,EAAI,EAAE,EAC5B,EAAI,EAAE,EAAI,EAAE,EAAI,EAAI,EAAE,EAAI,EAAE,EAC5B,EAAI,EAAE,EAAI,EAAE,EAAI,EAAI,EAAE,EAAI,EAAE,EAC5B,EAAI,EAAI,EAAE,EAAI,EAAE,EAAI,EAAI,EAAE,EAAI,EAAE,EAChC,EAAI,EAAE,EAAI,EAAE,EAAI,EAAI,EAAE,EAAI,EAAE,EAC5B,EAAI,EAAE,EAAI,EAAE,EAAI,EAAI,EAAE,EAAI,EAAE,EAC5B,EAAI,EAAE,EAAI,EAAE,EAAI,EAAI,EAAE,EAAI,EAAE,EAC5B,EAAI,EAAI,EAAE,EAAI,EAAE,EAAI,EAAI,EAAE,EAAI,EAAE,CACtC,CACG,CACD,OAAO,kBAAkB,EAAG,CAC1B,MAAMvB,EAAI,KAAK,IAAI,EAAE,CAAC,EAAGC,EAAI,KAAK,IAAI,EAAE,CAAC,EAAGC,EAAI,KAAK,IAAI,EAAE,CAAC,EAAGC,EAAI,KAAK,IAAI,EAAE,CAAC,EAAGC,EAAI,KAAK,IAAI,EAAE,CAAC,EAAGC,EAAI,KAAK,IAAI,EAAE,CAAC,EAAGC,EAAI,CAC1HJ,EAAIE,EAAID,EAAIF,EAAII,EAChB,CAACH,EAAIG,EAAIF,EAAIF,EAAIG,EACjBD,EAAIH,EACJA,EAAIK,EACJL,EAAII,EACJ,CAACH,EACD,CAACE,EAAIC,EAAIF,EAAID,EAAII,EACjBF,EAAIE,EAAIH,EAAID,EAAIG,EAChBF,EAAIF,CACV,EACI,OAAO,IAAIuB,EAAE,GAAGjB,CAAC,CAClB,CACD,UAAW,CACT,MAAO,IAAI,KAAK,OAAO,KAAK,IAAI,CAAC,GAClC,CACH,CACA,MAAMkB,CAAE,CACN,YAAY,EAAI,EAAGxB,EAAI,KAAMC,EAAI,KAAMC,EAAI,KAAMC,EAAI,KAAM,CACzD,KAAK,QAAU,GAAI,KAAK,SAAW,GAAI,KAAK,aAAe,EAAG,KAAK,WAAaH,GAAK,IAAI,aAAa,CAAC,EAAG,KAAK,WAAaC,GAAK,IAAI,aAAa,CAAC,EAAG,KAAK,QAAUC,GAAK,IAAI,aAAa,CAAC,EAAG,KAAK,QAAUC,GAAK,IAAI,WAAW,CAAC,EAAG,KAAK,WAAa,IAAI,WAAW,KAAK,WAAW,EAAG,KAAK,UAAaC,GAAM,CAChT,QAASC,EAAI,EAAGA,EAAI,KAAK,YAAaA,IACpC,KAAK,UAAU,EAAIA,EAAI,CAAC,GAAKD,EAAE,EAAG,KAAK,UAAU,EAAIC,EAAI,CAAC,GAAKD,EAAE,EAAG,KAAK,UAAU,EAAIC,EAAI,CAAC,GAAKD,EAAE,EACrG,KAAK,QAAU,EACrB,EAAO,KAAK,OAAUA,GAAM,CACtB,MAAMC,EAAIkB,EAAE,uBAAuBnB,CAAC,EAAE,OACtC,QAASE,EAAI,EAAGA,EAAI,KAAK,YAAaA,IAAK,CACzC,MAAMC,EAAI,KAAK,UAAU,EAAID,EAAI,CAAC,EAAG,EAAI,KAAK,UAAU,EAAIA,EAAI,CAAC,EAAG,EAAI,KAAK,UAAU,EAAIA,EAAI,CAAC,EAChG,KAAK,UAAU,EAAIA,EAAI,CAAC,EAAID,EAAE,CAAC,EAAIE,EAAIF,EAAE,CAAC,EAAI,EAAIA,EAAE,CAAC,EAAI,EAAG,KAAK,UAAU,EAAIC,EAAI,CAAC,EAAID,EAAE,CAAC,EAAIE,EAAIF,EAAE,CAAC,EAAI,EAAIA,EAAE,CAAC,EAAI,EAAG,KAAK,UAAU,EAAIC,EAAI,CAAC,EAAID,EAAE,CAAC,EAAIE,EAAIF,EAAE,CAAC,EAAI,EAAIA,EAAE,CAAC,EAAI,EACjL,MAAMI,EAAI,IAAI,EACZ,KAAK,UAAU,EAAIH,EAAI,CAAC,EACxB,KAAK,UAAU,EAAIA,EAAI,CAAC,EACxB,KAAK,UAAU,EAAIA,EAAI,CAAC,EACxB,KAAK,UAAU,EAAIA,EAAI,CAAC,CACzB,EAAEI,EAAIN,EAAE,SAASK,CAAC,EACnB,KAAK,UAAU,EAAIH,EAAI,CAAC,EAAII,EAAE,EAAG,KAAK,UAAU,EAAIJ,EAAI,CAAC,EAAII,EAAE,EAAG,KAAK,UAAU,EAAIJ,EAAI,CAAC,EAAII,EAAE,EAAG,KAAK,UAAU,EAAIJ,EAAI,CAAC,EAAII,EAAE,CAClI,CACD,KAAK,QAAU,EACrB,EAAO,KAAK,MAASN,GAAM,CACrB,QAASC,EAAI,EAAGA,EAAI,KAAK,YAAaA,IACpC,KAAK,UAAU,EAAIA,EAAI,CAAC,GAAKD,EAAE,EAAG,KAAK,UAAU,EAAIC,EAAI,CAAC,GAAKD,EAAE,EAAG,KAAK,UAAU,EAAIC,EAAI,CAAC,GAAKD,EAAE,EAAG,KAAK,OAAO,EAAIC,EAAI,CAAC,GAAKD,EAAE,EAAG,KAAK,OAAO,EAAIC,EAAI,CAAC,GAAKD,EAAE,EAAG,KAAK,OAAO,EAAIC,EAAI,CAAC,GAAKD,EAAE,EAClM,KAAK,QAAU,EACrB,EAAO,KAAK,UAAY,IAAM,CACxB,MAAMA,EAAI,IAAI,WAAW,KAAK,YAAcoB,EAAE,SAAS,EAAGnB,EAAI,IAAI,aAAaD,EAAE,MAAM,EAAGE,EAAI,IAAI,WAAWF,EAAE,MAAM,EACrH,QAASG,EAAI,EAAGA,EAAI,KAAK,YAAaA,IACpCF,EAAE,EAAIE,EAAI,CAAC,EAAI,KAAK,UAAU,EAAIA,EAAI,CAAC,EAAGF,EAAE,EAAIE,EAAI,CAAC,EAAI,KAAK,UAAU,EAAIA,EAAI,CAAC,EAAGF,EAAE,EAAIE,EAAI,CAAC,EAAI,KAAK,UAAU,EAAIA,EAAI,CAAC,EAAGD,EAAE,GAAKC,EAAI,GAAK,CAAC,EAAI,KAAK,OAAO,EAAIA,EAAI,CAAC,EAAGD,EAAE,GAAKC,EAAI,GAAK,CAAC,EAAI,KAAK,OAAO,EAAIA,EAAI,CAAC,EAAGD,EAAE,GAAKC,EAAI,GAAK,CAAC,EAAI,KAAK,OAAO,EAAIA,EAAI,CAAC,EAAGD,EAAE,GAAKC,EAAI,GAAK,CAAC,EAAI,KAAK,OAAO,EAAIA,EAAI,CAAC,EAAGF,EAAE,EAAIE,EAAI,EAAI,CAAC,EAAI,KAAK,OAAO,EAAIA,EAAI,CAAC,EAAGF,EAAE,EAAIE,EAAI,EAAI,CAAC,EAAI,KAAK,OAAO,EAAIA,EAAI,CAAC,EAAGF,EAAE,EAAIE,EAAI,EAAI,CAAC,EAAI,KAAK,OAAO,EAAIA,EAAI,CAAC,EAAGD,EAAE,GAAKC,EAAI,GAAK,CAAC,EAAI,KAAK,UAAU,EAAIA,EAAI,CAAC,EAAI,IAAM,IAAM,IAAKD,EAAE,GAAKC,EAAI,GAAK,CAAC,EAAI,KAAK,UAAU,EAAIA,EAAI,CAAC,EAAI,IAAM,IAAM,IAAKD,EAAE,GAAKC,EAAI,GAAK,CAAC,EAAI,KAAK,UAAU,EAAIA,EAAI,CAAC,EAAI,IAAM,IAAM,IAAKD,EAAE,GAAKC,EAAI,GAAK,CAAC,EAAI,KAAK,UAAU,EAAIA,EAAI,CAAC,EAAI,IAAM,IAAM,IACxrB,OAAOH,CACb,EAAO,KAAK,SAAW,CAACA,EAAGC,EAAGC,EAAGC,EAAG,IAAM,CACpC,QAAQ,OACNH,EAAE,aAAe,KAAK,YAAc,EAAI,EACxC,YAAY,KAAK,YAAc,EAAI,CAAC,eAAeA,EAAE,UAAU,QAChE,EAAE,KAAK,WAAa,IAAI,aAAaA,CAAC,EAAG,KAAK,WAAa,IAAI,aAAaC,CAAC,EAAG,KAAK,QAAU,IAAI,aAAaC,CAAC,EAAG,KAAK,QAAU,IAAI,WAAWC,CAAC,EAAG,KAAK,WAAa,IAAI,WAAW,CAAC,EAAG,KAAK,SAAW,EAClN,CACG,CACD,MAAA,CACE,KAAK,UAAY,EAAI,EAAI,EAAI,EAAI,EAAI,CACtC,CACD,OAAO,YAAY,EAAG,CACpB,MAAMP,EAAI,EAAE,OAASwB,EAAE,UAAWvB,EAAI,IAAI,aAAa,EAAID,CAAC,EAAGE,EAAI,IAAI,aAAa,EAAIF,CAAC,EAAGG,EAAI,IAAI,aAAa,EAAIH,CAAC,EAAGI,EAAI,IAAI,WAAW,EAAIJ,CAAC,EAAGK,EAAI,IAAI,aAAa,EAAE,MAAM,EAAGC,EAAI,IAAI,WAAW,EAAE,MAAM,EAC/M,QAASC,EAAI,EAAGA,EAAIP,EAAGO,IACrBN,EAAE,EAAIM,EAAI,CAAC,EAAIF,EAAE,EAAIE,EAAI,CAAC,EAAGN,EAAE,EAAIM,EAAI,CAAC,EAAIF,EAAE,EAAIE,EAAI,CAAC,EAAGN,EAAE,EAAIM,EAAI,CAAC,EAAIF,EAAE,EAAIE,EAAI,CAAC,EAAGL,EAAE,EAAIK,EAAI,CAAC,GAAKD,EAAE,GAAKC,EAAI,GAAK,CAAC,EAAI,KAAO,IAAKL,EAAE,EAAIK,EAAI,CAAC,GAAKD,EAAE,GAAKC,EAAI,GAAK,CAAC,EAAI,KAAO,IAAKL,EAAE,EAAIK,EAAI,CAAC,GAAKD,EAAE,GAAKC,EAAI,GAAK,CAAC,EAAI,KAAO,IAAKL,EAAE,EAAIK,EAAI,CAAC,GAAKD,EAAE,GAAKC,EAAI,GAAK,CAAC,EAAI,KAAO,IAAKJ,EAAE,EAAII,EAAI,CAAC,EAAIF,EAAE,EAAIE,EAAI,EAAI,CAAC,EAAGJ,EAAE,EAAII,EAAI,CAAC,EAAIF,EAAE,EAAIE,EAAI,EAAI,CAAC,EAAGJ,EAAE,EAAII,EAAI,CAAC,EAAIF,EAAE,EAAIE,EAAI,EAAI,CAAC,EAAGH,EAAE,EAAIG,EAAI,CAAC,EAAID,EAAE,GAAKC,EAAI,GAAK,CAAC,EAAGH,EAAE,EAAIG,EAAI,CAAC,EAAID,EAAE,GAAKC,EAAI,GAAK,CAAC,EAAGH,EAAE,EAAIG,EAAI,CAAC,EAAID,EAAE,GAAKC,EAAI,GAAK,CAAC,EAAGH,EAAE,EAAIG,EAAI,CAAC,EAAID,EAAE,GAAKC,EAAI,GAAK,CAAC,EACzgB,OAAO,IAAIiB,EAAExB,EAAGC,EAAGC,EAAGC,EAAGC,CAAC,CAC3B,CACD,IAAI,aAAc,CAChB,OAAO,KAAK,YACb,CACD,IAAI,WAAY,CACd,OAAO,KAAK,UACb,CACD,IAAI,WAAY,CACd,OAAO,KAAK,UACb,CACD,IAAI,QAAS,CACX,OAAO,KAAK,OACb,CACD,IAAI,QAAS,CACX,OAAO,KAAK,OACb,CACD,IAAI,WAAY,CACd,OAAO,KAAK,UACb,CACD,OAAQ,CACN,OAAO,IAAIoB,EACT,KAAK,YACL,IAAI,aAAa,KAAK,SAAS,EAC/B,IAAI,aAAa,KAAK,SAAS,EAC/B,IAAI,aAAa,KAAK,MAAM,EAC5B,IAAI,WAAW,KAAK,MAAM,CAChC,CACG,CACH,CA8BA,MAAMC,EAAG,CACP,MAAA,CACE,KAAK,MAAQ,kBACd,CACD,OAAO,WAAW,EAAGzB,EAAG,CACtB,IAAIC,EAAI;AAAA;AAAA,EAGRA,GAAK,kBAAkBD,CAAC;AAAA,EAExB,MAAME,EAAI,CAAC,IAAK,IAAK,IAAK,KAAM,KAAM,KAAM,SAAU,SAAU,QAAQ,EACxE,QAASW,EAAI,EAAGA,EAAI,GAAIA,IACtBX,EAAE,KAAK,UAAUW,CAAC,EAAE,EACtBX,EAAE,KAAK,SAAS,EAAGA,EAAE,KAAK,SAAS,EAAGA,EAAE,KAAK,SAAS,EAAGA,EAAE,KAAK,SAAS,EAAGA,EAAE,KAAK,OAAO,EAAGA,EAAE,KAAK,OAAO,EAAGA,EAAE,KAAK,OAAO,EAAGA,EAAE,KAAK,OAAO,EAC7I,UAAWW,KAAKX,EACdD,GAAK,kBAAkBY,CAAC;AAAA,EAE1BZ,GAAK;AAAA,EAEL,MAAME,EAAI,IAAI,YAAW,EAAG,OAAOF,CAAC,EAAGG,EAAI,EAAI,EAAI,EAAI,EAAI,EAAI,EAAI,EAAI,GAAK,EAAI,EAAI,EAAI,EAAI,EAAGC,EAAIL,EAAII,EAAGE,EAAI,IAAI,SAAS,IAAI,YAAYH,EAAE,OAASE,CAAC,CAAC,EACxJ,IAAI,WAAWC,EAAE,MAAM,EAAE,IAAIH,EAAG,CAAC,EACjC,MAAMI,EAAI,IAAI,aAAa,CAAC,EAAG,EAAI,IAAI,WAAW,CAAC,EAAG,EAAIJ,EAAE,OAAQM,EAAI,EAAI,EAAI,EAAI,EAAGC,EAAID,EAAI,EAAI,EAAI,EAAI,GAAIE,EAAID,EAAI,EAAGE,EAAID,EAAI,EAAI,EACtI,QAASE,EAAI,EAAGA,EAAIb,EAAGa,IAAK,CAC1B,MAAMC,EAAIP,EAAE,EAAIM,EAAI,CAAC,EAAGE,EAAIR,EAAE,EAAIM,EAAI,CAAC,EAAGG,EAAIT,EAAE,EAAIM,EAAI,CAAC,EAAGI,GAAK,EAAE,GAAKJ,EAAI,GAAK,CAAC,EAAI,IAAM,IAAO,KAAK,MAAOK,GAAK,EAAE,GAAKL,EAAI,GAAK,CAAC,EAAI,IAAM,IAAO,KAAK,MAAOM,GAAK,EAAE,GAAKN,EAAI,GAAK,CAAC,EAAI,IAAM,IAAO,KAAK,MAAOa,EAAI,EAAE,GAAKb,EAAI,GAAK,CAAC,EAAI,IAAKc,EAAI,KAAK,IAAID,GAAK,EAAIA,EAAE,EAAGE,EAAI,KAAK,IAAIrB,EAAE,EAAIM,EAAI,EAAI,CAAC,CAAC,EAAG,EAAI,KAAK,IAAIN,EAAE,EAAIM,EAAI,EAAI,CAAC,CAAC,EAAGgB,EAAI,KAAK,IAAItB,EAAE,EAAIM,EAAI,EAAI,CAAC,CAAC,EAC3W,IAAIiB,EAAI,IAAI,GACT,EAAE,GAAKjB,EAAI,GAAK,CAAC,EAAI,KAAO,KAC5B,EAAE,GAAKA,EAAI,GAAK,CAAC,EAAI,KAAO,KAC5B,EAAE,GAAKA,EAAI,GAAK,CAAC,EAAI,KAAO,KAC5B,EAAE,GAAKA,EAAI,GAAK,CAAC,EAAI,KAAO,GACrC,EACMiB,EAAIA,EAAE,YACN,MAAMC,EAAID,EAAE,EAAGE,EAAIF,EAAE,EAAG,EAAIA,EAAE,EAAG,EAAIA,EAAE,EACvCxB,EAAE,WAAW,EAAIF,EAAIS,EAAI,EAAGC,EAAG,EAAE,EAAGR,EAAE,WAAW,EAAIF,EAAIS,EAAI,EAAGE,EAAG,EAAE,EAAGT,EAAE,WAAW,EAAIF,EAAIS,EAAI,EAAGG,EAAG,EAAE,EAAGV,EAAE,WAAW,EAAIF,EAAIS,EAAIJ,EAAI,EAAGQ,EAAG,EAAE,EAAGX,EAAE,WAAW,EAAIF,EAAIS,EAAIJ,EAAI,EAAGS,EAAG,EAAE,EAAGZ,EAAE,WAAW,EAAIF,EAAIS,EAAIJ,EAAI,EAAGU,EAAG,EAAE,EAAGb,EAAE,WAAW,EAAIF,EAAIS,EAAIH,EAAGiB,EAAG,EAAE,EAAGrB,EAAE,WAAW,EAAIF,EAAIS,EAAIF,EAAI,EAAGiB,EAAG,EAAE,EAAGtB,EAAE,WAAW,EAAIF,EAAIS,EAAIF,EAAI,EAAG,EAAG,EAAE,EAAGL,EAAE,WAAW,EAAIF,EAAIS,EAAIF,EAAI,EAAGkB,EAAG,EAAE,EAAGvB,EAAE,WAAW,EAAIF,EAAIS,EAAID,EAAI,EAAGmB,EAAG,EAAE,EAAGzB,EAAE,WAAW,EAAIF,EAAIS,EAAID,EAAI,EAAGoB,EAAG,EAAE,EAAG1B,EAAE,WAAW,EAAIF,EAAIS,EAAID,EAAI,EAAG,EAAG,EAAE,EAAGN,EAAE,WAAW,EAAIF,EAAIS,EAAID,EAAI,GAAI,EAAG,EAAE,CAC/hB,CACD,OAAON,EAAE,MACV,CACH,CACA,MAAM2B,EAAG,CACP,YAAY,EAAGjC,EAAG,CAChB,KAAK,IAAM,EAAG,KAAK,IAAMA,CAC1B,CACD,SAAS,EAAG,CACV,OAAO,EAAE,GAAK,KAAK,IAAI,GAAK,EAAE,GAAK,KAAK,IAAI,GAAK,EAAE,GAAK,KAAK,IAAI,GAAK,EAAE,GAAK,KAAK,IAAI,GAAK,EAAE,GAAK,KAAK,IAAI,GAAK,EAAE,GAAK,KAAK,IAAI,CACjI,CACD,WAAW,EAAG,CACZ,OAAO,KAAK,IAAI,GAAK,EAAE,IAAI,GAAK,KAAK,IAAI,GAAK,EAAE,IAAI,GAAK,KAAK,IAAI,GAAK,EAAE,IAAI,GAAK,KAAK,IAAI,GAAK,EAAE,IAAI,GAAK,KAAK,IAAI,GAAK,EAAE,IAAI,GAAK,KAAK,IAAI,GAAK,EAAE,IAAI,CACzJ,CACD,MAAO,CACL,OAAO,KAAK,IAAI,SAAS,KAAK,GAAG,CAClC,CACD,QAAS,CACP,OAAO,KAAK,IAAI,IAAI,KAAK,GAAG,EAAE,OAAO,CAAC,CACvC,CACD,OAAO,EAAG,CACR,KAAK,IAAM,KAAK,IAAI,IAAI,CAAC,EAAG,KAAK,IAAM,KAAK,IAAI,IAAI,CAAC,CACtD,CACD,SAAU,CACR,MAAM,EAAI,KAAK,IAAKA,EAAI,KAAK,IAC7B,KAAK,IAAM,IAAID,EAAE,KAAK,IAAI,EAAE,EAAGC,EAAE,CAAC,EAAG,KAAK,IAAI,EAAE,EAAGA,EAAE,CAAC,EAAG,KAAK,IAAI,EAAE,EAAGA,EAAE,CAAC,CAAC,EAAG,KAAK,IAAM,IAAID,EAAE,KAAK,IAAI,EAAE,EAAGC,EAAE,CAAC,EAAG,KAAK,IAAI,EAAE,EAAGA,EAAE,CAAC,EAAG,KAAK,IAAI,EAAE,EAAGA,EAAE,CAAC,CAAC,CAC1J,CACH,CACA,MAAM,UAAU,EAAG,CACjB,YAAY,EAAI,OAAQ,CACtB,MAAO,EAAE,KAAK,gBAAkB,GAAI,KAAK,sBAAwB,GAAI,KAAK,UAAY,GAAI,KAAK,iBAAmB,CAAA,EAAI,KAAK,oBAAsC,IAAI,IAAO,KAAK,MAAQ,GAAK,IAAIwB,EAAK,KAAK,QAAU,IAAIS,GACxN,IAAIlC,EAAE,IAAO,IAAO,GAAK,EACzB,IAAIA,EAAE,KAAQ,KAAQ,IAAM,CAClC,EAAO,KAAK,kBAAoB,IAAM,CAChC,KAAK,QAAU,IAAIkC,GACjB,IAAIlC,EAAE,IAAO,IAAO,GAAK,EACzB,IAAIA,EAAE,KAAQ,KAAQ,IAAM,CACpC,EACM,QAASC,EAAI,EAAGA,EAAI,KAAK,MAAM,YAAaA,IAC1C,KAAK,QAAQ,OACX,IAAID,EACF,KAAK,MAAM,UAAU,EAAIC,CAAC,EAC1B,KAAK,MAAM,UAAU,EAAIA,EAAI,CAAC,EAC9B,KAAK,MAAM,UAAU,EAAIA,EAAI,CAAC,CAC/B,CACX,CACA,EAAO,KAAK,cAAgB,IAAM,CAC5B,KAAK,KAAK,UAAU,KAAK,QAAQ,EAAG,KAAK,SAAW,IAAID,CAC9D,EAAO,KAAK,cAAgB,IAAM,CAC5B,KAAK,KAAK,OAAO,KAAK,QAAQ,EAAG,KAAK,SAAW,IAAI,CAC3D,EAAO,KAAK,WAAa,IAAM,CACzB,KAAK,KAAK,MAAM,KAAK,KAAK,EAAG,KAAK,MAAQ,IAAIA,EAAE,EAAG,EAAG,CAAC,CAC7D,EAAO,KAAK,mBACT,CACD,WAAW,EAAI,KAAMC,EAAI,QAAS,CAChC,GAAI,CAAC,SAAU,OACf,GAAI,CAAC,EAAG,CACN,MAAMK,EAAoB,IAAI,KAC9B,EAAI,SAASA,EAAE,YAAa,CAAA,IAAIA,EAAE,SAAU,EAAG,CAAC,IAAIA,EAAE,QAAS,CAAA,IAAIL,CAAC,EACrE,CACD,MAAMC,EAAI,KAAK,QACfA,EAAE,cAAa,EAAIA,EAAE,WAAU,EAAIA,EAAE,gBACrC,MAAMC,EAAID,EAAE,KAAK,UAAS,EAC1B,IAAIE,EACJ,GAAIH,IAAM,MAAO,CACf,MAAMK,EAAIoB,GAAG,WAAWvB,EAAE,OAAQD,EAAE,KAAK,WAAW,EACpDE,EAAI,IAAI,KAAK,CAACE,CAAC,EAAG,CAAE,KAAM,0BAA0B,CAAE,CACvD,MACCF,EAAI,IAAI,KAAK,CAACD,EAAE,MAAM,EAAG,CAAE,KAAM,0BAA0B,CAAE,EAC/D,MAAME,EAAI,SAAS,cAAc,GAAG,EACpCA,EAAE,SAAW,EAAGA,EAAE,KAAO,IAAI,gBAAgBD,CAAC,EAAGC,EAAE,MAAK,CACzD,CACD,IAAI,MAAO,CACT,OAAO,KAAK,KACb,CACD,IAAI,UAAW,CACb,OAAO,KAAK,SACb,CACD,IAAI,SAAS,EAAG,CACd,KAAK,YAAc,IAAM,KAAK,UAAY,EAAG,KAAK,gBAAkB,GAAI,KAAK,cAAc,KAAK,YAAY,EAC7G,CACD,IAAI,iBAAkB,CACpB,OAAO,KAAK,gBACb,CACD,IAAI,oBAAqB,CACvB,OAAO,KAAK,mBACb,CACD,IAAI,QAAS,CACX,IAAI,EAAI,KAAK,QAAQ,OAAM,EAC3B,EAAI,EAAE,IAAI,KAAK,QAAQ,EACvB,IAAIJ,EAAI,KAAK,QAAQ,KAAI,EACzB,OAAOA,EAAIA,EAAE,SAAS,KAAK,KAAK,EAAG,IAAIiC,GAAG,EAAE,SAASjC,EAAE,OAAO,CAAC,CAAC,EAAG,EAAE,IAAIA,EAAE,OAAO,CAAC,CAAC,CAAC,CACtF,CACD,OAAQ,CACN,MAAM,EAAI,IAAI,EAAE,KAAK,KAAK,MAAK,CAAE,EACjC,OAAO,EAAE,SAAW,KAAK,SAAS,MAAO,EAAE,EAAE,SAAW,KAAK,SAAS,MAAK,EAAI,EAAE,MAAQ,KAAK,MAAM,MAAO,EAAE,CAC9G,CACH,CASA,MAAM,EAAG,CACP,aAAc,CACZ,KAAK,IAAM,KAAM,KAAK,IAAM,KAAM,KAAK,MAAQ,GAAK,KAAK,KAAO,IAAK,KAAK,OAAS,IAAK,KAAK,QAAU,IAAK,KAAK,kBAAoB,IAAIQ,EAAK,KAAK,YAAc,IAAIA,EAAK,KAAK,UAAY,IAAIA,EAAK,KAAK,wBAA0B,IAAM,CACvO,KAAK,kBAAoB,IAAIA,EAC3B,EAAI,KAAK,GAAK,KAAK,MACnB,EACA,EACA,EACA,EACA,GAAK,KAAK,GAAK,KAAK,OACpB,EACA,EACA,EACA,EACA,KAAK,KAAO,KAAK,IAAM,KAAK,MAC5B,EACA,EACA,EACA,EAAE,KAAK,IAAM,KAAK,OAAS,KAAK,IAAM,KAAK,MAC3C,CACR,EAAS,KAAK,UAAY,KAAK,iBAAiB,SAAS,KAAK,UAAU,CACnE,EAAE,KAAK,OAAS,CAAC,EAAGR,IAAM,CACzB,MAAMC,EAAIsB,EAAE,uBAAuBvB,CAAC,EAAE,OAAQE,EAAI,EAAE,OACpD,KAAK,YAAc,IAAIM,EACrBP,EAAE,CAAC,EACHA,EAAE,CAAC,EACHA,EAAE,CAAC,EACH,EACAA,EAAE,CAAC,EACHA,EAAE,CAAC,EACHA,EAAE,CAAC,EACH,EACAA,EAAE,CAAC,EACHA,EAAE,CAAC,EACHA,EAAE,CAAC,EACH,EACA,CAACC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EACvC,CAACC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EACvC,CAACC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EAAIC,EAAE,CAAC,EAAID,EAAE,CAAC,EACvC,CACR,EAAS,KAAK,UAAY,KAAK,iBAAiB,SAAS,KAAK,UAAU,CACnE,EAAE,KAAK,QAAU,CAAC,EAAGD,IAAM,CAC1B,KAAK,OAAS,EAAG,KAAK,QAAUA,EAAG,KAAK,yBAC9C,CACG,CACD,IAAI,IAAK,CACP,OAAO,KAAK,GACb,CACD,IAAI,GAAG,EAAG,CACR,KAAK,MAAQ,IAAM,KAAK,IAAM,EAAG,KAAK,wBAAuB,EAC9D,CACD,IAAI,IAAK,CACP,OAAO,KAAK,GACb,CACD,IAAI,GAAG,EAAG,CACR,KAAK,MAAQ,IAAM,KAAK,IAAM,EAAG,KAAK,wBAAuB,EAC9D,CACD,IAAI,MAAO,CACT,OAAO,KAAK,KACb,CACD,IAAI,KAAK,EAAG,CACV,KAAK,QAAU,IAAM,KAAK,MAAQ,EAAG,KAAK,wBAAuB,EAClE,CACD,IAAI,KAAM,CACR,OAAO,KAAK,IACb,CACD,IAAI,IAAI,EAAG,CACT,KAAK,OAAS,IAAM,KAAK,KAAO,EAAG,KAAK,wBAAuB,EAChE,CACD,IAAI,OAAQ,CACV,OAAO,KAAK,MACb,CACD,IAAI,QAAS,CACX,OAAO,KAAK,OACb,CACD,IAAI,kBAAmB,CACrB,OAAO,KAAK,iBACb,CACD,IAAI,YAAa,CACf,OAAO,KAAK,WACb,CACD,IAAI,UAAW,CACb,OAAO,KAAK,SACb,CACH,CACA,MAAMkC,CAAE,CACN,YAAY,EAAI,EAAGlC,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAG,CACtC,KAAK,EAAI,EAAG,KAAK,EAAIF,EAAG,KAAK,EAAIC,EAAG,KAAK,EAAIC,CAC9C,CACD,OAAO,EAAG,CACR,MAAO,EAAE,KAAK,IAAM,EAAE,GAAK,KAAK,IAAM,EAAE,GAAK,KAAK,IAAM,EAAE,GAAK,KAAK,IAAM,EAAE,EAC7E,CACD,IAAI,EAAG,CACL,OAAO,OAAO,GAAK,SAAW,IAAIgC,EAAE,KAAK,EAAI,EAAG,KAAK,EAAI,EAAG,KAAK,EAAI,EAAG,KAAK,EAAI,CAAC,EAAI,IAAIA,EAAE,KAAK,EAAI,EAAE,EAAG,KAAK,EAAI,EAAE,EAAG,KAAK,EAAI,EAAE,EAAG,KAAK,EAAI,EAAE,CAAC,CACnJ,CACD,SAAS,EAAG,CACV,OAAO,OAAO,GAAK,SAAW,IAAIA,EAAE,KAAK,EAAI,EAAG,KAAK,EAAI,EAAG,KAAK,EAAI,EAAG,KAAK,EAAI,CAAC,EAAI,IAAIA,EAAE,KAAK,EAAI,EAAE,EAAG,KAAK,EAAI,EAAE,EAAG,KAAK,EAAI,EAAE,EAAG,KAAK,EAAI,EAAE,CAAC,CACnJ,CACD,SAAS,EAAG,CACV,OAAO,OAAO,GAAK,SAAW,IAAIA,EAAE,KAAK,EAAI,EAAG,KAAK,EAAI,EAAG,KAAK,EAAI,EAAG,KAAK,EAAI,CAAC,EAAI,aAAaA,EAAI,IAAIA,EAAE,KAAK,EAAI,EAAE,EAAG,KAAK,EAAI,EAAE,EAAG,KAAK,EAAI,EAAE,EAAG,KAAK,EAAI,EAAE,CAAC,EAAI,IAAIA,EACzK,KAAK,EAAI,EAAE,OAAO,CAAC,EAAI,KAAK,EAAI,EAAE,OAAO,CAAC,EAAI,KAAK,EAAI,EAAE,OAAO,CAAC,EAAI,KAAK,EAAI,EAAE,OAAO,EAAE,EACzF,KAAK,EAAI,EAAE,OAAO,CAAC,EAAI,KAAK,EAAI,EAAE,OAAO,CAAC,EAAI,KAAK,EAAI,EAAE,OAAO,CAAC,EAAI,KAAK,EAAI,EAAE,OAAO,EAAE,EACzF,KAAK,EAAI,EAAE,OAAO,CAAC,EAAI,KAAK,EAAI,EAAE,OAAO,CAAC,EAAI,KAAK,EAAI,EAAE,OAAO,EAAE,EAAI,KAAK,EAAI,EAAE,OAAO,EAAE,EAC1F,KAAK,EAAI,EAAE,OAAO,CAAC,EAAI,KAAK,EAAI,EAAE,OAAO,CAAC,EAAI,KAAK,EAAI,EAAE,OAAO,EAAE,EAAI,KAAK,EAAI,EAAE,OAAO,EAAE,CAChG,CACG,CACD,IAAI,EAAG,CACL,OAAO,KAAK,EAAI,EAAE,EAAI,KAAK,EAAI,EAAE,EAAI,KAAK,EAAI,EAAE,EAAI,KAAK,EAAI,EAAE,CAChE,CACD,KAAK,EAAGlC,EAAG,CACT,OAAO,IAAIkC,EACT,KAAK,GAAK,EAAE,EAAI,KAAK,GAAKlC,EAC1B,KAAK,GAAK,EAAE,EAAI,KAAK,GAAKA,EAC1B,KAAK,GAAK,EAAE,EAAI,KAAK,GAAKA,EAC1B,KAAK,GAAK,EAAE,EAAI,KAAK,GAAKA,CAChC,CACG,CACD,WAAY,CACV,OAAO,KAAK,KAAK,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,CAAC,CACvF,CACD,WAAW,EAAG,CACZ,OAAO,KAAK,MAAM,KAAK,EAAI,EAAE,IAAM,GAAK,KAAK,EAAI,EAAE,IAAM,GAAK,KAAK,EAAI,EAAE,IAAM,GAAK,KAAK,EAAI,EAAE,IAAM,CAAC,CACvG,CACD,WAAY,CACV,MAAM,EAAI,KAAK,YACf,OAAO,IAAIkC,EAAE,KAAK,EAAI,EAAG,KAAK,EAAI,EAAG,KAAK,EAAI,EAAG,KAAK,EAAI,CAAC,CAC5D,CACD,MAAO,CACL,MAAO,CAAC,KAAK,EAAG,KAAK,EAAG,KAAK,EAAG,KAAK,CAAC,CACvC,CACD,OAAQ,CACN,OAAO,IAAIA,EAAE,KAAK,EAAG,KAAK,EAAG,KAAK,EAAG,KAAK,CAAC,CAC5C,CACD,UAAW,CACT,MAAO,IAAI,KAAK,KAAI,EAAG,KAAK,IAAI,CAAC,GAClC,CACH,CACA,MAAMC,WAAW,EAAG,CAClB,YAAY,EAAI,OAAQ,CACtB,MAAK,EAAI,KAAK,MAAQ,GAAK,IAAI,GAAM,KAAK,UAAY,IAAIpC,EAAE,EAAG,EAAG,EAAE,EAAG,KAAK,OAAS,IAAM,CACzF,KAAK,KAAK,OAAO,KAAK,SAAU,KAAK,QAAQ,CAC9C,EAAE,KAAK,iBAAmB,CAACC,EAAGC,IAAM,CACnC,MAAMC,EAAI,IAAIgC,EAAElC,EAAGC,EAAG,GAAI,CAAC,EAAGE,EAAI,KAAK,MAAM,iBAAiB,OAAQ,EAAEC,EAAIF,EAAE,SAASC,CAAC,EAAGE,EAAI,KAAK,MAAM,WAAW,OAAQ,EAAEC,EAAIF,EAAE,SAASC,CAAC,EAC/I,OAAO,IAAIN,EACTO,EAAE,EAAIA,EAAE,EACRA,EAAE,EAAIA,EAAE,EACRA,EAAE,EAAIA,EAAE,CACT,EAAC,SAAS,KAAK,QAAQ,EAAE,UAAS,CACzC,CACG,CACD,IAAI,MAAO,CACT,OAAO,KAAK,KACb,CACH,CACA,MAAM8B,WAAW,EAAG,CAClB,aAAc,CACZ,MAAK,EAAI,KAAK,SAAW,CAAE,EAAE,KAAK,UAAa,GAAM,CACnD,KAAK,QAAQ,KAAK,CAAC,EAAG,KAAK,cAAc,IAAIhB,GAAG,CAAC,CAAC,CACxD,EAAO,KAAK,aAAgB,GAAM,CAC5B,MAAMpB,EAAI,KAAK,QAAQ,QAAQ,CAAC,EAChC,GAAIA,EAAI,EACN,MAAM,IAAI,MAAM,2BAA2B,EAC7C,KAAK,QAAQ,OAAOA,EAAG,CAAC,EAAG,KAAK,cAAc,IAAIqB,GAAG,CAAC,CAAC,CAC7D,EAAO,KAAK,WAAc,GAAM,CAC1B,UAAWrB,KAAK,KAAK,QACnB,GAAI,EAAEA,CAAC,EACL,OAAOA,CACjB,EAAO,KAAK,iBAAoB,GAAM,CAChC,UAAWA,KAAK,KAAK,QACnB,GAAIA,aAAa,EACf,OAAOA,CACjB,EAAO,KAAK,MAAQ,IAAM,CACpB,MAAM,EAAI,KAAK,QAAQ,MAAK,EAC5B,UAAWA,KAAK,EACd,KAAK,aAAaA,CAAC,CAC3B,EAAO,KAAK,OACT,CACD,yBAAyB,EAAI,QAAS,CACpC,MAAMA,EAAI,CAAA,EACV,IAAIC,EAAI,EACR,UAAWG,KAAK,KAAK,QACnB,GAAIA,aAAa,EAAG,CAClB,MAAMC,EAAID,EAAE,QACZC,EAAE,cAAa,EAAIA,EAAE,WAAU,EAAIA,EAAE,gBACrC,MAAMC,EAAID,EAAE,KAAK,UAAS,EAC1BL,EAAE,KAAKM,CAAC,EAAGL,GAAKI,EAAE,KAAK,WACxB,CACH,MAAMH,EAAI,IAAI,WAAWD,EAAIuB,EAAE,SAAS,EACxC,IAAIrB,EAAI,EACR,UAAWC,KAAKJ,EACdE,EAAE,IAAIE,EAAGD,CAAC,EAAGA,GAAKC,EAAE,OACtB,OAAO,IAAM,MAAQqB,GAAG,WAAWvB,EAAE,OAAQD,CAAC,EAAIC,EAAE,MACrD,CACD,WAAW,EAAI,KAAMF,EAAI,QAAS,CAChC,GAAI,CAAC,SAAU,OACf,GAAI,CAAC,EAAG,CACN,MAAMI,EAAoB,IAAI,KAC9B,EAAI,SAASA,EAAE,YAAa,CAAA,IAAIA,EAAE,SAAU,EAAG,CAAC,IAAIA,EAAE,QAAS,CAAA,IAAIJ,CAAC,EACrE,CACD,MAAMC,EAAI,KAAK,yBAAyBD,CAAC,EAAGE,EAAI,IAAI,KAAK,CAACD,CAAC,EAAG,CAAE,KAAM,2BAA4B,EAAGE,EAAI,SAAS,cAAc,GAAG,EACnIA,EAAE,SAAW,EAAGA,EAAE,KAAO,IAAI,gBAAgBD,CAAC,EAAGC,EAAE,MAAK,CACzD,CACD,IAAI,SAAU,CACZ,OAAO,KAAK,QACb,CACH,CACA,eAAekC,GAAGC,EAAG,EAAG,CACtB,MAAMtC,EAAI,MAAM,MAAMsC,EAAG,CACvB,KAAM,OACN,YAAa,OACb,MAAO,EAAI,cAAgB,SAC/B,CAAG,EACD,GAAItC,EAAE,QAAU,IACd,MAAM,IAAI,MAAMA,EAAE,OAAS,mBAAqBA,EAAE,GAAG,EACvD,OAAOA,CACT,CACA,eAAeuC,GAAGD,EAAG,EAAG,CACtB,MAAMtC,EAAIsC,EAAE,KAAK,UAAW,EAAErC,EAAIqC,EAAE,QAAQ,IAAI,gBAAgB,EAAGpC,EAAID,GAAK,CAAC,MAAM,SAASA,CAAC,CAAC,EAAI,SAASA,CAAC,EAAI,OAAQE,EAAI,CAAA,EAC5H,IAAIC,EAAI,EACR,OAAW,CACT,KAAM,CAAE,KAAMG,EAAG,MAAO,CAAC,EAAK,MAAMP,EAAE,OACtC,GAAIO,EAAG,MACP,GAAIJ,EAAE,KAAK,CAAC,EAAGC,GAAK,EAAE,OAAQ,GAAKF,EAAG,CACpC,MAAM,EAAIE,EAAIF,EAAGO,EAAI,KAAK,IAAI,EAAI,IAAM,GAAI,EAC5C,EAAEA,CAAC,CACJ,CACF,CACD,MAAMJ,EAAI,IAAI,WAAWD,CAAC,EAC1B,IAAIE,EAAI,EACR,UAAWC,KAAKJ,EACdE,EAAE,IAAIE,EAAGD,CAAC,EAAGA,GAAKC,EAAE,OACtB,OAAO,GAAK,EAAE,CAAC,EAAGF,CACpB,CACA,MAAMmC,EAAG,CACP,aAAa,UAAU,EAAGxC,EAAGC,EAAGC,EAAI,GAAI,CACtC,MAAMC,EAAI,MAAMkC,GAAG,EAAGnC,CAAC,EAAGE,EAAI,MAAMmC,GAAGpC,EAAGF,CAAC,EAC3C,OAAO,KAAK,oBAAoBG,EAAE,OAAQJ,CAAC,CAC5C,CACD,aAAa,kBAAkB,EAAGA,EAAGC,EAAG,CACtC,MAAMC,EAAI,IAAI,WACd,IAAIC,EAAI,IAAI,EACZ,OAAOD,EAAE,OAAUE,GAAM,CACvBD,EAAI,KAAK,oBAAoBC,EAAE,OAAO,OAAQJ,CAAC,CACrD,EAAOE,EAAE,WAAcE,GAAM,CACvBH,IAAIG,EAAE,OAASA,EAAE,KAAK,CAC5B,EAAOF,EAAE,kBAAkB,CAAC,EAAG,MAAM,IAAI,QAASE,GAAM,CAClDF,EAAE,UAAY,IAAM,CAClBE,GACR,CACK,CAAA,EAAGD,CACL,CACD,OAAO,oBAAoB,EAAGH,EAAG,CAC/B,MAAMC,EAAI,IAAI,WAAW,CAAC,EAAGC,EAAIsB,EAAE,YAAYvB,CAAC,EAAGE,EAAI,IAAI,EAAED,CAAC,EAC9D,OAAOF,EAAE,UAAUG,CAAC,EAAGA,CACxB,CACH,CACA,MAAMsC,EAAG,CACP,aAAa,UAAU,EAAGzC,EAAGC,EAAGC,EAAI,GAAIC,EAAI,GAAI,CAC9C,MAAMC,EAAI,MAAMiC,GAAG,EAAGlC,CAAC,EAAGE,EAAI,MAAMkC,GAAGnC,EAAGH,CAAC,EAC3C,GAAII,EAAE,CAAC,IAAM,KAAOA,EAAE,CAAC,IAAM,KAAOA,EAAE,CAAC,IAAM,KAAOA,EAAE,CAAC,IAAM,GAC3D,MAAM,IAAI,MAAM,kBAAkB,EACpC,OAAO,KAAK,oBAAoBA,EAAE,OAAQL,EAAGE,CAAC,CAC/C,CACD,aAAa,kBAAkB,EAAGF,EAAGC,EAAGC,EAAI,GAAI,CAC9C,MAAMC,EAAI,IAAI,WACd,IAAIC,EAAI,IAAI,EACZ,OAAOD,EAAE,OAAUE,GAAM,CACvBD,EAAI,KAAK,oBAAoBC,EAAE,OAAO,OAAQL,EAAGE,CAAC,CACxD,EAAOC,EAAE,WAAcE,GAAM,CACvBJ,IAAII,EAAE,OAASA,EAAE,KAAK,CAC5B,EAAOF,EAAE,kBAAkB,CAAC,EAAG,MAAM,IAAI,QAASE,GAAM,CAClDF,EAAE,UAAY,IAAM,CAClBE,GACR,CACK,CAAA,EAAGD,CACL,CACD,OAAO,oBAAoB,EAAGJ,EAAGC,EAAI,GAAI,CACvC,MAAMC,EAAI,IAAI,WAAW,KAAK,gBAAgB,EAAGD,CAAC,CAAC,EAAGE,EAAIqB,EAAE,YAAYtB,CAAC,EAAGE,EAAI,IAAI,EAAED,CAAC,EACvF,OAAOH,EAAE,UAAUI,CAAC,EAAGA,CACxB,CACD,OAAO,gBAAgB,EAAGJ,EAAG,CAC3B,MAAMC,EAAI,IAAI,WAAW,CAAC,EAAGC,EAAI,IAAI,YAAa,EAAC,OAAOD,EAAE,MAAM,EAAG,KAAO,EAAE,CAAC,EAAGE,EAAI;AAAA,EACvFC,EAAIF,EAAE,QAAQC,CAAC,EACd,GAAIC,EAAI,EAAG,MAAM,IAAI,MAAM,iCAAiC,EAC5D,MAAMC,EAAI,SAAS,yBAAyB,KAAKH,CAAC,EAAE,CAAC,CAAC,EACtD,IAAII,EAAI,EACR,MAAMC,EAAI,CACR,OAAQ,EACR,IAAK,EACL,KAAM,EACN,MAAO,EACP,MAAO,EACP,OAAQ,EACR,MAAO,CACb,EAAO,EAAI,CAAA,EACP,UAAWI,KAAKT,EAAE,MAAM,EAAGE,CAAC,EAAE,MAAM;AAAA,CACvC,EAAE,OAAQQ,GAAMA,EAAE,WAAW,WAAW,CAAC,EAAG,CACvC,KAAM,CAACA,EAAGC,EAAGC,CAAC,EAAIH,EAAE,MAAM,GAAG,EAC7B,GAAI,EAAE,KAAK,CAAE,KAAMG,EAAG,KAAMD,EAAG,OAAQP,CAAC,CAAE,EAAG,CAACC,EAAEM,CAAC,EAAG,MAAM,IAAI,MAAM,8BAA8BA,CAAC,EAAE,EACrGP,GAAKC,EAAEM,CAAC,CACT,CACD,MAAM,EAAI,IAAI,SAAS,EAAGT,EAAID,EAAE,MAAM,EAAGM,EAAI,IAAI,YAAYe,EAAE,UAAYnB,CAAC,EAAGK,EAAI,EAAE,UAAU,IAAIX,EAAE,KAAK,GAAK,EAAG,EAAG,CAAC,CAAC,EACvH,QAASY,EAAI,EAAGA,EAAIN,EAAGM,IAAK,CAC1B,MAAMC,EAAI,IAAI,aAAaH,EAAGE,EAAIa,EAAE,UAAW,CAAC,EAAGX,EAAI,IAAI,aAAaJ,EAAGE,EAAIa,EAAE,UAAY,GAAI,CAAC,EAAGV,EAAI,IAAI,kBAAkBL,EAAGE,EAAIa,EAAE,UAAY,GAAI,CAAC,EAAGT,EAAI,IAAI,kBAAkBN,EAAGE,EAAIa,EAAE,UAAY,GAAI,CAAC,EAChN,IAAIR,EAAI,IAAKC,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAC/B,EAAE,QAASQ,GAAM,CACf,IAAIC,EACJ,OAAQD,EAAE,KAAI,CACZ,IAAK,QACHC,EAAI,EAAE,WAAWD,EAAE,OAAShB,EAAIL,EAAG,EAAE,EACrC,MACF,IAAK,MACHsB,EAAI,EAAE,SAASD,EAAE,OAAShB,EAAIL,EAAG,EAAE,EACnC,MACF,QACE,MAAM,IAAI,MAAM,8BAA8BqB,EAAE,IAAI,EAAE,CACzD,CACD,OAAQA,EAAE,KAAI,CACZ,IAAK,IACHf,EAAE,CAAC,EAAIgB,EACP,MACF,IAAK,IACHhB,EAAE,CAAC,EAAIgB,EACP,MACF,IAAK,IACHhB,EAAE,CAAC,EAAIgB,EACP,MACF,IAAK,UACL,IAAK,YACHf,EAAE,CAAC,EAAI,KAAK,IAAIe,CAAC,EACjB,MACF,IAAK,UACL,IAAK,YACHf,EAAE,CAAC,EAAI,KAAK,IAAIe,CAAC,EACjB,MACF,IAAK,UACL,IAAK,YACHf,EAAE,CAAC,EAAI,KAAK,IAAIe,CAAC,EACjB,MACF,IAAK,MACHd,EAAE,CAAC,EAAIc,EACP,MACF,IAAK,QACHd,EAAE,CAAC,EAAIc,EACP,MACF,IAAK,OACHd,EAAE,CAAC,EAAIc,EACP,MACF,IAAK,SACL,IAAK,aACHd,EAAE,CAAC,GAAK,GAAMW,GAAG,MAAQG,GAAK,IAC9B,MACF,IAAK,SACL,IAAK,aACHd,EAAE,CAAC,GAAK,GAAMW,GAAG,MAAQG,GAAK,IAC9B,MACF,IAAK,SACL,IAAK,aACHd,EAAE,CAAC,GAAK,GAAMW,GAAG,MAAQG,GAAK,IAC9B,MACF,IAAK,SACHd,EAAE,CAAC,GAAK,GAAMW,GAAG,MAAQG,GAAK,IAC9B,MACF,IAAK,UACL,IAAK,YACHd,EAAE,CAAC,EAAI,GAAK,EAAI,KAAK,IAAI,CAACc,CAAC,GAAK,IAChC,MACF,IAAK,QACL,IAAK,aACHZ,EAAIY,EACJ,MACF,IAAK,QACL,IAAK,aACHX,EAAIW,EACJ,MACF,IAAK,QACL,IAAK,aACHV,EAAIU,EACJ,MACF,IAAK,QACL,IAAK,aACHT,EAAIS,EACJ,KACH,CACT,CAAO,EACD,IAAIF,EAAI,IAAI,EAAET,EAAGC,EAAGC,EAAGH,CAAC,EACxB,OAAQhB,EAAC,CACP,IAAK,UAAW,CACd,MAAM2B,EAAIf,EAAE,CAAC,EACbA,EAAE,CAAC,EAAI,CAACA,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAIe,EAAGD,EAAIhB,EAAE,SAASgB,CAAC,EACxC,KACD,CACD,IAAK,GACH,MACF,QACE,MAAM,IAAI,MAAM,uBAAuB1B,CAAC,EAAE,CAC7C,CACD0B,EAAIA,EAAE,UAAW,EAAEX,EAAE,CAAC,EAAIW,EAAE,EAAI,IAAM,IAAKX,EAAE,CAAC,EAAIW,EAAE,EAAI,IAAM,IAAKX,EAAE,CAAC,EAAIW,EAAE,EAAI,IAAM,IAAKX,EAAE,CAAC,EAAIW,EAAE,EAAI,IAAM,GAC/G,CACD,OAAOjB,CACR,CACH,CA4EA,MAAMiC,GAAK,u9qBAAw9qBC,GAAML,GAAM,WAAW,KAAK,KAAKA,CAAC,EAAI,GAAM,EAAE,WAAW,CAAC,CAAC,EAAGM,GAAK,OAAO,KAAO,KAAO,KAAK,MAAQ,IAAI,KAAK,CAAC,wCAAyCD,GAAGD,EAAE,CAAC,EAAG,CAAE,KAAM,+BAAiC,CAAA,EAC7qrB,SAASG,GAAGP,EAAG,CACb,IAAI,EACJ,GAAI,CACF,GAAI,EAAIM,KAAO,KAAK,KAAO,KAAK,WAAW,gBAAgBA,EAAE,EAAG,CAAC,EAAG,KAAM,GAC1E,MAAM5C,EAAI,IAAI,OAAO,EAAG,CACtB,KAAM,SACN,KAAMsC,GAAG,IACf,CAAK,EACD,OAAOtC,EAAE,iBAAiB,QAAS,IAAM,EACtC,KAAK,KAAO,KAAK,WAAW,gBAAgB,CAAC,CAC/C,CAAA,EAAGA,CACR,MAAU,CACN,OAAO,IAAI,OACT,+BAAiC0C,GACjC,CACE,KAAM,SACN,KAAMJ,GAAG,IACV,CACP,CACG,CACH,CACA,MAAMQ,EAAG,CACP,YAAY,EAAG9C,EAAG,CAChB,KAAK,OAAS,KAAM,KAAK,QAAU,KAAM,KAAK,SAAW,GAAI,KAAK,aAAe,GAAI,KAAK,UAAY,EACtG,MAAMC,EAAI,EAAE,GACZ,KAAK,SAAWA,EAAE,cAAa,EAAI,KAAK,QAAUD,GAAK,GACvD,MAAME,EAAID,EAAE,aAAaA,EAAE,aAAa,EACxCA,EAAE,aAAaC,EAAG,KAAK,iBAAkB,CAAA,EAAGD,EAAE,cAAcC,CAAC,EAAGD,EAAE,mBAAmBC,EAAGD,EAAE,cAAc,GAAK,QAAQ,MAAMA,EAAE,iBAAiBC,CAAC,CAAC,EAChJ,MAAMC,EAAIF,EAAE,aAAaA,EAAE,eAAe,EAC1CA,EAAE,aAAaE,EAAG,KAAK,mBAAoB,CAAA,EAAGF,EAAE,cAAcE,CAAC,EAAGF,EAAE,mBAAmBE,EAAGF,EAAE,cAAc,GAAK,QAAQ,MAAMA,EAAE,iBAAiBE,CAAC,CAAC,EAAGF,EAAE,aAAa,KAAK,QAASC,CAAC,EAAGD,EAAE,aAAa,KAAK,QAASE,CAAC,EAAGF,EAAE,YAAY,KAAK,OAAO,EAAGA,EAAE,oBAAoB,KAAK,QAASA,EAAE,WAAW,GAAK,QAAQ,MAAMA,EAAE,kBAAkB,KAAK,OAAO,CAAC,EAAG,KAAK,OAAS,IAAM,CAC9WA,EAAE,WAAW,KAAK,QAAQ,EAAG,KAAK,SACxC,EAAO,KAAK,WAAa,IAAM,CACzB,QAAQ,OAAO,CAAC,KAAK,aAAc,mCAAmC,EAAGA,EAAE,WAAW,KAAK,QAAQ,EAAG,KAAK,YAAW,EACtH,UAAWG,KAAK,KAAK,OACnBA,EAAE,WAAW,IAAI,EACnB,KAAK,aAAe,GAAI,KAAK,SAAW,EACzC,EAAE,KAAK,OAAS,CAACA,EAAGC,IAAM,CACzBJ,EAAE,WAAW,KAAK,QAAQ,GAAI,KAAK,SAAWG,GAAK,KAAK,UAAYC,KAAO,KAAK,UAAW,KAAK,OAASD,EAAG,KAAK,QAAUC,EAAG,KAAK,WAAU,GAC7I,UAAWC,KAAK,KAAK,OACnBA,EAAE,OAAM,EACV,KAAK,QAAO,CAClB,EAAO,KAAK,QAAU,IAAM,CACtB,GAAI,KAAK,aAAc,CACrBL,EAAE,WAAW,KAAK,QAAQ,EAC1B,UAAWG,KAAK,KAAK,OACnBA,EAAE,QAAO,EACX,KAAK,SAAQ,EAAI,KAAK,OAAS,KAAM,KAAK,QAAU,KAAM,KAAK,aAAe,EAC/E,CACP,CACG,CACD,IAAI,UAAW,CACb,OAAO,KAAK,SACb,CACD,IAAI,OAAQ,CACV,OAAO,KAAK,MACb,CACD,IAAI,QAAS,CACX,OAAO,KAAK,OACb,CACD,IAAI,SAAU,CACZ,OAAO,KAAK,QACb,CACD,IAAI,QAAS,CACX,OAAO,KAAK,OACb,CACD,IAAI,SAAU,CACZ,OAAO,KAAK,QACb,CACH,CACA,MAAM,GAAK,26tBAA46tB,GAAMkC,GAAM,WAAW,KAAK,KAAKA,CAAC,EAAI,GAAM,EAAE,WAAW,CAAC,CAAC,EAAGS,GAAK,OAAO,KAAO,KAAO,KAAK,MAAQ,IAAI,KAAK,CAAC,wCAAyC,GAAG,EAAE,CAAC,EAAG,CAAE,KAAM,+BAAiC,CAAA,EACjouB,SAASC,GAAGV,EAAG,CACb,IAAI,EACJ,GAAI,CACF,GAAI,EAAIS,KAAO,KAAK,KAAO,KAAK,WAAW,gBAAgBA,EAAE,EAAG,CAAC,EAAG,KAAM,GAC1E,MAAM/C,EAAI,IAAI,OAAO,EAAG,CACtB,KAAM,SACN,KAAMsC,GAAG,IACf,CAAK,EACD,OAAOtC,EAAE,iBAAiB,QAAS,IAAM,EACtC,KAAK,KAAO,KAAK,WAAW,gBAAgB,CAAC,CAC/C,CAAA,EAAGA,CACR,MAAU,CACN,OAAO,IAAI,OACT,+BAAiC,GACjC,CACE,KAAM,SACN,KAAMsC,GAAG,IACV,CACP,CACG,CACH,CACA,IAAIW,GAAK,SAASX,EAAI,GAAI,CACxB,IAAI,EAAGtC,EAAIsC,EAAGrC,EAAI,YAAY,IAAKC,EAAI,GAAIC,EAC3C,CACE,GAAI,CACFD,EAAI,IAAI,IAAI,IAAKD,CAAC,EAAE,IAC1B,MAAY,CACP,CACDE,EAAK+C,GAAM,CACT,IAAIC,EAAI,IAAI,eACZ,OAAOA,EAAE,KAAK,MAAOD,EAAG,EAAE,EAAGC,EAAE,aAAe,cAAeA,EAAE,KAAK,IAAI,EAAG,IAAI,WAAWA,EAAE,QAAQ,CAC1G,CACG,CACD,QAAQ,IAAI,KAAK,OAAO,EAAG,QAAQ,MAAM,KAAK,OAAO,EACrD,IAAI/C,EAAGC,EAAGC,EACV,SAASC,GAAI,CACX,IAAI2C,EAAI7C,EAAE,OACVL,EAAE,OAASM,EAAI,IAAI,WAAW4C,CAAC,EAAGlD,EAAE,QAAU,IAAI,YAAYkD,CAAC,EAAGlD,EAAE,QAAU,IAAI,aAAakD,CAAC,EAAG,IAAI,cAAcA,CAAC,EAAG,IAAI,eAAeA,CAAC,CAC9I,CACD,SAAS,GAAI,CACX,GAAIlD,EAAE,OACJ,IAAK,OAAOA,EAAE,QAAU,aAAeA,EAAE,OAAS,CAACA,EAAE,MAAM,GAAIA,EAAE,OAAO,QACtE6B,EAAE7B,EAAE,OAAO,MAAO,CAAA,EACtB0B,EAAE,CAAC,CACJ,CACD,SAAS,GAAI,CACX0B,EAAE,EAAC,CACJ,CACD,SAAS3C,GAAI,CACX,GAAIT,EAAE,QACJ,IAAK,OAAOA,EAAE,SAAW,aAAeA,EAAE,QAAU,CAACA,EAAE,OAAO,GAAIA,EAAE,QAAQ,QAC1E4B,EAAE5B,EAAE,QAAQ,MAAO,CAAA,EACvB0B,EAAEC,CAAC,CACJ,CACD,IAAIjB,EAAI,EAAGC,EAAI,KACf,SAASC,EAAEsC,EAAG,CACZxC,IAAKV,EAAE,yBAAyBU,CAAC,CAClC,CACD,SAASG,EAAEqC,EAAG,CACZ,GAAIxC,IAAKV,EAAE,yBAAyBU,CAAC,EAAGA,GAAK,GAAKC,EAAG,CACnD,IAAIwC,EAAIxC,EACRA,EAAI,KAAMwC,GACX,CACF,CACD,IAAIrC,EACJ,SAASC,GAAI,CACX,OAAOe,EAAE,84VAA84V,CACx5V,CACD,SAASd,EAAEkC,EAAG,CACZ,GAAI,YAAY,OAAOA,CAAC,EACtB,OAAOA,EACT,GAAIA,GAAKpC,GAAKV,EACZ,OAAO,IAAI,WAAWA,CAAC,EACzB,GAAID,EACF,OAAOA,EAAE+C,CAAC,EACZ,KAAM,0JACP,CACD,SAASjC,EAAEiC,EAAGC,EAAG,CACf,IAAI,EAAGE,EAAIrC,EAAEkC,CAAC,EACd,EAAI,IAAI,YAAY,OAAOG,CAAC,EAC5B,IAAIC,EAAI,IAAI,YAAY,SAAS,EAAGH,CAAC,EACrC,MAAO,CAACG,EAAG,CAAC,CACb,CACD,SAASpC,GAAI,CACX,MAAO,CAAE,EAAG,EACb,CACD,SAASC,GAAI,CACX,SAAS+B,EAAEG,EAAGC,EAAG,CACf,OAAOF,EAAIC,EAAE,QAAShD,EAAI+C,EAAE,EAAG7C,EAAC,EAAIgD,EAAEH,CAAC,EAAGvC,EAAC,EAAIuC,CAChD,CACDxC,IACA,IAAIuC,EAAIjC,IACR,GAAIlB,EAAE,gBACJ,OAAO,IAAI,QAAQ,CAACqD,EAAGC,IAAM,CAC3BtD,EAAE,gBAAgBmD,EAAG,CAAC,EAAG,IAAM,CAC7BE,EAAEH,EAAE,CAAC,CAAC,CAChB,CAAS,CACT,CAAO,EACHpC,IAAMC,EAAC,EACP,IAAI,EAAIE,EAAEH,EAAGqC,CAAC,EACd,OAAOD,EAAE,EAAE,CAAC,CAAC,CACd,CACD,QAASxB,EAAKwB,GAAM,CAClB,KAAOA,EAAE,OAAS,GAChBA,EAAE,QAAQlD,CAAC,CACjB,EAAK2B,EAAI,CAAE,EAAEC,EAAKsB,GAAMvB,EAAE,KAAKuB,CAAC,EAAG,EAAI,CAAA,EAAIrB,EAAKqB,GAAM,EAAE,KAAKA,CAAC,EAAGpB,EAAKoB,GAAM,CACxE,QAASC,EAAG,EAAGE,EAAI,EAAGC,EAAI,EAAG,EAAIJ,EAAE,OAAQ,EAAI,IAAI,YAAY,EAAI,GAAK,IAAMA,EAAE,EAAI,CAAC,GAAK,MAAQA,EAAE,EAAI,CAAC,GAAK,IAAI,EAAGG,EAAI,EAAGA,GAAK,EAAGC,GAAK,EACvIH,EAAIK,EAAEN,EAAE,WAAWG,EAAI,CAAC,CAAC,EAAG,EAAIG,EAAEN,EAAE,WAAWG,EAAI,CAAC,CAAC,EAAG,EAAEC,CAAC,EAAIE,EAAEN,EAAE,WAAWG,CAAC,CAAC,GAAK,EAAIF,GAAK,EAAG,EAAEG,EAAI,CAAC,EAAIH,GAAK,EAAI,GAAK,EAAG,EAAEG,EAAI,CAAC,EAAI,GAAK,EAAIE,EAAEN,EAAE,WAAWG,EAAI,CAAC,CAAC,EACxK,OAAO,CACX,EAAKtB,EAAI,IAAM,WAAYC,EAAI,CAACkB,EAAGC,IAAM,KAAK,KAAKD,EAAIC,CAAC,EAAIA,EAAG,EAAKD,GAAM,CACtE,IAAIC,EAAI9C,EAAE,OAAQ,GAAK6C,EAAIC,EAAE,WAAa,OAAS,MAAQ,EAC3D,GAAI,CACF,OAAO9C,EAAE,KAAK,CAAC,EAAGE,EAAC,EAAI,CAC7B,MAAY,CACP,CACL,EAAK,EAAK2C,GAAM,CACZ,IAAIC,EAAI7C,EAAE,OACV4C,KAAO,EACP,IAAI,EAAInB,IACR,GAAImB,EAAI,EACN,MAAO,GACT,QAASG,EAAI,EAAGA,GAAK,EAAGA,GAAK,EAAG,CAC9B,IAAIC,EAAIH,GAAK,EAAI,GAAME,GACvBC,EAAI,KAAK,IAAIA,EAAGJ,EAAI,SAAS,EAC7B,IAAI,EAAI,KAAK,IAAI,EAAGlB,EAAE,KAAK,IAAIkB,EAAGI,CAAC,EAAG,KAAK,CAAC,EAAG,EAAI,EAAE,CAAC,EACtD,GAAI,EACF,MAAO,EACV,CACD,MAAO,EACX,EAAKE,EAAI,IAAI,WAAW,GAAG,EAAG,EAAI,GAAI,GAAK,EAAG,EAAE,EAC5CA,EAAE,GAAK,CAAC,EAAI,GAAK,EAAGA,EAAE,GAAK,CAAC,EAAI,EAAGA,EAAE,GAAK,CAAC,EAAI,GAAK,EACtDA,EAAE,EAAE,EAAI,GAAIA,EAAE,EAAE,EAAI,GAAIxD,EAAE,eAAiBA,EAAE,cAAeA,EAAE,OAASA,EAAE,MAAOA,EAAE,UAAYA,EAAE,SAAUA,EAAE,aAAeI,EAAIJ,EAAE,YAAaA,EAAE,WAAaA,EAAE,UAAWA,EAAE,aAAeA,EAAE,YAC7L,SAASuD,EAAEL,EAAG,CACZlD,EAAE,MAAQkD,EAAE,EAAGlD,EAAE,QAAUkD,EAAE,EAAGlD,EAAE,MAAQkD,EAAE,CAC7C,CACD,IAAI,EAAI,CAAE,EAAG,CAAC,EAAIE,EAAIjC,IACtB,SAAS,GAAI,CACX,GAAIT,EAAI,EAAG,CACTC,EAAI,EACJ,MACD,CACD,GAAI,EAAC,EAAID,EAAI,EAAG,CACdC,EAAI,EACJ,MACD,CACD,SAASuC,GAAI,CACXlD,EAAE,UAAY,GAAI,EAAG,EAAEA,EAAE,yBAA0BS,GACpD,CACDT,EAAE,WAAaA,EAAE,UAAU,YAAY,EAAG,WAAW,IAAM,CACzD,WAAW,IAAMA,EAAE,UAAU,EAAE,EAAG,CAAC,EAAGkD,GAC5C,EAAO,CAAC,GAAKA,GACV,CACD,SAASO,GAAI,CACX,GAAIzD,EAAE,QACJ,IAAK,OAAOA,EAAE,SAAW,aAAeA,EAAE,QAAU,CAACA,EAAE,OAAO,GAAIA,EAAE,QAAQ,OAAS,GACnFA,EAAE,QAAQ,MAAK,GACpB,CACD,OAAOyD,EAAG,EAAE,EAAC,EAAI,EAAIzD,EAAG,CAC1B,EACA,MAAM0D,GAAK,IAAM,IAAIV,GACrB,MAAMW,EAAG,CACP,YAAY,EAAG,CACb,KAAK,YAAc,GAAI,KAAK,kBAAoB,GAAI,KAAK,uBAAyB,GAAI,KAAK,UAA4B,IAAI,IAAO,KAAK,OAAyB,IAAI,IACpK,IAAI3D,EAAI,EAAGC,EAAI,EACf,KAAK,cAAgC,IAAI,IAAO,KAAK,SAA2B,IAAI,IACpF,MAAMC,EAAoB,IAAI,IAC9B,UAAWO,KAAK,EAAE,QAChBA,aAAa,IAAM,KAAK,cAAc,IAAIA,EAAGR,CAAC,EAAG,KAAK,SAAS,IAAIQ,EAAGT,CAAC,EAAGE,EAAE,IAAIF,EAAGS,CAAC,EAAGT,GAAKS,EAAE,KAAK,YAAaR,KAClH,KAAK,aAAeD,EAAG,KAAK,OAAS,KAAM,KAAK,QAAU,KAAK,KAAK,EAAI,KAAK,YAAc,KAAK,KAAK,EAAG,KAAK,MAAQ,IAAI,YAAY,KAAK,MAAQ,KAAK,OAAS,CAAC,EAAG,KAAK,iBAAmB,EAAG,KAAK,kBAAoBE,EAAE,KAAM,KAAK,YAAc,IAAI,aAAa,KAAK,iBAAmB,KAAK,kBAAoB,CAAC,EAAG,KAAK,uBAAyB,KAAM,KAAK,wBAA0B,KAAK,KAAK,KAAK,YAAc,KAAK,sBAAsB,EAAG,KAAK,kBAAoB,IAAI,YAAY,KAAK,uBAAyB,KAAK,uBAAuB,EAAG,KAAK,sBAAwB,EAAG,KAAK,uBAAyB,GAAI,KAAK,iBAAmB,IAAI,aAAa,KAAK,sBAAwB,KAAK,uBAAyB,CAAC,EAAG,KAAK,iBAAiB,KAAK,CAAC,EAAG,KAAK,iBAAiB,CAAC,EAAI,EAAG,KAAK,iBAAiB,CAAC,EAAI,EAAG,KAAK,iBAAiB,EAAE,EAAI,EAAG,KAAK,iBAAiB,EAAE,EAAI,EAAG,KAAK,4BAA8B,KAAM,KAAK,6BAA+B,KAAK,KAAK,KAAK,YAAc,KAAK,2BAA2B,EAAG,KAAK,uBAAyB,IAAI,YAC7gC,KAAK,4BAA8B,KAAK,4BACzC,EAAE,KAAK,sBAAsB,KAAK,CAAC,EAAG,KAAK,WAAa,IAAI,aAAa,KAAK,YAAc,CAAC,EAAG,KAAK,WAAa,IAAI,aAAa,KAAK,YAAc,CAAC,EAAG,KAAK,QAAU,IAAI,aAAa,KAAK,YAAc,CAAC,EAAG,KAAK,QAAUwD,GAAE,EACnO,MAAMvD,EAAKM,GAAM,CACf,MAAMC,EAAI,KAAK,cAAc,IAAID,CAAC,EAClC,KAAK,YAAY,IAAIA,EAAE,UAAU,OAAQC,EAAI,EAAE,EAAG,KAAK,YAAYA,EAAI,GAAK,EAAE,EAAID,EAAE,SAAW,EAAI,EAAGA,EAAE,gBAAkB,GAAIA,EAAE,gBAAkB,GAAIA,EAAE,aAAe,GAAIA,EAAE,gBAAkB,GAAI,KAAK,kBAAoB,EAC7N,EAAEL,EAAI,IAAM,CACX,IAAIK,EAAI,GACR,UAAWG,KAAK,KAAK,cAAc,KAAM,EACvC,GAAIA,EAAE,sBAAuB,CAC3BH,EAAI,GACJ,KACD,CACH,GAAI,CAACA,EACH,OACF,MAAMC,EAAI,CAAC,IAAIF,CAAG,EAClB,KAAK,uBAAuB,KAAK,CAAC,EAClC,IAAIG,EAAI,EACR,UAAWC,KAAK,KAAK,cAAc,KAAI,EAAI,CACzC,MAAMC,EAAI,KAAK,SAAS,IAAID,CAAC,EAC7B,UAAWE,KAAKF,EAAE,gBAChBF,EAAE,SAASI,CAAC,IAAMJ,EAAE,KAAKI,CAAC,EAAGH,KAC/B,UAAWG,KAAKF,EAAE,mBAAmB,KAAI,EAAI,CAC3C,MAAMG,EAAIH,EAAE,mBAAmB,IAAIE,CAAC,EACpC,KAAK,uBAAuBA,EAAID,CAAC,EAAIE,EAAIJ,EAAI,CAC9C,CACDC,EAAE,sBAAwB,EAC3B,CACD,QAASA,EAAI,EAAGA,EAAIF,EAAE,OAAQE,IAAK,CACjC,MAAMC,EAAIH,EAAEE,CAAC,EACb,KAAK,iBAAiB,IAAIC,EAAE,OAAQD,EAAI,EAAE,CAC3C,CACD,KAAK,uBAAyB,EACpC,EACI,KAAK,QAAQ,UAAaH,GAAM,CAC9B,GAAIA,EAAE,KAAK,SAAU,CACnB,MAAMC,EAAID,EAAE,KAAK,SAAUE,EAAIT,EAAE,IAAIQ,EAAE,MAAM,EAC7CP,EAAEQ,CAAC,EAAGP,IACN,MAAMQ,EAAI,KAAK,cAAc,IAAID,CAAC,EAClC,QAASE,EAAI,EAAGA,EAAIF,EAAE,KAAK,YAAaE,IACtC,KAAK,kBAAkBH,EAAE,OAASG,CAAC,EAAID,EACzC,KAAK,MAAM,IAAIF,EAAE,KAAMA,EAAE,OAAS,CAAC,EAAGC,EAAE,KAAK,SAC3CD,EAAE,UACFA,EAAE,UACFA,EAAE,OACFA,EAAE,OACFA,EAAE,SACZ,EAAW,KAAK,WAAW,IAAIA,EAAE,eAAgBA,EAAE,OAAS,CAAC,EAAG,KAAK,WAAW,IAAIA,EAAE,eAAgBA,EAAE,OAAS,CAAC,EAAG,KAAK,QAAQ,IAAIA,EAAE,YAAaA,EAAE,OAAS,CAAC,EAAG,KAAK,UAAU,OAAOC,CAAC,EAAGA,EAAE,gBAAkB,GAAI,KAAK,YAAc,EAClO,CACP,EACI,IAAIN,EACJ,eAAeC,GAAI,CACjBD,EAAI,MAAM4C,IACX,CACD3C,IACA,eAAeC,GAAI,CACjB,KAAO,CAACF,GACN,MAAM,IAAI,QAASI,GAAM,WAAWA,EAAG,CAAC,CAAC,CAC5C,CACD,MAAM,EAAKA,GAAM,CACf,GAAI,CAACJ,EAAG,CACNE,EAAC,EAAG,KAAK,IAAM,CACb,EAAEE,CAAC,CACb,CAAS,EACD,MACD,CACDN,EAAEM,CAAC,EACH,MAAMC,EAAIL,EAAE,QAAQ,EAAII,EAAE,KAAK,YAAc,CAAC,EAAGE,EAAIN,EAAE,QAAQ,EAAII,EAAE,KAAK,YAAc,CAAC,EAAGG,EAAIP,EAAE,QAAQ,EAAII,EAAE,KAAK,YAAc,CAAC,EAAGI,EAAIR,EAAE,QAAQ,EAAII,EAAE,KAAK,WAAW,EAAGK,EAAIT,EAAE,QAAQI,EAAE,KAAK,WAAW,EAAGM,EAAIV,EAAE,QAAQ,EAAII,EAAE,KAAK,YAAc,CAAC,EAAGO,EAAIX,EAAE,QAAQ,EAAII,EAAE,KAAK,YAAc,CAAC,EAAGQ,EAAIZ,EAAE,QAAQ,EAAII,EAAE,KAAK,YAAc,CAAC,EAAGS,EAAIb,EAAE,QAAQ,EAAII,EAAE,KAAK,YAAc,CAAC,EAC1XJ,EAAE,QAAQ,IAAII,EAAE,KAAK,UAAWC,EAAI,CAAC,EAAGL,EAAE,QAAQ,IAAII,EAAE,KAAK,UAAWE,EAAI,CAAC,EAAGN,EAAE,QAAQ,IAAII,EAAE,KAAK,OAAQG,EAAI,CAAC,EAAGP,EAAE,OAAO,IAAII,EAAE,KAAK,OAAQI,CAAC,EAAGR,EAAE,OAAO,IAAII,EAAE,KAAK,UAAWK,CAAC,EAAGT,EAAE,MACxLI,EAAE,SACFA,EAAE,KAAK,YACPC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,CACR,EACM,MAAMC,EAAI,IAAI,YAAYd,EAAE,QAAQ,OAAQU,EAAGN,EAAE,KAAK,YAAc,CAAC,EAAGiB,EAAI,IAAI,aAC9ErB,EAAE,QAAQ,OACVW,EACAP,EAAE,KAAK,YAAc,CAC7B,EAASkB,EAAI,IAAI,aACTtB,EAAE,QAAQ,OACVY,EACAR,EAAE,KAAK,YAAc,CACtB,EAAEmB,EAAI,IAAI,aAAavB,EAAE,QAAQ,OAAQa,EAAGT,EAAE,KAAK,YAAc,CAAC,EAAG,EAAI,KAAK,cAAc,IAAIA,CAAC,EAAGoB,EAAI,KAAK,SAAS,IAAIpB,CAAC,EAC5H,QAASqB,EAAI,EAAGA,EAAIrB,EAAE,KAAK,YAAaqB,IACtC,KAAK,kBAAkBD,EAAIC,CAAC,EAAI,EAClC,KAAK,MAAM,IAAIX,EAAGU,EAAI,CAAC,EAAG,KAAK,WAAW,IAAIH,EAAGG,EAAI,CAAC,EAAG,KAAK,WAAW,IAAIF,EAAGE,EAAI,CAAC,EAAG,KAAK,QAAQ,IAAID,EAAGC,EAAI,CAAC,EAAGxB,EAAE,MAAMK,CAAC,EAAGL,EAAE,MAAMM,CAAC,EAAGN,EAAE,MAAMO,CAAC,EAAGP,EAAE,MAAMQ,CAAC,EAAGR,EAAE,MAAMS,CAAC,EAAGT,EAAE,MAAMU,CAAC,EAAGV,EAAE,MAAMW,CAAC,EAAGX,EAAE,MAAMY,CAAC,EAAGZ,EAAE,MAAMa,CAAC,EAAG,KAAK,YAAc,GAAI,KAAK,uBAAyB,EAC3R,EAAO,EAAKT,GAAM,CACZ,IAAKA,EAAE,iBAAmBA,EAAE,iBAAmBA,EAAE,cAAgBA,EAAE,kBAAoBN,EAAEM,CAAC,EAAGA,EAAE,uBAAyBL,EAAG,EAAE,CAACK,EAAE,KAAK,SAAWA,EAAE,KAAK,SAAU,OACjK,MAAMC,EAAI,CACR,SAAU,IAAI,aAAaD,EAAE,SAAS,KAAI,CAAE,EAC5C,SAAU,IAAI,aAAaA,EAAE,SAAS,KAAI,CAAE,EAC5C,MAAO,IAAI,aAAaA,EAAE,MAAM,KAAI,CAAE,EACtC,SAAUA,EAAE,SACZ,YAAaA,EAAE,KAAK,YACpB,UAAWA,EAAE,KAAK,UAClB,UAAWA,EAAE,KAAK,UAClB,OAAQA,EAAE,KAAK,OACf,OAAQA,EAAE,KAAK,OACf,UAAWA,EAAE,KAAK,UAClB,OAAQ,KAAK,SAAS,IAAIA,CAAC,CACnC,EACM,KAAK,QAAQ,YACX,CACE,MAAOC,CACR,EACD,CACEA,EAAE,SAAS,OACXA,EAAE,SAAS,OACXA,EAAE,MAAM,OACRA,EAAE,UAAU,OACZA,EAAE,UAAU,OACZA,EAAE,OAAO,OACTA,EAAE,OAAO,OACTA,EAAE,UAAU,MACb,CACT,EAAS,KAAK,UAAU,IAAID,CAAC,EAAGA,EAAE,KAAK,SAAW,EAClD,EACI,KAAK,SAAYA,GAAM,CACrB,IAAIC,EAAI,KACR,SAAW,CAACC,EAAGC,CAAC,IAAK,KAAK,SACxB,GAAIH,GAAKG,EACPF,EAAIC,MAEJ,OACJ,OAAOD,CACR,EAAE,KAAK,cAAgB,CAACD,EAAGC,IAAM,CAChC,MAAMC,EAAI,KAAK,SAAS,IAAIF,CAAC,EAC7B,OAAOC,EAAIC,CACjB,EAAO,KAAK,UAAaF,GAAM,CACzB,KAAK,OAAO,IAAIA,CAAC,CACvB,EAAO,KAAK,QAAU,IAAM,CACtB,UAAWA,KAAK,KAAK,OACnB,EAAEA,CAAC,EACL,KAAK,OAAO,OAClB,EAAO,KAAK,QAAU,IAAM,CACtB,KAAK,QAAQ,WACnB,EACI,UAAWA,KAAK,KAAK,cAAc,KAAM,EACvC,EAAEA,CAAC,EACLL,GACD,CACD,IAAI,SAAU,CACZ,OAAO,KAAK,QACb,CACD,IAAI,MAAO,CACT,OAAO,KAAK,KACb,CACD,IAAI,OAAQ,CACV,OAAO,KAAK,MACb,CACD,IAAI,QAAS,CACX,OAAO,KAAK,OACb,CACD,IAAI,YAAa,CACf,OAAO,KAAK,WACb,CACD,IAAI,iBAAkB,CACpB,OAAO,KAAK,gBACb,CACD,IAAI,kBAAmB,CACrB,OAAO,KAAK,iBACb,CACD,IAAI,kBAAmB,CACrB,OAAO,KAAK,iBACb,CACD,IAAI,uBAAwB,CAC1B,OAAO,KAAK,sBACb,CACD,IAAI,wBAAyB,CAC3B,OAAO,KAAK,uBACb,CACD,IAAI,iBAAkB,CACpB,OAAO,KAAK,gBACb,CACD,IAAI,sBAAuB,CACzB,OAAO,KAAK,qBACb,CACD,IAAI,uBAAwB,CAC1B,OAAO,KAAK,sBACb,CACD,IAAI,uBAAwB,CAC1B,OAAO,KAAK,sBACb,CACD,IAAI,4BAA6B,CAC/B,OAAO,KAAK,2BACb,CACD,IAAI,6BAA8B,CAChC,OAAO,KAAK,4BACb,CACD,IAAI,WAAY,CACd,OAAO,KAAK,UACb,CACD,IAAI,WAAY,CACd,OAAO,KAAK,UACb,CACD,IAAI,QAAS,CACX,OAAO,KAAK,OACb,CACD,IAAI,aAAc,CAChB,OAAO,KAAK,YACb,CACD,IAAI,cAAe,CACjB,OAAO,KAAK,OAAO,KAAO,CAC3B,CACD,IAAI,UAAW,CACb,OAAO,KAAK,UAAU,KAAO,CAC9B,CACH,CACA,MAAMwD,EAAG,CACP,YAAY,EAAI,EAAG5D,EAAI,EAAGC,EAAI,EAAGC,EAAI,IAAK,CACxC,KAAK,EAAI,EAAG,KAAK,EAAIF,EAAG,KAAK,EAAIC,EAAG,KAAK,EAAIC,CAC9C,CACD,MAAO,CACL,MAAO,CAAC,KAAK,EAAG,KAAK,EAAG,KAAK,EAAG,KAAK,CAAC,CACvC,CACD,UAAW,CACT,MAAO,CAAC,KAAK,EAAI,IAAK,KAAK,EAAI,IAAK,KAAK,EAAI,IAAK,KAAK,EAAI,GAAG,CAC/D,CACD,aAAc,CACZ,MAAO,IAAM,KAAK,KAAI,EAAG,IAAK,GAAM,EAAE,SAAS,EAAE,EAAE,SAAS,EAAG,GAAG,CAAC,EAAE,KAAK,EAAE,CAC7E,CACD,UAAW,CACT,MAAO,IAAI,KAAK,KAAI,EAAG,KAAK,IAAI,CAAC,GAClC,CACH,CACA,MAAM2D,GAAK,IAAM,IAAIhB,GAAMiB,GAEzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA8GCC,GAED;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoCF,MAAM,WAAWjB,EAAG,CAClB,YAAY,EAAG9C,EAAG,CAChB,MAAM,EAAGA,CAAC,EAAG,KAAK,kBAAoB,GAAI,KAAK,cAAgB,IAAI4D,GAAG,IAAK,IAAK,EAAG,GAAG,EAAG,KAAK,YAAc,KAAM,KAAK,YAAc,IAAI,YAAe,KAAK,cAAgB,KAAM,KAAK,QAAU,KAClM,MAAM3D,EAAI,EAAE,OAAQC,EAAI,EAAE,GAC1B,IAAIC,EAAGC,EAAGC,EAAGC,EAAGC,EAAG,EAAG,EAAGE,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGO,EAC1D,KAAK,QAAU,IAAM,CACnB,KAAK,UAAY,KAAK,QAAQ,KAAK,QAAQzB,EAAE,MAAOA,EAAE,MAAM,EAAG,KAAK,QAAQ,OAAQ,EAAEE,EAAID,EAAE,mBAAmB,KAAK,QAAS,YAAY,EAAGA,EAAE,iBAAiBC,EAAG,GAAI,KAAK,QAAQ,KAAK,iBAAiB,MAAM,EAAGC,EAAIF,EAAE,mBAAmB,KAAK,QAAS,UAAU,EAAGA,EAAE,WAAWE,EAAG,IAAI,aAAa,CAACH,EAAE,MAAOA,EAAE,MAAM,CAAC,CAAC,EACjU,EACI,MAAM0B,EAAI,IAAM,CACd,KAAK,QAAUkC,GAAI,EAAE,KAAK,QAAQ,UAAa9B,GAAM,CACnD,GAAIA,EAAE,KAAK,WAAY,CACrB,KAAM,CAAE,WAAYC,GAAMD,EAAE,KAC5B,KAAK,YAAcC,EAAG9B,EAAE,WAAWA,EAAE,aAAcwB,CAAC,EAAGxB,EAAE,WAAWA,EAAE,aAAc8B,EAAG9B,EAAE,WAAW,CACrG,CACT,CACA,EACI,KAAK,YAAc,IAAM,CACvB,GAAI,CAAC,KAAK,QAAU,CAAC,KAAK,QAAS,CACjC,QAAQ,MAAM,wCAAwC,EACtD,MACD,CACD,KAAK,QAAS,EAAE,KAAK,OAAO,iBAAiB,cAAe0B,CAAC,EAAG,KAAK,OAAO,iBAAiB,gBAAiB,CAAC,EAC/G,UAAWG,KAAK,KAAK,OAAO,QAC1BA,aAAa,GAAKA,EAAE,iBAAiB,gBAAiBF,CAAC,EACzD,KAAK,YAAc,IAAI8B,GAAG,KAAK,MAAM,EAAGtD,EAAIH,EAAE,mBAAmB,KAAK,QAAS,OAAO,EAAGA,EAAE,WAAWG,EAAG,IAAI,aAAa,CAAC,KAAK,QAAQ,KAAK,GAAI,KAAK,QAAQ,KAAK,EAAE,CAAC,CAAC,EAAGC,EAAIJ,EAAE,mBAAmB,KAAK,QAAS,MAAM,EAAGA,EAAE,iBAAiBI,EAAG,GAAI,KAAK,QAAQ,KAAK,WAAW,MAAM,EAAGK,EAAIT,EAAE,mBAAmB,KAAK,QAAS,kBAAkB,EAAGA,EAAE,UAAUS,EAAG,KAAK,gBAAgB,EAAGC,EAAIV,EAAE,mBAAmB,KAAK,QAAS,cAAc,EAAGA,EAAE,WAAWU,EAAG,IAAI,aAAa,KAAK,aAAa,SAAQ,CAAE,CAAC,EAAG,KAAK,cAAgBV,EAAE,gBAAiBK,EAAIL,EAAE,mBAAmB,KAAK,QAAS,WAAW,EAAGA,EAAE,UAAUK,EAAG,CAAC,EAAGQ,EAAIb,EAAE,cAAa,EAAI,EAAIA,EAAE,mBAAmB,KAAK,QAAS,cAAc,EAAGA,EAAE,UAAU,EAAG,CAAC,EAAGc,EAAId,EAAE,cAAa,EAAI,EAAIA,EAAE,mBAAmB,KAAK,QAAS,oBAAoB,EAAGA,EAAE,UAAU,EAAG,CAAC,EAAGe,EAAIf,EAAE,cAAe,EAAEO,EAAIP,EAAE,mBAAmB,KAAK,QAAS,mBAAmB,EAAGA,EAAE,UAAUO,EAAG,CAAC,EAAGS,EAAIhB,EAAE,cAAa,EAAIQ,EAAIR,EAAE,mBACl7B,KAAK,QACL,yBACR,EAASA,EAAE,UAAUQ,EAAG,CAAC,EAAGS,EAAIjB,EAAE,aAAY,EAAIA,EAAE,WAAWA,EAAE,aAAciB,CAAC,EAAGjB,EAAE,WAAWA,EAAE,aAAc,IAAI,aAAa,CAAC,GAAI,GAAI,EAAG,GAAI,EAAG,EAAG,GAAI,CAAC,CAAC,EAAGA,EAAE,WAAW,EAAGW,EAAIX,EAAE,kBAAkB,KAAK,QAAS,UAAU,EAAGA,EAAE,wBAAwBW,CAAC,EAAGX,EAAE,oBAAoBW,EAAG,EAAGX,EAAE,MAAO,GAAI,EAAG,CAAC,EAAGwB,EAAIxB,EAAE,aAAY,EAAIY,EAAIZ,EAAE,kBAAkB,KAAK,QAAS,OAAO,EAAGA,EAAE,wBAAwBY,CAAC,EAAGZ,EAAE,WAAWA,EAAE,aAAcwB,CAAC,EAAGC,GACtb,EACI,MAAMC,EAAKG,GAAM,CACf,MAAMC,EAAID,EACVC,EAAE,kBAAkB,GAAKA,EAAE,OAAO,iBAAiB,gBAAiBH,CAAC,EAAGC,GAC9E,EAAO,EAAKC,GAAM,CACZ,MAAMC,EAAID,EACVC,EAAE,kBAAkB,GAAKA,EAAE,OAAO,oBAAoB,gBAAiBH,CAAC,EAAGC,GACjF,EAAOD,EAAKE,GAAM,CACZ,MAAMC,EAAID,EACVC,EAAE,kBAAkB,GAAK,KAAK,aAAe,KAAK,YAAY,UAAUA,EAAE,MAAM,CACjF,EAAEF,EAAI,IAAM,CACX,KAAK,aAAa,QAAO,EAAI,KAAK,YAAc,IAAI6B,GAAG,KAAK,MAAM,EAAG,KAAK,SAAS,UAAW,EAAEhC,EAAC,CACvG,EACI,KAAK,QAAU,IAAM,CACnB,GAAI,CAAC,KAAK,QAAU,CAAC,KAAK,SAAW,CAAC,KAAK,WAAY,CACrD,QAAQ,MAAM,wCAAwC,EACtD,MACD,CACD,GAAI,KAAK,WAAW,cAAgB,KAAK,WAAW,UAAW,KAAK,WAAW,aAAe,KAAK,WAAW,mBAAqB,KAAK,WAAW,uBAAwB,CACzK,KAAK,WAAW,cAAgBzB,EAAE,cAAcA,EAAE,QAAQ,EAAGA,EAAE,YAAYA,EAAE,WAAY,KAAK,YAAY,EAAGA,EAAE,cAAcA,EAAE,WAAYA,EAAE,eAAgBA,EAAE,aAAa,EAAGA,EAAE,cAAcA,EAAE,WAAYA,EAAE,eAAgBA,EAAE,aAAa,EAAGA,EAAE,cAAcA,EAAE,WAAYA,EAAE,mBAAoBA,EAAE,OAAO,EAAGA,EAAE,cAAcA,EAAE,WAAYA,EAAE,mBAAoBA,EAAE,OAAO,EAAGA,EAAE,WACjXA,EAAE,WACF,EACAA,EAAE,SACF,KAAK,WAAW,MAChB,KAAK,WAAW,OAChB,EACAA,EAAE,aACFA,EAAE,aACF,KAAK,WAAW,IACjB,GAAG,KAAK,WAAW,oBAAsBA,EAAE,cAAcA,EAAE,QAAQ,EAAGA,EAAE,YAAYA,EAAE,WAAYa,CAAC,EAAGb,EAAE,cAAcA,EAAE,WAAYA,EAAE,eAAgBA,EAAE,aAAa,EAAGA,EAAE,cAAcA,EAAE,WAAYA,EAAE,eAAgBA,EAAE,aAAa,EAAGA,EAAE,cAAcA,EAAE,WAAYA,EAAE,mBAAoBA,EAAE,OAAO,EAAGA,EAAE,cAAcA,EAAE,WAAYA,EAAE,mBAAoBA,EAAE,OAAO,EAAGA,EAAE,WAC3WA,EAAE,WACF,EACAA,EAAE,QACF,KAAK,WAAW,gBAChB,KAAK,WAAW,iBAChB,EACAA,EAAE,KACFA,EAAE,MACF,KAAK,WAAW,UAC1B,EAAWA,EAAE,cAAcA,EAAE,QAAQ,EAAGA,EAAE,YAAYA,EAAE,WAAYc,CAAC,EAAGd,EAAE,cAAcA,EAAE,WAAYA,EAAE,eAAgBA,EAAE,aAAa,EAAGA,EAAE,cAAcA,EAAE,WAAYA,EAAE,eAAgBA,EAAE,aAAa,EAAGA,EAAE,cAAcA,EAAE,WAAYA,EAAE,mBAAoBA,EAAE,OAAO,EAAGA,EAAE,cAAcA,EAAE,WAAYA,EAAE,mBAAoBA,EAAE,OAAO,EAAGA,EAAE,WACpUA,EAAE,WACF,EACAA,EAAE,MACF,KAAK,WAAW,sBAChB,KAAK,WAAW,uBAChB,EACAA,EAAE,YACFA,EAAE,aACF,KAAK,WAAW,gBACjB,GAAG,KAAK,WAAW,yBAA2BA,EAAE,cAAcA,EAAE,QAAQ,EAAGA,EAAE,YAAYA,EAAE,WAAYe,CAAC,EAAGf,EAAE,cAAcA,EAAE,WAAYA,EAAE,eAAgBA,EAAE,aAAa,EAAGA,EAAE,cAAcA,EAAE,WAAYA,EAAE,eAAgBA,EAAE,aAAa,EAAGA,EAAE,cAAcA,EAAE,WAAYA,EAAE,mBAAoBA,EAAE,OAAO,EAAGA,EAAE,cAAcA,EAAE,WAAYA,EAAE,mBAAoBA,EAAE,OAAO,EAAGA,EAAE,WAChXA,EAAE,WACF,EACAA,EAAE,QACF,KAAK,WAAW,qBAChB,KAAK,WAAW,sBAChB,EACAA,EAAE,KACFA,EAAE,MACF,KAAK,WAAW,eAC1B,EAAWA,EAAE,cAAcA,EAAE,QAAQ,EAAGA,EAAE,YAAYA,EAAE,WAAYgB,CAAC,EAAGhB,EAAE,cAAcA,EAAE,WAAYA,EAAE,eAAgBA,EAAE,aAAa,EAAGA,EAAE,cAAcA,EAAE,WAAYA,EAAE,eAAgBA,EAAE,aAAa,EAAGA,EAAE,cAAcA,EAAE,WAAYA,EAAE,mBAAoBA,EAAE,OAAO,EAAGA,EAAE,cAAcA,EAAE,WAAYA,EAAE,mBAAoBA,EAAE,OAAO,EAAGA,EAAE,WACpUA,EAAE,WACF,EACAA,EAAE,MACF,KAAK,WAAW,2BAChB,KAAK,WAAW,4BAChB,EACAA,EAAE,YACFA,EAAE,aACF,KAAK,WAAW,qBAC1B,GACQ,MAAM6B,EAAI,IAAI,aAAa,KAAK,WAAW,UAAU,MAAK,EAAG,MAAM,EAAGC,EAAI,IAAI,aAAa,KAAK,WAAW,WAAW,MAAO,EAAC,MAAM,EAAG,EAAI,IAAI,YAAY,KAAK,WAAW,iBAAiB,MAAO,EAAC,MAAM,EAC1M,KAAK,SAAS,YACZ,CACE,SAAU,CACR,UAAWD,EACX,WAAYC,EACZ,iBAAkB,EAClB,YAAa,KAAK,WAAW,WAC9B,CACF,EACD,CAACD,EAAE,OAAQC,EAAE,OAAQ,EAAE,MAAM,CAC9B,EAAE,KAAK,WAAW,YAAc,GAAI,KAAK,WAAW,kBAAoB,GAAI,KAAK,WAAW,uBAAyB,EACvH,CACD,KAAK,QAAQ,OAAM,EAAI,KAAK,SAAS,YAAY,CAAE,SAAU,KAAK,QAAQ,KAAK,SAAS,MAAM,CAAE,EAAG9B,EAAE,SAAS,EAAG,EAAGD,EAAE,MAAOA,EAAE,MAAM,EAAGC,EAAE,WAAW,EAAG,EAAG,EAAG,CAAC,EAAGA,EAAE,MAAMA,EAAE,gBAAgB,EAAGA,EAAE,QAAQA,EAAE,UAAU,EAAGA,EAAE,OAAOA,EAAE,KAAK,EAAGA,EAAE,kBAAkBA,EAAE,oBAAqBA,EAAE,IAAKA,EAAE,oBAAqBA,EAAE,GAAG,EAAGA,EAAE,sBAAsBA,EAAE,SAAUA,EAAE,QAAQ,EAAGA,EAAE,iBAAiBC,EAAG,GAAI,KAAK,QAAQ,KAAK,iBAAiB,MAAM,EAAGD,EAAE,iBAAiBI,EAAG,GAAI,KAAK,QAAQ,KAAK,WAAW,MAAM,EAAGJ,EAAE,WAAWA,EAAE,aAAciB,CAAC,EAAGjB,EAAE,oBAAoBW,EAAG,EAAGX,EAAE,MAAO,GAAI,EAAG,CAAC,EAAGA,EAAE,WAAWA,EAAE,aAAcwB,CAAC,EAAGxB,EAAE,WAAWA,EAAE,aAAc,KAAK,WAAYA,EAAE,WAAW,EAAGA,EAAE,qBAAqBY,EAAG,EAAGZ,EAAE,IAAK,EAAG,CAAC,EAAGA,EAAE,oBAAoBY,EAAG,CAAC,EAAGZ,EAAE,oBAAoBA,EAAE,aAAc,EAAG,EAAG,KAAK,WAAW,MAAM,CACnzB,EAAO,KAAK,SAAW,IAAM,CACvB,GAAI,CAAC,KAAK,QAAU,CAAC,KAAK,SAAW,CAAC,KAAK,WAAY,CACrD,QAAQ,MAAM,yCAAyC,EACvD,MACD,CACD,KAAK,OAAO,oBAAoB,cAAe0B,CAAC,EAAG,KAAK,OAAO,oBAAoB,gBAAiB,CAAC,EACrG,UAAWG,KAAK,KAAK,OAAO,QAC1BA,aAAa,GAAKA,EAAE,oBAAoB,gBAAiBF,CAAC,EAC5D,KAAK,SAAS,UAAW,EAAE,KAAK,WAAW,QAAS,EAAE3B,EAAE,cAAc,KAAK,YAAY,EAAGA,EAAE,cAAca,CAAC,EAAGb,EAAE,cAAcc,CAAC,EAAGd,EAAE,aAAawB,CAAC,EAAGxB,EAAE,aAAaiB,CAAC,CAC3K,EAAO,KAAK,qBAAwBY,GAAM,CACpC,KAAK,kBAAoBA,EAAG,KAAK,cAAgB7B,EAAE,UAAUS,EAAGoB,CAAC,CACvE,EAAO,KAAK,iBAAoBA,GAAM,CAChC,KAAK,cAAgBA,EAAG,KAAK,cAAgB7B,EAAE,WAAWU,EAAG,IAAI,aAAamB,EAAE,SAAQ,CAAE,CAAC,CACjG,CACG,CACD,IAAI,YAAa,CACf,OAAO,KAAK,WACb,CACD,IAAI,YAAa,CACf,OAAO,KAAK,WACb,CACD,IAAI,cAAe,CACjB,OAAO,KAAK,aACb,CACD,IAAI,kBAAmB,CACrB,OAAO,KAAK,iBACb,CACD,IAAI,iBAAiB,EAAG,CACtB,KAAK,qBAAqB,CAAC,CAC5B,CACD,IAAI,cAAe,CACjB,OAAO,KAAK,aACb,CACD,IAAI,aAAa,EAAG,CAClB,KAAK,iBAAiB,CAAC,CACxB,CACD,IAAI,QAAS,CACX,OAAO,KAAK,OACb,CACD,kBAAmB,CACjB,OAAO+B,EACR,CACD,oBAAqB,CACnB,OAAOC,EACR,CACH,CACA,MAAMC,EAAG,CACP,YAAY,EAAI,EAAG,CACjB,IAAIhE,EAAI,EAAGC,EAAI,GAAIC,EAAGC,EAAGC,EAAGC,EAC5B,KAAK,WAAcC,GAAM,CACvB,GAAI,EAAEA,aAAa,IACjB,MAAM,IAAI,MAAM,qCAAqC,EACvDN,EAAIM,EAAE,QAAU,EAAI,EAAGL,EAAI,GAAIC,EAAII,EAAGH,EAAIG,EAAE,SAAS,GAAIF,EAAID,EAAE,mBAAmBD,EAAE,QAAS,cAAc,EAAGC,EAAE,UAAUC,EAAG,CAAC,EAAGC,EAAIF,EAAE,mBAAmBD,EAAE,QAAS,WAAW,EAAGC,EAAE,UAAUE,EAAGL,CAAC,CACzM,EAAO,KAAK,OAAS,IAAM,CACrB,CAACC,GAAKC,EAAE,YAAY,WAAaC,EAAE,WAAWD,EAAE,OAAO,EAAGF,EAAI,KAAK,IAAIA,EAAI,EAAI,IAAM,CAAC,EAAGA,GAAK,IAAMC,EAAI,GAAIE,EAAE,UAAUC,EAAG,CAAC,GAAID,EAAE,UAAUE,EAAGL,CAAC,EACtJ,CACG,CACD,SAAU,CACT,CACH,CACA,MAAMiE,EAAG,CACP,YAAY,EAAI,KAAMjE,EAAI,KAAM,CAC9B,KAAK,iBAAmB,IAAI4D,GAC5B,MAAM3D,EAAI,GAAK,SAAS,cAAc,QAAQ,EAC9C,IAAMA,EAAE,MAAM,QAAU,QAASA,EAAE,MAAM,UAAY,aAAcA,EAAE,MAAM,MAAQ,OAAQA,EAAE,MAAM,OAAS,OAAQA,EAAE,MAAM,OAAS,IAAKA,EAAE,MAAM,QAAU,IAAK,SAAS,KAAK,YAAYA,CAAC,GAAIA,EAAE,MAAM,WAAa,KAAK,iBAAiB,YAAa,EAAE,KAAK,QAAUA,EAAG,KAAK,IAAMA,EAAE,WAAW,SAAU,CAAE,UAAW,EAAI,CAAA,EAC/T,MAAMC,EAAIF,GAAK,GACfA,GAAKE,EAAE,KAAK,IAAI8D,EAAI,EAAG,KAAK,eAAiB,IAAI,GAAG,KAAM9D,CAAC,EAC3D,MAAMC,EAAI,CAAC,KAAK,cAAc,EAC9B,KAAK,OAAS,IAAM,CAClB,MAAMC,EAAIH,EAAE,YAAaI,EAAIJ,EAAE,cAC9BA,EAAE,QAAUG,GAAKH,EAAE,SAAWI,IAAM,KAAK,QAAQD,EAAGC,CAAC,CACvD,EAAE,KAAK,QAAU,CAACD,EAAGC,IAAM,CAC1BJ,EAAE,MAAQG,EAAGH,EAAE,OAASI,EAAG,KAAK,IAAI,SAAS,EAAG,EAAGJ,EAAE,MAAOA,EAAE,MAAM,EACpE,UAAWK,KAAKH,EACdG,EAAE,OAAM,CACX,EAAE,KAAK,OAAS,CAACF,EAAGC,IAAM,CACzB,UAAWC,KAAKH,EACdG,EAAE,OAAOF,EAAGC,CAAC,CACrB,EAAO,KAAK,QAAU,IAAM,CACtB,UAAWD,KAAKD,EACdC,EAAE,QAAO,CACjB,EAAO,KAAK,WAAcA,GAAM,CAC1BD,EAAE,KAAKC,CAAC,CACd,EAAO,KAAK,cAAiBA,GAAM,CAC7B,MAAMC,EAAIF,EAAE,QAAQC,CAAC,EACrB,GAAIC,EAAI,EACN,MAAM,IAAI,MAAM,mBAAmB,EACrCF,EAAE,OAAOE,EAAG,CAAC,CACnB,EAAO,KAAK,QACT,CACD,IAAI,QAAS,CACX,OAAO,KAAK,OACb,CACD,IAAI,IAAK,CACP,OAAO,KAAK,GACb,CACD,IAAI,eAAgB,CAClB,OAAO,KAAK,cACb,CACD,IAAI,iBAAkB,CACpB,OAAO,KAAK,gBACb,CACD,IAAI,gBAAgB,EAAG,CACrB,KAAK,iBAAmB,EAAG,KAAK,QAAQ,MAAM,WAAa,EAAE,aAC9D,CACH,CACA,MAAM6D,EAAG,CACP,YAAY,EAAGlE,EAAGC,EAAI,GAAKC,EAAI,GAAKC,EAAI,EAAGC,EAAI,GAAIC,EAAI,IAAIN,EAAK,CAC9D,KAAK,SAAW,IAAK,KAAK,SAAW,GAAI,KAAK,QAAU,GAAK,KAAK,QAAU,GAAI,KAAK,WAAa,EAAG,KAAK,SAAW,EAAG,KAAK,UAAY,EAAG,KAAK,UAAY,IAAM,KAAK,gBAAkB,IAAM,CACpM,EACI,IAAIO,EAAID,EAAE,MAAK,EAAIE,EAAID,EAAE,MAAK,EAAI,EAAIL,EAAG,EAAIC,EAAGO,EAAIN,EAAGO,EAAI,GAAIC,EAAI,GAAIC,EAAI,EAAGC,EAAI,EAAGC,EAAI,EACzF,MAAMC,EAAI,CAAA,EACV,IAAIC,EAAI,GACR,MAAMC,EAAI,IAAM,CACd,GAAID,EAAG,OACP,MAAMwC,EAAI,EAAE,SAAS,QAAO,EAC5B,EAAI,CAACA,EAAE,EAAG,EAAI,CAACA,EAAE,EACjB,MAAM,EAAI,EAAE,SAAS,EAAI/C,EAAI,KAAK,IAAI,CAAC,EAAI,KAAK,IAAI,CAAC,EAAG8C,EAAI,EAAE,SAAS,EAAI9C,EAAI,KAAK,IAAI,CAAC,EAAG,EAAI,EAAE,SAAS,EAAIA,EAAI,KAAK,IAAI,CAAC,EAAI,KAAK,IAAI,CAAC,EAC3IF,EAAI,IAAIR,EAAE,EAAGwD,EAAG,CAAC,CACvB,EACI,EAAE,iBAAiB,gBAAiBtC,CAAC,EAAG,KAAK,gBAAmBuC,GAAM,CACpE,MAAM,EAAIA,EAAE,EAAI,EAAE,SAAS,EAAGD,EAAIC,EAAE,EAAI,EAAE,SAAS,EAAG,EAAIA,EAAE,EAAI,EAAE,SAAS,EAC3E/C,EAAI,KAAK,KAAK,EAAI,EAAI8C,EAAIA,EAAI,EAAI,CAAC,EAAG,EAAI,KAAK,MAAMA,EAAG,KAAK,KAAK,EAAI,EAAI,EAAI,CAAC,CAAC,EAAG,EAAI,CAAC,KAAK,MAAM,EAAG,CAAC,EAAGhD,EAAI,IAAIR,EAAEyD,EAAE,EAAGA,EAAE,EAAGA,EAAE,CAAC,CACvI,EACI,MAAMtC,EAAI,IAAM,GAAM,IAAOT,EAAI,KAAK,UAAY,KAAK,QAAU,KAAK,SAAUU,EAAKqC,GAAM,CACzFzC,EAAEyC,EAAE,IAAI,EAAI,GAAIA,EAAE,OAAS,YAAczC,EAAE,KAAO,IAAKyC,EAAE,OAAS,cAAgBzC,EAAE,KAAO,IAAKyC,EAAE,OAAS,cAAgBzC,EAAE,KAAO,IAAKyC,EAAE,OAAS,eAAiBzC,EAAE,KAAO,GACpL,EAAOW,EAAK8B,GAAM,CACZzC,EAAEyC,EAAE,IAAI,EAAI,GAAIA,EAAE,OAAS,YAAczC,EAAE,KAAO,IAAKyC,EAAE,OAAS,cAAgBzC,EAAE,KAAO,IAAKyC,EAAE,OAAS,cAAgBzC,EAAE,KAAO,IAAKyC,EAAE,OAAS,eAAiBzC,EAAE,KAAO,GACpL,EAAOY,EAAK6B,GAAM,CACZ,EAAEA,CAAC,EAAG9C,EAAI,GAAIC,EAAI6C,EAAE,SAAW,EAAG3C,EAAI2C,EAAE,QAAS1C,EAAI0C,EAAE,QAAS,OAAO,iBAAiB,UAAW5B,CAAC,CAC1G,EAAOA,EAAK4B,GAAM,CACZ,EAAEA,CAAC,EAAG9C,EAAI,GAAIC,EAAI,GAAI,OAAO,oBAAoB,UAAWiB,CAAC,CACnE,EAAO,EAAK4B,GAAM,CACZ,GAAI,EAAEA,CAAC,EAAG,CAAC9C,GAAK,CAAC,EAAG,OACpB,MAAM,EAAI8C,EAAE,QAAU3C,EAAG0C,EAAIC,EAAE,QAAU1C,EACzC,GAAIH,EAAG,CACL,MAAM,EAAIO,EAAG,EAAEkC,EAAI,CAAC,EAAI,KAAK,SAAW,IAAO,EAAG,EAAI,CAACG,EAAI,KAAK,SAAW,IAAO,EAAGE,EAAIlC,EAAE,uBAAuB,EAAE,QAAQ,EAAE,OAAQ2B,EAAI,IAAInD,EAAE0D,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,CAAC,EAAGN,EAAI,IAAIpD,EAAE0D,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,CAAC,EAC7LlD,EAAIA,EAAE,IAAI2C,EAAE,SAASE,CAAC,CAAC,EAAG7C,EAAIA,EAAE,IAAI4C,EAAE,SAAS,CAAC,CAAC,CAClD,MACC,GAAK,EAAI,KAAK,WAAa,KAAM,GAAKI,EAAI,KAAK,WAAa,KAAM,EAAI,KAAK,IACzE,KAAK,IAAI,EAAG,KAAK,SAAW,KAAK,GAAK,GAAG,EACzC,KAAK,SAAW,KAAK,GAAK,GACpC,EACM1C,EAAI2C,EAAE,QAAS1C,EAAI0C,EAAE,OAC3B,EAAO3B,EAAK2B,GAAM,CACZ,EAAEA,CAAC,EACH,MAAM,EAAItC,IACVT,GAAK+C,EAAE,OAAS,KAAK,UAAY,KAAQ,EAAG/C,EAAI,KAAK,IAAI,KAAK,IAAIA,EAAG,KAAK,OAAO,EAAG,KAAK,OAAO,CACtG,EAAOqB,EAAK0B,GAAM,CACZ,GAAI,EAAEA,CAAC,EAAGA,EAAE,QAAQ,SAAW,EAC7B9C,EAAI,GAAIC,EAAI,GAAIE,EAAI2C,EAAE,QAAQ,CAAC,EAAE,QAAS1C,EAAI0C,EAAE,QAAQ,CAAC,EAAE,QAAS5C,EAAI,UACjE4C,EAAE,QAAQ,SAAW,EAAG,CAC/B9C,EAAI,GAAIC,EAAI,GAAIE,GAAK2C,EAAE,QAAQ,CAAC,EAAE,QAAUA,EAAE,QAAQ,CAAC,EAAE,SAAW,EAAG1C,GAAK0C,EAAE,QAAQ,CAAC,EAAE,QAAUA,EAAE,QAAQ,CAAC,EAAE,SAAW,EAC3H,MAAM,EAAIA,EAAE,QAAQ,CAAC,EAAE,QAAUA,EAAE,QAAQ,CAAC,EAAE,QAASD,EAAIC,EAAE,QAAQ,CAAC,EAAE,QAAUA,EAAE,QAAQ,CAAC,EAAE,QAC/F5C,EAAI,KAAK,KAAK,EAAI,EAAI2C,EAAIA,CAAC,CAC5B,CACP,EAAOxB,EAAKyB,GAAM,CACZ,EAAEA,CAAC,EAAG9C,EAAI,GAAIC,EAAI,EACxB,EAAOqB,EAAKwB,GAAM,CACZ,GAAI,EAAEA,CAAC,EAAG,EAAE,CAAC9C,GAAK,CAAC,GACjB,GAAIC,EAAG,CACL,MAAM,EAAIO,EAAG,EAAEqC,EAAIC,EAAE,QAAQ,CAAC,EAAE,QAAUA,EAAE,QAAQ,CAAC,EAAE,QAAS,EAAIA,EAAE,QAAQ,CAAC,EAAE,QAAUA,EAAE,QAAQ,CAAC,EAAE,QAASJ,EAAI,KAAK,KAAKG,EAAIA,EAAI,EAAI,CAAC,EAAG,EAAI3C,EAAIwC,EACvJ3C,GAAK,EAAI,KAAK,UAAY,GAAM,EAAGA,EAAI,KAAK,IAAI,KAAK,IAAIA,EAAG,KAAK,OAAO,EAAG,KAAK,OAAO,EAAGG,EAAIwC,EAC9F,MAAMK,GAAKD,EAAE,QAAQ,CAAC,EAAE,QAAUA,EAAE,QAAQ,CAAC,EAAE,SAAW,EAAGN,GAAKM,EAAE,QAAQ,CAAC,EAAE,QAAUA,EAAE,QAAQ,CAAC,EAAE,SAAW,EAAGL,EAAIM,EAAI5C,EAAG,EAAIqC,EAAIpC,EAAGuC,EAAI9B,EAAE,uBAAuB,EAAE,QAAQ,EAAE,OAAQ+B,EAAI,IAAIvD,EAAEsD,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,CAAC,EAAG,EAAI,IAAItD,EAAEsD,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,CAAC,EAClP9C,EAAIA,EAAE,IAAI+C,EAAE,SAAS,CAACH,EAAI,KAAK,SAAW,KAAQ,CAAC,CAAC,EAAG5C,EAAIA,EAAE,IAAI,EAAE,SAAS,CAAC,EAAI,KAAK,SAAW,KAAQ,CAAC,CAAC,EAAGM,EAAI4C,EAAG3C,EAAIoC,CACnI,KAAe,CACL,MAAM,EAAIM,EAAE,QAAQ,CAAC,EAAE,QAAU3C,EAAG0C,EAAIC,EAAE,QAAQ,CAAC,EAAE,QAAU1C,EAC/D,GAAK,EAAI,KAAK,WAAa,KAAM,GAAKyC,EAAI,KAAK,WAAa,KAAM,EAAI,KAAK,IACzE,KAAK,IAAI,EAAG,KAAK,SAAW,KAAK,GAAK,GAAG,EACzC,KAAK,SAAW,KAAK,GAAK,GAC3B,EAAE1C,EAAI2C,EAAE,QAAQ,CAAC,EAAE,QAAS1C,EAAI0C,EAAE,QAAQ,CAAC,EAAE,OAC/C,CACJ,EAAE,EAAI,CAACA,EAAG,EAAGD,KAAO,EAAIA,GAAKC,EAAID,EAAI,EACtC,KAAK,OAAS,IAAM,CAClBvC,EAAI,GAAIf,EAAI,EAAEA,EAAG,EAAG,KAAK,SAAS,EAAGC,EAAI,EAAEA,EAAG,EAAG,KAAK,SAAS,EAAGC,EAAI,EAAEA,EAAGM,EAAG,KAAK,SAAS,EAAGH,EAAIA,EAAE,KAAKC,EAAG,KAAK,SAAS,EAC3H,MAAMiD,EAAIlD,EAAE,EAAIH,EAAI,KAAK,IAAIF,CAAC,EAAI,KAAK,IAAIC,CAAC,EAAG,EAAII,EAAE,EAAIH,EAAI,KAAK,IAAID,CAAC,EAAGqD,EAAIjD,EAAE,EAAIH,EAAI,KAAK,IAAIF,CAAC,EAAI,KAAK,IAAIC,CAAC,EAChH,EAAE,SAAW,IAAIH,EAAEyD,EAAG,EAAGD,CAAC,EAC1B,MAAM,EAAIjD,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAS,EAAI8C,EAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAG,EAAI,KAAK,MAAM,EAAE,EAAG,EAAE,CAAC,EAC1F,EAAE,SAAW,EAAE,UAAU,IAAIrD,EAAEqD,EAAG,EAAG,CAAC,CAAC,EACvC,MAAMK,EAAI,KAAOP,EAAI,IAAMC,EAAI5B,EAAE,uBAAuB,EAAE,QAAQ,EAAE,OAAQ,EAAI,IAAIxB,EAAE,CAACoD,EAAE,CAAC,EAAG,CAACA,EAAE,CAAC,EAAG,CAACA,EAAE,CAAC,CAAC,EAAGE,EAAI,IAAItD,EAAEoD,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,CAAC,EACtIpC,EAAE,OAASR,EAAIA,EAAE,IAAI,EAAE,SAASkD,CAAC,CAAC,GAAI1C,EAAE,OAASR,EAAIA,EAAE,SAAS,EAAE,SAASkD,CAAC,CAAC,GAAI1C,EAAE,OAASR,EAAIA,EAAE,SAAS8C,EAAE,SAASI,CAAC,CAAC,GAAI1C,EAAE,OAASR,EAAIA,EAAE,IAAI8C,EAAE,SAASI,CAAC,CAAC,GAAI1C,EAAE,OAAS,GAAKmC,GAAInC,EAAE,OAAS,GAAKmC,GAAInC,EAAE,OAAS,GAAKmC,GAAInC,EAAE,OAAS,GAAKmC,GAAIlC,EAAI,EAC5P,EACI,MAAM,EAAKwC,GAAM,CACfA,EAAE,eAAc,EAAIA,EAAE,gBAAe,CAC3C,EACI,KAAK,QAAU,IAAM,CACnBxD,EAAE,oBAAoB,YAAa,CAAC,EAAGA,EAAE,oBAAoB,WAAY,CAAC,EAAGA,EAAE,oBAAoB,YAAa,CAAC,EAAGA,EAAE,oBAAoB,cAAe,CAAC,EAAGA,EAAE,oBAAoB,YAAa2B,CAAC,EAAG3B,EAAE,oBAAoB,YAAa,CAAC,EAAGA,EAAE,oBAAoB,QAAS6B,CAAC,EAAG7B,EAAE,oBAAoB,aAAc8B,CAAC,EAAG9B,EAAE,oBAAoB,WAAY+B,CAAC,EAAG/B,EAAE,oBAAoB,YAAagC,CAAC,EAAG5B,IAAM,OAAO,oBAAoB,UAAWe,CAAC,EAAG,OAAO,oBAAoB,QAASO,CAAC,EACzd,EAAEtB,IAAM,OAAO,iBAAiB,UAAWe,CAAC,EAAG,OAAO,iBAAiB,QAASO,CAAC,GAAI1B,EAAE,iBAAiB,YAAa,CAAC,EAAGA,EAAE,iBAAiB,WAAY,CAAC,EAAGA,EAAE,iBAAiB,YAAa,CAAC,EAAGA,EAAE,iBAAiB,cAAe,CAAC,EAAGA,EAAE,iBAAiB,YAAa2B,CAAC,EAAG3B,EAAE,iBAAiB,YAAa,CAAC,EAAGA,EAAE,iBAAiB,QAAS6B,CAAC,EAAG7B,EAAE,iBAAiB,aAAc8B,CAAC,EAAG9B,EAAE,iBAAiB,WAAY+B,CAAC,EAAG/B,EAAE,iBAAiB,YAAagC,CAAC,EAAG,KAAK,OAAM,CACvc,CACH,uKC7iEyB,EAAA,OAAA,kFAgIzBmC,GAAmCC,EAAAC,EAAAC,CAAA,gFA3HvB,CAAA,MAAAC,CAAA,EAAAC,EACA,CAAA,WAAAC,CAAA,EAAAD,EACA,CAAA,UAAAE,CAAA,EAAAF,GAKA,aAAAG,EAA2B,MAAA,EAAAH,EAIlCI,EAsBAC,EACAC,EACAC,EACAC,EAAuC,KACvCC,EACAC,EAAU,GACVC,EAAyB,KAEpB,SAAAC,GAAA,CAkBH,GAjBDD,IAAY,OACf,qBAAqBA,CAAO,EAC5BA,EAAU,MAGPH,IAAa,OAChBA,EAAS,QAAA,EACTA,EAAW,MAGZF,EAAA,IAAYO,GACZN,EAAA,IAAaO,GACbN,EAAe,IAAAO,GAAoBV,CAAM,EACzCI,EAAe,IAAAO,GAAoBT,EAAQF,CAAM,EACjDI,EAAS,UAAYR,EACrBQ,EAAS,SAAWP,EAEf,CAAAH,aAIDkB,EAAU,GACR,MAAAC,EAAA,SAAA,CACD,GAAAD,EAAA,CACH,QAAQ,MAAM,iBAAiB,SAG3B,GAAA,CAAAd,EACM,MAAA,IAAA,MAAM,iBAAiB,EAG9B,GADJc,EAAU,GACNd,EAAa,SAAS,MAAM,EACzB,MAAAgB,GAAgB,UAAUhB,EAAcG,EAAA,MAAgB,UACpDH,EAAa,SAAS,QAAQ,EAClC,MAAAiB,GAAa,UAAUjB,EAAcG,EAAA,MAAgB,MAEjD,OAAA,IAAA,MAAM,uBAAuB,EAExCW,EAAU,IAGLI,EAAA,IAAA,CACA,GAAAb,EAID,IAAAS,EAAA,CACHN,EAAU,sBAAsBU,CAAK,SAItCZ,EAAS,OAAA,EACTD,EAAS,OAAOF,EAAOC,CAAM,EAE7BI,EAAU,sBAAsBU,CAAK,IAGtCH,IACAP,EAAU,sBAAsBU,CAAK,EAGtCC,GAAA,KACKvB,GAAS,MACZa,QAEDF,EAAU,EAAA,OAGLF,GACHA,EAAS,QAAA,+CAYMH,EAAMkB,iNAvHxBC,EAAA,EAAGC,EAAM1B,EAAM,GAAA,uBAcdI,EAAesB,CAAA,EAEXA,GAAA,KACHrB,EAAaqB,CAAA,QACPC,EAAgBD,EACtBE,GAAiBF,CAAG,EAAE,KAAMG,GAAA,CACvBxB,IAAesB,EAClBF,EAAA,EAAArB,EAAeyB,GAAY,MAAA,EAE3BA,GAAY,IAAI,gBAAgBA,CAAQ,kBAyFtCJ,EAAA,EAAA,CAAA,KAAAK,GAAS9B,IACd,KAAM,QAAA8B,CAAA,kBAGJxB,GAAUK,GAAWmB,GAAQjB", "x_google_ignoreList": [0]}