import{SvelteComponent as D,init as E,safe_not_equal as V,create_slot as z,element as A,space as B,claim_element as F,children as G,claim_space as H,detach as S,attr as m,toggle_class as _,set_style as g,insert_hydration as J,append_hydration as K,transition_in as r,group_outros as L,transition_out as c,check_outros as M,update_slot_base as N,get_all_dirty_from_scope as O,get_slot_changes as P,assign as Q,create_component as R,claim_component as T,mount_component as U,get_spread_update as W,get_spread_object as X,destroy_component as Y}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{S as Z}from"./2.B2AoQPnG.js";function I(t){let e,a;const o=[{autoscroll:t[6].autoscroll},{i18n:t[6].i18n},t[5],{status:t[5]?t[5].status=="pending"?"generating":t[5].status:null}];let f={};for(let l=0;l<o.length;l+=1)f=Q(f,o[l]);return e=new Z({props:f}),{c(){R(e.$$.fragment)},l(l){T(e.$$.fragment,l)},m(l,u){U(e,l,u),a=!0},p(l,u){const h=u&96?W(o,[u&64&&{autoscroll:l[6].autoscroll},u&64&&{i18n:l[6].i18n},u&32&&X(l[5]),u&32&&{status:l[5]?l[5].status=="pending"?"generating":l[5].status:null}]):{};e.$set(h)},i(l){a||(r(e.$$.fragment,l),a=!0)},o(l){c(e.$$.fragment,l),a=!1},d(l){Y(e,l)}}}function y(t){let e,a,o,f,l=t[5]&&t[7]&&t[6]&&I(t);const u=t[14].default,h=z(u,t,t[13],null);return{c(){e=A("div"),l&&l.c(),a=B(),h&&h.c(),this.h()},l(i){e=F(i,"DIV",{id:!0,class:!0});var s=G(e);l&&l.l(s),a=H(s),h&&h.l(s),s.forEach(S),this.h()},h(){m(e,"id",t[1]),m(e,"class",o="row "+t[2].join(" ")+" svelte-1xp0cw7"),_(e,"compact",t[4]==="compact"),_(e,"panel",t[4]==="panel"),_(e,"unequal-height",t[0]===!1),_(e,"stretch",t[0]),_(e,"hide",!t[3]),_(e,"grow-children",t[11]&&t[11]>=1),g(e,"height",t[12](t[8])),g(e,"max-height",t[12](t[10])),g(e,"min-height",t[12](t[9])),g(e,"flex-grow",t[11])},m(i,s){J(i,e,s),l&&l.m(e,null),K(e,a),h&&h.m(e,null),f=!0},p(i,[s]){i[5]&&i[7]&&i[6]?l?(l.p(i,s),s&224&&r(l,1)):(l=I(i),l.c(),r(l,1),l.m(e,a)):l&&(L(),c(l,1,1,()=>{l=null}),M()),h&&h.p&&(!f||s&8192)&&N(h,u,i,i[13],f?P(u,i[13],s,null):O(i[13]),null),(!f||s&2)&&m(e,"id",i[1]),(!f||s&4&&o!==(o="row "+i[2].join(" ")+" svelte-1xp0cw7"))&&m(e,"class",o),(!f||s&20)&&_(e,"compact",i[4]==="compact"),(!f||s&20)&&_(e,"panel",i[4]==="panel"),(!f||s&5)&&_(e,"unequal-height",i[0]===!1),(!f||s&5)&&_(e,"stretch",i[0]),(!f||s&12)&&_(e,"hide",!i[3]),(!f||s&2052)&&_(e,"grow-children",i[11]&&i[11]>=1),s&256&&g(e,"height",i[12](i[8])),s&1024&&g(e,"max-height",i[12](i[10])),s&512&&g(e,"min-height",i[12](i[9])),s&2048&&g(e,"flex-grow",i[11])},i(i){f||(r(l),r(h,i),f=!0)},o(i){c(l),c(h,i),f=!1},d(i){i&&S(e),l&&l.d(),h&&h.d(i)}}}function p(t,e,a){let{$$slots:o={},$$scope:f}=e,{equal_height:l=!0}=e,{elem_id:u}=e,{elem_classes:h=[]}=e,{visible:i=!0}=e,{variant:s="default"}=e,{loading_status:d=void 0}=e,{gradio:w=void 0}=e,{show_progress:b=!1}=e,{height:k}=e,{min_height:q}=e,{max_height:v}=e,{scale:j=null}=e;const C=n=>{if(n!==void 0){if(typeof n=="number")return n+"px";if(typeof n=="string")return n}};return t.$$set=n=>{"equal_height"in n&&a(0,l=n.equal_height),"elem_id"in n&&a(1,u=n.elem_id),"elem_classes"in n&&a(2,h=n.elem_classes),"visible"in n&&a(3,i=n.visible),"variant"in n&&a(4,s=n.variant),"loading_status"in n&&a(5,d=n.loading_status),"gradio"in n&&a(6,w=n.gradio),"show_progress"in n&&a(7,b=n.show_progress),"height"in n&&a(8,k=n.height),"min_height"in n&&a(9,q=n.min_height),"max_height"in n&&a(10,v=n.max_height),"scale"in n&&a(11,j=n.scale),"$$scope"in n&&a(13,f=n.$$scope)},[l,u,h,i,s,d,w,b,k,q,v,j,C,f,o]}class le extends D{constructor(e){super(),E(this,e,p,y,V,{equal_height:0,elem_id:1,elem_classes:2,visible:3,variant:4,loading_status:5,gradio:6,show_progress:7,height:8,min_height:9,max_height:10,scale:11})}}export{le as default};
//# sourceMappingURL=Index.MLf-ptpy.js.map
