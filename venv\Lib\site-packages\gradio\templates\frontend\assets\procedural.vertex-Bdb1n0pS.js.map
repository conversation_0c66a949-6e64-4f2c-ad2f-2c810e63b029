{"version": 3, "file": "procedural.vertex-Bdb1n0pS.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Shaders/procedural.vertex.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nconst name = \"proceduralVertexShader\";\nconst shader = `attribute vec2 position;varying vec2 vPosition;varying vec2 vUV;const vec2 madd=vec2(0.5,0.5);\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void) {\n#define CUSTOM_VERTEX_MAIN_BEGIN\nvPosition=position;vUV=position*madd+madd;gl_Position=vec4(position,0.0,1.0);\n#define CUSTOM_VERTEX_MAIN_END\n}`;\n// Sideeffect\nif (!ShaderStore.ShadersStore[name]) {\n    ShaderStore.ShadersStore[name] = shader;\n}\n/** @internal */\nexport const proceduralVertexShader = { name, shader };\n//# sourceMappingURL=procedural.vertex.js.map"], "names": ["name", "shader", "ShaderStore", "proceduralVert<PERSON><PERSON><PERSON><PERSON>"], "mappings": "+FAEA,MAAMA,EAAO,yBACPC,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAQVC,EAAY,aAAaF,CAAI,IAC9BE,EAAY,aAAaF,CAAI,EAAIC,GAGzB,MAACE,EAAyB,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}