import{SvelteComponent as Q,init as R,safe_not_equal as X,create_component as w,space as E,empty as z,claim_component as p,claim_space as N,mount_component as k,insert_hydration as v,group_outros as B,transition_out as b,check_outros as D,transition_in as m,detach as h,destroy_component as $,createEventDispatcher as Y,bubble as I,binding_callbacks as Z,element as S,claim_element as T,children as L,attr as V,toggle_class as y,append_hydration as q,listen as x}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{u as ee,r as ne,I as le}from"./2.B2AoQPnG.js";import{B as te}from"./BlockLabel.BTSz9r5s.js";import{E as oe}from"./Empty.DwQ6nkN6.js";import{S as re}from"./ShareButton.Be7APJkJ.js";import{D as ae}from"./Download.CpfEFmFf.js";import{I as P}from"./Image.CTVzPhL7.js";import{I as fe}from"./IconButtonWrapper.D5aGR59h.js";import{F as ie}from"./FullscreenButton.g_8wwg6y.js";import{g as se}from"./utils.Gtzs_Zla.js";import{D as ue}from"./DownloadLink.D1g3Q1HV.js";function ce(a){let n,l,e,o,t,i,u,f,_;return l=new fe({props:{display_top_corner:a[8],$$slots:{default:[be]},$$scope:{ctx:a}}}),i=new ne({props:{src:a[0].url,alt:"",loading:"lazy"}}),i.$on("load",a[16]),{c(){n=S("div"),w(l.$$.fragment),e=E(),o=S("button"),t=S("div"),w(i.$$.fragment),this.h()},l(r){n=T(r,"DIV",{class:!0});var c=L(n);p(l.$$.fragment,c),e=N(c),o=T(c,"BUTTON",{class:!0});var g=L(o);t=T(g,"DIV",{class:!0});var d=L(t);p(i.$$.fragment,d),d.forEach(h),g.forEach(h),c.forEach(h),this.h()},h(){V(t,"class","image-frame svelte-zxsjoa"),y(t,"selectable",a[4]),V(o,"class","svelte-zxsjoa"),V(n,"class","image-container svelte-zxsjoa")},m(r,c){v(r,n,c),k(l,n,null),q(n,e),q(n,o),q(o,t),k(i,t,null),a[17](n),u=!0,f||(_=x(o,"click",a[11]),f=!0)},p(r,c){const g={};c&256&&(g.display_top_corner=r[8]),c&525033&&(g.$$scope={dirty:c,ctx:r}),l.$set(g);const d={};c&1&&(d.src=r[0].url),i.$set(d),(!u||c&16)&&y(t,"selectable",r[4])},i(r){u||(m(l.$$.fragment,r),m(i.$$.fragment,r),u=!0)},o(r){b(l.$$.fragment,r),b(i.$$.fragment,r),u=!1},d(r){r&&h(n),$(l),$(i),a[17](null),f=!1,_()}}}function _e(a){let n,l;return n=new oe({props:{unpadded_box:!0,size:"large",$$slots:{default:[ge]},$$scope:{ctx:a}}}),{c(){w(n.$$.fragment)},l(e){p(n.$$.fragment,e)},m(e,o){k(n,e,o),l=!0},p(e,o){const t={};o&524288&&(t.$$scope={dirty:o,ctx:e}),n.$set(t)},i(e){l||(m(n.$$.fragment,e),l=!0)},o(e){b(n.$$.fragment,e),l=!1},d(e){$(n,e)}}}function C(a){let n,l;return n=new ie({props:{fullscreen:a[9]}}),n.$on("fullscreen",a[12]),{c(){w(n.$$.fragment)},l(e){p(n.$$.fragment,e)},m(e,o){k(n,e,o),l=!0},p(e,o){const t={};o&512&&(t.fullscreen=e[9]),n.$set(t)},i(e){l||(m(n.$$.fragment,e),l=!0)},o(e){b(n.$$.fragment,e),l=!1},d(e){$(n,e)}}}function H(a){let n,l;return n=new ue({props:{href:a[0].url,download:a[0].orig_name||"image",$$slots:{default:[me]},$$scope:{ctx:a}}}),{c(){w(n.$$.fragment)},l(e){p(n.$$.fragment,e)},m(e,o){k(n,e,o),l=!0},p(e,o){const t={};o&1&&(t.href=e[0].url),o&1&&(t.download=e[0].orig_name||"image"),o&524352&&(t.$$scope={dirty:o,ctx:e}),n.$set(t)},i(e){l||(m(n.$$.fragment,e),l=!0)},o(e){b(n.$$.fragment,e),l=!1},d(e){$(n,e)}}}function me(a){let n,l;return n=new le({props:{Icon:ae,label:a[6]("common.download")}}),{c(){w(n.$$.fragment)},l(e){p(n.$$.fragment,e)},m(e,o){k(n,e,o),l=!0},p(e,o){const t={};o&64&&(t.label=e[6]("common.download")),n.$set(t)},i(e){l||(m(n.$$.fragment,e),l=!0)},o(e){b(n.$$.fragment,e),l=!1},d(e){$(n,e)}}}function O(a){let n,l;return n=new re({props:{i18n:a[6],formatter:a[13],value:a[0]}}),n.$on("share",a[14]),n.$on("error",a[15]),{c(){w(n.$$.fragment)},l(e){p(n.$$.fragment,e)},m(e,o){k(n,e,o),l=!0},p(e,o){const t={};o&64&&(t.i18n=e[6]),o&1&&(t.value=e[0]),n.$set(t)},i(e){l||(m(n.$$.fragment,e),l=!0)},o(e){b(n.$$.fragment,e),l=!1},d(e){$(n,e)}}}function be(a){let n,l,e,o,t=a[7]&&C(a),i=a[3]&&H(a),u=a[5]&&O(a);return{c(){t&&t.c(),n=E(),i&&i.c(),l=E(),u&&u.c(),e=z()},l(f){t&&t.l(f),n=N(f),i&&i.l(f),l=N(f),u&&u.l(f),e=z()},m(f,_){t&&t.m(f,_),v(f,n,_),i&&i.m(f,_),v(f,l,_),u&&u.m(f,_),v(f,e,_),o=!0},p(f,_){f[7]?t?(t.p(f,_),_&128&&m(t,1)):(t=C(f),t.c(),m(t,1),t.m(n.parentNode,n)):t&&(B(),b(t,1,1,()=>{t=null}),D()),f[3]?i?(i.p(f,_),_&8&&m(i,1)):(i=H(f),i.c(),m(i,1),i.m(l.parentNode,l)):i&&(B(),b(i,1,1,()=>{i=null}),D()),f[5]?u?(u.p(f,_),_&32&&m(u,1)):(u=O(f),u.c(),m(u,1),u.m(e.parentNode,e)):u&&(B(),b(u,1,1,()=>{u=null}),D())},i(f){o||(m(t),m(i),m(u),o=!0)},o(f){b(t),b(i),b(u),o=!1},d(f){f&&(h(n),h(l),h(e)),t&&t.d(f),i&&i.d(f),u&&u.d(f)}}}function ge(a){let n,l;return n=new P({}),{c(){w(n.$$.fragment)},l(e){p(n.$$.fragment,e)},m(e,o){k(n,e,o),l=!0},i(e){l||(m(n.$$.fragment,e),l=!0)},o(e){b(n.$$.fragment,e),l=!1},d(e){$(n,e)}}}function de(a){let n,l,e,o,t,i;n=new te({props:{show_label:a[2],Icon:P,label:a[2]?a[1]||a[6]("image.image"):""}});const u=[_e,ce],f=[];function _(r,c){return r[0]===null||!r[0].url?0:1}return e=_(a),o=f[e]=u[e](a),{c(){w(n.$$.fragment),l=E(),o.c(),t=z()},l(r){p(n.$$.fragment,r),l=N(r),o.l(r),t=z()},m(r,c){k(n,r,c),v(r,l,c),f[e].m(r,c),v(r,t,c),i=!0},p(r,[c]){const g={};c&4&&(g.show_label=r[2]),c&70&&(g.label=r[2]?r[1]||r[6]("image.image"):""),n.$set(g);let d=e;e=_(r),e===d?f[e].p(r,c):(B(),b(f[d],1,1,()=>{f[d]=null}),D(),o=f[e],o?o.p(r,c):(o=f[e]=u[e](r),o.c()),m(o,1),o.m(t.parentNode,t))},i(r){i||(m(n.$$.fragment,r),m(o),i=!0)},o(r){b(n.$$.fragment,r),b(o),i=!1},d(r){r&&(h(l),h(t)),$(n,r),f[e].d(r)}}}function he(a,n,l){let{value:e}=n,{label:o=void 0}=n,{show_label:t}=n,{show_download_button:i=!0}=n,{selectable:u=!1}=n,{show_share_button:f=!1}=n,{i18n:_}=n,{show_fullscreen_button:r=!0}=n,{display_icon_button_wrapper_top_corner:c=!1}=n,{fullscreen:g=!1}=n;const d=Y(),U=s=>{let F=se(s);F&&d("select",{index:F,value:null})};let j;function W(s){I.call(this,a,s)}const A=async s=>s?`<img src="${await ee(s)}" />`:"";function G(s){I.call(this,a,s)}function J(s){I.call(this,a,s)}function K(s){I.call(this,a,s)}function M(s){Z[s?"unshift":"push"](()=>{j=s,l(10,j)})}return a.$$set=s=>{"value"in s&&l(0,e=s.value),"label"in s&&l(1,o=s.label),"show_label"in s&&l(2,t=s.show_label),"show_download_button"in s&&l(3,i=s.show_download_button),"selectable"in s&&l(4,u=s.selectable),"show_share_button"in s&&l(5,f=s.show_share_button),"i18n"in s&&l(6,_=s.i18n),"show_fullscreen_button"in s&&l(7,r=s.show_fullscreen_button),"display_icon_button_wrapper_top_corner"in s&&l(8,c=s.display_icon_button_wrapper_top_corner),"fullscreen"in s&&l(9,g=s.fullscreen)},[e,o,t,i,u,f,_,r,c,g,j,U,W,A,G,J,K,M]}class Fe extends Q{constructor(n){super(),R(this,n,he,de,X,{value:0,label:1,show_label:2,show_download_button:3,selectable:4,show_share_button:5,i18n:6,show_fullscreen_button:7,display_icon_button_wrapper_top_corner:8,fullscreen:9})}}export{Fe as default};
//# sourceMappingURL=ImagePreview.B6xPgKdu.js.map
