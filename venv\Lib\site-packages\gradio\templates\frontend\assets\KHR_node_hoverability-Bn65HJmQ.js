import{an as f,ao as b}from"./index-Dpxo-yl_.js";import{a as p}from"./declarationMapper-BZjsjg7g.js";import{A as m}from"./objectModelMapping-BR4RdEzn.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";const i="KHR_node_hoverability",d="targetMeshPointerOver_";p("event/onHoverIn",i,{blocks:["FlowGraphPointerOverEventBlock","FlowGraphGetVariableBlock","FlowGraphIndexOfBlock","KHR_interactivity/FlowGraphGLTFDataProvider"],configuration:{stopPropagation:{name:"stopPropagation"},nodeIndex:{name:"variable",toBlock:"FlowGraphGetVariableBlock",dataTransformer(e){return[d+e[0]]}}},outputs:{values:{hoverNodeIndex:{name:"index",toBlock:"FlowGraphIndexOfBlock"},controllerIndex:{name:"pointerId"}},flows:{out:{name:"done"}}},interBlockConnectors:[{input:"targetMesh",output:"value",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"array",output:"nodes",inputBlockIndex:2,outputBlockIndex:3,isVariable:!0},{input:"object",output:"meshUnderPointer",inputBlockIndex:2,outputBlockIndex:0,isVariable:!0}],extraProcessor(e,o,s,h,t,u,r){const a=t[t.length-1];a.config=a.config||{},a.config.glTF=r;const n=e.configuration?.nodeIndex?.value[0];if(n===void 0||typeof n!="number")throw new Error("nodeIndex not found in configuration");const l=d+n;return t[1].config.variable=l,u._userVariables[l]={className:"Mesh",id:r?.nodes?.[n]._babylonTransformNode?.id,uniqueId:r?.nodes?.[n]._babylonTransformNode?.uniqueId},t}});const c="targetMeshPointerOut_";p("event/onHoverOut",i,{blocks:["FlowGraphPointerOutEventBlock","FlowGraphGetVariableBlock","FlowGraphIndexOfBlock","KHR_interactivity/FlowGraphGLTFDataProvider"],configuration:{stopPropagation:{name:"stopPropagation"},nodeIndex:{name:"variable",toBlock:"FlowGraphGetVariableBlock",dataTransformer(e){return[c+e[0]]}}},outputs:{values:{hoverNodeIndex:{name:"index",toBlock:"FlowGraphIndexOfBlock"},controllerIndex:{name:"pointerId"}},flows:{out:{name:"done"}}},interBlockConnectors:[{input:"targetMesh",output:"value",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"array",output:"nodes",inputBlockIndex:2,outputBlockIndex:3,isVariable:!0},{input:"object",output:"meshOutOfPointer",inputBlockIndex:2,outputBlockIndex:0,isVariable:!0}],extraProcessor(e,o,s,h,t,u,r){const a=t[t.length-1];a.config=a.config||{},a.config.glTF=r;const n=e.configuration?.nodeIndex?.value[0];if(n===void 0||typeof n!="number")throw new Error("nodeIndex not found in configuration");const l=c+n;return t[1].config.variable=l,u._userVariables[l]={className:"Mesh",id:r?.nodes?.[n]._babylonTransformNode?.id,uniqueId:r?.nodes?.[n]._babylonTransformNode?.uniqueId},t}});m("/nodes/{}/extensions/KHR_node_hoverability/hoverable",{get:e=>{const o=e._babylonTransformNode;return o&&o.pointerOverDisableMeshTesting!==void 0?o.pointerOverDisableMeshTesting:!0},set:(e,o)=>{o._primitiveBabylonMeshes?.forEach(s=>{s.pointerOverDisableMeshTesting=!e})},getTarget:e=>e._babylonTransformNode,getPropertyName:[()=>"pointerOverDisableMeshTesting"],type:"boolean"});class v{constructor(o){this.name=i,this._loader=o,this.enabled=o.isExtensionUsed(i)}async onReady(){this._loader.gltf.nodes?.forEach(o=>{o.extensions?.KHR_node_hoverability&&o.extensions?.KHR_node_hoverability.hoverable===!1&&o._babylonTransformNode?.getChildMeshes().forEach(s=>{s.pointerOverDisableMeshTesting=!0})})}dispose(){this._loader=null}}f(i);b(i,!0,e=>new v(e));export{v as KHR_node_hoverability};
//# sourceMappingURL=KHR_node_hoverability-Bn65HJmQ.js.map
