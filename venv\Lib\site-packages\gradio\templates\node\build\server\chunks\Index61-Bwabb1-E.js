import { c as create_ssr_component, v as validate_component, s as subscribe, b as createEventDispatcher, e as escape, d as add_attribute, f as each, k as setContext, m as missing_component, o as onDestroy, j as null_to_empty, l as get_store_value } from './ssr-C3HYbsxA.js';
import { t as tick } from './Component-NmRBwSfF.js';
import { m as mt, z as zA, E as An$1, F as Lt, G as Gt, l as Le } from './2-DJbI4FWc.js';
import { u as u$1 } from './index8-DWEiEAhs.js';
import { w as writable } from './index-ClteBeTX.js';
import { u } from './index7-DcVoQjit.js';
import { g as ge$1 } from './ModifyUpload-wYzp15o5.js';
import { BaseCheckbox as K } from './Index40-7r90nnuu.js';
import Rt from './Index26-R8oUQBBK.js';
export { default as BaseExample } from './Example9-FhoLJ9wH.js';
import 'path';
import 'url';
import 'fs';
import './ImagePreview-dCWEyjhO.js';
import './Example10-DN5tOkvr.js';

function ve(n,e,t){if(!e.length)return "none";const a=e.find(A=>{const d=A.col;return d<0||d>=t.length?!1:t[d]===n});return a?a.direction:"none"}function nn(n,e){if(!n||!n.length||!n[0])return [];if(e.length>0){const t=[...Array(n.length)].map((a,A)=>A);return t.sort((a,A)=>{const d=n[a],l=n[A];for(const{col:o,direction:s}of e){if(!d||!l||o<0||o>=d.length||o>=l.length||!d[o]||!l[o])continue;const i=d[o].value,r=l[o].value,_=i<r?-1:i>r?1:0;if(_!==0)return s==="asc"?_:-_}return 0}),t}return [...Array(n.length)].map((t,a)=>a)}function ln(n,e,t,a,A,d){let l=null;A&&A[0]in n&&A[1]in n[A[0]]&&(l=n[A[0]][A[1]].id),ge(n,e,t,a);let o=A;if(l){const[s,i]=d(l,n);o=[s,i];}return {data:n,selected:o}}function An(n,e){if(!n||!n.length||!n[0])return [];let t=[...Array(n.length)].map((a,A)=>A);return e.length>0?(e.forEach(a=>{if(a.datatype==="string")switch(a.filter){case"Contains":t=t.filter(A=>n[A][a.col]?.value.toString().includes(a.value));break;case"Does not contain":t=t.filter(A=>!n[A][a.col]?.value.toString().includes(a.value));break;case"Starts with":t=t.filter(A=>n[A][a.col]?.value.toString().startsWith(a.value));break;case"Ends with":t=t.filter(A=>n[A][a.col]?.value.toString().endsWith(a.value));break;case"Is":t=t.filter(A=>n[A][a.col]?.value.toString()===a.value);break;case"Is not":t=t.filter(A=>n[A][a.col]?.value.toString()!==a.value);break;case"Is empty":t=t.filter(A=>n[A][a.col]?.value.toString()==="");break;case"Is not empty":t=t.filter(A=>n[A][a.col]?.value.toString()!=="");break}else if(a.datatype==="number")switch(a.filter){case"=":t=t.filter(A=>!isNaN(Number(n[A][a.col]?.value))&&!isNaN(Number(a.value))?Number(n[A][a.col]?.value)===Number(a.value):!1);break;case"≠":t=t.filter(A=>!isNaN(Number(n[A][a.col]?.value))&&!isNaN(Number(a.value))?Number(n[A][a.col]?.value)!==Number(a.value):!1);break;case">":t=t.filter(A=>!isNaN(Number(n[A][a.col]?.value))&&!isNaN(Number(a.value))?Number(n[A][a.col]?.value)>Number(a.value):!1);break;case"<":t=t.filter(A=>!isNaN(Number(n[A][a.col]?.value))&&!isNaN(Number(a.value))?Number(n[A][a.col]?.value)<Number(a.value):!1);break;case"≥":t=t.filter(A=>!isNaN(Number(n[A][a.col]?.value))&&!isNaN(Number(a.value))?Number(n[A][a.col]?.value)>=Number(a.value):!1);break;case"≤":t=t.filter(A=>!isNaN(Number(n[A][a.col]?.value))&&!isNaN(Number(a.value))?Number(n[A][a.col]?.value)<=Number(a.value):!1);break;case"Is empty":t=t.filter(A=>n[A][a.col]?.value.toString()==="");break;case"Is not empty":t=t.filter(A=>isNaN(Number(n[A][a.col]?.value))?!1:n[A][a.col]?.value.toString()!=="");break}}),t):[...Array(n.length)].map((a,A)=>A)}function on(n,e,t,a,A,d,l,o,s){let i=null;A&&A[0]in n&&A[1]in n[A[0]]&&(i=n[A[0]][A[1]].id),an(n,e,t,a,l,o,s);let r=A;if(i){const[_,C]=d(i,n);r=[_,C];}return {data:n,selected:r}}function rn(n){if(!n||!n.length)return [];let e=n[0].slice();for(let t=0;t<n.length;t++)for(let a=0;a<n[t].length;a++)`${e[a].value}`.length<`${n[t][a].value}`.length&&(e[a]=n[t][a]);return e}function ge(n,e,t,a){if(!a.length||!n||!n.length)return;const A=nn(n,a),d=A.map(l=>n[l]);if(n.splice(0,n.length,...d),e){const l=A.map(o=>e[o]);e.splice(0,e.length,...l);}if(t){const l=A.map(o=>t[o]);t.splice(0,t.length,...l);}}function an(n,e,t,a,A,d,l){const o=A??n,s=d??e,i=l??t;if(!a.length){n.splice(0,n.length,...o.map(C=>[...C])),e&&s&&e.splice(0,e.length,...s.map(C=>[...C])),t&&i&&t.splice(0,t.length,...i.map(C=>[...C]));return}if(!n||!n.length)return;const r=An(o,a),_=r.map(C=>o[C]);if(n.splice(0,n.length,..._),e&&s){const C=r.map(p=>s[p]);e.splice(0,e.length,...C);}if(t&&i){const C=r.map(p=>i[p]);t.splice(0,t.length,...C);}}async function sn(n,e){if(!n||!n.length)return;const a=n.flatMap((o,s)=>o.map((i,r)=>[s,r])).reduce((o,[s,i])=>{o[s]=o[s]||{};const r=String(n[s][i].value);return o[s][i]=r.includes(",")||r.includes('"')||r.includes(`
`)?`"${r.replace(/"/g,'""')}"`:r,o},{}),A=Object.keys(a).sort((o,s)=>+o-+s);if(!A.length)return;const d=Object.keys(a[A[0]]).sort((o,s)=>+o-+s),l=A.map(o=>d.map(s=>a[o][s]||"").join(",")).join(`
`);try{await navigator.clipboard.writeText(l);}catch(o){throw new Error("Failed to copy to clipboard: "+o.message)}}function cn(n,e){const[t,a]=n;return e.some(([A,d])=>A===t&&d===a)}function dn(n,e){const[t,a]=n;if(!e.some(([s,i])=>s===t&&i===a))return "";const A=e.some(([s,i])=>s===t-1&&i===a),d=e.some(([s,i])=>s===t+1&&i===a),l=e.some(([s,i])=>s===t&&i===a-1),o=e.some(([s,i])=>s===t&&i===a+1);return `cell-selected${A?" no-top":""}${d?" no-bottom":""}${l?" no-left":""}${o?" no-right":""}`}function Nt(n,e){const[t,a]=n,[A,d]=e,l=Math.min(t,A),o=Math.max(t,A),s=Math.min(a,d),i=Math.max(a,d),r=[];r.push(n);for(let _=l;_<=o;_++)for(let C=s;C<=i;C++)_===t&&C===a||r.push([_,C]);return r}function Cn(n,e,t){if(t.shiftKey&&e.length>0)return Nt(e[e.length-1],n);if(t.metaKey||t.ctrlKey){const a=([d,l])=>d===n[0]&&l===n[1],A=e.findIndex(a);return A===-1?[...e,n]:e.filter((d,l)=>l!==A)}return [n]}function _n(n,e,t){const[a,A]=n;return t&&e.length===1&&e[0][0]===a&&e[0][1]===A}function un(n,e,t){const[a,A]=n,d=t?-1:1;if(e[a]?.[A+d])return [a,A+d];const l=a+(d>0?1:0),o=a+(d<0?-1:0);return d>0&&e[l]?.[0]?[l,0]:d<0&&e[o]?.[e[0].length-1]?[o,e[0].length-1]:!1}function vn(n,e,t){const a=n.key,A={ArrowRight:[0,1],ArrowLeft:[0,-1],ArrowDown:[1,0],ArrowUp:[-1,0]}[a];let d,l;if(n.metaKey||n.ctrlKey)if(a==="ArrowRight")d=e[0],l=t[0].length-1;else if(a==="ArrowLeft")d=e[0],l=0;else if(a==="ArrowDown")d=t.length-1,l=e[1];else if(a==="ArrowUp")d=0,l=e[1];else return !1;else d=e[0]+A[0],l=e[1]+A[1];return d<0&&l<=0?!1:t[d]?.[l]?[d,l]:!1}function he(n,e){return e.reduce((t,a,A)=>{const d=a.reduce((l,o,s)=>n===o.id?s:l,-1);return d===-1?t:[A,d]},[-1,-1])}function fn(n,e,t,a,A){const[d,l]=n;if(!e[d]?.[l])return {col_pos:"0px",row_pos:void 0};const o=e[d][l].id,s=t[o]?.cell;if(!s)return {col_pos:"0px",row_pos:void 0};const i=s.getBoundingClientRect(),r=A.getBoundingClientRect(),_=`${i.left-r.left+i.width/2}px`,C=`${i.top-r.top+i.height/2}px`;return {col_pos:_,row_pos:C}}const mn=Symbol("dataframe");function pn(n,e){const t=l=>n.update(o=>({...o,...l(o)})),a=(l,o,s)=>{const i=l[0]?.length?Array(l[0].length).fill(null).map(()=>({value:"",id:o()})):[{value:"",id:o()}],r=[...l];return s!==void 0?r.splice(s,0,i):r.push(i),r},A=(l,o,s,i)=>{const r=e.headers?[...o.map(C=>e.headers[o.indexOf(C)].value)]:[...o,`Header ${o.length+1}`],_=l.map(C=>[...C,{value:"",id:s()}]);return i!==void 0&&(r.splice(i,0,r.pop()),_.forEach(C=>C.splice(i,0,C.pop()))),{data:_,headers:r}},d=(l,o)=>{l&&o&&o.splice(0,o.length,...JSON.parse(JSON.stringify(l)));};return {handle_search:l=>t(o=>({current_search_query:l})),handle_sort:(l,o)=>t(s=>{const i=s.sort_state.sort_columns.filter(_=>_.col!==l);s.sort_state.sort_columns.some(_=>_.col===l&&_.direction===o)||i.push({col:l,direction:o});const r=s.sort_state.initial_data||(e.data&&i.length>0?{data:JSON.parse(JSON.stringify(e.data)),display_value:e.display_value?JSON.parse(JSON.stringify(e.display_value)):null,styling:e.styling?JSON.parse(JSON.stringify(e.styling)):null}:null);return {sort_state:{...s.sort_state,sort_columns:i.slice(-3),initial_data:r}}}),handle_filter:(l,o,s,i)=>t(r=>{const _=r.filter_state.filter_columns.some(p=>p.col===l)?r.filter_state.filter_columns.filter(p=>p.col!==l):[...r.filter_state.filter_columns,{col:l,datatype:o,filter:s,value:i}],C=r.filter_state.initial_data||(e.data&&_.length>0?{data:JSON.parse(JSON.stringify(e.data)),display_value:e.display_value?JSON.parse(JSON.stringify(e.display_value)):null,styling:e.styling?JSON.parse(JSON.stringify(e.styling)):null}:null);return {filter_state:{...r.filter_state,filter_columns:_,initial_data:C}}}),get_sort_status:(l,o)=>{const i=get_store_value(n).sort_state.sort_columns.find(r=>o[r.col]===l);return i?i.direction:"none"},sort_data:(l,o,s)=>{const{sort_state:{sort_columns:i}}=get_store_value(n);i.length&&ge(l,o,s,i);},update_row_order:l=>t(o=>({sort_state:{...o.sort_state,row_order:o.sort_state.sort_columns.length&&l[0]?[...Array(l.length)].map((s,i)=>i).sort((s,i)=>{for(const{col:r,direction:_}of o.sort_state.sort_columns){const C=(l[s]?.[r]?.value??"")<(l[i]?.[r]?.value??"")?-1:1;if(C)return _==="asc"?C:-C}return 0}):[...Array(l.length)].map((s,i)=>i)}})),filter_data:l=>{const o=get_store_value(n).current_search_query?.toLowerCase();return o?l.filter(s=>s.some(i=>String(i?.value).toLowerCase().includes(o))):l},add_row:a,add_col:A,add_row_at:(l,o,s,i)=>a(l,i,s==="above"?o:o+1),add_col_at:(l,o,s,i,r)=>A(l,o,r,i==="left"?s:s+1),delete_row:(l,o)=>l.length>1?l.filter((s,i)=>i!==o):l,delete_col:(l,o,s)=>o.length>1?{data:l.map(i=>i.filter((r,_)=>_!==s)),headers:o.filter((i,r)=>r!==s)}:{data:l,headers:o},delete_row_at:(l,o)=>l.length>1?[...l.slice(0,o),...l.slice(o+1)]:l,delete_col_at:(l,o,s)=>o.length>1?{data:l.map(i=>[...i.slice(0,s),...i.slice(s+1)]),headers:[...o.slice(0,s),...o.slice(s+1)]}:{data:l,headers:o},trigger_change:async(l,o,s,i,r,_)=>{if(get_store_value(n).current_search_query)return;const p=o.map(k=>k.value),b=l.map(k=>k.map(S=>String(S.value)));(!u$1(b,s)||!u$1(p,i))&&(u$1(p,i)||t(k=>({sort_state:{sort_columns:[],row_order:[],initial_data:null},filter_state:{filter_columns:[],initial_data:null}})),_("change",{data:l.map(k=>k.map(S=>S.value)),headers:p,metadata:null}),r||_("input"));},reset_sort_state:()=>t(l=>{if(l.sort_state.initial_data&&e.data){const o=l.sort_state.initial_data;d(o.data,e.data),d(o.display_value,e.display_value),d(o.styling,e.styling);}return {sort_state:{sort_columns:[],row_order:[],initial_data:null}}}),reset_filter_state:()=>t(l=>{if(l.filter_state.initial_data&&e.data){const o=l.filter_state.initial_data;d(o.data,e.data),d(o.display_value,e.display_value),d(o.styling,e.styling);}return {filter_state:{filter_columns:[],initial_data:null}}}),set_active_cell_menu:l=>t(o=>({ui_state:{...o.ui_state,active_cell_menu:l}})),set_active_header_menu:l=>t(o=>({ui_state:{...o.ui_state,active_header_menu:l}})),set_selected_cells:l=>t(o=>({ui_state:{...o.ui_state,selected_cells:l}})),set_selected:l=>t(o=>({ui_state:{...o.ui_state,selected:l}})),set_editing:l=>t(o=>({ui_state:{...o.ui_state,editing:l}})),clear_ui_state:()=>t(l=>({ui_state:{active_cell_menu:null,active_header_menu:null,selected_cells:[],selected:!1,editing:!1,header_edit:!1,selected_header:!1,active_button:null,copy_flash:!1}})),set_header_edit:l=>t(o=>({ui_state:{...o.ui_state,selected_cells:[],selected_header:l,header_edit:l}})),set_selected_header:l=>t(o=>({ui_state:{...o.ui_state,selected_header:l,selected:!1,selected_cells:[]}})),handle_header_click:(l,o)=>t(s=>({ui_state:{...s.ui_state,active_cell_menu:null,active_header_menu:null,selected:!1,selected_cells:[],selected_header:l,header_edit:o?l:!1}})),end_header_edit:l=>{["Escape","Enter","Tab"].includes(l)&&t(o=>({ui_state:{...o.ui_state,selected:!1,header_edit:!1}}));},get_selected_cells:()=>get_store_value(n).ui_state.selected_cells,get_active_cell_menu:()=>get_store_value(n).ui_state.active_cell_menu,get_active_button:()=>get_store_value(n).ui_state.active_button,set_active_button:l=>t(o=>({ui_state:{...o.ui_state,active_button:l}})),set_copy_flash:l=>t(o=>({ui_state:{...o.ui_state,copy_flash:l}})),handle_cell_click:(l,o,s)=>{l.preventDefault(),l.stopPropagation();const i=get_store_value(n);if(i.config.show_row_numbers&&s===-1)return;let r=o;if(i.current_search_query&&e.data){const C=[];e.data.forEach((p,b)=>{p.some(k=>String(k?.value).toLowerCase().includes(i.current_search_query?.toLowerCase()||""))&&C.push(b);}),r=C[o]??o;}const _=Cn([r,s],i.ui_state.selected_cells,l);t(C=>({ui_state:{...C.ui_state,active_cell_menu:null,active_header_menu:null,selected_header:!1,header_edit:!1,selected_cells:_,selected:_[0]}})),i.config.editable&&_.length===1?(t(C=>({ui_state:{...C.ui_state,editing:[r,s]}})),tick().then(()=>e.els[e.data[r][s].id]?.input?.focus())):tick().then(()=>{e.parent_element&&e.parent_element.focus();}),e.dispatch?.("select",{index:[r,s],col_value:e.get_column(s),row_value:e.get_row(r),value:e.get_data_at(r,s)});},toggle_cell_menu:(l,o,s)=>{l.stopPropagation();const i=get_store_value(n).ui_state.active_cell_menu;if(i?.row===o&&i.col===s)t(r=>({ui_state:{...r.ui_state,active_cell_menu:null}}));else {const r=l.target.closest("td");if(r){const _=r.getBoundingClientRect();t(C=>({ui_state:{...C.ui_state,active_cell_menu:{row:o,col:s,x:_.right,y:_.bottom}}}));}}},toggle_cell_button:(l,o)=>{const s=get_store_value(n).ui_state.active_button,i=s?.type==="cell"&&s.row===l&&s.col===o?null:{type:"cell",row:l,col:o};t(r=>({ui_state:{...r.ui_state,active_button:i}}));},handle_select_column:l=>{if(!e.data)return;const o=e.data.map((s,i)=>[i,l]);t(s=>({ui_state:{...s.ui_state,selected_cells:o,selected:o[0],editing:!1}})),setTimeout(()=>e.parent_element?.focus(),0);},handle_select_row:l=>{if(!e.data||!e.data[0])return;const o=e.data[0].map((s,i)=>[l,i]);t(s=>({ui_state:{...s.ui_state,selected_cells:o,selected:o[0],editing:!1}})),setTimeout(()=>e.parent_element?.focus(),0);},get_next_cell_coordinates:un,get_range_selection:Nt,move_cursor:vn}}function gn(n){const e=writable({config:n,current_search_query:null,sort_state:{sort_columns:[],row_order:[],initial_data:null},filter_state:{filter_columns:[],initial_data:null},ui_state:{active_cell_menu:null,active_header_menu:null,selected_cells:[],selected:!1,editing:!1,header_edit:!1,selected_header:!1,active_button:null,copy_flash:!1}}),t={state:e,actions:null};t.actions=pn(e,t);const a=Symbol(`dataframe_${Math.random().toString(36).substring(2)}`);return setContext(a,t),setContext(mn,{instance_id:a,context:t}),t}const bn={code:".selection-button.svelte-1mp8yw1{position:absolute;background:var(--color-accent);width:var(--size-3);height:var(--size-5);color:var(--background-fill-primary)}.selection-button-column.svelte-1mp8yw1{top:-15px;left:50%;transform:translateX(-50%) rotate(90deg);border-radius:var(--radius-sm) 0 0 var(--radius-sm)}.selection-button-row.svelte-1mp8yw1{left:calc(var(--size-2-5) * -1);border-radius:var(--radius-sm) 0 0 var(--radius-sm)}.move-down.svelte-1mp8yw1{bottom:-14px;top:auto;border-radius:0 var(--radius-sm) var(--radius-sm) 0}.move-right.svelte-1mp8yw1{left:auto;right:calc(var(--size-2-5) * -1);border-radius:0 var(--radius-sm) var(--radius-sm) 0}svg.svelte-1mp8yw1{fill:currentColor}span.svelte-1mp8yw1{display:flex;width:100%;height:100%}.up.svelte-1mp8yw1{transform:rotate(-90deg)}.down.svelte-1mp8yw1{transform:rotate(90deg)}.left.svelte-1mp8yw1{transform:rotate(-90deg)}.right.svelte-1mp8yw1{transform:rotate(90deg)}",map:'{"version":3,"file":"SelectionButtons.svelte","sources":["SelectionButtons.svelte"],"sourcesContent":["<script lang=\\"ts\\">export let position;\\nexport let coords;\\nexport let on_click = null;\\n$: is_first_position = position === \\"column\\" ? coords[0] === 0 : coords[1] === 0;\\n$: direction = position === \\"column\\" ? is_first_position ? \\"down\\" : \\"up\\" : is_first_position ? \\"right\\" : \\"left\\";\\n<\/script>\\n\\n<button\\n\\tclass=\\"selection-button selection-button-{position} {is_first_position\\n\\t\\t? `move-${direction}`\\n\\t\\t: \'\'}\\"\\n\\ton:click|stopPropagation={() => on_click && on_click()}\\n\\taria-label={`Select ${position}`}\\n>\\n\\t<span class={direction}>\\n\\t\\t<svg xmlns=\\"http://www.w3.org/2000/svg\\" viewBox=\\"0 0 24 24\\">\\n\\t\\t\\t<path\\n\\t\\t\\t\\td=\\"m16.707 13.293-4-4a1 1 0 0 0-1.414 0l-4 4A1 1 0 0 0 8 15h8a1 1 0 0 0 .707-1.707z\\"\\n\\t\\t\\t\\tdata-name={direction}\\n\\t\\t\\t/>\\n\\t\\t</svg>\\n\\t</span>\\n</button>\\n\\n<style>\\n\\t.selection-button {\\n\\t\\tposition: absolute;\\n\\t\\tbackground: var(--color-accent);\\n\\t\\twidth: var(--size-3);\\n\\t\\theight: var(--size-5);\\n\\t\\tcolor: var(--background-fill-primary);\\n\\t}\\n\\n\\t.selection-button-column {\\n\\t\\ttop: -15px;\\n\\t\\tleft: 50%;\\n\\t\\ttransform: translateX(-50%) rotate(90deg);\\n\\t\\tborder-radius: var(--radius-sm) 0 0 var(--radius-sm);\\n\\t}\\n\\n\\t.selection-button-row {\\n\\t\\tleft: calc(var(--size-2-5) * -1);\\n\\t\\tborder-radius: var(--radius-sm) 0 0 var(--radius-sm);\\n\\t}\\n\\n\\t.move-down {\\n\\t\\tbottom: -14px;\\n\\t\\ttop: auto;\\n\\t\\tborder-radius: 0 var(--radius-sm) var(--radius-sm) 0;\\n\\t}\\n\\n\\t.move-right {\\n\\t\\tleft: auto;\\n\\t\\tright: calc(var(--size-2-5) * -1);\\n\\t\\tborder-radius: 0 var(--radius-sm) var(--radius-sm) 0;\\n\\t}\\n\\n\\tsvg {\\n\\t\\tfill: currentColor;\\n\\t}\\n\\n\\tspan {\\n\\t\\tdisplay: flex;\\n\\t\\twidth: 100%;\\n\\t\\theight: 100%;\\n\\t}\\n\\n\\t.up {\\n\\t\\ttransform: rotate(-90deg);\\n\\t}\\n\\n\\t.down {\\n\\t\\ttransform: rotate(90deg);\\n\\t}\\n\\n\\t.left {\\n\\t\\ttransform: rotate(-90deg);\\n\\t}\\n\\n\\t.right {\\n\\t\\ttransform: rotate(90deg);\\n\\t}</style>\\n"],"names":[],"mappings":"AAyBC,gCAAkB,CACjB,QAAQ,CAAE,QAAQ,CAClB,UAAU,CAAE,IAAI,cAAc,CAAC,CAC/B,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,yBAAyB,CACrC,CAEA,uCAAyB,CACxB,GAAG,CAAE,KAAK,CACV,IAAI,CAAE,GAAG,CACT,SAAS,CAAE,WAAW,IAAI,CAAC,CAAC,OAAO,KAAK,CAAC,CACzC,aAAa,CAAE,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,CACpD,CAEA,oCAAsB,CACrB,IAAI,CAAE,KAAK,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAChC,aAAa,CAAE,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,CACpD,CAEA,yBAAW,CACV,MAAM,CAAE,KAAK,CACb,GAAG,CAAE,IAAI,CACT,aAAa,CAAE,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,CACpD,CAEA,0BAAY,CACX,IAAI,CAAE,IAAI,CACV,KAAK,CAAE,KAAK,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CACjC,aAAa,CAAE,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,CACpD,CAEA,kBAAI,CACH,IAAI,CAAE,YACP,CAEA,mBAAK,CACJ,OAAO,CAAE,IAAI,CACb,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CAEA,kBAAI,CACH,SAAS,CAAE,OAAO,MAAM,CACzB,CAEA,oBAAM,CACL,SAAS,CAAE,OAAO,KAAK,CACxB,CAEA,oBAAM,CACL,SAAS,CAAE,OAAO,MAAM,CACzB,CAEA,qBAAO,CACN,SAAS,CAAE,OAAO,KAAK,CACxB"}'},fe=create_ssr_component((n,e,t,a)=>{let A,d,{position:l}=e,{coords:o}=e,{on_click:s=null}=e;return e.position===void 0&&t.position&&l!==void 0&&t.position(l),e.coords===void 0&&t.coords&&o!==void 0&&t.coords(o),e.on_click===void 0&&t.on_click&&s!==void 0&&t.on_click(s),n.css.add(bn),A=l==="column"?o[0]===0:o[1]===0,d=l==="column"?A?"down":"up":A?"right":"left",`<button class="${"selection-button selection-button-"+escape(l,!0)+" "+escape(A?`move-${d}`:"",!0)+" svelte-1mp8yw1"}"${add_attribute("aria-label",`Select ${l}`,0)}><span class="${escape(null_to_empty(d),!0)+" svelte-1mp8yw1"}"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="svelte-1mp8yw1"><path d="m16.707 13.293-4-4a1 1 0 0 0-1.414 0l-4 4A1 1 0 0 0 8 15h8a1 1 0 0 0 .707-1.707z"${add_attribute("data-name",d,0)}></path></svg></span> </button>`}),wn={code:".bool-cell.svelte-1svo4lb{display:flex;align-items:center;justify-content:center;width:min-content;height:var(--size-full);margin:0 auto}.bool-cell.svelte-1svo4lb input:disabled{cursor:not-allowed}.bool-cell.svelte-1svo4lb label{margin:0;width:100%;display:flex;justify-content:center;align-items:center}.bool-cell.svelte-1svo4lb span{display:none}",map:'{"version":3,"file":"BooleanCell.svelte","sources":["BooleanCell.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { BaseCheckbox } from \\"@gradio/checkbox\\";\\nexport let value = false;\\nexport let editable = true;\\nexport let on_change;\\n$: bool_value = typeof value === \\"string\\" ? value.toLowerCase() === \\"true\\" : !!value;\\nfunction handle_change(event) {\\n    if (editable) {\\n        on_change(event.detail);\\n    }\\n}\\n<\/script>\\n\\n<div class=\\"bool-cell\\" role=\\"button\\" tabindex=\\"-1\\">\\n\\t<BaseCheckbox\\n\\t\\tbind:value={bool_value}\\n\\t\\tlabel=\\"\\"\\n\\t\\tinteractive={editable}\\n\\t\\ton:change={handle_change}\\n\\t/>\\n</div>\\n\\n<style>\\n\\t.bool-cell {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tjustify-content: center;\\n\\t\\twidth: min-content;\\n\\t\\theight: var(--size-full);\\n\\t\\tmargin: 0 auto;\\n\\t}\\n\\n\\t.bool-cell :global(input:disabled) {\\n\\t\\tcursor: not-allowed;\\n\\t}\\n\\n\\t.bool-cell :global(label) {\\n\\t\\tmargin: 0;\\n\\t\\twidth: 100%;\\n\\t\\tdisplay: flex;\\n\\t\\tjustify-content: center;\\n\\t\\talign-items: center;\\n\\t}\\n\\n\\t.bool-cell :global(span) {\\n\\t\\tdisplay: none;\\n\\t}</style>\\n"],"names":[],"mappings":"AAsBC,yBAAW,CACV,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,KAAK,CAAE,WAAW,CAClB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,MAAM,CAAE,CAAC,CAAC,IACX,CAEA,yBAAU,CAAS,cAAgB,CAClC,MAAM,CAAE,WACT,CAEA,yBAAU,CAAS,KAAO,CACzB,MAAM,CAAE,CAAC,CACT,KAAK,CAAE,IAAI,CACX,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MACd,CAEA,yBAAU,CAAS,IAAM,CACxB,OAAO,CAAE,IACV"}'},yn=create_ssr_component((n,e,t,a)=>{let A,{value:d=!1}=e,{editable:l=!0}=e,{on_change:o}=e;e.value===void 0&&t.value&&d!==void 0&&t.value(d),e.editable===void 0&&t.editable&&l!==void 0&&t.editable(l),e.on_change===void 0&&t.on_change&&o!==void 0&&t.on_change(o),n.css.add(wn);let s,i,r=n.head;do s=!0,n.head=r,A=typeof d=="string"?d.toLowerCase()==="true":!!d,i=`<div class="bool-cell svelte-1svo4lb" role="button" tabindex="-1">${validate_component(K,"BaseCheckbox").$$render(n,{label:"",interactive:l,value:A},{value:_=>{A=_,s=!1;}},{})} </div>`;while(!s);return i}),xn={code:".dragging.svelte-fvkwu{cursor:crosshair !important}textarea.svelte-fvkwu{position:absolute;flex:1 1 0%;transform:translateX(-0.1px);outline:none;border:none;background:transparent;cursor:text;width:calc(100% - var(--size-2));resize:none;height:100%;padding-left:0;font-size:inherit;font-weight:inherit;line-height:var(--line-lg)}textarea.svelte-fvkwu:focus{outline:none}span.svelte-fvkwu{flex:1 1 0%;position:relative;display:inline-block;outline:none;-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text;cursor:text;width:100%;height:100%;overflow:hidden}span.text.expanded.svelte-fvkwu{height:auto;min-height:100%;white-space:pre-wrap;word-break:break-word;overflow:visible}.multiline.svelte-fvkwu{white-space:pre;overflow:hidden;text-overflow:ellipsis}.header.svelte-fvkwu{transform:translateX(0);font-weight:var(--weight-bold);white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-left:var(--size-1)}.edit.svelte-fvkwu{opacity:0;pointer-events:none}span.svelte-fvkwu img{max-height:100px;width:auto;object-fit:contain}textarea.svelte-fvkwu:read-only{cursor:not-allowed}.wrap.svelte-fvkwu,.wrap.expanded.svelte-fvkwu{white-space:normal;word-wrap:break-word;overflow-wrap:break-word;word-wrap:break-word}",map:'{"version":3,"file":"EditableCell.svelte","sources":["EditableCell.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { createEventDispatcher } from \\"svelte\\";\\nimport { MarkdownCode } from \\"@gradio/markdown-code\\";\\nimport SelectionButtons from \\"./icons/SelectionButtons.svelte\\";\\nimport BooleanCell from \\"./BooleanCell.svelte\\";\\nexport let edit;\\nexport let value = \\"\\";\\nexport let display_value = null;\\nexport let styling = \\"\\";\\nexport let header = false;\\nexport let datatype = \\"str\\";\\nexport let latex_delimiters;\\nexport let line_breaks = true;\\nexport let editable = true;\\nexport let is_static = false;\\nexport let max_chars = null;\\nexport let components = {};\\nexport let i18n;\\nexport let is_dragging = false;\\nexport let wrap_text = false;\\nexport let show_selection_buttons = false;\\nexport let coords;\\nexport let on_select_column = null;\\nexport let on_select_row = null;\\nexport let el;\\nconst dispatch = createEventDispatcher();\\nfunction truncate_text(text, max_length = null, is_image = false) {\\n    if (is_image)\\n        return String(text);\\n    const str = String(text);\\n    if (!max_length || max_length <= 0)\\n        return str;\\n    if (str.length <= max_length)\\n        return str;\\n    return str.slice(0, max_length) + \\"...\\";\\n}\\n$: should_truncate = !edit && max_chars !== null && max_chars > 0;\\n$: display_content = editable ? value : display_value !== null ? display_value : value;\\n$: display_text = should_truncate ? truncate_text(display_content, max_chars, datatype === \\"image\\") : display_content;\\nfunction use_focus(node) {\\n    requestAnimationFrame(() => {\\n        node.focus();\\n    });\\n    return {};\\n}\\nfunction handle_blur(event) {\\n    dispatch(\\"blur\\", {\\n        blur_event: event,\\n        coords\\n    });\\n}\\nfunction handle_keydown(event) {\\n    dispatch(\\"keydown\\", event);\\n}\\nfunction handle_bool_change(new_value) {\\n    value = new_value.toString();\\n    dispatch(\\"blur\\", {\\n        blur_event: {\\n            target: {\\n                type: \\"checkbox\\",\\n                checked: new_value,\\n                value: new_value.toString()\\n            }\\n        },\\n        coords\\n    });\\n}\\n<\/script>\\n\\n{#if edit && datatype !== \\"bool\\"}\\n\\t<textarea\\n\\t\\treadonly={is_static}\\n\\t\\taria-readonly={is_static}\\n\\t\\taria-label={is_static ? \\"Cell is read-only\\" : \\"Edit cell\\"}\\n\\t\\tbind:this={el}\\n\\t\\tbind:value\\n\\t\\tclass:header\\n\\t\\ttabindex=\\"-1\\"\\n\\t\\ton:blur={handle_blur}\\n\\t\\ton:mousedown|stopPropagation\\n\\t\\ton:click|stopPropagation\\n\\t\\tuse:use_focus\\n\\t\\ton:keydown={handle_keydown}\\n\\t/>\\n{/if}\\n\\n{#if datatype === \\"bool\\"}\\n\\t<BooleanCell\\n\\t\\tvalue={String(display_content)}\\n\\t\\t{editable}\\n\\t\\ton_change={handle_bool_change}\\n\\t/>\\n{:else}\\n\\t<span\\n\\t\\tclass:dragging={is_dragging}\\n\\t\\ton:keydown={handle_keydown}\\n\\t\\ttabindex=\\"0\\"\\n\\t\\trole=\\"button\\"\\n\\t\\tclass:edit\\n\\t\\tclass:expanded={edit}\\n\\t\\tclass:multiline={header}\\n\\t\\ton:focus|preventDefault\\n\\t\\tstyle={styling}\\n\\t\\tdata-editable={editable}\\n\\t\\tdata-max-chars={max_chars}\\n\\t\\tdata-expanded={edit}\\n\\t\\tplaceholder=\\" \\"\\n\\t\\tclass:text={datatype === \\"str\\"}\\n\\t\\tclass:wrap={wrap_text}\\n\\t>\\n\\t\\t{#if datatype === \\"image\\" && components.image}\\n\\t\\t\\t<svelte:component\\n\\t\\t\\t\\tthis={components.image}\\n\\t\\t\\t\\tvalue={{ url: display_text }}\\n\\t\\t\\t\\tshow_label={false}\\n\\t\\t\\t\\tlabel=\\"cell-image\\"\\n\\t\\t\\t\\tshow_download_button={false}\\n\\t\\t\\t\\t{i18n}\\n\\t\\t\\t\\tgradio={{ dispatch: () => {} }}\\n\\t\\t\\t/>\\n\\t\\t{:else if datatype === \\"html\\"}\\n\\t\\t\\t{@html display_text}\\n\\t\\t{:else if datatype === \\"markdown\\"}\\n\\t\\t\\t<MarkdownCode\\n\\t\\t\\t\\tmessage={display_text.toLocaleString()}\\n\\t\\t\\t\\t{latex_delimiters}\\n\\t\\t\\t\\t{line_breaks}\\n\\t\\t\\t\\tchatbot={false}\\n\\t\\t\\t/>\\n\\t\\t{:else}\\n\\t\\t\\t{display_text}\\n\\t\\t{/if}\\n\\t</span>\\n{/if}\\n\\n{#if show_selection_buttons && coords && on_select_column && on_select_row}\\n\\t<SelectionButtons\\n\\t\\tposition=\\"column\\"\\n\\t\\t{coords}\\n\\t\\ton_click={() => on_select_column(coords[1])}\\n\\t/>\\n\\t<SelectionButtons\\n\\t\\tposition=\\"row\\"\\n\\t\\t{coords}\\n\\t\\ton_click={() => on_select_row(coords[0])}\\n\\t/>\\n{/if}\\n\\n<style>\\n\\t.dragging {\\n\\t\\tcursor: crosshair !important;\\n\\t}\\n\\n\\ttextarea {\\n\\t\\tposition: absolute;\\n\\t\\tflex: 1 1 0%;\\n\\t\\ttransform: translateX(-0.1px);\\n\\t\\toutline: none;\\n\\t\\tborder: none;\\n\\t\\tbackground: transparent;\\n\\t\\tcursor: text;\\n\\t\\twidth: calc(100% - var(--size-2));\\n\\t\\tresize: none;\\n\\t\\theight: 100%;\\n\\t\\tpadding-left: 0;\\n\\t\\tfont-size: inherit;\\n\\t\\tfont-weight: inherit;\\n\\t\\tline-height: var(--line-lg);\\n\\t}\\n\\n\\ttextarea:focus {\\n\\t\\toutline: none;\\n\\t}\\n\\n\\tspan {\\n\\t\\tflex: 1 1 0%;\\n\\t\\tposition: relative;\\n\\t\\tdisplay: inline-block;\\n\\t\\toutline: none;\\n\\t\\t-webkit-user-select: text;\\n\\t\\t-moz-user-select: text;\\n\\t\\t-ms-user-select: text;\\n\\t\\tuser-select: text;\\n\\t\\tcursor: text;\\n\\t\\twidth: 100%;\\n\\t\\theight: 100%;\\n\\t\\toverflow: hidden;\\n\\t}\\n\\n\\tspan.text.expanded {\\n\\t\\theight: auto;\\n\\t\\tmin-height: 100%;\\n\\t\\twhite-space: pre-wrap;\\n\\t\\tword-break: break-word;\\n\\t\\toverflow: visible;\\n\\t}\\n\\n\\t.multiline {\\n\\t\\twhite-space: pre;\\n\\t\\toverflow: hidden;\\n\\t\\ttext-overflow: ellipsis;\\n\\t}\\n\\n\\t.header {\\n\\t\\ttransform: translateX(0);\\n\\t\\tfont-weight: var(--weight-bold);\\n\\t\\twhite-space: nowrap;\\n\\t\\toverflow: hidden;\\n\\t\\ttext-overflow: ellipsis;\\n\\t\\tmargin-left: var(--size-1);\\n\\t}\\n\\n\\t.edit {\\n\\t\\topacity: 0;\\n\\t\\tpointer-events: none;\\n\\t}\\n\\n\\tspan :global(img) {\\n\\t\\tmax-height: 100px;\\n\\t\\twidth: auto;\\n\\t\\tobject-fit: contain;\\n\\t}\\n\\n\\ttextarea:read-only {\\n\\t\\tcursor: not-allowed;\\n\\t}\\n\\n\\t.wrap,\\n\\t.wrap.expanded {\\n\\t\\twhite-space: normal;\\n\\t\\tword-wrap: break-word;\\n\\t\\toverflow-wrap: break-word;\\n\\t\\tword-wrap: break-word;\\n\\t}</style>\\n"],"names":[],"mappings":"AAoJC,sBAAU,CACT,MAAM,CAAE,SAAS,CAAC,UACnB,CAEA,qBAAS,CACR,QAAQ,CAAE,QAAQ,CAClB,IAAI,CAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CACZ,SAAS,CAAE,WAAW,MAAM,CAAC,CAC7B,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,WAAW,CACvB,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CACjC,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,IAAI,CACZ,YAAY,CAAE,CAAC,CACf,SAAS,CAAE,OAAO,CAClB,WAAW,CAAE,OAAO,CACpB,WAAW,CAAE,IAAI,SAAS,CAC3B,CAEA,qBAAQ,MAAO,CACd,OAAO,CAAE,IACV,CAEA,iBAAK,CACJ,IAAI,CAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CACZ,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,YAAY,CACrB,OAAO,CAAE,IAAI,CACb,mBAAmB,CAAE,IAAI,CACzB,gBAAgB,CAAE,IAAI,CACtB,eAAe,CAAE,IAAI,CACrB,WAAW,CAAE,IAAI,CACjB,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,QAAQ,CAAE,MACX,CAEA,IAAI,KAAK,sBAAU,CAClB,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,IAAI,CAChB,WAAW,CAAE,QAAQ,CACrB,UAAU,CAAE,UAAU,CACtB,QAAQ,CAAE,OACX,CAEA,uBAAW,CACV,WAAW,CAAE,GAAG,CAChB,QAAQ,CAAE,MAAM,CAChB,aAAa,CAAE,QAChB,CAEA,oBAAQ,CACP,SAAS,CAAE,WAAW,CAAC,CAAC,CACxB,WAAW,CAAE,IAAI,aAAa,CAAC,CAC/B,WAAW,CAAE,MAAM,CACnB,QAAQ,CAAE,MAAM,CAChB,aAAa,CAAE,QAAQ,CACvB,WAAW,CAAE,IAAI,QAAQ,CAC1B,CAEA,kBAAM,CACL,OAAO,CAAE,CAAC,CACV,cAAc,CAAE,IACjB,CAEA,iBAAI,CAAS,GAAK,CACjB,UAAU,CAAE,KAAK,CACjB,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,OACb,CAEA,qBAAQ,UAAW,CAClB,MAAM,CAAE,WACT,CAEA,kBAAK,CACL,KAAK,sBAAU,CACd,WAAW,CAAE,MAAM,CACnB,SAAS,CAAE,UAAU,CACrB,aAAa,CAAE,UAAU,CACzB,SAAS,CAAE,UACZ"}'};function En(n,e=null,t=!1){if(t)return String(n);const a=String(n);return !e||e<=0||a.length<=e?a:a.slice(0,e)+"..."}const qt=create_ssr_component((n,e,t,a)=>{let A,d,l,{edit:o}=e,{value:s=""}=e,{display_value:i=null}=e,{styling:r=""}=e,{header:_=!1}=e,{datatype:C="str"}=e,{latex_delimiters:p}=e,{line_breaks:b=!0}=e,{editable:k=!0}=e,{is_static:S=!1}=e,{max_chars:h=null}=e,{components:j={}}=e,{i18n:K}=e,{is_dragging:g=!1}=e,{wrap_text:T=!1}=e,{show_selection_buttons:w=!1}=e,{coords:I}=e,{on_select_column:U=null}=e,{on_select_row:W=null}=e,{el:G}=e;const Y=createEventDispatcher();function y(L){s=L.toString(),Y("blur",{blur_event:{target:{type:"checkbox",checked:L,value:L.toString()}},coords:I});}return e.edit===void 0&&t.edit&&o!==void 0&&t.edit(o),e.value===void 0&&t.value&&s!==void 0&&t.value(s),e.display_value===void 0&&t.display_value&&i!==void 0&&t.display_value(i),e.styling===void 0&&t.styling&&r!==void 0&&t.styling(r),e.header===void 0&&t.header&&_!==void 0&&t.header(_),e.datatype===void 0&&t.datatype&&C!==void 0&&t.datatype(C),e.latex_delimiters===void 0&&t.latex_delimiters&&p!==void 0&&t.latex_delimiters(p),e.line_breaks===void 0&&t.line_breaks&&b!==void 0&&t.line_breaks(b),e.editable===void 0&&t.editable&&k!==void 0&&t.editable(k),e.is_static===void 0&&t.is_static&&S!==void 0&&t.is_static(S),e.max_chars===void 0&&t.max_chars&&h!==void 0&&t.max_chars(h),e.components===void 0&&t.components&&j!==void 0&&t.components(j),e.i18n===void 0&&t.i18n&&K!==void 0&&t.i18n(K),e.is_dragging===void 0&&t.is_dragging&&g!==void 0&&t.is_dragging(g),e.wrap_text===void 0&&t.wrap_text&&T!==void 0&&t.wrap_text(T),e.show_selection_buttons===void 0&&t.show_selection_buttons&&w!==void 0&&t.show_selection_buttons(w),e.coords===void 0&&t.coords&&I!==void 0&&t.coords(I),e.on_select_column===void 0&&t.on_select_column&&U!==void 0&&t.on_select_column(U),e.on_select_row===void 0&&t.on_select_row&&W!==void 0&&t.on_select_row(W),e.el===void 0&&t.el&&G!==void 0&&t.el(G),n.css.add(xn),A=!o&&h!==null&&h>0,d=k?s:i!==null?i:s,l=A?En(d,h,C==="image"):d,`${o&&C!=="bool"?`<textarea ${S?"readonly":""}${add_attribute("aria-readonly",S,0)}${add_attribute("aria-label",S?"Cell is read-only":"Edit cell",0)} tabindex="-1" class="${["svelte-fvkwu",_?"header":""].join(" ").trim()}"${add_attribute("this",G,0)}>${escape(s||"")}</textarea>`:""} ${C==="bool"?`${validate_component(yn,"BooleanCell").$$render(n,{value:String(d),editable:k,on_change:y},{},{})}`:`<span tabindex="0" role="button"${add_attribute("style",r,0)}${add_attribute("data-editable",k,0)}${add_attribute("data-max-chars",h,0)}${add_attribute("data-expanded",o,0)} placeholder=" " class="${["svelte-fvkwu",(g?"dragging":"")+" "+(o?"edit":"")+" "+(o?"expanded":"")+" "+(_?"multiline":"")+" "+(C==="str"?"text":"")+" "+(T?"wrap":"")].join(" ").trim()}">${C==="image"&&j.image?`${validate_component(j.image||missing_component,"svelte:component").$$render(n,{value:{url:l},show_label:!1,label:"cell-image",show_download_button:!1,i18n:K,gradio:{dispatch:()=>{}}},{},{})}`:`${C==="html"?`<!-- HTML_TAG_START -->${l}<!-- HTML_TAG_END -->`:`${C==="markdown"?`${validate_component(An$1,"MarkdownCode").$$render(n,{message:l.toLocaleString(),latex_delimiters:p,line_breaks:b,chatbot:!1},{},{})}`:`${escape(l)}`}`}`}</span>`} ${w&&I&&U&&W?`${validate_component(fe,"SelectionButtons").$$render(n,{position:"column",coords:I,on_click:()=>U(I[1])},{},{})} ${validate_component(fe,"SelectionButtons").$$render(n,{position:"row",coords:I,on_click:()=>W(I[0])},{},{})}`:""}`}),In={code:".row-number.svelte-ux4in1{text-align:center;padding:var(--size-1);min-width:var(--size-12);width:var(--size-12);overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-weight:var(--weight-semibold);border-right:1px solid var(--border-color-primary)}",map:'{"version":3,"file":"RowNumber.svelte","sources":["RowNumber.svelte"],"sourcesContent":["<script lang=\\"ts\\">export let index = null;\\nexport let is_header = false;\\n<\/script>\\n\\n{#if is_header}\\n\\t<th tabindex=\\"-1\\" class=\\"row-number\\">\\n\\t\\t<div class=\\"cell-wrap\\">\\n\\t\\t\\t<div class=\\"header-content\\">\\n\\t\\t\\t\\t<div class=\\"header-text\\"></div>\\n\\t\\t\\t</div>\\n\\t\\t</div>\\n\\t</th>\\n{:else}\\n\\t<td class=\\"row-number\\" tabindex=\\"-1\\" data-row={index} data-col=\\"row-number\\">\\n\\t\\t{index !== null ? index + 1 : \\"\\"}\\n\\t</td>\\n{/if}\\n\\n<style>\\n\\t.row-number {\\n\\t\\ttext-align: center;\\n\\t\\tpadding: var(--size-1);\\n\\t\\tmin-width: var(--size-12);\\n\\t\\twidth: var(--size-12);\\n\\t\\toverflow: hidden;\\n\\t\\ttext-overflow: ellipsis;\\n\\t\\twhite-space: nowrap;\\n\\t\\tfont-weight: var(--weight-semibold);\\n\\t\\tborder-right: 1px solid var(--border-color-primary);\\n\\t}</style>\\n"],"names":[],"mappings":"AAmBC,yBAAY,CACX,UAAU,CAAE,MAAM,CAClB,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,QAAQ,CAAE,MAAM,CAChB,aAAa,CAAE,QAAQ,CACvB,WAAW,CAAE,MAAM,CACnB,WAAW,CAAE,IAAI,iBAAiB,CAAC,CACnC,YAAY,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CACnD"}'},zt=create_ssr_component((n,e,t,a)=>{let{index:A=null}=e,{is_header:d=!1}=e;return e.index===void 0&&t.index&&A!==void 0&&t.index(A),e.is_header===void 0&&t.is_header&&d!==void 0&&t.is_header(d),n.css.add(In),`${d?'<th tabindex="-1" class="row-number svelte-ux4in1" data-svelte-h="svelte-1aj56zf"><div class="cell-wrap"><div class="header-content"><div class="header-text"></div></div></div></th>':`<td class="row-number svelte-ux4in1" tabindex="-1"${add_attribute("data-row",A,0)} data-col="row-number">${escape(A!==null?A+1:"")}</td>`}`}),Bn={code:".cell-menu-button.svelte-vt38nd{flex-shrink:0;display:none;align-items:center;justify-content:center;background-color:var(--block-background-fill);border:1px solid var(--border-color-primary);border-radius:var(--block-radius);width:var(--size-5);height:var(--size-5);min-width:var(--size-5);padding:0;margin-right:var(--spacing-sm);z-index:2;position:absolute;right:var(--size-1);top:50%;transform:translateY(-50%)}",map:'{"version":3,"file":"CellMenuButton.svelte","sources":["CellMenuButton.svelte"],"sourcesContent":["<script lang=\\"ts\\">export let on_click;\\n<\/script>\\n\\n<button\\n\\taria-label=\\"Open cell menu\\"\\n\\tclass=\\"cell-menu-button\\"\\n\\taria-haspopup=\\"menu\\"\\n\\ton:click={on_click}\\n\\ton:touchstart={(event) => {\\n\\t\\tevent.preventDefault();\\n\\t\\tconst touch = event.touches[0];\\n\\t\\tconst mouseEvent = new MouseEvent(\\"click\\", {\\n\\t\\t\\tclientX: touch.clientX,\\n\\t\\t\\tclientY: touch.clientY,\\n\\t\\t\\tbubbles: true,\\n\\t\\t\\tcancelable: true,\\n\\t\\t\\tview: window\\n\\t\\t});\\n\\t\\ton_click(mouseEvent);\\n\\t}}\\n>\\n\\t&#8942;\\n</button>\\n\\n<style>\\n\\t.cell-menu-button {\\n\\t\\tflex-shrink: 0;\\n\\t\\tdisplay: none;\\n\\t\\talign-items: center;\\n\\t\\tjustify-content: center;\\n\\t\\tbackground-color: var(--block-background-fill);\\n\\t\\tborder: 1px solid var(--border-color-primary);\\n\\t\\tborder-radius: var(--block-radius);\\n\\t\\twidth: var(--size-5);\\n\\t\\theight: var(--size-5);\\n\\t\\tmin-width: var(--size-5);\\n\\t\\tpadding: 0;\\n\\t\\tmargin-right: var(--spacing-sm);\\n\\t\\tz-index: 2;\\n\\t\\tposition: absolute;\\n\\t\\tright: var(--size-1);\\n\\t\\ttop: 50%;\\n\\t\\ttransform: translateY(-50%);\\n\\t}</style>\\n"],"names":[],"mappings":"AAyBC,+BAAkB,CACjB,WAAW,CAAE,CAAC,CACd,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,cAAc,CAAC,CAClC,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,SAAS,CAAE,IAAI,QAAQ,CAAC,CACxB,OAAO,CAAE,CAAC,CACV,YAAY,CAAE,IAAI,YAAY,CAAC,CAC/B,OAAO,CAAE,CAAC,CACV,QAAQ,CAAE,QAAQ,CAClB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,GAAG,CAAE,GAAG,CACR,SAAS,CAAE,WAAW,IAAI,CAC3B"}'},be=create_ssr_component((n,e,t,a)=>{let{on_click:A}=e;return e.on_click===void 0&&t.on_click&&A!==void 0&&t.on_click(A),n.css.add(Bn),`<button aria-label="Open cell menu" class="cell-menu-button svelte-vt38nd" aria-haspopup="menu" data-svelte-h="svelte-qulk5p">⋮
</button>`}),kn={code:".wrapper.svelte-1skchaw{display:flex;align-items:center;justify-content:center}",map:'{"version":3,"file":"Padlock.svelte","sources":["Padlock.svelte"],"sourcesContent":["<div class=\\"wrapper\\" aria-label=\\"Static column\\">\\n\\t<svg\\n\\t\\txmlns=\\"http://www.w3.org/2000/svg\\"\\n\\t\\twidth=\\"13\\"\\n\\t\\theight=\\"13\\"\\n\\t\\tviewBox=\\"0 0 24 24\\"\\n\\t\\tfill=\\"none\\"\\n\\t\\tstroke=\\"currentColor\\"\\n\\t\\tstroke-width=\\"2\\"\\n\\t\\tstroke-linecap=\\"round\\"\\n\\t\\tstroke-linejoin=\\"round\\"\\n\\t>\\n\\t\\t<rect x=\\"3\\" y=\\"11\\" width=\\"18\\" height=\\"11\\" rx=\\"2\\" ry=\\"2\\"></rect>\\n\\t\\t<path d=\\"M7 11V7a5 5 0 0 1 10 0v4\\"></path>\\n\\t</svg>\\n</div>\\n\\n<style>\\n\\t.wrapper {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tjustify-content: center;\\n\\t}</style>\\n"],"names":[],"mappings":"AAkBC,uBAAS,CACR,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAClB"}'},Mn=create_ssr_component((n,e,t,a)=>(n.css.add(kn),'<div class="wrapper svelte-1skchaw" aria-label="Static column" data-svelte-h="svelte-1ernod4"><svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg> </div>')),zn=create_ssr_component((n,e,t,a)=>{let{size:A=16}=e;return e.size===void 0&&t.size&&A!==void 0&&t.size(A),`<svg${add_attribute("width",A,0)}${add_attribute("height",A,0)} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4 8L8 4L12 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M8 4V12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path></svg>`}),Kn=create_ssr_component((n,e,t,a)=>{let{size:A=16}=e;return e.size===void 0&&t.size&&A!==void 0&&t.size(A),`<svg${add_attribute("width",A,0)}${add_attribute("height",A,0)} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4 8L8 12L12 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M8 12V4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path></svg>`}),rt=create_ssr_component((n,e,t,a)=>{let{icon:A}=e;return e.icon===void 0&&t.icon&&A!==void 0&&t.icon(A),`${A=="add-column-right"?'<svg viewBox="0 0 24 24" width="16" height="16"><rect x="4" y="6" width="4" height="12" stroke="currentColor" stroke-width="2" fill="none"></rect><path d="M12 12H19M16 8L19 12L16 16" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round"></path></svg>':`${A=="add-column-left"?'<svg viewBox="0 0 24 24" width="16" height="16"><rect x="16" y="6" width="4" height="12" stroke="currentColor" stroke-width="2" fill="none"></rect><path d="M12 12H5M8 8L5 12L8 16" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round"></path></svg>':`${A=="add-row-above"?'<svg viewBox="0 0 24 24" width="16" height="16"><rect x="6" y="16" width="12" height="4" stroke="currentColor" stroke-width="2"></rect><path d="M12 12V5M8 8L12 5L16 8" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round"></path></svg>':`${A=="add-row-below"?'<svg viewBox="0 0 24 24" width="16" height="16"><rect x="6" y="4" width="12" height="4" stroke="currentColor" stroke-width="2"></rect><path d="M12 12V19M8 16L12 19L16 16" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round"></path></svg>':`${A=="delete-row"?'<svg viewBox="0 0 24 24" width="16" height="16"><rect x="5" y="10" width="14" height="4" stroke="currentColor" stroke-width="2"></rect><path d="M8 7L16 17M16 7L8 17" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg>':`${A=="delete-column"?'<svg viewBox="0 0 24 24" width="16" height="16"><rect x="10" y="5" width="4" height="14" stroke="currentColor" stroke-width="2"></rect><path d="M7 8L17 16M17 8L7 16" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg>':`${A=="sort-asc"?'<svg viewBox="0 0 24 24" width="16" height="16"><path d="M8 16L12 12L16 16" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"></path><path d="M12 12V19" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path><path d="M5 7H19" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg>':`${A=="sort-desc"?'<svg viewBox="0 0 24 24" width="16" height="16"><path d="M8 12L12 16L16 12" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"></path><path d="M12 16V9" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path><path d="M5 5H19" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg>':`${A=="clear-sort"?'<svg viewBox="0 0 24 24" width="16" height="16"><path d="M5 5H19" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path><path d="M5 9H15" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path><path d="M5 13H11" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path><path d="M5 17H7" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path><path d="M17 17L21 21M21 17L17 21" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg>':`${A=="filter"?'<svg viewBox="0 0 24 24" width="16" height="16"><path d="M5 5H19" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path><path d="M8 9H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path><path d="M11 13H13" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg>':`${A=="clear-filter"?'<svg viewBox="0 0 24 24" width="16" height="16"><path d="M5 5H19" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path><path d="M8 9H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path><path d="M11 13H13" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path><path d="M17 17L21 21M21 17L17 21" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg>':""}`}`}`}`}`}`}`}`}`}`}`}),Qn={code:"th.svelte-41hbvn{--ring-color:transparent;position:relative;outline:none;box-shadow:inset 0 0 0 1px var(--ring-color);padding:0;background:var(--table-even-background-fill);border-right-width:0px;border-left-width:1px;border-style:solid;border-color:var(--border-color-primary)}th.svelte-41hbvn:first-child{border-top-left-radius:var(--table-radius);border-bottom-left-radius:var(--table-radius);border-left-width:0px}th.svelte-41hbvn:last-child{border-top-right-radius:var(--table-radius);border-bottom-right-radius:var(--table-radius)}th.focus.svelte-41hbvn{--ring-color:var(--color-accent);box-shadow:inset 0 0 0 2px var(--ring-color);z-index:4}th.focus.svelte-41hbvn .cell-menu-button{display:flex}th.svelte-41hbvn:hover .cell-menu-button{display:flex}.cell-wrap.svelte-41hbvn{display:flex;align-items:center;justify-content:flex-start;outline:none;min-height:var(--size-9);position:relative;height:100%;padding:var(--size-2);box-sizing:border-box;margin:0;gap:var(--size-1);overflow:visible;min-width:0;border-radius:var(--table-radius)}.header-content.svelte-41hbvn{display:flex;align-items:center;overflow:hidden;flex-grow:1;min-width:0;white-space:normal;overflow-wrap:break-word;word-break:normal;height:100%;gap:var(--size-1)}.header-button.svelte-41hbvn{display:flex;text-align:left;width:100%;overflow:hidden;text-overflow:ellipsis;display:flex;align-items:center;position:relative}.sort-indicators.svelte-41hbvn{display:flex;align-items:center;margin-left:var(--size-1);gap:var(--size-1)}.sort-arrow.svelte-41hbvn{display:flex;align-items:center;justify-content:center;color:var(--body-text-color)}.sort-priority.svelte-41hbvn{display:flex;align-items:center;justify-content:center;font-size:var(--size-2);background-color:var(--button-secondary-background-fill);color:var(--body-text-color);border-radius:var(--radius-sm);width:var(--size-2-5);height:var(--size-2-5);padding:var(--size-1-5)}.filter-indicators.svelte-41hbvn{display:flex;align-items:center;margin-left:var(--size-1);gap:var(--size-1)}.filter-icon.svelte-41hbvn{display:flex;align-items:center;justify-content:center;color:var(--body-text-color)}.pinned-column.svelte-41hbvn{position:sticky;z-index:5;border-right:none}",map:'{"version":3,"file":"TableHeader.svelte","sources":["TableHeader.svelte"],"sourcesContent":["<script lang=\\"ts\\">import EditableCell from \\"./EditableCell.svelte\\";\\nimport CellMenuButton from \\"./CellMenuButton.svelte\\";\\nimport { get_sort_status } from \\"./utils/sort_utils\\";\\nimport Padlock from \\"./icons/Padlock.svelte\\";\\nimport SortArrowUp from \\"./icons/SortArrowUp.svelte\\";\\nimport SortArrowDown from \\"./icons/SortArrowDown.svelte\\";\\nimport CellMenuIcons from \\"./CellMenuIcons.svelte\\";\\nexport let value;\\nexport let i;\\nexport let actual_pinned_columns;\\nexport let header_edit;\\nexport let selected_header;\\nexport let headers;\\nexport let get_cell_width;\\nexport let handle_header_click;\\nexport let toggle_header_menu;\\nexport let end_header_edit;\\nexport let sort_columns = [];\\nexport let filter_columns = [];\\nexport let latex_delimiters;\\nexport let line_breaks;\\nexport let max_chars;\\nexport let editable;\\nexport let i18n;\\nexport let el;\\nexport let is_static;\\nexport let col_count;\\n$: can_add_columns = col_count && col_count[1] === \\"dynamic\\";\\n$: sort_index = sort_columns.findIndex((item) => item.col === i);\\n$: filter_index = filter_columns.findIndex((item) => item.col === i);\\n$: sort_priority = sort_index !== -1 ? sort_index + 1 : null;\\n$: current_direction = sort_index !== -1 ? sort_columns[sort_index].direction : null;\\nfunction get_header_position(col_index) {\\n    if (col_index >= actual_pinned_columns) {\\n        return \\"auto\\";\\n    }\\n    if (col_index === 0) {\\n        return \\"0\\";\\n    }\\n    const previous_widths = Array(col_index).fill(0).map((_, idx) => {\\n        return get_cell_width(idx);\\n    }).join(\\" + \\");\\n    return `calc(${previous_widths})`;\\n}\\n<\/script>\\n\\n<th\\n\\tclass:pinned-column={i < actual_pinned_columns}\\n\\tclass:last-pinned={i === actual_pinned_columns - 1}\\n\\tclass:focus={header_edit === i || selected_header === i}\\n\\tclass:sorted={sort_index !== -1}\\n\\tclass:filtered={filter_index !== -1}\\n\\taria-sort={get_sort_status(value, sort_columns, headers) === \\"none\\"\\n\\t\\t? \\"none\\"\\n\\t\\t: get_sort_status(value, sort_columns, headers) === \\"asc\\"\\n\\t\\t\\t? \\"ascending\\"\\n\\t\\t\\t: \\"descending\\"}\\n\\tstyle=\\"width: {get_cell_width(i)}; left: {get_header_position(i)};\\"\\n\\ton:click={(event) => handle_header_click(event, i)}\\n\\ton:mousedown={(event) => {\\n\\t\\tevent.preventDefault();\\n\\t\\tevent.stopPropagation();\\n\\t}}\\n\\ttitle={value}\\n>\\n\\t<div class=\\"cell-wrap\\">\\n\\t\\t<div class=\\"header-content\\">\\n\\t\\t\\t<button\\n\\t\\t\\t\\tclass=\\"header-button\\"\\n\\t\\t\\t\\ton:click={(event) => handle_header_click(event, i)}\\n\\t\\t\\t\\ton:mousedown={(event) => {\\n\\t\\t\\t\\t\\tevent.preventDefault();\\n\\t\\t\\t\\t\\tevent.stopPropagation();\\n\\t\\t\\t\\t}}\\n\\t\\t\\t\\ttitle={value}\\n\\t\\t\\t>\\n\\t\\t\\t\\t<EditableCell\\n\\t\\t\\t\\t\\t{max_chars}\\n\\t\\t\\t\\t\\tbind:value\\n\\t\\t\\t\\t\\tbind:el\\n\\t\\t\\t\\t\\t{latex_delimiters}\\n\\t\\t\\t\\t\\t{line_breaks}\\n\\t\\t\\t\\t\\tedit={header_edit === i}\\n\\t\\t\\t\\t\\ton:keydown={(event) => {\\n\\t\\t\\t\\t\\t\\tif (\\n\\t\\t\\t\\t\\t\\t\\tevent.detail.key === \\"Enter\\" ||\\n\\t\\t\\t\\t\\t\\t\\tevent.detail.key === \\"Escape\\" ||\\n\\t\\t\\t\\t\\t\\t\\tevent.detail.key === \\"Tab\\"\\n\\t\\t\\t\\t\\t\\t) {\\n\\t\\t\\t\\t\\t\\t\\tend_header_edit(event);\\n\\t\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\t}}\\n\\t\\t\\t\\t\\theader\\n\\t\\t\\t\\t\\t{editable}\\n\\t\\t\\t\\t\\t{is_static}\\n\\t\\t\\t\\t\\t{i18n}\\n\\t\\t\\t\\t\\tcoords={[i, 0]}\\n\\t\\t\\t\\t/>\\n\\t\\t\\t\\t{#if sort_index !== -1}\\n\\t\\t\\t\\t\\t<div class=\\"sort-indicators\\">\\n\\t\\t\\t\\t\\t\\t<span class=\\"sort-arrow\\">\\n\\t\\t\\t\\t\\t\\t\\t{#if current_direction === \\"asc\\"}\\n\\t\\t\\t\\t\\t\\t\\t\\t<SortArrowUp size={12} />\\n\\t\\t\\t\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t\\t\\t\\t<SortArrowDown size={12} />\\n\\t\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t\\t</span>\\n\\t\\t\\t\\t\\t\\t{#if sort_columns.length > 1}\\n\\t\\t\\t\\t\\t\\t\\t<span class=\\"sort-priority\\">\\n\\t\\t\\t\\t\\t\\t\\t\\t{sort_priority}\\n\\t\\t\\t\\t\\t\\t\\t</span>\\n\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t{#if filter_index !== -1}\\n\\t\\t\\t\\t\\t<div class=\\"filter-indicators\\">\\n\\t\\t\\t\\t\\t\\t<span class=\\"filter-icon\\">\\n\\t\\t\\t\\t\\t\\t\\t<CellMenuIcons icon=\\"filter\\" />\\n\\t\\t\\t\\t\\t\\t</span>\\n\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t</button>\\n\\t\\t\\t{#if is_static}\\n\\t\\t\\t\\t<Padlock />\\n\\t\\t\\t{/if}\\n\\t\\t</div>\\n\\t\\t{#if can_add_columns}\\n\\t\\t\\t<CellMenuButton on_click={(event) => toggle_header_menu(event, i)} />\\n\\t\\t{/if}\\n\\t</div>\\n</th>\\n\\n<style>\\n\\tth {\\n\\t\\t--ring-color: transparent;\\n\\t\\tposition: relative;\\n\\t\\toutline: none;\\n\\t\\tbox-shadow: inset 0 0 0 1px var(--ring-color);\\n\\t\\tpadding: 0;\\n\\t\\tbackground: var(--table-even-background-fill);\\n\\t\\tborder-right-width: 0px;\\n\\t\\tborder-left-width: 1px;\\n\\t\\tborder-style: solid;\\n\\t\\tborder-color: var(--border-color-primary);\\n\\t}\\n\\n\\tth:first-child {\\n\\t\\tborder-top-left-radius: var(--table-radius);\\n\\t\\tborder-bottom-left-radius: var(--table-radius);\\n\\t\\tborder-left-width: 0px;\\n\\t}\\n\\n\\tth:last-child {\\n\\t\\tborder-top-right-radius: var(--table-radius);\\n\\t\\tborder-bottom-right-radius: var(--table-radius);\\n\\t}\\n\\n\\tth.focus {\\n\\t\\t--ring-color: var(--color-accent);\\n\\t\\tbox-shadow: inset 0 0 0 2px var(--ring-color);\\n\\t\\tz-index: 4;\\n\\t}\\n\\n\\tth.focus :global(.cell-menu-button) {\\n\\t\\tdisplay: flex;\\n\\t}\\n\\n\\tth:hover :global(.cell-menu-button) {\\n\\t\\tdisplay: flex;\\n\\t}\\n\\n\\t.cell-wrap {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tjustify-content: flex-start;\\n\\t\\toutline: none;\\n\\t\\tmin-height: var(--size-9);\\n\\t\\tposition: relative;\\n\\t\\theight: 100%;\\n\\t\\tpadding: var(--size-2);\\n\\t\\tbox-sizing: border-box;\\n\\t\\tmargin: 0;\\n\\t\\tgap: var(--size-1);\\n\\t\\toverflow: visible;\\n\\t\\tmin-width: 0;\\n\\t\\tborder-radius: var(--table-radius);\\n\\t}\\n\\n\\t.header-content {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\toverflow: hidden;\\n\\t\\tflex-grow: 1;\\n\\t\\tmin-width: 0;\\n\\t\\twhite-space: normal;\\n\\t\\toverflow-wrap: break-word;\\n\\t\\tword-break: normal;\\n\\t\\theight: 100%;\\n\\t\\tgap: var(--size-1);\\n\\t}\\n\\n\\t.header-button {\\n\\t\\tdisplay: flex;\\n\\t\\ttext-align: left;\\n\\t\\twidth: 100%;\\n\\t\\toverflow: hidden;\\n\\t\\ttext-overflow: ellipsis;\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tposition: relative;\\n\\t}\\n\\n\\t.sort-indicators {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tmargin-left: var(--size-1);\\n\\t\\tgap: var(--size-1);\\n\\t}\\n\\n\\t.sort-arrow {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tjustify-content: center;\\n\\t\\tcolor: var(--body-text-color);\\n\\t}\\n\\n\\t.sort-priority {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tjustify-content: center;\\n\\t\\tfont-size: var(--size-2);\\n\\t\\tbackground-color: var(--button-secondary-background-fill);\\n\\t\\tcolor: var(--body-text-color);\\n\\t\\tborder-radius: var(--radius-sm);\\n\\t\\twidth: var(--size-2-5);\\n\\t\\theight: var(--size-2-5);\\n\\t\\tpadding: var(--size-1-5);\\n\\t}\\n\\n\\t.filter-indicators {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tmargin-left: var(--size-1);\\n\\t\\tgap: var(--size-1);\\n\\t}\\n\\n\\t.filter-icon {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tjustify-content: center;\\n\\t\\tcolor: var(--body-text-color);\\n\\t}\\n\\n\\t.pinned-column {\\n\\t\\tposition: sticky;\\n\\t\\tz-index: 5;\\n\\t\\tborder-right: none;\\n\\t}</style>\\n"],"names":[],"mappings":"AAqIC,gBAAG,CACF,YAAY,CAAE,WAAW,CACzB,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,CACb,UAAU,CAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,CAC7C,OAAO,CAAE,CAAC,CACV,UAAU,CAAE,IAAI,4BAA4B,CAAC,CAC7C,kBAAkB,CAAE,GAAG,CACvB,iBAAiB,CAAE,GAAG,CACtB,YAAY,CAAE,KAAK,CACnB,YAAY,CAAE,IAAI,sBAAsB,CACzC,CAEA,gBAAE,YAAa,CACd,sBAAsB,CAAE,IAAI,cAAc,CAAC,CAC3C,yBAAyB,CAAE,IAAI,cAAc,CAAC,CAC9C,iBAAiB,CAAE,GACpB,CAEA,gBAAE,WAAY,CACb,uBAAuB,CAAE,IAAI,cAAc,CAAC,CAC5C,0BAA0B,CAAE,IAAI,cAAc,CAC/C,CAEA,EAAE,oBAAO,CACR,YAAY,CAAE,mBAAmB,CACjC,UAAU,CAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,CAC7C,OAAO,CAAE,CACV,CAEA,EAAE,oBAAM,CAAS,iBAAmB,CACnC,OAAO,CAAE,IACV,CAEA,gBAAE,MAAM,CAAS,iBAAmB,CACnC,OAAO,CAAE,IACV,CAEA,wBAAW,CACV,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,UAAU,CAC3B,OAAO,CAAE,IAAI,CACb,UAAU,CAAE,IAAI,QAAQ,CAAC,CACzB,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,UAAU,CAAE,UAAU,CACtB,MAAM,CAAE,CAAC,CACT,GAAG,CAAE,IAAI,QAAQ,CAAC,CAClB,QAAQ,CAAE,OAAO,CACjB,SAAS,CAAE,CAAC,CACZ,aAAa,CAAE,IAAI,cAAc,CAClC,CAEA,6BAAgB,CACf,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,QAAQ,CAAE,MAAM,CAChB,SAAS,CAAE,CAAC,CACZ,SAAS,CAAE,CAAC,CACZ,WAAW,CAAE,MAAM,CACnB,aAAa,CAAE,UAAU,CACzB,UAAU,CAAE,MAAM,CAClB,MAAM,CAAE,IAAI,CACZ,GAAG,CAAE,IAAI,QAAQ,CAClB,CAEA,4BAAe,CACd,OAAO,CAAE,IAAI,CACb,UAAU,CAAE,IAAI,CAChB,KAAK,CAAE,IAAI,CACX,QAAQ,CAAE,MAAM,CAChB,aAAa,CAAE,QAAQ,CACvB,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,QAAQ,CAAE,QACX,CAEA,8BAAiB,CAChB,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,WAAW,CAAE,IAAI,QAAQ,CAAC,CAC1B,GAAG,CAAE,IAAI,QAAQ,CAClB,CAEA,yBAAY,CACX,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CAEA,4BAAe,CACd,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,SAAS,CAAE,IAAI,QAAQ,CAAC,CACxB,gBAAgB,CAAE,IAAI,kCAAkC,CAAC,CACzD,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,KAAK,CAAE,IAAI,UAAU,CAAC,CACtB,MAAM,CAAE,IAAI,UAAU,CAAC,CACvB,OAAO,CAAE,IAAI,UAAU,CACxB,CAEA,gCAAmB,CAClB,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,WAAW,CAAE,IAAI,QAAQ,CAAC,CAC1B,GAAG,CAAE,IAAI,QAAQ,CAClB,CAEA,0BAAa,CACZ,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CAEA,4BAAe,CACd,QAAQ,CAAE,MAAM,CAChB,OAAO,CAAE,CAAC,CACV,YAAY,CAAE,IACf"}'},me=create_ssr_component((n,e,t,a)=>{let A,d,l,o,s,{value:i}=e,{i:r}=e,{actual_pinned_columns:_}=e,{header_edit:C}=e,{selected_header:p}=e,{headers:b}=e,{get_cell_width:k}=e,{handle_header_click:S}=e,{toggle_header_menu:h}=e,{end_header_edit:j}=e,{sort_columns:K=[]}=e,{filter_columns:g=[]}=e,{latex_delimiters:T}=e,{line_breaks:w}=e,{max_chars:I}=e,{editable:U}=e,{i18n:W}=e,{el:G}=e,{is_static:Y}=e,{col_count:y}=e;function L(f){return f>=_?"auto":f===0?"0":`calc(${Array(f).fill(0).map((N,q)=>k(q)).join(" + ")})`}e.value===void 0&&t.value&&i!==void 0&&t.value(i),e.i===void 0&&t.i&&r!==void 0&&t.i(r),e.actual_pinned_columns===void 0&&t.actual_pinned_columns&&_!==void 0&&t.actual_pinned_columns(_),e.header_edit===void 0&&t.header_edit&&C!==void 0&&t.header_edit(C),e.selected_header===void 0&&t.selected_header&&p!==void 0&&t.selected_header(p),e.headers===void 0&&t.headers&&b!==void 0&&t.headers(b),e.get_cell_width===void 0&&t.get_cell_width&&k!==void 0&&t.get_cell_width(k),e.handle_header_click===void 0&&t.handle_header_click&&S!==void 0&&t.handle_header_click(S),e.toggle_header_menu===void 0&&t.toggle_header_menu&&h!==void 0&&t.toggle_header_menu(h),e.end_header_edit===void 0&&t.end_header_edit&&j!==void 0&&t.end_header_edit(j),e.sort_columns===void 0&&t.sort_columns&&K!==void 0&&t.sort_columns(K),e.filter_columns===void 0&&t.filter_columns&&g!==void 0&&t.filter_columns(g),e.latex_delimiters===void 0&&t.latex_delimiters&&T!==void 0&&t.latex_delimiters(T),e.line_breaks===void 0&&t.line_breaks&&w!==void 0&&t.line_breaks(w),e.max_chars===void 0&&t.max_chars&&I!==void 0&&t.max_chars(I),e.editable===void 0&&t.editable&&U!==void 0&&t.editable(U),e.i18n===void 0&&t.i18n&&W!==void 0&&t.i18n(W),e.el===void 0&&t.el&&G!==void 0&&t.el(G),e.is_static===void 0&&t.is_static&&Y!==void 0&&t.is_static(Y),e.col_count===void 0&&t.col_count&&y!==void 0&&t.col_count(y),n.css.add(Qn);let P,D,M=n.head;do P=!0,n.head=M,A=y&&y[1]==="dynamic",d=K.findIndex(f=>f.col===r),l=g.findIndex(f=>f.col===r),o=d!==-1?d+1:null,s=d!==-1?K[d].direction:null,D=`<th${add_attribute("aria-sort",ve(i,K,b)==="none"?"none":ve(i,K,b)==="asc"?"ascending":"descending",0)} style="${"width: "+escape(k(r),!0)+"; left: "+escape(L(r),!0)+";"}"${add_attribute("title",i,0)} class="${["svelte-41hbvn",(r<_?"pinned-column":"")+" "+(r===_-1?"last-pinned":"")+" "+(C===r||p===r?"focus":"")+" "+(d!==-1?"sorted":"")+" "+(l!==-1?"filtered":"")].join(" ").trim()}"><div class="cell-wrap svelte-41hbvn"><div class="header-content svelte-41hbvn"><button class="header-button svelte-41hbvn"${add_attribute("title",i,0)}>${validate_component(qt,"EditableCell").$$render(n,{max_chars:I,latex_delimiters:T,line_breaks:w,edit:C===r,header:!0,editable:U,is_static:Y,i18n:W,coords:[r,0],value:i,el:G},{value:f=>{i=f,P=!1;},el:f=>{G=f,P=!1;}},{})} ${d!==-1?`<div class="sort-indicators svelte-41hbvn"><span class="sort-arrow svelte-41hbvn">${s==="asc"?`${validate_component(zn,"SortArrowUp").$$render(n,{size:12},{},{})}`:`${validate_component(Kn,"SortArrowDown").$$render(n,{size:12},{},{})}`}</span> ${K.length>1?`<span class="sort-priority svelte-41hbvn">${escape(o)}</span>`:""}</div>`:""} ${l!==-1?`<div class="filter-indicators svelte-41hbvn"><span class="filter-icon svelte-41hbvn">${validate_component(rt,"CellMenuIcons").$$render(n,{icon:"filter"},{},{})}</span></div>`:""}</button> ${Y?`${validate_component(Mn,"Padlock").$$render(n,{},{},{})}`:""}</div> ${A?`${validate_component(be,"CellMenuButton").$$render(n,{on_click:f=>h(f,r)},{},{})}`:""}</div> </th>`;while(!P);return D}),Un={code:`td.svelte-v1pjjd{--ring-color:transparent;position:relative;outline:none;box-shadow:inset 0 0 0 1px var(--ring-color);padding:0;border-right-width:0px;border-left-width:1px;border-style:solid;border-color:var(--border-color-primary)}.cell-wrap.svelte-v1pjjd{display:flex;align-items:center;justify-content:flex-start;outline:none;min-height:var(--size-9);position:relative;height:100%;padding:var(--size-2);box-sizing:border-box;margin:0;gap:var(--size-1);overflow:visible;min-width:0;border-radius:var(--table-radius)}.cell-selected.svelte-v1pjjd{--ring-color:var(--color-accent);box-shadow:inset 0 0 0 2px var(--ring-color);z-index:2;position:relative}.cell-selected.svelte-v1pjjd .cell-menu-button{display:flex}.flash.cell-selected.svelte-v1pjjd{animation:svelte-v1pjjd-flash-color 700ms ease-out}@keyframes svelte-v1pjjd-flash-color{0%,30%{background:var(--color-accent-copied)}100%{background:transparent}}.pinned-column.svelte-v1pjjd{position:sticky;z-index:3;border-right:none}.pinned-column.svelte-v1pjjd:nth-child(odd){background:var(--table-odd-background-fill)}.pinned-column.svelte-v1pjjd:nth-child(even){background:var(--table-even-background-fill)}td.svelte-v1pjjd:first-child{border-left-width:0px}tr:last-child td.svelte-v1pjjd:first-child{border-bottom-left-radius:var(--table-radius)}tr:last-child td.svelte-v1pjjd:last-child{border-bottom-right-radius:var(--table-radius)}.dragging.svelte-v1pjjd{cursor:crosshair}.cell-selected.no-top.svelte-v1pjjd{box-shadow:inset 2px 0 0 var(--ring-color),
			inset -2px 0 0 var(--ring-color),
			inset 0 -2px 0 var(--ring-color)}.cell-selected.no-bottom.svelte-v1pjjd{box-shadow:inset 2px 0 0 var(--ring-color),
			inset -2px 0 0 var(--ring-color),
			inset 0 2px 0 var(--ring-color)}.cell-selected.no-left.svelte-v1pjjd{box-shadow:inset 0 2px 0 var(--ring-color),
			inset -2px 0 0 var(--ring-color),
			inset 0 -2px 0 var(--ring-color)}.cell-selected.no-right.svelte-v1pjjd{box-shadow:inset 0 2px 0 var(--ring-color),
			inset 2px 0 0 var(--ring-color),
			inset 0 -2px 0 var(--ring-color)}.cell-selected.no-top.no-left.svelte-v1pjjd{box-shadow:inset -2px 0 0 var(--ring-color),
			inset 0 -2px 0 var(--ring-color)}.cell-selected.no-top.no-right.svelte-v1pjjd{box-shadow:inset 2px 0 0 var(--ring-color),
			inset 0 -2px 0 var(--ring-color)}.cell-selected.no-bottom.no-left.svelte-v1pjjd{box-shadow:inset -2px 0 0 var(--ring-color),
			inset 0 2px 0 var(--ring-color)}.cell-selected.no-bottom.no-right.svelte-v1pjjd{box-shadow:inset 2px 0 0 var(--ring-color),
			inset 0 2px 0 var(--ring-color)}.cell-selected.no-top.no-bottom.svelte-v1pjjd{box-shadow:inset 2px 0 0 var(--ring-color),
			inset -2px 0 0 var(--ring-color)}.cell-selected.no-left.no-right.svelte-v1pjjd{box-shadow:inset 0 2px 0 var(--ring-color),
			inset 0 -2px 0 var(--ring-color)}.cell-selected.no-top.no-left.no-right.svelte-v1pjjd{box-shadow:inset 0 -2px 0 var(--ring-color)}.cell-selected.no-bottom.no-left.no-right.svelte-v1pjjd{box-shadow:inset 0 2px 0 var(--ring-color)}.cell-selected.no-left.no-top.no-bottom.svelte-v1pjjd{box-shadow:inset -2px 0 0 var(--ring-color)}.cell-selected.no-right.no-top.no-bottom.svelte-v1pjjd{box-shadow:inset 2px 0 0 var(--ring-color)}.cell-selected.no-top.no-bottom.no-left.no-right.svelte-v1pjjd{box-shadow:none}`,map:'{"version":3,"file":"TableCell.svelte","sources":["TableCell.svelte"],"sourcesContent":["<script lang=\\"ts\\">import EditableCell from \\"./EditableCell.svelte\\";\\nimport CellMenuButton from \\"./CellMenuButton.svelte\\";\\nimport { is_cell_in_selection } from \\"./selection_utils\\";\\nexport let value;\\nexport let index;\\nexport let j;\\nexport let actual_pinned_columns;\\nexport let get_cell_width;\\nexport let handle_cell_click;\\nexport let handle_blur;\\nexport let toggle_cell_menu;\\nexport let is_cell_selected;\\nexport let should_show_cell_menu;\\nexport let selected_cells;\\nexport let copy_flash;\\nexport let active_cell_menu;\\nexport let styling;\\nexport let latex_delimiters;\\nexport let line_breaks;\\nexport let datatype;\\nexport let editing;\\nexport let max_chars;\\nexport let editable;\\nexport let is_static = false;\\nexport let i18n;\\nexport let components = {};\\nexport let el;\\nexport let handle_select_column;\\nexport let handle_select_row;\\nexport let is_dragging;\\nexport let display_value;\\nexport let wrap = false;\\nfunction get_cell_position(col_index) {\\n    if (col_index >= actual_pinned_columns) {\\n        return \\"auto\\";\\n    }\\n    if (col_index === 0) {\\n        return \\"0\\";\\n    }\\n    const previous_widths = Array(col_index).fill(0).map((_, idx) => {\\n        return get_cell_width(idx);\\n    }).join(\\" + \\");\\n    return `calc(${previous_widths})`;\\n}\\n$: cell_classes = is_cell_selected([index, j], selected_cells || []);\\n$: is_in_selection = is_cell_in_selection([index, j], selected_cells);\\n$: has_no_top = cell_classes.includes(\\"no-top\\");\\n$: has_no_bottom = cell_classes.includes(\\"no-bottom\\");\\n$: has_no_left = cell_classes.includes(\\"no-left\\");\\n$: has_no_right = cell_classes.includes(\\"no-right\\");\\n<\/script>\\n\\n<td\\n\\tclass:pinned-column={j < actual_pinned_columns}\\n\\tclass:last-pinned={j === actual_pinned_columns - 1}\\n\\ttabindex={j < actual_pinned_columns ? -1 : 0}\\n\\tbind:this={el.cell}\\n\\tdata-row={index}\\n\\tdata-col={j}\\n\\tdata-testid={`cell-${index}-${j}`}\\n\\ton:mousedown={(e) => handle_cell_click(e, index, j)}\\n\\ton:contextmenu|preventDefault={(e) => toggle_cell_menu(e, index, j)}\\n\\tstyle=\\"width: {get_cell_width(j)}; left: {get_cell_position(j)}; {styling ||\\n\\t\\t\'\'}\\"\\n\\tclass:flash={copy_flash && is_in_selection}\\n\\tclass:cell-selected={is_in_selection}\\n\\tclass:no-top={has_no_top}\\n\\tclass:no-bottom={has_no_bottom}\\n\\tclass:no-left={has_no_left}\\n\\tclass:no-right={has_no_right}\\n\\tclass:menu-active={active_cell_menu &&\\n\\t\\tactive_cell_menu.row === index &&\\n\\t\\tactive_cell_menu.col === j}\\n\\tclass:dragging={is_dragging}\\n>\\n\\t<div class=\\"cell-wrap\\">\\n\\t\\t<EditableCell\\n\\t\\t\\tbind:value\\n\\t\\t\\tbind:el={el.input}\\n\\t\\t\\tdisplay_value={display_value !== undefined\\n\\t\\t\\t\\t? display_value\\n\\t\\t\\t\\t: String(value)}\\n\\t\\t\\t{latex_delimiters}\\n\\t\\t\\t{line_breaks}\\n\\t\\t\\t{editable}\\n\\t\\t\\t{is_static}\\n\\t\\t\\tedit={editing && editing[0] === index && editing[1] === j}\\n\\t\\t\\t{datatype}\\n\\t\\t\\ton:focus={() => {\\n\\t\\t\\t\\tconst row = index;\\n\\t\\t\\t\\tconst col = j;\\n\\t\\t\\t\\tif (!selected_cells.some(([r, c]) => r === row && c === col)) {\\n\\t\\t\\t\\t\\tselected_cells = [[row, col]];\\n\\t\\t\\t\\t}\\n\\t\\t\\t}}\\n\\t\\t\\ton:blur={handle_blur}\\n\\t\\t\\t{max_chars}\\n\\t\\t\\t{i18n}\\n\\t\\t\\t{components}\\n\\t\\t\\tshow_selection_buttons={selected_cells.length === 1 &&\\n\\t\\t\\t\\tselected_cells[0][0] === index &&\\n\\t\\t\\t\\tselected_cells[0][1] === j}\\n\\t\\t\\tcoords={[index, j]}\\n\\t\\t\\ton_select_column={handle_select_column}\\n\\t\\t\\ton_select_row={handle_select_row}\\n\\t\\t\\t{is_dragging}\\n\\t\\t\\twrap_text={wrap}\\n\\t\\t/>\\n\\t\\t{#if editable && should_show_cell_menu([index, j], selected_cells, editable)}\\n\\t\\t\\t<CellMenuButton on_click={(event) => toggle_cell_menu(event, index, j)} />\\n\\t\\t{/if}\\n\\t</div>\\n</td>\\n\\n<style>\\n\\ttd {\\n\\t\\t--ring-color: transparent;\\n\\t\\tposition: relative;\\n\\t\\toutline: none;\\n\\t\\tbox-shadow: inset 0 0 0 1px var(--ring-color);\\n\\t\\tpadding: 0;\\n\\t\\tborder-right-width: 0px;\\n\\t\\tborder-left-width: 1px;\\n\\t\\tborder-style: solid;\\n\\t\\tborder-color: var(--border-color-primary);\\n\\t}\\n\\n\\t.cell-wrap {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tjustify-content: flex-start;\\n\\t\\toutline: none;\\n\\t\\tmin-height: var(--size-9);\\n\\t\\tposition: relative;\\n\\t\\theight: 100%;\\n\\t\\tpadding: var(--size-2);\\n\\t\\tbox-sizing: border-box;\\n\\t\\tmargin: 0;\\n\\t\\tgap: var(--size-1);\\n\\t\\toverflow: visible;\\n\\t\\tmin-width: 0;\\n\\t\\tborder-radius: var(--table-radius);\\n\\t}\\n\\n\\t.cell-selected {\\n\\t\\t--ring-color: var(--color-accent);\\n\\t\\tbox-shadow: inset 0 0 0 2px var(--ring-color);\\n\\t\\tz-index: 2;\\n\\t\\tposition: relative;\\n\\t}\\n\\n\\t.cell-selected :global(.cell-menu-button) {\\n\\t\\tdisplay: flex;\\n\\t}\\n\\n\\t.flash.cell-selected {\\n\\t\\tanimation: flash-color 700ms ease-out;\\n\\t}\\n\\n\\t@keyframes flash-color {\\n\\t\\t0%,\\n\\t\\t30% {\\n\\t\\t\\tbackground: var(--color-accent-copied);\\n\\t\\t}\\n\\n\\t\\t100% {\\n\\t\\t\\tbackground: transparent;\\n\\t\\t}\\n\\t}\\n\\n\\t.pinned-column {\\n\\t\\tposition: sticky;\\n\\t\\tz-index: 3;\\n\\t\\tborder-right: none;\\n\\t}\\n\\n\\t.pinned-column:nth-child(odd) {\\n\\t\\tbackground: var(--table-odd-background-fill);\\n\\t}\\n\\n\\t.pinned-column:nth-child(even) {\\n\\t\\tbackground: var(--table-even-background-fill);\\n\\t}\\n\\n\\ttd:first-child {\\n\\t\\tborder-left-width: 0px;\\n\\t}\\n\\n\\t:global(tr:last-child) td:first-child {\\n\\t\\tborder-bottom-left-radius: var(--table-radius);\\n\\t}\\n\\n\\t:global(tr:last-child) td:last-child {\\n\\t\\tborder-bottom-right-radius: var(--table-radius);\\n\\t}\\n\\n\\t.dragging {\\n\\t\\tcursor: crosshair;\\n\\t}\\n\\n\\t/* Add back the cell selection border styles */\\n\\t.cell-selected.no-top {\\n\\t\\tbox-shadow:\\n\\t\\t\\tinset 2px 0 0 var(--ring-color),\\n\\t\\t\\tinset -2px 0 0 var(--ring-color),\\n\\t\\t\\tinset 0 -2px 0 var(--ring-color);\\n\\t}\\n\\n\\t.cell-selected.no-bottom {\\n\\t\\tbox-shadow:\\n\\t\\t\\tinset 2px 0 0 var(--ring-color),\\n\\t\\t\\tinset -2px 0 0 var(--ring-color),\\n\\t\\t\\tinset 0 2px 0 var(--ring-color);\\n\\t}\\n\\n\\t.cell-selected.no-left {\\n\\t\\tbox-shadow:\\n\\t\\t\\tinset 0 2px 0 var(--ring-color),\\n\\t\\t\\tinset -2px 0 0 var(--ring-color),\\n\\t\\t\\tinset 0 -2px 0 var(--ring-color);\\n\\t}\\n\\n\\t.cell-selected.no-right {\\n\\t\\tbox-shadow:\\n\\t\\t\\tinset 0 2px 0 var(--ring-color),\\n\\t\\t\\tinset 2px 0 0 var(--ring-color),\\n\\t\\t\\tinset 0 -2px 0 var(--ring-color);\\n\\t}\\n\\n\\t.cell-selected.no-top.no-left {\\n\\t\\tbox-shadow:\\n\\t\\t\\tinset -2px 0 0 var(--ring-color),\\n\\t\\t\\tinset 0 -2px 0 var(--ring-color);\\n\\t}\\n\\n\\t.cell-selected.no-top.no-right {\\n\\t\\tbox-shadow:\\n\\t\\t\\tinset 2px 0 0 var(--ring-color),\\n\\t\\t\\tinset 0 -2px 0 var(--ring-color);\\n\\t}\\n\\n\\t.cell-selected.no-bottom.no-left {\\n\\t\\tbox-shadow:\\n\\t\\t\\tinset -2px 0 0 var(--ring-color),\\n\\t\\t\\tinset 0 2px 0 var(--ring-color);\\n\\t}\\n\\n\\t.cell-selected.no-bottom.no-right {\\n\\t\\tbox-shadow:\\n\\t\\t\\tinset 2px 0 0 var(--ring-color),\\n\\t\\t\\tinset 0 2px 0 var(--ring-color);\\n\\t}\\n\\n\\t.cell-selected.no-top.no-bottom {\\n\\t\\tbox-shadow:\\n\\t\\t\\tinset 2px 0 0 var(--ring-color),\\n\\t\\t\\tinset -2px 0 0 var(--ring-color);\\n\\t}\\n\\n\\t.cell-selected.no-left.no-right {\\n\\t\\tbox-shadow:\\n\\t\\t\\tinset 0 2px 0 var(--ring-color),\\n\\t\\t\\tinset 0 -2px 0 var(--ring-color);\\n\\t}\\n\\n\\t.cell-selected.no-top.no-left.no-right {\\n\\t\\tbox-shadow: inset 0 -2px 0 var(--ring-color);\\n\\t}\\n\\n\\t.cell-selected.no-bottom.no-left.no-right {\\n\\t\\tbox-shadow: inset 0 2px 0 var(--ring-color);\\n\\t}\\n\\n\\t.cell-selected.no-left.no-top.no-bottom {\\n\\t\\tbox-shadow: inset -2px 0 0 var(--ring-color);\\n\\t}\\n\\n\\t.cell-selected.no-right.no-top.no-bottom {\\n\\t\\tbox-shadow: inset 2px 0 0 var(--ring-color);\\n\\t}\\n\\n\\t.cell-selected.no-top.no-bottom.no-left.no-right {\\n\\t\\tbox-shadow: none;\\n\\t}</style>\\n"],"names":[],"mappings":"AAmHC,gBAAG,CACF,YAAY,CAAE,WAAW,CACzB,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,CACb,UAAU,CAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,CAC7C,OAAO,CAAE,CAAC,CACV,kBAAkB,CAAE,GAAG,CACvB,iBAAiB,CAAE,GAAG,CACtB,YAAY,CAAE,KAAK,CACnB,YAAY,CAAE,IAAI,sBAAsB,CACzC,CAEA,wBAAW,CACV,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,UAAU,CAC3B,OAAO,CAAE,IAAI,CACb,UAAU,CAAE,IAAI,QAAQ,CAAC,CACzB,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,UAAU,CAAE,UAAU,CACtB,MAAM,CAAE,CAAC,CACT,GAAG,CAAE,IAAI,QAAQ,CAAC,CAClB,QAAQ,CAAE,OAAO,CACjB,SAAS,CAAE,CAAC,CACZ,aAAa,CAAE,IAAI,cAAc,CAClC,CAEA,4BAAe,CACd,YAAY,CAAE,mBAAmB,CACjC,UAAU,CAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,CAC7C,OAAO,CAAE,CAAC,CACV,QAAQ,CAAE,QACX,CAEA,4BAAc,CAAS,iBAAmB,CACzC,OAAO,CAAE,IACV,CAEA,MAAM,4BAAe,CACpB,SAAS,CAAE,yBAAW,CAAC,KAAK,CAAC,QAC9B,CAEA,WAAW,yBAAY,CACtB,EAAE,CACF,GAAI,CACH,UAAU,CAAE,IAAI,qBAAqB,CACtC,CAEA,IAAK,CACJ,UAAU,CAAE,WACb,CACD,CAEA,4BAAe,CACd,QAAQ,CAAE,MAAM,CAChB,OAAO,CAAE,CAAC,CACV,YAAY,CAAE,IACf,CAEA,4BAAc,WAAW,GAAG,CAAE,CAC7B,UAAU,CAAE,IAAI,2BAA2B,CAC5C,CAEA,4BAAc,WAAW,IAAI,CAAE,CAC9B,UAAU,CAAE,IAAI,4BAA4B,CAC7C,CAEA,gBAAE,YAAa,CACd,iBAAiB,CAAE,GACpB,CAEQ,aAAc,CAAC,gBAAE,YAAa,CACrC,yBAAyB,CAAE,IAAI,cAAc,CAC9C,CAEQ,aAAc,CAAC,gBAAE,WAAY,CACpC,0BAA0B,CAAE,IAAI,cAAc,CAC/C,CAEA,uBAAU,CACT,MAAM,CAAE,SACT,CAGA,cAAc,qBAAQ,CACrB,UAAU,CACT,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC;AACnC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC;AACpC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,YAAY,CACjC,CAEA,cAAc,wBAAW,CACxB,UAAU,CACT,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC;AACnC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC;AACpC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,YAAY,CAChC,CAEA,cAAc,sBAAS,CACtB,UAAU,CACT,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC;AACnC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC;AACpC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,YAAY,CACjC,CAEA,cAAc,uBAAU,CACvB,UAAU,CACT,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC;AACnC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC;AACnC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,YAAY,CACjC,CAEA,cAAc,OAAO,sBAAS,CAC7B,UAAU,CACT,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC;AACpC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,YAAY,CACjC,CAEA,cAAc,OAAO,uBAAU,CAC9B,UAAU,CACT,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC;AACnC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,YAAY,CACjC,CAEA,cAAc,UAAU,sBAAS,CAChC,UAAU,CACT,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC;AACpC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,YAAY,CAChC,CAEA,cAAc,UAAU,uBAAU,CACjC,UAAU,CACT,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC;AACnC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,YAAY,CAChC,CAEA,cAAc,OAAO,wBAAW,CAC/B,UAAU,CACT,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC;AACnC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CACjC,CAEA,cAAc,QAAQ,uBAAU,CAC/B,UAAU,CACT,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC;AACnC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,YAAY,CACjC,CAEA,cAAc,OAAO,QAAQ,uBAAU,CACtC,UAAU,CAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,YAAY,CAC5C,CAEA,cAAc,UAAU,QAAQ,uBAAU,CACzC,UAAU,CAAE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,YAAY,CAC3C,CAEA,cAAc,QAAQ,OAAO,wBAAW,CACvC,UAAU,CAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAC5C,CAEA,cAAc,SAAS,OAAO,wBAAW,CACxC,UAAU,CAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAC3C,CAEA,cAAc,OAAO,UAAU,QAAQ,uBAAU,CAChD,UAAU,CAAE,IACb"}'},On=create_ssr_component((n,e,t,a)=>{let A,d,l,o,s,i,{value:r}=e,{index:_}=e,{j:C}=e,{actual_pinned_columns:p}=e,{get_cell_width:b}=e,{handle_cell_click:k}=e,{handle_blur:S}=e,{toggle_cell_menu:h}=e,{is_cell_selected:j}=e,{should_show_cell_menu:K}=e,{selected_cells:g}=e,{copy_flash:T}=e,{active_cell_menu:w}=e,{styling:I}=e,{latex_delimiters:U}=e,{line_breaks:W}=e,{datatype:G}=e,{editing:Y}=e,{max_chars:y}=e,{editable:L}=e,{is_static:P=!1}=e,{i18n:D}=e,{components:M={}}=e,{el:f}=e,{handle_select_column:V}=e,{handle_select_row:N}=e,{is_dragging:q}=e,{display_value:$}=e,{wrap:tt=!1}=e;function R(Z){return Z>=p?"auto":Z===0?"0":`calc(${Array(Z).fill(0).map((bt,et)=>b(et)).join(" + ")})`}e.value===void 0&&t.value&&r!==void 0&&t.value(r),e.index===void 0&&t.index&&_!==void 0&&t.index(_),e.j===void 0&&t.j&&C!==void 0&&t.j(C),e.actual_pinned_columns===void 0&&t.actual_pinned_columns&&p!==void 0&&t.actual_pinned_columns(p),e.get_cell_width===void 0&&t.get_cell_width&&b!==void 0&&t.get_cell_width(b),e.handle_cell_click===void 0&&t.handle_cell_click&&k!==void 0&&t.handle_cell_click(k),e.handle_blur===void 0&&t.handle_blur&&S!==void 0&&t.handle_blur(S),e.toggle_cell_menu===void 0&&t.toggle_cell_menu&&h!==void 0&&t.toggle_cell_menu(h),e.is_cell_selected===void 0&&t.is_cell_selected&&j!==void 0&&t.is_cell_selected(j),e.should_show_cell_menu===void 0&&t.should_show_cell_menu&&K!==void 0&&t.should_show_cell_menu(K),e.selected_cells===void 0&&t.selected_cells&&g!==void 0&&t.selected_cells(g),e.copy_flash===void 0&&t.copy_flash&&T!==void 0&&t.copy_flash(T),e.active_cell_menu===void 0&&t.active_cell_menu&&w!==void 0&&t.active_cell_menu(w),e.styling===void 0&&t.styling&&I!==void 0&&t.styling(I),e.latex_delimiters===void 0&&t.latex_delimiters&&U!==void 0&&t.latex_delimiters(U),e.line_breaks===void 0&&t.line_breaks&&W!==void 0&&t.line_breaks(W),e.datatype===void 0&&t.datatype&&G!==void 0&&t.datatype(G),e.editing===void 0&&t.editing&&Y!==void 0&&t.editing(Y),e.max_chars===void 0&&t.max_chars&&y!==void 0&&t.max_chars(y),e.editable===void 0&&t.editable&&L!==void 0&&t.editable(L),e.is_static===void 0&&t.is_static&&P!==void 0&&t.is_static(P),e.i18n===void 0&&t.i18n&&D!==void 0&&t.i18n(D),e.components===void 0&&t.components&&M!==void 0&&t.components(M),e.el===void 0&&t.el&&f!==void 0&&t.el(f),e.handle_select_column===void 0&&t.handle_select_column&&V!==void 0&&t.handle_select_column(V),e.handle_select_row===void 0&&t.handle_select_row&&N!==void 0&&t.handle_select_row(N),e.is_dragging===void 0&&t.is_dragging&&q!==void 0&&t.is_dragging(q),e.display_value===void 0&&t.display_value&&$!==void 0&&t.display_value($),e.wrap===void 0&&t.wrap&&tt!==void 0&&t.wrap(tt),n.css.add(Un);let lt,ct,dt=n.head;do lt=!0,n.head=dt,A=j([_,C],g||[]),d=cn([_,C],g),l=A.includes("no-top"),o=A.includes("no-bottom"),s=A.includes("no-left"),i=A.includes("no-right"),ct=`<td${add_attribute("tabindex",C<p?-1:0,0)}${add_attribute("data-row",_,0)}${add_attribute("data-col",C,0)}${add_attribute("data-testid",`cell-${_}-${C}`,0)} style="${"width: "+escape(b(C),!0)+"; left: "+escape(R(C),!0)+"; "+escape(I||"",!0)}" class="${["svelte-v1pjjd",(C<p?"pinned-column":"")+" "+(C===p-1?"last-pinned":"")+" "+(T&&d?"flash":"")+" "+(d?"cell-selected":"")+" "+(l?"no-top":"")+" "+(o?"no-bottom":"")+" "+(s?"no-left":"")+" "+(i?"no-right":"")+" "+(w&&w.row===_&&w.col===C?"menu-active":"")+" "+(q?"dragging":"")].join(" ").trim()}"${add_attribute("this",f.cell,0)}><div class="cell-wrap svelte-v1pjjd">${validate_component(qt,"EditableCell").$$render(n,{display_value:$!==void 0?$:String(r),latex_delimiters:U,line_breaks:W,editable:L,is_static:P,edit:Y&&Y[0]===_&&Y[1]===C,datatype:G,max_chars:y,i18n:D,components:M,show_selection_buttons:g.length===1&&g[0][0]===_&&g[0][1]===C,coords:[_,C],on_select_column:V,on_select_row:N,is_dragging:q,wrap_text:tt,value:r,el:f.input},{value:Z=>{r=Z,lt=!1;},el:Z=>{f.input=Z,lt=!1;}},{})} ${L&&K([_,C],g,L)?`${validate_component(be,"CellMenuButton").$$render(n,{on_click:Z=>h(Z,_,C)},{},{})}`:""}</div> </td>`;while(!lt);return ct}),Gn={code:".add-row-button.svelte-jkwuz7{width:100%;padding:var(--size-1);background:transparent;border:1px dashed var(--border-color-primary);border-radius:var(--radius-sm);color:var(--body-text-color);cursor:pointer;transition:all 150ms;margin-top:var(--size-2);z-index:10;position:relative;pointer-events:auto}.add-row-button.svelte-jkwuz7:hover{background:var(--background-fill-secondary);border-style:solid}",map:'{"version":3,"file":"EmptyRowButton.svelte","sources":["EmptyRowButton.svelte"],"sourcesContent":["<script lang=\\"ts\\">export let on_click;\\n<\/script>\\n\\n<button class=\\"add-row-button\\" on:click={on_click} aria-label=\\"Add row\\">\\n\\t+\\n</button>\\n\\n<style>\\n\\t.add-row-button {\\n\\t\\twidth: 100%;\\n\\t\\tpadding: var(--size-1);\\n\\t\\tbackground: transparent;\\n\\t\\tborder: 1px dashed var(--border-color-primary);\\n\\t\\tborder-radius: var(--radius-sm);\\n\\t\\tcolor: var(--body-text-color);\\n\\t\\tcursor: pointer;\\n\\t\\ttransition: all 150ms;\\n\\t\\tmargin-top: var(--size-2);\\n\\t\\tz-index: 10;\\n\\t\\tposition: relative;\\n\\t\\tpointer-events: auto;\\n\\t}\\n\\n\\t.add-row-button:hover {\\n\\t\\tbackground: var(--background-fill-secondary);\\n\\t\\tborder-style: solid;\\n\\t}</style>\\n"],"names":[],"mappings":"AAQC,6BAAgB,CACf,KAAK,CAAE,IAAI,CACX,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,UAAU,CAAE,WAAW,CACvB,MAAM,CAAE,GAAG,CAAC,MAAM,CAAC,IAAI,sBAAsB,CAAC,CAC9C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,MAAM,CAAE,OAAO,CACf,UAAU,CAAE,GAAG,CAAC,KAAK,CACrB,UAAU,CAAE,IAAI,QAAQ,CAAC,CACzB,OAAO,CAAE,EAAE,CACX,QAAQ,CAAE,QAAQ,CAClB,cAAc,CAAE,IACjB,CAEA,6BAAe,MAAO,CACrB,UAAU,CAAE,IAAI,2BAA2B,CAAC,CAC5C,YAAY,CAAE,KACf"}'},Sn=create_ssr_component((n,e,t,a)=>{let{on_click:A}=e;return e.on_click===void 0&&t.on_click&&A!==void 0&&t.on_click(A),n.css.add(Gn),`<button class="add-row-button svelte-jkwuz7" aria-label="Add row" data-svelte-h="svelte-qq2si4">+
</button>`}),Wn={code:"table.svelte-zsmsrz.svelte-zsmsrz{position:relative;overflow:auto;-webkit-overflow-scrolling:touch;max-height:var(--max-height);box-sizing:border-box;display:block;padding:0;margin:0;color:var(--body-text-color);font-size:var(--input-text-size);line-height:var(--line-md);font-family:var(--font-mono);border-spacing:0;width:100%;scroll-snap-type:x proximity;border-collapse:separate;scrollbar-width:thin;scrollbar-color:rgba(128, 128, 128, 0.5) transparent}table.svelte-zsmsrz.svelte-zsmsrz::-webkit-scrollbar{width:4px;height:4px}table.svelte-zsmsrz.svelte-zsmsrz::-webkit-scrollbar-track{background:transparent}table.svelte-zsmsrz.svelte-zsmsrz::-webkit-scrollbar-thumb{background-color:rgba(128, 128, 128, 0.5);border-radius:4px}table.svelte-zsmsrz.svelte-zsmsrz:hover{scrollbar-color:rgba(160, 160, 160, 0.7) transparent}table.svelte-zsmsrz.svelte-zsmsrz:hover::-webkit-scrollbar-thumb{background-color:rgba(160, 160, 160, 0.7);border-radius:4px;width:4px}@media(hover: none){table.svelte-zsmsrz.svelte-zsmsrz{scrollbar-color:rgba(160, 160, 160, 0.7) transparent}table.svelte-zsmsrz.svelte-zsmsrz::-webkit-scrollbar-thumb{background-color:rgba(160, 160, 160, 0.7);border-radius:4px}}@media(pointer: coarse){table.svelte-zsmsrz.svelte-zsmsrz::-webkit-scrollbar{width:8px;height:8px}}table.svelte-zsmsrz .svelte-zsmsrz:is(thead, tfoot, tbody){display:table;table-layout:fixed;width:100%;box-sizing:border-box}tbody.svelte-zsmsrz.svelte-zsmsrz{overflow-x:scroll;overflow-y:hidden}table.svelte-zsmsrz tbody.svelte-zsmsrz{padding-top:var(--bw-svt-p-top);padding-bottom:var(--bw-svt-p-bottom)}tbody.svelte-zsmsrz.svelte-zsmsrz{position:relative;box-sizing:border-box;border:0px solid currentColor}tbody.svelte-zsmsrz>tr:last-child{border:none}table.svelte-zsmsrz td{scroll-snap-align:start}tbody.svelte-zsmsrz td.pinned-column{position:sticky;z-index:3}tbody.svelte-zsmsrz tr:nth-child(odd) td.pinned-column{background:var(--table-odd-background-fill)}tbody.svelte-zsmsrz tr:nth-child(even) td.pinned-column{background:var(--table-even-background-fill)}tbody.svelte-zsmsrz td.last-pinned{border-right:1px solid var(--border-color-primary)}thead.svelte-zsmsrz.svelte-zsmsrz{position:sticky;top:0;left:0;background:var(--background-fill-primary);z-index:7}thead.svelte-zsmsrz th{background:var(--table-even-background-fill) !important}thead.svelte-zsmsrz th.pinned-column{position:sticky;z-index:7;background:var(--table-even-background-fill) !important}thead.svelte-zsmsrz th.last-pinned{border-right:1px solid var(--border-color-primary)}.table.disable-scroll.svelte-zsmsrz.svelte-zsmsrz{overflow:hidden !important}",map:'{"version":3,"file":"VirtualTable.svelte","sources":["VirtualTable.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { onMount, tick, createEventDispatcher } from \\"svelte\\";\\nimport { _ } from \\"svelte-i18n\\";\\nexport let items = [];\\nexport let max_height;\\nexport let actual_height;\\nexport let table_scrollbar_width;\\nexport let start = 0;\\nexport let end = 20;\\nexport let selected;\\nexport let disable_scroll = false;\\nexport let show_scroll_button = false;\\nexport let viewport;\\nconst dispatch = createEventDispatcher();\\nlet height = \\"100%\\";\\nlet average_height = 30;\\nlet bottom = 0;\\nlet contents;\\nlet head_height = 0;\\nlet foot_height = 0;\\nlet height_map = [];\\nlet mounted;\\nlet rows;\\nlet top = 0;\\nlet viewport_height = 200;\\nlet visible = [];\\nlet viewport_box;\\n$: viewport_height = viewport_box?.height || 200;\\nconst is_browser = typeof window !== \\"undefined\\";\\nconst raf = is_browser ? window.requestAnimationFrame : (cb) => cb();\\n$: {\\n    if (mounted && viewport_height && viewport.offsetParent) {\\n        sortedItems, raf(refresh_height_map);\\n    }\\n}\\nasync function refresh_height_map() {\\n    if (sortedItems.length < start) {\\n        await scroll_to_index(sortedItems.length - 1, { behavior: \\"auto\\" });\\n    }\\n    const scrollTop = Math.max(0, viewport.scrollTop);\\n    show_scroll_button = scrollTop > 100;\\n    table_scrollbar_width = viewport.offsetWidth - viewport.clientWidth;\\n    for (let v = 0; v < rows.length; v += 1) {\\n        height_map[start + v] = rows[v].getBoundingClientRect().height;\\n    }\\n    let i = 0;\\n    let y = head_height;\\n    while (i < sortedItems.length) {\\n        const row_height = height_map[i] || average_height;\\n        if (y + row_height > scrollTop - max_height) {\\n            start = i;\\n            top = y - head_height;\\n            break;\\n        }\\n        y += row_height;\\n        i += 1;\\n    }\\n    let content_height = head_height;\\n    while (i < sortedItems.length) {\\n        const row_height = height_map[i] || average_height;\\n        content_height += row_height;\\n        i += 1;\\n        if (content_height - head_height > 3 * max_height) {\\n            break;\\n        }\\n    }\\n    end = i;\\n    const remaining = sortedItems.length - end;\\n    const scrollbar_height = viewport.offsetHeight - viewport.clientHeight;\\n    if (scrollbar_height > 0) {\\n        content_height += scrollbar_height;\\n    }\\n    let filtered_height_map = height_map.filter((v) => typeof v === \\"number\\");\\n    average_height = filtered_height_map.reduce((a, b) => a + b, 0) / filtered_height_map.length || 30;\\n    bottom = remaining * average_height;\\n    if (!isFinite(bottom)) {\\n        bottom = 2e5;\\n    }\\n    height_map.length = sortedItems.length;\\n    while (i < sortedItems.length) {\\n        i += 1;\\n        height_map[i] = average_height;\\n    }\\n    if (max_height && content_height > max_height) {\\n        actual_height = max_height;\\n    }\\n    else {\\n        actual_height = content_height;\\n    }\\n}\\n$: scroll_and_render(selected);\\nasync function scroll_and_render(n) {\\n    raf(async () => {\\n        if (typeof n !== \\"number\\")\\n            return;\\n        const direction = typeof n !== \\"number\\" ? false : is_in_view(n);\\n        if (direction === true) {\\n            return;\\n        }\\n        if (direction === \\"back\\") {\\n            await scroll_to_index(n, { behavior: \\"instant\\" });\\n        }\\n        if (direction === \\"forwards\\") {\\n            await scroll_to_index(n, { behavior: \\"instant\\" }, true);\\n        }\\n    });\\n}\\nfunction is_in_view(n) {\\n    const current = rows && rows[n - start];\\n    if (!current && n < start) {\\n        return \\"back\\";\\n    }\\n    if (!current && n >= end - 1) {\\n        return \\"forwards\\";\\n    }\\n    const { top: viewport_top } = viewport.getBoundingClientRect();\\n    const { top: top2, bottom: bottom2 } = current.getBoundingClientRect();\\n    if (top2 - viewport_top < 37) {\\n        return \\"back\\";\\n    }\\n    if (bottom2 - viewport_top > viewport_height) {\\n        return \\"forwards\\";\\n    }\\n    return true;\\n}\\nexport async function scroll_to_index(index, opts, align_end = false) {\\n    await tick();\\n    const _itemHeight = average_height;\\n    let distance = index * _itemHeight;\\n    if (align_end) {\\n        distance = distance - viewport_height + _itemHeight + head_height;\\n    }\\n    const scrollbar_height = viewport.offsetHeight - viewport.clientHeight;\\n    if (scrollbar_height > 0) {\\n        distance += scrollbar_height;\\n    }\\n    const _opts = {\\n        top: distance,\\n        behavior: \\"smooth\\",\\n        ...opts\\n    };\\n    viewport.scrollTo(_opts);\\n}\\n$: sortedItems = items;\\n$: visible = is_browser ? sortedItems.slice(start, end).map((data, i) => {\\n    return { index: i + start, data };\\n}) : sortedItems.slice(0, max_height / sortedItems.length * average_height + 1).map((data, i) => {\\n    return { index: i + start, data };\\n});\\nonMount(() => {\\n    rows = contents.children;\\n    mounted = true;\\n});\\n<\/script>\\n\\n<svelte-virtual-table-viewport>\\n\\t<div>\\n\\t\\t<table\\n\\t\\t\\tclass=\\"table\\"\\n\\t\\t\\tclass:disable-scroll={disable_scroll}\\n\\t\\t\\tbind:this={viewport}\\n\\t\\t\\tbind:contentRect={viewport_box}\\n\\t\\t\\ton:scroll={refresh_height_map}\\n\\t\\t\\tstyle=\\"height: {height}; --bw-svt-p-top: {top}px; --bw-svt-p-bottom: {bottom}px; --bw-svt-head-height: {head_height}px; --bw-svt-foot-height: {foot_height}px; --bw-svt-avg-row-height: {average_height}px; --max-height: {max_height}px\\"\\n\\t\\t>\\n\\t\\t\\t<thead class=\\"thead\\" bind:offsetHeight={head_height}>\\n\\t\\t\\t\\t<slot name=\\"thead\\" />\\n\\t\\t\\t</thead>\\n\\t\\t\\t<tbody bind:this={contents} class=\\"tbody\\">\\n\\t\\t\\t\\t{#if visible.length && visible[0].data.length}\\n\\t\\t\\t\\t\\t{#each visible as item (item.data[0].id)}\\n\\t\\t\\t\\t\\t\\t<slot name=\\"tbody\\" item={item.data} index={item.index}>\\n\\t\\t\\t\\t\\t\\t\\tMissing Table Row\\n\\t\\t\\t\\t\\t\\t</slot>\\n\\t\\t\\t\\t\\t{/each}\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t</tbody>\\n\\t\\t\\t<tfoot class=\\"tfoot\\" bind:offsetHeight={foot_height}>\\n\\t\\t\\t\\t<slot name=\\"tfoot\\" />\\n\\t\\t\\t</tfoot>\\n\\t\\t</table>\\n\\t</div>\\n</svelte-virtual-table-viewport>\\n\\n<style type=\\"text/css\\">\\n\\ttable {\\n\\t\\tposition: relative;\\n\\t\\toverflow: auto;\\n\\t\\t-webkit-overflow-scrolling: touch;\\n\\t\\tmax-height: var(--max-height);\\n\\t\\tbox-sizing: border-box;\\n\\t\\tdisplay: block;\\n\\t\\tpadding: 0;\\n\\t\\tmargin: 0;\\n\\t\\tcolor: var(--body-text-color);\\n\\t\\tfont-size: var(--input-text-size);\\n\\t\\tline-height: var(--line-md);\\n\\t\\tfont-family: var(--font-mono);\\n\\t\\tborder-spacing: 0;\\n\\t\\twidth: 100%;\\n\\t\\tscroll-snap-type: x proximity;\\n\\t\\tborder-collapse: separate;\\n\\t\\tscrollbar-width: thin;\\n\\t\\tscrollbar-color: rgba(128, 128, 128, 0.5) transparent;\\n\\t}\\n\\n\\ttable::-webkit-scrollbar {\\n\\t\\twidth: 4px;\\n\\t\\theight: 4px;\\n\\t}\\n\\n\\ttable::-webkit-scrollbar-track {\\n\\t\\tbackground: transparent;\\n\\t}\\n\\n\\ttable::-webkit-scrollbar-thumb {\\n\\t\\tbackground-color: rgba(128, 128, 128, 0.5);\\n\\t\\tborder-radius: 4px;\\n\\t}\\n\\n\\ttable:hover {\\n\\t\\tscrollbar-color: rgba(160, 160, 160, 0.7) transparent;\\n\\t}\\n\\n\\ttable:hover::-webkit-scrollbar-thumb {\\n\\t\\tbackground-color: rgba(160, 160, 160, 0.7);\\n\\t\\tborder-radius: 4px;\\n\\t\\twidth: 4px;\\n\\t}\\n\\n\\t@media (hover: none) {\\n\\t\\ttable {\\n\\t\\t\\tscrollbar-color: rgba(160, 160, 160, 0.7) transparent;\\n\\t\\t}\\n\\n\\t\\ttable::-webkit-scrollbar-thumb {\\n\\t\\t\\tbackground-color: rgba(160, 160, 160, 0.7);\\n\\t\\t\\tborder-radius: 4px;\\n\\t\\t}\\n\\t}\\n\\n\\t@media (pointer: coarse) {\\n\\t\\ttable::-webkit-scrollbar {\\n\\t\\t\\twidth: 8px;\\n\\t\\t\\theight: 8px;\\n\\t\\t}\\n\\t}\\n\\n\\ttable :is(thead, tfoot, tbody) {\\n\\t\\tdisplay: table;\\n\\t\\ttable-layout: fixed;\\n\\t\\twidth: 100%;\\n\\t\\tbox-sizing: border-box;\\n\\t}\\n\\n\\ttbody {\\n\\t\\toverflow-x: scroll;\\n\\t\\toverflow-y: hidden;\\n\\t}\\n\\n\\ttable tbody {\\n\\t\\tpadding-top: var(--bw-svt-p-top);\\n\\t\\tpadding-bottom: var(--bw-svt-p-bottom);\\n\\t}\\n\\ttbody {\\n\\t\\tposition: relative;\\n\\t\\tbox-sizing: border-box;\\n\\t\\tborder: 0px solid currentColor;\\n\\t}\\n\\n\\ttbody > :global(tr:last-child) {\\n\\t\\tborder: none;\\n\\t}\\n\\n\\ttable :global(td) {\\n\\t\\tscroll-snap-align: start;\\n\\t}\\n\\n\\ttbody :global(td.pinned-column) {\\n\\t\\tposition: sticky;\\n\\t\\tz-index: 3;\\n\\t}\\n\\n\\ttbody :global(tr:nth-child(odd)) :global(td.pinned-column) {\\n\\t\\tbackground: var(--table-odd-background-fill);\\n\\t}\\n\\n\\ttbody :global(tr:nth-child(even)) :global(td.pinned-column) {\\n\\t\\tbackground: var(--table-even-background-fill);\\n\\t}\\n\\n\\ttbody :global(td.last-pinned) {\\n\\t\\tborder-right: 1px solid var(--border-color-primary);\\n\\t}\\n\\n\\tthead {\\n\\t\\tposition: sticky;\\n\\t\\ttop: 0;\\n\\t\\tleft: 0;\\n\\t\\tbackground: var(--background-fill-primary);\\n\\t\\tz-index: 7;\\n\\t}\\n\\n\\tthead :global(th) {\\n\\t\\tbackground: var(--table-even-background-fill) !important;\\n\\t}\\n\\n\\tthead :global(th.pinned-column) {\\n\\t\\tposition: sticky;\\n\\t\\tz-index: 7;\\n\\t\\tbackground: var(--table-even-background-fill) !important;\\n\\t}\\n\\n\\tthead :global(th.last-pinned) {\\n\\t\\tborder-right: 1px solid var(--border-color-primary);\\n\\t}\\n\\n\\t.table.disable-scroll {\\n\\t\\toverflow: hidden !important;\\n\\t}</style>\\n"],"names":[],"mappings":"AAwLC,iCAAM,CACL,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,IAAI,CACd,0BAA0B,CAAE,KAAK,CACjC,UAAU,CAAE,IAAI,YAAY,CAAC,CAC7B,UAAU,CAAE,UAAU,CACtB,OAAO,CAAE,KAAK,CACd,OAAO,CAAE,CAAC,CACV,MAAM,CAAE,CAAC,CACT,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,SAAS,CAAE,IAAI,iBAAiB,CAAC,CACjC,WAAW,CAAE,IAAI,SAAS,CAAC,CAC3B,WAAW,CAAE,IAAI,WAAW,CAAC,CAC7B,cAAc,CAAE,CAAC,CACjB,KAAK,CAAE,IAAI,CACX,gBAAgB,CAAE,CAAC,CAAC,SAAS,CAC7B,eAAe,CAAE,QAAQ,CACzB,eAAe,CAAE,IAAI,CACrB,eAAe,CAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,WAC3C,CAEA,iCAAK,mBAAoB,CACxB,KAAK,CAAE,GAAG,CACV,MAAM,CAAE,GACT,CAEA,iCAAK,yBAA0B,CAC9B,UAAU,CAAE,WACb,CAEA,iCAAK,yBAA0B,CAC9B,gBAAgB,CAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAC1C,aAAa,CAAE,GAChB,CAEA,iCAAK,MAAO,CACX,eAAe,CAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,WAC3C,CAEA,iCAAK,MAAM,yBAA0B,CACpC,gBAAgB,CAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAC1C,aAAa,CAAE,GAAG,CAClB,KAAK,CAAE,GACR,CAEA,MAAO,QAAQ,IAAI,CAAE,CACpB,iCAAM,CACL,eAAe,CAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,WAC3C,CAEA,iCAAK,yBAA0B,CAC9B,gBAAgB,CAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAC1C,aAAa,CAAE,GAChB,CACD,CAEA,MAAO,UAAU,MAAM,CAAE,CACxB,iCAAK,mBAAoB,CACxB,KAAK,CAAE,GAAG,CACV,MAAM,CAAE,GACT,CACD,CAEA,mBAAK,eAAC,IAAI,KAAK,EAAE,KAAK,EAAE,KAAK,CAAE,CAC9B,OAAO,CAAE,KAAK,CACd,YAAY,CAAE,KAAK,CACnB,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,UACb,CAEA,iCAAM,CACL,UAAU,CAAE,MAAM,CAClB,UAAU,CAAE,MACb,CAEA,mBAAK,CAAC,mBAAM,CACX,WAAW,CAAE,IAAI,cAAc,CAAC,CAChC,cAAc,CAAE,IAAI,iBAAiB,CACtC,CACA,iCAAM,CACL,QAAQ,CAAE,QAAQ,CAClB,UAAU,CAAE,UAAU,CACtB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,YACnB,CAEA,mBAAK,CAAW,aAAe,CAC9B,MAAM,CAAE,IACT,CAEA,mBAAK,CAAS,EAAI,CACjB,iBAAiB,CAAE,KACpB,CAEA,mBAAK,CAAS,gBAAkB,CAC/B,QAAQ,CAAE,MAAM,CAChB,OAAO,CAAE,CACV,CAEA,mBAAK,CAAS,iBAAkB,CAAS,gBAAkB,CAC1D,UAAU,CAAE,IAAI,2BAA2B,CAC5C,CAEA,mBAAK,CAAS,kBAAmB,CAAS,gBAAkB,CAC3D,UAAU,CAAE,IAAI,4BAA4B,CAC7C,CAEA,mBAAK,CAAS,cAAgB,CAC7B,YAAY,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CACnD,CAEA,iCAAM,CACL,QAAQ,CAAE,MAAM,CAChB,GAAG,CAAE,CAAC,CACN,IAAI,CAAE,CAAC,CACP,UAAU,CAAE,IAAI,yBAAyB,CAAC,CAC1C,OAAO,CAAE,CACV,CAEA,mBAAK,CAAS,EAAI,CACjB,UAAU,CAAE,IAAI,4BAA4B,CAAC,CAAC,UAC/C,CAEA,mBAAK,CAAS,gBAAkB,CAC/B,QAAQ,CAAE,MAAM,CAChB,OAAO,CAAE,CAAC,CACV,UAAU,CAAE,IAAI,4BAA4B,CAAC,CAAC,UAC/C,CAEA,mBAAK,CAAS,cAAgB,CAC7B,YAAY,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CACnD,CAEA,MAAM,2CAAgB,CACrB,QAAQ,CAAE,MAAM,CAAC,UAClB"}'};let jn="100%";const Yn=create_ssr_component((n,e,t,a)=>{let A,{items:d=[]}=e,{max_height:l}=e,{actual_height:o}=e,{table_scrollbar_width:s}=e,{start:i=0}=e,{end:r=20}=e,{selected:_}=e,{disable_scroll:C=!1}=e,{show_scroll_button:p=!1}=e,{viewport:b}=e;createEventDispatcher();let k=30,S=0,h,j=0,K=0,w,I=0,U=200,W=[];const G=typeof window<"u",Y=G?window.requestAnimationFrame:M=>M();async function L(M){Y(async()=>{if(typeof M!="number")return;const f=typeof M!="number"?!1:P(M);f!==!0&&(f==="back"&&await D(M,{behavior:"instant"}),f==="forwards"&&await D(M,{behavior:"instant"},!0));});}function P(M){const f=w;if(M<i)return "back";if(M>=r-1)return "forwards";const{top:V}=b.getBoundingClientRect(),{top:N,bottom:q}=f.getBoundingClientRect();return N-V<37?"back":q-V>U?"forwards":!0}async function D(M,f,V=!1){await tick();const N=k;let q=M*N;V&&(q=q-U+N+j);const $=b.offsetHeight-b.clientHeight;$>0&&(q+=$);const tt={top:q,behavior:"smooth",...f};b.scrollTo(tt);}return e.items===void 0&&t.items&&d!==void 0&&t.items(d),e.max_height===void 0&&t.max_height&&l!==void 0&&t.max_height(l),e.actual_height===void 0&&t.actual_height&&o!==void 0&&t.actual_height(o),e.table_scrollbar_width===void 0&&t.table_scrollbar_width&&s!==void 0&&t.table_scrollbar_width(s),e.start===void 0&&t.start&&i!==void 0&&t.start(i),e.end===void 0&&t.end&&r!==void 0&&t.end(r),e.selected===void 0&&t.selected&&_!==void 0&&t.selected(_),e.disable_scroll===void 0&&t.disable_scroll&&C!==void 0&&t.disable_scroll(C),e.show_scroll_button===void 0&&t.show_scroll_button&&p!==void 0&&t.show_scroll_button(p),e.viewport===void 0&&t.viewport&&b!==void 0&&t.viewport(b),e.scroll_to_index===void 0&&t.scroll_to_index&&D!==void 0&&t.scroll_to_index(D),n.css.add(Wn),U=200,A=d,L(_),W=G?A.slice(i,r).map((M,f)=>({index:f+i,data:M})):A.slice(0,l/A.length*k+1).map((M,f)=>({index:f+i,data:M})),`<svelte-virtual-table-viewport><div><table class="${["table svelte-zsmsrz",C?"disable-scroll":""].join(" ").trim()}" style="${"height: "+escape(jn,!0)+"; --bw-svt-p-top: "+escape(I,!0)+"px; --bw-svt-p-bottom: "+escape(S,!0)+"px; --bw-svt-head-height: "+escape(j,!0)+"px; --bw-svt-foot-height: "+escape(K,!0)+"px; --bw-svt-avg-row-height: "+escape(k,!0)+"px; --max-height: "+escape(l,!0)+"px"}"${add_attribute("this",b,0)}><thead class="thead svelte-zsmsrz">${a.thead?a.thead({}):""}</thead> <tbody class="tbody svelte-zsmsrz"${add_attribute("this",h,0)}>${W.length&&W[0].data.length?`${each(W,M=>`${a.tbody?a.tbody({item:M.data,index:M.index}):`
							Missing Table Row
						`}`)}`:""}</tbody> <tfoot class="tfoot svelte-zsmsrz">${a.tfoot?a.tfoot({}):""}</tfoot></table></div> </svelte-virtual-table-viewport>`});const Nn={code:`.cell-menu.svelte-42thj4.svelte-42thj4{position:fixed;z-index:9;background:var(--background-fill-primary);border:1px solid var(--border-color-primary);border-radius:var(--radius-sm);padding:var(--size-1);display:flex;flex-direction:column;gap:var(--size-1);box-shadow:var(--shadow-drop-lg);min-width:150px;z-index:var(--layer-1)}.cell-menu.svelte-42thj4 button.svelte-42thj4{background:none;border:none;cursor:pointer;text-align:left;padding:var(--size-1) var(--size-2);border-radius:var(--radius-sm);color:var(--body-text-color);font-size:var(--text-sm);transition:background-color 0.2s,
			color 0.2s;display:flex;align-items:center;gap:var(--size-2);position:relative}.cell-menu.svelte-42thj4 button.active.svelte-42thj4{background-color:var(--background-fill-secondary)}.cell-menu.svelte-42thj4 button.svelte-42thj4:hover{background-color:var(--background-fill-secondary)}.cell-menu.svelte-42thj4 button.svelte-42thj4 svg{fill:currentColor;transition:fill 0.2s}.priority.svelte-42thj4.svelte-42thj4{display:flex;align-items:center;justify-content:center;margin-left:auto;font-size:var(--size-2);background-color:var(--button-secondary-background-fill);color:var(--body-text-color);border-radius:var(--radius-sm);width:var(--size-2-5);height:var(--size-2-5)}`,map:'{"version":3,"file":"CellMenu.svelte","sources":["CellMenu.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { onMount } from \\"svelte\\";\\nimport CellMenuIcons from \\"./CellMenuIcons.svelte\\";\\nimport FilterMenu from \\"./FilterMenu.svelte\\";\\nexport let x;\\nexport let y;\\nexport let on_add_row_above;\\nexport let on_add_row_below;\\nexport let on_add_column_left;\\nexport let on_add_column_right;\\nexport let row;\\nexport let col_count;\\nexport let row_count;\\nexport let on_delete_row;\\nexport let on_delete_col;\\nexport let can_delete_rows;\\nexport let can_delete_cols;\\nexport let on_sort = () => {\\n};\\nexport let on_clear_sort = () => {\\n};\\nexport let sort_direction = null;\\nexport let sort_priority = null;\\nexport let on_filter = () => {\\n};\\nexport let on_clear_filter = () => {\\n};\\nexport let filter_active = null;\\nexport let editable = true;\\nexport let i18n;\\nlet menu_element;\\nlet active_filter_menu = null;\\n$: is_header = row === -1;\\n$: can_add_rows = editable && row_count[1] === \\"dynamic\\";\\n$: can_add_columns = editable && col_count[1] === \\"dynamic\\";\\nonMount(() => {\\n    position_menu();\\n});\\nfunction position_menu() {\\n    if (!menu_element)\\n        return;\\n    const viewport_width = window.innerWidth;\\n    const viewport_height = window.innerHeight;\\n    const menu_rect = menu_element.getBoundingClientRect();\\n    let new_x = x - 30;\\n    let new_y = y - 20;\\n    if (new_x + menu_rect.width > viewport_width) {\\n        new_x = x - menu_rect.width + 10;\\n    }\\n    if (new_y + menu_rect.height > viewport_height) {\\n        new_y = y - menu_rect.height + 10;\\n    }\\n    menu_element.style.left = `${new_x}px`;\\n    menu_element.style.top = `${new_y}px`;\\n}\\nfunction toggle_filter_menu() {\\n    if (filter_active) {\\n        on_filter(\\"string\\", \\"\\", \\"\\");\\n        return;\\n    }\\n    const menu_rect = menu_element.getBoundingClientRect();\\n    active_filter_menu = {\\n        x: menu_rect.right,\\n        y: menu_rect.top + menu_rect.height / 2\\n    };\\n}\\n<\/script>\\n\\n<div bind:this={menu_element} class=\\"cell-menu\\" role=\\"menu\\">\\n\\t{#if is_header}\\n\\t\\t<button\\n\\t\\t\\trole=\\"menuitem\\"\\n\\t\\t\\ton:click={() => on_sort(\\"asc\\")}\\n\\t\\t\\tclass:active={sort_direction === \\"asc\\"}\\n\\t\\t>\\n\\t\\t\\t<CellMenuIcons icon=\\"sort-asc\\" />\\n\\t\\t\\t{i18n(\\"dataframe.sort_ascending\\")}\\n\\t\\t\\t{#if sort_direction === \\"asc\\" && sort_priority !== null}\\n\\t\\t\\t\\t<span class=\\"priority\\">{sort_priority}</span>\\n\\t\\t\\t{/if}\\n\\t\\t</button>\\n\\t\\t<button\\n\\t\\t\\trole=\\"menuitem\\"\\n\\t\\t\\ton:click={() => on_sort(\\"desc\\")}\\n\\t\\t\\tclass:active={sort_direction === \\"desc\\"}\\n\\t\\t>\\n\\t\\t\\t<CellMenuIcons icon=\\"sort-desc\\" />\\n\\t\\t\\t{i18n(\\"dataframe.sort_descending\\")}\\n\\t\\t\\t{#if sort_direction === \\"desc\\" && sort_priority !== null}\\n\\t\\t\\t\\t<span class=\\"priority\\">{sort_priority}</span>\\n\\t\\t\\t{/if}\\n\\t\\t</button>\\n\\t\\t<button role=\\"menuitem\\" on:click={on_clear_sort}>\\n\\t\\t\\t<CellMenuIcons icon=\\"clear-sort\\" />\\n\\t\\t\\t{i18n(\\"dataframe.clear_sort\\")}\\n\\t\\t</button>\\n\\t\\t<button\\n\\t\\t\\trole=\\"menuitem\\"\\n\\t\\t\\ton:click|stopPropagation={toggle_filter_menu}\\n\\t\\t\\tclass:active={filter_active || active_filter_menu}\\n\\t\\t>\\n\\t\\t\\t<CellMenuIcons icon=\\"filter\\" />\\n\\t\\t\\t{i18n(\\"dataframe.filter\\")}\\n\\t\\t\\t{#if filter_active}\\n\\t\\t\\t\\t<span class=\\"priority\\">1</span>\\n\\t\\t\\t{/if}\\n\\t\\t</button>\\n\\t\\t<button role=\\"menuitem\\" on:click={on_clear_filter}>\\n\\t\\t\\t<CellMenuIcons icon=\\"clear-filter\\" />\\n\\t\\t\\t{i18n(\\"dataframe.clear_filter\\")}\\n\\t\\t</button>\\n\\t{/if}\\n\\n\\t{#if !is_header && can_add_rows}\\n\\t\\t<button\\n\\t\\t\\trole=\\"menuitem\\"\\n\\t\\t\\ton:click={() => on_add_row_above()}\\n\\t\\t\\taria-label=\\"Add row above\\"\\n\\t\\t>\\n\\t\\t\\t<CellMenuIcons icon=\\"add-row-above\\" />\\n\\t\\t\\t{i18n(\\"dataframe.add_row_above\\")}\\n\\t\\t</button>\\n\\t\\t<button\\n\\t\\t\\trole=\\"menuitem\\"\\n\\t\\t\\ton:click={() => on_add_row_below()}\\n\\t\\t\\taria-label=\\"Add row below\\"\\n\\t\\t>\\n\\t\\t\\t<CellMenuIcons icon=\\"add-row-below\\" />\\n\\t\\t\\t{i18n(\\"dataframe.add_row_below\\")}\\n\\t\\t</button>\\n\\t\\t{#if can_delete_rows}\\n\\t\\t\\t<button\\n\\t\\t\\t\\trole=\\"menuitem\\"\\n\\t\\t\\t\\ton:click={on_delete_row}\\n\\t\\t\\t\\tclass=\\"delete\\"\\n\\t\\t\\t\\taria-label=\\"Delete row\\"\\n\\t\\t\\t>\\n\\t\\t\\t\\t<CellMenuIcons icon=\\"delete-row\\" />\\n\\t\\t\\t\\t{i18n(\\"dataframe.delete_row\\")}\\n\\t\\t\\t</button>\\n\\t\\t{/if}\\n\\t{/if}\\n\\t{#if can_add_columns}\\n\\t\\t<button\\n\\t\\t\\trole=\\"menuitem\\"\\n\\t\\t\\ton:click={() => on_add_column_left()}\\n\\t\\t\\taria-label=\\"Add column to the left\\"\\n\\t\\t>\\n\\t\\t\\t<CellMenuIcons icon=\\"add-column-left\\" />\\n\\t\\t\\t{i18n(\\"dataframe.add_column_left\\")}\\n\\t\\t</button>\\n\\t\\t<button\\n\\t\\t\\trole=\\"menuitem\\"\\n\\t\\t\\ton:click={() => on_add_column_right()}\\n\\t\\t\\taria-label=\\"Add column to the right\\"\\n\\t\\t>\\n\\t\\t\\t<CellMenuIcons icon=\\"add-column-right\\" />\\n\\t\\t\\t{i18n(\\"dataframe.add_column_right\\")}\\n\\t\\t</button>\\n\\t\\t{#if can_delete_cols}\\n\\t\\t\\t<button\\n\\t\\t\\t\\trole=\\"menuitem\\"\\n\\t\\t\\t\\ton:click={on_delete_col}\\n\\t\\t\\t\\tclass=\\"delete\\"\\n\\t\\t\\t\\taria-label=\\"Delete column\\"\\n\\t\\t\\t>\\n\\t\\t\\t\\t<CellMenuIcons icon=\\"delete-column\\" />\\n\\t\\t\\t\\t{i18n(\\"dataframe.delete_column\\")}\\n\\t\\t\\t</button>\\n\\t\\t{/if}\\n\\t{/if}\\n</div>\\n\\n{#if active_filter_menu}\\n\\t<FilterMenu {on_filter} />\\n{/if}\\n\\n<style>\\n\\t.cell-menu {\\n\\t\\tposition: fixed;\\n\\t\\tz-index: 9;\\n\\t\\tbackground: var(--background-fill-primary);\\n\\t\\tborder: 1px solid var(--border-color-primary);\\n\\t\\tborder-radius: var(--radius-sm);\\n\\t\\tpadding: var(--size-1);\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: column;\\n\\t\\tgap: var(--size-1);\\n\\t\\tbox-shadow: var(--shadow-drop-lg);\\n\\t\\tmin-width: 150px;\\n\\t\\tz-index: var(--layer-1);\\n\\t}\\n\\n\\t.cell-menu button {\\n\\t\\tbackground: none;\\n\\t\\tborder: none;\\n\\t\\tcursor: pointer;\\n\\t\\ttext-align: left;\\n\\t\\tpadding: var(--size-1) var(--size-2);\\n\\t\\tborder-radius: var(--radius-sm);\\n\\t\\tcolor: var(--body-text-color);\\n\\t\\tfont-size: var(--text-sm);\\n\\t\\ttransition:\\n\\t\\t\\tbackground-color 0.2s,\\n\\t\\t\\tcolor 0.2s;\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tgap: var(--size-2);\\n\\t\\tposition: relative;\\n\\t}\\n\\n\\t.cell-menu button.active {\\n\\t\\tbackground-color: var(--background-fill-secondary);\\n\\t}\\n\\n\\t.cell-menu button:hover {\\n\\t\\tbackground-color: var(--background-fill-secondary);\\n\\t}\\n\\n\\t.cell-menu button :global(svg) {\\n\\t\\tfill: currentColor;\\n\\t\\ttransition: fill 0.2s;\\n\\t}\\n\\n\\t.priority {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tjustify-content: center;\\n\\t\\tmargin-left: auto;\\n\\t\\tfont-size: var(--size-2);\\n\\t\\tbackground-color: var(--button-secondary-background-fill);\\n\\t\\tcolor: var(--body-text-color);\\n\\t\\tborder-radius: var(--radius-sm);\\n\\t\\twidth: var(--size-2-5);\\n\\t\\theight: var(--size-2-5);\\n\\t}</style>\\n"],"names":[],"mappings":"AAiLC,sCAAW,CACV,QAAQ,CAAE,KAAK,CACf,OAAO,CAAE,CAAC,CACV,UAAU,CAAE,IAAI,yBAAyB,CAAC,CAC1C,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,GAAG,CAAE,IAAI,QAAQ,CAAC,CAClB,UAAU,CAAE,IAAI,gBAAgB,CAAC,CACjC,SAAS,CAAE,KAAK,CAChB,OAAO,CAAE,IAAI,SAAS,CACvB,CAEA,wBAAU,CAAC,oBAAO,CACjB,UAAU,CAAE,IAAI,CAChB,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,OAAO,CACf,UAAU,CAAE,IAAI,CAChB,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CACpC,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,UAAU,CACT,gBAAgB,CAAC,IAAI,CAAC;AACzB,GAAG,KAAK,CAAC,IAAI,CACX,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,GAAG,CAAE,IAAI,QAAQ,CAAC,CAClB,QAAQ,CAAE,QACX,CAEA,wBAAU,CAAC,MAAM,qBAAQ,CACxB,gBAAgB,CAAE,IAAI,2BAA2B,CAClD,CAEA,wBAAU,CAAC,oBAAM,MAAO,CACvB,gBAAgB,CAAE,IAAI,2BAA2B,CAClD,CAEA,wBAAU,CAAC,oBAAM,CAAS,GAAK,CAC9B,IAAI,CAAE,YAAY,CAClB,UAAU,CAAE,IAAI,CAAC,IAClB,CAEA,qCAAU,CACT,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,IAAI,CACjB,SAAS,CAAE,IAAI,QAAQ,CAAC,CACxB,gBAAgB,CAAE,IAAI,kCAAkC,CAAC,CACzD,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,KAAK,CAAE,IAAI,UAAU,CAAC,CACtB,MAAM,CAAE,IAAI,UAAU,CACvB"}'},qn=create_ssr_component((n,e,t,a)=>{let A,d,l,{x:o}=e,{y:s}=e,{on_add_row_above:i}=e,{on_add_row_below:r}=e,{on_add_column_left:_}=e,{on_add_column_right:C}=e,{row:p}=e,{col_count:b}=e,{row_count:k}=e,{on_delete_row:S}=e,{on_delete_col:h}=e,{can_delete_rows:j}=e,{can_delete_cols:K}=e,{on_sort:g=()=>{}}=e,{on_clear_sort:T=()=>{}}=e,{sort_direction:w=null}=e,{sort_priority:I=null}=e,{on_filter:U=()=>{}}=e,{on_clear_filter:W=()=>{}}=e,{filter_active:G=null}=e,{editable:Y=!0}=e,{i18n:y}=e,L,P=null;return e.x===void 0&&t.x&&o!==void 0&&t.x(o),e.y===void 0&&t.y&&s!==void 0&&t.y(s),e.on_add_row_above===void 0&&t.on_add_row_above&&i!==void 0&&t.on_add_row_above(i),e.on_add_row_below===void 0&&t.on_add_row_below&&r!==void 0&&t.on_add_row_below(r),e.on_add_column_left===void 0&&t.on_add_column_left&&_!==void 0&&t.on_add_column_left(_),e.on_add_column_right===void 0&&t.on_add_column_right&&C!==void 0&&t.on_add_column_right(C),e.row===void 0&&t.row&&p!==void 0&&t.row(p),e.col_count===void 0&&t.col_count&&b!==void 0&&t.col_count(b),e.row_count===void 0&&t.row_count&&k!==void 0&&t.row_count(k),e.on_delete_row===void 0&&t.on_delete_row&&S!==void 0&&t.on_delete_row(S),e.on_delete_col===void 0&&t.on_delete_col&&h!==void 0&&t.on_delete_col(h),e.can_delete_rows===void 0&&t.can_delete_rows&&j!==void 0&&t.can_delete_rows(j),e.can_delete_cols===void 0&&t.can_delete_cols&&K!==void 0&&t.can_delete_cols(K),e.on_sort===void 0&&t.on_sort&&g!==void 0&&t.on_sort(g),e.on_clear_sort===void 0&&t.on_clear_sort&&T!==void 0&&t.on_clear_sort(T),e.sort_direction===void 0&&t.sort_direction&&w!==void 0&&t.sort_direction(w),e.sort_priority===void 0&&t.sort_priority&&I!==void 0&&t.sort_priority(I),e.on_filter===void 0&&t.on_filter&&U!==void 0&&t.on_filter(U),e.on_clear_filter===void 0&&t.on_clear_filter&&W!==void 0&&t.on_clear_filter(W),e.filter_active===void 0&&t.filter_active&&G!==void 0&&t.filter_active(G),e.editable===void 0&&t.editable&&Y!==void 0&&t.editable(Y),e.i18n===void 0&&t.i18n&&y!==void 0&&t.i18n(y),n.css.add(Nn),A=p===-1,d=Y&&k[1]==="dynamic",l=Y&&b[1]==="dynamic",`<div class="cell-menu svelte-42thj4" role="menu"${add_attribute("this",L,0)}>${A?`<button role="menuitem" class="${["svelte-42thj4",w==="asc"?"active":""].join(" ").trim()}">${validate_component(rt,"CellMenuIcons").$$render(n,{icon:"sort-asc"},{},{})} ${escape(y("dataframe.sort_ascending"))} ${w==="asc"&&I!==null?`<span class="priority svelte-42thj4">${escape(I)}</span>`:""}</button> <button role="menuitem" class="${["svelte-42thj4",w==="desc"?"active":""].join(" ").trim()}">${validate_component(rt,"CellMenuIcons").$$render(n,{icon:"sort-desc"},{},{})} ${escape(y("dataframe.sort_descending"))} ${w==="desc"&&I!==null?`<span class="priority svelte-42thj4">${escape(I)}</span>`:""}</button> <button role="menuitem" class="svelte-42thj4">${validate_component(rt,"CellMenuIcons").$$render(n,{icon:"clear-sort"},{},{})} ${escape(y("dataframe.clear_sort"))}</button> <button role="menuitem" class="${["svelte-42thj4",G||P?"active":""].join(" ").trim()}">${validate_component(rt,"CellMenuIcons").$$render(n,{icon:"filter"},{},{})} ${escape(y("dataframe.filter"))} ${G?'<span class="priority svelte-42thj4" data-svelte-h="svelte-1abh2by">1</span>':""}</button> <button role="menuitem" class="svelte-42thj4">${validate_component(rt,"CellMenuIcons").$$render(n,{icon:"clear-filter"},{},{})} ${escape(y("dataframe.clear_filter"))}</button>`:""} ${!A&&d?`<button role="menuitem" aria-label="Add row above" class="svelte-42thj4">${validate_component(rt,"CellMenuIcons").$$render(n,{icon:"add-row-above"},{},{})} ${escape(y("dataframe.add_row_above"))}</button> <button role="menuitem" aria-label="Add row below" class="svelte-42thj4">${validate_component(rt,"CellMenuIcons").$$render(n,{icon:"add-row-below"},{},{})} ${escape(y("dataframe.add_row_below"))}</button> ${j?`<button role="menuitem" class="delete svelte-42thj4" aria-label="Delete row">${validate_component(rt,"CellMenuIcons").$$render(n,{icon:"delete-row"},{},{})} ${escape(y("dataframe.delete_row"))}</button>`:""}`:""} ${l?`<button role="menuitem" aria-label="Add column to the left" class="svelte-42thj4">${validate_component(rt,"CellMenuIcons").$$render(n,{icon:"add-column-left"},{},{})} ${escape(y("dataframe.add_column_left"))}</button> <button role="menuitem" aria-label="Add column to the right" class="svelte-42thj4">${validate_component(rt,"CellMenuIcons").$$render(n,{icon:"add-column-right"},{},{})} ${escape(y("dataframe.add_column_right"))}</button> ${K?`<button role="menuitem" class="delete svelte-42thj4" aria-label="Delete column">${validate_component(rt,"CellMenuIcons").$$render(n,{icon:"delete-column"},{},{})} ${escape(y("dataframe.delete_column"))}</button>`:""}`:""}</div> `}),Ln={code:".toolbar.svelte-b1nr0g{display:flex;align-items:center;gap:var(--size-2);flex:0 0 auto}.toolbar-buttons.svelte-b1nr0g{display:flex;gap:var(--size-1);flex-wrap:nowrap}.toolbar-button.svelte-b1nr0g{display:flex;align-items:center;justify-content:center;width:var(--size-6);height:var(--size-6);padding:var(--size-1);border:none;border-radius:var(--radius-sm);background:transparent;color:var(--body-text-color-subdued);cursor:pointer;transition:all 0.2s}.toolbar-button.svelte-b1nr0g:hover{background:var(--background-fill-secondary);color:var(--body-text-color)}.toolbar-button.svelte-b1nr0g svg{width:var(--size-4);height:var(--size-4)}.search-container.svelte-b1nr0g{position:relative}.search-input.svelte-b1nr0g{width:var(--size-full);height:var(--size-6);padding:var(--size-2);padding-right:var(--size-8);border:1px solid var(--border-color-primary);border-radius:var(--table-radius);font-size:var(--text-sm);color:var(--body-text-color);background:var(--background-fill-secondary);transition:all 0.2s ease}.search-input.svelte-b1nr0g:hover{border-color:var(--border-color-secondary);background:var(--background-fill-primary)}.search-input.svelte-b1nr0g:focus{outline:none;border-color:var(--color-accent);background:var(--background-fill-primary);box-shadow:0 0 0 1px var(--color-accent)}.check-button.svelte-b1nr0g{position:absolute;right:var(--size-1);top:50%;transform:translateY(-50%);background:var(--color-accent);color:white;border:none;width:var(--size-4);height:var(--size-4);border-radius:var(--radius-sm);display:flex;align-items:center;justify-content:center;padding:var(--size-1)}.check-button.svelte-b1nr0g svg{width:var(--size-3);height:var(--size-3)}.check-button.svelte-b1nr0g:hover{background:var(--color-accent-soft)}",map:'{"version":3,"file":"Toolbar.svelte","sources":["Toolbar.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { Copy, Check } from \\"@gradio/icons\\";\\nimport { FullscreenButton } from \\"@gradio/atoms\\";\\nimport { onDestroy } from \\"svelte\\";\\nimport { createEventDispatcher } from \\"svelte\\";\\nexport let show_fullscreen_button = false;\\nexport let show_copy_button = false;\\nexport let show_search = \\"none\\";\\nexport let fullscreen = false;\\nexport let on_copy;\\nexport let on_commit_filter;\\nconst dispatch = createEventDispatcher();\\nlet copied = false;\\nlet timer;\\nexport let current_search_query = null;\\nlet input_value = \\"\\";\\nfunction handle_search_input(e) {\\n    const target = e.target;\\n    input_value = target.value;\\n    const new_query = input_value || null;\\n    if (current_search_query !== new_query) {\\n        current_search_query = new_query;\\n        dispatch(\\"search\\", current_search_query);\\n    }\\n}\\nfunction copy_feedback() {\\n    copied = true;\\n    if (timer)\\n        clearTimeout(timer);\\n    timer = setTimeout(() => {\\n        copied = false;\\n    }, 2e3);\\n}\\nasync function handle_copy() {\\n    await on_copy();\\n    copy_feedback();\\n}\\nonDestroy(() => {\\n    if (timer)\\n        clearTimeout(timer);\\n});\\n<\/script>\\n\\n<div class=\\"toolbar\\" role=\\"toolbar\\" aria-label=\\"Table actions\\">\\n\\t<div class=\\"toolbar-buttons\\">\\n\\t\\t{#if show_search !== \\"none\\"}\\n\\t\\t\\t<div class=\\"search-container\\">\\n\\t\\t\\t\\t<input\\n\\t\\t\\t\\t\\ttype=\\"text\\"\\n\\t\\t\\t\\t\\tvalue={current_search_query || \\"\\"}\\n\\t\\t\\t\\t\\ton:input={handle_search_input}\\n\\t\\t\\t\\t\\tplaceholder={show_search === \\"filter\\" ? \\"Filter...\\" : \\"Search...\\"}\\n\\t\\t\\t\\t\\tclass=\\"search-input\\"\\n\\t\\t\\t\\t\\tclass:filter-mode={show_search === \\"filter\\"}\\n\\t\\t\\t\\t\\ttitle={`Enter text to ${show_search} the table`}\\n\\t\\t\\t\\t/>\\n\\t\\t\\t\\t{#if current_search_query && show_search === \\"filter\\"}\\n\\t\\t\\t\\t\\t<button\\n\\t\\t\\t\\t\\t\\tclass=\\"toolbar-button check-button\\"\\n\\t\\t\\t\\t\\t\\ton:click={on_commit_filter}\\n\\t\\t\\t\\t\\t\\taria-label=\\"Apply filter and update dataframe values\\"\\n\\t\\t\\t\\t\\t\\ttitle=\\"Apply filter and update dataframe values\\"\\n\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t<Check />\\n\\t\\t\\t\\t\\t</button>\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t</div>\\n\\t\\t{/if}\\n\\t\\t{#if show_copy_button}\\n\\t\\t\\t<button\\n\\t\\t\\t\\tclass=\\"toolbar-button\\"\\n\\t\\t\\t\\ton:click={handle_copy}\\n\\t\\t\\t\\taria-label={copied ? \\"Copied to clipboard\\" : \\"Copy table data\\"}\\n\\t\\t\\t\\ttitle={copied ? \\"Copied to clipboard\\" : \\"Copy table data\\"}\\n\\t\\t\\t>\\n\\t\\t\\t\\t{#if copied}\\n\\t\\t\\t\\t\\t<Check />\\n\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t<Copy />\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t</button>\\n\\t\\t{/if}\\n\\t\\t{#if show_fullscreen_button}\\n\\t\\t\\t<FullscreenButton {fullscreen} on:fullscreen />\\n\\t\\t{/if}\\n\\t</div>\\n</div>\\n\\n<style>\\n\\t.toolbar {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tgap: var(--size-2);\\n\\t\\tflex: 0 0 auto;\\n\\t}\\n\\n\\t.toolbar-buttons {\\n\\t\\tdisplay: flex;\\n\\t\\tgap: var(--size-1);\\n\\t\\tflex-wrap: nowrap;\\n\\t}\\n\\n\\t.toolbar-button {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tjustify-content: center;\\n\\t\\twidth: var(--size-6);\\n\\t\\theight: var(--size-6);\\n\\t\\tpadding: var(--size-1);\\n\\t\\tborder: none;\\n\\t\\tborder-radius: var(--radius-sm);\\n\\t\\tbackground: transparent;\\n\\t\\tcolor: var(--body-text-color-subdued);\\n\\t\\tcursor: pointer;\\n\\t\\ttransition: all 0.2s;\\n\\t}\\n\\n\\t.toolbar-button:hover {\\n\\t\\tbackground: var(--background-fill-secondary);\\n\\t\\tcolor: var(--body-text-color);\\n\\t}\\n\\n\\t.toolbar-button :global(svg) {\\n\\t\\twidth: var(--size-4);\\n\\t\\theight: var(--size-4);\\n\\t}\\n\\n\\t.search-container {\\n\\t\\tposition: relative;\\n\\t}\\n\\n\\t.search-input {\\n\\t\\twidth: var(--size-full);\\n\\t\\theight: var(--size-6);\\n\\t\\tpadding: var(--size-2);\\n\\t\\tpadding-right: var(--size-8);\\n\\t\\tborder: 1px solid var(--border-color-primary);\\n\\t\\tborder-radius: var(--table-radius);\\n\\t\\tfont-size: var(--text-sm);\\n\\t\\tcolor: var(--body-text-color);\\n\\t\\tbackground: var(--background-fill-secondary);\\n\\t\\ttransition: all 0.2s ease;\\n\\t}\\n\\n\\t.search-input:hover {\\n\\t\\tborder-color: var(--border-color-secondary);\\n\\t\\tbackground: var(--background-fill-primary);\\n\\t}\\n\\n\\t.search-input:focus {\\n\\t\\toutline: none;\\n\\t\\tborder-color: var(--color-accent);\\n\\t\\tbackground: var(--background-fill-primary);\\n\\t\\tbox-shadow: 0 0 0 1px var(--color-accent);\\n\\t}\\n\\n\\t.check-button {\\n\\t\\tposition: absolute;\\n\\t\\tright: var(--size-1);\\n\\t\\ttop: 50%;\\n\\t\\ttransform: translateY(-50%);\\n\\t\\tbackground: var(--color-accent);\\n\\t\\tcolor: white;\\n\\t\\tborder: none;\\n\\t\\twidth: var(--size-4);\\n\\t\\theight: var(--size-4);\\n\\t\\tborder-radius: var(--radius-sm);\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tjustify-content: center;\\n\\t\\tpadding: var(--size-1);\\n\\t}\\n\\n\\t.check-button :global(svg) {\\n\\t\\twidth: var(--size-3);\\n\\t\\theight: var(--size-3);\\n\\t}\\n\\n\\t.check-button:hover {\\n\\t\\tbackground: var(--color-accent-soft);\\n\\t}</style>\\n"],"names":[],"mappings":"AAwFC,sBAAS,CACR,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,GAAG,CAAE,IAAI,QAAQ,CAAC,CAClB,IAAI,CAAE,CAAC,CAAC,CAAC,CAAC,IACX,CAEA,8BAAiB,CAChB,OAAO,CAAE,IAAI,CACb,GAAG,CAAE,IAAI,QAAQ,CAAC,CAClB,SAAS,CAAE,MACZ,CAEA,6BAAgB,CACf,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,MAAM,CAAE,IAAI,CACZ,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,UAAU,CAAE,WAAW,CACvB,KAAK,CAAE,IAAI,yBAAyB,CAAC,CACrC,MAAM,CAAE,OAAO,CACf,UAAU,CAAE,GAAG,CAAC,IACjB,CAEA,6BAAe,MAAO,CACrB,UAAU,CAAE,IAAI,2BAA2B,CAAC,CAC5C,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CAEA,6BAAe,CAAS,GAAK,CAC5B,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CACrB,CAEA,+BAAkB,CACjB,QAAQ,CAAE,QACX,CAEA,2BAAc,CACb,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,aAAa,CAAE,IAAI,QAAQ,CAAC,CAC5B,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,cAAc,CAAC,CAClC,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,UAAU,CAAE,IAAI,2BAA2B,CAAC,CAC5C,UAAU,CAAE,GAAG,CAAC,IAAI,CAAC,IACtB,CAEA,2BAAa,MAAO,CACnB,YAAY,CAAE,IAAI,wBAAwB,CAAC,CAC3C,UAAU,CAAE,IAAI,yBAAyB,CAC1C,CAEA,2BAAa,MAAO,CACnB,OAAO,CAAE,IAAI,CACb,YAAY,CAAE,IAAI,cAAc,CAAC,CACjC,UAAU,CAAE,IAAI,yBAAyB,CAAC,CAC1C,UAAU,CAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,cAAc,CACzC,CAEA,2BAAc,CACb,QAAQ,CAAE,QAAQ,CAClB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,GAAG,CAAE,GAAG,CACR,SAAS,CAAE,WAAW,IAAI,CAAC,CAC3B,UAAU,CAAE,IAAI,cAAc,CAAC,CAC/B,KAAK,CAAE,KAAK,CACZ,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,OAAO,CAAE,IAAI,QAAQ,CACtB,CAEA,2BAAa,CAAS,GAAK,CAC1B,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CACrB,CAEA,2BAAa,MAAO,CACnB,UAAU,CAAE,IAAI,mBAAmB,CACpC"}'},Dn=create_ssr_component((n,e,t,a)=>{let{show_fullscreen_button:A=!1}=e,{show_copy_button:d=!1}=e,{show_search:l="none"}=e,{fullscreen:o=!1}=e,{on_copy:s}=e,{on_commit_filter:i}=e;createEventDispatcher();let{current_search_query:r=null}=e;return onDestroy(()=>{}),e.show_fullscreen_button===void 0&&t.show_fullscreen_button&&A!==void 0&&t.show_fullscreen_button(A),e.show_copy_button===void 0&&t.show_copy_button&&d!==void 0&&t.show_copy_button(d),e.show_search===void 0&&t.show_search&&l!==void 0&&t.show_search(l),e.fullscreen===void 0&&t.fullscreen&&o!==void 0&&t.fullscreen(o),e.on_copy===void 0&&t.on_copy&&s!==void 0&&t.on_copy(s),e.on_commit_filter===void 0&&t.on_commit_filter&&i!==void 0&&t.on_commit_filter(i),e.current_search_query===void 0&&t.current_search_query&&r!==void 0&&t.current_search_query(r),n.css.add(Ln),`<div class="toolbar svelte-b1nr0g" role="toolbar" aria-label="Table actions"><div class="toolbar-buttons svelte-b1nr0g">${l!=="none"?`<div class="search-container svelte-b1nr0g"><input type="text"${add_attribute("value",r||"",0)}${add_attribute("placeholder",l==="filter"?"Filter...":"Search...",0)} class="${["search-input svelte-b1nr0g",l==="filter"?"filter-mode":""].join(" ").trim()}"${add_attribute("title",`Enter text to ${l} the table`,0)}> ${r&&l==="filter"?`<button class="toolbar-button check-button svelte-b1nr0g" aria-label="Apply filter and update dataframe values" title="Apply filter and update dataframe values">${validate_component(Lt,"Check").$$render(n,{},{},{})}</button>`:""}</div>`:""} ${d?`<button class="toolbar-button svelte-b1nr0g"${add_attribute("aria-label","Copy table data",0)}${add_attribute("title","Copy table data",0)}>${`${validate_component(Gt,"Copy").$$render(n,{},{},{})}`}</button>`:""} ${A?`${validate_component(Le,"FullscreenButton").$$render(n,{fullscreen:o},{},{})}`:""}</div> </div>`});function Yt(n,e,t,a){let A=n||[];if(e[1]==="fixed"&&A.length<e[0]){const d=Array(e[0]-A.length).fill("").map((l,o)=>`${o+A.length}`);A=A.concat(d);}return !A||A.length===0?Array(e[0]).fill(0).map((d,l)=>{const o=a();return t[o]={cell:null,input:null},{id:o,value:JSON.stringify(l+1)}}):A.map((d,l)=>{const o=a();return t[o]={cell:null,input:null},{id:o,value:d??""}})}function Vn(n,e,t,a,A=null){return !n||n.length===0?[]:n.map((l,o)=>l.map((s,i)=>{const r=a();e[r]={cell:null,input:null},t[r]=s;let _=A?.[o]?.[i];return _===void 0&&(_=String(s)),{id:r,value:s,display_value:_}}))}function Rn(n,e){if(e==="number"){const t=Number(n);return isNaN(t)?n:t}if(e==="bool"){if(typeof n=="boolean")return n;if(typeof n=="number")return n!==0;const t=String(n).toLowerCase();return t==="true"||t==="1"?!0:t==="false"||t==="0"?!1:n}if(e==="date"){const t=new Date(n);return isNaN(t.getTime())?n:t.toISOString()}return n}async function Pn(n,e,t,a){if(!e.data||!e.data[t]||!e.data[t][a])return;const A=e.data[t][a].value;e.data[t][a].value=n,A!==n&&e.dispatch&&e.dispatch("change",{data:e.data.map(d=>d.map(l=>l.value)),headers:e.headers?.map(d=>d.value)||[],metadata:null}),e.actions.set_selected([t,a]);}async function Hn(n,e,t){if(!e.data||!e.headers||!e.els)return;const a=n.target;!a||a.value===void 0||await Pn(a.type==="checkbox"?String(a.checked):a.value,e,t[0],t[1]);}const Fn={code:".table-container.svelte-1vwr9xf.svelte-1vwr9xf{display:flex;flex-direction:column;gap:var(--size-2);position:relative}.table-wrap.svelte-1vwr9xf.svelte-1vwr9xf{position:relative;transition:150ms}.table-wrap.menu-open.svelte-1vwr9xf.svelte-1vwr9xf{overflow:hidden}.table-wrap.svelte-1vwr9xf.svelte-1vwr9xf:focus-within{outline:none}.table-wrap.dragging.svelte-1vwr9xf.svelte-1vwr9xf{cursor:crosshair !important;user-select:none}.table-wrap.dragging.svelte-1vwr9xf .svelte-1vwr9xf{cursor:crosshair !important;user-select:none}.table-wrap.svelte-1vwr9xf>button{border:1px solid var(--border-color-primary);border-radius:var(--table-radius);overflow:hidden}table.svelte-1vwr9xf.svelte-1vwr9xf{position:absolute;opacity:0;z-index:-1;transition:150ms;width:var(--size-full);table-layout:auto;color:var(--body-text-color);font-size:var(--input-text-size);line-height:var(--line-md);font-family:var(--font-mono);border-spacing:0;border-collapse:separate}thead.svelte-1vwr9xf.svelte-1vwr9xf{position:sticky;top:0;z-index:5;box-shadow:var(--shadow-drop)}thead.svelte-1vwr9xf th.pinned-column{position:sticky;z-index:6;background:var(--table-even-background-fill) !important}.dragging.svelte-1vwr9xf.svelte-1vwr9xf{border-color:var(--color-accent)}.no-wrap.svelte-1vwr9xf.svelte-1vwr9xf{white-space:nowrap}div.svelte-1vwr9xf:not(.no-wrap) td.svelte-1vwr9xf{overflow-wrap:anywhere}div.no-wrap.svelte-1vwr9xf td.svelte-1vwr9xf{overflow-x:hidden}tr.svelte-1vwr9xf.svelte-1vwr9xf{background:var(--table-even-background-fill)}tr.row-odd.svelte-1vwr9xf.svelte-1vwr9xf{background:var(--table-odd-background-fill)}.header-row.svelte-1vwr9xf.svelte-1vwr9xf{display:flex;justify-content:flex-end;align-items:center;gap:var(--size-2);min-height:var(--size-6);flex-wrap:nowrap;width:100%}.header-row.svelte-1vwr9xf .label.svelte-1vwr9xf{flex:1 1 auto;margin-right:auto}.header-row.svelte-1vwr9xf .label p.svelte-1vwr9xf{margin:0;color:var(--block-label-text-color);font-size:var(--block-label-text-size);line-height:var(--line-sm);position:relative;z-index:4}.scroll-top-button.svelte-1vwr9xf.svelte-1vwr9xf{position:absolute;right:var(--size-4);bottom:var(--size-4);width:var(--size-8);height:var(--size-8);border-radius:var(--table-radius);background:var(--color-accent);color:white;border:none;cursor:pointer;display:flex;align-items:center;justify-content:center;font-size:var(--text-lg);z-index:9;opacity:0.5}.scroll-top-button.svelte-1vwr9xf.svelte-1vwr9xf:hover{opacity:1}tr.svelte-1vwr9xf.svelte-1vwr9xf{border-bottom:1px solid var(--border-color-primary);text-align:left}",map:'{"version":3,"file":"Table.svelte","sources":["Table.svelte"],"sourcesContent":["<script lang=\\"ts\\" context=\\"module\\">import { create_dataframe_context } from \\"./context/dataframe_context\\";\\n<\/script>\\n\\n<script lang=\\"ts\\">import { afterUpdate, createEventDispatcher, tick, onMount } from \\"svelte\\";\\nimport { dequal } from \\"dequal/lite\\";\\nimport { Upload } from \\"@gradio/upload\\";\\nimport EditableCell from \\"./EditableCell.svelte\\";\\nimport RowNumber from \\"./RowNumber.svelte\\";\\nimport TableHeader from \\"./TableHeader.svelte\\";\\nimport TableCell from \\"./TableCell.svelte\\";\\nimport EmptyRowButton from \\"./EmptyRowButton.svelte\\";\\nimport {} from \\"@gradio/client\\";\\nimport VirtualTable from \\"./VirtualTable.svelte\\";\\nimport CellMenu from \\"./CellMenu.svelte\\";\\nimport Toolbar from \\"./Toolbar.svelte\\";\\nimport { is_cell_selected, should_show_cell_menu, get_current_indices, handle_click_outside as handle_click_outside_util, calculate_selection_positions } from \\"./selection_utils\\";\\nimport { copy_table_data, get_max, handle_file_upload } from \\"./utils/table_utils\\";\\nimport { make_headers, process_data } from \\"./utils/data_processing\\";\\nimport { cast_value_to_type } from \\"./utils\\";\\nimport { handle_keydown, handle_cell_blur } from \\"./utils/keyboard_utils\\";\\nimport { create_drag_handlers } from \\"./utils/drag_utils\\";\\nimport { sort_data_and_preserve_selection } from \\"./utils/sort_utils\\";\\nimport { filter_data_and_preserve_selection } from \\"./utils/filter_utils\\";\\nexport let datatype;\\nexport let label = null;\\nexport let show_label = true;\\nexport let headers = [];\\nexport let values = [];\\nexport let col_count;\\nexport let row_count;\\nexport let latex_delimiters;\\nexport let components = {};\\nexport let editable = true;\\nexport let wrap = false;\\nexport let root;\\nexport let i18n;\\nexport let max_height = 500;\\nexport let line_breaks = true;\\nexport let column_widths = [];\\nexport let show_row_numbers = false;\\nexport let upload;\\nexport let stream_handler;\\nexport let show_fullscreen_button = false;\\nexport let show_copy_button = false;\\nexport let value_is_output = false;\\nexport let max_chars = void 0;\\nexport let show_search = \\"none\\";\\nexport let pinned_columns = 0;\\nexport let static_columns = [];\\nexport let fullscreen = false;\\nconst df_ctx = create_dataframe_context({\\n    show_fullscreen_button,\\n    show_copy_button,\\n    show_search,\\n    show_row_numbers,\\n    editable,\\n    pinned_columns,\\n    show_label,\\n    line_breaks,\\n    wrap,\\n    max_height,\\n    column_widths,\\n    max_chars,\\n    static_columns\\n});\\nconst { state: df_state, actions: df_actions } = df_ctx;\\n$: selected_cells = $df_state.ui_state.selected_cells;\\n$: selected = $df_state.ui_state.selected;\\n$: editing = $df_state.ui_state.editing;\\n$: header_edit = $df_state.ui_state.header_edit;\\n$: selected_header = $df_state.ui_state.selected_header;\\n$: active_cell_menu = $df_state.ui_state.active_cell_menu;\\n$: active_header_menu = $df_state.ui_state.active_header_menu;\\n$: copy_flash = $df_state.ui_state.copy_flash;\\n$: actual_pinned_columns = pinned_columns && data?.[0]?.length ? Math.min(pinned_columns, data[0].length) : 0;\\nonMount(() => {\\n    df_ctx.parent_element = parent;\\n    df_ctx.get_data_at = get_data_at;\\n    df_ctx.get_column = get_column;\\n    df_ctx.get_row = get_row;\\n    df_ctx.dispatch = dispatch;\\n    init_drag_handlers();\\n    const observer = new IntersectionObserver((entries) => {\\n        entries.forEach((entry) => {\\n            if (entry.isIntersecting && !is_visible) {\\n                width_calculated = false;\\n            }\\n            is_visible = entry.isIntersecting;\\n        });\\n    });\\n    observer.observe(parent);\\n    document.addEventListener(\\"click\\", handle_click_outside);\\n    window.addEventListener(\\"resize\\", handle_resize);\\n    const global_mouse_up = (event) => {\\n        if (is_dragging || drag_start) {\\n            handle_mouse_up(event);\\n        }\\n    };\\n    document.addEventListener(\\"mouseup\\", global_mouse_up);\\n    return () => {\\n        observer.disconnect();\\n        document.removeEventListener(\\"click\\", handle_click_outside);\\n        window.removeEventListener(\\"resize\\", handle_resize);\\n        document.removeEventListener(\\"mouseup\\", global_mouse_up);\\n    };\\n});\\n$: {\\n    if (data || _headers || els) {\\n        df_ctx.data = data;\\n        df_ctx.headers = _headers;\\n        df_ctx.els = els;\\n        df_ctx.display_value = display_value;\\n        df_ctx.styling = styling;\\n    }\\n}\\nconst dispatch = createEventDispatcher();\\nlet els = {};\\nlet data_binding = {};\\nlet _headers = make_headers(headers, col_count, els, make_id);\\nlet old_headers = headers;\\nlet data = [[]];\\nlet old_val = void 0;\\nlet search_results = [[]];\\nlet dragging = false;\\nlet color_accent_copied;\\nlet filtered_to_original_map = [];\\nonMount(() => {\\n    const color = getComputedStyle(document.documentElement).getPropertyValue(\\"--color-accent\\").trim();\\n    color_accent_copied = color + \\"40\\";\\n    document.documentElement.style.setProperty(\\"--color-accent-copied\\", color_accent_copied);\\n});\\nconst get_data_at = (row, col) => data?.[row]?.[col]?.value;\\nconst get_column = (col) => data?.map((row) => row[col]?.value) ?? [];\\nconst get_row = (row) => data?.[row]?.map((cell) => cell.value) ?? [];\\n$: {\\n    if (!dequal(headers, old_headers)) {\\n        _headers = make_headers(headers, col_count, els, make_id);\\n        old_headers = JSON.parse(JSON.stringify(headers));\\n    }\\n}\\nfunction make_id() {\\n    return Math.random().toString(36).substring(2, 15);\\n}\\nexport let display_value = null;\\nexport let styling = null;\\n$: if (!dequal(values, old_val)) {\\n    if (parent) {\\n        const is_reset2 = values.length === 0 || values.length === 1 && values[0].length === 0;\\n        const is_different_structure2 = old_val !== void 0 && (values.length !== old_val.length || values[0] && old_val[0] && values[0].length !== old_val[0].length);\\n        if (is_reset2 || is_different_structure2) {\\n            for (let i = 0; i < 50; i++) {\\n                parent.style.removeProperty(`--cell-width-${i}`);\\n            }\\n            last_width_data_length = 0;\\n            last_width_column_count = 0;\\n            width_calculated = false;\\n        }\\n    }\\n    const is_reset = values.length === 0 || values.length === 1 && values[0].length === 0;\\n    const is_different_structure = old_val !== void 0 && (values.length !== old_val.length || values[0] && old_val[0] && values[0].length !== old_val[0].length);\\n    data = process_data(values, els, data_binding, make_id, display_value);\\n    old_val = JSON.parse(JSON.stringify(values));\\n    if (is_reset || is_different_structure) {\\n        df_actions.reset_sort_state();\\n    }\\n    else if ($df_state.sort_state.sort_columns.length > 0) {\\n        sort_data(data, display_value, styling);\\n    }\\n    else {\\n        df_actions.handle_sort(-1, \\"asc\\");\\n        df_actions.reset_sort_state();\\n    }\\n    if ($df_state.filter_state.filter_columns.length > 0) {\\n        filter_data(data, display_value, styling);\\n    }\\n    else {\\n        df_actions.reset_filter_state();\\n    }\\n    if ($df_state.current_search_query) {\\n        df_actions.handle_search(null);\\n    }\\n    if (parent && cells.length > 0 && (is_reset || is_different_structure)) {\\n        width_calculated = false;\\n    }\\n}\\n$: if ($df_state.current_search_query !== void 0) {\\n    const cell_map = /* @__PURE__ */ new Map();\\n    filtered_to_original_map = [];\\n    data.forEach((row, row_idx) => {\\n        if (row.some((cell) => String(cell?.value).toLowerCase().includes($df_state.current_search_query?.toLowerCase() || \\"\\"))) {\\n            filtered_to_original_map.push(row_idx);\\n        }\\n        row.forEach((cell, col_idx) => {\\n            cell_map.set(cell.id, {\\n                value: cell.value,\\n                display_value: cell.display_value !== void 0 ? cell.display_value : String(cell.value),\\n                styling: styling?.[row_idx]?.[col_idx] || \\"\\"\\n            });\\n        });\\n    });\\n    const filtered = df_actions.filter_data(data);\\n    search_results = filtered.map((row) => row.map((cell) => {\\n        const original = cell_map.get(cell.id);\\n        return {\\n            ...cell,\\n            display_value: original?.display_value !== void 0 ? original.display_value : String(cell.value),\\n            styling: original?.styling || \\"\\"\\n        };\\n    }));\\n}\\nelse {\\n    filtered_to_original_map = [];\\n}\\nlet previous_headers = _headers.map((h) => h.value);\\nlet previous_data = data.map((row) => row.map((cell) => String(cell.value)));\\n$: {\\n    if (data || _headers) {\\n        df_actions.trigger_change(data.map((row, rowIdx) => row.map((cell, colIdx) => {\\n            const dtype = Array.isArray(datatype) ? datatype[colIdx] : datatype;\\n            return {\\n                ...cell,\\n                value: cast_value_to_type(cell.value, dtype)\\n            };\\n        })), _headers, previous_data, previous_headers, value_is_output, dispatch);\\n        previous_data = data.map((row) => row.map((cell) => String(cell.value)));\\n        previous_headers = _headers.map((h) => h.value);\\n    }\\n}\\nfunction handle_sort(col, direction) {\\n    df_actions.handle_sort(col, direction);\\n    sort_data(data, display_value, styling);\\n}\\nfunction clear_sort() {\\n    df_actions.reset_sort_state();\\n    sort_data(data, display_value, styling);\\n}\\n$: {\\n    if ($df_state.filter_state.filter_columns.length > 0) {\\n        filter_data(data, display_value, styling);\\n    }\\n    if ($df_state.sort_state.sort_columns.length > 0) {\\n        sort_data(data, display_value, styling);\\n        df_actions.update_row_order(data);\\n    }\\n}\\nfunction handle_filter(col, datatype2, filter, value) {\\n    df_actions.handle_filter(col, datatype2, filter, value);\\n    filter_data(data, display_value, styling);\\n}\\nfunction clear_filter() {\\n    df_actions.reset_filter_state();\\n    filter_data(data, display_value, styling);\\n}\\nasync function edit_header(i, _select = false) {\\n    if (!editable || header_edit === i || col_count[1] !== \\"dynamic\\")\\n        return;\\n    df_actions.set_header_edit(i);\\n}\\nfunction handle_header_click(event, col) {\\n    if (event.target instanceof HTMLAnchorElement) {\\n        return;\\n    }\\n    event.preventDefault();\\n    event.stopPropagation();\\n    if (!editable)\\n        return;\\n    df_actions.set_editing(false);\\n    df_actions.handle_header_click(col, editable);\\n    parent.focus();\\n}\\nfunction end_header_edit(event) {\\n    if (!editable)\\n        return;\\n    df_actions.end_header_edit(event.detail.key);\\n    parent.focus();\\n}\\nasync function add_row(index) {\\n    parent.focus();\\n    if (row_count[1] !== \\"dynamic\\")\\n        return;\\n    const new_row = Array(data[0]?.length || headers.length).fill(0).map((_, i) => {\\n        const _id = make_id();\\n        els[_id] = { cell: null, input: null };\\n        return { id: _id, value: \\"\\" };\\n    });\\n    if (data.length === 0) {\\n        data = [new_row];\\n    }\\n    else if (index !== void 0 && index >= 0 && index <= data.length) {\\n        data.splice(index, 0, new_row);\\n    }\\n    else {\\n        data.push(new_row);\\n    }\\n    selected = [index !== void 0 ? index : data.length - 1, 0];\\n}\\nasync function add_col(index) {\\n    parent.focus();\\n    if (col_count[1] !== \\"dynamic\\")\\n        return;\\n    const result = df_actions.add_col(data, headers, make_id, index);\\n    result.data.forEach((row) => {\\n        row.forEach((cell) => {\\n            if (!els[cell.id]) {\\n                els[cell.id] = { cell: null, input: null };\\n            }\\n        });\\n    });\\n    data = result.data;\\n    headers = result.headers;\\n    await tick();\\n    requestAnimationFrame(() => {\\n        edit_header(index !== void 0 ? index : data[0].length - 1, true);\\n        const new_w = parent.querySelectorAll(\\"tbody\\")[1].offsetWidth;\\n        parent.querySelectorAll(\\"table\\")[1].scrollTo({ left: new_w });\\n    });\\n}\\nfunction handle_click_outside(event) {\\n    if (handle_click_outside_util(event, parent)) {\\n        df_actions.clear_ui_state();\\n        header_edit = false;\\n        selected_header = false;\\n    }\\n}\\n$: max = get_max(data);\\nlet width_calc_timeout;\\n$: if (cells[0] && cells[0]?.clientWidth) {\\n    clearTimeout(width_calc_timeout);\\n    width_calc_timeout = setTimeout(() => set_cell_widths(), 100);\\n}\\nlet width_calculated = false;\\n$: if (cells[0] && !width_calculated) {\\n    set_cell_widths();\\n    width_calculated = true;\\n}\\nlet cells = [];\\nlet parent;\\nlet table;\\nlet last_width_data_length = 0;\\nlet last_width_column_count = 0;\\nfunction set_cell_widths() {\\n    const column_count = data[0]?.length || 0;\\n    if ($df_state.filter_state.filter_columns.length > 0) {\\n        return;\\n    }\\n    if (last_width_data_length === data.length && last_width_column_count === column_count && $df_state.sort_state.sort_columns.length > 0) {\\n        return;\\n    }\\n    last_width_data_length = data.length;\\n    last_width_column_count = column_count;\\n    const widths = cells.map((el) => el?.clientWidth || 0);\\n    if (widths.length === 0)\\n        return;\\n    if (show_row_numbers) {\\n        parent.style.setProperty(`--cell-width-row-number`, `${widths[0]}px`);\\n    }\\n    for (let i = 0; i < 50; i++) {\\n        if (!column_widths[i]) {\\n            parent.style.removeProperty(`--cell-width-${i}`);\\n        }\\n        else if (column_widths[i].endsWith(\\"%\\")) {\\n            const percentage = parseFloat(column_widths[i]);\\n            const pixel_width = Math.floor(percentage / 100 * parent.clientWidth);\\n            parent.style.setProperty(`--cell-width-${i}`, `${pixel_width}px`);\\n        }\\n        else {\\n            parent.style.setProperty(`--cell-width-${i}`, column_widths[i]);\\n        }\\n    }\\n    widths.forEach((width, i) => {\\n        if (!column_widths[i]) {\\n            const calculated_width = `${Math.max(width, 45)}px`;\\n            parent.style.setProperty(`--cell-width-${i}`, calculated_width);\\n        }\\n    });\\n}\\nfunction get_cell_width(index) {\\n    return `var(--cell-width-${index})`;\\n}\\nlet table_height = values.slice(0, max_height / values.length * 37).length * 37 + 37;\\nlet scrollbar_width = 0;\\nfunction sort_data(_data, _display_value, _styling) {\\n    const result = sort_data_and_preserve_selection(_data, _display_value, _styling, $df_state.sort_state.sort_columns, selected, get_current_indices);\\n    data = result.data;\\n    selected = result.selected;\\n}\\nfunction filter_data(_data, _display_value, _styling) {\\n    const result = filter_data_and_preserve_selection(_data, _display_value, _styling, $df_state.filter_state.filter_columns, selected, get_current_indices, $df_state.filter_state.initial_data?.data, $df_state.filter_state.initial_data?.display_value, $df_state.filter_state.initial_data?.styling);\\n    data = result.data;\\n    selected = result.selected;\\n}\\n$: selected_index = !!selected && selected[0];\\nlet is_visible = false;\\nconst set_copy_flash = (value) => {\\n    df_actions.set_copy_flash(value);\\n    if (value) {\\n        setTimeout(() => df_actions.set_copy_flash(false), 800);\\n    }\\n};\\nlet previous_selected_cells = [];\\n$: {\\n    if (copy_flash && !dequal(selected_cells, previous_selected_cells)) {\\n        set_copy_flash(false);\\n    }\\n    previous_selected_cells = selected_cells;\\n}\\nfunction handle_blur(event) {\\n    const { blur_event, coords } = event.detail;\\n    handle_cell_blur(blur_event, df_ctx, coords);\\n}\\nfunction toggle_header_menu(event, col) {\\n    event.stopPropagation();\\n    if (active_header_menu && active_header_menu.col === col) {\\n        df_actions.set_active_header_menu(null);\\n    }\\n    else {\\n        const header = event.target.closest(\\"th\\");\\n        if (header) {\\n            const rect = header.getBoundingClientRect();\\n            df_actions.set_active_header_menu({\\n                col,\\n                x: rect.right,\\n                y: rect.bottom\\n            });\\n        }\\n    }\\n}\\nafterUpdate(() => {\\n    value_is_output = false;\\n});\\nfunction delete_col_at(index) {\\n    if (col_count[1] !== \\"dynamic\\")\\n        return;\\n    if (data[0].length <= 1)\\n        return;\\n    const result = df_actions.delete_col_at(data, headers, index);\\n    data = result.data;\\n    headers = result.headers;\\n    _headers = make_headers(headers, col_count, els, make_id);\\n    df_actions.set_active_cell_menu(null);\\n    df_actions.set_active_header_menu(null);\\n    df_actions.set_selected(false);\\n    df_actions.set_selected_cells([]);\\n    df_actions.set_editing(false);\\n}\\nfunction delete_row_at(index) {\\n    data = df_actions.delete_row_at(data, index);\\n    df_actions.set_active_cell_menu(null);\\n    df_actions.set_active_header_menu(null);\\n}\\nlet selected_cell_coords;\\n$: if (selected !== false)\\n    selected_cell_coords = selected;\\n$: if (selected !== false) {\\n    const positions = calculate_selection_positions(selected, data, els, parent, table);\\n    document.documentElement.style.setProperty(\\"--selected-col-pos\\", positions.col_pos);\\n    document.documentElement.style.setProperty(\\"--selected-row-pos\\", positions.row_pos || \\"0px\\");\\n}\\nfunction commit_filter() {\\n    if ($df_state.current_search_query && show_search === \\"filter\\") {\\n        const filtered_data = [];\\n        const filtered_display_values = [];\\n        const filtered_styling = [];\\n        search_results.forEach((row) => {\\n            const data_row = [];\\n            const display_row = [];\\n            const styling_row = [];\\n            row.forEach((cell) => {\\n                data_row.push(cell.value);\\n                display_row.push(cell.display_value !== void 0 ? cell.display_value : String(cell.value));\\n                styling_row.push(cell.styling || \\"\\");\\n            });\\n            filtered_data.push(data_row);\\n            filtered_display_values.push(display_row);\\n            filtered_styling.push(styling_row);\\n        });\\n        const change_payload = {\\n            data: filtered_data,\\n            headers: _headers.map((h) => h.value),\\n            metadata: {\\n                display_value: filtered_display_values,\\n                styling: filtered_styling\\n            }\\n        };\\n        dispatch(\\"change\\", change_payload);\\n        if (!value_is_output) {\\n            dispatch(\\"input\\");\\n        }\\n        df_actions.handle_search(null);\\n    }\\n}\\nlet viewport;\\nlet show_scroll_button = false;\\nfunction scroll_to_top() {\\n    viewport.scrollTo({\\n        top: 0\\n    });\\n}\\nfunction handle_resize() {\\n    df_actions.set_active_cell_menu(null);\\n    df_actions.set_active_header_menu(null);\\n    selected_cells = [];\\n    selected = false;\\n    editing = false;\\n    width_calculated = false;\\n    set_cell_widths();\\n}\\nfunction add_row_at(index, position) {\\n    const row_index = position === \\"above\\" ? index : index + 1;\\n    add_row(row_index);\\n    active_cell_menu = null;\\n    active_header_menu = null;\\n}\\nfunction add_col_at(index, position) {\\n    const col_index = position === \\"left\\" ? index : index + 1;\\n    add_col(col_index);\\n    active_cell_menu = null;\\n    active_header_menu = null;\\n}\\nexport function reset_sort_state() {\\n    df_actions.reset_sort_state();\\n}\\nlet is_dragging = false;\\nlet drag_start = null;\\nlet mouse_down_pos = null;\\nconst drag_state = {\\n    is_dragging,\\n    drag_start,\\n    mouse_down_pos\\n};\\n$: {\\n    is_dragging = drag_state.is_dragging;\\n    drag_start = drag_state.drag_start;\\n    mouse_down_pos = drag_state.mouse_down_pos;\\n}\\nlet drag_handlers;\\nfunction init_drag_handlers() {\\n    drag_handlers = create_drag_handlers(drag_state, (value) => is_dragging = value, (cells2) => df_actions.set_selected_cells(cells2), (cell) => df_actions.set_selected(cell), (event, row, col) => df_actions.handle_cell_click(event, row, col), show_row_numbers, parent);\\n}\\n$: if (parent)\\n    init_drag_handlers();\\n$: handle_mouse_down = drag_handlers?.handle_mouse_down || (() => {\\n});\\n$: handle_mouse_move = drag_handlers?.handle_mouse_move || (() => {\\n});\\n$: handle_mouse_up = drag_handlers?.handle_mouse_up || (() => {\\n});\\nfunction get_cell_display_value(row, col) {\\n    const is_search_active = $df_state.current_search_query !== void 0;\\n    if (is_search_active && search_results?.[row]?.[col]) {\\n        return search_results[row][col].display_value !== void 0 ? search_results[row][col].display_value : String(search_results[row][col].value);\\n    }\\n    if (data?.[row]?.[col]) {\\n        return data[row][col].display_value !== void 0 ? data[row][col].display_value : String(data[row][col].value);\\n    }\\n    return \\"\\";\\n}\\n<\/script>\\n\\n<svelte:window on:resize={() => set_cell_widths()} />\\n\\n<div class=\\"table-container\\">\\n\\t{#if (label && label.length !== 0 && show_label) || show_fullscreen_button || show_copy_button || show_search !== \\"none\\"}\\n\\t\\t<div class=\\"header-row\\">\\n\\t\\t\\t{#if label && label.length !== 0 && show_label}\\n\\t\\t\\t\\t<div class=\\"label\\">\\n\\t\\t\\t\\t\\t<p>{label}</p>\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t{/if}\\n\\t\\t\\t<Toolbar\\n\\t\\t\\t\\t{show_fullscreen_button}\\n\\t\\t\\t\\t{fullscreen}\\n\\t\\t\\t\\ton_copy={async () => await copy_table_data(data, null)}\\n\\t\\t\\t\\t{show_copy_button}\\n\\t\\t\\t\\t{show_search}\\n\\t\\t\\t\\ton:search={(e) => df_actions.handle_search(e.detail)}\\n\\t\\t\\t\\ton:fullscreen\\n\\t\\t\\t\\ton_commit_filter={commit_filter}\\n\\t\\t\\t\\tcurrent_search_query={$df_state.current_search_query}\\n\\t\\t\\t/>\\n\\t\\t</div>\\n\\t{/if}\\n\\t<div\\n\\t\\tbind:this={parent}\\n\\t\\tclass=\\"table-wrap\\"\\n\\t\\tclass:dragging={is_dragging}\\n\\t\\tclass:no-wrap={!wrap}\\n\\t\\tstyle=\\"height:{table_height}px;\\"\\n\\t\\tclass:menu-open={active_cell_menu || active_header_menu}\\n\\t\\ton:keydown={(e) => handle_keydown(e, df_ctx)}\\n\\t\\ton:mousemove={handle_mouse_move}\\n\\t\\ton:mouseup={handle_mouse_up}\\n\\t\\ton:mouseleave={handle_mouse_up}\\n\\t\\trole=\\"grid\\"\\n\\t\\ttabindex=\\"0\\"\\n\\t>\\n\\t\\t<table bind:this={table} aria-hidden=\\"true\\">\\n\\t\\t\\t{#if label && label.length !== 0}\\n\\t\\t\\t\\t<caption class=\\"sr-only\\">{label}</caption>\\n\\t\\t\\t{/if}\\n\\t\\t\\t<thead>\\n\\t\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\t{#if show_row_numbers}\\n\\t\\t\\t\\t\\t\\t<RowNumber is_header={true} />\\n\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t{#each _headers as { value, id }, i (id)}\\n\\t\\t\\t\\t\\t\\t<TableHeader\\n\\t\\t\\t\\t\\t\\t\\tbind:value={_headers[i].value}\\n\\t\\t\\t\\t\\t\\t\\t{i}\\n\\t\\t\\t\\t\\t\\t\\t{actual_pinned_columns}\\n\\t\\t\\t\\t\\t\\t\\t{header_edit}\\n\\t\\t\\t\\t\\t\\t\\t{selected_header}\\n\\t\\t\\t\\t\\t\\t\\t{headers}\\n\\t\\t\\t\\t\\t\\t\\t{get_cell_width}\\n\\t\\t\\t\\t\\t\\t\\t{handle_header_click}\\n\\t\\t\\t\\t\\t\\t\\t{toggle_header_menu}\\n\\t\\t\\t\\t\\t\\t\\t{end_header_edit}\\n\\t\\t\\t\\t\\t\\t\\tsort_columns={$df_state.sort_state.sort_columns}\\n\\t\\t\\t\\t\\t\\t\\tfilter_columns={$df_state.filter_state.filter_columns}\\n\\t\\t\\t\\t\\t\\t\\t{latex_delimiters}\\n\\t\\t\\t\\t\\t\\t\\t{line_breaks}\\n\\t\\t\\t\\t\\t\\t\\t{max_chars}\\n\\t\\t\\t\\t\\t\\t\\t{editable}\\n\\t\\t\\t\\t\\t\\t\\tis_static={static_columns.includes(i)}\\n\\t\\t\\t\\t\\t\\t\\t{i18n}\\n\\t\\t\\t\\t\\t\\t\\tbind:el={els[id].input}\\n\\t\\t\\t\\t\\t\\t\\t{col_count}\\n\\t\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t\\t{/each}\\n\\t\\t\\t\\t</tr>\\n\\t\\t\\t</thead>\\n\\t\\t\\t<tbody>\\n\\t\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\t{#if show_row_numbers}\\n\\t\\t\\t\\t\\t\\t<RowNumber index={0} />\\n\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t{#each max as { value, id }, j (id)}\\n\\t\\t\\t\\t\\t\\t<td tabindex=\\"-1\\" bind:this={cells[j]}>\\n\\t\\t\\t\\t\\t\\t\\t<div class=\\"cell-wrap\\">\\n\\t\\t\\t\\t\\t\\t\\t\\t<EditableCell\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{value}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{latex_delimiters}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{line_breaks}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tdatatype={Array.isArray(datatype) ? datatype[j] : datatype}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tedit={false}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tel={null}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{editable}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{i18n}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tshow_selection_buttons={selected_cells.length === 1 &&\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tselected_cells[0][0] === 0 &&\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tselected_cells[0][1] === j}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tcoords={selected_cell_coords}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\ton_select_column={df_actions.handle_select_column}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\ton_select_row={df_actions.handle_select_row}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{is_dragging}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\ton:blur={handle_blur}\\n\\t\\t\\t\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t{/each}\\n\\t\\t\\t\\t</tr>\\n\\t\\t\\t</tbody>\\n\\t\\t</table>\\n\\t\\t<Upload\\n\\t\\t\\t{upload}\\n\\t\\t\\t{stream_handler}\\n\\t\\t\\tflex={false}\\n\\t\\t\\tcenter={false}\\n\\t\\t\\tboundedheight={false}\\n\\t\\t\\tdisable_click={true}\\n\\t\\t\\t{root}\\n\\t\\t\\ton:load={({ detail }) =>\\n\\t\\t\\t\\thandle_file_upload(\\n\\t\\t\\t\\t\\tdetail.data,\\n\\t\\t\\t\\t\\t(head) => {\\n\\t\\t\\t\\t\\t\\t_headers = make_headers(\\n\\t\\t\\t\\t\\t\\t\\thead.map((h) => h ?? \\"\\"),\\n\\t\\t\\t\\t\\t\\t\\tcol_count,\\n\\t\\t\\t\\t\\t\\t\\tels,\\n\\t\\t\\t\\t\\t\\t\\tmake_id\\n\\t\\t\\t\\t\\t\\t);\\n\\t\\t\\t\\t\\t\\treturn _headers;\\n\\t\\t\\t\\t\\t},\\n\\t\\t\\t\\t\\t(vals) => {\\n\\t\\t\\t\\t\\t\\tvalues = vals;\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t)}\\n\\t\\t\\tbind:dragging\\n\\t\\t\\taria_label={i18n(\\"dataframe.drop_to_upload\\")}\\n\\t\\t>\\n\\t\\t\\t<div class=\\"table-wrap\\">\\n\\t\\t\\t\\t<VirtualTable\\n\\t\\t\\t\\t\\tbind:items={search_results}\\n\\t\\t\\t\\t\\t{max_height}\\n\\t\\t\\t\\t\\tbind:actual_height={table_height}\\n\\t\\t\\t\\t\\tbind:table_scrollbar_width={scrollbar_width}\\n\\t\\t\\t\\t\\tselected={selected_index}\\n\\t\\t\\t\\t\\tdisable_scroll={active_cell_menu !== null ||\\n\\t\\t\\t\\t\\t\\tactive_header_menu !== null}\\n\\t\\t\\t\\t\\tbind:viewport\\n\\t\\t\\t\\t\\tbind:show_scroll_button\\n\\t\\t\\t\\t\\ton:scroll_top={(_) => {}}\\n\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t{#if label && label.length !== 0}\\n\\t\\t\\t\\t\\t\\t<caption class=\\"sr-only\\">{label}</caption>\\n\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t<tr slot=\\"thead\\">\\n\\t\\t\\t\\t\\t\\t{#if show_row_numbers}\\n\\t\\t\\t\\t\\t\\t\\t<RowNumber is_header={true} />\\n\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t\\t{#each _headers as { value, id }, i (id)}\\n\\t\\t\\t\\t\\t\\t\\t<TableHeader\\n\\t\\t\\t\\t\\t\\t\\t\\tbind:value={_headers[i].value}\\n\\t\\t\\t\\t\\t\\t\\t\\t{i}\\n\\t\\t\\t\\t\\t\\t\\t\\t{actual_pinned_columns}\\n\\t\\t\\t\\t\\t\\t\\t\\t{header_edit}\\n\\t\\t\\t\\t\\t\\t\\t\\t{selected_header}\\n\\t\\t\\t\\t\\t\\t\\t\\t{headers}\\n\\t\\t\\t\\t\\t\\t\\t\\t{get_cell_width}\\n\\t\\t\\t\\t\\t\\t\\t\\t{handle_header_click}\\n\\t\\t\\t\\t\\t\\t\\t\\t{toggle_header_menu}\\n\\t\\t\\t\\t\\t\\t\\t\\t{end_header_edit}\\n\\t\\t\\t\\t\\t\\t\\t\\tsort_columns={$df_state.sort_state.sort_columns}\\n\\t\\t\\t\\t\\t\\t\\t\\tfilter_columns={$df_state.filter_state.filter_columns}\\n\\t\\t\\t\\t\\t\\t\\t\\t{latex_delimiters}\\n\\t\\t\\t\\t\\t\\t\\t\\t{line_breaks}\\n\\t\\t\\t\\t\\t\\t\\t\\t{max_chars}\\n\\t\\t\\t\\t\\t\\t\\t\\t{editable}\\n\\t\\t\\t\\t\\t\\t\\t\\tis_static={static_columns.includes(i)}\\n\\t\\t\\t\\t\\t\\t\\t\\t{i18n}\\n\\t\\t\\t\\t\\t\\t\\t\\tbind:el={els[id].input}\\n\\t\\t\\t\\t\\t\\t\\t\\t{col_count}\\n\\t\\t\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t\\t\\t{/each}\\n\\t\\t\\t\\t\\t</tr>\\n\\t\\t\\t\\t\\t<tr slot=\\"tbody\\" let:item let:index class:row-odd={index % 2 === 0}>\\n\\t\\t\\t\\t\\t\\t{#if show_row_numbers}\\n\\t\\t\\t\\t\\t\\t\\t<RowNumber {index} />\\n\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t\\t{#each item as { value, id }, j (id)}\\n\\t\\t\\t\\t\\t\\t\\t<TableCell\\n\\t\\t\\t\\t\\t\\t\\t\\tbind:value={search_results[index][j].value}\\n\\t\\t\\t\\t\\t\\t\\t\\tdisplay_value={get_cell_display_value(index, j)}\\n\\t\\t\\t\\t\\t\\t\\t\\tindex={$df_state.current_search_query !== undefined &&\\n\\t\\t\\t\\t\\t\\t\\t\\tfiltered_to_original_map[index] !== undefined\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t? filtered_to_original_map[index]\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t: index}\\n\\t\\t\\t\\t\\t\\t\\t\\t{j}\\n\\t\\t\\t\\t\\t\\t\\t\\t{actual_pinned_columns}\\n\\t\\t\\t\\t\\t\\t\\t\\t{get_cell_width}\\n\\t\\t\\t\\t\\t\\t\\t\\thandle_cell_click={(e, r, c) => handle_mouse_down(e, r, c)}\\n\\t\\t\\t\\t\\t\\t\\t\\t{handle_blur}\\n\\t\\t\\t\\t\\t\\t\\t\\ttoggle_cell_menu={df_actions.toggle_cell_menu}\\n\\t\\t\\t\\t\\t\\t\\t\\t{is_cell_selected}\\n\\t\\t\\t\\t\\t\\t\\t\\t{should_show_cell_menu}\\n\\t\\t\\t\\t\\t\\t\\t\\t{selected_cells}\\n\\t\\t\\t\\t\\t\\t\\t\\t{copy_flash}\\n\\t\\t\\t\\t\\t\\t\\t\\t{active_cell_menu}\\n\\t\\t\\t\\t\\t\\t\\t\\tstyling={search_results[index][j].styling}\\n\\t\\t\\t\\t\\t\\t\\t\\t{latex_delimiters}\\n\\t\\t\\t\\t\\t\\t\\t\\t{line_breaks}\\n\\t\\t\\t\\t\\t\\t\\t\\tdatatype={Array.isArray(datatype) ? datatype[j] : datatype}\\n\\t\\t\\t\\t\\t\\t\\t\\t{editing}\\n\\t\\t\\t\\t\\t\\t\\t\\t{max_chars}\\n\\t\\t\\t\\t\\t\\t\\t\\t{editable}\\n\\t\\t\\t\\t\\t\\t\\t\\tis_static={static_columns.includes(j)}\\n\\t\\t\\t\\t\\t\\t\\t\\t{i18n}\\n\\t\\t\\t\\t\\t\\t\\t\\t{components}\\n\\t\\t\\t\\t\\t\\t\\t\\thandle_select_column={df_actions.handle_select_column}\\n\\t\\t\\t\\t\\t\\t\\t\\thandle_select_row={df_actions.handle_select_row}\\n\\t\\t\\t\\t\\t\\t\\t\\tbind:el={els[id]}\\n\\t\\t\\t\\t\\t\\t\\t\\t{is_dragging}\\n\\t\\t\\t\\t\\t\\t\\t\\t{wrap}\\n\\t\\t\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t\\t\\t{/each}\\n\\t\\t\\t\\t\\t</tr>\\n\\t\\t\\t\\t</VirtualTable>\\n\\t\\t\\t</div>\\n\\t\\t</Upload>\\n\\t\\t{#if show_scroll_button}\\n\\t\\t\\t<button class=\\"scroll-top-button\\" on:click={scroll_to_top}>\\n\\t\\t\\t\\t&uarr;\\n\\t\\t\\t</button>\\n\\t\\t{/if}\\n\\t</div>\\n</div>\\n{#if data.length === 0 && editable && row_count[1] === \\"dynamic\\"}\\n\\t<EmptyRowButton on_click={() => add_row()} />\\n{/if}\\n\\n{#if active_cell_menu || active_header_menu}\\n\\t<CellMenu\\n\\t\\tx={active_cell_menu?.x ?? active_header_menu?.x ?? 0}\\n\\t\\ty={active_cell_menu?.y ?? active_header_menu?.y ?? 0}\\n\\t\\trow={active_header_menu ? -1 : active_cell_menu?.row ?? 0}\\n\\t\\t{col_count}\\n\\t\\t{row_count}\\n\\t\\ton_add_row_above={() => add_row_at(active_cell_menu?.row ?? -1, \\"above\\")}\\n\\t\\ton_add_row_below={() => add_row_at(active_cell_menu?.row ?? -1, \\"below\\")}\\n\\t\\ton_add_column_left={() =>\\n\\t\\t\\tadd_col_at(\\n\\t\\t\\t\\tactive_cell_menu?.col ?? active_header_menu?.col ?? -1,\\n\\t\\t\\t\\t\\"left\\"\\n\\t\\t\\t)}\\n\\t\\ton_add_column_right={() =>\\n\\t\\t\\tadd_col_at(\\n\\t\\t\\t\\tactive_cell_menu?.col ?? active_header_menu?.col ?? -1,\\n\\t\\t\\t\\t\\"right\\"\\n\\t\\t\\t)}\\n\\t\\ton_delete_row={() => delete_row_at(active_cell_menu?.row ?? -1)}\\n\\t\\ton_delete_col={() =>\\n\\t\\t\\tdelete_col_at(active_cell_menu?.col ?? active_header_menu?.col ?? -1)}\\n\\t\\t{editable}\\n\\t\\tcan_delete_rows={!active_header_menu && data.length > 1 && editable}\\n\\t\\tcan_delete_cols={data.length > 0 && data[0]?.length > 1 && editable}\\n\\t\\t{i18n}\\n\\t\\ton_sort={active_header_menu\\n\\t\\t\\t? (direction) => {\\n\\t\\t\\t\\t\\tif (active_header_menu) {\\n\\t\\t\\t\\t\\t\\thandle_sort(active_header_menu.col, direction);\\n\\t\\t\\t\\t\\t\\tdf_actions.set_active_header_menu(null);\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t}\\n\\t\\t\\t: undefined}\\n\\t\\ton_clear_sort={active_header_menu\\n\\t\\t\\t? () => {\\n\\t\\t\\t\\t\\tclear_sort();\\n\\t\\t\\t\\t\\tdf_actions.set_active_header_menu(null);\\n\\t\\t\\t\\t}\\n\\t\\t\\t: undefined}\\n\\t\\tsort_direction={active_header_menu\\n\\t\\t\\t? $df_state.sort_state.sort_columns.find(\\n\\t\\t\\t\\t\\t(item) => item.col === (active_header_menu?.col ?? -1)\\n\\t\\t\\t\\t)?.direction ?? null\\n\\t\\t\\t: null}\\n\\t\\tsort_priority={active_header_menu\\n\\t\\t\\t? $df_state.sort_state.sort_columns.findIndex(\\n\\t\\t\\t\\t\\t(item) => item.col === (active_header_menu?.col ?? -1)\\n\\t\\t\\t\\t) + 1 || null\\n\\t\\t\\t: null}\\n\\t\\ton_filter={active_header_menu\\n\\t\\t\\t? (datatype, filter, value) => {\\n\\t\\t\\t\\t\\tif (active_header_menu) {\\n\\t\\t\\t\\t\\t\\thandle_filter(active_header_menu.col, datatype, filter, value);\\n\\t\\t\\t\\t\\t\\tdf_actions.set_active_header_menu(null);\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t}\\n\\t\\t\\t: undefined}\\n\\t\\ton_clear_filter={active_header_menu\\n\\t\\t\\t? () => {\\n\\t\\t\\t\\t\\tclear_filter();\\n\\t\\t\\t\\t\\tdf_actions.set_active_header_menu(null);\\n\\t\\t\\t\\t}\\n\\t\\t\\t: undefined}\\n\\t\\tfilter_active={active_header_menu\\n\\t\\t\\t? $df_state.filter_state.filter_columns.some(\\n\\t\\t\\t\\t\\t(c) => c.col === (active_header_menu?.col ?? -1)\\n\\t\\t\\t\\t)\\n\\t\\t\\t: null}\\n\\t/>\\n{/if}\\n\\n<style>\\n\\t.table-container {\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: column;\\n\\t\\tgap: var(--size-2);\\n\\t\\tposition: relative;\\n\\t}\\n\\n\\t.table-wrap {\\n\\t\\tposition: relative;\\n\\t\\ttransition: 150ms;\\n\\t}\\n\\n\\t.table-wrap.menu-open {\\n\\t\\toverflow: hidden;\\n\\t}\\n\\n\\t.table-wrap:focus-within {\\n\\t\\toutline: none;\\n\\t}\\n\\n\\t.table-wrap.dragging {\\n\\t\\tcursor: crosshair !important;\\n\\t\\tuser-select: none;\\n\\t}\\n\\n\\t.table-wrap.dragging * {\\n\\t\\tcursor: crosshair !important;\\n\\t\\tuser-select: none;\\n\\t}\\n\\n\\t.table-wrap > :global(button) {\\n\\t\\tborder: 1px solid var(--border-color-primary);\\n\\t\\tborder-radius: var(--table-radius);\\n\\t\\toverflow: hidden;\\n\\t}\\n\\n\\ttable {\\n\\t\\tposition: absolute;\\n\\t\\topacity: 0;\\n\\t\\tz-index: -1;\\n\\t\\ttransition: 150ms;\\n\\t\\twidth: var(--size-full);\\n\\t\\ttable-layout: auto;\\n\\t\\tcolor: var(--body-text-color);\\n\\t\\tfont-size: var(--input-text-size);\\n\\t\\tline-height: var(--line-md);\\n\\t\\tfont-family: var(--font-mono);\\n\\t\\tborder-spacing: 0;\\n\\t\\tborder-collapse: separate;\\n\\t}\\n\\n\\tthead {\\n\\t\\tposition: sticky;\\n\\t\\ttop: 0;\\n\\t\\tz-index: 5;\\n\\t\\tbox-shadow: var(--shadow-drop);\\n\\t}\\n\\n\\tthead :global(th.pinned-column) {\\n\\t\\tposition: sticky;\\n\\t\\tz-index: 6;\\n\\t\\tbackground: var(--table-even-background-fill) !important;\\n\\t}\\n\\n\\t.dragging {\\n\\t\\tborder-color: var(--color-accent);\\n\\t}\\n\\n\\t.no-wrap {\\n\\t\\twhite-space: nowrap;\\n\\t}\\n\\n\\tdiv:not(.no-wrap) td {\\n\\t\\toverflow-wrap: anywhere;\\n\\t}\\n\\n\\tdiv.no-wrap td {\\n\\t\\toverflow-x: hidden;\\n\\t}\\n\\n\\ttr {\\n\\t\\tbackground: var(--table-even-background-fill);\\n\\t}\\n\\n\\ttr.row-odd {\\n\\t\\tbackground: var(--table-odd-background-fill);\\n\\t}\\n\\n\\t.header-row {\\n\\t\\tdisplay: flex;\\n\\t\\tjustify-content: flex-end;\\n\\t\\talign-items: center;\\n\\t\\tgap: var(--size-2);\\n\\t\\tmin-height: var(--size-6);\\n\\t\\tflex-wrap: nowrap;\\n\\t\\twidth: 100%;\\n\\t}\\n\\n\\t.header-row .label {\\n\\t\\tflex: 1 1 auto;\\n\\t\\tmargin-right: auto;\\n\\t}\\n\\n\\t.header-row .label p {\\n\\t\\tmargin: 0;\\n\\t\\tcolor: var(--block-label-text-color);\\n\\t\\tfont-size: var(--block-label-text-size);\\n\\t\\tline-height: var(--line-sm);\\n\\t\\tposition: relative;\\n\\t\\tz-index: 4;\\n\\t}\\n\\n\\t.scroll-top-button {\\n\\t\\tposition: absolute;\\n\\t\\tright: var(--size-4);\\n\\t\\tbottom: var(--size-4);\\n\\t\\twidth: var(--size-8);\\n\\t\\theight: var(--size-8);\\n\\t\\tborder-radius: var(--table-radius);\\n\\t\\tbackground: var(--color-accent);\\n\\t\\tcolor: white;\\n\\t\\tborder: none;\\n\\t\\tcursor: pointer;\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tjustify-content: center;\\n\\t\\tfont-size: var(--text-lg);\\n\\t\\tz-index: 9;\\n\\t\\topacity: 0.5;\\n\\t}\\n\\n\\t.scroll-top-button:hover {\\n\\t\\topacity: 1;\\n\\t}\\n\\n\\ttr {\\n\\t\\tborder-bottom: 1px solid var(--border-color-primary);\\n\\t\\ttext-align: left;\\n\\t}</style>\\n"],"names":[],"mappings":"AA+1BC,8CAAiB,CAChB,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,GAAG,CAAE,IAAI,QAAQ,CAAC,CAClB,QAAQ,CAAE,QACX,CAEA,yCAAY,CACX,QAAQ,CAAE,QAAQ,CAClB,UAAU,CAAE,KACb,CAEA,WAAW,wCAAW,CACrB,QAAQ,CAAE,MACX,CAEA,yCAAW,aAAc,CACxB,OAAO,CAAE,IACV,CAEA,WAAW,uCAAU,CACpB,MAAM,CAAE,SAAS,CAAC,UAAU,CAC5B,WAAW,CAAE,IACd,CAEA,WAAW,wBAAS,CAAC,eAAE,CACtB,MAAM,CAAE,SAAS,CAAC,UAAU,CAC5B,WAAW,CAAE,IACd,CAEA,0BAAW,CAAW,MAAQ,CAC7B,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,cAAc,CAAC,CAClC,QAAQ,CAAE,MACX,CAEA,mCAAM,CACL,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,CAAC,CACV,OAAO,CAAE,EAAE,CACX,UAAU,CAAE,KAAK,CACjB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,YAAY,CAAE,IAAI,CAClB,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,SAAS,CAAE,IAAI,iBAAiB,CAAC,CACjC,WAAW,CAAE,IAAI,SAAS,CAAC,CAC3B,WAAW,CAAE,IAAI,WAAW,CAAC,CAC7B,cAAc,CAAE,CAAC,CACjB,eAAe,CAAE,QAClB,CAEA,mCAAM,CACL,QAAQ,CAAE,MAAM,CAChB,GAAG,CAAE,CAAC,CACN,OAAO,CAAE,CAAC,CACV,UAAU,CAAE,IAAI,aAAa,CAC9B,CAEA,oBAAK,CAAS,gBAAkB,CAC/B,QAAQ,CAAE,MAAM,CAChB,OAAO,CAAE,CAAC,CACV,UAAU,CAAE,IAAI,4BAA4B,CAAC,CAAC,UAC/C,CAEA,uCAAU,CACT,YAAY,CAAE,IAAI,cAAc,CACjC,CAEA,sCAAS,CACR,WAAW,CAAE,MACd,CAEA,kBAAG,KAAK,QAAQ,CAAC,CAAC,iBAAG,CACpB,aAAa,CAAE,QAChB,CAEA,GAAG,uBAAQ,CAAC,iBAAG,CACd,UAAU,CAAE,MACb,CAEA,gCAAG,CACF,UAAU,CAAE,IAAI,4BAA4B,CAC7C,CAEA,EAAE,sCAAS,CACV,UAAU,CAAE,IAAI,2BAA2B,CAC5C,CAEA,yCAAY,CACX,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,QAAQ,CACzB,WAAW,CAAE,MAAM,CACnB,GAAG,CAAE,IAAI,QAAQ,CAAC,CAClB,UAAU,CAAE,IAAI,QAAQ,CAAC,CACzB,SAAS,CAAE,MAAM,CACjB,KAAK,CAAE,IACR,CAEA,0BAAW,CAAC,qBAAO,CAClB,IAAI,CAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CACd,YAAY,CAAE,IACf,CAEA,0BAAW,CAAC,MAAM,CAAC,gBAAE,CACpB,MAAM,CAAE,CAAC,CACT,KAAK,CAAE,IAAI,wBAAwB,CAAC,CACpC,SAAS,CAAE,IAAI,uBAAuB,CAAC,CACvC,WAAW,CAAE,IAAI,SAAS,CAAC,CAC3B,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,CACV,CAEA,gDAAmB,CAClB,QAAQ,CAAE,QAAQ,CAClB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,aAAa,CAAE,IAAI,cAAc,CAAC,CAClC,UAAU,CAAE,IAAI,cAAc,CAAC,CAC/B,KAAK,CAAE,KAAK,CACZ,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,OAAO,CACf,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,OAAO,CAAE,CAAC,CACV,OAAO,CAAE,GACV,CAEA,gDAAkB,MAAO,CACxB,OAAO,CAAE,CACV,CAEA,gCAAG,CACF,aAAa,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CACpD,UAAU,CAAE,IACb"}'};function vt(){return Math.random().toString(36).substring(2,15)}function Tt(n){return `var(--cell-width-${n})`}const Zn=create_ssr_component((n,e,t,a)=>{let A,d,l,o,s,i,r,_,C,p,b,k,h,j,{datatype:K}=e,{label:g=null}=e,{show_label:T=!0}=e,{headers:w=[]}=e,{values:I=[]}=e,{col_count:U}=e,{row_count:W}=e,{latex_delimiters:G}=e,{components:Y={}}=e,{editable:y=!0}=e,{wrap:L=!1}=e,{root:P}=e,{i18n:D}=e,{max_height:M=500}=e,{line_breaks:f=!0}=e,{column_widths:V=[]}=e,{show_row_numbers:N=!1}=e,{upload:q}=e,{stream_handler:$}=e,{show_fullscreen_button:tt=!1}=e,{show_copy_button:R=!1}=e,{value_is_output:lt=!1}=e,{max_chars:ct=void 0}=e,{show_search:dt="none"}=e,{pinned_columns:Z=0}=e,{static_columns:Ct=[]}=e,{fullscreen:bt=!1}=e;const et=gn({show_fullscreen_button:tt,show_copy_button:R,show_search:dt,show_row_numbers:N,editable:y,pinned_columns:Z,show_label:T,line_breaks:f,wrap:L,max_height:M,column_widths:V,max_chars:ct,static_columns:Ct}),{state:we,actions:x}=et;j=subscribe(we,c=>h=c);const wt=createEventDispatcher();let F={},ye={},J=Yt(w,U,F,vt),Lt=w,m=[[]],ut,at=[[]],Dt=!1,ht=[];let{display_value:At=null}=e,{styling:ot=null}=e,Rt=J.map(c=>c.value),Pt=m.map(c=>c.map(u=>String(u.value)));function Be(c,u){x.handle_sort(c,u),xt(m,At,ot);}function ke(){x.reset_sort_state(),xt(m,At,ot);}function Me(c,u,v,B){x.handle_filter(c,u,v,B),Et(m,At,ot);}function ze(){x.reset_filter_state(),Et(m,At,ot);}async function Ke(c,u=!1){!y||o===c||U[1]!=="dynamic"||x.set_header_edit(c);}function Ht(c,u){c.target instanceof HTMLAnchorElement||(c.preventDefault(),c.stopPropagation(),y&&(x.set_editing(!1),x.handle_header_click(u,y),H.focus()));}function Xt(c){y&&(x.end_header_edit(c.detail.key),H.focus());}async function Ft(c){if(H.focus(),W[1]!=="dynamic")return;const u=Array(m[0]?.length||w.length).fill(0).map((v,B)=>{const O=vt();return F[O]={cell:null,input:null},{id:O,value:""}});m.length===0?m=[u]:c!==void 0&&c>=0&&c<=m.length?m.splice(c,0,u):m.push(u),d=[c!==void 0?c:m.length-1,0];}async function Qe(c){if(H.focus(),U[1]!=="dynamic")return;const u=x.add_col(m,w,vt,c);u.data.forEach(v=>{v.forEach(B=>{F[B.id]||(F[B.id]={cell:null,input:null});});}),m=u.data,w=u.headers,await tick(),requestAnimationFrame(()=>{Ke(c!==void 0?c:m[0].length-1,!0);const v=H.querySelectorAll("tbody")[1].offsetWidth;H.querySelectorAll("table")[1].scrollTo({left:v});});}let ft=[],H,$t;let Ot=I.slice(0,M/I.length*37).length*37+37,ne=0;function xt(c,u,v){const B=ln(c,u,v,h.sort_state.sort_columns,d,he);m=B.data,d=B.selected;}function Et(c,u,v){const B=on(c,u,v,h.filter_state.filter_columns,d,he,h.filter_state.initial_data?.data,h.filter_state.initial_data?.display_value,h.filter_state.initial_data?.styling);m=B.data,d=B.selected;}const Ue=c=>{x.set_copy_flash(c);};let Ae=[];function Oe(c){const{blur_event:u,coords:v}=c.detail;Hn(u,et,v);}function oe(c,u){if(c.stopPropagation(),r&&r.col===u)x.set_active_header_menu(null);else {const v=c.target.closest("th");if(v){const B=v.getBoundingClientRect();x.set_active_header_menu({col:u,x:B.right,y:B.bottom});}}}function Ge(c){if(U[1]!=="dynamic"||m[0].length<=1)return;const u=x.delete_col_at(m,w,c);m=u.data,w=u.headers,J=Yt(w,U,F,vt),x.set_active_cell_menu(null),x.set_active_header_menu(null),x.set_selected(!1),x.set_selected_cells([]),x.set_editing(!1);}function Se(c){m=x.delete_row_at(m,c),x.set_active_cell_menu(null),x.set_active_header_menu(null);}let re;function We(){if(h.current_search_query&&dt==="filter"){const c=[],u=[],v=[];at.forEach(O=>{const it=[],kt=[],Mt=[];O.forEach(mt=>{it.push(mt.value),kt.push(mt.display_value!==void 0?mt.display_value:String(mt.value)),Mt.push(mt.styling||"");}),c.push(it),u.push(kt),v.push(Mt);});const B={data:c,headers:J.map(O=>O.value),metadata:{display_value:u,styling:v}};wt("change",B),lt||wt("input"),x.handle_search(null);}}let ae,Gt=!1;function se(c,u){const v=u==="above"?c:c+1;Ft(v),i=null,r=null;}function ce(c,u){const v=u==="left"?c:c+1;Qe(v),i=null,r=null;}function de(){x.reset_sort_state();}let _t=!1,St=null,Ce=null;const It={is_dragging:_t,drag_start:St,mouse_down_pos:Ce};function Ye(c,u){return h.current_search_query!==void 0&&at?.[c]?.[u]?at[c][u].display_value!==void 0?at[c][u].display_value:String(at[c][u].value):m?.[c]?.[u]?m[c][u].display_value!==void 0?m[c][u].display_value:String(m[c][u].value):""}e.datatype===void 0&&t.datatype&&K!==void 0&&t.datatype(K),e.label===void 0&&t.label&&g!==void 0&&t.label(g),e.show_label===void 0&&t.show_label&&T!==void 0&&t.show_label(T),e.headers===void 0&&t.headers&&w!==void 0&&t.headers(w),e.values===void 0&&t.values&&I!==void 0&&t.values(I),e.col_count===void 0&&t.col_count&&U!==void 0&&t.col_count(U),e.row_count===void 0&&t.row_count&&W!==void 0&&t.row_count(W),e.latex_delimiters===void 0&&t.latex_delimiters&&G!==void 0&&t.latex_delimiters(G),e.components===void 0&&t.components&&Y!==void 0&&t.components(Y),e.editable===void 0&&t.editable&&y!==void 0&&t.editable(y),e.wrap===void 0&&t.wrap&&L!==void 0&&t.wrap(L),e.root===void 0&&t.root&&P!==void 0&&t.root(P),e.i18n===void 0&&t.i18n&&D!==void 0&&t.i18n(D),e.max_height===void 0&&t.max_height&&M!==void 0&&t.max_height(M),e.line_breaks===void 0&&t.line_breaks&&f!==void 0&&t.line_breaks(f),e.column_widths===void 0&&t.column_widths&&V!==void 0&&t.column_widths(V),e.show_row_numbers===void 0&&t.show_row_numbers&&N!==void 0&&t.show_row_numbers(N),e.upload===void 0&&t.upload&&q!==void 0&&t.upload(q),e.stream_handler===void 0&&t.stream_handler&&$!==void 0&&t.stream_handler($),e.show_fullscreen_button===void 0&&t.show_fullscreen_button&&tt!==void 0&&t.show_fullscreen_button(tt),e.show_copy_button===void 0&&t.show_copy_button&&R!==void 0&&t.show_copy_button(R),e.value_is_output===void 0&&t.value_is_output&&lt!==void 0&&t.value_is_output(lt),e.max_chars===void 0&&t.max_chars&&ct!==void 0&&t.max_chars(ct),e.show_search===void 0&&t.show_search&&dt!==void 0&&t.show_search(dt),e.pinned_columns===void 0&&t.pinned_columns&&Z!==void 0&&t.pinned_columns(Z),e.static_columns===void 0&&t.static_columns&&Ct!==void 0&&t.static_columns(Ct),e.fullscreen===void 0&&t.fullscreen&&bt!==void 0&&t.fullscreen(bt),e.display_value===void 0&&t.display_value&&At!==void 0&&t.display_value(At),e.styling===void 0&&t.styling&&ot!==void 0&&t.styling(ot),e.reset_sort_state===void 0&&t.reset_sort_state&&de!==void 0&&t.reset_sort_state(de),n.css.add(Fn);let nt,_e,Te=n.head;do{if(nt=!0,n.head=Te,A=h.ui_state.selected_cells,d=h.ui_state.selected,l=h.ui_state.editing,o=h.ui_state.header_edit,s=h.ui_state.selected_header,i=h.ui_state.active_cell_menu,r=h.ui_state.active_header_menu,_=h.ui_state.copy_flash,!u(I,ut)){const c=I.length===0||I.length===1&&I[0].length===0,u=ut!==void 0&&(I.length!==ut.length||I[0]&&ut[0]&&I[0].length!==ut[0].length);m=Vn(I,F,ye,vt,At),ut=JSON.parse(JSON.stringify(I)),c||u?x.reset_sort_state():h.sort_state.sort_columns.length>0?xt(m,At,ot):(x.handle_sort(-1,"asc"),x.reset_sort_state()),h.filter_state.filter_columns.length>0?Et(m,At,ot):x.reset_filter_state(),h.current_search_query&&x.handle_search(null);}if(C=Z&&m?.[0]?.length?Math.min(Z,m[0].length):0,u(w,Lt)||(J=Yt(w,U,F,vt),Lt=JSON.parse(JSON.stringify(w))),(m||J||F)&&(et.data=m,et.headers=J,et.els=F,et.display_value=At,et.styling=ot),h.current_search_query!==void 0){const c=new Map;ht=[],m.forEach((v,B)=>{v.some(O=>String(O?.value).toLowerCase().includes(h.current_search_query?.toLowerCase()||""))&&ht.push(B),v.forEach((O,it)=>{c.set(O.id,{value:O.value,display_value:O.display_value!==void 0?O.display_value:String(O.value),styling:ot?.[B]?.[it]||""});});}),at=x.filter_data(m).map(v=>v.map(B=>{const O=c.get(B.id);return {...B,display_value:O?.display_value!==void 0?O.display_value:String(B.value),styling:O?.styling||""}}));}else ht=[];if((m||J)&&(x.trigger_change(m.map((c,u)=>c.map((v,B)=>{const O=Array.isArray(K)?K[B]:K;return {...v,value:Rn(v.value,O)}})),J,Pt,Rt,lt,wt),Pt=m.map(c=>c.map(u=>String(u.value))),Rt=J.map(c=>c.value)),h.filter_state.filter_columns.length>0&&Et(m,At,ot),h.sort_state.sort_columns.length>0&&(xt(m,At,ot),x.update_row_order(m)),p=rn(m),b=!!d&&d[0],_&&!u(A,Ae)&&Ue(!1),Ae=A,d!==!1&&(re=d),d!==!1){const c=fn(d,m,F,H,$t);document.documentElement.style.setProperty("--selected-col-pos",c.col_pos),document.documentElement.style.setProperty("--selected-row-pos",c.row_pos||"0px");}_t=It.is_dragging,St=It.drag_start,Ce=It.mouse_down_pos,k=(()=>{}),_e=` <div class="table-container svelte-1vwr9xf">${g&&g.length!==0&&T||tt||R||dt!=="none"?`<div class="header-row svelte-1vwr9xf">${g&&g.length!==0&&T?`<div class="label svelte-1vwr9xf"><p class="svelte-1vwr9xf">${escape(g)}</p></div>`:""} ${validate_component(Dn,"Toolbar").$$render(n,{show_fullscreen_button:tt,fullscreen:bt,on_copy:async()=>await sn(m),show_copy_button:R,show_search:dt,on_commit_filter:We,current_search_query:h.current_search_query},{},{})}</div>`:""} <div class="${["table-wrap svelte-1vwr9xf",(_t?"dragging":"")+" "+(L?"":"no-wrap")+" "+(i||r?"menu-open":"")].join(" ").trim()}" style="${"height:"+escape(Ot,!0)+"px;"}" role="grid" tabindex="0"${add_attribute("this",H,0)}><table aria-hidden="true" class="svelte-1vwr9xf"${add_attribute("this",$t,0)}>${g&&g.length!==0?`<caption class="sr-only svelte-1vwr9xf">${escape(g)}</caption>`:""} <thead class="svelte-1vwr9xf"><tr class="svelte-1vwr9xf">${N?`${validate_component(zt,"RowNumber").$$render(n,{is_header:!0},{},{})}`:""} ${each(J,({value:c,id:u},v)=>`${validate_component(me,"TableHeader").$$render(n,{i:v,actual_pinned_columns:C,header_edit:o,selected_header:s,headers:w,get_cell_width:Tt,handle_header_click:Ht,toggle_header_menu:oe,end_header_edit:Xt,sort_columns:h.sort_state.sort_columns,filter_columns:h.filter_state.filter_columns,latex_delimiters:G,line_breaks:f,max_chars:ct,editable:y,is_static:Ct.includes(v),i18n:D,col_count:U,value:J[v].value,el:F[u].input},{value:B=>{J[v].value=B,nt=!1;},el:B=>{F[u].input=B,nt=!1;}},{})}`)}</tr></thead> <tbody class="svelte-1vwr9xf"><tr class="svelte-1vwr9xf">${N?`${validate_component(zt,"RowNumber").$$render(n,{index:0},{},{})}`:""} ${each(p,({value:c,id:u},v)=>`<td tabindex="-1" class="svelte-1vwr9xf"${add_attribute("this",ft[v],0)}><div class="cell-wrap svelte-1vwr9xf">${validate_component(qt,"EditableCell").$$render(n,{value:c,latex_delimiters:G,line_breaks:f,datatype:Array.isArray(K)?K[v]:K,edit:!1,el:null,editable:y,i18n:D,show_selection_buttons:A.length===1&&A[0][0]===0&&A[0][1]===v,coords:re,on_select_column:x.handle_select_column,on_select_row:x.handle_select_row,is_dragging:_t},{},{})}</div> </td>`)}</tr></tbody></table> ${validate_component(ge$1,"Upload").$$render(n,{upload:q,stream_handler:$,flex:!1,center:!1,boundedheight:!1,disable_click:!0,root:P,aria_label:D("dataframe.drop_to_upload"),dragging:Dt},{dragging:c=>{Dt=c,nt=!1;}},{default:()=>`<div class="table-wrap svelte-1vwr9xf">${validate_component(Yn,"VirtualTable").$$render(n,{max_height:M,selected:b,disable_scroll:i!==null||r!==null,items:at,actual_height:Ot,table_scrollbar_width:ne,viewport:ae,show_scroll_button:Gt},{items:c=>{at=c,nt=!1;},actual_height:c=>{Ot=c,nt=!1;},table_scrollbar_width:c=>{ne=c,nt=!1;},viewport:c=>{ae=c,nt=!1;},show_scroll_button:c=>{Gt=c,nt=!1;}},{tbody:({index:c,item:u})=>`<tr slot="tbody" class="${["svelte-1vwr9xf",c%2===0?"row-odd":""].join(" ").trim()}">${N?`${validate_component(zt,"RowNumber").$$render(n,{index:c},{},{})}`:""} ${each(u,({value:v,id:B},O)=>`${validate_component(On,"TableCell").$$render(n,{display_value:Ye(c,O),index:h.current_search_query!==void 0&&ht[c]!==void 0?ht[c]:c,j:O,actual_pinned_columns:C,get_cell_width:Tt,handle_cell_click:(it,kt,Mt)=>k(it,kt,Mt),handle_blur:Oe,toggle_cell_menu:x.toggle_cell_menu,is_cell_selected:dn,should_show_cell_menu:_n,selected_cells:A,copy_flash:_,active_cell_menu:i,styling:at[c][O].styling,latex_delimiters:G,line_breaks:f,datatype:Array.isArray(K)?K[O]:K,editing:l,max_chars:ct,editable:y,is_static:Ct.includes(O),i18n:D,components:Y,handle_select_column:x.handle_select_column,handle_select_row:x.handle_select_row,is_dragging:_t,wrap:L,value:at[c][O].value,el:F[B]},{value:it=>{at[c][O].value=it,nt=!1;},el:it=>{F[B]=it,nt=!1;}},{})}`)}</tr>`,thead:()=>`<tr slot="thead" class="svelte-1vwr9xf">${N?`${validate_component(zt,"RowNumber").$$render(n,{is_header:!0},{},{})}`:""} ${each(J,({value:c,id:u},v)=>`${validate_component(me,"TableHeader").$$render(n,{i:v,actual_pinned_columns:C,header_edit:o,selected_header:s,headers:w,get_cell_width:Tt,handle_header_click:Ht,toggle_header_menu:oe,end_header_edit:Xt,sort_columns:h.sort_state.sort_columns,filter_columns:h.filter_state.filter_columns,latex_delimiters:G,line_breaks:f,max_chars:ct,editable:y,is_static:Ct.includes(v),i18n:D,col_count:U,value:J[v].value,el:F[u].input},{value:B=>{J[v].value=B,nt=!1;},el:B=>{F[u].input=B,nt=!1;}},{})}`)}</tr>`,default:()=>`${g&&g.length!==0?`<caption class="sr-only svelte-1vwr9xf">${escape(g)}</caption>`:""}`})}</div>`})} ${Gt?'<button class="scroll-top-button svelte-1vwr9xf" data-svelte-h="svelte-oaisf6">↑</button>':""}</div></div> ${m.length===0&&y&&W[1]==="dynamic"?`${validate_component(Sn,"EmptyRowButton").$$render(n,{on_click:()=>Ft()},{},{})}`:""} ${i||r?`${validate_component(qn,"CellMenu").$$render(n,{x:i?.x??r?.x??0,y:i?.y??r?.y??0,row:r?-1:i?.row??0,col_count:U,row_count:W,on_add_row_above:()=>se(i?.row??-1,"above"),on_add_row_below:()=>se(i?.row??-1,"below"),on_add_column_left:()=>ce(i?.col??r?.col??-1,"left"),on_add_column_right:()=>ce(i?.col??r?.col??-1,"right"),on_delete_row:()=>Se(i?.row??-1),on_delete_col:()=>Ge(i?.col??r?.col??-1),editable:y,can_delete_rows:!r&&m.length>1&&y,can_delete_cols:m.length>0&&m[0]?.length>1&&y,i18n:D,on_sort:r?c=>{r&&(Be(r.col,c),x.set_active_header_menu(null));}:void 0,on_clear_sort:r?()=>{ke(),x.set_active_header_menu(null);}:void 0,sort_direction:r?h.sort_state.sort_columns.find(c=>c.col===(r?.col??-1))?.direction??null:null,sort_priority:r&&h.sort_state.sort_columns.findIndex(c=>c.col===(r?.col??-1))+1||null,on_filter:r?(c,u,v)=>{r&&(Me(r.col,c,u,v),x.set_active_header_menu(null));}:void 0,on_clear_filter:r?()=>{ze(),x.set_active_header_menu(null);}:void 0,filter_active:r?h.filter_state.filter_columns.some(c=>c.col===(r?.col??-1)):null},{},{})}`:""}`;}while(!nt);return j(),_e}),Jn=Zn,dl=create_ssr_component((n,e,t,a)=>{let{elem_id:A=""}=e,{elem_classes:d=[]}=e,{visible:l=!0}=e,{value:o={data:[["","",""]],headers:["1","2","3"],metadata:null}}=e,{value_is_output:s=!1}=e,{col_count:i}=e,{row_count:r}=e,{label:_=null}=e,{show_label:C=!0}=e,{wrap:p}=e,{datatype:b}=e,{scale:k=null}=e,{min_width:S=void 0}=e,{root:h}=e,{line_breaks:j=!0}=e,{column_widths:K=[]}=e,{gradio:g}=e,{latex_delimiters:T}=e,{max_height:w=void 0}=e,{loading_status:I}=e,{interactive:U}=e,{show_fullscreen_button:W=!1}=e,{max_chars:G=void 0}=e,{show_copy_button:Y=!1}=e,{show_row_numbers:y=!1}=e,{show_search:L="none"}=e,{pinned_columns:P=0}=e,{static_columns:D=[]}=e,{fullscreen:M=!1}=e;e.elem_id===void 0&&t.elem_id&&A!==void 0&&t.elem_id(A),e.elem_classes===void 0&&t.elem_classes&&d!==void 0&&t.elem_classes(d),e.visible===void 0&&t.visible&&l!==void 0&&t.visible(l),e.value===void 0&&t.value&&o!==void 0&&t.value(o),e.value_is_output===void 0&&t.value_is_output&&s!==void 0&&t.value_is_output(s),e.col_count===void 0&&t.col_count&&i!==void 0&&t.col_count(i),e.row_count===void 0&&t.row_count&&r!==void 0&&t.row_count(r),e.label===void 0&&t.label&&_!==void 0&&t.label(_),e.show_label===void 0&&t.show_label&&C!==void 0&&t.show_label(C),e.wrap===void 0&&t.wrap&&p!==void 0&&t.wrap(p),e.datatype===void 0&&t.datatype&&b!==void 0&&t.datatype(b),e.scale===void 0&&t.scale&&k!==void 0&&t.scale(k),e.min_width===void 0&&t.min_width&&S!==void 0&&t.min_width(S),e.root===void 0&&t.root&&h!==void 0&&t.root(h),e.line_breaks===void 0&&t.line_breaks&&j!==void 0&&t.line_breaks(j),e.column_widths===void 0&&t.column_widths&&K!==void 0&&t.column_widths(K),e.gradio===void 0&&t.gradio&&g!==void 0&&t.gradio(g),e.latex_delimiters===void 0&&t.latex_delimiters&&T!==void 0&&t.latex_delimiters(T),e.max_height===void 0&&t.max_height&&w!==void 0&&t.max_height(w),e.loading_status===void 0&&t.loading_status&&I!==void 0&&t.loading_status(I),e.interactive===void 0&&t.interactive&&U!==void 0&&t.interactive(U),e.show_fullscreen_button===void 0&&t.show_fullscreen_button&&W!==void 0&&t.show_fullscreen_button(W),e.max_chars===void 0&&t.max_chars&&G!==void 0&&t.max_chars(G),e.show_copy_button===void 0&&t.show_copy_button&&Y!==void 0&&t.show_copy_button(Y),e.show_row_numbers===void 0&&t.show_row_numbers&&y!==void 0&&t.show_row_numbers(y),e.show_search===void 0&&t.show_search&&L!==void 0&&t.show_search(L),e.pinned_columns===void 0&&t.pinned_columns&&P!==void 0&&t.pinned_columns(P),e.static_columns===void 0&&t.static_columns&&D!==void 0&&t.static_columns(D),e.fullscreen===void 0&&t.fullscreen&&M!==void 0&&t.fullscreen(M);let f,V,N=n.head;do f=!0,n.head=N,V=`   ${validate_component(mt,"Block").$$render(n,{visible:l,padding:!1,elem_id:A,elem_classes:d,container:!1,scale:k,min_width:S,overflow_behavior:"visible",fullscreen:M},{fullscreen:q=>{M=q,f=!1;}},{default:()=>`${validate_component(zA,"StatusTracker").$$render(n,Object.assign({},{autoscroll:g.autoscroll},{i18n:g.i18n},I),{},{})} ${validate_component(Jn,"Table").$$render(n,{root:h,label:_,show_label:C,row_count:r,col_count:i,values:o.data,display_value:o.metadata?.display_value,styling:o.metadata?.styling,headers:o.headers,fullscreen:M,wrap:p,datatype:b,latex_delimiters:T,editable:U,max_height:w,i18n:g.i18n,line_breaks:j,column_widths:K,upload:(...q)=>g.client.upload(...q),stream_handler:(...q)=>g.client.stream(...q),show_fullscreen_button:W,max_chars:G,show_copy_button:Y,show_row_numbers:y,show_search:L,pinned_columns:P,components:{image:Rt},static_columns:D,value_is_output:s},{value_is_output:q=>{s=q,f=!1;}},{})}`})}`;while(!f);return V});

export { Jn as BaseDataFrame, dl as default };
//# sourceMappingURL=Index61-Bwabb1-E.js.map
