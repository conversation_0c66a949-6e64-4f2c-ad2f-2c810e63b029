#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API fbgemm_linear_quantize_weight {
  using schema = ::std::tuple<at::Tensor,at::Tensor,double,int64_t> (const at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::fbgemm_linear_quantize_weight";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "fbgemm_linear_quantize_weight(Tensor input) -> (Tensor, Tensor, float, int)";
  static ::std::tuple<at::Tensor,at::Tensor,double,int64_t> call(const at::Tensor & input);
  static ::std::tuple<at::Tensor,at::Tensor,double,int64_t> redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & input);
};

}} // namespace at::_ops
