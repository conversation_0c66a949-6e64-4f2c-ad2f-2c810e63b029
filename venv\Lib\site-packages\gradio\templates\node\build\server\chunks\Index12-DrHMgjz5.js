import { c as create_ssr_component, v as validate_component } from './ssr-C3HYbsxA.js';
import G from './Plot-0LMUNZFA.js';
import { m as mt, e as bt, ay as Ae, j as Ke, l as Le, z as zA } from './2-DJbI4FWc.js';
import './index-ClteBeTX.js';
import './Component-NmRBwSfF.js';
import 'path';
import 'url';
import 'fs';

const K=create_ssr_component((t,e,l,D)=>{let{value:v=null}=e,{elem_id:c=""}=e,{elem_classes:i=[]}=e,{visible:d=!0}=e,{loading_status:f}=e,{label:u}=e,{show_label:_}=e,{container:m=!0}=e,{scale:h=null}=e,{min_width:r=void 0}=e,{theme_mode:w}=e,{caption:s}=e,{bokeh_version:B}=e,{gradio:o}=e,{show_actions_button:k=!1}=e,{_selectable:x=!1}=e,{x_lim:n=null}=e,{show_fullscreen_button:I=!1}=e,P=!1;e.value===void 0&&l.value&&v!==void 0&&l.value(v),e.elem_id===void 0&&l.elem_id&&c!==void 0&&l.elem_id(c),e.elem_classes===void 0&&l.elem_classes&&i!==void 0&&l.elem_classes(i),e.visible===void 0&&l.visible&&d!==void 0&&l.visible(d),e.loading_status===void 0&&l.loading_status&&f!==void 0&&l.loading_status(f),e.label===void 0&&l.label&&u!==void 0&&l.label(u),e.show_label===void 0&&l.show_label&&_!==void 0&&l.show_label(_),e.container===void 0&&l.container&&m!==void 0&&l.container(m),e.scale===void 0&&l.scale&&h!==void 0&&l.scale(h),e.min_width===void 0&&l.min_width&&r!==void 0&&l.min_width(r),e.theme_mode===void 0&&l.theme_mode&&w!==void 0&&l.theme_mode(w),e.caption===void 0&&l.caption&&s!==void 0&&l.caption(s),e.bokeh_version===void 0&&l.bokeh_version&&B!==void 0&&l.bokeh_version(B),e.gradio===void 0&&l.gradio&&o!==void 0&&l.gradio(o),e.show_actions_button===void 0&&l.show_actions_button&&k!==void 0&&l.show_actions_button(k),e._selectable===void 0&&l._selectable&&x!==void 0&&l._selectable(x),e.x_lim===void 0&&l.x_lim&&n!==void 0&&l.x_lim(n),e.show_fullscreen_button===void 0&&l.show_fullscreen_button&&I!==void 0&&l.show_fullscreen_button(I);let F,S,L=t.head;do F=!0,t.head=L,S=`${validate_component(mt,"Block").$$render(t,{padding:!1,elem_id:c,elem_classes:i,visible:d,container:m,scale:h,min_width:r,allow_overflow:!1,fullscreen:P},{fullscreen:W=>{P=W,F=!1;}},{default:()=>`${validate_component(bt,"BlockLabel").$$render(t,{show_label:_,label:u||o.i18n("plot.plot"),Icon:Ae},{},{})} ${I?`${validate_component(Ke,"IconButtonWrapper").$$render(t,{},{},{default:()=>`${validate_component(Le,"FullscreenButton").$$render(t,{fullscreen:P},{},{})}`})}`:""} ${validate_component(zA,"StatusTracker").$$render(t,Object.assign({},{autoscroll:o.autoscroll},{i18n:o.i18n},f),{},{})} ${validate_component(G,"Plot").$$render(t,{value:v,theme_mode:w,caption:s,bokeh_version:B,show_actions_button:k,gradio:o,show_label:_,_selectable:x,x_lim:n},{},{})}`})}`;while(!F);return S});

export { G as BasePlot, K as default };
//# sourceMappingURL=Index12-DrHMgjz5.js.map
