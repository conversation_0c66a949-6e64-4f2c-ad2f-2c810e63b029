import{ar as l,an as h,ao as c}from"./index.BoI39RQH.js";import{GLTFLoader as p}from"./glTFLoader.BetPWe9U.js";const o="KHR_materials_ior";class i{constructor(e){this.name=o,this.order=180,this._loader=e,this.enabled=this._loader.isExtensionUsed(o)}dispose(){this._loader=null}loadMaterialPropertiesAsync(e,s,r){return p.LoadExtensionAsync(e,s,this.name,(a,d)=>{const t=new Array;return t.push(this._loader.loadMaterialPropertiesAsync(e,s,r)),t.push(this._loadIorPropertiesAsync(a,d,r)),Promise.all(t).then(()=>{})})}_loadIorPropertiesAsync(e,s,r){if(!(r instanceof l))throw new Error(`${e}: Material type not supported`);return s.ior!==void 0?r.indexOfRefraction=s.ior:r.indexOfRefraction=i._DEFAULT_IOR,Promise.resolve()}}i._DEFAULT_IOR=1.5;h(o);c(o,!0,n=>new i(n));export{i as KHR_materials_ior};
//# sourceMappingURL=KHR_materials_ior.Cq60gFAz.js.map
