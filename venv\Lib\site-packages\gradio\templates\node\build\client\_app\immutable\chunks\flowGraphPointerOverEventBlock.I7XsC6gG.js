import{c as o,_ as i}from"./KHR_interactivity.DEAVS2UW.js";import{b as a,R as n}from"./declarationMapper.UBCwU7BT.js";import{R as p}from"./index.BoI39RQH.js";class u extends o{constructor(e){super(e),this.type="PointerOver",this.pointerId=this.registerDataOutput("pointerId",a),this.targetMesh=this.registerDataInput("targetMesh",n,e==null?void 0:e.targetMesh),this.meshUnderPointer=this.registerDataOutput("meshUnderPointer",n)}_executeEvent(e,t){var s;const r=this.targetMesh.getValue(e);this.meshUnderPointer.setValue(t.mesh,e);const h=t.out&&i(t.out,r);return this.pointerId.setValue(t.pointerId,e),!h&&(t.mesh===r||i(t.mesh,r))?(this._execute(e),!((s=this.config)!=null&&s.stopPropagation)):!0}_preparePendingTasks(e){}_cancelPendingTasks(e){}getClassName(){return"FlowGraphPointerOverEventBlock"}}p("FlowGraphPointerOverEventBlock",u);export{u as FlowGraphPointerOverEventBlock};
//# sourceMappingURL=flowGraphPointerOverEventBlock.I7XsC6gG.js.map
