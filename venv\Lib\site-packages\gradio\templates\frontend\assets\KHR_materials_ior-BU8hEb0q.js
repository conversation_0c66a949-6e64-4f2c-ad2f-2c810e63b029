import{ar as p,an as m,ao as l}from"./index-Dpxo-yl_.js";import{GLTFLoader as h}from"./glTFLoader-9Z3KGax5.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./bone-kZWM5-u7.js";import"./rawTexture-DmvUfjqF.js";import"./assetContainer-BRzQBugc.js";import"./objectModelMapping-BR4RdEzn.js";const s="KHR_materials_ior";class i{constructor(r){this.name=s,this.order=180,this._loader=r,this.enabled=this._loader.isExtensionUsed(s)}dispose(){this._loader=null}loadMaterialPropertiesAsync(r,o,e){return h.LoadExtensionAsync(r,o,this.name,(a,d)=>{const t=new Array;return t.push(this._loader.loadMaterialPropertiesAsync(r,o,e)),t.push(this._loadIorPropertiesAsync(a,d,e)),Promise.all(t).then(()=>{})})}_loadIorPropertiesAsync(r,o,e){if(!(e instanceof p))throw new Error(`${r}: Material type not supported`);return o.ior!==void 0?e.indexOfRefraction=o.ior:e.indexOfRefraction=i._DEFAULT_IOR,Promise.resolve()}}i._DEFAULT_IOR=1.5;m(s);l(s,!0,n=>new i(n));export{i as KHR_materials_ior};
//# sourceMappingURL=KHR_materials_ior-BU8hEb0q.js.map
