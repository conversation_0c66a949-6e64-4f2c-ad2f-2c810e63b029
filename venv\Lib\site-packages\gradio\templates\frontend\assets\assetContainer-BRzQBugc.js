import{ab as y,aM as x,b as S,h as I,aa as B,aN as T,M as R,a as O,aO as q,R as V,ak as E,aq as D,aL as C,aP as L}from"./index-Dpxo-yl_.js";y._instancedMeshFactory=(f,e)=>{const t=new b(f,e);if(e.instancedBuffers){t.instancedBuffers={};for(const s in e.instancedBuffers)t.instancedBuffers[s]=e.instancedBuffers[s]}return t};class b extends x{constructor(e,t){super(e,t.getScene()),this._indexInSourceMeshInstanceArray=-1,this._distanceToCamera=0,t.addInstance(this),this._sourceMesh=t,this._unIndexed=t._unIndexed,this.position.copyFrom(t.position),this.rotation.copyFrom(t.rotation),this.scaling.copyFrom(t.scaling),t.rotationQuaternion&&(this.rotationQuaternion=t.rotationQuaternion.clone()),this.animations=t.animations.slice();for(const s of t.getAnimationRanges())s!=null&&this.createAnimationRange(s.name,s.from,s.to);this.infiniteDistance=t.infiniteDistance,this.setPivotMatrix(t.getPivotMatrix()),this.refreshBoundingInfo(!0,!0),this._syncSubMeshes()}getClassName(){return"InstancedMesh"}get lightSources(){return this._sourceMesh._lightSources}_resyncLightSources(){}_resyncLightSource(){}_removeLightSource(){}get receiveShadows(){return this._sourceMesh.receiveShadows}set receiveShadows(e){this._sourceMesh?.receiveShadows!==e&&S.Warn("Setting receiveShadows on an instanced mesh has no effect")}get material(){return this._sourceMesh.material}set material(e){this._sourceMesh?.material!==e&&S.Warn("Setting material on an instanced mesh has no effect")}get visibility(){return this._sourceMesh.visibility}set visibility(e){this._sourceMesh?.visibility!==e&&S.Warn("Setting visibility on an instanced mesh has no effect")}get skeleton(){return this._sourceMesh.skeleton}set skeleton(e){this._sourceMesh?.skeleton!==e&&S.Warn("Setting skeleton on an instanced mesh has no effect")}get renderingGroupId(){return this._sourceMesh.renderingGroupId}set renderingGroupId(e){!this._sourceMesh||e===this._sourceMesh.renderingGroupId||I.Warn("Note - setting renderingGroupId of an instanced mesh has no effect on the scene")}getTotalVertices(){return this._sourceMesh?this._sourceMesh.getTotalVertices():0}getTotalIndices(){return this._sourceMesh.getTotalIndices()}get sourceMesh(){return this._sourceMesh}get geometry(){return this._sourceMesh._geometry}createInstance(e){return this._sourceMesh.createInstance(e)}isReady(e=!1){return this._sourceMesh.isReady(e,!0)}getVerticesData(e,t,s){return this._sourceMesh.getVerticesData(e,t,s)}copyVerticesData(e,t){this._sourceMesh.copyVerticesData(e,t)}setVerticesData(e,t,s,n){return this.sourceMesh&&this.sourceMesh.setVerticesData(e,t,s,n),this.sourceMesh}updateVerticesData(e,t,s,n){return this.sourceMesh&&this.sourceMesh.updateVerticesData(e,t,s,n),this.sourceMesh}setIndices(e,t=null){return this.sourceMesh&&this.sourceMesh.setIndices(e,t),this.sourceMesh}isVerticesDataPresent(e){return this._sourceMesh.isVerticesDataPresent(e)}getIndices(){return this._sourceMesh.getIndices()}get _positions(){return this._sourceMesh._positions}refreshBoundingInfo(e=!1,t=!1){if(this.hasBoundingInfo&&this.getBoundingInfo().isLocked)return this;let s;typeof e=="object"?s=e:s={applySkeleton:e,applyMorph:t};const n=this._sourceMesh.geometry?this._sourceMesh.geometry.boundingBias:null;return this._refreshBoundingInfo(this._sourceMesh._getData(s,null,B.PositionKind),n),this}_preActivate(){return this._currentLOD&&this._currentLOD._preActivate(),this}_activate(e,t){if(super._activate(e,t),this._sourceMesh.subMeshes||I.Warn("Instances should only be created for meshes with geometry."),this._currentLOD){if(this._currentLOD._getWorldMatrixDeterminant()>=0!=this._getWorldMatrixDeterminant()>=0)return this._internalAbstractMeshDataInfo._actAsRegularMesh=!0,!0;if(this._internalAbstractMeshDataInfo._actAsRegularMesh=!1,this._currentLOD._registerInstanceForRenderId(this,e),t){if(!this._currentLOD._internalAbstractMeshDataInfo._isActiveIntermediate)return this._currentLOD._internalAbstractMeshDataInfo._onlyForInstancesIntermediate=!0,!0}else if(!this._currentLOD._internalAbstractMeshDataInfo._isActive)return this._currentLOD._internalAbstractMeshDataInfo._onlyForInstances=!0,!0}return!1}_postActivate(){this._sourceMesh.edgesShareWithInstances&&this._sourceMesh._edgesRenderer&&this._sourceMesh._edgesRenderer.isEnabled&&this._sourceMesh._renderingGroup?(this._sourceMesh._renderingGroup._edgesRenderers.pushNoDuplicate(this._sourceMesh._edgesRenderer),this._sourceMesh._edgesRenderer.customInstances.push(this.getWorldMatrix())):this._edgesRenderer&&this._edgesRenderer.isEnabled&&this._sourceMesh._renderingGroup&&this._sourceMesh._renderingGroup._edgesRenderers.push(this._edgesRenderer)}getWorldMatrix(){if(this._currentLOD&&this._currentLOD.billboardMode!==T.BILLBOARDMODE_NONE&&this._currentLOD._masterMesh!==this){this._billboardWorldMatrix||(this._billboardWorldMatrix=new R);const e=this._currentLOD._masterMesh;return this._currentLOD._masterMesh=this,O.Vector3[7].copyFrom(this._currentLOD.position),this._currentLOD.position.set(0,0,0),this._billboardWorldMatrix.copyFrom(this._currentLOD.computeWorldMatrix(!0)),this._currentLOD.position.copyFrom(O.Vector3[7]),this._currentLOD._masterMesh=e,this._billboardWorldMatrix}return super.getWorldMatrix()}get isAnInstance(){return!0}getLOD(e){if(!e)return this;const t=this.sourceMesh.getLODLevels();if(!t||t.length===0)this._currentLOD=this.sourceMesh;else{const s=this.getBoundingInfo();this._currentLOD=this.sourceMesh.getLOD(e,s.boundingSphere)}return this._currentLOD}_preActivateForIntermediateRendering(e){return this.sourceMesh._preActivateForIntermediateRendering(e)}_syncSubMeshes(){if(this.releaseSubMeshes(),this._sourceMesh.subMeshes)for(let e=0;e<this._sourceMesh.subMeshes.length;e++)this._sourceMesh.subMeshes[e].clone(this,this._sourceMesh);return this}_generatePointsArray(){return this._sourceMesh._generatePointsArray()}_updateBoundingInfo(){return this.hasBoundingInfo?this.getBoundingInfo().update(this.worldMatrixFromCache):this.buildBoundingInfo(this.absolutePosition,this.absolutePosition,this.worldMatrixFromCache),this._updateSubMeshesBoundingInfo(this.worldMatrixFromCache),this}clone(e,t=null,s,n){const o=(n||this._sourceMesh).createInstance(e);if(q.DeepCopy(this,o,["name","subMeshes","uniqueId","parent","lightSources","receiveShadows","material","visibility","skeleton","sourceMesh","isAnInstance","facetNb","isFacetDataEnabled","isBlocked","useBones","hasInstances","collider","edgesRenderer","forward","up","right","absolutePosition","absoluteScaling","absoluteRotationQuaternion","isWorldMatrixFrozen","nonUniformScaling","behaviors","worldMatrixFromCache","hasThinInstances","hasBoundingInfo","geometry"],[]),this.refreshBoundingInfo(),t&&(o.parent=t),!s)for(let l=0;l<this.getScene().meshes.length;l++){const r=this.getScene().meshes[l];r.parent===this&&r.clone(r.name,o)}return o.computeWorldMatrix(!0),this.onClonedObservable.notifyObservers(o),o}dispose(e,t=!1){this._sourceMesh.removeInstance(this),super.dispose(e,t)}_serializeAsParent(e){super._serializeAsParent(e),e.parentId=this._sourceMesh.uniqueId,e.parentInstanceIndex=this._indexInSourceMeshInstanceArray}instantiateHierarchy(e=null,t,s){const n=this.clone("Clone of "+(this.name||this.id),e||this.parent,!0,t&&t.newSourcedMesh);n&&s&&s(this,n);for(const o of this.getChildTransformNodes(!0))o.instantiateHierarchy(n,t,s);return n}}y.prototype.registerInstancedBuffer=function(f,e){if(this._userInstancedBuffersStorage?.vertexBuffers[f]?.dispose(),!this.instancedBuffers){this.instancedBuffers={};for(const t of this.instances)t.instancedBuffers={}}this._userInstancedBuffersStorage||(this._userInstancedBuffersStorage={data:{},vertexBuffers:{},strides:{},sizes:{},vertexArrayObjects:this.getEngine().getCaps().vertexArrayObject?{}:void 0}),this.instancedBuffers[f]=null,this._userInstancedBuffersStorage.strides[f]=e,this._userInstancedBuffersStorage.sizes[f]=e*32,this._userInstancedBuffersStorage.data[f]=new Float32Array(this._userInstancedBuffersStorage.sizes[f]),this._userInstancedBuffersStorage.vertexBuffers[f]=new B(this.getEngine(),this._userInstancedBuffersStorage.data[f],f,!0,!1,e,!0);for(const t of this.instances)t.instancedBuffers[f]=null;this._invalidateInstanceVertexArrayObject(),this._markSubMeshesAsAttributesDirty()};y.prototype._processInstancedBuffers=function(f,e){const t=f?f.length:0;for(const s in this.instancedBuffers){let n=this._userInstancedBuffersStorage.sizes[s];const o=this._userInstancedBuffersStorage.strides[s],l=(t+1)*o;for(;n<l;)n*=2;this._userInstancedBuffersStorage.data[s].length!=n&&(this._userInstancedBuffersStorage.data[s]=new Float32Array(n),this._userInstancedBuffersStorage.sizes[s]=n,this._userInstancedBuffersStorage.vertexBuffers[s]&&(this._userInstancedBuffersStorage.vertexBuffers[s].dispose(),this._userInstancedBuffersStorage.vertexBuffers[s]=null));const r=this._userInstancedBuffersStorage.data[s];let a=0;if(e){const c=this.instancedBuffers[s];c.toArray?c.toArray(r,a):c.copyToArray?c.copyToArray(r,a):r[a]=c,a+=o}for(let c=0;c<t;c++){const g=f[c].instancedBuffers[s];g.toArray?g.toArray(r,a):g.copyToArray?g.copyToArray(r,a):r[a]=g,a+=o}this._userInstancedBuffersStorage.vertexBuffers[s]?this._userInstancedBuffersStorage.vertexBuffers[s].updateDirectly(r,0):(this._userInstancedBuffersStorage.vertexBuffers[s]=new B(this.getEngine(),this._userInstancedBuffersStorage.data[s],s,!0,!1,o,!0),this._invalidateInstanceVertexArrayObject())}};y.prototype._invalidateInstanceVertexArrayObject=function(){if(!(!this._userInstancedBuffersStorage||this._userInstancedBuffersStorage.vertexArrayObjects===void 0)){for(const f in this._userInstancedBuffersStorage.vertexArrayObjects)this.getEngine().releaseVertexArrayObject(this._userInstancedBuffersStorage.vertexArrayObjects[f]);this._userInstancedBuffersStorage.vertexArrayObjects={}}};y.prototype._disposeInstanceSpecificData=function(){for(this._instanceDataStorage.instancesBuffer&&(this._instanceDataStorage.instancesBuffer.dispose(),this._instanceDataStorage.instancesBuffer=null);this.instances.length;)this.instances[0].dispose();for(const f in this.instancedBuffers)this._userInstancedBuffersStorage.vertexBuffers[f]&&this._userInstancedBuffersStorage.vertexBuffers[f].dispose();this._invalidateInstanceVertexArrayObject(),this.instancedBuffers={}};V("BABYLON.InstancedMesh",b);class w{constructor(){this.rootNodes=[],this.cameras=[],this.lights=[],this.meshes=[],this.skeletons=[],this.particleSystems=[],this.animations=[],this.animationGroups=[],this.multiMaterials=[],this.materials=[],this.morphTargetManagers=[],this.geometries=[],this.transformNodes=[],this.actionManagers=[],this.textures=[],this._environmentTexture=null,this.postProcesses=[],this.sounds=null,this.effectLayers=[],this.layers=[],this.reflectionProbes=[]}get environmentTexture(){return this._environmentTexture}set environmentTexture(e){this._environmentTexture=e}getNodes(){let e=[];return e=e.concat(this.meshes),e=e.concat(this.lights),e=e.concat(this.cameras),e=e.concat(this.transformNodes),this.skeletons.forEach(t=>e=e.concat(t.bones)),e}}class k extends w{}class P{constructor(){this.rootNodes=[],this.skeletons=[],this.animationGroups=[]}dispose(){this.rootNodes.slice(0).forEach(e=>{e.dispose()}),this.rootNodes.length=0,this.skeletons.slice(0).forEach(e=>{e.dispose()}),this.skeletons.length=0,this.animationGroups.slice(0).forEach(e=>{e.dispose()}),this.animationGroups.length=0}}class F extends w{constructor(e){super(),this._wasAddedToScene=!1,e=e||E.LastCreatedScene,e&&(this.scene=e,this.proceduralTextures=[],e.onDisposeObservable.add(()=>{this._wasAddedToScene||this.dispose()}),this._onContextRestoredObserver=e.getEngine().onContextRestoredObservable.add(()=>{for(const t of this.geometries)t._rebuild();for(const t of this.meshes)t._rebuild();for(const t of this.particleSystems)t.rebuild();for(const t of this.textures)t._rebuild()}))}_topologicalSort(e){const t=new Map;for(const r of e)t.set(r.uniqueId,r);const s={dependsOn:new Map,dependedBy:new Map};for(const r of e){const a=r.uniqueId;s.dependsOn.set(a,new Set),s.dependedBy.set(a,new Set)}for(const r of e){const a=r.uniqueId,c=s.dependsOn.get(a);if(r instanceof b){const g=r.sourceMesh;t.has(g.uniqueId)&&(c.add(g.uniqueId),s.dependedBy.get(g.uniqueId).add(a))}const p=s.dependedBy.get(a);for(const g of r.getDescendants()){const M=g.uniqueId;t.has(M)&&(p.add(M),s.dependsOn.get(M).add(a))}}const n=[],o=[];for(const r of e){const a=r.uniqueId;s.dependsOn.get(a).size===0&&(o.push(r),t.delete(a))}const l=o;for(;l.length>0;){const r=l.shift();n.push(r);const a=s.dependedBy.get(r.uniqueId);for(const c of Array.from(a.values())){const p=s.dependsOn.get(c);p.delete(r.uniqueId),p.size===0&&t.get(c)&&(l.push(t.get(c)),t.delete(c))}}return t.size>0&&(I.Error("SceneSerializer._topologicalSort: There were unvisited nodes:"),t.forEach(r=>I.Error(r.name))),n}_addNodeAndDescendantsToList(e,t,s,n){if(!(!s||n&&!n(s)||t.has(s.uniqueId))){e.push(s),t.add(s.uniqueId);for(const o of s.getDescendants(!0))this._addNodeAndDescendantsToList(e,t,o,n)}}_isNodeInContainer(e){return e instanceof x&&this.meshes.indexOf(e)!==-1||e instanceof T&&this.transformNodes.indexOf(e)!==-1||e instanceof D&&this.lights.indexOf(e)!==-1||e instanceof C&&this.cameras.indexOf(e)!==-1}_isValidHierarchy(){for(const e of this.meshes)if(e.parent&&!this._isNodeInContainer(e.parent))return I.Warn(`Node ${e.name} has a parent that is not in the container.`),!1;for(const e of this.transformNodes)if(e.parent&&!this._isNodeInContainer(e.parent))return I.Warn(`Node ${e.name} has a parent that is not in the container.`),!1;for(const e of this.lights)if(e.parent&&!this._isNodeInContainer(e.parent))return I.Warn(`Node ${e.name} has a parent that is not in the container.`),!1;for(const e of this.cameras)if(e.parent&&!this._isNodeInContainer(e.parent))return I.Warn(`Node ${e.name} has a parent that is not in the container.`),!1;return!0}instantiateModelsToScene(e,t=!1,s){this._isValidHierarchy()||S.Warn("SceneSerializer.InstantiateModelsToScene: The Asset Container hierarchy is not valid.");const n={},o={},l=new P,r=[],a=[],c={doNotInstantiate:!0,...s},p=(i,h)=>{if(n[i.uniqueId]=h.uniqueId,o[h.uniqueId]=h,e&&(h.name=e(i.name)),h instanceof y){const u=h;if(u.morphTargetManager){const d=i.morphTargetManager;u.morphTargetManager=d.clone();for(let m=0;m<d.numTargets;m++){const v=d.getTarget(m),_=u.morphTargetManager.getTarget(m);n[v.uniqueId]=_.uniqueId,o[_.uniqueId]=_}}}},g=[],M=new Set;for(const i of this.transformNodes)i.parent===null&&this._addNodeAndDescendantsToList(g,M,i,c.predicate);for(const i of this.meshes)i.parent===null&&this._addNodeAndDescendantsToList(g,M,i,c.predicate);const A=this._topologicalSort(g),N=(i,h)=>{if(p(i,h),i.parent){const u=n[i.parent.uniqueId],d=o[u];d?h.parent=d:h.parent=i.parent}if(h.position&&i.position&&h.position.copyFrom(i.position),h.rotationQuaternion&&i.rotationQuaternion&&h.rotationQuaternion.copyFrom(i.rotationQuaternion),h.rotation&&i.rotation&&h.rotation.copyFrom(i.rotation),h.scaling&&i.scaling&&h.scaling.copyFrom(i.scaling),h.material){const u=h;if(u.material)if(t){const d=i.material;if(a.indexOf(d)===-1){let m=d.clone(e?e(d.name):"Clone of "+d.name);if(a.push(d),n[d.uniqueId]=m.uniqueId,o[m.uniqueId]=m,d.getClassName()==="MultiMaterial"){const v=d;for(const _ of v.subMaterials)_&&(m=_.clone(e?e(_.name):"Clone of "+_.name),a.push(_),n[_.uniqueId]=m.uniqueId,o[m.uniqueId]=m);v.subMaterials=v.subMaterials.map(_=>_&&o[n[_.uniqueId]])}}u.getClassName()!=="InstancedMesh"&&(u.material=o[n[d.uniqueId]])}else u.material.getClassName()==="MultiMaterial"?this.scene.multiMaterials.indexOf(u.material)===-1&&this.scene.addMultiMaterial(u.material):this.scene.materials.indexOf(u.material)===-1&&this.scene.addMaterial(u.material)}h.parent===null&&l.rootNodes.push(h)};return A.forEach(i=>{if(i.getClassName()==="InstancedMesh"){const h=i,u=h.sourceMesh,d=n[u.uniqueId],v=(typeof d=="number"?o[d]:u).createInstance(h.name);N(h,v)}else{let h=!0;i.getClassName()==="TransformNode"||i.getClassName()==="Node"||i.skeleton||!i.getTotalVertices||i.getTotalVertices()===0?h=!1:c.doNotInstantiate&&(typeof c.doNotInstantiate=="function"?h=!c.doNotInstantiate(i):h=!c.doNotInstantiate);const u=h?i.createInstance(`instance of ${i.name}`):i.clone(`Clone of ${i.name}`,null,!0);if(!u)throw new Error(`Could not clone or instantiate node on Asset Container ${i.name}`);N(i,u)}}),this.skeletons.forEach(i=>{if(c.predicate&&!c.predicate(i))return;const h=i.clone(e?e(i.name):"Clone of "+i.name);for(const u of this.meshes)if(u.skeleton===i&&!u.isAnInstance){const d=o[n[u.uniqueId]];if(!d||d.isAnInstance||(d.skeleton=h,r.indexOf(h)!==-1))continue;r.push(h);for(const m of h.bones)m._linkedTransformNode&&(m._linkedTransformNode=o[n[m._linkedTransformNode.uniqueId]])}l.skeletons.push(h)}),this.animationGroups.forEach(i=>{if(c.predicate&&!c.predicate(i))return;const h=i.clone(e?e(i.name):"Clone of "+i.name,u=>o[n[u.uniqueId]]||u);l.animationGroups.push(h)}),l}addAllToScene(){if(!this._wasAddedToScene){this._isValidHierarchy()||S.Warn("SceneSerializer.addAllToScene: The Asset Container hierarchy is not valid."),this._wasAddedToScene=!0,this.addToScene(null),this.environmentTexture&&(this.scene.environmentTexture=this.environmentTexture);for(const e of this.scene._serializableComponents)e.addFromContainer(this);this.scene.getEngine().onContextRestoredObservable.remove(this._onContextRestoredObserver),this._onContextRestoredObserver=null}}addToScene(e=null){const t=[];this.cameras.forEach(s=>{e&&!e(s)||(this.scene.addCamera(s),t.push(s))}),this.lights.forEach(s=>{e&&!e(s)||(this.scene.addLight(s),t.push(s))}),this.meshes.forEach(s=>{e&&!e(s)||(this.scene.addMesh(s),t.push(s))}),this.skeletons.forEach(s=>{e&&!e(s)||this.scene.addSkeleton(s)}),this.animations.forEach(s=>{e&&!e(s)||this.scene.addAnimation(s)}),this.animationGroups.forEach(s=>{e&&!e(s)||this.scene.addAnimationGroup(s)}),this.multiMaterials.forEach(s=>{e&&!e(s)||this.scene.addMultiMaterial(s)}),this.materials.forEach(s=>{e&&!e(s)||this.scene.addMaterial(s)}),this.morphTargetManagers.forEach(s=>{e&&!e(s)||this.scene.addMorphTargetManager(s)}),this.geometries.forEach(s=>{e&&!e(s)||this.scene.addGeometry(s)}),this.transformNodes.forEach(s=>{e&&!e(s)||(this.scene.addTransformNode(s),t.push(s))}),this.actionManagers.forEach(s=>{e&&!e(s)||this.scene.addActionManager(s)}),this.textures.forEach(s=>{e&&!e(s)||this.scene.addTexture(s)}),this.reflectionProbes.forEach(s=>{e&&!e(s)||this.scene.addReflectionProbe(s)});for(const s of t)s.parent&&this.scene.getNodes().indexOf(s.parent)===-1&&(s.setParent?s.setParent(null):s.parent=null)}removeAllFromScene(){this._isValidHierarchy()||S.Warn("SceneSerializer.removeAllFromScene: The Asset Container hierarchy is not valid."),this._wasAddedToScene=!1,this.removeFromScene(null),this.environmentTexture===this.scene.environmentTexture&&(this.scene.environmentTexture=null);for(const e of this.scene._serializableComponents)e.removeFromContainer(this)}removeFromScene(e=null){this.cameras.forEach(t=>{e&&!e(t)||this.scene.removeCamera(t)}),this.lights.forEach(t=>{e&&!e(t)||this.scene.removeLight(t)}),this.meshes.forEach(t=>{e&&!e(t)||this.scene.removeMesh(t,!0)}),this.skeletons.forEach(t=>{e&&!e(t)||this.scene.removeSkeleton(t)}),this.animations.forEach(t=>{e&&!e(t)||this.scene.removeAnimation(t)}),this.animationGroups.forEach(t=>{e&&!e(t)||this.scene.removeAnimationGroup(t)}),this.multiMaterials.forEach(t=>{e&&!e(t)||this.scene.removeMultiMaterial(t)}),this.materials.forEach(t=>{e&&!e(t)||this.scene.removeMaterial(t)}),this.morphTargetManagers.forEach(t=>{e&&!e(t)||this.scene.removeMorphTargetManager(t)}),this.geometries.forEach(t=>{e&&!e(t)||this.scene.removeGeometry(t)}),this.transformNodes.forEach(t=>{e&&!e(t)||this.scene.removeTransformNode(t)}),this.actionManagers.forEach(t=>{e&&!e(t)||this.scene.removeActionManager(t)}),this.textures.forEach(t=>{e&&!e(t)||this.scene.removeTexture(t)}),this.reflectionProbes.forEach(t=>{e&&!e(t)||this.scene.removeReflectionProbe(t)})}dispose(){this.cameras.slice(0).forEach(e=>{e.dispose()}),this.cameras.length=0,this.lights.slice(0).forEach(e=>{e.dispose()}),this.lights.length=0,this.meshes.slice(0).forEach(e=>{e.dispose()}),this.meshes.length=0,this.skeletons.slice(0).forEach(e=>{e.dispose()}),this.skeletons.length=0,this.animationGroups.slice(0).forEach(e=>{e.dispose()}),this.animationGroups.length=0,this.multiMaterials.slice(0).forEach(e=>{e.dispose()}),this.multiMaterials.length=0,this.materials.slice(0).forEach(e=>{e.dispose()}),this.materials.length=0,this.geometries.slice(0).forEach(e=>{e.dispose()}),this.geometries.length=0,this.transformNodes.slice(0).forEach(e=>{e.dispose()}),this.transformNodes.length=0,this.actionManagers.slice(0).forEach(e=>{e.dispose()}),this.actionManagers.length=0,this.textures.slice(0).forEach(e=>{e.dispose()}),this.textures.length=0,this.reflectionProbes.slice(0).forEach(e=>{e.dispose()}),this.reflectionProbes.length=0,this.morphTargetManagers.slice(0).forEach(e=>{e.dispose()}),this.morphTargetManagers.length=0,this.environmentTexture&&(this.environmentTexture.dispose(),this.environmentTexture=null);for(const e of this.scene._serializableComponents)e.removeFromContainer(this,!0);this._onContextRestoredObserver&&(this.scene.getEngine().onContextRestoredObservable.remove(this._onContextRestoredObserver),this._onContextRestoredObserver=null)}_moveAssets(e,t,s){if(!(!e||!t))for(const n of e){let o=!0;if(s){for(const l of s)if(n===l){o=!1;break}}o&&(t.push(n),n._parentContainer=this)}}moveAllFromScene(e){this._wasAddedToScene=!1,e===void 0&&(e=new k);for(const t in this)Object.prototype.hasOwnProperty.call(this,t)&&(this[t]=this[t]||(t==="_environmentTexture"?null:[]),this._moveAssets(this.scene[t],this[t],e[t]));this.environmentTexture=this.scene.environmentTexture,this.removeAllFromScene()}createRootMesh(){const e=new y("assetContainerRootMesh",this.scene);return this.meshes.forEach(t=>{t.parent||e.addChild(t)}),this.meshes.unshift(e),e}mergeAnimationsTo(e=E.LastCreatedScene,t,s=null){if(!e)return I.Error("No scene available to merge animations to"),[];const n=s||(r=>{let a=null;const c=r.animations.length?r.animations[0].targetProperty:"",p=r.name.split(".").join("").split("_primitive")[0];switch(c){case"position":case"rotationQuaternion":a=e.getTransformNodeByName(r.name)||e.getTransformNodeByName(p);break;case"influence":a=e.getMorphTargetByName(r.name)||e.getMorphTargetByName(p);break;default:a=e.getNodeByName(r.name)||e.getNodeByName(p)}return a});this.getNodes().forEach(r=>{const a=n(r);if(a!==null){for(const c of r.animations){const p=a.animations.filter(g=>g.targetProperty===c.targetProperty);for(const g of p){const M=a.animations.indexOf(g,0);M>-1&&a.animations.splice(M,1)}}a.animations=a.animations.concat(r.animations)}});const l=[];return this.animationGroups.slice().forEach(r=>{l.push(r.clone(r.name,n)),r.animatables.forEach(a=>{a.stop()})}),t.forEach(r=>{const a=n(r.target);a&&(e.beginAnimation(a,r.fromFrame,r.toFrame,r.loopAnimation,r.speedRatio,r.onAnimationEnd?r.onAnimationEnd:void 0,void 0,!0,void 0,r.onAnimationLoop?r.onAnimationLoop:void 0),e.stopAnimation(r.target))}),l}populateRootNodes(){this.rootNodes.length=0,this.meshes.forEach(e=>{!e.parent&&this.rootNodes.indexOf(e)===-1&&this.rootNodes.push(e)}),this.transformNodes.forEach(e=>{!e.parent&&this.rootNodes.indexOf(e)===-1&&this.rootNodes.push(e)}),this.lights.forEach(e=>{!e.parent&&this.rootNodes.indexOf(e)===-1&&this.rootNodes.push(e)}),this.cameras.forEach(e=>{!e.parent&&this.rootNodes.indexOf(e)===-1&&this.rootNodes.push(e)})}addAllAssetsToContainer(e){if(!e)return;const t=[],s=new Set;for(t.push(e);t.length>0;){const n=t.pop();if(n instanceof y?(n.geometry&&this.geometries.indexOf(n.geometry)===-1&&this.geometries.push(n.geometry),this.meshes.push(n)):n instanceof T?this.transformNodes.push(n):n instanceof D?this.lights.push(n):n instanceof C&&this.cameras.push(n),n instanceof x){if(n.material&&this.materials.indexOf(n.material)===-1){this.materials.push(n.material);for(const o of n.material.getActiveTextures())this.textures.indexOf(o)===-1&&this.textures.push(o)}n.skeleton&&this.skeletons.indexOf(n.skeleton)===-1&&this.skeletons.push(n.skeleton),n.morphTargetManager&&this.morphTargetManagers.indexOf(n.morphTargetManager)===-1&&this.morphTargetManagers.push(n.morphTargetManager)}for(const o of n.getChildren())s.has(o)||t.push(o);s.add(n)}this.populateRootNodes()}_getByTags(e,t,s){if(t===void 0)return e;const n=[];for(const o in e){const l=e[o];L&&L.MatchesQuery(l,t)&&(!s||s(l))&&n.push(l)}return n}getMeshesByTags(e,t){return this._getByTags(this.meshes,e,t)}getCamerasByTags(e,t){return this._getByTags(this.cameras,e,t)}getLightsByTags(e,t){return this._getByTags(this.lights,e,t)}getMaterialsByTags(e,t){return this._getByTags(this.materials,e,t).concat(this._getByTags(this.multiMaterials,e,t))}getTransformNodesByTags(e,t){return this._getByTags(this.transformNodes,e,t)}}export{F as A};
//# sourceMappingURL=assetContainer-BRzQBugc.js.map
