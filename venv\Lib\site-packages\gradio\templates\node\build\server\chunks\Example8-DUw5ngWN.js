import { c as create_ssr_component, e as escape } from './ssr-C3HYbsxA.js';

const v={code:"div.svelte-h6ogpl{width:var(--size-10);height:var(--size-10)}.table.svelte-h6ogpl{margin:0 auto}",map:`{"version":3,"file":"Example.svelte","sources":["Example.svelte"],"sourcesContent":["<script lang=\\"ts\\">export let value;\\nexport let type;\\nexport let selected = false;\\n<\/script>\\n\\n<div\\n\\tstyle=\\"background-color: {value ? value : 'black'}\\"\\n\\tclass:table={type === \\"table\\"}\\n\\tclass:gallery={type === \\"gallery\\"}\\n\\tclass:selected\\n/>\\n\\n<style>\\n\\tdiv {\\n\\t\\twidth: var(--size-10);\\n\\t\\theight: var(--size-10);\\n\\t}\\n\\t.table {\\n\\t\\tmargin: 0 auto;\\n\\t}</style>\\n"],"names":[],"mappings":"AAaC,iBAAI,CACH,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,MAAM,CAAE,IAAI,SAAS,CACtB,CACA,oBAAO,CACN,MAAM,CAAE,CAAC,CAAC,IACX"}`},i=create_ssr_component((A,e,t,r)=>{let{value:l}=e,{type:a}=e,{selected:s=!1}=e;return e.value===void 0&&t.value&&l!==void 0&&t.value(l),e.type===void 0&&t.type&&a!==void 0&&t.type(a),e.selected===void 0&&t.selected&&s!==void 0&&t.selected(s),A.css.add(v),`<div style="${"background-color: "+escape(l||"black",!0)}" class="${["svelte-h6ogpl",(a==="table"?"table":"")+" "+(a==="gallery"?"gallery":"")+" "+(s?"selected":"")].join(" ").trim()}"></div>`});

export { i as default };
//# sourceMappingURL=Example8-DUw5ngWN.js.map
