import{SvelteComponent as W,init as j,safe_not_equal as G,create_component as b,space as $,empty as B,claim_component as p,claim_space as A,mount_component as w,insert_hydration as y,group_outros as D,transition_out as m,check_outros as S,transition_in as s,detach as I,destroy_component as d,createEventDispatcher as J,bubble as k}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{u as K,I as O}from"./2.B2AoQPnG.js";import{B as Q}from"./BlockLabel.BTSz9r5s.js";import{E as R}from"./Empty.DwQ6nkN6.js";import{S as U}from"./ShareButton.Be7APJkJ.js";import{D as V}from"./Download.CpfEFmFf.js";import{M as L}from"./Music.BVFRDHso.js";import{I as X}from"./IconButtonWrapper.D5aGR59h.js";import{A as Y}from"./AudioPlayer.CnAk5fND.js";import{D as Z}from"./DownloadLink.D1g3Q1HV.js";function x(l){let n,r;return n=new R({props:{size:"small",$$slots:{default:[ne]},$$scope:{ctx:l}}}),{c(){b(n.$$.fragment)},l(e){p(n.$$.fragment,e)},m(e,t){w(n,e,t),r=!0},p(e,t){const o={};t&524288&&(o.$$scope={dirty:t,ctx:e}),n.$set(o)},i(e){r||(s(n.$$.fragment,e),r=!0)},o(e){m(n.$$.fragment,e),r=!1},d(e){d(n,e)}}}function ee(l){let n,r,e,t;return n=new X({props:{display_top_corner:l[10],$$slots:{default:[te]},$$scope:{ctx:l}}}),e=new Y({props:{value:l[0],label:l[1],i18n:l[5],waveform_settings:l[6],waveform_options:l[7],editable:l[8],loop:l[9]}}),e.$on("pause",l[14]),e.$on("play",l[15]),e.$on("stop",l[16]),e.$on("load",l[17]),{c(){b(n.$$.fragment),r=$(),b(e.$$.fragment)},l(o){p(n.$$.fragment,o),r=A(o),p(e.$$.fragment,o)},m(o,a){w(n,o,a),y(o,r,a),w(e,o,a),t=!0},p(o,a){const u={};a&1024&&(u.display_top_corner=o[10]),a&524345&&(u.$$scope={dirty:a,ctx:o}),n.$set(u);const _={};a&1&&(_.value=o[0]),a&2&&(_.label=o[1]),a&32&&(_.i18n=o[5]),a&64&&(_.waveform_settings=o[6]),a&128&&(_.waveform_options=o[7]),a&256&&(_.editable=o[8]),a&512&&(_.loop=o[9]),e.$set(_)},i(o){t||(s(n.$$.fragment,o),s(e.$$.fragment,o),t=!0)},o(o){m(n.$$.fragment,o),m(e.$$.fragment,o),t=!1},d(o){o&&I(r),d(n,o),d(e,o)}}}function ne(l){let n,r;return n=new L({}),{c(){b(n.$$.fragment)},l(e){p(n.$$.fragment,e)},m(e,t){w(n,e,t),r=!0},i(e){r||(s(n.$$.fragment,e),r=!0)},o(e){m(n.$$.fragment,e),r=!1},d(e){d(n,e)}}}function E(l){var e;let n,r;return n=new Z({props:{href:l[0].is_stream?(e=l[0].url)==null?void 0:e.replace("playlist.m3u8","playlist-file"):l[0].url,download:l[0].orig_name||l[0].path,$$slots:{default:[oe]},$$scope:{ctx:l}}}),{c(){b(n.$$.fragment)},l(t){p(n.$$.fragment,t)},m(t,o){w(n,t,o),r=!0},p(t,o){var u;const a={};o&1&&(a.href=t[0].is_stream?(u=t[0].url)==null?void 0:u.replace("playlist.m3u8","playlist-file"):t[0].url),o&1&&(a.download=t[0].orig_name||t[0].path),o&524320&&(a.$$scope={dirty:o,ctx:t}),n.$set(a)},i(t){r||(s(n.$$.fragment,t),r=!0)},o(t){m(n.$$.fragment,t),r=!1},d(t){d(n,t)}}}function oe(l){let n,r;return n=new O({props:{Icon:V,label:l[5]("common.download")}}),{c(){b(n.$$.fragment)},l(e){p(n.$$.fragment,e)},m(e,t){w(n,e,t),r=!0},p(e,t){const o={};t&32&&(o.label=e[5]("common.download")),n.$set(o)},i(e){r||(s(n.$$.fragment,e),r=!0)},o(e){m(n.$$.fragment,e),r=!1},d(e){d(n,e)}}}function N(l){let n,r;return n=new U({props:{i18n:l[5],formatter:l[11],value:l[0]}}),n.$on("error",l[12]),n.$on("share",l[13]),{c(){b(n.$$.fragment)},l(e){p(n.$$.fragment,e)},m(e,t){w(n,e,t),r=!0},p(e,t){const o={};t&32&&(o.i18n=e[5]),t&1&&(o.value=e[0]),n.$set(o)},i(e){r||(s(n.$$.fragment,e),r=!0)},o(e){m(n.$$.fragment,e),r=!1},d(e){d(n,e)}}}function te(l){let n,r,e,t=l[3]&&E(l),o=l[4]&&N(l);return{c(){t&&t.c(),n=$(),o&&o.c(),r=B()},l(a){t&&t.l(a),n=A(a),o&&o.l(a),r=B()},m(a,u){t&&t.m(a,u),y(a,n,u),o&&o.m(a,u),y(a,r,u),e=!0},p(a,u){a[3]?t?(t.p(a,u),u&8&&s(t,1)):(t=E(a),t.c(),s(t,1),t.m(n.parentNode,n)):t&&(D(),m(t,1,1,()=>{t=null}),S()),a[4]?o?(o.p(a,u),u&16&&s(o,1)):(o=N(a),o.c(),s(o,1),o.m(r.parentNode,r)):o&&(D(),m(o,1,1,()=>{o=null}),S())},i(a){e||(s(t),s(o),e=!0)},o(a){m(t),m(o),e=!1},d(a){a&&(I(n),I(r)),t&&t.d(a),o&&o.d(a)}}}function le(l){let n,r,e,t,o,a;n=new Q({props:{show_label:l[2],Icon:L,float:!1,label:l[1]||l[5]("audio.audio")}});const u=[ee,x],_=[];function v(f,c){return f[0]!==null?0:1}return e=v(l),t=_[e]=u[e](l),{c(){b(n.$$.fragment),r=$(),t.c(),o=B()},l(f){p(n.$$.fragment,f),r=A(f),t.l(f),o=B()},m(f,c){w(n,f,c),y(f,r,c),_[e].m(f,c),y(f,o,c),a=!0},p(f,[c]){const h={};c&4&&(h.show_label=f[2]),c&34&&(h.label=f[1]||f[5]("audio.audio")),n.$set(h);let g=e;e=v(f),e===g?_[e].p(f,c):(D(),m(_[g],1,1,()=>{_[g]=null}),S(),t=_[e],t?t.p(f,c):(t=_[e]=u[e](f),t.c()),s(t,1),t.m(o.parentNode,o))},i(f){a||(s(n.$$.fragment,f),s(t),a=!0)},o(f){m(n.$$.fragment,f),m(t),a=!1},d(f){f&&(I(r),I(o)),d(n,f),_[e].d(f)}}}function re(l,n,r){let{value:e=null}=n,{label:t}=n,{show_label:o=!0}=n,{show_download_button:a=!0}=n,{show_share_button:u=!1}=n,{i18n:_}=n,{waveform_settings:v={}}=n,{waveform_options:f={show_recording_waveform:!0}}=n,{editable:c=!0}=n,{loop:h}=n,{display_icon_button_wrapper_top_corner:g=!1}=n;const M=J(),q=async i=>i?`<audio controls src="${await K(i.url)}"></audio>`:"";function z(i){k.call(this,l,i)}function C(i){k.call(this,l,i)}function F(i){k.call(this,l,i)}function H(i){k.call(this,l,i)}function P(i){k.call(this,l,i)}function T(i){k.call(this,l,i)}return l.$$set=i=>{"value"in i&&r(0,e=i.value),"label"in i&&r(1,t=i.label),"show_label"in i&&r(2,o=i.show_label),"show_download_button"in i&&r(3,a=i.show_download_button),"show_share_button"in i&&r(4,u=i.show_share_button),"i18n"in i&&r(5,_=i.i18n),"waveform_settings"in i&&r(6,v=i.waveform_settings),"waveform_options"in i&&r(7,f=i.waveform_options),"editable"in i&&r(8,c=i.editable),"loop"in i&&r(9,h=i.loop),"display_icon_button_wrapper_top_corner"in i&&r(10,g=i.display_icon_button_wrapper_top_corner)},l.$$.update=()=>{l.$$.dirty&1&&e&&M("change",e)},[e,t,o,a,u,_,v,f,c,h,g,q,z,C,F,H,P,T]}class he extends W{constructor(n){super(),j(this,n,re,le,G,{value:0,label:1,show_label:2,show_download_button:3,show_share_button:4,i18n:5,waveform_settings:6,waveform_options:7,editable:8,loop:9,display_icon_button_wrapper_top_corner:10})}}export{he as default};
//# sourceMappingURL=StaticAudio.CEB3a_-s.js.map
