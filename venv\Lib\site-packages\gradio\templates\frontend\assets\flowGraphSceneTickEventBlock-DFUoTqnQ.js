import{c as i}from"./KHR_interactivity-DTxiAnOo.js";import{R as s}from"./index-Dpxo-yl_.js";import{b as r}from"./declarationMapper-BZjsjg7g.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class a extends i{constructor(){super(),this.type="SceneBeforeRender",this.timeSinceStart=this.registerDataOutput("timeSinceStart",r),this.deltaTime=this.registerDataOutput("deltaTime",r)}_preparePendingTasks(e){}_executeEvent(e,t){return this.timeSinceStart.setValue(t.timeSinceStart,e),this.deltaTime.setValue(t.deltaTime,e),this._execute(e),!0}_cancelPendingTasks(e){}getClassName(){return"FlowGraphSceneTickEventBlock"}}s("FlowGraphSceneTickEventBlock",a);export{a as FlowGraphSceneTickEventBlock};
//# sourceMappingURL=flowGraphSceneTickEventBlock-DFUoTqnQ.js.map
