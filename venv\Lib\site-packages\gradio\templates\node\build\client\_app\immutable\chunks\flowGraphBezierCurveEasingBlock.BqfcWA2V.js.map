{"version": 3, "file": "flowGraphBezierCurveEasingBlock.BqfcWA2V.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/Animation/flowGraphBezierCurveEasingBlock.js"], "sourcesContent": ["import { BezierCurveEase } from \"../../../../Animations/easing.js\";\nimport { FlowGraphBlock } from \"../../../flowGraphBlock.js\";\nimport { RichTypeAny, RichTypeNumber, RichTypeVector2 } from \"../../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\n/**\n * An easing block that generates a BezierCurveEase easingFunction object based on the data provided.\n */\nexport class FlowGraphBezierCurveEasingBlock extends FlowGraphBlock {\n    constructor(\n    /**\n     * the configuration of the block\n     */\n    config) {\n        super(config);\n        this.config = config;\n        /**\n         * Internal cache of reusable easing functions.\n         * key is type-mode-properties\n         */\n        this._easingFunctions = {};\n        this.mode = this.registerDataInput(\"mode\", RichTypeNumber, 0);\n        this.controlPoint1 = this.registerDataInput(\"controlPoint1\", RichTypeVector2);\n        this.controlPoint2 = this.registerDataInput(\"controlPoint2\", RichTypeVector2);\n        this.easingFunction = this.registerDataOutput(\"easingFunction\", RichTypeAny);\n    }\n    _updateOutputs(context) {\n        const mode = this.mode.getValue(context);\n        const controlPoint1 = this.controlPoint1.getValue(context);\n        const controlPoint2 = this.controlPoint2.getValue(context);\n        if (mode === undefined) {\n            return;\n        }\n        const key = `${mode}-${controlPoint1.x}-${controlPoint1.y}-${controlPoint2.x}-${controlPoint2.y}`;\n        if (!this._easingFunctions[key]) {\n            const easing = new BezierCurveEase(controlPoint1.x, controlPoint1.y, controlPoint2.x, controlPoint2.y);\n            easing.setEasingMode(mode);\n            this._easingFunctions[key] = easing;\n        }\n        this.easingFunction.setValue(this._easingFunctions[key], context);\n    }\n    getClassName() {\n        return \"FlowGraphBezierCurveEasing\" /* FlowGraphBlockNames.BezierCurveEasing */;\n    }\n}\nRegisterClass(\"FlowGraphBezierCurveEasing\" /* FlowGraphBlockNames.BezierCurveEasing */, FlowGraphBezierCurveEasingBlock);\n//# sourceMappingURL=flowGraphBezierCurveEasingBlock.js.map"], "names": ["FlowGraphBezierCurveEasingBlock", "FlowGraphBlock", "config", "RichTypeNumber", "RichTypeVector2", "RichTypeAny", "context", "mode", "controlPoint1", "controlPoint2", "key", "easing", "BezierCurveEase", "RegisterClass"], "mappings": "sKAOO,MAAMA,UAAwCC,CAAe,CAChE,YAIAC,EAAQ,CACJ,MAAMA,CAAM,EACZ,KAAK,OAASA,EAKd,KAAK,iBAAmB,GACxB,KAAK,KAAO,KAAK,kBAAkB,OAAQC,EAAgB,CAAC,EAC5D,KAAK,cAAgB,KAAK,kBAAkB,gBAAiBC,CAAe,EAC5E,KAAK,cAAgB,KAAK,kBAAkB,gBAAiBA,CAAe,EAC5E,KAAK,eAAiB,KAAK,mBAAmB,iBAAkBC,CAAW,CAC9E,CACD,eAAeC,EAAS,CACpB,MAAMC,EAAO,KAAK,KAAK,SAASD,CAAO,EACjCE,EAAgB,KAAK,cAAc,SAASF,CAAO,EACnDG,EAAgB,KAAK,cAAc,SAASH,CAAO,EACzD,GAAIC,IAAS,OACT,OAEJ,MAAMG,EAAM,GAAGH,CAAI,IAAIC,EAAc,CAAC,IAAIA,EAAc,CAAC,IAAIC,EAAc,CAAC,IAAIA,EAAc,CAAC,GAC/F,GAAI,CAAC,KAAK,iBAAiBC,CAAG,EAAG,CAC7B,MAAMC,EAAS,IAAIC,EAAgBJ,EAAc,EAAGA,EAAc,EAAGC,EAAc,EAAGA,EAAc,CAAC,EACrGE,EAAO,cAAcJ,CAAI,EACzB,KAAK,iBAAiBG,CAAG,EAAIC,CAChC,CACD,KAAK,eAAe,SAAS,KAAK,iBAAiBD,CAAG,EAAGJ,CAAO,CACnE,CACD,cAAe,CACX,MAAO,4BACV,CACL,CACAO,EAAc,6BAA0Eb,CAA+B", "x_google_ignoreList": [0]}