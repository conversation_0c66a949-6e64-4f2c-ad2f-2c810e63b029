/* empty css                                                        */const{SvelteComponent:d,append:u,attr:b,create_component:g,destroy_component:m,detach:w,element:c,flush:_,init:I,insert:q,mount_component:k,safe_not_equal:B,set_data:C,space:L,text:S,toggle_class:o,transition_in:j,transition_out:v}=window.__gradio__svelte__internal;function z(s){let e,a,i,h,f,r,n;return i=new s[1]({}),{c(){e=c("label"),a=c("span"),g(i.$$.fragment),h=L(),f=S(s[0]),b(a,"class","svelte-1to105q"),b(e,"for",""),b(e,"data-testid","block-label"),b(e,"dir",r=s[5]?"rtl":"ltr"),b(e,"class","svelte-1to105q"),o(e,"hide",!s[2]),o(e,"sr-only",!s[2]),o(e,"float",s[4]),o(e,"hide-label",s[3])},m(l,t){q(l,e,t),u(e,a),k(i,a,null),u(e,h),u(e,f),n=!0},p(l,[t]){(!n||t&1)&&C(f,l[0]),(!n||t&32&&r!==(r=l[5]?"rtl":"ltr"))&&b(e,"dir",r),(!n||t&4)&&o(e,"hide",!l[2]),(!n||t&4)&&o(e,"sr-only",!l[2]),(!n||t&16)&&o(e,"float",l[4]),(!n||t&8)&&o(e,"hide-label",l[3])},i(l){n||(j(i.$$.fragment,l),n=!0)},o(l){v(i.$$.fragment,l),n=!1},d(l){l&&w(e),m(i)}}}function A(s,e,a){let{label:i=null}=e,{Icon:h}=e,{show_label:f=!0}=e,{disable:r=!1}=e,{float:n=!0}=e,{rtl:l=!1}=e;return s.$$set=t=>{"label"in t&&a(0,i=t.label),"Icon"in t&&a(1,h=t.Icon),"show_label"in t&&a(2,f=t.show_label),"disable"in t&&a(3,r=t.disable),"float"in t&&a(4,n=t.float),"rtl"in t&&a(5,l=t.rtl)},[i,h,f,r,n,l]}class E extends d{constructor(e){super(),I(this,e,A,z,B,{label:0,Icon:1,show_label:2,disable:3,float:4,rtl:5})}get label(){return this.$$.ctx[0]}set label(e){this.$$set({label:e}),_()}get Icon(){return this.$$.ctx[1]}set Icon(e){this.$$set({Icon:e}),_()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),_()}get disable(){return this.$$.ctx[3]}set disable(e){this.$$set({disable:e}),_()}get float(){return this.$$.ctx[4]}set float(e){this.$$set({float:e}),_()}get rtl(){return this.$$.ctx[5]}set rtl(e){this.$$set({rtl:e}),_()}}export{E as B};
//# sourceMappingURL=BlockLabel-3KxTaaiM.js.map
