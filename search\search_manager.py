"""
智能搜索管理器
自动判断是否需要搜索，整合搜索结果
"""

import re
import json
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime
import threading

from search.engines.duckduckgo_search import duckduckgo_search, SearchResult
from core.utils.logger import get_logger
from core.utils.config import config_manager

logger = get_logger(__name__)

class SearchManager:
    """智能搜索管理器"""
    
    def __init__(self):
        self.auto_search = config_manager.get('search.auto_search', True)
        self.max_results = config_manager.get('search.max_results', 5)
        self.search_engine = duckduckgo_search
        
        # 搜索触发关键词
        self.search_triggers = [
            # 时间相关
            r'今天|昨天|明天|最近|现在|当前|最新',
            r'\d{4}年|\d{1,2}月|\d{1,2}日',
            r'今年|去年|明年|这个月|上个月|下个月',
            
            # 事实查询
            r'什么是|谁是|哪里|为什么|怎么|如何',
            r'查询|搜索|找到|了解|知道',
            
            # 新闻事件
            r'新闻|消息|事件|发生|报道',
            r'股价|汇率|天气|疫情',
            
            # 技术相关
            r'最新版本|更新|发布|下载',
            r'教程|文档|API|使用方法',
            
            # 比较和选择
            r'比较|对比|哪个更好|推荐',
            r'价格|评价|评测|排行榜'
        ]
        
        # 编译正则表达式
        self.search_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.search_triggers]
    
    def should_search(self, query: str) -> bool:
        """判断是否需要搜索"""
        if not self.auto_search:
            return False
        
        # 检查是否包含搜索触发词
        for pattern in self.search_patterns:
            if pattern.search(query):
                return True
        
        # 检查是否包含问号
        if '?' in query or '？' in query:
            return True
        
        # 检查是否是明确的搜索请求
        search_keywords = ['搜索', '查找', '查询', '了解', '知道']
        for keyword in search_keywords:
            if keyword in query:
                return True
        
        return False
    
    def extract_search_query(self, user_input: str) -> str:
        """从用户输入中提取搜索查询"""
        # 移除常见的对话词汇
        remove_words = ['请', '帮我', '你能', '可以', '告诉我', '我想', '想要']
        query = user_input
        
        for word in remove_words:
            query = query.replace(word, '')
        
        # 清理多余空格
        query = ' '.join(query.split())
        
        return query.strip()
    
    def search_with_context(self, query: str, context: str = "", 
                           progress_callback: Optional[Callable[[str], None]] = None) -> Dict[str, Any]:
        """带上下文的智能搜索"""
        if progress_callback:
            progress_callback("🔍 正在搜索相关信息...")
        
        # 提取搜索查询
        search_query = self.extract_search_query(query)
        
        logger.info(f"执行搜索: {search_query}")
        
        try:
            # 执行搜索
            results = self.search_engine.search_and_extract(
                search_query, 
                max_results=self.max_results,
                extract_content=True
            )
            
            if progress_callback:
                progress_callback(f"✅ 找到 {len(results)} 个相关结果")
            
            # 整理搜索结果
            search_summary = self._create_search_summary(results, search_query)
            
            return {
                'success': True,
                'query': search_query,
                'results': results,
                'summary': search_summary,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            if progress_callback:
                progress_callback(f"❌ 搜索失败: {str(e)}")
            
            return {
                'success': False,
                'query': search_query,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _create_search_summary(self, results: List[Dict[str, Any]], query: str) -> str:
        """创建搜索结果摘要"""
        if not results:
            return f"未找到关于 '{query}' 的相关信息。"
        
        summary_parts = [f"关于 '{query}' 的搜索结果：\n"]
        
        for i, result in enumerate(results, 1):
            title = result.get('title', '无标题')
            snippet = result.get('snippet', '')
            url = result.get('url', '')
            
            # 限制摘要长度
            if len(snippet) > 200:
                snippet = snippet[:200] + "..."
            
            summary_parts.append(f"{i}. **{title}**")
            if snippet:
                summary_parts.append(f"   {snippet}")
            if url:
                summary_parts.append(f"   来源: {url}")
            summary_parts.append("")
        
        return "\n".join(summary_parts)
    
    def format_search_results_for_llm(self, search_data: Dict[str, Any]) -> str:
        """格式化搜索结果供LLM使用"""
        if not search_data.get('success', False):
            return f"搜索失败: {search_data.get('error', '未知错误')}"
        
        results = search_data.get('results', [])
        if not results:
            return f"未找到关于 '{search_data.get('query', '')}' 的相关信息。"
        
        formatted_parts = [
            f"=== 搜索结果: {search_data.get('query', '')} ===",
            f"搜索时间: {search_data.get('timestamp', '')}",
            ""
        ]
        
        for i, result in enumerate(results, 1):
            title = result.get('title', '无标题')
            snippet = result.get('snippet', '')
            content = result.get('content', '')
            url = result.get('url', '')
            
            formatted_parts.append(f"结果 {i}: {title}")
            
            # 优先使用完整内容，否则使用摘要
            text_content = content if content else snippet
            if text_content:
                # 限制内容长度
                if len(text_content) > 1000:
                    text_content = text_content[:1000] + "..."
                formatted_parts.append(f"内容: {text_content}")
            
            if url:
                formatted_parts.append(f"来源: {url}")
            
            formatted_parts.append("-" * 50)
        
        return "\n".join(formatted_parts)
    
    def search_if_needed(self, user_input: str, context: str = "",
                        progress_callback: Optional[Callable[[str], None]] = None) -> Optional[Dict[str, Any]]:
        """如果需要则执行搜索"""
        if self.should_search(user_input):
            return self.search_with_context(user_input, context, progress_callback)
        return None
    
    def get_search_suggestions(self, query: str) -> List[str]:
        """获取搜索建议"""
        suggestions = []
        
        # 基于查询内容生成建议
        if any(word in query.lower() for word in ['天气', 'weather']):
            suggestions.extend([
                f"{query} 今天",
                f"{query} 明天",
                f"{query} 一周预报"
            ])
        
        elif any(word in query.lower() for word in ['股票', '股价', 'stock']):
            suggestions.extend([
                f"{query} 实时价格",
                f"{query} 走势分析",
                f"{query} 财报"
            ])
        
        elif any(word in query.lower() for word in ['新闻', 'news']):
            suggestions.extend([
                f"{query} 最新",
                f"{query} 今日",
                f"{query} 热点"
            ])
        
        else:
            # 通用建议
            suggestions.extend([
                f"{query} 是什么",
                f"{query} 怎么用",
                f"{query} 最新消息"
            ])
        
        return suggestions[:5]  # 返回最多5个建议

# 全局搜索管理器实例
search_manager = SearchManager()
