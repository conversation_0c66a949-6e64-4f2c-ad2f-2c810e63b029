{"name": "@gradio/code", "version": "0.14.12", "description": "Gradio UI packages", "type": "module", "author": "", "license": "ISC", "private": false, "dependencies": {"@codemirror/autocomplete": "^6.18.6", "@codemirror/commands": "^6.8.0", "@codemirror/lang-css": "^6.3.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-javascript": "^6.2.3", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.3.2", "@codemirror/lang-python": "^6.1.7", "@codemirror/language": "^6.10.8", "@codemirror/legacy-modes": "^6.4.3", "@codemirror/lint": "^6.8.4", "@codemirror/search": "^6.5.10", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.36.3", "@gradio/atoms": "workspace:^", "@gradio/icons": "workspace:^", "@gradio/statustracker": "workspace:^", "@gradio/upload": "workspace:^", "@gradio/utils": "workspace:^", "@gradio/wasm": "workspace:^", "@lezer/common": "^1.2.3", "@lezer/highlight": "^1.2.1", "@lezer/markdown": "^1.4.2", "cm6-theme-basic-dark": "^0.2.0", "cm6-theme-basic-light": "^0.2.0", "codemirror": "^6.0.1"}, "main_changeset": true, "main": "./Index.svelte", "exports": {".": {"gradio": "./Index.svelte", "svelte": "./dist/Index.svelte", "types": "./dist/Index.svelte.d.ts"}, "./example": {"gradio": "./Example.svelte", "svelte": "./dist/Example.svelte", "types": "./dist/Example.svelte.d.ts"}, "./package.json": "./package.json"}, "devDependencies": {"@gradio/preview": "workspace:^"}, "peerDependencies": {"svelte": "^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/gradio-app/gradio.git", "directory": "js/code"}}