# This file is autogenerated by the command `make fix-copies`, do not edit.
from ..utils import DummyObject, requires_backends


class GGUFQuantizationConfig(metaclass=DummyObject):
    _backends = ["gguf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["gguf"])

    @classmethod
    def from_config(cls, *args, **kwargs):
        requires_backends(cls, ["gguf"])

    @classmethod
    def from_pretrained(cls, *args, **kwargs):
        requires_backends(cls, ["gguf"])
