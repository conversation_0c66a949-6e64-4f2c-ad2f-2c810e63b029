import{C as M,T as D,aS as x,ay as F,V as y,aa as C,h as V,aT as P,ab as A,aU as L,aV as T,b as I,af as v}from"./index.BoI39RQH.js";import{A as U}from"./assetContainer.w1CRG79i.js";import{S as B}from"./standardMaterial.Cnq1RvLI.js";class b{constructor(){this.materials=[]}parseMTL(s,n,e,a){if(n instanceof ArrayBuffer)return;const l=n.split(`
`),h=/\s+/;let p,r=null;for(let d=0;d<l.length;d++){const m=l[d].trim();if(m.length===0||m.charAt(0)==="#")continue;const o=m.indexOf(" ");let t=o>=0?m.substring(0,o):m;t=t.toLowerCase();const i=o>=0?m.substring(o+1).trim():"";if(t==="newmtl")r&&this.materials.push(r),s._blockEntityCollection=!!a,r=new B(i,s),r._parentContainer=a,s._blockEntityCollection=!1;else if(t==="kd"&&r)p=i.split(h,3).map(parseFloat),r.diffuseColor=M.FromArray(p);else if(t==="ka"&&r)p=i.split(h,3).map(parseFloat),r.ambientColor=M.FromArray(p);else if(t==="ks"&&r)p=i.split(h,3).map(parseFloat),r.specularColor=M.FromArray(p);else if(t==="ke"&&r)p=i.split(h,3).map(parseFloat),r.emissiveColor=M.FromArray(p);else if(t==="ns"&&r)r.specularPower=parseFloat(i);else if(t==="d"&&r)r.alpha=parseFloat(i);else if(t==="map_ka"&&r)r.ambientTexture=b._GetTexture(e,i,s);else if(t==="map_kd"&&r)r.diffuseTexture=b._GetTexture(e,i,s);else if(t==="map_ks"&&r)r.specularTexture=b._GetTexture(e,i,s);else if(t!=="map_ns")if(t==="map_bump"&&r){const _=i.split(h),f=_.indexOf("-bm");let g=null;f>=0&&(g=_[f+1],_.splice(f,2)),r.bumpTexture=b._GetTexture(e,_.join(" "),s),r.bumpTexture&&g!==null&&(r.bumpTexture.level=parseFloat(g))}else t==="map_d"&&r&&(r.opacityTexture=b._GetTexture(e,i,s))}r&&this.materials.push(r)}static _GetTexture(s,n,e){if(!n)return null;let a=s;if(s==="file:"){let l=n.lastIndexOf("\\");l===-1&&(l=n.lastIndexOf("/")),l>-1?a+=n.substring(l+1):a+=n}else a+=n;return new D(a,e,!1,b.INVERT_TEXTURE_Y)}}b.INVERT_TEXTURE_Y=!0;class u{constructor(s,n,e){this._positions=[],this._normals=[],this._uvs=[],this._colors=[],this._extColors=[],this._meshesFromObj=[],this._indicesForBabylon=[],this._wrappedPositionForBabylon=[],this._wrappedUvsForBabylon=[],this._wrappedColorsForBabylon=[],this._wrappedNormalsForBabylon=[],this._tuplePosNorm=[],this._curPositionInIndices=0,this._hasMeshes=!1,this._unwrappedPositionsForBabylon=[],this._unwrappedColorsForBabylon=[],this._unwrappedNormalsForBabylon=[],this._unwrappedUVForBabylon=[],this._triangles=[],this._materialNameFromObj="",this._objMeshName="",this._increment=1,this._isFirstMaterial=!0,this._grayColor=new x(.5,.5,.5,1),this._hasLineData=!1,this._materialToUse=s,this._babylonMeshesArray=n,this._loadingOptions=e}_isInArray(s,n){s[n[0]]||(s[n[0]]={normals:[],idx:[]});const e=s[n[0]].normals.indexOf(n[1]);return e===-1?-1:s[n[0]].idx[e]}_isInArrayUV(s,n){s[n[0]]||(s[n[0]]={normals:[],idx:[],uv:[]});const e=s[n[0]].normals.indexOf(n[1]);return e!=1&&n[2]===s[n[0]].uv[e]?s[n[0]].idx[e]:-1}_setData(s,n,e,a,l,h,p){let r;this._loadingOptions.optimizeWithUV?r=this._isInArrayUV(this._tuplePosNorm,[s,e,n]):r=this._isInArray(this._tuplePosNorm,[s,e]),r===-1?(this._indicesForBabylon.push(this._wrappedPositionForBabylon.length),this._wrappedPositionForBabylon.push(a),l=l??new F(0,0),this._wrappedUvsForBabylon.push(l),this._wrappedNormalsForBabylon.push(h),p!==void 0&&this._wrappedColorsForBabylon.push(p),this._tuplePosNorm[s].normals.push(e),this._tuplePosNorm[s].idx.push(this._curPositionInIndices++),this._loadingOptions.optimizeWithUV&&this._tuplePosNorm[s].uv.push(n)):this._indicesForBabylon.push(r)}_unwrapData(){try{for(let s=0;s<this._wrappedPositionForBabylon.length;s++)this._unwrappedPositionsForBabylon.push(this._wrappedPositionForBabylon[s].x*this._handednessSign,this._wrappedPositionForBabylon[s].y,this._wrappedPositionForBabylon[s].z),this._unwrappedNormalsForBabylon.push(this._wrappedNormalsForBabylon[s].x*this._handednessSign,this._wrappedNormalsForBabylon[s].y,this._wrappedNormalsForBabylon[s].z),this._unwrappedUVForBabylon.push(this._wrappedUvsForBabylon[s].x,this._wrappedUvsForBabylon[s].y),this._loadingOptions.importVertexColors&&this._unwrappedColorsForBabylon.push(this._wrappedColorsForBabylon[s].r,this._wrappedColorsForBabylon[s].g,this._wrappedColorsForBabylon[s].b,this._wrappedColorsForBabylon[s].a);this._wrappedPositionForBabylon.length=0,this._wrappedNormalsForBabylon.length=0,this._wrappedUvsForBabylon.length=0,this._wrappedColorsForBabylon.length=0,this._tuplePosNorm.length=0,this._curPositionInIndices=0}catch{throw new Error("Unable to unwrap data while parsing OBJ data.")}}_getTriangles(s,n){for(let e=n;e<s.length-1;e++)this._pushTriangle(s,e)}_getColor(s){if(this._loadingOptions.importVertexColors)return this._extColors[s]??this._colors[s]}_setDataForCurrentFaceWithPattern1(s,n){this._getTriangles(s,n);for(let e=0;e<this._triangles.length;e++){const a=parseInt(this._triangles[e])-1;this._setData(a,0,0,this._positions[a],F.Zero(),y.Up(),this._getColor(a))}this._triangles.length=0}_setDataForCurrentFaceWithPattern2(s,n){this._getTriangles(s,n);for(let e=0;e<this._triangles.length;e++){const a=this._triangles[e].split("/"),l=parseInt(a[0])-1,h=parseInt(a[1])-1;this._setData(l,h,0,this._positions[l],this._uvs[h]??F.Zero(),y.Up(),this._getColor(l))}this._triangles.length=0}_setDataForCurrentFaceWithPattern3(s,n){this._getTriangles(s,n);for(let e=0;e<this._triangles.length;e++){const a=this._triangles[e].split("/"),l=parseInt(a[0])-1,h=parseInt(a[1])-1,p=parseInt(a[2])-1;this._setData(l,h,p,this._positions[l],this._uvs[h]??F.Zero(),this._normals[p]??y.Up())}this._triangles.length=0}_setDataForCurrentFaceWithPattern4(s,n){this._getTriangles(s,n);for(let e=0;e<this._triangles.length;e++){const a=this._triangles[e].split("//"),l=parseInt(a[0])-1,h=parseInt(a[1])-1;this._setData(l,1,h,this._positions[l],F.Zero(),this._normals[h],this._getColor(l))}this._triangles.length=0}_setDataForCurrentFaceWithPattern5(s,n){this._getTriangles(s,n);for(let e=0;e<this._triangles.length;e++){const a=this._triangles[e].split("/"),l=this._positions.length+parseInt(a[0]),h=this._uvs.length+parseInt(a[1]),p=this._normals.length+parseInt(a[2]);this._setData(l,h,p,this._positions[l],this._uvs[h],this._normals[p],this._getColor(l))}this._triangles.length=0}_addPreviousObjMesh(){this._meshesFromObj.length>0&&(this._handledMesh=this._meshesFromObj[this._meshesFromObj.length-1],this._unwrapData(),this._loadingOptions.useLegacyBehavior&&this._indicesForBabylon.reverse(),this._handledMesh.indices=this._indicesForBabylon.slice(),this._handledMesh.positions=this._unwrappedPositionsForBabylon.slice(),this._handledMesh.normals=this._unwrappedNormalsForBabylon.slice(),this._handledMesh.uvs=this._unwrappedUVForBabylon.slice(),this._handledMesh.hasLines=this._hasLineData,this._loadingOptions.importVertexColors&&(this._handledMesh.colors=this._unwrappedColorsForBabylon.slice()),this._indicesForBabylon.length=0,this._unwrappedPositionsForBabylon.length=0,this._unwrappedColorsForBabylon.length=0,this._unwrappedNormalsForBabylon.length=0,this._unwrappedUVForBabylon.length=0,this._hasLineData=!1)}_optimizeNormals(s){const n=s.getVerticesData(C.PositionKind),e=s.getVerticesData(C.NormalKind),a={};if(!n||!e)return;for(let h=0;h<n.length/3;h++){const p=n[h*3+0],r=n[h*3+1],d=n[h*3+2],m=p+"_"+r+"_"+d;let o=a[m];o||(o=[],a[m]=o),o.push(h)}const l=new y;for(const h in a){const p=a[h];if(p.length<2)continue;const r=p[0];for(let d=1;d<p.length;++d){const m=p[d];e[r*3+0]+=e[m*3+0],e[r*3+1]+=e[m*3+1],e[r*3+2]+=e[m*3+2]}l.copyFromFloats(e[r*3+0],e[r*3+1],e[r*3+2]),l.normalize();for(let d=0;d<p.length;++d){const m=p[d];e[m*3+0]=l.x,e[m*3+1]=l.y,e[m*3+2]=l.z}}s.setVerticesData(C.NormalKind,e)}static _IsLineElement(s){return s.startsWith("l")}static _IsObjectElement(s){return s.startsWith("o")}static _IsGroupElement(s){return s.startsWith("g")}static _GetZbrushMRGB(s,n){if(!s.startsWith("mrgb"))return null;if(s=s.replace("mrgb","").trim(),n)return[];const e=/[a-z0-9]/g,a=s.match(e);if(!a||a.length%8!==0)return[];const l=[];for(let h=0;h<a.length/8;h++){const p=a[h*8+2]+a[h*8+3],r=a[h*8+4]+a[h*8+5],d=a[h*8+6]+a[h*8+7];l.push(new x(parseInt(p,16)/255,parseInt(r,16)/255,parseInt(d,16)/255,1))}return l}parse(s,n,e,a,l){var m;n=n.replace(/#MRGB/g,"mrgb"),n=n.replace(/#.*$/gm,"").trim(),this._loadingOptions.useLegacyBehavior?(this._pushTriangle=(o,t)=>this._triangles.push(o[0],o[t],o[t+1]),this._handednessSign=1):e.useRightHandedSystem?(this._pushTriangle=(o,t)=>this._triangles.push(o[0],o[t+1],o[t]),this._handednessSign=1):(this._pushTriangle=(o,t)=>this._triangles.push(o[0],o[t],o[t+1]),this._handednessSign=-1);const h=n.split(`
`),p=[];let r=[];p.push(r);for(let o=0;o<h.length;o++){const t=h[o].trim().replace(/\s\s/g," ");if(!(t.length===0||t.charAt(0)==="#"))if((u._IsGroupElement(t)||u._IsObjectElement(t))&&(r=[],p.push(r)),u._IsLineElement(t)){const i=t.split(" ");for(let _=1;_<i.length-1;_++)r.push(`l ${i[_]} ${i[_+1]}`)}else r.push(t)}const d=p.flat();for(let o=0;o<d.length;o++){const t=d[o].trim().replace(/\s\s/g," ");let i;if(!(t.length===0||t.charAt(0)==="#"))if(u.VertexPattern.test(t)){if(i=t.match(/[^ ]+/g),this._positions.push(new y(parseFloat(i[1]),parseFloat(i[2]),parseFloat(i[3]))),this._loadingOptions.importVertexColors)if(i.length>=7){const _=parseFloat(i[4]),f=parseFloat(i[5]),g=parseFloat(i[6]);this._colors.push(new x(_>1?_/255:_,f>1?f/255:f,g>1?g/255:g,i.length===7||i[7]===void 0?1:parseFloat(i[7])))}else this._colors.push(this._grayColor)}else if((i=u.NormalPattern.exec(t))!==null)this._normals.push(new y(parseFloat(i[1]),parseFloat(i[2]),parseFloat(i[3])));else if((i=u.UVPattern.exec(t))!==null)this._uvs.push(new F(parseFloat(i[1])*this._loadingOptions.UVScaling.x,parseFloat(i[2])*this._loadingOptions.UVScaling.y));else if((i=u.FacePattern3.exec(t))!==null)this._setDataForCurrentFaceWithPattern3(i[1].trim().split(" "),1);else if((i=u.FacePattern4.exec(t))!==null)this._setDataForCurrentFaceWithPattern4(i[1].trim().split(" "),1);else if((i=u.FacePattern5.exec(t))!==null)this._setDataForCurrentFaceWithPattern5(i[1].trim().split(" "),1);else if((i=u.FacePattern2.exec(t))!==null)this._setDataForCurrentFaceWithPattern2(i[1].trim().split(" "),1);else if((i=u.FacePattern1.exec(t))!==null)this._setDataForCurrentFaceWithPattern1(i[1].trim().split(" "),1);else if((i=u.LinePattern1.exec(t))!==null)this._setDataForCurrentFaceWithPattern1(i[1].trim().split(" "),0),this._hasLineData=!0;else if((i=u.LinePattern2.exec(t))!==null)this._setDataForCurrentFaceWithPattern2(i[1].trim().split(" "),0),this._hasLineData=!0;else if(i=u._GetZbrushMRGB(t,!this._loadingOptions.importVertexColors))i.forEach(_=>{this._extColors.push(_)});else if((i=u.LinePattern3.exec(t))!==null)this._setDataForCurrentFaceWithPattern3(i[1].trim().split(" "),0),this._hasLineData=!0;else if(u.GroupDescriptor.test(t)||u.ObjectDescriptor.test(t)){const _={name:t.substring(2).trim(),indices:null,positions:null,normals:null,uvs:null,colors:null,materialName:this._materialNameFromObj,isObject:u.ObjectDescriptor.test(t)};this._addPreviousObjMesh(),this._meshesFromObj.push(_),this._hasMeshes=!0,this._isFirstMaterial=!0,this._increment=1}else if(u.UseMtlDescriptor.test(t)){if(this._materialNameFromObj=t.substring(7).trim(),!this._isFirstMaterial||!this._hasMeshes){this._addPreviousObjMesh();const _={name:(this._objMeshName||"mesh")+"_mm"+this._increment.toString(),indices:null,positions:null,normals:null,uvs:null,colors:null,materialName:this._materialNameFromObj,isObject:!1};this._increment++,this._meshesFromObj.push(_),this._hasMeshes=!0}this._hasMeshes&&this._isFirstMaterial&&(this._meshesFromObj[this._meshesFromObj.length-1].materialName=this._materialNameFromObj,this._isFirstMaterial=!1)}else u.MtlLibGroupDescriptor.test(t)?l(t.substring(7).trim()):u.SmoothDescriptor.test(t)||V.Log("Unhandled expression at line : "+t)}if(this._hasMeshes&&(this._handledMesh=this._meshesFromObj[this._meshesFromObj.length-1],this._loadingOptions.useLegacyBehavior&&this._indicesForBabylon.reverse(),this._unwrapData(),this._handledMesh.indices=this._indicesForBabylon,this._handledMesh.positions=this._unwrappedPositionsForBabylon,this._handledMesh.normals=this._unwrappedNormalsForBabylon,this._handledMesh.uvs=this._unwrappedUVForBabylon,this._handledMesh.hasLines=this._hasLineData,this._loadingOptions.importVertexColors&&(this._handledMesh.colors=this._unwrappedColorsForBabylon)),!this._hasMeshes){let o=null;if(this._indicesForBabylon.length)this._loadingOptions.useLegacyBehavior&&this._indicesForBabylon.reverse(),this._unwrapData();else{for(const t of this._positions)this._unwrappedPositionsForBabylon.push(t.x,t.y,t.z);if(this._normals.length)for(const t of this._normals)this._unwrappedNormalsForBabylon.push(t.x,t.y,t.z);if(this._uvs.length)for(const t of this._uvs)this._unwrappedUVForBabylon.push(t.x,t.y);if(this._extColors.length)for(const t of this._extColors)this._unwrappedColorsForBabylon.push(t.r,t.g,t.b,t.a);else if(this._colors.length)for(const t of this._colors)this._unwrappedColorsForBabylon.push(t.r,t.g,t.b,t.a);this._materialNameFromObj||(o=new B(P.RandomId(),e),o.pointsCloud=!0,this._materialNameFromObj=o.name,this._normals.length||(o.disableLighting=!0,o.emissiveColor=M.White()))}this._meshesFromObj.push({name:P.RandomId(),indices:this._indicesForBabylon,positions:this._unwrappedPositionsForBabylon,colors:this._unwrappedColorsForBabylon,normals:this._unwrappedNormalsForBabylon,uvs:this._unwrappedUVForBabylon,materialName:this._materialNameFromObj,directMaterial:o,isObject:!0,hasLines:this._hasLineData})}for(let o=0;o<this._meshesFromObj.length;o++){if(s&&this._meshesFromObj[o].name){if(s instanceof Array){if(s.indexOf(this._meshesFromObj[o].name)===-1)continue}else if(this._meshesFromObj[o].name!==s)continue}this._handledMesh=this._meshesFromObj[o],e._blockEntityCollection=!!a;const t=new A(this._meshesFromObj[o].name,e);if(t._parentContainer=a,e._blockEntityCollection=!1,this._handledMesh._babylonMesh=t,!this._handledMesh.isObject){for(let _=o-1;_>=0;--_)if(this._meshesFromObj[_].isObject&&this._meshesFromObj[_]._babylonMesh){t.parent=this._meshesFromObj[_]._babylonMesh;break}}if(this._materialToUse.push(this._meshesFromObj[o].materialName),this._handledMesh.hasLines&&(t._internalMetadata??(t._internalMetadata={}),t._internalMetadata._isLine=!0),((m=this._handledMesh.positions)==null?void 0:m.length)===0){this._babylonMeshesArray.push(t);continue}const i=new L;if(i.uvs=this._handledMesh.uvs,i.indices=this._handledMesh.indices,i.positions=this._handledMesh.positions,this._loadingOptions.computeNormals){const _=new Array;L.ComputeNormals(this._handledMesh.positions,this._handledMesh.indices,_),i.normals=_}else i.normals=this._handledMesh.normals;this._loadingOptions.importVertexColors&&(i.colors=this._handledMesh.colors),i.applyToMesh(t),this._loadingOptions.invertY&&(t.scaling.y*=-1),this._loadingOptions.optimizeNormals&&this._optimizeNormals(t),this._babylonMeshesArray.push(t),this._handledMesh.directMaterial&&(t.material=this._handledMesh.directMaterial)}}}u.ObjectDescriptor=/^o/;u.GroupDescriptor=/^g/;u.MtlLibGroupDescriptor=/^mtllib /;u.UseMtlDescriptor=/^usemtl /;u.SmoothDescriptor=/^s /;u.VertexPattern=/^v(\s+[\d|.|+|\-|e|E]+){3,7}/;u.NormalPattern=/^vn(\s+[\d|.|+|\-|e|E]+)( +[\d|.|+|\-|e|E]+)( +[\d|.|+|\-|e|E]+)/;u.UVPattern=/^vt(\s+[\d|.|+|\-|e|E]+)( +[\d|.|+|\-|e|E]+)/;u.FacePattern1=/^f\s+(([\d]{1,}[\s]?){3,})+/;u.FacePattern2=/^f\s+((([\d]{1,}\/[\d]{1,}[\s]?){3,})+)/;u.FacePattern3=/^f\s+((([\d]{1,}\/[\d]{1,}\/[\d]{1,}[\s]?){3,})+)/;u.FacePattern4=/^f\s+((([\d]{1,}\/\/[\d]{1,}[\s]?){3,})+)/;u.FacePattern5=/^f\s+(((-[\d]{1,}\/-[\d]{1,}\/-[\d]{1,}[\s]?){3,})+)/;u.LinePattern1=/^l\s+(([\d]{1,}[\s]?){2,})+/;u.LinePattern2=/^l\s+((([\d]{1,}\/[\d]{1,}[\s]?){2,})+)/;u.LinePattern3=/^l\s+((([\d]{1,}\/[\d]{1,}\/[\d]{1,}[\s]?){2,})+)/;class c{static get INVERT_TEXTURE_Y(){return b.INVERT_TEXTURE_Y}static set INVERT_TEXTURE_Y(s){b.INVERT_TEXTURE_Y=s}constructor(s){this.name=T.name,this.extensions=T.extensions,this._assetContainer=null,this._loadingOptions={...c._DefaultLoadingOptions,...s??{}}}static get _DefaultLoadingOptions(){return{computeNormals:c.COMPUTE_NORMALS,optimizeNormals:c.OPTIMIZE_NORMALS,importVertexColors:c.IMPORT_VERTEX_COLORS,invertY:c.INVERT_Y,invertTextureY:c.INVERT_TEXTURE_Y,UVScaling:c.UV_SCALING,materialLoadingFailsSilently:c.MATERIAL_LOADING_FAILS_SILENTLY,optimizeWithUV:c.OPTIMIZE_WITH_UV,skipMaterials:c.SKIP_MATERIALS,useLegacyBehavior:c.USE_LEGACY_BEHAVIOR}}_loadMTL(s,n,e,a){const l=n+s;I.LoadFile(l,e,void 0,void 0,!1,(h,p)=>{a(l,p)})}createPlugin(s){return new c(s[T.name])}canDirectLoad(){return!1}importMeshAsync(s,n,e,a){return this._parseSolid(s,n,e,a).then(l=>({meshes:l,particleSystems:[],skeletons:[],animationGroups:[],transformNodes:[],geometries:[],lights:[],spriteManagers:[]}))}loadAsync(s,n,e){return this.importMeshAsync(null,s,n,e).then(()=>{})}loadAssetContainerAsync(s,n,e){const a=new U(s);return this._assetContainer=a,this.importMeshAsync(null,s,n,e).then(l=>(l.meshes.forEach(h=>a.meshes.push(h)),l.meshes.forEach(h=>{const p=h.material;p&&a.materials.indexOf(p)==-1&&(a.materials.push(p),p.getActiveTextures().forEach(d=>{a.textures.indexOf(d)==-1&&a.textures.push(d)}))}),this._assetContainer=null,a)).catch(l=>{throw this._assetContainer=null,l})}_parseSolid(s,n,e,a){let l="";const h=new b,p=[],r=[];e=e.replace(/#.*$/gm,"").trim(),new u(p,r,this._loadingOptions).parse(s,e,n,this._assetContainer,o=>{l=o});const m=[];return l!==""&&!this._loadingOptions.skipMaterials&&m.push(new Promise((o,t)=>{this._loadMTL(l,a,i=>{try{h.parseMTL(n,i,a,this._assetContainer);for(let _=0;_<h.materials.length;_++){let f=0;const g=[];let w;for(;(w=p.indexOf(h.materials[_].name,f))>-1;)g.push(w),f=w+1;if(w===-1&&g.length===0)h.materials[_].dispose();else for(let O=0;O<g.length;O++){const E=r[g[O]],N=h.materials[_];E.material=N,E.getTotalIndices()||(N.pointsCloud=!0)}}o()}catch(_){I.Warn(`Error processing MTL file: '${l}'`),this._loadingOptions.materialLoadingFailsSilently?o():t(_)}},(i,_)=>{I.Warn(`Error downloading MTL file: '${l}'`),this._loadingOptions.materialLoadingFailsSilently?o():t(_)})})),Promise.all(m).then(()=>{const o=t=>{var i;return!!(((i=t._internalMetadata)==null?void 0:i._isLine)??!1)};return r.forEach(t=>{if(o(t)){let i=t.material??new B(t.name+"_line",n);i.getBindedMeshes().filter(f=>!o(f)).length>0&&(i=i.clone(i.name+"_line")??i),i.wireframe=!0,t.material=i,t._internalMetadata&&(t._internalMetadata._isLine=void 0)}}),r})}}c.OPTIMIZE_WITH_UV=!0;c.INVERT_Y=!1;c.IMPORT_VERTEX_COLORS=!1;c.COMPUTE_NORMALS=!1;c.OPTIMIZE_NORMALS=!1;c.UV_SCALING=new F(1,1);c.SKIP_MATERIALS=!1;c.MATERIAL_LOADING_FAILS_SILENTLY=!0;c.USE_LEGACY_BEHAVIOR=!1;v(new c);export{c as OBJFileLoader};
//# sourceMappingURL=objFileLoader.D_EDW7od.js.map
