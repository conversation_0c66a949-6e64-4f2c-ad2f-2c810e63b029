{"version": 3, "file": "AudioPlayer-CFZK-O44.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/AudioPlayer.js"], "sourcesContent": ["import{create_ssr_component as T,validate_component as R,add_attribute as B,add_styles as tt,escape as Y}from\"svelte/internal\";import{onMount as et,createEventDispatcher as rt}from\"svelte\";import{N as ot,O as st,Q as at,X as lt,x as dt,i as ct,Y as ut,q as At,H as ht,E as mt,M as vt}from\"./FullscreenButton.js\";import\"./client.js\";import{f as pt}from\"./utils.js\";import{r as X}from\"./DownloadLink.js\";import\"./hls.js\";var ft=function(l,t,e,n){function i(r){return r instanceof e?r:new e(function(o){o(r)})}return new(e||(e=Promise))(function(r,o){function a(s){try{d(n.next(s))}catch(u){o(u)}}function c(s){try{d(n.throw(s))}catch(u){o(u)}}function d(s){s.done?r(s.value):i(s.value).then(a,c)}d((n=n.apply(l,t||[])).next())})};function Ct(l,t){return ft(this,void 0,void 0,function*(){const e=new AudioContext({sampleRate:t});return e.decodeAudioData(l).finally(()=>e.close())})}function gt(l){const t=l[0];if(t.some(e=>e>1||e<-1)){const e=t.length;let n=0;for(let i=0;i<e;i++){const r=Math.abs(t[i]);r>n&&(n=r)}for(const i of l)for(let r=0;r<e;r++)i[r]/=n}return l}function wt(l,t){return typeof l[0]==\"number\"&&(l=[l]),gt(l),{duration:t,length:l[0].length,sampleRate:l[0].length/t,numberOfChannels:l.length,getChannelData:e=>l?.[e],copyFromChannel:AudioBuffer.prototype.copyFromChannel,copyToChannel:AudioBuffer.prototype.copyToChannel}}const U={decode:Ct,createBuffer:wt};var F=function(l,t,e,n){function i(r){return r instanceof e?r:new e(function(o){o(r)})}return new(e||(e=Promise))(function(r,o){function a(s){try{d(n.next(s))}catch(u){o(u)}}function c(s){try{d(n.throw(s))}catch(u){o(u)}}function d(s){s.done?r(s.value):i(s.value).then(a,c)}d((n=n.apply(l,t||[])).next())})};function bt(l,t,e){var n,i;return F(this,void 0,void 0,function*(){const r=yield fetch(l,e);{const o=(n=r.clone().body)===null||n===void 0?void 0:n.getReader(),a=Number((i=r.headers)===null||i===void 0?void 0:i.get(\"Content-Length\"));let c=0;const d=(s,u)=>F(this,void 0,void 0,function*(){if(s)return;c+=u?.length||0;const m=Math.round(c/a*100);return t(m),o?.read().then(({done:h,value:A})=>d(h,A))});o?.read().then(({done:s,value:u})=>d(s,u))}return r.blob()})}const yt={fetchBlob:bt};class H{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(t,e,n){if(this.listeners[t]||(this.listeners[t]=new Set),this.listeners[t].add(e),n?.once){const i=()=>{this.removeEventListener(t,i),this.removeEventListener(t,e)};return this.addEventListener(t,i),i}return()=>this.removeEventListener(t,e)}removeEventListener(t,e){var n;(n=this.listeners[t])===null||n===void 0||n.delete(e)}once(t,e){return this.on(t,e,{once:!0})}unAll(){this.listeners={}}emit(t,...e){this.listeners[t]&&this.listeners[t].forEach(n=>n(...e))}}class _t extends H{constructor(t){super(),this.isExternalMedia=!1,t.media?(this.media=t.media,this.isExternalMedia=!0):this.media=document.createElement(\"audio\"),t.mediaControls&&(this.media.controls=!0),t.autoplay&&(this.media.autoplay=!0),t.playbackRate!=null&&this.onceMediaEvent(\"canplay\",()=>{t.playbackRate!=null&&(this.media.playbackRate=t.playbackRate)})}onMediaEvent(t,e,n){return this.media.addEventListener(t,e,n),()=>this.media.removeEventListener(t,e)}onceMediaEvent(t,e){return this.onMediaEvent(t,e,{once:!0})}getSrc(){return this.media.currentSrc||this.media.src||\"\"}revokeSrc(){const t=this.getSrc();t.startsWith(\"blob:\")&&URL.revokeObjectURL(t)}setSrc(t,e){if(this.getSrc()===t)return;this.revokeSrc();const i=e instanceof Blob?URL.createObjectURL(e):t;this.media.src=i,this.media.load()}destroy(){this.media.pause(),!this.isExternalMedia&&(this.media.remove(),this.revokeSrc(),this.media.src=\"\",this.media.load())}setMediaElement(t){this.media=t}play(){return this.media.play()}pause(){this.media.pause()}isPlaying(){return!this.media.paused&&!this.media.ended}setTime(t){this.media.currentTime=t}getDuration(){return this.media.duration}getCurrentTime(){return this.media.currentTime}getVolume(){return this.media.volume}setVolume(t){this.media.volume=t}getMuted(){return this.media.muted}setMuted(t){this.media.muted=t}getPlaybackRate(){return this.media.playbackRate}setPlaybackRate(t,e){e!=null&&(this.media.preservesPitch=e),this.media.playbackRate=t}getMediaElement(){return this.media}setSinkId(t){return this.media.setSinkId(t)}}function Et(l,t,e,n,i=5){let r=()=>{};if(!l)return r;const o=a=>{if(a.button===2)return;a.preventDefault(),a.stopPropagation(),l.style.touchAction=\"none\";let c=a.clientX,d=a.clientY,s=!1;const u=A=>{A.preventDefault(),A.stopPropagation();const p=A.clientX,C=A.clientY;if(s||Math.abs(p-c)>=i||Math.abs(C-d)>=i){const{left:_,top:g}=l.getBoundingClientRect();s||(s=!0,e?.(c-_,d-g)),t(p-c,C-d,p-_,C-g),c=p,d=C}},m=A=>{s&&(A.preventDefault(),A.stopPropagation())},h=()=>{l.style.touchAction=\"\",s&&n?.(),r()};document.addEventListener(\"pointermove\",u),document.addEventListener(\"pointerup\",h),document.addEventListener(\"pointerleave\",h),document.addEventListener(\"click\",m,!0),r=()=>{document.removeEventListener(\"pointermove\",u),document.removeEventListener(\"pointerup\",h),document.removeEventListener(\"pointerleave\",h),setTimeout(()=>{document.removeEventListener(\"click\",m,!0)},10)}};return l.addEventListener(\"pointerdown\",o),()=>{r(),l.removeEventListener(\"pointerdown\",o)}}class K extends H{constructor(t,e){super(),this.timeouts=[],this.isScrolling=!1,this.audioData=null,this.resizeObserver=null,this.isDragging=!1,this.options=t;const n=this.parentFromOptionsContainer(t.container);this.parent=n;const[i,r]=this.initHtml();n.appendChild(i),this.container=i,this.scrollContainer=r.querySelector(\".scroll\"),this.wrapper=r.querySelector(\".wrapper\"),this.canvasWrapper=r.querySelector(\".canvases\"),this.progressWrapper=r.querySelector(\".progress\"),this.cursor=r.querySelector(\".cursor\"),e&&r.appendChild(e),this.initEvents()}parentFromOptionsContainer(t){let e;if(typeof t==\"string\"?e=document.querySelector(t):t instanceof HTMLElement&&(e=t),!e)throw new Error(\"Container not found\");return e}initEvents(){const t=n=>{const i=this.wrapper.getBoundingClientRect(),r=n.clientX-i.left,o=n.clientX-i.left,a=r/i.width,c=o/i.height;return[a,c]};this.wrapper.addEventListener(\"click\",n=>{const[i,r]=t(n);this.emit(\"click\",i,r)}),this.wrapper.addEventListener(\"dblclick\",n=>{const[i,r]=t(n);this.emit(\"dblclick\",i,r)}),this.options.dragToSeek&&this.initDrag(),this.scrollContainer.addEventListener(\"scroll\",()=>{const{scrollLeft:n,scrollWidth:i,clientWidth:r}=this.scrollContainer,o=n/i,a=(n+r)/i;this.emit(\"scroll\",o,a)});const e=this.createDelay(100);this.resizeObserver=new ResizeObserver(()=>{e(()=>this.reRender())}),this.resizeObserver.observe(this.scrollContainer)}initDrag(){Et(this.wrapper,(t,e,n)=>{this.emit(\"drag\",Math.max(0,Math.min(1,n/this.wrapper.getBoundingClientRect().width)))},()=>this.isDragging=!0,()=>this.isDragging=!1)}getHeight(){return this.options.height==null?128:isNaN(Number(this.options.height))?this.options.height===\"auto\"&&this.parent.clientHeight||128:Number(this.options.height)}initHtml(){const t=document.createElement(\"div\"),e=t.attachShadow({mode:\"open\"});return e.innerHTML=`\n      <style>\n        :host {\n          user-select: none;\n          min-width: 1px;\n        }\n        :host audio {\n          display: block;\n          width: 100%;\n        }\n        :host .scroll {\n          overflow-x: auto;\n          overflow-y: hidden;\n          width: 100%;\n          position: relative;\n        }\n        :host .noScrollbar {\n          scrollbar-color: transparent;\n          scrollbar-width: none;\n        }\n        :host .noScrollbar::-webkit-scrollbar {\n          display: none;\n          -webkit-appearance: none;\n        }\n        :host .wrapper {\n          position: relative;\n          overflow: visible;\n          z-index: 2;\n        }\n        :host .canvases {\n          min-height: ${this.getHeight()}px;\n        }\n        :host .canvases > div {\n          position: relative;\n        }\n        :host canvas {\n          display: block;\n          position: absolute;\n          top: 0;\n          image-rendering: pixelated;\n        }\n        :host .progress {\n          pointer-events: none;\n          position: absolute;\n          z-index: 2;\n          top: 0;\n          left: 0;\n          width: 0;\n          height: 100%;\n          overflow: hidden;\n        }\n        :host .progress > div {\n          position: relative;\n        }\n        :host .cursor {\n          pointer-events: none;\n          position: absolute;\n          z-index: 5;\n          top: 0;\n          left: 0;\n          height: 100%;\n          border-radius: 2px;\n        }\n      </style>\n\n      <div class=\"scroll\" part=\"scroll\">\n        <div class=\"wrapper\" part=\"wrapper\">\n          <div class=\"canvases\"></div>\n          <div class=\"progress\" part=\"progress\"></div>\n          <div class=\"cursor\" part=\"cursor\"></div>\n        </div>\n      </div>\n    `,[t,e]}setOptions(t){if(this.options.container!==t.container){const e=this.parentFromOptionsContainer(t.container);e.appendChild(this.container),this.parent=e}t.dragToSeek&&!this.options.dragToSeek&&this.initDrag(),this.options=t,this.reRender()}getWrapper(){return this.wrapper}getScroll(){return this.scrollContainer.scrollLeft}destroy(){var t;this.container.remove(),(t=this.resizeObserver)===null||t===void 0||t.disconnect()}createDelay(t=10){const e={};return this.timeouts.push(e),n=>{e.timeout&&clearTimeout(e.timeout),e.timeout=setTimeout(n,t)}}convertColorValues(t){if(!Array.isArray(t))return t||\"\";if(t.length<2)return t[0]||\"\";const e=document.createElement(\"canvas\"),i=e.getContext(\"2d\").createLinearGradient(0,0,0,e.height),r=1/(t.length-1);return t.forEach((o,a)=>{const c=a*r;i.addColorStop(c,o)}),i}renderBarWaveform(t,e,n,i){const r=t[0],o=t[1]||t[0],a=r.length,{width:c,height:d}=n.canvas,s=d/2,u=window.devicePixelRatio||1,m=e.barWidth?e.barWidth*u:1,h=e.barGap?e.barGap*u:e.barWidth?m/2:0,A=e.barRadius||0,p=c/(m+h)/a,C=A&&\"roundRect\"in n?\"roundRect\":\"rect\";n.beginPath();let _=0,g=0,v=0;for(let E=0;E<=a;E++){const w=Math.round(E*p);if(w>_){const k=Math.round(g*s*i),M=Math.round(v*s*i),S=k+M||1;let I=s-k;e.barAlign===\"top\"?I=0:e.barAlign===\"bottom\"&&(I=d-S),n[C](_*(m+h),I,m,S,A),_=w,g=0,v=0}const f=Math.abs(r[E]||0),x=Math.abs(o[E]||0);f>g&&(g=f),x>v&&(v=x)}n.fill(),n.closePath()}renderLineWaveform(t,e,n,i){const r=o=>{const a=t[o]||t[0],c=a.length,{height:d}=n.canvas,s=d/2,u=n.canvas.width/c;n.moveTo(0,s);let m=0,h=0;for(let A=0;A<=c;A++){const p=Math.round(A*u);if(p>m){const _=Math.round(h*s*i)||1,g=s+_*(o===0?-1:1);n.lineTo(m,g),m=p,h=0}const C=Math.abs(a[A]||0);C>h&&(h=C)}n.lineTo(m,s)};n.beginPath(),r(0),r(1),n.fill(),n.closePath()}renderWaveform(t,e,n){if(n.fillStyle=this.convertColorValues(e.waveColor),e.renderFunction){e.renderFunction(t,n);return}let i=e.barHeight||1;if(e.normalize){const r=Array.from(t[0]).reduce((o,a)=>Math.max(o,Math.abs(a)),0);i=r?1/r:1}if(e.barWidth||e.barGap||e.barAlign){this.renderBarWaveform(t,e,n,i);return}this.renderLineWaveform(t,e,n,i)}renderSingleCanvas(t,e,n,i,r,o,a,c){const d=window.devicePixelRatio||1,s=document.createElement(\"canvas\"),u=t[0].length;s.width=Math.round(n*(o-r)/u),s.height=i*d,s.style.width=`${Math.floor(s.width/d)}px`,s.style.height=`${i}px`,s.style.left=`${Math.floor(r*n/d/u)}px`,a.appendChild(s);const m=s.getContext(\"2d\");if(this.renderWaveform(t.map(h=>h.slice(r,o)),e,m),s.width>0&&s.height>0){const h=s.cloneNode(),A=h.getContext(\"2d\");A.drawImage(s,0,0),A.globalCompositeOperation=\"source-in\",A.fillStyle=this.convertColorValues(e.progressColor),A.fillRect(0,0,s.width,s.height),c.appendChild(h)}}renderChannel(t,e,n){const i=document.createElement(\"div\"),r=this.getHeight();i.style.height=`${r}px`,this.canvasWrapper.style.minHeight=`${r}px`,this.canvasWrapper.appendChild(i);const o=i.cloneNode();this.progressWrapper.appendChild(o);const{scrollLeft:a,scrollWidth:c,clientWidth:d}=this.scrollContainer,s=t[0].length,u=s/c;let m=Math.min(K.MAX_CANVAS_WIDTH,d);if(e.barWidth||e.barGap){const w=e.barWidth||.5,f=e.barGap||w/2,x=w+f;m%x!==0&&(m=Math.floor(m/x)*x)}const h=Math.floor(Math.abs(a)*u),A=Math.floor(h+m*u),p=A-h,C=(w,f)=>{this.renderSingleCanvas(t,e,n,r,Math.max(0,w),Math.min(f,s),i,o)},_=this.createDelay(),g=this.createDelay(),v=(w,f)=>{C(w,f),w>0&&_(()=>{v(w-p,f-p)})},E=(w,f)=>{C(w,f),f<s&&g(()=>{E(w+p,f+p)})};v(h,A),A<s&&E(A,A+p)}render(t){this.timeouts.forEach(a=>a.timeout&&clearTimeout(a.timeout)),this.timeouts=[],this.canvasWrapper.innerHTML=\"\",this.progressWrapper.innerHTML=\"\",this.wrapper.style.width=\"\",this.options.width!=null&&(this.scrollContainer.style.width=typeof this.options.width==\"number\"?`${this.options.width}px`:this.options.width);const e=window.devicePixelRatio||1,n=this.scrollContainer.clientWidth,i=Math.ceil(t.duration*(this.options.minPxPerSec||0));this.isScrolling=i>n;const r=this.options.fillParent&&!this.isScrolling,o=(r?n:i)*e;if(this.wrapper.style.width=r?\"100%\":`${i}px`,this.scrollContainer.style.overflowX=this.isScrolling?\"auto\":\"hidden\",this.scrollContainer.classList.toggle(\"noScrollbar\",!!this.options.hideScrollbar),this.cursor.style.backgroundColor=`${this.options.cursorColor||this.options.progressColor}`,this.cursor.style.width=`${this.options.cursorWidth}px`,this.options.splitChannels)for(let a=0;a<t.numberOfChannels;a++){const c=Object.assign(Object.assign({},this.options),this.options.splitChannels[a]);this.renderChannel([t.getChannelData(a)],c,o)}else{const a=[t.getChannelData(0)];t.numberOfChannels>1&&a.push(t.getChannelData(1)),this.renderChannel(a,this.options,o)}this.audioData=t,this.emit(\"render\")}reRender(){if(!this.audioData)return;const t=this.progressWrapper.clientWidth;this.render(this.audioData);const e=this.progressWrapper.clientWidth;this.scrollContainer.scrollLeft+=e-t}zoom(t){this.options.minPxPerSec=t,this.reRender()}scrollIntoView(t,e=!1){const{clientWidth:n,scrollLeft:i,scrollWidth:r}=this.scrollContainer,o=r*t,a=n/2,c=e&&this.options.autoCenter&&!this.isDragging?a:n;if(o>i+c||o<i)if(this.options.autoCenter&&!this.isDragging){const d=a/20;o-(i+a)>=d&&o<i+n?this.scrollContainer.scrollLeft+=d:this.scrollContainer.scrollLeft=o-a}else this.isDragging?this.scrollContainer.scrollLeft=o<i?o-10:o-n+10:this.scrollContainer.scrollLeft=o;{const{scrollLeft:d}=this.scrollContainer,s=d/r,u=(d+n)/r;this.emit(\"scroll\",s,u)}}renderProgress(t,e){if(isNaN(t))return;const n=t*100;this.canvasWrapper.style.clipPath=`polygon(${n}% 0, 100% 0, 100% 100%, ${n}% 100%)`,this.progressWrapper.style.width=`${n}%`,this.cursor.style.left=`${n}%`,this.cursor.style.marginLeft=Math.round(n)===100?`-${this.options.cursorWidth}px`:\"\",this.isScrolling&&this.options.autoScroll&&this.scrollIntoView(t,e)}}K.MAX_CANVAS_WIDTH=4e3;class xt extends H{constructor(){super(...arguments),this.unsubscribe=()=>{}}start(){this.unsubscribe=this.on(\"tick\",()=>{requestAnimationFrame(()=>{this.emit(\"tick\")})}),this.emit(\"tick\")}stop(){this.unsubscribe()}destroy(){this.unsubscribe()}}var Q=function(l,t,e,n){function i(r){return r instanceof e?r:new e(function(o){o(r)})}return new(e||(e=Promise))(function(r,o){function a(s){try{d(n.next(s))}catch(u){o(u)}}function c(s){try{d(n.throw(s))}catch(u){o(u)}}function d(s){s.done?r(s.value):i(s.value).then(a,c)}d((n=n.apply(l,t||[])).next())})};class kt extends H{constructor(t=new AudioContext){super(),this.bufferNode=null,this.autoplay=!1,this.playStartTime=0,this.playedDuration=0,this._muted=!1,this.buffer=null,this.currentSrc=\"\",this.paused=!0,this.crossOrigin=null,this.audioContext=t,this.gainNode=this.audioContext.createGain(),this.gainNode.connect(this.audioContext.destination)}load(){return Q(this,void 0,void 0,function*(){})}get src(){return this.currentSrc}set src(t){this.currentSrc=t,fetch(t).then(e=>e.arrayBuffer()).then(e=>this.audioContext.decodeAudioData(e)).then(e=>{this.buffer=e,this.emit(\"loadedmetadata\"),this.emit(\"canplay\"),this.autoplay&&this.play()})}_play(){var t;this.paused&&(this.paused=!1,(t=this.bufferNode)===null||t===void 0||t.disconnect(),this.bufferNode=this.audioContext.createBufferSource(),this.bufferNode.buffer=this.buffer,this.bufferNode.connect(this.gainNode),this.playedDuration>=this.duration&&(this.playedDuration=0),this.bufferNode.start(this.audioContext.currentTime,this.playedDuration),this.playStartTime=this.audioContext.currentTime,this.bufferNode.onended=()=>{this.currentTime>=this.duration&&(this.pause(),this.emit(\"ended\"))})}_pause(){var t;this.paused||(this.paused=!0,(t=this.bufferNode)===null||t===void 0||t.stop(),this.playedDuration+=this.audioContext.currentTime-this.playStartTime)}play(){return Q(this,void 0,void 0,function*(){this._play(),this.emit(\"play\")})}pause(){this._pause(),this.emit(\"pause\")}setSinkId(t){return Q(this,void 0,void 0,function*(){return this.audioContext.setSinkId(t)})}get playbackRate(){var t,e;return(e=(t=this.bufferNode)===null||t===void 0?void 0:t.playbackRate.value)!==null&&e!==void 0?e:1}set playbackRate(t){this.bufferNode&&(this.bufferNode.playbackRate.value=t)}get currentTime(){return this.paused?this.playedDuration:this.playedDuration+this.audioContext.currentTime-this.playStartTime}set currentTime(t){this.emit(\"seeking\"),this.paused?this.playedDuration=t:(this._pause(),this.playedDuration=t,this._play()),this.emit(\"timeupdate\")}get duration(){var t;return((t=this.buffer)===null||t===void 0?void 0:t.duration)||0}get volume(){return this.gainNode.gain.value}set volume(t){this.gainNode.gain.value=t,this.emit(\"volumechange\")}get muted(){return this._muted}set muted(t){this._muted!==t&&(this._muted=t,this._muted?this.gainNode.disconnect():this.gainNode.connect(this.audioContext.destination))}getGainNode(){return this.gainNode}}var W=function(l,t,e,n){function i(r){return r instanceof e?r:new e(function(o){o(r)})}return new(e||(e=Promise))(function(r,o){function a(s){try{d(n.next(s))}catch(u){o(u)}}function c(s){try{d(n.throw(s))}catch(u){o(u)}}function d(s){s.done?r(s.value):i(s.value).then(a,c)}d((n=n.apply(l,t||[])).next())})};const Rt={waveColor:\"#999\",progressColor:\"#555\",cursorWidth:1,minPxPerSec:0,fillParent:!0,interact:!0,dragToSeek:!1,autoScroll:!0,autoCenter:!0,sampleRate:8e3};class G extends _t{static create(t){return new G(t)}constructor(t){const e=t.media||(t.backend===\"WebAudio\"?new kt:void 0);super({media:e,mediaControls:t.mediaControls,autoplay:t.autoplay,playbackRate:t.audioRate}),this.plugins=[],this.decodedData=null,this.subscriptions=[],this.mediaSubscriptions=[],this.options=Object.assign({},Rt,t),this.timer=new xt;const n=e?void 0:this.getMediaElement();this.renderer=new K(this.options,n),this.initPlayerEvents(),this.initRendererEvents(),this.initTimerEvents(),this.initPlugins();const i=this.options.url||this.getSrc();i?this.load(i,this.options.peaks,this.options.duration):this.options.peaks&&this.options.duration&&this.loadPredecoded()}initTimerEvents(){this.subscriptions.push(this.timer.on(\"tick\",()=>{const t=this.getCurrentTime();this.renderer.renderProgress(t/this.getDuration(),!0),this.emit(\"timeupdate\",t),this.emit(\"audioprocess\",t)}))}initPlayerEvents(){this.mediaSubscriptions.push(this.onMediaEvent(\"timeupdate\",()=>{const t=this.getCurrentTime();this.renderer.renderProgress(t/this.getDuration(),this.isPlaying()),this.emit(\"timeupdate\",t)}),this.onMediaEvent(\"play\",()=>{this.emit(\"play\"),this.timer.start()}),this.onMediaEvent(\"pause\",()=>{this.emit(\"pause\"),this.timer.stop()}),this.onMediaEvent(\"emptied\",()=>{this.timer.stop()}),this.onMediaEvent(\"ended\",()=>{this.emit(\"finish\")}),this.onMediaEvent(\"seeking\",()=>{this.emit(\"seeking\",this.getCurrentTime())}))}initRendererEvents(){this.subscriptions.push(this.renderer.on(\"click\",(t,e)=>{this.options.interact&&(this.seekTo(t),this.emit(\"interaction\",t*this.getDuration()),this.emit(\"click\",t,e))}),this.renderer.on(\"dblclick\",(t,e)=>{this.emit(\"dblclick\",t,e)}),this.renderer.on(\"scroll\",(t,e)=>{const n=this.getDuration();this.emit(\"scroll\",t*n,e*n)}),this.renderer.on(\"render\",()=>{this.emit(\"redraw\")}));{let t;this.subscriptions.push(this.renderer.on(\"drag\",e=>{this.options.interact&&(this.renderer.renderProgress(e),clearTimeout(t),t=setTimeout(()=>{this.seekTo(e)},this.isPlaying()?0:200),this.emit(\"interaction\",e*this.getDuration()),this.emit(\"drag\",e))}))}}initPlugins(){var t;!((t=this.options.plugins)===null||t===void 0)&&t.length&&this.options.plugins.forEach(e=>{this.registerPlugin(e)})}unsubscribePlayerEvents(){this.mediaSubscriptions.forEach(t=>t()),this.mediaSubscriptions=[]}setOptions(t){this.options=Object.assign({},this.options,t),this.renderer.setOptions(this.options),t.audioRate&&this.setPlaybackRate(t.audioRate),t.mediaControls!=null&&(this.getMediaElement().controls=t.mediaControls)}registerPlugin(t){return t.init(this),this.plugins.push(t),this.subscriptions.push(t.once(\"destroy\",()=>{this.plugins=this.plugins.filter(e=>e!==t)})),t}getWrapper(){return this.renderer.getWrapper()}getScroll(){return this.renderer.getScroll()}getActivePlugins(){return this.plugins}loadPredecoded(){return W(this,void 0,void 0,function*(){this.options.peaks&&this.options.duration&&(this.decodedData=U.createBuffer(this.options.peaks,this.options.duration),yield Promise.resolve(),this.renderDecoded())})}renderDecoded(){return W(this,void 0,void 0,function*(){this.decodedData&&(this.emit(\"decode\",this.getDuration()),this.renderer.render(this.decodedData))})}loadAudio(t,e,n,i){return W(this,void 0,void 0,function*(){if(this.emit(\"load\",t),!this.options.media&&this.isPlaying()&&this.pause(),this.decodedData=null,!e&&!n){const r=o=>this.emit(\"loading\",o);e=yield yt.fetchBlob(t,r,this.options.fetchParams)}if(this.setSrc(t,e),i=(yield Promise.resolve(i||this.getDuration()))||(yield new Promise(r=>{this.onceMediaEvent(\"loadedmetadata\",()=>r(this.getDuration()))}))||(yield Promise.resolve(0)),n)this.decodedData=U.createBuffer(n,i);else if(e){const r=yield e.arrayBuffer();this.decodedData=yield U.decode(r,this.options.sampleRate)}this.renderDecoded(),this.emit(\"ready\",this.getDuration())})}load(t,e,n){return W(this,void 0,void 0,function*(){yield this.loadAudio(t,void 0,e,n)})}loadBlob(t,e,n){return W(this,void 0,void 0,function*(){yield this.loadAudio(\"blob\",t,e,n)})}zoom(t){if(!this.decodedData)throw new Error(\"No audio loaded\");this.renderer.zoom(t),this.emit(\"zoom\",t)}getDecodedData(){return this.decodedData}exportPeaks({channels:t=2,maxLength:e=8e3,precision:n=1e4}={}){if(!this.decodedData)throw new Error(\"The audio has not been decoded yet\");const i=Math.min(t,this.decodedData.numberOfChannels),r=[];for(let o=0;o<i;o++){const a=this.decodedData.getChannelData(o),c=[],d=Math.round(a.length/e);for(let s=0;s<e;s++){const u=a.slice(s*d,(s+1)*d),m=Math.max(...u);c.push(Math.round(m*n)/n)}r.push(c)}return r}getDuration(){let t=super.getDuration()||0;return(t===0||t===1/0)&&this.decodedData&&(t=this.decodedData.duration),t}toggleInteraction(t){this.options.interact=t}seekTo(t){const e=this.getDuration()*t;this.setTime(e)}playPause(){return W(this,void 0,void 0,function*(){return this.isPlaying()?this.pause():this.play()})}stop(){this.pause(),this.setTime(0)}skip(t){this.setTime(this.getCurrentTime()+t)}empty(){this.load(\"\",[[0]],.001)}setMediaElement(t){this.unsubscribePlayerEvents(),super.setMediaElement(t),this.initPlayerEvents()}destroy(){this.emit(\"destroy\"),this.plugins.forEach(t=>t.destroy()),this.subscriptions.forEach(t=>t()),this.unsubscribePlayerEvents(),this.timer.destroy(),this.renderer.destroy(),super.destroy()}}function It(l){const t=l.numberOfChannels,e=l.length*t*2+44,n=new ArrayBuffer(e),i=new DataView(n);let r=0;const o=function(a,c,d){for(let s=0;s<d.length;s++)a.setUint8(c+s,d.charCodeAt(s))};o(i,r,\"RIFF\"),r+=4,i.setUint32(r,e-8,!0),r+=4,o(i,r,\"WAVE\"),r+=4,o(i,r,\"fmt \"),r+=4,i.setUint32(r,16,!0),r+=4,i.setUint16(r,1,!0),r+=2,i.setUint16(r,t,!0),r+=2,i.setUint32(r,l.sampleRate,!0),r+=4,i.setUint32(r,l.sampleRate*2*t,!0),r+=4,i.setUint16(r,t*2,!0),r+=2,i.setUint16(r,16,!0),r+=2,o(i,r,\"data\"),r+=4,i.setUint32(r,l.length*t*2,!0),r+=4;for(let a=0;a<l.length;a++)for(let c=0;c<t;c++){const d=Math.max(-1,Math.min(1,l.getChannelData(c)[a]));i.setInt16(r,d*32767,!0),r+=2}return new Uint8Array(n)}const Mt=async(l,t,e,n)=>{const i=new AudioContext({sampleRate:n||l.sampleRate}),r=l.numberOfChannels,o=n||l.sampleRate;let a=l.length,c=0;t&&e&&(c=Math.round(t*o),a=Math.round(e*o)-c);const d=i.createBuffer(r,a,o);for(let s=0;s<r;s++){const u=l.getChannelData(s),m=d.getChannelData(s);for(let h=0;h<a;h++)m[h]=u[c+h]}return It(d)},Z=(l,t)=>{l&&l.skip(t)},J=(l,t)=>(t||(t=5),l/100*t||5);class nt{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(t,e,n){if(this.listeners[t]||(this.listeners[t]=new Set),this.listeners[t].add(e),n?.once){const i=()=>{this.removeEventListener(t,i),this.removeEventListener(t,e)};return this.addEventListener(t,i),i}return()=>this.removeEventListener(t,e)}removeEventListener(t,e){var n;(n=this.listeners[t])===null||n===void 0||n.delete(e)}once(t,e){return this.on(t,e,{once:!0})}unAll(){this.listeners={}}emit(t,...e){this.listeners[t]&&this.listeners[t].forEach(n=>n(...e))}}class Bt extends nt{constructor(t){super(),this.subscriptions=[],this.options=t}onInit(){}init(t){this.wavesurfer=t,this.onInit()}destroy(){this.emit(\"destroy\"),this.subscriptions.forEach(t=>t())}}function P(l,t,e,n,i=5){let r=()=>{};if(!l)return r;const o=a=>{if(a.button===2)return;a.preventDefault(),a.stopPropagation(),l.style.touchAction=\"none\";let c=a.clientX,d=a.clientY,s=!1;const u=A=>{A.preventDefault(),A.stopPropagation();const p=A.clientX,C=A.clientY;if(s||Math.abs(p-c)>=i||Math.abs(C-d)>=i){const{left:_,top:g}=l.getBoundingClientRect();s||(s=!0,e?.(c-_,d-g)),t(p-c,C-d,p-_,C-g),c=p,d=C}},m=A=>{s&&(A.preventDefault(),A.stopPropagation())},h=()=>{l.style.touchAction=\"\",s&&n?.(),r()};document.addEventListener(\"pointermove\",u),document.addEventListener(\"pointerup\",h),document.addEventListener(\"pointerleave\",h),document.addEventListener(\"click\",m,!0),r=()=>{document.removeEventListener(\"pointermove\",u),document.removeEventListener(\"pointerup\",h),document.removeEventListener(\"pointerleave\",h),setTimeout(()=>{document.removeEventListener(\"click\",m,!0)},10)}};return l.addEventListener(\"pointerdown\",o),()=>{r(),l.removeEventListener(\"pointerdown\",o)}}class $ extends nt{constructor(t,e,n=0){var i,r,o,a,c,d,s;super(),this.totalDuration=e,this.numberOfChannels=n,this.minLength=0,this.maxLength=1/0,this.id=t.id||`region-${Math.random().toString(32).slice(2)}`,this.start=this.clampPosition(t.start),this.end=this.clampPosition((i=t.end)!==null&&i!==void 0?i:t.start),this.drag=(r=t.drag)===null||r===void 0||r,this.resize=(o=t.resize)===null||o===void 0||o,this.color=(a=t.color)!==null&&a!==void 0?a:\"rgba(0, 0, 0, 0.1)\",this.minLength=(c=t.minLength)!==null&&c!==void 0?c:this.minLength,this.maxLength=(d=t.maxLength)!==null&&d!==void 0?d:this.maxLength,this.channelIdx=(s=t.channelIdx)!==null&&s!==void 0?s:-1,this.element=this.initElement(),this.setContent(t.content),this.setPart(),this.renderPosition(),this.initMouseEvents()}clampPosition(t){return Math.max(0,Math.min(this.totalDuration,t))}setPart(){const t=this.start===this.end;this.element.setAttribute(\"part\",`${t?\"marker\":\"region\"} ${this.id}`)}addResizeHandles(t){const e=document.createElement(\"div\");e.setAttribute(\"data-resize\",\"left\"),e.setAttribute(\"style\",`\n        position: absolute;\n        z-index: 2;\n        width: 6px;\n        height: 100%;\n        top: 0;\n        left: 0;\n        border-left: 2px solid rgba(0, 0, 0, 0.5);\n        border-radius: 2px 0 0 2px;\n        cursor: ew-resize;\n        word-break: keep-all;\n      `),e.setAttribute(\"part\",\"region-handle region-handle-left\");const n=e.cloneNode();n.setAttribute(\"data-resize\",\"right\"),n.style.left=\"\",n.style.right=\"0\",n.style.borderRight=n.style.borderLeft,n.style.borderLeft=\"\",n.style.borderRadius=\"0 2px 2px 0\",n.setAttribute(\"part\",\"region-handle region-handle-right\"),t.appendChild(e),t.appendChild(n),P(e,i=>this.onResize(i,\"start\"),()=>null,()=>this.onEndResizing(),1),P(n,i=>this.onResize(i,\"end\"),()=>null,()=>this.onEndResizing(),1)}removeResizeHandles(t){const e=t.querySelector('[data-resize=\"left\"]'),n=t.querySelector('[data-resize=\"right\"]');e&&t.removeChild(e),n&&t.removeChild(n)}initElement(){const t=document.createElement(\"div\"),e=this.start===this.end;let n=0,i=100;return this.channelIdx>=0&&this.channelIdx<this.numberOfChannels&&(i=100/this.numberOfChannels,n=i*this.channelIdx),t.setAttribute(\"style\",`\n      position: absolute;\n      top: ${n}%;\n      height: ${i}%;\n      background-color: ${e?\"none\":this.color};\n      border-left: ${e?\"2px solid \"+this.color:\"none\"};\n      border-radius: 2px;\n      box-sizing: border-box;\n      transition: background-color 0.2s ease;\n      cursor: ${this.drag?\"grab\":\"default\"};\n      pointer-events: all;\n    `),!e&&this.resize&&this.addResizeHandles(t),t}renderPosition(){const t=this.start/this.totalDuration,e=(this.totalDuration-this.end)/this.totalDuration;this.element.style.left=100*t+\"%\",this.element.style.right=100*e+\"%\"}initMouseEvents(){const{element:t}=this;t&&(t.addEventListener(\"click\",e=>this.emit(\"click\",e)),t.addEventListener(\"mouseenter\",e=>this.emit(\"over\",e)),t.addEventListener(\"mouseleave\",e=>this.emit(\"leave\",e)),t.addEventListener(\"dblclick\",e=>this.emit(\"dblclick\",e)),P(t,e=>this.onMove(e),()=>this.onStartMoving(),()=>this.onEndMoving()))}onStartMoving(){this.drag&&(this.element.style.cursor=\"grabbing\")}onEndMoving(){this.drag&&(this.element.style.cursor=\"grab\",this.emit(\"update-end\"))}_onUpdate(t,e){if(!this.element.parentElement)return;const n=t/this.element.parentElement.clientWidth*this.totalDuration,i=e&&e!==\"start\"?this.start:this.start+n,r=e&&e!==\"end\"?this.end:this.end+n,o=r-i;i>=0&&r<=this.totalDuration&&i<=r&&o>=this.minLength&&o<=this.maxLength&&(this.start=i,this.end=r,this.renderPosition(),this.emit(\"update\"))}onMove(t){this.drag&&this._onUpdate(t)}onResize(t,e){this.resize&&this._onUpdate(t,e)}onEndResizing(){this.resize&&this.emit(\"update-end\")}_setTotalDuration(t){this.totalDuration=t,this.renderPosition()}play(){this.emit(\"play\")}setContent(t){var e;if((e=this.content)===null||e===void 0||e.remove(),t){if(typeof t==\"string\"){this.content=document.createElement(\"div\");const n=this.start===this.end;this.content.style.padding=`0.2em ${n?.2:.4}em`,this.content.textContent=t}else this.content=t;this.content.setAttribute(\"part\",\"region-content\"),this.element.appendChild(this.content)}else this.content=void 0}setOptions(t){var e,n;if(t.color&&(this.color=t.color,this.element.style.backgroundColor=this.color),t.drag!==void 0&&(this.drag=t.drag,this.element.style.cursor=this.drag?\"grab\":\"default\"),t.start!==void 0||t.end!==void 0){const i=this.start===this.end;this.start=this.clampPosition((e=t.start)!==null&&e!==void 0?e:this.start),this.end=this.clampPosition((n=t.end)!==null&&n!==void 0?n:i?this.start:this.end),this.renderPosition(),this.setPart()}if(t.content&&this.setContent(t.content),t.id&&(this.id=t.id,this.setPart()),t.resize!==void 0&&t.resize!==this.resize){const i=this.start===this.end;this.resize=t.resize,this.resize&&!i?this.addResizeHandles(this.element):this.removeResizeHandles(this.element)}}remove(){this.emit(\"remove\"),this.element.remove(),this.element=null}}class N extends Bt{constructor(t){super(t),this.regions=[],this.regionsContainer=this.initRegionsContainer()}static create(t){return new N(t)}onInit(){if(!this.wavesurfer)throw Error(\"WaveSurfer is not initialized\");this.wavesurfer.getWrapper().appendChild(this.regionsContainer);let t=[];this.subscriptions.push(this.wavesurfer.on(\"timeupdate\",e=>{const n=this.regions.filter(i=>i.start<=e&&i.end>=e);n.forEach(i=>{t.includes(i)||this.emit(\"region-in\",i)}),t.forEach(i=>{n.includes(i)||this.emit(\"region-out\",i)}),t=n}))}initRegionsContainer(){const t=document.createElement(\"div\");return t.setAttribute(\"style\",`\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      z-index: 3;\n      pointer-events: none;\n    `),t}getRegions(){return this.regions}avoidOverlapping(t){if(!t.content)return;const e=t.content,n=e.getBoundingClientRect().left,i=t.element.scrollWidth,r=this.regions.filter(o=>{if(o===t||!o.content)return!1;const a=o.content.getBoundingClientRect().left,c=o.element.scrollWidth;return n<a+c&&a<n+i}).map(o=>{var a;return((a=o.content)===null||a===void 0?void 0:a.getBoundingClientRect().height)||0}).reduce((o,a)=>o+a,0);e.style.marginTop=`${r}px`}saveRegion(t){this.regionsContainer.appendChild(t.element),this.avoidOverlapping(t),this.regions.push(t);const e=[t.on(\"update-end\",()=>{this.avoidOverlapping(t),this.emit(\"region-updated\",t)}),t.on(\"play\",()=>{var n,i;(n=this.wavesurfer)===null||n===void 0||n.play(),(i=this.wavesurfer)===null||i===void 0||i.setTime(t.start)}),t.on(\"click\",n=>{this.emit(\"region-clicked\",t,n)}),t.on(\"dblclick\",n=>{this.emit(\"region-double-clicked\",t,n)}),t.once(\"remove\",()=>{e.forEach(n=>n()),this.regions=this.regions.filter(n=>n!==t)})];this.subscriptions.push(...e),this.emit(\"region-created\",t)}addRegion(t){var e,n;if(!this.wavesurfer)throw Error(\"WaveSurfer is not initialized\");const i=this.wavesurfer.getDuration(),r=(n=(e=this.wavesurfer)===null||e===void 0?void 0:e.getDecodedData())===null||n===void 0?void 0:n.numberOfChannels,o=new $(t,i,r);return i?this.saveRegion(o):this.subscriptions.push(this.wavesurfer.once(\"ready\",a=>{o._setTotalDuration(a),this.saveRegion(o)})),o}enableDragSelection(t){var e,n;const i=(n=(e=this.wavesurfer)===null||e===void 0?void 0:e.getWrapper())===null||n===void 0?void 0:n.querySelector(\"div\");if(!i)return()=>{};let r=null,o=0;return P(i,(a,c,d)=>{r&&r._onUpdate(a,d>o?\"end\":\"start\")},a=>{var c,d;if(o=a,!this.wavesurfer)return;const s=this.wavesurfer.getDuration(),u=(d=(c=this.wavesurfer)===null||c===void 0?void 0:c.getDecodedData())===null||d===void 0?void 0:d.numberOfChannels,m=this.wavesurfer.getWrapper().clientWidth,h=a/m*s,A=(a+5)/m*s;r=new $(Object.assign(Object.assign({},t),{start:h,end:A}),s,u),this.regionsContainer.appendChild(r.element)},()=>{r&&(this.saveRegion(r),r=null)})}clearRegions(){this.regions.forEach(t=>t.remove())}destroy(){this.clearRegions(),super.destroy()}}const St=T((l,t,e,n)=>{let{currentVolume:i}=t;return t.currentVolume===void 0&&e.currentVolume&&i!==void 0&&e.currentVolume(i),`${i==0?`${R(ot,\"VolumeMuted\").$$render(l,{},{},{})}`:`${i<.5?`${R(st,\"VolumeLow\").$$render(l,{},{},{})}`:`${i>=.5?`${R(at,\"VolumeHigh\").$$render(l,{},{},{})}`:\"\"}`}`}`}),Lt={code:'.volume-slider.svelte-wuo8j5{-webkit-appearance:none;appearance:none;width:var(--size-20);accent-color:var(--color-accent);height:4px;cursor:pointer;outline:none;border-radius:15px;background-color:var(--neutral-400)}input[type=\"range\"].svelte-wuo8j5::-webkit-slider-thumb{-webkit-appearance:none;appearance:none;height:15px;width:15px;background-color:var(--color-accent);border-radius:50%;border:none;transition:0.2s ease-in-out}input[type=\"range\"].svelte-wuo8j5::-moz-range-thumb{height:15px;width:15px;background-color:var(--color-accent);border-radius:50%;border:none;transition:0.2s ease-in-out}',map:'{\"version\":3,\"file\":\"VolumeControl.svelte\",\"sources\":[\"VolumeControl.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { onMount } from \\\\\"svelte\\\\\";\\\\nimport WaveSurfer from \\\\\"wavesurfer.js\\\\\";\\\\nexport let currentVolume = 1;\\\\nexport let show_volume_slider = false;\\\\nexport let waveform;\\\\nlet volumeElement;\\\\nonMount(() => {\\\\n    adjustSlider();\\\\n});\\\\nconst adjustSlider = () => {\\\\n    let slider = volumeElement;\\\\n    if (!slider)\\\\n        return;\\\\n    slider.style.background = `linear-gradient(to right, var(--color-accent) ${currentVolume * 100}%, var(--neutral-400) ${currentVolume * 100}%)`;\\\\n};\\\\n$: currentVolume, adjustSlider();\\\\n<\\/script>\\\\n\\\\n<input\\\\n\\\\tbind:this={volumeElement}\\\\n\\\\tid=\\\\\"volume\\\\\"\\\\n\\\\tclass=\\\\\"volume-slider\\\\\"\\\\n\\\\ttype=\\\\\"range\\\\\"\\\\n\\\\tmin=\\\\\"0\\\\\"\\\\n\\\\tmax=\\\\\"1\\\\\"\\\\n\\\\tstep=\\\\\"0.01\\\\\"\\\\n\\\\tvalue={currentVolume}\\\\n\\\\ton:focusout={() => (show_volume_slider = false)}\\\\n\\\\ton:input={(e) => {\\\\n\\\\t\\\\tif (e.target instanceof HTMLInputElement) {\\\\n\\\\t\\\\t\\\\tcurrentVolume = parseFloat(e.target.value);\\\\n\\\\t\\\\t\\\\twaveform?.setVolume(currentVolume);\\\\n\\\\t\\\\t}\\\\n\\\\t}}\\\\n/>\\\\n\\\\n<style>\\\\n\\\\t.volume-slider {\\\\n\\\\t\\\\t-webkit-appearance: none;\\\\n\\\\t\\\\tappearance: none;\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\taccent-color: var(--color-accent);\\\\n\\\\t\\\\theight: 4px;\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\toutline: none;\\\\n\\\\t\\\\tborder-radius: 15px;\\\\n\\\\t\\\\tbackground-color: var(--neutral-400);\\\\n\\\\t}\\\\n\\\\n\\\\tinput[type=\\\\\"range\\\\\"]::-webkit-slider-thumb {\\\\n\\\\t\\\\t-webkit-appearance: none;\\\\n\\\\t\\\\tappearance: none;\\\\n\\\\t\\\\theight: 15px;\\\\n\\\\t\\\\twidth: 15px;\\\\n\\\\t\\\\tbackground-color: var(--color-accent);\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\ttransition: 0.2s ease-in-out;\\\\n\\\\t}\\\\n\\\\n\\\\tinput[type=\\\\\"range\\\\\"]::-moz-range-thumb {\\\\n\\\\t\\\\theight: 15px;\\\\n\\\\t\\\\twidth: 15px;\\\\n\\\\t\\\\tbackground-color: var(--color-accent);\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\ttransition: 0.2s ease-in-out;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAqCC,4BAAe,CACd,kBAAkB,CAAE,IAAI,CACxB,UAAU,CAAE,IAAI,CAChB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,YAAY,CAAE,IAAI,cAAc,CAAC,CACjC,MAAM,CAAE,GAAG,CACX,MAAM,CAAE,OAAO,CACf,OAAO,CAAE,IAAI,CACb,aAAa,CAAE,IAAI,CACnB,gBAAgB,CAAE,IAAI,aAAa,CACpC,CAEA,KAAK,CAAC,IAAI,CAAC,OAAO,eAAC,sBAAuB,CACzC,kBAAkB,CAAE,IAAI,CACxB,UAAU,CAAE,IAAI,CAChB,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IAAI,CACX,gBAAgB,CAAE,IAAI,cAAc,CAAC,CACrC,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,IAAI,CAAC,WAClB,CAEA,KAAK,CAAC,IAAI,CAAC,OAAO,eAAC,kBAAmB,CACrC,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IAAI,CACX,gBAAgB,CAAE,IAAI,cAAc,CAAC,CACrC,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,IAAI,CAAC,WAClB\"}'},jt=T((l,t,e,n)=>{let{currentVolume:i=1}=t,{show_volume_slider:r=!1}=t,{waveform:o}=t,a;et(()=>{c()});const c=()=>{};return t.currentVolume===void 0&&e.currentVolume&&i!==void 0&&e.currentVolume(i),t.show_volume_slider===void 0&&e.show_volume_slider&&r!==void 0&&e.show_volume_slider(r),t.waveform===void 0&&e.waveform&&o!==void 0&&e.waveform(o),l.css.add(Lt),`<input id=\"volume\" class=\"volume-slider svelte-wuo8j5\" type=\"range\" min=\"0\" max=\"1\" step=\"0.01\"${B(\"value\",i,0)}${B(\"this\",a,0)}>`}),Dt={code:`.settings-wrapper.svelte-ije4bl.svelte-ije4bl{display:flex;justify-self:self-end;align-items:center;grid-area:editing}.text-button.svelte-ije4bl.svelte-ije4bl{border:1px solid var(--neutral-400);border-radius:var(--radius-sm);font-weight:300;font-size:var(--size-3);text-align:center;color:var(--neutral-400);height:var(--size-5);font-weight:bold;padding:0 5px;margin-left:5px}.text-button.svelte-ije4bl.svelte-ije4bl:hover,.text-button.svelte-ije4bl.svelte-ije4bl:focus{color:var(--color-accent);border-color:var(--color-accent)}.controls.svelte-ije4bl.svelte-ije4bl{display:grid;grid-template-columns:1fr 1fr 1fr;grid-template-areas:\"controls playback editing\";margin-top:5px;align-items:center;position:relative;flex-wrap:wrap;justify-content:space-between}.controls.svelte-ije4bl div.svelte-ije4bl{margin:var(--size-1) 0}@media(max-width: 600px){.controls.svelte-ije4bl.svelte-ije4bl{grid-template-columns:1fr 1fr;grid-template-rows:auto auto;grid-template-areas:\"playback playback\"\n\t\t\t\t\"controls editing\"}}@media(max-width: 319px){.controls.svelte-ije4bl.svelte-ije4bl{overflow-x:scroll}}.hidden.svelte-ije4bl.svelte-ije4bl{display:none}.control-wrapper.svelte-ije4bl.svelte-ije4bl{display:flex;justify-self:self-start;align-items:center;justify-content:space-between;grid-area:controls}.action.svelte-ije4bl.svelte-ije4bl{width:var(--size-5);color:var(--neutral-400);margin-left:var(--spacing-md)}.icon.svelte-ije4bl.svelte-ije4bl:hover,.icon.svelte-ije4bl.svelte-ije4bl:focus{color:var(--color-accent)}.play-pause-wrapper.svelte-ije4bl.svelte-ije4bl{display:flex;justify-self:center;grid-area:playback}@media(max-width: 600px){.play-pause-wrapper.svelte-ije4bl.svelte-ije4bl{margin:var(--spacing-md)}}.playback.svelte-ije4bl.svelte-ije4bl{border:1px solid var(--neutral-400);border-radius:var(--radius-sm);width:5.5ch;font-weight:300;font-size:var(--size-3);text-align:center;color:var(--neutral-400);height:var(--size-5);font-weight:bold}.playback.svelte-ije4bl.svelte-ije4bl:hover,.playback.svelte-ije4bl.svelte-ije4bl:focus{color:var(--color-accent);border-color:var(--color-accent)}.rewind.svelte-ije4bl.svelte-ije4bl,.skip.svelte-ije4bl.svelte-ije4bl{margin:0 10px;color:var(--neutral-400)}.play-pause-button.svelte-ije4bl.svelte-ije4bl{width:var(--size-8);display:flex;align-items:center;justify-content:center;color:var(--neutral-400);fill:var(--neutral-400)}.volume.svelte-ije4bl.svelte-ije4bl{position:relative;display:flex;justify-content:center;margin-right:var(--spacing-xl);width:var(--size-5)}`,map:'{\"version\":3,\"file\":\"WaveformControls.svelte\",\"sources\":[\"WaveformControls.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { Play, Pause, Forward, Backward, Undo, Trim } from \\\\\"@gradio/icons\\\\\";\\\\nimport { get_skip_rewind_amount } from \\\\\"../shared/utils\\\\\";\\\\nimport WaveSurfer from \\\\\"wavesurfer.js\\\\\";\\\\nimport RegionsPlugin, {} from \\\\\"wavesurfer.js/dist/plugins/regions.js\\\\\";\\\\nimport VolumeLevels from \\\\\"./VolumeLevels.svelte\\\\\";\\\\nimport VolumeControl from \\\\\"./VolumeControl.svelte\\\\\";\\\\nexport let waveform;\\\\nexport let audio_duration;\\\\nexport let i18n;\\\\nexport let playing;\\\\nexport let show_redo = false;\\\\nexport let interactive = false;\\\\nexport let handle_trim_audio;\\\\nexport let mode = \\\\\"\\\\\";\\\\nexport let container;\\\\nexport let handle_reset_value;\\\\nexport let waveform_options = {};\\\\nexport let trim_region_settings = {};\\\\nexport let show_volume_slider = false;\\\\nexport let editable = true;\\\\nexport let trimDuration = 0;\\\\nlet playbackSpeeds = [0.5, 1, 1.5, 2];\\\\nlet playbackSpeed = playbackSpeeds[1];\\\\nlet trimRegion = null;\\\\nlet activeRegion = null;\\\\nlet leftRegionHandle;\\\\nlet rightRegionHandle;\\\\nlet activeHandle = \\\\\"\\\\\";\\\\nlet currentVolume = 1;\\\\n$: trimRegion = container && waveform ? waveform.registerPlugin(RegionsPlugin.create()) : null;\\\\n$: trimRegion?.on(\\\\\"region-out\\\\\", (region) => {\\\\n    region.play();\\\\n});\\\\n$: trimRegion?.on(\\\\\"region-updated\\\\\", (region) => {\\\\n    trimDuration = region.end - region.start;\\\\n});\\\\n$: trimRegion?.on(\\\\\"region-clicked\\\\\", (region, e) => {\\\\n    e.stopPropagation();\\\\n    activeRegion = region;\\\\n    region.play();\\\\n});\\\\nconst addTrimRegion = () => {\\\\n    if (!trimRegion)\\\\n        return;\\\\n    activeRegion = trimRegion?.addRegion({\\\\n        start: audio_duration / 4,\\\\n        end: audio_duration / 2,\\\\n        ...trim_region_settings\\\\n    });\\\\n    trimDuration = activeRegion.end - activeRegion.start;\\\\n};\\\\n$: if (activeRegion) {\\\\n    const shadowRoot = container.children[0].shadowRoot;\\\\n    rightRegionHandle = shadowRoot.querySelector(\\'[data-resize=\\\\\"right\\\\\"]\\');\\\\n    leftRegionHandle = shadowRoot.querySelector(\\'[data-resize=\\\\\"left\\\\\"]\\');\\\\n    if (leftRegionHandle && rightRegionHandle) {\\\\n        leftRegionHandle.setAttribute(\\\\\"role\\\\\", \\\\\"button\\\\\");\\\\n        rightRegionHandle.setAttribute(\\\\\"role\\\\\", \\\\\"button\\\\\");\\\\n        leftRegionHandle?.setAttribute(\\\\\"aria-label\\\\\", \\\\\"Drag to adjust start time\\\\\");\\\\n        rightRegionHandle?.setAttribute(\\\\\"aria-label\\\\\", \\\\\"Drag to adjust end time\\\\\");\\\\n        leftRegionHandle?.setAttribute(\\\\\"tabindex\\\\\", \\\\\"0\\\\\");\\\\n        rightRegionHandle?.setAttribute(\\\\\"tabindex\\\\\", \\\\\"0\\\\\");\\\\n        leftRegionHandle.addEventListener(\\\\\"focus\\\\\", () => {\\\\n            if (trimRegion)\\\\n                activeHandle = \\\\\"left\\\\\";\\\\n        });\\\\n        rightRegionHandle.addEventListener(\\\\\"focus\\\\\", () => {\\\\n            if (trimRegion)\\\\n                activeHandle = \\\\\"right\\\\\";\\\\n        });\\\\n    }\\\\n}\\\\nconst trimAudio = () => {\\\\n    if (waveform && trimRegion) {\\\\n        if (activeRegion) {\\\\n            const start = activeRegion.start;\\\\n            const end = activeRegion.end;\\\\n            handle_trim_audio(start, end);\\\\n            mode = \\\\\"\\\\\";\\\\n            activeRegion = null;\\\\n        }\\\\n    }\\\\n};\\\\nconst clearRegions = () => {\\\\n    trimRegion?.getRegions().forEach((region) => {\\\\n        region.remove();\\\\n    });\\\\n    trimRegion?.clearRegions();\\\\n};\\\\nconst toggleTrimmingMode = () => {\\\\n    clearRegions();\\\\n    if (mode === \\\\\"edit\\\\\") {\\\\n        mode = \\\\\"\\\\\";\\\\n    }\\\\n    else {\\\\n        mode = \\\\\"edit\\\\\";\\\\n        addTrimRegion();\\\\n    }\\\\n};\\\\nconst adjustRegionHandles = (handle, key) => {\\\\n    let newStart;\\\\n    let newEnd;\\\\n    if (!activeRegion)\\\\n        return;\\\\n    if (handle === \\\\\"left\\\\\") {\\\\n        if (key === \\\\\"ArrowLeft\\\\\") {\\\\n            newStart = activeRegion.start - 0.05;\\\\n            newEnd = activeRegion.end;\\\\n        }\\\\n        else {\\\\n            newStart = activeRegion.start + 0.05;\\\\n            newEnd = activeRegion.end;\\\\n        }\\\\n    }\\\\n    else {\\\\n        if (key === \\\\\"ArrowLeft\\\\\") {\\\\n            newStart = activeRegion.start;\\\\n            newEnd = activeRegion.end - 0.05;\\\\n        }\\\\n        else {\\\\n            newStart = activeRegion.start;\\\\n            newEnd = activeRegion.end + 0.05;\\\\n        }\\\\n    }\\\\n    activeRegion.setOptions({\\\\n        start: newStart,\\\\n        end: newEnd\\\\n    });\\\\n    trimDuration = activeRegion.end - activeRegion.start;\\\\n};\\\\n$: trimRegion && window.addEventListener(\\\\\"keydown\\\\\", (e) => {\\\\n    if (e.key === \\\\\"ArrowLeft\\\\\") {\\\\n        adjustRegionHandles(activeHandle, \\\\\"ArrowLeft\\\\\");\\\\n    }\\\\n    else if (e.key === \\\\\"ArrowRight\\\\\") {\\\\n        adjustRegionHandles(activeHandle, \\\\\"ArrowRight\\\\\");\\\\n    }\\\\n});\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"controls\\\\\" data-testid=\\\\\"waveform-controls\\\\\">\\\\n\\\\t<div class=\\\\\"control-wrapper\\\\\">\\\\n\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\tclass=\\\\\"action icon volume\\\\\"\\\\n\\\\t\\\\t\\\\tstyle:color={show_volume_slider\\\\n\\\\t\\\\t\\\\t\\\\t? \\\\\"var(--color-accent)\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t: \\\\\"var(--neutral-400)\\\\\"}\\\\n\\\\t\\\\t\\\\taria-label=\\\\\"Adjust volume\\\\\"\\\\n\\\\t\\\\t\\\\ton:click={() => (show_volume_slider = !show_volume_slider)}\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t<VolumeLevels {currentVolume} />\\\\n\\\\t\\\\t</button>\\\\n\\\\n\\\\t\\\\t{#if show_volume_slider}\\\\n\\\\t\\\\t\\\\t<VolumeControl bind:currentVolume bind:show_volume_slider {waveform} />\\\\n\\\\t\\\\t{/if}\\\\n\\\\n\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\tclass:hidden={show_volume_slider}\\\\n\\\\t\\\\t\\\\tclass=\\\\\"playback icon\\\\\"\\\\n\\\\t\\\\t\\\\taria-label={`Adjust playback speed to ${\\\\n\\\\t\\\\t\\\\t\\\\tplaybackSpeeds[\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t(playbackSpeeds.indexOf(playbackSpeed) + 1) % playbackSpeeds.length\\\\n\\\\t\\\\t\\\\t\\\\t]\\\\n\\\\t\\\\t\\\\t}x`}\\\\n\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\tplaybackSpeed =\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tplaybackSpeeds[\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t(playbackSpeeds.indexOf(playbackSpeed) + 1) % playbackSpeeds.length\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t];\\\\n\\\\n\\\\t\\\\t\\\\t\\\\twaveform?.setPlaybackRate(playbackSpeed);\\\\n\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t<span>{playbackSpeed}x</span>\\\\n\\\\t\\\\t</button>\\\\n\\\\t</div>\\\\n\\\\n\\\\t<div class=\\\\\"play-pause-wrapper\\\\\">\\\\n\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\tclass=\\\\\"rewind icon\\\\\"\\\\n\\\\t\\\\t\\\\taria-label={`Skip backwards by ${get_skip_rewind_amount(\\\\n\\\\t\\\\t\\\\t\\\\taudio_duration,\\\\n\\\\t\\\\t\\\\t\\\\twaveform_options.skip_length\\\\n\\\\t\\\\t\\\\t)} seconds`}\\\\n\\\\t\\\\t\\\\ton:click={() =>\\\\n\\\\t\\\\t\\\\t\\\\twaveform?.skip(\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tget_skip_rewind_amount(audio_duration, waveform_options.skip_length) *\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t-1\\\\n\\\\t\\\\t\\\\t\\\\t)}\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t<Backward />\\\\n\\\\t\\\\t</button>\\\\n\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\tclass=\\\\\"play-pause-button icon\\\\\"\\\\n\\\\t\\\\t\\\\ton:click={() => waveform?.playPause()}\\\\n\\\\t\\\\t\\\\taria-label={playing ? i18n(\\\\\"audio.pause\\\\\") : i18n(\\\\\"audio.play\\\\\")}\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t{#if playing}\\\\n\\\\t\\\\t\\\\t\\\\t<Pause />\\\\n\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t<Play />\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t</button>\\\\n\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\tclass=\\\\\"skip icon\\\\\"\\\\n\\\\t\\\\t\\\\taria-label=\\\\\"Skip forward by {get_skip_rewind_amount(\\\\n\\\\t\\\\t\\\\t\\\\taudio_duration,\\\\n\\\\t\\\\t\\\\t\\\\twaveform_options.skip_length\\\\n\\\\t\\\\t\\\\t)} seconds\\\\\"\\\\n\\\\t\\\\t\\\\ton:click={() =>\\\\n\\\\t\\\\t\\\\t\\\\twaveform?.skip(\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tget_skip_rewind_amount(audio_duration, waveform_options.skip_length)\\\\n\\\\t\\\\t\\\\t\\\\t)}\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t<Forward />\\\\n\\\\t\\\\t</button>\\\\n\\\\t</div>\\\\n\\\\n\\\\t<div class=\\\\\"settings-wrapper\\\\\">\\\\n\\\\t\\\\t{#if editable && interactive}\\\\n\\\\t\\\\t\\\\t{#if show_redo && mode === \\\\\"\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"action icon\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\taria-label=\\\\\"Reset audio\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\thandle_reset_value();\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclearRegions();\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmode = \\\\\"\\\\\";\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<Undo />\\\\n\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\n\\\\t\\\\t\\\\t{#if mode === \\\\\"\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"action icon\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\taria-label=\\\\\"Trim audio to selection\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={toggleTrimmingMode}\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<Trim />\\\\n\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t<button class=\\\\\"text-button\\\\\" on:click={trimAudio}>Trim</button>\\\\n\\\\t\\\\t\\\\t\\\\t<button class=\\\\\"text-button\\\\\" on:click={toggleTrimmingMode}>Cancel</button\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t{/if}\\\\n\\\\t</div>\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.settings-wrapper {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-self: self-end;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tgrid-area: editing;\\\\n\\\\t}\\\\n\\\\t.text-button {\\\\n\\\\t\\\\tborder: 1px solid var(--neutral-400);\\\\n\\\\t\\\\tborder-radius: var(--radius-sm);\\\\n\\\\t\\\\tfont-weight: 300;\\\\n\\\\t\\\\tfont-size: var(--size-3);\\\\n\\\\t\\\\ttext-align: center;\\\\n\\\\t\\\\tcolor: var(--neutral-400);\\\\n\\\\t\\\\theight: var(--size-5);\\\\n\\\\t\\\\tfont-weight: bold;\\\\n\\\\t\\\\tpadding: 0 5px;\\\\n\\\\t\\\\tmargin-left: 5px;\\\\n\\\\t}\\\\n\\\\n\\\\t.text-button:hover,\\\\n\\\\t.text-button:focus {\\\\n\\\\t\\\\tcolor: var(--color-accent);\\\\n\\\\t\\\\tborder-color: var(--color-accent);\\\\n\\\\t}\\\\n\\\\n\\\\t.controls {\\\\n\\\\t\\\\tdisplay: grid;\\\\n\\\\t\\\\tgrid-template-columns: 1fr 1fr 1fr;\\\\n\\\\t\\\\tgrid-template-areas: \\\\\"controls playback editing\\\\\";\\\\n\\\\t\\\\tmargin-top: 5px;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tflex-wrap: wrap;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t}\\\\n\\\\t.controls div {\\\\n\\\\t\\\\tmargin: var(--size-1) 0;\\\\n\\\\t}\\\\n\\\\n\\\\t@media (max-width: 600px) {\\\\n\\\\t\\\\t.controls {\\\\n\\\\t\\\\t\\\\tgrid-template-columns: 1fr 1fr;\\\\n\\\\t\\\\t\\\\tgrid-template-rows: auto auto;\\\\n\\\\t\\\\t\\\\tgrid-template-areas:\\\\n\\\\t\\\\t\\\\t\\\\t\\\\\"playback playback\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\\"controls editing\\\\\";\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t@media (max-width: 319px) {\\\\n\\\\t\\\\t.controls {\\\\n\\\\t\\\\t\\\\toverflow-x: scroll;\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.hidden {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.control-wrapper {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-self: self-start;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\tgrid-area: controls;\\\\n\\\\t}\\\\n\\\\n\\\\t.action {\\\\n\\\\t\\\\twidth: var(--size-5);\\\\n\\\\t\\\\tcolor: var(--neutral-400);\\\\n\\\\t\\\\tmargin-left: var(--spacing-md);\\\\n\\\\t}\\\\n\\\\t.icon:hover,\\\\n\\\\t.icon:focus {\\\\n\\\\t\\\\tcolor: var(--color-accent);\\\\n\\\\t}\\\\n\\\\t.play-pause-wrapper {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-self: center;\\\\n\\\\t\\\\tgrid-area: playback;\\\\n\\\\t}\\\\n\\\\n\\\\t@media (max-width: 600px) {\\\\n\\\\t\\\\t.play-pause-wrapper {\\\\n\\\\t\\\\t\\\\tmargin: var(--spacing-md);\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\t.playback {\\\\n\\\\t\\\\tborder: 1px solid var(--neutral-400);\\\\n\\\\t\\\\tborder-radius: var(--radius-sm);\\\\n\\\\t\\\\twidth: 5.5ch;\\\\n\\\\t\\\\tfont-weight: 300;\\\\n\\\\t\\\\tfont-size: var(--size-3);\\\\n\\\\t\\\\ttext-align: center;\\\\n\\\\t\\\\tcolor: var(--neutral-400);\\\\n\\\\t\\\\theight: var(--size-5);\\\\n\\\\t\\\\tfont-weight: bold;\\\\n\\\\t}\\\\n\\\\n\\\\t.playback:hover,\\\\n\\\\t.playback:focus {\\\\n\\\\t\\\\tcolor: var(--color-accent);\\\\n\\\\t\\\\tborder-color: var(--color-accent);\\\\n\\\\t}\\\\n\\\\n\\\\t.rewind,\\\\n\\\\t.skip {\\\\n\\\\t\\\\tmargin: 0 10px;\\\\n\\\\t\\\\tcolor: var(--neutral-400);\\\\n\\\\t}\\\\n\\\\n\\\\t.play-pause-button {\\\\n\\\\t\\\\twidth: var(--size-8);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\tcolor: var(--neutral-400);\\\\n\\\\t\\\\tfill: var(--neutral-400);\\\\n\\\\t}\\\\n\\\\n\\\\t.volume {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\tmargin-right: var(--spacing-xl);\\\\n\\\\t\\\\twidth: var(--size-5);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA6PC,6CAAkB,CACjB,OAAO,CAAE,IAAI,CACb,YAAY,CAAE,QAAQ,CACtB,WAAW,CAAE,MAAM,CACnB,SAAS,CAAE,OACZ,CACA,wCAAa,CACZ,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,CACpC,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,WAAW,CAAE,GAAG,CAChB,SAAS,CAAE,IAAI,QAAQ,CAAC,CACxB,UAAU,CAAE,MAAM,CAClB,KAAK,CAAE,IAAI,aAAa,CAAC,CACzB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,WAAW,CAAE,IAAI,CACjB,OAAO,CAAE,CAAC,CAAC,GAAG,CACd,WAAW,CAAE,GACd,CAEA,wCAAY,MAAM,CAClB,wCAAY,MAAO,CAClB,KAAK,CAAE,IAAI,cAAc,CAAC,CAC1B,YAAY,CAAE,IAAI,cAAc,CACjC,CAEA,qCAAU,CACT,OAAO,CAAE,IAAI,CACb,qBAAqB,CAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAClC,mBAAmB,CAAE,2BAA2B,CAChD,UAAU,CAAE,GAAG,CACf,WAAW,CAAE,MAAM,CACnB,QAAQ,CAAE,QAAQ,CAClB,SAAS,CAAE,IAAI,CACf,eAAe,CAAE,aAClB,CACA,uBAAS,CAAC,iBAAI,CACb,MAAM,CAAE,IAAI,QAAQ,CAAC,CAAC,CACvB,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,qCAAU,CACT,qBAAqB,CAAE,GAAG,CAAC,GAAG,CAC9B,kBAAkB,CAAE,IAAI,CAAC,IAAI,CAC7B,mBAAmB,CAClB,mBAAmB;AACvB,IAAI,kBACF,CACD,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,qCAAU,CACT,UAAU,CAAE,MACb,CACD,CAEA,mCAAQ,CACP,OAAO,CAAE,IACV,CAEA,4CAAiB,CAChB,OAAO,CAAE,IAAI,CACb,YAAY,CAAE,UAAU,CACxB,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,aAAa,CAC9B,SAAS,CAAE,QACZ,CAEA,mCAAQ,CACP,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,KAAK,CAAE,IAAI,aAAa,CAAC,CACzB,WAAW,CAAE,IAAI,YAAY,CAC9B,CACA,iCAAK,MAAM,CACX,iCAAK,MAAO,CACX,KAAK,CAAE,IAAI,cAAc,CAC1B,CACA,+CAAoB,CACnB,OAAO,CAAE,IAAI,CACb,YAAY,CAAE,MAAM,CACpB,SAAS,CAAE,QACZ,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,+CAAoB,CACnB,MAAM,CAAE,IAAI,YAAY,CACzB,CACD,CACA,qCAAU,CACT,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,CACpC,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,KAAK,CAAE,KAAK,CACZ,WAAW,CAAE,GAAG,CAChB,SAAS,CAAE,IAAI,QAAQ,CAAC,CACxB,UAAU,CAAE,MAAM,CAClB,KAAK,CAAE,IAAI,aAAa,CAAC,CACzB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,WAAW,CAAE,IACd,CAEA,qCAAS,MAAM,CACf,qCAAS,MAAO,CACf,KAAK,CAAE,IAAI,cAAc,CAAC,CAC1B,YAAY,CAAE,IAAI,cAAc,CACjC,CAEA,mCAAO,CACP,iCAAM,CACL,MAAM,CAAE,CAAC,CAAC,IAAI,CACd,KAAK,CAAE,IAAI,aAAa,CACzB,CAEA,8CAAmB,CAClB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,KAAK,CAAE,IAAI,aAAa,CAAC,CACzB,IAAI,CAAE,IAAI,aAAa,CACxB,CAEA,mCAAQ,CACP,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,YAAY,CAAE,IAAI,YAAY,CAAC,CAC/B,KAAK,CAAE,IAAI,QAAQ,CACpB\"}'},Wt=T((l,t,e,n)=>{let{waveform:i}=t,{audio_duration:r}=t,{i18n:o}=t,{playing:a}=t,{show_redo:c=!1}=t,{interactive:d=!1}=t,{handle_trim_audio:s}=t,{mode:u=\"\"}=t,{container:m}=t,{handle_reset_value:h}=t,{waveform_options:A={}}=t,{trim_region_settings:p={}}=t,{show_volume_slider:C=!1}=t,{editable:_=!0}=t,{trimDuration:g=0}=t,v=[.5,1,1.5,2],E=v[1],w=null,f=null,x,k,M=\"\",S=1;const I=(b,L)=>{let j,y;f&&(b===\"left\"?L===\"ArrowLeft\"?(j=f.start-.05,y=f.end):(j=f.start+.05,y=f.end):L===\"ArrowLeft\"?(j=f.start,y=f.end-.05):(j=f.start,y=f.end+.05),f.setOptions({start:j,end:y}),g=f.end-f.start)};t.waveform===void 0&&e.waveform&&i!==void 0&&e.waveform(i),t.audio_duration===void 0&&e.audio_duration&&r!==void 0&&e.audio_duration(r),t.i18n===void 0&&e.i18n&&o!==void 0&&e.i18n(o),t.playing===void 0&&e.playing&&a!==void 0&&e.playing(a),t.show_redo===void 0&&e.show_redo&&c!==void 0&&e.show_redo(c),t.interactive===void 0&&e.interactive&&d!==void 0&&e.interactive(d),t.handle_trim_audio===void 0&&e.handle_trim_audio&&s!==void 0&&e.handle_trim_audio(s),t.mode===void 0&&e.mode&&u!==void 0&&e.mode(u),t.container===void 0&&e.container&&m!==void 0&&e.container(m),t.handle_reset_value===void 0&&e.handle_reset_value&&h!==void 0&&e.handle_reset_value(h),t.waveform_options===void 0&&e.waveform_options&&A!==void 0&&e.waveform_options(A),t.trim_region_settings===void 0&&e.trim_region_settings&&p!==void 0&&e.trim_region_settings(p),t.show_volume_slider===void 0&&e.show_volume_slider&&C!==void 0&&e.show_volume_slider(C),t.editable===void 0&&e.editable&&_!==void 0&&e.editable(_),t.trimDuration===void 0&&e.trimDuration&&g!==void 0&&e.trimDuration(g),l.css.add(Dt);let D,z,V=l.head;do{if(D=!0,l.head=V,w=m&&i?i.registerPlugin(N.create()):null,w?.on(\"region-out\",b=>{b.play()}),w?.on(\"region-updated\",b=>{g=b.end-b.start}),w?.on(\"region-clicked\",(b,L)=>{L.stopPropagation(),f=b,b.play()}),f){const b=m.children[0].shadowRoot;k=b.querySelector('[data-resize=\"right\"]'),x=b.querySelector('[data-resize=\"left\"]'),x&&k&&(x.setAttribute(\"role\",\"button\"),k.setAttribute(\"role\",\"button\"),x?.setAttribute(\"aria-label\",\"Drag to adjust start time\"),k?.setAttribute(\"aria-label\",\"Drag to adjust end time\"),x?.setAttribute(\"tabindex\",\"0\"),k?.setAttribute(\"tabindex\",\"0\"),x.addEventListener(\"focus\",()=>{w&&(M=\"left\")}),k.addEventListener(\"focus\",()=>{w&&(M=\"right\")}))}w&&window.addEventListener(\"keydown\",b=>{b.key===\"ArrowLeft\"?I(M,\"ArrowLeft\"):b.key===\"ArrowRight\"&&I(M,\"ArrowRight\")}),z=`<div class=\"controls svelte-ije4bl\" data-testid=\"waveform-controls\"><div class=\"control-wrapper svelte-ije4bl\"><button class=\"action icon volume svelte-ije4bl\" aria-label=\"Adjust volume\"${tt({color:C?\"var(--color-accent)\":\"var(--neutral-400)\"})}>${R(St,\"VolumeLevels\").$$render(l,{currentVolume:S},{},{})}</button> ${C?`${R(jt,\"VolumeControl\").$$render(l,{waveform:i,currentVolume:S,show_volume_slider:C},{currentVolume:b=>{S=b,D=!1},show_volume_slider:b=>{C=b,D=!1}},{})}`:\"\"} <button class=\"${[\"playback icon svelte-ije4bl\",C?\"hidden\":\"\"].join(\" \").trim()}\"${B(\"aria-label\",`Adjust playback speed to ${v[(v.indexOf(E)+1)%v.length]}x`,0)}><span>${Y(E)}x</span></button></div> <div class=\"play-pause-wrapper svelte-ije4bl\"><button class=\"rewind icon svelte-ije4bl\"${B(\"aria-label\",`Skip backwards by ${J(r,A.skip_length)} seconds`,0)}>${R(lt,\"Backward\").$$render(l,{},{},{})}</button> <button class=\"play-pause-button icon svelte-ije4bl\"${B(\"aria-label\",o(a?\"audio.pause\":\"audio.play\"),0)}>${a?`${R(dt,\"Pause\").$$render(l,{},{},{})}`:`${R(ct,\"Play\").$$render(l,{},{},{})}`}</button> <button class=\"skip icon svelte-ije4bl\" aria-label=\"${\"Skip forward by \"+Y(J(r,A.skip_length),!0)+\" seconds\"}\">${R(ut,\"Forward\").$$render(l,{},{},{})}</button></div> <div class=\"settings-wrapper svelte-ije4bl\">${_&&d?`${c&&u===\"\"?`<button class=\"action icon svelte-ije4bl\" aria-label=\"Reset audio\">${R(At,\"Undo\").$$render(l,{},{},{})}</button>`:\"\"} ${u===\"\"?`<button class=\"action icon svelte-ije4bl\" aria-label=\"Trim audio to selection\">${R(ht,\"Trim\").$$render(l,{},{},{})}</button>`:'<button class=\"text-button svelte-ije4bl\" data-svelte-h=\"svelte-1brf00d\">Trim</button> <button class=\"text-button svelte-ije4bl\" data-svelte-h=\"svelte-1r0ma01\">Cancel</button>'}`:\"\"}</div> </div>`}while(!D);return z}),zt={code:\".component-wrapper.svelte-19usgod{padding:var(--size-3);width:100%}::part(wrapper){margin-bottom:var(--size-2)}.timestamps.svelte-19usgod{display:flex;justify-content:space-between;align-items:center;width:100%;padding:var(--size-1) 0}#time.svelte-19usgod{color:var(--neutral-400)}#duration.svelte-19usgod{color:var(--neutral-400)}#trim-duration.svelte-19usgod{color:var(--color-accent);margin-right:var(--spacing-sm)}.waveform-container.svelte-19usgod{display:flex;align-items:center;justify-content:center;width:var(--size-full)}#waveform.svelte-19usgod{width:100%;height:100%;position:relative}.standard-player.svelte-19usgod{width:100%;padding:var(--size-2)}.hidden.svelte-19usgod{display:none}\",map:'{\"version\":3,\"file\":\"AudioPlayer.svelte\",\"sources\":[\"AudioPlayer.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { onMount } from \\\\\"svelte\\\\\";\\\\nimport { Music } from \\\\\"@gradio/icons\\\\\";\\\\nimport { format_time } from \\\\\"@gradio/utils\\\\\";\\\\nimport WaveSurfer from \\\\\"wavesurfer.js\\\\\";\\\\nimport { skip_audio, process_audio } from \\\\\"../shared/utils\\\\\";\\\\nimport WaveformControls from \\\\\"../shared/WaveformControls.svelte\\\\\";\\\\nimport { Empty } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { resolve_wasm_src } from \\\\\"@gradio/wasm/svelte\\\\\";\\\\nimport { createEventDispatcher } from \\\\\"svelte\\\\\";\\\\nimport Hls from \\\\\"hls.js\\\\\";\\\\nexport let value = null;\\\\n$: url = value?.url;\\\\nexport let label;\\\\nexport let i18n;\\\\nexport let dispatch_blob = () => Promise.resolve();\\\\nexport let interactive = false;\\\\nexport let editable = true;\\\\nexport let trim_region_settings = {};\\\\nexport let waveform_settings;\\\\nexport let waveform_options;\\\\nexport let mode = \\\\\"\\\\\";\\\\nexport let loop;\\\\nexport let handle_reset_value = () => {\\\\n};\\\\nlet container;\\\\nlet waveform;\\\\nlet playing = false;\\\\nlet timeRef;\\\\nlet durationRef;\\\\nlet audio_duration;\\\\nlet trimDuration = 0;\\\\nlet show_volume_slider = false;\\\\nlet audio_player;\\\\nlet stream_active = false;\\\\nconst dispatch = createEventDispatcher();\\\\n$: use_waveform = waveform_options.show_recording_waveform && !value?.is_stream;\\\\nconst create_waveform = () => {\\\\n    waveform = WaveSurfer.create({\\\\n        container,\\\\n        ...waveform_settings\\\\n    });\\\\n    resolve_wasm_src(value?.url).then((resolved_src) => {\\\\n        if (resolved_src && waveform) {\\\\n            return waveform.load(resolved_src);\\\\n        }\\\\n    });\\\\n};\\\\n$: if (use_waveform && container !== void 0 && container !== null) {\\\\n    if (waveform !== void 0)\\\\n        waveform.destroy();\\\\n    container.innerHTML = \\\\\"\\\\\";\\\\n    create_waveform();\\\\n    playing = false;\\\\n}\\\\n$: waveform?.on(\\\\\"decode\\\\\", (duration) => {\\\\n    audio_duration = duration;\\\\n    durationRef && (durationRef.textContent = format_time(duration));\\\\n});\\\\n$: waveform?.on(\\\\\"timeupdate\\\\\", (currentTime) => timeRef && (timeRef.textContent = format_time(currentTime)));\\\\n$: waveform?.on(\\\\\"ready\\\\\", () => {\\\\n    if (!waveform_settings.autoplay) {\\\\n        waveform?.stop();\\\\n    }\\\\n    else {\\\\n        waveform?.play();\\\\n    }\\\\n});\\\\n$: waveform?.on(\\\\\"finish\\\\\", () => {\\\\n    if (loop) {\\\\n        waveform?.play();\\\\n    }\\\\n    else {\\\\n        playing = false;\\\\n        dispatch(\\\\\"stop\\\\\");\\\\n    }\\\\n});\\\\n$: waveform?.on(\\\\\"pause\\\\\", () => {\\\\n    playing = false;\\\\n    dispatch(\\\\\"pause\\\\\");\\\\n});\\\\n$: waveform?.on(\\\\\"play\\\\\", () => {\\\\n    playing = true;\\\\n    dispatch(\\\\\"play\\\\\");\\\\n});\\\\n$: waveform?.on(\\\\\"load\\\\\", () => {\\\\n    dispatch(\\\\\"load\\\\\");\\\\n});\\\\nconst handle_trim_audio = async (start, end) => {\\\\n    mode = \\\\\"\\\\\";\\\\n    const decodedData = waveform?.getDecodedData();\\\\n    if (decodedData)\\\\n        await process_audio(decodedData, start, end, waveform_settings.sampleRate).then(async (trimmedBlob) => {\\\\n            await dispatch_blob([trimmedBlob], \\\\\"change\\\\\");\\\\n            waveform?.destroy();\\\\n            container.innerHTML = \\\\\"\\\\\";\\\\n        });\\\\n    dispatch(\\\\\"edit\\\\\");\\\\n};\\\\nasync function load_audio(data) {\\\\n    stream_active = false;\\\\n    await resolve_wasm_src(data).then((resolved_src) => {\\\\n        if (!resolved_src || value?.is_stream)\\\\n            return;\\\\n        if (waveform_options.show_recording_waveform) {\\\\n            waveform?.load(resolved_src);\\\\n        }\\\\n        else if (audio_player) {\\\\n            audio_player.src = resolved_src;\\\\n        }\\\\n    });\\\\n}\\\\n$: url && load_audio(url);\\\\nfunction load_stream(value2) {\\\\n    if (!value2 || !value2.is_stream || !value2.url)\\\\n        return;\\\\n    if (Hls.isSupported() && !stream_active) {\\\\n        const hls = new Hls({\\\\n            maxBufferLength: 1,\\\\n            maxMaxBufferLength: 1,\\\\n            lowLatencyMode: true\\\\n        });\\\\n        hls.loadSource(value2.url);\\\\n        hls.attachMedia(audio_player);\\\\n        hls.on(Hls.Events.MANIFEST_PARSED, function () {\\\\n            if (waveform_settings.autoplay)\\\\n                audio_player.play();\\\\n        });\\\\n        hls.on(Hls.Events.ERROR, function (event, data) {\\\\n            console.error(\\\\\"HLS error:\\\\\", event, data);\\\\n            if (data.fatal) {\\\\n                switch (data.type) {\\\\n                    case Hls.ErrorTypes.NETWORK_ERROR:\\\\n                        console.error(\\\\\"Fatal network error encountered, trying to recover\\\\\");\\\\n                        hls.startLoad();\\\\n                        break;\\\\n                    case Hls.ErrorTypes.MEDIA_ERROR:\\\\n                        console.error(\\\\\"Fatal media error encountered, trying to recover\\\\\");\\\\n                        hls.recoverMediaError();\\\\n                        break;\\\\n                    default:\\\\n                        console.error(\\\\\"Fatal error, cannot recover\\\\\");\\\\n                        hls.destroy();\\\\n                        break;\\\\n                }\\\\n            }\\\\n        });\\\\n        stream_active = true;\\\\n    }\\\\n    else if (!stream_active) {\\\\n        audio_player.src = value2.url;\\\\n        if (waveform_settings.autoplay)\\\\n            audio_player.play();\\\\n        stream_active = true;\\\\n    }\\\\n}\\\\n$: if (audio_player && value?.is_stream) {\\\\n    load_stream(value);\\\\n}\\\\nonMount(() => {\\\\n    window.addEventListener(\\\\\"keydown\\\\\", (e) => {\\\\n        if (!waveform || show_volume_slider)\\\\n            return;\\\\n        if (e.key === \\\\\"ArrowRight\\\\\" && mode !== \\\\\"edit\\\\\") {\\\\n            skip_audio(waveform, 0.1);\\\\n        }\\\\n        else if (e.key === \\\\\"ArrowLeft\\\\\" && mode !== \\\\\"edit\\\\\") {\\\\n            skip_audio(waveform, -0.1);\\\\n        }\\\\n    });\\\\n});\\\\n<\\/script>\\\\n\\\\n<audio\\\\n\\\\tclass=\\\\\"standard-player\\\\\"\\\\n\\\\tclass:hidden={use_waveform}\\\\n\\\\tcontrols\\\\n\\\\tautoplay={waveform_settings.autoplay}\\\\n\\\\ton:load\\\\n\\\\tbind:this={audio_player}\\\\n\\\\ton:ended={() => dispatch(\\\\\"stop\\\\\")}\\\\n\\\\ton:play={() => dispatch(\\\\\"play\\\\\")}\\\\n/>\\\\n{#if value === null}\\\\n\\\\t<Empty size=\\\\\"small\\\\\">\\\\n\\\\t\\\\t<Music />\\\\n\\\\t</Empty>\\\\n{:else if use_waveform}\\\\n\\\\t<div\\\\n\\\\t\\\\tclass=\\\\\"component-wrapper\\\\\"\\\\n\\\\t\\\\tdata-testid={label ? \\\\\"waveform-\\\\\" + label : \\\\\"unlabelled-audio\\\\\"}\\\\n\\\\t>\\\\n\\\\t\\\\t<div class=\\\\\"waveform-container\\\\\">\\\\n\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\tid=\\\\\"waveform\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tbind:this={container}\\\\n\\\\t\\\\t\\\\t\\\\tstyle:height={container ? null : \\\\\"58px\\\\\"}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t</div>\\\\n\\\\n\\\\t\\\\t<div class=\\\\\"timestamps\\\\\">\\\\n\\\\t\\\\t\\\\t<time bind:this={timeRef} id=\\\\\"time\\\\\">0:00</time>\\\\n\\\\t\\\\t\\\\t<div>\\\\n\\\\t\\\\t\\\\t\\\\t{#if mode === \\\\\"edit\\\\\" && trimDuration > 0}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<time id=\\\\\"trim-duration\\\\\">{format_time(trimDuration)}</time>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t<time bind:this={durationRef} id=\\\\\"duration\\\\\">0:00</time>\\\\n\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t</div>\\\\n\\\\n\\\\t\\\\t<WaveformControls\\\\n\\\\t\\\\t\\\\t{container}\\\\n\\\\t\\\\t\\\\t{waveform}\\\\n\\\\t\\\\t\\\\t{playing}\\\\n\\\\t\\\\t\\\\t{audio_duration}\\\\n\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t{interactive}\\\\n\\\\t\\\\t\\\\t{handle_trim_audio}\\\\n\\\\t\\\\t\\\\tbind:mode\\\\n\\\\t\\\\t\\\\tbind:trimDuration\\\\n\\\\t\\\\t\\\\tbind:show_volume_slider\\\\n\\\\t\\\\t\\\\tshow_redo={interactive}\\\\n\\\\t\\\\t\\\\t{handle_reset_value}\\\\n\\\\t\\\\t\\\\t{waveform_options}\\\\n\\\\t\\\\t\\\\t{trim_region_settings}\\\\n\\\\t\\\\t\\\\t{editable}\\\\n\\\\t\\\\t/>\\\\n\\\\t</div>\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.component-wrapper {\\\\n\\\\t\\\\tpadding: var(--size-3);\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t:global(::part(wrapper)) {\\\\n\\\\t\\\\tmargin-bottom: var(--size-2);\\\\n\\\\t}\\\\n\\\\n\\\\t.timestamps {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tpadding: var(--size-1) 0;\\\\n\\\\t}\\\\n\\\\n\\\\t#time {\\\\n\\\\t\\\\tcolor: var(--neutral-400);\\\\n\\\\t}\\\\n\\\\n\\\\t#duration {\\\\n\\\\t\\\\tcolor: var(--neutral-400);\\\\n\\\\t}\\\\n\\\\n\\\\t#trim-duration {\\\\n\\\\t\\\\tcolor: var(--color-accent);\\\\n\\\\t\\\\tmargin-right: var(--spacing-sm);\\\\n\\\\t}\\\\n\\\\t.waveform-container {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t}\\\\n\\\\n\\\\t#waveform {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t}\\\\n\\\\n\\\\t.standard-player {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tpadding: var(--size-2);\\\\n\\\\t}\\\\n\\\\n\\\\t.hidden {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAsOC,iCAAmB,CAClB,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,KAAK,CAAE,IACR,CAEQ,eAAiB,CACxB,aAAa,CAAE,IAAI,QAAQ,CAC5B,CAEA,0BAAY,CACX,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,aAAa,CAC9B,WAAW,CAAE,MAAM,CACnB,KAAK,CAAE,IAAI,CACX,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,CACxB,CAEA,oBAAM,CACL,KAAK,CAAE,IAAI,aAAa,CACzB,CAEA,wBAAU,CACT,KAAK,CAAE,IAAI,aAAa,CACzB,CAEA,6BAAe,CACd,KAAK,CAAE,IAAI,cAAc,CAAC,CAC1B,YAAY,CAAE,IAAI,YAAY,CAC/B,CACA,kCAAoB,CACnB,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,KAAK,CAAE,IAAI,WAAW,CACvB,CAEA,wBAAU,CACT,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,QAAQ,CAAE,QACX,CAEA,+BAAiB,CAChB,KAAK,CAAE,IAAI,CACX,OAAO,CAAE,IAAI,QAAQ,CACtB,CAEA,sBAAQ,CACP,OAAO,CAAE,IACV\"}'},Ot=T((l,t,e,n)=>{let i,r,{value:o=null}=t,{label:a}=t,{i18n:c}=t,{dispatch_blob:d=()=>Promise.resolve()}=t,{interactive:s=!1}=t,{editable:u=!0}=t,{trim_region_settings:m={}}=t,{waveform_settings:h}=t,{waveform_options:A}=t,{mode:p=\"\"}=t,{loop:C}=t,{handle_reset_value:_=()=>{}}=t,g,v,E=!1,w,f,x,k=0,M=!1,S;const I=rt(),D=()=>{v=G.create({container:g,...h}),X(o?.url).then(y=>{if(y&&v)return v.load(y)})},z=async(y,O)=>{p=\"\";const q=v?.getDecodedData();q&&await Mt(q,y,O,h.sampleRate).then(async it=>{await d([it],\"change\"),v?.destroy(),g.innerHTML=\"\"}),I(\"edit\")};async function V(y){await X(y).then(O=>{!O||o?.is_stream||A.show_recording_waveform&&v?.load(O)})}et(()=>{window.addEventListener(\"keydown\",y=>{!v||M||(y.key===\"ArrowRight\"&&p!==\"edit\"?Z(v,.1):y.key===\"ArrowLeft\"&&p!==\"edit\"&&Z(v,-.1))})}),t.value===void 0&&e.value&&o!==void 0&&e.value(o),t.label===void 0&&e.label&&a!==void 0&&e.label(a),t.i18n===void 0&&e.i18n&&c!==void 0&&e.i18n(c),t.dispatch_blob===void 0&&e.dispatch_blob&&d!==void 0&&e.dispatch_blob(d),t.interactive===void 0&&e.interactive&&s!==void 0&&e.interactive(s),t.editable===void 0&&e.editable&&u!==void 0&&e.editable(u),t.trim_region_settings===void 0&&e.trim_region_settings&&m!==void 0&&e.trim_region_settings(m),t.waveform_settings===void 0&&e.waveform_settings&&h!==void 0&&e.waveform_settings(h),t.waveform_options===void 0&&e.waveform_options&&A!==void 0&&e.waveform_options(A),t.mode===void 0&&e.mode&&p!==void 0&&e.mode(p),t.loop===void 0&&e.loop&&C!==void 0&&e.loop(C),t.handle_reset_value===void 0&&e.handle_reset_value&&_!==void 0&&e.handle_reset_value(_),l.css.add(zt);let b,L,j=l.head;do b=!0,l.head=j,i=o?.url,r=A.show_recording_waveform&&!o?.is_stream,r&&g!==void 0&&g!==null&&(v!==void 0&&v.destroy(),g.innerHTML=\"\",D(),E=!1),v?.on(\"decode\",y=>{x=y}),v?.on(\"timeupdate\",y=>w),v?.on(\"ready\",()=>{h.autoplay?v?.play():v?.stop()}),v?.on(\"finish\",()=>{C?v?.play():(E=!1,I(\"stop\"))}),v?.on(\"pause\",()=>{E=!1,I(\"pause\")}),v?.on(\"play\",()=>{E=!0,I(\"play\")}),v?.on(\"load\",()=>{I(\"load\")}),i&&V(i),L=`<audio class=\"${[\"standard-player svelte-19usgod\",r?\"hidden\":\"\"].join(\" \").trim()}\" controls ${h.autoplay?\"autoplay\":\"\"}${B(\"this\",S,0)}></audio> ${o===null?`${R(mt,\"Empty\").$$render(l,{size:\"small\"},{},{default:()=>`${R(vt,\"Music\").$$render(l,{},{},{})}`})}`:`${r?`<div class=\"component-wrapper svelte-19usgod\"${B(\"data-testid\",a?\"waveform-\"+a:\"unlabelled-audio\",0)}><div class=\"waveform-container svelte-19usgod\"><div id=\"waveform\" class=\"svelte-19usgod\"${tt({height:\"58px\"})}${B(\"this\",g,0)}></div></div> <div class=\"timestamps svelte-19usgod\"><time id=\"time\" class=\"svelte-19usgod\"${B(\"this\",w,0)} data-svelte-h=\"svelte-lp3mlp\">0:00</time> <div>${p===\"edit\"&&k>0?`<time id=\"trim-duration\" class=\"svelte-19usgod\">${Y(pt(k))}</time>`:\"\"} <time id=\"duration\" class=\"svelte-19usgod\"${B(\"this\",f,0)} data-svelte-h=\"svelte-1jd0owv\">0:00</time></div></div> ${R(Wt,\"WaveformControls\").$$render(l,{container:g,waveform:v,playing:E,audio_duration:x,i18n:c,interactive:s,handle_trim_audio:z,show_redo:s,handle_reset_value:_,waveform_options:A,trim_region_settings:m,editable:u,mode:p,trimDuration:k,show_volume_slider:M},{mode:y=>{p=y,b=!1},trimDuration:y=>{k=y,b=!1},show_volume_slider:y=>{M=y,b=!1}},{})}</div>`:\"\"}`}`;while(!b);return L}),Yt=Ot;export{Yt as A,Z as s};\n//# sourceMappingURL=AudioPlayer.js.map\n"], "names": ["T", "R", "ot", "st", "at", "B", "tt", "Y", "lt", "dt", "ct", "ut", "At", "ht", "A", "w", "rt", "X", "mt", "vt", "pt"], "mappings": ";;;;AAAma,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,SAAS,CAAC,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,oBAAmB,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM,IAAI,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,GAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,EAAC,CAAC,EAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,GAAG,CAAC,eAAe,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAE,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAC,CAAC,IAAI,EAAE,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAE,CAAC,SAAS,EAAE,CAAC,OAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,EAAC,CAAC,WAAW,EAAE,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAC,CAAC,eAAe,EAAE,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,EAAC,CAAC,eAAe,EAAE,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,eAAe,EAAE,EAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,GAAE,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAE,EAAC,EAAC,CAAC,CAAC,OAAO,CAAC,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,GAAE,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAC,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAE,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC;AACh9N;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAE,CAAC,UAAU,EAAE,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,GAAE,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,GAAG,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS,GAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS,GAAE,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,EAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,KAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC,CAAC,EAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAE,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,EAAC,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,wBAAwB,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,GAAE,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,WAAW,GAAE,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,WAAW,GAAE,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,EAAC,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,GAAE,CAAC,EAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAC,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAC,CAAC,IAAI,WAAW,EAAE,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAC,CAAC,IAAI,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAC,CAAC,IAAI,KAAK,EAAE,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,EAAC,CAAC,WAAW,EAAE,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,OAAO,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,GAAE,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,gBAAgB,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAE,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAE,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAE,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,EAAC,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,uBAAuB,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,aAAa,EAAE,IAAI,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,EAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,SAAS,EAAE,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,gBAAgB,EAAE,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE,EAAC,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,MAAM,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAC,CAAC,CAAC,CAAC,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAC,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,cAAc,EAAE,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,OAAM,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,EAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,GAAE,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,OAAO,GAAE,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAC,CAAC,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAM,MAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,oBAAmB,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAM,IAAI,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,GAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,EAAC,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAE,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,eAAe,EAAE,EAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,GAAE,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAE,EAAC,EAAC,CAAC,CAAC,OAAO,CAAC,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,eAAe,GAAE,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AAClhkB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,kCAAkC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,mCAAmC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,EAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,EAAC,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AACt2B;AACA,WAAW,EAAE,CAAC,CAAC;AACf,cAAc,EAAE,CAAC,CAAC;AAClB,wBAAwB,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;AAC9C,mBAAmB,EAAE,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AACtD;AACA;AACA;AACA,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;AAC3C;AACA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAG,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,EAAC,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,GAAE,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAC,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAC,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,EAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,OAAO,GAAE,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,EAAC,CAAC,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,KAAI,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,oBAAoB,GAAE,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,+BAA+B,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,oBAAoB,EAAE,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AACt/F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,OAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,+BAA+B,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAM,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,EAAC,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,KAAK,CAAC,OAAO,GAAE,CAAC,CAAM,MAAC,EAAE,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEC,kBAAC,CAACC,EAAE,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAED,kBAAC,CAACE,EAAE,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAEF,kBAAC,CAACG,EAAE,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,2lBAA2lB,CAAC,GAAG,CAAC,mnFAAmnF,CAAC,CAAC,EAAE,CAACJ,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAA8B,OAAO,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kBAAkB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,+FAA+F,EAAEK,aAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,aAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;AACloM,u/CAAu/C,CAAC,CAAC,GAAG,CAAC,o1aAAo1a,CAAC,CAAC,EAAE,CAACL,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kBAAkB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kBAAkB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,GAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,YAAY,CAAC,2BAA2B,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,YAAY,CAAC,yBAAyB,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAE,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,0LAA0L,EAAEM,UAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,EAAEL,kBAAC,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAEA,kBAAC,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,6BAA6B,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAEI,aAAC,CAAC,YAAY,CAAC,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAEE,MAAC,CAAC,CAAC,CAAC,CAAC,+GAA+G,EAAEF,aAAC,CAAC,YAAY,CAAC,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEJ,kBAAC,CAACO,IAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,8DAA8D,EAAEH,aAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEJ,kBAAC,CAACQ,EAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAER,kBAAC,CAACS,EAAE,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,8DAA8D,EAAE,kBAAkB,CAACH,MAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,EAAEN,kBAAC,CAACU,EAAE,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,4DAA4D,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,mEAAmE,EAAEV,kBAAC,CAACW,EAAE,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,+EAA+E,EAAEX,kBAAC,CAACY,EAAE,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,iLAAiL,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,EAAC,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,4rBAA4rB,CAAC,GAAG,CAAC,i6RAAi6R,CAAC,CAAC,EAAE,CAACb,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAACc,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAACC,qBAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAACC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,cAAc,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,GAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,MAAMA,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,EAAEH,GAAC,CAAC,uBAAuB,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAA+I,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAEA,GAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAACA,GAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kBAAkB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAACA,GAAC,CAAC,uBAAuB,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,EAAEC,GAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,GAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,EAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,gCAAgC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,EAAEV,aAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,EAAEJ,kBAAC,CAACiB,IAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAEjB,kBAAC,CAACkB,EAAE,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,6CAA6C,EAAEd,aAAC,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,yFAAyF,EAAEC,UAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAED,aAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,2FAA2F,EAAEA,aAAC,CAAC,MAAM,CAACU,GAAC,CAAC,CAAC,CAAC,CAAC,gDAAgD,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,gDAAgD,EAAER,MAAC,CAACa,CAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,2CAA2C,EAAEf,aAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,wDAAwD,EAAEJ,kBAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,gBAAgB,CAACa,GAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;;;"}