{"version": 3, "file": "Example.VAz9a1G1.js", "sources": ["../../../../../../../json/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport <PERSON><PERSON><PERSON> from \"./shared/JSON.svelte\";\n\n\texport let value: any;\n\texport let theme_mode: \"system\" | \"light\" | \"dark\" = \"system\";\n\tlet show_indices = false;\n\tlet label_height = 0;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass=\"container\"\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n\tclass:border={value}\n>\n\t{#if value}\n\t\t<JSON\n\t\t\t{value}\n\t\t\topen={true}\n\t\t\t{theme_mode}\n\t\t\t{show_indices}\n\t\t\t{label_height}\n\t\t\tinteractive={false}\n\t\t\tshow_copy_button={false}\n\t\t/>\n\t{/if}\n</div>\n\n<style>\n\t.container :global(img) {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.container.selected {\n\t\tborder-color: var(--border-color-accent);\n\t}\n\t.border.table {\n\t\tborder: 1px solid var(--border-color-primary);\n\t}\n\n\t.container.table {\n\t\tmargin: 0 auto;\n\t\tborder-radius: var(--radius-lg);\n\t\toverflow: hidden;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tmax-width: var(--size-40);\n\t\tmax-height: var(--size-20);\n\t\tobject-fit: cover;\n\t}\n\n\t.container.gallery {\n\t\twidth: 100%;\n\t\tmax-width: 100%;\n\t\tobject-fit: cover;\n\t\tmax-width: var(--size-40);\n\t\tmax-height: var(--size-20);\n\t\toverflow: hidden;\n\t}\n</style>\n"], "names": ["ctx", "create_if_block", "toggle_class", "div", "insert_hydration", "target", "anchor", "show_indices", "label_height", "value", "$$props", "theme_mode", "type", "selected"], "mappings": "yfAqBS,6DAIO,oBACK,6PARfA,EAAK,CAAA,GAAAC,EAAAD,CAAA,+<PERSON><PERSON><PERSON>,EAAAC,EAAA,QAAAH,OAAS,OAAO,EACdE,EAAAC,EAAA,UAAAH,OAAS,SAAS,oCAEnBA,EAAK,CAAA,CAAA,UALpBI,EAkBKC,EAAAF,EAAAG,CAAA,gCAXCN,EAAK,CAAA,2GALGE,EAAAC,EAAA,QAAAH,OAAS,OAAO,aACdE,EAAAC,EAAA,UAAAH,OAAS,SAAS,0DAEnBA,EAAK,CAAA,CAAA,oEAXfO,EAAe,GACfC,EAAe,oBAHR,GAAA,CAAA,MAAAC,CAAA,EAAAC,GACA,WAAAC,EAA0C,QAAA,EAAAD,EAG1C,CAAA,KAAAE,CAAA,EAAAF,GACA,SAAAG,EAAW,EAAA,EAAAH"}