import{SvelteComponent as y,init as T,safe_not_equal as E,element as _,space as w,text as C,claim_element as k,children as g,claim_space as A,claim_text as L,detach as m,attr as h,toggle_class as p,insert_hydration as N,append_hydration as f,listen as b,set_data as P,noop as v,run_all as S,create<PERSON>ventDispatcher as q}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function B(c){let t,e,d,a,i,r,u;return{c(){t=_("label"),e=_("input"),d=w(),a=_("span"),i=C(c[1]),this.h()},l(s){t=k(s,"LABEL",{class:!0});var l=g(t);e=k(l,"INPUT",{type:!0,name:!0,"data-testid":!0,class:!0}),d=A(l),a=k(l,"SPAN",{class:!0});var o=g(a);i=L(o,c[1]),o.forEach(m),l.forEach(m),this.h()},h(){e.disabled=c[2],h(e,"type","checkbox"),h(e,"name","test"),h(e,"data-testid","checkbox"),h(e,"class","svelte-1a15wmk"),h(a,"class","svelte-1a15wmk"),h(t,"class","svelte-1a15wmk"),p(t,"disabled",c[2])},m(s,l){N(s,t,l),f(t,e),e.checked=c[0],f(t,d),f(t,a),f(a,i),r||(u=[b(e,"change",c[6]),b(e,"keydown",c[3]),b(e,"input",c[4])],r=!0)},p(s,[l]){l&4&&(e.disabled=s[2]),l&1&&(e.checked=s[0]),l&2&&P(i,s[1]),l&4&&p(t,"disabled",s[2])},i:v,o:v,d(s){s&&m(t),r=!1,S(u)}}}function D(c,t,e){let d,{value:a=!1}=t,{label:i="Checkbox"}=t,{interactive:r}=t;const u=q();async function s(n){n.key==="Enter"&&(e(0,a=!a),u("select",{index:0,value:n.currentTarget.checked,selected:n.currentTarget.checked}))}async function l(n){e(0,a=n.currentTarget.checked),u("select",{index:0,value:n.currentTarget.checked,selected:n.currentTarget.checked})}function o(){a=this.checked,e(0,a)}return c.$$set=n=>{"value"in n&&e(0,a=n.value),"label"in n&&e(1,i=n.label),"interactive"in n&&e(5,r=n.interactive)},c.$$.update=()=>{c.$$.dirty&1&&u("change",a),c.$$.dirty&32&&e(2,d=!r)},[a,i,d,s,l,r,o]}class j extends y{constructor(t){super(),T(this,t,D,B,E,{value:0,label:1,interactive:5})}}export{j as C};
//# sourceMappingURL=Checkbox.BBJAZbOw.js.map
