import{SvelteComponent as pe,init as ve,safe_not_equal as we,svg_element as de,claim_svg_element as me,children as B,detach as b,attr as g,insert_hydration as V,append_hydration as z,noop as Z,element as N,create_component as R,space as A,text as oe,claim_element as C,claim_component as P,claim_space as F,claim_text as se,set_style as ge,mount_component as L,listen as re,transition_in as w,transition_out as E,destroy_component as q,createEventDispatcher as We,toggle_class as Y,src_url_equal as Se,group_outros as x,check_outros as $,onMount as et,onDestroy as tt,binding_callbacks as ae,empty as ee,add_render_callback as lt,create_in_transition as it,stop_propagation as nt,action_destroyer as rt,run_all as Qe,ensure_array_like as Ue,destroy_each as at,set_input_value as Oe,set_data as ke,bind as he,add_flush_callback as be,tick as Ee,bubble as _e,create_slot as ot,update_slot_base as st,get_all_dirty_from_scope as ut,get_slot_changes as ct}from"../../../svelte/svelte.js";import{fade as ft}from"../../../svelte/svelte-submodules.js";import{A as _t,Q as dt,I as mt,C as gt,r as ht}from"./2.B2AoQPnG.js";import{B as bt}from"./BlockLabel.BTSz9r5s.js";import{I as pt}from"./Image.CTVzPhL7.js";import{W as vt,a as wt,S as kt}from"./SelectSource.CTC8Kkgx.js";import{I as It}from"./IconButtonWrapper.D5aGR59h.js";import{F as Et}from"./FullscreenButton.g_8wwg6y.js";import{g as yt}from"./utils.Gtzs_Zla.js";import{D as Je}from"./DropdownArrow.pfrcUdj1.js";import{S as Dt}from"./StreamingBar.BOfzkLQo.js";import{a as zt}from"./Upload.yOHVlgUe.js";function Tt(n){let e,t,l;return{c(){e=de("svg"),t=de("path"),l=de("circle"),this.h()},l(i){e=me(i,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0,class:!0});var a=B(e);t=me(a,"path",{d:!0}),B(t).forEach(b),l=me(a,"circle",{cx:!0,cy:!0,r:!0}),B(l).forEach(b),a.forEach(b),this.h()},h(){g(t,"d","M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"),g(l,"cx","12"),g(l,"cy","13"),g(l,"r","4"),g(e,"xmlns","http://www.w3.org/2000/svg"),g(e,"width","100%"),g(e,"height","100%"),g(e,"viewBox","0 0 24 24"),g(e,"fill","none"),g(e,"stroke","currentColor"),g(e,"stroke-width","1.5"),g(e,"stroke-linecap","round"),g(e,"stroke-linejoin","round"),g(e,"class","feather feather-camera")},m(i,a){V(i,e,a),z(e,t),z(e,l)},p:Z,i:Z,o:Z,d(i){i&&b(e)}}}class Bt extends pe{constructor(e){super(),ve(this,e,null,Tt,we,{})}}function Vt(n){let e,t;return{c(){e=de("svg"),t=de("circle"),this.h()},l(l){e=me(l,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0,class:!0});var i=B(e);t=me(i,"circle",{cx:!0,cy:!0,r:!0}),B(t).forEach(b),i.forEach(b),this.h()},h(){g(t,"cx","12"),g(t,"cy","12"),g(t,"r","10"),g(e,"xmlns","http://www.w3.org/2000/svg"),g(e,"width","100%"),g(e,"height","100%"),g(e,"viewBox","0 0 24 24"),g(e,"stroke-width","1.5"),g(e,"stroke-linecap","round"),g(e,"stroke-linejoin","round"),g(e,"class","feather feather-circle")},m(l,i){V(l,e,i),z(e,t)},p:Z,i:Z,o:Z,d(l){l&&b(e)}}}class Nt extends pe{constructor(e){super(),ve(this,e,null,Vt,we,{})}}function Ct(n){let e,t,l,i,a,r="Click to Access Webcam",s,o,c,f;return i=new vt({}),{c(){e=N("button"),t=N("div"),l=N("span"),R(i.$$.fragment),a=A(),s=oe(r),this.h()},l(_){e=C(_,"BUTTON",{class:!0});var k=B(e);t=C(k,"DIV",{class:!0});var y=B(t);l=C(y,"SPAN",{class:!0});var M=B(l);P(i.$$.fragment,M),M.forEach(b),a=F(y),s=se(y,r),y.forEach(b),k.forEach(b),this.h()},h(){g(l,"class","icon-wrap svelte-qbrfs"),g(t,"class","wrap svelte-qbrfs"),g(e,"class","svelte-qbrfs"),ge(e,"height","100%")},m(_,k){V(_,e,k),z(e,t),z(t,l),L(i,l,null),z(t,a),z(t,s),o=!0,c||(f=re(e,"click",n[1]),c=!0)},p:Z,i(_){o||(w(i.$$.fragment,_),o=!0)},o(_){E(i.$$.fragment,_),o=!1},d(_){_&&b(e),q(i),c=!1,f()}}}function Ot(n){const e=We();return[e,()=>e("click")]}class Wt extends pe{constructor(e){super(),ve(this,e,Ot,Ct,we,{})}}function Mt(){return navigator.mediaDevices.enumerateDevices()}function St(n,e){e.srcObject=n,e.muted=!0,e.play()}async function je(n,e,t,l){const i={video:l?{deviceId:{exact:l},...t==null?void 0:t.video}:(t==null?void 0:t.video)||{width:{ideal:1920},height:{ideal:1440}},audio:n&&((t==null?void 0:t.audio)??!0)};return navigator.mediaDevices.getUserMedia(i).then(a=>(St(a,e),a))}function Ut(n){return n.filter(t=>t.kind==="videoinput")}function Re(n,e,t){const l=n.slice();return l[39]=e[t],l}function jt(n){let e,t,l,i,a,r,s,o,c,f,_;const k=[Lt,Pt],y=[];function M(I,D){return I[2]==="video"||I[1]?0:1}l=M(n),i=y[l]=k[l](n);let m=!n[11]&&Pe(n),p=n[13]&&n[8]&&Le(n);return{c(){e=N("div"),t=N("button"),i.c(),r=A(),m&&m.c(),s=A(),p&&p.c(),o=ee(),this.h()},l(I){e=C(I,"DIV",{class:!0});var D=B(e);t=C(D,"BUTTON",{"aria-label":!0,class:!0});var S=B(t);i.l(S),S.forEach(b),r=F(D),m&&m.l(D),D.forEach(b),s=F(I),p&&p.l(I),o=ee(),this.h()},h(){g(t,"aria-label",a=n[2]==="image"?"capture photo":"start recording"),g(t,"class","svelte-10cpz3p"),g(e,"class","button-wrap svelte-10cpz3p")},m(I,D){V(I,e,D),z(e,t),y[l].m(t,null),z(e,r),m&&m.m(e,null),V(I,s,D),p&&p.m(I,D),V(I,o,D),c=!0,f||(_=re(t,"click",n[28]),f=!0)},p(I,D){let S=l;l=M(I),l===S?y[l].p(I,D):(x(),E(y[S],1,1,()=>{y[S]=null}),$(),i=y[l],i?i.p(I,D):(i=y[l]=k[l](I),i.c()),w(i,1),i.m(t,null)),(!c||D[0]&4&&a!==(a=I[2]==="image"?"capture photo":"start recording"))&&g(t,"aria-label",a),I[11]?m&&(x(),E(m,1,1,()=>{m=null}),$()):m?(m.p(I,D),D[0]&2048&&w(m,1)):(m=Pe(I),m.c(),w(m,1),m.m(e,null)),I[13]&&I[8]?p?(p.p(I,D),D[0]&8448&&w(p,1)):(p=Le(I),p.c(),w(p,1),p.m(o.parentNode,o)):p&&(x(),E(p,1,1,()=>{p=null}),$())},i(I){c||(w(i),w(m),w(p),c=!0)},o(I){E(i),E(m),E(p),c=!1},d(I){I&&(b(e),b(s),b(o)),y[l].d(),m&&m.d(),p&&p.d(I),f=!1,_()}}}function Rt(n){let e,t,l,i;return t=new Wt({}),t.$on("click",n[27]),{c(){e=N("div"),R(t.$$.fragment),this.h()},l(a){e=C(a,"DIV",{title:!0,style:!0});var r=B(e);P(t.$$.fragment,r),r.forEach(b),this.h()},h(){g(e,"title","grant webcam access"),ge(e,"height","100%")},m(a,r){V(a,e,r),L(t,e,null),i=!0},p:Z,i(a){i||(w(t.$$.fragment,a),a&&(l||lt(()=>{l=it(e,ft,{delay:100,duration:200}),l.start()})),i=!0)},o(a){E(t.$$.fragment,a),i=!1},d(a){a&&b(e),q(t)}}}function Pt(n){let e,t,l;return t=new Bt({}),{c(){e=N("div"),R(t.$$.fragment),this.h()},l(i){e=C(i,"DIV",{class:!0,title:!0});var a=B(e);P(t.$$.fragment,a),a.forEach(b),this.h()},h(){g(e,"class","icon svelte-10cpz3p"),g(e,"title","capture photo")},m(i,a){V(i,e,a),L(t,e,null),l=!0},p:Z,i(i){l||(w(t.$$.fragment,i),l=!0)},o(i){E(t.$$.fragment,i),l=!1},d(i){i&&b(e),q(t)}}}function Lt(n){let e,t,l,i;const a=[Ft,At,qt],r=[];function s(o,c){return o[1]&&o[10]==="waiting"?0:o[1]&&o[10]==="open"||!o[1]&&o[11]?1:2}return e=s(n),t=r[e]=a[e](n),{c(){t.c(),l=ee()},l(o){t.l(o),l=ee()},m(o,c){r[e].m(o,c),V(o,l,c),i=!0},p(o,c){let f=e;e=s(o),e===f?r[e].p(o,c):(x(),E(r[f],1,1,()=>{r[f]=null}),$(),t=r[e],t?t.p(o,c):(t=r[e]=a[e](o),t.c()),w(t,1),t.m(l.parentNode,l))},i(o){i||(w(t),i=!0)},o(o){E(t),i=!1},d(o){o&&b(l),r[e].d(o)}}}function qt(n){let e,t,l,i,a=n[4]("audio.record")+"",r,s;return l=new Nt({}),{c(){e=N("div"),t=N("div"),R(l.$$.fragment),i=A(),r=oe(a),this.h()},l(o){e=C(o,"DIV",{class:!0});var c=B(e);t=C(c,"DIV",{class:!0,title:!0});var f=B(t);P(l.$$.fragment,f),f.forEach(b),i=F(c),r=se(c,a),c.forEach(b),this.h()},h(){g(t,"class","icon color-primary svelte-10cpz3p"),g(t,"title","start recording"),g(e,"class","icon-with-text svelte-10cpz3p")},m(o,c){V(o,e,c),z(e,t),L(l,t,null),z(e,i),z(e,r),s=!0},p(o,c){(!s||c[0]&16)&&a!==(a=o[4]("audio.record")+"")&&ke(r,a)},i(o){s||(w(l.$$.fragment,o),s=!0)},o(o){E(l.$$.fragment,o),s=!1},d(o){o&&b(e),q(l)}}}function At(n){let e,t,l,i,a=n[4]("audio.stop")+"",r,s;return l=new dt({}),{c(){e=N("div"),t=N("div"),R(l.$$.fragment),i=A(),r=oe(a),this.h()},l(o){e=C(o,"DIV",{class:!0});var c=B(e);t=C(c,"DIV",{class:!0,title:!0});var f=B(t);P(l.$$.fragment,f),f.forEach(b),i=F(c),r=se(c,a),c.forEach(b),this.h()},h(){g(t,"class","icon color-primary svelte-10cpz3p"),g(t,"title","stop recording"),g(e,"class","icon-with-text svelte-10cpz3p")},m(o,c){V(o,e,c),z(e,t),L(l,t,null),z(e,i),z(e,r),s=!0},p(o,c){(!s||c[0]&16)&&a!==(a=o[4]("audio.stop")+"")&&ke(r,a)},i(o){s||(w(l.$$.fragment,o),s=!0)},o(o){E(l.$$.fragment,o),s=!1},d(o){o&&b(e),q(l)}}}function Ft(n){let e,t,l,i,a=n[4]("audio.waiting")+"",r,s;return l=new wt({}),{c(){e=N("div"),t=N("div"),R(l.$$.fragment),i=A(),r=oe(a),this.h()},l(o){e=C(o,"DIV",{class:!0,style:!0});var c=B(e);t=C(c,"DIV",{class:!0,title:!0});var f=B(t);P(l.$$.fragment,f),f.forEach(b),i=F(c),r=se(c,a),c.forEach(b),this.h()},h(){g(t,"class","icon color-primary svelte-10cpz3p"),g(t,"title","spinner"),g(e,"class","icon-with-text svelte-10cpz3p"),ge(e,"width","var(--size-24)")},m(o,c){V(o,e,c),z(e,t),L(l,t,null),z(e,i),z(e,r),s=!0},p(o,c){(!s||c[0]&16)&&a!==(a=o[4]("audio.waiting")+"")&&ke(r,a)},i(o){s||(w(l.$$.fragment,o),s=!0)},o(o){E(l.$$.fragment,o),s=!1},d(o){o&&b(e),q(l)}}}function Pe(n){let e,t,l,i,a;return t=new Je({}),{c(){e=N("button"),R(t.$$.fragment),this.h()},l(r){e=C(r,"BUTTON",{class:!0,"aria-label":!0});var s=B(e);P(t.$$.fragment,s),s.forEach(b),this.h()},h(){g(e,"class","icon svelte-10cpz3p"),g(e,"aria-label","select input source")},m(r,s){V(r,e,s),L(t,e,null),l=!0,i||(a=re(e,"click",n[29]),i=!0)},p:Z,i(r){l||(w(t.$$.fragment,r),l=!0)},o(r){E(t.$$.fragment,r),l=!1},d(r){r&&b(e),q(t),i=!1,a()}}}function Le(n){let e,t,l,i,a,r,s;l=new Je({});function o(_,k){return _[7].length===0?Gt:Ht}let c=o(n),f=c(n);return{c(){e=N("select"),t=N("button"),R(l.$$.fragment),i=A(),f.c(),this.h()},l(_){e=C(_,"SELECT",{class:!0,"aria-label":!0});var k=B(e);t=C(k,"BUTTON",{class:!0});var y=B(t);P(l.$$.fragment,y),i=F(y),y.forEach(b),f.l(k),k.forEach(b),this.h()},h(){g(t,"class","inset-icon svelte-10cpz3p"),g(e,"class","select-wrap svelte-10cpz3p"),g(e,"aria-label","select source")},m(_,k){V(_,e,k),z(e,t),L(l,t,null),z(t,i),f.m(e,null),a=!0,r||(s=[re(t,"click",nt(n[30])),rt(Me.call(null,e,n[17])),re(e,"change",n[14])],r=!0)},p(_,k){c===(c=o(_))&&f?f.p(_,k):(f.d(1),f=c(_),f&&(f.c(),f.m(e,null)))},i(_){a||(w(l.$$.fragment,_),a=!0)},o(_){E(l.$$.fragment,_),a=!1},d(_){_&&b(e),q(l),f.d(),r=!1,Qe(s)}}}function Ht(n){let e,t=Ue(n[7]),l=[];for(let i=0;i<t.length;i+=1)l[i]=qe(Re(n,t,i));return{c(){for(let i=0;i<l.length;i+=1)l[i].c();e=ee()},l(i){for(let a=0;a<l.length;a+=1)l[a].l(i);e=ee()},m(i,a){for(let r=0;r<l.length;r+=1)l[r]&&l[r].m(i,a);V(i,e,a)},p(i,a){if(a[0]&384){t=Ue(i[7]);let r;for(r=0;r<t.length;r+=1){const s=Re(i,t,r);l[r]?l[r].p(s,a):(l[r]=qe(s),l[r].c(),l[r].m(e.parentNode,e))}for(;r<l.length;r+=1)l[r].d(1);l.length=t.length}},d(i){i&&b(e),at(l,i)}}}function Gt(n){let e,t=n[4]("common.no_devices")+"",l;return{c(){e=N("option"),l=oe(t),this.h()},l(i){e=C(i,"OPTION",{class:!0});var a=B(e);l=se(a,t),a.forEach(b),this.h()},h(){e.__value="",Oe(e,e.__value),g(e,"class","svelte-10cpz3p")},m(i,a){V(i,e,a),z(e,l)},p(i,a){a[0]&16&&t!==(t=i[4]("common.no_devices")+"")&&ke(l,t)},d(i){i&&b(e)}}}function qe(n){let e,t=n[39].label+"",l,i,a,r;return{c(){e=N("option"),l=oe(t),i=A(),this.h()},l(s){e=C(s,"OPTION",{class:!0});var o=B(e);l=se(o,t),i=F(o),o.forEach(b),this.h()},h(){e.__value=a=n[39].deviceId,Oe(e,e.__value),e.selected=r=n[8].deviceId===n[39].deviceId,g(e,"class","svelte-10cpz3p")},m(s,o){V(s,e,o),z(e,l),z(e,i)},p(s,o){o[0]&128&&t!==(t=s[39].label+"")&&ke(l,t),o[0]&128&&a!==(a=s[39].deviceId)&&(e.__value=a,Oe(e,e.__value)),o[0]&384&&r!==(r=s[8].deviceId===s[39].deviceId)&&(e.selected=r)},d(s){s&&b(e)}}}function Qt(n){let e,t,l,i,a,r,s,o,c,f,_;t=new Dt({props:{time_limit:n[9]}});const k=[Rt,jt],y=[];function M(m,p){return m[12]?1:0}return c=M(n),f=y[c]=k[c](n),{c(){e=N("div"),R(t.$$.fragment),l=A(),i=N("video"),a=A(),r=N("img"),o=A(),f.c(),this.h()},l(m){e=C(m,"DIV",{class:!0});var p=B(e);P(t.$$.fragment,p),l=F(p),i=C(p,"VIDEO",{class:!0}),B(i).forEach(b),a=F(p),r=C(p,"IMG",{src:!0,class:!0}),o=F(p),f.l(p),p.forEach(b),this.h()},h(){var m;g(i,"class","svelte-10cpz3p"),Y(i,"flip",n[3]),Y(i,"hide",!n[12]||n[12]&&!!n[0]),Se(r.src,s=(m=n[0])==null?void 0:m.url)||g(r,"src",s),g(r,"class","svelte-10cpz3p"),Y(r,"hide",!n[12]||n[12]&&!n[0]),g(e,"class","wrap svelte-10cpz3p")},m(m,p){V(m,e,p),L(t,e,null),z(e,l),z(e,i),n[26](i),z(e,a),z(e,r),z(e,o),y[c].m(e,null),_=!0},p(m,p){var S;const I={};p[0]&512&&(I.time_limit=m[9]),t.$set(I),(!_||p[0]&8)&&Y(i,"flip",m[3]),(!_||p[0]&4097)&&Y(i,"hide",!m[12]||m[12]&&!!m[0]),(!_||p[0]&1&&!Se(r.src,s=(S=m[0])==null?void 0:S.url))&&g(r,"src",s),(!_||p[0]&4097)&&Y(r,"hide",!m[12]||m[12]&&!m[0]);let D=c;c=M(m),c===D?y[c].p(m,p):(x(),E(y[D],1,1,()=>{y[D]=null}),$(),f=y[c],f?f.p(m,p):(f=y[c]=k[c](m),f.c()),w(f,1),f.m(e,null))},i(m){_||(w(t.$$.fragment,m),w(f),_=!0)},o(m){E(t.$$.fragment,m),E(f),_=!1},d(m){m&&b(e),q(t),n[26](null),y[c].d()}}}function Me(n,e){const t=l=>{n&&!n.contains(l.target)&&!l.defaultPrevented&&e(l)};return document.addEventListener("click",t,!0),{destroy(){document.removeEventListener("click",t,!0)}}}function Jt(n,e,t){let l,i=[],a=null,r=null,s="closed";const o=h=>{h==="closed"?(t(9,r=null),t(10,s="closed"),t(0,Q=null)):h==="waiting"?t(10,s="waiting"):t(10,s="open")},c=h=>{v&&t(9,r=h)};let f,{streaming:_=!1}=e,{pending:k=!1}=e,{root:y=""}=e,{stream_every:M=1}=e,{mode:m="image"}=e,{mirror_webcam:p}=e,{include_audio:I}=e,{webcam_constraints:D=null}=e,{i18n:S}=e,{upload:J}=e,{value:Q=null}=e;const W=We();et(()=>{f=document.createElement("canvas"),_&&m==="image"&&window.setInterval(()=>{l&&!k&&d()},M*1e3)});const K=async h=>{const X=h.target.value;await je(I,l,D,X).then(async ie=>{H=ie,t(8,a=i.find(fe=>fe.deviceId===X)||null),t(13,le=!1)})};async function T(){try{je(I,l,D).then(async h=>{t(12,ue=!0),t(7,i=await Mt()),H=h}).then(()=>Ut(i)).then(h=>{t(7,i=h);const U=H.getTracks().map(X=>{var ie;return(ie=X.getSettings())==null?void 0:ie.deviceId})[0];t(8,a=U&&h.find(X=>X.deviceId===U)||i[0])}),(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)&&W("error",S("image.no_webcam_support"))}catch(h){if(h instanceof DOMException&&h.name=="NotAllowedError")W("error",S("image.allow_webcam_access"));else throw h}}function d(){var h=f.getContext("2d");if((!_||_&&v)&&l.videoWidth&&l.videoHeight){if(f.width=l.videoWidth,f.height=l.videoHeight,h.drawImage(l,0,0,l.videoWidth,l.videoHeight),p&&(h.scale(-1,1),h.drawImage(l,-l.videoWidth,0)),_&&(!v||s==="waiting"))return;if(_){const U=f.toDataURL("image/jpeg");W("stream",U);return}f.toBlob(U=>{W(_?"stream":"capture",U)},`image/${_?"jpeg":"png"}`,.8)}}let v=!1,G=[],H,O,j;function te(){if(v){j.stop();let h=new Blob(G,{type:O}),U=new FileReader;U.onload=async function(X){var ie;if(X.target){let fe=new File([h],"sample."+O.substring(6));const Ne=await _t([fe]);let Ce=(ie=await J(Ne,y))==null?void 0:ie.filter(Boolean)[0];W("capture",Ce),W("stop_recording")}},U.readAsDataURL(h)}else if(typeof MediaRecorder<"u"){W("start_recording"),G=[];let h=["video/webm","video/mp4"];for(let U of h)if(MediaRecorder.isTypeSupported(U)){O=U;break}if(O===null){console.error("No supported MediaRecorder mimeType");return}j=new MediaRecorder(H,{mimeType:O}),j.addEventListener("dataavailable",function(U){G.push(U.data)}),j.start(200)}t(11,v=!v)}let ue=!1;function ce({destroy:h}={}){m==="image"&&_&&t(11,v=!v),h||(m==="image"?d():te()),!v&&H&&(W("close_stream"),H.getTracks().forEach(U=>U.stop()),t(6,l.srcObject=null,l),t(12,ue=!1),window.setTimeout(()=>{t(0,Q=null)},500),t(0,Q=null))}let le=!1;function ye(h){h.preventDefault(),h.stopPropagation(),t(13,le=!1)}tt(()=>{typeof window>"u"||(ce({destroy:!0}),H==null||H.getTracks().forEach(h=>h.stop()))});function De(h){ae[h?"unshift":"push"](()=>{l=h,t(6,l)})}const ze=async()=>T(),Te=()=>ce(),Be=()=>t(13,le=!0),Ve=()=>t(13,le=!1);return n.$$set=h=>{"streaming"in h&&t(1,_=h.streaming),"pending"in h&&t(20,k=h.pending),"root"in h&&t(21,y=h.root),"stream_every"in h&&t(22,M=h.stream_every),"mode"in h&&t(2,m=h.mode),"mirror_webcam"in h&&t(3,p=h.mirror_webcam),"include_audio"in h&&t(23,I=h.include_audio),"webcam_constraints"in h&&t(24,D=h.webcam_constraints),"i18n"in h&&t(4,S=h.i18n),"upload"in h&&t(25,J=h.upload),"value"in h&&t(0,Q=h.value)},[Q,_,m,p,S,Me,l,i,a,r,s,v,ue,le,K,T,ce,ye,o,c,k,y,M,I,D,J,De,ze,Te,Be,Ve]}class Kt extends pe{constructor(e){super(),ve(this,e,Jt,Qt,we,{modify_stream:18,set_time_limit:19,streaming:1,pending:20,root:21,stream_every:22,mode:2,mirror_webcam:3,include_audio:23,webcam_constraints:24,i18n:4,upload:25,value:0,click_outside:5},null,[-1,-1])}get modify_stream(){return this.$$.ctx[18]}get set_time_limit(){return this.$$.ctx[19]}get click_outside(){return Me}}const Xt=Kt;function Ae(n){let e,t,l,i=n[18]&&Fe(n);return t=new mt({props:{Icon:gt,label:"Remove Image"}}),t.$on("click",n[34]),{c(){i&&i.c(),e=A(),R(t.$$.fragment)},l(a){i&&i.l(a),e=F(a),P(t.$$.fragment,a)},m(a,r){i&&i.m(a,r),V(a,e,r),L(t,a,r),l=!0},p(a,r){a[18]?i?(i.p(a,r),r[0]&262144&&w(i,1)):(i=Fe(a),i.c(),w(i,1),i.m(e.parentNode,e)):i&&(x(),E(i,1,1,()=>{i=null}),$())},i(a){l||(w(i),w(t.$$.fragment,a),l=!0)},o(a){E(i),E(t.$$.fragment,a),l=!1},d(a){a&&b(e),i&&i.d(a),q(t,a)}}}function Fe(n){let e,t;return e=new Et({props:{fullscreen:n[19]}}),e.$on("fullscreen",n[33]),{c(){R(e.$$.fragment)},l(l){P(e.$$.fragment,l)},m(l,i){L(e,l,i),t=!0},p(l,i){const a={};i[0]&524288&&(a.fullscreen=l[19]),e.$set(a)},i(l){t||(w(e.$$.fragment,l),t=!0)},o(l){E(e.$$.fragment,l),t=!1},d(l){q(e,l)}}}function Yt(n){var i;let e,t,l=((i=n[3])==null?void 0:i.url)&&!n[20]&&Ae(n);return{c(){l&&l.c(),e=ee()},l(a){l&&l.l(a),e=ee()},m(a,r){l&&l.m(a,r),V(a,e,r),t=!0},p(a,r){var s;(s=a[3])!=null&&s.url&&!a[20]?l?(l.p(a,r),r[0]&1048584&&w(l,1)):(l=Ae(a),l.c(),w(l,1),l.m(e.parentNode,e)):l&&(x(),E(l,1,1,()=>{l=null}),$())},i(a){t||(w(l),t=!0)},o(a){E(l),t=!1},d(a){a&&b(e),l&&l.d(a)}}}function He(n){let e;const t=n[32].default,l=ot(t,n,n[49],null);return{c(){l&&l.c()},l(i){l&&l.l(i)},m(i,a){l&&l.m(i,a),e=!0},p(i,a){l&&l.p&&(!e||a[1]&262144)&&st(l,t,i,i[49],e?ct(t,i[49],a,null):ut(i[49]),null)},i(i){e||(w(l,i),e=!0)},o(i){E(l,i),e=!1},d(i){l&&l.d(i)}}}function Zt(n){let e,t,l=n[3]===null&&He(n);return{c(){l&&l.c(),e=ee()},l(i){l&&l.l(i),e=ee()},m(i,a){l&&l.m(i,a),V(i,e,a),t=!0},p(i,a){i[3]===null?l?(l.p(i,a),a[0]&8&&w(l,1)):(l=He(i),l.c(),w(l,1),l.m(e.parentNode,e)):l&&(x(),E(l,1,1,()=>{l=null}),$())},i(i){t||(w(l),t=!0)},o(i){E(l),t=!1},d(i){i&&b(e),l&&l.d(i)}}}function xt(n){let e,t,l,i,a;return t=new ht({props:{src:n[3].url,alt:n[3].alt_text}}),{c(){e=N("div"),R(t.$$.fragment),this.h()},l(r){e=C(r,"DIV",{class:!0});var s=B(e);P(t.$$.fragment,s),s.forEach(b),this.h()},h(){g(e,"class","image-frame svelte-1hdlew6"),Y(e,"selectable",n[11])},m(r,s){V(r,e,s),L(t,e,null),l=!0,i||(a=re(e,"click",n[27]),i=!0)},p(r,s){const o={};s[0]&8&&(o.src=r[3].url),s[0]&8&&(o.alt=r[3].alt_text),t.$set(o),(!l||s[0]&2048)&&Y(e,"selectable",r[11])},i(r){l||(w(t.$$.fragment,r),l=!0)},o(r){E(t.$$.fragment,r),l=!1},d(r){r&&b(e),q(t),i=!1,a()}}}function $t(n){let e,t,l,i;function a(o){n[39](o)}function r(o){n[40](o)}let s={root:n[12],value:n[3],mirror_webcam:n[10].mirror,stream_every:n[17],streaming:n[9],mode:"image",include_audio:!1,i18n:n[13],upload:n[15],webcam_constraints:n[10].constraints};return n[4]!==void 0&&(s.modify_stream=n[4]),n[5]!==void 0&&(s.set_time_limit=n[5]),e=new Xt({props:s}),ae.push(()=>he(e,"modify_stream",a)),ae.push(()=>he(e,"set_time_limit",r)),e.$on("capture",n[41]),e.$on("stream",n[42]),e.$on("error",n[43]),e.$on("drag",n[44]),e.$on("upload",n[45]),e.$on("close_stream",n[46]),{c(){R(e.$$.fragment)},l(o){P(e.$$.fragment,o)},m(o,c){L(e,o,c),i=!0},p(o,c){const f={};c[0]&4096&&(f.root=o[12]),c[0]&8&&(f.value=o[3]),c[0]&1024&&(f.mirror_webcam=o[10].mirror),c[0]&131072&&(f.stream_every=o[17]),c[0]&512&&(f.streaming=o[9]),c[0]&8192&&(f.i18n=o[13]),c[0]&32768&&(f.upload=o[15]),c[0]&1024&&(f.webcam_constraints=o[10].constraints),!t&&c[0]&16&&(t=!0,f.modify_stream=o[4],be(()=>t=!1)),!l&&c[0]&32&&(l=!0,f.set_time_limit=o[5],be(()=>l=!1)),e.$set(f)},i(o){i||(w(e.$$.fragment,o),i=!0)},o(o){E(e.$$.fragment,o),i=!1},d(o){q(e,o)}}}function Ge(n){let e,t,l;function i(r){n[47](r)}let a={sources:n[8],handle_clear:n[24],handle_select:n[28]};return n[1]!==void 0&&(a.active_source=n[1]),e=new kt({props:a}),ae.push(()=>he(e,"active_source",i)),{c(){R(e.$$.fragment)},l(r){P(e.$$.fragment,r)},m(r,s){L(e,r,s),l=!0},p(r,s){const o={};s[0]&256&&(o.sources=r[8]),!t&&s[0]&2&&(t=!0,o.active_source=r[1],be(()=>t=!1)),e.$set(o)},i(r){l||(w(e.$$.fragment,r),l=!0)},o(r){E(e.$$.fragment,r),l=!1},d(r){q(e,r)}}}function el(n){let e,t,l,i,a,r,s,o,c,f,_,k,y,M=n[8].length>1||n[8].includes("clipboard"),m,p,I;e=new bt({props:{show_label:n[7],Icon:pt,label:n[6]||"Image"}}),i=new It({props:{$$slots:{default:[Yt]},$$scope:{ctx:n}}});function D(d){n[36](d)}function S(d){n[37](d)}let J={hidden:n[3]!==null||n[1]==="webcam",filetype:n[1]==="clipboard"?"clipboard":"image/*",root:n[12],max_file_size:n[14],disable_click:!n[8].includes("upload")||n[3]!==null,upload:n[15],stream_handler:n[16],aria_label:n[13]("image.drop_to_upload"),$$slots:{default:[Zt]},$$scope:{ctx:n}};n[0]!==void 0&&(J.uploading=n[0]),n[2]!==void 0&&(J.dragging=n[2]),s=new zt({props:J}),n[35](s),ae.push(()=>he(s,"uploading",D)),ae.push(()=>he(s,"dragging",S)),s.$on("load",n[23]),s.$on("error",n[38]);const Q=[$t,xt],W=[];function K(d,v){return d[1]==="webcam"&&(d[9]||!d[9]&&!d[3])?0:d[3]!==null&&!d[9]?1:-1}~(_=K(n))&&(k=W[_]=Q[_](n));let T=M&&Ge(n);return{c(){R(e.$$.fragment),t=A(),l=N("div"),R(i.$$.fragment),a=A(),r=N("div"),R(s.$$.fragment),f=A(),k&&k.c(),y=A(),T&&T.c(),this.h()},l(d){P(e.$$.fragment,d),t=F(d),l=C(d,"DIV",{"data-testid":!0,class:!0});var v=B(l);P(i.$$.fragment,v),a=F(v),r=C(v,"DIV",{class:!0});var G=B(r);P(s.$$.fragment,G),f=F(G),k&&k.l(G),G.forEach(b),y=F(v),T&&T.l(v),v.forEach(b),this.h()},h(){g(r,"class","upload-container svelte-1hdlew6"),Y(r,"reduced-height",n[8].length>1),ge(r,"width",n[3]?"auto":"100%"),g(l,"data-testid","image"),g(l,"class","image-container svelte-1hdlew6")},m(d,v){L(e,d,v),V(d,t,v),V(d,l,v),L(i,l,null),z(l,a),z(l,r),L(s,r,null),z(r,f),~_&&W[_].m(r,null),z(l,y),T&&T.m(l,null),n[48](l),m=!0,p||(I=[re(r,"dragover",n[29]),re(r,"drop",n[30])],p=!0)},p(d,v){const G={};v[0]&128&&(G.show_label=d[7]),v[0]&64&&(G.label=d[6]||"Image"),e.$set(G);const H={};v[0]&1835016|v[1]&262144&&(H.$$scope={dirty:v,ctx:d}),i.$set(H);const O={};v[0]&10&&(O.hidden=d[3]!==null||d[1]==="webcam"),v[0]&2&&(O.filetype=d[1]==="clipboard"?"clipboard":"image/*"),v[0]&4096&&(O.root=d[12]),v[0]&16384&&(O.max_file_size=d[14]),v[0]&264&&(O.disable_click=!d[8].includes("upload")||d[3]!==null),v[0]&32768&&(O.upload=d[15]),v[0]&65536&&(O.stream_handler=d[16]),v[0]&8192&&(O.aria_label=d[13]("image.drop_to_upload")),v[0]&8|v[1]&262144&&(O.$$scope={dirty:v,ctx:d}),!o&&v[0]&1&&(o=!0,O.uploading=d[0],be(()=>o=!1)),!c&&v[0]&4&&(c=!0,O.dragging=d[2],be(()=>c=!1)),s.$set(O);let j=_;_=K(d),_===j?~_&&W[_].p(d,v):(k&&(x(),E(W[j],1,1,()=>{W[j]=null}),$()),~_?(k=W[_],k?k.p(d,v):(k=W[_]=Q[_](d),k.c()),w(k,1),k.m(r,null)):k=null),(!m||v[0]&256)&&Y(r,"reduced-height",d[8].length>1),v[0]&8&&ge(r,"width",d[3]?"auto":"100%"),v[0]&256&&(M=d[8].length>1||d[8].includes("clipboard")),M?T?(T.p(d,v),v[0]&256&&w(T,1)):(T=Ge(d),T.c(),w(T,1),T.m(l,null)):T&&(x(),E(T,1,1,()=>{T=null}),$())},i(d){m||(w(e.$$.fragment,d),w(i.$$.fragment,d),w(s.$$.fragment,d),w(k),w(T),m=!0)},o(d){E(e.$$.fragment,d),E(i.$$.fragment,d),E(s.$$.fragment,d),E(k),E(T),m=!1},d(d){d&&(b(t),b(l)),q(e,d),q(i),n[35](null),q(s),~_&&W[_].d(),T&&T.d(),n[48](null),p=!1,Qe(I)}}}function tl(n,e,t){let l,{$$slots:i={},$$scope:a}=e,{value:r=null}=e,{label:s=void 0}=e,{show_label:o}=e,{sources:c=["upload","clipboard","webcam"]}=e,{streaming:f=!1}=e,{pending:_=!1}=e,{webcam_options:k}=e,{selectable:y=!1}=e,{root:M}=e,{i18n:m}=e,{max_file_size:p=null}=e,{upload:I}=e,{stream_handler:D}=e,{stream_every:S}=e,{modify_stream:J}=e,{set_time_limit:Q}=e,{show_fullscreen_button:W=!0}=e,K,{uploading:T=!1}=e,{active_source:d=null}=e,{fullscreen:v=!1}=e;async function G({detail:u}){var ne;if(!f){if((ne=u.path)!=null&&ne.toLowerCase().endsWith(".svg")&&u.url){const $e=await(await fetch(u.url)).text();t(3,r={...u,url:`data:image/svg+xml,${encodeURIComponent($e)}`})}else t(3,r=u);await Ee(),j("upload")}}function H(){t(3,r=null),j("clear"),j("change",null)}async function O(u,ne){if(ne==="stream"){j("stream",{value:{url:u},is_value_data:!0});return}t(31,_=!0);const Ie=await K.load_files([new File([u],`image/${f?"jpeg":"png"}`)]);(ne==="change"||ne==="upload")&&(t(3,r=(Ie==null?void 0:Ie[0])||null),await Ee(),j("change")),t(31,_=!1)}const j=We();let{dragging:te=!1}=e;function ue(u){let ne=yt(u);ne&&j("select",{index:ne,value:null})}async function ce(u){switch(u){case"clipboard":K.paste_clipboard();break}}let le;function ye(u){u.preventDefault(),u.stopPropagation(),u.dataTransfer&&(u.dataTransfer.dropEffect="copy"),t(2,te=!0)}async function De(u){u.preventDefault(),u.stopPropagation(),t(2,te=!1),r&&(H(),await Ee()),t(1,d="upload"),await Ee(),K.load_files_from_drop(u)}function ze(u){_e.call(this,n,u)}const Te=u=>{t(3,r=null),j("clear"),u.stopPropagation()};function Be(u){ae[u?"unshift":"push"](()=>{K=u,t(21,K)})}function Ve(u){T=u,t(0,T)}function h(u){te=u,t(2,te)}function U(u){_e.call(this,n,u)}function X(u){J=u,t(4,J)}function ie(u){Q=u,t(5,Q)}const fe=u=>O(u.detail,"change"),Ne=u=>O(u.detail,"stream");function Ce(u){_e.call(this,n,u)}function Ke(u){_e.call(this,n,u)}const Xe=u=>O(u.detail,"upload");function Ye(u){_e.call(this,n,u)}function Ze(u){d=u,t(1,d),t(8,c)}function xe(u){ae[u?"unshift":"push"](()=>{le=u,t(22,le)})}return n.$$set=u=>{"value"in u&&t(3,r=u.value),"label"in u&&t(6,s=u.label),"show_label"in u&&t(7,o=u.show_label),"sources"in u&&t(8,c=u.sources),"streaming"in u&&t(9,f=u.streaming),"pending"in u&&t(31,_=u.pending),"webcam_options"in u&&t(10,k=u.webcam_options),"selectable"in u&&t(11,y=u.selectable),"root"in u&&t(12,M=u.root),"i18n"in u&&t(13,m=u.i18n),"max_file_size"in u&&t(14,p=u.max_file_size),"upload"in u&&t(15,I=u.upload),"stream_handler"in u&&t(16,D=u.stream_handler),"stream_every"in u&&t(17,S=u.stream_every),"modify_stream"in u&&t(4,J=u.modify_stream),"set_time_limit"in u&&t(5,Q=u.set_time_limit),"show_fullscreen_button"in u&&t(18,W=u.show_fullscreen_button),"uploading"in u&&t(0,T=u.uploading),"active_source"in u&&t(1,d=u.active_source),"fullscreen"in u&&t(19,v=u.fullscreen),"dragging"in u&&t(2,te=u.dragging),"$$scope"in u&&t(49,a=u.$$scope)},n.$$.update=()=>{n.$$.dirty[0]&258&&!d&&c&&t(1,d=c[0]),n.$$.dirty[0]&514&&t(20,l=f&&d==="webcam"),n.$$.dirty[0]&1048577&&T&&!l&&t(3,r=null),n.$$.dirty[0]&4&&j("drag",te)},[T,d,te,r,J,Q,s,o,c,f,k,y,M,m,p,I,D,S,W,v,l,K,le,G,H,O,j,ue,ce,ye,De,_,i,ze,Te,Be,Ve,h,U,X,ie,fe,Ne,Ce,Ke,Xe,Ye,Ze,xe,a]}class ll extends pe{constructor(e){super(),ve(this,e,tl,el,we,{value:3,label:6,show_label:7,sources:8,streaming:9,pending:31,webcam_options:10,selectable:11,root:12,i18n:13,max_file_size:14,upload:15,stream_handler:16,stream_every:17,modify_stream:4,set_time_limit:5,show_fullscreen_button:18,uploading:0,active_source:1,fullscreen:19,dragging:2},null,[-1,-1])}}const gl=ll;export{Nt as C,gl as I,Xt as W};
//# sourceMappingURL=ImageUploader.Dvb2Mtrn.js.map
