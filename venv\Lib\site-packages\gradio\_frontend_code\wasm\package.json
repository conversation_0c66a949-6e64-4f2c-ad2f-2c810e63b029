{"name": "@gradio/wasm", "version": "0.18.1", "description": "Gradio Wasm package", "type": "module", "main": "./dist/src/index.js", "exports": {".": {"gradio": "./src/index.ts", "import": "./dist/src/index.js", "types": "./dist/src/index.d.ts"}, "./package.json": "./package.json", "./svelte": {"gradio": "./svelte/index.ts", "svelte": "./dist/svelte/index.js", "types": "./dist/svelte/index.d.ts"}, "./network": {"import": "./network/index.ts", "types": "./dist/network/index.d.ts"}, "./webworker": {"import": "./webworker/webworker.js", "types": "./webworker/webworker.d.ts"}}, "keywords": [], "author": "", "license": "ISC", "main_changeset": true, "scripts": {"dev:client": "tsc -w --incremental", "dev:worker": "vite build --config vite.worker.config.js --watch --emptyOutDir=false", "dev": "run-p dev:*", "build:client": "svelte-package --input=. --cwd=../../.config/", "build:worker": "vite build --config vite.worker.config.js", "build": "run-s build:client build:worker"}, "dependencies": {"@types/path-browserify": "^1.0.0", "path-browserify": "^1.0.1", "pyodide": "0.27.3"}, "peerDependencies": {"svelte": "^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/gradio-app/gradio.git", "directory": "js/wasm"}}