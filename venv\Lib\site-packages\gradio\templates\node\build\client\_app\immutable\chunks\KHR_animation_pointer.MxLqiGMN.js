import{ap as t,C as Z,h as K,an as W,ao as B}from"./index.BoI39RQH.js";import{S as e,G as D}from"./objectModelMapping.ha_8hIyl.js";import{AnimationPropertyInfo as H}from"./glTFLoaderAnimation.CKms7EUf.js";function c(l,r,a,o){return Z.FromArray(r,a).scale(o)}function V(l,r,a,o){return r[a+3]*o}function n(l,r,a,o){return r[a]*o}function p(l,r,a,o){return-r[a]*o}function A(l,r,a,o){return r[a+1]*o}function G(l,r,a,o){return r[a]*o*2}function i(l){return{scale:[new s(t.ANIMATIONTYPE_FLOAT,`${l}.uScale`,n,()=>2),new s(t.ANIMATIONTYPE_FLOAT,`${l}.vScale`,A,()=>2)],offset:[new s(t.ANIMATIONTYPE_FLOAT,`${l}.uOffset`,n,()=>2),new s(t.ANIMATIONTYPE_FLOAT,`${l}.vOffset`,A,()=>2)],rotation:[new s(t.ANIMATIONTYPE_FLOAT,`${l}.wAng`,p,()=>1)]}}class _ extends H{buildAnimations(r,a,o,m){return[{babylonAnimatable:r._babylonCamera,babylonAnimation:this._buildAnimation(a,o,m)}]}}class s extends H{buildAnimations(r,a,o,m){const x=[];for(const T in r._data)x.push({babylonAnimatable:r._data[T].babylonMaterial,babylonAnimation:this._buildAnimation(a,o,m)});return x}}class u extends H{buildAnimations(r,a,o,m){return[{babylonAnimatable:r._babylonLight,babylonAnimation:this._buildAnimation(a,o,m)}]}}e("/cameras/{}/orthographic/xmag",[new _(t.ANIMATIONTYPE_FLOAT,"orthoLeft",p,()=>1),new _(t.ANIMATIONTYPE_FLOAT,"orthoRight",A,()=>1)]);e("/cameras/{}/orthographic/ymag",[new _(t.ANIMATIONTYPE_FLOAT,"orthoBottom",p,()=>1),new _(t.ANIMATIONTYPE_FLOAT,"orthoTop",A,()=>1)]);e("/cameras/{}/orthographic/zfar",[new _(t.ANIMATIONTYPE_FLOAT,"maxZ",n,()=>1)]);e("/cameras/{}/orthographic/znear",[new _(t.ANIMATIONTYPE_FLOAT,"minZ",n,()=>1)]);e("/cameras/{}/perspective/yfov",[new _(t.ANIMATIONTYPE_FLOAT,"fov",n,()=>1)]);e("/cameras/{}/perspective/zfar",[new _(t.ANIMATIONTYPE_FLOAT,"maxZ",n,()=>1)]);e("/cameras/{}/perspective/znear",[new _(t.ANIMATIONTYPE_FLOAT,"minZ",n,()=>1)]);e("/materials/{}/pbrMetallicRoughness/baseColorFactor",[new s(t.ANIMATIONTYPE_COLOR3,"albedoColor",c,()=>4),new s(t.ANIMATIONTYPE_FLOAT,"alpha",V,()=>4)]);e("/materials/{}/pbrMetallicRoughness/metallicFactor",[new s(t.ANIMATIONTYPE_FLOAT,"metallic",n,()=>1)]);e("/materials/{}/pbrMetallicRoughness/metallicFactor",[new s(t.ANIMATIONTYPE_FLOAT,"roughness",n,()=>1)]);const O=i("albedoTexture");e("/materials/{}/pbrMetallicRoughness/baseColorTexture/extensions/KHR_texture_transform/scale",O.scale);e("/materials/{}/pbrMetallicRoughness/baseColorTexture/extensions/KHR_texture_transform/offset",O.offset);e("/materials/{}/pbrMetallicRoughness/baseColorTexture/extensions/KHR_texture_transform/rotation",O.rotation);const N=i("metallicTexture");e("//materials/{}/pbrMetallicRoughness/metallicRoughnessTexture/scale",N.scale);e("//materials/{}/pbrMetallicRoughness/metallicRoughnessTexture/offset",N.offset);e("//materials/{}/pbrMetallicRoughness/metallicRoughnessTexture/rotation",N.rotation);e("/materials/{}/emissiveFactor",[new s(t.ANIMATIONTYPE_COLOR3,"emissiveColor",c,()=>3)]);const g=i("bumpTexture");e("/materials/{}/normalTexture/scale",[new s(t.ANIMATIONTYPE_FLOAT,"bumpTexture.level",n,()=>1)]);e("/materials/{}/normalTexture/extensions/KHR_texture_transform/scale",g.scale);e("/materials/{}/normalTexture/extensions/KHR_texture_transform/offset",g.offset);e("/materials/{}/normalTexture/extensions/KHR_texture_transform/rotation",g.rotation);e("/materials/{}/occlusionTexture/strength",[new s(t.ANIMATIONTYPE_FLOAT,"ambientTextureStrength",n,()=>1)]);const d=i("ambientTexture");e("/materials/{}/occlusionTexture/extensions/KHR_texture_transform/scale",d.scale);e("/materials/{}/occlusionTexture/extensions/KHR_texture_transform/offset",d.offset);e("/materials/{}/occlusionTexture/extensions/KHR_texture_transform/rotation",d.rotation);const b=i("emissiveTexture");e("/materials/{}/emissiveTexture/extensions/KHR_texture_transform/scale",b.scale);e("/materials/{}/emissiveTexture/extensions/KHR_texture_transform/offset",b.offset);e("/materials/{}/emissiveTexture/extensions/KHR_texture_transform/rotation",b.rotation);e("/materials/{}/extensions/KHR_materials_anisotropy/anisotropyStrength",[new s(t.ANIMATIONTYPE_FLOAT,"anisotropy.intensity",n,()=>1)]);e("/materials/{}/extensions/KHR_materials_anisotropy/anisotropyRotation",[new s(t.ANIMATIONTYPE_FLOAT,"anisotropy.angle",n,()=>1)]);const F=i("anisotropy.texture");e("/materials/{}/extensions/KHR_materials_anisotropy/anisotropyTexture/extensions/KHR_texture_transform/scale",F.scale);e("/materials/{}/extensions/KHR_materials_anisotropy/anisotropyTexture/extensions/KHR_texture_transform/offset",F.offset);e("/materials/{}/extensions/KHR_materials_anisotropy/anisotropyTexture/extensions/KHR_texture_transform/rotation",F.rotation);e("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatFactor",[new s(t.ANIMATIONTYPE_FLOAT,"clearCoat.intensity",n,()=>1)]);e("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatRoughnessFactor",[new s(t.ANIMATIONTYPE_FLOAT,"clearCoat.roughness",n,()=>1)]);const M=i("clearCoat.texture");e("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatTexture/extensions/KHR_texture_transform/scale",M.scale);e("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatTexture/extensions/KHR_texture_transform/offset",M.offset);e("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatTexture/extensions/KHR_texture_transform/rotation",M.rotation);const w=i("clearCoat.bumpTexture");e("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatNormalTexture/scale",[new s(t.ANIMATIONTYPE_FLOAT,"clearCoat.bumpTexture.level",n,()=>1)]);e("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatNormalTexture/extensions/KHR_texture_transform/scale",w.scale);e("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatNormalTexture/extensions/KHR_texture_transform/offset",w.offset);e("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatNormalTexture/extensions/KHR_texture_transform/rotation",w.rotation);const E=i("clearCoat.textureRoughness");e("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatRoughnessTexture/extensions/KHR_texture_transform/scale",E.scale);e("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatRoughnessTexture/extensions/KHR_texture_transform/offset",E.offset);e("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatRoughnessTexture/extensions/KHR_texture_transform/rotation",E.rotation);e("/materials/{}/extensions/KHR_materials_dispersion/dispersionFactor",[new s(t.ANIMATIONTYPE_FLOAT,"subSurface.dispersion",n,()=>1)]);e("/materials/{}/extensions/KHR_materials_emissive_strength/emissiveStrength",[new s(t.ANIMATIONTYPE_FLOAT,"emissiveIntensity",n,()=>1)]);e("/materials/{}/extensions/KHR_materials_ior/ior",[new s(t.ANIMATIONTYPE_FLOAT,"indexOfRefraction",n,()=>1)]);e("/materials/{}/extensions/KHR_materials_iridescence/iridescenceFactor",[new s(t.ANIMATIONTYPE_FLOAT,"iridescence.intensity",n,()=>1)]);e("/materials/{}/extensions/KHR_materials_iridescence/iridescenceIor",[new s(t.ANIMATIONTYPE_FLOAT,"iridescence.indexOfRefraction",n,()=>1)]);e("/materials/{}/extensions/KHR_materials_iridescence/iridescenceThicknessMinimum",[new s(t.ANIMATIONTYPE_FLOAT,"iridescence.minimumThickness",n,()=>1)]);e("/materials/{}/extensions/KHR_materials_iridescence/iridescenceThicknessMaximum",[new s(t.ANIMATIONTYPE_FLOAT,"iridescence.maximumThickness",n,()=>1)]);const C=i("iridescence.texture");e("/materials/{}/extensions/KHR_materials_iridescence/iridescenceTexture/extensions/KHR_texture_transform/scale",C.scale);e("/materials/{}/extensions/KHR_materials_iridescence/iridescenceTexture/extensions/KHR_texture_transform/offset",C.offset);e("/materials/{}/extensions/KHR_materials_iridescence/iridescenceTexture/extensions/KHR_texture_transform/rotation",C.rotation);const L=i("iridescence.thicknessTexture");e("/materials/{}/extensions/KHR_materials_iridescence/iridescenceThicknessTexture/extensions/KHR_texture_transform/scale",L.scale);e("/materials/{}/extensions/KHR_materials_iridescence/iridescenceThicknessTexture/extensions/KHR_texture_transform/offset",L.offset);e("/materials/{}/extensions/KHR_materials_iridescence/iridescenceThicknessTexture/extensions/KHR_texture_transform/rotation",L.rotation);e("/materials/{}/extensions/KHR_materials_sheen/sheenColorFactor",[new s(t.ANIMATIONTYPE_COLOR3,"sheen.color",c,()=>3)]);e("/materials/{}/extensions/KHR_materials_sheen/sheenRoughnessFactor",[new s(t.ANIMATIONTYPE_FLOAT,"sheen.roughness",n,()=>1)]);const P=i("sheen.texture");e("/materials/{}/extensions/KHR_materials_sheen/sheenColorTexture/extensions/KHR_texture_transform/scale",P.scale);e("/materials/{}/extensions/KHR_materials_sheen/sheenColorTexture/extensions/KHR_texture_transform/offset",P.offset);e("/materials/{}/extensions/KHR_materials_sheen/sheenColorTexture/extensions/KHR_texture_transform/rotation",P.rotation);const y=i("sheen.textureRoughness");e("/materials/{}/extensions/KHR_materials_sheen/sheenRoughnessTexture/extensions/KHR_texture_transform/scale",y.scale);e("/materials/{}/extensions/KHR_materials_sheen/sheenRoughnessTexture/extensions/KHR_texture_transform/offset",y.offset);e("/materials/{}/extensions/KHR_materials_sheen/sheenRoughnessTexture/extensions/KHR_texture_transform/rotation",y.rotation);e("/materials/{}/extensions/KHR_materials_specular/specularFactor",[new s(t.ANIMATIONTYPE_FLOAT,"metallicF0Factor",n,()=>1)]);e("/materials/{}/extensions/KHR_materials_specular/specularColorFactor",[new s(t.ANIMATIONTYPE_COLOR3,"metallicReflectanceColor",c,()=>3)]);const Y=i("metallicReflectanceTexture");e("/materials/{}/extensions/KHR_materials_specular/specularTexture/extensions/KHR_texture_transform/scale",Y.scale);e("/materials/{}/extensions/KHR_materials_specular/specularTexture/extensions/KHR_texture_transform/offset",Y.offset);e("/materials/{}/extensions/KHR_materials_specular/specularTexture/extensions/KHR_texture_transform/rotation",Y.rotation);const v=i("reflectanceTexture");e("/materials/{}/extensions/KHR_materials_specular/specularColorTexture/extensions/KHR_texture_transform/scale",v.scale);e("/materials/{}/extensions/KHR_materials_specular/specularColorTexture/extensions/KHR_texture_transform/offset",v.offset);e("/materials/{}/extensions/KHR_materials_specular/specularColorTexture/extensions/KHR_texture_transform/rotation",v.rotation);e("/materials/{}/extensions/KHR_materials_transmission/transmissionFactor",[new s(t.ANIMATIONTYPE_FLOAT,"subSurface.refractionIntensity",n,()=>1)]);const S=i("subSurface.refractionIntensityTexture");e("/materials/{}/extensions/KHR_materials_transmission/transmissionTexture/extensions/KHR_texture_transform/scale",S.scale);e("/materials/{}/extensions/KHR_materials_transmission/transmissionTexture/extensions/KHR_texture_transform/offset",S.offset);e("/materials/{}/extensions/KHR_materials_transmission/transmissionTexture/extensions/KHR_texture_transform/rotation",S.rotation);e("/materials/{}/extensions/KHR_materials_volume/attenuationColor",[new s(t.ANIMATIONTYPE_COLOR3,"subSurface.tintColor",c,()=>3)]);e("/materials/{}/extensions/KHR_materials_volume/attenuationDistance",[new s(t.ANIMATIONTYPE_FLOAT,"subSurface.tintColorAtDistance",n,()=>1)]);e("/materials/{}/extensions/KHR_materials_volume/thicknessFactor",[new s(t.ANIMATIONTYPE_FLOAT,"subSurface.maximumThickness",n,()=>1)]);const $=i("subSurface.thicknessTexture");e("/materials/{}/extensions/KHR_materials_volume/thicknessTexture/extensions/KHR_texture_transform/scale",$.scale);e("/materials/{}/extensions/KHR_materials_volume/thicknessTexture/extensions/KHR_texture_transform/offset",$.offset);e("/materials/{}/extensions/KHR_materials_volume/thicknessTexture/extensions/KHR_texture_transform/rotation",$.rotation);e("/materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionFactor",[new s(t.ANIMATIONTYPE_FLOAT,"subSurface.translucencyIntensity",n,()=>1)]);const k=i("subSurface.translucencyIntensityTexture");e("materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionTexture/extensions/KHR_texture_transform/scale",k.scale);e("materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionTexture/extensions/KHR_texture_transform/offset",k.offset);e("materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionTexture/extensions/KHR_texture_transform/rotation",k.rotation);e("/materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionColorFactor",[new s(t.ANIMATIONTYPE_COLOR3,"subSurface.translucencyColor",c,()=>3)]);const j=i("subSurface.translucencyColorTexture");e("materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionColorTexture/extensions/KHR_texture_transform/scale",j.scale);e("materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionColorTexture/extensions/KHR_texture_transform/offset",j.offset);e("materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionColorTexture/extensions/KHR_texture_transform/rotation",j.rotation);e("/extensions/KHR_lights_punctual/lights/{}/color",[new u(t.ANIMATIONTYPE_COLOR3,"diffuse",c,()=>3)]);e("/extensions/KHR_lights_punctual/lights/{}/intensity",[new u(t.ANIMATIONTYPE_FLOAT,"intensity",n,()=>1)]);e("/extensions/KHR_lights_punctual/lights/{}/range",[new u(t.ANIMATIONTYPE_FLOAT,"range",n,()=>1)]);e("/extensions/KHR_lights_punctual/lights/{}/spot/innerConeAngle",[new u(t.ANIMATIONTYPE_FLOAT,"innerAngle",G,()=>1)]);e("/extensions/KHR_lights_punctual/lights/{}/spot/outerConeAngle",[new u(t.ANIMATIONTYPE_FLOAT,"angle",G,()=>1)]);e("/nodes/{}/extensions/EXT_lights_ies/color",[new u(t.ANIMATIONTYPE_COLOR3,"diffuse",c,()=>3)]);e("/nodes/{}/extensions/EXT_lights_ies/multiplier",[new u(t.ANIMATIONTYPE_FLOAT,"intensity",n,()=>1)]);const R="KHR_animation_pointer";class X{constructor(r){this.name=R,this._loader=r,this._pathToObjectConverter=D(this._loader.gltf)}get enabled(){return this._loader.isExtensionUsed(R)}dispose(){this._loader=null,delete this._pathToObjectConverter}_loadAnimationChannelAsync(r,a,o,m,x){var z;const T=(z=m.target.extensions)==null?void 0:z.KHR_animation_pointer;if(!T||!this._pathToObjectConverter)return null;m.target.path!=="pointer"&&K.Warn(`${r}/target/path: Value (${m.target.path}) must be (pointer) when using the ${this.name} extension`),m.target.node!=null&&K.Warn(`${r}/target/node: Value (${m.target.node}) must not be present when using the ${this.name} extension`);const I=`${r}/extensions/${this.name}`,h=T.pointer;if(!h)throw new Error(`${I}: Pointer is missing`);try{const f=this._pathToObjectConverter.convert(h);if(!f.info.interpolation)throw new Error(`${I}/pointer: Interpolation is missing`);return this._loader._loadAnimationChannelFromTargetInfoAsync(r,a,o,m,{object:f.object,info:f.info.interpolation},x)}catch{return K.Warn(`${I}/pointer: Invalid pointer (${h}) skipped`),null}}}W(R);B(R,!0,l=>new X(l));export{X as KHR_animation_pointer};
//# sourceMappingURL=KHR_animation_pointer.MxLqiGMN.js.map
