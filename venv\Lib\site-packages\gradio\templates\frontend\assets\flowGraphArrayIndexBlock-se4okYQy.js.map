{"version": 3, "file": "flowGraphArrayIndexBlock-se4okYQy.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Utils/flowGraphArrayIndexBlock.js"], "sourcesContent": ["import { FlowGraphBlock } from \"../../../flowGraphBlock.js\";\nimport { RichTypeAny } from \"../../../flowGraphRichTypes.js\";\nimport { RegisterClass } from \"../../../../Misc/typeStore.js\";\nimport { FlowGraphInteger } from \"../../../CustomTypes/flowGraphInteger.js\";\nimport { getNumericValue } from \"../../../utils.js\";\n/**\n * This simple Util block takes an array as input and selects a single element from it.\n */\nexport class FlowGraphArrayIndexBlock extends FlowGraphBlock {\n    /**\n     * Construct a FlowGraphArrayIndexBlock.\n     * @param config construction parameters\n     */\n    constructor(config) {\n        super(config);\n        this.config = config;\n        this.array = this.registerDataInput(\"array\", RichTypeAny);\n        this.index = this.registerDataInput(\"index\", RichTypeAny, new FlowGraphInteger(-1));\n        this.value = this.registerDataOutput(\"value\", RichTypeAny);\n    }\n    /**\n     * @internal\n     */\n    _updateOutputs(context) {\n        const array = this.array.getValue(context);\n        const index = getNumericValue(this.index.getValue(context));\n        if (array && index >= 0 && index < array.length) {\n            this.value.setValue(array[index], context);\n        }\n        else {\n            this.value.setValue(null, context);\n        }\n    }\n    /**\n     * Serializes this block\n     * @param serializationObject the object to serialize to\n     */\n    serialize(serializationObject) {\n        super.serialize(serializationObject);\n    }\n    getClassName() {\n        return \"FlowGraphArrayIndexBlock\" /* FlowGraphBlockNames.ArrayIndex */;\n    }\n}\nRegisterClass(\"FlowGraphArrayIndexBlock\" /* FlowGraphBlockNames.ArrayIndex */, FlowGraphArrayIndexBlock);\n//# sourceMappingURL=flowGraphArrayIndexBlock.js.map"], "names": ["FlowGraphArrayIndexBlock", "FlowGraphBlock", "config", "RichTypeAny", "FlowGraphInteger", "context", "array", "index", "getNumericValue", "serializationObject", "RegisterClass"], "mappings": "8PAQO,MAAMA,UAAiCC,CAAe,CAKzD,YAAYC,EAAQ,CAChB,MAAMA,CAAM,EACZ,KAAK,OAASA,EACd,KAAK,MAAQ,KAAK,kBAAkB,QAASC,CAAW,EACxD,KAAK,MAAQ,KAAK,kBAAkB,QAASA,EAAa,IAAIC,EAAiB,EAAE,CAAC,EAClF,KAAK,MAAQ,KAAK,mBAAmB,QAASD,CAAW,CAC5D,CAID,eAAeE,EAAS,CACpB,MAAMC,EAAQ,KAAK,MAAM,SAASD,CAAO,EACnCE,EAAQC,EAAgB,KAAK,MAAM,SAASH,CAAO,CAAC,EACtDC,GAASC,GAAS,GAAKA,EAAQD,EAAM,OACrC,KAAK,MAAM,SAASA,EAAMC,CAAK,EAAGF,CAAO,EAGzC,KAAK,MAAM,SAAS,KAAMA,CAAO,CAExC,CAKD,UAAUI,EAAqB,CAC3B,MAAM,UAAUA,CAAmB,CACtC,CACD,cAAe,CACX,MAAO,0BACV,CACL,CACAC,EAAc,2BAAiEV,CAAwB", "x_google_ignoreList": [0]}