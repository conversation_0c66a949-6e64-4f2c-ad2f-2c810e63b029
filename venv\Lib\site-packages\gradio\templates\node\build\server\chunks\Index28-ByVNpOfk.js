import { c as create_ssr_component, v as validate_component, e as escape, d as add_attribute } from './ssr-C3HYbsxA.js';
import { m as mt, z as zA, N as xt } from './2-DJbI4FWc.js';
import './index-ClteBeTX.js';
import './Component-NmRBwSfF.js';
import 'path';
import 'url';
import 'fs';

const M={code:'label.svelte-7ha85a.svelte-7ha85a:not(.container),label.svelte-7ha85a:not(.container)>input.svelte-7ha85a{height:100%;border:none}.container.svelte-7ha85a>input.svelte-7ha85a{border:var(--input-border-width) solid var(--input-border-color);border-radius:var(--input-radius)}input[type="number"].svelte-7ha85a.svelte-7ha85a{display:block;position:relative;outline:none !important;box-shadow:var(--input-shadow);background:var(--input-background-fill);padding:var(--input-padding);width:100%;color:var(--body-text-color);font-size:var(--input-text-size);line-height:var(--line-sm)}input.svelte-7ha85a.svelte-7ha85a:disabled{-webkit-text-fill-color:var(--body-text-color);-webkit-opacity:1;opacity:1}input.svelte-7ha85a.svelte-7ha85a:focus{box-shadow:var(--input-shadow-focus);border-color:var(--input-border-color-focus);background:var(--input-background-fill-focus)}input.svelte-7ha85a.svelte-7ha85a::placeholder{color:var(--input-placeholder-color)}input.svelte-7ha85a.svelte-7ha85a:out-of-range{border:var(--input-border-width) solid var(--error-border-color)}',map:'{"version":3,"file":"Index.svelte","sources":["Index.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { Block, BlockTitle } from \\"@gradio/atoms\\";\\nimport { StatusTracker } from \\"@gradio/statustracker\\";\\nimport { afterUpdate, tick } from \\"svelte\\";\\nexport let gradio;\\nexport let label = gradio.i18n(\\"number.number\\");\\nexport let info = void 0;\\nexport let elem_id = \\"\\";\\nexport let elem_classes = [];\\nexport let visible = true;\\nexport let container = true;\\nexport let scale = null;\\nexport let min_width = void 0;\\nexport let value = null;\\nexport let show_label;\\nexport let minimum = void 0;\\nexport let maximum = void 0;\\nexport let loading_status;\\nexport let value_is_output = false;\\nexport let step = null;\\nexport let interactive;\\nexport let placeholder = \\"\\";\\nif (value === null && placeholder === \\"\\") {\\n    value = 0;\\n}\\nfunction handle_change() {\\n    if (value !== null && !isNaN(value)) {\\n        gradio.dispatch(\\"change\\");\\n        if (!value_is_output) {\\n            gradio.dispatch(\\"input\\");\\n        }\\n    }\\n}\\nafterUpdate(() => {\\n    value_is_output = false;\\n});\\nasync function handle_keypress(e) {\\n    await tick();\\n    if (e.key === \\"Enter\\") {\\n        e.preventDefault();\\n        gradio.dispatch(\\"submit\\");\\n    }\\n}\\n$: value, handle_change();\\n$: disabled = !interactive;\\n<\/script>\\n\\n<Block\\n\\t{visible}\\n\\t{elem_id}\\n\\t{elem_classes}\\n\\tpadding={container}\\n\\tallow_overflow={false}\\n\\t{scale}\\n\\t{min_width}\\n>\\n\\t<StatusTracker\\n\\t\\tautoscroll={gradio.autoscroll}\\n\\t\\ti18n={gradio.i18n}\\n\\t\\t{...loading_status}\\n\\t\\ton:clear_status={() => gradio.dispatch(\\"clear_status\\", loading_status)}\\n\\t/>\\n\\t<label class=\\"block\\" class:container>\\n\\t\\t<BlockTitle {show_label} {info}>{label}</BlockTitle>\\n\\t\\t<input\\n\\t\\t\\taria-label={label}\\n\\t\\t\\ttype=\\"number\\"\\n\\t\\t\\tbind:value\\n\\t\\t\\tmin={minimum}\\n\\t\\t\\tmax={maximum}\\n\\t\\t\\t{step}\\n\\t\\t\\t{placeholder}\\n\\t\\t\\ton:keypress={handle_keypress}\\n\\t\\t\\ton:blur={() => gradio.dispatch(\\"blur\\")}\\n\\t\\t\\ton:focus={() => gradio.dispatch(\\"focus\\")}\\n\\t\\t\\t{disabled}\\n\\t\\t/>\\n\\t</label>\\n</Block>\\n\\n<style>\\n\\tlabel:not(.container),\\n\\tlabel:not(.container) > input {\\n\\t\\theight: 100%;\\n\\t\\tborder: none;\\n\\t}\\n\\t.container > input {\\n\\t\\tborder: var(--input-border-width) solid var(--input-border-color);\\n\\t\\tborder-radius: var(--input-radius);\\n\\t}\\n\\tinput[type=\\"number\\"] {\\n\\t\\tdisplay: block;\\n\\t\\tposition: relative;\\n\\t\\toutline: none !important;\\n\\t\\tbox-shadow: var(--input-shadow);\\n\\t\\tbackground: var(--input-background-fill);\\n\\t\\tpadding: var(--input-padding);\\n\\t\\twidth: 100%;\\n\\t\\tcolor: var(--body-text-color);\\n\\t\\tfont-size: var(--input-text-size);\\n\\t\\tline-height: var(--line-sm);\\n\\t}\\n\\tinput:disabled {\\n\\t\\t-webkit-text-fill-color: var(--body-text-color);\\n\\t\\t-webkit-opacity: 1;\\n\\t\\topacity: 1;\\n\\t}\\n\\n\\tinput:focus {\\n\\t\\tbox-shadow: var(--input-shadow-focus);\\n\\t\\tborder-color: var(--input-border-color-focus);\\n\\t\\tbackground: var(--input-background-fill-focus);\\n\\t}\\n\\n\\tinput::placeholder {\\n\\t\\tcolor: var(--input-placeholder-color);\\n\\t}\\n\\n\\tinput:out-of-range {\\n\\t\\tborder: var(--input-border-width) solid var(--error-border-color);\\n\\t}</style>\\n"],"names":[],"mappings":"AAgFC,iCAAK,KAAK,UAAU,CAAC,CACrB,mBAAK,KAAK,UAAU,CAAC,CAAG,mBAAM,CAC7B,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,IACT,CACA,wBAAU,CAAG,mBAAM,CAClB,MAAM,CAAE,IAAI,oBAAoB,CAAC,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CACjE,aAAa,CAAE,IAAI,cAAc,CAClC,CACA,KAAK,CAAC,IAAI,CAAC,QAAQ,6BAAE,CACpB,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,CAAC,UAAU,CACxB,UAAU,CAAE,IAAI,cAAc,CAAC,CAC/B,UAAU,CAAE,IAAI,uBAAuB,CAAC,CACxC,OAAO,CAAE,IAAI,eAAe,CAAC,CAC7B,KAAK,CAAE,IAAI,CACX,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,SAAS,CAAE,IAAI,iBAAiB,CAAC,CACjC,WAAW,CAAE,IAAI,SAAS,CAC3B,CACA,iCAAK,SAAU,CACd,uBAAuB,CAAE,IAAI,iBAAiB,CAAC,CAC/C,eAAe,CAAE,CAAC,CAClB,OAAO,CAAE,CACV,CAEA,iCAAK,MAAO,CACX,UAAU,CAAE,IAAI,oBAAoB,CAAC,CACrC,YAAY,CAAE,IAAI,0BAA0B,CAAC,CAC7C,UAAU,CAAE,IAAI,6BAA6B,CAC9C,CAEA,iCAAK,aAAc,CAClB,KAAK,CAAE,IAAI,yBAAyB,CACrC,CAEA,iCAAK,aAAc,CAClB,MAAM,CAAE,IAAI,oBAAoB,CAAC,CAAC,KAAK,CAAC,IAAI,oBAAoB,CACjE"}'},z=create_ssr_component((o,t,e,O)=>{let I,{gradio:a}=t,{label:i=a.i18n("number.number")}=t,{info:u=void 0}=t,{elem_id:c=""}=t,{elem_classes:C=[]}=t,{visible:v=!0}=t,{container:n=!0}=t,{scale:s=null}=t,{min_width:m=void 0}=t,{value:l=null}=t,{show_label:h}=t,{minimum:f=void 0}=t,{maximum:p=void 0}=t,{loading_status:b}=t,{value_is_output:r=!1}=t,{step:_=null}=t,{interactive:B}=t,{placeholder:d=""}=t;l===null&&d===""&&(l=0);function k(){l!==null&&!isNaN(l)&&(a.dispatch("change"),r||a.dispatch("input"));}return t.gradio===void 0&&e.gradio&&a!==void 0&&e.gradio(a),t.label===void 0&&e.label&&i!==void 0&&e.label(i),t.info===void 0&&e.info&&u!==void 0&&e.info(u),t.elem_id===void 0&&e.elem_id&&c!==void 0&&e.elem_id(c),t.elem_classes===void 0&&e.elem_classes&&C!==void 0&&e.elem_classes(C),t.visible===void 0&&e.visible&&v!==void 0&&e.visible(v),t.container===void 0&&e.container&&n!==void 0&&e.container(n),t.scale===void 0&&e.scale&&s!==void 0&&e.scale(s),t.min_width===void 0&&e.min_width&&m!==void 0&&e.min_width(m),t.value===void 0&&e.value&&l!==void 0&&e.value(l),t.show_label===void 0&&e.show_label&&h!==void 0&&e.show_label(h),t.minimum===void 0&&e.minimum&&f!==void 0&&e.minimum(f),t.maximum===void 0&&e.maximum&&p!==void 0&&e.maximum(p),t.loading_status===void 0&&e.loading_status&&b!==void 0&&e.loading_status(b),t.value_is_output===void 0&&e.value_is_output&&r!==void 0&&e.value_is_output(r),t.step===void 0&&e.step&&_!==void 0&&e.step(_),t.interactive===void 0&&e.interactive&&B!==void 0&&e.interactive(B),t.placeholder===void 0&&e.placeholder&&d!==void 0&&e.placeholder(d),o.css.add(M),k(),I=!B,`${validate_component(mt,"Block").$$render(o,{visible:v,elem_id:c,elem_classes:C,padding:n,allow_overflow:!1,scale:s,min_width:m},{},{default:()=>`${validate_component(zA,"StatusTracker").$$render(o,Object.assign({},{autoscroll:a.autoscroll},{i18n:a.i18n},b),{},{})} <label class="${["block svelte-7ha85a",n?"container":""].join(" ").trim()}">${validate_component(xt,"BlockTitle").$$render(o,{show_label:h,info:u},{},{default:()=>`${escape(i)}`})} <input${add_attribute("aria-label",i,0)} type="number"${add_attribute("min",f,0)}${add_attribute("max",p,0)}${add_attribute("step",_,0)}${add_attribute("placeholder",d,0)} ${I?"disabled":""} class="svelte-7ha85a"${add_attribute("value",l,0)}></label>`})}`});

export { z as default };
//# sourceMappingURL=Index28-ByVNpOfk.js.map
