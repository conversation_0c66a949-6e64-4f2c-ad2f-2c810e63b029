import{F as p,g as s,G as l}from"./KHR_interactivity.DEAVS2UW.js";import{R as t,F as u}from"./declarationMapper.UBCwU7BT.js";import{R as h}from"./index.BoI39RQH.js";class o extends p{constructor(e){super(e),this.config=e,this.type=this.registerDataInput("type",t,e.type),this.value=this.registerDataOutput("value",t),this.index=this.registerDataInput("index",t,new u(s(e.index??-1)))}_updateOutputs(e){const a=this.type.getValue(e),r=this.index.getValue(e),i=l(e.assetsContext,a,s(r),this.config.useIndexAsUniqueId);this.value.setValue(i,e)}getClassName(){return"FlowGraphGetAssetBlock"}}h("FlowGraphGetAssetBlock",o);export{o as FlowGraphGetAssetBlock};
//# sourceMappingURL=flowGraphGetAssetBlock.B9sydsa6.js.map
