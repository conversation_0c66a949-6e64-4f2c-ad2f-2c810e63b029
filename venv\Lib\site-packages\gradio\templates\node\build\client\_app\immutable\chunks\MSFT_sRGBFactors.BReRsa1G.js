import{ar as a,an as u,ao as f}from"./index.BoI39RQH.js";import{GLTFLoader as p}from"./glTFLoader.BetPWe9U.js";const o="MSFT_sRGBFactors";class l{constructor(r){this.name=o,this._loader=r,this.enabled=this._loader.isExtensionUsed(o)}dispose(){this._loader=null}loadMaterialPropertiesAsync(r,t,e){return p.LoadExtraAsync(r,t,this.name,(n,c)=>{if(c){if(!(e instanceof a))throw new Error(`${n}: Material type not supported`);const d=this._loader.loadMaterialPropertiesAsync(r,t,e),i=e.getScene().getEngine().useExactSrgbConversions;return e.albedoTexture||e.albedoColor.toLinearSpaceToRef(e.albedoColor,i),e.reflectivityTexture||e.reflectivityColor.toLinearSpaceToRef(e.reflectivityColor,i),d}return null})}}u(o);f(o,!0,s=>new l(s));export{l as MSFT_sRGBFactors};
//# sourceMappingURL=MSFT_sRGBFactors.BReRsa1G.js.map
