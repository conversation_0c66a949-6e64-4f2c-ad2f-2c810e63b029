// qaudiosink.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QAudioSink : public QObject
{
%TypeHeaderCode
#include <qaudiosink.h>
%End

public:
    QAudioSink(const QAudioDevice &audioDeviceInfo, const QAudioFormat &format = QAudioFormat(), QObject *parent /TransferThis/ = 0);
    QAudioSink(const QAudioFormat &format = QAudioFormat(), QObject *parent /TransferThis/ = 0);
    virtual ~QAudioSink();
    QAudioFormat format() const;
    void start(QIODevice *device);
    QIODevice *start();
    void stop();
    void reset();
    void suspend();
    void resume();
    void setBufferSize(qsizetype bytes);
    qsizetype bufferSize() const;
    qsizetype bytesFree() const;
    qint64 processedUSecs() const;
    qint64 elapsedUSecs() const;
    QAudio::Error error() const;
    QAudio::State state() const;
    void setVolume(qreal);
    qreal volume() const;

signals:
    void stateChanged(QAudio::State state);
};

%End
