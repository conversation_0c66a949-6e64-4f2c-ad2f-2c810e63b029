"""
日志工具模块
提供精美的日志输出和文件记录
"""

import logging
import sys
from pathlib import Path
from typing import Optional
from loguru import logger as loguru_logger
import yaml

class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        record.levelname = f"{color}{record.levelname}{self.COLORS['RESET']}"
        record.name = f"\033[94m{record.name}{self.COLORS['RESET']}"
        
        return super().format(record)

class LoggerManager:
    """日志管理器"""
    
    def __init__(self, config_path: str = "config/settings/app_config.yaml"):
        self.config_path = config_path
        self.loggers = {}
        self._setup_loguru()
    
    def _load_config(self):
        """加载日志配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                return config.get('logging', {})
        except Exception:
            return {}
    
    def _setup_loguru(self):
        """设置loguru日志"""
        config = self._load_config()
        
        # 移除默认处理器
        loguru_logger.remove()
        
        # 控制台输出 - 美化版本
        loguru_logger.add(
            sys.stdout,
            format="<green>🕐 {time:HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<blue>📦 {name}</blue> | "
                   "<level>{message}</level>",
            level=config.get('log_level', 'INFO'),
            colorize=True
        )
        
        # 文件输出
        log_path = config.get('log_path', 'logs')
        Path(log_path).mkdir(exist_ok=True)
        
        loguru_logger.add(
            f"{log_path}/reverie_agents.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            level="DEBUG",
            rotation=config.get('max_log_size', '10MB'),
            retention=config.get('backup_count', 5),
            compression="zip",
            encoding="utf-8"
        )
        
        # 错误日志单独文件
        loguru_logger.add(
            f"{log_path}/errors.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            level="ERROR",
            rotation="1 week",
            retention="1 month",
            compression="zip",
            encoding="utf-8"
        )
    
    def get_logger(self, name: str) -> loguru_logger:
        """获取logger实例"""
        if name not in self.loggers:
            self.loggers[name] = loguru_logger.bind(name=name)
        return self.loggers[name]

# 全局日志管理器
_logger_manager = LoggerManager()

def get_logger(name: str = __name__):
    """获取logger实例"""
    return _logger_manager.get_logger(name)

# 默认logger
logger = get_logger("reverie")
