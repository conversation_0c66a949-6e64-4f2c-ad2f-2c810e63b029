{"version": 3, "file": "layout.DwfVdqTo.js", "sources": ["../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_flatRest.js", "../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/cloneDeep.js", "../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/forIn.js", "../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/forOwn.js", "../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseGt.js", "../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/mapValues.js", "../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/max.js", "../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/minBy.js", "../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseSortBy.js", "../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_compareAscending.js", "../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_compareMultiple.js", "../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseOrderBy.js", "../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_basePick.js", "../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/pick.js", "../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseRange.js", "../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_createRange.js", "../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/range.js", "../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/sortBy.js", "../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/uniqueId.js", "../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseZipObject.js", "../../../../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/zipObject.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/data/list.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/greedy-fas.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/acyclic.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/util.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/add-border-segments.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/coordinate-system.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/normalize.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/rank/util.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/rank/feasible-tree.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/alg/topsort.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/alg/dfs.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/alg/postorder.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/alg/preorder.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/rank/network-simplex.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/rank/index.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/nesting-graph.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/add-subgraph-constraints.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/build-layer-graph.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/cross-count.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/init-order.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/barycenter.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/resolve-conflicts.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/sort.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/sort-subgraph.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/index.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/parent-dummy-chains.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/position/bk.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/position/index.js", "../../../../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/layout.js"], "sourcesContent": ["import flatten from './flatten.js';\nimport overRest from './_overRest.js';\nimport setToString from './_setToString.js';\n\n/**\n * A specialized version of `baseRest` which flattens the rest array.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @returns {Function} Returns the new function.\n */\nfunction flatRest(func) {\n  return setToString(overRest(func, undefined, flatten), func + '');\n}\n\nexport default flatRest;\n", "import baseClone from './_baseClone.js';\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_DEEP_FLAG = 1,\n    CLONE_SYMBOLS_FLAG = 4;\n\n/**\n * This method is like `_.clone` except that it recursively clones `value`.\n *\n * @static\n * @memberOf _\n * @since 1.0.0\n * @category Lang\n * @param {*} value The value to recursively clone.\n * @returns {*} Returns the deep cloned value.\n * @see _.clone\n * @example\n *\n * var objects = [{ 'a': 1 }, { 'b': 2 }];\n *\n * var deep = _.cloneDeep(objects);\n * console.log(deep[0] === objects[0]);\n * // => false\n */\nfunction cloneDeep(value) {\n  return baseClone(value, CLONE_DEEP_FLAG | CLONE_SYMBOLS_FLAG);\n}\n\nexport default cloneDeep;\n", "import baseFor from './_baseFor.js';\nimport castFunction from './_castFunction.js';\nimport keysIn from './keysIn.js';\n\n/**\n * Iterates over own and inherited enumerable string keyed properties of an\n * object and invokes `iteratee` for each property. The iteratee is invoked\n * with three arguments: (value, key, object). Iteratee functions may exit\n * iteration early by explicitly returning `false`.\n *\n * @static\n * @memberOf _\n * @since 0.3.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns `object`.\n * @see _.forInRight\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.forIn(new Foo, function(value, key) {\n *   console.log(key);\n * });\n * // => Logs 'a', 'b', then 'c' (iteration order is not guaranteed).\n */\nfunction forIn(object, iteratee) {\n  return object == null\n    ? object\n    : baseFor(object, castFunction(iteratee), keysIn);\n}\n\nexport default forIn;\n", "import baseForOwn from './_baseForOwn.js';\nimport castFunction from './_castFunction.js';\n\n/**\n * Iterates over own enumerable string keyed properties of an object and\n * invokes `iteratee` for each property. The iteratee is invoked with three\n * arguments: (value, key, object). Iteratee functions may exit iteration\n * early by explicitly returning `false`.\n *\n * @static\n * @memberOf _\n * @since 0.3.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns `object`.\n * @see _.forOwnRight\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.forOwn(new Foo, function(value, key) {\n *   console.log(key);\n * });\n * // => Logs 'a' then 'b' (iteration order is not guaranteed).\n */\nfunction forOwn(object, iteratee) {\n  return object && baseForOwn(object, castFunction(iteratee));\n}\n\nexport default forOwn;\n", "/**\n * The base implementation of `_.gt` which doesn't coerce arguments.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if `value` is greater than `other`,\n *  else `false`.\n */\nfunction baseGt(value, other) {\n  return value > other;\n}\n\nexport default baseGt;\n", "import baseAssignValue from './_baseAssignValue.js';\nimport baseForOwn from './_baseForOwn.js';\nimport baseIteratee from './_baseIteratee.js';\n\n/**\n * Creates an object with the same keys as `object` and values generated\n * by running each own enumerable string keyed property of `object` thru\n * `iteratee`. The iteratee is invoked with three arguments:\n * (value, key, object).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns the new mapped object.\n * @see _.mapKeys\n * @example\n *\n * var users = {\n *   'fred':    { 'user': 'fred',    'age': 40 },\n *   'pebbles': { 'user': 'pebbles', 'age': 1 }\n * };\n *\n * _.mapValues(users, function(o) { return o.age; });\n * // => { 'fred': 40, 'pebbles': 1 } (iteration order is not guaranteed)\n *\n * // The `_.property` iteratee shorthand.\n * _.mapValues(users, 'age');\n * // => { 'fred': 40, 'pebbles': 1 } (iteration order is not guaranteed)\n */\nfunction mapValues(object, iteratee) {\n  var result = {};\n  iteratee = baseIteratee(iteratee, 3);\n\n  baseForOwn(object, function(value, key, object) {\n    baseAssignValue(result, key, iteratee(value, key, object));\n  });\n  return result;\n}\n\nexport default mapValues;\n", "import baseExtremum from './_baseExtremum.js';\nimport baseGt from './_baseGt.js';\nimport identity from './identity.js';\n\n/**\n * Computes the maximum value of `array`. If `array` is empty or falsey,\n * `undefined` is returned.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Math\n * @param {Array} array The array to iterate over.\n * @returns {*} Returns the maximum value.\n * @example\n *\n * _.max([4, 2, 8, 6]);\n * // => 8\n *\n * _.max([]);\n * // => undefined\n */\nfunction max(array) {\n  return (array && array.length)\n    ? baseExtremum(array, identity, baseGt)\n    : undefined;\n}\n\nexport default max;\n", "import baseExtremum from './_baseExtremum.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseLt from './_baseLt.js';\n\n/**\n * This method is like `_.min` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the criterion by which\n * the value is ranked. The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {*} Returns the minimum value.\n * @example\n *\n * var objects = [{ 'n': 1 }, { 'n': 2 }];\n *\n * _.minBy(objects, function(o) { return o.n; });\n * // => { 'n': 1 }\n *\n * // The `_.property` iteratee shorthand.\n * _.minBy(objects, 'n');\n * // => { 'n': 1 }\n */\nfunction minBy(array, iteratee) {\n  return (array && array.length)\n    ? baseExtremum(array, baseIteratee(iteratee, 2), baseLt)\n    : undefined;\n}\n\nexport default minBy;\n", "/**\n * The base implementation of `_.sortBy` which uses `comparer` to define the\n * sort order of `array` and replaces criteria objects with their corresponding\n * values.\n *\n * @private\n * @param {Array} array The array to sort.\n * @param {Function} comparer The function to define sort order.\n * @returns {Array} Returns `array`.\n */\nfunction baseSortBy(array, comparer) {\n  var length = array.length;\n\n  array.sort(comparer);\n  while (length--) {\n    array[length] = array[length].value;\n  }\n  return array;\n}\n\nexport default baseSortBy;\n", "import isSymbol from './isSymbol.js';\n\n/**\n * Compares values to sort them in ascending order.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {number} Returns the sort order indicator for `value`.\n */\nfunction compareAscending(value, other) {\n  if (value !== other) {\n    var valIsDefined = value !== undefined,\n        valIsNull = value === null,\n        valIsReflexive = value === value,\n        valIsSymbol = isSymbol(value);\n\n    var othIsDefined = other !== undefined,\n        othIsNull = other === null,\n        othIsReflexive = other === other,\n        othIsSymbol = isSymbol(other);\n\n    if ((!othIsNull && !othIsSymbol && !valIsSymbol && value > other) ||\n        (valIsSymbol && othIsDefined && othIsReflexive && !othIsNull && !othIsSymbol) ||\n        (valIsNull && othIsDefined && othIsReflexive) ||\n        (!valIsDefined && othIsReflexive) ||\n        !valIsReflexive) {\n      return 1;\n    }\n    if ((!valIsNull && !valIsSymbol && !othIsSymbol && value < other) ||\n        (othIsSymbol && valIsDefined && valIsReflexive && !valIsNull && !valIsSymbol) ||\n        (othIsNull && valIsDefined && valIsReflexive) ||\n        (!othIsDefined && valIsReflexive) ||\n        !othIsReflexive) {\n      return -1;\n    }\n  }\n  return 0;\n}\n\nexport default compareAscending;\n", "import compareAscending from './_compareAscending.js';\n\n/**\n * Used by `_.orderBy` to compare multiple properties of a value to another\n * and stable sort them.\n *\n * If `orders` is unspecified, all values are sorted in ascending order. Otherwise,\n * specify an order of \"desc\" for descending or \"asc\" for ascending sort order\n * of corresponding values.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {boolean[]|string[]} orders The order to sort by for each property.\n * @returns {number} Returns the sort order indicator for `object`.\n */\nfunction compareMultiple(object, other, orders) {\n  var index = -1,\n      objCriteria = object.criteria,\n      othCriteria = other.criteria,\n      length = objCriteria.length,\n      ordersLength = orders.length;\n\n  while (++index < length) {\n    var result = compareAscending(objCriteria[index], othCriteria[index]);\n    if (result) {\n      if (index >= ordersLength) {\n        return result;\n      }\n      var order = orders[index];\n      return result * (order == 'desc' ? -1 : 1);\n    }\n  }\n  // Fixes an `Array#sort` bug in the JS engine embedded in Adobe applications\n  // that causes it, under certain circumstances, to provide the same value for\n  // `object` and `other`. See https://github.com/jashkenas/underscore/pull/1247\n  // for more details.\n  //\n  // This also ensures a stable sort in V8 and other engines.\n  // See https://bugs.chromium.org/p/v8/issues/detail?id=90 for more details.\n  return object.index - other.index;\n}\n\nexport default compareMultiple;\n", "import arrayMap from './_arrayMap.js';\nimport baseGet from './_baseGet.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseMap from './_baseMap.js';\nimport baseSortBy from './_baseSortBy.js';\nimport baseUnary from './_baseUnary.js';\nimport compareMultiple from './_compareMultiple.js';\nimport identity from './identity.js';\nimport isArray from './isArray.js';\n\n/**\n * The base implementation of `_.orderBy` without param guards.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function[]|Object[]|string[]} iteratees The iteratees to sort by.\n * @param {string[]} orders The sort orders of `iteratees`.\n * @returns {Array} Returns the new sorted array.\n */\nfunction baseOrderBy(collection, iteratees, orders) {\n  if (iteratees.length) {\n    iteratees = arrayMap(iteratees, function(iteratee) {\n      if (isArray(iteratee)) {\n        return function(value) {\n          return baseGet(value, iteratee.length === 1 ? iteratee[0] : iteratee);\n        }\n      }\n      return iteratee;\n    });\n  } else {\n    iteratees = [identity];\n  }\n\n  var index = -1;\n  iteratees = arrayMap(iteratees, baseUnary(baseIteratee));\n\n  var result = baseMap(collection, function(value, key, collection) {\n    var criteria = arrayMap(iteratees, function(iteratee) {\n      return iteratee(value);\n    });\n    return { 'criteria': criteria, 'index': ++index, 'value': value };\n  });\n\n  return baseSortBy(result, function(object, other) {\n    return compareMultiple(object, other, orders);\n  });\n}\n\nexport default baseOrderBy;\n", "import basePickBy from './_basePickBy.js';\nimport hasIn from './hasIn.js';\n\n/**\n * The base implementation of `_.pick` without support for individual\n * property identifiers.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @returns {Object} Returns the new object.\n */\nfunction basePick(object, paths) {\n  return basePickBy(object, paths, function(value, path) {\n    return hasIn(object, path);\n  });\n}\n\nexport default basePick;\n", "import basePick from './_basePick.js';\nimport flatRest from './_flatRest.js';\n\n/**\n * Creates an object composed of the picked `object` properties.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The source object.\n * @param {...(string|string[])} [paths] The property paths to pick.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.pick(object, ['a', 'c']);\n * // => { 'a': 1, 'c': 3 }\n */\nvar pick = flatRest(function(object, paths) {\n  return object == null ? {} : basePick(object, paths);\n});\n\nexport default pick;\n", "/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeCeil = Math.ceil,\n    nativeMax = Math.max;\n\n/**\n * The base implementation of `_.range` and `_.rangeRight` which doesn't\n * coerce arguments.\n *\n * @private\n * @param {number} start The start of the range.\n * @param {number} end The end of the range.\n * @param {number} step The value to increment or decrement by.\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Array} Returns the range of numbers.\n */\nfunction baseRange(start, end, step, fromRight) {\n  var index = -1,\n      length = nativeMax(nativeCeil((end - start) / (step || 1)), 0),\n      result = Array(length);\n\n  while (length--) {\n    result[fromRight ? length : ++index] = start;\n    start += step;\n  }\n  return result;\n}\n\nexport default baseRange;\n", "import baseRange from './_baseRange.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport toFinite from './toFinite.js';\n\n/**\n * Creates a `_.range` or `_.rangeRight` function.\n *\n * @private\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new range function.\n */\nfunction createRange(fromRight) {\n  return function(start, end, step) {\n    if (step && typeof step != 'number' && isIterateeCall(start, end, step)) {\n      end = step = undefined;\n    }\n    // Ensure the sign of `-0` is preserved.\n    start = toFinite(start);\n    if (end === undefined) {\n      end = start;\n      start = 0;\n    } else {\n      end = toFinite(end);\n    }\n    step = step === undefined ? (start < end ? 1 : -1) : toFinite(step);\n    return baseRange(start, end, step, fromRight);\n  };\n}\n\nexport default createRange;\n", "import createRange from './_createRange.js';\n\n/**\n * Creates an array of numbers (positive and/or negative) progressing from\n * `start` up to, but not including, `end`. A step of `-1` is used if a negative\n * `start` is specified without an `end` or `step`. If `end` is not specified,\n * it's set to `start` with `start` then set to `0`.\n *\n * **Note:** JavaScript follows the IEEE-754 standard for resolving\n * floating-point values which can produce unexpected results.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {number} [start=0] The start of the range.\n * @param {number} end The end of the range.\n * @param {number} [step=1] The value to increment or decrement by.\n * @returns {Array} Returns the range of numbers.\n * @see _.inRange, _.rangeRight\n * @example\n *\n * _.range(4);\n * // => [0, 1, 2, 3]\n *\n * _.range(-4);\n * // => [0, -1, -2, -3]\n *\n * _.range(1, 5);\n * // => [1, 2, 3, 4]\n *\n * _.range(0, 20, 5);\n * // => [0, 5, 10, 15]\n *\n * _.range(0, -4, -1);\n * // => [0, -1, -2, -3]\n *\n * _.range(1, 4, 0);\n * // => [1, 1, 1]\n *\n * _.range(0);\n * // => []\n */\nvar range = createRange();\n\nexport default range;\n", "import baseFlatten from './_baseFlatten.js';\nimport baseOrderBy from './_baseOrderBy.js';\nimport baseRest from './_baseRest.js';\nimport isIterateeCall from './_isIterateeCall.js';\n\n/**\n * Creates an array of elements, sorted in ascending order by the results of\n * running each element in a collection thru each iteratee. This method\n * performs a stable sort, that is, it preserves the original sort order of\n * equal elements. The iteratees are invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {...(Function|Function[])} [iteratees=[_.identity]]\n *  The iteratees to sort by.\n * @returns {Array} Returns the new sorted array.\n * @example\n *\n * var users = [\n *   { 'user': 'fred',   'age': 48 },\n *   { 'user': 'barney', 'age': 36 },\n *   { 'user': 'fred',   'age': 30 },\n *   { 'user': 'barney', 'age': 34 }\n * ];\n *\n * _.sortBy(users, [function(o) { return o.user; }]);\n * // => objects for [['barney', 36], ['barney', 34], ['fred', 48], ['fred', 30]]\n *\n * _.sortBy(users, ['user', 'age']);\n * // => objects for [['barney', 34], ['barney', 36], ['fred', 30], ['fred', 48]]\n */\nvar sortBy = baseRest(function(collection, iteratees) {\n  if (collection == null) {\n    return [];\n  }\n  var length = iteratees.length;\n  if (length > 1 && isIterateeCall(collection, iteratees[0], iteratees[1])) {\n    iteratees = [];\n  } else if (length > 2 && isIterateeCall(iteratees[0], iteratees[1], iteratees[2])) {\n    iteratees = [iteratees[0]];\n  }\n  return baseOrderBy(collection, baseFlatten(iteratees, 1), []);\n});\n\nexport default sortBy;\n", "import toString from './toString.js';\n\n/** Used to generate unique IDs. */\nvar idCounter = 0;\n\n/**\n * Generates a unique ID. If `prefix` is given, the ID is appended to it.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {string} [prefix=''] The value to prefix the ID with.\n * @returns {string} Returns the unique ID.\n * @example\n *\n * _.uniqueId('contact_');\n * // => 'contact_104'\n *\n * _.uniqueId();\n * // => '105'\n */\nfunction uniqueId(prefix) {\n  var id = ++idCounter;\n  return toString(prefix) + id;\n}\n\nexport default uniqueId;\n", "/**\n * This base implementation of `_.zipObject` which assigns values using `assignFunc`.\n *\n * @private\n * @param {Array} props The property identifiers.\n * @param {Array} values The property values.\n * @param {Function} assignFunc The function to assign values.\n * @returns {Object} Returns the new object.\n */\nfunction baseZipObject(props, values, assignFunc) {\n  var index = -1,\n      length = props.length,\n      valsLength = values.length,\n      result = {};\n\n  while (++index < length) {\n    var value = index < valsLength ? values[index] : undefined;\n    assignFunc(result, props[index], value);\n  }\n  return result;\n}\n\nexport default baseZipObject;\n", "import assignValue from './_assignValue.js';\nimport baseZipObject from './_baseZipObject.js';\n\n/**\n * This method is like `_.fromPairs` except that it accepts two arrays,\n * one of property identifiers and one of corresponding values.\n *\n * @static\n * @memberOf _\n * @since 0.4.0\n * @category Array\n * @param {Array} [props=[]] The property identifiers.\n * @param {Array} [values=[]] The property values.\n * @returns {Object} Returns the new object.\n * @example\n *\n * _.zipObject(['a', 'b'], [1, 2]);\n * // => { 'a': 1, 'b': 2 }\n */\nfunction zipObject(props, values) {\n  return baseZipObject(props || [], values || [], assignValue);\n}\n\nexport default zipObject;\n", "/*\n * Simple doubly linked list implementation derived from <PERSON><PERSON><PERSON>, et al.,\n * \"Introduction to Algorithms\".\n */\n\nexport { List };\n\nclass List {\n  constructor() {\n    var sentinel = {};\n    sentinel._next = sentinel._prev = sentinel;\n    this._sentinel = sentinel;\n  }\n  dequeue() {\n    var sentinel = this._sentinel;\n    var entry = sentinel._prev;\n    if (entry !== sentinel) {\n      unlink(entry);\n      return entry;\n    }\n  }\n  enqueue(entry) {\n    var sentinel = this._sentinel;\n    if (entry._prev && entry._next) {\n      unlink(entry);\n    }\n    entry._next = sentinel._next;\n    sentinel._next._prev = entry;\n    sentinel._next = entry;\n    entry._prev = sentinel;\n  }\n  toString() {\n    var strs = [];\n    var sentinel = this._sentinel;\n    var curr = sentinel._prev;\n    while (curr !== sentinel) {\n      strs.push(JSON.stringify(curr, filterOutLinks));\n      curr = curr._prev;\n    }\n    return '[' + strs.join(', ') + ']';\n  }\n}\n\nfunction unlink(entry) {\n  entry._prev._next = entry._next;\n  entry._next._prev = entry._prev;\n  delete entry._next;\n  delete entry._prev;\n}\n\nfunction filterOutLinks(k, v) {\n  if (k !== '_next' && k !== '_prev') {\n    return v;\n  }\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\nimport { List } from './data/list.js';\n\n/*\n * A greedy heuristic for finding a feedback arc set for a graph. A feedback\n * arc set is a set of edges that can be removed to make a graph acyclic.\n * The algorithm comes from: <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> <PERSON><PERSON>, \"A fast and\n * effective heuristic for the feedback arc set problem.\" This implementation\n * adjusts that from the paper to allow for weighted edges.\n */\nexport { greedyFAS };\n\nvar DEFAULT_WEIGHT_FN = _.constant(1);\n\nfunction greedyFAS(g, weightFn) {\n  if (g.nodeCount() <= 1) {\n    return [];\n  }\n  var state = buildState(g, weightFn || DEFAULT_WEIGHT_FN);\n  var results = doGreedyFAS(state.graph, state.buckets, state.zeroIdx);\n\n  // Expand multi-edges\n  return _.flatten(\n    _.map(results, function (e) {\n      return g.outEdges(e.v, e.w);\n    }),\n  );\n}\n\nfunction doGreedyFAS(g, buckets, zeroIdx) {\n  var results = [];\n  var sources = buckets[buckets.length - 1];\n  var sinks = buckets[0];\n\n  var entry;\n  while (g.nodeCount()) {\n    while ((entry = sinks.dequeue())) {\n      removeNode(g, buckets, zeroIdx, entry);\n    }\n    while ((entry = sources.dequeue())) {\n      removeNode(g, buckets, zeroIdx, entry);\n    }\n    if (g.nodeCount()) {\n      for (var i = buckets.length - 2; i > 0; --i) {\n        entry = buckets[i].dequeue();\n        if (entry) {\n          results = results.concat(removeNode(g, buckets, zeroIdx, entry, true));\n          break;\n        }\n      }\n    }\n  }\n\n  return results;\n}\n\nfunction removeNode(g, buckets, zeroIdx, entry, collectPredecessors) {\n  var results = collectPredecessors ? [] : undefined;\n\n  _.forEach(g.inEdges(entry.v), function (edge) {\n    var weight = g.edge(edge);\n    var uEntry = g.node(edge.v);\n\n    if (collectPredecessors) {\n      results.push({ v: edge.v, w: edge.w });\n    }\n\n    uEntry.out -= weight;\n    assignBucket(buckets, zeroIdx, uEntry);\n  });\n\n  _.forEach(g.outEdges(entry.v), function (edge) {\n    var weight = g.edge(edge);\n    var w = edge.w;\n    var wEntry = g.node(w);\n    wEntry['in'] -= weight;\n    assignBucket(buckets, zeroIdx, wEntry);\n  });\n\n  g.removeNode(entry.v);\n\n  return results;\n}\n\nfunction buildState(g, weightFn) {\n  var fasGraph = new Graph();\n  var maxIn = 0;\n  var maxOut = 0;\n\n  _.forEach(g.nodes(), function (v) {\n    fasGraph.setNode(v, { v: v, in: 0, out: 0 });\n  });\n\n  // Aggregate weights on nodes, but also sum the weights across multi-edges\n  // into a single edge for the fasGraph.\n  _.forEach(g.edges(), function (e) {\n    var prevWeight = fasGraph.edge(e.v, e.w) || 0;\n    var weight = weightFn(e);\n    var edgeWeight = prevWeight + weight;\n    fasGraph.setEdge(e.v, e.w, edgeWeight);\n    maxOut = Math.max(maxOut, (fasGraph.node(e.v).out += weight));\n    maxIn = Math.max(maxIn, (fasGraph.node(e.w)['in'] += weight));\n  });\n\n  var buckets = _.range(maxOut + maxIn + 3).map(function () {\n    return new List();\n  });\n  var zeroIdx = maxIn + 1;\n\n  _.forEach(fasGraph.nodes(), function (v) {\n    assignBucket(buckets, zeroIdx, fasGraph.node(v));\n  });\n\n  return { graph: fasGraph, buckets: buckets, zeroIdx: zeroIdx };\n}\n\nfunction assignBucket(buckets, zeroIdx, entry) {\n  if (!entry.out) {\n    buckets[0].enqueue(entry);\n  } else if (!entry['in']) {\n    buckets[buckets.length - 1].enqueue(entry);\n  } else {\n    buckets[entry.out - entry['in'] + zeroIdx].enqueue(entry);\n  }\n}\n", "import * as _ from 'lodash-es';\nimport { greedyFAS } from './greedy-fas.js';\n\nexport { run, undo };\n\nfunction run(g) {\n  var fas = g.graph().acyclicer === 'greedy' ? greedyFAS(g, weightFn(g)) : dfsFAS(g);\n  _.forEach(fas, function (e) {\n    var label = g.edge(e);\n    g.removeEdge(e);\n    label.forwardName = e.name;\n    label.reversed = true;\n    g.setEdge(e.w, e.v, label, _.uniqueId('rev'));\n  });\n\n  function weightFn(g) {\n    return function (e) {\n      return g.edge(e).weight;\n    };\n  }\n}\n\nfunction dfsFAS(g) {\n  var fas = [];\n  var stack = {};\n  var visited = {};\n\n  function dfs(v) {\n    if (Object.prototype.hasOwnProperty.call(visited, v)) {\n      return;\n    }\n    visited[v] = true;\n    stack[v] = true;\n    _.forEach(g.outEdges(v), function (e) {\n      if (Object.prototype.hasOwnProperty.call(stack, e.w)) {\n        fas.push(e);\n      } else {\n        dfs(e.w);\n      }\n    });\n    delete stack[v];\n  }\n\n  _.forEach(g.nodes(), dfs);\n  return fas;\n}\n\nfunction undo(g) {\n  _.forEach(g.edges(), function (e) {\n    var label = g.edge(e);\n    if (label.reversed) {\n      g.removeEdge(e);\n\n      var forwardName = label.forwardName;\n      delete label.reversed;\n      delete label.forwardName;\n      g.setEdge(e.w, e.v, label, forwardName);\n    }\n  });\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\n\nexport {\n  addDummyNode,\n  simplify,\n  asNonCompoundGraph,\n  successorWeights,\n  predecessorWeights,\n  intersectRect,\n  buildLayerMatrix,\n  normalizeRanks,\n  removeEmptyRanks,\n  addBorderNode,\n  maxRank,\n  partition,\n  time,\n  notime,\n};\n\n/*\n * Adds a dummy node to the graph and return v.\n */\nfunction addDummyNode(g, type, attrs, name) {\n  var v;\n  do {\n    v = _.uniqueId(name);\n  } while (g.hasNode(v));\n\n  attrs.dummy = type;\n  g.setNode(v, attrs);\n  return v;\n}\n\n/*\n * Returns a new graph with only simple edges. Handles aggregation of data\n * associated with multi-edges.\n */\nfunction simplify(g) {\n  var simplified = new Graph().setGraph(g.graph());\n  _.forEach(g.nodes(), function (v) {\n    simplified.setNode(v, g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    var simpleLabel = simplified.edge(e.v, e.w) || { weight: 0, minlen: 1 };\n    var label = g.edge(e);\n    simplified.setEdge(e.v, e.w, {\n      weight: simpleLabel.weight + label.weight,\n      minlen: Math.max(simpleLabel.minlen, label.minlen),\n    });\n  });\n  return simplified;\n}\n\nfunction asNonCompoundGraph(g) {\n  var simplified = new Graph({ multigraph: g.isMultigraph() }).setGraph(g.graph());\n  _.forEach(g.nodes(), function (v) {\n    if (!g.children(v).length) {\n      simplified.setNode(v, g.node(v));\n    }\n  });\n  _.forEach(g.edges(), function (e) {\n    simplified.setEdge(e, g.edge(e));\n  });\n  return simplified;\n}\n\nfunction successorWeights(g) {\n  var weightMap = _.map(g.nodes(), function (v) {\n    var sucs = {};\n    _.forEach(g.outEdges(v), function (e) {\n      sucs[e.w] = (sucs[e.w] || 0) + g.edge(e).weight;\n    });\n    return sucs;\n  });\n  return _.zipObject(g.nodes(), weightMap);\n}\n\nfunction predecessorWeights(g) {\n  var weightMap = _.map(g.nodes(), function (v) {\n    var preds = {};\n    _.forEach(g.inEdges(v), function (e) {\n      preds[e.v] = (preds[e.v] || 0) + g.edge(e).weight;\n    });\n    return preds;\n  });\n  return _.zipObject(g.nodes(), weightMap);\n}\n\n/*\n * Finds where a line starting at point ({x, y}) would intersect a rectangle\n * ({x, y, width, height}) if it were pointing at the rectangle's center.\n */\nfunction intersectRect(rect, point) {\n  var x = rect.x;\n  var y = rect.y;\n\n  // Rectangle intersection algorithm from:\n  // http://math.stackexchange.com/questions/108113/find-edge-between-two-boxes\n  var dx = point.x - x;\n  var dy = point.y - y;\n  var w = rect.width / 2;\n  var h = rect.height / 2;\n\n  if (!dx && !dy) {\n    throw new Error('Not possible to find intersection inside of the rectangle');\n  }\n\n  var sx, sy;\n  if (Math.abs(dy) * w > Math.abs(dx) * h) {\n    // Intersection is top or bottom of rect.\n    if (dy < 0) {\n      h = -h;\n    }\n    sx = (h * dx) / dy;\n    sy = h;\n  } else {\n    // Intersection is left or right of rect.\n    if (dx < 0) {\n      w = -w;\n    }\n    sx = w;\n    sy = (w * dy) / dx;\n  }\n\n  return { x: x + sx, y: y + sy };\n}\n\n/*\n * Given a DAG with each node assigned \"rank\" and \"order\" properties, this\n * function will produce a matrix with the ids of each node.\n */\nfunction buildLayerMatrix(g) {\n  var layering = _.map(_.range(maxRank(g) + 1), function () {\n    return [];\n  });\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    var rank = node.rank;\n    if (!_.isUndefined(rank)) {\n      layering[rank][node.order] = v;\n    }\n  });\n  return layering;\n}\n\n/*\n * Adjusts the ranks for all nodes in the graph such that all nodes v have\n * rank(v) >= 0 and at least one node w has rank(w) = 0.\n */\nfunction normalizeRanks(g) {\n  var min = _.min(\n    _.map(g.nodes(), function (v) {\n      return g.node(v).rank;\n    }),\n  );\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (_.has(node, 'rank')) {\n      node.rank -= min;\n    }\n  });\n}\n\nfunction removeEmptyRanks(g) {\n  // Ranks may not start at 0, so we need to offset them\n  var offset = _.min(\n    _.map(g.nodes(), function (v) {\n      return g.node(v).rank;\n    }),\n  );\n\n  var layers = [];\n  _.forEach(g.nodes(), function (v) {\n    var rank = g.node(v).rank - offset;\n    if (!layers[rank]) {\n      layers[rank] = [];\n    }\n    layers[rank].push(v);\n  });\n\n  var delta = 0;\n  var nodeRankFactor = g.graph().nodeRankFactor;\n  _.forEach(layers, function (vs, i) {\n    if (_.isUndefined(vs) && i % nodeRankFactor !== 0) {\n      --delta;\n    } else if (delta) {\n      _.forEach(vs, function (v) {\n        g.node(v).rank += delta;\n      });\n    }\n  });\n}\n\nfunction addBorderNode(g, prefix, rank, order) {\n  var node = {\n    width: 0,\n    height: 0,\n  };\n  if (arguments.length >= 4) {\n    node.rank = rank;\n    node.order = order;\n  }\n  return addDummyNode(g, 'border', node, prefix);\n}\n\nfunction maxRank(g) {\n  return _.max(\n    _.map(g.nodes(), function (v) {\n      var rank = g.node(v).rank;\n      if (!_.isUndefined(rank)) {\n        return rank;\n      }\n    }),\n  );\n}\n\n/*\n * Partition a collection into two groups: `lhs` and `rhs`. If the supplied\n * function returns true for an entry it goes into `lhs`. Otherwise it goes\n * into `rhs.\n */\nfunction partition(collection, fn) {\n  var result = { lhs: [], rhs: [] };\n  _.forEach(collection, function (value) {\n    if (fn(value)) {\n      result.lhs.push(value);\n    } else {\n      result.rhs.push(value);\n    }\n  });\n  return result;\n}\n\n/*\n * Returns a new function that wraps `fn` with a timer. The wrapper logs the\n * time it takes to execute the function.\n */\nfunction time(name, fn) {\n  var start = _.now();\n  try {\n    return fn();\n  } finally {\n    console.log(name + ' time: ' + (_.now() - start) + 'ms');\n  }\n}\n\nfunction notime(name, fn) {\n  return fn();\n}\n", "import * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { addBorderSegments };\n\nfunction addBorderSegments(g) {\n  function dfs(v) {\n    var children = g.children(v);\n    var node = g.node(v);\n    if (children.length) {\n      _.forEach(children, dfs);\n    }\n\n    if (Object.prototype.hasOwnProperty.call(node, 'minRank')) {\n      node.borderLeft = [];\n      node.borderRight = [];\n      for (var rank = node.minRank, maxRank = node.maxRank + 1; rank < maxRank; ++rank) {\n        addBorderNode(g, 'borderLeft', '_bl', v, node, rank);\n        addBorderNode(g, 'borderRight', '_br', v, node, rank);\n      }\n    }\n  }\n\n  _.forEach(g.children(), dfs);\n}\n\nfunction addBorderNode(g, prop, prefix, sg, sgNode, rank) {\n  var label = { width: 0, height: 0, rank: rank, borderType: prop };\n  var prev = sgNode[prop][rank - 1];\n  var curr = util.addDummyNode(g, 'border', label, prefix);\n  sgNode[prop][rank] = curr;\n  g.setParent(curr, sg);\n  if (prev) {\n    g.setEdge(prev, curr, { weight: 1 });\n  }\n}\n", "import * as _ from 'lodash-es';\n\nexport { adjust, undo };\n\nfunction adjust(g) {\n  var rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === 'lr' || rankDir === 'rl') {\n    swapWidthHeight(g);\n  }\n}\n\nfunction undo(g) {\n  var rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === 'bt' || rankDir === 'rl') {\n    reverseY(g);\n  }\n\n  if (rankDir === 'lr' || rankDir === 'rl') {\n    swapXY(g);\n    swapWidthHeight(g);\n  }\n}\n\nfunction swapWidthHeight(g) {\n  _.forEach(g.nodes(), function (v) {\n    swapWidthHeightOne(g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    swapWidthHeightOne(g.edge(e));\n  });\n}\n\nfunction swapWidthHeightOne(attrs) {\n  var w = attrs.width;\n  attrs.width = attrs.height;\n  attrs.height = w;\n}\n\nfunction reverseY(g) {\n  _.forEach(g.nodes(), function (v) {\n    reverseYOne(g.node(v));\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, reverseYOne);\n    if (Object.prototype.hasOwnProperty.call(edge, 'y')) {\n      reverseYOne(edge);\n    }\n  });\n}\n\nfunction reverseYOne(attrs) {\n  attrs.y = -attrs.y;\n}\n\nfunction swapXY(g) {\n  _.forEach(g.nodes(), function (v) {\n    swapXYOne(g.node(v));\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, swapXYOne);\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      swapXYOne(edge);\n    }\n  });\n}\n\nfunction swapXYOne(attrs) {\n  var x = attrs.x;\n  attrs.x = attrs.y;\n  attrs.y = x;\n}\n", "/**\n * TypeScript type imports:\n *\n * @import { Graph } from '../graphlib/graph.js';\n */\nimport * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { run, undo };\n\n/*\n * Breaks any long edges in the graph into short segments that span 1 layer\n * each. This operation is undoable with the denormalize function.\n *\n * Pre-conditions:\n *\n *    1. The input graph is a DAG.\n *    2. Each node in the graph has a \"rank\" property.\n *\n * Post-condition:\n *\n *    1. All edges in the graph have a length of 1.\n *    2. Dummy nodes are added where edges have been split into segments.\n *    3. The graph is augmented with a \"dummyChains\" attribute which contains\n *       the first dummy in each chain of dummy nodes produced.\n */\nfunction run(g) {\n  g.graph().dummyChains = [];\n  _.forEach(g.edges(), function (edge) {\n    normalizeEdge(g, edge);\n  });\n}\n\n/**\n * @param {Graph} g\n */\nfunction normalizeEdge(g, e) {\n  var v = e.v;\n  var vRank = g.node(v).rank;\n  var w = e.w;\n  var wRank = g.node(w).rank;\n  var name = e.name;\n  var edgeLabel = g.edge(e);\n  var labelRank = edgeLabel.labelRank;\n\n  if (wRank === vRank + 1) return;\n\n  g.removeEdge(e);\n\n  /**\n   * @typedef {Object} Attrs\n   * @property {number} width\n   * @property {number} height\n   * @property {ReturnType<Graph[\"node\"]>} edgeLabel\n   * @property {any} edgeObj\n   * @property {ReturnType<Graph[\"node\"]>[\"rank\"]} rank\n   * @property {string} [dummy]\n   * @property {ReturnType<Graph[\"node\"]>[\"labelpos\"]} [labelpos]\n   */\n\n  /** @type {Attrs | undefined} */\n  var attrs = undefined;\n  var dummy, i;\n  for (i = 0, ++vRank; vRank < wRank; ++i, ++vRank) {\n    edgeLabel.points = [];\n    attrs = {\n      width: 0,\n      height: 0,\n      edgeLabel: edgeLabel,\n      edgeObj: e,\n      rank: vRank,\n    };\n    dummy = util.addDummyNode(g, 'edge', attrs, '_d');\n    if (vRank === labelRank) {\n      attrs.width = edgeLabel.width;\n      attrs.height = edgeLabel.height;\n      attrs.dummy = 'edge-label';\n      attrs.labelpos = edgeLabel.labelpos;\n    }\n    g.setEdge(v, dummy, { weight: edgeLabel.weight }, name);\n    if (i === 0) {\n      g.graph().dummyChains.push(dummy);\n    }\n    v = dummy;\n  }\n\n  g.setEdge(v, w, { weight: edgeLabel.weight }, name);\n}\n\nfunction undo(g) {\n  _.forEach(g.graph().dummyChains, function (v) {\n    var node = g.node(v);\n    var origLabel = node.edgeLabel;\n    var w;\n    g.setEdge(node.edgeObj, origLabel);\n    while (node.dummy) {\n      w = g.successors(v)[0];\n      g.removeNode(v);\n      origLabel.points.push({ x: node.x, y: node.y });\n      if (node.dummy === 'edge-label') {\n        origLabel.x = node.x;\n        origLabel.y = node.y;\n        origLabel.width = node.width;\n        origLabel.height = node.height;\n      }\n      v = w;\n      node = g.node(v);\n    }\n  });\n}\n", "import * as _ from 'lodash-es';\n\nexport { longestPath, slack };\n\n/*\n * Initializes ranks for the input graph using the longest path algorithm. This\n * algorithm scales well and is fast in practice, it yields rather poor\n * solutions. Nodes are pushed to the lowest layer possible, leaving the bottom\n * ranks wide and leaving edges longer than necessary. However, due to its\n * speed, this algorithm is good for getting an initial ranking that can be fed\n * into other algorithms.\n *\n * This algorithm does not normalize layers because it will be used by other\n * algorithms in most cases. If using this algorithm directly, be sure to\n * run normalize at the end.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG.\n *    2. Input graph node labels can be assigned properties.\n *\n * Post-conditions:\n *\n *    1. Each node will be assign an (unnormalized) \"rank\" property.\n */\nfunction longestPath(g) {\n  var visited = {};\n\n  function dfs(v) {\n    var label = g.node(v);\n    if (Object.prototype.hasOwnProperty.call(visited, v)) {\n      return label.rank;\n    }\n    visited[v] = true;\n\n    var rank = _.min(\n      _.map(g.outEdges(v), function (e) {\n        return dfs(e.w) - g.edge(e).minlen;\n      }),\n    );\n\n    if (\n      rank === Number.POSITIVE_INFINITY || // return value of _.map([]) for Lodash 3\n      rank === undefined || // return value of _.map([]) for Lodash 4\n      rank === null\n    ) {\n      // return value of _.map([null])\n      rank = 0;\n    }\n\n    return (label.rank = rank);\n  }\n\n  _.forEach(g.sources(), dfs);\n}\n\n/*\n * Returns the amount of slack for the given edge. The slack is defined as the\n * difference between the length of the edge and its minimum length.\n */\nfunction slack(g, e) {\n  return g.node(e.w).rank - g.node(e.v).rank - g.edge(e).minlen;\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport { slack } from './util.js';\n\nexport { feasibleTree };\n\n/*\n * Constructs a spanning tree with tight edges and adjusted the input node's\n * ranks to achieve this. A tight edge is one that is has a length that matches\n * its \"minlen\" attribute.\n *\n * The basic structure for this function is derived from <PERSON><PERSON><PERSON>, et al., \"A\n * Technique for Drawing Directed Graphs.\"\n *\n * Pre-conditions:\n *\n *    1. Graph must be a DAG.\n *    2. Graph must be connected.\n *    3. Graph must have at least one node.\n *    5. Graph nodes must have been previously assigned a \"rank\" property that\n *       respects the \"minlen\" property of incident edges.\n *    6. Graph edges must have a \"minlen\" property.\n *\n * Post-conditions:\n *\n *    - Graph nodes will have their rank adjusted to ensure that all edges are\n *      tight.\n *\n * Returns a tree (undirected graph) that is constructed using only \"tight\"\n * edges.\n */\nfunction feasibleTree(g) {\n  var t = new Graph({ directed: false });\n\n  // Choose arbitrary node from which to start our tree\n  var start = g.nodes()[0];\n  var size = g.nodeCount();\n  t.setNode(start, {});\n\n  var edge, delta;\n  while (tightTree(t, g) < size) {\n    edge = findMinSlackEdge(t, g);\n    delta = t.hasNode(edge.v) ? slack(g, edge) : -slack(g, edge);\n    shiftRanks(t, g, delta);\n  }\n\n  return t;\n}\n\n/*\n * Finds a maximal tree of tight edges and returns the number of nodes in the\n * tree.\n */\nfunction tightTree(t, g) {\n  function dfs(v) {\n    _.forEach(g.nodeEdges(v), function (e) {\n      var edgeV = e.v,\n        w = v === edgeV ? e.w : edgeV;\n      if (!t.hasNode(w) && !slack(g, e)) {\n        t.setNode(w, {});\n        t.setEdge(v, w, {});\n        dfs(w);\n      }\n    });\n  }\n\n  _.forEach(t.nodes(), dfs);\n  return t.nodeCount();\n}\n\n/*\n * Finds the edge with the smallest slack that is incident on tree and returns\n * it.\n */\nfunction findMinSlackEdge(t, g) {\n  return _.minBy(g.edges(), function (e) {\n    if (t.hasNode(e.v) !== t.hasNode(e.w)) {\n      return slack(g, e);\n    }\n  });\n}\n\nfunction shiftRanks(t, g, delta) {\n  _.forEach(t.nodes(), function (v) {\n    g.node(v).rank += delta;\n  });\n}\n", "import * as _ from 'lodash-es';\n\nexport { topsort, CycleException };\n\ntopsort.CycleException = CycleException;\n\nfunction topsort(g) {\n  var visited = {};\n  var stack = {};\n  var results = [];\n\n  function visit(node) {\n    if (Object.prototype.hasOwnProperty.call(stack, node)) {\n      throw new CycleException();\n    }\n\n    if (!Object.prototype.hasOwnProperty.call(visited, node)) {\n      stack[node] = true;\n      visited[node] = true;\n      _.each(g.predecessors(node), visit);\n      delete stack[node];\n      results.push(node);\n    }\n  }\n\n  _.each(g.sinks(), visit);\n\n  if (_.size(visited) !== g.nodeCount()) {\n    throw new CycleException();\n  }\n\n  return results;\n}\n\nfunction CycleException() {}\nCycleException.prototype = new Error(); // must be an instance of Error to pass testing\n", "import * as _ from 'lodash-es';\n\nexport { dfs };\n\n/*\n * A helper that preforms a pre- or post-order traversal on the input graph\n * and returns the nodes in the order they were visited. If the graph is\n * undirected then this algorithm will navigate using neighbors. If the graph\n * is directed then this algorithm will navigate using successors.\n *\n * Order must be one of \"pre\" or \"post\".\n */\nfunction dfs(g, vs, order) {\n  if (!_.isArray(vs)) {\n    vs = [vs];\n  }\n\n  var navigation = (g.isDirected() ? g.successors : g.neighbors).bind(g);\n\n  var acc = [];\n  var visited = {};\n  _.each(vs, function (v) {\n    if (!g.hasNode(v)) {\n      throw new Error('Graph does not have node: ' + v);\n    }\n\n    doDfs(g, v, order === 'post', visited, navigation, acc);\n  });\n  return acc;\n}\n\nfunction doDfs(g, v, postorder, visited, navigation, acc) {\n  if (!Object.prototype.hasOwnProperty.call(visited, v)) {\n    visited[v] = true;\n\n    if (!postorder) {\n      acc.push(v);\n    }\n    _.each(navigation(v), function (w) {\n      doDfs(g, w, postorder, visited, navigation, acc);\n    });\n    if (postorder) {\n      acc.push(v);\n    }\n  }\n}\n", "import { dfs } from './dfs.js';\n\nexport { postorder };\n\nfunction postorder(g, vs) {\n  return dfs(g, vs, 'post');\n}\n", "import { dfs } from './dfs.js';\n\nexport { preorder };\n\nfunction preorder(g, vs) {\n  return dfs(g, vs, 'pre');\n}\n", "import * as _ from 'lodash-es';\nimport * as alg from '../../graphlib/alg/index.js';\nimport { simplify } from '../util.js';\nimport { feasibleTree } from './feasible-tree.js';\nimport { longestPath, slack } from './util.js';\n\nexport { networkSimplex };\n\n// Expose some internals for testing purposes\nnetworkSimplex.initLowLimValues = initLowLimValues;\nnetworkSimplex.initCutValues = initCutValues;\nnetworkSimplex.calcCutValue = calcCutValue;\nnetworkSimplex.leaveEdge = leaveEdge;\nnetworkSimplex.enterEdge = enterEdge;\nnetworkSimplex.exchangeEdges = exchangeEdges;\n\n/*\n * The network simplex algorithm assigns ranks to each node in the input graph\n * and iteratively improves the ranking to reduce the length of edges.\n *\n * Preconditions:\n *\n *    1. The input graph must be a DAG.\n *    2. All nodes in the graph must have an object value.\n *    3. All edges in the graph must have \"minlen\" and \"weight\" attributes.\n *\n * Postconditions:\n *\n *    1. All nodes in the graph will have an assigned \"rank\" attribute that has\n *       been optimized by the network simplex algorithm. Ranks start at 0.\n *\n *\n * A rough sketch of the algorithm is as follows:\n *\n *    1. Assign initial ranks to each node. We use the longest path algorithm,\n *       which assigns ranks to the lowest position possible. In general this\n *       leads to very wide bottom ranks and unnecessarily long edges.\n *    2. Construct a feasible tight tree. A tight tree is one such that all\n *       edges in the tree have no slack (difference between length of edge\n *       and minlen for the edge). This by itself greatly improves the assigned\n *       rankings by shorting edges.\n *    3. Iteratively find edges that have negative cut values. Generally a\n *       negative cut value indicates that the edge could be removed and a new\n *       tree edge could be added to produce a more compact graph.\n *\n * Much of the algorithms here are derived from Gansner, et al., \"A Technique\n * for Drawing Directed Graphs.\" The structure of the file roughly follows the\n * structure of the overall algorithm.\n */\nfunction networkSimplex(g) {\n  g = simplify(g);\n  longestPath(g);\n  var t = feasibleTree(g);\n  initLowLimValues(t);\n  initCutValues(t, g);\n\n  var e, f;\n  while ((e = leaveEdge(t))) {\n    f = enterEdge(t, g, e);\n    exchangeEdges(t, g, e, f);\n  }\n}\n\n/*\n * Initializes cut values for all edges in the tree.\n */\nfunction initCutValues(t, g) {\n  var vs = alg.postorder(t, t.nodes());\n  vs = vs.slice(0, vs.length - 1);\n  _.forEach(vs, function (v) {\n    assignCutValue(t, g, v);\n  });\n}\n\nfunction assignCutValue(t, g, child) {\n  var childLab = t.node(child);\n  var parent = childLab.parent;\n  t.edge(child, parent).cutvalue = calcCutValue(t, g, child);\n}\n\n/*\n * Given the tight tree, its graph, and a child in the graph calculate and\n * return the cut value for the edge between the child and its parent.\n */\nfunction calcCutValue(t, g, child) {\n  var childLab = t.node(child);\n  var parent = childLab.parent;\n  // True if the child is on the tail end of the edge in the directed graph\n  var childIsTail = true;\n  // The graph's view of the tree edge we're inspecting\n  var graphEdge = g.edge(child, parent);\n  // The accumulated cut value for the edge between this node and its parent\n  var cutValue = 0;\n\n  if (!graphEdge) {\n    childIsTail = false;\n    graphEdge = g.edge(parent, child);\n  }\n\n  cutValue = graphEdge.weight;\n\n  _.forEach(g.nodeEdges(child), function (e) {\n    var isOutEdge = e.v === child,\n      other = isOutEdge ? e.w : e.v;\n\n    if (other !== parent) {\n      var pointsToHead = isOutEdge === childIsTail,\n        otherWeight = g.edge(e).weight;\n\n      cutValue += pointsToHead ? otherWeight : -otherWeight;\n      if (isTreeEdge(t, child, other)) {\n        var otherCutValue = t.edge(child, other).cutvalue;\n        cutValue += pointsToHead ? -otherCutValue : otherCutValue;\n      }\n    }\n  });\n\n  return cutValue;\n}\n\nfunction initLowLimValues(tree, root) {\n  if (arguments.length < 2) {\n    root = tree.nodes()[0];\n  }\n  dfsAssignLowLim(tree, {}, 1, root);\n}\n\nfunction dfsAssignLowLim(tree, visited, nextLim, v, parent) {\n  var low = nextLim;\n  var label = tree.node(v);\n\n  visited[v] = true;\n  _.forEach(tree.neighbors(v), function (w) {\n    if (!Object.prototype.hasOwnProperty.call(visited, w)) {\n      nextLim = dfsAssignLowLim(tree, visited, nextLim, w, v);\n    }\n  });\n\n  label.low = low;\n  label.lim = nextLim++;\n  if (parent) {\n    label.parent = parent;\n  } else {\n    // TODO should be able to remove this when we incrementally update low lim\n    delete label.parent;\n  }\n\n  return nextLim;\n}\n\nfunction leaveEdge(tree) {\n  return _.find(tree.edges(), function (e) {\n    return tree.edge(e).cutvalue < 0;\n  });\n}\n\nfunction enterEdge(t, g, edge) {\n  var v = edge.v;\n  var w = edge.w;\n\n  // For the rest of this function we assume that v is the tail and w is the\n  // head, so if we don't have this edge in the graph we should flip it to\n  // match the correct orientation.\n  if (!g.hasEdge(v, w)) {\n    v = edge.w;\n    w = edge.v;\n  }\n\n  var vLabel = t.node(v);\n  var wLabel = t.node(w);\n  var tailLabel = vLabel;\n  var flip = false;\n\n  // If the root is in the tail of the edge then we need to flip the logic that\n  // checks for the head and tail nodes in the candidates function below.\n  if (vLabel.lim > wLabel.lim) {\n    tailLabel = wLabel;\n    flip = true;\n  }\n\n  var candidates = _.filter(g.edges(), function (edge) {\n    return (\n      flip === isDescendant(t, t.node(edge.v), tailLabel) &&\n      flip !== isDescendant(t, t.node(edge.w), tailLabel)\n    );\n  });\n\n  return _.minBy(candidates, function (edge) {\n    return slack(g, edge);\n  });\n}\n\nfunction exchangeEdges(t, g, e, f) {\n  var v = e.v;\n  var w = e.w;\n  t.removeEdge(v, w);\n  t.setEdge(f.v, f.w, {});\n  initLowLimValues(t);\n  initCutValues(t, g);\n  updateRanks(t, g);\n}\n\nfunction updateRanks(t, g) {\n  var root = _.find(t.nodes(), function (v) {\n    return !g.node(v).parent;\n  });\n  var vs = alg.preorder(t, root);\n  vs = vs.slice(1);\n  _.forEach(vs, function (v) {\n    var parent = t.node(v).parent,\n      edge = g.edge(v, parent),\n      flipped = false;\n\n    if (!edge) {\n      edge = g.edge(parent, v);\n      flipped = true;\n    }\n\n    g.node(v).rank = g.node(parent).rank + (flipped ? edge.minlen : -edge.minlen);\n  });\n}\n\n/*\n * Returns true if the edge is in the tree.\n */\nfunction isTreeEdge(tree, u, v) {\n  return tree.hasEdge(u, v);\n}\n\n/*\n * Returns true if the specified node is descendant of the root node per the\n * assigned low and lim attributes in the tree.\n */\nfunction isDescendant(tree, vLabel, rootLabel) {\n  return rootLabel.low <= vLabel.lim && vLabel.lim <= rootLabel.lim;\n}\n", "import { feasibleTree } from './feasible-tree.js';\nimport { networkSimplex } from './network-simplex.js';\nimport { longestPath } from './util.js';\n\nexport { rank };\n\n/*\n * Assigns a rank to each node in the input graph that respects the \"minlen\"\n * constraint specified on edges between nodes.\n *\n * This basic structure is derived from <PERSON><PERSON><PERSON>, et al., \"A Technique for\n * Drawing Directed Graphs.\"\n *\n * Pre-conditions:\n *\n *    1. Graph must be a connected DAG\n *    2. Graph nodes must be objects\n *    3. Graph edges must have \"weight\" and \"minlen\" attributes\n *\n * Post-conditions:\n *\n *    1. Graph nodes will have a \"rank\" attribute based on the results of the\n *       algorithm. Ranks can start at any index (including negative), we'll\n *       fix them up later.\n */\nfunction rank(g) {\n  switch (g.graph().ranker) {\n    case 'network-simplex':\n      networkSimplexRanker(g);\n      break;\n    case 'tight-tree':\n      tightTreeRanker(g);\n      break;\n    case 'longest-path':\n      longestPathRanker(g);\n      break;\n    default:\n      networkSimplexRanker(g);\n  }\n}\n\n// A fast and simple ranker, but results are far from optimal.\nvar longestPathRanker = longestPath;\n\nfunction tightTreeRanker(g) {\n  longestPath(g);\n  feasibleTree(g);\n}\n\nfunction networkSimplexRanker(g) {\n  networkSimplex(g);\n}\n", "import * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { run, cleanup };\n\n/*\n * A nesting graph creates dummy nodes for the tops and bottoms of subgraphs,\n * adds appropriate edges to ensure that all cluster nodes are placed between\n * these boundries, and ensures that the graph is connected.\n *\n * In addition we ensure, through the use of the minlen property, that nodes\n * and subgraph border nodes to not end up on the same rank.\n *\n * Preconditions:\n *\n *    1. Input graph is a DAG\n *    2. Nodes in the input graph has a minlen attribute\n *\n * Postconditions:\n *\n *    1. Input graph is connected.\n *    2. Dummy nodes are added for the tops and bottoms of subgraphs.\n *    3. The minlen attribute for nodes is adjusted to ensure nodes do not\n *       get placed on the same rank as subgraph border nodes.\n *\n * The nesting graph idea comes from <PERSON><PERSON>, \"Layout of Compound Directed\n * Graphs.\"\n */\nfunction run(g) {\n  var root = util.addDummyNode(g, 'root', {}, '_root');\n  var depths = treeDepths(g);\n  var height = _.max(_.values(depths)) - 1; // Note: depths is an Object not an array\n  var nodeSep = 2 * height + 1;\n\n  g.graph().nestingRoot = root;\n\n  // Multiply minlen by nodeSep to align nodes on non-border ranks.\n  _.forEach(g.edges(), function (e) {\n    g.edge(e).minlen *= nodeSep;\n  });\n\n  // Calculate a weight that is sufficient to keep subgraphs vertically compact\n  var weight = sumWeights(g) + 1;\n\n  // Create border nodes and link them up\n  _.forEach(g.children(), function (child) {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n  });\n\n  // Save the multiplier for node layers for later removal of empty border\n  // layers.\n  g.graph().nodeRankFactor = nodeSep;\n}\n\nfunction dfs(g, root, nodeSep, weight, height, depths, v) {\n  var children = g.children(v);\n  if (!children.length) {\n    if (v !== root) {\n      g.setEdge(root, v, { weight: 0, minlen: nodeSep });\n    }\n    return;\n  }\n\n  var top = util.addBorderNode(g, '_bt');\n  var bottom = util.addBorderNode(g, '_bb');\n  var label = g.node(v);\n\n  g.setParent(top, v);\n  label.borderTop = top;\n  g.setParent(bottom, v);\n  label.borderBottom = bottom;\n\n  _.forEach(children, function (child) {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n\n    var childNode = g.node(child);\n    var childTop = childNode.borderTop ? childNode.borderTop : child;\n    var childBottom = childNode.borderBottom ? childNode.borderBottom : child;\n    var thisWeight = childNode.borderTop ? weight : 2 * weight;\n    var minlen = childTop !== childBottom ? 1 : height - depths[v] + 1;\n\n    g.setEdge(top, childTop, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true,\n    });\n\n    g.setEdge(childBottom, bottom, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true,\n    });\n  });\n\n  if (!g.parent(v)) {\n    g.setEdge(root, top, { weight: 0, minlen: height + depths[v] });\n  }\n}\n\nfunction treeDepths(g) {\n  var depths = {};\n  function dfs(v, depth) {\n    var children = g.children(v);\n    if (children && children.length) {\n      _.forEach(children, function (child) {\n        dfs(child, depth + 1);\n      });\n    }\n    depths[v] = depth;\n  }\n  _.forEach(g.children(), function (v) {\n    dfs(v, 1);\n  });\n  return depths;\n}\n\nfunction sumWeights(g) {\n  return _.reduce(\n    g.edges(),\n    function (acc, e) {\n      return acc + g.edge(e).weight;\n    },\n    0,\n  );\n}\n\nfunction cleanup(g) {\n  var graphLabel = g.graph();\n  g.removeNode(graphLabel.nestingRoot);\n  delete graphLabel.nestingRoot;\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.nestingEdge) {\n      g.removeEdge(e);\n    }\n  });\n}\n", "import * as _ from 'lodash-es';\n\nexport { addSubgraphConstraints };\n\nfunction addSubgraphConstraints(g, cg, vs) {\n  var prev = {},\n    rootPrev;\n\n  _.forEach(vs, function (v) {\n    var child = g.parent(v),\n      parent,\n      prevChild;\n    while (child) {\n      parent = g.parent(child);\n      if (parent) {\n        prevChild = prev[parent];\n        prev[parent] = child;\n      } else {\n        prevChild = rootPrev;\n        rootPrev = child;\n      }\n      if (prevChild && prevChild !== child) {\n        cg.setEdge(prevChild, child);\n        return;\n      }\n      child = parent;\n    }\n  });\n\n  /*\n  function dfs(v) {\n    var children = v ? g.children(v) : g.children();\n    if (children.length) {\n      var min = Number.POSITIVE_INFINITY,\n          subgraphs = [];\n      _.each(children, function(child) {\n        var childMin = dfs(child);\n        if (g.children(child).length) {\n          subgraphs.push({ v: child, order: childMin });\n        }\n        min = Math.min(min, childMin);\n      });\n      _.reduce(_.sortBy(subgraphs, \"order\"), function(prev, curr) {\n        cg.setEdge(prev.v, curr.v);\n        return curr;\n      });\n      return min;\n    }\n    return g.node(v).order;\n  }\n  dfs(undefined);\n  */\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\n\nexport { buildLayerGraph };\n\n/*\n * Constructs a graph that can be used to sort a layer of nodes. The graph will\n * contain all base and subgraph nodes from the request layer in their original\n * hierarchy and any edges that are incident on these nodes and are of the type\n * requested by the \"relationship\" parameter.\n *\n * Nodes from the requested rank that do not have parents are assigned a root\n * node in the output graph, which is set in the root graph attribute. This\n * makes it easy to walk the hierarchy of movable nodes during ordering.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG\n *    2. Base nodes in the input graph have a rank attribute\n *    3. Subgraph nodes in the input graph has minRank and maxRank attributes\n *    4. Edges have an assigned weight\n *\n * Post-conditions:\n *\n *    1. Output graph has all nodes in the movable rank with preserved\n *       hierarchy.\n *    2. Root nodes in the movable layer are made children of the node\n *       indicated by the root attribute of the graph.\n *    3. Non-movable nodes incident on movable nodes, selected by the\n *       relationship parameter, are included in the graph (without hierarchy).\n *    4. Edges incident on movable nodes, selected by the relationship\n *       parameter, are added to the output graph.\n *    5. The weights for copied edges are aggregated as need, since the output\n *       graph is not a multi-graph.\n */\nfunction buildLayerGraph(g, rank, relationship) {\n  var root = createRootNode(g),\n    result = new Graph({ compound: true })\n      .setGraph({ root: root })\n      .setDefaultNodeLabel(function (v) {\n        return g.node(v);\n      });\n\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v),\n      parent = g.parent(v);\n\n    if (node.rank === rank || (node.minRank <= rank && rank <= node.maxRank)) {\n      result.setNode(v);\n      result.setParent(v, parent || root);\n\n      // This assumes we have only short edges!\n      _.forEach(g[relationship](v), function (e) {\n        var u = e.v === v ? e.w : e.v,\n          edge = result.edge(u, v),\n          weight = !_.isUndefined(edge) ? edge.weight : 0;\n        result.setEdge(u, v, { weight: g.edge(e).weight + weight });\n      });\n\n      if (Object.prototype.hasOwnProperty.call(node, 'minRank')) {\n        result.setNode(v, {\n          borderLeft: node.borderLeft[rank],\n          borderRight: node.borderRight[rank],\n        });\n      }\n    }\n  });\n\n  return result;\n}\n\nfunction createRootNode(g) {\n  var v;\n  while (g.hasNode((v = _.uniqueId('_root'))));\n  return v;\n}\n", "import * as _ from 'lodash-es';\n\nexport { crossCount };\n\n/*\n * A function that takes a layering (an array of layers, each with an array of\n * ordererd nodes) and a graph and returns a weighted crossing count.\n *\n * Pre-conditions:\n *\n *    1. Input graph must be simple (not a multigraph), directed, and include\n *       only simple edges.\n *    2. Edges in the input graph must have assigned weights.\n *\n * Post-conditions:\n *\n *    1. The graph and layering matrix are left unchanged.\n *\n * This algorithm is derived from <PERSON><PERSON>, et al., \"Bilayer Cross Counting.\"\n */\nfunction crossCount(g, layering) {\n  var cc = 0;\n  for (var i = 1; i < layering.length; ++i) {\n    cc += twoLayerCrossCount(g, layering[i - 1], layering[i]);\n  }\n  return cc;\n}\n\nfunction twoLayerCrossCount(g, northLayer, southLayer) {\n  // Sort all of the edges between the north and south layers by their position\n  // in the north layer and then the south. Map these edges to the position of\n  // their head in the south layer.\n  var southPos = _.zipObject(\n    southLayer,\n    _.map(southLayer, function (v, i) {\n      return i;\n    }),\n  );\n  var southEntries = _.flatten(\n    _.map(northLayer, function (v) {\n      return _.sortBy(\n        _.map(g.outEdges(v), function (e) {\n          return { pos: southPos[e.w], weight: g.edge(e).weight };\n        }),\n        'pos',\n      );\n    }),\n  );\n\n  // Build the accumulator tree\n  var firstIndex = 1;\n  while (firstIndex < southLayer.length) firstIndex <<= 1;\n  var treeSize = 2 * firstIndex - 1;\n  firstIndex -= 1;\n  var tree = _.map(new Array(treeSize), function () {\n    return 0;\n  });\n\n  // Calculate the weighted crossings\n  var cc = 0;\n  _.forEach(\n    // @ts-expect-error\n    southEntries.forEach(function (entry) {\n      var index = entry.pos + firstIndex;\n      tree[index] += entry.weight;\n      var weightSum = 0;\n      // @ts-expect-error\n      while (index > 0) {\n        // @ts-expect-error\n        if (index % 2) {\n          weightSum += tree[index + 1];\n        }\n        // @ts-expect-error\n        index = (index - 1) >> 1;\n        tree[index] += entry.weight;\n      }\n      cc += entry.weight * weightSum;\n    }),\n  );\n\n  return cc;\n}\n", "import * as _ from 'lodash-es';\n\n/*\n * Assigns an initial order value for each node by performing a DFS search\n * starting from nodes in the first rank. Nodes are assigned an order in their\n * rank as they are first visited.\n *\n * This approach comes from <PERSON><PERSON><PERSON>, et al., \"A Technique for Drawing Directed\n * Graphs.\"\n *\n * Returns a layering matrix with an array per layer and each layer sorted by\n * the order of its nodes.\n */\nexport function initOrder(g) {\n  var visited = {};\n  var simpleNodes = _.filter(g.nodes(), function (v) {\n    return !g.children(v).length;\n  });\n  var maxRank = _.max(\n    _.map(simpleNodes, function (v) {\n      return g.node(v).rank;\n    }),\n  );\n  var layers = _.map(_.range(maxRank + 1), function () {\n    return [];\n  });\n\n  function dfs(v) {\n    if (_.has(visited, v)) return;\n    visited[v] = true;\n    var node = g.node(v);\n    layers[node.rank].push(v);\n    _.forEach(g.successors(v), dfs);\n  }\n\n  var orderedVs = _.sortBy(simpleNodes, function (v) {\n    return g.node(v).rank;\n  });\n  _.forEach(orderedVs, dfs);\n\n  return layers;\n}\n", "import * as _ from 'lodash-es';\n\nexport { barycenter };\n\nfunction barycenter(g, movable) {\n  return _.map(movable, function (v) {\n    var inV = g.inEdges(v);\n    if (!inV.length) {\n      return { v: v };\n    } else {\n      var result = _.reduce(\n        inV,\n        function (acc, e) {\n          var edge = g.edge(e),\n            nodeU = g.node(e.v);\n          return {\n            sum: acc.sum + edge.weight * nodeU.order,\n            weight: acc.weight + edge.weight,\n          };\n        },\n        { sum: 0, weight: 0 },\n      );\n\n      return {\n        v: v,\n        barycenter: result.sum / result.weight,\n        weight: result.weight,\n      };\n    }\n  });\n}\n", "import * as _ from 'lodash-es';\n\nexport { resolveConflicts };\n\n/*\n * Given a list of entries of the form {v, barycenter, weight} and a\n * constraint graph this function will resolve any conflicts between the\n * constraint graph and the barycenters for the entries. If the barycenters for\n * an entry would violate a constraint in the constraint graph then we coalesce\n * the nodes in the conflict into a new node that respects the contraint and\n * aggregates barycenter and weight information.\n *\n * This implementation is based on the description in Forster, \"A Fast and\n * Simple Hueristic for Constrained Two-Level Crossing Reduction,\" thought it\n * differs in some specific details.\n *\n * Pre-conditions:\n *\n *    1. Each entry has the form {v, barycenter, weight}, or if the node has\n *       no barycenter, then {v}.\n *\n * Returns:\n *\n *    A new list of entries of the form {vs, i, barycenter, weight}. The list\n *    `vs` may either be a singleton or it may be an aggregation of nodes\n *    ordered such that they do not violate constraints from the constraint\n *    graph. The property `i` is the lowest original index of any of the\n *    elements in `vs`.\n */\nfunction resolveConflicts(entries, cg) {\n  var mappedEntries = {};\n  _.forEach(entries, function (entry, i) {\n    var tmp = (mappedEntries[entry.v] = {\n      indegree: 0,\n      in: [],\n      out: [],\n      vs: [entry.v],\n      i: i,\n    });\n    if (!_.isUndefined(entry.barycenter)) {\n      // @ts-expect-error\n      tmp.barycenter = entry.barycenter;\n      // @ts-expect-error\n      tmp.weight = entry.weight;\n    }\n  });\n\n  _.forEach(cg.edges(), function (e) {\n    var entryV = mappedEntries[e.v];\n    var entryW = mappedEntries[e.w];\n    if (!_.isUndefined(entryV) && !_.isUndefined(entryW)) {\n      entryW.indegree++;\n      entryV.out.push(mappedEntries[e.w]);\n    }\n  });\n\n  var sourceSet = _.filter(mappedEntries, function (entry) {\n    // @ts-expect-error\n    return !entry.indegree;\n  });\n\n  return doResolveConflicts(sourceSet);\n}\n\nfunction doResolveConflicts(sourceSet) {\n  var entries = [];\n\n  function handleIn(vEntry) {\n    return function (uEntry) {\n      if (uEntry.merged) {\n        return;\n      }\n      if (\n        _.isUndefined(uEntry.barycenter) ||\n        _.isUndefined(vEntry.barycenter) ||\n        uEntry.barycenter >= vEntry.barycenter\n      ) {\n        mergeEntries(vEntry, uEntry);\n      }\n    };\n  }\n\n  function handleOut(vEntry) {\n    return function (wEntry) {\n      wEntry['in'].push(vEntry);\n      if (--wEntry.indegree === 0) {\n        sourceSet.push(wEntry);\n      }\n    };\n  }\n\n  while (sourceSet.length) {\n    var entry = sourceSet.pop();\n    entries.push(entry);\n    _.forEach(entry['in'].reverse(), handleIn(entry));\n    _.forEach(entry.out, handleOut(entry));\n  }\n\n  return _.map(\n    _.filter(entries, function (entry) {\n      return !entry.merged;\n    }),\n    function (entry) {\n      return _.pick(entry, ['vs', 'i', 'barycenter', 'weight']);\n    },\n  );\n}\n\nfunction mergeEntries(target, source) {\n  var sum = 0;\n  var weight = 0;\n\n  if (target.weight) {\n    sum += target.barycenter * target.weight;\n    weight += target.weight;\n  }\n\n  if (source.weight) {\n    sum += source.barycenter * source.weight;\n    weight += source.weight;\n  }\n\n  target.vs = source.vs.concat(target.vs);\n  target.barycenter = sum / weight;\n  target.weight = weight;\n  target.i = Math.min(source.i, target.i);\n  source.merged = true;\n}\n", "import * as _ from 'lodash-es';\nimport * as util from '../util.js';\n\nexport { sort };\n\nfunction sort(entries, biasRight) {\n  var parts = util.partition(entries, function (entry) {\n    return Object.prototype.hasOwnProperty.call(entry, 'barycenter');\n  });\n  var sortable = parts.lhs,\n    unsortable = _.sortBy(parts.rhs, function (entry) {\n      return -entry.i;\n    }),\n    vs = [],\n    sum = 0,\n    weight = 0,\n    vsIndex = 0;\n\n  sortable.sort(compareWithBias(!!biasRight));\n\n  vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n\n  _.forEach(sortable, function (entry) {\n    vsIndex += entry.vs.length;\n    vs.push(entry.vs);\n    sum += entry.barycenter * entry.weight;\n    weight += entry.weight;\n    vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n  });\n\n  var result = { vs: _.flatten(vs) };\n  if (weight) {\n    result.barycenter = sum / weight;\n    result.weight = weight;\n  }\n  return result;\n}\n\nfunction consumeUnsortable(vs, unsortable, index) {\n  var last;\n  while (unsortable.length && (last = _.last(unsortable)).i <= index) {\n    unsortable.pop();\n    vs.push(last.vs);\n    index++;\n  }\n  return index;\n}\n\nfunction compareWithBias(bias) {\n  return function (entryV, entryW) {\n    if (entryV.barycenter < entryW.barycenter) {\n      return -1;\n    } else if (entryV.barycenter > entryW.barycenter) {\n      return 1;\n    }\n\n    return !bias ? entryV.i - entryW.i : entryW.i - entryV.i;\n  };\n}\n", "import * as _ from 'lodash-es';\nimport { barycenter } from './barycenter.js';\nimport { resolveConflicts } from './resolve-conflicts.js';\nimport { sort } from './sort.js';\n\nexport { sortSubgraph };\n\nfunction sortSubgraph(g, v, cg, biasRight) {\n  var movable = g.children(v);\n  var node = g.node(v);\n  var bl = node ? node.borderLeft : undefined;\n  var br = node ? node.borderRight : undefined;\n  var subgraphs = {};\n\n  if (bl) {\n    movable = _.filter(movable, function (w) {\n      return w !== bl && w !== br;\n    });\n  }\n\n  var barycenters = barycenter(g, movable);\n  _.forEach(barycenters, function (entry) {\n    if (g.children(entry.v).length) {\n      var subgraphResult = sortSubgraph(g, entry.v, cg, biasRight);\n      subgraphs[entry.v] = subgraphResult;\n      if (Object.prototype.hasOwnProperty.call(subgraphResult, 'barycenter')) {\n        mergeBarycenters(entry, subgraphResult);\n      }\n    }\n  });\n\n  var entries = resolveConflicts(barycenters, cg);\n  expandSubgraphs(entries, subgraphs);\n\n  var result = sort(entries, biasRight);\n\n  if (bl) {\n    result.vs = _.flatten([bl, result.vs, br]);\n    if (g.predecessors(bl).length) {\n      var blPred = g.node(g.predecessors(bl)[0]),\n        brPred = g.node(g.predecessors(br)[0]);\n      if (!Object.prototype.hasOwnProperty.call(result, 'barycenter')) {\n        result.barycenter = 0;\n        result.weight = 0;\n      }\n      result.barycenter =\n        (result.barycenter * result.weight + blPred.order + brPred.order) / (result.weight + 2);\n      result.weight += 2;\n    }\n  }\n\n  return result;\n}\n\nfunction expandSubgraphs(entries, subgraphs) {\n  _.forEach(entries, function (entry) {\n    entry.vs = _.flatten(\n      entry.vs.map(function (v) {\n        if (subgraphs[v]) {\n          return subgraphs[v].vs;\n        }\n        return v;\n      }),\n    );\n  });\n}\n\nfunction mergeBarycenters(target, other) {\n  if (!_.isUndefined(target.barycenter)) {\n    target.barycenter =\n      (target.barycenter * target.weight + other.barycenter * other.weight) /\n      (target.weight + other.weight);\n    target.weight += other.weight;\n  } else {\n    target.barycenter = other.barycenter;\n    target.weight = other.weight;\n  }\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport * as util from '../util.js';\nimport { addSubgraphConstraints } from './add-subgraph-constraints.js';\nimport { buildLayerGraph } from './build-layer-graph.js';\nimport { crossCount } from './cross-count.js';\nimport { initOrder } from './init-order.js';\nimport { sortSubgraph } from './sort-subgraph.js';\n\nexport { order };\n\n/*\n * Applies heuristics to minimize edge crossings in the graph and sets the best\n * order solution as an order attribute on each node.\n *\n * Pre-conditions:\n *\n *    1. Graph must be DAG\n *    2. Graph nodes must be objects with a \"rank\" attribute\n *    3. Graph edges must have the \"weight\" attribute\n *\n * Post-conditions:\n *\n *    1. Graph nodes will have an \"order\" attribute based on the results of the\n *       algorithm.\n */\nfunction order(g) {\n  var maxRank = util.maxRank(g),\n    downLayerGraphs = buildLayerGraphs(g, _.range(1, maxRank + 1), 'inEdges'),\n    upLayerGraphs = buildLayerGraphs(g, _.range(maxRank - 1, -1, -1), 'outEdges');\n\n  var layering = initOrder(g);\n  assignOrder(g, layering);\n\n  var bestCC = Number.POSITIVE_INFINITY,\n    best;\n\n  for (var i = 0, lastBest = 0; lastBest < 4; ++i, ++lastBest) {\n    sweepLayerGraphs(i % 2 ? downLayerGraphs : upLayerGraphs, i % 4 >= 2);\n\n    layering = util.buildLayerMatrix(g);\n    var cc = crossCount(g, layering);\n    if (cc < bestCC) {\n      lastBest = 0;\n      best = _.cloneDeep(layering);\n      bestCC = cc;\n    }\n  }\n\n  assignOrder(g, best);\n}\n\nfunction buildLayerGraphs(g, ranks, relationship) {\n  return _.map(ranks, function (rank) {\n    return buildLayerGraph(g, rank, relationship);\n  });\n}\n\nfunction sweepLayerGraphs(layerGraphs, biasRight) {\n  var cg = new Graph();\n  _.forEach(layerGraphs, function (lg) {\n    var root = lg.graph().root;\n    var sorted = sortSubgraph(lg, root, cg, biasRight);\n    _.forEach(sorted.vs, function (v, i) {\n      lg.node(v).order = i;\n    });\n    addSubgraphConstraints(lg, cg, sorted.vs);\n  });\n}\n\nfunction assignOrder(g, layering) {\n  _.forEach(layering, function (layer) {\n    _.forEach(layer, function (v, i) {\n      g.node(v).order = i;\n    });\n  });\n}\n", "import * as _ from 'lodash-es';\n\nexport { parentDummyChains };\n\nfunction parentDummyChains(g) {\n  var postorderNums = postorder(g);\n\n  _.forEach(g.graph().dummyChains, function (v) {\n    var node = g.node(v);\n    var edgeObj = node.edgeObj;\n    var pathData = findPath(g, postorderNums, edgeObj.v, edgeObj.w);\n    var path = pathData.path;\n    var lca = pathData.lca;\n    var pathIdx = 0;\n    var pathV = path[pathIdx];\n    var ascending = true;\n\n    while (v !== edgeObj.w) {\n      node = g.node(v);\n\n      if (ascending) {\n        while ((pathV = path[pathIdx]) !== lca && g.node(pathV).maxRank < node.rank) {\n          pathIdx++;\n        }\n\n        if (pathV === lca) {\n          ascending = false;\n        }\n      }\n\n      if (!ascending) {\n        while (\n          pathIdx < path.length - 1 &&\n          g.node((pathV = path[pathIdx + 1])).minRank <= node.rank\n        ) {\n          pathIdx++;\n        }\n        pathV = path[pathIdx];\n      }\n\n      g.setParent(v, pathV);\n      v = g.successors(v)[0];\n    }\n  });\n}\n\n// Find a path from v to w through the lowest common ancestor (LCA). Return the\n// full path and the LCA.\nfunction findPath(g, postorderNums, v, w) {\n  var vPath = [];\n  var wPath = [];\n  var low = Math.min(postorderNums[v].low, postorderNums[w].low);\n  var lim = Math.max(postorderNums[v].lim, postorderNums[w].lim);\n  var parent;\n  var lca;\n\n  // Traverse up from v to find the LCA\n  parent = v;\n  do {\n    parent = g.parent(parent);\n    vPath.push(parent);\n  } while (parent && (postorderNums[parent].low > low || lim > postorderNums[parent].lim));\n  lca = parent;\n\n  // Traverse from w to LCA\n  parent = w;\n  while ((parent = g.parent(parent)) !== lca) {\n    wPath.push(parent);\n  }\n\n  return { path: vPath.concat(wPath.reverse()), lca: lca };\n}\n\nfunction postorder(g) {\n  var result = {};\n  var lim = 0;\n\n  function dfs(v) {\n    var low = lim;\n    _.forEach(g.children(v), dfs);\n    result[v] = { low: low, lim: lim++ };\n  }\n  _.forEach(g.children(), dfs);\n\n  return result;\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport * as util from '../util.js';\n\n/*\n * This module provides coordinate assignment based on <PERSON><PERSON> and <PERSON>, \"Fast\n * and Simple Horizontal Coordinate Assignment.\"\n */\n\nexport {\n  positionX,\n  findType1Conflicts,\n  findType2Conflicts,\n  addConflict,\n  hasConflict,\n  verticalAlignment,\n  horizontalCompaction,\n  alignCoordinates,\n  findSmallestWidthAlignment,\n  balance,\n};\n\n/*\n * Marks all edges in the graph with a type-1 conflict with the \"type1Conflict\"\n * property. A type-1 conflict is one where a non-inner segment crosses an\n * inner segment. An inner segment is an edge with both incident nodes marked\n * with the \"dummy\" property.\n *\n * This algorithm scans layer by layer, starting with the second, for type-1\n * conflicts between the current layer and the previous layer. For each layer\n * it scans the nodes from left to right until it reaches one that is incident\n * on an inner segment. It then scans predecessors to determine if they have\n * edges that cross that inner segment. At the end a final scan is done for all\n * nodes on the current rank to see if they cross the last visited inner\n * segment.\n *\n * This algorithm (safely) assumes that a dummy node will only be incident on a\n * single node in the layers being scanned.\n */\nfunction findType1Conflicts(g, layering) {\n  var conflicts = {};\n\n  function visitLayer(prevLayer, layer) {\n    var // last visited node in the previous layer that is incident on an inner\n      // segment.\n      k0 = 0,\n      // Tracks the last node in this layer scanned for crossings with a type-1\n      // segment.\n      scanPos = 0,\n      prevLayerLength = prevLayer.length,\n      lastNode = _.last(layer);\n\n    _.forEach(layer, function (v, i) {\n      var w = findOtherInnerSegmentNode(g, v),\n        k1 = w ? g.node(w).order : prevLayerLength;\n\n      if (w || v === lastNode) {\n        _.forEach(layer.slice(scanPos, i + 1), function (scanNode) {\n          _.forEach(g.predecessors(scanNode), function (u) {\n            var uLabel = g.node(u),\n              uPos = uLabel.order;\n            if ((uPos < k0 || k1 < uPos) && !(uLabel.dummy && g.node(scanNode).dummy)) {\n              addConflict(conflicts, u, scanNode);\n            }\n          });\n        });\n        // @ts-expect-error\n        scanPos = i + 1;\n        k0 = k1;\n      }\n    });\n\n    return layer;\n  }\n\n  _.reduce(layering, visitLayer);\n  return conflicts;\n}\n\nfunction findType2Conflicts(g, layering) {\n  var conflicts = {};\n\n  function scan(south, southPos, southEnd, prevNorthBorder, nextNorthBorder) {\n    var v;\n    _.forEach(_.range(southPos, southEnd), function (i) {\n      v = south[i];\n      if (g.node(v).dummy) {\n        _.forEach(g.predecessors(v), function (u) {\n          var uNode = g.node(u);\n          if (uNode.dummy && (uNode.order < prevNorthBorder || uNode.order > nextNorthBorder)) {\n            addConflict(conflicts, u, v);\n          }\n        });\n      }\n    });\n  }\n\n  function visitLayer(north, south) {\n    var prevNorthPos = -1,\n      nextNorthPos,\n      southPos = 0;\n\n    _.forEach(south, function (v, southLookahead) {\n      if (g.node(v).dummy === 'border') {\n        var predecessors = g.predecessors(v);\n        if (predecessors.length) {\n          nextNorthPos = g.node(predecessors[0]).order;\n          scan(south, southPos, southLookahead, prevNorthPos, nextNorthPos);\n          // @ts-expect-error\n          southPos = southLookahead;\n          prevNorthPos = nextNorthPos;\n        }\n      }\n      scan(south, southPos, south.length, nextNorthPos, north.length);\n    });\n\n    return south;\n  }\n\n  _.reduce(layering, visitLayer);\n  return conflicts;\n}\n\nfunction findOtherInnerSegmentNode(g, v) {\n  if (g.node(v).dummy) {\n    return _.find(g.predecessors(v), function (u) {\n      return g.node(u).dummy;\n    });\n  }\n}\n\nfunction addConflict(conflicts, v, w) {\n  if (v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n\n  var conflictsV = conflicts[v];\n  if (!conflictsV) {\n    conflicts[v] = conflictsV = {};\n  }\n  conflictsV[w] = true;\n}\n\nfunction hasConflict(conflicts, v, w) {\n  if (v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return !!conflicts[v] && Object.prototype.hasOwnProperty.call(conflicts[v], w);\n}\n\n/*\n * Try to align nodes into vertical \"blocks\" where possible. This algorithm\n * attempts to align a node with one of its median neighbors. If the edge\n * connecting a neighbor is a type-1 conflict then we ignore that possibility.\n * If a previous node has already formed a block with a node after the node\n * we're trying to form a block with, we also ignore that possibility - our\n * blocks would be split in that scenario.\n */\nfunction verticalAlignment(g, layering, conflicts, neighborFn) {\n  var root = {},\n    align = {},\n    pos = {};\n\n  // We cache the position here based on the layering because the graph and\n  // layering may be out of sync. The layering matrix is manipulated to\n  // generate different extreme alignments.\n  _.forEach(layering, function (layer) {\n    _.forEach(layer, function (v, order) {\n      root[v] = v;\n      align[v] = v;\n      pos[v] = order;\n    });\n  });\n\n  _.forEach(layering, function (layer) {\n    var prevIdx = -1;\n    _.forEach(layer, function (v) {\n      var ws = neighborFn(v);\n      if (ws.length) {\n        ws = _.sortBy(ws, function (w) {\n          return pos[w];\n        });\n        var mp = (ws.length - 1) / 2;\n        for (var i = Math.floor(mp), il = Math.ceil(mp); i <= il; ++i) {\n          var w = ws[i];\n          if (align[v] === v && prevIdx < pos[w] && !hasConflict(conflicts, v, w)) {\n            align[w] = v;\n            align[v] = root[v] = root[w];\n            prevIdx = pos[w];\n          }\n        }\n      }\n    });\n  });\n\n  return { root: root, align: align };\n}\n\nfunction horizontalCompaction(g, layering, root, align, reverseSep) {\n  // This portion of the algorithm differs from BK due to a number of problems.\n  // Instead of their algorithm we construct a new block graph and do two\n  // sweeps. The first sweep places blocks with the smallest possible\n  // coordinates. The second sweep removes unused space by moving blocks to the\n  // greatest coordinates without violating separation.\n  var xs = {},\n    blockG = buildBlockGraph(g, layering, root, reverseSep),\n    borderType = reverseSep ? 'borderLeft' : 'borderRight';\n\n  function iterate(setXsFunc, nextNodesFunc) {\n    var stack = blockG.nodes();\n    var elem = stack.pop();\n    var visited = {};\n    while (elem) {\n      if (visited[elem]) {\n        setXsFunc(elem);\n      } else {\n        visited[elem] = true;\n        stack.push(elem);\n        stack = stack.concat(nextNodesFunc(elem));\n      }\n\n      elem = stack.pop();\n    }\n  }\n\n  // First pass, assign smallest coordinates\n  function pass1(elem) {\n    xs[elem] = blockG.inEdges(elem).reduce(function (acc, e) {\n      return Math.max(acc, xs[e.v] + blockG.edge(e));\n    }, 0);\n  }\n\n  // Second pass, assign greatest coordinates\n  function pass2(elem) {\n    var min = blockG.outEdges(elem).reduce(function (acc, e) {\n      return Math.min(acc, xs[e.w] - blockG.edge(e));\n    }, Number.POSITIVE_INFINITY);\n\n    var node = g.node(elem);\n    if (min !== Number.POSITIVE_INFINITY && node.borderType !== borderType) {\n      xs[elem] = Math.max(xs[elem], min);\n    }\n  }\n\n  iterate(pass1, blockG.predecessors.bind(blockG));\n  iterate(pass2, blockG.successors.bind(blockG));\n\n  // Assign x coordinates to all nodes\n  _.forEach(align, function (v) {\n    xs[v] = xs[root[v]];\n  });\n\n  return xs;\n}\n\nfunction buildBlockGraph(g, layering, root, reverseSep) {\n  var blockGraph = new Graph(),\n    graphLabel = g.graph(),\n    sepFn = sep(graphLabel.nodesep, graphLabel.edgesep, reverseSep);\n\n  _.forEach(layering, function (layer) {\n    var u;\n    _.forEach(layer, function (v) {\n      var vRoot = root[v];\n      blockGraph.setNode(vRoot);\n      if (u) {\n        var uRoot = root[u],\n          prevMax = blockGraph.edge(uRoot, vRoot);\n        blockGraph.setEdge(uRoot, vRoot, Math.max(sepFn(g, v, u), prevMax || 0));\n      }\n      u = v;\n    });\n  });\n\n  return blockGraph;\n}\n\n/*\n * Returns the alignment that has the smallest width of the given alignments.\n */\nfunction findSmallestWidthAlignment(g, xss) {\n  return _.minBy(_.values(xss), function (xs) {\n    var max = Number.NEGATIVE_INFINITY;\n    var min = Number.POSITIVE_INFINITY;\n\n    _.forIn(xs, function (x, v) {\n      var halfWidth = width(g, v) / 2;\n\n      max = Math.max(x + halfWidth, max);\n      min = Math.min(x - halfWidth, min);\n    });\n\n    return max - min;\n  });\n}\n\n/*\n * Align the coordinates of each of the layout alignments such that\n * left-biased alignments have their minimum coordinate at the same point as\n * the minimum coordinate of the smallest width alignment and right-biased\n * alignments have their maximum coordinate at the same point as the maximum\n * coordinate of the smallest width alignment.\n */\nfunction alignCoordinates(xss, alignTo) {\n  var alignToVals = _.values(alignTo),\n    alignToMin = _.min(alignToVals),\n    alignToMax = _.max(alignToVals);\n\n  _.forEach(['u', 'd'], function (vert) {\n    _.forEach(['l', 'r'], function (horiz) {\n      var alignment = vert + horiz,\n        xs = xss[alignment],\n        delta;\n      if (xs === alignTo) return;\n\n      var xsVals = _.values(xs);\n      delta = horiz === 'l' ? alignToMin - _.min(xsVals) : alignToMax - _.max(xsVals);\n\n      if (delta) {\n        xss[alignment] = _.mapValues(xs, function (x) {\n          return x + delta;\n        });\n      }\n    });\n  });\n}\n\nfunction balance(xss, align) {\n  return _.mapValues(xss.ul, function (ignore, v) {\n    if (align) {\n      return xss[align.toLowerCase()][v];\n    } else {\n      var xs = _.sortBy(_.map(xss, v));\n      return (xs[1] + xs[2]) / 2;\n    }\n  });\n}\n\nfunction positionX(g) {\n  var layering = util.buildLayerMatrix(g);\n  var conflicts = _.merge(findType1Conflicts(g, layering), findType2Conflicts(g, layering));\n\n  var xss = {};\n  var adjustedLayering;\n  _.forEach(['u', 'd'], function (vert) {\n    adjustedLayering = vert === 'u' ? layering : _.values(layering).reverse();\n    _.forEach(['l', 'r'], function (horiz) {\n      if (horiz === 'r') {\n        adjustedLayering = _.map(adjustedLayering, function (inner) {\n          return _.values(inner).reverse();\n        });\n      }\n\n      var neighborFn = (vert === 'u' ? g.predecessors : g.successors).bind(g);\n      var align = verticalAlignment(g, adjustedLayering, conflicts, neighborFn);\n      var xs = horizontalCompaction(g, adjustedLayering, align.root, align.align, horiz === 'r');\n      if (horiz === 'r') {\n        xs = _.mapValues(xs, function (x) {\n          return -x;\n        });\n      }\n      xss[vert + horiz] = xs;\n    });\n  });\n\n  var smallestWidth = findSmallestWidthAlignment(g, xss);\n  alignCoordinates(xss, smallestWidth);\n  return balance(xss, g.graph().align);\n}\n\nfunction sep(nodeSep, edgeSep, reverseSep) {\n  return function (g, v, w) {\n    var vLabel = g.node(v);\n    var wLabel = g.node(w);\n    var sum = 0;\n    var delta;\n\n    sum += vLabel.width / 2;\n    if (Object.prototype.hasOwnProperty.call(vLabel, 'labelpos')) {\n      switch (vLabel.labelpos.toLowerCase()) {\n        case 'l':\n          delta = -vLabel.width / 2;\n          break;\n        case 'r':\n          delta = vLabel.width / 2;\n          break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n\n    sum += (vLabel.dummy ? edgeSep : nodeSep) / 2;\n    sum += (wLabel.dummy ? edgeSep : nodeSep) / 2;\n\n    sum += wLabel.width / 2;\n    if (Object.prototype.hasOwnProperty.call(wLabel, 'labelpos')) {\n      switch (wLabel.labelpos.toLowerCase()) {\n        case 'l':\n          delta = wLabel.width / 2;\n          break;\n        case 'r':\n          delta = -wLabel.width / 2;\n          break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n\n    return sum;\n  };\n}\n\nfunction width(g, v) {\n  return g.node(v).width;\n}\n", "import * as _ from 'lodash-es';\nimport * as util from '../util.js';\nimport { positionX } from './bk.js';\n\nexport { position };\n\nfunction position(g) {\n  g = util.asNonCompoundGraph(g);\n\n  positionY(g);\n  _.forOwn(positionX(g), function (x, v) {\n    g.node(v).x = x;\n  });\n}\n\nfunction positionY(g) {\n  var layering = util.buildLayerMatrix(g);\n  var rankSep = g.graph().ranksep;\n  var prevY = 0;\n  _.forEach(layering, function (layer) {\n    var maxHeight = _.max(\n      _.map(layer, function (v) {\n        return g.node(v).height;\n      }),\n    );\n    _.forEach(layer, function (v) {\n      g.node(v).y = prevY + maxHeight / 2;\n    });\n    prevY += maxHeight + rankSep;\n  });\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\nimport { addBorderSegments } from './add-border-segments.js';\nimport * as coordinateSystem from './coordinate-system.js';\nimport * as acyclic from './acyclic.js';\nimport * as normalize from './normalize.js';\nimport { rank } from './rank/index.js';\nimport * as nestingGraph from './nesting-graph.js';\nimport { order } from './order/index.js';\nimport { parentDummyChains } from './parent-dummy-chains.js';\nimport { position } from './position/index.js';\nimport * as util from './util.js';\n\nexport { layout };\n\nfunction layout(g, opts) {\n  var time = opts && opts.debugTiming ? util.time : util.notime;\n  time('layout', () => {\n    var layoutGraph = time('  buildLayoutGraph', () => buildLayoutGraph(g));\n    time('  runLayout', () => runLayout(layoutGraph, time));\n    time('  updateInputGraph', () => updateInputGraph(g, layoutGraph));\n  });\n}\n\nfunction runLayout(g, time) {\n  time('    makeSpaceForEdgeLabels', () => makeSpaceForEdgeLabels(g));\n  time('    removeSelfEdges', () => removeSelfEdges(g));\n  time('    acyclic', () => acyclic.run(g));\n  time('    nestingGraph.run', () => nestingGraph.run(g));\n  time('    rank', () => rank(util.asNonCompoundGraph(g)));\n  time('    injectEdgeLabelProxies', () => injectEdgeLabelProxies(g));\n  time('    removeEmptyRanks', () => util.removeEmptyRanks(g));\n  time('    nestingGraph.cleanup', () => nestingGraph.cleanup(g));\n  time('    normalizeRanks', () => util.normalizeRanks(g));\n  time('    assignRankMinMax', () => assignRankMinMax(g));\n  time('    removeEdgeLabelProxies', () => removeEdgeLabelProxies(g));\n  time('    normalize.run', () => normalize.run(g));\n  time('    parentDummyChains', () => parentDummyChains(g));\n  time('    addBorderSegments', () => addBorderSegments(g));\n  time('    order', () => order(g));\n  time('    insertSelfEdges', () => insertSelfEdges(g));\n  time('    adjustCoordinateSystem', () => coordinateSystem.adjust(g));\n  time('    position', () => position(g));\n  time('    positionSelfEdges', () => positionSelfEdges(g));\n  time('    removeBorderNodes', () => removeBorderNodes(g));\n  time('    normalize.undo', () => normalize.undo(g));\n  time('    fixupEdgeLabelCoords', () => fixupEdgeLabelCoords(g));\n  time('    undoCoordinateSystem', () => coordinateSystem.undo(g));\n  time('    translateGraph', () => translateGraph(g));\n  time('    assignNodeIntersects', () => assignNodeIntersects(g));\n  time('    reversePoints', () => reversePointsForReversedEdges(g));\n  time('    acyclic.undo', () => acyclic.undo(g));\n}\n\n/*\n * Copies final layout information from the layout graph back to the input\n * graph. This process only copies whitelisted attributes from the layout graph\n * to the input graph, so it serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction updateInputGraph(inputGraph, layoutGraph) {\n  _.forEach(inputGraph.nodes(), function (v) {\n    var inputLabel = inputGraph.node(v);\n    var layoutLabel = layoutGraph.node(v);\n\n    if (inputLabel) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n\n      if (layoutGraph.children(v).length) {\n        inputLabel.width = layoutLabel.width;\n        inputLabel.height = layoutLabel.height;\n      }\n    }\n  });\n\n  _.forEach(inputGraph.edges(), function (e) {\n    var inputLabel = inputGraph.edge(e);\n    var layoutLabel = layoutGraph.edge(e);\n\n    inputLabel.points = layoutLabel.points;\n    if (Object.prototype.hasOwnProperty.call(layoutLabel, 'x')) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n    }\n  });\n\n  inputGraph.graph().width = layoutGraph.graph().width;\n  inputGraph.graph().height = layoutGraph.graph().height;\n}\n\nvar graphNumAttrs = ['nodesep', 'edgesep', 'ranksep', 'marginx', 'marginy'];\nvar graphDefaults = { ranksep: 50, edgesep: 20, nodesep: 50, rankdir: 'tb' };\nvar graphAttrs = ['acyclicer', 'ranker', 'rankdir', 'align'];\nvar nodeNumAttrs = ['width', 'height'];\nvar nodeDefaults = { width: 0, height: 0 };\nvar edgeNumAttrs = ['minlen', 'weight', 'width', 'height', 'labeloffset'];\nvar edgeDefaults = {\n  minlen: 1,\n  weight: 1,\n  width: 0,\n  height: 0,\n  labeloffset: 10,\n  labelpos: 'r',\n};\nvar edgeAttrs = ['labelpos'];\n\n/*\n * Constructs a new graph from the input graph, which can be used for layout.\n * This process copies only whitelisted attributes from the input graph to the\n * layout graph. Thus this function serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction buildLayoutGraph(inputGraph) {\n  var g = new Graph({ multigraph: true, compound: true });\n  var graph = canonicalize(inputGraph.graph());\n\n  g.setGraph(\n    _.merge({}, graphDefaults, selectNumberAttrs(graph, graphNumAttrs), _.pick(graph, graphAttrs)),\n  );\n\n  _.forEach(inputGraph.nodes(), function (v) {\n    var node = canonicalize(inputGraph.node(v));\n    g.setNode(v, _.defaults(selectNumberAttrs(node, nodeNumAttrs), nodeDefaults));\n    g.setParent(v, inputGraph.parent(v));\n  });\n\n  _.forEach(inputGraph.edges(), function (e) {\n    var edge = canonicalize(inputGraph.edge(e));\n    g.setEdge(\n      e,\n      _.merge({}, edgeDefaults, selectNumberAttrs(edge, edgeNumAttrs), _.pick(edge, edgeAttrs)),\n    );\n  });\n\n  return g;\n}\n\n/*\n * This idea comes from the Gansner paper: to account for edge labels in our\n * layout we split each rank in half by doubling minlen and halving ranksep.\n * Then we can place labels at these mid-points between nodes.\n *\n * We also add some minimal padding to the width to push the label for the edge\n * away from the edge itself a bit.\n */\nfunction makeSpaceForEdgeLabels(g) {\n  var graph = g.graph();\n  graph.ranksep /= 2;\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    edge.minlen *= 2;\n    if (edge.labelpos.toLowerCase() !== 'c') {\n      if (graph.rankdir === 'TB' || graph.rankdir === 'BT') {\n        edge.width += edge.labeloffset;\n      } else {\n        edge.height += edge.labeloffset;\n      }\n    }\n  });\n}\n\n/*\n * Creates temporary dummy nodes that capture the rank in which each edge's\n * label is going to, if it has one of non-zero width and height. We do this\n * so that we can safely remove empty ranks while preserving balance for the\n * label's position.\n */\nfunction injectEdgeLabelProxies(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.width && edge.height) {\n      var v = g.node(e.v);\n      var w = g.node(e.w);\n      var label = { rank: (w.rank - v.rank) / 2 + v.rank, e: e };\n      util.addDummyNode(g, 'edge-proxy', label, '_ep');\n    }\n  });\n}\n\nfunction assignRankMinMax(g) {\n  var maxRank = 0;\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.borderTop) {\n      node.minRank = g.node(node.borderTop).rank;\n      node.maxRank = g.node(node.borderBottom).rank;\n      // @ts-expect-error\n      maxRank = _.max(maxRank, node.maxRank);\n    }\n  });\n  g.graph().maxRank = maxRank;\n}\n\nfunction removeEdgeLabelProxies(g) {\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.dummy === 'edge-proxy') {\n      g.edge(node.e).labelRank = node.rank;\n      g.removeNode(v);\n    }\n  });\n}\n\nfunction translateGraph(g) {\n  var minX = Number.POSITIVE_INFINITY;\n  var maxX = 0;\n  var minY = Number.POSITIVE_INFINITY;\n  var maxY = 0;\n  var graphLabel = g.graph();\n  var marginX = graphLabel.marginx || 0;\n  var marginY = graphLabel.marginy || 0;\n\n  function getExtremes(attrs) {\n    var x = attrs.x;\n    var y = attrs.y;\n    var w = attrs.width;\n    var h = attrs.height;\n    minX = Math.min(minX, x - w / 2);\n    maxX = Math.max(maxX, x + w / 2);\n    minY = Math.min(minY, y - h / 2);\n    maxY = Math.max(maxY, y + h / 2);\n  }\n\n  _.forEach(g.nodes(), function (v) {\n    getExtremes(g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      getExtremes(edge);\n    }\n  });\n\n  minX -= marginX;\n  minY -= marginY;\n\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    node.x -= minX;\n    node.y -= minY;\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, function (p) {\n      p.x -= minX;\n      p.y -= minY;\n    });\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      edge.x -= minX;\n    }\n    if (Object.prototype.hasOwnProperty.call(edge, 'y')) {\n      edge.y -= minY;\n    }\n  });\n\n  graphLabel.width = maxX - minX + marginX;\n  graphLabel.height = maxY - minY + marginY;\n}\n\nfunction assignNodeIntersects(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    var nodeV = g.node(e.v);\n    var nodeW = g.node(e.w);\n    var p1, p2;\n    if (!edge.points) {\n      edge.points = [];\n      p1 = nodeW;\n      p2 = nodeV;\n    } else {\n      p1 = edge.points[0];\n      p2 = edge.points[edge.points.length - 1];\n    }\n    edge.points.unshift(util.intersectRect(nodeV, p1));\n    edge.points.push(util.intersectRect(nodeW, p2));\n  });\n}\n\nfunction fixupEdgeLabelCoords(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      if (edge.labelpos === 'l' || edge.labelpos === 'r') {\n        edge.width -= edge.labeloffset;\n      }\n      switch (edge.labelpos) {\n        case 'l':\n          edge.x -= edge.width / 2 + edge.labeloffset;\n          break;\n        case 'r':\n          edge.x += edge.width / 2 + edge.labeloffset;\n          break;\n      }\n    }\n  });\n}\n\nfunction reversePointsForReversedEdges(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.reversed) {\n      edge.points.reverse();\n    }\n  });\n}\n\nfunction removeBorderNodes(g) {\n  _.forEach(g.nodes(), function (v) {\n    if (g.children(v).length) {\n      var node = g.node(v);\n      var t = g.node(node.borderTop);\n      var b = g.node(node.borderBottom);\n      var l = g.node(_.last(node.borderLeft));\n      var r = g.node(_.last(node.borderRight));\n\n      node.width = Math.abs(r.x - l.x);\n      node.height = Math.abs(b.y - t.y);\n      node.x = l.x + node.width / 2;\n      node.y = t.y + node.height / 2;\n    }\n  });\n\n  _.forEach(g.nodes(), function (v) {\n    if (g.node(v).dummy === 'border') {\n      g.removeNode(v);\n    }\n  });\n}\n\nfunction removeSelfEdges(g) {\n  _.forEach(g.edges(), function (e) {\n    if (e.v === e.w) {\n      var node = g.node(e.v);\n      if (!node.selfEdges) {\n        node.selfEdges = [];\n      }\n      node.selfEdges.push({ e: e, label: g.edge(e) });\n      g.removeEdge(e);\n    }\n  });\n}\n\nfunction insertSelfEdges(g) {\n  var layers = util.buildLayerMatrix(g);\n  _.forEach(layers, function (layer) {\n    var orderShift = 0;\n    _.forEach(layer, function (v, i) {\n      var node = g.node(v);\n      node.order = i + orderShift;\n      _.forEach(node.selfEdges, function (selfEdge) {\n        util.addDummyNode(\n          g,\n          'selfedge',\n          {\n            width: selfEdge.label.width,\n            height: selfEdge.label.height,\n            rank: node.rank,\n            order: i + ++orderShift,\n            e: selfEdge.e,\n            label: selfEdge.label,\n          },\n          '_se',\n        );\n      });\n      delete node.selfEdges;\n    });\n  });\n}\n\nfunction positionSelfEdges(g) {\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.dummy === 'selfedge') {\n      var selfNode = g.node(node.e.v);\n      var x = selfNode.x + selfNode.width / 2;\n      var y = selfNode.y;\n      var dx = node.x - x;\n      var dy = selfNode.height / 2;\n      g.setEdge(node.e, node.label);\n      g.removeNode(v);\n      node.label.points = [\n        { x: x + (2 * dx) / 3, y: y - dy },\n        { x: x + (5 * dx) / 6, y: y - dy },\n        { x: x + dx, y: y },\n        { x: x + (5 * dx) / 6, y: y + dy },\n        { x: x + (2 * dx) / 3, y: y + dy },\n      ];\n      node.label.x = node.x;\n      node.label.y = node.y;\n    }\n  });\n}\n\nfunction selectNumberAttrs(obj, attrs) {\n  return _.mapValues(_.pick(obj, attrs), Number);\n}\n\nfunction canonicalize(attrs) {\n  var newAttrs = {};\n  _.forEach(attrs, function (v, k) {\n    newAttrs[k.toLowerCase()] = v;\n  });\n  return newAttrs;\n}\n"], "names": ["flatRest", "func", "setToString", "overRest", "flatten", "CLONE_DEEP_FLAG", "CLONE_SYMBOLS_FLAG", "cloneDeep", "value", "baseClone", "forIn", "object", "iteratee", "baseFor", "castFunction", "keysIn", "forOwn", "baseForOwn", "baseGt", "other", "mapValues", "result", "baseIteratee", "key", "baseAssignValue", "max", "array", "baseExtremum", "identity", "minBy", "baseLt", "baseSortBy", "comparer", "length", "compareAscending", "valIsDefined", "valIsNull", "valIsReflexive", "valIsSymbol", "isSymbol", "othIsDefined", "othIsNull", "othIsReflexive", "othIsSymbol", "compareMultiple", "orders", "index", "objCriteria", "othCriteria", "ordersLength", "order", "baseOrderBy", "collection", "iteratees", "arrayMap", "isArray", "baseGet", "baseUnary", "baseMap", "criteria", "base<PERSON>ick", "paths", "basePickBy", "path", "hasIn", "pick", "nativeCeil", "nativeMax", "baseRange", "start", "end", "step", "fromRight", "createRange", "isIterateeCall", "toFinite", "range", "sortBy", "baseRest", "baseFlatten", "idCounter", "uniqueId", "prefix", "id", "toString", "baseZipObject", "props", "values", "assignFunc", "vals<PERSON><PERSON><PERSON>", "zipObject", "assignValue", "List", "sentinel", "entry", "unlink", "strs", "curr", "filterOutLinks", "k", "v", "DEFAULT_WEIGHT_FN", "_.constant", "greedyFAS", "g", "weightFn", "state", "buildState", "results", "doGreedyFAS", "_.flatten", "_.map", "e", "buckets", "zeroIdx", "sources", "sinks", "removeNode", "i", "collectPredecessors", "_.for<PERSON>ach", "edge", "weight", "uEntry", "assignBucket", "w", "wEntry", "fasGraph", "Graph", "maxIn", "maxOut", "prevWeight", "edgeWeight", "_.range", "run", "fas", "dfsFAS", "label", "_.uniqueId", "stack", "visited", "dfs", "undo", "<PERSON><PERSON><PERSON>", "addDummyNode", "type", "attrs", "name", "simplify", "simplified", "simpleLabel", "asNonCompoundGraph", "intersectRect", "rect", "point", "x", "y", "dx", "dy", "h", "sx", "sy", "buildLayerMatrix", "layering", "maxRank", "node", "rank", "_.isUndefined", "normalizeRanks", "min", "_.min", "_.has", "removeEmptyRanks", "offset", "layers", "delta", "nodeRankFactor", "vs", "addBorderNode", "_.max", "partition", "fn", "notime", "addBorderSegments", "children", "prop", "sg", "sgNode", "prev", "util.addDummyNode", "adjust", "rankDir", "swapWidthHeight", "reverseY", "swapXY", "swapWidthHeightOne", "reverseYOne", "swapXYOne", "normalizeEdge", "vRank", "wRank", "edgeLabel", "labelRank", "dummy", "origLabel", "longestPath", "slack", "feasibleTree", "t", "size", "tightTree", "find<PERSON>in<PERSON>lack<PERSON>dge", "shiftRanks", "edgeV", "_.minBy", "CycleException", "_.<PERSON><PERSON><PERSON><PERSON>", "navigation", "acc", "_.each", "doDfs", "postorder", "preorder", "networkSimplex", "initLowLimValues", "initCutValues", "calcCutValue", "leaveEdge", "enterEdge", "exchangeEdges", "f", "alg.postorder", "assignCutValue", "child", "childLab", "parent", "childIsTail", "graphEdge", "cutValue", "isOutEdge", "pointsToHead", "otherWeight", "isTreeEdge", "otherCutValue", "tree", "root", "dfsAssignLowLim", "<PERSON><PERSON><PERSON>", "low", "_.find", "vLabel", "w<PERSON><PERSON><PERSON>", "tailLabel", "flip", "candidates", "_.filter", "isDescendant", "updateRanks", "alg.preorder", "flipped", "u", "rootLabel", "networkSimplexRanker", "tightTreeRanker", "longestPathRanker", "depths", "treeDepths", "height", "_.values", "nodeSep", "sumWeights", "top", "util.addBorderNode", "bottom", "childNode", "childTop", "childBottom", "thisWeight", "minlen", "depth", "_.reduce", "cleanup", "graphLabel", "addSubgraphConstraints", "cg", "rootPrev", "prev<PERSON><PERSON><PERSON>", "buildLayerGraph", "relationship", "createRootNode", "crossCount", "cc", "twoLayerCrossCount", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "southPos", "_.zipObject", "southEntries", "_.sortBy", "firstIndex", "treeSize", "weightSum", "initOrder", "simpleNodes", "orderedVs", "barycenter", "movable", "inV", "nodeU", "resolveConflicts", "entries", "mappedEntries", "tmp", "entryV", "entryW", "sourceSet", "doResolveConflicts", "handleIn", "vEntry", "mergeEntries", "handleOut", "_.pick", "target", "source", "sum", "sort", "biasRight", "parts", "util.partition", "sortable", "unsortable", "vsIndex", "compareWithBias", "consumeUnsortable", "last", "_.last", "bias", "sortSubgraph", "bl", "br", "subgraphs", "barycenters", "subgraphResult", "mergeBarycenters", "expandSubgraphs", "blPred", "br<PERSON><PERSON>", "util.maxRank", "downLayerGraphs", "buildLayerGraphs", "upLayerGraphs", "assignOrder", "bestCC", "best", "lastBest", "sweepLayerGraphs", "util.buildLayerMatrix", "_.clone<PERSON><PERSON>", "ranks", "layerGraphs", "lg", "sorted", "layer", "parent<PERSON>ummy<PERSON><PERSON><PERSON>", "postorderNums", "edgeObj", "pathData", "<PERSON><PERSON><PERSON>", "lca", "pathIdx", "pathV", "ascending", "vPath", "wPath", "lim", "findType1Conflicts", "conflicts", "<PERSON><PERSON><PERSON><PERSON>", "prevLayer", "k0", "scanPos", "prevL<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastNode", "findOtherInnerSegmentNode", "k1", "scanNode", "uLabel", "uPos", "addConflict", "findType2Conflicts", "scan", "south", "southEnd", "prevNorthBorder", "nextNorthBorder", "uNode", "north", "prevNorthPos", "nextNorthPos", "southLookahead", "predecessors", "conflictsV", "hasConflict", "verticalAlignment", "neighborFn", "align", "pos", "prevIdx", "ws", "mp", "il", "horizontalCompaction", "reverseSep", "xs", "blockG", "buildBlockGraph", "borderType", "iterate", "setXsFunc", "nextNodesFunc", "elem", "pass1", "pass2", "blockGraph", "sepFn", "sep", "vRoot", "uRoot", "prevMax", "findSmallestWidthAlignment", "xss", "_.forIn", "halfWidth", "width", "alignCoordinates", "alignTo", "alignToVals", "alignToMin", "alignToMax", "vert", "horiz", "alignment", "xsVals", "_.map<PERSON><PERSON><PERSON>", "balance", "ignore", "positionX", "_.merge", "adjustedLayering", "inner", "smallestWidth", "edgeSep", "position", "util.asNonCompoundGraph", "positionY", "_.forOwn", "rankSep", "prevY", "maxHeight", "layout", "opts", "time", "util.notime", "layoutGraph", "buildLayoutGraph", "runLayout", "updateInputGraph", "makeSpaceForEdgeLabels", "removeSelfEdges", "acyclic.run", "nestingGraph.run", "injectEdgeLabelProxies", "util.removeEmptyRanks", "nestingGraph.cleanup", "util.normalizeRanks", "assignRankMinMax", "removeEdgeLabelProxies", "normalize.run", "insertSelf<PERSON>dges", "coordinateSystem.adjust", "position<PERSON><PERSON><PERSON><PERSON>", "removeBorderNodes", "normalize.undo", "fixupEdgeLabelCoords", "coordinateSystem.undo", "translateGraph", "assignNodeIntersects", "reversePointsForReversedEdges", "acyclic.undo", "inputGraph", "inputLabel", "layoutLabel", "graphNumAttrs", "graphDefaults", "graphAttrs", "nodeNumAttrs", "nodeDefaults", "edgeNumAttrs", "edgeDefaults", "edgeAttrs", "graph", "canonicalize", "selectNumberAttrs", "_.defaults", "minX", "maxX", "minY", "maxY", "marginX", "marginY", "getExtremes", "p", "nodeV", "nodeW", "p1", "p2", "util.intersectRect", "b", "l", "r", "orderShift", "selfEdge", "selfNode", "obj", "newAttrs"], "mappings": "0dAWA,SAASA,GAASC,EAAM,CACtB,OAAOC,GAAYC,GAASF,EAAM,OAAWG,CAAO,EAAGH,EAAO,EAAE,CAClE,CCVA,IAAII,GAAkB,EAClBC,GAAqB,EAoBzB,SAASC,GAAUC,EAAO,CACxB,OAAOC,GAAUD,EAAOH,GAAkBC,EAAkB,CAC9D,CCMA,SAASI,GAAMC,EAAQC,EAAU,CAC/B,OAAOD,GAAU,KACbA,EACAE,GAAQF,EAAQG,GAAaF,CAAQ,EAAGG,EAAM,CACpD,CCLA,SAASC,GAAOL,EAAQC,EAAU,CAChC,OAAOD,GAAUM,GAAWN,EAAQG,GAAaF,CAAQ,CAAC,CAC5D,CCxBA,SAASM,GAAOV,EAAOW,EAAO,CAC5B,OAAOX,EAAQW,CACjB,CCqBA,SAASC,EAAUT,EAAQC,EAAU,CACnC,IAAIS,EAAS,CAAA,EACb,OAAAT,EAAWU,EAAaV,CAAW,EAEnCK,GAAWN,EAAQ,SAASH,EAAOe,EAAKZ,EAAQ,CAC9Ca,GAAgBH,EAAQE,EAAKX,EAASJ,EAAOe,EAAKZ,CAAM,CAAC,CAC7D,CAAG,EACMU,CACT,CClBA,SAASI,EAAIC,EAAO,CAClB,OAAQA,GAASA,EAAM,OACnBC,GAAaD,EAAOE,GAAUV,EAAM,EACpC,MACN,CCCA,SAASW,EAAMH,EAAOd,EAAU,CAC9B,OAAQc,GAASA,EAAM,OACnBC,GAAaD,EAAOJ,EAAaV,CAAW,EAAGkB,EAAM,EACrD,MACN,CCrBA,SAASC,GAAWL,EAAOM,EAAU,CACnC,IAAIC,EAASP,EAAM,OAGnB,IADAA,EAAM,KAAKM,CAAQ,EACZC,KACLP,EAAMO,CAAM,EAAIP,EAAMO,CAAM,EAAE,MAEhC,OAAOP,CACT,CCRA,SAASQ,GAAiB1B,EAAOW,EAAO,CACtC,GAAIX,IAAUW,EAAO,CACnB,IAAIgB,EAAe3B,IAAU,OACzB4B,EAAY5B,IAAU,KACtB6B,EAAiB7B,IAAUA,EAC3B8B,EAAcC,GAAS/B,CAAK,EAE5BgC,EAAerB,IAAU,OACzBsB,EAAYtB,IAAU,KACtBuB,EAAiBvB,IAAUA,EAC3BwB,EAAcJ,GAASpB,CAAK,EAEhC,GAAK,CAACsB,GAAa,CAACE,GAAe,CAACL,GAAe9B,EAAQW,GACtDmB,GAAeE,GAAgBE,GAAkB,CAACD,GAAa,CAACE,GAChEP,GAAaI,GAAgBE,GAC7B,CAACP,GAAgBO,GAClB,CAACL,EACH,MAAO,GAET,GAAK,CAACD,GAAa,CAACE,GAAe,CAACK,GAAenC,EAAQW,GACtDwB,GAAeR,GAAgBE,GAAkB,CAACD,GAAa,CAACE,GAChEG,GAAaN,GAAgBE,GAC7B,CAACG,GAAgBH,GAClB,CAACK,EACH,MAAO,EAEV,CACD,MAAO,EACT,CCtBA,SAASE,GAAgBjC,EAAQQ,EAAO0B,EAAQ,CAO9C,QANIC,EAAQ,GACRC,EAAcpC,EAAO,SACrBqC,EAAc7B,EAAM,SACpBc,EAASc,EAAY,OACrBE,EAAeJ,EAAO,OAEnB,EAAEC,EAAQb,GAAQ,CACvB,IAAIZ,EAASa,GAAiBa,EAAYD,CAAK,EAAGE,EAAYF,CAAK,CAAC,EACpE,GAAIzB,EAAQ,CACV,GAAIyB,GAASG,EACX,OAAO5B,EAET,IAAI6B,EAAQL,EAAOC,CAAK,EACxB,OAAOzB,GAAU6B,GAAS,OAAS,GAAK,EACzC,CACF,CAQD,OAAOvC,EAAO,MAAQQ,EAAM,KAC9B,CCtBA,SAASgC,GAAYC,EAAYC,EAAWR,EAAQ,CAC9CQ,EAAU,OACZA,EAAYC,EAASD,EAAW,SAASzC,EAAU,CACjD,OAAI2C,GAAQ3C,CAAQ,EACX,SAASJ,EAAO,CACrB,OAAOgD,GAAQhD,EAAOI,EAAS,SAAW,EAAIA,EAAS,CAAC,EAAIA,CAAQ,CACrE,EAEIA,CACb,CAAK,EAEDyC,EAAY,CAACzB,EAAQ,EAGvB,IAAIkB,EAAQ,GACZO,EAAYC,EAASD,EAAWI,GAAUnC,CAAY,CAAC,EAEvD,IAAID,EAASqC,GAAQN,EAAY,SAAS5C,EAAOe,EAAK6B,EAAY,CAChE,IAAIO,EAAWL,EAASD,EAAW,SAASzC,EAAU,CACpD,OAAOA,EAASJ,CAAK,CAC3B,CAAK,EACD,MAAO,CAAE,SAAYmD,EAAU,MAAS,EAAEb,EAAO,MAAStC,EAC9D,CAAG,EAED,OAAOuB,GAAWV,EAAQ,SAASV,EAAQQ,EAAO,CAChD,OAAOyB,GAAgBjC,EAAQQ,EAAO0B,CAAM,CAChD,CAAG,CACH,CClCA,SAASe,GAASjD,EAAQkD,EAAO,CAC/B,OAAOC,GAAWnD,EAAQkD,EAAO,SAASrD,EAAOuD,EAAM,CACrD,OAAOC,GAAMrD,EAAQoD,CAAI,CAC7B,CAAG,CACH,CCIA,IAAIE,EAAOjE,GAAS,SAASW,EAAQkD,EAAO,CAC1C,OAAOlD,GAAU,KAAO,CAAA,EAAKiD,GAASjD,EAAQkD,CAAK,CACrD,CAAC,ECrBGK,GAAa,KAAK,KAClBC,GAAY,KAAK,IAarB,SAASC,GAAUC,EAAOC,EAAKC,EAAMC,EAAW,CAK9C,QAJI1B,EAAQ,GACRb,EAASkC,GAAUD,IAAYI,EAAMD,IAAUE,GAAQ,EAAE,EAAG,CAAC,EAC7DlD,EAAS,MAAMY,CAAM,EAElBA,KACLZ,EAA4B,EAAEyB,CAAK,EAAIuB,EACvCA,GAASE,EAEX,OAAOlD,CACT,CCdA,SAASoD,GAAYD,EAAW,CAC9B,OAAO,SAASH,EAAOC,EAAKC,EAAM,CAChC,OAAIA,GAAQ,OAAOA,GAAQ,UAAYG,EAAeL,EAAOC,EAAKC,CAAI,IACpED,EAAMC,EAAO,QAGfF,EAAQM,EAASN,CAAK,EAClBC,IAAQ,QACVA,EAAMD,EACNA,EAAQ,GAERC,EAAMK,EAASL,CAAG,EAEpBC,EAAOA,IAAS,OAAaF,EAAQC,EAAM,EAAI,GAAMK,EAASJ,CAAI,EAC3DH,GAAUC,EAAOC,EAAKC,CAAe,CAChD,CACA,CCgBA,IAAIK,EAAQH,GAAa,ECTrBI,EAASC,GAAS,SAAS1B,EAAYC,EAAW,CACpD,GAAID,GAAc,KAChB,MAAO,GAET,IAAInB,EAASoB,EAAU,OACvB,OAAIpB,EAAS,GAAKyC,EAAetB,EAAYC,EAAU,CAAC,EAAGA,EAAU,CAAC,CAAC,EACrEA,EAAY,CAAA,EACHpB,EAAS,GAAKyC,EAAerB,EAAU,CAAC,EAAGA,EAAU,CAAC,EAAGA,EAAU,CAAC,CAAC,IAC9EA,EAAY,CAACA,EAAU,CAAC,CAAC,GAEpBF,GAAYC,EAAY2B,GAAY1B,CAAY,EAAG,CAAE,CAAA,CAC9D,CAAC,EC1CG2B,GAAY,EAmBhB,SAASC,EAASC,EAAQ,CACxB,IAAIC,EAAK,EAAEH,GACX,OAAOI,GAASF,CAAM,EAAIC,CAC5B,CChBA,SAASE,GAAcC,EAAOC,EAAQC,EAAY,CAMhD,QALI1C,EAAQ,GACRb,EAASqD,EAAM,OACfG,EAAaF,EAAO,OACpBlE,EAAS,CAAA,EAEN,EAAEyB,EAAQb,GAAQ,CACvB,IAAIzB,EAAQsC,EAAQ2C,EAAaF,EAAOzC,CAAK,EAAI,OACjD0C,EAAWnE,EAAQiE,EAAMxC,CAAK,EAAGtC,CAAK,CACvC,CACD,OAAOa,CACT,CCDA,SAASqE,GAAUJ,EAAOC,EAAQ,CAChC,OAAOF,GAAcC,GAAS,CAAA,EAAIC,GAAU,CAAA,EAAII,EAAW,CAC7D,CCdA,MAAMC,EAAK,CACT,aAAc,CACZ,IAAIC,EAAW,CAAA,EACfA,EAAS,MAAQA,EAAS,MAAQA,EAClC,KAAK,UAAYA,CAClB,CACD,SAAU,CACR,IAAIA,EAAW,KAAK,UAChBC,EAAQD,EAAS,MACrB,GAAIC,IAAUD,EACZ,OAAAE,GAAOD,CAAK,EACLA,CAEV,CACD,QAAQA,EAAO,CACb,IAAID,EAAW,KAAK,UAChBC,EAAM,OAASA,EAAM,OACvBC,GAAOD,CAAK,EAEdA,EAAM,MAAQD,EAAS,MACvBA,EAAS,MAAM,MAAQC,EACvBD,EAAS,MAAQC,EACjBA,EAAM,MAAQD,CACf,CACD,UAAW,CAIT,QAHIG,EAAO,CAAA,EACPH,EAAW,KAAK,UAChBI,EAAOJ,EAAS,MACbI,IAASJ,GACdG,EAAK,KAAK,KAAK,UAAUC,EAAMC,EAAc,CAAC,EAC9CD,EAAOA,EAAK,MAEd,MAAO,IAAMD,EAAK,KAAK,IAAI,EAAI,GAChC,CACH,CAEA,SAASD,GAAOD,EAAO,CACrBA,EAAM,MAAM,MAAQA,EAAM,MAC1BA,EAAM,MAAM,MAAQA,EAAM,MAC1B,OAAOA,EAAM,MACb,OAAOA,EAAM,KACf,CAEA,SAASI,GAAeC,EAAGC,EAAG,CAC5B,GAAID,IAAM,SAAWA,IAAM,QACzB,OAAOC,CAEX,CCzCA,IAAIC,GAAoBC,GAAW,CAAC,EAEpC,SAASC,GAAUC,EAAGC,EAAU,CAC9B,GAAID,EAAE,UAAW,GAAI,EACnB,MAAO,GAET,IAAIE,EAAQC,GAAWH,EAAGC,GAAYJ,EAAiB,EACnDO,EAAUC,GAAYH,EAAM,MAAOA,EAAM,QAASA,EAAM,OAAO,EAGnE,OAAOI,EACLC,EAAMH,EAAS,SAAUI,EAAG,CAC1B,OAAOR,EAAE,SAASQ,EAAE,EAAGA,EAAE,CAAC,CAChC,CAAK,CACL,CACA,CAEA,SAASH,GAAYL,EAAGS,EAASC,EAAS,CAMxC,QALIN,EAAU,CAAA,EACVO,EAAUF,EAAQA,EAAQ,OAAS,CAAC,EACpCG,EAAQH,EAAQ,CAAC,EAEjBnB,EACGU,EAAE,aAAa,CACpB,KAAQV,EAAQsB,EAAM,WACpBC,EAAWb,EAAGS,EAASC,EAASpB,CAAK,EAEvC,KAAQA,EAAQqB,EAAQ,WACtBE,EAAWb,EAAGS,EAASC,EAASpB,CAAK,EAEvC,GAAIU,EAAE,aACJ,QAASc,EAAIL,EAAQ,OAAS,EAAGK,EAAI,EAAG,EAAEA,EAExC,GADAxB,EAAQmB,EAAQK,CAAC,EAAE,QAAO,EACtBxB,EAAO,CACTc,EAAUA,EAAQ,OAAOS,EAAWb,EAAGS,EAASC,EAASpB,EAAO,EAAI,CAAC,EACrE,KACD,EAGN,CAED,OAAOc,CACT,CAEA,SAASS,EAAWb,EAAGS,EAASC,EAASpB,EAAOyB,EAAqB,CACnE,IAAIX,EAAUW,EAAsB,CAAE,EAAG,OAEzCC,OAAAA,EAAUhB,EAAE,QAAQV,EAAM,CAAC,EAAG,SAAU2B,EAAM,CAC5C,IAAIC,EAASlB,EAAE,KAAKiB,CAAI,EACpBE,EAASnB,EAAE,KAAKiB,EAAK,CAAC,EAEtBF,GACFX,EAAQ,KAAK,CAAE,EAAGa,EAAK,EAAG,EAAGA,EAAK,CAAC,CAAE,EAGvCE,EAAO,KAAOD,EACdE,EAAaX,EAASC,EAASS,CAAM,CACzC,CAAG,EAEDH,EAAUhB,EAAE,SAASV,EAAM,CAAC,EAAG,SAAU2B,EAAM,CAC7C,IAAIC,EAASlB,EAAE,KAAKiB,CAAI,EACpBI,EAAIJ,EAAK,EACTK,EAAStB,EAAE,KAAKqB,CAAC,EACrBC,EAAO,IAASJ,EAChBE,EAAaX,EAASC,EAASY,CAAM,CACzC,CAAG,EAEDtB,EAAE,WAAWV,EAAM,CAAC,EAEbc,CACT,CAEA,SAASD,GAAWH,EAAGC,EAAU,CAC/B,IAAIsB,EAAW,IAAIC,EACfC,EAAQ,EACRC,EAAS,EAEbV,EAAUhB,EAAE,MAAO,EAAE,SAAUJ,EAAG,CAChC2B,EAAS,QAAQ3B,EAAG,CAAE,EAAGA,EAAG,GAAI,EAAG,IAAK,CAAC,CAAE,CAC/C,CAAG,EAIDoB,EAAUhB,EAAE,MAAO,EAAE,SAAUQ,EAAG,CAChC,IAAImB,EAAaJ,EAAS,KAAKf,EAAE,EAAGA,EAAE,CAAC,GAAK,EACxCU,EAASjB,EAASO,CAAC,EACnBoB,EAAaD,EAAaT,EAC9BK,EAAS,QAAQf,EAAE,EAAGA,EAAE,EAAGoB,CAAU,EACrCF,EAAS,KAAK,IAAIA,EAASH,EAAS,KAAKf,EAAE,CAAC,EAAE,KAAOU,CAAM,EAC3DO,EAAQ,KAAK,IAAIA,EAAQF,EAAS,KAAKf,EAAE,CAAC,EAAE,IAASU,CAAM,CAC/D,CAAG,EAED,IAAIT,EAAUoB,EAAQH,EAASD,EAAQ,CAAC,EAAE,IAAI,UAAY,CACxD,OAAO,IAAIrC,EACf,CAAG,EACGsB,EAAUe,EAAQ,EAEtBT,OAAAA,EAAUO,EAAS,MAAO,EAAE,SAAU3B,EAAG,CACvCwB,EAAaX,EAASC,EAASa,EAAS,KAAK3B,CAAC,CAAC,CACnD,CAAG,EAEM,CAAE,MAAO2B,EAAU,QAASd,EAAS,QAASC,EACvD,CAEA,SAASU,EAAaX,EAASC,EAASpB,EAAO,CACxCA,EAAM,IAECA,EAAM,GAGhBmB,EAAQnB,EAAM,IAAMA,EAAM,GAAQoB,CAAO,EAAE,QAAQpB,CAAK,EAFxDmB,EAAQA,EAAQ,OAAS,CAAC,EAAE,QAAQnB,CAAK,EAFzCmB,EAAQ,CAAC,EAAE,QAAQnB,CAAK,CAM5B,CCxHA,SAASwC,GAAI9B,EAAG,CACd,IAAI+B,EAAM/B,EAAE,MAAK,EAAG,YAAc,SAAWD,GAAUC,EAAGC,EAASD,CAAC,CAAC,EAAIgC,GAAOhC,CAAC,EACjFgB,EAAUe,EAAK,SAAUvB,EAAG,CAC1B,IAAIyB,EAAQjC,EAAE,KAAKQ,CAAC,EACpBR,EAAE,WAAWQ,CAAC,EACdyB,EAAM,YAAczB,EAAE,KACtByB,EAAM,SAAW,GACjBjC,EAAE,QAAQQ,EAAE,EAAGA,EAAE,EAAGyB,EAAOC,EAAW,KAAK,CAAC,CAChD,CAAG,EAED,SAASjC,EAASD,EAAG,CACnB,OAAO,SAAUQ,EAAG,CAClB,OAAOR,EAAE,KAAKQ,CAAC,EAAE,MACvB,CACG,CACH,CAEA,SAASwB,GAAOhC,EAAG,CACjB,IAAI+B,EAAM,CAAA,EACNI,EAAQ,CAAA,EACRC,EAAU,CAAA,EAEd,SAASC,EAAIzC,EAAG,CACV,OAAO,UAAU,eAAe,KAAKwC,EAASxC,CAAC,IAGnDwC,EAAQxC,CAAC,EAAI,GACbuC,EAAMvC,CAAC,EAAI,GACXoB,EAAUhB,EAAE,SAASJ,CAAC,EAAG,SAAUY,EAAG,CAChC,OAAO,UAAU,eAAe,KAAK2B,EAAO3B,EAAE,CAAC,EACjDuB,EAAI,KAAKvB,CAAC,EAEV6B,EAAI7B,EAAE,CAAC,CAEf,CAAK,EACD,OAAO2B,EAAMvC,CAAC,EACf,CAEDoB,OAAAA,EAAUhB,EAAE,MAAO,EAAEqC,CAAG,EACjBN,CACT,CAEA,SAASO,GAAKtC,EAAG,CACfgB,EAAUhB,EAAE,MAAO,EAAE,SAAUQ,EAAG,CAChC,IAAIyB,EAAQjC,EAAE,KAAKQ,CAAC,EACpB,GAAIyB,EAAM,SAAU,CAClBjC,EAAE,WAAWQ,CAAC,EAEd,IAAI+B,EAAcN,EAAM,YACxB,OAAOA,EAAM,SACb,OAAOA,EAAM,YACbjC,EAAE,QAAQQ,EAAE,EAAGA,EAAE,EAAGyB,EAAOM,CAAW,CACvC,CACL,CAAG,CACH,CCpCA,SAASC,EAAaxC,EAAGyC,EAAMC,EAAOC,EAAM,CAC1C,IAAI/C,EACJ,GACEA,EAAIsC,EAAWS,CAAI,QACZ3C,EAAE,QAAQJ,CAAC,GAEpB,OAAA8C,EAAM,MAAQD,EACdzC,EAAE,QAAQJ,EAAG8C,CAAK,EACX9C,CACT,CAMA,SAASgD,GAAS5C,EAAG,CACnB,IAAI6C,EAAa,IAAIrB,EAAO,EAAC,SAASxB,EAAE,MAAK,CAAE,EAC/CgB,OAAAA,EAAUhB,EAAE,MAAO,EAAE,SAAUJ,EAAG,CAChCiD,EAAW,QAAQjD,EAAGI,EAAE,KAAKJ,CAAC,CAAC,CACnC,CAAG,EACDoB,EAAUhB,EAAE,MAAO,EAAE,SAAUQ,EAAG,CAChC,IAAIsC,EAAcD,EAAW,KAAKrC,EAAE,EAAGA,EAAE,CAAC,GAAK,CAAE,OAAQ,EAAG,OAAQ,CAAC,EACjEyB,EAAQjC,EAAE,KAAKQ,CAAC,EACpBqC,EAAW,QAAQrC,EAAE,EAAGA,EAAE,EAAG,CAC3B,OAAQsC,EAAY,OAASb,EAAM,OACnC,OAAQ,KAAK,IAAIa,EAAY,OAAQb,EAAM,MAAM,CACvD,CAAK,CACL,CAAG,EACMY,CACT,CAEA,SAASE,GAAmB/C,EAAG,CAC7B,IAAI6C,EAAa,IAAIrB,EAAM,CAAE,WAAYxB,EAAE,aAAc,CAAA,CAAE,EAAE,SAASA,EAAE,MAAO,CAAA,EAC/EgB,OAAAA,EAAUhB,EAAE,MAAO,EAAE,SAAUJ,EAAG,CAC3BI,EAAE,SAASJ,CAAC,EAAE,QACjBiD,EAAW,QAAQjD,EAAGI,EAAE,KAAKJ,CAAC,CAAC,CAErC,CAAG,EACDoB,EAAUhB,EAAE,MAAO,EAAE,SAAUQ,EAAG,CAChCqC,EAAW,QAAQrC,EAAGR,EAAE,KAAKQ,CAAC,CAAC,CACnC,CAAG,EACMqC,CACT,CA4BA,SAASG,GAAcC,EAAMC,EAAO,CAClC,IAAIC,EAAIF,EAAK,EACTG,EAAIH,EAAK,EAITI,EAAKH,EAAM,EAAIC,EACfG,EAAKJ,EAAM,EAAIE,EACf/B,EAAI4B,EAAK,MAAQ,EACjBM,EAAIN,EAAK,OAAS,EAEtB,GAAI,CAACI,GAAM,CAACC,EACV,MAAM,IAAI,MAAM,2DAA2D,EAG7E,IAAIE,EAAIC,EACR,OAAI,KAAK,IAAIH,CAAE,EAAIjC,EAAI,KAAK,IAAIgC,CAAE,EAAIE,GAEhCD,EAAK,IACPC,EAAI,CAACA,GAEPC,EAAMD,EAAIF,EAAMC,EAChBG,EAAKF,IAGDF,EAAK,IACPhC,EAAI,CAACA,GAEPmC,EAAKnC,EACLoC,EAAMpC,EAAIiC,EAAMD,GAGX,CAAE,EAAGF,EAAIK,EAAI,EAAGJ,EAAIK,EAC7B,CAMA,SAASC,EAAiB1D,EAAG,CAC3B,IAAI2D,EAAWpD,EAAMsB,EAAQ+B,GAAQ5D,CAAC,EAAI,CAAC,EAAG,UAAY,CACxD,MAAO,EACX,CAAG,EACDgB,OAAAA,EAAUhB,EAAE,MAAO,EAAE,SAAUJ,EAAG,CAChC,IAAIiE,EAAO7D,EAAE,KAAKJ,CAAC,EACfkE,EAAOD,EAAK,KACXE,EAAcD,CAAI,IACrBH,EAASG,CAAI,EAAED,EAAK,KAAK,EAAIjE,EAEnC,CAAG,EACM+D,CACT,CAMA,SAASK,GAAehE,EAAG,CACzB,IAAIiE,EAAMC,EACR3D,EAAMP,EAAE,MAAO,EAAE,SAAUJ,EAAG,CAC5B,OAAOI,EAAE,KAAKJ,CAAC,EAAE,IACvB,CAAK,CACL,EACEoB,EAAUhB,EAAE,MAAO,EAAE,SAAUJ,EAAG,CAChC,IAAIiE,EAAO7D,EAAE,KAAKJ,CAAC,EACfuE,GAAMN,EAAM,MAAM,IACpBA,EAAK,MAAQI,EAEnB,CAAG,CACH,CAEA,SAASG,GAAiBpE,EAAG,CAE3B,IAAIqE,EAASH,EACX3D,EAAMP,EAAE,MAAO,EAAE,SAAUJ,EAAG,CAC5B,OAAOI,EAAE,KAAKJ,CAAC,EAAE,IACvB,CAAK,CACL,EAEM0E,EAAS,CAAA,EACbtD,EAAUhB,EAAE,MAAO,EAAE,SAAUJ,EAAG,CAChC,IAAIkE,EAAO9D,EAAE,KAAKJ,CAAC,EAAE,KAAOyE,EACvBC,EAAOR,CAAI,IACdQ,EAAOR,CAAI,EAAI,IAEjBQ,EAAOR,CAAI,EAAE,KAAKlE,CAAC,CACvB,CAAG,EAED,IAAI2E,EAAQ,EACRC,EAAiBxE,EAAE,MAAK,EAAG,eAC/BgB,EAAUsD,EAAQ,SAAUG,EAAI3D,EAAG,CAC7BiD,EAAcU,CAAE,GAAK3D,EAAI0D,IAAmB,EAC9C,EAAED,EACOA,GACTvD,EAAUyD,EAAI,SAAU7E,EAAG,CACzBI,EAAE,KAAKJ,CAAC,EAAE,MAAQ2E,CAC1B,CAAO,CAEP,CAAG,CACH,CAEA,SAASG,GAAc1E,EAAGtB,EAAQoF,EAAMpH,EAAO,CAC7C,IAAImH,EAAO,CACT,MAAO,EACP,OAAQ,CACZ,EACE,OAAI,UAAU,QAAU,IACtBA,EAAK,KAAOC,EACZD,EAAK,MAAQnH,GAER8F,EAAaxC,EAAG,SAAU6D,EAAMnF,CAAM,CAC/C,CAEA,SAASkF,GAAQ5D,EAAG,CAClB,OAAO2E,EACLpE,EAAMP,EAAE,MAAO,EAAE,SAAUJ,EAAG,CAC5B,IAAIkE,EAAO9D,EAAE,KAAKJ,CAAC,EAAE,KACrB,GAAI,CAACmE,EAAcD,CAAI,EACrB,OAAOA,CAEf,CAAK,CACL,CACA,CAOA,SAASc,GAAUhI,EAAYiI,EAAI,CACjC,IAAIhK,EAAS,CAAE,IAAK,CAAE,EAAE,IAAK,CAAE,CAAA,EAC/BmG,OAAAA,EAAUpE,EAAY,SAAU5C,EAAO,CACjC6K,EAAG7K,CAAK,EACVa,EAAO,IAAI,KAAKb,CAAK,EAErBa,EAAO,IAAI,KAAKb,CAAK,CAE3B,CAAG,EACMa,CACT,CAeA,SAASiK,GAAOnC,EAAMkC,EAAI,CACxB,OAAOA,EAAE,CACX,CCpPA,SAASE,GAAkB/E,EAAG,CAC5B,SAASqC,EAAIzC,EAAG,CACd,IAAIoF,EAAWhF,EAAE,SAASJ,CAAC,EACvBiE,EAAO7D,EAAE,KAAKJ,CAAC,EAKnB,GAJIoF,EAAS,QACXhE,EAAUgE,EAAU3C,CAAG,EAGrB,OAAO,UAAU,eAAe,KAAKwB,EAAM,SAAS,EAAG,CACzDA,EAAK,WAAa,GAClBA,EAAK,YAAc,GACnB,QAASC,EAAOD,EAAK,QAASD,EAAUC,EAAK,QAAU,EAAGC,EAAOF,EAAS,EAAEE,EAC1EY,GAAc1E,EAAG,aAAc,MAAOJ,EAAGiE,EAAMC,CAAI,EACnDY,GAAc1E,EAAG,cAAe,MAAOJ,EAAGiE,EAAMC,CAAI,CAEvD,CACF,CAED9C,EAAUhB,EAAE,SAAU,EAAEqC,CAAG,CAC7B,CAEA,SAASqC,GAAc1E,EAAGiF,EAAMvG,EAAQwG,EAAIC,EAAQrB,EAAM,CACxD,IAAI7B,EAAQ,CAAE,MAAO,EAAG,OAAQ,EAAG,KAAM6B,EAAM,WAAYmB,GACvDG,EAAOD,EAAOF,CAAI,EAAEnB,EAAO,CAAC,EAC5BrE,EAAO4F,EAAkBrF,EAAG,SAAUiC,EAAOvD,CAAM,EACvDyG,EAAOF,CAAI,EAAEnB,CAAI,EAAIrE,EACrBO,EAAE,UAAUP,EAAMyF,CAAE,EAChBE,GACFpF,EAAE,QAAQoF,EAAM3F,EAAM,CAAE,OAAQ,CAAC,CAAE,CAEvC,CC/BA,SAAS6F,GAAOtF,EAAG,CACjB,IAAIuF,EAAUvF,EAAE,MAAO,EAAC,QAAQ,YAAW,GACvCuF,IAAY,MAAQA,IAAY,OAClCC,GAAgBxF,CAAC,CAErB,CAEA,SAASsC,GAAKtC,EAAG,CACf,IAAIuF,EAAUvF,EAAE,MAAO,EAAC,QAAQ,YAAW,GACvCuF,IAAY,MAAQA,IAAY,OAClCE,GAASzF,CAAC,GAGRuF,IAAY,MAAQA,IAAY,QAClCG,GAAO1F,CAAC,EACRwF,GAAgBxF,CAAC,EAErB,CAEA,SAASwF,GAAgBxF,EAAG,CAC1BgB,EAAUhB,EAAE,MAAO,EAAE,SAAUJ,EAAG,CAChC+F,GAAmB3F,EAAE,KAAKJ,CAAC,CAAC,CAChC,CAAG,EACDoB,EAAUhB,EAAE,MAAO,EAAE,SAAUQ,EAAG,CAChCmF,GAAmB3F,EAAE,KAAKQ,CAAC,CAAC,CAChC,CAAG,CACH,CAEA,SAASmF,GAAmBjD,EAAO,CACjC,IAAIrB,EAAIqB,EAAM,MACdA,EAAM,MAAQA,EAAM,OACpBA,EAAM,OAASrB,CACjB,CAEA,SAASoE,GAASzF,EAAG,CACnBgB,EAAUhB,EAAE,MAAO,EAAE,SAAUJ,EAAG,CAChCgG,EAAY5F,EAAE,KAAKJ,CAAC,CAAC,CACzB,CAAG,EAEDoB,EAAUhB,EAAE,MAAO,EAAE,SAAUQ,EAAG,CAChC,IAAIS,EAAOjB,EAAE,KAAKQ,CAAC,EACnBQ,EAAUC,EAAK,OAAQ2E,CAAW,EAC9B,OAAO,UAAU,eAAe,KAAK3E,EAAM,GAAG,GAChD2E,EAAY3E,CAAI,CAEtB,CAAG,CACH,CAEA,SAAS2E,EAAYlD,EAAO,CAC1BA,EAAM,EAAI,CAACA,EAAM,CACnB,CAEA,SAASgD,GAAO1F,EAAG,CACjBgB,EAAUhB,EAAE,MAAO,EAAE,SAAUJ,EAAG,CAChCiG,EAAU7F,EAAE,KAAKJ,CAAC,CAAC,CACvB,CAAG,EAEDoB,EAAUhB,EAAE,MAAO,EAAE,SAAUQ,EAAG,CAChC,IAAIS,EAAOjB,EAAE,KAAKQ,CAAC,EACnBQ,EAAUC,EAAK,OAAQ4E,CAAS,EAC5B,OAAO,UAAU,eAAe,KAAK5E,EAAM,GAAG,GAChD4E,EAAU5E,CAAI,CAEpB,CAAG,CACH,CAEA,SAAS4E,EAAUnD,EAAO,CACxB,IAAIS,EAAIT,EAAM,EACdA,EAAM,EAAIA,EAAM,EAChBA,EAAM,EAAIS,CACZ,CChDA,SAASrB,GAAI9B,EAAG,CACdA,EAAE,MAAK,EAAG,YAAc,GACxBgB,EAAUhB,EAAE,MAAO,EAAE,SAAUiB,EAAM,CACnC6E,GAAc9F,EAAGiB,CAAI,CACzB,CAAG,CACH,CAKA,SAAS6E,GAAc9F,EAAGQ,EAAG,CAC3B,IAAIZ,EAAIY,EAAE,EACNuF,EAAQ/F,EAAE,KAAKJ,CAAC,EAAE,KAClByB,EAAIb,EAAE,EACNwF,EAAQhG,EAAE,KAAKqB,CAAC,EAAE,KAClBsB,EAAOnC,EAAE,KACTyF,EAAYjG,EAAE,KAAKQ,CAAC,EACpB0F,EAAYD,EAAU,UAE1B,GAAID,IAAUD,EAAQ,EAEtB,CAAA/F,EAAE,WAAWQ,CAAC,EAcd,IAAIkC,EAAQ,OACRyD,EAAOrF,EACX,IAAKA,EAAI,EAAG,EAAEiF,EAAOA,EAAQC,EAAO,EAAElF,EAAG,EAAEiF,EACzCE,EAAU,OAAS,GACnBvD,EAAQ,CACN,MAAO,EACP,OAAQ,EACR,UAAWuD,EACX,QAASzF,EACT,KAAMuF,CACZ,EACII,EAAQd,EAAkBrF,EAAG,OAAQ0C,EAAO,IAAI,EAC5CqD,IAAUG,IACZxD,EAAM,MAAQuD,EAAU,MACxBvD,EAAM,OAASuD,EAAU,OACzBvD,EAAM,MAAQ,aACdA,EAAM,SAAWuD,EAAU,UAE7BjG,EAAE,QAAQJ,EAAGuG,EAAO,CAAE,OAAQF,EAAU,QAAUtD,CAAI,EAClD7B,IAAM,GACRd,EAAE,MAAO,EAAC,YAAY,KAAKmG,CAAK,EAElCvG,EAAIuG,EAGNnG,EAAE,QAAQJ,EAAGyB,EAAG,CAAE,OAAQ4E,EAAU,QAAUtD,CAAI,EACpD,CAEA,SAASL,GAAKtC,EAAG,CACfgB,EAAUhB,EAAE,MAAO,EAAC,YAAa,SAAUJ,EAAG,CAC5C,IAAIiE,EAAO7D,EAAE,KAAKJ,CAAC,EACfwG,EAAYvC,EAAK,UACjBxC,EAEJ,IADArB,EAAE,QAAQ6D,EAAK,QAASuC,CAAS,EAC1BvC,EAAK,OACVxC,EAAIrB,EAAE,WAAWJ,CAAC,EAAE,CAAC,EACrBI,EAAE,WAAWJ,CAAC,EACdwG,EAAU,OAAO,KAAK,CAAE,EAAGvC,EAAK,EAAG,EAAGA,EAAK,CAAC,CAAE,EAC1CA,EAAK,QAAU,eACjBuC,EAAU,EAAIvC,EAAK,EACnBuC,EAAU,EAAIvC,EAAK,EACnBuC,EAAU,MAAQvC,EAAK,MACvBuC,EAAU,OAASvC,EAAK,QAE1BjE,EAAIyB,EACJwC,EAAO7D,EAAE,KAAKJ,CAAC,CAErB,CAAG,CACH,CCpFA,SAASyG,EAAYrG,EAAG,CACtB,IAAIoC,EAAU,CAAA,EAEd,SAASC,EAAIzC,EAAG,CACd,IAAIqC,EAAQjC,EAAE,KAAKJ,CAAC,EACpB,GAAI,OAAO,UAAU,eAAe,KAAKwC,EAASxC,CAAC,EACjD,OAAOqC,EAAM,KAEfG,EAAQxC,CAAC,EAAI,GAEb,IAAIkE,EAAOI,EACT3D,EAAMP,EAAE,SAASJ,CAAC,EAAG,SAAUY,EAAG,CAChC,OAAO6B,EAAI7B,EAAE,CAAC,EAAIR,EAAE,KAAKQ,CAAC,EAAE,MACpC,CAAO,CACP,EAEI,OACEsD,IAAS,OAAO,mBAChBA,IAAS,QACTA,IAAS,QAGTA,EAAO,GAGD7B,EAAM,KAAO6B,CACtB,CAED9C,EAAUhB,EAAE,QAAS,EAAEqC,CAAG,CAC5B,CAMA,SAASiE,EAAMtG,EAAGQ,EAAG,CACnB,OAAOR,EAAE,KAAKQ,EAAE,CAAC,EAAE,KAAOR,EAAE,KAAKQ,EAAE,CAAC,EAAE,KAAOR,EAAE,KAAKQ,CAAC,EAAE,MACzD,CC/BA,SAAS+F,GAAavG,EAAG,CACvB,IAAIwG,EAAI,IAAIhF,EAAM,CAAE,SAAU,EAAO,CAAA,EAGjC3D,EAAQmC,EAAE,MAAO,EAAC,CAAC,EACnByG,EAAOzG,EAAE,YACbwG,EAAE,QAAQ3I,EAAO,CAAA,CAAE,EAGnB,QADIoD,EAAMsD,EACHmC,GAAUF,EAAGxG,CAAC,EAAIyG,GACvBxF,EAAO0F,GAAiBH,EAAGxG,CAAC,EAC5BuE,EAAQiC,EAAE,QAAQvF,EAAK,CAAC,EAAIqF,EAAMtG,EAAGiB,CAAI,EAAI,CAACqF,EAAMtG,EAAGiB,CAAI,EAC3D2F,GAAWJ,EAAGxG,EAAGuE,CAAK,EAGxB,OAAOiC,CACT,CAMA,SAASE,GAAUF,EAAGxG,EAAG,CACvB,SAASqC,EAAIzC,EAAG,CACdoB,EAAUhB,EAAE,UAAUJ,CAAC,EAAG,SAAUY,EAAG,CACrC,IAAIqG,EAAQrG,EAAE,EACZa,EAAIzB,IAAMiH,EAAQrG,EAAE,EAAIqG,EACtB,CAACL,EAAE,QAAQnF,CAAC,GAAK,CAACiF,EAAMtG,EAAGQ,CAAC,IAC9BgG,EAAE,QAAQnF,EAAG,CAAA,CAAE,EACfmF,EAAE,QAAQ5G,EAAGyB,EAAG,CAAE,CAAA,EAClBgB,EAAIhB,CAAC,EAEb,CAAK,CACF,CAEDL,OAAAA,EAAUwF,EAAE,MAAO,EAAEnE,CAAG,EACjBmE,EAAE,WACX,CAMA,SAASG,GAAiBH,EAAGxG,EAAG,CAC9B,OAAO8G,EAAQ9G,EAAE,MAAO,EAAE,SAAUQ,EAAG,CACrC,GAAIgG,EAAE,QAAQhG,EAAE,CAAC,IAAMgG,EAAE,QAAQhG,EAAE,CAAC,EAClC,OAAO8F,EAAMtG,EAAGQ,CAAC,CAEvB,CAAG,CACH,CAEA,SAASoG,GAAWJ,EAAGxG,EAAGuE,EAAO,CAC/BvD,EAAUwF,EAAE,MAAO,EAAE,SAAU5G,EAAG,CAChCI,EAAE,KAAKJ,CAAC,EAAE,MAAQ2E,CACtB,CAAG,CACH,CCpDA,SAASwC,IAAiB,CAAE,CAC5BA,GAAe,UAAY,IAAI,MCvB/B,SAAS1E,GAAIrC,EAAGyE,EAAI/H,EAAO,CACpBsK,GAAUvC,CAAE,IACfA,EAAK,CAACA,CAAE,GAGV,IAAIwC,GAAcjH,EAAE,aAAeA,EAAE,WAAaA,EAAE,WAAW,KAAKA,CAAC,EAEjEkH,EAAM,CAAA,EACN9E,EAAU,CAAA,EACd+E,OAAAA,EAAO1C,EAAI,SAAU7E,EAAG,CACtB,GAAI,CAACI,EAAE,QAAQJ,CAAC,EACd,MAAM,IAAI,MAAM,6BAA+BA,CAAC,EAGlDwH,GAAMpH,EAAGJ,EAAGlD,IAAU,OAAQ0F,EAAS6E,EAAYC,CAAG,CAC1D,CAAG,EACMA,CACT,CAEA,SAASE,GAAMpH,EAAGJ,EAAGyH,EAAWjF,EAAS6E,EAAYC,EAAK,CACnD,OAAO,UAAU,eAAe,KAAK9E,EAASxC,CAAC,IAClDwC,EAAQxC,CAAC,EAAI,GAERyH,GACHH,EAAI,KAAKtH,CAAC,EAEZuH,EAAOF,EAAWrH,CAAC,EAAG,SAAUyB,EAAG,CACjC+F,GAAMpH,EAAGqB,EAAGgG,EAAWjF,EAAS6E,EAAYC,CAAG,CACrD,CAAK,EACGG,GACFH,EAAI,KAAKtH,CAAC,EAGhB,CCzCA,SAASyH,GAAUrH,EAAGyE,EAAI,CACxB,OAAOpC,GAAIrC,EAAGyE,EAAI,MAAM,CAC1B,CCFA,SAAS6C,GAAStH,EAAGyE,EAAI,CACvB,OAAOpC,GAAIrC,EAAGyE,EAAI,KAAK,CACzB,CCGA8C,EAAe,iBAAmBC,EAClCD,EAAe,cAAgBE,EAC/BF,EAAe,aAAeG,GAC9BH,EAAe,UAAYI,GAC3BJ,EAAe,UAAYK,GAC3BL,EAAe,cAAgBM,GAmC/B,SAASN,EAAevH,EAAG,CACzBA,EAAI4C,GAAS5C,CAAC,EACdqG,EAAYrG,CAAC,EACb,IAAIwG,EAAID,GAAavG,CAAC,EACtBwH,EAAiBhB,CAAC,EAClBiB,EAAcjB,EAAGxG,CAAC,EAGlB,QADIQ,EAAGsH,EACCtH,EAAImH,GAAUnB,CAAC,GACrBsB,EAAIF,GAAUpB,EAAGxG,EAAGQ,CAAC,EACrBqH,GAAcrB,EAAGxG,EAAGQ,EAAGsH,CAAC,CAE5B,CAKA,SAASL,EAAcjB,EAAGxG,EAAG,CAC3B,IAAIyE,EAAKsD,GAAcvB,EAAGA,EAAE,MAAO,CAAA,EACnC/B,EAAKA,EAAG,MAAM,EAAGA,EAAG,OAAS,CAAC,EAC9BzD,EAAUyD,EAAI,SAAU7E,EAAG,CACzBoI,GAAexB,EAAGxG,EAAGJ,CAAC,CAC1B,CAAG,CACH,CAEA,SAASoI,GAAexB,EAAGxG,EAAGiI,EAAO,CACnC,IAAIC,EAAW1B,EAAE,KAAKyB,CAAK,EACvBE,EAASD,EAAS,OACtB1B,EAAE,KAAKyB,EAAOE,CAAM,EAAE,SAAWT,GAAalB,EAAGxG,EAAGiI,CAAK,CAC3D,CAMA,SAASP,GAAalB,EAAGxG,EAAGiI,EAAO,CACjC,IAAIC,EAAW1B,EAAE,KAAKyB,CAAK,EACvBE,EAASD,EAAS,OAElBE,EAAc,GAEdC,EAAYrI,EAAE,KAAKiI,EAAOE,CAAM,EAEhCG,EAAW,EAEf,OAAKD,IACHD,EAAc,GACdC,EAAYrI,EAAE,KAAKmI,EAAQF,CAAK,GAGlCK,EAAWD,EAAU,OAErBrH,EAAUhB,EAAE,UAAUiI,CAAK,EAAG,SAAUzH,EAAG,CACzC,IAAI+H,EAAY/H,EAAE,IAAMyH,EACtBtN,EAAQ4N,EAAY/H,EAAE,EAAIA,EAAE,EAE9B,GAAI7F,IAAUwN,EAAQ,CACpB,IAAIK,EAAeD,IAAcH,EAC/BK,EAAczI,EAAE,KAAKQ,CAAC,EAAE,OAG1B,GADA8H,GAAYE,EAAeC,EAAc,CAACA,EACtCC,GAAWlC,EAAGyB,EAAOtN,CAAK,EAAG,CAC/B,IAAIgO,EAAgBnC,EAAE,KAAKyB,EAAOtN,CAAK,EAAE,SACzC2N,GAAYE,EAAe,CAACG,EAAgBA,CAC7C,CACF,CACL,CAAG,EAEML,CACT,CAEA,SAASd,EAAiBoB,EAAMC,EAAM,CAChC,UAAU,OAAS,IACrBA,EAAOD,EAAK,MAAO,EAAC,CAAC,GAEvBE,GAAgBF,EAAM,CAAA,EAAI,EAAGC,CAAI,CACnC,CAEA,SAASC,GAAgBF,EAAMxG,EAAS2G,EAASnJ,EAAGuI,EAAQ,CAC1D,IAAIa,EAAMD,EACN9G,EAAQ2G,EAAK,KAAKhJ,CAAC,EAEvB,OAAAwC,EAAQxC,CAAC,EAAI,GACboB,EAAU4H,EAAK,UAAUhJ,CAAC,EAAG,SAAUyB,EAAG,CACnC,OAAO,UAAU,eAAe,KAAKe,EAASf,CAAC,IAClD0H,EAAUD,GAAgBF,EAAMxG,EAAS2G,EAAS1H,EAAGzB,CAAC,EAE5D,CAAG,EAEDqC,EAAM,IAAM+G,EACZ/G,EAAM,IAAM8G,IACRZ,EACFlG,EAAM,OAASkG,EAGf,OAAOlG,EAAM,OAGR8G,CACT,CAEA,SAASpB,GAAUiB,EAAM,CACvB,OAAOK,EAAOL,EAAK,MAAO,EAAE,SAAUpI,EAAG,CACvC,OAAOoI,EAAK,KAAKpI,CAAC,EAAE,SAAW,CACnC,CAAG,CACH,CAEA,SAASoH,GAAUpB,EAAGxG,EAAGiB,EAAM,CAC7B,IAAIrB,EAAIqB,EAAK,EACTI,EAAIJ,EAAK,EAKRjB,EAAE,QAAQJ,EAAGyB,CAAC,IACjBzB,EAAIqB,EAAK,EACTI,EAAIJ,EAAK,GAGX,IAAIiI,EAAS1C,EAAE,KAAK5G,CAAC,EACjBuJ,EAAS3C,EAAE,KAAKnF,CAAC,EACjB+H,EAAYF,EACZG,EAAO,GAIPH,EAAO,IAAMC,EAAO,MACtBC,EAAYD,EACZE,EAAO,IAGT,IAAIC,EAAaC,EAASvJ,EAAE,MAAO,EAAE,SAAUiB,EAAM,CACnD,OACEoI,IAASG,GAAahD,EAAGA,EAAE,KAAKvF,EAAK,CAAC,EAAGmI,CAAS,GAClDC,IAASG,GAAahD,EAAGA,EAAE,KAAKvF,EAAK,CAAC,EAAGmI,CAAS,CAExD,CAAG,EAED,OAAOtC,EAAQwC,EAAY,SAAUrI,EAAM,CACzC,OAAOqF,EAAMtG,EAAGiB,CAAI,CACxB,CAAG,CACH,CAEA,SAAS4G,GAAcrB,EAAGxG,EAAGQ,EAAGsH,EAAG,CACjC,IAAIlI,EAAIY,EAAE,EACNa,EAAIb,EAAE,EACVgG,EAAE,WAAW5G,EAAGyB,CAAC,EACjBmF,EAAE,QAAQsB,EAAE,EAAGA,EAAE,EAAG,CAAA,CAAE,EACtBN,EAAiBhB,CAAC,EAClBiB,EAAcjB,EAAGxG,CAAC,EAClByJ,GAAYjD,EAAGxG,CAAC,CAClB,CAEA,SAASyJ,GAAYjD,EAAGxG,EAAG,CACzB,IAAI6I,EAAOI,EAAOzC,EAAE,MAAO,EAAE,SAAU5G,EAAG,CACxC,MAAO,CAACI,EAAE,KAAKJ,CAAC,EAAE,MACtB,CAAG,EACG6E,EAAKiF,GAAalD,EAAGqC,CAAI,EAC7BpE,EAAKA,EAAG,MAAM,CAAC,EACfzD,EAAUyD,EAAI,SAAU7E,EAAG,CACzB,IAAIuI,EAAS3B,EAAE,KAAK5G,CAAC,EAAE,OACrBqB,EAAOjB,EAAE,KAAKJ,EAAGuI,CAAM,EACvBwB,EAAU,GAEP1I,IACHA,EAAOjB,EAAE,KAAKmI,EAAQvI,CAAC,EACvB+J,EAAU,IAGZ3J,EAAE,KAAKJ,CAAC,EAAE,KAAOI,EAAE,KAAKmI,CAAM,EAAE,MAAQwB,EAAU1I,EAAK,OAAS,CAACA,EAAK,OAC1E,CAAG,CACH,CAKA,SAASyH,GAAWE,EAAMgB,EAAGhK,EAAG,CAC9B,OAAOgJ,EAAK,QAAQgB,EAAGhK,CAAC,CAC1B,CAMA,SAAS4J,GAAaZ,EAAMM,EAAQW,EAAW,CAC7C,OAAOA,EAAU,KAAOX,EAAO,KAAOA,EAAO,KAAOW,EAAU,GAChE,CClNA,SAAS/F,GAAK9D,EAAG,CACf,OAAQA,EAAE,MAAK,EAAG,OAAM,CACtB,IAAK,kBACH8J,GAAqB9J,CAAC,EACtB,MACF,IAAK,aACH+J,GAAgB/J,CAAC,EACjB,MACF,IAAK,eACHgK,GAAkBhK,CAAC,EACnB,MACF,QACE8J,GAAqB9J,CAAC,CACzB,CACH,CAGA,IAAIgK,GAAoB3D,EAExB,SAAS0D,GAAgB/J,EAAG,CAC1BqG,EAAYrG,CAAC,EACbuG,GAAavG,CAAC,CAChB,CAEA,SAAS8J,GAAqB9J,EAAG,CAC/BuH,EAAevH,CAAC,CAClB,CCvBA,SAAS8B,GAAI9B,EAAG,CACd,IAAI6I,EAAOxD,EAAkBrF,EAAG,OAAQ,CAAA,EAAI,OAAO,EAC/CiK,EAASC,GAAWlK,CAAC,EACrBmK,EAASxF,EAAMyF,EAASH,CAAM,CAAC,EAAI,EACnCI,EAAU,EAAIF,EAAS,EAE3BnK,EAAE,MAAK,EAAG,YAAc6I,EAGxB7H,EAAUhB,EAAE,MAAO,EAAE,SAAUQ,EAAG,CAChCR,EAAE,KAAKQ,CAAC,EAAE,QAAU6J,CACxB,CAAG,EAGD,IAAInJ,EAASoJ,GAAWtK,CAAC,EAAI,EAG7BgB,EAAUhB,EAAE,SAAU,EAAE,SAAUiI,EAAO,CACvC5F,GAAIrC,EAAG6I,EAAMwB,EAASnJ,EAAQiJ,EAAQF,EAAQhC,CAAK,CACvD,CAAG,EAIDjI,EAAE,MAAK,EAAG,eAAiBqK,CAC7B,CAEA,SAAShI,GAAIrC,EAAG6I,EAAMwB,EAASnJ,EAAQiJ,EAAQF,EAAQrK,EAAG,CACxD,IAAIoF,EAAWhF,EAAE,SAASJ,CAAC,EAC3B,GAAI,CAACoF,EAAS,OAAQ,CAChBpF,IAAMiJ,GACR7I,EAAE,QAAQ6I,EAAMjJ,EAAG,CAAE,OAAQ,EAAG,OAAQyK,CAAO,CAAE,EAEnD,MACD,CAED,IAAIE,EAAMC,GAAmBxK,EAAG,KAAK,EACjCyK,EAASD,GAAmBxK,EAAG,KAAK,EACpCiC,EAAQjC,EAAE,KAAKJ,CAAC,EAEpBI,EAAE,UAAUuK,EAAK3K,CAAC,EAClBqC,EAAM,UAAYsI,EAClBvK,EAAE,UAAUyK,EAAQ7K,CAAC,EACrBqC,EAAM,aAAewI,EAErBzJ,EAAUgE,EAAU,SAAUiD,EAAO,CACnC5F,GAAIrC,EAAG6I,EAAMwB,EAASnJ,EAAQiJ,EAAQF,EAAQhC,CAAK,EAEnD,IAAIyC,EAAY1K,EAAE,KAAKiI,CAAK,EACxB0C,EAAWD,EAAU,UAAYA,EAAU,UAAYzC,EACvD2C,EAAcF,EAAU,aAAeA,EAAU,aAAezC,EAChE4C,EAAaH,EAAU,UAAYxJ,EAAS,EAAIA,EAChD4J,EAASH,IAAaC,EAAc,EAAIT,EAASF,EAAOrK,CAAC,EAAI,EAEjEI,EAAE,QAAQuK,EAAKI,EAAU,CACvB,OAAQE,EACR,OAAQC,EACR,YAAa,EACnB,CAAK,EAED9K,EAAE,QAAQ4K,EAAaH,EAAQ,CAC7B,OAAQI,EACR,OAAQC,EACR,YAAa,EACnB,CAAK,CACL,CAAG,EAEI9K,EAAE,OAAOJ,CAAC,GACbI,EAAE,QAAQ6I,EAAM0B,EAAK,CAAE,OAAQ,EAAG,OAAQJ,EAASF,EAAOrK,CAAC,CAAG,CAAA,CAElE,CAEA,SAASsK,GAAWlK,EAAG,CACrB,IAAIiK,EAAS,CAAA,EACb,SAAS5H,EAAIzC,EAAGmL,EAAO,CACrB,IAAI/F,EAAWhF,EAAE,SAASJ,CAAC,EACvBoF,GAAYA,EAAS,QACvBhE,EAAUgE,EAAU,SAAUiD,EAAO,CACnC5F,EAAI4F,EAAO8C,EAAQ,CAAC,CAC5B,CAAO,EAEHd,EAAOrK,CAAC,EAAImL,CACb,CACD/J,OAAAA,EAAUhB,EAAE,SAAU,EAAE,SAAUJ,EAAG,CACnCyC,EAAIzC,EAAG,CAAC,CACZ,CAAG,EACMqK,CACT,CAEA,SAASK,GAAWtK,EAAG,CACrB,OAAOgL,EACLhL,EAAE,MAAO,EACT,SAAUkH,EAAK1G,EAAG,CAChB,OAAO0G,EAAMlH,EAAE,KAAKQ,CAAC,EAAE,MACxB,EACD,CACJ,CACA,CAEA,SAASyK,GAAQjL,EAAG,CAClB,IAAIkL,EAAalL,EAAE,QACnBA,EAAE,WAAWkL,EAAW,WAAW,EACnC,OAAOA,EAAW,YAClBlK,EAAUhB,EAAE,MAAO,EAAE,SAAUQ,EAAG,CAChC,IAAIS,EAAOjB,EAAE,KAAKQ,CAAC,EACfS,EAAK,aACPjB,EAAE,WAAWQ,CAAC,CAEpB,CAAG,CACH,CCpIA,SAAS2K,GAAuBnL,EAAGoL,EAAI3G,EAAI,CACzC,IAAIW,EAAO,CAAE,EACXiG,EAEFrK,EAAUyD,EAAI,SAAU7E,EAAG,CAIzB,QAHIqI,EAAQjI,EAAE,OAAOJ,CAAC,EACpBuI,EACAmD,EACKrD,GAAO,CASZ,GARAE,EAASnI,EAAE,OAAOiI,CAAK,EACnBE,GACFmD,EAAYlG,EAAK+C,CAAM,EACvB/C,EAAK+C,CAAM,EAAIF,IAEfqD,EAAYD,EACZA,EAAWpD,GAETqD,GAAaA,IAAcrD,EAAO,CACpCmD,EAAG,QAAQE,EAAWrD,CAAK,EAC3B,MACD,CACDA,EAAQE,CACT,CACL,CAAG,CAyBH,CCjBA,SAASoD,GAAgBvL,EAAG8D,EAAM0H,EAAc,CAC9C,IAAI3C,EAAO4C,GAAezL,CAAC,EACzBnF,EAAS,IAAI2G,EAAM,CAAE,SAAU,EAAI,CAAE,EAClC,SAAS,CAAE,KAAMqH,EAAM,EACvB,oBAAoB,SAAUjJ,EAAG,CAChC,OAAOI,EAAE,KAAKJ,CAAC,CACvB,CAAO,EAELoB,OAAAA,EAAUhB,EAAE,MAAO,EAAE,SAAUJ,EAAG,CAChC,IAAIiE,EAAO7D,EAAE,KAAKJ,CAAC,EACjBuI,EAASnI,EAAE,OAAOJ,CAAC,GAEjBiE,EAAK,OAASC,GAASD,EAAK,SAAWC,GAAQA,GAAQD,EAAK,WAC9DhJ,EAAO,QAAQ+E,CAAC,EAChB/E,EAAO,UAAU+E,EAAGuI,GAAUU,CAAI,EAGlC7H,EAAUhB,EAAEwL,CAAY,EAAE5L,CAAC,EAAG,SAAUY,EAAG,CACzC,IAAIoJ,EAAIpJ,EAAE,IAAMZ,EAAIY,EAAE,EAAIA,EAAE,EAC1BS,EAAOpG,EAAO,KAAK+O,EAAGhK,CAAC,EACvBsB,EAAU6C,EAAc9C,CAAI,EAAkB,EAAdA,EAAK,OACvCpG,EAAO,QAAQ+O,EAAGhK,EAAG,CAAE,OAAQI,EAAE,KAAKQ,CAAC,EAAE,OAASU,CAAQ,CAAA,CAClE,CAAO,EAEG,OAAO,UAAU,eAAe,KAAK2C,EAAM,SAAS,GACtDhJ,EAAO,QAAQ+E,EAAG,CAChB,WAAYiE,EAAK,WAAWC,CAAI,EAChC,YAAaD,EAAK,YAAYC,CAAI,CAC5C,CAAS,EAGT,CAAG,EAEMjJ,CACT,CAEA,SAAS4Q,GAAezL,EAAG,CAEzB,QADIJ,EACGI,EAAE,QAASJ,EAAIsC,EAAW,OAAO,CAAC,GAAG,CAC5C,OAAOtC,CACT,CCvDA,SAAS8L,GAAW1L,EAAG2D,EAAU,CAE/B,QADIgI,EAAK,EACA7K,EAAI,EAAGA,EAAI6C,EAAS,OAAQ,EAAE7C,EACrC6K,GAAMC,GAAmB5L,EAAG2D,EAAS7C,EAAI,CAAC,EAAG6C,EAAS7C,CAAC,CAAC,EAE1D,OAAO6K,CACT,CAEA,SAASC,GAAmB5L,EAAG6L,EAAYC,EAAY,CAuBrD,QAnBIC,EAAWC,GACbF,EACAvL,EAAMuL,EAAY,SAAUlM,EAAGkB,EAAG,CAChC,OAAOA,CACb,CAAK,CACL,EACMmL,EAAe3L,EACjBC,EAAMsL,EAAY,SAAUjM,EAAG,CAC7B,OAAOsM,EACL3L,EAAMP,EAAE,SAASJ,CAAC,EAAG,SAAUY,EAAG,CAChC,MAAO,CAAE,IAAKuL,EAASvL,EAAE,CAAC,EAAG,OAAQR,EAAE,KAAKQ,CAAC,EAAE,MAAM,CAC/D,CAAS,EACD,KACR,CACA,CAAK,CACL,EAGM2L,EAAa,EACVA,EAAaL,EAAW,QAAQK,IAAe,EACtD,IAAIC,EAAW,EAAID,EAAa,EAChCA,GAAc,EACd,IAAIvD,EAAOrI,EAAM,IAAI,MAAM6L,CAAQ,EAAG,UAAY,CAChD,MAAO,EACX,CAAG,EAGGT,EAAK,EACT3K,OAAAA,EAEEiL,EAAa,QAAQ,SAAU3M,EAAO,CACpC,IAAIhD,EAAQgD,EAAM,IAAM6M,EACxBvD,EAAKtM,CAAK,GAAKgD,EAAM,OAGrB,QAFI+M,EAAY,EAET/P,EAAQ,GAETA,EAAQ,IACV+P,GAAazD,EAAKtM,EAAQ,CAAC,GAG7BA,EAASA,EAAQ,GAAM,EACvBsM,EAAKtM,CAAK,GAAKgD,EAAM,OAEvBqM,GAAMrM,EAAM,OAAS+M,CAC3B,CAAK,CACL,EAESV,CACT,CCpEO,SAASW,GAAUtM,EAAG,CAC3B,IAAIoC,EAAU,CAAA,EACVmK,EAAchD,EAASvJ,EAAE,MAAO,EAAE,SAAUJ,EAAG,CACjD,MAAO,CAACI,EAAE,SAASJ,CAAC,EAAE,MAC1B,CAAG,EACGgE,EAAUe,EACZpE,EAAMgM,EAAa,SAAU3M,EAAG,CAC9B,OAAOI,EAAE,KAAKJ,CAAC,EAAE,IACvB,CAAK,CACL,EACM0E,EAAS/D,EAAMsB,EAAQ+B,EAAU,CAAC,EAAG,UAAY,CACnD,MAAO,EACX,CAAG,EAED,SAASvB,EAAIzC,EAAG,CACd,GAAIuE,CAAAA,GAAM/B,EAASxC,CAAC,EACpB,CAAAwC,EAAQxC,CAAC,EAAI,GACb,IAAIiE,EAAO7D,EAAE,KAAKJ,CAAC,EACnB0E,EAAOT,EAAK,IAAI,EAAE,KAAKjE,CAAC,EACxBoB,EAAUhB,EAAE,WAAWJ,CAAC,EAAGyC,CAAG,EAC/B,CAED,IAAImK,EAAYN,EAASK,EAAa,SAAU3M,EAAG,CACjD,OAAOI,EAAE,KAAKJ,CAAC,EAAE,IACrB,CAAG,EACDoB,OAAAA,EAAUwL,EAAWnK,CAAG,EAEjBiC,CACT,CCrCA,SAASmI,GAAWzM,EAAG0M,EAAS,CAC9B,OAAOnM,EAAMmM,EAAS,SAAU9M,EAAG,CACjC,IAAI+M,EAAM3M,EAAE,QAAQJ,CAAC,EACrB,GAAK+M,EAAI,OAEF,CACL,IAAI9R,EAASmQ,EACX2B,EACA,SAAUzF,EAAK1G,EAAG,CAChB,IAAIS,EAAOjB,EAAE,KAAKQ,CAAC,EACjBoM,EAAQ5M,EAAE,KAAKQ,EAAE,CAAC,EACpB,MAAO,CACL,IAAK0G,EAAI,IAAMjG,EAAK,OAAS2L,EAAM,MACnC,OAAQ1F,EAAI,OAASjG,EAAK,MACtC,CACS,EACD,CAAE,IAAK,EAAG,OAAQ,CAAG,CAC7B,EAEM,MAAO,CACL,EAAGrB,EACH,WAAY/E,EAAO,IAAMA,EAAO,OAChC,OAAQA,EAAO,MACvB,CACK,KApBC,OAAO,CAAE,EAAG+E,EAqBlB,CAAG,CACH,CCDA,SAASiN,GAAiBC,EAAS1B,EAAI,CACrC,IAAI2B,EAAgB,CAAA,EACpB/L,EAAU8L,EAAS,SAAUxN,EAAO,EAAG,CACrC,IAAI0N,EAAOD,EAAczN,EAAM,CAAC,EAAI,CAClC,SAAU,EACV,GAAI,CAAE,EACN,IAAK,CAAE,EACP,GAAI,CAACA,EAAM,CAAC,EACZ,CACN,EACSyE,EAAczE,EAAM,UAAU,IAEjC0N,EAAI,WAAa1N,EAAM,WAEvB0N,EAAI,OAAS1N,EAAM,OAEzB,CAAG,EAED0B,EAAUoK,EAAG,MAAO,EAAE,SAAU5K,EAAG,CACjC,IAAIyM,EAASF,EAAcvM,EAAE,CAAC,EAC1B0M,EAASH,EAAcvM,EAAE,CAAC,EAC1B,CAACuD,EAAckJ,CAAM,GAAK,CAAClJ,EAAcmJ,CAAM,IACjDA,EAAO,WACPD,EAAO,IAAI,KAAKF,EAAcvM,EAAE,CAAC,CAAC,EAExC,CAAG,EAED,IAAI2M,EAAY5D,EAASwD,EAAe,SAAUzN,EAAO,CAEvD,MAAO,CAACA,EAAM,QAClB,CAAG,EAED,OAAO8N,GAAmBD,CAAS,CACrC,CAEA,SAASC,GAAmBD,EAAW,CACrC,IAAIL,EAAU,CAAA,EAEd,SAASO,EAASC,EAAQ,CACxB,OAAO,SAAUnM,EAAQ,CACnBA,EAAO,SAIT4C,EAAc5C,EAAO,UAAU,GAC/B4C,EAAcuJ,EAAO,UAAU,GAC/BnM,EAAO,YAAcmM,EAAO,aAE5BC,GAAaD,EAAQnM,CAAM,CAEnC,CACG,CAED,SAASqM,EAAUF,EAAQ,CACzB,OAAO,SAAUhM,EAAQ,CACvBA,EAAO,GAAM,KAAKgM,CAAM,EACpB,EAAEhM,EAAO,WAAa,GACxB6L,EAAU,KAAK7L,CAAM,CAE7B,CACG,CAED,KAAO6L,EAAU,QAAQ,CACvB,IAAI7N,EAAQ6N,EAAU,MACtBL,EAAQ,KAAKxN,CAAK,EAClB0B,EAAU1B,EAAM,GAAM,QAAO,EAAI+N,EAAS/N,CAAK,CAAC,EAChD0B,EAAU1B,EAAM,IAAKkO,EAAUlO,CAAK,CAAC,CACtC,CAED,OAAOiB,EACLgJ,EAASuD,EAAS,SAAUxN,EAAO,CACjC,MAAO,CAACA,EAAM,MACpB,CAAK,EACD,SAAUA,EAAO,CACf,OAAOmO,EAAOnO,EAAO,CAAC,KAAM,IAAK,aAAc,QAAQ,CAAC,CACzD,CACL,CACA,CAEA,SAASiO,GAAaG,EAAQC,EAAQ,CACpC,IAAIC,EAAM,EACN1M,EAAS,EAETwM,EAAO,SACTE,GAAOF,EAAO,WAAaA,EAAO,OAClCxM,GAAUwM,EAAO,QAGfC,EAAO,SACTC,GAAOD,EAAO,WAAaA,EAAO,OAClCzM,GAAUyM,EAAO,QAGnBD,EAAO,GAAKC,EAAO,GAAG,OAAOD,EAAO,EAAE,EACtCA,EAAO,WAAaE,EAAM1M,EAC1BwM,EAAO,OAASxM,EAChBwM,EAAO,EAAI,KAAK,IAAIC,EAAO,EAAGD,EAAO,CAAC,EACtCC,EAAO,OAAS,EAClB,CC1HA,SAASE,GAAKf,EAASgB,EAAW,CAChC,IAAIC,EAAQC,GAAelB,EAAS,SAAUxN,EAAO,CACnD,OAAO,OAAO,UAAU,eAAe,KAAKA,EAAO,YAAY,CACnE,CAAG,EACG2O,EAAWF,EAAM,IACnBG,EAAahC,EAAS6B,EAAM,IAAK,SAAUzO,EAAO,CAChD,MAAO,CAACA,EAAM,CACpB,CAAK,EACDmF,EAAK,CAAE,EACPmJ,EAAM,EACN1M,EAAS,EACTiN,EAAU,EAEZF,EAAS,KAAKG,GAAgB,CAAC,CAACN,CAAS,CAAC,EAE1CK,EAAUE,GAAkB5J,EAAIyJ,EAAYC,CAAO,EAEnDnN,EAAUiN,EAAU,SAAU3O,EAAO,CACnC6O,GAAW7O,EAAM,GAAG,OACpBmF,EAAG,KAAKnF,EAAM,EAAE,EAChBsO,GAAOtO,EAAM,WAAaA,EAAM,OAChC4B,GAAU5B,EAAM,OAChB6O,EAAUE,GAAkB5J,EAAIyJ,EAAYC,CAAO,CACvD,CAAG,EAED,IAAItT,EAAS,CAAE,GAAIyF,EAAUmE,CAAE,CAAC,EAChC,OAAIvD,IACFrG,EAAO,WAAa+S,EAAM1M,EAC1BrG,EAAO,OAASqG,GAEXrG,CACT,CAEA,SAASwT,GAAkB5J,EAAIyJ,EAAY5R,EAAO,CAEhD,QADIgS,EACGJ,EAAW,SAAWI,EAAOC,EAAOL,CAAU,GAAG,GAAK5R,GAC3D4R,EAAW,IAAG,EACdzJ,EAAG,KAAK6J,EAAK,EAAE,EACfhS,IAEF,OAAOA,CACT,CAEA,SAAS8R,GAAgBI,EAAM,CAC7B,OAAO,SAAUvB,EAAQC,EAAQ,CAC/B,OAAID,EAAO,WAAaC,EAAO,WACtB,GACED,EAAO,WAAaC,EAAO,WAC7B,EAGDsB,EAA6BtB,EAAO,EAAID,EAAO,EAAxCA,EAAO,EAAIC,EAAO,CACrC,CACA,CCnDA,SAASuB,GAAazO,EAAGJ,EAAGwL,EAAI0C,EAAW,CACzC,IAAIpB,EAAU1M,EAAE,SAASJ,CAAC,EACtBiE,EAAO7D,EAAE,KAAKJ,CAAC,EACf8O,EAAK7K,EAAOA,EAAK,WAAa,OAC9B8K,EAAK9K,EAAOA,EAAK,YAAc,OAC/B+K,EAAY,CAAA,EAEZF,IACFhC,EAAUnD,EAASmD,EAAS,SAAUrL,EAAG,CACvC,OAAOA,IAAMqN,GAAMrN,IAAMsN,CAC/B,CAAK,GAGH,IAAIE,EAAcpC,GAAWzM,EAAG0M,CAAO,EACvC1L,EAAU6N,EAAa,SAAUvP,EAAO,CACtC,GAAIU,EAAE,SAASV,EAAM,CAAC,EAAE,OAAQ,CAC9B,IAAIwP,EAAiBL,GAAazO,EAAGV,EAAM,EAAG8L,EAAI0C,CAAS,EAC3Dc,EAAUtP,EAAM,CAAC,EAAIwP,EACjB,OAAO,UAAU,eAAe,KAAKA,EAAgB,YAAY,GACnEC,GAAiBzP,EAAOwP,CAAc,CAEzC,CACL,CAAG,EAED,IAAIhC,EAAUD,GAAiBgC,EAAazD,CAAE,EAC9C4D,GAAgBlC,EAAS8B,CAAS,EAElC,IAAI/T,EAASgT,GAAKf,EAASgB,CAAS,EAEpC,GAAIY,IACF7T,EAAO,GAAKyF,EAAU,CAACoO,EAAI7T,EAAO,GAAI8T,CAAE,CAAC,EACrC3O,EAAE,aAAa0O,CAAE,EAAE,QAAQ,CAC7B,IAAIO,EAASjP,EAAE,KAAKA,EAAE,aAAa0O,CAAE,EAAE,CAAC,CAAC,EACvCQ,EAASlP,EAAE,KAAKA,EAAE,aAAa2O,CAAE,EAAE,CAAC,CAAC,EAClC,OAAO,UAAU,eAAe,KAAK9T,EAAQ,YAAY,IAC5DA,EAAO,WAAa,EACpBA,EAAO,OAAS,GAElBA,EAAO,YACJA,EAAO,WAAaA,EAAO,OAASoU,EAAO,MAAQC,EAAO,QAAUrU,EAAO,OAAS,GACvFA,EAAO,QAAU,CAClB,CAGH,OAAOA,CACT,CAEA,SAASmU,GAAgBlC,EAAS8B,EAAW,CAC3C5N,EAAU8L,EAAS,SAAUxN,EAAO,CAClCA,EAAM,GAAKgB,EACThB,EAAM,GAAG,IAAI,SAAUM,EAAG,CACxB,OAAIgP,EAAUhP,CAAC,EACNgP,EAAUhP,CAAC,EAAE,GAEfA,CACf,CAAO,CACP,CACA,CAAG,CACH,CAEA,SAASmP,GAAiBrB,EAAQ/S,EAAO,CAClCoJ,EAAc2J,EAAO,UAAU,GAMlCA,EAAO,WAAa/S,EAAM,WAC1B+S,EAAO,OAAS/S,EAAM,SANtB+S,EAAO,YACJA,EAAO,WAAaA,EAAO,OAAS/S,EAAM,WAAaA,EAAM,SAC7D+S,EAAO,OAAS/S,EAAM,QACzB+S,EAAO,QAAU/S,EAAM,OAK3B,CCnDA,SAAS+B,GAAMsD,EAAG,CAChB,IAAI4D,EAAUuL,GAAanP,CAAC,EAC1BoP,EAAkBC,GAAiBrP,EAAG6B,EAAQ,EAAG+B,EAAU,CAAC,EAAG,SAAS,EACxE0L,EAAgBD,GAAiBrP,EAAG6B,EAAQ+B,EAAU,EAAG,GAAI,EAAE,EAAG,UAAU,EAE1ED,EAAW2I,GAAUtM,CAAC,EAC1BuP,GAAYvP,EAAG2D,CAAQ,EAKvB,QAHI6L,EAAS,OAAO,kBAClBC,EAEO3O,EAAI,EAAG4O,EAAW,EAAGA,EAAW,EAAG,EAAE5O,EAAG,EAAE4O,EAAU,CAC3DC,GAAiB7O,EAAI,EAAIsO,EAAkBE,EAAexO,EAAI,GAAK,CAAC,EAEpE6C,EAAWiM,EAAsB5P,CAAC,EAClC,IAAI2L,EAAKD,GAAW1L,EAAG2D,CAAQ,EAC3BgI,EAAK6D,IACPE,EAAW,EACXD,EAAOI,GAAYlM,CAAQ,EAC3B6L,EAAS7D,EAEZ,CAED4D,GAAYvP,EAAGyP,CAAI,CACrB,CAEA,SAASJ,GAAiBrP,EAAG8P,EAAOtE,EAAc,CAChD,OAAOjL,EAAMuP,EAAO,SAAUhM,EAAM,CAClC,OAAOyH,GAAgBvL,EAAG8D,EAAM0H,CAAY,CAChD,CAAG,CACH,CAEA,SAASmE,GAAiBI,EAAajC,EAAW,CAChD,IAAI1C,EAAK,IAAI5J,EACbR,EAAU+O,EAAa,SAAUC,EAAI,CACnC,IAAInH,EAAOmH,EAAG,MAAK,EAAG,KAClBC,EAASxB,GAAauB,EAAInH,EAAMuC,EAAI0C,CAAS,EACjD9M,EAAUiP,EAAO,GAAI,SAAUrQ,EAAGkB,EAAG,CACnCkP,EAAG,KAAKpQ,CAAC,EAAE,MAAQkB,CACzB,CAAK,EACDqK,GAAuB6E,EAAI5E,EAAI6E,EAAO,EAAE,CAC5C,CAAG,CACH,CAEA,SAASV,GAAYvP,EAAG2D,EAAU,CAChC3C,EAAU2C,EAAU,SAAUuM,EAAO,CACnClP,EAAUkP,EAAO,SAAUtQ,EAAGkB,EAAG,CAC/Bd,EAAE,KAAKJ,CAAC,EAAE,MAAQkB,CACxB,CAAK,CACL,CAAG,CACH,CCxEA,SAASqP,GAAkBnQ,EAAG,CAC5B,IAAIoQ,EAAgB/I,GAAUrH,CAAC,EAE/BgB,EAAUhB,EAAE,MAAO,EAAC,YAAa,SAAUJ,EAAG,CAU5C,QATIiE,EAAO7D,EAAE,KAAKJ,CAAC,EACfyQ,EAAUxM,EAAK,QACfyM,EAAWC,GAASvQ,EAAGoQ,EAAeC,EAAQ,EAAGA,EAAQ,CAAC,EAC1D9S,EAAO+S,EAAS,KAChBE,EAAMF,EAAS,IACfG,EAAU,EACVC,EAAQnT,EAAKkT,CAAO,EACpBE,EAAY,GAET/Q,IAAMyQ,EAAQ,GAAG,CAGtB,GAFAxM,EAAO7D,EAAE,KAAKJ,CAAC,EAEX+Q,EAAW,CACb,MAAQD,EAAQnT,EAAKkT,CAAO,KAAOD,GAAOxQ,EAAE,KAAK0Q,CAAK,EAAE,QAAU7M,EAAK,MACrE4M,IAGEC,IAAUF,IACZG,EAAY,GAEf,CAED,GAAI,CAACA,EAAW,CACd,KACEF,EAAUlT,EAAK,OAAS,GACxByC,EAAE,KAAM0Q,EAAQnT,EAAKkT,EAAU,CAAC,CAAG,EAAC,SAAW5M,EAAK,MAEpD4M,IAEFC,EAAQnT,EAAKkT,CAAO,CACrB,CAEDzQ,EAAE,UAAUJ,EAAG8Q,CAAK,EACpB9Q,EAAII,EAAE,WAAWJ,CAAC,EAAE,CAAC,CACtB,CACL,CAAG,CACH,CAIA,SAAS2Q,GAASvQ,EAAGoQ,EAAexQ,EAAGyB,EAAG,CACxC,IAAIuP,EAAQ,CAAA,EACRC,EAAQ,CAAA,EACR7H,EAAM,KAAK,IAAIoH,EAAcxQ,CAAC,EAAE,IAAKwQ,EAAc/O,CAAC,EAAE,GAAG,EACzDyP,EAAM,KAAK,IAAIV,EAAcxQ,CAAC,EAAE,IAAKwQ,EAAc/O,CAAC,EAAE,GAAG,EACzD8G,EACAqI,EAGJrI,EAASvI,EACT,GACEuI,EAASnI,EAAE,OAAOmI,CAAM,EACxByI,EAAM,KAAKzI,CAAM,QACVA,IAAWiI,EAAcjI,CAAM,EAAE,IAAMa,GAAO8H,EAAMV,EAAcjI,CAAM,EAAE,MAKnF,IAJAqI,EAAMrI,EAGNA,EAAS9G,GACD8G,EAASnI,EAAE,OAAOmI,CAAM,KAAOqI,GACrCK,EAAM,KAAK1I,CAAM,EAGnB,MAAO,CAAE,KAAMyI,EAAM,OAAOC,EAAM,SAAS,EAAG,IAAKL,EACrD,CAEA,SAASnJ,GAAUrH,EAAG,CACpB,IAAInF,EAAS,CAAA,EACTiW,EAAM,EAEV,SAASzO,EAAIzC,EAAG,CACd,IAAIoJ,EAAM8H,EACV9P,EAAUhB,EAAE,SAASJ,CAAC,EAAGyC,CAAG,EAC5BxH,EAAO+E,CAAC,EAAI,CAAE,IAAKoJ,EAAK,IAAK8H,IAC9B,CACD9P,OAAAA,EAAUhB,EAAE,SAAU,EAAEqC,CAAG,EAEpBxH,CACT,CC9CA,SAASkW,GAAmB/Q,EAAG2D,EAAU,CACvC,IAAIqN,EAAY,CAAA,EAEhB,SAASC,EAAWC,EAAWhB,EAAO,CACpC,IAEEiB,EAAK,EAGLC,EAAU,EACVC,EAAkBH,EAAU,OAC5BI,EAAW/C,EAAO2B,CAAK,EAEzBlP,OAAAA,EAAUkP,EAAO,SAAUtQ,EAAGkB,EAAG,CAC/B,IAAIO,EAAIkQ,GAA0BvR,EAAGJ,CAAC,EACpC4R,EAAKnQ,EAAIrB,EAAE,KAAKqB,CAAC,EAAE,MAAQgQ,GAEzBhQ,GAAKzB,IAAM0R,KACbtQ,EAAUkP,EAAM,MAAMkB,EAAStQ,EAAI,CAAC,EAAG,SAAU2Q,EAAU,CACzDzQ,EAAUhB,EAAE,aAAayR,CAAQ,EAAG,SAAU7H,EAAG,CAC/C,IAAI8H,EAAS1R,EAAE,KAAK4J,CAAC,EACnB+H,EAAOD,EAAO,OACXC,EAAOR,GAAMK,EAAKG,IAAS,EAAED,EAAO,OAAS1R,EAAE,KAAKyR,CAAQ,EAAE,QACjEG,GAAYZ,EAAWpH,EAAG6H,CAAQ,CAEhD,CAAW,CACX,CAAS,EAEDL,EAAUtQ,EAAI,EACdqQ,EAAKK,EAEb,CAAK,EAEMtB,CACR,CAEDlF,OAAAA,EAASrH,EAAUsN,CAAU,EACtBD,CACT,CAEA,SAASa,GAAmB7R,EAAG2D,EAAU,CACvC,IAAIqN,EAAY,CAAA,EAEhB,SAASc,EAAKC,EAAOhG,EAAUiG,EAAUC,EAAiBC,EAAiB,CACzE,IAAItS,EACJoB,EAAUa,EAAQkK,EAAUiG,CAAQ,EAAG,SAAUlR,EAAG,CAClDlB,EAAImS,EAAMjR,CAAC,EACPd,EAAE,KAAKJ,CAAC,EAAE,OACZoB,EAAUhB,EAAE,aAAaJ,CAAC,EAAG,SAAUgK,EAAG,CACxC,IAAIuI,EAAQnS,EAAE,KAAK4J,CAAC,EAChBuI,EAAM,QAAUA,EAAM,MAAQF,GAAmBE,EAAM,MAAQD,IACjEN,GAAYZ,EAAWpH,EAAGhK,CAAC,CAEvC,CAAS,CAET,CAAK,CACF,CAED,SAASqR,EAAWmB,EAAOL,EAAO,CAChC,IAAIM,EAAe,GACjBC,EACAvG,EAAW,EAEb/K,OAAAA,EAAU+Q,EAAO,SAAUnS,EAAG2S,EAAgB,CAC5C,GAAIvS,EAAE,KAAKJ,CAAC,EAAE,QAAU,SAAU,CAChC,IAAI4S,EAAexS,EAAE,aAAaJ,CAAC,EAC/B4S,EAAa,SACfF,EAAetS,EAAE,KAAKwS,EAAa,CAAC,CAAC,EAAE,MACvCV,EAAKC,EAAOhG,EAAUwG,EAAgBF,EAAcC,CAAY,EAEhEvG,EAAWwG,EACXF,EAAeC,EAElB,CACDR,EAAKC,EAAOhG,EAAUgG,EAAM,OAAQO,EAAcF,EAAM,MAAM,CACpE,CAAK,EAEML,CACR,CAED/G,OAAAA,EAASrH,EAAUsN,CAAU,EACtBD,CACT,CAEA,SAASO,GAA0BvR,EAAGJ,EAAG,CACvC,GAAII,EAAE,KAAKJ,CAAC,EAAE,MACZ,OAAOqJ,EAAOjJ,EAAE,aAAaJ,CAAC,EAAG,SAAUgK,EAAG,CAC5C,OAAO5J,EAAE,KAAK4J,CAAC,EAAE,KACvB,CAAK,CAEL,CAEA,SAASgI,GAAYZ,EAAWpR,EAAGyB,EAAG,CACpC,GAAIzB,EAAIyB,EAAG,CACT,IAAI2L,EAAMpN,EACVA,EAAIyB,EACJA,EAAI2L,CACL,CAED,IAAIyF,EAAazB,EAAUpR,CAAC,EACvB6S,IACHzB,EAAUpR,CAAC,EAAI6S,EAAa,IAE9BA,EAAWpR,CAAC,EAAI,EAClB,CAEA,SAASqR,GAAY1B,EAAWpR,EAAGyB,EAAG,CACpC,GAAIzB,EAAIyB,EAAG,CACT,IAAI2L,EAAMpN,EACVA,EAAIyB,EACJA,EAAI2L,CACL,CACD,MAAO,CAAC,CAACgE,EAAUpR,CAAC,GAAK,OAAO,UAAU,eAAe,KAAKoR,EAAUpR,CAAC,EAAGyB,CAAC,CAC/E,CAUA,SAASsR,GAAkB3S,EAAG2D,EAAUqN,EAAW4B,EAAY,CAC7D,IAAI/J,EAAO,CAAE,EACXgK,EAAQ,CAAE,EACVC,EAAM,CAAA,EAKR9R,OAAAA,EAAU2C,EAAU,SAAUuM,EAAO,CACnClP,EAAUkP,EAAO,SAAUtQ,EAAGlD,EAAO,CACnCmM,EAAKjJ,CAAC,EAAIA,EACViT,EAAMjT,CAAC,EAAIA,EACXkT,EAAIlT,CAAC,EAAIlD,CACf,CAAK,CACL,CAAG,EAEDsE,EAAU2C,EAAU,SAAUuM,EAAO,CACnC,IAAI6C,EAAU,GACd/R,EAAUkP,EAAO,SAAUtQ,EAAG,CAC5B,IAAIoT,EAAKJ,EAAWhT,CAAC,EACrB,GAAIoT,EAAG,OAAQ,CACbA,EAAK9G,EAAS8G,EAAI,SAAU3R,EAAG,CAC7B,OAAOyR,EAAIzR,CAAC,CACtB,CAAS,EAED,QADI4R,GAAMD,EAAG,OAAS,GAAK,EAClBlS,EAAI,KAAK,MAAMmS,CAAE,EAAGC,EAAK,KAAK,KAAKD,CAAE,EAAGnS,GAAKoS,EAAI,EAAEpS,EAAG,CAC7D,IAAIO,EAAI2R,EAAGlS,CAAC,EACR+R,EAAMjT,CAAC,IAAMA,GAAKmT,EAAUD,EAAIzR,CAAC,GAAK,CAACqR,GAAY1B,EAAWpR,EAAGyB,CAAC,IACpEwR,EAAMxR,CAAC,EAAIzB,EACXiT,EAAMjT,CAAC,EAAIiJ,EAAKjJ,CAAC,EAAIiJ,EAAKxH,CAAC,EAC3B0R,EAAUD,EAAIzR,CAAC,EAElB,CACF,CACP,CAAK,CACL,CAAG,EAEM,CAAE,KAAMwH,EAAM,MAAOgK,CAAK,CACnC,CAEA,SAASM,GAAqBnT,EAAG2D,EAAUkF,EAAMgK,EAAOO,EAAY,CAMlE,IAAIC,EAAK,CAAE,EACTC,EAASC,GAAgBvT,EAAG2D,EAAUkF,EAAMuK,CAAU,EACtDI,EAAaJ,EAAa,aAAe,cAE3C,SAASK,EAAQC,EAAWC,EAAe,CAIzC,QAHIxR,EAAQmR,EAAO,QACfM,EAAOzR,EAAM,MACbC,EAAU,CAAA,EACPwR,GACDxR,EAAQwR,CAAI,EACdF,EAAUE,CAAI,GAEdxR,EAAQwR,CAAI,EAAI,GAChBzR,EAAM,KAAKyR,CAAI,EACfzR,EAAQA,EAAM,OAAOwR,EAAcC,CAAI,CAAC,GAG1CA,EAAOzR,EAAM,KAEhB,CAGD,SAAS0R,EAAMD,EAAM,CACnBP,EAAGO,CAAI,EAAIN,EAAO,QAAQM,CAAI,EAAE,OAAO,SAAU1M,EAAK1G,EAAG,CACvD,OAAO,KAAK,IAAI0G,EAAKmM,EAAG7S,EAAE,CAAC,EAAI8S,EAAO,KAAK9S,CAAC,CAAC,CAC9C,EAAE,CAAC,CACL,CAGD,SAASsT,EAAMF,EAAM,CACnB,IAAI3P,EAAMqP,EAAO,SAASM,CAAI,EAAE,OAAO,SAAU1M,EAAK1G,EAAG,CACvD,OAAO,KAAK,IAAI0G,EAAKmM,EAAG7S,EAAE,CAAC,EAAI8S,EAAO,KAAK9S,CAAC,CAAC,CACnD,EAAO,OAAO,iBAAiB,EAEvBqD,EAAO7D,EAAE,KAAK4T,CAAI,EAClB3P,IAAQ,OAAO,mBAAqBJ,EAAK,aAAe2P,IAC1DH,EAAGO,CAAI,EAAI,KAAK,IAAIP,EAAGO,CAAI,EAAG3P,CAAG,EAEpC,CAED,OAAAwP,EAAQI,EAAOP,EAAO,aAAa,KAAKA,CAAM,CAAC,EAC/CG,EAAQK,EAAOR,EAAO,WAAW,KAAKA,CAAM,CAAC,EAG7CtS,EAAU6R,EAAO,SAAUjT,EAAG,CAC5ByT,EAAGzT,CAAC,EAAIyT,EAAGxK,EAAKjJ,CAAC,CAAC,CACtB,CAAG,EAEMyT,CACT,CAEA,SAASE,GAAgBvT,EAAG2D,EAAUkF,EAAMuK,EAAY,CACtD,IAAIW,EAAa,IAAIvS,EACnB0J,EAAalL,EAAE,MAAO,EACtBgU,EAAQC,GAAI/I,EAAW,QAASA,EAAW,QAASkI,CAAU,EAEhEpS,OAAAA,EAAU2C,EAAU,SAAUuM,EAAO,CACnC,IAAItG,EACJ5I,EAAUkP,EAAO,SAAUtQ,EAAG,CAC5B,IAAIsU,EAAQrL,EAAKjJ,CAAC,EAElB,GADAmU,EAAW,QAAQG,CAAK,EACpBtK,EAAG,CACL,IAAIuK,EAAQtL,EAAKe,CAAC,EAChBwK,EAAUL,EAAW,KAAKI,EAAOD,CAAK,EACxCH,EAAW,QAAQI,EAAOD,EAAO,KAAK,IAAIF,EAAMhU,EAAGJ,EAAGgK,CAAC,EAAGwK,GAAW,CAAC,CAAC,CACxE,CACDxK,EAAIhK,CACV,CAAK,CACL,CAAG,EAEMmU,CACT,CAKA,SAASM,GAA2BrU,EAAGsU,EAAK,CAC1C,OAAOxN,EAAQsD,EAASkK,CAAG,EAAG,SAAUjB,EAAI,CAC1C,IAAIpY,EAAM,OAAO,kBACbgJ,EAAM,OAAO,kBAEjBsQ,OAAAA,GAAQlB,EAAI,SAAUlQ,EAAGvD,EAAG,CAC1B,IAAI4U,EAAYC,GAAMzU,EAAGJ,CAAC,EAAI,EAE9B3E,EAAM,KAAK,IAAIkI,EAAIqR,EAAWvZ,CAAG,EACjCgJ,EAAM,KAAK,IAAId,EAAIqR,EAAWvQ,CAAG,CACvC,CAAK,EAEMhJ,EAAMgJ,CACjB,CAAG,CACH,CASA,SAASyQ,GAAiBJ,EAAKK,EAAS,CACtC,IAAIC,EAAcxK,EAASuK,CAAO,EAChCE,EAAa3Q,EAAM0Q,CAAW,EAC9BE,EAAanQ,EAAMiQ,CAAW,EAEhC5T,EAAU,CAAC,IAAK,GAAG,EAAG,SAAU+T,EAAM,CACpC/T,EAAU,CAAC,IAAK,GAAG,EAAG,SAAUgU,EAAO,CACrC,IAAIC,EAAYF,EAAOC,EACrB3B,EAAKiB,EAAIW,CAAS,EAClB1Q,EACF,GAAI8O,IAAOsB,EAEX,KAAIO,EAAS9K,EAASiJ,CAAE,EACxB9O,EAAQyQ,IAAU,IAAMH,EAAa3Q,EAAMgR,CAAM,EAAIJ,EAAanQ,EAAMuQ,CAAM,EAE1E3Q,IACF+P,EAAIW,CAAS,EAAIE,EAAY9B,EAAI,SAAUlQ,EAAG,CAC5C,OAAOA,EAAIoB,CACrB,CAAS,GAET,CAAK,CACL,CAAG,CACH,CAEA,SAAS6Q,GAAQd,EAAKzB,EAAO,CAC3B,OAAOsC,EAAYb,EAAI,GAAI,SAAUe,EAAQzV,EAAG,CAC9C,GAAIiT,EACF,OAAOyB,EAAIzB,EAAM,YAAW,CAAE,EAAEjT,CAAC,EAEjC,IAAIyT,EAAKnH,EAAS3L,EAAM+T,EAAK1U,CAAC,CAAC,EAC/B,OAAQyT,EAAG,CAAC,EAAIA,EAAG,CAAC,GAAK,CAE/B,CAAG,CACH,CAEA,SAASiC,GAAUtV,EAAG,CACpB,IAAI2D,EAAWiM,EAAsB5P,CAAC,EAClCgR,EAAYuE,EAAQxE,GAAmB/Q,EAAG2D,CAAQ,EAAGkO,GAAmB7R,EAAG2D,CAAQ,CAAC,EAEpF2Q,EAAM,CAAA,EACNkB,EACJxU,EAAU,CAAC,IAAK,GAAG,EAAG,SAAU+T,EAAM,CACpCS,EAAmBT,IAAS,IAAMpR,EAAWyG,EAASzG,CAAQ,EAAE,UAChE3C,EAAU,CAAC,IAAK,GAAG,EAAG,SAAUgU,EAAO,CACjCA,IAAU,MACZQ,EAAmBjV,EAAMiV,EAAkB,SAAUC,EAAO,CAC1D,OAAOrL,EAASqL,CAAK,EAAE,SACjC,CAAS,GAGH,IAAI7C,GAAcmC,IAAS,IAAM/U,EAAE,aAAeA,EAAE,YAAY,KAAKA,CAAC,EAClE6S,EAAQF,GAAkB3S,EAAGwV,EAAkBxE,EAAW4B,CAAU,EACpES,EAAKF,GAAqBnT,EAAGwV,EAAkB3C,EAAM,KAAMA,EAAM,MAAOmC,IAAU,GAAG,EACrFA,IAAU,MACZ3B,EAAK8B,EAAY9B,EAAI,SAAUlQ,EAAG,CAChC,MAAO,CAACA,CAClB,CAAS,GAEHmR,EAAIS,EAAOC,CAAK,EAAI3B,CAC1B,CAAK,CACL,CAAG,EAED,IAAIqC,EAAgBrB,GAA2BrU,EAAGsU,CAAG,EACrD,OAAAI,GAAiBJ,EAAKoB,CAAa,EAC5BN,GAAQd,EAAKtU,EAAE,MAAO,EAAC,KAAK,CACrC,CAEA,SAASiU,GAAI5J,EAASsL,EAASvC,EAAY,CACzC,OAAO,SAAUpT,EAAGJ,EAAGyB,EAAG,CACxB,IAAI6H,EAASlJ,EAAE,KAAKJ,CAAC,EACjBuJ,EAASnJ,EAAE,KAAKqB,CAAC,EACjBuM,EAAM,EACNrJ,EAGJ,GADAqJ,GAAO1E,EAAO,MAAQ,EAClB,OAAO,UAAU,eAAe,KAAKA,EAAQ,UAAU,EACzD,OAAQA,EAAO,SAAS,YAAa,EAAA,CACnC,IAAK,IACH3E,EAAQ,CAAC2E,EAAO,MAAQ,EACxB,MACF,IAAK,IACH3E,EAAQ2E,EAAO,MAAQ,EACvB,KACH,CAWH,GATI3E,IACFqJ,GAAOwF,EAAa7O,EAAQ,CAACA,GAE/BA,EAAQ,EAERqJ,IAAQ1E,EAAO,MAAQyM,EAAUtL,GAAW,EAC5CuD,IAAQzE,EAAO,MAAQwM,EAAUtL,GAAW,EAE5CuD,GAAOzE,EAAO,MAAQ,EAClB,OAAO,UAAU,eAAe,KAAKA,EAAQ,UAAU,EACzD,OAAQA,EAAO,SAAS,YAAa,EAAA,CACnC,IAAK,IACH5E,EAAQ4E,EAAO,MAAQ,EACvB,MACF,IAAK,IACH5E,EAAQ,CAAC4E,EAAO,MAAQ,EACxB,KACH,CAEH,OAAI5E,IACFqJ,GAAOwF,EAAa7O,EAAQ,CAACA,GAE/BA,EAAQ,EAEDqJ,CACX,CACA,CAEA,SAAS6G,GAAMzU,EAAGJ,EAAG,CACnB,OAAOI,EAAE,KAAKJ,CAAC,EAAE,KACnB,CChaA,SAASgW,GAAS5V,EAAG,CACnBA,EAAI6V,GAAwB7V,CAAC,EAE7B8V,GAAU9V,CAAC,EACX+V,GAAST,GAAUtV,CAAC,EAAG,SAAUmD,EAAGvD,EAAG,CACrCI,EAAE,KAAKJ,CAAC,EAAE,EAAIuD,CAClB,CAAG,CACH,CAEA,SAAS2S,GAAU9V,EAAG,CACpB,IAAI2D,EAAWiM,EAAsB5P,CAAC,EAClCgW,EAAUhW,EAAE,MAAK,EAAG,QACpBiW,EAAQ,EACZjV,EAAU2C,EAAU,SAAUuM,EAAO,CACnC,IAAIgG,EAAYvR,EACdpE,EAAM2P,EAAO,SAAUtQ,EAAG,CACxB,OAAOI,EAAE,KAAKJ,CAAC,EAAE,MACzB,CAAO,CACP,EACIoB,EAAUkP,EAAO,SAAUtQ,EAAG,CAC5BI,EAAE,KAAKJ,CAAC,EAAE,EAAIqW,EAAQC,EAAY,CACxC,CAAK,EACDD,GAASC,EAAYF,CACzB,CAAG,CACH,CCfA,SAASG,GAAOnW,EAAGoW,EAAM,CACvB,IAAIC,EAA8CC,GAClDD,EAAK,SAAU,IAAM,CACnB,IAAIE,EAAcF,EAAK,qBAAsB,IAAMG,GAAiBxW,CAAC,CAAC,EACtEqW,EAAK,cAAe,IAAMI,GAAUF,EAAaF,CAAI,CAAC,EACtDA,EAAK,qBAAsB,IAAMK,GAAiB1W,EAAGuW,CAAW,CAAC,CACrE,CAAG,CACH,CAEA,SAASE,GAAUzW,EAAGqW,EAAM,CAC1BA,EAAK,6BAA8B,IAAMM,GAAuB3W,CAAC,CAAC,EAClEqW,EAAK,sBAAuB,IAAMO,GAAgB5W,CAAC,CAAC,EACpDqW,EAAK,cAAe,IAAMQ,GAAY7W,CAAC,CAAC,EACxCqW,EAAK,uBAAwB,IAAMS,GAAiB9W,CAAC,CAAC,EACtDqW,EAAK,WAAY,IAAMvS,GAAK+R,GAAwB7V,CAAC,CAAC,CAAC,EACvDqW,EAAK,6BAA8B,IAAMU,GAAuB/W,CAAC,CAAC,EAClEqW,EAAK,uBAAwB,IAAMW,GAAsBhX,CAAC,CAAC,EAC3DqW,EAAK,2BAA4B,IAAMY,GAAqBjX,CAAC,CAAC,EAC9DqW,EAAK,qBAAsB,IAAMa,GAAoBlX,CAAC,CAAC,EACvDqW,EAAK,uBAAwB,IAAMc,GAAiBnX,CAAC,CAAC,EACtDqW,EAAK,6BAA8B,IAAMe,GAAuBpX,CAAC,CAAC,EAClEqW,EAAK,oBAAqB,IAAMgB,GAAcrX,CAAC,CAAC,EAChDqW,EAAK,wBAAyB,IAAMlG,GAAkBnQ,CAAC,CAAC,EACxDqW,EAAK,wBAAyB,IAAMtR,GAAkB/E,CAAC,CAAC,EACxDqW,EAAK,YAAa,IAAM3Z,GAAMsD,CAAC,CAAC,EAChCqW,EAAK,sBAAuB,IAAMiB,GAAgBtX,CAAC,CAAC,EACpDqW,EAAK,6BAA8B,IAAMkB,GAAwBvX,CAAC,CAAC,EACnEqW,EAAK,eAAgB,IAAMT,GAAS5V,CAAC,CAAC,EACtCqW,EAAK,wBAAyB,IAAMmB,GAAkBxX,CAAC,CAAC,EACxDqW,EAAK,wBAAyB,IAAMoB,GAAkBzX,CAAC,CAAC,EACxDqW,EAAK,qBAAsB,IAAMqB,GAAe1X,CAAC,CAAC,EAClDqW,EAAK,2BAA4B,IAAMsB,GAAqB3X,CAAC,CAAC,EAC9DqW,EAAK,2BAA4B,IAAMuB,GAAsB5X,CAAC,CAAC,EAC/DqW,EAAK,qBAAsB,IAAMwB,GAAe7X,CAAC,CAAC,EAClDqW,EAAK,2BAA4B,IAAMyB,GAAqB9X,CAAC,CAAC,EAC9DqW,EAAK,oBAAqB,IAAM0B,GAA8B/X,CAAC,CAAC,EAChEqW,EAAK,mBAAoB,IAAM2B,GAAahY,CAAC,CAAC,CAChD,CAQA,SAAS0W,GAAiBuB,EAAY1B,EAAa,CACjDvV,EAAUiX,EAAW,MAAO,EAAE,SAAUrY,EAAG,CACzC,IAAIsY,EAAaD,EAAW,KAAKrY,CAAC,EAC9BuY,EAAc5B,EAAY,KAAK3W,CAAC,EAEhCsY,IACFA,EAAW,EAAIC,EAAY,EAC3BD,EAAW,EAAIC,EAAY,EAEvB5B,EAAY,SAAS3W,CAAC,EAAE,SAC1BsY,EAAW,MAAQC,EAAY,MAC/BD,EAAW,OAASC,EAAY,QAGxC,CAAG,EAEDnX,EAAUiX,EAAW,MAAO,EAAE,SAAUzX,EAAG,CACzC,IAAI0X,EAAaD,EAAW,KAAKzX,CAAC,EAC9B2X,EAAc5B,EAAY,KAAK/V,CAAC,EAEpC0X,EAAW,OAASC,EAAY,OAC5B,OAAO,UAAU,eAAe,KAAKA,EAAa,GAAG,IACvDD,EAAW,EAAIC,EAAY,EAC3BD,EAAW,EAAIC,EAAY,EAEjC,CAAG,EAEDF,EAAW,MAAK,EAAG,MAAQ1B,EAAY,MAAO,EAAC,MAC/C0B,EAAW,MAAK,EAAG,OAAS1B,EAAY,MAAO,EAAC,MAClD,CAEA,IAAI6B,GAAgB,CAAC,UAAW,UAAW,UAAW,UAAW,SAAS,EACtEC,GAAgB,CAAE,QAAS,GAAI,QAAS,GAAI,QAAS,GAAI,QAAS,MAClEC,GAAa,CAAC,YAAa,SAAU,UAAW,OAAO,EACvDC,GAAe,CAAC,QAAS,QAAQ,EACjCC,GAAe,CAAE,MAAO,EAAG,OAAQ,CAAC,EACpCC,GAAe,CAAC,SAAU,SAAU,QAAS,SAAU,aAAa,EACpEC,GAAe,CACjB,OAAQ,EACR,OAAQ,EACR,MAAO,EACP,OAAQ,EACR,YAAa,GACb,SAAU,GACZ,EACIC,GAAY,CAAC,UAAU,EAQ3B,SAASnC,GAAiByB,EAAY,CACpC,IAAIjY,EAAI,IAAIwB,EAAM,CAAE,WAAY,GAAM,SAAU,EAAI,CAAE,EAClDoX,EAAQC,EAAaZ,EAAW,MAAO,CAAA,EAE3C,OAAAjY,EAAE,SACAuV,EAAQ,CAAA,EAAI8C,GAAeS,EAAkBF,EAAOR,EAAa,EAAG3K,EAAOmL,EAAON,EAAU,CAAC,CACjG,EAEEtX,EAAUiX,EAAW,MAAO,EAAE,SAAUrY,EAAG,CACzC,IAAIiE,EAAOgV,EAAaZ,EAAW,KAAKrY,CAAC,CAAC,EAC1CI,EAAE,QAAQJ,EAAGmZ,GAAWD,EAAkBjV,EAAM0U,EAAY,EAAGC,EAAY,CAAC,EAC5ExY,EAAE,UAAUJ,EAAGqY,EAAW,OAAOrY,CAAC,CAAC,CACvC,CAAG,EAEDoB,EAAUiX,EAAW,MAAO,EAAE,SAAUzX,EAAG,CACzC,IAAIS,EAAO4X,EAAaZ,EAAW,KAAKzX,CAAC,CAAC,EAC1CR,EAAE,QACAQ,EACA+U,EAAQ,CAAA,EAAImD,GAAcI,EAAkB7X,EAAMwX,EAAY,EAAGhL,EAAOxM,EAAM0X,EAAS,CAAC,CAC9F,CACA,CAAG,EAEM3Y,CACT,CAUA,SAAS2W,GAAuB3W,EAAG,CACjC,IAAI4Y,EAAQ5Y,EAAE,QACd4Y,EAAM,SAAW,EACjB5X,EAAUhB,EAAE,MAAO,EAAE,SAAUQ,EAAG,CAChC,IAAIS,EAAOjB,EAAE,KAAKQ,CAAC,EACnBS,EAAK,QAAU,EACXA,EAAK,SAAS,YAAW,IAAO,MAC9B2X,EAAM,UAAY,MAAQA,EAAM,UAAY,KAC9C3X,EAAK,OAASA,EAAK,YAEnBA,EAAK,QAAUA,EAAK,YAG5B,CAAG,CACH,CAQA,SAAS8V,GAAuB/W,EAAG,CACjCgB,EAAUhB,EAAE,MAAO,EAAE,SAAUQ,EAAG,CAChC,IAAIS,EAAOjB,EAAE,KAAKQ,CAAC,EACnB,GAAIS,EAAK,OAASA,EAAK,OAAQ,CAC7B,IAAIrB,EAAII,EAAE,KAAKQ,EAAE,CAAC,EACda,EAAIrB,EAAE,KAAKQ,EAAE,CAAC,EACdyB,EAAQ,CAAE,MAAOZ,EAAE,KAAOzB,EAAE,MAAQ,EAAIA,EAAE,KAAM,EAAGY,CAAC,EACxD6E,EAAkBrF,EAAG,aAAciC,EAAO,KAAK,CAChD,CACL,CAAG,CACH,CAEA,SAASkV,GAAiBnX,EAAG,CAC3B,IAAI4D,EAAU,EACd5C,EAAUhB,EAAE,MAAO,EAAE,SAAUJ,EAAG,CAChC,IAAIiE,EAAO7D,EAAE,KAAKJ,CAAC,EACfiE,EAAK,YACPA,EAAK,QAAU7D,EAAE,KAAK6D,EAAK,SAAS,EAAE,KACtCA,EAAK,QAAU7D,EAAE,KAAK6D,EAAK,YAAY,EAAE,KAEzCD,EAAUe,EAAMf,EAASC,EAAK,OAAO,EAE3C,CAAG,EACD7D,EAAE,MAAK,EAAG,QAAU4D,CACtB,CAEA,SAASwT,GAAuBpX,EAAG,CACjCgB,EAAUhB,EAAE,MAAO,EAAE,SAAUJ,EAAG,CAChC,IAAIiE,EAAO7D,EAAE,KAAKJ,CAAC,EACfiE,EAAK,QAAU,eACjB7D,EAAE,KAAK6D,EAAK,CAAC,EAAE,UAAYA,EAAK,KAChC7D,EAAE,WAAWJ,CAAC,EAEpB,CAAG,CACH,CAEA,SAASiY,GAAe7X,EAAG,CACzB,IAAIgZ,EAAO,OAAO,kBACdC,EAAO,EACPC,EAAO,OAAO,kBACdC,EAAO,EACPjO,EAAalL,EAAE,QACfoZ,EAAUlO,EAAW,SAAW,EAChCmO,EAAUnO,EAAW,SAAW,EAEpC,SAASoO,EAAY5W,EAAO,CAC1B,IAAIS,EAAIT,EAAM,EACVU,EAAIV,EAAM,EACVrB,EAAIqB,EAAM,MACVa,EAAIb,EAAM,OACdsW,EAAO,KAAK,IAAIA,EAAM7V,EAAI9B,EAAI,CAAC,EAC/B4X,EAAO,KAAK,IAAIA,EAAM9V,EAAI9B,EAAI,CAAC,EAC/B6X,EAAO,KAAK,IAAIA,EAAM9V,EAAIG,EAAI,CAAC,EAC/B4V,EAAO,KAAK,IAAIA,EAAM/V,EAAIG,EAAI,CAAC,CAChC,CAEDvC,EAAUhB,EAAE,MAAO,EAAE,SAAUJ,EAAG,CAChC0Z,EAAYtZ,EAAE,KAAKJ,CAAC,CAAC,CACzB,CAAG,EACDoB,EAAUhB,EAAE,MAAO,EAAE,SAAUQ,EAAG,CAChC,IAAIS,EAAOjB,EAAE,KAAKQ,CAAC,EACf,OAAO,UAAU,eAAe,KAAKS,EAAM,GAAG,GAChDqY,EAAYrY,CAAI,CAEtB,CAAG,EAED+X,GAAQI,EACRF,GAAQG,EAERrY,EAAUhB,EAAE,MAAO,EAAE,SAAUJ,EAAG,CAChC,IAAIiE,EAAO7D,EAAE,KAAKJ,CAAC,EACnBiE,EAAK,GAAKmV,EACVnV,EAAK,GAAKqV,CACd,CAAG,EAEDlY,EAAUhB,EAAE,MAAO,EAAE,SAAUQ,EAAG,CAChC,IAAIS,EAAOjB,EAAE,KAAKQ,CAAC,EACnBQ,EAAUC,EAAK,OAAQ,SAAUsY,EAAG,CAClCA,EAAE,GAAKP,EACPO,EAAE,GAAKL,CACb,CAAK,EACG,OAAO,UAAU,eAAe,KAAKjY,EAAM,GAAG,IAChDA,EAAK,GAAK+X,GAER,OAAO,UAAU,eAAe,KAAK/X,EAAM,GAAG,IAChDA,EAAK,GAAKiY,EAEhB,CAAG,EAEDhO,EAAW,MAAQ+N,EAAOD,EAAOI,EACjClO,EAAW,OAASiO,EAAOD,EAAOG,CACpC,CAEA,SAASvB,GAAqB9X,EAAG,CAC/BgB,EAAUhB,EAAE,MAAO,EAAE,SAAUQ,EAAG,CAChC,IAAIS,EAAOjB,EAAE,KAAKQ,CAAC,EACfgZ,EAAQxZ,EAAE,KAAKQ,EAAE,CAAC,EAClBiZ,EAAQzZ,EAAE,KAAKQ,EAAE,CAAC,EAClBkZ,EAAIC,EACH1Y,EAAK,QAKRyY,EAAKzY,EAAK,OAAO,CAAC,EAClB0Y,EAAK1Y,EAAK,OAAOA,EAAK,OAAO,OAAS,CAAC,IALvCA,EAAK,OAAS,GACdyY,EAAKD,EACLE,EAAKH,GAKPvY,EAAK,OAAO,QAAQ2Y,GAAmBJ,EAAOE,CAAE,CAAC,EACjDzY,EAAK,OAAO,KAAK2Y,GAAmBH,EAAOE,CAAE,CAAC,CAClD,CAAG,CACH,CAEA,SAAShC,GAAqB3X,EAAG,CAC/BgB,EAAUhB,EAAE,MAAO,EAAE,SAAUQ,EAAG,CAChC,IAAIS,EAAOjB,EAAE,KAAKQ,CAAC,EACnB,GAAI,OAAO,UAAU,eAAe,KAAKS,EAAM,GAAG,EAIhD,QAHIA,EAAK,WAAa,KAAOA,EAAK,WAAa,OAC7CA,EAAK,OAASA,EAAK,aAEbA,EAAK,SAAQ,CACnB,IAAK,IACHA,EAAK,GAAKA,EAAK,MAAQ,EAAIA,EAAK,YAChC,MACF,IAAK,IACHA,EAAK,GAAKA,EAAK,MAAQ,EAAIA,EAAK,YAChC,KACH,CAEP,CAAG,CACH,CAEA,SAAS8W,GAA8B/X,EAAG,CACxCgB,EAAUhB,EAAE,MAAO,EAAE,SAAUQ,EAAG,CAChC,IAAIS,EAAOjB,EAAE,KAAKQ,CAAC,EACfS,EAAK,UACPA,EAAK,OAAO,SAElB,CAAG,CACH,CAEA,SAASwW,GAAkBzX,EAAG,CAC5BgB,EAAUhB,EAAE,MAAO,EAAE,SAAUJ,EAAG,CAChC,GAAII,EAAE,SAASJ,CAAC,EAAE,OAAQ,CACxB,IAAIiE,EAAO7D,EAAE,KAAKJ,CAAC,EACf,EAAII,EAAE,KAAK6D,EAAK,SAAS,EACzBgW,EAAI7Z,EAAE,KAAK6D,EAAK,YAAY,EAC5BiW,EAAI9Z,EAAE,KAAKuO,EAAO1K,EAAK,UAAU,CAAC,EAClCkW,EAAI/Z,EAAE,KAAKuO,EAAO1K,EAAK,WAAW,CAAC,EAEvCA,EAAK,MAAQ,KAAK,IAAIkW,EAAE,EAAID,EAAE,CAAC,EAC/BjW,EAAK,OAAS,KAAK,IAAIgW,EAAE,EAAI,EAAE,CAAC,EAChChW,EAAK,EAAIiW,EAAE,EAAIjW,EAAK,MAAQ,EAC5BA,EAAK,EAAI,EAAE,EAAIA,EAAK,OAAS,CAC9B,CACL,CAAG,EAED7C,EAAUhB,EAAE,MAAO,EAAE,SAAUJ,EAAG,CAC5BI,EAAE,KAAKJ,CAAC,EAAE,QAAU,UACtBI,EAAE,WAAWJ,CAAC,CAEpB,CAAG,CACH,CAEA,SAASgX,GAAgB5W,EAAG,CAC1BgB,EAAUhB,EAAE,MAAO,EAAE,SAAUQ,EAAG,CAChC,GAAIA,EAAE,IAAMA,EAAE,EAAG,CACf,IAAIqD,EAAO7D,EAAE,KAAKQ,EAAE,CAAC,EAChBqD,EAAK,YACRA,EAAK,UAAY,IAEnBA,EAAK,UAAU,KAAK,CAAE,EAAGrD,EAAG,MAAOR,EAAE,KAAKQ,CAAC,CAAG,CAAA,EAC9CR,EAAE,WAAWQ,CAAC,CACf,CACL,CAAG,CACH,CAEA,SAAS8W,GAAgBtX,EAAG,CAC1B,IAAIsE,EAASsL,EAAsB5P,CAAC,EACpCgB,EAAUsD,EAAQ,SAAU4L,EAAO,CACjC,IAAI8J,EAAa,EACjBhZ,EAAUkP,EAAO,SAAUtQ,EAAG,EAAG,CAC/B,IAAIiE,EAAO7D,EAAE,KAAKJ,CAAC,EACnBiE,EAAK,MAAQ,EAAImW,EACjBhZ,EAAU6C,EAAK,UAAW,SAAUoW,EAAU,CAC5C5U,EACErF,EACA,WACA,CACE,MAAOia,EAAS,MAAM,MACtB,OAAQA,EAAS,MAAM,OACvB,KAAMpW,EAAK,KACX,MAAO,GAAI,EAAEmW,EACb,EAAGC,EAAS,EACZ,MAAOA,EAAS,KACjB,EACD,KACV,CACA,CAAO,EACD,OAAOpW,EAAK,SAClB,CAAK,CACL,CAAG,CACH,CAEA,SAAS2T,GAAkBxX,EAAG,CAC5BgB,EAAUhB,EAAE,MAAO,EAAE,SAAUJ,EAAG,CAChC,IAAIiE,EAAO7D,EAAE,KAAKJ,CAAC,EACnB,GAAIiE,EAAK,QAAU,WAAY,CAC7B,IAAIqW,EAAWla,EAAE,KAAK6D,EAAK,EAAE,CAAC,EAC1BV,EAAI+W,EAAS,EAAIA,EAAS,MAAQ,EAClC9W,EAAI8W,EAAS,EACb7W,EAAKQ,EAAK,EAAIV,EACdG,EAAK4W,EAAS,OAAS,EAC3Bla,EAAE,QAAQ6D,EAAK,EAAGA,EAAK,KAAK,EAC5B7D,EAAE,WAAWJ,CAAC,EACdiE,EAAK,MAAM,OAAS,CAClB,CAAE,EAAGV,EAAK,EAAIE,EAAM,EAAG,EAAGD,EAAIE,CAAI,EAClC,CAAE,EAAGH,EAAK,EAAIE,EAAM,EAAG,EAAGD,EAAIE,CAAI,EAClC,CAAE,EAAGH,EAAIE,EAAI,EAAGD,CAAG,EACnB,CAAE,EAAGD,EAAK,EAAIE,EAAM,EAAG,EAAGD,EAAIE,CAAI,EAClC,CAAE,EAAGH,EAAK,EAAIE,EAAM,EAAG,EAAGD,EAAIE,CAAI,CAC1C,EACMO,EAAK,MAAM,EAAIA,EAAK,EACpBA,EAAK,MAAM,EAAIA,EAAK,CACrB,CACL,CAAG,CACH,CAEA,SAASiV,EAAkBqB,EAAKzX,EAAO,CACrC,OAAOyS,EAAY1H,EAAO0M,EAAKzX,CAAK,EAAG,MAAM,CAC/C,CAEA,SAASmW,EAAanW,EAAO,CAC3B,IAAI0X,EAAW,CAAA,EACfpZ,OAAAA,EAAU0B,EAAO,SAAU9C,EAAGD,EAAG,CAC/Bya,EAASza,EAAE,YAAa,CAAA,EAAIC,CAChC,CAAG,EACMwa,CACT", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49]}