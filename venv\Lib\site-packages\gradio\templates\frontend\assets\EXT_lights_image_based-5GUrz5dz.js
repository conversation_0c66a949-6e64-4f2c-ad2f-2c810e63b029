import{S as g,Q as u,M as f,aQ as y,ai as x,an as b,ao as A}from"./index-Dpxo-yl_.js";import{_ as T}from"./environmentTextureTools-DK-Qazev.js";import{CubeTexture as S}from"./cubeTexture-CrzX_ljl.js";import{GLTFLoader as w,ArrayItem as h}from"./glTFLoader-9Z3KGax5.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./dumpTools-DkqtMIaE.js";import"./abstractEngine.cubeTexture-7uN6d2SV.js";import"./bone-kZWM5-u7.js";import"./rawTexture-DmvUfjqF.js";import"./assetContainer-BRzQBugc.js";import"./objectModelMapping-BR4RdEzn.js";class m extends S{constructor(t,e,n,a=5,r=0,s=!1,o=!1,l=3,i=null){super("",t),this._texture=t.getEngine().createRawCubeTexture(e,n,a,r,s,o,l,i)}update(t,e,n,a,r=null){this._texture.getEngine().updateRawCubeTexture(this._texture,t,e,n,a,r)}updateRGBDAsync(t,e=null,n=.8,a=0){return T(this._texture,t,e,n,a).then(()=>{})}clone(){return g.Clone(()=>{const t=this.getScene(),e=this._texture,n=new m(t,e._bufferViewArray,e.width,e.format,e.type,e.generateMipMaps,e.invertY,e.samplingMode,e._compression);return e.source===13&&n.updateRGBDAsync(e._bufferViewArrayArray,e._sphericalPolynomial,e._lodGenerationScale,e._lodGenerationOffset),n},this)}}const c="EXT_lights_image_based";class I{constructor(t){this.name=c,this._loader=t,this.enabled=this._loader.isExtensionUsed(c)}dispose(){this._loader=null,delete this._lights}onLoading(){const t=this._loader.gltf.extensions;if(t&&t[this.name]){const e=t[this.name];this._lights=e.lights}}loadSceneAsync(t,e){return w.LoadExtensionAsync(t,e,this.name,(n,a)=>{this._loader._allMaterialsDirtyRequired=!0;const r=new Array;r.push(this._loader.loadSceneAsync(t,e)),this._loader.logOpen(`${n}`);const s=h.Get(`${n}/light`,this._lights,a.light);return r.push(this._loadLightAsync(`/extensions/${this.name}/lights/${a.light}`,s).then(o=>{this._loader.babylonScene.environmentTexture=o})),this._loader.logClose(),Promise.all(r).then(()=>{})})}_loadLightAsync(t,e){if(!e._loaded){const n=new Array;this._loader.logOpen(`${t}`);const a=new Array(e.specularImages.length);for(let r=0;r<e.specularImages.length;r++){const s=e.specularImages[r];a[r]=new Array(s.length);for(let o=0;o<s.length;o++){const l=`${t}/specularImages/${r}/${o}`;this._loader.logOpen(`${l}`);const i=s[o],p=h.Get(l,this._loader.gltf.images,i);n.push(this._loader.loadImageAsync(`/images/${i}`,p).then(_=>{a[r][o]=_})),this._loader.logClose()}}this._loader.logClose(),e._loaded=Promise.all(n).then(()=>{const r=new m(this._loader.babylonScene,null,e.specularImageSize);if(r.name=e.name||"environment",e._babylonTexture=r,e.intensity!=null&&(r.level=e.intensity),e.rotation){let i=u.FromArray(e.rotation);this._loader.babylonScene.useRightHandedSystem||(i=u.Inverse(i)),f.FromQuaternionToRef(i,r.getReflectionTextureMatrix())}if(!e.irradianceCoefficients)throw new Error(`${t}: Irradiance coefficients are missing`);const s=y.FromArray(e.irradianceCoefficients);s.scaleInPlace(e.intensity),s.convertIrradianceToLambertianRadiance();const o=x.FromHarmonics(s),l=(a.length-1)/Math.log2(e.specularImageSize);return r.updateRGBDAsync(a,o,l)})}return e._loaded.then(()=>e._babylonTexture)}}b(c);A(c,!0,d=>new I(d));export{I as EXT_lights_image_based};
//# sourceMappingURL=EXT_lights_image_based-5GUrz5dz.js.map
