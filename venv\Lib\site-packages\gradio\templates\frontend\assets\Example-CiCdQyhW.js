import{I as j}from"./Image-CnqB5dbD.js";import"./index-B7J2Z2jS.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";/* empty css                                                   */import{V as A}from"./Video-DtShVFLe.js";import"./file-url-DoxvUUVV.js";import"./svelte/svelte.js";import"./prism-python-MMh3z1bK.js";import"./hls-CnVhpNcu.js";const{SvelteComponent:D,add_iframe_resize_listener:F,add_render_callback:G,append:k,attr:w,binding_callbacks:H,check_outros:B,create_component:C,destroy_component:E,destroy_each:J,detach:b,element:$,empty:K,ensure_array_like:q,flush:y,group_outros:M,init:L,insert:v,mount_component:N,noop:h,safe_not_equal:O,set_data:P,space:Q,src_url_equal:I,text:W,toggle_class:p,transition_in:d,transition_out:g}=window.__gradio__svelte__internal,{onMount:R}=window.__gradio__svelte__internal;function S(o,e,n){const t=o.slice();return t[7]=e[n],t}function T(o){let e=o[7].orig_name+"",n;return{c(){n=W(e)},m(t,r){v(t,n,r)},p(t,r){r&1&&e!==(e=t[7].orig_name+"")&&P(n,e)},i:h,o:h,d(t){t&&b(n)}}}function U(o){let e,n;return{c(){e=$("audio"),I(e.src,n=o[7].url)||w(e,"src",n),e.controls=!0},m(t,r){v(t,e,r)},p(t,r){r&1&&!I(e.src,n=t[7].url)&&w(e,"src",n)},i:h,o:h,d(t){t&&b(e)}}}function X(o){let e,n;return e=new A({props:{src:o[7].url,alt:"",loop:!0,is_stream:!1}}),{c(){C(e.$$.fragment)},m(t,r){N(e,t,r),n=!0},p(t,r){const c={};r&1&&(c.src=t[7].url),e.$set(c)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){g(e.$$.fragment,t),n=!1},d(t){E(e,t)}}}function Y(o){let e,n;return e=new j({props:{src:o[7].url,alt:""}}),{c(){C(e.$$.fragment)},m(t,r){N(e,t,r),n=!0},p(t,r){const c={};r&1&&(c.src=t[7].url),e.$set(c)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){g(e.$$.fragment,t),n=!1},d(t){E(e,t)}}}function V(o){let e,n,t,r,c,f,a;const m=[Y,X,U,T],i=[];function _(l,s){return s&1&&(e=null),s&1&&(n=null),s&1&&(t=null),e==null&&(e=!!(l[7].mime_type&&l[7].mime_type.includes("image"))),e?0:(n==null&&(n=!!(l[7].mime_type&&l[7].mime_type.includes("video"))),n?1:(t==null&&(t=!!(l[7].mime_type&&l[7].mime_type.includes("audio"))),t?2:3))}return r=_(o,-1),c=i[r]=m[r](o),{c(){c.c(),f=K()},m(l,s){i[r].m(l,s),v(l,f,s),a=!0},p(l,s){let u=r;r=_(l,s),r===u?i[r].p(l,s):(M(),g(i[u],1,1,()=>{i[u]=null}),B(),c=i[r],c?c.p(l,s):(c=i[r]=m[r](l),c.c()),d(c,1),c.m(f.parentNode,f))},i(l){a||(d(c),a=!0)},o(l){g(c),a=!1},d(l){l&&b(f),i[r].d(l)}}}function Z(o){let e,n,t=(o[0].text?o[0].text:"")+"",r,c,f,a,m=q(o[0].files),i=[];for(let l=0;l<m.length;l+=1)i[l]=V(S(o,m,l));const _=l=>g(i[l],1,1,()=>{i[l]=null});return{c(){e=$("div"),n=$("p"),r=W(t),c=Q();for(let l=0;l<i.length;l+=1)i[l].c();w(e,"class","container svelte-1cl8bqt"),G(()=>o[5].call(e)),p(e,"table",o[1]==="table"),p(e,"gallery",o[1]==="gallery"),p(e,"selected",o[2]),p(e,"border",o[0])},m(l,s){v(l,e,s),k(e,n),k(n,r),k(e,c);for(let u=0;u<i.length;u+=1)i[u]&&i[u].m(e,null);f=F(e,o[5].bind(e)),o[6](e),a=!0},p(l,[s]){if((!a||s&1)&&t!==(t=(l[0].text?l[0].text:"")+"")&&P(r,t),s&1){m=q(l[0].files);let u;for(u=0;u<m.length;u+=1){const z=S(l,m,u);i[u]?(i[u].p(z,s),d(i[u],1)):(i[u]=V(z),i[u].c(),d(i[u],1),i[u].m(e,null))}for(M(),u=m.length;u<i.length;u+=1)_(u);B()}(!a||s&2)&&p(e,"table",l[1]==="table"),(!a||s&2)&&p(e,"gallery",l[1]==="gallery"),(!a||s&4)&&p(e,"selected",l[2]),(!a||s&1)&&p(e,"border",l[0])},i(l){if(!a){for(let s=0;s<m.length;s+=1)d(i[s]);a=!0}},o(l){i=i.filter(Boolean);for(let s=0;s<i.length;s+=1)g(i[s]);a=!1},d(l){l&&b(e),J(i,l),f(),o[6](null)}}}function x(o,e){o.style.setProperty("--local-text-width",`${e&&e<150?e:200}px`),o.style.whiteSpace="unset"}function ee(o,e,n){let{value:t={text:"",files:[]}}=e,{type:r}=e,{selected:c=!1}=e,f,a;R(()=>{x(a,f)});function m(){f=this.clientWidth,n(3,f)}function i(_){H[_?"unshift":"push"](()=>{a=_,n(4,a)})}return o.$$set=_=>{"value"in _&&n(0,t=_.value),"type"in _&&n(1,r=_.type),"selected"in _&&n(2,c=_.selected)},[t,r,c,f,a,m,i]}class fe extends D{constructor(e){super(),L(this,e,ee,Z,O,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),y()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),y()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),y()}}export{fe as default};
//# sourceMappingURL=Example-CiCdQyhW.js.map
