{"version": 3, "file": "KHR_node_hoverability.CkqDT3n3.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_node_hoverability.js"], "sourcesContent": ["import { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nimport { addNewInteractivityFlowGraphMapping } from \"./KHR_interactivity/declarationMapper.js\";\nimport { AddObjectAccessorToKey } from \"./objectModelMapping.js\";\nconst NAME = \"KHR_node_hoverability\";\n// interactivity\nconst meshPointerOverPrefix = \"targetMeshPointerOver_\";\naddNewInteractivityFlowGraphMapping(\"event/onHoverIn\", NAME, {\n    // using GetVariable as the nodeIndex is a configuration and not a value (i.e. it's not mutable)\n    blocks: [\"FlowGraphPointerOverEventBlock\" /* FlowGraphBlockNames.PointerOverEvent */, \"FlowGraphGetVariableBlock\" /* FlowGraphBlockNames.GetVariable */, \"FlowGraphIndexOfBlock\" /* FlowGraphBlockNames.IndexOf */, \"KHR_interactivity/FlowGraphGLTFDataProvider\"],\n    configuration: {\n        stopPropagation: { name: \"stopPropagation\" },\n        nodeIndex: {\n            name: \"variable\",\n            toBlock: \"FlowGraphGetVariableBlock\" /* FlowGraphBlockNames.GetVariable */,\n            dataTransformer(data) {\n                return [meshPointerOverPrefix + data[0]];\n            },\n        },\n    },\n    outputs: {\n        values: {\n            hoverNodeIndex: { name: \"index\", toBlock: \"FlowGraphIndexOfBlock\" /* FlowGraphBlockNames.IndexOf */ },\n            controllerIndex: { name: \"pointerId\" },\n        },\n        flows: {\n            out: { name: \"done\" },\n        },\n    },\n    interBlockConnectors: [\n        {\n            input: \"targetMesh\",\n            output: \"value\",\n            inputBlockIndex: 0,\n            outputBlockIndex: 1,\n            isVariable: true,\n        },\n        {\n            input: \"array\",\n            output: \"nodes\",\n            inputBlockIndex: 2,\n            outputBlockIndex: 3,\n            isVariable: true,\n        },\n        {\n            input: \"object\",\n            output: \"meshUnderPointer\",\n            inputBlockIndex: 2,\n            outputBlockIndex: 0,\n            isVariable: true,\n        },\n    ],\n    extraProcessor(gltfBlock, _declaration, _mapping, _arrays, serializedObjects, context, globalGLTF) {\n        // add the glTF to the configuration of the last serialized object\n        const serializedObject = serializedObjects[serializedObjects.length - 1];\n        serializedObject.config = serializedObject.config || {};\n        serializedObject.config.glTF = globalGLTF;\n        // find the listener nodeIndex value\n        const nodeIndex = gltfBlock.configuration?.[\"nodeIndex\"]?.value[0];\n        if (nodeIndex === undefined || typeof nodeIndex !== \"number\") {\n            throw new Error(\"nodeIndex not found in configuration\");\n        }\n        const variableName = meshPointerOverPrefix + nodeIndex;\n        // find the nodeIndex value\n        serializedObjects[1].config.variable = variableName;\n        context._userVariables[variableName] = {\n            className: \"Mesh\",\n            id: globalGLTF?.nodes?.[nodeIndex]._babylonTransformNode?.id,\n            uniqueId: globalGLTF?.nodes?.[nodeIndex]._babylonTransformNode?.uniqueId,\n        };\n        return serializedObjects;\n    },\n});\nconst meshPointerOutPrefix = \"targetMeshPointerOut_\";\naddNewInteractivityFlowGraphMapping(\"event/onHoverOut\", NAME, {\n    // using GetVariable as the nodeIndex is a configuration and not a value (i.e. it's not mutable)\n    blocks: [\"FlowGraphPointerOutEventBlock\" /* FlowGraphBlockNames.PointerOutEvent */, \"FlowGraphGetVariableBlock\" /* FlowGraphBlockNames.GetVariable */, \"FlowGraphIndexOfBlock\" /* FlowGraphBlockNames.IndexOf */, \"KHR_interactivity/FlowGraphGLTFDataProvider\"],\n    configuration: {\n        stopPropagation: { name: \"stopPropagation\" },\n        nodeIndex: {\n            name: \"variable\",\n            toBlock: \"FlowGraphGetVariableBlock\" /* FlowGraphBlockNames.GetVariable */,\n            dataTransformer(data) {\n                return [meshPointerOutPrefix + data[0]];\n            },\n        },\n    },\n    outputs: {\n        values: {\n            hoverNodeIndex: { name: \"index\", toBlock: \"FlowGraphIndexOfBlock\" /* FlowGraphBlockNames.IndexOf */ },\n            controllerIndex: { name: \"pointerId\" },\n        },\n        flows: {\n            out: { name: \"done\" },\n        },\n    },\n    interBlockConnectors: [\n        {\n            input: \"targetMesh\",\n            output: \"value\",\n            inputBlockIndex: 0,\n            outputBlockIndex: 1,\n            isVariable: true,\n        },\n        {\n            input: \"array\",\n            output: \"nodes\",\n            inputBlockIndex: 2,\n            outputBlockIndex: 3,\n            isVariable: true,\n        },\n        {\n            input: \"object\",\n            output: \"meshOutOfPointer\",\n            inputBlockIndex: 2,\n            outputBlockIndex: 0,\n            isVariable: true,\n        },\n    ],\n    extraProcessor(gltfBlock, _declaration, _mapping, _arrays, serializedObjects, context, globalGLTF) {\n        // add the glTF to the configuration of the last serialized object\n        const serializedObject = serializedObjects[serializedObjects.length - 1];\n        serializedObject.config = serializedObject.config || {};\n        serializedObject.config.glTF = globalGLTF;\n        const nodeIndex = gltfBlock.configuration?.[\"nodeIndex\"]?.value[0];\n        if (nodeIndex === undefined || typeof nodeIndex !== \"number\") {\n            throw new Error(\"nodeIndex not found in configuration\");\n        }\n        const variableName = meshPointerOutPrefix + nodeIndex;\n        // find the nodeIndex value\n        serializedObjects[1].config.variable = variableName;\n        context._userVariables[variableName] = {\n            className: \"Mesh\",\n            id: globalGLTF?.nodes?.[nodeIndex]._babylonTransformNode?.id,\n            uniqueId: globalGLTF?.nodes?.[nodeIndex]._babylonTransformNode?.uniqueId,\n        };\n        return serializedObjects;\n    },\n});\nAddObjectAccessorToKey(\"/nodes/{}/extensions/KHR_node_hoverability/hoverable\", {\n    get: (node) => {\n        const tn = node._babylonTransformNode;\n        if (tn && tn.pointerOverDisableMeshTesting !== undefined) {\n            return tn.pointerOverDisableMeshTesting;\n        }\n        return true;\n    },\n    set: (value, node) => {\n        node._primitiveBabylonMeshes?.forEach((mesh) => {\n            mesh.pointerOverDisableMeshTesting = !value;\n        });\n    },\n    getTarget: (node) => node._babylonTransformNode,\n    getPropertyName: [() => \"pointerOverDisableMeshTesting\"],\n    type: \"boolean\",\n});\n/**\n * Loader extension for KHR_node_hoverability\n * @see https://github.com/KhronosGroup/glTF/pull/2426\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_node_hoverability {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        this._loader = loader;\n        this.enabled = loader.isExtensionUsed(NAME);\n    }\n    async onReady() {\n        this._loader.gltf.nodes?.forEach((node) => {\n            // default is true, so only apply if false\n            if (node.extensions?.KHR_node_hoverability && node.extensions?.KHR_node_hoverability.hoverable === false) {\n                node._babylonTransformNode?.getChildMeshes().forEach((mesh) => {\n                    mesh.pointerOverDisableMeshTesting = true;\n                });\n            }\n        });\n    }\n    dispose() {\n        this._loader = null;\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_node_hoverability(loader));\n//# sourceMappingURL=KHR_node_hoverability.js.map"], "names": ["NAME", "meshPointerOverPrefix", "addNewInteractivityFlowGraphMapping", "data", "gltfBlock", "_declaration", "_mapping", "_arrays", "serializedObjects", "context", "globalGLTF", "serializedObject", "nodeIndex", "_b", "_a", "variableName", "_d", "_c", "_f", "_e", "meshPointerOutPrefix", "AddObjectAccessorToKey", "node", "tn", "value", "mesh", "KHR_node_hoverability", "loader", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "0JAGA,MAAMA,EAAO,wBAEPC,EAAwB,yBAC9BC,EAAoC,kBAAmBF,EAAM,CAEzD,OAAQ,CAAC,iCAA6E,4BAAmE,wBAA2D,6CAA6C,EACjQ,cAAe,CACX,gBAAiB,CAAE,KAAM,iBAAmB,EAC5C,UAAW,CACP,KAAM,WACN,QAAS,4BACT,gBAAgBG,EAAM,CAClB,MAAO,CAACF,EAAwBE,EAAK,CAAC,CAAC,CAC1C,CACJ,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,eAAgB,CAAE,KAAM,QAAS,QAAS,uBAA2D,EACrG,gBAAiB,CAAE,KAAM,WAAa,CACzC,EACD,MAAO,CACH,IAAK,CAAE,KAAM,MAAQ,CACxB,CACJ,EACD,qBAAsB,CAClB,CACI,MAAO,aACP,OAAQ,QACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,QACP,OAAQ,QACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,SACP,OAAQ,mBACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,CACJ,EACD,eAAeC,EAAWC,EAAcC,EAAUC,EAASC,EAAmBC,EAASC,EAAY,iBAE/F,MAAMC,EAAmBH,EAAkBA,EAAkB,OAAS,CAAC,EACvEG,EAAiB,OAASA,EAAiB,QAAU,CAAA,EACrDA,EAAiB,OAAO,KAAOD,EAE/B,MAAME,GAAYC,GAAAC,EAAAV,EAAU,gBAAV,YAAAU,EAA0B,YAA1B,YAAAD,EAAwC,MAAM,GAChE,GAAID,IAAc,QAAa,OAAOA,GAAc,SAChD,MAAM,IAAI,MAAM,sCAAsC,EAE1D,MAAMG,EAAed,EAAwBW,EAE7C,OAAAJ,EAAkB,CAAC,EAAE,OAAO,SAAWO,EACvCN,EAAQ,eAAeM,CAAY,EAAI,CACnC,UAAW,OACX,IAAIC,GAAAC,EAAAP,GAAA,YAAAA,EAAY,QAAZ,YAAAO,EAAoBL,GAAW,wBAA/B,YAAAI,EAAsD,GAC1D,UAAUE,GAAAC,EAAAT,GAAA,YAAAA,EAAY,QAAZ,YAAAS,EAAoBP,GAAW,wBAA/B,YAAAM,EAAsD,QAC5E,EACeV,CACV,CACL,CAAC,EACD,MAAMY,EAAuB,wBAC7BlB,EAAoC,mBAAoBF,EAAM,CAE1D,OAAQ,CAAC,gCAA2E,4BAAmE,wBAA2D,6CAA6C,EAC/P,cAAe,CACX,gBAAiB,CAAE,KAAM,iBAAmB,EAC5C,UAAW,CACP,KAAM,WACN,QAAS,4BACT,gBAAgBG,EAAM,CAClB,MAAO,CAACiB,EAAuBjB,EAAK,CAAC,CAAC,CACzC,CACJ,CACJ,EACD,QAAS,CACL,OAAQ,CACJ,eAAgB,CAAE,KAAM,QAAS,QAAS,uBAA2D,EACrG,gBAAiB,CAAE,KAAM,WAAa,CACzC,EACD,MAAO,CACH,IAAK,CAAE,KAAM,MAAQ,CACxB,CACJ,EACD,qBAAsB,CAClB,CACI,MAAO,aACP,OAAQ,QACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,QACP,OAAQ,QACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,EACD,CACI,MAAO,SACP,OAAQ,mBACR,gBAAiB,EACjB,iBAAkB,EAClB,WAAY,EACf,CACJ,EACD,eAAeC,EAAWC,EAAcC,EAAUC,EAASC,EAAmBC,EAASC,EAAY,iBAE/F,MAAMC,EAAmBH,EAAkBA,EAAkB,OAAS,CAAC,EACvEG,EAAiB,OAASA,EAAiB,QAAU,CAAA,EACrDA,EAAiB,OAAO,KAAOD,EAC/B,MAAME,GAAYC,GAAAC,EAAAV,EAAU,gBAAV,YAAAU,EAA0B,YAA1B,YAAAD,EAAwC,MAAM,GAChE,GAAID,IAAc,QAAa,OAAOA,GAAc,SAChD,MAAM,IAAI,MAAM,sCAAsC,EAE1D,MAAMG,EAAeK,EAAuBR,EAE5C,OAAAJ,EAAkB,CAAC,EAAE,OAAO,SAAWO,EACvCN,EAAQ,eAAeM,CAAY,EAAI,CACnC,UAAW,OACX,IAAIC,GAAAC,EAAAP,GAAA,YAAAA,EAAY,QAAZ,YAAAO,EAAoBL,GAAW,wBAA/B,YAAAI,EAAsD,GAC1D,UAAUE,GAAAC,EAAAT,GAAA,YAAAA,EAAY,QAAZ,YAAAS,EAAoBP,GAAW,wBAA/B,YAAAM,EAAsD,QAC5E,EACeV,CACV,CACL,CAAC,EACDa,EAAuB,uDAAwD,CAC3E,IAAMC,GAAS,CACX,MAAMC,EAAKD,EAAK,sBAChB,OAAIC,GAAMA,EAAG,gCAAkC,OACpCA,EAAG,8BAEP,EACV,EACD,IAAK,CAACC,EAAOF,IAAS,QAClBR,EAAAQ,EAAK,0BAAL,MAAAR,EAA8B,QAASW,GAAS,CAC5CA,EAAK,8BAAgC,CAACD,CAClD,EACK,EACD,UAAYF,GAASA,EAAK,sBAC1B,gBAAiB,CAAC,IAAM,+BAA+B,EACvD,KAAM,SACV,CAAC,EAMM,MAAMI,CAAsB,CAI/B,YAAYC,EAAQ,CAIhB,KAAK,KAAO3B,EACZ,KAAK,QAAU2B,EACf,KAAK,QAAUA,EAAO,gBAAgB3B,CAAI,CAC7C,CACD,MAAM,SAAU,QACZc,EAAA,KAAK,QAAQ,KAAK,QAAlB,MAAAA,EAAyB,QAASQ,GAAS,YAEnCR,EAAAQ,EAAK,aAAL,MAAAR,EAAiB,yBAAyBD,EAAAS,EAAK,aAAL,YAAAT,EAAiB,sBAAsB,aAAc,MAC/FI,EAAAK,EAAK,wBAAL,MAAAL,EAA4B,iBAAiB,QAASQ,GAAS,CAC3DA,EAAK,8BAAgC,EACzD,GAEA,EACK,CACD,SAAU,CACN,KAAK,QAAU,IAClB,CACL,CACAG,EAAwB5B,CAAI,EAC5B6B,EAAsB7B,EAAM,GAAO2B,GAAW,IAAID,EAAsBC,CAAM,CAAC", "x_google_ignoreList": [0]}