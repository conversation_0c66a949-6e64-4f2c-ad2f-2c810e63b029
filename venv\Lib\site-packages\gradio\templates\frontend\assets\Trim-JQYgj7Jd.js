const{SvelteComponent:x,append:w,attr:n,detach:v,init:f,insert:m,noop:_,safe_not_equal:y,svg_element:p}=window.__gradio__svelte__internal;function $(d){let e,r,o;return{c(){e=p("svg"),r=p("rect"),o=p("rect"),n(r,"x","6"),n(r,"y","4"),n(r,"width","4"),n(r,"height","16"),n(o,"x","14"),n(o,"y","4"),n(o,"width","4"),n(o,"height","16"),n(e,"xmlns","http://www.w3.org/2000/svg"),n(e,"width","100%"),n(e,"height","100%"),n(e,"viewBox","0 0 24 24"),n(e,"fill","currentColor"),n(e,"stroke","currentColor"),n(e,"stroke-width","1.5"),n(e,"stroke-linecap","round"),n(e,"stroke-linejoin","round")},m(i,l){m(i,e,l),w(e,r),w(e,o)},p:_,i:_,o:_,d(i){i&&v(e)}}}class P extends x{constructor(e){super(),f(this,e,null,$,y,{})}}const{SvelteComponent:k,append:a,attr:t,detach:C,init:q,insert:S,noop:u,safe_not_equal:j,svg_element:c}=window.__gradio__svelte__internal;function B(d){let e,r,o,i,l,s;return{c(){e=c("svg"),r=c("circle"),o=c("circle"),i=c("line"),l=c("line"),s=c("line"),t(r,"cx","6"),t(r,"cy","6"),t(r,"r","3"),t(o,"cx","6"),t(o,"cy","18"),t(o,"r","3"),t(i,"x1","20"),t(i,"y1","4"),t(i,"x2","8.12"),t(i,"y2","15.88"),t(l,"x1","14.47"),t(l,"y1","14.48"),t(l,"x2","20"),t(l,"y2","20"),t(s,"x1","8.12"),t(s,"y1","8.12"),t(s,"x2","12"),t(s,"y2","12"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"width","100%"),t(e,"height","100%"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","2"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-scissors")},m(h,g){S(h,e,g),a(e,r),a(e,o),a(e,i),a(e,l),a(e,s)},p:u,i:u,o:u,d(h){h&&C(e)}}}class T extends k{constructor(e){super(),q(this,e,null,B,j,{})}}export{P,T};
//# sourceMappingURL=Trim-JQYgj7Jd.js.map
