const __vite__fileDeps=["./PlotlyPlot.D5k6VvrP.js","./2.B2AoQPnG.js","./preload-helper.D6kgxu3v.js","./stores.z8sZTwoA.js","./client.Cd1aarwx.js","../assets/2.BTQDGmJF.css","./BokehPlot.BywEwpiC.js","../assets/BokehPlot.Cd-I2ErV.css","./AltairPlot.C0eMzJOG.js","./color.6b7WW5Kp.js","./vega-tooltip.module.sYf3DH7j.js","./time.Ddv6ux-b.js","./step.Ce-xBr2D.js","./linear.BVb3otZY.js","./init.Dmth1JHB.js","./dsv.DB8NKgIY.js","./range.OtVwhkKS.js","./ordinal.BJp8kCrd.js","./arc.CL2LCPr_.js","./dispatch.kxCwF96_.js","../assets/AltairPlot.CSe9xcFj.css","./MatplotlibPlot.BqYrERW-.js","../assets/MatplotlibPlot.AF_QcUtc.css"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as E}from"./preload-helper.D6kgxu3v.js";import{SvelteComponent as G,init as J,safe_not_equal as R,svg_element as b,claim_svg_element as p,children as g,detach as m,attr as s,insert_hydration as L,append_hydration as w,noop as P,empty as y,group_outros as D,transition_out as h,check_outros as V,transition_in as d,createEventDispatcher as $,bubble as H,create_component as C,claim_component as I,mount_component as T,destroy_component as A,construct_svelte_component as q,binding_callbacks as Y,bind as Z,add_flush_callback as ee}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import"./2.B2AoQPnG.js";import{E as te}from"./Empty.DwQ6nkN6.js";function le(a){let e,l,o,r,c,_,f;return{c(){e=b("svg"),l=b("circle"),o=b("circle"),r=b("circle"),c=b("circle"),_=b("circle"),f=b("path"),this.h()},l(t){e=p(t,"svg",{xmlns:!0,"xmlns:xlink":!0,"aria-hidden":!0,role:!0,class:!0,width:!0,height:!0,preserveAspectRatio:!0,viewBox:!0});var n=g(e);l=p(n,"circle",{cx:!0,cy:!0,r:!0,fill:!0}),g(l).forEach(m),o=p(n,"circle",{cx:!0,cy:!0,r:!0,fill:!0}),g(o).forEach(m),r=p(n,"circle",{cx:!0,cy:!0,r:!0,fill:!0}),g(r).forEach(m),c=p(n,"circle",{cx:!0,cy:!0,r:!0,fill:!0}),g(c).forEach(m),_=p(n,"circle",{cx:!0,cy:!0,r:!0,fill:!0}),g(_).forEach(m),f=p(n,"path",{fill:!0,d:!0}),g(f).forEach(m),n.forEach(m),this.h()},h(){s(l,"cx","20"),s(l,"cy","4"),s(l,"r","2"),s(l,"fill","currentColor"),s(o,"cx","8"),s(o,"cy","16"),s(o,"r","2"),s(o,"fill","currentColor"),s(r,"cx","28"),s(r,"cy","12"),s(r,"r","2"),s(r,"fill","currentColor"),s(c,"cx","11"),s(c,"cy","7"),s(c,"r","2"),s(c,"fill","currentColor"),s(_,"cx","16"),s(_,"cy","24"),s(_,"r","2"),s(_,"fill","currentColor"),s(f,"fill","currentColor"),s(f,"d","M30 3.413L28.586 2L4 26.585V2H2v26a2 2 0 0 0 2 2h26v-2H5.413Z"),s(e,"xmlns","http://www.w3.org/2000/svg"),s(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),s(e,"aria-hidden","true"),s(e,"role","img"),s(e,"class","iconify iconify--carbon"),s(e,"width","100%"),s(e,"height","100%"),s(e,"preserveAspectRatio","xMidYMid meet"),s(e,"viewBox","0 0 32 32")},m(t,n){L(t,e,n),w(e,l),w(e,o),w(e,r),w(e,c),w(e,_),w(e,f)},p:P,i:P,o:P,d(t){t&&m(e)}}}let oe=class extends G{constructor(e){super(),J(this,e,null,le,R,{})}};function re(a){let e,l;return e=new te({props:{unpadded_box:!0,size:"large",$$slots:{default:[ie]},$$scope:{ctx:a}}}),{c(){C(e.$$.fragment)},l(o){I(e.$$.fragment,o)},m(o,r){T(e,o,r),l=!0},p(o,r){const c={};r&4194304&&(c.$$scope={dirty:r,ctx:o}),e.$set(c)},i(o){l||(d(e.$$.fragment,o),l=!0)},o(o){h(e.$$.fragment,o),l=!1},d(o){A(e,o)}}}function ne(a){let e=a[12],l,o,r=F(a);return{c(){r.c(),l=y()},l(c){r.l(c),l=y()},m(c,_){r.m(c,_),L(c,l,_),o=!0},p(c,_){_&4096&&R(e,e=c[12])?(D(),h(r,1,1,P),V(),r=F(c),r.c(),d(r,1),r.m(l.parentNode,l)):r.p(c,_)},i(c){o||(d(r),o=!0)},o(c){h(r),o=!1},d(c){c&&m(l),r.d(c)}}}function ie(a){let e,l;return e=new oe({}),{c(){C(e.$$.fragment)},l(o){I(e.$$.fragment,o)},m(o,r){T(e,o,r),l=!0},i(o){l||(d(e.$$.fragment,o),l=!0)},o(o){h(e.$$.fragment,o),l=!1},d(o){A(e,o)}}}function F(a){let e,l,o,r;function c(t){a[16](t)}var _=a[10];function f(t,n){let u={value:t[0],colors:t[1],theme_mode:t[3],show_label:t[2],caption:t[4],bokeh_version:t[5],show_actions_button:t[6],gradio:t[7],_selectable:t[9],x_lim:t[8]};return t[11]!==void 0&&(u.loaded_plotly_css=t[11]),{props:u}}return _&&(e=q(_,f(a)),Y.push(()=>Z(e,"loaded_plotly_css",c)),e.$on("load",a[17]),e.$on("select",a[18])),{c(){e&&C(e.$$.fragment),o=y()},l(t){e&&I(e.$$.fragment,t),o=y()},m(t,n){e&&T(e,t,n),L(t,o,n),r=!0},p(t,n){if(n&1024&&_!==(_=t[10])){if(e){D();const u=e;h(u.$$.fragment,1,0,()=>{A(u,1)}),V()}_?(e=q(_,f(t)),Y.push(()=>Z(e,"loaded_plotly_css",c)),e.$on("load",t[17]),e.$on("select",t[18]),C(e.$$.fragment),d(e.$$.fragment,1),T(e,o.parentNode,o)):e=null}else if(_){const u={};n&1&&(u.value=t[0]),n&2&&(u.colors=t[1]),n&8&&(u.theme_mode=t[3]),n&4&&(u.show_label=t[2]),n&16&&(u.caption=t[4]),n&32&&(u.bokeh_version=t[5]),n&64&&(u.show_actions_button=t[6]),n&128&&(u.gradio=t[7]),n&512&&(u._selectable=t[9]),n&256&&(u.x_lim=t[8]),!l&&n&2048&&(l=!0,u.loaded_plotly_css=t[11],ee(()=>l=!1)),e.$set(u)}},i(t){r||(e&&d(e.$$.fragment,t),r=!0)},o(t){e&&h(e.$$.fragment,t),r=!1},d(t){t&&m(o),e&&A(e,t)}}}function ce(a){let e,l,o,r;const c=[ne,re],_=[];function f(t,n){return t[0]&&t[10]?0:1}return e=f(a),l=_[e]=c[e](a),{c(){l.c(),o=y()},l(t){l.l(t),o=y()},m(t,n){_[e].m(t,n),L(t,o,n),r=!0},p(t,[n]){let u=e;e=f(t),e===u?_[e].p(t,n):(D(),h(_[u],1,1,()=>{_[u]=null}),V(),l=_[e],l?l.p(t,n):(l=_[e]=c[e](t),l.c()),d(l,1),l.m(o.parentNode,o))},i(t){r||(d(l),r=!0)},o(t){h(l),r=!1},d(t){t&&m(o),_[e].d(t)}}}function _e(a,e,l){let{value:o}=e,r,{colors:c=[]}=e,{show_label:_}=e,{theme_mode:f}=e,{caption:t}=e,{bokeh_version:n}=e,{show_actions_button:u}=e,{gradio:N}=e,{x_lim:S=null}=e,{_selectable:j}=e,k=null,M=o==null?void 0:o.type,O=!1;const K=$(),z={plotly:()=>E(()=>import("./PlotlyPlot.D5k6VvrP.js"),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url),bokeh:()=>E(()=>import("./BokehPlot.BywEwpiC.js"),__vite__mapDeps([6,7]),import.meta.url),altair:()=>E(()=>import("./AltairPlot.C0eMzJOG.js"),__vite__mapDeps([8,1,2,3,4,5,9,10,11,12,13,14,15,16,17,18,19,20]),import.meta.url),matplotlib:()=>E(()=>import("./MatplotlibPlot.BqYrERW-.js"),__vite__mapDeps([21,22]),import.meta.url)};let v={};const Q=typeof window<"u";let B=0;function U(i){O=i,l(11,O)}function W(i){H.call(this,a,i)}function X(i){H.call(this,a,i)}return a.$$set=i=>{"value"in i&&l(0,o=i.value),"colors"in i&&l(1,c=i.colors),"show_label"in i&&l(2,_=i.show_label),"theme_mode"in i&&l(3,f=i.theme_mode),"caption"in i&&l(4,t=i.caption),"bokeh_version"in i&&l(5,n=i.bokeh_version),"show_actions_button"in i&&l(6,u=i.show_actions_button),"gradio"in i&&l(7,N=i.gradio),"x_lim"in i&&l(8,S=i.x_lim),"_selectable"in i&&l(9,j=i._selectable)},a.$$.update=()=>{if(a.$$.dirty&62465&&o!==r){l(12,B+=1);let i=o==null?void 0:o.type;i!==M&&l(10,k=null),i&&i in z&&Q&&(v[i]?l(10,k=v[i]):z[i]().then(x=>{l(10,k=x.default),l(15,v[i]=k,v)})),l(13,r=o),l(14,M=i),K("change")}},[o,c,_,f,t,n,u,N,S,j,k,O,B,r,M,v,U,W,X]}class se extends G{constructor(e){super(),J(this,e,_e,ce,R,{value:0,colors:1,show_label:2,theme_mode:3,caption:4,bokeh_version:5,show_actions_button:6,gradio:7,x_lim:8,_selectable:9})}}const be=Object.freeze(Object.defineProperty({__proto__:null,default:se},Symbol.toStringTag,{value:"Module"}));export{oe as P,se as a,be as b};
//# sourceMappingURL=Plot.BOh0qlQz.js.map
