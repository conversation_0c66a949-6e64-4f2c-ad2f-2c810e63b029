#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API chain_matmul {
  using schema = at::Tensor (at::TensorList);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::chain_matmul";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "chain_matmul(Tensor[] matrices) -> Tensor";
  static at::Tensor call(at::TensorList matrices);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList matrices);
};

struct TORCH_API chain_matmul_out {
  using schema = at::Tensor & (at::TensorList, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::chain_matmul";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "chain_matmul.out(Tensor[] matrices, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(at::TensorList matrices, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList matrices, at::Tensor & out);
};

}} // namespace at::_ops
