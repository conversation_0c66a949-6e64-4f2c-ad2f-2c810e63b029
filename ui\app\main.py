"""
Reverie Agents 主应用程序
PyQt6桌面应用界面
"""

import sys
import os
import asyncio
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon, QPalette, QColor

from ui.app.components.chat_widget import ChatWidget
from ui.app.components.sidebar import Sidebar
from ui.app.components.settings_dialog import SettingsDialog
from ui.app.components.persona_selector import PersonaSelector
from ui.app.components.status_bar import StatusBar
from ui.app.components.agents_hub import AgentsHub
from ui.app.components.splash_screen import SplashScreen
from ui.app.components.loading_widget import LoadingWidget, TypingIndicator
from ui.app.components.notification import show_success, show_error, show_info
from ui.app.styles.theme import DarkTheme, LightTheme

from core.utils.logger import get_logger
from core.utils.config import config_manager
from personas.persona_manager import persona_manager
from memory.context_engine import context_engine

logger = get_logger(__name__)

class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.current_theme = None
        self.settings_dialog = None
        self.response_start_time = None
        self.tokens_generated_this_response = 0
        
        self.init_ui()
        self.setup_connections()
        self.load_settings()
        
        logger.info("Reverie Agents 桌面应用启动成功")

        # 显示欢迎通知
        QTimer.singleShot(1000, lambda: show_success(
            "🌟 欢迎使用 Reverie Agents",
            "AI陪伴对话系统已成功启动！"
        ))
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("Reverie Agents - AI陪伴对话系统")
        self.setMinimumSize(1000, 700)
        
        # 设置窗口图标
        icon_path = project_root / "assets" / "icons" / "app_icon.png"
        if icon_path.exists():
            self.setWindowIcon(QIcon(str(icon_path)))
        
        # 创建中央部件
        central_widget = QWidget()
        central_widget.setObjectName("main_central_widget")
        central_widget.setStyleSheet("""
            QWidget#main_central_widget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0a0e1a, stop:0.3 #1a1f2e, stop:0.7 #2d3548, stop:1 #1a1f2e);
                border-radius: 0;
            }
        """)
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)
        
        # 创建侧边栏
        self.sidebar = Sidebar()
        self.sidebar.setFixedWidth(280)
        
        # 创建右侧内容区域
        content_widget = QWidget()
        content_widget.setObjectName("content_area")
        content_widget.setStyleSheet("""
            QWidget#content_area {
                background: rgba(26, 31, 46, 0.8);
                border-radius: 16px;
                border: 1px solid rgba(102, 126, 234, 0.2);
            }
        """)
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(8, 8, 8, 12)  # 底部增加更多边距
        content_layout.setSpacing(8)
        
        # 人设选择器
        self.persona_selector = PersonaSelector()

        # 聊天组件
        self.chat_widget = ChatWidget()

        # Agents Hub
        self.agents_hub = AgentsHub()

        # 状态栏
        self.status_bar = StatusBar()
        
        # 添加到布局 - 默认显示聊天界面
        content_layout.addWidget(self.persona_selector)
        content_layout.addWidget(self.chat_widget, 1)  # 占据剩余空间
        content_layout.addWidget(self.agents_hub, 1)  # Agents Hub页面
        content_layout.addWidget(self.status_bar)

        # 默认隐藏Agents Hub
        self.agents_hub.hide()
        
        # 添加到主布局
        main_layout.addWidget(self.sidebar)
        main_layout.addWidget(content_widget, 1)
        
        # 设置初始焦点
        self.chat_widget.set_focus_to_input()

    def show_agents_hub(self):
        """显示Agents Hub页面"""
        self.persona_selector.hide()
        self.chat_widget.hide()
        self.agents_hub.show()

    def show_chat_page(self):
        """显示聊天页面"""
        self.agents_hub.hide()
        self.persona_selector.show()
        self.chat_widget.show()
        self.chat_widget.set_focus_to_input()

    def select_agent_from_hub(self, persona_id: str):
        """从Hub选择Agent"""
        try:
            # 切换到聊天页面
            self.show_chat_page()

            # 更新人设选择器
            self.persona_selector.set_current_persona(persona_id)

            # 更改人设
            self.change_persona(persona_id)

            logger.info(f"从Hub选择了Agent: {persona_id}")

        except Exception as e:
            logger.error(f"从Hub选择Agent时出错: {e}")
            show_error(self, "选择Agent失败", str(e))
    
    def setup_connections(self):
        """设置信号连接"""
        # 侧边栏信号
        self.sidebar.new_chat_requested.connect(self.start_new_chat)
        self.sidebar.chat_selected.connect(self.load_chat)
        self.sidebar.settings_requested.connect(self.show_settings)
        
        # 人设选择器信号
        self.persona_selector.persona_changed.connect(self.change_persona)
        self.persona_selector.agents_hub_requested.connect(self.show_agents_hub)

        # Agents Hub信号
        self.agents_hub.agent_selected.connect(self.select_agent_from_hub)
        self.agents_hub.back_requested.connect(self.show_chat_page)
        
        # 聊天组件信号
        self.chat_widget.message_sent.connect(self.handle_message)
        self.chat_widget.typing_started.connect(self.on_typing_started)
        self.chat_widget.typing_stopped.connect(self.on_typing_stopped)
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(5000)  # 每5秒更新一次
    
    def load_settings(self):
        """加载设置"""
        # 窗口大小和位置
        width = config_manager.get('ui.window_width', 1200)
        height = config_manager.get('ui.window_height', 800)
        self.resize(width, height)
        
        # 主题
        theme_name = config_manager.get('ui.theme', 'dark')
        self.apply_theme(theme_name)
        
        # 字体
        font_family = config_manager.get('ui.font_family', 'Microsoft YaHei UI')
        font_size = config_manager.get('ui.font_size', 12)
        font = QFont(font_family, font_size)
        self.setFont(font)
        
        # 加载当前人设
        current_persona = persona_manager.get_current_persona()
        if current_persona:
            self.persona_selector.set_current_persona(current_persona.id)
            self.status_bar.set_persona(current_persona.name)
    
    def apply_theme(self, theme_name: str):
        """应用主题"""
        if theme_name == 'dark':
            self.current_theme = DarkTheme()
        else:
            self.current_theme = LightTheme()
        
        self.setStyleSheet(self.current_theme.get_main_stylesheet())
        
        # 应用到子组件
        self.sidebar.apply_theme(self.current_theme)
        self.chat_widget.apply_theme(self.current_theme)
        self.persona_selector.apply_theme(self.current_theme)
        self.status_bar.apply_theme(self.current_theme)
    
    def start_new_chat(self):
        """开始新对话"""
        current_persona = persona_manager.get_current_persona()
        if current_persona:
            session_id = context_engine.start_session(current_persona.id)
            self.chat_widget.clear_chat()
            self.sidebar.add_new_chat(session_id, current_persona.name)
            logger.info(f"开始新对话: {session_id}")
    
    def load_chat(self, session_id: str):
        """加载对话"""
        if context_engine.load_session(session_id):
            # 加载对话历史到聊天组件
            context = context_engine.get_context_for_generation()
            self.chat_widget.load_conversation(context)
            logger.info(f"加载对话: {session_id}")
    
    def change_persona(self, persona_id: str):
        """切换人设"""
        if persona_manager.switch_persona(persona_id):
            persona = persona_manager.get_current_persona()
            self.status_bar.set_persona(persona.name)
            
            # 如果有当前对话，开始新对话
            if context_engine.current_session:
                self.start_new_chat()
            
            logger.info(f"切换人设: {persona.name}")
    
    def handle_message(self, message: str):
        """处理用户消息"""
        current_persona = persona_manager.get_current_persona()
        if not current_persona:
            self.chat_widget.add_error_message("请先选择一个人设")
            return
        
        # 添加用户消息到记忆
        context_engine.add_memory(message, "user", current_persona.id)
        
        # 显示打字指示器
        self.chat_widget.show_typing_indicator()

        # 开始生成回复
        self.chat_widget.start_ai_response()

        # 重置速度计算器
        self.response_start_time = datetime.now()
        self.tokens_generated_this_response = 0

        # 在后台线程中生成回复
        self.response_thread = ResponseThread(message, current_persona)
        self.response_thread.response_chunk.connect(self.on_response_chunk)
        self.response_thread.response_complete.connect(self.on_response_complete)
        self.response_thread.search_started.connect(self.on_search_started)
        self.response_thread.search_complete.connect(self.on_search_complete)
        self.response_thread.start()

    def on_response_chunk(self, chunk: str):
        """处理AI回复的流式数据块"""
        # 更新聊天窗口
        self.chat_widget.append_ai_response(chunk)

        # 计算并更新token速度
        self.tokens_generated_this_response += len(chunk)
        if self.response_start_time:
            elapsed = (datetime.now() - self.response_start_time).total_seconds()
            if elapsed > 0.1:  # 避免除以零或太小的值
                speed = self.tokens_generated_this_response / elapsed
                self.status_bar.set_token_speed(speed)
    
    def on_response_complete(self, full_response: str):
        """AI回复完成"""
        # 隐藏打字指示器
        self.chat_widget.hide_typing_indicator()

        current_persona = persona_manager.get_current_persona()
        if current_persona:
            # 添加AI回复到记忆
            context_engine.add_memory(full_response, "assistant", current_persona.id)
            
            # 检查是否需要生成对话标题（第一次对话后）
            if context_engine.current_session and len(context_engine.current_session.messages) == 2:
                self._generate_conversation_title()
        
        self.chat_widget.finish_ai_response()
        self.status_bar.set_status("就绪")
        
        # 重置速度显示并强制更新状态栏
        self.status_bar.set_token_speed(0.0)
        self.response_start_time = None
        self.update_status()
    
    def _generate_conversation_title(self):
        """生成对话标题"""
        try:
            if not context_engine.current_session:
                return
            
            session_id = context_engine.current_session.session_id
            
            # 在后台线程中生成标题
            def generate_title():
                try:
                    from memory.history_manager import history_manager
                    new_title = history_manager.generate_conversation_title(session_id)
                    
                    # 更新侧边栏显示
                    QTimer.singleShot(0, lambda: self.sidebar.update_chat_title(session_id, new_title))
                    
                    logger.info(f"对话标题已生成: {new_title}")
                except Exception as e:
                    logger.error(f"生成对话标题失败: {e}")
            
            # 使用QTimer在主线程中启动后台任务
            QTimer.singleShot(1000, generate_title)  # 延迟1秒生成标题
            
        except Exception as e:
            logger.error(f"启动标题生成失败: {e}")
    
    def on_search_started(self, query: str):
        """搜索开始"""
        self.status_bar.set_status(f"正在搜索: {query}")
    
    def on_search_complete(self, results_count: int):
        """搜索完成"""
        self.status_bar.set_status(f"搜索完成，找到 {results_count} 个结果")
    
    def on_typing_started(self):
        """用户开始输入"""
        self.status_bar.set_status("正在输入...")
    
    def on_typing_stopped(self):
        """用户停止输入"""
        self.status_bar.set_status("就绪")
    
    def show_settings(self):
        """显示设置对话框"""
        if not self.settings_dialog:
            self.settings_dialog = SettingsDialog(self)
        
        if self.settings_dialog.exec():
            # 设置已更改，重新加载
            self.load_settings()
    
    def update_status(self):
        """更新状态信息"""
        # 更新模型状态
        from core.llm.model_manager import model_manager
        current_model = model_manager.get_current_model_info()
        if current_model:
            self.status_bar.set_model(current_model['name'])
        
        # 更新内存使用情况
        if context_engine.current_session:
            token_count = context_engine.current_session.total_tokens
            self.status_bar.set_tokens(token_count)
    
    def closeEvent(self, event):
        """关闭事件"""
        # 保存窗口大小
        config_manager.set('ui.window_width', self.width())
        config_manager.set('ui.window_height', self.height())
        config_manager.save_config()
        
        logger.info("应用程序关闭")
        event.accept()

class ResponseThread(QThread):
    """AI回复生成线程"""
    
    response_chunk = pyqtSignal(str)
    response_complete = pyqtSignal(str)
    search_started = pyqtSignal(str)
    search_complete = pyqtSignal(int)
    
    def __init__(self, message: str, persona):
        super().__init__()
        self.message = message
        self.persona = persona
    
    def run(self):
        """运行AI回复生成"""
        try:
            from core.ai_engine import ai_engine
            
            # 生成回复
            full_response = ""
            for chunk in ai_engine.generate_response(self.message, self.persona):
                if chunk.startswith("SEARCH:"):
                    # 搜索信号
                    query = chunk[7:]
                    self.search_started.emit(query)
                elif chunk.startswith("SEARCH_COMPLETE:"):
                    # 搜索完成信号
                    count = int(chunk[15:])
                    self.search_complete.emit(count)
                else:
                    # 正常回复内容
                    full_response += chunk
                    self.response_chunk.emit(chunk)
            
            self.response_complete.emit(full_response)
            
        except Exception as e:
            logger.error(f"AI回复生成失败: {e}")
            self.response_chunk.emit(f"抱歉，生成回复时出现错误: {str(e)}")
            self.response_complete.emit("")

def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用信息
    app.setApplicationName("Reverie Agents")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Reverie AI")

    # 创建并显示启动画面
    splash = SplashScreen()

    # 创建主窗口（但不显示）
    window = MainWindow()

    # 启动画面完成后显示主窗口
    def show_main_window():
        window.show()
        logger.info("🎉 Reverie Agents 启动完成！")

    splash.finished.connect(show_main_window)
    splash.start_loading()

    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
