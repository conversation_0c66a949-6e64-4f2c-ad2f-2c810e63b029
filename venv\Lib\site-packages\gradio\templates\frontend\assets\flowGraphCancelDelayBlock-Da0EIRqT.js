import{R as t}from"./index-Dpxo-yl_.js";import{b as r}from"./KHR_interactivity-DTxiAnOo.js";import{b as l}from"./declarationMapper-BZjsjg7g.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./objectModelMapping-BR4RdEzn.js";class s extends r{constructor(e){super(e),this.delayIndex=this.registerDataInput("delayIndex",l)}_execute(e,n){const i=this.delayIndex.getValue(e);if(i<=0||isNaN(i)||!isFinite(i))return this._reportError(e,"Invalid delay index");const a=e._getExecutionVariable(this,"pendingDelays",[])[i];a&&a.dispose(),this.out._activateSignal(e)}getClassName(){return"FlowGraphCancelDelayBlock"}}t("FlowGraphCancelDelayBlock",s);export{s as FlowGraphCancelDelayBlock};
//# sourceMappingURL=flowGraphCancelDelayBlock-Da0EIRqT.js.map
