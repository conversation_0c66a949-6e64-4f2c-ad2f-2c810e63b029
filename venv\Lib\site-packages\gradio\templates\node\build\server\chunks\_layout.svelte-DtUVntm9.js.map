{"version": 3, "file": "_layout.svelte-DtUVntm9.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/entries/pages/_layout.svelte.js"], "sourcesContent": ["import{create_ssr_component as r}from\"svelte/internal\";import*as t from\"svelte\";const s=typeof window<\"u\";if(s){const o={SvelteComponent:t.SvelteComponent};for(const e in t)e!==\"SvelteComponent\"&&(e===\"SvelteComponentDev\"?o[e]=o.SvelteComponent:o[e]=t[e]);window.__gradio__svelte__internal=o,window.__gradio__svelte__internal.globals={},window.globals=window}const a={code:\"body{background:var(--body-background-fill);color:var(--body-text-color)}@media(prefers-color-scheme: dark){body:not(.theme-loaded){background:var(--neutral-950)}}\",map:'{\"version\":3,\"file\":\"+layout.svelte\",\"sources\":[\"+layout.svelte\"],\"sourcesContent\":[\"<script>\\\\n\\\\timport \\\\\"@gradio/theme/reset.css\\\\\";\\\\n\\\\timport \\\\\"@gradio/theme/global.css\\\\\";\\\\n\\\\timport \\\\\"@gradio/theme/pollen.css\\\\\";\\\\n\\\\timport \\\\\"@gradio/theme/typography.css\\\\\";\\\\n\\\\timport \\\\\"./svelte_init\\\\\";\\\\n<\\/script>\\\\n\\\\n<slot></slot>\\\\n\\\\n<style>\\\\n\\\\t:global(body) {\\\\n\\\\t\\\\tbackground: var(--body-background-fill);\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t}\\\\n\\\\n\\\\t@media (prefers-color-scheme: dark) {\\\\n\\\\t\\\\t:global(body:not(.theme-loaded)) {\\\\n\\\\t\\\\t\\\\tbackground: var(--neutral-950);\\\\n\\\\t\\\\t}\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAWS,IAAM,CACb,UAAU,CAAE,IAAI,sBAAsB,CAAC,CACvC,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CAEA,MAAO,uBAAuB,IAAI,CAAE,CAC3B,uBAAyB,CAChC,UAAU,CAAE,IAAI,aAAa,CAC9B,CACD\"}'},d=r((o,e,l,n)=>(o.css.add(a),`${n.default?n.default({}):\"\"}`));export{d as default};\n//# sourceMappingURL=_layout.svelte.js.map\n"], "names": ["t.<PERSON>omponent", "r"], "mappings": ";;;;AAAgF,MAAM,CAAC,CAAC,OAAO,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAACA,kBAAiB,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC,CAAC,MAAM,CAAC,0BAA0B,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,OAAM,CAAM,MAAC,CAAC,CAAC,CAAC,IAAI,CAAC,qKAAqK,CAAC,GAAG,CAAC,wyBAAwyB,CAAC,CAAC,CAAC,CAACC,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;;;"}