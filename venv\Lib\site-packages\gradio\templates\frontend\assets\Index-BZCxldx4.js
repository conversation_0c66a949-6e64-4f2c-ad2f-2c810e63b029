import{B as E}from"./Block-CJdXVpa7.js";import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";/* empty css                                                        */import"./index-B7J2Z2jS.js";import{S as z}from"./index-B1FJGuzG.js";import"./StreamingBar.svelte_svelte_type_style_lang-DyfAPhwM.js";import F from"./Index-C7inCcrM.js";import"./prism-python-MMh3z1bK.js";import"./svelte/svelte.js";import"./IconButton-C_HS7fTi.js";import"./Clear-By3xiIwg.js";const{SvelteComponent:G,append:h,attr:q,create_slot:H,detach:B,element:v,flush:j,get_all_dirty_from_scope:J,get_slot_changes:K,init:L,insert:I,listen:M,safe_not_equal:N,set_data:O,set_style:w,space:A,text:P,toggle_class:D,transition_in:Q,transition_out:R,update_slot_base:T}=window.__gradio__svelte__internal,{createEventDispatcher:U}=window.__gradio__svelte__internal;function V(n){let e,l,t,s,o,c,f,m,_,i;const r=n[4].default,d=H(r,n,n[3],null);return{c(){e=v("button"),l=v("span"),t=P(n[1]),s=A(),o=v("span"),o.textContent="▼",c=A(),f=v("div"),d&&d.c(),q(l,"class","svelte-1w6vloh"),q(o,"class","icon svelte-1w6vloh"),w(o,"transform",n[0]?"rotate(0)":"rotate(90deg)"),q(e,"class","label-wrap svelte-1w6vloh"),D(e,"open",n[0]),w(f,"display",n[0]?"block":"none")},m(a,g){I(a,e,g),h(e,l),h(l,t),h(e,s),h(e,o),I(a,c,g),I(a,f,g),d&&d.m(f,null),m=!0,_||(i=M(e,"click",n[5]),_=!0)},p(a,[g]){(!m||g&2)&&O(t,a[1]),g&1&&w(o,"transform",a[0]?"rotate(0)":"rotate(90deg)"),(!m||g&1)&&D(e,"open",a[0]),d&&d.p&&(!m||g&8)&&T(d,r,a,a[3],m?K(r,a[3],g,null):J(a[3]),null),g&1&&w(f,"display",a[0]?"block":"none")},i(a){m||(Q(d,a),m=!0)},o(a){R(d,a),m=!1},d(a){a&&(B(e),B(c),B(f)),d&&d.d(a),_=!1,i()}}}function W(n,e,l){let{$$slots:t={},$$scope:s}=e;const o=U();let{open:c=!0}=e,{label:f=""}=e;const m=()=>{l(0,c=!c),o(c?"expand":"collapse")};return n.$$set=_=>{"open"in _&&l(0,c=_.open),"label"in _&&l(1,f=_.label),"$$scope"in _&&l(3,s=_.$$scope)},[c,f,o,s,t,m]}class X extends G{constructor(e){super(),L(this,e,W,V,N,{open:0,label:1})}get open(){return this.$$.ctx[0]}set open(e){this.$$set({open:e}),j()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),j()}}const{SvelteComponent:Y,add_flush_callback:Z,assign:y,bind:x,binding_callbacks:ee,create_component:k,create_slot:te,destroy_component:S,detach:se,flush:p,get_all_dirty_from_scope:le,get_slot_changes:ne,get_spread_object:oe,get_spread_update:ie,init:ae,insert:re,mount_component:C,safe_not_equal:_e,space:ue,transition_in:$,transition_out:b,update_slot_base:ce}=window.__gradio__svelte__internal;function fe(n){let e;const l=n[7].default,t=te(l,n,n[11],null);return{c(){t&&t.c()},m(s,o){t&&t.m(s,o),e=!0},p(s,o){t&&t.p&&(!e||o&2048)&&ce(t,l,s,s[11],e?ne(l,s[11],o,null):le(s[11]),null)},i(s){e||($(t,s),e=!0)},o(s){b(t,s),e=!1},d(s){t&&t.d(s)}}}function me(n){let e,l;return e=new F({props:{$$slots:{default:[fe]},$$scope:{ctx:n}}}),{c(){k(e.$$.fragment)},m(t,s){C(e,t,s),l=!0},p(t,s){const o={};s&2048&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){l||($(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){S(e,t)}}}function de(n){let e,l,t,s,o;const c=[{autoscroll:n[6].autoscroll},{i18n:n[6].i18n},n[5]];let f={};for(let i=0;i<c.length;i+=1)f=y(f,c[i]);e=new z({props:f});function m(i){n[8](i)}let _={label:n[1],$$slots:{default:[me]},$$scope:{ctx:n}};return n[0]!==void 0&&(_.open=n[0]),t=new X({props:_}),ee.push(()=>x(t,"open",m)),t.$on("expand",n[9]),t.$on("collapse",n[10]),{c(){k(e.$$.fragment),l=ue(),k(t.$$.fragment)},m(i,r){C(e,i,r),re(i,l,r),C(t,i,r),o=!0},p(i,r){const d=r&96?ie(c,[r&64&&{autoscroll:i[6].autoscroll},r&64&&{i18n:i[6].i18n},r&32&&oe(i[5])]):{};e.$set(d);const a={};r&2&&(a.label=i[1]),r&2048&&(a.$$scope={dirty:r,ctx:i}),!s&&r&1&&(s=!0,a.open=i[0],Z(()=>s=!1)),t.$set(a)},i(i){o||($(e.$$.fragment,i),$(t.$$.fragment,i),o=!0)},o(i){b(e.$$.fragment,i),b(t.$$.fragment,i),o=!1},d(i){i&&se(l),S(e,i),S(t,i)}}}function ge(n){let e,l;return e=new E({props:{elem_id:n[2],elem_classes:n[3],visible:n[4],$$slots:{default:[de]},$$scope:{ctx:n}}}),{c(){k(e.$$.fragment)},m(t,s){C(e,t,s),l=!0},p(t,[s]){const o={};s&4&&(o.elem_id=t[2]),s&8&&(o.elem_classes=t[3]),s&16&&(o.visible=t[4]),s&2147&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){l||($(e.$$.fragment,t),l=!0)},o(t){b(e.$$.fragment,t),l=!1},d(t){S(e,t)}}}function pe(n,e,l){let{$$slots:t={},$$scope:s}=e,{label:o}=e,{elem_id:c}=e,{elem_classes:f}=e,{visible:m=!0}=e,{open:_=!0}=e,{loading_status:i}=e,{gradio:r}=e;function d(u){_=u,l(0,_)}const a=()=>r.dispatch("expand"),g=()=>r.dispatch("collapse");return n.$$set=u=>{"label"in u&&l(1,o=u.label),"elem_id"in u&&l(2,c=u.elem_id),"elem_classes"in u&&l(3,f=u.elem_classes),"visible"in u&&l(4,m=u.visible),"open"in u&&l(0,_=u.open),"loading_status"in u&&l(5,i=u.loading_status),"gradio"in u&&l(6,r=u.gradio),"$$scope"in u&&l(11,s=u.$$scope)},[_,o,c,f,m,i,r,t,d,a,g,s]}class je extends Y{constructor(e){super(),ae(this,e,pe,ge,_e,{label:1,elem_id:2,elem_classes:3,visible:4,open:0,loading_status:5,gradio:6})}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),p()}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),p()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),p()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),p()}get open(){return this.$$.ctx[0]}set open(e){this.$$set({open:e}),p()}get loading_status(){return this.$$.ctx[5]}set loading_status(e){this.$$set({loading_status:e}),p()}get gradio(){return this.$$.ctx[6]}set gradio(e){this.$$set({gradio:e}),p()}}export{je as default};
//# sourceMappingURL=Index-BZCxldx4.js.map
