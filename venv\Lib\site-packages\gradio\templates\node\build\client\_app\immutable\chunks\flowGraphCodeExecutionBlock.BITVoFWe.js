import{F as i}from"./KHR_interactivity.DEAVS2UW.js";import{R as e}from"./declarationMapper.UBCwU7BT.js";class l extends i{constructor(t){super(t),this.config=t,this.executionFunction=this.registerDataInput("function",e),this.value=this.registerDataInput("value",e),this.result=this.registerDataOutput("result",e)}_updateOutputs(t){const s=this.executionFunction.getValue(t),u=this.value.getValue(t);s&&this.result.setValue(s(u,t),t)}getClassName(){return"FlowGraphCodeExecutionBlock"}}export{l as FlowGraphCodeExecutionBlock};
//# sourceMappingURL=flowGraphCodeExecutionBlock.BITVoFWe.js.map
