import{F as u}from"./KHR_interactivity.DEAVS2UW.js";import{q as a,R as o}from"./declarationMapper.UBCwU7BT.js";import{R as r}from"./index.BoI39RQH.js";class c extends u{constructor(t){super(t),this.functionName=this.registerDataInput("functionName",a),this.object=this.registerDataInput("object",o),this.context=this.registerDataInput("context",o,null),this.output=this.registerDataOutput("output",o)}_updateOutputs(t){const s=this.functionName.getValue(t),n=this.object.getValue(t),i=this.context.getValue(t);if(n&&s){const e=n[s];e&&typeof e=="function"&&this.output.setValue(e.bind(i),t)}}getClassName(){return"FlowGraphFunctionReference"}}r("FlowGraphFunctionReference",c);export{c as FlowGraphFunctionReferenceBlock};
//# sourceMappingURL=flowGraphFunctionReferenceBlock.DYL4rOb8.js.map
