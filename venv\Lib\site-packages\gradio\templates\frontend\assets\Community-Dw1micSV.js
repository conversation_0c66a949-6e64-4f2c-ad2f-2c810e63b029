const{SvelteComponent:s,append:c,attr:e,detach:p,init:_,insert:d,noop:o,safe_not_equal:g,svg_element:i}=window.__gradio__svelte__internal;function h(l){let t,n;return{c(){t=i("svg"),n=i("path"),e(n,"d","M23,20a5,5,0,0,0-3.89,1.89L11.8,17.32a4.46,4.46,0,0,0,0-2.64l7.31-4.57A5,5,0,1,0,18,7a4.79,4.79,0,0,0,.2,1.32l-7.31,4.57a5,5,0,1,0,0,6.22l7.31,4.57A4.79,4.79,0,0,0,18,25a5,5,0,1,0,5-5ZM23,4a3,3,0,1,1-3,3A3,3,0,0,1,23,4ZM7,19a3,3,0,1,1,3-3A3,3,0,0,1,7,19Zm16,9a3,3,0,1,1,3-3A3,3,0,0,1,23,28Z"),e(n,"fill","currentColor"),e(t,"id","icon"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"viewBox","0 0 32 32"),e(t,"width","100%"),e(t,"height","100%")},m(a,r){d(a,t,r),c(t,n)},p:o,i:o,o,d(a){a&&p(t)}}}class m extends s{constructor(t){super(),_(this,t,null,h,g,{})}}export{m as C};
//# sourceMappingURL=Community-Dw1micSV.js.map
