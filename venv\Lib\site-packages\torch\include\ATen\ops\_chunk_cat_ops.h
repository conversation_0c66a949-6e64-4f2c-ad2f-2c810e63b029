#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _chunk_cat {
  using schema = at::Tensor (at::TensorList, int64_t, int64_t);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_chunk_cat";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "_chunk_cat(Tensor[] tensors, int dim, int num_chunks) -> Tensor";
  static at::Tensor call(at::TensorList tensors, int64_t dim, int64_t num_chunks);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList tensors, int64_t dim, int64_t num_chunks);
};

struct TORCH_API _chunk_cat_out {
  using schema = at::Tensor & (at::TensorList, int64_t, int64_t, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_chunk_cat";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "_chunk_cat.out(Tensor[] tensors, int dim, int num_chunks, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(at::TensorList tensors, int64_t dim, int64_t num_chunks, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, at::TensorList tensors, int64_t dim, int64_t num_chunks, at::Tensor & out);
};

}} // namespace at::_ops
