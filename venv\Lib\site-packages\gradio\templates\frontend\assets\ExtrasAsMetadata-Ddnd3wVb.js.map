{"version": 3, "file": "ExtrasAsMetadata-Ddnd3wVb.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/ExtrasAsMetadata.js"], "sourcesContent": ["import { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"ExtrasAsMetadata\";\n/**\n * Store glTF extras (if present) in BJS objects' metadata\n */\nexport class ExtrasAsMetadata {\n    _assignExtras(babylonObject, gltfProp) {\n        if (gltfProp.extras && Object.keys(gltfProp.extras).length > 0) {\n            const metadata = (babylonObject.metadata = babylonObject.metadata || {});\n            const gltf = (metadata.gltf = metadata.gltf || {});\n            gltf.extras = gltfProp.extras;\n        }\n    }\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        /**\n         * Defines whether this extension is enabled.\n         */\n        this.enabled = true;\n        this._loader = loader;\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /**\n     * @internal\n     */\n    loadNodeAsync(context, node, assign) {\n        return this._loader.loadNodeAsync(context, node, (babylonTransformNode) => {\n            this._assignExtras(babylonTransformNode, node);\n            assign(babylonTransformNode);\n        });\n    }\n    /**\n     * @internal\n     */\n    loadCameraAsync(context, camera, assign) {\n        return this._loader.loadCameraAsync(context, camera, (babylonCamera) => {\n            this._assignExtras(babylonCamera, camera);\n            assign(babylonCamera);\n        });\n    }\n    /**\n     * @internal\n     */\n    createMaterial(context, material, babylonDrawMode) {\n        const babylonMaterial = this._loader.createMaterial(context, material, babylonDrawMode);\n        this._assignExtras(babylonMaterial, material);\n        return babylonMaterial;\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, false, (loader) => new ExtrasAsMetadata(loader));\n//# sourceMappingURL=ExtrasAsMetadata.js.map"], "names": ["NAME", "ExtrasAsMetadata", "babylonObject", "gltfProp", "metadata", "gltf", "loader", "context", "node", "assign", "babylonTransformNode", "camera", "babylonCamera", "material", "babylonDrawMode", "babylonMaterial", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "wGACA,MAAMA,EAAO,mBAIN,MAAMC,CAAiB,CAC1B,cAAcC,EAAeC,EAAU,CACnC,GAAIA,EAAS,QAAU,OAAO,KAAKA,EAAS,MAAM,EAAE,OAAS,EAAG,CAC5D,MAAMC,EAAYF,EAAc,SAAWA,EAAc,UAAY,CAAA,EAC/DG,EAAQD,EAAS,KAAOA,EAAS,MAAQ,CAAA,EAC/CC,EAAK,OAASF,EAAS,MAC1B,CACJ,CAID,YAAYG,EAAQ,CAIhB,KAAK,KAAON,EAIZ,KAAK,QAAU,GACf,KAAK,QAAUM,CAClB,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAID,cAAcC,EAASC,EAAMC,EAAQ,CACjC,OAAO,KAAK,QAAQ,cAAcF,EAASC,EAAOE,GAAyB,CACvE,KAAK,cAAcA,EAAsBF,CAAI,EAC7CC,EAAOC,CAAoB,CACvC,CAAS,CACJ,CAID,gBAAgBH,EAASI,EAAQF,EAAQ,CACrC,OAAO,KAAK,QAAQ,gBAAgBF,EAASI,EAASC,GAAkB,CACpE,KAAK,cAAcA,EAAeD,CAAM,EACxCF,EAAOG,CAAa,CAChC,CAAS,CACJ,CAID,eAAeL,EAASM,EAAUC,EAAiB,CAC/C,MAAMC,EAAkB,KAAK,QAAQ,eAAeR,EAASM,EAAUC,CAAe,EACtF,YAAK,cAAcC,EAAiBF,CAAQ,EACrCE,CACV,CACL,CACAC,EAAwBhB,CAAI,EAC5BiB,EAAsBjB,EAAM,GAAQM,GAAW,IAAIL,EAAiBK,CAAM,CAAC", "x_google_ignoreList": [0]}