import{GLTFLoader as t,ArrayItem as n}from"./glTFLoader.BetPWe9U.js";import{an as _,ao as c}from"./index.BoI39RQH.js";const r="KHR_texture_basisu";class f{constructor(e){this.name=r,this._loader=e,this.enabled=e.isExtensionUsed(r)}dispose(){this._loader=null}_loadTextureAsync(e,s,o){return t.LoadExtensionAsync(e,s,this.name,(i,l)=>{const u=s.sampler==null?t.DefaultSampler:n.Get(`${e}/sampler`,this._loader.gltf.samplers,s.sampler),m=n.Get(`${i}/source`,this._loader.gltf.images,l.source);return this._loader._createTextureAsync(e,u,m,d=>{o(d)},s._textureInfo.nonColorData?{useRGBAIfASTCBC7NotAvailableWhenUASTC:!0}:void 0,!s._textureInfo.nonColorData)})}}_(r);c(r,!0,a=>new f(a));export{f as KHR_texture_basisu};
//# sourceMappingURL=KHR_texture_basisu.SN6A01jS.js.map
