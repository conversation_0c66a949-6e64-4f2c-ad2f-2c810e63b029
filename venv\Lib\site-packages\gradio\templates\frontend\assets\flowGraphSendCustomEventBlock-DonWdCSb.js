import{b as i}from"./KHR_interactivity-DTxiAnOo.js";import{R as n}from"./index-Dpxo-yl_.js";import"./index-B7J2Z2jS.js";import"./svelte/svelte.js";import"./declarationMapper-BZjsjg7g.js";import"./objectModelMapping-BR4RdEzn.js";class s extends i{constructor(t){super(t),this.config=t;for(const e in this.config.eventData)this.registerDataInput(e,this.config.eventData[e].type,this.config.eventData[e].value)}_execute(t){const e=this.config.eventId,o={};this.dataInputs.forEach(a=>{o[a.name]=a.getValue(t)}),t.configuration.coordinator.notifyCustomEvent(e,o),this.out._activateSignal(t)}getClassName(){return"FlowGraphReceiveCustomEventBlock"}}n("FlowGraphReceiveCustomEventBlock",s);export{s as FlowGraphSendCustomEventBlock};
//# sourceMappingURL=flowGraphSendCustomEventBlock-DonWdCSb.js.map
