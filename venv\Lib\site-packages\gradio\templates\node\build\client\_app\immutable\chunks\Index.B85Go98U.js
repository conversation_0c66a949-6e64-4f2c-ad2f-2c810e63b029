import{SvelteComponent as v,init as f,safe_not_equal as d,onDestroy as s}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";function m(c,l,e){let{gradio:r}=l,{value:a=1}=l,{active:n=!0}=l,u,o,t;return s(()=>{t&&clearInterval(t)}),c.$$set=i=>{"gradio"in i&&e(0,r=i.gradio),"value"in i&&e(1,a=i.value),"active"in i&&e(2,n=i.active)},c.$$.update=()=>{c.$$.dirty&63&&(u!==a||n!==o)&&(t&&clearInterval(t),n&&e(5,t=setInterval(()=>{document.visibilityState==="visible"&&r.dispatch("tick")},a*1e3)),e(3,u=a),e(4,o=n))},[r,a,n,u,o,t]}class _ extends v{constructor(l){super(),f(this,l,m,null,d,{gradio:0,value:1,active:2})}}export{_ as default};
//# sourceMappingURL=Index.B85Go98U.js.map
