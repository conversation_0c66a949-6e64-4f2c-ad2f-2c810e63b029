@echo off
chcp 65001 > nul
color 0B

echo.
echo   ========================================================
echo   =                 REVERIE AGENTS                     =
echo   =              Desktop Application                   =
echo   ========================================================
echo.

:: Check virtual environment
if not exist "venv\Scripts\activate.bat" (
    echo   [ERROR] Virtual environment not found, please run setup.bat first
    pause
    exit /b 1
)

:: Activate virtual environment
echo   [INFO] Activating virtual environment...
call venv\Scripts\activate.bat

:: Start application
echo   [INFO] Starting Reverie Agents desktop application...
echo.
python ui\app\main.py

echo.
echo   Thank you for using Reverie Agents!
pause
