import{SvelteComponent as H,init as J,safe_not_equal as K,element as L,space as O,create_component as Q,claim_element as R,claim_space as V,claim_component as X,attr as r,insert_hydration as j,mount_component as Y,listen as T,transition_in as M,transition_out as P,detach as C,destroy_component as Z,run_all as p,createEventDispatcher as $,create_slot as ee,update_slot_base as le,get_all_dirty_from_scope as ie,get_slot_changes as te,binding_callbacks as ne,src_url_equal as W,tick as fe,text as ae,claim_text as ue,set_data as _e}from"../../../svelte/svelte.js";import"../../../svelte/svelte-submodules.js";import{o as se,A as ce}from"./2.B2AoQPnG.js";function F(t){let e,i,l;return{c(){e=L("img"),this.h()},l(n){e=R(n,"IMG",{class:!0,src:!0,alt:!0}),this.h()},h(){r(e,"class","button-icon svelte-1rvxzzt"),W(e.src,i=t[7].url)||r(e,"src",i),r(e,"alt",l=`${t[0]} icon`)},m(n,f){j(n,e,f)},p(n,f){f&128&&!W(e.src,i=n[7].url)&&r(e,"src",i),f&1&&l!==(l=`${n[0]} icon`)&&r(e,"alt",l)},d(n){n&&C(e)}}}function me(t){let e,i,l=t[7]&&F(t);const n=t[20].default,f=ee(n,t,t[22],null);return{c(){l&&l.c(),e=O(),f&&f.c()},l(s){l&&l.l(s),e=V(s),f&&f.l(s)},m(s,m){l&&l.m(s,m),j(s,e,m),f&&f.m(s,m),i=!0},p(s,m){s[7]?l?l.p(s,m):(l=F(s),l.c(),l.m(e.parentNode,e)):l&&(l.d(1),l=null),f&&f.p&&(!i||m&4194304)&&le(f,n,s,s[22],i?te(n,s[22],m,null):ie(s[22]),null)},i(s){i||(M(f,s),i=!0)},o(s){P(f,s),i=!1},d(s){s&&C(e),l&&l.d(s),f&&f.d(s)}}}function oe(t){let e,i,l,n,f,s,m,o,b,h;return m=new se({props:{size:t[6],variant:t[10],elem_id:t[1],elem_classes:t[2],visible:t[3],scale:t[8],min_width:t[9],disabled:t[11],$$slots:{default:[me]},$$scope:{ctx:t}}}),m.$on("click",t[14]),{c(){e=L("input"),s=O(),Q(m.$$.fragment),this.h()},l(_){e=R(_,"INPUT",{class:!0,accept:!0,type:!0,webkitdirectory:!0,mozdirectory:!0,"data-testid":!0}),s=V(_),X(m.$$.fragment,_),this.h()},h(){r(e,"class","hide svelte-1rvxzzt"),r(e,"accept",t[13]),r(e,"type","file"),e.multiple=i=t[5]==="multiple"||void 0,r(e,"webkitdirectory",l=t[5]==="directory"||void 0),r(e,"mozdirectory",n=t[5]==="directory"||void 0),r(e,"data-testid",f=t[4]+"-upload-button")},m(_,c){j(_,e,c),t[21](e),j(_,s,c),Y(m,_,c),o=!0,b||(h=[T(e,"change",t[15]),T(e,"click",de)],b=!0)},p(_,[c]){(!o||c&8192)&&r(e,"accept",_[13]),(!o||c&32&&i!==(i=_[5]==="multiple"||void 0))&&(e.multiple=i),(!o||c&32&&l!==(l=_[5]==="directory"||void 0))&&r(e,"webkitdirectory",l),(!o||c&32&&n!==(n=_[5]==="directory"||void 0))&&r(e,"mozdirectory",n),(!o||c&16&&f!==(f=_[4]+"-upload-button"))&&r(e,"data-testid",f);const d={};c&64&&(d.size=_[6]),c&1024&&(d.variant=_[10]),c&2&&(d.elem_id=_[1]),c&4&&(d.elem_classes=_[2]),c&8&&(d.visible=_[3]),c&256&&(d.scale=_[8]),c&512&&(d.min_width=_[9]),c&2048&&(d.disabled=_[11]),c&4194433&&(d.$$scope={dirty:c,ctx:_}),m.$set(d)},i(_){o||(M(m.$$.fragment,_),o=!0)},o(_){P(m.$$.fragment,_),o=!1},d(_){_&&(C(e),C(s)),t[21](null),Z(m,_),b=!1,p(h)}}}function de(t){const e=t.target;e.value&&(e.value="")}function re(t,e,i){let{$$slots:l={},$$scope:n}=e,{elem_id:f=""}=e,{elem_classes:s=[]}=e,{visible:m=!0}=e,{label:o}=e,{value:b}=e,{file_count:h}=e,{file_types:_=[]}=e,{root:c}=e,{size:d="lg"}=e,{icon:I=null}=e,{scale:U=null}=e,{min_width:y=void 0}=e,{variant:g="secondary"}=e,{disabled:v=!1}=e,{max_file_size:z=null}=e,{upload:q}=e;const w=$();let k,A;_==null?A=null:(_=_.map(a=>a.startsWith(".")?a:a+"/*"),A=_.join(", "));function D(){w("click"),k.click()}async function u(a){var S;let N=Array.from(a);if(!a.length)return;h==="single"&&(N=[a[0]]);let B=await ce(N);await fe();try{B=(S=await q(B,c,void 0,z??1/0))==null?void 0:S.filter(G=>G!==null)}catch(G){w("error",G.message);return}i(0,b=h==="single"?B==null?void 0:B[0]:B),w("change",b),w("upload",b)}async function E(a){const N=a.target;N.files&&await u(N.files)}function x(a){ne[a?"unshift":"push"](()=>{k=a,i(12,k)})}return t.$$set=a=>{"elem_id"in a&&i(1,f=a.elem_id),"elem_classes"in a&&i(2,s=a.elem_classes),"visible"in a&&i(3,m=a.visible),"label"in a&&i(4,o=a.label),"value"in a&&i(0,b=a.value),"file_count"in a&&i(5,h=a.file_count),"file_types"in a&&i(16,_=a.file_types),"root"in a&&i(17,c=a.root),"size"in a&&i(6,d=a.size),"icon"in a&&i(7,I=a.icon),"scale"in a&&i(8,U=a.scale),"min_width"in a&&i(9,y=a.min_width),"variant"in a&&i(10,g=a.variant),"disabled"in a&&i(11,v=a.disabled),"max_file_size"in a&&i(18,z=a.max_file_size),"upload"in a&&i(19,q=a.upload),"$$scope"in a&&i(22,n=a.$$scope)},[b,f,s,m,o,h,d,I,U,y,g,v,k,A,D,E,_,c,z,q,l,x,n]}class be extends H{constructor(e){super(),J(this,e,re,oe,K,{elem_id:1,elem_classes:2,visible:3,label:4,value:0,file_count:5,file_types:16,root:17,size:6,icon:7,scale:8,min_width:9,variant:10,disabled:11,max_file_size:18,upload:19})}}const he=be;function ge(t){let e=(t[4]??"")+"",i;return{c(){i=ae(e)},l(l){i=ue(l,e)},m(l,n){j(l,i,n)},p(l,n){n&16&&e!==(e=(l[4]??"")+"")&&_e(i,e)},d(l){l&&C(i)}}}function ze(t){let e,i;return e=new he({props:{elem_id:t[1],elem_classes:t[2],visible:t[3],file_count:t[5],file_types:t[6],size:t[8],scale:t[9],icon:t[10],min_width:t[11],root:t[7],value:t[0],disabled:t[14],variant:t[12],label:t[4],max_file_size:t[13].max_file_size,upload:t[17],$$slots:{default:[ge]},$$scope:{ctx:t}}}),e.$on("click",t[18]),e.$on("change",t[19]),e.$on("upload",t[20]),e.$on("error",t[21]),{c(){Q(e.$$.fragment)},l(l){X(e.$$.fragment,l)},m(l,n){Y(e,l,n),i=!0},p(l,[n]){const f={};n&2&&(f.elem_id=l[1]),n&4&&(f.elem_classes=l[2]),n&8&&(f.visible=l[3]),n&32&&(f.file_count=l[5]),n&64&&(f.file_types=l[6]),n&256&&(f.size=l[8]),n&512&&(f.scale=l[9]),n&1024&&(f.icon=l[10]),n&2048&&(f.min_width=l[11]),n&128&&(f.root=l[7]),n&1&&(f.value=l[0]),n&16384&&(f.disabled=l[14]),n&4096&&(f.variant=l[12]),n&16&&(f.label=l[4]),n&8192&&(f.max_file_size=l[13].max_file_size),n&8192&&(f.upload=l[17]),n&4194320&&(f.$$scope={dirty:n,ctx:l}),e.$set(f)},i(l){i||(M(e.$$.fragment,l),i=!0)},o(l){P(e.$$.fragment,l),i=!1},d(l){Z(e,l)}}}function ve(t,e,i){let l,{elem_id:n=""}=e,{elem_classes:f=[]}=e,{visible:s=!0}=e,{label:m}=e,{value:o}=e,{file_count:b}=e,{file_types:h=[]}=e,{root:_}=e,{size:c="lg"}=e,{scale:d=null}=e,{icon:I=null}=e,{min_width:U=void 0}=e,{variant:y="secondary"}=e,{gradio:g}=e,{interactive:v}=e;async function z(u,E){i(0,o=u),g.dispatch(E)}const q=(...u)=>g.client.upload(...u),w=()=>g.dispatch("click"),k=({detail:u})=>z(u,"change"),A=({detail:u})=>z(u,"upload"),D=({detail:u})=>{g.dispatch("error",u)};return t.$$set=u=>{"elem_id"in u&&i(1,n=u.elem_id),"elem_classes"in u&&i(2,f=u.elem_classes),"visible"in u&&i(3,s=u.visible),"label"in u&&i(4,m=u.label),"value"in u&&i(0,o=u.value),"file_count"in u&&i(5,b=u.file_count),"file_types"in u&&i(6,h=u.file_types),"root"in u&&i(7,_=u.root),"size"in u&&i(8,c=u.size),"scale"in u&&i(9,d=u.scale),"icon"in u&&i(10,I=u.icon),"min_width"in u&&i(11,U=u.min_width),"variant"in u&&i(12,y=u.variant),"gradio"in u&&i(13,g=u.gradio),"interactive"in u&&i(16,v=u.interactive)},t.$$.update=()=>{t.$$.dirty&65536&&i(14,l=!v)},[o,n,f,s,m,b,h,_,c,d,I,U,y,g,l,z,v,q,w,k,A,D]}class Ie extends H{constructor(e){super(),J(this,e,ve,ze,K,{elem_id:1,elem_classes:2,visible:3,label:4,value:0,file_count:5,file_types:6,root:7,size:8,scale:9,icon:10,min_width:11,variant:12,gradio:13,interactive:16})}}export{he as BaseUploadButton,Ie as default};
//# sourceMappingURL=Index.BfN4FUC5.js.map
