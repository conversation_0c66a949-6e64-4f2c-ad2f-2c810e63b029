import{F as m}from"./KHR_interactivity.DEAVS2UW.js";import{R as e,e as r}from"./declarationMapper.UBCwU7BT.js";import{a,V as c,R as h}from"./index.BoI39RQH.js";class y extends m{constructor(t){super(t),this.sourceSystem=this.registerDataInput("sourceSystem",e),this.destinationSystem=this.registerDataInput("destinationSystem",e),this.inputCoordinates=this.registerDataInput("inputCoordinates",r),this.outputCoordinates=this.registerDataOutput("outputCoordinates",r)}_updateOutputs(t){const i=this.sourceSystem.getValue(t),n=this.destinationSystem.getValue(t),u=this.inputCoordinates.getValue(t),p=i.getWorldMatrix(),l=n.getWorldMatrix(),s=a.Matrix[0].copyFrom(l);s.invert();const o=a.Matrix[1];s.multiplyToRef(p,o);const d=this.outputCoordinates.getValue(t);c.TransformCoordinatesToRef(u,o,d)}getClassName(){return"FlowGraphTransformCoordinatesSystemBlock"}}h("FlowGraphTransformCoordinatesSystemBlock",y);export{y as FlowGraphTransformCoordinatesSystemBlock};
//# sourceMappingURL=flowGraphTransformCoordinatesSystemBlock.DdG5wdid.js.map
