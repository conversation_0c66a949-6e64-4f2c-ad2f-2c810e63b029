"""
模型下载器
支持从HuggingFace和镜像站下载模型
"""

import os
import requests
import yaml
from pathlib import Path
from typing import Optional, Callable
from urllib.parse import urlparse
import threading
from dataclasses import dataclass

from ..utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class DownloadProgress:
    """下载进度信息"""
    total_size: int
    downloaded: int
    percentage: float
    speed: str
    eta: str

class ModelDownloader:
    """模型下载器"""
    
    def __init__(self, config_path: str = "config/settings/app_config.yaml"):
        self.config_path = config_path
        self.download_thread: Optional[threading.Thread] = None
        self.is_downloading = False
        self.download_cancelled = False
        
        self._load_config()
    
    def _load_config(self):
        """加载配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                self.model_path = config.get('models', {}).get('llm', {}).get('model_path', 'models/llm')
                self.mirrors = config.get('mirrors', {})
                self.timeout = config.get('network', {}).get('timeout', 30)
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            self.model_path = 'models/llm'
            self.mirrors = {}
            self.timeout = 30
    
    def _get_download_url(self, url: str) -> str:
        """获取下载URL（支持镜像）"""
        if 'huggingface.co' in url and 'huggingface' in self.mirrors:
            mirror_url = self.mirrors['huggingface']
            return url.replace('https://huggingface.co', mirror_url)
        return url
    
    def _format_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
    
    def _format_speed(self, speed: float) -> str:
        """格式化下载速度"""
        return f"{self._format_size(speed)}/s"
    
    def _format_time(self, seconds: int) -> str:
        """格式化时间"""
        if seconds < 60:
            return f"{seconds}s"
        elif seconds < 3600:
            return f"{seconds//60}m {seconds%60}s"
        else:
            return f"{seconds//3600}h {(seconds%3600)//60}m"
    
    def download_model(self, 
                      url: str, 
                      filename: Optional[str] = None,
                      progress_callback: Optional[Callable[[DownloadProgress], None]] = None) -> bool:
        """
        下载模型文件
        
        Args:
            url: 下载URL
            filename: 保存的文件名（可选）
            progress_callback: 进度回调函数
        
        Returns:
            bool: 下载是否成功
        """
        if self.is_downloading:
            logger.warning("已有下载任务在进行中")
            return False
        
        # 确保模型目录存在
        os.makedirs(self.model_path, exist_ok=True)
        
        # 获取文件名
        if not filename:
            parsed_url = urlparse(url)
            filename = os.path.basename(parsed_url.path)
            if not filename:
                filename = "model.gguf"
        
        file_path = os.path.join(self.model_path, filename)
        
        # 获取实际下载URL
        download_url = self._get_download_url(url)
        
        def _download():
            self.is_downloading = True
            self.download_cancelled = False
            
            try:
                logger.info(f"开始下载模型: {filename}")
                logger.info(f"下载URL: {download_url}")
                
                # 发送HEAD请求获取文件大小
                head_response = requests.head(download_url, timeout=self.timeout)
                total_size = int(head_response.headers.get('content-length', 0))
                
                # 检查是否支持断点续传
                resume_header = {}
                downloaded = 0
                if os.path.exists(file_path):
                    downloaded = os.path.getsize(file_path)
                    if downloaded < total_size:
                        resume_header['Range'] = f'bytes={downloaded}-'
                        logger.info(f"断点续传，已下载: {self._format_size(downloaded)}")
                    elif downloaded == total_size:
                        logger.info("文件已存在且完整")
                        if progress_callback:
                            progress = DownloadProgress(
                                total_size=total_size,
                                downloaded=total_size,
                                percentage=100.0,
                                speed="0 B/s",
                                eta="0s"
                            )
                            progress_callback(progress)
                        return True
                
                # 开始下载
                response = requests.get(download_url, headers=resume_header, stream=True, timeout=self.timeout)
                response.raise_for_status()
                
                # 打开文件（追加模式如果是断点续传）
                mode = 'ab' if downloaded > 0 else 'wb'
                
                with open(file_path, mode) as f:
                    start_time = time.time()
                    last_time = start_time
                    last_downloaded = downloaded
                    
                    for chunk in response.iter_content(chunk_size=8192):
                        if self.download_cancelled:
                            logger.info("下载已取消")
                            return False
                        
                        if chunk:
                            f.write(chunk)
                            downloaded += len(chunk)
                            
                            # 更新进度
                            current_time = time.time()
                            if current_time - last_time >= 1.0:  # 每秒更新一次
                                speed = (downloaded - last_downloaded) / (current_time - last_time)
                                percentage = (downloaded / total_size * 100) if total_size > 0 else 0
                                eta_seconds = int((total_size - downloaded) / speed) if speed > 0 else 0
                                
                                if progress_callback:
                                    progress = DownloadProgress(
                                        total_size=total_size,
                                        downloaded=downloaded,
                                        percentage=percentage,
                                        speed=self._format_speed(speed),
                                        eta=self._format_time(eta_seconds)
                                    )
                                    progress_callback(progress)
                                
                                last_time = current_time
                                last_downloaded = downloaded
                
                logger.info(f"模型下载完成: {filename}")
                return True
                
            except Exception as e:
                logger.error(f"模型下载失败: {e}")
                # 删除不完整的文件
                if os.path.exists(file_path) and os.path.getsize(file_path) != total_size:
                    os.remove(file_path)
                return False
            
            finally:
                self.is_downloading = False
        
        # 在新线程中下载
        self.download_thread = threading.Thread(target=_download)
        self.download_thread.start()
        
        return True
    
    def cancel_download(self):
        """取消下载"""
        if self.is_downloading:
            self.download_cancelled = True
            logger.info("正在取消下载...")
    
    def wait_for_download(self) -> bool:
        """等待下载完成"""
        if self.download_thread and self.download_thread.is_alive():
            self.download_thread.join()
            return not self.download_cancelled
        return True
    
    def download_default_model(self, progress_callback: Optional[Callable[[DownloadProgress], None]] = None) -> bool:
        """下载默认模型 (Lucy-128k)"""
        default_url = "https://huggingface.co/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf"
        return self.download_model(default_url, "lucy_128k-Q8_0.gguf", progress_callback)

# 导入time模块
import time

# 全局下载器实例
model_downloader = ModelDownloader()
