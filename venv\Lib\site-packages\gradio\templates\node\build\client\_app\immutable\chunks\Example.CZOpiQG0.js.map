{"version": 3, "file": "Example.CZOpiQG0.js", "sources": ["../../../../../../../file/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { FileData } from \"@gradio/client\";\n\n\texport let value: FileData | null;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value ? (Array.isArray(value) ? value.join(\", \") : value) : \"\"}\n</div>\n\n<style>\n\tdiv {\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t}\n\t.gallery {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tcursor: pointer;\n\t\tpadding: var(--size-1) var(--size-2);\n\t\ttext-align: left;\n\t}\n</style>\n"], "names": ["ctx", "toggle_class", "div", "insert_hydration", "target", "anchor", "set_data", "t", "t_value", "value", "$$props", "type", "selected"], "mappings": "8YAaEA,EAAK,CAAA,EAAI,MAAM,QAAQA,EAAK,CAAA,CAAA,EAAIA,EAAK,CAAA,EAAC,KAAK,IAAI,EAAIA,EAAK,CAAA,EAAI,IAAE,qJAJlDC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,gCADHL,EAAK,CAAA,EAAI,MAAM,QAAQA,EAAK,CAAA,CAAA,EAAIA,EAAK,CAAA,EAAC,KAAK,IAAI,EAAIA,EAAK,CAAA,EAAI,IAAE,KAAAM,EAAAC,EAAAC,CAAA,OAJlDP,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,oEAPtB,GAAA,CAAA,MAAAS,CAAA,EAAAC,EACA,CAAA,KAAAC,CAAA,EAAAD,GACA,SAAAE,EAAW,EAAA,EAAAF"}