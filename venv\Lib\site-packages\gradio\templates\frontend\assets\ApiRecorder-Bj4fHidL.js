import"./index-B7J2Z2jS.js";import{B as k}from"./Button-B3gqVEq2.js";import"./svelte/svelte.js";import"./Image-CnqB5dbD.js";import"./file-url-DoxvUUVV.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-Cf_zCqIz.js";import"./prism-python-MMh3z1bK.js";/* empty css                                                   */const{SvelteComponent:A,append:c,attr:u,create_component:B,destroy_component:R,detach:d,element:_,flush:y,init:q,insert:f,mount_component:z,safe_not_equal:I,set_data:C,space:g,text:m,transition_in:P,transition_out:S}=window.__gradio__svelte__internal;function w(o){let e,s,l=o[1][o[0][o[0].length-1].fn_index].api_name+"",n;return{c(){e=_("span"),s=m("/"),n=m(l),u(e,"class","api-name svelte-sy28j6")},m(t,a){f(t,e,a),c(e,s),c(e,n)},p(t,a){a&3&&l!==(l=t[1][t[0][t[0].length-1].fn_index].api_name+"")&&C(n,l)},d(t){t&&d(e)}}}function D(o){let e,s,l,n,t,a,h,v=o[0].length+"",$,b,j,i=o[0].length>0&&w(o);return{c(){e=_("div"),s=g(),l=_("p"),l.textContent="Recording API Calls:",n=g(),t=_("p"),a=_("span"),h=m("["),$=m(v),b=m("]"),j=g(),i&&i.c(),u(e,"class","loading-dot self-baseline svelte-sy28j6"),u(l,"class","self-baseline svelte-sy28j6"),u(a,"class","api-count svelte-sy28j6"),u(t,"class","self-baseline api-section svelte-sy28j6")},m(p,r){f(p,e,r),f(p,s,r),f(p,l,r),f(p,n,r),f(p,t,r),c(t,a),c(a,h),c(a,$),c(a,b),c(t,j),i&&i.m(t,null)},p(p,r){r&1&&v!==(v=p[0].length+"")&&C($,v),p[0].length>0?i?i.p(p,r):(i=w(p),i.c(),i.m(t,null)):i&&(i.d(1),i=null)},d(p){p&&(d(e),d(s),d(l),d(n),d(t)),i&&i.d()}}}function E(o){let e,s,l;return s=new k({props:{size:"sm",variant:"secondary",$$slots:{default:[D]},$$scope:{ctx:o}}}),{c(){e=_("div"),B(s.$$.fragment),u(e,"id","api-recorder")},m(n,t){f(n,e,t),z(s,e,null),l=!0},p(n,[t]){const a={};t&7&&(a.$$scope={dirty:t,ctx:n}),s.$set(a)},i(n){l||(P(s.$$.fragment,n),l=!0)},o(n){S(s.$$.fragment,n),l=!1},d(n){n&&d(e),R(s)}}}function F(o,e,s){let{api_calls:l=[]}=e,{dependencies:n}=e;return o.$$set=t=>{"api_calls"in t&&s(0,l=t.api_calls),"dependencies"in t&&s(1,n=t.dependencies)},[l,n]}class U extends A{constructor(e){super(),q(this,e,F,E,I,{api_calls:0,dependencies:1})}get api_calls(){return this.$$.ctx[0]}set api_calls(e){this.$$set({api_calls:e}),y()}get dependencies(){return this.$$.ctx[1]}set dependencies(e){this.$$set({dependencies:e}),y()}}export{U as default};
//# sourceMappingURL=ApiRecorder-Bj4fHidL.js.map
