{"version": 3, "file": "workerPool.CfMXSLnf.js", "sources": ["../../../../../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Misc/workerPool.js"], "sourcesContent": ["/**\n * Helper class to push actions to a pool of workers.\n */\nexport class WorkerPool {\n    /**\n     * Constructor\n     * @param workers Array of workers to use for actions\n     */\n    constructor(workers) {\n        this._pendingActions = new Array();\n        this._workerInfos = workers.map((worker) => ({\n            workerPromise: Promise.resolve(worker),\n            idle: true,\n        }));\n    }\n    /**\n     * Terminates all workers and clears any pending actions.\n     */\n    dispose() {\n        for (const workerInfo of this._workerInfos) {\n            workerInfo.workerPromise.then((worker) => {\n                worker.terminate();\n            });\n        }\n        this._workerInfos.length = 0;\n        this._pendingActions.length = 0;\n    }\n    /**\n     * Pushes an action to the worker pool. If all the workers are active, the action will be\n     * pended until a worker has completed its action.\n     * @param action The action to perform. Call onComplete when the action is complete.\n     */\n    push(action) {\n        if (!this._executeOnIdleWorker(action)) {\n            this._pendingActions.push(action);\n        }\n    }\n    _executeOnIdleWorker(action) {\n        for (const workerInfo of this._workerInfos) {\n            if (workerInfo.idle) {\n                this._execute(workerInfo, action);\n                return true;\n            }\n        }\n        return false;\n    }\n    _execute(workerInfo, action) {\n        workerInfo.idle = false;\n        workerInfo.workerPromise.then((worker) => {\n            action(worker, () => {\n                const nextAction = this._pendingActions.shift();\n                if (nextAction) {\n                    this._execute(workerInfo, nextAction);\n                }\n                else {\n                    workerInfo.idle = true;\n                }\n            });\n        });\n    }\n}\n/**\n * Similar to the WorkerPool class except it creates and destroys workers automatically with a maximum of `maxWorkers` workers.\n * Workers are terminated when it is idle for at least `idleTimeElapsedBeforeRelease` milliseconds.\n */\nexport class AutoReleaseWorkerPool extends WorkerPool {\n    constructor(maxWorkers, createWorkerAsync, options = AutoReleaseWorkerPool.DefaultOptions) {\n        super([]);\n        this._maxWorkers = maxWorkers;\n        this._createWorkerAsync = createWorkerAsync;\n        this._options = options;\n    }\n    push(action) {\n        if (!this._executeOnIdleWorker(action)) {\n            if (this._workerInfos.length < this._maxWorkers) {\n                const workerInfo = {\n                    workerPromise: this._createWorkerAsync(),\n                    idle: false,\n                };\n                this._workerInfos.push(workerInfo);\n                this._execute(workerInfo, action);\n            }\n            else {\n                this._pendingActions.push(action);\n            }\n        }\n    }\n    _execute(workerInfo, action) {\n        // Reset the idle timeout.\n        if (workerInfo.timeoutId) {\n            clearTimeout(workerInfo.timeoutId);\n            delete workerInfo.timeoutId;\n        }\n        super._execute(workerInfo, (worker, onComplete) => {\n            action(worker, () => {\n                onComplete();\n                if (workerInfo.idle) {\n                    // Schedule the worker to be terminated after the elapsed time.\n                    workerInfo.timeoutId = setTimeout(() => {\n                        workerInfo.workerPromise.then((worker) => {\n                            worker.terminate();\n                        });\n                        const indexOf = this._workerInfos.indexOf(workerInfo);\n                        if (indexOf !== -1) {\n                            this._workerInfos.splice(indexOf, 1);\n                        }\n                    }, this._options.idleTimeElapsedBeforeRelease);\n                }\n            });\n        });\n    }\n}\n/**\n * Default options for the constructor.\n * Override to change the defaults.\n */\nAutoReleaseWorkerPool.DefaultOptions = {\n    idleTimeElapsedBeforeRelease: 1000,\n};\n//# sourceMappingURL=workerPool.js.map"], "names": ["WorkerPool", "workers", "worker", "workerInfo", "action", "nextAction", "AutoReleaseWorkerPool", "maxWorkers", "createWorkerAsync", "options", "onComplete", "indexOf"], "mappings": "AAGO,MAAMA,CAAW,CAKpB,YAAYC,EAAS,CACjB,KAAK,gBAAkB,IAAI,MAC3B,KAAK,aAAeA,EAAQ,IAAKC,IAAY,CACzC,cAAe,QAAQ,QAAQA,CAAM,EACrC,KAAM,EACT,EAAC,CACL,CAID,SAAU,CACN,UAAWC,KAAc,KAAK,aAC1BA,EAAW,cAAc,KAAMD,GAAW,CACtCA,EAAO,UAAS,CAChC,CAAa,EAEL,KAAK,aAAa,OAAS,EAC3B,KAAK,gBAAgB,OAAS,CACjC,CAMD,KAAKE,EAAQ,CACJ,KAAK,qBAAqBA,CAAM,GACjC,KAAK,gBAAgB,KAAKA,CAAM,CAEvC,CACD,qBAAqBA,EAAQ,CACzB,UAAWD,KAAc,KAAK,aAC1B,GAAIA,EAAW,KACX,YAAK,SAASA,EAAYC,CAAM,EACzB,GAGf,MAAO,EACV,CACD,SAASD,EAAYC,EAAQ,CACzBD,EAAW,KAAO,GAClBA,EAAW,cAAc,KAAMD,GAAW,CACtCE,EAAOF,EAAQ,IAAM,CACjB,MAAMG,EAAa,KAAK,gBAAgB,MAAK,EACzCA,EACA,KAAK,SAASF,EAAYE,CAAU,EAGpCF,EAAW,KAAO,EAEtC,CAAa,CACb,CAAS,CACJ,CACL,CAKO,MAAMG,UAA8BN,CAAW,CAClD,YAAYO,EAAYC,EAAmBC,EAAUH,EAAsB,eAAgB,CACvF,MAAM,CAAE,CAAA,EACR,KAAK,YAAcC,EACnB,KAAK,mBAAqBC,EAC1B,KAAK,SAAWC,CACnB,CACD,KAAKL,EAAQ,CACT,GAAI,CAAC,KAAK,qBAAqBA,CAAM,EACjC,GAAI,KAAK,aAAa,OAAS,KAAK,YAAa,CAC7C,MAAMD,EAAa,CACf,cAAe,KAAK,mBAAoB,EACxC,KAAM,EAC1B,EACgB,KAAK,aAAa,KAAKA,CAAU,EACjC,KAAK,SAASA,EAAYC,CAAM,CACnC,MAEG,KAAK,gBAAgB,KAAKA,CAAM,CAG3C,CACD,SAASD,EAAYC,EAAQ,CAErBD,EAAW,YACX,aAAaA,EAAW,SAAS,EACjC,OAAOA,EAAW,WAEtB,MAAM,SAASA,EAAY,CAACD,EAAQQ,IAAe,CAC/CN,EAAOF,EAAQ,IAAM,CACjBQ,IACIP,EAAW,OAEXA,EAAW,UAAY,WAAW,IAAM,CACpCA,EAAW,cAAc,KAAMD,GAAW,CACtCA,EAAO,UAAS,CAC5C,CAAyB,EACD,MAAMS,EAAU,KAAK,aAAa,QAAQR,CAAU,EAChDQ,IAAY,IACZ,KAAK,aAAa,OAAOA,EAAS,CAAC,CAE/D,EAAuB,KAAK,SAAS,4BAA4B,EAEjE,CAAa,CACb,CAAS,CACJ,CACL,CAKAL,EAAsB,eAAiB,CACnC,6BAA8B,GAClC", "x_google_ignoreList": [0]}