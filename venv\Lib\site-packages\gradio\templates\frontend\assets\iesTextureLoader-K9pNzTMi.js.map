{"version": 3, "file": "iesTextureLoader-K9pNzTMi.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Lights/IES/iesLoader.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/Textures/Loaders/iesTextureLoader.js"], "sourcesContent": ["import { Lerp } from \"../../Maths/math.scalar.functions.js\";\nfunction lineToArray(line) {\n    return line\n        .split(\" \")\n        .filter((x) => x !== \"\")\n        .map((x) => parseFloat(x));\n}\nfunction readArray(dataPointer, count, targetArray) {\n    while (targetArray.length !== count) {\n        const line = lineToArray(dataPointer.lines[dataPointer.index++]);\n        targetArray.push(...line);\n    }\n}\nfunction interpolateCandelaValues(data, phi, theta) {\n    let phiIndex = 0;\n    let thetaIndex = 0;\n    let startTheta = 0;\n    let endTheta = 0;\n    let startPhi = 0;\n    let endPhi = 0;\n    // Check if the angle is outside the range\n    for (let index = 0; index < data.numberOfHorizontalAngles - 1; index++) {\n        if (theta < data.horizontalAngles[index + 1] || index === data.numberOfHorizontalAngles - 2) {\n            thetaIndex = index;\n            startTheta = data.horizontalAngles[index];\n            endTheta = data.horizontalAngles[index + 1];\n            break;\n        }\n    }\n    for (let index = 0; index < data.numberOfVerticalAngles - 1; index++) {\n        if (phi < data.verticalAngles[index + 1] || index === data.numberOfVerticalAngles - 2) {\n            phiIndex = index;\n            startPhi = data.verticalAngles[index];\n            endPhi = data.verticalAngles[index + 1];\n            break;\n        }\n    }\n    const deltaTheta = endTheta - startTheta;\n    const deltaPhi = endPhi - startPhi;\n    if (deltaPhi === 0) {\n        return 0;\n    }\n    // Interpolate\n    const t1 = deltaTheta === 0 ? 0 : (theta - startTheta) / deltaTheta;\n    const t2 = (phi - startPhi) / deltaPhi;\n    const nextThetaIndex = deltaTheta === 0 ? thetaIndex : thetaIndex + 1;\n    const v1 = Lerp(data.candelaValues[thetaIndex][phiIndex], data.candelaValues[nextThetaIndex][phiIndex], t1);\n    const v2 = Lerp(data.candelaValues[thetaIndex][phiIndex + 1], data.candelaValues[nextThetaIndex][phiIndex + 1], t1);\n    const v = Lerp(v1, v2, t2);\n    return v;\n}\n/**\n * Generates IES data buffer from a string representing the IES data.\n * @param uint8Array defines the IES data\n * @returns the IES data buffer\n * @see https://ieslibrary.com/browse\n * @see https://playground.babylonjs.com/#UQGPDT#1\n */\nexport function LoadIESData(uint8Array) {\n    const decoder = new TextDecoder(\"utf-8\");\n    const source = decoder.decode(uint8Array);\n    // Read data\n    const dataPointer = {\n        lines: source.split(\"\\n\"),\n        index: 0,\n    };\n    const data = { version: dataPointer.lines[0], candelaValues: [], horizontalAngles: [], verticalAngles: [], numberOfHorizontalAngles: 0, numberOfVerticalAngles: 0 };\n    // Skip metadata\n    dataPointer.index = 1;\n    while (dataPointer.lines.length > 0 && !dataPointer.lines[dataPointer.index].includes(\"TILT=\")) {\n        dataPointer.index++;\n    }\n    // Process tilt data?\n    if (dataPointer.lines[dataPointer.index].includes(\"INCLUDE\")) {\n        // Not supported yet as I did not manage to find an example :)\n    }\n    dataPointer.index++;\n    // Header\n    const header = lineToArray(dataPointer.lines[dataPointer.index++]);\n    data.numberOfLights = header[0];\n    data.lumensPerLamp = header[1];\n    data.candelaMultiplier = header[2];\n    data.numberOfVerticalAngles = header[3];\n    data.numberOfHorizontalAngles = header[4];\n    data.photometricType = header[5]; // We ignore cylindrical type for now. Will add support later if needed\n    data.unitsType = header[6];\n    data.width = header[7];\n    data.length = header[8];\n    data.height = header[9];\n    // Additional data\n    const additionalData = lineToArray(dataPointer.lines[dataPointer.index++]);\n    data.ballastFactor = additionalData[0];\n    data.fileGenerationType = additionalData[1];\n    data.inputWatts = additionalData[2];\n    // Prepare arrays\n    for (let index = 0; index < data.numberOfHorizontalAngles; index++) {\n        data.candelaValues[index] = [];\n    }\n    // Vertical angles\n    readArray(dataPointer, data.numberOfVerticalAngles, data.verticalAngles);\n    // Horizontal angles\n    readArray(dataPointer, data.numberOfHorizontalAngles, data.horizontalAngles);\n    // Candela values\n    for (let index = 0; index < data.numberOfHorizontalAngles; index++) {\n        readArray(dataPointer, data.numberOfVerticalAngles, data.candelaValues[index]);\n    }\n    // Evaluate candela values\n    let maxCandela = -1;\n    for (let index = 0; index < data.numberOfHorizontalAngles; index++) {\n        for (let subIndex = 0; subIndex < data.numberOfVerticalAngles; subIndex++) {\n            data.candelaValues[index][subIndex] *= data.candelaValues[index][subIndex] * data.candelaMultiplier * data.ballastFactor * data.fileGenerationType;\n            maxCandela = Math.max(maxCandela, data.candelaValues[index][subIndex]);\n        }\n    }\n    // Normalize candela values\n    if (maxCandela > 0) {\n        for (let index = 0; index < data.numberOfHorizontalAngles; index++) {\n            for (let subIndex = 0; subIndex < data.numberOfVerticalAngles; subIndex++) {\n                data.candelaValues[index][subIndex] /= maxCandela;\n            }\n        }\n    }\n    // Create the cylindrical texture\n    const height = 180;\n    const width = height * 2;\n    const size = width * height;\n    const arrayBuffer = new Float32Array(width * height);\n    // Fill the texture\n    const startTheta = data.horizontalAngles[0];\n    const endTheta = data.horizontalAngles[data.numberOfHorizontalAngles - 1];\n    for (let index = 0; index < size; index++) {\n        let theta = index % width;\n        const phi = Math.floor(index / width);\n        // Symmetry\n        if (endTheta - startTheta !== 0 && (theta < startTheta || theta >= endTheta)) {\n            theta %= endTheta * 2;\n            if (theta > endTheta) {\n                theta = endTheta * 2 - theta;\n            }\n        }\n        arrayBuffer[phi + theta * height] = interpolateCandelaValues(data, phi, theta);\n    }\n    // So far we only need the first half of the first row of the texture as we only support IES for spot light. We can add support for other types later.\n    return {\n        width: width / 2,\n        height: 1,\n        data: arrayBuffer,\n    };\n}\n//# sourceMappingURL=iesLoader.js.map", "\nimport { LoadIESData } from \"../../../Lights/IES/iesLoader.js\";\n/**\n * Implementation of the IES Texture Loader.\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class _IESTextureLoader {\n    constructor() {\n        /**\n         * Defines whether the loader supports cascade loading the different faces.\n         */\n        this.supportCascades = false;\n    }\n    /**\n     * Uploads the cube texture data to the WebGL texture. It has already been bound.\n     */\n    loadCubeData() {\n        // eslint-disable-next-line no-throw-literal\n        throw \".ies not supported in Cube.\";\n    }\n    /**\n     * Uploads the 2D texture data to the WebGL texture. It has already been bound once in the callback.\n     * @param data contains the texture data\n     * @param texture defines the BabylonJS internal texture\n     * @param callback defines the method to call once ready to upload\n     */\n    loadData(data, texture, callback) {\n        const uint8array = new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n        const textureData = LoadIESData(uint8array);\n        callback(textureData.width, textureData.height, false, false, () => {\n            const engine = texture.getEngine();\n            texture.type = 1;\n            texture.format = 6;\n            texture._gammaSpace = false;\n            engine._uploadDataToTextureDirectly(texture, textureData.data);\n        });\n    }\n}\n//# sourceMappingURL=iesTextureLoader.js.map"], "names": ["lineToArray", "line", "x", "readArray", "dataPointer", "count", "targetArray", "interpolate<PERSON>and<PERSON><PERSON><PERSON><PERSON>", "data", "phi", "theta", "phiIndex", "thetaIndex", "startTheta", "endTheta", "startPhi", "endPhi", "index", "deltaTheta", "deltaPhi", "t1", "t2", "nextThetaIndex", "v1", "<PERSON><PERSON>", "v2", "LoadIESData", "uint8Array", "header", "additionalData", "max<PERSON>and<PERSON>", "subIndex", "height", "width", "size", "arrayBuffer", "_IESTextureLoader", "texture", "callback", "uint8array", "textureData", "engine"], "mappings": "gGACA,SAASA,EAAYC,EAAM,CACvB,OAAOA,EACF,MAAM,GAAG,EACT,OAAQC,GAAMA,IAAM,EAAE,EACtB,IAAKA,GAAM,WAAWA,CAAC,CAAC,CACjC,CACA,SAASC,EAAUC,EAAaC,EAAOC,EAAa,CAChD,KAAOA,EAAY,SAAWD,GAAO,CACjC,MAAMJ,EAAOD,EAAYI,EAAY,MAAMA,EAAY,OAAO,CAAC,EAC/DE,EAAY,KAAK,GAAGL,CAAI,CAC3B,CACL,CACA,SAASM,EAAyBC,EAAMC,EAAKC,EAAO,CAChD,IAAIC,EAAW,EACXC,EAAa,EACbC,EAAa,EACbC,EAAW,EACXC,EAAW,EACXC,EAAS,EAEb,QAASC,EAAQ,EAAGA,EAAQT,EAAK,yBAA2B,EAAGS,IAC3D,GAAIP,EAAQF,EAAK,iBAAiBS,EAAQ,CAAC,GAAKA,IAAUT,EAAK,yBAA2B,EAAG,CACzFI,EAAaK,EACbJ,EAAaL,EAAK,iBAAiBS,CAAK,EACxCH,EAAWN,EAAK,iBAAiBS,EAAQ,CAAC,EAC1C,KACH,CAEL,QAASA,EAAQ,EAAGA,EAAQT,EAAK,uBAAyB,EAAGS,IACzD,GAAIR,EAAMD,EAAK,eAAeS,EAAQ,CAAC,GAAKA,IAAUT,EAAK,uBAAyB,EAAG,CACnFG,EAAWM,EACXF,EAAWP,EAAK,eAAeS,CAAK,EACpCD,EAASR,EAAK,eAAeS,EAAQ,CAAC,EACtC,KACH,CAEL,MAAMC,EAAaJ,EAAWD,EACxBM,EAAWH,EAASD,EAC1B,GAAII,IAAa,EACb,MAAO,GAGX,MAAMC,EAAKF,IAAe,EAAI,GAAKR,EAAQG,GAAcK,EACnDG,GAAMZ,EAAMM,GAAYI,EACxBG,EAAiBJ,IAAe,EAAIN,EAAaA,EAAa,EAC9DW,EAAKC,EAAKhB,EAAK,cAAcI,CAAU,EAAED,CAAQ,EAAGH,EAAK,cAAcc,CAAc,EAAEX,CAAQ,EAAGS,CAAE,EACpGK,EAAKD,EAAKhB,EAAK,cAAcI,CAAU,EAAED,EAAW,CAAC,EAAGH,EAAK,cAAcc,CAAc,EAAEX,EAAW,CAAC,EAAGS,CAAE,EAElH,OADUI,EAAKD,EAAIE,EAAIJ,CAAE,CAE7B,CAQO,SAASK,EAAYC,EAAY,CAIpC,MAAMvB,EAAc,CAChB,MAJY,IAAI,YAAY,OAAO,EAChB,OAAOuB,CAAU,EAGtB,MAAM;AAAA,CAAI,EACxB,MAAO,CACf,EACUnB,EAAO,CAAE,QAASJ,EAAY,MAAM,CAAC,EAAG,cAAe,CAAA,EAAI,iBAAkB,CAAA,EAAI,eAAgB,CAAE,EAAE,yBAA0B,EAAG,uBAAwB,GAGhK,IADAA,EAAY,MAAQ,EACbA,EAAY,MAAM,OAAS,GAAK,CAACA,EAAY,MAAMA,EAAY,KAAK,EAAE,SAAS,OAAO,GACzFA,EAAY,QAGZA,EAAY,MAAMA,EAAY,KAAK,EAAE,SAAS,SAAS,EAG3DA,EAAY,QAEZ,MAAMwB,EAAS5B,EAAYI,EAAY,MAAMA,EAAY,OAAO,CAAC,EACjEI,EAAK,eAAiBoB,EAAO,CAAC,EAC9BpB,EAAK,cAAgBoB,EAAO,CAAC,EAC7BpB,EAAK,kBAAoBoB,EAAO,CAAC,EACjCpB,EAAK,uBAAyBoB,EAAO,CAAC,EACtCpB,EAAK,yBAA2BoB,EAAO,CAAC,EACxCpB,EAAK,gBAAkBoB,EAAO,CAAC,EAC/BpB,EAAK,UAAYoB,EAAO,CAAC,EACzBpB,EAAK,MAAQoB,EAAO,CAAC,EACrBpB,EAAK,OAASoB,EAAO,CAAC,EACtBpB,EAAK,OAASoB,EAAO,CAAC,EAEtB,MAAMC,EAAiB7B,EAAYI,EAAY,MAAMA,EAAY,OAAO,CAAC,EACzEI,EAAK,cAAgBqB,EAAe,CAAC,EACrCrB,EAAK,mBAAqBqB,EAAe,CAAC,EAC1CrB,EAAK,WAAaqB,EAAe,CAAC,EAElC,QAASZ,EAAQ,EAAGA,EAAQT,EAAK,yBAA0BS,IACvDT,EAAK,cAAcS,CAAK,EAAI,GAGhCd,EAAUC,EAAaI,EAAK,uBAAwBA,EAAK,cAAc,EAEvEL,EAAUC,EAAaI,EAAK,yBAA0BA,EAAK,gBAAgB,EAE3E,QAASS,EAAQ,EAAGA,EAAQT,EAAK,yBAA0BS,IACvDd,EAAUC,EAAaI,EAAK,uBAAwBA,EAAK,cAAcS,CAAK,CAAC,EAGjF,IAAIa,EAAa,GACjB,QAASb,EAAQ,EAAGA,EAAQT,EAAK,yBAA0BS,IACvD,QAASc,EAAW,EAAGA,EAAWvB,EAAK,uBAAwBuB,IAC3DvB,EAAK,cAAcS,CAAK,EAAEc,CAAQ,GAAKvB,EAAK,cAAcS,CAAK,EAAEc,CAAQ,EAAIvB,EAAK,kBAAoBA,EAAK,cAAgBA,EAAK,mBAChIsB,EAAa,KAAK,IAAIA,EAAYtB,EAAK,cAAcS,CAAK,EAAEc,CAAQ,CAAC,EAI7E,GAAID,EAAa,EACb,QAASb,EAAQ,EAAGA,EAAQT,EAAK,yBAA0BS,IACvD,QAASc,EAAW,EAAGA,EAAWvB,EAAK,uBAAwBuB,IAC3DvB,EAAK,cAAcS,CAAK,EAAEc,CAAQ,GAAKD,EAKnD,MAAME,EAAS,IACTC,EAAQD,EAAS,EACjBE,EAAOD,EAAQD,EACfG,EAAc,IAAI,aAAaF,EAAQD,CAAM,EAE7CnB,EAAaL,EAAK,iBAAiB,CAAC,EACpCM,EAAWN,EAAK,iBAAiBA,EAAK,yBAA2B,CAAC,EACxE,QAASS,EAAQ,EAAGA,EAAQiB,EAAMjB,IAAS,CACvC,IAAIP,EAAQO,EAAQgB,EACpB,MAAMxB,EAAM,KAAK,MAAMQ,EAAQgB,CAAK,EAEhCnB,EAAWD,IAAe,IAAMH,EAAQG,GAAcH,GAASI,KAC/DJ,GAASI,EAAW,EAChBJ,EAAQI,IACRJ,EAAQI,EAAW,EAAIJ,IAG/ByB,EAAY1B,EAAMC,EAAQsB,CAAM,EAAIzB,EAAyBC,EAAMC,EAAKC,CAAK,CAChF,CAED,MAAO,CACH,MAAOuB,EAAQ,EACf,OAAQ,EACR,KAAME,CACd,CACA,CC7IO,MAAMC,CAAkB,CAC3B,aAAc,CAIV,KAAK,gBAAkB,EAC1B,CAID,cAAe,CAEX,KAAM,6BACT,CAOD,SAAS5B,EAAM6B,EAASC,EAAU,CAC9B,MAAMC,EAAa,IAAI,WAAW/B,EAAK,OAAQA,EAAK,WAAYA,EAAK,UAAU,EACzEgC,EAAcd,EAAYa,CAAU,EAC1CD,EAASE,EAAY,MAAOA,EAAY,OAAQ,GAAO,GAAO,IAAM,CAChE,MAAMC,EAASJ,EAAQ,YACvBA,EAAQ,KAAO,EACfA,EAAQ,OAAS,EACjBA,EAAQ,YAAc,GACtBI,EAAO,6BAA6BJ,EAASG,EAAY,IAAI,CACzE,CAAS,CACJ,CACL", "x_google_ignoreList": [0, 1]}