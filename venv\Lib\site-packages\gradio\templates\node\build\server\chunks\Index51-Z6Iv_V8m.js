import { c as create_ssr_component, v as validate_component, b as createEventDispatcher, e as escape, d as add_attribute, h as add_styles, p as merge_ssr_styles } from './ssr-C3HYbsxA.js';
import { m as mt, z as zA, ao as y, j as Ke, q as j, G as Gt, E as An } from './2-DJbI4FWc.js';
export { default as BaseExample } from './Example18-CjYVa28x.js';
import './index-ClteBeTX.js';
import './Component-NmRBwSfF.js';
import 'path';
import 'url';
import 'fs';

const U={code:"div.svelte-lag733 .math.inline{fill:var(--body-text-color);display:inline-block;vertical-align:middle;padding:var(--size-1-5) -var(--size-1);color:var(--body-text-color)}div.svelte-lag733 .math.inline svg{display:inline;margin-bottom:0.22em}div.svelte-lag733{max-width:100%}.hide.svelte-lag733{display:none}",map:`{"version":3,"file":"Markdown.svelte","sources":["Markdown.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { createEventDispatcher } from \\"svelte\\";\\nimport { copy, css_units } from \\"@gradio/utils\\";\\nimport { Copy, Check } from \\"@gradio/icons\\";\\nimport { IconButton, IconButtonWrapper } from \\"@gradio/atoms\\";\\nimport { MarkdownCode } from \\"@gradio/markdown-code\\";\\nexport let elem_classes = [];\\nexport let visible = true;\\nexport let value;\\nexport let min_height = void 0;\\nexport let rtl = false;\\nexport let sanitize_html = true;\\nexport let line_breaks = false;\\nexport let latex_delimiters;\\nexport let header_links = false;\\nexport let height = void 0;\\nexport let show_copy_button = false;\\nexport let loading_status = void 0;\\nexport let theme_mode;\\nlet copied = false;\\nlet timer;\\nconst dispatch = createEventDispatcher();\\n$: value, dispatch(\\"change\\");\\nasync function handle_copy() {\\n    if (\\"clipboard\\" in navigator) {\\n        await navigator.clipboard.writeText(value);\\n        dispatch(\\"copy\\", { value });\\n        copy_feedback();\\n    }\\n}\\nfunction copy_feedback() {\\n    copied = true;\\n    if (timer)\\n        clearTimeout(timer);\\n    timer = setTimeout(() => {\\n        copied = false;\\n    }, 1e3);\\n}\\n<\/script>\\n\\n<div\\n\\tclass=\\"prose {elem_classes?.join(' ') || ''}\\"\\n\\tclass:hide={!visible}\\n\\tdata-testid=\\"markdown\\"\\n\\tdir={rtl ? \\"rtl\\" : \\"ltr\\"}\\n\\tuse:copy\\n\\tstyle={height ? \`max-height: \${css_units(height)}; overflow-y: auto;\` : \\"\\"}\\n\\tstyle:min-height={min_height && loading_status?.status !== \\"pending\\"\\n\\t\\t? css_units(min_height)\\n\\t\\t: undefined}\\n>\\n\\t{#if show_copy_button}\\n\\t\\t<IconButtonWrapper>\\n\\t\\t\\t<IconButton\\n\\t\\t\\t\\tIcon={copied ? Check : Copy}\\n\\t\\t\\t\\ton:click={handle_copy}\\n\\t\\t\\t\\tlabel={copied ? \\"Copied conversation\\" : \\"Copy conversation\\"}\\n\\t\\t\\t></IconButton>\\n\\t\\t</IconButtonWrapper>\\n\\t{/if}\\n\\t<MarkdownCode\\n\\t\\tmessage={value}\\n\\t\\t{latex_delimiters}\\n\\t\\t{sanitize_html}\\n\\t\\t{line_breaks}\\n\\t\\tchatbot={false}\\n\\t\\t{header_links}\\n\\t\\t{theme_mode}\\n\\t/>\\n</div>\\n\\n<style>\\n\\tdiv :global(.math.inline) {\\n\\t\\tfill: var(--body-text-color);\\n\\t\\tdisplay: inline-block;\\n\\t\\tvertical-align: middle;\\n\\t\\tpadding: var(--size-1-5) -var(--size-1);\\n\\t\\tcolor: var(--body-text-color);\\n\\t}\\n\\n\\tdiv :global(.math.inline svg) {\\n\\t\\tdisplay: inline;\\n\\t\\tmargin-bottom: 0.22em;\\n\\t}\\n\\n\\tdiv {\\n\\t\\tmax-width: 100%;\\n\\t}\\n\\n\\t.hide {\\n\\t\\tdisplay: none;\\n\\t}</style>\\n"],"names":[],"mappings":"AAuEC,iBAAG,CAAS,YAAc,CACzB,IAAI,CAAE,IAAI,iBAAiB,CAAC,CAC5B,OAAO,CAAE,YAAY,CACrB,cAAc,CAAE,MAAM,CACtB,OAAO,CAAE,IAAI,UAAU,CAAC,CAAC,KAAK,QAAQ,CAAC,CACvC,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CAEA,iBAAG,CAAS,gBAAkB,CAC7B,OAAO,CAAE,MAAM,CACf,aAAa,CAAE,MAChB,CAEA,iBAAI,CACH,SAAS,CAAE,IACZ,CAEA,mBAAM,CACL,OAAO,CAAE,IACV"}`},b=create_ssr_component((a,t,e,p)=>{let{elem_classes:s=[]}=t,{visible:l=!0}=t,{value:o}=t,{min_height:n=void 0}=t,{rtl:i=!1}=t,{sanitize_html:v=!0}=t,{line_breaks:c=!1}=t,{latex_delimiters:m}=t,{header_links:d=!1}=t,{height:r=void 0}=t,{show_copy_button:h=!1}=t,{loading_status:_=void 0}=t,{theme_mode:A}=t;const f=createEventDispatcher();return t.elem_classes===void 0&&e.elem_classes&&s!==void 0&&e.elem_classes(s),t.visible===void 0&&e.visible&&l!==void 0&&e.visible(l),t.value===void 0&&e.value&&o!==void 0&&e.value(o),t.min_height===void 0&&e.min_height&&n!==void 0&&e.min_height(n),t.rtl===void 0&&e.rtl&&i!==void 0&&e.rtl(i),t.sanitize_html===void 0&&e.sanitize_html&&v!==void 0&&e.sanitize_html(v),t.line_breaks===void 0&&e.line_breaks&&c!==void 0&&e.line_breaks(c),t.latex_delimiters===void 0&&e.latex_delimiters&&m!==void 0&&e.latex_delimiters(m),t.header_links===void 0&&e.header_links&&d!==void 0&&e.header_links(d),t.height===void 0&&e.height&&r!==void 0&&e.height(r),t.show_copy_button===void 0&&e.show_copy_button&&h!==void 0&&e.show_copy_button(h),t.loading_status===void 0&&e.loading_status&&_!==void 0&&e.loading_status(_),t.theme_mode===void 0&&e.theme_mode&&A!==void 0&&e.theme_mode(A),a.css.add(U),f("change"),`<div class="${["prose "+escape(s?.join(" ")||"",!0)+" svelte-lag733",l?"":"hide"].join(" ").trim()}" data-testid="markdown"${add_attribute("dir",i?"rtl":"ltr",0)}${add_styles(merge_ssr_styles(escape(r?`max-height: ${y(r)}; overflow-y: auto;`:"",!0),{"min-height":n&&_?.status!=="pending"?y(n):void 0}))}>${h?`${validate_component(Ke,"IconButtonWrapper").$$render(a,{},{},{default:()=>`${validate_component(j,"IconButton").$$render(a,{Icon:Gt,label:"Copy conversation"},{},{})}`})}`:""} ${validate_component(An,"MarkdownCode").$$render(a,{message:o,latex_delimiters:m,sanitize_html:v,line_breaks:c,chatbot:!1,header_links:d,theme_mode:A},{},{})} </div>`}),D=b,G={code:"div.svelte-vuh1yp{transition:150ms}.pending.svelte-vuh1yp{opacity:0.2}.padding.svelte-vuh1yp{padding:var(--block-padding)}",map:'{"version":3,"file":"Index.svelte","sources":["Index.svelte"],"sourcesContent":["<script context=\\"module\\" lang=\\"ts\\">export { default as BaseMarkdown } from \\"./shared/Markdown.svelte\\";\\nexport { default as BaseExample } from \\"./Example.svelte\\";\\n<\/script>\\n\\n<script lang=\\"ts\\">import Markdown from \\"./shared/Markdown.svelte\\";\\nimport { StatusTracker } from \\"@gradio/statustracker\\";\\nimport { Block } from \\"@gradio/atoms\\";\\nexport let elem_id = \\"\\";\\nexport let elem_classes = [];\\nexport let visible = true;\\nexport let value = \\"\\";\\nexport let loading_status;\\nexport let rtl = false;\\nexport let sanitize_html = true;\\nexport let line_breaks = false;\\nexport let gradio;\\nexport let latex_delimiters;\\nexport let header_links = false;\\nexport let height;\\nexport let min_height;\\nexport let max_height;\\nexport let show_copy_button = false;\\nexport let container = false;\\nexport let theme_mode;\\nexport let padding = false;\\n<\/script>\\n\\n<Block\\n\\t{visible}\\n\\t{elem_id}\\n\\t{elem_classes}\\n\\t{container}\\n\\tallow_overflow={true}\\n\\toverflow_behavior=\\"auto\\"\\n\\t{height}\\n\\t{min_height}\\n\\t{max_height}\\n>\\n\\t<StatusTracker\\n\\t\\tautoscroll={gradio.autoscroll}\\n\\t\\ti18n={gradio.i18n}\\n\\t\\t{...loading_status}\\n\\t\\tvariant=\\"center\\"\\n\\t\\ton:clear_status={() => gradio.dispatch(\\"clear_status\\", loading_status)}\\n\\t/>\\n\\t<div class:padding class:pending={loading_status?.status === \\"pending\\"}>\\n\\t\\t<Markdown\\n\\t\\t\\t{value}\\n\\t\\t\\t{elem_classes}\\n\\t\\t\\t{visible}\\n\\t\\t\\t{rtl}\\n\\t\\t\\ton:change={() => gradio.dispatch(\\"change\\")}\\n\\t\\t\\ton:copy={(e) => gradio.dispatch(\\"copy\\", e.detail)}\\n\\t\\t\\t{latex_delimiters}\\n\\t\\t\\t{sanitize_html}\\n\\t\\t\\t{line_breaks}\\n\\t\\t\\t{header_links}\\n\\t\\t\\t{show_copy_button}\\n\\t\\t\\t{loading_status}\\n\\t\\t\\t{theme_mode}\\n\\t\\t/>\\n\\t</div>\\n</Block>\\n\\n<style>\\n\\tdiv {\\n\\t\\ttransition: 150ms;\\n\\t}\\n\\n\\t.pending {\\n\\t\\topacity: 0.2;\\n\\t}\\n\\n\\t.padding {\\n\\t\\tpadding: var(--block-padding);\\n\\t}</style>\\n"],"names":[],"mappings":"AAiEC,iBAAI,CACH,UAAU,CAAE,KACb,CAEA,sBAAS,CACR,OAAO,CAAE,GACV,CAEA,sBAAS,CACR,OAAO,CAAE,IAAI,eAAe,CAC7B"}'},q=create_ssr_component((a,t,e,p)=>{let{elem_id:s=""}=t,{elem_classes:l=[]}=t,{visible:o=!0}=t,{value:n=""}=t,{loading_status:i}=t,{rtl:v=!1}=t,{sanitize_html:c=!0}=t,{line_breaks:m=!1}=t,{gradio:d}=t,{latex_delimiters:r}=t,{header_links:h=!1}=t,{height:_}=t,{min_height:A}=t,{max_height:f}=t,{show_copy_button:C=!1}=t,{container:x=!1}=t,{theme_mode:k}=t,{padding:g=!1}=t;return t.elem_id===void 0&&e.elem_id&&s!==void 0&&e.elem_id(s),t.elem_classes===void 0&&e.elem_classes&&l!==void 0&&e.elem_classes(l),t.visible===void 0&&e.visible&&o!==void 0&&e.visible(o),t.value===void 0&&e.value&&n!==void 0&&e.value(n),t.loading_status===void 0&&e.loading_status&&i!==void 0&&e.loading_status(i),t.rtl===void 0&&e.rtl&&v!==void 0&&e.rtl(v),t.sanitize_html===void 0&&e.sanitize_html&&c!==void 0&&e.sanitize_html(c),t.line_breaks===void 0&&e.line_breaks&&m!==void 0&&e.line_breaks(m),t.gradio===void 0&&e.gradio&&d!==void 0&&e.gradio(d),t.latex_delimiters===void 0&&e.latex_delimiters&&r!==void 0&&e.latex_delimiters(r),t.header_links===void 0&&e.header_links&&h!==void 0&&e.header_links(h),t.height===void 0&&e.height&&_!==void 0&&e.height(_),t.min_height===void 0&&e.min_height&&A!==void 0&&e.min_height(A),t.max_height===void 0&&e.max_height&&f!==void 0&&e.max_height(f),t.show_copy_button===void 0&&e.show_copy_button&&C!==void 0&&e.show_copy_button(C),t.container===void 0&&e.container&&x!==void 0&&e.container(x),t.theme_mode===void 0&&e.theme_mode&&k!==void 0&&e.theme_mode(k),t.padding===void 0&&e.padding&&g!==void 0&&e.padding(g),a.css.add(G),`${validate_component(mt,"Block").$$render(a,{visible:o,elem_id:s,elem_classes:l,container:x,allow_overflow:!0,overflow_behavior:"auto",height:_,min_height:A,max_height:f},{},{default:()=>`${validate_component(zA,"StatusTracker").$$render(a,Object.assign({},{autoscroll:d.autoscroll},{i18n:d.i18n},i,{variant:"center"}),{},{})} <div class="${["svelte-vuh1yp",(g?"padding":"")+" "+(i?.status==="pending"?"pending":"")].join(" ").trim()}">${validate_component(D,"Markdown").$$render(a,{value:n,elem_classes:l,visible:o,rtl:v,latex_delimiters:r,sanitize_html:c,line_breaks:m,header_links:h,show_copy_button:C,loading_status:i,theme_mode:k},{},{})}</div>`})}`});

export { D as BaseMarkdown, q as default };
//# sourceMappingURL=Index51-Z6Iv_V8m.js.map
