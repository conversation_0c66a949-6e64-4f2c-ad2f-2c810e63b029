// qrubberband.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QRubberBand : public QWidget
{
%TypeHeaderCode
#include <qrubberband.h>
%End

public:
    enum Shape
    {
        Line,
        Rectangle,
    };

    QRubberBand(QRubberBand::Shape, QWidget *parent /TransferThis/ = 0);
    virtual ~QRubberBand();
    QRubberBand::Shape shape() const;
    void setGeometry(const QRect &r);
    void setGeometry(int ax, int ay, int aw, int ah);
    void move(const QPoint &p);
    void move(int ax, int ay);
    void resize(int w, int h);
    void resize(const QSize &s);

protected:
    virtual void initStyleOption(QStyleOptionRubberBand *option) const;
    virtual bool event(QEvent *e);
    virtual void paintEvent(QPaintEvent *);
    virtual void changeEvent(QEvent *);
    virtual void showEvent(QShowEvent *);
    virtual void resizeEvent(QResizeEvent *);
    virtual void moveEvent(QMoveEvent *);
};
